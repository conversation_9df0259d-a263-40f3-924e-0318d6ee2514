package com.accelevents.registration.approval.domain;

import com.accelevents.BaseAuditInfoUser;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.RegistrationRequestStatus;
import com.accelevents.domain.enums.RegistrationRequestType;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static com.accelevents.domain.enums.RecordStatus.CREATE;

@Entity
@Table(name = "registration_request")
@Where(clause="rec_status<>'DELETE'")
public class RegistrationRequest extends BaseAuditInfoUser implements Serializable{

    private static final long serialVersionUID = 3083344570780424760L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private long id;

    @Column(name = "event_id", nullable = false)
    private long eventId;

    @ManyToOne(fetch = FetchType.LAZY,targetEntity = Event.class)
    @JoinColumn(name = "event_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private Event event;
    
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private RegistrationRequestType type;

	@OneToOne
	@JoinColumn(name = "ticket_holder_attributes_id", nullable = false)
	private TicketHolderAttributes ticketHolderAttributes;

    @Column(name = "recurring_event_id")
    private Long recurringEventId;

    @ManyToOne(fetch = FetchType.LAZY,targetEntity = RecurringEvents.class)
    @JoinColumn(name ="recurring_event_id" , insertable = false, updatable = false)
    private RecurringEvents recurringEvents;

    @Column(name = "notes")
    private String notes;

    @ManyToOne
    @JoinColumn(name = "requested_for_user_id")
    private User requestedForUser;

    @ManyToOne
    @JoinColumn(name = "requested_by_user_id")
    private User requestedByUser;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private RegistrationRequestStatus status = RegistrationRequestStatus.PENDING;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recStatus = CREATE;

    @Column(name = "is_checkout_email_sent")
    private boolean checkoutEmailSent;

    @Column(name = "recent_approved_at")
    private Date recentApprovedAt;

    @Column(name = "is_checkout_link_expired")
    private boolean checkoutLinkExpired;

    @Column(name = "approval_disclaimer")
    private String approvalDisclaimer;

    @Column(name = "approval_wl_disclaimer")
    private String approvalWLDisclaimer;

    @Type(type = "text")
    @Column(name = "rec_source")
    private String recSource;

    @OneToOne(fetch = FetchType.LAZY,targetEntity = TrackingLinks.class)
    @JoinColumn(name = "tracking_link_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private TrackingLinks trackingLinks;

    @Column(name = "tracking_link_id")
    private Long trackingLinkId;

    @Column(name = "ticketing_coupon_id")
    private Long ticketingCouponId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getEventId() {
        return eventId;
    }

    public void setEventId(long eventId) {
        this.eventId = eventId;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public TicketHolderAttributes getTicketHolderAttributes() {
        return ticketHolderAttributes;
    }

    public void setTicketHolderAttributes(TicketHolderAttributes ticketHolderAttributes) {
        this.ticketHolderAttributes = ticketHolderAttributes;
    }

    public User getRequestedByUser() {
        return requestedByUser;
    }

    public void setRequestedByUser(User requestedByUser) {
        this.requestedByUser = requestedByUser;
    }

    public RecordStatus getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(RecordStatus recStatus) {
        this.recStatus = recStatus;
    }

    public RegistrationRequestType getType() {
        return type;
    }

    public void setType(RegistrationRequestType type) {
        this.type = type;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public RegistrationRequestStatus getStatus() {
        return status;
    }

    public void setStatus(RegistrationRequestStatus status) {
        this.status = status;
    }

    public Long getRecurringEventId() {
        return recurringEventId;
    }

    public void setRecurringEventId(Long recurringEventId) {
        this.recurringEventId = recurringEventId;
    }

    public RecurringEvents getRecurringEvents() {
        return recurringEvents;
    }

    public void setRecurringEvents(RecurringEvents recurringEvents) {
        this.recurringEvents = recurringEvents;
    }

    public User getRequestedForUser() {
        return requestedForUser;
    }

    public void setRequestedForUser(User requestedForUser) {
        this.requestedForUser = requestedForUser;
    }

    public boolean isCheckoutEmailSent() {
        return checkoutEmailSent;
    }

    public void setCheckoutEmailSent(boolean checkoutEmailSent) {
        this.checkoutEmailSent = checkoutEmailSent;
    }

    public Date getRecentApprovedAt() {
        return recentApprovedAt;
    }

    public void setRecentApprovedAt(Date recentApprovedAt) {
        this.recentApprovedAt = recentApprovedAt;
    }

    public boolean isCheckoutLinkExpired() {
        return checkoutLinkExpired;
    }

    public void setCheckoutLinkExpired(boolean checkoutLinkExpired) {
        this.checkoutLinkExpired = checkoutLinkExpired;
    }

    public String getApprovalDisclaimer() { return approvalDisclaimer; }

    public void setApprovalDisclaimer(String approvalDisclaimer) { this.approvalDisclaimer = approvalDisclaimer; }

    public String getRecSource() { return recSource;}

    public void setRecSource(String recSource) {this.recSource = recSource;}

    public TrackingLinks getTrackingLinks() {
        return trackingLinks;
    }

    public void setTrackingLinks(TrackingLinks trackingLinks) {
        this.trackingLinks = trackingLinks;
    }

    public Long getTrackingLinkId() {
        return trackingLinkId;
    }

    public void setTrackingLinkId(Long trackingLinkId) {
        this.trackingLinkId = trackingLinkId;
    }

    public String getApprovalWLDisclaimer() {
        return approvalWLDisclaimer;
    }

    public void setApprovalWLDisclaimer(String approvalWLDisclaimer) {
        this.approvalWLDisclaimer = approvalWLDisclaimer;
    }

    public Long getTicketingCouponId() {
        return ticketingCouponId;
    }

    public void setTicketingCouponId(Long ticketingCouponId) {
        this.ticketingCouponId = ticketingCouponId;
    }

    @Override
    public String toString() {
        return "RegistrationRequest{" +
                "id=" + id +
                ", eventId=" + eventId +
                ", event=" + event +
                ", type=" + type +
                ", ticketHolderAttributes=" + ticketHolderAttributes +
                ", recurringEventId=" + recurringEventId +
                ", recurringEvents=" + recurringEvents +
                ", notes='" + notes + '\'' +
                ", requestedForUser=" + requestedForUser +
                ", requestedByUser=" + requestedByUser +
                ", status=" + status +
                ", recStatus=" + recStatus +
                ", checkoutEmailSent=" + checkoutEmailSent +
                ", recentApprovedAt=" + recentApprovedAt +
                ", checkoutLinkExpired=" + checkoutLinkExpired +
                ", approvalDisclaimer=" + approvalDisclaimer +
                ", approvalWLDisclaimer=" + approvalWLDisclaimer +
                ", trackingLinkId=" + trackingLinkId +
                ", ticketingCouponId=" + ticketingCouponId +
                '}';
    }
}