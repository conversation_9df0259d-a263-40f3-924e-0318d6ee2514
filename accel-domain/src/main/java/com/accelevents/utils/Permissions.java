package com.accelevents.utils;

import com.accelevents.enums.StaffRole;
import com.accelevents.enums.UserRole;

import java.util.ArrayList;
import java.util.List;

import static com.accelevents.enums.UserRole.*;

public class Permissions {

    public static List<UserRole> SUPER_ADMIN_PERMISSIONS = new ArrayList<>();
    public static List<UserRole> SUPER_ADMIN_WL_COORDINATOR_PERMISSIONS = new ArrayList<>();
    public static List<String> SUPER_ADMIN_WL_COORDINATOR_NAME_PERMISSIONS = new ArrayList<>();
    public static List<UserRole> GtEq_EVENT_LEVEL_ADMIN_PERMISSION = new ArrayList<>();
    public static List<UserRole> GtEq_EVENT_LEVEL_STAFF_PERMISSION = new ArrayList<>();
    public static List<UserRole> GtEq_EXHIBITOR_ADMIN_LEVEL_PERMISSIONS = new ArrayList<>();
    public static List<UserRole> GtEq_EXHIBITOR_STAFF_LEVEL_PERMISSIONS = new ArrayList<>();
    public static List<UserRole> EXHIBITOR_STAFF_ONLY_PERMISSIONS = new ArrayList<>();
    public static List<StaffRole> WL_AND_EVENT_COORDINATOR_PERMISSIONS = new ArrayList<>();
    public static List<String> WL_AND_EVENT_COORDINATOR_STRING_PERMISSIONS = new ArrayList<>();

    static {
        SUPER_ADMIN_PERMISSIONS.add(ROLE_SUPERADMIN);

        GtEq_EVENT_LEVEL_ADMIN_PERMISSION.add(ROLE_WHITELABELADMIN);
        GtEq_EVENT_LEVEL_ADMIN_PERMISSION.add(ROLE_EVENT_COORDINATOR);
        GtEq_EVENT_LEVEL_ADMIN_PERMISSION.add(ROLE_ADMIN);
        GtEq_EVENT_LEVEL_ADMIN_PERMISSION.add(ROLE_ORGANIZER_OWNER);
        GtEq_EVENT_LEVEL_ADMIN_PERMISSION.add(ROLE_ORGANIZER_ADMIN);
        GtEq_EVENT_LEVEL_ADMIN_PERMISSION.addAll(SUPER_ADMIN_PERMISSIONS);

        SUPER_ADMIN_WL_COORDINATOR_PERMISSIONS.add(ROLE_WHITELABELADMIN);
        SUPER_ADMIN_WL_COORDINATOR_PERMISSIONS.add(ROLE_EVENT_COORDINATOR);
        SUPER_ADMIN_WL_COORDINATOR_PERMISSIONS.add(ROLE_SUPERADMIN);

        SUPER_ADMIN_WL_COORDINATOR_NAME_PERMISSIONS.add(ROLE_WHITELABELADMIN.name());
        SUPER_ADMIN_WL_COORDINATOR_NAME_PERMISSIONS.add(ROLE_EVENT_COORDINATOR.name());
        SUPER_ADMIN_WL_COORDINATOR_NAME_PERMISSIONS.add(ROLE_SUPERADMIN.name());

        GtEq_EVENT_LEVEL_STAFF_PERMISSION.add(ROLE_STAFF);
        GtEq_EVENT_LEVEL_STAFF_PERMISSION.addAll(GtEq_EVENT_LEVEL_ADMIN_PERMISSION);

        GtEq_EXHIBITOR_ADMIN_LEVEL_PERMISSIONS.add(ROLE_EXHBITOR_ADMIN);
        GtEq_EXHIBITOR_ADMIN_LEVEL_PERMISSIONS.addAll(GtEq_EVENT_LEVEL_STAFF_PERMISSION);

        GtEq_EXHIBITOR_STAFF_LEVEL_PERMISSIONS.add(ROLE_LEAD_RETRIEVER);
        GtEq_EXHIBITOR_STAFF_LEVEL_PERMISSIONS.addAll(GtEq_EXHIBITOR_ADMIN_LEVEL_PERMISSIONS);

        EXHIBITOR_STAFF_ONLY_PERMISSIONS.add(ROLE_LEAD_RETRIEVER);
        EXHIBITOR_STAFF_ONLY_PERMISSIONS.add(ROLE_EXHBITOR_ADMIN);
        WL_AND_EVENT_COORDINATOR_PERMISSIONS.add(StaffRole.whitelabeladmin);
        WL_AND_EVENT_COORDINATOR_PERMISSIONS.add(StaffRole.eventcoordinator);
        WL_AND_EVENT_COORDINATOR_PERMISSIONS.add(StaffRole.eventbudgetowner);
        WL_AND_EVENT_COORDINATOR_PERMISSIONS.add(StaffRole.eventplanner);
        WL_AND_EVENT_COORDINATOR_STRING_PERMISSIONS.add(StaffRole.whitelabeladmin.name());
        WL_AND_EVENT_COORDINATOR_STRING_PERMISSIONS.add(StaffRole.eventcoordinator.name());
        WL_AND_EVENT_COORDINATOR_STRING_PERMISSIONS.add(StaffRole.eventbudgetowner.name());
        WL_AND_EVENT_COORDINATOR_STRING_PERMISSIONS.add(StaffRole.eventplanner.name());
    }

    public static boolean STAFF_OR_HIGHER_ROLE(UserRole userRole){
        return GtEq_EVENT_LEVEL_STAFF_PERMISSION.contains(userRole);
    }

    public static boolean ADMIN_OR_HIGHER_ROLE(UserRole userRole){
        return GtEq_EVENT_LEVEL_ADMIN_PERMISSION.contains(userRole);
    }

    public static boolean EXHIBITOR_ADMIN_OR_HIGHER_ROLE(UserRole userRole){
        return GtEq_EXHIBITOR_ADMIN_LEVEL_PERMISSIONS.contains(userRole);
    }

    public static boolean EXHIBITOR_LEAD_OR_HIGHER_ROLE(UserRole userRole){
        return GtEq_EXHIBITOR_STAFF_LEVEL_PERMISSIONS.contains(userRole);
    }
}
