package com.accelevents.common.dto;

import com.accelevents.enums.BillingType;
import com.accelevents.utils.Constants;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Schema(description ="Organizer Details")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class OrganizerDto implements Serializable {
    private static final long serialVersionUID = 404846936737772649L;

    @Schema(description ="Organizer Id")
    private Long organizerId;

    @Schema(description ="Organizer Name")
    private String name;

    @Schema(description ="Organizer Url")
    private String organizerPageURL;

    @Schema(description ="Organizer Description")
    private String organizerDescription;

    @Schema(description ="Organizer Logo Image")
    private String logoImage;

    @Schema(description ="Organizer Facebook link")
    private String facebookLink;

    @Schema(description ="Organizer Twitter link")
    private String twitterLink;

    @Schema(description ="Organizer LinkedIn link")
    private String linkedInLink;

    @Schema(description ="Organizer Background Color")
    private String backgroundColor;

    @Schema(description ="Organizer Text Color")
    private String textColor;

    @Schema(description ="Organizer website")
    @Size(max = 250, message = "Maximum 250 Characters are allowed for Website")
    private String website;

    @Schema(description ="Organizer contact email address")
    @Size(max = Constants.EMAIL_SIZE, message = Constants.EMAIL_SIZE_MSG)
    private String contactEmailAddress;

    @Schema(description ="Chargebee Plan Id")
    private Long planId;

    @Schema(description ="Chargebee Plan Name")
    private String chargebeePlanName;

    @Schema(description ="Chargebee Display Plan Name")
    private String chargebeeDisplayPlanName;

    @Schema(description ="Chargebee Customer Id")
    private String chargebeeCustomerId;

    @Schema(description ="Plan end date")
    private String planEndDate;

    @Schema(description ="Plan renewal date")
    private String renewalDate;

    @Schema(description ="subscription Id")
    private String subscriptionId;

    @Schema(description ="Show or hide tray integration")
    private Boolean showTrayIntegration;

    @Schema(description ="White Label Id")
    private Long whiteLabelId;

    @Schema(description ="show count of White Label Events which is associate with particular organizer")
    private Long countOfWhiteLabelEvents;

    @Schema(description ="number of sold ticket count")
    private Long soldTicketCountForWhiteLabel;

    @Schema(description ="number of total ticket count")
    private Long totalTicketCountForWhiteLabel;

    @Schema(description ="charge details")
    private List<OrganizerChargeInfoDto> organizerChargeInfoDtos;

    @Schema(description ="allow social sharing")
    private Boolean allowSocialSharing = true;

    @Schema(description ="billing type")
    private BillingType billingType;

    @Schema(description ="Chargebee plan id")
    private String chargebeePlanId;

    @Schema(description ="White Label URL")
    private String whiteLabelURL;
    @Schema(description ="White Label Host Base URL")
    private String whiteLabelHostBaseUrl;
    @Schema(description ="White Label Fav Icon")
    private String whiteLabelFavIcon;
    @Schema(description ="White Label Account Name")
    private String whiteLabelFirmName;

    private String headerBackgroundColor;

    private  String headerTextColor;

    @Schema(description ="First Address of Organizer")
    private String address1;

    @Schema(description ="Second Address of Organizer")
    private String address2;

    @Schema(description ="City of Organizer")
    private String city;

    @Schema(description ="Zip code of Organizer")
    private String zipcode;

    @Schema(description ="State of Organizer")
    private String state;

    @Schema(description ="Country of organizer")
    private String country;

    private  String headerLogoImage;

    @Schema(description ="Mobile Chargebee Plan Id")
    private Long mobilePlanId;

    @Schema(description ="Mobile Chargebee Plan Name")
    private String mobileChargebeePlanName;

    @Schema(description ="Mobile Chargebee Customer Id")
    private String mobileChargebeeCustomerId;

    @Schema(description ="Mobile Chargebee subscription Id")
    private String mobileChargebeeSubscriptionId;

    @Schema(description = "Hide Product Update Notification")
    private boolean hideProductUpdateNotification;

    @Schema(description = "Hubspot company Id")
    private Long hubspotCompanyId;

    @Schema(description = "Enable white label redirect")
    private boolean enableWhiteLabelRedirect;

    public String getChargebeeDisplayPlanName() {
        return chargebeeDisplayPlanName;
    }

    public void setChargebeeDisplayPlanName(String chargebeeDisplayPlanName) {
        this.chargebeeDisplayPlanName = chargebeeDisplayPlanName;
    }

    public OrganizerDto() {
    }

    public OrganizerDto(long organizerId, String name, String organizerPageURL, String organizerDescription, String logoImage, String facebookLink,//NOSONAR
                        String twitterLink, String linkedInLink, String backgroundColor, String textColor, String website, String contactEmailAddress) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.organizerDescription = organizerDescription;
        this.logoImage = logoImage;
        this.facebookLink = facebookLink;
        this.twitterLink = twitterLink;
        this.linkedInLink = linkedInLink;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
        this.website = website;
        this.contactEmailAddress = contactEmailAddress;
    }

    public OrganizerDto(long organizerId, String name, String organizerPageURL, String organizerDescription, String logoImage, String facebookLink,//NOSONAR
                        String twitterLink, String linkedInLink, String backgroundColor, String textColor, String website, String contactEmailAddress, Long planId, String chargebeePlanName,
                        String chargebeeCustomerId,String subscriptionId, boolean showTrayIntegration, Long whiteLabelId) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.organizerDescription = organizerDescription;
        this.logoImage = logoImage;
        this.facebookLink = facebookLink;
        this.twitterLink = twitterLink;
        this.linkedInLink = linkedInLink;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
        this.website = website;
        this.contactEmailAddress = contactEmailAddress;
        this.planId = planId;
        this.chargebeePlanName = chargebeePlanName;
        this.chargebeeCustomerId = chargebeeCustomerId;
        this.subscriptionId = subscriptionId;
        this.showTrayIntegration = showTrayIntegration;
        this.whiteLabelId = whiteLabelId;
    }


    public OrganizerDto(long organizerId, String name, String organizerPageURL, String organizerDescription, String logoImage, String facebookLink,//NOSONAR
                        String twitterLink, String linkedInLink, String backgroundColor, String textColor, String website,
                        String contactEmailAddress, boolean allowSocialSharing) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.organizerDescription = organizerDescription;
        this.logoImage = logoImage;
        this.facebookLink = facebookLink;
        this.twitterLink = twitterLink;
        this.linkedInLink = linkedInLink;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
        this.website = website;
        this.contactEmailAddress = contactEmailAddress;
        this.allowSocialSharing = allowSocialSharing;
    }

    public OrganizerDto(long organizerId, String name, String organizerPageURL) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
    }

    public OrganizerDto(long organizerId, String name, String organizerPageURL, Long whiteLabelId) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.whiteLabelId = whiteLabelId;
    }

    public OrganizerDto(String name, String organizerPageURL) {
        this.name = name;
        this.organizerPageURL = organizerPageURL;
    }

    public OrganizerDto(long organizerId,String name, String organizerPageURL, String logoImage) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.logoImage = logoImage;
    }

    public OrganizerDto(long organizerId, String name, String organizerPageURL, String organizerDescription, String logoImage, String facebookLink,//NOSONAR
                        String twitterLink, String linkedInLink, String backgroundColor, String textColor, String website,
                        String contactEmailAddress, Long planId, String chargebeePlanName, String chargebeeCustomerId,
                        String subscriptionId, boolean showTrayIntegration, Long whiteLabelId,boolean allowSocialSharing) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.organizerDescription = organizerDescription;
        this.logoImage = logoImage;
        this.facebookLink = facebookLink;
        this.twitterLink = twitterLink;
        this.linkedInLink = linkedInLink;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
        this.website = website;
        this.contactEmailAddress = contactEmailAddress;
        this.planId = planId;
        this.chargebeePlanName = chargebeePlanName;
        this.chargebeeCustomerId = chargebeeCustomerId;
        this.subscriptionId = subscriptionId;
        this.showTrayIntegration = showTrayIntegration;
        this.whiteLabelId = whiteLabelId;
        this.allowSocialSharing = allowSocialSharing;
    }


    public OrganizerDto(long organizerId, String name, String organizerPageURL, String organizerDescription, String logoImage, String facebookLink,//NOSONAR
                        String twitterLink, String linkedInLink, String backgroundColor, String textColor, String website,
                        String contactEmailAddress, Long planId, String chargebeePlanName, String chargebeeCustomerId,
                        String subscriptionId, boolean showTrayIntegration, Long whiteLabelId,boolean allowSocialSharing,
                        BillingType billingType,Long mobileChargebeePlanId,String mobileChargebeeCustomerId,String mobileChargebeeSubscriptionId,String mobileChargebeePlanName, Long hubspotCompanyId) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.organizerDescription = organizerDescription;
        this.logoImage = logoImage;
        this.facebookLink = facebookLink;
        this.twitterLink = twitterLink;
        this.linkedInLink = linkedInLink;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
        this.website = website;
        this.contactEmailAddress = contactEmailAddress;
        this.planId = planId;
        this.chargebeePlanName = chargebeePlanName;
        this.chargebeeCustomerId = chargebeeCustomerId;
        this.subscriptionId = subscriptionId;
        this.showTrayIntegration = showTrayIntegration;
        this.whiteLabelId = whiteLabelId;
        this.allowSocialSharing = allowSocialSharing;
        this.billingType = billingType;
        this.mobilePlanId=mobileChargebeePlanId;
        this.mobileChargebeeCustomerId=mobileChargebeeCustomerId;
        this.mobileChargebeeSubscriptionId=mobileChargebeeSubscriptionId;
        this.mobileChargebeePlanName=mobileChargebeePlanName;
        this.hubspotCompanyId = hubspotCompanyId;
    }
    public OrganizerDto(long organizerId, String name, String organizerPageURL, String organizerDescription, String logoImage, String facebookLink,//NOSONAR
                        String twitterLink, String linkedInLink, String backgroundColor, String textColor, String website,
                        String contactEmailAddress, Long planId, String chargebeePlanName,String chargebeeDisplayPlanName, String chargebeeCustomerId,
                        String subscriptionId, boolean showTrayIntegration, Long whiteLabelId,boolean allowSocialSharing,
                        BillingType billingType,Long mobileChargebeePlanId,String mobileChargebeeCustomerId,String mobileChargebeeSubscriptionId,String mobileChargebeePlanName, Long hubspotCompanyId) {
        this.organizerId = organizerId;
        this.name = name;
        this.organizerPageURL = organizerPageURL;
        this.organizerDescription = organizerDescription;
        this.logoImage = logoImage;
        this.facebookLink = facebookLink;
        this.twitterLink = twitterLink;
        this.linkedInLink = linkedInLink;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
        this.website = website;
        this.contactEmailAddress = contactEmailAddress;
        this.planId = planId;
        this.chargebeePlanName = chargebeePlanName;
        this.chargebeeDisplayPlanName=chargebeeDisplayPlanName;
        this.chargebeeCustomerId = chargebeeCustomerId;
        this.subscriptionId = subscriptionId;
        this.showTrayIntegration = showTrayIntegration;
        this.whiteLabelId = whiteLabelId;
        this.allowSocialSharing = allowSocialSharing;
        this.billingType = billingType;
        this.mobilePlanId=mobileChargebeePlanId;
        this.mobileChargebeeCustomerId=mobileChargebeeCustomerId;
        this.mobileChargebeeSubscriptionId=mobileChargebeeSubscriptionId;
        this.mobileChargebeePlanName=mobileChargebeePlanName;
        this.hubspotCompanyId = hubspotCompanyId;
    }


    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public Long getOrganizerId() {
        return organizerId;
    }

    public void setOrganizerId(Long organizerId) {
        this.organizerId = organizerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrganizerPageURL() {
        return organizerPageURL;
    }

    public void setOrganizerPageURL(String organizerPageURL) {
        this.organizerPageURL = organizerPageURL;
    }

    public String getOrganizerDescription() {
        return organizerDescription;
    }

    public void setOrganizerDescription(String organizerDescription) {
        this.organizerDescription = organizerDescription;
    }

    public String getLogoImage() {
        return logoImage;
    }

    public void setLogoImage(String logoImage) {
        this.logoImage = logoImage;
    }

    public String getFacebookLink() {
        return facebookLink;
    }

    public void setFacebookLink(String facebookLink) {
        this.facebookLink = facebookLink;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getContactEmailAddress() {
        return contactEmailAddress;
    }

    public void setContactEmailAddress(String contactEmailAddress) {
        this.contactEmailAddress = contactEmailAddress;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }


    public String getChargebeePlanName() {
        return chargebeePlanName;
    }

    public void setChargebeePlanName(String chargebeePlanName) {
        this.chargebeePlanName = chargebeePlanName;
    }

    public String getChargebeeCustomerId() {
        return chargebeeCustomerId;
    }

    public void setChargebeeCustomerId(String chargebeeCustomerId) {
        this.chargebeeCustomerId = chargebeeCustomerId;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getPlanEndDate() {
        return planEndDate;
    }

    public void setPlanEndDate(String planEndDate) {
        this.planEndDate = planEndDate;
    }

    public String getRenewalDate() {
        return renewalDate;
    }

    public void setRenewalDate(String renewalDate) {
        this.renewalDate = renewalDate;
    }

    public Boolean isShowTrayIntegration() {
        return showTrayIntegration;
    }

    public void setShowTrayIntegration(Boolean showTrayIntegration) {
        this.showTrayIntegration = showTrayIntegration;
    }

    public String getTwitterLink() {
        return twitterLink;
    }

    public void setTwitterLink(String twitterLink) {
        this.twitterLink = twitterLink;
    }

    public String getLinkedInLink() {
        return linkedInLink;
    }

    public void setLinkedInLink(String linkedInLink) {
        this.linkedInLink = linkedInLink;
    }

    public Long getWhiteLabelId() {
        return whiteLabelId;
    }

    public void setWhiteLabelId(Long whiteLabelId) {
        this.whiteLabelId = whiteLabelId;
    }

    public Long getCountOfWhiteLabelEvents() {
        return countOfWhiteLabelEvents;
    }

    public void setCountOfWhiteLabelEvents(Long countOfWhiteLabelEvents) {
        this.countOfWhiteLabelEvents = countOfWhiteLabelEvents;
    }

    public Long getSoldTicketCountForWhiteLabel() {
        return soldTicketCountForWhiteLabel;
    }

    public void setSoldTicketCountForWhiteLabel(Long soldTicketCountForWhiteLabel) {
        this.soldTicketCountForWhiteLabel = soldTicketCountForWhiteLabel;
    }

    public Long getTotalTicketCountForWhiteLabel() {
        return totalTicketCountForWhiteLabel;
    }

    public void setTotalTicketCountForWhiteLabel(Long totalTicketCountForWhiteLabel) {
        this.totalTicketCountForWhiteLabel = totalTicketCountForWhiteLabel;
    }

    public List<OrganizerChargeInfoDto> getOrganizerChargeInfoDtos() {
        return organizerChargeInfoDtos;
    }

    public void setOrganizerChargeInfoDtos(List<OrganizerChargeInfoDto> organizerChargeInfoDtos) {
        this.organizerChargeInfoDtos = organizerChargeInfoDtos;
    }

    public Boolean isAllowSocialSharing() {
        return allowSocialSharing;
    }

    public void setAllowSocialSharing(Boolean allowSocialSharing) {
        this.allowSocialSharing = allowSocialSharing;
    }

    public BillingType getBillingType() {
        return billingType;
    }

    public void setBillingType(BillingType billingType) {
        this.billingType = billingType;
    }

    public String getChargebeePlanId() {
        return chargebeePlanId;
    }

    public void setChargebeePlanId(String chargebeePlanId) {
        this.chargebeePlanId = chargebeePlanId;
    }

    public String getWhiteLabelURL() {
        return whiteLabelURL;
    }

    public void setWhiteLabelURL(String whiteLabelURL) {
        this.whiteLabelURL = whiteLabelURL;
    }

    public String getWhiteLabelFavIcon() {
        return whiteLabelFavIcon;
    }

    public void setWhiteLabelFavIcon(String whiteLabelFavIcon) {
        this.whiteLabelFavIcon = whiteLabelFavIcon;
    }

    public String getWhiteLabelFirmName() {
        return whiteLabelFirmName;
    }

    public void setWhiteLabelFirmName(String whiteLabelFirmName) {
        this.whiteLabelFirmName = whiteLabelFirmName;
    }

    public String getHeaderBackgroundColor() {
        return headerBackgroundColor;
    }

    public void setHeaderBackgroundColor(String headerBackgroundColor) {
        this.headerBackgroundColor = headerBackgroundColor;
    }

    public String getHeaderTextColor() {
        return headerTextColor;
    }

    public void setHeaderTextColor(String headerTextColor) {
        this.headerTextColor = headerTextColor;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getHeaderLogoImage() {
        return headerLogoImage;
    }

    public void setHeaderLogoImage(String headerLogoImage) {
        this.headerLogoImage = headerLogoImage;
    }

    public boolean isHideProductUpdateNotification() {
        return hideProductUpdateNotification;
    }

    public void setHideProductUpdateNotification(boolean hideProductUpdateNotification) {
        this.hideProductUpdateNotification = hideProductUpdateNotification;
    }

    public Long getMobilePlanId() { return mobilePlanId; }

    public void setMobilePlanId(Long mobilePlanId) {
        this.mobilePlanId = mobilePlanId;
    }

    public String getMobileChargebeePlanName() { return mobileChargebeePlanName; }

    public void setMobileChargebeePlanName(String mobileChargebeePlanName) {
        this.mobileChargebeePlanName = mobileChargebeePlanName;
    }

    public String getMobileChargebeeCustomerId() { return mobileChargebeeCustomerId; }

    public void setMobileChargebeeCustomerId(String mobileChargebeeCustomerId) {
        this.mobileChargebeeCustomerId = mobileChargebeeCustomerId;
    }

    public String getMobileChargebeeSubscriptionId() {
        return mobileChargebeeSubscriptionId;
    }

    public void setMobileChargebeeSubscriptionId(String mobileChargebeeSubscriptionId) {
        this.mobileChargebeeSubscriptionId = mobileChargebeeSubscriptionId;
    }

    public Long getHubspotCompanyId() {
        return hubspotCompanyId;
    }

    public void setHubspotCompanyId(Long hubspotCompanyId) {
        this.hubspotCompanyId = hubspotCompanyId;
    }

    public String getWhiteLabelHostBaseUrl() {
        return whiteLabelHostBaseUrl;
    }

    public void setWhiteLabelHostBaseUrl(String whiteLabelHostBaseUrl) {
        this.whiteLabelHostBaseUrl = whiteLabelHostBaseUrl;
    }

    public boolean isEnableWhiteLabelRedirect() {
        return enableWhiteLabelRedirect;
    }

    public void setEnableWhiteLabelRedirect(boolean enableWhiteLabelRedirect) {
        this.enableWhiteLabelRedirect = enableWhiteLabelRedirect;
    }
}
