package com.accelevents.dto;

import com.accelevents.utils.Constants;

public class UserAttendeeDTO {

    private Long userId;

    private String firstName;

    private String lastName;

    private String email;

    private String photo;

    private Long eventTicketId;

    private Long orderId;

    private String ticketStatus;

    private String entryExitStatus;

    private String barcodeId;

    public UserAttendeeDTO() {
    }

    public UserAttendeeDTO(Long userId, String firstName, String lastName, String email, String photo) {
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.photo = photo;
    }

    public UserAttendeeDTO(Long userId, String firstName, String lastName, String email, String photo, Long eventTicketId, Long orderId) {
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.photo = photo;
        this.eventTicketId = eventTicketId;
        this.orderId = orderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public Long getEventTicketId() {
        return eventTicketId;
    }

    public void setEventTicketId(Long eventTicketId) {
        this.eventTicketId = eventTicketId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getTicketStatus() {
        return ticketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        this.ticketStatus = ticketStatus;
    }

    public String getEntryExitStatus() {
        return entryExitStatus;
    }

    public void setEntryExitStatus(String entryExitStatus) {
        this.entryExitStatus = entryExitStatus;
    }

    public String getBarcodeId() {
        return barcodeId;
    }

    public void setBarcodeId(String barcodeId) {
        this.barcodeId = barcodeId;
    }
}
