package com.accelevents.dto;

import com.accelevents.domain.EventLevelSettings;
import com.accelevents.domain.enums.SessionTimeConflictsEnum;
import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.persistence.Column;

public class EventLevelSettingsDTO {
    @Schema(description ="Enable/Disable People Registration Tab")
    private boolean isEnablePeopleRegistrationTab;

    private boolean showSaveASeatButton = Boolean.TRUE;

    private boolean isSendSaveASeatEmail = Boolean.FALSE;

    private boolean reviewerRegistrationApproval = Boolean.FALSE;

    private boolean reviewerRegistrationSubmission = Boolean.FALSE;

    private boolean showCECredits = Boolean.FALSE;

    private boolean ssoRequired = Boolean.FALSE;

    private boolean enableSSOMapping = Boolean.FALSE;

    private boolean isRecheckInEnabled;

    private String sessionRegisterButton = Constants.SAVE_A_SEAT;

    private String sessionRegisteredButton = Constants.SEAT_SAVED;

    private String sessionCapacityReachedButton = Constants.UNAVAILABLE;

    private SessionTimeConflictsEnum sessionTimeConflicts = SessionTimeConflictsEnum.ALLOW_OVERRIDE;

    private boolean allowSessionOverlap = Boolean.FALSE;

    private int concurrentSessionOverlapTime = 15;

    private boolean allowAttendeeToDownloadCertificate = Boolean.FALSE;

    private boolean enableRFIDBadge = Boolean.FALSE;

    private boolean surveyRequiresCheckIn = Boolean.TRUE;

    private boolean allowOrderExchange = Boolean.FALSE;

    private boolean displayStaffAndSpeakerBadges = Boolean.FALSE;

    private boolean allowStaffToManageOrders = Boolean.FALSE;

    private boolean useAppsync = Boolean.TRUE;

    private boolean enablePreRegistrationRouting = Boolean.FALSE;

    public boolean isEnablePeopleRegistrationTab() {
        return isEnablePeopleRegistrationTab;
    }

    public void setEnablePeopleRegistrationTab(boolean enablePeopleRegistrationTab) {
        isEnablePeopleRegistrationTab = enablePeopleRegistrationTab;
    }

    public boolean isShowSaveASeatButton() {
        return showSaveASeatButton;
    }

    public void setShowSaveASeatButton(boolean showSaveASeatButton) {
        this.showSaveASeatButton = showSaveASeatButton;
    }

    public boolean isSendSaveASeatEmail() {
        return isSendSaveASeatEmail;
    }

    public void setSendSaveASeatEmail(boolean sendSaveASeatEmail) {
        isSendSaveASeatEmail = sendSaveASeatEmail;
    }

    public boolean isReviewerRegistrationApproval() {
        return reviewerRegistrationApproval;
    }

    public void setReviewerRegistrationApproval(boolean reviewerRegistrationApproval) {
        this.reviewerRegistrationApproval = reviewerRegistrationApproval;
    }

    public boolean isReviewerRegistrationSubmission() {
        return reviewerRegistrationSubmission;
    }

    public void setReviewerRegistrationSubmission(boolean reviewerRegistrationSubmission) {
        this.reviewerRegistrationSubmission = reviewerRegistrationSubmission;
    }

    public boolean isSurveyRequiresCheckIn() {
        return surveyRequiresCheckIn;
    }

    public void setSurveyRequiresCheckIn(boolean surveyRequiresCheckIn) {
        this.surveyRequiresCheckIn = surveyRequiresCheckIn;
    }

    public void toEntity(Long eventId, EventLevelSettings eventLevelSettings){
        eventLevelSettings.setEventId(eventId);
        eventLevelSettings.setEnablePeopleRegistrationTab(this.isEnablePeopleRegistrationTab);
        eventLevelSettings.setShowSaveASeatButton(this.showSaveASeatButton);
        eventLevelSettings.setSendSaveASeatEmail(Boolean.FALSE);
        if (eventLevelSettings.isShowSaveASeatButton()) {
            eventLevelSettings.setSendSaveASeatEmail(this.isSendSaveASeatEmail);
        }
        eventLevelSettings.setReviewerRegistrationApproval(this.reviewerRegistrationApproval);
        eventLevelSettings.setShowCECredits(this.showCECredits);
        eventLevelSettings.setSsoRequired(this.isSsoRequired());
        eventLevelSettings.setEnableSSOMapping(this.enableSSOMapping);
        eventLevelSettings.setRecheckInEnabled(this.isRecheckInEnabled);
        eventLevelSettings.setSessionRegisterButton(this.sessionRegisterButton);
        eventLevelSettings.setSessionRegisteredButton(this.sessionRegisteredButton);
        eventLevelSettings.setSessionCapacityReachedButton(this.sessionCapacityReachedButton);
        eventLevelSettings.setSessionTimeConflicts(this.sessionTimeConflicts);
        eventLevelSettings.setConcurrentSessionOverlapTime(this.concurrentSessionOverlapTime);
        eventLevelSettings.setAllowSessionOverlap(this.allowSessionOverlap);
        eventLevelSettings.setAllowAttendeeToDownloadCertificate(this.allowAttendeeToDownloadCertificate);
        eventLevelSettings.setEnableRFIDBadge(this.enableRFIDBadge);
        eventLevelSettings.setSurveyRequiresCheckIn(this.surveyRequiresCheckIn);
        eventLevelSettings.setAllowOrderExchange(this.allowOrderExchange);
        eventLevelSettings.setDisplayStaffAndSpeakerBadges(this.displayStaffAndSpeakerBadges);
        eventLevelSettings.setAllowStaffToManageOrders(this.allowStaffToManageOrders);
        eventLevelSettings.setUseAppsync(this.useAppsync);
        eventLevelSettings.setEnablePreRegistrationRouting(this.enablePreRegistrationRouting);
    }

    public void toDTO(EventLevelSettings eventLevelSettings) {
        this.isEnablePeopleRegistrationTab = eventLevelSettings.isEnablePeopleRegistrationTab();
        this.showSaveASeatButton = eventLevelSettings.isShowSaveASeatButton();
        this.isSendSaveASeatEmail = eventLevelSettings.isSendSaveASeatEmail();
        this.reviewerRegistrationApproval = eventLevelSettings.isReviewerRegistrationApproval();
        this.reviewerRegistrationSubmission = eventLevelSettings.isReviewerRegistrationSubmission();
        this.showCECredits = eventLevelSettings.isShowCECredits();
        this.ssoRequired = eventLevelSettings.isSsoRequired();
        this.enableSSOMapping = eventLevelSettings.isEnableSSOMapping();
        this.isRecheckInEnabled = eventLevelSettings.isRecheckInEnabled();
        this.sessionRegisterButton = eventLevelSettings.getSessionRegisterButton();
        this.sessionRegisteredButton = eventLevelSettings.getSessionRegisteredButton();
        this.sessionCapacityReachedButton = eventLevelSettings.getSessionCapacityReachedButton();
        this.sessionTimeConflicts = eventLevelSettings.getSessionTimeConflicts();
        this.concurrentSessionOverlapTime = eventLevelSettings.getConcurrentSessionOverlapTime();
        this.allowSessionOverlap = eventLevelSettings.isAllowSessionOverlap();
        this.allowAttendeeToDownloadCertificate = eventLevelSettings.isAllowAttendeeToDownloadCertificate();
        this.enableRFIDBadge = eventLevelSettings.isEnableRFIDBadge();
        this.surveyRequiresCheckIn = eventLevelSettings.isSurveyRequiresCheckIn();
        this.allowOrderExchange = eventLevelSettings.isAllowOrderExchange();
        this.displayStaffAndSpeakerBadges = eventLevelSettings.isDisplayStaffAndSpeakerBadges();
        this.allowStaffToManageOrders = eventLevelSettings.isAllowStaffToManageOrders();
        this.useAppsync = eventLevelSettings.isUseAppsync();
        this.enablePreRegistrationRouting = eventLevelSettings.isEnablePreRegistrationRouting();
    }

    @Override
    public String toString() {
        return "EventLevelSettingsDTO{" +
                "isEnablePeopleRegistrationTab=" + isEnablePeopleRegistrationTab +
                ", showSaveASeatButton=" + showSaveASeatButton +
                ", isSendSaveASeatEmail=" + isSendSaveASeatEmail +
                ", reviewerRegistrationApproval=" + reviewerRegistrationApproval +
                ", showCECredits=" + showCECredits +
                ", ssoRequired=" + ssoRequired +
                ", enableSSOMapping=" + enableSSOMapping +
                ", isRecheckInEnabled=" + isRecheckInEnabled +
                ", sessionRegisterButton='" + sessionRegisterButton +
                ", sessionRegisteredButton='" + sessionRegisteredButton +
                ", sessionCapacityReachedButton='" + sessionCapacityReachedButton +
                ", sessionTimeConflicts=" + sessionTimeConflicts +
                ", concurrentSessionOverlapTime=" + concurrentSessionOverlapTime +
                ", allowSessionOverlap=" + allowSessionOverlap +
                ", allowAttendeeDownloadCertificate=" + allowAttendeeToDownloadCertificate +
                ", allowOrderExchange=" + allowOrderExchange +
                ", useAppsync=" + useAppsync +
                ", enablePreRegistrationRouting=" + enablePreRegistrationRouting +
                '}';
    }

    public boolean isShowCECredits() {
        return showCECredits;
    }

    public void setShowCECredits(boolean showCECredits) {
        this.showCECredits = showCECredits;
    }

    public boolean isSsoRequired() {
        return ssoRequired;
    }

    public void setSsoRequired(boolean ssoRequired) {
        this.ssoRequired = ssoRequired;
    }

    public boolean isRecheckInEnabled() {return isRecheckInEnabled; }

    public void setRecheckInEnabled(boolean recheckInEnabled) {isRecheckInEnabled = recheckInEnabled; }

    public boolean isEnableSSOMapping() {
        return enableSSOMapping;
    }

    public void setEnableSSOMapping(boolean enableSSOMapping) {
        this.enableSSOMapping = enableSSOMapping;
    }

    public SessionTimeConflictsEnum getSessionTimeConflicts() {
        return sessionTimeConflicts;
    }

    public void setSessionTimeConflicts(SessionTimeConflictsEnum sessionTimeConflicts) {
        this.sessionTimeConflicts = sessionTimeConflicts;
    }

    public String getSessionRegisterButton() {
        return sessionRegisterButton;
    }

    public void setSessionRegisterButton(String sessionRegisterButton) {
        this.sessionRegisterButton = sessionRegisterButton;
    }

    public String getSessionRegisteredButton() {
        return sessionRegisteredButton;
    }

    public void setSessionRegisteredButton(String sessionRegisteredButton) {
        this.sessionRegisteredButton = sessionRegisteredButton;
    }

    public String getSessionCapacityReachedButton() {
        return sessionCapacityReachedButton;
    }

    public void setSessionCapacityReachedButton(String sessionCapacityReachedButton) {
        this.sessionCapacityReachedButton = sessionCapacityReachedButton;
    }

    public int getConcurrentSessionOverlapTime() {
        return concurrentSessionOverlapTime;
    }

    public void setConcurrentSessionOverlapTime(int concurrentSessionOverlapTime) {
        this.concurrentSessionOverlapTime = concurrentSessionOverlapTime;
    }

    public boolean isAllowSessionOverlap() {
        return allowSessionOverlap;
    }

    public void setAllowSessionOverlap(boolean allowSessionOverlap) {
        this.allowSessionOverlap = allowSessionOverlap;
    }

    public boolean isAllowAttendeeToDownloadCertificate() {
        return allowAttendeeToDownloadCertificate;
    }

    public void setAllowAttendeeToDownloadCertificate(boolean allowAttendeeToDownloadCertificate) {
        this.allowAttendeeToDownloadCertificate = allowAttendeeToDownloadCertificate;
    }

    public boolean isEnableRFIDBadge() {
        return enableRFIDBadge;
    }

    public void setEnableRFIDBadge(boolean enableRFIDBadge) {
        this.enableRFIDBadge = enableRFIDBadge;
    }

    public boolean isAllowOrderExchange() {
        return allowOrderExchange;
    }

    public void setAllowOrderExchange(boolean allowOrderExchange) {
        this.allowOrderExchange = allowOrderExchange;
    }

    public boolean isDisplayStaffAndSpeakerBadges() {
        return displayStaffAndSpeakerBadges;
    }

    public void setDisplayStaffAndSpeakerBadges(boolean displayStaffAndSpeakerBadges) {
        this.displayStaffAndSpeakerBadges = displayStaffAndSpeakerBadges;
    }

    public boolean isAllowStaffToManageOrders() {
        return allowStaffToManageOrders;
    }

    public void setAllowStaffToManageOrders(boolean allowStaffToManageOrders) {
        this.allowStaffToManageOrders = allowStaffToManageOrders;
    }

    public boolean isUseAppsync() {
        return useAppsync;
    }

    public void setUseAppsync(boolean useAppsync) {
        this.useAppsync = useAppsync;
    }

    public boolean isEnablePreRegistrationRouting() { return enablePreRegistrationRouting; }

    public void setEnablePreRegistrationRouting(boolean enablePreRegistrationRouting) { this.enablePreRegistrationRouting = enablePreRegistrationRouting; }
}
