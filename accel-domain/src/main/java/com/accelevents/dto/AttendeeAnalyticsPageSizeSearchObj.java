package com.accelevents.dto;

import com.accelevents.column.selection.dto.ColumnDto;
import com.accelevents.domain.enums.EventRequestFormSubmissionStatus;

import java.util.List;

import org.springframework.data.domain.Sort;

public class AttendeeAnalyticsPageSizeSearchObj extends PageSizeSearchObj {
    private Long displayViewId;
    private List<Long> filterTicketTypeIds;
    private List<String> filterTicketStatus;
    private List<String> filterCompany;
    private String advanceFilterJson;
    private String advanceStaticFilterJson;
    private List<ColumnDto> editColumns;
    private List<EventRequestFormSubmissionStatus> filterEventStatus;


    public AttendeeAnalyticsPageSizeSearchObj() {

    }

    public AttendeeAnalyticsPageSizeSearchObj(int page, int size, String search) {
        super(page, size, search);
    }
    
    public AttendeeAnalyticsPageSizeSearchObj(int page, int size, String search,String sortColumn,
    		Sort.Direction sortDirection,
    		List<EventRequestFormSubmissionStatus> filterEventStatus) {
        super(page, size, search,sortColumn,sortDirection);
        this.filterEventStatus = filterEventStatus;
    }

    public AttendeeAnalyticsPageSizeSearchObj(int page, int size, String search,String sortColumn,
                                              Sort.Direction sortDirection
                                              ) {
        super(page, size, search,sortColumn,sortDirection);
    }

    public Long getDisplayViewId() {
        return displayViewId;
    }

    public void setDisplayViewId(Long displayViewId) {
        this.displayViewId = displayViewId;
    }

    public List<Long> getFilterTicketTypeIds() { return filterTicketTypeIds; }

    public void setFilterTicketTypeIds(List<Long> filterTicketTypeIds) { this.filterTicketTypeIds = filterTicketTypeIds; }

    public List<String> getFilterTicketStatus() { return filterTicketStatus; }

    public void setFilterTicketStatus(List<String> filterTicketStatus) { this.filterTicketStatus = filterTicketStatus; }

    public List<String> getFilterCompany() { return filterCompany; }

    public void setFilterCompany(List<String> filterCompany) { this.filterCompany = filterCompany; }

    public String getAdvanceFilterJson() { return advanceFilterJson; }

    public void setAdvanceFilterJson(String advanceFilterJson) { this.advanceFilterJson = advanceFilterJson; }

    public List<ColumnDto> getEditColumns() {
        return editColumns;
    }

    public void setEditColumns(List<ColumnDto> editColumns) {
        this.editColumns = editColumns;
    }
    
    public List<EventRequestFormSubmissionStatus> getFilterEventStatus() {
		return filterEventStatus;
	}
	public void setFilterEventStatus(List<EventRequestFormSubmissionStatus> filterEventStatus) {
		this.filterEventStatus = filterEventStatus;
	}

    public String getAdvanceStaticFilterJson() {
        return advanceStaticFilterJson;
    }

    public void setAdvanceStaticFilterJson(String advanceStaticFilterJson) {
        this.advanceStaticFilterJson = advanceStaticFilterJson;
    }
}
