package com.accelevents.dto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

import static com.accelevents.utils.Constants.*;
import static java.util.Collections.emptyList;

public class PageUtil {

    private static final Logger log = LoggerFactory.getLogger(PageUtil.class);

    public static <T> PageImpl<T> getPageable(List<T> items, Pageable pageable) {
        int start = Math.toIntExact(pageable.getOffset());
        int end = (start + pageable.getPageSize()) > items.size() ? items.size() : (start + pageable.getPageSize());
        if (start <= end) {
            return new PageImpl<T>(items.subList(start, end), pageable, items.size());
        } else {
            return new PageImpl<T>(emptyList(), pageable, items.size());
        }
    }

    public static void validateSortingForOrgEventListing(String sortColumnName) {
        if (!(NAME_SMALL.equals(sortColumnName) || EVENT_END_DATE_SMALL_UNDERSCORE_SEP.equals(sortColumnName))) {
            log.info("Invalid sortBy value: {}, defaulting to name sorting", sortColumnName);
            throw new IllegalArgumentException("Sorting is not supported for the " + sortColumnName + " column.");
        }
    }

}
