package com.accelevents.dto;

import java.io.Serializable;

public class LoginUserWLOrgEventDetailsDto implements Serializable {
    private Long id;
    private String name;
    private String url;
    private String imageUrl;
    private String role;
    private boolean is2FAEnabled;

    public LoginUserWLOrgEventDetailsDto() {
    }
    public LoginUserWLOrgEventDetailsDto(Long id, String name, String url, String imageUrl) {
        this.id = id;
        this.name = name;
        this.url = url;
        this.imageUrl = imageUrl;
    }

    public LoginUserWLOrgEventDetailsDto(Long id, String name, String url, String imageUrl, boolean is2FAEnabled,String role) {
        this.id = id;
        this.name = name;
        this.url = url;
        this.imageUrl = imageUrl;
        this.is2FAEnabled = is2FAEnabled;
        this.role = role;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean isIs2FAEnabled() {
        return is2FAEnabled;
    }

    public void setIs2FAEnabled(boolean is2FAEnabled) {
        this.is2FAEnabled = is2FAEnabled;
    }

    public String getRole() {
        return role;
    }
    public void setRole(String role) {
        this.role = role;
    }
}
