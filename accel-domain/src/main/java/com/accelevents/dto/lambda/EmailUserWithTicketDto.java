package com.accelevents.dto.lambda;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class EmailUserWithTicketDto extends EventTaskUserEmailDto implements Serializable {

    private static final long serialVersionUID = 6215983334023548444L;

    private String magicLink;
    private Long orderNumber;
    private String orderStatus;
    private Long ticketNumber;
    private String ticketTypeName;
    private String ticketTypeDesc;
    private Long autoAssignedSeqNumber;
    private String seatNumber;
    private String ticketBarcodeId;
    private boolean allowPDFDownload;
    private Double price;

    private String purchaserFirstName;
    private String purchaserLastName;
    private String purchaserEmail;
    private Date purchaseDate;
    private boolean holderEmail;
    private Map<String, Object> buyerAttributesValuesMap = new HashMap<>();
    private Map<String, Object> holderAttributesValuesMap = new HashMap<>();
    private String ticketPdfDesign;

    private String ticketCode;

    // Contact attribute fields for CONTACT_LIST recipients
    private Long contactAttributeId;
    private String contactAttributeJsonValue;

    // CC email addresses extracted from CC_EMAIL_ADDRESS type custom attributes
    private Set<String> ccEmails = new HashSet<>();

    // Phone number from contact table
    private String phoneNumber;

    public EmailUserWithTicketDto() {
    }

    public EmailUserWithTicketDto(String firstName, String lastName, String email) {
        super(firstName, lastName, email);
    }

    public EmailUserWithTicketDto(String firstName, String lastName, String email, Long orderNumber, Long ticketNumber, String ticketTypeName, Long autoAssignedSeqNumber) {
        super(firstName, lastName, email);
        this.orderNumber = orderNumber;
        this.ticketNumber = ticketNumber;
        this.ticketTypeName = ticketTypeName;
        this.autoAssignedSeqNumber = autoAssignedSeqNumber;
    }

    public EmailUserWithTicketDto(String firstName, String lastName, String email, Long orderNumber, Long ticketNumber, String ticketTypeName, Long autoAssignedSeqNumber, String orderStatus) {
        super(firstName, lastName, email);
        this.orderNumber = orderNumber;
        this.ticketNumber = ticketNumber;
        this.ticketTypeName = ticketTypeName;
        this.autoAssignedSeqNumber = autoAssignedSeqNumber;
        this.orderStatus = orderStatus;
    }

    public String getMagicLink() {
        return magicLink;
    }

    public void setMagicLink(String magicLink) {
        this.magicLink = magicLink;
    }

    public Long getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Long orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getOrderNumberStr() {
        return this.getOrderNumber()!=null && this.getOrderNumber() > 0 ? this.orderNumber.toString() : "N/A";
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Long getTicketNumber() {
        return ticketNumber;
    }

    public void setTicketNumber(Long ticketNumber) {
        this.ticketNumber = ticketNumber;
    }

    public String getTicketNumberStr(){
        return this.getTicketNumber()!=null && this.getTicketNumber() > 0 ? this.getTicketNumber().toString() : "N/A";
    }

    public String getTicketTypeName() {
        return ticketTypeName;
    }

    public void setTicketTypeName(String ticketTypeName) {
        this.ticketTypeName = ticketTypeName;
    }

    public String getTicketTypeDesc() {
        return ticketTypeDesc;
    }

    public void setTicketTypeDesc(String ticketTypeDesc) {
        this.ticketTypeDesc = ticketTypeDesc;
    }

    public Long getAutoAssignedSeqNumber() {
        return autoAssignedSeqNumber;
    }

    public void setAutoAssignedSeqNumber(Long autoAssignedSeqNumber) {
        this.autoAssignedSeqNumber = autoAssignedSeqNumber;
    }

    public String getAutoAssignedSeqNumberStr() {
        return this.getAutoAssignedSeqNumber()!=null && this.getAutoAssignedSeqNumber() > 0 ? this.getAutoAssignedSeqNumber().toString() : "N/A";
    }

    public String getSeatNumber() {
        return this.seatNumber!=null ? this.seatNumber : "N/A";
    }

    public void setSeatNumber(String seatNumber) {
        this.seatNumber = seatNumber;
    }

    public String getTicketBarcodeId() {
        return ticketBarcodeId;
    }

    public void setTicketBarcodeId(String ticketBarcodeId) {
        this.ticketBarcodeId = ticketBarcodeId;
    }

    public boolean isAllowPDFDownload() {
        return allowPDFDownload;
    }

    public void setAllowPDFDownload(boolean allowPDFDownload) {
        this.allowPDFDownload = allowPDFDownload;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getPurchaserFirstName() {
        return purchaserFirstName;
    }

    public void setPurchaserFirstName(String purchaserFirstName) {
        this.purchaserFirstName = purchaserFirstName;
    }

    public String getPurchaserLastName() {
        return purchaserLastName;
    }

    public void setPurchaserLastName(String purchaserLastName) {
        this.purchaserLastName = purchaserLastName;
    }

    public String getPurchaserEmail() {
        return purchaserEmail;
    }

    public void setPurchaserEmail(String purchaserEmail) {
        this.purchaserEmail = purchaserEmail;
    }

    public Date getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Date purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public boolean isHolderEmail() {
        return holderEmail;
    }

    public void setHolderEmail(boolean holderEmail) {
        this.holderEmail = holderEmail;
    }

    public Map<String, Object> getBuyerAttributesValuesMap() { return buyerAttributesValuesMap; }

    public void setBuyerAttributesValuesMap(Map<String, Object> buyerAttributesValuesMap) { this.buyerAttributesValuesMap = buyerAttributesValuesMap; }

    public Map<String, Object> getHolderAttributesValuesMap() { return holderAttributesValuesMap; }

    public void setHolderAttributesValuesMap(Map<String, Object> holderAttributesValuesMap) { this.holderAttributesValuesMap = holderAttributesValuesMap; }



    public String getTicketCode() {
        return ticketCode;
    }

    public void setTicketCode(String ticketCode) {
        this.ticketCode = ticketCode;
    }

    public String getTicketPdfDesign() {
        return ticketPdfDesign;
    }

    public void setTicketPdfDesign(String ticketPdfDesign) {
        this.ticketPdfDesign = ticketPdfDesign;
    }

    public Long getContactAttributeId() {
        return contactAttributeId;
    }

    public void setContactAttributeId(Long contactAttributeId) {
        this.contactAttributeId = contactAttributeId;
    }

    public String getContactAttributeJsonValue() {
        return contactAttributeJsonValue;
    }

    public void setContactAttributeJsonValue(String contactAttributeJsonValue) {
        this.contactAttributeJsonValue = contactAttributeJsonValue;
    }

    public Set<String> getCcEmails() {
        return ccEmails;
    }

    public void setCcEmails(Set<String> ccEmails) {
        this.ccEmails = ccEmails != null ? ccEmails : new HashSet<>();
    }

    public void addCcEmail(String ccEmail) {
        if (this.ccEmails == null) {
            this.ccEmails = new HashSet<>();
        }
        this.ccEmails.add(ccEmail);
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Override
    public String toString() {
        return "EmailUserDto{" +
                "firstName='" + super.getFirstName() + '\'' +
                ", lastName='" + super.getLastName() + '\'' +
                ", email='" + super.getEmail() + '\'' +
                ", magicLink='" + magicLink + '\'' +
                ", orderNumber=" + orderNumber +
                ", ticketNumber=" + ticketNumber +
                ", ticketTypeName='" + ticketTypeName + '\'' +
                ", autoAssignedSeqNumber=" + autoAssignedSeqNumber +
                '}';
    }
}
