package com.accelevents.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.accelevents.domain.User;
import com.accelevents.domain.enums.EventRequestFormSubmissionStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description ="EventRequest")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventRequestDto implements Serializable {
	
	private static final long serialVersionUID = 1L;

	private Long id;
	
	private String firstName;
	
	private String lastName;
	
	private String email;
	
	private String requestFormName;

    private String attributeJson;
	
	private String eventName;
	
	private Date createdAt;
	
	private EventRequestFormSubmissionStatus status;

	private Long whiteLabelId;
	
	private String note;
	
    private List<KeyValueDto> eventRequestFormDetailAttributes;

    private String photo;

    private Long budgetOwnerId;

    private String budgetOwnerFirstName;

    private String budgetOwnerLastName;

    private String budgetOwnerEmail;

    private String budgetOwnerPhoto;

    private Long coordinatorId;

    private String coordinatorFirstName;

    private String coordinatorLastName;

    private String coordinatorEmail;

    private String coordinatorPhoto;

    private Long plannerId;

    private String plannerFirstName;

    private String plannerLastName;

    private String plannerEmail;

    private String plannerPhoto;


    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getRequestFormName() {
		return requestFormName;
	}

	public void setRequestFormName(String requestFormName) {
		this.requestFormName = requestFormName;
	}

	public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public EventRequestFormSubmissionStatus getStatus() {
		return status;
	}

	public void setStatus(EventRequestFormSubmissionStatus status) {
		this.status = status;
	}

	public String getAttributeJson() {
		return attributeJson;
	}

	public void setAttributeJson(String attributeJson) {
		this.attributeJson = attributeJson;
	}
	
	public List<KeyValueDto> getEventRequestFormDetailAttributes() {
		return eventRequestFormDetailAttributes;
	}

	public void setEventRequestFormDetailAttributes(List<KeyValueDto> eventRequestFormDetailAttributes) {
		this.eventRequestFormDetailAttributes = eventRequestFormDetailAttributes;
	}

	public Long getWhiteLabelId() {
		return whiteLabelId;
	}

	public void setWhiteLabelId(Long whiteLabelId) {
		this.whiteLabelId = whiteLabelId;
	}
	
	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getBudgetOwnerFirstName() {
        return budgetOwnerFirstName;
    }

    public void setBudgetOwnerFirstName(String budgetOwnerFirstName) {
        this.budgetOwnerFirstName = budgetOwnerFirstName;
    }

    public String getBudgetOwnerLastName() {
        return budgetOwnerLastName;
    }

    public void setBudgetOwnerLastName(String budgetOwnerLastName) {
        this.budgetOwnerLastName = budgetOwnerLastName;
    }

    public String getBudgetOwnerEmail() {
        return budgetOwnerEmail;
    }

    public void setBudgetOwnerEmail(String budgetOwnerEmail) {
        this.budgetOwnerEmail = budgetOwnerEmail;
    }

    public String getBudgetOwnerPhoto() {
        return budgetOwnerPhoto;
    }

    public void setBudgetOwnerPhoto(String budgetOwnerPhoto) {
        this.budgetOwnerPhoto = budgetOwnerPhoto;
    }

    public String getCoordinatorFirstName() {
        return coordinatorFirstName;
    }

    public void setCoordinatorFirstName(String coordinatorFirstName) {
        this.coordinatorFirstName = coordinatorFirstName;
    }

    public String getCoordinatorLastName() {
        return coordinatorLastName;
    }

    public void setCoordinatorLastName(String coordinatorLastName) {
        this.coordinatorLastName = coordinatorLastName;
    }

    public String getCoordinatorEmail() {
        return coordinatorEmail;
    }

    public void setCoordinatorEmail(String coordinatorEmail) {
        this.coordinatorEmail = coordinatorEmail;
    }

    public String getCoordinatorPhoto() {
        return coordinatorPhoto;
    }

    public void setCoordinatorPhoto(String coordinatorPhoto) {
        this.coordinatorPhoto = coordinatorPhoto;
    }

    public String getPlannerFirstName() {
        return plannerFirstName;
    }

    public void setPlannerFirstName(String plannerFirstName) {
        this.plannerFirstName = plannerFirstName;
    }

    public String getPlannerLastName() {
        return plannerLastName;
    }

    public void setPlannerLastName(String plannerLastName) {
        this.plannerLastName = plannerLastName;
    }

    public String getPlannerEmail() {
        return plannerEmail;
    }

    public void setPlannerEmail(String plannerEmail) {
        this.plannerEmail = plannerEmail;
    }

    public String getPlannerPhoto() {
        return plannerPhoto;
    }

    public void setPlannerPhoto(String plannerPhoto) {
        this.plannerPhoto = plannerPhoto;
    }

    public Long getBudgetOwnerId() {
        return budgetOwnerId;
    }

    public void setBudgetOwnerId(Long budgetOwnerId) {
        this.budgetOwnerId = budgetOwnerId;
    }

    public Long getCoordinatorId() {
        return coordinatorId;
    }

    public void setCoordinatorId(Long coordinatorId) {
        this.coordinatorId = coordinatorId;
    }

    public Long getPlannerId() {
        return plannerId;
    }

    public void setPlannerId(Long plannerId) {
        this.plannerId = plannerId;
    }

    public EventRequestDto(Long id, String firstName, String lastName, String email, String requestFormName,
                           String attributeJson, Date createdAt, EventRequestFormSubmissionStatus status, Long whiteLabelId, String note, String photo,
                           Long budgetOwnerId,String budgetOwnerFirstName, String budgetOwnerLastName, String budgetOwnerEmail, String budgetOwnerPhoto,
                           Long coordinatorId,String coordinatorFirstName, String coordinatorLastName, String coordinatorEmail, String coordinatorPhoto,
                           Long plannerId,String plannerFirstName, String plannerLastName, String plannerEmail, String plannerPhoto) {
		super();
		this.id = id;
		this.firstName = firstName;
		this.lastName = lastName;
		this.email = email;
		this.requestFormName = requestFormName;
		this.attributeJson = attributeJson;
		this.createdAt = createdAt;
		this.status = status;
		this.whiteLabelId = whiteLabelId;
		this.note = note;
        this.photo=photo;
        this.budgetOwnerId = budgetOwnerId;
        this.budgetOwnerFirstName = budgetOwnerFirstName;
        this.budgetOwnerLastName = budgetOwnerLastName;
        this.budgetOwnerEmail = budgetOwnerEmail;
        this.budgetOwnerPhoto = budgetOwnerPhoto;
        this.coordinatorId = coordinatorId;
        this.coordinatorFirstName = coordinatorFirstName;
        this.coordinatorLastName = coordinatorLastName;
        this.coordinatorEmail = coordinatorEmail;
        this.coordinatorPhoto = coordinatorPhoto;
        this.plannerId = plannerId;
        this.plannerFirstName = plannerFirstName;
        this.plannerLastName = plannerLastName;
        this.plannerEmail = plannerEmail;
        this.plannerPhoto = plannerPhoto;

	}
	
	

}
