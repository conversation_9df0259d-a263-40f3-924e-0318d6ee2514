package com.accelevents.dto;

import com.accelevents.domain.EventTickets;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.DataType;
import com.accelevents.utils.JsonMapper;
import com.accelevents.utils.TicketHolderHelperPrimitive;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;
import static com.accelevents.utils.TicketHolderHelperPrimitive.checkSeparator;

public class TicketHolderUtils {
    private static final Logger log = LoggerFactory.getLogger(TicketHolderUtils.class);

    public static List<String> CONTACT_ATTRIBUTES = Arrays.asList(STRING_EMAIL_SPACE,//NOSONAR
            STRING_CELL_SPACE_PHONE,
            AttributeValueType.BILLING_ADDRESS.name(),
            AttributeValueType.SHIPPING_ADDRESS.name());

    public static TicketAttributeValueDto unmarshler(String xml, Unmarshaller unmarshaller, long eventTicketId) {
//		if (unmarshaller == null) {
//			throw new JAXBException("unmarshaller not found!");
//		}

        StringReader reader = new StringReader(xml);
        try {
            return (TicketAttributeValueDto) unmarshaller.unmarshal(reader);
        } catch (JAXBException e) {
            log.info("Exception while xml data unmarshal {} and id {}", xml, eventTicketId);
            e.printStackTrace();
            return null;
        }
    }

    public static void prepareDataList(//NOSONAR
                                       EventTickets eventTicket,
                                       TicketAttributeValueDto1 ticketAttributeValueDto,
                                       List<String> ticketHolderData,
                                       TicketHolderRequiredAttributes attributes,
                                       String ticketBuyerUploadsUrl,
                                       boolean isHolder,
                                       boolean isLeadData,
                                       TicketHolderDetailDto ticketHolderDetailDto,
                                       boolean appendAddOn, boolean isLeadSourceManual) {

        if (attributes.getName().equals(BIRTHDAY)) {
            String birthday = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto,
                    attributes.getName(), isHolder,
                    attributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attributes.getAttributeValueType()));

            if (null != birthday) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
                LocalDate date = LocalDate.parse(birthday, formatter);
                ticketHolderData.add(date.toString());
                return;
            }
        }

        if (STRING_CELL_SPACE_PHONE.equals(attributes.getName())) {
            if (isLeadData) {
                if (isLeadSourceManual) {
                    if (null != ticketHolderDetailDto.getHolderPhoneNumber() && ticketHolderDetailDto.getHolderPhoneNumber() > 0) {
                        ticketHolderData.add(TicketHolderHelperPrimitive.getCellNumber(ticketHolderDetailDto.getHolderCountryCode(), String.valueOf(ticketHolderDetailDto.getHolderPhoneNumber())));
                    } else {
                        ticketHolderData.add(STRING_EMPTY);
                    }
                } else {
                    String phoneNumber = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_PHONE_NUMBER, isHolder, true, false);
                    String countryCode = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_COUNTRY_CODE, isHolder, true, false);
                    if (StringUtils.isNotBlank(phoneNumber)) {
                        String cellNumber = TicketHolderHelperPrimitive.getCellNumber(countryCode, phoneNumber);
                        ticketHolderData.add(cellNumber != null ? cellNumber : STRING_EMPTY);
                    } else {
                        ticketHolderData.add(STRING_EMPTY);
                    }
                }
            } else {
                User ticketPurchaser = eventTicket.getTicketPurchaserId();
                CountryCode tpCountryCode = ticketPurchaser.getCountryCode();
                ticketHolderData.add(TicketHolderHelperPrimitive.getCellNumber(eventTicket.getHolderPhoneNumber(), eventTicket.getHolderCountryCode(),
                        ticketPurchaser.getPhoneNumber(), null != tpCountryCode ? tpCountryCode.name() : CountryCode.US.name(),
                        isHolder, ticketAttributeValueDto));
            }
        } else if (isBillingOrShipping(attributes)) {
            String addressValue = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto,
                    attributes.getName(), isHolder,
                    attributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attributes.getAttributeValueType()));
            addressValue = StringUtils.isBlank(addressValue) || checkSeparator(addressValue) ? ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR : addressValue;
            String[] values = addressValue.split("\\" + ADD_SEPARATOR, -1);
            ticketHolderData.addAll(Arrays.asList(values));
        } else if (attributes.getAttributeValueType().getType().equalsIgnoreCase(AttributeValueType.IMAGE.getType())) {
            String url;
            if (appendAddOn && DataType.ADDON.equals(eventTicket.getDataType())) {
                url = TicketHolderHelperPrimitive.getAddOnAttributeValue(ticketAttributeValueDto,
                        attributes.getName(),
                        attributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attributes.getAttributeValueType()));
            } else {
                url = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto,
                        attributes.getName(), isHolder,
                        attributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attributes.getAttributeValueType()));
            }
            if (StringUtils.isNotEmpty(url)) {
                url = ticketBuyerUploadsUrl + url;
            }
            ticketHolderData.add(url);
        } else if (attributes.getAttributeValueType().getType().equalsIgnoreCase(AttributeValueType.UPLOAD.getType())) { //NOSONAR
            String url = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto,
                    attributes.getName(), isHolder,
                    attributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attributes.getAttributeValueType()));
            if (StringUtils.isNotBlank(url)) {
                url = ticketBuyerUploadsUrl + url;
            }
            ticketHolderData.add(url);
        } else if (AttributeValueType.SINGLE_CHECKBOX.getType().equalsIgnoreCase(attributes.getAttributeValueType().getType())) {
            String value = getTicketAttributeValue(ticketAttributeValueDto, attributes, isHolder);
            if (value == null) {
                String defaultValueJson;
                if (isHolder) {
                    defaultValueJson = attributes.getDefaultValueJsonHolder();
                } else {
                    defaultValueJson = attributes.getDefaultValueJsonPurchaser();
                }
                if (defaultValueJson != null) {
                    JSONObject jsonObjectHolder;
                    try {
                        jsonObjectHolder = new JSONObject(defaultValueJson);
                        if (isHolder) {
                            value = jsonObjectHolder.get("defaultValueForHolder").toString();
                        } else {
                            value = jsonObjectHolder.get("defaultValueForPurchaser").toString();
                        }
                    } catch (JSONException e) {
                        log.info("singleCheckBoxValue parseIssue attributeId {} error {}", attributes.getId(), e.getMessage());
                    }
                }
            }
            ticketHolderData.add(TRUE.equalsIgnoreCase(value) ? "Checked" : "Unchecked");
        } else if (AttributeValueType.INTEREST.getType().equalsIgnoreCase(attributes.getAttributeValueType().getType())) {
            String interestAttValue = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto,
                    attributes.getName(), isHolder,
                    attributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attributes.getAttributeValueType()));
            String interestAttributeValue = "";
            if (StringUtils.isNotBlank(interestAttValue)) {
                interestAttributeValue = JsonMapper.parseJsonArray(interestAttValue, InterestDto.class)
                        .stream()
                        .map(InterestDto::getName)
                        .collect(Collectors.joining(STRING_COMMA_WITH_SPACE));
            }
            ticketHolderData.add(interestAttributeValue);
        } else {
            if (isLeadData && STRING_EMAIL_SPACE.equalsIgnoreCase(attributes.getName())) {
                String emailValue = getTicketAttributeValue(ticketAttributeValueDto, attributes, isHolder);
                if (StringUtils.isBlank(emailValue)) {
                    // Handle for manual lead data
                    emailValue = ticketHolderDetailDto.getHolderEmail();
                }
                ticketHolderData.add(emailValue);
            } else {
                String value = getTicketAttributeValue(ticketAttributeValueDto, attributes, isHolder);
                if (null != eventTicket
                        && (DataType.ADDON.equals(eventTicket.getDataType())
                        || (eventTicket.getGuestOfBuyer() && (StringUtils.isEmpty(eventTicket.getHolderFirstName()) || StringUtils.isEmpty(eventTicket.getHolderLastName()))))) {
                    if (ticketHolderDetailDto != null) {
                        if (attributes.getName().equalsIgnoreCase(EMAIL)) {
                            value = ticketHolderDetailDto.getHolderEmail();
                        } else if (attributes.getName().equalsIgnoreCase(FIRST_NAME)) {
                            value = ticketHolderDetailDto.getHolderFirstName();
                        } else if (attributes.getName().equalsIgnoreCase(LAST_NAME)) {
                            value = ticketHolderDetailDto.getHolderLastName();
                        }
                    }
                    if (StringUtils.isBlank(value) && appendAddOn) {
                        value = TicketHolderHelperPrimitive.getAddOnAttributeValue(ticketAttributeValueDto,
                                attributes.getName(),
                                attributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attributes.getAttributeValueType()) || isNumberGreaterThanZero(attributes.getParentQuestionId()));
                    }
                }
                ticketHolderData.add(null != value ? value : STRING_EMPTY);
            }
        }
    }


    public static String getAttributeValue(EventTickets ticket,
                                           boolean isHolder,
                                           TicketAttributeValueDto1 ticketAttributeValueDto,
                                           TicketHolderRequiredAttributes attribute,
                                           String ticketBuyerUploadFileUrl) {

        String value = getTicketAttributeValue(ticketAttributeValueDto, attribute, isHolder);

        if (attribute.getName().equals(BIRTHDAY)) {
            if (null != value) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
                LocalDate date = LocalDate.parse(value, formatter);
                value = date.toString();
            }
        } else if (TicketHolderUtils.isBillingOrShipping(attribute)) {
            value = StringUtils.isBlank(value) || checkSeparator(value) ? ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR : value;
            String[] values = value.split("\\" + ADD_SEPARATOR, -1);
            value = Arrays.stream(values).filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
        } else if (STRING_CELL_SPACE_PHONE.equals(attribute.getName())) {
            User ticketPurchaser = ticket.getTicketPurchaserId();
            CountryCode tpCountryCode = ticket.getTicketPurchaserId().getCountryCode();
            value = TicketHolderHelperPrimitive.getCellNumber(ticket.getHolderPhoneNumber(), ticket.getHolderCountryCode(), ticketPurchaser.getPhoneNumber(), null != tpCountryCode ? tpCountryCode.name() : CountryCode.US.name(), isHolder, ticketAttributeValueDto);
        } else if (attribute.getAttributeValueType().getType().equalsIgnoreCase(AttributeValueType.IMAGE.getType())) {
            if (StringUtils.isNotBlank(value)) {
                value = ticketBuyerUploadFileUrl + value;
            }
        } else if (attribute.getAttributeValueType().getType().equalsIgnoreCase(AttributeValueType.UPLOAD.getType())) {
            if (StringUtils.isNotBlank(value)) {
                value = ticketBuyerUploadFileUrl + value;
            }
        } else if (AttributeValueType.INTEREST.getType().equalsIgnoreCase(attribute.getAttributeValueType().getType())) {
            if (StringUtils.isNotBlank(value)) {
                value = JsonMapper.parseJsonArray(value, InterestDto.class)
                        .stream()
                        .map(InterestDto::getName)
                        .collect(Collectors.joining(STRING_COMMA_WITH_SPACE));
            }
        } else {
            if (StringUtils.isBlank(value) && DataType.ADDON.equals(ticket.getDataType())) {
                value = TicketHolderHelperPrimitive.getAddOnAttributeValue(ticketAttributeValueDto,
                        attribute.getName(),
                        attribute.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(attribute.getAttributeValueType()));
            }
        }
        return value;
    }

    public static boolean isBillingOrShipping(TicketHolderRequiredAttributes attributes) {
        return AttributeValueType.BILLING_ADDRESS.equals(attributes.getAttributeValueType()) ||
                AttributeValueType.SHIPPING_ADDRESS.equals(attributes.getAttributeValueType());
    }

    public static boolean isContactAttribute(TicketHolderRequiredAttributes attributes) {
        return STRING_CELL_SPACE_PHONE.equals(attributes.getName()) ||
                STRING_EMAIL_SPACE.equalsIgnoreCase(attributes.getName()) ||
                isBillingOrShipping(attributes);
    }


    protected static String getTicketAttributeValue(TicketAttributeValueDto1 ticketAttributeValueDto, TicketHolderRequiredAttributes ticketHolderRequiredAttributes, boolean isHolder) {
        String ticketHolderAttributeValue;
//		if(checkEventTicketsit coAttribute(ticketHolderRequiredAttributes.getName())){
//			ticketHolderAttributeValue = getTicketHolderAttributesValues(eventTicket, ticketHolderRequiredAttributes.getName());
//		} else {
        ticketHolderAttributeValue = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto,
                ticketHolderRequiredAttributes.getName(), isHolder,
                ticketHolderRequiredAttributes.isAttribute(),
                (AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType()) || ticketHolderRequiredAttributes.getParentQuestionId() != null));
//		}
        return ticketHolderAttributeValue;
    }


    protected static String getTicketHolderAttributesValues(EventTickets eventTicket, String attributeKey) {

        if (STRING_EMAIL_SPACE.equalsIgnoreCase(attributeKey)) {
            return eventTicket.getHolderEmail();
        }
        if (STRING_FIRST_SPACE_NAME.equalsIgnoreCase(attributeKey)) {
            return eventTicket.getHolderFirstName();
        }
        if (STRING_LAST_SPACE_NAME.equalsIgnoreCase(attributeKey)) {
            return eventTicket.getHolderLastName();
        }
        return null;
    }


    protected static boolean checkEventTicketsAttribute(String name) {
        return Arrays.asList(STRING_EMAIL_SPACE, STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME).contains(name);
    }

    public static List<String> addTicketHolderRequiredAttributesToHeader(List<TicketHolderRequiredAttributes> attributes) {
        List<String> headerList = new ArrayList<>();
        attributes.forEach(attribute -> headerList.addAll(addTicketHolderRequiredAttributesToHeader(attribute)));
        return headerList;
    }

    public static List<String> addTicketHolderRequiredAttributesToHeader(TicketHolderRequiredAttributes attributes) {
        List<String> headerList = new ArrayList<>();
        if (AttributeValueType.BILLING_ADDRESS.equals(attributes.getAttributeValueType())
                || AttributeValueType.SHIPPING_ADDRESS.equals(attributes.getAttributeValueType())) {
            appendAddressColumns(headerList, attributes);
        } else {
            headerList.add(attributes.getName());
        }
        return headerList;
    }


    public static List<TicketHolderRequiredAttributes> getListOfAttributesHavingEnableForTicketHolderOnly(List<TicketHolderRequiredAttributes> holderRequiredAttributes, DataType dataType) {
        return holderRequiredAttributes.stream()
                .filter(e -> (e.getEnabledForTicketHolder() || (null != dataType && dataType.equals(DataType.ADDON))))
                .collect(Collectors.toList());
    }

    public static List<TicketHolderRequiredAttributes> getListOfAttributesHavingEnableForBuyerOnly(List<TicketHolderRequiredAttributes> holderRequiredAttributes) {
        return holderRequiredAttributes.stream()
                .filter(TicketHolderRequiredAttributes::getEnabledForTicketPurchaser)
                .collect(Collectors.toList());
    }

    public static List<TicketHolderRequiredAttributes> addTicketBuyerOrHolderAttributesToCSVHeader(List<TicketHolderRequiredAttributes> attributesList, DataType dataType, List<String> headerList, String buyerOrHolderPrefix) {
        List<TicketHolderRequiredAttributes> buyerOrHolderAttributes = new ArrayList<>();
        if (PREFIX_BUYER.equals(buyerOrHolderPrefix)) {
            buyerOrHolderAttributes = getListOfAttributesHavingEnableForBuyerOnly(attributesList);
        } else if (PREFIX_HOLDER.equals(buyerOrHolderPrefix)) {
            buyerOrHolderAttributes = getListOfAttributesHavingEnableForTicketHolderOnly(attributesList, dataType);
        }
        headerList.addAll(addTicketHolderRequiredAttributesToHeader(buyerOrHolderAttributes)
                .stream()
                .map(buyerOrHolderPrefix::concat)
                .collect(Collectors.toList()));
        return buyerOrHolderAttributes;
    }


    private static void appendAddressColumns(List<String> headerList, TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
        int indexOf = ticketHolderRequiredAttributes.getName().indexOf(" ");
        String addStr = ticketHolderRequiredAttributes.getName();
        if (indexOf > 0) {
            addStr = addStr.substring(0, indexOf);
        }
        headerList.add(ticketHolderRequiredAttributes.getName() + " 1");
        headerList.add(ticketHolderRequiredAttributes.getName() + " 2");
        headerList.add(addStr + " City");
        headerList.add(addStr + " State");
        headerList.add(addStr + " Zip Code");
        headerList.add(addStr + " Country");
    }

}
