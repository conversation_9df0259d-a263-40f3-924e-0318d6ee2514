package com.accelevents.domain;

import com.accelevents.domain.enums.*;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import io.leangen.graphql.annotations.GraphQLQuery;
import org.hibernate.annotations.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "event_ticket_type")
@Where(clause="rec_status<>'CANCEL' or rec_status is NULL and is_disable_ticket = false ")
@DynamicUpdate
public class TicketingType implements Serializable, Cloneable {

	private static final long serialVersionUID = 3724772280167802713L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @GraphQLQuery(name = "id")
    private long id;

    @ManyToOne(fetch = FetchType.EAGER, targetEntity = Ticketing.class)
    @JoinColumn(name = "ticketing_ID", nullable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private Ticketing ticketing;

    @Column(name = "event_id")
    private Long eventId;

    @ManyToOne(fetch = FetchType.EAGER, targetEntity = Event.class)
    @JoinColumn(name = "event_id", nullable = false, insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private Event event;

    @Type(type = "org.jadira.usertype.dateandtime.legacyjdk.PersistentDate")
    @Column(name = "start_date")
    @GraphQLQuery(name = "startDate")
    private Date startDate;

    @Type(type = "org.jadira.usertype.dateandtime.legacyjdk.PersistentDate")
    @Column(name = "end_date")
    @GraphQLQuery
    private Date endDate;

    @Column(name = "number_of_tickets")
    @GraphQLQuery
    private long numberOfTickets;

    @Column(name = "price")
    @GraphQLQuery
    private double price;

    @Column(name = "pass_fee_to_buyer")
    @GraphQLQuery
    private Boolean passfeetobuyer = true;

    @Column(name = "pass_fee_vat_to_buyer")
    @GraphQLQuery
    private Boolean passFeeVatToBuyer = true;

    @Column(name = "max_tickets_per_buyer", length = 11)
    @GraphQLQuery
    private long maxTicketsPerBuyer;

    @Column(name = "min_tickets_per_buyer", length = 11)
    @GraphQLQuery
    private long minTicketsPerBuyer = 0;

    @Column(name = "allow_discount_coupon")
    @GraphQLQuery
    private Boolean allow_discount_coupon;

    @Column(name = "is_hidden")
    @GraphQLQuery
    private Boolean isHidden = false;

    @Column(name = "ticket_type_name")
    @GraphQLQuery
    private String ticketTypeName;

    @Column(name = "ticket_type_description")
    @Type(type = "text")
    @GraphQLQuery
    private String ticketTypeDescription;

    @Column(name = "enable_ticket_description")
    @GraphQLQuery
    private Boolean enableTicketDescription;

    @Column(name = "position")
    @GraphQLQuery
    private double position;

    @Column(name = "ticket_bundle_type")
    @Enumerated(EnumType.STRING)
    private TicketBundleType bundleType;

    @Column(name = "number_of_ticket_per_table")
    @GraphQLQuery
    private int numberOfTicketPerTable = 0;

    @Column(name = "ae_fee_flat")
    @GraphQLQuery
    private double aeFeeFlat;

    @Column(name = "ae_fee_percentage")
    @GraphQLQuery
    private double aeFeePercentage;

    @Column(name = "wl_a_fee_flat")
    @GraphQLQuery
    private double wlAFeeFlat;

    @Column(name = "wl_a_fee_percentage")
    @GraphQLQuery
    private double wlAFeePercentage;

    @Column(name = "wl_b_fee_flat")
    @GraphQLQuery
    private double wlBFeeFlat;

    @Column(name = "wl_b_fee_percentage")
    @GraphQLQuery
    private double wlBFeePercentage;

    @Column(name = "ticket_type")
    @Enumerated(EnumType.STRING)
    private TicketType ticketType;

    @Column(name = "recurring_event_id")
    @GraphQLQuery
    private Long recurringEventId;

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = RecurringEvents.class)
    @JoinColumn(name = "recurring_event_id", insertable = false, updatable = false)
    private RecurringEvents recurringEvent;

    @Column(name = "created_from")
    @GraphQLQuery
    private Long createdFrom;

    @Column(name = "recurring_event_sales_end_time")
    @GraphQLQuery
    private Integer recurringEventSalesEndTime;

    @Column(name = "recurring_event_sales_end_before_start_or_end")
    @Enumerated(EnumType.STRING)
    private RecurringEventSalesEndStatus recurringEventSalesEndStatus;

    @Column(name = "categories")
    private String categories;

    @Column(name = "allow_pdf_download")
    private Boolean allowPDFDownload = false;

	@Column(name = "max_session_register_user")
	private Integer maxSessionRegisterUser;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus status;

    @Column(name = "data_type")
    @Enumerated(EnumType.STRING)
    private DataType dataType;

    @Column(name = "category_id")
    private Long categoryId;

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = SeatingCategories.class)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private SeatingCategories category;

    @Column(name = "ticket_type_format")
    @Enumerated(EnumType.STRING)
    private TicketTypeFormat ticketTypeFormat;

    public RecordStatus getStatus() {
        return status;
    }

    @Column(name = "is_disable_ticket")
    private Boolean isDisableTicket = false;

    @Column(name = "is_lounges_restricted")
    private Boolean isLoungesRestricted;

    @Column(name = "is_expo_restricted")
    private Boolean isExpoRestricted;

    @Column(name = "is_chat_restricted")
    private Boolean isChatRestricted;

    @Column(name = "allow_invoice_pdf_download")
    private Boolean allowInvoicePDFDownload = false;

    @Column(name = "pay_later")
    private boolean payLater;

    @Column(name = "custom_invoice_text")
    @Type(type = "text")
    private String customInvoiceText;

    @Column(name = "allow_invoice_pdf_for_buyers")
    private Boolean allowInvoicePDFForBuyers = false;

    @Column(name = "allow_invoice_pdf_for_holders")
    private Boolean allowInvoicePDFForHolders = false;

    @Column(name = "registration_approval_required")
    private boolean isRegistrationApprovalRequired;

    @Column(name = "allow_assigned_seating")
    private boolean allowAssignedSeating = false;

    @Column(name = "allow_meeting_creation")
    private boolean allowMeetingCreation = true;

    @Column(name="custom_template_id")
    private Long customTemplateId;

    @Column(name="custom_confirmation_email_id")
    private Long customConfirmationEmailId;
    
    @Column(name = "custom_ticket_pdf_design_id")
    private Long customTicketPdfDesignId;

    @Column(name = "is_all_ticket_types_selected_for_add_on")
    private boolean isAllTicketTypesSelectedForAddOn = false;

    @Column(name = "list_of_ticket_types_for_add_on",columnDefinition = "TEXT")
    private String listOfTicketTypesForAddOn;

    @Column(name = "require_deposit_amount")
    private boolean requireDepositAmount = false;

    @Column(name = "deposit_type")
    @Enumerated(EnumType.STRING)
    private DepositType depositType;

    @Column(name = "deposit_amount")
    private double depositAmount = 0.0;
    @Column(name="is_non_transferable")
    private boolean nonTransferable;

    @Column(name = "enable_ticket_level_vat_tax")
    private boolean enableTicketLevelVatTax = false;

    @Column(name = "vat_tax_percentage")
    private double vatTaxPercentage = 0.0d;

    @Column(name = "tracks_sessions_limit_allowed")
    private boolean tracksSessionsLimitAllowed = false;

    public void setStatus(RecordStatus status) {
        this.status = status;
    }

    @Transient
    private double priceWithFee;
    @Transient
    private double salePrice;
    @Transient
    private String startDateStr;
    @Transient
    private String endDateStr;
    @Transient
    private long remaningtickets;
    @Transient
    private double discount;
    @Transient
    private int numberofticket;
    @Transient
    private double aeFee;
    @Transient
    private double wlTotalFee;
    @Transient
    private Boolean changeIsTable;
    @Transient
    private Long BadgeId;

    public Long getCreatedFrom() {
        return createdFrom;
    }

    public void setCreatedFrom(Long createdFrom) {
        this.createdFrom = createdFrom;
    }

    public RecurringEvents getRecurringEvent() {
        return recurringEvent;
    }

    public void setRecurringEvent(RecurringEvents recurringEvent) {
        this.recurringEvent = recurringEvent;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public Ticketing getTicketing() {
        return ticketing;
    }

    public void setTicketing(Ticketing ticketing) {
        this.ticketing = ticketing;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public long getNumberOfTickets() {
        return numberOfTickets;
    }

    public void setNumberOfTickets(long numberOfTickets) {
        this.numberOfTickets = numberOfTickets;
    }

    public long getMaxTicketsPerBuyer() {
        return maxTicketsPerBuyer;
    }

    public void setMaxTicketsPerBuyer(long maxTicketsPerBuyer) {
        this.maxTicketsPerBuyer = maxTicketsPerBuyer;
    }

    public Boolean getAllow_discount_coupon() {
        return allow_discount_coupon;
    }

    public void setAllow_discount_coupon(Boolean allow_discount_coupon) {
        this.allow_discount_coupon = allow_discount_coupon;
    }

    public Boolean isHidden() {
        return isHidden;
    }

    public void setHidden(Boolean hidden) {
        this.isHidden = hidden;
    }

    public String getTicketTypeName() {
        return ticketTypeName;
    }

    public void setTicketTypeName(String ticketTypeName) {
        this.ticketTypeName = ticketTypeName;
    }

    public String getTicketTypeDescription() {
        return ticketTypeDescription;
    }

    public void setTicketTypeDescription(String ticketTypeDescription) {
        this.ticketTypeDescription = ticketTypeDescription;
    }

    public Boolean getEnableTicketDescription() {
        return enableTicketDescription != null ? enableTicketDescription : false;
    }

    public void setEnableTicketDescription(Boolean enableTicketDescription) {
        this.enableTicketDescription = enableTicketDescription;
    }

    public Boolean isPassfeetobuyer() {
        return passfeetobuyer;
    }

    public void setPassfeetobuyer(Boolean passfeetobuyer) {
        this.passfeetobuyer = passfeetobuyer;
    }

    public double getPriceWithFee() {
        return priceWithFee;
    }

    public void setPriceWithFee(double priceWithFee) {
        this.priceWithFee = priceWithFee;
    }

    public String getStartDateStr() {
        return startDateStr;
    }

    public void setStartDateStr(String startDateStr) {
        this.startDateStr = startDateStr;
    }

    public String getEndDateStr() {
        return endDateStr;
    }

    public void setEndDateStr(String endDateStr) {
        this.endDateStr = endDateStr;
    }

    public long getRemaningtickets() {
        return remaningtickets;
    }

    public void setRemaningtickets(long remaningtickets) {
        this.remaningtickets = remaningtickets;
    }

    public double getDiscount() {
        return discount;
    }

    public void setDiscount(double discount) {
        this.discount = discount;
    }

    public double getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(double salePrice) {
        this.salePrice = salePrice;
    }

    public int getNumberofticket() {
        return numberofticket;
    }

    public void setNumberofticket(int numberofticket) {
        this.numberofticket = numberofticket;
    }

    public long getMinTicketsPerBuyer() {
        return minTicketsPerBuyer;
    }

    public void setMinTicketsPerBuyer(long minTicketsPerBuyer) {
        this.minTicketsPerBuyer = minTicketsPerBuyer;
    }

    public double getAeFee() {
        return aeFee;
    }

    public void setAeFee(double aeFee) {
        this.aeFee = aeFee;
    }

    public double getPosition() {
        return position;
    }

    public void setPosition(double position) {
        this.position = position;
    }

    public TicketBundleType getBundleType() {
        return bundleType;
    }

    public void setBundleType(TicketBundleType bundleType) {
        this.bundleType = bundleType;
    }

    public int getNumberOfTicketPerTable() {
        return numberOfTicketPerTable;
    }

    public void setNumberOfTicketPerTable(int numberOfTicketPerTable) {
        this.numberOfTicketPerTable = numberOfTicketPerTable;
    }

    public Boolean isChangeIsTable() {
        return changeIsTable;
    }

    public void setChangeIsTable(Boolean changeIsTable) {
        this.changeIsTable = changeIsTable;
    }

    public double getAeFeePercentage() {
        return aeFeePercentage;
    }

    public void setAeFeePercentage(double aeFeePercentage) {
        this.aeFeePercentage = aeFeePercentage;
    }

    public double getWlTotalFeeFlat() {
        return wlAFeeFlat + wlBFeeFlat;
    }

    public double getWlTotalFeePercentage() {
        return wlAFeePercentage + wlBFeePercentage;
    }

    public double getAeFeeFlat() {
        return aeFeeFlat;
    }

    public void setAeFeeFlat(double aeFeeFlat) {
        this.aeFeeFlat = aeFeeFlat;
    }

    public double getWlTotalFee() {
        return wlTotalFee;
    }

    public void setWlTotalFee(double wlTotalFee) {
        this.wlTotalFee = wlTotalFee;
    }

    public TicketType getTicketType() {
        return ticketType;
    }

    public void setTicketType(TicketType ticketType) {
        this.ticketType = ticketType;
    }

    public double getWlAFeeFlat() {
        return wlAFeeFlat;
    }

    public void setWlAFeeFlat(double wlAFeeFlat) {
        this.wlAFeeFlat = wlAFeeFlat;
    }

    public double getWlAFeePercentage() {
        return wlAFeePercentage;
    }

    public void setWlAFeePercentage(double wlAFeePercentage) {
        this.wlAFeePercentage = wlAFeePercentage;
    }

    public double getWlBFeeFlat() {
        return wlBFeeFlat;
    }

    public void setWlBFeeFlat(double wlBFeeFlat) {
        this.wlBFeeFlat = wlBFeeFlat;
    }

    public double getWlBFeePercentage() {
        return wlBFeePercentage;
    }

    public void setWlBFeePercentage(double wlBFeePercentage) {
        this.wlBFeePercentage = wlBFeePercentage;
    }

    public TicketTypeFormat getTicketTypeFormat() {
        return ticketTypeFormat;
    }

    public void setTicketTypeFormat(TicketTypeFormat ticketTypeFormat) {
        this.ticketTypeFormat = ticketTypeFormat;
    }

    public Long getBadgeId() {
        return BadgeId;
    }

    public void setBadgeId(Long badgeId) {
        BadgeId = badgeId;
    }

    public Boolean getAllowInvoicePDFDownload() {
        return allowInvoicePDFDownload;
    }

    public void setAllowInvoicePDFDownload(Boolean allowInvoicePDFDownload) {
        this.allowInvoicePDFDownload = allowInvoicePDFDownload;
    }

    public boolean isEnableTicketLevelVatTax() {
        return enableTicketLevelVatTax;
    }

    public void setEnableTicketLevelVatTax(boolean enableTicketLevelVatTax) {
        this.enableTicketLevelVatTax = enableTicketLevelVatTax;
    }

    @Override
    public Object clone() {   //NOSONAR
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null; //NOSONAR
        }
    }

    @Override
    public String toString() {
        return "TicketingType{" +
                "id=" + id +
                ", ticketing=" + ticketing +
                ", eventId=" + eventId +
                ", event=" + event +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", numberOfTickets=" + numberOfTickets +
                ", price=" + price +
                ", passfeetobuyer=" + passfeetobuyer +
                ", passFeeVatToBuyer=" + passFeeVatToBuyer +
                ", maxTicketsPerBuyer=" + maxTicketsPerBuyer +
                ", minTicketsPerBuyer=" + minTicketsPerBuyer +
                ", allow_discount_coupon=" + allow_discount_coupon +
                ", isHidden=" + isHidden +
                ", ticketTypeName='" + ticketTypeName + '\'' +
                ", ticketTypeDescription='" + ticketTypeDescription + '\'' +
                ", enableTicketDescription=" + enableTicketDescription +
                ", position=" + position +
                ", bundleType=" + bundleType +
                ", numberOfTicketPerTable=" + numberOfTicketPerTable +
                ", aeFeeFlat=" + aeFeeFlat +
                ", aeFeePercentage=" + aeFeePercentage +
                ", wlAFeeFlat=" + wlAFeeFlat +
                ", wlAFeePercentage=" + wlAFeePercentage +
                ", wlBFeeFlat=" + wlBFeeFlat +
                ", wlBFeePercentage=" + wlBFeePercentage +
                ", ticketType=" + ticketType +
                ", recurringEventId=" + recurringEventId +
                ", recurringEvent=" + recurringEvent +
                ", createdFrom=" + createdFrom +
                ", recurringEventSalesEndTime=" + recurringEventSalesEndTime +
                ", recurringEventSalesEndStatus=" + recurringEventSalesEndStatus +
                ", categories='" + categories + '\'' +
                ", allowPDFDownload=" + allowPDFDownload +
                ", maxSessionRegisterUser=" + maxSessionRegisterUser +
                ", status=" + status +
                ", dataType=" + dataType +
                ", categoryId=" + categoryId +
                ", category=" + category +
                ", ticketTypeFormat=" + ticketTypeFormat +
                ", isDisableTicket=" + isDisableTicket +
                ", isLoungesRestricted=" + isLoungesRestricted +
                ", isExpoRestricted=" + isExpoRestricted +
                ", isChatRestricted=" + isChatRestricted +
                ", allowInvoicePDFDownload=" + allowInvoicePDFDownload +
                ", payLater=" + payLater +
                ", customInvoiceText='" + customInvoiceText + '\'' +
                ", allowInvoicePDFForBuyers=" + allowInvoicePDFForBuyers +
                ", allowInvoicePDFForHolders=" + allowInvoicePDFForHolders +
                ", isRegistrationApprovalRequired=" + isRegistrationApprovalRequired +
                ", allowAssignedSeating=" + allowAssignedSeating +
                ", allowMeetingCreation=" + allowMeetingCreation +
                ", customTemplateId=" + customTemplateId +
                ", customConfirmationEmailId=" + customConfirmationEmailId +
                ", isAllTicketTypesSelectedForAddOn=" + isAllTicketTypesSelectedForAddOn +
                ", listOfTicketTypesForAddOn='" + listOfTicketTypesForAddOn + '\'' +
                ", requireDepositAmount=" + requireDepositAmount +
                ", depositType=" + depositType +
                ", depositAmount=" + depositAmount +
                ", nonTransferable=" + nonTransferable +
                ", priceWithFee=" + priceWithFee +
                ", salePrice=" + salePrice +
                ", startDateStr='" + startDateStr + '\'' +
                ", endDateStr='" + endDateStr + '\'' +
                ", remaningtickets=" + remaningtickets +
                ", discount=" + discount +
                ", numberofticket=" + numberofticket +
                ", aeFee=" + aeFee +
                ", wlTotalFee=" + wlTotalFee +
                ", changeIsTable=" + changeIsTable +
                ", BadgeId=" + BadgeId +
                '}';
    }

    public Long getRecurringEventId() {
        return recurringEventId;
    }

    public void setRecurringEventId(Long recurringEventId) {
        this.recurringEventId = recurringEventId;
    }

    public Integer getRecurringEventSalesEndTime() {
        return recurringEventSalesEndTime;
    }

    public void setRecurringEventSalesEndTime(Integer recurringEventSalesEndTime) {
        this.recurringEventSalesEndTime = recurringEventSalesEndTime;
    }

    public RecurringEventSalesEndStatus getRecurringEventSalesEndStatus() {
        return recurringEventSalesEndStatus;
    }

    public void setRecurringEventSalesEndStatus(RecurringEventSalesEndStatus recurringEventSalesEndStatus) {
        this.recurringEventSalesEndStatus = recurringEventSalesEndStatus;
    }

    public String getCategories() {
        return categories;
    }

//    public void setCategories(String categories) {
//        this.categories = categories;
//    }

    public DataType getDataType() {
        return dataType;
    }

    public void setDataType(DataType dataType) {
        this.dataType = dataType;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public SeatingCategories getCategory() {
        return category;
    }

    public void setCategory(SeatingCategories category) {
        this.category = category;
    }

    public enum RecurringEventSalesEndStatus {
        START, END
    }
	/*public Category createCategory() {
		return new Category(id, ticketTypeName, StringUtils.isNotBlank(categoryColor) ? categoryColor : "#D3D3D3");
	}*/

    public Boolean getAllowPDFDownload() {
        return allowPDFDownload;
    }

	public Integer getMaxSessionRegisterUser(){
		return maxSessionRegisterUser;
	}

	public void setMaxSessionRegisterUser(Integer maxSessionRegisterUser){
		this.maxSessionRegisterUser=maxSessionRegisterUser;
	}

	public void setAllowPDFDownload(Boolean allowPDFDownload) {
        this.allowPDFDownload = allowPDFDownload;
    }

    public Boolean getDisableTicket() {
        return isDisableTicket;
    }

    public void setDisableTicket(Boolean disableTicket) {
        isDisableTicket = disableTicket;
    }

    public Boolean getLoungesRestricted() {
        return isLoungesRestricted;
    }

    public void setLoungesRestricted(Boolean loungesRestricted) {
        isLoungesRestricted = loungesRestricted;
    }

    public Boolean getExpoRestricted() {
        return isExpoRestricted;
    }

    public void setExpoRestricted(Boolean expoRestricted) {
        isExpoRestricted = expoRestricted;
    }

    public Boolean getChatRestricted() {
        return isChatRestricted;
    }

    public void setChatRestricted(Boolean chatRestricted) {
        isChatRestricted = chatRestricted;
    }

    public boolean isPayLater() {
        return payLater;
    }

    public void setPayLater(boolean payLater) {
        this.payLater = payLater;
    }

    public Boolean isPassFeeVatToBuyer() {
        return passFeeVatToBuyer;
    }

    public void setPassFeeVatToBuyer(Boolean passFeeVatToBuyer) {
        this.passFeeVatToBuyer = passFeeVatToBuyer;
    }

    public String getCustomInvoiceText() { return customInvoiceText; }

    public void setCustomInvoiceText(String customInvoiceText) { this.customInvoiceText = customInvoiceText; }

    public Boolean getAllowInvoicePDFForBuyers() { return allowInvoicePDFForBuyers; }

    public void setAllowInvoicePDFForBuyers(Boolean allowInvoicePDFForBuyers) { this.allowInvoicePDFForBuyers = allowInvoicePDFForBuyers; }

    public Boolean getAllowInvoicePDFForHolders() { return allowInvoicePDFForHolders; }

    public void setAllowInvoicePDFForHolders(Boolean allowInvoicePDFForHolders) { this.allowInvoicePDFForHolders = allowInvoicePDFForHolders; }

    public boolean isRegistrationApprovalRequired() { return isRegistrationApprovalRequired; }

    public void setRegistrationApprovalRequired(boolean registrationApprovalRequired) { isRegistrationApprovalRequired = registrationApprovalRequired; }

    public boolean isAllowAssignedSeating() {
        return allowAssignedSeating;
    }

    public void setAllowAssignedSeating(boolean allowAssignedSeating) {
        this.allowAssignedSeating = allowAssignedSeating;
    }
    public boolean isAllowMeetingCreation() {
        return allowMeetingCreation;
    }

    public void setAllowMeetingCreation(boolean allowMeetingCreation) {
        this.allowMeetingCreation = allowMeetingCreation;
    }
    public Long getCustomTemplateId() { return customTemplateId; }

    public void setCustomTemplateId(Long customTemplateId) { this.customTemplateId = customTemplateId; }

    public Long getCustomConfirmationEmailId() { return customConfirmationEmailId; }

    public void setCustomConfirmationEmailId(Long customConfirmationEmailId) { this.customConfirmationEmailId = customConfirmationEmailId; }

    public boolean isAllTicketTypesSelectedForAddOn() {
        return isAllTicketTypesSelectedForAddOn;
    }

    public void setAllTicketTypesSelectedForAddOn(boolean allTicketTypesSelectedForAddOn) {
        isAllTicketTypesSelectedForAddOn = allTicketTypesSelectedForAddOn;
    }

    public String getListOfTicketTypesForAddOn() {
        return listOfTicketTypesForAddOn;
    }

    public void setListOfTicketTypesForAddOn(String listOfTicketTypesForAddOn) {
        this.listOfTicketTypesForAddOn = listOfTicketTypesForAddOn;
    }

    public boolean isRequireDepositAmount() {
        return requireDepositAmount;
    }

    public void setRequireDepositAmount(boolean requireDepositAmount) {
        this.requireDepositAmount = requireDepositAmount;
    }

    public DepositType getDepositType() {
        return depositType;
    }

    public void setDepositType(DepositType depositType) {
        this.depositType = depositType;
    }

    public double getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(double depositAmount) {
        this.depositAmount = depositAmount;
    }

    public long getEventId() {
        return eventId;
    }

    public void setEventId(long eventId) {
        this.eventId = eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public boolean isNonTransferable() {
        return nonTransferable;
    }

    public void setNonTransferable(boolean nonTransferable) {
        this.nonTransferable = nonTransferable;
    }

    public double getVatTaxPercentage() {
        return vatTaxPercentage;
    }

    public void setVatTaxPercentage(double vatTaxPercentage) {
        this.vatTaxPercentage = vatTaxPercentage;
    }

    public boolean isTracksSessionsLimitAllowed() {
        return tracksSessionsLimitAllowed;
    }

    public void setTracksSessionsLimitAllowed(boolean tracksSessionsLimitAllowed) {
        this.tracksSessionsLimitAllowed = tracksSessionsLimitAllowed;
    }

	public Long getCustomTicketPdfDesignId() {
		return customTicketPdfDesignId;
	}

	public void setCustomTicketPdfDesignId(Long customTicketPdfDesignId) {
		this.customTicketPdfDesignId = customTicketPdfDesignId;
	}
}
