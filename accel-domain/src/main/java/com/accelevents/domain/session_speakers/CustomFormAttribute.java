package com.accelevents.domain.session_speakers;

import com.accelevents.domain.BaseAuditInfo;
import com.accelevents.domain.Event;
import com.accelevents.domain.RecurringEvents;
import com.accelevents.domain.enums.AttributeType;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.RecordStatus;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "custom_form_attribute")
@Where(clause="rec_status<>'DElETE'")
public class CustomFormAttribute extends BaseAuditInfo implements Serializable, Cloneable {


    /**
     *
     */
    private static final long serialVersionUID = 6915814721042018536L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "event_id", nullable = false)
    private long eventId;

    @ManyToOne(fetch = FetchType.LAZY,targetEntity = Event.class)
    @JoinColumn(name = "event_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private Event event;

    @Column(name = "name")
    private String name;

    @Column(name = "value_type")
    @Enumerated(EnumType.STRING)
    private AttributeValueType attributeValueType;

    @Column(name = "enabled")
    private boolean enabled;

    @Column(name = "is_required")
    private boolean required = false;

    @Column(name = "is_attribute")
    private boolean isAttribute;

    @Type(type = "text")
    @Column(name = "default_value", columnDefinition = "LONGTEXT")
    private String defaultValue;

    @Column(name = "recurring_event_id")
    private Long recurringEventId;

    @ManyToOne(fetch = FetchType.LAZY,targetEntity = RecurringEvents.class)
    @JoinColumn(name ="recurring_event_id" , insertable = false, updatable = false)
    private RecurringEvents recurringEvents;

    @Column(name = "created_from")
    private Long createdFrom;

    @Column(name = "attribute_order")
    private int order;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus status = RecordStatus.CREATE;

    @Column(name="selected_answer")
    private Long selectedAnswer;

    @Column(name="parent_attribute_id")
    private Long parentQuestionId;

    @Column(name="is_default")
    private boolean isDefaultAttribute = false;

    @Column(name="attribute_type", nullable = false, columnDefinition = "ENUM('SPEAKER','SESSION','CONTACT')")
    @Enumerated(EnumType.STRING)
    private AttributeType attributeType;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AttributeValueType getAttributeValueType() {
        return attributeValueType;
    }

    public void setAttributeValueType(AttributeValueType attributeValueType) {
        this.attributeValueType = attributeValueType;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public boolean isAttribute() {
        return isAttribute;
    }

    public void setAttribute(boolean attribute) {
        isAttribute = attribute;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Long getRecurringEventId() {
        return recurringEventId;
    }

    public void setRecurringEventId(Long recurringEventId) {
        this.recurringEventId = recurringEventId;
    }

    public RecurringEvents getRecurringEvents() {
        return recurringEvents;
    }

    public void setRecurringEvents(RecurringEvents recurringEvents) {
        this.recurringEvents = recurringEvents;
    }

    public Long getCreatedFrom() {
        return createdFrom;
    }

    public void setCreatedFrom(Long createdFrom) {
        this.createdFrom = createdFrom;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public RecordStatus getStatus() {
        return status;
    }

    public void setStatus(RecordStatus status) {
        this.status = status;
    }

    public Long getSelectedAnswer() {
        return selectedAnswer;
    }

    public void setSelectedAnswer(Long selectedAnswer) {
        this.selectedAnswer = selectedAnswer;
    }

    public Long getParentQuestionId() {
        return parentQuestionId;
    }

    public void setParentQuestionId(Long parentQuestionId) {
        this.parentQuestionId = parentQuestionId;
    }

    public boolean isDefaultAttribute() {
        return isDefaultAttribute;
    }

    public void setDefaultAttribute(boolean defaultAttribute) {
        isDefaultAttribute = defaultAttribute;
    }

    public long getEventId() {
        return eventId;
    }

    public void setEventId(long eventId) {
        this.eventId = eventId;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public AttributeType getAttributeType() {return attributeType; }

    public void setAttributeType(AttributeType attributeType) { this.attributeType = attributeType;}

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof CustomFormAttribute)) {
            return false;
        }
        CustomFormAttribute rhs = (CustomFormAttribute) obj;

        return StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(rhs.getName()) && rhs.getName().equals(name);
    }

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String toString() {
        return "SpeakerAttribute{" +
                "id=" + id +
                ", eventId=" + eventId +
                ", name='" + name + '\'' +
                ", attributeValueType=" + attributeValueType +
                ", enabled=" + enabled +
                ", required=" + required +
                ", isAttribute=" + isAttribute +
                ", defaultValue='" + defaultValue + '\'' +
                ", recurringEventId=" + recurringEventId +
                ", recurringEvents=" + recurringEvents +
                ", createdFrom=" + createdFrom +
                ", order=" + order +
                ", status=" + status +
                ", selectedAnswer=" + selectedAnswer +
                ", parentQuestionId=" + parentQuestionId +
                ", isDefaultAttribute=" + isDefaultAttribute +
                '}';
    }
}