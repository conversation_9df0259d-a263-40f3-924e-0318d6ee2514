package com.accelevents.domain.session_speakers;


import com.accelevents.domain.Event;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.utils.Constants;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

import static com.accelevents.domain.enums.RecordStatus.CREATE;

@Entity
@Table(name = "speakers")
@Where(clause="speaker_status<>'DELETE' or speaker_status <> 'DELETED'")
public class Speaker implements Cloneable, Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2491660178933864602L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private long id;

	@ManyToOne(fetch = FetchType.LAZY,targetEntity = Event.class)
	@JoinColumn(name = "event_id", insertable = false, updatable = false)
	private Event event;

	@Column(name = "event_id", nullable = false)
	private Long eventId;

	@Column(name = "user_id")
	private Long userId;

	@Column(name = "first_name", columnDefinition = "text")
	private String firstName;

	@Column(name = "last_name", columnDefinition = "text")
	private String lastName;

	@Column(name = "email")
	private String email;

	@Column(name = "image_url")
	private String imageUrl;

	@Column(name = "title", columnDefinition = "text")
	private String title;

	@Column(name = "company", columnDefinition = "MEDIUMTEXT")
	private String company;

	@Column(name = "pronouns", columnDefinition = "text")
    private String pronouns;

	@Column(name = "bio", columnDefinition = "LONGTEXT")
	private String bio;

	@Column(name = "linked_in", columnDefinition = "text")
	private String linkedIn;

	@Column(name = "twitter", columnDefinition = "text")
	private String twitter;

	@Column(name = "instagram", columnDefinition = "text")
	private String instagram;

    @Column(name = "position")
    private Double position = 1000d;
	@Transient
	private Boolean moderator;

    @Column(name = "allow_override_details")
    private Boolean isAllowOverrideDetails;

    @Column(name = "allow_attendee_access")
    private Boolean isAllowAttendeeAccess;

    @Column(name = "speaker_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus speakerStatus = CREATE;

    @Column(name="speaker_onboarding_status")
    @Enumerated(EnumType.STRING)
    private Constants.SpeakerOnBoardingStatus speakerOnBoardingStatus;

    @Column(name = "highlighted")
    private Boolean highlighted = true;

    @Column(name = "speaker_attribute")
    private Long speakerAttributeId;

    @OneToOne(fetch = FetchType.LAZY,targetEntity = CustomFormAttributeData.class)
    @JoinColumn(name = "speaker_attribute", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private CustomFormAttributeData speakerAttribute;

    @OneToMany(mappedBy = "speaker", fetch = FetchType.LAZY)
    private Set<SessionSpeaker> sessionSpeakers;

    public Set<SessionSpeaker> getSessionSpeakers() {
        return sessionSpeakers;
    }

    public void setSessionSpeakers(Set<SessionSpeaker> sessionSpeakers) {
        this.sessionSpeakers = sessionSpeakers;
    }

    public Boolean getModerator() {
		return moderator;
	}

	public void setModerator(Boolean moderator) {
		this.moderator = moderator;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Event getEvent() {
		return event;
	}

	public void setEvent(Event event) {
		this.event = event;
	}

	public Long getEventId() {
		return eventId;
	}

	public void setEventId(Long eventId) {
		this.eventId = eventId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

    public String getPronouns() {
        return pronouns;
    }

    public void setPronouns(String pronouns) {
        this.pronouns = pronouns;
    }

    public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getBio() {
		return bio;
	}

	public void setBio(String bio) {
		this.bio = bio;
	}

	public String getLinkedIn() {
		return linkedIn;
	}

	public void setLinkedIn(String linkedIn) {
		this.linkedIn = linkedIn;
	}

	public String getTwitter() {
		return twitter;
	}

	public void setTwitter(String twitter) {
		this.twitter = twitter;
	}

	public String getInstagram() {
		return instagram;
	}

	public void setInstagram(String instagram) {
		this.instagram = instagram;
	}

    public Double getPosition() {
        return position;
    }

    public void setPosition(Double position) {
        this.position = position;
    }

    public Boolean getAllowOverrideDetails() { return isAllowOverrideDetails; }

    public void setAllowOverrideDetails(Boolean allowOverrideDetails) { isAllowOverrideDetails = allowOverrideDetails; }

    public Boolean getAllowAttendeeAccess() { return isAllowAttendeeAccess; }

    public void setAllowAttendeeAccess(Boolean allowAttendeeAccess) { isAllowAttendeeAccess = allowAttendeeAccess; }

    public RecordStatus getSpeakerStatus() {
        return speakerStatus;
    }

    public void setSpeakerStatus(RecordStatus speakerStatus) {
        this.speakerStatus = speakerStatus;
    }

    public Constants.SpeakerOnBoardingStatus getSpeakerOnBoardingStatus() {
        return speakerOnBoardingStatus;
    }

    public void setSpeakerOnBoardingStatus(Constants.SpeakerOnBoardingStatus speakerOnBoardingStatus) {
        this.speakerOnBoardingStatus = speakerOnBoardingStatus;
    }

    public Boolean getHighlighted() {
        return highlighted;
    }

    public void setHighlighted(Boolean highlighted) {
        this.highlighted = highlighted;
    }

    public Long getSpeakerAttributeId() {
        return speakerAttributeId;
    }

    public void setSpeakerAttributeId(Long speakerAttributeId) {
        this.speakerAttributeId = speakerAttributeId;
    }

    public CustomFormAttributeData getSpeakerAttribute() {
        return speakerAttribute;
    }

    public void setSpeakerAttribute(CustomFormAttributeData speakerAttribute) {
        this.speakerAttribute = speakerAttribute;
    }

    @Override
	public Object clone() {
		try {
			return super.clone();
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
			return null;
		}
	}
}
