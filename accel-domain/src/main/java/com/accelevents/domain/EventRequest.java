package com.accelevents.domain;

import com.accelevents.domain.enums.EventRequestFormSubmissionStatus;
import com.accelevents.domain.enums.RecordStatus;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "event_request")
@Where(clause = "rec_status<>'DELETE'")
public class EventRequest extends BaseAuditInfo implements Serializable {
	
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "white_label_id", nullable = false)
    private Long whiteLabelId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "white_label_id", insertable = false, updatable = false)
    private WhiteLabel whiteLabel;

    @Column(name = "event_request_form_id", nullable = false)
    private Long eventRequestFormId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "event_request_form_id", insertable = false, updatable = false)
    private EventRequestForm eventRequestForm;

    @Column(name = "ticket_holder_attributes_id")
    private Long ticketHolderAttributesId;

    @OneToOne
    @JoinColumn(name = "ticket_holder_attributes_id", insertable = false, updatable = false)
    private TicketHolderAttributes ticketHolderAttributes;

    @Column(name = "requested_for_user_id")
    private Long requestedForUserId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requested_for_user_id", insertable = false, updatable = false)
    private User requestedForUser;
    
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "recent_approved_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date recentApprovedAt;

	@Column(name = "status", nullable = false, length = 45)
	@Enumerated(EnumType.STRING)
	private EventRequestFormSubmissionStatus status = EventRequestFormSubmissionStatus.PENDING;

    @Column(name = "created_by")
    private Long createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", insertable = false, updatable = false)
    private User createdByUser;

    @Column(name = "updated_by")
    private Long updatedBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "updated_by", insertable = false, updatable = false)
    private User updatedByUser;

    @Column(name = "rec_status", nullable = false, length = 45)
    @Enumerated(EnumType.STRING)
    private RecordStatus recStatus = RecordStatus.CREATE;
    
    @Column(name = "note", nullable = true,columnDefinition = "LONGTEXT")
	private String note;

    @Column(name = "budget_owner_id")
    private Long budgetOwnerId;

    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "budget_owner_id", insertable = false, updatable = false)
    private User budgetOwner;

    @Column(name = "coordinator_id")
    private Long coordinatorId;

    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "coordinator_id", insertable = false, updatable = false)
    private User coordinator;

    @Column(name = "planner_id")
    private Long plannerId;

    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "planner_id", insertable = false, updatable = false)
    private User planner;

    @Column(name = "is_event_created", nullable = false)
    private Boolean isEventCreated = false;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWhiteLabelId() {
        return whiteLabelId;
    }

    public void setWhiteLabelId(Long whiteLabelId) {
        this.whiteLabelId = whiteLabelId;
    }

    public WhiteLabel getWhiteLabel() {
        return whiteLabel;
    }

    public void setWhiteLabel(WhiteLabel whiteLabel) {
        this.whiteLabel = whiteLabel;
    }

    public Long getEventRequestFormId() {
        return eventRequestFormId;
    }

    public void setEventRequestFormId(Long eventRequestFormId) {
        this.eventRequestFormId = eventRequestFormId;
    }

    public EventRequestForm getEventRequestForm() {
        return eventRequestForm;
    }

    public void setEventRequestForm(EventRequestForm eventRequestForm) {
        this.eventRequestForm = eventRequestForm;
    }

    public Long getTicketHolderAttributesId() {
        return ticketHolderAttributesId;
    }

    public void setTicketHolderAttributesId(Long ticketHolderAttributesId) {
        this.ticketHolderAttributesId = ticketHolderAttributesId;
    }

    public TicketHolderAttributes getTicketHolderAttributes() {
        return ticketHolderAttributes;
    }

    public void setTicketHolderAttributes(TicketHolderAttributes ticketHolderAttributes) {
        this.ticketHolderAttributes = ticketHolderAttributes;
    }

    public Long getRequestedForUserId() {
        return requestedForUserId;
    }

    public void setRequestedForUserId(Long requestedForUserId) {
        this.requestedForUserId = requestedForUserId;
    }

    public User getRequestedForUser() {
        return requestedForUser;
    }

    public void setRequestedForUser(User requestedForUser) {
        this.requestedForUser = requestedForUser;
    }

    public Date getRecentApprovedAt() {
        return recentApprovedAt;
    }

    public void setRecentApprovedAt(Date recentApprovedAt) {
        this.recentApprovedAt = recentApprovedAt;
    }


    public EventRequestFormSubmissionStatus getStatus() {
        return status;
    }

    public void setStatus(EventRequestFormSubmissionStatus status) {
        this.status = status;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public User getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(User createdByUser) {
        this.createdByUser = createdByUser;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public User getUpdatedByUser() {
        return updatedByUser;
    }

    public void setUpdatedByUser(User updatedByUser) {
        this.updatedByUser = updatedByUser;
    }

    public RecordStatus getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(RecordStatus recStatus) {
        this.recStatus = recStatus;
    }

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

    public Long getBudgetOwnerId() {
        return budgetOwnerId;
    }

    public void setBudgetOwnerId(Long budgetOwnerId) {
        this.budgetOwnerId = budgetOwnerId;
    }

    public User getBudgetOwner() {
        return budgetOwner;
    }

    public void setBudgetOwner(User budgetOwner) {
        this.budgetOwner = budgetOwner;
    }

    public Long getCoordinatorId() {
        return coordinatorId;
    }

    public void setCoordinatorId(Long coordinatorId) {
        this.coordinatorId = coordinatorId;
    }

    public User getCoordinator() {
        return coordinator;
    }

    public void setCoordinator(User coordinator) {
        this.coordinator = coordinator;
    }

    public Long getPlannerId() {
        return plannerId;
    }

    public void setPlannerId(Long plannerId) {
        this.plannerId = plannerId;
    }

    public User getPlanner() {
        return planner;
    }

    public void setPlanner(User planner) {
        this.planner = planner;
    }

    public Boolean getIsEventCreated() {
        return isEventCreated;
    }

    public void setIsEventCreated(Boolean isEventCreated) {
        this.isEventCreated = isEventCreated;
    }
}

