package com.accelevents.domain;

import com.accelevents.domain.enums.*;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "ticketing")
@DynamicUpdate
@Where(clause="rec_status<>'DELETE' or rec_status is NULL ")
public class Ticketing extends BaseAuditInfo implements Serializable, Cloneable {


	/**
	 * 
	 */
	private static final long serialVersionUID = -6634109383865328728L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;

	@ManyToOne(targetEntity = Event.class)
	@JoinColumn(name = "event_id")
	private Event eventid;

	@Column(name = "is_activated")
	private boolean activated;

	@Column(name = "social_sharing")
	private boolean socialSharing;

	@Type(type = "org.jadira.usertype.dateandtime.legacyjdk.PersistentDate")
	@Column(name = "event_start_date")
	private Date eventStartDate;

	@Type(type = "org.jadira.usertype.dateandtime.legacyjdk.PersistentDate")
	@Column(name = "event_end_date")
	private Date eventEndDate;

	@Column(name = "event_address")
	private String eventAddress;

	@Column(name = "collect_ticket_holder_attributes")
	private boolean collectTicketHolderAttributes = true;

    @Column(name = "checkout_minutes")
    private Integer checkoutminutes;

    @Column(name = "show_remaining_tickets")
    private boolean showRemainingTickets = true;

    @Column(name = "custom_disclaimer", columnDefinition = "TEXT")
    private String customDisclaimer;

    @Column(name = "allow_editing_of_disclaimer")
    private boolean allowEditingOfDisclaimer;

    @Column(name = "chart_key")
    private String chartKey;

    @Column(name = "allow_attendee_to_edit_info")
    private boolean allowAttendeeToEditInfo;

    @Column(name = "is_online_event")
    private boolean onlineEvent;

    @Column(name = "is_recurring_event")
    private boolean isRecurringEvent = false;

    @Column(name = "exit_intent_popup_enabled")
    private boolean exitIntentPopupEnabled = true;

    @Column(name = "latitude")
    private String latitude;

    @Column(name = "longitude")
    private String longitude;

    @Column(name = "ticket_PDF_design", columnDefinition = "TEXT")
    private String ticketPdfDesign;

    @Column(name = "require_disclaimer_confirmation")
    private boolean requireDisclaimerConfirmation;

    @Column(name = "is_lead_retrieval_enabled")
    private boolean isLeadRetrievalEnabled;

    @Column(name ="limit_event_capacity")
    private boolean limitEventCapacity;

    @Column(name ="event_capacity")
    private Double eventCapacity;

    @Column(name = "collect_addon_attributes")
    private boolean collectAddOnAttributes = false;

    @Column(name = "show_registration_button")
    private boolean showRegistrationButton = true;

    @Column(name = "enable_order_confirmation_email")
    private boolean enableOrderConfirmationEmail = true;

    @Column(name = "allow_disagree_disclaimer_confirmation")
    private boolean allowDisagreeDisclaimerConfirmation;

    @Column(name = "show_ticket_price")
    private boolean showTicketPrice = true;

    @Column(name = "pre_event_access_minutes")
    private Integer preEventAccessMinutes;

    @Column(name = "access_beefree_template")
    private boolean accessBeeFreeTemplate;

    @Column(name = "access_beefree_reminder_template")
    private boolean accessBeeFreeReminderTemplate;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus;


    @Column(name = "fee_uploaded_amount")
    private double feeUploadedAmount;

    @Column(name = "post_event_access_minutes")
    private Integer postEventAccessMinutes;

    @Column(name = "engage_email_days_permits_access")
    private Long engageEmailDaysPermitsAccess = 30L;

    @Column(name = "is_reminder_emails_allows")
    private boolean isReminderEmailsAllows=true;

    @Enumerated(EnumType.STRING)
    @Column(name = "calendar_invite_type")
    private CalendarInviteType calendarInviteType = CalendarInviteType.FULL_INVITE;

    @Column(name = "allow_ticket_exchange")
    private boolean allowTicketExchange= false;

    public Double getEventCapacity() {
        return eventCapacity;
    }

    public void setEventCapacity(Double eventCapacity) {
        this.eventCapacity = eventCapacity;
    }

    public String getTicketPdfDesign() {
        return ticketPdfDesign;
    }

    public void setTicketPdfDesign(String ticketPdfDesign) {
        this.ticketPdfDesign = ticketPdfDesign;
    }

    public String getChartKey() {
        return chartKey;
    }

    public void setChartKey(String chartKey) {
        this.chartKey = chartKey;
    }

    @Column(name = "send_cart_abandonment_email_to_user")
    private boolean sendCartAbandonmentEmailToUser = true;

    @Column(name = "event_payout_status")
    @Enumerated(value = EnumType.STRING)
    private EnumEventPayoutStatus eventPayoutStatus;

    @Column(name = "status")
    @Enumerated(value = EnumType.STRING)
    private EnumOverageStatus status = EnumOverageStatus.CREATE;

    @Column(name = "show_member_count_in_checkout")
    private boolean showMemberCountInCheckout;

    @Column(name = "show_enter_event_button_in_reminder_template")
    private boolean showEnterEventButtonInReminderTemplate;

    @Column(name = "attendee_registration_approval")
    private boolean attendeeRegistrationApproval;

    @Column(name = "attendee_registration_auto_send_mail", nullable = false)
    @Enumerated(value = EnumType.STRING)
    private AutoSendEmail attendeeRegistrationAutoSendMail = AutoSendEmail.NOT_SELECTED_NO;

    @Column(name = "event_qr_check_in")
    private boolean eventQrCheckIn = true;

    @Column(name = "email_search")
    private boolean emailSearch =  false;

    @Column(name = "ticket_number_search")
    private boolean ticketNumberSearch = true;

    @Column(name ="is_limit_registration_emails")
    private boolean isLimitRegistrationEmails=false;

    @Column(name = "org_note",columnDefinition = "LONGTEXT")
    private String orgNote;
    @Column(name = "offline_payment")
    private boolean offlinePayment;

    @Column(name = "custom_invoice_text")
    @Type(type = "text")
    private String customInvoiceText;

    @Column(name = "is_unique_ticket_holder_email")
    private boolean isUniqueTicketHolderEmail = Boolean.FALSE;

    @Column(name ="allow_check_in_with_unpaid_ticket")
    private boolean allowCheckInWithUnpaidTicket = false;

    @Column(name ="approval_request_emails_enabled")
    private boolean approvalRequestEmailsEnabled = false;

    @Column(name = "approval_email_receiver_ids",columnDefinition = "TEXT")
    private String approvalEmailReceiverIds;

    @Column(name = "invoice_PDF_design", columnDefinition = "TEXT")
    private String invoicePdfDesign;

    @Column(name = "attendee_approval_card_capture_enabled")
    private Boolean attendeeApprovalCardCaptureEnabled = false;

    @Column(name = "is_unique_ticket_buyer_email")
    private boolean isUniqueTicketBuyerEmail = Boolean.FALSE;

    public boolean isOfflinePayment() {
        return offlinePayment;
    }

    public void setOfflinePayment(boolean offlinePayment) {
        this.offlinePayment = offlinePayment;
    }

    public Ticketing() {
        super();
    }

    public Ticketing(boolean activated, Date eventEndDate) {
        super();
        this.activated = activated;
        this.eventEndDate = eventEndDate;
    }

    public Ticketing(long id, double feeUploadedAmount){
        this.id = id;
        this.feeUploadedAmount = feeUploadedAmount;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Event getEventid() {
        return eventid;
    }

    public void setEventid(Event eventid) {
        this.eventid = eventid;
    }

    public boolean getActivated() {
        return activated;
    }

    public void setActivated(boolean activated) {
        this.activated = activated;
    }

    public boolean getSocialSharing() {
        return socialSharing;
    }

    public void setSocialSharing(boolean socialSharing) {
        this.socialSharing = socialSharing;
    }

    public Date getEventStartDate() {
        return eventStartDate;
    }

    public void setEventStartDate(Date eventStartDate) {
        this.eventStartDate = eventStartDate;
    }

    public Date getEventEndDate() {
        return eventEndDate;
    }

    public void setEventEndDate(Date eventEndDate) {
        this.eventEndDate = eventEndDate;
    }

    public String getEventAddress() {
        return eventAddress;
    }

    public void setEventAddress(String eventAddress) {
        this.eventAddress = eventAddress;
    }

    public boolean getCollectTicketHolderAttributes() {
        return collectTicketHolderAttributes;
    }

    public void setCollectTicketHolderAttributes(boolean collectTicketHolderAttributes) {
        this.collectTicketHolderAttributes = collectTicketHolderAttributes;
    }

    public Integer getCheckoutminutes() {
        return checkoutminutes;
    }

    public void setCheckoutminutes(Integer checkoutminutes) {
        this.checkoutminutes = checkoutminutes;
    }

    public String getCustomDisclaimer() {
        return customDisclaimer;
    }

    public void setCustomDisclaimer(String customDisclaimer) {
        this.customDisclaimer = customDisclaimer;
    }

    public boolean isAllowEditingOfDisclaimer() {
        return allowEditingOfDisclaimer;
    }

    public void setAllowEditingOfDisclaimer(Boolean allowEditingOfDisclaimer) {
        this.allowEditingOfDisclaimer = (null != allowEditingOfDisclaimer) ? allowEditingOfDisclaimer : false;//NOSONAR
    }
    public boolean isExitIntentPopupEnabled() {
        return exitIntentPopupEnabled;
    }

    public void setExitIntentPopupEnabled(boolean exitIntentPopupEnabled) {
        this.exitIntentPopupEnabled = exitIntentPopupEnabled;
    }

    public boolean isLimitEventCapacity() {
        return limitEventCapacity;
    }

    public void setLimitEventCapacity(boolean limitEventCapacity) {
        this.limitEventCapacity = limitEventCapacity;
    }

    public EnumOverageStatus getStatus() {
        return status;
    }

    public void setStatus(EnumOverageStatus status) {
        this.status = status;
    }

    public Integer getPostEventAccessMinutes() { return postEventAccessMinutes; }

    public void setPostEventAccessMinutes(Integer postEventAccessMinutes) { this.postEventAccessMinutes = postEventAccessMinutes; }

    public boolean isLimitRegistrationEmails() { return isLimitRegistrationEmails; }

    public void setLimitRegistrationEmails(boolean limitRegistrationEmails) { isLimitRegistrationEmails = limitRegistrationEmails; }

    public String getCustomInvoiceText() { return customInvoiceText; }

    public void setCustomInvoiceText(String customInvoiceText) { this.customInvoiceText = customInvoiceText; }

    public Long getEngageEmailDaysPermitsAccess() {
        return engageEmailDaysPermitsAccess;
    }

    public void setEngageEmailDaysPermitsAccess(Long engageEmailDaysPermitsAccess) {
        this.engageEmailDaysPermitsAccess = engageEmailDaysPermitsAccess;
    }

    @Override
    public String toString() {
        return "Ticketing{" +
                "id=" + id +
                ", eventid=" + eventid +
                ", activated=" + activated +
                ", socialSharing=" + socialSharing +
                ", eventStartDate=" + eventStartDate +
                ", eventEndDate=" + eventEndDate +
                ", eventAddress='" + eventAddress + '\'' +
                ", collectTicketHolderAttributes=" + collectTicketHolderAttributes +
                ", checkoutminutes=" + checkoutminutes +
                ", showRemainingTickets=" + showRemainingTickets +
                ", customDisclaimer='" + customDisclaimer + '\'' +
                ", allowEditingOfDisclaimer=" + allowEditingOfDisclaimer +
                ", chartKey='" + chartKey + '\'' +
                ", allowAttendeeToEditInfo=" + allowAttendeeToEditInfo +
                ", onlineEvent=" + onlineEvent +
                ", isRecurringEvent=" + isRecurringEvent +
                ", exitIntentPopupEnabled=" + exitIntentPopupEnabled +
                ", latitude='" + latitude + '\'' +
                ", longitude='" + longitude + '\'' +
                ", ticketPdfDesign='" + ticketPdfDesign + '\'' +
                ", invoicePdfDesign='" + invoicePdfDesign + '\'' +
                ", requireDisclaimerConfirmation=" + requireDisclaimerConfirmation +
                ", isLeadRetrievalEnabled=" + isLeadRetrievalEnabled +
                ", limitEventCapacity=" + limitEventCapacity +
                ", eventCapacity=" + eventCapacity +
                ", collectAddOnAttributes=" + collectAddOnAttributes +
                ", showRegistrationButton=" + showRegistrationButton +
                ", enableOrderConfirmationEmail=" + enableOrderConfirmationEmail +
                ", allowDisagreeDisclaimerConfirmation=" + allowDisagreeDisclaimerConfirmation +
                ", showTicketPrice=" + showTicketPrice +
                ", preEventAccessMinutes=" + preEventAccessMinutes +
                ", accessBeeFreeTemplate=" + accessBeeFreeTemplate +
                ", accessBeeFreeReminderTemplate=" + accessBeeFreeReminderTemplate +
                ", recordStatus=" + recordStatus +
                ", feeUploadedAmount=" + feeUploadedAmount +
                ", postEventAccessMinutes=" + postEventAccessMinutes +
                ", engageEmailDaysPermitsAccess=" + engageEmailDaysPermitsAccess +
                ", eventPayoutStatus=" + eventPayoutStatus +
                ", status=" + status +
                ", showMemberCountInCheckout=" + showMemberCountInCheckout +
                ", showEnterEventButtonInReminderTemplate=" + showEnterEventButtonInReminderTemplate +
                ", attendeeRegistrationApproval=" + attendeeRegistrationApproval +
                ", attendeeRegistrationAutoSendMail=" + attendeeRegistrationAutoSendMail +
                ", eventQrCheckIn=" + eventQrCheckIn +
                ", emailSearch=" + emailSearch +
                ", ticketNumberSearch=" + ticketNumberSearch +
                ", isLimitRegistrationEmails=" + isLimitRegistrationEmails +
                ", orgNote='" + orgNote + '\'' +
                ", offlinePayment=" + offlinePayment +
                ", isUniqueTicketHolderEmail=" + isUniqueTicketHolderEmail +
                ", allowCheckInWithUnpaidTicket=" + allowCheckInWithUnpaidTicket +
                ", sendCartAbandonmentEmailToUser=" + sendCartAbandonmentEmailToUser +
                ", allowTicketExchange=" + allowTicketExchange +
                ", attendeeApprovalCardCaptureEnabled=" + attendeeApprovalCardCaptureEnabled +
                ", isUniqueTicketBuyerEmail=" + isUniqueTicketBuyerEmail +
                '}';

    }

    @Override
    public Object clone() {//NOSONAR
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;//NOSONAR
        }
    }

    public boolean isAllowAttendeeToEditInfo() {
        return allowAttendeeToEditInfo;
    }

    public void setAllowAttendeeToEditInfo(boolean allowAttendeeToEditInfo) {
        this.allowAttendeeToEditInfo = allowAttendeeToEditInfo;
    }

    public boolean isOnlineEvent() {
        return onlineEvent;
    }

    public void setOnlineEvent(Boolean onlineEvent) {
        this.onlineEvent = (null != onlineEvent) ? onlineEvent : false;//NOSONAR
    }

    public boolean isRecurringEvent() {
        return isRecurringEvent;
    }

    public void setRecurringEvent(boolean recurringEvent) {
        isRecurringEvent = recurringEvent;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public EnumEventPayoutStatus getEventPayoutStatus() {
        return eventPayoutStatus;
    }

    public void setEventPayoutStatus(EnumEventPayoutStatus eventPayoutStatus) {
        this.eventPayoutStatus = eventPayoutStatus;
    }

    public boolean isRequireDisclaimerConfirmation() {
        return requireDisclaimerConfirmation;
    }

    public void setRequireDisclaimerConfirmation(boolean requireDisclaimerConfirmation) {
        this.requireDisclaimerConfirmation = requireDisclaimerConfirmation;
    }

    public boolean isLeadRetrievalEnabled() {
        return isLeadRetrievalEnabled;
    }

    public void setLeadRetrievalEnabled(boolean leadRetrievalEnabled) {
		this.isLeadRetrievalEnabled = leadRetrievalEnabled;
	}

	public boolean isCollectAddOnAttributes() {
        return collectAddOnAttributes;
    }

    public void setCollectAddOnAttributes(boolean collectAddOnAttributes) {
        this.collectAddOnAttributes = collectAddOnAttributes;
    }

    public boolean isAllowDisagreeDisclaimerConfirmation() {
        return allowDisagreeDisclaimerConfirmation;
    }

    public void setAllowDisagreeDisclaimerConfirmation(boolean allowDisagreeDisclaimerConfirmation) {
        this.allowDisagreeDisclaimerConfirmation = allowDisagreeDisclaimerConfirmation;
    }

    public Integer getPreEventAccessMinutes() {
        return preEventAccessMinutes;
    }

    public void setPreEventAccessMinutes(Integer preEventAccessMinutes) {
        this.preEventAccessMinutes = preEventAccessMinutes;
    }

    public boolean isAccessBeeFreeTemplate() {
        return accessBeeFreeTemplate;
    }

    public void setAccessBeeFreeTemplate(boolean accessBeeFreeTemplate) {
        this.accessBeeFreeTemplate = accessBeeFreeTemplate;
    }

    public boolean isShowRemainingTickets() {
        return showRemainingTickets;
    }

    public void setShowRemainingTickets(boolean showRemainingTickets) {
        this.showRemainingTickets = showRemainingTickets;
    }

    public boolean isShowRegistrationButton() {
        return showRegistrationButton;
    }

    public void setShowRegistrationButton(boolean showRegistrationButton) {
        this.showRegistrationButton = showRegistrationButton;
    }

    public boolean isShowTicketPrice() {
        return showTicketPrice;
    }

    public void setShowTicketPrice(boolean showTicketPrice) {
        this.showTicketPrice = showTicketPrice;
    }

    public boolean isEnableOrderConfirmationEmail() {
        return enableOrderConfirmationEmail;
    }

    public void setEnableOrderConfirmationEmail(boolean enableOrderConfirmationEmail) {
        this.enableOrderConfirmationEmail = enableOrderConfirmationEmail;
    }

    public RecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }


    public double getFeeUploadedAmount() {
        return feeUploadedAmount;
    }

    public void setFeeUploadedAmount(double feeUploadedAmount) {
        this.feeUploadedAmount = feeUploadedAmount;
    }

    public boolean isAccessBeeFreeReminderTemplate() { return accessBeeFreeReminderTemplate; }

    public void setAccessBeeFreeReminderTemplate(boolean accessBeeFreeReminderTemplate) { this.accessBeeFreeReminderTemplate = accessBeeFreeReminderTemplate; }

    public boolean isShowMemberCountInCheckout() {
        return showMemberCountInCheckout;
    }

    public void setShowMemberCountInCheckout(boolean showMemberCountInCheckout) {
        this.showMemberCountInCheckout = showMemberCountInCheckout;
    }

    public boolean isShowEnterEventButtonInReminderTemplate() {
        return showEnterEventButtonInReminderTemplate;
    }

    public void setShowEnterEventButtonInReminderTemplate(boolean showEnterEventButtonInReminderTemplate) {
        this.showEnterEventButtonInReminderTemplate = showEnterEventButtonInReminderTemplate;
    }

    public boolean isAttendeeRegistrationApproval() {
        return attendeeRegistrationApproval;
    }

    public void setAttendeeRegistrationApproval(boolean attendeeRegistrationApproval) {
        this.attendeeRegistrationApproval = attendeeRegistrationApproval;
    }

    public AutoSendEmail getAttendeeRegistrationAutoSendMail() {
        return attendeeRegistrationAutoSendMail;
    }

    public void setAttendeeRegistrationAutoSendMail(AutoSendEmail attendeeRegistrationAutoSendMail) {
        this.attendeeRegistrationAutoSendMail = attendeeRegistrationAutoSendMail;
    }

    public boolean isEventQrCheckIn() {
        return eventQrCheckIn;
    }

    public void setEventQrCheckIn(boolean eventQrCheckIn) {
        this.eventQrCheckIn = eventQrCheckIn;
    }

    public boolean isEmailSearch() {
        return emailSearch;
    }

    public void setEmailSearch(boolean emailSearch) {
        this.emailSearch = emailSearch;
    }

    public boolean isTicketNumberSearch() {
        return ticketNumberSearch;
    }

    public void setTicketNumberSearch(boolean ticketNumberSearch) {
        this.ticketNumberSearch = ticketNumberSearch;
    }

    public String getOrgNote() {
        return orgNote;
    }

    public void setOrgNote(String orgNote) {
        this.orgNote = orgNote;
    }

    public boolean getUniqueTicketHolderEmail() {
        return isUniqueTicketHolderEmail;
    }

    public void setUniqueTicketHolderEmail(boolean uniqueTicketHolderEmail) {
        isUniqueTicketHolderEmail = uniqueTicketHolderEmail;
    }

    public boolean isAllowCheckInWithUnpaidTicket() {
        return allowCheckInWithUnpaidTicket;
    }

    public void setAllowCheckInWithUnpaidTicket(boolean allowCheckInWithUnpaidTicket) {
        this.allowCheckInWithUnpaidTicket = allowCheckInWithUnpaidTicket;
    }

    public boolean isReminderEmailsAllows() { return isReminderEmailsAllows; }

    public void setReminderEmailsAllows(boolean reminderEmailsAllows) { this.isReminderEmailsAllows = reminderEmailsAllows; }

    public boolean isSendCartAbandonmentEmailToUser() {return sendCartAbandonmentEmailToUser;}

    public void setSendCartAbandonmentEmailToUser(boolean sendCartAbandonmentEmailToUser) {this.sendCartAbandonmentEmailToUser = sendCartAbandonmentEmailToUser;}

    public boolean isApprovalRequestEmailsEnabled() {
        return approvalRequestEmailsEnabled;
    }

    public CalendarInviteType getCalendarInviteType() {
        return calendarInviteType;
    }

    public void setCalendarInviteType(CalendarInviteType calendarInviteType) {
        this.calendarInviteType = calendarInviteType;
    }

    public void setApprovalRequestEmailsEnabled(boolean approvalRequestEmailsEnabled) {
        this.approvalRequestEmailsEnabled = approvalRequestEmailsEnabled;
    }

    public String getApprovalEmailReceiverIds() {
        return approvalEmailReceiverIds;
    }

    public void setApprovalEmailReceiverIds(String approvalEmailReceiverIds) {
        this.approvalEmailReceiverIds = approvalEmailReceiverIds;
    }

    public String getInvoicePdfDesign() {return invoicePdfDesign;}

    public void setInvoicePdfDesign(String invoicePdfDesign) {this.invoicePdfDesign = invoicePdfDesign;}

    public boolean isAllowTicketExchange() {
        return allowTicketExchange;
    }

    public void setAllowTicketExchange(boolean allowTicketExchange) {
        this.allowTicketExchange = allowTicketExchange;
    }

    public Boolean isAttendeeApprovalCardCaptureEnabled() {
        return attendeeApprovalCardCaptureEnabled;
    }

    public void setAttendeeApprovalCardCaptureEnabled(Boolean attendeeApprovalCardCaptureEnabled) {
        this.attendeeApprovalCardCaptureEnabled = attendeeApprovalCardCaptureEnabled;
    }

    public boolean isUniqueTicketBuyerEmail() {
        return isUniqueTicketBuyerEmail;
    }

    public void setUniqueTicketBuyerEmail(boolean uniqueTicketBuyerEmail) {
        isUniqueTicketBuyerEmail = uniqueTicketBuyerEmail;
    }
}
