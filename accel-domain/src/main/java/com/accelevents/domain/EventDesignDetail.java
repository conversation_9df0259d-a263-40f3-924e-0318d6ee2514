package com.accelevents.domain;

import com.accelevents.domain.enums.EventType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.utils.GeneralUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

import static com.accelevents.utils.Constants.DEFAULT_ORDER_CONFIRMATION_TEXT;

/**
 * To store all the events designs details.
 *
 */
@Entity
@Table(name = "event_design_details")
@Where(clause="rec_status<>'DELETE' or rec_status is NULL ")
public class EventDesignDetail extends BaseAuditInfo implements Serializable, Cloneable {


	/**
	 * 
	 */
	private static final long serialVersionUID = -9208845724734166656L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;

	@Column(name = "logo_enabled")
	private boolean logoEnabled = true;

	@Column(name = "logo_image")
	private String logoImage;

	@Column(name = "header_logo_image")
	private String headerLogoImage;

	@Column(name = "header_color", length = 30)
	private String headerColor = "#1e1e4e";

	@Column(name = "header_font_color", length = 8)
	private String headerFontColor;

	@Column(name = "banner_image_enabled")
	private boolean bannerImageEnabled = true;

	@Column(name = "banner_image")
	private String bannerImage;

	@Column(name = "description", columnDefinition = "LONGTEXT")
	private String desc;//change

    @Column(name = "show_total_funds_raised")
	private boolean totalFundRaisedShow = true;

	@Column(name = "social_sharing")
	private boolean socialSharingEnabled = true;

	@OneToOne(cascade = { CascadeType.MERGE })
	@JoinColumn(name = "event_id")
	private Event event;

	@Column(name = "intercom_activated")
	private boolean intercomActivated = true;

	@Column(name = "help_center_activated")
	private boolean helpCenterActivated = true;

	@Column(name = "powered_by_ae_activated")
	private boolean poweredByAeActivated = true;

	@Column(name = "marketing_opt_in_checked")
	private boolean marketingOptInChecked = true;

	@Column(name = "marketing_opt_in_hidden")
	private boolean marketingOptInHidden = false;

	@Column(name = "billing_page_enabled")
	private boolean biillingPageEnabled = true;

	@Column(name = "hide_fund_raising_module_toggle")
	private boolean hideFundRaisingModuleToggle;

	@Column(name = "show_enable_module_popup")
	private boolean showEnableModulePopup = true;

	@Column(name = "auction_tab_title")
	private String auctionTabTitle;

	@Column(name = "fundANeed_tab_title")
	private String fundANeedTabTitle;

	@Column(name = "raffle_tab_title")
	private String raffleTabTitle;

	@Column(name = "ticketing_tab_title")
	private String ticketingTabTitle;

	@Column(name = "donation_tab_title")
	private String donationTabTitle;

	@Column(name = "reply_email")
	private String replyToEmail;

    @Column(name = "email_sender_name")
    private String emailSenderName;
    @Column(name = "notification_email")
    private String notificationEmail;

	@Column(name = "event_tag_line")
	private String eventTagLine;

	@Column(name = "sponsor_section",  columnDefinition = "LONGTEXT")
	private String sponsorSection;

	@Column(name = "display_background_color")
	private String displayBackgroundColor;

	@Column(name = "display_text_color")
	private String displayTextColor;

	@Column(name = "hide_sponsor_section")
	private boolean hideSponsorSection;

	@Column(name = "buy_button_text")
	@Type(type = "text")
	private String ticketingBuyButtonText;

    @Column(name = "register_button_text")
    @Type(type = "text")
    private String registerButtonText;

	@Column(name = "raffle_buy_button_text")
	@Type(type = "text")
	private String raffleBuyButtonText;

    @Column(name = "tracking_script", columnDefinition = "TEXT")
    private String trackingScript;//change

    @Column(name = "virtual_event_script", columnDefinition = "TEXT")
    private String virtualEventScript;//change

	@Column(name = "theme_id")
	private Long themeId;

	@Column(name = "order_confirmation_text")
	private String orderConfirmationText;

	@Column(name = "enable_sessions_speakers")
	private boolean enableSessionsSpeakers = false;

	@Column(name = "enable_auto_assigned_sequence")
	private boolean enableAutoAssignedSequence = false;

	@Column(name = "neon_event_id")
	private String neonEventId;

	@Column(name = "event_type")
	@Enumerated(value = EnumType.STRING)
	private EventType eventType;

	@Column(name = "enable_event_type")
	private boolean enableEventType = true;

	@Column(name = "views_scroll_speed")
	private Long viewScrollSpeed=30000l; // Default value

	@Column(name = "hide_google_map")
	private boolean hideGoogleMap = false;

	@Column(name = "default_resend_email_hour_before_event")
	private int defaultResendEmailHoursBeforeEvent;

	@Column(name = "donation_button_text")
	@Type(type = "text")
	private String donationButtonText;

	@Column(name = "configure_tabs_json", columnDefinition = "TEXT")
	private String configureTabsAsJson;

	@Column(name = "allow_ended_event_access")
	private boolean allowEndedEventAccess = true;

	@Column(name ="hide_create_event_button")
	private boolean hideCreateEventButton = false ;

	@Column(name="hide_product_update_notification")
	private boolean hideProductUpdateNotification = false;

    @Column(name = "event_calendar_invite", columnDefinition = "MEDIUMTEXT")
    private String eventCalendarInvite;//change

	@Column(name = "display_tabs_color")
	private String displayTabsColor;

    @Column(name = "display_tabs_text_color")
    private String displayTabTextColor;

    @Column(name = "show_organizer")
    private boolean showOrganizer = true;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus;

    @Column(name="linkedin_trackin_partner_id")
    private String linkedinTrackinPartnerId;

    @Column(name="linkedin_trackin_conversion_id")
    private String linkedinTrackinConversionId;

    @Column(name="is_theme_wise_default_config")
    private boolean isThemeWiseDefaultConfig;

    @Column(name="logo_image_extension")
    private String logoImageExtension;

    @Column(name="banner_image_extension")
    private String bannerImageExtension;

    @Column(name="venue_map_image")
    private String venueMapImage;

    @Column(name="is_custom_css_enable")
    private boolean isCustomCssEnable = true;

    @Column(name = "speaker_reg", columnDefinition = "LONGTEXT")
    private String speakerReg;

    @Column(name = "exhibitor_reg", columnDefinition = "LONGTEXT")
    private String exhibitorReg;

    @Column(name = "reviewer_reg", columnDefinition = "LONGTEXT")
    private String reviewerReg;

    @Column(name = "enable_captcha")
    private boolean enableCaptcha;

    @Column(name="is_advanced_website_layout")
    private boolean isAdvancedWebsiteLayout;

    @Column(name="is_print_node_enabled")
    private boolean isPrintNodeEnabled=false;

    @Column(name="print_node_api_key")
    private String printNodeApiKey;

    @Column(name="embedded_checkout_form_enabled")
    private boolean embeddedCheckoutFormEnabled = false;

    @Column(name = "enable_rfid_badge_scanning")
    private boolean enableRfidBadgeScanning = false;

    public boolean isHideCreateEventButton() {
		return hideCreateEventButton;
	}

	public void setHideCreateEventButton(boolean hideCreateEventButton) {
		this.hideCreateEventButton = hideCreateEventButton;
	}

	public boolean isHideProductUpdateNotification() { return hideProductUpdateNotification; }

	public void setHideProductUpdateNotification(boolean hideProductUpdateNotification) {
		this.hideProductUpdateNotification = hideProductUpdateNotification;
	}

	public EventDesignDetail() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public boolean isLogoEnabled() {
		return logoEnabled;
	}

	public void setLogoEnabled(boolean logoEnabled) {
		this.logoEnabled = logoEnabled;
	}

	public String getLogoImage() {
		return logoImage;
	}

	public void setLogoImage(String logoImage) {
		this.logoImage = logoImage;
	}

	public String getHeaderLogoImage() {
		return headerLogoImage;
	}

	public void setHeaderLogoImage(String headerLogoImage) {
		this.headerLogoImage = headerLogoImage;
	}

	public boolean isBannerImageEnabled() {
		return bannerImageEnabled;
	}

	public void setBannerImageEnabled(boolean bannerImageEnabled) {
		this.bannerImageEnabled = bannerImageEnabled;
	}

	public String getBannerImage() {
		return this.bannerImage;
	}

	public void setBannerImage(String bannerImage) {
		this.bannerImage = bannerImage;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

    public boolean isTotalFundRaisedShow() {
        return totalFundRaisedShow;
    }

    public void setTotalFundRaisedShow(boolean totalFundRaisedShow) {
        this.totalFundRaisedShow = totalFundRaisedShow;
    }


	public boolean isSocialSharingEnabled() {
		return socialSharingEnabled;
	}

	public void setSocialSharingEnabled(boolean socialSharingEnabled) {
		this.socialSharingEnabled = socialSharingEnabled;
	}

	public Event getEvent() {
		return event;
	}

	public void setEvent(Event event) {
		this.event = event;
	}

	public boolean isIntercomActivated() {
		return intercomActivated;
	}

	public void setIntercomActivated(boolean intercomActivated) {
		this.intercomActivated = intercomActivated;
	}

	public boolean isHelpCenterActivated() {
		return helpCenterActivated;
	}

	public void setHelpCenterActivated(boolean helpCenterActivated) {
		this.helpCenterActivated = helpCenterActivated;
	}

	public boolean isPoweredByAeActivated() {
		return poweredByAeActivated;
	}

	public void setPoweredByAeActivated(boolean poweredByAeActivated) {
		this.poweredByAeActivated = poweredByAeActivated;
	}

	public boolean isMarketingOptInChecked() {
		return marketingOptInChecked;
	}

	public void setMarketingOptInChecked(boolean marketingOptInChecked) {
		this.marketingOptInChecked = marketingOptInChecked;
	}

	public boolean isMarketingOptInHidden() {
		return marketingOptInHidden;
	}

	public void setMarketingOptInHidden(boolean marketingOptInHidden) {
		this.marketingOptInHidden = marketingOptInHidden;
	}

	public boolean isBiillingPageEnabled() {
		return biillingPageEnabled;
	}

	public void setBiillingPageEnabled(boolean biillingPageEnabled) {
		this.biillingPageEnabled = biillingPageEnabled;
	}

	public String getHeaderColor() {
		return headerColor;
	}

	public void setHeaderColor(String headerColor) {
		this.headerColor = headerColor;
	}

	public String getHeaderFontColor() {
		return headerFontColor;
	}

	public void setHeaderFontColor(String headerFontColor) {
		this.headerFontColor = headerFontColor;
	}

	public boolean isHideFundRaisingModuleToggle() {
		return hideFundRaisingModuleToggle;
	}

	public void setHideFundRaisingModuleToggle(boolean hideFundRaisingModuleToggle) {
		this.hideFundRaisingModuleToggle = hideFundRaisingModuleToggle;
	}

	public boolean isShowEnableModulePopup() {
		return showEnableModulePopup;
	}

	public void setShowEnableModulePopup(boolean showEnableModulePopup) {
		this.showEnableModulePopup = showEnableModulePopup;
	}

	public String getAuctionTabTitle() {
		return auctionTabTitle;
	}

	public void setAuctionTabTitle(String auctionTabTitle) {
		this.auctionTabTitle = auctionTabTitle;
	}

	public String getFundANeedTabTitle() {
		return fundANeedTabTitle;
	}

	public void setFundANeedTabTitle(String fundANeedTabTitle) {
		this.fundANeedTabTitle = fundANeedTabTitle;
	}

	public String getRaffleTabTitle() {
		return raffleTabTitle;
	}

	public void setRaffleTabTitle(String raffleTabTitle) {
		this.raffleTabTitle = raffleTabTitle;
	}

	public String getTicketingTabTitle() {
		return ticketingTabTitle;
	}

	public void setTicketingTabTitle(String ticketingTabTitle) {
		this.ticketingTabTitle = ticketingTabTitle;
	}

	public String getDonationTabTitle() {
		return donationTabTitle;
	}

	public void setDonationTabTitle(String donationTabTitle) {
		this.donationTabTitle = donationTabTitle;
	}

	public String getReplyToEmail() {
		return replyToEmail;
	}

	public void setReplyToEmail(String replyToEmail) {
		if(null != replyToEmail){
			replyToEmail = replyToEmail.trim();
		}
		this.replyToEmail = replyToEmail;
	}

    public String getEmailSenderName() {
        return emailSenderName;
    }

    public void setEmailSenderName(String emailSenderName) {
        this.emailSenderName = emailSenderName;
    }

    public String getNotificationEmail() {
        return notificationEmail;
    }

    public void setNotificationEmail(String notificationEmail) {
        if(null != notificationEmail) {
            this.notificationEmail = notificationEmail.trim();
        }
        this.notificationEmail = notificationEmail;
    }

    public String getEventTagLine() {
		return eventTagLine;
	}

	public void setEventTagLine(String eventTagLine) {
		this.eventTagLine = eventTagLine;
	}

	public String getSponsorSection() {
		return sponsorSection;
	}

	public void setSponsorSection(String sponsorSection) {
		this.sponsorSection = sponsorSection;
	}

	public String getDisplayBackgroundColor() {
		return displayBackgroundColor;
	}

	public void setDisplayBackgroundColor(String displayBackgroundColor) {
		this.displayBackgroundColor = displayBackgroundColor;
	}

	public String getDisplayTextColor() {
		return displayTextColor;
	}

	public void setDisplayTextColor(String displayTextColor) {
		this.displayTextColor = displayTextColor;
	}

	public boolean isHideSponsorSection() {
		return hideSponsorSection;
	}

	public void setHideSponsorSection(boolean hideSponsorSection) {
		this.hideSponsorSection = hideSponsorSection;
	}

	public String getTicketingBuyButtonText() {
		return ticketingBuyButtonText;
	}

	public void setTicketingBuyButtonText(String ticketingBuyButtonText) {
		this.ticketingBuyButtonText = ticketingBuyButtonText;
	}

	public String getTrackingScript() {
		return trackingScript;
	}

	public void setTrackingScript(String trackingScript) {
		this.trackingScript = trackingScript;
	}

	public Long getThemeId() {
		return themeId;
	}

	public void setThemeId(Long themeId) {
		this.themeId = themeId;
	}

	public String getNeonEventId() {
		return neonEventId;
	}

	public void setNeonEventId(String neonEventId) {
		this.neonEventId = neonEventId;
	}

	public EventType getEventType() {
		return eventType;
	}

	public void setEventType(EventType eventType) {
		this.eventType = eventType;
	}

	public boolean isHideGoogleMap() {
		return hideGoogleMap;
	}

	public void setHideGoogleMap(boolean hideGoogleMap) {
		this.hideGoogleMap = hideGoogleMap;
	}

    public String getDisplayTabTextColor() {
        return displayTabTextColor;
    }

    public void setDisplayTabTextColor(String displayTabTextColor) {
        this.displayTabTextColor = displayTabTextColor;
    }

    public boolean isEnableRfidBadgeScanning() {
        return enableRfidBadgeScanning;
    }

    public void setEnableRfidBadgeScanning(boolean enableRfidBadgeScanning) {
        this.enableRfidBadgeScanning = enableRfidBadgeScanning;
    }

    @Override
	public String toString() {
		return "EventDesignDetail{" +
				"id=" + id +
				", logoEnabled=" + logoEnabled +
				", logoImage='" + logoImage + '\'' +
				", headerLogoImage='" + headerLogoImage + '\'' +
				", headerColor='" + headerColor + '\'' +
				", headerFontColor='" + headerFontColor + '\'' +
				", bannerImageEnabled=" + bannerImageEnabled +
				", bannerImage='" + bannerImage + '\'' +
				", desc='" + desc + '\'' +
				", totalFundRaisedShow=" + totalFundRaisedShow +
				", socialSharingEnabled=" + socialSharingEnabled +
				", event=" + event +
				", intercomActivated=" + intercomActivated +
				", helpCenterActivated=" + helpCenterActivated +
				", poweredByAeActivated=" + poweredByAeActivated +
				", marketingOptInChecked=" + marketingOptInChecked +
				", marketingOptInHidden=" + marketingOptInHidden +
				", biillingPageEnabled=" + biillingPageEnabled +
				", hideFundRaisingModuleToggle=" + hideFundRaisingModuleToggle +
				", showEnableModulePopup=" + showEnableModulePopup +
				", auctionTabTitle='" + auctionTabTitle + '\'' +
				", fundANeedTabTitle='" + fundANeedTabTitle + '\'' +
				", raffleTabTitle='" + raffleTabTitle + '\'' +
				", ticketingTabTitle='" + ticketingTabTitle + '\'' +
				", donationTabTitle='" + donationTabTitle + '\'' +
				", replyToEmail='" + replyToEmail + '\'' +
				", eventTagLine='" + eventTagLine + '\'' +
				", sponsorSection='" + sponsorSection + '\'' +
				", displayBackgroundColor='" + displayBackgroundColor + '\'' +
				", displayTextColor='" + displayTextColor + '\'' +
                ", eventCalendarInvite='" + eventCalendarInvite + '\'' +
				", displayTabsColor='" + displayTabsColor + '\'' +
                ", showOrganizer='" + showOrganizer + '\'' +
                ", linkedinTrackinPartnerId='" + linkedinTrackinPartnerId + '\'' +
                ", linkedinTrackinConversionId='" + linkedinTrackinConversionId + '\'' +
                ", isThemeWiseDefaultConfig='" + isThemeWiseDefaultConfig + '\'' +
                ", logoImageExtension='" + logoImageExtension + '\'' +
                ", bannerImageExtension='" + bannerImageExtension + '\'' +
                ", venueMapImage='" + venueMapImage + '\'' +
                ", enableCaptcha='" + enableCaptcha + '\'' +
                ", isAdvancedWebsiteLayout='" + isAdvancedWebsiteLayout + '\'' +
                ", isPrintNodeEnabled='" + isPrintNodeEnabled + '\'' +
                ", printNodeApiKey='" + printNodeApiKey + '\'' +
                ", embeddedCheckoutFormEnabled='" + embeddedCheckoutFormEnabled + '\'' +
                ", enableRfidBadgeScanning='" + enableRfidBadgeScanning + '\'' +
				'}';
	}


	public String getOrderConfirmationText() {
		orderConfirmationText = StringUtils.isNotEmpty(orderConfirmationText) ? orderConfirmationText : DEFAULT_ORDER_CONFIRMATION_TEXT;
		orderConfirmationText = GeneralUtils.replace(orderConfirmationText, "$EventName", event.getName());
		orderConfirmationText = GeneralUtils.replace(orderConfirmationText, "$EventUrl", event.getEventURL());
		return orderConfirmationText;
	}


	public void setOrderConfirmationText(String orderConfirmationText) {
		this.orderConfirmationText = StringUtils.isNotEmpty(orderConfirmationText) ? orderConfirmationText : DEFAULT_ORDER_CONFIRMATION_TEXT;
	}

	@Override
	public Object clone() {
		try {
			return super.clone();
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
			return null;
		}
	}

	public boolean isEnableSessionsSpeakers() {
		return enableSessionsSpeakers;
	}

	public void setEnableSessionsSpeakers(boolean enableSessionsSpeakers) {
		this.enableSessionsSpeakers = enableSessionsSpeakers;
	}

	public boolean isEnableAutoAssignedSequence() {
		return enableAutoAssignedSequence;
	}

	public void setEnableAutoAssignedSequence(boolean enableAutoAssignedSequence) {
		this.enableAutoAssignedSequence = enableAutoAssignedSequence;
	}

	public boolean isEnableEventType() {
		return enableEventType;
	}

	public void setEnableEventType(boolean enableEventType) {
		this.enableEventType = enableEventType;
	}

	public Long getViewScrollSpeed() {
		return viewScrollSpeed;
	}

	public void setViewScrollSpeed(Long viewScrollSpeed) {
		this.viewScrollSpeed = viewScrollSpeed;
	}

	public int getDefaultResendEmailHoursBeforeEvent() {
		return defaultResendEmailHoursBeforeEvent;
	}

	public void setDefaultResendEmailHoursBeforeEvent(int defaultResendEmailHoursBeforeEvent) {
		this.defaultResendEmailHoursBeforeEvent = defaultResendEmailHoursBeforeEvent;
	}

	public String getDonationButtonText() {
		return donationButtonText;
	}

	public void setDonationButtonText(String donationButtonText) {
		this.donationButtonText = donationButtonText;
	}

	public String getRaffleBuyButtonText() {
		return raffleBuyButtonText;
	}

	public void setRaffleBuyButtonText(String raffleBuyButtonText) {
		this.raffleBuyButtonText = raffleBuyButtonText;
	}

	public String getConfigureTabsAsJson() {
		return configureTabsAsJson;
	}

	public void setConfigureTabsAsJson(String configureTabsAsJson) {
		this.configureTabsAsJson = configureTabsAsJson;
	}

	public String getVirtualEventScript() {
		return virtualEventScript;
	}

	public void setVirtualEventScript(String virtualEventScript) {
		this.virtualEventScript = virtualEventScript;
	}

    public boolean isAllowEndedEventAccess() {
        return allowEndedEventAccess;
    }

    public void setAllowEndedEventAccess(boolean allowEndedEventAccess) {
        this.allowEndedEventAccess = allowEndedEventAccess;
    }

    public String getEventCalendarInvite() {
        return eventCalendarInvite;
    }

    public void setEventCalendarInvite(String eventCalendarInvite) {
        this.eventCalendarInvite = eventCalendarInvite;
    }

	public String getDisplayTabsColor() {
		return displayTabsColor;
	}

	public void setDisplayTabsColor(String displayTabsColor) {
		this.displayTabsColor = displayTabsColor;
	}

    public boolean isShowOrganizer() {
        return showOrganizer;
    }

    public void setShowOrganizer(boolean showOrganizer) {
        this.showOrganizer = showOrganizer;
    }

    public RecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getLinkedinTrackinPartnerId() {
        return linkedinTrackinPartnerId;
    }

    public void setLinkedinTrackinPartnerId(String linkedinTrackinPartnerId) {
        this.linkedinTrackinPartnerId = linkedinTrackinPartnerId;
    }

    public String getLinkedinTrackinConversionId() {
        return linkedinTrackinConversionId;
    }

    public void setLinkedinTrackinConversionId(String linkedinTrackinConversionId) {
        this.linkedinTrackinConversionId = linkedinTrackinConversionId;
    }

    public boolean isThemeWiseDefaultConfig() {
        return isThemeWiseDefaultConfig;
    }

    public void setThemeWiseDefaultConfig(boolean themeWiseDefaultConfig) {
        isThemeWiseDefaultConfig = themeWiseDefaultConfig;
    }

    public String getLogoImageExtension() {
        return logoImageExtension;
    }

    public void setLogoImageExtension(String logoImageExtension) {
        this.logoImageExtension = logoImageExtension;
    }

    public String getBannerImageExtension() {
        return bannerImageExtension;
    }

    public void setBannerImageExtension(String bannerImageExtension) {
        this.bannerImageExtension = bannerImageExtension;
    }

    public String getVenueMapImage() {
        return venueMapImage;
    }

    public void setVenueMapImage(String venueMapImage) {
        this.venueMapImage = venueMapImage;
    }

    public boolean isCustomCssEnable() { return isCustomCssEnable; }

    public void setCustomCssEnable(boolean customCssEnable) { this.isCustomCssEnable = customCssEnable; }

    public String getSpeakerReg() {
        return speakerReg;
    }

    public void setSpeakerReg(String speakerReg) {
        this.speakerReg = speakerReg;
    }

    public String getExhibitorReg() {
        return exhibitorReg;
    }

    public void setExhibitorReg(String exhibitorReg) {
        this.exhibitorReg = exhibitorReg;
    }

    public boolean isEnableCaptcha() {
        return enableCaptcha;
    }

    public void setEnableCaptcha(boolean enableCaptcha) {
        this.enableCaptcha = enableCaptcha;
    }
    public boolean isAdvancedWebsiteLayout() {
        return isAdvancedWebsiteLayout;
    }
    public void setAdvancedWebsiteLayout(boolean advancedWebsiteLayout) {
        isAdvancedWebsiteLayout = advancedWebsiteLayout;
    }

    public boolean isPrintNodeEnabled() {
        return isPrintNodeEnabled;
    }

    public void setPrintNodeEnabled(boolean printNodeEnabled) {
        isPrintNodeEnabled = printNodeEnabled;
    }

    public String getPrintNodeApiKey() {
        return printNodeApiKey;
    }

    public void setPrintNodeApiKey(String printNodeApiKey) {
        this.printNodeApiKey = printNodeApiKey;
    }

    public String getRegisterButtonText() {return registerButtonText;}

    public void setRegisterButtonText(String registerButtonText) {this.registerButtonText = registerButtonText;}

    public boolean isEmbeddedCheckoutFormEnabled() {
        return embeddedCheckoutFormEnabled;
    }

    public void setEmbeddedCheckoutFormEnabled(boolean embeddedCheckoutFormEnabled) {
        this.embeddedCheckoutFormEnabled = embeddedCheckoutFormEnabled;
    }

    public String getReviewerReg() {
        return reviewerReg;
    }

    public void setReviewerReg(String reviewerReg) {
        this.reviewerReg = reviewerReg;
    }
}
