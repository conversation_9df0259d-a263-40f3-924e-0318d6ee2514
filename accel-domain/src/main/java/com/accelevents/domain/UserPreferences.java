package com.accelevents.domain;

import javax.persistence.*;

@Entity
@Table(name = "user_preferences")
public class UserPreferences {

    // this table is created to store user-level settings across the platform.

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @OneToOne(targetEntity = User.class)
    @JoinColumn(name = "user_id", nullable = false, insertable = false, updatable = false)
    private User user;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "is_hide_view_disclaimer")
    private boolean isHideViewDisclaimer;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public User getUser() { return user; }

    public void setUser(User user) { this.user = user; }

    public Long getUserId() { return userId; }

    public void setUserId(Long userId) { this.userId = userId; }

    public boolean isHideViewDisclaimer() { return isHideViewDisclaimer; }

    public void setHideViewDisclaimer(boolean hideViewDisclaimer) { isHideViewDisclaimer = hideViewDisclaimer; }
}
