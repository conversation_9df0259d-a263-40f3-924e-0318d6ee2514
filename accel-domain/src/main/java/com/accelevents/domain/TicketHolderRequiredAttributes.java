package com.accelevents.domain;

import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketAttributesDefaultRequirement;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "ticket_holder_required_attributes")
@Where(clause="rec_status<>'CANCEL' or rec_status is NULL ")
public class TicketHolderRequiredAttributes implements Serializable, Cloneable {


	/**
	 * 
	 */
	private static final long serialVersionUID = 6915814721042018536L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;

	@ManyToOne(targetEntity = Event.class)
	@JoinColumn(name = "event_id")
	private Event eventid;

	@Column(name = "attribute_name")
	private String name;

	@Column(name = "attribute_value_type")
	@Enumerated(EnumType.STRING)
	private AttributeValueType attributeValueType;

	@Column(name = "buyer_attribute_order")
	private int buyerAttributeOrder;

	@Column(name = "enabled_for_ticket_holder")
	private boolean enabledForTicketHolder;

	@Column(name = "required_for_ticket_holder")
	private boolean requiredForTicketHolder;

	@Column(name = "enabled_for_ticket_purchaser")
	private boolean enabledForTicketPurchaser;

	@Column(name = "required_for_ticket_purchaser")
	private boolean requiredForTicketPurchaser;

	@Column(name = "is_attribute")
	private boolean isAttribute;

    @Type(type = "text")
    @Column(name = "default_value_json_purchaser", columnDefinition = "LONGTEXT")
    private String defaultValueJsonPurchaser;

    @Type(type = "text")
    @Column(name = "default_value_json_holder", columnDefinition = "LONGTEXT")
    private String defaultValueJsonHolder;

	@Column(name = "hidden_for_purchaser")
	private boolean hiddenForPurchaser;

	@Column(name = "hidden_for_holder")
	private boolean hiddenForHolder;

    @Column(name = "invisible_for_ticket_purchaser")
    private boolean invisibleForPurchaser;

    @Column(name = "invisible_for_ticket_holder")
    private boolean invisibleForHolder;

	@Column(name = "is_deleted_for_holder")
	private Boolean isDeletedForHolder=false;

	@Column(name = "is_deleted_for_buyer")
	private Boolean isDeletedForBuyer=false;

	@Column(name = "holder_event_ticket_type_id", columnDefinition = "text")
	private String holderEventTicketTypeId;

    @Column(name = "holder_required_ticket_type_id", columnDefinition = "text")
    private String holderRequiredTicketTypeId;

    @Column(name = "holder_optional_ticket_type_id", columnDefinition = "text")
    private String holderOptionalTicketTypeId;

    @Column(name = "buyer_required_ticket_type_id", columnDefinition = "text")
    private String buyerRequiredTicketTypeId;

    @Column(name = "buyer_optional_ticket_type_id", columnDefinition = "text")
    private String buyerOptionalTicketTypeId;

	@Column(name = "buyer_event_ticket_type_id", columnDefinition = "text")
	private String buyerEventTicketTypeId;

	@Column(name = "recurring_event_id")
	private Long recurringEventId;

	@ManyToOne(fetch = FetchType.LAZY,targetEntity = RecurringEvents.class)
	@JoinColumn(name ="recurring_event_id" , insertable = false, updatable = false)
	private RecurringEvents recurringEvents;

	@Column(name = "created_from")
	private Long createdFrom;

    @Column(name = "holder_attribute_order")
    private int holderAttributeOrder;
	
	public TicketHolderRequiredAttributes(){
		super();
	}

	@Column(name = "rec_status")
	@Enumerated(EnumType.STRING)
	private RecordStatus status;

	@Column(name = "data_type")
	@Enumerated(EnumType.STRING)
	private DataType dataType=DataType.TICKET;

    @Column(name="selected_answer")
    private Long selectedAnswer;

    @Column(name="parent_question_id")
    private Long parentQuestionId;

    @Column(name="is_default_attribute")
    private boolean isDefaultAttribute;

    @Column(name="enabled_for_kiosk")
    private boolean isEnabledForKiosk = false;

    @Column(name="editable_for_kiosk")
    private boolean isEditableForKiosk = false;

    @Column(name="is_capacity_hidden_for_purchaser")
    private boolean isCapacityHiddenForPurchaser = true;

    @Column(name="is_capacity_hidden_for_holder")
    private boolean isCapacityHiddenForHolder = true;

    @Column(name = "hidden_for_purchaser_registration")
    private boolean hiddenForPurchaserRegistration = false;

    @Column(name = "hidden_for_holder_registration")
    private boolean hiddenForHolderRegistration = false;

    @Column(name = "hidden_registration_holder_ticket_type", columnDefinition = "text")
    private String holderRegistrationHiddenTicketTypeId;

    @Column(name = "hidden_registration_purchaser_ticket_type", columnDefinition = "text")
    private String purchaserRegistrationHiddenTicketTypeId;

    @Column(name = "buyer_description", columnDefinition = "MEDIUMTEXT")
    private String buyerDescription;

    @Column(name = "holder_description", columnDefinition = "MEDIUMTEXT")
    private String holderDescription;

    @Column(name = "holder_default_requirement")
    @Enumerated(EnumType.STRING)
    private TicketAttributesDefaultRequirement holderDefaultRequirement = TicketAttributesDefaultRequirement.REQUIRED;

    @Column(name = "buyer_default_requirement")
    @Enumerated(EnumType.STRING)
    private TicketAttributesDefaultRequirement buyerDefaultRequirement = TicketAttributesDefaultRequirement.REQUIRED;


    public RecordStatus getStatus() {
		return status;
	}

	public void setStatus(RecordStatus status) {
		this.status = status;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Event getEventid() {
		return eventid;
	}

	public void setEventid(Event eventid) {
		this.eventid = eventid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public AttributeValueType getAttributeValueType() {
		return attributeValueType;
	}

	public void setAttributeValueType(AttributeValueType attributeValueType) {
		this.attributeValueType = attributeValueType;
	}

	public int getBuyerAttributeOrder() {
		return buyerAttributeOrder;
	}

	public void setBuyerAttributeOrder(int buyerAttributeOrder) {
		this.buyerAttributeOrder = buyerAttributeOrder;
	}

	public boolean getEnabledForTicketHolder() {
		return enabledForTicketHolder;
	}

	public void setEnabledForTicketHolder(boolean enabledForTicketHolder) {
		this.enabledForTicketHolder = enabledForTicketHolder;
	}

	public boolean isAttribute() {
		return isAttribute;
	}

	public void setAttribute(boolean isAttribute) {
		this.isAttribute = isAttribute;
	}

	public boolean isHiddenForPurchaser() {
		return hiddenForPurchaser;
	}

	public void setHiddenForPurchaser(boolean hiddenForPurchaser) {
		this.hiddenForPurchaser = hiddenForPurchaser;
	}

	public boolean isHiddenForHolder() {
		return hiddenForHolder;
	}

	public void setHiddenForHolder(boolean hiddenForHolder) {
		this.hiddenForHolder = hiddenForHolder;
	}

	public Boolean getDeletedForHolder() {
		return isDeletedForHolder;
	}

	public void setDeletedForHolder(Boolean deletedForHolder) {
		isDeletedForHolder = deletedForHolder;
	}

	public Boolean getDeletedForBuyer() {
		return isDeletedForBuyer;
	}

	public void setDeletedForBuyer(Boolean deletedForBuyer) {
		isDeletedForBuyer = deletedForBuyer;
	}
	public Long getRecurringEventId() {
		return recurringEventId;
	}

	public void setRecurringEventId(Long recurringEventId) {
		this.recurringEventId = recurringEventId;
	}

	public RecurringEvents getRecurringEvents() {
		return recurringEvents;
	}

	public void setRecurringEvents(RecurringEvents recurringEvents) {
		this.recurringEvents = recurringEvents;
	}

	public Long getCreatedFrom() {
		return createdFrom;
	}

	public void setCreatedFrom(Long createdFrom) {
		this.createdFrom = createdFrom;
	}

    public int getHolderAttributeOrder() {
        return holderAttributeOrder;
    }

    public void setHolderAttributeOrder(int holderAttributeOrder) {
        this.holderAttributeOrder = holderAttributeOrder;
    }

    public Long getSelectedAnswer() {
        return selectedAnswer;
    }

    public void setSelectedAnswer(Long selectedAnswer) {
        this.selectedAnswer = selectedAnswer;
    }

    public Long getParentQuestionId() {
        return parentQuestionId;
    }

    public void setParentQuestionId(Long parentQuestionId) {
        this.parentQuestionId = parentQuestionId;
    }

    @Override
	public boolean equals(Object obj) {
		if (!(obj instanceof TicketHolderRequiredAttributes)) {
			System.out.println("Not a TicketHolderRequiredAttributes object");
			return false;
		}
		if (obj == this) {
			System.out.println("Same object.");
			// return true;
		}
		TicketHolderRequiredAttributes rhs = (TicketHolderRequiredAttributes) obj;

		return rhs.getName() != null && rhs.getName() != "" && name != null && name != "" && rhs.getName() == name;
	}

	@Override
	public Object clone() {
		try {
			return super.clone();
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
			return null;
		}
	}

	public DataType getDataType() {
		return dataType;
	}

	public void setDataType(DataType dataType) {
		this.dataType = dataType;
	}

    public boolean isDefaultAttribute() {
        return isDefaultAttribute;
    }

    public void setDefaultAttribute(boolean defaultAttribute) {
        isDefaultAttribute = defaultAttribute;
    }
    
    public boolean isInvisibleForPurchaser() {
        return invisibleForPurchaser;
    }

    public void setInvisibleForPurchaser(boolean invisibleForPurchaser) {
        this.invisibleForPurchaser = invisibleForPurchaser;
    }

    public boolean isInvisibleForHolder() {
        return invisibleForHolder;
    }

    public void setInvisibleForHolder(boolean invisibleForHolder) {
        this.invisibleForHolder = invisibleForHolder;
    }

    public String getDefaultValueJsonPurchaser() {
        return defaultValueJsonPurchaser;
    }

    public void setDefaultValueJsonPurchaser(String defaultValueJsonPurchaser) {
        this.defaultValueJsonPurchaser = defaultValueJsonPurchaser;
    }

    public String getDefaultValueJsonHolder() {
        return defaultValueJsonHolder;
    }

    public void setDefaultValueJsonHolder(String defaultValueJsonHolder) {
        this.defaultValueJsonHolder = defaultValueJsonHolder;
    }

    public boolean isEnabledForKiosk() {
        return isEnabledForKiosk;
    }

    public void setEnabledForKiosk(boolean enabledForKiosk) {
        isEnabledForKiosk = enabledForKiosk;
    }

    public boolean isEditableForKiosk() {
        return isEditableForKiosk;
    }

    public void setEditableForKiosk(boolean editableForKiosk) {
        isEditableForKiosk = editableForKiosk;
    }

    public boolean isCapacityHiddenForPurchaser() {
        return isCapacityHiddenForPurchaser;
    }

    public void setCapacityHiddenForPurchaser(boolean capacityHiddenForPurchaser) {
        isCapacityHiddenForPurchaser = capacityHiddenForPurchaser;
    }

    public boolean isCapacityHiddenForHolder() {
        return isCapacityHiddenForHolder;
    }

    public void setCapacityHiddenForHolder(boolean capacityHiddenForHolder) {
        isCapacityHiddenForHolder = capacityHiddenForHolder;
    }

    public String getHolderRequiredTicketTypeId() {
        return holderRequiredTicketTypeId;
    }

    public void setHolderRequiredTicketTypeId(String holderRequiredTicketTypeId) {
        this.holderRequiredTicketTypeId = holderRequiredTicketTypeId;
    }

    public String getHolderOptionalTicketTypeId() {
        return holderOptionalTicketTypeId;
    }

    public void setHolderOptionalTicketTypeId(String holderOptionalTicketTypeId) {
        this.holderOptionalTicketTypeId = holderOptionalTicketTypeId;
    }

    public String getBuyerRequiredTicketTypeId() {
        return buyerRequiredTicketTypeId;
    }

    public void setBuyerRequiredTicketTypeId(String buyerRequiredTicketTypeId) {
        this.buyerRequiredTicketTypeId = buyerRequiredTicketTypeId;
    }

    public String getBuyerOptionalTicketTypeId() {
        return buyerOptionalTicketTypeId;
    }

    public void setBuyerOptionalTicketTypeId(String buyerOptionalTicketTypeId) {
        this.buyerOptionalTicketTypeId = buyerOptionalTicketTypeId;
    }

    public void setEnabledForTicketPurchaser(boolean enabledForTicketPurchaser) {
        this.enabledForTicketPurchaser = enabledForTicketPurchaser;
    }

    public boolean getEnabledForTicketPurchaser() {
        return enabledForTicketPurchaser;
    }

    public boolean getRequiredForTicketHolder() {
        return requiredForTicketHolder;
    }

    public void setRequiredForTicketHolder(boolean requiredForTicketHolder) {
        this.requiredForTicketHolder = requiredForTicketHolder;
    }

    public boolean getRequiredForTicketPurchaser() {
        return requiredForTicketPurchaser;
    }

    public void setRequiredForTicketPurchaser(boolean requiredForTicketPurchaser) {
        this.requiredForTicketPurchaser = requiredForTicketPurchaser;
    }

    public String getHolderEventTicketTypeId() {
        return holderEventTicketTypeId;
    }

    public void setHolderEventTicketTypeId(String holderEventTicketTypeId) {
        this.holderEventTicketTypeId = holderEventTicketTypeId;
    }

    public String getBuyerEventTicketTypeId() {
        return buyerEventTicketTypeId;
    }

    public void setBuyerEventTicketTypeId(String buyerEventTicketTypeId) {
        this.buyerEventTicketTypeId = buyerEventTicketTypeId;
    }

    public boolean isHiddenForHolderRegistration() {
        return hiddenForHolderRegistration;
    }

    public void setHiddenForHolderRegistration(boolean hiddenForHolderRegistration) {
        this.hiddenForHolderRegistration = hiddenForHolderRegistration;
    }

    public String getHolderRegistrationHiddenTicketTypeId() {
        return holderRegistrationHiddenTicketTypeId;
    }

    public void setHolderRegistrationHiddenTicketTypeId(String holderRegistrationHiddenTicketTypeId) {
        this.holderRegistrationHiddenTicketTypeId = holderRegistrationHiddenTicketTypeId;
    }

    public boolean isHiddenForPurchaserRegistration() {
        return hiddenForPurchaserRegistration;
    }

    public void setHiddenForPurchaserRegistration(boolean hiddenForPurchaserRegistration) {
        this.hiddenForPurchaserRegistration = hiddenForPurchaserRegistration;
    }

    public String getPurchaserRegistrationHiddenTicketTypeId() {
        return purchaserRegistrationHiddenTicketTypeId;
    }

    public void setPurchaserRegistrationHiddenTicketTypeId(String purchaserRegistrationHiddenTicketTypeId) {
        this.purchaserRegistrationHiddenTicketTypeId = purchaserRegistrationHiddenTicketTypeId;
    }

    public String getBuyerDescription() {
        return buyerDescription;
    }

    public void setBuyerDescription(String buyerDescription) {
        this.buyerDescription = buyerDescription;
    }

    public String getHolderDescription() {
        return holderDescription;
    }

    public void setHolderDescription(String holderDescription) {
        this.holderDescription = holderDescription;
    }

    public TicketAttributesDefaultRequirement getHolderDefaultRequirement() {
        return holderDefaultRequirement;
    }

    public void setHolderDefaultRequirement(TicketAttributesDefaultRequirement holderDefaultRequirement) {
        this.holderDefaultRequirement = holderDefaultRequirement;
    }

    public TicketAttributesDefaultRequirement getBuyerDefaultRequirement() {
        return buyerDefaultRequirement;
    }

    public void setBuyerDefaultRequirement(TicketAttributesDefaultRequirement buyerDefaultRequirement) {
        this.buyerDefaultRequirement = buyerDefaultRequirement;
    }

    @Override
    public String toString() {
        return "TicketHolderRequiredAttributes{" +
                "id=" + id +
                ", eventid=" + eventid +
                ", name='" + name + '\'' +
                ", attributeValueType=" + attributeValueType +
                ", buyerAttributeOrder=" + buyerAttributeOrder +
                ", enabledForTicketHolder=" + enabledForTicketHolder +
                ", requiredForTicketHolder=" + requiredForTicketHolder +
                ", enabledForTicketPurchaser=" + enabledForTicketPurchaser +
                ", requiredForTicketPurchaser=" + requiredForTicketPurchaser +
                ", isAttribute=" + isAttribute +
                ", defaultValueJsonPurchaser='" + defaultValueJsonPurchaser + '\'' +
                ", defaultValueJsonHolder='" + defaultValueJsonHolder + '\'' +
                ", hiddenForPurchaser=" + hiddenForPurchaser +
                ", hiddenForHolder=" + hiddenForHolder +
                ", invisibleForPurchaser=" + invisibleForPurchaser +
                ", invisibleForHolder=" + invisibleForHolder +
                ", isDeletedForHolder=" + isDeletedForHolder +
                ", isDeletedForBuyer=" + isDeletedForBuyer +
                ", holderEventTicketTypeId='" + holderEventTicketTypeId + '\'' +
                ", holderRequiredTicketTypeId='" + holderRequiredTicketTypeId + '\'' +
                ", holderOptionalTicketTypeId='" + holderOptionalTicketTypeId + '\'' +
                ", buyerRequiredTicketTypeId='" + buyerRequiredTicketTypeId + '\'' +
                ", buyerOptionalTicketTypeId='" + buyerOptionalTicketTypeId + '\'' +
                ", buyerEventTicketTypeId='" + buyerEventTicketTypeId + '\'' +
                ", recurringEventId=" + recurringEventId +
                ", recurringEvents=" + recurringEvents +
                ", createdFrom=" + createdFrom +
                ", holderAttributeOrder=" + holderAttributeOrder +
                ", status=" + status +
                ", dataType=" + dataType +
                ", selectedAnswer=" + selectedAnswer +
                ", parentQuestionId=" + parentQuestionId +
                ", isDefaultAttribute=" + isDefaultAttribute +
                ", isEnabledForKiosk=" + isEnabledForKiosk +
                ", isEditableForKiosk=" + isEditableForKiosk +
                ", isCapacityHiddenForPurchaser=" + isCapacityHiddenForPurchaser +
                ", isCapacityHiddenForHolder=" + isCapacityHiddenForHolder +
                ", hiddenForPurchaserRegistration=" + hiddenForPurchaserRegistration +
                ", purchaserRegistrationHiddenTicketTypeId=" + purchaserRegistrationHiddenTicketTypeId +
                ", hiddenForHolderRegistration=" + hiddenForHolderRegistration +
                ", holderRegistrationHiddenTicketTypeId=" + holderRegistrationHiddenTicketTypeId +
                ", buyerDescription='" + buyerDescription + '\'' +
                ", holderDescription='" + holderDescription + '\'' +
                ", holderDefaultRequirement=" + holderDefaultRequirement +
                ", buyerDefaultRequirement=" + buyerDefaultRequirement +
                '}';
    }
}