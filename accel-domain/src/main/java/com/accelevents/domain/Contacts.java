package com.accelevents.domain;

import com.accelevents.auction.dto.BaseContactDto;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.CustomFormAttributeData;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "contacts")
@Where(clause="rec_status<>'DELETE'")
public class Contacts implements Cloneable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private long id;

    @JoinColumn(name = "event_id", nullable = false)
    private long eventId;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "email")
    private String email;

    @Enumerated(EnumType.STRING)
    @Column(name = "country_code", nullable = true, length = 5)
    private CountryCode countryCode;

    @Column(name = "phone_number", nullable = true)
    private long phoneNumber;

    @Column(name = "created_date", nullable = true)
    private Date createdDate;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recStatus=RecordStatus.CREATE;

    @Column(name = "contact_attribute")
    private Long contactAttributeId;

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = CustomFormAttributeData.class)
    @JoinColumn(name = "contact_attribute", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private CustomFormAttributeData contactAttribute;

    public Contacts() {
    }
    public Contacts(long eventId,String email) {
        this.eventId = eventId;
        this.email = email;
        this.createdDate = new Date();
    }

    public Contacts(long eventId, String email, BaseContactDto baseContactDto) {
        this.eventId = eventId;
        this.email = email;
        if (baseContactDto != null) {
            this.firstName = baseContactDto.getFirstName();
            this.lastName = baseContactDto.getLastName();
        }
        this.createdDate = new Date();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getEventId() {
        return eventId;
    }

    public void setEventId(long eventId) {
        this.eventId = eventId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        if (null != email) {
            email = email.trim();
        }
        this.email = email;
    }

    public CountryCode getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(CountryCode countryCode) {
        this.countryCode = countryCode;
    }

    public long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public RecordStatus getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(RecordStatus recStatus) {
        this.recStatus = recStatus;
    }

    public Long getContactAttributeId() {
        return contactAttributeId;
    }

    public void setContactAttributeId(Long contactAttributeId) {
        this.contactAttributeId = contactAttributeId;
    }

    public CustomFormAttributeData getContactAttribute() {
        return contactAttribute;
    }

    public void setContactAttribute(CustomFormAttributeData contactAttribute) {
        this.contactAttribute = contactAttribute;
    }

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;
        }
    }
}
