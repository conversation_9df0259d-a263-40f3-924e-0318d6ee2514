package com.accelevents.domain;

import com.accelevents.domain.enums.RecordStatus;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "private_event")
@Where(clause="rec_status<>'DELETE'")
@Setter
@Getter
public class PrivateEvent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @OneToOne(fetch = FetchType.LAZY,targetEntity = Event.class)
    @JoinColumn(name = "event_id", insertable = false, updatable = false)
    private Event event;

    @Column(name = "event_id", nullable = false)
    private Long eventId;

    @Column(name = "is_password_required")
    private  boolean isPasswordRequired=false;

    @ManyToOne
    @JoinColumn(name = "updated_by")
    private User updatedBy;

    @Column(name = "updated_at")
    @UpdateTimestamp
    private Date updatedAt;

    @Column(name="password")
    private String password;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus;
}