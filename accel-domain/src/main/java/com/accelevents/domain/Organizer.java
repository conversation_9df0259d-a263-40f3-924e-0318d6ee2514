package com.accelevents.domain;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.enums.BillingType;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import static com.accelevents.domain.enums.RecordStatus.CREATE;

@Entity
@Table(name = "organizers")
@Where(clause="rec_status<>'DELETE'")
public class Organizer extends ChargeBeeDetails implements Serializable {

	private static final long serialVersionUID = 8907344625495494873L;

	@Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long Id;

    @NotNull(message = "Organizer Name Cannot Be Empty.")
    @Column(name = "name")
    private String name;

    @NotNull(message = "Organizer URL cannot be Empty")
    @Column(name = "organizer_page_URL", unique = true, nullable = false)
    private String organizerPageURL;

    @Column(name = "organizer_description")
    @Type(type = "text")
    private String organizerDescription;

    @Column(name = "logo_image")
    private String logoImage;

    @Column(name = "facebook_link")
    private String facebookLink;

    @Column(name = "twitter_link")
    private String twitterLink;

    @Column(name = "linkedin_link")
    private String linkedInLink;

    @Column(name = "background_color")
    private String backgroundColor;

    @Column(name = "text_color")
    private String textColor;

    @Column(name = "website")
    private String website;

    @Column(name = "contact_email_address")
    private String contactEmailAddress;

    @ManyToOne
    @JoinColumn(name = "created_by", nullable = false)
    private User createdBy;

    @ManyToOne(targetEntity = WhiteLabel.class)
    @JoinColumn(name = "white_label")
    private WhiteLabel whiteLabel;

    @Column(name = "allow_social_sharing")
    private boolean allowSocialSharing = true;

    @Column(name = "waive_off_attendees_upload_fee")
    private boolean waiveOffAttendeesUploadFee = true;

    @Column(name = "billing_type")
    @Enumerated(EnumType.STRING)
    private BillingType billingType = BillingType.Paid;

    @Column(name = "free_quantity")
    private Integer freeQuantity;

    @Column(name = "is_auto_billing")
    private boolean isAutoBilling = true;

    @Column(name = "overage_charge")
    private Double overageCharge = 3.0;

    @Column(name = "attendee_upload_charge")
    private Double attendeeUploadCharge = 1.0;

    @Column(name = "expo_booth_charge")
    private Double expoBoothCharge = 99.0;

    @Transient
    private String oldOrganizerPageURL;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus= CREATE;

    @Column(name = "hs_company_id")
    private Long hubSpotCompanyId;

    @Column(name = "is_2FA_required")
    private boolean twoFactorRequired = false;

    @Column(name = "engage_emails_limit")
    private int engageEmailsLimit=0;

    @Column(name = "is_secure_last_name_on_hub_side", nullable = false)
    private boolean isSecureLastNameOnHubSide = false;

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public long getId() {
        return Id;
    }

    public void setId(long id) {
        Id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrganizerPageURL() {
        return organizerPageURL;
    }

    public void setOrganizerPageURL(String organizerPageURL) {
        this.organizerPageURL = organizerPageURL;
    }

    public String getOrganizerDescription() {
        return organizerDescription;
    }

    public void setOrganizerDescription(String organizerDescription) {
        this.organizerDescription = organizerDescription;
    }

    public String getLogoImage() {
        return logoImage;
    }

    public void setLogoImage(String logoImage) {
        this.logoImage = logoImage;
    }

    public String getFacebookLink() {
        return facebookLink;
    }

    public void setFacebookLink(String facebookLink) {
        this.facebookLink = facebookLink;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getContactEmailAddress() {
        return contactEmailAddress;
    }

    public void setContactEmailAddress(String contactEmailAddress) {
        if(null != contactEmailAddress){
            contactEmailAddress = contactEmailAddress.trim();
        }
        this.contactEmailAddress = contactEmailAddress;
    }

    public User getCreatedBy() {return createdBy;}

    public void setCreatedBy(User createdBy) {this.createdBy = createdBy;}

    public WhiteLabel getWhiteLabel() {
        return whiteLabel;
    }

    public void setWhiteLabel(WhiteLabel whiteLabel) {
        this.whiteLabel = whiteLabel;
    }

    public String getTwitterLink() {
        return twitterLink;
    }

    public void setTwitterLink(String twitterLink) {
        this.twitterLink = twitterLink;
    }

    public String getLinkedInLink() {
        return linkedInLink;
    }

    public void setLinkedInLink(String linkedInLink) {
        this.linkedInLink = linkedInLink;
    }

    public boolean isAllowSocialSharing() {
        return allowSocialSharing;
    }

    public void setAllowSocialSharing(boolean allowSocialSharing) {
        this.allowSocialSharing = allowSocialSharing;
    }

    public boolean isWaiveOffAttendeesUploadFee() {
        return waiveOffAttendeesUploadFee;
    }

    public void setWaiveOffAttendeesUploadFee(boolean waiveOffAttendeesUploadFee) {
        this.waiveOffAttendeesUploadFee = waiveOffAttendeesUploadFee;
    }

    public BillingType getBillingType() {
        return billingType;
    }

    public void setBillingType(BillingType billingType) {
        this.billingType = billingType;
    }

    public Integer getFreeQuantity() { return freeQuantity; }

    public void setFreeQuantity(Integer freeQuantity) { this.freeQuantity = freeQuantity; }

    public boolean isAutoBilling() {
        return isAutoBilling;
    }

    public void setAutoBilling(boolean autoBilling) {
        isAutoBilling = autoBilling;
    }

    public Double getOverageCharge() {
        return overageCharge;
    }

    public void setOverageCharge(Double overageCharge) {
        this.overageCharge = overageCharge;
    }

    public Double getAttendeeUploadCharge() { return attendeeUploadCharge; }

    public void setAttendeeUploadCharge(Double attendeeUploadCharge) { this.attendeeUploadCharge = attendeeUploadCharge; }

    public Double getExpoBoothCharge() { return expoBoothCharge; }

    public void setExpoBoothCharge(Double expoBoothCharge) { this.expoBoothCharge = expoBoothCharge; }

    public String getOldOrganizerPageURL() {
        return oldOrganizerPageURL;
    }

    public void setOldOrganizerPageURL(String oldOrganizerPageURL) {
        this.oldOrganizerPageURL = oldOrganizerPageURL;
    }

    public RecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    public Long getHubSpotCompanyId() {
        return hubSpotCompanyId;
    }

    public void setHubSpotCompanyId(Long hubSpotCompanyId) {
        this.hubSpotCompanyId = hubSpotCompanyId;
    }

    public boolean isTwoFactorRequired() {
        return twoFactorRequired;
    }

    public void setTwoFactorRequired(boolean twoFactorRequired) {
        this.twoFactorRequired = twoFactorRequired;
    }

    public int getEngageEmailsLimit() {
        return engageEmailsLimit;
    }

    public void setEngageEmailsLimit(int engageEmailsLimit) {
        this.engageEmailsLimit = engageEmailsLimit;
    }

    public boolean isSecureLastNameOnHubSide() {
        return isSecureLastNameOnHubSide;
    }

    public void setSecureLastNameOnHubSide(boolean secureLastNameOnHubSide) {
        isSecureLastNameOnHubSide = secureLastNameOnHubSide;
    }
}
