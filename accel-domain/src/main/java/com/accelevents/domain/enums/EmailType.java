package com.accelevents.domain.enums;

import java.util.Map;

public enum EmailType {
    EMAIL_INVITATIONS, WAIT_LIST_RELEASE, ATTENDEE_CHECKIN, ORDER_CONFIRMATION, <PERSON><PERSON>C_LINK_CODE, RESET_PASSWORD, OR<PERSON><PERSON>ZER_TEAM_ACCESS, STAFF_ACCESS, WHITE_LABEL_NEW_ADMIN, REQUEST_NOTIFICATION, PARTIC<PERSON>ANT_QUESTION, SUPPORT_QUESTION, CART_ABANDONMENT, MEETING_NOTIFICATION, BUYER_RECEIPT_AUCTION, EVENT_TICKET_REFUND, R<PERSON><PERSON>DE<PERSON>, ADMIN_ORDER_CONFIRMATION_EMAIL,
    R<PERSON>IS<PERSON>ATION_EMAIL_PENDING_ATTENDEE, R<PERSON><PERSON><PERSON><PERSON>ION_EMAIL_PENDING_SPEAKER, REGISTRATION_EMAIL_PENDING_EXPO,
    REGISTRATION_EMAIL_DENIED_ATTENDEE, REGISTRATION_EMAIL_DENIED_SPEAKER, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_EMAIL_DENIED_EXPO,
    R<PERSON><PERSON><PERSON><PERSON>ION_EMAIL_APPROVED_ATTENDEE, REGISTRATION_EMAIL_APPROVED_SPEAKER, REGISTRATION_EMAIL_APPROVED_EXPO,
    REGISTRATION_EMAIL_WAITLIST_ATTENDEE, REGISTRATION_EMAIL_WAITLIST_SPEAKER, REGISTRATION_EMAIL_WAITLIST_EXPO, SESSION_SAVE_A_SEAT_CONFIRMATION_EMAIL,
    REGISTRATION_EMAIL_DEFAULT_REVIEWER, REGISTRATION_EMAIL_PENDING_REVIEWER, REGISTRATION_EMAIL_DENIED_REVIEWER, REGISTRATION_EMAIL_APPROVED_REVIEWER,
    REGISTRATION_EMAIL_WAITLIST_REVIEWER;

    private static final Map<String, EmailType> REVIEWER_EMAIL_TYPE_STATUSES = Map.of("PENDING", REGISTRATION_EMAIL_PENDING_REVIEWER,
            "DENIED", REGISTRATION_EMAIL_DENIED_REVIEWER,
            "WAITLISTED", REGISTRATION_EMAIL_WAITLIST_REVIEWER,
            "APPROVED", REGISTRATION_EMAIL_APPROVED_REVIEWER,
            "DEFAULT", REGISTRATION_EMAIL_DEFAULT_REVIEWER);

    private static final Map<String, EmailType> EXHIBITOR_EMAIL_TYPE_STATUSES = Map.of("PENDING", REGISTRATION_EMAIL_PENDING_EXPO,
            "DENIED", REGISTRATION_EMAIL_DENIED_EXPO,
            "WAITLISTED", REGISTRATION_EMAIL_WAITLIST_EXPO,
            "APPROVED", REGISTRATION_EMAIL_APPROVED_EXPO);

    private static final Map<String, EmailType> SPEAKER_EMAIL_TYPE_STATUSES = Map.of("PENDING", REGISTRATION_EMAIL_PENDING_SPEAKER,
            "DENIED", REGISTRATION_EMAIL_DENIED_SPEAKER,
            "WAITLISTED", REGISTRATION_EMAIL_WAITLIST_SPEAKER,
            "APPROVED", REGISTRATION_EMAIL_APPROVED_SPEAKER);

    private static final Map<String, EmailType> ATTENDEE_EMAIL_TYPE_STATUSES = Map.of("PENDING", REGISTRATION_EMAIL_PENDING_ATTENDEE,
            "DENIED", REGISTRATION_EMAIL_DENIED_ATTENDEE,
            "WAITLISTED", REGISTRATION_EMAIL_WAITLIST_ATTENDEE,
            "APPROVED", REGISTRATION_EMAIL_APPROVED_ATTENDEE);

    public static EmailType getReviewerStatus(String status) {

        return REVIEWER_EMAIL_TYPE_STATUSES.get(status);
    }

    public static EmailType getExhibitorStatus(String status) {

        return EXHIBITOR_EMAIL_TYPE_STATUSES.get(status);
    }

    public static EmailType getSpeakerStatus(String status) {

        return SPEAKER_EMAIL_TYPE_STATUSES.get(status);
    }

    public static EmailType getAttendeeStatus(String status) {

        return ATTENDEE_EMAIL_TYPE_STATUSES.get(status);
    }
}
