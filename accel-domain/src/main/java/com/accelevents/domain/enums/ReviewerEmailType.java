package com.accelevents.domain.enums;

import java.util.List;
import java.util.stream.Collectors;

public enum ReviewerEmailType {
    DEFAULT, APPROVED, PENDING, WAITLISTED, DENIED;

    public static List<ReviewerEmailType> getList() {
        return List.of(ReviewerEmailType.values());
    }

    public static List<ReviewerEmailType> remainingTypes(List<ReviewerEmailType> existingTypes) {

        return getList().stream()
                .filter(type -> !existingTypes.contains(type))
                .collect(Collectors.toList());
    }
}
