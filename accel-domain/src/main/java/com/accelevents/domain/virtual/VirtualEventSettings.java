package com.accelevents.domain.virtual;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Rtmp;
import org.hibernate.annotations.Where;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "virtual_event_settings")
@Where(clause="rec_status<>'DELETE' or rec_status is NULL ")
public class VirtualEventSettings extends Rtmp implements Cloneable, Serializable {

    private static final long serialVersionUID = 7234648087943923469L;

    private static final Logger log = LoggerFactory.getLogger(VirtualEventSettings.class);

    public VirtualEventSettings(){
        setWorkshopEnabled(true);
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private long id;

    @Column(name = "event_id")
    private Long eventId;

    @ManyToOne(fetch = FetchType.LAZY,targetEntity = Event.class)
    @JoinColumn(name = "event_id", insertable = false, updatable = false)
    private Event event;

    @Column(name = "is_accept_direct_messages")
    private boolean acceptDirectMessages=true;

    @Column(name = "is_stage_enabled")
    private boolean isStageEnabled;

    @Column(name = "is_session_enabled")
    private boolean isSessionEnabled;

    @Column(name = "is_networking_enabled")
    private boolean isNetworkingEnabled;

    @Column(name = "is_expo_enabled")
    private boolean isExpoEnabled;

    @Column(name = "is_pronouns_field_enabled")
    private boolean isPronounsFieldEnabled = false;

    @Column(name = "is_feed_enabled")
    private  boolean isFeedEnabled;

    @Column(name = "enable_people_page")
    private boolean isEnablePeoplePage = true;

    @Column(name = "subscription_item_id")
    private String subscriptionItemId;

    @Column(name = "customer_id")
    private String customerId;

    @Column(name = "hub_navigation_background_color", length = 8)
    private String hubNavigationBackgroundColor = "#406AE8";

    @Column(name = "hub_navigation_text_color", length = 8)
    private String hubNavigationTextColor = "#FFFFFF";

    @Column(name = "hub_main_background_color ", length = 8)
    private String hubMainBackgroundColor  = "#F7F7FA";

    @Column(name = "card_id")
    private String cardId;

    @Column(name = "allow_attendee_post_feed")
    private  boolean allowAttendeeToPostInFeed = false;

    @Column(name = "allow_attendee_comment_feed")
    private boolean allowAttendeeToCommentInFeed = true;

    @Column(name = "welcome_message", columnDefinition = "mediumtext")
    private String welcomeMessage;

    @Column(name = "welcome_message_video")
    private String welcomeMessageVideo;

    @Column(name = "welcome_message_video_s3key")
    private String welcomeMessageVideoS3Key;

    @Column(name = "is_menu_cta_enabled")
    private boolean isMenuCTAEnabled = true;

    @Column(name = "menu_cta_URL")
    private String menuCTAUrl;

    @Column(name = "menu_cta_image")
    private String menuCTAImage;

    @Column(name = "is_workshop_enabled")
    private boolean isWorkshopEnabled  = false;

    @Column(name = "virtual_tabs", columnDefinition = "LONGTEXT")
    private String virtualTabs;

    @Column(name = "number_of_live_streaming_exhibitors_purchased")
    private Long numberOfLiveStreamingExhibitorsPurchased = 0L;

    @Column(name = "max_number_of_free_attendees")
    private Long maxNumberOfFreeAttendees = 0L;

    @Column(name = "custom_tab", columnDefinition = "LONGTEXT")
    private String customTab;

    @Column(name = "enable_speaker_invite")
    private boolean isSpeakerInviteEnable = true;

    @Column(name="expo_background_image")
    private String expoBackgroundImage;

    @Column(name = "virtual_labels", columnDefinition = "LONGTEXT")
    private String virtualLabels;

    @Column(name = "simultaneous_session_scroll")
    private boolean simultaneousSessionScroll = true;

    @Column(name = "session_notification")
    private boolean sessionNotification = true;

    @Column(name="is_enabled_lounges")
    private boolean isEnabledLounges = true;

    @Column(name="gamification_new_flow")
    private boolean gamificationNewFlow = false;

    @Column(name = "button_text_color_configuration", columnDefinition = "LONGTEXT")
    private String buttonTextColorConfiguration;

    @Column(name="lobby_banner_image")
    private String lobbyBannerImage;

    @Column(name = "max_agenda_items")
    private Integer maxAgendaItems = 10;

    @Column(name = "customize_speaker_invite_email", columnDefinition = "LONGTEXT")
    private String customizeSpeakerInviteEmail;

    @Column(name="is_hide_lobby_banner")
    private boolean hideLobbyBanner = false;

    @Column(name="add_no_follow")
    private boolean addNoFollow = false;

    @Column(name = "book_mark_button_color")
    private String bookMarkButtonColor;

    @Column(name = "allow_interest_to_attendee")
    private boolean allowInterestToAttendee = true;

    @Column(name="allow_attendee_to_schedule_meeting")
    private boolean allowAttendeeToScheduleMeeting = true;

    @Column(name = "allow_late_join")
    private boolean allowLateJoin  = true;

    @Column(name="is_lobby_live_stream")
    private boolean isLobbyLiveStream;

    @Column(name="session_waiting_image")
    private String sessionWaitingImage;

    @Column(name="expo_banner_image")
    private String expoBannerImage;

    @Column(name = "label_language_code")
    private String labelLanguageCode;

    @Column(name = "is_hide_expo_banner_image")
    private boolean hideExpoBannerImage = false;

    @Column(name = "rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus;

    @Column(name = "wants_to_learn",columnDefinition = "LONGTEXT")
    private String wantsToLearn;

    @Column(name = "is_menu_cta_new_tab")
    private boolean isMenuCTANewTab = true;

    @Column(name = "right_sidebar_config", columnDefinition = "LONGTEXT")
    private String rightSidebarConfig;

    @Column(name = "show_speaker_summery")
    private boolean showSpeakerSummery = true;

    @Column(name = "theme")
    @Enumerated(EnumType.STRING)
    private Theme theme;

    @Column(name = "advanced_color_settings")
    private boolean advancedColorSettings ;

    @Column(name = "event_hub_disclaimer")
    private boolean eventHubDisclaimer;

    @Column(name = "allow_event_hub_disagree_disclaimer_confirmation")
    private boolean allowEventHubDisagreeDisclaimerConfirmation;

    @Column(name = "custom_event_hub_disclaimer", columnDefinition = "TEXT")
    private String customEventHubDisclaimer;

    @Column(name = "speaker_registration_approval")
    private boolean speakerRegistrationApproval;

    @Column(name = "speaker_registration_submission")
    private boolean speakerRegistrationSubmission;

    @Column(name = "primary_theme_color", length = 8)
    private String primaryThemeColor;

    @Column(name = "exhibitor_registration_approval")
    private boolean exhibitorRegistrationApproval;

    @Column(name = "exhibitor_registration_submission")
    private boolean exhibitorRegistrationSubmission;

    @Column(name = "speaker_registration_auto_send_mail", nullable = false)
    @Enumerated(value = EnumType.STRING)
    private AutoSendEmail speakerRegistrationAutoSendMail = AutoSendEmail.NOT_SELECTED_NO;

    @Column(name = "exhibitor_registration_auto_send_mail", nullable = false)
    @Enumerated(value = EnumType.STRING)
    private AutoSendEmail exhibitorRegistrationAutoSendMail = AutoSendEmail.NOT_SELECTED_NO;

    @Column(name = "speaker_widget_layout", nullable = false)
    @Enumerated(value = EnumType.STRING)
    private SpeakerWidgetLayoutEnum speakerWidgetLayout = SpeakerWidgetLayoutEnum.VERTICAL;

    @Column(name = "speaker_widget_show_social_links")
    private boolean speakerWidgetShowSocialLinks = true;

    @Column(name = "speaker_widget_highlight_specific_speaker")
    private boolean speakerWidgetHighlightSpecificSpeaker = false;

    @Column(name = "is_show_count_of_attending")
    private boolean isShowCountOfAttending = true;

    @Column(name = "speaker_widget_profile_size", nullable = false)
    @Enumerated(value = EnumType.STRING)
    private SpeakerWidgetProfileSizeEnum speakerWidgetProfileSize = SpeakerWidgetProfileSizeEnum.REGULAR;

    @Column(name = "allow_session_bookmark_capacity")
    private boolean allowSessionBookmarkCapacity = false;

    @Column(name = "holder_info_visible_to_attendees")
        private boolean holderInfoVisibleToAttendees = false;

    @Column(name = "is_show_full_agenda_tab")
    private boolean isShowFullAgendaTab;

    @Column(name = "show_bookmark_button")
    private boolean showBookmarkButton = true;

    @Column(name = "enable_session_waitlist_capacity_reached")
    private boolean enableSessionWaitlistCapacityReached = false;

    public String getCustomTab() {
        return customTab;
    }

    public void setCustomTab(String customTab) {
        this.customTab = customTab;
    }

    public Long getNumberOfLiveStreamingExhibitorsPurchased() {
        return numberOfLiveStreamingExhibitorsPurchased!=null?numberOfLiveStreamingExhibitorsPurchased:0L;
    }

    public void setNumberOfLiveStreamingExhibitorsPurchased(Long numberOfLiveStreamingExhibitorsPurchased) {
        this.numberOfLiveStreamingExhibitorsPurchased = numberOfLiveStreamingExhibitorsPurchased;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public boolean isStageEnabled() {
        return isStageEnabled;
    }

    public void setStageEnabled(boolean stageEnabled) {
        isStageEnabled = stageEnabled;
    }

    public boolean isAcceptDirectMessages() {
        return acceptDirectMessages;
    }

    public void setAcceptDirectMessages(boolean acceptDirectMessages) {
        this.acceptDirectMessages = acceptDirectMessages;
    }

    public boolean isSessionEnabled() {
        return isSessionEnabled;
    }

    public void setSessionEnabled(boolean sessionEnabled) {
        isSessionEnabled = sessionEnabled;
    }

    public boolean isNetworkingEnabled() {
        return isNetworkingEnabled;
    }

    public void setNetworkingEnabled(boolean networkingEnabled) {
        isNetworkingEnabled = networkingEnabled;
    }

    public boolean isExpoEnabled() {
        return isExpoEnabled;
    }

    public void setExpoEnabled(boolean expoEnabled) {
        isExpoEnabled = expoEnabled;
    }

    public boolean isPronounsFieldEnabled() {
        return isPronounsFieldEnabled;
    }

    public void setPronounsFieldEnabled(boolean pronounsFieldEnabled) {
        isPronounsFieldEnabled = pronounsFieldEnabled;
    }

    public boolean isFeedEnabled() {
        return isFeedEnabled;
    }

    public void setFeedEnabled(boolean feedEnabled) {
        isFeedEnabled = feedEnabled;
    }

    public boolean isEnablePeoplePage() {
        return isEnablePeoplePage;
    }

    public void setEnablePeoplePage(boolean enablePeoplePage) {
        isEnablePeoplePage = enablePeoplePage;
    }

    public String getSubscriptionItemId() {
        return subscriptionItemId;
    }

    public void setSubscriptionItemId(String subscriptionItemId) {
        this.subscriptionItemId = subscriptionItemId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getHubNavigationBackgroundColor() {
        return hubNavigationBackgroundColor;
    }

    public void setHubNavigationBackgroundColor(String hubNavigationBackgroundColor) {
        this.hubNavigationBackgroundColor = hubNavigationBackgroundColor;
    }

    public String getHubNavigationTextColor() {
        return hubNavigationTextColor;
    }

    public void setHubNavigationTextColor(String hubNavigationTextColor) {
        this.hubNavigationTextColor = hubNavigationTextColor;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public boolean isAllowAttendeeToPostInFeed() {
        return allowAttendeeToPostInFeed;
    }

    public void setAllowAttendeeToPostInFeed(boolean allowAttendeeToPostInFeed) {
        this.allowAttendeeToPostInFeed = allowAttendeeToPostInFeed;
    }

    public boolean isAllowAttendeeToCommentInFeed() {
        return allowAttendeeToCommentInFeed;
    }

    public void setAllowAttendeeToCommentInFeed(boolean allowAttendeeToCommentInFeed) {
        this.allowAttendeeToCommentInFeed = allowAttendeeToCommentInFeed;
    }

    public String getWelcomeMessage() {
        return welcomeMessage;
    }

    public void setWelcomeMessage(String welcomeMessage) {
        this.welcomeMessage = welcomeMessage;
    }

    public String getWelcomeMessageVideo() {
        return welcomeMessageVideo;
    }

    public void setWelcomeMessageVideo(String welcomeMessageVideo) {
        this.welcomeMessageVideo = welcomeMessageVideo;
    }

    public String getWelcomeMessageVideoS3Key() {
        return welcomeMessageVideoS3Key;
    }

    public void setWelcomeMessageVideoS3Key(String welcomeMessageVideoS3Key) {
        this.welcomeMessageVideoS3Key = welcomeMessageVideoS3Key;
    }

    public boolean isMenuCTAEnabled() {
        return isMenuCTAEnabled;
    }

    public void setMenuCTAEnabled(boolean menuCTAEnabled) {
        isMenuCTAEnabled = menuCTAEnabled;
    }

    public String getMenuCTAUrl() {
        return menuCTAUrl;
    }

    public void setMenuCTAUrl(String menuCTAUrl) {
        this.menuCTAUrl = menuCTAUrl;
    }

    public String getMenuCTAImage() {
        return menuCTAImage;
    }

    public void setMenuCTAImage(String menuCTAImage) {
        this.menuCTAImage = menuCTAImage;
    }

    public boolean isWorkshopEnabled() {
        return isWorkshopEnabled;
    }

    public void setWorkshopEnabled(boolean workshopEnabled) {
        isWorkshopEnabled = workshopEnabled;
    }

    public String getHubMainBackgroundColor() {
        return hubMainBackgroundColor;
    }

    public void setHubMainBackgroundColor(String hubMainBackgroundColor) {
        this.hubMainBackgroundColor = hubMainBackgroundColor;
    }

    public String getVirtualTabs() {
        return virtualTabs;
    }

    public void setVirtualTabs(String virtualTabs) {
        this.virtualTabs = virtualTabs;
    }

    public Long getMaxNumberOfFreeAttendees() {
        return maxNumberOfFreeAttendees;
    }

    public void setMaxNumberOfFreeAttendees(Long maxNumberOfFreeAttendees) {
        this.maxNumberOfFreeAttendees = maxNumberOfFreeAttendees;
    }

    public String getExpoBackgroundImage() {
        return expoBackgroundImage;
    }

    public void setExpoBackgroundImage(String expoBackgroundImage) {
        this.expoBackgroundImage = expoBackgroundImage;
    }

    public String getVirtualLabels() {
        return virtualLabels;
    }

    public void setVirtualLabels(String virtualLabels) {
        this.virtualLabels = virtualLabels;
    }

    public boolean isSimultaneousSessionScroll() {
        return simultaneousSessionScroll;
    }

    public void setSimultaneousSessionScroll(boolean simultaneousSessionScroll) {
        this.simultaneousSessionScroll = simultaneousSessionScroll;
    }

    public boolean isEnabledLounges() {
        return isEnabledLounges;
    }

    public void setEnabledLounges(boolean enabledLounges) {
        isEnabledLounges = enabledLounges;
    }

    public boolean isGamificationNewFlow() {
        return gamificationNewFlow;
    }

    public void setGamificationNewFlow(boolean gamificationNewFlow) {
        this.gamificationNewFlow = gamificationNewFlow;
    }

    public String getButtonTextColorConfiguration() {
        return buttonTextColorConfiguration;
    }

    public void setButtonTextColorConfiguration(String buttonTextColorConfiguration) {
        this.buttonTextColorConfiguration = buttonTextColorConfiguration;
    }

    public String getLobbyBannerImage() { return lobbyBannerImage; }

    public void setLobbyBannerImage(String lobbyBannerImage) { this.lobbyBannerImage = lobbyBannerImage; }

    public Integer getMaxAgendaItems() {
        return maxAgendaItems;
    }

    public void setMaxAgendaItems(Integer maxAgendaItems) {
        this.maxAgendaItems = maxAgendaItems;
    }

    public String getCustomizeSpeakerInviteEmail() {
        return customizeSpeakerInviteEmail;
    }

    public void setCustomizeSpeakerInviteEmail(String customizeSpeakerInviteEmail) {
        this.customizeSpeakerInviteEmail = customizeSpeakerInviteEmail;
    }

    public boolean isHideLobbyBanner() {
        return hideLobbyBanner;
    }

    public void setHideLobbyBanner(boolean hideLobbyBanner) {
        this.hideLobbyBanner = hideLobbyBanner;
    }

    public boolean isAddNoFollow() {
        return addNoFollow;
    }

    public void setAddNoFollow(boolean addNoFollow) {
        this.addNoFollow = addNoFollow;
    }

    public String getBookMarkButtonColor() {
        return bookMarkButtonColor;
    }

    public void setBookMarkButtonColor(String bookMarkButtonColor) {
        this.bookMarkButtonColor = bookMarkButtonColor;
    }

    public boolean isAllowInterestToAttendee() {
        return allowInterestToAttendee;
    }

    public void setAllowInterestToAttendee(boolean allowInterestToAttendee) {
        this.allowInterestToAttendee = allowInterestToAttendee;
    }

    public boolean isLobbyLiveStream() {
        return isLobbyLiveStream;
    }

    public void setLobbyLiveStream(boolean lobbyLiveStream) {
        isLobbyLiveStream = lobbyLiveStream;
    }

    public String getSessionWaitingImage() { return sessionWaitingImage; }

    public void setSessionWaitingImage(String sessionWaitingImage) { this.sessionWaitingImage = sessionWaitingImage; }

    public String getExpoBannerImage() {
        return expoBannerImage;
    }

    public void setExpoBannerImage(String expoBannerImage) {
        this.expoBannerImage = expoBannerImage;
    }


    public String getLabelLanguageCode() {
        return labelLanguageCode;
    }

    public void setLabelLanguageCode(String labelLanguageCode) {
        this.labelLanguageCode = labelLanguageCode;
    }

    public boolean isHideExpoBannerImage() { return hideExpoBannerImage; }

    public void setHideExpoBannerImage(boolean hideExpoBannerImage) { this.hideExpoBannerImage = hideExpoBannerImage; }

    public boolean isMenuCTANewTab() { return isMenuCTANewTab; }

    public void setMenuCTANewTab(boolean menuCTANewTab) { isMenuCTANewTab = menuCTANewTab; }

    public String getRightSidebarConfig() {
        return rightSidebarConfig;
    }

    public void setRightSidebarConfig(String rightSidebarConfig) {
        this.rightSidebarConfig = rightSidebarConfig;
    }

    public boolean isShowSpeakerSummery() {
        return showSpeakerSummery;
    }

    public void setShowSpeakerSummery(boolean showSpeakerSummery) {
        this.showSpeakerSummery = showSpeakerSummery;
    }

    @Override
    public String toString() {
        return "VirtualEventSettings{" +
                "id=" + id +
                ", eventId=" + eventId +
                ", event=" + event +
                ", acceptDirectMessages=" + acceptDirectMessages +
                ", isStageEnabled=" + isStageEnabled +
                ", isSessionEnabled=" + isSessionEnabled +
                ", isNetworkingEnabled=" + isNetworkingEnabled +
                ", isExpoEnabled=" + isExpoEnabled +
                ", isPronounsFieldEnabled=" + isPronounsFieldEnabled +
                ", isFeedEnabled=" + isFeedEnabled +
                ", isEnablePeoplePage=" + isEnablePeoplePage +
                ", subscriptionItemId='" + subscriptionItemId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", hubNavigationBackgroundColor='" + hubNavigationBackgroundColor + '\'' +
                ", hubNavigationTextColor='" + hubNavigationTextColor + '\'' +
                ", hubMainBackgroundColor='" + hubMainBackgroundColor + '\'' +
                ", cardId='" + cardId + '\'' +
                ", allowAttendeeToPostInFeed=" + allowAttendeeToPostInFeed +
                ", allowAttendeeToCommentInFeed=" + allowAttendeeToCommentInFeed +
                ", welcomeMessage='" + welcomeMessage + '\'' +
                ", welcomeMessageVideo='" + welcomeMessageVideo + '\'' +
                ", welcomeMessageVideoS3Key='" + welcomeMessageVideoS3Key + '\'' +
                ", isMenuCTAEnabled=" + isMenuCTAEnabled +
                ", menuCTAUrl='" + menuCTAUrl + '\'' +
                ", menuCTAImage='" + menuCTAImage + '\'' +
                ", isWorkshopEnabled=" + isWorkshopEnabled +
                ", virtualTabs='" + virtualTabs + '\'' +
                ", numberOfLiveStreamingExhibitorsPurchased=" + numberOfLiveStreamingExhibitorsPurchased +
                ", maxNumberOfFreeAttendees=" + maxNumberOfFreeAttendees +
                ", customTab='" + customTab + '\'' +
                ", isSpeakerInviteEnable=" + isSpeakerInviteEnable +
                ", expoBackgroundImage='" + expoBackgroundImage + '\'' +
                ", virtualLabels='" + virtualLabels + '\'' +
                ", simultaneousSessionScroll=" + simultaneousSessionScroll +
                ", sessionNotification=" + sessionNotification +
                ", isEnabledLounges=" + isEnabledLounges +
                ", gamificationNewFlow=" + gamificationNewFlow +
                ", buttonTextColorConfiguration='" + buttonTextColorConfiguration + '\'' +
                ", lobbyBannerImage='" + lobbyBannerImage + '\'' +
                ", maxAgendaItems=" + maxAgendaItems +
                ", customizeSpeakerInviteEmail='" + customizeSpeakerInviteEmail + '\'' +
                ", hideLobbyBanner=" + hideLobbyBanner +
                ", addNoFollow=" + addNoFollow +
                ", bookMarkButtonColor='" + bookMarkButtonColor + '\'' +
                ", allowInterestToAttendee=" + allowInterestToAttendee +
                ", allowAttendeeToScheduleMeeting=" + allowAttendeeToScheduleMeeting +
                ", allowLateJoin=" + allowLateJoin +
                ", isLobbyLiveStream=" + isLobbyLiveStream +
                ", sessionWaitingImage='" + sessionWaitingImage + '\'' +
                ", expoBannerImage='" + expoBannerImage + '\'' +
                ", labelLanguageCode='" + labelLanguageCode + '\'' +
                ", hideExpoBannerImage=" + hideExpoBannerImage +
                ", recordStatus=" + recordStatus +
                ", wantsToLearn='" + wantsToLearn + '\'' +
                ", isMenuCTANewTab=" + isMenuCTANewTab +
                ", rightSidebarConfig='" + rightSidebarConfig + '\'' +
                ", showSpeakerSummery=" + showSpeakerSummery +
                ", theme=" + theme +
                ", advancedColorSettings=" + advancedColorSettings +
                ", eventHubDisclaimer=" + eventHubDisclaimer +
                ", allowEventHubDisagreeDisclaimerConfirmation=" + allowEventHubDisagreeDisclaimerConfirmation +
                ", customEventHubDisclaimer='" + customEventHubDisclaimer + '\'' +
                ", speakerRegistrationApproval=" + speakerRegistrationApproval +
                ", speakerRegistrationSubmission=" + speakerRegistrationSubmission +
                ", primaryThemeColor='" + primaryThemeColor + '\'' +
                ", exhibitorRegistrationApproval=" + exhibitorRegistrationApproval +
                ", exhibitorRegistrationSubmission=" + exhibitorRegistrationSubmission +
                ", speakerRegistrationAutoSendMail=" + speakerRegistrationAutoSendMail +
                ", exhibitorRegistrationAutoSendMail=" + exhibitorRegistrationAutoSendMail +
                ", speakerWidgetLayout=" + speakerWidgetLayout +
                ", speakerWidgetShowSocialLinks=" + speakerWidgetShowSocialLinks +
                ", speakerWidgetHighlightSpecificSpeaker=" + speakerWidgetHighlightSpecificSpeaker +
                ", isShowCountOfAttending=" + isShowCountOfAttending +
                ", speakerWidgetProfileSize=" + speakerWidgetProfileSize +
                ", allowSessionBookmarkCapacity=" + allowSessionBookmarkCapacity +
                ", holderInfoVisibleToAttendees=" + holderInfoVisibleToAttendees +
                ", isShowFullAgendaTab=" + isShowFullAgendaTab +
                ", showBookmarkButton=" + showBookmarkButton +
                '}';
    }

    @Override
    public Object clone() {   //NOSONAR
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace(); //NOSONAR
            log.warn("Exception while cloning virtual event setting: {} " , e.getMessage());
            return null; //NOSONAR
        }
    }

    public boolean isSpeakerInviteEnable() {
        return isSpeakerInviteEnable;
    }

    public void setSpeakerInviteEnable(boolean speakerInviteEnable) {
        isSpeakerInviteEnable = speakerInviteEnable;
    }

    public boolean isAllowLateJoin() {
        return allowLateJoin;
    }

    public void setAllowLateJoin(boolean allowLateJoin) {
        this.allowLateJoin = allowLateJoin;
    }

    public RecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getWantsToLearn() { return wantsToLearn; }

    public void setWantsToLearn(String wantsToLearn) { this.wantsToLearn = wantsToLearn; }

    public Theme getTheme() {
        return theme;
    }

    public void setTheme(Theme theme) {
        this.theme = theme;
    }

    public boolean getAdvancedColorSettings() {
        return advancedColorSettings;
    }

    public void setAdvancedColorSettings(boolean advancedColorSettings) {
        this.advancedColorSettings = advancedColorSettings;
    }

    public boolean isSessionNotification() {
        return sessionNotification;
    }

    public void setSessionNotification(boolean sessionNotification) {
        this.sessionNotification = sessionNotification;
    }

    public boolean isEventHubDisclaimer() {
        return eventHubDisclaimer;
    }

    public void setEventHubDisclaimer(boolean eventHubDisclaimer) {
        this.eventHubDisclaimer = eventHubDisclaimer;
    }

    public boolean isAllowEventHubDisagreeDisclaimerConfirmation() {
        return allowEventHubDisagreeDisclaimerConfirmation;
    }

    public void setAllowEventHubDisagreeDisclaimerConfirmation(boolean allowEventHubDisagreeDisclaimerConfirmation) {
        this.allowEventHubDisagreeDisclaimerConfirmation = allowEventHubDisagreeDisclaimerConfirmation;
    }

    public String getCustomEventHubDisclaimer() {
        return customEventHubDisclaimer;
    }

    public void setCustomEventHubDisclaimer(String customEventHubDisclaimer) {
        this.customEventHubDisclaimer = customEventHubDisclaimer;
    }

    public boolean isSpeakerRegistrationApproval() {
        return speakerRegistrationApproval;
    }

    public void setSpeakerRegistrationApproval(boolean speakerRegistrationApproval) {
        this.speakerRegistrationApproval = speakerRegistrationApproval;
    }

    public boolean isSpeakerRegistrationSubmission() {
        return speakerRegistrationSubmission;
    }

    public void setSpeakerRegistrationSubmission(boolean speakerRegistrationSubmission) {
        this.speakerRegistrationSubmission = speakerRegistrationSubmission;
    }

    public String getPrimaryThemeColor() {
        return primaryThemeColor;
    }

    public void setPrimaryThemeColor(String primaryThemeColor) {
        this.primaryThemeColor = primaryThemeColor;
    }

    public boolean isExhibitorRegistrationApproval() {
        return exhibitorRegistrationApproval;
    }

    public void setExhibitorRegistrationApproval(boolean exhibitorRegistrationApproval) {
        this.exhibitorRegistrationApproval = exhibitorRegistrationApproval;
    }

    public boolean isExhibitorRegistrationSubmission() {
        return exhibitorRegistrationSubmission;
    }

    public void setExhibitorRegistrationSubmission(boolean exhibitorRegistrationSubmission) {
        this.exhibitorRegistrationSubmission = exhibitorRegistrationSubmission;
    }

    public AutoSendEmail getSpeakerRegistrationAutoSendMail() {
        return speakerRegistrationAutoSendMail;
    }

    public void setSpeakerRegistrationAutoSendMail(AutoSendEmail speakerRegistrationAutoSendMail) {
        this.speakerRegistrationAutoSendMail = speakerRegistrationAutoSendMail;
    }

    public AutoSendEmail getExhibitorRegistrationAutoSendMail() {
        return exhibitorRegistrationAutoSendMail;
    }

    public void setExhibitorRegistrationAutoSendMail(AutoSendEmail exhibitorRegistrationAutoSendMail) {
        this.exhibitorRegistrationAutoSendMail = exhibitorRegistrationAutoSendMail;
    }

    public boolean isAdvancedColorSettings() {
        return advancedColorSettings;
    }

    public SpeakerWidgetLayoutEnum getSpeakerWidgetLayout() {
        return speakerWidgetLayout;
    }

    public void setSpeakerWidgetLayout(SpeakerWidgetLayoutEnum speakerWidgetLayout) {
        this.speakerWidgetLayout = speakerWidgetLayout;
    }

    public boolean isSpeakerWidgetShowSocialLinks() {
        return speakerWidgetShowSocialLinks;
    }

    public void setSpeakerWidgetShowSocialLinks(boolean speakerWidgetShowSocialLinks) {
        this.speakerWidgetShowSocialLinks = speakerWidgetShowSocialLinks;
    }

    public boolean isSpeakerWidgetHighlightSpecificSpeaker() {
        return speakerWidgetHighlightSpecificSpeaker;
    }

    public void setSpeakerWidgetHighlightSpecificSpeaker(boolean speakerWidgetHighlightSpecificSpeaker) {
        this.speakerWidgetHighlightSpecificSpeaker = speakerWidgetHighlightSpecificSpeaker;
    }

    public boolean isShowCountOfAttending() {
        return isShowCountOfAttending;
    }

    public void setShowCountOfAttending(boolean showCountOfAttending) {
        isShowCountOfAttending = showCountOfAttending;
    }

    public SpeakerWidgetProfileSizeEnum getSpeakerWidgetProfileSize() {
        return speakerWidgetProfileSize;
    }

    public void setSpeakerWidgetProfileSize(SpeakerWidgetProfileSizeEnum speakerWidgetProfileSize) {
        this.speakerWidgetProfileSize = speakerWidgetProfileSize;
    }

    public boolean isAllowSessionBookmarkCapacity() {
        return allowSessionBookmarkCapacity;
    }

    public void setAllowSessionBookmarkCapacity(boolean allowSessionBookmarkCapacity) {
        this.allowSessionBookmarkCapacity = allowSessionBookmarkCapacity;
    }

    public boolean isAllowAttendeeToScheduleMeeting() {
        return allowAttendeeToScheduleMeeting;
    }

    public void setAllowAttendeeToScheduleMeeting(boolean allowAttendeeToScheduleMeeting) {
        this.allowAttendeeToScheduleMeeting = allowAttendeeToScheduleMeeting;
    }

    public boolean isHolderInfoVisibleToAttendees() {
        return holderInfoVisibleToAttendees;
    }

    public void setHolderInfoVisibleToAttendees(boolean holderInfoVisibleToAttendees) {
        this.holderInfoVisibleToAttendees = holderInfoVisibleToAttendees;
    }

    public boolean isShowFullAgendaTab() { return isShowFullAgendaTab; }

    public void setShowFullAgendaTab(boolean showFullAgendaTab) {
        isShowFullAgendaTab = showFullAgendaTab;
    }
    public boolean isShowBookmarkButton() {
        return showBookmarkButton;
    }

    public void setShowBookmarkButton(boolean showBookmarkButton) {
        this.showBookmarkButton = showBookmarkButton;
    }

    public boolean isEnableSessionWaitlistCapacityReached() {
        return enableSessionWaitlistCapacityReached;
    }

    public void setEnableSessionWaitlistCapacityReached(boolean enableSessionWaitlistCapacityReached) {
        this.enableSessionWaitlistCapacityReached = enableSessionWaitlistCapacityReached;
    }
}
