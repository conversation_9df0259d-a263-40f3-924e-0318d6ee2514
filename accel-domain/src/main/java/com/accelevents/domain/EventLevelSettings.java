package com.accelevents.domain;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.SessionTimeConflictsEnum;
import com.accelevents.utils.Constants;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * To store all the events details.
 *
 */
@Entity
@Table(name = "event_level_settings")
@DynamicUpdate
@Where(clause="rec_status<>'DELETE' or rec_status is NULL")
public class EventLevelSettings implements Serializable {

    private static final long serialVersionUID = -1096110056216243088L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "event_id")
    private Long eventId;

    @OneToOne(fetch = FetchType.LAZY,targetEntity = Event.class)
    @JoinColumn(name = "event_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private Event event;

    @Column(name = "enable_people_registration_tab")
    private boolean isEnablePeopleRegistrationTab = Boolean.FALSE;

    @Column(name = "rec_status" ,  length = 50, nullable = false)
    @Enumerated(EnumType.STRING)
    private RecordStatus recStatus = RecordStatus.CREATE;

    @Column(name = "show_save_a_seat_button")
    private boolean showSaveASeatButton = Boolean.TRUE;

    @Column(name = "is_send_save_a_seat_email")
    private boolean isSendSaveASeatEmail = Boolean.FALSE;

    @Column(name = "reviewer_registration_approval")
    private boolean reviewerRegistrationApproval = Boolean.FALSE;

    @Column(name = "reviewer_registration_submission")
    private boolean reviewerRegistrationSubmission = Boolean.FALSE;

    @Column(name = "show_ce_credits")
    private boolean showCECredits = Boolean.FALSE;

    @Column(name = "sso_required")
    private boolean ssoRequired = Boolean.FALSE;

    @Column(name = "enable_sso_mapping")
    private boolean enableSSOMapping = Boolean.FALSE;

    @Column(name = "is_recheck_in_enabled")
    private boolean isRecheckInEnabled = Boolean.FALSE;

    @Column(name = "session_register_button")
    private String sessionRegisterButton = Constants.SAVE_A_SEAT;

    @Column(name = "session_registered_button")
    private String sessionRegisteredButton  = Constants.SEAT_SAVED;

    @Column(name = "session_capacity_reached_button")
    private String sessionCapacityReachedButton = Constants.UNAVAILABLE;

    @Column(name = "session_time_conflicts")
    @Enumerated(EnumType.STRING)
    private SessionTimeConflictsEnum sessionTimeConflicts = SessionTimeConflictsEnum.ALLOW_OVERRIDE;

    @Column(name = "concurrent_session_overlap_time")
    private int concurrentSessionOverlapTime = 15;

    @Column(name = "allow_session_overlap")
    private boolean allowSessionOverlap = Boolean.FALSE;

    //For the CE certificate download form attendee side, only 1 toggle is obtained. That's why this field added to this entity; if multiple toggles are required in the future, then a separate table needs to be created for it.    @Column(name = "allow_attendee_download_certificate")
    @Column(name = "allow_attendee_to_download_certificates")
    private boolean allowAttendeeToDownloadCertificate = Boolean.FALSE;

    @Column(name = "enable_rfid_badge")
    private boolean enableRFIDBadge = Boolean.FALSE;

    @Column(name = "survey_requires_checkin")
    private boolean surveyRequiresCheckIn = Boolean.TRUE;

    @Column(name = "allow_order_exchange")
    private boolean allowOrderExchange = Boolean.FALSE;

    @Column(name = "display_staff_and_speaker_badges")
    private boolean displayStaffAndSpeakerBadges = Boolean.FALSE;

    @Column(name = "allow_staff_to_manage_orders")
    private boolean allowStaffToManageOrders = Boolean.FALSE;

    @Column(name = "use_appsync")
    private boolean useAppsync = Boolean.TRUE;

    @Column(name = "enable_pre_registration_routing")
    private boolean enablePreRegistrationRouting = Boolean.FALSE;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public boolean isEnablePeopleRegistrationTab() {
        return isEnablePeopleRegistrationTab;
    }

    public void setEnablePeopleRegistrationTab(boolean enablePeopleRegistrationTab) {
        isEnablePeopleRegistrationTab = enablePeopleRegistrationTab;
    }

    public RecordStatus getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(RecordStatus recStatus) {
        this.recStatus = recStatus;
    }

    public boolean isShowSaveASeatButton() {
        return showSaveASeatButton;
    }

    public void setShowSaveASeatButton(boolean showSaveASeatButton) {
        this.showSaveASeatButton = showSaveASeatButton;
    }

    public boolean isSendSaveASeatEmail() {
        return isSendSaveASeatEmail;
    }

    public void setSendSaveASeatEmail(boolean sendSaveASeatEmail) {
        isSendSaveASeatEmail = sendSaveASeatEmail;
    }

    public boolean isReviewerRegistrationApproval() {
        return reviewerRegistrationApproval;
    }

    public boolean isReviewerRegistrationSubmission() {
        return reviewerRegistrationSubmission;
    }

    public void setReviewerRegistrationSubmission(boolean reviewerRegistrationSubmission) {
        this.reviewerRegistrationSubmission = reviewerRegistrationSubmission;
    }

    public void setReviewerRegistrationApproval(boolean reviewerRegistrationApproval) {
        this.reviewerRegistrationApproval = reviewerRegistrationApproval;
    }

    public boolean isShowCECredits() {
        return showCECredits;
    }

    public void setShowCECredits(boolean showCECredits) {
        this.showCECredits = showCECredits;
    }

    public boolean isSsoRequired() {
        return ssoRequired;
    }

    public void setSsoRequired(boolean ssoRequired) {
        this.ssoRequired = ssoRequired;
    }

    public boolean isEnableSSOMapping() {
        return enableSSOMapping;
    }

    public void setEnableSSOMapping(boolean enableSSOMapping) {
        this.enableSSOMapping = enableSSOMapping;
    }

    public boolean isRecheckInEnabled() { return isRecheckInEnabled; }

    public void setRecheckInEnabled(boolean recheckInEnabled) { isRecheckInEnabled = recheckInEnabled; }

    public String getSessionRegisterButton() {
        return sessionRegisterButton;
    }

    public void setSessionRegisterButton(String sessionRegisterButton) {
        this.sessionRegisterButton = sessionRegisterButton;
    }

    public String getSessionRegisteredButton() {
        return sessionRegisteredButton;
    }

    public void setSessionRegisteredButton(String sessionRegisteredButton) {
        this.sessionRegisteredButton = sessionRegisteredButton;
    }

    public String getSessionCapacityReachedButton() {
        return sessionCapacityReachedButton;
    }

    public void setSessionCapacityReachedButton(String sessionCapacityReachedButton) {
        this.sessionCapacityReachedButton = sessionCapacityReachedButton;
    }

    public SessionTimeConflictsEnum getSessionTimeConflicts() {
        return sessionTimeConflicts;
    }

    public void setSessionTimeConflicts(SessionTimeConflictsEnum sessionTimeConflicts) {
        this.sessionTimeConflicts = sessionTimeConflicts;
    }

    public int getConcurrentSessionOverlapTime() {
        return concurrentSessionOverlapTime;
    }

    public void setConcurrentSessionOverlapTime(int concurrentSessionOverlapTime) {
        this.concurrentSessionOverlapTime = concurrentSessionOverlapTime;
    }

    public boolean isAllowSessionOverlap() {
        return allowSessionOverlap;
    }

    public void setAllowSessionOverlap(boolean allowSessionOverlap) {
        this.allowSessionOverlap = allowSessionOverlap;
    }

    public boolean isAllowAttendeeToDownloadCertificate() {
        return allowAttendeeToDownloadCertificate;
    }

    public void setAllowAttendeeToDownloadCertificate(boolean allowAttendeeToDownloadCertificate) {
        this.allowAttendeeToDownloadCertificate = allowAttendeeToDownloadCertificate;
    }

    public boolean isEnableRFIDBadge() {
        return enableRFIDBadge;
    }

    public void setEnableRFIDBadge(boolean enableRFIDBadge) {
        this.enableRFIDBadge = enableRFIDBadge;
    }

    public boolean isSurveyRequiresCheckIn() {
        return surveyRequiresCheckIn;
    }

    public void setSurveyRequiresCheckIn(boolean surveyRequiresCheckIn) {
        this.surveyRequiresCheckIn = surveyRequiresCheckIn;
    }

    public boolean isAllowOrderExchange() {
        return allowOrderExchange;
    }

    public void setAllowOrderExchange(boolean allowOrderExchange) {
        this.allowOrderExchange = allowOrderExchange;
    }

    public boolean isDisplayStaffAndSpeakerBadges() {
        return displayStaffAndSpeakerBadges;
    }

    public void setDisplayStaffAndSpeakerBadges(boolean displayStaffAndSpeakerBadges) {
        this.displayStaffAndSpeakerBadges = displayStaffAndSpeakerBadges;
    }

    public boolean isAllowStaffToManageOrders() {
        return allowStaffToManageOrders;
    }

    public void setAllowStaffToManageOrders(boolean allowStaffToManageOrders) {
        this.allowStaffToManageOrders = allowStaffToManageOrders;
    }

    public boolean isUseAppsync() {
        return useAppsync;
    }

    public void setUseAppsync(boolean useAppsync) {
        this.useAppsync = useAppsync;
    }

    public boolean isEnablePreRegistrationRouting() { return enablePreRegistrationRouting; }

    public void setEnablePreRegistrationRouting(boolean enablePreRegistrationRouting) { this.enablePreRegistrationRouting = enablePreRegistrationRouting; }
}
