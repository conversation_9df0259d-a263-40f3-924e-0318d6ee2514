# Created by .ignore support plugin (hsz.mobi)
### JetBrains template
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion

*.iml

## Directory-based project format:
#.idea/
# if you remove the above rule, at least ignore the following:


 !.idea/modules.xml
 !.idea/misc.xml
 !.idea/vcs.xml

# User-specific stuff:
 .idea/workspace.xml
 .idea/tasks.xml
 .idea/dictionaries
 .idea/.name
 .idea/compiler.xml
 .idea/encodings.xml


# Sensitive or high-churn files:
 .idea/dataSources.ids
 .idea/dataSources.xml
 .idea/sqlDataSources.xml
 .idea/dynamic.xml
 .idea/uiDesigner.xml

# Gradle:
 .idea/gradle.xml
 .idea/libraries
 .idea/copyright

# Mongo Explorer plugin:
 .idea/mongoSettings.xml

## File-based project format:
*.ipr
*.iws

## Plugin-specific files:

# IntelliJ
/out/
.idea

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties


### Maven template
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties


Email_Lambda/bin
.project
accel-bulk-email-lambda/bin
*.class
accel-utils/bin
accel-commons/.settings
accel-email/.settings
accel-domain/.settings
AccelEventsWebApp/.settings
.classpath
.DS_Store
*.aws-sam
*.aws-sam/*

.vscode/
.DS_Store
AccelEventsWebApp/.classpath
AccelEventsWebApp/.settings/org.eclipse.core.resources.prefs

log
log/*
*.ser