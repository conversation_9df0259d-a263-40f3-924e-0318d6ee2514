<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>AccelEventsWebApp</groupId>
    <artifactId>AccelEvents</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>

    <modules>
        <module>accel-utils</module>
        <module>accel-domain</module>
        <module>accel-email</module>
        <module>accel-common-components</module>
        <module>accel-common-ro-service</module>
        <module>accel-commons</module>
        <module>Email_Lambda</module>
        <module>accel-bulk-email-lambda</module>
        <module>accel-download-lambda</module>
        <module>accel-webapp-common</module>
        <module>accel-webapp-ro</module>
        <module>AccelEventsWebApp</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>11</java.version>
        <main.basedir>${basedir}/..</main.basedir>
        <spring.security.oauth2>2.1.0.RELEASE</spring.security.oauth2>
        <m2eclipse.wtp.contextRoot>/</m2eclipse.wtp.contextRoot>
        <sendgrid.version>3.0.9</sendgrid.version>
        <!--<hibernate.version>5.0.9.Final</hibernate.version>-->
        <!--<hibernate.validator.version>6.0.14.Final</hibernate.validator.version>-->
        <jackson.version>2.15.2</jackson.version>
        <aws.sdk.version>1.12.49</aws.sdk.version>
        <opencsv.version>4.0</opencsv.version>
        <mysql-connector-version>5.1.47</mysql-connector-version>
        <twilio.version>7.55.3</twilio.version>
        <twilio-java-sdk.version>(6.0,6.9)</twilio-java-sdk.version>
        <stripe-java.version>22.0.0</stripe-java.version>
        <apache.httpcomponents.version>4.4.10</apache.httpcomponents.version>
        <quartz.version>2.2.1</quartz.version>
        <commons.io.version>2.5</commons.io.version>
        <commons.beanutils>1.9.3</commons.beanutils>
        <commons.lang3.version>3.12.0</commons.lang3.version>
        <commons.text.version>1.6</commons.text.version>
        <commons.collection4.version>4.4</commons.collection4.version>
        <commons.logging.version>1.1.3</commons.logging.version>
        <commons.dbutils.version>1.7</commons.dbutils.version>
        <jadira.version>7.0.0.CR1</jadira.version>
        <springdoc.openapi.version>1.7.0</springdoc.openapi.version>
        <spring.jms>5.3.22</spring.jms>
        <project.oauth.version>1.23.0</project.oauth.version>
        <powermock.version>2.0.9</powermock.version>
        <elasticsearch.version>7.1.1</elasticsearch.version>
        <aws.java.sdk.version>2.20.55</aws.java.sdk.version>
        <jackson-datatype-hibernate4.version>2.5.3</jackson-datatype-hibernate4.version>
        <gson.version>2.8.9</gson.version>
        <aws-lambda-java-core.version>1.2.0</aws-lambda-java-core.version>
        <aws-lambda-java-events.version>2.2.2</aws-lambda-java-events.version>
        <itextpdf.version>7.1.14</itextpdf.version>
        <jsoup.version>1.10.1</jsoup.version>
        <intercom-java.version>2.8.2</intercom-java.version>
        <jpasskit.version>0.0.9</jpasskit.version>
        <flying-saucer-pdf-itext5.version>9.1.12</flying-saucer-pdf-itext5.version>
        <sentry.verion>6.29.0</sentry.verion>
        <jersey-hk2.version>2.26</jersey-hk2.version>
        <unirest-java.version>1.4.9</unirest-java.version>
        <seatsio-java.version>82.7.0</seatsio-java.version>
        <google-api-services-analytics.version>v3-rev142-1.23.0</google-api-services-analytics.version>
        <cloudinary-http44.version>1.21.0</cloudinary-http44.version>
        <qrgen.version>1.4</qrgen.version>
        <libphonenumber.version>8.13.17</libphonenumber.version>
        <aws-xray-recorder-sdk-core.version>1.2.1</aws-xray-recorder-sdk-core.version>
        <spring-boot-parameter-store-integration.version>1.5.0</spring-boot-parameter-store-integration.version>
        <httpclient.version>4.5.12</httpclient.version>
        <elasticsearch-rest-high-level-client.version>7.1.1</elasticsearch-rest-high-level-client.version>
        <aws-java-sdk-lambda.version>1.11.771</aws-java-sdk-lambda.version>
        <opentok-server-sdk.version>4.13.2</opentok-server-sdk.version>
        <UserAgentUtils.version>1.21</UserAgentUtils.version>
        <gremlin-client.version>8420b7e</gremlin-client.version>
        <auth0.java-jwt.version>3.4.0</auth0.java-jwt.version>
        <aws-java-sdk-chimesdkmeetings.version>1.12.571</aws-java-sdk-chimesdkmeetings.version>
        <aws-java-sdk-chimesdkmediapipelines.version>1.12.285</aws-java-sdk-chimesdkmediapipelines.version>
        <amazon-kinesis-client.version>1.11.2</amazon-kinesis-client.version>
        <aws-java-sdk-glacier.version>1.11.772</aws-java-sdk-glacier.version>
        <gremlin-driver.version>3.6.2</gremlin-driver.version>
        <google-api-client-gson.version>1.22.0</google-api-client-gson.version>
        <google-api-services-analyticsreporting.version>v4-rev20190904-1.30.1</google-api-services-analyticsreporting.version>
        <getstream.version>3.2.3</getstream.version>
        <chargebee-java.version>3.0.0</chargebee-java.version>
        <lombok.version>1.18.20</lombok.version>
        <javax.validation.version>2.0.0.Final</javax.validation.version>
        <graphql.spqr.version>0.12.3</graphql.spqr.version>
        <spring-beans.version>5.1.4.RELEASE</spring-beans.version>
        <square.version>32.0.0.20230816</square.version>
        <sonar.organization>accelevents</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
        <subtitle.version>192ba1be2d</subtitle.version>
        <netty.version>4.1.72.Final</netty.version>
        <!-- elasticache.version is getting referred from spring-cloud-aws-dependencies if you want different version this property is required-->
        <elasticache.version>1.2.2</elasticache.version>
    </properties>

    <repositories>
        <repository>
            <id>jcenter</id>
            <url>https://jcenter.bintray.com</url>
        </repository>
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <ebextensions.path>src/main/ebextensions/dev</ebextensions.path>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.3.3</version>
                    <type>pom</type>
                </dependency>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>rt</artifactId>
                    <version>2.3.3</version>
                </dependency>
                <dependency>
                    <groupId>javax.xml.ws</groupId>
                    <artifactId>jaxws-api</artifactId>
                    <version>2.3.0</version>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.corba</groupId>
                    <artifactId>glassfish-corba-omgapi</artifactId>
                    <version>4.2.1</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <ebextensions.path>src/main/ebextensions/dev</ebextensions.path>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.3.3</version>
                    <type>pom</type>
                </dependency>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>rt</artifactId>
                    <version>2.3.3</version>
                </dependency>
                <dependency>
                    <groupId>javax.xml.ws</groupId>
                    <artifactId>jaxws-api</artifactId>
                    <version>2.3.0</version>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.corba</groupId>
                    <artifactId>glassfish-corba-omgapi</artifactId>
                    <version>4.2.1</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>stage</id>
            <properties>
                <ebextensions.path>src/main/ebextensions/stage</ebextensions.path>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.3.3</version>
                    <type>pom</type>
                </dependency>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>rt</artifactId>
                    <version>2.3.3</version>
                </dependency>
                <dependency>
                    <groupId>javax.xml.ws</groupId>
                    <artifactId>jaxws-api</artifactId>
                    <version>2.3.0</version>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.corba</groupId>
                    <artifactId>glassfish-corba-omgapi</artifactId>
                    <version>4.2.1</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <ebextensions.path>src/main/ebextensions/prod</ebextensions.path>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.3.3</version>
                    <type>pom</type>
                </dependency>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>rt</artifactId>
                    <version>2.3.3</version>
                </dependency>
                <dependency>
                    <groupId>javax.xml.ws</groupId>
                    <artifactId>jaxws-api</artifactId>
                    <version>2.3.0</version>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.corba</groupId>
                    <artifactId>glassfish-corba-omgapi</artifactId>
                    <version>4.2.1</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <ebextensions.path>src/main/ebextensions/stage</ebextensions.path>
                <sonar.exclusions>com/accelevents/controllers/**</sonar.exclusions>
                <sonar.coverage.exclusions>**/controllers/**,**/utils/**,**/dto/**,**/domain/**,**/configuration/**,**/config/**,**/security/**,**/WebSecurityConfig.java,**/exceptions/**,**/TrayIntegrationServiceImpl.java</sonar.coverage.exclusions>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.3.3</version>
                    <type>pom</type>
                </dependency>
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>rt</artifactId>
                    <version>2.3.3</version>
                </dependency>
                <dependency>
                    <groupId>javax.xml.ws</groupId>
                    <artifactId>jaxws-api</artifactId>
                    <version>2.3.0</version>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.corba</groupId>
                    <artifactId>glassfish-corba-omgapi</artifactId>
                    <version>4.2.1</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${aws.java.sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.accelevents</groupId>
                <artifactId>accel-utils</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.accelevents</groupId>
                <artifactId>accel-domain</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.accelevents</groupId>
                <artifactId>accel-email</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.accelevents</groupId>
                <artifactId>accel-common-components</artifactId>
                <version>1.0</version>
            </dependency>

            <dependency>
                <groupId>com.accelevents</groupId>
                <artifactId>accel-common-ro-service</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>AccelEventsWebApp</groupId>
                <artifactId>accel-commons</artifactId>
                <version>1.0</version>
            </dependency>

            <dependency>
                <groupId>com.accelevents</groupId>
                <artifactId>accel-webapp-common</artifactId>
                <version>1.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${spring.security.oauth2}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jms</artifactId>
                <version>${spring.jms}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-hibernate4</artifactId>
                <version>${jackson-datatype-hibernate4.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.twilio.sdk</groupId>
                <artifactId>twilio-java-sdk</artifactId>
                <version>${twilio-java-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.twilio.sdk</groupId>
                <artifactId>twilio</artifactId>
                <version>${twilio.version}</version>
                <!-- After twilio version update from 7.1.0 to 7.55.3 internal jjwt version gets updated, and it causes the issue
                    during token generation so excluded jjwt dependencies from 7.55.3 twilio version -->
                <exclusions>
                    <exclusion>
                        <groupId>io.jsonwebtoken</groupId>
                        <artifactId>jjwt-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.jsonwebtoken</groupId>
                        <artifactId>jjwt-jackson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.jsonwebtoken</groupId>
                        <artifactId>jjwt-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- After twilio version update from 7.1.0 to 7.55.3 internal jjwt version gets updated, and it causes the issue
                    during token generation so excluded jjwt dependencies from 7.55.3 twilio version -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>0.9.1</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-sqs</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-core</artifactId>
                <version>${aws-lambda-java-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-events</artifactId>
                <version>${aws-lambda-java-events.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-glacier</artifactId>
                <version>${aws-java-sdk-glacier.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>amazon-sqs-java-messaging-lib</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-cloudfront</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons.beanutils}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons.text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons.collection4.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons.logging.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-dbutils</groupId>
                <artifactId>commons-dbutils</artifactId>
                <version>${commons.dbutils.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext7-core</artifactId>
                <version>${itextpdf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jadira.usertype</groupId>
                <artifactId>usertype.core</artifactId>
                <version>${jadira.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendgrid</groupId>
                <artifactId>sendgrid-java</artifactId>
                <version>${sendgrid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stripe</groupId>
                <artifactId>stripe-java</artifactId>
                <version>${stripe-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${apache.httpcomponents.version}</version>
            </dependency>

            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>

            <dependency>
                <!-- jsoup HTML parser library @ http://jsoup.org/ -->
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.openapi.version}</version>
            </dependency>

            <dependency>
                <groupId>io.intercom</groupId>
                <artifactId>intercom-java</artifactId>
                <version>${intercom-java.version}</version>
            </dependency>

            <dependency>
                <groupId>de.brendamour</groupId>
                <artifactId>jpasskit</artifactId>
                <version>${jpasskit.version}</version>
            </dependency>

            <dependency>
                <groupId>org.xhtmlrenderer</groupId>
                <artifactId>flying-saucer-pdf-itext5</artifactId>
                <version>${flying-saucer-pdf-itext5.version}</version>
            </dependency>

            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-logback</artifactId>
                <version>${sentry.verion}</version>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-spring-boot-starter</artifactId>
                <version>${sentry.verion}</version>
            </dependency>


            <dependency>
                <groupId>com.squareup</groupId>
                <artifactId>square</artifactId>
                <version>${square.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.9.1</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>1.3.70</version>
            </dependency>
            <!--Need to remove jersey dependency BUT it's used by square so first need to think approach of square-->
            <dependency>
                <groupId>org.glassfish.jersey.inject</groupId>
                <artifactId>jersey-hk2</artifactId>
                <version>${jersey-hk2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mashape.unirest</groupId>
                <artifactId>unirest-java</artifactId>
                <version>${unirest-java.version}</version>
            </dependency>
            <dependency>
                <groupId>io.seats</groupId>
                <artifactId>seatsio-java</artifactId>
                <version>${seatsio-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.apis</groupId>
                <artifactId>google-api-services-analytics</artifactId>
                <version>${google-api-services-analytics.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.oauth-client</groupId>
                <artifactId>google-oauth-client-jetty</artifactId>
                <version>${project.oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>9.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.cloudinary</groupId>
                <artifactId>cloudinary-http44</artifactId>
                <version>${cloudinary-http44.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.11.2</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.13.0</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>net.glxn</groupId>
                <artifactId>qrgen</artifactId>
                <version>${qrgen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.coveo</groupId>
                <artifactId>spring-boot-parameter-store-integration</artifactId>
                <version>${spring-boot-parameter-store-integration.version}</version>
            </dependency>

            <!-- After twilio version update from 7.1.0 to 7.55.3 httpclient is available, but it causes issue
                (https://github.com/twilio/twilio-java/issues/603) so manually added httpclient -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch-rest-high-level-client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.25.0-GA</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>oauth2-oidc-sdk</artifactId>
                <version>10.7</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>9.31</version>
            </dependency>

            <dependency>
                <groupId>com.tokbox</groupId>
                <artifactId>opentok-server-sdk</artifactId>
                <version>${opentok-server-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${UserAgentUtils.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${auth0.java-jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-xray-recorder-sdk-core</artifactId>
                <version>${aws-xray-recorder-sdk-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-ssm</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-dynamodb</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-lambda</artifactId>
                <version>${aws-java-sdk-lambda.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-chimesdkmeetings</artifactId>
                <version>${aws-java-sdk-chimesdkmeetings.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-chimesdkmediapipelines</artifactId>
                <version>${aws-java-sdk-chimesdkmediapipelines.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>amazon-kinesis-client</artifactId>
                <version>${amazon-kinesis-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tinkerpop</groupId>
                <artifactId>gremlin-driver</artifactId>
                <version>${gremlin-driver.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/software.amazon.neptune/gremlin-client -->
            <dependency>
                <groupId>software.amazon.neptune</groupId>
                <artifactId>gremlin-client</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.googlecode.libphonenumber/libphonenumber -->
            <!-- Updated from 8.9.4 to 8.12.10 as such it didn't provide region code from provided number -->
            <dependency>
                <groupId>com.googlecode.libphonenumber</groupId>
                <artifactId>libphonenumber</artifactId>
                <version>${libphonenumber.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.api-client</groupId>
                <artifactId>google-api-client-gson</artifactId>
                <version>${google-api-client-gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.apis</groupId>
                <artifactId>google-api-services-analyticsreporting</artifactId>
                <version>${google-api-services-analyticsreporting.version}</version>
            </dependency>
            <dependency>
                <groupId>io.getstream.client</groupId>
                <artifactId>stream-java</artifactId>
                <version>${getstream.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chargebee</groupId>
                <artifactId>chargebee-java</artifactId>
                <version>${chargebee-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>elasticache-java-cluster-client</artifactId>
                <version>${elasticache.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${javax.validation.version}</version>
            </dependency>
            <dependency>
                <groupId>io.leangen.graphql</groupId>
                <artifactId>spqr</artifactId>
                <version>${graphql.spqr.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.noophq</groupId>
                <artifactId>subtitle</artifactId>
                <version>${subtitle.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.seratch</groupId>
                <artifactId>jslack</artifactId>
                <version>3.4.2</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>1.63</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <parameters>true</parameters>
                        <source>11</source>
                        <target>11</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version>
                    <configuration>
                        <includes>
                            <include>**/*.java</include>
                        </includes>
                        <useSystemClassLoader>false</useSystemClassLoader>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.12.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-codegen-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>
        </plugins>
    </build>
</project>