package com.accelevents.service.impl;

import com.accelevents.configuration.FreeMarkerConfiguration;
import com.accelevents.helpers.HTMLHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.service.PDFCreator;
import com.itextpdf.text.DocumentException;
import freemarker.template.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.xhtmlrenderer.pdf.ITextOutputDevice;
import org.xhtmlrenderer.pdf.ITextRenderer;
import org.xhtmlrenderer.pdf.ITextUserAgent;

import java.io.*;
import java.util.Map;

public class PDFCreatorImpl implements PDFCreator {

    private Configuration configuration;

    @Autowired
    public PDFCreatorImpl(Configuration configuration){
        this.configuration = configuration;
    }

    public PDFCreatorImpl(){

    }

	private String pdfContent(TemplateId templateId, Map<String, Object> pdfData) throws TemplateException, IOException {
		StringWriter result = new StringWriter();
		Template template = FreeMarkerConfiguration.getInstance().getTemplate(templateId.getValue());
		template.process(pdfData, result);
		return  result.toString();
	}

	public void createPDF(TemplateId templateId, Map<String, Object> pdfData, OutputStream outputStream)
			throws DocumentException, IOException, TemplateException {

		ITextRenderer renderer = new ITextRenderer();
		ResourceLoaderUserAgent callback = new ResourceLoaderUserAgent(renderer.getOutputDevice());
		callback.setSharedContext(renderer.getSharedContext());
		renderer.getSharedContext().setUserAgentCallback(callback);


		renderer.setDocumentFromString(pdfContent(templateId, pdfData));
		renderer.layout();
		renderer.createPDF(outputStream);

	}

    public void createInvoicePDF(TemplateId templateId, Map<String, Object> pdfData, OutputStream outputStream)
            throws DocumentException, IOException, TemplateException {

        ITextRenderer renderer = new ITextRenderer();
        ResourceLoaderUserAgent callback = new ResourceLoaderUserAgent(renderer.getOutputDevice());
        callback.setSharedContext(renderer.getSharedContext());
        renderer.getSharedContext().setUserAgentCallback(callback);
        String documentString = pdfContent(templateId, pdfData);
        documentString = HTMLHelper.cleanHtmlAndMakeTransformable(documentString);
        renderer.setDocumentFromString(documentString);
        renderer.layout();
        renderer.createPDF(outputStream);

    }

	private static class ResourceLoaderUserAgent extends ITextUserAgent {
		public ResourceLoaderUserAgent(ITextOutputDevice outputDevice) {
			super(outputDevice);
		}

		protected InputStream resolveAndOpenStream(String uri) {
			return super.resolveAndOpenStream(uri);
		}
	}

    private String invoicePdfContent(String htmlValues,  Map<String, Object> pdfData) throws TemplateException, IOException {
        Configuration config= new Configuration(Configuration.VERSION_2_3_28);

        Template t = new Template("templateName", new StringReader(htmlValues), config);

        Writer out = new StringWriter();
        t.process(pdfData, out);

        return out.toString();
    }

    public void createCustomInvoicePDF(String htmlValue, Map<String, Object> pdfData, OutputStream outputStream)
            throws DocumentException, IOException, TemplateException {

        ITextRenderer renderer = new ITextRenderer();
        ResourceLoaderUserAgent callback = new ResourceLoaderUserAgent(renderer.getOutputDevice());
        callback.setSharedContext(renderer.getSharedContext());
        renderer.getSharedContext().setUserAgentCallback(callback);
        String documentString = invoicePdfContent(htmlValue, pdfData);
        documentString = HTMLHelper.cleanHtmlAndMakeTransformable(documentString);
        renderer.setDocumentFromString(documentString);
        renderer.layout();
        renderer.createPDF(outputStream);
    }
}
