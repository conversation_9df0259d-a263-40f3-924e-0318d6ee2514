package com.accelevents.email.utils;

public interface BulkLambdaConstants {

    String COL_PRESENT = "PRESENT";

    // MessageToContacts Columns
    String COL_ID = "id";
    String COL_MESSAGE_BODY = "message_body";
    String COL_SUBJECT_LINE = "subject_line";
    String COL_EVENT_ID = "event_id";
    String COL_REPLY_TO_EMAIL = "reply_to_email";
    String COL_SEND_TO = "send_to";
    String COL_ALL_MEMBERS_SELECTED = "all_members_selected";
    String COL_CONTACTS_LIST_ID = "contacts_list_id";
    String COL_TICKET_TYPE_IDS = "ticket_type_ids";
    String COL_STATUS = "status";
    String DONT_SEND_TO = "dont_send_to";
    String COL_SCHEDULED_AT = "scheduled_at";
    String COL_UPDATED_AT = "updated_at";

    // events Columns
    String COL_NAME = "name";
    String COL_EVENTURL = "eventurl";
    String COL_WHITE_LABEL = "white_label";
    String COL_NOTIFICATION_EMAIL = "notification_email";
    String COL_REPLY_EMAIL = "reply_email";
    String COL_EMAIL_SENDER_NAME = "email_sender_name";
    String COL_EVENT_FORMAT = "event_format";
    String COL_EQUIVALENT_TIMEZONE = "equivalent_timezone";
    String COL_TIMEZONE_ID  = "timezone_id";
    String COL_HIDE_EVENT_DATE = "hide_event_date";
    String COL_CURRENCY = "currency";
    String COL_EVENT_CALENDAR_INVITE = "event_calendar_invite";
    String COL_LOGO_IMAGE = "logo_image";
    String COL_EVENT_START_DATE = "event_start_date";
    String COL_EVENT_END_DATE = "event_end_date";
    String COL_EVENT_ADDRESS = "event_address";
    String COL_LATITUDE = "latitude";
    String COL_LONGITUDE = "longitude";
    String COL_SHOW_ENTER_EVENT_BUTTON_IN_REMINDER_TEMPLATE = "show_enter_event_button_in_reminder_template";

    //white_label columns
    String COL_WHITE_LABEL_URL = "white_label_url";
    String COL_FIRM_NAME = "firm_name";
    String COL_MAIL_API_KEY = "mail_api_key";
    String COL_SUPPORT_EMAIL = "support_email";
    String COL_TRANSACTIONAL_EMAIL = "transactional_email";
    String COL_HOST_BASE_URL = "host_base_url";
    String COL_PLAN_NAME = "plan_name";
    String COL_HEADER_LOGO_IMAGE = "header_logo_image";
    String COL_GET_STARTED = "get_started";
    String COL_HELP_CENTER_URL = "help_center_URL";
    String COL_PRIVACY_POLICY = "privacy_policy";
    String COL_FACEBOOK_SHARE = "facebook_share";
    String COL_TWITTER_SHARE = "twitter_share";
    String COL_LINKEDIN_SHARE = "linkedin_share";
    String COL_INSTAGRAM_SHARE = "instagram_share";

    //Contacts columns
    String COL_FIRST_NAME = "first_name";
    String COL_LAST_NAME = "last_name";
    String COL_EMAIL = "email";
    String COL_PHONE_NUMBER = "phone_number";
    String COL_TICKET_ORDER_ID = "ticket_order_id";
    String COL_TICKET_NUMBER = "ticket_number";
    String COL_TICKET_TYPE_NAME = "ticket_type_name";
    String COL_AUTO_ASSIGNED_ATTENDEE_NUMBER_ID = "auto_assigned_attendee_number_id";

    //Users columns
    String COL_USER_ID = "user_id";

    // Session columns
    String COL_SESSION_ID = "session_id";

    //Ticketing
    String COL_ACCESS_STARTED = "access_started";

    Long DUMMY_ORDER_NUMBER = 111L;
    Long DUMMY_TICKET_NUMBER = 222L;

    String DUMMY_TICKET_NAME = "TicketTypeName";
    Long DUMMY_ASSIGNED_NUMBER = 333L;
    String DUMMY_BARCODE_ID = "abc-def-test-barcode-id";
    String DUMMY_SEAT_NUMBER = "Section 1-Row 1-A";

    //ResendTicketingEmail
    String COL_RECURRING_EVENT_ID = "recurring_event_id";
    String COL_RESEND_TICKET_ORDER_TEXT = "resend_ticket_order_text";
    String COL_RESEND_TICKET_SUBJECT = "resend_ticket_subject";
    String COL_RESEND_TICKET_STATUS = "resend_ticket_status";
    String COL_RECIPIENT_TYPE= "recipient_type";
    String COL_HTML_VALUE = "html_value";
    String COL_JSON_VALUE = "json_value";
    String COL_IS_ADVANCE_EMAIL_BUILDER = "is_advance_email_builder";
    String COL_ALLOWED_TICKET_TYPES_FOR_REMINDER = "allowed_ticket_types_for_reminder";
    String COL_EXECUTION_TIME = "execution_time";

    //OrgRelatedColumns
    String COL_ORG_NAME = "org_name";
    String COL_ORG_CONTACT_EMAIL = "org_contact_email";
    String COL_ORGANIZER_PAGE_URL = "organizer_page_URL";

    //EventTickets
    String COL_H_FIRST_NAME = "h_first_name";
    String COL_H_LAST_NAME = "h_last_name";
    String COL_H_EMAIL = "h_email";
    String COL_H_USER_ID="holder_user_id";
    String COL_BARCODE_ID = "barcode_id";
    String COL_CREATED_AT = "created_at";
    String COL_GUEST_OF_BUYER = "guest_of_buyer";
    String COL_ALLOW_PDF_DOWNLOAD = "allow_pdf_download";
    String COL_SEAT_NUMBER = "seat_number";
    String COL_TICKET_TYPE_DESCRIPTION = "ticket_type_description";
    String COL_PRICE = "price";
    String COL_ORDER_STATUS = "order_status";
    String COL_TICKET_PDF_DESIGN = "ticket_PDF_design";
    String COL_TICKET_CUSTOM_PDF_DESIGN = "custom_ticket_pdf_design";
    String COL_CHART_KEY = "chart_key";
    String COL_TICKET_PURCHASER_ID = "ticket_purchaser_id";
    String COL_TICKET_HOLDER_ATTRIBUTES = "ticket_holder_attributes";

    //auto login column
    String COL_TOKEN = "token";

    String COL_EVENT_TICKET_MAIL_RESEND_TIME = "event_ticket_mail_resend_time";

    //buyer attributes
    String COL_ATTRIBUTE_NAME="attribute_name";
    String COL_ATTRIBUTE_VALUE_TYPE="attribute_value_type";

    // Session/Speaker task Column
    String COL_SCHEDULED_STATUS = "scheduled_status";
    String COL_EXECUTED_AT = "executed_at";
    String COL_TASK_ID = "task_id";
    String COL_ALLOW_BEFORE_TASK_IS_DUE = "allow_before_task_is_due";
    String COL_ASSIGNED_TO = "assigned_to";
    String COL_TASK_TYPE = "task_type";
    String COL_TITLE = "title";
    String COL_SPEAKER_ID = "speaker_id";
    String DISPLAY_VIEW_ID="display_view_id";
    String FILTER_TICKET_TYPES="filter_ticket_types";
    String FILTER_STATUS="filter_status";
    String ADVANCE_FILTER_JSON = "advance_filter_json";
    String ADVANCE_STATIC_FILTER_JSON = "advance_static_filter_json";
    String MERGE_TAG_PATTERN_CONTACT = "\\$[a-zA-Z][a-zA-Z0-9]*_\\d+";
}
