package com.accelevents.email.service;


import org.apache.commons.lang3.StringUtils;

public class QueryBuilder {

    public StringBuilder createEventAccessStartedQuery(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder(" SELECT CASE WHEN NOW() > DATE_ADD(t.event_start_date, INTERVAL -COALESCE(t.pre_event_access_minutes, 0) MINUTE) THEN TRUE ELSE FALSE END AS access_started ");
        queryBuilder.append(" FROM ticketing t ");
        queryBuilder.append(" WHERE t.event_id = ");
        queryBuilder.append(eventId);

        return queryBuilder;
    }

    public StringBuilder createMessageToContactTableQuery(Long messageToContactId) {
        StringBuilder queryBuilder = new StringBuilder(" SELECT mtc.message_body, mtc.subject_line, mtc.event_id, mtc.reply_to_email, ");
        queryBuilder.append(" mtc.send_to, mtc.ALL_MEMBERS_SELECTED, mtc.CONTACTS_LIST_ID, mtc.ticket_type_ids, mtc.status, ");
        queryBuilder.append(" mtc.dont_send_to ");
        queryBuilder.append(" FROM messages_to_contacts mtc ");
        queryBuilder.append(" WHERE mtc.id = ");
        queryBuilder.append(messageToContactId);

        return queryBuilder;
    }

    public StringBuilder createEventWithDesignAndTicketingTableQuery(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT e.name, e.eventurl, e.white_label, e.event_format,  ");
        queryBuilder.append(" e.timezone_id, e.equivalent_timezone, e.hide_event_date, e.currency, ");
        queryBuilder.append(" edd.notification_email, edd.reply_email, edd.email_sender_name, edd.event_calendar_invite, edd.logo_image, edd.header_logo_image, ");
        queryBuilder.append(" t.event_start_date, t.event_end_date, t.event_address, t.latitude, t.longitude, t.show_enter_event_button_in_reminder_template, ");
        queryBuilder.append(" t.ticket_PDF_design, t.chart_key, ");
        queryBuilder.append(" o.name AS org_name, o.contact_email_address AS org_contact_email, o.organizer_page_URL, ct.html_value as custom_ticket_pdf_design");
        queryBuilder.append(" FROM events e ");
        queryBuilder.append(" JOIN event_design_details edd ON e.event_id = edd.event_id ");
        queryBuilder.append(" JOIN ticketing t ON t.event_id = e.event_id  ");
        queryBuilder.append(" LEFT JOIN organizers o ON o.id = e.organizer_id  ");
        queryBuilder.append(" LEFT JOIN custom_templates ct ON ct.event_id = e.event_id  ");
        queryBuilder.append(" AND ct.template_type = 'TICKET_PDF_DESIGN' AND ct.is_default_page = 1 ");
        queryBuilder.append(" WHERE e.event_id = ");
        queryBuilder.append(eventId);

        return queryBuilder;
    }

    public StringBuilder createWhiteLabelTableQuery(Long whiteLabelId) {
        StringBuilder queryBuilder = new StringBuilder(" SELECT wl.white_label_url, wl.firm_name, wl.mail_api_key, wl.notification_email, ");
        queryBuilder.append(" wl.support_email, wl.transactional_email, wl.host_base_url,  ");
        queryBuilder.append(" wl.header_logo_image, wl.logo_image, wl.get_started, wl.help_center_URL, wl.privacy_policy,  ");
        queryBuilder.append(" wl.facebook_share, wl.twitter_share, wl.linkedin_share, wl.instagram_share, ");
        queryBuilder.append(" cp.plan_name   ");
        queryBuilder.append(" FROM white_label wl  ");
        queryBuilder.append(" LEFT JOIN chargebee_plans cp ON cp.id = wl.plan_id  ");
        queryBuilder.append(" WHERE wl.id = ");
        queryBuilder.append(whiteLabelId);

        return queryBuilder;
    }

    public StringBuilder getUserDataFromEmailQuery(String userEmail) {
        StringBuilder queryBuilder = new StringBuilder(" SELECT u.email, u.first_name, u.last_name, u.user_id ");
        queryBuilder.append(" FROM users u ");
        queryBuilder.append(" WHERE u.email = ");
        queryBuilder.append("'");
        queryBuilder.append(userEmail);
        queryBuilder.append("'");

        return queryBuilder;
    }

    public StringBuilder getEmailSuppressionForEventQuery(Long eventId){
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT es.email ");
        queryBuilder.append(" FROM email_suppressions es ");
        queryBuilder.append(" WHERE es.event_id = ");
        queryBuilder.append(eventId);

        return queryBuilder;
    }

    private StringBuilder commonFilterEventTicketsBlock(Long eventId){
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" WITH filtered_event_tickets AS ( ");
        queryBuilder.append("     SELECT ");
        queryBuilder.append("         et.id AS ticket_number, ");
        queryBuilder.append("         et.ticket_order_id, ");
        queryBuilder.append("         et.auto_assigned_attendee_number_id, ");
        queryBuilder.append("         ett.ticket_type_name, ");
        queryBuilder.append("         et.h_email, ");
        queryBuilder.append("         tord.order_status, ");
        queryBuilder.append("         ROW_NUMBER() OVER (PARTITION BY et.h_email ORDER BY et.id) AS row_num ");
        queryBuilder.append("     FROM ");
        queryBuilder.append("         event_tickets et ");
        queryBuilder.append("     JOIN ");
        queryBuilder.append("         event_ticket_type ett ON et.ticketing_type_id = ett.id ");
        queryBuilder.append("     JOIN ");
        queryBuilder.append("         ticketing_order tord ON et.ticket_order_id = tord.id ");
        queryBuilder.append("     WHERE et.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append("       AND et.ticket_status  not in ('CANCELED','DELETED') ");
        queryBuilder.append("          ");
        queryBuilder.append(" ), ");
        queryBuilder.append(" selected_event_tickets AS ( ");
        queryBuilder.append("     SELECT ");
        queryBuilder.append("         ticket_number, ");
        queryBuilder.append("         ticket_order_id, ");
        queryBuilder.append("         auto_assigned_attendee_number_id, ");
        queryBuilder.append("         ticket_type_name, ");
        queryBuilder.append("         h_email, ");
        queryBuilder.append("         order_status ");
        queryBuilder.append("     FROM ");
        queryBuilder.append("         filtered_event_tickets ");
        queryBuilder.append("     WHERE ");
        queryBuilder.append("         row_num = 1 ");
        queryBuilder.append(" ) ");
        return queryBuilder;
    }

    public StringBuilder createAllContactsWithFirstTicketMappingDataQuery(Long eventId){
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append(" SELECT DISTINCT ");
        queryBuilder.append("     c.first_name, ");
        queryBuilder.append("     c.last_name, ");
        queryBuilder.append("     c.email, ");
        queryBuilder.append("     etfm.ticket_order_id, ");
        queryBuilder.append("     etfm.ticket_number, ");
        queryBuilder.append("     etfm.ticket_type_name, ");
        queryBuilder.append("     etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("     etfm.order_status ");
        queryBuilder.append(" FROM ");
        queryBuilder.append("     contacts c ");
        queryBuilder.append(" LEFT JOIN ");
        queryBuilder.append("     selected_event_tickets etfm ON c.email = etfm.h_email ");
        queryBuilder.append(" WHERE ");
        queryBuilder.append("     c.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append("     AND c.rec_status != 'DELETE' ");

        return queryBuilder;
    }

    public StringBuilder checkSmartContactList(Long contactListId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT display_view_id FROM contacts_list ");
        queryBuilder.append("WHERE id =");
        queryBuilder.append(contactListId);
        return queryBuilder;
    }

    public StringBuilder findViewFilterDetails(Long displayViewId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT filter_ticket_types,filter_status,advance_filter_json,advance_static_filter_json FROM view_filter_details ");
        queryBuilder.append("WHERE view_id =");
        queryBuilder.append(displayViewId);
        return queryBuilder;
    }

    public StringBuilder createSmartContactListWithFirstTicketMappingFromContactListDataQuery(Long eventId,String ticketTypeIds,String eventTicketStatus, String advanceFilterConditions){
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append("SELECT DISTINCT u.first_name,u.last_name,u.email, ");
        queryBuilder.append(" etfm.ticket_order_id, etfm.ticket_number, etfm.ticket_type_name, etfm.auto_assigned_attendee_number_id, etfm.order_status, ");
        queryBuilder.append(" et.guest_of_buyer, tha.json_value AS ticket_holder_attributes ");
        queryBuilder.append(" FROM users u ");
        queryBuilder.append(" JOIN event_tickets et ON et.holder_user_id=u.user_id ");
        queryBuilder.append(" JOIN ticketing_order AS tord ON et.ticket_order_id = tord.id ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON tha.id = et.ticket_holder_attributes_id ");
        queryBuilder.append(" LEFT JOIN selected_event_tickets etfm ON u.email = etfm.h_email ");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(eventId);
        if (StringUtils.isNotBlank(advanceFilterConditions)) {
            queryBuilder.append(" ").append(advanceFilterConditions);
        }
        queryBuilder.append(" AND (COALESCE(").append(ticketTypeIds).append(") IS NULL OR et.ticketing_type_id in (").append(ticketTypeIds).append(")) ");

        if (eventTicketStatus == null) {
            queryBuilder.append(" AND et.ticket_status <> 'DELETED' ");
        } else {
            queryBuilder.append(" AND et.ticket_status IN (").append(eventTicketStatus).append(") ");
        }

        return queryBuilder;
    }
    public StringBuilder createContactsWithFirstTicketMappingFromContactListDataQuery(Long eventId, Long contactListId){
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append("  SELECT DISTINCT ");
        queryBuilder.append("      c.first_name,  ");
        queryBuilder.append("      c.last_name,  ");
        queryBuilder.append("      c.email,  ");
        queryBuilder.append("      c.phone_number,  ");
        queryBuilder.append("      etfm.ticket_order_id,  ");
        queryBuilder.append("      etfm.ticket_number,  ");
        queryBuilder.append("      etfm.ticket_type_name,  ");
        queryBuilder.append("      etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("      etfm.order_status, ");
        queryBuilder.append("      c.contact_attribute, ");
        queryBuilder.append("      cfad.json_value ");
        queryBuilder.append("  FROM  ");
        queryBuilder.append("      contacts c  ");
        queryBuilder.append("  JOIN contacts_list_mapping clm ON c.id = clm.contacts_id ");
        queryBuilder.append("  LEFT JOIN  ");
        queryBuilder.append("      selected_event_tickets etfm ON c.email = etfm.h_email  ");
        queryBuilder.append("  LEFT JOIN  ");
        queryBuilder.append("      custom_form_attribute_data cfad ON c.contact_attribute = cfad.id  ");
        queryBuilder.append("  WHERE  ");
        queryBuilder.append("      c.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append("     AND clm.contacts_list_id = ");
        queryBuilder.append(contactListId);
        queryBuilder.append("     AND c.rec_status != 'DELETE' ");
        return queryBuilder;
    }

    public StringBuilder createSelectedContactsFromListWithFirstTicketMappingDataQuery(Long eventId, Long messageToContactId, Long contactListId){
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append("  SELECT DISTINCT ");
        queryBuilder.append("      c.first_name,  ");
        queryBuilder.append("      c.last_name,  ");
        queryBuilder.append("      c.email,  ");
        queryBuilder.append("      c.phone_number,  ");
        queryBuilder.append("      etfm.ticket_order_id,  ");
        queryBuilder.append("      etfm.ticket_number,  ");
        queryBuilder.append("      etfm.ticket_type_name,  ");
        queryBuilder.append("      etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("      etfm.order_status, ");
        queryBuilder.append("      c.contact_attribute, ");
        queryBuilder.append("      cfad.json_value ");
        queryBuilder.append("   FROM messages_to_contacts mtc  ");
        queryBuilder.append(" 	JOIN contacts_list_mapping clm on clm.contacts_list_id = mtc.contacts_list_id  ");
        queryBuilder.append(" 	JOIN contacts c on c.id = clm.contacts_id   ");
        queryBuilder.append(" 	LEFT JOIN  ");
        queryBuilder.append(" 	     selected_event_tickets etfm ON c.email = etfm.h_email  ");
        queryBuilder.append(" 	LEFT JOIN  ");
        queryBuilder.append(" 	     custom_form_attribute_data cfad ON c.contact_attribute = cfad.id  ");
        queryBuilder.append(" 	WHERE mtc.id = ");
        queryBuilder.append(messageToContactId);
        queryBuilder.append(" 	AND clm.contacts_list_id = ");
        queryBuilder.append(contactListId);
        queryBuilder.append(" 	AND FIND_IN_SET(clm.contacts_id, mtc.MEMBERS_SELECTED) ");
        queryBuilder.append(" 	AND clm.rec_status != 'DELETE' ");
        return queryBuilder;
    }

    public StringBuilder createCustomContactsForEventWithFirstTicketMappingDataQuery(Long eventId, Long messageToContactId){
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append("  SELECT DISTINCT ");
        queryBuilder.append("      c.first_name,  ");
        queryBuilder.append("      c.last_name,  ");
        queryBuilder.append("      c.email,  ");
        queryBuilder.append("      etfm.ticket_order_id,  ");
        queryBuilder.append("      etfm.ticket_number,  ");
        queryBuilder.append("      etfm.ticket_type_name,  ");
        queryBuilder.append("      etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("      etfm.order_status ");
        queryBuilder.append("   FROM messages_to_contacts mtc  ");
        queryBuilder.append(" 	JOIN contacts c on c.event_id = mtc.event_id  ");
        queryBuilder.append(" 	LEFT JOIN  ");
        queryBuilder.append(" 	     selected_event_tickets etfm ON c.email = etfm.h_email  ");
        queryBuilder.append(" 	WHERE mtc.id =  ");
        queryBuilder.append(messageToContactId);
        queryBuilder.append(" 	AND c.event_id =  ");
        queryBuilder.append(eventId);
        queryBuilder.append(" 	AND FIND_IN_SET(c.id, mtc.MEMBERS_SELECTED) ");
        queryBuilder.append(" 	AND c.rec_status != 'DELETE' ");
        return queryBuilder;
    }

    public StringBuilder allTicketHolderAndBuyerDataQuery(Long eventId){
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT  ");
        queryBuilder.append(" 	et.h_first_name AS first_name, ");
        queryBuilder.append(" 	et.h_last_name AS last_name, ");
        queryBuilder.append(" 	et.h_email AS email, ");
        queryBuilder.append(" 	et.id AS ticket_number,  ");
        queryBuilder.append(" 	et.ticket_order_id,  ");
        queryBuilder.append(" 	et.auto_assigned_attendee_number_id,  ");
        queryBuilder.append(" 	ett.ticket_type_name,  ");
        queryBuilder.append(" 	et.guest_of_buyer,  ");
        queryBuilder.append(" 	tha.json_value AS ticket_holder_attributes,  ");
        queryBuilder.append(" 	tord.order_status  ");
        queryBuilder.append(" FROM event_tickets et ");
        queryBuilder.append(" JOIN event_ticket_type ett ON et.ticketing_type_id = ett.id ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id ");
        queryBuilder.append(" JOIN ticketing_order tord ON et.ticket_order_id = tord.id ");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append("   AND et.ticket_status NOT IN ('CANCELED','DELETED') ");
        return queryBuilder;
    }

    public StringBuilder allModuleWinnersDataQuery(Long eventId, String moduleType){
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append("  SELECT DISTINCT ");
        queryBuilder.append(" 	u.first_name, ");
        queryBuilder.append(" 	u.last_name, ");
        queryBuilder.append(" 	u.email,  ");
        queryBuilder.append("   etfm.ticket_order_id,  ");
        queryBuilder.append("   etfm.ticket_number,  ");
        queryBuilder.append("   etfm.ticket_type_name,  ");
        queryBuilder.append("   etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("   etfm.order_status ");
        queryBuilder.append(" FROM winners w  ");
        queryBuilder.append(" JOIN users u on w.user_id = u.user_id ");
        queryBuilder.append(" LEFT JOIN ");
        queryBuilder.append(" 	selected_event_tickets etfm ON u.email = etfm.h_email ");
        queryBuilder.append(" WHERE w.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND w.module_type = ");
        queryBuilder.append("'");
        queryBuilder.append(moduleType);
        queryBuilder.append("'");
        return queryBuilder;
    }

    public StringBuilder allRaffleParticipantsDataQuery(Long eventId){
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append(" SELECT DISTINCT ");
        queryBuilder.append(" 	u.first_name, ");
        queryBuilder.append(" 	u.last_name, ");
        queryBuilder.append(" 	u.email,  ");
        queryBuilder.append("   etfm.ticket_order_id,  ");
        queryBuilder.append("   etfm.ticket_number,  ");
        queryBuilder.append("   etfm.ticket_type_name,  ");
        queryBuilder.append("   etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("   etfm.order_status ");
        queryBuilder.append(" FROM purchased_raffle_tickets prt  ");
        queryBuilder.append(" JOIN raffles r on r.id = prt.raffle_id  ");
        queryBuilder.append(" JOIN users u on u.user_id = prt.user_id ");
        queryBuilder.append(" LEFT JOIN ");
        queryBuilder.append(" 	selected_event_tickets etfm ON u.email = etfm.h_email ");
        queryBuilder.append(" WHERE r.event_id =  ");
        queryBuilder.append(eventId);
        return queryBuilder;
    }

    public StringBuilder allTextToGiveDonorsDataQuery(Long eventId) {
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append(" SELECT DISTINCT  ");
        queryBuilder.append(" 	u.first_name, ");
        queryBuilder.append(" 	u.last_name, ");
        queryBuilder.append(" 	u.email,  ");
        queryBuilder.append("   etfm.ticket_order_id,  ");
        queryBuilder.append("   etfm.ticket_number,  ");
        queryBuilder.append("   etfm.ticket_type_name,  ");
        queryBuilder.append("   etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("   etfm.order_status ");
        queryBuilder.append(" FROM donations d  ");
        queryBuilder.append(" JOIN users u on u.user_id = d.user_id ");
        queryBuilder.append(" LEFT JOIN ");
        queryBuilder.append(" 	selected_event_tickets etfm ON u.email = etfm.h_email ");
        queryBuilder.append(" WHERE d.event_id = ");
        queryBuilder.append(eventId);
        return queryBuilder;
    }

    public StringBuilder auctionParticipantsDataQuery(Long eventId) {
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append(" SELECT DISTINCT  ");
        queryBuilder.append(" 	u.first_name, ");
        queryBuilder.append(" 	u.last_name, ");
        queryBuilder.append(" 	u.email,  ");
        queryBuilder.append("   etfm.ticket_order_id,  ");
        queryBuilder.append("   etfm.ticket_number,  ");
        queryBuilder.append("   etfm.ticket_type_name,  ");
        queryBuilder.append("   etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("   etfm.order_status ");
        queryBuilder.append(" FROM auction_bids ab  ");
        queryBuilder.append(" JOIN users u on u.user_id = ab.user_id  ");
        queryBuilder.append(" JOIN auctions a on a.id = ab.auction_id  ");
        queryBuilder.append(" LEFT JOIN ");
        queryBuilder.append(" 	selected_event_tickets etfm ON u.email = etfm.h_email ");
        queryBuilder.append(" WHERE a.event_id = ");
        queryBuilder.append(eventId);
        return queryBuilder;
    }

    public StringBuilder allFundANeedPledgesDataQuery(Long eventId) {
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append(" SELECT DISTINCT ");
        queryBuilder.append(" 	u.first_name,");
        queryBuilder.append(" 	u.last_name,");
        queryBuilder.append(" 	u.email, ");
        queryBuilder.append("   etfm.ticket_order_id, ");
        queryBuilder.append("   etfm.ticket_number, ");
        queryBuilder.append("   etfm.ticket_type_name, ");
        queryBuilder.append("   etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("   etfm.order_status");
        queryBuilder.append(" FROM pledge p ");
        queryBuilder.append(" JOIN cause_auctions ca ON ca.id = p.cause_auction_id ");
        queryBuilder.append(" JOIN users u on u.user_id = p.user_id ");
        queryBuilder.append(" LEFT JOIN");
        queryBuilder.append(" 	selected_event_tickets etfm ON u.email = etfm.h_email");
        queryBuilder.append(" WHERE ca.event_id = ");
        queryBuilder.append(eventId);
        return queryBuilder;
    }

    public StringBuilder allTicketHolderByTicketTypeDataQuery(Long eventId, String ticketTypeIdsCommaString){
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT  ");
        queryBuilder.append(" 	et.h_first_name AS first_name, ");
        queryBuilder.append(" 	et.h_last_name AS last_name, ");
        queryBuilder.append(" 	et.h_email AS email, ");
        queryBuilder.append(" 	et.id AS ticket_number,  ");
        queryBuilder.append(" 	et.ticket_order_id,  ");
        queryBuilder.append(" 	et.auto_assigned_attendee_number_id,  ");
        queryBuilder.append(" 	ett.ticket_type_name,  ");
        queryBuilder.append(" 	et.guest_of_buyer,  ");
        queryBuilder.append(" 	tha.json_value AS ticket_holder_attributes  ");
        queryBuilder.append(" FROM  ");
        queryBuilder.append("     event_tickets et  ");
        queryBuilder.append(" JOIN  ");
        queryBuilder.append("     event_ticket_type ett ON et.ticketing_type_id = ett.id  ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id = tha.id ");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(eventId);

        queryBuilder.append("   AND et.ticket_status NOT IN ('CANCELED','DELETED') ");
        queryBuilder.append("   AND et.ticketing_type_id IN ( ");
        queryBuilder.append(ticketTypeIdsCommaString);
        queryBuilder.append(" ) ");
        return queryBuilder;
    }

    public StringBuilder allSpeakersDataQuery(Long eventId) {
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append(" SELECT DISTINCT ");
        queryBuilder.append(" 	s.first_name,");
        queryBuilder.append(" 	s.last_name,");
        queryBuilder.append(" 	s.email, ");
        queryBuilder.append("   etfm.ticket_order_id, ");
        queryBuilder.append("   etfm.ticket_number, ");
        queryBuilder.append("   etfm.ticket_type_name, ");
        queryBuilder.append("   etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("   etfm.order_status");
        queryBuilder.append(" FROM speakers s ");
        queryBuilder.append(" LEFT JOIN");
        queryBuilder.append(" 	selected_event_tickets etfm ON s.email = etfm.h_email");
        queryBuilder.append(" WHERE s.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" 	AND s.speaker_status != 'DELETE' ");
        return queryBuilder;
    }

    public StringBuilder allExhibitorsStaffDataQuery(Long eventId) {
        StringBuilder queryBuilder = this.commonFilterEventTicketsBlock(eventId);
        queryBuilder.append(" SELECT DISTINCT ");
        queryBuilder.append(" 	u.first_name,");
        queryBuilder.append(" 	u.last_name,");
        queryBuilder.append(" 	u.email, ");
        queryBuilder.append("   etfm.ticket_order_id, ");
        queryBuilder.append("   etfm.ticket_number, ");
        queryBuilder.append("   etfm.ticket_type_name, ");
        queryBuilder.append("   etfm.auto_assigned_attendee_number_id, ");
        queryBuilder.append("   etfm.order_status");
        queryBuilder.append(" FROM staff s ");
        queryBuilder.append(" JOIN users u on s.user_id = u.user_id ");
        queryBuilder.append(" LEFT JOIN");
        queryBuilder.append(" 	selected_event_tickets etfm ON u.email = etfm.h_email");
        queryBuilder.append(" WHERE s.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND s.`role` IN ('exhibitoradmin','leadretriever') ");
        queryBuilder.append(" AND s.rec_status != 'DELETE' ");
        return queryBuilder;
    }

    public StringBuilder allCheckedInAttendeesDataQuery(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT DISTINCT  ");
        queryBuilder.append(" 	et.h_first_name AS first_name, ");
        queryBuilder.append(" 	et.h_last_name AS last_name, ");
        queryBuilder.append(" 	et.h_email AS email, ");
        queryBuilder.append(" 	et.id AS ticket_number, ");
        queryBuilder.append(" 	et.ticket_order_id, ");
        queryBuilder.append(" 	et.auto_assigned_attendee_number_id, ");
        queryBuilder.append(" 	ett.ticket_type_name, ");
        queryBuilder.append(" 	et.event_id, ");
        queryBuilder.append(" 	et.guest_of_buyer, ");
        queryBuilder.append(" 	tha.json_value AS ticket_holder_attributes ");
        queryBuilder.append(" FROM check_in_audit_log cial  ");
        queryBuilder.append(" JOIN event_tickets et on et.id = cial.event_ticket_id  ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id ");
        queryBuilder.append(" JOIN  ");
        queryBuilder.append(" 	event_ticket_type ett on ett.id = et.ticketing_type_id ");
        queryBuilder.append(" WHERE  ");
        queryBuilder.append(" 	cial.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append("   AND et.ticket_status not in ('CANCELED','DELETED') ");
        queryBuilder.append(" AND cial.checkin_source IN ('HYBRID_CHECKIN','IN_PERSON','VIRTUAL_EVENT_PORTAL') ");
        return queryBuilder;
    }

    public StringBuilder allNotCheckedInAttendeesDataQuery(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT DISTINCT  ");
        queryBuilder.append(" 	et.h_first_name AS first_name, ");
        queryBuilder.append(" 	et.h_last_name AS last_name, ");
        queryBuilder.append(" 	et.h_email AS email, ");
        queryBuilder.append(" 	et.id AS ticket_number, ");
        queryBuilder.append(" 	et.ticket_order_id, ");
        queryBuilder.append(" 	et.auto_assigned_attendee_number_id, ");
        queryBuilder.append(" 	ett.ticket_type_name, ");
        queryBuilder.append(" 	et.event_id, ");
        queryBuilder.append(" 	et.guest_of_buyer, ");
        queryBuilder.append(" 	tha.json_value AS ticket_holder_attributes ");
        queryBuilder.append(" FROM event_tickets et   ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id ");
        queryBuilder.append(" JOIN  ");
        queryBuilder.append(" 	event_ticket_type ett on ett.id = et.ticketing_type_id ");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND et.ticket_status not in ('CANCELED','DELETED') ");
        queryBuilder.append(" AND et.id NOT IN ( ");
        queryBuilder.append(" 	SELECT cial.event_ticket_id FROM check_in_audit_log cial  ");
        queryBuilder.append(" 	WHERE cial.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" 	AND cial.event_ticket_id IS NOT NULL ");
        queryBuilder.append(" ) ");
        return queryBuilder;
    }

    public StringBuilder getResendTicketingEmailQuery(Long resendTicketingEmailId) {
        StringBuilder queryBuilder = new StringBuilder(" SELECT ");
        queryBuilder.append(" rte.event_id, rte.recurring_event_id, rte.resend_ticket_order_text, ");
        queryBuilder.append(" rte.resend_ticket_subject, rte.resend_ticket_status, rte.recipient_type, ");
        queryBuilder.append(" ct.html_value, ct.json_value, ct.is_advance_email_builder, ct.allowed_ticket_types_for_reminder  ");
        queryBuilder.append(" FROM resend_ticketing_email rte ");
        queryBuilder.append(" LEFT JOIN custom_templates ct ON rte.custom_email_id = ct.id ");
        queryBuilder.append(" WHERE rte.id = ");
        queryBuilder.append(resendTicketingEmailId);

        return queryBuilder;
    }

    public StringBuilder getAllowDownloadPdfTicketForTestEmailQuery(Long eventId,String allowedTicketTypeIds){
        StringBuilder queryBuilder = new StringBuilder("SELECT ");
        queryBuilder.append(" allow_pdf_download ");
        queryBuilder.append(" FROM event_ticket_type ");
        queryBuilder.append(" WHERE id IN (");
        queryBuilder.append(allowedTicketTypeIds);
        queryBuilder.append(" ) ");
        queryBuilder.append(" AND event_id = ").append(eventId);

        return queryBuilder;
    }

    public StringBuilder getAllEventTicketsDataQueryForReminderEmail(String allowedTicketTypeIds, Long eventId, String recipientType){
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT  ");
        queryBuilder.append(" et.h_first_name, et.h_last_name, et.h_email, et.holder_user_id, et.ticket_purchaser_id, ");
        queryBuilder.append(" et.id, et.ticket_order_id, to2.order_status, ");
        queryBuilder.append(" et.auto_assigned_attendee_number_id, et.barcode_id, et.created_at, et.guest_of_buyer, et.seat_number, ");
        queryBuilder.append(" ett.ticket_type_name, ett.ticket_type_description, ett.allow_pdf_download, ett.price, ");
        queryBuilder.append(" u.first_name, u.last_name, u.email, tha.json_value , ct.html_value as custom_ticket_pdf_design");
        queryBuilder.append(" FROM event_tickets et ");
        queryBuilder.append(" JOIN ticketing_order to2 on to2.id = et.ticket_order_id   ");
        queryBuilder.append(" JOIN event_ticket_type ett ON et.ticketing_type_id = ett.id  ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha on tha.id = et.ticket_holder_attributes_id  ");
        queryBuilder.append(" JOIN users u on u.user_id = et.ticket_purchaser_id  ");
        queryBuilder.append("LEFT JOIN custom_templates ct on ct.id = ett.custom_ticket_pdf_design_id");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND et.ticketing_type_id in ( ");
        queryBuilder.append(allowedTicketTypeIds);
        queryBuilder.append(" ) ");
        queryBuilder.append(" AND et.ticket_status not in ('CANCELED','DELETED') ");

        switch (recipientType) {
            case "ALL_REGISTRANTS":
                break;

            case "NOT_CHECKED_IN_REGISTRANTS":
                queryBuilder.append(" AND et.check_in_date IS NULL ");
                break;

            case "CHECKED_IN_REGISTRANTS":
                queryBuilder.append(" AND et.check_in_date IS NOT NULL ");
                break;

            case "GUEST_OF_BUYER_REGISTRANTS":
                queryBuilder.append(" AND et.guest_of_buyer = TRUE ");
                break;

            default:
                throw new IllegalArgumentException("Invalid recipient type: " + recipientType);
        }

        return queryBuilder;
    }

    public StringBuilder createAutoLoginQuery(Long userId, Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT al.token ");
        queryBuilder.append(" FROM auto_login al ");
        queryBuilder.append(" WHERE al.user_id = ");
        queryBuilder.append(userId);
        queryBuilder.append(" AND al.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND al.link_type = 'EVENT_LEVEL' ");
        queryBuilder.append(" AND al.login_link = 'MAGIC_LINK' ");

        return queryBuilder;
    }

    public StringBuilder createMessageToContactTableScheduleQuery() {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT mtc.id, mtc.scheduled_at ");
        queryBuilder.append(" FROM messages_to_contacts mtc ");
        queryBuilder.append(" JOIN events e on e.event_id = mtc.event_id ");
        queryBuilder.append(" WHERE mtc.`type` = 'EMAIL'  ");
        queryBuilder.append(" AND mtc.rec_status != 'DELETE' ");
        queryBuilder.append(" AND (e.event_status IS NULL OR e.event_status != 'EVENT_DELETED') ");
        queryBuilder.append(" AND mtc.date_sent IS NULL ");
        queryBuilder.append(" AND mtc.scheduled_at IS NOT NULL ");
        queryBuilder.append(" AND mtc.status = 'SCHEDULED' ");
        queryBuilder.append(" AND mtc.scheduled_at <= CURRENT_TIMESTAMP() ");

        return queryBuilder;
    }

    public StringBuilder createResendTicketingEmailTableScheduleQuery() {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT rte.id, rte.event_ticket_mail_resend_time   ");
        queryBuilder.append(" FROM resend_ticketing_email rte ");
        queryBuilder.append(" JOIN events e on rte.event_id = e.event_id  ");
        queryBuilder.append(" WHERE rte.resend_ticket_status = 'SCHEDULED' ");
        queryBuilder.append(" AND (e.event_status IS NULL OR e.event_status != 'EVENT_DELETED') ");
        queryBuilder.append(" AND rte.execution_time IS NULL  ");
        queryBuilder.append(" AND rte.event_ticket_mail_resend_time <= CURRENT_TIMESTAMP() ");

        return queryBuilder;
    }

    public StringBuilder getAllTicketHolderRequiredAttributesByIdsAndEventId(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT id, attribute_name, attribute_value_type, enabled_for_ticket_purchaser, enabled_for_ticket_holder ");
        queryBuilder.append("FROM ticket_holder_required_attributes  ");
        queryBuilder.append("WHERE event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND (enabled_for_ticket_purchaser = 1 OR enabled_for_ticket_holder = 1) ");
        queryBuilder.append(" AND (rec_status != 'CANCEL' or rec_status is NULL) ");
        return queryBuilder;
    }

    public StringBuilder noBalanceDueAttendeesDataQuery(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT DISTINCT  ");
        queryBuilder.append(" 	u.first_name AS first_name, ");
        queryBuilder.append(" 	u.last_name AS last_name, ");
        queryBuilder.append(" 	tord.buyer_email AS email, ");
        queryBuilder.append(" 	et.id AS ticket_number, ");
        queryBuilder.append(" 	et.ticket_order_id, ");
        queryBuilder.append(" 	et.auto_assigned_attendee_number_id, ");
        queryBuilder.append(" 	ett.ticket_type_name, ");
        queryBuilder.append(" 	et.event_id, ");
        queryBuilder.append(" 	tord.order_status, ");
        queryBuilder.append(" 	et.guest_of_buyer, ");
        queryBuilder.append(" 	tha.json_value AS ticket_holder_attributes ");
        queryBuilder.append(" FROM event_tickets et ");
        queryBuilder.append(" JOIN event_ticket_type ett ON ett.id = et.ticketing_type_id ");
        queryBuilder.append(" JOIN ticketing_order tord ON et.ticket_order_id = tord.id ");
        queryBuilder.append(" JOIN users u ON tord.purchaser_id = u.user_id ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id ");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND et.ticket_status NOT IN ('CANCELED','DELETED') ");
        queryBuilder.append(" AND et.data_type = 'TICKET' ");
        queryBuilder.append(" AND tord.buyer_email IS NOT NULL ");
        queryBuilder.append(" AND tord.buyer_email != '' ");
        queryBuilder.append(" AND (");
        queryBuilder.append("   (ett.ticket_type = 'FREE') OR ");
        queryBuilder.append("   (ett.ticket_type = 'COMPLIMENTARY') OR ");
        queryBuilder.append("   (ett.ticket_type = 'PAID' AND et.ticket_payment_status = 'PAID') OR ");
        queryBuilder.append("   (ett.ticket_type = 'PAID' AND et.ticket_price = 0) OR ");
        queryBuilder.append("   (ett.require_deposit_amount = true ");
        queryBuilder.append("       AND et.ticket_payment_status = 'PAID') OR ");
        queryBuilder.append("   (tord.order_type = 'PAY_LATER' AND et.ticket_payment_status = 'PAID') ");
        queryBuilder.append(" ) ");
        return queryBuilder;
    }

    public StringBuilder balanceDueAttendeesDataQuery(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT DISTINCT  ");
        queryBuilder.append(" 	u.first_name AS first_name, ");
        queryBuilder.append(" 	u.last_name AS last_name, ");
        queryBuilder.append(" 	tord.buyer_email AS email, ");
        queryBuilder.append(" 	et.id AS ticket_number, ");
        queryBuilder.append(" 	et.ticket_order_id, ");
        queryBuilder.append(" 	et.auto_assigned_attendee_number_id, ");
        queryBuilder.append(" 	ett.ticket_type_name, ");
        queryBuilder.append(" 	et.event_id, ");
        queryBuilder.append(" 	tord.order_status, ");
        queryBuilder.append(" 	et.guest_of_buyer, ");
        queryBuilder.append(" 	tha.json_value AS ticket_holder_attributes ");
        queryBuilder.append(" FROM event_tickets et ");
        queryBuilder.append(" JOIN event_ticket_type ett ON ett.id = et.ticketing_type_id ");
        queryBuilder.append(" JOIN ticketing_order tord ON et.ticket_order_id = tord.id ");
        queryBuilder.append(" JOIN users u ON tord.purchaser_id = u.user_id ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id ");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND et.ticket_status NOT IN ('CANCELED','DELETED') ");
        queryBuilder.append(" AND et.data_type = 'TICKET' ");
        queryBuilder.append(" AND ett.ticket_type <> 'COMPLIMENTARY' ");
        queryBuilder.append(" AND tord.buyer_email IS NOT NULL ");
        queryBuilder.append(" AND tord.buyer_email != '' ");
        queryBuilder.append(" AND ( ");
        queryBuilder.append("   (et.ticket_payment_status IN ('UNPAID', 'PARTIALLY_PAID')) ");
        queryBuilder.append("   OR (tord.order_type = 'PAY_LATER' AND et.ticket_payment_status = 'UNPAID') ");
        queryBuilder.append("   OR (ett.require_deposit_amount = true ");
        queryBuilder.append("       AND et.ticket_payment_status IN ('UNPAID', 'PARTIALLY_PAID') ");
        queryBuilder.append("       AND (et.paid_amount < et.ticket_price OR et.paid_amount IS NULL)) ");
        queryBuilder.append("   OR (et.transfers_count > 0 ");
        queryBuilder.append("       AND et.ticket_payment_status IN ('UNPAID', 'PARTIALLY_PAID')) ");
        queryBuilder.append(" ) ");
        return queryBuilder;
    }

    public StringBuilder createEventTaskReminderEmailScheduleQuery() {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT ts.id, ts.scheduled_at  ");
        queryBuilder.append(" FROM task_schedule ts ");
        queryBuilder.append(" JOIN events e on ts.event_id = e.event_id  ");
        queryBuilder.append(" JOIN event_task et on ts.task_id = et.id  ");
        queryBuilder.append(" WHERE ts.scheduled_status = 'SCHEDULED' ");
        queryBuilder.append(" AND (e.event_status IS NULL OR e.event_status != 'EVENT_DELETED') ");
        queryBuilder.append(" AND ts.executed_at IS NULL AND ts.rec_status<>'DELETE' ");
        queryBuilder.append(" AND ts.scheduled_at <= CURRENT_TIMESTAMP() ");

        return queryBuilder;
    }

    public StringBuilder getEventTaskReminderEmailQuery(Long eventTaskReminderEmail) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT ts.task_id, ts.event_id, ts.allow_before_task_is_due, ");
        queryBuilder.append(" ts.scheduled_status, et.task_type, et.title ");
        queryBuilder.append(" FROM task_schedule ts ");
        queryBuilder.append(" JOIN event_task et ON ts.task_id = et.id ");
        queryBuilder.append(" WHERE ts.id = ");
        queryBuilder.append(eventTaskReminderEmail);

        return queryBuilder;
    }

    public StringBuilder getEventTaskEmailForManualSendQuery(Long taskId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT et.id AS task_id, et.title, et.task_type, et.event_id, ");
        queryBuilder.append(" CASE WHEN et.submission_due_date >= CURRENT_TIMESTAMP() THEN TRUE ELSE FALSE END AS allow_before_task_is_due, ");
        queryBuilder.append(" 'SCHEDULED' AS scheduled_status ");
        queryBuilder.append(" FROM event_task et ");
        queryBuilder.append(" WHERE et.id = ");
        queryBuilder.append(taskId);

        return queryBuilder;
    }

    public StringBuilder getEventTaskParticipantSpeakerDataAndSessionIdNullQuery(Long eventId, Long eventTaskId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT sp.first_name, sp.email, sp.user_id ");
        queryBuilder.append(" FROM task_participants tp ");
        queryBuilder.append(" JOIN speakers sp on tp.speaker_id = sp.id AND sp.speaker_status not in ('DELETE','DELETED') ");
        queryBuilder.append(" WHERE tp.rec_status <>'DELETE' AND tp.workflow_status ='PENDING' AND tp.session_id IS NULL AND tp.event_id =  ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND tp.task_id = ");
        queryBuilder.append(eventTaskId);

        return queryBuilder;
    }

    public StringBuilder getEventTaskParticipantSpeakerWithSessionDataQuery(Long eventId, Long eventTaskId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT sp.first_name, sp.email, sp.user_id, s.title  ");
        queryBuilder.append(" FROM task_participants tp ");
        queryBuilder.append(" JOIN sessions s on tp.session_id = s.id AND s.rec_status<>'DELETE' ");
        queryBuilder.append(" JOIN session_speaker ss on ss.session_id = s.id AND ss.rec_status<>'DELETE' ");
        queryBuilder.append(" JOIN speakers sp on ss.speaker_id = sp.id AND sp.speaker_status not in ('DELETE','DELETED') ");
        queryBuilder.append(" WHERE tp.rec_status <>'DELETE' AND tp.workflow_status ='PENDING' AND tp.event_id =  ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND tp.task_id = ");
        queryBuilder.append(eventTaskId);

        return queryBuilder;
    }

    public StringBuilder getEventTaskParticipantExhibitorDataAndSessionIdAndSpeakerIdNullQuery(Long eventId, Long eventTaskId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(" SELECT u.first_name, u.email, u.user_id ");
        queryBuilder.append(" FROM task_participants tp ");
        queryBuilder.append(" JOIN staff s on tp.exhibitor_id = s.exhibitor_id and s.rec_status <> 'DELETE' ");
        queryBuilder.append(" JOIN  users u on u.user_id = s.user_id ");
        queryBuilder.append(" WHERE tp.rec_status <>'DELETE' AND tp.workflow_status ='PENDING' AND tp.session_id IS NULL AND tp.speaker_id IS NULL AND tp.event_id =  ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND tp.task_id = ");
        queryBuilder.append(eventTaskId);

        return queryBuilder;
    }

    public StringBuilder getSessionUserIdsByEventQuery(Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT us.session_id, GROUP_CONCAT(us.user_id) AS user_id ");
        queryBuilder.append("FROM user_sessions us ");
        queryBuilder.append("JOIN sessions s ON s.id = us.session_id ");
        queryBuilder.append("WHERE s.event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND us.session_status = 'REGISTERED' ");
        queryBuilder.append("GROUP BY us.session_id");

        return queryBuilder;
    }

}
