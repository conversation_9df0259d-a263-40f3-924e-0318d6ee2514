package com.accelevents.email.mail;

import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.EMAIL_RECIPIENTS;
import com.accelevents.domain.enums.ReminderEmailRecipientsType;
import com.accelevents.domain.enums.TaskType;
import com.accelevents.dto.AttendeeAnalyticsAdvanceFilterDto;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.dto.lambda.EmailUserWithTicketDto;
import com.accelevents.dto.lambda.EventTaskUserEmailDto;
import com.accelevents.email.db.DBRequest;
import com.accelevents.email.service.AdvancedFilterService;
import com.accelevents.email.service.QueryBuilder;
import com.accelevents.email.utils.BulkLambdaConstants;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.email.utils.BulkLambdaConstants.*;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.isValidEmailAddress;

public class RecipientListDetailsService {

    private static final Logger log = LoggerFactory.getLogger(RecipientListDetailsService.class);

    private final DBRequest dbRequest;
    private final QueryBuilder queryBuilder;
    private final String imagePreFix = System.getenv("IMAGE_PREFIX");
    private final AdvancedFilterService advancedFilterService;

    public RecipientListDetailsService(DBRequest dbRequest, QueryBuilder queryBuilder, AdvancedFilterService advancedFilterService) {
        this.dbRequest = dbRequest;
        this.queryBuilder = queryBuilder;
        this.advancedFilterService = advancedFilterService;
    }

    public List<EmailUserWithTicketDto> getRecipientList(Long messageToContactId,
                                                         EMAIL_RECIPIENTS emailRecipients,
                                                         Long eventId, Long contactListId,
                                                         Boolean isAllSelectedContactsInList,
                                                         String ticketingTypeId) throws SQLException {


        List<EmailUserWithTicketDto> baseEmailUserWithTicketDto = new ArrayList<EmailUserWithTicketDto>();

        switch (emailRecipients) {
            case ALL_CONTACTS:
                ResultSet contactsWithTicketDataRs = dbRequest.getQueryResult(queryBuilder.createAllContactsWithFirstTicketMappingDataQuery(eventId).toString());
                while (contactsWithTicketDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(contactsWithTicketDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case CONTACT_LIST:
                log.info("getRecipientList contactListId {} isAllSelectedContactsInList {}",contactListId,isAllSelectedContactsInList);
                if(isAllSelectedContactsInList){
                    ResultSet checkSmartContactList = dbRequest.getQueryResult(queryBuilder.checkSmartContactList(contactListId).toString());
                    String displayViewId = null;
                    while (checkSmartContactList.next()) {
                        displayViewId = checkSmartContactList.getString(DISPLAY_VIEW_ID);
                    }
                    ResultSet contactListDataWithTicketDataRs;
                    log.info("getRecipientList contactListId {} displayViewId {}",contactListId,displayViewId);
                    if (displayViewId != null) {
                        ResultSet viewFilterDetails = dbRequest.getQueryResult(queryBuilder.findViewFilterDetails(Long.valueOf(displayViewId)).toString());
                        String filterTicketTypes = null;
                        String filterStatus = null;
                        String advancedFilterJson = null;
                        String advancedStaticFilterJson = null;
                        while (viewFilterDetails.next()) {
                            filterTicketTypes = viewFilterDetails.getString(FILTER_TICKET_TYPES);
                            filterStatus = viewFilterDetails.getString(FILTER_STATUS);
                            advancedFilterJson = viewFilterDetails.getString(ADVANCE_FILTER_JSON);
                            advancedStaticFilterJson = viewFilterDetails.getString(ADVANCE_STATIC_FILTER_JSON);
                        }

                        AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsAdvanceFilterDto = advancedFilterService.filterAllAttendeeByAdvanceFilter(advancedFilterJson);
                        AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsStaticAdvanceFilterDto = advancedFilterService.filterAllAttendeeByAdvanceFilter(advancedStaticFilterJson);
                        String advanceFilterConditions = advancedFilterService.buildQueryWithAdvanceFilterAttributes(attendeeAnalyticsAdvanceFilterDto, attendeeAnalyticsStaticAdvanceFilterDto, eventId);

                        filterStatus = filterStatus != null ? ("'" + filterStatus.replace(",", "','")) + "'" : null;
                        log.info("getRecipientList displayViewId {} filterStatus {} filterTicketTypes {} advanceFilterConditions {} ",displayViewId,filterStatus,filterTicketTypes,advanceFilterConditions);
                        String query = queryBuilder.createSmartContactListWithFirstTicketMappingFromContactListDataQuery(eventId, filterTicketTypes,filterStatus,advanceFilterConditions).toString();
                        contactListDataWithTicketDataRs = dbRequest.getQueryResult(query);
                    } else {
                        contactListDataWithTicketDataRs = dbRequest.getQueryResult(queryBuilder.createContactsWithFirstTicketMappingFromContactListDataQuery(eventId, contactListId).toString());
                    }
                    while (contactListDataWithTicketDataRs.next()) {
                        EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSetWithContactAttributes(contactListDataWithTicketDataRs);

                        // Extract and set CC emails directly from contactAttributeJsonValue
                        Set<String> ccEmails = extractCcEmailsFromContactAttributes(emailUserWithTicketDto.getContactAttributeJsonValue(), eventId);
                        emailUserWithTicketDto.setCcEmails(ccEmails);

                        // Process ticket holder attributes for smart contact list if available
                        if (displayViewId != null) {
                            String ticketHolderAttributesJson = contactListDataWithTicketDataRs.getString(TICKET_HOLDER_ATTRIBUTES);
                            processTicketHolderAttributes(emailUserWithTicketDto, ticketHolderAttributesJson, eventId);
                        }

                        baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                    }
                }else {
                    ResultSet selectedContactsWithTicketDataRs = dbRequest.getQueryResult(queryBuilder.createSelectedContactsFromListWithFirstTicketMappingDataQuery(eventId, messageToContactId, contactListId).toString());
                    while (selectedContactsWithTicketDataRs.next()) {
                        EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSetWithContactAttributes(selectedContactsWithTicketDataRs);
                        baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                    }
                }
                break;
            case CUSTOM_RECIPIENTS:
                ResultSet customContactsWithTicketDataRs = dbRequest.getQueryResult(queryBuilder.createCustomContactsForEventWithFirstTicketMappingDataQuery(eventId, messageToContactId).toString());
                while (customContactsWithTicketDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(customContactsWithTicketDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_TICKER_HOLDER_AND_BUYERS:
                ResultSet allTicketHolderAndBuyerDataRs = dbRequest.getQueryResult(queryBuilder.allTicketHolderAndBuyerDataQuery(eventId).toString());
                while (allTicketHolderAndBuyerDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoAndCheckTicketIsGuestOfBuyerFromResultSet(allTicketHolderAndBuyerDataRs);
                    String ticketHolderAttributesJson = allTicketHolderAndBuyerDataRs.getString(TICKET_HOLDER_ATTRIBUTES);

                    // Process ticket holder attributes using reusable method
                    processTicketHolderAttributes(emailUserWithTicketDto, ticketHolderAttributesJson, eventId);

                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_RAFFLE_WINNERS:
                ResultSet allRaffleWinnerDataRs = dbRequest.getQueryResult(queryBuilder.allModuleWinnersDataQuery(eventId, "RAFFLE").toString());
                while (allRaffleWinnerDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(allRaffleWinnerDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_RAFFLE_PARTICIPANTS:
                ResultSet allRaffleParticipantsDataRs = dbRequest.getQueryResult(queryBuilder.allRaffleParticipantsDataQuery(eventId).toString());
                while (allRaffleParticipantsDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(allRaffleParticipantsDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_TEXT_TO_GIVE_DONORS:
                ResultSet allTextToGiveDonorsDataRs = dbRequest.getQueryResult(queryBuilder.allTextToGiveDonorsDataQuery(eventId).toString());
                while (allTextToGiveDonorsDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(allTextToGiveDonorsDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_AUCTION_WINNERS:
                ResultSet allAuctionWinnerDataRs = dbRequest.getQueryResult(queryBuilder.allModuleWinnersDataQuery(eventId, "AUCTION").toString());
                while (allAuctionWinnerDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(allAuctionWinnerDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_AUCTION_PARTICIPANTS:
                ResultSet auctionParticipantsDataRs = dbRequest.getQueryResult(queryBuilder.auctionParticipantsDataQuery(eventId).toString());
                while (auctionParticipantsDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(auctionParticipantsDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_FUND_A_NEED_PLEDGERS:
                ResultSet allFuncANeedPledgersDataRs = dbRequest.getQueryResult(queryBuilder.allFundANeedPledgesDataQuery(eventId).toString());
                while (allFuncANeedPledgersDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(allFuncANeedPledgersDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case BY_TICKET_TYPE_VIA:
                if(StringUtils.isNotBlank(ticketingTypeId)) {
                    Pattern pattern = Pattern.compile(":");
                    List<Long> ticketingTypeIdList = pattern.splitAsStream(ticketingTypeId).map(Long::valueOf).collect(Collectors.toList());
                    StringJoiner joiner = new StringJoiner(",");
                    for (Long ttId : ticketingTypeIdList) {
                        joiner.add(ttId.toString());
                    }
                    String ticketingTypeIdsString = joiner.toString();
                    ResultSet allAttendeeByTicketTypeDataRs = dbRequest.getQueryResult(queryBuilder.allTicketHolderByTicketTypeDataQuery(eventId, ticketingTypeIdsString).toString());
                    while (allAttendeeByTicketTypeDataRs.next()) {
                        EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoAndCheckTicketIsGuestOfBuyerFromResultSet(allAttendeeByTicketTypeDataRs);

                        // Process ticket holder attributes using reusable method
                        String ticketHolderAttributesJson = allAttendeeByTicketTypeDataRs.getString(TICKET_HOLDER_ATTRIBUTES);
                        processTicketHolderAttributes(emailUserWithTicketDto, ticketHolderAttributesJson, eventId);

                        baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                    }
                }
                break;
            case ALL_SPEAKERS:
                ResultSet allSpeakersDataRs = dbRequest.getQueryResult(queryBuilder.allSpeakersDataQuery(eventId).toString());
                while (allSpeakersDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(allSpeakersDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case ALL_EXHIBITORS:
                ResultSet allExhibitorsDataRs = dbRequest.getQueryResult(queryBuilder.allExhibitorsStaffDataQuery(eventId).toString());
                while (allExhibitorsDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoFromResultSet(allExhibitorsDataRs);
                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case CHECKED_IN:
                ResultSet allCheckInUsersDataRs = dbRequest.getQueryResult(queryBuilder.allCheckedInAttendeesDataQuery(eventId).toString());
                while (allCheckInUsersDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoAndCheckTicketIsGuestOfBuyerFromResultSet(allCheckInUsersDataRs);

                    // Process ticket holder attributes
                    String ticketHolderAttributesJson = allCheckInUsersDataRs.getString(TICKET_HOLDER_ATTRIBUTES);
                    processTicketHolderAttributes(emailUserWithTicketDto, ticketHolderAttributesJson, eventId);

                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case NOT_CHECKED_IN:
                ResultSet allNotCheckInUsersDataRs = dbRequest.getQueryResult(queryBuilder.allNotCheckedInAttendeesDataQuery(eventId).toString());
                while (allNotCheckInUsersDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoAndCheckTicketIsGuestOfBuyerFromResultSet(allNotCheckInUsersDataRs);

                    // Process ticket holder attributes
                    String ticketHolderAttributesJson = allNotCheckInUsersDataRs.getString(TICKET_HOLDER_ATTRIBUTES);
                    processTicketHolderAttributes(emailUserWithTicketDto, ticketHolderAttributesJson, eventId);

                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case NO_BALANCE_DUE:
                ResultSet noBalanceDueUsersDataRs = dbRequest.getQueryResult(queryBuilder.noBalanceDueAttendeesDataQuery(eventId).toString());
                while (noBalanceDueUsersDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoAndCheckTicketIsGuestOfBuyerFromResultSet(noBalanceDueUsersDataRs);

                    // Process ticket holder attributes
                    String ticketHolderAttributesJson = noBalanceDueUsersDataRs.getString(TICKET_HOLDER_ATTRIBUTES);
                    processTicketHolderAttributes(emailUserWithTicketDto, ticketHolderAttributesJson, eventId);

                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            case BALANCE_DUE:
                ResultSet balanceDueUsersDataRs = dbRequest.getQueryResult(queryBuilder.balanceDueAttendeesDataQuery(eventId).toString());
                while (balanceDueUsersDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = this.generateEmailUserDtoAndCheckTicketIsGuestOfBuyerFromResultSet(balanceDueUsersDataRs);

                    // Process ticket holder attributes
                    String ticketHolderAttributesJson = balanceDueUsersDataRs.getString(TICKET_HOLDER_ATTRIBUTES);
                    processTicketHolderAttributes(emailUserWithTicketDto, ticketHolderAttributesJson, eventId);

                    baseEmailUserWithTicketDto.add(emailUserWithTicketDto);
                }
                break;
            default:
                log.error("emailRecipients doesn't match with enum");
        }

        List<String> emailSuppressionList = getEmailSuppressionList(eventId);

        if(!emailSuppressionList.isEmpty()) {
            return  baseEmailUserWithTicketDto.stream()
                    .filter(e -> emailSuppressionList.stream()
                            .map(String::toLowerCase)
                            .noneMatch(suppressedEmail -> suppressedEmail.equals(e.getEmail().toLowerCase())))
                    .collect(Collectors.toList());
        }

        return baseEmailUserWithTicketDto;
    }

    /**
     * Extracts CC emails from contactAttributeJsonValue
     * 1. Extracts all attribute IDs from JSON keys like "$CustomTextTest_230"
     * 2. Checks which IDs have value_type = 'CC_EMAIL_ADDRESS' in database
     * 3. Returns CC email values for matching attributes
     * @param contactAttributeJsonValue JSON string containing contact attributes
     * @param eventId Event ID for validation
     * @return Set of validated CC email addresses
     */
    private Set<String> extractCcEmailsFromContactAttributes(String contactAttributeJsonValue, Long eventId) {
        Set<String> ccEmails = new HashSet<>();

        if (StringUtils.isBlank(contactAttributeJsonValue)) {
            return ccEmails;
        }

        try {
            // Step 1: Extract key-to-ID mapping from JSON keys (call extractAttributeIdFromKey only once per key)
            Map<String, Long> keyToIdMapping = extractKeyToIdMappingFromJson(contactAttributeJsonValue);

            if (keyToIdMapping.isEmpty()) {
                return ccEmails;
            }

            // Step 2: Check which of these IDs are CC_EMAIL_ADDRESS type in one query
            Set<Long> allAttributeIds = new HashSet<>(keyToIdMapping.values());
            Set<Long> ccEmailAttributeIds = getCcEmailAttributeIdsByIds(allAttributeIds, eventId);

            if (ccEmailAttributeIds.isEmpty()) {
                return ccEmails;
            }

            // Step 3: Extract CC email values for matching attributes using the pre-built mapping
            JSONObject jsonObject = new JSONObject(contactAttributeJsonValue);
            JSONObject attributesObject = null;

            // Check if the JSON has an "attributes" object
            if (jsonObject.has(CONTACT_ATTRIBUTES)) {
                attributesObject = jsonObject.getJSONObject(CONTACT_ATTRIBUTES);
            }

            // Look for CC email values in the appropriate object
            JSONObject searchObject = attributesObject != null ? attributesObject : jsonObject;

            for (Map.Entry<String, Long> entry : keyToIdMapping.entrySet()) {
                String key = entry.getKey();
                Long attributeId = entry.getValue();

                if (ccEmailAttributeIds.contains(attributeId) && searchObject.has(key)) {
                    String emailValue = searchObject.optString(key);

                    if (StringUtils.isNotBlank(emailValue)) {
                        // Handle comma-separated email values using GeneralUtils
                        List<String> emailList = GeneralUtils.convertCommaSeparatedToList(emailValue);

                        for (String singleEmail : emailList) {
                            String trimmedEmail = singleEmail.trim();

                            if (StringUtils.isNotBlank(trimmedEmail) && isValidEmailAddress(trimmedEmail)) {
                                ccEmails.add(trimmedEmail.toLowerCase());

                                // Apply per-contact limit (max 2 CC emails)
                                if (ccEmails.size() >= 2) {
                                    break;
                                }
                            }
                        }

                        // Break outer loop if we've reached the limit
                        if (ccEmails.size() >= 2) {
                            break;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error extracting CC emails from contact attributes: {}", e.getMessage());
        }

        return ccEmails;
    }

    /**
     * Extracts key-to-ID mapping from JSON keys like "$CustomTextTest_230" -> 230
     * This optimized method calls extractAttributeIdFromKey only once per key
     * @param contactAttributeJsonValue JSON string containing contact attributes
     * @return Map of key to attribute ID for all valid keys found in the JSON
     */
    private Map<String, Long> extractKeyToIdMappingFromJson(String contactAttributeJsonValue) {
        Map<String, Long> keyToIdMapping = new HashMap<>();

        try {
            JSONObject jsonObject = new JSONObject(contactAttributeJsonValue);

            // Check if the JSON has an "attributes" object
            if (jsonObject.has(CONTACT_ATTRIBUTES)) {
                JSONObject attributesObject = jsonObject.getJSONObject(CONTACT_ATTRIBUTES);

                for (String key : attributesObject.keySet()) {
                    Long attributeId = extractAttributeIdFromKey(key);
                    if (attributeId != null) {
                        keyToIdMapping.put(key, attributeId);
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error extracting key-to-ID mapping from JSON: {}", e.getMessage());
        }

        return keyToIdMapping;
    }

    /**
     * Extracts attribute ID from a key like "$CustomTextTest_230"
     * @param key The attribute key
     * @return The attribute ID (230) or null if not found
     */
    private Long extractAttributeIdFromKey(String key) {
        if (StringUtils.isBlank(key) || !key.contains(STRING_UNDERSCORE)) {
            return null;
        }

        try {
            // Find the last underscore and extract the number after it
            int lastUnderscoreIndex = key.lastIndexOf("_");
            if (lastUnderscoreIndex > 0 && lastUnderscoreIndex < key.length() - 1) {
                String idString = key.substring(lastUnderscoreIndex + 1);
                return Long.parseLong(idString);
            }
        } catch (NumberFormatException e) {
            log.debug("Could not extract attribute ID from key: {}", key);
        }

        return null;
    }

    /**
     * Checks which of the given attribute IDs have value_type = 'CC_EMAIL_ADDRESS'
     * @param attributeIds Set of attribute IDs to check
     * @param eventId Event ID for validation
     * @return Set of attribute IDs that are CC_EMAIL_ADDRESS type
     */
    private Set<Long> getCcEmailAttributeIdsByIds(Set<Long> attributeIds, Long eventId) {
        Set<Long> ccEmailAttributeIds = new HashSet<>();

        if (attributeIds.isEmpty()) {
            return ccEmailAttributeIds;
        }

        try {
            String query = buildCcEmailAttributeCheckQuery(attributeIds, eventId);
            ResultSet resultSet = dbRequest.getQueryResult(query);

            while (resultSet.next()) {
                Long attributeId = resultSet.getLong(ID);
                ccEmailAttributeIds.add(attributeId);
            }

        } catch (SQLException e) {
            log.error("Error checking CC email attribute IDs: {}", e.getMessage());
        }

        return ccEmailAttributeIds;
    }

    /**
     * Builds query to check which attribute IDs have value_type = 'CC_EMAIL_ADDRESS'
     * @param attributeIds Set of attribute IDs to check
     * @param eventId Event ID for validation
     * @return SQL query string
     */
    private String buildCcEmailAttributeCheckQuery(Set<Long> attributeIds, Long eventId) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT id ");
        queryBuilder.append("FROM custom_form_attribute ");
        queryBuilder.append("WHERE event_id = ");
        queryBuilder.append(eventId);
        queryBuilder.append(" AND value_type = 'CC_EMAIL_ADDRESS' ");
        queryBuilder.append(" AND enabled = 1 ");  // Use 1 instead of true for better database compatibility
        queryBuilder.append(" AND attribute_type = 'CONTACT' ");
        queryBuilder.append(" AND rec_status != 'DELETE' ");
        queryBuilder.append(" AND id IN (");

        // Use GeneralUtils method for comma-separated conversion
        String idList = GeneralUtils.convertLongListToCommaSeparated(new ArrayList<>(attributeIds));
        queryBuilder.append(idList);
        queryBuilder.append(")");

        return queryBuilder.toString();
    }

    private List<String> getEmailSuppressionList(Long eventId) throws SQLException {
        List<String> emailSuppressionList = new ArrayList<>();
        ResultSet emailSuppressionDataRs = dbRequest.getQueryResult(queryBuilder.getEmailSuppressionForEventQuery(eventId).toString());
        while (emailSuppressionDataRs.next()) {
            emailSuppressionList.add(emailSuppressionDataRs.getString(BulkLambdaConstants.COL_EMAIL));
        }
        return emailSuppressionList;
    }

    private EmailUserWithTicketDto generateEmailUserDtoFromResultSet(ResultSet resultSet) throws SQLException {
        EmailUserWithTicketDto emailUserWithTicketDto = new EmailUserWithTicketDto(
                resultSet.getString(COL_FIRST_NAME),
                resultSet.getString(COL_LAST_NAME),
                resultSet.getString(COL_EMAIL),
                Long.valueOf(resultSet.getLong(COL_TICKET_ORDER_ID)),
                Long.valueOf(resultSet.getLong(COL_TICKET_NUMBER)),
                resultSet.getString(COL_TICKET_TYPE_NAME),
                Long.valueOf(resultSet.getLong(COL_AUTO_ASSIGNED_ATTENDEE_NUMBER_ID)));

        // Check if order_status column exists in the ResultSet and set it if available
        if (hasColumn(resultSet, COL_ORDER_STATUS)) {
            emailUserWithTicketDto.setOrderStatus(resultSet.getString(COL_ORDER_STATUS));
        }

        return emailUserWithTicketDto;
    }

    /**
     * Helper method to check if a column exists in the ResultSet
     */
    private static boolean hasColumn(ResultSet resultSet, String columnName) {
        try {
            resultSet.findColumn(columnName);
            return true;
        } catch (SQLException e) {
            return false;
        }
    }

    private EmailUserWithTicketDto generateEmailUserDtoFromResultSetWithContactAttributes(ResultSet resultSet) throws SQLException {
        EmailUserWithTicketDto emailUserWithTicketDto = new EmailUserWithTicketDto(
                resultSet.getString(COL_FIRST_NAME),
                resultSet.getString(COL_LAST_NAME),
                resultSet.getString(COL_EMAIL),
                resultSet.getLong(COL_TICKET_ORDER_ID),
                resultSet.getLong(COL_TICKET_NUMBER),
                resultSet.getString(COL_TICKET_TYPE_NAME),
                resultSet.getLong(COL_AUTO_ASSIGNED_ATTENDEE_NUMBER_ID),
                resultSet.getString(COL_ORDER_STATUS));


        try {
            // Set phone number if available
            long phoneNumberValue = resultSet.getLong(COL_PHONE_NUMBER);
            if (!resultSet.wasNull() && phoneNumberValue > 0) {
                emailUserWithTicketDto.setPhoneNumber(String.valueOf(phoneNumberValue));
            }

            // Set contact attribute ID if available
            Long contactAttributeId = resultSet.getLong(CONTACT_ATTRIBUTE);
            if (!resultSet.wasNull()) {
                emailUserWithTicketDto.setContactAttributeId(contactAttributeId);
            }
        } catch (SQLException e) {
            // Column might not exist in some queries, ignore
            log.warn("Column not found phone_number and contact_attribute in ResultSet: {}", e.getMessage());
        }

        try {
            String jsonValue = resultSet.getString(JSON_VALUE);
            if (jsonValue != null) {
                emailUserWithTicketDto.setContactAttributeJsonValue(jsonValue);
            }
        } catch (SQLException e) {
            // Column might not exist in some queries, ignore
            log.warn("Column not found json_value in ResultSet: {}", e.getMessage());
        }

        return emailUserWithTicketDto;
    }

    private EmailUserWithTicketDto generateEmailUserDtoAndCheckTicketIsGuestOfBuyerFromResultSet(ResultSet resultSet) throws SQLException {
        String firstName = STRING_EMPTY;
        String lastName = STRING_EMPTY;
        String email = STRING_EMPTY;
        String orderStatus = resultSet.getString(COL_ORDER_STATUS);

        if (resultSet.getBoolean(COL_GUEST_OF_BUYER)) {
            TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(resultSet.getString(COL_TICKET_HOLDER_ATTRIBUTES));
            Map<String, String> purchaser = (Map<String, String>) ticketAttributeValueDto.getPurchaser().get(Constants.TICKETING.ATTRIBUTES);

            if (purchaser != null) {
                firstName = purchaser.get(FIRST_NAME);
                lastName = purchaser.get(LAST_NAME);
                email = purchaser.get(EMAIL);
            } else {
                log.error("No purchaser info found for event ticket number {}", resultSet.getLong(COL_TICKET_NUMBER));
            }
        } else {
            firstName = resultSet.getString(COL_FIRST_NAME);
            lastName = resultSet.getString(COL_LAST_NAME);
            email = resultSet.getString(COL_EMAIL);
        }

        return new EmailUserWithTicketDto(
                firstName,
                lastName,
                email,
                resultSet.getLong(COL_TICKET_ORDER_ID),
                resultSet.getLong(COL_TICKET_NUMBER),
                resultSet.getString(COL_TICKET_TYPE_NAME),
                resultSet.getLong(COL_AUTO_ASSIGNED_ATTENDEE_NUMBER_ID),
                orderStatus
        );
    }


    public List<EmailUserWithTicketDto> getResendReminderEmailRecipientList(String allowedTicketTypeIds,
                                                                            String recipientType,
                                                                            Long eventId,Map<String, TicketHolderRequiredAttributes> emailTicketBuyerRequiredAttributesMap, Map<String, TicketHolderRequiredAttributes> emailTicketHolderRequiredAttributesMap) throws SQLException {

        List<EmailUserWithTicketDto> emailUserWithTicketDtoList = new ArrayList<>();
        Map<Long, List<EmailUserWithTicketDto>> ticketPurchaserOrderMapping = new HashMap<>();

        ResultSet eventTicketsRS = dbRequest.getQueryResult(queryBuilder.getAllEventTicketsDataQueryForReminderEmail(allowedTicketTypeIds, eventId, recipientType).toString());
            while (eventTicketsRS.next()){

                EmailUserWithTicketDto emailUserWithTicketDto = getEmailUserWithTicketDto(eventTicketsRS);
                Map<String, Object> dynamicEmailMergeTagsMapWithValues = modifyValueOfDynamicMergeTags(eventTicketsRS.getString(COL_JSON_VALUE), emailTicketBuyerRequiredAttributesMap,true);
                Map<String, Object> dynamicHolderEmailMergeTagsMapWithValues = modifyValueOfDynamicMergeTags(eventTicketsRS.getString(COL_JSON_VALUE), emailTicketHolderRequiredAttributesMap,false);
                emailUserWithTicketDto.setBuyerAttributesValuesMap(dynamicEmailMergeTagsMapWithValues);
                emailUserWithTicketDto.setHolderAttributesValuesMap(dynamicHolderEmailMergeTagsMapWithValues);
                emailUserWithTicketDtoList.add(emailUserWithTicketDto);
            }


        //This is used to identify if purchaser is different then holder, then send one reminder email to purchaser as well
        if(!ReminderEmailRecipientsType.CHECKED_IN_REGISTRANTS.name().equals(recipientType)) {
            for (EmailUserWithTicketDto dto : emailUserWithTicketDtoList) {
                ticketPurchaserOrderMapping
                        .computeIfAbsent(dto.getOrderNumber(), k -> new ArrayList<>())
                        .add(dto);
            }

            Map<Long, EmailUserWithTicketDto> ticketPurchaserOrderTempMapping = new HashMap<>();

            for (Long ticketingOrder : ticketPurchaserOrderMapping.keySet()) {
                List<EmailUserWithTicketDto> emailUserWithTicketDtos = ticketPurchaserOrderMapping.get(ticketingOrder);

                for (EmailUserWithTicketDto emailUserWithTicketDto : emailUserWithTicketDtos) {
                    boolean isPurchaserEmailDifferent;
                    if(emailUserWithTicketDto.getPurchaserEmail() == null) {
                        isPurchaserEmailDifferent = false;
                    } else {
                        isPurchaserEmailDifferent = !emailUserWithTicketDto.getPurchaserEmail().equalsIgnoreCase(emailUserWithTicketDto.getEmail());
                    }

                    if (isPurchaserEmailDifferent) {
                        EmailUserWithTicketDto emailUserWithTicketPurchaserDto = getEmailUserWithTicketPurchaserDto(emailUserWithTicketDto);
                        ticketPurchaserOrderTempMapping.putIfAbsent(emailUserWithTicketPurchaserDto.getOrderNumber(), emailUserWithTicketPurchaserDto);
                    } else {
                        ticketPurchaserOrderTempMapping.remove(emailUserWithTicketDto.getOrderNumber());
                    }
                }
            }

            emailUserWithTicketDtoList.addAll(ticketPurchaserOrderTempMapping.values());
        }

        List<String> emailSuppressionList = getEmailSuppressionList(eventId);

        if(!emailSuppressionList.isEmpty()) {
            return  emailUserWithTicketDtoList.stream()
                    .filter(e -> emailSuppressionList.stream()
                            .map(String::toLowerCase)
                            .noneMatch(suppressedEmail -> suppressedEmail.equals(e.getEmail().toLowerCase())))
                    .collect(Collectors.toList());
        }

        return emailUserWithTicketDtoList;
    }

    private Map<String, Object> modifyValueOfDynamicMergeTags(String buyerAttributeJson, Map<String, TicketHolderRequiredAttributes> ticketBuyerRequiredAttributesMap,boolean isForBuyer) {
        Map<String, Object> dynamicMergeTagsMapWithValues = new HashMap<>();

        if (!CollectionUtils.isEmpty(ticketBuyerRequiredAttributesMap)) {
            TicketAttributeValueDto1 buyerAttributeJsonValue = TicketHolderAttributesHelper.parseJsonToObject(buyerAttributeJson);
            Map<String, String> ticketAttributeJsonMap = isForBuyer ? (Map<String, String>) buyerAttributeJsonValue.getPurchaser().get(Constants.TICKETING.ATTRIBUTES)
                    : (Map<String, String>) buyerAttributeJsonValue.getHolder().get(Constants.TICKETING.ATTRIBUTES) ;

            for (String tag : ticketBuyerRequiredAttributesMap.keySet()) {
                if (ticketAttributeJsonMap != null) {
                    TicketHolderRequiredAttributes attributes = ticketBuyerRequiredAttributesMap.get(tag);
                    if (attributes != null) {
                        String valueKey = attributes.getName();
                        if (AttributeValueType.IMAGE.equals(attributes.getAttributeValueType())){
                            updateImageMergeTagValueInSubstitutionMap(dynamicMergeTagsMapWithValues, tag, ticketAttributeJsonMap, valueKey);
                        } else if (!isForBuyer && AttributeValueType.INTEREST.equals(attributes.getAttributeValueType()) && ticketAttributeJsonMap.get(attributes.getName()) != null) {
                            updateInterestMergeTagValueInSubstitutionMap(dynamicMergeTagsMapWithValues,tag,ticketAttributeJsonMap.get(attributes.getName()));
                        } else if (!isForBuyer && AttributeValueType.NUMBER.equals(attributes.getAttributeValueType()) && STRING_CELL_SPACE_PHONE.equals(attributes.getName())) {
                            String phoneNumber = ticketAttributeJsonMap.getOrDefault(STRING_PHONE_NUMBER,STRING_EMPTY);
                            dynamicMergeTagsMapWithValues.put(tag,phoneNumber);
                        } else {
                            String value = ticketAttributeJsonMap.getOrDefault(valueKey, STRING_EMPTY);
                            dynamicMergeTagsMapWithValues.put(tag, value);
                        }
                    } else {
                        dynamicMergeTagsMapWithValues.put(tag, STRING_EMPTY);
                    }
                } else {
                    dynamicMergeTagsMapWithValues.put(tag, STRING_EMPTY);
                }
            }
        }
        return dynamicMergeTagsMapWithValues;
    }

    private void updateInterestMergeTagValueInSubstitutionMap(Map<String, Object> substitutionMap, String key, String interestTags) {
        try {
            List<String> addedInterestTagList = new ArrayList<>();
            JSONArray jsonArray = new JSONArray(interestTags);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject.has(NAME_SMALL)) {
                    addedInterestTagList.add(jsonObject.getString(NAME_SMALL));
                }
            }
            substitutionMap.put(key,String.join(", ", addedInterestTagList));
            log.info("interest tag updated for substitution and key: {} ,and addedInterestTagList: {}",key,addedInterestTagList.size());
        } catch (Exception e) {
            log.info("update interest tag for substitution map key: {} ,errorMsg: {}",key,e.getMessage());
        }
    }

    private void updateImageMergeTagValueInSubstitutionMap(Map<String, Object> substitutionMap, String key, Map<String, String> buyerAttributesMap, String attributeName) {
        log.info("Update image merge tag value in substitution map for key: {} and attribute name: {}", key, attributeName);
        String imageTag= STRING_EMPTY;
        if(MapUtils.isNotEmpty(buyerAttributesMap) && StringUtils.isNotEmpty(buyerAttributesMap.get(attributeName))) {
            String imageUrl = imagePreFix.concat("ticket_buyer_uploads/").concat(buyerAttributesMap.get(attributeName));
            imageTag = "<img src=\"" + imageUrl + "\" height=\"200\" width=\"300\">";
        }
        substitutionMap.put(key, imageTag);
    }

    public List<EventTaskUserEmailDto> getEventTaskReminderEmailRecipientList(Long eventId, Long eventTaskId, String taskType) throws SQLException {

        List<EventTaskUserEmailDto> emailUserWithTicketDtoList = new ArrayList<>();
        log.info("getEventTaskReminderEmailRecipientList process started for eventId {} eventTaskId {} taskType {}", eventId, eventTaskId, taskType);
        if(eventTaskId > 0) {
            ResultSet speakersRS;
            boolean isSpeakerTask = TaskType.SPEAKER.name().equals(taskType);
            boolean isExhibitorTask = TaskType.EXHIBITOR.name().equals(taskType);
            if(isSpeakerTask) {
                speakersRS = dbRequest.getQueryResult(queryBuilder.getEventTaskParticipantSpeakerDataAndSessionIdNullQuery(eventId, eventTaskId).toString());
            }else if (isExhibitorTask){
                speakersRS = dbRequest.getQueryResult(queryBuilder.getEventTaskParticipantExhibitorDataAndSessionIdAndSpeakerIdNullQuery(eventId, eventTaskId).toString());
            }else {
                speakersRS = dbRequest.getQueryResult(queryBuilder.getEventTaskParticipantSpeakerWithSessionDataQuery(eventId, eventTaskId).toString());
            }
            while (speakersRS.next()) {
                EventTaskUserEmailDto eventTaskUserEmailDto = new EventTaskUserEmailDto();
                eventTaskUserEmailDto.setEmail(speakersRS.getString(COL_EMAIL));
                eventTaskUserEmailDto.setFirstName(speakersRS.getString(COL_FIRST_NAME));
                eventTaskUserEmailDto.setUserId(speakersRS.getLong(COL_USER_ID));
                if(!isSpeakerTask && !isExhibitorTask) {
                    eventTaskUserEmailDto.setSessionName(speakersRS.getString(COL_TITLE));
                }
                emailUserWithTicketDtoList.add(eventTaskUserEmailDto);
            }
        }

        List<String> emailSuppressionList = getEmailSuppressionList(eventId);

        if(!emailSuppressionList.isEmpty()) {
            return  emailUserWithTicketDtoList.stream()
                    .filter(e -> emailSuppressionList.stream()
                            .map(String::toLowerCase)
                            .noneMatch(suppressedEmail -> suppressedEmail.equals(e.getEmail().toLowerCase())))
                    .collect(Collectors.toList());
        }

        log.info("getEventTaskReminderEmailRecipientList process ended for eventId {} eventTaskId {} taskType {} recipientSize {}", eventId, eventTaskId, taskType, emailUserWithTicketDtoList.size());
        return emailUserWithTicketDtoList;
    }


    private static EmailUserWithTicketDto getEmailUserWithTicketPurchaserDto(EmailUserWithTicketDto emailUserWithTicketDto) throws SQLException {
        EmailUserWithTicketDto emailUserWithTicketPurchaserDto = new EmailUserWithTicketDto(
                emailUserWithTicketDto.getPurchaserFirstName(),
                emailUserWithTicketDto.getPurchaserLastName(),
                emailUserWithTicketDto.getPurchaserEmail(),
                emailUserWithTicketDto.getOrderNumber(),
                emailUserWithTicketDto.getTicketNumber(),
                emailUserWithTicketDto.getTicketTypeName(),
                emailUserWithTicketDto.getAutoAssignedSeqNumber()
        );


        emailUserWithTicketPurchaserDto.setTicketBarcodeId(emailUserWithTicketDto.getTicketBarcodeId());
        emailUserWithTicketPurchaserDto.setPurchaseDate(emailUserWithTicketDto.getPurchaseDate());
        emailUserWithTicketPurchaserDto.setPurchaserFirstName(emailUserWithTicketDto.getPurchaserFirstName());
        emailUserWithTicketPurchaserDto.setPurchaserLastName(emailUserWithTicketDto.getPurchaserLastName());
        emailUserWithTicketPurchaserDto.setPurchaserEmail(emailUserWithTicketPurchaserDto.getPurchaserEmail());
        emailUserWithTicketPurchaserDto.setUserId(emailUserWithTicketDto.getUserId());
        emailUserWithTicketPurchaserDto.setSeatNumber(emailUserWithTicketDto.getSeatNumber());
        emailUserWithTicketPurchaserDto.setTicketTypeDesc(emailUserWithTicketDto.getTicketTypeDesc());
        emailUserWithTicketPurchaserDto.setPrice(emailUserWithTicketDto.getPrice());
        emailUserWithTicketPurchaserDto.setOrderStatus(emailUserWithTicketDto.getOrderStatus());
        emailUserWithTicketPurchaserDto.setAllowPDFDownload(emailUserWithTicketDto.isAllowPDFDownload());
        emailUserWithTicketPurchaserDto.setHolderEmail(false);
        return emailUserWithTicketPurchaserDto;
    }

    private static EmailUserWithTicketDto getEmailUserWithTicketDto(ResultSet eventTicketsRS) throws SQLException {
        EmailUserWithTicketDto emailUserWithTicketDto = new EmailUserWithTicketDto(
                eventTicketsRS.getString(COL_H_FIRST_NAME),
                eventTicketsRS.getString(COL_H_LAST_NAME),
                eventTicketsRS.getString(COL_H_EMAIL),
                eventTicketsRS.getLong(COL_TICKET_ORDER_ID),
                eventTicketsRS.getLong(COL_ID),
                eventTicketsRS.getString(COL_TICKET_TYPE_NAME),
                eventTicketsRS.getLong(COL_AUTO_ASSIGNED_ATTENDEE_NUMBER_ID)
        );

        emailUserWithTicketDto.setTicketBarcodeId(eventTicketsRS.getString(COL_BARCODE_ID));
        emailUserWithTicketDto.setPurchaseDate(eventTicketsRS.getTimestamp(COL_CREATED_AT));
        emailUserWithTicketDto.setPurchaserFirstName(eventTicketsRS.getString(COL_FIRST_NAME));
        emailUserWithTicketDto.setPurchaserLastName(eventTicketsRS.getString(COL_LAST_NAME));
        emailUserWithTicketDto.setPurchaserEmail(eventTicketsRS.getString(COL_EMAIL));
        emailUserWithTicketDto.setUserId(eventTicketsRS.getLong(COL_H_USER_ID));
        emailUserWithTicketDto.setSeatNumber(eventTicketsRS.getString(COL_SEAT_NUMBER));
        emailUserWithTicketDto.setTicketTypeDesc(eventTicketsRS.getString(COL_TICKET_TYPE_DESCRIPTION));
        emailUserWithTicketDto.setPrice(eventTicketsRS.getDouble(COL_PRICE));
        // Check if order_status column exists before accessing it
        if (hasColumn(eventTicketsRS, COL_ORDER_STATUS)) {
            emailUserWithTicketDto.setOrderStatus(eventTicketsRS.getString(COL_ORDER_STATUS));
        }
        emailUserWithTicketDto.setTicketPdfDesign(eventTicketsRS.getString(COL_TICKET_CUSTOM_PDF_DESIGN));
        emailUserWithTicketDto.setHolderEmail(true);
        emailUserWithTicketDto.setAllowPDFDownload(eventTicketsRS.getBoolean(COL_ALLOW_PDF_DOWNLOAD));
        return emailUserWithTicketDto;
    }

    /**
     * Processes ticket holder attributes and sets buyer and holder attributes maps on the EmailUserWithTicketDto
     * This method is reusable across all recipient types that have ticket holder attributes
     */
    private void processTicketHolderAttributes(EmailUserWithTicketDto emailUserWithTicketDto, String ticketHolderAttributesJson, Long eventId) {
        if (StringUtils.isNotBlank(ticketHolderAttributesJson)) {
            try {
                // Parse the JSON to extract buyer and holder attributes
                TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributesJson);

                // Extract buyer attributes with merge tag keys
                Map<String, Object> buyerAttributesValuesMap = extractAttributesMap(ticketAttributeValueDto.getPurchaser(), eventId, true);
                if (buyerAttributesValuesMap != null) {
                    emailUserWithTicketDto.setBuyerAttributesValuesMap(buyerAttributesValuesMap);
                }

                // Extract holder attributes with merge tag keys
                Map<String, Object> holderAttributesValuesMap = extractAttributesMap(ticketAttributeValueDto.getHolder(), eventId, false);
                if (holderAttributesValuesMap != null) {
                    emailUserWithTicketDto.setHolderAttributesValuesMap(holderAttributesValuesMap);
                }
            } catch (Exception e) {
                log.warn("Error processing ticket holder attributes for ticket {}: {}", emailUserWithTicketDto.getTicketNumber(), e.getMessage());
            }
        }
    }

    /**
     * Generic method to extract attributes from purchaser or holder data.
     * This method eliminates code duplication between buyer and holder attribute extraction.
     *
     * @param attributeSource the source map (purchaser or holder) containing attributes
     * @param eventId the event ID for creating merge tag keys
     * @param isBuyer true if extracting buyer attributes, false for holder attributes
     * @return the attributes map with merge tag keys, or null if no attributes found
     */
    private Map<String, Object> extractAttributesMap(Map<String, Object> attributeSource, Long eventId, boolean isBuyer) {
        if (attributeSource == null) {
            return null;
        }

        Map<String, String> attributesMap = (Map<String, String>) attributeSource.get(Constants.TICKETING.ATTRIBUTES);
        if (attributesMap != null && !attributesMap.isEmpty()) {
            Map<String, Object> attributesValuesMap = createMergeTagMap(attributesMap, eventId, isBuyer);
            return attributesValuesMap.isEmpty() ? null : attributesValuesMap;
        }

        return null;
    }

    /**
     * Creates a map with merge tag keys (like "buyer_66009") instead of attribute names
     */
    private Map<String, Object> createMergeTagMap(Map<String, String> attributesMap, Long eventId, boolean isBuyer) {
        Map<String, Object> mergeTagMap = new HashMap<>();

        if (MapUtils.isEmpty(attributesMap)) {
            return mergeTagMap;
        }

        try {
            // Get all attribute names from the map
            Set<String> attributeNames = new HashSet<>(attributesMap.keySet());

            // If holder has phoneNumber, we need to query for "Cell Phone" instead
            if (!isBuyer && attributeNames.contains(STRING_PHONE_NUMBER)) {
                attributeNames.remove(STRING_PHONE_NUMBER);
                attributeNames.add(STRING_CELL_SPACE_PHONE);
            }

            // Create SQL query to fetch attribute IDs by names
            StringBuilder query = new StringBuilder();
            query.append("SELECT id, attribute_name FROM ticket_holder_required_attributes ");
            query.append("WHERE event_id = ").append(eventId);
            query.append(" AND recurring_event_id IS NULL ");
            query.append(" AND data_type = 'TICKET' ");
            query.append(" AND attribute_name IN (");

            // Add attribute names to the query
            boolean first = true;
            for (String attributeName : attributeNames) {
                if (!first) {
                    query.append(", ");
                }
                query.append("'").append(attributeName.replace("'", "''")).append("'"); // Escape single quotes
                first = false;
            }
            query.append(")");

            // Execute query and build name to ID mapping
            Map<String, Long> nameToIdMap = new HashMap<>();
            ResultSet rs = dbRequest.getQueryResult(query.toString());
            while (rs.next()) {
                String attributeName = rs.getString(ATTRIBUTE_NAME);
                Long attributeId = rs.getLong(ID);
                nameToIdMap.put(attributeName, attributeId);
            }

            // Create merge tag map with proper keys
            String prefix = isBuyer ? ENGAGE_BUYER : ENGAGE_HOLDER;
            for (Map.Entry<String, String> entry : attributesMap.entrySet()) {
                String attributeName = entry.getKey();
                String attributeValue = entry.getValue();

                Long attributeId = nameToIdMap.get(attributeName);
                if (STRING_CELL_SPACE_PHONE.equalsIgnoreCase(attributeName) && isBuyer) {
                    attributeId = nameToIdMap.get(STRING_CELL_SPACE_PHONE);
                }
                // For holder, map phoneNumber to Cell Phone attribute ID
                if(STRING_PHONE_NUMBER.equalsIgnoreCase(attributeName) && !isBuyer) {
                    attributeId = nameToIdMap.get(STRING_CELL_SPACE_PHONE);
                }
                if (attributeId != null) {
                    String mergeTagKey = prefix + attributeId;
                    mergeTagMap.put(mergeTagKey, attributeValue);
                } else {
                    // Fallback: use original attribute name if ID not found
                    mergeTagMap.put(attributeName, attributeValue);
                }
            }

        } catch (Exception e) {
            log.warn("Error creating merge tag map for eventId {}: {}", eventId, e.getMessage());
            // Fallback: return original map
            return new HashMap<>(attributesMap);
        }

        return mergeTagMap;
    }

}