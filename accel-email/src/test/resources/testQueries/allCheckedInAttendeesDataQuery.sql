SELECT DISTINCT
  et.h_first_name AS first_name,
  et.h_last_name AS last_name,
  et.h_email AS email,
  et.id AS ticket_number,
  et.ticket_order_id,
  et.auto_assigned_attendee_number_id,
  ett.ticket_type_name,
  et.event_id,
  et.guest_of_buyer,
  tha.json_value AS ticket_holder_attributes
FROM check_in_audit_log cial
JOIN event_tickets et on et.id = cial.event_ticket_id
JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id
JOIN
  event_ticket_type ett on ett.id = et.ticketing_type_id
WHERE
  cial.event_id = 111
  AND et.ticket_status not in ('CANCELED','DELETED')
AND cial.checkin_source IN ('HYBRID_CHECKIN','IN_PERSON','VIRTUAL_EVENT_PORTAL')