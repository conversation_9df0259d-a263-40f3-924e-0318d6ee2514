WITH filtered_event_tickets AS (
    SELECT
        et.id AS ticket_number,
        et.ticket_order_id,
        et.auto_assigned_attendee_number_id,
        ett.ticket_type_name,
        et.h_email,
        tord.order_status,
        ROW_NUMBER() OVER (PARTITION BY et.h_email ORDER BY et.id) AS row_num
    FROM
        event_tickets et
    JOIN
        event_ticket_type ett ON et.ticketing_type_id = ett.id
    JOIN
        ticketing_order tord ON et.ticket_order_id = tord.id
    WHERE et.event_id = 111
      AND et.ticket_status not in ('CANCELED','DELETED')

),
selected_event_tickets AS (
    SELECT
        ticket_number,
        ticket_order_id,
        auto_assigned_attendee_number_id,
        ticket_type_name,
        h_email,
        order_status
    FROM
        filtered_event_tickets
    WHERE
        row_num = 1
)
SELECT DISTINCT
    c.first_name,
    c.last_name,
    c.email,
    c.phone_number,
    etfm.ticket_order_id,
    etfm.ticket_number,
    etfm.ticket_type_name,
    etfm.auto_assigned_attendee_number_id,
    etfm.order_status,
    c.contact_attribute,
    cfad.json_value
FROM
    contacts c
JOIN contacts_list_mapping clm ON c.id = clm.contacts_id
LEFT JOIN
    selected_event_tickets etfm ON c.email = etfm.h_email
LEFT JOIN
    custom_form_attribute_data cfad ON c.contact_attribute = cfad.id
WHERE
    c.event_id = 111
   AND clm.contacts_list_id = 22
   AND c.rec_status != 'DELETE'