SELECT DISTINCT
	u.first_name AS first_name,
	u.last_name AS last_name,
	tord.buyer_email AS email,
	et.id AS ticket_number,
	et.ticket_order_id,
	et.auto_assigned_attendee_number_id,
	ett.ticket_type_name,
	et.event_id,
	tord.order_status,
	et.guest_of_buyer,
	tha.json_value AS ticket_holder_attributes
FROM event_tickets et
JOIN event_ticket_type ett ON ett.id = et.ticketing_type_id
JOIN ticketing_order tord ON et.ticket_order_id = tord.id
JOIN users u ON tord.purchaser_id = u.user_id
JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id
WHERE et.event_id = 111
AND et.ticket_status NOT IN ('CANCELED','DELETED')
AND et.data_type = 'TICKET'
AND ett.ticket_type <> 'COMPLIMENTARY'
AND tord.buyer_email IS NOT NULL
AND tord.buyer_email != ''
AND (
  (et.ticket_payment_status IN ('UNPAID', 'PARTIALLY_PAID'))
  OR (tord.order_type = 'PAY_LATER' AND et.ticket_payment_status = 'UNPAID')
  OR (ett.require_deposit_amount = true
      AND et.ticket_payment_status IN ('UNPAID', 'PARTIALLY_PAID')
      AND (et.paid_amount < et.ticket_price OR et.paid_amount IS NULL))
  OR (et.transfers_count > 0
      AND et.ticket_payment_status IN ('UNPAID', 'PARTIALLY_PAID'))
)
