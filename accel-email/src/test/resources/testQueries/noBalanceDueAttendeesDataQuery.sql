SELECT DISTINCT
	u.first_name AS first_name,
	u.last_name AS last_name,
	tord.buyer_email AS email,
	et.id AS ticket_number,
	et.ticket_order_id,
	et.auto_assigned_attendee_number_id,
	ett.ticket_type_name,
	et.event_id,
	tord.order_status,
	et.guest_of_buyer,
	tha.json_value AS ticket_holder_attributes
FROM event_tickets et
JOIN event_ticket_type ett ON ett.id = et.ticketing_type_id
JOIN ticketing_order tord ON et.ticket_order_id = tord.id
JOIN users u ON tord.purchaser_id = u.user_id
JOIN ticket_holder_attributes tha ON et.ticket_holder_attributes_id=tha.id
WHERE et.event_id = 111
AND et.ticket_status NOT IN ('CANCELED','DELETED')
AND et.data_type = 'TICKET'
AND tord.buyer_email IS NOT NULL
AND tord.buyer_email != ''
AND (
  (ett.ticket_type = 'FREE') OR
  (ett.ticket_type = 'COMPLIMENTARY') OR
  (ett.ticket_type = 'PAID' AND et.ticket_payment_status = 'PAID') OR
  (ett.ticket_type = 'PAID' AND et.ticket_price = 0) OR
  (ett.require_deposit_amount = true
      AND et.ticket_payment_status = 'PAID') OR
  (tord.order_type = 'PAY_LATER' AND et.ticket_payment_status = 'PAID')
)
