package com.accelevents.email.service;

import com.accelevents.domain.enums.ReminderEmailRecipientsType;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.assertEquals;

class QueryBuilderTest {

    private static QueryBuilder queryBuilder;

    @BeforeAll
    public static void init(){
        queryBuilder = new QueryBuilder();
    }

    private String loadSqlFile(String fileName) throws IOException {
        return new String(Files.readAllBytes(Paths.get(getClass().getClassLoader().getResource("testQueries/"+fileName).getPath())));
    }

    private String normalizeWhitespace(String input) {
        return input.replaceAll("\\s+", " ").trim();
    }

    @Test
    void createEventAccessStartedQuery() throws IOException {
        String expectedQuery = loadSqlFile("createEventAccessStartedQuery.sql");

        StringBuilder actualQuery = queryBuilder.createEventAccessStartedQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createMessageToContactTableQuery() throws IOException {
        String expectedQuery = loadSqlFile("createMessageToContactTableQuery.sql");

        StringBuilder actualQuery = queryBuilder.createMessageToContactTableQuery(10L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createEventWithDesignAndTicketingTableQuery() throws IOException {
        String expectedQuery = loadSqlFile("createEventWithDesignAndTicketingTableQuery.sql");

        StringBuilder actualQuery = queryBuilder.createEventWithDesignAndTicketingTableQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createWhiteLabelTableQuery() throws IOException {
        String expectedQuery = loadSqlFile("createWhiteLabelTableQuery.sql");

        StringBuilder actualQuery = queryBuilder.createWhiteLabelTableQuery(222L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getUserDataFromEmailQuery() throws IOException {
        String expectedQuery = loadSqlFile("getUserDataFromEmailQuery.sql");

        StringBuilder actualQuery = queryBuilder.getUserDataFromEmailQuery("<EMAIL>");

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getEmailSuppressionForEventQuery() throws IOException {
        String expectedQuery = loadSqlFile("getEmailSuppressionForEventQuery.sql");

        StringBuilder actualQuery = queryBuilder.getEmailSuppressionForEventQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createAllContactsWithFirstTicketMappingDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("createAllContactsWithFirstTicketMappingDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.createAllContactsWithFirstTicketMappingDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createContactsWithFirstTicketMappingFromContactListDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("createContactsWithFirstTicketMappingFromContactListDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.createContactsWithFirstTicketMappingFromContactListDataQuery(111L, 22L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createSmartContactListWithFirstTicketMappingFromContactListDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("createSmartContactListWithFirstTicketMappingFromContactListDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.createSmartContactListWithFirstTicketMappingFromContactListDataQuery(111L, "1,2,3", "'PAID','UNPAID'", "AND u.first_name IS NOT NULL");

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createSelectedContactsFromListWithFirstTicketMappingDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("createSelectedContactsFromListWithFirstTicketMappingDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.createSelectedContactsFromListWithFirstTicketMappingDataQuery(111L, 333L, 22L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createCustomContactsForEventWithFirstTicketMappingDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("createCustomContactsForEventWithFirstTicketMappingDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.createCustomContactsForEventWithFirstTicketMappingDataQuery(111L, 333L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allTicketHolderAndBuyerDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allTicketHolderAndBuyerDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allTicketHolderAndBuyerDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allModuleWinnersDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allModuleWinnersDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allModuleWinnersDataQuery(111L, "EMAIL");

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allRaffleParticipantsDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allRaffleParticipantsDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allRaffleParticipantsDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allTextToGiveDonorsDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allTextToGiveDonorsDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allTextToGiveDonorsDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void auctionParticipantsDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("auctionParticipantsDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.auctionParticipantsDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allFundANeedPledgesDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allFundANeedPledgesDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allFundANeedPledgesDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allTicketHolderByTicketTypeDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allTicketHolderByTicketTypeDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allTicketHolderByTicketTypeDataQuery(111L, "1,6,8");

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allSpeakersDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allSpeakersDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allSpeakersDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allExhibitorsStaffDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allExhibitorsStaffDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allExhibitorsStaffDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allCheckedInAttendeesDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allCheckedInAttendeesDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allCheckedInAttendeesDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void allNotCheckedInAttendeesDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("allNotCheckedInAttendeesDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.allNotCheckedInAttendeesDataQuery(111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getResendTicketingEmailQuery() throws IOException {
        String expectedQuery = loadSqlFile("getResendTicketingEmailQuery.sql");

        StringBuilder actualQuery = queryBuilder.getResendTicketingEmailQuery(55L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getAllEventTicketsDataQueryForReminderEmailWithCheckInTrue() throws IOException {
        String expectedQuery = loadSqlFile("getAllEventTicketsDataQueryForReminderEmailWithCheckInTrue.sql");

        StringBuilder actualQuery = queryBuilder.getAllEventTicketsDataQueryForReminderEmail("2,3,5", 111L, ReminderEmailRecipientsType.NOT_CHECKED_IN_REGISTRANTS.name());

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getAllEventTicketsDataQueryForReminderEmailWithCheckInFalse() throws IOException {
        String expectedQuery = loadSqlFile("getAllEventTicketsDataQueryForReminderEmailWithCheckInFalse.sql");

        StringBuilder actualQuery = queryBuilder.getAllEventTicketsDataQueryForReminderEmail("2,3,5", 111L, ReminderEmailRecipientsType.CHECKED_IN_REGISTRANTS.name());

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createAutoLoginQuery() throws IOException {
        String expectedQuery = loadSqlFile("createAutoLoginQuery.sql");

        StringBuilder actualQuery = queryBuilder.createAutoLoginQuery(444L, 111L);

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createMessageToContactTableScheduleQuery() throws IOException {
        String expectedQuery = loadSqlFile("createMessageToContactTableScheduleQuery.sql");

        StringBuilder actualQuery = queryBuilder.createMessageToContactTableScheduleQuery();

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void createResendTicketingEmailTableScheduleQuery() throws IOException {
        String expectedQuery = loadSqlFile("createResendTicketingEmailTableScheduleQuery.sql");

        StringBuilder actualQuery = queryBuilder.createResendTicketingEmailTableScheduleQuery();

        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

        @Test
        void getAllTicketBuyerRequiredAttributesByIdsAndEventId() throws IOException {
            String expectedQuery = loadSqlFile("getAllTicketHolderRequiredAttributesByIdsAndEventId.sql");

            StringBuilder actualQuery = queryBuilder.getAllTicketHolderRequiredAttributesByIdsAndEventId(111L);

            assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
        }

    @Test
    void createEventTaskReminderEmailScheduleQuery() throws IOException {
        String expectedQuery = loadSqlFile("createEventTaskReminderEmailScheduleQuery.sql");

        StringBuilder actualQuery = queryBuilder.createEventTaskReminderEmailScheduleQuery();
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getEventTaskReminderEmailQuery() throws IOException {
        String expectedQuery = loadSqlFile("getEventTaskReminderEmailQuery.sql");

        StringBuilder actualQuery = queryBuilder.getEventTaskReminderEmailQuery(111L);
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getEventTaskEmailForManualSendQuery() throws IOException {
        String expectedQuery = loadSqlFile("getEventTaskEmailForManualSendQuery.sql");

        StringBuilder actualQuery = queryBuilder.getEventTaskEmailForManualSendQuery(111L);
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getEventTaskParticipantSpeakerDataAndSessionIdNullQuery() throws IOException {
        String expectedQuery = loadSqlFile("getEventTaskParticipantSpeakerDataAndSessionIdNullQuery.sql");

        StringBuilder actualQuery = queryBuilder.getEventTaskParticipantSpeakerDataAndSessionIdNullQuery(111L, 222L);
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getEventTaskParticipantSpeakerWithSessionDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("getEventTaskParticipantSpeakerWithSessionDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.getEventTaskParticipantSpeakerWithSessionDataQuery(111L, 222L);
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getEventTaskParticipantExhibitorDataAndSessionIdAndSpeakerIdNullQuery() throws IOException {
        String expectedQuery = loadSqlFile("getEventTaskParticipantExhibitorDataAndSessionIdAndSpeakerIdNullQuery.sql");

        StringBuilder actualQuery = queryBuilder.getEventTaskParticipantExhibitorDataAndSessionIdAndSpeakerIdNullQuery(1L, 2L);
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void getAllowDownloadPdfTicketForTestEmailQuery() throws IOException {
        String expectedQuery = loadSqlFile("getAllowDownloadPdfTicketForTestEmailQuery.sql");

        StringBuilder actualQuery = queryBuilder.getAllowDownloadPdfTicketForTestEmailQuery(1L, "2");
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void balanceDueAttendeesDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("balanceDueAttendeesDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.balanceDueAttendeesDataQuery(111L);
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

    @Test
    void noBalanceDueAttendeesDataQuery() throws IOException {
        String expectedQuery = loadSqlFile("noBalanceDueAttendeesDataQuery.sql");

        StringBuilder actualQuery = queryBuilder.noBalanceDueAttendeesDataQuery(111L);
        assertEquals(normalizeWhitespace(expectedQuery), normalizeWhitespace(actualQuery.toString()));
    }

}