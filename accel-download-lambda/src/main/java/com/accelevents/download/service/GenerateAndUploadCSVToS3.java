package com.accelevents.download.service;

import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.download.db.DBRequest;
import com.accelevents.download.dto.DownloadCSVRequestDto;
import com.accelevents.download.dto.EventTicketDownloadCSVDto;
import com.accelevents.download.dto.SessionDownloadCSVDto;
import com.accelevents.download.dto.UserSessionsDownloadCSVDto;
import com.accelevents.download.utils.TicketHolderUtils;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.graphql.NotificationGraphQLHandler;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.helpers.UserSessionHelper;
import com.accelevents.session_speakers.dto.AttendeeAnalyticsDTO;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.opencsv.CSVWriter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.io.*;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.download.LambdaConstants.*;
import static com.accelevents.utils.Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN;
import static com.accelevents.utils.Constants.CSV_DOWN_STRING;

public class GenerateAndUploadCSVToS3 {

    private AmazonS3 s3Client;
    private String AWS_S3_IMAGES_BUCKET;
    private String TICKET_BUYER_UPLOADS_URL;
    private NotificationGraphQLHandler notificationGraphQLHandler;

    private DBRequest dbRequest;

    private String region = "us-east-1";

    public GenerateAndUploadCSVToS3(String jdbcUrl, String userName, String password, String awsS3bucket,
                                    String appSyncApiNotificationUrl) throws SQLException {
        dbRequest = new DBRequest(jdbcUrl, userName, password);
        notificationGraphQLHandler = NotificationGraphQLHandler.getInstance(appSyncApiNotificationUrl, BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
        this.AWS_S3_IMAGES_BUCKET = awsS3bucket;

        s3Client = AmazonS3ClientBuilder
                .standard()
                .withCredentials(
                        new DefaultAWSCredentialsProviderChain()
                )
                .withRegion(region)
                .build();
    }

    private void appendAddressColumns(List<String> headerList, String ticketHolderRequiredAttributesName) {
        int indexOf = ticketHolderRequiredAttributesName.indexOf(" ");
        String addStr = ticketHolderRequiredAttributesName;
        if (indexOf > 0) {
            addStr = addStr.substring(0, indexOf);
        }
        headerList.add(ticketHolderRequiredAttributesName + " 1");
        headerList.add(ticketHolderRequiredAttributesName + " 2");
        headerList.add(addStr + " City");
        headerList.add(addStr + " State");
        headerList.add(addStr + " Zip Code");
        headerList.add(addStr + " Country");
    }

    private List<String> addTicketHolderRequiredAttributesToHeader(String attributesValueType, String attributesName) {
        List<String> headerList = new ArrayList<>();
        if (AttributeValueType.BILLING_ADDRESS.name().equals(attributesValueType)
                || AttributeValueType.SHIPPING_ADDRESS.name().equals(attributesValueType)) {
            appendAddressColumns(headerList, attributesName);
        } else {
            headerList.add(attributesName);
        }
        return headerList;
    }

    public void generateAllAttendeeSessionInfoCSVUploadS3AndSendAppsyncNotification(DownloadCSVRequestDto downloadCSVRequestDto) throws SQLException, IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();

        this.downloadAllAttendeeSessionsInfoCSV(new PrintWriter(stream), downloadCSVRequestDto);
        System.out.println("CSV Data is ready");

        InputStream inputStream = new ByteArrayInputStream(stream.toByteArray());
        Date currentDate = new Date();
        String uploadKey = "csv_dl/"+downloadCSVRequestDto.getEventId()+"/All-Attendees-Sessions-"+currentDate.getTime()+".csv";

        LambdaS3UploadService s3UploadService = new LambdaS3UploadService(s3Client);

        System.out.println("Before s3 file upload key => " + uploadKey);
        s3UploadService.uploadInBucket(inputStream, uploadKey, AWS_S3_IMAGES_BUCKET, "");

        notificationGraphQLHandler.createNotification(downloadCSVRequestDto.getEventId(), downloadCSVRequestDto.getUserId(),
                CSV_DOWN_STRING, uploadKey, null);
    }

    private void downloadAllAttendeeSessionsInfoCSV(PrintWriter writer, DownloadCSVRequestDto downloadCSVRequestDto) throws SQLException {

        List<List<String>> dataRowList = new ArrayList<>();

        try {
            System.out.println("Download preparation started");

            List<String> headerList = new ArrayList<>();
            List<Map<String, Object>> listOfAttributesHavingOnlyEnableForTicketHolder = new ArrayList<>();

            ResultSet ticketHolderRequiredAttributesRS = dbRequest.getQueryResult(this.createTicketHolderRequiredAttributesTableQuery(downloadCSVRequestDto).toString());
            while (ticketHolderRequiredAttributesRS.next()){
                String ticketHolderRAName = ticketHolderRequiredAttributesRS.getString(COL_ATTRIBUTE_NAME);
                String ticketHolderRAValueType = ticketHolderRequiredAttributesRS.getString(COL_ATTRIBUTE_VALUE_TYPE);
                boolean ticketHolderRAIsAttribute = ticketHolderRequiredAttributesRS.getBoolean(COL_IS_ATTRIBUTE);
                boolean ticketHolderRAEnabledForTicketHolder = ticketHolderRequiredAttributesRS.getBoolean(COL_ENABLED_FOR_TICKET_HOLDER);

                if(ticketHolderRAEnabledForTicketHolder){
                    Map<String, Object> ticketHolderAttributesMap = new HashMap<>();
                    ticketHolderAttributesMap.put(COL_ATTRIBUTE_NAME, ticketHolderRAName);
                    ticketHolderAttributesMap.put(COL_ATTRIBUTE_VALUE_TYPE, ticketHolderRAValueType);
                    ticketHolderAttributesMap.put(COL_IS_ATTRIBUTE, ticketHolderRAIsAttribute);

                    listOfAttributesHavingOnlyEnableForTicketHolder.add(ticketHolderAttributesMap);
                    headerList.addAll(this.addTicketHolderRequiredAttributesToHeader(ticketHolderRAValueType, ticketHolderRAName));
                }
            }
            System.out.println("Ticket holder data ready");

            Map<Long, List<Map<String, Object>>> holderUserEventTickets = new HashMap<>();

            ResultSet eventTicketsRS = dbRequest.getQueryResult(this.createEventTicketsTableQuery(downloadCSVRequestDto).toString());
            while (eventTicketsRS.next()){
                Map<String, Object> eventTicketsMap = new HashMap<>();

                Long etHolderUserId = eventTicketsRS.getLong(COL_HOLDER_USER_ID);
                eventTicketsMap.put(COL_ID, eventTicketsRS.getLong(COL_ID));
                eventTicketsMap.put(COL_H_PHONE_NUMBER, eventTicketsRS.getLong(COL_H_PHONE_NUMBER));
                eventTicketsMap.put(COL_H_COUNTRY_CODE, eventTicketsRS.getString(COL_H_COUNTRY_CODE));
                eventTicketsMap.put(COL_P_PHONE_NUMBER, eventTicketsRS.getLong(COL_P_PHONE_NUMBER));
                eventTicketsMap.put(COL_P_COUNTRY_CODE, eventTicketsRS.getString(COL_P_COUNTRY_CODE));
                eventTicketsMap.put(COL_JSON_VALUE, eventTicketsRS.getString(COL_JSON_VALUE));

                if(holderUserEventTickets.get(etHolderUserId) != null){
                    holderUserEventTickets.get(etHolderUserId).add(eventTicketsMap);
                }else {
                    List<Map<String, Object>> holderUserEventTicket = new ArrayList<>();
                    holderUserEventTicket.add(eventTicketsMap);
                    holderUserEventTickets.put(etHolderUserId, holderUserEventTicket);
                }
            }
            System.out.println("EventTickets data ready");

            ResultSet sessionsRS = dbRequest.getQueryResult(this.createSessionsTableQuery(downloadCSVRequestDto).toString());
            List<String> sessionTitle = new ArrayList<>();
            List<SessionDownloadCSVDto> sessions = new ArrayList<>();
            while (sessionsRS.next()){
                sessionTitle.add(sessionsRS.getString(COL_TITLE));
                sessions.add(new SessionDownloadCSVDto(sessionsRS.getLong("session_id")));
            }
            System.out.println("Sessions Title data ready");

            if (CollectionUtils.isNotEmpty(sessionTitle)){
                headerList.addAll(sessionTitle);
            }

            ResultSet rsEventTickets = dbRequest.getQueryResult(getEventTickets(downloadCSVRequestDto.getEventId()).toString());
            List<EventTicketDownloadCSVDto> tickets = new ArrayList<>();
            while (rsEventTickets.next()) {
                tickets.add(new EventTicketDownloadCSVDto(
                        rsEventTickets.getString("h_first_name"),
                        rsEventTickets.getString("h_last_name"),
                        rsEventTickets.getString("h_email"),
                        rsEventTickets.getLong("holder_user_id")
                ));
            }

            System.out.println("EventTicket Dto ready");

            ResultSet rsUserSessions = dbRequest.getQueryResult(this.getUserSessions(downloadCSVRequestDto.getEventId()).toString());
            List<UserSessionsDownloadCSVDto> userSessions = new ArrayList<>();
            while (rsUserSessions.next()) {
                userSessions.add(new UserSessionsDownloadCSVDto(
                        rsUserSessions.getLong("user_id"),
                        rsUserSessions.getLong("session_id"),
                        rsUserSessions.getTimestamp("check_in_time"),
                        rsUserSessions.getString("session_status"),
                        rsUserSessions.getBoolean("is_bookmarked")
                ));
            }

            System.out.println("userSessions DTO ready");

            Map<Long, List<UserSessionsDownloadCSVDto>> userSessionMap = userSessions.stream()
                    .collect(Collectors.groupingBy(UserSessionsDownloadCSVDto::getUserId));

            List<AttendeeAnalyticsDTO> attendeeAnalyticsDTOs = new ArrayList<>();

            // Process each ticket
            for (EventTicketDownloadCSVDto ticket : tickets) {
                Long userId = ticket.getHolderUserId();

                // Get user sessions for this user
                List<UserSessionsDownloadCSVDto> userSessionsForUser = userSessionMap.getOrDefault(userId, Collections.emptyList());

                // Create a map of sessionId to UserSession for this user
                //In case duplicate records found select the last record from user_sessions table for the same sessionId
                Map<Long, UserSessionsDownloadCSVDto> sessionMap = userSessionsForUser.stream()
                        .collect(Collectors.toMap(UserSessionsDownloadCSVDto::getSessionId, Function.identity(), (existing, replacement) -> replacement));

                // For each session, create a DTO if there's a user session or just basic info
                for (SessionDownloadCSVDto session : sessions) {
                    Long sessionId = session.getId();
                    UserSessionsDownloadCSVDto userSession = sessionMap.get(sessionId);

                    String status = "";
                    if (userSession != null) {
                        if (userSession.getCheckInTime() != null) {
                            status = "Joined";
                        } else if ("REGISTERED".equals(userSession.getSessionStatus())) {
                            status = "REGISTERED";
                        } else if (userSession.isBookmarked() && "BOOKMARKED".equals(userSession.getSessionStatus())) {
                            status = "BOOKMARKED";
                        }
                    }

                    attendeeAnalyticsDTOs.add(new AttendeeAnalyticsDTO(
                            ticket.getFirstName(),
                            ticket.getLastName(),
                            ticket.getEmail(),
                            sessionId,
                            userId,
                            status
                    ));
                }
            }

            System.out.println("Attendee analytics data ready");

            Map<Long, List<Map<Long,String>>> savedAttendeeDetail = new HashMap<>();
            List<AttendeeAnalyticsDTO> attendeeSessionDetail = UserSessionHelper.updateAttendeeSessionDetail(attendeeAnalyticsDTOs,savedAttendeeDetail);

            attendeeSessionDetail.forEach(attendeeSession->{
                List<String> dataList = new ArrayList<>();

                // Handle Required Questions Answers
                prepareTicketHolderRequiredAttributesDataList(listOfAttributesHavingOnlyEnableForTicketHolder, dataList,
                        holderUserEventTickets.get(attendeeSession.getUserId()) );

                // Handle List Of sessions
                if(MapUtils.isNotEmpty((savedAttendeeDetail))){
                    List<Map<Long,String>> listSession = savedAttendeeDetail.get(attendeeSession.getUserId());
                    listSession.forEach(attendeeMap-> dataList.addAll(attendeeMap.values()));
                }
                dataRowList.add(dataList);
            });

            System.out.println("CSV download data ready");
            this.downloadCSVFile(writer, headerList, dataRowList);

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public void downloadCSVFile(PrintWriter writer, List<String> headerList, List<List<String>> dataRowList) {
        try (CSVWriter csvWriter = new CSVWriter(writer, CSVWriter.DEFAULT_SEPARATOR,CSVWriter.DEFAULT_QUOTE_CHARACTER,CSVWriter.DEFAULT_ESCAPE_CHARACTER,CSVWriter.DEFAULT_LINE_END)) {

            String[] header = headerList.toArray(new String[0]);
            csvWriter.writeNext(header);

            for (List<String> dataList : dataRowList) {

                String[] dataCSV = dataList.toArray(new String[0]);

                csvWriter.writeNext(dataCSV);
            }
            csvWriter.flush();
        } catch (IOException ex) {
            System.err.println("IOException => " + ex);
        }
    }

    private StringBuilder createTicketHolderRequiredAttributesTableQuery(DownloadCSVRequestDto downloadCSVRequestDto) {
        StringBuilder queryBuilder = new StringBuilder("SELECT attribute_name, attribute_value_type, is_attribute, enabled_for_ticket_holder ");
        queryBuilder.append(" FROM ticket_holder_required_attributes thra ");
        queryBuilder.append(" WHERE thra.event_id = ");
        queryBuilder.append(downloadCSVRequestDto.getEventId());
        queryBuilder.append(" AND thra.recurring_event_id is NULL ");
        queryBuilder.append(" AND thra.data_type = 'TICKET' ORDER BY thra.buyer_attribute_order ");

        return queryBuilder;
    }

    private StringBuilder createEventTicketsTableQuery(DownloadCSVRequestDto downloadCSVRequestDto) {
        StringBuilder queryBuilder = new StringBuilder("SELECT et.id, et.holder_user_id, et.h_phone_number, et.h_country_code, u.phone_number as p_phone_number, u.country_code AS p_country_code, et.data_type, tha.json_value ");
        queryBuilder.append(" FROM event_tickets et ");
        queryBuilder.append(" JOIN users u on et.ticket_purchaser_id = u.user_id ");
        queryBuilder.append(" JOIN ticket_holder_attributes tha on et.ticket_holder_attributes_id = tha.id ");
        queryBuilder.append(" WHERE et.event_id = ");
        queryBuilder.append(downloadCSVRequestDto.getEventId());
        queryBuilder.append(" AND et.recurring_event_id is NULL ");

        return queryBuilder;
    }

    private StringBuilder createSessionsTableQuery(DownloadCSVRequestDto downloadCSVRequestDto){
        StringBuilder queryBuilder = new StringBuilder(" SELECT s.id as session_id, s.title FROM sessions s ");
        queryBuilder.append(" WHERE s.event_id = ");
        queryBuilder.append(downloadCSVRequestDto.getEventId());
        queryBuilder.append(" AND s.rec_status <> 'DELETE' ");
        queryBuilder.append(" ORDER BY id ASC ");

        return queryBuilder;
    }

    private StringBuilder getEventTickets(Long eventId) {
        return new StringBuilder()
                .append("SELECT et.h_first_name, et.h_last_name, et.h_email, et.holder_user_id ")
                .append("FROM event_tickets et ")
                .append("WHERE event_id = ").append(eventId)
                .append(" AND (rec_status IS NULL OR rec_status <> 'CANCEL')");
    }

    private StringBuilder getUserSessions(Long eventId) {
        return new StringBuilder()
                    .append("SELECT us.user_id, us.session_id, us.check_in_time, us.session_status, us.is_bookmarked ")
                .append("FROM user_sessions us ")
                .append("WHERE event_id = ").append(eventId)
                .append(" AND rec_status != 'DELETE'");
    }

    private void prepareTicketHolderRequiredAttributesDataList(List<Map<String, Object>> finalListOfAttributesHavingOnlyEnableForTicketHolder,
                                                               List<String> dataList,
                                                               List<Map<String, Object>> eventTicketsList) {
        // Handle Required Questions Answers
        if (CollectionUtils.isNotEmpty(eventTicketsList)) {
            Map<String, Object> eventTicket = eventTicketsList.get(0);
            String ticketHolderAttributesJsonValueString = (String) eventTicket.get(COL_JSON_VALUE);
            for (Map<String, Object> ticketHolderRequiredAttributes : finalListOfAttributesHavingOnlyEnableForTicketHolder) {
                TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributesJsonValueString);
                TicketHolderUtils.prepareDataList(eventTicket, ticketAttributeValueDto, dataList, ticketHolderRequiredAttributes, TICKET_BUYER_UPLOADS_URL, true, false, null, false, false);
            }
        }
        else  {
            for (Map<String, Object> ignored : finalListOfAttributesHavingOnlyEnableForTicketHolder) {
                dataList.add("");
            }
        }
    }


}
