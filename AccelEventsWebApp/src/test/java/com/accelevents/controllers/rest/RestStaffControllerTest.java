package com.accelevents.controllers.rest;


import com.accelevents.common.dto.SearchAttendeeDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.AccessTokenContainer;
import com.accelevents.dto.ResponseDto;
import com.accelevents.dto.TicketCheckInDto;
import com.accelevents.dto.UserLoginDto;
import com.accelevents.services.TicketHolderAttributesService;
import com.accelevents.session_speakers.dto.UserSessionDTO;
import com.accelevents.utils.Constants;
import com.google.gson.Gson;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Component
public class RestStaffControllerTest{

    @Autowired
    private TicketHolderAttributesService ticketHolderAttributesService;

    public void checkInWithEmailSuccess(MockMvc mockMvc) throws Exception {
        Event event = WebLayerTestDataUtils.getEvent();
        User user = WebLayerTestDataUtils.getUser("Normal", "User", "<EMAIL>");
        SearchAttendeeDto searchAttendeeDto = new SearchAttendeeDto();
        searchAttendeeDto.setEmail(user.getEmail());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());
        Gson gson = new Gson();
        String json = gson.toJson(searchAttendeeDto);

        String accessToken = loginUser(mockMvc);

        MvcResult result = mockMvc.perform(post(getRestStaffControllerUrl(event) +  "/checkin/eventTicket/")
                        .header("Authorization", accessToken)
                        .content(json)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        TicketCheckInDto ticketCheckInDto = new Gson().fromJson(response, TicketCheckInDto.class);
        assertNotNull(ticketCheckInDto);
        assertEquals(ticketCheckInDto.getStatus(), TicketStatus.CHECKED_IN.getStatus());
    }

    public void checkInWithTicketIdSuccess(MockMvc mockMvc) throws Exception {
        Event event = WebLayerTestDataUtils.getEvent();
        User user = WebLayerTestDataUtils.getUser("participant", "User", "<EMAIL>");
        SearchAttendeeDto searchAttendeeDto = new SearchAttendeeDto();
        searchAttendeeDto.setTicketNumber(2L);
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());
        Gson gson = new Gson();
        String json = gson.toJson(searchAttendeeDto);

        String accessToken = loginUser(mockMvc);

        MvcResult result = mockMvc.perform(post(getRestStaffControllerUrl(event) +  "/checkin/eventTicket/")
                        .header("Authorization", accessToken)
                        .content(json)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        TicketCheckInDto ticketCheckInDto = new Gson().fromJson(response, TicketCheckInDto.class);
        assertNotNull(ticketCheckInDto);
        assertEquals(ticketCheckInDto.getStatus(), TicketStatus.CHECKED_IN.getStatus());
    }

    public void checkInNoRequestBodyError(MockMvc mockMvc) throws Exception {
        Event event = WebLayerTestDataUtils.getEvent();
        String accessToken = loginUser(mockMvc);
        mockMvc.perform(post(getRestStaffControllerUrl(event) + "/checkin/eventTicket/")
                        .header("Authorization", accessToken)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotAcceptable());
    }


    public void checkInWithBarcodeSuccess(MockMvc mockMvc) throws  Exception {
        String barcode = "barcode-1";
        Event event = WebLayerTestDataUtils.getEvent();
        String accessToken = loginUser(mockMvc);
        MvcResult result = mockMvc.perform(get( getRestStaffControllerUrl(event) + "/checkin/barcode/"+barcode+"/checkin/true")
                        .header("Authorization", accessToken)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        TicketCheckInDto ticketCheckInDto = new Gson().fromJson(response, TicketCheckInDto.class);
        assertNotNull(ticketCheckInDto);
        assertEquals(ticketCheckInDto.getStatus(), TicketStatus.CHECKED_IN.getStatus());
    }

    public void sessionCheckInWithBarcodeSuccess(MockMvc mockMvc) throws  Exception {
        String barcode = "barcode-4";
        Event event = WebLayerTestDataUtils.getEvent();
        User user = WebLayerTestDataUtils.getUser("participant", "User", "<EMAIL>");
        UserSessionDTO userSessionDTO = new UserSessionDTO();
        userSessionDTO.setUserId(user.getUserId());
        userSessionDTO.setSessionId(1L);
        userSessionDTO.setJoin(true);

        String accessToken = loginUser(mockMvc);
        Gson gson = new Gson();
        String json = gson.toJson(userSessionDTO);

        MvcResult result = mockMvc.perform(post( getRestStaffControllerUrl(event) + "/session/checkIn/barcode/"+ barcode)
                        .header("Authorization", accessToken)
                        .content(json)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        ResponseDto responseDto = new Gson().fromJson(response, ResponseDto.class);
        assertNotNull(responseDto);
        assertEquals(responseDto.getType(), Constants.SUCCESS);
    }

    public void sessionCheckInBarcodeError(MockMvc mockMvc) throws  Exception {
        String barcode = "a40dc928-89a5-4497-83a7-b56215a46b16";
        Event event = WebLayerTestDataUtils.getEvent();
        String accessToken = loginUser(mockMvc);
        mockMvc.perform(post( getRestStaffControllerUrl(event) + "/session/checkIn/barcode/"+ barcode)
                        .header("Authorization", accessToken).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotAcceptable());
    }

    private String getRestStaffControllerUrl(Event event) {
        return "/rest/events/" + event.getEventURL() + "/staff";
    }

    private String loginUser(MockMvc mockMvc) throws Exception {
        UserLoginDto userLoginDto = new UserLoginDto();
        userLoginDto.setUsername(Constants.ADMIN_EMAIL_ID);
        userLoginDto.setPassword("password");
        userLoginDto.setLoginSource(Constants.WEB_APP);

        Gson gson = new Gson();
        String json = gson.toJson(userLoginDto);
        MvcResult result = mockMvc.perform(post("/rest/u/login/")
                        .content(json)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        AccessTokenContainer accessTokenContainer1 = gson.fromJson(response, AccessTokenContainer.class);
        return accessTokenContainer1.getAccess_token();
    }
}