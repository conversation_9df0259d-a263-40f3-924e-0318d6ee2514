#Application PORT
server.port = 9090
server.error.include-message=always

#Application Base URL
uiBaseurl =https://www.stagingaccel.com:8080/AccelEventsWebApp
apiBaseUrl = http://localhost:9090

application.environment=dev
#application.environment=stage
#application.environment=prod
stripe.texttogive.product=prod_Cenq4VrQpqZ3EU

#RDS Stage Database Configuration
spring.datasource.url=*****************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.testWhileIdle = true
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1

#Default Phonenumber
default.phoneNumber=4152344879

spring.jpa.hibernate.ddl-auto=validate
#spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.hibernate.generate_statistics=true


spring.jpa.show-sql=false
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.datasource.initialize=false
spring.jpa.hibernate.ddl-auto=create-drop
spring.datasource.url=jdbc:h2:~/test3
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

#S3 Bucket
# you can get into iam page [https://console.aws.amazon.com/iam/home]
# do not change below credential, because We are using DefaultAWSCredentialsProviderChain in DEV,STAGE,PROD environment, and we use accessKey and secretKey in local environment
cloud.aws.credentials.accessKey="DefaultAWSCredentialsProviderChain"
cloud.aws.credentials.secretKey="DefaultAWSCredentialsProviderChain"

# example : s3-bucket-test-1
cloud.aws.s3.bucket=v2-dev-images-public

#SQS Queue
aws.queueName=EmailMessageQueue

#AWS APIGATEWAY
aws.api.region=us-east-1
aws.api.serviceName=execute-api
aws.api.baseUrl=https://te051bsnf9.execute-api.us-east-1.amazonaws.com/AEMatch/@connections/
aws.apigateway.rest.baseurl=http://localhost:9090


#File Upload
# Set the file size limit (default 1Mb). If you want to specify that files be 
# unlimited set the multipart.maxFileSize property to -1.
#Here set 100MB for file upload limit but in code we have set it to 10MB only. Here 100MB because when we set it here 10MB only and host upload the 11MB file then its throws un-handle exception
spring.http.multipart.max-file-size=200MB

# Set the total request size for a multipart/form-data (default 10Mb)
spring.http.multipart.max-request-size=200MB

#Sendgrid
spring.sendgrid.api-key=*********************************************************************

#Event ids which are used holder and profile sync process - ALL-IN summit event
sync.profile.eventids = 2361

#This domain's email required okta authentication for particular whitelabel's events
okta.authentication.required.domain=gmail.com
okta.authentication.required.whitelabelid=1

#handle error for spring boot
server.error.whitelabel.enabled=false
#stripe configuration
stripe.AUTHORIZE_URI=https://connect.stripe.com/oauth/authorize
stripe.TOKEN_URI=https://connect.stripe.com/oauth/token
stripe.clientId=ca_9hKpQHJERAbQzNMBJKOa3vdgLLXPFU6h
stripe.apiKey=sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf
stripe.publicKey=pk_test_0xmci0YghCzVIK8y2PIlDhdY
stripe.fixedCharge = 0.30
stripe.percentCharge = 2.9

#square configuration
square.AUTHORIZE_URI=https://connect.squareup.com/oauth2/authorize
square.TOKEN_URI=https://connect.squareup.com/oauth2/token
square.CLIENT_URI=https://connect.squareup.com/oauth2/clients
square.mobile.AUTHORIZE=https://connect.squareup.com/mobile/authorization-code

#TestApplicationLive App credentials
#square.clientId=*****************************
#square.appSecretKey=sq0csp-aTtUwI4AYsmELLnLNG1RyT4SeJ3su_3upt5j_3kBF2Q

#AccelEventTestApp
#Sandbox Access Token
square.clientId=sandbox-sq0idp-A57Oi3xFvkk3-3fNftKu6w
#Sandbox Access Token
square.appSecretKey=sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w
square.sandbox.mode=true
#Coffee & Toffee SF	1455 Market Street
square.connect.ae.locationId=CBASEGMgZfZldaslqzMCk33YnHggAQ

#Twilio
twilio.ACCOUNT_SID=**********************************
twilio.AUTH_TOKEN=eb586a8cbd798fa78c9deb6f8e7d3d94
twilio.MESSAGE_SERVICE_SID=

#Default Images
images.acceleventlogo = accelevent-logo.png
images.blacklogo = Accelevents_Default_Event_Logo.jpg
images.accelevents.default.blacklogo = default_ae_images/Smooth_AE_Icon_700x350.png
images.whitelogo = 6bafabd0-5f33-4dcc-a95c-602babb11761accelevents-logo-white.png
images.defaultitem = eee2f81b-92c8-4826-92b6-68a64fb696b7A_600x600.jpg
images.defaultheader = newheaderlogo.JPG
images.defaultBanner = default_banner_image.jpg
color.default.displayBackgroundColor= #406AE8
color.default.displayTextColor= #000000
color.default.displayTabsTextColor=#000000
images.defaultBanner_theme_A = default_ae_banner_theme_A.jpg
images.defaultBanner_theme_B = default_ae_banner_theme_B.jpg
images.defaultBanner_theme_C = default_ae_banner_theme_C.jpg
color.default.displayBackgroundColor_theme_A_and_B= #FFFFFF
color.default.displayBackgroundColor_theme_C= #F7F7FA
color.default.displayTextColor_theme_A= #FFFFFF
color.default.displayTextColor_theme_B_and_C= #1E2137
color.default.displayTabsColor= #406AE8
color.default.displayTabsTextColor_theme_A_and_B=#4B4D5F
color.default.displayTabsTextColor_theme_C=#6D6F7D

image.prefix = http://v2-dev-images-public.s3-website-us-east-1.amazonaws.com/
image.bucketUrl = https://v2-dev-images-public.s3.amazonaws.com/

#Google API Key For URL Shortner
google.shortnerApiKey=AIzaSyDW-Hz1HHbjlZKwHSsRC8i_sgzQpZFZ9g0
rebrandly.shortnerApiKey=********************************
 
#Google API Key For other Services
google.apiKey=AIzaSyDtLyd6ZZn_g4NaPstiJ8QgOLeWnPu0zas

#Intercom APP ID
intercom.app_id=ltwflky8
intercom.token=************************************************************

spring.session.store-type= hash-map
use.redis.session.store=false
security.sessions=always

#At 11:50 PM Daily
cronjob.refreshAcessTokenInterval=0/15 * * * * ?

#Email links
default.getStarted=https://www.accelevents.com/u/signup
default.facebookShare=https://www.facebook.com/accelevents
default.twitterShare=https://twitter.com/accelevents
default.instagramShare=https://www.instagram.com/accelevents
default.linkedInShare=https://www.linkedin.com/company/accelevents

#Sentry
sentry.dsn=

#Seats.io
seats.io.secretKey=2a44d4e8-24f6-4258-9b30-6fdf3060f2a4
seats.io.workspaceKey=42f18566-26e3-46ce-9ecf-2fb729ef18db

#Google app
google.api.token.url=https://www.googleapis.com/oauth2/v4/token
google.client.id=479283504567-dhr5pk76mh6rtft0oh0b72jtu0rlk53u.apps.googleusercontent.com
google.client.secret=43wVPh35oQqMAFV0wZc6CxAE
google.oauth2.accessToken=*********************************************************************************************************************************
google.oauth2.refreshToken=1/w560dGETV8z3tvfYNwmdKxiYOg0AkzgdkpVmLRVSooI
google.analytics.view.id=*********

#Cloudinary configuration
cloudinary.cloud.name=aestage

neon.apiUrl=https://trial.z2systems.com/neonws/services/api

#Cloudinary
cloudinary.API_KEY=631279911478654
cloudinary.API_SECRET=TLsY9m3i5BnZkkeOz-OsEIgvUJw
cloudinary.CLOUD_NAME=aestage
cloudinary.UPLOAD_FOLDER_PATH=uploaded-assets

zoom.secretKey=bdTIkM8kNIwr66VuNWpE9Il4LmMz0gxiotmS
zoom.apiKey=f90q4zSDSc2ggr9sW47zqA