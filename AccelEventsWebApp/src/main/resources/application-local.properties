#Spring
#RDS Local mysql Database Configuration
spring.datasource.url=********************************************************************************************************************************************************************************='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.testWhileIdle = true
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true

spring.jpa.hibernate.ddl-auto=validate
spring.jpa.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.query.in_clause_parameter_padding=true
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.open-in-view=false
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER

app.profile=local

#This domain's email required okta authentication for particular whitelabel's events
okta.authentication.required.domain=xyz.com
okta.authentication.required.whitelabelid=32

management.endpoints.web.exposure.include=health,info,metrics
logging.level.com.zaxxer.hikari=debug

spring.datasource.hikari.leak-detection-threshold=10000
spring.datasource.hikari.maximumPoolSize=10

#File Upload
# Set the file size limit (default 1Mb). If you want to specify that files be unlimited set the multipart.maxFileSize property to -1.
#Here set 300MB for file upload limit but in code we have set it to 200MB only. Here 300MB because when we set it here 200MB only and host upload the 201MB file then its throws un-handle exception
spring.servlet.multipart.max-file-size=200MB

# Set the total request size for a multipart/form-data (default 10Mb)
spring.servlet.multipart.max-request-size=200MB

#Spring Redis
#spring.redis.host=127.0.0.1
#spring.redis.port=6379

use.redis.session.store=false
spring.session.maxInactive=always
#use.redis.session.store=true
#spring.session.maxInactive=300
spring.session.store-type= hash-map

spring.cache.type=SIMPLE

#Sendgrid
spring.sendgrid.api-key=*********************************************************************

spring.jackson.serialization.write-dates-as-timestamps=true

#handle error for spring boot
server.error.whitelabel.enabled=false
server.error.include-message=always

#Event ids which are used holder and profile sync process - ALL-IN summit event
sync.profile.eventids = 2361


#Application PORT
server.port=5080

#Mux Application Domain Validation
domainName =www.devaccel.com
mux.private.key=mux/private_key_DEV_ENV.pem
signing_key=MHBo1xZGOOPiGQ2xcp021N9sUVy3q5TDPg5CwJMLq02BU
mux.data.private.key=mux/private_key_DATA_DEV_ENV.pem
data_signing_key=Wh02tjCUmhEIDhj8ni02vGXHnXRQyHx00ISVgwcMIGMIEA
playback_restriction_id=6kOtScYqO02aLncfKrIz5bZuU01wsxBX9D3fve6zpXIGc

#Application Base URL
uiBaseurl =http://localhost:3000
apiBaseUrl =http://localhost:5080

#Default Phonenumber
default.phoneNumber=4152344879

#Email links
default.getStarted=https://www.accelevents.com/u/signup
default.facebookShare=https://www.facebook.com/accelevents
default.twitterShare=https://twitter.com/accelevents
default.instagramShare=https://www.instagram.com/accelevents
default.linkedInShare=https://www.linkedin.com/company/accelevents

#Default Images
images.blacklogo = default_ae_images/Ae_Icn_700x350.png
images.accelevents.default.blacklogo = default_ae_images/Smooth_AE_Icon_700x350.png
images.whitelogo = default_ae_images/Smooth_Accelevents_Default_Event_Logo_White.svg
images.defaultitem = default_ae_images/Smooth_Accelevents_dark_fill_icon.png
images.defaultBanner = default_banner_image.jpg
images.default.defaultRegistrationIntroBanner = default_ae_images/default_registration_banner_image.png
color.default.displayBackgroundColor= #406AE8
color.default.displayTextColor= #000000
color.default.displayTabsTextColor=#000000
images.defaultBanner_theme_A = default_ae_banner_theme_A.jpg
images.defaultBanner_theme_B = default_ae_banner_theme_B.jpg
images.defaultBanner_theme_C = default_ae_banner_theme_C.jpg
color.default.displayBackgroundColor_theme_A_and_B= #FFFFFF
color.default.displayBackgroundColor_theme_C= #F7F7FA
color.default.displayTextColor_theme_A= #FFFFFF
color.default.displayTextColor_theme_B_and_C= #1E2137
color.default.displayTabsColor= #406AE8
color.default.displayTabsTextColor_theme_A_and_B=#4B4D5F
color.default.displayTabsTextColor_theme_C=#6D6F7D
images.acceleventlogo = Smooth_Accelevents_Default_Event_Logo_Black.png

image.prefix = https://d1jq2jmhdt3fy.cloudfront.net/
image.bucketUrl = https://v2-dev-images-public.s3.amazonaws.com/
image.url=https://d1jq2jmhdt3fy.cloudfront.net/
imageLogo.url=https://d1jq2jmhdt3fy.cloudfront.net/email_template_images

kiosk_default_primary_color=#406AE8
kiosk_default_secondary_color=#030134
kiosk_default_background_image=kiosk_home_background_image.png

#Cloudinary configuration
cloudinary.cloud.name=devaccel

#firebase configuration
app.firebase-config= google/accelevents-1482262106272-firebase-adminsdk-20w9n-1f3f813e31.json

#AWS
#Credential: you can get into iam page [https://console.aws.amazon.com/iam/home]
cloud.aws.credentials.accessKey=********************
cloud.aws.credentials.secretKey=D7XGg+4agISOZ7a9U6eYdYZvhaTR3qXhuARPxGT5
cw.temp.aws.key=********************
cw.temp.aws.id=xzy4esI284uUdtT2nwBMEBERe4Sm+0OdacfhdJhm
temp.google.map.key=AIzaSyBcLGcnTj4zqgo7VtUBI7G50hOnm24QNrY
temp.google.font.key=AIzaSyD-aPQwBvF7_PDm0-3aZUlJ15CY5WyHRFk

#S3 Bucket
# example : s3-bucket-test-1
cloud.aws.s3.bucket=accelevents-dev-public-images
cloud.aws.s3.bucket.ticketBuyerUploads=ticket_buyer_uploads
cloud.aws.s3.bucket.ticketBuyerUploads.url=https://s3.amazonaws.com/v2-dev-images-public/ticket_buyer_uploads/
cloud.aws.s3.bucket.mux.assets=ae-dev-mux-assets
cloud.aws.cloudFront.mux.assets.url=https://d2ar4dy44c0pjp.cloudfront.net/

#SQS Queue
aws.queueName=EmailMessageQueue

#AWS APIGATEWAY
aws.api.region=us-east-1
aws.api.serviceName=execute-api
aws.api.baseUrl=https://te051bsnf9.execute-api.us-east-1.amazonaws.com/AEMatch/@connections/
aws.api.expo.count.websocket.url=https://3qzs6ogh03.execute-api.us-east-1.amazonaws.com/dev/@connections/
aws.apigateway.rest.baseurl=http://localhost:5080

#stripe configuration
stripe.AUTHORIZE_URI=https://connect.stripe.com/oauth/authorize
stripe.TOKEN_URI=https://connect.stripe.com/oauth/token
stripe.clientId=ca_JUzgFRs9UtpO9czEQc8ZEj1ieZsZBSwj
stripe.apiKey=sk_test_51InTEDIGIrlkp2c1Zi8MqismcN6YtzuaiCNzIokEgqCMtK0pu8Or6qSZwPuuDIgbx8tqo82r3KdEQSSHLXj9ZY3e00SE66oW96
stripe.publicKey=pk_test_51InTEDIGIrlkp2c1MZLN2m65ST6xUIKmvgmTEvn4ES96rJXmcACqhYU8xyPOl0vceCplYkZqQvWYmnR7mIeBmHpa00I5Lx8Rzf
stripe.webhook.signing.secret=whsec_U3NeklxHaaYL07eI86MKFLaRnyBIZEvO
stripe.fixedCharge = 0.30
stripe.percentCharge = 2.9

stripe.auction.product=prod_JXfVAtNUAzBNuI
stripe.raffle.product=prod_JXfWlHFCEpsBU8
stripe.fund_a_need.product=prod_JXfajCbIXEayqt
stripe.virtual.product=prod_JV1C1RMEzv5BcY
stripe.texttogive.product=prod_JXfbchJoIh2jg9
stripe.virtual.exhibitorPro=prod_JXfe5fCYbqEKDw

#paypal configuration
paypal.AUTHORIZE_URI=https://www.sandbox.paypal.com/signin/authorize
paypal.BASE_URI=https://api-m.sandbox.paypal.com
paypal.TOKEN_URI=https://api-m.sandbox.paypal.com/v1/oauth2/token
paypal.clientId=AcCwK2Dq5OitghTZVki6lmRIysd_kT9G36bo9MWbwdIyyQXpZ4y9LUXg8So09eGWq8wZ-IktyAAj8UZ-
paypal.apiKey=ECMUibgqXvWYYLCAgdR2ZaD1SLpDJk90o4Pf5apJwthzgOLpYOWLrv8UTUyBRhkaXuxG1RFC5wbiGKxA
paypal.redirectURI=http://localhost:3000/host/settings/credit-card
paypal.merchantId=6ND9Q4GYMGPAC

#payflow configuration
payflow.BASE_URI=https://pilot-payflowpro.paypal.com

#chargebee configuration
chargebee.apiKey=test_SOMuNJcXBocu1Ij5o4wz5cushw3Lnpuv0p
chargebee.site=accelevents-test
chargebee.annual.item.price.id=annual-usages-addon-stage-usd-yearly
chargebee.post.event.access.item.price.id=monthly-post-event-access-usage-addon-usd-monthly
chargebee.monthly.plan.id=Monthly-Overage-Usages-USD-Monthly
chargebee.text_to_give_plan.id=Text-to-Give---Production-USD-Monthly

#square configuration
square.AUTHORIZE_URI=https://connect.squareupsandbox.com/oauth2/authorize
square.TOKEN_URI=https://connect.squareupsandbox.com/oauth2/token
square.CLIENT_URI=https://connect.squareupsandbox.com/oauth2/clients
square.mobile.AUTHORIZE=https://connect.squareupsandbox.com/mobile/authorization-code
square.LOCATION_URI=https://connect.squareupsandbox.com/v2/locations

#TestApplicationLive App credentials
#square.clientId=*****************************
#square.appSecretKey=sq0csp-aTtUwI4AYsmELLnLNG1RyT4SeJ3su_3upt5j_3kBF2Q

#AccelEventTestApp
#Sandbox Access Token
square.clientId=*************************************
#Sandbox Access Token
square.appSecretKey=sandbox-sq0csb-XowLzf7z031RH8f0Ah6jfDntbsPSmm-ZtP1xBZsjxBE
square.sandbox.mode=true
#Coffee & Toffee SF	1455 Market Street
square.connect.ae.locationId=L1E94SJ6QZG60


#Twilio
twilio.ACCOUNT_SID=**********************************
twilio.AUTH_TOKEN=2e9061637a1adbf3803977a3bd6b067d
twilio.api.key=********************************************************************************************
twilio.MESSAGE_SERVICE_SID=

#Vonage
vonageApi.key=********
vonageApi.secret=eeff6b1c7b48fc0f1367063b05270e3aa76ff2d0

#Google API Key For URL Shortner
google.shortnerApiKey=AIzaSyDW-Hz1HHbjlZKwHSsRC8i_sgzQpZFZ9g0
rebrandly.shortnerApiKey=********************************
shortio.apikey=sk_4reEJlBjDDymVryP

#Google API Key For other Services
google.apiKey=AIzaSyCTdjRtF5L54QIJdEQ8DyXlf2umq6MpvEw

#Google app
google.api.token.url=https://www.googleapis.com/oauth2/v4/token
google.client.id=************-dhr5pk76mh6rtft0oh0b72jtu0rlk53u.apps.googleusercontent.com
google.client.secret=43wVPh35oQqMAFV0wZc6CxAE
google.oauth2.accessToken=*********************************************************************************************************************************
google.oauth2.refreshToken=1/w560dGETV8z3tvfYNwmdKxiYOg0AkzgdkpVmLRVSooI
google.analytics.view.id=182825504


#Intercom APP ID
intercom.app_id=ltwflky8
intercom.token=************************************************************


#Sentry
sentry.dsn=
sentry.traces-sample-rate=1.0
application.environment=dev

#Seats.io
seats.io.secretKey=2a44d4e8-24f6-4258-9b30-6fdf3060f2a4
seats.io.workspaceKey=42f18566-26e3-46ce-9ecf-2fb729ef18db


#Neon
neon.apiUrl=https://trial.z2systems.com/neonws/services/api

#Cloudinary
cloudinary.API_KEY=248468548175184
cloudinary.API_SECRET=577d6IU2jKBWeJhfa1I2en7jIDI
cloudinary.CLOUD_NAME=devaccel
cloudinary.UPLOAD_FOLDER_PATH=uploaded-assets
#PipeDrive
pipedrive.apiUrl=https://accelevents-sandbox-3099ae.pipedrive.com/v1
pipedrive.apiToken=****************************************
#PipeDriveField
pipedrive.field.eventurl=****************************************
pipedrive.field.eventId=****************************************
pipedrive.field.ticketSalesStartDate=****************************************
pipedrive.field.eventDate=****************************************
pipedrive.field.eventTime=****************************************
pipedrive.field.totalTicketSales=****************************************
pipedrive.field.totalAEFees=****************************************
pipedrive.field.potentialTicketSales=****************************************
pipedrive.field.potentialAEFees=****************************************
pipedrive.field.dateFirstTicketSold=****************************************
pipedrive.field.assignedSeatingEnabled=****************************************
pipedrive.field.assignedSeatingEnabled.option.true=9
pipedrive.field.assignedSeatingEnabled.option.false=10
pipedrive.field.isrecurringevent=****************************************
pipedrive.field.isrecurringevent.option.true=11
pipedrive.field.isrecurringevent.option.false=12
error.square.email=<EMAIL>

payout.email.receivers=<EMAIL>

getStream.secretKey=49q2jkzhy98h6apacadre8bc2jzvmpgnby46cxjpvrerhddr87zgz9r3nxd7hc9j
getStream.apiKey=4ezv3ck6yrn6
mux.secretKey=pwdZGho0Kg1956HyiElmRi/74YIa0E3U3XomyLPXG87fOPtB7/1CYjX6QsE9i8dnsciYJ90CuUb
mux.apiToken=************************************
voxeet.consumerSecret=iUihMrls5aipZBMtqtmfwuBhNybGbHoXBen0LqZGCBM=
voxeet.consumerKey=ymIWLMSaysGH3-kpZxEA9g==

logging.level.org.hibernate.SQL=ERROR
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=ERROR
logging.file.path=./log

appsync.session.broadcast.api.url=https://session.devaccel.com/graphql
appsync.qa.api.url=https://qa.devaccel.com/graphql
appsync.polls.api.url=https://polls.devaccel.com/graphql
appsync.activityLog.api.url=https://activitylogs.devaccel.com/graphql
appsync.pushNotification.api.url=https://event-notification.devaccel.com/graphql
appsync.attendeePushNotification.api.url=https://attendee.devaccel.com/graphql
appsync.commandCenter.api.url=https://command-center.devaccel.com/graphql
appsync.chime.meeting.api.url=https://chime.devaccel.com/graphql
#Appsync url for Item Out bid
appsync.fundRaisingStatus.api.url=https://fundraising.devaccel.com/graphql
#Appsync url for Notification
appsync.async.api.url=https://async-api.devaccel.com/graphql
#Appsync url for Event Live Stream
appsync.broadcast.eventstream.api.url=https://stream.devaccel.com/graphql
#Appsync url for Chime meeting recording status
appsync.chime.recording.asset.api.url=https://chimers.devaccel.com/graphql
#Appsync url for Attendee Details used for check-in
appsync.attendeeDetails.api.url=https://attendee-details.devaccel.com/graphql


neptune.url=ae-dev-neptune-cluster.cluster-cv6wel746ups.us-east-1.neptune.amazonaws.com
neptune.port=8182
neptune.enableSsl=true
neptune.clusterId=ae-dev-neptune-cluster
neptune.keyStore.type=PKCS12
neptune.keyStore.resourcePath=keystore/neptune-local.pfx
neptune.keyStore.password=AccelLocal

chime.region=us-east-1
chime.queue.name=ae-dev-chime-events
chime.sqs.arn=arn:aws:sqs:us-east-1:147478257448:ae-dev-chime-events
chime.recording.bucket.sinkArn=arn:aws:s3:::ae-dev-chime-workshop-session-recording
chime.recording.meeting.sourceArn=arn:aws:chime::147478257448:meeting
cloud.aws.s3.bucket.workshopRecording=ae-dev-chime-workshop-session-recording
chime.meeting.recording.lambda.name=ae-dev-chime-combine-share-screen-chunk

gamification.elasticsearch.host = localhost
gamification.elasticsearch.port = 9200
gamification.elasticsearch.scheme = http

# Not in Used just kept for the reference
#zoom.secretKey=lb8phx1DPFO2i2dAPBr4LYWE1gVxyHvEaxsi
#zoom.apiKey=VxBh4o3aSnWvY1A_PvKuBA
#zoom.sdkKey=d0dgx29KiyNVSO71OMA11y1SiRgZ5YlHFYpZ

zoom.apiKey=dpPXjFrTQtC_RJoFtNLxNQ
zoom.secretKey=oQgDSD4AE9xikwS0Qgz4NF0PFUF8RtQk

memcached.expiration.sec=300
memcached.addresses=127.0.0.1:11211

youtube.api.url=https://youtube.googleapis.com/youtube/v3/videos
youtube.api.key=AIzaSyCjTyK-IrliJseLHRXF7m27xa6nUD5_nRk

vimeo.api.url=https://api.vimeo.com/videos
vimeo.api.token=********************************

hubspot.url=https://api.hubapi.com/crm/v3
hubspot.url.v4=https://api.hubapi.com/crm/v4
hubspot.api.key=********************************************
hubspot.event.object.name=p_AEevents
hubspot.event.url=https://api.hubspot.com/events/v3
hubspot.event.objectTypeId=2-1309213
hubspot.organizer.object.name=p_Organizer
hubspot.custom.event.name=pe9229002_virtual_event_hub___sales_info_request_new
hubSpot.custom.event.eventCreated.name=pe9229002_event_created
hubSpot.custom.event.addedUser.name=pe9229002_added_user
hubSpot.custom.event.integration.name=pe9229002_integration
hubSpot.custom.event.logIn.name=pe9229002_log_in
hubSpot.custom.event.organizerCreated.name=pe9229002_organizer_created
hubspot.custom.event.viewedFeatureUpgrade.name=pe9229002_viewed_feature_upgrade_v2
hubspot.custom.event.requestedFeatureUpgrade.name=pe9229002_requested_feature_upgrade

recordingViewStartDate=2021/01/22 00:00

agora.appId=********************************
agora.appCertificate=********************************

#tray.io
tray.io.master.token=da3f876aa03840dabb2a83cf1a2247222530782bb7564b13a4a882bb096a7504
tray.io.graphql.url=https://tray.io/graphql
tray.io.embedded.url=https://stageaccel.integration-configuration.com
tray.io.source.marketo.workflow=f93e1485-34a0-4360-91f3-a5f04b00333d
tray.io.source.hubspot.workflow=************************************
tray.io.source.salesforce.workflow=a0519273-9547-49eb-b8d5-75d4c0a961e0
tray.io.source.pardot.workflow=aded9946-6690-4c93-88dc-94f0caf77bfc
tray.io.source.hubspot.mapping.workflow=************************************
tray.io.source.salesforce.mapping.workflow=8d961ec9-cfad-44ae-824a-e549102cd558
tray.io.source.marketo.mapping.workflow=1f57c2fc-ee63-4899-9928-e64a8fb678f8
tray.io.source.pardot.mapping.workflow=3d4c919b-d735-4e55-83e8-28dcf9dbee67
tray.io.source.marketo.tracking.workflow=1870b725-8294-4e52-9a06-db7e169a42e6
tray.io.source.hubspot.tracking.workflow=************************************
tray.io.source.salesforce.tracking.workflow=ed7394a8-8bd1-4592-b0f5-d20712935b37
tray.io.source.salesforce.campaign.recordType.workflow=3ab9f056-b1b0-431d-b811-275f9de31402
tray.io.source.salesforce.custom.workflow=c48e3bac-630c-4992-b5fb-8efd3db56f1b
tray.io.source.salesforce.custom.source.whitelable.id=200
tray.io.source.salesforce.custom.source.organizer.id= 74
tray.io.source.hubspot.discount.criteria.matching.workflow=************************************
tray.io.source.salesforce.discount.criteria.matching.workflow=930e5507-2628-4700-916f-ea7226af051b
tray.io.source.hubspot.retrieve.contact.workflow=************************************
tray.io.source.googlesheet.workflow=da8268d1-ca23-4031-837f-f959afa46f6b
tray.io.source.googlesheet.mapping.workflow=b75ec265-ab3f-486f-9138-c080d4bea9aa


aws.lambda.download.csv.name=ae-dev-download-csv
aws.lambda.engage.email.count.name=ae-dev-engage-email-count
aws.sqs.generate.engage.email=ae-dev-generate-engage-email
aws.sqs.generate.reminder.email=ae-dev-generate-reminder-email

kinesis.stream.name=ae-dev-gamification-stream

dynamodb.user.activity.table=UserActivity
dynamodb.video-analytics.processed.table=ae-dev-video-analytics-processed-data
dynamodb.video-analytics.processed.user-watch-time.table=ae-dev-video-analytics-processed-user-watch-time
dynamodb.meeting-analytics.processed.table=ae-dev-meeting-analytics-processed-data
dynamodb.mobile-analytics.processed.table=ae-dev-mobile-analytics-processed-data

resetEvent.eventIds= 1983

#reset event under organiser id
resetEvent.organiserId= 9245

#linkedin Credentials
linkedinClientId=78bx6ui9ehccq7
linkedinClientSecret=6p3bo9zgDh7wgeLc

applePrivateKey=privatekey
appleClientId=appleClientId
appleKeyId=applekey
appleTeamId=applteamId

custom.css.s3.bucket=ae-dev-custom-css
custom.css.distribution=

mux.asset.glacier.vault.name=ae-dev-mux-live-stream-assets
#Multipart size(byte) for upload archive in to glacier // 4MB
mux.asset.glacier.multipart.upload.size=4194304
mux.asset.glacier.sns.topic.arn=arn:aws:sns:us-east-1:147478257448:ae-dev-mux-asset-archive-retrival
mux.to.s3.trigger.apigw.url=https://oyyhi9dloj.execute-api.us-east-1.amazonaws.com/un

api.rate.limit.per.minute=100

beefreeClientId=246778b9-51de-438d-8755-ab4b46f3af27
beefreeClientSecret=nMHCqRxP3gLmUHCx5snzjbm31B3g9CRZZpuForPVH7DGVya8YXUd

beePageBuilderClientId=c1b4e712-deac-430f-81b2-ca56cdd7d74f
beePageBuilderSecret=1Bw4kyO3B5EwzZVZ4WFux6xOnxtX10dXqYTDtehcfoDqAbdA17ws
beefree.api.token=ee686fdca0c4230d8ae61d62a6bb6e0753951be895e565c1f5185600d8915443
beefree.thumbnail.bucket=accelevents-beefree-dev

slack.check.in.error.webhook=*******************************************************************************
cloudinary.url=https://res.cloudinary.com/devaccel/image/fetch/

cadmium.integration.lambda.name=ae-dev-cadmium-integration

badge.pdf.lambda.name=ae-dev-badge-pdf-generator
badge.pdf.lambda.api.token=3e4fe4c7-3a36-4712-8003-cad668496075

server.servlet.session.cookie.secure=true

spreedly.host.url=https://core.spreedly.com/v1/
spreedly.environment.key=0HRQ2KWV5B9EN8625JYXFRSSJW
spreedly.api.key=vLtjYqbp3snsFyf3MNCTVN0ZApZ2C326B1Fr2rbJhuONCnC1I6M4XcJiljipzHV4
spreedly.private.key=spreedly/private_key_DEV_ENV.pem
spreedly.certificate.token=7XMYTJ29F38JPAG8JTEWFY5ETW

recaptcha.enabled=false

cvent.custom.integration.wl.ids=33