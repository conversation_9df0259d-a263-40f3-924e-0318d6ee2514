awsParameterStorePropertySource.enabled=true
#Spring
#RDS Stage Database Configuration
spring.datasource.url=************************************************************************************************************************************************************************************************
spring.datasource.username=${/app/stage/credential/mysql_username}
spring.datasource.password=${/app/stage/credential/mysql_password}
spring.datasource.testWhileIdle = true
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.driverClassName=com.mysql.jdbc.Driver

spring.jpa.hibernate.ddl-auto=validate
spring.jpa.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.query.in_clause_parameter_padding=true
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.open-in-view=${/app/stage/credential/open_in_view}
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER

app.profile=stage

management.endpoints.web.exposure.include=health,info,metrics
logging.level.com.zaxxer.hikari=debug
logging.pattern.level=%5p %mdc

spring.datasource.hikari.leak-detection-threshold=${/app/stage/credential/leak_detection_threshold}
spring.datasource.hikari.maximumPoolSize=${/app/stage/credential/max_pool_size}

#File Upload
# Set the file size limit (default 1Mb). If you want to specify that files be unlimited set the multipart.maxFileSize property to -1.
#Here set 100MB for file upload limit but in code we have set it to 10MB only. Here 100MB because when we set it here 10MB only and host upload the 11MB file then its throws un-handle exception
spring.servlet.multipart.max-file-size=200MB

# Set the total request size for a multipart/form-data (default 10Mb)
spring.servlet.multipart.max-request-size=200MB

#Spring Redis

use.redis.session.store=true
spring.session.maxInactive=300
#
#
#

#spring.cache.type=REDIS

#Sendgrid
spring.sendgrid.api-key=${/app/stage/credential/sendgrid_api_key}

spring.jackson.serialization.write-dates-as-timestamps=true

#handle error for spring boot
server.error.whitelabel.enabled=false
server.error.include-message=always

#Event ids which are used holder and profile sync process - ALL-IN summit event
sync.profile.eventids = 48086


#This domain's email required okta authentication for particular whitelabel's events
okta.authentication.required.domain=yopmail.com
okta.authentication.required.whitelabelid=61

#Application PORT
server.port=5000

#Mux Application Domain Validation
domainName =www.stagingaccel.com
mux.private.key=mux/private_key_STAGE_ENV.pem
signing_key=NG8KOU8uwADTPlfM3fyAOu1bKAUr02CWb17fNAtqg5xE
mux.data.private.key=mux/private_key_DATA_STAGE_ENV.pem
data_signing_key=qqFES00006ipGJGKJOvvc01Zd02tj2yXwrt8tW00XnFFx1gA
playback_restriction_id=HaKd02azj4IrqftjPeTIjP7Z4vRc01zxerPxFjUnWl6Lw

#Application Base URL   
uiBaseurl =https://www.stagingaccel.com
apiBaseUrl =https://api.stagingaccel.com

#Default Phonenumber
default.phoneNumber=4152344879

#Email links
default.getStarted=https://www.accelevents.com/u/signup
default.facebookShare=https://www.facebook.com/accelevents
default.twitterShare=https://twitter.com/accelevents
default.instagramShare=https://www.instagram.com/accelevents
default.linkedInShare=https://www.linkedin.com/company/accelevents

#Default Images
images.blacklogo = default_ae_images/Ae_Icn_700x350.png
images.accelevents.default.blacklogo = default_ae_images/Smooth_AE_Icon_700x350.png
images.whitelogo = default_ae_images/Smooth_Accelevents_Default_Event_Logo_White.svg
images.defaultitem = default_ae_images/Smooth_Accelevents_dark_fill_icon.png
images.defaultBanner = default_banner_image.jpg
images.default.defaultRegistrationIntroBanner = default_ae_images/default_registration_banner_image.png
color.default.displayBackgroundColor= #406AE8
color.default.displayTextColor= #000000
color.default.displayTabsTextColor=#000000
images.defaultBanner_theme_A = default_ae_banner_theme_A.jpg
images.defaultBanner_theme_B = default_ae_banner_theme_B.jpg
images.defaultBanner_theme_C = default_ae_banner_theme_C.jpg
color.default.displayBackgroundColor_theme_A_and_B= #FFFFFF
color.default.displayBackgroundColor_theme_C= #F7F7FA
color.default.displayTextColor_theme_A= #FFFFFF
color.default.displayTextColor_theme_B_and_C= #1E2137
color.default.displayTabsColor= #406AE8
color.default.displayTabsTextColor_theme_A_and_B=#4B4D5F
color.default.displayTabsTextColor_theme_C=#6D6F7D
images.acceleventlogo = Smooth_Accelevents_Default_Event_Logo_Black.png

image.prefix = https://d2aenk4y8fj821.cloudfront.net/
image.bucketUrl = https://ae-stage-public-images.s3.amazonaws.com/
image.url=https://d2aenk4y8fj821.cloudfront.net/
imageLogo.url=https://d2aenk4y8fj821.cloudfront.net/email_template_images

kiosk_default_primary_color=#406AE8
kiosk_default_secondary_color=#030134
kiosk_default_background_image=kiosk_home_background_image.png

#Cloudinary configuration
cloudinary.cloud.name=aestage

#firebase configuration
app.firebase-config= google/accelevents-1482262106272-firebase-adminsdk-20w9n-1f3f813e31.json

#AWS
#Credential: you can get into iam page [https://console.aws.amazon.com/iam/home] S3 Accesss
# do not change below credential, because We are using DefaultAWSCredentialsProviderChain in DEV,STAGE,PROD environment, and we use accessKey and secretKey in local environment
cloud.aws.credentials.accessKey="DefaultAWSCredentialsProviderChain"
cloud.aws.credentials.secretKey="DefaultAWSCredentialsProviderChain"
cw.temp.aws.key=${/app/stage/credential/cw_temp_aws_key}
cw.temp.aws.id=${/app/stage/credential/cw_temp_aws_id}
temp.google.map.key=${/app/stage/credential/temp_google_map_key}
temp.google.font.key=${/app/stage/credential/temp_google_font_key}

#S3 Bucket
# example : s3-bucket-test-1
cloud.aws.s3.bucket=ae-stage-public-images
cloud.aws.s3.bucket.ticketBuyerUploads=ticket_buyer_uploads
cloud.aws.s3.bucket.ticketBuyerUploads.url=https://ae-stage-public-images.s3.amazonaws.com/ticket_buyer_uploads/
cloud.aws.s3.bucket.mux.assets=${/app/stage/credential/aws_mux_asset_s3_bucket}
cloud.aws.cloudFront.mux.assets.url=https://d1loxp475o9yk6.cloudfront.net/

#SQS Queue
aws.queueName=STAGE_EmailMessageQueue

#AWS APIGATEWAY
aws.api.region=us-east-1
aws.api.serviceName=execute-api
aws.api.baseUrl=${/app/stage/credential/aws_apigateway_networking_websocket_url}
aws.api.expo.count.websocket.url=${/app/stage/credential/aws_apigateway_expo_websocket_url}
aws.apigateway.rest.baseurl=${/app/stage/credential/aws_apigateway_rest_url}

#stripe configuration
stripe.AUTHORIZE_URI=https://connect.stripe.com/oauth/authorize
stripe.TOKEN_URI=https://connect.stripe.com/oauth/token
stripe.clientId=${/app/stage/credential/stripe_clientid}
stripe.apiKey=${/app/stage/credential/stripe_api_key}
stripe.publicKey=${/app/stage/credential/stripe_public_key}
stripe.webhook.signing.secret=whsec_z06ye40d7xXgPSnhMJpaH4JQnqVLxIhe
stripe.fixedCharge = 0.30
stripe.percentCharge = 2.9
stripe.auction.product=${/app/stage/credential/stripe_auction_product}
stripe.raffle.product=${/app/stage/credential/stripe_raffle_product}
stripe.fund_a_need.product=${/app/stage/credential/stripe_fund_a_need_product}
stripe.virtual.product=${/app/stage/credential/stripe_virtual_product}
stripe.texttogive.product=${/app/stage/credential/stripe_texttogive_product}
stripe.virtual.exhibitorPro=${/app/stage/credential/stripe_virtual_exhibitorPro_product}

#paypal configuration
paypal.AUTHORIZE_URI=https://www.sandbox.paypal.com/signin/authorize
paypal.BASE_URI=https://api-m.sandbox.paypal.com
paypal.TOKEN_URI=https://api-m.sandbox.paypal.com/v1/oauth2/token
paypal.clientId=AcCwK2Dq5OitghTZVki6lmRIysd_kT9G36bo9MWbwdIyyQXpZ4y9LUXg8So09eGWq8wZ-IktyAAj8UZ-
paypal.apiKey=ECMUibgqXvWYYLCAgdR2ZaD1SLpDJk90o4Pf5apJwthzgOLpYOWLrv8UTUyBRhkaXuxG1RFC5wbiGKxA
paypal.redirectURI=https://www.stagingaccel.com/host/settings/credit-card
paypal.merchantId=6ND9Q4GYMGPAC

#payflow configuration
payflow.BASE_URI=https://pilot-payflowpro.paypal.com

#chargebee configuration
chargebee.apiKey=${/app/stage/credential/chargebee_apiKey}
chargebee.site=${/app/stage/credential/chargebee_site}
chargebee.annual.item.price.id=annual-usages-addon-stage-usd-yearly
chargebee.post.event.access.item.price.id=monthly-post-event-access-usage-addon-usd-monthly
chargebee.monthly.plan.id=Monthly-Overage-Usages-USD-Monthly
chargebee.text_to_give_plan.id=Text-to-Give---Production-USD-Monthly

#square configuration
square.AUTHORIZE_URI=https://connect.squareupsandbox.com/oauth2/authorize
square.TOKEN_URI=https://connect.squareupsandbox.com/oauth2/token
square.CLIENT_URI=https://connect.squareupsandbox.com/oauth2/clients
square.mobile.AUTHORIZE=https://connect.squareupsandbox.com/mobile/authorization-code
square.LOCATION_URI=https://connect.squareupsandbox.com/v2/locations

#AccelEventTestApp
#Sandbox Access Token
square.clientId=${/app/stage/credential/square_clientId}
#Sandbox Access Token
square.appSecretKey=${/app/stage/credential/square_appSecretKey}
square.sandbox.mode=true
#Coffee & Toffee SF	1455 Market Street
square.connect.ae.locationId=${/app/stage/credential/square_connect_ae_locationId}


#Twilio
twilio.ACCOUNT_SID=${/app/stage/credential/twilio_ACCOUNT_SID}
twilio.AUTH_TOKEN=${/app/stage/credential/twilio_AUTH_TOKEN}
twilio.api.key=${/app/stage/credential/twilio_api_key}
twilio.MESSAGE_SERVICE_SID=${/app/stage/credential/twilio_MESSAGE_SERVICE_SID}


#Vonage
vonageApi.key=${/app/stage/credential/vonageApi_key}
vonageApi.secret=${/app/stage/credential/vonageApi_secret}

#Google API Key For URL Shortner
google.shortnerApiKey=${/app/stage/credential/google_shortnerApiKey}
rebrandly.shortnerApiKey=${/app/stage/credential/rebrandly_shortnerApiKey}
shortio.apikey=${/app/stage/credential/shortio_apikey}

#Google API Key For other Services
google.apiKey=${/app/stage/credential/google_apiKey}

#Google app
google.api.token.url=https://www.googleapis.com/oauth2/v4/token
google.client.id=${/app/stage/credential/google_client_id}
google.client.secret=${/app/stage/credential/google_client_secret}
google.oauth2.accessToken=${/app/stage/credential/google_oauth2_accessToken}
google.oauth2.refreshToken=${/app/stage/credential/google_oauth2_refreshToken}
google.analytics.view.id=${/app/stage/credential/google_analytics_view_id}


#Intercom APP ID
intercom.app_id=${/app/stage/credential/intercom_app_id}
intercom.token=${/app/stage/credential/intercom_token}


#Sentry
sentry.dsn=${/app/stage/credential/sentry_dsn}
sentry.traces-sample-rate=0.02
application.environment=stage

#Seats.io
seats.io.secretKey=${/app/stage/credential/seats_io_secretKey}
seats.io.workspaceKey=${/app/stage/credential/seats_io_WorkSpaceKey}


#Neon
neon.apiUrl=https://trial.z2systems.com/neonws/services/api

#Cloudinary
cloudinary.API_KEY=${/app/stage/credential/cloudinary_API_KEY}
cloudinary.API_SECRET=${/app/stage/credential/cloudinary_API_SECRET}
cloudinary.CLOUD_NAME=aestage
cloudinary.UPLOAD_FOLDER_PATH=uploaded-assets

error.square.email=<EMAIL>

payout.email.receivers=<EMAIL>

getStream.secretKey=${/app/stage/credential/getStream_secretKey}
getStream.apiKey=${/app/stage/credential/getStream_apiKey}
mux.secretKey=${/app/stage/credential/mux_secretKey}
mux.apiToken=${/app/stage/credential/mux_apiToken}
voxeet.consumerSecret=${/app/stage/credential/voxeet_consumerSecret}
voxeet.consumerKey=${/app/stage/credential/voxeet_consumerKey}

appsync.session.broadcast.api.url=https://session.stagingaccel.com/graphql
appsync.qa.api.url=https://qa.stagingaccel.com/graphql
appsync.polls.api.url=https://polls.stagingaccel.com/graphql
appsync.activityLog.api.url=https://activitylogs.stagingaccel.com/graphql
appsync.pushNotification.api.url=https://event-notification.stagingaccel.com/graphql
appsync.attendeePushNotification.api.url=https://attendee.stagingaccel.com/graphql
appsync.commandCenter.api.url=https://command-center.stagingaccel.com/graphql
appsync.chime.meeting.api.url=https://chime.stagingaccel.com/graphql
#Appsync url for Item Out bid
appsync.fundRaisingStatus.api.url=https://fundraising.stagingaccel.com/graphql
#Appsync url for Notification
appsync.async.api.url=https://async-api.stagingaccel.com/graphql
#Appsync url for Event Live Stream
appsync.broadcast.eventstream.api.url=https://stream.stagingaccel.com/graphql
#Appsync url for Chime meeting recording status
appsync.chime.recording.asset.api.url=https://chimers.stagingaccel.com/graphql

aws.lambda.download.csv.name=ae-stage-download-csv
aws.lambda.engage.email.count.name=ae-stage-engage-email-count
aws.sqs.generate.engage.email=ae-stage-generate-engage-email
aws.sqs.generate.reminder.email=ae-stage-generate-reminder-email

neptune.url=${/app/stage/credential/aws_neptune_url}
neptune.port=${/app/stage/credential/aws_neptune_port}
neptune.clusterId=${/app/stage/credential/aws_neptune_cluster_id}
neptune.enableSsl=true
neptune.keyStore.type=PKCS12
neptune.keyStore.resourcePath=${/app/stage/credential/aws_neptune_keystore_filename}
neptune.keyStore.password=${/app/stage/credential/aws_neptune_keystore_password}

chime.region=us-east-1
chime.queue.name=${/app/stage/credential/chime_sqs_name}
chime.sqs.arn=${/app/stage/credential/chime_sqs_arn}
chime.recording.bucket.sinkArn=${/app/stage/credential/chime_recording_s3_bucket_sink_arn}
chime.recording.meeting.sourceArn=${/app/stage/credential/chime_recording_meeting_source_arn}
cloud.aws.s3.bucket.workshopRecording=${/app/stage/credential/chime_s3_bucket_workshop_recordings}
chime.meeting.recording.lambda.name=ae-stage-chime-combine-share-screen-chunk

gamification.elasticsearch.host = ${/app/stage/credential/aws_elasticsearch_gamification_host}
gamification.elasticsearch.port = ${/app/stage/credential/aws_elasticsearch_gamification_port}
gamification.elasticsearch.scheme = https

zoom.secretKey=${/app/stage/credential/zoom_secretKey}
zoom.apiKey=${/app/stage/credential/zoom_apiKey}

memcached.expiration.sec=${/app/stage/credential/aws_memcached_expiration_sec}
memcached.addresses=${/app/stage/credential/aws_memcached_addresses}

redis.gamification.host=${/app/stage/credential/aws_redis_gamification_host}
redis.host=${/app/stage/credential/aws_redis_host}
redis.maxTotal=${/app/stage/credential/aws_redis_max_total}
redis.minIdle=${/app/stage/credential/aws_redis_min_idle}
redis.maxIdle=${/app/stage/credential/aws_redis_max_idle}

youtube.api.url=https://youtube.googleapis.com/youtube/v3/videos
youtube.api.key=${/app/stage/credential/youtube_api_key}

vimeo.api.url=https://api.vimeo.com/videos
vimeo.api.token=${/app/stage/credential/vimeo_api_token}

hubspot.url=https://api.hubapi.com/crm/v3
hubspot.url.v4=https://api.hubapi.com/crm/v4
hubspot.api.key=${/app/stage/credential/hubspot_api_key}
hubspot.event.object.name=p_AEevents
hubspot.event.url=https://api.hubspot.com/events/v3
hubspot.event.objectTypeId=2-7768448
hubspot.organizer.object.name=p_Organizer
hubspot.custom.event.name=pe22353958_virtual_event_hub_sales_info_request
hubSpot.custom.event.eventCreated.name=pe22353958_event_created
hubSpot.custom.event.addedUser.name=pe22353958_added_user
hubSpot.custom.event.integration.name=pe22353958_integration
hubSpot.custom.event.logIn.name=pe22353958_log_in
hubSpot.custom.event.organizerCreated.name=pe22353958_organizer_created
hubspot.custom.event.viewedFeatureUpgrade.name=pe22353958_viewed_feature_upgrade
hubspot.custom.event.requestedFeatureUpgrade.name=pe22353958_requested_feature_upgrade

recordingViewStartDate=2021/10/17 00:00

agora.appId=${/app/stage/credential/agora_appId}
agora.appCertificate=${/app/stage/credential/agora_appCertificate}

#tray.io
tray.io.master.token=${/app/stage/credential/tray_io_master_token}
tray.io.graphql.url=https://tray.io/graphql
tray.io.embedded.url=https://stageaccel.integration-configuration.com
tray.io.source.marketo.workflow=${/app/stage/credential/tray_io_source_marketo_workflow}
tray.io.source.hubspot.workflow=${/app/stage/credential/tray_io_source_hubspot_workflow}
tray.io.source.salesforce.workflow=${/app/stage/credential/tray_io_source_salesforce_workflow}
tray.io.source.pardot.workflow=${/app/stage/credential/tray_io_source_pardot_workflow}
tray.io.source.hubspot.mapping.workflow=${/app/stage/credential/tray_io_mapping_hubspot_workflow}
tray.io.source.salesforce.mapping.workflow=${/app/stage/credential/tray_io_mapping_salesforce_workflow}
tray.io.source.marketo.mapping.workflow=${/app/stage/credential/tray_io_mapping_marketo_workflow}
tray.io.source.pardot.mapping.workflow=${/app/stage/credential/tray_io_mapping_pardot_workflow}
tray.io.source.marketo.tracking.workflow=${/app/satge/credential/tray_io_tracking_marketo_workflow}
tray.io.source.hubspot.tracking.workflow=${/app/stage/credential/tray_io_tracking_hubspot_workflow}
tray.io.source.salesforce.tracking.workflow=${/app/stage/credential/tray_io_tracking_salesforce_workflow}
tray.io.source.salesforce.campaign.recordType.workflow=${/app/stage/credential/tray_io_salesforce_campaign_record_type_workflow}
tray.io.source.salesforce.custom.workflow=${/app/stage/credential/tray_io_source_custom_salesforce_workflow}
tray.io.source.salesforce.custom.source.whitelable.id=${/app/stage/credential/tray_io_source_custom_source_whitelable_id}
tray.io.source.salesforce.custom.source.organizer.id=${/app/stage/credential/tray_io_source_custom_source_organizer_id}
tray.io.source.hubspot.discount.criteria.matching.workflow=************************************
tray.io.source.salesforce.discount.criteria.matching.workflow=6ae781be-729b-4f6e-95df-16ff7e21458f
tray.io.source.hubspot.retrieve.contact.workflow=${/app/stage/credential/tray_io_fetch_hubspot_contact_property_workflow}
tray.io.source.googlesheet.workflow=${/app/stage/credential/tray_io_source_googlesheet_workflow}
tray.io.source.googlesheet.mapping.workflow=${/app/stage/credential/tray_io_mapping_googlesheet_workflow}

kinesis.stream.name=ae-stage-gamification-stream

dynamodb.user.activity.table=${/app/stage/credential/useractivity_dynamodb_table_name}
dynamodb.video-analytics.processed.table=${/app/stage/credential/video_analytics_processed_dynamodb_table_name}
dynamodb.video-analytics.processed.user-watch-time.table=${/app/stage/credential/video_analytics_processed_user_watch_time_dynamodb_table_name}
dynamodb.meeting-analytics.processed.table=${/app/stage/credential/meeting_analytics_processed_data_dynamodb_table_name}
dynamodb.mobile-analytics.processed.table=${/app/stage/credential/mobile_analytics_processed_data_dynamodb_table_name}

#reset event id
resetEvent.eventIds= ${/app/stage/credential/reset_event_ids}

#reset event under organiser id
resetEvent.organiserId= ${/app/stage/credential/reset_event_organiser_id}

#linkedin Credentials
linkedinClientId=${/app/stage/credential/linkedIn_clientId}
linkedinClientSecret=${/app/stage/credential/linkedIn_clientSecret}

#AppleLogin Credentials
applePrivateKey=${/app/stage/credential/apple_authKeyP8}
appleClientId=${/app/stage/credential/apple_clientId}
appleKeyId=${/app/stage/credential/apple_keyId}
appleTeamId=${/app/stage/credential/apple_teamId}

custom.css.s3.bucket=${/app/stage/credential/custom_css_s3_bucket}
custom.css.distribution=${/app/stage/credential/custom_css_cloudfront_distribution_id}

mux.asset.glacier.vault.name=ae-stage-mux-live-stream-assets
#Multipart size(byte) for upload archive in to glacier // 4MB
mux.asset.glacier.multipart.upload.size=4194304
mux.asset.glacier.sns.topic.arn=${/app/stage/credential/mux_live_stream_archive_sns_topic_arn}
mux.to.s3.trigger.apigw.url=${/app/stage/credential/aws_mux_to_s3_trigger_apigw_url}

api.rate.limit.per.minute=${/app/stage/credential/api_rate_limit_count}


beefreeClientId=${/app/stage/credential/beefree_clientId}
beefreeClientSecret=${/app/stage/credential/beefree_clientSecret}

beePageBuilderClientId=${/app/stage/credential/beefree_pageBuilder_clientId}
beePageBuilderSecret=${/app/stage/credential/beefree_pageBuilder_clientSecret}
beefree.api.token=${/app/stage/credential/beefree_api_token}
beefree.thumbnail.bucket=${/app/stage/credential/beefree_thumbnail_bucket}

slack.check.in.error.webhook=*******************************************************************************
cloudinary.url=https://res.cloudinary.com/aestage/image/fetch/

cadmium.integration.lambda.name=ae-stage-cadmium-integration

badge.pdf.lambda.name=ae-stage-badge-pdf-generator
badge.pdf.lambda.api.token=${/app/stage/credential/badge_pdf_token}

server.servlet.session.cookie.secure=true

spreedly.host.url=https://core.spreedly.com/v1/
spreedly.environment.key=${/app/stage/credential/spreedly_environment_key}
spreedly.api.key=${/app/stage/credential/spreedly_api_key}
spreedly.private.key=spreedly/private_key_STAGE_ENV.pem
spreedly.certificate.token=2RF2074E8N973V05S39YBVNS65

recaptcha.enabled=${/app/stage/credential/recaptcha_enabled}

cvent.custom.integration.wl.ids=${/app/stage/credential/cvent_custom_integration_wl_id}