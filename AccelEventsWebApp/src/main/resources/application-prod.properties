awsParameterStorePropertySource.enabled=true
#Spring
#RDS Stage Database Configuration
spring.datasource.url=****************************************************************************************************************************************************************************************************
spring.datasource.username=${/app/prod/credential/mysql_username}
spring.datasource.password=${/app/prod/credential/mysql_password}
spring.datasource.testWhileIdle = true
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.driverClassName=com.mysql.jdbc.Driver

spring.jpa.hibernate.ddl-auto=validate
spring.jpa.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.query.in_clause_parameter_padding=true
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.open-in-view=${OPEN_IN_VIEW}
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER

app.profile=prod

#This domain's email required okta authentication for particular whitelabel's events
okta.authentication.required.domain=intapp.com
okta.authentication.required.whitelabelid=260

management.endpoints.web.exposure.include=health,info,metrics
logging.level.com.zaxxer.hikari=debug
logging.pattern.level=%5p %mdc

spring.datasource.hikari.leak-detection-threshold=10000
spring.datasource.hikari.maximumPoolSize=${MAX_POOL_SIZE}

#File Upload
# Set the file size limit (default 1Mb). If you want to specify that files be unlimited set the multipart.maxFileSize property to -1.
#Here set 100MB for file upload limit but in code we have set it to 100MB only. Here 20MB because when we set it here 10MB only and host upload the 11MB file then its throws un-handle exception
spring.servlet.multipart.max-file-size=200MB

# Set the total request size for a multipart/form-data (default 10Mb)
spring.servlet.multipart.max-request-size=200MB

#Spring Redis
#spring.redis.cluster.nodes=production-0001-001.amcanb.0001.use1.cache.amazonaws.com:6379,production-0001-002.amcanb.0001.use1.cache.amazonaws.com:6379
#spring.redis.cluster.max-redirects=2

#spring.redis.cluster.nodes=${redis_node_url}
#spring.redis.cluster.max-redirects=${redis_max_redirect}

use.redis.session.store=true
spring.session.maxInactive=43200
#
#
#

#spring.cache.type=REDIS

#Sendgrid
spring.sendgrid.api-key=${/app/prod/credential/sendgrid_api_key}

spring.jackson.serialization.write-dates-as-timestamps=true

#handle error for spring boot
server.error.whitelabel.enabled=false
server.error.include-message=always

#Event ids which are used holder and profile sync process - ALL-IN summit event And The MarketCounsel Summit AND Chargebee-Beelieve-25-London AND 2025-Annual-SMA-Conference AND spring-2025-etp-forum
sync.profile.eventids = 119157,117986,125936,119861,127554


#Application PORT
server.port=5000

#Mux Application Domain Validation
domainName =www.accelevents.com
mux.private.key=mux/private_key_PROD_ENV.pem
signing_key=lH01E900mfbEDYOLrGWHl02e9ymu1aqtDWok3E702A5RrLM
mux.data.private.key=mux/private_key_DATA_PROD_ENV.pem
data_signing_key=900xYjfNki5cBesYcBBRrRLyvzEkjLVDqLxkW2dYH5Rw
playback_restriction_id=fu1zhy02ZuaLM01R6551whc2qpCj16pARwRZUWAqoXSSk

#Application Base URL
uiBaseurl =https://www.accelevents.com
apiBaseUrl =https://api.accelevents.com

#Default Phonenumber
default.phoneNumber=4152344879

#Email links
default.getStarted=https://www.accelevents.com/u/signup
default.facebookShare=https://www.facebook.com/accelevents
default.twitterShare=https://twitter.com/accelevents
default.instagramShare=https://www.instagram.com/accelevents
default.linkedInShare=https://www.linkedin.com/company/accelevents

#Default Images
images.blacklogo = default_ae_images/Ae_Icn_700x350.png
images.accelevents.default.blacklogo = default_ae_images/Smooth_AE_Icon_700x350.png
images.whitelogo = default_ae_images/Smooth_Accelevents_Default_Event_Logo_White.svg
images.defaultitem = default_ae_images/Smooth_Accelevents_dark_fill_icon.png
images.defaultBanner = default_banner_image.jpg
images.default.defaultRegistrationIntroBanner = default_ae_images/default_registration_banner_image.png
color.default.displayBackgroundColor= #406AE8
color.default.displayTextColor= #000000
color.default.displayTabsTextColor=#000000
images.defaultBanner_theme_A = default_ae_banner_theme_A.jpg
images.defaultBanner_theme_B = default_ae_banner_theme_B.jpg
images.defaultBanner_theme_C = default_ae_banner_theme_C.jpg
images.acceleventlogo = Smooth_Accelevents_Default_Event_Logo_Black.png

color.default.displayBackgroundColor_theme_A_and_B= #FFFFFF
color.default.displayBackgroundColor_theme_C= #F7F7FA
color.default.displayTextColor_theme_A= #FFFFFF
color.default.displayTextColor_theme_B_and_C= #1E2137
color.default.displayTabsColor= #406AE8
color.default.displayTabsTextColor_theme_A_and_B=#4B4D5F
color.default.displayTabsTextColor_theme_C=#6D6F7D

image.prefix = https://d2413hm4ynib6z.cloudfront.net/
image.bucketUrl = https://v2-s3-prod-accelevents.s3.amazonaws.com/
image.url=https://s3.amazonaws.com/v2-s3-prod-accelevents/
imageLogo.url=https://v2-dev-images-public.s3.amazonaws.com/email_template_images

kiosk_default_primary_color=#406AE8
kiosk_default_secondary_color=#030134
kiosk_default_background_image=kiosk_home_background_image.png

#Cloudinary configuration
cloudinary.cloud.name=accelevents

#firebase configuration
app.firebase-config= google/accelevents-1482262106272-firebase-adminsdk-20w9n-1f3f813e31.json


#AWS
#Credential: you can get into iam page [https://console.aws.amazon.com/iam/home]
# do not change below credential, because We are using DefaultAWSCredentialsProviderChain in DEV,STAGE,PROD environment, and we use accessKey and secretKey in local environment
cloud.aws.credentials.accessKey="DefaultAWSCredentialsProviderChain"
cloud.aws.credentials.secretKey="DefaultAWSCredentialsProviderChain"
cw.temp.aws.key=${/app/prod/credential/cw_temp_aws_key}
cw.temp.aws.id=${/app/prod/credential/cw_temp_aws_id}
temp.google.map.key=${/app/prod/credential/temp_google_map_key}
temp.google.font.key=${/app/prod/credential/temp_google_font_key}

#S3 Bucket
# example : s3-bucket-test-1
cloud.aws.s3.bucket=v2-s3-prod-accelevents
cloud.aws.s3.bucket.ticketBuyerUploads=ticket_buyer_uploads
cloud.aws.s3.bucket.ticketBuyerUploads.url=https://s3.amazonaws.com/v2-s3-prod-accelevents/ticket_buyer_uploads/
cloud.aws.s3.bucket.mux.assets=${/app/prod/credential/aws_mux_asset_s3_bucket}
cloud.aws.cloudFront.mux.assets.url=https://dwgj4sy35ma4l.cloudfront.net/

#SQS Queue
aws.queueName=PROD_EmailMessageQueue

#AWS APIGATEWAY
aws.api.region=us-east-1
aws.api.serviceName=execute-api
aws.api.baseUrl=${/app/prod/credential/aws_apigateway_networking_websocket_url}
aws.apigateway.rest.baseurl=${/app/prod/credential/aws_apigateway_rest_url}

#stripe configuration
stripe.AUTHORIZE_URI=https://connect.stripe.com/oauth/authorize
stripe.TOKEN_URI=https://connect.stripe.com/oauth/token
stripe.clientId=${/app/prod/credential/stripe_clientid}
stripe.apiKey=${/app/prod/credential/stripe_api_key}
stripe.publicKey=${/app/prod/credential/stripe_public_key}
stripe.webhook.signing.secret=whsec_ZH6Pi2UCN265qFR77xaNa1QcqugVsQFV
stripe.fixedCharge = 0.30
stripe.percentCharge = 2.9
stripe.auction.product=${/app/prod/credential/stripe_auction_product}
stripe.raffle.product=${/app/prod/credential/stripe_raffle_product}
stripe.fund_a_need.product=${/app/prod/credential/stripe_fund_a_need_product}
stripe.virtual.product=${/app/prod/credential/stripe_virtual_product}
stripe.texttogive.product=${/app/prod/credential/stripe_texttogive_product}
stripe.virtual.exhibitorPro=${/app/prod/credential/stripe_virtual_exhibitorPro_product}

#paypal configuration
paypal.AUTHORIZE_URI=https://www.paypal.com/signin/authorize
paypal.BASE_URI=https://api-m.paypal.com
paypal.TOKEN_URI=https://api-m.paypal.com/v1/oauth2/token
paypal.clientId=AcCwK2Dq5OitghTZVki6lmRIysd_kT9G36bo9MWbwdIyyQXpZ4y9LUXg8So09eGWq8wZ-IktyAAj8UZ-
paypal.apiKey=ECMUibgqXvWYYLCAgdR2ZaD1SLpDJk90o4Pf5apJwthzgOLpYOWLrv8UTUyBRhkaXuxG1RFC5wbiGKxA
paypal.redirectURI=https://www.accelevents.com/host/settings/credit-card
paypal.merchantId=6ND9Q4GYMGPAC

#payflow configuration
payflow.BASE_URI=https://payflowpro.paypal.com

#chargebee configuration
chargebee.apiKey=${/app/prod/credential/chargebee_apiKey}
chargebee.site=${/app/prod/credential/chargebee_site}
chargebee.annual.item.price.id=annual-usages-addon-stage-usd-yearly
chargebee.post.event.access.item.price.id=monthly-post-event-access-usage-addon-usd-monthly
chargebee.monthly.plan.id=Monthly-Overage-Usages-USD-Monthly
chargebee.text_to_give_plan.id=Text-to-Give---Production-USD-Monthly

#square configuration
square.AUTHORIZE_URI=https://connect.squareup.com/oauth2/authorize
square.TOKEN_URI=https://connect.squareup.com/oauth2/token
square.CLIENT_URI=https://connect.squareup.com/oauth2/clients
square.mobile.AUTHORIZE=https://connect.squareup.com/mobile/authorization-code
square.LOCATION_URI=https://connect.squareup.com/v2/locations


#
#
#

#
#Square API
square.clientId=${/app/prod/credential/square_clientId}
square.appSecretKey=${/app/prod/credential/square_appSecretKey}
square.connect.ae.locationId=${/app/prod/credential/square_connect_ae_locationId}
square.sandbox.mode=false
#
#


#Twilio
twilio.ACCOUNT_SID=${/app/prod/credential/twilio_ACCOUNT_SID}
twilio.AUTH_TOKEN=${/app/prod/credential/twilio_AUTH_TOKEN}
twilio.api.key=${/app/prod/credential/twilio_api_key}
twilio.MESSAGE_SERVICE_SID=${/app/prod/credential/twilio_MESSAGE_SERVICE_SID}

#Vonage
vonageApi.key=${/app/prod/credential/vonageApi_key}
vonageApi.secret=${/app/prod/credential/vonageApi_secret}

#Google API Key For URL Shortner
google.shortnerApiKey=${/app/prod/credential/google_shortnerApiKey}
rebrandly.shortnerApiKey=${/app/prod/credential/rebrandly_shortnerApiKey}
shortio.apikey=${/app/prod/credential/shortio_apikey}

#Google API Key For other Services
google.apiKey=${/app/prod/credential/google_apiKey}

#Google app
google.api.token.url=https://www.googleapis.com/oauth2/v4/token
google.client.id=${/app/prod/credential/google_client_id}
google.client.secret=${/app/prod/credential/google_client_secret}
google.oauth2.accessToken=${/app/prod/credential/google_oauth2_accessToken}
google.oauth2.refreshToken=${/app/prod/credential/google_oauth2_refreshToken}
google.analytics.view.id=${/app/prod/credential/google_analytics_view_id}


#Intercom APP ID
intercom.app_id=${/app/prod/credential/intercom_app_id}
intercom.token=${/app/prod/credential/intercom_token}


#Sentry
sentry.dsn=${/app/prod/credential/sentry_dsn}
sentry.traces-sample-rate=0.01
application.environment=prod

#Seats.io
seats.io.secretKey=${/app/prod/credential/seats_io_secretKey}
seats.io.workspaceKey=${/app/prod/credential/seats_io_WorkSpaceKey}


#Neon
neon.apiUrl=https://api.neoncrm.com/neonws/services/api

#Cloudinary
cloudinary.API_KEY=${/app/prod/credential/cloudinary_API_KEY}
cloudinary.API_SECRET=${/app/prod/credential/cloudinary_API_SECRET}
cloudinary.CLOUD_NAME=accelevents
cloudinary.UPLOAD_FOLDER_PATH=uploaded-assets

error.square.email=<EMAIL>,<EMAIL>

payout.email.receivers=<EMAIL>,<EMAIL>
getStream.secretKey=${/app/prod/credential/getStream_secretKey}
getStream.apiKey=${/app/prod/credential/getStream_apiKey}
mux.secretKey=${/app/prod/credential/mux_secretKey}
mux.apiToken=${/app/prod/credential/mux_apiToken}
voxeet.consumerSecret=${/app/prod/credential/voxeet_consumerSecret}
voxeet.consumerKey=${/app/prod/credential/voxeet_consumerKey}

appsync.session.broadcast.api.url=https://session.accelevents.com/graphql
appsync.qa.api.url=https://qa.accelevents.com/graphql
appsync.polls.api.url=https://polls.accelevents.com/graphql
appsync.activityLog.api.url=https://activitylogs.accelevents.com/graphql
appsync.pushNotification.api.url=https://event-notification.accelevents.com/graphql
appsync.attendeePushNotification.api.url=https://attendee.accelevents.com/graphql
appsync.commandCenter.api.url=https://command-center.accelevents.com/graphql
appsync.chime.meeting.api.url=https://chime.accelevents.com/graphql
#Appsync url for Item Out bid
appsync.fundRaisingStatus.api.url=https://fundraising.accelevents.com/graphql
#Appsync url for Notification
appsync.async.api.url=https://async-api.accelevents.com/graphql
#Appsync url for Event Live Stream
appsync.broadcast.eventstream.api.url=https://stream.accelevents.com/graphql
#Appsync url for Chime meeting recording status
appsync.chime.recording.asset.api.url=https://chimers.accelevents.com/graphql

aws.lambda.download.csv.name=ae-prod-download-csv
aws.lambda.engage.email.count.name=ae-prod-engage-email-count
aws.sqs.generate.engage.email=ae-prod-generate-engage-email
aws.sqs.generate.reminder.email=ae-prod-generate-reminder-email

neptune.url=${/app/prod/credential/aws_neptune_url}
neptune.port=${/app/prod/credential/aws_neptune_port}
neptune.clusterId=${/app/prod/credential/aws_neptune_cluster_id}
neptune.enableSsl=true
neptune.keyStore.type=PKCS12
neptune.keyStore.resourcePath=${/app/prod/credential/aws_neptune_keystore_filename}
neptune.keyStore.password=${/app/prod/credential/aws_neptune_keystore_password}

chime.region=us-east-1
chime.queue.name=${/app/prod/credential/chime_sqs_name}
chime.sqs.arn=${/app/prod/credential/chime_sqs_arn}
chime.recording.bucket.sinkArn=${/app/prod/credential/chime_recording_s3_bucket_sink_arn}
chime.recording.meeting.sourceArn=${/app/prod/credential/chime_recording_meeting_source_arn}
cloud.aws.s3.bucket.workshopRecording=${/app/prod/credential/chime_s3_bucket_workshop_recordings}
chime.meeting.recording.lambda.name=ae-prod-chime-combine-share-screen-chunk

gamification.elasticsearch.host = ${/app/prod/credential/aws_elasticsearch_gamification_host}
gamification.elasticsearch.port = ${/app/prod/credential/aws_elasticsearch_gamification_port}
gamification.elasticsearch.scheme = https

zoom.secretKey=${/app/prod/credential/zoom_secretKey}
zoom.apiKey=${/app/prod/credential/zoom_apiKey}

memcached.expiration.sec=${/app/prod/credential/aws_memcached_expiration_sec}
memcached.addresses=${/app/prod/credential/aws_memcached_addresses}

redis.gamification.host=${/app/prod/credential/aws_redis_gamification_host}
redis.host=${/app/prod/credential/aws_redis_host}
redis.maxTotal=${/app/prod/credential/aws_redis_max_total}
redis.minIdle=${/app/prod/credential/aws_redis_min_idle}
redis.maxIdle=${/app/prod/credential/aws_redis_max_idle}
#spring.redis.cluster.nodes=${redis_node_url}
#spring.redis.cluster.max-redirects=${redis_max_redirect}


youtube.api.url=https://youtube.googleapis.com/youtube/v3/videos
youtube.api.key=${/app/prod/credential/youtube_api_key}

vimeo.api.url=https://api.vimeo.com/videos
vimeo.api.token=${/app/prod/credential/vimeo_api_token}

hubspot.url=https://api.hubapi.com/crm/v3
hubspot.url.v4=https://api.hubapi.com/crm/v4
hubspot.api.key=${/app/prod/credential/hubspot_api_key}
hubspot.event.object.name=p_AEevents
hubspot.event.url=https://api.hubspot.com/events/v3
hubspot.event.objectTypeId=2-1322573
hubspot.organizer.object.name=p_Organizer
hubspot.custom.event.name=pe8377389_virtual_event_hub_sales_info_request
hubSpot.custom.event.eventCreated.name=pe8377389_event_created
hubSpot.custom.event.addedUser.name=pe8377389_added_user
hubSpot.custom.event.integration.name=pe8377389_integration
hubSpot.custom.event.logIn.name=pe8377389_log_in
hubSpot.custom.event.organizerCreated.name=pe8377389_organizer_created
hubspot.custom.event.viewedFeatureUpgrade.name=pe8377389_viewed_feature_upgrade
hubspot.custom.event.requestedFeatureUpgrade.name=pe8377389_requested_feature_upgrade

recordingViewStartDate=2021/02/02 00:00

agora.appId=${/app/prod/credential/agora_appId}
agora.appCertificate=${/app/prod/credential/agora_appCertificate}

#tray.io
tray.io.master.token=${/app/prod/credential/tray_io_master_token}
tray.io.graphql.url=https://tray.io/graphql
tray.io.embedded.url=https://accelevents.integration-configuration.com
tray.io.source.marketo.workflow=${/app/prod/credential/tray_io_source_marketo_workflow}
tray.io.source.hubspot.workflow=${/app/prod/credential/tray_io_source_hubspot_workflow}
tray.io.source.pardot.workflow=${/app/prod/credential/tray_io_source_pardot_workflow}
tray.io.source.salesforce.workflow=${/app/prod/credential/tray_io_source_salesforce_workflow}
tray.io.source.hubspot.mapping.workflow=${/app/prod/credential/tray_io_mapping_hubspot_workflow}
tray.io.source.salesforce.mapping.workflow=${/app/prod/credential/tray_io_mapping_salesforce_workflow}
tray.io.source.marketo.mapping.workflow=${/app/prod/credential/tray_io_mapping_marketo_workflow}
tray.io.source.pardot.mapping.workflow=${/app/prod/credential/tray_io_mapping_pardot_workflow}
tray.io.source.marketo.tracking.workflow=${/app/prod/credential/tray_io_tracking_marketo_workflow}
tray.io.source.hubspot.tracking.workflow=${/app/prod/credential/tray_io_tracking_hubspot_workflow}
tray.io.source.salesforce.tracking.workflow=${/app/prod/credential/tray_io_tracking_salesforce_workflow}
tray.io.source.salesforce.campaign.recordType.workflow=${/app/prod/credential/tray_io_salesforce_campaign_record_type_workflow}
tray.io.source.salesforce.custom.workflow=${/app/prod/credential/tray_io_source_custom_salesforce_workflow}
tray.io.source.salesforce.custom.source.whitelable.id=${/app/prod/credential/tray_io_source_custom_source_whitelable_id}
tray.io.source.salesforce.custom.source.organizer.id=${/app/prod/credential/tray_io_source_custom_source_organizer_id}
tray.io.source.hubspot.discount.criteria.matching.workflow=************************************
tray.io.source.salesforce.discount.criteria.matching.workflow=ccfbb1c3-a261-4972-b413-dc82347e17a4
tray.io.source.hubspot.retrieve.contact.workflow=${/app/prod/credential/tray_io_fetch_hubspot_contact_property_workflow}
tray.io.source.googlesheet.workflow=${/app/prod/credential/tray_io_source_googlesheet_workflow}
tray.io.source.googlesheet.mapping.workflow=${/app/prod/credential/tray_io_mapping_googlesheet_workflow}

kinesis.stream.name=ae-prod-gamification-stream

dynamodb.user.activity.table=ae-prod-userActivity
dynamodb.video-analytics.processed.table=${/app/prod/credential/video_analytics_processed_dynamodb_table_name}
dynamodb.video-analytics.processed.user-watch-time.table=${/app/prod/credential/video_analytics_processed_user_watch_time_dynamodb_table_name}
dynamodb.meeting-analytics.processed.table=${/app/prod/credential/meeting_analytics_processed_data_dynamodb_table_name}
dynamodb.mobile-analytics.processed.table=${/app/prod/credential/mobile_analytics_processed_data_dynamodb_table_name}

#reset event id
resetEvent.eventIds= ${/app/prod/credential/reset_event_ids}

#reset event under organiser id
resetEvent.organiserId= ${/app/prod/credential/reset_event_organiser_id}

#expo count
aws.api.expo.count.websocket.url=${/app/prod/credential/aws_apigateway_expo_websocket_url}

#linkedin Credentials
linkedinClientId=${/app/prod/credential/linkedIn_clientId}
linkedinClientSecret=${/app/prod/credential/linkedIn_clientSecret}

#AppleLogin Credentials
applePrivateKey=${/app/prod/credential/apple_authKeyP8}
appleClientId=${/app/prod/credential/apple_clientId}
appleKeyId=${/app/prod/credential/apple_keyId}
appleTeamId=${/app/prod/credential/apple_teamId}

custom.css.s3.bucket=${/app/prod/credential/custom_css_s3_bucket}
custom.css.distribution=${/app/prod/credential/custom_css_cloudfront_distribution_id}

mux.asset.glacier.vault.name=ae-prod-mux-live-stream-assets
#Multipart size(byte) for upload archive in to glacier // 4MB
mux.asset.glacier.multipart.upload.size=4194304
mux.asset.glacier.sns.topic.arn=${/app/prod/credential/mux_live_stream_archive_sns_topic_arn}
mux.to.s3.trigger.apigw.url=${/app/prod/credential/aws_mux_to_s3_trigger_apigw_url}

api.rate.limit.per.minute=${/app/prod/credential/api_rate_limit_count}


beefreeClientId=${/app/prod/credential/beefree_clientId}
beefreeClientSecret=${/app/prod/credential/beefree_clientSecret}

beePageBuilderClientId=${/app/prod/credential/beefree_pageBuilder_clientId}
beePageBuilderSecret=${/app/prod/credential/beefree_pageBuilder_clientSecret}
beefree.api.token=${/app/prod/credential/beefree_api_token}
beefree.thumbnail.bucket=${/app/prod/credential/beefree_thumbnail_bucket}

slack.check.in.error.webhook=*******************************************************************************
cloudinary.url=https://res.cloudinary.com/accelevents/image/fetch/
cadmium.integration.lambda.name=ae-prod-cadmium-integration

badge.pdf.lambda.name=ae-prod-badge-pdf-generator
badge.pdf.lambda.api.token=${/app/prod/credential/badge_pdf_token}

server.servlet.session.cookie.secure=true

spreedly.host.url=https://core.spreedly.com/v1/
spreedly.environment.key=${/app/prod/credential/spreedly_environment_key}
spreedly.api.key=${/app/prod/credential/spreedly_api_key}
spreedly.private.key=spreedly/private_key_PROD_ENV.pem
spreedly.certificate.token=0YK737MWGF8CA8YD10T8BJEEV9

recaptcha.enabled=${/app/prod/credential/recaptcha_enabled}

cvent.custom.integration.wl.ids=${/app/prod/credential/cvent_custom_integration_wl_id}