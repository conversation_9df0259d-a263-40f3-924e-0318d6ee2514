
stripe.texttogive.product=xxxxxxx
#Security
security.ignored=/**
security.basic.enabled=false

#logging
logging.level.root=WARN

stripe.webhook.signing.secret=whsec_U3NeklxHaaYL07eI86MKFLaRnyBIZEvO

#H2 Database Details
spring.h2.console.enabled=true
spring.h2.console.path=/console
spring.datasource.platform=h2
#jdbc:h2:mem:testdb
spring.datasource.initialize=true
spring.jpa.hibernate.ddl-auto=create-drop
spring.datasource.url=jdbc:h2:~/test2522;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER

#Event ids which are used holder and profile sync process - ALL-IN summit event
sync.profile.eventids = 2361

#This domain's email required okta authentication for particular whitelabel's events
okta.authentication.required.domain=gmail.com
okta.authentication.required.whitelabelid=1

#File Upload
# Set the file size limit (default 1Mb). If you want to specify that files be unlimited set the multipart.maxFileSize property to -1.
#Here set 100MB for file upload limit but in code we have set it to 10MB only. Here 100MB because when we set it here 10MB only and host upload the 11MB file then its throws un-handle exception
spring.servlet.multipart.max-file-size=200MB

# Set the total request size for a multipart/form-data (default 10Mb)
spring.servlet.multipart.max-request-size=200MB

#Sendgrid
spring.sendgrid.api-key=*********************************************************************

spring.jackson.serialization.write-dates-as-timestamps=true

#Application PORT
server.port = 9099
server.error.include-message=always

#Default Phonenumber
default.phoneNumber=4152344879

#Application Base URL
uiBaseurl =https://www.stagingaccel.com
apiBaseUrl =http://api.stagingaccel.com


#square configuration
square.AUTHORIZE_URI=https://connect.squareup.com/oauth2/authorize
square.TOKEN_URI=https://connect.squareup.com/oauth2/token
square.CLIENT_URI=https://connect.squareup.com/oauth2/clients
square.mobile.AUTHORIZE=https://connect.squareup.com/mobile/authorization-code

#TestApplicationLive App credentials
#square.clientId=*****************************
#square.appSecretKey=sq0csp-aTtUwI4AYsmELLnLNG1RyT4SeJ3su_3upt5j_3kBF2Q

#AccelEventTestApp
#Sandbox Access Token
square.clientId=sandbox-sq0idp-A57Oi3xFvkk3-3fNftKu6w
#Sandbox Access Token
square.appSecretKey=sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w
square.sandbox.mode=true
#Coffee & Toffee SF	1455 Market Street
square.connect.ae.locationId=CBASEGMgZfZldaslqzMCk33YnHggAQ



#S3 Bucket
# you can get into iam page [https://console.aws.amazon.com/iam/home]
cloud.aws.credentials.accessKey=********************
cloud.aws.credentials.secretKey=J1JUNFVdJdplr+7tf/6fLFrd2CjvzGW+6YgKLuBp

# example : s3-bucket-test-1
cloud.aws.s3.bucket=v2-dev-images-public
cloud.aws.s3.bucket.ticketBuyerUploads=ticket_buyer_uploads
cloud.aws.s3.bucket.ticketBuyerUploads.url=https://s3.amazonaws.com/v2-dev-images-public/ticket_buyer_uploads/

#SQS Queue
aws.queueName=EmailMessageQueue

#AWS APIGATEWAY
aws.api.region=us-east-1
aws.api.serviceName=execute-api
aws.api.baseUrl=https://te051bsnf9.execute-api.us-east-1.amazonaws.com/AEMatch/@connections/
aws.apigateway.rest.baseurl=http://localhost:9090


#handle error for spring boot
server.error.whitelabel.enabled=false

stripe.AUTHORIZE_URI=https://connect.stripe.com/oauth/authorize
stripe.TOKEN_URI=https://connect.stripe.com/oauth/token
stripe.clientId=ca_9L9axDKRKxjbpOrqFhxLbh7y0gTfayaX
stripe.apiKey=sk_test_VdWjJ3utvEVEMNvIojtMYMWn
stripe.publicKey=pk_test_GHXcXTCaR8n2BoabLKblDpXY
stripe.auction.product=prod_Cen5qwPvDrUYv9
stripe.raffle.product=prod_Cen5RURzoolzB6
stripe.fund_a_need.product=prod_Cen5hDjNygMuzg
stripe.fixedCharge = 0.30
stripe.percentCharge = 2.9
stripe.virtual.product=prod_H9eZE8kolmGUc0
stripe.virtual.exhibitorPro=prod_Hh3PjRLwITikxV

#Twilio
twilio.ACCOUNT_SID=**********************************
twilio.AUTH_TOKEN=eb586a8cbd798fa78c9deb6f8e7d3d94
twilio.MESSAGE_SERVICE_SID=

#Email links
default.getStarted=https://www.accelevents.com/u/signup
default.facebookShare=https://www.facebook.com/accelevents
default.twitterShare=https://twitter.com/accelevents
default.instagramShare=https://www.instagram.com/accelevents
default.linkedInShare=https://www.linkedin.com/company/accelevents

#Default Images
images.blacklogo = Accelevents_Default_Event_Logo.jpg
images.accelevents.default.blacklogo = default_ae_images/Smooth_AE_Icon_700x350.png
images.whitelogo = 6bafabd0-5f33-4dcc-a95c-602babb11761accelevents-logo-white.png
images.defaultitem = eee2f81b-92c8-4826-92b6-68a64fb696b7A_600x600.jpg
images.defaultheader = newheaderlogo.JPG
images.defaultBanner = default_banner_image.jpg
color.default.displayBackgroundColor= #406AE8
color.default.displayTextColor= #000000
color.default.displayTabsTextColor=#000000
images.defaultBanner_theme_A = default_ae_banner_theme_A.jpg
images.defaultBanner_theme_B = default_ae_banner_theme_B.jpg
images.defaultBanner_theme_C = default_ae_banner_theme_C.jpg
color.default.displayBackgroundColor_theme_A_and_B= #FFFFFF
color.default.displayBackgroundColor_theme_C= #F7F7FA
color.default.displayTextColor_theme_A= #FFFFFF
color.default.displayTextColor_theme_B_and_C= #1E2137
color.default.displayTabsColor= #406AE8
color.default.displayTabsTextColor_theme_A_and_B=#4B4D5F
color.default.displayTabsTextColor_theme_C=#6D6F7D
images.acceleventlogo = accelevent-logo.png

image.prefix = http://v2-dev-images-public.s3-website-us-east-1.amazonaws.com/
image.bucketUrl = https://v2-dev-images-public.s3.amazonaws.com/

kiosk_default_primary_color=#406AE8
kiosk_default_secondary_color=#030134
kiosk_default_background_image=kiosk_home_background_image.png

#Cloudinary configuration
cloudinary.cloud.name=aestage

#Google API Key For URL Shortner
google.shortnerApiKey=AIzaSyDW-Hz1HHbjlZKwHSsRC8i_sgzQpZFZ9g0
rebrandly.shortnerApiKey=********************************

#Google API Key For other Services
google.apiKey=AIzaSyCTdjRtF5L54QIJdEQ8DyXlf2umq6MpvEw

#Google app
google.api.token.url=https://www.googleapis.com/oauth2/v4/token
google.client.id=479283504567-dhr5pk76mh6rtft0oh0b72jtu0rlk53u.apps.googleusercontent.com
google.client.secret=43wVPh35oQqMAFV0wZc6CxAE
google.oauth2.accessToken=*********************************************************************************************************************************
google.oauth2.refreshToken=1/w560dGETV8z3tvfYNwmdKxiYOg0AkzgdkpVmLRVSooI
google.analytics.view.id=182825504

#Intercom APP ID
intercom.app_id=ltwflky8
intercom.token=************************************************************

spring.session.store-type= hash-map
use.redis.session.store=false
security.sessions=always



#Sentry
sentry.dsn=
application.environment=test

#Seats.io
seats.io.secretKey=2a44d4e8-24f6-4258-9b30-6fdf3060f2a4
seats.io.workspaceKey=42f18566-26e3-46ce-9ecf-2fb729ef18db


#Neon
neon.apiUrl=https://trial.z2systems.com/neonws/services/api

#Cloudinary
cloudinary.API_KEY=631279911478654
cloudinary.API_SECRET=TLsY9m3i5BnZkkeOz-OsEIgvUJw
cloudinary.CLOUD_NAME=aestage
cloudinary.UPLOAD_FOLDER_PATH=uploaded-assets


error.square.email=<EMAIL>
payout.email.receivers=<EMAIL>

#neptune.url=brilneptunedemocluster.cluster-cvzj4sy82zdd.us-east-1.neptune.amazonaws.com


neptune.url=ae-dev-neptune-cluster.cluster-cv6wel746ups.us-east-1.neptune.amazonaws.com
neptune.port=8182
neptune.enableSsl=true
neptune.clusterId=ae-dev-neptune-cluster
neptune.keyStore.type=PKCS12
neptune.keyStore.resourcePath=keystore/neptune-local.pfx
neptune.keyStore.password=AccelLocal


zoom.secretKey=bdTIkM8kNIwr66VuNWpE9Il4LmMz0gxiotmS
zoom.apiKey=f90q4zSDSc2ggr9sW47zqA


#Spring
#RDS Local mysql Database Configuration
spring.datasource.testWhileIdle = true
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true

spring.jpa.hibernate.generate_statistics=true
spring.jpa.show-sql=false
spring.jpa.open-in-view=false

app.profile=local

management.endpoints.web.exposure.include=health,info,metrics
logging.level.com.zaxxer.hikari=debug

spring.datasource.hikari.leak-detection-threshold=10000
spring.datasource.hikari.maximumPoolSize=10

#File Upload
# Set the file size limit (default 1Mb). If you want to specify that files be unlimited set the multipart.maxFileSize property to -1.
#Here set 300MB for file upload limit but in code we have set it to 200MB only. Here 300MB because when we set it here 200MB only and host upload the 201MB file then its throws un-handle exception

# Set the total request size for a multipart/form-data (default 10Mb)

#Spring Redis
#spring.redis.host=127.0.0.1
#spring.redis.port=6379

spring.session.maxInactive=always
#use.redis.session.store=true
#spring.session.maxInactive=300

spring.cache.type=SIMPLE

#Sendgrid


#handle error for spring boot


#Mux Application Domain Validation
domainName =www.devaccel.com
mux.private.key=mux/private_key_DEV_ENV.pem
signing_key=MHBo1xZGOOPiGQ2xcp021N9sUVy3q5TDPg5CwJMLq02BU
mux.data.private.key=mux/private_key_DATA_DEV_ENV.pem
data_signing_key=Wh02tjCUmhEIDhj8ni02vGXHnXRQyHx00ISVgwcMIGMIEA
playback_restriction_id=6kOtScYqO02aLncfKrIz5bZuU01wsxBX9D3fve6zpXIGc

#Application Base URL

#Default Phonenumber

#Email links

#Default Images
images.default.defaultRegistrationIntroBanner = default_ae_images/default_registration_banner_image.png
image.url =https://v2-dev-images-public.s3.amazonaws.com/
imageLogo.url=https://accelevents-dev-public-images.s3.amazonaws.com/email_template_images

#Cloudinary configuration

#firebase configuration
app.firebase-config= google/accelevents-aae51-firebase-adminsdk-ubsrc-c2e989ae29.json
aws.credentials.stageFromProd.secretKey=

#S3 Bucket
# example : s3-bucket-test-1
cloud.aws.s3.bucket.mux.assets=ae-dev-mux-assets
cloud.aws.cloudFront.mux.assets.url=https://d2ar4dy44c0pjp.cloudfront.net/

#SQS Queue

#AWS APIGATEWAY
aws.api.expo.count.websocket.url=https://3qzs6ogh03.execute-api.us-east-1.amazonaws.com/dev/@connections/

#stripe configuration

#paypal configuration
paypal.AUTHORIZE_URI=https://www.sandbox.paypal.com/signin/authorize
paypal.BASE_URI=https://api-m.sandbox.paypal.com
paypal.TOKEN_URI=https://api-m.sandbox.paypal.com/v1/oauth2/token
paypal.clientId=AcCwK2Dq5OitghTZVki6lmRIysd_kT9G36bo9MWbwdIyyQXpZ4y9LUXg8So09eGWq8wZ-IktyAAj8UZ-
paypal.apiKey=ECMUibgqXvWYYLCAgdR2ZaD1SLpDJk90o4Pf5apJwthzgOLpYOWLrv8UTUyBRhkaXuxG1RFC5wbiGKxA
paypal.redirectURI=http://localhost:3000/host/settings/credit-card
paypal.merchantId=6ND9Q4GYMGPAC

#payflow configuration
payflow.BASE_URI=https://pilot-payflowpro.paypal.com

#chargebee configuration
chargebee.apiKey=test_SOMuNJcXBocu1Ij5o4wz5cushw3Lnpuv0p
chargebee.site=accelevents-test
chargebee.annual.item.price.id=annual-usages-addon-stage-usd-yearly
chargebee.post.event.access.item.price.id=monthly-post-event-access-usage-addon-usd-monthly
chargebee.monthly.plan.id=Monthly-Overage-Usages-USD-Monthly
chargebee.text_to_give_plan.id=Text-to-Give---Production-USD-Monthly

#square configuration
square.LOCATION_URI=https://connect.squareupsandbox.com/v2/locations

#TestApplicationLive App credentials
#square.clientId=*****************************
#square.appSecretKey=sq0csp-aTtUwI4AYsmELLnLNG1RyT4SeJ3su_3upt5j_3kBF2Q

#AccelEventTestApp
#Sandbox Access Token
#Sandbox Access Token
#Coffee & Toffee SF	1455 Market Street

#Twilio
twilio.api.key=********************************************************************************************

#Vonage
vonageApi.key=47140994
vonageApi.secret=eeff6b1c7b48fc0f1367063b05270e3aa76ff2d0

#Google API Key For URL Shortner
shortio.apikey=sk_4reEJlBjDDymVryP

#Google API Key For other Services

#Google app


#Intercom APP ID


#Sentry

#Seats.io


#Neon
#PipeDrive
pipedrive.apiUrl=https://accelevents-sandbox-3099ae.pipedrive.com/v1
pipedrive.apiToken=****************************************
#PipeDriveField
pipedrive.field.eventurl=****************************************
pipedrive.field.eventId=****************************************
pipedrive.field.ticketSalesStartDate=****************************************
pipedrive.field.eventDate=****************************************
pipedrive.field.eventTime=****************************************
pipedrive.field.totalTicketSales=****************************************
pipedrive.field.totalAEFees=****************************************
pipedrive.field.potentialTicketSales=****************************************
pipedrive.field.potentialAEFees=****************************************
pipedrive.field.dateFirstTicketSold=****************************************
pipedrive.field.assignedSeatingEnabled=****************************************
pipedrive.field.assignedSeatingEnabled.option.true=9
pipedrive.field.assignedSeatingEnabled.option.false=10
pipedrive.field.isrecurringevent=****************************************
pipedrive.field.isrecurringevent.option.true=11
pipedrive.field.isrecurringevent.option.false=12

getStream.secretKey=49q2jkzhy98h6apacadre8bc2jzvmpgnby46cxjpvrerhddr87zgz9r3nxd7hc9j
getStream.apiKey=4ezv3ck6yrn6
mux.secretKey=pwdZGho0Kg1956HyiElmRi/74YIa0E3U3XomyLPXG87fOPtB7/1CYjX6QsE9i8dnsciYJ90CuUb
mux.apiToken=************************************
voxeet.consumerSecret=tSYEx5-cJEtPzMcXk0oJOYZBFcvi_9EbilzBXYowFGk=
voxeet.consumerKey=vVEgreRNIZksBQmMMMD8zw==

logging.level.org.hibernate.SQL=ERROR
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=ERROR

appsync.session.broadcast.api.url=https://session.devaccel.com/graphql
appsync.qa.api.url=https://qa.devaccel.com/graphql
appsync.polls.api.url=https://polls.devaccel.com/graphql
appsync.activityLog.api.url=https://activitylogs.devaccel.com/graphql
appsync.pushNotification.api.url=https://event-notification.devaccel.com/graphql
appsync.attendeePushNotification.api.url=https://attendee.devaccel.com/graphql
appsync.commandCenter.api.url=https://command-center.devaccel.com/graphql
appsync.chime.meeting.api.url=https://chime.devaccel.com/graphql
#Appsync url for Item Out bid
appsync.fundRaisingStatus.api.url=https://fundraising.devaccel.com/graphql
#Appsync url for Notification
appsync.async.api.url=https://async-api.devaccel.com/graphql
#Appsync url for Event Live Stream
appsync.broadcast.eventstream.api.url=https://stream.devaccel.com/graphql
#Appsync url for Chime meeting recording status
appsync.chime.recording.asset.api.url=https://chimers.devaccel.com/graphql

chime.region=us-east-1
chime.queue.name=ae-dev-chime-events
chime.sqs.arn=arn:aws:sqs:us-east-1:147478257448:ae-dev-chime-events
chime.recording.bucket.sinkArn=arn:aws:s3:::ae-dev-chime-workshop-session-recording
chime.recording.meeting.sourceArn=arn:aws:chime::147478257448:meeting
cloud.aws.s3.bucket.workshopRecording=ae-dev-chime-workshop-session-recording
chime.meeting.recording.lambda.name=ae-dev-chime-combine-share-screen-chunk

gamification.elasticsearch.host = localhost
gamification.elasticsearch.port = 9200
gamification.elasticsearch.scheme = http

zoom.sdkKey=d0dgx29KiyNVSO71OMA11y1SiRgZ5YlHFYpZ

memcached.expiration.sec=300
memcached.addresses=127.0.0.1:11211

youtube.api.url=https://youtube.googleapis.com/youtube/v3/videos
youtube.api.key=AIzaSyCjTyK-IrliJseLHRXF7m27xa6nUD5_nRk

vimeo.api.url=https://api.vimeo.com/videos
vimeo.api.token=********************************

hubspot.url=https://api.hubapi.com/crm/v3
hubspot.url.v4=https://api.hubapi.com/crm/v4
hubspot.api.key=********************************************
hubspot.event.object.name=p_AEevents
hubspot.event.url=https://api.hubspot.com/events/v3
hubspot.event.objectTypeId=2-1309213
hubspot.organizer.object.name=p_Organizer
hubspot.custom.event.name=pe9229002_virtual_event_hub___sales_info_request_new
hubSpot.custom.event.eventCreated.name=pe9229002_event_created
hubSpot.custom.event.addedUser.name=pe9229002_added_user
hubSpot.custom.event.integration.name=pe9229002_integration
hubSpot.custom.event.logIn.name=pe9229002_log_in
hubSpot.custom.event.organizerCreated.name=pe9229002_organizer_created
hubspot.custom.event.viewedFeatureUpgrade.name=pe9229002_viewed_feature_upgrade_v2
hubspot.custom.event.requestedFeatureUpgrade.name=pe9229002_requested_feature_upgrade

recordingViewStartDate=2021/01/22 00:00

agora.appId=********************************
agora.appCertificate=********************************

#tray.io
tray.io.master.token=da3f876aa03840dabb2a83cf1a2247222530782bb7564b13a4a882bb096a7504
tray.io.graphql.url=https://tray.io/graphql
tray.io.embedded.url=https://stageaccel.integration-configuration.com
tray.io.source.marketo.workflow=f93e1485-34a0-4360-91f3-a5f04b00333d
tray.io.source.hubspot.workflow=************************************
tray.io.source.salesforce.workflow=a0519273-9547-49eb-b8d5-75d4c0a961e0
tray.io.source.pardot.workflow=aded9946-6690-4c93-88dc-94f0caf77bfc
tray.io.source.hubspot.mapping.workflow=************************************
tray.io.source.salesforce.mapping.workflow=8d961ec9-cfad-44ae-824a-e549102cd558
tray.io.source.marketo.mapping.workflow=1f57c2fc-ee63-4899-9928-e64a8fb678f8
tray.io.source.pardot.mapping.workflow=3d4c919b-d735-4e55-83e8-28dcf9dbee67
tray.io.source.marketo.tracking.workflow=1870b725-8294-4e52-9a06-db7e169a42e6
tray.io.source.hubspot.tracking.workflow=************************************
tray.io.source.salesforce.tracking.workflow=ed7394a8-8bd1-4592-b0f5-d20712935b37
tray.io.source.salesforce.campaign.recordType.workflow=3ab9f056-b1b0-431d-b811-275f9de31402
tray.io.source.salesforce.custom.workflow=c48e3bac-630c-4992-b5fb-8efd3db56f1b
tray.io.source.salesforce.custom.source.whitelable.id=
tray.io.source.salesforce.custom.source.organizer.id=509
tray.io.source.hubspot.discount.criteria.matching.workflow=************************************
tray.io.source.salesforce.discount.criteria.matching.workflow=930e5507-2628-4700-916f-ea7226af051b
tray.io.source.hubspot.retrieve.contact.workflow=************************************
tray.io.source.googlesheet.workflow=da8268d1-ca23-4031-837f-f959afa46f6b
tray.io.source.googlesheet.mapping.workflow=b75ec265-ab3f-486f-9138-c080d4bea9aa

#expo count async task executor
expo.count.executor.core.pool.size=1
expo.count.executor.max.pool.size=5
expo.count.executor.queue.capacity=50

aws.lambda.download.csv.name=ae-dev-download-csv

kinesis.stream.name=ae-dev-gamification-stream

dynamodb.user.activity.table=UserActivity

resetEvent.eventIds= 1983

#reset event under organiser id
resetEvent.organiserId= 9245

#linkedin Credentials
linkedinClientId=78bx6ui9ehccq7
linkedinClientSecret=6p3bo9zgDh7wgeLc

applePrivateKey=privatekey
appleClientId=appleClientId
appleKeyId=applekey
appleTeamId=applteamId

custom.css.s3.bucket=ae-dev-custom-css
custom.css.distribution=

mux.asset.glacier.vault.name=ae-dev-mux-live-stream-assets
#Multipart size(byte) for upload archive in to glacier // 4MB
mux.asset.glacier.multipart.upload.size=4194304
mux.asset.glacier.sns.topic.arn=arn:aws:sns:us-east-1:147478257448:ae-dev-mux-asset-archive-retrival
mux.to.s3.trigger.apigw.url=https://oyyhi9dloj.execute-api.us-east-1.amazonaws.com/un

api.rate.limit.per.minute=100

beefreeClientId=246778b9-51de-438d-8755-ab4b46f3af27
beefreeClientSecret=nMHCqRxP3gLmUHCx5snzjbm31B3g9CRZZpuForPVH7DGVya8YXUd

beePageBuilderClientId=4b952309-2cbd-4e2f-952c-5fe75c45b3b8
beePageBuilderSecret=vMGZwrWScoZEcp8e6d8Geh2Udc7Kkahec1PDoWUANEAo3I0TAApm
beefree.api.token=ee686fdca0c4230d8ae61d62a6bb6e0753951be895e565c1f5185600d8915443
beefree.thumbnail.bucket=accelevents-beefree-dev

slack.check.in.error.webhook=*******************************************************************************
cloudinary.url=https://res.cloudinary.com/aestage/image/fetch/

cadmium.integration.lambda.name=ae-dev-cadmium-integration

badge.pdf.lambda.name=ae-dev-badge-pdf-generator
badge.pdf.lambda.api.token=3e4fe4c7-3a36-4712-8003-cad668496075

server.servlet.session.cookie.secure=true

spreedly.host.url=https://core.spreedly.com/v1/
spreedly.environment.key=0HRQ2KWV5B9EN8625JYXFRSSJW
spreedly.api.key=vLtjYqbp3snsFyf3MNCTVN0ZApZ2C326B1Fr2rbJhuONCnC1I6M4XcJiljipzHV4a

recaptcha.enabled=false

cvent.custom.integration.wl.ids=${/app/dev/credential/cvent_custom_integration_wl_id}