awsParameterStorePropertySource.enabled=true
#Spring
#RDS Local mysql Database Configuration
spring.datasource.url=*******************************************************************************************************************************************************************************************************
spring.datasource.username=${/app/dev/credential/mysql_username}
spring.datasource.password=${/app/dev/credential/mysql_password}
spring.datasource.testWhileIdle = true
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true


spring.jpa.hibernate.ddl-auto=validate
spring.jpa.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.query.in_clause_parameter_padding=true
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.open-in-view=false
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER

app.profile=dev

#This domain's email required okta authentication for particular whitelabel's events
okta.authentication.required.domain=yopmail.com
okta.authentication.required.whitelabelid=815

management.endpoints.web.exposure.include=health,info,metrics
logging.level.com.zaxxer.hikari=debug
logging.pattern.level=%5p %mdc

spring.datasource.hikari.leak-detection-threshold=15000
spring.datasource.hikari.maximumPoolSize=20

#File Upload
# Set the file size limit (default 1Mb). If you want to specify that files be unlimited set the multipart.maxFileSize property to -1.
#Here set 300MB for file upload limit but in code we have set it to 200MB only. Here 300MB because when we set it here 200MB only and host upload the 201MB file then its throws un-handle exception
spring.servlet.multipart.max-file-size=200MB

# Set the total request size for a multipart/form-data (default 10Mb)
spring.servlet.multipart.max-request-size=200MB

#Spring Redis
#use.redis.session.store=false
#spring.session.maxInactive=always
use.redis.session.store=true
spring.session.maxInactive=300
#spring.session.store-type= hash-map

redis.gamification.host=${/app/dev/credential/aws_redis_gamification_host}
redis.host=${/app/dev/credential/aws_redis_host}
redis.maxTotal=${/app/dev/credential/aws_redis_max_total}
redis.minIdle=${/app/dev/credential/aws_redis_min_idle}
redis.maxIdle=${/app/dev/credential/aws_redis_max_idle}

#spring.cache.type=SIMPLE

#Sendgrid
spring.sendgrid.api-key=${/app/dev/credential/sendgrid_api_key}

spring.jackson.serialization.write-dates-as-timestamps=true

#handle error for spring boot
server.error.whitelabel.enabled=false
server.error.include-message=always

#Event ids which are used holder and profile sync process - ALL-IN summit event
sync.profile.eventids = 33300


#Application PORT
server.port=5000

#Mux Application Domain Validation
domainName =www.devaccel.com
mux.private.key=mux/private_key_DEV_ENV.pem
signing_key=MHBo1xZGOOPiGQ2xcp021N9sUVy3q5TDPg5CwJMLq02BU
mux.data.private.key=mux/private_key_DATA_DEV_ENV.pem
data_signing_key=Wh02tjCUmhEIDhj8ni02vGXHnXRQyHx00ISVgwcMIGMIEA
playback_restriction_id=6kOtScYqO02aLncfKrIz5bZuU01wsxBX9D3fve6zpXIGc

#Application Base URL
uiBaseurl =https://www.devaccel.com
apiBaseUrl =https://api.devaccel.com

#Default Phonenumber
default.phoneNumber=4152344879

#Email links
default.getStarted=https://www.devaccel.com/u/signup
default.facebookShare=https://www.facebook.com/accelevents
default.twitterShare=https://twitter.com/accelevents
default.instagramShare=https://www.instagram.com/accelevents
default.linkedInShare=https://www.linkedin.com/company/accelevents

#Default Images
images.blacklogo = default_ae_images/Ae_Icn_700x350.png
images.accelevents.default.blacklogo = default_ae_images/Smooth_AE_Icon_700x350.png
images.whitelogo = default_ae_images/Smooth_Accelevents_Default_Event_Logo_White.svg
images.defaultitem = default_ae_images/Smooth_Accelevents_dark_fill_icon.png
images.defaultBanner = default_banner_image.jpg
images.default.defaultRegistrationIntroBanner = default_ae_images/default_registration_banner_image.png
color.default.displayBackgroundColor= #406AE8
color.default.displayTextColor= #000000
color.default.displayTabsTextColor=#000000
images.defaultBanner_theme_A = default_ae_banner_theme_A.jpg
images.defaultBanner_theme_B = default_ae_banner_theme_B.jpg
images.defaultBanner_theme_C = default_ae_banner_theme_C.jpg
color.default.displayBackgroundColor_theme_A_and_B= #FFFFFF
color.default.displayBackgroundColor_theme_C= #F7F7FA
color.default.displayTextColor_theme_A= #FFFFFF
color.default.displayTextColor_theme_B_and_C= #1E2137
color.default.displayTabsColor= #406AE8
color.default.displayTabsTextColor_theme_A_and_B=#4B4D5F
color.default.displayTabsTextColor_theme_C=#6D6F7D
images.acceleventlogo = Smooth_Accelevents_Default_Event_Logo_Black.png
image.url=https://d1jq2jmhdt3fy.cloudfront.net/
imageLogo.url=https://d1jq2jmhdt3fy.cloudfront.net/email_template_images
image.prefix = https://d1jq2jmhdt3fy.cloudfront.net/
image.bucketUrl = https://accelevents-dev-public-images.s3.amazonaws.com/

kiosk_default_primary_color=#406AE8
kiosk_default_secondary_color=#030134
kiosk_default_background_image=kiosk_home_background_image.png

#Cloudinary configuration
cloudinary.cloud.name=devaccel

#firebase configuration
app.firebase-config= google/accelevents-1482262106272-firebase-adminsdk-20w9n-1f3f813e31.json

#AWS
#Credential: you can get into iam page [https://console.aws.amazon.com/iam/home]
# do not change below credential, because We are using DefaultAWSCredentialsProviderChain in DEV,STAGE,PROD environment, and we use accessKey and secretKey in local environment
cloud.aws.credentials.accessKey="DefaultAWSCredentialsProviderChain"
cloud.aws.credentials.secretKey="DefaultAWSCredentialsProviderChain"
cw.temp.aws.key=${/app/dev/credential/cw_temp_aws_key}
cw.temp.aws.id=${/app/dev/credential/cw_temp_aws_id}
temp.google.map.key=${/app/dev/credential/temp_google_map_key}
temp.google.font.key=${/app/dev/credential/temp_google_font_key}

#S3 Bucket
# example : s3-bucket-test-1
cloud.aws.s3.bucket=accelevents-dev-public-images
cloud.aws.s3.bucket.ticketBuyerUploads=ticket_buyer_uploads
cloud.aws.s3.bucket.ticketBuyerUploads.url=https://s3.amazonaws.com/accelevents-dev-public-images/ticket_buyer_uploads/
cloud.aws.s3.bucket.mux.assets=${/app/dev/credential/aws_mux_asset_s3_bucket}
cloud.aws.cloudFront.mux.assets.url=https://d2ar4dy44c0pjp.cloudfront.net/

#SQS Queue
aws.queueName=EmailMessageQueue

#AWS APIGATEWAY
aws.api.region=us-east-1
aws.api.serviceName=execute-api
aws.api.baseUrl=${/app/dev/credential/aws_apigateway_networking_websocket_url}
aws.apigateway.rest.baseurl=${/app/dev/credential/aws_apigateway_rest_url}
aws.api.expo.count.websocket.url=${/app/dev/credential/aws_apigateway_expo_websocket_url}

#stripe configuration
stripe.AUTHORIZE_URI=https://connect.stripe.com/oauth/authorize
stripe.TOKEN_URI=https://connect.stripe.com/oauth/token
stripe.clientId=${/app/dev/credential/stripe_clientid}
stripe.apiKey=${/app/dev/credential/stripe_api_key}
stripe.publicKey=${/app/dev/credential/stripe_public_key}
stripe.webhook.signing.secret=whsec_U3NeklxHaaYL07eI86MKFLaRnyBIZEvO
stripe.fixedCharge = 0.30
stripe.percentCharge = 2.9

stripe.auction.product=${/app/dev/credential/stripe_auction_product}
stripe.raffle.product=${/app/dev/credential/stripe_raffle_product}
stripe.fund_a_need.product=${/app/dev/credential/stripe_fund_a_need_product}
stripe.virtual.product=${/app/dev/credential/stripe_virtual_product}
stripe.texttogive.product=${/app/dev/credential/stripe_texttogive_product}
stripe.virtual.exhibitorPro=${/app/dev/credential/stripe_virtual_exhibitorPro_product}

#paypal configuration
paypal.AUTHORIZE_URI=https://www.sandbox.paypal.com/signin/authorize
paypal.BASE_URI=https://api-m.sandbox.paypal.com
paypal.TOKEN_URI=https://api-m.sandbox.paypal.com/v1/oauth2/token
paypal.clientId=AcCwK2Dq5OitghTZVki6lmRIysd_kT9G36bo9MWbwdIyyQXpZ4y9LUXg8So09eGWq8wZ-IktyAAj8UZ-
paypal.apiKey=ECMUibgqXvWYYLCAgdR2ZaD1SLpDJk90o4Pf5apJwthzgOLpYOWLrv8UTUyBRhkaXuxG1RFC5wbiGKxA
paypal.redirectURI=https://www.devaccel.com/host/settings/credit-card
paypal.merchantId=6ND9Q4GYMGPAC

#payflow configuration
payflow.BASE_URI=https://pilot-payflowpro.paypal.com

#chargebee configuration
chargebee.apiKey=${/app/dev/credential/chargebee_apiKey}
chargebee.site=${/app/dev/credential/chargebee_site}
chargebee.annual.item.price.id=annual-usages-addon-stage-usd-yearly
chargebee.post.event.access.item.price.id=monthly-post-event-access-usage-addon-usd-monthly
chargebee.monthly.plan.id=Monthly-Overage-Usages-USD-Monthly
chargebee.text_to_give_plan.id=Text-to-Give---Production-USD-Monthly

#square configuration
square.AUTHORIZE_URI=https://connect.squareupsandbox.com/oauth2/authorize
square.TOKEN_URI=https://connect.squareupsandbox.com/oauth2/token
square.CLIENT_URI=https://connect.squareupsandbox.com/oauth2/clients
square.mobile.AUTHORIZE=https://connect.squareupsandbox.com/mobile/authorization-code
square.LOCATION_URI=https://connect.squareupsandbox.com/v2/locations

#AccelEventTestApp
#Sandbox Access Token
square.clientId=${/app/dev/credential/square_clientId}
#Sandbox Access Token
square.appSecretKey=${/app/dev/credential/square_appSecretKey}
square.sandbox.mode=true
#Coffee & Toffee SF	1455 Market Street
square.connect.ae.locationId=${/app/dev/credential/square_connect_ae_locationId}


#Twilio
twilio.ACCOUNT_SID=${/app/dev/credential/twilio_ACCOUNT_SID}
twilio.AUTH_TOKEN=${/app/dev/credential/twilio_AUTH_TOKEN}
twilio.api.key=${/app/dev/credential/twilio_api_key}
twilio.MESSAGE_SERVICE_SID=${/app/dev/credential/twilio_MESSAGE_SERVICE_SID}

#Vonage
vonageApi.key=${/app/dev/credential/vonageApi_key}
vonageApi.secret=${/app/dev/credential/vonageApi_secret}

#Google API Key For URL Shortner
google.shortnerApiKey=${/app/dev/credential/google_shortnerApiKey}
rebrandly.shortnerApiKey=${/app/dev/credential/rebrandly_shortnerApiKey}
shortio.apikey=${/app/dev/credential/shortio_apikey}

#Google API Key For other Services
google.apiKey=${/app/dev/credential/google_apiKey}

#Google app
google.api.token.url=https://www.googleapis.com/oauth2/v4/token
google.client.id=${/app/dev/credential/google_client_id}
google.client.secret=${/app/dev/credential/google_client_secret}
google.oauth2.accessToken=${/app/dev/credential/google_oauth2_accessToken}
google.oauth2.refreshToken=${/app/dev/credential/google_oauth2_refreshToken}
google.analytics.view.id=${/app/dev/credential/google_analytics_view_id}


#Intercom APP ID
intercom.app_id=${/app/dev/credential/intercom_app_id}
intercom.token=${/app/dev/credential/intercom_token}


#Sentry
sentry.dsn=${/app/dev/credential/sentry_dsn}
sentry.traces-sample-rate=0.02
application.environment=dev

#Seats.io
seats.io.secretKey=${/app/dev/credential/seats_io_secretKey}
seats.io.workspaceKey=${/app/dev/credential/seats_io_WorkSpaceKey}


#Neon
neon.apiUrl=https://trial.z2systems.com/neonws/services/api

#Cloudinary
cloudinary.API_KEY=${/app/dev/credential/cloudinary_API_KEY}
cloudinary.API_SECRET=${/app/dev/credential/cloudinary_API_SECRET}
cloudinary.CLOUD_NAME=devaccel
cloudinary.UPLOAD_FOLDER_PATH=uploaded-assets

error.square.email=<EMAIL>

payout.email.receivers=<EMAIL>

getStream.secretKey=${/app/dev/credential/getStream_secretKey}
getStream.apiKey=${/app/dev/credential/getStream_apiKey}
mux.secretKey=${/app/dev/credential/mux_secretKey}
mux.apiToken=${/app/dev/credential/mux_apiToken}
voxeet.consumerSecret=${/app/dev/credential/voxeet_consumerSecret}
voxeet.consumerKey=${/app/dev/credential/voxeet_consumerKey}

logging.level.org.hibernate.SQL=ERROR
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=ERROR

appsync.session.broadcast.api.url=https://session.devaccel.com/graphql
appsync.qa.api.url=https://qa.devaccel.com/graphql
appsync.polls.api.url=https://polls.devaccel.com/graphql
appsync.activityLog.api.url=https://activitylogs.devaccel.com/graphql
appsync.pushNotification.api.url=https://event-notification.devaccel.com/graphql
appsync.attendeePushNotification.api.url=https://attendee.devaccel.com/graphql
appsync.attendeeDetails.api.url=https://attendee-details.devaccel.com/graphql
appsync.commandCenter.api.url=https://command-center.devaccel.com/graphql
appsync.chime.meeting.api.url=https://chime.devaccel.com/graphql
#Appsync url for Item Out bid
appsync.fundRaisingStatus.api.url=https://fundraising.devaccel.com/graphql
#Appsync url for Notification
appsync.async.api.url=https://async-api.devaccel.com/graphql
#Appsync url for Event Live Stream
appsync.broadcast.eventstream.api.url=https://stream.devaccel.com/graphql
#Appsync url for Chime meeting recording status
appsync.chime.recording.asset.api.url=https://chimers.devaccel.com/graphql

aws.lambda.download.csv.name=ae-dev-download-csv
aws.lambda.engage.email.count.name=ae-dev-engage-email-count
aws.sqs.generate.engage.email=ae-dev-generate-engage-email
aws.sqs.generate.reminder.email=ae-dev-generate-reminder-email

neptune.url=${/app/dev/credential/aws_neptune_url}
neptune.port=${/app/dev/credential/aws_neptune_port}
neptune.clusterId=${/app/dev/credential/aws_neptune_cluster_id}
neptune.enableSsl=true
neptune.keyStore.type=PKCS12
neptune.keyStore.resourcePath=${/app/dev/credential/aws_neptune_keystore_filename}
neptune.keyStore.password=${/app/dev/credential/aws_neptune_keystore_password}

chime.region=us-east-1
chime.queue.name=${/app/dev/credential/chime_sqs_name}
chime.sqs.arn=${/app/dev/credential/chime_sqs_arn}
chime.recording.bucket.sinkArn=${/app/dev/credential/chime_recording_s3_bucket_sink_arn}
chime.recording.meeting.sourceArn=${/app/dev/credential/chime_recording_meeting_source_arn}
cloud.aws.s3.bucket.workshopRecording=${/app/dev/credential/chime_s3_bucket_workshop_recordings}
chime.meeting.recording.lambda.name=ae-dev-chime-combine-share-screen-chunk

gamification.elasticsearch.host = ${/app/dev/credential/aws_elasticsearch_gamification_host}
gamification.elasticsearch.port = ${/app/dev/credential/aws_elasticsearch_gamification_port}
gamification.elasticsearch.scheme = https

zoom.secretKey=${/app/dev/credential/zoom_secretKey}
zoom.apiKey=${/app/dev/credential/zoom_apiKey}

memcached.expiration.sec=${/app/dev/credential/aws_memcached_expiration_sec}
memcached.addresses=${/app/dev/credential/aws_memcached_addresses}

youtube.api.url=https://youtube.googleapis.com/youtube/v3/videos
youtube.api.key=${/app/dev/credential/youtube_api_key}

vimeo.api.url=https://api.vimeo.com/videos
vimeo.api.token=${/app/dev/credential/vimeo_api_token}

hubspot.url=https://api.hubapi.com/crm/v3
hubspot.url.v4=https://api.hubapi.com/crm/v4
hubspot.api.key=${/app/dev/credential/hubspot_api_key}
hubspot.event.object.name=p_AEevents
hubspot.event.url=https://api.hubspot.com/events/v3
hubspot.event.objectTypeId=2-2252333
hubspot.organizer.object.name=p_Organizer
hubspot.custom.event.name=pe20197148_virtual_event_hub___sales_info_request
hubSpot.custom.event.eventCreated.name=pe20197148_event_created
hubSpot.custom.event.addedUser.name=pe20197148_added_user
hubSpot.custom.event.integration.name=pe20197148_integration
hubSpot.custom.event.logIn.name=pe20197148_log_in
hubSpot.custom.event.organizerCreated.name=pe20197148_organizer_created
hubspot.custom.event.viewedFeatureUpgrade.name=pe20197148_viewed_feature_upgrade
hubspot.custom.event.requestedFeatureUpgrade.name=pe20197148_requested_feature_upgrade

recordingViewStartDate=2021/01/22 00:00

agora.appId=${/app/dev/credential/agora_appId}
agora.appCertificate=${/app/dev/credential/agora_appCertificate}

#tray.io
tray.io.master.token=${/app/dev/credential/tray_io_master_token}
tray.io.graphql.url=https://tray.io/graphql
tray.io.embedded.url=https://stageaccel.integration-configuration.com
tray.io.source.marketo.workflow=${/app/dev/credential/tray_io_source_marketo_workflow}
tray.io.source.hubspot.workflow=${/app/dev/credential/tray_io_source_hubspot_workflow}
tray.io.source.salesforce.workflow=${/app/dev/credential/tray_io_source_salesforce_workflow}
tray.io.source.pardot.workflow=${/app/dev/credential/tray_io_source_pardot_workflow}
tray.io.source.hubspot.mapping.workflow=${/app/dev/credential/tray_io_mapping_hubspot_workflow}
tray.io.source.salesforce.mapping.workflow=${/app/dev/credential/tray_io_mapping_salesforce_workflow}
tray.io.source.marketo.mapping.workflow=${/app/dev/credential/tray_io_mapping_marketo_workflow}
tray.io.source.pardot.mapping.workflow=${/app/dev/credential/tray_io_mapping_pardot_workflow}
tray.io.source.marketo.tracking.workflow=${/app/dev/credential/tray_io_tracking_marketo_workflow}
tray.io.source.hubspot.tracking.workflow=${/app/dev/credential/tray_io_tracking_hubspot_workflow}
tray.io.source.salesforce.tracking.workflow=${/app/dev/credential/tray_io_tracking_salesforce_workflow}
tray.io.source.salesforce.campaign.recordType.workflow=${/app/dev/credential/tray_io_salesforce_campaign_record_type_workflow}
tray.io.source.salesforce.custom.workflow=${/app/dev/credential/tray_io_source_custom_salesforce_workflow}
tray.io.source.salesforce.custom.source.whitelable.id=${/app/dev/credential/tray_io_source_custom_source_whitelable_id}
tray.io.source.salesforce.custom.source.organizer.id=${/app/dev/credential/tray_io_source_custom_source_organizer_id}
tray.io.source.hubspot.discount.criteria.matching.workflow=************************************
tray.io.source.salesforce.discount.criteria.matching.workflow=930e5507-2628-4700-916f-ea7226af051b
tray.io.source.hubspot.retrieve.contact.workflow=${/app/dev/credential/tray_io_fetch_hubspot_contact_property_workflow}
tray.io.source.googlesheet.workflow=${/app/dev/credential/tray_io_source_googlesheet_workflow}
tray.io.source.googlesheet.mapping.workflow=${/app/dev/credential/tray_io_mapping_googlesheet_workflow}

kinesis.stream.name=ae-dev-gamification-stream

dynamodb.user.activity.table=${/app/dev/credential/useractivity_dynamodb_table_name}
dynamodb.video-analytics.processed.table=${/app/dev/credential/video_analytics_processed_dynamodb_table_name}
dynamodb.video-analytics.processed.user-watch-time.table=${/app/dev/credential/video_analytics_processed_user_watch_time_dynamodb_table_name}
dynamodb.meeting-analytics.processed.table=${/app/dev/credential/meeting_analytics_processed_data_dynamodb_table_name}
dynamodb.mobile-analytics.processed.table=${/app/dev/credential/mobile_analytics_processed_data_dynamodb_table_name}

filter.profile.table=profile
filter.registration.table=registration
filter.engagement.table=engagement

#reset event id
resetEvent.eventIds= ${/app/dev/credential/reset_event_ids}

#reset event under organiser id
resetEvent.organiserId= ${/app/dev/credential/reset_event_organiser_id}

#linkedin Credentials
linkedinClientId=${/app/dev/credential/linkedIn_clientId}
linkedinClientSecret=${/app/dev/credential/linkedIn_clientSecret}

#AppleLogin Credentials
applePrivateKey=${/app/dev/credential/apple_authKeyP8}
appleClientId=${/app/dev/credential/apple_clientId}
appleKeyId=${/app/dev/credential/apple_keyId}
appleTeamId=${/app/dev/credential/apple_teamId}

custom.css.s3.bucket=${/app/dev/credential/custom_css_s3_bucket}
custom.css.distribution=${/app/dev/credential/custom_css_cloudfront_distribution_id}

mux.asset.glacier.vault.name=ae-dev-mux-live-stream-assets
#Multipart size(byte) for upload archive in to glacier // 4MB
mux.asset.glacier.multipart.upload.size=4194304
mux.asset.glacier.sns.topic.arn=${/app/dev/credential/mux_live_stream_archive_sns_topic_arn}
mux.to.s3.trigger.apigw.url=${/app/dev/credential/aws_mux_to_s3_trigger_apigw_url}

api.rate.limit.per.minute=${/app/dev/credential/api_rate_limit_count}

beefreeClientId=${/app/dev/credential/beefree_clientId}
beefreeClientSecret=${/app/dev/credential/beefree_clientSecret}

beePageBuilderClientId=${/app/dev/credential/beefree_pageBuilder_clientId}
beePageBuilderSecret=${/app/dev/credential/beefree_pageBuilder_clientSecret}
beefree.api.token=${/app/dev/credential/beefree_api_token}
beefree.thumbnail.bucket=${/app/dev/credential/beefree_thumbnail_bucket}

slack.check.in.error.webhook=*******************************************************************************
cloudinary.url=https://res.cloudinary.com/devaccel/image/fetch/

cadmium.integration.lambda.name=ae-dev-cadmium-integration

badge.pdf.lambda.name=ae-dev-badge-pdf-generator
badge.pdf.lambda.api.token=${/app/dev/credential/badge_pdf_token}

server.servlet.session.cookie.secure=true

spreedly.host.url=https://core.spreedly.com/v1/
spreedly.environment.key=${/app/dev/credential/spreedly_environment_key}
spreedly.api.key=${/app/dev/credential/spreedly_api_key}
spreedly.private.key=spreedly/private_key_DEV_ENV.pem
spreedly.certificate.token=7XMYTJ29F38JPAG8JTEWFY5ETW

recaptcha.enabled=${/app/dev/credential/recaptcha_enabled}

cvent.custom.integration.wl.ids=${/app/dev/credential/cvent_custom_integration_wl_id}