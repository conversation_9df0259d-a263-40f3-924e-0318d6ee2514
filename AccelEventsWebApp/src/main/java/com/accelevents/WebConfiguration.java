package com.accelevents;

import com.accelevents.handler.ratelimit.RateLimitInterceptor;
import com.amazonaws.xray.javax.servlet.AWSXRayServletFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.Filter;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {
    @Autowired
    private RateLimitInterceptor rateLimitingInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry){
        registry.addInterceptor(rateLimitingInterceptor);

    }

	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("/mobile/**");
		registry.addMapping("/rest/**");
	}
	
	@Override
	public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
	    configurer.favorPathExtension(false); 
	}

	@Bean
	public Filter TracingFilter() {
		return new AWSXRayServletFilter("API-accelevents");
	}
}