package com.accelevents.controllers.rest.session_speaker.host;

import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventPlanConfig;
import com.accelevents.domain.User;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.LogClickDocumentDto;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.dto.SessionDocumentDownloadedDTO;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.DownloadService;
import com.accelevents.services.VirtualEventSessionLoggingService;
import com.accelevents.services.dynamodb.analytics.ConsolidatedAnalyticsService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.SessionThirdPartyService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.EventUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static com.accelevents.domain.enums.EventFormat.IN_PERSON;
import static com.accelevents.enums.PlanConfigNames.SINGLE_EVENT_PLAN_STARTER;
import static com.accelevents.utils.Constants.CONTENT_CSV;

@RestController
@RequestMapping({"/rest/host/event/{eventUrl}/session","/rest/host/session"})
@Tag(name = "/rest/host/session")
public class HostSessionAnalyticsController {

	@Autowired
	private AuthValidator authValidator;
	@Autowired
	private SessionService sessionService;
	@Autowired
	private UserSessionService userSessionService;
	@Autowired
	private SessionThirdPartyService sessionThirdPartyService;
	@Autowired
    private DownloadService downloadService;
    @Autowired
    private EventUtils eventUtils;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    VirtualEventSessionLoggingService virtualEventSessionLoggingService;
    @Autowired
    private ConsolidatedAnalyticsService consolidatedAnalyticsService;

	//@Operation(description = "get sessions details analytics")
	@GetMapping("{id}/analytics")
	public SessionDetailsAnalytics getSessionsDetailsAnalytics(@PathVariable("id") Long sessionId,
															   @RequestParam(required = false, defaultValue = "") String expand,
                                                               @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
															   Authentication auth) {
		UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
		SessionDetailsAnalytics sessionDetailsAnalytics = sessionService.getSessionsAnalyticsById(sessionId, expand, userEvent.getEvent());

        sessionDetailsAnalytics.setTotalChatCount(sessionThirdPartyService.setChatCountBySessionId(sessionId));
		return sessionDetailsAnalytics;
	}

    @GetMapping("{sessionId}/videoAnalytics/with-chart-view")
    public SessionConsolidatedVideoAnalyticsWithChartDto getConsolidatedSessionVideoAnalytics(@PathVariable("sessionId") Long sessionId,
                                                                                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                                              Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        SessionConsolidatedVideoAnalyticsWithChartDto sessionConsolidatedVideoAnalyticsWithChartDto =  consolidatedAnalyticsService.getConsolidatedVideoAnalyticsForSession(userEvent.getEvent().getEventId(), sessionId);
        sessionConsolidatedVideoAnalyticsWithChartDto.setTotalChatCount(sessionThirdPartyService.setChatCountBySessionId(sessionId));

        return sessionConsolidatedVideoAnalyticsWithChartDto;
    }

    @GetMapping("/videoAnalytics/consolidated")
    public EventConsolidatedVideoAnalyticsDto getConsolidatedEvent(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                             Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return consolidatedAnalyticsService.getEventVideoAnalyticsDashboardDetails(userEvent.getEvent());
    }

    @PostMapping("/videoAnalytics/consolidated-sessions")
    public List<SessionConsolidatedVideoAnalyticsDto> getConsolidatedEventAllSessionVideoAnalytics(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                                                    @RequestBody SessionListInputDto sessionListInputDto,
                                                                                                   Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return consolidatedAnalyticsService.getConsolidatedVideoAnalyticsForEvent(userEvent.getEvent().getEventId(), sessionListInputDto);
    }

	//@Operation(description = "get all sessions")
	@PostMapping("/analytics")
	public DataTableResponse getSessionsAnalytics(
            @RequestBody PageSizeSearchObj searchObj,
            @RequestParam(required = false) Long eventId,
            @RequestParam(required = false, defaultValue = "") String expand,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            Authentication auth) {
		Event event = authValidator.authEventOrGetEvent(eventId, auth, eventUrl);
		return sessionService.getSessionsAnalytics(searchObj.getSearchWithEscapeSpecialChars(), expand, event, PageRequest.of(Math.max(searchObj.getPage(),0), (searchObj.getSize()>0)?searchObj.getSize():10));
	}


    @GetMapping("/analytics/dashboard")
    public SessionDashboardAnalyticsDto getSessionDashboardAnalytics(@PathVariable(required = false) String eventUrl,
                                                                     Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getSessionDashboardAnalytics(userEvent.getEvent(), userEvent.getUser());
    }

	//@Operation(description = "get all sessions")
	@GetMapping("{id}/attendee")
	public List<RegisterdHolderUsers> getSessionsAttendee(
			@PathVariable("id") Long sessionId,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
			Authentication auth) {
		UserEvent userEvent = authValidator.authHostLevelAccess( auth, eventUrl);
        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(userEvent.getEvent().getEventId());
        if (eventPlanConfig != null && (eventPlanConfig.getPlanConfig().getPlanName().equals(PlanConfigNames.FREE_PLAN.getName()) ||
                eventPlanConfig.getPlanConfig().getPlanName().equals(PlanConfigNames.STARTER.getName()) || eventPlanConfig.getPlanConfig().getPlanName().equalsIgnoreCase(SINGLE_EVENT_PLAN_STARTER.getName())) && !IN_PERSON.equals(userEvent.getEvent().getEventFormat())){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ATTENDEE_ANALYTICS_NOT_AVAILBLE_IN_THIS_PLAN);
        }
        return userSessionService.findSessionAttendedUserInfo(sessionId);
	}

    @GetMapping("{id}/attendeePage")
    public DataTableResponse getSessionsAttendeePage(
            @PathVariable("id") Long sessionId,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            @RequestParam( required = false, defaultValue = "0") Integer page,
            @RequestParam( required = false, defaultValue = "10") Integer size,
            Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess( auth, eventUrl);
        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(userEvent.getEvent().getEventId());
        if (eventPlanConfig != null && (eventPlanConfig.getPlanConfig().getPlanName().equals(PlanConfigNames.FREE_PLAN.getName()) ||
                eventPlanConfig.getPlanConfig().getPlanName().equals(PlanConfigNames.STARTER.getName()) || eventPlanConfig.getPlanConfig().getPlanName().equalsIgnoreCase(SINGLE_EVENT_PLAN_STARTER.getName())) && !IN_PERSON.equals(userEvent.getEvent().getEventFormat())){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ATTENDEE_ANALYTICS_NOT_AVAILBLE_IN_THIS_PLAN);
        }
        return userSessionService.findSessionAttendedUserInfoPage(sessionId, page, size);
    }

    //@Operation(description = "Download session attendance csv report")
    @GetMapping("{id}/download/session-attendance/CSV")
    public void downloadSessionAttendanceReport(@PathVariable("id") Long sessionId,
                                                HttpServletResponse response,
                                                @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        CommonUtil.prepareDownloadableResponseHeader(response, "Session Attendance", CONTENT_CSV);
        downloadService.downloadSessionAttendanceReport(response.getWriter(), sessionId, userEvent.getEvent());
    }

    //@Operation(description = "Download session video analytics graph csv report")
    @GetMapping("{id}/download/graph/CSV")
    public void downloadSessionVideoAnalyticsGraphDataCSV(@PathVariable("id") Long sessionId, @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                HttpServletResponse response,
                                                Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth,eventUrl);
        CommonUtil.prepareDownloadableResponseHeader(response, "Session Graph Data", CONTENT_CSV);
        downloadService.downloadSessionVideoAnalyticsGraphDataCSV(response.getWriter(), sessionId, userEvent.getEvent());
    }

    //@Operation(description = "Download session overview csv report")
    @GetMapping("/download/session-overview-report/CSV")
    public void downloadSessionOverviewReport(HttpServletResponse response,
                                              Authentication auth,
                                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        CommonUtil.prepareDownloadableResponseHeader(response, "Session Overview", CONTENT_CSV);
        downloadService.downloadSessionOverviewReport(response.getWriter(), userEvent.getEvent());

    }
    //@Operation(description = "Log Click document data")
    @PostMapping("/{sessionId}/document")
    public void logClickDocumentData(@PathVariable String eventUrl,@PathVariable("sessionId") Long sessionId,
                                     @RequestBody @Validated LogClickDocumentDto logClickDocumentDto, Authentication auth) {
        User user = authValidator.authUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        virtualEventSessionLoggingService.logClickDocument(sessionId, logClickDocumentDto,user, event);
    }

    //@Operation(description = "Log document download data")
    @GetMapping("/{sessionId}/document/{documentName}")
    public void logDocumentDownloadData(@PathVariable String eventUrl, @PathVariable Long sessionId,
                                        @PathVariable String documentName, Authentication auth) {
        User user = authValidator.authUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        virtualEventSessionLoggingService.logDocumentDownload(sessionId, documentName, user, event);
    }

    //@Operation(description = "Get All Visitor who downloaded the document")
    @GetMapping("{sessionId}/documentDownloaded")
    public List<SessionDocumentDownloadedDTO> getDocumetDownlodList(@PathVariable String eventUrl, Authentication auth, @PathVariable Long sessionId) {
        Event event = roEventService.getEventByURL(eventUrl);
        authValidator.authUser(auth);
        return virtualEventSessionLoggingService.getVisitorListForSessionDocDownloaded(event, sessionId);
    }
    //@Operation(description = "Get All Visitor")
    @GetMapping("{sessionId}/visitor")
    public List<SessionDocumentDownloadedDTO> getVisitorList(@PathVariable String eventUrl, Authentication auth, @PathVariable Long sessionId) {
        Event event = roEventService.getEventByURL(eventUrl);
        authValidator.authUser(auth);
        return virtualEventSessionLoggingService.getVisitorList(event, sessionId);
    }
}
