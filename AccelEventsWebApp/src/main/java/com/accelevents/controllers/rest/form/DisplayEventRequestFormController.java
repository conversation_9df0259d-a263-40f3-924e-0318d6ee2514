package com.accelevents.controllers.rest.form;

import com.accelevents.domain.enums.RuleFormType;
import com.accelevents.dto.EventRequestFormFieldDto;
import com.accelevents.dto.FormRulesDetailsAttributesDto;
import com.accelevents.form.dto.EventRequestFormDto;
import com.accelevents.form.services.EventRequestFormService;
import com.accelevents.services.FormRulesService;
import com.accelevents.services.TicketingManageService;
import com.accelevents.utils.TimeZone;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/rest/event-request-form/")
@Tag(name = "Event Request Form API")
public class DisplayEventRequestFormController {

    @Autowired
    private EventRequestFormService eventRequestFormService;

    @Autowired
    private FormRulesService formRulesService;

    @Autowired
    private TicketingManageService ticketingManageService;

    /**
     * Retrieves the list of visible fields for a specific Event Request Form by its ID.
     *
     * @param id the ID of the Event Request Form
     * @return a list of {@link EventRequestFormFieldDto} representing the visible fields of the form
     */
    @GetMapping("/{id}/fields")
    @Operation(summary = "Get form fields by id", description = "Returns the fields for the specified event request form by id")
    public List<EventRequestFormFieldDto> getEventRequestFormFields(
            @PathVariable("id") long id) {
        return eventRequestFormService.findByEventRequestFormIdAndExcludeHidden(id);
    }

    /**
     * Retrieves all conditional logic rules based on attributes for a specific Event Request Form.
     *
     * @param id the ID of the Event Request Form
     * @param ruleFormType the type of form (e.g., EVENT_REGISTRATION, REQUEST_FORM) to filter rules
     * @return a list of {@link FormRulesDetailsAttributesDto} containing the attribute-based conditional rules
     */
    @GetMapping("/{id}/rules-attributes")
    @Operation(summary = "Retrieve form rules based on attributes", description = "Fetches all conditional logic rules associated with attributes for a specific event request form, based on the provided form type.")
    public List<FormRulesDetailsAttributesDto> getAllFormRulesByAttributes( @PathVariable("id") long id,@RequestParam(value = "type") RuleFormType ruleFormType) {
        return formRulesService.getAllFormRulesByAttributes(null, id, ruleFormType, 0L);
    }

    /**
     * Retrieves the details of an Event Request Form by its URL.
     *
     * @param url the unique URL slug associated with the Event Request Form
     * @return an {@link EventRequestFormDto} containing the details of the form
     */
    @GetMapping("/{url}")
    @Operation(summary = "Get event request form details", description = "Returns the details of the event request form by channel URL.")
    public EventRequestFormDto getEventRequestFormDetails(@PathVariable("url") String url,
                                                          @RequestParam(required = false, defaultValue = "false") boolean isRequestedByWhiteLabelAdmin) {
        return eventRequestFormService.getEventRequestFormDetailsByUrl(url,isRequestedByWhiteLabelAdmin);
    }

    /**
     * Retrieves all available time zones.
     *
     * @return A list of {@link TimeZone} objects representing the available time zones.
     * @see java.util.TimeZone
     */
    @GetMapping("/all-time-zones")
    @Operation(summary = "Get all time zones", description = "Returns all time zones")
    public List<TimeZone> getAllTimeZones() {
        return ticketingManageService.getAllTimeZonesForEvent();
    }

}
