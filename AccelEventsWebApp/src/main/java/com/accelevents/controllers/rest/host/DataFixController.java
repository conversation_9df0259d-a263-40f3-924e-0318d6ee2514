package com.accelevents.controllers.rest.host;

import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.IntegrationSourceType;
import com.accelevents.dto.*;
import com.accelevents.dto.tray.io.SolutionDetails;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.security.tokenstore.TokenStoreService;
import com.accelevents.services.DownloadService;
import com.accelevents.services.UserService;
import com.accelevents.services.WaitListService;
import com.accelevents.services.impl.DataFixServiceImpl;
import com.accelevents.services.tray.io.TrayIOService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.UserUtils;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

import static com.accelevents.utils.ApiMessages.EVENT_URL_DESC;
import static com.accelevents.utils.Constants.NOT_AUTHORIZE;
import static com.accelevents.utils.Constants.SUCCESS;

@RestController
@RequestMapping("/rest/host/dataFix/")
@Tag(name = "/rest/host/dataFix/")
public class DataFixController {


    @Autowired
    private DataFixServiceImpl dataFixService;

    @Autowired
    private AuthValidator authValidator;

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private TokenStoreService tokenStoreService;

    @Autowired
    private ROEventService roEventService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private UserUtils userUtils;

    @Autowired
    private TrayIOService trayIOService;

    @Autowired
    private TrayIntegrationService trayIntegrationService;

    @Autowired
    private WaitListService waitListService;

    @GetMapping(path = {"/cellPhone/{from}/{to}"})
    public ResponseDto updatePhoneNumber(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.getPhoneNumberByTicketHolderAttributes(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventTicketsWithHolderInfo/{from}/{to}")
    public ResponseDto updateEventTicketsWithHolderInfo(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEventTicketsWithHolderInfo(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //TODO: Do we want this API ? Please check once the API before do data fix, there are some changes over it   //NOSONAR
    @GetMapping("/updateAddressWithCountryCode/{from}/{to}")
    public ResponseDto updateAddressWithCountryCode(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateAddressWithCountryCode(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //TODO: Do we want this API ? DONE changes of jsonFormatedValue, Please check once the API before do data fix, there are some changes over it  //NOSONAR
    @GetMapping("/updateAddressWithRenameParameted/{from}/{to}/{renamedParameter}/")
    public ResponseDto updateAddressWithRenameParameted(@PathVariable long from, @PathVariable long to, @PathVariable String renamedParameter, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateAddressWithRenameParameted(from, to, renamedParameter);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEmailInTicketingOrderFromPurchaserData/{from}/{to}")
    public ResponseDto updateEmailInTicketingOrderFromPurchaserData(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEmailInTicketingOrderFromPurchaserData(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateStripeForDuplicateEvent/{from}/{to}")
    public ResponseDto updateStripeForDuplicateEvent(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.dataFixForDuplicateEvent(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventWithBookWholeTables/ticketing/{from}/{to}")
    public ResponseDto updateEventWithBookWholeTables(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEventWithBookWholeTables(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateIntercomCompany/intercom/{from}/{to}")
    public ResponseDto updateIntercomCompany(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateIntercomCompany(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateIntercomUser/intercom/{from}/{to}")
    public ResponseDto updateIntercomUser(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateIntercomUser(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateHolderAttribute/attribute/{from}/{to}")
    public ResponseDto updateHolderAttribute(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateHolderAttribute(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updatePayments")
    public ResponseDto test(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updatePayments();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateBuyButtonText")
    public ResponseDto updateBuyButtonText(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateBuyButtonText();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/createCouponAccessCodeAndAttributesForRecurringEvents/{from}/{to}")
    public ResponseDto createCouponAccessCodeAndAttributesForRecurringEvents(@PathVariable long from, @PathVariable long to, Authentication auth) throws ParseException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createCouponAccessCodeAndAttributesForRecurringEvents(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/updateWhiteLabelUserNotification")
    public ResponseDto updateWhiteLabelUserNotification(@RequestBody @Validated WhiteLabelIdsDto whiteLabelIdsDto, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateWhiteLabelUserNotification(whiteLabelIdsDto,superAdminUser);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/updateCountryCodeInEventTickets/eventId/{eventId}")
    public ResponseDto updateEventTickets(@PathVariable long eventId, Authentication auth) throws ParseException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEventTicketsHavingCountryCodeMissingAndPhoneNumberPresent(eventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventInRedis/{from}/{to}")
    public ResponseDto updateEventInRedis(@PathVariable long from, @PathVariable long to, Authentication auth) {

        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/createAttributesForSpecficRecurringEvent/eventId/{eventId}/recurringEventId/{recurringEventId}")
    public ResponseDto createAttributesForSpecficRecurringEvent(@PathVariable long eventId, @PathVariable long recurringEventId, Authentication auth) throws ParseException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createAttributeForRecurringEvent(eventId, recurringEventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(path = {"/phoneNumber/ticketHolder/{from}/{to}"})
    public ResponseDto updatePhoneNumberInUser(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updatePhoneNumberByTicketHolderAttributes(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PutMapping("/updateCategoryForSeatsIo/eventId/{from}/{to}")
    public ResponseDto updateCategoryForSeatsIo(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateCategoryForSeatsIo(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update VirtualEventSettings Left Nav Menu by VirtualEventSettings ids")
    @PostMapping("/updateVirtualEventSettingsLeftNavMenu/{from}/{to}")
    public ResponseDto updateVirtualEventSettingsLeftNavMenu(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateVirtualEventSettingsLeftNavMenu(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/updateDuplicatePhone")
    public ResponseDto updateDuplicatePhoneNumber(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updatePhoneNumberRemoveDuplicate();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PutMapping("updateWhiteLabelImages/whiteLabelId/{whiteLabelId}")
    public ResponseDto updateWhiteLabelImages(@PathVariable long whiteLabelId, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateWhiteLabelImages(whiteLabelId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(path = {"/stripeCountry/{from}/{to}"})
    public ResponseDto updateStripeCountry(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateStripeCountry(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(path = {"/stripeCardCountry/{from}/{to}"})
    public ResponseDto updateStripeCardCountry(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateStripeCardCountry(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateIntercomCompany/eventId/{from}/{to}")
    public ResponseDto updateIntercomCompanyAddEventDuplicatedFrom(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateIntercomCompanyAddEventDuplicatedFrom(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateDeletedEventsInIntercom")
    public ResponseDto updateDeletedEventsInIntercom(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateDeletedEventsInIntercom();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateRecurringEventCancelStatus")
    public ResponseDto updateRecurringEventCancelStatus(Authentication auth) {

        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateRecurringEventCancelStatus();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/transferCategoriesToCategoryId/{from}/{to}")
    public ResponseDto transferCategoriesToCategoryId(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.transferCategoriesToCategoryId(from, to) ? new ResponseDto(Constants.SUCCESS, Constants.SUCCESS) : new ResponseDto(Constants.FAIL, Constants.FAIL);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateCategoryIdRecurringEvents/{from}/{to}")
    public ResponseDto updateCategoryIdForRecurringEvents(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateCategoryIdForRecurringEvents(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/createUserFromSpeaker/{from}/{to}")
    public ResponseDto createUserFromSpeaker(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createUserFromSpeaker(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/createTransactionalConditionalLogic")
    public ResponseDto createTransactionalConditionalLogic(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createTransactionRecordsForVirtualEvents();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/createSessionTagAndTrack")
    public ResponseDto createSessionTagAndTrack(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createSessionTagAndTrack();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateSpeakersPosition/eventId/{from}/{to}")
    public ResponseDto updateSpeakerPosition(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateSpeakerPosition(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateSessionPosition/eventId/{from}/{to}")
    public ResponseDto updateSessionPosition(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateSessionPositionForDataFix(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateExhibitorDocument/exhibitorId/{from}/{to}")
    public ResponseDto updateExhibitorDocument(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateExhibitorDocuments(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateExhibitorProductPosition/exhibitorId/{from}/{to}")
    public ResponseDto updateExhibitorProductPosition(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateExhibitorProductPosition(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateSessionSpeakersPosition/sessionId/{from}/{to}")
    public ResponseDto updateSessionSpeakerPosition(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateSessionSpeakerPosition(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateLeadRetrieverUserId/{from}/{to}")
    public ResponseDto updateLeadRetrieverUserId(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateLeadRetrieverUserId(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateExhibitorDocumentsKeyAndName/exhibitor/{from}/{to}")
    public ResponseDto updateExhibitorDocumentsKeyAndName(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateExhibitorDocumentsKeyAndName(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updatePortalTabName")
    public ResponseDto updatePortalTabName(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updatePortalTabName();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updatePortalTabLobbySection/AddGamificationToggle")
    public ResponseDto updatePortalTabLobbySectionWithAddGamificationToggle(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            boolean isAllRecordsUpdated = dataFixService.updatePortalTabLobbySectionWithAddGamificationToggle();
            return new ResponseDto(Constants.SUCCESS, isAllRecordsUpdated ? Constants.SUCCESS : Constants.SOME_RECORDS_ARE_NOT_UPDATED);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateIntercomCompanyEventFormat/eventId/{from}/{to}")
    public ResponseDto updateIntercomCompanyEventFormat(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateIntercomCompanyEventFormat(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateSpeakersPosition/eventId/{eventId}/position/{position}/lastPosition/{lastPosition}")
    public ResponseDto updateSpeakerPosition(@PathVariable long eventId, @PathVariable double position, @PathVariable double lastPosition, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateSpeakerPosition(eventId, position, lastPosition);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updatePortalTabLobbySection/AddCustomToggle/eventId/{from}/{to}")
    public ResponseDto updatePortalTabLobbySectionWithAddCustomToggle(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            boolean isAllRecordsUpdated = dataFixService.updatePortalTabLobbySectionWithAddCustomToggle(from, to);
            return new ResponseDto(Constants.SUCCESS, isAllRecordsUpdated ? Constants.SUCCESS : Constants.SOME_RECORDS_ARE_NOT_UPDATED);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateItemPositionByModuleIdAndModule/module/{module}/moduleId/{moduleId}")
    public ResponseDto updateSpeakerPosition(@PathVariable long moduleId, @PathVariable String module, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateItemPositionByEventIdAndModule(moduleId, module);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateMeetingObj")
    public ResponseDto updateMeetingObj(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateMeetingObj();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateMeetingObjForExhibitor")
    public ResponseDto updateMeetingObjForExhibitor(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateMeetingObjForExhibitor();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateLeadRetrieverLocation/{from}/{to}")
    public ResponseDto updateLeadRetrieverLocation(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateLeadRetrieverLocation(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //TODO: This data fix seems for specific event only. check Git blame comment, Please check once the API before do data fix, there are some changes over it  //NOSONAR
    @GetMapping("/updateBillingAddress/ticketTypeId/{ticketTypeId}")
    public ResponseDto updateBillingAddress(@PathVariable long ticketTypeId, Authentication auth) throws JAXBException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateBillingAddress(ticketTypeId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/CSVAttendee/to/newAttendee/{eventId}")
    public ResponseDto migrateAttendeeCSVRecordToNewAttendeeTable(@PathVariable long eventId, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.transferCSVAttendeeToNewTable(eventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateCardIdBasedOnCustomerId/virtualEventSetting/{from}/{to}")
    public List<Long> updateCardIdBasedOnCustomerId(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateCardIdBasedOnCustomerId(from, to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateCardIdAndCustomerId/event/{from}/{to}")
    public List<Long> updateCardIdAndCustomerId(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateCardIdAndCustomerIdInStripeTransactionBasedOnCustomerIdFromStaff(from, to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventIdInGetStreamChannels/{eventId}")
    public ResponseDto updateEventIdInGetStreamChannels(@PathVariable long eventId, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateEventIdInGetStreamChannels(eventId);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateGetStreamOneToOneChannels/{from}/{to}")
    public ResponseDto updateGetStreamOneToOneChannels(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateGetStreamOneToOneChannels(from, to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateNetworkingLoungePosition/networkingLoungeId/{from}/{to}")
    public ResponseDto updateNetworkingLoungePosition(@PathVariable String from, @PathVariable String to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateNetworkingLoungePosition(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/eventBilling/{tab}")
    public List<EventBillingInfo> getEventBillingInfo(@PathVariable String tab, @RequestParam(required = false) String searchString,
                                                      @Parameter(description = "Pages are zero indexed, thus providing 0 for page will return the first page.")
                                                      @RequestParam(defaultValue = "0") int page,
                                                      @RequestParam(defaultValue = "10") int size,
                                                      @RequestParam(value = "fromDate", required = false) String fromDate,
                                                      @RequestParam(value = "toDate", required = false) String toDate,
                                                      @RequestParam(value = "eventStatus", required = false) String eventStatus,
                                                      @RequestParam(value = "isAllowToUpdate", required = false) Boolean isAllowToUpdate, Authentication auth) {

        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateEventBillingInfo(tab, size, page, searchString, fromDate, toDate, eventStatus, (null != isAllowToUpdate) ? isAllowToUpdate : false);//NOSONAR
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateCategoryPositionInExhibitor/exhibitorCategoryId/{from}/{to}")
    public ResponseDto updateCategoryPositionInExhibitor(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateCategoryPositionInExhibitor(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(value = {"/addOldEventInHubspot/{from}/{to}", "/addOldEventInHubspot/{eventUrl}"})
    public ResponseDto addOldEventInHubspot(@PathVariable(required = false) String eventUrl,
                                            @PathVariable(required = false) String from,
                                            @PathVariable(required = false) String to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addEventInHubspot(eventUrl, from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(value = {"/addContactInHubspot/{from}/{to}", "/addContactInHubspot/{eventUrl}"})
    public ResponseDto addContactInHubspot(@PathVariable(required = false) String eventUrl,
                                           @PathVariable(required = false) String from,
                                           @PathVariable(required = false) String to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addContactInHubspot(eventUrl, from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(value = {"/addAssociationInHubspot/{from}/{to}", "/addAssociationInHubspot/{eventUrl}"})
    public ResponseDto addAssociationInHubspot(@PathVariable(required = false) String eventUrl,
                                               @PathVariable(required = false) String from,
                                               @PathVariable(required = false) String to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createAssociationInHubspot(eventUrl, from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/createHubspotEventSchema")
    public ResponseDto createHubspotEventSchema(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createHubspotEventSchema();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/createHubspotOrganizerSchema")
    public ResponseDto createHubspotOrganizerSchema(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createHubspotOrganizerSchema();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateAttendeeDetailsForEventInHubspot/{from}/{to}")
    public ResponseDto updateAttendeeDetailsForEventInHubspot(@PathVariable("from") Long from,
                                            @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateAttendeeDetailsForEventInHubspot(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventNameForEventInHubspot/{from}/{to}")
    public ResponseDto updateEventNameForEventInHubspot(@PathVariable("from") Long from,
                                                              @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEventNameForEventInHubspot(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateExhibitorSponsorJson/event/{from}/{to}")
    public void updateExhibitorSponsorJson(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateExhibitorSponsorJson(from, to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventDesignConfigTabsJson/eventDesignDetails/{from}/{to}")
    public ResponseDto updateConfigTabsJson(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEventDesignConfigTabsJson(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/gamification/{eventUrl}/{isEnable}")
    public ResponseDto enableGamification(@PathVariable String eventUrl, @PathVariable boolean isEnable, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.enableGamification(isEnable, eventUrl);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateTagAndTrackPosition/eventId/{from}/{to}")
    public ResponseDto updateTagAndTrackPosition(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateTagAndTrackPosition(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/updateEncodedTemplateId")
    public ResponseDto updateEncodedTemplateId(Authentication auth){
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEncodedTemplateId();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateHideChallenges/AddHideChallengesToggle")
    public ResponseDto updateHideChallengesLobbySectionWithAddHideChallengesToggle(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            boolean isAllRecordsUpdated = dataFixService.updateHideChallengesLobbySectionWithAddHideChallengesToggle();
            return new ResponseDto(Constants.SUCCESS, isAllRecordsUpdated ? Constants.SUCCESS : Constants.SOME_RECORDS_ARE_NOT_UPDATED);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/addNameInNeptune/eventId/{from}/{to}")
    public ResponseDto addNameInNeptune(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addNameInNeptune(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }

    @GetMapping("/addFullNameAndCompanyTitleInNeptune/eventId/{from}/{to}")
    public ResponseDto addFullNameInNeptune(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addFullNameAndCompanyTitleInNeptune(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }

    @GetMapping("/addFullNameAndCompanyInNeptune/eventId/{from}/{to}")
    public ResponseDto addFullNameAndCompanyInNeptune(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addFullNameAndCompanyInNeptune(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }

    @GetMapping("/updateButtonColorConfig/{from}/{to}")
    public ResponseDto updateVirtualEventSetting( @PathVariable("from") long from, @PathVariable("to") long to, Authentication auth)
    {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            this.dataFixService.updateVirtualEventSetting(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update VirtualEventSettings join session button color by VirtualEventSettings ids")
    @GetMapping("/updateButtonTextColorConfigFotJoinSession/{from}/{to}")
    public ResponseDto updateButtonTextColorConfigFotJoinSession( @PathVariable("from") long from, @PathVariable("to") long to, Authentication auth)
    {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            this.dataFixService.updateButtonTextColorConfigFotJoinSession(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/peoplePageUpdate/eventId/{from}/{to}")
    public ResponseDto peoplePageUpdate(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.peoplePageUpdate(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }

    @GetMapping("/peoplePageUpdateTwo/eventId/{from}/{to}")
    public ResponseDto peoplePageUpdateTwo(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.peoplePageUpdateTwo(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }
    @GetMapping("/deleteAttendees/eventId/{from}/{to}")
    public ResponseDto deleteAttendees(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteAttendees(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }
    @GetMapping("/deleteAttendeesTwo/eventId/{from}/{to}")
    public ResponseDto deleteAttendeesTwo(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteAttendeesTwo(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }
    @GetMapping("/updateSessionTypeLabels")
    public ResponseDto updateSessionTypeLabelsWithVirtualEventsTabs(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            boolean isAllRecordsUpdated = dataFixService.updateSessionTypeLabelsWithVirtualEventsTabs();
            return new ResponseDto(Constants.SUCCESS, isAllRecordsUpdated ? Constants.SUCCESS : Constants.SOME_RECORDS_ARE_NOT_UPDATED);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PutMapping("/updateVirtualEventButtonTextColorConfigJson/AddAllSessionSpeakerNameTextColor/eventId/{from}/{to}")
    public ResponseDto updateVirtualEventButtonTextColorConfigJson(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            boolean isAllRecordsUpdated = dataFixService.updateVirtualEventButtonTextColorConfigJson(from, to);
            return new ResponseDto(Constants.SUCCESS, isAllRecordsUpdated ? Constants.SUCCESS : Constants.SOME_RECORDS_ARE_NOT_UPDATED);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventPlan/{eventUrl}/planId/{planId}")
    public ResponseDto updateEventPlan(@PathVariable String eventUrl,
                                       @PathVariable Long planId,
                                       Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.mapChageebe(eventUrl,planId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }

    @GetMapping("/updateTransactionFeeConditionalLogicEvent/eventId/{eventId}")
    public ResponseDto updateTransactionFeeConditionalLogicEvent(@PathVariable("eventId") long id, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateTransactionFeeConditionalLogicEvent(id);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateTransactionFeeConditionalLogicOrganizer/organizerId/{organizerId}")
    public ResponseDto updateTransactionFeeConditionalLogicOrganizer(@PathVariable("organizerId") long id, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateTransactionFeeConditionalLogicOrganizer(id);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PutMapping("/transactionFeeConditionalLogic/events")
    public ResponseDto updateTransactionFeeConditionalLogicForEvent(
            @RequestBody @Validated EventIdsDto eventIdsDto, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateTransactionFeeConditionalLogicForEvent(eventIdsDto);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    //@Operation(description = "Update EventTicketingTypeAeFee From TFCL")
    @PutMapping("/updateEventTicketingTypeAeFeeFromTransactionFeeConditionalLogicByEvent/event/")
    public ResponseDto updateEventTicketingTypeAeFeeFromTransactionFeeConditionalLogicByEvent(@RequestBody @Validated EventIdsDto eventIdsDto, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEventTicketingTypeAeFeeOnTransactionFeeConditionalLogicByEvent(eventIdsDto);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PutMapping("/transactionFeeConditionalLogic/whiteLabels")
    public ResponseDto updateTransactionFeeConditionalLogicForWhiteLabel(
            @RequestBody @Validated WhiteLabelIdsDto whiteLabelIdsDto, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateTransactionFeeConditionalLogicForWL(whiteLabelIdsDto);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transactionFeeConditionalLogic/whiteLabels")
    public ResponseDto insertTransactionFeeConditionalLogicForWhiteLabel(
            @RequestBody @Validated WhiteLabelIdsDto whiteLabelIdsDto, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.insertTransactionFeeConditionalLogicForWL(whiteLabelIdsDto);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transactionFeeConditionalLogic/organizer")
    public ResponseDto insertTransactionFeeConditionalLogicForOrganizer(
            @RequestBody @Validated OrganizerIdsDto organizerIdsDto, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.insertTransactionFeeConditionalLogicForOrg(organizerIdsDto);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transactionFeeConditionalLogic/events")
    public ResponseDto insertTransactionFeeConditionalLogicFoEvent(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.insertTransactionFeeConditionalLogicFoEvent();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/intercom/company/planAndOrganizer/{fromEventId}/{toEventId}")
    public ResponseDto updateAllCompanyPlanAndOrg(@PathVariable long fromEventId, @PathVariable long toEventId, Authentication auth) throws InterruptedException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateAllCompanyPlanAndOrg(fromEventId, toEventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/intercom/user/plan/{fromUserId}/{toUserId}")
    public ResponseDto updateAllUserPlan(@PathVariable long fromUserId, @PathVariable long toUserId, Authentication auth) throws InterruptedException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateAllUsersPlan(fromUserId, toUserId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update isCustomPricing flag for non-standard values")
    @PutMapping("/transactionFeeConditionalLogic/isCustomPricingFlag/{fromEventId}/{toEventId}")
    public ResponseDto updateIsCustomPricingFlagForNonStandardAEFee(@PathVariable long fromEventId, @PathVariable long toEventId,Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateIsCustomPricingFlagForNonStandardAEFee(fromEventId, toEventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transactionFeeConditionalLogic/baseAddOn/whiteLabelId/{from}/{to}")
    public ResponseDto insertBaseAddOnRecordInTransactionFeeConditionalLogicForWL(
            @PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.insertBaseAddOnRecordInTransactionFeeConditionalLogicForWL(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transactionFeeConditionalLogic/baseAddOn/organizerId/{from}/{to}")
    public ResponseDto insertBaseAddOnRecordInTransactionFeeConditionalLogicForOrg(
            @PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.insertBaseAddOnRecordInTransactionFeeConditionalLogicForOrg(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transactionFeeConditionalLogic/baseAddOn/eventId/{from}/{to}")
    public ResponseDto insertBaseAddOnRecordInTransactionFeeConditionalLogicForEvent(
            @PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.insertBaseAddOnRecordInTransactionFeeConditionalLogicForEvent(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/updateTimezoneInPeoplePage/{from}/{to}")
    public ResponseDto updateTimezoneAndEquivalentTimezone(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateTimezoneIdAndAddEquivalentTimezoneInNeptune(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

//    //@Operation(description = "check solution reconfiguration required", authorizations = @Authorization(value = "access_token"), response = ResponseDto.class)
//    ////@ApiResponses(value = {
//            //@ApiResponse(code = 200, message = "returns success if success fully get solution version"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(value = "/reconfigurationRequired")
    public List<SolutionDetails> isMajorVersionFound(Authentication auth) {
        User user = this.userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return trayIOService.solutionReconfigurationRequired(IntegrationSourceType.ORGANIZER, "organizerURL", Boolean.FALSE, user);
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }


//    //@Operation(description = "update solution instance mapping TriggerUrl", authorizations = @Authorization(value = "access_token"), response = ResponseDto.class)
//    ////@ApiResponses(value = {
//            //@ApiResponse(code = 200, message = "returns success if success fully get solution version"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(value = "/updateMappingTriggerUrl/{instanceName}")
    public ResponseDto updateMappingTriggerUrl(Authentication auth, @PathVariable("instanceName") String instanceName) {
        User user = this.userUtils.getUser(auth);
        if(roUserService.isSuperAdminUser(user)){
            return new ResponseDto(Constants.SUCCESS,trayIOService.updateMappingTriggerUrl(instanceName));
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

//    //@Operation(description = "update solution instance mapping TriggerUrl", authorizations = @Authorization(value = "access_token"), response = ResponseDto.class)
//    ////@ApiResponses(value = {
//            //@ApiResponse(code = 200, message = "returns success if success fully get solution version"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(value = "/updateCampaignRecordTypeTriggerUrl/{instanceName}")
    public ResponseDto updateCampaignRecordTypeTriggerUrl(Authentication auth, @PathVariable("instanceName") String instanceName) {
        User user = this.userUtils.getUser(auth);
        if(roUserService.isSuperAdminUser(user)){
            return new ResponseDto(Constants.SUCCESS,trayIOService.updateCampaignRecordTypeTriggerUrl(instanceName));
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "update user notification preference data")
    @GetMapping(value = "/updateUserNotificationPreference")
    public ResponseDto updateUserNotificationPreference(Authentication auth) {
        User user = this.userUtils.getUser(auth);
        if(roUserService.isSuperAdminUser(user)){
            dataFixService.updateUserNotificationPreference();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //TODO: We can remove this. Can see in slack thread which has shared in jira DEV-11024, Please check once the API before do data fix, there are some changes over it  //NOSONAR
    //@Operation(description = "Update Holder Name In Event Tickets And User Table By CSV")
    @PostMapping("/updateUserByCSV/eventId/{id}")
    public ResponseDto updateHolderNameInEventTicketsAndUserTable(@RequestParam("File") MultipartFile multiPartFile,
                                         @PathVariable("id") Long eventId, Authentication auth) {
        User user = this.userUtils.getUser(auth);
        if(roUserService.isSuperAdminUser(user)){
            dataFixService.updateHolderNameInEventTicketsAndUserTable(multiPartFile, eventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/interestUpdate/{from}/{to}")
    public ResponseDto interestUpdate(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.interestUpdate(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }

    @PostMapping("/chargebee/customers/subscriptions/organizers/{from}/{to}")
    public ResponseDto createSubscriptionBInChargebeeForExistingCustomersForOrg(@PathVariable("from") long from,
                                                                          @PathVariable("to") long to,
                                                                          Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createSubscriptionBInChargebeeForExistingCustomersForOrg(from, to, superAdminUser);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/chargebee/customers/subscriptions/whitelabels/{from}/{to}")
    public ResponseDto createSubscriptionBInChargebeeForExistingCustomersForWL(@PathVariable("from") long from,
                                                                               @PathVariable("to") long to,
                                                                               Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createSubscriptionBInChargebeeForExistingCustomersForWL(from, to, superAdminUser);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "update ownerAvailabilityRule ")
    @GetMapping(value = "/updateOwnerAvailabilityRule")
    public ResponseDto updateOwnerAvailabilityRule(Authentication auth) {
        User user = this.userUtils.getUser(auth);
        if(roUserService.isSuperAdminUser(user)){
            dataFixService.updateOwnerAvailabilityRule();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }


    @PostMapping("/createMultiLanguageLabelForExistingVirtualEventSetting/virtualEventSettings/{from}/{to}")
    public ResponseDto createMultiLanguageLabelRecordsForExistingVirtualEvents(@PathVariable("from") long from,
                                                                                @PathVariable("to") long to,
                                                                                Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createMultiLanguageLabelRecordsForExistingVirtualEvents(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/createMultiLanguageLabelForExistingExpo/exhibitorId/{from}/{to}")
    public ResponseDto createMultiLanguageLabelRecordsForExistingExpo(@PathVariable("from") long from,
                                                                               @PathVariable("to") long to,
                                                                               Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createMultiLanguageLabelRecordsForExistingExpo(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "insert free tickets in seating categories")
    @PostMapping(value = "/insertFreeTicketsInSeatingCategories/{from}/{to}")
    public ResponseDto insertFreeTicketsInSeatingCategories(@PathVariable("from") long from,
                                                            @PathVariable("to") long to,Authentication auth){
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.insertFreeTicketsInSeatingCategories(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "update platform config json in EventPlanConfig")
    @PostMapping(value = "/updatePlatformConfigJsonLabel/{from}/{to}")
    public ResponseDto updatePlatformConfigJsonLabel(@PathVariable("from") long from,
                                                            @PathVariable("to") long to,Authentication auth){
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updatePlatformConfigJsonLabel(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    //@Operation(description = "update platform config json in chargebee plans")
    @PostMapping(value = "/updateChargebeePlatformConfigJsonLabel/{from}/{to}")
    public ResponseDto updateChargebeePlatformConfigJsonLabel(@PathVariable("from") long from,
                                                 @PathVariable("to") long to,Authentication auth){
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateChargebeePlatformConfigJsonLabel(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update join users with organizers")
    @PostMapping("/updateJoinUsersWithOrganizers/{fromEventId}/{toEventId}")
    public ResponseDto updateJoinUsersWithOrganizers(@PathVariable long fromEventId, @PathVariable long toEventId, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateJoinUsersWithOrganizers(fromEventId, toEventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

//    //@Operation(description = "Update Low latency flag and generate new stream key", response = ResponseDto.class)
//    @PostMapping("/updateLowLatencyFlagToDisable/{fromEventId}/{toEventId}")
//    public ResponseDto updateLowLatencyFlagToDisable(@PathVariable long fromEventId, @PathVariable long toEventId, Authentication auth) {
//        User superAdminUser = this.userUtils.getUser(auth);
//        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
//            dataFixService.updateLowLatencyFlagToDisable(fromEventId, toEventId);
//            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
//        } else {
//            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
//        }
//    }


    //@Operation(description = "delete lookingToMeet and tagLine fields from Neptune")
    @GetMapping("/deleteLookingToMeetAndTagLine/eventId/{from}/{to}")
    public ResponseDto deleteLookingToMeetAndTagLine(@PathVariable("from") long from,
                                                     @PathVariable("to") long to,
                                                     Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteLookingToMeetAndTagLine(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Data Fix For Event Landing Page")
    @PostMapping("/createMultiLanguageLabelForEventLandingPage/EventDesignDetail/{from}/{to}")
    public ResponseDto createMultiLanguageLabelForEventLandingPage(@PathVariable("from") long from,
                                                                               @PathVariable("to") long to,
                                                                               Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createMultiLanguageLabelRecordsForExistingVirtualEventsEventLandingPage(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Data Fix For Virtual Event Hub Page")
    @PostMapping("/createMultiLanguageLabelTipsForVirtualEventHub/Event/{from}/{to}")
    public ResponseDto createMultiLanguageLabelTipsForVirtualEventHub(@PathVariable("from") long from,
                                                                   @PathVariable("to") long to,
                                                                   Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.createMultiLanguageLabelTipsForVirtualEventHub(from, to);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @GetMapping("/updateExhibitorCategoryPositionJson/{from}/{to}")
    public ResponseDto updateExhibitorCategoryPositionJson(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateExhibitorCategoryPositionJson(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/createMultiLanguageLabelRecordsForSessionLabel/eventId/{from}/{to}")
    public ResponseDto createMultiLanguageLabelRecordsForSessionLabel(@PathVariable("from") long from,
                                                                      @PathVariable("to") long to,
                                                                      Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createMultiLanguageLabelRecordsForSessionLabel(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    //@Operation(description = "add enableAttendeeList in platform config json using chargebee plans")
    @PostMapping(value = "/addEnableAttendeeList/ChargebeePlansPlatformConfigJson/{from}/{to}")
    public ResponseDto addEnableAttendeeListForChargebeePlans(@PathVariable("from") long from,
                                                              @PathVariable("to") long to,Authentication auth){
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addEnableAttendeeListForChargebeePlans(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "add enableAttendeeList in platform config json column using EventPlanConfig")
    @PostMapping(value = "/addEnableAttendeeList/EventPlanConfig/{from}/{to}")
    public ResponseDto addEnableAttendeeListForEventPlanConfig(@PathVariable("from") long from,
                                                     @PathVariable("to") long to,Authentication auth){
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addEnableAttendeeListForEventPlanConfig(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "created invoice for attendees upload which was not created because subscription was not found")
    @PostMapping(value = "/import/attendees")
    public ResponseDto createInvoicesForAttendeesUpload(@RequestParam(value = "fromDate") String fromDate,
                                                        Authentication auth) throws Exception {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.createInvoicesForAttendeesUpload(fromDate);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    /**
     *This Endpoint is not datafix endpoint, it's generate the csv file of Attendee Profile Details of event.
     *Only for internal uses
     */
    @GetMapping(value = "/downloadCsvFileOfAttendees/{eventUrl:.+}")
    public void downloadCsvFileOfAttendees(@Parameter(description = EVENT_URL_DESC)  @PathVariable("eventUrl") String eventUrl, HttpServletResponse response, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        if (!this.roUserService.isSuperAdminUser(userEvent.getUser()) && !this.userService.isEventAdmin(event,userEvent.getUser())) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN_OR_EVENT_ADMIN);
        }
        downloadService.downloadAttendeeDetails(userEvent.getUser(),event,response);
    }

    @GetMapping("/updateMeetingRequestInNeptune/eventId/{from}/{to}")
    public ResponseDto updateMeetingRequestInNeptune(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateMeetingRequestInNeptune(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/insertOwnerToOrganizer")
    public ResponseDto insertOwnerToOrganizer(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.insertOwnerToOrganizer();
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @PostMapping("/mapContactToEvents/user/{email}")
    public ResponseDto mapContactToHSEventsByAdminEmail(@PathVariable(name = "email") String email, Authentication auth){
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        if(StringUtils.isNotBlank(email)){
            dataFixService.addAssociationContactToEvents(email);
        }
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @GetMapping("/updateVirtualEventSettingColor/{from}/{to}")
    public ResponseDto updateVirtualEventSettingColor( @PathVariable("from") long from, @PathVariable("to") long to, Authentication auth)
    {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            this.dataFixService.updateVirtualEventSettingColor(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "add state field separate from country field in ticket order form host side")
    @PostMapping(value = "/addStateField/{from}/{to}")
    public ResponseDto addStateField(@PathVariable("from") long from,
                                                               @PathVariable("to") long to,Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addStateFieldToTicketOrderForm(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Add missing usages records")
    @PostMapping(value = "/usages/organizerId/{from}/{to}")
    public ResponseDto insertMissingUsagesRecords(@PathVariable long from, @PathVariable long to, Authentication auth) throws Exception {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.insertMissingUsagesRecords(from, to, superAdminUser);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    //@Operation(description = "Add missing usages records for WL")
    @PostMapping(value = "/usages/whiteLabelId/{from}/{to}")
    public ResponseDto insertMissingUsagesRecordsForWL(@PathVariable long from, @PathVariable long to, Authentication auth) throws Exception {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.insertMissingUsagesRecordsForWL(from, to, superAdminUser);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    //@Operation(description = "Add DES hours charge records for organizer")
    @PostMapping(value = "/des/organizerId/{from}/{to}")
    public ResponseDto insertDESUsagesRecordsForOrganizer(@PathVariable long from, @PathVariable long to, Authentication auth) throws Exception {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.insertDESUsagesRecordsForOrganizer(from, to, superAdminUser);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    //@Operation(description = "Add DES hours charge records for whitelabel")
    @PostMapping(value = "/des/whiteLabelId/{from}/{to}")
    public ResponseDto insertDESUsagesRecordsForWL(@PathVariable long from, @PathVariable long to, Authentication auth) throws Exception {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.insertDESUsagesRecordsForWL(from, to, superAdminUser);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    //@Operation(description = "Add Fund Raiser Usages")
    @PostMapping(value = "/fundRaiserUsages/eventId/{from}/{to}")
    public ResponseDto insertFundRaiserUsagesRecords(@PathVariable long from, @PathVariable long to, Authentication auth) throws Exception {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.insertFundRaiserUsagesRecords(from, to, superAdminUser);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    //@Operation(description = "Add Ticketing fee Logs")
    @PostMapping(value = "/ticketingFee/eventId/{from}/{to}")
    public ResponseDto insertTicketingFeeUsagesRecords(@PathVariable long from, @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.insertTicketingFeeUsagesRecords(from, to, superAdminUser);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @PostMapping("/mux-domain/whitelabels/{from}/{to}")
    public ResponseDto createMuxDomainForWL(@PathVariable("from") long from,
                                                                               @PathVariable("to") long to,
                                                                               Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createMuxDomainForWL(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateEventStatusAndNumberOfRegistrantInHS/{from}/{to}")
    public ResponseDto updateEventStatusAndNumberOfRegistrantInHS(@PathVariable("from") Long from, @PathVariable("to") Long to,
                                                                  Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.updateNumberOfRegistrantsAndEventStatusInHubSpot(from, to);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @GetMapping("/updateMUXLivestreamAssetDetails")
    public ResponseDto updateMUXLivestreamAssetDetails(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.updateMUXLivestreamAssetDetails();
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @PostMapping("/updateOrganizerPlanFromLegacyToFree/{from}/{to}")
    public ResponseDto updateOrganizerPlanFromLegacyToFree(@PathVariable("from") Long from, @PathVariable("to") Long to,
                                                           Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.updateOrganizerPlanFromLegacyToFree(from, to);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @PostMapping("/updateWLPlanFromWLLegacyToFree/{from}/{to}")
    public ResponseDto updateWLPlanFromWLLegacyToFree(@PathVariable("from") Long from, @PathVariable("to") Long to,
                                                           Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.updateWLPlanFromWLLegacyToFree(from, to);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @GetMapping("/updateButtonColorConfigNumberOfSessionColor/{from}/{to}")
    public ResponseDto updateVirtualEventSettingForNumberOfSessionColor( @PathVariable("from") long from, @PathVariable("to") long to, Authentication auth)
    {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            this.dataFixService.updateVirtualEventSettingForNumberOfSessionColor(from,to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

//    //@Operation(description = "update solution instance salesforce record type", authorizations = @Authorization(value = "access_token"), response = ResponseDto.class)
//    ////@ApiResponses(value = {
//            //@ApiResponse(code = 200, message = "returns success if success fully get solution version"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(value = "/updateRecordType/instanceName/{instanceName}")
    public ResponseDto updateRecordTypeInIntegration(Authentication auth, @PathVariable("instanceName") String instanceName) {
        User user = this.userUtils.getUser(auth);
        if(roUserService.isSuperAdminUser(user)){
            return new ResponseDto(Constants.SUCCESS,trayIOService.updateRecordTypeInIntegration(instanceName));
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping("/updateSpeaker/eventId/{eventId}/userId/{userId}")
    public ResponseDto updateDuplicateSpeakerStatus( @PathVariable("eventId") long eventId, @PathVariable("userId") long userId, Authentication auth)
    {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            this.dataFixService.updateDuplicateSpeakerStatus(eventId,userId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Create Playback URL for workshop recording assets by session ID")
    @GetMapping("/createWorkshopRecordingPlayBackUrl/{sessionId}")
    public ResponseDto createWorkshopRecordingPlayBackUrl( @PathVariable("sessionId") long sessionId, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.createPlayBackUrlForWorkshopSession(sessionId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transfer/subscriptions/organizers/{from}/{to}")
    public ResponseDto transferSubscriptionForOrg(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.transferSubscription(from, to, superAdminUser);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/transfer/subscriptions/wl/{from}/{to}")
    public ResponseDto transferSubscriptionForWL(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.transferSubscriptionForWL(from, to, superAdminUser);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Delete User/Contact from Webapp,Hubspot,Intercom")
    @PostMapping("/deleteSpamUserAndEvent/{from}/{to}")
    public ResponseDto deleteSpamUserAndEventByUserId( @PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteUserFromDBAndHSAndInterCom(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update Workshop recording policy for WL domain")
    @PostMapping("/updateWorkshopRecordingPolicy")
    public ResponseDto updateWorkshopRecordingPolicy(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addPublicAccessToWorkshopRecordingBucketForWLDomain();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Process Workshop recording")
    @PostMapping("/process/workshopRecording/{assetId}")
    public ResponseDto updateWorkshopRecordingPolicy(@PathVariable("assetId") Long assetId, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.processWorkshopRecordingAssets(assetId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/updateRemainCredits")
    public void updateRemainCredits() {
            dataFixService.updateRemainingCredits();
    }


    //@Operation(description = "Update CustomTab Description")
    @PutMapping("/updateCustomTabDescription")
    public ResponseDto updateCustomTabDescription(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateCustomTabDescription();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update column selection")
    @PutMapping("/updateColumnSelection")
    public ResponseDto updateColumnSelection(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateColumnSelection();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Add required columns in column selection")
    @PutMapping("/updateColumnSelection/addRequiredColumns")
    public ResponseDto addRequiredColumnsInColumnSelection(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.addRequiredColumnsInColumnSelection();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Delete Mux assets from MUX which are marked as DELETE")
    @PostMapping("/deleteMuxAssetMarkedAsDeleted/{from}/{to}")
    public ResponseDto deleteMuxAssetsFromMuxByAssetsStatusDelete(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteMuxAssetsFromMuxByAssetsStatusDelete(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Delete Mux assets from MUX which are marked as DELETE")
    @PostMapping("/session/{sessionId}/storedMuxAssets/{assetId}")
    public ResponseDto storedMuxAsset(@PathVariable("sessionId") Long sessionId,@PathVariable("assetId") String assetId, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.storedMuxAssetDetail(sessionId,assetId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Delete Mux assets by Deleted event ids")
    @PostMapping("/deleteMuxAssetByEvent/{from}/{to}")
    public ResponseDto deleteMuxAssetsByDeletedEventId(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteMuxAssetsByDeletedEventId(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Delete Mux assets by Default playback is false")
    @PostMapping("/deleteMuxAsset/defaultPlaybackFalse/{from}/{to}")
    public ResponseDto deleteMuxAssetByIsDefaultPlaybackFalseAndId(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteMuxAssetByIsDefaultPlaybackFalseAndId(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Upload Mux Assets in Archive")
    @PostMapping("/uploadMuxAssets/{from}/{to}")
    public ResponseDto uploadMuxAssetIntoGlacierByAssetId(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.uploadMuxAssetIntoGlacierByAssetId(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Delete Mux assets with Default playback is true and have less than 5 registrant")
    @PostMapping("/deleteMuxAsset/defaultPlaybackTrueAndFiveRegistrant/{from}/{to}")
    public ResponseDto deleteMuxAssetByDefaultPlaybackTrueAndAssetId(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteMuxAssetByDefaultPlaybackTrueAndAssetId(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Delete Welcome video mux asset which are not in used")
    @PostMapping("/deleteWelcomeVideoMuxAsset/{from}/{to}")
    public ResponseDto deleteWelcomeVideoMuxAssetByAssetId(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.deleteWelcomeVideoMuxAssetByAssetId(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "disable live caption in session")
    @PostMapping("/disableLiveCaption/{from}/{to}")
    public ResponseDto disableLiveCaptionInExistingSession(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.disableLiveCaptionInExistingSession(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Data Fix For Virtual Event Hub Page")
    @PostMapping("/createMultiLanguageLabelVenueMapForEventLanding/Event/{from}/{to}")
    public ResponseDto createMultiLanguageLabelVenueMapForEventLanding(@PathVariable("from") long from,
                                                                      @PathVariable("to") long to,
                                                                      Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.createMultiLanguageLabelVenueMapForEventLandingPage(from, to);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }


    @PutMapping("/updateEarlyBirdGoalPoints")
    public ResponseDto updateEarlyBirdGoalPoints(Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEarlyBirdGoalPoints();
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Data Fix For import organizers to hub spot")
    @PostMapping("/importOrganizersToHubspot/organizers/{from}/{to}")
    public ResponseDto importOrganizersToHubspot(@PathVariable("from") long from,
                                                                       @PathVariable("to") long to,
                                                                       Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.importOrganizersToHubspot(from, to);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @PostMapping(value = {"/associateOldEventsToOrganizerAndContactsInHubspot/{from}/{to}", "/associateOldEventsToOrganizerAndContactsInHubspot/{eventUrl}"})
    public ResponseDto associateOldEventsToOrganizerAndContactsInHubspot(@PathVariable(required = false) String eventUrl,
                                            @PathVariable(required = false) String from,
                                            @PathVariable(required = false) String to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.associateOldEventsToOrganizerAndContactsInHubspot(eventUrl, from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/associateOldWLEventsToCompaniesInHubspot/WhiteLabel/{from}/{to}")
    public ResponseDto associateOldWLEventsToCompaniesInHubspot(
            @PathVariable long from,
            @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.associateOldWLEventsToCompaniesInHubspot(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/associateOldOrganizerEventsToCompaniesInHubspot/Organizer/{from}/{to}")
    public ResponseDto associateOldOrganizerEventsToCompaniesInHubspot(
            @PathVariable long from,
            @PathVariable long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.associateOldOrganizerEventsToCompaniesInHubspot(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Data Fix For Default Attribute")
    @PostMapping("/updateDefaultAttribute/Event/{from}/{to}")
    public ResponseDto updateDefaultAttributeValue(@PathVariable("from") long from,
                                                                       @PathVariable("to") long to,
                                                                       Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        dataFixService.addDefaultAttributeFlag(from, to);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    //@Operation(description = "Data Fix For advanced color setting flag in virtual event settigns ")
    @PostMapping("/updateAdvancedColorSettingFlag/event/{from}/{to}")
    public ResponseDto updateAdvancedColorSettingFlag(@PathVariable("from") long from,
                                                      @PathVariable("to") long to,
                                                      Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateAdvancedColorSettingFlag(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);

        }
    }

    //@Operation(description = "send magic link emails to event's ticket holders")
    @PostMapping("/sendMagicLinks/event/{eventId}")
    public ResponseDto updateAdvancedColorSettingFlag(@PathVariable("eventId") long eventId,
                                                      Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.sendMagicLinksToTicketHolders(eventId);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);

        }
    }

    @PostMapping("/updateS3ObjectMetadata")
    public ResponseDto updateS3ObjectMetadataForDownload(@RequestParam("bucketName") String bucketName,@RequestParam(name = "startKey", defaultValue = "") String startKey,
                                                         @RequestParam(name="batchSize", defaultValue = "1000") Integer batchSize, @RequestParam(name="totalBatch", defaultValue = "0") long totalBatch,
                                                      @RequestParam(name = "continueBatch", defaultValue="true") boolean continueBatch, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateS3ObjectMetadataForDownload(bucketName, startKey, batchSize, totalBatch, continueBatch);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    //@Operation(description = "Refund Overcharged of AE fees 5% to 2% and if it is paid by buyer")
    @PostMapping("/refund/overChargedAEFee/{eventId}/orderId/{from}/{to}")
    public ResponseDto refundOverChargedAEFee(@PathVariable(name = "eventId") Long eventId, @PathVariable(name = "from") Long from, @PathVariable(name = "to") Long to, Authentication auth) throws StripeException, ApiException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.refundOverChargedAEFeeAmount(eventId, from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Set Auto Send Email Registration status")
    @PostMapping("/registration/approval/email/{from}/{to}")
    public ResponseDto setRegistrationEmailAutoSend(Authentication auth,@PathVariable("from") long from,
                                                    @PathVariable("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.setRegistrationEmailStatus(superAdminUser, from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update Event phone number for match event to given phone number")
    @PostMapping(value = {"/updateEventPhoneNumber/from/{from}/to/{to}/phoneNumber/{phoneNumber}","/updateEventPhoneNumber/eventId/{eventId}/phoneNumber/{phoneNumber}"})
    public ResponseDto updateEventPhoneNumberForSamePhoneNumber(Authentication auth,@PathVariable(value = "from", required = false) Long from,
                                                    @PathVariable(value = "to", required = false) Long to, @PathVariable(value = "eventId", required = false) Long eventId, @PathVariable("phoneNumber") Long phoneNumber) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateEventPhoneNumber(from, to, eventId, phoneNumber);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Update holder data using CSV")
    @PostMapping("/upload-holder-csv/{eventUrl}")
    public ResponseDto updateHolderDataUsingCSV(@RequestParam("File") MultipartFile multiPartFile,
                                                @PathVariable String eventUrl, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateHolderDataUsingCSV(multiPartFile, eventUrl, superAdminUser);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/bulk-update-holder-by-csv/{eventUrl}")
    public List<Object> bulkUpdateHolderInfoByCSV(@PathVariable String eventUrl, Authentication auth,
                                                  @RequestParam("File") MultipartFile multiPartFile){

        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.bulkUpdateHolderDataUsingCSV(multiPartFile, eventUrl, superAdminUser);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }


    @GetMapping("/update-lounge-data-sql/loungeId/{from}/{to}")
    public ResponseDto updateLoungeDataInSqlFromNeptune(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateLoungeDataInSqlFromNeptune(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }

    @PostMapping("/update-beefree-page-data/from/{from}/to/{to}")
    public ResponseDto updateBeeFreePageData(@PathVariable("from") long from, @PathVariable("to") long to,
                                             @RequestParam("originalContent") String originalContent,
                                             @RequestParam("replacedContent") String replacedContent, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateBeeFreePagesData(from, to, originalContent, replacedContent);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    /**
     * This API endpoint is created for a update virtual event settings for button text color configuration.
     * add Opacity 8 % to button text color configuration session background color.
     */
    @PostMapping("/update-virtual-event-settings-color-configuration/from/{from}/to/{to}")
    public ResponseDto updateVirtualEventSettings(@PathVariable("from") long from, @PathVariable("to") long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.updateVirtualEventSettingsForButtonTextColorConfiguration(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    /**
     * This API endpoint is used for sync order with specific integration manually.
     *
     */
    @PostMapping("/sync-integration/event/{eventId}/instance/{instanceName}/fromOrder/{fromOrderId}/toOrder/{toOrderId}")
    public ResponseDto syncOrderWithIntegration(@PathVariable("fromOrderId") long fromOrderId, @PathVariable("toOrderId") long toOrderId, @PathVariable("eventId") Long eventId, @PathVariable("instanceName") String instanceName, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.syncOlderOrderWithSpecificIntegration(eventId,fromOrderId, toOrderId, instanceName);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    //@Operation(description = "Delete Mux assets by Deleted event ids")
    @PostMapping("/addRecordingTabInSessionMenuTabSection/{from}/{to}")
    public Boolean addRecordingTabInSessionMenuTabSection(@PathVariable("from") Long from, @PathVariable("to") Long to, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.addRecordingTabInSessionMenuTabSection(from, to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    @GetMapping(value = "/updateEntitlementsForOldPlanWL")
    public void updateEntitlementsForOldPlanWL(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateEntitlementsForOldPlanWL(from, to);
        }
    }
    @GetMapping(value = "/update-white-label-entitlements")
    public void updateWhiteLabelEntitlements(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateWhiteLabelEntitlements(from, to);
        }
    }
    @PostMapping(value = "/update-white-label-organisers-plans")
    public void updateWhiteLabelOrganisersPlans(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateWhiteLabelOrganisersPlans(from, to);
        }
    }
    @GetMapping(value = "/updateOldPlanEntitlements")
    public void updateEntitlementsForOldPlanOrganisers(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateEntitlementsForOldPlanOrganisers(from,to);
        }
    }
    @PostMapping(value = "/update-organiser-entitlements")
    public void updateOrganiserEntitlements(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateOrganiserEntitlements(from,to);
        }
    }
    //@Operation(description = "update all existing registration approval attribute")
    @GetMapping("/update-registration-approval-attributes")
    public ResponseDto getRegistrationAttributes(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateAllExistingRegistrationApprovalAttribute(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "update all ticket type with assign seating on")
    @GetMapping("/update-ticketing-types-seating-assigned")
    public ResponseDto updateTicketingTypesWithSeatingAssigned(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateTicketingTypesWithSeatingAssigned(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Get User Activity Report by user Id and event Id")
    @GetMapping("/downloadUserActivityReport")
    public void downloadUserActivityReport(HttpServletResponse response, @PathVariable("eventUrl") String eventUrl, Authentication auth, @RequestParam(required = false, defaultValue = "0") Long userId, @RequestParam(required = false) String userActivity) throws IOException {

        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            Event event = roEventService.getEventByURL(eventUrl);
            dataFixService.downloadUserActivityReport(response, userId, event, userActivity);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }

    }
    @GetMapping(value = "/update-organiser-virtual-component-entitlement")
    public void updateOrganiserVirtualComponentEntitlement(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateOrganiserVirtualComponentEntitlement(from, to);
        }
    }

    @GetMapping(value = "/update-wl-virtual-component-entitlement")
    public void updateWlVirtualComponentEntitlement(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateWlVirtualComponentEntitlement(from, to);
        }
    }
    @GetMapping(value = "/update-organiser-subscription-expire-date")
    public void updateOrganiserSubscriptionExpireDate(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateOrganiserSubscriptionExpireDate(from, to);
        }
    }
    @GetMapping(value = "/update-organiser-subscription-status")
    public void updateOrganiserSubscriptionStatus(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateOrganiserSubscriptionStatus(from, to);
        }
    }
    @GetMapping(value = "/update-white-label-subscription-status")
    public void updateWhiteLabelSubscriptionStatus(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateWhiteLabelSubscriptionStatus(from, to);
        }
    }
    @GetMapping(value = "/update-white-label-subscription-expire-date")
    public void updateWhiteLabelSubscriptionExpireDate(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            dataFixService.updateWhiteLabelSubscriptionExpireDate(from, to);
        }
    }
    /**
     * This API endpoint is created for data-fix in existing orders' discount amounts.
     */
    @PostMapping("/update-event-ticket-discount")
    public ResponseDto updateEventTicketDiscount(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateAllExistingEventTicketDiscountValue(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    @GetMapping(value = "/update-add-on-ticketing-type")
    public ResponseDto updateAddOnTicketingTypes(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return dataFixService.updateAddOnTicketingTypes(from, to);
        }
        else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    @GetMapping(value = "/update-add-on-ticketing-type-recurring-event")
    public ResponseDto updateAddOnTicketingTypesForRecurringEvent(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return dataFixService.updateAddOnTicketingTypesForRecurringEvent(from, to);
        }
        else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/update-event-ticket-charge")
    public ResponseDto updateEventTicketCharge(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateAllExistingEventTicketChargeId(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/create-event-ticket-charge")
    public ResponseDto createEventTicketCharge(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.createAllExistingEventTicketChargeId(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(path = {"/white-label-event-usages-report/{whiteLabelURL}"})
    public void listAttendeeOverageByWhitelabel(HttpServletResponse response,
                                                @PathVariable String whiteLabelURL, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            downloadService.getAttendeeOveragesByWhitelabelDataFix(response, whiteLabelURL);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @GetMapping(path = {"/organiser-event-usages-report/{organizerId}"})
    public  void listAttendeeOverageByOrganizer(HttpServletResponse response,
                                                @PathVariable("organizerId") @Parameter(description = "organizer Id") long organizerId, Authentication auth) {
        User user = this.userUtils.getUser(auth);
        boolean isSuperAdminUser = this.roUserService.isSuperAdminUser(user);
        if (!isSuperAdminUser ) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        downloadService.getAttendeeOverageByOrganizerDataFix(response,organizerId);
    }
    @GetMapping(value = "/update-contacts-to-contactsList")
    public ResponseDto updateContactsToContactsList(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to,@RequestParam("listName") String listName) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return dataFixService.updateContactsToContactsList(from, to,user,listName);
        }
        else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }
    @PostMapping("/update-cash-payment-amount")
    public ResponseDto updateCashPaymentAmount(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateAllExistingCasePaymentToTicket(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    /**
     * This API endpoint is created for data-fix to create default custom page for the existing events.
     */
    @PostMapping("/create-default-custom-page-for-existing-events")
    public ResponseDto createDefaultOrderConfirmationPageForTheExistingEvents(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.createDefaultOrderConfirmationPageForTheExistingEvents(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/create-default-confirmation-email-for-existing-events")
    public ResponseDto createDefaultOrderConfirmationEmailForTheExistingEvents(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.createDefaultOrderConfirmationEmailForTheExistingEvents(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/create-default-reminder-email-for-existing-events")
    public ResponseDto createDefaultReminderEmailsForTheExistingEvents(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.createDefaultReminderEmailsForTheExistingEvents(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/update-show-pay-later-message-flag")
    public ResponseDto updateShowPayLaterMessageFlag(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateShowPayLaterMessageFlag(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }


    @PostMapping("/update-order-manager-duplicate-records")
    public ResponseDto updateTicketingOrderManagerForTicketCountFixed(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateTicketingOrderManagerForTicketCountFixed(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/update-wl-stripe-userid-by-whitelabel")
    public ResponseDto updateWlStripeUserIdByWhitelabel(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateWLAStripeUserIdInTFCLForWhiteLabel(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/update-wl-stripe-userid-by-event")
    public ResponseDto updateWlStripeUserIdByEvent(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateWLAStripeUserIdInTFCLByEvent(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @GetMapping(value = "/transfer-profile-to-holder-in-expo-setting")
    public ResponseDto transferProfileToHolderInExpoSetting(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return dataFixService.transferProfileToHolderInExpoSetting(from, to,user);
        }
        else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/attach-addon-with-ticket")
    public ResponseDto attachAddonWithTicket(@RequestParam("File") MultipartFile multiPartFile,
                                                 Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.attachAddonWithTicket(multiPartFile, superAdminUser);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/checkWaitListAvailability/{waitListSettingId}")
    public ResponseDto checkWaitListForGivenId(@PathVariable Long waitListSettingId) {
        waitListService.handleWaitListTriggerForGivenId(waitListSettingId);
        return new ResponseDto(SUCCESS, waitListSettingId+" waitListSettingsId process registered");
    }

    @PostMapping(value = "/add-default-ticket-code-with-event-tickets")
    public ResponseDto addTicketCodeWithExistingEventTickets(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return dataFixService.addTicketCodeWithExistingEventTickets(from, to);
        }
        else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping(value = "/remove-ticket-number-from-search-by-in-kiosk-setting")
    public ResponseDto removeTicketNumberFromSearchByInKioskSetting(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return dataFixService.removeTicketNumberFromSearchByInKioskSetting(from, to);
        }
        else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }



    @PostMapping("/move-cash-payment-to-ticket-transaction")
    public ResponseDto moveAllExistingCasePaymentToEventTicketTransaction(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.moveAllExistingCasePaymentToEventTicketTransaction(from, to);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/updateUserProfileImageByCSV")
    public ResponseDto updateAttendeeProfileImageInUserTable(@RequestParam("File") MultipartFile multiPartFile, Authentication auth) {
        User user = this.userUtils.getUser(auth);
        if(roUserService.isSuperAdminUser(user)){
            dataFixService.updateAttendeeProfileImageInUserTable(multiPartFile);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/create-default-save-a-seat-confirmation-email-for-existing-events")
    public ResponseDto createDefaultSaveASeatOrderConfirmationEmailForTheExistingEvents(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.createDefaultSaveASeatOrderConfirmationEmailForTheExistingEvents(from,to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/update-organiser-engage-email-limit")
    public ResponseDto updateOrganiserEngageEmailLimit(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateOrganiserEngageEmailLimit(from, to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/update-whitelabel-engage-email-limit")
    public ResponseDto updateWhiteLabelEngageEmailLimit(Authentication auth, @RequestParam("from") long from, @RequestParam("to") long to) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateWhiteLabelEngageEmailLimit(from, to);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping("/updateSessionDescriptionAndTracks/{eventUrl}")
    public ResponseDto updateSessionDescriptionAndTracks(@RequestParam("File") MultipartFile multiPartFile,
                                                @PathVariable String eventUrl, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateSessionDescriptionAndTracks(multiPartFile, eventUrl, superAdminUser);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    //@Operation(description = "Update holder email using CSV")
    @PostMapping("/upload-holder-email-csv/{eventUrl}")
    public ResponseDto updateHolderEmailUsingCSV(@RequestParam("File") MultipartFile multiPartFile,
                                                @PathVariable String eventUrl, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.updateHolderEmailUsingCSV(multiPartFile, eventUrl);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/update-attendee-company/from/{from}/to/{to}")
    public ResponseDto updateAttendeeCompany(@PathVariable("from") long from, @PathVariable("to") long to, 
                                                Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
             dataFixService.fetchAttendeeProfileAndUpdateUser(from, to);
             return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/update-attendee-company/bulk/from/{from}/to/{to}")
    public ResponseDto updateAttendeeCompanyBulk(@PathVariable("from") long from, @PathVariable("to") long to, 
                                                Authentication auth, HttpServletRequest request) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.fetchAttendeeProfileAndUpdateUserBulk(from, to, request.getHeader("Authorization"));
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/bulk-update-Buyer-by-csv/{eventUrl}")
    public List<Object> bulkUpdateBuyerInfoByCSV(@PathVariable String eventUrl, Authentication auth, @RequestParam("File") MultipartFile multiPartFile) throws IOException {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            return dataFixService.bulkUpdateBuyerDataUsingCSV(multiPartFile, eventUrl);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }


    @PostMapping("/bulk-update-Buyer-holder-conditional-questions/{eventUrl}")
    public ResponseDto bulkUpdateBuyerHolderInfoConditionalQuestions(@PathVariable String eventUrl,
                                                                     @RequestParam(defaultValue = "0",value = "page") int page, @RequestParam(value = "size", defaultValue = "10") int size, Authentication auth){
            User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
             dataFixService.bulkUpdateBuyerHolderInfoConditionalQuestions(eventUrl,page,size);
             return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/custom-ticket-invoice-default-design/bulk/from/{from}/to/{to}")
    public ResponseDto setDataForOldEventDefaultTicketAndInvoiceCustomDesgin(@PathVariable("from") long from, @PathVariable("to") long to,
                                                                             Authentication auth, HttpServletRequest request) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.setDataForOldEventDefaultTicketAndInvoiceCustomDesign(from, to, superAdminUser);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/bulk-update-waitlist/from/{from}/to/{to}")
    public ResponseDto bulkUpdateWaitListWithPosition(@PathVariable("from") long from, @PathVariable("to") long to,
                                                                             Authentication auth, HttpServletRequest request) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            dataFixService.bulkUpdateWaitListWithPosition(from, to);
            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }
}
