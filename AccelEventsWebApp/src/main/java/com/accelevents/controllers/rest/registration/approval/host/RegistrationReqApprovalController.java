package com.accelevents.controllers.rest.registration.approval.host;

import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.registration.approval.domain.RegistrationAttribute;
import com.accelevents.registration.approval.dto.*;
import com.accelevents.registration.approval.services.RegistrationApprovalEmailService;
import com.accelevents.registration.approval.services.RegistrationAttributeService;
import com.accelevents.registration.approval.services.RegistrationIntroductoryConfigService;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.services.SessionApprovalSettingService;
import com.accelevents.services.StaffService;
import com.accelevents.ticketing.dto.SessionRegistrationRequestBasicDetailDto;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.squareup.square.exceptions.ApiException;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.util.List;

import static com.accelevents.exceptions.ForbiddenException.StaffExceptionMsg.NOT_STAFF_USER;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.getFileName;

@RestController
@RequestMapping({"/rest/host/registrationReq/event/{eventUrl}","/rest/host/event/{eventUrl}/registrationReq"})
@Tag(name = "/rest/host/registrationReq/event/{eventUrl}/")
public class RegistrationReqApprovalController {

    public static final Logger log = LoggerFactory.getLogger(RegistrationReqApprovalController.class);

    @Autowired
    private AuthValidator authValidator;
    @Autowired
    private StaffService staffService;
    @Autowired
    private RegistrationAttributeService registrationAttributeService;
    @Autowired
    private RegistrationRequestsService registrationRequestsService;
    @Autowired
    private RegistrationIntroductoryConfigService registrationIntroductoryConfigService;
    @Autowired
    private RegistrationApprovalEmailService registrationApprovalEmailService;
    @Autowired
    private SessionApprovalSettingService sessionApprovalSettingService;

    //@Operation(description = "get all registration attributes")
    @GetMapping("/registrationAttributes")
    public List<RegistrationAttributeDto> getRegistrationAttributes(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                                                    @RequestParam(name = "RegistrationRequestType", defaultValue = "ATTENDEE") RegistrationAttributeType attributeType,
                                                                    @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                                    Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationAttributeService.getRegistrationAttributes(userEvent.getEvent(), attributeType, recurringEventId);
    }

    //@Operation(description = "Update registration request approval toggle")
    @PutMapping
    public ResponseDto updateRegistrationApproval(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                                  @RequestParam RegistrationRequestType registrationRequestType,
                                                  @RequestParam(value = "isRegistrationApprovalEnabled") boolean isRegistrationApprovalEnabled,
                                                  Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationAttributeService.updateRegistrationApproval(userEvent.getEvent(), registrationRequestType, isRegistrationApprovalEnabled);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @PutMapping("/updateSubmission")
    public ResponseDto updateRegistrationSubmission(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                                  @RequestParam RegistrationRequestType registrationRequestType,
                                                    @RequestParam(value = "isSubmissionEnabled") boolean isSubmissionEnabled,
                                                  Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationAttributeService.updateRegistrationSubmission(userEvent.getEvent(), registrationRequestType, isSubmissionEnabled);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Get registration request approval Enable")
    @GetMapping("/isRegistrationApprovalEnable")
    public boolean isRegistrationApprovalEnable(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                           @RequestParam RegistrationRequestType registrationRequestType,
                                           Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationAttributeService.isRegistrationApprovalEnable(userEvent.getEvent(), registrationRequestType);
    }

    //@Operation(description = "Fetch registration request")
    @GetMapping
    public DataTableResponse getRegistrationRequest(@PathVariable String eventUrl,
                                                    @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                    @RequestParam(name = "RegistrationRequestType", defaultValue = "ATTENDEE") RegistrationRequestType type,
                                                    @RequestParam(required = false, name = "status", defaultValue = "") List<RegistrationRequestStatus> status,
                                                        @Parameter(description = "Pages are zero indexed, thus providing 0 for page will return the first page.")
                                                    @RequestParam(defaultValue = "0") int page,
                                                    @RequestParam(defaultValue = "10") int size,
                                                    @RequestParam(name = "sortColumn", defaultValue = "addedDate") String sortColumn,
                                                    @RequestParam(name = "isAsc", defaultValue = "false") boolean isAsc,
                                                    @RequestParam(required = false) String searchString,
                                                    Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationRequestsService.getRegistrationRequest(userEvent.getEvent(), userEvent.getUser(), recurringEventId, type, status,
                page, size, sortColumn, isAsc, searchString);
    }

    //@Operation(description = "Check if registration request exists")
    @GetMapping("/isRequestExists")
    public boolean isRegistrationRequestExists(@PathVariable String eventUrl,
                                               @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                               @RequestParam(name = "RegistrationRequestType", defaultValue = "ATTENDEE") RegistrationRequestType type,
                                               Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationRequestsService.isRegistrationRequestExists(userEvent.getEvent(), recurringEventId, type);
    }

    //@Operation(description = "Change registration request status")
    @PutMapping("/status")
    public RegistrationRequestApprovalResponseDTO updateRegistrationRequestStatus(@PathVariable String eventUrl, @RequestParam("requestIds") List<Long> requestIds,
                                                                                  @RequestParam(name = "status", defaultValue = "") RegistrationRequestStatus status, Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationRequestsService.updateRegistrationRequest(userEvent.getEvent(), userEvent.getUser(), requestIds, status);
    }

    //@Operation(description = "Change registration request status")
    @PostMapping("/resendCheckoutLink")
    public ResponseDto resendCheckoutLink(@PathVariable String eventUrl,
                                          @RequestParam("requestIds") List<Long> requestIds,
                                          Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.updateRegistrationRequestForResendCheckoutLink(userEvent.getEvent(), userEvent.getUser(), requestIds);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Update Registration Attribute Settings")
    @PutMapping("/updateRegistrationAttribute/{RegistrationRequestType}")
    public ResponseDto updateRegistrationAttributeSettings(@RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                    @RequestBody @Validated List<RegistrationAttributeDto> registrationAttributeDtos,
                                                    @PathVariable("RegistrationRequestType") RegistrationRequestType type,
                                                    @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                    Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationAttributeService.saveRegistrationAttributes(registrationAttributeDtos, type, userEvent.getEvent(), userEvent.getUser(), recurringEventId);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @PostMapping(path = "/regCustomAttribute")
    public ResponseDto addRegCustomAttributes(@RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                              @RequestBody List<RegistrationCustomAttributeDto> customAttributeDtoList,
                                              @Parameter(description = "Event url") @PathVariable String eventUrl,
                                              Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        try {
            registrationAttributeService.addCustomAttributeNew(userEvent.getEvent(), customAttributeDtoList, recurringEventId);
        } catch (NotAcceptableException e) {
            throw e;
        } catch (Exception e) {
            log.info("Error while regCustomAttribute  error {} ", e.getMessage());
            return new ResponseDto(FAIL, FAIL);
        }
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Delete Custom Attribute")
    @DeleteMapping(path = {"/regCustomAttribute/{RegistrationRequestType}/{attributeId}"})
    public ResponseDto deleteRegAttribute(@RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                          @PathVariable Long attributeId,
                                          @PathVariable("RegistrationRequestType") RegistrationRequestType type,
                                          @Parameter(description = "Event url") @PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationAttributeService.deleteRegCustomAttribute(attributeId, recurringEventId, userEvent.getEvent(), type);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Get Register Custom Attribute")
    @GetMapping(path = {"/customAttribute/{attributeId}"})
    public List<RegistrationAttributeDto> getRegCustomAttribute(@PathVariable Long attributeId,
                                                                @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                                Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationAttributeService.getRegisterCustomAttribute(attributeId, userEvent.getEvent());
    }

    //@Operation(description = "Update Registration Custom Attribute")
    @PutMapping(path = {"/regCustomAttribute/{attributeId}"})
    public ResponseDto updateCustomAttribute(@RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                             @PathVariable Long attributeId,
                                             @RequestBody RegistrationCustomAttributeDto customAttributeDto,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl,
                                             Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationAttributeService.updateRegistrationCustomAttribute(attributeId, customAttributeDto, userEvent.getEvent(), recurringEventId);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Update Registration Custom Attribute position")
    @PostMapping("/customAttribute/{customAttributeId}/topId/{topId}/topBottomId/{topBottomId}")
    public ResponseDto updateRegCustomAttributePosition(@RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                                 @PathVariable Long customAttributeId, @PathVariable Long topId,
                                                 @PathVariable Long topBottomId,
                                                 @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                 Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationAttributeService.updateCustomAttributeSequence(customAttributeId, topId, topBottomId, recurringEventId, userEvent.getEvent(), userEvent.getUser());
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Update registration request note")
    @PutMapping("/requestId/{id}")
    public ResponseDto updateRegistrationRequestNotes(@PathVariable String eventUrl, @PathVariable("id") Long id,
                                                      @RequestParam(name = "note", defaultValue = "") String note, Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.updateRegistrationRequestNote(userEvent.getEvent(), userEvent.getUser(), id, note);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Get registration request history")
    @PutMapping("/id/{id}/history")
    public List<RegistrationRequestStatusChangeDto> getRegistrationRequestHistory(@PathVariable String eventUrl, @PathVariable("id") Long id, Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationRequestsService.getRegistrationRequestHistory(userEvent.getEvent(), id);
    }

    //@Operation(description = "Delete registration request")
    @DeleteMapping
    public ResponseDto deleteRegistrationRequest(@PathVariable String eventUrl, @RequestParam("requestIds") List<Long> requestIds, Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.deleteRegistrationRequest(userEvent.getEvent(), userEvent.getUser(), requestIds);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Download registration request list in CSV format")
    @GetMapping("/download/CSV")
    public void downloadRegistrationRequests(HttpServletResponse response, Authentication auth,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl,
                                             @RequestParam("requestIds") List<Long> requestIds) throws IOException, JAXBException {

        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        CommonUtil.prepareDownloadableResponseHeader(response, "AttendeeRegistrationRequests", CONTENT_CSV);
        registrationRequestsService.downloadRegistrationRequests(userEvent.getEvent(), response.getWriter(), requestIds);
    }

    //@Operation(description = "Update Details of Enabled Introductory Page")
    @PutMapping("/introductoryPage")
    public ResponseDto updateIntroductoryPage(@PathVariable String eventUrl, Authentication auth,
                                                                 @RequestBody RegistrationIntroductoryConfigDto registrationIntroductoryConfigDto) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationIntroductoryConfigService.updateIntroductoryPageDetails(registrationIntroductoryConfigDto, userEvent.getEvent());
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Get Introductory Page Response")
    @GetMapping("/introductoryConfig")
    public RegistrationIntroductoryConfigDto getIntroductoryPageResponse(@PathVariable String eventUrl, Authentication auth,
                                                                      @RequestParam RegistrationRequestType type) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationIntroductoryConfigService.getIntroductoryConfig(userEvent.getEvent(), type);
    }

    //@Operation(description = "Enable Introductory Page Toggle")
    @PutMapping("/enableIntroductoryToggle/{introductoryToggle}")
    public ResponseDto updateIntroductoryPageToggle(@PathVariable String eventUrl, Authentication auth,
                                                    @RequestParam RegistrationRequestType type,
                                                    @PathVariable("introductoryToggle") boolean introductoryToggle){
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationIntroductoryConfigService.updateIntroductoryPageToggle(userEvent.getEvent(), introductoryToggle, type);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Send Mail to user for registration")
    @PutMapping("/registrationRequestId/{requestId}")
    public ResponseDto sendRegistrationRequestStatusMail(@PathVariable String eventUrl, Authentication auth,
                                                         @PathVariable(value = "requestId") Long requestId){
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationApprovalEmailService.sendRegistrationMail(userEvent.getEvent(), userEvent.getUser(), requestId);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "get registrant attributes data by request Id")
    @GetMapping(path = { "/registration/requestId/{requestId}" })
    public HolderDisplayAttributesDto getRegistrantDetails(@PathVariable long requestId,
                                                           @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                           @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                           Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        try {
            return registrationRequestsService.getRegistrantDetails(requestId, recurringEventId, userEvent.getEvent());
        } catch (JAXBException e) {
            throw new NotAcceptableException(e);
        }
    }

    @PutMapping(path = { "/registration/requestId/{requestId}" })
    public ResponseDto updateRegistrantPurchaserDetails(@PathVariable long requestId,
                                                           @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                           @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                           @RequestBody @Validated AttendeeAttributeValueDto attendeeAttributes,
                                                           Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        try {

            registrationRequestsService.updateRegistrantPurchaserDetails(requestId, attendeeAttributes, userEvent.getEvent(), userEvent.getUser(), recurringEventId);
            return new ResponseDto(SUCCESS, SUCCESS);

        } catch (Exception e) {
            throw new NotAcceptableException(e);
        }
    }

    //@Operation(description = "Resend ticket checkout mail to registration request")
    @PostMapping("/registration/requestId/{requestId}/resendCheckoutLink")
    public ResponseDto resendCheckoutLink(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                          @PathVariable(value = "requestId") Long requestId,
                                          Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.resendCheckoutLink(userEvent.getEvent(), requestId);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "create or update holder attribute mapping for the registration approval")
    @PostMapping("/registration/map-holder-attribute-with-registration-request")
    public ResponseDto createOrUpdateHolderAttributeMapping(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                         @RequestBody RegistrationApprovalFieldHolderAttributeMappingDto mappingOfHolderAttributeDtos,
                                          Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.createOrUpdateHolderAttributeMapping(userEvent.getEvent(),mappingOfHolderAttributeDtos);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "approve registration request and create order ")
    @PostMapping("/registration/approve-registration-request-and-create-order/{requestId}")
    public ResponseDto approveRegistrationRequestAndCreateOrder(@Parameter(description = "Event url") @PathVariable String eventUrl,@PathVariable(value = "requestId") Long requestId,
                                                                              @RequestParam(required = false, defaultValue = "false") boolean isAnyPaidTicket,
                                                                              @RequestParam(required = false, defaultValue = STRING_EMPTY) String couponCode,
                                                                              Authentication auth){
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.approveRegistrationRequestAndCreateOrder(userEvent.getUser(),userEvent.getEvent(),requestId, isAnyPaidTicket, couponCode);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @GetMapping(path = "/get-all-dynamic-merge-tags/{approvalType}")
    public List<NameValueDto> getAllDynamicMergeTagsListForApproval(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                    @PathVariable("approvalType") ConfirmationApprovalType approvalType,
                                                                    @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                                    Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return registrationRequestsService.getAllDynamicMergeTagsListForApproval(userEvent.getEvent(),recurringEventId, approvalType);
    }

    @PutMapping(path ="/speaker-settings")
    public ResponseDto updateEventSpeakerSettings(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                                   @RequestBody SessionApprovalSettingDto sessionApprovalSettingDto,
                                                   Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.updateEventSpeakerSettings(userEvent.getEvent(),userEvent.getUser(), sessionApprovalSettingDto);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @GetMapping(path = "/session-form-settings")
    public SessionApprovalSettingDto getSessionDetails(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                                    Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationRequestsService.getSessionFormSettings(userEvent.getEvent());
    }

    @DeleteMapping(path = "/requestId/{id}/speaker/{email}")
    public ResponseDto deleteRegistrationRequest(@PathVariable String eventUrl,
                                                 @PathVariable("id") Long requestId, @PathVariable("email") String email ,Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.deleteSpeakerFromRegistration(requestId, email,userEvent.getEvent(), userEvent.getUser());
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @PostMapping("/registration-request/{requestId}/speaker/{email}")
    public ResponseDto sendRegistrationRequestStatusMailToSpeaker(@PathVariable String eventUrl, Authentication auth,
                                                         @PathVariable(value = "requestId") Long requestId,
                                                         @PathVariable(value = "email") String email){
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationApprovalEmailService.sendRegistrationMailToSpeaker(userEvent.getEvent(), userEvent.getUser(), requestId, email);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @GetMapping(path = "/session-request-basic-details")
    public List<SessionRegistrationRequestBasicDetailDto> getSessionRegistrationRequestBasicDetails(@PathVariable String eventUrl, Authentication auth){
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return  registrationRequestsService.getSessionRegistrationRequestBasicDetails(userEvent.getEvent());
    }

    @GetMapping("/speaker-details-attribute")
    public List<RegistrationAttribute> getSpeakerDetailsRegistrationRequestAttribute(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                                                                     Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        return registrationAttributeService.getSpeakerDetailsRegistrationRequestAttribute(userEvent.getEvent());
    }

    @GetMapping("/download/CSV/{area}")
    public void downloadAllRegistrationRequest(HttpServletResponse response, Authentication auth,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl,
                                             @PathVariable("area") AnalyticsArea area) throws IOException {

        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        CommonUtil.prepareDownloadableResponseHeader(response,  getFileName(area.name()) + "RegistrationRequests", CONTENT_CSV);
        registrationRequestsService.downloadAllRegistrationRequests(userEvent.getEvent(),userEvent.getUser(), response.getWriter(),area);
    }

    @GetMapping("/download-attendee-requests-holder-buyer/CSV")
    public void downloadAttendeeRequestsHolderBuyerCSV(HttpServletResponse response, Authentication auth,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl) throws IOException {

        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        CommonUtil.prepareDownloadableResponseHeader(response, "AttendeeRegistrationRequests", CONTENT_CSV);
        registrationRequestsService.downloadAttendeeRequestHolderBuyerCSV(userEvent.getEvent(), userEvent.getUser(),response.getWriter());
    }

    //remove discount code from registration request
    @PutMapping("/remove-discount-code/{requestId}")
    public ResponseDto removeDiscountCodeFromRegistrationRequest(@PathVariable String eventUrl,
                                                                 @PathVariable Long requestId,
                                                                 Authentication auth) {
        UserEvent userEvent = authValidator.hasHostAccessForEventOrThrowError(eventUrl, auth);
        registrationRequestsService.removeDiscountCodeFromRegistrationRequest(requestId, userEvent.getEvent(), userEvent.getUser().getUserId());
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }


}