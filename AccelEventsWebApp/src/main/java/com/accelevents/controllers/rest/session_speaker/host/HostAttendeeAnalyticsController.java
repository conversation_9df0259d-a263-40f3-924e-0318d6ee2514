package com.accelevents.controllers.rest.session_speaker.host;

import com.accelevents.common.dto.AttendeeAnalyticStatusDTO;
import com.accelevents.common.dto.AttendeeAnalyticsProfileDTO;
import com.accelevents.common.dto.AttendeeCheckInAnalyticsDto;
import com.accelevents.common.dto.MagicLinkResponseDto;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.dto.AttendeeAnalyticsPageSizeSearchObj;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.ResponseDto;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.AttendeeAnalyticsService;
import com.accelevents.services.DownloadService;
import com.accelevents.ro.analytics.ROAttendeeAnalyticsService;
import com.accelevents.services.UserService;
import com.accelevents.session_speakers.dto.AttendeeSession;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.SessionThirdPartyService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.session_speakers.services.VirtualEventAnalyticsService;
import com.accelevents.ticketing.dto.AttendeeBulkUpdateDTO;
import com.accelevents.ticketing.dto.AttendeeBulkUpdateResponseDTO;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.UserUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping({"/rest/host/event/{eventUrl}/attendee","/rest/host/attendee"})
@Tag(name = "/rest/host/attendee")
public class HostAttendeeAnalyticsController {

	@Autowired
	private AuthValidator authValidator;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private UserUtils userUtils;
    @Autowired
    private ROEventService roEventService;
	@Autowired
	private SessionService sessionService;
	@Autowired
	private UserSessionService userSessionService;
	@Autowired
	private SessionThirdPartyService sessionThirdPartyService;
	@Autowired
	private DownloadService downloadService;
	@Autowired
	private VirtualEventAnalyticsService virtualEventAnalyticsService;
	@Autowired
    private UserService userService;
	@Autowired
	private AttendeeAnalyticsService attendeeAnalyticsService;
    @Autowired
    private ROAttendeeAnalyticsService roAttendeeAnalyticsService;

//	//@Operation(description = "Get Attendee checkIn detail", authorizations = @Authorization(value = "access_token"),
//			response = AttendeeCheckInAnalyticsDto.class, produces = APPLICATION_JSON, consumes = APPLICATION_JSON)
	@GetMapping(value = "/attendeeCheckInAnalytics")
	public AttendeeCheckInAnalyticsDto getAttendeeCheckInAnalytics(Authentication auth,
                                                                   @RequestParam(value = "date", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date date,
                                                                   @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
																   @RequestParam(value = "ticketTypeId", required = false) Long ticketTypeId) {
		UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
		return this.virtualEventAnalyticsService.getAttendeeCheckInAnalyticsForEvent(userEvent.getEvent(),date, ticketTypeId);
	}

	//@Operation(description = "get all sessions for attendee")
	@GetMapping("/user/{id}")
	public List<AttendeeSession> getSessionsAttendee(
			@PathVariable("id") Long userId,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            @RequestParam(value = "fetchEngagement", required = false, defaultValue = "false") boolean fetchEngagement,
            @RequestParam(required = false) String searchString,
            Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return userSessionService.findAttendeeSessionsByUserId(userEvent.getEvent(), userId, fetchEngagement, searchString);
    }

	//@Operation(description = "Download all sessions for attendee")
	@GetMapping("/user/{id}/download/CSV")
	public void getSessionsAttendeeCSV(
			@PathVariable("id") Long userId,
			HttpServletResponse response,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
			Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        String fileName = "All Contact";
        CommonUtil.prepareDownloadableResponseHeader(response, fileName, Constants.CONTENT_CSV);
        List<AttendeeSession> attendeeSessions = userSessionService.findAttendeeSessionsByUserId(userEvent.getEvent(), userId, true, null);
        downloadService.downloadAttendeeSessions(response.getWriter(), attendeeSessions,userId);
    }

	//@Operation(description = "Download Detailed Leaderboard score")
	@GetMapping("/leaderboard/download/CSV")
	@Parameter(name = "Authorization", description = "Authorization", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
	public void downloadContactCSV(HttpServletResponse response, Authentication auth,
                                   @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl)
			throws IOException {
		UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
		String fileName = "Leaderboard";
        CommonUtil.prepareDownloadableResponseHeader(response, fileName, Constants.CONTENT_CSV);
		downloadService.downloadLeaderboardDetailDataCSV(response.getWriter(), userEvent.getEvent());
	}

	//@Operation(description = "Download All Attendees with All Session")
	@GetMapping("/download/CSV")
	public ResponseDto getAllSessionsAllAttendeeCSV(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
			Authentication auth) throws IOException {
		UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
//		String fileName = "All Attendees Sessions.csv";
//		response.setContentType(Constants.CONTENT_CSV);
//		String headerKey = Constants.CONTENT_DISPOSITION;
//		String headerValue = String.format(Constants.ATTACHMENT_FILENAME, fileName);
//		response.setHeader(headerKey, headerValue);
//        response.setCharacterEncoding("UTF-16");
		downloadService.downloadAllAttendeeSessionsInfoCSVAsync(userEvent.getEvent(), userEvent.getUser());

        return new ResponseDto(Constants.SUCCESS, "CSV file preparation started");
	}

    //@Operation(description = "Returns all the attendees of event")
    @PostMapping(value = "/analytics")
    public DataTableResponse getAllAttendees(@RequestBody @Validated AttendeeAnalyticsPageSizeSearchObj attendeeAnalyticsPageSizeSearchObj,
                                             @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                             Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        if (attendeeAnalyticsPageSizeSearchObj.getDisplayViewId()!=null)
        {
            return roAttendeeAnalyticsService.findAllAttendeeByDisplayViewId(userEvent.getEvent(), userEvent.getUser(), attendeeAnalyticsPageSizeSearchObj);
        }
        return roAttendeeAnalyticsService.getAllAttendeeByEvent(userEvent.getEvent(), userEvent.getUser(), attendeeAnalyticsPageSizeSearchObj);

    }

    //@ApiOperation(value = "Returns all the attendees of event")
    @PostMapping(value = "/analytics/bulk-update")
    public List<AttendeeBulkUpdateResponseDTO> bulkAttendeeUpdate(@RequestBody @Validated AttendeeBulkUpdateDTO bulkAttendeeUpdate,
                                                                  @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                  Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return attendeeAnalyticsService.bulkAttendeeUpdate(userEvent.getEvent(), userEvent.getUser(), bulkAttendeeUpdate);
    }

	//@ApiOperation(value = "Returns attendee analytics profile information", response = AttendeeAnalyticsProfileDTO.class)
	//@Operation(description = "Returns attendee analytics profile information")
	@GetMapping(value = "/{userId}/profile")
	public AttendeeAnalyticsProfileDTO getAttendeeAnalyticsProfile(@PathVariable("userId") Long userId,
                                                                   @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                                   @RequestParam(name = "eventTicketId", required = false, defaultValue = "0") Long eventTicketId,
														  Authentication auth) {
        User user = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        if(roStaffService.hasStaffAccessForEvent(user, event)){
            return attendeeAnalyticsService.getAttendeeAnalyticsProfile(event,userId,eventTicketId);
        }else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_STAFF_OR_ADMIN);
        }
	}

    //@Operation(description = "Returns attendee count by ticket status")
    @GetMapping(value = "/attendeesCountByStatus")
    public List<AttendeeAnalyticStatusDTO> attendeesCountByStatus(Authentication auth,
                                                                  @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return attendeeAnalyticsService.getAttendeesCountByEventByTicketStatus(userEvent.getEvent());
    }

    //@Operation(description = "download attendee analytics CSV for custom column selection")
    @GetMapping ("/analytics/download/CSV")
    public void downloadAttendeeAnalyticsCSV(Authentication auth,
                                          HttpServletResponse response, @RequestParam(value = "displayViewId",required = false) Long displayViewId,
                                             @RequestParam(value = "searchString",required = false) String searchString,
                                          @PathVariable(required = false) String eventUrl) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        String fileName = "Attendee Analytics";
        CommonUtil.prepareDownloadableResponseHeader(response, fileName, Constants.CONTENT_CSV);
        downloadService.downloadAttendeeAnalyticsCSV(response.getWriter(),userEvent.getEvent(),userEvent.getUser(),displayViewId,searchString);
    }


    @GetMapping("/magic-link")
    public String getMagicLinkOfUser(Authentication auth,
                                             @PathVariable(required = false) String eventUrl,
                                             @RequestParam(value = "email") String email) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return userService.getMagicLinkOfUser(userEvent.getEvent(), userEvent.getUser(), email);
    }

    @PostMapping("/expire-magic-link")
    public MagicLinkResponseDto expireMagicLinkOfUser(Authentication auth,
                                                      @PathVariable(required = false) String eventUrl,
                                                      @RequestParam(value = "email") String email,
                                                      @RequestParam(value = "generateNewLink", required = false, defaultValue = "false") boolean generateNewLink) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return userService.expireOldAndGenerateNewMagicLink(userEvent.getEvent(), userEvent.getUser(), email, generateNewLink);
    }

}
