package com.accelevents.controllers.rest.session_speaker.host;

import com.accelevents.common.dto.CECriteriaBasicDTO;
import com.accelevents.continuing.ed.ContinuingEdConstants.ContinuingEdStatus;
import com.accelevents.continuing.ed.dto.ContinuingEdProgressDTO;
import com.accelevents.continuing.ed.dto.ContinuingEdStatisticsDTO;
import com.accelevents.continuing.ed.dto.ContinuingEdUserDTO;
import com.accelevents.continuing.ed.service.ContinuingEdService;
import com.accelevents.controllers.rest.RestAuthValidator;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.EventCECriteriaDTO;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.DownloadService;
import com.accelevents.services.EventCECriteriaService;
import com.accelevents.services.StaffService;
import com.accelevents.services.UserService;
import com.accelevents.utils.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static com.accelevents.utils.Constants.CONTENT_CSV;

@RestController
@RequestMapping({"/rest/host/event/{eventUrl}/continuing-education", "/rest/host/continuing-education"})
public class ContinuingEdController {

    @Autowired
    private AuthValidator authValidator;
    @Autowired
    private RestAuthValidator restAuthValidator;
    @Autowired
    private ContinuingEdService continuingEdService;
    @Autowired
    private DownloadService downloadService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private UserService userService;
    @Autowired
    private EventCECriteriaService eventCECriteriaService;
    @Autowired
    private StaffService staffService;

    @PostMapping("criteria/{criteriaId}")
    public DataTableResponse getContinuingEdAnalyticsData(@PathVariable("criteriaId") Long criteriaId,
                                                          @PathVariable(required = false) String eventUrl,
                                                          @RequestParam(required = false, defaultValue = "ALL") ContinuingEdStatus status,
                                                          @RequestBody PageSizeSearchObj pageSizeSearchObj,
                                                          Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return continuingEdService.getContinuingEdAnalytics(criteriaId, status, userEvent.getEvent(), userEvent.getUser(), pageSizeSearchObj);
    }

    @GetMapping("criteria/{criteriaId}/user/{userId}")
    public List<ContinuingEdProgressDTO> getContinuingEdAnalyticsDataByUser(@PathVariable("criteriaId") Long criteriaId,
                                                                            @PathVariable("userId") Long userId,
                                                                            @PathVariable(required = false) String eventUrl,
                                                                            Authentication auth) {
        User user = userService.findByUserId(userId);
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return continuingEdService.getContinuingEdAnalyticsByUser(criteriaId, user, userEvent.getEvent());
    }


    @GetMapping("statistics")
    public ContinuingEdStatisticsDTO getContinuingEdStatisticsAnalytics(@PathVariable(required = false) String eventUrl,
                                                                        Authentication auth) {

        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return continuingEdService.getContinuingEdStatistics(userEvent.getEvent(), userEvent.getUser());
    }

    @PostMapping("criteria/{criteriaId}/download/CSV")
    public void downloadContinuingEdAnalyticsCSV(@PathVariable("criteriaId") Long criteriaId,
                                                 @PathVariable(required = false) String eventUrl,
                                                 @RequestParam(required = false, defaultValue = "ALL") ContinuingEdStatus status,
                                                 @RequestBody PageSizeSearchObj pageSizeSearchObj,
                                                 HttpServletResponse response,
                                                 Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        CommonUtil.prepareDownloadableResponseHeader(response, "Continuing Ed Analytics", CONTENT_CSV);
        downloadService.downloadContinuingEdAnalyticsCSV(response.getWriter(), criteriaId, status, userEvent.getUser(), userEvent.getEvent(), pageSizeSearchObj);
    }


    @GetMapping("criteria/{criteriaId}/user/{userId}/download/CSV")
    public void downloadContinuingEdUserAnalyticsCSV(@PathVariable("criteriaId") Long criteriaId,
                                                     @PathVariable("userId") Long userId,
                                                     @PathVariable(required = false) String eventUrl,
                                                     @RequestParam(required = false, defaultValue = "-1") Long trackId,
                                                     HttpServletResponse response,
                                                     Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        User user = userService.findByUserId(userId);
        CommonUtil.prepareDownloadableResponseHeader(response, "Continuing Ed Analytics User", CONTENT_CSV);
        downloadService.downloadContinuingEdUserAnalyticsCSV(response.getWriter(), criteriaId, user, userEvent.getEvent());
    }

    @GetMapping("criteria/{criteriaId}/user/{userId}/download/PDF")
    public void downloadContinuingEdUserAnalyticsPDF(@PathVariable("criteriaId") Long criteriaId,
                                                     @PathVariable("userId") Long userId,
                                                     @PathVariable(required = false) String eventUrl,
                                                     HttpServletResponse response,
                                                     Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
            User user = userService.findByUserId(userId);
            downloadService.downloadContinuingEdUserAnalyticsPDF( criteriaId, user, userEvent.getEvent(),response);
    }

    @GetMapping("criteria/{criteriaId}/eligible")
    public List<ContinuingEdUserDTO> getEligibleUserByChallenge(@PathVariable("criteriaId") Long criteriaId,
                                                                @PathVariable(required = false) String eventUrl,
                                                                Authentication auth) {
        User user = authValidator.authUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        return continuingEdService.getEligibleUserByChallenge(criteriaId, event, user);
    }


    @GetMapping("/user/{userId}/criteria")
    public List<EventCECriteriaDTO> getChallengeByUser(@PathVariable("userId") Long userId,
                                                       @PathVariable("eventUrl") String eventUrl,
                                                       Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        User user = userService.findByUserId(userId);
        Event event = userEvent.getEvent();
        return eventCECriteriaService.getCriteriaDTOsByUser(user, event);
    }


    @GetMapping("/user/{userId}/completed-criteria")
    public List<CECriteriaBasicDTO> getCompletedChallengesOfUser(@PathVariable("userId") Long userId,
                                                                                @PathVariable("eventUrl") String eventUrl,
                                                                                Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        User user = userService.findByUserId(userId);
        Event event = userEvent.getEvent();
        return continuingEdService.getCompletedCriteriaOfUser(user, event); 
    }

}
