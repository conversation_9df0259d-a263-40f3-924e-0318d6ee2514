package com.accelevents.controllers.rest.session_speaker.back_stage;

import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.session_speakers.services.SessionBroadCastService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Optional;

@RestController
@RequestMapping("/rest/webhook/session")
@Tag(name = "/rest/webhook/session")
public class SessionWebHookController {

    @Autowired
    SessionRepoService sessionRepoService;

    @Autowired
    private SessionBroadCastService sessionBroadCastService;

    @Autowired
    private UserSessionService userSessionService;

    private static final Logger logger = LoggerFactory.getLogger(SessionWebHookController.class);

    @PostMapping("/restrict")
    public void preventAccessOfSession(@RequestBody String body) {
        logger.info("SessionWebhookToPreventOutsideAccess{}", body);
        JSONObject jsonBody = new JSONObject(body);
        String streamUrl = jsonBody.getString("streamUrl");
        int numberOfViewer = jsonBody.getInt("numberOfViewer");

        Optional<Session> optionalSession = sessionRepoService.findByStreamUrl(streamUrl);
        if (optionalSession.isPresent()) {
            Session session = optionalSession.get();
            int numberOfRegisteredUser = userSessionService.countRegisteredBySessionId(session.getId());

            if (isSessionLive(session)) {
                if (numberOfViewer > numberOfRegisteredUser * 1.25) {
                    stopMux(session);
                    logger.info("Number of viewer is greater than register user.");
                }
            } else {
                stopMux(session);
                logger.info("Session is not live.");
            }
        }
    }

    private void stopMux(Session session) {
        sessionBroadCastService.stopBroadCast(session.getId(), String.valueOf(session.getId()), session.getEvent(), User.dummyUser("MUX", Constants.USER));
    }

    private boolean isSessionLive(Session session) {
        if(session.getStartTime() == null || session.getEndTime() == null) return false;

        Date startDate = session.getStartTime();
        Date endDate = DateUtils.addMinutes(session.getEndTime(), 30);

        return startDate.after(com.accelevents.utils.DateUtils.getCurrentDate())
                && com.accelevents.utils.DateUtils.getCurrentDate().before(endDate);
    }
}
