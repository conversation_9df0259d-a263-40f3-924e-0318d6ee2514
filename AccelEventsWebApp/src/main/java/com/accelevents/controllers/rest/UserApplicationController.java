package com.accelevents.controllers.rest;


import com.accelevents.common.dto.UserApplicationDto;
import com.accelevents.domain.User;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.utils.UserUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.accelevents.utils.Constants.NOT_AUTHORIZE;

@RestController
@RequestMapping("rest/u/application")
@Tag(name = "rest/u/application")
public class UserApplicationController {

    @Autowired
    private UserUtils userUtils;

    @Autowired
    private RegistrationRequestsService registrationRequestsService;

    @GetMapping
    public List<UserApplicationDto> userTicketActiveOrders(Authentication auth) {
        User user = this.userUtils.getUser(auth);
        if (user != null) {
            return registrationRequestsService.getRegistrationApplicationByUser(user);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }
}
