package com.accelevents.controllers.rest.whitelabel;

import com.accelevents.billing.chargebee.dto.BillingSummerDto;
import com.accelevents.common.dto.OrganizerBasicDto;
import com.accelevents.common.dto.OrganizerDto;
import com.accelevents.common.dto.WhiteLabelDto;
import com.accelevents.controllers.rest.RestAuthValidator;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.HubspotCustomEventType;
import com.accelevents.dto.*;
import com.accelevents.dto.whitelabel.WhiteLabelSecuritySettingDto;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.exceptions.ForbiddenException.UserForbiddenExceptionMsg;
import com.accelevents.hubspot.service.HubspotContactService;
import com.accelevents.hubspot.service.HubspotEventService;
import com.accelevents.repositories.EventRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.security.tokenstore.TokenStoreService;
import com.accelevents.services.*;
import com.accelevents.spreedly.dto.gateways.AuthMode;
import com.accelevents.spreedly.dto.gateways.GatewayRequestDto;
import com.accelevents.ticketing.dto.EntitlementsAvailabilityDTO;
import com.accelevents.utils.*;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

import static com.accelevents.utils.ApiMessages.EVENT_STATUS_DESC;
import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping("/rest/whiteLabelURL/{whiteLabelURL}")
@Tag(name = "/rest/whiteLabelURL/{whiteLabelURL}")
public class WhiteLabelController {

	private static final Logger log = LoggerFactory.getLogger(WhiteLabelController.class);

	@Autowired
	private ROStaffService roStaffService;
	@Autowired
	private WhiteLabelService whiteLabelService;
	@Autowired
	private EventService eventService;
    @Autowired
    private ROEventService roEventService;
	@Autowired
	private UserUtils userUtils;
    @Autowired
    private TokenStoreUtils tokenStoreUtils;
	@Autowired
	private EventUtils eventUtils;
    @Autowired
    private WhitelabelUtils whitelabelUtils;
	@Autowired
	private UserService userService;
    @Autowired
    private ROUserService roUserService;

    @Autowired
    private OrganizerService organizerService;

    @Autowired
    private RestAuthValidator restAuthValidator;

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private TokenStoreService tokenStoreService;
    @Autowired
    private HubspotEventService hubspotEventService;

    @Autowired
    private HubspotContactService hubspotContactService;
    @Autowired
    private ApiKeyService apiKeyService;
    @Autowired
    private PayFlowConfigService payFlowConfigService;

    private final static String WHITE_LABEL_SETTINGS = "white label settings for: {}";

	//@Operation(description = "WhiteLabel details")
//	////@ApiResponses(value = {
//			//@ApiResponse(code = 200, message = "Successfully retrieved white labels details."),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping(path = { "" })
	public WhiteLabelDto getEventDataWithDesignDetails(@PathVariable String whiteLabelURL) {
		return whiteLabelService.getWhiteLabelDetailDto(whiteLabelURL);
	}
	
	//@Operation(description = "This will set event for user so you can access this event from event host page")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PostMapping("/setEvent/{eventId}")
	public ResponseDto setEvent(@PathVariable String whiteLabelURL, @PathVariable Long eventId, Authentication auth,
			HttpServletRequest httpRequest) {
		new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
		Event event = roEventService.getEventById(eventId);
		log.info("Redirecting White Label Admin to: {}" , event.getEventURL());
		String authToken = httpRequest.getHeader(Authorization);
		eventUtils.setEvent(auth, event, authToken);
		return new ResponseDto(SUCCESS, SUCCESS);
	}

    //@Operation(description = "list of past event for white lable")
//    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping("/events/{status:active|past|true|false}")
    public DataTableResponse pastWhiteLabelEvents(@PathVariable String whiteLabelURL,
                                                    @Parameter(description = "Pages are zero indexed, thus providing 0 for page will return the first page.") @RequestParam(defaultValue = "0") int page,
                                                    @Parameter(description = EVENT_STATUS_DESC) @PathVariable String status,
                                                    @RequestParam(defaultValue = "10") int size,
                                                    @RequestParam(value = "search", required = false) String search,
                                                   @RequestParam(name = "sortColumn", defaultValue = "event_end_date") String sortColumn,
                                                   @RequestParam(name = "isAsc", defaultValue = "false") boolean isAsc,
                                                    Authentication auth) {
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        User user = restAuthValidator.authUser(auth);
        roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(user, whiteLabel);
        if(!roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(user, whiteLabel)){
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_VALID_WHITELABEL_USER);
        }
        //TODO: Remove true and false support once API stop using it, only allowed values should be active or past APIGW_UN //NOSONAR
        Boolean isPastEvent = null;
        if (StringUtils.equals(status, "true") || StringUtils.equals(status, "past")) {
            isPastEvent = Boolean.TRUE;
        } else if (StringUtils.equals(status, "false") || StringUtils.equals(status, "active")) {
            isPastEvent = Boolean.FALSE;
        }
        // now pass sort & direction through to the service
        return whiteLabelService.getWhiteLabelEventDetails(
                whiteLabel,
                isPastEvent,new PageSizeSearchObj(page,size,search,sortColumn, isAsc ? Sort.Direction.ASC : Sort.Direction.DESC)

        );
    }

	//@Operation(description = "get setting detail")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping("/settings")
	public WhiteLabelSettingDto whiteLabelSettings(@PathVariable String whiteLabelURL, Authentication auth) {
		log.info(WHITE_LABEL_SETTINGS , whiteLabelURL);
		User superAdminUser = this.userUtils.getUser(auth);
		if (this.roUserService.isSuperAdminUser(superAdminUser)) {
			return whiteLabelService.getWhiteLabelSetting(whiteLabelURL, true);
		} else {
			new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
			return whiteLabelService.getWhiteLabelSetting(whiteLabelURL, false);
		}
	}

	//@Operation(description = "save setting")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PutMapping(value = "/settings")
	public ResponseDto updateSettings(@PathVariable String whiteLabelURL,
			@RequestBody WhiteLabelSuperAdminSettingsDTO whiteLabelSuperAdminSettingsDTO, Authentication auth) {

		User user = this.userUtils.getUser(auth);
		AuthenticateWhiteLabel authWL = new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
		boolean isSuperAdminUser = this.roUserService.isSuperAdminUser(user);
        log.info("update wl settings by user {} | isSuperAdminUser {} | whiteLabelSuperAdminSettingsDTO {}", user.getUserId(), isSuperAdminUser, whiteLabelSuperAdminSettingsDTO);
		if (isSuperAdminUser || authWL!=null) {
			whiteLabelService.saveSettings(whiteLabelSuperAdminSettingsDTO,isSuperAdminUser,user);
		}
		return new ResponseDto(SUCCESS, SUCCESS);
	}

    @GetMapping("/captcha")
    public CaptchaDto getWhiteLabelCaptchaDetail(@PathVariable String whiteLabelURL) {
        return whiteLabelService.getWhiteLabelCaptchaDetail(whiteLabelURL);
    }


	//@Operation(description = "create new white label event")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PutMapping(value = "/create/whiteLabelEvent")
	public ResponseEventDto createWhiteLabelEvent(@PathVariable String whiteLabelURL, Authentication auth, HttpServletRequest httpRequest,
                                             @RequestParam(required = false) String startTime, @RequestParam(required = false) String timeZone) {
		AuthenticateWhiteLabel authWL = new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
		Event event = whiteLabelService.addNewEvent(authWL.getWhiteLabel(), authWL.getWhiteLabelUser());
        eventService.saveEventStartTimeAsSystemTime(startTime, timeZone, event);
		String authToken = httpRequest.getHeader(Authorization);
		this.eventUtils.setEvent(auth, event, authToken);
		this.tokenStoreUtils.updateUser(auth, authWL.getWhiteLabelUser(), authToken);
        hubspotContactService.asyncAddContactToCustomEventCreated(authWL.getWhiteLabelUser(),event.getEventId(), ENTERPRISE_PAGE,HubspotCustomEventType.EVENT_CREATED);
        User user = this.userUtils.getUser(auth);
        eventService.addEventCreatorContactWithLabelForEventToHubspot(event, user);
        eventService.addContactsWithLabelForEventToHubspot(event, user);
		return new ResponseEventDto(SUCCESS, SUCCESS, event.getEventId(), event.getEventURL());
	}


	@PostMapping(value = "/stripeConnect")
	public String authorizationStripeWL(@PathVariable String whiteLabelURL,Authentication auth, HttpServletRequest httpRequest) {
		log.info(WHITE_LABEL_SETTINGS, whiteLabelURL);
		AuthenticateWhiteLabel authWL = new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
		return whiteLabelService.connectStripe(authWL.getWhiteLabelUser(), authWL.getWhiteLabel(), httpRequest.getHeader(Authorization));
	}

//	//@Operation(description = "disconnect stripe account", response = HostBillingSettings.class)
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Account Disconnected successfully"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PostMapping(value = "/disconnect/payment-gateway")
	public ResponseDto stripeDisconnect(@PathVariable String whiteLabelURL,Authentication auth,
										HttpServletRequest httpRequest) {

		AuthenticateWhiteLabel authWL = new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
        log.info("disconnect payment gateway from whitelabel {} | user {}", whiteLabelURL, authWL.getWhiteLabelUser().getUserId());
		whiteLabelService.disconnectStripe(authWL.getWhiteLabelUser(),authWL.getWhiteLabel());
		return new ResponseDto(SUCCESS, ACCOUNT_DISCONNECT_SUCCESS_MSG);

	}

//	//@Operation(description = "getall events list", response = DataTableResponse.class)
//	////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
	@GetMapping("/analytics")
	public DataTableResponse analytics(
			@PathVariable String whiteLabelURL,
			@Parameter(description = "Pages are zero indexed, thus providing 0 for page will return the first page.")
			@RequestParam(defaultValue = "0") int page,
			@RequestParam(defaultValue = "10") int size,
			@RequestParam(defaultValue = "false") boolean pastEvent,
			@RequestParam(value = "search[value]", required = false) String search,
			@RequestParam(value = "sort[field]", required = false) String sortField,
			@RequestParam(value = "sort[direction]", required = false) String sortDirection,
			@RequestParam(value = "fromDate") Date from,
			@RequestParam(value = "toDate") Date to,
			Authentication auth) {
//		Sort.Direction sortDir = isNotBlank(sortDirection) ?
//											Sort.Direction.valueOf(sortDirection.toUpperCase()) : Sort.Direction.ASC;//nosonar
//		sortField = isNotBlank(sortField) ? sortField : "eventName";//nosonar
//		PageRequest pageRequest = PageRequest.of(page,size,sortDir, sortField);//nosonar
		// TODO : Refactor


		new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
		return eventService.getAllEventAnalytics(page, size, search, sortField, sortDirection, from, to, pastEvent, whiteLabelURL);
	}

	@GetMapping(path = {"/whiteLabelUrls"})
	public List<WhiteLabelURLDto> getWhiteLabelDetails(Authentication auth) {
		User user = userUtils.getUser(auth);
		return whiteLabelService.getWhiteLabelByUserAndRole(user);
	}

	private class AuthenticateWhiteLabel {
		private String whiteLabelURL;
		private Authentication auth;
		private User whiteLabelUser;
		private WhiteLabel whiteLabel;

		public AuthenticateWhiteLabel(String whiteLabelURL, Authentication auth) {
			this.whiteLabelURL = whiteLabelURL;
			this.auth = auth;
		}

		public User getWhiteLabelUser() {
			return whiteLabelUser;
		}

		public WhiteLabel getWhiteLabel() {
			return whiteLabel;
		}

		public AuthenticateWhiteLabel authenticate() {
			log.info(WHITE_LABEL_SETTINGS , whiteLabelURL);
			whiteLabelUser = WhiteLabelController.this.userUtils.getUser(auth);
			whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
			if (!WhiteLabelController.this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
				throw new ForbiddenException(UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
			}
			return this;
		}
	}

    //@Operation(description = "Create organizer url")
    @PostMapping(value = "/organizer/{organizerName}")
    public OrganizerDto createWLOrganizer(Authentication auth,
                                          @PathVariable String organizerName, @PathVariable String whiteLabelURL,
                                          @RequestBody @Validated OrganizerDto organizerDto) {
        User whiteLabelUser = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        log.info("createWLOrganizer wl {} | user {} | organizer {}", whiteLabel.getId(), whiteLabelUser.getUserId(), organizerName);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
            return whiteLabelService.createWLOrganizer(organizerName, whiteLabel, organizerDto, whiteLabelUser, Boolean.TRUE);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    //@Operation(description = "organizers list for white label events")
//    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Return attendee and session count by organizer"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping(value = "/organizers")
    public List<OrganizerBasicDto> getCountOfWhiteLabelEvent(@PathVariable String whiteLabelURL, Authentication auth){
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
	    return organizerService.getListOfOrganiserWithWhiteLabel(whiteLabel.getId());
    }

    @GetMapping( value = "/unsubscribedUsers/{eventId}/CSV")
    public void downloadUnsubscribedUsersData(HttpServletResponse response, Authentication auth,
                                              @PathVariable String whiteLabelURL, @PathVariable Long eventId){
        User whiteLabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)){
            downloadService.getUnsubscribedUsersData(response,eventId);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        }

    //@Operation(description = "Returns list usages items")
    @GetMapping(path = {"/billing/summery"})
    public BillingSummerDto getBillingSummery(@PathVariable String whiteLabelURL,Authentication auth) {
        User whiteLabelUser = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        return this.whiteLabelService.getBillingSummeryForWhiteLabel(whiteLabel);
    }

    //@Operation(description = "Update Whitelabel Billing Settings")
    @PutMapping(value = "/whitelabel/billing/settings")
    public ResponseDto updateWhiteLabelBillingSettings(Authentication auth,
                                                       @RequestBody @Validated WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto) {
        User superAdminUser = this.userUtils.getUser(auth);
        boolean isChargeBeeConfigAdminRole = roStaffService.hasChargebeeConfigAdminRole(superAdminUser);
        log.info("updateWhiteLabelBillingSettings wl {} | user {} | isChargeBeeConfigAdminRole {}", whiteLabelBillingSettingsDto.getWhiteLabelURL(), superAdminUser.getUserId(), isChargeBeeConfigAdminRole);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        whiteLabelService.updateWhiteLabelBillingSettings(superAdminUser, whiteLabelBillingSettingsDto, isChargeBeeConfigAdminRole, superAdminUser);
        return new ResponseDto(Constants.SUCCESS, SUCCESS);
    }

    //@Operation(description = "Returns WhiteLabel Billing Settings")
    @GetMapping(value = "/whitelabel/billing/settings")
    public WhiteLabelBillingSettingsDto getWhiteLabelBillingSettings(Authentication auth,
                                                                     @PathVariable("whiteLabelURL") String whiteLabelURL) {
        User superAdminUser = this.userUtils.getUser(auth);
        boolean isChargebeeConfigAdminRole = roStaffService.hasChargebeeConfigAdminRole(superAdminUser);
        if (!this.roUserService.isSuperAdminUser(superAdminUser)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
        return whiteLabelService.getWhiteLabelBillingSettings(whiteLabelURL,isChargebeeConfigAdminRole,superAdminUser);
    }

    @GetMapping(path = {"/listOverages"})
    public  void listAttendeeOverageByWhitelabel(HttpServletResponse response,
                                                @PathVariable String whiteLabelURL, Authentication auth) {
        User whiteLabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)){
            downloadService.getAttendeeOveragesByWhitelabel(response,whiteLabel);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    //@Operation(description = "Generate WhiteLabel Access key")
    @PostMapping(value = "/whitelabel/generateKey")
    public String generateAccessKey(Authentication auth, @PathVariable String whiteLabelURL) {
        User whiteLabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)){
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        String apiKey = whiteLabelService.retrieveApiKeyIfExistForWhiteLabel(whiteLabel.getId());
        if(StringUtils.isEmpty(apiKey)) {
            AccessTokenContainer accessTokenContainer = tokenStoreService.generateAccessToken(whiteLabelService.generateAccessTokenModelForApiUser(whiteLabel));
            apiKey = whiteLabelService.generateAccessKey(whiteLabel, accessTokenContainer.getAccess_token(), accessTokenContainer.getUserId(), whiteLabelUser);
        }
        return apiKey;
    }

    //@Operation(description = "Get Api Key for WhiteLabel")
    @GetMapping(value = "/whitelabel/apiKey")
    public String getAccessKey(Authentication auth, @PathVariable String whiteLabelURL) {
        User whiteLabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)){
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        return whiteLabelService.retrieveApiKeyAndInsertAuditLog(whiteLabel.getId(), whiteLabelUser);
    }

    //@Operation(description = "Associate White label event to HS company ")
    @PostMapping("/associate/wlEventToHsCompany")
    public ResponseDto addAssociationWLEventToHSCompany(@PathVariable("whiteLabelURL") String whiteLabelURL, Authentication auth) {
        User superAdminUser = this.userUtils.getUser(auth);
        if (this.roUserService.isSuperAdminUser(superAdminUser)) {
            WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
            hubspotEventService.associateWhiteLabelEventToCompanyInBatch(whiteLabel);
            return new ResponseDto(SUCCESS, SUCCESS);
        } else {
            throw new ForbiddenException(UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    //@Operation(description = "Rotate Api Key For WhiteLabel")
    @PutMapping(value = "/whitelabel/rotate/apiKey")
    public String rotateKey(Authentication authentication, @PathVariable String whiteLabelURL) {
        User whiteLabelUser = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)){
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        return whiteLabelService.rotateApiKeyForWhiteLabel(whiteLabel, whiteLabelUser);
    }

    //@Operation(description = "Deactivate Api Key For WhiteLabel")
    @PostMapping(value = "/whitelabel/deactivate/apiKey")
    public ResponseDto deactivateKey(Authentication authentication, @PathVariable String whiteLabelURL) {
        User whiteLabelUser = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)){
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        whiteLabelService.deactivateApiKeyForWhiteLabel(whiteLabel.getId(), whiteLabelUser);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Get Api Key Log Details")
    @GetMapping(value = "/whitelabel/apiKey/log")
    public ApiKeyDto getApiKeyLogDetails(Authentication authentication, @PathVariable String whiteLabelURL) {
        User whitelabelUSer = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if(!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUSer, whiteLabel)) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        return whiteLabelService.getApiKeyAuditDetails(whiteLabel.getId());
    }

    //@Operation(description = "Get PayFlow configurations")
    @GetMapping(value = "/whitelabel/payflow/configuration")
    public PayFlowConfigDTO getPayFlowConfiguration(Authentication authentication, @PathVariable String whiteLabelURL) {
        User whitelabelUSer = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if(!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUSer, whiteLabel)) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        return payFlowConfigService.getPayFlowConfigurations(whiteLabel.getId());
    }

    //@Operation(description = "Save PayFlow Configurations")
    @PostMapping(value = "/whitelabel/payflow/configuration")
    public ResponseDto savePayFlowConfiguration(Authentication authentication, @PathVariable String whiteLabelURL,@RequestBody PayFlowConfigDTO payFlowConfigDTO) {
        User whitelabelUSer = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        log.info("savePayFlowConfiguration wl {} | user {}", whiteLabel.getId(), whitelabelUSer.getUserId());
        if(!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUSer, whiteLabel)) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        String returnValue = payFlowConfigService.savePayFlowConfigurations(whiteLabel, payFlowConfigDTO, whitelabelUSer);
        return new ResponseDto(SUCCESS, returnValue);
    }

    //@Operation(description = "update PayFlow Configurations")
    @PutMapping(value = "/whitelabel/payflow/configuration")
    public ResponseDto updatePayFlowConfiguration(Authentication authentication, @PathVariable String whiteLabelURL,
                                                  @RequestBody PayFlowConfigDTO payFlowConfigDTO) {
        User whitelabelUSer = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        log.info("updatePayFlowConfiguration wl {} | user {}", whiteLabel.getId(), whitelabelUSer.getUserId());
        if(!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUSer, whiteLabel)) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        String returnValue = payFlowConfigService.updatePayFlowConfigurations(whiteLabel, payFlowConfigDTO);
        return new ResponseDto(SUCCESS, returnValue);
    }

    //@Operation(description = "Disconnect PayFlow Configurations")
    @PostMapping(value = "/whitelabel/payflow/configuration/disconnect")
    public ResponseDto disconnectPayFlowConfiguration(Authentication authentication, @PathVariable String whiteLabelURL) {
        User whitelabelUser = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if(!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUser, whiteLabel)) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        payFlowConfigService.disconnectPayFlowConfigurations(whitelabelUser, whiteLabel);
        return new ResponseDto(SUCCESS,ACCOUNT_DISCONNECT_SUCCESS_MSG);
    }

//    //@Operation(description = "white label image extension updated successfully", response = ResponseDto.class)
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PutMapping(value = "/imageExtension", produces = "application/json")
    public ResponseDto updateWLLogoImageExtension(@PathVariable String whiteLabelURL,
                                                  @RequestParam(value = "logoType") String logoType,
                                                  @RequestParam(value = "imgExtension") String imgExtension) {
	    whiteLabelService.updateWLLogoImageExtension(whiteLabelURL, logoType, imgExtension);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "Send Mail For The Create New White Label Event")
    @PostMapping(value = "/sendMailForNewWLEvent/{EventURL}")
    public ResponseDto sendWLCreateEventMail(Authentication auth,
                                             @PathVariable(value = "EventURL")String eventURL,
                                             @PathVariable String whiteLabelURL){
        AuthenticateWhiteLabel authWL = new AuthenticateWhiteLabel(whiteLabelURL, auth).authenticate();
        whiteLabelService.sendMailForNewWLEvent(eventURL,whiteLabelURL,authWL.getWhiteLabelUser());
        return new ResponseDto(SUCCESS, SUCCESS);
    }


    @PutMapping(value = "/team/{teamId}/billingContact/{mark}")
    public ResponseDto markAsBillingContact(Authentication authentication, @PathVariable String whiteLabelURL,
                                            @PathVariable("teamId") Long teamId, @PathVariable("mark") boolean mark) {

        User whitelabelUser = restAuthValidator.authUser(authentication);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        log.info("markAsBillingContact WL {} | teamId {} | mark {}", whiteLabel.getId(), teamId, mark);
        if (!this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUser, whiteLabel)) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        whiteLabelService.markAsBillingContactForWL(teamId, mark, whitelabelUser);
        return new ResponseDto(SUCCESS, mark ? BILLING_CONTACT_UPDATED_SUCCESSFULLY : TEAM_MEMBER_REMOVE_FROM_BILLING_CONTACT);
    }

    @GetMapping(value = "/setting/security")
    public WhiteLabelSecuritySettingDto getWLSecuritySetting(@PathVariable String whiteLabelURL, Authentication auth) {

        User whitelabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUser, whiteLabel)) {
            return whiteLabelService.getWhiteLabelSecuritySettingDto(whiteLabelURL);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }
    @PostMapping(value = "/setting/security")
    public ResponseDto updateWLSecuritySetting(@PathVariable String whiteLabelURL,
                                      @RequestBody WhiteLabelSecuritySettingDto whiteLabelSecuritySettingDto, Authentication auth) {

        User whitelabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        log.info("updateWLSecuritySetting for WL {} | user {}", whiteLabel.getId(), whitelabelUser.getUserId());
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUser, whiteLabel)) {
            whiteLabelService.updateWhiteLabelSecuritySettingDto(whiteLabelURL, whiteLabelSecuritySettingDto);
            return new ResponseDto(SUCCESS, SUCCESS);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    // We can't move this API to wl-admin controller because this API is used to update whiteLabelId in token and that need to be done without 2FA test
    @GetMapping(path = {"/update"})
    public boolean updateTokenWithWhiteLabelId(@PathVariable String whiteLabelURL,Authentication auth, HttpServletRequest request) {
        User whiteLabelUser = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
            String token = request.getHeader("Authorization");
            whitelabelUtils.updateWhiteLabelIdInToken(whiteLabel.getId(), token);
            return true;
        }
        throw new AuthorizationException(NOT_AUTHORIZE);
    }
    //@Operation(description = "Get entitlements for the whiteLabel")
    @GetMapping(path = {"/entitlements"})
    public EntitlementsAvailabilityDTO getEntitlementsBasedOnOrganizerUrl(Authentication auth, @PathVariable("whiteLabelURL") String whiteLabelUrl) {
        User whitelabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(whitelabelUser, whiteLabel)) {
            return whiteLabelService.getEntitlementsBasedOnWhiteLabelUrl(whiteLabel);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @GetMapping(path = {"/event-coordinator"})
    public boolean isEventCoordinator(Authentication auth, @PathVariable("whiteLabelURL") String whiteLabelUrl) {
        User user = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        return this.roStaffService.isEventCoordinator(user, whiteLabel);
    }

    @PutMapping(value = "/update-salesforce-login-flag/{isEnable}")
    public ResponseDto updateSalesforceLoginFlag(@PathVariable String whiteLabelURL,
                                                 @PathVariable("isEnable") boolean isEnable, Authentication auth) {

        User whitelabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUser, whiteLabel)) {
            whiteLabelService.updateSalesforceLoginFlag(whiteLabelURL,isEnable);
            return new ResponseDto(SUCCESS, SUCCESS);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @GetMapping("/payment_gateway/auth_modes/{gatewayType}")
    public List<AuthMode> getAuthModeForSpreedlyGatewayOnWL(@PathVariable String whiteLabelURL, @PathVariable("gatewayType") String gatewayType, Authentication auth) {
        User whitelabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUser, whiteLabel)) {
            return whiteLabelService.getPaymentGatewayAuthModeFieldForWL(gatewayType);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping(value = "/payment-gateway/create")
    public ResponseDto connectSpreedlyPaymentGatewayForWL(@PathVariable String whiteLabelURL, @RequestBody GatewayRequestDto gatewayRequestDto,Authentication auth) {
        User whitelabelUser = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whitelabelUser, whiteLabel)) {
            whiteLabelService.connectSpreedlyPaymentGatewayForWL(whiteLabel, whitelabelUser, gatewayRequestDto);
            return new ResponseDto(SUCCESS, SUCCESS);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }
}
