package com.accelevents.controllers.rest;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.RuleFormType;
import com.accelevents.dto.FormRulesDetailsAttributesDto;
import com.accelevents.dto.FormRulesDetailsDto;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.FormRulesService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping({"/rest/event/{eventUrl}/form-rules"})
@Tag(name = "/rest/event/{eventUrl}/form-rules")
public class DisplayFormRulesController {


    private final FormRulesService formRulesService;

    private final ROEventService eventService;

    @Autowired
    public DisplayFormRulesController(FormRulesService formRulesService, ROEventService eventService){
        this.formRulesService = formRulesService;
        this.eventService = eventService;
    }

    @GetMapping("/get-all-form-rules")
    public List<FormRulesDetailsDto> getAllFormRules(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl, @RequestParam(value = "type") RuleFormType ruleFormType,
    @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
        Event event = eventService.getEventByURL(eventUrl);
        return formRulesService.getAllFormRules(null, event.getEventId(), ruleFormType, recurringEventId);
    }

    @GetMapping("/attributes-form-rules")
    public List<FormRulesDetailsAttributesDto> getAllFormRulesByAttributes(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl, @RequestParam(value = "type") RuleFormType ruleFormType,
                                                                           @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
        Event event = eventService.getEventByURL(eventUrl);
        return formRulesService.getAllFormRulesByAttributes(null, event.getEventId(), ruleFormType, recurringEventId);
    }

}
