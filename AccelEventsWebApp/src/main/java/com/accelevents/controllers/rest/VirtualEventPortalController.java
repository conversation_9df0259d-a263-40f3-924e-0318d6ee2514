package com.accelevents.controllers.rest;

import com.accelevents.common.dto.UserTaskParticipantsDto;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumLabelLanguageCode;
import com.accelevents.domain.enums.EnumMultiLanguageLabelType;
import com.accelevents.domain.enums.TaskTabType;
import com.accelevents.domain.enums.TaskType;
import com.accelevents.dto.*;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.security.tokenstore.TokenStoreService;
import com.accelevents.services.*;
import com.accelevents.session_speakers.services.MeetingScheduleService;
import com.accelevents.session_speakers.services.SessionSpeakerService;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.ticketing.dto.EventTicketingDto;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.EventUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.accelevents.utils.Constants.Authorization;
import static com.accelevents.utils.Constants.SUCCESS;


@RestController
@RequestMapping("/rest/event/{eventUrl}/virtualEventPortal")
@Tag(name = "/rest/event/{eventUrl}/virtualEventPortal")
public class VirtualEventPortalController {

    @Autowired private RestAuthValidator restAuthValidator;
    @Autowired private AuthValidator authValidator;
    @Autowired private ROEventService roEventService;
    @Autowired private VirtualEventPortalService virtualEventPortalService;
    @Autowired private TicketingCheckInService ticketingCheckInService;
    @Autowired private VirtualEventService virtualEventService;
    @Autowired private SpeakerService speakerService;
    @Autowired private EventDesignDetailService eventDesignDetailService;
    @Autowired private TicketingManageService ticketingManageService;
    @Autowired private ZoomMeetingService zoomMeetingService;
    @Autowired private CheckInAuditLogService checkInAuditLogService;
    @Autowired private SessionSpeakerService sessionSpeakerService;
    @Autowired private MultiLanguageLabelService multiLanguageLabelService;
    @Autowired
    private TokenStoreService tokenStoreService;
    @Autowired
    private MeetingScheduleService meetingScheduleService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private  EventTaskService eventTaskService;
    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;
    @Autowired
    private EventUtils eventUtils;
    @Autowired
    private UserService userService;

    Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

    private static final Logger log = LoggerFactory.getLogger(VirtualEventPortalController.class);

    //@Operation(description = "get virtual event settings", response = VirtualEventSettingsDTO.class)
    @GetMapping(path = {"/virtualAccess"})
    public @ResponseBody
    VirtualEventAccessDto getUserRole(@PathVariable final String eventUrl,
                                      HttpServletRequest httpRequest,
                                        Authentication authentication) {
        UserEvent userEvent = authValidator.getEventByUrlAndUserFromAuth(eventUrl, authentication);
        String role = restAuthValidator.getSuperAdminRole(userEvent.getUser());
        List<String> userRoles = new ArrayList<>();
        String headerValue=httpRequest.getHeader(Constants.SOURCE);
        if ((Constants.MOBILE_APP).equals(headerValue)){
            log.info("virtualAccess call from mobile app eventID: {} and userId: {} ",userEvent.getEvent().getEventId(),userEvent.getUser().getUserId());
            userRoles=roStaffService.getAllUserRoles(userEvent.getEvent().getEventId(), userEvent.getUser().getUserId());
        }else {
            userRoles=roStaffService.getAllUserRolesWithVirtualAndHybridTickets(userEvent.getEvent().getEventId(), userEvent.getUser().getUserId());
        }
        if(StringUtils.isEmpty(role)){
            if(CollectionUtils.isEmpty(userRoles)){
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.PERMISSION_DENIED_REGISTER_EVENT);
            }
            return new VirtualEventAccessDto(userRoles);
        } else {
            userRoles.add(UserRole.ROLE_SUPERADMIN.name());
            return new VirtualEventAccessDto(true, userRoles);
        }

    }

    //@Operation(description = "Check if user can access virtual events portal")
    @GetMapping(value = "/checkEndEventAccess")
    public boolean checkEndEventAccess(@PathVariable final String eventUrl,
                                            Authentication auth) {
        User user = restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);

        boolean isAllowed = speakerService.isSpeakerInEvent(event, user)
                            || restAuthValidator.isAdminStaffOrExhibitorOrSuperAdminOfEvent(event, user);

        if(!isAllowed && eventDesignDetailService.checkEndEventEnd(event)){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.EVENT_ENDED);
        }
        return true;
    }

    //@Operation(description = "set status as checkedIn for this ticket")
    @PutMapping("/check-in")
    public boolean checkInTicket(@PathVariable("eventUrl") String eventUrl,
                                 @RequestParam(name = "source",required = false) String source,
                                 @RequestParam(name = "sourceDescription",required = false) String sourceDescription,
                              Authentication auth,
                              HttpServletRequest httpRequest) {
        User user = restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        String authToken = httpRequest.getHeader(Authorization);
        AccessTokenModel accessTokenModel = tokenStoreService.readAccessToken(authToken);
        this.eventUtils.setEvent(auth, event, authToken);
        log.info("user details before the user check-in: {} and eventId: {}",user,event.getEventId());
        user = userService.updateUserMostResentEvent(user, event);
        String device = ticketingCheckInService.getDeviceInfo(httpRequest);
        boolean isAdminStaffOrSuperAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        return ticketingCheckInService.checkInVirtualEventTicket(user, event, device, isAdminStaffOrSuperAdmin,accessTokenModel.isAppLogin(),source,sourceDescription);
    }

    //@Operation(description = "Check user is staff or admin.", response = UserRoleDto.class)
    @GetMapping(path = {"/isStaffOrAdminOrSpeaker"})
    public ExtendedUserRoleDto isStaffOrAdminOrSpeaker(@PathVariable final String eventUrl, @RequestParam(defaultValue = "0") Long sessionId, Authentication auth) {
        User user = restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return virtualEventPortalService.isStaffOrAdminOrSpeaker(user,event, sessionId);
    }

    //@Operation(description = "Check user is exhibitor staff or not.", response = Boolean.class)
    @GetMapping(path = {"/isExhibitorStaff/{exhibitorId}"})
    public boolean isExhibitorStaff(@PathVariable final String eventUrl, @PathVariable long exhibitorId, Authentication auth) {
        try{
            authValidator.authExhibitorStaffAccess(auth, eventUrl, exhibitorId);
            return true;
        } catch (ForbiddenException ex){
            return false;
        }
    }

    //@Operation(description = "store get stream channel details by channel id.", response = ResponseDto.class)
    @PostMapping(path = {"/channel/{id}/matchedUser/{matchedUserId}"})
    public ResponseDto streamChannelDetails(@PathVariable final String eventUrl,
                                             @PathVariable(value = "id") String channelId,
                                             @PathVariable(value = "matchedUserId") Long matchedUserId,
                                             Authentication auth) {
        User user = restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        virtualEventPortalService.createStreamChannelDetails(user, event, channelId, matchedUserId, restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user));
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    //@Operation(description = "store get stream channel details.")
    @GetMapping(path = {"/channel"})
    public List<String> channelIdsByUserIdAndEventId(@PathVariable final String eventUrl,
                                                      Authentication auth) {
        User user = restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return virtualEventPortalService.getStreamChannelIdsByUserIdAndEventId(user, event);
    }

    //@Operation(description = "get getstream channels.")
    @GetMapping(path = {"/channels"})
    public List<String> getChannelIds(@PathVariable final String eventUrl, @RequestParam(name = "isExpoStaff", required = false) boolean isExpoStaff,
                                       @RequestParam(name = "isSpeaker", required = false) boolean isSpeaker, Authentication auth) {
        User user = restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return virtualEventPortalService.getStreamChannelIds(user, event, true, isExpoStaff, isSpeaker);
    }

    //@Operation(description = "get all ticketing settings for portal page", response = EventTicketingDto.class)
    @GetMapping("/ticketing/settings")
    public EventTicketingDto getTicketingSettingsForPortalPage(@PathVariable final String eventUrl, Authentication auth) {
        restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return this.ticketingManageService.getTicketing(event);
    }

    //@Operation(description = "Generate Zoom Meeting Signature", response = ResponseDto.class)
    @PostMapping(path = {"/zoom/signature/meetingNumber/{meetingNumber}/role/{role}"})
    public String generateZoomMeetingSignature(@PathVariable final String eventUrl,
                                                @PathVariable(value = "meetingNumber") String meetingNumber,
                                                @PathVariable(value = "role") Integer role,
                                                Authentication auth) {
        restAuthValidator.authUser(auth,eventUrl);
        return zoomMeetingService.generateSignature(meetingNumber, role, eventUrl);
    }

    //@Operation(description = "Get virtual access user role", response = VirtualEventAccessDto.class)
    @GetMapping(path = {"/allowedToVirtualAccess"})
    public VirtualEventAccessDto getUserAccessRole(@PathVariable final String eventUrl,
                                            Authentication auth) {
        UserEvent userEvent = authValidator.getEventByUrlAndUserFromAuth(eventUrl, auth);
        boolean isAdminStaffOrSuperAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(userEvent.getEvent(), userEvent.getUser());
        String role = restAuthValidator.getSuperAdminRole(userEvent.getUser());
        return virtualEventPortalService.checkEventPortalPageAccess(userEvent.getEvent(), userEvent.getUser(), role, isAdminStaffOrSuperAdmin);
    }

    //@Operation(description = "Check allowed attendees", response = Boolean.class)
    @GetMapping(path = {"/isCheckInAttendeeMoreThanAllowed"})
    public boolean isCheckInAttendeeMoreThanAllowed(@PathVariable final String eventUrl,
                                                    Authentication auth) {
          restAuthValidator.authUser(auth,eventUrl);
          Event event = roEventService.getEventByURL(eventUrl);
        return virtualEventPortalService.isCheckInAttendeeMoreThanAllowed(event);
    }

    //@Operation(description = "Get Virtual Event Labels By Language Code")
    @GetMapping("/virtualeventsettings/language/{languageCode}")
    public List<LabelDto> getVirtualEventLabelByLanguageCode(@PathVariable String eventUrl, @PathVariable String languageCode, @RequestParam(required = false) EnumMultiLanguageLabelType labelType) {
        Event event = roEventService.getEventByURL(eventUrl);
        return multiLanguageLabelService.getVirtualEventLabelByLanguageCodeAndType(event, languageCode,labelType);
    }

    //@Operation(description = "Check the ticketOrder amount is fully paid or not based on OrderType(PayLater)")
    @GetMapping("/check-amount-paid-for-paylater")
    public boolean checkEventAmountFullyPaidOrNotBasedOnPayLater(@PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.getEventByUrlAndUserFromAuth(eventUrl, auth);
        return virtualEventPortalService.checkEventAmountFullyPaidOrNotBasedOnPayLater(userEvent.getUser(), userEvent.getEvent());
    }

    @GetMapping("/user-allowed-check-in-with-unpaid-ticket")
    public boolean isUserAllowedToCheckInWithUnpaidTicket(@PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.getEventByUrlAndUserFromAuth(eventUrl, auth);
        boolean isAdminStaffOrSuperAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(userEvent.getEvent(), userEvent.getUser());
        return virtualEventPortalService.checkUserAllowedToCheckInWithUnPaidTicket(userEvent.getUser(), userEvent.getEvent(), isAdminStaffOrSuperAdmin);
    }

    //@Operation(description = "update show pop flag while entering in event")
    @PostMapping("/updateShowPopUpFlag")
    public void updateShowPopUpWhileEnteringInEvent(@PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.getEventByUrlAndUserFromAuth(eventUrl, auth);
        virtualEventPortalService.updateShowPopUpWhileEnteringInEvent(userEvent.getUser(), userEvent.getEvent());
    }

    //@Operation(description = "Get survey response", response = VirtualEventAccessDto.class)
    @GetMapping(path = {"/access-survey"})
    public VirtualEventAccessDto getSurveyResponse(@PathVariable final String eventUrl,
                                                   @RequestParam(value = "isLoginRequiredToAccessSurvey", defaultValue = "false", required = false) boolean isLoginRequiredToAccessSurvey,
                                                   Authentication auth) {
        User user = authValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        String role = restAuthValidator.getSuperAdminRole(user);
        return virtualEventPortalService.accessSurvey(event, user, role, isLoginRequiredToAccessSurvey);
    }

    //@Operation(description = "Get Pre-Scheduled meetings is available and meeting creation status of user event ticket")
    @GetMapping("/meeting-schedule-creation-status")
    public AttendeeMeetingCreationConfigDTO isPreScheduledMeetingAvailable(@PathVariable String eventUrl, Authentication auth) {
        User user = authValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return meetingScheduleService.getPreScheduleMeetingAndEventTicketMeetingCreationStatus(event, user.getUserId());
    }
    //@Operation(description = "Attendee mobile app entitlement")
    @GetMapping("/attendee-mobile-app-entitlement")
    public boolean checkAttendeeMobileAppAccess(@PathVariable("eventUrl") String eventUrl, Authentication auth) {
        restAuthValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return virtualEventService.attendeeMobileAppEntitlement(event);
    }
    @GetMapping("/user-restricted-custom-tab")
    public Set<String> getAttendeeRestrictedCustomLobbyTab(@PathVariable("eventUrl") String eventUrl, Authentication auth) {
        User user = authValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        if(roStaffService.hasHostAccessForEvent(user,event)) {
            return Collections.emptySet();
        }else {
            return virtualEventService.getAttendeeRestrictedCustomLobbyTab(user, event);
        }
    }
    @GetMapping("/user-all-task")
    public List<UserTaskParticipantsDto> getUserEventTasks(@PathVariable("eventUrl") String eventUrl, Authentication auth,
                                                           @Parameter(description = "Tab") @RequestParam(defaultValue = "ALL") TaskTabType tab,
                                                           @RequestParam(required = false) Long exhibitorId,
                                                           @RequestParam(required = false) List<TaskType> taskTypes) {
        User user = authValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return eventTaskService.getUserEventTasks(event,user,tab,taskTypes,exhibitorId);
    }
    @PostMapping ("/update-user-task-workflow-status/{taskType}/{taskParticipantId}")
    public ResponseDto updateUserTaskWorkFlowStatus(@PathVariable("eventUrl") String eventUrl, @PathVariable TaskType taskType, Authentication auth,
                                                    @PathVariable long taskParticipantId) {
        User user = authValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        eventTaskService.updateUserTaskWorkFlowStatus(event,user,taskParticipantId,taskType);
        return new ResponseDto(SUCCESS,SUCCESS);
    }

    @Deprecated
    @GetMapping(path = {"/event-level-settings"})
    public EventLevelSettingsDTO getEventLevelSettings(@Parameter(description = "Event url")
                                                           @PathVariable() String eventUrl) {

        Event event = roEventService.getEventByURL(eventUrl);
        return roEventLevelSettingService.getEventLevelSettings(event);
    }
}
