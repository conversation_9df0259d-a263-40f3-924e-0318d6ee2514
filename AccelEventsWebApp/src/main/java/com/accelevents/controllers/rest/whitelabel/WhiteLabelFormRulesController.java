package com.accelevents.controllers.rest.whitelabel;

import com.accelevents.controllers.rest.RestAuthValidator;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.RuleFormType;
import com.accelevents.dto.FormRulesDetailsAttributesDto;
import com.accelevents.dto.FormRulesDetailsDto;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.FormRulesService;
import com.accelevents.services.repo.helper.impl.WhiteLabelRepoServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller for event request forms
 */
@RestController
@RequestMapping("/rest/whiteLabel/{whiteLabelURL}/form-rules")
@Tag(name = "Event Request Forms API")
public class WhiteLabelFormRulesController {

    @Autowired
    private WhiteLabelRepoServiceImpl whiteLabelRepoService;
    @Autowired
    private RestAuthValidator restAuthValidator;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private  FormRulesService formRulesService;

    @PostMapping("/event-request-form/{formId}")
    @Operation(summary = "Create a new event request form", description = "Creates a new event request form with the provided details")
    public Long createFormRule(
            @Parameter(description = "White label URL", required = true)
            @PathVariable("whiteLabelURL") String whiteLabelURL,
            @PathVariable("formId") Long formId,
            @Parameter(description = "Form data", required = true)
            @RequestBody @Validated FormRulesDetailsDto formRulesDetailsDto,
            Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelOrNotFoundException(whiteLabelURL);
        roStaffService.hasWhiteLabelAdminAccess(user, whiteLabel);
        return formRulesService.createFormRule(user, formId, formRulesDetailsDto, 0L);
    }

    @PutMapping("/event-request-form/{formId}/rule/{ruleId}")
    @Operation(summary = "Update an event request form rule", description = "Updates an existing event request form rule with the provided details")
    public void updateFormRule(
            @Parameter(description = "White label URL", required = true)
            @PathVariable("whiteLabelURL") String whiteLabelURL,
            @PathVariable("formId") Long formId,
            @PathVariable("ruleId") Long ruleId,
            @Parameter(description = "Form rule data", required = true)
            @RequestBody @Validated FormRulesDetailsDto formRulesDetailsDto,
            Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelOrNotFoundException(whiteLabelURL);
        roStaffService.hasWhiteLabelAdminAccess(user, whiteLabel);
        formRulesService.updateFormRule(user, formId, ruleId, formRulesDetailsDto, 0L);
    }

    @DeleteMapping("/event-request-form/{formId}/rule/{ruleId}")
    @Operation(summary = "Delete an event request form rule", description = "Deletes an existing event request form rule")
    public void deleteFormRule(
            @Parameter(description = "White label URL", required = true)
            @PathVariable("whiteLabelURL") String whiteLabelURL,
            @PathVariable("formId") Long formId,
            @PathVariable("ruleId") Long ruleId,
            Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelOrNotFoundException(whiteLabelURL);
        roStaffService.hasWhiteLabelAdminAccess(user, whiteLabel);
        formRulesService.deleteFormRule(user, formId, ruleId, 0L);
    }

    @GetMapping("/event-request-form/{formId}/rule/{ruleId}")
    @Operation(summary = "Get an event request form rule", description = "Retrieves details of a specific event request form rule")
    public FormRulesDetailsDto getFormRule(
            @Parameter(description = "White label URL", required = true)
            @PathVariable("whiteLabelURL") String whiteLabelURL,
            @PathVariable("formId") Long formId,
            @PathVariable("ruleId") Long ruleId,
            Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelOrNotFoundException(whiteLabelURL);
        roStaffService.hasWhiteLabelAdminAccess(user, whiteLabel);
        return formRulesService.getFormRule(user, formId, ruleId);
    }

    @GetMapping("/event-request-form/{formId}/rules")
    @Operation(summary = "Get all event request form rules", description = "Retrieves all rules for a specific event request form")
    public List<FormRulesDetailsDto> getAllFormRules(
            @Parameter(description = "White label URL", required = true)
            @PathVariable("whiteLabelURL") String whiteLabelURL,
            @PathVariable("formId") Long formId,
            @Parameter(description = "Rule form type", required = true)
            @RequestParam(value = "type") RuleFormType ruleFormType,
            Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelOrNotFoundException(whiteLabelURL);
        roStaffService.hasWhiteLabelAdminAccess(user, whiteLabel);
        return formRulesService.getAllFormRules(user, formId, ruleFormType, 0L);
    }

    @GetMapping("/event-request-form/{formId}/rules/attributes")
    @Operation(summary = "Get all event request form rules by attributes", description = "Retrieves all rules by attributes for a specific event request form")
    public List<FormRulesDetailsAttributesDto> getAllFormRulesByAttributes(
            @Parameter(description = "White label URL", required = true)
            @PathVariable("whiteLabelURL") String whiteLabelURL,
            @PathVariable("formId") Long formId,
            @Parameter(description = "Rule form type", required = true)
            @RequestParam(value = "type") RuleFormType ruleFormType,
            Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelOrNotFoundException(whiteLabelURL);
        roStaffService.hasWhiteLabelAdminAccess(user, whiteLabel);
        return formRulesService.getAllFormRulesByAttributes(user, formId, ruleFormType, 0L);
    }
}