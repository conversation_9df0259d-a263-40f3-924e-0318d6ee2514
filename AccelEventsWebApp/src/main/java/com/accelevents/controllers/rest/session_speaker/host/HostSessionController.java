package com.accelevents.controllers.rest.session_speaker.host;


import com.accelevents.auction.dto.UploadSessionAttendeeResponseContainer;
import com.accelevents.common.dto.SessionBulkUpdatesDto;
import com.accelevents.common.dto.SessionMassOperationConfigDTO;
import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.BulkSessionStatusUpdateResultDto;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.dto.ResponseDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.EnumQNAType;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.DownloadService;
import com.accelevents.services.MessageToContactService;
import com.accelevents.services.MuxService;
import com.accelevents.services.StaffService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.services.*;
import com.accelevents.session_speakers.services.impl.BroadcastGraphQLHandler;
import com.accelevents.session_speakers.services.impl.SessionDeleteHandler;
import com.accelevents.utils.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.Authentication;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static com.accelevents.domain.enums.EnumSessionFormat.MEET_UP;
import static com.accelevents.utils.ApiMessages.PAGE_DESC;
import static com.accelevents.utils.ApiMessages.START_DATE;
import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping({"/rest/host/event/{eventUrl}/session","/rest/host/session"})
@Tag(name = "/rest/host/session")
public class HostSessionController {

    private static final Logger log = LoggerFactory.getLogger(HostSessionController.class);

    @Autowired
    private AuthValidator authValidator;
    @Autowired
    private SessionService sessionService;
    @Autowired
    private SessionRepoService sessionRepoService;
    @Autowired
    private SessionThirdPartyService sessionThirdPartyService;
    @Autowired
    private SessionDeleteHandler sessionDeleteHandler;
    @Autowired
    private DownloadService downloadService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private GraphQLConfiguration graphQLConfiguration;
    @Autowired
    private UserSessionService userSessionService;
    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private MuxService muxService;
    @Autowired
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private MessageToContactService messageToContactService;

    @Autowired
    SessionLocationService sessionLocationService;

    Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

    //@Operation(description = "Create or Reschedule session push notification")
    @PutMapping("/createOrRescheduleSessionPushNotification/{id}")
    public void createOrRescheduleSessionNotification(@PathVariable Long id,
                                                      @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                  Authentication auth) {
        log.info("HostSessionController | createOrRescheduleSessionNotification | session id {}",id);
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        if(NumberUtils.isNumberGreaterThanZero(id)) {
            Session session = sessionService.getSessionById(id, userEvent.getEvent());
            if (session != null && session.getStartTime() != null && session.getEndTime() != null){
                if (session.getStartTime().after(new Date()))
                    messageToContactService.createOrRescheduleSessionPushNotification(session, userEvent.getUser(), userEvent.getEvent());
                else if (TimeZoneUtil.getDateInUTC(session.getStartTime(), userEvent.getEvent().getEquivalentTimeZone()).before(userEvent.getEvent().getCreatedDate()) ||
                        TimeZoneUtil.getDateInUTC(session.getStartTime(), userEvent.getEvent().getEquivalentTimeZone()).before(new Date())) {
                    if (NumberUtils.isNumberGreaterThanZero(session.getId()))
                        messageToContactService.deleteSessionOrMeetingNotification(session.getId(), userEvent.getEvent().getEventId(),EmailNotificationType.SESSION_NOTIFICATION);
                }
            }
        }
    }

    //@Operation(description = "Create session")
    @PostMapping
    public Long createSession(@RequestBody @Validated SessionDTO sessionDTO,
                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                              Authentication auth) {
        /*
         *
         When changing logic in this method, change respective logic (if any) in uploadSessionCSV method too.
         *
         */
        UserEvent userEvent = authValidator.authHostLevelAccess(auth,eventUrl);
        sessionService.checkNonVisualSessionCount(sessionDTO.getFormat(), userEvent.getEvent(), null, false);
        Long sessionId = sessionService.createSession(userEvent.getEvent(), sessionDTO);
        if (!sessionDTO.getFormat().equals(MEET_UP)) {
            sessionThirdPartyService.createChannel(sessionId, sessionDTO.getTitle(), sessionDTO.getFormat(), userEvent.getEvent());
        }
        return sessionId;

    }

    //@Operation(description = "Update session")
    @PutMapping("/update/{id}")
    public void
    updateSessionById(@RequestBody @Validated SessionDragAndDropDto sessionDTO,
                              @PathVariable Long id,
                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                              Authentication auth, HttpServletRequest httpRequest) {
        log.info("Update session Id drag and drop {}",id);
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.updateSessionWithDragAndDrop(id, sessionDTO, userEvent.getEvent(), userEvent.getUser());

    }

    //@Operation(description = "Register user to session from host")
    @PostMapping("/register/user")
    public void registerUserFromHost(
            @RequestBody UserSessionDTO userSessionDTO,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            HttpServletRequest httpRequest,
            Authentication auth) {
        String sourceHeader = httpRequest.getHeader(Constants.SOURCE);
        boolean isAppLogin = StringUtils.isNotBlank(sourceHeader) && Constants.MOBILE_APP.equals(sourceHeader);

        if(isAppLogin){
            UserEvent userEvent = authValidator.authHasStaffLevelAccess(auth, eventUrl);
            Session session = sessionRepoService.getSessionById(userSessionDTO.getSessionId(), userEvent.getEvent());
            userSessionService.registerUserFromHost(userSessionDTO, session,userEvent.getEvent());
        }
        else{
            UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
            Session session = sessionRepoService.getSessionById(userSessionDTO.getSessionId(), userEvent.getEvent());
            userSessionService.registerUserFromHost(userSessionDTO, session,userEvent.getEvent());
        }

    }


    @PostMapping("/register/bulk-users/{sessionId}")
    public UploadSessionAttendeeResponseContainer registerUsersFromHost(@PathVariable("sessionId") Long sessionId,
            @RequestParam("userIds") String userIds,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        return userSessionService.registerBulkUsersFromHost(session,userEvent.getEvent(), userIds, languageCode);
    }

    //@Operation(description = "Register users to session from host in bulk")
    @PostMapping("/register/upload/user/{sessionId}")
    public UploadSessionAttendeeResponseContainer registerUserFromHostInBulk(
            @RequestParam("attendeeFile") MultipartFile multiPartFile,
            @PathVariable("sessionId") Long sessionId,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        User user = authValidator.authUser(auth);
        Event event = userEvent.getEvent();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
        return userSessionService.registerUserFromHostInBulk(sessionId, multiPartFile, userEvent.getEvent(),languageCode);
    }

    //@Operation(description = "Update session status")
    @PutMapping("/{id}/visible/{isVisible}")
    public void updateSessionStatus(@PathVariable Long id,
                                    @PathVariable boolean isVisible,
                                    @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                    Authentication auth) {
        log.info("Update session Id {}",id);
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionService.getSessionById(id, userEvent.getEvent());
        if (isVisible) {
            session.setStatus(EnumSessionStatus.VISIBLE);
        } else {
            session.setStatus(EnumSessionStatus.HIDDEN);
        }
        sessionService.save(session);
    }

    //@Operation(description = "Delete session")
    @DeleteMapping("/{id}")
    public void deleteSession(Authentication auth, HttpServletRequest httpServletRequest,
                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                              @PathVariable("id") Long sessionId) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionDeleteHandler.deleteSession(sessionId, userEvent.getEvent(), userEvent.getUser(), httpServletRequest.getHeader(Authorization));
        sessionThirdPartyService.deleteSessionChannel(sessionId, userEvent.getEvent().getEventId());
        messageToContactService.deleteSessionOrMeetingNotification(sessionId,userEvent.getEvent().getEventId(),EmailNotificationType.SESSION_NOTIFICATION);
    }

    //@Operation(description = "get session ")
    @GetMapping("/{id}")
    public SessionDTO getSessionInfoById(
            @PathVariable("id") Long sessionId,
            @RequestParam(required = false, defaultValue = "") String expand,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            Authentication auth) {

        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getSessionInfoById(sessionId, userEvent.getEvent(), userEvent.getUser(),expand);
    }

    //@Operation(description = "get all sessions")
    @GetMapping
    public DataTableResponse getSessionList(
        @RequestParam(required = false) String searchString,
        @RequestParam(required = false) Long eventId,
        @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false, defaultValue = "") String expand,
        @RequestParam(required = false, defaultValue = "false") boolean isFromBillingPage,
        @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
        @RequestParam(name = "sortColumn", defaultValue = "startTime") String sortColumn,
        @RequestParam(name = "isAsc", defaultValue = "true") boolean isAsc,
        @RequestParam(name="sessionTypeFormats",defaultValue = "") List<SessionTypeFormat> sessionTypeFormats,
        @RequestParam(required = false, name = "searchDate") @DateTimeFormat(pattern = "dd/MM/yyyy") List<Date> searchDate,
        @RequestParam(name="sessionTypes",defaultValue = "") List<EnumSessionFormat> sessionTypes,
        @RequestParam(required = false,name = "tagIds") String tagIds,
        @RequestParam(required = false,name= "trackIds") String trackIds,
        @RequestParam(required = false,defaultValue ="false") boolean isPast,
        @RequestParam(required = false,defaultValue = "false") boolean isUpcoming,
        Authentication auth) {

        Event event = authValidator.authEventOrGetEvent(eventId, auth, eventUrl);
        List<Long> listOfTagAndTrackIds=sessionService.getListOfIdsForTagAndTrackForSorting(tagIds,trackIds);
        Sort by = null;
        if (isAsc) {
            by = Sort.by(sortColumn);
        } else {
            by = Sort.by(sortColumn).descending();
        }

        if ("startTime".equalsIgnoreCase(sortColumn) && isAsc) {
            return sessionService.getSessionHostList(expand + ",STATS", event, searchString, PageRequest.of(Math.max(page, 0), (size>0)?size:10), isFromBillingPage,sessionTypeFormats, searchDate, sessionTypes,listOfTagAndTrackIds,isPast,isUpcoming);
        } else {
            return sessionService.getSessionHostListWithSorting(expand + ",STATS", event, searchString, PageRequest.of(Math.max(page, 0), (size>0)?size:10, by), isFromBillingPage,sessionTypeFormats, searchDate, sessionTypes,listOfTagAndTrackIds,isPast,isUpcoming);
        }
    }



    @GetMapping("/calendar-view")
    public List<SessionCalendarViewDTO> getSessionListOfCalendarView(
            @RequestParam(required = false) String searchString,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            @RequestParam(name="sessionTypes",defaultValue = "") List<EnumSessionFormat> sessionTypes,
            @RequestParam(required = false,name = "tagIds") String tagIds,
            @RequestParam(required = false,name= "trackIds") String trackIds,
            @RequestParam(required = false, name= "speakerIds") String speakerIds,
            @RequestParam(required = false, name= "locationIds") String locationIds,
            @Parameter(description = START_DATE) @RequestParam(required = false, name= "date") String date,
            @RequestParam(required = false, name="filter") String filter,
            Authentication auth) {

        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        List<Long> listOfTagAndTrackIds=sessionService.getListOfIdsForTagAndTrackForSorting(tagIds,trackIds);
        return sessionService.getSessionCalendarViewList(userEvent.getEvent(), searchString, sessionTypes,listOfTagAndTrackIds, speakerIds, locationIds, date, filter);

    }

    @GetMapping("/unscheduled-sessions")
    public DataTableResponse getUnscheduledSessions(
            @RequestParam(required = false) String searchString,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(name="sessionTypes",defaultValue = "") List<EnumSessionFormat> sessionTypes,
            @RequestParam(required = false,name = "tagIds") String tagIds,
            @RequestParam(required = false,name= "trackIds") String trackIds,
            @RequestParam(required = false, name= "speakerIds") String speakerIds,
            @RequestParam(required = false, name= "locationIds") String locationIds,
            Authentication auth) {

        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        List<Long> listOfTagAndTrackIds=sessionService.getListOfIdsForTagAndTrackForSorting(tagIds,trackIds);
        return sessionService.getUnscheduledSessionsOfEvent(userEvent.getEvent(), searchString, sessionTypes,listOfTagAndTrackIds, speakerIds, locationIds, page, size);

    }


    @PutMapping("/{id}")
    public void
    updateSession(@RequestBody @Validated SessionDTO sessionDTO,
                  @PathVariable Long id,
                  @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                  Authentication auth, HttpServletRequest httpRequest) {
        log.info("Update session Id {}",id);
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session oldSession = sessionRepoService.getSessionByIdWithoutCache(id, userEvent.getEvent());
        sessionService.checkNonVisualSessionCount(sessionDTO.getFormat(), userEvent.getEvent(), oldSession.getFormat(), true);
        EnumChannelStatus channelStatus = sessionService.updateSession(id, sessionDTO, userEvent.getEvent(), userEvent.getUser());
        if (null != channelStatus) {
            if (channelStatus.equals(EnumChannelStatus.UPDATE)) {
                sessionThirdPartyService.updateChannelName(id, sessionDTO.getTitle(), sessionDTO.getFormat(),!sessionDTO.isChatEnabled(), userEvent.getEvent().getEventId());
            } else if (channelStatus.equals(EnumChannelStatus.CREATE)) {
                sessionThirdPartyService.createChannel(id, sessionDTO.getTitle(), sessionDTO.getFormat(), userEvent.getEvent());
            } else if (channelStatus.equals(EnumChannelStatus.DELETE)) {
                sessionThirdPartyService.deleteSessionChannel(id, userEvent.getEvent().getEventId());
            }
        }

    }

    @GetMapping("/stop/sessions")
    public void endExceededSessions() {
        List<Session> sessions = new ArrayList<>(0);
        List<Session> sessionList = sessionRepoService.getActiveExtendedSessions(StreamProvider.ACCELEVENTS, new Date());
        sessionList.forEach(session -> {
            if (isSessionEnded(session)) {
                sessionThirdPartyService.stopConference(String.valueOf(session.getId()));
                //stop session
                session.setSessionStartStatus(EnumSessionStartStatus.STOPPED);
                sessions.add(session);
            }
        });
        sessionRepoService.saveAll(sessions);
    }


    //@Operation(description = "Update upload id and file name")
    @PutMapping("/{id}/upload-recording")
    public MuxAssetDTO uploadSessionRecording(@PathVariable Long id,
                                                   @RequestBody DirectUploadDto uploadDto,
                                                   Authentication auth, HttpServletRequest httpServletRequest) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, uploadDto.getEventUrl());
        return sessionService.uploadSessionRecording(id, uploadDto, userEvent.getEvent(), userEvent.getUser(), httpServletRequest.getHeader(Authorization));
    }

    //@Operation(description = "Update upload id and file name")
    @PutMapping("/{id}/directUpload")
    public MuxAssetDTO updateDirectUploadIdAndFile(@PathVariable Long id,
                                                   @RequestBody DirectUploadDto uploadDto,
                                                   Authentication auth, HttpServletRequest httpServletRequest) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, uploadDto.getEventUrl());

        MuxAssetDTO muxAssetDTO = sessionThirdPartyService.getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(id, userEvent.getEvent(), uploadDto.getUploadId(), AssetType.SESSION_ASSET,  uploadDto.getTitle(), uploadDto.getDescription());
        if (muxAssetDTO != null) {
            log.info("Direct uploaded video eventUrl {} and uploadId {} playBackRestrictionToken {}", uploadDto.getEventUrl(), uploadDto.getUploadId(), uploadDto.getPlayBackRestrictionToken());

            Session session = sessionService.updateDirectUploadIdAndFile(id, muxAssetDTO.getPlayBackId(), StreamProvider.DIRECT_UPLOAD,userEvent.getEvent(), muxAssetDTO.getPlayBackRestrictionToken(), muxAssetDTO.getThumbnailRestrictionToken());
            log.info("Direct uploaded video for session {} with event {}", session.getId(), userEvent.getEvent().getEventId());
            if (null != muxAssetDTO.getAssetStatus()) {
                String assetStatus = muxAssetDTO.getAssetStatus();
                String status = assetStatus.equals("preparing") ? MuxEventType.VIDEO_ASSET_STATIC_RENDITIONS_PREPARING.getValue()
                        : MuxEventType.VIDEO_ASSET_READY.getValue();
                new BroadcastGraphQLHandler(graphQLConfiguration, httpServletRequest.getHeader(Authorization))
                        .handleCreateOrUpdateSessionStatusLogs(status, session, userEvent.getUser()
                        );
            }

        } else {
            throw new NotFoundException(NotFoundException.NotFound.MUX_ASSET_DETAIL_NOT_FOUND);
        }
        return muxAssetDTO;
    }

    //@Operation(description = "Set default playback id after live stream ended")
    @PutMapping("/{id}/default-playback/{playbackId}")
    public ResponseDto setDefaultPlayBackForSession(@PathVariable Long id,
                                                   @PathVariable Long playbackId,
                                                    @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                   Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.setDefaultPlayBackForSession(id, playbackId, userEvent.getEvent());
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    //@Operation(description = "Get default playback for session")
    @GetMapping("/{id}/default-playback")
    public MuxAssetDTO getDefaultPlayBackForSession(@PathVariable Long id,
                                                    @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                    Authentication auth) {
        authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.findDefaultPlayBackForSession(id , AssetType.SESSION_ASSET);
    }
    //@Operation(description = "Get default playback for session")
    @GetMapping("/{id}/playbacks")
    public List<MuxAssetDTO> getPlayBacksForSession(@PathVariable Long id,
                                                    @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                    Authentication auth) {
        authValidator.authHostLevelAccess(auth, eventUrl);
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getSessionPlayBacks(id , userEvent.getEvent());
    }

    private boolean isSessionEnded(Session session) {
        return session.getEndTime() != null && (new Date().compareTo(DateUtils.getAddedMinutes(session.getEndTime(), 20)) > 0);
    }


    //@Operation(description = "Duplicate session")
    @PostMapping("/{id}/duplicate")
    public void duplicateSession(Authentication auth,
                                 @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                 @PathVariable("id") Long sessionId) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session oldSession = sessionRepoService.getSessionByIdWithoutCache(sessionId, userEvent.getEvent());
        sessionService.checkNonVisualSessionCount(oldSession.getFormat(), userEvent.getEvent(), oldSession.getFormat(), false);
        log.info("duplicateSession  sessionId {}  DuplicateSession  event {} ",  sessionId, userEvent.getEvent().getEventId());
        Session session = sessionService.duplicateSession(sessionId, userEvent.getEvent(), userEvent.getUser());
        if(StreamProvider.ACCELEVENTS.equals(session.getStreamProvider())){
            sessionThirdPartyService.createStreamKey(session.getId(), userEvent.getEvent(),false, new CaptionsDto());
        }
        log.info("findAllEventAdminId event {} ",  userEvent.getEvent().getEventId());
        List<Long> eventAdmins = staffService.findAllEventAdminId(userEvent.getEvent());
        sessionThirdPartyService.createChannelByListOfSessionIds(Collections.singletonList(session), eventAdmins);
    }

    //@Operation(description = "Download Session Attendees CSV")
    @GetMapping("/{id}/download/attendees/CSV")
    public void downloadSessionAttendeesCSV(HttpServletResponse response, Authentication auth,
                                            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                            @PathVariable("id") Long sessionId) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        CommonUtil.prepareDownloadableResponseHeader(response, session.getTitle(), CONTENT_CSV);
        downloadService.downloadSessionAttendeesCSV(response.getWriter(),session);
    }

    //@Operation(description = "Download Registrants CSV")
    @GetMapping("/{id}/download/registrants/CSV")
    public void downloadRegistrantsCSV(HttpServletResponse response, Authentication auth,
                                       @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                       @PathVariable("id") Long sessionId) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        CommonUtil.prepareDownloadableResponseHeader(response, session.getTitle(), CONTENT_CSV);
        downloadService.downloadRegistrantsCSV(response.getWriter(),session);
    }

    //@Operation(description = "Download Session checkIn/CheckOut Attendees log CSV")
    @GetMapping("/{id}/download/checkIn/attendeesLog/CSV")
    public void downloadSessionCheckInLogCSV(HttpServletResponse response, Authentication auth,
                                       @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                       @PathVariable("id") Long sessionId) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        CommonUtil.prepareDownloadableResponseHeader(response, session.getTitle(), CONTENT_CSV);
        downloadService.downloadSessionCheckInLogCSV(response.getWriter(),session);
    }

    //@Operation(description = "Download Polls CSV")
    @GetMapping("/{id}/download/polls/CSV")
    public void downloadPollsCSV(HttpServletResponse response, Authentication auth,@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                 @PathVariable("id") Long sessionId) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        CommonUtil.prepareDownloadableResponseHeader(response, session.getTitle()+"_Polls", CONTENT_CSV);
        downloadService.downloadPolls(response.getWriter(), session.getId(), EnumQNAType.SESSION, userEvent.getEvent(), Boolean.TRUE);
    }

    //@Operation(description = "Download Registrants CSV")
    @GetMapping("/{id}/download/questionAnswers/CSV")
    public void downloadQuestionAnswersCSV(HttpServletResponse response, Authentication auth,
                                           @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                           @PathVariable("id") Long sessionId) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        CommonUtil.prepareDownloadableResponseHeader(response, session.getTitle()+"_Q&A", CONTENT_CSV);
        downloadService.downloadQuestionAnswers(response.getWriter(),session.getId(), EnumQNAType.SESSION, userEvent.getEvent(), Boolean.TRUE);
    }

    //@Operation(description = "Download Chat CSV")
    @GetMapping("/{id}/download/chat/CSV")
    public void downloadChatCSV(HttpServletResponse response, Authentication auth,
                                @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                @PathVariable("id") Long sessionId) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        CommonUtil.prepareDownloadableResponseHeader(response, session.getTitle()+UNDERSCORE_CHAT, CONTENT_CSV);
        downloadService.downloadChatCSV(response.getWriter(), SESSION_.concat(Long.toString(session.getId())), session.getTitle(), LIVESTREAM,userEvent.getEvent());
    }

    //@Operation(description = "Remove recorded video for session")
    @DeleteMapping("/recordedSessionVideo/{playbackId}")
    public ResponseDto removeRecordedSessionVideo(@PathVariable Long playbackId,
                                                  @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                  Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.removeRecordedSessionVideo(playbackId);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    @GetMapping("/download/session-list/CSV")
    public void downloadSessionListCSV(HttpServletResponse response,
                                       @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                       Authentication auth) throws IOException{
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        CommonUtil.prepareDownloadableResponseHeader(response, "Session List", CONTENT_CSV);
        downloadService.downloadSessionListCSV(response.getWriter(), userEvent.getEvent());
    }

    //@Operation(description = "Upload caption or subtitle file")
    @DeleteMapping("/{id}/remove-caption-file/{subtitleId}")
    public ResponseDto removeCaptionFile(@PathVariable Long id,@PathVariable Long subtitleId,
                                         @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                         Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.removeSubTitleFileInSession(id,subtitleId,userEvent.getEvent() );

        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    @GetMapping("/sessionDropDown")
    public List<SessionIdTitleDto> getSessionIdTitleList(Authentication auth,
                                                         @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                         @RequestParam(required = false, defaultValue = "") String sessionFormat){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        EnumSessionFormat enumSessionFormats;
        if (StringUtils.isNotBlank(sessionFormat) && EnumUtils.isValidEnum(EnumSessionFormat.class, sessionFormat)) {
            enumSessionFormats = EnumSessionFormat.valueOf(sessionFormat);
        } else {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_SESSION_FORMAT);
        }
        return sessionService.getSessionDropDownList(userEvent.getEvent(),List.of(enumSessionFormats));
    }

    //@Operation(description = "Update session position")
    @PostMapping("/{id}/topSession/{topSessionId}/topBottomSession/{topBottomSessionId}")
    public ResponseDto updateSessionPosition(
            @PathVariable("id") Long sessionId,
            @PathVariable Long topSessionId,
            @PathVariable Long topBottomSessionId,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.updateSessionSequence(sessionId, topSessionId, topBottomSessionId, userEvent.getEvent());
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SESSION_POSITION_CHANGED_SUCCESSFULLY)));
    }

    //@Operation(description = "Sort Session Position By Title")
    @PutMapping("/sortSessionPositionByTitle/{eventId}/{sortType}")
    public ResponseDto sortSessionPositionByTitle(@PathVariable("eventId") Long eventId, @PathVariable("sortType") String sortType,
                                                  @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                  Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.sortSessionPositionByTitle(eventId, sortType);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SESSIONS_SORTED_SUCCESSFULLY)));
    }

    //@Operation(description = "Session Mass Operation")
    @PostMapping("/massOperation")
    public ResponseDto sessionMassOperation(@RequestParam EnumSessionMassOperation enumSessionMassOperation,
                                            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                            @RequestBody(required = false) SessionMassOperationConfigDTO sessionMassOperationConfigDTO,
                                            Authentication auth,
                                            HttpServletRequest httpRequest) {
            UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.sessionMassOperation(userEvent.getEvent(), userEvent.getUser(), enumSessionMassOperation, sessionMassOperationConfigDTO.getSessionIds(),languageMap, sessionMassOperationConfigDTO,httpRequest, httpRequest.getHeader(Authorization));
    }

    //@Operation(description = "Get Session Post Call To Action Dropdown details")
    @GetMapping("/sessionPostCallToAction")
    public PostSessionCallToActionDropDownDto sessionPostCallToActionDropDown(Authentication auth,
                                                                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getFutureSessionExpoLoungesByEvent(userEvent.getEvent());
    }

    //@Operation(description = "Get Session Post Call To Action Dropdown details")
    @GetMapping("/sessionPostCallToAction/{sessionId}")
    public PostSessionCallToActionDropDownDto sessionPostCallToActionDropDownBySessionId(Authentication auth,
                                                                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                                         @Parameter(description = "sessionId") @PathVariable long sessionId) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getFutureSessionExpoLoungesByEventAndSessionId(userEvent.getEvent(),sessionId);
    }

    //@Operation(description = "Start/Stop Workshop Recording and Update Recoding Status")
    @GetMapping("/recording/{sessionId}/{status}")
    public ResponseDto startOrStopWorkshopRecording(@PathVariable("sessionId") Long sessionId, @PathVariable("status") String status, Authentication auth,
                                                    @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.startOrStopWorkshopRecording(sessionId, userEvent.getEvent(), status);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    //@Operation(description = "sorted sessions with date and time")
    @GetMapping("/getSortData")
    public DataTableResponse getSessionListByDateAndTime(
            @RequestParam(required = false) Long eventId,
            @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false, defaultValue = "") String expand,
            @RequestParam(value = "sort[field]", required = false) String sortField,
            @RequestParam(value = "sort[direction]", required = false) String sortDirection,
            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
            Authentication auth) {

        Event event = authValidator.authEventOrGetEvent(eventId, auth, eventUrl);
        expand = expand + ",STATS";
        return sessionService.getSortedSessionWithDateAndTime(expand, event,PageRequest.of(Math.max(page, 0), (size>0)?size:10),sortField,sortDirection);
    }

    //@Operation(description = "Get total Session,Registered/CheckIn attendee by event url")
    @GetMapping("/session/registered/analytics")
    public UserSessionAnalyticsDTO getSessionCountWithUserSessionRegistered( @Parameter(description = "Event url") @PathVariable(name = "eventUrl",required = false) String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getSessionCountWithUserSessionRegistered(userEvent.getEvent());
    }

    @GetMapping("/all-session-title")
    public List<SessionListDto> getSessionTitleList(Authentication auth, @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getAllSessionByEventId(userEvent.getEvent());
    }

    @Operation(description = "update sessions in bulk by session ids and attributes")
    @PostMapping("/update-sessions-in-bulk")
    public ResponseDto updateSessionsInBulk(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                            @RequestBody(required = false) SessionBulkUpdatesDto sessionBulkUpdatesDto,Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.updateSessionsInBulk(userEvent.getEvent(), userEvent.getUser(), sessionBulkUpdatesDto);
    }

    @Operation(description = "update sessions status in bulk by session ids")
    @PostMapping("/update-sessions-status-in-bulk")
    public BulkSessionStatusUpdateResultDto updateSessionsStatusInBulk(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                       @RequestParam("sessionIds") List<Long> sessionIds, @RequestParam("status") EnumSessionStatus status, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.updateSessionsStatusInBulk(userEvent.getEvent(), userEvent.getUser(), sessionIds, status);
    }

    @GetMapping("/basic-detail")
    public DataTableResponse getAllSessionsByEvent(Authentication auth, @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getAllSessionsListByEvent(userEvent.getEvent());
    }

    @PostMapping("/sessionDropDown")
    public List<SessionIdTitleDto> getSessionIdTitleList(Authentication auth,
                                                         @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                         @RequestBody(required = false) List<EnumSessionFormat> sessionFormats){
        if (CollectionUtils.isEmpty(sessionFormats)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_SESSION_FORMAT);
        }
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getSessionDropDownList(userEvent.getEvent(),sessionFormats);
    }


    @PostMapping("/location")
    public ResponseDto addLocation(Authentication auth,
                                                         @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                         @Validated @RequestBody SessionLocationDTO sessionLocationDTO){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionLocationService.addSessionLocation(sessionLocationDTO, userEvent.getEvent(),userEvent.getUser());
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }


    @PutMapping("/location/{id}")
    public ResponseDto updateLocation(Authentication auth,
                                      @PathVariable Long id,
                                      @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                      @Validated @RequestBody SessionLocationDTO sessionLocationDTO){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        log.info("Update session location Id : {}",id);
        sessionLocationService.updateSessionLocation(id,sessionLocationDTO, userEvent.getEvent(),userEvent.getUser());
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    @DeleteMapping("/location/{id}")
    public ResponseDto deleteSessionLocation(Authentication auth,
                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                              @PathVariable("id") Long sessionId) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionLocationService.deleteSessionLocation(sessionId, userEvent.getEvent(),userEvent.getUser());
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(userEvent.getUser(), userEvent.getEvent());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    @GetMapping("/locations")
    public DataTableResponse getAllSessionLocations(Authentication auth,@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                           @RequestParam(required = false) String searchString,
                                                           @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
                                                           @RequestParam(defaultValue = "10") int size) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Pageable pageable = PageRequest.of(Math.max(page, 0), (size>0)?size:10);
        return sessionLocationService.getAllSessionLocations(userEvent.getEvent(),pageable,searchString);
    }

    @GetMapping("/location/{id}")
    public SessionLocationDTO getSessionLocationById(Authentication auth,
                                                           @PathVariable("id") Long id,
                                                           @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionLocationService.getSessionLocationById(id,userEvent.getEvent());
    }

    @PostMapping("/check-location-availability")
    public Optional<List<String>> checkLocationAvailability(Authentication auth,
                                          @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                          @RequestBody SessionLocationDTO sessionLocationDTO){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionLocationService.checkSessionLocationAvailability(sessionLocationDTO, userEvent.getEvent());
    }

    @GetMapping("/download/session-location/CSV")
    public void downloadSessionLocationListCSV(HttpServletResponse response,
                                       @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                       Authentication auth) throws IOException{
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        CommonUtil.prepareDownloadableResponseHeader(response, "Session Location List", CONTENT_CSV);
        downloadService.downloadSessionLocationListCSV(response.getWriter(), userEvent.getEvent());
    }

    @PostMapping("/import/session-location/CSV")
    public List<UploadSessionLocationResponseContainer> importSessionLocationListCSV(@RequestParam("locationFile") MultipartFile multiPartFile,
                                                                                     @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                                                     Authentication auth) throws IOException{
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionLocationService.importSessionLocationList(multiPartFile, userEvent.getEvent(), userEvent.getUser());
    }

    @GetMapping("/waitlisted-users-details/{sessionId}")
    public DataTableResponse getWaitlistedUsersInfoById(@PathVariable("sessionId") Long sessionId, @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                 @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
                                                 @RequestParam(defaultValue = "10") int size,
                                                 @RequestParam(required = false) String searchString,
                                                 Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getWaitlistedUserDetailsBySessionId(sessionId, userEvent.getEvent(), userEvent.getUser(),new PageSizeSearchObj(page,size),searchString);
    }

    @PostMapping("/register-waitlisted-user")
    public ResponseDto registerWaitlistedUsers(@RequestParam("userSessionId") Long userSessionId,@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.registerWaitlistedUserByUserSessionId(userEvent.getEvent(), userEvent.getUser(),userSessionId);
        return new ResponseDto(Constants.SUCCESS, Constants.REGISTERED_WAITLISTED_USER_SUCCESSFULLY);
    }

    @DeleteMapping("/remove-waitlisted-user/{userSessionId}")
    public ResponseDto removeWaitlistedUsers(@PathVariable("userSessionId") Long userSessionId, @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.removeWaitlistedUserByUserSessionId(userEvent.getEvent(), userEvent.getUser(),userSessionId);
        return new ResponseDto(Constants.SUCCESS, Constants.REMOVED_WAITLISTED_USER_SUCCESSFULLY);
    }

    @PostMapping("/add-attendee-to-waitlist/{sessionId}")
    public ResponseDto addAttendeeToWaitlist(@PathVariable("sessionId") Long sessionId, @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl, @RequestBody List<Long> userIds,Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        sessionService.addAttendeesToWaitlistBySessionId(userEvent.getEvent(),userIds,sessionId);
        return new ResponseDto(Constants.SUCCESS, Constants.USER_ADDED_TO_WAITLIST_SUCCESSFULLY);
    }

    @PostMapping(value = "/all-attendees/{sessionId}")
    public DataTableResponse getAllAttendeesWithoutWaitlist(@RequestBody PageSizeSearchObj pageSizeSearchObj,
                                                            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,@PathVariable("sessionId") Long sessionId,
                                                            Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.getAllAttendeeListByEvent(userEvent.getEvent(),new PageSizeSearchObj(pageSizeSearchObj.getPage(),pageSizeSearchObj.getSize(),pageSizeSearchObj.getSearch()), sessionId);
    }

    @GetMapping("/{id}/download/waitlisted-users/CSV")
    public void downloadWaitlistedUsersCSV(HttpServletResponse response, Authentication auth,
                                       @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                       @PathVariable("id") Long sessionId) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Session session = sessionRepoService.getSessionById(sessionId, userEvent.getEvent());
        CommonUtil.prepareDownloadableResponseHeader(response, session.getTitle(), CONTENT_CSV);
        downloadService.downloadWaitlistedUsersCSV(response.getWriter(),session);
    }
}
