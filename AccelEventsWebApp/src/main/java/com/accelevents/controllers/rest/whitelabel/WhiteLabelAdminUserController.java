package com.accelevents.controllers.rest.whitelabel;

import com.accelevents.common.dto.StaffDetail;
import com.accelevents.common.dto.StaffDetailDto;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.dto.AttendeeAnalyticsPageSizeSearchObj;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.ResponseDto;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.exceptions.ForbiddenException.UserForbiddenExceptionMsg;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.StaffService;
import com.accelevents.services.WhiteLabelService;
import com.accelevents.utils.UserUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.accelevents.utils.Constants.SUCCESS;

@RestController
@RequestMapping("/rest/wl-admin/wl-url/{whiteLabelURL}/users")
@Tag(name = "/rest/wl-admin/wl-url/{whiteLabelURL}/users")
public class WhiteLabelAdminUserController {

	@Autowired
	private WhiteLabelService whiteLabelService;
	@Autowired
	private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;
	@Autowired
	private UserUtils userUtils;

	//@Operation(description = "all staff detail")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping("/staffs")
	public DataTableResponse whiteLabelUsers(@PathVariable String whiteLabelURL,
                                             @RequestParam (name= "search" ,required = false, defaultValue = "") String search,
                                             @RequestParam(name = "sortColumn", defaultValue = "id") String sortColumn,
                                             @RequestParam(name = "sortDirection" , defaultValue = "DESC") String sortDirection,
                                             @RequestParam(defaultValue = "0") int page,
                                             @RequestParam(defaultValue = "10") int size,
                                             Authentication auth) {
		User user = this.userUtils.getUser(auth);
		WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
		if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(user, whiteLabel)) {
            AttendeeAnalyticsPageSizeSearchObj searchObj = new AttendeeAnalyticsPageSizeSearchObj(
                    page, size, search,sortColumn,sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC);
            return staffService.findStaffDtoByWhiteLabel(whiteLabel,searchObj);
		} else {
			throw new ForbiddenException(UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
		}
	}

	//@Operation(description = "add white label staff")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PostMapping(path = { "/staff" })
	public ResponseDto add(@PathVariable String whiteLabelURL, @RequestBody @Validated StaffDetail staff, Authentication auth) {
		User whiteLabelUser = this.userUtils.getUser(auth);
		WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
		if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
			try {
				staffService.addWhiteLabelAdmin(whiteLabel, staff,whiteLabelUser);
				return new ResponseDto(SUCCESS, SUCCESS);
			} catch (IOException e) {
				throw new NotAcceptableException(e);
			}
		} else {
			throw new ForbiddenException(UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
		}
	}

	//@Operation(description = "update white label staff")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PutMapping(path = { "/staff/{id}" })
	public ResponseDto update(@PathVariable Long id, @PathVariable String whiteLabelURL, @RequestBody @Validated StaffDetail staff,
			Authentication auth) {
		User whiteLabelUser = this.userUtils.getUser(auth);
		WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
		if (this.roStaffService.hasWhiteLabelAdminAccess(whiteLabelUser, whiteLabel)) {
			try {
				staffService.updateWhiteLabelAdmin(whiteLabel, id, staff, whiteLabelUser);
				return new ResponseDto(SUCCESS, SUCCESS);
			} catch (IOException e) {
				throw new NotAcceptableException(e);
			}
		} else {
			throw new ForbiddenException(UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
		}
	}

	//@Operation(description = "delete white label staff")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@DeleteMapping(path = { "/staff/{id}" })
	public ResponseDto delete(@PathVariable Long id, @PathVariable String whiteLabelURL, Authentication auth) {
		User whiteLabelUser = this.userUtils.getUser(auth);
		WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
		if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
			staffService.deleteWhiteLabelStaff(whiteLabel, id, whiteLabelUser);
			return new ResponseDto(SUCCESS, SUCCESS);
		} else {
			throw new ForbiddenException(UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
		}
	}

	//@Operation(description = "resend email white label staff")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PostMapping(path = { "/resendConfirmation/staff/{id}" })
	public ResponseDto resendEmail(@PathVariable Long id, @PathVariable String whiteLabelURL, Authentication auth) {
		User whiteLabelUser = this.userUtils.getUser(auth);
		WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
		if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
			try {
				staffService.resendWhiteLabelStaffInvitation(whiteLabel, id);
				return new ResponseDto(SUCCESS, "Your invitation has been resent.");
			} catch (IOException e) {
				throw new NotAcceptableException(e);
			}
		} else {
			throw new ForbiddenException(UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
		}
	}
}
