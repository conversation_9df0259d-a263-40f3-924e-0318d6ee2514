package com.accelevents.controllers.rest;

import com.accelevents.common.dto.RedirectUrlDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.AttributeFormType;
import com.accelevents.dto.*;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.security.tokenstore.TokenStoreService;
import com.accelevents.services.*;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DecoderUtil;
import com.accelevents.utils.UserUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.accelevents.exceptions.NotFoundException.UserNotFound.USER_NOT_FOUND;
import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping("rest/u/sso")
@Tag(name = "SSO", description = "Operations for sso user")
public class SSOUserController {

    private static final Logger log = LoggerFactory.getLogger(SSOUserController.class);

    @Autowired
    private SSOUserService ssoUserService;
    @Autowired
    private UserService userService;
    @Autowired
    private TokenStoreService tokenStoreService;
    @Autowired
    private UserUtils userUtils;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private EventLevelSettingService eventLevelSettingService;
    @Autowired
    private EventService eventService;
    @Autowired
    private SSOFieldMappingService ssoFieldMappingService;
    @Autowired
    PreRegistrationRoutingRulesService preRegistrationRoutingRulesService;

    @Value("${uiBaseurl}")
    private String uiBaseUrl;

    //@Operation(description = "Get SSO Configuration", response = WhitelabelSSOConfigurationDTO.class)
    @GetMapping(value = "/config/whiteLabelURL/{whiteLabelUrl}", produces = APPLICATION_JSON)
    public List<WhitelabelSSOConfigurationDTO> getSSOConfigurationForWhitelabel(@PathVariable String whiteLabelUrl, @RequestParam(defaultValue = "false") boolean isWlLoginPage) {
        log.debug("Trying to login using sso user, {}", whiteLabelUrl);
        return ssoUserService.getSSOWhiteLabelConfiguration(whiteLabelUrl, isWlLoginPage);
    }

    //@Operation(description = "Add SSO Configuration", response = ResponseDto.class)
    @PostMapping(value = "/config/addSSOConfiguration/whiteLabelURL/{whiteLabelUrl}", produces = APPLICATION_JSON)
    public ResponseDto addSSOConfigurationForWhitelabel(@PathVariable String whiteLabelUrl, @RequestBody WhitelabelSSOConfigurationDTO whitelabelSSOConfigurationDTO, Authentication auth) {
        User user = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        if (this.roStaffService.hasWhiteLabelAdminAccess(user,whiteLabel)) {
            ssoUserService.addSSOWhiteLabelConfiguration(whiteLabelUrl, whitelabelSSOConfigurationDTO,whiteLabel);
            return new ResponseDto(Constants.SUCCESS, Constants.CONFIGURATION_ADDED_SUCCESSFULLY);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
        }
    }

    //@Operation(description = "Update SSO Configuration", response = ResponseDto.class)
    @PutMapping(value = "/config/updateSSOConfiguration/whiteLabelURL/{whiteLabelUrl}/configId/{id}", produces = APPLICATION_JSON)
    public ResponseDto updateSSOConfigurationForWhitelabel(@PathVariable Long id, @PathVariable String whiteLabelUrl, @RequestBody WhitelabelSSOConfigurationDTO whitelabelSSOConfigurationDTO, Authentication auth) {
        User user = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        if (this.roStaffService.hasWhiteLabelAdminAccess(user,whiteLabel)) {
            ssoUserService.updateSSOWhiteLabelConfiguration(whitelabelSSOConfigurationDTO, id);
            return new ResponseDto(Constants.SUCCESS, Constants.CONFIGURATION_UPDATED_SUCCESSFULLY);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
        }
    }

    //@Operation(description = "Delete SSO Configuration", response = ResponseDto.class)
    @DeleteMapping(value = "/config/deleteSSOConfiguration/whiteLabelURL/{whiteLabelUrl}/configId/{id}", produces = APPLICATION_JSON)
    public ResponseDto deleteSSOConfigurationForWhitelabel(@PathVariable Long id,@PathVariable String whiteLabelUrl ,Authentication auth) {
        User user = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        if (this.roStaffService.hasWhiteLabelAdminAccess(user,whiteLabel)) {
            ssoUserService.deleteSSOWhiteLabelConfiguration(id);
            return new ResponseDto(Constants.SUCCESS, Constants.CONFIGURATION_DELETED_SUCCESSFULLY);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
        }
    }

    //@Operation(description = "SSO User login", response = AccessTokenContainer.class)
    @PostMapping(value = "/login/whiteLabelURL/{whiteLabelUrl}", consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    public AccessTokenContainer loginUser(@PathVariable String whiteLabelUrl, @RequestBody @Validated SSOAuthenticationDTO ssoAuthenticationDTO) {
        try {
            log.info("Trying to login using sso user, {}", whiteLabelUrl);
            AccessTokenModel accessTokenModel = ssoUserService.validateTokenAndGetUserEmail(whiteLabelUrl, ssoAuthenticationDTO);
            accessTokenModel.setLoginUsingSso(true);
            accessTokenModel.setSsoIdentityProvidersType(AccessTokenModel.SSOIdentityProvidersType.OKTA);
            AccessTokenContainer container = this.tokenStoreService.generateAccessToken(accessTokenModel);
            RedirectUrlDto redirectUrlDto = userUtils.getWhiteLabelRedirectUrl(accessTokenModel, whiteLabelUrl);
            if (StringUtils.isEmpty(ssoAuthenticationDTO.getRedirectUrl())) {
                log.info("Found redirect URL from white label config while login with okta: {}", redirectUrlDto.getRedirectUrl());
                container.setRedirectUrl(redirectUrlDto.getRedirectUrl());
            } else {
                log.info("Using redirect URL from request param while login with okta: {}", ssoAuthenticationDTO.getRedirectUrl());
                container.setRedirectUrl(ssoAuthenticationDTO.getRedirectUrl());
            }
            container.setUserRole(redirectUrlDto.getUserRole());
            container.setWhiteLabelURL(redirectUrlDto.getWhiteLabelURL());
            return container;
        } catch (Exception e) {  //NOSONAR
            log.info("Something went wrong while login using sso for{}",Arrays.toString(e.getStackTrace()));
            throw e;
        }
    }


    @GetMapping("/login-with-salesforce/{whiteLabelUrl}")
    public AccessTokenContainer getSalesforceAccessToken(@PathVariable String whiteLabelUrl,@RequestParam("code") String code,@RequestParam("code_verifier") String codeVerifier,
                                                      @RequestParam("client_id")String clientId,@RequestParam(value = "redirectUrl",required = false) String redirectUrl) {
        try {
            AccessTokenModel accessTokenModel = ssoUserService.validateTokenAndGetUserEmailForSalesforce(whiteLabelUrl, code, codeVerifier, clientId);
            accessTokenModel.setLoginUsingSso(true);
            accessTokenModel.setSsoIdentityProvidersType(AccessTokenModel.SSOIdentityProvidersType.SALESFORCE);
            AccessTokenContainer container = this.tokenStoreService.generateAccessToken(accessTokenModel);
            RedirectUrlDto redirectUrlDto = userUtils.getWhiteLabelRedirectUrl(accessTokenModel, whiteLabelUrl);
            if (StringUtils.isEmpty(redirectUrl)) {
                log.info("Found redirect URL from white label config while login with salesforce: {}", redirectUrlDto.getRedirectUrl());
                container.setRedirectUrl(redirectUrlDto.getRedirectUrl());
            } else {
                log.info("Using redirect URL from request param while login with salesforce: {}", redirectUrl);
                container.setRedirectUrl(redirectUrl);
            }
            container.setUserRole(redirectUrlDto.getUserRole());
            container.setWhiteLabelURL(redirectUrlDto.getWhiteLabelURL());
            return container;
        }catch (Exception e) {
            log.info("Something went wrong while trying to login using sso for salesforce {}",Arrays.toString(e.getStackTrace()));
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @GetMapping("/is-salesforce-mandatory-for-login/{whiteLabelUrl}")
    public boolean isSalesforceMandatoryForLogin(@PathVariable("whiteLabelUrl")String whiteLabelUrl) {
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        return whiteLabel != null && whiteLabel.isSalesforceLoginRequired();
    }

    @GetMapping("/is-sso-required-for-login/event/{eventUrl}")
    public boolean isSsoRequiredForLogin(@PathVariable("eventUrl")String eventUrl) {
        Event event = eventService.getEventByURLOrThrowException(eventUrl);
        return eventLevelSettingService.isSsoRequired(event.getEventId());
    }

    @GetMapping("/is-sso-mapping-enabled/event/{eventUrl}")
    public boolean isSsoMappingEnabled(@PathVariable("eventUrl")String eventUrl) {
        Event event = eventService.getEventByURLOrThrowException(eventUrl);
        return eventLevelSettingService.isSsoMappingEnabled(event.getEventId());
    }

    @GetMapping("/event/{eventUrl}/mappings/{attributeFormType}")
    public List<SSOFieldMappingDTO> getSSOMappigns(@PathVariable("eventUrl") String eventUrl,
                                                   @PathVariable("attributeFormType") AttributeFormType attributeFormType) {
        Event event = eventService.getEventByURLOrThrowException(eventUrl);
        return ssoFieldMappingService.getSSOMapping(event, attributeFormType);
    }

    @GetMapping("/login-with-ping/{whiteLabelUrl}")
    public AccessTokenContainer loginWithPing(@PathVariable String whiteLabelUrl,
                                              @RequestParam("code") String code,
                                              @RequestParam("code_verifier") String codeVerifier,
                                              @RequestParam("client_id") String clientId,
                                              @RequestParam(value = "redirectUrl",required = false) String redirectUrl) {
        try {
            AccessTokenModel accessTokenModel = ssoUserService.validateTokenAndGetUserEmailForPing(whiteLabelUrl, code, codeVerifier, clientId);
            accessTokenModel.setLoginUsingSso(true);
            AccessTokenContainer container = tokenStoreService.generateAccessToken(accessTokenModel);
            RedirectUrlDto redirectUrlDto = userUtils.getWhiteLabelRedirectUrl(accessTokenModel, whiteLabelUrl);
            if (StringUtils.isEmpty(redirectUrl)) {
                log.info("Found redirect URL from white label config while login with ping : {}", redirectUrlDto.getRedirectUrl());
                container.setRedirectUrl(redirectUrlDto.getRedirectUrl());
            } else {
                log.info("Using redirect URL from request param while login with ping: {}", redirectUrl);
                container.setRedirectUrl(redirectUrl);
            }
            container.setUserRole(redirectUrlDto.getUserRole());
            container.setWhiteLabelURL(redirectUrlDto.getWhiteLabelURL());
            return container;
        } catch (Exception e) {
            log.info("Ping SSO login failed: {}", Arrays.toString(e.getStackTrace()));
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @PutMapping("/update-ping-app-details/{whiteLabelUrl}")
    public ResponseDto updatePingAppDetails(@PathVariable String whiteLabelUrl,Authentication auth, @RequestBody PingSSOCredentialsDto pingSSOCredentialsDto) {
        User user = userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        if (this.roStaffService.hasWhiteLabelAdminAccess(user,whiteLabel)) {
            return preRegistrationRoutingRulesService.updatePingAppDetails(pingSSOCredentialsDto);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    /**
     * This is the endpoint where PingFederate.
     * Make sure to configure this URL in PingFederate's SP connection as the ACS URL.
     */
    @GetMapping("/ping-federate/callback")
    public ResponseEntity<Void> handlePingFederateCallback(@RequestParam("code")String code, @RequestParam("state")String state, HttpServletResponse response) {
        try {
            // Decode the state parameter from Base64-encoded UTF-8 JSON
            Map<String, Object> payload = DecoderUtil.decodeBase64Utf8Json(
                    state,
                    new TypeReference<Map<String, Object>>() {}
            );
            log.info("Payload from ping federate callback: {}", payload);
            // Extract origin from decoded payload
            String baseUrl = (payload != null && payload.get("origin") != null)
                    ? payload.get("origin").toString()
                    : uiBaseUrl;
            String redirectUrl = baseUrl.concat("/u/ping-federate-callback?code=").concat(URLEncoder.encode(code, StandardCharsets.UTF_8)).concat(CONST_STATE).concat(state);
            log.info("redirect URL from ping federate callback: {}", redirectUrl);
            response.sendRedirect(redirectUrl);
            return null; // sendRedirect already handled the response
        } catch (Exception e) {
            log.error("Error while processing ping federate", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/login-with-ping-federate/{whiteLabelUrl}")
    public AccessTokenContainer loginWithPingFederate(@PathVariable String whiteLabelUrl,
                                              @RequestParam("code") String code,
                                              @RequestParam("code_verifier") String codeVerifier,
                                              @RequestParam(value = "redirectUrl",required = false) String redirectUrl) {
        try {
            AccessTokenModel accessTokenModel = ssoUserService.validateTokenAndGetUserEmailForPingFederate(whiteLabelUrl, code, codeVerifier);
            accessTokenModel.setLoginUsingSso(true);
            AccessTokenContainer container = tokenStoreService.generateAccessToken(accessTokenModel);
            RedirectUrlDto redirectUrlDto = userUtils.getWhiteLabelRedirectUrl(accessTokenModel, whiteLabelUrl);
            if (StringUtils.isEmpty(redirectUrl)) {
                log.info("Found redirect URL from white label config while login with ping federate: {}", redirectUrlDto.getRedirectUrl());
                container.setRedirectUrl(redirectUrlDto.getRedirectUrl());
            } else {
                log.info("Using redirect URL from request param while login with ping federate: {}", redirectUrl);
                container.setRedirectUrl(redirectUrl);
            }
            container.setUserRole(redirectUrlDto.getUserRole());
            container.setWhiteLabelURL(redirectUrlDto.getWhiteLabelURL());
            return container;
        } catch (Exception e) {
            log.error("Ping federate SSO login failed: {}", Arrays.toString(e.getStackTrace()));
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }
}
