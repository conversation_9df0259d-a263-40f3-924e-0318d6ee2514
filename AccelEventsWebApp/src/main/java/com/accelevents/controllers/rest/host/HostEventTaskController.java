package com.accelevents.controllers.rest.host;

import com.accelevents.common.dto.EventTaskDto;
import com.accelevents.common.dto.TaskParticipantsResponseDto;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.enums.TaskType;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.dto.ResponseDto;
import com.accelevents.services.DownloadService;
import com.accelevents.services.EventTaskService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping("/rest/host/event/{eventUrl}/task")
@Tag(name = "/rest/host/event/{eventUrl}/task")
public class HostEventTaskController {

    private final AuthValidator authValidator;
    private final EventTaskService eventTaskService;
    private final DownloadService downloadService;
    @Autowired
    public HostEventTaskController(AuthValidator authValidator, EventTaskService eventTaskService,DownloadService downloadService) {
        this.authValidator = authValidator;
        this.eventTaskService = eventTaskService;
        this.downloadService = downloadService;
    }

    @GetMapping("/type/{taskType}")
    public DataTableResponse getTaskListByType(Authentication auth, @PathVariable TaskType taskType,
                                               @Parameter(description = "Event url") @PathVariable String eventUrl,
                                               @RequestParam(required = false) String searchString,
                                               @RequestParam(defaultValue = "0") int page,
                                               @RequestParam(defaultValue = "10") int size,
                                               @RequestParam(name = "sortColumn", defaultValue = "id") String sortColumn,
                                               @RequestParam(name = "isAsc", defaultValue = "false") boolean isAsc) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return  eventTaskService.getTaskListByType(userEvent.getEvent(),taskType,
                new PageSizeSearchObj(page,size,searchString,sortColumn, isAsc? Sort.Direction.ASC:Sort.Direction.DESC));
    }

    @PostMapping
    public ResponseDto createTask(Authentication auth,@RequestBody EventTaskDto eventTaskDto,
                                  @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        eventTaskService.createTask(userEvent.getEvent(),userEvent.getUser(),eventTaskDto);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @PutMapping("/{id}")
    public ResponseDto updateTask(Authentication auth,@PathVariable(name = "id")Long id, @RequestBody EventTaskDto eventTaskDto,
                                  @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        eventTaskService.updateTask(id,userEvent.getEvent(),userEvent.getUser(),eventTaskDto);
        return new ResponseDto(SUCCESS, SUCCESS);
    }


    @GetMapping("/{id}")
    public EventTaskDto getTask(Authentication auth,@PathVariable(name = "id")Long id,
                                  @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return eventTaskService.getTask(id,userEvent.getEvent(),userEvent.getUser());
    }

    @DeleteMapping("/{id}")
    public ResponseDto deleteTask(Authentication auth,@PathVariable(name = "id")Long id,
                                @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        eventTaskService.deleteTask(id,userEvent.getEvent(),userEvent.getUser());
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @PostMapping("/duplicate/{id}")
    public ResponseDto duplicateTask(Authentication auth,@PathVariable(name = "id")Long id,
                                  @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        eventTaskService.duplicateTask(id,userEvent.getEvent(),userEvent.getUser());
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @GetMapping("/view-result/{id}")
    public TaskParticipantsResponseDto getTaskResult(Authentication auth, @PathVariable(name = "id")Long id,
                                                     @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return eventTaskService.getTaskResult(id,userEvent.getEvent(),userEvent.getUser());
    }

    @GetMapping("/download/{id}")
    @Parameter(name = "Authorization", description = "Authorization", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    public void downloadEventTaskDataCSV(HttpServletResponse response, Authentication auth,
                                         @Parameter(description = "Event url") @PathVariable String eventUrl,
                                         @PathVariable long id) {

        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
            downloadService.downloadEventTaskDataCSV(response, userEvent.getEvent(),id);
    }

    @PostMapping("/reminder-email/{eventTaskId}")
    public ResponseDto sendReminderEmailToSpeakers(Authentication auth, @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                   @PathVariable(name = "eventTaskId") Long eventTaskId) {
        authValidator.authHostLevelAccess(auth, eventUrl);
        eventTaskService.sendEventTaskReminderEmailSqs(eventTaskId);
        return new ResponseDto(SUCCESS, EMAIL_SEND_SUCCESS_MSG);
    }

    @PostMapping("/publish/{taskId}")
    public ResponseDto publishTask(Authentication auth,@PathVariable(name = "taskId")Long taskId, @RequestBody EventTaskDto eventTaskDto,
                                  @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        eventTaskService.publishTask(taskId,userEvent.getEvent(),userEvent.getUser(),eventTaskDto);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @PostMapping("/save-as-draft")
    public ResponseDto saveAsDraft(Authentication auth,@RequestBody EventTaskDto eventTaskDto,
                                @Parameter(description = "Event url") @PathVariable String eventUrl) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        eventTaskService.saveAsDraft(userEvent.getEvent(), userEvent.getUser(), eventTaskDto);
        return new ResponseDto(SUCCESS, SUCCESS);
    }
}
