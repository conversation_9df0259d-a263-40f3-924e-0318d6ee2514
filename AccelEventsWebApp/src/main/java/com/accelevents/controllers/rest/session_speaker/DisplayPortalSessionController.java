package com.accelevents.controllers.rest.session_speaker;

import com.accelevents.common.dto.ChimeConfigDto;
import com.accelevents.common.dto.SpeakerRegisterSessionCountDto;
import com.accelevents.controllers.rest.RestAuthValidator;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.dto.DataTableResponseForSession;
import com.accelevents.dto.SessionRegistrationLimitDTO;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.UserRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROSessionService;
import com.accelevents.session_speakers.dto.DisplayPortalCommonSessionDTO;
import com.accelevents.session_speakers.dto.PostSessionCallToActionDto;
import com.accelevents.session_speakers.dto.PostSessionSurveyDto;
import com.accelevents.session_speakers.dto.SessionFilter;
import com.accelevents.session_speakers.services.SessionService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.accelevents.utils.ApiMessages.*;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@RestController
@RequestMapping("/rest/events/{eventurl:.+}/session/v2")
@Tag(name = "/rest/events/{eventurl}/session/v2")
public class DisplayPortalSessionController {

    @Autowired
    private SessionService sessionService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private AuthValidator authValidator;
    @Autowired
    private RestAuthValidator restAuthValidator;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ROSessionService roSessionService;

    //@Operation(description = "Get all sessions for display and portal page ")
    @GetMapping
    public DataTableResponseForSession getSessionList(
        @RequestParam(required = false) String searchString,
        @Parameter(description = EVENT_URL_DESC) @PathVariable("eventurl") String eventUrl,
        @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false, defaultValue = "") String expand,
        @RequestParam(required = false) String tagIds,
        @RequestParam(required = false) String trackIds,
        @RequestParam(required = false) String sessionFormat,
        @RequestParam(required = false,name = "ids") String ids,
        @Parameter(description = PAST_DESC) @RequestParam(required = false) Boolean past,
        @Parameter(description = PAST_UPCOMING_DESC) @RequestParam(required = false) Boolean showPastAndUpcoming,
        @Parameter(description = START_DATE) @RequestParam(required = false) String[] filterDate,
        @RequestParam(required = false) String calledFrom,
        Authentication auth) {
        User user = authValidator.authUserValid(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        SessionFilter sessionFilter = new SessionFilter(tagIds,trackIds,sessionFormat, searchString,past, filterDate, showPastAndUpcoming, ids);
        if(isNotBlank(sessionFormat)){
            EnumSessionFormat.valueOf(sessionFormat);
        }
        boolean isAdminOrStaff = false;
        if (user != null) {
            isAdminOrStaff = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        }
        return sessionService.getDisplayPortalSessionList(expand, event, user, PageRequest.of(Math.max(page, 0), (size>0)?size:10), sessionFilter , isAdminOrStaff, showPastAndUpcoming, calledFrom);
    }


    @GetMapping("/get-all-sessions")
    public DataTableResponseForSession getAllSessionList(
            @RequestParam(required = false) String searchString,
            @Parameter(description = EVENT_URL_DESC) @PathVariable("eventurl") String eventUrl,
            @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String tagIds,
            @RequestParam(required = false) String trackIds,
            @RequestParam(required = false) String sessionFormat,
            @RequestParam(required = false,name = "ids") String ids,
            @Parameter(description = PAST_DESC) @RequestParam(required = false) Boolean past,
            @Parameter(description = PAST_UPCOMING_DESC) @RequestParam(required = false) Boolean showPastAndUpcoming,
            @Parameter(description = START_DATE) @RequestParam(required = false) String filterDate,
            Authentication auth) {
        User user = authValidator.authUserValid(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        SessionFilter sessionFilter = new SessionFilter(tagIds,trackIds,sessionFormat, searchString,past, filterDate, showPastAndUpcoming, ids);
        if(isNotBlank(sessionFormat)){
            EnumSessionFormat.valueOf(sessionFormat);
        }
        boolean isAdminOrStaff = false;
        if (user != null) {
            isAdminOrStaff = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        }
        return roSessionService.getRedisDisplayPortalSessionListWithPagination(event, user, PageRequest.of(Math.max(page, 0), (size>0)?size:100), sessionFilter , isAdminOrStaff, showPastAndUpcoming);
    }

    //@Operation(description = "Get session for display and portal page ")
    @GetMapping("/{sessionId}")
    public DisplayPortalCommonSessionDTO getSessionInfoById(
        @PathVariable String eventurl,
        @PathVariable Long sessionId,
        @RequestParam(required = false, defaultValue = "") String expand,
        Authentication auth) {
        User user = authValidator.authUserValid(auth,eventurl);
        Event event = roEventService.getEventByURL(eventurl);
        boolean isAdminOrStaff = (user != null) && restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        return sessionService.getDisplayPortalSessionInfoById(sessionId, event, user, expand,isAdminOrStaff);
    }

    //@Operation(description = "Get session for display and portal page ")
    @PutMapping("/chimeConfig/{sessionId}")
    public void updateChimeConfigDetails(
            @PathVariable String eventurl,
            @PathVariable Long sessionId,
            @RequestBody @Validated ChimeConfigDto chimeConfigDto,
            Authentication auth) {
        User user = authValidator.authUserValid(auth,eventurl);
        Event event = roEventService.getEventByURL(eventurl);
        sessionService.updateChimeConfigDetails(sessionId, event, user, chimeConfigDto);
    }

    //@Operation(description = "Get session chime config portal page ")
    @GetMapping("/chimeConfig/{sessionId}")
    public ChimeConfigDto getChimeConfigDetails(
            @PathVariable String eventurl,
            @PathVariable Long sessionId,            Authentication auth) {
        authValidator.authUserValid(auth,eventurl);
        Event event = roEventService.getEventByURL(eventurl);
        return sessionService.getChimeConfigDetails(sessionId, event);
    }

    //@Operation(description = "Get registerd session and speaker count ")
    @GetMapping("/speakerRegisterSessionCount")
    public SpeakerRegisterSessionCountDto speakerRegisterSessionCount(
            @PathVariable String eventurl,
            @RequestParam(required = false) EnumSessionFormat sessionFormat,
            Authentication auth) {
        User user = authValidator.authUserValid(auth,eventurl);
        Event event = roEventService.getEventByURL(eventurl);
        return sessionService.speakerRegisterSessionCount(event, user,sessionFormat);
    }

    //@Operation(description = "Get Session Or Lounge Or Expo Existing Or Upcoming")
    @GetMapping("/sessionExistingOrUpcoming/id/{id}")
    public PostSessionCallToActionDto sessionOrLoungeOrExpoExistingOrUpcoming(
            @PathVariable String eventurl,
            @PathVariable Long id,
            Authentication auth) {
        User user = authValidator.authUserValid(auth,eventurl);
        if(user ==  null){
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
        Event event = roEventService.getEventByURL(eventurl);
        boolean isAdminOrStaff = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        return sessionService.sessionOrLoungeOrExpoExistingOrUpcoming(event, user, id, isAdminOrStaff);
    }


    @GetMapping("/post-sessions/surveys")
    public List<PostSessionSurveyDto> getPostSessionsSurveysDetails( @PathVariable String eventurl,Authentication auth){
        User user = authValidator.authUserValid(auth,eventurl);
        if(user ==  null){
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
        Event event = roEventService.getEventByURL(eventurl);
        boolean isAdminOrStaff = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);

        return sessionService.getPostSessionsSurveysDetails(event, user, isAdminOrStaff);

    }

    @GetMapping("/registration-limits")
    public SessionRegistrationLimitDTO getPlayBacksForSession(@PathVariable String eventurl, Authentication auth) {
        User user = authValidator.authUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        boolean isAdminOrStaff = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        return sessionService.getSessionRegistrationLimits(user, event, isAdminOrStaff);
    }


}
