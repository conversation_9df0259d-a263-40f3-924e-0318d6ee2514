package com.accelevents.controllers.rest.host;

import com.accelevents.controllers.rest.RestAuthValidator;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RegistrationRequestType;
import com.accelevents.domain.exhibitors.VirtualPortalImage;
import com.accelevents.dto.ImageUploadDto;
import com.accelevents.dto.ResponseDto;
import com.accelevents.dto.WLLogoImageUploadDto;
import com.accelevents.dto.WaitingMediaUploadDto;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.S3UploadService;
import com.accelevents.services.S3Wrapper;
import com.accelevents.utils.Constants;
import com.accelevents.utils.EventUtils;
import com.accelevents.utils.UserUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping({"/rest/host/upload", "rest/event/upload" , "rest/host/event/{eventUrl}/upload"})
@Tag(name = "/rest/host/upload")
public class RestUploadController {

    private static final Logger log = LoggerFactory.getLogger(RestUploadController.class);

    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(
            "pdf", "csv", "doc", "docx", "xls", "xlsx", "xlsm", "pptx", "json", "xml",
            "jpeg", "jpg", "png", "gif", "bmp", "webp", "tiff", "svg", "ico",
            "txt", "html", "css", "js", "md",
            "mp3", "wav", "ogg", "mp4", "aac", "flac",
            "3gp", "3g2", "avi", "divx", "mp4", "mpeg", "ogv", "mov", "webm", "flv", "mkv", "wmv"
    );

    private static final List<String> ALLOWED_MIME_TYPES = Arrays.asList(
            "application/pdf",
            "application/csv",
            "application/msword",
            "application/vnd.ms-excel",
            "application/vnd.ms-excel.sheet.macroEnabled.12",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "application/json",
            "application/xml",
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/bmp",
            "image/webp",
            "image/tiff",
            "image/svg+xml",
            "image/x-icon",
            "text/plain",
            "text/csv",
            "text/html",
            "text/xml",
            "text/css",
            "text/javascript",
            "text/markdown",
            "audio/mpeg",
            "audio/wav",
            "audio/ogg",
            "audio/mp4",
            "audio/aac",
            "audio/flac",
            "video/3gpp",
            "video/3gpp2",
            "video/avi",
            "video/divx",
            "video/mp4",
            "video/mpeg",
            "video/ogg",
            "video/quicktime",
            "video/webm",
            "video/x-flv",
            "video/x-matroska",
            "video/x-ms-wmv",
            "video/x-msvideo"
    );

    private static final List<String> PROHIBITED_EXTENSIONS = Arrays.asList(
            "exe", "bat", "cmd", "com", "cpl", "dll", "scr", "msi", "msp", "sys",
            "sh", "bash", "zsh", "ps1", "vbs", "js", "jse", "wsf", "wsh",
            "php", "php3", "php4", "phtml", "pl", "py", "rb", "jsp", "asp", "aspx", "htm", "html", "shtml", "jhtml",
            "cgi", "sh", "ksh", "csh", "tcsh", "bash", "zsh",
            "bin", "out", "app", "dmg", "pkg", "iso", "toast", "vmdk", "ova", "ovf",
            "zip", "tar", "gz", "bz2", "7z", "rar", "iso", "dmg", "img",
            "sql", "db", "dbf", "mdb", "accdb",
            "c", "cpp", "cxx", "h", "hpp", "hxx", "java", "class", "jar", "cs", "vb", "swift", "kt", "m", "mm"
    );


    @Autowired
    S3Wrapper s3client;
    @Autowired
    private EventUtils eventUtils;
    @Autowired
    private S3UploadService s3UploadService;
    @Autowired
    private RestAuthValidator restAuthValidator;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private UserUtils userUtils;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private AuthValidator authValidator;

    //@Operation(description = "Exhibitor carousel image upload by exhibitor id")
    @PostMapping(value = "/{eventUrl}/carousel/exhibitor/{id}", produces = "application/json")
    public ImageUploadDto uploadExhibitorCarouselImage(@PathVariable long id,@PathVariable String eventUrl,
                                                   @RequestBody ImageUploadDto imageUploadDto,
                                                   Authentication auth) {
        Event event = roEventService.getEventByURL(eventUrl);
        return s3UploadService.uploadExhibitorCarouselImage(id,imageUploadDto,event);
    }

    //@Operation(description = "Sponsor carousel upload by sponsor id")
    @PostMapping(value = "/{eventUrl}/carousel/sponsor/{id}", produces = "application/json")
    public ImageUploadDto uploadSponsorCarouselImage(@PathVariable long id,@PathVariable String eventUrl,
                                                 @RequestBody ImageUploadDto imageUploadDto,
                                                 Authentication auth) {
        Event event = roEventService.getEventByURL(eventUrl);
        return s3UploadService.uploadSponsorCarouselImage(id,imageUploadDto,event);
    }

    //@Operation(description = "Upload image")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = "/image", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadFile(@RequestParam("file") MultipartFile uploadFile) {
        checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_2_MB, TWO_INT);
        String url = uploadDocumentAndGetKey(uploadFile,false,null, false);
        return new ResponseDto(SUCCESS, url);
    }

    private void checkUploadImageOrFileSize(@RequestParam("file") MultipartFile uploadFile, int maximumFileSize, int sizeLimit) {
        if(uploadFile.getSize()>maximumFileSize){
            NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.FILE_SIZE_IS_EXCEED_THE_LIMIT;
            exception.setErrorMessage(Constants.FILE_SIZE_IS_EXCEED_THE_LIMIT.replace("${sizeLimit}", String.valueOf((sizeLimit))));
            exception.setDeveloperMessage(Constants.FILE_SIZE_IS_EXCEED_THE_LIMIT.replace("${sizeLimit}", String.valueOf(sizeLimit)));
            log.info("checkUploadFileSize: {}",exception.getErrorMessage());
            throw new NotAcceptableException(exception);
        }
    }

    //@Operation(description = "Upload document")
    @PostMapping(value = "/document", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadDocument(@RequestParam("file") MultipartFile uploadFile) {
        checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_200_MB, TWO_HUNDRED_INT);
        String url = uploadDocumentAndGetKey(uploadFile,false,null, false);
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "Upload caption file for session")
    @PostMapping(value = "/caption/{sessionId}/{assetId}", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadCaptionFile(@PathVariable(name = "sessionId") Long sessionId,@PathVariable(name = "assetId") String assetId,
                                         @RequestParam("file") MultipartFile uploadFile, @RequestParam("languageCode") String languageCode,
                                         @PathVariable(name = "eventUrl" , required = false) String eventUrl,
                                         @RequestParam(name = "subtitleId", required = false, defaultValue = "0") Long subtitleId,Authentication auth) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_200_MB, TWO_HUNDRED_INT);
        String url = s3UploadService.uploadSessionCaptionFile(event, sessionId,assetId, uploadFile, languageCode, subtitleId);
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "Upload badge PDF")
    @PostMapping(value = "/badge-pdf", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadBadgePDF(@RequestParam("file") MultipartFile uploadFile) {
        checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_200_MB, TWO_HUNDRED_INT);
        String url = uploadDocumentAndGetKey(uploadFile,false,null, true);
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "Upload lounge document")
    @PostMapping(value = "/networking-lounge/document", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadLoungeDocument(@RequestParam("file") MultipartFile uploadFile) {
        checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_10_MB, TEN_INT);
        String url = uploadDocumentAndGetKey(uploadFile,false,null, false);
        return new ResponseDto(SUCCESS, url);
    }

    private String uploadDocumentAndGetKey(@RequestParam("file") MultipartFile uploadFile, boolean isAppendEventUrl, String eventUrl, boolean isPDF) {
        log.info("Request Received for File Upload ");
        String url;
        String uploadKey;
        String fileName;

        fileName = uploadFile.getOriginalFilename();
        log.info("upload file request received from Event Url : {} uploaded File Name : {}", eventUrl, fileName);
        String fileExtension = FilenameUtils.getExtension(fileName);

        if ((fileName != null &&  fileName.indexOf('.') != fileName.lastIndexOf('.')) || (!ALLOWED_EXTENSIONS.contains(fileExtension) && PROHIBITED_EXTENSIONS.contains(fileExtension))) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_FILE_TYPE);
        }
        String mimeType = null;
        try {
            mimeType = getMimeTypeUsingFileContent(uploadFile);
        } catch (IOException e) {
            log.error("Error while getting mime type of file", e);
        }
        if (mimeType != null && !ALLOWED_MIME_TYPES.contains(mimeType)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_FILE_TYPE);
        }
        try {
            fileName = FilenameUtils.removeExtension(fileName);
            Matcher match = Pattern.compile(ATOZ_BOTHCASE_AND_NUMBERS).matcher(fileName.toLowerCase());
            if(isPDF) {
                fileExtension = StringUtils.isNotBlank(fileExtension) ?  fileExtension.concat("pdf") : "pdf";
            }

            while (match.find()) {
                fileName = fileName.replaceAll("\\".concat(match.group()), "");
            }
            if(isAppendEventUrl && StringUtils.isNotBlank(eventUrl)) {
                uploadKey = UUID.randomUUID() +"-" + eventUrl+ "_" + URLEncoder.encode(fileName, UTF8) + "." + fileExtension;
            }else {
                uploadKey = UUID.randomUUID() + "_" + URLEncoder.encode(fileName, UTF8) + "." + fileExtension;
            }
            log.info("Upload Key : {}" , uploadKey);

            s3client.upload(uploadFile.getInputStream(), uploadKey, uploadFile.getOriginalFilename());

            url = uploadKey;
            log.info(url);
        } catch (Exception e) {
            log.error("Upload File : ", e);
            throw new NotAcceptableException(e);
        }
        return url;
    }

    private String getMimeTypeUsingFileContent(MultipartFile file) throws IOException {
        // Save the file to a temporary location
        Path tempFile = Files.createTempFile("upload-", file.getOriginalFilename());
        Files.write(tempFile, file.getBytes());

        // Probe the MIME type
        String mimeType = Files.probeContentType(tempFile);

        // Delete the temporary file
        Files.delete(tempFile);

        return mimeType;
    }

    //@Operation(description = "Event Design logo uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/event/{eventUrl}/logoImage","/logoImage"}, produces = "application/json")
    public ImageUploadDto uploadLogoImageFiles(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                               @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadLogoImageFiles(imageUploadDto,event);
    }

    //@Operation(description = "Event venue map image upload")
    @PostMapping(value = "/venueMapImage", produces = "application/json")
    public ImageUploadDto uploadVenueMapImage(@RequestBody ImageUploadDto imageUploadDto){
        return  s3UploadService.commonImageUploadOnS3(imageUploadDto);
    }

    //@Operation(description = "Exhibitor logo upload by exhibitor id")
    @PostMapping(value = "/logo/exhibitor/{id}", produces = "application/json")
    public ImageUploadDto uploadExhibitorLogoImage(@PathVariable long id,
                                                        @RequestBody ImageUploadDto imageUploadDto,
                                                        Authentication auth) {
        return s3UploadService.uploadExhibitorLogoImage(id,imageUploadDto);
    }

    //@Operation(description = "Exhibitor image as the background for the card")
    @PostMapping(value = {"/event/{eventUrl}/expoCardImage/exhibitor/{id}","/expoCardImage/exhibitor/{id}"}, produces = "application/json")
    public ImageUploadDto uploadExpoCardImage(@PathVariable long id, @RequestBody ImageUploadDto imageUploadDto,
                                              @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                              Authentication auth) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadExhibitorExpoCard(id, imageUploadDto, event);
    }

    //@Operation(description = "Exhibitor banner image upload")
    @PostMapping(value = "/expoBannerImage/exhibitor/{id}", produces = "application/json")
    public ImageUploadDto uploadExpoBannerImage(@PathVariable long id, @RequestBody ImageUploadDto imageUploadDto, Authentication auth) {
        return s3UploadService.uploadExhibitorExpoBannerImage(id, imageUploadDto);
    }

    //@Operation(description = "Exhibitor Page banner image upload")
    @PostMapping(value = "/expoPageBannerImage", produces = "application/json")
    public ImageUploadDto uploadExpoPageBannerImage(@RequestBody ImageUploadDto imageUploadDto){
        return  s3UploadService.commonImageUploadOnS3(imageUploadDto);
    }

    //@Operation(description = "Introductory Page banner image upload")
    @PostMapping(value = "/event/{eventUrl}/introductoryPageBannerImage", produces = "application/json")
    public ImageUploadDto uploadIntroductoryPagBannerImage(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                           @RequestParam RegistrationRequestType type,
                                                           Authentication auth,
                                                           @RequestBody ImageUploadDto imageUploadDto) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadIntroductoryPageBannerImage(event, imageUploadDto, type);
    }

    //@Operation(description = "Upload expo logo for registration")
    @PostMapping(value = "/event/{eventUrl}/expo/logo", produces = "application/json")
    public ImageUploadDto uploadExpoLogoImageForRegistration(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl, @RequestBody ImageUploadDto imageUploadDto,
                                                             Authentication auth) {
        eventUtils.getEvent(auth, eventUrl);
        log.info("Upload expo logo for registration eventUrl : {} , imageUploadDto : {}", eventUrl, imageUploadDto);
        return s3UploadService.commonImageUploadOnS3(imageUploadDto);
    }

    //@Operation(description = "Exhibitor Page left side banner image upload")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/event/{eventUrl}/leftSideExpoBannerImage/{imagePosition}","/leftSideExpoBannerImage/{imagePosition}"},
            produces = "application/json")
    public VirtualPortalImage uploadLeftSideExpoBannerImage(@PathVariable String imagePosition, @RequestBody ImageUploadDto imageUploadDto,
                                                            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                            Authentication auth) {
        restAuthValidator.authUser(auth);
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadLeftSideExpoBannerImage(imageUploadDto,event,imagePosition);
    }

    //@Operation(description = "Expo background image upload")
    @PostMapping(value = "/backgroundImage/expo", produces = "application/json")
    public ImageUploadDto uploadExpoBackgroundImage(@RequestBody ImageUploadDto imageUploadDto) {
        return s3UploadService.commonImageUploadOnS3(imageUploadDto);
    }

    //@Operation(description = "Sponsor logo upload by sponsor id")
    @PostMapping(value = {"/event/{eventUrl}/logo/sponsor/{id}","/logo/sponsor/{id}"}, produces = "application/json")
    public ImageUploadDto uploadSponsorLogoImage(@PathVariable long id,
                                                   @RequestBody ImageUploadDto imageUploadDto,
                                                   @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                                   Authentication auth) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadSponsorLogoImage(id,imageUploadDto,event);
    }

    //@Operation(description = "User image upload")
    @PostMapping(value = {"/event/{eventUrl}/logo/user","/logo/user"}, produces = "application/json")
    public ImageUploadDto uploadUserImage(@RequestBody ImageUploadDto imageUploadDto,
                                          @RequestParam(value = "isFromSpeaker", required = false) boolean isFromSpeaker,
                                          @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl,
                                          Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadUserImage(user, imageUploadDto, event, isFromSpeaker);
    }

	//@Operation(description = "Event Design logo uploaded successfully")
//	////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 406, message = "Input is not Acceptable")})
	@PostMapping(value = {"/event/{eventUrl}/customThermometerImage","/customThermometerImage"}, produces = "application/json")
	public ImageUploadDto uploadCustomThermometerImage(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                       @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
		Event event = eventUtils.getEvent(auth, eventUrl);
		return s3UploadService.customThermometerImage(imageUploadDto,event);
	}

	//@Operation(description = "Event Design logo uploaded successfully")
//	////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 406, message = "Input is not Acceptable")})
	@PostMapping(value = {"/event/{eventUrl}/donationViewBackgroundImage","/donationViewBackgroundImage"}, produces = "application/json")
	public ImageUploadDto uploadDonationViewBackgroundImage(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                            @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
		Event event = eventUtils.getEvent(auth, eventUrl);
		return s3UploadService.donationViewBackgroundImage(imageUploadDto,event);
	}

    //@Operation(description = "Event Design banner uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/event/{eventUrl}/bannerImage","/bannerImage"}, produces = "application/json")
    public ImageUploadDto uploadBannerImageFiles(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                 @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadBannerImageFiles(imageUploadDto,event);
    }

    //@Operation(description = "Event Design lobby page banner uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/event/{eventUrl}/lobbyBannerImage","/lobbyBannerImage"}, produces = "application/json")
    public ImageUploadDto uploadLobbyBannerImageFiles(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                      @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        restAuthValidator.authUser(auth);
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadLobbyBannerImageFiles(imageUploadDto,event);
    }

    //@Operation(description = "white label image uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = "/wlDesignImage", produces = "application/json")
    public WLLogoImageUploadDto uploadWlImageFiles(@RequestBody ImageUploadDto imageUploadDto, @RequestParam(value = "whiteLabelUrl", required = false) String whiteLabelUrl) {
        return s3UploadService.uploadWLImageFiles(imageUploadDto, whiteLabelUrl);
    }

    //@Operation(description = "upload image for ticketing")
//	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "image upload key"),
//			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//			//@ApiResponse(code = 406, message = "Input is not Acceptable") })
	@PostMapping(value = "/uploadImageForTicketing", produces = "application/json")
	@ResponseBody
	public ResponseDto uploadImageForTicketing(@RequestParam("file") MultipartFile uploadFile) {

		log.info("Request Received for Image Upload ");
		String url;
		String uploadKey;
		String fileName;
		try {
            fileName = uploadFile.getOriginalFilename();
            String extension = FilenameUtils.getExtension(fileName);

            Matcher match = Pattern.compile(ATOZ_BOTHCASE_AND_NUMBERS).matcher(fileName.toLowerCase());//NOSONAR
            while (match.find()) {
                fileName = fileName.replaceAll("\\".concat(match.group()), "");
            }
            uploadKey = UUID.randomUUID() + "_" + URLEncoder.encode(fileName, UTF8) + "." + extension;
            log.info("Upload Key : {}" , uploadKey);
            s3client.uploadInTicketBuyerBucket(uploadFile.getInputStream(), uploadKey, uploadFile.getContentType());

			url = uploadKey;
			log.info(url);
		} catch (Exception e) {
			log.error("Upload Image : ", e);
			throw new NotAcceptableException(e);
		}
		return new ResponseDto(SUCCESS, url);
	}

    //@Operation(description = "upload file for ticketing")
//    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "file upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable") })
    @PostMapping(value = "/uploadFileForTicketing", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadImageFileTicketing(@RequestParam("file") MultipartFile uploadFile) {

        log.info("Request Received for File Upload ");
        String url;
        String uploadKey;
        String fileName;
        try {
            checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_20_MB, TWENTY_INT);
            fileName = uploadFile.getOriginalFilename();
            String extension = FilenameUtils.getExtension(fileName);

            Matcher match = Pattern.compile(ATOZ_BOTHCASE_AND_NUMBERS).matcher(fileName.toLowerCase());//NOSONAR
            while (match.find()) {
                fileName = fileName.replaceAll("\\".concat(match.group()), "");
            }
            uploadKey = UUID.randomUUID() + "_" + URLEncoder.encode(fileName, UTF8) + "." + extension;
            log.info("File Upload Key : {}" , uploadKey);
            s3client.uploadInTicketBuyerBucket(uploadFile.getInputStream(), uploadKey, uploadFile.getContentType());

            url = uploadKey;
            log.info(url);
        } catch (Exception e) {
            log.error("Upload File : ", e);
            throw new NotAcceptableException(e);
        }
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "upload file for registration")
//    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "file upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable") })
    @PostMapping(value = "/uploadFileForRegistration", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadImageFileRegistration(@RequestParam("file") MultipartFile uploadFile) {

        log.info("Request received to upload file for registration");
        String url;
        String uploadKey;
        String fileName;
        try {
            checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_20_MB, TWENTY_INT);
            fileName = uploadFile.getOriginalFilename();
            String extension = FilenameUtils.getExtension(fileName);

            Matcher match = Pattern.compile(ATOZ_BOTHCASE_AND_NUMBERS).matcher(fileName.toLowerCase());//NOSONAR
            while (match.find()) {
                fileName = fileName.replaceAll("\\".concat(match.group()), "");
            }
            uploadKey = UUID.randomUUID() + "_" + URLEncoder.encode(fileName, UTF8) + "." + extension;
            log.info("File Upload Key : {}" , uploadKey);
            s3client.uploadInRegistrationForm(uploadFile.getInputStream(), uploadKey, uploadFile.getContentType());

            url = uploadKey;
            log.info(url);
        } catch (Exception e) {
            log.error("Upload File : ", e);
            throw new NotAcceptableException(e);
        }
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "Call to action logo uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = "/callToActionImage", produces = "application/json")
    public ImageUploadDto uploadCallToActionLogo(@RequestBody ImageUploadDto imageUploadDto) {
        return s3UploadService.commonImageUploadOnS3(imageUploadDto);
    }

    //@Operation(description = "NetworkingLounge Image upload by Networking Lounge id")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = "/networkingLoungeImage/{id}", produces = "application/json")
    public ImageUploadDto uploadNetworkingLoungeImage(@PathVariable String id,@RequestBody ImageUploadDto imageUploadDto,
                                                      @RequestParam(required = false, defaultValue = "false") Boolean isProfileImage,
                                                      @RequestParam(required = false, defaultValue = "false") Boolean isBannerImage,
                                                      Authentication auth) {
        User user = restAuthValidator.authUser(auth);
        return s3UploadService.uploadNetworkingLoungeImage(id,imageUploadDto,isProfileImage,user.getUserId(),isBannerImage);
    }

    //@Operation(description = "Upload Video")
    @PostMapping(value = "/video", produces = "application/json")
    @ResponseBody
    public ResponseDto uploadVideo(@RequestParam("file") MultipartFile uploadFile) {
        checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_200_MB, TWO_HUNDRED_INT);
        String url = uploadDocumentAndGetKey(uploadFile,false,null, false);
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "Sponsor image as the background for the card")
    @PostMapping(value = {"/event/{eventUrl}/sponsorCardImage/sponsor/{id}", "/sponsorCardImage/sponsor/{id}"}, produces = "application/json")
    public ImageUploadDto uploadSponsorCardImage(@PathVariable long id, @RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                 @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        restAuthValidator.authUser(auth);
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadSponsorCardImage(id, imageUploadDto, event);
    }

    //@Operation(description = "Upload screen shot to share at social media")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/event/{eventUrl}/screenShot","/screenShot"}, produces = "application/json")
    @ResponseBody
    public ResponseDto uploadScreenShot(@RequestParam("file") MultipartFile uploadFile,Authentication auth,
                                        @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        restAuthValidator.authUser(auth);
        checkUploadImageOrFileSize(uploadFile, MAXIMUM_FILE_SIZE_UP_TO_2_MB, TWO_INT);
        Event event = eventUtils.getEvent(auth, eventUrl);
        String url = uploadDocumentAndGetKey(uploadFile,true,event.getEventURL(), false);
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "Call to action logo uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = "/uploadImageOnS3", produces = "application/json")
    public ImageUploadDto sessionWaitingImage(@RequestBody ImageUploadDto imageUploadDto,Authentication auth) {
        restAuthValidator.authUser(auth);
        return s3UploadService.commonImageUploadOnS3(imageUploadDto);
    }

    //@Operation(description = "User virtual background for the session")
    @PostMapping(value = {"/event/{eventUrl}/userVirtualBackgroundImage/{eventUrl}/user/{id}","/userVirtualBackgroundImage/user/{id}"},
            produces = "application/json")
    public ImageUploadDto uploadUserVirtualBackgroundImage(@PathVariable long id, @RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                           @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadUserVirtualBackgroundImage(id, imageUploadDto, event);
    }

    //@Operation(description = "upload cover photo by user")
    @PostMapping(value = {"/event/{eventUrl}/coverPhoto/user","/coverPhoto/user"}, produces = "application/json")
    public ImageUploadDto uploadCoverPhoto(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                           @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        User user = restAuthValidator.authUser(auth);
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadCoverPhoto(user, imageUploadDto, event);
    }

    //@Operation(description = "Upload session waiting image or video")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/event/{eventUrl}/session/{sessionId}"}, produces = "application/json")
    public WaitingMediaUploadDto uploadSessionWaitingImageOrVideo(@RequestBody WaitingMediaUploadDto waitingMediaUploadDto, Authentication auth,
                                                                  @PathVariable long sessionId, @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadSessionWaitingImageOrVideo(waitingMediaUploadDto, event, sessionId);
    }

    //@Operation(description = "Gamification Reward image uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/gamificationRewardImage"}, produces = "application/json")
    public ImageUploadDto uploadGamificationImage(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                  @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        eventUtils.getEvent(auth, eventUrl);
        return  s3UploadService.commonImageUploadOnS3(imageUploadDto);
    }

    //@Operation(description = "Event Design Venue Image uploaded successfully")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "image upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/event/{eventUrl}/venueMap","/venueMap"}, produces = "application/json")
    public ImageUploadDto uploadVenueMapImageFiles(@RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                 @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadVenueMapImageFiles(imageUploadDto,event);
    }

    //@Operation(description = "Generate pre-signed Url")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Upload key"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 406, message = "Input is not Acceptable")})
    @PostMapping(value = {"/generatePresignedUrl/{uploadKey}"}, produces = "application/json")
    public ResponseDto generatePreSignedUrl(@PathVariable String uploadKey, @RequestParam(required = false, defaultValue = "") String fileName) {
        String url = s3UploadService.generatePreSignedUrl(uploadKey, fileName);
        return new ResponseDto(SUCCESS, url);
    }

    //@Operation(description = "Session detail image upload")
    @PostMapping(value = "/session/image", produces = "application/json")
    public ImageUploadDto uploadSessionDetailImage(@RequestBody ImageUploadDto imageUploadDto) {
        return s3UploadService.imageUploadInS3(imageUploadDto);
    }

    //@Operation(description = "sponsor's attendee mobile app image")
    @PostMapping(value = {"/event/{eventUrl}/attendee-mobile-app-image/sponsor/{id}", "/attendee-mobile-app-image/sponsor/{id}"}, produces = "application/json")
    public ImageUploadDto uploadAttendeeMobileAppImage(@PathVariable long id, @RequestBody ImageUploadDto imageUploadDto, Authentication auth,
                                                 @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        restAuthValidator.authUser(auth);
        Event event = eventUtils.getEvent(auth, eventUrl);
        return s3UploadService.uploadAttendeeMobileAppImage(id, imageUploadDto, event);
    }

    @PostMapping(value = {"/mobile-app-master-icon/{iconName}"}, produces = "application/json")
    public ResponseDto uploadMobileIconMasterImageFiles(@PathVariable String iconName, @RequestBody ImageUploadDto imageUploadDto, Authentication auth) {
        User user = userUtils.getUser(auth);
        if (roUserService.isSuperAdminUser(user)) {
            return new ResponseDto(s3UploadService.uploadMobileIconMasterImageFiles(iconName,imageUploadDto),"Icon uploaded successfully","Failed to upload icon.");
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SUPER_ADMIN);
        }
    }

    @PostMapping(value = {"/event/{eventUrl}/mobile-app-icon"}, produces = "application/json")
    public ImageUploadDto uploadMobileIconImageFilesByEventId(@Parameter(description = "Event url") @PathVariable String eventUrl,
                                                        @RequestBody ImageUploadDto imageUploadDto, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return s3UploadService.uploadMobileIconImageFilesByEventId(userEvent.getEvent().getEventId(), userEvent.getUser().getUserId(),imageUploadDto);
    }
}
