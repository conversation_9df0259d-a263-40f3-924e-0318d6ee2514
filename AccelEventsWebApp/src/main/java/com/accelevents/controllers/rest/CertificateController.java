package com.accelevents.controllers.rest;


import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.CertificateImages;
import com.accelevents.domain.enums.CertificateTypes;
import com.accelevents.dto.*;
import com.accelevents.services.CertificatesService;
import com.accelevents.services.S3UploadService;
import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

import static com.accelevents.utils.ApiMessages.PAGE_DESC;

@RestController
@RequestMapping({"/rest/host/event/{eventUrl}/certificates"})
@Tag(name = "/rest/host/event/{eventUrl}/certificates")
public class CertificateController {

    private AuthValidator authValidator;
    private CertificatesService certificatesService;

    private S3UploadService s3UploadService;

    @Autowired
    public CertificateController(CertificatesService certificatesService, AuthValidator authValidator, S3UploadService s3UploadService) {
        this.certificatesService = certificatesService;
        this.authValidator = authValidator;
        this.s3UploadService = s3UploadService;
    }


    @GetMapping
    public DataTableResponse getCertificates(@RequestParam(required = false) String searchString,
                                             @Parameter(description = PAGE_DESC) @RequestParam(defaultValue = "0") int page,
                                             @RequestParam(defaultValue = "10") int size,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl,
                                             @RequestParam(required = false, defaultValue = "id") String sortColumn,
                                             @RequestParam(required = false) boolean isAsc,
                                             Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.getAllCertificatesOfEvent(userEvent.getEvent(), searchString, page, size, sortColumn, isAsc);

    }

    @PostMapping(path = "/update-certificate")
    public CertificatesDto updateCertificate(@RequestBody @Validated CertificatesDto certificatesDto,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl,
                                             Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.createOrUpdateCertificate(certificatesDto, userEvent.getEvent(), userEvent.getUser());
    }


    @GetMapping(path = "/{id}")
    public CertificatesDto getCertificate(@Parameter(description = "id") @PathVariable Long id,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl,
                                             Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.getCertificate(id, userEvent.getEvent(), userEvent.getUser());

    }

    @DeleteMapping(path = "/{id}")
    public ResponseDto deleteCertificate(@Parameter(description = "id") @PathVariable Long id,
                                         @Parameter(description = "Event url") @PathVariable String eventUrl,
                                         Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        certificatesService.deleteCertificate(id, userEvent.getEvent(), userEvent.getUser());
        return new ResponseDto(Constants.SUCCESS, Constants.CERTIFICATE_DELETED_SUCCESSFULLY);
    }

    @PostMapping(path = "/clone-certificate")
    public CertificatesDto cloneBadgesForEvent(@PathVariable("eventUrl") String eventUrl,
                                                 @RequestParam("certificateId") Long certificateId,
                                                 Authentication auth){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.cloneCertificates(userEvent.getUser(),userEvent.getEvent(),certificateId);

    }


    @PostMapping(value = "/{certificateId}/upload-image", produces = "application/json")
    public CertificateImages uploadCertificateImage(@PathVariable("eventUrl") String eventUrl,
                                                    @PathVariable("certificateId") Long certificateId,
                                                    @RequestBody ImageUploadDto imageUploadDto, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return s3UploadService.uploadCertificateImage(imageUploadDto,userEvent.getEvent(),userEvent.getUser(),certificateId);
    }

    @PutMapping(value = "/images/{imageId}/{hideImage}", produces = "application/json")
    public ResponseDto updateCertificateImageHideFlag(@PathVariable("eventUrl") String eventUrl,
                                            @PathVariable("hideImage") boolean hideImage,
                                            @PathVariable("imageId") Long imageId, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.updateCertificateImageHideFlag(imageId,hideImage, userEvent.getEvent(),userEvent.getUser());
    }

    @GetMapping(path = "/{certificateId}/all-images")
    public List<CertificateImages> getCertificateImages(@PathVariable("eventUrl") String eventUrl,
                                             @PathVariable("certificateId") Long certificateId,
                                             Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.getAllImagesOfCertificate(userEvent.getEvent(),certificateId);

    }

    @DeleteMapping(path = "/{certificateId}/images/{imageId}")
    public ResponseDto removeCertificateImage(@PathVariable("eventUrl") String eventUrl,
                                              @PathVariable("certificateId") Long certificateId,
                                              @PathVariable("imageId") Long imageId,
                                              Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.removeCertificateImage(certificateId,imageId, userEvent.getUser(),userEvent.getEvent());
    }

    @PostMapping(path = "/{certificateId}/custom-fields")
    public ResponseDto upsertCustomFieldsForBadge(@PathVariable String eventUrl, @PathVariable Long certificateId, Authentication auth, @RequestBody List<HashMap<String,Object>> customAttributes) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        certificatesService.upsertCustomFields(userEvent.getEvent(),userEvent.getUser(),certificateId,customAttributes);

        return new ResponseDto(Constants.SUCCESS,Constants.SUCCESS);
    }


    @GetMapping(path = "/all-certificates")
    public List<CertificateBasicDto> getCertificatesBasicDetails(@PathVariable("eventUrl") String eventUrl, @RequestParam(value = "allTypes", defaultValue = "false") boolean allTypes,
                                                                 Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return certificatesService.getAllCertificatesBasicDetails(userEvent.getEvent(), allTypes);

    }

}
