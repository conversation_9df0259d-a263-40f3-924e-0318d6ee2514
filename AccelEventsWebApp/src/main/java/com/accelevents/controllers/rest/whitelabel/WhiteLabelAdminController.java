package com.accelevents.controllers.rest.whitelabel;

import com.accelevents.billing.chargebee.dto.BillingSummerDto;
import com.accelevents.common.dto.OrganizerDto;
import com.accelevents.common.dto.WhiteLabelDto;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.dto.WhiteLabelSettingDto;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.OrganizerService;
import com.accelevents.services.UserService;
import com.accelevents.services.WhiteLabelService;
import com.accelevents.utils.UserUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.accelevents.utils.Constants.NOT_AUTHORIZE;

@RestController
@RequestMapping("/rest/wl-admin/wl-url/{whiteLabelURL}")
@Tag(name = "/rest/wl-admin/wl-url/{whiteLabelURL}")
public class WhiteLabelAdminController {

    private static final Logger log = LoggerFactory.getLogger(WhiteLabelAdminController.class);

    @Autowired
    private UserUtils userUtils;
    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private OrganizerService organizerService;


    //@Operation(description = "WhiteLabel details")
//    ////@ApiResponses(value = {
//            //@ApiResponse(code = 200, message = "Successfully retrieved white labels details."),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping(path = { "" })
    public WhiteLabelDto getEventDataWithDesignDetails(@PathVariable String whiteLabelURL, Authentication auth) {
        User whiteLabelUser = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(whiteLabelUser, whiteLabel)) {
            return whiteLabelService.getWhiteLabelDetailDto(whiteLabelURL);
        }else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
        }
    }

    //@Operation(description = "get setting detail")
//    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Success"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping("/settings")
    public WhiteLabelSettingDto whiteLabelSettings(@PathVariable String whiteLabelURL, Authentication auth) {
        User user = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roUserService.isSuperAdminUser(user)) {
            return whiteLabelService.getWhiteLabelSetting(whiteLabelURL, true);
        } else if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(user, whiteLabel)) {
            return whiteLabelService.getWhiteLabelSetting(whiteLabelURL, false);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
        }
    }

    @PutMapping("/event-closing-form/{isEnabled}")
    public void updateEventClosingFormToggle(@PathVariable String whiteLabelURL, Authentication auth,
                                             @PathVariable("isEnabled") boolean isEnabled) {
        User user = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roUserService.isSuperAdminUser(user) || this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(user, whiteLabel)) {
            whiteLabelService.updateEventClosingFormToggle(whiteLabelURL, isEnabled, user);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
        }
    }

    //@Operation(description = "organizers list for white label events")
//    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Return attendee and session count by organizer"),
//            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
//            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping(value = "/organizers")
    public List<OrganizerDto> getCountOfWhiteLabelEvent(@PathVariable String whiteLabelURL, Authentication auth){
        User whiteLabelUser = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(whiteLabelUser, whiteLabel)) {
            return organizerService.getListOfWhiteLabelEventWithOrg(whiteLabel.getId());
        }else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN);
        }
    }

    //@Operation(description = "Create organizer url")
    @PostMapping(value = "/organizer/{organizerName}")
    public OrganizerDto createWLOrganizer(Authentication auth,
                                          @PathVariable String organizerName, @PathVariable String whiteLabelURL,
                                          @RequestBody @Validated OrganizerDto organizerDto) {
        User whiteLabelUser = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(whiteLabelUser, whiteLabel)) {
            return whiteLabelService.createWLOrganizer(organizerName, whiteLabel, organizerDto, whiteLabelUser, Boolean.TRUE);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    //@Operation(description = "Returns list usages items")
    @GetMapping(path = {"/billing/summery"})
    public BillingSummerDto getBillingSummery(@PathVariable String whiteLabelURL,Authentication auth) {
        User whiteLabelUser = this.userUtils.getUser(auth);
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);
        if (this.roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(whiteLabelUser, whiteLabel)) {
            return this.whiteLabelService.getBillingSummeryForWhiteLabel(whiteLabel);
        }
        throw new AuthorizationException(NOT_AUTHORIZE);
    }
}
