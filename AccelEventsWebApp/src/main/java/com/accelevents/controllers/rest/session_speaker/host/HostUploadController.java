package com.accelevents.controllers.rest.session_speaker.host;

import com.accelevents.common.dto.*;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.services.ExhibitorService;
import com.accelevents.services.SponsorsService;
import com.accelevents.services.StaffService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.SessionThirdPartyService;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.utils.CommonUtil;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/rest/host/events/{eventUrl}/upload")
@Tag(name = "/rest/host/events/{eventUrl}/upload")
public class HostUploadController {

    @Autowired
    private AuthValidator authValidator;

    @Autowired
    private SpeakerService speakerService;

    @Autowired
    private SessionService sessionService;

    @Autowired
    private ExhibitorService exhibitorService;

    @Autowired
    private SponsorsService sponsorsService;

    @Autowired
    private SessionThirdPartyService sessionThirdPartyService;

    @Autowired
    private StaffService staffService;

    Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

    //@Operation(description = "Parse uploaded exhibitor staff file")
    @PostMapping("/exhibitorStaff/{id}/parse/csv")
    public UploadStaffResponseContainer parseExhibitorStaffCsv(@PathVariable String eventUrl,
                                                               @Parameter(description = "Exhibitor Id") @PathVariable Long id,
                                                               @RequestParam("exhibitorStaffFile") MultipartFile multiPartFile,
                                                               Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return staffService.parseExhibitorStaffCSV(multiPartFile, userEvent.getEvent(), userEvent.getUser(),id);
    }

    //@Operation(description = "Upload Exhibitor Staff")
    @PostMapping("/staff/{id}")
    public UploadStaffResponseContainer uploadExhibitors(@PathVariable String eventUrl,@Parameter(description = "Exhibitor Id") @PathVariable Long id,
                                                              @RequestBody UploadStaffDto[] staffDtos,
                                                              Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return staffService.uploadStaffCSV(staffDtos, userEvent.getEvent(), userEvent.getUser(), id);
    }

    //@Operation(description = "Parse uploaded speaker file")
    @PostMapping("/speaker/parse/csv")
    public UploadedSpeakerResponseContainer parseSpeakersCsv(@PathVariable String eventUrl,
                                                             @RequestParam("speakerFile") MultipartFile multiPartFile,
                                                             Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return speakerService.parseSpeakerCSV(multiPartFile, userEvent.getEvent(), userEvent.getUser(),languageMap);
    }

    //@Operation(description = "Upload Speakers")
    @PostMapping("/speaker")
    public UploadedSpeakerResponseContainer uploadSpeakers(@PathVariable String eventUrl,
                                                           @RequestBody @Validated UploadSpeakerDto[] speakerDTOS,
                                                           Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return speakerService.uploadSpeakerCSVOrZapier(speakerDTOS,userEvent.getEvent(), userEvent.getUser(),languageMap);
    }

    //@Operation(description = "Parse uploaded session file")
    @PostMapping("/session/parse/csv")
    public UploadSessionResponseContainer parseSessionCsv(@PathVariable String eventUrl,
                                                          @RequestParam("sessionFile") MultipartFile multiPartFile,
                                                          Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sessionService.saveOrUpdateParseSessionCSV(multiPartFile, userEvent.getEvent(), userEvent.getUser(),languageMap);
    }


    //@Operation(description = "Upload Session")
    @PostMapping("/session")
    public UploadSessionResponseContainer uploadSessions(@PathVariable String eventUrl,
                                                         @RequestBody UploadSessionDto[] sessionDtos,
                                                         Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        UploadSessionResponseContainer uploadSessionResponseContainer =  sessionService.uploadSessionCSVOrZapier(sessionDtos, userEvent.getEvent(), false,false, userEvent.getUser());
        sessionThirdPartyService.createChannelByListOfSessionIds(uploadSessionResponseContainer.getValidSession(), userEvent.getEvent());
        return uploadSessionResponseContainer;
    }

    //@Operation(description = "Parse uploaded exhibitor file")
    @PostMapping("/exhibitor/parse/csv")
    public UploadExhibitorsResponseContainer parseExhibitorCsv(@PathVariable String eventUrl,
                                                               @RequestParam("exhibitorFile") MultipartFile multiPartFile,
                                                               Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return exhibitorService.parseExhibitorCSV(multiPartFile, userEvent.getEvent(), userEvent.getUser());
    }

    //@Operation(description = "Upload Exhibitor")
    @PostMapping("/exhibitor")
    public UploadExhibitorsResponseContainer uploadExhibitors(@PathVariable String eventUrl,
                                                              @RequestBody UploadExhibitorDto[] exhibitorDtos,
                                                              Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return exhibitorService.uploadExhibitorCSV(exhibitorDtos, userEvent.getEvent(), userEvent.getUser());
    }

    //@Operation(description = "Parse and upload valid sponsors from CSV")
    @PostMapping("/sponsor/parse/csv")
    public UploadSponsorsResponseContainer parseAndUploadValidSponsorCsv(@PathVariable String eventUrl,
                                                               @RequestParam("sponsorFile") MultipartFile multiPartFile,
                                                               Authentication auth) throws IOException {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sponsorsService.parseAndUploadValidSponsorCsv(multiPartFile, userEvent.getEvent());
    }

    //@Operation(description = "Upload Sponsors")
    @PostMapping("/sponsor")
    public UploadSponsorsResponseContainer uploadSponsors(@PathVariable String eventUrl,
                                                              @RequestBody UploadSponsorDto[] sponsorDtos,
                                                              Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return sponsorsService.uploadSponsors(sponsorDtos, userEvent.getEvent());
    }
}
