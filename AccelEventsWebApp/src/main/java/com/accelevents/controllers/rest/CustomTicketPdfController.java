package com.accelevents.controllers.rest;

import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.CustomTicketPdf;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.CustomTicketPdfService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/rest/events/{eventUrl}/customTicketPdf")
@Tag(name = "/rest/events/{eventUrl}/customTicketPdf")
public class CustomTicketPdfController {

    private AuthValidator authValidator;
    private ROStaffService roStaffService;
    private CustomTicketPdfService customTicketPdfService;

    @Autowired
    public CustomTicketPdfController(AuthValidator authValidator, ROStaffService roStaffService, CustomTicketPdfService customTicketPdfService) {
        this.authValidator = authValidator;
        this.roStaffService = roStaffService;
        this.customTicketPdfService = customTicketPdfService;
    }

    @PostMapping
    public void createCustomTicketPdf(@PathVariable("eventUrl") String eventUrl, @RequestBody String invoicePdfDesign, Authentication auth){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        if (roStaffService.hasHostAccessForEvent(userEvent.getUser(), userEvent.getEvent())) {
            customTicketPdfService.createCustomTicketPdf(invoicePdfDesign, userEvent.getEvent());
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }

    }

    @GetMapping("/{id}")
    public String getCustomTicketPdfById(@PathVariable("eventUrl") String eventUrl, @PathVariable("id") long id, Authentication auth){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        if (roStaffService.hasHostAccessForEvent(userEvent.getUser(), userEvent.getEvent())) {
            return customTicketPdfService.findById(id);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    @GetMapping()
    public CustomTicketPdf getCustomTicketPdfByEventId(@PathVariable("eventUrl") String eventUrl, Authentication auth){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        if (roStaffService.hasHostAccessForEvent(userEvent.getUser(), userEvent.getEvent())) {
            return customTicketPdfService.findByEvent(userEvent.getEvent());
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    @PutMapping("/{id}")
    public void updateCustomTicketPdfById(@PathVariable("eventUrl") String eventUrl, @RequestBody String invoicePdfDesign, @PathVariable("id") long id, Authentication auth){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        if (roStaffService.hasHostAccessForEvent(userEvent.getUser(), userEvent.getEvent())) {
            customTicketPdfService.updateCustomTicketPdfById(invoicePdfDesign, id);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    @DeleteMapping("/{id}")
    public void deleteCustomTicketPdfById(@PathVariable("eventUrl") String eventUrl, @PathVariable("id") long id, Authentication auth){
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        if (roStaffService.hasHostAccessForEvent(userEvent.getUser(), userEvent.getEvent())) {
            customTicketPdfService.deleteCustomTicketPdfById(id);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }
}
