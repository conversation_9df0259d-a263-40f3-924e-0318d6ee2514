package com.accelevents.controllers.rest;

import com.accelevents.auction.dto.AuctionSmsCheckout;
import com.accelevents.auction.dto.BidCheckOutDto;
import com.accelevents.common.dto.*;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.*;
import com.accelevents.domain.TicketingOrder.OrderType;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.*;
import com.accelevents.exceptions.NotAcceptableException.TicketingExceptionMsg;
import com.accelevents.exceptions.NotFoundException.UserNotFound;
import com.accelevents.messages.EnumDonationSMS;
import com.accelevents.messages.TicketType;
import com.accelevents.perfomance.dto.ItemNameCodeDto;
import com.accelevents.perfomance.dto.TicketReportData;
import com.accelevents.raffle.dto.RaffleDisplayPageSettingsDto;
import com.accelevents.registration.approval.dto.CouponCodeApprovalTicketDto;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.staff.ROStaffBasicService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.security.tokenstore.TokenStoreService;
import com.accelevents.services.*;
import com.accelevents.session_speakers.dto.UserSessionDTO;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.staff.dto.*;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.*;
import com.accelevents.virtualevents.dto.UploadCSVInfo;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.Authentication;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.util.*;

import static com.accelevents.exceptions.ForbiddenException.StaffExceptionMsg.NOT_STAFF_USER;
import static com.accelevents.exceptions.NotAcceptableException.TicketingExceptionMsg.TICKETS_ARE_NOT_CURRENTLY_FOR_SALE_FOR_THIS_EVENT;
import static com.accelevents.exceptions.NotFoundException.UserNotFound.USER_NOT_FOUND_FOR_STAFF_CHECKOUT;
import static com.accelevents.utils.ApiMessages.EVENT_URL_DESC;
import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping("/rest/events/{eventurl}/staff")
@Tag(name = "/rest/events/{eventurl}/staff")
public class RestStaffController {

    private static final Logger log = LoggerFactory.getLogger(RestStaffController.class);

    @Autowired
    private EventService eventService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private TimeZoneService timeZoneService;
    @Autowired
    private StaffPageService staffPageService;
    @Autowired
    private RaffleService raffleService;
    @Autowired
    private UserUtils userUtils;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private ROStaffBasicService roStaffBasicService;
    @Autowired
    private BidderNumberService bidderNumberService;
    @Autowired
    private DonationService donationService;
    @Autowired
    private AuctionBidService auctionBidService;
    @Autowired
    private TicketingPurchaseService ticketingPurchaseService;

    @Autowired
    private DonationCustomSmsService donationCustomSmsService;

    @Autowired
    private TicketingOrderDetailsService ticketingOrderDetailsService;

    @Autowired
    private TicketingCouponCodeService ticketingCouponCodeService;

    @Autowired
    private TicketingCheckInService ticketingCheckInService;

    @Autowired
    private TicketingDisplayService ticketingDisplayService;

    @Autowired
    private AuctionService auctionService;

    @Autowired
    private TokenStoreService tokenStoreService;

    @Autowired
    private PaymentHandlerService paymentHandlerService;

    @Autowired
	private RestAuthValidator restAuthValidator;

    @Autowired
    private TicketingRefundService ticketingRefundService;

    @Autowired
    private StripeService stripeService;

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private TicketingPurchaseFromAttendeeUploadService ticketingPurchaseFromAttendeeUploadService;

    @Autowired
    private AuthValidator authValidator;
    @Autowired
    private UserSessionService userSessionService;

    @Autowired
    private AttendeeProfileService attendeeProfileService;

    @Autowired
    private KioskModeCustomMessageService kioskModeCustomMessageService;

    @Autowired EntryExitSettingService entryExitSettingService;

    Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

    private static final String SUBMIT_PLEDGE= "submitPledge";
    private static final String RECEIVE_PAYMENT= "receivePayment";

    //@Operation(description = "Assign Attendee Access To Exhibitor Staff" , response = StaffTicketTypeDto.class)
    @PutMapping("/{staffId}/assignAttendeeAccessToExhibitorStaff")
    public List<StaffTicketTypeDto> assignAttendeeAccessToExhibitorStaff(@Parameter(description = "Event url")@PathVariable String eventurl,
                                                                         @Parameter(description = "Staff Id")@PathVariable Long staffId,
                                                                         @RequestParam(required = false) List<Long> ticketTypeIds,
                                                                         @RequestParam(required = true) boolean isAllowAttendeeAccess,
                                                                         Authentication auth) {

        UserEvent userEvent = restAuthValidator.authHasStaffAccessForEvent(auth, eventurl);
        if(CollectionUtils.isEmpty(ticketTypeIds) && isAllowAttendeeAccess)
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.SELECT_ATLEAST_ONE_TICKET);
        else
            return  staffService.assignAttendeeAccessToExhibitorStaff(userEvent.getEvent(),ticketTypeIds,staffId,userEvent.getUser(),isAllowAttendeeAccess);
    }

    // auction
    //@Operation(description = "Submit bids", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully submitted bid"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/submitBids"})
    public ResponseDto submitBids(@PathVariable String eventurl,
                                  @RequestBody @Validated StaffAuctionPurchaseDto auctionPurchaseDto, Authentication auth) throws StripeException, ApiException {
        log.info("submitBids {}" , auctionPurchaseDto);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        try {
            if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                User user = this.userService.getUserForStaffCheckout(auctionPurchaseDto.getEmail(),
                        auctionPurchaseDto.getCellNumber(), auctionPurchaseDto.getCountryCode(), event);
                if (user != null) {
                    user = this.userService.mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(user,
                            auctionPurchaseDto.getCountryCode(), auctionPurchaseDto.getPhoneNumber(), null,
                            auctionPurchaseDto);

                    return new ResponseDto(SUCCESS, this.eventService.auctionCharges(user, event, auctionPurchaseDto, true,
                            auctionPurchaseDto.getPaymenttype(), staffUser,false, languageMap));
                } else {
                    throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
                }
            } else {
                throw new ForbiddenException(NOT_STAFF_USER);
            }
        } catch (ConflictException conflictException) {    //NOSONAR
            log.info("submitBids ConflictException {}", conflictException);
            throw new ConflictException(conflictException);
        } catch (Exception e) {    //NOSONAR
            log.info("submitBids {}",e);
            throw e;
        }
    }

    //@Operation(description = "Checkout for live items purchased in live auction", response = ResponseDto.class,
//            consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully submitted bid"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/liveItem/submitBids"})
    public ResponseDto checkoutForLiveAuctionItems(@PathVariable String eventurl,
                                                   @RequestBody @Validated StaffLiveAuctionPurchaseDto staffLiveAuctionPurchaseDto,
                                                   Authentication auth) {
        log.info("submitBids {}" , staffLiveAuctionPurchaseDto);
        Event event = roEventService.getEventByURL(eventurl);
        try {
            User staffUser = this.userUtils.getUser(auth);
            if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                User user = this.userService.getUserForStaffCheckout(staffLiveAuctionPurchaseDto.getEmail(),
                        staffLiveAuctionPurchaseDto.getCellNumber(), staffLiveAuctionPurchaseDto.getCountryCode(), event);
                if (user != null) {
                    user = this.userService.mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(user,
                            staffLiveAuctionPurchaseDto.getCountryCode(), staffLiveAuctionPurchaseDto.getCellNumber(),
                            null, staffLiveAuctionPurchaseDto);

                    return new ResponseDto(SUCCESS,
                            this.eventService.liveAuctionCharges(user, event, staffLiveAuctionPurchaseDto,
                                    true, staffLiveAuctionPurchaseDto.getPaymenttype(), staffUser,languageMap));
                } else {
                    throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
                }
            } else {
                throw new ForbiddenException(NOT_STAFF_USER);
            }
        }catch (StripeException e){      //NOSONAR
            log.info("checkoutForLiveAuctionItems",e);
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        }catch (ApiException | IOException e) {   //NOSONAR
            log.info("checkoutForLiveAuctionItems",e);
            throw new NotAcceptableException(e);
        } catch (Exception e) {     //NOSONAR
            log.info("checkoutForLiveAuctionItems",e);
            throw e;
        }
    }

    //@Operation(description = "Get items purchased by paddle number", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved items"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/bidderNumber/{bidderNumber}"})
    public List<LiveItemsCodeAndAmountDto> getSubmitedBidsByPaddleNumber(@PathVariable String eventurl,
                                                                         @PathVariable Long bidderNumber, Authentication auth) {

        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return auctionBidService.findAuctionBidsByAuctionAndBidderNumber(event, bidderNumber);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }


    // RAFFLE
    //@Operation(description = "Sell tickets", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully sold tickets"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/sellTickets"})
    public ResponseDto sellTickets(@PathVariable String eventurl,
                                   @RequestParam(required = false, value = "isTerminalPayment", defaultValue = "false") Boolean isTerminalPayment,
                                   @RequestBody @Validated StaffRaffleTicketPurchaseDto raffleTicketPurchaseDto, Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            User user = this.userService.getUserForStaffCheckout(raffleTicketPurchaseDto.getEmail(),
                    raffleTicketPurchaseDto.getCellNumber(), raffleTicketPurchaseDto.getCountryCode(), event);
            if (user != null) {
                try {
                    this.userService.mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(user,
                            raffleTicketPurchaseDto.getCountryCode(), raffleTicketPurchaseDto.getPhoneNumber(), null,
                            raffleTicketPurchaseDto);
                    String successMessage = this.raffleService.purchaseTickets(user, event, raffleTicketPurchaseDto,
                            staffUser, raffleTicketPurchaseDto.getPaymenttype(), isTerminalPayment);
                    return new ResponseDto(SUCCESS, successMessage);
                } catch (StripeException e) {
                    throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
                }catch (ApiException e) {    //NOSONAR
                    log.info(e.getMessage());
                    throw new NotAcceptableException("4066001", eventService.getErrorMessage(e), eventService.getErrorMessage(e));
                } catch (Exception e) {
                    throw e;
                }
            } else {
                throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
            }
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Submit Raffle Tickets", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully submitted tickets"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/submitTickets"})
    public ResponseDto submitTickets(@PathVariable String eventurl,
                                     @RequestBody @Validated StaffRaffleSubmitTicketDto submitTicketDto, Authentication auth) {
        log.info("SubmitTicketDto : {}", submitTicketDto);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            User user = this.userService.getUserForStaffCheckout(submitTicketDto.getEmail(),
                    submitTicketDto.getCellNumber(), submitTicketDto.getCountryCode(), event);
            if (user != null) {
                String successMessage = raffleService.prepareSubmitRaffleTicket(user, event, submitTicketDto, staffUser);
                return new ResponseDto(SUCCESS, successMessage);
            } else {
                throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
            }
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    // FUND A NEED
    //@Operation(description = "Submit Pledge", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully submitted pledge"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/submitPledge"})
    public ResponseDto submitPledge(@PathVariable String eventurl,
                                    @RequestBody @Validated StaffFundANeedPurchaseDto fundANeedPurchaseDto, Authentication auth) {
        Event event = roEventService.getEventByURL(eventurl);
        try {
            User staffUser = this.userUtils.getUser(auth);
            if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                User user = this.userService.getUserForStaffCheckout(fundANeedPurchaseDto.getEmail(),
                        fundANeedPurchaseDto.getCellNumber(), fundANeedPurchaseDto.getCountryCode(), event);
                if (user != null) {
                    return new ResponseDto(SUCCESS, eventService.submitPledge(event, user, fundANeedPurchaseDto, false,
                            fundANeedPurchaseDto.getPaymenttype(), staffUser));
                } else {
                    throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
                }
            } else {
                throw new ForbiddenException(NOT_STAFF_USER);
            }
        }catch (StripeException e) {     //NOSONAR
            log.info(SUBMIT_PLEDGE,e);
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        }catch (IOException e) {     //NOSONAR
            log.info(SUBMIT_PLEDGE,e);
            throw new NotAcceptableException(e);
        }catch (ApiException e) {     //NOSONAR
            log.info(SUBMIT_PLEDGE,e);
            throw new NotAcceptableException("4066001", eventService.getErrorMessage(e), eventService.getErrorMessage(e));
        } catch (Exception e) {   //NOSONAR
            log.info(SUBMIT_PLEDGE,e);
            throw e;
        }
    }

    // DONATION
    //@Operation(description = "Donate", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully donated"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/donate"})
    public ResponseDto donateAmount(@PathVariable String eventurl,
                                    @RequestBody @Validated StaffDonationPurchaseDto donation, Authentication auth) {
        Event event = roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        try {
            if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                User user = this.userService.getUserForStaffCheckout(donation.getEmail(), donation.getCellNumber(),
                        donation.getCountryCode(), event);
                if (user != null) {
                    this.donationService.donation(user, event, donation, BiddingSource.STAFF,
                            donation.getPaymenttype(), staffUser, false);
                    return new ResponseDto(SUCCESS, donationCustomSmsService.getValueBysmsKeyAndEvent(EnumDonationSMS.DONATION_THANKS, event,staffUser));
                } else {
                    throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
                }
            } else {
                throw new ForbiddenException(NOT_STAFF_USER);
            }
        }catch (StripeException e){   //NOSONAR
            log.info(DONATE_AMOUNT,e);
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        }catch (ApiException se) {
            log.info(DONATE_AMOUNT,se);
            throw new NotAcceptableException(se);
        } catch (Exception e) {    ////NOSONAR
            log.info(DONATE_AMOUNT,e);
            throw e;
        }

    }

    // TICKETING
    //@Operation(description = "Ticket module display page setting", response = TicketDisplayPageDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved settings"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/ticketing/settings"})
    public TicketDisplayPageDto ticketingSettings(@PathVariable final String eventurl,
                                                  Authentication auth,
                                                  @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                  @RequestParam(required = false, defaultValue = "false") boolean isFromCheckInAttendeePage) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingDisplayService.getDisplayPageSettingData(event, true, null, recurringEventId,isFromCheckInAttendeePage);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Order tickets", response = OrderDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully ordered tickets"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/ticketing/order"})
    public OrderDto order(@PathVariable String eventurl, @RequestBody @Validated StaffTicketingOrderDto staffTicketingOrderDto,
                          Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {

            if (StringUtils.isBlank(staffTicketingOrderDto.getPaymentType())) {
                throw new NotAcceptableException(TicketingExceptionMsg.PAYMENT_METHOD_REQUIRED);
            }
            OrderType orderType = ticketingCheckInService.getOrderType(staffTicketingOrderDto);
            return this.ticketingPurchaseService.bookTicket(event, staffTicketingOrderDto.getTicketings(), staffUser, new Date(), orderType,
                    true, StringUtils.isNotBlank(staffTicketingOrderDto.getNote()) ? staffTicketingOrderDto.getNote() : STRING_EMPTY,STRING_EMPTY,STRING_EMPTY,0l,staffTicketingOrderDto.getUtmTrackSourceDto(),null);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Update order tickets", response = OrderDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully ordered tickets"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PutMapping(path = {"/ticketing/order/{orderid}"})
    public OrderDto updateOrder(@PathVariable String eventurl,
                                @RequestBody @Validated StaffTicketingOrderDto staffTicketingOrderDto,
                                @PathVariable Long orderid,
                                Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {

            if (StringUtils.isBlank(staffTicketingOrderDto.getPaymentType())) {
                throw new NotAcceptableException(TicketingExceptionMsg.PAYMENT_METHOD_REQUIRED);
            }
            OrderType orderType = ticketingCheckInService.getOrderType(staffTicketingOrderDto);
            return this.ticketingPurchaseService.bookTicket(orderid, event, staffTicketingOrderDto.getTicketings(), staffUser, new Date(), orderType,
                    true, StringUtils.isNotBlank(staffTicketingOrderDto.getNote()) ? staffTicketingOrderDto.getNote() : STRING_EMPTY,STRING_EMPTY,STRING_EMPTY,0l,staffTicketingOrderDto.getUtmTrackSourceDto(),null);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Ticket module get dynamic form data", response = TicketingCheckOutDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Attributes fetched"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/ticketing/order/{orderid}/formattributes"})
    public TicketingCheckOutDto showOrder(@PathVariable String eventurl, @PathVariable long orderid,
                                          @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                          @RequestParam(value = "payLater", required = false, defaultValue = "false") boolean payLater,
                                          Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return this.ticketingPurchaseService.formAttributes(staffUser, eventurl, orderid, true, recurringEventId, payLater,"0", false);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Ticket module display purchase ticket", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @RequestMapping(path = "/tickets/payment/order/{orderid}", method = RequestMethod.POST)
    public List<AttendeeDetailsDto> purchaseTicket(@PathVariable String eventurl, @PathVariable Long orderid,
                                      @RequestParam(required = false, value = "isInternationalPayment", defaultValue = "false") Boolean isInternationalPayment,
                                      @RequestParam(required = false, value = "waitListIds", defaultValue = "") String waitListIds,
                                      @RequestParam(required = false, value = "isTerminalPayment", defaultValue = "false") Boolean isTerminalPayment,
                                      @RequestParam(required = false, value = "deliveryMode") DeliveryModes deliveryMode,
                                      @RequestBody @Validated StaffTicketBookingDto ticketBookingDto, Authentication auth,
                                      HttpServletRequest request) {
        Event event = roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        User user = authenticatePurchase(ticketBookingDto, request, event, staffUser);
        try {
            return ticketingPurchaseService.purchaseTicketFromStaff(event,
                    ticketBookingDto, orderid, user,
                    null, staffUser, null,
                    waitListIds, isTerminalPayment, isInternationalPayment, deliveryMode, CheckoutFrom.ADMIN_OR_STAFF_CHECKOUT, false, false);
        }catch (StripeException e){   //NOSONAR
            log.info(PURCHASE_TICKET,e);
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        }catch (ApiException e) {    //NOSONAR
            log.info(e.getMessage());
            log.info(PURCHASE_TICKET,e);
            throw new NotAcceptableException("4066001", eventService.getErrorMessage(e), eventService.getErrorMessage(e));
        }catch (JAXBException e) {     //NOSONAR
            log.info(PURCHASE_TICKET,e);
            throw new NotAcceptableException(e);
        } catch (Exception e) {   //NOSONAR
            log.info(PURCHASE_TICKET,e);
            throw e;
        }
    }

    //@Operation(description = "Partial Payment", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @RequestMapping(path = "/tickets/partialPayment/order/{orderId}", method = RequestMethod.POST)
    public ResponseDto partialPayment(@PathVariable String eventurl, @PathVariable Long orderId,
                                                   @RequestParam(required = false, value = "isInternationalPayment", defaultValue = "false") Boolean isInternationalPayment,
                                                   @RequestParam(required = false, value = "waitListIds", defaultValue = "") String waitListIds,
                                                   @RequestParam(required = false, value = "isTerminalPayment", defaultValue = "false") Boolean isTerminalPayment,
                                                   @RequestBody @Validated AttendeePartialPaymentDto attendeePartialPaymentDto, Authentication auth) {
        Event event = roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        try {
            ticketingPurchaseService.chargePartialPayment(event,
                    attendeePartialPaymentDto, orderId,
                    null, staffUser, null,
                    waitListIds, isTerminalPayment, isInternationalPayment, CheckoutFrom.ADMIN_OR_STAFF_CHECKOUT, false);
        } catch (Exception e) {   //NOSONAR
            log.info(PURCHASE_TICKET,e);
            throw e;
        }
        return new ResponseDto(SUCCESS,SUCCESS);
    }

    private User authenticatePurchase(StaffTicketBookingDto ticketBookingDto,
                                      HttpServletRequest request,
                                      Event event,
                                      User staffUser) {
        if (!this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
        User user = this.userService.getOrCreateUserForTicketing(ticketBookingDto, event,
                EventUtils.getIP(request));
        if (user == null) {
            throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
        }
        return user;
    }

    //@Operation(description = "to apply coupon for given order", response = TicketingAppliedCouponDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PutMapping(path = "/tickets/order/{orderid}/coupon/{couponcode:.+}/{emailOrCell}")
    public TicketingAppliedCouponDto applyCoupon(@PathVariable String eventurl, @PathVariable String couponcode,
                                                 @PathVariable Long orderid, @PathVariable String emailOrCell,
                                                 @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                                 @RequestBody @Validated List<HolderEmailDiscountDto> holderEmailDiscountDtos,
                                                 Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            TicketingOrder ticketingOrder = staffPageService.setPurchaserInTicketingOrder(emailOrCell, orderid, event);
            return ticketingCouponCodeService.applyCouponCode(couponcode, orderid, event,recurringEventId, holderEmailDiscountDtos, ticketingOrder);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "delete applied coupon to order", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @DeleteMapping(path = "/tickets/order/{orderid}/coupon")
    public ResponseDto deleteCoupon(@PathVariable String eventurl, @PathVariable Long orderid, Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            this.ticketingCouponCodeService.removeCouponFromTicketingOrder(eventurl, orderid);
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(COUPON_DELETE_SUCCESS_MSG)));
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    // Bidder number register
    //@Operation(description = "newBidderNumber", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully added new bidder number"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(path = {"/newBidderNumber"})
    public ResponseDto bidderNumerRegister(@PathVariable String eventurl,
                                           @RequestBody @Validated BidderNumberDto bidderDto, Authentication auth) {
        try {
            User staffUser = this.userUtils.getUser(auth);
            Event event = roEventService.getEventByURL(eventurl);
            if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
                ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
                BidderNumber bidderNumber = this.bidderNumberService.addBidderNumber(event, bidderDto,
                        staffUser, true);
                return new ResponseDto(SUCCESS, String.format(resourceBundle.getString(languageMap.get(NEW_BIDDER_NUMBER_MESSAGE)), bidderNumber.getBidderNumber()));
            } else {
                throw new ForbiddenException(NOT_STAFF_USER);
            }
        }catch (StripeException e){
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        }catch (ApiException e) {
            throw new NotAcceptableException(e);
        } catch (ConflictException e) {
            throw new NotAcceptableException("406", e.getErrorMessage(), e.getDeveloperMessage());
        }

    }

    //@Operation(description = "updateBidderNumber", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully added new bidder number"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PutMapping(path = {"/updateBidderNumber"})
    public ResponseDto bidderNumberUpdate(@PathVariable String eventurl,
                                          @RequestBody @Validated BidderNumberDto bidderDto, Authentication auth) {
        try {
            User staffUser = this.userUtils.getUser(auth);
            Event event = roEventService.getEventByURL(eventurl);
            if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
                ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
                BidderNumber bidder = this.bidderNumberService.updateBidderNumber(event, bidderDto,
                        staffUser);
                return new ResponseDto(
                        SUCCESS, String.format(resourceBundle.getString(languageMap.get(UPDATE_BIDDER_NUMBER_MESSAGE)), bidder.getBidderNumber()));
            } else {
                throw new ForbiddenException(NOT_STAFF_USER);
            }
        } catch (StripeException e) {
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        }

    }

    // OTHER API
    //@Operation(description = "load user by module", response = StaffUserDetail.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/loaduser/{emailOrCell}/module/{module}",
            "/loaduser/{emailOrCell}/{countryCode}/module/{module}"})
    public StaffUserDetail getUserDetails(@PathVariable String eventurl, @PathVariable String emailOrCell,
                                          @PathVariable String module, @PathVariable Optional<String> countryCode, Authentication auth) {
        User staffuser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffuser, event)) {
            return this.staffPageService.getUserDetailForCheckOut(emailOrCell, countryCode, module, event);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "load user by bidder number", response = StaffUserDetail.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = "/loaduser/bidder/{bidderNumber}/module/{module}")
    public StaffUserDetail getBidderDetails(@PathVariable String eventurl,
                                            @PathVariable("bidderNumber") Integer bidderNumber,
                                            @PathVariable String module,
                                            Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return this.staffPageService.getUserDetailForCheckOut(bidderNumber, module, event);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "load user detail of user without module", response = StaffUserDetail.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/loaduser/{emailOrCell:.+}", "/loaduser/{emailOrCell:.+}/{countryCode}"})
    public EventUserInfoDto getUserDetails(@PathVariable String eventurl, @PathVariable String emailOrCell,
                                           @PathVariable Optional<String> countryCode, Authentication auth) {
        log.info("getUserDetails emailOrCell=>{}" , emailOrCell);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return this.staffPageService.getUserDetailForCheckOut(emailOrCell, countryCode, event);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Check item as per given itemcode", response = StaffItemDetail.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved selected item"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/module/{module}/prices/item/{itemCode}"})
    public StaffItemDetail getBidValues(@PathVariable String eventurl,
                                        @Parameter(description = "module name") @PathVariable String module,
                                        @PathVariable("itemCode") String itemCode, Authentication auth) {
        log.info("getBidValues {}" , itemCode);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser,event);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return staffPageService.getBidValue(event, itemCode, module, languageCode);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Check item as per given module, itemCode, itemName and itemShortName", response = StaffItemDetail.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved selected item"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/module/{module}/prices/item"})
    public List<StaffItemDetail> getItemByValuesForMobileApp(@PathVariable String eventurl,
                                        @Parameter(description = "module name") @PathVariable String module,
                                              @RequestParam(value = "searchString", required = false) String search, Authentication auth) {
        log.info("getBidValues {}" , search);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return staffPageService.getItemByValuesForMobileApp(event, search, module,languageCode);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Check item as per given itemCode, itemName and itemShortName", response = ItemNameCodeDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved selected item"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/item/search"})
    public List<ItemNameCodeDto> getMatchingItems(@PathVariable String eventurl,
                                                  @RequestParam(value = "searchString", required = false) String search,
                                                  Authentication auth) {
        log.info("getBidValues {}" , search);
        Event event = getEvent(eventurl, auth);
        return staffPageService.getItemByValues(event, search);
    }

    private Event getEvent(@PathVariable String eventurl, Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (!roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
        return event;
    }

    //@Operation(description = "Check item as per given itemCode, itemName and itemShortName", response = StaffItemDetail.class)
    @GetMapping(path = {"/prices/item/{itemCode}"})
    public StaffItemDetail getItemPriceByCode(@PathVariable String eventurl,
                                              @PathVariable String itemCode,
                                              Authentication auth) {
        Event event = getEvent(eventurl, auth);
        User staffUser = this.userUtils.getUser(auth);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser,event);
        return staffPageService.getItemDetailsByCode(event, itemCode, languageCode);
    }

    //@Operation(description = "check staff page access", response = Boolean.class, consumes = "application/json", produces = "application/json")
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "true or false based on access")})
    @GetMapping(value = "/isStaff")
    public boolean isStaff(@PathVariable String eventurl, Authentication auth) {
        log.info("checking hasStaff access or not");
        Event event = roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        try {
            return this.roStaffService.hasStaffAccessForEvent(staffUser, event);
        } catch (AuthorizationException e) {
            log.info("AuthorizationException : {}" , e.getErrorMessage());
            return false;
        }
    }

    //@Operation(description = "Get all attendees", response = AttendeeResponseContainer.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved attendees"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/allAttendees"})
    public AttendeeResponseContainer getAllAttendeesByEventAndTicketTypesAndTicketStatus(
            @PathVariable String eventurl,
            @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
            Authentication auth,
            @Parameter(description = "Pages are zero indexed, thus providing 0 for page will return the first page.") @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "100") int size,
            @RequestParam(value = "searchString", required = false) String search,
            @RequestParam(value = "dataType", required = true, defaultValue = "TICKET") DataType dataType,
            @RequestParam(value = "isUnique", required = false, defaultValue = "false") boolean isUnique,
            @RequestParam(value = "ticketTypes", required = false) List<TicketType> ticketTypes,
            @RequestParam(value = "ticketStatus", required = false) List<String> ticketStatus,
            @RequestParam(value = "ticketTypeIds", required = false) List<Long> ticketTypeIds,
            @RequestParam(value = "isBulkPrintPage", required = false) boolean isBulkPrintPage,
            @RequestParam(value = "sessionCheckinStatus", required = false) boolean sessionCheckinStatus) {

        log.info("getAllAttendeesByEvent {}" , eventurl);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingDisplayService.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus(
                    event, ticketTypes, new PageSizeSearchObj(page, size, search), ticketStatus, recurringEventId, ticketTypeIds, dataType, isUnique,isBulkPrintPage,sessionCheckinStatus );
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Get tickets stats that purchased by attendees", response = AttendeeResponseContainer.class)
    @GetMapping(path = {"/allAttendees/stats"})
    public AttendeeResponseContainer getCheckInAttendeeTicketStats(@PathVariable String eventurl,
                                                                   @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                                   @RequestParam(value = "dataType", required = true, defaultValue = "TICKET") DataType dataType,
                                                                   @RequestParam(value = "ticketTypes") List<TicketType> ticketTypes,
                                                                   @RequestParam(value = "ticketTypeIds") List<Long> ticketTypeIds,
                                                                   Authentication auth) {
        log.info("getCheckInAttendeeTicketStats {}", eventurl);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingDisplayService.getCheckInAttendeeTicketStats(event, ticketTypeIds, ticketTypes, dataType, recurringEventId);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }
    //@Operation(description = "Get all attendees with session check in details", response = AttendeeResponseContainer.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved attendees"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/allAttendees/session/{sessionId}"})
    public AttendeeResponseContainer getAllAttendeesByEventAndTicketTypesAndSessionId(
            @PathVariable String eventurl,
            @PathVariable(value = "sessionId") Long sessionId,
            @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
            Authentication auth,
            @Parameter(description = "Pages are zero indexed, thus providing 0 for page will return the first page.") @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "100") int size,
            @RequestParam(value = "searchString", required = false) String search,
            @RequestParam(value = "ticketTypeIds", required = false) List<Long> ticketTypeIds,
            @RequestParam(value = "checkInStatus", required = false) List<EnumUserSessionStatus> checkInStatus) {

        log.info("getAllAttendeesByEvent {}" , eventurl);
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingDisplayService.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(
                    event, checkInStatus, new PageSizeSearchObj(page, size, search), recurringEventId, ticketTypeIds,sessionId);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Register/Check-In event ticket to session by staff")
    @PostMapping("/user-session/register")
    public void registerUserFromStaff(
            @RequestBody UserSessionDTO userSessionDTO,
            @Parameter(description = EVENT_URL_DESC) @PathVariable("eventurl") String eventUrl,
            @RequestParam(value = "joinSession", required = false, defaultValue = "false") Boolean joinSession,
            @RequestParam(value = "pastJoinSession", required = false, defaultValue = "false") Boolean pastJoinSession,
            @RequestParam(required = false, name = "source") String source,
            @RequestParam(required = false, name = "source_description") String sourceDescription,
            Authentication auth, HttpServletRequest request){
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            User user  = userService.findByUserId(userSessionDTO.getUserId());
            String device = CommonUtil.getDeviceInfo(request);
            String sourceHeader = request.getHeader(Constants.SOURCE);
            boolean isAppLogin = StringUtils.isNotBlank(sourceHeader) && Constants.MOBILE_APP.equals(sourceHeader);
            StringBuilder barcode = new StringBuilder();
            try{
                userSessionService.registerUserFromStaff(user, userSessionDTO ,joinSession, event, pastJoinSession, device, staffUser, isAppLogin, source, sourceDescription, barcode);
            }
            catch (BaseException exception) {
                if(StringUtils.isNotBlank(barcode.toString())){
                    userSessionService.createSessionCheckinCheckoutActivityLog(event, staffUser, barcode.toString(), EnumSessionCheckInLogStatus.CHECK_IN, false, exception, false, source, sourceDescription, isAppLogin, null);
                }
                throw exception;
            } catch (Exception exception) {
                if(StringUtils.isNotBlank(barcode.toString())){
                    userSessionService.createSessionCheckinCheckoutActivityLog(event, staffUser, barcode.toString(), EnumSessionCheckInLogStatus.CHECK_IN, false, new NotAcceptableException(exception), false, source, sourceDescription, isAppLogin, null);
                }
                throw exception;
            }

        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }

    }

    //@Operation(description = "Session checkIn using barcode", response = TicketCheckInDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully checked in"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/session/checkIn/barcode/{barcodeId}")
    public ResponseDto checkInSessionUsingBarCodeId(@PathVariable("eventurl") String eventUrl,
                                                    @PathVariable("barcodeId") String barcode,
                                                    @RequestBody UserSessionDTO userSessionDTO,
                                                    @RequestParam(value = "joinSession", required = false, defaultValue = "false") Boolean joinSession,
                                                    @RequestParam(value = "pastJoinSession", required = false, defaultValue = "false") Boolean pastJoinSession,
                                                    @RequestParam(required = false, name = "source") String source,
                                                    @RequestParam(required = false, name = "source_description") String sourceDescription,
                                                    @RequestParam(required = false, name = "isRFID", defaultValue = "false") boolean isRFID,
                                       Authentication auth, HttpServletRequest httpRequest) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String device = CommonUtil.getDeviceInfo(httpRequest);
            String sourceHeader = httpRequest.getHeader(Constants.SOURCE);
            boolean isAppLogin = StringUtils.isNotBlank(sourceHeader) && Constants.MOBILE_APP.equals(sourceHeader);
            try{
                userSessionService.checkInSessionUsingBarcodeId(event, barcode, userSessionDTO, joinSession, pastJoinSession, device, staffUser, false, isAppLogin, source, sourceDescription, isRFID);
            }
            catch (BaseException e) {
                userSessionService.createSessionCheckinCheckoutActivityLog(event, staffUser, barcode, EnumSessionCheckInLogStatus.CHECK_IN, false, e, isRFID, source, sourceDescription, isAppLogin, null);
                throw e;
            } catch (Exception e) {
                userSessionService.createSessionCheckinCheckoutActivityLog(event, staffUser, barcode, EnumSessionCheckInLogStatus.CHECK_IN, false, new NotAcceptableException(e), isRFID, source, sourceDescription, isAppLogin, null);
                throw e;
            }

            return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "add new card", response = ResponseDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PutMapping(path = {"/bidder/{bidderNumber}/creditCard/{newToken}"})
    private ResponseDto addNewCard(@PathVariable Integer bidderNumber,
                                   @PathVariable String newToken,
                                   @PathVariable String eventurl,
                                   Authentication auth) {

        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            this.bidderNumberService.addNewCardToBidder(bidderNumber, newToken, event);
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(Constants.CARD_UPDATED_SUCCESSFULLY)));
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }


    //@Operation(description = "Event ticket chekin", response = TicketCheckInDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully checked in"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping("/checkin/barcode/{barcode}/checkin/{checkin}")
    public TicketCheckInDto checkingTicket(@PathVariable String eventurl,
                                           @PathVariable String barcode,
                                           @PathVariable boolean checkin,
                                           Authentication auth, HttpServletRequest httpRequest,
                                           @RequestParam(required = false, name = "source") String source,
                                           @RequestParam(required = false, name = "source_description") String sourceDescription,
                                           @RequestParam(required = false, name = "isRFID", defaultValue = "false") boolean isRFID) {
        log.debug("checkingTicket start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            String sourceHeader = httpRequest.getHeader(Constants.SOURCE);
            boolean isAppLogin = StringUtils.isNotBlank(sourceHeader) && Constants.MOBILE_APP.equals(sourceHeader);

            try {
                String device = ticketingCheckInService.getDeviceInfo(httpRequest);
                if (checkin) {
                    return ticketingCheckInService.checkInTicket(null,barcode, user, event, device,isAppLogin,source,sourceDescription, isRFID);
                } else {
                    return ticketingCheckInService.changeTicketStatusToBookFromCheckIn(barcode, user, event, device,isAppLogin, source,sourceDescription, isRFID);
                }
            } catch (IOException e) {
                ticketingCheckInService.createEntryExitActivityLog(event, user, barcode, (checkin) ? EntryExitStatus.ENTRY : EntryExitStatus.EXIT, false, new NotAcceptableException(e), isRFID, source, sourceDescription, isAppLogin, null, user.getUserId());
                throw new NotAcceptableException(e);
            } catch (BaseException e) {
                ticketingCheckInService.createEntryExitActivityLog(event, user, barcode, (checkin) ? EntryExitStatus.ENTRY : EntryExitStatus.EXIT, false, e, isRFID, source, sourceDescription, isAppLogin, null, user.getUserId() );
                throw e;
            } catch (Exception e) {
                ticketingCheckInService.createEntryExitActivityLog(event, user, barcode, (checkin) ? EntryExitStatus.ENTRY : EntryExitStatus.EXIT, false, new NotAcceptableException(e), isRFID, source, sourceDescription, isAppLogin, null, user.getUserId());
                ticketingCheckInService.sendMessageToSlack(e.getMessage(), event, user);
                throw e;
            }
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }


    //@Operation(description = "Get Holder details by barcode id", response = AttendeeProfileDto.class)
    @GetMapping("/holder-data/barcode/{barcodeid}")
    public HolderAttendeeDetailsDto getAttendeeDetailByTicketId(@PathVariable("eventurl") String eventUrl, Authentication auth, @PathVariable("barcodeid") String barcodeId) {
        UserEvent userEvent = restAuthValidator.authHasStaffAccessForEvent(auth, eventUrl);
        return attendeeProfileService.getAttendeeDetailsByBarcodeIdOrRfidTag(userEvent.getUser(),userEvent.getEvent(), barcodeId, false);
    }

    @GetMapping("/holder-data/rfid/{rfidTag}")
    public HolderAttendeeDetailsDto getAttendeeDetailByRfidTag(@PathVariable("eventurl") String eventUrl, Authentication auth, @PathVariable("rfidTag") String rfidTag) {
        User user = authValidator.authUser(auth,eventUrl);
        Event event = roEventService.getEventByURL(eventUrl);
        return attendeeProfileService.getAttendeeDetailsByBarcodeIdOrRfidTag(user,event, rfidTag, true);
    }

    //@Operation(description = "Event ticket checkin", response = TicketCheckInDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully checked in"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/checkin/eventTicket")
    public TicketCheckInDto checkingTicket(@PathVariable String eventurl,
                                           @RequestBody SearchAttendeeDto searchAttendeeDto,
                                           Authentication auth, HttpServletRequest httpRequest) {
        log.debug("checkingTicket start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            try {
                String device = ticketingCheckInService.getDeviceInfo(httpRequest);
                return ticketingCheckInService.checkInTicket(searchAttendeeDto,null, user, event, device,false,searchAttendeeDto.getSource(),searchAttendeeDto.getSourceDescription(), false);
            } catch (IOException e) {
                throw new NotAcceptableException(e);
            } catch (NotAcceptableException e) {
                throw e;
            } catch (Exception e) {
                ticketingCheckInService.sendMessageToSlack(e.getMessage(), event, user);
                throw e;
            }
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Get All Check-in Attendees", response = DataTableResponse.class)
    @GetMapping("/check-in/attendees")
    public DataTableResponse getAllCheckinAttendees(@PathVariable String eventurl, Authentication auth,@RequestParam(defaultValue = "0") int page,
                                                    @RequestParam(defaultValue = "3000") int size) {
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (!this.roStaffService.hasStaffAccessForEvent(user, event)) {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
        try {
            return ticketingCheckInService.getAllCheckInAttendees(user, event, page,size);
        } catch (IOException e) {
            throw new NotAcceptableException(e);
        } catch (Exception e) {
            throw e;
        }
    }

    //@Operation(description = "Order details", response = OrderDtoV2.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved order details "),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping("/orderDetails/barcode/{barcode}")
    public OrderDtoV2 getOrderDetailsByBarcode(@PathVariable String barcode,
                                               @PathVariable String eventurl,
                                               Authentication auth) {
        log.debug("get order details start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return ticketingOrderDetailsService.getOrderDetailsByBarcode(event, barcode);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Order data for ticket summery", response = TicketAttributeDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved order Ticket Attribute details "),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping("/orderData/barcode/{barcode}")
    public TicketAttributeDto getOrderDataByBarcode(@PathVariable String barcode,
                                                    @PathVariable String eventurl,
                                                    Authentication auth) {
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return ticketingOrderDetailsService.getOrderDataByBarcode(event, barcode, user);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }


    //@Operation(description = "receive payment and change order status", response = ResponseDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = SUCCESS),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    // TODO: This API is used to collect payment form the CheckInAttendee Page but currently this API not in used as we hide that functionality
    @PutMapping(path = "/receiveUnpaidPayment")
    public ResponseDto receivePayment(@PathVariable String eventurl,
                                      @RequestBody PaymentDetailsForStaffDto detailsForStaffDto,
                                      Authentication auth)  {
        log.debug("receivePayment by staff from checkIn page start");
        User user = this.userUtils.validateUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(user, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            try {
                ticketingPurchaseService.paymentFromStaffCheckIn(event, user, detailsForStaffDto);
            } catch (StripeException e) { //NOSONAR
                log.info(RECEIVE_PAYMENT,e);
                throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
            }catch (ApiException se) {
                log.info(RECEIVE_PAYMENT,se);
                throw new NotAcceptableException(se);
            }
            catch (Exception e) {  //NOSONAR
                log.info(RECEIVE_PAYMENT,e);
                throw e;
            }
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(Constants.RECEIVE_AMOUNT_FOR_UNPAID_TICKETS)));
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "Event ticket Reports", response = TicketCheckInDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrived total tickets reports "),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping("/ticketSummary")
    public List<TicketReportData> getAllTicketsReports(@PathVariable String eventurl,
                                                       @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                 Authentication auth) {
        log.debug("getAllTicketsReports start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return ticketingDisplayService.getAllTicketsReport(event,recurringEventId, DataType.TICKET);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Event addOn Reports", response = TicketCheckInDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrived total addOn reports "),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping("/addOnSummary")
    public List<TicketReportData> getAllAddOnReports(@PathVariable String eventurl,
                                                       Authentication auth) {
        log.debug("getAllAddOnReports start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return ticketingDisplayService.getAllTicketsReport(event,0l, DataType.ADDON);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Raffle module staff page setting", response = RaffleDisplayPageSettingsDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved settings"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/raffle/settings"})
    public RaffleDisplayPageSettingsDto raffleSetting(@PathVariable String eventurl, Authentication auth,Map<String,String> languageMap) {
        Event event = roEventService.getEventByURL(eventurl);
        User user = userUtils.getUser(auth);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return raffleService.getDispalyPageSettings(event, user, true,languageMap);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Resend text to user", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully sent message"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/resendText/{bidderNumber}"})
    public ResponseDto resendBidderInfo(@PathVariable String eventurl,
                                        @PathVariable long bidderNumber, Authentication auth) {

        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            return this.bidderNumberService.resendBidderInfo(event, staffUser, bidderNumber,languageCode,languageMap);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "list out all winners", response = WinnerDto.class, produces = APPLICATION_JSON, consumes = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "array of winners"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/winners"})
    public List<WinnerDto> getListOfWinners(@PathVariable String eventurl, Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return auctionService.getListOfWinnersForEvent(event);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "listout winner items for user", produces = APPLICATION_JSON, consumes = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "array of winner items"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping("/winner/{winnerId}")
    public List<WinnerAuctionBidData> getWinnerBidData(@PathVariable String eventurl, @PathVariable long winnerId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return auctionService.getListOfWinnerBidsForEvent(event, winnerId);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "send message to bidder", authorizations = @Authorization(value = "access_token"), response = ResponseDto.class, produces = APPLICATION_JSON, consumes = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "success or fail message for message send"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/notify/bid/{bidId}")
    public ResponseDto notifyBidder(@PathVariable String eventurl, @PathVariable Long bidId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            return auctionBidService.sendMailAndMessageToAuctionBidder(bidId, event,languageCode,languageMap);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "manually pay for auction bid", produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "success"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/manuallyPay/bid/{bidId}")
    public ResponseDto payAuctionBid(@PathVariable String eventurl, @PathVariable Long bidId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            auctionBidService.payAuctionBidManualy(bidId, staffUser, event);
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "manually pay for auction bids", produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "success"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PutMapping("/auction/payManual/bids")
    public ResponseDto payManualAuctionBids(@PathVariable String eventurl, @RequestBody List<Long> bidIds, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        if (!roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
        auctionBidService.payAuctionBidsManualy(bidIds, staffUser, event);
        return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    //@Operation(description = "", authorizations = @Authorization(value = "access_token"), produces = APPLICATION_JSON, consumes = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = EMAIL_SEND_SUCCESS_MSG),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PutMapping("/auction/markDistributed/{distribute}")
    public ResponseDto markDistributed(@PathVariable String eventurl, @PathVariable boolean distribute, @RequestBody List<Long> bidIds, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        if (!roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
        auctionService.saveItemsDistributionDetails(bidIds, distribute, event, staffUser);
        return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
    }

    //@Operation(description = "manually un paid for auction bid", produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "success"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/manuallyUnPaid/bid/{bidId}")
    public ResponseDto unPaidAuctionBidManually(@PathVariable String eventurl, @PathVariable Long bidId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            auctionBidService.unPaidAuctionBidManually(bidId, staffUser, event);
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "", authorizations = @Authorization(value = "access_token"), produces = APPLICATION_JSON, consumes = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = EMAIL_SEND_SUCCESS_MSG),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PutMapping("/markDistributed/{bidId}")
    public ResponseDto saveNotesAndMarkDistributed(@PathVariable String eventurl,
                                                   @PathVariable long bidId,
                                                   @RequestBody NoteRequestDto notes,
                                                   @RequestParam(defaultValue = "false") boolean distributed,
                                                   Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            auctionService.saveItemDistributionDetails(bidId, notes.getNote(), distributed, event, staffUser);
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "Refund auction item", response = ResponseDto.class)
    ////@ApiResponses(value = {
            //@ApiResponse(code = 200, message = "Refund successful!"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(value = "/refund")
    public ResponseDto refundAuction(@PathVariable String eventurl, Authentication auth, @RequestBody ModuleRefundDto refundDto) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            try {
                auctionBidService.refundAuctionBidAndUpdateCurrentBidForItem(refundDto.getId(), event,
                        TimeZoneUtil.getDateInUTC(refundDto.getClientDate(), event.getEquivalentTimeZone(), "dd/MM/yyyy HH:mm:ss"),staffUser);
            } catch (StripeException | ApiException e) {
                log.info("refundAuction", e);
                throw new NotAcceptableException(NotAcceptableException.AuctionExceptionMsg.AUCTION_REFUND_FAILED);
            }
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(REFUND_SUCCESS_MSG)));
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "Resend paid bid email", authorizations = @Authorization(value = "access_token"), produces = APPLICATION_JSON, consumes = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = EMAIL_SEND_SUCCESS_MSG),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/resendEmail/{bidId}")
    public ResponseDto resendReceipt(@PathVariable String eventurl, @PathVariable long bidId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            log.info("Resend mail started");
            auctionService.sendBuyerReceipt(bidId, event);
            log.info("Resend mail complete");
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(EMAIL_SEND_SUCCESS_MSG)));
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "delete auction bid", authorizations = @Authorization(value = "access_token"), produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Bid was successfully deleted."),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @DeleteMapping("/bid/{bidId}")
    public ResponseDto deleteAuctionBid(@PathVariable String eventurl, @PathVariable Long bidId, Authentication auth) throws IOException{
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            auctionBidService.deleteAuctionBidAndUpdateCurrentBidForItem(bidId, event,staffUser);
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(BID_DELETE_SUCCESS_MSG)));
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "send message to confirm payment", authorizations = @Authorization(value = "access_token"), produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "success or fail message for message send"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/notify/payment/confirm/bid/{bidId}")
    public ResponseDto confirmBidder(@PathVariable String eventurl, @PathVariable Long bidId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            String message = auctionBidService.resentAuctionBidPaymentConfirmationLink(bidId, event,languageCode,languageMap);
            return new ResponseDto(SUCCESS, message);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "send message to confirm bid with bidder name", authorizations = @Authorization(value = "access_token"), produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "success or fail message for message send"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping("/notify/confirm/bid/{bidId}/bidderName")
    public ResponseDto confirmBidWithName(@PathVariable String eventurl, @PathVariable Long bidId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            String message = auctionBidService.sendMessageToConfirmPurchaseWithBidderNameResponse(bidId, event,languageCode,languageMap);
            return new ResponseDto(SUCCESS, message);
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "auction checkout item list", authorizations = @Authorization(value = "access_token"), response = AuctionSmsCheckout.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = ""),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = "/auction/user/{userId}")
    public AuctionSmsCheckout showAuctionItem(@PathVariable String eventurl, @PathVariable Long userId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            Optional<User> userOptional = roUserService.getUserById(userId);
            if (userOptional.isPresent()) {
                User user = userOptional.get();
                AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(this.roUserService.getUserDetailForSMS(user));
                AuctionSmsCheckout auctionSmsCheckout = this.auctionService.getUnPaidItems(event, user);
                auctionSmsCheckout.setAccessToken(accessTokenContainer);
                return auctionSmsCheckout;
            } else {
                throw new NotFoundException(UserNotFound.USER_NOT_FOUND);
            }
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "auction items payment", response = ResponseDto.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Thank you for your purchase."),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @PostMapping(value = "/auction/payment")
    public ResponseDto auctionPayment(@PathVariable String eventurl,
                                      @RequestBody @Validated BidCheckOutDto bidCheckOutDetails, Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            if (null != bidCheckOutDetails && null != bidCheckOutDetails.getEmail()) {
                User user = userService.getUserByEMailOrCellNumber(bidCheckOutDetails.getEmail(), bidCheckOutDetails.getPhoneNumber(), bidCheckOutDetails.getCountryCode());
                if (user != null) {
                    user = userService.mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(user,
                            bidCheckOutDetails.getCountryCode(), bidCheckOutDetails.getPhoneNumber(),
                            bidCheckOutDetails.getEmail(), bidCheckOutDetails);
                    try {
                        eventService.bidCheckout(user, event, bidCheckOutDetails,staffUser);
                        return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(THANKS_FOR_PURCHASE_MSG)));
                    } catch (StripeException e) {   //NOSONAR
                        log.info(AUCTION_PAYMENT,e);
                        throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
                    }catch (com.squareup.square.exceptions.ApiException e){  //NOSONAR
                        log.info(AUCTION_PAYMENT,e);
                        log.info(e.getMessage());
                        throw new NotAcceptableException("4066001", eventService.getErrorMessage(e), eventService.getErrorMessage(e));
                    } catch (Exception e) {   //NOSONAR
                        log.info(AUCTION_PAYMENT,e);
                        throw e;
                    }
                } else {
                    throw new NotFoundException(UserNotFound.USER_NOT_FOUND);
                }
            } else {
                throw new AuthorizationException(NOT_AUTHORIZE);
            }
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_SALES_REP);
        }
    }

    //@Operation(description = "Ticket module display purchase ticket", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Ticket Purchased"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @RequestMapping(path = "/tickets/terminal/payment/order/{orderid}", method = RequestMethod.POST)
    public ResponseDto purchaseTicketByStripeTerminal(@PathVariable String eventurl, @PathVariable Long orderid,
                                      @RequestParam(required = false, value = "isInternationalPayment", defaultValue = "false") Boolean isInternationalPayment,
                                      @RequestParam(required = false, value = "waitListIds", defaultValue = "") String waitListIds,
                                      @RequestParam(value = "paymentIntentId") String paymentIntentId,
                                      @RequestBody @Validated StaffTicketBookingDto ticketBookingDto, Authentication auth,
                                      HttpServletRequest request) {
        Event event = roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        try {
            if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
                if (ticketing.getActivated()) {
                    User user = this.userService.getOrCreateUserForTicketing(ticketBookingDto, event,
                            EventUtils.getIP(request));
                    if (user != null) {
                        ticketingPurchaseService.purchaseTicketFromStaff(event, ticketBookingDto, orderid, user,
                                ticketBookingDto.getTokenOrIntentId(), null, staffUser, null, waitListIds, paymentIntentId, isInternationalPayment);
                        return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(SUCCESS)));
                    } else {
                        throw new NotFoundException(USER_NOT_FOUND_FOR_STAFF_CHECKOUT);
                    }
                } else {
                    throw new NotAcceptableException(
                            TICKETS_ARE_NOT_CURRENTLY_FOR_SALE_FOR_THIS_EVENT);
                }
            } else {
                throw new ForbiddenException(NOT_STAFF_USER);
            }
        } catch (Exception e) {
            throw new NotAcceptableException(e);
        }
    }

    //@Operation(description = "retrieve applicable coupon for event ticket type")
    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Coupon retrieved"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @PostMapping(path = "/couponCodes")
    public List<TicketingCoupon> getTicketingCoupons(@PathVariable String eventurl, @RequestBody List<Long> eventTicketTypeIds, Authentication auth) {
        Event event = roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingCouponCodeService.getCouponCodeByTicketingTypeId(eventTicketTypeIds, event.getEventId());
        } else {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    //@Operation(description = "Update ordered tickets status Checked_In", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    @PutMapping(path = {"/checkIn/order/{id}"})
    public ResponseDto updateOrderTicketsStausCheckIn(@PathVariable String eventurl,@PathVariable Long id,
                                                      Authentication auth, HttpServletRequest httpRequest) {
        Event event = roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        UserEvent userEvent = restAuthValidator.authHasStaffAccessForEvent(auth, eventurl);

        String device = ticketingCheckInService.getDeviceInfo(httpRequest);
        boolean isAdminStaffOrSuperAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, staffUser);
        ticketingCheckInService.changeOrderTicketsStausCheckIn(id, userEvent.getEvent(), userEvent.getUser(), device, isAdminStaffOrSuperAdmin);
        return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(SUCCESSFULLY_UPDATED_ORDER_TICKETS_STATUS_CHECK_IN)));
    }

    //@Operation(description = "get ticketing orders", response = OrderPageData.class)
    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "ticketing order array"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping("/orders")
    public OrderPageData getEventOrders(@PathVariable String eventurl,
                                        @RequestParam(required = false) String searchString,
                                        @RequestParam(required = false, name = "dataType") DataType dataType,
                                        @RequestParam(required = false, name = "searchDate") @DateTimeFormat(pattern = "dd/MM/yyyy") Date searchDate,
                                        @RequestParam(required = false, name = "searchTimeStamp") @DateTimeFormat(pattern = "MM/dd/yyyy HH:mm:ss") Date searchTimeStamp,
                                        @RequestParam(required = false, name = "ticketUpdatedAfter",defaultValue = "false") boolean ticketUpdatedAfter,
                                        @Parameter(description = "Pages are zero indexed, thus providing 0 for page will return the first page.") @RequestParam(defaultValue = "0") int page,
                                        @RequestParam(defaultValue = "10") int size,
                                        @RequestParam (required = false, defaultValue = "0") Long recurringEventId,
                                        @RequestParam(name = "orderId", required = false) Long orderId,
                                        @RequestParam(name = "isFetchDeletedOrder", required = false, defaultValue = "false") boolean isFetchDeletedOrder,
                                        Authentication auth) {
        log.debug("Staff - Serving Page to manage orders");
        log.info("while fetching orders: Include deleted orders:  {}", isFetchDeletedOrder);
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingOrderDetailsService.getOrderData(event,new PageSizeSearchObj(page,size), searchString, recurringEventId, searchDate, dataType, searchTimeStamp,isFetchDeletedOrder, orderId, ticketUpdatedAfter);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Get event info of same organizer's events for the user", response = EventDetailsPastOrganizerDto.class, responseContainer = "List")
    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "event info details"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping("/orders/pastEventsInfoSameOrganizer/{purchaserUserId}")
    public List<EventDetailsPastOrganizerDto> getPastEventInfoOfSameOrganizer(@PathVariable String eventurl, @PathVariable Long purchaserUserId, Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingOrderDetailsService.getPastEventInfoSameOrganizer(event.getEventId(),purchaserUserId);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "get ticketing order data by order id", response = RefundDto.class)
    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "ticketing order data"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping(path = { "/refund/order/{orderId}" })
    public RefundDto getRefund(@PathVariable String eventurl, @PathVariable long orderId, Authentication auth) {
        log.debug("Staff - refund");
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingRefundService.getRefundData(orderId, event);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "refund ticketing order by id", response = ResponseDto.class)
    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = REFUND_SUCCESS_MSG),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @PostMapping(path = { "/refund/order/{orderId}" })
    public ResponseDto refund(@PathVariable String eventurl, @PathVariable long orderId, @RequestBody @Validated RefundInfoArray refundDto,@RequestParam(required = false) boolean isDeleteRequest,
                              Authentication auth) {
        log.debug("Staff - refund post start");
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            try {
                ticketingRefundService.refund(orderId, event, refundDto.getRefunds(), TimeZoneUtil
                        .getDateInUTC(refundDto.getClientDate(), event.getEquivalentTimeZone(), DATE_FORMAT), true, true,staffUser,isDeleteRequest,STRING_EMPTY,refundDto.isRefundWithCancel(), true, true);
                return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(REFUND_SUCCESS_MSG)));
            } catch (StripeException | ApiException e) {
                throw new NotAcceptableException(e);
            }
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }


//    @ApiOperation(value = "Cancel order", response = ResponseDto.class)
    @PostMapping("/cancel/order/{orderId}")
    public ResponseDto cancelOrder(Authentication auth, @PathVariable String eventurl, @PathVariable long orderId,
                                   @RequestParam(required = false, value = "isSendCancelEmailToAttendees", defaultValue = "false") boolean isSendCancelEmailToAttendees) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
                ticketingRefundService.cancelOrder(orderId, event,staffUser, isSendCancelEmailToAttendees);
                return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(SUCCESS)));
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

//    @ApiOperation(value = "Cancel ticket", response = ResponseDto.class)
    @PostMapping("/cancel/ticket/{ticketId}")
    public ResponseDto cancelTicket(Authentication auth, @PathVariable String eventurl, @PathVariable long ticketId,
                                    @RequestParam(required = false, value = "isSendCancelEmailToAttendees", defaultValue = "false") boolean isSendCancelEmailToAttendees){
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            ticketingRefundService.cancelTicket(ticketId, event, staffUser, isSendCancelEmailToAttendees);
            return new ResponseDto(SUCCESS, resourceBundle.getString(languageMap.get(SUCCESS)));
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    //@Operation(description = "Stripe Terminal Connection", response = Map.class)
    @GetMapping("/terminalConnection")
    public Map<String, String> getSecretTerminalConnection(Authentication auth, @PathVariable String eventurl) {
        UserEvent userEvent = restAuthValidator.authHasStaffAccessForEvent(auth, eventurl);
        return Collections.singletonMap("token", stripeService.getSecretTerminalConnection(userEvent.getEvent()));
    }

    //@Operation(description = "Returns list of event staff basic information", response = EventUserRoleInfoDto.class)
    @GetMapping("/staffsBasicInfo")
    public EventUserWrapperDto getStaffsAndAdminByEvent(Authentication auth, @PathVariable String eventurl) {
        User user = restAuthValidator.authUser(auth,eventurl);
        Event event = roEventService.getEventByURL(eventurl);
        if (event != null) {
            return this.roStaffBasicService.getUserRoleInfoByEvent(event,user);
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    //@Operation(description = "Check Attendee CSV have correct records.", response = Boolean.class)
    @PostMapping("/checkAttendeeCSV/ticketType")
    public UploadCSVInfo checkAttendeeCSV(@RequestParam("File") MultipartFile multiPartFile,
                                          @RequestParam("ticketTypeIds") List<Long> ticketTypeIds,
                                          @RequestParam("mappings") String mappings,
                                          @PathVariable String eventurl) throws IOException {
        Event event = roEventService.getEventByURL(eventurl);
        return ticketingPurchaseFromAttendeeUploadService.checkCSVHaveCorrectRecords(multiPartFile, event, ticketTypeIds,mappings);
    }

    //@Operation(description = "Buy tickets from uploaded CSV", response = ResponseDto.class)
    @PostMapping("/uploadAttendeeCSV/ticketType")
    public UploadCSVInfo uploadAttendeeCSV(@RequestParam("File") MultipartFile multiPartFile,
                                  @RequestParam("ticketTypeIds") List<Long> ticketTypeIds,
                                  @RequestParam("mappings") String mappings,
                                  @PathVariable String eventurl, Authentication auth) throws IOException {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        UserEvent userEvent = restAuthValidator.authHasStaffAccessForEvent(auth, eventurl);
        return ticketingPurchaseFromAttendeeUploadService.purchaseTicketsFromUploadedCSV(ticketTypeIds, multiPartFile, event, userEvent.getUser(),staffUser,mappings);
    }

    //@Operation(description = "Get user ticketing orders", response = OrderPageData.class)
    @GetMapping("/user/{userId}/orders")
    public OrderPageData getUserEventOrders(@PathVariable String eventurl,
                                            @PathVariable Long userId,
                                            @RequestParam(defaultValue = "0") int page,
                                            @RequestParam(defaultValue = "10") int size,
                                            @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                            Authentication auth) {

        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            Optional<User> optionalUser = roUserService.getUserById(userId);
            if (optionalUser.isPresent()) {
                return ticketingOrderDetailsService.getUserOrderData(event, optionalUser.get(), size, page, recurringEventId);
            }
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
        throw new ForbiddenException(NOT_STAFF_USER);
    }

    /**
     * Special API using for only Audience page. Do not use.
     * Throwing HTTP 406 status if user have not staff or higher roles, to avoid redirection issue in UI.
     */
    @GetMapping("/user/{userId}/event-orders")
    public OrderPageData getUserOrdersByEvent(@PathVariable String eventurl,
                                              @PathVariable Long userId,
                                              @RequestParam(defaultValue = "0") int page,
                                              @RequestParam(defaultValue = "10") int size,
                                              @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                              Authentication auth) {
        try {
            return getUserEventOrders(eventurl, userId, page, size, recurringEventId, auth);
        } catch (ForbiddenException e) {
            if (NOT_STAFF_USER.getStatusCode().equals(e.getErrorCode())) {
                throw new NotAcceptableException(NOT_STAFF_USER.getStatusCode(), NOT_STAFF_USER.getErrorMessage(), NOT_STAFF_USER.getDeveloperMessage());
            }
            throw e;
        }
    }


    //@Operation(description = "Get kiosk configuration", response = KioskConfiguration.class)
    @GetMapping("/configure/kiosk")
    public KioskConfiguration updatekioskConfiguration(@PathVariable String eventurl,
                                                Authentication auth) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return   staffService.getStaffKioskConfiguration(staffUser,event);
        }
        throw new ForbiddenException(NOT_STAFF_USER);
    }

    //@Operation(description = "Update kiosk configuration", response = ResponseDto.class)
    @PutMapping("/configure/kiosk")
    public ResponseDto updatekioskConfiguration(@PathVariable String eventurl,
                                                    Authentication auth,
                                                    @RequestBody KioskConfiguration kioskConfiguration) {
        User staffUser = userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (roStaffService.hasStaffAccessForEvent(staffUser, event)) {
           return   staffService.updateStaffKioskConfiguration(staffUser,event,kioskConfiguration);
        }
        throw new ForbiddenException(NOT_STAFF_USER);
    }

    //@Operation(description = "Fetch user address information ")
    @GetMapping(value = "email/{email}/getAddressDetails")
    public AddressDto getAddressDetails(@PathVariable String eventurl, @PathVariable String email, Authentication auth) {
        User user = this.userUtils.validateUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (!roStaffService.hasStaffAccessForEvent(user, event)) {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
        return userService.getUserAddress(email);
    }

    @GetMapping(path = { "/ticketing-type/{ticketingTypeIds}/all-purchased-tickets-by-user/{email}"})
    public List<Map<Long, List<TicketPurchasedDetailsDto>>> getAllPurchasedTicketsByUser(@PathVariable final String eventurl, @PathVariable String ticketingTypeIds,@PathVariable String email, @RequestParam(value = "recurringEventId",required = false) Long recurringEventId, Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventurl);
        User staffUser = this.userUtils.getUser(auth);
        if (!roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
        User user = roUserService.findByEmail(email);
        return ticketingDisplayService.getAllPurchasedTicketsByUser(event,user,ticketingTypeIds,recurringEventId);
    }
    @PostMapping(path={"/get-attendees-by-eventTicketIds"})
    public List<AttendeeDtoV3> getAttendeesByEventTicketIds(@PathVariable String eventurl,
                                                            @RequestBody List<Long> eventTicketIds,
                                                            Authentication auth){
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            return ticketingDisplayService.getAttendeesList(event, eventTicketIds);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    @PostMapping("/ticketing/order/{orderId}/remove-unavailable-tickets")
    public ResponseDto removeUnavailableTickets(@PathVariable("eventurl") String eventUrl, @RequestBody List<Long> unavailableTickets, @PathVariable(value = "orderId") Long orderId, Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventUrl);
        User user = this.userUtils.getUser(auth);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            ticketingPurchaseService.removeUnavailableTicketsFromOrder(orderId,event,unavailableTickets, user);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @GetMapping("/attendee-check-in-log/CSV")
    public ResponseDto attendeeCheckInLogs(@PathVariable("eventurl") String eventUrl, Authentication auth,
                                           HttpServletResponse response) throws IOException {
        Event event = this.roEventService.getEventByURL(eventUrl);
        User user = this.userUtils.getUser(auth);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            CommonUtil.prepareDownloadableResponseHeader(response, "AttendeeCheckInLog", CONTENT_CSV);
            downloadService.attendeeCheckInLogs(event,response.getWriter());
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @GetMapping("/get-check-in-via-kiosk-messages")
    public KioskModeCustomMessageDto getCheckInViaKioskMessages(@PathVariable("eventurl") String eventUrl,Authentication auth) {
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return kioskModeCustomMessageService.getCheckInViaKioskMessagesByEventId(event.getEventId());
        }else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    @PostMapping("/rfid-barcode-mapping/barcode/{barcode}/rfid/{rfid}")
    public TicketHolderBasicDetailDto barcodeMappingWithRFID(@PathVariable String eventurl,
                                                             @PathVariable String barcode,
                                                             @PathVariable String rfid,
                                                             @RequestParam(required = false, name = "enforced") boolean enforced,
                                                             Authentication auth, HttpServletRequest httpRequest,
                                                             @RequestParam(required = false, name = "source") String source,
                                                             @RequestParam(required = false, name = "source_description") String sourceDescription) {
        log.debug("checkingTicket start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return ticketingCheckInService.barcodeMappingWithRFID(barcode, user, event,rfid, enforced, source, sourceDescription);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    @GetMapping("/entry-exit/barcode-rfid/{id}/{entry}")
    public TicketCheckInDto entryExitTicket(@PathVariable String eventurl,
                                           @PathVariable String id,
                                           @PathVariable boolean entry,
                                           Authentication auth, HttpServletRequest httpRequest,
                                           @RequestParam(required = false, name = "source") String source,
                                           @RequestParam(required = false, name = "source_description") String sourceDescription,
                                           @RequestParam(required = false, name = "isRFID", defaultValue = "false") boolean isRFID) {
        log.debug("checkingTicket start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            String sourceHeader = httpRequest.getHeader(Constants.SOURCE);
            boolean isAppLogin = StringUtils.isNotBlank(sourceHeader) && Constants.MOBILE_APP.equals(sourceHeader);
            try {
                String device = ticketingCheckInService.getDeviceInfo(httpRequest);
                return ticketingCheckInService.entryExit(id, user, event, device,isAppLogin,source,sourceDescription, isRFID, entry);
            }catch (IOException exception){
                throw new NotAcceptableException(exception);
            }catch (BaseException exception) {
                ticketingCheckInService.createEntryExitActivityLog(event, user, id, (entry) ? EntryExitStatus.ENTRY : EntryExitStatus.EXIT, false, exception, isRFID, source, sourceDescription, isAppLogin, null, user.getUserId());
                throw exception;
            } catch (Exception exception) {
                ticketingCheckInService.createEntryExitActivityLog(event, user, id, (entry) ? EntryExitStatus.ENTRY : EntryExitStatus.EXIT, false, new NotAcceptableException(exception), isRFID, source, sourceDescription, isAppLogin, null, user.getUserId());
                throw exception;
            }
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    @GetMapping("/entry-exit-settings")
    public EntryExitSettingsDTO getEntryExitSettings(@Parameter(description = "Event url") @PathVariable(required = true, name = "eventurl") String eventurl,
                                                     Authentication auth) {
        UserEvent userEvent = authValidator.authHasStaffLevelAccess(auth, eventurl);

        return entryExitSettingService.getEntryExitSettings(userEvent.getUser(), userEvent.getEvent());
    }

    @PostMapping("/entry-exit/verify-password")
    public ResponseDto validateKioskPassword(@Parameter(description = "Event url") @PathVariable String eventurl,
                                             @RequestBody EntryExitSettingsPasswordDto entryExitSettingsPasswordDto,
                                             Authentication auth) {

        UserEvent userEvent = authValidator.authHasStaffLevelAccess(auth, eventurl);
        ticketingCheckInService.validateEntryExitPassword(userEvent.getEvent(), entryExitSettingsPasswordDto);
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @PostMapping("/entry-exit/bulk/barcode/{entry}")
    public List<String> bulkEntryExitTicket(@PathVariable String eventurl,
                                            @RequestBody List<String> barcodeIds,
                                            @PathVariable boolean entry,
                                            Authentication auth, HttpServletRequest httpRequest,
                                            @RequestParam(required = false, name = "source") String source,
                                            @RequestParam(required = false, name = "source_description") String sourceDescription) {
        log.debug("checkingTicket start");
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            String sourceHeader = httpRequest.getHeader(Constants.SOURCE);
            boolean isAppLogin = StringUtils.isNotBlank(sourceHeader) && Constants.MOBILE_APP.equals(sourceHeader);
            String device = ticketingCheckInService.getDeviceInfo(httpRequest);
            return ticketingCheckInService.bulkEntryExit(barcodeIds, user, event, device, source,sourceDescription, entry, isAppLogin);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    @GetMapping("/uploadAttendeeCSV/default-fields")
    public List<String>  getAttendeeUploadDefaultFields(@PathVariable String eventurl,
                                                        @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                        Authentication auth){
        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(user, event)) {
            return ticketingPurchaseFromAttendeeUploadService.getAttendeeUploadDefaultFields(event.getEventId(),recurringEventId);
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }

    // validate coupon code for approval tickets from host side (Just validate the code not apply)
    @PostMapping("/approval-tickets/coupon")
    public TicketingAppliedCouponDto applyCouponCodeInApprovalTicket(@PathVariable String eventurl,
                                                 @RequestBody CouponCodeApprovalTicketDto couponCodeApprovalTicketDto,
                                                 Authentication auth) {
        User staffUser = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        if (this.roStaffService.hasStaffAccessForEvent(staffUser, event)) {
            User purchaser = roUserService.findByEmail(couponCodeApprovalTicketDto.getEmail());
            if(purchaser == null){
                purchaser = userService.getOrCreateUser(new UserBasicDto(couponCodeApprovalTicketDto.getFirstName(), couponCodeApprovalTicketDto.getLastName(), couponCodeApprovalTicketDto.getEmail()), event);
            }
            try {
                return ticketingCouponCodeService.applyCouponCodeInApprovalTicket(event, couponCodeApprovalTicketDto, purchaser, staffUser.getUserId());
            } catch (Exception e) {
                log.info("Exception for applyCouponCodeInApprovalTicket : event id {} by staff user {} exception {}",event.getEventId(),staffUser.getUserId(), e.getMessage());
                throw e;
            }
        } else {
            throw new ForbiddenException(NOT_STAFF_USER);
        }
    }
}
