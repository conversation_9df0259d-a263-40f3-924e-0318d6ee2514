package com.accelevents.controllers.rest;


import com.accelevents.column.selection.dto.AnalyticsColumnMasterDto;
import com.accelevents.column.selection.services.AnalyticsColumnMasterService;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.AnalyticsArea;
import com.accelevents.dto.AttendeeAnalyticsPageSizeSearchObj;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.DisplayViewService;
import com.accelevents.services.DownloadService;
import com.accelevents.ticketing.dto.DisplaySharingViewDto;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.accelevents.utils.Constants.STRING_EMPTY;



@RestController
@RequestMapping("/rest/events/{eventUrl}/display-sharing-view")
@Tag(name = "/rest/events/{eventUrl}/display-sharing-view")
public class DisplaySharingViewController {

    @Autowired
    private ROEventService roEventService;

    @Autowired
    private DisplayViewService displayViewService;

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private AnalyticsColumnMasterService columnMasterService;

    @PostMapping
    public DisplaySharingViewDto getAttendeesBySharingView(@RequestParam(value = "ViewPassword",required = false) String viewPassword,
                                                           @RequestParam(value = "displayViewId") String displayViewId,
                                                           @RequestBody @Validated AttendeeAnalyticsPageSizeSearchObj attendeeAnalyticsPageSizeSearchObj,
                                                           @Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        Event event = roEventService.getEventByURL(eventUrl);
        return displayViewService.getAttendeesBySharingView(viewPassword,attendeeAnalyticsPageSizeSearchObj,event,false,displayViewId);
    }

    @GetMapping ("/download-sharing-view/CSV")
    public void downloadSharingViewCSV(HttpServletResponse response, @RequestParam(value = "displayViewId") String displayViewId,
                                       @RequestParam(value = "searchString",required = false,defaultValue = STRING_EMPTY) String searchString,
                                       @PathVariable(required = false) String eventUrl) throws IOException {
        String fileName = "Attendee Analytics";
        Event event = roEventService.getEventByURL(eventUrl);
        CommonUtil.prepareDownloadableResponseHeader(response, fileName, Constants.CONTENT_CSV);
        downloadService.downloadSharingViewDataCSV(response.getWriter(),event,displayViewId,searchString);
    }

    @GetMapping("/get-column-selection")
    public AnalyticsColumnMasterDto getColumnMasterJson(@Parameter(description = "Event url") @PathVariable(required = false) String eventUrl) {
        Event event = roEventService.getEventByURL(eventUrl);
        return columnMasterService.getColumnMasterJson(AnalyticsArea.ATTENDEE_ANALYTICS,event);
    }
}