package com.accelevents.controllers.rest;

import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.TicketingOrder.OrderType;
import com.accelevents.domain.enums.AttributeFormType;
import com.accelevents.domain.enums.CheckoutFrom;
import com.accelevents.domain.enums.EnumLabelLanguageCode;
import com.accelevents.domain.enums.RegistrationRequestType;
import com.accelevents.domain.ticketing.TicketingCacheTicketTypeContainerDto;
import com.accelevents.dto.*;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.registration.approval.dto.CouponCodeApprovalTicketDto;
import com.accelevents.registration.approval.dto.TicketTypeCountDetails;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.eventTicket.ROTicketingDisplayService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.impl.PaymentHandlerServiceImpl;
import com.accelevents.services.impl.TicketingAppliedCouponForExistingOrderDto;
import com.accelevents.services.repo.helper.EventDesignDetailRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.session_speakers.services.ConfirmationPagesService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.*;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.text.ParseException;
import java.time.format.DateTimeParseException;
import java.util.*;

import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping("/rest/events/{eventurl}")
@Tag(name = "/rest/events/{eventurl}")
public class DisplayTicketingController {

	private static final Logger log = LoggerFactory.getLogger(DisplayTicketingController.class);

	@Autowired
	private RestAuthValidator restAuthValidator;
	@Autowired
	private EventService eventService;
    @Autowired
    private ROEventService roEventService;
	@Autowired
	private EventDesignDetailRepoService eventDesignDetailRepoService;

	@Autowired
	private UserService userService;
    @Autowired
    private ROUserService roUserService;

	@Autowired
	private AmountPayCalculationService amountPayCalculationService;

	@Autowired
	private TicketingService ticketingService;

	@Autowired
	private TicketingDownloadService ticketingDownloadService;

	@Autowired
	private TicketingHelperService ticketingHelperService;

	@Autowired
	private TimeZoneService timeZoneService;

	@Autowired
	private RecurringEventsScheduleBRService recurringEventsScheduleService;

	@Autowired
	private UserUtils userUtils;
    @Autowired
    private TokenStoreUtils tokenStoreUtils;

	@Autowired
	private TicketingOrderRepoService ticketingOrderRepoService;

	@Autowired
	private TicketingPurchaseService ticketingPurchaseService;

	@Autowired
	private TicketingCouponCodeService ticketingCouponCodeService;

	@Autowired
	private TicketingCheckInService ticketingCheckInService;

	@Autowired
	private TicketingDisplayService ticketingDisplayService;
    @Autowired
    private ROTicketingDisplayService roTicketingDisplayService;

	@Autowired
	private PaymentHandlerServiceImpl paymentHandlerService;

    @Autowired
    private PaypalPaymentService paypalPaymentService;
    @Autowired
    private VatTaxService vatTaxService;
    @Autowired
    private EventTicketsService eventTicketsService;

    @Autowired
    private WhiteLabelService whiteLabelService;

    @Autowired
    private ConfirmationPagesService confirmationPagesService;

    @Autowired
    private SSOFieldMappingService ssoFieldMappingService;

    @Autowired
    TicketExchangeRuleService ticketExchangeRuleService;

    Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

    @Value("${uiBaseurl}")
    private String uiBaseurl;

	//@Operation(description = "Ticket module display page setting", response = TicketDisplayPageDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Successfully retrieved settings"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping(path = { "/ticketing/settings" })
	public TicketDisplayPageDto ticketingSettings(@PathVariable final String eventurl,
												  @RequestParam(required = false) String accessCode,
                                                  @RequestParam(required = false,defaultValue = "false" ) boolean accessHiddenTicketType,
												  @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {

		Event event = this.roEventService.getEventByURL(eventurl);
		return ticketingDisplayService.getDisplayPageSettingData(event, accessHiddenTicketType, accessCode, recurringEventId,false);
	}

    //TODO: Merged this code with ticketing/settings api
	//@Operation(description = "Ticket module display page setting , Check Avalability Of AccessCode", response = TicketDisplayPageDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Successfully retrieved settings"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping(path = { "/ticketing/checkAvalabilityOfAccessCode" })
	public Boolean ticketingSettingsCheckAvalableAccessCode(@PathVariable final String eventurl) {
		Event event = this.roEventService.getEventByURL(eventurl);
		return ticketingDisplayService.checkAvalableAccessCode(event);
	}

	//@Operation(description = "Ticket module book ticket", response = OrderDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Order Created"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PostMapping(path = { "/ticketing/order" }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public OrderDto order(@PathVariable("eventurl") String eventURL,
			@RequestBody @Validated TicketingOrderRequestDto dto, Authentication auth,
                          @RequestParam(required = false, value = "accessCodeId", defaultValue = "") String accessCode,
                          @RequestParam(required = false, value = "limitedDisplayCodeId", defaultValue = "") String limitedDisplayCodeId,
                          @RequestParam(required = false, value = "recurringEventId", defaultValue = "0") Long recurringEventId,
                          HttpServletRequest httpRequest) {
		Event event = this.roEventService.getEventByURL(eventURL);
		User user = this.userUtils.getUser(auth);
		OrderDto orderDto = ticketingPurchaseService.bookTicket(
		        event, dto.getTicketings(), user, new Date(), OrderType.CARD, false, STRING_EMPTY,accessCode,limitedDisplayCodeId,recurringEventId,dto.getUtmTrackSourceDto(),null);
		String authToken = httpRequest.getHeader(Authorization);
		this.tokenStoreUtils.updateUser(auth, user, authToken);
		return orderDto;
	}

	//@Operation(description = "Ticket module book ticket", response = OrderDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Order Created"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PutMapping(path = { "/ticketing/order/{orderid}" }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public OrderDto updateOrder(@PathVariable("eventurl") String eventURL,
						          @RequestBody @Validated TicketingOrderRequestDto dto,
								  @PathVariable Long orderid,
								  Authentication auth,
                                  @RequestParam(required = false, value = "accessCodeId", defaultValue = "") String accessCode,
                                  @RequestParam(required = false, value = "limitedDisplayCodeId", defaultValue = "") String limitedDisplayCodeId,
                                  @RequestParam(required = false, value = "recurringEventId", defaultValue = "0") Long recurringEventId,
                                  HttpServletRequest httpRequest) {
		Event event = this.roEventService.getEventByURL(eventURL);
		User user = this.userUtils.getUser(auth);
		OrderDto orderDto = ticketingPurchaseService.bookTicket(orderid, event, dto.getTicketings(), user, new Date(),OrderType.CARD, false, STRING_EMPTY,accessCode,limitedDisplayCodeId,recurringEventId,dto.getUtmTrackSourceDto(),null);
		String authToken = httpRequest.getHeader(Authorization);
		this.tokenStoreUtils.updateUser(auth, user, authToken);
		return orderDto;
	}

	@PostMapping("/createPaypalOrder")
    public PaypalCreateOrderResponse createPaypalOrder(@PathVariable("eventurl") String eventURL) {
        Event event = this.roEventService.getEventByURL(eventURL);
        return paypalPaymentService.createOrder(event);
    }

    @PostMapping("/capturePaypalOrder/{orderId}")
    public PaypalCaptureOrderResponse capturePayaplOrder(@PathVariable("eventurl") String eventURL, @PathVariable("orderId") String orderId) {
        Event event = this.roEventService.getEventByURL(eventURL);
        return paypalPaymentService.captureOrder(event, orderId);
    }

    @GetMapping("/getPaypalClientToken")
    public PaypalClientTokenResponse getPaypalClientToken(@PathVariable("eventurl") String eventURL) throws UnirestException {
        Event event = this.roEventService.getEventByURL(eventURL);
        return paypalPaymentService.getClientTokenAndMerchantId(event);
    }

	//@Operation(description = "get all available category for the seating type ticketing")
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "array of categories dto type"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping("/ticketing/seatingCategories")
	public List<CategoryDto> getSeatingCategories(@PathVariable("eventurl") String eventUrl,
												  @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
		Event event = this.roEventService.getEventByURL(eventUrl);
		return this.ticketingDisplayService.getSeatingCategories(event, recurringEventId);
	}


	//@Operation(description = "Ticket module get dynamic form data", response = TicketingCheckOutDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Attributes fetched"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping(path = { "/ticketing/order/{orderid}/formattributes" })
	public TicketingCheckOutDto showOrder(@PathVariable String eventurl, @PathVariable long orderid,
										  @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                          @RequestParam(value = "payLater", required = false, defaultValue = "false") boolean payLater,
                                          @RequestParam(value = "registrationRequestId",required = false, defaultValue = "0") String registrationRequestId,
                                          Authentication auth, HttpServletRequest httpRequest,
                                          @RequestParam(value = "isOldOrder", required = false, defaultValue = "false") boolean isOldOrder) {
//        isOldOrder is used for the add to Order flow (for adding new tickets in old order)
		User user = this.userUtils.getUser(auth);
		TicketingCheckOutDto ticketingCheckOutDto = this.ticketingPurchaseService.formAttributes(user, eventurl, orderid,
				false, recurringEventId, payLater,registrationRequestId, isOldOrder);
		String authToken = httpRequest.getHeader(Authorization);
		this.tokenStoreUtils.updateUser(auth, user, authToken);
		return ticketingCheckOutDto;
	}

    /*
     This method is used to get the checkout form attributes by ticket type id for the Embedded checkout form
     */
    @GetMapping(path = { "/ticketing/checkout-form-attributes" })
    public TicketingCheckOutDto getTicketingCheckoutAttributes(@PathVariable String eventurl,
                                          @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                            @RequestParam(value="ticketTypeId") Long ticketTypeId,
                                          Authentication auth) {
        User user = this.userUtils.getUser(auth);
        return this.ticketingPurchaseService.getTicketCheckOutFormAttributes(user, eventurl, recurringEventId, ticketTypeId);
    }

    //@Operation(description = "Attendee Registration form data", response = AttendeeAttributeWithTypeAndTableDto.class)
    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Attributes fetched"),
                            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
                            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
                            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @PostMapping(path = { "/ticketing/registrationAttributes" })
    public AttendeeAttributesForRegistrationApproval getAttributesForRegistration(@PathVariable String eventurl,
                                                                             @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                                                             @RequestBody List<TicketTypeCountDetails> ticketTypeCountDetails,
                                                                             Authentication auth) {
        this.userUtils.getUser(auth);
        return this.ticketingPurchaseService.getFormAttributes(eventurl, recurringEventId, ticketTypeCountDetails);
    }

    //@Operation(description = "Get Order Amount Details", response = AmountPayDTO.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Attributes fetched"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@Deprecated
    @GetMapping(path = { "/ticketing/order/{id}/amountDetails" })
	public AmountPayDTO amountDetails(@PathVariable String eventurl,
											  @PathVariable long id) {
		Event event = roEventService.getEventByURL(eventurl);
		return amountPayCalculationService.getTicketPriceDetails(id,event);
	}

	//@Operation(description = "Ticket module display purchase ticket", response = ResponseDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Ticket Purchased"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PostMapping(path = "/tickets/payment/order/{orderid}")
	public ResponseDto purchaseTicket(@PathVariable String eventurl, @PathVariable Long orderid,
			@RequestBody @Validated TicketBookingDto ticketBookingDto, Authentication auth,
			@RequestParam(required = false, value = "isInternationalPayment", defaultValue = "false") Boolean isInternationalPayment,
			@RequestParam(required = false, value = "trackingUrl", defaultValue = "") String trackingUrl,
			@RequestParam(required = false, value = "waitListIds", defaultValue = "") String waitListIds,
            @RequestParam(required = false,value = "isWithoutLogin",defaultValue = "false") Boolean isWithoutLogin,
            @RequestParam(required = false, value = "recurringEventId", defaultValue = "0") long recurringEventId,
			HttpServletRequest httpRequest) {
		Event event = roEventService.getEventByURL(eventurl);
        User user;
        if (Boolean.TRUE.equals(isWithoutLogin)) {
            user = userService.getOrCreateUserForTicketing(ticketBookingDto, event, EventUtils.getIP(httpRequest));
        } else {
            user = userUtils.validateUser(auth);
        }

		boolean isAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
		String purchaseTicket = "purchaseTicket method";
		try {
			ticketingPurchaseService.purchaseTicket(event, ticketBookingDto, orderid, user,
					null, null, trackingUrl, waitListIds,
					false, isInternationalPayment, isAdmin, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, recurringEventId);
			tokenStoreUtils.updateUser(auth, user, httpRequest.getHeader(Authorization));
			if(!isAdmin){
			    // Send ticket purchase event to kinesis
    			ticketingPurchaseService.sendTicketPurchaseDataToKinesis(event, user, orderid);
            }
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));

		}catch (StripeException e){//NOSONAR
            log.info(purchaseTicket,e);
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
		}catch (JAXBException | ApiException | ParseException e) {//NOSONAR
            log.info(purchaseTicket, e);
            throw new NotAcceptableException(e);
        } catch (DateTimeParseException e) {
            log.info(purchaseTicket,e);
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_DATE);
        } catch (Exception e) {//NOSONAR
            log.info(purchaseTicket,e);
			throw e;
		}
	}
    //@Operation(description = "Payment for unpaid or partial payment", response = ResponseDto.class)
    @PostMapping(path = "/payment/order")
    public ResponseDto paymentLinkOrder(@PathVariable String eventurl, Authentication auth,
                                   @RequestBody @Validated PartialPaymentDueDTO partialPaymentDueDTO,
                                   @RequestParam(required = false, value = "isInternationalPayment", defaultValue = "false") Boolean isInternationalPayment,
                                   @RequestParam(required = false, value = "trackingUrl", defaultValue = "") String trackingUrl,
                                   @RequestParam(required = false, value = "waitListIds", defaultValue = "") String waitListIds,
                                   HttpServletRequest httpRequest) throws StripeException, JAXBException, ApiException {
        Event event = roEventService.getEventByURL(eventurl);
        TicketingOrder ticketingOrder = ticketingOrderRepoService.findById(partialPaymentDueDTO.getOrderId()).orElseThrow(() -> new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND));
        User user = ticketingOrder.getPurchaser();
        boolean isAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        String paymentTicket = "Payment for unpaid or partial order";
        try {
            ticketingPurchaseService.paymentForUnpaidOrPartialOrder(event, partialPaymentDueDTO.getTokenOrIntentId(), partialPaymentDueDTO.getOrderId(),
                    null, null, trackingUrl,
                    false, isInternationalPayment, isAdmin,  false);
            tokenStoreUtils.updateUser(auth, user, httpRequest.getHeader(Authorization));
            if(!isAdmin){
                // Send ticket purchase event to kinesis
                ticketingPurchaseService.sendTicketPurchaseDataToKinesis(event, user, partialPaymentDueDTO.getOrderId());
            }
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));

        } catch (DateTimeParseException e) {
            log.info(paymentTicket,e);
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_DATE);
        } catch (Exception e) {//NOSONAR
            log.info(paymentTicket,e);
            throw e;
        }
    }

    @PostMapping(path = "/payment/ticket")
    public ResponseDto paymentLinkTicket(@PathVariable String eventurl, Authentication auth,
                                        @RequestBody PartialPaymentDueDTO partialPaymentDueDTO,
                                        @RequestParam(required = false, value = "isInternationalPayment", defaultValue = "false") Boolean isInternationalPayment,
                                        HttpServletRequest httpRequest) throws StripeException, JAXBException, ApiException {
        Event event = roEventService.getEventByURL(eventurl);
        TicketingOrder ticketingOrder = ticketingOrderRepoService.findById(partialPaymentDueDTO.getOrderId()).orElseThrow(() -> new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND));
        User user = ticketingOrder.getPurchaser();
        boolean isAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        String paymentTicket = "Payment for unpaid or partial ticket";
        try {
            ticketingPurchaseService.paymentForUnpaidOrPartialTicket(event, partialPaymentDueDTO.getTokenOrIntentId(), partialPaymentDueDTO.getOrderId(),partialPaymentDueDTO.getEventTicketId(),
                     isInternationalPayment, isAdmin,  false);
            tokenStoreUtils.updateUser(auth, user, httpRequest.getHeader(Authorization));
            if(!isAdmin){
                // Send ticket purchase event to kinesis
                ticketingPurchaseService.sendTicketPurchaseDataToKinesis(event, user, partialPaymentDueDTO.getOrderId());
            }
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));

        } catch (DateTimeParseException e) {
            log.info(paymentTicket,e);
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_DATE);
        } catch (Exception e) {//NOSONAR
            log.info(paymentTicket,e);
            throw e;
        }
    }


    //@Operation(description = "to apply coupon for given order", response = TicketingAppliedCouponDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Ticket Purchased"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PutMapping(path = "/tickets/order/{orderid}/coupon/{couponcode:.+}")
	public TicketingAppliedCouponDto applyCoupon(@PathVariable String eventurl, @PathVariable String couponcode,
												 @PathVariable Long orderid,
												 @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                                 @RequestParam(value = "email", required = false, defaultValue = "") String email,
                                                 @RequestBody @Validated List<HolderEmailDiscountDto> holderEmailDiscountDtos,
												 Authentication auth) {
		Event event = roEventService.getEventByURL(eventurl);
		User user = this.userUtils.getUser(auth);
		if(user == null && StringUtils.isNotEmpty(email)){
            user = roUserService.findByEmail(email);
		}
		TicketingOrder ticketingOrder = ticketingDisplayService.setPurchaserInTicketingOrder(user, orderid, event);
		return ticketingCouponCodeService.applyCouponCode(couponcode, orderid, event, recurringEventId, holderEmailDiscountDtos, ticketingOrder);
	}

	//@Operation(description = "delete applied coupon to order", response = ResponseDto.class, consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Ticket Purchased"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@DeleteMapping(path = "/tickets/order/{orderid}/coupon")
	public ResponseDto deleteCoupon(@PathVariable String eventurl, @PathVariable Long orderid) {
		this.ticketingCouponCodeService.removeCouponFromTicketingOrder(eventurl, orderid);
        Event event = roEventService.getEventByURL(eventurl);
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(null, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));
	}

	/**
	 * This method is Deprecated please use new method com.accelevents.controllers.rest.UserMyTicketController#exportEventTicketPass
	 */
	//@Operation(description = "Export ticket pass")
	@Deprecated
	@GetMapping("/ticket/download/pass/{barcode}")
	public void exportEventTicketPass(HttpServletResponse response, @PathVariable String barcode) throws IOException {
        CommonUtil.prepareDownloadableResponseHeader(response, "TicketPass", APPLICATION_VND_APPLE_PKPASS);
		ticketingDownloadService.downloadTicketPass(barcode, response);
	}

	/**
	 * This method is deprecated please use new method com.accelevents.controllers.rest.UserMyTicketController#exportEventTicketsZip
	 */
	//@Operation(description = "Export ticket pass")
	@Deprecated
	@GetMapping("/ticket/download/pass/order/{orderId}")
	public void exportEventTicketsZip(HttpServletResponse response, @PathVariable Long orderId) throws IOException {
        CommonUtil.prepareDownloadableResponseHeader(response, TICKET_PASS_ZIP, APPLICATION_ZIP);
		ticketingDownloadService.downloadTicketOrderPassZip(orderId, response);
	}

	//@Operation(description = "Ticket module display page setting", response = TicketDisplayPageDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Successfully retrieved settings"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping(path = { "/ticketing/waitList/settings/{waitListIds}" })
	public TicketDisplayPageDto getWaitListCheckout(@PathVariable final String eventurl, @PathVariable String waitListIds, @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
		Event event = this.roEventService.getEventByURL(eventurl);
		return ticketingCheckInService.getTicketTYpesDetailsForWaitListCheckout(event, SecurityUtils.decode(waitListIds), recurringEventId);
	}


	//@Operation(description = "Update triggered for exit intent popup", response = OrderDto.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Order Created"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@PutMapping(path = { "/ticketing/order/{orderId}/popupTriggered" })
	public Boolean exitIntentPopupTriggered(@PathVariable("eventurl") String eventURL,
											@PathVariable Long orderId,Authentication auth) {
		Event event = this.roEventService.getEventByURL(eventURL);
		if(event != null){
			return this.ticketingOrderRepoService.setExitIntentPopupTriggered(orderId,event);
		}
		return false;
	}
    //@Operation(description = "get list of recurring events by eventUrl", response = RecurringScheduleResDto.class)
    ////@ApiResponses(value = { //@ApiResponse(code = 200, message = SUCCESS),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
    @GetMapping(path = { "/ticketing/recurringEvents" })
    public List<RecurringEventResDto> getRecurringEvents(@PathVariable final String eventurl) {
        log.debug("list of recurring events");
        Event event = this.roEventService.getEventByURL(eventurl);
        return recurringEventsScheduleService.getAllRecurringEventsByEvent(event, true);
    }

	//@Operation(description = "Download iCal calendar")
	@GetMapping(path = {"/download/iCal/{eventName}/{startDate}/{endDate}/{description}/{eventAddress}"})
	public HttpEntity<byte[]> calendarIcal(@PathVariable String eventurl,
			                               @PathVariable String eventName,
										   @PathVariable String startDate,
										   @PathVariable String endDate,
										   @PathVariable String description,
										   @PathVariable String eventAddress) {

		String  calenderInvite = eventDesignDetailRepoService.getEventCalenderInviteByEventUrl(eventurl);
		String seatsForCal = StringUtils.isNotBlank(calenderInvite) ? calenderInvite : "Description not available";
        Event event=eventService.findEventByEventURLWithoutCache(eventurl);
        String buffer;
        if (null != event.getOrganizer()) {
            buffer = EmailUtils.getCalendarText(eventName, startDate, endDate, seatsForCal, eventAddress, event.getOrganizer().getName() != null ? event.getOrganizer().getName() : STRING_EMPTY, event.getOrganizer().getContactEmailAddress() != null ? event.getOrganizer().getContactEmailAddress() : STRING_EMPTY,null);
        } else {
            buffer = EmailUtils.getCalendarText(eventName, startDate, endDate, seatsForCal, eventAddress, STRING_EMPTY, STRING_EMPTY,null);
        }
        HttpHeaders header = new HttpHeaders();
		header.setContentType(new MediaType("text", "calendar"));
		header.set(HttpHeaders.CONTENT_DISPOSITION,
				"attachment; filename=calendar.ics");
		header.setContentLength(buffer.getBytes().length);

		return new HttpEntity<>(buffer.getBytes(), header);
	}

	//@Operation(description = "Download iCal calendar")
	@GetMapping(path = {"/download/iCal/event/{id}"})
	public HttpEntity<byte[]> calendarIcal(@PathVariable("id") Long eventId) {

		Event event = roEventService.findEventByEventId(eventId);
        if(null==event){
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }
        String urlOfEvent;
        if(StringUtils.isNotBlank(event.getEventURL())){
            urlOfEvent = "Click this link to see the event page: " + uiBaseurl + "/e/" + event.getEventURL();
        }else{
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_URL_NOT_FOUND);
        }
        String  calenderInvite = eventDesignDetailRepoService.getEventCalenderInviteByEventUrl(event.getEventURL());

		String seatsForCal = StringUtils.isNotBlank(calenderInvite) ? calenderInvite : urlOfEvent;

		Ticketing ticketing = ticketingService.findByEvent(event);
		String startDate = String.format("%s", StringTools.formatDateBasedOnPattern(
				ticketing.getEventStartDate(),
				"yyyyMMdd'T'HHmmss'Z'"));
		String endDate = String.format("%s", StringTools.formatDateBasedOnPattern(
				ticketing.getEventEndDate(),
				"yyyyMMdd'T'HHmmss'Z'"));

        String buffer = EmailUtils.getCalendarText(event.getName(), startDate, endDate, seatsForCal, ticketing.getEventAddress(), (event.getOrganizer() != null && event.getOrganizer().getName() != null) ? event.getOrganizer().getName() : STRING_EMPTY, (event.getOrganizer() != null && event.getOrganizer().getContactEmailAddress() != null)  ? event.getOrganizer().getContactEmailAddress() : STRING_EMPTY,null);
		HttpHeaders header = new HttpHeaders();
		header.setContentType(new MediaType("text", "calendar"));
		header.set(HttpHeaders.CONTENT_DISPOSITION,
				"attachment; filename=calendar.ics");
		header.setContentLength(buffer.getBytes().length);

		return new HttpEntity<>(buffer.getBytes(), header);
	}

	//@Operation(description = "Check ticket type is applicable for access code.", response = Boolean.class)
	@GetMapping(path = {"/isAccessCodeApplicable/ticketType/{ticketTypeId}"})
	public boolean isAccessCodeApplicable(@PathVariable("eventurl") String eventUrl,
													   @RequestParam(required = true) String accessCode,
													   @PathVariable Long ticketTypeId,
													   @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
		Event event = this.roEventService.getEventByURL(eventUrl);
        return ticketingDisplayService.isTicketTypeIsRelevantToAccessCode(event,accessCode,recurringEventId,ticketTypeId);
	}

	//@Operation(description = "Calculate fees of selected tickets", response = List.class)
	@PostMapping("/calculateFee")
	public List<DisplayFeeDto> retrieveFee(@PathVariable("eventurl") String eventUrl,
										   @RequestBody List<TicketTypeAndQuantityDto> ticketTypeAndQuantityDto) {
		log.info("calculate fees of selected tickets start");
		Event event = roEventService.getEventByURL(eventUrl);
		return ticketingDisplayService.calculateFeeByTicketTypeId(ticketTypeAndQuantityDto, event);
	}

    //TODO: Remove API after FE start using cache API APIGW_UN //NOSONAR
    //@Operation(description = "Ticket module display page setting", response = TicketingSettingDto.class)
    @GetMapping(path = { "/displayTicketing/settings" },consumes=MediaType.ALL_VALUE,produces = APPLICATION_JSON)
    @ExceptionHandler(HttpMediaTypeNotAcceptableException.class)
    public TicketingSettingDto displayTicketingSettings(@PathVariable final String eventurl,
                                                  @RequestParam(required = false) String accessCode,
                                                  @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
        Event event = this.roEventService.getEventByURL(eventurl);
        return ticketingDisplayService.getTicketingDisplayPageSetting(event, accessCode, recurringEventId);
    }

    //TODO: Remove API after FE start using cache API APIGW_UN //NOSONAR
    //@Operation(description = "Ticket module display page setting", response = TicketingSettingDto.class)
    @GetMapping(path = { "/displayTicketing/settings/portal" },consumes=MediaType.ALL_VALUE,produces = APPLICATION_JSON)
    public TicketingBasicSettingDto displayTicketingSettingsPortal(@PathVariable final String eventurl,
                                                        @RequestParam(required = false) String accessCode,
                                                        @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
        Event event = this.roEventService.getEventByURL(eventurl);
        return ticketingDisplayService.getTicketingDisplayPageSettingPortal(event);
    }

    //@Operation(description = "Ticket module display page setting", response = TicketingSettingPriceDto.class)
    @Deprecated
    @GetMapping(path = { "/displayTicketing/settings/display" },consumes=MediaType.ALL_VALUE,produces = APPLICATION_JSON)
    public TicketingSettingPriceDto displayTicketingSettingsDisplay(@PathVariable final String eventurl,
                                                                   @RequestParam(required = false) String accessCode,
                                                                   @RequestParam(required = false,defaultValue = "0") Long recurringEventId,  Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventurl);
        User user = userUtils.getUser(auth);
        return ticketingDisplayService.getTicketingDisplayPageSettingDisplay(event, accessCode, recurringEventId, user);
    }

    //@Operation(description = "Ticket module display page setting", response = TicketingSettingPriceDto.class)
    @GetMapping(path = { "/ticketing/registration-request-status" },consumes=MediaType.ALL_VALUE,produces = APPLICATION_JSON)
    public RegistrationRequestStatusDto getRegistrationRequestStatus(@PathVariable final String eventurl,
                                                                    @RequestParam(required = false,defaultValue = "0") Long recurringEventId,  Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventurl);
        User user = userUtils.getUser(auth);
        return roTicketingDisplayService.getRegistrationRequestStatusDto(event, recurringEventId, user);
    }
    //@Operation(description = "Users Information of orders", response = OrderUserInfoDto.class)
    @GetMapping(path = { "/displayTicketing/settings/userdataOfOrders" },consumes=MediaType.ALL_VALUE,produces = APPLICATION_JSON)
    public OrderUserInfoDto userDataOfOrders(@PathVariable final String eventurl,@RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
        Event event = this.roEventService.getEventByURL(eventurl);
        return ticketingDisplayService.getUsersDataOfOrders(event,recurringEventId);
    }

    //@Operation(description = "Generate secure token for PayFlow", response = PayFlowOrderDto.class,
//            consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    @PostMapping(path = "/payflow/token/order/{orderid}")
    public PayFlowOrderDto generatePayFlowSecureToken(@PathVariable String eventurl,
                                                      @RequestParam(value = "amount") Double amount,
                                                      Authentication auth) {
        this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        return ticketingDisplayService.generatePayFlowSecureToken(event, amount);
    }

    @GetMapping("/{orderId}/status")
    public String getOrderStatus(@PathVariable String eventurl, @PathVariable Long orderId,Authentication auth) {
        this.userUtils.getUser(auth);
        roEventService.getEventByURL(eventurl);
	    return ticketingDisplayService.getOrderStatus(orderId);
    }

    @GetMapping("/isVatEnabled")
    public boolean isVatTaxEnabled(@PathVariable String eventurl) {
        Event event = roEventService.getEventByURL(eventurl);
        return vatTaxService.isVatTaxEnabled(event);
    }

    //@Operation(description = "Update agree disclaimer ")
    @PutMapping(value = "/updateAgreeVirtualEventHubDisclaimer/{status}")
    public ResponseDto updateAgreeVirtualEventHubDisclaimer(Authentication auth,
                                                            @Parameter(description = "Event url") @PathVariable("eventurl") String eventUrl, @PathVariable("status") boolean status) {

        User user = this.userUtils.getUser(auth);
        Event event = roEventService.getEventByURL(eventUrl);
        if (null == event){
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }else{
            return eventTicketsService.updateAgreeVirtualEventHubDisclaimer(event,user,languageMap,status);
        }
    }

	//@Operation(description = "Get all discount code of event", response = List.class)
	////@ApiResponses(value = { //@ApiResponse(code = 200, message = "Successfully retrieved settings"),
			//@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
			//@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
			//@ApiResponse(code = 404, message = "The resource you were trying to reach is not found") })
	@GetMapping(path = { "/ticketing/discountCodes" })
	public List<DiscountCouponDto> getDiscountCodes(@PathVariable final String eventurl,
												  @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
		Event event = this.roEventService.getEventByURL(eventurl);
		return ticketingDisplayService.getAllDiscountCodes(event, recurringEventId);
	}

    @GetMapping("/payLater/order/{orderId}")
    public OrderInvoiceDto getInvoiceOrder(@PathVariable String eventurl,
                                           @PathVariable("orderId") String encryptedOrderId,
                                           Authentication auth) {
        Event event = roEventService.getEventByURL(eventurl);
        long orderId = decryptOrderId(encryptedOrderId);
        return ticketingDisplayService.getPayLaterOrder(orderId, event);
    }

    @GetMapping("/payLater/order/{orderId}/ticket/{eventTicketId}")
    public TicketInvoiceDto getInvoiceOrder(@PathVariable String eventurl,
                                           @PathVariable("orderId") String encryptedOrderId,
                                           @PathVariable("eventTicketId") String encryptedEventTicketId) {
        Event event = roEventService.getEventByURL(eventurl);
        long orderId = decryptOrderId(encryptedOrderId);
        long eventTicketId = decryptOrderId(encryptedEventTicketId);

        return ticketingDisplayService.getTicketDetailForCheckOut(orderId, event, eventTicketId);
    }
    private long decryptOrderId(String encryptedOrderId) {
        String stringOrderId = EncryptUtils.decrypt(encryptedOrderId);
        if(null != stringOrderId){
            return Long.parseLong(stringOrderId);
        } else {
            throw new NotFoundException(NotFoundException.NotFound.ORDER_ID_NOT_VALID);
        }
    }

    //@Operation(description = "Ticketing access code and event capacity reach check", response = TicketingTypeCodeAndCapacityInfo.class)
    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully retrieved settings"),
            //@ApiResponse(code = 401, message = "You are not authorized to view the resource"),
            //@ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            //@ApiResponse(code = 404, message = "The resource you were trying to reach is not found")})
    @GetMapping(path = {"/ticketing/access-code-and-capacity-info"})
    public TicketingTypeCodeAndCapacityInfo getTicketingTypeAccessCodeAndAvailableCapacityInfo(@PathVariable("eventurl") String eventUrl,
                                                                                               @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId) {
        Event event = roEventService.getEventByURL(eventUrl);
        return ticketingDisplayService.getTicketingTypeAccessCodeAndAvailableCapacityInfo(event, recurringEventId);
    }

    //@Operation(description = "Ticket module display page ticket types", response = TicketingCacheTicketTypeContainerDto.class)
    @GetMapping(path = { "/all-ticket-types-with-hidden-ticket-types" },consumes= MediaType.ALL_VALUE,produces = APPLICATION_JSON)
    public TicketingCacheTicketTypeContainerDto getAllTicketTypesWithHiddenTicketTypes(@PathVariable("eventurl") final String eventurl,
                                                                            @RequestParam(required = false,defaultValue = "0") Long recurringEventId) {
        Event event = this.roEventService.getEventByURL(eventurl);
        return ticketingDisplayService.getAllTicketTypesWithHiddenTicketTypes(event, recurringEventId);
    }

    @PostMapping("/check-unique-emails-for-holders")
    public Set<String> checkUniqueEmailForHolders(@PathVariable("eventurl") String eventUrl, @RequestBody List<String> holderEmails) {
        Event event = this.roEventService.getEventByURL(eventUrl);
        Set<String> duplicateEmails;
        if(!holderEmails.isEmpty()) {
            duplicateEmails =  ticketingDisplayService.checkUniqueEmailForHolders(event, holderEmails);
        }else {
            log.info("holderEmails list is Empty");
            duplicateEmails = Collections.emptySet();
        }
        return  duplicateEmails;
    }

    @PostMapping("/check-unique-emails-for-buyer/{email}")
    public Set<Long> checkUniqueEmailForHolders(@PathVariable("eventurl") String eventUrl, @PathVariable("email") String buyerEmail) {
        Event event = this.roEventService.getEventByURL(eventUrl);
        if(event != null){
            Ticketing ticketing = ticketingService.findByEvent(event);
            if(ticketing != null && ticketing.isUniqueTicketBuyerEmail()){
                return ticketingDisplayService.checkUniqueEmailForBuyer(event, buyerEmail);
            }
        }
        return Collections.emptySet();
    }

    @GetMapping("/get-purchased-ticketing-types")
    public  Set<Long> getPurchasedTicketingTypesByUser(@PathVariable("eventurl")  String eventUrl,
                                                 @RequestParam(required = false,defaultValue = "0") Long recurringEventId,Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventUrl);
        User user = this.userUtils.getUser(auth);
        return  eventTicketsService.getPurchasedTicketingTypesByUser(event,user,recurringEventId);
    }

    /* This API is created to retrieve the confirmation page associated with the order for rendering to the user. */
    @GetMapping("/get-confirmation-page-by-order/{orderId}")
    public CustomTemplatesDto getConfirmationPageByOrder(@PathVariable(name = "orderId") Long orderId,
                                                         @Parameter(description = "Event url") @PathVariable(required = false, name = "eventurl") String eventUrl, Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventUrl);
        return confirmationPagesService.getConfirmationPageByOrder(event, orderId);
    }

    @GetMapping("/get-confirmation-page-by-registration-request/{registrationRequestType}/{registrationRequestId}")
    public CustomTemplatesDto getConfirmationPageByRegistrationRequest(@PathVariable(name = "registrationRequestType") RegistrationRequestType registrationRequestType,
                                                                       @PathVariable(name = "registrationRequestId") Long registrationRequestId,
                                                                       @PathVariable(name = "eventurl") String eventUrl) {
        Event event = this.roEventService.getEventByURL(eventUrl);
        return confirmationPagesService.getConfirmationPageByRegistrationRequest(event, registrationRequestId, registrationRequestType);
    }

    @GetMapping(path = { "/ticketing/waitList/{waitListIds}" })
    public boolean isWaitListCheckoutLinkExpired(@PathVariable final String eventurl, @PathVariable String waitListIds) {
        Event event = this.roEventService.getEventByURL(eventurl);
        return ticketingCheckInService.isWaitListCheckoutLinkExpired(event, SecurityUtils.decode(waitListIds));
    }

    @GetMapping(path = { "/ticketing/ticket-types/remaining-count" })
    public List<TicketTypesRemainingCountDto> getTicketTypesRemainingCount(@PathVariable final String eventurl,
																		   @RequestParam(required = false, value = "recurringEventId", defaultValue = "0") Long recurringEventId,
																		   @RequestParam(required = false, value = "waitListIds", defaultValue = "") String waitListIds) {
        Event event = this.roEventService.getEventByURL(eventurl);
        return ticketingDisplayService.getTicketTypesRemainingCount(event, recurringEventId, (StringUtils.isNotBlank(waitListIds))?SecurityUtils.decode(waitListIds): null);
    }


    @GetMapping(path = { "/ticketing-type/{ticketingTypeIds}/all-purchased-tickets-by-user"})
    public List<Map<Long, List<TicketPurchasedDetailsDto>>> getAllPurchasedTicketsByUser(@PathVariable final String eventurl, @PathVariable String ticketingTypeIds, @RequestParam(value = "recurringEventId", required = false) Long recurringEventId, @RequestParam(value = "email", required = false) String email, Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventurl);
        User user;
        if(!StringUtils.isBlank(email)) {
            user = roUserService.findByEmail(email);
        }else {
            user = this.userUtils.getUser(auth);
        }
        return ticketingDisplayService.getAllPurchasedTicketsByUser(event,user,ticketingTypeIds,recurringEventId);
    }


    @PostMapping("/ticketing/order/{orderId}/remove-unavailable-tickets")
    public ResponseDto removeUnavailableTickets(@PathVariable("eventurl") String eventUrl, @RequestBody List<Long> unavailableTickets, @PathVariable(value = "orderId") Long orderId, Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventUrl);
        User user = this.userUtils.getUser(auth);
        if(!unavailableTickets.isEmpty()) {
             ticketingPurchaseService.removeUnavailableTicketsFromOrder(orderId,event,unavailableTickets, user);
        }
        return new ResponseDto(Constants.SUCCESS, Constants.SUCCESS);
    }

    @GetMapping(path = { "/sso-property-values/{attributeFormType}" })
    public List<SSOFieldMappingDTO> getSsoFieldValues(@PathVariable("eventurl") String eventurl,
                                                      @PathVariable("attributeFormType") AttributeFormType attributeFormType,
                                                      Authentication auth) {
        Event event = this.roEventService.getEventByURL(eventurl);
        User user = this.userUtils.getUser(auth);
        return ssoFieldMappingService.getSSOFieldValues(event, attributeFormType, user);
    }

    @GetMapping("/ticket-exchange-mapping")
    public List<TicketExchangeMappingRuleDTO> getTicketingMapping(Authentication auth,@PathVariable("eventurl") String eventUrl){
        User user = restAuthValidator.authUser(auth);
        return ticketExchangeRuleService.getTicketExchangeMappingRulesByTicketId(eventUrl);
    }

	@GetMapping(path = { "/display-ticketing/attribute/settings" },consumes=MediaType.ALL_VALUE,produces = APPLICATION_JSON)
	public TicketSettingGetDto getEventAttribute(@PathVariable final String eventurl,
												 @RequestParam(required = false) String accessCode,
												 @RequestParam(required = false,defaultValue = "0") Long recurringEventId,
												 Authentication auth) {

		User user = this.userUtils.getUser(auth);
		if (null != user) {
			Event event = this.roEventService.getEventByURL(eventurl);
			return this.ticketingService.getTicketSettingAttributes(event, recurringEventId);
		} else {
			throw new AuthorizationException(Constants.NOT_AUTHORIZE);
		}

	}

    @PostMapping(path = "/tickets/payment/existing-order/{orderId}")
    public ResponseDto purchaseTicketInExistingOrder(@PathVariable String eventurl, @PathVariable Long orderId,
                                      @RequestBody @Validated TicketBookingDto ticketBookingDto, Authentication auth,
                                      @RequestParam(required = false, value = "isInternationalPayment", defaultValue = "false") Boolean isInternationalPayment,
                                      HttpServletRequest httpRequest) {
        Event event = roEventService.getEventByURL(eventurl);
        User user = this.userUtils.getUser(auth);
        if (user == null){
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
        boolean isAdmin = restAuthValidator.isAdminStaffOrSuperAdminOfEvent(event, user);
        String purchaseTicketInExistingOrder = "purchaseTicketInExistingOrder method";
        try {
            ticketingPurchaseService.purchaseTicketInExistingOrder(event, ticketBookingDto, ticketBookingDto.getOrderTicketings(), orderId, user,
                     null, false, isInternationalPayment, isAdmin);
            tokenStoreUtils.updateUser(auth, user, httpRequest.getHeader(Authorization));

            if(!isAdmin) {
                // Send ticket purchase event to kinesis
                ticketingPurchaseService.sendTicketPurchaseDataToKinesis(event, user, orderId);
            }

            String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            return new ResponseDto(Constants.SUCCESS, resourceBundle.getString(languageMap.get(Constants.SUCCESS)));

        }catch (StripeException e){//NOSONAR
            log.info(purchaseTicketInExistingOrder,e);
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        }catch (ApiException e) {//NOSONAR
            log.info(purchaseTicketInExistingOrder, e);
            throw new NotAcceptableException(e);
        } catch (DateTimeParseException e) {
            log.info(purchaseTicketInExistingOrder,e);
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_DATE);
        } catch (Exception e) {//NOSONAR
            log.info(purchaseTicketInExistingOrder,e);
            throw e;
        }
    }

    @PutMapping(path = "/tickets/old-order/{orderId}/coupon")
    public TicketingAppliedCouponDto applyCouponInExistingOrder(@PathVariable String eventurl,
                                                                @RequestBody TicketCouponCheckDto ticketCouponCheckDto,
                                                                @PathVariable Long orderId,
                                                                @RequestParam(value = "recurringEventId", required = false, defaultValue = "0") Long recurringEventId,
                                                                Authentication auth) {
        Event event = roEventService.getEventByURL(eventurl);
        this.userUtils.validateUser(auth);
        List<TicketingOrderManager> ticketingOrderManagers = ticketingPurchaseService.createTicketingOrderManagers(ticketCouponCheckDto.getOrderTicketings(), event, orderId);
        TicketingAppliedCouponForExistingOrderDto ticketingAppliedCouponForExistingOrderDto = ticketingCouponCodeService.applyCouponCodeForOldOrder(orderId, event, recurringEventId, ticketCouponCheckDto.getHolderEmailDiscountDtos(), ticketingOrderManagers, null);
        return (ticketingAppliedCouponForExistingOrderDto != null) ? ticketingAppliedCouponForExistingOrderDto.getTicketingAppliedCouponDto() : null;
    }

    @GetMapping(path = "/order/{orderId}/card-details")
    public List<StripeCreditCardDto> getCardDetailsByEventTicketId(@PathVariable String eventurl,
                                                                   @PathVariable(name = "orderId") Long orderId,
                                                                   Authentication auth) {
        User user = userUtils.validateUser(auth);
        Event event = roEventService.getEventByURL(eventurl);
        return ticketingService.getCreditCardDetailByEventTicketId(event, user, orderId);

    }

    // validate coupon code for approval tickets (Just validate the code not apply)
    @PostMapping(path = "/approval-tickets/coupon")
    public TicketingAppliedCouponDto applyCouponCodeInApprovalTicket(@PathVariable String eventurl,
                                                 @RequestBody CouponCodeApprovalTicketDto couponCodeApprovalTicketDto,
                                                 Authentication auth) {
        Event event = roEventService.getEventByURL(eventurl);
        User user = this.userUtils.getUser(auth);
        if(user == null && StringUtils.isNotEmpty(couponCodeApprovalTicketDto.getEmail())){
            user = roUserService.findByEmail(couponCodeApprovalTicketDto.getEmail());
            if(user == null){
                user = userService.getOrCreateUser(new UserBasicDto(couponCodeApprovalTicketDto.getFirstName(), couponCodeApprovalTicketDto.getLastName(), couponCodeApprovalTicketDto.getEmail()), event);
            }
        }
        try {
            return ticketingCouponCodeService.applyCouponCodeInApprovalTicket(event, couponCodeApprovalTicketDto, user, null);
        }catch (Exception e) {
            log.info("Exception for applyCouponCodeInApprovalTicket : event id {} exception {}",event.getEventId(), e.getMessage());
            throw e;
        }
    }

}
