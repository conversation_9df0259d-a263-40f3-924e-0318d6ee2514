package com.accelevents.controllers.rest;

import com.accelevents.common.dto.AdminSignupDto;
import com.accelevents.common.dto.RecaptchaRequestDto;
import com.accelevents.common.dto.RedirectUrlDto;
import com.accelevents.common.dto.TwoFactorCodeDto;
import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.BlockUserKey;
import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.HubspotCustomEventType;
import com.accelevents.dto.*;
import com.accelevents.dto.zapier.AppleDto;
import com.accelevents.dto.zapier.LinkedinDto;
import com.accelevents.dto.zapier.MicrosoftDto;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.LoginLink;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.hubspot.ROHubspotContactService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.security.GoogleRecaptchaService;
import com.accelevents.security.tokenstore.TokenStoreService;
import com.accelevents.services.*;
import com.accelevents.utils.Constants;
import com.accelevents.utils.EventUtils;
import com.accelevents.utils.SecurityUtils;
import com.accelevents.utils.UserUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Optional;

import static com.accelevents.enums.UserRole.*;
import static com.accelevents.exceptions.NotFoundException.UserNotFound.USER_NOT_FOUND;
import static com.accelevents.utils.ApiMessages.*;
import static com.accelevents.utils.Constants.*;

@RestController
@RequestMapping("rest/u")
@Tag(name = "Authorization", description = "Authenticate the user")
public class RestAuthorizationController {

    private static final Logger log = LoggerFactory.getLogger(RestUserController.class);

    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private EventService eventService;
    @Autowired
    private ROEventService roEventService;

    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private UserUtils userUtils;
    @Autowired
    private TokenStoreService tokenStoreService;
    @Autowired
    private EventUtils eventUtils;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private AutoLoginService autoLoginService;
    @Autowired
    private BlockUserService blockUserService;

    @Autowired
    private ROHubspotContactService roHubspotContactService;
    @Autowired
    private TwilioPhoneNumberValidateService twilioPhoneNumberValidateService;
    @Autowired AuthValidator authValidator;

    @Autowired
    private IntercomDetailsService intercomDetailsService;

    @Autowired
    private ApiKeyService apiKeyService;

    @Value("${app.profile}")
    private String profile;

    @Value("${recaptcha.enabled}")
    private boolean recaptchaEnabled;

    //@Operation(description = "User login")
    @PostMapping(value = "/login")
    public @ResponseBody
    AccessTokenContainer loginUser(@Parameter(description = LOGIN_MODEL_DESC) @RequestBody @Validated UserLoginDto loginDto) {
        try {
            if (this.tokenStoreService.checkAccountLocked(loginDto.getUsername())) {
                throw new AuthorizationException(ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME,
                        ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME);
            }
            AccessTokenModel accessTokenModel = this.roUserService.getUserDetailForLogin(loginDto);
            accessTokenModel.setDomain(loginDto.getCurrentDomain());
            accessTokenModel.setAppLogin(loginDto.isAppLogin());
            this.tokenStoreService.deleteUserFromInvalidPasswordMap(loginDto.getUsername());
            AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
            if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
                roHubspotContactService.updateHubspotContactLastLoginProperty(accessTokenModel.getUsername(), loginDto.getLoginSource());
                roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), loginDto.getUsername()), accessTokenModel.getEventId(), loginDto.getLoginSource(), HubspotCustomEventType.LOG_IN, loginDto.getAppVersionNumber());
            }
            return accessTokenContainer;
        } catch (AuthorizationException e) {
            if (Constants.PASSWORD_NOT_MATCH.equals(e.getDeveloperMessage())) {
                this.tokenStoreService.incrementInvalidPasswordCount(loginDto.getUsername());
                if (this.tokenStoreService.checkAccountLocked(loginDto.getUsername())) {
                    throw new AuthorizationException(MAX_LIMIT_REACHED, MAX_LIMIT_REACHED);
                }
            }
            throw e;
        }

    }

    //@Operation(description = "User can use to login or sign up without event from organizer page." +
//            "If user already exists, then user will be logged in. Otherwise a new user will be created and will be login in automatically.")
    @PostMapping(path = "/loginSignUp")
    public @ResponseBody
    AccessTokenContainer loginSignUpWithoutEvent(
            @Parameter(description = SKIP_PASS_DESC) @RequestParam(required = false, value = "skipPassword", defaultValue = "false") Boolean skipPassword,
            @Parameter(description = SIGN_UP_MODEL_DESC) @RequestBody @Validated UserSignupDto userSignupDto,
            HttpServletRequest httpRequest) {
        try {
            if (this.tokenStoreService.checkAccountLocked(userSignupDto.getEmail())) {
                throw new AuthorizationException(ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME, ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME);
            }
            AccessTokenModel accessTokenModel = this.userService.getBidderUserDetailForLoginSignupWithoutEvent(
                    userSignupDto, false, skipPassword);
            accessTokenModel.setAppLogin(userSignupDto.isAppLogin());
            accessTokenModel.setRememberMe(Boolean.TRUE);
            this.tokenStoreService.deleteUserFromInvalidPasswordMap(userSignupDto.getEmail());
            return this.tokenStoreService.generateAccessToken(accessTokenModel);
        } catch (ConflictException e) {
            if (ConflictException.UserExceptionConflictMsg.INCORRECT_PASSWORD.getStatusCode().equals(e.getErrorCode())) {
                this.tokenStoreService.incrementInvalidPasswordCount(userSignupDto.getEmail());
                if (this.tokenStoreService.checkAccountLocked(userSignupDto.getEmail())) {
                    throw new AuthorizationException(MAX_LIMIT_REACHED, MAX_LIMIT_REACHED);
                }
            }
            throw e;
        }

    }

    //@Operation(description = "User can use to login or sign up." +
//            "If user already exists, then user will be logged in. Otherwise a new user will be created and will be login in automatically.")
    @PostMapping(path = "/loginSignUp/{eventurl:.+}")
    public @ResponseBody
    AccessTokenContainer loginSignUp(
            @Parameter(description = EVENT_URL_DESC, required = true) @PathVariable("eventurl") String eventUrl,
            @Parameter(description = SKIP_PASS_DESC) @RequestParam(required = false, value = "skipPassword", defaultValue = "false") Boolean skipPassword,
            @Parameter(description = SIGN_UP_MODEL_DESC) @RequestBody @Validated UserSignupDto userSignupDto,
            HttpServletRequest httpRequest) {
        try {
            if (this.tokenStoreService.checkAccountLocked(userSignupDto.getEmail())) {
                throw new AuthorizationException(ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME, ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME);
            }
            AccessTokenModel accessTokenModel = this.userService.getBidderUserDetailForLoginSignup(
                    userSignupDto, eventUrl, false, EventUtils.getIP(httpRequest), skipPassword);
            accessTokenModel.setAppLogin(userSignupDto.isAppLogin());
            accessTokenModel.setRememberMe(Boolean.TRUE);
            this.tokenStoreService.deleteUserFromInvalidPasswordMap(userSignupDto.getEmail());
            return this.tokenStoreService.generateAccessToken(accessTokenModel);
        } catch (ConflictException e) {
            if (ConflictException.UserExceptionConflictMsg.INCORRECT_PASSWORD.getStatusCode().equals(e.getErrorCode())) {
                this.tokenStoreService.incrementInvalidPasswordCount(userSignupDto.getEmail());
                if (this.tokenStoreService.checkAccountLocked(userSignupDto.getEmail())) {
                    throw new AuthorizationException(MAX_LIMIT_REACHED, MAX_LIMIT_REACHED);
                }
            }
            throw e;
        }

    }

    //@Operation(description = "User can signup as Admin.")
    @PostMapping(value = "/signup/admin")
    public @ResponseBody
    AccessTokenContainer signUpAdminUser(@Parameter(description = ADMIN_SIGN_UP_MODEL_DESC) @RequestBody @Validated AdminSignupDto signupDto) {
        User user = apiKeyService.retrieveApiUserByAPIKey(signupDto.getApiKey());
        log.info("admin sign-up request received for email: {}, isAppLogin: {}, user: {}", signupDto.getEmail(), signupDto.isAppLogin(), null!=user ? user.getEmail() : "");
        if (!signupDto.isAppLogin() && null == user) {
            if(recaptchaEnabled) {
                boolean isValidCaptcha = GoogleRecaptchaService.isValid(signupDto.getReCaptchaToken(), signupDto.getSiteKey(), profile);
                if (!isValidCaptcha) {
                    log.info("In valid captcha ----> = {}", signupDto.getEmail());
                    throw new AuthorizationException(NOT_AUTHORIZE);
                }
            }
        }
        AccessTokenModel accessTokenModel = this.userService.signUpAdminUserAndReturnAccessTokenModel(signupDto, false);
        intercomDetailsService.createIntercomUser(accessTokenModel.getUser());
        accessTokenModel.setAppLogin(signupDto.isAppLogin());
        return this.tokenStoreService
                .generateAccessToken(accessTokenModel);
    }

    //@Operation(description = "User can login as white Label user.")
    @PostMapping(value = "/signup/whiteLabel/{whiteLabelURL}")
    public @ResponseBody
    AccessTokenContainer signUpWhiteLabelEventAdmin(
            @Parameter(description = ADMIN_SIGN_UP_MODEL_DESC) @RequestBody @Validated AdminSignupDto signupDto,
            @Parameter(description = WHITE_LABEL_URL_DESC, required = true) @PathVariable String whiteLabelURL) {
        if (!signupDto.isAppLogin()) {
            if(recaptchaEnabled) {
                boolean isValidCaptcha = GoogleRecaptchaService.isValid(signupDto.getReCaptchaToken(), signupDto.getSiteKey(), profile);
                if (!isValidCaptcha) {
                    log.info("In valid captcha ----> = {}", signupDto.getEmail());
                    throw new AuthorizationException(NOT_AUTHORIZE);
                }
            }
        }
        AccessTokenModel accessTokenModel = this.userService.signUpWLEventAdminUserAndReturnAccessTokenModel(signupDto, whiteLabelURL);
        intercomDetailsService.createIntercomUser(accessTokenModel.getUser());
        accessTokenModel.setAppLogin(signupDto.isAppLogin());
        AccessTokenContainer container = tokenStoreService.generateAccessToken(accessTokenModel);
        container.setUserRole(StaffRole.admin.toString());
        container.setWhiteLabelURL(whiteLabelURL);
        return container;
    }

    //@Operation(description = "Create new event for the existing user.")
    @PostMapping(value = "/signup/createNewEvent")
    public @ResponseBody
    AccessTokenContainer signUpAdminUserCreateNewEvent(
            @Parameter(description = ADMIN_SIGN_UP_MODEL_DESC) @RequestBody @Validated AdminSignupDto signupDto) {
        return this.tokenStoreService.generateAccessToken(this.userService.createNewEventForAdmin(signupDto, null));
    }

    //@Operation(description = "Create a new event for the existing user")
    @PostMapping(value = "/wl-signup/createNewEvent/{whiteLabelURL}")
    public @ResponseBody
    AccessTokenContainer signupWLEventAdminUserCreateNewEvent(
            @Parameter(description = WHITE_LABEL_ADMIN_SIGN_UP_DESC) @RequestBody @Validated AdminSignupDto signupDto,
            @Parameter(description = WHITE_LABEL_URL_DESC, required = true) @PathVariable String whiteLabelURL) {
        return this.tokenStoreService
                .generateAccessToken(this.userService.createNewEventForAdmin(signupDto, whiteLabelURL));
    }

    //@Operation(description = "Logout user from the system.")
    @PostMapping(value = "/logout")
    public @ResponseBody
    ResponseDto logoutUser(Authentication auth, HttpServletRequest httpRequest) {
        User user = this.userUtils.getUser(auth);
        if (user != null) {
            this.tokenStoreService.removeToken(httpRequest.getHeader(Authorization));
        }
        return new ResponseDto(SUCCESS, USER_LOG_OUT);
    }

    //@Operation(description = "Returns Access token from user key.")
    @PostMapping(value = "/loginUserByUserKey/{userKey}")
    public @ResponseBody
    AccessTokenContainer loginUserByUserKey(@Parameter(description = USER_KEY_DESC) @PathVariable String userKey, @RequestParam(required = false) String eventUrl,
                                            @RequestParam(required = false) String redirectUrl,
                                            @RequestParam(required = false) String wayToLogin) {


        if (StringUtils.isNotEmpty(userKey)) {

                if (StringUtils.isNotBlank(wayToLogin) && !(LoginLink.MAGIC_LINK.name().equals(wayToLogin) || LoginLink.VERIFY_EMAIL.name().equals(wayToLogin))) {
                throw new AuthorizationException(NOT_VALID_MAGIC_LINK);
            }

            if (isBlockKey(userKey)) {
                throw new AuthorizationException(MESSAGE_FOR_BLOCKKEY_USER, MESSAGE_FOR_BLOCKKEY_USER);
            }
            Optional<User> userOptional = getUser(userKey, wayToLogin);

            if (userOptional.isPresent()) {
                User user = userOptional.get();
                AccessTokenModel accessTokenModel = this.roUserService.getUserDetailForSMS(user, eventUrl);
                if (StringUtils.isNotBlank(eventUrl)) {
                    Event event = roEventService.getEventByURL(eventUrl);
                    user.setMostRecentEventId(event.getEventId());
                    userService.save(user);
                    accessTokenModel.setEventId(event.getEventId());
                }

                accessTokenModel.setRememberMe(Boolean.TRUE);
                AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
                if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
                    roHubspotContactService.updateHubspotContactLastLoginProperty(user.getEmail(), WEB_APP);
                    roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), user.getEmail()), accessTokenModel.getEventId(), WEB_APP, HubspotCustomEventType.LOG_IN, "");
                }
                if (StringUtils.isNotEmpty(redirectUrl)) {
                    log.info("Using redirect URL from request parameter while generating token from userKey: {}", redirectUrl);
                    accessTokenContainer.setRedirectUrl(redirectUrl);
                }
                return accessTokenContainer;
            } else {
                throw new AuthorizationException(NOT_AUTHORIZE);
            }
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping(value = "/loginUserByUserKey/{userKey}/event/{eventId}")
    public @ResponseBody
    AccessTokenContainer loginUserByUserKeyWithEventId(@Parameter(description = USER_KEY_DESC) @PathVariable String userKey, @PathVariable Long eventId,
                                                       @RequestParam(required = false) String wayToLogin) {

        if (StringUtils.isNotEmpty(userKey)) {
            Event event = roEventService.findEventByEventId(eventId);
            if (event != null) {
                Ticketing ticketing = ticketingService.findByEvent(event);
                if (ticketing != null) {
                    Date eventEndDate = ticketing.getEventEndDate();
                    int postAccessMinutes = ticketing.getPostEventAccessMinutes();
                    Instant instantEndDate = eventEndDate.toInstant();
                    Instant modifiedInstantEndDate = instantEndDate.plus(postAccessMinutes, ChronoUnit.MINUTES);
                    Date endDateModified = Date.from(modifiedInstantEndDate);

                    if (endDateModified.before(new Date())) {
                        throw new NotAcceptableException(NotAcceptableException.EventExceptionMsg.MAGIC_LINK_EVENT_ENDED);
                    }
                }
        }else {
                throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
            }

        if (StringUtils.isNotBlank(wayToLogin) && !(LoginLink.MAGIC_LINK.name().equals(wayToLogin) || LoginLink.VERIFY_EMAIL.name().equals(wayToLogin))) {
            throw new AuthorizationException(NOT_VALID_MAGIC_LINK);
        }

        if (isBlockKey(userKey)) {
            throw new AuthorizationException(MESSAGE_FOR_BLOCKKEY_USER, MESSAGE_FOR_BLOCKKEY_USER);
        }


        Optional<User> userOptional = userService.getUserByEventMagicLink(userKey, wayToLogin);

        if (userOptional.isPresent()) {
            User user = userOptional.get();
            AccessTokenModel accessTokenModel = this.roUserService.getUserDetailForSMS(user, event.getEventURL());
            accessTokenModel.setMagicLinkEventId(eventId);
            accessTokenModel.setRememberMe(Boolean.TRUE);
            accessTokenModel.setEventId(eventId);
            user.setMostRecentEventId(eventId);
            userService.save(user);
            AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
            if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
                roHubspotContactService.updateHubspotContactLastLoginProperty(user.getEmail(), WEB_APP);
                roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), user.getEmail()), accessTokenModel.getEventId(), WEB_APP, HubspotCustomEventType.LOG_IN, "");
            }
            return accessTokenContainer;
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @PostMapping("/is-user-loggedin-as-event-admin/user-key/{userKey}")
    public boolean isUserLoggedInAsAnAdmin(@Parameter(description = USER_KEY_DESC) @PathVariable String userKey,@RequestParam Long eventId
            ,Authentication auth) {

        User user = userUtils.getUser(auth);
        return userService.isUserLoggedInAsAdminForEvent(userKey, user, eventId);

    }


    //TODO: moved this scheduler API to /scheduler/remove-expired-user-login-tokens remove this method
    @PostMapping(value = "/loginUserByUserKey/removeTokens/schedule")
    public @ResponseBody
    ResponseDto loginUserKeyRemoveScheduler(){
        autoLoginService.removeExpiredLoginUserKeys();
        return new ResponseDto(SUCCESS,SUCCESS);
    }

    public boolean isBlockKey(String userKey) {
        BlockUserKey blockUserKeys = blockUserService.getEncodeUserId(userKey);
        return blockUserKeys != null && blockUserKeys.getUserkey() != null;
    }

    public Optional<User> getUser(String userKey, String wayToLogin) {
        Optional<User> userOptional;
        if (StringUtils.isNotBlank(wayToLogin)) {
            userOptional = autoLoginService.findAndUpdateAutoLoginByToken(userKey, wayToLogin);
        } else {
            userOptional = roUserService.getUserById(SecurityUtils.decodeUserid(userKey));
        }
        return userOptional;
    }

//    //@Operation(description = "User Login/Sign up using facebook", response = AccessTokenContainer.class)
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully token create"),
//            //@ApiResponse(code = 404, message = "User not found")})
    @PostMapping(path = "/login/facebook/{eventurl:.+}")
    public @ResponseBody
    AccessTokenContainer facebookLogin(@Parameter(description = EVENT_URL_DESC, required = true) @PathVariable String eventurl,
                                       @Parameter(description = FACEBOOK_DTO_DESC) @RequestBody @Validated FacebookDto facebook, HttpServletRequest request) {
        log.info("Trying to facebook sign in a user");
        AccessTokenModel accessTokenModel = this.userService.addUserFacebook(facebook, eventurl, EventUtils.getIP(request));
        AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
        if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(accessTokenModel.getUsername(), facebook.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), accessTokenModel.getUsername()), accessTokenModel.getEventId(), facebook.getLoginSource(), HubspotCustomEventType.LOG_IN, facebook.getAppVersionNumber());
        }
        return accessTokenContainer;
    }

    //@Operation(description = "Admin user Login/Sign up using facebook.")
    @PostMapping(path = "/login/facebook/admin")
    public @ResponseBody
    SocialLoginAccessTokenContainer facebookLogin(@Parameter(description = FACEBOOK_DTO_DESC) @RequestBody FacebookDto facebookDto) {
        log.info("Trying to facebook sign in a user");
        SocialLoginAccessTokenModel socialLoginAccessTokenModel = this.userService.addAdminUserToFacebook(facebookDto.getAccessToken());
        SocialLoginAccessTokenContainer socialLoginAccessTokenContainer = new SocialLoginAccessTokenContainer(this.tokenStoreService.generateAccessToken(socialLoginAccessTokenModel.getAccessTokenModel()), socialLoginAccessTokenModel.isNewUserRegistered());
        if (null != socialLoginAccessTokenContainer.getUserRole() && socialLoginAccessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(socialLoginAccessTokenModel.getAccessTokenModel().getUsername(), facebookDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(socialLoginAccessTokenContainer.getUserId(), socialLoginAccessTokenModel.getAccessTokenModel().getUsername()),
                    socialLoginAccessTokenModel.getAccessTokenModel().getEventId(), facebookDto.getLoginSource(), HubspotCustomEventType.LOG_IN, facebookDto.getAppVersionNumber());
        }
        return socialLoginAccessTokenContainer;
    }

    //@Operation(description = "User Login/Sign up using google")
    @PostMapping(path = "/login/google/{eventurl:.+}")
    public @ResponseBody
    AccessTokenContainer googleLogin(@Parameter(description = EVENT_URL_DESC, required = true) @PathVariable String eventurl,
                                     @Parameter(description = GOOGLE_DTO_DESC) @RequestBody @Validated GoogleDto google, HttpServletRequest request) {
        log.info("Trying to google sign in a user");
        AccessTokenModel accessTokenModel = this.userService.addUserGoogle(google, eventurl, EventUtils.getIP(request));
        AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
        if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(accessTokenModel.getUsername(), google.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), accessTokenModel.getUsername()), accessTokenModel.getEventId(), google.getLoginSource(), HubspotCustomEventType.LOG_IN, google.getAppVersionNumber());
        }
        return accessTokenContainer;
    }

    //@Operation(description = "Admin user Login/Sign up using google.")
    @PostMapping(path = "/login/google/admin")
    public @ResponseBody
    SocialLoginAccessTokenContainer googleLogin(@Parameter(description = GOOGLE_DTO_DESC) @RequestBody GoogleDto googleDto) {
        log.info("Trying to google sign in a user");
        SocialLoginAccessTokenModel socialLoginAccessTokenModel = this.userService.addAdminUserToGoogle(googleDto.getAccessToken());
        AccessTokenModel accessTokenModel = socialLoginAccessTokenModel.getAccessTokenModel();
        accessTokenModel.setFromAELoginPage(googleDto.isFromAELoginPage());
        socialLoginAccessTokenModel.setAccessTokenModel(accessTokenModel);
        SocialLoginAccessTokenContainer socialLoginAccessTokenContainer = new SocialLoginAccessTokenContainer(this.tokenStoreService.generateAccessToken(socialLoginAccessTokenModel.getAccessTokenModel()), socialLoginAccessTokenModel.isNewUserRegistered());
        if (null != socialLoginAccessTokenContainer.getUserRole() && socialLoginAccessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(socialLoginAccessTokenModel.getAccessTokenModel().getUsername(), googleDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(socialLoginAccessTokenContainer.getUserId(), socialLoginAccessTokenModel.getAccessTokenModel().getUsername()), socialLoginAccessTokenModel.getAccessTokenModel().getEventId(), googleDto.getLoginSource(), HubspotCustomEventType.LOG_IN, googleDto.getAppVersionNumber());
        }
        return socialLoginAccessTokenContainer;
    }

    //@Operation(description = "Admin user Login/Sign up using Linkedin.")
    @PostMapping(path = "/login/Linkedin/admin")
    public @ResponseBody
    SocialLoginAccessTokenContainer linkedinLogin(@Parameter(description = LINKEDIN_DTO_DESC) @RequestBody LinkedinDto linkedinDto) {
        log.info("Trying to Linkedin sign in a user");
        SocialLoginAccessTokenModel socialLoginAccessTokenModel = this.userService.addAdminUserToLinkedin(linkedinDto);
        SocialLoginAccessTokenContainer socialLoginAccessTokenContainer = new SocialLoginAccessTokenContainer(this.tokenStoreService.generateAccessToken(socialLoginAccessTokenModel.getAccessTokenModel()), socialLoginAccessTokenModel.isNewUserRegistered());
        if (null != socialLoginAccessTokenContainer.getUserRole() && socialLoginAccessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(socialLoginAccessTokenModel.getAccessTokenModel().getUsername(), linkedinDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(socialLoginAccessTokenContainer.getUserId(),
                    socialLoginAccessTokenModel.getAccessTokenModel().getUsername()), socialLoginAccessTokenModel.getAccessTokenModel().getEventId(), linkedinDto.getLoginSource(), HubspotCustomEventType.LOG_IN, linkedinDto.getAppVersionNumber());
        }
        return socialLoginAccessTokenContainer;
    }


//    //@Operation(description = "User Login/Sign up using Linkedin", response = AccessTokenContainer.class)
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully token create"),
//            //@ApiResponse(code = 404, message = "User not found")})
    @PostMapping(path = "/login/Linkedin/{eventurl:.+}")
    public @ResponseBody
    AccessTokenContainer LinkedinLogin(@Parameter(description = EVENT_URL_DESC, required = true) @PathVariable String eventurl,
                                       @Parameter(description = LINKEDIN_DTO_DESC) @RequestBody @Validated LinkedinDto linkedinDto, HttpServletRequest request) {
        log.info("Trying to Linkedin sign in a user");
        AccessTokenModel accessTokenModel = this.userService.addUserLinkedin(linkedinDto, eventurl, EventUtils.getIP(request));
        AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
        if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(accessTokenModel.getUsername(), linkedinDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), accessTokenModel.getUsername()), accessTokenModel.getEventId(), linkedinDto.getLoginSource(), HubspotCustomEventType.LOG_IN, linkedinDto.getAppVersionNumber());
        }
        return accessTokenContainer;
    }


    //@Operation(description = "Admin user Login/Sign up using Apple.")
    @PostMapping(path = "/login/Apple/admin")
    public @ResponseBody
    SocialLoginAccessTokenContainer appleLogin(@Parameter(description = APPLE_DTO_DESC) @RequestBody AppleDto appleDto) {
        log.info("Trying to Apple sign in a user");
        SocialLoginAccessTokenModel socialLoginAccessTokenModel = this.userService.addAdminUserToApple(appleDto);
        SocialLoginAccessTokenContainer socialLoginAccessTokenContainer = new SocialLoginAccessTokenContainer(this.tokenStoreService.generateAccessToken(socialLoginAccessTokenModel.getAccessTokenModel()), socialLoginAccessTokenModel.isNewUserRegistered());
        if (null != socialLoginAccessTokenContainer.getUserRole() && socialLoginAccessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(socialLoginAccessTokenModel.getAccessTokenModel().getUsername(), appleDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(socialLoginAccessTokenContainer.getUserId(), socialLoginAccessTokenModel.getAccessTokenModel().getUsername()), socialLoginAccessTokenModel.getAccessTokenModel().getEventId(), appleDto.getLoginSource(), HubspotCustomEventType.LOG_IN, appleDto.getAppVersionNumber());
        }
        return socialLoginAccessTokenContainer;
    }


    //@Operation(description = "User Login/Sign up using Apple")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully token create"),
//            //@ApiResponse(code = 404, message = "User not found")})
    @PostMapping(path = {"/login/Apple","/login/Apple/{eventurl:.+}"})
    public @ResponseBody
    AccessTokenContainer appleLogin(@Parameter(description = EVENT_URL_DESC) @PathVariable(required = false) String eventurl,
                                    @Parameter(description = APPLE_DTO_DESC) @RequestBody @Validated AppleDto appleDto, HttpServletRequest request) {
        log.info("Trying to Apple sign in a user");
        AccessTokenModel accessTokenModel = this.userService.addUserToApple(appleDto, eventurl);
        AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
        if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(accessTokenModel.getUsername(), appleDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), accessTokenModel.getUsername()), accessTokenModel.getEventId(), appleDto.getLoginSource(), HubspotCustomEventType.LOG_IN, appleDto.getAppVersionNumber());
        }
        return accessTokenContainer;
    }

    //@Operation(description = "Login using token")
    @PostMapping(path = "/login/token/{token:.+}")
    public @ResponseBody
    AccessTokenModelUserDetail tokenBasedLogin(@Parameter(description = TOKEN_DESC, required = true) @PathVariable String token) {
        log.info("Trying to tokenBased Login in a user");
        AccessTokenModel accessTokenModel = this.roUserService.tokenLogin(token);
        accessTokenModel.setRememberMe(Boolean.TRUE);
        AccessTokenContainer container = this.tokenStoreService.generateAccessToken(accessTokenModel);
        AccessTokenModelUserDetail accessTokenModelUserDetail = new AccessTokenModelUserDetail();
        accessTokenModelUserDetail.setAccessToken(container);
        accessTokenModelUserDetail.setUserInfoDto(this.roUserService.extractUserInfo(accessTokenModel.getUser()));
        return accessTokenModelUserDetail;
    }

    //@Operation(description = "Login by white label user.")
    @PostMapping(value = "/login/whiteLabel/{whiteLabelURL}")
    public @ResponseBody
    AccessTokenContainer whiteLabelUserSignIn(@Parameter(description = LOGIN_MODEL_DESC) @RequestBody @Validated UserLoginDto loginDto,
                                              @Parameter(description = SKIP_PASS_DESC) @RequestParam(required = false, value = "skipPassword", defaultValue = "false") Boolean skipPassword,
                                              @Parameter(description = SSO_LOGIN) @RequestParam(required = false, value = "ssoLogin", defaultValue = "false") Boolean ssoLogin,
                                              @Parameter(description = WHITE_LABEL_URL_DESC, required = true) @PathVariable String whiteLabelURL) {
        log.info("Trying to sign in a user {} | whiteLabelURL {} | skipPassword {} | ssoLogin {}", loginDto.getUsername(),whiteLabelURL,skipPassword, ssoLogin);
        try {
            if (this.tokenStoreService.checkAccountLocked(loginDto.getUsername())) {
                throw new AuthorizationException(ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME,
                        ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME);
            }
            AccessTokenModel accessTokenModel = this.roUserService.getWhiteLabelUserDetailForLogin(loginDto, whiteLabelURL, skipPassword, ssoLogin);
            AccessTokenContainer container = this.tokenStoreService.generateAccessToken(accessTokenModel);
            RedirectUrlDto redirectUrlDto = userUtils.getWhiteLabelRedirectUrl(accessTokenModel, whiteLabelURL);
            container.setRedirectUrl(redirectUrlDto.getRedirectUrl());
            container.setUserRole(redirectUrlDto.getUserRole());
            container.setWhiteLabelURL(redirectUrlDto.getWhiteLabelURL());
            container.setTwoFactorRequired(redirectUrlDto.isTwoFactorRequired());
            this.tokenStoreService.deleteUserFromInvalidPasswordMap(loginDto.getUsername());
            if (null != container.getUserRole() && container.getUserRole().contains(ROLE_ADMIN.name()) || container.getUserRole().contains(ROLE_WHITELABELADMIN.name()) || container.getUserRole().contains(ROLE_EVENT_COORDINATOR.name())) {
                roHubspotContactService.updateHubspotContactLastLoginProperty(accessTokenModel.getUsername(), loginDto.getLoginSource());
                roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(container.getUserId(), accessTokenModel.getUsername()), accessTokenModel.getEventId(), loginDto.getLoginSource(), HubspotCustomEventType.LOG_IN, loginDto.getAppVersionNumber());
            }
            return container;
        } catch (AuthorizationException e) {
            if (Constants.PASSWORD_NOT_MATCH.equals(e.getDeveloperMessage())) {
                this.tokenStoreService.incrementInvalidPasswordCount(loginDto.getUsername());
                if (this.tokenStoreService.checkAccountLocked(loginDto.getUsername())) {
                    throw new AuthorizationException(MAX_LIMIT_REACHED, MAX_LIMIT_REACHED);
                }
            }
            throw e;
        }
    }


    //@Operation(description = "Return a new token with the help of old token")
    @PostMapping(path = {"/refreshtoken/{acesstoken:.+}"})
    public RefeshTokenContainer resendEmail(@Parameter(description = TOKEN_DESC, required = true) @PathVariable String acesstoken) {
        return this.tokenStoreService.generateRefreshToken(acesstoken);
    }


    //@Operation(description = "Check phone number is valid or not")
    @GetMapping("/validate/phoneNumber/{phoneNumber}")
    public boolean validatePhoneNumber(@PathVariable String phoneNumber) {
        return twilioPhoneNumberValidateService.validatePhoneNumber(phoneNumber);
    }

    //@Operation(description = "Return a new token with the help of old token")
    @Deprecated // Not called from FE side
    @PostMapping(path = {"/generateMagicLink/eventUrl/{eventUrl}/email/{email}"})
    public String generateMagicLink(Authentication auth, @Parameter(description = EVENT_URL_DESC, required = true) @PathVariable String eventUrl, @Parameter(description = EMAIL_DESC, required = true) @PathVariable String email) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        Optional<User> optionalUser = roUserService.getUserByEmail(email);
        if (optionalUser.isPresent()) {
            return autoLoginService.getMagicLinkForPortal(optionalUser.get(), userEvent.getEvent());
        } else {
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
    }

    //@Operation(description = "Admin user Login/Sign up using Microsoft.")
    @PostMapping(path = "/login/Microsoft/admin")
    public @ResponseBody
    SocialLoginAccessTokenContainer microsoftLogin(@Parameter(description = LINKEDIN_DTO_DESC) @RequestBody MicrosoftDto microsoftDto) {
        log.info("Trying to Microsoft sign in a user, data {}", microsoftDto);
        SocialLoginAccessTokenModel socialLoginAccessTokenModel = this.userService.addAdminUserToMicrosoft(microsoftDto);
        SocialLoginAccessTokenContainer socialLoginAccessTokenContainer = new SocialLoginAccessTokenContainer(this.tokenStoreService.generateAccessToken(socialLoginAccessTokenModel.getAccessTokenModel()), socialLoginAccessTokenModel.isNewUserRegistered());
        if (null != socialLoginAccessTokenContainer.getUserRole() && socialLoginAccessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(socialLoginAccessTokenModel.getAccessTokenModel().getUsername(), microsoftDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(socialLoginAccessTokenContainer.getUserId(),
                    socialLoginAccessTokenModel.getAccessTokenModel().getUsername()), socialLoginAccessTokenModel.getAccessTokenModel().getEventId(), microsoftDto.getLoginSource(), HubspotCustomEventType.LOG_IN, microsoftDto.getAppVersionNumber());
        }
        if (StringUtils.isNotBlank(microsoftDto.getRedirectURL())) {
            socialLoginAccessTokenContainer.setRedirectUrl(microsoftDto.getRedirectURL());
        }
        return socialLoginAccessTokenContainer;
    }


    //@Operation(description = "User Login/Sign up using Microsoft")
//    ////@ApiResponses(value = {//@ApiResponse(code = 200, message = "Successfully token create"),
//            //@ApiResponse(code = 404, message = "User not found")})
    @PostMapping(path = "/login/Microsoft/{eventurl:.+}")
    public @ResponseBody
    AccessTokenContainer microsoftLogin(@Parameter(description = EVENT_URL_DESC, required = true) @PathVariable String eventurl,
                                    @Parameter(description = APPLE_DTO_DESC) @RequestBody @Validated MicrosoftDto microsoftDto, HttpServletRequest request) {
        log.info("Trying to Microsoft sign in a user");
        AccessTokenModel accessTokenModel = this.userService.addUserMicrosoft(microsoftDto, eventurl,EventUtils.getIP(request));
        AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
        if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
            roHubspotContactService.updateHubspotContactLastLoginProperty(accessTokenModel.getUsername(), microsoftDto.getLoginSource());
            roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), accessTokenModel.getUsername()), accessTokenModel.getEventId(), microsoftDto.getLoginSource(), HubspotCustomEventType.LOG_IN, microsoftDto.getAppVersionNumber());
        }
        return accessTokenContainer;
    }

    //@Operation(description = "Validate two factor code for user")
    @PostMapping(value = "/validate/twoFactorCode")
    public boolean twoFactorCodeValidate(@RequestBody TwoFactorCodeDto twoFactorCodeDto, Authentication auth, HttpServletRequest httpRequest) {
        User user = this.userUtils.getUser(auth);
        if (user != null) {
            boolean result =  userService.validateTwoFactorCode(user, twoFactorCodeDto, httpRequest.getHeader(Authorization));
            tokenStoreService.updateUser2FA(httpRequest.getHeader(Authorization), result);
            return result;
        }else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }
    //@Operation(description = "verify captcha.")
    @PostMapping(value = "/verify/captcha")
    public @ResponseBody
    RecaptchaRequestDto verifyCaptcha( @RequestBody @Validated RecaptchaRequestDto recaptchaRequestDto) {
        return GoogleRecaptchaService.validateRecaptcha(recaptchaRequestDto.getReCaptchaToken(), recaptchaRequestDto.getSiteKey(), profile);
    }


    @PostMapping("/confirm-four-digit-code/platform-level")
    public @ResponseBody
    AccessTokenContainer confirmFourDigitCodeToAccessPlatformLevel(@RequestParam(name = "code") Long code,
                                                                   @RequestParam(name = "email",required = false) String email,
                                                                   Authentication auth) {
        User user = null;
        if (StringUtils.isNotEmpty(email)){
            user = roUserService.findByEmail(email);
        } else {
            user = this.userUtils.getUser(auth);
        }

        if (user != null) {
            userService.confirmFourDigitCodeToAccessPlatformLevel(user, code);
            AccessTokenModel accessTokenModel = this.roUserService.getUserDetailForSMS(user, null);
            accessTokenModel.setRememberMe(Boolean.TRUE);
            AccessTokenContainer accessTokenContainer = this.tokenStoreService.generateAccessToken(accessTokenModel);
            if (null != accessTokenContainer.getUserRole() && accessTokenContainer.getUserRole().contains(ROLE_ADMIN.name())) {
                roHubspotContactService.updateHubspotContactLastLoginProperty(user.getEmail(), WEB_APP);
                roHubspotContactService.asyncAddCustomLogInEventHubspot(new User(accessTokenContainer.getUserId(), user.getEmail()), accessTokenModel.getEventId(), WEB_APP, HubspotCustomEventType.LOG_IN, "");
            }
            return accessTokenContainer;
        } else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }

    }

    @GetMapping("/whitelabel/get-white-label-url-by-host-base-url")
    public String getWhiteLabelUrl(@RequestParam(name = "hostBaseUrl") String hostBaseUrl) {
        return whiteLabelService.getWhiteLabelUrlByHostBaseUrl(hostBaseUrl);
    }

    @PostMapping(value = "/login-user-with-salesforce/{eventUrl}")
    public AccessTokenContainer loginUserWithSalesforce(@Parameter(description = EVENT_URL_DESC, required = true) @PathVariable String eventUrl,
                                                        @RequestParam("code") String code,@RequestParam("code_verifier") String codeVerifier,
                                                        @RequestParam("client_id")String clientId) {
        try {
            AccessTokenModel accessTokenModel = userService.loginUserWithSalesforce(code,codeVerifier,clientId,eventUrl);
            this.tokenStoreService.deleteUserFromInvalidPasswordMap(accessTokenModel.getUsername());
            return this.tokenStoreService.generateAccessToken(accessTokenModel);
        } catch (AuthorizationException e) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }

    }

    @PostMapping(value = "/login-user-with-salesforce-using-token")
    public AccessTokenContainer loginUserWithSalesforceUsingSSOToken(@RequestParam("token") String token) {
        try {
            AccessTokenModel accessTokenModel = userService.loginUserWithSalesforceUsingSSOToken(token);
            this.tokenStoreService.deleteUserFromInvalidPasswordMap(accessTokenModel.getUsername());
            return this.tokenStoreService.generateAccessToken(accessTokenModel);
        } catch (AuthorizationException e) {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

}
