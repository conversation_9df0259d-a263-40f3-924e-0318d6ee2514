package com.accelevents.controllers.rest.session_speaker.host;

import com.accelevents.controllers.rest.exhibitor.AuthValidator;
import com.accelevents.controllers.rest.exhibitor.UserEvent;
import com.accelevents.domain.enums.AttributeType;
import com.accelevents.domain.enums.EngageEmailMergeTagType;
import com.accelevents.dto.ResponseDto;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.services.CustomFormAttributeService;
import com.accelevents.ticketing.dto.CustomAttribute;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.accelevents.utils.Constants.SUCCESS;

@RestController
@RequestMapping({"/rest/host/event/{eventUrl}/custom-attribute"})
@Tag(name = "/rest/host/speaker")
public class CustomFormAttributeController {

    private final CustomFormAttributeService customFormAttributeService;
    private final AuthValidator authValidator;

    public CustomFormAttributeController(CustomFormAttributeService customFormAttributeService, AuthValidator authValidator) {
        this.customFormAttributeService = customFormAttributeService;
        this.authValidator = authValidator;
    }

    @GetMapping("/{attributeType}")
    public List<CustomFormAttributeDTO> getCustomFormAttributes(@RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                             @PathVariable("attributeType") AttributeType attributeType,
                                                             @Parameter(name="eventUrl", description = "Event url") @PathVariable String eventUrl,
                                                             Authentication auth) {

        UserEvent userEvent = authValidator.authHasStaffLevelAccess(auth, eventUrl);
        return customFormAttributeService.getCustomFormAttributes(userEvent.getEvent(), userEvent.getUser(), recurringEventId,attributeType);
    }

    @PostMapping
    public void createCustomFormAttribute(@RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                          @Parameter(name="eventUrl", description = "Event url") @PathVariable String eventUrl,
                                                          @RequestBody @Validated CustomFormCustomAttributeDto customFormCustomAttributeDto,
                                                          Authentication auth) {

        UserEvent userEvent = authValidator.authHasStaffLevelAccess(auth, eventUrl);
        customFormAttributeService.createCustomFormAttribute(userEvent.getEvent(), userEvent.getUser(), recurringEventId, customFormCustomAttributeDto);
    }

    @GetMapping(path = {"/{attributeType}/{attributeId}"})
    public List<CustomAttribute> getCustomAttribute(@PathVariable Long attributeId,
                                                    @PathVariable("attributeType") AttributeType attributeType,
                                                    @Parameter(description = "Event url") @PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return customFormAttributeService.getCustomAttribute(attributeId, userEvent.getEvent(),attributeType);
    }

    @GetMapping(path = {"/{attributeType}/{attributeId}/count"})
    public int getCustomAttributeUsageCount(@PathVariable Long attributeId,
                                            @PathVariable("attributeType") AttributeType attributeType,
                                                    @Parameter(description = "Event url") @PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return customFormAttributeService.getCustomAttributeUsageCount(attributeId, userEvent.getEvent(),attributeType);
    }

    @PutMapping(path = {"/{attributeId}"})
    public ResponseDto updateCustomAttribute(@PathVariable Long attributeId,
                                             @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                             @RequestBody @Validated CustomFormCustomAttributeDto customFormCustomAttributeDto,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        customFormAttributeService.updateCustomAttribute(attributeId, customFormCustomAttributeDto,userEvent.getEvent(), recurringEventId);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @DeleteMapping(path = {"/{attributeId}"})
    public ResponseDto deleteCustomAttribute(@PathVariable("attributeId") Long attributeId,
                                             @RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                             @Parameter(description = "Event url") @PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        customFormAttributeService.deleteCustomFormAttribute(attributeId, recurringEventId, userEvent.getEvent());
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @GetMapping(path = {"/{attributeType}/session-registration-attribute-mapping-list"})
    public List<CustomFormAttributeMappingDto> getSessionRegistrationAttributeMappingList(@RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                                                          @PathVariable("attributeType") AttributeType attributeType,
                                                                                          @Parameter(description = "Event url") @PathVariable String eventUrl, Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return customFormAttributeService.getSessionRegistrationAttributeMappingList(userEvent.getEvent(),recurringEventId,attributeType);
    }

    @GetMapping(path = {"/{engageEmailMergeTagType}/get-all-engage-email-merge-tags"})
    public EngageEmailMergeTagResponseDto getEngageEmailMergeTags(@RequestParam(required = false, defaultValue = "0") Long recurringEventId,
                                                                  @PathVariable("engageEmailMergeTagType") List<EngageEmailMergeTagType> engageEmailMergeTagType,
                                                                  @Parameter(description = "Event url") @PathVariable String eventUrl,
                                                                  Authentication auth) {
        UserEvent userEvent = authValidator.authHostLevelAccess(auth, eventUrl);
        return customFormAttributeService.getContactListMergeTagsDto(userEvent.getEvent(), userEvent.getUser(),recurringEventId,engageEmailMergeTagType);
    }

}
