sessions.sorted.successfully=Sesiones ordenadas correctamente ...
infodesk.update.successfully=Los detalles de Infodesk se actualizaron correctamente.
infodesk.delete.successfully=Los detalles de Infodesk se eliminaron correctamente.
networking.lounge.deleted=Se eliminó la sala de networking.
networking.lounge.updated=Salón de networking actualizado.
networking.lounge.photo.deleted=Se eliminó la foto del salón de networking.
networking.lounge.video.deleted=Se eliminó el video de Networking Lounge.
networking.lounge.leave.successfully=Salir del salón de networking con éxito.
networking.lounge.position.changed=La posición de la sala de networking cambió con éxito ...
exhibitor.staff.saved=Se guardaron los datos del personal del expositor.
exhibitor.staff.deleted=Se eliminaron los datos del personal del expositor.
exhibitor.admin.updated=Administrador de expositores actualizado para el evento.
exhibitor.staff.updated=Personal del expositor actualizado para el evento.
staff.send.invite.msg=Su invitación ha sido reenviada.
product.added.success=Producto agregado exitosamente.
product.updated.success=Producto actualizado con éxito.
product.deleted.success=Producto eliminado correctamente.
exhibitor.product.position.changed.success=La posición del producto del expositor cambió correctamente ...
attendee.deleted=Asistente eliminada
attendee.connected.msg=Asistente conectado
notification.preference.saved=Preferencia de notificación guardada.
notification.preference.updated=Preferencia de notificación actualizada.
notification.preference.deleted=Preferencia de notificación eliminada correctamente.
send.contact.mail=Gracias por escribirnos, ¡nos comunicaremos con usted pronto!
new.bidder.number.message=¡Te has registrado con éxito para el evento! Su número de participante es %d
bidder.registered.message=¡Te has registrado con éxito para el evento!
device.checker.added=Verificador de dispositivos agregado
device.checked.with.device.id=Comprobación de dispositivo realizada en ${DATE}
lead.deleted=Cliente potencial eliminado.
lead.saved=Plomo salvada.
lead.updated.msg=Cliente potencial actualizado para el evento
session.breakout.room.deleted=Se eliminó la sala de reuniones para grupos.
breakout.room.updated.success=BreakoutRoom actualizado correctamente.
exhibitor.fields.tablist.updated=Expositor TabList actualizada con éxito.
success=Éxito

#Error
exhibitor.already.exists=La expositora ya existe

unable.to.create.new.feed=No se puede crear un nuevo feed
unable.to.update.feed=No se pudo actualizar el feed
unable.to.retrieve.feed=No se pueden recuperar los feeds.
unable.to.follow.feed=No se pueden seguir los feeds
unable.to.get.client=Incapaz de conseguir cliente
unable.to.retrieve.feeds.reaction=No se pudo recuperar la reacción de los feeds
activity.stream.not.found=No se encontró el flujo de actividad

info.desk.already.present=El mostrador de información ya está presente.
info.desk.not.found=Mesa de información no encontrada.

session.dates.not.between.event=La hora de inicio y finalización de la sesión debe ser posterior a la hora de inicio del evento y anterior a la hora de finalización del evento.
required.for.ae.studio=Stream Key, Stream Url no puede estar en blanco para el proveedor de aceleraciones

session.not.found=¡Sesión no encontrada!
speaker.not.found=¡Altavoz no encontrado!
tag.or.track.not.found=¡Etiqueta o pista no encontrada!
event.ticket.not.found=¡No se encontró el boleto del evento!
session.speaker.not.found=¡No se encontró el orador de la sesión!

session.is.ended=¡La sesión ha terminado!
default.playback.not.found=No se encontró reproducción predeterminada para la sesión.
event.plan.config.not.found.error.msg=Esta bandera solo es aplicable para eventos virtuales
event.plan.config.not.found.developer.msg=No se encontraron los detalles de configuración del plan de eventos para este evento

attendee.can.not.added=El asistente no tiene ticket o no coincide con el rol de administrador, expositor, orador.
attendee.already.added=La asistente ya ha añadido.
attendee.name.can.not.empty=El nombre del asistente no se puede vaciar.
super.admin.cannot.connect.without.ticket=El superadministrador no puede conectarse sin comprar el boleto del evento
attendee.not.found=Asistente no encontrada
attendee.can.not.connect.to.self=La asistente no puede conectarse a sí misma.
attendee.already.connected=Asistente ya conectada.
connection.request.not.found=Solicitud de conexión no encontrada
connection.request.already.accepted=Solicitud de conexión ya aceptada
connection.request.already.rejected=Solicitud de conexión ya rechazada

permission.denied.register.event=Este no es un usuario registrado
event.ended=Este evento ya no está disponible
not.allow.to.access.virtual.portal=No ha comprado entradas para eventos virtuales. Por favor, contacte con los organizadores para más información.
ticketing.module.not.activated=Active su evento para acceder al portal.
max.free.attendee.reached.for.portal=Se ha canjeado toda la capacidad para inscripciones gratuitas. ¡Puedes comprar un pase para acceder al evento!
max.attendee.allows.limit.reached=Se alcanzó el número máximo de asistentes a este evento. Comuníquese con el organizador del evento.
blocked=El administrador te ha bloqueado. Comuníquese con el administrador del sistema.
user.did.donation.not.purchase.ticket=Parece que olvidó obtener un boleto cuando hizo una donación, compre un boleto en la página del evento o comuníquese con el organizador del evento.
url.not.supported=No puedo crear URL
event.not.found=Evento no encontrado

user.not.allowed.to.register.without.ticket=Usuario no autorizado a registrarse sin ticket
ticketing.type.not.matched=Su boleto no permite el acceso a esta sesión
not.authorized.to.access.session.user.analytics=El administrador con el plan actual no está autorizado para acceder a esta funcionalidad. Actualice su plan para acceder.
register.failed=Aún no te has registrado para el evento. Debe registrarse para el evento antes de poder registrarse para sesiones individuales.
time.access.elapsed.live=El límite de tiempo para acceder a esta sesión ha expirado.
time.access.elapsed.recording=La grabación de esta sesión solo está disponible para los usuarios que la vieron en vivo.
exceed.session.capacity=Se alcanzó la capacidad máxima de registro de usuarios
session.is.not.private=Sorry, This session is not private, Bulk registration is only supported in private sessions.
exceed.workshop.session.capacity=Los 250 espacios de esta reunión ya han sido ocupados. ¿Le gustaría eliminar a alguien de la sesión para poder unirse?

can.not.generate.stream.in.progress=No se puede generar la clave de transmisión, transmisión en vivo en proceso

mux.live.stream.not.available=Transmisión en vivo de Mux no disponible.
mux.live.stream.not.completed=Transmisión en vivo de Mux no completada.
mux.live.stream.status.not.available=El estado de la transmisión en vivo de Mux no está disponible.
mux.live.stream.can.not.create=La transmisión en vivo de Mux no se puede crear.
mux.live.stream.can.not.create.upload.url=La transmisión en vivo de Mux no puede crear una URL de carga.
mux.asset.id.not.available=ID de activo de Mux no disponible.
mux.playback.id.not.available=ID de reproducción de Mux no disponible.
failed.to.enable.mp4.support.for.mux=No se pudo habilitar la compatibilidad con MP4.
mux.duration.not.available=Duración de mux no disponible.
mux.preparing.video.asset=Mux está preparando el recurso de video, ¡espere un tiempo para agregarle subtítulos!
mux.subtitle.file.can.not.be.added=No se puede agregar un archivo de subtítulos mux.
mux.subtitle.file.can.not.be.removed=El archivo de subtítulos Mux no se puede eliminar.
mux.live.stream.not.re.enabled=Debido a un error técnico, la transmisión no se inicia.

notification.preference.already.added=Preferencia de notificación ya agregada.
json.format.is.not.valid=El formato JSON no es válido

you.can.not.delete.networking.lounge.video=Solo puede eliminar el propio video de Networking Lounge.
default.playback.not.found.for.video=No se encontró reproducción predeterminada para video.
networking.lounge.not.found=No se ha encontrado la sala de networking.
you.can.delete.only.own.networking.lounge.photo=Solo puede eliminar la propia foto de la sala de networking.
networking.lounge.with.same.name.already.exist=Ya existe una sala de networking con el mismo nombre.

not.authorize=No autorizar el acceso

session.breakout.room.not.found=No se encontró el chat de la sala de ruptura de sesión
breakout.room.already.exist=Nombre de la sala de descanso ${session_breakout_room_name} ya existe en sesión

expo.category.not.exists=Categoría de expositor no existe

product.not.found=Producto no encontrado

staff.details.not.found=Detalles del personal no encontrados
user.has.already.logged.in=El usuario ya ha iniciado sesión en el sitio web. ¡No se puede cambiar el correo electrónico ahora!
already.staff=Ya has agregado esta miembro del equipo.
email.already.assigned=Esta dirección de correo electrónico ya está asignada. No puede cambiar el personal existente con esta dirección de correo electrónico. Agregue nuevo personal si desea agregar ese correo electrónico como administrador / personal.
expo.staff.role.not.matched=exhibitoradmin y leadretriever son roles válidos para el personal del expositor
max.permitted.team.member.reached=has alcanzado el límite de miembros del equipo permitidos
expo.admin.can.not.remove.expo.admin=Los administradores de expositores no deberían poder eliminar a los administradores de expositores

can.not.generate.lead=No se permite la recuperación de clientes potenciales para este expositorgit status
first.name.size.limit=Solo se permiten 50 caracteres en firstName
last.name.size.limit=Solo se permiten 50 caracteres en lastName
email.name.size.limit=Solo se permiten 75 caracteres en el correo electrónico
already.used.ticket=Ya usaste tu boleto
lead.is.not.belong.to.this.lead.retrieval=No ha agregado este cliente potencial. Puede eliminar solo los clientes potenciales que haya agregado.
lead.not.exists=El plomo no existe

not.allow.to.select.interest.tags=No se permite seleccionar más de 10 etiquetas de interés.
item.not.found=objeto no encontrado
not.parse.number.to.intended.format=No se puede analizar el número en el formato deseado.
raffle.not.active=Active este módulo para comenzar a aceptar boletos.
raffle.is.expire=¡Esta rifa ha terminado!
event.cc.not.enable=El evento CC no está habilitado
raffle.tickets.are.sold.out=¡Los boletos de la rifa están agotados!
limitted.ticket.multi.language=Solo quedan {ticket_max_val} entradas disponibles. Su compra máxima debe ser de {ticket_max_min_val} boletos o menos.
limited.raffle.ticket=Solo hay %d entradas restantes disponibles. Su compra máxima debe ser de %d boletos o menos.
RAFFLE_TICKET_PKG_NOT_FOUND=Paquete de entradas no encontrado
tickets.added=Entradas agregadas a su cuenta.
ticket.purchasing.for.event=Gracias por comprar {number_of_ticket} {ticket} para {event_name}
do.not.have.enough.ticket=No tiene suficientes boletos, compre boletos adicionales a continuación.
tickets.submitted.successfully=Entradas enviadas correctamente.
ticket.should.be.more.than.zero=Los boletos deben ser más de 0.
can.not.default.playback = No se puede establecer la reproducción predeterminada para la sesión activa
purchase.event.ticket.required.enable.show.registration.button = Actualmente no hay entradas disponibles. Por favor, contacte con el organizador del evento.
purchase.event.ticket.required.for.addons.ticket = No puede comprar entradas adicionales sin comprar entradas para eventos.
hold.token.does.not.match = El asiento seleccionado ya ha sido reservado por otra persona. Vuelve a intentarlo.
user.not.found = Usuario no encontrado
ticket.coupon.not.found = Cupón de venta de entradas no encontrado
order.not.found = Orden no encontrada
access.code.not.found=No se encontró el código de acceso.
attribute.name.exist=El nombre del atributo ya existe
attribute.not.exists=El atributo no existe
mcq.only.for.enterprise.plan=La pregunta de opción múltiple es solo para el plan empresarial
zero.order.count=el recuento de tickets de pedido es cero
number.of.seats.must.equal.to.number.of.tickets=La cantidad de asientos seleccionados debe ser la misma que la cantidad de boletos.
seat.selection.not.allowed.for.free.ticket=La selección de asientos no está permitida para boletos GRATUITOS
not.unpaid.order=El tipo de pedido NO ES SIN PAGAR.
already.paid.order=El pedido ya está pagado
credit.card.processing.not.enable=Credit card processing not enabled
birth.date.not.allowed.in.future=La fecha de nacimiento no está permitida en el futuro
blocked.admin.msg=Esta usuario está bloqueada
activate.registration=Active su evento para comenzar a aceptar inscripciones
recurring.event.modified.by.host=El anfitrión modifica los detalles del evento. Inténtalo de nuevo.
checkout.time.expire=No ha completado el pago en el tiempo asignado. Haga clic aquí para reiniciar el proceso de pago.
recurring.event.id.not.empty=El ID de evento recurrente no puede ser nulo para un evento recurrente
coupon.code.not.applicable.on.donation=El cupón de descuento no se puede aplicar al tipo de boleto de donación.
coupon.not.applied.to.this.order=El cupón no se aplica a este pedido
coupon.has.already.applied.to.your.transaction=Este código de descuento ya se aplicó a su transacción.
coupon.reached.max.usage.per.user=Ya alcanzó el número máximo de usos para este cupón.
coupon.reached.max.usage=Este cupón ha alcanzado el número máximo de usos.
coupon.not.available=El cupón no está disponible
coupon.code.expired=Este cupón ha caducado.
applied.coupon.not.found=Cupón aplicado no encontrado
barcode.not.exist=El código de barras no existe
download.pdf.is.not.allowed.for.this.order=No se permite descargar pdf para este pedido
invalid.wait.list.details=Detalles de la lista de espera no válidos.
event.tickets.not.found=No hay boletos disponibles para actualizar el estado del boleto para Check IN.
check.in.not.allowed.before.time=Check IN No permitido antes de la hora configurada
barcode.already.refunded=El registro falló. El boleto fue reembolsado.
barcode.already.check.in=El registro falló. Boleto ya usado.
attendee.have.virtual.ticket=This ticket only grants access to the virtual event
collect.payment.first=Cobra primero el pago.
not.valid.payment=Tipo de pago no válido
can.not.cancel.schedule.event=No se puede cancelar la programación del evento. Los boletos se venden a partir de este programa de eventos.
coupon.not.applicable.for.ticket.type=Este cupón no es válido para este tipo de boleto.
block.user.by.admin=Bloqueo de usuario por administrador . ${userEmail}
raffle.online.purchase.tickets=¡Gracias por comprar boletos de [number_of_tickets]! Puede comprar boletos en [raffle_ticket_purchase_url]
raffle.online.purchase=Gracias por comprar boletos de [#]. Para ingresar boletos para un artículo, responda con el código de artículo de 3 caracteres y el número de boletos. Ejemplo: ABC4
successful.ticket.submission.payment.disabled=Gracias por ingresar [tickets_submitted] tickets para el artículo [item_code]. Te quedan [numberoftickets] entradas.
causeauction.not.found=Causa Subasta no encontrada
more.pledges.submitted=Ya se ha alcanzado el número máximo de promesas aceptadas.
charge.creation.failed=Error al crear el cargo
auction.charge.failed=los cargos de la subasta fallaron
pledge.submit.msg=Compromiso enviado con éxito.
pledge.staff.success=Gracias por contribuir [currency_symbol] [amount] para el artículo [item_code].
pledge.staff.item.submitted.alert=Gracias por enviar su compromiso para el artículo [item_code], confirme su compromiso respondiendo con su nombre y apellido.
pledge.not.found=Active este módulo para comenzar a aceptar promesas.
staff.cash.pledge=¡Gracias por tu compromiso!
number.of.tickets.has.been.submitted.msg=%s Se han enviado tickets para este artículo.
string.item.has.been.purchased.msg= 	Este artículo ha sido comprado por %s%s.
string.current.bid.for.this.item.msg=La oferta actual para este artículo es %s%s, las ofertas se pueden enviar en cantidades de %s%s.
string.starting.bid.for.this.item.msg=La oferta inicial para este artículo es %s%s, las ofertas pueden presentarse en cantidades de %s%s.
min.bid.for.item.msg=La oferta mínima para este artículo es %s%s.
purchase=Compra
winner=Ganadora
potential.winner=Ganadoras potenciales
outbid=Sobrepujar
paid=Pagada
unpaid=No pagado
alerts.when.buy.it.now.item.purchased=Alertas cuando se compran artículos de Cómprelo ahora
order.confirmations.from.attendees=Confirmaciones de pedidos de mis asistentes
weekly.sales.report=Informe de ventas semanal
questions.from.attendees=Preguntas de las asistentes
payment.processing.is.not.setup=El procesamiento de pagos no está configurado. Por favor notifique al administrador.
donation.not.active=Active este módulo para comenzar a aceptar donaciones.
recurring.payment.not.supported.in.square=El pago recurrente no se admite en cuadrado.
donation.charge.failed.msg=los cargos de donación fallaron
amount.to.large=Las donaciones con tarjeta de crédito están limitadas a $ 999,999.99. Para donaciones más grandes, comuníquese directamente con la organización.
donation.thanks=Gracias por tu donación.
user.not.found.for.staff.checkout=Usuario no encontrado para el pago del personal
not.staff.user=Usuario no personal
incorrect.password=Contraseña incorrecta. Por favor, pruebe de nuevo.
phone.number.already.attach.to.email=Este número de teléfono ya está asociado a otra dirección de correo electrónico
email.already.attach.to.phone.number=Este correo electrónico ya está asociado a otro número de teléfono
user.already.exist=El usuario ya existe
user.already.present.with.country.code=El usuario ya está presente con [country_code] como código de país
item.already.purchased=Lo sentimos, este artículo ya ha sido comprado.
bid_success_notify=Oferta enviada con éxito, se le notificará si se supera la oferta.
thank.you.for.your.purchase=Gracias por su compra!
thank.you.for.purchasing=Gracias por comprar 
buy.it.now.bid.contacted.to.collect.payment=Estás enviando una oferta ¡Cómpralo ya! Nos comunicaremos con usted para cobrar el pago.
bid.should.be.greater.than.starting.bid=La oferta del artículo debe ser mayor que la oferta inicial
minimum.bid.increment.for.this.item=El incremento de oferta mínimo para este artículo es {bidIncrement}. Haga una oferta de al menos {minimumBid}.
bidding.has.ended.for.item.with.itemcode=Ha finalizado la puja por el artículo {item_short_name}.
auction.not.active=Active este módulo para comenzar a aceptar ofertas.
more.then.one.user.exist.with.this.email=Existe más de un usuario con el mismo correo electrónico
password.validation.failed=La contraseña debe contener al menos 1 mayúscula, 1 minúscula, 1 carácter especial (caracteres especiales válidos:! @ # $% ^ & * + -_ =), 1 número, sin espacios en blanco y debe tener al menos 8 caracteres de longitud
email.not.match.with.user.id=la dirección de correo electrónico no coincide con el ID de usuario [user_id]
email.not.match.with.user=la dirección de correo electrónico no coincide con el ID de usuario
bid.not.found=No se pudo encontrar la oferta.
bidder.not.found=Licitador no encontrado
payment.method.required=Se requiere método de pago.
coupon.applied.success.msg=Cupón aplicado con éxito
coupon.applied.but.not.with.donation.ticket.type=Cupón aplicado correctamente. Tenga en cuenta que el cupón no se aplicará a los boletos de donación.
coupon.delete.success.msg=Eliminar cupón correctamente
limited.display.code.saved=Código de visualización limitado guardado
limited.display.code.updated=Código de visualización limitado actualizado
limited.display.code.deleted=Código de visualización limitado eliminado
user.biddernumber.already.present=El postor número %d ya está registrado con este correo electrónico o número de teléfono.
bidder.number.not.enanled=El número de postor no está habilitado para este evento
update.bidder.number.message=El número de postor es %d actualizado correctamente
country.code.not.valid=El código de país no es válido.
bid.instuction=Haga una oferta aquí o envíe un mensaje de texto con su oferta a: [phone_number] con el código de tres letras del artículo y el monto de la oferta, ej. [item_code_example]
card.updated.successfully=Tarjeta actualizada con éxito
please.login=Por favor Iniciar sesión
receive.amount.for.unpaid.tickets=Importe recibido con éxito
not.event.host=No anfitrión del evento
welcome.bidder.number.sms.donation=[first_name], ¡bienvenido al evento! Visite [auction_url] para ofertar en línea.
welcome.bidder.number.sms=[first_name], ¡bienvenido al evento! Su número de postor es [bidder_number]. Visite [auction_url] para ofertar en línea.
winning.bid.single.item.payment.enabled=FELICIDADES. Has ganado el artículo [item_short_name]. Pague en [stripe_link] o comuníquese con la organización para pagar en efectivo.
purchase.checkout.payment.disabled=Gracias por comprar el artículo [item_short_name] ([item_code]) por [currency_symbol] [amount]. Consulte a un miembro del personal para verificar.
purchase.checkout.payment.enabled=Gracias por comprar [item_short_name] ([item_code]), complete su compra haciendo clic en este enlace [link]
mail.sent.unable.to.use.twilio=El correo electrónico se ha enviado correctamente. no podemos usar twilio en este momento, para notificar por mensaje, intente nuevamente más tarde
unable.to.use.twilio=No pudimos usar twilio en este momento. Por favor, inténtelo de nuevo más tarde.
bid.is.refunded=La oferta ya se reembolsó, no se puede marcar como distribuida.
refund.success.msg=Reembolso completo.
auction.refund.failed=No se puede reembolsar la oferta de la subasta.
bid.already.refunded=La oferta ya se reembolsó.
only.paid.bid.can.be.refunded=Lo sentimos, solo se puede reembolsar la oferta pagada.
email.send.success.msg=El correo electrónico se ha enviado correctamente.
bid.delete.success.msg=La oferta se eliminó correctamente.
can.not.delet.alreadt.paid=No puede eliminar ofertas que ya se hayan pagado.
payment.confirmation.required=Gracias por hacer una oferta por el artículo [item_short_name] ([item_code]). Visite esta página para confirmar su oferta. [link]
confirm.purchase.payment.disabled=Confirme su oferta para [item_short_name] ([item_code]) respondiendo con su nombre en el siguiente formato: Nombre Apellido
error.in.generation.of.token=Error en la generación del token de acceso
thanks.for.purchase.msg=Gracias por su compra.
not.sales.rep=No representante de ventas
tickets.are.not.currently.for.sale.for.this.event=Las entradas para este evento no están actualmente a la venta.
successfully.updated.order.tickets.status.check.in=Se actualizó con éxito el estado de los tickets de pedido a Check In.
refund.order.not.paid=Orden de reembolso no pagada
refund.amount.should.not.greater.then.paid=El monto del reembolso debe ser menor que el monto pagado.
virtual.events.are.not.support.with.recurring.event=Convierta el evento en no recurrente O comuníquese con el equipo de soporte para que sea un evento no recurrente.
not.allow.to.upload.attendees=No se permite la carga de asistentes para el plan gratuito.
some.of.fields.are.empty.in.attendee.csv=Algunos de los campos tienen un campo vacío en el CSV de carga.
email.field.should.contain.single.value=El campo de correo electrónico debe contener un solo valor de correo electrónico.
invalid.email=este no es un ID de correo electrónico válido, proporcione un ID de correo electrónico válido.
order.id.have.must.number.only=La identificación del pedido debe tener solo el número.
upload.file.header.not.correct.for.attendee.csv=El nombre de los encabezados del archivo de carga no es correcto o no está en la secuencia adecuada. Debe ser Id de transacción, Nombre, Apellido, Correo electrónico.
csv.upload.success.message=CSV subido con éxito, le notificaremos pronto por correo electrónico.
attendee.list.not.found=No se encontró la lista de asistentes.

#Billing Page
successfully.purchased.plan.msg=Gracias por comprar el plan ${planType}. Tu compra está en proceso y deberías tener acceso a la plataforma en una hora. Háganos saber a través del chat si no puede acceder a la plataforma en una hora.
only.admin.owner.can.subscribe.plan=Solo el administrador o el propietario del organizador pueden suscribirse al plan
member.not.allowed.to.subscribe.plan=Los miembros no pueden suscribirse al plan, solo el administrador/propietario puede suscribirse al plan
customer.not.found.in.chargebee=Cliente no encontrado a cargo.
subscription.cancelled.successfully=Suscripción cancelada con éxito.
white.label.url.not.found=URL de etiqueta blanca no encontrada
can.not.purchase.more.than.twenty=Se puede comprar una cantidad máxima de 20 a la vez
charge.purchased.success=Ha comprado con éxito ${quantity} cantidad del plan ${plan_name}. Puede ir a la pestaña de eventos para asociar este plan de compra con cualquiera de los eventos activos
des.charge.purchased.success=Ha comprado correctamente ${quantity} cantidad de ${charge_name} cargo.
payment.collected.successfully=Pago cobrado correctamente

#Chargebee Plan Name & charge Display Name
free=Gratis
starter=Arrancadora
enterprise=Empresa
professional=Profesional
whitelabel=Etiqueta blanca
legacy=Legado
whitelabellegacy=WhiteLabelLegacy
scale=Escala
singleeventunit=SingleEventUnit
unlimited.app=Aplicación móvil ilimitada
single.event.app.charge=Cargo de aplicación de evento único
dedicated.event.support.hours=Horas de asistencia para eventos dedicadas
attendee.upload.charge=Cargo por carga de asistentes
auction.charge=Cargo de subasta
fund.a.need.charge=Financiar un cargo por necesidad
exhibitor.pro.booth.charge=Cargo por stand de expositor Pro
raffle.charge=Cargo de la rifa
max.limit.reached=Su cuenta ha sido bloqueada para proteger su seguridad. <a href='/u/password-reset'> restablezca su contraseña </a> o vuelva a intentarlo transcurridos 30 minutos.
account.is.locked.please.try.after.sometime=Su cuenta ha sido bloqueada para proteger su seguridad. <a href='/u/password-reset'> restablezca su contraseña </a> o espere y vuelva a intentarlo.
pledge.instuction=Promesa aquí o envía un mensaje de texto con tu promesa a: [phone_number] con el código de tres letras del artículo y el monto de la promesa, ej. [item_code_example]
message.service.unable.to.send.message=El servicio de mensajes no puede enviar mensajes
raffle.instruction=Envíe sus boletos para la rifa aquí o envíelos por mensaje de texto a: [phone_number] con el código de tres letras del artículo y la cantidad de boletos para la rifa, ej. [item_code_example]
organizer.not.found=Organizador no encontrado
organizer.url.not.found=No se encontró la URL del organizador o se modificó.
user.not.allow.to.update=El usuario no permite actualizar.
user.already.present.in.team=Usuario ya presente en el equipo
organizer.is.updated.successfully=El organizador se actualiza con éxito
not.allow.to.edit.organizer.page=No autorizado para editar la página del organizador
chargebee.subscription.not.found=Suscripción no encontrada en Chargebee.
not_valid_email=No es una dirección de correo electrónico bien formada
contact.to.you.soon=Gracias por escribirnos, ¡nos comunicaremos con usted pronto!
not.super.admin=No es superadministrador
evnet.not.associated.with.this.organizer=El evento no está asociado con este organizador.
no.unsubscribedusers.are.present=No hay usuarios cancelados para este evento.
user.not.found.in.event.tickets=El usuario no ha comprado la entrada para este evento.
event.deleted.successfully=El evento se eliminó correctamente
can.not.delete.event={event_name} tiene más de 40 participantes, debido al tamaño del evento, no se puede eliminar. Si se trata de un evento de prueba, asegúrese de que el evento esté marcado como tal.
phone.number.already.attach.to.email.with.email.parameter=Este número de teléfono ya está asociado con la dirección de correo electrónico [email_address]
make.duplicate.event=Evento duplicado creado correctamente.
member.cannot.be.marked.as.billing.contact=El miembro no se puede marcar como contacto de facturación
can.not.update.billing.contact=No está autorizado para actualizar el contacto de facturación
billing.contact.updated.successfully=El contacto de facturación se actualizó correctamente
team.member.remove.from.billing.contact=Miembro del equipo eliminado de los contactos de facturación
can.not.remove.last.owner.from.billing.contact=No se puede eliminar al último propietario del contacto de facturación
can.not.remove.owner.from.billing.contact=No está autorizado para eliminar al propietario del contacto de facturación
can.not.delete.billing.contact=No está autorizado para eliminar el contacto de facturación
deleted.team.member=Miembro del equipo eliminado correctamente
can.not.delete.last.owner.billing.contact=No se puede eliminar el último propietario o contacto de facturación
can.not.delete.owner=No tienes autorización para eliminar al propietario.
invitation.sent=Invitación enviada
can.not.mark.billing.contact=No está autorizado para agregar miembros del equipo con el contacto de facturación habilitado
owner.updated.successfully=Propietario actualizado correctamente
can.not.remove.last.billing.contact=No se puede eliminar el último contacto de facturación
can.not.update.owner.of.the.organizer=No tienes autorización para cambiar de propietario.
add.embed.widget.settings.successfully=Agregue la configuración del widget para insertar correctamente
organizer.or.event.id.not.exist=El ID del organizador / evento no existe
widget.setting.already.exists=La configuración del widget para insertar ya existe
widget.setting.type.not.exists=El tipo de configuración del widget para insertar no existe
widget.setting.not.found=No se encontró la configuración del widget para insertar
not.authorized.to.create=No autorizado para crear integración
white.label.event.not.found=Este evento de marca blanca no existe
failed.to.get.user.info=Error al recuperar los detalles de los usuarios
failed.to.generate.user.token=Error al crear el token de usuario
failed.to.delete.solution.instance=Error al eliminar la instancia de la solución
failed.to.reconfigure.solution.instance=Error al reconfigurar la instancia de la solución
failed.to.get.auth.code=Error al generar el código de autorización
donation.successful=Gracias por donar Confirme su donación aquí [link]
transaction.config.not.found=Falta la configuración de la lógica condicional de transacción para la etiqueta blanca
whitelabel.stripe.not.configured=Conecte la banda para el evento de etiqueta blanca.
successfully.created.new.event=Nuevo evento creado con éxito
event.name.already.exist=El nombre del evento ya está en uso. Intente con otro nombre.
event.name.can.not.null=El nombre del evento no puede estar vacío
cannot.change.organizer.after.event.publish=No se puede cambiar de organizador después de que se publique el evento.
virtual.event.settings.created=La configuración de eventos virtuales se creó correctamente.
virtual.event.settings.updated=La configuración de eventos virtuales se actualizó correctamente.
virtual.event.custom.label.updated=Etiqueta personalizada de eventos virtuales actualizada correctamente.
virtual.event.already.created=Configuraciones de eventos virtuales ya creadas
event.challenge.config.not.found=Desafío de evento no encontrado.
setting.not.allowed=Esta configuración no está permitida para su plan de eventos.
virtual.event.settings.not.found=No se encontró la configuración del evento virtual.
popup.activate.ticketing.module=Confirme para activar el módulo de venta de entradas.
popup.finish.stripe.payments=Termine de configurar el procesamiento de pagos de Stripe para comenzar a cobrar los pagos.
popup.finish.stripe.payments.for.selling=Termine de configurar el procesamiento de pago de Stripe para comenzar a vender boletos.
popup.finish.stripe.payments.for.donations=Termine de configurar el procesamiento de pagos de Stripe para comenzar a recolectar donaciones.
settings.saved=Se guardó la configuración.

bid.instarction.str=Envíe su compromiso por mensaje de texto a [display_number] con el código de tres letras y el monto del compromiso. [amount_example_str]
phone.number.already.attach.to.email.with.parameter=Este número de teléfono [phone_number] ya está asociado con otra dirección de correo electrónico
need.to.publish.event=El plan [plan] está limitado a [noOfItems] [itemSingular]. Publica el evento para agregar más [itemPlural]
counter.exceeded.cap.usage.items=El plan [plan] está limitado a [noOfItems] [itemSingular]. Actualice su plan para agregar más [itemPlural].
pre.event.access.is.limited=El acceso previo al evento está limitado a [hours] horas antes del inicio del evento en el plan [plan]. Actualice su plan para agregar más Días de acceso previos al evento.
session.already.exists=La sesión ya existe con el mismo título
invalid.session.date.time=Introduzca un formato de fecha / hora válido. La fecha no está en formato yyyy/MM/dd ni en formato HH:mm
session.slots.collides=Las sesiones del escenario principal no pueden superponerse. Parece que tienes un conflicto con {SESSION_NAME}
networking.rule.not.found=¡No se han encontrado las reglas de red!
networking.rule.already.exists=La regla de networking ya existe.
duplicate.session.detail.id.entry=No se permite el ID de detalle de sesión duplicado.
already.ticket.type.used=No se puede eliminar este tipo de ticket. Los asistentes ya se han registrado con este tipo de entrada.
register.max.limit.reached=Su tipo de registro solo permite registrarse en {number_of_sessions_permitted} sesiones. Si desea registrarse para esta sesión, primero anule el registro de una sesión diferente.
can.not.upload.empty.file=No se puede cargar un archivo vacío. Agregue datos y vuelva a intentarlo.
upload.file.header.not.correct=Los nombres del encabezado del archivo de carga no son correctos o no están en la secuencia adecuada
session.can.not.be.delete=Esta sesión no se puede eliminar porque se está ejecutando la transmisión. Detenga la transmisión.
invalid.session.format=Formato de sesión no válido para el área seleccionada
session.position.changed.successfully=La posición de la sesión cambió correctamente ...
can.not.allowed.for.concurrent.session=Solo permitido para sesiones simultáneas
session.deleted=Sesión eliminada.
session.duplicated=Sesión duplicada correctamente.
session.hidden=Sesión oculta con éxito.
time=La hora no debe ser nula O debe estar dentro de la fecha / hora de inicio y finalización del evento O La fecha no está en formato dd/MM/yyyy O Hora HH: mm
title=El título no debe ser nulo O BIEN el título debe tener menos de 255 caracteres.
format=El formato no debe ser nulo O debe ser REGULAR_SESSION, MEET_UP, MAIN_STAGE_SESSION, WORKSHOP, EXPO, BREAK, OTHER
capacity=La capacidad debe ser numérica o vacía.
location.id=El ID de ubicación debe ser numérico o estar vacío.
signup.text=Al registrarme, acepto Accelevent&#39;s <a href=\"https://www.accelevents.com/terms-conditions/\" target=\"_blank\"> condiciones de servicio </a> y <a href=\"https://www.accelevents.com/Privacy-Policy/\" target=\"_blank\"> política de privacidad </a>.
key.value.already.exist=El nombre de {KEY} ya existe
position.changed.successfully=Posición cambiada con éxito ...
audience.filter.already.exists=El filtro de audiencia ya existe con el mismo nombre
audience.filter.not.found=Filtro de audiencia no encontrado
speaker.already.exist=El orador ya existe con el mismo correo electrónico
email.is.already.present=El registro para el correo electrónico [email_address] ya está presente.
speaker.position.changed.successfully=La posición del altavoz se cambió correctamente ...
no.speaker.profile.present=No hay perfil de orador presente
social_media_url.is.invalid=La URL de la red social no es válida, ingrese la URL válida de la red social.
first.name.not.be.null=El nombre no debe ser nulo O BIEN el nombre debe tener menos de 50 caracteres.
last.name.not.be.null=El apellido no debe ser nulo O BIEN El apellido debe tener menos de 50 caracteres.
email.not.be.null=El correo electrónico no debe ser nulo O BIEN el correo electrónico debe tener menos de 75 caracteres.
email.not.valid=Esta no es una dirección de correo electrónico válida.
email.already.in.csv.file=[email_address] Ya está presente en el archivo csv.
tital.not.be.null=El título no debe ser nulo O BIEN el título debe tener menos de 255 caracteres.
company.must.be.not.null=La empresa no debe ser nula La empresa debe tener menos de 255 caracteres.
linkdin.url.not.valid=La URL de LinkedIn no es válida
instagram.url.not.valid=La URL del identificador de Instagram no es válida
twitter.url.not.valid=La URL del identificador de Twitter no es válida
email.already.exist=[email_address] Correo electrónico ya existe.
ticketing.setting.not.found=No se encontró la configuración de emisión de boletos
coupon.updated.successfully=Cupón actualizado con éxito
more.coupon.is.used.then.set=Se usa más cupón y luego se configura
ticketing.coupon.percentage=El porcentaje del cupón de venta de entradas no debe ser superior a 100
fill.the.recurring.relative.start.and.end.time=Complete la hora de inicio y finalización relativa recurrente.
ticketing.coupon.already.exist=El cupón de venta de entradas ya existe
discount.Coupon.Saved=Cupón de descuento guardado.
coupon.deleted.successfully=Cupón eliminado correctamente
discount.code.cannot.be.deleted.once.it.has.been.used=Un código de descuento no se puede eliminar una vez que se ha utilizado.
ticket.type.does.not.grant.access=Su tipo de entrada no otorga acceso a esta sesión
auto.create.moderator=[first_name] [last_name] ahora es moderador de esta sesión
successfully.saved.wants.to.learn=Quiere aprender guardado con éxito.
speaker.email.change.not.have.attendee.access=Este nuevo orador aún no tiene acceso de asistente, seleccione un tipo de entrada.
speaker.updated=Orador actualizado
virtual.portal.image.not.found=No se encontró la imagen del portal virtual
speaker.already.exist.with.email=El orador ya existe con [email_address]
not_found_badges=Insignia no encontrada
badges.added.successfully=Insignia añadida con éxito
badges.updated.successfully=Insignia actualizada con éxito
badges.deleted=Insignia eliminada con éxito
not.found.badges.image=No se encontró la imagen de la insignia
duplicate.badges.name=El nombre de la credencial no debe estar duplicado, ingrese un nombre de credencial único
duplicate.ticketing.type.id=El tipo de boleto [ticketing_type_name] ya está seleccionado en la insignia [badge_name]
attendee.connect.request.cancelled=Solicitud de conexión cancelada
attendees.already.connected.or.rejected=Esta solicitud de conexión no está en estado pendiente y, por lo tanto, no se puede cancelar
attendee.connection.removed=Conexión eliminada
attendee.has.not.connected=No estás conectado con este asistente.
networking.lounge.deleted.by.the.admin=Salón de redes eliminado por el administrador. Actualice la página y vuelva a intentarlo.
not.allowed.to.access.virtual.event.page=El formato del evento es Presencial y no permite el acceso al Virtual Event Hub
does.not.allowed.to.change.event.format.after.event.start=Una vez iniciado el evento, no permite cambiar el formato del evento.
does.not.allowed.to.change.event.format.after.pre.event.start=Después del inicio del acceso previo al evento, no permite cambiar el formato del evento.
not.possible.to.activate.autoplay=No es posible activar la reproducción automática antes de subir un video
short.description.should.not.be.more.than.150.characters=La descripción breve no debe tener más de 150 caracteres.
ticket.does.not.allow.lounges=Su billete no permite el acceso a Salas VIP
ticket.does.not.allow.expo=Su entrada no permite el acceso a los Stands de Expositor
not.found.kiosk.mode.check.in.detail=No se encontró el detalle de check-in del quiosco
already.added.kiosk.detail=Ya ha agregado detalles de check-in en modo quiosco
networking.lounge.document.uploaded=Sala de networking ${document} subido con éxito
networking.lounge.document.updated=Salón de networking ${document} actualizado con éxito
networking.lounge.document.deleted=Salón de networking ${document} eliminado con éxito
document=documento
link=enlace
virtual.session.tags.tracks.character.limit=Las etiquetas/pistas individuales no deben tener más de 50 caracteres.