package com.accelevents.repository;

import com.accelevents.domain.EventTickets;
import com.accelevents.domain.enums.DataType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional(readOnly = true)
public interface ROEventTicketsCommonRepo extends CrudRepository<EventTickets, Long> {
    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingOrder " +
            " WHERE et.eventId=:eventId " +
            " AND et.holderUserId.userId IN :userIds " +
            " AND (:dataType is null OR et.dataType =:dataType ) " +
            " AND ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND et.ticketStatus not in ('CANCELED','DELETED')" +
            " ORDER BY et.id ")
    List<EventTickets> findByEventAndTicketStatusAndHolderUserIds(@Param("eventId") Long eventId,
                                                                  @Param("userIds") List<Long> userIds,
                                                                  @Param("recurringEventId") Long recurringEventId,
                                                                  @Param("dataType") DataType dataType);

    @Query(value = "WITH ticket_counts AS (" +
            "  SELECT ticketing_type_id, COUNT(DISTINCT et.id) as cnt " +
            "  FROM event_tickets et " +
            "  WHERE et.ticket_status NOT IN ('CANCELED','DELETED') " +
            "  AND EXISTS (SELECT 1 FROM event_ticket_type ett WHERE ett.id = et.ticketing_type_id AND ett.ticketing_ID = :ticketingId) " +
            "  GROUP BY ticketing_type_id" +
            "  UNION ALL " +
            "  SELECT tom.ticket_type_id, SUM(tom.number_of_ticket) as cnt " +
            "  FROM ticketing_order tOr " +
            "  JOIN ticketing_order_manager tom ON tOr.id = tom.order_id " +
            "  WHERE tOr.expire_in >= NOW() AND tOr.order_status = :created " +
            "  AND tOr.event_id = :eventId " +
            "  GROUP BY tom.ticket_type_id" +
            ") " +
            "SELECT ticketing_type_id, SUM(cnt) as soldTicket " +
            "FROM ticket_counts " +
            "GROUP BY ticketing_type_id", nativeQuery = true)
    List<Object[]> getSoldCountByTicketingId(
            @Param("eventId") Long eventId,
            @Param("ticketingId") Long ticketingId,
            @Param("created") String created);
}
