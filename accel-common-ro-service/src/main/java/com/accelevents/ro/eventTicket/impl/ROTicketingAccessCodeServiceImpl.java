package com.accelevents.ro.eventTicket.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketingAccessCode;
import com.accelevents.dto.LabelKeyDTO;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.ro.eventTicket.ROTicketingAccessCodeService;
import com.accelevents.ro.eventTicket.repo.ROTicketingAccessCodeRepo;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.NumberUtils;
import com.accelevents.utils.TimeZone;
import com.accelevents.utils.TimeZoneUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;

@Service
public class ROTicketingAccessCodeServiceImpl implements ROTicketingAccessCodeService {


    private final ROTicketingAccessCodeRepo roTicketingAccessCodeRepo;

    @Autowired
    public ROTicketingAccessCodeServiceImpl(ROTicketingAccessCodeRepo roTicketingAccessCodeRepo) {
        this.roTicketingAccessCodeRepo = roTicketingAccessCodeRepo;
    }

    @Override
    public boolean isValidAccessCode(Event event, String accessCode, Long recurringEventId) {
        return isNumberGreaterThanZero(recurringEventId) ?
                roTicketingAccessCodeRepo.existsByNameAndEventIdAndRecurringEventId(accessCode, event, recurringEventId) :
                roTicketingAccessCodeRepo.existsByNameAndEventId(accessCode,event);

    }

    @Override
    public List<LabelKeyDTO> getAccessCodeIdAndNameMapByEventId(Long eventId) {
        return roTicketingAccessCodeRepo.findTicketingAccessCodeByEventId(eventId);
    }

    @Override
    public Optional<TicketingAccessCode> getByCodeAndCheckEndDate(String accessCode, Event event, Long recurringEventId) {
        Optional<TicketingAccessCode> ticketingAccessCode;
        if (NumberUtils.isNumberGreaterThanZero(recurringEventId)) {
            ticketingAccessCode =  roTicketingAccessCodeRepo.findByCodeAndEventIdAndRecurringEventId(accessCode, event, recurringEventId);
        } else {
            ticketingAccessCode = roTicketingAccessCodeRepo.findByCodeAndEventIdAndRecurringEventIdIsNull(accessCode, event);
        }

        if(ticketingAccessCode.isPresent()) {
            TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());
            String eventTimeZone = timeZone.getEquivalentTimezone();
            Date endDate = DateUtils.getDate(ticketingAccessCode.get().getEndDate(), eventTimeZone);
            Date startDate = DateUtils.getDate(ticketingAccessCode.get().getStartDate(), eventTimeZone);
            Date currentDate = DateUtils.getDate(DateUtils.getCurrentDate(), eventTimeZone);
            if(startDate.compareTo(currentDate) > 0){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_ACCESS_CODE_IN_FUTURE);
            } else if(endDate.compareTo(currentDate) < 0){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_ACCESS_CODE_IS_EXPIRED);
            } else {
                return ticketingAccessCode;
            }
        }

        return Optional.empty();
    }

    @Override
    public Optional<TicketingAccessCode> getByCode(String accessCode, Event event, Long recurringEventId) {
        return roTicketingAccessCodeRepo.findByCodeAndEventId(accessCode, event, recurringEventId);
    }
}
