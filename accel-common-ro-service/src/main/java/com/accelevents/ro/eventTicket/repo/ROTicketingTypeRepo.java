package com.accelevents.ro.eventTicket.repo;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.LabelKeyDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
@Transactional(readOnly = true)
public interface ROTicketingTypeRepo extends org.springframework.data.repository.Repository<TicketingType, Long> {

    @Query("SELECT COUNT(1) FROM TicketingType ticketingType " +
            "WHERE ticketingType.eventId = :eventId AND ticketingType.price > 0 AND ticketingType.dataType='TICKET' ")
    Long existsByEventIdAndPriceGreaterThanZero(@Param("eventId") Long eventId);

    @Query("	SELECT ticketType.id,ticketType.ticketTypeName,ticketing.eventid.name FROM TicketingType AS ticketType " +
            " 	JOIN ticketType.ticketing AS ticketing " +
            " 	WHERE ticketing.eventid.eventId in(:eventId) " +
            "	AND ticketType.dataType =:dataType " +
            " 	AND ticketType.recurringEventId IS NULL " +
            " 	AND ticketType.ticketTypeName like %:ticketTypeName% " +
            " 	ORDER BY ticketType.position DESC")
    List<Object[]> findAllByEventsIdRecurringIdNullAndTicketTypeLikeAAndTicketTypeNameLike(@Param("eventId") List<Long> eventId, @Param("dataType") DataType dataType, @Param("ticketTypeName") String ticketTypeName);

    @Query(value = "select new com.accelevents.dto.LabelKeyDTO(ett.id,ett.ticketTypeName) from TicketingType ett where ett.eventId=:eventId and ett.dataType=:dataType")
    List<LabelKeyDTO> findAllTicketingTypeAddonByEventId(@Param("eventId") Long eventId,@Param("dataType") DataType dataType);

    @Query("SELECT ett.eventId, " +
            " SUM(case when (ett.bundleType !='INDIVIDUAL_TICKET') then (ett.numberOfTickets * ett.numberOfTicketPerTable) else ett.numberOfTickets end) " +
            " FROM TicketingType as ett" +
            " WHERE  ett.dataType ='TICKET' AND ett.ticketType <> 'DONATION'" +
            " AND ett.eventId IN(:eventIds) GROUP BY ett.eventId")
    List<Object[]> findNumberOfTotalTickets(@Param("eventIds") Set<Long> eventIds);

    @Query("SELECT tt FROM TicketingType tt " +
            "WHERE tt.eventId = :#{#event.eventId} " +
            "AND tt.recurringEventId IS NULL " +
            "AND (:excludeHidden = false OR tt.isHidden = false) " +
            "ORDER BY tt.position ASC")
    List<TicketingType> getTicketingTypesForNonRecurringEvent(@Param("event") Event event,
                                                              @Param("excludeHidden") boolean excludeHidden);

    @Query("SELECT tt FROM TicketingType tt " +
            "WHERE tt.eventId = :#{#event.eventId} " +
            "AND tt.recurringEventId = :recurringEventId " +
            "AND (:excludeHidden = false OR tt.isHidden = false) " +
            "ORDER BY tt.position ASC")
    List<TicketingType> getTicketingTypesForRecurringEvent(@Param("event") Event event,
                                                           @Param("excludeHidden") boolean excludeHidden,
                                                           @Param("recurringEventId") Long recurringEventId);

    /**
     * Find ticketing types by IDs and event - MUST match original business logic exactly
     */
    @Query("SELECT tt FROM TicketingType tt " +
           "WHERE tt.id IN :ticketTypeIds " +
           "AND tt.event = :event " +
           "ORDER BY tt.position DESC")
    List<TicketingType> findByIdInAndEvent(@Param("ticketTypeIds") List<Long> ticketTypeIds,
                                         @Param("event") Event event);

    /**
     * Get ticket type ID and number of tickets - MUST match original business logic exactly
     */
    @Query("SELECT tt.id, tt.numberOfTickets, tt.minTicketsPerBuyer " +
           "FROM TicketingType tt " +
           "WHERE tt.event = :event " +
           "AND (:recurringEventId = 0 OR tt.recurringEventId = :recurringEventId)")
    List<Object[]> getTicketTypeIdAndNumberOfTickets(@Param("event") Event event,
                                                   @Param("recurringEventId") int recurringEventId);

    /**
     * Find ticketing types by IDs only - MUST match original business logic exactly
     */
    @Query("SELECT tt FROM TicketingType tt WHERE tt.id IN :ticketTypeIds")
    List<TicketingType> findByIdIn(@Param("ticketTypeIds") List<Long> ticketTypeIds);

    /**
     * Find ticketing types by recurring event ID with hidden filter - maintains original business logic
     */
    @Query(value = "SELECT * FROM event_ticket_type " +
            "WHERE recurring_event_id = :recurringEventId " +
            "AND (CASE WHEN :excludeHidden = true THEN is_hidden = false ELSE 1=1 END) " +
            "ORDER BY position ASC", nativeQuery = true)
    List<TicketingType> findByRecurringEventIdAndHidden(@Param("recurringEventId") Long recurringEventId,
                                                        @Param("excludeHidden") boolean excludeHidden);
}
