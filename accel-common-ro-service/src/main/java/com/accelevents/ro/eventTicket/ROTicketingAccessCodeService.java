package com.accelevents.ro.eventTicket;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketingAccessCode;
import com.accelevents.dto.LabelKeyDTO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ROTicketingAccessCodeService {

    boolean isValidAccessCode(Event event, String accessCode, Long recurringEventId);

    List<LabelKeyDTO> getAccessCodeIdAndNameMapByEventId(Long eventId);

    Optional<TicketingAccessCode> getByCodeAndCheckEndDate(String accessCode, Event event, Long recurringEventId);

    Optional<TicketingAccessCode> getByCode(String accessCode, Event event, Long recurringEventId);
}
