package com.accelevents.ro.eventTicket.repo;

import com.accelevents.common.dto.TicketTypeOtherRestrictionDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventTickets;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.virtualevents.dto.UserPurchasedTicketStatusDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
@Transactional(readOnly = true)
public interface ROEventTicketsRepo extends org.springframework.data.repository.Repository<EventTickets, Long>{

    @Query("select new com.accelevents.common.dto.TicketTypeOtherRestrictionDto(ticketingType.id," +
            " ticketingType.isChatRestricted, ticketingType.isExpoRestricted, ticketingType.isLoungesRestricted) " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.holderUserId=:holderUserId AND " +
            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.dataType ='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.event=:event ")
    List<TicketTypeOtherRestrictionDto> getUserTicketOtherRestrictions(@Param("holderUserId") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus, @Param("event") Event event);

    @Query(" SELECT new com.accelevents.virtualevents.dto.UserPurchasedTicketStatusDTO(" +
            "case when count(case when et.recordStatus='BLOCK' then 1 else null end) > 0 then true else false end, " +
            "case when count(case when et.ticketPrice > 0 AND ticketingType.ticketType = 'PAID' AND ticketingType.dataType = 'TICKET' then 1 else null end) > 0 then true else false end, " +
            "case when count(case when et.ticketPrice <= 0 then 1 else null end) > 0 then true else false end, " +
            "case when count(case when et.ticketPrice <= 0 AND et.checkInSource IN ('VIRTUAL_EVENT_PORTAL','HYBRID_CHECKIN') THEN 1 ELSE null END) > 0 then true else false end, " +
            "case when count(case when et.ticketPrice > 0 AND ticketingType.ticketType = 'DONATION' then 1 else null end) > 0 then true else false end " +
            ")" +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event = :event AND " +
            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.holderUserId = :user AND "  +
            " et.guestOfBuyer = false")
    UserPurchasedTicketStatusDTO getUserPurchasedTicketStatus(@Param("user") User user, @Param("event") Event event, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(" SELECT count(1) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event = :event AND " +
            " et.checkInSource IN ('VIRTUAL_EVENT_PORTAL','HYBRID_CHECKIN') AND " +
            " et.checkInDate is not null AND " +
            " et.ticketStatus NOT IN (:ticketStatus)")
    Long getCountOfFreeAttendeesCheckedIn(@Param("event") Event event,
                                          @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et.eventId FROM EventTickets AS et " +
            " JOIN et.event AS event " +
            " WHERE et.holderUserId = :user"  +
            " ORDER BY et.id DESC")
    List<Long> findEventFromPurchasedTickets(@Param("user") User user);

    @Query("SELECT e.eventId, COUNT(ett.id) FROM EventTickets et JOIN et.ticketingTypeId AS ett, Event AS e " +
            " WHERE e.ticketingId = ett.ticketing.id AND e.eventId IN(:eventIds) " +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ett.dataType ='TICKET' AND ett.ticketType <> 'DONATION'" +
            " GROUP BY e.eventId")
    List<Object[]> getTicketSoldCount(@Param("eventIds") Set<Long> eventIds, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT ticketingType.id FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user" +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' ")
    List<Long> getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);
}
