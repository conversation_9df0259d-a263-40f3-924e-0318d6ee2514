package com.accelevents.ro.eventTicket.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.LabelKeyDTO;
import com.accelevents.ro.eventTicket.ROTicketingTypeTicketService;
import com.accelevents.ro.eventTicket.repo.ROTicketingTypeRepo;
import com.accelevents.utils.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import java.util.Collections;
import java.util.Set;

import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;

@Service
public class ROTicketingTypeTicketServiceImpl implements ROTicketingTypeTicketService {

    @Autowired
    private ROTicketingTypeRepo roTicketingTypeRepo;

    @Override
    public boolean isAnyTicketExistsWithAmountGreaterThanZero(Event event) {
        return roTicketingTypeRepo.existsByEventIdAndPriceGreaterThanZero(event.getEventId()).intValue() > 0;
    }

    @Override
    public List<LabelKeyDTO> getAddOnIdAndNameMapByEventId(Long eventId) {
        return roTicketingTypeRepo.findAllTicketingTypeAddonByEventId(eventId, DataType.ADDON);
    }

    @Override
    public List<Object[]> getNumberOfTotalTickets(Set<Long> eventIds) {
        if(eventIds == null || eventIds.isEmpty())
            return Collections.emptyList();

        return roTicketingTypeRepo.findNumberOfTotalTickets(eventIds);
    }

    @Override
    public List<TicketingType> getTicketingTypesDisplay(Event event, boolean excludeHidden, Long recurringEventId, Ticketing ticketing) {
        if(ticketing.isRecurringEvent() && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
            return roTicketingTypeRepo.findByRecurringEventIdAndHidden(recurringEventId, excludeHidden);
        } else {
            if (NumberUtils.isNumberGreaterThanZero(recurringEventId)) {
                return roTicketingTypeRepo.getTicketingTypesForRecurringEvent(event, excludeHidden, recurringEventId);
            } else {
                return roTicketingTypeRepo.getTicketingTypesForNonRecurringEvent(event, excludeHidden);
            }
        }
    }

    @Override
    public List<TicketingType> findByIdInAndEvent(List<Long> ticketTypeIds, Event event) {
        if (ticketTypeIds == null || ticketTypeIds.isEmpty()) {
            return Collections.emptyList();
        }
        return roTicketingTypeRepo.findByIdInAndEvent(ticketTypeIds, event);
    }

    @Override
    public List<Object[]> getTicketTypeIdAndNumberOfTickets(Event event, Long recurringEventId) {
        int recurringEventIdInt = recurringEventId != null ? recurringEventId.intValue() : 0;
        return roTicketingTypeRepo.getTicketTypeIdAndNumberOfTickets(event, recurringEventIdInt);
    }

    @Override
    public List<TicketingType> getByTicketingTypeIds(List<Long> ticketTypeIds) {
        if (ticketTypeIds == null || ticketTypeIds.isEmpty()) {
            return Collections.emptyList();
        }
        return roTicketingTypeRepo.findByIdIn(ticketTypeIds);
    }
}
