package com.accelevents.ro.eventTicket;

import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingType;
import com.accelevents.dto.LabelKeyDTO;

import java.util.List;

import java.util.Set;

public interface ROTicketingTypeTicketService {
    boolean isAnyTicketExistsWithAmountGreaterThanZero(Event event);

    List<LabelKeyDTO> getAddOnIdAndNameMapByEventId(Long eventId);

    List<Object[]> getNumberOfTotalTickets(Set<Long> eventIds);

    List<TicketingType> getTicketingTypesDisplay(Event event, boolean excludeHidden, Long recurringEventId, Ticketing ticketing);

    List<TicketingType> findByIdInAndEvent(List<Long> ticketTypeIds, Event event);

    List<Object[]> getTicketTypeIdAndNumberOfTickets(Event event, Long recurringEventId);

    List<TicketingType> getByTicketingTypeIds(List<Long> ticketTypeIds);
}
