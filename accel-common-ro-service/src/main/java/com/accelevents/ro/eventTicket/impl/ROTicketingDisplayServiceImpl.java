package com.accelevents.ro.eventTicket.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RegistrationRequestType;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.domain.ticketing.TicketingCacheTicketTypeContainerDto;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketType;
import com.accelevents.registration.approval.domain.RegistrationRequest;
import com.accelevents.ro.eventTicket.*;
import com.accelevents.ro.eventTicket.coupons.ROTicketingCouponService;
import com.accelevents.ro.eventTicket.repo.ROTicketingRepo;
import com.accelevents.ticketing.dto.RegistrationRequestStatusDto;
import com.accelevents.utils.GeneralUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.JpaObjectRetrievalFailureException;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.messages.TicketBundleType.INDIVIDUAL_TICKET;
import static com.accelevents.utils.Constants.STRING_COMMA;
import static com.accelevents.utils.Constants.STRING_NULL;

@Service
public class ROTicketingDisplayServiceImpl implements ROTicketingDisplayService {

    private static final Logger log = LoggerFactory.getLogger(ROTicketingDisplayServiceImpl.class);

    @Autowired
    private RORegistrationRequestsService roRegistrationRequestsService;

    @Autowired
    private ROTicketingCouponService roTicketingCouponService;

    @Autowired
    private ROTicketingAccessCodeService roTicketingAccessCodeService;

    @Autowired
    private ROTicketingRepo roTicketingRepo;

    @Autowired
    private ROTicketingLimitedDisplayCodeService roTicketingLimitedDisplayCodeService;

    @Autowired
    private ROTicketingTypeTicketService roTicketingTypeTicketService;

    @Autowired
    private ROTicketingOrderService roTicketingOrderService;

    @Override
    public RegistrationRequestStatusDto getRegistrationRequestStatusDto(Event event, Long recurringEventId, User user) {
        RegistrationRequestStatusDto registrationRequestStatusDto = new RegistrationRequestStatusDto();
        List<RegistrationRequest> requests = roRegistrationRequestsService.getRegistrationRequests(event, recurringEventId, user);
        requests.stream().filter(e -> RegistrationRequestType.ATTENDEE.equals(e.getType())).findFirst().ifPresent(
                registrationRequests -> registrationRequestStatusDto.setAttendeeRegistrationRequestStatus(registrationRequests.getStatus().toString()));

        requests.stream().sorted(Comparator.comparing(RegistrationRequest::getId).reversed()).filter(e -> RegistrationRequestType.EXPO.equals(e.getType())).findFirst().ifPresent(
                registrationRequests -> registrationRequestStatusDto.setExpRegistrationRequestStatus(registrationRequests.getStatus().toString()));

        requests.stream().sorted(Comparator.comparing(RegistrationRequest::getId).reversed()).filter(e -> RegistrationRequestType.SPEAKER.equals(e.getType())).findFirst().ifPresent(
                registrationRequests -> registrationRequestStatusDto.setSpeakerRegistrationRequestStatus(registrationRequests.getStatus().toString()));

        requests.stream().sorted(Comparator.comparing(RegistrationRequest::getId).reversed()).filter(e -> RegistrationRequestType.REVIEWER.equals(e.getType())).findFirst().ifPresent(
                registrationRequests -> registrationRequestStatusDto.setReviewerRegistrationRequestStatus(registrationRequests.getStatus().toString()));
        return registrationRequestStatusDto;
    }

    @Override
    public boolean isValidDiscountCode(Event event, String discountCodeOrAccessCode, Long recurringEventId) {
        boolean isValidAccessCode = roTicketingAccessCodeService.isValidAccessCode(event,discountCodeOrAccessCode,recurringEventId);
        if(isValidAccessCode) return false;
        return roTicketingCouponService.isDisCountCodeExistsByEventIdOrRecurringEventIdAndCouponCode(event.getEventId(), recurringEventId, discountCodeOrAccessCode);
    }

    @Override
    public TicketingCacheTicketTypeContainerDto getTicketingCacheTicketTypeContainerDto(Event event, Long recurringEventId, String accessCode, String displayCode) {
        Ticketing ticketing;
        try {
            ticketing = this.roTicketingRepo.findByEventid(event);
        } catch (JpaObjectRetrievalFailureException exception) {
            log.info("Exception while getting ticketing info: {}", exception.getMessage());
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }
        if (ticketing == null) {
            throw new NotFoundException(NotFoundException.NotFound.TICKETING_SETTING_NOT_FOUND);
        }

        TicketingCacheTicketTypeContainerDto ticketingCacheTicketTypeContainerDto = new TicketingCacheTicketTypeContainerDto();
        ticketingCacheTicketTypeContainerDto.addAllTicketTypesDtos(this.getTicketTypesForCachedAPI(accessCode, event, ticketing, recurringEventId,displayCode));

        return ticketingCacheTicketTypeContainerDto;
    }

    protected List<TicketTypeDto> getTicketTypesForCachedAPI(String accessCode, Event event, Ticketing ticketing, Long recurringEventId, //NOSONAR
                                                             String displayCode) {
        log.info("Getting ticket types for event: {} with access code: {} and recurring event id: {}", event.getEventId(), accessCode, recurringEventId);

        List<TicketingType> ticketingTypes;

        // Pre-compute common values
        final boolean isChartKeyEmpty = StringUtils.isEmpty(ticketing.getChartKey());
        final boolean isAttendeeRegistrationApproval = ticketing.isAttendeeRegistrationApproval();

        if (StringUtils.isBlank(accessCode)) {
            boolean excludeHidden = isChartKeyEmpty;
            ticketingTypes = roTicketingTypeTicketService.getTicketingTypesDisplay(event, excludeHidden, recurringEventId, ticketing)
                    .stream().filter(e -> e.getDataType() == DataType.TICKET || !e.isHidden())
                    .filter(ticketingType -> isChartKeyEmpty || Boolean.TRUE.equals(ticketingType.isAllowAssignedSeating()) || Boolean.FALSE.equals(ticketingType.isHidden()))
                    .collect(Collectors.toList());
        } else {
            List<TicketingType> finalTicketingTypes = new ArrayList<>();
            roTicketingAccessCodeService.getByCodeAndCheckEndDate(accessCode, event, recurringEventId)
                    .ifPresent(ticketingAccessCode -> {
                        if (ticketingAccessCodeHasRemainingUses(ticketingAccessCode, event)) {
                            finalTicketingTypes.addAll(getTicketTypeFromAccessCode(ticketingAccessCode, event));
                        }
                    });
            ticketingTypes = finalTicketingTypes;
        }

        if (ticketingTypes.isEmpty()) {
            return new ArrayList<>();
        }

        // Batch collect all ticket type IDs upfront
        List<Long> allTicketTypeIds = ticketingTypes.stream().map(TicketingType::getId).collect(Collectors.toList());

        // Single DB call for display codes using RO service
        List<String> ticketTypeIds = StringUtils.isNotBlank(displayCode) ?
                roTicketingLimitedDisplayCodeService.getTicketTypeIdsByEventIdAndLimitedDisplayCode(event.getEventId(), displayCode, recurringEventId) :
                null;

        // Convert to Set for O(1) lookup performance
        Set<String> ticketTypeIdSet = ticketTypeIds != null ? new HashSet<>(ticketTypeIds) : Collections.emptySet();

        return ticketingTypes.stream()
                .filter(ticketingType -> !(isAttendeeRegistrationApproval && TicketType.DONATION.equals(ticketingType.getTicketType())
                )).filter(ticketingType -> {
                    if (StringUtils.isNotBlank(displayCode) && ticketTypeIds != null) {
                        boolean isAddon = DataType.ADDON.equals(ticketingType.getDataType()) &&
                                !Collections.disjoint(allTicketTypeIds, GeneralUtils.convertCommaSeparatedToListLong(ticketingType.getListOfTicketTypesForAddOn()));

                        return !DataType.TICKET.equals(ticketingType.getDataType()) && !isAddon || ticketTypeIdSet.contains(String.valueOf(ticketingType.getId()));
                    }
                    return true;
                }).map(ticketingType -> {
                    TicketTypeDto ticketType = new TicketTypeDto();
                    ticketType.setTypeId(ticketingType.getId());
                    ticketType.setName(ticketingType.getTicketTypeName());
                    ticketType.setMaxTickerPerBuyer(ticketingType.getMaxTicketsPerBuyer());
                    ticketType.setMinTickerPerBuyer(ticketingType.getMinTicketsPerBuyer());
                    ticketType.setAllowAssignedSeating(ticketingType.isAllowAssignedSeating());
                    ticketType.setBundleType(ticketingType.getBundleType());
                    ticketType.setPosition(ticketingType.getPosition());
                    ticketType.setTicketType(ticketingType.getTicketType());
                    ticketType.setPassFeesToBuyer(ticketingType.isPassfeetobuyer());
                    if (!INDIVIDUAL_TICKET.equals(ticketingType.getBundleType())) {
                        ticketType.setTicketsPerTable(ticketingType.getNumberOfTicketPerTable());
                    }
                    ticketType.setEndDate(ticketingType.getEndDate());
                    ticketType.setStartDate(ticketingType.getStartDate());
                    ticketType.setPrice(ticketingType.getPrice());
                    ticketType.setCategoryId(ticketingType.getCategoryId());
                    ticketType.setDataType(ticketingType.getDataType());
                    ticketType.setPayLater(ticketingType.isPayLater());
                    ticketType.setRegistrationApprovalRequired(ticketingType.isRegistrationApprovalRequired());
                    ticketType.setPassFeesVatToBuyer(ticketingType.isPassFeeVatToBuyer());
                    ticketType.setTicketTypeFormat(ticketingType.getTicketTypeFormat());
                    ticketType.setAllTicketTypesSelectedForAddOn(ticketingType.isAllTicketTypesSelectedForAddOn());
                    ticketType.setListOfTicketTypesForAddOn(GeneralUtils.convertCommaSeparatedToListLong(ticketingType.getListOfTicketTypesForAddOn()));
                    ticketType.setRequireDepositAmount(ticketingType.isRequireDepositAmount());
                    return ticketType;
                }).collect(Collectors.toList());
    }

    private boolean ticketingAccessCodeHasRemainingUses(TicketingAccessCode ticketingAccessCode, Event event) {
        return ticketingAccessCode.getUses() == -1 ||
                (ticketingAccessCode.getUses() - roTicketingOrderService.getAccessCodeUsed(event.getEventId(), ticketingAccessCode.getId()) > 0);
    }

    private List<TicketingType> getTicketTypeFromAccessCode (TicketingAccessCode ticketingAccessCode,Event event) {
        List<Long> ticketTypeList = Arrays.stream(ticketingAccessCode.getEventTicketTypeId().
                        split(STRING_COMMA)).filter(s -> StringUtils.isNoneBlank(s) && !STRING_NULL.equalsIgnoreCase(s)).
                map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        // Use RO service for optimized read-only operation
        return roTicketingTypeTicketService.findByIdInAndEvent(ticketTypeList, event);
    }

}
