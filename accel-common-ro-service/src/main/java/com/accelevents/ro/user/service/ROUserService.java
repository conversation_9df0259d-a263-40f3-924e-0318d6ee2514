package com.accelevents.ro.user.service;

import com.accelevents.domain.AccelEventsPhoneNumber;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.dto.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface ROUserService {

    Optional<User> findByIdOp(Long userId);

    Optional<User> getUserById(Long userId);

    List<String> getUserRoles(User user);

    boolean isSuperAdminUser(User user);

    boolean isSuperAdminUser(User user, List<String> userRoles);

    boolean isWhiteLabelAdminUser(User user);

    boolean isEventCoordinatorUser(User user);

    boolean isEventBudgetOwnerUser(User user);

    boolean isEventPlannerUser(User user);

    UserInfoDto extractUserInfo(Long userId);

    UserInfoDto extractUserInfo(User user);

    AccessTokenModel getUserDetailForLogin(UserLoginDto loginDto);

    Optional<User> getUserByPhoneOrEmail(String phoneOrEmail);

    Optional<User> findOpUserByEmail(String email);

    Optional<User> getUserByEmail(String email);

    List<User> findUserByPhoneNumberCountryCodeList(long phoneNumber, CountryCode countryCode);

    Optional<User> getUserByAEPhoneNumber(AccelEventsPhoneNumber phoneNumber);

    AccessTokenModel tokenLogin(String token);

    AccessTokenModel getUserDetailForSMS(User user);

    AccessTokenModel getUserDetailForSMS(User user, String eventUrl);

    @Transactional
    AccessTokenModel getWhiteLabelUserDetailForLogin(UserLoginDto loginDto, String whiteLabelURL, boolean skipPassword, boolean ssoLogin);

    User findByEmail(String email);

    User findByPhone(long phonenumber, CountryCode countryCode);

    User findByEmailOrCellNumberAndCountryCode(String email, long phonenumber, CountryCode countryCode);

    Optional<User> findUserByCountryCodeAndPhoneNumber(long phoneNumber, CountryCode countryCode);

    boolean isDuplicatePhoneNumber(AccelEventsPhoneNumber phoneNumber);

    boolean isSalesSuperAdmin(User user, List<String> userRoles);

    boolean isChargebeeConfigAdminRole(User user, List<String> userRoles);

    Optional<User> findByEmailAndSignUpDateAfter(String email, Date signUpDate);

    List<String> findUniqueCompanyListByEventId(Long eventId, String searchString, int offset, int limit);

    Long countUniqueCompanyListByEventId(Long eventId, String searchString);

    List<String> findUniqueTitleListByEventId(Long eventId, String searchString, int offset, int limit);

    Long countUniqueTitleListByEventId(Long eventId, String searchString);

    List<User> getListOfUsersByUserIds(List<Long> userList);

    List<User> findAllAttendeesByEvent(Long eventId);

    List<User> getUsersByUserIds(List<Long> userIds);

    DataTableResponse getUserEventsInfo(User user, String source, String eventStatus, String searchString, Date searchDate, PageSizeSearchObj pageSizeSearchObj);

    List<Object[]> findAllByEmails(List<String> emails);
}
