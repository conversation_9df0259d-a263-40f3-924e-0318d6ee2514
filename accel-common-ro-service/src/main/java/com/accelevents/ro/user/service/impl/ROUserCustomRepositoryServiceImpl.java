package com.accelevents.ro.user.service.impl;

import com.accelevents.column.selection.ColumnSelectionConstants;
import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repository.ROTicketRequiresAttributesRepository;
import com.accelevents.ro.event.service.ROUserSessionService;
import com.accelevents.ro.eventTicket.ROTicketingAccessCodeService;
import com.accelevents.ro.eventTicket.coupons.ROTicketingCouponService;
import com.accelevents.ro.eventTicket.tracking_link.ROTrackingLinksService;
import com.accelevents.ro.user.repo.ROUserRepo;
import com.accelevents.ro.user.service.ROUserCustomRepositoryService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.column.selection.ColumnSelectionConstants.*;
import static com.accelevents.column.selection.ColumnSelectionConstants.EMAIL;
import static com.accelevents.column.selection.ColumnSelectionConstants.FIRST_NAME;
import static com.accelevents.column.selection.ColumnSelectionConstants.LAST_NAME;
import static com.accelevents.dto.AudienceConstants.*;
import static com.accelevents.dto.AudienceConstants.DATE;
import static com.accelevents.utils.Constants.*;

@Service
public class ROUserCustomRepositoryServiceImpl implements ROUserCustomRepositoryService {
    private final Logger log = LoggerFactory.getLogger(ROUserCustomRepositoryServiceImpl.class);

    private final ROTicketRequiresAttributesRepository roTicketRequiresAttributesRepository;
    private final EntityManager entityManager;
    private final ROUserRepo roUserRepository;
    private final ROUserSessionService roUserSessionService;

    @Autowired
    public ROUserCustomRepositoryServiceImpl(ROTicketRequiresAttributesRepository roTicketRequiresAttributesRepository,
                                             EntityManager entityManager,
                                             ROUserRepo roUserRepository,
                                             ROUserSessionService roUserSessionService) {
        this.roTicketRequiresAttributesRepository = roTicketRequiresAttributesRepository;
        this.entityManager = entityManager;
        this.roUserRepository = roUserRepository;
        this.roUserSessionService = roUserSessionService;
    }

    @Override
    public Page<UserAttendeeDTO> getAttendeesByDisplayViewFilters(Event event, int page, int size, String search, String sortBy, String sortDirection, ViewFilterDetailsDTO viewFilterDetailsDto,
                                                                  AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsAdvanceFilterDto,
                                                                  AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsStaticAdvanceFilterDto) {
        String advanceFilterConditions = buildQueryWithAdvanceFilterAttributes(attendeeAnalyticsAdvanceFilterDto, attendeeAnalyticsStaticAdvanceFilterDto,event.getEventId());
        String query = prepareQuery(page, size, sortBy, sortDirection, advanceFilterConditions);

        Query request = entityManager.createNativeQuery(query).setParameter("searchString", "%" + search + "%")
                .setParameter("eventId", event.getEventId());

        List<Object[]> resultList = request.setParameter("eventTicketTypeIds", getFilterTicketTypes(viewFilterDetailsDto.getFilterTicketTypes()))
                .setParameter("ticketStatus", getFilterStatus(viewFilterDetailsDto.getFilterStatus())).getResultList();
        Long count;
        if (StringUtils.isNotBlank(advanceFilterConditions)) {
            count = getAllAttendeeCountByEventAndTicketStatusAndTicketTypesWithAdvanceFilter(event.getEventId(), search, getFilterStatus(viewFilterDetailsDto.getFilterStatus()), getFilterTicketTypes(viewFilterDetailsDto.getFilterTicketTypes()), advanceFilterConditions);
        } else {
            count = roUserRepository.getAllAttendeeCountByEventAndTicketStatusAndTicketTypes(event.getEventId(), search, getFilterStatus(viewFilterDetailsDto.getFilterStatus()), getFilterTicketTypes(viewFilterDetailsDto.getFilterTicketTypes()));
        }


        List<UserAttendeeDTO> users = resultList.stream().filter(Objects::nonNull).map(userData -> {
            UserAttendeeDTO user = new UserAttendeeDTO();
            user.setUserId(((BigInteger) userData[0]).longValue());
            user.setFirstName(Objects.toString(userData[1].toString()));
            user.setLastName(Objects.toString(userData[2].toString()));
            user.setEmail(Objects.toString(userData[3].toString()));
            user.setPhoto(Objects.toString(userData[4], null));
            if (userData[5] != null) {
                user.setEventTicketId(((BigInteger) userData[5]).longValue());
            }
            if (userData[6] != null) {
                user.setOrderId(((BigInteger) userData[6]).longValue());
            }
            if (userData[7] != null) {
                user.setTicketStatus(userData[7].toString());
            }
            if (userData[8] != null) {
                user.setEntryExitStatus(userData[8].toString());
            }
            if (userData[9] != null) {
                user.setBarcodeId(userData[9].toString());
            }
            return user;
        }).collect(Collectors.toList());

        log.info("Result fetched successfully users result size {}, user count {} ", users.size(), count);
        return new PageImpl<>(users, PageRequest.of(page, size), count);
    }

    @Override
    public Long getAllAttendeeCountByEventAndTicketStatusAndTicketTypesWithAdvanceFilter(Long eventId, String searchString, List<String> ticketStatus, List<Long> eventTicketTypeIds, String advanceFilterConditions) {

        StringBuilder queryBuilder = new StringBuilder();

        queryBuilder.append("SELECT COUNT(DISTINCT(userdetails.event_ticket_id)) FROM (");
        queryBuilder.append(" SELECT us.first_name, us.last_name, us.email, et.id AS event_ticket_id, tord.id AS order_id");
        queryBuilder.append(" FROM event_tickets AS et");
        queryBuilder.append(" JOIN ticketing_order AS tord ON et.ticket_order_id = tord.id");
        queryBuilder.append(" JOIN users AS us ON us.user_id = et.holder_user_id");
        queryBuilder.append(" JOIN ticket_holder_attributes tha on tha.id = et.ticket_holder_attributes_id");
        queryBuilder.append(" WHERE tord.event_id = :eventId");

        if (ticketStatus != null && !ticketStatus.isEmpty()) {
            queryBuilder.append(" AND et.ticket_status IN (:ticketStatus)");
        }
        if (eventTicketTypeIds != null && !eventTicketTypeIds.isEmpty()) {
            queryBuilder.append(" AND et.ticketing_type_id IN (:eventTicketTypeIds)");
        }
        if (StringUtils.isNotBlank(advanceFilterConditions)) {
            queryBuilder.append(" ").append(advanceFilterConditions);
        }
        queryBuilder.append(" AND tord.order_status IN ('PAID', 'UNPAID', 'PARTIAL', 'PROCESSING', 'PAYMENT_FAILED')");
        queryBuilder.append(" ) AS userdetails");
        queryBuilder.append(" WHERE (COALESCE(:searchString, '') = ''");
        queryBuilder.append(" OR userdetails.first_name LIKE :searchString");
        queryBuilder.append(" OR userdetails.last_name LIKE :searchString");
        queryBuilder.append(" OR userdetails.email LIKE :searchString");
        queryBuilder.append(" OR CONCAT(userdetails.first_name, ' ', userdetails.last_name) LIKE :searchString)");

        Query query = entityManager.createNativeQuery(queryBuilder.toString());
        query.setParameter("eventId", eventId);
        if (StringUtils.isNotBlank(searchString)) {
            query.setParameter("searchString", "%" + searchString + "%");
        } else {
            query.setParameter("searchString", "");
        }
        if (ticketStatus != null && !ticketStatus.isEmpty()) {
            query.setParameter("ticketStatus", ticketStatus);
        }
        if (eventTicketTypeIds != null && !eventTicketTypeIds.isEmpty()) {
            query.setParameter("eventTicketTypeIds", eventTicketTypeIds);
        }
        return ((Number) query.getSingleResult()).longValue();
    }

    private List<Long> getFilterTicketTypes(List<Long> filterTicketTypes) {
        if (CollectionUtils.isEmpty(filterTicketTypes)) {
            return null;
        }
        return filterTicketTypes;
    }

    private List<String> getFilterStatus(List<String> filterStatus) {
        if (CollectionUtils.isEmpty(filterStatus)) {
            return null;
        }
        return filterStatus;
    }

    private String prepareQuery(int page, int size, String sortBy, String sortDirection, String advanceFilterConditions) {
        long from = (long) page * size;
        String query = "select userdetails.user_id, COALESCE(userdetails.first_name,'') , COALESCE(userdetails.last_name,''), COALESCE(userdetails.email,''), userdetails.photo, userdetails.event_ticket_id,userdetails.order_id, userdetails.ticket_status, userdetails.entry_exit_status, userdetails.barcode_id   from " +
                "   (select us.*,et.id as event_ticket_id, tord.id as order_id , tord.order_status as status, et.ticket_status as ticket_status," +
                "            tord.ticketing_coupon_id as ticketing_coupon_id,tord.ticketing_access_code_id as ticketing_access_code_id,tord.tracking_link_id as tracking_link_id, " +
                "            tord.rec_source as rec_source, et.check_in_date as check_in_date, tord.order_date as order_date,tord.updated_at as updated_at," +
                "            et.ticketing_type_id as ticketing_type_id , et.holder_user_id as holder_user_id, et.data_type as data_type, et.entry_exit_status as entry_exit_status, et.barcode_id as barcode_id " + getSortColumn(sortBy) +
                "            from event_tickets as et " +
                "            join ticketing_order as tord on et.ticket_order_id=tord.id " + getTicketTypeTable(sortBy) +
                "            join users as us on us.user_id = et.holder_user_id " +
                "            join ticket_holder_attributes tha on tha.id = et.ticket_holder_attributes_id " +
                "            WHERE tord.event_id=:eventId" +
                "            AND (COALESCE(:eventTicketTypeIds) IS NULL OR et.ticketing_type_id IN (:eventTicketTypeIds))" +
                "            AND (COALESCE(:ticketStatus) IS NULL OR et.ticket_status in (:ticketStatus)) " +
                advanceFilterConditions +
                "            AND tord.order_status in ('PAID','UNPAID','PARTIAL','PROCESSING','PAYMENT_FAILED') ) AS userdetails " +
                "            WHERE (coalesce(:searchString ,'') = '' or (userdetails.first_name like :searchString or userdetails.last_name like :searchString or userdetails.email like :searchString or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE :searchString )) " +
                "            ORDER BY sortParam, userdetails.user_id " + sortDirection + " limit " + size + " offset " + from;
        log.info("prepareQueryByEventTicketStatusAndTicketTypes Custom Query {}", query);
        return query;
    }

    private String getTicketTypeTable(String sortBy) {
        return "ticket_type".equals(sortBy) ? " JOIN event_ticket_type ett on ett.id = et.ticketing_type_id " : STRING_EMPTY;
    }

    private String getSortColumn(String sortBy) {
        if (StringUtils.isNotBlank(sortBy)) {
            String[] attributeId = sortBy.split("_");
            if (attributeId != null && attributeId.length == 2 && (attributeId[1].equals(ColumnSelectionConstants.ColumnType.HOLDER.name()) || attributeId[1].equals(ColumnSelectionConstants.ColumnType.BUYER.name()))) {
                Optional<TicketHolderRequiredAttributes> attributeOptional = roTicketRequiresAttributesRepository.findById(Long.parseLong(attributeId[0]));
                if (attributeOptional.isPresent()) {
                    String attributeName = attributeOptional.get().getName();
                    if (attributeId[1].equals(ColumnSelectionConstants.ColumnType.HOLDER.name())) {
                        return ", JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '$.holder.attributes.\"" + attributeName + "\"')) as sortParam ";
                    } else {
                        return ", JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '$.purchaser.attributes.\"" + attributeName + "\"')) as sortParam ";
                    }
                } else {
                    throw new NotFoundException(NotFoundException.NotFound.HOLDER_ATTRIBUTE_NOT_FOUND);
                }
            }
        }
        String sortColumn;
        switch (sortBy) {
            case FIRST_NAME:
            case LAST_NAME:
            case EMAIL:
                sortColumn = ", us." + getSortBy(sortBy) + " as sortParam ";
                break;
            case ColumnSelectionConstants.TICKET_TYPE:
                sortColumn = ", ett.ticket_type_name as sortParam ";
                break;
            case ColumnSelectionConstants.LAST_EVENT_REGISTERED_UTM_SOURCE:
                sortColumn = ", JSON_UNQUOTE(JSON_EXTRACT(tord.rec_source, '$.utmSource')) as sortParam ";
                break;
            case ColumnSelectionConstants.LAST_EVENT_REGISTERED_UTM_MEDIUM:
                sortColumn = ", JSON_UNQUOTE(JSON_EXTRACT(tord.rec_source, '$.utmMedium')) as sortParam ";
                break;
            case ColumnSelectionConstants.LAST_EVENT_REGISTERED_UTM_CAMPAIGN:
                sortColumn = ", JSON_UNQUOTE(JSON_EXTRACT(tord.rec_source, '$.utmCampaign')) as sortParam ";
                break;
            case ColumnSelectionConstants.LAST_EVENT_REGISTERED_UTM_REFERRER:
                sortColumn = ", JSON_UNQUOTE(JSON_EXTRACT(tord.rec_source, '$.utmReferrer')) as sortParam ";
                break;
            default:
                sortColumn = ", us.first_name as sortParam ";
                break;
        }
        return sortColumn;
    }

    private String getSortBy(String sortBy) {
        switch (StringUtils.trimToEmpty(sortBy)) {
            case ColumnSelectionConstants.FIRST_NAME:
                return FIRST_NAME_UNDERSCORE;
            case ColumnSelectionConstants.LAST_NAME:
                return LAST_NAME_UNDERSCORE;
            default:
                return sortBy;
        }
    }

    private String getOrderByColumnName(String sortBy) {
        switch (StringUtils.trimToEmpty(sortBy)) {
            case FIRST_NAME:
            case LAST_NAME:
            case EMAIL:
                return ", us." + getSortBy(sortBy) + " as sortParam ";
            default:
                return ", '' ";
        }
    }

    @Override
    public String buildQueryWithAdvanceFilterAttributes(AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsAdvanceFilterDto,
                                                        AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsStaticAdvanceFilterDto,Long eventId) {
        if (attendeeAnalyticsAdvanceFilterDto == null && attendeeAnalyticsStaticAdvanceFilterDto == null) {
            return STRING_EMPTY;
        }
        List<Map<String, String>> attributesList = getAttributesList(attendeeAnalyticsAdvanceFilterDto, attendeeAnalyticsStaticAdvanceFilterDto);
        AudienceConstants.ConditionalOperator conditionalOperator = getOperator(attendeeAnalyticsAdvanceFilterDto, attendeeAnalyticsStaticAdvanceFilterDto);
        if (attributesList.isEmpty()) {
            return STRING_EMPTY;
        }
        List<String> conditions = new ArrayList<>();
        for (Map<String, String> attributeMap : attributesList) {
            String attributeName = attributeMap.get("columnName");
            String operator = attributeMap.get(OPERATOR).toUpperCase();
            String value = attributeMap.get(COLUMN_VALUE);
            String jsonType = attributeMap.get(JSON_TYPE);
            String valueType = attributeMap.get(VALUE_TYPE);

            if (jsonType == null) {
                continue;
            }

            String condition = STRING_EMPTY;
            value = "'" + value.replace("'", "''") + "'";
            if ("holder".equalsIgnoreCase(jsonType) || "purchaser".equalsIgnoreCase(jsonType)) {
                String jsonPathPrefix = "$." + jsonType.toLowerCase() + ".attributes.\"";
                if ("IS_EMPTY".equals(operator)) {
                    String jsonPath = "JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPathPrefix + attributeName + "\"'))";
                    // value is null/empty
                    condition = "(" + jsonPath + " IS NULL OR " + jsonPath + " = ''" + ")";
                } else if ("IS_NOT_EMPTY".equals(operator)) {
                    String jsonPath = "JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPathPrefix + attributeName + "\"'))";
                    // value is  not null/empty
                    condition = "(" + jsonPath + " IS NOT NULL AND " + jsonPath + " != ''" + ")";
                } else if (VALUE_TYPE_STARING.equalsIgnoreCase(valueType) || AttributeValueType.MULTIPLE_CHOICE.getType().equalsIgnoreCase(valueType) || AttributeValueType.DROPDOWN.getType().equalsIgnoreCase(valueType)) {
                    condition = "JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPathPrefix + attributeName + "\"')) " + operator + " " + value;
                } else if (DATE.equalsIgnoreCase(valueType)) {
                    condition = "STR_TO_DATE(TRIM(IFNULL(JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPathPrefix + attributeName + "\"')), '')), '%m/%d/%Y') "
                            + operator + " STR_TO_DATE(" + value + ", '%m/%d/%Y')";
                } else if (AttributeValueType.CONDITIONAL_QUE.getType().equals(valueType)) {
                    String nestedQuesPrefix = "$." + jsonType.toLowerCase() + ".nestedQuestions";
                    condition = "JSON_SEARCH(JSON_EXTRACT(tha.json_value, '" + nestedQuesPrefix + "[*].name'), 'one', '" + attributeName + "') IS NOT NULL " +
                            "AND JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, CONCAT('" + nestedQuesPrefix + "[', " +
                            "CAST(REGEXP_SUBSTR(JSON_UNQUOTE(JSON_SEARCH(JSON_EXTRACT(tha.json_value, '" + nestedQuesPrefix + "[*].name'), 'one', '" + attributeName + "')), '[0-9]+') AS UNSIGNED), " +
                            "'].value'))) " + operator + " " + value;
                } else if (AttributeValueType.NUMBER.getType().equalsIgnoreCase(valueType)) {
                    if (CELL_PHONE.equals(attributeName)) {
                        String[] splitValues = value.split("-");
                        if (splitValues.length == 2) {
                            String cellNumber = splitValues[1].replace("'", STRING_EMPTY);
                            attributeName = "phoneNumber";
                            String jsonPath = "JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPathPrefix + attributeName + "\"'))";
                            if (AttendeeAnalyticsAdvanceFilterDto.AdvanceFilterOperator.IS.getOperatorSymbol().equals(operator)) {
                                condition = "(" + jsonPath + " " + operator + " '" + cellNumber + "' AND " + jsonPath + " IS NOT NULL)";
                            } else if (AttendeeAnalyticsAdvanceFilterDto.AdvanceFilterOperator.IS_NOT.getOperatorSymbol().equals(operator)) {
                                condition = "(" + jsonPath + " " + operator + " '" + cellNumber + "' OR " + jsonPath + " IS NULL)";
                            } else {
                                condition = jsonPath + " " + operator + " '" + cellNumber + "'";
                            }
                        }
                    } else if (AGE.equals(attributeName)) {
                        String jsonPath = "CAST(JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPathPrefix + attributeName + "\"')) AS UNSIGNED)";
                        condition = jsonPath + " " + operator + " " + value;
                    } else {
                        condition = "JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPathPrefix + attributeName + "\"')) " + operator + " " + value;
                    }
                } else if (AttributeValueType.INTEREST.getType().equals(valueType)) {
                    String jsonPath = "$.holder.attributes.Interest";
                    List<String> interestTags = Arrays.stream(value.replaceAll("^'|'$", "").split("\\|"))
                            .map(String::trim)
                            .collect(Collectors.toList());

                    List<String> interestConditions = new ArrayList<>();
                    if (AttendeeAnalyticsAdvanceFilterDto.AdvanceFilterOperator.IS.getOperatorSymbol().equals(operator)) {
                        for (String tag : interestTags) {
                            interestConditions.add("JSON_SEARCH(JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPath + "')), 'one', '" + tag + "') IS NOT NULL");
                        }
                        condition = "(" + String.join(" AND ", interestConditions) + ")";
                    } else if (AttendeeAnalyticsAdvanceFilterDto.AdvanceFilterOperator.IS_NOT.getOperatorSymbol().equals(operator)) {
                        for (String tag : interestTags) {
                            interestConditions.add("(NOT JSON_CONTAINS(JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, '" + jsonPath + "')), " +
                                    "JSON_OBJECT('name', '" + tag + "')) OR JSON_EXTRACT(tha.json_value, '" + jsonPath + "') IS NULL)");
                        }
                        condition = "(" + String.join(" AND ", interestConditions) + ")";
                    }
                }
            } else if (ColumnSelectionConstants.DISCLAIMER.equalsIgnoreCase(jsonType)) {
                if (operator.equals(STRING_IS_EMPTY)) {
                    condition = "tord.disclaimer" + " IS NULL OR tord.disclaimer = ''";
                } else if (operator.equals(STRING_IS_NOT_EMPTY)) {
                    condition = "tord.disclaimer" + " IS NOT NULL AND tord.disclaimer != ''";
                } else {
                    condition = "tord.disclaimer" + operator + value;
                }
            } else if (ORDER_STATUS.equalsIgnoreCase(attributeName)) {
                condition = statusConditions("tord.order_status", operator, value);
            } else if (TICKET_STATUS.equalsIgnoreCase(attributeName)) {
                condition = statusConditions("et.ticket_status", operator, value);
            } else if (PAYMENT_STATUS.equalsIgnoreCase(attributeName)) {
                condition = statusConditions("et.ticket_payment_status", operator, value);
            } else if (HEADER_UTM_SOURCE.equalsIgnoreCase(attributeName)) {
                condition = getUTMFieldCondition(operator, value, "utmSource");
            } else if (HEADER_UTM_MEDIUM.equalsIgnoreCase(attributeName)) {
                condition = getUTMFieldCondition(operator, value, "utmMedium");
            } else if (HEADER_UTM_CAMPAIGN.equalsIgnoreCase(attributeName)) {
                condition = getUTMFieldCondition(operator, value, "utmCampaign");
            } else if (HEADER_UTM_REFERRER.equalsIgnoreCase(attributeName)) {
                condition = getUTMFieldCondition(operator, value, "utmReferrer");
            } else if (STRING_CHECK_IN_DATE.equalsIgnoreCase(attributeName)) {
                condition = getDateCondition(operator, attributeMap, "et.check_in_date");
            } else if (STRING_REGISTRATION_TIME.equalsIgnoreCase(attributeName)) {
                condition = getDateCondition(operator, attributeMap, "tord.order_date");
            } else if (STRING_LAST_UPDATE_TIME.equalsIgnoreCase(attributeName)) {
                condition = getDateCondition(operator, attributeMap, "tord.updated_at");
            } else if (STRING_DISCOUNT_CODE.equalsIgnoreCase(attributeName)) {
                condition = discountOrAccessCodeOrTrackingLinkCondition(value, operator, "tord.ticketing_coupon_id");
            } else if (STRING_ACCESS_CODE.equalsIgnoreCase(attributeName)) {
                condition = discountOrAccessCodeOrTrackingLinkCondition(value, operator, "tord.ticketing_access_code_id");
            } else if (STRING_TRACKING_LINK.equalsIgnoreCase(attributeName)) {
                condition = discountOrAccessCodeOrTrackingLinkCondition(value, operator, "tord.tracking_link_id");
            } else if (STRING_ADD_ONS.equalsIgnoreCase(attributeName)) {
                condition = addOnsCondition(value, operator);
            } else if (STRING_SESSION_REGISTRATION.equalsIgnoreCase(attributeName)) {
                condition = sessionRegistrationCondition(value, operator, eventId);
            }

            if (!condition.isEmpty()) {
                conditions.add("(" + condition + ")");
            }
        }

        if (conditions.isEmpty()) {
            return STRING_EMPTY;
        }

        // Join conditions based on the operator
        String logicalOperator = (conditionalOperator == AudienceConstants.ConditionalOperator.ANY) ? " OR " : " AND ";
        return "AND (" + String.join(logicalOperator, conditions) + ")";
    }

    private String sessionRegistrationCondition(String value, String operator, Long eventId) {
        value = value.substring(1, value.length() - 1); // remove single quotes from value
        value = value.replace("|",",");
        Map<Long, List<Integer>> eventsUserIdsEventsMap = roUserSessionService.findAllSessionUserIdsByEventId(eventId);
        if (STRING_IS_EMPTY.equalsIgnoreCase(operator) || STRING_IS_NOT_EMPTY.equalsIgnoreCase(operator)) {
            Set<Integer> uniqueUserIds = eventsUserIdsEventsMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());
            if (!uniqueUserIds.isEmpty()) {
                String commaSeparatedUserIds = uniqueUserIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                if (STRING_IS_EMPTY.equalsIgnoreCase(operator)) {
                    return "et.holder_user_id NOT IN (" + commaSeparatedUserIds + ")";
                }
                return "et.holder_user_id IN (" + commaSeparatedUserIds + ")";
            } else {
                if (STRING_IS_NOT_EMPTY.equalsIgnoreCase(operator)) {
                    return "et.holder_user_id IS NULL OR et.holder_user_id=0";
                }
            }

        } else if (STRING_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) || STRING_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            List<Integer> userIdsList = getUserIdsBySessionIds(value, eventsUserIdsEventsMap,operator);
            if (!userIdsList.isEmpty()) {
                String userIdsString = userIdsList.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                return "et.holder_user_id IN (" + userIdsString + ")";
            }
            return "et.holder_user_id IS NULL OR et.holder_user_id=0";
        } else if (STRING_NOT_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            List<Integer> userIdsList = getUserIdsByNotInSelectedSessionId(value, eventsUserIdsEventsMap, operator);
            if (!userIdsList.isEmpty()) {
                String userIdsString = userIdsList.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                return "et.holder_user_id NOT IN (" + userIdsString + ")";
            }
            return STRING_EMPTY;
        }
        return STRING_EMPTY;
    }

    private List<Integer> getUserIdsBySessionIds(String sessionIdString, Map<Long, List<Integer>> eventsUserIdsEventsMap,String operator) {
        if (STRING_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            sessionIdString = sessionIdString.substring(1, sessionIdString.length() - 1);
        }
        List<Long> sessionIdList = Arrays.stream(sessionIdString.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Set<Integer> uniqueUserIds = new HashSet<>();
        eventsUserIdsEventsMap.forEach((key, value) -> {
            if (sessionIdList.contains(key)) {
                uniqueUserIds.addAll(value);
            }
        });
        return new ArrayList<>(uniqueUserIds);
    }

    private List<Integer> getUserIdsByNotInSelectedSessionId(String sessionIdString, Map<Long, List<Integer>> eventsUserIdsEventsMap,String operator) {
        if (STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            sessionIdString = sessionIdString.substring(1, sessionIdString.length() - 1);
        }
        List<Long> sessionIdList = Arrays.stream(sessionIdString.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Set<Integer> uniqueSessionUserIds = new HashSet<>();
        eventsUserIdsEventsMap.forEach((key, value) -> {
            if (sessionIdList.contains(key)) {
                uniqueSessionUserIds.addAll(value);
            }
        });
        Set<Integer> uniqueUserIdsNotInGivenSessionIds = new HashSet<>();
        eventsUserIdsEventsMap.forEach((key, value) -> {
            if (!sessionIdList.contains(key)) {
                uniqueUserIdsNotInGivenSessionIds.addAll(value);
            }
        });

        List<Integer> finalList = uniqueSessionUserIds.stream()
                .filter(id -> !uniqueUserIdsNotInGivenSessionIds.contains(id))
                .collect(Collectors.toList());
        return new ArrayList<>(finalList);
    }

    private String discountOrAccessCodeOrTrackingLinkCondition(String value, String operator, String fieldName) {
        value = value.substring(1, value.length() - 1); // remove single quotes from value
        value = value.replace("|", ","); // replace pipe with comma for multiple values
        if (STRING_LIKE_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            value = value.replace("%", "");
        }
        if (STRING_IS_EMPTY.equalsIgnoreCase(operator)) {
            return "(" + fieldName + " IS NULL OR " + fieldName + " = 0)";
        } else if (STRING_IS_NOT_EMPTY.equalsIgnoreCase(operator)) {
            return "(" + fieldName + " IS NOT NULL AND " + fieldName + " != 0)";
        } else if (STRING_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) ||
                STRING_LIKE_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            if (STRING_NOT_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
                return fieldName + " IS NULL OR " + fieldName + " NOT IN (" + value + ")";
            }
            return fieldName + " IS NOT NULL AND " + fieldName + " IN ( " + value + ")";
        }
        return STRING_EMPTY;
    }

    private String addOnsCondition(String value, String operator) {
        value = value.substring(1, value.length() - 1); // remove single quotes from value
        value = value.replace("|", ","); // replace pipe with comma for multiple values
        if (STRING_LIKE_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            value = value.replace("%", "");
        }
        if (STRING_IS_EMPTY.equalsIgnoreCase(operator)) {
            return "et.data_type!='ADDON'";
        } else if (STRING_IS_NOT_EMPTY.equalsIgnoreCase(operator)) {
            return "et.data_type='ADDON'";
        } else if (STRING_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) ||
                STRING_LIKE_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
            if (STRING_NOT_EQUAL_TO_OPERATOR.equalsIgnoreCase(operator) || STRING_NOT_LIKE_OPERATOR.equalsIgnoreCase(operator)) {
                return "et.ticketing_type_id NOT IN (" + value + ")";
            }
            return "et.ticketing_type_id IN (" + value + ")";
        }
        return STRING_EMPTY;
    }

    private String statusConditions(String fieldName, String operator, String value) {
        if (STRING_IS_EMPTY.equals(operator)) {
            return fieldName + " IS NULL OR " + fieldName + " = ''";
        } else if (STRING_IS_NOT_EMPTY.equals(operator)) {
            return fieldName + " IS NOT NULL AND " + fieldName + " != ''";
        } else {
            return fieldName + " " + operator + " " + value;
        }
    }

    private String getUTMFieldCondition(String operator, String value, String variableName) {
        String condition;
        if (STRING_IS_EMPTY.equals(operator)) {
            String jsonPath = "JSON_UNQUOTE(JSON_EXTRACT(tord.rec_source,'$." + variableName + "')) ";
            // value is null/empty
            condition = "(" + jsonPath + " IS NULL OR " + jsonPath + " = ''" + ")";
        } else if (STRING_IS_NOT_EMPTY.equals(operator)) {
            String jsonPath = "JSON_UNQUOTE(JSON_EXTRACT(tord.rec_source,'$." + variableName + "')) ";
            // value is null/empty
            condition = "(" + jsonPath + " IS NOT NULL AND " + jsonPath + " != ''" + ")";
        } else {
            condition = "JSON_UNQUOTE(JSON_EXTRACT(tord.rec_source,'$." + variableName + "')) " + operator + " " + value;
        }
        return condition;
    }

    private String getFormatedDate(String value) {
        SimpleDateFormat formatter = new SimpleDateFormat(DATE_FORMAT_ONLY_MONTH); // '06/10/2025'
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMATE);
        try {
            return sdf.format(formatter.parse(value));
        } catch (ParseException e) {
            return null;
        }
    }

    private String getDateCondition(String operator, Map<String, String> attributeMap, String fieldName) {
        String condition = STRING_EMPTY;
        if (STRING_IS_BEFORE.equals(operator)) {
            String formatedDate = getFormatedDate(attributeMap.get(COLUMN_VALUE));
            if (formatedDate != null) {
                condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) < '" + formatedDate + "'";
            }
        } else if (STRING_IS_AFTER.equals(operator)) {
            String formatedDate = getFormatedDate(attributeMap.get(COLUMN_VALUE));
            if (formatedDate != null) {
                condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) > '" + formatedDate + "'";
            }
        } else if (STRING_IS_EQUAL_TO.equals(operator)) {
            String formatedDate = getFormatedDate(attributeMap.get(COLUMN_VALUE));
            if (formatedDate != null) {
                condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) = '" + formatedDate + "'";
            }
        } else if (STRING_IS_EMPTY.equals(operator)) {
            condition = fieldName + " IS NULL";
        } else if (STRING_IS_NOT_EMPTY.equals(operator)) {
            condition = fieldName + " IS NOT NULL";
        } else if (STRING_IS_BETWEEN.equals(operator)) {
            String dateValues = attributeMap.get(COLUMN_VALUE);
            List<String> dateList = Arrays.stream(dateValues.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
            String startDateString = dateList.get(0);
            String endDateString = dateList.get(1);
            String startDate = getFormatedDate(startDateString);
            String endDate = getFormatedDate(endDateString);
            if (startDate != null && endDate != null) {
                condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) between '" + startDate + "' and '" + endDate + "'";
            }
        } else if (STRING_TODAY.equals(attributeMap.get(COLUMN_VALUE))) {
            condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) = DATE(NOW())";
        } else if (STRING_YESTERDAY.equals(attributeMap.get(COLUMN_VALUE))) {
            condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) = DATE_SUB(DATE(NOW()), INTERVAL 1 DAY)";
        } else if (STRING_THIS_WEEK.equals(attributeMap.get(COLUMN_VALUE))) {
            condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) >= DATE_SUB(DATE(NOW()), INTERVAL WEEKDAY(NOW()) DAY) " +
                    "AND DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) <= DATE_ADD(DATE_SUB(DATE(NOW()), INTERVAL WEEKDAY(NOW()) DAY), INTERVAL 6 DAY)";
        } else if (STRING_LAST_WEEK.equals(attributeMap.get(COLUMN_VALUE))) {
            condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) >= DATE_SUB(DATE(NOW()), INTERVAL 7 DAY)";
        } else if (STRING_LAST_MONTH.equals(attributeMap.get(COLUMN_VALUE))) {
            condition = "DATE(DATE_ADD(" + fieldName + ", INTERVAL 0 DAY)) >= DATE_SUB(DATE(NOW()), INTERVAL 30 DAY)";
        } else {
            condition = STRING_EMPTY;
        }
        return condition;
    }

    private List<Map<String, String>> getAttributesList(AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsAdvanceFilterDto,
                                                        AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsStaticAdvanceFilterDto) {
        List<Map<String, String>> attributesList = new ArrayList<>();
        if (attendeeAnalyticsAdvanceFilterDto != null) {
            attributesList.addAll(attendeeAnalyticsAdvanceFilterDto.getAttributes());
        }
        if (attendeeAnalyticsStaticAdvanceFilterDto != null) {
            attributesList.addAll(attendeeAnalyticsStaticAdvanceFilterDto.getAttributes());
        }
        return attributesList;
    }

    private AudienceConstants.ConditionalOperator getOperator(AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsAdvanceFilterDto,
                                                              AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsStaticAdvanceFilterDto) {
        if (attendeeAnalyticsAdvanceFilterDto != null) {
            return attendeeAnalyticsAdvanceFilterDto.getOperator();
        } else {
            return attendeeAnalyticsStaticAdvanceFilterDto.getOperator();
        }
    }
}
