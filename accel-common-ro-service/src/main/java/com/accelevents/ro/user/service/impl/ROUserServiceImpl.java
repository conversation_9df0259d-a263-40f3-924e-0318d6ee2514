package com.accelevents.ro.user.service.impl;

import com.accelevents.common.dto.EventEndInfoDto;
import com.accelevents.domain.AccelEventsPhoneNumber;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.dto.*;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.repo.ROUserRepo;
import com.accelevents.ro.user.repo.ROUserRoleRepo;
import com.accelevents.ro.user.service.ROPhoneNumberService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.accelevents.enums.UserRole.*;
import static com.accelevents.exceptions.NotFoundException.UserNotFound.USER_NOT_FOUND;

@Service
public class ROUserServiceImpl implements ROUserService {

    private static final Logger log = LoggerFactory.getLogger(ROUserServiceImpl.class);

    @Autowired
    private ROUserRoleRepo roUserRoleRepo;
    @Autowired
    private ROUserRepo roUserRepo;
    @Autowired
    private ROPhoneNumberService roPhoneNumberService;
    @Autowired
    @Lazy
    private PasswordEncoder passwordEncoder;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;

    @Override
    public Optional<User> findByIdOp(Long userId) {
        return roUserRepo.findById(userId);
    }

    @Override
    public Optional<User> getUserById(Long userId) {
        if(userId == null){
            throw new NotFoundException(USER_NOT_FOUND);
        }

        return roUserRepo.findById(userId);
    }

    @Override
    public List<String> getUserRoles(User user) {
        return roUserRoleRepo.findRoleByUserid(user.getUserId());
    }

    @Override
    public boolean isSuperAdminUser(User user) {
        if (user != null) {
            List<String> userRoles = this.getUserRoles(user);
            return this.isSuperAdminUser(user, userRoles);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }

    @Override
    public boolean isSuperAdminUser(User user, List<String> userRoles){
        return this.verifyUserRoleExists(user, userRoles, ROLE_SUPERADMIN);
    }

    private boolean verifyUserRoleExists(User user, List<String> userRoles, com.accelevents.enums.UserRole roleToVerify){
        if (user != null) {
            boolean result = false;
            if (userRoles.contains(roleToVerify.name())) {
                result = true;
            }
            return result;
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }

    @Override
    public boolean isWhiteLabelAdminUser(User user) {
        boolean result = false;
        List<String> userRoles = this.getUserRoles(user);
        if (userRoles.contains(ROLE_WHITELABELADMIN.name())) {
            result = true;
        }
        return result;
    }

    @Override
    public boolean isEventCoordinatorUser(User user) {
        boolean result = false;
        List<String> userRoles = this.getUserRoles(user);
        if (userRoles.contains(ROLE_EVENT_COORDINATOR.name())) {
            result = true;
        }
        return result;
    }

    @Override
    public boolean isEventBudgetOwnerUser(User user) {
        boolean result = false;
        if(roUserRoleRepo.existsByUseridAndRole(user.getUserId(), ROLE_EVENT_BUDGET_OWNER.name())) {
            result = true;
        }
        return result;
    }

    @Override
    public boolean isEventPlannerUser(User user) {
        boolean result = false;
        if(roUserRoleRepo.existsByUseridAndRole(user.getUserId(), ROLE_EVENT_PLANNER.name())) {
            result = true;
        }
        return result;
    }

    @Override
    public UserInfoDto extractUserInfo(Long userId) {
        Optional<User> userOptional = roUserRepo.findById(userId);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            return getUserInfoFromUser(user);
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @Override
    public UserInfoDto extractUserInfo(User user) {
        return getUserInfoFromUser(user);
    }

    private UserInfoDto getUserInfoFromUser(User user) {
        UserInfoDto userInfo = new UserInfoDto();
        userInfo.setCountryCode(user.getCountryCode());
        userInfo.setEmail(user.getEmail());
        userInfo.setPhonenumber(user.getPhoneNumber());
        userInfo.setMarketingOptIn(user.isMarketingOptIn() != null ? user.isMarketingOptIn() : false);
        userInfo.setUserId(user.getUserId());
        userInfo.setFirstName(user.getFirstName());
        userInfo.setLastName(user.getLastName());
        userInfo.setUserProfilePhoto(user.getPhoto());
        userInfo.setVirtualBackgroundSetting(user.getVirtualBackgroundSettings()!= null ?
                user.getVirtualBackgroundSettings() : null);
        userInfo.setVirtualBackgroundDetails(user.getVirtualBackgroundDetails()!=null ?
                user.getVirtualBackgroundDetails() : null);
        return userInfo;
    }

    @Override
    public AccessTokenModel getUserDetailForLogin(UserLoginDto loginDto) {
        Optional<User> objUserOptional = this.getUserByPhoneOrEmail(loginDto.getUsername());
        if (objUserOptional.isPresent()) {
            User objUser = objUserOptional.get();
//			if (StringUtils.isBlank(objUser.getPassword())) {
//				objUser.setPassword(this.passwordEncoder.encode(loginDto.getPassword()));
//				this.save(objUser);
//			}
            if (this.passwordEncoder.matches(loginDto.getPassword(), objUser.getPassword())) {
                List<String> userRoles = this.getUserRoles(objUser);
                // put all user role in list
                if (null != userRoles && !userRoles.isEmpty()) {
                    Event event = roEventService.getEventByHostUserAndMostRecentEventId(objUser);

                    return new AccessTokenModel(loginDto.getUsername(), loginDto.getPassword(), userRoles, objUser,
                            loginDto.isRememberme(), event, roStaffService.getStaffRole(event, objUser));
                } else {
                    throw new NotFoundException(USER_NOT_FOUND);
                }
            } else {
                throw new AuthorizationException(Constants.PASSWORD_NOT_MATCH);
            }
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @Override
    public Optional<User> getUserByPhoneOrEmail(String phoneOrEmail) {
        if (roPhoneNumberService.isAePhoneNumber(phoneOrEmail)) {
            log.info("getUserByPhoneOrEmail|phoneNumber|{}",phoneOrEmail);
            AccelEventsPhoneNumber ae = roPhoneNumberService.parseIncomingSender(phoneOrEmail);
            return getUserByAEPhoneNumber(ae);
        } else {
            return getUserByEmail(phoneOrEmail);
        }
    }

    @Override
    public Optional<User> findOpUserByEmail(String email) {
        return roUserRepo.findUserByEmail(email);
    }

    @Override
    public Optional<User> getUserByEmail(String email) {
        try {
            log.info("ROUserServiceImpl | getUserByEmail | email | {}", email);//NOSONAR
            if(StringUtils.isBlank(email)) return Optional.empty();
            if(GeneralUtils.isStringContainsUniCode(email)){
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_EMAIL);
            }
            return roUserRepo.findUserByEmail(email);
        } catch (IncorrectResultSizeDataAccessException ex) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.MORE_THEN_ONE_USER_EXIST_WITH_THIS_EMAIL);
        }
    }

    @Override
    public List<User> findUserByPhoneNumberCountryCodeList(long phoneNumber, CountryCode countryCode) {
        return roUserRepo.findUserByPhoneNumberCountryCodeList(phoneNumber, countryCode);
    }

    @Override
    public Optional<User> getUserByAEPhoneNumber(AccelEventsPhoneNumber phoneNumber) {
        log.info("getUserByAEPhoneNumber|{}",phoneNumber);
        if (phoneNumber.getPhoneNumber() != 0) {
            if (CountryCode.US.equals(phoneNumber.getCountryCode())
                    || CountryCode.CA.equals(phoneNumber.getCountryCode())) {
                List<User> userList = roUserRepo.findUserByPhoneNumberAndCountryCodeoneOrCountryCodetwo(
                        phoneNumber.getPhoneNumber(), CountryCode.US, CountryCode.CA);
                if(!CollectionUtils.isEmpty(userList)) {
                    return Optional.ofNullable(userList.get(0));
                } else {
                    return Optional.ofNullable(null);
                }
            } else {
                List<User> userList = this.findUserByPhoneNumberCountryCodeList(phoneNumber.getPhoneNumber(),phoneNumber.getCountryCode());
                if (!CollectionUtils.isEmpty(userList)){
                    return Optional.ofNullable(userList.get(0));
                }else {
                    return Optional.ofNullable(null);
                }
            }
        } else {
            return Optional.ofNullable(null);
        }
    }

    @Override
    public AccessTokenModel tokenLogin(String token) {
        Long userId = SecurityUtils.decodeUseridTest(token);
        User user = this.roUserRepo.findById(userId).orElse(null);
        if (user != null) {
            return getUserDetailForSMS(user);
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @Override
    public AccessTokenModel getUserDetailForSMS(User user) {
        return  getUserDetailForSMS(user,null);
    }

    @Override
    public AccessTokenModel getUserDetailForSMS(User user, String eventUrl) {
        List<String> userRoles = null != user ? this.getUserRoles(user) : Collections.emptyList();
        // put all user role in list
        if ( user != null &&!CollectionUtils.isEmpty(userRoles)) {
            Event event;
            if (StringUtils.isNotBlank(eventUrl)) {
                event = roEventService.getEventByURL(eventUrl);
            } else {
                event = roEventService.getEventByHostUserAndMostRecentEventId(user);
            }
            return new AccessTokenModel(
                    StringUtils.isBlank(user.getEmail()) ? user.getDisplayNumber() : user.getEmail(),
                    user.getPassword(), userRoles, user, false, event,
                    roStaffService.getStaffRole(event, user));
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @Override
    @Transactional
    public AccessTokenModel getWhiteLabelUserDetailForLogin(UserLoginDto loginDto, String whiteLabelURL, boolean skipPassword, boolean ssoLogin) {
        Optional<User> objUserOptional = this.getUserByPhoneOrEmail(loginDto.getUsername());
        if (objUserOptional.isPresent()) {
            User objUser = objUserOptional.get();
            if ((skipPassword && ssoLogin) || this.passwordEncoder.matches(loginDto.getPassword(), objUser.getPassword())) {
                if(skipPassword && ssoLogin) loginDto.setPassword(null);
                Optional<WhiteLabel> whiteLabelOptional = roWhiteLabelService.findWhiteLabelByUrlOp(whiteLabelURL);
                if(whiteLabelOptional.isPresent()) {
                    List<String> userRoles = this.getUserRoles(objUser);
                    if (null != userRoles && !userRoles.isEmpty()) {
                        Event event = roEventService.getEventByHostUserAndMostRecentEventId(objUser);
                        log.info("getWhiteLabelUserDetailForLogin | host event {}", event != null ? event.getEventId(): null);
                        return new AccessTokenModel(loginDto.getUsername(), loginDto.getPassword(), userRoles, objUser,
                                loginDto.isRememberme(), event, roStaffService.findStaffRoleByUserAndWhiteLabel(objUser, whiteLabelOptional.get()),
                                whiteLabelOptional.get().getId(), null);
                    } else {
                        throw new NotFoundException(USER_NOT_FOUND);
                    }
                }else{
                    throw new NotFoundException(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND);
                }
            } else {
                throw new AuthorizationException(Constants.PASSWORD_NOT_MATCH);
            }
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @Override
    public User findByEmail(String email) {
        return findByEmailOrCellNumberAndCountryCode(email, 0, null);
    }

    @Override
    public User findByPhone(long phonenumber, CountryCode countryCode) {
        return findByEmailOrCellNumberAndCountryCode(null, phonenumber, countryCode);
    }

    @Override
    public User findByEmailOrCellNumberAndCountryCode(String email, long phonenumber, CountryCode countryCode) {
        log.info("getUserByAEPhoneNumber|countryCode{}||phoneNumber{}||email{}|",countryCode,phonenumber,email);
        Optional<User> optUser = Optional.empty();
        if (StringUtils.isNotBlank(email)) {
            optUser = this.roUserRepo.findUserByEmail(email);
        }
        if(optUser.isPresent()){
            log.info("getUserByAEPhoneNumber User found by email {}", optUser.get().getEmail());
            return optUser.get();
        }else if(phonenumber > 0){
            return this.roUserRepo.findUserByPhoneNumberAndCountryCode(phonenumber, countryCode);
        } else {
            return null;
        }
    }

    @Override
    public Optional<User> findUserByCountryCodeAndPhoneNumber(long phoneNumber, CountryCode countryCode) {
        log.info("findUserByCountryCodeAndPhoneNumber{}||phoneNumber{}|",countryCode,phoneNumber);
        return Optional.ofNullable(roUserRepo.findUserByPhoneNumberAndCountryCode(phoneNumber, countryCode));
    }

    @Override
    public boolean isDuplicatePhoneNumber(AccelEventsPhoneNumber phoneNumber){
        log.info("ROUserServiceImpl | isDuplicatePhoneNumber phoneNumber {}",phoneNumber);
        List <User> userList = roUserRepo.findUserByPhoneNumberAndCountryCodeoneOrCountryCodetwo(phoneNumber.getPhoneNumber(),phoneNumber.getCountryCode(),phoneNumber.getCountryCode()) ;
        return userList.size() > 1;
    }

    @Override
    public boolean isSalesSuperAdmin(User user, List<String> userRoles){
        return this.verifyUserRoleExists(user, userRoles, ROLE_SALES_SUPER_ADMIN);
    }

    @Override
    public boolean isChargebeeConfigAdminRole(User user , List<String> userRoles){
        return this.verifyUserRoleExists(user, userRoles, ROLE_CHARGEBEE_CONFIG_ADMIN);
    }

    @Override
    public Optional<User> findByEmailAndSignUpDateAfter(String email, Date signUpDate) {
        return roUserRepo.findByEmailAndSignUpDateAfter(email, signUpDate);
    }

    @Override
    public List<String> findUniqueCompanyListByEventId(Long eventId, String searchString, int offset, int limit) {
        return roUserRepo.findUniqueCompanyListByEventId(eventId, searchString, offset, limit);
    }

    @Override
    public Long countUniqueCompanyListByEventId(Long eventId, String searchString) {
        return roUserRepo.countUniqueCompanyListByEventId(eventId, searchString);
    }

    @Override
    public List<String> findUniqueTitleListByEventId(Long eventId, String searchString, int offset, int limit) {
        return roUserRepo.findUniqueTitleListByEventId(eventId, searchString, offset, limit);
    }

    @Override
    public Long countUniqueTitleListByEventId(Long eventId, String searchString) {
        return roUserRepo.countUniqueTitleListByEventId(eventId, searchString);
    }

    @Override
    public List<User> getListOfUsersByUserIds(List<Long> userList) {
        return CollectionUtils.isEmpty(userList) ? Collections.emptyList() : roUserRepo.getListOfUsersByUserIds(userList);
    }

    @Override
    public List<User> findAllAttendeesByEvent(Long eventId) {
        return roUserRepo.findAllAttendeesByEvent(eventId);
    }

    @Override
    public List<User> getUsersByUserIds(List<Long> userIds) {
        return roUserRepo.getUsersByUserIdIn(userIds);
    }

    @Override
    public DataTableResponse getUserEventsInfo(User user, String source, String eventStatus, String searchString, Date searchDate,PageSizeSearchObj pageSizeSearchObj) {
        List<EventEndInfoDto> userEventInfo = new ArrayList<>();

        // Get paginated events with all filtering applied at query level
        Page<Object[]> eventsPage = roEventService.getAllEventsForUserWithPagination(user, searchString, pageSizeSearchObj, eventStatus);
        long totalRecords = eventsPage.getTotalElements();

        // Transform the paginated results without additional filtering
        List<EventEndInfoDto> eventEndInfoDtos = roEventService.getEventEndInfoForUserPagination(eventsPage, user, source, searchString, searchDate, pageSizeSearchObj, eventStatus);
        userEventInfo.addAll(eventEndInfoDtos);

        DataTableResponse response = new DataTableResponse();
        response.setData(userEventInfo);
        response.setRecordsTotal(totalRecords);
        response.setRecordsFiltered(userEventInfo.size());
        return response;
    }

    @Override
    public List<Object[]> findAllByEmails(List<String> emails) {
        return roUserRepo.findAllByEmails(emails);
    }

}
