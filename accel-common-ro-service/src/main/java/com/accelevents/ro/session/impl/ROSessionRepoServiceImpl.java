package com.accelevents.ro.session.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionStartStatus;
import com.accelevents.domain.enums.EnumSessionStatus;
import com.accelevents.domain.enums.SessionVisibilityType;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.ro.event.repository.ROSessionRepository;
import com.accelevents.ro.session.ROSessionRepoService;
import com.accelevents.ro.speaker.ROSpeakerService;
import com.accelevents.services.ROVirtualEventPortalService;
import com.accelevents.session_speakers.dto.SessionFilter;
import com.accelevents.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.OffsetDateTime;
import java.util.*;

import static com.accelevents.dto.PageUtil.getPageable;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.springframework.util.CollectionUtils.isEmpty;

@Service
public class ROSessionRepoServiceImpl implements ROSessionRepoService {
    @Autowired
    private ROSessionRepository roSessionRepository;
    @Autowired
    private ROSpeakerService roSpeakerService;
    @Autowired
    private ROVirtualEventPortalService roVirtualEventPortalService;

    @Override
    public Page<Session> getAllPublicSessionByEventId(Event event, Pageable pageable, SessionFilter filter) {
        List<Long> filterSessionIds = new ArrayList<>();
        String searchString = filter.getSearch();
        if (!isEmpty(filter.getTagOrTrackIds()) || isNotBlank(searchString) || !CollectionUtils.isEmpty(filter.getIds())) {
            if (isEmpty(filter.getTagOrTrackIds())) {
                filter.setTagOrTrackIds(singletonList(0L));
            }
            if (!CollectionUtils.isEmpty(filter.getIds())) {
                filterSessionIds = filter.getIds();
            } else {
                if (StringUtils.isNotBlank(searchString)) {
                    filterSessionIds = roSessionRepository.filterSession(event.getEventId(), filter.getTagOrTrackIds(), searchString.trim());
                } else {
                    filterSessionIds = roSessionRepository.filterSessionWithoutSearch(event.getEventId(), filter.getTagOrTrackIds());
                }
            }
            if (filterSessionIds.isEmpty()) {
                return new PageImpl(emptyList());
            }
        }

        Set<Long> finalFilterSessionIds = !filterSessionIds.isEmpty() ? new HashSet<>(filterSessionIds) : null;

        List<Session> sessions = roSessionRepository.getSessionByEvent(event.getEventId());
        List<Session> sessionsToReturn;

        if (null != filter.getFilterDate()) {
            sessionsToReturn = filteredPublicCacheSessions(sessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), filter.getFilterDate(), filter.isShowPastAndUpcoming());
        } else {
            sessionsToReturn = filteredPublicCacheSessions(sessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), null, filter.isShowPastAndUpcoming());
        }

        return getPageable(sessionsToReturn, PageRequest.of(0,Integer.MAX_VALUE));
    }

    @Override
    public Page<Session> getAllCachePrivateSessionByEventId(Event event, Pageable pageable, SessionFilter filter, List<Session> userSpecificSessions) {
        List<Long> filterSessionIds = new ArrayList<>();
        String searchString = filter.getSearch();
        if (!isEmpty(filter.getTagOrTrackIds()) || isNotBlank(searchString) || !CollectionUtils.isEmpty(filter.getIds())) {
            if (isEmpty(filter.getTagOrTrackIds())) {
                filter.setTagOrTrackIds(singletonList(0L));
            }
            if (!CollectionUtils.isEmpty(filter.getIds())) {
                filterSessionIds = filter.getIds();
            } else {
                if (StringUtils.isNotBlank(searchString)) {
                    filterSessionIds = roSessionRepository.filterSession(event.getEventId(), filter.getTagOrTrackIds(), searchString.trim());
                } else {
                    filterSessionIds = roSessionRepository.filterSessionWithoutSearch(event.getEventId(), filter.getTagOrTrackIds());
                }
            }
            if (filterSessionIds.isEmpty()) {
                return new PageImpl(emptyList());
            }
        }

        Set<Long> finalFilterSessionIds = !filterSessionIds.isEmpty() ? new HashSet<>(filterSessionIds) : null;

        List<Session> sessionsToReturn;

        if (null != filter.getFilterDate()) {
            sessionsToReturn = filteredCachePrivateSessions(userSpecificSessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), filter.getFilterDate(), filter.isShowPastAndUpcoming());
        } else {
            sessionsToReturn = filteredCachePrivateSessions(userSpecificSessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), null, filter.isShowPastAndUpcoming());
        }
        return getPageable(sessionsToReturn, PageRequest.of(0, Integer.MAX_VALUE));
    }

    @Override
    public boolean allSessionVisibleForUser(User user, Event event) {
        return roSpeakerService.isSpeakerInEvent(event, user) || roVirtualEventPortalService.isUserExhibitorAdminOrLeadRetriever(user, event);
    }

    private List<Session> filteredPublicCacheSessions(List<Session> sessions, Set<Long> finalFilterSessionIds, Boolean past, String sessionFormat, String filterDate, boolean showPastAndUpcoming) {
        List<Session> sessionsToReturn = new ArrayList<>();
        Date currentDate = new Date();
        OffsetDateTime startDate = isNotBlank(filterDate) ? DateUtils.getDateInUTCFromTimeZoneStr(filterDate) : null;
        OffsetDateTime endDate = (startDate != null) ? DateUtils.add24Hours1SecLess(startDate) : null;
        sessions.forEach(session -> {
            if ((isNotBlank(sessionFormat) && session.getFormat() != null && !sessionFormat.equals(session.getFormat().name())) ||
                    session.isHideSessionFromAttendees() || session.getSessionVisibilityType().equals(SessionVisibilityType.PRIVATE) || EnumSessionStatus.HIDDEN.equals(session.getStatus())) {
                return;
            }
            boolean selected = isSelected(finalFilterSessionIds, past, showPastAndUpcoming, currentDate, startDate, endDate, session);
            if (selected && !EnumSessionStatus.DRAFT.equals(session.getStatus())) {
                sessionsToReturn.add(session);
            }
        });
        return sessionsToReturn;

    }

    private boolean isSelected(Set<Long> finalFilterSessionIds, Boolean past, boolean showPastAndUpcoming, Date currentDate, OffsetDateTime startDate, OffsetDateTime endDate, Session session) {
        boolean selected = false;

        if (finalFilterSessionIds != null) {
            if (finalFilterSessionIds.contains(session.getId())) {
                selected = canSelectRecord(session, currentDate, past, startDate, endDate, showPastAndUpcoming);
            }
        } else {
            selected = canSelectRecord(session, currentDate, past, startDate, endDate, showPastAndUpcoming);
        }
        return selected;
    }

    private boolean canSelectRecord(Session session, Date currentDate, Boolean past, OffsetDateTime startDate, OffsetDateTime endDate, boolean showPastAndUpcoming) {
        if (startDate != null && endDate != null && session.getStartTime() != null && session.getEndTime() != null) {
            boolean withinDateRange = isWithinDateRange(session, startDate, endDate);
            if (showPastAndUpcoming) {
                return withinDateRange;
            }
            return withinDateRange && isUpcomingOrStarted(session, currentDate);
        }

        if (past == null) {
            return showPastAndUpcoming || isUpcomingOrStarted(session, currentDate);
        }

        return past && session.getEndTime() != null ? session.getEndTime().before(currentDate) : isUpcomingOrStarted(session, currentDate);
    }

    private boolean isWithinDateRange(Session session, OffsetDateTime startDate, OffsetDateTime endDate) {
        Date start = Date.from(startDate.toInstant());
        Date end = Date.from(endDate.toInstant());
        return !start.after(session.getStartTime()) && !end.before(session.getStartTime());
    }

    private boolean isUpcomingOrStarted(Session session, Date currentDate) {
        return (session.getSessionStartStatus() != null && session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED))
                || (session.getEndTime() != null && session.getEndTime().after(currentDate));
    }

    private List<Session> filteredCachePrivateSessions(List<Session> sessions, Set<Long> finalFilterSessionIds, Boolean past, String sessionFormat, String filterDate, boolean showPastAndUpcoming) {
        List<Session> sessionsToReturn = new ArrayList<>();
        Date currentDate = new Date();
        OffsetDateTime startDate = isNotBlank(filterDate) ? DateUtils.getDateInUTCFromTimeZoneStr(filterDate) : null;
        OffsetDateTime endDate = (startDate != null) ? DateUtils.add24Hours1SecLess(startDate) : null;
        sessions.forEach(session -> {
            if (isNotBlank(sessionFormat) && null != session.getFormat() && !sessionFormat.equals(session.getFormat().name())) {
                return;
            }
            boolean selected = isSelected(finalFilterSessionIds, past, showPastAndUpcoming, currentDate, startDate, endDate, session);
            if (selected && !EnumSessionStatus.DRAFT.equals(session.getStatus())) {
                sessionsToReturn.add(session);
            }
        });
        return sessionsToReturn;

    }

    @Override
    public List<Long> findSessionIdsByEventId(Long eventId) {
        List<Long> sessionIds = roSessionRepository.findSessionIdsByEventId(eventId);
        if (sessionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return sessionIds;
    }
}
