package com.accelevents.ro.staff;

import com.accelevents.domain.Event;
import com.accelevents.domain.Staff;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.enums.StaffRole;
import com.accelevents.enums.UserRole;

import java.util.List;
import java.util.Optional;

public interface ROStaffService {

    List<Staff> getListOfStaffByEvent(Event event);

    List<Staff> findByEvent(Event event);

    List<Staff> findByUserAndWhiteLabelIsNotNull(User user, List<StaffRole> staffRoles);

    Staff findByEventIdAndUserIdAndRole(Long eventId, Long userId);

    boolean hasSuperAdminAccess(User user);

    boolean hasSuperSaleAdminAccess(User user);

    boolean hasStaffAccessForEvent(User user, Event event);

    boolean hasWhiteLabelAdminOrEventCoordinatorAccess(User user, WhiteLabel whiteLabel);

    boolean hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(User user, WhiteLabel whiteLabel);

    boolean hasAccessToDisconnect(User user, Event event);

    boolean isEventStaffOrAdmin(Event event, User user);

    boolean isUserApiUserForEvent(User user, Event event);

    boolean checkStaffTableForAdminAndStaffRole(Event event, User user);

    boolean isUserIsAdminAndStaffByEventIdAndUserId(Long eventId, Long userId);

    UserRole getUserRole(User user, Event event, Long exhibitorId);

    boolean isUserAdminForEvent(User user, List<String> userRoles, Event event);

    Staff getApiUserAdminOrWhiteLabelAdmin(User user, WhiteLabel whiteLabel, Event event);

    Staff findApiUserByUserAndRoleAndApiUserAndWhiteLabel(Long userId, WhiteLabel whiteLabel);

    Staff findStaffAdminByUserIdAndEventIdAndAPIUserTrue(Long userId, Long eventId);

    boolean isOrganizerAdmin(User user, Event event);

    boolean isOrganizerOwner(User user, Event event);

    boolean isWhiteLabelAdminForEvent(User user, List<String> userRoles, Event event);

    boolean isEventCoordinatorForEvent(User user, List<String> userRoles, Event event);

    boolean isEventAdmin(Event event, User user);

    Staff findByEventAndUserNotExhibitor(Event event, User user, boolean isWhitelabelUser);

    boolean isEventStaff(Event event, User user);

    boolean isUserAdminInAnyEvent(User user, Event event);

    List<Staff> getExhibitorDetail(User user, Event event);

    boolean hasHostAccessForEvent(User user, Event event);

    List<Staff> findByUserAndRoleAndEventIsNotNull(User user, StaffRole role);

    List<Staff> findByEventAndUser(Event event, User user);

    List<Staff> findByEventAndUserNotExhibitor(Event event, User user);

    List<StaffRole> getStaffRole(Event event, User objUser);

    List<StaffRole> findStaffRoleByUserAndWhiteLabel(User user, WhiteLabel whiteLabel);

    boolean isWhiteLabelAdmin(User user, List<String> userRoles, WhiteLabel whiteLabel);

    boolean isEventCoordinator(User user, WhiteLabel whiteLabel);

    boolean isEventCoordinator(User user, List<String> userRoles, WhiteLabel whiteLabel);

    boolean isEventBudgetOwner(User user, List<String> userRoles, WhiteLabel whiteLabel);

    boolean isEventPlanner(User user, List<String> userRoles, WhiteLabel whiteLabel);

    List<StaffRole> findRoleByEventAndUser(long eventId, Long userId);

    boolean hasWhiteLabelAdminAccess(User user, WhiteLabel whiteLabel);

    boolean hasWhiteLabelEventCoordinator(User user, WhiteLabel whiteLabel);

    List<String> getAllUserRolesWithVirtualAndHybridTickets(Long eventId, Long userId);

    Optional<Staff> findWhiteLabelAdminStaff(User user, WhiteLabel whiteLabel);

    boolean isUserHaveAdminOrStaffOrExhibitorAdminOrLeadRetrieverRole(Event event, User user);

    boolean hasChargebeeConfigAdminRole(User user);

    Optional<Staff> findWhiteLabelEventCoordinator(User user, WhiteLabel whiteLabel);

    Optional<Staff> findWhiteLabelEventBudgetOwner(User user, WhiteLabel whiteLabel);

    Optional<Staff> findWhiteLabelEventPlanner(User user, WhiteLabel whiteLabel);

    Staff findApiUserByEventAndUserAndRoleAndApiUser(Long userId, Long eventId);

    List<String> findAllRoleByUserId(Long userId);

    List<String> getAllUserRoles(Long eventId, Long userId);

    List<Staff> findExhibitorByExhibitorAdminOrLeadAndEventAndUser(Event event, User user);

    boolean isStaffAllowedToManageOrders(User user, Event event);

    boolean hasWhiteLabelAdmin(User user, List<String> userRoles, WhiteLabel whiteLabel);

}
