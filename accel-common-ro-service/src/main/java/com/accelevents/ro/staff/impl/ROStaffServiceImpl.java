package com.accelevents.ro.staff.impl;

import com.accelevents.domain.*;
import com.accelevents.dto.EventLevelSettingsDTO;
import com.accelevents.enums.OrganizerRole;
import com.accelevents.enums.StaffRole;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.event.service.ROJoinUserWithOrganizerService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.staff.repo.ROStaffRepository;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.utils.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.enums.UserRole.*;
import static com.accelevents.utils.Permissions.WL_AND_EVENT_COORDINATOR_PERMISSIONS;
import static com.accelevents.utils.Permissions.WL_AND_EVENT_COORDINATOR_STRING_PERMISSIONS;

@Service
public class ROStaffServiceImpl implements ROStaffService {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ROStaffServiceImpl.class);

    @Autowired
    private ROStaffRepository roStaffRepository;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private ROJoinUserWithOrganizerService roJoinUserWithOrganizerService;
    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;

    @Override
    public List<Staff> getListOfStaffByEvent(Event event) {
        return roStaffRepository.getStaffsByEvent(event);
    }

    @Override
    public List<Staff> findByEvent(Event event) {
        return getListOfStaffByEvent(event).stream().filter(e->
                StaffRole.admin.equals(e.getRole()) || StaffRole.staff.equals(e.getRole())).collect(Collectors.toList());
    }

    @Override
    public List<Staff> findByUserAndWhiteLabelIsNotNull(User user, List<StaffRole> staffRoles){
        return roStaffRepository.findByUserAndWhiteLabelIsNotNull(user, staffRoles);

    }

    @Override
    public Staff findByEventIdAndUserIdAndRole(Long eventId, Long userId) {
        return roStaffRepository.findByEventIdAndUserIdAndRole(eventId, userId);
    }

    @Override
    public boolean hasSuperAdminAccess(User user) {
        if (user != null) {
            List<String> userRoles = this.roUserService.getUserRoles(user);
            return roUserService.isSuperAdminUser(user, userRoles);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }

    @Override
    public boolean hasSuperSaleAdminAccess(User user){
        if (user != null) {
            List<String> userRoles = this.roUserService.getUserRoles(user);
            return roUserService.isSalesSuperAdmin(user, userRoles);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }

    @Override
    public boolean hasStaffAccessForEvent(User user, Event event) {
        if (user != null && event != null) {
            return isEventStaffOrAdmin(event, user);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }

    @Override
    public boolean hasWhiteLabelAdminOrEventCoordinatorAccess(User user, WhiteLabel whiteLabel) {
        if (user != null) {
            List<String> userRoles = this.roUserService.getUserRoles(user);
            return roUserService.isSuperAdminUser(user, userRoles) || this.isWhiteLabelAdmin(user, userRoles, whiteLabel) || this.isEventCoordinator(user, userRoles, whiteLabel);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }


    @Override
    public boolean hasWhiteLabelAdminOrEventCoordinatorOrBudgetOwnerOrPlannerAccess(User user, WhiteLabel whiteLabel){
        if (user != null) {
            List<String> userRoles = this.roUserService.getUserRoles(user);
            return roUserService.isSuperAdminUser(user, userRoles) || this.hasWhiteLabelAdmin(user, userRoles, whiteLabel) || this.isEventCoordinator(user, userRoles, whiteLabel)
                    || this.isEventBudgetOwner(user, userRoles, whiteLabel) || this.isEventPlanner(user, userRoles, whiteLabel);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }
    
    @Override
    public boolean hasAccessToDisconnect(User user, Event event) {
        boolean result = true;
        WhiteLabel whiteLabel = event.getWhiteLabel();
        if (whiteLabel != null && whiteLabel.isManualPayout()) {
            result = this.hasWhiteLabelAdminOrEventCoordinatorAccess(user, event.getWhiteLabel());
        }
        return result;
    }

    @Override
    public boolean isEventStaffOrAdmin(Event event, User user) {
        // TODO : Optimize
        // log.info("Checking if staff or admin");//nosonar
        // log.info(user);//nosonar
        List<String> userRoles = this.roUserService.getUserRoles(user);
        if (roUserService.isSuperAdminUser(user, userRoles) || this.isWhiteLabelAdminForEvent(user, userRoles, event) || this.isEventCoordinatorForEvent(user, userRoles, event) ||
                this.isUserApiUserForEvent(user, event) || this.isOrganizerAdmin(user, event) || this.isOrganizerOwner(user, event)) {
            // log.info("User is super admin");//NOSONAR
            return true;
        } else {
            return checkStaffTableForAdminAndStaffRole(event, user);
        }
    }

    @Override
    public boolean isUserApiUserForEvent(User user, Event event) {
        boolean result = false;
        Staff staff = this.getApiUserAdminOrWhiteLabelAdmin(user, event.getWhiteLabel(), event);
        if (staff != null) {
            result = true;
        }
        return result;
    }

    @Override
    public boolean checkStaffTableForAdminAndStaffRole(Event event, User user) {
        // TODO : Optimize
        boolean isAdminOrStaff = this.isUserIsAdminAndStaffByEventIdAndUserId(event.getEventId(), user.getUserId());

        if(!isAdminOrStaff) {
            isAdminOrStaff = this.isUserApiUserForEvent(user, event);
        }
        return isAdminOrStaff;
    }

    @Override
    public boolean isUserIsAdminAndStaffByEventIdAndUserId(Long eventId, Long userId) {
        return roStaffRepository.isUserIsAdminAndStaffByEventIdAndUserId(eventId, userId);
    }

    @Override
    public UserRole getUserRole(User user, Event event, Long exhibitorId) {
        checkUserAndEventNotNull(user, event);
        if (exhibitorId != null) {
            List<Staff> staff = this.getExhibitorDetail(user, event);
            List<StaffRole> staffRoles = new ArrayList<>();
            staffRoles.add(StaffRole.admin);
            staffRoles.add(StaffRole.whitelabeladmin);
            staffRoles.add(StaffRole.eventcoordinator);
            Optional<Staff> optionalStaff = staff.stream().filter(e -> staffRoles.contains(e.getRole())).findFirst();
            if (optionalStaff.isPresent()) {
                return optionalStaff.get().getRole().equals(StaffRole.admin)
                        ? ROLE_ADMIN : (optionalStaff.get().getRole().equals(StaffRole.eventcoordinator) ? ROLE_EVENT_COORDINATOR : ROLE_WHITELABELADMIN);
            }

            List staffRoles1 = new ArrayList<>();
            staffRoles1.add(StaffRole.exhibitoradmin);
            staffRoles1.add(StaffRole.leadretriever);

            optionalStaff = staff.stream().
                    filter(e -> staffRoles1.contains(e.getRole()) && exhibitorId.equals(e.getExhibitorId()))
                    .findFirst();

            if (optionalStaff.isPresent()) {
                return StaffRole.exhibitoradmin.equals(optionalStaff.get().getRole()) ? ROLE_EXHBITOR_ADMIN : ROLE_LEAD_RETRIEVER;
            }
        }

        List<String> userRoles = roUserService.getUserRoles(user);
        if (isUserAdminForEvent(user, userRoles, event)) {
            return ROLE_ADMIN;
        } else if (isOrganizerAdmin(user,event)){
            return ROLE_ORGANIZER_ADMIN;
        } else if (isOrganizerOwner(user,event)){
            return ROLE_ORGANIZER_OWNER;
        } else if (isWhiteLabelAdminForEvent(user, userRoles, event)) {
            return ROLE_WHITELABELADMIN;
        }else if (isEventCoordinatorForEvent(user, userRoles, event)) {
            return ROLE_EVENT_COORDINATOR;
        } else if (roUserService.isSuperAdminUser(user, userRoles)) {
            return UserRole.ROLE_SUPERADMIN;
        }
        return null;
    }

    private void checkUserAndEventNotNull(User user, Event event) {
        if (user == null) {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
        if (event == null) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

    @Override
    public boolean isUserAdminForEvent(User user, List<String> userRoles, Event event) {
        Staff staff = null;
        boolean result = false;
        if (userRoles.contains(ROLE_ADMIN.name())) {
            staff = roStaffRepository.findByEventAndUserAndRole(event, user, Collections.singletonList(StaffRole.admin));
            if (staff != null) {
                result = true;
            } else {
                staff = this.getApiUserAdminOrWhiteLabelAdmin(user, event.getWhiteLabel(), event);
                if (staff != null) {
                    result = true;
                }
            }
        }
        if(userRoles.contains(ROLE_USER.name()) && staff==null){
            Optional<JoinUsersWithOrganizers> optionalJoinUsersWithOrganizers = roJoinUserWithOrganizerService.getByUserIdAndOrgId(user.getUserId(), event.getOrganizerId());
            result=optionalJoinUsersWithOrganizers.isPresent() && OrganizerRole.admin.equals(OrganizerRole.valueOf(optionalJoinUsersWithOrganizers.get().getRole().name())) || optionalJoinUsersWithOrganizers.isPresent() && OrganizerRole.owner.equals(OrganizerRole.valueOf(optionalJoinUsersWithOrganizers.get().getRole().name()));
        }
        return result;
    }

    @Override
    public Staff getApiUserAdminOrWhiteLabelAdmin(User user, WhiteLabel whiteLabel, Event event) {
        Staff staff = findApiUserByUserAndRoleAndApiUserAndWhiteLabel(user.getUserId(), whiteLabel);
        if (staff == null) {
            staff = findStaffAdminByUserIdAndEventIdAndAPIUserTrue(user.getUserId(), event.getEventId());
        }
        return staff;
    }

    @Override
    public Staff findApiUserByUserAndRoleAndApiUserAndWhiteLabel(Long userId, WhiteLabel whiteLabel) {
        Staff staff = null;
        if (whiteLabel != null) {
            staff = roStaffRepository.findByUserAndRoleAndApiUser(userId, whiteLabel.getId(), WL_AND_EVENT_COORDINATOR_STRING_PERMISSIONS);
        }
        return staff;
    }

    @Override
    public Staff findStaffAdminByUserIdAndEventIdAndAPIUserTrue(Long userId, Long eventId) {
        return roStaffRepository.findByEventAndUserAndRoleAndApiUser(eventId, userId, StaffRole.admin.name());
    }

    @Override
    public boolean isOrganizerAdmin(User user, Event event) {
        Optional<JoinUsersWithOrganizers> optionalJoinUsersWithOrganizers = roJoinUserWithOrganizerService.getByUserIdAndOrgId(user.getUserId(), event.getOrganizerId());
        return  optionalJoinUsersWithOrganizers.isPresent() && OrganizerRole.admin.equals(OrganizerRole.valueOf(optionalJoinUsersWithOrganizers.get().getRole().name()));
    }

    @Override
    public boolean isOrganizerOwner(User user, Event event) {
        Optional<JoinUsersWithOrganizers> optionalJoinUsersWithOrganizers = roJoinUserWithOrganizerService.getByUserIdAndOrgId(user.getUserId(), event.getOrganizerId());
        return optionalJoinUsersWithOrganizers.isPresent() && OrganizerRole.owner.equals(OrganizerRole.valueOf(optionalJoinUsersWithOrganizers.get().getRole().name()));
    }

    @Override
    public boolean isWhiteLabelAdminForEvent(User user, List<String> userRoles, Event event) {
        boolean result = false;
        if (userRoles.contains(ROLE_WHITELABELADMIN.name())) {
            WhiteLabel whiteLabel = event.getWhiteLabel();
            Optional<Staff> staffOptional = roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.whitelabeladmin);
            if (staffOptional.isPresent()) {
                result = true;
            } else {
                Staff staff = this.findApiUserByUserAndRoleAndApiUserAndWhiteLabel(user.getUserId(), whiteLabel);
                if(staff!=null) {
                    return true;
                }
            }
        }
        return result;
    }

    @Override
    public boolean isEventCoordinatorForEvent(User user, List<String> userRoles, Event event) {
        boolean result = false;
        if (userRoles.contains(ROLE_EVENT_COORDINATOR.name())) {
            WhiteLabel whiteLabel = event.getWhiteLabel();
            Optional<Staff> staffOptional = roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.eventcoordinator);
            if (staffOptional.isPresent()) {
                result = true;
            } else {
                Staff staff = this.findApiUserByUserAndRoleAndApiUserAndWhiteLabel(user.getUserId(), whiteLabel);
                if (staff != null) {
                    return true;
                }
            }
        }
        return result;
    }

    @Override
    public boolean isEventAdmin(Event event, User user) {
        Staff staff = roStaffRepository.findByEventAndUserAndRole(event, user, Collections.singletonList(StaffRole.admin));
        return staff != null;
    }

    @Override
    public Staff findByEventAndUserNotExhibitor(Event event, User user, boolean isWhitelabelUser) {
        if (isWhitelabelUser) {
            return roStaffRepository.findByEventAndUserAndRole(event, user, WL_AND_EVENT_COORDINATOR_PERMISSIONS);
        } else {
            return roStaffRepository.findByEventIdAndUserIdAndRole(event.getEventId(), user.getUserId());
        }
    }

    @Override
    public boolean isEventStaff(Event event, User user) {
        boolean result = false;
        Staff staff = roStaffRepository.findByEventAndUserAndRole(event, user, Collections.singletonList(StaffRole.staff));
        if (staff != null && staff.getUser().getUserId().equals(user.getUserId())
                && staff.getRole().equals(StaffRole.staff)) {
            result = true;
        }

        return result;
    }

    @Override
    public boolean isUserAdminInAnyEvent(User user, Event event) {
        List<StaffRole> staffRoles = Arrays.asList(StaffRole.whitelabeladmin, StaffRole.admin, StaffRole.eventcoordinator);
        return roStaffRepository.countByUserIdAndEventIdAndStaffRole(user, staffRoles, event);
    }

    @Override
    public List<Staff> getExhibitorDetail(User user, Event event) {
        checkUserAndEventNotNull(user, event);
        return roStaffRepository.findEventAndUser(event.getEventId(), user.getUserId());
    }

    @Override
    public boolean hasHostAccessForEvent(User user, Event event) {
        checkUserAndEventNotNull(user, event);
        List<String> userRoles = roUserService.getUserRoles(user);

        return (isUserAdminForEvent(user, userRoles, event) ||
                isWhiteLabelAdminForEvent(user, userRoles, event) || isEventCoordinatorForEvent(user, userRoles, event) ||
                roUserService.isSuperAdminUser(user, userRoles));
    }

    @Override
    public List<Staff> findByUserAndRoleAndEventIsNotNull(User user, StaffRole role) {
        return roStaffRepository.findByUserAndRoleAndEventIsNotNull(user, role);
    }

    @Override
    public List<Staff> findByEventAndUser(Event event, User user){
        return roStaffRepository.findByEventAndUser(event, user);
    }

    @Override
    public List<Staff> findByEventAndUserNotExhibitor(Event event, User user) {
        return roStaffRepository.findByEventAndUser(event, user).stream()
                .filter(e -> e.getExhibitorId() == null).collect(Collectors.toList());
    }

    @Override
    public List<StaffRole> getStaffRole(Event event, User objUser) {
        List<Staff> staffList = this.findByEventAndUserNotExhibitor(event, objUser);
        log.info("Staff list size: {} and event {}", staffList.size(), null != event ? event.getEventId() : null);
        return staffList.stream().map(Staff::getRole).collect(Collectors.toList());
    }

    @Override
    public List<StaffRole> findStaffRoleByUserAndWhiteLabel(User user, WhiteLabel whiteLabel) {
        return roStaffRepository.findByWhiteLabelAndUser(whiteLabel, user).parallelStream().map(Staff::getRole).collect(Collectors.toList());
    }

    @Override
    public boolean isWhiteLabelAdmin(User user, List<String> userRoles, WhiteLabel whiteLabel) {
        boolean result = false;
        //List<String> roles = this.roUserService.getUserRoles(user);//NOSONAR
        if (userRoles.contains(ROLE_WHITELABELADMIN.name())) {
            Optional<Staff> staffOptional = roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.whitelabeladmin);
            if (staffOptional.isPresent()) {
                result = true;
            } else {
                Staff staff = this.findApiUserByUserAndRoleAndApiUserAndWhiteLabel(user.getUserId(), whiteLabel);
                if (staff != null) {
                    return true;
                }
            }
        }
        return result;
    }

    @Override
    public boolean isEventCoordinator(User user, WhiteLabel whiteLabel) {
        boolean result = false;
        Optional<Staff> staffOptional = roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.eventcoordinator);
        if (staffOptional.isPresent()) {
            result = true;
        }
        return result;
    }

    @Override
    public boolean isEventCoordinator(User user, List<String> userRoles, WhiteLabel whiteLabel) {
        boolean result = false;
        if (userRoles.contains(ROLE_EVENT_COORDINATOR.name())) {
            Optional<Staff> staffOptional = roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.eventcoordinator);
            if (staffOptional.isPresent()) {
                result = true;
            }
        }
        return result;
    }

    @Override
    public boolean hasWhiteLabelAdmin(User user, List<String> userRoles, WhiteLabel whiteLabel) {
        return hasStaffRole(user, userRoles, whiteLabel,
                ROLE_WHITELABELADMIN.name(), StaffRole.whitelabeladmin);
    }

    @Override
    public boolean isEventBudgetOwner(User user, List<String> userRoles, WhiteLabel whiteLabel) {
        return hasStaffRole(user, userRoles, whiteLabel,
                ROLE_EVENT_BUDGET_OWNER.name(), StaffRole.eventbudgetowner);
    }

    @Override
    public boolean isEventPlanner(User user, List<String> userRoles, WhiteLabel whiteLabel) {
        return hasStaffRole(user, userRoles, whiteLabel,
                ROLE_EVENT_PLANNER.name(), StaffRole.eventplanner);
    }

    private boolean hasStaffRole(User user, List<String> userRoles, WhiteLabel whiteLabel,
                                 String roleConstant, StaffRole staffRole) {
        if (userRoles.contains(roleConstant)) {
            return roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, staffRole).isPresent();
        }
        return false;
    }

    @Override
    public List<StaffRole> findRoleByEventAndUser(long eventId, Long userId) {
        return roStaffRepository.findStaffRoleByEventAndUser(eventId, userId);
    }

    @Override
    public boolean hasWhiteLabelAdminAccess(User user, WhiteLabel whiteLabel) {
        if (user != null) {
            List<String> userRoles = this.roUserService.getUserRoles(user);
            return roUserService.isSuperAdminUser(user, userRoles) || this.isWhiteLabelAdmin(user, userRoles, whiteLabel);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }

    @Override
    public boolean hasWhiteLabelEventCoordinator(User user, WhiteLabel whiteLabel) {
        if (user != null) {
            List<String> userRoles = this.roUserService.getUserRoles(user);
            return roUserService.isSuperAdminUser(user, userRoles) || this.isEventCoordinator(user, userRoles, whiteLabel);
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
    }

    @Override
    public List<String> getAllUserRolesWithVirtualAndHybridTickets(Long eventId, Long userId){
        return roStaffRepository.findAllRolesByUserIdAndEventIdAndEventTicketType(userId, eventId);
    }

    @Override
    public Optional<Staff> findWhiteLabelAdminStaff(User user, WhiteLabel whiteLabel) {
        return roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.whitelabeladmin);
    }

    @Override
    public boolean isUserHaveAdminOrStaffOrExhibitorAdminOrLeadRetrieverRole(Event event, User user) {
        boolean isAdminOrStaff = roStaffRepository.isUserHaveAdminOrStaffOrExhibitorAdminOrLeadRetrieverRole(event.getEventId(), user.getUserId());
        if(!isAdminOrStaff) {
            isAdminOrStaff = this.isUserApiUserForEvent(user, event);
        }
        return isAdminOrStaff;
    }

    @Override
    public boolean hasChargebeeConfigAdminRole(User user){
        List<String> userRoles = roUserService.getUserRoles(user);
        return roUserService.isChargebeeConfigAdminRole(user,userRoles);
    }

    @Override
    public Optional<Staff> findWhiteLabelEventCoordinator(User user, WhiteLabel whiteLabel) {
        return roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.eventcoordinator);
    }

    @Override
    public Optional<Staff> findWhiteLabelEventBudgetOwner(User user, WhiteLabel whiteLabel) {
        return roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.eventbudgetowner);
    }

    @Override
    public Optional<Staff> findWhiteLabelEventPlanner(User user, WhiteLabel whiteLabel) {
        return roStaffRepository.findByWhiteLabelAndUserAndRole(whiteLabel, user, StaffRole.eventplanner);
    }

    @Override
    public Staff findApiUserByEventAndUserAndRoleAndApiUser(Long userId, Long eventId) {
        return roStaffRepository.findByEventAndUserAndRoleAndApiUser(eventId, userId, StaffRole.admin.name());
    }

    @Override
    public List<String> findAllRoleByUserId(Long userId) {
        return roStaffRepository.findAllRoleByUserId(userId);
    }

    @Override
    public List<String> getAllUserRoles(Long eventId, Long userId) {
        return roStaffRepository.findAllRolesByUserIdAndEventId(userId, eventId);
    }

    @Override
    public List<Staff> findExhibitorByExhibitorAdminOrLeadAndEventAndUser(Event event, User user) {
        List<Staff> staffs = roStaffRepository.findEventAndUser(event.getEventId(), user.getUserId());
        if (staffs != null && !staffs.isEmpty()) {
            return staffs.stream().filter(staff -> staff.getRole().equals(StaffRole.exhibitoradmin) || staff.getRole().equals(StaffRole.leadretriever)).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    public boolean isStaffAllowedToManageOrders(User user, Event event) {
        return hasStaffAccessForEvent(user, event) &&
                roEventLevelSettingService.findByEventId(event.getEventId())
                        .map(EventLevelSettings::isAllowStaffToManageOrders)
                        .orElseGet(() -> new EventLevelSettingsDTO().isAllowStaffToManageOrders());
    }
}
