package com.accelevents.ro.event.service.impl;

import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.LoginUserWLOrgEventDetailsDto;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.event.repository.ROWhiteLabelRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROOrganizerService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.utils.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.accelevents.enums.UserRole.*;

@Service
public class ROWhiteLabelServiceImpl implements ROWhiteLabelService {

    private static final Logger log = LoggerFactory.getLogger(ROWhiteLabelServiceImpl.class);

    @Autowired
    private ROWhiteLabelRepository roWhiteLabelRepository;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private ROOrganizerService roOrganizerService;
    @Autowired
    private ROUserService roUserService;

    @Override
    public Optional<WhiteLabel> getWhiteLabelById(Long whiteLabelId) {
        return roWhiteLabelRepository.findById(whiteLabelId);
    }

    @Override
    public WhiteLabel findByHostBaseUrl(String hostBaseUrl) {
        log.info("ROWhiteLabelServiceImpl -> findByHostBaseUrl, Fetching white label info by host base URL {} ", hostBaseUrl);
        List<WhiteLabel> whiteLabels = roWhiteLabelRepository.findWhiteLabelByHostBaseUrlAndHostBaseUrlIsNotNull(hostBaseUrl);
        return CollectionUtils.isNotEmpty(whiteLabels) ? whiteLabels.get(0) : null;
    }

    @Override
    public WhiteLabel getWhiteLabelDetailsByHostBaseUrl(String hostBaseUrl) {
        return this.findByHostBaseUrl(hostBaseUrl);
    }

    @Override
    public Optional<WhiteLabel> findWhiteLabelByUrlOp(String whiteLabelURL){
        return roWhiteLabelRepository.findWhiteLabelByWhiteLabelUrl(whiteLabelURL);
    }

    @Override
    public WhiteLabel findWhiteLabelOrThrowNotFound(String whiteLabelURL) {
        return roWhiteLabelRepository.findWhiteLabelByWhiteLabelUrl(whiteLabelURL)
                .orElseThrow(() -> new NotFoundException(NotFoundException.EventNotFound.WHITE_LABEL_EVENT_NOT_FOUND));
    }

    @Override
    public String getHostBaseUrlByWhiteLabelId(Long whiteLabelId) {
        return roWhiteLabelRepository.findHostBaseUrlByWhiteLabelId(whiteLabelId);
    }

    @Override
    public List<LoginUserWLOrgEventDetailsDto> findLoginUserWlOrgEventAccounts(User user, String role) {
        List<LoginUserWLOrgEventDetailsDto> eventList;
        if (ROLE_WHITELABELADMIN.name().equals(role) || ROLE_EVENT_COORDINATOR.name().equals(role) || ROLE_EVENT_BUDGET_OWNER.name().equals(role) || ROLE_EVENT_PLANNER.name().equals(role)) {
            eventList = roWhiteLabelRepository.findLoginUserWlAccounts(user.getUserId(), List.of(StaffRole.whitelabeladmin, StaffRole.eventcoordinator, StaffRole.eventbudgetowner, StaffRole.eventplanner), RecordStatus.CREATE);
        } else if (ROLE_ADMIN.name().equals(role)) {
            user = roUserService.getUserById(user.getUserId()).orElse(user);
            eventList = roEventService.findLoginUserEventAccounts(user.getUserId());
            long mostRecentEventId = user.getMostRecentEventId();
            if(NumberUtils.isNumberGreaterThanZero(user.getMostRecentEventId()) && CollectionUtils.isNotEmpty(eventList)) {
                eventList.sort(Comparator.comparing(e -> !Objects.equals(e.getId(), mostRecentEventId)));
            }
        } else {
            eventList = roOrganizerService.findLoginUserOrgAccounts(user.getUserId());
        }
        return eventList;
    }
}
