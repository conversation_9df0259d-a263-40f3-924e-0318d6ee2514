package com.accelevents.ro.event.repository;

import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional(readOnly = true)
public interface ROUserSessionRepository extends org.springframework.data.repository.Repository<UserSession, Long> {

    @Query("SELECT COUNT(us) > 0 FROM UserSession us " +
            "WHERE us.sessionId = :sessionId " +
            "AND us.eventId = :eventId " +
            "AND us.userId = :userId " +
            "AND us.checkInTime IS NOT NULL ")
    Boolean checkIfUserIsCheckedInInSession(@Param("eventId") Long eventId,
                                            @Param("sessionId") Long sessionId,
                                            @Param("userId") Long userId);

    @Query(value = "select us.session_id ,group_concat(us.user_id) from user_sessions us join sessions s on s.id=us.session_id " +
            "where s.event_id=:eventId and us.session_status='REGISTERED' group by us.session_id", nativeQuery = true)
    List<Object[]> findAllSessionUserIdsByEventId(@Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
            " sessionId, COUNT(1) "+
            " )" +
            " FROM UserSession " +
            " WHERE sessionId IN (:sessionIds)" +
            " AND sessionStatus = :sessionStatus " +
            " GROUP BY sessionId ")
    List<IdCountDto> getRegisterCountBySessionIdsAndSessionStatus(@Param("sessionIds") List<Long> sessionIds, @Param("sessionStatus") EnumUserSessionStatus sessionStatus);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
            " sessionId, COUNT(1) "+
            " )" +
            " FROM UserSession " +
            " WHERE sessionId IN (:sessionIds)" +
            " AND eventId= :eventId" +
            " AND eventTicketId IS NOT NULL AND sessionStatus= :sessionStatus "+
            " GROUP BY sessionId ")
    List<IdCountDto> countBySessionIdInAndEventIdAndTicketIdIsNotNull(@Param("sessionIds") List<Long> sessionIds, @Param("eventId") long eventId, @Param("sessionStatus") EnumUserSessionStatus sessionStatus);
}
