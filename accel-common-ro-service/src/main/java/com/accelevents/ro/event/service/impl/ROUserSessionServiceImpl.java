package com.accelevents.ro.event.service.impl;

import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.ro.event.repository.ROUserSessionRepository;
import com.accelevents.ro.event.service.ROUserSessionService;
import com.accelevents.session_speakers.dto.IdCountDto;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.STRING_COMMA;

@Service
public class ROUserSessionServiceImpl implements ROUserSessionService {
    private static final Logger log = LoggerFactory.getLogger(ROUserSessionServiceImpl.class);

    @Autowired
    ROUserSessionRepository roUserSessionRepository;

    @Override
    public Boolean checkIfUserIsCheckinInSession(Long userId, Long eventId, Long sessionId) {
        return roUserSessionRepository.checkIfUserIsCheckedInInSession(eventId,sessionId, userId);
    }

    @Override
    public Map<Long, List<Integer>> findAllSessionUserIdsByEventId(Long eventId) {
        Map<Long, List<Integer>> eventsUserIdsEventsMap = new HashMap<>();
        List<Object[]> userSessionObjects = roUserSessionRepository.findAllSessionUserIdsByEventId(eventId);
        userSessionObjects.forEach(objects -> {
            Long sessionId = ((BigInteger) objects[0]).longValue();
            String userIdsString = (String) objects[1];
            List<Integer> userIds = Arrays.stream(userIdsString.split(STRING_COMMA)).map(Integer::parseInt).collect(Collectors.toList());
            eventsUserIdsEventsMap.put(sessionId, userIds);
        });
        log.info("findAllSessionUserIdsByEventId: eventId={}, sessionUserIdsMap={}", eventId, eventsUserIdsEventsMap);
        return eventsUserIdsEventsMap;
    }

    @Override
    public List<IdCountDto> getSessionStatsForIdsIn(List<Long> sessionIds) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() :  roUserSessionRepository.getRegisterCountBySessionIdsAndSessionStatus(sessionIds, EnumUserSessionStatus.REGISTERED);
    }

    @Override
    public List<IdCountDto> getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(List<Long> sessionIds, long eventId) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() :  roUserSessionRepository.countBySessionIdInAndEventIdAndTicketIdIsNotNull(sessionIds, eventId, EnumUserSessionStatus.REGISTERED);
    }

}
