package com.accelevents.ro.event.repository;

import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.LoginUserWLOrgEventDetailsDto;
import com.accelevents.dto.WLBasicDetailsDto;
import com.accelevents.enums.StaffRole;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional(readOnly = true)
public interface ROWhiteLabelRepository extends org.springframework.data.repository.Repository<WhiteLabel, Long>{

    Optional<WhiteLabel> findById(Long id);

    @Cacheable(value = "findWhiteLabelByHostBaseUrl", key = "#p0", condition="#p0 != null", unless="#result != null")
    List<WhiteLabel> findWhiteLabelByHostBaseUrlAndHostBaseUrlIsNotNull(String whiteLabelHostBaseUrl);

    @Query("SELECT new com.accelevents.dto.WLBasicDetailsDto(wl.id,wl.whiteLabelUrl,wl.hostBaseUrl,wl.playBackRestrictionId) FROM WhiteLabel AS wl WHERE wl.hostBaseUrl is not null AND wl.hostBaseUrl !='' ")
    List<WLBasicDetailsDto> getWhiteLabelBasicDetailsDto();

    @Cacheable(value = "findWhiteLabelByWhiteLabelUrl", key = "#p0",condition="#p0 != null",unless="#result == null")
    Optional<WhiteLabel> findWhiteLabelByWhiteLabelUrl(String whiteLabelUrl);

    @Query("SELECT w.hostBaseUrl FROM WhiteLabel w WHERE w.id = :whiteLabelId")
    String findHostBaseUrlByWhiteLabelId(@Param("whiteLabelId") Long whiteLabelId);

    @Query("SELECT new com.accelevents.dto.LoginUserWLOrgEventDetailsDto(w.id,w.firmName,w.whiteLabelUrl,w.headerLogoImage,w.twoFactorRequired,CONCAT('', s.role)) FROM WhiteLabel w " +
            "JOIN Staff s on w.id = s.whiteLabelId " +
            "WHERE s.userId=:userId " +
            "AND s.role IN (:staffRoles) " +
            "AND s.recordStatus=:recordStatus")
    List<LoginUserWLOrgEventDetailsDto> findLoginUserWlAccounts(@Param("userId") Long userId,
                                                                @Param("staffRoles") List<StaffRole> staffRoles,
                                                                @Param("recordStatus") RecordStatus recordStatus);
}
