package com.accelevents.ro.event.service;

import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.dto.LoginUserWLOrgEventDetailsDto;

import java.util.List;
import java.util.Optional;

public interface ROWhiteLabelService {

    Optional<WhiteLabel> getWhiteLabelById(Long whiteLabelId);

    WhiteLabel findByHostBaseUrl(String hostBaseUrl);

    WhiteLabel getWhiteLabelDetailsByHostBaseUrl(String hostBaseUrl);

    Optional<WhiteLabel> findWhiteLabelByUrlOp(String whiteLabelURL);

    WhiteLabel findWhiteLabelOrThrowNotFound(String whiteLabelURL);

    String getHostBaseUrlByWhiteLabelId(Long whiteLabelId);

    List<LoginUserWLOrgEventDetailsDto> findLoginUserWlOrgEventAccounts(User user, String role);
}
