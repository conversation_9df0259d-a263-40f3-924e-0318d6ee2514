package com.accelevents.ro.event.service.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumSessionStartStatus;
import com.accelevents.domain.enums.StreamProvider;
import com.accelevents.domain.session_speakers.*;
import com.accelevents.dto.LabelKeyDTO;
import com.accelevents.dto.DataTableResponseForSession;
import com.accelevents.dto.WaitingMediaDto;
import com.accelevents.ro.event.repository.ROSessionRepository;
import com.accelevents.ro.event.service.*;
import com.accelevents.ro.session.ROSessionDetailsRepoService;
import com.accelevents.ro.session.ROSessionRepoService;
import com.accelevents.ro.session.repo.ROMUXLivestreamAssetRepo;
import com.accelevents.ro.session.repo.ROSessionSpeakerRepo;
import com.accelevents.ro.session.repo.ROUserSessionRepo;
import com.accelevents.ro.session.repo.ROWorkshopRecordingAssetsRepo;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.repo.helper.ROEventTicketsRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.utils.JsonMapper;
import com.cloudinary.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.Constants.PRIVATE_IDS_UNDERSCORE;
import static com.accelevents.utils.Constants.PRIVATE_UNDERSCORE;
import static com.accelevents.utils.Constants.PUBLIC_UNDERSCORE;
import static com.accelevents.utils.Constants.STRING_UNDERSCORE;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class ROSessionServiceImpl implements ROSessionService {

    private static final Logger log = LoggerFactory.getLogger(ROSessionServiceImpl.class);
    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Autowired
    private ROSessionRepository roSessionRepository;
    @Autowired
    private ROSessionTagAndTrackService roSessionTagAndTrackService;
    @Autowired
    private ROKeyValueService roKeyValueService;
    @Autowired
    private ROSessionRepoService roSessionRepoService;
    @Autowired
    private GamificationCacheStoreService<String,Object> redisCacheService;
    @Autowired
    private ROSessionDetailsRepoService roSessionDetailsRepoService;
    @Autowired
    private ROEventTicketsRepoService roEventTicketsRepoService;
    @Autowired
    private ROSessionSpeakerRepo roSessionSpeakerRepo;
    @Autowired
    private ROUserSessionRepo roUserSessionRepo;
    @Autowired
    private ROVirtualEventService roVirtualEventService;
    @Autowired
    private ROUserSessionService roUserSessionService;
    @Autowired
    private ROWorkshopRecordingAssetsRepo roWorkshopRecordingAssetsRepo;
    @Autowired
    private ROMUXLivestreamAssetRepo romuxLivestreamAssetRepo;


    @Override
    public List<Long> findAllSessionIdByEventId(Long eventId) {
        return roSessionRepository.findAllIdByEventId(eventId);
    }

    @Override
    public List<Long> findAllSessionIdByEvent(Event event) {
        return this.findAllSessionIdByEventId(event.getEventId());
    }

    @Override
    public List<SessionTracksDTO> getSessionTracks(Event event) {
        List<Long> sessionIds = this.findAllSessionIdByEvent(event);
        List<SessionTagAndTrack> sessionTagAndTracks = roSessionTagAndTrackService.findBySessionIds(sessionIds);

        Map<Long, List<Long>> groupedTracksBySessionId = sessionTagAndTracks.stream()
                .collect(Collectors.groupingBy(
                        SessionTagAndTrack::getSessionId,
                        Collectors.mapping(SessionTagAndTrack::getTagOrTrackId, Collectors.toList())
                ));

        Map<Long, IdNameDto> trackIdToDtoMap = roKeyValueService.findAllByIds(
                sessionTagAndTracks.stream()
                        .map(SessionTagAndTrack::getTagOrTrackId)
                        .distinct() // Avoid duplicate lookups
                        .collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(KeyValue::getId, IdNameDto::new));

        return sessionIds.stream()
                .map(sessionId -> {
                    List<IdNameDto> trackDtos = groupedTracksBySessionId.getOrDefault(sessionId, Collections.emptyList())
                            .stream()
                            .map(trackIdToDtoMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    return new SessionTracksDTO(sessionId, trackDtos);
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelKeyDTO> findAllSessionIdsAndNameByEventId(Long eventId) {
        return roSessionRepository.findAllSessionIdsAndNameByEventId(eventId);
    }

    @Override
    public DataTableResponseForSession getRedisDisplayPortalSessionListWithPagination(Event event, User user, Pageable pageable, SessionFilter sessionFilter, boolean isAdminOrStaff, Boolean showPastAndUpcoming) {
        Long userId = user != null ? user.getUserId(): 0L;
        Long eventId = event.getEventId();
        log.info("getRedisDisplayPortalSessionList | event {} | user {}", eventId, userId);
        int sessionFilterHashCode = sessionFilter.hashCode();
        String basePrefix = SESSIONS_UNDERSCORE + eventId + STRING_UNDERSCORE;
        log.info("Redis key for user {} and event {} and filter hashcode {}",userId, eventId, sessionFilterHashCode);

        String redisAggregatedKey = basePrefix + USER_UNDERSCORE + userId + STRING_UNDERSCORE + sessionFilterHashCode +STRING_UNDERSCORE+ pageable.getPageNumber() +STRING_UNDERSCORE+ pageable.getPageSize();

        // Try fetching from Redis first
        DataTableResponseForSession cachedResponse = getAggregateDataFromRedis(redisAggregatedKey);
        if (Objects.nonNull(cachedResponse)) {
            log.info("return from the redis cached data");
            return cachedResponse;
        }

        final StopWatch stopwatch = new StopWatch();

        String redisPublicSessionKey = basePrefix + PUBLIC_UNDERSCORE + sessionFilterHashCode;
        String redisPrivateSessionKey = basePrefix + PRIVATE_UNDERSCORE + userId + STRING_UNDERSCORE + sessionFilterHashCode;
        String userPrivateSessionIdsKey = basePrefix + PRIVATE_IDS_UNDERSCORE + userId;
        String userAllSessionKey = basePrefix + PUBLIC_UNDERSCORE + PRIVATE_UNDERSCORE + userId + STRING_UNDERSCORE + sessionFilterHashCode;


        PastUpComingDto pastUpComingDto = getPastUpComingDtoDetail(event, sessionFilter, showPastAndUpcoming);
        DataTableResponseForSession dataTableResponseForPublicSession = getAggregateDataFromRedis(redisPublicSessionKey);
        if (Objects.isNull(dataTableResponseForPublicSession)) {
            stopwatch.start();
            log.info("Not found in redis public sessions of the user {} and event id {}", userId, eventId );

            //fetch all the public sessions (page=0 and size = infinite)
            Page<Session> sessionsPage = roSessionRepoService.getAllPublicSessionByEventId(event, pageable, sessionFilter);
            dataTableResponseForPublicSession = getDisplayPortalSessionDataTable(event, sessionsPage, pastUpComingDto);
            redisCacheService.set(redisPublicSessionKey, JsonMapper.convertToString(dataTableResponseForPublicSession), 5, TimeUnit.MINUTES);
            stopwatch.stop();
            log.info("Execution time to fetch public session {}", stopwatch.getTotalTimeMillis());
        }

        if(userId == 0L){
            //return public sessions only with pagination, if the loggedin user is not present
            DataTableResponseForSession publicSessionsWithoutUser = processSessionsDataWithPagination(dataTableResponseForPublicSession, pageable);
            redisCacheService.set(redisAggregatedKey,JsonMapper.convertToString(publicSessionsWithoutUser), 5, TimeUnit.MINUTES);
            return publicSessionsWithoutUser;
        }

        DataTableResponseForSession allSessionsOfUser = getAggregateDataFromRedis(userAllSessionKey);
        if(!Objects.isNull(allSessionsOfUser)) {
            //return public + private sessions if the data is present
            return processSessionsDataWithPagination(allSessionsOfUser, pageable);
        }

        // Fetch private session IDs from Redis or compute if missing
        List<Long> privateSessionIdsOfUser = getPrivateSessionIdsFromRedis(userPrivateSessionIdsKey);
        if (Objects.isNull(privateSessionIdsOfUser)) {
            log.info("Not found in redis private sessions id of the user {} and event id {}", userId, eventId );
            privateSessionIdsOfUser = fetchHiddenSessionsOfUser(event, user, isAdminOrStaff);
            redisCacheService.set(userPrivateSessionIdsKey, JsonMapper.convertToString(privateSessionIdsOfUser), 5, TimeUnit.MINUTES);
        }

        log.info("fetch private session Ids {}",  privateSessionIdsOfUser);

        // Fetch private sessions from Redis or DB
        DataTableResponseForSession dataTableResponseForPrivateSession = getAggregateDataFromRedis(redisPrivateSessionKey);
        if (Objects.isNull(dataTableResponseForPrivateSession) && !CollectionUtils.isEmpty(privateSessionIdsOfUser)) {
            stopwatch.start();
            log.info("Not found in redis private sessions of the user {} and event id {}", userId, eventId);
            List<Session> userSpecificSessions = roSessionRepository.getAllSessionByIdsWithOrderByStartTimeEndTimeAndPosition(privateSessionIdsOfUser, eventId);

            //fetch all the private sessions for the user (page=0 and size = infinite)
            Page<Session>  privateSessionPage = roSessionRepoService.getAllCachePrivateSessionByEventId(event, pageable, sessionFilter, userSpecificSessions);
            dataTableResponseForPrivateSession = getDisplayPortalSessionDataTable(event, privateSessionPage, pastUpComingDto);
            redisCacheService.set(redisPrivateSessionKey, JsonMapper.convertToString(dataTableResponseForPrivateSession), 5, TimeUnit.MINUTES);
            stopwatch.stop();
            log.info("Execution time to fetch private sessions {}", stopwatch.getTotalTimeMillis());
        }


        // Merge & Sort sessions
        if(null != dataTableResponseForPrivateSession) {

            //merge and sort public+private if the user has private sessions
            DataTableResponseForSession dataTableResponseForPublicAndPrivateSession = mergeAndSortSessions(dataTableResponseForPublicSession, dataTableResponseForPrivateSession, pastUpComingDto);

            //set the sorted list of private + public list so no need to sort + merge everytime, we just get from the redis and return the data
            redisCacheService.set(userAllSessionKey, JsonMapper.convertToString(dataTableResponseForPublicAndPrivateSession), 5, TimeUnit.MINUTES);

            DataTableResponseForSession responseWithPagination = processSessionsDataWithPagination(dataTableResponseForPublicAndPrivateSession, pageable);

            // Store aggregated result in Redis
            redisCacheService.set(redisAggregatedKey, JsonMapper.convertToString(responseWithPagination), 5, TimeUnit.MINUTES);
            return responseWithPagination;
        }else{
            DataTableResponseForSession publicSessionsWithUser = processSessionsDataWithPagination(dataTableResponseForPublicSession, pageable);
            redisCacheService.set(redisAggregatedKey,JsonMapper.convertToString(publicSessionsWithUser), 5, TimeUnit.MINUTES);
            return publicSessionsWithUser;
        }
    }

    // Optimized getAggregateDataFromRedis
    private DataTableResponseForSession getAggregateDataFromRedis(String key) {
        String json = (String) redisCacheService.get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JsonMapper.stringtoObject(json, DataTableResponseForSession.class);
        } catch (Exception e) {
            log.error("Error deserializing aggregate data from Redis for key: {}", key, e);
            return null;
        }
    }


    public PastUpComingDto getPastUpComingDtoDetail(Event event, SessionFilter sessionFilter, Boolean showPastAndUpcoming) {
        List<SessionEndDateTypeDto> sessions = roSessionRepository.getSessionEndDateTypeByEvent(event);
        Date now = new Date();

        boolean isPastSession = false;
        boolean isUpComingSession = false;

        Stream<SessionEndDateTypeDto> sessionStream = sessions.stream();
        if (StringUtils.isNotBlank(sessionFilter.getSessionFormat())) {
            String sessionFormat = sessionFilter.getSessionFormat();
            sessionStream = sessionStream.filter(s -> s.getFormat() != null && s.getFormat().name().equals(sessionFormat));
        }

        for (SessionEndDateTypeDto session : sessionStream.collect(Collectors.toList())) {
            boolean isLive = isAcceleventSessionLive(session);
            if(session != null && session.getEndTime() != null){
                Date endTime = session.getEndTime();
                if (!isLive && endTime.before(now)) {
                    isPastSession = true;
                }
                if (isLive || endTime.after(now)) {
                    isUpComingSession = true;
                }

                if (isPastSession && isUpComingSession) {
                    break;
                }
            }
        }
        if (sessionFilter.getPast() == null && showPastAndUpcoming == null) {
            sessionFilter.setShowPastAndUpcoming(!isUpComingSession && isPastSession);
        }

        return new PastUpComingDto(isPastSession, isUpComingSession);
    }

    public DataTableResponseForSession getDisplayPortalSessionDataTable(Event event, Page<Session> sessionsPage, PastUpComingDto pastUpComingDto) {
        List<SessionDetails> sessionDetails = roSessionDetailsRepoService.getAllSessionDetailsByEventId(event.getEventId());
        Map<Long, List<SessionDetails>> sessionDetailsMap = sessionDetails.stream().collect(Collectors.groupingBy(e-> e.getSession().getId()));
        Map<Long,Boolean> sessionBookmarkCapacity = countCapacityReachedForSessions(event, sessionsPage);
        Map<Long, Boolean> sessionViewRecording = sessionViewRecordingsButton(event, sessionsPage);
        List<DisplayPortalSessionDTO> displayPortalSessionDTOS = sessionsPage.getContent().stream()
                .map(session -> createDisplayPortalSessionDTO(session, event, sessionDetailsMap, sessionBookmarkCapacity.get(session.getId()),sessionViewRecording.getOrDefault(session.getId(),false)))
                .collect(toList());

        DataTableResponseForSession dataTableResponseForSession = new DataTableResponseForSession();
        dataTableResponseForSession.setRecordsTotal(sessionsPage.getTotalElements());
        dataTableResponseForSession.setRecordsFiltered(sessionsPage.getContent().size());
        dataTableResponseForSession.setData(displayPortalSessionDTOS);
        dataTableResponseForSession.setPastSession(pastUpComingDto.isPastSession());
        dataTableResponseForSession.setUpComingSession(pastUpComingDto.isUpComingSession());

        return dataTableResponseForSession;
    }

    private DataTableResponseForSession processSessionsDataWithPagination(DataTableResponseForSession dataTableResponseForSession, Pageable pageable) {
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<DisplayPortalSessionDTO> allSessions = new ArrayList<>();
        int totalSize = dataTableResponseForSession.getData().size();

        int startIndex = pageable.getPageNumber() * pageable.getPageSize();
        int endIndex = Math.min(startIndex + pageable.getPageSize(), totalSize);

        if (startIndex < totalSize) { // Ensure startIndex is within bounds
            allSessions.addAll(convertToDTOList(dataTableResponseForSession.getData().subList(startIndex, endIndex)));
        }
        DataTableResponseForSession paginatedResponse = new DataTableResponseForSession();
        paginatedResponse.setData(allSessions);
        paginatedResponse.setRecordsFiltered(allSessions.size());
        paginatedResponse.setRecordsTotal(dataTableResponseForSession.getRecordsTotal());
        paginatedResponse.setPastSession(dataTableResponseForSession.isPastSession());
        paginatedResponse.setUpComingSession(dataTableResponseForSession.isUpComingSession());
        stopWatch.stop();
        log.info("Execution time of the pagination {}", stopWatch.getTotalTimeMillis());
        return paginatedResponse;
    }

    // Optimized getPrivateSessionIdsFromRedis
    private List<Long> getPrivateSessionIdsFromRedis(String key) {
        String json = (String) redisCacheService.get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return new ObjectMapper().readValue(json, new TypeReference<List<Long>>() {});
        } catch (JsonProcessingException e) {
            log.error("Error deserializing private session IDs from Redis for key: {}", key, e);
            return Collections.emptyList();
        }
    }

    private List<Long> fetchHiddenSessionsOfUser(Event event, User user, boolean isAdminOrStaff) {
        // Get hidden sessions if the user is not an admin or staff
        List<Long> hiddenSessionIds = isAdminOrStaff
                ? roSessionRepository.getAllHiddenSessionOfEventId(event.getEventId()) : new ArrayList<>();
        if (user != null) {
            // Get user ticket types if visibility restrictions apply
            List<Long> userTicketTypes = (roSessionRepoService.allSessionVisibleForUser(user, event) && isAdminOrStaff)
                    ? new ArrayList<>()
                    : roEventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(event, user);

            // Fetch sessions hidden from attendees
            List<Session> hiddenFromAttendeesSessions = roSessionRepository.getAllSessionsByEventIdAndHideFromAttendeesAndStatusNotHidden(event.getEventId());

            for (Session session : hiddenFromAttendeesSessions) {
                // Skip processing if userTicketTypes is empty and session has no ticket type restrictions
                if (userTicketTypes.isEmpty() || isBlank(session.getTicketTypesThatCanBeRegistered())) {
                    hiddenSessionIds.add(session.getId());
                    continue;
                }

                // Convert ticket type string to list of IDs
                List<Long> sessionTicketTypeIds = Arrays.stream(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                // Add session to hidden list only if there's an intersection with userTicketTypes
                if (!Collections.disjoint(sessionTicketTypeIds, userTicketTypes)) {
                    hiddenSessionIds.add(session.getId());
                }
            }
        }


        //filterPrivateSessions
        List<Long> privateSessionIds = roSessionRepository.getAllPrivateSessionByEventIdAndStatusNotHidden(event.getEventId());
        if (!isAdminOrStaff && !CollectionUtils.isEmpty(privateSessionIds) && user != null) {
            Set<Long> userAccessibleSessionIds = new HashSet<>(roUserSessionRepo.findSessionIdByUserIdAndEventIdAndSessionIdIn(
                    user.getUserId(), event.getEventId(), privateSessionIds));

            userAccessibleSessionIds.addAll(roSessionSpeakerRepo.findAllSessionIdBySpeakerUserId(user.getUserId(), event.getEventId()));


            privateSessionIds.stream()
                    .filter(userAccessibleSessionIds::contains)
                    .forEach(hiddenSessionIds::add);
        } else {
            hiddenSessionIds.addAll(privateSessionIds);
        }

        return hiddenSessionIds;
    }

    // Optimized mergeAndSortSessions
    public DataTableResponseForSession mergeAndSortSessions(DataTableResponseForSession publicSessions, DataTableResponseForSession privateSessions,PastUpComingDto pastUpComingDto) {
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<DisplayPortalSessionDTO> allSessions = new ArrayList<>();
        allSessions.addAll(convertToDTOList(publicSessions.getData()));
        allSessions.addAll(convertToDTOList(privateSessions.getData()));

        allSessions.sort(Comparator.comparing(DisplayPortalSessionDTO::getStartTime)
                .thenComparing(DisplayPortalSessionDTO::getEndTime)
                .thenComparing(DisplayPortalSessionDTO::getPosition, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DisplayPortalSessionDTO::getTitle));

        DataTableResponseForSession dataTableResponseForSession = new DataTableResponseForSession();
        dataTableResponseForSession.setData(allSessions);
        dataTableResponseForSession.setRecordsFiltered(allSessions.size());
        dataTableResponseForSession.setRecordsTotal(allSessions.size());
        dataTableResponseForSession.setPastSession(pastUpComingDto.isPastSession());
        dataTableResponseForSession.setUpComingSession(pastUpComingDto.isUpComingSession());
        stopWatch.stop();
        log.info("Execution time Merge and sort public and private session {}", stopWatch.getTotalTimeMillis());
        return dataTableResponseForSession;
    }

    private boolean isAcceleventSessionLive(SessionEndDateTypeDto session) {
        if (null != session && null != session.getStreamProvider() &&
                StreamProvider.ACCELEVENTS.name().equals(session.getStreamProvider()) &&
                session.getSessionStartStatus() != null && session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED.name())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    private Map<Long,Boolean> countCapacityReachedForSessions(Event event, Page<Session> sessionsPage) {
        List<Session> sessions = sessionsPage.getContent();
        List<Long> sessionIds = sessions.stream().map(Session::getId).collect(toList());

        boolean isAllowSessionBookmarkCapacity = roVirtualEventService.hasSessionBookmarkCapacity(event.getEventId());
        Map<Long,Boolean> sessionBookmarkCapacity;
        log.info("countCapacityReachedForSessions getSessionInfoById isAllowSessionBookmarkCapacity {} ",isAllowSessionBookmarkCapacity);
        if (isAllowSessionBookmarkCapacity) {
            List<Long> workshopSessionIds = sessions.stream().filter(e->e.getFormat().equals(EnumSessionFormat.WORKSHOP)).map(Session::getId).collect(toList());
            List<IdCountDto> registerCount;
            if(!CollectionUtils.isEmpty(workshopSessionIds)) {
                registerCount = roUserSessionService.getSessionStatsForIdsIn(workshopSessionIds);
                sessionIds.removeAll(workshopSessionIds);
                registerCount.addAll(roUserSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds, event.getEventId()));
                sessionIds.addAll(workshopSessionIds);
            }else{
                registerCount = roUserSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds, event.getEventId());
            }
            Map<Long, Long> registerSessionStats = getMap(registerCount);
            log.info("countCapacityReachedForSessions getSessionInfoById registerSessionStats {} ",registerSessionStats);
            sessionBookmarkCapacity = sessions.stream()
                    .collect(Collectors.toMap(
                            Session::getId,
                            session -> {
                                Long registeredCount = registerSessionStats.getOrDefault(session.getId(), 0L);
                                return registeredCount != 0 && session.getCapacity() != 0 && registeredCount >= session.getCapacity();
                            }
                    ));
        } else {
            log.info("countCapacityReachedForSessions getSessionInfoById session bookmark capacity reached is false for all the session of the event {}",event.getEventId());
            sessionBookmarkCapacity = sessions.stream()
                    .collect(Collectors.toMap(Session::getId, session -> false));
        }
        log.info("countCapacityReachedForSessions getSessionInfoById sessionBookmarkCapacity {} ",sessionBookmarkCapacity);
        return sessionBookmarkCapacity;
    }

    private Map<Long, Boolean> sessionViewRecordingsButton(Event event, Page<Session> sessionsPage) {
        List<Long> sessionIds = sessionsPage.getContent().stream()
                .map(Session::getId)
                .collect(Collectors.toList());

        List<Object[]> recordingsWithSessionIds = romuxLivestreamAssetRepo.findSessionRecordingsBySessionIdsAndAssetTypeAndVisible(
                sessionIds, AssetType.SESSION_ASSET, true);

        List<Object[]> workshopRecordingsSessionIds = roWorkshopRecordingAssetsRepo.findWorkshopRecordingsByVisibleStatusAndSessionIdsIn(
                sessionIds, true);

        Stream<Object[]> combinedResults = Stream.concat(recordingsWithSessionIds.stream(), workshopRecordingsSessionIds.stream());

        return combinedResults.collect(Collectors.toMap(
                row -> (Long) row[0],          // sessionId
                row -> (Boolean) row[1],       // isPresent
                (existing, replacement) -> existing
        ));
    }

    private DisplayPortalSessionDTO createDisplayPortalSessionDTO(Session session, Event event, Map<Long, List<SessionDetails>> sessionDetailsMap, Boolean sessionBookmarkCapacityReached, Boolean isViewRecording) {
        List<SessionDetails> sessionDetails = sessionDetailsMap.getOrDefault(session.getId(), List.of());
        SessionDetails details = sessionDetails.isEmpty() ? null : sessionDetails.get(0);

        return new DisplayPortalSessionDTO(
                session,
                event,
                details != null && details.isRecordSession(),
                convertWaitingMedia(details),
                details != null && details.isEnableSessionWaitingMedia(),
                details != null && details.getHideVideoControls(),
                sessionBookmarkCapacityReached,
                isViewRecording
        );
    }

    private WaitingMediaDto convertWaitingMedia(SessionDetails details) {
        if (details == null || details.getWaitingMedia() == null) {
            return new WaitingMediaDto();
        }
        return WaitingMediaDto.convertJSONToObject(details.getWaitingMedia());
    }

    private List<DisplayPortalSessionDTO> convertToDTOList(List<?> data) {
        if (data == null || data.isEmpty()) {
            return Collections.emptyList();
        }
        return data.stream()
                .map(item -> {
                    if (item instanceof LinkedHashMap) {
                        return MAPPER.convertValue(item, DisplayPortalSessionDTO.class);
                    }
                    return (DisplayPortalSessionDTO) item;
                })
                .collect(Collectors.toList());
    }

    private Map<Long, Long> getMap(List<IdCountDto> idCountDtos1) {
        return idCountDtos1.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount));
    }
}
