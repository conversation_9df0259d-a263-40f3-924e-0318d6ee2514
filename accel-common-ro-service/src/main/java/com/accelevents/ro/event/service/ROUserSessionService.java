package com.accelevents.ro.event.service;

import com.accelevents.domain.User;
import com.accelevents.session_speakers.dto.IdCountDto;

import java.util.List;
import java.util.Map;

public interface ROUserSessionService {
    Boolean checkIfUserIsCheckinInSession(Long userId,Long eventId,Long sessionId);

    Map<Long, List<Integer>> findAllSessionUserIdsByEventId(Long eventId);

    List<IdCountDto> getSessionStatsForIdsIn(List<Long> sessionIds);

    List<IdCountDto> getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(List<Long> sessionIds, long eventId);
}
