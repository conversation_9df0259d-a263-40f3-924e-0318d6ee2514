package com.accelevents.ro.event.repository;

import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.LabelKeyDTO;
import com.accelevents.session_speakers.dto.SessionEndDateTypeDto;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional(readOnly = true)
public interface ROSessionRepository extends org.springframework.data.repository.Repository<Session, Long> {

    @Query("SELECT s.id FROM Session s WHERE s.eventId = :eventId")
    List<Long> findAllIdByEventId(@Param("eventId") Long eventId);

    @Query("SELECT session.id, session.title, session.event.name FROM Session as session WHERE session.eventId IN (:listOfEventIds) and session.title like %:title%")
    List<Object[]> getSessionNameByEventIdsAndTitleLike(@Param("listOfEventIds") List<Long> listOfEventIds,@Param("title") String title);

    @Query(value = "select new com.accelevents.dto.LabelKeyDTO(s.id,s.title) from Session s where s.eventId=:eventId")
    List<LabelKeyDTO> findAllSessionIdsAndNameByEventId(@Param("eventId") Long eventId);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId AND id IN (:sessionIds) ORDER BY session.startTime ASC, session.endTime ASC, session.position ASC, BIN(session.title) ASC")
    List<Session> getAllSessionByIdsWithOrderByStartTimeEndTimeAndPosition(@Param("sessionIds") List<Long> sessionIds,@Param("eventId") Long eventId);

    @Cacheable(value = "getSessionEndDateTypeByEvent", key = "#p0.eventId", unless="#result == null")
    @Query("SELECT new com.accelevents.session_speakers.dto.SessionEndDateTypeDto(session.id,session.title,session.format,session.endTime,session.sessionStartStatus,session.streamProvider) FROM Session session WHERE session.event=:event")
    List<SessionEndDateTypeDto> getSessionEndDateTypeByEvent(@Param("event") Event event);

    @Cacheable(value = "getAllHiddenSessionOfEventId", key = "#p0", unless="#result == null")
    @Query("SELECT s.id FROM Session s WHERE s.eventId= :eventId AND s.status = 'HIDDEN'")
    List<Long> getAllHiddenSessionOfEventId(@Param("eventId") long eventId);

    @Cacheable(value = "getAllSessionsByEventIdAndHideFromAttendees", key = "#eventId", unless = "#result == null")
    @Query("SELECT s FROM Session s WHERE s.eventId = :eventId AND s.hideSessionFromAttendees = true AND s.status <> 'HIDDEN' AND s.sessionVisibilityType = 'PUBLIC'")
    List<Session> getAllSessionsByEventIdAndHideFromAttendeesAndStatusNotHidden(@Param("eventId") long eventId);

    @Cacheable(value = "getAllPrivateSessionByEventId", key = "#p0", unless = "#result == null")
    @Query("SELECT s.id FROM Session s WHERE s.eventId = :eventId AND s.sessionVisibilityType = 'PRIVATE' AND s.status <> 'HIDDEN'")
    List<Long> getAllPrivateSessionByEventIdAndStatusNotHidden(@Param("eventId") long eventId);

    @Query("SELECT DISTINCT session.id FROM Session session " +
            " LEFT JOIN session.sessionTagAndTracks stt " +
            " LEFT JOIN session.sessionSpeakers sessionSpeaker " +
            " LEFT JOIN sessionSpeaker.speaker speaker " +
            " WHERE session.eventId=:eventId " +
            " AND ( stt.tagOrTrackId IN (:tagAndTrackIds) OR 0 IN (:tagAndTrackIds) )"+
            " AND	" +
            "	(" +
            "		( coalesce(:searchString, 1) = 1 " +
            "		  OR " +
            "		 	(session.title like %:searchString% " +
            "			OR session.description like %:searchString% " +
            "			OR speaker.firstName like %:searchString% " +
            "			OR speaker.lastName like %:searchString% " +
            "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
            "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
            "			 )" +
            "   	)" +
            "	)  " )
    List<Long> filterSession(@Param("eventId") Long eventId,
                             @Param("tagAndTrackIds") List<Long> tagAndTrackIds,
                             @Param("searchString") String searchString);

    @Query("SELECT DISTINCT session.id FROM Session session " +
            " LEFT JOIN session.sessionTagAndTracks stt " +
            " WHERE session.eventId=:eventId " +
            " AND ( stt.tagOrTrackId IN (:tagAndTrackIds) OR 0 IN (:tagAndTrackIds) ) ")
    List<Long> filterSessionWithoutSearch(@Param("eventId") Long eventId,
                                          @Param("tagAndTrackIds") List<Long> tagAndTrackIds);


    @Cacheable(value = "SessionByEventAndFormat", key = "#p0", unless="#result == null")
    @Query("SELECT session FROM Session as session " +
            " LEFT JOIN FETCH session.sessionLocation" +
            " WHERE session.eventId=:eventId" +
            " ORDER BY session.startTime ASC, session.endTime ASC, session.position ASC, BIN(session.title) ASC")
    List<Session> getSessionByEvent(@Param("eventId") Long eventId);

    @Query("SELECT s.id FROM Session s WHERE s.eventId = :eventId")
    List<Long> findSessionIdsByEventId(@Param("eventId") Long eventId);
}
