package com.accelevents.ro.event.service;

import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.dto.LabelKeyDTO;
import com.accelevents.domain.User;
import com.accelevents.dto.DataTableResponseForSession;
import com.accelevents.session_speakers.dto.SessionFilter;
import com.accelevents.session_speakers.dto.SessionTracksDTO;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ROSessionService {

    List<Long> findAllSessionIdByEventId(Long eventId);

    List<Long> findAllSessionIdByEvent(Event event);

    List<SessionTracksDTO> getSessionTracks(Event event);

    List<LabelKeyDTO> findAllSessionIdsAndNameByEventId(Long eventId);

    DataTableResponseForSession getRedisDisplayPortalSessionListWithPagination(Event event, User user, Pageable pageable, SessionFilter sessionFilter, boolean isAdminOrStaff, Boolean showPastAndUpcoming);
}
