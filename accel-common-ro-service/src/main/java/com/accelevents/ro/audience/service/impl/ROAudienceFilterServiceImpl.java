package com.accelevents.ro.audience.service.impl;

import com.accelevents.column.selection.dto.ColumnDto;
import com.accelevents.column.selection.dto.UserColumnSelectionDto;
import com.accelevents.common.dto.EventNameLookUpDto;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.AnalyticsArea;
import com.accelevents.domain.enums.AnalyticsSource;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.audience.AudienceAnalyticsQueryBuilder;
import com.accelevents.dto.AudienceConstants;
import com.accelevents.ro.audience.repository.ROAudienceFilterEventRepo;
import com.accelevents.ro.audience.repository.ROAudienceFilterMasterRepo;
import com.accelevents.ro.audience.repository.ROAudienceFilterRepo;
import com.accelevents.ro.audience.service.ROAnalyticsColumnMasterService;
import com.accelevents.ro.audience.service.ROAnalyticsUserColumnSelectionService;
import com.accelevents.ro.audience.service.ROAudienceFilterService;
import com.accelevents.ro.event.repository.ROEventRepository;
import com.accelevents.ro.event.repository.ROSessionRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROOrganizerService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.eventTicket.coupons.repository.ROTicketingCouponRepository;
import com.accelevents.ro.eventTicket.repo.ROTicketingTypeRepo;
import com.accelevents.services.audience.Audience;
import com.accelevents.services.audience.filter.AudienceFilter;
import com.accelevents.services.audience.filter.AudienceFilterDTO;
import com.accelevents.services.audience.filter.AudienceFilterMasterDTO;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ROAudienceFilterServiceImpl implements ROAudienceFilterService {

    private static final Logger log = LoggerFactory.getLogger(ROAudienceFilterService.class);

    @Autowired
    private ROOrganizerService roOrganizerService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private ROAudienceFilterRepo roAudienceFilterRepo;
    @Autowired
    private ROAudienceFilterEventRepo roAudienceFilterEventRepo;
    @Autowired
    private RORDSQueryRunnerService roRDSQueryRunnerService;
    @Autowired
    private ROAudienceFilterColumnSelectionService roAudienceFilterColumnSelectionService;
    @Autowired
    private ROAnalyticsColumnMasterService roAnalyticsColumnMasterService;
    @Autowired
    private ROAnalyticsUserColumnSelectionService roAnalyticsUserColumnSelectionService;
    @Autowired
    private ROTicketingTypeRepo roTicketingTypeRepo;
    @Autowired
    private ROEventRepository roEventRepository;
    @Autowired
    private ROTicketingCouponRepository roTicketingCouponRepository;
    @Autowired
    private ROSessionRepository roSessionRepository;
    @Autowired
    private ROAudienceFilterMasterRepo roAudienceFilterMasterRepo;

    @Override
    public AudienceFilter findById(Long id) {
        Optional<AudienceFilter> optionalAudienceFilter =  roAudienceFilterRepo.findById(id);
        if(optionalAudienceFilter.isPresent()){
            return optionalAudienceFilter.get();
        }
        else{
            throw  new NotFoundException(NotFoundException.NotFound.AUDIENCE_FILTER_NOT_FOUND);
        }
    }

    @Override
    public List<AudienceFilterDTO> listAudienceFilters(String source, String sourceURL) {
        List<AudienceFilterDTO> audienceFilterDTOList = new ArrayList<>();
        List<Long> eventIds = new ArrayList<>();


        Long sourceId = getEventIdsFromSource(source, sourceURL,eventIds);
        roAudienceFilterRepo.findBySourceAndSourceId(source, sourceId).forEach(audienceFilter -> audienceFilterDTOList.add(new AudienceFilterDTO(audienceFilter)));
        audienceFilterDTOList.add(0,createEveryoneFilter());
        getAudienceCount(audienceFilterDTOList,eventIds,null);

        return audienceFilterDTOList;
    }

    private Long getEventIdsFromSource(String source, String sourceURL, List<Long> eventIds) {
        if(source.equalsIgnoreCase(AudienceConstants.AudienceFilterSourceType.ORGANIZER.name())){
            Optional<Organizer> organizerOptional = roOrganizerService.findOrganizerByOrganizerPageURLOp(sourceURL);
            if(organizerOptional.isPresent()){
                Long sourceId = organizerOptional.get().getId();
                eventIds.addAll(roEventService.findEventIdsByOrganizerId(sourceId));
                return sourceId;
            }
        }
        else if(source.equalsIgnoreCase(AudienceConstants.AudienceFilterSourceType.WHITELABEL.name())){
            Optional<WhiteLabel> whiteLabelOptional = roWhiteLabelService.findWhiteLabelByUrlOp(sourceURL);
            if(whiteLabelOptional.isPresent()){
                Long sourceId = whiteLabelOptional.get().getId();
                eventIds.addAll(roEventService.findEventIdsByWhiteLabelId(sourceId));
                return sourceId;
            }
        }
        return null;
    }

    private AudienceFilterDTO createEveryoneFilter(){
        AudienceFilterDTO allAudienceProfile = new AudienceFilterDTO();
        allAudienceProfile.setFilterJson("{}");
        allAudienceProfile.setName("Everyone");
        allAudienceProfile.setId(-1L);
        return allAudienceProfile;
    }

    private void getAudienceCount(List<AudienceFilterDTO> audienceFilterDTOList, List<Long> eventIds,String search){
        for(AudienceFilterDTO audienceFilterDTO : audienceFilterDTOList){
            if(!eventIds.isEmpty()){
                String query = AudienceAnalyticsQueryBuilder.getAudienceCountQuery(convertToJSONObject(audienceFilterDTO.getFilterJson()), eventIds, -1L, -1L, Boolean.FALSE,search);
                List<AudienceFilterDTO> audienceFilterDTOS = roRDSQueryRunnerService.executeQuery(query, AudienceFilterDTO.class);
                if(audienceFilterDTOS != null && audienceFilterDTOS.size()>0 && audienceFilterDTOS.get(0).getTotalAudience() !=null){
                    audienceFilterDTO.setTotalAudience(audienceFilterDTOS.get(0).getTotalAudience());
                }
                else {
                    audienceFilterDTO.setTotalAudience(0L);
                }
            }
            else{
                audienceFilterDTO.setTotalAudience(0L);
            }
        }
    }

    private JSONObject convertToJSONObject(String value){

        try{
            return new JSONObject(value);
        }
        catch (Exception exception){
            log.warn("failed to create json object from string | string {} | exception {}", value, exception.getMessage());
            return new JSONObject();
        }
    }

    @Override
    public <T> DataTableResponse applyAudienceFilterWithColumn(Long filterId, AudienceFilterDTO audienceFilterDTO, String source, String sourceURL, Long offset, Long size, Class<T> valueType) {
        List<Long> eventIds = new ArrayList<>();

        JSONObject condition = new JSONObject();
        DataTableResponse dataTableResponse = new DataTableResponse();
        String query = AudienceAnalyticsQueryBuilder.prepareSelectQuery();

        if(audienceFilterDTO != null && StringUtils.isNoneEmpty(audienceFilterDTO.getFilterJson())){
            condition = convertToJSONObject(audienceFilterDTO.getFilterJson());
        }
        else if(filterId != -1){
            audienceFilterDTO = new AudienceFilterDTO(this.findById(filterId));
            condition = convertToJSONObject(audienceFilterDTO.getFilterJson());
        }

        if (audienceFilterDTO == null){
            audienceFilterDTO = createEveryoneFilter();
        }

        getEventIdsFromSource(source, sourceURL, eventIds);
        if(eventIds.size() > 0){
            query = AudienceAnalyticsQueryBuilder.prepareDynamicQuery(query, condition, eventIds, offset, size, Boolean.TRUE,null,null,null).trim();

            List<AudienceFilterDTO> audienceFilterDTOS = new ArrayList<>();
            audienceFilterDTOS.add(audienceFilterDTO);

            getAudienceCount(audienceFilterDTOS,eventIds,null);

            List<T>  rows= roRDSQueryRunnerService.executeQuery(query, valueType);
            List<Long> userIds = rows.stream().map(row->(((Audience)row).getUserId())).collect(Collectors.toList());

            roAudienceFilterColumnSelectionService.setColumnSelectionValues(rows,
                    Collections.emptyMap(), Collections.emptyMap(), Collections.emptyMap(), Collections.emptyMap());

            dataTableResponse.setData(rows);
            dataTableResponse.setRecordsTotal(audienceFilterDTO.getTotalAudience() == null ? 0L: audienceFilterDTO.getTotalAudience());
            dataTableResponse.setRecordsFiltered(rows.size());
        }
        return dataTableResponse;
    }

    /**
     * Method to fetch audience data with custom user column selected values
     */
    @Override
    public DataTableResponse applyAudienceFilterWithColumn(Long filterId, AudienceFilterDTO audienceFilterDTO, String source, String sourceURL, Long offset, Long size, User loggedInUser,String sortBy, String sortDirection,String search) {

        List<Long> eventIds = new ArrayList<>();

        JSONObject condition = new JSONObject();
        DataTableResponse dataTableResponse = new DataTableResponse();
        Long orgIdOrWhiteLabelId = getEventIdsFromSource(source, sourceURL, eventIds);

        List<String> defaultColumns = new ArrayList<>();
        List<String> customiseColumns = new ArrayList<>();
        List<ColumnDto> columnDtoList;
        if (audienceFilterDTO != null && StringUtils.isNoneEmpty(audienceFilterDTO.getFilterJson())) {
            columnDtoList = roAnalyticsColumnMasterService.getDefaultColumns(AnalyticsArea.AUDIENCE_FILTER);
        } else {
            UserColumnSelectionDto columnSelectionDto = roAnalyticsUserColumnSelectionService.getDefaultOrUserColumnSelection(AnalyticsSource.valueOf(source), orgIdOrWhiteLabelId, AnalyticsArea.AUDIENCE_FILTER, loggedInUser.getUserId(), filterId);
            columnDtoList = columnSelectionDto.getColumnSelection();
        }
        roAnalyticsUserColumnSelectionService.setDefaultAndCustomiseColumns(defaultColumns, customiseColumns, Collections.emptyList(),Collections.emptyList(), columnDtoList);
        log.info("Column selection fetched for userId {}, columns {}", loggedInUser.getUserId(), defaultColumns);

        if (audienceFilterDTO != null && StringUtils.isNoneEmpty(audienceFilterDTO.getFilterJson())) {
            condition = convertToJSONObject(audienceFilterDTO.getFilterJson());
        } else if (filterId != -1) {
            audienceFilterDTO = new AudienceFilterDTO(this.findById(filterId));
            condition = convertToJSONObject(audienceFilterDTO.getFilterJson());
        }

        if (audienceFilterDTO == null) {
            audienceFilterDTO = createEveryoneFilter();
        }
        String query = AudienceAnalyticsQueryBuilder.prepareSelectQuery(defaultColumns);

        if (eventIds.size() > 0) {
            query = AudienceAnalyticsQueryBuilder.prepareDynamicQuery(query, condition, eventIds, offset, size, Boolean.TRUE, sortBy, sortDirection,search).trim();

            List<AudienceFilterDTO> audienceFilterDTOS = new ArrayList<>();
            audienceFilterDTOS.add(audienceFilterDTO);

            getAudienceCount(audienceFilterDTOS, eventIds,search);
            List<Audience>  audienceList = roRDSQueryRunnerService.executeQuery(query, Audience.class);
            log.info("Successfully data fetched form read-only result size {}", audienceList.size());
            List<Long> userIds = audienceList.stream().map(Audience::getUserId).collect(Collectors.toList());

            roAudienceFilterColumnSelectionService.setColumnSelectionData(audienceList, loggedInUser, orgIdOrWhiteLabelId, source, eventIds, userIds, customiseColumns);

            dataTableResponse.setData(audienceList);
            dataTableResponse.setRecordsTotal(audienceFilterDTO.getTotalAudience() == null ? 0L : audienceFilterDTO.getTotalAudience());
            dataTableResponse.setRecordsFiltered(audienceList.size());
        }
        return dataTableResponse;
    }

    @Override
    public Map<String,String> getReferenceFieldValues(String destination, String fieldName, String instance, String value, Long limit, String source, String sourceURL) {
        List<Long> eventIds = new ArrayList<>();
        Map<String,String> result = new HashMap<>();

        getEventIdsFromSource(source,sourceURL,eventIds);

        if(eventIds.size()>0 && !checkSpecialFields(fieldName, eventIds, result, value)) {

            String query = AudienceAnalyticsQueryBuilder.prepareReferenceQuery(destination,fieldName,instance, value, limit, eventIds);

            if(StringUtils.isNotBlank(query)) {
                result = roRDSQueryRunnerService.executeQuery(query, Audience.class).stream().filter(audience -> audience.getReferenceValue() != null && !audience.getReferenceValue().isEmpty()).map(Audience::getReferenceValue).collect(Collectors.toList()).stream().collect(Collectors.toMap(Function.identity(), Function.identity()));
            }
        }
        return result;
    }

    private boolean checkSpecialFields(String fieldName, List<Long> eventIds, Map<String, String> result, String value){

        boolean flag = Boolean.TRUE;

        if(fieldName.equalsIgnoreCase("tickettypename")){
            result.putAll(roTicketingTypeRepo.findAllByEventsIdRecurringIdNullAndTicketTypeLikeAAndTicketTypeNameLike(eventIds, DataType.TICKET,value).stream().collect(Collectors.toMap(objects->objects[0].toString(), objects->objects[1].toString()+" ("+ objects[2].toString()+" )")));
        }
        else if(fieldName.equalsIgnoreCase("eventName")){
            result.putAll(roEventRepository.findEventNameByEventIdsAndNameLike(eventIds,value).stream().collect(Collectors.toMap(objects->objects[0].toString(), objects->objects[1].toString())));
        }
        else if(fieldName.equalsIgnoreCase("discountCode")){
            result.putAll(roTicketingCouponRepository.findTicketingCouponByEventIdsAndnAndNameLike(eventIds,value).stream().collect(Collectors.toMap(objects->objects[0].toString(), objects->objects[1].toString()+" ("+ objects[2].toString()+" )")));
        }
        else if(fieldName.equalsIgnoreCase("joinedsession") || fieldName.equalsIgnoreCase("bookmarksession") ){
            result.putAll(roSessionRepository.getSessionNameByEventIdsAndTitleLike(eventIds,value).stream().collect(Collectors.toMap(objects->objects[0].toString(), objects->objects[1].toString()+" ("+ objects[2].toString()+" )")));
        }
        else{
            flag = Boolean.FALSE;
        }
        return flag;
    }

    @Override
    public DataTableResponse getUserEventListByOrganizerForOrdersAndUserActivityTab(String sourceURL, Long id, AudienceConstants.AudienceFilterSourceType sourceType) {
        List<Object[]> userEventList;
        List<EventNameLookUpDto> eventNameLookUpDto;
        DataTableResponse dataTableResponse = new DataTableResponse();
        if (sourceType == AudienceConstants.AudienceFilterSourceType.ORGANIZER) {
            Long organizerId = roOrganizerService.findOrganizerIdByOrganizerPageURL(sourceURL);
            if (organizerId != null) {
                log.info("successfully get the organizer {}", organizerId);
                userEventList = roAudienceFilterEventRepo.findUserEventListForOrdersAndUserActivity(id,organizerId);
                eventNameLookUpDto = addUserEventDataIntoDto(userEventList);
                log.info("successfully get the attended events {} for user {} ",userEventList.size(),id);
            } else {
                throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
            }
        } else {
            Optional<WhiteLabel> whiteLabelOptional = roWhiteLabelService.findWhiteLabelByUrlOp(sourceURL);
            if (whiteLabelOptional.isPresent()) {
                WhiteLabel whiteLabel = whiteLabelOptional.get();
                log.info("successfully get the Whitelabel {}", whiteLabel.getId());
                userEventList = roAudienceFilterEventRepo.findUserEventListForOrdersAndUserActivityInWhitelabel(id,whiteLabel.getId());
                eventNameLookUpDto = addUserEventDataIntoDto(userEventList);
                log.info("successfully get the attended events {} for user {} ",userEventList.size(),id);
            } else {
                throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
            }
        }
        dataTableResponse.setData(eventNameLookUpDto);
        dataTableResponse.setRecordsTotal(eventNameLookUpDto.size());
        dataTableResponse.setRecordsFiltered(eventNameLookUpDto.size());
        return dataTableResponse;
    }

    private List<EventNameLookUpDto> addUserEventDataIntoDto(List<Object[]> userEventList){
        List<EventNameLookUpDto> eventNameLookUpDto = new ArrayList<>();
        if(userEventList != null) {
            for (int i = 0; i < userEventList.size(); i++) {
                EventNameLookUpDto eventNameLookUpDtos = new EventNameLookUpDto(Long.valueOf(userEventList.get(i)[0].toString()), (userEventList.get(i)[1].toString()), (userEventList.get(i)[2]).toString(),(userEventList.get(i)[3]).toString());
                eventNameLookUpDto.add(eventNameLookUpDtos);
            }
        }
        return eventNameLookUpDto;
    }

    @Override
    public DataTableResponse getUserAttendedEventList(String sourceURL, Long id, AudienceConstants.AudienceFilterSourceType sourceType) {
        List<EventNameLookUpDto> userAttendedEventList;
        DataTableResponse dataTableResponse = new DataTableResponse();
        if(sourceType == AudienceConstants.AudienceFilterSourceType.ORGANIZER){
            Long organizerId = roOrganizerService.findOrganizerIdByOrganizerPageURL(sourceURL);
            if(organizerId != null){
                log.info("successfully get the organizer {}",organizerId);
                userAttendedEventList = roAudienceFilterEventRepo.findEventsOfUserAttended(id, organizerId);
                log.info("successfully get the attended events for user {} ",id);
            }
            else {
                throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
            }
        }
        else{
            Optional<WhiteLabel> whiteLabelOptional = roWhiteLabelService.findWhiteLabelByUrlOp(sourceURL);
            if(whiteLabelOptional.isPresent()){
                WhiteLabel whiteLabel = whiteLabelOptional.get();
                log.info("successfully get the Whitelabel {}",whiteLabel.getId());
                userAttendedEventList = roAudienceFilterEventRepo.findEventsOfUserAttendedInWhiteLabel(id, whiteLabel.getId());
                log.info("successfully get the attended events for user {} ",id);
            }
            else {
                throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
            }
        }

        dataTableResponse.setData(userAttendedEventList);
        dataTableResponse.setRecordsTotal(userAttendedEventList.size());
        dataTableResponse.setRecordsFiltered(userAttendedEventList.size());
        return dataTableResponse;
    }

    @Override
    public AudienceFilterMasterDTO getAudienceFilterMasterJson() {
        return new AudienceFilterMasterDTO(roAudienceFilterMasterRepo.findFirstBy().getAudienceFilterJson());
    }

    @Override
    public AudienceFilterDTO getAudienceFIlterDTOById(Long id) {
        AudienceFilter audienceFilter = this.findById(id);
        return new AudienceFilterDTO(audienceFilter);
    }
}
