package com.accelevents.utils;

import com.accelevents.domain.enums.EnumLabelLanguageCode;
import com.accelevents.domain.enums.EventListingStatus;
import com.accelevents.session_speakers.dto.PostSessionCallToActionDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import eu.bitwalker.useragentutils.UserAgent;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Entities;
import org.jsoup.parser.Parser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.util.TimeZone;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.accelevents.utils.Constants.*;

public class CommonUtil {

    static final CustomCleaner.MyWhitelist whitelist = new CustomCleaner.MyWhitelist();
    private static final Pattern pattern = Pattern.compile("[(http(s)?):\\/\\/(www\\.)?a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)");

    private static final String IF_TAG_REPLACEMENT = "<AEIF ";
    private static final String END_IF_TAG_REPLACEMENT = "</AEIF > ";
    private static final String DOC_TYPE_TAG_REPLACEMENT = "<AEDOC ";
    private static final String DOC_TYPE_STRING = "<!DOCTYPE HTML PUBLIC";
    private static final String END_IF_COMMENT_STRING = "<!\\[endif]-->";
    private static final String IF_TAG_COMMENT_STRING = "<!--\\[if";
    private static final String NEXT_LINE_TAG_STRING = "[\n\r]";
    private static final String NEW_LINE_REPLACEMENT = "---linebreak--";
    private static final String SCRIPT_REGEX_STRING = "<(/?)(\\s*)(script)([^<>]*)>";
    private static final Pattern docTypePattern = Pattern.compile("(?i)<!DOCTYPE[^>]*>",Pattern.MULTILINE);
    private static final Pattern startHTMLPattern = Pattern.compile("<html[^>]*>",Pattern.MULTILINE);
    private static final Pattern endHTMLPattern = Pattern.compile("</html[^>]*>",Pattern.MULTILINE);
    private static final Pattern startHeadPattern = Pattern.compile("<head[^>]*>",Pattern.MULTILINE);
    private static final Pattern endHeadPattern = Pattern.compile("</head[^>]*>",Pattern.MULTILINE);
    private static final Pattern commentTagPattern = Pattern.compile("<!--[^>]*>",Pattern.MULTILINE);
    private static final Pattern anyOtherHashTagStartPattern = Pattern.compile("<#[^>]*>",Pattern.MULTILINE);
    private static final Pattern anyOtherHashTagEndPattern = Pattern.compile("</#[^>]*>",Pattern.MULTILINE);
    private static final Pattern xmlTagPattern = Pattern.compile("<xml[^>]*>[\\s\\S]+<\\/xml>",Pattern.MULTILINE);
    private static  final Pattern htmlPattern = Pattern.compile(".*\\<[^>]+>.*", Pattern.DOTALL);

    private CommonUtil() {
    }
    private static final Pattern linkedInPattern = Pattern.compile("(?:(?:http|https):\\/\\/)?(([w]{3}||\\w\\w)\\.)?linkedin.com(\\w+:{0,1}\\w*@)?(\\S+)(:([0-9])+)?(\\/|\\/([\\w#!:.?+=&%@!\\-\\/]))?");
    private static final Pattern linkedInPatternForSpeaker = Pattern.compile("^(?:(?:http|https):\\/\\/)?((www|\\w\\w)\\.)?linkedin.com\\/((in\\/[^/]+\\/?)|(groups\\/[^/]+\\/?)|(pub\\/[^/]+\\/((\\w|\\d)+\\/?){3}))");
    private static final Pattern facebookPattern = Pattern.compile("(?:(?:http|https):\\/\\/)?(?:www.)?facebook.com\\/(?:(?:\\w)*#!\\/)?(?:pages\\/)?(?:[?\\w\\-]*\\/)?(?:profile.php\\?id=(?=\\d.*))?([\\w\\-]*)?");
    private static final Pattern instagramPattern = Pattern.compile("(?:(?:http|https):\\/\\/)?(?:www\\.)?(?:instagram\\.com|instagr\\.am)\\/([A-Za-z0-9-_\\.]+)");
    private static final Pattern instagramPatternForSpeaker = Pattern.compile("(?:(?:http|https):\\/\\/)?(?:www\\.)?(?:instagram\\.com|instagr\\.am)\\/([A-Za-z0-9-_]+).*");
    private static final Pattern twitterPattern = Pattern.compile("(?:(?:http|https):\\/\\/)?(?:www\\.)?(twitter|x)\\.com\\/([a-zA-Z0-9_]+)");
    private static final Pattern twitterPatternForSpeaker = Pattern.compile("(?:(?:http|https):\\/\\/)?(?:www\\.)?(twitter|x)\\.com\\/([a-zA-Z0-9_]+).*");
    private static Matcher urlMatcher ;
    private static final int DAYSOFYEAR = 365;
    private static final TimeZone UTC_TIMEZONE = TimeZone.getTimeZone("UTC");
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CODE_LENGTH = 6;
    static SecureRandom secureRandom = new SecureRandom();
    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);

    public static List<EventListingStatus> getLiveEventStatuses() {
        List<EventListingStatus> listingStatuses = getPublicStatus();
        listingStatuses.add(EventListingStatus.PRIVATE);
        return listingStatuses;
    }


    public static String convertToGson(PostSessionCallToActionDto postSessionCallToActionDto) {
        Gson gson = new Gson();
        if(null == postSessionCallToActionDto) {
            return null;
        }
        return gson.toJson(postSessionCallToActionDto, PostSessionCallToActionDto.class);
    }


    public static PostSessionCallToActionDto getDtoPostCallToActionJson(String jsonInString) {
        ObjectMapper mapper = new ObjectMapper();
        PostSessionCallToActionDto postSessionCallToActionDto = new PostSessionCallToActionDto();
        if (StringUtils.isNotBlank(jsonInString)) {
            try {
                postSessionCallToActionDto = mapper.readValue(jsonInString, PostSessionCallToActionDto.class);
            } catch (IOException e) {
                log.info("CommonUtil getDtoPostCallToActionJson jsonInString {}", jsonInString);
            }
        }
        return postSessionCallToActionDto;
    }

    public static List<EventListingStatus> getPublicStatus() {
        List<EventListingStatus> listingStatuses = new ArrayList<>();
        listingStatuses.add(EventListingStatus.PUBLIC);
        listingStatuses.add(EventListingStatus.PUBLISHED);
        return listingStatuses;
    }

    public static Long getLongValue(Object target, Long defaultValue) {
        Long value = defaultValue;
        try {
            if (null != target) {
                value = Long.parseLong(target.toString());
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return value;
    }

    public static Object convertToLongValue(Object target) {
        try {
            if (null != target) {
                return Long.parseLong(target.toString());
            }
        } catch (Exception exception) {
            log.info(exception.getMessage());
        }
        return String.valueOf(target);
    }

    public static Long getLongValueIgnoreException(Object target, Long defaultValue) {
        Long value = defaultValue;
        try {
            if (null != target) {
                value = Long.parseLong(target.toString());
            }
        } catch (Exception exception) {
            log.info(exception.getMessage());
            return value;
        }
        return value;
    }

    public static Date getDateValue(Object target) {
        Date value = null;
        try {
            if (null != target) {
                value = (Date) target;
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }

        return value;
    }

    public static BigInteger getBigIntegerValue(Object target) {
        BigInteger value = null;
        try {
            if (null != target) {
                value = new BigInteger(getStringValue(target, null));
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return value;
    }

    public static BigDecimal getBigDecimalValue(Object target) {
        BigDecimal value = null;
        try {
            if (null != target) {
                value = new BigDecimal(getStringValue(target, null));
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
        return value;
    }

    public static BigInteger getBigIntegerValue(Object target, BigInteger defaultValue) {
        BigInteger value = defaultValue;

        try {
            if (null != target) {
                value = new BigInteger(target.toString());
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }

        return value;
    }

    public static Double getDoubleValue(Object target, Double defaultValue) {
        Double value = defaultValue;
        try {
            if (null != target) {
                value = Double.parseDouble(target.toString());
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }

        return value;
    }


    public static String getStringValue(Object target, String defaultValue) {
        String value = defaultValue;

        if (null != target) {
            value = String.valueOf(target);
        }

        return value;
    }

    public static boolean checkIfUrlIsValidOrNot(String url) {
        urlMatcher = pattern.matcher(url);
        return urlMatcher.matches();
    }

    public static boolean checkIfLinkedInUrlIsValidOrNot(String url) {
        urlMatcher = linkedInPattern.matcher(url);
        return urlMatcher.matches();
    }

    public static boolean checkSpeakerLinkedUrlIsValidOrNot(String url) {
        urlMatcher = linkedInPatternForSpeaker.matcher(url);
        return urlMatcher.matches();
    }

    public static boolean checkTwitterUrlIsValidOrNot(String url){
        urlMatcher = twitterPattern.matcher(url);
        return urlMatcher.matches();
    }

    public static boolean checkSpeakerTwitterUrlIsValidOrNot(String url){
        urlMatcher = twitterPatternForSpeaker.matcher(url);
        return urlMatcher.matches();
    }

    public static boolean checkInstagramUrlIsValidOrNot(String url){
        urlMatcher = instagramPattern.matcher(url);
        return urlMatcher.matches();
    }
    public static boolean checkSpeakerInstagramUrlIsValidOrNot(String url){
        urlMatcher = instagramPatternForSpeaker.matcher(url);
        return urlMatcher.matches();
    }
    public static boolean checkFacebookUrlIsValidOrNot(String url){
        urlMatcher = facebookPattern.matcher(url);
        return urlMatcher.matches();
    }

    public static boolean checkStringIsNumberOrNot(String number) {
       return number != null && number.matches("[0-9]+");
    }

    public static boolean checkStringIsNumber(String number) {
        return number.matches("[0-9]+");
    }

    public static String getDeviceInfo(HttpServletRequest httpRequest) {
        UserAgent userAgent = UserAgent.parseUserAgentString(httpRequest.getHeader("User-Agent"));
        String deviceType = userAgent.getOperatingSystem().getDeviceType().getName();
        String browser = userAgent.getBrowser().getName();
        return browser + " from " + deviceType;
    }

    public static Map<String, String> getMessageLanguageMap() {
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put(SUCCESS, "success");
        hashMap.put(SESSIONS_SORTED_SUCCESSFULLY, "sessions.sorted.successfully");
        hashMap.put(Constants.INFO_DESK_DETAILS_UPDATED_SUCCESSFULLY, "infodesk.update.successfully");
        hashMap.put(Constants.INFO_DESK_DETAILS_DELETED_SUCCESSFULLY, "infodesk.delete.successfully");
        hashMap.put(Constants.NETWORKING_LOUNGE_DELETED, "networking.lounge.deleted");
        hashMap.put(Constants.NETWORKING_LOUNGE_UPDATED, "networking.lounge.updated");
        hashMap.put(Constants.NETWORKING_LOUNGE_PHOTO_DELETED, "networking.lounge.photo.deleted");
        hashMap.put(NETWORKING_LOUNGE_VIDEO_DELETED, "networking.lounge.video.deleted");
        hashMap.put(NETWORKING_LOUNGE_LEAVE_MSG, "networking.lounge.leave.successfully");
        hashMap.put(Constants.NETWORKING_LOUNGE_POSITION_CHANGED_SUCCESSFULLY, "networking.lounge.position.changed");
        hashMap.put(Constants.EXHIBITOR_STAFF_SAVED, "exhibitor.staff.saved");
        hashMap.put(Constants.EXHIBITOR_STAFF_DELETED, "exhibitor.staff.deleted");
        hashMap.put(EXHIBITOR_ADMIN_UPDATED_MSG, "exhibitor.admin.updated");
        hashMap.put(EXHIBITOR_STAFF_UPDATED_MSG, "exhibitor.staff.updated");
        hashMap.put(STAFF_SEND_INVITE_MSG, "staff.send.invite.msg");
        hashMap.put(PRODUCT_ADDED_SUCCESSFULLY, "product.added.success");
        hashMap.put(PRODUCT_UPDATED_SUCCESSFULLY, "product.updated.success");
        hashMap.put(PRODUCT_DELETED_SUCCESSFULLY, "product.deleted.success");
        hashMap.put(Constants.EXHIBITOR_PRODUCT_POSITION_CHANGED_SUCCESSFULLY, "exhibitor.product.position.changed.success");
        hashMap.put(Constants.ATTENDEE_DELETED, "attendee.deleted");
        hashMap.put(ATTENDEE_CONNECTED_MSG, "attendee.connected.msg");
        hashMap.put(NOTIFICATION_PREFERENCE_SAVED, "notification.preference.saved");
        hashMap.put(Constants.NOTIFICATION_PREFERENCE_UPDATED, "notification.preference.updated");
        hashMap.put(NOTIFICATION_PREFERENCE_DELETED, "notification.preference.deleted");
        hashMap.put(SEND_CONTACT_MAIL, "send.contact.mail");
        hashMap.put(NEW_BIDDER_NUMBER_MESSAGE, "new.bidder.number.message");
        hashMap.put(BIDDER_REGISTERED_MESSAGE, "bidder.registered.message");
        hashMap.put(DEVICE_CHECKER_ADDED, "device.checker.added");
        hashMap.put(DEVICE_CHECKED_WITH_DEVICE_ID, "device.checked.with.device.id");
        hashMap.put(LEAD_DELETED, "lead.deleted");
        hashMap.put(Constants.LEAD_SAVED, "lead.saved");
        hashMap.put(LEAD_UPDATED_MSG, "lead.updated.msg");
        hashMap.put(SESSION_BREAKOUT_ROOM_DELETED, "session.breakout.room.deleted");
        hashMap.put(BREAKOUT_ROOM_UPDATED_SUCCESSFULLY, "breakout.room.updated.success");
        hashMap.put(EXHIBITOR_FIELDS_TABLIST_UPDATED, "exhibitor.fields.tablist.updated");

        //error
        hashMap.put(EXHIBITOR_ALREADY_EXISTS, "exhibitor.already.exists");

        hashMap.put(UNABLE_TO_CREATE_FEED, "unable.to.create.new.feed");
        hashMap.put(UNABLE_TO_UPDATE_FEED, "unable.to.update.feed");
        hashMap.put(UNABLE_TO_RETRIEVE_FEED, "unable.to.retrieve.feed");
        hashMap.put(UNABLE_TO_FOLLOW_FEED, "unable.to.follow.feed");
        hashMap.put(UNABLE_TO_GET_CLIENT, "unable.to.get.client");
        hashMap.put(UNABLE_TO_RETRIEVE_FEED_REACTION, "unable.to.retrieve.feeds.reaction");
        hashMap.put(LOUNGE_FEED_NOT_FOUND, "activity.stream.not.found");

        hashMap.put(INFO_DESK_ALREADY_EXIST, "info.desk.already.present");
        hashMap.put(INFO_DESK_NOT_FOUND, "info.desk.not.found");

        hashMap.put(SESSION_DATES_NOT_BETWEEN_EVENT, "session.dates.not.between.event");
        hashMap.put(REQUIRED_FOR_AE_STUDIO, "required.for.ae.studio");

        hashMap.put(SESSION_NOT_FOUND, "session.not.found");
        hashMap.put(SPEAKER_NOT_FOUND, "speaker.not.found");
        hashMap.put(TAG_TRACK_NOT_FOUND, "tag.or.track.not.found");
        hashMap.put(EVENT_TICKET_NOT_FOUND, "event.ticket.not.found");
        hashMap.put(SESSION_SPEAKER_NOT_FOUND, "session.speaker.not.found");

        hashMap.put(SESSION_ENDED, "session.is.ended");
        hashMap.put(DEFAULT_PLAYBACK_NOT_FOUND, "default.playback.not.found");
        hashMap.put(EVENT_PLAN_CONFIG_NOT_FOUND_1, "event.plan.config.not.found.error.msg");
        hashMap.put(EVENT_PLAN_CONFIG_NOT_FOUND, "event.plan.config.not.found.developer.msg");

        hashMap.put(ATTENDEE_CAN_NOT_ADDED, "attendee.can.not.added");
        hashMap.put(ATTENDEE_ALREADY_ADDED, "attendee.already.added");
        hashMap.put(ATTENDEE_NAME_CAN_NOT_EMPTY, "attendee.name.can.not.empty");
        hashMap.put(SUPER_ADMIN_CANNOT_CONNECT_WITHOUT_TICKET, "super.admin.cannot.connect.without.ticket");
        hashMap.put(ATTENDEE_NOT_FOUND, "attendee.not.found");
        hashMap.put(ATTENDEE_CAN_NOT_CONNECTED, "attendee.can.not.connect.to.self");
        hashMap.put(ATTENDEE_ALREADY_CONNECTED, "attendee.already.connected");
        hashMap.put(CONNECTION_REQUEST_ERROR, "connection.request.not.found");
        hashMap.put(CONNECTION_ALREADY_ACCEPTED, "connection.request.already.accepted");
        hashMap.put(CONNECTION_ALREADY_REJECTED, "connection.request.already.rejected");
        hashMap.put(ATTENDEE_CONNECT_REQUEST_CANCELED,"attendee.connect.request.cancelled");
        hashMap.put(ATTENDEE_ALREADY_CONNECTED_OR_REJECTED,"attendees.already.connected.or.rejected");
        hashMap.put(ATTENDEE_CONNECTION_REMOVED,"attendee.connection.removed");
        hashMap.put(ATTENDEE_HAS_NOT_CONNECTED,"attendee.has.not.connected");

        hashMap.put(PERMISSION_DENIED_REGISTER_EVENT, "permission.denied.register.event");
        hashMap.put(EVENT_ENDED, "event.ended");
        hashMap.put(NOT_ALLOW_TO_ACCESS_VIRTUAL_PORTAL, "not.allow.to.access.virtual.portal");
        hashMap.put(TICKETING_MODULE_NOT_ACTIVATED, "ticketing.module.not.activated");
        hashMap.put(MAX_FREE_ATTENDEES_REACHED_FOR_PORTAL, "max.free.attendee.reached.for.portal");
        hashMap.put(MAX_ATTENDEE_ALLOWED_LIMIT_REACHED, "max.attendee.allows.limit.reached");
        hashMap.put(BLOCKED, "blocked");
        hashMap.put(USER_DID_DONATION_NOT_PURCHASE_TICKET, "user.did.donation.not.purchase.ticket");

        hashMap.put(URL_NOT_SUPPORTED, "url.not.supported");
        hashMap.put(EVENT_NOT_FOUND, "event.not.found");

        hashMap.put(USER_NOT_ALLOWED_TO_REGITER_WITHOUT_TICKET, "user.not.allowed.to.register.without.ticket");
        hashMap.put(TICKET_TYPE_NOT_MATCHED, "ticketing.type.not.matched");
        hashMap.put(NOT_AUTHORIZED_TO_ACCESS_SESSION_USER_ANALYTICS, "not.authorized.to.access.session.user.analytics");
        hashMap.put(REGISTER_FAILED, "register.failed");
        hashMap.put(TIME_ACCESS_ELAPSED_LIVE, "time.access.elapsed.live");
        hashMap.put(TIME_ACCESS_ELAPSED_RECORDING, "time.access.elapsed.recording");
        hashMap.put(EXCEED_SESSION_CAPACITY, "exceed.session.capacity");
        hashMap.put(SESSION_IS_NOT_PRIVATE, "session.is.not.private");
        hashMap.put(EXCEED_WORKSHOP_SESSION_CAPACITY, "exceed.workshop.session.capacity");

        hashMap.put(CAN_NOT_GENERATE_STREAM_IN_PROGRESS, "can.not.generate.stream.in.progress");

        hashMap.put(MUX_LIVE_STREAM_NOT_AVAILABLE, "mux.live.stream.not.available");
        hashMap.put(MUX_LIVE_STREAM_NOT_COMPLETED, "mux.live.stream.not.completed");
        hashMap.put(MUX_LIVE_STREAM_STATUS_NOT_AVAILABLE, "mux.live.stream.status.not.available");
        hashMap.put(MUX_LIVE_STREAM_CAN_NOT_CREATE, "mux.live.stream.can.not.create");
        hashMap.put(MUX_LIVE_STREAM_CAN_NOT_CREATE_UPLOAD_URL, "mux.live.stream.can.not.create.upload.url");
        hashMap.put(MUX_ASSET_ID_NOT_AVAILABLE, "mux.asset.id.not.available");
        hashMap.put(MUX_PLAYBACK_ID_NOT_AVAILABLE, "mux.playback.id.not.available");
        hashMap.put(FAILED_TO_ENABLE_MP4_SUPPORT_FOR_MUX, "failed.to.enable.mp4.support.for.mux");
        hashMap.put(MUX_DURATION_NOT_AVAILABLE, "mux.duration.not.available");
        hashMap.put(MUX_PREPARING_VIDEO_ASSET, "mux.preparing.video.asset");
        hashMap.put(MUX_SUBTITLE_FILE_CAN_NOT_BE_ADDED, "mux.subtitle.file.can.not.be.added");
        hashMap.put(MUX_SUBTITLE_FILE_CAN_NOT_BE_REMOVED, "mux.subtitle.file.can.not.be.removed");
        hashMap.put(MUX_LIVE_STREAM_NOT_RE_ENABLED, "mux.live.stream.not.re.enabled");

        hashMap.put(NOTIFICATION_PREFERENCE_ADDED, "notification.preference.already.added");
        hashMap.put(NOT_VALID_JSON_FORMAT, "json.format.is.not.valid");

        hashMap.put(YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_VIDEO, "you.can.not.delete.networking.lounge.video");
        hashMap.put(DEFAULT_PLAYBACK_NOT_FOUND_FOR_VIDEO, "default.playback.not.found.for.video");
        hashMap.put(NETWORKING_LOUNGE_NOT_FOUND, "networking.lounge.not.found");
        hashMap.put(YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_PHOTO, "you.can.delete.only.own.networking.lounge.photo");
        hashMap.put(NETWORKING_LOUNGE_WITH_SAME_NAME_ALREADY_EXIST, "networking.lounge.with.same.name.already.exist");

        hashMap.put(NOT_AUTHORIZE, "not.authorize");
        hashMap.put(SESSION_BREAKOUT_ROOM_NOT_FOUND, "session.breakout.room.not.found");
        hashMap.put(Constants.BREAKOUT_ROOM_ALREADY_EXIST, "breakout.room.already.exist");

        hashMap.put(EXHIBITOR_CATEGORY_NOT_EXISTS, "expo.category.not.exists");

        hashMap.put(EXHIBITOR_PRODUCT_NOT_FOUND, "product.not.found");

        hashMap.put(STAFF_NOT_FOUND, "staff.details.not.found");
        hashMap.put(USER_HAS_ALREADY_LOGGED_IN, "user.has.already.logged.in");
        hashMap.put(ALREADY_STAFF, "already.staff");
        hashMap.put(EMAIL_ALREADY_ASSIGNEDED, "email.already.assigned");
        hashMap.put(EXHIBITOR_STAFF_ROLE_NOT_MATCHED, "expo.staff.role.not.matched");
        hashMap.put(MAX_TEAM_MEMBER_LIMIT_REACHED, "max.permitted.team.member.reached");
        hashMap.put(EXHIBITOR_ADMIN_CAN_NOT_REMOVE_EXHIBITOR_ADMIN, "expo.admin.can.not.remove.expo.admin");

        hashMap.put(CAN_NOT_GENERATE_LEAD, "can.not.generate.lead");
        hashMap.put(FIRST_NAME_SIZE_LIMIT, "first.name.size.limit");
        hashMap.put(LAST_NAME_SIZE_LIMIT, "lastName.name.size.limit");
        hashMap.put(EMAIL_SIZE_LIMIT, "email.name.size.limit");
        hashMap.put(ALREADY_USED_TICKET, "already.used.ticket");
        hashMap.put(LEAD_IS_NOT_BELONG_TO_THIS_LEADRETRIEVER, "lead.is.not.belong.to.this.lead.retrieval");
        hashMap.put(LEAD_NOT_EXISTS, "lead.not.exists");
        hashMap.put(CAN_NOT_SET_DEFAULT_PLAYBACK, "can.not.default.playback");
        hashMap.put(NOT_ALLOW_TO_SELECT_INTEREST_TAGS,"not.allow.to.select.interest.tags");
        hashMap.put(NOT_PARSE_NUMBER_TO_INTENDED_FORMAT,"not.parse.number.to.intended.format");
        hashMap.put(RAFFLE_NOT_ACTIVE,"raffle.not.active");
        hashMap.put(RAFFLE_IS_EXPIRE,"raffle.is.expire");
        hashMap.put(NUMBER_OF_TICKTES_HAS_BEEN_SUBMITTED_MSG,"number.of.tickets.has.been.submitted.msg");
        hashMap.put(EVENT_CC_NOT_ENABLE,"event.cc.not.enable");
        hashMap.put(RAFFLE_TICKETS_ARE_SOLD_OUT,"raffle.tickets.are.sold.out");
        hashMap.put(LIMITTED_TICKET_MULTI_LANGUAGE,"limitted.ticket.multi.language");
        hashMap.put(LIMITED_RAFFLE_TICKET,"limited.raffle.ticket");
        hashMap.put(RAFFLE_TICKET_PKG_NOT_FOUND,"raffle.ticket.pkg.not.found");
        hashMap.put(TICKETS_ADDED,"tickets.added");
        hashMap.put(TICKET_PURCHASING_FOR_EVENT,"ticket.purchasing.for.event");
        hashMap.put(DO_NOT_HAVE_ENOUGH_TICKET,"do.not.have.enough.ticket");
        hashMap.put(TICKETS_SUBMITTED_SUCCESSFULLY,"tickets.submitted.successfully");
        hashMap.put(TICKET_SHOULD_BE_MORE_THAN_ZERO,"ticket.should.be.more.than.zero");

        hashMap.put(PURCHASE_EVENT_TICKET_REQUIRED_ENABLE_SHOW_REGISTRATION_BUTTON,"purchase.event.ticket.required.enable.show.registration.button");
        hashMap.put(PURCHASE_EVENT_TICKET_REQUIRED_FOR_ADDONS_TICKET,"purchase.event.ticket.required.for.addons.ticket");
        hashMap.put(HOLD_TOKEN_DOES_NOT_MATCH,"hold.token.does.not.match");
        hashMap.put(TICKET_COUPON_NOT_FOUND,"ticket.coupon.not.found");
        hashMap.put(ORDER_NOT_FOUND,"order.not.found");
        hashMap.put(ACCESS_CODE_NOT_FOUND,"access.code.not.found");
        hashMap.put(ATTRIBUTE_NAME_EXIST,"attribute.name.exist");
        hashMap.put(ATTRIBUTE_NOT_EXISTS,"attribute.not.exists");
        hashMap.put(MCQ_ONLY_FOR_ENTERPRISE_PLAN,"mcq.only.for.enterprise.plan");
        hashMap.put(ZEROORDERCOUNT,"zero.order.count");
        hashMap.put(NUMBER_OF_SEATS_MUST_BE_EQUAL_TO_NUMBER_OF_TICKETS,"number.of.seats.must.equal.to.number.of.tickets");
        hashMap.put(SEAT_SELECTION_NOT_ALLOWED_FOR_FREE_TICKET,"seat.selection.not.allowed.for.free.ticket");
        hashMap.put(NOT_UNPAID_ORDER,"not.unpaid.order");
        hashMap.put(ALREADY_PAID_ORDER,"already.paid.order");
        hashMap.put(CREDIT_CARD_PROCESSING_NOT_ENABLE,"credit.card.processing.not.enable");
        hashMap.put(BIRTH_DATE_NOT_ALLOWED_IN_FUTURE,"birth.date.not.allowed.in.future");
        hashMap.put(BLOCKED_ADMIN_MSG,"blocked.admin.msg");
        hashMap.put(ACTIVATE_REGISTRATION,"activate.registration");
        hashMap.put(RECURRING_EVENT_MODIFIED_BY_HOST,"recurring.event.modified.by.host");
        hashMap.put(CHECK_OUT_TIME_EXPIRE,"checkout.time.expire");
        hashMap.put(RECURRING_EVENT_ID_NOT_EMPTY,"recurring.event.id.not.empty");
        hashMap.put(COUPON_CODE_NOT_APPLICABLE_ON_DONATION_TICKETING_TYPE,"coupon.code.not.applicable.on.donation");
        hashMap.put(COUPON_IS_NOT_APPLIED_TO_THIS_ORDER,"coupon.not.applied.to.this.order");
        hashMap.put(THIS_DISCOUNT_CODE_HAS_ALREADY_BEEN_APPLIED_TO_YOUR_TRANSACTION,"coupon.has.already.applied.to.your.transaction");
        hashMap.put(COUPON_REACHED_MAX_USAGE_PER_USER,"coupon.reached.max.usage.per.user");
        hashMap.put(COUPON_REACHED_MAX_USAGE,"coupon.reached.max.usage");
        hashMap.put(COUPON_IS_NOT_AVAILABLE,"coupon.not.available");
        hashMap.put(COUPON_CODE_EXPIRED,"coupon.code.expired");
        hashMap.put(APPLIED_COUPON_NOT_FOUND,"applied.coupon.not.found");
        hashMap.put(BARCODE_NOT_EXIST,"barcode.not.exist");
        hashMap.put(THIS_DOWNLOAD_PDF_IS_NOT_ALLOWED_FOR_THIS_ORDER,"download.pdf.is.not.allowed.for.this.order");
        hashMap.put(INVALID_WAIT_LIST_IDS,"invalid.wait.list.details");
        hashMap.put(EVENT_TICKETS_NOT_FOUND,"event.tickets.not.found");
        hashMap.put(CHECKIN_NOT_ALLOWED_BEFORE_TIME,"check.in.not.allowed.before.time");
        hashMap.put(BARCODE_ALREADY_REFUNDED,"barcode.already.refunded");
        hashMap.put(BARCODE_ALREADY_CHECKED_IN,"barcode.already.check.in");
        hashMap.put(ATTENDEE_HAVE_VIRTUAL_TICKET,"attendee.have.virtual.ticket");
        hashMap.put(COLLECT_PAYMENT_FIRST,"collect.payment.first");
        hashMap.put(NOT_VALID_PAYMENT,"not.valid.payment");
        hashMap.put(CAN_NOT_CANCEL_SCHEDULE_EVENT,"can.not.cancel.schedule.event");
        hashMap.put(USER_NOT_FOUND,"user.not.found");
        hashMap.put(COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE,"coupon.not.applicable.for.ticket.type");
        hashMap.put(BLOCK_USER,"block.user.by.admin");
        hashMap.put(RAFFLE_ONLINE_PURCHASE_TICKETS,"raffle.online.purchase.tickets");
        hashMap.put(RAFFLE_ONLINE_PURCHASE,"raffle.online.purchase");
        hashMap.put(SUCCESSFUL_TICKET_SUBMISSION_PAYMENT_DISABLED,"successful.ticket.submission.payment.disabled");
        hashMap.put(ITEM_NOT_FOUND,"item.not.found");
        hashMap.put(PLEDGE_NOT_ACTIVE,"pledge.not.found");
        hashMap.put(CAUSEAUCTION_NOT_FOUND,"causeauction.not.found");
        hashMap.put(MORE_PLEDGES_SUBMITTED,"more.pledges.submitted");
        hashMap.put(CHARGE_CREATION_FAILED,"charge.creation.failed");
        hashMap.put(AUCTION_CHARGE_FAILED,"auction.charge.failed");
        hashMap.put(PLEDGE_SUBMIT_MSG,"pledge.submit.msg");
        hashMap.put(PLEDGE_STAFF_SUCCESS,"pledge.staff.success");
        hashMap.put(PLEDGE_STAFF_ITEM_SUBMITTED_ALERT,"pledge.staff.item.submitted.alert");
        hashMap.put(BID_INSTARCTION_STR,"bid.instarction.str");
        hashMap.put(STAFF_CASH_PLEDGE,"staff.cash.pledge");
        hashMap.put(STRING_ITEM_HAS_BEEN_PURCHASED_FOR_MSG,"string.item.has.been.purchased.msg");
        hashMap.put(STRING_CURRENT_BID_FOR_THIS_ITEM_MSG,"string.current.bid.for.this.item.msg");
        hashMap.put(STRING_STARTING_BID_FOR_THIS_ITEM_MSG,"string.starting.bid.for.this.item.msg");
        hashMap.put(MIN_BID_FOR_ITEM_MSG,"min.bid.for.item.msg");
        hashMap.put(PURCHASE,"purchase");
        hashMap.put(WINNER,"winner");
        hashMap.put(POTENTIAL_WINNER,"potential.winner");
        hashMap.put(OUTBID,"outbid");
        hashMap.put(PAID_WITH_BIG_CASE,"paid");
        hashMap.put(UNPAID,"unpaid");
        hashMap.put(ALERTS_WHEN_BUY_IT_NOW_ITEM_PURCHASED, "alerts.when.buy.it.now.item.purchased");
        hashMap.put(ORDER_CONFIRMATIONS_FROM_MY_ATTENDEES, "order.confirmations.from.attendees");
        hashMap.put(WEEKLY_SALES_REPORT, "weekly.sales.report");
        hashMap.put(QUESTIONS_FROM_ATTENDEES, "questions.from.attendees");
        hashMap.put(PAYMENT_PROCESSING_IS_NOT_SETUP,"payment.processing.is.not.setup");
        hashMap.put(DONATION_NOT_ACTIVE,"donation.not.active");
        hashMap.put(RECURRING_PAYMENT_NOT_SUPPORTED_IN_SQUARE,"recurring.payment.not.supported.in.square");
        hashMap.put(DONATION_CHARGE_FAILED_MSG,"donation.charge.failed.msg");
        hashMap.put(AMOUNT_TO_LARGE,"amount.to.large");
        hashMap.put(DONATION_THANKS,"donation.thanks");
        hashMap.put(USER_NOT_FOUND_FOR_STAFF_CHECKOUT,"user.not.found.for.staff.checkout");
        hashMap.put(NOT_STAFF_USER,"not.staff.user");
        hashMap.put(INCORRECT_PASSWORD,"incorrect.password");
        hashMap.put(PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL,"phone.number.already.attach.to.email");
        hashMap.put(EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER,"email.already.attach.to.phone.number");
        hashMap.put(USER_ALREADY_EXIST,"user.already.exist");
        hashMap.put(USER_ALREADY_PRESENT_WITH_COUNTRY_CODE,"user.already.present.with.country.code");
        hashMap.put(ITEM_ALREADY_PURCHASED,"item.already.purchased");
        hashMap.put(BID_SUCCESS_NOTIFY,"bid_success_notify");
        hashMap.put(THANK_YOU_FOR_YOUR_PURCHASE,"thank.you.for.your.purchase");
        hashMap.put(THANK_YOU_FOR_PURCHASING,"thank.you.for.purchasing");
        hashMap.put(BUY_IT_NOW_BID_CONTACTED_TO_COLLECT_PAYMENT,"buy.it.now.bid.contacted.to.collect.payment");
        hashMap.put(BID_SHOULD_BE_GREATER_THAN_STARTING_BID,"bid.should.be.greater.than.starting.bid");
        hashMap.put(AUCTION_NOT_ACTIVE,"auction.not.active");
        hashMap.put(MINIMUM_BID_INCREMENT_FOR_THIS_ITEM,"minimum.bid.increment.for.this.item");
        hashMap.put(BIDDING_HAS_ENDED_FOR_ITEM_WITH_ITEMCODE,"bidding.has.ended.for.item.with.itemcode");
        hashMap.put(MORE_THEN_ONE_USER_EXIST_WITH_THIS_EMAIL,"more.then.one.user.exist.with.this.email");
        hashMap.put(PASSWORD_VALIDATION_FAILED,"password.validation.failed");
        hashMap.put(EMAIL_NOT_MATCH_WITH_USER_ID,"email.not.match.with.user.id");
        hashMap.put(EMAIL_NOT_MATCH_WITH_USER,"email.not.match.with.user");
        hashMap.put(BID_NOT_FOUND,"bid.not.found");
        hashMap.put(BIDDER_NOT_FOUND,"bidder.not.found");
        hashMap.put(PAYMENT_METHOD_REQUIRED,"payment.method.required");
        hashMap.put(COUPON_APPLIED_SUCCESS_MSG,"coupon.applied.success.msg");
        hashMap.put(COUPON_APPLIED_BUT_NOT_WITH_DONATION_TICKET_TYPE,"coupon.applied.but.not.with.donation.ticket.type");
        hashMap.put(COUPON_DELETE_SUCCESS_MSG,"coupon.delete.success.msg");
        hashMap.put(USER_BIDDERNUMBER_ALREADY_PRESENT,"user.biddernumber.already.present");
        hashMap.put(BIDDER_NUMBER_NOT_ENANLED,"bidder.number.not.enanled");
        hashMap.put(UPDATE_BIDDER_NUMBER_MESSAGE,"update.bidder.number.message");
        hashMap.put(COUNTRY_CODE_NOT_VALID,"country.code.not.valid");
        hashMap.put(BID_INSTUCTION,"bid.instuction");
        hashMap.put(CARD_UPDATED_SUCCESSFULLY,"card.updated.successfully");
        hashMap.put(PLEASE_LOGIN,"please.login");
        hashMap.put(RECEIVE_AMOUNT_FOR_UNPAID_TICKETS,"receive.amount.for.unpaid.tickets");
        hashMap.put(NOT_EVENT_HOST,"not.event.host");
        hashMap.put(WELCOME_BIDDER_NUMBER_SMS_DONATION,"welcome.bidder.number.sms.donation");
        hashMap.put(WELCOME_BIDDER_NUMBER_SMS,"welcome.bidder.number.sms");
        hashMap.put(WINNING_BID_SINGLE_ITEM_PAYMENT_ENABLED,"winning.bid.single.item.payment.enabled");
        hashMap.put(PURCHASE_CHECKOUT_PAYMENT_DISABLED,"purchase.checkout.payment.disabled");
        hashMap.put(PURCHASE_CHECKOUT_PAYMENT_ENABLED,"purchase.checkout.payment.enabled");
        hashMap.put(MAIL_SENT_UNABLE_TO_USE_TWILIO,"mail.sent.unable.to.use.twilio");
        hashMap.put(UNABLE_TO_USE_TWILIO,"unable.to.use.twilio");
        hashMap.put(BID_IS_REFUNDED,"bid.is.refunded");
        hashMap.put(REFUND_SUCCESS_MSG,"refund.success.msg");
        hashMap.put(AUCTION_REFUND_FAILED,"auction.refund.failed");
        hashMap.put(BID_ALREADY_REFUNDED,"bid.already.refunded");
        hashMap.put(ONLY_PAID_BID_CAN_BE_REFUNDED,"only.paid.bid.can.be.refunded");
        hashMap.put(EMAIL_SEND_SUCCESS_MSG,"email.send.success.msg");
        hashMap.put(BID_DELETE_SUCCESS_MSG,"bid.delete.success.msg");
        hashMap.put(CAN_NOT_DELET_ALREADT_PAID,"can.not.delet.alreadt.paid");
        hashMap.put(PAYMENT_CONFIRMATION_REQUIRED,"payment.confirmation.required");
        hashMap.put(CONFIRM_PURCHASE_PAYMENT_DISABLED,"confirm.purchase.payment.disabled");
        hashMap.put(ERROR_IN_GENERATION_OF_TOKEN,"error.in.generation.of.token");
        hashMap.put(THANKS_FOR_PURCHASE_MSG,"thanks.for.purchase.msg");
        hashMap.put(NOT_SALES_REP,"not.sales.rep");
        hashMap.put(TICKETS_ARE_NOT_CURRENTLY_FOR_SALE_FOR_THIS_EVENT,"tickets.are.not.currently.for.sale.for.this.event");
        hashMap.put(SUCCESSFULLY_UPDATED_ORDER_TICKETS_STATUS_CHECK_IN,"successfully.updated.order.tickets.status.check.in");
        hashMap.put(REFUND_ORDER_NOT_PAID,"refund.order.not.paid");
        hashMap.put(REFUND_AMOUNT_SHOULD_NOT_GREATER_THEN_PAID,"refund.amount.should.not.greater.then.paid");
        hashMap.put(VIRTUAL_EVENTS_ARE_NOT_SUPPORT_WITH_RECURRING_EVENT,"virtual.events.are.not.support.with.recurring.event");
        hashMap.put(NOT_ALLOW_TO_UPLOAD_ATTENDEES,"not.allow.to.upload.attendees");
        hashMap.put(SOME_OF_FIELDS_ARE_EMPTY_IN_ATTENDEE_CSV,"some.of.fields.are.empty.in.attendee.csv");
        hashMap.put(EMAIL_FIELD_SHOULD_CONTAIN_SINGLE_VALUE,"email.field.should.contain.single.value");
        hashMap.put(INVALID_EMAIL,"invalid.email");
        hashMap.put(ORDER_ID_HAVE_MUST_NUMBER_ONLY,"order.id.have.must.number.only");
        hashMap.put(UPLOAD_FILE_HEADER_NOT_CORRECT_FOR_ATTENDEE_CSV,"upload.file.header.not.correct.for.attendee.csv");
        hashMap.put(CSV_UPLOAD_SUCCESS_MESSAGE,"csv.upload.success.message");
        hashMap.put(ATTENDEE_LIST_NOT_FOUND,"attendee.list.not.found");
        hashMap.put(MAX_LIMIT_REACHED,"max.limit.reached");
        hashMap.put(ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME,"account.is.locked.please.try.after.sometime");
        hashMap.put(PLEDGE_INSTUCTION,"pledge.instuction");
        hashMap.put(MESSAGE_SERVICE_UNABLE_TO_SEND_MESSAGE,"message.service.unable.to.send.message");
        hashMap.put(THANKS_FOR_PURCHASE_PLAN, "successfully.purchased.plan.msg");
        hashMap.put(ONLY_ORGANIZER_TEAM_MEMBER_CAN_SUBSCRIBE_PLAN, "only.admin.owner.can.subscribe.plan");
        hashMap.put(MEMBER_CANNOT_SUBSCRIBE_PLAN, "member.not.allowed.to.subscribe.plan");
        hashMap.put(CHARGEBEE_CUSTOMER_NOT_FOUND, "customer.not.found.in.chargebee");
        hashMap.put(SUCCESS_SUBSCRIPTION_CANCELLED, "subscription.cancelled.successfully");
        hashMap.put(WHITE_LABEL_URL_NOT_FOUND, "white.label.url.not.found");
        hashMap.put(CANNOT_PURCHASE_MORE_THEN_TWENTY, "can.not.purchase.more.than.twenty");
        hashMap.put(CHARGE_PURCHASED_SUCCESS, "charge.purchased.success");
        hashMap.put(DES_CHARGE_PURCHASED_SUCCESS, "des.charge.purchased.success");
        hashMap.put(PAYMENT_COLLECTED, "payment.collected.successfully");
        hashMap.put(RAFFLE_INSTRUCTION,"raffle.instruction");
        hashMap.put(ORGANIZER_NOT_FOUND,"organizer.not.found");
        hashMap.put(ORGANIZER_URL_NOT_FOUND,"organizer.url.not.found");
        hashMap.put(USER_ALREADY_PRESENT_IN_TEAM,"user.already.present.in.team");
        hashMap.put(USER_NOT_ALLOW_TO_UPDATE,"user.not.allow.to.update");
        hashMap.put(ORGANIZER_IS_UPDATED_SUCCESSFULLY,"organizer.is.updated.successfully");
        hashMap.put(NOT_ALLOW_TO_EDIT_ORGANIZER_PAGE,"not.allow.to.edit.organizer.page");
        hashMap.put(CHARGEBEE_SUBSCRIPTION_NOT_FOUND,"chargebee.subscription.not.found");
        hashMap.put(NOT_VALID_EMAIL,"not_valid_email");
        hashMap.put(CONTACT_TO_YOU_SOON,"contact.to.you.soon");
        hashMap.put(NOT_SUPER_ADMIN,"not.super.admin");
        hashMap.put(EVNET_NOT_ASSOCIATED_WITH_THIS_ORGANIZER,"evnet.not.associated.with.this.organizer");
        hashMap.put(NO_UNSUBSCRIBEDUSERS_ARE_PRESENT,"no.unsubscribedusers.are.present");
        hashMap.put(USER_NOT_FOUND_IN_EVENT_TICKETS,"user.not.found.in.event.tickets");
        hashMap.put(EVENT_DELETED_SUCCESSFULLY,"event.deleted.successfully");
        hashMap.put(CAN_NOT_DELETE_EVENT,"can.not.delete.event");
        hashMap.put(PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_EMAIL_PARAMETER,"phone.number.already.attach.to.email.with.email.parameter");
        hashMap.put(MAKE_DUPLICATE_EVENT,"make.duplicate.event");
        hashMap.put(MEMBER_CANNOT_BE_MARKED_AS_BILLING_CONTACT,"member.cannot.be.marked.as.billing.contact");
        hashMap.put(CAN_NOT_UPDATE_BILLING_CONTACT,"can.not.update.billing.contact");
        hashMap.put(BILLING_CONTACT_UPDATED_SUCCESSFULLY,"billing.contact.updated.successfully");
        hashMap.put(TEAM_MEMBER_REMOVE_FROM_BILLING_CONTACT,"team.member.remove.from.billing.contact");
        hashMap.put(CAN_NOT_REMOVE_LAST_OWNER_FROM_BILLING_CONTACT,"can.not.remove.last.owner.from.billing.contact");
        hashMap.put(CAN_NOT_MARK_BILLING_CONTACT, "can.not.mark.billing.contact");
        hashMap.put(CAN_NOT_UPDATE_OWNER_OF_THE_ORGANIZER, "can.not.update.owner.of.the.organizer");
        hashMap.put(CAN_NOT_REMOVE_OWNER_FROM_BILLING_CONTACT,"can.not.remove.owner.from.billing.contact");
        hashMap.put(CAN_NOT_DELETE_BILLING_CONTACT,"can.not.delete.billing.contact");
        hashMap.put(DELETED_TEAM_MEMBER,"deleted.team.member");
        hashMap.put(OWNER_UPDATED_SUCCESSFULLY,"owner.updated.successfully");
        hashMap.put(CAN_NOT_REMOVE_LAST_BILLING_CONTACT, "can.not.remove.last.billing.contact");
        hashMap.put(CAN_NOT_DELETE_LAST_OWNER_BILLING_CONTACT,"can.not.delete.last.owner.billing.contact");
        hashMap.put(CAN_NOT_DELETE_OWNER,"can.not.delete.owner");
        hashMap.put(INVITATION_SENT,"invitation.sent");
        hashMap.put(ADD_EMBED_WIDGET_SETTINGS_SUCCESSFULLY,"add.embed.widget.settings.successfully");
        hashMap.put(ORGANIZER_OR_EVENT_ID_NOT_EXIST,"organizer.or.event.id.not.exist");
        hashMap.put(WIDGET_SETTING_ALREADY_EXISTS,"widget.setting.already.exists");
        hashMap.put(WIDGET_SETTING_TYPE_NOT_EXISTS,"widget.setting.type.not.exists");
        hashMap.put(WIDGET_SETTING_NOT_FOUND,"widget.setting.not.found");
        hashMap.put(NOT_AUTHORIZED_TO_CREATE,"not.authorized.to.create");
        hashMap.put(WHITE_LABEL_EVENT_NOT_FOUND,"white.label.event.not.found");
        hashMap.put(FAILED_TO_GET_USER_INFO,"failed.to.get.user.info");
        hashMap.put(FAILED_TO_GENERATE_USER_TOKEN,"failed.to.generate.user.token");
        hashMap.put(FAILED_TO_DELETE_SOLUTION_INSTANCE,"failed.to.delete.solution.instance");
        hashMap.put(FAILED_TO_RECONFIGURE_SOLUTION_INSTANCE,"failed.to.reconfigure.solution.instance");
        hashMap.put(FAILED_TO_GET_AUTH_CODE,"failed.to.get.auth.code");
        hashMap.put(DONATION_SUCCESSFUL,"donation.successful");
        hashMap.put(TRANSACTION_CONFIG_NOT_FOUND,"transaction.config.not.found");
        hashMap.put(WHITELABEL_STRIPE_NOT_CONFIGURED,"whitelabel.stripe.not.configured");
        hashMap.put(SUCCESSFULLY_CREATED_NEW_EVENT,"successfully.created.new.event");
        hashMap.put(EVENT_NAME_ALREADY_EXIST,"event.name.already.exist");
        hashMap.put(EVENT_NAME_CAN_NOT_NULL,"event.name.can.not.null");
        hashMap.put(CANNOT_CHANGE_ORGANIZER_AFTER_EVENT_PUBLISH,"cannot.change.organizer.after.event.publish");
        hashMap.put(VIRTUAL_EVENT_SETTINGS_CREATED,"virtual.event.settings.created");
        hashMap.put(VIRTUAL_EVENT_SETTINGS_UPDATED,"virtual.event.settings.updated");
        hashMap.put(VIRTUAL_EVENT_CUSTOM_LABEL_UPDATED,"virtual.event.custom.label.updated");
        hashMap.put(VIRTUAL_EVENT_ALREADY_CREATED,"virtual.event.already.created");
        hashMap.put(EVENT_CHALLENGE_CONFIG_NOT_FOUND,"event.challenge.config.not.found");
        hashMap.put(SETTING_NOT_ALLOWED,"setting.not.allowed");
        hashMap.put(VIRTUAL_EVENT_SETTINGS_NOT_FOUND,"virtual.event.settings.not.found");
        hashMap.put(POPUP_ACTIVATE_TICKETING_MODULE,"popup.activate.ticketing.module");
        hashMap.put(POPUP_FINISH_STRIPE_PAYMENTS,"popup.finish.stripe.payments");
        hashMap.put(POPUP_FINISH_STRIPE_PAYMENTS_FOR_SELLING,"popup.finish.stripe.payments.for.selling");
        hashMap.put(POPUP_FINISH_STRIPE_PAYMENTS_FOR_DONATIONS,"popup.finish.stripe.payments.for.donations");
        hashMap.put(SETTINGS_SAVED,"settings.saved");
        hashMap.put(PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_PARAMETER,"phone.number.already.attach.to.email.with.parameter");
        hashMap.put(NEED_TO_PUBLISH_EVENT,"need.to.publish.event");
        hashMap.put(COUNTER_EXCEEDED_CAP_USAGE_ITEMS,"counter.exceeded.cap.usage.items");
        hashMap.put(PRE_EVENT_ACCESS_IS_LIMITED,"pre.event.access.is.limited");
        hashMap.put(SESSION_ALREADY_EXISTS,"session.already.exists");
        hashMap.put(INVALID_SESSION_DATE_TIME,"invalid.session.date.time");
        hashMap.put(SESSION_SLOTS_COLLIDES,"session.slots.collides");
        hashMap.put(NETWORKING_RULE_NOT_FOUND,"networking.rule.not.found");
        hashMap.put(NETWORKING_RULE_ALREADY_EXISTS,"networking.rule.already.exists");
        hashMap.put(DUPLICATE_SESSION_DETAIL_ID_ENTRY,"duplicate.session.detail.id.entry");
        hashMap.put(ALREADY_TICKET_TYPE_USED,"already.ticket.type.used");
        hashMap.put(REGISTER_MAX_LIMIT_REACHED,"register.max.limit.reached");
        hashMap.put(CAN_NOT_UPLOAD_EMPTY_FILE,"can.not.upload.empty.file");
        hashMap.put(UPLOAD_FILE_HEADER_NOT_CORRECT,"upload.file.header.not.correct");
        hashMap.put(SESSION_CAN_NOT_BE_DELETE,"session.can.not.be.delete");
        hashMap.put(INVALID_SESSION_FORMAT,"invalid.session.format");
        hashMap.put(SESSION_POSITION_CHANGED_SUCCESSFULLY,"session.position.changed.successfully");
        hashMap.put(CAN_NOT_ALLOWED_FOR_CONCURRENT_SESSION,"can.not.allowed.for.concurrent.session");
        hashMap.put(SESSION_DELETED,"session.deleted");
        hashMap.put(SESSION_DUPLICATED,"session.duplicated");
        hashMap.put(SESSION_HIDDEN,"session.hidden");
        hashMap.put(SESSION_TIME_ERROR,"session.time.error");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.TIME,"time");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.TITLE,"title");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.FORMAT,"format");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.CAPACITY,"capacity");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.VIRTUAL_SESSION_LOCATION,"virtual.session.location");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.TAGS_TRACKS_CHARACTER_LIMIT,"virtual.session.tags.tracks.character.limit");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.SESSION_LOCATION,"session.location");
        hashMap.put(SIGNUP_TEXT,"signup.text");
        hashMap.put(KEY_VALUE_ALREADY_EXIST,"key.value.already.exist");
        hashMap.put(POSITION_CHANGED_SUCCESSFULLY,"position.changed.successfully");
        hashMap.put(AUDIENCE_FILTER_ALREADY_EXISTS,"audience.filter.already.exists");
        hashMap.put(AUDIENCE_FILTER_NOT_FOUND,"audience.filter.not.found");
        hashMap.put(SPEAKER_ALREADY_EXIST,"speaker.already.exist");
        hashMap.put(EMAIL_IS_ALREADY_PRESENT,"email.is.already.present");
        hashMap.put(SPEAKER_POSITION_CHANGED_SUCCESSFULLY,"speaker.position.changed.successfully");
        hashMap.put(N0_SPEAKER_PROFILE_PRESENT,"no.speaker.profile.present");
        hashMap.put(SOCIAL_MEDIA_URL_IS_INVALID,"social_media_url.is.invalid");
        hashMap.put(FIRST_NAME_NOT_BE_NULL,"first.name.not.be.null");
        hashMap.put(LAST_NAME_NOT_BE_NULL,"last.name.not.be.null");
        hashMap.put(PRIMARY_SESSION_INVALID, "primary.session.invalid");
        hashMap.put(SECONDARY_SESSION_INVALID, "secondary.session.invalid");
        hashMap.put(PRIMARY_SECONDARY_CONFLICT, "primary.secondary.conflict");
        hashMap.put(PRIMARY_SESSION_DUPLICATE, "primary.session.duplicate");
        hashMap.put(SECONDARY_SESSION_DUPLICATE, "secondary.session.duplicate");
        hashMap.put(SESSION_IDS_INVALID,"session.ids.invalid");
        hashMap.put(EMAIL_NOT_BE_NULL,"email.not.be.null");
        hashMap.put(EMAIL_NOT_VALID,"email.not.valid");
        hashMap.put(EMAIL_ALREADY_IN_CSV_FILE,"email.already.in.csv.file");
        hashMap.put(EMAIL_ALREADY_DB,"email.already.in.db");
        hashMap.put(TITAL_NOT_BE_NULL,"tital.not.be.null");
        hashMap.put(COMPANY_MUST_BE_NOT_NULL,"company.must.be.not.null");
        hashMap.put(LINKDIN_URL_NOT_VALID,"linkdin.url.not.valid");
        hashMap.put(INSTAGRAM_URL_NOT_VALID,"instagram.url.not.valid");
        hashMap.put(TWITTER_URL_NOT_VALID,"twitter.url.not.valid");
        hashMap.put(EMAIL_ALREADY_EXIST,"email.already.exist");
        hashMap.put(TICKETING_SETTING_NOT_FOUND,"ticketing.setting.not.found");
        hashMap.put(TICKETING_DISCOUNT_CODE_UPDATE_SUCCESS_MSG,"coupon.updated.successfully");
        hashMap.put(MORE_TICKET_COUPON_USED,"more.coupon.is.used.then.set");
        hashMap.put(TICKET_COUPON_PERCENT_GREATER_100,"ticketing.coupon.percentage");
        hashMap.put(CAN_NOT_NULL_XX_RELATIVE_TIME,"fill.the.recurring.relative.start.and.end.time");
        hashMap.put(TICKET_COUPON_ALREADY_EXIST,"ticketing.coupon.already.exist");
        hashMap.put(TICKETING_DISCOUNT_CODE_SAVE_SUCCESS_MSG,"discount.Coupon.Saved");
        hashMap.put(TICKETING_DISCOUNT_CODE_DELETE_SUCCESS_MSG,"coupon.deleted.successfully");
        hashMap.put(TICKETING_LIMITED_DISPLAY_CODE_SAVE_SUCCESS_MSG,"limited.display.code.saved");
        hashMap.put(TICKETING_LIMITED_DISPLAY_DISCOUNT_CODE_UPDATE_SUCCESS_MSG,"limited.display.code.updated");
        hashMap.put(TICKETING_LIMITED_DISPLAY_DISCOUNT_CODE_DELETE_SUCCESS_MSG,"limited.display.code.deleted");
        hashMap.put(TICKET_COUPON_USED,"discount.code.cannot.be.deleted.once.it.has.been.used");
        hashMap.put(TICKET_TYPE_DOES_NOT_GRANT_ACCESS,"ticket.type.does.not.grant.access");
        hashMap.put(AUTO_CREATE_MODERATOR,"auto.create.moderator");
        hashMap.put(SUCCESSFULLY_SAVED_WANTS_TO_LEARN,"successfully.saved.wants.to.learn");
        hashMap.put(SPEAKER_EMAIL_CHANGE_NOT_HAVE_ATTENDEE_ACCESS,"speaker.email.change.not.have.attendee.access");
        hashMap.put(SPEAKER_UPDATED,"speaker.updated");
        hashMap.put(VIRTUAL_PORTAL_IMAGE_NOT_FOUND,"virtual.portal.image.not.found");
        hashMap.put(SPEAKER_ALREADY_EXIST_WITH_EMAIL,"speaker.already.exist.with.email");
        hashMap.put(NOT_FOUND_BADGES,"not_found_badges");
        hashMap.put(BADGES_ADDED_SUCCESSFULLY,"badges.added.successfully");
        hashMap.put(BADGES_UPDATED_SUCCESSFULLY,"badges.updated.successfully");
        hashMap.put(BADGES_DELETED,"badges.deleted");
        hashMap.put(NOT_FOUND_BADGES_IMAGE,"not.found.badges.image");
        hashMap.put(DUPLICATE_BADGES_NAME,"duplicate.badges.name");
        hashMap.put(DUPLICATE_TICKETING_TYPE_ID,"duplicate.ticketing.type.id");
        hashMap.put(NETWORKING_LOUNGE_DELETED_BY_THE_ADMIN,"networking.lounge.deleted.by.the.admin");
        hashMap.put(NOT_ALLOWED_TO_ACCESS_VIRTUAL_EVENT_PAGE,"not.allowed.to.access.virtual.event.page");
        hashMap.put(DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_EVENT_START,"does.not.allowed.to.change.event.format.after.event.start");
        hashMap.put(DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_PRE_EVENT_START,"does.not.allowed.to.change.event.format.after.pre.event.start");
        hashMap.put(NOT_POSSIBLE_TO_ACTIVATE_AUTOPLAY,"not.possible.to.activate.autoplay");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.SHORT_DESCRIPTION,"short.description.should.not.be.more.than.150.characters");
        hashMap.put(TICKET_DOES_NOT_ALLOW_LOUNGES,"ticket.does.not.allow.lounges");
        hashMap.put(TICKET_DOES_NOT_ALLOW_EXPO,"ticket.does.not.allow.expo");
        hashMap.put(NOT_FOUND_KIOSK_MODE_CHECK_IN_DETAIL,"not.found.kiosk.mode.check.in.detail");
        hashMap.put(ALREADY_ADDED_KIOSK_DETAIL,"already.added.kiosk.detail");
        hashMap.put(FEATURE_NOT_AVAILABLE_FOR_FREE_PLAN, "feature.not.available.for.free.plan");
        hashMap.put(SINGLE_EVENT_APP_CHARGE,"singlp.charge");
        hashMap.put(UNLIMITED_MOBILE_APP,"unlimited.app");
        hashMap.put(NETWORKING_LOUNGE_DOCUMENT_UPLOADED,"networking.lounge.document.uploaded");
        hashMap.put(NETWORKING_LOUNGE_DOCUMENT_UPDATED,"networking.lounge.document.updated");
        hashMap.put(NETWORKING_LOUNGE_DOCUMENT_DELETED,"networking.lounge.document.deleted");
        hashMap.put(NETWORKING_DOCUMENT,"document");
        hashMap.put(NETWORKING_LINK,"link");
        hashMap.put(STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT, "starttime.or.endtime.is.not.in.valid.format");
        hashMap.put(NETWORKING_LOUNGE_VIDEO_UPDATED,"networking.lounge.video.updated");
        hashMap.put(BUSINESS_PLAN_2023_LOWERCASE,"2023_business_plan");
        hashMap.put(ESSENTIAL_PLAN_LOWERCASE,"essential_plan");
        hashMap.put(WHITE_LABEL_2023_LOWERCASE,"2023-white-label-plan");
        hashMap.put(ENTERPRISE_2023_LOWERCASE,"enterprise-plan-2023");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.ID,"session.id");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.SESSION_TYPE_FORMAT,"session.type.format");
        hashMap.put(CSV_SESSION_ERROR_MESSAGE.LOCATION_ID,"location.id");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.BUYER_ATTENDEE_ALREADY_EXISTS,"buyer.already.exists");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.HOLDER_ATTENDEE_ALREADY_EXISTS,"holder.already.exists");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.BUYER_AND_HOLDER_WITH_SAME_EMAIL_ALREADY_EXISTS,"buyer.and.holder.already.exists");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.FIRST_NAME_BLANK_MESSAGE,"first.name.blank");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_BLANK_MESSAGE,"email.blank");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.LAST_NAME_BLANK_MESSAGE,"last.name.blank");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_NOT_VALID_MESSAGE,"email.not.valid");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_LIMIT_MESSAGE,"email.limit.message");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.PROFILE_IMAGE_NOT_VALID_MESSAGE,"profile.not.valid");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.TRANSACTION_ID_NOT_VALID_MESSAGE,"transaction.id.not.valid");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.TRANSACTION_ID_SIZE_MESSAGE,"transaction.id.size.message");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.FIRST_NAME_SIZE_MSG,"first.name.size.limit");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.LAST_NAME_SIZE_MSG,"last.name.size.limit");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_SIZE_MSG,"email.name.size.limit");
        hashMap.put(CSV_ATTENDEE_ERROR_MESSAGE.TRANSACTION_ID_ALREADY_CREATED_ORDER,"transaction.id.already.created.order");

        return hashMap;
    }

    public static ResourceBundle getLanguageResourceBundle(String language) {
        Locale locale = new Locale(language != null ? language : EnumLabelLanguageCode.EN.toString());
        return ResourceBundle.getBundle("messages", locale);
    }

    private static String replaceScriptTag(String htmlString) {
        if (!StringUtils.isEmpty(htmlString)) {
            Pattern scriptPattern = Pattern.compile(SCRIPT_REGEX_STRING, Pattern.CASE_INSENSITIVE);
            String unEscapedHTML = Parser.unescapeEntities(htmlString, true);
            Matcher matcher = scriptPattern.matcher(unEscapedHTML);
            return  matcher.replaceAll("") ;
        }
        return htmlString;
    }

    public static String sanitizeStringBasic(String htmlString) {
        String updateHTMLString = replaceScriptTag(htmlString);
        if (!StringUtils.isEmpty(updateHTMLString) && checkStringContainsHTML(htmlString)) {
            HashMap<String , String> replaceBasicHTMLTags = new HashMap<>();
            updateHTMLString = replaceBasicTags(updateHTMLString,replaceBasicHTMLTags);
            updateHTMLString = replaceStringByHashMapPair(updateHTMLString,defaultTagsReplaceForOriginalSting());
            updateHTMLString = Parser.unescapeEntities(updateHTMLString, true);
            updateHTMLString = Jsoup.clean(updateHTMLString, "", whitelist, new Document.OutputSettings().prettyPrint(false).escapeMode(Entities.EscapeMode.xhtml));
            updateHTMLString = replaceStringByHashMapPair(updateHTMLString, defaultTagsReplaceForSanitizedSting());
            updateHTMLString = removeBodyTagsAfterSanitize(updateHTMLString);
            updateHTMLString = updateHTMLString.trim();
            updateHTMLString = updateHTMLString.replace(NEXT_LINE_TAG_STRING,"\n");
            updateHTMLString = updateHTMLString.replace("----DOUBLE_QUOTES_STRING----","\\\"");
            updateHTMLString = updateHTMLString.replace("&quot;","\\\"");
            updateHTMLString = replaceStringBasicTags(updateHTMLString,replaceBasicHTMLTags);
        }
        return updateHTMLString;
    }

    public static String replaceStringBasicTags(String updateHTMLString, Map<String, String> replaceBasicHTMLTags) {
        for (Map.Entry<String,String> entry:replaceBasicHTMLTags.entrySet()) {
            updateHTMLString = updateHTMLString.replace(entry.getKey(),entry.getValue());
        }
        return  updateHTMLString;
    }

    private static String replaceBasicTags(String updateHTMLString, HashMap<String, String> replaceBasicHTMLTags) {
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, docTypePattern, "<div type=\"docType", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, startHTMLPattern, "<div type=\"startHTML", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, endHTMLPattern, "<div type=\"endHTML", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, startHeadPattern, "<div type=\"startHead", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, endHeadPattern, "<div type=\"endHead", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, commentTagPattern, "<div type=\"comment", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, xmlTagPattern, "<div type=\"xml", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, anyOtherHashTagStartPattern, "<div type=\"customStart", "\">");
        updateHTMLString = replaceBasicHTMLCommonTags(updateHTMLString, replaceBasicHTMLTags, anyOtherHashTagEndPattern, "----customEndTag", "----");
        return updateHTMLString;
    }

    public static String replaceBasicHTMLCommonTags(String updateHTMLString, Map<String, String> replaceBasicHTMLTags, Pattern docTypePattern, String startReplacement, String endReplacement) {
        Matcher matcher = docTypePattern.matcher(updateHTMLString);
        if (matcher.find()) {
            for (int i = 0; i <= matcher.groupCount(); i++) {
                String replaceString = matcher.group(i);
                String toReplace = startReplacement + i + endReplacement;
                replaceBasicHTMLTags.put(toReplace, replaceString);
                updateHTMLString = updateHTMLString.replace(replaceString, toReplace);
            }
        }
        return updateHTMLString;
    }

    public static boolean checkStringContainsHTML(String htmlString) {
        String textOfHtmlString = Jsoup.parse(htmlString).text();
        if(!textOfHtmlString.equals(htmlString)) {
            return htmlPattern.matcher(htmlString).matches();
        }
        return false;
    }

    private static String removeBodyTagsAfterSanitize(String updateHTMLString) {
        if (updateHTMLString.startsWith("<body>")) {
            updateHTMLString = updateHTMLString.replaceFirst("<body>", "");
        }
        if (updateHTMLString.endsWith("</body>")) {
            updateHTMLString = replaceLast(updateHTMLString, "</body>", "");
        }
        return updateHTMLString;
    }

    private static String replaceStringByHashMapPair(String updateHTMLString,HashMap<String,String> replaceMap) {
        for (Map.Entry<String,String> entry : replaceMap.entrySet()) {
            updateHTMLString = updateHTMLString.replaceAll(entry.getKey(),entry.getValue());
        }
        return updateHTMLString;
    }

    public static String replaceLast(String text, String regex, String replacement) {
        return text.replaceFirst("(?s)" + regex + "(?!.*?" + regex + ")", replacement);
    }

    public static String sanitizeHTMLTemplate(String docString) {
        Document doc = Jsoup.parse(docString, "", Parser.htmlParser());
        CustomCleaner cleaner = new CustomCleaner(whitelist);
        doc = cleaner.clean(doc.outputSettings(new Document.OutputSettings().escapeMode(Entities.EscapeMode.xhtml).prettyPrint(false)));
        String returnString = doc.html();
        returnString = returnString.replaceFirst("<html>", "<html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\">");
        returnString= returnString.replace("[\n\r\t]", "").replace("&lt;","<").replace("&gt;",">").replace("<!--#if-->","</#if>");
        return returnString;
    }

    private static HashMap<String,String> defaultTagsReplaceForOriginalSting(){
        HashMap<String,String> mapToReplaceOriginalStringForDefaultTags = new HashMap<>();
        mapToReplaceOriginalStringForDefaultTags.put(NEXT_LINE_TAG_STRING,NEW_LINE_REPLACEMENT);
        mapToReplaceOriginalStringForDefaultTags.put(IF_TAG_COMMENT_STRING,IF_TAG_REPLACEMENT);
        mapToReplaceOriginalStringForDefaultTags.put(END_IF_COMMENT_STRING,END_IF_TAG_REPLACEMENT);
        mapToReplaceOriginalStringForDefaultTags.put(DOC_TYPE_STRING,DOC_TYPE_TAG_REPLACEMENT);
        return  mapToReplaceOriginalStringForDefaultTags;
    }

    private static HashMap<String,String>  defaultTagsReplaceForSanitizedSting() {
        HashMap<String, String> mapToReplaceOriginalStringForDefaultTags = new HashMap<>();
        mapToReplaceOriginalStringForDefaultTags.put(NEW_LINE_REPLACEMENT, NEXT_LINE_TAG_STRING);
        mapToReplaceOriginalStringForDefaultTags.put(IF_TAG_REPLACEMENT, IF_TAG_COMMENT_STRING);
        mapToReplaceOriginalStringForDefaultTags.put(END_IF_TAG_REPLACEMENT, END_IF_COMMENT_STRING);
        mapToReplaceOriginalStringForDefaultTags.put(DOC_TYPE_TAG_REPLACEMENT, DOC_TYPE_STRING);
        return mapToReplaceOriginalStringForDefaultTags;
    }
    public static Integer getDaysFromToday(Date pastDate) {
        if (pastDate != null) {
            long timeDiff = getCurrentUTCDate().getTime()- pastDate.getTime();
            return (int) (timeDiff / (1000 * 60 * 60 * 24));
        }
        return DAYSOFYEAR;
    }

    public static Date getCurrentUTCDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(UTC_TIMEZONE);
        return calendar.getTime();
    }

    public static String convertToDecimalPlace(double amount, int decimalPlace) {
        return String.format("%."+decimalPlace+"f",amount);
    }
    public static String getSplitName(String name){
        if(name!= null){
            String[] nameParts = StringUtils.isNotBlank(name) ? name.split(" ") : STRING_EMPTY.split(" ");
            if (nameParts.length > 1) {
                return nameParts[0];
            } else {
                return name;
            }
        }
        else{
            return STRING_EMPTY;
        }
    }

    public static String replaceSpecialCharacter(String stringToReplace) {
        return StringUtils.isNotBlank(stringToReplace) ?stringToReplace.replaceAll("[^a-zA-Z0-9]", ""): stringToReplace;
    }

    public static String getCountryNameFromCountryCode(String countryCode, String language) {
        if(StringUtils.isNotBlank(countryCode)) {
            Locale l = new Locale(language, countryCode);
            return l.getDisplayCountry();
        }
        return STRING_EMPTY;
    }

    public static String convertUrlBySeparateWords(String url) {
        if (StringUtils.isNotBlank(url)) {
            url = StringUtils.normalizeSpace(url);
            url = url.replaceAll(ATOZ_BOTHCASE_AND_NUMBERS_AND_HYPHEN, STRING_EMPTY);

            if(url.length() > 50) {
                url = url.substring(0, 50);
            }
            if (url.matches(ATOZ_LOWERCASE)) {
                return url;
            } else if (url.matches(ATOZ_UPPERCASE)) {
                return url.toLowerCase();
            } else {
                url = url.toLowerCase();
                url = url.replaceAll(WHITE_SPACE, HYPHEN);
                url = url.replaceAll(DOUBLE_HYPHEN, HYPHEN);
                url = (!StringUtils.isBlank(url)  && url.substring(0, 1).equalsIgnoreCase("-"))
                        ? url.substring(1) : url;
                return url;
            }
        }else{
            return url;
        }
    }

    public static void prepareDownloadableResponseHeader(HttpServletResponse response, String fileName, String contentType) {
        if(StringUtils.isNotBlank(fileName)){
            try {
                fileName = getFileNameByContentType(fileName, contentType);
                fileName =  URLEncoder.encode(fileName, UTF8).replace("\\+", "%20");
            } catch (UnsupportedEncodingException e) {
                log.error("Error while encoding file name filename {}  error message {}  error {}", fileName, e.getMessage(), e);
            }
        }
        if(CONTENT_CSV.equals(contentType)) {
            response.setCharacterEncoding(UTF_16);
        }
        response.setContentType(contentType);
        response.setHeader(CONTENT_DISPOSITION, String.format(ATTACHMENT_FILENAME, fileName));
    }

    private static String getFileNameByContentType(String fileName, String contentType) {
        switch (contentType) {
            case CONTENT_CSV:
                fileName += DOT_CSV;
                break;
            case APPLICATION_PDF:
                fileName += DOT_PDF;
                break;
            case APPLICATION_ZIP:
                fileName += DOT_ZIP;
                break;
            case APPLICATION_VND_APPLE_PKPASS:
                fileName += DOT_PKPASS;
                break;
            case APPLICATION_JSON:
                fileName += DOT_JSON;
                break;
            default:
                break;
        }
        return fileName;
    }

    public static boolean isNotNullOrBlank(String str) {
        return StringUtils.isNotBlank(str) && !STRING_NULL.equalsIgnoreCase(str);
    }


    public static String generateUniqueCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHARACTERS.charAt(secureRandom.nextInt(CHARACTERS.length())));
        }
        return code.toString();
    }

    public static String replaceSpaceWithUnderScore(String input) {
        return input.replaceAll(WHITE_SPACE, STRING_UNDERSCORE).trim();
    }

    public static String replaceUnderScoreWithSpace(String input) {
        return input.replaceAll(STRING_UNDERSCORE, SINGLE_WHITE_SPACE).trim();
    }


}
