package com.accelevents.services.keystore;

import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROOrganizerService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.utils.Constants;
import io.lettuce.core.KeyScanCursor;
import io.lettuce.core.ScanArgs;
import io.lettuce.core.ScanCursor;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.async.RedisAsyncCommands;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.accelevents.utils.Constants.SESSIONS_UNDERSCORE;

@Service
@ConditionalOnProperty(name = "use.redis.session.store", havingValue = "true", matchIfMissing = true)
public class GamificationRedisCacheImpl extends AbstractGamificationCacheStoreService implements GamificationCacheStoreService<String, Object> {


    private static final Logger log = LoggerFactory.getLogger(GamificationRedisCacheImpl.class);

    private ROEventService roEventService;
    private ROOrganizerService organizerService;
    private ROWhiteLabelService whiteLabelService;
    private ROEventLevelSettingService eventLevelSettingService;
    private RedisTemplate<String, Object> gamificationRedisObjectTemplate;

    private StatefulRedisConnection<String, String> gamificationLettuceConnection;

    @Autowired
    public GamificationRedisCacheImpl(ROEventService roEventService, ROOrganizerService organizerService,
                                      ROWhiteLabelService whiteLabelService, RedisTemplate<String, Object> gamificationRedisObjectTemplate,ROEventLevelSettingService eventLevelSettingService,StatefulRedisConnection<String, String> gamificationLettuceConnection) {
        super(roEventService, organizerService, whiteLabelService,eventLevelSettingService);
        this.gamificationRedisObjectTemplate = gamificationRedisObjectTemplate;
        this.gamificationLettuceConnection = gamificationLettuceConnection;
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit timeUnit) {
        gamificationRedisObjectTemplate.opsForValue().set(key,value, timeout,timeUnit);
    }

    @Override
    public void set(String key, Object value) {
        gamificationRedisObjectTemplate.opsForValue().set(key,value);
    }

    @Override
    public Object get(Object key) {
        return gamificationRedisObjectTemplate.opsForValue().get(key);
    }

    @Override
    public boolean delete(String key) {
        return gamificationRedisObjectTemplate.delete(key);
    }

    @Override
    public void clearSessionCache(Long eventId) {
        asyncDeleteKeysByPattern(SESSIONS_UNDERSCORE + eventId + Constants.STRING_UNDERSCORE + "*");
    }

    /**
     * Async method to delete keys matching a pattern in batches
     */
    @Async
    public void asyncDeleteKeysByPattern(String pattern) {
        ExecutorService executor = Executors.newFixedThreadPool(4);
        List<Future<?>> tasks = new ArrayList<>();

        RedisAsyncCommands<String, String> asyncCommands = gamificationLettuceConnection.async();

        String cursor = "0";
        try {
            List<String> batch = new ArrayList<>();
            do {
                KeyScanCursor<String> scanCursor = asyncCommands.scan(
                        ScanCursor.of(cursor),
                        ScanArgs.Builder.matches(pattern).limit(2000)
                ).get(30, TimeUnit.SECONDS);

                List<String> keys = scanCursor.getKeys();
                if (keys != null) {
                    for (String rawKey : keys) {
                        if (StringUtils.isNotEmpty(rawKey) && rawKey.startsWith(SESSIONS_UNDERSCORE)) {
                            batch.add(rawKey);
                        }

                        if (batch.size() >= 2000) {
                            log.info("Delete redis session keys in batches {}", batch.size());
                            List<String> keysToDelete = new ArrayList<>(batch);
                            tasks.add(executor.submit(() -> deleteKeys(keysToDelete)));
                            batch.clear();
                        }
                    }
                }

                cursor = scanCursor.getCursor();

            } while (!cursor.equals("0"));

            if (!batch.isEmpty()) {
                log.info("Delete redis session keys, remaining keys {}", batch.size());
                List<String> keysToDelete = new ArrayList<>(batch);
                tasks.add(executor.submit(() -> deleteKeys(keysToDelete)));
            }

            for (Future<?> task : tasks) {
                task.get();
            }
        } catch (Exception e) {
            log.error("Exception while clearing session cache pattern {} and exception message {}", pattern, e.getMessage(), e);
        } finally {
            executor.shutdown();
        }
    }


    private void deleteKeys(List<String> keys) {
        if (!keys.isEmpty()) {
            gamificationRedisObjectTemplate.unlink(keys); // Batch delete
        }
    }

    @Override
    public Long increment(String key) {
        return gamificationRedisObjectTemplate.opsForValue().increment(key);
    }

    @Override
    public void expire(String key, long timeout, TimeUnit timeUnit) {
        gamificationRedisObjectTemplate.expire(key, timeout, timeUnit);
    }
}
