package com.accelevents.services.keystore;

import java.util.concurrent.TimeUnit;

public interface GamificationCacheStoreService<K, V> { //NOSONAR
    void set(String key, V value, long timeout, TimeUnit timeUnit);

    void set(String key, V value);

    V get(Object key);

    boolean delete(String key);

    void set2FaForEvent(Long eventId, Boolean flag);

    Boolean is2FaEnabledForEvent(long eventId);

    void set2FaForOrganizer(Long orgId, Boolean flag);

    Boolean is2FaEnabledForOrganizer(Long orgId);

    void set2FaForWhiteLabel(Long whiteLabelId, Boolean flag);

    Boolean is2FaEnabledForWhiteLabel(Long whiteLabelId);

    Boolean isSalesforceEnableForEvent(long eventId);

    void setSalesforceEnableForEvent(Long eventId, Boolean flag);

    Boolean isSalesforceEnableForWL(Long whiteLabelId);

    void setSalesforceEnableForWL(Long whiteLabelId, Boolean isSalesforceEnableForEvent);

    void clearSessionCache(Long eventId);

    Boolean isSsoLoginRequiredForEvent(Long eventId);

    Long increment(String key);

    void expire(String key, long timeout, TimeUnit timeUnit);
}
