package com.accelevents.services.keystore;

import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROOrganizerService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.utils.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@ConditionalOnProperty(name = "use.redis.session.store", havingValue = "false", matchIfMissing = true)
public class GamificationInMemoryCacheImpl extends AbstractGamificationCacheStoreService implements GamificationCacheStoreService<String, Object> {

    private ROEventService eventService;
    private ROOrganizerService organizerService;
    private ROWhiteLabelService whiteLabelService;
    private ROEventLevelSettingService eventLevelSettingService;

    private static class ValueWithExpiry {
        Object value;
        long expiryTimeMillis; // 0 = never expires

        ValueWithExpiry(Object value, long ttlMillis) {
            this.value = value;
            this.expiryTimeMillis = (ttlMillis > 0)
                    ? System.currentTimeMillis() + ttlMillis
                    : 0;
        }

        boolean isExpired() {
            return expiryTimeMillis > 0 && System.currentTimeMillis() > expiryTimeMillis;
        }
    }

    private ConcurrentHashMap<String, ValueWithExpiry> map = new ConcurrentHashMap<>();



    @Autowired
    public GamificationInMemoryCacheImpl(ROEventService eventService, ROOrganizerService organizerService, ROWhiteLabelService whiteLabelService,ROEventLevelSettingService eventLevelSettingService) {
        super(eventService, organizerService, whiteLabelService,eventLevelSettingService);
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit timeUnit) {
        long ttlMillis = timeUnit.toMillis(timeout);
        map.put(key, new ValueWithExpiry(value, ttlMillis));
    }

    @Override
    public void set(String key, Object value) {
        map.put(key, new ValueWithExpiry(value, 0));
    }

    @Override
    public Object get(Object key) {
        ValueWithExpiry wrapper = map.get(key);
        if (wrapper == null) return null;

        if (wrapper.isExpired()) {
            map.remove(key);
            return null;
        }
        return wrapper.value;
    }

    @Override
    public boolean delete(String key) {
        return map.remove(key)!=null;
    }

    @Override
    public void clearSessionCache(Long eventId) {
        map.keySet().removeIf(key -> key.startsWith(Constants.SESSIONS_UNDERSCORE + eventId + Constants.STRING_UNDERSCORE));
    }

    @Override
    public Long increment(String key) {
        ValueWithExpiry wrapper = map.compute(key, (k, existing) -> {
            if (existing == null || existing.isExpired()) {
                return new ValueWithExpiry(new AtomicInteger(1), 1000); // 1s TTL
            } else {
                AtomicInteger counter = (AtomicInteger) existing.value;
                counter.incrementAndGet();
                return existing;
            }
        });

        return ((AtomicInteger) wrapper.value).longValue();
    }

    @Override
    public void expire(String key, long timeout, TimeUnit timeUnit) {
        ValueWithExpiry wrapper = map.get(key);
        if (wrapper != null) {
            wrapper.expiryTimeMillis = System.currentTimeMillis() + timeUnit.toMillis(timeout);
        }
    }
}
