package com.accelevents.domain.enums;

public enum AttributeValueType {
	DROPDOWN("dropdown"),
    TEXT("text"),
    TEXT_BLOCK("TEXT_BLOCK"),
    PASSWORD("password"),
    NUMBER("number"),
    EMAIL("email"),
    DATE("date"),
    IMAGE("image"),
    BILLING_ADDRESS("BILLING_ADDRESS"),
    SHIPPING_ADDRESS("SHIPPING_ADDRESS"),
    COUNTRY("COUNTRY"),
    STATE("STATE"),
    UPLOAD("UPLOAD"),
    MULTIPLE_CHOICE("MULTIPLE_CHOICE"),
    COND<PERSON>IONAL_QUE("CO<PERSON><PERSON>IONAL_QUE"),
    VAT_FIELD("VAT"),
	SHORT_TEXT("short_text"),
	LONG_TEXT("long_text"),
    NPS("NPS"),
    STAR_RATING("star_rating"),
    ADDRESS("ADDRESS"),
    INTEREST("INTEREST"),
    RADIO("radio"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_UPLOAD("MULT<PERSON>LE_UPLOAD"),
    NUMBER_RATING("number_rating"),
    SINGLE_CHECKBOX("SINGLE_CHECKBOX"),
    EVENT_NAME("EVENT_NAME"),
    EVENT_FORMAT("EVENT_FORMAT"),
    EVENT_DESCRIPTION("EVENT_DESCRIPTION"),
    EVENT_START_DATE("EVENT_START_DATE"),
    EVENT_END_DATE("EVENT_END_DATE"),
    EVENT_LOCATION("EVENT_LOCATION"),
    EVENT_LOGO("EVENT_LOGO"),
    EVENT_CURRENCY("EVENT_CURRENCY"),
    EVENT_TEMPLATE("EVENT_TEMPLATE"),
    ORGANIZER("ORGANIZER"),
    PHONE_NUMBER("PHONE_NUMBER"),
    OTHER_EMAIL_ADDRESS("OTHER_EMAIL_ADDRESS"),
    CC_EMAIL_ADDRESS("CC_EMAIL_ADDRESS");

	private final String type;

	AttributeValueType(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}
}
