package com.accelevents.helpers;

import com.accelevents.dto.TicketHolderPDFDto;
import com.accelevents.dto.TicketHolderPDFEventDataDto;
import com.accelevents.messages.TicketType;
import com.accelevents.utils.Constants;
import com.accelevents.utils.StringTools;
import com.accelevents.utils.TimeZoneUtil;
import net.glxn.qrgen.QRCode;
import net.glxn.qrgen.image.ImageType;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class TicketingPDFHelper {

    public static StringBuilder ticketPDFNew(TicketHolderPDFEventDataDto ticketHolderPDFEventDataDto, List<TicketHolderPDFDto> holderPDFData) throws UnsupportedEncodingException {//NOSONAR
        StringBuilder mainBodyStr = new StringBuilder();
        boolean isFirstTicket = true;
        for (TicketHolderPDFDto holder : holderPDFData) {
        
            if(holder.getAllowPDFDownload() || ticketHolderPDFEventDataDto.isOnePdfAllow()){//NOSONAR
            	
            	
            	// Determine the ticket design to use based on priority:
            	// 1. Check if a custom template is present in `holderCustomTicketDesign` for this specific ticket.
            	// 2. If not found, check ticket-level settings in the `ticketing` table (e.g., whitelabel settings or custom event level configurations).
            	// 3. If not defined at above to cases , fallback to the default template.
            	
                String holderBodyPDF = !ObjectUtils.isEmpty(holder.getCustomTicketDesignHtml()) ? holder.getCustomTicketDesignHtml() :
                		ticketHolderPDFEventDataDto.getTicketPdfDesign();
                
                String pageBreakStyle = isFirstTicket ? "" : "page-break-before: always";
                mainBodyStr.append("<div style=\"").append(pageBreakStyle).append("\">");

                String unpaidOrderString = Constants.STRING_EMPTY;
                if (Constants.UNPAID.equalsIgnoreCase(ticketHolderPDFEventDataDto.getTicketingOrderStatusName())) {
                    unpaidOrderString = Constants.THIS_TICKET_HAS_NOT_YET_BEEN_PAID;
                } else if (ticketHolderPDFEventDataDto.isSendEmailForMarkAsPaid()) {
                    unpaidOrderString = Constants.THIS_TICKET_HAS_BEEN_MARK_AS_PAID;
                }
                String qrCodeFilePath = new String(Base64.encodeBase64(QRCode.from(holder.getBarcode()).to(ImageType.PNG).stream().toByteArray()), StandardCharsets.UTF_8);

				/* in the html when ${QR_code} merge tag pass like this */
                String qrCodeImageTag = "data:image/png;base64," + qrCodeFilePath;

             // Extract old QR code from the ticketPdfDesign if it exists
                String oldQrCodeFilePath = "";
                if (StringUtils.isNotEmpty(ticketHolderPDFEventDataDto.getTicketPdfDesign())) {
                    oldQrCodeFilePath = StringUtils.substringBetween(
                        ticketHolderPDFEventDataDto.getTicketPdfDesign(),
                        "<div class=\"tac padding3\"><img src=\"data:image/jpeg;base64,",
                        "\" width=\""
                    );
                }

                //TimeZoneService timeZoneService = new TimeZoneServiceImpl();
                Date eventStartDateTz = TimeZoneUtil.getDateInLocal(ticketHolderPDFEventDataDto.getEventStartDate(), ticketHolderPDFEventDataDto.getEquivalentTimeZone());
                Date eventEndDateTz = TimeZoneUtil.getDateInLocal(ticketHolderPDFEventDataDto.getEventEndDate(), ticketHolderPDFEventDataDto.getEquivalentTimeZone());

                Date eventStartDateWithoutTime = null;
                Date eventEndDateWithoutTime= null;
                DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                try {
                    eventStartDateWithoutTime = formatter.parse(formatter.format(eventStartDateTz));
                    eventEndDateWithoutTime = formatter.parse(formatter.format(eventEndDateTz));
                } catch (ParseException e) {
                    e.printStackTrace();
                }

                String eventStartAndEndDateTime;
                if (eventStartDateWithoutTime.compareTo(eventEndDateWithoutTime) == 0) {
                    eventStartAndEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true) + " at "
                            + StringTools.formatCalanderTime(eventStartDateTz, false) + " - "
                            + StringTools.formatCalanderTime(eventEndDateTz, false);
                } else {
                    eventStartAndEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true) + " at "
                            + StringTools.formatCalanderTime(eventStartDateTz, false) + " - "
                            + StringTools.formatCalanderDate(eventEndDateTz, true, true) + " at "
                            + StringTools.formatCalanderTime(eventEndDateTz, false);
                }

                if (StringUtils.isEmpty(ticketHolderPDFEventDataDto.getWlUrl())) {
                    //not present
                    String isWhitelabelPresentString = "<div><p class=\"tac fs14\">Powered by Accelevents</p><div class=\"tac\"><img style=\"width:auto;\" height=\"50px\" width=\"70px\" src=" + ticketHolderPDFEventDataDto.getAccelLogo() + " /></div></div><p class=\"tac fs14\">www.accelevents.com</p>";
                    holderBodyPDF = holderBodyPDF.replace("${isWhitelabelPresent}", isWhitelabelPresentString);
                    holderBodyPDF = holderBodyPDF.replace("${whiteLabelLogo}", "<div class=\"bee-block bee-block-3 bee-image\"><img class=\"bee-center bee-fixedwidth\" src=\"https://d2y8br9fe6j45e.cloudfront.net/images/27755/Smooth_Accelevents_Default_Event_Logo_Black.webp\" style=\"max-width:257px;\" alt=\"\"></div>");
                    holderBodyPDF = holderBodyPDF.replace("${wl_url}", "www.accelevents.com");
                    holderBodyPDF = holderBodyPDF.replace("${wl_firmName}", "Accelevents");

                } else {
                    String wlUrl = StringTools.getValueOrDefault(ticketHolderPDFEventDataDto.getWlUrl(), Constants.STRING_EMPTY);
                    String wLHeaderLogo = StringUtils.isNotBlank(ticketHolderPDFEventDataDto.getEventOrWhiteLabelLogo()) ? ticketHolderPDFEventDataDto.getEventOrWhiteLabelLogo().replaceAll(Constants.STRING_BLANK, "%20") : Constants.STRING_EMPTY;
                    String isWhitelabelPresentString = "<div><p class=\"tac fs14\">Powered by " + ticketHolderPDFEventDataDto.getWlFirmName() + "</p><div class=\"tac\"><img style=\"width:auto;\" height=\"50px\" width=\"70px\" src='" + wLHeaderLogo +
                            "' /></div></div><p class=\"tac fs14\">" + wlUrl + "</p>";
                    holderBodyPDF = holderBodyPDF.replace("${isWhitelabelPresent}", isWhitelabelPresentString);
                    holderBodyPDF = holderBodyPDF.replace("${whiteLabelLogo}", "<div class=\"bee-block bee-block-3 bee-image\"><img class=\"bee-center bee-fixedwidth\" src=\"" + wLHeaderLogo + "\" style=\"max-width:100px;max-height:100px;\" alt=\"\"></div>");
                    holderBodyPDF = holderBodyPDF.replace("${wl_firmName}", ticketHolderPDFEventDataDto.getWlFirmName());
                    holderBodyPDF = holderBodyPDF.replace("${wl_url}", wlUrl);

                }

                String eventOrWhiteLabelLogo = ticketHolderPDFEventDataDto.getEventOrWhiteLabelLogo();
                eventOrWhiteLabelLogo = eventOrWhiteLabelLogo.replace("1-300x50/", "");

                holderBodyPDF = holderBodyPDF.replace("${eventOrWhiteLabelLogo}", eventOrWhiteLabelLogo);
                holderBodyPDF = holderBodyPDF.replace("${event_logo}", ticketHolderPDFEventDataDto.getEventLogo());
                holderBodyPDF = holderBodyPDF.replace("${event_name}", ticketHolderPDFEventDataDto.getEventName());
                holderBodyPDF = holderBodyPDF.replace("${event_start_and_end_date_time}", ticketHolderPDFEventDataDto.isHideEventDate()?" " : (eventStartAndEndDateTime + " " + ticketHolderPDFEventDataDto.getTimeZoneId()));
                holderBodyPDF = holderBodyPDF.replace("${location}", ticketHolderPDFEventDataDto.getEventAddress());
                holderBodyPDF = holderBodyPDF.replace("${holderfullname}", holder.getHolderName());
                holderBodyPDF = holderBodyPDF.replace("${order_number}", String.valueOf(holder.getOrderNumber()));
                
                if(!StringUtils.isEmpty(oldQrCodeFilePath))
                holderBodyPDF = holderBodyPDF.replace(oldQrCodeFilePath, qrCodeFilePath);
                
                holderBodyPDF = holderBodyPDF.replace("${QR_code}", qrCodeImageTag);
                holderBodyPDF = holderBodyPDF.replace("${unpaidOrderString}", unpaidOrderString);

                if(StringUtils.isNotBlank(ticketHolderPDFEventDataDto.getChartKey())){
                    holderBodyPDF = holderBodyPDF.replace("${seatNumber}", "- Seat No. ".concat(String.valueOf(holder.getSeatNumber())));
                }else {
                    holderBodyPDF = holderBodyPDF.replace("${seatNumber}",Constants.STRING_EMPTY);
                }
                holderBodyPDF = holderBodyPDF.replace("${ticketCode}",StringUtils.isNotBlank(holder.getTicketCode())?holder.getTicketCode():Constants.STRING_EMPTY);
                String priceString = "";
                if (TicketType.FREE.name().equalsIgnoreCase(holder.getTicketTypeName())) {
                    priceString = String.format("%s ", holder.getTicketTypeName());
                } else {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(holder.getTicketTypeName());

                    if(holder.getPrice()>0){
                        stringBuilder.append(String.format(" (Price: %s%s)", ticketHolderPDFEventDataDto.getCurrencySymbol(),
                                DoubleHelper.roundValueTwoDecimalReturnString(holder.getPrice())));
                    }

                    priceString = stringBuilder.toString();
                }
                Date dateOfOrder = TimeZoneUtil.getDateInLocal(holder.getDateOfPurchase(), ticketHolderPDFEventDataDto.getEquivalentTimeZone());
                String orderDate =  StringTools.formatCalanderDate(dateOfOrder, true, true) + " at "
                        + StringTools.formatCalanderTime(dateOfOrder, false);
                holderBodyPDF = holderBodyPDF.replace("${purchaserfullname}", holder.getPurchaserName());
                holderBodyPDF = holderBodyPDF.replace("${order_date}", orderDate+ " "+ ticketHolderPDFEventDataDto.getTimeZoneId());
                holderBodyPDF = holderBodyPDF.replace("${Ticket_type_with_price}", priceString);
                holderBodyPDF = holderBodyPDF.replace("${ticket_number}", String.valueOf(holder.getEventTicketId()));

                String ticketingTypeDesc = holder.getTicketingTypeDesc() != null ? holder.getTicketingTypeDesc() : Constants.STRING_EMPTY;

                if (ticketHolderPDFEventDataDto.isCustomTemplate()) {
                    holderBodyPDF = holderBodyPDF.replace("${desc}", ticketingTypeDesc);
                    holderBodyPDF = holderBodyPDF.replace("${auto_assigned_number}",StringUtils.isNotBlank(holder.getAutoAssignedNumber())? holder.getAutoAssignedNumber():Constants.STRING_EMPTY);
                } else {
                    if (StringUtils.isBlank(holder.getAutoAssignedNumber())) {
                        holderBodyPDF = holderBodyPDF.replace("${auto_assigned_number}", "<td width=\"50%\" class=\"solidGreyBorder\">\n" +
                                "                        <div class=\"padding3\">" + ticketingTypeDesc + "</div>\n" +
                                "                        </td>");
                    } else {
                        holderBodyPDF = holderBodyPDF.replace("${auto_assigned_number}", " <td class=\"solidGreyBorder padding3\" rowspan=\"2\"   width=\"50%\">\n" +
                                "                        <div class=\"padding3\">" + ticketingTypeDesc + "</div>\n" +
                                "                        </td>\n" +
                                "                        <td class=\"solidGreyBorder padding3\" colspan=\"2\"  >\n" +
                                "                        <div class=\"label\">Assigned number</div>\n" +
                                "                        <br />\n" +
                                "                        <div class=\"label_data\">" + holder.getAutoAssignedNumber() + "</div>\n" +
                                "                        </td>\n" +
                                "                        </tr>");

                    }
                }

                mainBodyStr.append(holderBodyPDF);

                isFirstTicket = false;
            }
        }
        return mainBodyStr;
    }

    private static String getLabelBlockText(String label, String data){
        String s = "<div class=\"label\">" + label +"</div><br/>" +
                "<div class=\"label_data\">"+ data +"</div>";

        return s;
    }
}
