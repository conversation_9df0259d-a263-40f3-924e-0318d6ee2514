package com.accelevents.utils;

public interface ConstantsAPIGW {

    String EVENT_API_URL = "/rest/cache/event/{eventurl}";
    String ED_SPONSORS = EVENT_API_URL + "/sponsors";
    String ED_SPEAKER_PORTAL = EVENT_API_URL + "/speaker/portal";
    String ED_TAG_AND_TRACK_TYPE = EVENT_API_URL + "/tagAndTrack/{type}";

    String ED_IS_VAT_ENABLED = EVENT_API_URL + "/is-vat-enabled";
    String ED_REGISTER_COUNT = EVENT_API_URL + "/register-count";

    String VE_SETTINGS = EVENT_API_URL + "/ve-portal/ve-settings";
    String VE_MULTI_LANGUAGE_LABEL = EVENT_API_URL + "/ve-portal/ve-settings/language/{languageCode}";

    String WL_HOST_BASE_URL = "/rest/cache/wl/host-base-url";
    String WL_SETTINGS = "/rest/cache/wl/{whiteLabelURL}";

    String ED_TICKETING = EVENT_API_URL + "/ticketing";
    String ED_TICKETING_SETTINGS = ED_TICKETING + "/settings";
    String ED_TICKETING_TICKET_TYPES = ED_TICKETING + "/ticket-types";
    String ED_TICKETING_FEE = ED_TICKETING + "/fee";
    String ED_TICKETING_TICKET_TYPES_CATEGORIES = ED_TICKETING_TICKET_TYPES + "/categories";

    String ED_TICKETING_TICKET_TYPES_DESC_AND_FEE = ED_TICKETING_TICKET_TYPES + "/description-and-fees";

    String ED_PAYMENT = EVENT_API_URL + "/payment";
    String ED_PAYMENT_OFFLINE_CONFIGURATION = ED_PAYMENT + "/offline-payment-configuration";
    String ED_PAYMENT_SETTINGS = ED_PAYMENT + "/settings";

    String ED_BEE_FREE_DEFAULT_HOME_PAGE = EVENT_API_URL + "/page/default-home-page";
    String ED_BEE_FREE_PUBLISHED_PAGE = EVENT_API_URL + "/page/{pageUrl}";

    String EMBED_WIDGET_SETTINGS_URL = "/rest/cache/widget/settings";

    String ED_EMBED_WIDGET_SETTINGS_TYPE_EVENT = EMBED_WIDGET_SETTINGS_URL + "/event/{id}"  ;
    String ED_EMBED_WIDGET_SETTINGS_TYPE_ORGANIZER = EMBED_WIDGET_SETTINGS_URL + "/organizer/{id}"  ;

    String SESSION_API_URL = EVENT_API_URL + "/session";

    String ED_SESSION_SPEAKERS = SESSION_API_URL + "/speakers";
    String ED_SESSION_TRACKS = SESSION_API_URL + "/tracks";
    String ED_SESSION_SPONSORS = SESSION_API_URL + "/sponsors";
    String ED_SESSION_EXHIBITORS = SESSION_API_URL + "/exhibitors";

    String ORGANIZER_API_URL = "/rest/cache/organizer/{organizerUrl}";
    String WHITELABEL_API_URL = "/rest/cache/wl/{whiteLabelUrl}";
    String ED_EVENT_DIRECTORY_PAGE = "/event-directory-page";

    String ED_AUCTION_ITEMS = "/auction/items";

    String ED_AUCTION_SETTING = "/auction/settings";
    String ED_AUCTION_TOTAL_FUND_RAISED = "/auction/total-fund-raised";

    String ED_TICKETING_TICKET_TYPES_REMAINING_COUNT = "/ticketing/ticket-types/remaining-count";

}
