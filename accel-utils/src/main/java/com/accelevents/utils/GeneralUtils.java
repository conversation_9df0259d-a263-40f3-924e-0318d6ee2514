package com.accelevents.utils;

import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Entities;
import org.jsoup.safety.Whitelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.DOLLAR;
import static com.accelevents.utils.Constants.STRING_UNDERSCORE;

public class GeneralUtils {

    private static final Logger log = LoggerFactory.getLogger(GeneralUtils.class);
    private static final String EMAIL_PATTERN = Constants.EMAIL_VALIDATION;
    private static final Pattern PATTERN = java.util.regex.Pattern.compile(EMAIL_PATTERN);
    private static final Map<String,String> headersMap = new HashMap<>(){};

    static {
        headersMap.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
    }

    public static double getRoundValue(double value){
        return BigDecimal.valueOf(value).setScale(2, BigDecimal.ROUND_HALF_EVEN).doubleValue();
    }

    public static double getRoundUP(double value){
        return BigDecimal.valueOf(value).setScale(2, BigDecimal.ROUND_UP).doubleValue();
    }




    public static double getRoundDown(double value){
        return BigDecimal.valueOf(value).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
    }

    public static double convertCentToDollar(Long cent){
        return null != cent ? BigDecimal.valueOf(Double.valueOf(cent) / 100).setScale(2, BigDecimal.ROUND_HALF_EVEN).doubleValue():0;
    }

    public static Long getLong(long value){
        return value > 0 ? value : null;
    }
    public static long getLong(Long value){
        return value == null ? 0 : value;
    }

    public static String prepareCommaStringFromMap(Map<String, Object> map) {
        try {
            return map.entrySet().stream().map(e->e.getKey()+":"+e.getValue()).collect(Collectors.joining(","));
        } catch (Exception e){
            return "";
        }
    }
    public static  boolean isValidEmailAddress(String email) {
        if (StringUtils.isNotEmpty(email)) {
            Matcher m = PATTERN.matcher(email);
            return m.matches();
        } else {
            return false;
        }
    }

    public static boolean isValidURL(String url) {
        try{
            new URL(url);
            return true;
        } catch (Exception e){
            log.warn("URL {} is invalid, reason {}", url, e.getMessage());
            return false;
        }
    }

    public static List<String> convertCommaSeparatedToList(String commaSeparated){
        if(StringUtils.isNotBlank(commaSeparated)){
            return new ArrayList<>(Arrays.asList(commaSeparated.split(Constants.STRING_COMMA)));
        }
        return new ArrayList<>();
    }

    public static List<Long> convertCommaSeparatedToListLong(String commaSeparated){
        if(StringUtils.isBlank(commaSeparated)){
            return new ArrayList<>();
        }
        return new ArrayList<String>(Arrays.asList(StringUtils.split(commaSeparated, Constants.STRING_COMMA)))
                .stream().map(Long::valueOf).collect(Collectors.toList());
    }

    public static Map<String,String> convertListToMap(List<String> value){
        return value.stream().map(str -> str.split(":")).collect(Collectors.toMap(str -> str[0], str -> str[1]));
    }

    public static String getValueFromStandardCommaSeparatedKeyValueString(String key, String commaSeparatedStr){
        return convertListToMap(convertCommaSeparatedToList(commaSeparatedStr)).get(key);
    }

    public static String convertLongListToCommaSeparated(List<Long> list){
        return StringUtils.join(list, ',');
    }

    public static String convertListToCommaSeparated(List<String> list){
        return StringUtils.join(list, ',');
    }

    public static String getRefundType(String paymentGateway) {
        if(StringUtils.isBlank(paymentGateway)) {
            return Constants.STRIPE;
        }
        switch (paymentGateway) {
            case Constants.SQUARE:
                return Constants.SQUARE;
            case Constants.STRIPE:
                return Constants.STRIPE;
            default:
                return paymentGateway;
        }
    }

    public static String getEventPath(){
        return "/e/";
    }

    public static String getMyProfileUrl(){
        return "/u/myProfile#tickets";
    }
    public static String getOrderUrl(){
        return "/u/myprofile/order/";
    }

    public static String getCheckoutPath(){
        return "/e/u/checkout/";
    }

    public static String replace(String message, String variableName, String replaceValue){
        if (null != message) {
            return message.replace(variableName, StringUtils.isNoneBlank(replaceValue)? replaceValue : Constants.STRING_EMPTY);
        } else {
            return Constants.STRING_EMPTY;
        }
    }

    public static Map<String,String> ticketActiveOrdersMapping() {
        Map<String, String> ticketActiveOrdersMapping = new HashMap();
        ticketActiveOrdersMapping.put("purchaseDate", "orderDate");
        ticketActiveOrdersMapping.put("seatNumbers", "seats");
        ticketActiveOrdersMapping.put("ticketOrderNumber", "id");
        ticketActiveOrdersMapping.put("eventName", "event.name");
        ticketActiveOrdersMapping.put("eventUrl", "event.eventURL");
        return ticketActiveOrdersMapping;
    }

    public static long getInCentsForSquare(double amount) {
        return BigDecimal.valueOf(amount * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN).longValue();
    }

    public static long convertToSmallestCurrencyUnit(double amount, String currency) {
        if (StringUtils.isNotBlank(currency) && currency.equalsIgnoreCase("JPY")) {
            return BigDecimal.valueOf(amount).setScale(0, RoundingMode.CEILING).longValue();
        }
        return BigDecimal.valueOf(amount * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN).longValue();
    }
    public static double convertToPlatformCurrencyUnit(double amount, String currency) {
        if (StringUtils.isNotBlank(currency) && currency.equalsIgnoreCase("JPY")) {
            return BigDecimal.valueOf(amount).setScale(0, RoundingMode.CEILING).longValue();
        }
        return BigDecimal.valueOf(Double.valueOf(amount) / 100).setScale(2, BigDecimal.ROUND_HALF_EVEN).doubleValue();
    }
    public static String getStackTrace(){
        StringBuilder sb = new StringBuilder();
        for(StackTraceElement stackTraceElement : Thread.currentThread().getStackTrace()){
            if(stackTraceElement!=null && stackTraceElement.toString().contains("com")){
                sb.append(stackTraceElement.toString());
                sb.append("===");
            }
        }
        return sb.toString();
    }

    public static void threadSleep(long l) {
        try {
            Thread.sleep(l);
        } catch (InterruptedException e) { //NOSONAR
            e.printStackTrace();
        }
    }

    /**
     * Pick random n elements from given list
     * @param list the list that need to pick elements from
     * @param n number of elements to be pick
     * @return returns list of selected elements
     */
    public static List pickNRandomElements(List list, int n) {
        List copy = new ArrayList<>(list);
        Collections.shuffle(copy);
        return n > copy.size() ? copy.subList(0, copy.size()) : copy.subList(0, n);
    }


    /**
     * Returns next unique name for given string
     * Example : Sample Text = Sample Text (1)
     *           Sample Text (1) = Sample Text (2)
     *           Sample Text (2) = Sample Text (3)
     *           and so on...
     * for null or empty string returns an empty string
     * @param str
     * @return next unique name
     */
    public static String getNextUniqueName(String str) {
        if (StringUtils.isBlank(str)) return Constants.STRING_EMPTY;

        int start = str.lastIndexOf("(");
        int end = str.lastIndexOf(")");

        if (start > -1 && end > -1) {
            String number = str.substring(start + 1, end);

            StringBuilder sb = new StringBuilder();
            sb.append(str, 0, start + 1)
                    .append(Long.parseLong(number) + 1)
                    .append(str, end, str.length());

            return sb.toString();
        } else {
            StringBuilder sb = new StringBuilder(str);
            return sb.append(" (1)").toString();
        }
    }

    public static boolean isStringContainsUniCode(String str) {
        boolean isUnicode = false;

        if(StringUtils.isNotBlank(str)){
            for (char c : str.toCharArray()) {
                if (!CharUtils.isAscii(c)) {
                    isUnicode= true;
                }
            }
        }

        return isUnicode;
    }

    public static String checkAndGetFromArray(String[] arr, int index){
        return (arr.length>=(index+1)) ? (arr[index] != null)? arr[index] :Constants.STRING_EMPTY : Constants.STRING_EMPTY;
    }

    /**
     * Escape special char from a String
     * e.g. test% -> test\%
     *
     * @param string string that need to escaped special chars
     * @return escaped special char string
     */
    public static String escapeSpecialCharacter(String string) {
        if (StringUtils.isNotBlank(string)) {
            string= string.replaceAll("([%_\\\\])", "\\\\$1");
        }
        return  string;
    }

    public static boolean is5xxServerError(int statusCode) {
        int statusCodeSeries = statusCode/100;
        return statusCodeSeries == 5;
    }

    public static boolean isTooManyRequestError(int statusCode) {
        return statusCode == 429;
    }

    public static boolean is2xxSuccess(int statusCode) {
        int statusCodeSeries = statusCode/100;
        return statusCodeSeries == 2;
    }

    public static double parseDouble(String value){
        try{
            return Double.parseDouble(value);
        }
        catch (Exception e){
            return 0;
        }
    }

    public static String removeXSSAttackWordsFromString(String input) {
        if(StringUtils.isNotBlank(input)) {
            String output = Jsoup.clean(input, "", Whitelist.basic(), new Document.OutputSettings().prettyPrint(false).escapeMode(Entities.EscapeMode.extended));
            output = output.replace("&amp;", "&").replace("&lt;", "<").replace("&gt;", ">")
                    .replace("&quot;", "\"").replace("&#x27;","'");
           return output;
        }
        return Constants.STRING_EMPTY;
    }

    public static String replaceAllNonNumericCharacters(String input) {
        if(StringUtils.isNotBlank(input)) {
            return input.replaceAll("\\D","");
        }
        return Constants.STRING_EMPTY;
    }

    /**
     * Converts string to camelCase format
     * Example: "Custom text test" -> "CustomTextTest"
     * @param input Input string to convert
     * @return CamelCase string
     */
    public static String toCamelCase(String input) {
        if (StringUtils.isBlank(input)) {
            return Constants.STRING_EMPTY;
        }

        // Split by spaces and other common delimiters, then capitalize first letter of each word
        return Arrays.stream(input.trim().split("[\\s_-]+"))
                .filter(word -> !word.isEmpty())
                .map(word -> Character.toUpperCase(word.charAt(0)) +
                           (word.length() > 1 ? word.substring(1).toLowerCase() : Constants.STRING_EMPTY))
                .collect(Collectors.joining());
    }

    public static String formatAttributeKey(String attributeName, Long attributeId, String suffix) {
        StringBuilder result = new StringBuilder();

        // Add attribute name (camelCase if not blank)
        if (StringUtils.isNotBlank(attributeName)) {
            result.append(toCamelCase(attributeName));
        }

        // Add suffix if not null
        if (suffix != null) {
            result.append(suffix);
        }

        // Add attribute ID if not null
        if (attributeId != null) {
            result.append(attributeId);
        }

        return result.toString();
    }

    /**
     * Formats attribute key in the standard format: $CamelCaseName_ID
     * Example: formatAttributeKey("Custom text test", 201) -> "$CustomTextTest_201"
     * @param attributeName Original attribute name
     * @param attributeId Attribute ID
     * @return Formatted key with $ prefix and _ID suffix
     */
    public static String formatAttributeKey(String attributeName, Long attributeId) {
        return formatAttributeKey(attributeName, attributeId, STRING_UNDERSCORE);
    }

    public static double getTaxRate(double taxPercentage) {
        return BigDecimal.valueOf(taxPercentage).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
    }

    public static List<Long> mergeTwoCommaSeparatedStringIntoListLong(String str1, String str2) {
        List<Long> combinedList = new ArrayList<>();
        if(StringUtils.isNotBlank(str1)) {
            combinedList.addAll(convertCommaSeparatedToListLong(str1));
        }
        if(StringUtils.isNotBlank(str2)) {
            combinedList.addAll(convertCommaSeparatedToListLong(str2));
        }
        return combinedList;
    }

    public static boolean areAllElementsIdenticalInLongList(Collection<Long> list1, Collection<Long> list2) {
        // Check if both lists are null or empty
        if (list1 == null || list2 == null) {
            return list1 == list2; // Both must be null to be equal
        }

        // Check if both lists are empty
        if (list1.isEmpty() && list2.isEmpty()) {
            return true;
        }

        // If sizes are different, the lists cannot be equal
        if (list1.size() != list2.size()) {
            return false;
        }
        return new HashSet<>(list1).containsAll(list2);
    }

    public static Map<String, String> getApplicationJsonHeaderMap() {
         return headersMap;
    }

    public static String removeUnicode(String input) {
        return input.replaceAll("[^\\x00-\\x7F]", "");
    }

    public static boolean hasOnlyNullFields(Object obj) {
        if (obj == null) return true;

        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                if (field.get(obj) != null) {
                    return false;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public static String getFileName(String type){

        switch (type) {
            case Constants.CSV_REGISTRATION_APPROVAL_EXPO:
                return "Exhibitor";
            case Constants.CSV_REGISTRATION_APPROVAL_SPEAKER:
                return "Speaker";
            default:
                return "Attendee";
        }
    }

    public static String getDateAndTimeFormatString(String createdDate){
        if(StringUtils.isNotBlank(createdDate)){
            return createdDate.substring(0, createdDate.length() - 5);
        }
        return "";

    }

    /**
     * Generate a unique value by adding a counter-suffix if the value already exists,
     * and ensure the final value does not exceed the specified character limit.
     *
     * <p>If the base value plus suffix exceeds the character limit, it will be truncated to fit within the limit.</p>
     *
     * @param baseValue     The initial value
     * @param existsFunction Function to check if the value exists
     * @param separator     Separator to use between base value and counter
     * @param maxLength     Maximum allowed length for the generated value
     * @return A unique value within the specified length
     */
    public static String generateUniqueValueWithCharacterLimit(String baseValue,
                                       Function<String, Boolean> existsFunction,
                                       String separator,
                                       int maxLength) {
        String value = baseValue;
        log.info("Generating unique value for base: {}, maxLength: {}", baseValue, maxLength);
        // Initial truncation if base value exceeds maxLength
        if (baseValue.length() > maxLength) {
            baseValue = baseValue.substring(0, maxLength);
            value = baseValue;
        }

        // Check if value already exists
        if (existsFunction.apply(value)) {
            int counter = 1;

            // Try generating a unique value within the character limit
            while (existsFunction.apply(value)) {
                String counterSuffix = separator + counter;
                int maxBaseLength = maxLength - counterSuffix.length();

                // Truncate base value to fit with suffix
                String truncatedBase = baseValue.length() > maxBaseLength
                        ? baseValue.substring(0, maxBaseLength)
                        : baseValue;

                value = truncatedBase + counterSuffix;
                counter++;

                // Safety check
                if (counter > 100) {
                    log.error("Unable to generate a unique value for base: {} after 100 attempts", baseValue);
                    return null;
                }
            }
        }
        log.info("Generated unique value: {}", value);
        return value.length() > maxLength ? value.substring(0, maxLength) : value;
    }

}


