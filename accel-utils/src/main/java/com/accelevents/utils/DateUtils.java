package com.accelevents.utils;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DateFormat;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeParseException;
import java.util.TimeZone;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;

public class DateUtils {

    public static OffsetDateTime getDateInUTCFromTimeZoneStr(String date){
        java.time.format.DateTimeFormatter DATE_TIME_FORMATTER = java.time.format.DateTimeFormatter
                .ofPattern("yyyy-MM-dd'T'HH:mm:ssZ");
        try {
            OffsetDateTime odtInstanceAtOffset = OffsetDateTime.parse(date, DATE_TIME_FORMATTER);
            return odtInstanceAtOffset.withOffsetSameInstant(ZoneOffset.UTC);
        } catch (DateTimeParseException e) {
            e.printStackTrace();
        }
        return null;
	}

    public static Date formatDateToEventTimeZoneTest(Date date, String timeZoneId){
        DateFormat dateFormatter = new SimpleDateFormat("MM/dd/yyyy");
        Date dt = getDateInLocal(date, timeZoneId);
        dt = setHoursMinutesToZero(dt);
        return dt;
    }

    public static String formatDateToEventTimeZone(Date date, String equivalentTimeZone) {
        if (null == date) {
            return null;
        }
        DateFormat dateFormatter = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
        return dateFormatter.format(getDateInLocal(date, equivalentTimeZone));
    }

	public static OffsetDateTime add24Hours1SecLess(OffsetDateTime startTime){
		return startTime.plusSeconds(86399);
	}

	public static void main(String[] args) {
		LocalDate oldDate = convertToLocalDateViaInstant(getFormattedDate("24/07/2020 01:55:10"));
		LocalDate oldDate2 = convertToLocalDateViaInstant(getFormattedDate("24/07/2020 17:55:10"));
		Period diff = Period.between(oldDate, oldDate2);
		System.out.println(diff.getDays()+1);
	}

	public static LocalDate convertToLocalDateViaInstant(Date dateToConvert) {
		return dateToConvert.toInstant()
				.atZone(ZoneId.of("NZ"))
				.toLocalDate();
	}

	public static Date addDaysWithoutTime(Date date, int days){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, days);
		return setHoursMinutesToZero(calendar.getTime());
	}

	public static long getDaysBetween(Date startDate , Date endDate){
		return TimeUnit.MILLISECONDS.toDays(setHoursMinutesToZero(endDate).getTime() - setHoursMinutesToZero(startDate).getTime());
	}

	public static boolean isDateBetweenStartAndEnd(Date startDate, Date endDate){
		return new Date().after(startDate) && new Date().before(endDate);
	}

	public static Date getDateDifference(Date startdate, Date enddate, String timezone) {
		String[] convert = timezone.split(":");
		DateTimeZone timeZone = DateTimeZone.forOffsetHoursMinutes(Integer.valueOf(convert[0]),
				Integer.valueOf(convert[1]));
		DateTime endDatetime = new DateTime(enddate.getTime(), timeZone);
		return new Date(endDatetime.toDate().getTime() - startdate.getTime());
	}

	public static Date getDate(Date date, String timezone) {
		if (timezone != null && !"".equals(timezone)) {
			ZonedDateTime zdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of(timezone));
			LocalDateTime localDateTime = zdt.toLocalDateTime();
			return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
		} else {
			return date;
		}
	}

	public static Date getDateTimeZone(Date date, String requiretimezone) {
		Date d = new Date();

		long currenttime = d.getTime() - (TimeZone.getDefault().getRawOffset() - getOffSet(requiretimezone));
		long expectedtime = date.getTime();

		long diff = expectedtime - currenttime;
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(d.getTime() + diff);
		return c.getTime();
	}

	public static long getOffSet(String offset) {
		TimeZone tz = TimeZone.getTimeZone(ZoneId.of(offset));
		long msFromEpochGmt = new Date().getTime();

		return tz.getOffset(msFromEpochGmt);
	}

	public static String getFormattedDateString(Date date) {
		if (date == null)
			return "";
		String pattern = Constants.DATE_FORMAT;
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		return formatter.format(date);
	}

    public static String getFormattedOnlyDateString(Date date) {
        if (date == null)
            return "";
        String pattern = ONLY_DATE_FORMAT;
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        return formatter.format(date);
    }
	public static String getFormattedDateWithPattern(Date date, String pattern) {
		if (date == null)
			return "";
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		return formatter.format(date);
	}

	public static String getDefaultFormatDateStr(){
		return new SimpleDateFormat(Constants.DATE_FORMAT).format(new Date());
	}

	public static boolean validateDaysOfWeek(String[] input){
		if(input == null || input.length == 0){
			return false;
		}

		DateFormatSymbols dfs = new DateFormatSymbols();
		for (String weekday : input) {
			if(!Arrays.asList(dfs.getWeekdays()).contains(weekday)) {
				return false;
			}
		}
		return true;
	}

	public static Date getFormattedDate(String date) {
		SimpleDateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
		try {
			return formatter.parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Date getLocalFormattedDate(String date) {
		SimpleDateFormat formatter = new SimpleDateFormat(LOCAL_DATE_FORMAT);
		try {
			return formatter.parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Date getFormattedDateTimeZonString(String date ,String pattern) {
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
		try {
			return formatter.parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Date getFormattedDate(String date, String pattern) {
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		try {
			return formatter.parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Date getAddSeconds(Date date, long seconds) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.SECOND, (int)seconds);
		return calendar.getTime();
	}

	public static Date getAddedDate(int seconds, int minutes, int hours) {
		DateTime objDateTime = null;
		objDateTime = new DateTime();
		objDateTime = objDateTime.plusSeconds(seconds);
		objDateTime = objDateTime.plusMinutes(minutes);
		objDateTime = objDateTime.plusHours(hours);
		return objDateTime.toDate();
	}

	public static Date getAddedHours(Date date, int hours){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.HOUR, hours);
		return calendar.getTime();
	}

	public static Date getAddedMinutes(Date date, int minutes){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MINUTE, minutes);
		return calendar.getTime();
	}

    public static Date getAddedDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    public static Date getAddedMinutes(DateTime auctionEnd, int minutes) {
		auctionEnd = auctionEnd.plusMinutes(minutes);
		return auctionEnd.toDate();
	}

	public static String getDateString(Date date, String dateFormat) {
		DateTimeFormatter objDateFormat = null;
		DateTime objDateTime = null;
		String objFormateDate = null;
		try {
			objDateFormat = DateTimeFormat.forPattern(dateFormat);
			objDateTime = new DateTime(date);
			objFormateDate = objDateFormat.print(objDateTime);
		} catch (Exception exception) {
			objFormateDate = null;
		} finally {
			objDateFormat = null;
			objDateTime = null;
		}
		return objFormateDate;
	}

	public static String formatDate(Date date, String dateFormat){
		DateFormat format = new SimpleDateFormat(dateFormat);
		return format.format(date);
	}
	
	public static Date getCurrentDate() {
		return new Date();
	}

	public static Date getCurDateWithOutTime(){
		return setHoursMinutesToZero(new Date());
	}

	public static Date addDaysToNow(int days){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.DATE, days);
		return setHoursMinutesToZero(calendar.getTime());
	}

	public static Date convertUTCToLocalDate(String utcDate) {
		TimeZone t = TimeZone.getDefault();
		DateFormat utcFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
		utcFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

		try {
			return getDateTimeZone(utcFormat.parse(utcDate), t.getID());
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;

	}

	public static List<Date> getDatesBetweenForDashboardCharts(Date startDate, Date endDate) {
		List<Date> result = new ArrayList<>();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);
		while (calendar.getTime().before(endDate)) {
			result.add(calendar.getTime());
			calendar.add(Calendar.DATE, 1);
		}
		// for adding end date
		result.add(calendar.getTime());
		return result;
	}

	public static Date setHoursMinutesToZero(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	public static Date setHoursMinutesToEndOfDay(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		return calendar.getTime();
	}


	public static int getMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.DAY_OF_MONTH);
	}

	public static Date getOnlyDateFromDate(Date date){
		DateFormat formatter = new SimpleDateFormat(ONLY_DATE_FORMAT);
		if (date == null){
            return null;
        }
		try {
                return formatter.parse(formatter.format(date));
		} catch (ParseException e) {
			e.printStackTrace();
		}
        return null;
	}

	public static Boolean checkDateBetween(Date startDate, Date endDate, Date checkDate) {
		return (( checkDate.equals(startDate) || checkDate.after(startDate)) &&
				(checkDate.equals(endDate) || checkDate.before(endDate)));
	}

	public static long getNumberDaysBasedOnEventStartAndEndDateTime(Date firstDate, Date secondDate) {
		long diff = (secondDate.getTime()-firstDate.getTime())/(60*60 * 1000);

		long numberOfDays = (diff/24) + 1;

		return numberOfDays;

	}

	public static long getNumberDaysInclusiveBothTheDatesInEventTimeZone(Date firstDate, Date secondDate, String equivalentTimezone) {

		LocalDate oldDate = convertToLocalDateViaInstant(firstDate, equivalentTimezone);
		LocalDate oldDate2 = convertToLocalDateViaInstant(secondDate, equivalentTimezone);
		long diff = Duration.between(oldDate.atStartOfDay(), oldDate2.atStartOfDay()).toDays(); // Period.Between was not work when both the dates from different month.

		return diff+1;
    }

    public static long getNumberDaysInclusiveBothTheDatesInEvent(Date firstDate, Date secondDate) {

        LocalDate oldDate = new java.sql.Date(firstDate.getTime()).toLocalDate();
        LocalDate oldDate2 = new java.sql.Date(secondDate.getTime()).toLocalDate();
        long diff = Duration.between(oldDate.atStartOfDay(), oldDate2.atStartOfDay()).toDays(); // Period.Between was not work when both the dates from different month.

        return diff+1;
    }

	public static LocalDate convertToLocalDateViaInstant(Date dateToConvert, String eventTimeZoneId) {
		return dateToConvert.toInstant()
				.atZone(ZoneId.of(TimeZoneUtil.equivalentTimeZone(eventTimeZoneId)))
				.toLocalDate();
	}

	public static int getValueOfDaysOfWeek(String dayOfWeek) {
		int value=1;
		switch (dayOfWeek){
			case ("Monday"):
				value =2;
				break;
			case ("Tuesday"):
				value =3;
				break;
			case ("Wednesday"):
				value =4;
				break;

			case ("Thuresday"):
				value =5;
				break;
			case ("Friday"):
				value =6;
				break;
			case ("Saturday"):
				value =7;
				break;
			case ("Sunday"):
				value =1;
				break;
		}
		return value;
	}
    public static String getFormattedDateInString(String date, String pattern) {

        SimpleDateFormat formatter1 = new SimpleDateFormat(pattern);
        SimpleDateFormat formatter2 = new SimpleDateFormat(LOCAL_DATE_FORMAT);

        try {
            Date dateNew  = formatter1.parse(date);
            return formatter2.format(dateNew);
        } catch (ParseException e) {
            return date;
        }
    }

    public static String getLastActiveTime(Long timestamp, String eventTimeZone) {

        String userLastActivity;
        int userLastActiveTime;

        ZoneId zoneId = ZoneId.of(TimeZoneUtil.getTimeZoneByEquivalentTimeZone(eventTimeZone).getEquivalentTimezone());

        LocalDateTime timeStamp = Instant.ofEpochMilli(timestamp).atZone(zoneId).toLocalDateTime();
        LocalDateTime currentDate = LocalDateTime.now(zoneId);

        Duration duration = Duration.between(timeStamp, currentDate);
        Period period = Period.between(timeStamp.toLocalDate(), currentDate.toLocalDate());

        if (period.getYears() > 0) {
            userLastActivity = YEAR;
            userLastActiveTime = period.getYears();
        } else if (period.getMonths() > 0) {
            userLastActivity = MONTH;
            userLastActiveTime = period.getMonths();
        } else if (period.getDays() > 0) {
            userLastActivity = DAY;
            userLastActiveTime = period.getDays();
        } else if (duration.toHours() > 0) {
            userLastActivity = HOUR;
            userLastActiveTime = (int) duration.toHours();
        } else if (duration.toMinutes() > 0) {
            userLastActivity = MINUTE;
            userLastActiveTime = (int) duration.toMinutes();
        } else {
            userLastActivity = SECOND;
            userLastActiveTime = (int) duration.getSeconds();
        }
        StringBuilder timeAgo = new StringBuilder();
        timeAgo.append(userLastActiveTime).append(STRING_BLANK).append(userLastActivity).append(userLastActiveTime != 1 ? SUFFIX_S : STRING_EMPTY).append(AGO);
        return timeAgo.toString();
    }


    public static String getDateFromFormatInUTC(Date date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            SimpleDateFormat sdf = new SimpleDateFormat(Constants.DATE_SIMPLE_FORMAT);
            sdf.setTimeZone(java.util.TimeZone.getTimeZone("UTC"));
            return sdf.format(calendar.getTime());
    }

    public static String getDateFromFormatInUTC(Date date, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setTimeZone(java.util.TimeZone.getTimeZone("UTC"));
        return sdf.format(calendar.getTime());
    }

    public static Date addDaysInDate(Date date, int days){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        return calendar.getTime();
    }

    public static String getCurrentTimeInUTCFormat(){
        java.time.format.DateTimeFormatter dtf = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        return dtf.format(LocalDateTime.now(ZoneId.of("UTC")));
    }

    /**
     * Method used to check the date is after the past date of given hour
     * @param date
     * @param hour hour
     * @param eventTimeZone event equivalent timezone
     * @return true if the date is after the past x hour
     */

    public static boolean isDateIsAfterThePastTimeAndBeforeTheCurrentTime(Date date, int hour, String eventTimeZone) {

        ZoneId zoneId = ZoneId.of(TimeZoneUtil.getTimeZoneByEquivalentTimeZone(eventTimeZone).getEquivalentTimezone());
        LocalDateTime pastDateTime = date.toInstant()
                .atZone(zoneId)
                .toLocalDateTime();

        // Current date and time
        LocalDateTime now = LocalDateTime.now(zoneId);

        // Calculate hours ago from now
        LocalDateTime hoursAgoDate = now.minusHours(hour);

       //Check if transaction date is strictly after the past x hour mark and before the current time
        return pastDateTime.isAfter(hoursAgoDate) && pastDateTime.isBefore(now);

    }

    /**
     * Validates whether the given date string matches the specified date pattern.
     * @param date    the date string to validate, may be null or blank
     * @param pattern the date format pattern (e.g., "MM/dd/yyyy", "yyyy-MM-dd")
     * @return {@code true} if the date string is valid and matches the pattern;
     *         {@code false} otherwise
     */
    public static boolean isValidDate(String date, String pattern) {
        if (StringUtils.isBlank(date)) {
            return false;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        formatter.setLenient(false);
        try {
            formatter.parse(date.trim());
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    public static Date parseDateStrict(String date, String pattern) {
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        formatter.setLenient(false); // strict parsing
        try {
            return formatter.parse(date);
        } catch (ParseException e) {
            // e.printStackTrace(); // optional
        }
        return null;
    }

    public static boolean isDateValidStrict(String date, String pattern) {
        return parseDateStrict(date, pattern) != null;
    }


}
