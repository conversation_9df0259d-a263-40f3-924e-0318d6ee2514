package com.accelevents.utils;

import com.accelevents.dto.CategoryPositionDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;


public class JsonMapper {

	private static final Logger log = LoggerFactory.getLogger(JsonMapper.class);

	private static ObjectMapper objectMapper = new ObjectMapper();

    public static final TypeReference<List<Map<String, Object>>> listOfMapTypeReference = new TypeReference<>() {};
	
	public static <T> T stringtoObject(String jsonString, Class<T> valueType){
		try {
			if(StringUtils.isNotBlank(jsonString)){
				return objectMapper.readValue(jsonString, valueType);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static String convertToString(Object object){
		try {
			return objectMapper.writeValueAsString(object);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static <T> T stringToObjectUsingGSON(String jsonString, Class<T> valueType){
		Gson gson = new Gson();
		try {
			T dto = gson.fromJson(jsonString, valueType);
			if(dto == null){
				dto = valueType.getDeclaredConstructor().newInstance();
			}
			return dto;
		} catch (IllegalAccessException | InstantiationException | NoSuchMethodException | InvocationTargetException e) {
			log.error(e.getMessage() , "{} String: {}" , jsonString, e);
			e.printStackTrace();
		}
		return null;
	}

	public static String parseToJsonString(Object object){
		Gson gson = new Gson();
		return gson.toJson(object);
	}

	public static JsonElement toJSONTree(Object object){
		Gson gson = new Gson();
		return gson.toJsonTree(object);
	}

    public static Map<Long, List<CategoryPositionDto>> getCategoryPositionInMap(String jsonInString) {
        ObjectMapper mapper = new ObjectMapper();
        Map<Long, List<CategoryPositionDto>> linkKeyValueDtos = new HashMap<>();
        if (StringUtils.isNotBlank(jsonInString)) {
            try {
                linkKeyValueDtos = mapper.readValue(jsonInString, new TypeReference<Map<Long, List<CategoryPositionDto>>>() {
                });
            } catch (IOException e) {
                log.error("getCategoryPositionInMap | {}", e.getMessage());
            }
        }
        return linkKeyValueDtos;
    }

    public static String getJsonCategoryPosition(Map<Long, List<CategoryPositionDto>> keyValueDtos) {
        if (CollectionUtils.isEmpty(keyValueDtos)) {
            return null;
        }
        Gson gson = new Gson();
        return gson.toJson(keyValueDtos);
    }

    public static <T> List<T> getListFromJsonArray(String jsonString, Class<T> clazz) {
        try {
            if(StringUtils.isNotBlank(jsonString)) {
                return objectMapper.readValue(jsonString, new TypeReference<List>() {});
            }
        } catch (IOException e) {
            log.warn("getArrayFromString - could not create list {} | {}", clazz.getSimpleName(), e.getMessage());
        }
        return Collections.EMPTY_LIST;
    }

    public static <T> List<T> parseJsonArray(String json, Class<T> clazz) {
        try {
            if(StringUtils.isNotEmpty(json)){
                return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
            }
        } catch (IOException e) {
            log.warn("Error while parsing json to array  json {}, message {}, error {} ", json, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public static <T> T stringToObjectWithTypeReference(String json, TypeReference<T> typeReference) {
        try {
            if(StringUtils.isNotEmpty(json)){
                return objectMapper.readValue(json, typeReference);
            }
        } catch (IOException e) {
            log.warn("Error while convert String to Object with type reference json {}, message {}, error", json, e.getMessage(), e);
        }
        return null;
    }

    public static <K, V> String convertMapToJsonString(Map<K, V> maps) {
        try {
            return objectMapper.writeValueAsString(maps);
        } catch (JsonProcessingException e) {
            log.warn("Error converting List<Map<K, V>> to JSON. Data: {}, Error: {}", maps, e.getMessage(), e);
            return null;
        }
    }

    public static <K, V> Map<K, V> convertToMap(Object dto) {
        try {
            return objectMapper.convertValue(dto, new TypeReference<Map<K, V>>() {});
        } catch (IllegalArgumentException e) {
            log.warn("Error while converting <K, V> Map<K, V> object to map. Object: {}, Error: {}", dto, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public static <T, C extends Collection<T>> C stringToCollection(
            String jsonString,
            Class<C> collectionClass,
            Class<T> elementClass) {

        try {
            if (StringUtils.isNotBlank(jsonString)) {
                JavaType javaType = objectMapper
                        .getTypeFactory()
                        .constructCollectionType(collectionClass, elementClass);
                return objectMapper.readValue(jsonString, javaType);
            }
        } catch (IOException e) {
            log.warn("Error while converting JSON to Collection<{}> - Error: {}", elementClass.getSimpleName(), e.getMessage(), e);
        }
        return null;
    }

}
