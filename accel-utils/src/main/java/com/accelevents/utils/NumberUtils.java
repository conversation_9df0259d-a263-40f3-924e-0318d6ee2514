package com.accelevents.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static java.util.stream.Collectors.toList;

public class NumberUtils {

    public static boolean isNumberGreaterThanZero(Number number){
		return number != null && number.longValue() > 0;
    }

    public static boolean isCommaSeparatedNumbers(String numbers) {
        if (StringUtils.isBlank(numbers)) {
            return true; // empty is considered valid
        }

        return Arrays.stream(numbers.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .allMatch(org.apache.commons.lang3.math.NumberUtils::isDigits); // only digits allowed
    }

    public static boolean isValidPositiveNumber(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        String trimmed = value.trim();

        // must contain only digits
        if (!org.apache.commons.lang3.math.NumberUtils.isDigits(trimmed)) {
            return false;
        }

        try {
            long number = Long.parseLong(trimmed);
            return number > 0; // reject 0 and negatives
        } catch (NumberFormatException e) {
            return false; // in case it's too large for Long
        }
    }
}
