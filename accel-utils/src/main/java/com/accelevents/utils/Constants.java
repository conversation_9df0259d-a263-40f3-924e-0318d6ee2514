package com.accelevents.utils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

public interface Constants {

    String WEB = "WEB";
    String MOBILE = "MOBILE";
    String SINGLE_EVENT_APP_CHARGE = "Single Event App Charge";
    String UNLIMITED_MOBILE_APP = "unlimited app";
    String WHITE_LABEL = "whitelabel";
    String WHITE_LABEL_ADMIN_STRING = "whitelabeladmin";
    String EVENT_COORDINATOR_STRING = "eventcoordinator";
    String EVENT_BUDGET_OWNER_STRING = "eventbudgetowner";
    String EVENT_PLANNER_STRING = "eventplanner";
    String ACCOUNT_ADMIN_NAME = "Account Admin";
    String ATTENDEE_DAYS = "Attendee Days";
    String BILLING_DAYS = "Billing Days";
    String FINAL_ATTENDEE_DAYS = "Final Attendee Days";
    String FREE_CREDITS = "Free Credits";
    String PAID_CREDITS = "Paid Credits";
    String CHARGEBEE_PLAN = "Chargebee Plan";
    String AMOUNT_CAMELCASE = "Amount";
    String SUBSCRIPTION_ID = "Subscription Id";
    String CUSTOMER_ID_CAMELCASE = "Customer Id";
    String REGISTRATIONS = "registrations";
    String CHECK_INS = "check-ins";
    String EVENT_START_DATE = "eventStartDate";
    String ENTERPRISE = "enterprise";
    String ENTERPRISE_URL = "enterpriseUrl";
    String ORGANIZER_URL = "organizerUrl";
    String ORGANIZER_SMALL = "organizer";
    String EVENT_CREATE_DATE = "eventCreateDate";
    String EVENT_END_DATE = "eventEndDate";

    String STAFF_SMALL = "staff";
    String SPEAKER_SMALL = "speaker";
    String STAFF_BADGE_COLOR = "#40A8E8";
    String SPEAKER_BADGE_COLOR = "#7840E8";

    String DEFAULT_VALUE = "dafultvalue";//Frontend sends this value in JSON string
    String DEFAULT_VALUES = "dafultvalues";//Frontend sends this value in JSON string
    String DEFAULT_VALUE_WITHOUT_SPACE = "defaultvalue";
    String DEFAULT_VALUES_WITHOUT_SPACE = "defaultvalues";
    String LABEL = "label";
    String CAPACITY_LOWER = "capacity";
    String TWILIO_NUMBER = "twilioNumber";
    String CONST_STATE = "&state=";
    String PAYMENT_GATEWAY = "{PAYMENT_GATEWAY}";
    String EVENTPLUS = "EVENT+";
    String UPCOMING_MEETING = "Upcoming Meeting";
    String MEETING_RESCHEDULING = "Meeting Rescheduling";

    String FORM_STATUS_DRAFT = "DRAFT";
    String FORM_STATUS_PUBLISHED = "PUBLISHED";
    String SESSION_MESSAGE_JSON = "{\"startTime\":\"{START_TIME}\",\"endTime\":\"{END_TIME}\", \"sessionFormat\":\"{SESSION_FORMAT}\"}";
    String SESSION = "Session";
    String REVIEWER = "Reviewer";
    String UPCOMING_SESSION = "Upcoming Session";
    String SESSION_RESCHEDULING = "Session Rescheduling";

    String RECORDINGS = "Recordings";
    String ENV_PROD = "prod";
    String ENV_DEV = "dev";
    String ENV_STAGE = "stage";
    String ENV_LOCAL = "local";
    String EVENT = "event";
    String OneMinus300x50 = "1-300x50/";
    String Authorization = "Authorization";
    String Key = "Key";
    String BEARER = "Bearer ";
    String SOURCE = "SOURCE";
    String MOBILE_APP = "MOBILE_APP";
    String EMAIL_TEMPLATE_IMAGES = "email_template_images/";

    String NONE = "NONE";
    String LIMIT_KEY = "limit";

    String SEND_CONTACT_MAIL = "Thank you for writing to us, we will get back to you soon!";

    String SESSION_BREAKOUT_ROOM_DELETED = "Session Breakout Room deleted.";
    String BREAKOUT_ROOM = "Breakout Room ";

    String EXPAND_SESSION_SPEAKER_COUNT = "sessionSpeakerCount";

    String STREAM_PROVIDER = "Stream Provider";
    String ACCELEVENTS_STUDIO_LINK = "Accelevents Studio Link";
    String ACCEL_STREAMING_KEY_RTMP = "Accelevents Streaming Key (RTMP ONLY)";
    String RTMP_URL = "RTMP URL (RTMP ONLY)";
    String LIVE_STRAMING_PAGE_URL_RTMP = "Live Streaming Page URL (RTMP ONLY)";
    String ZOOM_MEETING_ID = "Zoom Meeting ID";
    String ZOOM_PASSWORD = "Zoom Password";
    String YOUTUBE_LINK = "YouTube Link";
    String VIMEO_LINK = "Vimeo Link";
    String WISTIA_LINK = "Wistia Link";
    String FACEBOOK_LIVE_LINK = "Facebook Live Link";
    String VIDYARD_LINK = "Vidyard Link";
    String ACCELEVENTS_STUDIO = "Accelevents Studio";
    String ACCELEVENTS_RTMP = "Accelevents RTMP";
    String PORTAL_LINK = "/portal/studio";
    String LIVE_STREAMING_MAIN_STAGE_LINK = "/portal/stage";
    String LIVE_STREAMING_BREAKOUT_LINK = "/portal/schedule";
    String REVIEWER_PORTAL_LINK = "/review/portal";
    String EN_US = "en-US";
    String TAG = "Tag";
    String TAGS = "Tags";
    String TRACKS = "Tracks";
    String LOCATION_ID = "Location Id";
    String PRIMARY_SPEAKERS = "Primary speaker";
    String SECONDARY_SPEAKERS = "Secondary speaker";
    String SPONSORING_COMPANY = "Sponsoring Company";
    String DEVICE_CHECKER_ADDED = "Device Checker Added";
    String DEVICE_CHECKED_WITH_DEVICE_ID = "Performed device check on ${DATE}";
    String BIO = "Bio";
    String ABOUT_ME = "About Me";
    String FACEBOOK = "Facebook";
    String TWITTER = "Twitter";
    String LINKED_IN = "LinkedIn";
    String INSTAGRAM = "Instagram";
    String INDUSTRY = "Industry";
    String WANTS_TO_MEET = "Wants To Meet";
    String WANTS_TO_LEARN = "Wants To Learn";
    String KNOWS = "Knows";
    String SESSION_CSV_ID = "ID";
    String TITLE = "Title";
    String FORMAT = "Format";
    String SESSION_TYPE = "Session Type";
    String START_DATE_TIME = "Start Date&Time";
    String LOCATION = "Location";
    String LOCATION_SMALL = "location";
    String END_DATE_TIME = "End Date&Time";
    String FULL_DETAIL = "Full Detail";
    String CAPACITY = "Capacity";
    String NAME = "Name";
    String NAME_SMALL = "name";
    String VALUE_SMALL = "value";
    String FEATURE = "Feature";
    String FEATURE_SMALL = "feature";
    String TWITTER_HANDLE = "Twitter Handle";
    String INSTAGRAM_HANDLE = "Instagram Handle";
    String LINKEDIN_URL = "LinkedIn URL";
    String SPEAKER_SPACE_ID = "Speaker Id";
    String OVERRIDE_PROFILE_DETAILS = "Override Profile Details";
    String SPEAKER_TICKET = "Speaker Ticket";
    String AVATAR_URL = "avatarUrl";
    String PLAN = "Plan";
    String SESSION_CSV_START_DATE = "Start Date";
    String SESSION_CSV_START_TIME = "Start Time";
    String SESSION_CSV_END_TIME = "End Time";
    String SHORT_DESCRIPTION = "Short Description";
    int MAXIMUM_FILE_SIZE_UP_TO_2_MB = 2097152;
    int MAXIMUM_FILE_SIZE_UP_TO_5_MB = 5242880;
    int MAXIMUM_FILE_SIZE_UP_TO_10_MB = 10485760;
    int MAXIMUM_FILE_SIZE_UP_TO_20_MB = 20971520;
    int MAXIMUM_FILE_SIZE_UP_TO_200_MB = 209715200;
    int TWO_INT = 2;
    int THREE_INT = 3;
    int FIVE_INT = 5;
    int HUNDRED_INT = 100;
    int TEN_INT = 10;
    int TWENTY_INT = 20;
    int TWO_HUNDRED_INT = 200;
    String FILE_SIZE_IS_EXCEED_THE_LIMIT = "Please upload file less than ${sizeLimit} MB of size.";
    String UPLOAD_FILE_HEADER_MISSING_FOR_ATTENDEE_CSV = "CSV is missing required column: ${columnName}";
    String NUMBER_OF_TICKET = "Number of tickets must be greater than zero for Bundle type ${ticketBundleType}";
    String ITEM_NAME = "Item Name";
    String ITEM_CODE = "Item Code";
    String ITEM_CATEGORY = "Item Category";
    String ITEM_DESCRIPTION = "Item Description";
    String BID_INCREMENT_AMOUNT = "Bid Increment";
    String STARTING_BID_AMOUNT = "Starting Bid";
    String CURRENT_BID_AMOUNT = "Current Bid";
    String BUY_IT_NOW_PRICE = "Buy it Now price";
    String ITEM_MARKET_VALUE = "Item Market Value";
    String ITEM_SHORT_NAME = "Item Short Name";
    String MINIMUM_PLEDGE = "Minimum Pledge";
    String BUY_TICKET_BUTTON_TEXT = "Register Now";
    String REGISTER = "Register";
    String TYPE = "Type";
    String NUMBER_OF_BIDDERS_WHO_CAN_WIN = "Number of bidders who can win";
    String SETDATE_AND_TIME = "Set Date &amp; Time";
    String ACTIVATE_AUCTION = "Activate Auction";
    String ACTIVATE_RAFFLE = "Activate Raffle";
    String ACTIVATE_PAYMENT_PROCESSING = "Activate Payment Processing";
    String CUSTOMIZE_EVENT_URL = "Customize Event URL";
    String ADD_EXHIBITOR = "Add exhibitor";
    String SETUP_REGISTRATION = "Setup registration";
    String SETUP_EVENT_AGENDA = "Setup event agenda";
    String ADD_EVENT_LOGO = "Add an event logo";
    String ADD_TEAM_MEMBERS = "Add team members";
    String ATOZ_BOTHCASE_AND_NUMBERS = "[^a-zA-Z0-9]";
    String ATOZ_BOTHCASE_AND_NUMBERS_AND_HYPHEN = "[^a-zA-Z0-9-\\s]";
    String ATOZ_BOTHCASE_AND_NUMBERS_AND_PLUS_AND_FORWARDSLASH_AND_EQUAL_NOT_REMOVE = "[^A-Za-z0-9+/=]";
    String ATOZ_LOWERCASE = "^[a-z]+$";
    String ATOZ_UPPERCASE = "^[A-Z]+$";
    String HYPHEN = "-";
    String MULTIPLE_HYPHEN = "-+";
    String MULTIPLE_HYPHEN_WITH_START = "^-+";
    String DOUBLE_HYPHEN = "--";
    String WHITE_SPACE = "\\s";
    String MULTIPLE_WHITE_SPACE = "\\s+";
    String EVENT_NAME = "eventName";
    String calenderInvitationDescription = "Click this link to see the event page: %s/e/%s";

    String GUEST_OF = "Guest of";
    String RELEASING = "    RELEASING...";
    String STRING_CHANNELS = "/channels/";
    String MEMBERS = "members";
    String EVENT_ID = "eventId";
    String DISABLE_CHAT = "disableChat";
    String CHANNEL = "channel";
    String DELETED = "deleted";
    String RECORD_NOT_FOUND = "Record not found";
    String MESSAGES = "messages";
    String API_KEY = "api_key";
    String FETCH = "fetch";
    String SCALE = "scale";
    String NOT_CONNECTED = "Not Connected";
    String CSV_UPLOAD_SUCCESS = "CSV uploaded successfully.";
    String TOTALING = " totaling ";
    String TO_YOUR_FINAL_BILL = " to your final bill.";
    String WE_CHARGED_YOUR_CARD_ENDING_IN = "We have charged your card ending in ";
    String FOR_AMOUNT_OF = " for the amount of $";
    String PLEASE_SUBMIT_LINK_TO_STRIPE = " Please submit payment here via the link to Stripe: ";
    String REQUIRE_INVOICE = " If you require an invoice, please use the information included in this email as the itemized invoice.";
    String END_LIST = "</li>";
    String DAILY = "Daily";
    String WEEKLY = "Weekly";
    String MONTHLY = "Monthly";
    String CUSTOM = "Custom";
    String EVENTURL = "EVENT_URL";
    String DELETE_CONVERSATION_CHANNELS = "delete_conversation_channels";
    String MARK_MESSAGES_DELETED = "mark_messages_deleted";
    String HARD_DELETED = "hard_delete";
    String INTEREST = "Interest";

    String BLOCK_USER = "User block by Admin.  ${userEmails}";

    String AUCTIONCUSTOMSMS = "auctionCustomSms";
    String RAFFLECUSTOMSMS = "raffleCustomSms";
    String CAUSEAUCTIONCUSTOMSMS = "causeAuctionCustomSms";

    String PREFIX = "Prefix";
    String PREFIX_DEFAULT_JSON = "{\"dafultvalues\":{\"dafultvalue\":[{\"label\":\"Mr.\",\"value\":\"Mr.\"},{\"label\":\"Ms.\",\"value\":\"Ms.\"},{\"label\":\"Mrs.\",\"value\":\"Mrs.\"}]}}";
    String GENDER = "Gender";
    String GENDER_DEFAULT_JSON = "{\"dafultvalues\":{\"dafultvalue\":[{\"label\":\"Male\",\"value\":\"Male\"},{\"label\":\"Female\",\"value\":\"Female\"},{\"label\":\"Prefer not to answer\",\"value\":\"Prefer not to answer\"}]}}";
    String GENDER_DEFAULT_JSON_UPDATED = "{\"defaultvalues\":{\"defaultvalue\":[{\"label\":\"Male\",\"value\":\"Male\"},{\"label\":\"Female\",\"value\":\"Female\"},{\"label\":\"Prefer not to answer\",\"value\":\"Prefer not to answer\"}]}}";
    String BIRTHDAY = "Birthday";
    String AGE = "Age";
    String ORGANIZATION = "Organization";
    String CONTACT_SPACE_ID = "Contact Id";
    String FIRST_NAME = "First Name";
    String LAST_NAME = "Last Name";
    String FULL_NAME = "Full Name";
    String EMAIL = "Email";
    String PROFILE_IMAGE_URL = "Profile_Image_URL";
    String EMAIL_ADDRESS = "Email Address";
    String PRONOUNS = "Pronouns";
    String JOB_TITLE = "Job Title";
    String IMAGE = "Image";
    String UPLOAD = "Upload";
    String COUNTRY_CODE_TXT = "Country Code";
    String PHONE_NUMBER = "Phone Number";
    String CELL_PHONE = "Cell Phone";
    String ERROR_MESSAGE = "Error Message";
    String ERROR_MESSAGE_SMALL = "errorMessage";
    String ADD_SEPARATOR = "|";
    String PHONE = "Phone";

    String PRIMARY_SESSIONS = "Primary Sessions";
    String SECONDARY_SESSIONS = "Secondary Sessions";
    String SESSIONS_IDS = "Sessions Ids";
    String CREDITS_EARNED = "Credits Earned";
    String CRITERIA_NAME = "Criteria Name";
    String SESSION_NAME_CSV = "Session Name";
    String CRITERIA_TYPE = "Criteria Type";
    String TRACKS_SLASH_SUBTRACKS = "Tracks/Subtracks";
    String ANONYMOUS_USER = "Anonymous";
    String TICKET_TYPE = "Ticket Type";
    String ASSIGNED_SEQUENCE = "Assigned Sequence";
    String ASSIGNED_NUMBER = "Assigned Number";
    String EVENT_TICKET_ID = "Event Ticket Id";
    String NOTE = "Note";
    String RATING = "Rating";
    String COMPANY = "Company";
    String Title_WITH_BIG_CASE = "Title";
    String COUNTRY = "Country";
    String STATE = "State";
    String VIDEO_INTRO = "Video Intro";
    String ACCEPT_MEETING_REQUEST = "Accept Meeting Requests";
    String ACCEPT_DIRECT_MESSAGE = "Accept Direct Messages";
    String CREATED_ON = "Created On";
    String ROLE = "Role";
    String WORKSHOP = "Workshop";
    String MEETING = "Meeting";
    String DEVICE_CHECKER = "Device_checker";
    String EXHIBITOR = "Exhibitor";
    String NETWORKING = "Networking";
    String ORGANIZER = "Organizer";
    String BREAKOUT_ROOM_MEETING = "Breakout_room_meeting";
    String ATTENDEE = "Attendee";
    String OWNER = "Owner";
    String UNASSIGNED = "Unassigned";
    String NETWORKING_LOUNGE = "Networking-lounge";
    String COUNTRY_CODE_KEY = "countryCode";
    String PHONE_NUMBER_KEY = "phoneNumber";
    String CAP_FIRST_UNDERSCORE_NAME = "FIRST_NAME";
    String CAP_LAST_UNDERSCORE_NAME = "LAST_NAME";
    String CAP_EMAIL = "EMAIL";
    String CAP_CELL_UNDERSCORE_PHONE = "CELL_PHONE";
    String CAP_CELL_UNDERSCORE_COUNTRY = "CELL_COUNTRY";
    String CELL_PHONE_COUNTRY = "Cell Phone Country";
    String CAP_EVENT_STATUS = "EVENT_STATUS";
    String CAP_CREATED_AT = "CREATED_AT";
    String CAP_REQUEST_FORM = "REQUEST_FORM";
    String CAP_NOTE = "NOTE";


    String INTEREST_NAME = "Interest";

    String ACCOUNT_IS_LOCKED_PLEASE_TRY_AFTER_SOMETIME = "Your account has been locked to protect your security. Please <a href='/u/password-reset'> reset your password</a> or wait and try again.";
    String MAX_LIMIT_REACHED = "Your account has been locked to protect your security. Please <a href='/u/password-reset'> reset your password</a> or try again after 30 minutes.";
    String STAFF = "Staff";

    String CONTENT_TYPE = "Content-Type";
    String APPLICATION_JSON = "application/json";
    String CONTENT_CSV = "text/csv";
    String APPLICATION_OCTET_STREAM = "application/octet-stream";
    String HTTP_HEADER_ACCEPT = "Accept";
    String HTTP_METHOD_GET = "GET";
    String CONTENT_VTT = "text/vtt";
    String CONTENT_PNG = "image/png";

    String ACCELEVENTS = "Accelevents";
    String AN_ACCELEVENTS = "an Accelevents";
    String ACCELEVENTS_FOOTER = "AccelEventsFooter";
    String DONOTREPLY = "donotreply";
    String INFO_ACCELEVENTS_EMAIL = "<EMAIL>";
    String SESSION_REMINDER_EMAIL_DEV = "<EMAIL>";
    String MANUAL_TRANSACTION_TYPE = "MANUAL";
    String STRIPE_TRANSACTION_TYPE = "Stripe";
    String HOST_SETTINGS_ACCOUNT = "/host/settings/account";
    String MESSAGE_FORMAT = "[a-zA-Z]{3}.*";

    String TICKETING_STATUS_BOOKED = "Booked";
    String TICKETING_STATUS_REFUNDED = "Refunded";

    String TICKETING_STATUS_CANCELED = "Canceled";
    String TICKETING_STATUS_CHECKED_IN = "Checked In";
    String STATUS_TO_DISPLAY_PARTIALLY_REFUNDED = "Partially Refunded";

    String TRANSACTIONAL_EMAIL_POOL = "Transactional";
    String ACCOUNT_EMAIL_POOL = "Account";
    String EVENTS_ACCOUNT_EMAIL_POOL = "Event_Account";
    String ENGAGE_EMAIL_POOL = "Engage";
    String ACCOUNT_ACCOUNT_PREFIX = "account@account.";
    String EVENT_ACCOUNT_PREFIX = "event@account.";
    String EVENT_ORDER_PREFIX = "events@orders.";
    String EVENT_ENGAGE_PREFIX = "events@engage.";

    String LIST_UNSUBSCRIBE_POST =  "List-Unsubscribe-Post";
    String LIST_UNSUBSCRIBE = "List-Unsubscribe";
    String LIST_UNSUBSCRIBE_ONE_CLICK = "List-Unsubscribe=One-Click";

    String YES = "Yes";
    String NO = "No";

    String DISPLAY = "DISPLAY";
    String PORTAL = "PORTAL";

    String REFUNDED = "REFUNDED";
    String CANCELED  = "CANCELED";
    String PARTIALLY_REFUNDED = "PARTIALLY REFUNDED";
    String FAILED = "FAILED";

    int TICKETING_DEFAULT_TIMEOUT = 10;
    int SESSION_IFRAME_HEIGHT = 150;

    String UTF8 = "UTF-8";
    String UTF_16 = "UTF-16";
    String FIRST_NAME_COMMA_PLACE_HOLDER = "[first_name],";
    String EXCEPTION_OCCURED = "Exception Occurred : ";

    // EXCEPTION
    String USER_NOT_FOUND = "User not found";
    String ITEM_NOT_FOUND = "Item not found";

    String UNCATEGORIZED = "Uncategorized";
    String FAVORITES = "Favorites";
    String SUCCESS = "Success";
    String ACCEPTED = "Accepted";
    String STATUS_FAILED = "Failed";
    String FAIL = "Fail";
    String SANDBOX = "SANDBOX";
    String DEFAULT_EVENT_TAGE_LINE = "You can click here to add a tagline for your event!";
    String EVENT_DEFAULT_DESC = "<div style=\"text-align:center; font-weight: bold; font-family: arial; font-size: 30px;\"><span style=\"color: rgb(0, 0, 0);\">Hello!</span></div><p style=\"text-align:center; font-family: arial; font-size: 18px;\"><span style=\"color: rgb(0, 0, 0);\">This is a free-text area where you can add your event's description. Feel free to play around with the settings to make sure the formatting fits your event's branding!</span></p><div style=\"text-align:center; font-family: arial; font-size: 18px;\"><span style=\"color: rgb(0, 0, 0);\">It is possible to add images, embed videos, iframes and</span> <a href=\"https://www.linkedin.com/company/accelevents/posts/?feedView=all\">hyperlinks</a>.</div><div data-empty=\"true\" style=\"text-align: center;\"><br></div><div style=\"text-align: center;\"><img src=\"https://res.cloudinary.com/aestage/image/fetch/c_fill_pad,dpr_2.0,f_auto,fl_lossy,g_auto,h_350,q_100,w_700/https://d1jq2jmhdt3fy.cloudfront.net/default_ae_images/Smooth_AE_Icon_700x350.png\" class=\"fr-fic fr-dii\" style=\"width: 322px;\"></div>";
    String WL_EVENT_DEFAULT_DESC = "<div style=\"text-align:center; font-weight: bold; font-family: arial; font-size: 30px;\"><span style=\"color: rgb(0, 0, 0);\">Hello!</span></div><p style=\"text-align:center; font-family: arial; font-size: 18px;\"><span style=\"color: rgb(0, 0, 0);\">This is a free-text area where you can add your event's description. Feel free to play around with the settings to make sure the formatting fits your event's branding!</span></p><div style=\"text-align:center; font-family: arial; font-size: 18px;\"><span style=\"color: rgb(0, 0, 0);\">It is possible to add images, embed videos, iframes and</span> <a href=\"https://www.linkedin.com/company/accelevents/posts/?feedView=all\">hyperlinks</a>.</div>";
    String SPONSORS_DEFAULT_SECT = "<b><p style=\"text-align:center\">Add custom text here or click the < > button to add your own custom HTML code!</p></b>";

    // EXCEPTION MESSAGES
    String NOT_AUTHORIZE = "Not authorize to access";

    String NOT_AUTHORIZE_WITH_INVALID_EVENT = "Not authorize to access with invalid event";
    String NOT_AUTHORIZE_TO_ACCESS_WITH_MAGIC_LINK = "Not authorize to access with event level magic link.";
    String MESSAGE_FOR_BLOCKKEY_USER = "This key has been blocked";
    String DATE = "date";
    String ONLY_DATE_FORMAT = "dd/MM/yyyy";
    String ONLY_TIME_FORMAT = "HH:mm";
    String DATE_FORMAT_DD_MM_YYYY_HH_MM = "dd/MM/yyyy HH:mm";
    String DATE_FORMAT = "dd/MM/yyyy HH:mm:ss";
    String DATE_FORMAT_MONTH = "MM/dd/yyyy HH:mm:ss";
    String DATE_FORMAT_ONLY_MONTH = "MM/dd/yyyy";
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy_MM_dd_HH_mm_ss";
    String DATE_FORMAT_ONLY_DAY = "dd";
    String DATE__TIME_FORMAT_MONTH = "MM/dd/yyyy HH:mm";//NOSONAR
    String GET_STREAM_DATE_TIME_FORMAT = "yyy-MM-dd'T'HH:mm:ss";
    String DATE_SIMPLE_FORMAT = "EEE MMM dd HH:mm:ss Z yyyy";
    String ONLY_DATE_SIMPLE_FORMAT = "yyyy/MM/dd";
    String NOT_VALID_MAGIC_LINK = "Not A Valid Magic Link.";
    String NOT_VALID_LINK = "Not A Valid Link.";
    String DATE_TIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    String DAY_DATE_TIME_FORMAT_TIMESTAMP = "EEE MMM dd HH:mm:ss zzz yyyy";
    String DATE_TIME_FORMAT_FULL_STANDARD_UTC = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    String UTC_TIMEZONE = "UTC";
    String VISIT = "VISIT";

    String ES_QUERY_START_EVENT = "ES query start event {} ";
    String ES_QUERY_START_EVENT_TIME_TAKEN = "ES query end event {} time taken {}";
    String ES_RESULT_PROCESS_EVENT_TIME_TAKEN_FOR_EXPORT = "ES result process end event {} time taken for export {}";

    String PLAIN = "plain";
    String STRING_EMAIL = "email";

    String UPN = "upn";
    String STRING_CELL = "cell";
    String STRING_FIRST_NAME = "firstName";
    String STRING_LAST_NAME = "lastName";
    String STRING_NAME = "name";
    String STRING_ITEM_NAME = "itemName";
    String eventNameAndUrl = "whiteLabelEvent";

    String STRING_AUCTION = "auction";
    String AVAILABLETICKETS = "availabletickets";
    String CURRENTBID = "currentBid";
    String STARTINGBID = "startingBid";
    String BUYITNOW = "Buyitnow";
    String TOTALTICKETSSUBMITTED = "totalticketssubmitted";
    String MINPRICE = "minprice";

    String STRING_FUNDANEED = "fundANeed";
    String STRING_RAFFLE = "raffle";
    String STRING_TICKETING = "ticketing";
    String STRING_DONATE = "donate";
    String AUCTION_TAB_TITLE = "Auction";
    String FUNDANEED_TAB_TITLE = "Fund a Need";
    String RAFFLE_TAB_TITLE = "Raffle";
    String TICKETING_TAB_TITLE = "The Event";
    String DONATION_TAB_TITLE = "Donate";
    String EVENT_URL = "eventURL";
    String EVENT_UNDERSCORE_URL = "event_url";
    String BILLING_TYPE = "billingType";

    String STRING_ITEM_HAS_BEEN_PURCHASED_FOR_MSG = "	This item has been purchased for %s%s.";
    String STRING_CURRENT_BID_FOR_THIS_ITEM_MSG = "The current bid for this item is %s%s, bids may be submitted in %s%s amounts.";
    String STRING_STARTING_BID_FOR_THIS_ITEM_MSG = "The starting bid for this item is %s%s, bids may be submitted in %s%s amounts.";
    String NUMBER_OF_TICKTES_HAS_BEEN_SUBMITTED_MSG = "%s tickets have been submitted for this item.";
    String MIN_BID_FOR_ITEM_MSG = "The minimum bid for this item is %s%s.";

    String EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS = "Please enter a number greater than or equal to the total number of sold tickets: ${sold_tickets}.";
    String EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS = "Please enter a number greater than or equal to the 100.";
    String EVENT_CAPACITY_IS_REACHED = "You can not buy tickets more then event capacity limit";
    String SOLD_TICKETS = "${sold_tickets}";
    String NUMBER_OF_TICKETS_PURCHASED = "Number of tickets purchased";

    String TICKETS_ADDED = " Tickets Added to Your Account.";

    String ONLINE = "Online";
    String CC = "CC";
    String CASH = "CASH";
    String CARD = "CARD";
    String COMPLIMENTARY = "COMPLIMENTARY";
    String PARTIALLY_PAID = "PARTIALLY PAID";
    String EXTERNAL_TRANSACTION = "EXTERNAL_TRANSACTION";
    String UNPAID = "UNPAID";
    String PAID = "PAID";
    String VOID = "VOID";
    String PAYMENT_REQUESTED = "PAYMENT_REQUESTED";
    String CHARGE_FAILED = "CHARGE FAILED";
    String COMPLETE = "COMPLETE";
    String CREATE = "CREATE";
    String NOT_APPLICABLE = "N/A";
    String BID_SUCCESS_NOTIFY = "Bid Submitted Successfully, You will be notified if you are outbid.";
    String THANK_YOU_FOR_YOUR_PURCHASE = "Thank you for your purchase!";
    String THANK_YOU_FOR_PURCHASING = "Thank you for purchasing ";
    String STRING_SMS_URL = "/sms/";
    String CREATED = "CREATED";
    String NBSP_STRING = "&nbsp;";
    String STRING_COLON = ":";
    String STRING_EMPTY = "";
    String STRING_BLANK = " ";
    String STRING_DASH = "-";
    String STRING_PLUS = "+";
    String STRING_UNDERSCORE = "_";
    String STRING_COMMA = ",";
    String STRING_COMMA_WITH_SPACE = ", ";
    String STRING_DOT = ".";
    String STRING_AT = "@";
    String STRING_EQUAL = "=";
    String PIPE = "|";
    String X = "x";
    String STRING_NULL = "null";
    String STRING_DOTTED = "...";
    String STRING_SLASH = "/";
    String EMPTY_JSON_BODY = "{}";
    String STRING_PERCENTAGE = "%";
    String STRING_DOUBLE_BACKSLASH = "\\";
    String DEFAULT_START_TIME = "08:00";
    String DEFAULT_END_TIME = "20:00";
    String STRING_9DOT88="9.88";
    String STRING_512="512";
    int ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN=10;
    String STRING_LIMIT="limit";
    String OFFSET = "offset";
    String STRING_AUTO="auto";
    String STRING_PERCENT_20="%20";
    String STRING_SPACE_AT_SPACE = " at ";
    String STRING_SPACE_TO_SPACE = " to ";
    String DEFAULT_SLOT = "[{\"intervals\":[{\"from\":\"08:00\",\"to\":\"20:00\"}],\"type\":\"wday\",\"wday\":\"sunday\",\"date\":\"\"},{\"intervals\":[{\"from\":\"08:00\",\"to\":\"20:00\"}],\"type\":\"wday\",\"wday\":\"monday\",\"date\":\"\"},{\"intervals\":[{\"from\":\"08:00\",\"to\":\"20:00\"}],\"type\":\"wday\",\"wday\":\"tuesday\",\"date\":\"\"},{\"intervals\":[{\"from\":\"08:00\",\"to\":\"20:00\"}],\"type\":\"wday\",\"wday\":\"wednesday\",\"date\":\"\"},{\"intervals\":[{\"from\":\"08:00\",\"to\":\"20:00\"}],\"type\":\"wday\",\"wday\":\"thursday\",\"date\":\"\"},{\"intervals\":[{\"from\":\"08:00\",\"to\":\"20:00\"}],\"type\":\"wday\",\"wday\":\"friday\",\"date\":\"\"},{\"intervals\":[{\"from\":\"08:00\",\"to\":\"20:00\"}],\"type\":\"wday\",\"wday\":\"saturday\",\"date\":\"\"}]";

    String HOSTING_YOUR_OWN_EVENT_LABEL = "Hosting Your Own Event?";
    String END_MESSAGE_LABEL = "Join thousands of organizations using {SENDER}'s to manage their events.";
    String GET_STARTED_LABEL = "Get Started for FREE!";
    String END_FIRM_NAME_MESSAGE_LABEL = "Join hundreds of people who are using {firmName}' ticketing solutions to improve their event experience and increase proceeds.";

    String ITEM_PURCHASE = " Item Purchase.";

    String PLEDGE_SUBMIT_MSG = "Pledge Submitted Successfully.";
    String STAFF_CASH_PLEDGE = "Thank you for your pledge!";
    String CHARGE_DESCRIPTION = "desciption for charges";
    String EVENT_BILLING_DES_SUCCESS = "EVENT_BILLING_PAID_SUCCESS";
    String PAYMENT_REQUEST_SENT = "PAYMENT_REQUEST_SENT";
    String EVENT_BILLING_DES_CHARGE_FAILED = "EVENT_BILLING_CHARGE_FAILED";
    String EVENT_BILLING_MARKED_VOID = "EVENT_BILLING_MARKED_VOID";
    String CSV_UPLOAD_CHARGE_SUCCESS = "CSV_UPLOAD_CHARGE_SUCCESS";
    String CSV_UPLOAD_CHARGE_FAILED = "CSV_UPLOAD_CHARGE_FAILED";
    String DONATION_CHARGE_FAILED = "DONATION_CHARGE_FAILED";
    String AUCTION_CHARGE_FAILED = "AUCTION_CHARGE_FAILED";
    String RAFFLE_CHARGE_FAILED = "RAFFLE_CHARGE_FAILED";
    String CAUSEAUCTION_CHARGE_FAILED = "CAUSEAUCTION_CHARGE_FAILED";
    String TICKET_PURCHASE_FAILED = "TICKET_PURCHASE_FAILED";
    String DATA_FIX_VIRTUAL_EVENT_ACTIVATE = ",DATA FIX:Virtual event activate";
    String THANKS_FOR_EVENT = "Thank you for choosing Accelevents!";

    String CREATED_EVENT = "Created Event Details";
    String PUBLISHED_EVENT = "Published Event Details";
    String DUPLICATED_EVENT = "Duplicated Event Details";

    String TICKETS_SUBMITTED_SUCCESSFULLY = "Tickets Submitted Successfully.";
    String SUCCESSFULLY_UPDATED_ORDER_TICKETS_STATUS_CHECK_IN = "Successfully updated order tickets status to Check In.";

    String ITEM_CODE_ALREADY_EXISTS = " Code Already exist.";
    String ITEM_CODE_REQUIRED = "Item Code is required";
    String INVALID_ITEM_CODE = "Invalid Item Code";
    String PASSWORD_NOT_MATCH = "Password doesn't match";
    String PASSWORD_RESET_SENT = "Password reset link sent successfully, Please check your email";

    String TWO_FACTOR_CODE_SENT = "Two factor code sent successfully, Please check your email";
    String MAGIC_LINK_SENT = "Magic link sent successfully, Please check your email";
    String COUPON_APPLIED_SUCCESS_MSG = "Coupon applied successfully";
    String COUPON_APPLIED_BUT_NOT_WITH_DONATION_TICKET_TYPE = "Coupon applied successfully. Note that Coupon will not be applied to donation tickets.";
    String COUPON_DELETE_SUCCESS_MSG = "Delete coupon successfully";
    String ORDER_NOTE_UPDATED_SUCCESSFULLY = "Order note has been updated successfully.";
    String ORDER_NOTE_ADDED_SUCCESSFULLY = "Order note has been added successfully.";
    String NUMBER_OF_TICKETS_UPDATED_SUCCESSFULLY = "Number of ticket count updated successfully.";
    String USER_LOG_OUT = "User log out";
    String STARTING_BID_PRICE_NUMBER_OR_NOT_NULL = "Starting bid price is either number or not a null.";
    String STARTING_BID_AT_LEAST_ONE = "Starting bid needs to be set to at least 1";

    String THANKS_BID_SMS = "Thank you for bidding! You will be notified if you are outbid.";
    String THANKS_BID_MSG = "Thank you For Confirming Bid.";
    String THANKS_FOR_PURCHASE_MSG = "Thank you for your purchase.";
    String THANKS_FOR_PLEDGE_MSG = "Thank you for your pledge!";
    String THANKS_FOR_RAFFLE_MSG = "Thank you For Purchasing %s Tickets";
    String PERMISSION_UPDATED_MSG = "Permission updated Successfully";
    String STAFF_ADDED_MSG = "Staff added to event";
    String STAFF_DELETED_MSG = "successfully removed";
    String STAFF_SEND_INVITE_MSG = "Your invitation has been resent.";
    String EVENT_INVOICE_READY = "Your AccelEvents Invoice is Ready!";

    String ADD_ONS_ADDED = "Add-Ons added successfully.";
    String ADD_ONS_UPDATED = "Add-Ons updated successfully.";
    String ADD_ONS_DELETED = "Add-Ons updated deleted.";

    String TYPE_LOWER_CASE = "type";
    String PARENT = "parent";
    String PRODUCT = "product";
    String ACTIVE = "active";

    String JOIN_THE_EVENT_LABEL = "Join The Event";
    String FUTURE_EVENT_LABEL = "Visit Event Site";
    String VIEW_EVENT_DETAILS_LABEL = "View Event Details";

    String BILLING_CONTACT_UPDATED_SUCCESSFULLY = "Billing contacts updated successfully";
    String TEAM_MEMBER_REMOVE_FROM_BILLING_CONTACT = "Team member removed from billing contacts";
    String DELETED_TEAM_MEMBER = "Team member deleted successfully";
    String OWNER_UPDATED_SUCCESSFULLY = "Owner updated successfully";
    String CAN_NOT_REMOVE_LAST_BILLING_CONTACT = "Cannot remove last Billing contact";
    String CAN_NOT_UPDATE_OWNER_OF_THE_ORGANIZER = "You are not authorized to change owner";
    String INVITATION_SENT = "Invitation sent";
    String CSV_UPLOAD_SUCCESS_MESSAGE = "CSV uploaded successfully, We will notify you soon by email.";
    String TICKET_TYPE_DELETE_SUCCESS_MSG = "Ticket type deleted successfully.";
    String EVENT_TICKET_DELETE_SUCCESS_MSG = "Ticket deleted successfully.";
    String RECEIVE_AMOUNT_FOR_UNPAID_TICKETS = "Successfully received amount";
    String SETTINGS_SAVED = "Settings saved.";
    String CALENDAR_INVITE_UPDATED = "Calendar invite updated successfully";
    String TICKET_EMAIL_SENT = "Test email sent successfully";
    String REFUND_SUCCESS_MSG = "Refund complete.";
    String EMAIL_SEND_SUCCESS_MSG = "Email has been successfully sent.";
    String EMAIL_SEND_STARTED_MSG = "Email delivery started.";
    String EMAIL_SEND_IN_PROGRESS_MSG = "Email delivery is in progress.";
    String AUTO_ASSIGNED_ATTENDEE_NUMBER_SAVED_MSG = "Sequence saved successfully.";
    String AUTO_ASSIGNED_ATTENDEE_NUMBER_UPDATE_MSG = "Sequence updated successfully.";
    String AUTO_ASSIGNED_ATTENDEE_NUMBER_DELETE_SUCCESS_MSG = "Sequence deleted successfully.";

    String DELETED_USER_ACCOUNT = "Your user account has been successfully deleted";

    String HOST_GENERAL_SETTINGS_SAVED = "Host general settings saved successfully";

    String SUCCESSFULLY_MARK_FAVOURITE = "Item marked as favourite successfully";
    String SUCCESSFULLY_CREATED_NEW_EVENT = "Successfully created new event";
    String SUCCESSFULLY_UPDATED_MOST_RECENT_EVENT = "Successfully updated most recent user event";
    String SUCCESSFULLY_UPDATED_USER_FIELD = "Successfully updated user field";
    String SUCCESSFULLY_UPDATED_USER = "Successfully updated user";
    String ITEM_SAVED = "Item Saved.";
    String VIRTUAL_EVENT_SETTINGS_CREATED = "Virtual events settings created successfully.";
    String VIRTUAL_EVENT_SETTINGS_UPDATED = "Virtual events settings updated successfully.";
    String VIRTUAL_EVENT_CUSTOM_LABEL_UPDATED = "Virtual events custom label updated successfully.";
    String EVENT_LANDING_PAGE_CUSTOM_LABEL_UPDATED = "Event landing page custom label updated successfully.";
    String EXHIBITOR_FIELDS_TABLIST_UPDATED = "Exhibitor TabList updated successfully.";
    String SUCCESSFULLY_SAVED_WANTS_TO_LEARN = "Wants to learn saved successfully.";
    String SUCCESSFULLY_DELETED_WANTS_TO_LEARN = "Wants to learn deleted successfully.";

    String CONTACT_SAVED = "Contact Saved.";
    String CONTACT_DELETED = "Contact Deleted.";
    String CONTACTS_DELETED = "Contacts Deleted.";
    String ITEM_DELETED_SUCCESSFULLY = "Item Deleted Successfully.";
    String FAVORITE_ITEM_DELETED = "Item removed from favorite item list Successfully.";
    String EVENT_DELETED_SUCCESSFULLY = "Event is deleted successfully";
    String EVENTS_DELETED_SUCCESSFULLY = "Events are deleted successfully";
    String ORGANIZER_DELETED_SUCCESSFULLY = "Organizer is deleted successfully";
    String USER_DELETED_SUCCESSFULLY = "User is deleted successfully";
    String WAIT_LIST_DELETED_SUCCESSFULLY = "Item Deleted Successfully.";
    String CATEGORY_ADDED_SUCCESSFULLY = "Category Added Successfully.";
    String CATEGORY_UPDATED_SUCCESSFULLY = "Category Updated Successfully.";
    String CATEGORY_DELETED_SUCCESSFULLY = "Category Deleted Successfully.";
    String TICKET_PRICE_ADDED_SUCCESSFULLY = "Ticket Price Added Successfully.";
    String SUCCESSFULLY_UPDATED_HOST_SETTINGS = "Successfully updated host settings";
    String SUCCESSFULLY_RESET_AUCTION = "Successfully reset auction";
    String SUCCESSFULLY_DELETED_RAFFLE_TICKET_PKG = "Successfully deleted raffle ticket package";
    String SUCCESSFULLY_UPLOADED_CONTACTS = "Successfully uploaded contacts";
    String NEW_ORGANIZER_WILL_BE_CREATED = "New Organizer Will be created";
    String ORGANIZER_CREATED = "Organizer created successfully.";
    String EVENT_WILL_BE_JOINED_WITH_ORGANIZER = "Event will be joined with %s";
    String WAIT_LIST_SAVED = "You will be notified if more space become available.";
    String WAIT_LIST_REORDERED = "Waitlist order has been updated successfully.";
    String REGISTRATION_REQUEST_RECEIVED = "Registration request received";
    String PRODUCT_ADDED_SUCCESSFULLY = "Product Added Successfully.";
    String PRODUCT_UPDATED_SUCCESSFULLY = "Product Updated Successfully.";
    String PRODUCT_DELETED_SUCCESSFULLY = "Product Deleted Successfully.";
    String EXHIBITOR_PRODUCT_POSITION_CHANGED_SUCCESSFULLY = "Exhibitor product position changed successfully ...";
    String WELCOME_MESSAGE_VIDEO_SUCCESSFULLY_DELETED = "Welcome Message Video Successfully Deleted.";

    String HOST_CREDIT_CARD_SETTINGS_SAVED = "Host credit card settings saved successfully";
    String EVENT_BILLING_ID_SAVED = "Event billing id saved successfully.";
    String STRING_UNKNOWN = "Unknown";
    String STRING_ACTIVE_UPPERCASE = "ACTIVE";
    String STRING_ACTIVE = "active";
    String STRING_SOLD = "SOLD";
    String STRING_PAST = "past";

    String LEAD_SAVED = "Lead saved.";
    String LEAD_DELETED = "Lead deleted.";
    String LEAD_UPDATED_MSG = "Lead updated for event";

    String REQUESTED_A_DEMO = "REQUESTED_A_DEMO";
    String AUTO_GENERATED_LEAD = "AUTO_GENERATED_LEAD";
    String LEAD_ALREADY_GENERATED = "Lead already generated.";
    String DOCUMENT = "DOCUMENT";

    // Lead CSV Headers
    String MANUALLY_CAMELCASE = "Manually";
    String DOCUMENT_CAMELCASE = "Document";
    String VISIT_CAMELCASE = "Visit";

    String NETWORKING_LOUNGE_DELETED = "Networking lounge deleted.";
    String NETWORKING_LOUNGE_UPDATED = "Networking lounge updated.";
    String NETWORKING_LOUNGE_JOINED_MSG = "Networking lounge joined.";
    String NETWORKING_LOUNGE_PHOTO_DELETED = "Networking lounge photo deleted.";
    String NETWORKING_LOUNGE_VIDEO_DELETED = "Networking lounge video deleted.";
    String NETWORKING_LOUNGE_VIDEO_UPDATED = "Networking lounge video updated.";
    String NETWORKING_LOUNGE_LEAVE_MSG = "Networking lounge leave successfully.";
    String NETWORKING_LOUNGE_POSITION_CHANGED_SUCCESSFULLY = "Networking lounge position changed successfully ...";

    String INTEREST_TAG_DELETED = "Interest tag deleted";

    String ATTENDEE_DELETED = "Attendee deleted";
    String ATTENDEE_CONNECTED_MSG = "Attendee Connected";
    String ATTENDEE_ALREADY_CONNECTED_OR_REJECTED = "This connection request is not in pending status and hence can not be cancelled";
    String ATTENDEE_CONNECT_REQUEST_CANCELED = "Connection request cancelled";
    String ATTENDEE_CONNECTION_REMOVED = "Connection removed";
    String ATTENDEE_HAS_NOT_CONNECTED = "You are not connected with this attendee.";
    String NOTIFICATION_PREFERENCE_SAVED = "Notification Preference saved.";
    String NOTIFICATION_PREFERENCE_UPDATED = "Notification Preference Updated.";
    String NOTIFICATION_PREFERENCE_DELETED = "Notification Preference Deleted Successfully.";

    String SESSION_DELETED = "Session deleted.";
    String SESSION_DUPLICATED = "Session Duplicated Successfully.";
    String SESSION_HIDDEN = "Session Hidden Successfully.";
    String REGISTRATION_DATE = "Registration Date";
    String WAITLISTED_DATE = "Waitlisted Date";
    String SESSION_CHECK_IN_DATE = "Session CheckIn Date";
    String SESSION_TIME_ERROR = "Session start time and end time should be before event time";

    //offline events contants
    String SESSION_CHECKIN = "SESSION_CHECKIN";
    String SESSION_CHECKOUT = "SESSION_CHECKOUT";
    String EVENT_CHECKIN = "EVENT_CHECKIN";
    String EVENT_CHECKOUT = "EVENT_CHECKOUT";
    String LEAD_CAPTURE = "LEAD_CAPTURE";

    String SESSION_SPEAKER_POSITION_CHANGED_SUCCESSFULLY = "SessionSpeaker position changed successfully ...";
    String SESSION_SPEAKER_ONBOARDING_PROCESS_COMPLETE_SUCCESSFULLY = "SessionSpeaker on-boarding process complete successfully";

    String SPEAKER_UPDATED = "Speaker updated.";
    String SPEAKER_PROFILE_REMOVED = "Speaker profile picture removed.";
    String SPEAKER_POSITION_CHANGED_SUCCESSFULLY = "Speaker position changed successfully ...";
    String SESSION_POSITION_CHANGED_SUCCESSFULLY = "Session position changed successfully ...";
    String SESSIONS_SORTED_SUCCESSFULLY = "Sessions sorted successfully ...";

    String EXHIBITOR_STAFF_SAVED = "Exhibitor staff details saved.";
    String EXHIBITOR_STAFF_DELETED = "Exhibitor staff details deleted.";
    String EXHIBITOR_ADMIN_UPDATED_MSG = "Exhibitor Admin updated for event.";
    String EXHIBITOR_STAFF_UPDATED_MSG = "Exhibitor Staff updated for event.";

    String EXHIBITOR_HOST_DELETED = "Exhibitor host details deleted.";
    String EXHIBITOR_HOST_UPDATED_MSG = "Exhibitor host updated for event.";
    String EXHIBITOR_POSITION_CHANGED_SUCCESSFULLY = "Exhibitor position changed successfully ...";
    String EXHIBITOR_DOCUMENT_POSITION_CHANGED_SUCCESSFULLY = "Exhibitor Document position changed successfully ...";
    String EXHIBITOR_DOCUMENT_DELETED_SUCCESSFULLY = "Exhibitor Document deleted successfully ...";
    String EXHIBITOR_NAME_ALREADY_USED = "Exhibitor name {} already in use.";
    String STAFF_EMAIL_ALREADY_USED = "Staff email already in used.";

    String INFO_DESK_DETAILS_UPDATED_SUCCESSFULLY = "Info-desk details updated successfully.";
    String INFO_DESK_DETAILS_DELETED_SUCCESSFULLY = "Info-desk details deleted successfully.";

    String SPONSOR_DELETED = "Sponsor details deleted.";
    String SPONSOR_UPDATED_MSG = "Sponsor updated for event.";
    String SPONSOR_POSITION_CHANGED_SUCCESSFULLY = "Sponsor position changed successfully ...";
    String SPONSOR_ID = "Sponsor id";
    String SPONSOR_NAME_ALREADY_USED = "Sponsor name {} already in use.";

    String SPONSOR_CARD_SIZE = "Sponsor Card Size";
    String SPONSOR_SHORT_DESCRIPTION = "Short Description";
    String SPONSOR_URL = "Sponsor URL";

    String POSITION_CHANGED_SUCCESSFULLY = "Position changed successfully ...";

    String EXHIBITOR_ = "Exhibitor_";
    String EVENT_ = "Event_";
    String SESSION_ = "Session_";
    String BACKSTAGE_ = "Backstage_";
    String LOUNGE_ = "NL_";
    String STAFF_ = "Staff_";
    String MESSAGING = "messaging";
    String LIVESTREAM = "livestream";
    String DES_MESSAGING = "des_messaging";
    String ADMIN_DES_ = "Admin_DES_";

    String ADMIN_SLASH_DES = "Admin/DES";
    Long GET_STREAM_CREATE_USER_BATCH_SIZE = 75L;
    Long GET_STREAM_CREATE_CHANNEL_BATCH_SIZE = 100L;
    Long PROD_GET_STREAM_DELETE_CHANNEL_BATCH_SIZE = 260L;
    Long DEV_STAGE_GET_STREAM_DELETE_CHANNEL_BATCH_SIZE = 50L;
    Long GET_STREAM_TRUNCATE_CHANNEL_BATCH_SIZE = 30L;
    Long GET_STREAM_FEED_DELETE_BATCH_SIZE = 90L;
    Long GET_STREAM_FEED_ADDTOMANY_BATCH_SIZE = 100L;
    Long BUYER_TRANSFER_LIMIT = 100L;
    String USER_DEFAULT_PHOTO = "images/user-icon-placeholder.png";
    String EXHIBITOR_BOOTH_ACCESS = " Exhibitor Booth Access for ";
    String ACCESS_GRANTED = "You've been granted access to an ";
    String ACCOUNT = " account ";

    String BID_DELETE_SUCCESS_MSG = "Bid was successfully deleted.";
    String PLEDGE_DELETE_SUCCESS_MSG = "Pledge get deleted successfully.";

    String ACCOUNT_DISCONNECT_SUCCESS_MSG = "Account Disconnected successfully";
    String TICKETING_DISCOUNT_CODE_SAVE_SUCCESS_MSG = "Discount Coupon Saved.";
    String TICKETING_DISCOUNT_CODE_UPDATE_SUCCESS_MSG = "Coupon updated successfully";
    String TICKETING_DISCOUNT_CODE_DELETE_SUCCESS_MSG = "Coupon deleted successfully";

    String TICKETING_LIMITED_DISPLAY_CODE_SAVE_SUCCESS_MSG = "Limited display code Saved.";
    String TICKETING_LIMITED_DISPLAY_DISCOUNT_CODE_UPDATE_SUCCESS_MSG = "Limited display code updated successfully";
    String TICKETING_LIMITED_DISPLAY_DISCOUNT_CODE_DELETE_SUCCESS_MSG = "Limited display code deleted successfully";

    String TICKETING_HOLDER_UPDATE_SUCCESS_MSG = "Ticket Holder Details Updated Successfully.";
    String MANDATORY_PURCHASER_FIELDS = "{purchaser_info} is not present in ticket holder json.";
    String NUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED = "The number of tickets per {bundle_type} cannot be changed after an order is filled.";

    String ATTRIBUTE_IS_REQUIRED = "{attribute} is required.";
    String ATTRIBUTE_IS_HIDDEN = "{attribute} is hidden.";

    String ATTRIBUTE = "{attribute}";
    String HOLDER_VALUE = "Holder value";
    String DIV = "</div>";
    String SESSION_SLOTS_COLLIDES = "Main stage sessions cannot overlap. It looks like you have a conflict with {SESSION_NAME}";
    String UTC_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    String UTC_DATE_FORMAT_MILLIS = "yyyy-MM-dd'T'HH:mm:ss.ss'Z'";
    String DATE_FORMAT_WITH_AM_PM = "yyyy/MM/dd hh:mm a";
    String DATE_FORMAT_YYYYMMDD_T_HHMMSS_Z = "yyyyMMdd'T'HHmmss'Z'";
    String DATE_FORMAT_YYYYMMDD_T_HHMMSS_X = "yyyyMMdd'T'HHmmssX";
    String DATE_FORMAT_YYYYMMDD_T_HHMMSS = "yyyyMMdd'T'HHmmss";
    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_S = "yyyy-MM-dd HH:mm:ss.S";
    String DELETE = "DELETE";
    String DD_MMM_YYYY="dd MMM yyyy";
    String HH_MM_A="hh:mm a ";
    String yyyy_MM_dd_HH_mm_ss = "yyyy/MM/dd HH:mm:ss";
    String HH_MM_SS="HH:mm:ss";
    String YYYY_MM_DD="yyyy/MM/dd";
    String LOCAL_DATE_FORMAT = "yyyy/MM/dd HH:mm";
    String TIME_FORMAT_WITH_ZONE_OFFSET = "HH:mm xxxxx";
    String AUTOMATIC = "AUTOMATIC";
    String STRING_FIRST_SPACE_NAME = "First Name";
    String STRING_LAST_SPACE_NAME = "Last Name";
    String STRING_EMAIL_SPACE = "EMAIL";
    String STRING_PHONE_NUMBER = "phoneNumber";
    String STRING_COUNTRY_CODE = "countryCode";
    String STRING_COUNTRY_CODE_NUMBER = "countryCodeNumber";
    String STRING_COUNTRY = "country";
    String STRING_CELL_SPACE_PHONE = "Cell Phone";
    String STRING_PASSWORD = "password";
    String BILLING_ADDRESS = "Billing Address";
    String SHIPPING_ADDRESS = "Shipping Address";
    String STRING_ADDRESS1 = "address1";
    String STRING_ADDRESS2 = "address2";
    String STRING_STATE = "state";
    String STRING_CITY_OR_PROVIDENCE = "cityOrProvidence";
    String STRING_ZIPCODE = "zipcode";
    String EVENT_ID_UPPERCASE = "EVENT_ID";
    String BASE_URL = "BASE_URL";
    String STAFF_EMAIL_LIST = "STAFF_EMAIL_LIST";
    String DISCLAIMER = "Disclaimer";
    String WL_DISCLAIMER = "Wl_Disclaimer";
    String ORGANIZER_ID = "organizer_id";

    int STATUS_CODE_OK = 200;

    String SURVEY_NAME = "Survey Name";
    String SURVEY_TITLE = "Survey Title";
    String DESCRIPTION = "Description";
    String SURVEY_HEADLINE = "Survey Headline";
    String SURVEY_FEEDBACK_MESSAGE = "Survey Feedback Message";
    String SURVEY_QUESTIONS = "Survey Questions";
    String SUBMISSIONS = "Submissions";
    String QUESTION_TITLE = "Question Title";
    String IS_REQUIRED = "Is Required";
    String POSITION = "Position";
    String ALLOWED_DESCRIPTION = "Allowed Description";
    String OPTIONS = "Options";
    String TRIGGER = "Trigger";
    String SUBMISSION_TIMESTAMP = "Submission Time";

    String SALESFORCE_AUTH2_TOKEN ="https://login.salesforce.com/services/oauth2/token";
    String SALESFORCE_TOKEN_API="/services/oauth2/token";
    String SALESFORCE_USER_DESCRIBE_API = "/services/data/v52.0/sobjects/User/describe";
    String SALESFORCE_FETCH_USER_API = "services/data/v60.0/query?q=";
    String SALESFORCE_ATTRIBUTE = "attributes";
    String SALESFORCE_USER_INFO_URL = "https://login.salesforce.com/services/oauth2/userinfo";
    String PING_TOKEN_API = "/as/token";
    String PING_FETCH_USER_API = "/as/userinfo";
    String PING_AUTH_API = "https://auth.pingone.com/";
    String PING_ENV_API = "https://api.pingone.com/v1/environments/";
    String PING_APPLICATIONS= "/applications/";
    String AUTHORIZATION_ENDPOINT= "authorizationEndpoint";
    String IEEE_AUTHORIZATION_ENDPOINT= "https://services10qa.ieee.org/as/authorization.oauth2";
    String USER_INFO_ENDPOINT = "userinfoEndpoint";
    String IEEE_USER_INFO_ENDPOINT = "https://services10qa.ieee.org/idp/userinfo.openid";
    String TOKEN_ENDPOINT = "tokenEndpoint";
    String IEEE_TOKEN_ENDPOINT = "https://services10qa.ieee.org/as/token.oauth2";
    String FACEBOOK_PROFILE_INFO_URL = "https://graph.facebook.com/v19.0/me?fields=id,name,last_name,first_name,email&access_token=";
    String GOOGLE_PROFILE_INFO_URL = "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token=";
    String LINKEDIN_AUTH2_URL = "https://www.linkedin.com/oauth/v2/accessToken";
    String APPLE_AUTH_URL = "https://appleid.apple.com/auth/token";
    String BEE_FREE_AUTH_URL = "https://auth.getbee.io/loginV2";
    String BEE_FREE_API_PREFIX = "https://api.getbee.io/v1/";
    String LINKEDIN_PROFILE_INFO_URL = "https://api.linkedin.com/v2/me";
    String LINKEDIN_EMAIL_INFO_URL = "https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))";

    String EMAIL_SVG_INSTAGRAM = "/icon_grey_instagram.svg";
    String EMAIL_SVG_FACEBOOK = "/icon_grey_facebook.svg";
    String EMAIL_SVG_TWITTER = "/icon_grey_twitter.svg";
    String EMAIL_SVG_LINKEDIN = "/icon_grey_linkedin.svg";
    String EMAIL_SVG_HAND_WAVE = "/icon_hand_wave.png";
    String EMAIL_SENDING_INITIATED = "Email sending initiated";

    String BIDDER_REGISTERED_MESSAGE = "You have successfully registered for the event!";
    String NEW_BIDDER_NUMBER_MESSAGE = "You have successfully registered for the event!. Your participant number is %d";
    String UPDATE_BIDDER_NUMBER_MESSAGE = "Bidder number is %d updated successfully";
    String BIDDER_NOT_REGISTERED = "Bidder is not registerd.";
    String BIDDER_CREDIT_CARD_NOT_FOUND = "Credit card not found";

    String ACTIVATED = "Activated";
    String ENABLED = "Enabled";
    String DISABLED = "Disabled";

    String CONNECTED_AND_ACTIVATED = "Connected and Activated";
    String PENDING_ACTIVATION = "Pending Activation";
    String SIGNUP_TEXT = "By signing up, I agree to Accelevent&#39;s <a href=\"https://www.accelevents.com/terms-conditions/\" target=\"_blank\">terms of service</a> and "
            + "<a href=\"https://www.accelevents.com/Privacy-Policy/\" target=\"_blank\">privacy policy</a>.";
    String NUMBER_OF_TICKETS_ARE_LESS_THEN_SELECTED_TICKETS = "Please select number of tickets equal or more than ${numberOfTickets} for Ticket type: ${ticketTypeName}.";
    String NUMBER_OF_TICKETS_ARE_MORE_THEN_SELECTED_TICKETS = "Please select number of tickets equal or less than ${numberOfTickets} for Ticket type: ${ticketTypeName}.";
    String SET_START_END_DATE_BEETWEEN_SOLD_RECURRING_DATE = "Attendees have already registered for a session ${position} ${new_occurs_date_time}. " +
            "In order to changing the Occurs ${untilFrom} date a custom event date will be created for the dates that have registrations. " +
            " Would you like to create these custom sessions and change the Occurs ${untilFrom} date?";
    String EMAIL_IS_NOT_REGISTERED_FOR_EVENT = "${email_address} is not registered for ${event_name}.";
    String ATTENDEE_IMPORT_LIMIT_LESS_THAN_CURRENT="Please add the attendee import limit more then imported attendee ${importedAttendee}";
    String USER__NOT_PRESENT_FOR_EMAIL = "An account does not exist with this ${email} address.";
    String MAGIC_LINK_EXPIRED_FOR_USERNAME = "The magic link for ${username} has been expired.";
    String UNSUBSCRIBE_CONTACT_TEXT = "<p style=\"text-align:center\">To be removed from communications regarding this event, please <a href=[unsubscribe_url]>click here</a> to unsubscribe</p>";
    String UNSUBSCRIBE_EMAIL_NOTIFICATION = "<p>To unsubscribe, go to your email preferences page or <a href=[unsubscribe_url]>click here.</a></p>";
    String CONFIRM_EMAIL_ADDRESS = "<p>To confirm your email address please <a href=[confirm_url]>click here.</a></p>";
    String DEFAULT_TICKETING_EMAIL_HEADER_TEXT = "Thank you for buying tickets for the event ${event_name}! Please find your purchase summary below.";
    String DEFAULT_TICKETING_EMAIL_HEADER_TEXT_NOT_ALLOW_DOWNLOAD_PDF = "Thank you for buying tickets for the event ${event_name}! Please find your purchase summary below.";
    String ORDER_TRANSFER_CONFIRMATION = " Your order has successfully transfer to ${recurring_event_date}";
    String ORDER_TRANSFER_SUCCESSFULLY = "Order #${order_id} has successfully been transferred from ${old_event_date} to ${new_event_date} and a confirmation email has been sent!";
    String UNPAID_ORDER = "<b> This tickets have not yet been paid.</b>";
    String YOU_NEED_TO_PAY = " You need to pay ";
    String THIS_TICKET_HAS_NOT_YET_BEEN_PAID = ", This ticket has not yet been paid.";
    String THIS_TICKET_HAS_BEEN_MARK_AS_PAID = ", This ticket has been mark as paid now.";
    String PAID_ORDER = "<b> This tickets has been PAID now.</b>";
    String DEFAULT_DONATION_EMAIL_HEADER_TEXT = "Thank you for your donation to ${event_name}! Below, please find your donation summary.";
    String ADMIN_TICKETING_EMAIL_HEADER_TEXT = "<table class=\"module\" role=\"module\" data-type=\"text\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"  width=\"100%\" style=\"table-layout: fixed;\" data-attributes='%7B%22dropped%22%3Atrue%2C%22child%22%3Afalse%2C%22padding%22%3A%220%2C0%2C0%2C0%22%2C%22containerbackground%22%3A%22%23ffffff%22%7D'>"
            + "<tr>"
            + "<td role=\"module-content\"  valign=\"top\" height=\"100%\" style=\"padding: 0px 0px 0px 0px;\" bgcolor=\"#ffffff\">"
            + "<div>"
            + "Good News! An order for ${event_name} just came through. Below you'll find a copy of the order confirmation email for: <br><b> ${first_Name} ${last_Name}</b>  <br> ${email_id} <br> Order #${order_id}"
            + "</div></td>" + "</tr>" + "</table>"
            + "<table class=\"module\" role=\"module\" data-type=\"spacer\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"table-layout: fixed;\" data-attributes='%7B%22dropped%22%3Atrue%2C%22spacing%22%3A30%2C%22containerbackground%22%3A%22%23ffffff%22%7D'>"
            + "<tr><td role=\"module-content\" style=\"padding: 0px 0px 30px 0px;\" bgcolor=\"#ffffff\"></td></tr></table>";
    String STRING_UNLIMITED = "UNLIMITED";
    String CONTENT_DISPOSITION = "Content-Disposition";
    String APPLICATION_PDF = "application/pdf";
    String APPLICATION_ZIP= "application/zip";
    String APPLICATION_VND_APPLE_PKPASS = "application/vnd.apple.pkpass";

    String TICKET_PASS_ZIP = "TicketPassZip";
    String SPEAKER_LIST = "Speaker List";
    String STRIPE = "STRIPE";
    String EXTERNAL = "EXTERNAL";
    String SUCCESS_SUBSCRIPTION_CREATED = "Subscription Created Successfully.";
    String PAYMENT_COLLECTED = "Payment Collected Successfully";
    String THANKS_FOR_PURCHASE_PLAN = "Thank you for your purchase of ${planType} plan . Your purchase is in process and you should be gaining access to the platform within an hour. Please let us know through chat if you are unable to access the platform in an hour.";
    String SUCCESSFULLY_ASSOCIATED_EVENT_WITH_PLAN = "Successfully associated event with plan.";
    String CHARGE_PURCHASED_SUCCESS = "You have purchased ${quantity} ${plan_name} event. You can associate this purchase with an active event on the Events tab.";
    String DES_CHARGE_PURCHASED_SUCCESS = "You have successfully purchase ${quantity} quantity of ${charge_name} charge.";
    String DOES_NOT_HAVE_ENOUGH_QUANTITY = "The organizer needs additional ${plan_name} credits. Please purchase more to proceed with publishing the event.";
    String QUANTITY_PLACE_HOLDER = "${quantity}";
    String CHARGE_NAME_PLACE_HOLDER = "${charge_name}";
    String PLAN_NAME_PLACE_HOLDER = "${plan_name}";
    String SUCCESS_SUBSCRIPTION_CANCELLED = "Subscription Cancelled Successfully.";
    String SEATING_CATEGORY_ALREADY_EXIST = "The seating category name ${category_name} already exists in the event URL ${eventURL}";
    String BREAKOUT_ROOM_ALREADY_EXIST = "BreakoutRoom Name ${session_breakout_room_name} is already exist in session";
    String VAR_BREAKOUT_ROOM_NAME = "${session_breakout_room_name}";
    String BREAKOUT_ROOM_UPDATED_SUCCESSFULLY = "BreakoutRoom updated successfully.";
    String BEEFREE_CUSTOM_TEMPLATE = "BEEFREE_CUSTOM_TEMPLATE";
    String BEEFREE_WAIT_LIST = "BEEFREE_WAIT_LIST";
    String ORGANIZER_TEAM_ACCESS = "ORGANIZER_TEAM_ACCESS";
    String MAGIC_LINK_CODE = "MAGIC_LINK_CODE";
    String SUPPORT_QUESTION = "SUPPORT_QUESTION";
    String REQUEST_NOTIFICATION = "REQUEST_NOTIFICATION";
    String ATTENDEE_CHECKIN = "ATTENDEE_CHECK_IN";
    String DEFAULT_JSON_VALUE_FOR_AUTO_SEARCH = "{\"fullName\":true,\"email\":false,\"ticketCode\":true,\"phoneNumber\":false}";
    String RESET_PASSWORD = "RESET_PASSWORD";
    String CART_ABANDONMENT = "CART_ABANDONMENT";
    String REMINDER = "REMINDER";
    String STAFF_ACCESS = "STAFF_ACCESS";
    String WHITE_LABEL_NEW_ADMIN = "WHITE_LABEL_NEW_ADMIN";
    String REGISTRATION_APPROVAL_EMAIL = "REGISTRATION_APPROVAL_EMAIL.json";
    String WAIT_LIST_MESSAGE = "This should be wait list message.";
    String ORDER_SUMMARY_BEGINNING_OF_THE_LIST = "order_summary_beginning_of_the_list";
    String ORDER_SUMMARY_ENDING_OF_THE_LIST = "order_summary_ending_of_the_list";
    String TICKET_DETAILS_BEGINNING_OF_THE_LIST = "ticket_details_beginning_of_the_list";
    String TICKET_DETAILS_ENDING_OF_THE_LIST = "ticket_details_ending_of_the_list";
    String PATH_OF_DEFAULT_BEEFREE_JSON = "ATTENDEE_ORDER_CONFIRMATION_EMAIL_V1.json";
    String PATH_OF_DEFAULT_BEEFREE_JSON_FOR_WHITELABEL = "ATTENDEE_ORDER_CONFIRMATION_EMAIL_V1.json";
    String PATH_OF_DEFAULT_REMINDER_JSON = "REMINDER_EMAIL.json";
    String PATH_OF_DEFAULT_ORDER_CONFIRMATION_PAGE_HTML="DEFAULT_ORDER_CONFIRMATION_PAGE.ftl";
    String PATH_OF_DEFAULT_ORDER_CONFIRMATION_PAGE_JSON="DEFAULT_ORDER_CONFIRMATION_PAGE.json";
    String PATH_OF_DEFAULT_ORDER_CONFIRMATION_EMAIL_HTML="ATTENDEE_ORDER_CONFIRMATION_EMAIL_V1.ftl";
    String PATH_OF_DEFAULT_ORDER_CONFIRMATION_EMAIL_JSON="ATTENDEE_ORDER_CONFIRMATION_EMAIL_V1.json";
    String PATH_OF_DEFAULT_REMINDER_EMAIL_HTML="REMINDER_EMAIL.ftl";
    String PATH_OF_DEFAULT_REMINDER_EMAIL_JSON="REMINDER_EMAIL.json";
    String PATH_OF_DEFAULT_SAVE_A_SEAT_EMAIL_HTML="SAVE_A_SEAT_CONFIRMATION_EMAIL.ftl";
    String PATH_OF_DEFAULT_SAVE_A_SEAT_EMAIL_JSON="SAVE_A_SEAT_CONFIRMATION_EMAIL.json";
    String PATH_OF_DEFAULT_EVENT_REQUEST_SUBMISSION_EMAIL_HTML="EVENT_REQUEST_STATUS_EMAIL.ftl";
    String PATH_OF_DEFAULT_EVENT_REQUEST_SUBMISSION_EMAIL_JSON="EVENT_REQUEST_STATUS_EMAIL.json";
    String PATH_OF_DEFAULT_TICKET_ORDER_PDF_HTML="DEFAULT_TICKET_ORDER_PDF.ftl";
    String PATH_OF_DEFAULT_TICKET_ORDER_PDF_JSON="DEFAULT_TICKET_ORDER_PDF.json";
    String PATH_OF_DEFAULT_INVOICE_ORDER_PDF_HTML="DEFAULT_INVOICE_ORDER_PDF.ftl";
    String PATH_OF_DEFAULT_INVOICE_ORDER_PDF_JSON="DEFAULT_INVOICE_ORDER_PDF.json";
    String DEFAULT_ORDER_CONFIRMATION_PAGE="Default Order Confirmation";
    String DEFAULT_ORDER_CONFIRMATION_EMAIL="Default Email Template";
    String DEFAULT_REMINDER_EMAIL="Default Reminder Email";
    String DEFAULT_SAVE_A_SEAT_EMAIL = "Default Save a Seat Email";
    String DEFAULT_TICKET_PDF_DESIGN ="Ticket Default Design";
    String DEFAULT_TICKET_INVOICE_DESIGN ="Invoice Default Design";
    String DEFAULT_SUBJECT_LINE_FOR_SAVE_A_SEAT_EMAIL = "Seat Saved for %s";
    String TEST_SAVE_A_SEAT_EMAIL_SPEAKER_NAMES = "John Doe, James Kean";
    String DEFAULT_BODY_TEXT_FOR_SAVE_A_SEAT_EMAIL = "Thank you for reserving your seat for ${session_name} at ${event_name}. We are delighted to confirm your participation in this session.";
    String DEFAULT_BODY_TEXT_FOR_TICKET_CANCEL_EMAIL = "<div id=\"isPasted\">This is to confirm that the following ticket purchase has been canceled <b>%s</b>.&nbsp;</div><div><br></div><div>Please find your purchase summary below.</div>";
    String DEFAULT_BODY_TEXT_FOR_ORDER_CANCEL_EMAIL = "<div id=\"isPasted\">This is to confirm that your <b>order #%d</b> has been canceled. All tickets associated with this order are no longer valid.&nbsp;</div><div><br></div><div>Please find your purchase summary below.</div>";
    String DEFAULT_SUBJECT_LINE_FOR_ORDER_CANCEL_EMAIL = "Your Purchased Order #%d for %s Has Been Canceled";
    String DEFAULT_SUBJECT_LINE_FOR_TICKET_CANCEL_EMAIL = "Your Purchased Ticket for %s Has Been Canceled";
    String TEMPLATES_SLASH = "templates/";
    String DOT_JSON = ".json";
    String JOIN_EVENT_OR_VIEW_ORDER_BUTTON = "joinEventOrViewOrderButton";
    String CUSTOM_TEXT = "customText";
    String OLD_TICKET_TYPE="oldTicketTypeName";
    String NEW_TICKET_TYPE="newTicketTypeName";
    String HOLDER_FULL_NAME="holderFullName";
    String VARIABLE_VIRTUAL_EVENT = "${isVirtualEvent}";
    String IS_VIEW_EVENT_BUTTON_ENABLE = "isViewEventButtonIsEnable";
    String IS_EVENT_DATE_HIDE = "isHideDates";
    String EVENT_CURRENCY="event_currency";
    String IS_FREE_TO_FREE_TICKET_TRANSFER="isFreeToFreeTicketTransfer";
    String IS_PAID_TO_FREE_TICKET_TRANSFER="isPaidToFreeTicketTransfer";
    String IS_FREE_TO_PAID_TICKET_TRANSFER="isFreeToPaidTicketTransfer";
    String IS_LOWER_TO_HIGHER_TICKET_TRANSFER="isLowerToHigherTicketTransfer";
    String IS_HIGHER_TO_LOWER_TICKET_TRANSFER="isHigherToLowerTicketTransfer";
    String IS_SAME_PRICE_TICKET_TRANSFER="isSamePriceTicketTransfer";
    String IS_COMPLEMENTARY_TICKET_TRANSFER="isComplementaryTicketTransfer";
    String IS_PAYMENT_DONE_AT_TICKET_TRANSFER = "isPaymentDoneAtTicketTransfer";
    String PAID_AMOUNTS="paid_amount";
    String UNPAID_AMOUNT="unpaid_amount";
    String IS_UNPAID_ORDER="isUnpaidOrder";
    String IS_PAID_ORDER="isPaidOrder";
    String REFUNDED_AMOUNTS="refunded_amount";
    String IS_REFUND_AMOUNT_AVAILABLE="is_refund_amount_available";
    String QR_CODES_IMAGES="qr_codes_images";
    String SMALL_CASE_TITLE = "title";
    String SMALL_CASE_COMPANY = "company";
    String EXPO_STAFF = "LEAD RETRIEVER";
    String ALL_SPEAKER_EMAILS = "All Speaker Emails";
    String ALL_EXPO_STAFF_DETAILS = "AllExpoAndStaffDetails";
    String VIEW_ON_MAP = "viewOnMap";
    String GOOGLE_MAP_URL = "http://maps.google.com/?q=";
    String TOKEN_LOWER = "token";
    String ORDER_NUMBER = "order_number";
    String ORDER_NUMBER_WITHOUT_LINK = "order_number_without_link";
    String DONATION_AMOUNT = "donation_amount";
    String TOTAL_DISCOUNT = "total_discount";
    String Y_STRING = "Y";
    String MODULE_ACTIVATION_CHARGES_IN_CHARGEBEE = "Module Activation charges are collected from chargebee";
    String CHARGEBEE_PLAN_UNSUBSCRIBED_WITH_END_DATE = "Your cancellation request has been accepted. You will still be able to use the service until {SUBSCRIPTION_END_DATE}.";
    String CHARGEBEE_PLAN_REACTIVATED_WITH_RENEWAL_DATE = "Renewing your subscription will take effect at {SUBSCRIPTION_START_DATE}.";
    String CHAREGEBEE_CANCELED_STATUS = "canceled";
    String CHARGEBEE_NON_RENEWED_STATUS = "NON_RENEWING";
    String STRING_ADD_CHARGE_FOR_PENDING_ATTENDEE_UPLOAD="Add Charge For Pending Attendee Upload";
    String STRING_ADD_CHARGE_FOR_UN_PAID_ATTENDEE_UPLOAD="Add Charge For UnPaid Attendee Upload";
    String STRING_SEND_FUND_RAISER_USAGES_TO_CHARGE_BEE="Send Fund Raiser Usages To ChargeBee";
    String STRING_SEND_EVENT_TICKET_FEES_TO_CHARGE_BEE="Send Event Ticket Fees To ChargeBee";
    String STRING_UPDATE_CHARGE_BEE_BILLING_SETTING="Update ChargeBee Billing Setting";
    String FREE_CREDITS_CHARGE_ID_FOR_DEV_OR_STAGE="Free-Credits-Used";
    String PAID_CREDITS_CHARGE_ID_FOR_DEV_OR_STAGE="2023-Registration-Usage-USD";
    String FREE_CREDITS_CHARGE_ID_FOR_PROD="Free-Credits-Used-2023-USD";
    String PAID_CREDITS_CHARGE_ID_FOR_PROD="Registration-Usage-2023-USD";
    String CONTENT = "content";
    String BODY = "body";
    String RAW_BODY = "rawBody";
    String RAW_BODY_CONTENT = "rawBodyContent";
    String PUSH_NOTIFICATION_TOKEN_SAVED = "Push Notification Token has been saved.";
    String PUSH_NOTIFICATION_TOKEN_REMOVED = "Push Notification Token has been removed.";
    String FAILED_TO_REMOVE_PUSH_NOTIFICATION_TOKEN = "Failed to remove Push Notification Token.";
    String NOTIFICATION_ICON = "notificationIcon";
    String MEETING_NOTIFICATION = "MEETING_NOTIFICATION";
    String BUYER_RECEIPT_AUCTION = "BUYER_RECEIPT_AUCTION";
    String EVENT_TICKET_REFUND = "EVENT_TICKET_REFUND";
    String DATA = "data";
    String SIGNING_KEY = "kid";
    String PLAYBACK_RESTRICTION_ID = "playback_restriction_id";

    String  PLAYBACK_ID ="playback_id";
    String PRIVATE_KEY = "private_key";
    String REFERRER = "referrer";
    String ALLOWED_DOMAINS = "allowed_domains";

    String IN_TAPP_DOMAIN = "intapp.com";
    String CONFIGURATION_ADDED_SUCCESSFULLY = "SSO Configuration added successfully";
    String CONFIGURATION_UPDATED_SUCCESSFULLY = "SSO Configuration updated successfully";
    String CONFIGURATION_DELETED_SUCCESSFULLY = "SSO Configuration deleted successfully";
    String SESSION_TIME_FORMAT = "E MMM dd HH:mm:ss z yyyy";
    String X_OPENTOK_AUTH = "X-OPENTOK-AUTH";
    String STREAM_MODE = "streamMode";
    String STREAM_NAME = "streamName";
    String HAS_AUDIO = "hasAudio";
    String HAS_VIDEO = "hasVideo";
    String STREAMS = "streams";
    String STREAM_ID = "streamId";
    String STARTED = "started";
    String UPLOADING = "Uploading";
    String WL_SSO_CONFIGURATION_ALREADY_EXIST = "White Label SSO configuration already exist for SSO Identity Provider Type {ssoType}.";
    String EXHIBITOR_EXPAND = "EXHIBITOR";
    String SPONSOR_EXPAND = "SPONSOR";
    String CREATE_COMMAND_CENTER_NOTIFICATION_INPUT = "createCommandCenterNotificationInput";
    String CREATE_COMMAND_CENTER_NOTIFICATION_NAME = "createCommandCenterNotification";
    String MODERATOR = "moderator";

    String IS_INVITED_ATTENDEE = "isInvitedAttendee";
    String DEFAULT = "default";
    String REQUIRED_COLUMNS_ARE_MISSING = "Required columns are missing (${column})";
    String REQUIRED_COLUMN_IS_MISSING = "Required column is missing (${column})";
    String RESTRICTED_SESSION_NOT_ALLOWED_TO_SAVE = "Restricted sessions not allowed to save in challenge, Sessions ($sessionList)";
    String SESSION_LIST_REPLACER = "$sessionList";
    String PENDING = "PENDING";
    String STRING_DEFAULT = "DEFAULT";
    String DENIED = "DENIED";
    String APPROVED = "APPROVED";
    String REGISTERED_CAPITAL = "REGISTERED";
    String SAVE_AS_DRAFT = "SAVE_AS_DRAFT";
    String WAITLISTED = "WAITLISTED";
    String PRELIMINARY_APPROVAL = "PRELIMINARY_APPROVAL";
    String REVIEW_COMPLETED = "REVIEW_COMPLETED";
    String NEED_INFO = "NEED_INFO";
    String REGISTRATIONS_APPROVAL_EMAIL = "REGISTRATION_APPROVAL_EMAIL";
    String REGISTRATIONS_DEFAULT_MESSAGE = "We're excited to invite you to join <strong>${eventname}</strong> as a Reviewer.";
    String REGISTRATIONS_PENDING_MESSAGE = "Your submission has been received, and we will be in touch as soon as there is an update. Thank You! <strong>${eventname}</strong>.";
    String REGISTRATIONS_DENIED_MESSAGE = "Thank you for your interest in <strong>${eventname}</strong>. Unfortunately we are unable to accept your submission for this event.";
    String REGISTRATIONS_APPROVED_MESSAGE = "Congratulations! Your ${requestType} proposal has been accepted. We look forward to see you at <strong>${eventname}</strong>. Further instructions will follow shortly.";
    String REGISTRATIONS_WAITLIST_MESSAGE = "Thank you for your interest in <strong>${eventname}</strong>. Your submission has been added to our waitlist and you will be contacted further if space becomes available.";
    String WELCOME_MESSAGE = "Please check in to print your badge.";
    String SUCCESSFUL_CHECK_IN_MESSAGE = "<strong> Your badge is being printed </strong> <br> Claim your badge from the printer on your right side.";
    String UNSUCCESSFUL_CHECK_IN_MESSAGE = "<strong> Failed,please try again </strong> <br> Double check your ticket or see a staff member for assistance.";
    String NO_ATTENDEE_FOUND_MESSAGE = "Oops! Registration not found. Please see a member of our team for assistance.";
    String UNPAID_TICKET_MESSAGE = "There is an outstanding balance for your ticket. Please see event staff.";
    String PLAN_QUANTITY_FINISH_MESSAGE="All purchased plans have been used with previous events. An additional plan purchase will be needed to publish an event with these features.";

    String EVENT_REQUEST_SUBMISSION_PENDING_MESSAGE_SUBJECT = "Status update regarding your event request: Pending";
    String EVENT_REQUEST_SUBMISSION_DENIED_MESSAGE_SUBJECT = "Status update regarding your event request: Denied";
    String EVENT_REQUEST_SUBMISSION_APPROVED_MESSAGE_SUBJECT = "Status update regarding your event request: Approved";
    String EVENT_REQUEST_SUBMISSION_WAITLISTED_MESSAGE_SUBJECT = "Status update regarding your event request: Waitlisted";
    String EVENT_REQUEST_SUBMISSION_NEED_INFO_MESSAGE_SUBJECT = "Status update regarding your event request: Need Info";

    String EVENT_REQUEST_SUBMISSION_PENDING_MESSAGE_BODY = "Your submission has been received, and we will be in touch as soon as there is an update. Thank You!";
    String EVENT_REQUEST_SUBMISSION_DENIED_MESSAGE_BODY = "Unfortunately, we are not able to accommodate your request at this time. For further information, please contact your event administration team.";
    String EVENT_REQUEST_SUBMISSION_APPROVED_MESSAGE_BODY = "Congratulations! Your request has been approved. Please check your email for more information from your event coordination team.";
    String EVENT_REQUEST_SUBMISSION_WAITLISTED_MESSAGE_BODY = "We are unable to accommodate your request at this time, but it has not been denied. Your event request has been added to our waitlist, and you will be contacted again when there is another update. For more information or assistance, please contact your event administration team.";
    String EVENT_REQUEST_SUBMISSION_NEED_INFO_MESSAGE_BODY = "Some additional information is needed regarding your event request. Please contact your event administration team for more information.";

    String ITEM_ID = "itemId";
    String BIDDER_AMOUNT = "biddingAmount";
    String HIGHEST_BIDDER_USERID = "highestBidderUserId";
    String WINNER_USER_ID = "winnerUserId";
    String SUBJECT = "Status update regarding your submission to ${eventname}";
    String PAY_LATER = "PAY_LATER";
    String INVOICE = "Invoice";
    String LANDSCAPE = "Landscape";
    String PORTRAIT = "Portrait";
    String STARTTIME = "startTime";
    String ENDTIME = "endTime";
    String BUSINESS_PLAN_2023_LOWERCASE = "2023_business_plan";
    String ESSENTIAL_PLAN_LOWERCASE="essential_plan";
    String WHITE_LABEL_2023_LOWERCASE = "2023-white-label-plan";
    String ENTERPRISE_2023_LOWERCASE = "enterprise-plan-2023";
    String ATTENDEE_ID = "attendeeId";
    String ATTENDEE_NAME = "attendeeName";
    String CERTIFICATE_DELETED_SUCCESSFULLY = "Certificate deleted successfully.";
    String COPY = "COPY";
    String TICKET_TRANSFER_NOT_POSSIBLE_TICKET_TYPE_NON_TRANSFERABLE = "Not allowed to transfer this ${ticketType} ticket because  ${ticketType} ticket is nonTransferable";
    String SAVE_A_SEAT = "Save a seat";
    String SEAT_SAVED = "Seat saved";
    String UNAVAILABLE = "Unavailable";
    String EVENT_VENUE = "VENUE" ;
    String EVENT_CLOSE_OUT_EMAIL_SUBJECT = "Reminder: Submit Your Close-out Report for ${eventName}";


    interface Zapier {
        String EVENT_BILLING_NOTE = "Zapier attendee upload charge";
        String CREATE = "CREATE";
        String UPDATE = "UPDATE";

        String externalOrderId = "externalOrderId";
        String FIRST_NAME_CAMEL_WITH_UNDERSCORE = "First_Name";
        String LAST_NAME_CAMEL_WITH_UNDERSCORE = "Last_Name";
        String EMAIL_CAMEL = "Email";
        String CELL_PHONE_CAMEL_WITH_UNDERSCORE = "Cell_Phone";
        String COUNTRY_CODE_CAMEL_WITH_UNDERSCORE = "Country_Code";
        String UNDERSCORE_2 = "_2";
        String UNDERSCORE_CITY_CAMEL = "_City";
        String UNDERSCORE_STATE_CAMEL = "_State";
        String UNDERSCORE_POSTAL_CODE_CAMEL = "_Postal_Code";
        String UNDERSCORE_COUNTRY_CAMEL = "_Country";
        String TITLE = "Title";
        String SESSION_FORMAT_TYPE = "Session_Format_Type";
        String SESSION_LOCATION = "Session_Location";
        String START_DATE_TIME = "Start_Date_Time";
        String END_DATE_TIME = "End_Date_Time";
        String DESCRIPTION = "Description";
        String CAPACITY = "Capacity";
        String SHORT_DESC = "Short_Description";
        String TAGS = "Tags";
        String TRACKS = "Tracks";
        String PRONOUNS = "Pronouns";
        String COMPANY = "Company";
        String BIO = "Bio";
        String LINKED_IN_URL = "Linked_In_URL";
        String INSTAGRAM_HANDLE = "Instagram_Handle";
        String TWITTER_HANDLE = "Twitter_Handle";
        String OVERRIDE_PROFILE = "Override_Profile";
        String SESSION_ID = "session_id";
        String IS_VISIBLE = "is_visible";
        String IMG_URL = "img_url";
        String TICKET_TYPE_NAMES = "ticket_type_names";
        String NO_TICKET_TYPES = "No Ticket Types";
        String SPEAKER_EMAILS = "speaker_emails";
        String TICKET_BUYER_OR_HOLDER = "ticket_buyer_or_holder";
    }

    String SESSIONS_SCHEDULED_OUTSIDE_EVENT_TIME_FAILURE="Some sessions are now scheduled to start prior to or end after your event. Please recheck your session date and time settings";
    String SESSIONS_SCHEDULED_OUTSIDE_EVENT_TIME_SUCCESS="All sessions are scheduled within your event time.";
    String CVENT_EVENT_BILLING_NOTE = "Cvent attendee upload charge";
    String TRAY_IO_CONNECTOR_EVENT_BILLING_NOTE = "Tray io connector attendee upload charge";
    String UNPAID_ATTENDEE_UPLOAD_CSV_NOTE = "This is the CSV uploaded With UnPaid Status";

    String SILENT = "Silent";
    String LIVE = "Live";
    String NOBIDS = "Nobids";
    String MINIMUM_BID_INCREMENT_FOR_THIS_ITEM = "The minimum bid increment for this item is {bidIncrement}. Please bid at least {minimumBid}.";
    String AFTER = "after";
    String UNTIL = "until";
    String BEFORE = "before";
    String FROM = "from";
    String TO = "to";
    String KEY_VALUE_ALREADY_EXIST = "{KEY} name already exist";
    String KEY_CAPITAL = "{KEY}";
    String REGISTER_MAX_LIMIT_REACHED = "Your registration type only permits registering for {number_of_sessions_permitted} sessions. If you would like to register for this session, please un-register from a different session first.";
    String RESEND_EMAIL_24_HOUR_BEFORE = "<p>Hi ${ticket_holder_first_name},</p>" +
            "<p><br></p>" +
            "<p>The ${event_name} starts tomorrow at ${eventStartEndDateTime} .</p>" +
            "<p><br></p>" +
            "<p>30 minutes before the event starts you will be able to join the event by clicking here.</p>" +
            "<p><br></p>" +
            "<p><a href=\"${eventURL}\">JOIN EVENT</a>&nbsp;</p>" +
            "<p><br></p>" +
            "<p>See you tomorrow!</p>";
    String RESEND_EMAIL_30_MINUTE_BEFORE = "<p>Hi ${ticket_holder_first_name},</p>" +
            "<p><br></p>" +
            "<p>The ${event_name} starts in 30 minutes! Click here to join the event.</p>" +
            "<p><br></p>" +
            "<p><a href=\"${eventURL}\">JOIN EVENT</a>&nbsp;</p>";

    String CHARGE_FOR = "chargefor";
    String DESC_FOR_CHECK_IN_PAYMENT = "Billing for check-in at portal page.";
    String LIVE_STREAM_EXHIBITORS_REACH = "You have purchased ${number} number of exhibitors for live streaming and you have reached that number.";
    String TOTAL_TIME_IN_BOOTH = "Total time in Booth";
    String DOCUMENT_DOWNLOADED = "Document Downloaded";
    String LINK_CLICKED = "Link Clicked";
    String GAME_TYPE = "Game Type";
    String VIDEO_PLAYED = "Video Played";
    String CHAT = "Chat";
    String NUMBER_OF_TIME_BOOTH_VISITED = "Number of times booth was visited";
    String CTA_BUTTON = "CTA Button";

    String MEETING_SCHEDULE_REQUEST = "MEETING_SCHEDULE_REQUEST";
    String CONNECTION_REQUEST = "connection-request";

    interface SMS {
        String AUCTION_LINK = "auction_link";
        String RAFFLE = "raffle";
    }

    String TEXT_TO_GIVE = "Text To Give";
    String TEXT_TO_GIVE_WITHOUT_FEE_PREFIX = "Text_To_Give_";
    String TEXT_TO_GIVE_WITH_FEE_PREFIX = "Text_To_Give_FEE_";
    String STRIPE_PLAN_UNSUBSCRIBED = "Successfully unsubscribed plan";
    String STRIPE_PLAN_UNSUBSCRIBED_WITH_END_DATE = "Your cancellation request has been accepted. You will still be able to use the service until {SUBSCRIPTION_END_DATE}.";
    String SPACE_EACH_SPACE = " each ";
    String AVAILABLE_UNTIL_DATE = "Available until {SUBSCRIPTION_END_DATE}";

    String STAFF_NAME = "Staff Name";
    String CHECK_PHONE_FOR_TEXT = "Please check your phone for a text message!";
    String UNABLE_TO_USE_TWILIO = "We were unable to use twilio at this time. Please try again later.";
    String MAIL_SENT_UNABLE_TO_USE_TWILIO = "Email has been successfully sent. we are unable to use twilio at this time, to notify by message please try again later";
    String NUMBER_ALREADY_ASSOCIATED_TO_EMAIL = "Your phone number is already associated with a different email address.";
    String ACCOUNT_ACTIVATION_REQUIRED = "[account activation required]";
    String STRIPE_CANCELED_STATUS = "canceled";
    String TEMPLATE_SAVED = "Template Saved.";
    String TEMPLATE_UPDATED = "Template Updated.";
    String TEMPLATE_DELETED = "Template Deleted.";
    String TEMPLATE_RESET = "The Template has been Reset Successfully";
    String REQUESTED_BY_CUSTOMER = "requested_by_customer";

    String PAGE_SAVED = "Page Saved.";
    String PAGE_UPDATED = "Page Updated.";
    String PAGE_DELETED = "Page Deleted.";

    String PAGE_URL_UPDATE = "Page URL Updated.";

    interface TICKETING { //NOSONAR
        String ATTRIBUTES = "attributes";
        String QUESTIONS = "questions";
        String NESTEDQUESTIONS = "nestedQuestions";
    }

    String EVENT_REQUEST_FORM_DETAIL = "eventRequestFormDetail";
    String BOOTH_DETAIL = "boothDetail";
    String SESSION_DETAIL = "sessionDetail";
    String SPEAKER_DETAIL = "speakerDetail";
    String REVIEWER_DETAIL = "reviewerDetail";
    String IS_PRIMARY_SPEAKER = "isPrimary";
    String ADMIN_AUCTION_BUY_NOW_EMAIL_HEADER_TEXT = "Congratulations, ${buyer_name} just purchased ${item_name}! A copy of the receipt can be found below." +
            "<table class=\"module\" role=\"module\" data-type=\"spacer\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"table-layout: fixed;\" data-attributes='%7B%22dropped%22%3Atrue%2C%22spacing%22%3A30%2C%22containerbackground%22%3A%22%23ffffff%22%7D'>\n" +
            "<tr><td role=\"module-content\" style=\"padding: 0px 0px 30px 0px;\" bgcolor=\"#ffffff\"></td></tr></table>";

    String DUPLICATE_AUCTION_ITEM_DEFAULT_CODE = "@@A";
    String DUPLICATE_RAFFLE_ITEM_DEFAULT_CODE = "@@R";
    String DUPLICATE_FUNDANEED_ITEM_DEFAULT_CODE = "@@F";
    String MAKE_DUPLICATE_EVENT = "Duplicate event created successfully.";

    String GENERAL_ADMISSION = "General Admission";
    String FREE_ADMISSION = "Free Admission";
    String DEFAULT_ORDER_CONFIRMATION_TEXT = "Thanks for registering for $EventName";

    String CHECK_YOUR_MAIL_FOR_YOUR_MAGIC_LINK = "Check your email for your magic link!";

    interface NEON {
        String SUCCESS = "SUCCESS";
        String FAIL = "FAIL";
        String Event_Name = "Event Name";
        String Event_SPACE_ID = "Event ID";
        String Event_Code = "Event Code";
        String Event_Description = "Event Description";
        String Event_Start_Date = "Event Start Date";
        String Event_Start_Time = "Event Start Time";
        String Event_End_Date = "Event End Date";
        String Event_End_Time = "Event End Time";
        String Event_Location_Name = "Event Location Name";
        String Address_Line_1 = "Address Line 1";
        String City = "City";
        String State = "State";
        String Country = "Country";
        String Zip_Code = "Zip Code";
        String AUCTION_C = "Silent_Auction_c";
        String EVENT_TICKETS_C = "Event_Tickets_c";
        String SESSION_ID = "SESSION_ID";
        String USER_SESSION_ID = "?userSessionId={SESSION_ID}";
        String OBJECT_API_NAME = "OBJECT_API_NAME";
        String AND_OBJECT_API_NAME = "&objectApiName={OBJECT_API_NAME}";
        String NOT_ACCEPTABLE_ERROR_CODE = "4061201";
    }

    interface PD {
        String TERM = "TERM";
        String DATE_FORMAT = "yyyy-MM-dd";
        String CURRENCY = "_currency";
    }

    interface TOKBOX {
        String STREAM_CREATED = "streamCreated";
        String STREAM_DESTROYED = "streamDestroyed";
        String CONNECTION = "connection";
        String STREAM = "stream";
        String VIDEO_TYPE = "videoType";
        String VIDEO_TYPE_CAMERA = "camera";

    }

    String WAITLIST_LIMIT_EXCEEDED = "There are only {SPOT_COUNT} spots remaining on the waitlist. Please adjust your request accordingly.";
    String CAMELCASE_YES = "Yes";
    String CAMELCASE_NO = "No";
    String HOST_ACCOUNT_PAGE_URL = "/host/settings/account";
    String ITEMS_WITH_USER_BID = "Items with user bid";
    String LIMITED_RAFFLE_TICKET = "There are only %d remaining tickets available. Your maximum purchase must be %d tickets or less.";
    String PASSWORD_RESET_SUCCESSFUL = "Your password has been reset successfully";
    String SUCCESSFULLY_UPLOADED_PHOTO = "Successfully uploaded user photo";

    int MAX_RETRIES_FOR_MP4_SUPPORT = 3;

    String CHALLENGE_DELETE_SUCCESSFUL = "Challenge Details Deleted Successfully";
    String CRITERIA_DELETE_SUCCESSFUL = "Criteria Details Deleted Successfully";
    String CRITERIA = "Criteria";
    String DEFAULT_CHALLENGE_NAME = "Default Challenge";
    int DEFAULT_CHALLENGE_GOAL = 10;
    String CHALLENGE_EXHIBITOR = "exhibitor";
    String MIN_VISIT_DURATION = "minVisitDuration";
    String PER_VISIT_POINT = "perVisitPoint";
    String POINT = "point";
    String ACTION_VISIT = "visit";
    String ACTION_CHAT = "chat";
    String ACTION_DOCUMENT_DOWNLOAD = "document download";
    String AREA_EXPO = "Expo";
    String YOU_CAN_NOT_ADD_EMPTY = "You can not add empty ";
    String LIST = " List";
    String OF_SESSION_AREA = " of Session Area";
    String INVALID_ACTION_MESSAGE_SESSION = "Invalid action %s for Session area.";

    String INVALID_ACTION_MESSAGE_SURVEY = "Invalid action %s of survey area";
    String YOU_CAN_NOT_ADD_EMPTY_LIST = "You can not add empty %s list";
    String INVALID_TRIGGER_MESSAGE_SURVEY = "Invalid trigger: %s of survey area.";

    String WORK_ADDRESS_1 = "Work Address 1";
    String WORK_ADDRESS_2 = "Work Address 2";
    String WORK_ADDRESS_3 = "Work Address 3";
    String WORK_COUNTRY = "Work Country";
    String WORK_COUNTRY_CODE = "Work Country Code";
    String WORK_STATE = "Work State";
    String WORK_STATE_CODE = "Work State Code";
    String WORK_PHONE = "Work Phone";
    String WORK_ZIP_CODE = "Work Zip Code";
    String WORK_CITY = "Work City";
    String WORK_FAX = "Work Fax";

    String CventCheckInManuallySync = "Attendee manually check-in process started";

    String PASSWORD_VALIDATION = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_=!@#$%^&*+-]).{8,}$";
    String PORTAL_LINK_FOR_ADD_TO_CALENDER = "On the day of the event, sign in with the email address {{speakerUserEmail}}. {{eventName}} is inviting you to be a speaker at {{sessionName}} during {{eventName}}. You can access a list of your talks here: {{portalLinkWithToken}} .";
    String STUDIO_LINK_FOR_ADD_TO_CALENDER = "You can join the talk directly by visiting: {{studioLinkWithToken}}";

    String IN_PERSON_PORTAL_REPLACE = "/portal";
    String LANDING_PAGE_LINK_FOR_ADD_TO_CALENDER = "On the day of the event, sign in with the email address {{speakerUserEmail}}. {{eventName}} is inviting you to be a speaker at {{sessionName}} during {{eventName}}. You can access a list of your session from here: {{portalLinkWithToken}} .";
    String STUDIO_LINK_FOR_IN_PERSON_FOR_ADD_TO_CALENDER = "You can access your session by visiting: {{studioLinkWithToken}}";

    String EMAIL_VALIDATION = "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$";
    String URL_VALIDATION = "^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]";
    String UPLOAD_ITEM_CSV = "Upload Item CSV : ";
    String SESSION_ID = "sessionId";
    String OPERATION_NAME = "operationName";
    String QUERY = "query";
    String TRUNCATE = "truncate";
    String USER_UNDERSCORE_ID = "user_id";
    String REPLIES = "replies";
    String REACTIONS = "reactions";
    String COOLDOWN = "cooldown";
    String SET = "set";
    String CONFIG_OVERRIDES = "config_overrides";
    String MODERATION = "/moderation/";
    String BAN = "ban";
    String CHANNEL_TYPE = "type";
    String CHANNEL_ID = "id";
    String BANNED_BY_ID = "banned_by_id";
    String TARGET_USER_ID = "target_user_id";
    String FLAG = "flag";
    String UN_FLAG = "unflag";
    String FILTER_CONDITIONS = "filter_conditions";
    String CONDITIONS="conditions";
    String CONDITION_SMALL ="condition";
    String COLUMN_NAME="columnName";
    String CID = "cid";

    String STATUS = "status";

    String IS_ADMIN_STAFF_OR_SUPER_ADMIN = "isAdminStaffOrSuperAdmin";
    String ASSET_STATUS = "assetStatus";
    String VARIABLES = "variables";
    String ITEM_CHECK = "itemCheck";
    String ITEM_CATEGORY_NAME = "itemCategoryName";
    String REGISTER_LOWERCASE = "register";
    String EVENT_FORMAT = "Event Format";
    String COMPANY_ID = "company_id";
    String COMPANY_NOT_FOUND_IN_DB = "Company not found in DB :";
    String COMPANY_NOT_FOUND_IN_INTERCOM = "Company not found in intercom : = {}";

    String AUTHORIZATION_CODE = "authorization_code";
    String GRANT_TYPE = "grant_type";
    String CODE = "code";
    String BASIC_SPACE = "Basic ";
    String REDIRECT_URI = "redirect_uri";
    String CODE_VERIFIER="code_verifier";
    String GIVEN_NAME="given_name";
    String FAMILY_NAME="family_name";
    String EMAIL_VERIFIED="email_verified";
    String CLIENT_ID = "client_id";
    String CLIENT_SECRET = "client_secret";
    String ACCESS_TOKEN = "access_token";
    String REFRESH_TOKEN = "refresh_token";
    String MEMBERSHIP_GRADE = "membership_grade";
    String SUB = "sub";
    String MEMBERSHIP_NUMBER = "membership_number";
    String MEMBERSHIP_SOCIETY = "membership_society";
    String MEMBERSHIP_STATUS = "membership_status";
    String EXPIRES_IN = "expires_in";
    String SENDING_MESSAGE_TO_WINNER = "Sending messages to winner : {}";
    String EXCEPTION = "Exception ";
    String UID= "uid";
    String PAGE_BUILDER= "PageBuilder";
    String USER_ID = "userId";
    String SPEAKER_ID = "speakerId";
    String USER_IDS = "userIds";
    String CHALLENGE_ID = "challengeId";
    String ATTACHMENTS = "attachments";

    String ATTACHMENT = "attachment";
    int ELASTIC_SEARCH_BATCH_SIZE = 10000;
    int ES_COMPOSITE_AGG_SIZE = 3000;
    String ES_COMPOSITE_AGGREGATION = "composite_aggregation";
    String SOURCE_ID="sourceId";

    String JOINED = "JOINED";


    interface HEADER {
        String ITEM_CODE = "Item Code";
        String ITEM_NAME = "Item Name";
        String BID_AMOUNT = "Bid Amount";
        String ITEM_CATEGORY = "Item Category";
        String FIRST_NAME = "First Name";
        String MARKET_VALUE = "Market Value";
        String LAST_NAME = "Last Name";
        String PHONE_NUMBER = "Phone Number";
        String EMAIL = "Email";
        String ADDRESS_1 = "Address 1";
        String ADDRESS_2 = "Address 2";
        String STATE = "State";
        String ZIP_CODE = "ZIP Code";
        String COUNTRY = "Country";
        String CITY = "City";
        String BID_TIME = "Bid Time";
        String BID_SOURCE = "Bid Source";

        String IS_WINNER = "Is Winner?";
        String PAID = "Paid?";
        String PAYMENT_SOURCE = "Payment Source";
        String STAFF_NAME = "Staff Name";
        String TICKETS_SUBMITTED_DATE = "Tickets Submitted Date";
        String TICKETS_SOURCE = "Tickets Source";
        String TICKETS_SUBMITTED = "Tickets Submitted";
        String TOTAL_DOLLAR_AMOUNT = "Total Dollar Amount";
        String DONATION_AMOUNT = "Donation Amount";
        String DONATION_TIME = "Donation Time";
        String DONATION_SOURCE = "Donation Source";
        String IS_REFUNDED = "Is Refunded?";
        String NOTE = "Note";
        String COMPANY = "Company";
        String EMAIL_ADDRESS = "Email Address";
        String TOTAL_POINT_EARNED = "Total Points Earned";
        String TOTAL_POINTS_CAMEL_CASE = "totalPoints";
        String BIDDER_NUMBER = "Bidder Number";
        String PLEDGE_AMOUNT = "Pledge Amount";
        String PLEDGE_DATE = "Pledge Date";
        String PLEDGE_SOURCE = "Pledge Source";
        String DATE_TIME = "Date/Time";
        String TITLE = "Title";
        String POINT = "Points";
        String USER_ID = "User Id";
        String USER_NAME = "User name";
        String MESSAGE = "Message";
        String TIMESTAMP = "Timestamp";
        String THREAD_MESSAGES = "Thread Messages";
        String EVENT_URL = "Event URL";
    }

    String STRIPE_EXCEPTION = "stripeException ";

    interface PDFHELPER {
        String TABLE_START = "<table>\n";
        String TABLE_END = "        </table>";
        String B_TAG_START = "                        <b class=\"fs18\">";
        String B_TAG_END = "</b>\n";
        String TD_TAG_START = "<td class=\"col-xs-3\">\n";
        String TD_TAG_END_WITH_NEWLINE = "                </td>\n";
        String TD_TAG_END = "                </td>";
        String TR_TAG_END = "            </tr>\n";
        String P_TAG_END = "                    </p>\n";
        String SPACES_WITH_DIV = "               </div>\n";
        String BR_B_LI_TAG = "<br /></b></li>\n";
    }

    String FILTER_GROUP = "filterGroups";
    String PROPERTIES = "properties";
    String OBJECT_CONTACT = "/objects/contacts";
    String OBJECT_COMPANY = "/objects/companies";
    String CONTACT_ID = "contactId";
    String LEAD = "lead";
    String LIMIT = "limit";
    String OBJECT_TYPE = "objectType";
    String OBJECT_ID = "objectId";
    String ADDED_BY_ADMIN = "Added by Admin";
    String TO_OBJECT_TYPE = "toObjectType";
    String TO_OBJECT_ID = "toObjectId";
    String ASSOCIATION_TYPE = "associationType";
    String AE_EVENTS_TO_CONTACT = "ae_events_to_contact";
    String COMPANYID = "companyId";
    String CONTACT = "contact";
    String OBJECTS = "/objects/";
    String ASSOCIATIONS = "/associations/";
    String INPUTS = "inputs";
    String ID_PROPERTY = "idProperty";
    String WHITE_LABEL_ID = "white_label_id";
    String AE_EVENTS_TO_DEAL = "ae_events_to_deal";
    String DEAL = "deal";
    String ID = "id";
    String ID_CAPITAL = "ID";
    String ADDON_ID = "addonId";
    String CONTAINS_TOKEN = "CONTAINS_TOKEN";
    String HAS_ADDITIONAL_EMAILS = "hs_additional_emails";
    String WEB_APP = "Web App";
    String ADMIN_APP = "Admin App";
    String ATTENDEE_APP = "Attendee App";
    String USER_DEFINED = "USER_DEFINED";
    String HUBSPOT_ORG_CREATOR = "Org Creator";
    String HUBSPOT_ORG_BILLING_CONTACT = "Org Billing Contact";
    String HUBSPOT_ORG_ADMIN = "Org Admin";
    String ASSOCIATED_EVENT = "Associated Event";
    String PRIMARY_COMPANY = "Primary Company";
    String PRIMARY_CONTACT = "Primary Contact";
    String HS_ENTERPRISE_ACCOUNT_LABEL = "Enterprise Account";
    String HS_WL_ACCOUNT_LABEL = "White Label Account";

    String HS_ORGANIZER_EVENTS_LABEL = "Organizer Events";
    String AE_EVENT_ID = "ae_event_id";
    String CREATION_LOCATION = "creation_location";
    String ADDED_TO = "added_to";
    String PLATFORM = "platform";
    String APP_VERSION_NUMBER = "app_version_number";
    String CONNECTOR = "connector";
    String HUBSPOT_CUSTOM_EVENT_PROPERTIES_FILTER = "hubspotCustomEventCommonPropertiesFilter";
    String USER_ADDED = "user_added";

    int HUBSPOT_MAX_RETRY_COUNT = 5;

    interface SESSION_OVERVIEW_REPORT_HEADER {
        String SESSION_NAME = "Session Name";
        String SESSION_DATE = "Session Date";
        String SESSION_LENGTH = "Session Length";
        String NUMBER_OF_ATTENDEES_BOOKMARKED="Number of Attendees bookmarked";
        String NUMBER_OF_ATTENDEES_REGISTERED = "Number of Attendees Registered";
        String NUMBER_OF_VIEWS_LIVE = "Number of Views (Live)";
        String AVERAGE_WATCH_TIME_LIVE = "Average Watch Time (Live)";
        String LIVE_ENGAGEMENT = "Engagement (Live)";
        String NUMBER_OF_VIEWS_RECORDED = "Number of Views (Recorded)";
        String AVERAGE_WATCH_TIME_RECORDED = "Average Watch Time (Recorded)";
        String RECORDED_ENGAGEMENT = "Engagement (Recorded)";

        String DOCUMENT = "Documents";
        String UNIQUE_DOWNLOADS = "Unique Document Downloads";
        String DOCUMENT_DOWNLOADED_LIST = "Documents Downloaded";
        String TOTAL_DOCUMENT_DOWNLOADED = "Total Document Downloads";
    }

    String REMINDER_FOR = "Reminder for ";
    String U_WL_LOGIN = "/u/wl-login/";
    String U_WL_NEW_PASSWORD = "/u/wl-new-password/";
    String TOKEN = "?token=";
    String SEATING_CHART = "Seating Chart";
    String SPONSORS = "sponsors";
    String EXHIBITORS = "exhibitors";
    String CUSTOMER = "customer";
    String CUSTOMER_ID = "Customer Id: ";
    String PAYMENT_METHOD = "payment_method";
    String API_KEY_STRIP_PAYMENT = "Api key: ";
    String REQUEST_OPTIONS = "requestOptions: ";
    String ITEMS = "items";
    String PAYOUT_ERROR_MESSAGE = "Something went wrong with payout";
    String BUYING_ITEM_NOW = "BUYING ITEM NOW";
    String API_EXCEPTION = "ApiException";

    String SESSION_TITLE = "Session Title";
    String QUESTION = "Question";
    String QUESTION_TYPE = "Question Type";
    String CHOICES = "Choices";
    String ANSWER_BY = "Answered By";
    String NO_DATA_FOUND = "No Data Found";
    String ASKER_NAME = "Asker Name";
    String ANSWER_S = "Answer(s)";
    String ANSWER_TYPE = "AnswerType";
    String QUESTION_STATUS = "Status";
    String HASH = "#";
    String DOLLAR = "$";
    String OPEN_CURLY_BRACE = "{";
    String CLOSE_CURLY_BRACE = "}";

    String QUESTION_TEXT = "questionText";
    String USER_NAME = "userName";
    String ANSWER = "answer";
    String QUESTION_ANSWER_TYPE = "answerType";

    String ATTACHMENT_FILENAME = "attachment; filename=\"%s\"";
    String OBJECT = "object";
    String EVENT_START_END_DATETIME = "eventStartEndDateTime";//NOSONAR
    String AMOUNT = "amount";
    String CURRENCY_SYMBOL = "currency_symbol";
    String SHORT_NAME_OF_ITEM = "item_short_name";
    String ITEM_OF_CODE = "item_code";
    String FIRST_OF_NAME = "first_name";
    String SEND_BUYER_RECEIPT_RAFFLE_START = "buyTickets raffleService.sendBuyerReceiptRaffle start";
    String SEND_BUYER_RECEIPT_RAFFLE_END = "buyTickets raffleService.sendBuyerReceiptRaffle end";
    String ADMIN_EMAIL_ID = "<EMAIL>";
    String ACCEL_EVENT_DOMAIN = "accelevents.com";

    String ACCEL_EVENT_REMOVED_DOMAIN = "acceleventsREMOVED.com";
    String BRILWORKS_DOMAIN = "brilworks.com";
    String PASSWORD = "$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm";
    String NEW_YORK_TIMEZONE_ID = "America/New_York";
    String LOGO_IMAGE = "6963b812-6c82-4cb8-832a-75380e734058tostitos-logo.jpeg";
    String ERRORS = "errors";
    String ERROR = "error";
    String PARTICIPANT = "participant";
    String DOLBY_ERROR = "DOLBY_ERROR_START_BROADCAST";
    String BROADCAST_ALREADY_STARTED = "The broadcast has already been started for the session";
    String UPDATE_STREAM = "UPDATE_STREAM";
    String ON_STAGE = "ON_STAGE";
    String SUBSCRIPTION_ITEM_TYPE_CHARGE="CHARGE";
    String SUBSCRIPTION_ITEM_TYPE_ADDON="ADDON";
    String OFF_STAGE = "OFF_STAGE";
    String CONNECTION_ID = "connectionId";

    String PRESENTATION = "PRESENTATION";
    String ACCESS_CODE = "Access code";
    String COUPON_CODE = "Coupon code";

    String DISPLAY_CODE = "Limited display code";

    String DISPLAY_CODE_ID = "displayCodeId";

    int ACCESSCODE_NAME_SIZE = 255;
    int COUPONCODE_NAME_SIZE = 255;
    int DISPLAY_CODE_NAME_SIZE = 255;

    interface SESSION_EXPAND {
        String ATTENDEE_COUNT = "attendeeCount";
        String WATCH_TIME_VIEWS = "watchTimeViews";
        String WATCH_TIME = "watchTime";

        String CHECKIN_ATTENDEE_COUNT = "checkInAttendeeCount";
    }

    int MAX_ATTENDEE_TO_DELETE_EVENT = 40;

    String SESSION_ALREADY_EXISTS = "Session already exist with same Title";
    String UPDATE_EVENT_NOTIFICATION_INPUT = "UpdateEventNotificationInput";
    String UPDATE_EVENT_NOTIFICATION = "updateEventNotification";
    String GET_EVENT_NOTIFICATION = "getEventNotification";
    String CREATE_EVENT_NOTIFICATION_INPUT = "createEventNotificationInput";
    String CREATE_EVENT_NOTIFICATION = "createEventNotification";
    String MODULE_TYPE_ID = "moduleTypeId";
    String MODULE_TYPE = "moduleType";
    String TARGET_AUDIENCE_TYPE = "targetAudienceType";
    String CANNOT_USE_RESTRICTED_FEATURE = "The organizer that this event is associated with is on the {plan_name}. " +
            "You have configured features beyond the limits of this plan. " +
            "Please upgrade the plan or switch to a different organizer in order to make your event Live.";

    interface CSV_SESSION_ERROR_MESSAGE { //NOSONAR
        String ID = "Session ID must be numeric or empty.";
        String CAPACITY = "Capacity must be numeric or empty.";
        String LOCATION_ID = "Location ID must be numeric or empty.";
        String TITLE = "Title must be not null OR Title must be less than 255 characters.";
        String FORMAT = "Format must be not null OR must be REGULAR_SESSION, MEET_UP, MAIN_STAGE_SESSION, WORKSHOP, EXPO, BREAK, OTHER";
        String TIME = "Time must be null OR must be within event start and end date/time OR Date is not in dd/MM/yyyy Or Time HH:mm format";
        String SHORT_DESCRIPTION = "Short description should not be more than 150 characters.";
        String VIRTUAL_SESSION_LOCATION = "Session location should be blank for virtual Session type.";
        String TAGS_TRACKS_CHARACTER_LIMIT = "Single tags/tracks should not be more than 50 characters.";
        String SESSION_NOT_FOUND = "Session not found!";
        String SESSION_TYPE_FORMAT = "Session type format is not valid for current event.";
        String SESSION_LOCATION = "Session Location ID is not valid for current event.";

    }

    interface CSV_ATTENDEE_ERROR_MESSAGE {
        String BUYER_ATTENDEE_ALREADY_EXISTS = "Buyer with email already exists.";
        String HOLDER_ATTENDEE_ALREADY_EXISTS = "Holder with email already exists.";
        String BUYER_AND_HOLDER_WITH_SAME_EMAIL_ALREADY_EXISTS = "A buyer and holder with the same email address already exist.";
        String FIRST_NAME_BLANK_MESSAGE = "First name is blank.";
        String EMAIL_BLANK_MESSAGE = "Email Address is blank.";
        String LAST_NAME_BLANK_MESSAGE = "Last name is blank.";
        String EMAIL_NOT_VALID_MESSAGE = "Email Address is not in valid format.";
        String EMAIL_LIMIT_MESSAGE = "Email field should contain single email value.";
        String PROFILE_IMAGE_NOT_VALID_MESSAGE = "Profile Image URL is not valid, please enter valid URL.";
        String TRANSACTION_ID_NOT_VALID_MESSAGE = "Order Id have must number only.";
        String TRANSACTION_ID_SIZE_MESSAGE = "Only 45 Characters are allowed in TransactionID.";
        String TRANSACTION_ID_ALREADY_CREATED_ORDER = "Transaction ID already exists. Please use a unique ID.";
        String FIRST_NAME_SIZE_MSG = "Only 50 Characters are allowed in firstName.";
        String LAST_NAME_SIZE_MSG = "Only 50 Characters are allowed in lastName.";
        String EMAIL_SIZE_MSG = "Only 75 Characters are allowed in email.";
    }

    String EXPO_TARGET_AUDIENCE = "STAFF,EXHIBITOR ADMIN";
    String MUX_LIVE_STREAM_STATUS_DISABLED = "disabled";
    String ANONYMOUS = "ANONYMOUS";
    String EVENT_URL_LENGTH_EXCEPTION = "The character limit for the Event URL is 50 characters, the URL you attempted to save is ${urlLength} characters. Please try a short event URL.";

    //Mux
    String MUX_ASSET_STATUS_READY = "ready";
    String LIVE_STREAM_ID = "live_stream_id";
    String ASSET_ID = "asset_id";
    String ASSET_NOT_FOUND = "ASSET_NOT_FOUND";

    String ASSET_DELETE = "ASSET_DELETE";
    String BASE_MUX_URL = "https://stream.cast.accelevents.com/";
    String STATIC_RENDITIONS = "static_renditions";
    String FILES = "files";
    String FILESIZE = "filesize";
    String HIGH_MP4 = "high.mp4";
    String MEDIUM_MP4 = "medium.mp4";
    String LOW_MP4 = "low.mp4";

    String SIGNED="signed";

    String PUBLIC="public";

    String INGEST_HEALTH= "ingest_health";

    String STREAM_DRIFT_SESSION_AVG =  "stream_drift_session_avg";

    String STREAM_DRIFT_DEVIATION_FROM_ROLLING_AVG="stream_drift_deviation_from_rolling_avg";

    String UPDATED_AT = "updated_at";

    String POLICY ="policy";
    String ARCHIVE_RETRIEVAL = "archive-retrieval";
    String GLACIER_SNS_SUBSCRIPTION_ENDPOINT = "/rest/webhook/archives/jobStatus";
    String SESSION_ARCHIVE_UPLOAD_PROCESS_STARTED = "The Session Assets retrieval process has started and it will take 5-8 hours to complete.";

    String ALL_ARCHIVES_ARLEADY_RESTORED = "All of the archives have been recovered. Please review sessions.";
    String SUBSCRIPTION_CONFIRMATION = "SubscriptionConfirmation";
    String STRING_TOKEN = "Token";
    String JOB_ID = "JobId";
    String COMPLETED = "Completed";
    String STATUSCODE = "StatusCode";
    String ARCHIVE_ID = "ArchiveId";
    String COMPLETION_DATE = "CompletionDate";
    String HTTPS = "https";
    String HTTPS_DOUBLE_SLASH = "https://";
    String HTTP = "http";

    String QUE_MARK_ID_PROPERTY_EQ = "?idProperty=";

    interface CSV_HEADER_LOUNGE {
        String PHOTO_UPLOAD_COUNT = "Photo Upload Count";
        String VIDEO_UPLOAD_COUNT = "Video Upload Count";
        String ATTEND_COUNT = "Attend Count";
    }

    int NAME_SIZE = 50;
    int PASSWORD_SIZE = 72;
    int FULL_NAME_SIZE = 100;
    int EMAIL_SIZE = 75;
    String FIRST_NAME_SIZE_MSG = "Only 50 Characters are allowed in firstName";
    String LAST_NAME_SIZE_MSG = "Only 50 Characters are allowed in lastName";
    String EMAIL_SIZE_MSG = "Only 75 Characters are allowed in email";
    String PASSWORD_SIZE_MSG = "Only 72 Characters are allowed in password";


    String FULL_NAME_SIZE_MSG = "Only 100 Characters are allowed in fullName";

    interface NETWORKING_WEBSOCKET {
        String CONNECTION_ID = "connectionId";
        String ACCESSTOKEN = "accessToken";
        String EVENT_URL = "eventUrl";
        String NETWORKING_RULE_MATCHING_URL = "/rest/virtual/event/{eventURL}/networking-rule/session/{sessionId}/connectionId/{connectionId}/matching";
    }

    String UPDATEDAT = "updatedAt";
    String ACTION_USER = "actionUser";

    String USER = "User";
    String PLAYBACK_URL = "playbackUrl";
    String PLAYBACK_RESTRICTION_TOKEN = "playBackRestrictionToken";
    String GET_EVENT_STREAM_STATUS_LOG = "getEventStreamStatusLog";
    String CREATED_EVENT_STREAM_STATUS_LOG = "createEventStreamStatusLog";
    String CREATED_EVENT_STREAM_STATUS_LOG_INPUT = "createEventStreamStatusLogInput";
    String UPDATE_EVENT_STREAM_STATUS_LOG = "updateEventStreamStatusLog";
    String UPDATE_EVENT_STREAM_STATUS_LOG_INPUT = "updateEventStreamStatusLogInput";
    String UPDATE_EVENT_STREAM_STATUS_LOG_MUTATION = "mutation updateEventStreamStatusLog($updateEventStreamStatusLogInput: UpdateEventStreamStatusLogInput!) { updateEventStreamStatusLog(input: $updateEventStreamStatusLogInput) { eventId type playbackUrl status actionUser updatedAt videoDuration playBackRestrictionToken } }";
    String CREATE_EVENT_STREAM_STATUS_LOG_MUTATION = "mutation createEventStreamStatusLog($createEventStreamStatusLogInput: CreateEventStreamStatusLogInput!) { createEventStreamStatusLog(input: $createEventStreamStatusLogInput) { eventId type playbackUrl status actionUser updatedAt videoDuration playBackRestrictionToken } }";

    String GET_CHIME_RECORDING_ASSET_STATUS = "getChimeSessionRecordingAssetStatus";
    String CREATED_CHIME_RECORDING_ASSET_STATUS = "createChimeSessionRecordingAssetStatus";
    String CREATED_CHIME_RECORDING_ASSET_STATUS_INPUT = "createChimeSessionRecordingAssetStatusInput";
    String UPDATE_CHIME_RECORDING_ASSET_STATUS = "updateChimeSessionRecordingAssetStatus";
    String UPDATE_CHIME_RECORDING_ASSET_STATUS_INPUT = "updateChimeSessionRecordingAssetStatusInput";


    String CREATE_ACCEL_CHIME_MEETING = "createAccelChimeMeeting";
    String CREATE_ACCEL_CHIME_MEETING_INPUT = "createAccelChimeMeetingInput";

    String DELETE_ACCEL_CHIME_MEETING = "deleteAccelChimeMeeting";
    String DELETE_ACCEL_CHIME_MEETING_INPUT = "deleteAccelChimeMeetingInput";

    String MEETING_ID = "meetingId";

    String EXTERNAL_USER_ID_REGEX = "[-_&@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*";

    String UPDATE_CHIME_RECORDING_ASSET_STATUS_MUTATION = "mutation updateChimeSessionRecordingAssetStatus($updateChimeSessionRecordingAssetStatusInput: UpdateChimeSessionRecordingAssetStatusInput!) { updateChimeSessionRecordingAssetStatus(input: $updateChimeSessionRecordingAssetStatusInput) { sessionId assetId status updatedAt } }";
    String CREATE_CHIME_RECORDING_ASSET_STATUS_MUTATION = "mutation createChimeSessionRecordingAssetStatus($createChimeSessionRecordingAssetStatusInput: CreateChimeSessionRecordingAssetStatusInput!) { createChimeSessionRecordingAssetStatus(input: $createChimeSessionRecordingAssetStatusInput) { sessionId assetId status updatedAt } }";
    String PROCESSING = "PROCESSING";


    String SINGLE_WHITE_SPACE = " ";
    String AND_WAY_TO_LOGIN_EQUALS = "&wayToLogin=";
    String QUE_MARK_USER_KEY_EQUALS = "?userKey=";
    String AND_NEW_USER_EQUALS = "&newUser=";
    String AND_EVENT_ID_EQUALS = "&eventId=";
    String AND_PASSWORD_LESS_EQUALS="&isPasswordLess=";
    String AND_ATTENDEE_MAIL_EQUALS = "&attendeeMail=";
    String QUE_IS_FROM_PLATFORM_EQUALS = "&isFromPlatform=";
    String SLASH_U_PROFILE = "/u/myprofile#Profile";
    String SLASH_U_PROFILE_TICKET = "/u/myprofile#tickets";
    String USER_KEY_EXPRESSION="(userKey=)([^&]*)";
    String USER_KEY_EQUALS="userKey=";
    String LEFT_SIDE_NAV_MENU = "leftSideNavMenu";
    String LOBBY_TAB = "lobbyTab";
    String STAGE_SESSION_TAB = "stageSessionTab";
    String STAGE_STRING = "Stage";
    String MAIN_STAGE_SESSION_STRING = "Main Stage Sessions";
    String MAIN_STAGE_SESSION_ENUM = "MAIN_STAGE_SESSION";
    String REGULAR_SESSION_ENUM = "REGULAR_SESSION";
    String REGULAR_SESSIONS = "Regular Sessions";
    String MAIN_STAGE_SESSION = "Main Stage";
    String BREAKOUT_SESSIONS = "Breakout Sessions";
    String SESSIONS = "Sessions";
    String RECORD_NOT_UPDATED_FOR_EQUALS_BRACES = "record not updated for ={}";
    String COLOR_HAX_0000FF = "#0000FF";
    String MUX_LIVE_STREAMS_VIDEO_V1_PATH = "/video/v1/live-streams/";
    String LIMIT_VALUE_BRACES_STRING = "{limitValue}";
    String DEFAULT_COLOR_HAX_000000 = "#000000";
    String DEFAULT_COLOR_HAX_00000014 = "#00000014";
    String HASH_COLOR_31415f = "#31415f";//NOSONAR
    String MEET_UP_STRING = "Meet Up";
    String HASH_COLOR_FFFFFF = "#FFFFFF";
    String HASH_COLOR_1E2137 = "#1E2137";
    String ASSET_PLAYBACK_DELETE = "ASSET_PLAYBACK_DELETE";
    String PLAYBACK_IDS_STRING = "playback_ids";
    String ATTENDEES = "Attendees";
    String COUNT_STRING = "Count";
    String AND_LOCATION_EQUALS = "&location=";
    String BACKSLASH_DOT_BACKSLASH = "\".\"";
    String AUCTION_SERVICEIMPL_GET_EVENTID_ERROR_MSG = "AuctionServiceImpl | parseItemsCSV | getEventId :{} | errorMessage : {} ";
    String AUCTION_SERVICEIMPL_ITEM_NAME_ITEM_DESC = "AuctionServiceImpl | parseItemsCSV | Item Name = {} | Item desc = {} ";
    String ITEM_CODE_STRING = "Item code ";
    String AND_IN_LOC_UC_EQUALS="&IN_LOC=";

    // url for add to calender
    String DEFAULT_CALENDER_INVITATION = "Click this link to see the event page: %s/e/%s ";
    String YAHOO_CALENDER_URL = "http://calendar.yahoo.com/?v=60&view=d&type=20&TITLE=%s&ST=%s&ET=%s&DESC=%s";
    String GOOGLE_CALENDER_URL = "https://calendar.google.com/calendar/r/eventedit?text=%s&dates=%s/%s&details=%s";
    String OUTLOOK_CALENDER_URL = "https://outlook.live.com/owa/?path=/calendar/action/compose&rru=addevent&startdt=%s&enddt=%s&subject=%s&body=%s";
    String ICAL_DOWNLOAD_URL = "%s/rest/events/%s/download/iCal/event/%s";
    String ICAL_DOWNLOAD_URL_FOR_SAVE_A_SEAT = "%s/rest/events/%s/session/download/iCal/%s/%s/%s/%d/";

    String ATTENDED = "Attended";
    String FREE = "free";
    String SWITCH_RECURRING_EVENT_TO_SINGLE_DAY_EVENT_SUCCESSFULLY = "Recurring Event switched to Single day Event successfully.";

    String EXHIBITOR_ALREADY_EXISTS = "Exhibitor already exists";
    String UNABLE_TO_CREATE_FEED = "Unable to create new feed";
    String UNABLE_TO_UPDATE_FEED = "Unable to update feed";
    String UNABLE_TO_RETRIEVE_FEED = "Unable to retrieve feeds";
    String UNABLE_TO_FOLLOW_FEED = "Unable to follow feeds";
    String UNABLE_TO_GET_CLIENT = "Unable to get client";
    String UNABLE_TO_RETRIEVE_FEED_REACTION = "Unable to retrieve feeds reaction";
    String LOUNGE_FEED_NOT_FOUND = "Activity stream not found";
    String INFO_DESK_ALREADY_EXIST = "Info desk already present.";
    String INFO_DESK_NOT_FOUND = "Info desk not found.";
    String SESSION_DATES_NOT_BETWEEN_EVENT = "Session start and end time should be after event start time and before event end time.";
    String REQUIRED_FOR_AE_STUDIO = "Stream Key, Stream Url can not be blank for accelevents provider";
    String SESSION_NOT_FOUND = "Session not found!";
    String SPEAKER_NOT_FOUND = "Speaker not found!";
    String TAG_TRACK_NOT_FOUND = "Tag or Track not found!";
    String EVENT_TICKET_NOT_FOUND = "EventTicket Not found!";
    String SESSION_SPEAKER_NOT_FOUND = "Session Speaker not found!";
    String SESSION_ENDED = "Session is ended!";
    String DEFAULT_PLAYBACK_NOT_FOUND = "No default playback found for session.";
    String EVENT_PLAN_CONFIG_NOT_FOUND_1 = "This flag is only applicable for virtual events";
    String EVENT_PLAN_CONFIG_NOT_FOUND = "Event Plan config details not found for this event";
    String ATTENDEE_CAN_NOT_ADDED = "Attendee doesn't have ticket or not match role admin, exhibitor, speaker.";
    String ATTENDEE_ALREADY_ADDED = "Attendee already added.";
    String ATTENDEE_NAME_CAN_NOT_EMPTY = "Attendee name can not empty.";
    String SUPER_ADMIN_CANNOT_CONNECT_WITHOUT_TICKET = "Super admin can not connect without purchasing event ticket.";
    String ATTENDEE_NOT_FOUND = "Attendee Not Found";
    String ATTENDEE_CAN_NOT_CONNECTED = "Attendee can not connect to self.";
    String ATTENDEE_ALREADY_CONNECTED = "Attendee already connected.";
    String CONNECTION_REQUEST_ERROR = "Connection Request Not Found";
    String CONNECTION_ALREADY_ACCEPTED = "Connection Request Already Accepted";
    String CONNECTION_ALREADY_REJECTED = "Connection Request Already Rejected";
    String PERMISSION_DENIED_REGISTER_EVENT = "This is not registered user";
    String EVENT_ENDED = "This event is no longer available";
    String NOT_ALLOW_TO_ACCESS_VIRTUAL_PORTAL = "You have not purchased Virtual Event Tickets. Please, contact the organizers for more information";
    String TICKETING_MODULE_NOT_ACTIVATED = "Please activate your event to access the portal.";
    String MAX_FREE_ATTENDEES_REACHED_FOR_PORTAL = "All capacity for free registrations have been redeemed. You can purchase a pass to access the event!";
    String MAX_ATTENDEE_ALLOWED_LIMIT_REACHED = "The maximum number of attendees for this event has been reached. Please contact the event organizer.";
    String BLOCKED = "You have been blocked by admin. Please contact system administrator.";
    String USER_DID_DONATION_NOT_PURCHASE_TICKET = "Looks like you forgot to get a ticket when you made a donation, please purchase a ticket from the event page or contact the event organizer.";
    String URL_NOT_SUPPORTED = "Cant create url";
    String EVENT_NOT_FOUND = "Event Not Found";
    String USER_NOT_ALLOWED_TO_REGITER_WITHOUT_TICKET = "User not allowed to regiter without ticket";
    String TICKET_TYPE_NOT_MATCHED = "Your registration does not permit access to this session";
    String NOT_AUTHORIZED_TO_ACCESS_SESSION_USER_ANALYTICS = "Admin with the current plan is not authorized to access this functionality. Please upgrade your plan to access.";
    String REGISTER_FAILED = "You have not yet registered for the event. You must register for the event before you can register for individual sessions.";
    String TIME_ACCESS_ELAPSED_LIVE = "The time limit for accessing this session is expired.";
    String TIME_ACCESS_ELAPSED_RECORDING = "The recording of this session is only available to users who watched it live.";
    String EXCEED_SESSION_CAPACITY = "Max user registration capacity reached";
    String SESSION_IS_NOT_PRIVATE = "Sorry, This session is not private, Bulk registration is only supported in private sessions.";
    String EXCEED_WORKSHOP_SESSION_CAPACITY = "All 250 spaces in this meeting have already been taken. Would you like to remove someone from the session so that you can join?";
    String CAN_NOT_GENERATE_STREAM_IN_PROGRESS = "Can not generate stream key, live stream in process";
    String MUX_LIVE_STREAM_NOT_AVAILABLE = "Mux live stream not available.";
    String MUX_LIVE_STREAM_NOT_COMPLETED = "Mux live stream not completed.";
    String MUX_LIVE_STREAM_STATUS_NOT_AVAILABLE = "Mux live stream status not available.";
    String MUX_LIVE_STREAM_CAN_NOT_CREATE = "Mux live stream can not create";
    String MUX_LIVE_STREAM_CAN_NOT_CREATE_UPLOAD_URL = "Mux live stream can not create upload url.";
    String MUX_ASSET_ID_NOT_AVAILABLE = "Mux asset id not available.";
    String MUX_PLAYBACK_ID_NOT_AVAILABLE = "Mux playback id not available.";
    String FAILED_TO_ENABLE_MP4_SUPPORT_FOR_MUX = "Failed to enable MP4 support.";
    String MUX_DURATION_NOT_AVAILABLE = "Mux duration not available.";
    String MUX_PREPARING_VIDEO_ASSET = "Mux is preparing video asset,wait for some time to add subtitle for it!";
    String MUX_SUBTITLE_FILE_CAN_NOT_BE_ADDED = "Mux subtitle file can not be added.";
    String MUX_SUBTITLE_FILE_CAN_NOT_BE_REMOVED = "Mux subtitle file can not be removed.";
    String MUX_LIVE_STREAM_NOT_RE_ENABLED = "Due to technical error broadcast is not starting.";
    String NOTIFICATION_PREFERENCE_ADDED = "Notification Preference already added.";
    String NOT_VALID_JSON_FORMAT = "Json Format is not valid";
    String YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_VIDEO = "You Can Delete Only Own Networking Lounge Video.";
    String DEFAULT_PLAYBACK_NOT_FOUND_FOR_VIDEO = "No default playback found for video.";
    String NETWORKING_LOUNGE_NOT_FOUND = "Networking Lounge Not Found.";
    String YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_PHOTO = "You Can Delete Only Own Networking Lounge Photo.";
    String NETWORKING_LOUNGE_WITH_SAME_NAME_ALREADY_EXIST = "A networking lounge with the same name already exists.";
    String SESSION_BREAKOUT_ROOM_NOT_FOUND = "SessionBreakoutRoom chat not found";
    String EXHIBITOR_CATEGORY_NOT_EXISTS = "Exhibitor Category Not exists";
    String EXHIBITOR_PRODUCT_NOT_FOUND = "Product not found";
    String STAFF_NOT_FOUND = "Staff detail not found";
    String USER_HAS_ALREADY_LOGGED_IN = "User has already logged in at website, Can't change email now!";
    String ALREADY_STAFF = "You have already added this team member.";
    String EMAIL_ALREADY_ASSIGNEDED = "This email address is already assigned. You can not change existing staff with this email address. Please add new staff if you want to add that email as admin/staff.";
    String EXHIBITOR_STAFF_ROLE_NOT_MATCHED = "exhibitoradmin & leadretriever are valid roles for exhibitor staff";
    String MAX_TEAM_MEMBER_LIMIT_REACHED = "The maximum number of team members has been reached";
    String EXHIBITOR_ADMIN_CAN_NOT_REMOVE_EXHIBITOR_ADMIN = "Exhibitors admins should not be able to remove exhibitor admins";
    String CAN_NOT_GENERATE_LEAD = "Lead retrieval is not allowed for this exhibitor";
    String FIRST_NAME_SIZE_LIMIT = "Only 50 Characters are allowed in firstName";
    String LAST_NAME_SIZE_LIMIT = "Only 50 Characters are allowed in lastName";
    String EMAIL_SIZE_LIMIT = "Only 75 Characters are allowed in email";
    String ALREADY_USED_TICKET = "You have already used your ticket";
    String LEAD_IS_NOT_BELONG_TO_THIS_LEADRETRIEVER = "You have not added this lead. You can delete only lead which are added by you.";
    String LEAD_NOT_EXISTS = "Lead Not exists";
    String ACCESS_CODE_NOT_FOUND = "Access code not found.";
    String ATTRIBUTE_NAME_EXIST = "Attribute name already exist";
    String ATTRIBUTE_NOT_EXISTS = "Attribute Not exists";
    String MCQ_ONLY_FOR_ENTERPRISE_PLAN = "Multiple choice question is only for enterprise plan";
    String PURCHASE_EVENT_TICKET_REQUIRED_ENABLE_SHOW_REGISTRATION_BUTTON = "There are currently no tickets available. Please, contact the event organizer.";
    String PURCHASE_EVENT_TICKET_REQUIRED_FOR_ADDONS_TICKET = "You can not purchase addons ticket without purchase event ticket.";
    String ZEROORDERCOUNT = "order ticket count is zero";
    String NUMBER_OF_SEATS_MUST_BE_EQUAL_TO_NUMBER_OF_TICKETS = "Number of selected seats must be same as number of tickets.";
    String SEAT_SELECTION_NOT_ALLOWED_FOR_FREE_TICKET = "Seat selection not allowed for FREE Tickets";
    String NOT_UNPAID_ORDER = "Order type is not UNPAID.";
    String ALREADY_PAID_ORDER = "Order is already paid";
    String CREDIT_CARD_PROCESSING_NOT_ENABLE = "Credit card processing not enabled";
    String BIRTH_DATE_NOT_ALLOWED_IN_FUTURE = "Birth date is not allowed in future";
    String BLOCKED_ADMIN_MSG = "This user is blocked";
    String ACTIVATE_REGISTRATION = "Please activate your event to begin accepting registrations";
    String RECURRING_EVENT_MODIFIED_BY_HOST = "Event details are modified by host. Please try again.";
    String CHECK_OUT_TIME_EXPIRE = "You have not completed checkout in the allotted time. Please click here to restart the checkout process.";
    String RECURRING_EVENT_ID_NOT_EMPTY = "Recurring event id can not null for recurring event";
    String COUPON_CODE_NOT_APPLICABLE_ON_DONATION_TICKETING_TYPE = "Discount coupon can not be applied on donation ticket type.";
    String COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE = "This coupon is not valid for this registration type.";
    String COUPON_IS_NOT_APPLIED_TO_THIS_ORDER = "Coupon is not applied to this order";
    String THIS_DISCOUNT_CODE_HAS_ALREADY_BEEN_APPLIED_TO_YOUR_TRANSACTION = "This discount code has already been applied to your transaction.";
    String COUPON_REACHED_MAX_USAGE_PER_USER = "You have already reached the maximum number of uses for this coupon.";
    String COUPON_REACHED_MAX_USAGE = "This coupon has reached max number of uses.";
    String COUPON_IS_NOT_AVAILABLE = "Coupon is not available";
    String COUPON_CODE_EXPIRED = "This coupon has expired.";
    String APPLIED_COUPON_NOT_FOUND = "Applied coupon not found";
    String BARCODE_NOT_EXIST = "Incorrect code. Please double-check that you are scanning the correct code for this event.";
    String THIS_DOWNLOAD_PDF_IS_NOT_ALLOWED_FOR_THIS_ORDER = "Download pdf is not allowed for this order";
    String INVALID_WAIT_LIST_IDS = "Invalid waitlist details.";
    String EVENT_TICKETS_NOT_FOUND = "There is no available tickets for update ticket status to Check IN.";
    String CHECKIN_NOT_ALLOWED_BEFORE_TIME = "Check IN Not allowed before configured time";
    String BARCODE_ALREADY_REFUNDED = "Checked-in failed. Ticket was refunded.";
    String BARCODE_ALREADY_CHECKED_IN = "This registration has already been used.";
    String ATTENDEE_HAVE_VIRTUAL_TICKET = "This ticket only grants access to the virtual event";
    String COLLECT_PAYMENT_FIRST = "Please collect payment first.";
    String NOT_VALID_PAYMENT = "Not valid payment type";
    String CAN_NOT_CANCEL_SCHEDULE_EVENT = "Can not cancel event schedule. Tickets are sold from this event schedule.";


    String PLEDGE_NOT_ACTIVE = "Please activate this module to start accepting pledges.";

    String NOT_ALLOW_TO_SELECT_INTEREST_TAGS = "Not allowed to select more than 10 interest tags.";
    String START = "start";
    String STOP = "stop";
    String CHIME_MEETING_ENDED = "chime:MeetingEnded";
    String CHIME_ATTENDEE_DROP = "chime:AttendeeDropped";
    String CHIME_ATTENDEE_DELETE = "chime:AttendeeDeleted";
    String CHIME_ATTENDEE_JOINED = "chime:AttendeeJoined";
    String CHIME_ATTENDEE_LEFT = "chime:AttendeeLeft";
    String CHIME_MEDIA_PIPELINE_DELETED = "chime:MediaPipelineDeleted";
    String SLASH_PROCESSED_SLASH = "/processed/";
    String MEDIA_PIPELINE_DASH = "MediaPipeline-";
    String MEDIA_PIPELINE = "media-pipeline";
    String COMPOSITED_VIDEO = "composited-video";
    String DOT_MP4 = ".mp4";
    String AWS_REFERER = "aws:Referer";
    String STATEMENT = "Statement";
    String SID = "Sid";
    String ALLOW_DOMAIN_ACCESS_TO_BUCKET = "Allow-Domain-Access-To-Bucket";
    String CONDITION = "Condition";
    String STRING_LIKE = "StringLike";
    String S3_BUCKET_URL_FORMATER = "https://%s.s3.amazonaws.com/";
    String ASSETID = "assetId";
    String VIDEO_LIVE_STREAM_RECORDING = "video.live_stream.recording";
    String VIDEO_LIVE_STREAM_IDLE = "video.live_stream.idle";

    String CONSUMER_CLOSED = "Consumer is closed";

    String CONNECTION_CLOSED = "Connection is closed";


    String NO_RATING = "No Rating";
    String UNRATED = "Unrated";
    String COLD = "Cold";
    String WARM = "Warm";
    String HOT = "Hot";

    String SPONSOR_ANALYTIC_REPORT_HEADER_NAME = "Name";
    String SPONSOR_ANALYTIC_REPORT_HEADER_TOTAL_CLICKS = "Total Clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_LANDING_PAGE_CLICKS = "Landing Page Clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_VEH_CLICKS = "Virtual Event Hub clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_LANDING_PAGE_ANONYMOUS_CLICKS = "Landing Page Anonymous Clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_LANDING_PAGE_LOGGED_IN_USER_CLICKS = "Landing Page Logged in User clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_LOBBY_CAROUSEL_CLICKS = "Lobby Carousel Clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_LOBBY_SPONSOR_TAB_CLICKS = "Lobby Sponsor Tab Clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_SESSION_CAROUSEL_CLICKS = "Session Carousel Clicks";
    String SPONSOR_ANALYTIC_REPORT_HEADER_SESSION_SPONSOR_TAB_CLICKS = "Session Sponsor Tab Clicks";

    String USER_NOT_FOUND_IN_EVENT_TICKETS = "User has not purchase ticket for this Event";
    String IS_PRONOUN_TYPE = "isPronoun";
    String IS_ACCEPT_DIRECT_MESSAGES = "isAcceptDirectMessages";
    String CAN_NOT_SET_DEFAULT_PLAYBACK = "Can not set default playback for active session";
    String HOLD_TOKEN_DOES_NOT_MATCH = "Selected seat already booked by someone else, Please try again.";
    String TICKET_COUPON_NOT_FOUND = "The discount code you entered is not valid for this event.";
    String ORDER_NOT_FOUND = "Order not found";

    String EXPOID = "EXPO";
    String NOT_PARSE_NUMBER_TO_INTENDED_FORMAT = "Unable to parse the number to the intended format.";
    String RAFFLE_NOT_ACTIVE = "Please activate this module to start accepting tickets.";
    String RAFFLE_IS_EXPIRE = "This Raffle is Over!";
    String EVENT_CC_NOT_ENABLE = "Event CC is not enabled";
    String RAFFLE_TICKETS_ARE_SOLD_OUT = "Raffle tickets are sold out!";
    String LIMITTED_TICKET_MULTI_LANGUAGE = "There are only {ticket_max_val} remaining tickets available. Your maximum purchase must be  {ticket_max_min_val} tickets or less.";
    String TICKET_MAX_VAL = "{ticket_max_val}";
    String TICKET_MAX_MIN_VAL = "{ticket_max_min_val}";
    String RAFFLE_TICKET_PKG_NOT_FOUND = "Ticket pkg not found";
    String TICKET_PURCHASING_FOR_EVENT = "Thank you for purchasing {number_of_ticket} {ticket} for {event_name}";
    String NUMBER_OF_TICKET_FOR_EVENT = "{number_of_ticket}";
    String TICKET = "{ticket}";
    String EVENT_NAME_FOR_TICKET = "{event_name}";
    String DO_NOT_HAVE_ENOUGH_TICKET = "You do not have enough tickets, please purchase additional tickets below.";
    String TICKET_SHOULD_BE_MORE_THAN_ZERO = "Tickets should be more than 0.";
    String RAFFLE_ONLINE_PURCHASE_TICKETS = "Thank you for purchasing [number_of_tickets] tickets! You can buy tickets at [raffle_ticket_purchase_url]";
    String ONLY_25_CHARACTER_ALLOWED_IN_ITEM_CATEGORY_NAME = "Only 25 character are allowed in item category name";
    String CONFIRM_NUMBER_EMAIL_SENT = "Email has been sent to your email address,please check and confirm your phone number with email address";
    String NO_ACCOUNT_FOUND = "Sorry, no account is associated with this address. Please double-check and try again.";
    String INVALID_EMAIL = "this is not valid emailid,Please provide Valid emailid.";
    String ENTER_EMAIL_FOR_PHONENUMBER = "Please enter the email address associated with this phone number. We will send a confirmation request to that Email address.";
    String CONFIRM_EMAIL_SUBJECT = "Confirm your Phone number with Email";
    String CHARGEBEE_FREE_PLAN = "free-plan";
    String RAFFLE_ONLINE_PURCHASE = "Thank you for purchasing [#] tickets. To enter tickets for an item, reply with the 3 character item code and the number of tickets. Example: ABC4";
    String SUCCESSFUL_TICKET_SUBMISSION_PAYMENT_DISABLED = "Thank you for entering [tickets_submitted] tickets for item [item_code]. You have [numberoftickets] tickets remaining.";
    String CAUSEAUCTION_NOT_FOUND = "CauseAuction Not Found";
    String MORE_PLEDGES_SUBMITTED = "The maximum number of pledges being accepted has already been reached.";
    String CHARGE_CREATION_FAILED = "Charge creation failed";
    String PLEDGE_STAFF_SUCCESS = "Thank you for pledging [currency_symbol][amount] for item [item_code].";
    String PLEDGE_STAFF_ITEM_SUBMITTED_ALERT = "Thank you for submitting your pledge for item [item_code], please confirm your pledge by responding with your first name and last name.";
    String ITEM_CODE_STR = "[item_code]";
    String BID_INSTARCTION_STR = "Text your Pledge to [display_number] with the three letter code and pledge amount E.g. [amount_example_str]";
    String AMOUNT_EXAMPLE_STR = "[amount_example_str]";
    String DISPLAY_NUMBER = "[display_number]";
    String TRANSACTION_ID = "Transaction ID";
    String PAYFLOW_IS_AVAILABLE_ONLY_FOR_TICKET_PURCHASE = "PayFlow is only available for a ticket purchase. We are working on making it available for other sections soon.";

    String PURCHASE = "Purchase";
    String WINNER = "Winner";
    String POTENTIAL_WINNER = "Potential Winners";
    String OUTBID = "Outbid";
    String PAID_WITH_BIG_CASE = "Paid";

    String ALERTS_WHEN_BUY_IT_NOW_ITEM_PURCHASED = "Alerts when buy it now items are purchased";
    String ORDER_CONFIRMATIONS_FROM_MY_ATTENDEES = "Order confirmations from my attendees";
    String WEEKLY_SALES_REPORT = "Weekly Sales Report";
    String QUESTIONS_FROM_ATTENDEES = "Questions from attendees";

    String PAYMENT_PROCESSING_IS_NOT_SETUP = "Payment processing is not set up. Please notify the administrator.";
    String DONATION_NOT_ACTIVE = "Please activate this module to start accepting donation.";
    String RECURRING_PAYMENT_NOT_SUPPORTED_IN_SQUARE = "Recurring payment is not supported in square.";
    String DONATION_CHARGE_FAILED_MSG = "donation charges failed";
    String AMOUNT_TO_LARGE = "Credit card donations are limited to $999,999.99. For larger donations please contact the organization directly";
    String DONATION_THANKS = "Thank you for your donation.";

    String PASSTHROUGH = "passthrough";
    String USER_NOT_FOUND_FOR_STAFF_CHECKOUT = "User Not Found for staff checkout";
    String NOT_STAFF_USER = "You are not listed as staff for this event";
    String INCORRECT_PASSWORD = "Incorrect password, please try again.";
    String PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL = "This phone number is already associated with another email address";
    String PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_PARAMETER = "This phone number [phone_number] is already associated with another email address";
    String PHONE_NUMBER_ALREDY_WITH_EMAIL_PARAMETER = "[phone_number]";
    String EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER = "This email is already associated with another phone number";
    String EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER = "This email is already associated with [phone_number] phone number";
    String USER_ALREADY_EXIST = "User already exist";
    String USER_ALREADY_PRESENT_WITH_COUNTRY_CODE = "User Already Present with [country_code] as country code";
    String COUNTRY_CODE_FOR_USER_WITH_COUNTRY_CODE_OR_NOT = "[country_code]";
    String ITEM_ALREADY_PURCHASED = "Sorry, this item has already been purchased.";
    String BUY_IT_NOW_BID_CONTACTED_TO_COLLECT_PAYMENT = "You are submitting a Buy it Now bid. You will be contacted to collect payment.";
    String BID_SHOULD_BE_GREATER_THAN_STARTING_BID = "Item bid should be greater than starting bid";
    String AUCTION_NOT_ACTIVE = "Please activate this module to start accepting bids.";
    String BIDDING_HAS_ENDED_FOR_ITEM_WITH_ITEMCODE = "Bidding has ended for item {item_short_name}.";
    String MORE_THEN_ONE_USER_EXIST_WITH_THIS_EMAIL = "More than one user exist with same email";
    String PASSWORD_VALIDATION_FAILED = "Password should contain at least 1 uppercase, 1 lowercase, 1 special character (Valid special characters: !@#$%^&*+-_=), 1 number, no whitespaces and should be at least 8 characters in length";
    String EMAIL_NOT_MATCH_WITH_USER_ID = "email address not match with userd id [user_id]";
    String EMAIL_NOT_MATCH_WITH_USER = "email address not match with userd id";
    String USER_ID_NOT_MATCH_PARAM = "[user_id]";
    String BID_NOT_FOUND = "Could not find bid.";
    String BIDDER_NOT_FOUND = "Bidder not found";
    String PAYMENT_METHOD_REQUIRED = "Payment method is required.";
    String USER_BIDDERNUMBER_ALREADY_PRESENT = "Bidder number %d already registered with this email or phone number.";
    String BIDDER_NUMBER_NOT_ENANLED = "Bidder number is not enabled for this event";
    String COUNTRY_CODE_NOT_VALID = "Country code not valid.";
    String BID_INSTUCTION = "Bid here or text Your Bid To: [phone_number] with the item's three letter code and bid amount ex. [item_code_example]";
    String ITEM_CODE_EXAMPLE = "[item_code_example]";
    String CARD_UPDATED_SUCCESSFULLY = "Card Updated successfully";
    String PLEASE_LOGIN = "Please Sign In";
    String NOT_EVENT_HOST = "Not Event Host";
    String WELCOME_BIDDER_NUMBER_SMS_DONATION = "[first_name], welcome to the event! Please visit [auction_url] to bid online";
    String WELCOME_BIDDER_NUMBER_SMS = "[first_name], welcome to the event! Your bidder number is [bidder_number]. Please visit [auction_url] to bid online";
    String WINNING_BID_SINGLE_ITEM_PAYMENT_ENABLED = "CONGRATULATIONS. You have won item [item_short_name]. Please pay at [stripe_link] or contact the organization to pay in cash.";
    String PURCHASE_CHECKOUT_PAYMENT_DISABLED = "Thank you for purchasing item [item_short_name] ([item_code]) for [currency_symbol][amount].  Please see a staff member to check out.";
    String PURCHASE_CHECKOUT_PAYMENT_ENABLED = "Thank you for purchasing [item_short_name] ([item_code]), please complete your purchase by clicking this link [link] ";
    String BID_IS_REFUNDED = "Bid is already refunded, can not mark as distributed.";
    String AUCTION_REFUND_FAILED = "Unable to refund the auction bid.";
    String BID_ALREADY_REFUNDED = "Bid is already refunded.";
    String ONLY_PAID_BID_CAN_BE_REFUNDED = "Sorry, only paid bid can be refunded.";
    String CAN_NOT_DELET_ALREADT_PAID = "You cannot delete bid which have already been paid.";
    String PAYMENT_CONFIRMATION_REQUIRED = "Thank you for bidding for item [item_short_name] ([item_code]). Please visit this page to confirm your bid. [link] ";
    String CONFIRM_PURCHASE_PAYMENT_DISABLED = "Please confirm your bid for [item_short_name] ([item_code]) by responding with your name in the following format: FirstName LastName";
    String ERROR_IN_GENERATION_OF_TOKEN = "Error in generation of access token";
    String NOT_SALES_REP = "Not Sales Representative";
    String TICKETS_ARE_NOT_CURRENTLY_FOR_SALE_FOR_THIS_EVENT = "Tickets are not currently for sale for this event.";
    String REFUND_ORDER_NOT_PAID = "Refund order not paid";
    String REFUND_AMOUNT_SHOULD_NOT_GREATER_THEN_PAID = "The refund amount must be less than the paid amount.";
    String VIRTUAL_EVENTS_ARE_NOT_SUPPORT_WITH_RECURRING_EVENT = "Please convert event to non-recurring OR contact to support team to make it non-recurring event.";
    String NOT_ALLOW_TO_UPLOAD_ATTENDEES = "Attendees upload not allowed for free plan.";
    String SOME_OF_FIELDS_ARE_EMPTY_IN_ATTENDEE_CSV = "Some of the fields having empty field in upload CSV.";
    String EMAIL_FIELD_SHOULD_CONTAIN_SINGLE_VALUE = "Email field should contain single email value.";
    String ORDER_ID_HAVE_MUST_NUMBER_ONLY = "Order Id have must number only.";
    String UPLOAD_FILE_HEADER_NOT_CORRECT_FOR_ATTENDEE_CSV = "Upload file headers name are not correct or not in proper sequence. It should be Transaction Id,First Name,Last Name,Email.";
    String ATTENDEE_LIST_NOT_FOUND = "Attendee list not found.";
    String PLEDGE_INSTUCTION = "Pledge here or text Your Pledge To: [phone_number] with the item's three letter code and pledge amount ex. [item_code_example]";
    String MESSAGE_SERVICE_UNABLE_TO_SEND_MESSAGE = "Message service is unable to send message";
    String ONLY_ORGANIZER_TEAM_MEMBER_CAN_SUBSCRIBE_PLAN = "Only organizer admin or owner can subscribe to the plan";
    String MEMBER_CANNOT_SUBSCRIBE_PLAN = "Member are not allowed to subscribe the plan, only Admin/Owner can subscribe to the plan";
    String CHARGEBEE_CUSTOMER_NOT_FOUND = "Customer Not Found In Chargebee.";
    String WHITE_LABEL_URL_NOT_FOUND = "White Label URL not found";
    String CANNOT_PURCHASE_MORE_THEN_TWENTY = "Max 20 quantity can be purchase at a time";
    String RAFFLE_INSTRUCTION = "Submit your raffle tickets here or submit via text message to: [phone_number] with the item's three letter code and number of raffle tickets ex. [item_code_example]";
    String ORGANIZER_NOT_FOUND = "Organizer Not Found";
    String ORGANIZER_URL_NOT_FOUND = "Organizer URL not found or Its changed.";
    String USER_NOT_ALLOW_TO_UPDATE = "User not allow to update.";
    String USER_ALREADY_PRESENT_IN_TEAM = "User already present in team";
    String ORGANIZER_IS_UPDATED_SUCCESSFULLY = "Organizer is updated successfully";
    String NOT_ALLOW_TO_EDIT_ORGANIZER_PAGE = "Not authorized for edit organizer page";
    String CHARGEBEE_SUBSCRIPTION_NOT_FOUND = "Subscription Not Found In Chargebee.";
    String NOT_VALID_EMAIL = "Not a well-formed email address";
    String CONTACT_TO_YOU_SOON = "Thank you for writing to us, we will get back to you soon!";
    String NOT_SUPER_ADMIN = "Not Super Admin";
    String EVNET_NOT_ASSOCIATED_WITH_THIS_ORGANIZER = "Event is not associated with this organizer";
    String NO_UNSUBSCRIBEDUSERS_ARE_PRESENT = "There are no unsubscribed users for this event";
    String CAN_NOT_DELETE_EVENT = "{event_name} has more than 40 participants, due to the size of event it cannot be deleted, if this is a test event, please make sure that the event is marked as such.";
    String PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_EMAIL_PARAMETER = "This phone number is already associated with  [email_address] email address";
    String EMAIL_ADDRESS_PARAMETER = "[email_address]";
    String MEMBER_CANNOT_BE_MARKED_AS_BILLING_CONTACT = "Member can not be marked as billing contact";
    String CAN_NOT_UPDATE_BILLING_CONTACT = "You are not authorized to update Billing contact";
    String CAN_NOT_REMOVE_LAST_OWNER_FROM_BILLING_CONTACT = "Cannot remove last owner from Billing contact";
    String CAN_NOT_MARK_BILLING_CONTACT = "You are not authorized to add team members with Billing contact enabled";
    String CAN_NOT_REMOVE_OWNER_FROM_BILLING_CONTACT = "You are not authorized to remove owner from Billing contact";
    String CAN_NOT_DELETE_BILLING_CONTACT = "You are not authorized to delete Billing contact";
    String CAN_NOT_DELETE_LAST_OWNER_BILLING_CONTACT = "Cannot delete last owner or billing contact";
    String CAN_NOT_DELETE_OWNER = "You are not authorized to delete owner";
    String ADD_EMBED_WIDGET_SETTINGS_SUCCESSFULLY = "Add embed widget settings successfully";
    String ORGANIZER_OR_EVENT_ID_NOT_EXIST = "Organizer/Event id not exists";
    String WIDGET_SETTING_ALREADY_EXISTS = "Embed widget setting already exists";
    String WIDGET_SETTING_TYPE_NOT_EXISTS = "Embed widget setting type not exists";
    String WIDGET_SETTING_NOT_FOUND = "Embed widget setting not found";
    String NOT_AUTHORIZED_TO_CREATE = "Not Authorized To Create Integration";
    String WHITE_LABEL_EVENT_NOT_FOUND = "This white label event does not exist";
    String FAILED_TO_GET_USER_INFO = "Error while fetching users details";
    String FAILED_TO_GENERATE_USER_TOKEN = "Error while creating user token";
    String FAILED_TO_DELETE_SOLUTION_INSTANCE = "Error while delete solution instance";
    String FAILED_TO_RECONFIGURE_SOLUTION_INSTANCE = "Error while reconfigure solution instance";
    String FAILED_TO_GET_AUTH_CODE = "Error while generating authorization code";
    String DONATION_SUCCESSFUL = "Thank you for donating. Please confirm your donation here [link]";
    String TRANSACTION_CONFIG_NOT_FOUND = "Transaction conditional logic configuration missing for white label";
    String WHITELABEL_STRIPE_NOT_CONFIGURED = "Please connect stripe for whitelabel event.";
    String EVENT_NAME_ALREADY_EXIST = "Event name is already in use. Please try another name.";
    String EVENT_NAME_CAN_NOT_NULL = "Event name can not be empty";
    String CANNOT_CHANGE_ORGANIZER_AFTER_EVENT_PUBLISH = "Cannot change organizer after the event is published";
    String VIRTUAL_EVENT_ALREADY_CREATED = "Virtual event settings already created";
    String EVENT_CHALLENGE_CONFIG_NOT_FOUND = "Event Challenge Not Found.";
    String SETTING_NOT_ALLOWED = "This settings is not allowed for your event plan";
    String VIRTUAL_EVENT_SETTINGS_NOT_FOUND = "Virtual event settings not found.";
    String POPUP_ACTIVATE_TICKETING_MODULE = "Please confirm to activate Ticketing module.";
    String POPUP_FINISH_STRIPE_PAYMENTS = "Please finish setting up Stripe payment processing to start collecting payments.";
    String POPUP_FINISH_STRIPE_PAYMENTS_FOR_SELLING = "Please finish setting up Stripe payment processing to start selling tickets.";
    String POPUP_FINISH_STRIPE_PAYMENTS_FOR_DONATIONS = "Please finish setting up Stripe payment processing to start collecting donations.";
    String NEED_TO_PUBLISH_EVENT = "The [plan] plan is limited to [noOfItems] [itemSingular]. Please publish event to add more [itemPlural]";
    String CHARGEBEE_PAYMENT_FAILED = "Payment failed due to ${errorMsg}.";
    String HOST_EXCEPTION_PLAN = "[plan]";
    String NO_OF_ITEM = "[noOfItems]";
    String ITEM_SINGULAR = "[itemSingular]";
    String ITEM_PLURAL = "[itemPlural]";
    String COUNTER_EXCEEDED_CAP_USAGE_ITEMS = "The [plan] plan is limited to [noOfItems] [itemSingular]. Please upgrade your plan to add more [itemPlural].";
    String PRE_EVENT_ACCESS_IS_LIMITED = "Pre-event access is limited to [hours] hrs prior to the start of the event on the [plan] plan. Please upgrade your plan to add more Pre Event Access Days.";
    String HOURS = "[hours]";
    String INVALID_SESSION_DATE_TIME = "Please enter valid date/time format. Date is not in yyyy/MM/dd Or Time HH:mm format";
    String SESSION_NAME = "{SESSION_NAME}";
    String NETWORKING_RULE_NOT_FOUND = "Networking Rules not found!";
    String NETWORKING_RULE_ALREADY_EXISTS = "Networking Rule already exists.";
    String DUPLICATE_SESSION_DETAIL_ID_ENTRY = "Duplicate session detail id is not allowed.";
    String ALREADY_TICKET_TYPE_USED = "Can not remove this ticket type. Attendees have already registered using this ticket type";
    String NUMBER_OF_SESSION_PERMITTED = "{number_of_sessions_permitted}";
    String CAN_NOT_UPLOAD_EMPTY_FILE = "Can not upload empty file. Please add data and try again.";
    String UPLOAD_FILE_HEADER_NOT_CORRECT = "Upload file headers name are not correct or not in proper sequence";
    String SESSION_CAN_NOT_BE_DELETE = "This session cannot be deleted because broadcast is running. Please stop broadcast.";
    String INVALID_SESSION_FORMAT = "Invalid Session Format For Selected Area";
    String CAN_NOT_ALLOWED_FOR_CONCURRENT_SESSION = "Only allowed for concurrent sessions";
    String PARTICIPANT_QUESTION = "PARTICIPANT_QUESTION";
    String AUDIENCE_FILTER_ALREADY_EXISTS = "Audience filter already exist with same name";
    String AUDIENCE_FILTER_NOT_FOUND = "Audience Filter not found";
    String SPEAKER_ALREADY_EXIST = "Speaker already exist with same email";
    String EMAIL_IS_ALREADY_PRESENT = "Record for [email_address] email is already present.";
    String N0_SPEAKER_PROFILE_PRESENT = "No Speaker Profile Present";
    String SOCIAL_MEDIA_URL_IS_INVALID = "social media URL is not valid, please enter the valid social media URL.";
    String FIRST_NAME_NOT_BE_NULL = "First name must be not null OR First name must be less than 50 characters.";
    String LAST_NAME_NOT_BE_NULL = "Last name must be not null OR Last name must be less than 50 characters.";
    String EMAIL_NOT_BE_NULL = "Email must be not null OR Email must be less than 75 characters.";
    String EMAIL_NOT_VALID = "This is not valid email address.";
    String EMAIL_ALREADY_IN_CSV_FILE = "[email_address] Already present in csv file.";
    String EMAIL_ALREADY_DB = "[email_address] Already exist with same email";
    String TITAL_NOT_BE_NULL = "Title must be not null OR Title must be less than 255 characters.";
    String COMPANY_MUST_BE_NOT_NULL = "Company must be not null Company must be less than 255 characters.";
    String LINKDIN_URL_NOT_VALID = "LinkedIn URL is not valid";
    String INSTAGRAM_URL_NOT_VALID = "Instagram Handle URL is not valid";
    String TWITTER_URL_NOT_VALID = "Twitter Handle URL is not valid";
    String SESSION_IDS_INVALID = "Sessions field contains invalid IDs. Only numeric values are allowed.";
    String EMAIL_ALREADY_EXIST = "[email_address] Email Already exist.";
    String TICKETING_SETTING_NOT_FOUND = "Ticketing setting not found";
    String PRIMARY_SESSION_INVALID = "Primary Sessions field contains invalid session IDs that do not belong to this event.";
    String SECONDARY_SESSION_INVALID = "Secondary Sessions field contains invalid session IDs that do not belong to this event.";
    String PRIMARY_SESSION_DUPLICATE = "Primary Sessions field contains duplicate session IDs";
    String SECONDARY_SESSION_DUPLICATE = "Secondary Sessions field contains duplicate session IDs";
    String PRIMARY_SECONDARY_CONFLICT = "For the same session, a speaker cannot be both Primary and Secondary at the same time.";

    String STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT = "Please send ${date} value in 'yyyy/MM/dd HH:mm:ss' format.";

    //COUPON CODE
    String MORE_TICKET_COUPON_USED = "More coupon is used then set";
    String TICKET_COUPON_PERCENT_GREATER_100 = "Ticketing coupon percentage should not be greater then 100";
    String CAN_NOT_NULL_XX_RELATIVE_TIME = "Please fill the recurring relative start and end time.";
    String TICKET_COUPON_ALREADY_EXIST = "Ticketing coupon already exist";
    String TICKET_COUPON_USED = "A discount code cannot be deleted once it has been used.";
    //-----Exhibitor CSV fields------

    String PRO_EXHIBITOR = "Pro_Exhibitor";
    String ALLOW_EXHIBITOR_TO_LIVE_STREAM = "Allow_Exhibitor_to_Live_Stream";
    String NUMBER_OF_TEAM_MEMBERS_PERMITTED = "Number_Of_Team_Members_Permitted";
    String ALLOW_REPS_TO_MANUALLY_ADD_LEADS = "Allow_Reps_To_Manually_Add_Leads";
    String ALLOW_REPS_TO_SEE_OTHER_REPS_LEADS = "Allow_Reps_To_See_Other_Reps_Leads";
    String ALLOW_REPS_TO_AUTO_GENERATE_LEADS = "Allow_Reps_To_Auto_Generate_Leads";
    String SHOW_DOWNLOAD_LEAD_BUTTON_IN_MY_BOOTH = "Show_Download_Lead_Button_In_My_Booth";
    String COUNT_EVERY_BOOTH_VISITOR_AS_A_LEAD = "Count_Every_Booth_Visitor_As_A_Lead";
    String BOOTH_SIZE = "Booth_Size";
    String PREFERRED_EXHIBITOR = "Preferred_Exhibitor";
    String INCLUDE_IN_EXPO_CAROUSEL = "Include_In_Expo_Carousel";
    String CAROUSEL_DISPLAY_TIME_IN_SECONDS = "Carousel_Display_Time_in_Seconds";
    String ENABLE_CHAT = "Enable_Chat";
    String ENABLE_POLLS = "Enable_Polls";
    String ENABLE_QA = "Enable_Q&A";
    String LINK_TO_SITE = "Link_To_Site";
    String CURRENCY = "Currency";
    String BOOTH_OWNER_FIRST_NAME = "Booth_Owner_First_Name";
    String BOOTH_OWNER_LAST_NAME = "Booth_Owner_Last_Name";
    String BOOTH_OWNER_EMAIL = "Booth_Owner_Email";
    String ADD_ONLY_YES_AND_NO = "Add only YES OR NO.";
    String NOT_REGISTERED_FOR_THIS_EVENT = "Looks like you are not registered for this event. Make sure the email [email_address] is the correct one or register by clicking the button below";
    String TICKET_TYPE_DOES_NOT_GRANT_ACCESS = "Your ticket type does not grant access to this session";
    String AUTO_CREATE_MODERATOR = "[first_name] [last_name] is now a moderator for this session";
    String FIRSTNAME = "[first_name]";
    String LASTNAME = "[last_name]";
    String SPEAKER_EMAIL_CHANGE_NOT_HAVE_ATTENDEE_ACCESS = "This new speaker has no attendee access yet, please select a ticket type.";
    String VIRTUAL_PORTAL_IMAGE_NOT_FOUND = "Virtual portal image not found";
    int RECORD_SIZE_FOR_SHOW_USERDATA = 10;
    String SPEAKER_ALREADY_EXIST_WITH_EMAIL = "Speaker already exist with [email_address]";
    String NOT_FOUND_BADGES = "Badge not found";
    String BADGES_ADDED_SUCCESSFULLY = "Badge added successfully";
    String BADGES_UPDATED_SUCCESSFULLY = "Badge updated successfully";
    String UTM_SOURCE = "utm_source";
    String UTM_MEDIUM = "utm_medium";
    String UTM_TERM = "utm_term";
    String UTM_CAMPAIGN = "utm_campaign";
    String UTM_CONTENT = "utm_content";
    String UTM_REFERRER = "utm_referrer";
    String IS_STAFF = "Is Staff";
    String IS_ADMIN = "Is Admin";
    String AMPERSAND = "&";
    String TRACKING_URL = "trackingUrl";
    String EVENT_TICKET_NUMBER = "ticketNumber";
    String orderNumber = "orderNumber";
    String STATUS_DELETED = "DELETE";
    String BADGES_DELETED = "Badge deleted successfully";
    String FROM_WITH_SPACE = " from ";
    String UNPAID_ORDER_TEXT = "unpaidOrderText";
    String JOINORVIEWEVENTLABEL = "joinOrViewEventLabel";
    String TICKET_HOLDER_FIRST_NAME = "ticket_holder_first_name";
    String TICKET_HOLDER = "ticket holder";
    String TICKET_BUYER = "ticket buyer";
    String EVENT_UC_NAME = "event_name";
    String EVENT_UC_URL = "event_url";
    String START_DATE = "event_startDate";
    String START_TIME = "event_startTime";
    String END_TIME = "event_endTime";
    String END_DATE = "event_endDate";
    String OPEN_CONTACT_FORM = "openContactForm";
    String EVENT_TIME_ZONE = "event_TimeZone";
    String TO_BE_ANNOUNCED = "To Be Announced";
    String EVENT_LOCATION = "event_location";
    String LOUNGE_LINK = "lounge_link";
    String LOUNGES_NAME="lounge_name";
    String IS_APPROVED="isApproved";
    String IS_DENIED="isDenied";
    String DENIED_STATUS_REASON="denied_status_reason";
    String IS_PENDING="isPending";
    String EVENT_SLOGAN = "event_slogan";
    String IS_VIRTUAL_EVENT = "isVirtualEvent";
    String IS_HOLDER_TICKET = "isHolderTicket";
    String IS_BUYER_TICKET="isBuyerTicket";
    String CREATE_EVENT = "create_Event";
    String SLASH_EVENTS = "/events";
    String STAFF_EMAIL = "staff_email";
    String ITEMS_NEW = "Items";
    String HEADER_IMAGE = "headerImage";
    String IMAGE_FOOTER = "image_footer";
    String IS_ONLINE_EVENT="isOnlineEvent";
    String LOWER_CASE_SUCCESS = "success";
    String SCORE = "score";
    String NOT_FOUND_BADGES_IMAGE = "Badge image not found";
    String DUPLICATE_BADGES_NAME = "Badge name should not be duplicate, Please enter unique badge name";
    String MERGE_TAG_SESSION_START_DATE = "session_startDate";
    String MERGE_TAG_SESSION_START_TIME = "session_startTime";
    String MERGE_TAG_SPEAKER_NAMES = "speaker_names";
    String MERGE_TAG_USER_UC_NAME = "user_name";
    String MERGE_TAG_SESSION_LOCATION = "session_location";
    String MERGE_TAG_REVIEWER_NAME = "reviewer_name";
    String MERGE_TAG_REVIEW_PORTAL_URL = "review_portal_url";
    String MERGE_TAG_MAGIC_LINK = "MagicLink";
    String VIEW_TICKET_TEXT = "viewTicketsText";
    String VIEW_TICKET_MAGIC_LINK = "viewTicketMagicLink";
    String DEFAULT_VIEW_TICKET_TEXT = "Thank you for registering for ${event_name}. Use the button below to access your tickets.";

    String TIME = "Time";
    String TIME_SMALL = "time";
    String LIVE_USERS = "Live";
    String RECORDING_USERS = "Recorded";
    String LIVE_STREAM_SETTINGS = "${settingName} setting is not allowed for this ${plan_name} plan. Kindly, upgrade your plan";
    String UNSUBSCRIBE_LINK = "unsubscribe_link";
    String UNSUBSCRIBE_LINK_VALUE = "/u/unsubscribe?key=";
    String WAITLIST_TICKET_RELEASE_MESSAGE="waitListTicketReleaseMessage";
    String MAGIC_LINK = "magic_link";
    String EVENT_ADDRESS = "eventAddress";
    String EVENT_TASK_CUSTOM_MESSAGE = "event_task_custom_message";
    String TASK_BUTTON_DESCRIPTION = "task_button_description";
    String TASK_NAME="task_name";
    String NAME_OF_SESSION="session_name";
    String TASK_URL = "task_url";
    String PORTAL_MY_TALK_TASK_LINK = "/portal/mytalks#mytasks";
    String PORTAL_EXHIBITOR_TASK_LINK = "/portal/mybooth#mytasks";
    String MANUAL = "MANUAL";

    String HIDE_EVENT_DATES = "hideEventDates";
    String PURCHASE_DATE = "purchase_date";
    String OWN_EVENT_LABEL = "ownEventLabel";
    String END_MESSAGE_LABEL1 = "endMessageLabel";
    String GET_STARTED_LABEL1 = "get_started_label";
    String ADMIN_TEXT = "adminText";
    String BODY_OF_QUERY = "body_of_query";
    String MEETING_SPACE_REQUEST = "Meeting request";
    String MEETING_CANCEL_TITLE = "Meeting Cancelled";
    String MEETING_REQUEST_ATTENDEE = "Meeting Request from an Attendee in";
    String CONNECTION_SPACE_REQUEST = "Connection request";
    String REQUEST_TYPE = "requestType";
    String IMAGE_URL = "imageUrl";
    String FIRM_NAME = "firmName";
    String CANNOT_SET_LESS_THAN_EMAILS_USED = "Event has already used %d engage email blasts, we can't set less than that.";

    String BADGES_ID = "id";
    String BADGES_NAME = "badgeName";
    String BADGES_UPDATED_AT = "updatedAt";
    String DUPLICATE_TICKETING_TYPE_ID = "Ticket type [ticketing_type_name] is already selected in [badge_name] badge";
    String TICKETING_TYPE_NAME = "[ticketing_type_name]";
    String BADGE_NAME = "[badge_name]";

    String CRITERIA_ALREADY_ASSOCIATED_WITH_CERTIFICATE = "Criteria {criteria_name} is already selected in {certificate_name} certificate.";
    String CRITERIA_NAME_CURLY_BRACES = "{criteria_name}";
    String CERTIFICATE_NAME_CURLY_BRACES = "{certificate_name}";


    String SEND_EMAIL_MAX_LIMIT_REACHED = "You have already used your {EMAIL_BLASTS} email blasts";
    String EVENT_CONNECTED_TO_PAYMENT_GATEWAY = "Event is connected to ${PAYMENT_GATEWAY} payment gateway.";
    String EVENT_CONNECTED_TO_PAYMENT_GATEWAY_AND_CUSTOMER_CREATED = "Event {eventName} is connected to {PAYMENT_GATEWAY} payment gateway and {PAYMENT_GATEWAY} customer already created.";

    String ORGANIZER_MOVED_INTO_WL_ACCOUNT = "{organizerName} is now part of {wlName}.";
    String ORGANIZER_DISCONNECTED_FROM_WL = "{organizerName} is disconnected from Enterprise/WL {wlName} account.";
    String ORGANIZER_MOVED_FROM_WL_TO_OTHER_WL = "{organizerName} is moved from this {fromWlName} account to this Enterprise/WL {wlName} account.";
    String CURLY_BRACES_ORGANIZER_NAME = "{organizerName}";
    String CURLY_BRACES_WL_NAME = "{wlName}";
    String CURLY_BRACES_FROM_WL_NAME = "{fromWlName}";

    String TYPES = "types";
    String DEFAULT_EXHIBITOR_FIELDS = "{\"tabList\":[{\"key\":\"LIVE\",\"value\":\"Live\"},{\"key\":\"VIDEO\",\"value\":\"Video\"},{\"key\":\"COMPANY_DETAILS\",\"value\":\"Company Details\"},{\"key\":\"OFFER\",\"value\":\"Offer\"},{\"key\":\"PRODUCTS\",\"value\":\"Products\"}],\"requestMeeting\":{\"label\":\"Request More Information\",\"hide\":false,\"buttonColor\":\"\",\"buttonTextColor\":\"\",\"confirmationMessage\":\"Your request has been received. We will be in touch!\",\"requestMeetingURL\":\"\"},\"companyRepresentativeTitle\":\"Company Representatives\"}";

    String GAMIFICATION_CHALLENGE = "You have created ${countOfChallenge} gamification challenges under this ${plan_name} plan. Only 1 Standard and 1 Scavenger Hunt challenge are allowed under this plan. Please update your organizer plan or delete extra challenges from gamification.";


    String PREFIX_BUYER = "Buyer ";
    String PREFIX_HOLDER = "Holder ";
    String HEADER_ORDER_NUMBER = "Order Number";
    String TICKET_OR_ADDON_NAME = "Ticket / Add On Name";
    String CHECK_IN_SOURCE = "Check In Source";
    String TRANSACTION_TYPE = "Transaction Type";
    String TICKET_NUMBER = "Ticket Number";
    String TICKET_TRANSFER_ENABLED = "Ticket Transfer Enabled";
    String TICKET_BARCODE_ID = "Ticket Barcode Id";
    String BADGE_PRINTED = "Badge Printed";

    String GUEST_TICKET = "Guest Ticket";
    String TABLE_NUMBER = "Table Number";
    String TICKET_STATUS = "Ticket Status";
    String NUMBER_OF_TICKETS = "Number of tickets";
    String PRICE = "Price";
    String PAID_AMOUNT = "Paid amount";
    String REFUNDED_AMOUNT = "Refunded amount";
    String FEES = "Fees";
    String SALES_TAX = "Sales tax";
    String VAT = "VAT";
    String ACCESS_CODE_USED = "Access code used";
    String COUPON_CODE_USED = "Coupon code used";
    String COUPON_DISCOUNT_AMOUNT = "Coupon discount amount";
    String PAYMENT_TYPE = "Payment Type";
    String TRACKING_LINK = "Tracking Link";
    String HOLDER_HEADER = "Holder";
    String PURCHASE_DATE_TIME = "Purchase Date/Time";
    String CHECKED_IN_STATUS = "Checked In Status";
    String CHECKED_IN_DATE_TIME = "Checked In Date/Time";
    String PAYMENT_STATUS = "Payment Status";
    String ORDER_STATUS = "Order Status";
    String STRING_TRACKING_LINK = "Tracking link";
    String STRING_ACCESS_CODE = "Acccess code";
    String STRING_DISCOUNT_CODE = "Discount code";
    String STRING_ADD_ONS = "Add-ons";
    String STRING_SESSION_REGISTRATION = "Session Registration";
    String STRING_EQUAL_TO_OPERATOR = " = ";
    String STRING_NOT_EQUAL_TO_OPERATOR = " <> ";
    String STRING_LIKE_OPERATOR = " LIKE ";
    String STRING_NOT_LIKE_OPERATOR = " NOT LIKE ";
    String STRING_CHECK_IN_DATE = "Check In Date";
    String STRING_REGISTRATION_TIME = "Registration Time";
    String STRING_LAST_UPDATE_TIME = "Last update Time";
    String STRING_IS_BEFORE = "IS_BEFORE";
    String STRING_IS_AFTER= "IS_AFTER";
    String STRING_IS_EQUAL_TO= "IS_EQUAL_TO";
    String STRING_IS_EMPTY= "IS_EMPTY";
    String STRING_IS_NOT_EMPTY= "IS_NOT_EMPTY";
    String STRING_IS_BETWEEN= "IS_BETWEEN";
    String STRING_TODAY= "TODAY";
    String STRING_YESTERDAY= "YESTERDAY";
    String STRING_THIS_WEEK= "THIS_WEEK";
    String STRING_LAST_WEEK= "LAST_WEEK";
    String STRING_LAST_MONTH= "LAST_MONTH";
    String REFUND_DATE_TIME = "Refund Date/Time";
    String NOTES = "Notes";
    String HEADER_UTM_SOURCE = "UTM Source";
    String HEADER_UTM_MEDIUM = "UTM Medium";
    String HEADER_UTM_CAMPAIGN = "UTM Campaign";
    String HEADER_UTM_TERM = "UTM Term";
    String HEADER_UTM_CONTENT = "UTM Content";
    String HEADER_UTM_REFERRER = "UTM Referrer";
    String HEADER_ATTENDED = "Attended";
    String HEADER_BLOCKED = "Blocked";
    String COUNTRY_TICKET_PURCHASED_IN = "Country Ticket Purchased In";
    String ADDON = "Add On";
    String ADD_ON = "addOn";
    String TICKET_NAME = "Ticket";
    String DONATION = "Donation";

    String TICKET_EXCHANGED = "Ticket Exchanged";

    String HUBSPOT_ADD_CONTACT_MSG = "Check your inbox for more information about Accelevents.";
    String ADDRESS_SEPARATOR = "|||||";

    String PAYMENT_FROM_STAFF_CHECK_IN = "paymentFromStaffCheckIn";
    String PURCHASE_TICKET = "purchaseTicket";
    String SPEAKER = "SPEAKER";
    String SOME_RECORDS_ARE_NOT_UPDATED = "Some records are not updated, Please check log details";
    String CAPITAL_EVENTURL = "EventURL";
    String ATTENDEE_DETAILS = "attendeeDetails";
    String IMAGE_LOWER = "image";
    String SCRIPT = "script";
    String PAGE = "page";
    String FILE_TYPE = "file_type";
    String WIDTH="width";
    String HEIGHT = "height";
    String HTML = "html";
    String PNG= "png";
    String DOT_PNG=".png";
    String DOT_CSV = ".csv";
    String DOT_PDF = ".pdf";
    String DOT_ZIP = ".zip";
    String DOT_PKPASS = ".pkpass";

    String UNDERSCORE_CHAT = "_chat";
    String SENDER_WITH_CURLY_BRACES = "{SENDER}";
    String B_TD_TR = "</b></td></tr>";
    String T_D = "</td>";
    String T_BODY_T_TABLE = "</tbody></table>";
    String ORGANIZER_TAX_ID = "Organization Tax ID: ";
    String ORGANIZATION_TAX_ID = "organization_tax_id_text";
    String CONFIG = "config";
    String INVAILD_ACTION = "invalid Action ";
    String INVALID_TRIGGER = "Invalid Trigger ";
    String SHOULD_NOT_BE_LESS_THAN = " should not be less than ";
    String NOT_GREATER_THAN = " and not greater than ";
    String RECURRING_EVENT_DATE = "recurring_event_date";
    String SUCCEEDED = "succeeded";
    String SQUARE = "SQUARE";
    String CAPTURED = "CAPTURED";
    String CREDIT_CARD_PROCESSING_FEES = "CreditCardProcessingFees";
    String ORGANISATION_NAME = "Organisation Name (As per VAT Registration)";
    String VAT_ID = "VAT ID";

    String ORGANIZER_NAME = "organizerName";
    String Organization_URL = "organizationUrl";
    String Owner_Name = "ownerName";
    String Owner_Email = "ownerEmail";

    String NEXT_EVENT = "nextEvent";

    String NEXT_EVENT_DATE = "nextEventDate";
    String NETWORKING_LOUNGE_DELETED_BY_THE_ADMIN = "Networking Lounge Deleted By The Admin. Please Refresh The Page And Try Again.";

    int FILE_NAME_MAX_LENGTH = 100;
    String LEADS_CSV_FILE_SUFFIX = " Leads.csv";
    String EXPO_SUMMERY_CSV_FILE_SUFFIX = " Exhibitor.csv";

    String POLLS_CSV_FILE_SUFFIX = " Polls.csv";

    String QA_SUMMERY_CSV_FILE_SUFFIX = " Q&A Summary.csv";

    String YEAR = "year";
    String MONTH = "month";
    String DAY = "day";
    String HOUR = "hour";
    String MINUTE = "minute";
    String SECOND = "second";
    String AGO = " ago";
    String SUFFIX_S = "s";

    String ROOM = "room";

    String RATE_LIMIT = "rate_limit_";
    String CLOUDINARY_IMAGE_PREFIX = "cloudinary_image_prefix";
    String DEFAULT_AE_IMAGES = "default_ae_images";
    String MOBILE_APP_UI_CONFIG_MASTER = "mobile-app-ui-config-master";
    String MOBILE_APP_UI_CONFIG = "mobile-app-ui-config";

    enum CustomCSSTargetType {

        LANDING_PAGE("Landing Page"),
        TICKETING_FLOW("Ticketing Flow"),
        VIRTUAL_EVENT_HUB_LOBBY("Virtual Event Hub Lobby"),
        VIRTUAL_EVENT_HUB_LOUNGES("Virtual Event Hub Lounges"),
        VIRTUAL_EVENT_HUB_NETWORKING("Virtual Event Hub Networking"),
        VIRTUAL_EVENT_HUB_EXHIBITORS("Virtual Event Hub Exhibitors"),
        VIRTUAL_EVENT_HUB_SESSIONS("Virtual Event Hub Sessions"),
        GENERAL("General"),
        WIDGET_TICKETS("Widget Tickets"),
        WIDGET_BUTTONS("Widget Buttons"),
        WIDGET_COUNTDOWN("Widget Countdown"),
        WIDGET_AGENDA("Widget Agenda"),
        WIDGET_SPEAKERS("Widget Speakers"),
        WIDGET_DONATE("Widget Donate"),
        WIDGET_SINGLE_PAGE_REGISTRATION("Widget Single Page Registration"),
        WIDGET_SESSION_APPROVAL("Widget Session Approval"),
        WIDGET_EXHIBITOR_APPROVAL("Widget Exhibitor Approval");

        private String targetType;

        CustomCSSTargetType(String targetType) {
            this.targetType = targetType;
        }

        public String getTargetType() {
            return targetType;
        }
    }

    String CSS_EXTENSION = ".css";

    String T_SLASH_BUY = "/T/buy/";
    String U_SLASH_CHECKOUT = "u/checkout/";
    String STATUS_UPDATED_SUCCESSFULLY = "Status updated successfully";

    String HEADER_TEXT = "headerText";
    String HEADER_DESCRIPTION = "headerDescription";
    String ACCEPT_BTN_URL = "acceptBtnUrl";
    String REJECT_BTN_URL = "rejectBtnUrl";
    String TOTAL = "total";
    String WELCOME_TO = "Welcome to ";
    String IS_LIVE_TOMORROW = " is live tomorrow!";
    String GET_STARTED = "getStarted";
    String FACEBOOK_SHARE = "facebookShare";
    String TWITTER_SHARE = "twitterShare";
    String INSTAGRAM_SHARE = "instagramShare";
    String LINKEDIN_SHARE = "linkedinShare";
    String IS_FACEBOOK_URL = "isFacebookUrl";
    String IS_TWITTER_URL = "isTwitterUrl";
    String IS_LINKEDIN_URL = "isLinkedinUrl";
    String IS_INSTAGRAM_URL = "isInstagramUrl";
    String IS_UNSUBSCRIBE = "isUnsubscribe";
    String ORGANIZER_LOGO = "orgLogo";
    String ORG_LOGO = "${orgLogo}";
    String MODULES = "modules";
    String IS_HELP_CENTER = "isHelpCenter";
    String IS_PRIVACY_POLICY = "isPrivacyPolicy";
    String PAYMENT_TYPES = "payment_type";
    String ORDER_PDF_LINK = "order_pdf_link";
    String EVENT_ORGANIZER_NAME = "event_organizer_name";


    String HELP_CENTER = "helpCenter";
    String PRIVACY_POLICY = "privacyPolicy";
    String PURCHASER_EMAIL = "purchaser_email";
    String PURCHASER_NAME = "purchaser_name";
    String PURCHASER_LAST_NAME = "purchaser_last_name";
    String QR_CODE="QR_code";
    String OLD_TICKET_TYPE_NAME="old_ticket_type_name";
    String NEW_TICKET_TYPE_NAME="new_ticket_type_name";
    String YOUR_TICKET_FOR = "Your Tickets for ";
    String ADMIN = "Admin";
    String DONATE_AMOUNT = "donateAmount";
    String NO_USER_PRESENT_WITH_EMAIL_OR_PHONE_NUMBER = "No user present with email or phone number: ";
    String AUCTION_PAYMENT = "auctionPayment";

    interface SENDER {
        String SENDER_NAME = "Sender_Name";
        String SENDER_ADDRESS = "Sender_Address";
        String SENDER_CITY = "Sender_City";
        String SENDER_STATE = "Sender_State";
        String SENDER_ZIP = "Sender_Zip";
    }

    interface AccelEventAddress {
        String ACCELEVENTS_INC = "Accelevents, Inc., ";
        String STREET_OR_LANDMARK = "One Boston Place, Suite 2600,";
        String BOSTAN = "Boston,";
        String MA = "MA";
        String ZIP_CODE_02109 = "02108";

    }

    Long DEMO_ORDER_NUMBER = 111l;
    Long DEMO_TICKET_NUMBER = 222l;

    Long FREE_PLAN_ID_CONSTANT = 4L;
    String DEMO_TICKET_NAME = "TicketTypeName";
    Long DEMO_ASSIGNED_NUMBER = 333l;


    String NOT_ALLOWED_TO_ACCESS_VIRTUAL_EVENT_PAGE = "The event format is In-Person and does not allow access to the Virtual Event Hub";
    String DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_EVENT_START = "After the event has started, does not allow to change the event format.";
    String DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_PRE_EVENT_START = "After the pre-event access start, does not allow to change the event format.";
    String SLASH_SEND = "/send";
    String CONTACTS = "contacts";

    String NOT_POSSIBLE_TO_ACTIVATE_AUTOPLAY = "It's not possible to activate Autoplay before uploading a video";

    String SELF_SIGN_UP = "Self Sign Up";
    String ORGANIZER_PAGE = "Organizer Page";
    String ENTERPRISE_PAGE = "Enterprise Page";
    String MY_PROFILE = "My Profile";

    String EVENT_CREATED = "EVENT_CREATED";

    String ORGANISER_CREATED = "ORGANIZER_CREATED";
    String ADD_USER = "ADD_USER";
    String LOG_IN = "LOG_IN";
    String ENTERPRISE_NEW = "Enterprise";
    String INTEGRATION = "INTEGRATION";
    String INTEGRATIONS = "integrations";
    String EVENT_NEW = "Event";
    String HUBSPOT = "HubSpot";
    String MARKETO = "Marketo";
    String SALESFORCE = "Salesforce";
    String CVENT = "Cvent";
    String ZAPIER = "Zapier";
    String CUSTOM_LANDING_PAGE_JS = "Custom Landing Page JS";
    String CUSTOM_EVENT_HUB_JS = "Custom Event Hub JS";
    String STRIPE_NEW = "Stripe";
    String SQUARE_NEW = "Square";
    String TICKET_ID = "eventTicketId";
    String TICKET_ORDER_ID = "orderId";
    String TICKET_BARCODE = "barcode";
    String STATUS_CODE = "statusCode";

    String PAYMENT_METHOD_TYPE = "paymentMethodType";

    String ORDER_ID = "ORDER_ID";

    String TICKET_DOES_NOT_ALLOW_LOUNGES = "Your registration does not allow access to Lounges";
    String TICKET_DOES_NOT_ALLOW_EXPO = "Your registration does not allow access to Exhibitor Booths";
    String NOT_FOUND_KIOSK_MODE_CHECK_IN_DETAIL = "Kiosk check-in detail not found";
    String ALREADY_ADDED_KIOSK_DETAIL = "You have already added kiosk mode check-in detail";
    String FEATURE_NOT_AVAILABLE_FOR_FREE_PLAN = "Kindly upgrade your plan to use this feature";

    String ACTION = "Action";
    String ACTIVITY_TIME = "Activity Time";
    String LINK = "Link";

    String VIEWED_PEOPLE_STRING = "viewed a people";
    String VIEWED_ATTENDEE_STRING = "viewed an attendee";

    String TRUE = "TRUE";
    String FALSE = "FALSE";
    String CUSTOM_TRUE = "CUSTOM_TRUE";
    String CUSTOM_FALSE = "CUSTOM_FALSE";
    String CHECKED = "Checked";
    String UNCHECKED= "Unchecked";
    String PROJECT = "project";
    String IST = "ist";
    String IAT = "iat";
    String EXP = "exp";

    String ATTENDEE_REG_INTRO_DESC = "This registration form will allow you to request ${requestType} access to our upcoming event, “${eventurl}” Please read the information below before filling in your information in the form!";
    String ATTENDEE_REG_INTRO_TITLE = "Welcome to ${requestType} registration";
    String REVIEWER_REG_INTRO_DESC = "This application will allow you to apply as a reviewer in our upcoming event, “${eventurl}” Please read the information below before filling in your information in the form!";
    String REVIEWER_REG_INTRO_TITLE = "Welcome to Reviewer Application";

    String EVENT_ADMIN = "Event Admin";
    String EVENT_BILLING_CONTACT = "Event Billing Contact";
    String EVENT_CREATOR = "Event Creator";
    String GRAPHQL_TYPEID = "typeId";

    String DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR = "#406AE8";
    String HUB_NAVIGATION_TEXT_COLOR = "#FFFFFF";
    String DEFAULT_HUB_MAIN_BACKGROUND_COLOR = "#F7F7FA";
    String HEX_COLOR_1F1F1F = "#1F1F1F";
    String HEX_COLOR_161616 = "#161616";
    String MUX_ASSERT_CREATED_TIME = "created_at";
    String MUX_ASSERT_VIDEO_DURATION = "duration";
    String MEETING_START_TIME = "meetingStartTime";
    String MEETING_END_TIME = "meetingEndTime";

    String CHECKIN_CHECKOUT_DATE = "Check-In/Check-Out Date";
    String CHECKIN_CHECKOUT_STATUS = "Check-In/Check-Out Status";
    String CHECKIN_CHECKOUT_BY_USER = "Check-In/Check-Out By User";

    String SRT_EXT = "srt";
    String VTT_EXT = "vtt";

    String FIELDS = "fields";
    String UNIQUE_DOWNLOADS = "Unique Downloads";
    String DOCUMENT_DOWNLOADED_LIST = "Document Downloaded";
    String TOTAL_DOCUMENT_DOWNLOADED = "Total Document downloaded";

    String MEETING_START_DATE_TIME="Meeting Start Date Time";
    String MEETING_END_DATE_TIME="Meeting End Date Time";
    String MEETING_SENDER_NAME="Sender Name";
    String SENDER_EMAIL="Sender Email";
    String SENDER_COMPANY="Sender Company";
    String MEETING_RECEIVER_NAME="Receiver Name";
    String RECEIVER_EMAIL="Receiver Email";
    String RECEIVER_COMPANY="Receiver Company";
    String EQUIVALENT_TIMEZONE="Equivalent Timezone";
    String SCHEDULED_BY="Scheduled By";
    String DECLINED_BY="Declined By";

    enum ExpoLayout {
        LOGO("logoOnly"),
        IMAGE("imageOnly"),
        LOGO_AND_IMAGE("bothLogoAndImage");

        private String layout;
        private static final Map<String, ExpoLayout> expoLayoutMap;

        static {
            Map<String, ExpoLayout> concurrentHashMap = new ConcurrentHashMap<>();
            for (ExpoLayout instance : ExpoLayout.values()) {
                concurrentHashMap.put(instance.getLayout(), instance);
            }
            expoLayoutMap = Collections.unmodifiableMap(concurrentHashMap);
        }

        private ExpoLayout(String layout) {
            this.layout = layout;
        }

        public String getLayout() {
            return layout;
        }

        public static ExpoLayout getNameByValue(String value) {
            return expoLayoutMap.get(value);
        }
    }

    enum VirtualEventHubDisclaimer {
        AGREE("agree"),
        DISAGREE("disagree");

        private String status;

        VirtualEventHubDisclaimer(String status) {
            this.status = status;
        }

        public String getStatus() {
            return status;
        }
    }

    enum QuestionAnswerType {
        SESSION("SESSION"),
        EVENT("EVENT"),
        EXPO("EXPO");

        private String type;

        private static final Map<String, QuestionAnswerType> questionAnswerTypeMap;

        QuestionAnswerType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }

        static {
            Map<String, QuestionAnswerType> concurrentHashMap = new ConcurrentHashMap<>();
            for (QuestionAnswerType instance : concurrentHashMap.values()) {
                concurrentHashMap.put(instance.getType(), instance);
            }
            questionAnswerTypeMap = Collections.unmodifiableMap(concurrentHashMap);
        }

        public static QuestionAnswerType getNameByValue(String value) {
            return questionAnswerTypeMap.get(value);
        }
    }

    enum SpeakerOnBoardingStatus {
        REGISTERED,
        INVITED,
        ONBOARDED

    }

    String SPEAKER_REG_DEFAULT = "{\"header\":\"Call For Sessions\",\"description\":\"We are now accepting session proposals for our upcoming event.\",\"registrationButtonText\":\"Submit Session\"}";
    String EXHIBITOR_REG_DEFAULT = "{\"header\":\"Exhibitor Registration\",\"description\":\"Interested in a booth for our upcoming event? Submit your information for review here.\",\"registrationButtonText\":\"Register as an Exhibitor\"}";
    String REVIEWER_REG_DEFAULT = "{\"header\":\"Apply as Reviewer\",\"description\":\"We are now accepting reviewers applications for our upcoming event.\",\"registrationButtonText\":\"Apply\"}";

    String PARTIAL_PAYMENT_OVER_DUE = "you have to pay only ${amount} amount";
    String PARTIAL_PAYMENT_ALREADY_DONE = "you have already payed ${amount} amount";

    String GAMIFICATON_VALIDATION_FOR_PLAN = "{} not available for this plan.";

    String SHOW = "show";
    String HIDE = "hide";
    String NETWORKING_LOUNGE_DOCUMENT_NAME_NOT_FOUND = "${documentType} not found.";
    String YOU_CAN_NOT_EDIT_NETWORKING_DOCUMENT = "You can edit only own networking lounge ${documentType}.";
    String UP_SELL_CONFIGURATION_NOT_FOUND_BY_MODULE="UpSell configuration not found for ${moduleName}";
    String YOU_CAN_NOT_DELETE_NETWORKING_DOCUMENT = "You can delete only own networking lounge ${documentType}.";
    String NETWORKING_LOUNGE_DOCUMENT_UPLOADED = "Networking lounge ${document} uploaded successfully";
    String NETWORKING_LOUNGE_DOCUMENT_UPDATED = "Networking lounge ${document} updated successfully";
    String NETWORKING_LOUNGE_DOCUMENT_DELETED = "Networking lounge ${document} deleted successfully";
    String NETWORKING_DOCUMENT = "document";
    String NETWORKING_LINK = "link";
    String TWO_FACTOR_CODE_USERID_PREFIX = "2FA_U_ID_";

    String FOUR_DIGIT_CODE_PLATFORM = "4_DIGIT_CODE_PLATFORM_";
    String TWO_FACTOR_EVENT_KEY_PREFIX = "2FA_E_";
    String TWO_FACTOR_ORGANIZER_KEY_PREFIX = "2FA_ORG_";
    String TWO_FACTOR_WHITE_LABEL_KEY_PREFIX = "2FA_WL_";
    String USER_PROFILE_UPDATE_AUTH_CODE_PREFIX = "USER_PROFILE_UPDATE_AUTH_CODE_";
    String SALESFORCE_WL_KEY_PREFIX = "SALESFORCE_WL_";
    String SALESFORCE_EVENT_KEY_PREFIX = "SALESFORCE_E_";
    String SSO_REQUIRED_FOR_EVENT_KEY_PREFIX = "SSO_REQUIRED_E";

    String REQUIRES_ACTION = "requires_action";
    String REQUIRES_CONFIRMATION = "requires_confirmation";
    String REQUIRES_PAYMENT_METHOD = "requires_payment_method";

    String CONDUIT_CANDIDATE_CHARGE_CREATED = "conduit_candidate_charge_created";

    String INTEGRATION_OBJECT = "object";
    String INTEGRATION_EVENT_URL = "eventUrl";
    String INTEGRATION_INPUT_FIELDS = "inputFields";
    String INTEGRATION_FIRST_NAME_SMALL = "firstname";
    String INTEGRATION_LAST_NAME_SMALL = "lastname";
    String INTEGRATION_TICKET_ID = "TICKET_ID";
    String REGISTRATION_REQUEST_ID = "REGISTRATION_REQUEST_ID";

    String ONLINE_EVENT = "Online Event";

    String E_MM_DD_YYYY_DATE_FORMAT = "E, MMM dd, yyyy";

    String HH_MM_A_TIME_FORMAT = "hh:mm a";

    String SPACE_TO_SPACE = " to ";

    String SPACE_AT_SPACE = " at ";
    String EVENT_SRC_URL = "${Event_URL_SRC}";
    String EVENT_DOMAIN = "event_domain";
    String EVENT_IFRAME_DOMAIN = "${event_iframe_domain}";
    String SPEAKER_WIDGET_ID_WITH_EVENT_ID = "${speaker_widget_id}";
    String EVENT_DESCRIPTION = "event_description";
    String ADD_TO_CALENDAR_BUTTON= "add_to_calendar_button";
    String DOLLAR_OPEN_CLOSE_CURLY_BRACES = "${%s}";
    String ATTENDEE_REGISTRATION_URL = "attendee_registration_url";
    String SPEAKER_REGISTRATION_URL = "speaker_registration_url";
    String EXHIBITOR_REGISTRATION_URL = "exhibitor_registration_url";
    String REGISTRATION_APPROVAL_URL = "/e/%s/registration-approval";
    String SPEAKER_APPROVAL_URL = "/e/%s/speaker-registration";
    String EXHIBITOR_APPROVAL_URL = "/e/%s/exhibitor-registration";
    String AUTHENTICATION_CODE = "authenticationCode";
    String U_SLASH_NEW_PASSWORD_PATH = "/u/new-password";

    String REGISTRATION_URL = "/e/u/checkout/%s/tickets/order";


    String DATE_FORMATE = "yyyy-MM-dd";
    String EVENT_LOGO_URL = "${event_logo}";
    String DEFAULT_BEE_FREE_PAGE_JSON = "DefaultBeeFreePageJson.json";
    String DEFAULT_BEE_FREE_PAGE_HTML =  "DefaultBeeFreePageHtml.html";
    String BLANK_BEE_FREE_PAGE_JSON = "BlankBeeFreePageJson.json";
    String NETWORK_LOUNGE_SETTING_UPDATED_SUCCESSFULLY = "Networking lounge updated successfully";

    String NETWORK_LOUNGE_SETTING_TIMESLOT_UPDATED_SUCCESSFULLY = "Networking lounge timeslot updated successfully";

    String NETWORK_LOUNGE_SETTING_TIMESLOT_ADDED_SUCCESSFULLY = "Networking lounge timeslot added successfully";

    String NETWORK_LOUNGE_SETTING_TIMESLOT_DELETED_SUCCESSFULLY = "Networking lounge timeslot deleted successfully";

    String NETWORK_LOUNGE_STATUS_UPDATED_SUCCESSFULLY = "Networking lounge status updated successfully";

    String NETWORK_LOUNGE_STATUS_MAIL_SUBJECT = " ${loungeStatus} :  ${loungeName}";

    String NETWORK_LOUNGE_STATUS_MAIL_HEADER_TEXT = "The status of a lounge you submitted has been updated. <strong> ${loungeName} </strong>  has just been <strong> ${loungeStatus} </strong>.";

    String NETWORKING_LOUNGE_MAX_APPROVED_LIMIT = "Networking lounge max approved limit ${count} has been reached.";

    String COMMON_ERROR_MESSAGE = "Something went wrong";

    String NETWORK_LOUNGE_MODERATOR_LIVE_FORUM_DATE_SAVED_SUCCESSFULLY = "Moderator Live forum date saved successfully";

    String NETWORK_LOUNGE_MODERATOR_SORT_MAX_DATE = "9999/01/01 00:00";

    String NETWORK_LOUNGE_MODERATOR_SORT_MIN_DATE = "0001/01/01 00:00";


    String PUSH_NOTIFICATION_TO_HOLDERS_SUBJECT = "${event_name} is starting soon!";

    String PUSH_NOTIFICATION_TO_HOLDERS_BODY = "Tap here to get ready.";

    String MOBILE_NOTIFICATION_TYPE = "EVENT_START_REMINDER";


    String NOTIFICATION_ID = "notificationId";
    String NAVIGATION_INFO ="navigationInfo";
    String AE_PAGE_THUBNAIL = "ae_page_thumbnail";
    long MAX_WAIT_MILLISECONDS = 5000L;

    String CSV_DOWN_STRING = "CSV_DOWN";
    String MESSAGE_LOWER = "message";

    String PARENT_QUESTION_ID = "parentQueId";

    String ADD = "ADD";

    String CAN_NOT_BULK_UPDATE_WITH_SAME_VALUE = "You can update this field {field} with only a single attendee.";

    enum AttendeeTypeEnum {
        BUYER("buyer"),
        HOLDER("holder"),
        USER("user");

        private String displayString;

        AttendeeTypeEnum(String value) {
            this.displayString = value;
        }

        public String getValue() {
            return displayString;
        }
    }

    enum LobbyTabWidgetType {
        AGENDA,
        SPEAKER,
    }

    List<String> BULK_ATTENDEE_UPDATE_NOT_ALLOWED_FIELDS = Arrays.asList("Prefix","First Name","Last Name","Email","Cell Phone","Gender","Birthdate","Age","Facebook","About Me","Instagram","Linkedin","Twitter");
    String DEFAULT_MEETING_REMINDER_CONTEXT = "Meeting with [Participant_Name] at [Meeting_Time] at [Meeting_Location]";
    String DEFAULT_MEETING_REMINDER_MACRO_PARTICIPANT_NAME = "[Participant_Name]";
    String DEFAULT_MEETING_REMINDER_MACRO_MEETING_TIME = "[Meeting_Time]";
    String DEFAULT_MEETING_REMINDER_MACRO_MEETING_LOCATION = "[Meeting_Location]";
    String MEETING_REMINDER_SUBJECT = "Meeting Reminder";
    String PROFILE_IMAGE = "profileImage";

    String COMMA_SPACE = ", ";
    String STRING_LEFT_CURLY_BRACKET="{";
    String STRING_LEFT_SQUARE_BRACKET="[";
    String STRING_RIGHT_SQUARE_BRACKET="]";
    String CHARGEBEE="CHARGEBEE";

    String REGEX_CONTAINS_ALPHA_NUMERIC = ".*[a-zA-Z0-9].*";
    String ANSWERS_HEADER = "Answers";

    String CONTACTS_LIST_NAME_RENAME_MESSAGE = "The contacts list has been successfully renamed.";
    String CONTACTS_LIST_DELETE_MESSAGE = "The contacts list has been deleted successfully.";
    String CREATED_BY = "createdByUsername" ;
    String UPDATED_DATE = "updatedDate";
    String LIST_NAME = "listName";
    String TOTAL_CONTACTS_COUNT = "totalContactsCount";

    String CONTACTS_LIST_ID = "id";
    String CONTACTS_EXIST_IN_CONTACTS_LIST = "contactsExistInList";
    String MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST = "maxUploadContactsPerList";
    String MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST_ERROR_MSG = "You have reached the contact upload limit of ${MAX_CONTACTS} contacts";

    String BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN = "backend-system-integration-appsync-token";

     String ERROR_MSG_FOR_CUSTOM_RECIPIENTS_WHEN_UPLOAD_MAX_LIMIT_REACHED = "You have reached the limit of ${MAX_CONTACTS} non-participant addresses allowed by your plan";

    String STRING_FIRST_CAP_EMAIL_SPACE = "Email";

    String ONLY_STRING_BOOLEAN_AND_LONG_ARE_ALLOWED="Only String, Boolean and Long are allowed";

    String LEAD_SOURCE_MANUAL_TXT = "MANUAL";
    String LEAD_SOURCE = "Lead Source";

    String IS_HOLDER_INFO_VISIBLE_TO_ATTENDEES = "isHolderInfoVisibleToAttendees";
    String BADGE_PDF_ID = "badgePdfId";
    String PDF_URL= "pdfURL";
    String BARCODES = "barcodes";
    String BADGES_PDF = "badges_pdf";
    String PDF_NAME="pdfName";
    String REQUESTER_USER_NAME  = "requester_user_name";
    String REQUESTED_DATE = "requested_date";
    String BADGE_PDF_URL = "badge_pdf_url";
    String BADGE_PDF_GENERATION = "BADGE_PDF_GENERATION";

    String REG_WELCOME_MESSAGE = "Please tab the button below to register for the event.";
    String REG_BUTTON_LABEL = "Register Now";
    String REG_BUTTON_DESC = "Get your access to the event";
    String CONFIRM_SUCCESS_TITLE = "Registration Completed";
    String CONFIRM_SUCCESS_DESC = "Please check your email for event access details. Go to check in line and get your badge.";
    String CONFIRM_UNSUCCESS_TITLE = "Registration Failed";
    String CONFIRM_UNSUCCESS_DESC = "We were unable to process your order. Please review your checkout details and try again.";

    String INVALID_API_TOKEN = "Invalid API token";
    String X_API_KEY = "x-api-key";

    String NETWORKING_LOUNGE_MAX_ATTENDEE_CAPACITY = "The lounge has reached its maximum attendee capacity of ${count}.";

    String REGISTRATION_APPROVAL_EMAIL_SUBJECT_ATTENDEES = "${EVENT_NAME} has received an Attendee application from ${APPLICANT_NAME}";
    String REGISTRATION_APPROVAL_EMAIL_SUBJECT_SPEAKERS = "${EVENT_NAME} has received a Session application from ${APPLICANT_NAME}";
    String REGISTRATION_APPROVAL_EMAIL_SUBJECT_EXHIBITORS = "${EVENT_NAME} has received an Exhibitors application from ${APPLICANT_NAME}";

    String REGISTRATION_APPROVAL_REQUEST_URL_ENDPOINT = "/host/${eventUrl}/event-ticketing/registration-approval#${REGISTRATION_REQUEST_TYPE}";

    String REGISTRATION_REQUEST_TYPE_ATTENDEE = "attendee";
    String REGISTRATION_REQUEST_TYPE_SPEAKER = "speaker";
    String REGISTRATION_REQUEST_TYPE_EXHIBITOR = "exhibitor";

    String REGISTRATION_REQUEST_TYPE_ATTENDEE_WITH_PREFIX = "an Attendee";
    String REGISTRATION_REQUEST_TYPE_SPEAKER_WITH_PREFIX = "a Speaker";
    String REGISTRATION_REQUEST_TYPE_EXHIBITOR_WITH_PREFIX = "an Exhibitor";

    String APPLICANT_NAME_PLACEHOLDER= "${APPLICANT_NAME}";
    String REGISTRATION_REQUEST_TYPE_PLACEHOLDER = "${REGISTRATION_REQUEST_TYPE}";


    //Cloudinary Constants
    String SECURE_URL= "secure_url";
    String ORIGINAL_FILENAME= "original_filename";
    String CLOUD_NAME= "cloud_name";

    String USER_NOTIFICATION_SAVED_SUCCESSFULLY = "User notification set successfully.";

    String TICKET_TRANSFER_AUDIT_MESSAGE_INFO = "The ticket has been transferred by ";

    String ORDER_AUDIT_SUCCESS_MESSAGE_FORMAT = "%s Order #%d  issued successfully.";

    String ORDER_AUDIT_PROCESSING_PAYMENT_MESSAGE_FORMAT = " %s PAYMENT for Order #%d";

    String AUDIT_MESSAGE_WHEN_TICKET_TRANSFERRED_BY_BUYER = "The buyer information has been updated and the tickets have been transferred by ";

    String NEW_TICKET_AUDIT_SUCCESS_MESSAGE_FORMAT = "New %d ticket added in Order #%d ";

    String NEW_TICKET_AUDIT_PROCESSING_PAYMENT_MESSAGE_FORMAT = "New Ticket added,%s payment for Order #%d";

    String NETWORKING_LOUNGE_FEED_PINNED_MESSAGE_SUCCESSFULLY = "Message pinned successfully.";
    String NETWORKING_LOUNGE_FEED_UNPINNED_MESSAGE_SUCCESSFULLY = "Message unpinned successfully.";

    String PURCHASER_ID = "purchaserId";
    String HOLDER_ID = "holderId";
    String PROFILE_PIC = "profilePic";

    String WEBHOOK_ID = "webhookId";
    String REQUEST_ID = "requestId";
    String QUESTION_ID = "questionId";

    String OTHER_PAYMENT_METHOD_NOT_ALLOWED_FOR_PARTIAL_PAID_ORDER_MESSAGE = "Order is partially paid by ${oldPaymentMethod}, please use ${oldPaymentMethod} payment type.";

    String SPEAKER_APPROVAL_SESSION_TOPIC = "Session Topic / Name";
    String SPEAKER_APPROVAL_SESSION_DESCRIPTION = "Session Description";
    String SPEAKER_APPROVAL_SESSION_DOCUMENTS = "Upload Session Documents";
    String SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME = "docName";
    String SPEAKER_APPROVAL_SESSION_DURATION = "Session Duration";

    String EXHIBITOR_APPROVAL_BOOTH_NAME = "Booth Name";
    String EXHIBITOR_APPROVAL_BOOTH_LOGO = "Logo";
    String EXHIBITOR_APPROVAL_BOOTH_DOCUMENTS = "Document for Attendees";

    String SUBMISSION_DUE_DATE = "submissionDueDate";

    String ASSIGNEE_COUNT = "#ASSIGNEE_COUNT#";
    String TASK_CSV_PREFIX = "Task_";
    String ZERO = "0";
    String IS_CORRECT_ANSWER = "isCorrectAnswer";
    enum MergeTagType {
        HOLDER,
        BUYER,
        SPEAKER,
        EXHIBITOR,
        ATTENDEE,
        SESSION,
        REVIEWER
    }
    String MODE = "mode";

    final class Spreedly {

        private Spreedly(){}

        public static final String GATEWAY_LOWER = "gateway";
        public static final String GATEWAY_TYPE= "gateway_type";
        public static final String GATEWAY_SLASH = "gateways/";
        public static final String PAYMENT_METHODS_SLASH = "payment_methods/";
        public static final String TRANSACTION = "transaction";
        public static final String PAYMENT_METHOD_TOKEN = "payment_method_token";
        public static final String GATEWAY_TOKEN = "GATEWAY_TOKEN";
        public static final String SPREEDLY_PAYMENT_PROCESSOR = "SPREEDLY_PAYMENT_PROCESSOR";
        public static final String TRANSACTION_TOKEN = "transaction_token";
        public static final String STATE = "state";
        public static final String LAST_FOUR_DIGIT = "last_four_digits";
        public static final String MONTH = "month";
        public static final String YEAR = "year";
        public static final String CARD_TYPE = "card_type";
        public static final String FINGERPRINT = "fingerprint";
        public static final String CUSTOMER_EVENT_WITH_USERID = "cus_eventId_%d_userId_%d";
        public static final String CUSTOMER_ID = "customer_id";
        public static final String BASIS_PAYMENT_METHOD = "basis_payment_method";
        public static final String GATEWAY_SPECIFIC_FIELDS = "gateway_specific_fields";
        public static final String GATEWAY_SPECIFIC_RESPONSE_FIELDS = "gateway_specific_response_fields";
        public static final String STORAGE_STATE = "storage_state";
        public static final String RETAIN = "retained";
        public static final String THIRD_PARTY_TOKEN = "third_party_token";
        public static final String AUTHORIZE_NET_DUPLICATE_CUSTOMER_PROFILE_MESSAGE = "A duplicate customer payment profile already exists";
    }

    final class AutoLoginCreationLocation {
        private AutoLoginCreationLocation(){}
        public static final String TICKET_PURCHASE_EMAIL = "TICKET_PURCHASE_EMAIL";
        public static final String ATTENDEE_CHECKIN_EMAIL = "ATTENDEE_CHECKIN_EMAIL";
        public static final String ATTENDEE_CHECKIN_SMS = "ATTENDEE_CHECKIN_SMS";
        public static final String STAFF_BIDDER_SMS = "STAFF_BIDDER_SMS";
        public static final String TRAY_IO_INTEGRATION_WEBHOOK = "TRAY_IO_INTEGRATION_WEBHOOK";
        public static final String USER_EMAIL_VERIFICATION_MAIL = "USER_EMAIL_VERIFICATION_MAIL";
        public static final String AUTO_SIGNIN_MAGIC_LINK_EMAIL = "AUTO_SIGNIN_MAGIC_LINK_EMAIL";
        public static final String PLATFORM_LEVEL_MAGIC_LINK_MAIL = "PLATFORM_LEVEL_MAGIC_LINK_MAIL";
        public static final String PLATFORM_LEVEL_MAGIC_LINK_API = "PLATFORM_LEVEL_MAGIC_LINK_API";
        public static final String TICKET_TYPE_TRANSFER_EMAIL = "TICKET_TYPE_TRANSFER_EMAIL";
        public static final String SESSION_SPEAKER_TASK_EMAIL = "SESSION_SPEAKER_TASK_EMAIL";
        public static final String EXHIBITOR_TASK_EMAIL = "EXHIBITOR_TASK_EMAIL";
        public static final String SPEAKER_INVITE_EMAIL =  "SPEAKER_INVITE_EMAIL";
        public static final String STAFF_OR_EXHIBITOR_STAFF_EMAIL = "STAFF_OR_EXHIBITOR_STAFF_EMAIL";
        public static final String EXHIBITOR_ADMIN_EMAIL = "EXHIBITOR_ADMIN_EMAIL";
        public static final String REGISTRATION_APPROVAL_TEST_EMAIL = "REGISTRATION_APPROVAL_TEST_EMAIL";
        public static final String MAGIC_LINK_FOR_PORTAL = "MAGIC_LINK_FOR_PORTAL";
        public static final String ADMIN_OR_STAFF_TICKET_CONFIRMATION_EMAIL = "ADMIN_OR_STAFF_TICKET_CONFIRMATION_EMAIL";
        public static final String TICKET_PURCHASE_BETWEEN_SIXTY_MIN_OF_EVENT_LIVE = "TICKET_PURCHASE_BETWEEN_SIXTY_MIN_OF_EVENT_LIVE";
        public static final String PASSWORD_CREATION_EMAIL = "PASSWORD_CREATION_EMAIL";
        public static final String EXPIRE_OLD_AND_GENERATE_NEW_MAGIC_LINK = "EXPIRE_OLD_AND_GENERATE_NEW_MAGIC_LINK";
        public static final String MAGIC_LINK_TO_SEND_ALL_USER_EMAIL = "MAGIC_LINK_TO_SEND_ALL_USER_EMAIL";
        public static final String MAGIC_LINK_SEND_TO_USER_EMAIL = "MAGIC_LINK_SEND_TO_USER_EMAIL";
        public static final String BUYER_DONATION_EMAIL = "BUYER_DONATION_EMAIL";
        public static final String LOUNGE_STATUS_MAIL = "LOUNGE_STATUS_MAIL";
        public static final String SESSION_MAGIC_LINK_EMAIL = "SESSION_MAGIC_LINK_EMAIL";
        public static final String WHITE_LABEL_EVENT_CREATE_OR_UPDATE_EMAIL_TO_WL_ADMIN = "WHITE_LABEL_EVENT_CREATE_OR_UPDATE_EMAIL_TO_WL_ADMIN";
        public static final String RESEND_MAGIC_LINK_EMAIL = "RESEND_MAGIC_LINK_EMAIL";
        public static final String EVENT_TASK_REMINDER_EMAIL = "EVENT_TASK_REMINDER_EMAIL";
        public static final String REGISTRATION_REMINDER_EMAIL = "REGISTRATION_REMINDER_EMAIL";
        public static final String ENGAGE_REMINDER_EMAIL = "ENGAGE_REMINDER_EMAIL";
        public static final String TICKET_HOLDER_CSV_DOWNLOAD = "TICKET_HOLDER_CSV_DOWNLOAD";
        public static final String REVIEWER_INVITE_EMAIL = "REVIEWER_INVITE_EMAIL";
    }


    String WHITELABEL_EVENT_DIRECTORY_PAGE_JSON = "Whitelabel_event_directory_page.json";
    String WHITELABEL_EVENT_DIRECTORY_PAGE_HTML = "Whitelabel_event_directory_page.html";
    String ORGANIZER_EVENT_DIRECTORY_PAGE_JSON = "Organizer_event_directory_page.json";
    String ORGANIZER_EVENT_DIRECTORY_PAGE_HTML = "Organizer_event_directory_page.html";

    String TICKET_TYPE_ID = "ticketTypeId";

    String DEFAULT_REVIEWER_EMAIL_SUBJECT = "You're Invited To Join As A Reviewer For %s";
    String REVIEWER_EMAIL_SUBJECT_STATUS_APPROVED = "Your reviewer application for %s has been approved";
    String REVIEWER_EMAIL_SUBJECT_STATUS_PENDING = "Your reviewer application for %s has been received";
    String REVIEWER_EMAIL_SUBJECT_STATUS_DENIED = "Your reviewer application for %s has been denied";
    String REVIEWER_EMAIL_SUBJECT_STATUS_WAITLISTED = "Your reviewer application for %s has been waitlisted";
    String DEFAULT_REVIEWER_EMAIL_BODY = "We're excited to invite you to join %s as a Reviewer.";
    String REVIEWER_EMAIL_BODY_STATUS_APPROVED = "Hi [firstname], Congratulations! Your reviewer application has been accepted. We look forward to see you at %s. Please keep an eye out for further instruction if needed.";
    String REVIEWER_EMAIL_BODY_STATUS_PENDING = "Hi [firstname], Your reviewer application has been received, and we will be in touch as soon as there is an update. Thank You!";
    String REVIEWER_EMAIL_BODY_STATUS_DENIED = "Hi [firstname], Thank you for your interest in %S. Unfortunately we are unable to accept your reviewer application for this event.";
    String REVIEWER_EMAIL_BODY_STATUS_WAITLISTED = "Hi [firstname], Thank you for your interest in %s. Your reviewer application been added to our waitlist and will be contacted further if spots become available.";
    String SOURCE_URL = "Source URL";
    String ASSIGNED_SESSION = "Assigned Sessions";

    String SESSIONS_UNDERSCORE = "SESSIONS_";
    String USER_UNDERSCORE = "USER_";
    String PRIVATE_UNDERSCORE = "PRIVATE_";
    String PUBLIC_UNDERSCORE = "PUBLIC_";
    String PRIVATE_IDS_UNDERSCORE = "PRIVATE_IDS_";


    String DEFAULT_COLUMN_REVIEW_STATUS = "reviewStatus";
    String REVIEW_PROCESS_DEFAULT_TITLE = "Default Process";

    String RECORDS = "records";
    String KEY_SMALL_CAP = "key";


    //conditional form logic operators
    String IS = "is";
    String IS_NOT = "isNot";
    String CONTAINS = "contains";
    String NOT_CONTAINS = "doesNotContain";
    String IS_EMPTY = "isEmpty";
    String IS_NOT_EMPTY = "isNotEmpty";
    String GREATER_THAN_STRING = "greaterThan";
    String LESS_THAN_STRING = "lessThan";
    String GREATER_THAN_EQUALS = "greaterThanEquals";
    String LESS_THAN_EQUALS = "lessThanEquals";
    String BEFORE_OPERATOR = "before";
    String AFTER_OPERATOR = "after";
    String HIDE_ACTION = "HIDE";
    String SHOW_ACTION = "SHOW";
    String REQUIRE_ACTION = "REQUIRE";
    String IS_CHECKED = "isChecked";
    String IS_NOT_CHECKED = "isNotChecked";
    
    String DUPLICATE_SURVEY_REG = ".*?(\\d+)(?!.*\\d)";
    String MERGE_TAG_PATTERNS_FOR_ALL = "\\$\\{(.+?)\\}";
    String ALL = "all";
    Pattern MERGE_TAG_PATTERN = Pattern.compile(MERGE_TAG_PATTERNS_FOR_ALL);

    enum ThirdPartyCouponApplicableFor{
        BUYER,
        HOLDER
    }

    String CHECK_IN_ACTIVITY = "Checked In to the event";
    String CHECK_OUT_ACTIVITY = "Checked Out of the event";

    String LEAD_ADD_ACTIVITY = "Lead added to the event";
    String LEAD_UPDATE_ACTIVITY = "Lead updated in the event";

    //Push Notification
    String PN_SCREEN_NAME = "screenName";
    String PN_SESSIONS = "sessions";
    String PN_SURVEY = "survey";
    String PN_EXHIBITOR = "exhibitors";

    String CSV_REGISTRATION_APPROVAL_SPEAKER = "REGISTRATION_APPROVAL_SPEAKER";
    String CSV_REGISTRATION_APPROVAL_ATTENDEE = "REGISTRATION_APPROVAL_ATTENDEE";
    String CSV_REGISTRATION_APPROVAL_EXPO = "REGISTRATION_APPROVAL_EXPO";
    String CSV_SUBMITTED_DATE = "Date Submitted";
    String CSV_STATUS = "Status";
    String CSV_PRIMARY_SPEAKER_NAME = "Primary Speaker";
    String CSV_REVIEW_STATUS = "Review Status";
    String CSV_TICKET_TYPE = "Ticket Type";
    String CSV_TICKET_QTY = "Ticket Qty";
    String CSV_NOTES = "Notes";
    String CSV_SESSION_NAME = "Session Topic / Name";
    String CSV_SESSION_DESCRIPTION="Session Description";

    String LINK_TYPE_EVENT_LEVEL = "EVENT_LEVEL";
    String LINK_TYPE_PLATFORM_LEVEL = "PLATFORM_LEVEL";

    String KIOSK_WELCOME_MESSAGE = "WELCOME_MESSAGE";
    String KIOSK_SUCCESSFUL_CHECK_IN_MESSAGE = "SUCCESSFUL_CHECK_IN_MESSAGE";
    String KIOSK_UNSUCCESSFUL_CHECK_IN_MESSAGE = "UNSUCCESSFUL_CHECK_IN_MESSAGE";
    String KIOSK_NO_ATTENDEE_FOUND_MESSAGE = "NO_ATTENDEE_FOUND_MESSAGE";
    String KIOSK_UNPAID_TICKET_MESSAGE = "UNPAID_TICKET_MESSAGE";

    String CREATED_AT = "createdAt";
    String ASC_CAP = "ASC";

    interface EventRequestFormConstant {

        String EVENT_NAME_CAP = "EVENT_NAME";
        String EVENT_FORMAT_CAP = "EVENT_FORMAT";
        String EVENT_DESCRIPTION_CAP = "EVENT_DESCRIPTION";
        String EVENT_START_DATE_CAP = "EVENT_START_DATE";
        String EVENT_END_DATE_CAP = "EVENT_END_DATE";
        String EVENT_LOCATION_CAP = "EVENT_LOCATION";
        String EVENT_LOGO_CAP = "EVENT_LOGO";
        String EVENT_CURRENCY_CAP = "EVENT_CURRENCY";
        String EVENT_TEMPLATE_CAP = "EVENT_TEMPLATE";
        String ORGANIZER_CAP = "ORGANIZER";

        String EVENT_REQUEST_TABLE_NAME = "event_request";

        String EVENT_LOGO = "Event Logo";
        String EVENT_TEMPLATE = "Event Template";
        String FORM_EVENT_START_DATE = "Event Start Date & Time";
        String FORM_EVENT_END_DATE = "Event End Date & Time";
        String FIELD_ATTRIBUTE_EVENT_FORMAT_DEFAULT_VALUE = "{\"defaultvalues\":{\"defaultvalue\":[{\"label\":\"In Person\",\"value\":\"IN_PERSON\"},{\"label\":\"Hybrid\",\"value\":\"HYBRID\"},{\"label\":\"Online\",\"value\":\"VIRTUAL\"}]}}";
        String FIELD_ATTRIBUTE_EVENT_CURRENCY_DEFAULT_VALUE = "{\"defaultvalues\":{\"defaultvalue\":[{\"label\":\"AUD ( $ )\",\"value\":\"AUD\"},{\"label\":\"CHF ( CHF )\",\"value\":\"CHF\"},{\"label\":\"NOK ( kr )\",\"value\":\"NOK\"},{\"label\":\"PHP ( ₱ )\",\"value\":\"PHP\"},{\"label\":\"MXN ( Mex$ )\",\"value\":\"MXN\"},{\"label\":\"SGD ( S$ )\",\"value\":\"SGD\"},{\"label\":\"USD ( $ )\",\"value\":\"USD\"},{\"label\":\"LIRA ( L )\",\"value\":\"LIRA\"},{\"label\":\"CAD ( $ )\",\"value\":\"CAD\"},{\"label\":\"EURO ( € )\",\"value\":\"EURO\"},{\"label\":\"JPY ( ¥ )\",\"value\":\"JPY\"},{\"label\":\"REALS ( R$ )\",\"value\":\"REALS\"},{\"label\":\"DKK ( kr. )\",\"value\":\"DKK\"},{\"label\":\"FRANC ( Fr )\",\"value\":\"FRANC\"},{\"label\":\"RAND ( R )\",\"value\":\"RAND\"},{\"label\":\"IDR ( Rp )\",\"value\":\"IDR\"},{\"label\":\"POUND ( £ )\",\"value\":\"POUND\"},{\"label\":\"HKD ( HK$ )\",\"value\":\"HKD\"},{\"label\":\"SEK ( kr )\",\"value\":\"SEK\"},{\"label\":\"NZD ( $ )\",\"value\":\"NZD\"}]}}";

        String EVENT_DATE_CANNOT_BE_IN_PAST = "The event start date cannot be in the past.";
        String EVENT_FORMAT_TYPE_NOT_CORRECT = "The event format type is not invalid.";
        String EVENT_CURRENCY_NOT_CORRECT = "The event currency is not invalid.";
        String EVENT_DATE_IS_NOT_VALID = "The event date are not valid.";
        String EVENT_DATE_TIME_ZONE_NOT_CORRECT = "The event date time equivalent time zone is not valid.";


    }


    String POSITION_SMALL = "position";
    String EVENT_REQUEST_FORM = "event_request_form";
    String SUBMISSION_DATE = "submission_date";
    String SUBMITTER = "submitter";
    String SUBMISSION_STATUS = "submission_status";
    String REQUEST_FORM_NAME = "request_form_name";
    String EVENT_END_DATE_SMALL_UNDERSCORE_SEP = "event_end_date";
    String NEED_INFO_STRING = "Need Info";

    String REGISTERED_WAITLISTED_USER_SUCCESSFULLY = "Registered waitlisted user successfully.";
    String REMOVED_WAITLISTED_USER_SUCCESSFULLY = "Removed waitlisted user successfully.";
    String USER_ADDED_TO_WAITLIST_SUCCESSFULLY = "User added to waitlist successfully.";

    String DEFAULT_ENTRY_ERROR_MESSAGE = "<div><strong>Unable to mark this attendee as Entered. Please ask to visit the info desk.</strong></div>";

    String DEFAULT_EXIT_ERROR_MESSAGE = "<div><strong>Unable to mark this attendee as Exited. Please ask to visit the info desk.</strong></div>";

    public enum EntryExitStatus {
        ENTRY,EXIT
    }

    long MAX_DEFAULT_TICKET_TRANSFER_LIMIT = 5;

    String DEFAULT_COLOR_CONFIG_JSON = "{\"globalColors\":{\"primaryColor\":\"#406AE8\",\"textColor\":\"#1E2137\"},\"homeTabColors\":{\"iconsColor\":\"#406AE8\",\"iconsBackgroundColor\":\"#FFFFFF\",\"buttonLabelColor\":\"#1E2137\",\"foregroundColor\":\"#F7F7FA\",\"backgroundColor\":\"#FFFFFF\"},\"menuTabColors\":{\"iconsColor\":\"#406AE8\",\"buttonLabelColor\":\"#1E2137\"}}";
    String IN_PERSON_WITH_UNDERSCORE = "IN_PERSON";
    String IN_PERSON_WITHOUT_UNDERSCORE = "IN PERSON";

    String TEXT_BLOCK = "Text Block";

    String DEFAULT_VALUE_FOR_PURCHASER = "defaultValueForPurchaser";
    String DEFAULT_VALUE_FOR_HOLDER = "defaultValueForHolder";
    String CONTACT_ATTRIBUTE = "contact_attribute";
    String JSON_VALUE = "json_value";
    String CONTACT_ATTRIBUTES = "attributes";
    String CHECKED_FALSE = "false";
    String ENGAGE_BUYER = "buyer_";
    String ENGAGE_HOLDER = "holder_";
    String ATTRIBUTE_NAME = "attribute_name";

    enum SpeakerImportMessage {
        SPEAKER_ID_MUST_BE_NUMERIC("SpeakerId must be numeric."),
        DUPLICATE_SPEAKER_ID("Duplicate Speaker Id in file."),
        SPEAKER_ID_NOT_FOUND_TO_UPDATE_FOR_EVENT("Speaker Id not found to update for event.");

        private String status;

        SpeakerImportMessage(String status) {
            this.status = status;
        }

        public String getStatus() {
            return status;
        }
    }

    String ENGAGE_MERRGE_TAG_PATTERN = "\\$\\{[a-zA-Z][a-zA-Z0-9]*_\\d+\\}";
    String TICKET_HOLDER_ATTRIBUTES = "ticket_holder_attributes";
    String IMAGE_VALIDATION_PATTERN = "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}.*";

    String CONTACT_UPLOAD_FILE_HEADER_NOT_CORRECT_ERROR_MESSAGE = "Upload file headers name contains ${REQUIRED_COLUMNS} ${DUPLICATE_COLUMNS} ${UNIDENTIFIED_COLUMNS}";
    String REQUIRED_COLUMNS_ERROR_MESSAGE_TEXT = "${REQUIRED_COLUMNS}";
    String DUPLICATE_COLUMNS_ERROR_MESSAGE_TEXT = "${DUPLICATE_COLUMNS}";
    String UNIDENTIFIED_COLUMNS_ERROR_MESSAGE_TEXT = "${UNIDENTIFIED_COLUMNS}";

}
