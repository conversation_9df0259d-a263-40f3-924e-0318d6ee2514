{"page": {"body": {"container": {"style": {"background-color": "#ececec"}}, "content": {"computedStyle": {"linkColor": "#0068A5", "messageBackgroundColor": "#ffffff", "messageWidth": "600px"}, "style": {"color": "#000000", "font-family": "Arial, Helvetica Neue, Helvetica, sans-serif"}}, "type": "mailup-bee-page-properties", "webFonts": []}, "description": "", "rows": [{"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\">${headerImage}</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "56383dd7-765a-4a99-b863-8fc5879c5c39"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "10px", "padding-right": "10px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"line-height: 18px; word-break: break-word;\" data-mce-style=\"line-height: 18px; word-break: break-word;\">${image}</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "e02cec98-b021-42d3-8fb7-f6a3101255bf"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "20px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\">Hi ${ticket_holder_first_name},</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "b5e8e426-9b45-433d-b8d1-c5dfb613c55e"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\">Welcome to ${event_name}! To continue your support of the event, head to the event page below.</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "cf86596a-1358-4304-8e04-26b6521c451c"}, {"descriptor": {"button": {"href": "${MagicLink}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 24px;\" data-mce-style=\"font-size: 12px; line-height: 24px;\"><p style=\"font-size: 16px; line-height: 32px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 32px; word-break: break-word;\">Access Event Page</p></div>", "style": {"background-color": "#35b62b", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "10px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#ffffff", "font-family": "inherit", "line-height": "200%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "20px", "padding-right": "20px", "padding-top": "5px", "width": "auto"}}, "computedStyle": {"height": 41, "hideContentOnMobile": false, "width": 178}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px", "text-align": "center"}}, "locked": false, "type": "mailup-bee-newsletter-modules-button", "uuid": "09afbaf1-0fd6-4a9f-bfe2-3fedd1295d13"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\">Thanks!</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "20e889a1-243d-48cc-bac1-bc9b66c129c7"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "15px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\">${org_name}</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "287cc954-568c-4caa-8780-36177adfc6c6"}, {"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "2px solid #000000", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}}, "locked": false, "type": "mailup-bee-newsletter-modules-divider", "uuid": "9351b43c-38b6-43b7-a016-143868a96b74"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "5px", "padding-left": "10px", "padding-right": "10px", "padding-top": "15px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\"><strong><span style=\"font-size: 24px; line-height: 36px;\" data-mce-style=\"font-size: 24px; line-height: 36px;\">Questions about this event?</span></strong></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "a433e057-0279-4930-b64d-3900de15f38b"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "15px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\">Contact the organizer at ${staff_email}</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "c9690f57-ea85-411a-bdc3-b45cf2a9ad66"}, {"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "2px solid #000000", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}}, "locked": false, "type": "mailup-bee-newsletter-modules-divider", "uuid": "0ed200a3-6311-48c7-83a6-de1f646bb382"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "20px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\"><span style=\"font-size: 20px; line-height: 30px;\" data-mce-style=\"font-size: 20px; line-height: 30px;\"><strong>Hosting Your Own Event?</strong></span></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "ead31b43-6652-4e72-b26f-94878af1fa27"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word;\">Join thousands of organizations using Accelevents to manage their events.</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "3520f8bb-0aed-48c2-862c-************"}, {"descriptor": {"button": {"href": "${getStarted}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 24px;\" data-mce-style=\"font-size: 12px; line-height: 24px;\"><p style=\"font-size: 16px; line-height: 32px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 32px; word-break: break-word;\">Get Started for FREE!</p></div>", "style": {"background-color": "#35b62b", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "10px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#ffffff", "font-family": "inherit", "line-height": "200%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "20px", "padding-right": "20px", "padding-top": "5px", "width": "auto"}}, "computedStyle": {"height": 41, "hideContentOnMobile": false, "width": 196}, "style": {"padding-bottom": "20px", "padding-left": "10px", "padding-right": "10px", "padding-top": "20px", "text-align": "center"}}, "locked": false, "type": "mailup-bee-newsletter-modules-button", "uuid": "addce7d2-78f4-439c-93a2-e6e8e80668c7"}, {"descriptor": {"computedStyle": {"hideContentOnDesktop": false, "hideContentOnMobile": false, "iconHeight": "32px", "iconSpacing": {"padding-bottom": "5px", "padding-left": "5px", "padding-right": "5px", "padding-top": "15px"}, "itemSpacing": "0px"}, "style": {"color": "#000000", "font-family": "inherit", "font-size": "14px", "padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "center"}}, "locked": false, "type": "mailup-bee-newsletter-modules-icons", "uuid": "1c52adbe-5b14-46d0-852e-f7b145b4aa74"}, {"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "2px solid #000000", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "20px"}}, "locked": false, "type": "mailup-bee-newsletter-modules-divider", "uuid": "e1d7b690-fe7e-4e4c-abe5-b94e611c7775"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word; text-align: center;\">This email was sent by: ${Sender_Name} ${Sender_Address} ${Sender_City} ${Sender_State} ${Sender_Zip}</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "cd1c24b2-4106-4d94-ae94-9d3998de57f4"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 14px; line-height: 21px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 21px; word-break: break-word; text-align: center;\">${unsubscribe_link}</p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "150%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "f1da21fd-a86c-4010-9b6d-1241963c0f15"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}, "uuid": "44e22a1a-7233-44e9-9259-e8ea596e1fda"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "600px"}}, "locked": false, "type": "one-column-empty", "uuid": "a4d66fbd-14d8-4951-ad5b-ccfc38458a0d"}], "template": {"name": "template-base", "type": "basic", "version": "2.0.0"}, "title": ""}}