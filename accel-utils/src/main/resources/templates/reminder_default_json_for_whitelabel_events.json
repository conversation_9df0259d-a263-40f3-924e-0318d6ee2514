{"page": {"body": {"container": {"style": {"background-color": "#ececec"}}, "content": {"computedStyle": {"linkColor": "#0068A5", "messageBackgroundColor": "#ffffff", "messageWidth": "750px"}, "style": {"color": "#000000", "font-family": "Arial, Helvetica Neue, Helvetica, sans-serif"}}, "type": "mailup-bee-page-properties", "webFonts": []}, "description": "", "rows": [{"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"class": "center fixedwidth", "width": "262.5px"}, "image": {"alt": "Accelevents", "height": "95px", "href": "", "src": "${whiteLabelLogo}", "width": "476px"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "50px", "width": "100%"}}, "locked": false, "type": "mailup-bee-newsletter-modules-image", "uuid": "28364001-eba8-4dc1-97df-d8e136b27fc8"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "60px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"text-align: center; line-height: 14px; word-break: break-word;\"><strong><span style=\"font-size: 30px; color: #000000; line-height: 36px;\" data-mce-style=\"font-size: 30px; color: #000000; line-height: 36px;\">Hi ${ticket_holder_first_name}, you're ready to go!</span></strong></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "120%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "4e7b5379-cf4c-4a1b-a10a-a23d9a60d85f"}, {"descriptor": {"computedStyle": {"class": "center fixedwidth fullwidthOnMobile", "width": "600px"}, "image": {"alt": "Event Logo", "height": "350px", "href": "", "src": "${whiteLabelEventLogo}", "width": "700px"}, "style": {"padding-bottom": "40px", "padding-left": "0px", "padding-right": "0px", "padding-top": "40px", "width": "100%"}}, "locked": false, "type": "mailup-bee-newsletter-modules-image", "uuid": "ad78ddc3-e9ac-40ea-ae1b-eea27b0c1b67"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "30px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">${adminText}${headerText}</span></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "120%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "f70d0fe8-8ce9-4eca-9a97-a7a058551976"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "609912a8-2f7a-4573-bb77-e3f2c92754c1"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "750px"}}, "locked": false, "type": "one-column-empty", "uuid": "92c7f5b1-3bc5-4a01-a87f-a3972d07364e"}, {"columns": [{"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #BBBBBB", "width": "100%"}}, "style": {"padding-bottom": "45px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "f2c4610a-03be-49ae-a421-bed8594b8fdd"}, {"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "5px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\">${OrganizerVatInformationLabel}</span></strong></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><span style=\"line-height: 14px;\" data-mce-style=\"line-height: 14px;\">${organizerVATName}</span></span></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><span style=\"line-height: 14px;\" data-mce-style=\"line-height: 14px;\">${organizerVATNumber}</span></span></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "8498eeda-fbfc-4c9f-bfa2-78360a25582d"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "0px", "padding-top": "40px"}, "uuid": "71d39420-593e-4bf9-abb0-7b73ea842e5d"}, {"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #BBBBBB", "width": "100%"}}, "style": {"padding-bottom": "45px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "513c32e2-4c1f-46ba-98d5-382cb714d229"}, {"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "5px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\"><strong><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\">${BuyerVatInformationLabel}</span></strong></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><span style=\"line-height: 14px;\" data-mce-style=\"line-height: 14px;\">${buyerOrganizationName}</span></span></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\"><span style=\"line-height: 14px;\" data-mce-style=\"line-height: 14px;\">${buyerVATNumber}</span></span></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "9323c12b-8061-443a-a3fe-e5e8a2e69f56"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "15px", "padding-top": "40px"}, "uuid": "c64b1f0f-17a3-4d1d-9369-723bbd7b1d9b"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if vatTaxEnabled??>", "description": "is vat tax enabled?", "label": "is vat tax enabled?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": false, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "metadata": {"category": 578593, "dateCreated": "2022-05-10T14:57:10.045885Z", "dateModified": "2022-05-10T14:57:13.651283Z", "description": "", "idParent": null, "name": "Vat details row", "slug": "vat-details-row", "uuid": "defcc1fa-078f-4b62-910d-9d19fc0b0471"}, "name": "Vat details row", "type": "two-columns-empty", "uuid": "405b6319-ae61-44c7-a3fa-7c3f435c5717"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #BBBBBB", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "40px"}}, "locked": false, "type": "mailup-bee-newsletter-modules-divider", "uuid": "530a4b57-7e66-4f24-8f76-756c631601e3"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "40px"}, "text": {"computedStyle": {"linkColor": "#0068a5"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 30px; line-height: 36px;\" data-mce-style=\"font-size: 30px; line-height: 36px;\">Ordered By: ${purchaser_name} </span></strong><span style=\"font-size: 20px; line-height: 24px; color: #0068a5;\" data-mce-style=\"font-size: 20px; line-height: 24px; color: #0068a5;\">( ${purchaser_email}<a href=\"mailto:<EMAIL>\" target=\"_blank\" rel=\"noopener\" style=\"color: #0068a5;\"></a>&nbsp;)</span></p></div>", "style": {"color": "#4a4a4a", "font-family": "inherit", "line-height": "120%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "38d1ffc4-b29e-4ab4-96b9-8038d01adf5b"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#0068a5"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 20px; line-height: 24px; color: #0068a5;\" data-mce-style=\"font-size: 20px; line-height: 24px; color: #0068a5;\">Order #${order_number}</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "2251e96d-**************-3ace4df496ce"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "ef7e6883-1f7d-42c2-9e77-dfcd23bcc6b7"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": false, "type": "one-column-empty", "uuid": "ea327da4-8603-47be-a521-eb6917915b90"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnDesktop": false, "hideContentOnMobile": false, "iconHeight": "16px", "iconSpacing": {"padding-bottom": "5px", "padding-left": "5px", "padding-right": "10px", "padding-top": "5px"}, "itemSpacing": "0px"}, "iconsList": {"icons": [{"alt": "", "height": "29px", "href": "", "image": "${imagePrefix}/Ticket-icon.png", "target": "_self", "text": "${total_tickets} x Ticket", "textPosition": "right", "title": "", "width": "29px"}]}, "style": {"color": "#000000", "font-family": "inherit", "font-size": "22px", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "left"}}, "type": "mailup-bee-newsletter-modules-icons", "uuid": "d9e39dc2-e95d-43f9-94ea-416a88f4558e"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "45px"}, "uuid": "a51c8d31-f12e-436c-a2dc-4b7416a03f17"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_tickets??>", "description": "is paid or free ticket present in order?", "label": "is paid or free ticket present in order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "7fab5909-27e5-4c4a-9645-fef7fb807916"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div id=\"ticket-details-begining-of-the-list\" style=\"padding-top: 5px;\"></div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "c56ba855-3871-443a-a3ef-edf622776e9a"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "uuid": "890cfa93-818c-44ca-9a60-ecfce26657cb"}], "container": {"displayCondition": {"after": "end_of_the_div", "before": "ticket_details_beginning_of_the_list", "description": "Ticket details beginning of the list (Read only)", "label": "Ticket details beginning of the list (Read only)", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "4352d261-c5ce-4ca1-b538-d9546783f0e8"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div style=\"text-align:left;\">\n  <div style=\"width: fit-content;display: inline-block;border-radius: 13px;    background-color: #EFEFEF; text-align: center; margin-left: 30px;margin-top:10px;\">\n    <span style=\"display: inline-block; min-width: 10px; padding: 3px 7px;    font-size: 12px;    font-weight: 700; line-height: 1;    color: #fff;    text-align: center;    white-space: nowrap; vertical-align: middle;    background-color: #777;    border-radius: 10px; margin-top: 3px;\">${numberOfTicket}</span>\n    <span style=\"color: #436EE5; margin-left: 5px; vertical-align: sub; padding-right: 10px\">${free_ticket_name}</span>\n  </div>\n  <div style=\"width: fit-content;display: inline-block;border-radius: 13px;    background-color: #EFEFEF; text-align: center; margin-left: 30px;margin-top:10px;\">\n    <span style=\"display: inline-block; min-width: 10px; padding: 3px 7px;    font-size: 12px;    font-weight: 700; line-height: 1;    color: #fff;    text-align: center;    white-space: nowrap; vertical-align: middle;    background-color: #777;    border-radius: 10px; margin-top: 3px;\">${numberOfTicket}</span>\n    <span style=\"color: #436EE5; margin-left: 5px; vertical-align: sub; padding-right: 10px\">${paid_ticket_name}: ${ticket_price}</span>\n  </div>\n</div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "56f84467-551b-41dc-984e-d81ee0c2a2cb"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "ee38edf0-c6a0-4831-bfd3-4d0da108c909"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_tickets??>", "description": "is paid or free ticket present in order?", "label": "is paid or free ticket present in order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "437baf9f-0981-407c-a6e9-327f8e2351d0"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div id=\"ticket-details-ending-of-the-list\" style=\"padding-top: 5px;\"></div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "3f877a6d-24ac-4449-abf1-d87f72c6b54b"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "uuid": "890cfa93-818c-44ca-9a60-ecfce26657cb"}], "container": {"displayCondition": {"after": "ticket_details_ending_of_the_list", "before": "start_of_the_div", "description": "Ticket details ending of the list (Read only)", "label": "Ticket details ending of the list (Read only)", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "592b6baf-eedd-45f4-a8f9-46151e33aa7b"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnDesktop": false, "hideContentOnMobile": false, "iconHeight": "16px", "iconSpacing": {"padding-bottom": "10px", "padding-left": "5px", "padding-right": "10px", "padding-top": "5px"}, "itemSpacing": "0px"}, "iconsList": {"icons": [{"alt": "", "height": "20px", "href": "", "image": "${imagePrefix}/donation-icon.png", "target": "_self", "text": "Donation", "textPosition": "right", "title": "", "width": "28px"}]}, "style": {"color": "#000000", "font-family": "inherit", "font-size": "22px", "letter-spacing": "0px", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "left"}}, "type": "mailup-bee-newsletter-modules-icons", "uuid": "e63746c7-6f47-4b98-9006-e909284c440e"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div style=\"text-align:left;\">\n  <div style=\"width: fit-content;display: inline-block;border-radius: 13px;    background-color: #EFEFEF; text-align: center; margin-left: 30px;\">\n    <span style=\"display: inline-block; min-width: 10px; padding: 3px 7px;    font-size: 12px;    font-weight: 700; line-height: 1;    color: #fff;    text-align: center;    white-space: nowrap; vertical-align: middle;    background-color: #777;    border-radius: 10px; margin-top: 3px;\">${total_donation_tickets}</span>\n    <span style=\"color: #436EE5; margin-left: 5px; vertical-align: sub; padding-right: 10px\">Donations: ${donation_amount}</span>\n  </div>\n</div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "c89c3f7f-b640-49e0-a0b0-d8657e878850"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "30px"}, "uuid": "a51c8d31-f12e-436c-a2dc-4b7416a03f17"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_donation_tickets??>", "description": "This condition is used to hide donation ticket from the email template", "label": "is Donation ticket in order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "cdb73211-5e2e-415d-9af3-df0ab783aafd"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnDesktop": false, "hideContentOnMobile": false, "iconHeight": "16px", "iconSpacing": {"padding-bottom": "0px", "padding-left": "5px", "padding-right": "10px", "padding-top": "5px"}, "itemSpacing": "0px"}, "iconsList": {"icons": [{"alt": "", "height": "27px", "href": "", "image": "${imagePrefix}/clock-icon.png", "target": "_self", "text": "${eventStartEndDateTime}", "textPosition": "right", "title": "", "width": "27px"}]}, "style": {"color": "#000000", "font-family": "inherit", "font-size": "22px", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "left"}}, "type": "mailup-bee-newsletter-modules-icons", "uuid": "53abd3f5-76c6-4f8f-a6a1-840ed464138d"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "30px"}, "uuid": "c4e31509-a9c3-4268-84af-725b1dfd3710"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}, "displayCondition": {"label": "Show event date", "description": "Show event date", "before": "<#if isShowEventDate>", "after": "</#if>", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "f06a31c0-b6a6-4054-af5e-5c06efa13ec8"}, {"columns": [{"grid-columns": 2, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "5px", "padding-left": "5px", "padding-right": "5px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Add to:</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "type": "mailup-bee-newsletter-modules-text", "uuid": "7315a790-9e25-45cd-9efd-f72a81fe4a03"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "10px"}, "uuid": "9e90c365-8367-4ef8-94c0-d1c8a354a093"}, {"grid-columns": 2, "modules": [{"descriptor": {"button": {"href": "${addToGoogleCalendar}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"\" data-mce-style=\"\"><p style=\"word-break: break-word;\" data-mce-style=\"word-break: break-word;\">Google</p></div>", "style": {"background-color": "#efefef", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "14px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#436ee5", "font-family": "inherit", "line-height": "120%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "10px", "padding-right": "10px", "padding-top": "5px", "width": "auto", "font-size": "16px", "direction": "ltr"}}, "computedStyle": {"height": 29, "hideContentOnMobile": false, "width": 71}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "center"}}, "type": "mailup-bee-newsletter-modules-button", "uuid": "a1896ce8-093a-46d1-9694-98c438f4b372"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "47fcbda2-5770-4be9-af39-6517ccc64fed"}, {"grid-columns": 2, "modules": [{"descriptor": {"button": {"href": "${addToIcalCalendar}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"\" data-mce-style=\"\"><p style=\"word-break: break-word;\" data-mce-style=\"word-break: break-word;\">iCal</p></div>", "style": {"background-color": "#efefef", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "14px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#436ee5", "font-family": "inherit", "line-height": "120%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "10px", "padding-right": "10px", "padding-top": "5px", "width": "auto", "font-size": "16px", "direction": "ltr"}}, "computedStyle": {"height": 29, "hideContentOnMobile": false, "width": 75}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "center"}}, "type": "mailup-bee-newsletter-modules-button", "uuid": "42660e60-ac82-46f0-b711-df7f7335aea8"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "970c8f94-d8de-4028-8a48-6827b00b5a5a"}, {"grid-columns": 2, "modules": [{"descriptor": {"button": {"href": "${addToYahooCalendar}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"\" data-mce-style=\"\"><p style=\"word-break: break-word;\" data-mce-style=\"word-break: break-word;\">Yahoo</p></div>", "style": {"background-color": "#efefef", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "14px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#436ee5", "font-family": "inherit", "line-height": "120%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "10px", "padding-right": "10px", "padding-top": "5px", "width": "auto", "font-size": "16px", "direction": "ltr"}}, "computedStyle": {"height": 29, "hideContentOnMobile": false, "width": 47}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "center"}}, "type": "mailup-bee-newsletter-modules-button", "uuid": "59953c5a-37de-4deb-b2ee-3a2e731b9ed3"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "09c42f71-fca2-4a51-b412-d6bd0ee5d58e"}, {"grid-columns": 2, "modules": [], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "f39c16a0-2933-4e64-9b56-96f071ee758a"}, {"grid-columns": 2, "modules": [], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "94368bd2-ea5a-4e64-835c-d7bb25be4063"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}, "displayCondition": {"after": "</#if>", "before": "<#if isShowEventDate>", "label": "Show event date", "description": "Show event date", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "f2e165ec-e3fc-44af-9a8e-6b52dcb28834"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnDesktop": false, "hideContentOnMobile": false, "iconHeight": "16px", "iconSpacing": {"padding-bottom": "5px", "padding-left": "5px", "padding-right": "10px", "padding-top": "5px"}, "itemSpacing": "0px"}, "iconsList": {"icons": [{"alt": "", "height": "25px", "href": "", "image": "${imagePrefix}/location-icon.png", "target": "_self", "text": "${eventAddress}", "textPosition": "right", "title": "", "width": "18px"}]}, "style": {"color": "#000000", "font-family": "inherit", "font-size": "22px", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "left"}}, "type": "mailup-bee-newsletter-modules-icons", "uuid": "8e5fa79c-dfdb-4319-85fd-04e60c3c101d"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "30px"}, "uuid": "a643de55-77db-4904-bc06-d129bdecabe6"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if eventAddress?has_content>", "description": "is event address present?", "label": "is event address present?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "3dbeac48-b40a-4c10-bf71-621eb5ae759c"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"button": {"href": "${viewOnMap}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"\" data-mce-style=\"\"><p style=\"word-break: break-word;\" data-mce-style=\"word-break: break-word;\">View on MAP</p></div>", "style": {"background-color": "#efefef", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "14px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#436ee5", "font-family": "inherit", "line-height": "120%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "10px", "padding-right": "10px", "padding-top": "5px", "width": "auto", "font-size": "16px", "direction": "ltr"}}, "computedStyle": {"height": 29, "hideContentOnMobile": false, "width": 115}, "style": {"padding-bottom": "0px", "padding-left": "35px", "padding-right": "0px", "padding-top": "0px", "text-align": "left"}}, "type": "mailup-bee-newsletter-modules-button", "uuid": "891f3fb1-88fe-408c-a3b9-b6dd9b959cfd"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "f5dc0970-e31c-4750-b05d-f8818c2054ba"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if viewOnMap?has_content>", "description": "View on map button", "label": "Show map", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "24dd72d0-81b9-4f79-be67-e1c48c1ad8f8"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"button": {"href": "${eventURL}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"\" data-mce-style=\"\"><p style=\"word-break: break-word;\" data-mce-style=\"word-break: break-word;\"><span style=\"\" data-mce-style=\"\">${joinOrViewEventLabel}</span></p></div>", "style": {"background-color": "#0396ff", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "34px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#ffffff", "font-family": "inherit", "line-height": "200%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "35px", "padding-right": "35px", "padding-top": "5px", "width": "auto", "font-size": "20px", "direction": "ltr"}}, "computedStyle": {"height": 50, "hideContentOnMobile": false, "width": 290}, "style": {"padding-bottom": "5px", "padding-left": "5px", "padding-right": "5px", "padding-top": "5px", "text-align": "center"}}, "type": "mailup-bee-newsletter-modules-button", "uuid": "08ad4b2a-7cda-4f5c-bd4e-0aa78f7bf6b7"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "45px", "padding-left": "5px", "padding-right": "5px", "padding-top": "45px"}, "uuid": "ad3fbe7c-245c-4748-b36b-b9c9db628537"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "c43c35e3-b7e9-48af-9847-8dce89f49306"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\"><strong><span style=\"color: #000000; line-height: 14px;\" data-mce-style=\"color: #000000; line-height: 14px;\">Share this Event</span></strong></span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "type": "mailup-bee-newsletter-modules-text", "uuid": "3c638f16-bc70-4e40-b944-4ecf3928dfd1"}, {"descriptor": {"computedStyle": {"hideContentOnDesktop": false, "hideContentOnMobile": false, "iconHeight": "32px", "iconSpacing": {"padding-bottom": "5px", "padding-left": "5px", "padding-right": "5px", "padding-top": "5px"}, "itemSpacing": "0px"}, "iconsList": {"icons": [{"image": "${imagePrefix}/Linkedin.png", "textPosition": "right", "text": "", "alt": "", "title": "", "href": "${linkedinShare}", "target": "_blank", "width": "45px", "height": "45px"}, {"alt": "", "height": "45px", "href": "${facebookShare}", "image": "${imagePrefix}/facebook-icon.png", "target": "_blank", "text": "", "textPosition": "right", "title": "", "width": "45px"}, {"alt": "", "height": "45px", "href": "${twitterShare}", "image": "${imagePrefix}/twitter-icon.png", "target": "_blank", "text": "", "textPosition": "right", "title": "", "width": "45px"}]}, "style": {"color": "#000000", "font-family": "inherit", "font-size": "14px", "padding-bottom": "15px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "text-align": "center"}}, "type": "mailup-bee-newsletter-modules-icons", "uuid": "71220d97-1040-46e2-b4e1-a4496da61667"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "1px solid #D1D1D1", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "25px"}, "uuid": "60a82969-df21-4998-a76b-0b387f4817dd"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if socialSharingEnabled>", "description": "Show social sharing", "label": "Show social sharing", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "d6cba345-9141-4ddb-8777-a5419fbc8a39"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #BBBBBB", "width": "50%"}}, "style": {"padding-bottom": "20px", "padding-left": "10px", "padding-right": "10px", "padding-top": "20px"}}, "type": "mailup-bee-newsletter-modules-divider", "uuid": "55c5655d-dfe8-4d16-864d-91278bab9ef1"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "5px", "padding-left": "5px", "padding-right": "5px", "padding-top": "15px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\"><strong><span style=\"color: #000000; line-height: 14px;\" data-mce-style=\"color: #000000; line-height: 14px;\">Any Questions?</span></strong></span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "type": "mailup-bee-newsletter-modules-text", "uuid": "8b453e2f-bb00-4e27-aebb-b2577ad04918"}, {"descriptor": {"button": {"href": "${openContactFormUrl}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"\" data-mce-style=\"\"><p style=\"word-break: break-word;\" data-mce-style=\"word-break: break-word;\"><span style=\"\" data-mce-style=\"\">Contact the Organizer</span></p></div>", "style": {"background-color": "#0396ff", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "5px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#ffffff", "font-family": "inherit", "line-height": "120%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "5px", "padding-right": "5px", "padding-top": "5px", "width": "auto", "font-size": "20px", "direction": "ltr"}}, "computedStyle": {"height": 33, "hideContentOnMobile": false, "width": 205}, "style": {"padding-bottom": "30px", "padding-left": "5px", "padding-right": "5px", "padding-top": "5px", "text-align": "center"}}, "type": "mailup-bee-newsletter-modules-button", "uuid": "3c09332a-f441-4cdd-9f54-492bb91461f6"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "uuid": "f9cfdc44-a397-4781-80c8-f8c8d6ffe03a"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "cd25af41-d2bb-4339-ae40-a3c50c20a8d5"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div id=\"order-summary-begining-of-the-list\" style=\"padding-top: 5px;\"></div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "28c49dd0-5401-4e73-8767-8765d21d6796"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "uuid": "8a932d48-e031-4c4d-a171-7339737ca668"}], "container": {"displayCondition": {"after": "end_of_the_div", "before": "order_summary_beginning_of_the_list", "description": "Order Summary beginning of the list (Read only)", "label": "Order Summary beginning of the list (Read only)", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "2156d2dc-da17-43df-ad9f-9164f80fa1f4"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div style=\"text-align:left;color:#316699;height: 44px; font-family: Aria<PERSON>, Helvetica Neue, Helvetica, sans-serif;   font-size: 35px;    font-weight: 600;   line-height: 44px;\">\n  <span style=\"height: 67px;  width: 14px;    background-color: #316699;margin-right:10px;\"> </span>Total\n  <span style=\"float: right; \"> ${total_amount}</span>\n</div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "d554123b-bd75-42b0-82fb-f2cb7a1a9a9e"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "60px"}, "uuid": "f4ab0ac7-5499-45d0-bd53-082a8fc8687a"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_amount_in_decimal gt 0 || discount gt 0>", "description": "Total amount will be shown only if amount or discount is greater than 0", "label": "Show Total amount", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "1f3fc99f-c78a-4d77-8085-5bc8fd1563ba"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><strong><span style=\"font-size: 24px; color: #316699; line-height: 28px;\" data-mce-style=\"font-size: 24px; color: #316699; line-height: 28px;\">Order Summary:</span></strong></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "5cd156c6-75b7-40b7-bd87-fbca7a314d06"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "60px"}, "uuid": "7acb54a1-1d14-46ec-a089-3a5049bfa41d"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "c77dbdb7-8615-479f-a118-d90dda80210a"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; color: #316699; line-height: 16px;\" data-mce-style=\"font-size: 14px; color: #316699; line-height: 16px;\">${free_ticket_name}</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "3fa903d9-b149-4417-a068-4045391f92dd"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "40px"}, "uuid": "377983ec-0b64-4659-9afc-eef3d6beddcd"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "b4dc8255-bc8b-4b09-8894-c8b9de16619e"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "d4f144be-2354-43d3-a200-797ec3d90894"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "c71d8cc8-0809-4dcc-bb7c-307cc9e2a939"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "5726ec0d-d6d2-4058-a624-693cb996efe2"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div style=\"text-align:left;color:#316699;width:100%;\">\n  <div style=\"width: 32%; display: inline-block; float: left; font-size: 14px;\">${purchaser_name}</div>\n  <div style=\"width: 50%; display: inline-block; font-size: 14px;\">${numberOfTicket} x Registration</div>\n  <div style=\"display: inline-block; float: right; font-size: 14px;\"></div>\n</div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "1f5c1d05-97fe-422a-85af-f97ba18fa3d5"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "1aec6b70-f3e7-4ccc-b885-72474042605f"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "17494bcb-3701-44cc-8d40-7f9be11dac9a"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "91d1d983-d6d7-4c80-8808-20edfc42682d"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "09455e39-d055-429e-a110-a4fcee6ef44b"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "083af7a4-a201-4881-8fbc-b21ad0136b1a"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; color: #316699; line-height: 16px;\" data-mce-style=\"font-size: 14px; color: #316699; line-height: 16px;\">${paid_ticket_name}</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "1bf0b882-9ea7-4324-a9fa-787d1057a0f8"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "40px"}, "uuid": "fbb9f47c-1923-4719-bb0c-ce9ae773f00b"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "515d56f6-6367-4f21-9b99-6ab8829026e8"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "dc478b17-47d2-4769-b78a-e34b0ef0af2a"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "c7d8193f-a023-45b2-bf28-038bdd53a91e"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "c8f1af8d-7b41-4c59-8ed4-af317654b965"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div style=\"text-align:left;color:#316699;width: 100%;\">\n  <div style=\"width: 32%; display: inline-block; float: left; font-size: 14px;\">${numberOfTicket} Tickets at ${ticket_price} each</div>\n  <div style=\"width: 50%; display: inline-block; font-size: 14px;min-height:14px;\"></div>\n  <div style=\"display: inline-block; float: right; font-size: 14px;\">${total_ticket_price}</div>\n</div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "1b3ea4ed-aba2-435a-874c-41536e8175ba"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "4e52f5fa-d762-4698-b6e3-1c5dce4dafc2"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "6356a383-0574-47ff-a3a0-396ed0c9e51f"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "8f2f3125-4607-4db6-bb94-51605da584ad"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "2c7cf5c2-bace-48e5-9605-a7c2e403550b"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "e045e99b-cf56-4f67-8c2f-31216bc11bd0"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; color: #316699; line-height: 16px;\" data-mce-style=\"font-size: 14px; color: #316699; line-height: 16px;\">${donation_ticket_name}</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "2dfe7f2a-1459-4050-8eb9-587c751dcaba"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "40px"}, "uuid": "fbb9f47c-1923-4719-bb0c-ce9ae773f00b"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_donation_tickets??>", "description": "This condition is used to hide donation ticket from the email template", "label": "is Donation ticket in order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "a2ef2e99-24ba-46cb-a210-7ad2ca74fc1a"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "a0a69931-43ca-4560-b7ec-d6ffeabd16c7"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "2c7cf5c2-bace-48e5-9605-a7c2e403550b"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_donation_tickets??>", "description": "This condition is used to hide donation ticket from the email template", "label": "is Donation ticket in order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "82186b17-b112-43a3-988c-f0113358872e"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div style=\"text-align:left;color:#316699;width: 100%;\">\n  <div style=\"width: 32%; display: inline-block; float: left; font-size: 14px;\">DONATION</div>\n  <div style=\"width: 20%; display: inline-block; font-size: 14px;min-height:14px;\"></div>\n  <div style=\"display: inline-block; float: right; font-size: 14px;\">${total_donation_amount}</div>\n</div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "5aed5d06-4c36-48e0-b71c-84cf74c7899c"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "4e52f5fa-d762-4698-b6e3-1c5dce4dafc2"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_donation_tickets??>", "description": "This condition is used to hide donation ticket from the email template", "label": "is Donation ticket in order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "6b4d5816-fea2-4bed-b65c-93c556a627c8"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "8bc6759c-028e-4765-b0d5-dfc924dabb34"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "0px"}, "uuid": "2c7cf5c2-bace-48e5-9605-a7c2e403550b"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if total_donation_tickets??>", "description": "This condition is used to hide donation ticket from the email template", "label": "is Donation ticket in order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "16550849-6454-4531-ac89-3cf8c6013910"}, {"columns": [{"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">Discount</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "3589eda0-9bb6-4113-8518-1e36ddbda3f4"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "0px", "padding-top": "30px"}, "uuid": "0a7ea892-a3da-4308-98e8-ad73a2952e8d"}, {"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">${total_discount}</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "756b672b-baa5-4107-8a42-81c5bddef998"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "15px", "padding-top": "30px"}, "uuid": "8bfe52ac-1e94-48d8-89a1-68eb79b5e2d2"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if discount gt 0>", "description": "is discount applied on order?", "label": "is discount applied on order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": false, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "metadata": {"category": 578593, "dateCreated": "2021-05-29T23:05:26.644124Z", "dateModified": "2021-05-29T23:05:33.928458Z", "description": "", "idParent": null, "name": "Order summary donation row", "slug": "order-summary-donation-row", "uuid": "3381665f-d15c-41fe-bc8c-e2504b9aeea6"}, "name": "Order summary donation row", "type": "one-column-empty", "uuid": "8e91796b-b551-47c6-a3ec-3c08aa99df40"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "19d20387-28e4-42a2-8ae8-35d925ecd19d"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "30bfa84e-e5cb-4c66-83ec-5ad978beba22"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if discount gt 0>", "description": "is discount applied on order?", "label": "is discount applied on order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "274334a1-4587-4ef2-8208-b5a2ff2532a6"}, {"columns": [{"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">Processing Fee</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "6e979cb1-e82f-46bb-bae3-b7fdaca57bf4"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "0px", "padding-top": "15px"}, "uuid": "0a7ea892-a3da-4308-98e8-ad73a2952e8d"}, {"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">${total_processing_fee}</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "9285e1b2-9f29-4a6c-a67f-7dd0c20bd141"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "15px", "padding-top": "15px"}, "uuid": "8bfe52ac-1e94-48d8-89a1-68eb79b5e2d2"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if applicationFee??>", "description": "Processing fee", "label": "Processing fee", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": false, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "metadata": {"category": 578593, "dateCreated": "2021-05-29T23:05:26.644124Z", "dateModified": "2021-05-29T23:05:33.928458Z", "description": "", "idParent": null, "name": "Order summary donation row", "slug": "order-summary-donation-row", "uuid": "3381665f-d15c-41fe-bc8c-e2504b9aeea6"}, "name": "Order summary donation row", "type": "one-column-empty", "uuid": "a0ef3bcb-7b6f-44b4-96eb-1decd3ee49fc"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "1ac2e112-f4b8-46bc-a5dd-0c6579dc0650"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "2c7cf5c2-bace-48e5-9605-a7c2e403550b"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if applicationFee??>", "description": "Processing fee", "label": "Processing fee", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "0ed92dcb-8f58-4e03-a049-e37b9ec21a3e"}, {"container": {"displayCondition": {"after": "</#if>", "before": "<#if show_total_sales_tax_fee??>", "description": "Sales Tax", "label": "Sales Tax", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": false, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "columns": [{"uuid": "be93a9b0-b4f3-4cbb-81c3-cba0ae4ea3f2", "grid-columns": 6, "modules": [{"uuid": "d7be403f-3a28-4381-b134-04b02757da96", "align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">Sales Tax</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "0px", "padding-top": "15px"}}, {"uuid": "3c05ba04-7aea-4480-813c-f4791c60b81c", "grid-columns": 6, "modules": [{"uuid": "c44e1599-e20e-401c-9cb0-b2eb2dbf2b63", "align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">${total_sales_fee}</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "15px", "padding-top": "15px"}}], "type": "one-column-empty", "name": "Sales tax row 1", "locked": true, "metadata": {"category": 578593, "dateCreated": "2022-09-29T10:33:42.393641Z", "dateModified": "2022-09-29T10:39:17.381139Z", "description": "", "idParent": null, "name": "Sales tax row 1", "slug": "sales-tax-row-1", "uuid": "a3c34387-858c-445f-92ed-ff92e231c759"}, "uuid": "7d570b2a-eb2e-4a64-8c05-efbf785e62bd"}, {"container": {"displayCondition": {"after": "</#if>", "before": "<#if show_total_sales_tax_fee??>", "description": "Sales Tax", "label": "Sales Tax", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "columns": [{"uuid": "e730d05f-daa3-48de-802a-762691c1af7b", "grid-columns": 12, "modules": [{"uuid": "d4c9f18c-ff01-43ab-b79a-2546340ddfe5", "descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "align": "left"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}}], "type": "one-column-empty", "name": "Sales tax row 2", "locked": true, "metadata": {"category": 578593, "dateCreated": "2022-09-29T10:34:23.993844Z", "dateModified": "2022-09-29T10:39:25.632713Z", "description": "", "idParent": null, "name": "Sales tax row 2", "slug": "sales-tax-row-2", "uuid": "361dc659-b5ba-47fd-af53-929433beed06"}, "uuid": "42874ef8-79b7-42bd-be9e-342f019878c0"}, {"columns": [{"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\">VAT</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "ef2d6db4-58d0-43be-96e0-2b5cb7b01017"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "15px", "padding-right": "0px", "padding-top": "15px"}, "uuid": "5b8f728b-1b52-4848-b6af-dd9b43ebb3ab"}, {"grid-columns": 6, "modules": [{"align": "left", "descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "5px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">${total_vat_fee}</p></div>", "style": {"color": "#316699", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "b292b76f-5ec4-4dee-9a2c-3da60855dd03"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "15px", "padding-top": "15px"}, "uuid": "475e8ed9-b1f3-4a10-a1ae-cd9741f9d0af"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if show_total_vat_tax_fee??>", "description": "VAT", "label": "VAT", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": false, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "metadata": {"category": 578593, "dateCreated": "2022-05-10T17:09:07.028116Z", "dateModified": "2022-05-10T17:09:11.501660Z", "description": "", "idParent": null, "name": "Order summary vat fee row", "slug": "order-summary-vat-fee-row", "uuid": "ac139f87-a064-441a-9d2e-623f596716be"}, "name": "Order summary vat fee row", "type": "one-column-empty", "uuid": "9805244e-7d52-4c6d-9a57-6f75df67083f"}, {"columns": [{"grid-columns": 12, "modules": [{"align": "left", "descriptor": {"computedStyle": {"align": "center", "hideContentOnMobile": false}, "divider": {"style": {"border-top": "3px solid #316699", "height": "1px", "width": "100%"}}, "style": {"padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-divider", "uuid": "542b531b-fa08-4ca0-851b-45de2d2ba68b"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "e3972147-e8c8-4c8a-a565-898221e05760"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if show_total_vat_tax_fee??>", "description": "VAT", "label": "VAT", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "metadata": {"category": 578593, "dateCreated": "2022-05-10T17:09:32.450291Z", "dateModified": "2022-05-10T17:09:34.428930Z", "description": "", "idParent": null, "name": "Order summary vat fee divider", "slug": "order-summary-vat-fee-divider", "uuid": "ed420c7c-3b25-4993-95ae-9b14bd897779"}, "name": "Order summary vat fee divider", "type": "one-column-empty", "uuid": "5fc1d093-67c1-4229-a0c6-df3d5a41ce08"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\"><span style=\"color: #316699; line-height: 14px;\" data-mce-style=\"color: #316699; line-height: 14px;\"><strong><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\">Total ${total_amount}</span></strong></span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "c3da2929-324e-4bf4-8a84-306d157e99f4"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "5de48841-8d1e-41e8-ac08-8714f3b3c214"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if !(unpaidOrderText??) && (!isFree || discount gt 0)>", "description": "is paid or discount greater than 0?", "label": "is paid or discount greater than 0?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "c3da1b31-762b-4b47-97b3-9d6a3153b843"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div style=\"text-align:left;color:#316699;\">\n  <img alt=\"VisaCard\" title=\"${card_type}\" border=\"0\" height=\"23\" width=\"40\" style=\"height: 23px; width: 40px; border:none; color: white; border-radius: 2.59px;margin-right:10px;\" src=\"${imagePrefix}/CreditCard.png\" />\n  <span style=\"vertical-align: super;\"> xxxx ${card_last4} </span>\n</div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "bbc04bb1-00d4-4846-98e8-2e35d9bf415a"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "10px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "1fd27d69-11f3-47ab-88bc-c0dfe29bb699"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if !(unpaidOrderText??) && !isFree>", "description": "is paid ticket? If yes then show last 4 digits of CC", "label": "is paid ticket? If yes then show last 4 digits of CC", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "677ca625-2c2f-4259-a80c-83cc30921a88"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; color: #316699; line-height: 19px;\" data-mce-style=\"font-size: 16px; color: #316699; line-height: 19px;\">${unpaidOrderText}</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "c5795f83-1da1-4bf7-8bd1-5f4fb00e4a22"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "10px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "5de48841-8d1e-41e8-ac08-8714f3b3c214"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if unpaidOrderText??>", "description": "is unPaid order?", "label": "is unPaid order?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "ddc332b3-2779-4326-a44e-a74b06bd64d5"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; color: #316699; line-height: 19px;\" data-mce-style=\"font-size: 16px; color: #316699; line-height: 19px;\">On ${purchase_date}</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": true, "type": "mailup-bee-newsletter-modules-text", "uuid": "f4da0a9d-561a-4273-879b-27513959a17d"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "60px", "padding-left": "15px", "padding-right": "15px", "padding-top": "5px"}, "uuid": "172e2960-374c-44d0-8b77-8d192867af81"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "1df5489d-27a0-4d58-aed8-bc25d6202c99"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "html": {"html": "<div id=\"order-summary-ending-of-the-list\" style=\"padding-top: 5px;\"></div>"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}}, "locked": true, "type": "mailup-bee-newsletter-modules-html", "uuid": "d8c3d423-99c2-48c9-b830-f3e37fdc782f"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "uuid": "8a932d48-e031-4c4d-a171-7339737ca668"}], "container": {"displayCondition": {"after": "order_summary_ending_of_the_list", "before": "start_of_the_div", "description": "Order Summary ending of the list (Read only)", "label": "Order Summary ending of the list (Read only)", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "locked": true, "type": "one-column-empty", "uuid": "5ec8f42e-43f5-4ead-9102-60c1ac2f6051"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"class": "center autowidth", "hideContentOnMobile": false, "width": "211px"}, "image": {"alt": "Add to Apple Wallet", "height": "65px", "href": "${link_for_download_tickets_with_new_template}", "src": "${imagePrefix}/Apple_Wallet_badge.png", "width": "211px"}, "style": {"padding-bottom": "0px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px", "width": "100%"}}, "type": "mailup-bee-newsletter-modules-image", "uuid": "9e16ca2c-82d8-48e0-b189-166cf7c94d08"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "60px", "padding-left": "0px", "padding-right": "0px", "padding-top": "10px"}, "uuid": "bb35c69f-2d79-4c61-84da-e1c650c5f058"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if link_for_download_tickets_with_new_template?? && !only_donation_tickets>", "description": "is add to apple wallet link present?", "label": "is add to apple wallet link present?", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#edf0fd", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "edb4378f-4cf4-4993-a2a6-bc15bcbae8ea"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "40px", "padding-left": "0px", "padding-right": "0px", "padding-top": "0px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 30px; line-height: 36px;\" data-mce-style=\"font-size: 30px; line-height: 36px;\">Hosting Your Own Event?</span></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "120%"}}}, "type": "mailup-bee-newsletter-modules-text", "uuid": "5269b865-df09-4cea-b20c-6371bb7a00c2"}, {"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "50px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\">Join thousands of organizations using Accelevents to manage their events.</span></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "120%"}}}, "type": "mailup-bee-newsletter-modules-text", "uuid": "2fd8d27f-97cf-4601-8dc7-0144d3588221"}, {"descriptor": {"button": {"href": "${getStarted}", "label": "<div class=\"txtTinyMce-wrapper\" style=\"\" data-mce-style=\"\"><p style=\"word-break: break-word;\" data-mce-style=\"word-break: break-word;\"><span style=\"\" data-mce-style=\"\">Get Started for FREE!</span></p></div>", "style": {"background-color": "#0396ff", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-radius": "34px", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "color": "#ffffff", "font-family": "inherit", "line-height": "200%", "max-width": "100%", "padding-bottom": "5px", "padding-left": "20px", "padding-right": "20px", "padding-top": "5px", "width": "auto", "font-size": "20px", "direction": "ltr"}}, "computedStyle": {"height": 50, "hideContentOnMobile": false, "width": 235}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px", "text-align": "center"}}, "type": "mailup-bee-newsletter-modules-button", "uuid": "58896510-c35f-4368-acb6-d13e42d5b1d6"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "45px", "padding-left": "5px", "padding-right": "5px", "padding-top": "60px"}, "uuid": "60b1c797-4e1a-4f54-9dae-aa5e5dc3b6fb"}], "container": {"displayCondition": {"after": "</#if>", "before": "<#if !hide_create_event_button>", "description": "Show hosting new event Info and get started button", "label": "Show hosting new event Info and get started button", "type": "BEE_CUSTOM_DISPLAY_CONDITION"}, "style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "32a1a0b0-82f6-4765-acd0-46aa410bd95b"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"line-height: 14px; font-size: 12px;\" data-mce-style=\"line-height: 14px; font-size: 12px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"color: #ffffff; line-height: 14px;\" data-mce-style=\"color: #ffffff; line-height: 14px;\">This email was sent by: Accelevents, Inc., One Boston Place, Suite 2600, Boston, MA 02108</span></p></div>", "style": {"color": "#393d47", "font-family": "inherit", "line-height": "120%"}}}, "locked": false, "type": "mailup-bee-newsletter-modules-text", "uuid": "76ec4c0d-88a8-4431-a1a9-07ec51dd3c30"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "5px", "padding-left": "5px", "padding-right": "5px", "padding-top": "5px"}, "uuid": "c97f8922-4e7e-47af-a220-110e2083801e"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#1e2137", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "eaa65b71-d6f6-47df-ae2a-2e02593578a1"}, {"columns": [{"grid-columns": 12, "modules": [{"descriptor": {"computedStyle": {"hideContentOnMobile": false}, "style": {"padding-bottom": "10px", "padding-left": "10px", "padding-right": "10px", "padding-top": "10px"}, "text": {"computedStyle": {"linkColor": "#8a3b8f"}, "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">${unsubscribe_link}</span></p></div>", "style": {"color": "#000000", "font-family": "inherit", "line-height": "120%"}}}, "type": "mailup-bee-newsletter-modules-text", "uuid": "a46bfedb-9764-475a-9115-e83ebae30249"}], "style": {"background-color": "transparent", "border-bottom": "0px solid transparent", "border-left": "0px solid transparent", "border-right": "0px solid transparent", "border-top": "0px solid transparent", "padding-bottom": "10px", "padding-left": "0px", "padding-right": "0px", "padding-top": "20px"}, "uuid": "a53a21c0-**************-d72e3ca0cb3f"}], "container": {"style": {"background-color": "transparent", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat"}}, "content": {"computedStyle": {"rowColStackOnMobile": true, "rowReverseColStackOnMobile": false}, "style": {"background-color": "#ffffff", "background-image": "none", "background-position": "top left", "background-repeat": "no-repeat", "color": "#000000", "width": "500px"}}, "type": "one-column-empty", "uuid": "068b39eb-826d-48dc-8eea-ed6d3a5bdbdb"}], "template": {"name": "template-base", "type": "basic", "version": "2.0.0"}, "title": ""}, "comments": {}}