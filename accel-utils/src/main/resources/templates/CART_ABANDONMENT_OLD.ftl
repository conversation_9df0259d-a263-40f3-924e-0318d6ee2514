<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">

    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="https://fonts.googleapis.com/css?family=Open+Sans&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <title>Welcome to Enevtbrite</title>
    <style  type="text/css">
        body {
            margin: 0;
            padding: 0;
            font-family: 'Open Sans';
            font-size: 12px;
        }
        html * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            -ms-box-sizing: border-box;
            box-sizing: border-box;
        }
        ul, li {
            margin: 0;
            padding: 0;
        }
        .container {
            margin: 0 auto;
            width: 100%;
            max-width: 640px;
            background-color: #fafafa;
            padding: 20px;
        }
        .logo {
            width: 150px;
        }
        .logo svg circle, .logo svg path {
            fill: #f05537!important;
        }
		.logo-img {
			width:150%;
		}
        .clearfix { display: block; }
        .clearfix:after {
            visibility: hidden;
            display: block;
            font-size: 0;
            content: " ";
            clear: both;
            height: 0;
        }

        .navigation {
            float: right;
            max-width: 60%;
            margin-top: 8px;
        }
        li {
            list-style: none;
        }
        a {
            text-decoration: none;
        }
        a:hover {
            text-decoration: none;
        }
        .navigation li {
            display: inline-block;
        }
        .navigation li a {
            font-weight: 600;
            padding-left: 20px;
            color: #6f7287;
        }

        footer {
            margin-top: 25px;
            border-top: 1px solid #e6e6e6;
            padding-top: 15px;
            padding-bottom: 15px;
            text-align: center;
        }
        footer li {
            padding-top: 3px;
            padding-bottom: 3px;
            color: #6f7287;
        }
        .wrapper {
            padding: 30px;
            border-radius: 8px;
            background-color: #fff;
            margin-top: 25px;
        }
        .content {
            color: #363a43;
        }
        .content h2 {
            color: #39364f;
        }
        a {
            color: #007bff;
        }
        .card img {
            max-width: 100%;
        }
        .card .card-image {
            width:30%;
            float: left;
        }
        .card .card-content {
            width: 55%;
            float: left;
            padding: 10px;
        }
        .divider {
            height: 1px;
            margin: 35px 0;
            background-color: #e6e6e6 ;
        }
        .actions {
            text-align: center;
        }
        .action {
            background: #ed563e;
            padding: 10px 20px;
            border: none;
            color: #fff;
            border-radius: 3px;
            cursor: pointer;
            margin: 20px 0;
        }
        .action:hover {
            background: #39364f;
        }
		.btn-free {
            background: #00b300;
            padding: 10px 20px;
            border: none;
            color: #fff;
            border-radius: 3px;
            cursor: pointer;
            margin: 20px 0;
        }
        .btn-free:hover {
            background: #00e600;
        }
        .full {
            display: block;
        }
        .price-bar {
            background-color: #eee;
            margin-top: -5px;
            padding: 3px 5px;
        }
        .card-content h4 {
            margin-top: 3px;
            margin-bottom: 2px;
        }
        .dated {
            color: #8e8e8e;
        }
        .address {
            color: #8e8e8e;
            margin-top: 20px;
        }
        .city {
            color: #8e8e8e;
        }
        .center-content {
            text-align: center;
        }
		.icon {
			width:5%;
			margin-bottom:10px;
		}
		.social-icons img {
            height: 25px;
            width: 25px;
            border:none;
            color: white;
            margin-right: 8px;
            cursor: pointer;
            position: relative;
            z-index: 1;
            /*font-size:16px;
            margin-right: 4px;
            padding: 6px;
            text-align: center;
            color: #555;
            border: none;
            cursor: pointer;
            position: relative;
            z-index: 1;
            background-color: none;
            border: 1px solid #555;*/
        }
        /*.social-icons img:hover {
            border: 1px solid #39adff;
            color: #39adff;
        }*/
    </style>

  </head>

  <body>
    <div class="container">
        <header class="clearfix">
            <div class="logo">
				<a href="${eventFullUrl}" class="eds-global-header__logo-link"><img class="logo-img" src="${headerLogo}"></a>
            </div>
            <ul class="navigation">
                <!-- <li><a href="#">Discover</a></li>
                <li><a href="#">Create an event</a></li> -->
            </ul>
        </header>
        <div class="wrapper" role="main">
            <div class="content">
                <div class="top-content">
                    <h2>Hi ${purchaserName},</h2>
                    <p>Are you still interested in <a href="${eventFullUrl}">${eventName}</a>? Don't miss your chance to grab your spot - popular events fill up fast!</p>
                </div>
                <div class="divider"></div>
                <div class="middle-content">
                    <ul class="card clearfix">
                        <li class="card-image">
                            <!-- <img src="https://blogmedia.evbstatic.com/wp-content/uploads/wpmulti/sites/3/2019/10/ronnie-SmartHustle1.jpg"/> -->
                            ${eventLogo}
                            <!-- <span class="full price-bar">$0-$20</span> -->
                        </li>
                        <li class="card-content">
                            <span class="full dated">${eventStartEndDateTime}</span>
                            <h4>${eventName}</h4>

                            <span class="address full">${eventAddress}</span>
                            <!-- <span class="city full">Boston</span> -->
                        </li>
                    </ul>
                    <div class="actions">
                        <a href="${cartUrl}">
                            <button class="action">Complete Your Order</button>
                        </a>
                    </div>
					<div class="divider"></div>
					<div class="top-content">
						<h2>${ownEventLabel}</h2>
						<p>${endMessageLabel}</p>
					</div>
					<#if get_started_label?has_content>
					    <div class="actions">
                            <a href="${signUpUrl}">
                                <button class="btn-free">${get_started_label}</button>
                            </a>
                        </div>
                    </#if>
                </div>
            </div>
        </div>
        <footer>
            <ul>
                <!-- <li>This email was sent to <a href="mailto:<EMAIL>"><EMAIL></a></li> -->
                <li>This email was sent by: ${Sender_Name}</li>
                <li>${Sender_Address} ${Sender_City} ${Sender_State} ${Sender_Zip}</li>
                <li>${unsubscribe_url}</li>
                <!-- <li>Copyright &copy; 2019 Eventbrite. All right reserved.</li> -->
            </ul>
        </footer>
    </div>
  </body>
</html>
