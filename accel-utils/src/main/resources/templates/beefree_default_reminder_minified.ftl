<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
<!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="width=device-width">
<!--[if !mso]><!-->
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<!--<![endif]-->
<title></title>
<!--[if !mso]><!-->
<!--<![endif]-->
<style type="text/css">
body{margin:0;padding:0}table,td,tr{vertical-align:top;border-collapse:collapse}*{line-height:inherit}a[x-apple-data-detectors=true]{color:inherit!important;text-decoration:none!important}
</style>
<style type="text/css" id="media-query">
@media (max-width:770px){.block-grid,.col{min-width:320px!important;max-width:100%!important;display:block!important}.block-grid{width:100%!important}.col{width:100%!important}.col_cont{margin:0 auto}img.fullwidth,img.fullwidthOnMobile{max-width:100%!important}.no-stack .col{min-width:0!important;display:table-cell!important}.no-stack.two-up .col{width:50%!important}.no-stack .col.num2{width:16.6%!important}.no-stack .col.num3{width:25%!important}.no-stack .col.num4{width:33%!important}.no-stack .col.num5{width:41.6%!important}.no-stack .col.num6{width:50%!important}.no-stack .col.num7{width:58.3%!important}.no-stack .col.num8{width:66.6%!important}.no-stack .col.num9{width:75%!important}.no-stack .col.num10{width:83.3%!important}.video-block{max-width:none!important}.mobile_hide{min-height:0;max-height:0;max-width:0;display:none;overflow:hidden;font-size:0}.desktop_hide{display:block!important;max-height:none!important}}
</style>
<style type="text/css" id="icon-media-query">
@media (max-width:770px){.icons-inner{text-align:center}.icons-inner td{margin:0 auto}}
</style>
</head>
<body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #ececec;">
<!--[if IE]><div class="ie-browser"><![endif]-->
<table class="nl-container" style="table-layout: fixed; vertical-align: top; min-width: 320px; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ececec; width: 100%;" cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#ececec" valign="top">
<tbody>
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top;" valign="top">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#ececec"><![endif]--><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div class="img-container center fixedwidth" align="center" style="padding-right: 0px;padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]-->
<div style="font-size:1px;line-height:50px">&nbsp;</div><img class="center fixedwidth" align="center" border="0" src="${AeOrWlLogo}" alt="${regards}" title="${regards}" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: 0; width: 100%; max-width: 263px; display: block;" width="263">
<!--[if mso]></td></tr></table><![endif]-->
</div>
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 60px; padding-bottom: 10px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#000000;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:60px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #000000; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; text-align: center; line-height: 1.2; word-break: break-word; mso-line-height-alt: 14px; margin-top: 0; margin-bottom: 0;"><strong><span style="font-size: 30px; color: #000000;">Hi ${ticket_holder_first_name}, you're ready to go!</span></strong></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
<div class="img-container center fixedwidth fullwidthOnMobile" align="center" style="padding-right: 0px;padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]-->
<div style="font-size:1px;line-height:40px">&nbsp;</div><img class="center fixedwidth fullwidthOnMobile" align="center" border="0" src="${eventLogo}" alt="Event Logo" title="Event Logo" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: 0; width: 100%; max-width: 600px; display: block;" width="600">
<div style="font-size:1px;line-height:40px">&nbsp;</div>
<!--[if mso]></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top: 30px; padding-bottom: 0px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#000000;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:30px;padding-right:15px;padding-bottom:0px;padding-left:15px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #000000; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">${adminText}${headerText}</span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div><#if vatTaxEnabled??><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tbody>
<tr>
<td>
<table class="row-content" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000; width: 750px;" width="750">
<tbody>
<tr>
<td class="column column-1" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-left: 15px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
<table class="divider_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tr>
<td style="padding-bottom:45px;padding-top:45px;">
<div align="center">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tr>
<td class="divider_inner" style="font-size: 1px; line-height: 1px; border-top: 3px solid #BBBBBB;"><span>&#8202;</span></td>
</tr>
</table>
</div>
</td>
</tr>
</table>
<table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
<tr>
<td style="padding-right:5px;">
<div style="font-family: sans-serif">
<div class="txtTinyMce-wrapper" style="font-size: 12px; mso-line-height-alt: 14.399999999999999px; color: #000000; line-height: 1.2; font-family: Arial, Helvetica Neue, Helvetica, sans-serif;">
<p style="margin: 0; font-size: 14px;"><strong><span style="font-size:20px;">${OrganizerVatInformationLabel}</span></strong></p>
<p style="margin: 0; font-size: 14px; mso-line-height-alt: 14.399999999999999px;">&nbsp;</p>
<p style="margin: 0; font-size: 14px;"><span style="font-size:16px;"><span style>${organizerVATName}</span></span></p>
<p style="margin: 0; font-size: 14px; mso-line-height-alt: 14.399999999999999px;">&nbsp;</p>
<p style="margin: 0; font-size: 14px;"><span style="font-size:16px;"><span style>${organizerVATNumber}</span></span></p>
</div>
</div>
</td>
</tr>
</table>
</td>
<td class="column column-2" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-right: 15px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
<table class="divider_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tr>
<td style="padding-bottom:45px;padding-top:45px;">
<div align="center">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tr>
<td class="divider_inner" style="font-size: 1px; line-height: 1px; border-top: 3px solid #BBBBBB;"><span>&#8202;</span></td>
</tr>
</table>
</div>
</td>
</tr>
</table>
<table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
<tr>
<td style="padding-left:5px;">
<div style="font-family: sans-serif">
<div class="txtTinyMce-wrapper" style="font-size: 12px; mso-line-height-alt: 14.399999999999999px; color: #000000; line-height: 1.2; font-family: Arial, Helvetica Neue, Helvetica, sans-serif;">
<p style="margin: 0; font-size: 14px; text-align: right;"><strong><span style="font-size:20px;">${BuyerVatInformationLabel}</span></strong></p>
<p style="margin: 0; font-size: 14px; text-align: right; mso-line-height-alt: 14.399999999999999px;">&nbsp;</p>
<p style="margin: 0; font-size: 14px; text-align: right;"><span style="font-size:16px;"><span style>${buyerOrganizationName}</span></span></p>
<p style="margin: 0; font-size: 14px; text-align: right; mso-line-height-alt: 14.399999999999999px;">&nbsp;</p>
<p style="margin: 0; font-size: 14px; text-align: right;"><span style="font-size:16px;"><span style>${buyerVATNumber}</span></span></p>
</div>
</div>
</td>
</tr>
</table>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table></#if><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 15px; padding-left: 15px;">
<!--<![endif]-->
<table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
<tbody>
<tr style="vertical-align: top;" valign="top">
<td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 40px; padding-right: 0px; padding-bottom: 10px; padding-left: 0px;" valign="top">
<table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 3px solid #BBBBBB; width: 100%;" align="center" role="presentation" valign="top">
<tbody>
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top: 40px; padding-bottom: 5px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#4a4a4a;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:40px;padding-right:0px;padding-bottom:5px;padding-left:0px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #4a4a4a; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;"><strong><span style="font-size: 30px;">Ordered By: ${purchaser_name} </span></strong><span style="font-size: 20px; color: #0068a5;">( ${purchaser_email}<a href="mailto:<EMAIL>" target="_blank" rel="noopener" style="color: #0068a5;"></a>&nbsp;)</span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top: 5px; padding-bottom: 0px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#393d47;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:5px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #393d47; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 20px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 20px; color: #0068a5;">Order #${order_number}</span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div><#if total_tickets??><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:45px; padding-bottom:0px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:45px; padding-bottom:0px; padding-right: 15px; padding-left: 15px;">
<!--<![endif]-->
<table width="100%" cellpadding="0" cellspacing="0" role="presentation" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" valign="top">
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; text-align: left;" align="left" valign="top">
<!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:NaNpx;padding-right:NaNpx;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
<!--[if !vml]><!-->
<table class="icons-inner" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block; margin-right: -4px; padding-left: NaNpx; padding-right: NaNpx;" cellpadding="0" cellspacing="0" role="presentation" valign="top">
<!--<![endif]-->
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; text-align: center; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 10px;" align="center" valign="top"><img class="icon" alt src="${image_prefix}email_template_images/Ticket-icon.png" height="16" width="null" align="center" style="border:0;"></td>
<td style="word-break: break-word; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; font-size: 22px; color: #000000; vertical-align: middle; letter-spacing: undefined;" valign="middle">${total_tickets} x Ticket</td>
</tr>
</table>
</td>
</tr>
</table>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><#if total_tickets??><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:0px; padding-bottom:5px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:0px; padding-bottom:5px; padding-right: 15px; padding-left: 15px;">
<!--<![endif]-->
<div style="font-size:16px;font-family:Arial, Helvetica Neue, Helvetica, sans-serif">
<#list dtoForDisplay as display>
<div style="width: fit-content;display: inline-block;border-radius: 13px; background-color: #EFEFEF; text-align: center; margin-left: 30px;margin-top:10px;">
<span style="display: inline-block; min-width: 10px; padding: 3px 7px; font-size: 12px; font-weight: 700; line-height: 1; color: #fff; text-align: center; white-space: nowrap; vertical-align: middle; background-color: #777; border-radius: 10px; margin-top: 3px;">${display.numberOfTicketPerTable}</span>
<#if display.ticketingTypePrice ==0>
<span style="color: #436EE5; margin-left: 5px; vertical-align: sub; padding-right: 10px">${display.ticketTypeName}</span>
<#else>
<span style="color: #436EE5; margin-left: 5px; vertical-align: sub; padding-right: 10px">${display.ticketTypeName}: ${event_currency}${display.ticketingTypePrice}</span>
</#if>
</div>
</#list>
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><#if total_donation_tickets??><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:30px; padding-bottom:0px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:30px; padding-bottom:0px; padding-right: 15px; padding-left: 15px;">
<!--<![endif]-->
<table width="100%" cellpadding="0" cellspacing="0" role="presentation" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" valign="top">
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; text-align: left;" align="left" valign="top">
<!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:NaNpx;padding-right:NaNpx;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
<!--[if !vml]><!-->
<table class="icons-inner" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block; margin-right: -4px; padding-left: NaNpx; padding-right: NaNpx;" cellpadding="0" cellspacing="0" role="presentation" valign="top">
<!--<![endif]-->
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; text-align: center; padding-top: 5px; padding-bottom: 10px; padding-left: 5px; padding-right: 10px;" align="center" valign="top"><img class="icon" alt src="${image_prefix}email_template_images/donation-icon.png" height="16" width="null" align="center" style="border:0;"></td>
<td style="word-break: break-word; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; font-size: 22px; color: #000000; vertical-align: middle; letter-spacing: normal;" valign="middle">Donation</td>
</tr>
</table>
</td>
</tr>
</table>
<div style="font-size:16px;text-align:center;font-family:Arial, Helvetica Neue, Helvetica, sans-serif">
<div style="text-align:left;">
<div style="width: fit-content;display: inline-block;border-radius: 13px; background-color: #EFEFEF; text-align: center; margin-left: 30px;">
<span style="display: inline-block; min-width: 10px; padding: 3px 7px; font-size: 12px; font-weight: 700; line-height: 1; color: #fff; text-align: center; white-space: nowrap; vertical-align: middle; background-color: #777; border-radius: 10px; margin-top: 3px;">${total_donation_tickets}</span>
<span style="color: #436EE5; margin-left: 5px; vertical-align: sub; padding-right: 10px">Donations: ${donation_amount}</span>
</div>
</div>
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><#if isShowEventDate><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:30px; padding-bottom:5px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:30px; padding-bottom:5px; padding-right: 15px; padding-left: 15px;">
<!--<![endif]-->
<table width="100%" cellpadding="0" cellspacing="0" role="presentation" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" valign="top">
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; text-align: left;" align="left" valign="top">
<!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:NaNpx;padding-right:NaNpx;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
<!--[if !vml]><!-->
<table class="icons-inner" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block; margin-right: -4px; padding-left: NaNpx; padding-right: NaNpx;" cellpadding="0" cellspacing="0" role="presentation" valign="top">
<!--<![endif]-->
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; text-align: center; padding-top: 5px; padding-bottom: 0px; padding-left: 5px; padding-right: 10px;" align="center" valign="top"><img class="icon" alt src="${image_prefix}email_template_images/clock-icon.png" height="16" width="null" align="center" style="border:0;"></td>
<td style="word-break: break-word; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; font-size: 22px; color: #000000; vertical-align: middle; letter-spacing: undefined;" valign="middle">${eventStartEndDateTime}</td>
</tr>
</table>
</td>
</tr>
</table>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><#if isShowEventDate><div style="background-color:transparent;">
<div class="block-grid six-up" style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="125" style="background-color:#ffffff;width:125px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:10px; padding-bottom:5px;"><![endif]-->
<div class="col num2" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 124px; width: 125px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:10px; padding-bottom:5px; padding-right: 15px; padding-left: 15px;">
<!--<![endif]-->
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px; padding-left: 5px; padding-top: 0px; padding-bottom: 5px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#393d47;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:0px;padding-right:5px;padding-bottom:5px;padding-left:5px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #393d47; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Add to:</span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td><td align="center" width="125" style="background-color:#ffffff;width:125px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num2" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 124px; width: 125px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div class="button-container" align="center" style="padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px" align="center"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${addToGoogleCalendar}" style="height:21pt;width:68.25pt;v-text-anchor:middle;" arcsize="50%" stroke="false" fillcolor="#efefef"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#436ee5; font-family:Arial, sans-serif; font-size:16px"><![endif]--><a href="${addToGoogleCalendar}" target="_blank" style="-webkit-text-size-adjust: none; text-decoration: none; display: inline-block; color: #436ee5; background-color: #efefef; border-radius: 14px; -webkit-border-radius: 14px; -moz-border-radius: 14px; width: auto; width: auto; border-top: 1px solid #efefef; border-right: 1px solid #efefef; border-bottom: 1px solid #efefef; border-left: 1px solid #efefef; padding-top: 5px; padding-bottom: 5px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;"><span style="padding-left:10px;padding-right:10px;font-size:16px;display:inline-block;letter-spacing:undefined;"><span style="font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px;">Google</span></span></a>
<!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td><td align="center" width="125" style="background-color:#ffffff;width:125px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num2" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 124px; width: 125px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div class="button-container" align="center" style="padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px" align="center"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${addToIcalCalendar}" style="height:21pt;width:50.25pt;v-text-anchor:middle;" arcsize="50%" stroke="false" fillcolor="#efefef"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#436ee5; font-family:Arial, sans-serif; font-size:16px"><![endif]--><a href="${addToIcalCalendar}" target="_blank" style="-webkit-text-size-adjust: none; text-decoration: none; display: inline-block; color: #436ee5; background-color: #efefef; border-radius: 14px; -webkit-border-radius: 14px; -moz-border-radius: 14px; width: auto; width: auto; border-top: 1px solid #efefef; border-right: 1px solid #efefef; border-bottom: 1px solid #efefef; border-left: 1px solid #efefef; padding-top: 5px; padding-bottom: 5px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;"><span style="padding-left:10px;padding-right:10px;font-size:16px;display:inline-block;letter-spacing:undefined;"><span style="font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px;">iCal</span></span></a>
<!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td><td align="center" width="125" style="background-color:#ffffff;width:125px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num2" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 124px; width: 125px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div class="button-container" align="center" style="padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px" align="center"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${addToYahooCalendar}" style="height:21pt;width:63.75pt;v-text-anchor:middle;" arcsize="50%" stroke="false" fillcolor="#efefef"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#436ee5; font-family:Arial, sans-serif; font-size:16px"><![endif]--><a href="${addToYahooCalendar}" target="_blank" style="-webkit-text-size-adjust: none; text-decoration: none; display: inline-block; color: #436ee5; background-color: #efefef; border-radius: 14px; -webkit-border-radius: 14px; -moz-border-radius: 14px; width: auto; width: auto; border-top: 1px solid #efefef; border-right: 1px solid #efefef; border-bottom: 1px solid #efefef; border-left: 1px solid #efefef; padding-top: 5px; padding-bottom: 5px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;"><span style="padding-left:10px;padding-right:10px;font-size:16px;display:inline-block;letter-spacing:undefined;"><span style="font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px;">Yahoo</span></span></a>
<!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td><td align="center" width="125" style="background-color:#ffffff;width:125px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num2" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 124px; width: 125px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div></div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td><td align="center" width="125" style="background-color:#ffffff;width:125px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num2" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 124px; width: 125px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div></div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><#if eventAddress?has_content><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:30px; padding-bottom:5px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:30px; padding-bottom:5px; padding-right: 15px; padding-left: 15px;">
<!--<![endif]-->
<table width="100%" cellpadding="0" cellspacing="0" role="presentation" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" valign="top">
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; text-align: left;" align="left" valign="top">
<!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:NaNpx;padding-right:NaNpx;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
<!--[if !vml]><!-->
<table class="icons-inner" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block; margin-right: -4px; padding-left: NaNpx; padding-right: NaNpx;" cellpadding="0" cellspacing="0" role="presentation" valign="top">
<!--<![endif]-->
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; text-align: center; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 10px;" align="center" valign="top"><img class="icon" alt src="${image_prefix}email_template_images/location-icon.png" height="16" width="null" align="center" style="border:0;"></td>
<td style="word-break: break-word; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; font-size: 22px; color: #000000; vertical-align: middle; letter-spacing: undefined;" valign="middle">${eventAddress}</td>
</tr>
</table>
</td>
</tr>
</table>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><#if viewOnMap?has_content><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div class="button-container" align="left" style="padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:35px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 35px" align="left"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${viewOnMap}" style="height:21pt;width:101.25pt;v-text-anchor:middle;" arcsize="50%" stroke="false" fillcolor="#efefef"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#436ee5; font-family:Arial, sans-serif; font-size:16px"><![endif]--><a href="${viewOnMap}" target="_blank" style="-webkit-text-size-adjust: none; text-decoration: none; display: inline-block; color: #436ee5; background-color: #efefef; border-radius: 14px; -webkit-border-radius: 14px; -moz-border-radius: 14px; width: auto; width: auto; border-top: 1px solid #efefef; border-right: 1px solid #efefef; border-bottom: 1px solid #efefef; border-left: 1px solid #efefef; padding-top: 5px; padding-bottom: 5px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;"><span style="padding-left:10px;padding-right:10px;font-size:16px;display:inline-block;letter-spacing:undefined;"><span style="font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px;">View on MAP</span></span></a>
<!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px; padding-left: 5px; padding-top:45px; padding-bottom:45px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:45px; padding-bottom:45px; padding-right: 5px; padding-left: 5px;">
<!--<![endif]-->
<div class="button-container" align="center" style="padding-top:5px;padding-right:5px;padding-bottom:5px;padding-left:5px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px" align="center"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${eventURL}" style="height:37.5pt;width:270pt;v-text-anchor:middle;" arcsize="68%" stroke="false" fillcolor="#0396ff"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#ffffff; font-family:Arial, sans-serif; font-size:20px"><![endif]--><a href="${eventURL}" target="_blank" style="-webkit-text-size-adjust: none; text-decoration: none; display: inline-block; color: #ffffff; background-color: #0396ff; border-radius: 34px; -webkit-border-radius: 34px; -moz-border-radius: 34px; width: auto; width: auto; border-top: 1px solid #0396ff; border-right: 1px solid #0396ff; border-bottom: 1px solid #0396ff; border-left: 1px solid #0396ff; padding-top: 5px; padding-bottom: 5px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;"><span style="padding-left:35px;padding-right:35px;font-size:20px;display:inline-block;letter-spacing:undefined;"><span style="font-size: 16px; line-height: 2; word-break: break-word; mso-line-height-alt: 32px;"><span style="font-size: 20px; line-height: 40px;" data-mce-style="font-size: 20px; line-height: 40px;">${joinOrViewEventLabel}</span></span></span></a>
<!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div><#if socialSharingEnabled><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 1px solid #D1D1D1; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:25px; padding-bottom:0px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:1px solid #D1D1D1; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:25px; padding-bottom:0px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#393d47;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #393d47; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 20px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 20px;"><strong><span style="color: #000000;">Share this Event</span></strong></span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
<table width="100%" cellpadding="0" cellspacing="0" role="presentation" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" valign="top">
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; padding-top: 0px; padding-right: 0px; padding-bottom: 15px; padding-left: 0px;" valign="top">
<table cellpadding="0" cellspacing="0" role="presentation" align="center" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" valign="top">
<tr style="vertical-align: top;" valign="top">
<td style="text-align:center;padding-top:5px;padding-bottom:5px;padding-left:5px;padding-right:5px;"><a href="${linkedinShare}"><img class="icon" src="${image_prefix}email_template_images/Linkedin.png" alt height="32" width="32" align="center" style="display: block; height: auto; border: 0;"></a></td>
<td style="word-break: break-word; vertical-align: top; text-align: center; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 5px;" align="center" valign="top"><a href="${facebookShare}"><img class="icon" alt src="${image_prefix}email_template_images/facebook-icon.png" height="32" width="null" align="center" style="border:0;"></a></td>
<td style="word-break: break-word; vertical-align: top; text-align: center; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 5px;" align="center" valign="top"><a href="${twitterShare}"><img class="icon" alt src="${image_prefix}email_template_images/twitter-icon.png" height="32" width="null" align="center" style="border:0;"></a></td>
</tr>
</table>
</td>
</tr>
</table>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:10px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:10px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
<tbody>
<tr style="vertical-align: top;" valign="top">
<td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 20px; padding-right: 10px; padding-bottom: 20px; padding-left: 10px;" valign="top">
<table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="50%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 3px solid #BBBBBB; width: 50%;" align="center" role="presentation" valign="top">
<tbody>
<tr style="vertical-align: top;" valign="top">
<td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px; padding-left: 5px; padding-top: 15px; padding-bottom: 5px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#393d47;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:15px;padding-right:5px;padding-bottom:5px;padding-left:5px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #393d47; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 20px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 20px;"><strong><span style="color: #000000;">Any Questions?</span></strong></span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
<div class="button-container" align="center" style="padding-top:5px;padding-right:5px;padding-bottom:30px;padding-left:5px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 5px; padding-right: 5px; padding-bottom: 30px; padding-left: 5px" align="center"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${openContactFormUrl}" style="height:24.75pt;width:161.25pt;v-text-anchor:middle;" arcsize="16%" stroke="false" fillcolor="#0396ff"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#ffffff; font-family:Arial, sans-serif; font-size:20px"><![endif]--><a href="${openContactFormUrl}" target="_blank" style="-webkit-text-size-adjust: none; text-decoration: none; display: inline-block; color: #ffffff; background-color: #0396ff; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px; width: auto; width: auto; border-top: 1px solid #0396ff; border-right: 1px solid #0396ff; border-bottom: 1px solid #0396ff; border-left: 1px solid #0396ff; padding-top: 5px; padding-bottom: 5px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;"><span style="padding-left:5px;padding-right:5px;font-size:20px;display:inline-block;letter-spacing:undefined;"><span style="font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px;"><span style="font-size: 20px; line-height: 24px;" data-mce-style="font-size: 20px; line-height: 24px;">Contact the Organizer</span></span></span></a>
<!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div>
<div>
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#edf0fd;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 15px; padding-left: 15px; padding-top:20px; padding-bottom:5px;"><![endif]-->
<div style="min-width: 320px; max-width: 750px; color: #316699; background-color: #edf0fd; Margin: 0 auto;">
<div style="padding: 0px 15px">
<#if total_amount_in_decimal gt 0 || discount gt 0>
<div style="height: 44px;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;font-size: 35px;font-weight: 600;line-height:44px;padding-top:50px;">
<span style="height: 67px; width: 14px;background-color: #316699;margin-right:10px;">&nbsp;</span>Total
<span style="float: right; "> ${total_amount}</span>
</div>
</#if>
<div style="height:30px; color:#316699; font-family:Arial, Helvetica Neue, Helvetica, sans-serif;font-size:25px;font-weight:600;line-height:30px;padding-top:60px;">Order Summary:</div>
<#list dataForTickets?keys as ticketDesc>
<div style="font-size: 18px;padding-top:40px;">${ticketDesc}</div>
<div style="padding-top:10px;">
<div style="box-sizing: border-box; height: 2px; border: 1px solid #316699; margin: auto;"></div>
</div>
<#assign seq =dataForTickets[ticketDesc]>
<#assign addedIntoFree=true>
<#assign addedIntoPaid=true>
<#list seq as test1>
<#if test1.ticketType=='FREE' || test1.price ==0>
<#if test1.holderName??>
<div style="width: 100%; padding-top:20px;">
<div style="width: 35%; height : 100%; display: inline-block; float: left; font-size: 14px;">${test1.holderName}</div>
<div style="width: 50%; display: inline-block; font-size: 14px;min-height:16px;"></div>
<div style="display: inline-block; float: right; font-size: 14px;"></div>
</div>
<#else>
<#if addedIntoFree=true>
<#assign addedIntoFree=false>
<div style="width: 100%; padding-top:20px;">
<div style="width: 35%; height: 100%;display: inline-block; float: left; font-size: 14px;">${test1.purchaserName}</div>
<div style="width: 50%; display: inline-block; font-size: 14px;">${seq?size} x Registration</div>
<div style="display: inline-block; float: right; font-size: 14px;"></div>
</div>
</#if>
</#if>
<#elseif test1.ticketType=='PAID'>
<#if test1.holderName??>
<div style="width: 100%; padding-top:20px;">
<div style="width: 35%; height: 100%; display: inline-block; float: left; font-size: 14px;">${test1.holderName}</div>
<#if test1.seatNumber??>
<div style="width: 50%; display: inline-block; font-size: 14px;">${test1.seatNumber}</div>
<#else>
<div style="width: 50%; display: inline-block; font-size: 14px;min-height:16px;"></div>
</#if>
<div style="display: inline-block; float: right; font-size: 14px;">${event_currency}${test1.price?string("0.00")}</div>
</div>
<#else>
<#if test1.seatNumber??>
<div style="width: 100%; padding-top:20px;">
<div style="width: 32%; display: inline-block; float: left; font-size: 14px;">${test1.seatNumber}</div>
<div style="width: 50%; display: inline-block; font-size: 14px;min-height:16px;"></div>
<div style="display: inline-block; float: right; font-size: 14px;">${event_currency}${test1.price?string("0.00")}</div>
</div>
<#else>
<#if addedIntoPaid=true>
<#assign addedIntoPaid=false>
<#assign priceOfOneTicket=test1.price>
<#assign bundleTypeTicketPrice=test1.bundleTicketTypePrice>
<#assign autoAssignedNumber=test1.autoAssignedNumber>
<#assign numberOfTicket=seq?size>
<div style="width: 100%; padding-top:20px;">
<div style="width: 32%; display: inline-block; float: left; font-size: 14px;">${numberOfTicket} Tickets at ${event_currency}${priceOfOneTicket} each ${autoAssignedNumber}</div>
<div style="width: 50%; display: inline-block; font-size: 14px;min-height:16px;"></div>
<div style="display: inline-block; float: right; font-size: 14px;">${event_currency}${(bundleTypeTicketPrice * numberOfTicket)?string("0.00")}</div>
</div>
</#if>
</#if>
</#if>
<#elseif test1.ticketType=='DONATION'>
<div style="width: 100%; padding-top:20px;">
<div style="width: 32%; display: inline-block; float: left; font-size: 14px;">DONATION</div>
<div style="width: 50%; display: inline-block; font-size: 14px;min-height:16px;"></div>
<div style="display: inline-block; float: right; font-size: 14px;">${event_currency}${test1.price?string("0.00")}</div>
</div>
</#if>
</#list>
<div style="padding-top:20px;">
<div style="box-sizing: border-box; height: 2px; border: 1px solid #316699; margin: auto;"></div>
</div>
</#list>
<#if discount gt 0>
<div style="font-size: 14px;padding-top:30px;">Discount
<span style="float: right;">${total_discount}</span>
</div>
<div style="padding-top:10px;">
<div style="box-sizing: border-box; height: 2px; border: 1px solid #316699; margin: auto;"></div>
</div>
</#if>
<#if applicationFee??>
<div style="font-size: 14px;padding-top:30px;">Processing Fee
<span style="float: right;">${total_processing_fee}</span>
</div>
<div style="padding-top:10px;">
<div style="box-sizing: border-box; height: 2px; border: 1px solid #316699; margin: auto;"></div>
</div>
</#if>
<#if show_total_sales_tax_fee??>
<div style="font-size: 14px;padding-top:30px;">Sales Tax
<span style="float: right;">${total_sales_fee}</span>
</div>
<div style="padding-top:10px;">
<div style="box-sizing: border-box; height: 2px; border: 1px solid #316699; margin: auto;"></div>
</div>
</#if>
<#if show_total_vat_tax_fee??>
<div style="font-size: 14px;padding-top:30px;">VAT
<span style="float: right;">${total_vat_fee}</span>
</div>
<div style="padding-top:10px;">
<div style="box-sizing: border-box; height: 2px; border: 1px solid #316699; margin: auto;"></div>
</div>
</#if>
<#if unpaidOrderText??>
<div style="text-align: left;">${unpaidOrderText}</div>
<#else>
<#if !isFree || discount gt 0>
<div style="height: 24px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif;font-size: 20px;font-weight: bold;line-height: 24px;text-align: right;padding-top:20px;"> Total ${total_amount}</div>
</#if>
<#if !isFree>
<div style="font-size:16px;text-align:center;font-family:Arial, Helvetica Neue, Helvetica, sans-serif">
<div style="text-align:left;color:#316699;">
<img role="social-icon" alt="VisaCard" title="${card_type}" border="0" height="23" width="40" style="height: 23px; width: 40px; border:none; color: white; border-radius: 2.59px;margin-right:10px;" src="${card_logo}" />
<span style="vertical-align: super;"> xxxx ${card_last4} </span>
</div>
</div>
</#if>
<div style="height: 60px; color: #316699; font-family: Arial, Helvetica Neue, Helvetica, sans-serif;font-size: 16px;line-height: 20px;padding-top:10px;">On ${purchase_date}</div>
</#if>
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
<#if link_for_download_tickets_with_new_template?? && !only_donation_tickets><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #edf0fd;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#edf0fd;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#edf0fd"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#edf0fd;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:10px; padding-bottom:60px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:10px; padding-bottom:60px; padding-right: 0px; padding-left: 0px;">
<!--<![endif]-->
<div class="img-container center autowidth" align="center" style="padding-right: 0px;padding-left: 0px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]--><a href="${link_for_download_tickets_with_new_template}" target="_blank" style="outline:none" tabindex="-1"><img class="center autowidth" align="center" border="0" src="${image_prefix}email_template_images/Apple_Wallet_badge.png" alt="Add to Apple Wallet" title="Add to Apple Wallet" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: 0; width: 100%; max-width: 211px; display: block;" width="211"></a>
<!--[if mso]></td></tr></table><![endif]-->
</div>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><#if !hide_create_event_button && (ownEventLabel?has_content || endMessageLabel?has_content || get_started_label?has_content)><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #ffffff;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#ffffff;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#ffffff"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#ffffff;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px; padding-left: 5px; padding-top:60px; padding-bottom:45px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:60px; padding-bottom:45px; padding-right: 5px; padding-left: 5px;">
<!--<![endif]-->
<#if ownEventLabel?has_content><!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top: 0px; padding-bottom: 40px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#000000;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:0px;padding-right:0px;padding-bottom:40px;padding-left:0px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #000000; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 30px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 36px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 30px;">Hosting Your Own Event?</span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]--></#if>
<#if endMessageLabel?has_content><!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 50px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#000000;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:50px;padding-left:10px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #000000; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 20px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 20px;">Join thousands of organizations using Accelevents to manage their events.</span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]--></#if>
<#if get_started_label?has_content><div class="button-container" align="center" style="padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px" align="center"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${getStarted}" style="height:37.5pt;width:206.25pt;v-text-anchor:middle;" arcsize="68%" stroke="false" fillcolor="#0396ff"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#ffffff; font-family:Arial, sans-serif; font-size:20px"><![endif]--><a href="${getStarted}" target="_blank" style="-webkit-text-size-adjust: none; text-decoration: none; display: inline-block; color: #ffffff; background-color: #0396ff; border-radius: 34px; -webkit-border-radius: 34px; -moz-border-radius: 34px; width: auto; width: auto; border-top: 1px solid #0396ff; border-right: 1px solid #0396ff; border-bottom: 1px solid #0396ff; border-left: 1px solid #0396ff; padding-top: 5px; padding-bottom: 5px; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; text-align: center; mso-border-alt: none; word-break: keep-all;"><span style="padding-left:20px;padding-right:20px;font-size:20px;display:inline-block;letter-spacing:undefined;"><span style="font-size: 16px; line-height: 2; word-break: break-word; mso-line-height-alt: 32px;"><span style="font-size: 20px; line-height: 40px;" data-mce-style="font-size: 20px; line-height: 40px;">Get Started for FREE!</span></span></span></a>
<!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
</div></#if>
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div></#if><div style="background-color:transparent;">
<div class="block-grid " style="min-width: 320px; max-width: 750px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #1e2137;">
<div style="border-collapse: collapse;display: table;width: 100%;background-color:#1e2137;">
<!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:750px"><tr class="layout-full-width" style="background-color:#1e2137"><![endif]-->
<!--[if (mso)|(IE)]><td align="center" width="750" style="background-color:#1e2137;width:750px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px; padding-left: 5px; padding-top:5px; padding-bottom:5px;"><![endif]-->
<div class="col num12" style="min-width: 320px; max-width: 750px; display: table-cell; vertical-align: top; width: 750px;">
<div class="col_cont" style="width:100% !important;">
<!--[if (!mso)&(!IE)]><!-->
<div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 5px; padding-left: 5px;">
<!--<![endif]-->
<!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Arial, sans-serif"><![endif]-->
<div style="color:#393d47;font-family:Arial, Helvetica Neue, Helvetica, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
<div class="txtTinyMce-wrapper" style="line-height: 1.2; font-size: 12px; color: #393d47; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 14px;">
<p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;"><span style="color: #ffffff;">This email was sent by: ${Sender_Name} ${Sender_Address} ${Sender_City} ${Sender_State} ${Sender_Zip}</span></p>
</div>
</div>
<!--[if mso]></td></tr></table><![endif]-->
<!--[if (!mso)&(!IE)]><!-->
</div>
<!--<![endif]-->
</div>
</div>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
<!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
</div>
</div>
</div>
<table class="row row-43" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tbody>
<tr>
<td>
<table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000;" width="750">
<tbody>
<tr>
<th class="column" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-top: 20px; padding-bottom: 10px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
<table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
<tr>
<td>
<div style="font-family: sans-serif">
<div style="font-size: 14px; mso-line-height-alt: 16.8px; color: #000000; line-height: 1.2; font-family: Arial, Helvetica Neue, Helvetica, sans-serif;">
<p style="margin: 0; text-align: center;"><span style="font-size:16px;">${unsubscribe_link}</span></p>
</div>
</div>
</td>
</tr>
</table>
</th>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<!--[if (mso)|(IE)]></td></tr></table><![endif]-->
</td>
</tr>
</tbody>
</table>
<!--[if (IE)]></div><![endif]-->
</body>
</html>