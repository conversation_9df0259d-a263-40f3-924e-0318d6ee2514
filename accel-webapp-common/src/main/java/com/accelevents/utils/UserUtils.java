package com.accelevents.utils;

import com.accelevents.common.dto.OrganizerDto;
import com.accelevents.common.dto.RedirectUrlDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.Staff;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.dto.AccessTokenModel;
import com.accelevents.dto.CustomUserDetail;
import com.accelevents.dto.LoginUserWLOrgEventDetailsDto;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROOrganizerService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.security.CustomUserDetailsService;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static com.accelevents.enums.UserRole.*;
import static com.accelevents.utils.Constants.NOT_AUTHORIZE;
import static com.accelevents.utils.Constants.SLASH_U_PROFILE;

@Component
public class UserUtils {

	private static final Logger log = LoggerFactory.getLogger(UserUtils.class);

    @Autowired
	private CustomUserDetailsService customUserDetailsService;
    @Autowired
	private ROStaffService roStaffService;
	@Autowired
	private ROEventService roEventService;
	@Autowired
	private ROWhiteLabelService roWhiteLabelService;
    @Autowired
    private ROOrganizerService roOrganizerService;
    @Autowired
    private GamificationCacheStoreService gamificationCacheStoreService;

    private static final String EVENT_URL_WITH_CURLY_BRACES = "event url {}";

    public User validateUser(Authentication auth){
		User user = getUser(auth);
		if (user != null) {
			return user;
		} else {
			throw new AuthorizationException(NOT_AUTHORIZE);
		}
	}

	public void forceLoginToUser(User user) {
		// Direct Login To User
		String userName = null;
		if (user.getEmail() == null || user.getEmail().isEmpty()) {
			userName = "+" + user.getCountryCode().getCode() + user.getPhoneNumber();
		} else {
			userName = user.getEmail();
		}
		UserDetails userDetails = customUserDetailsService.loadUserByUsername(userName);
		Authentication auth = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
		SecurityContextHolder.getContext().setAuthentication(auth);

	}

    public String getUserRoleByAuth(Authentication auth) {
        List<StaffRole> staffRoles = null;
        if (auth != null) {
            CustomUserDetail customUserDetail = (CustomUserDetail) auth.getPrincipal();
            if (customUserDetail == null || customUserDetail.getUser() == null) {
                throw new AuthorizationException(Constants.NOT_AUTHORIZE);
            }

            if (customUserDetail.getAuthorities().contains(new SimpleGrantedAuthority(ROLE_SUPERADMIN.name()))) {
                return ROLE_SUPERADMIN.name();
            } else if (!customUserDetail.getStaffRole().isEmpty()) {
                staffRoles = customUserDetail.getStaffRole();
                if (staffRoles.contains(StaffRole.whitelabeladmin)) {
                    return StaffRole.whitelabeladmin.name();
                } else if (staffRoles.contains(StaffRole.eventcoordinator)) {
                    return StaffRole.eventcoordinator.name();
                } else if (staffRoles.contains(StaffRole.admin)) {
                    return StaffRole.admin.name();
                } else {
                    return StaffRole.staff.name();
                }
            } else {
                return ROLE_USER.name();
            }
        } else {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }

    }

	public User getUser(Authentication auth) {
		User user = null;
		if (auth != null) {
			CustomUserDetail customUserDetail = (CustomUserDetail) auth.getPrincipal();
			if (customUserDetail != null && customUserDetail.getUser() != null) {
				user = customUserDetail.getUser();
			}
		}
		return user;
	}

	public List<String> getUserRole(Authentication auth) {
		List<String> userRoles = new ArrayList<>();
		if (auth != null) {
			CustomUserDetail customUserDetail = (CustomUserDetail) auth.getPrincipal();
			if (customUserDetail != null && !customUserDetail.getAuthorities().isEmpty()) {
				for (GrantedAuthority authority : customUserDetail.getAuthorities()) {
					userRoles.add(authority.getAuthority());
				}
			}
		}
		return userRoles;
	}


	public RedirectUrlDto getRedirectUrl(AccessTokenModel accessTokenModel) {//NOSONAR
		String url = "";
        String userRole ="";
        boolean twoFactorRequired = false;
        boolean hasMultiAccounts = false;
        WhiteLabel whiteLabel =  null;
		RedirectUrlDto redirectUrlDto = new RedirectUrlDto();
		List<String> roles = accessTokenModel.getUserRoles();
		whiteLabel = roWhiteLabelService.findByHostBaseUrl(accessTokenModel.getDomain());

        if(accessTokenModel.getWhiteLabelId() != null && whiteLabel == null){
            Optional<WhiteLabel> optionalWhiteLabel =  roWhiteLabelService.getWhiteLabelById(accessTokenModel.getWhiteLabelId());
            if (optionalWhiteLabel.isPresent()) {
                whiteLabel = optionalWhiteLabel.get();
            }
        }
		try {
            log.info("Trying to sign in a user {} | whiteLabelURL {} | roles {} | getRedirectUrl", accessTokenModel.getUsername(),accessTokenModel.getWhiteLabelId(),roles);
			if (hasSuperAdminRole(roles)) {
				log.info("Is super admin");
				url = "/u/superadmin/events";
				userRole = ROLE_SUPERADMIN.name();
            } else if (hasWhiteLabelAdminUserRole(roles) || hasEventCoordinatorUserRole(roles) || hasEventBudgetOwnerUserRole(roles) || hasEventPlannerUserRole(roles)) {
                // If user is WL admin,Login from WL domain then redirect Url = WL dashboard
                log.info("Is Whitelabel Admin and login from WL Domain. User => {}", accessTokenModel.getUser().toString());
                userRole = hasWhiteLabelAdminUserRole(roles) ? ROLE_WHITELABELADMIN.name() :
                            hasEventCoordinatorUserRole(roles) ? ROLE_EVENT_COORDINATOR.name() :
                                    hasEventBudgetOwnerUserRole(roles) ? ROLE_EVENT_BUDGET_OWNER.name() :
                                            hasEventPlannerUserRole(roles) ? ROLE_EVENT_PLANNER.name() : userRole;

                if (!accessTokenModel.isFromAELoginPage() && whiteLabel != null && ((StringUtils.isNotBlank(whiteLabel.getHostBaseUrl()) && whiteLabel.getHostBaseUrl().equals(accessTokenModel.getDomain())) ||
                        whiteLabel.getId() == accessTokenModel.getWhiteLabelId())) {
                    url = "/u/wl/" + whiteLabel.getWhiteLabelUrl() + "/home";
                    if (gamificationCacheStoreService.is2FaEnabledForWhiteLabel(whiteLabel.getId()) && !accessTokenModel.isUser2FA()) {
                        twoFactorRequired = true;
                    }
                } else {
                    List<LoginUserWLOrgEventDetailsDto> wlAccountList = roWhiteLabelService.findLoginUserWlOrgEventAccounts(accessTokenModel.getUser(), userRole);
                    if (wlAccountList.size() > 1) {
                        hasMultiAccounts = true;
                    }
                    LoginUserWLOrgEventDetailsDto whiteLabelData = wlAccountList.get(0);
                    twoFactorRequired=whiteLabelData.isIs2FAEnabled();
                    url = "/u/wl/" + whiteLabelData.getUrl() + "/home";
                }
            } else if (hasUserRole(roles) || hasAdminRole(roles)) {
                User user = accessTokenModel.getUser();
                log.info("hasUserRole Or hasAdminRole userId {} roles {}", user.getUserId(), roles);
                Long eventId = accessTokenModel.getEventId();
                Event event = eventId != null ? roEventService.getEventByIdIfPresentOrNull(eventId) : null;
                log.info("hasUserRole Or hasAdminRole eventId {}", eventId);
                List<LoginUserWLOrgEventDetailsDto> orgAccounts = roOrganizerService.findLoginUserOrgAccounts(user.getUserId());
                log.info("User hasUserRole Or hasAdminRole organiser list {}", orgAccounts.size());
                if (!orgAccounts.isEmpty()) {
                    if (orgAccounts.size() > 1) {
                        hasMultiAccounts = true;
                    }
                    url = "/o/edit/" + orgAccounts.get(0).getUrl();
                    if (gamificationCacheStoreService.is2FaEnabledForOrganizer(orgAccounts.get(0).getId()) && !accessTokenModel.isUser2FA()) {
                        twoFactorRequired = true;
                    }
                    userRole = ROLE_USER.name();
                } else if (hasAdminRole(roles) && isStaff(event, user, StaffRole.admin)) {
                    List<LoginUserWLOrgEventDetailsDto> eventAccounts = roEventService.findLoginUserEventAccounts(user.getUserId());
                    if (eventAccounts.size() > 1) {
                        hasMultiAccounts = true;
                    }
                    log.info("user is admin in one event");
                    LoginUserWLOrgEventDetailsDto eventData = eventAccounts.get(0);
                    url = "/host/" + eventData.getUrl() + "/overview/home";
                    if (gamificationCacheStoreService.is2FaEnabledForEvent(eventData.getId()) && !accessTokenModel.isUser2FA()) {
                        twoFactorRequired = true;
                    }
                    userRole = ROLE_ADMIN.name();
				} else if (isStaff(event, user, StaffRole.staff)) {
					log.info("is staff");
					url = GeneralUtils.getEventPath() + event.getEventURL() + "/staff";
					log.info(EVENT_URL_WITH_CURLY_BRACES , url);
                    if(gamificationCacheStoreService.is2FaEnabledForEvent(event.getEventId()) && !accessTokenModel.isUser2FA()){
                        twoFactorRequired = true;
                    }
				} else {
					log.info("is not staff");
                    try {
                        if (event == null) {
                            log.info("get most recent event");
                            event = roEventService.getEventById(user.getMostRecentEventId());
                            url = GeneralUtils.getEventPath() + event.getEventURL();
                        }
                        log.info(EVENT_URL_WITH_CURLY_BRACES, url);
                    } catch (NotFoundException nfe) {
                        url = "/u/myprofile";
                        userRole = ROLE_USER.name();
                    }
				}
			}
		} catch (Exception e) {
			log.error("Exception", e);
			url = "/u/myprofile";
		}
		redirectUrlDto.setRedirectUrl(url);
		redirectUrlDto.setUserRole(userRole);
        redirectUrlDto.setTwoFactorRequired(twoFactorRequired);
        redirectUrlDto.setHasMultiAccounts(hasMultiAccounts);
        log.info("redirect Url of the user {} | redirect Url {} | userRole {} | getRedirectUrl ", accessTokenModel.getUsername(), url, userRole);
		return redirectUrlDto;
	}

	public RedirectUrlDto getWhiteLabelRedirectUrl(AccessTokenModel accessTokenModel, String whiteLabelURL){
		String url = "";
		String userRole ="";
		String WLURL="";
        boolean twoFactorRequired = false;

		List<String> roles = accessTokenModel.getUserRoles();
        log.info("Login user {} | roles {} | whiteLabelURL {} | getWhiteLabelRedirectUrl", accessTokenModel.getUsername(),roles,whiteLabelURL);
		if (hasSuperAdminRole(roles)) {
			url = "/u/superadmin/events";
			userRole = ROLE_SUPERADMIN.name();

		} else if (hasWhiteLabelAdminUserRole(roles) && accessTokenModel.getStaffRole().contains(StaffRole.whitelabeladmin)){
			url = "/u/wl/" + whiteLabelURL + "/home";
			userRole = ROLE_WHITELABELADMIN.name();
			WLURL = whiteLabelURL;
            if(gamificationCacheStoreService.is2FaEnabledForWhiteLabel(accessTokenModel.getWhiteLabelId()) && !accessTokenModel.isUser2FA()){
                twoFactorRequired = true;
            }
        } else if (hasEventCoordinatorUserRole(roles) && (accessTokenModel.getStaffRole().contains(StaffRole.eventcoordinator))) {
            url = "/u/wl/" + whiteLabelURL + "/home";
            userRole = ROLE_EVENT_COORDINATOR.name();
            WLURL = whiteLabelURL;
            if (gamificationCacheStoreService.is2FaEnabledForWhiteLabel(accessTokenModel.getWhiteLabelId()) && !accessTokenModel.isUser2FA()) {
                twoFactorRequired = true;
            }
        }else if (hasEventBudgetOwnerUserRole(roles) && (accessTokenModel.getStaffRole().contains(StaffRole.eventbudgetowner))) {
            url = "/u/wl/" + whiteLabelURL + "/home";
            userRole = ROLE_EVENT_BUDGET_OWNER.name();
            WLURL = whiteLabelURL;
            if (gamificationCacheStoreService.is2FaEnabledForWhiteLabel(accessTokenModel.getWhiteLabelId()) && !accessTokenModel.isUser2FA()) {
                twoFactorRequired = true;
            }
        }
        else if (hasEventPlannerUserRole(roles) && (accessTokenModel.getStaffRole().contains(StaffRole.eventplanner))) {
            url = "/u/wl/" + whiteLabelURL + "/home";
            userRole = ROLE_EVENT_PLANNER.name();
            WLURL = whiteLabelURL;
            if (gamificationCacheStoreService.is2FaEnabledForWhiteLabel(accessTokenModel.getWhiteLabelId()) && !accessTokenModel.isUser2FA()) {
                twoFactorRequired = true;
            }
        }
        else if (hasAdminRole((roles))){
            Event event = roEventService.getEventById(accessTokenModel.getUser().getMostRecentEventId());
            url = (event != null) ? "/host/" + event.getEventURL() + "/overview/home" : SLASH_U_PROFILE;
			userRole = ROLE_ADMIN.name();
            if(null != accessTokenModel.getEventId() &&
                    gamificationCacheStoreService.is2FaEnabledForEvent(accessTokenModel.getEventId()) &&
                    !accessTokenModel.isUser2FA()){
                twoFactorRequired = true;
            }
		} else {
			try {
				Event event = roEventService.getEventById(accessTokenModel.getUser().getMostRecentEventId());
				url = GeneralUtils.getEventPath() + event.getEventURL();
				log.info(EVENT_URL_WITH_CURLY_BRACES , url);
			}catch (NotFoundException nfe){
				url = "/u/myprofile";
			}
		}
        log.info("redirect Url of the user {} | with redirect url {} | userRole {} | WL Url {} | getWhiteLabelRedirectUrl", accessTokenModel.getUsername(), url, userRole, WLURL);
		return new RedirectUrlDto(url,userRole,WLURL, twoFactorRequired);
	}

	private boolean hasUserRole(List<String> roles) {
		return roles.contains(ROLE_USER.name());
	}

	private boolean hasAdminRole(List<String> roles) {
		return roles.contains(ROLE_ADMIN.name());
	}

	private boolean hasSuperAdminRole(List<String> roles) {
		return roles.contains(ROLE_SUPERADMIN.name());
	}

    private boolean hasWhiteLabelAdminUserRole(List<String> roles) {
        return roles.contains(ROLE_WHITELABELADMIN.name());
    }

    private boolean hasEventCoordinatorUserRole(List<String> roles) {
        return roles.contains(ROLE_EVENT_COORDINATOR.name());
    }

    private boolean hasEventBudgetOwnerUserRole(List<String> roles) {
        return roles.contains(ROLE_EVENT_BUDGET_OWNER.name());
    }

    private boolean hasEventPlannerUserRole(List<String> roles) {
        return roles.contains(ROLE_EVENT_PLANNER.name());
    }




	private boolean isStaff(Event event, User user, StaffRole role) {
		// TODO : Optimize
		boolean result = false;
		if (event != null && user!=null) {
			for (Staff staff : roStaffService.findByEvent(event)) {
				if (staff.getUser().getUserId().equals(user.getUserId())
					&& staff.getRole().equals(role)) {
					result = true;
					break;
				}
			}
		}
		return result;
	}
}
