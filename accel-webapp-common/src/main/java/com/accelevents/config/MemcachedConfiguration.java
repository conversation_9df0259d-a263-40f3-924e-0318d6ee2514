package com.accelevents.config;

import com.accelevents.NoProfilesEnabled;
import com.accelevents.cache.Memcached;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.interceptor.*;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;

@Configuration
//@Profile({"!test"})
@NoProfilesEnabled({"test", "local", "sonar"})
public class MemcachedConfiguration implements CachingConfigurer {

	@Value("${memcached.addresses}")
	private String memcachedAddresses;

	@Value("${memcached.expiration.sec}")
	private int expirationSec;

	@Override
	@Bean
	public CacheManager cacheManager(){
		SimpleCacheManager cacheManager = new SimpleCacheManager();
		try {
			cacheManager.setCaches(internalCaches());
			return cacheManager;
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	private Collection<Memcached> internalCaches() throws IOException {
		final Collection<Memcached> caches = new ArrayList<>();
		caches.add(getMemCached("customEmail"));
		caches.add(getMemCached("raffleCustomSms"));
		caches.add(getMemCached("findAuctionByEventId"));
		caches.add(getMemCached("donationSettingByEventId"));
		caches.add(getMemCached("findUserById"));
		caches.add(getMemCached("raffleFindByEventId"));
		caches.add(getMemCached("eventById"));
		caches.add(getMemCached("eventByUrl"));
		caches.add(getMemCached("findAllSessionDatesByEvent"));
        caches.add(getMemCached("findAllSessionForAdminOrStaffDatesByEvent"));
		caches.add(getMemCached("SessionByEventAndFormat"));
		caches.add(getMemCached("causeAuctionByEventId"));
		caches.add(getMemCached("maxNumberOfFreeAttendees"));
		caches.add(getMemCached("userRoles"));
		caches.add(getMemCached("StaffByEvtExhi"));
		caches.add(getMemCached("isSpeakerOfEvt"));
		caches.add(getMemCached("speakersByEvent"));
		caches.add(getMemCached("speakersByEventPosition"));
		caches.add(getMemCached("StaffByEvtUsr"));
		caches.add(getMemCached("streamChannels"));
		caches.add(getMemCached("stremAllowChhanelCreate"));
		caches.add(getMemCached("sessionByEventAndSessionId"));
		caches.add(getMemCached("allSessionByEvent"));
		caches.add(getMemCached("exhibitorCategoryNames"));
		caches.add(getMemCached("exhibitorsByEvent"));
		caches.add(getMemCached("exhibitorMapByEvent"));
		caches.add(getMemCached("getAllExhibitorCarouselDetail"));
		caches.add(getMemCached("findAllUserByRole"));
        caches.add(getMemCached("findAllSalesRepUrls"));
        caches.add(getMemCached("exhibitorCategoryByEvent"));
        caches.add(getMemCached("getUsersByEvent"));
        caches.add(getMemCached("exhibitorProductByExhibitor"));
        caches.add(getMemCached("isRecurringByEvt"));
        caches.add(getMemCached("findByConfId"));
        caches.add(getMemCached("raffleModuleByEventId"));
        caches.add(getMemCached("findAllBySessionId"));
        caches.add(getMemCached("findAllExhibitorsByEvent"));
        caches.add(getMemCached("SessionTitleByEventIdOrderByIdAsc"));
        caches.add(getMemCached("findWhiteLabelByWhiteLabelUrl"));
        caches.add(getMemCached("findWhiteLabelByHostBaseUrl"));
        caches.add(getMemCached("getManagerByOrderId"));
        caches.add(getMemCached("allSessionDetailsByEvent"));
        caches.add(getMemCached("StaffByEvtUsrRole"));
        caches.add(getMemCached("sponsorsByEvent"));
        caches.add(getMemCached("sponsorPresentByExhibitorIdForEvent"));
        caches.add(getMemCached("findExhibitorCategoryByEventIdAndId"));
        caches.add(getMemCached("getNetworkingLoungeByLoungeId"));
        caches.add(getMemCached("getNetworkingLoungeByEventId"));
        caches.add(getMemCached("virtualEventLabelByLanguageCode"));
        caches.add(getMemCached("virtualEventLabelByLanguageCodeAndType"));
        caches.add(getMemCached("getSessionSpeakerByEventId"));
        caches.add(getMemCached("organizerByOrganizerPageURL"));
        caches.add(getMemCached("organizerById"));
        caches.add(getMemCached("organizerDtoByOrganizerPageURL"));
        caches.add(getMemCached("organizerByUserAndWhiteLabelNull"));
        caches.add(getMemCached("getSessionEndDateTypeByEvent"));
        caches.add(getMemCached("findByIdKeyValue"));
        caches.add(getMemCached("findByEventIdAndId"));
        caches.add(getMemCached("getKeyValueUsingEventIdAndType"));
        caches.add(getMemCached("stripeByEvent"));
        caches.add(getMemCached("getAttendeeUserIdsForEvent"));
        caches.add(getMemCached("getLastResetEventDetailsByEvent"));
        caches.add(getMemCached("getListOfSessionIds"));
		caches.add(getMemCached("allSpeakersByEventId"));
        caches.add(getMemCached("defaultBeeFreeHomePageByEvent"));
        caches.add(getMemCached("findDefaultBeeFreeHomePageDtoByEvent"));
        caches.add(getMemCached("isAllowToLateJoinSession"));
        caches.add(getMemCached("allowedMinutesToJoinLateSession"));
        caches.add(getMemCached("speakerUserIdBySessionId"));
        caches.add(getMemCached("allStaffUserIdsByEvent"));
        caches.add(getMemCached("getBeeFreePageByBeePageUrlAndEventId"));
        caches.add(getMemCached("getBeeFreePublishedPageByPageUrlAndEventId"));
        caches.add(getMemCached("isAllowSessionBookmarkCapacity"));
        caches.add(getMemCached("isAllowAttendeeToScheduleMeeting"));
        caches.add(getMemCached("isPreScheduleMeetingScheduled"));
        caches.add(getMemCached("findMeetingOptionByEventId"));
        caches.add(getMemCached("isAnyMeetingBookedByUserAndEventAndOrigin"));
        caches.add(getMemCached("isBadgeReprintAllow"));
        caches.add(getMemCached("findByModuleName"));
        caches.add(getMemCached("isAllowCheckInWithUnpaidTicketEnabledByEventId"));
        caches.add(getMemCached("isHolderInfoVisibleToAttendees"));
        caches.add(getMemCached("isAnyBadgePdfGeneratedForEvent"));
        caches.add(getMemCached("getLoungeByNeptuneId"));
        caches.add(getMemCached("getWaitlistSettingByEventId"));
        caches.add(getMemCached("findTicketingByEventId"));
        caches.add(getMemCached("getStripeFeesByEvent"));
        caches.add(getMemCached("findVatTaxByEventId"));
        caches.add(getMemCached("findEventLevelSettingByEventId"));
        caches.add(getMemCached("findTicketTypeIdsByRecurringEventIdForAccessCode"));
        caches.add(getMemCached("findTicketTypeIdsByEventForAccessCode"));
        caches.add(getMemCached("findEventPlanConfigByEventId"));
        caches.add(getMemCached("findNotificationPreferenceByUserIdAndEventId"));
        caches.add(getMemCached("isSalesforceLoginRequiredByEventUrl"));
        caches.add(getMemCached("getRegisteredSessionIdsOfUser"));
        caches.add(getMemCached("getBookmarkedSessionIdsOfUser"));
        caches.add(getMemCached("findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc"));
        caches.add(getMemCached("countFavoriteItemByUserIdAndModuleTypeAndActive"));
        caches.add(getMemCached("findItemCategoriesCountByModuleIdAndModuleTypeAndActive"));
        caches.add(getMemCached("countDistinctItemsByAuctionIdAndUser"));
        caches.add(getMemCached("getAllHiddenSessionOfEventId"));
        caches.add(getMemCached("getAllSessionsByEventIdAndHideFromAttendees"));
        caches.add(getMemCached("getAllPrivateSessionByEventId"));
        caches.add(getMemCached("isRecheckInEnabledByEventId"));
        caches.add(getMemCached("findEventExhibitorSettingsByEventId"));
        caches.add(getMemCached("getExhibitorChatPollAndQnASettings"));
        caches.add(getMemCached("findSessionApprovalSettingByEventId"));
        caches.add(getMemCached("allowAttendeeToDownloadCertificateByEventId"));
        caches.add(getMemCached("eventRequestFormByUrl"));
        caches.add(getMemCached("findCalendarDayLevelInviteBlockByEventId"));
        caches.add(getMemCached("getWaitedListedSessionIdsOfUser"));
        caches.add(getMemCached("enableSessionWaitlistCapacityReached"));
        caches.add(getMemCached("isSurveyRequiresCheckInByEventId"));
        caches.add(getMemCached("isTicketingCoupanAvailableForEvent"));
        caches.add(getMemCached("isLimitEventCapacityByEventId"));
        caches.add(getMemCached("findLimitEventCapacityByEventId"));
        caches.add(getMemCached("findBlockedEmailsForEvent"));
        caches.add(getMemCached("findEntryExitSettingByEventId"));
        caches.add(getMemCached("isEntryExitEnabledForEvent"));
        return caches;
	}

	private Memcached getMemCached(String userRoles) throws IOException {
		return new Memcached(userRoles, memcachedAddresses, expirationSec);
	}

	@Override
	public KeyGenerator keyGenerator() {
		return new SimpleKeyGenerator();
	}

	@Override
	public CacheErrorHandler errorHandler() {
		return new SimpleCacheErrorHandler();
	}

	@Override
	public CacheResolver cacheResolver() {
		return null;
	}
}