package com.accelevents.email.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

public class SqsMessageSender {

    private static final Logger log = LoggerFactory.getLogger(SqsMessageSender.class);

    private SqsClient sqsClient;

    public SqsMessageSender(SqsClient sqsClient) {
        this.sqsClient = sqsClient;
    }

    public void sendSQSMessage(String queueUrl, String messageBody){

        SendMessageRequest sendMsgRequest = SendMessageRequest.builder()
                .queueUrl(queueUrl)
                .messageBody(messageBody)
                .delaySeconds(1)
                .build();

        SendMessageResponse sendMsgResponse = sqsClient.sendMessage(sendMsgRequest);
        log.info("Message ID: {}", sendMsgResponse.messageId());
    }
}
