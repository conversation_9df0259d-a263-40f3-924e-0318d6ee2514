package com.accelevents.email.service;

import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

public class LambdaS3Service {

    private S3Client s3Client;

    public LambdaS3Service(S3Client s3Client) {
        this.s3Client = s3Client;
    }

    public PutObjectResponse uploadInBucket(String content, String uploadKey, String bucketName){
        return this.uploadDataInBucketWithRequestBody(uploadKey, bucketName, RequestBody.fromString(content));
    }

    public PutObjectResponse uploadInBucket(byte[] content, String uploadKey, String bucketName){
        return this.uploadDataInBucketWithRequestBody(uploadKey, bucketName, RequestBody.fromBytes(content));
    }

    private PutObjectResponse uploadDataInBucketWithRequestBody(String uploadKey, String bucketName, RequestBody requestBody){
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(uploadKey)
                .build();

        return s3Client.putObject(putObjectRequest, requestBody);
    }

    public String getContentFromS3(String bucketName, String keyName){

        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(keyName)
                .build();

        ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);

        // Read the content from the S3 object
        String content;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(s3Object, StandardCharsets.UTF_8))) {
            content = reader.lines().collect(Collectors.joining("\n"));
        } catch (Exception e) {
            e.printStackTrace();
            content = null;
        }

        return content;
    }
}
