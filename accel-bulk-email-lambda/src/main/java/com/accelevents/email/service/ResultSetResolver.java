package com.accelevents.email.service;

import com.accelevents.domain.enums.Currency;
import com.accelevents.dto.lambda.EmailDataDto;
import com.accelevents.helpers.EmailLogoImageHelper;
import org.apache.commons.lang3.StringUtils;

import java.sql.ResultSet;
import java.sql.SQLException;

import static com.accelevents.email.utils.BulkLambdaConstants.*;

public class ResultSetResolver {

    public static void updateEventDataFromTheEventResultSet(ResultSet eventWithDesignAndTicketingRS, EmailDataDto emailDataDto) throws SQLException {

        emailDataDto.setEventURL(eventWithDesignAndTicketingRS.getString(COL_EVENTURL));
        emailDataDto.setEventName(eventWithDesignAndTicketingRS.getString(COL_NAME));
        emailDataDto.setEventFormat(eventWithDesignAndTicketingRS.getString(COL_EVENT_FORMAT));
        emailDataDto.setWhiteLabelId(eventWithDesignAndTicketingRS.getLong(COL_WHITE_LABEL));
        emailDataDto.setEventTimeZone(eventWithDesignAndTicketingRS.getString(COL_EQUIVALENT_TIMEZONE));
        emailDataDto.setTimeZoneId(eventWithDesignAndTicketingRS.getString(COL_TIMEZONE_ID));
        emailDataDto.setHideEventDates(eventWithDesignAndTicketingRS.getBoolean(COL_HIDE_EVENT_DATE));

        emailDataDto.setSenderEmail(eventWithDesignAndTicketingRS.getString(COL_NOTIFICATION_EMAIL));
        if (StringUtils.isEmpty(emailDataDto.getReplyToEmail())){
            emailDataDto.setReplyToEmail(eventWithDesignAndTicketingRS.getString(COL_REPLY_EMAIL));
        }
        emailDataDto.setSenderName(eventWithDesignAndTicketingRS.getString(COL_EMAIL_SENDER_NAME));
        emailDataDto.setEventCalendarInvite(eventWithDesignAndTicketingRS.getString(COL_EVENT_CALENDAR_INVITE));
        emailDataDto.setEventLogoImg(eventWithDesignAndTicketingRS.getString(COL_LOGO_IMAGE));
        emailDataDto.setEventHeaderLogoImg(eventWithDesignAndTicketingRS.getString(COL_HEADER_LOGO_IMAGE));

        emailDataDto.setEventStartDate(eventWithDesignAndTicketingRS.getTimestamp(COL_EVENT_START_DATE));
        emailDataDto.setEventEndDate(eventWithDesignAndTicketingRS.getTimestamp(COL_EVENT_END_DATE));
        emailDataDto.setEventAddress(eventWithDesignAndTicketingRS.getString(COL_EVENT_ADDRESS));
        emailDataDto.setLatitude(eventWithDesignAndTicketingRS.getString(COL_LATITUDE));
        emailDataDto.setLongitude(eventWithDesignAndTicketingRS.getString(COL_LONGITUDE));
        emailDataDto.setShowEnterEventButtonInReminderTemplate(eventWithDesignAndTicketingRS.getBoolean(COL_SHOW_ENTER_EVENT_BUTTON_IN_REMINDER_TEMPLATE));

        String eventCurrency = eventWithDesignAndTicketingRS.getString(COL_CURRENCY);

        if(null != eventCurrency) {
            emailDataDto.setEventCurrencySymbol(Currency.valueOf(eventCurrency).getSymbol());
        }

        emailDataDto.setOrgName(eventWithDesignAndTicketingRS.getString(COL_ORG_NAME));
        emailDataDto.setOrgContactEmail(eventWithDesignAndTicketingRS.getString(COL_ORG_CONTACT_EMAIL));
        emailDataDto.setOrgPageURL(eventWithDesignAndTicketingRS.getString(COL_ORGANIZER_PAGE_URL));

        String ticketPdfDesign = eventWithDesignAndTicketingRS.getString(COL_TICKET_PDF_DESIGN);
        if(StringUtils.isNotEmpty(ticketPdfDesign))
            emailDataDto.setTicketPdfDesign(ticketPdfDesign);
        else
            emailDataDto.setTicketPdfDesign(eventWithDesignAndTicketingRS.getString(COL_TICKET_CUSTOM_PDF_DESIGN));

        emailDataDto.setTicketChartKey(eventWithDesignAndTicketingRS.getString(COL_CHART_KEY));
    }

    public static void updateWhiteLabelDataFromTheWhiteLabelResultSet(ResultSet whiteLabelRs, EmailDataDto emailDataDto) throws SQLException {

        emailDataDto.setWhiteLabelFirmName(whiteLabelRs.getString(COL_FIRM_NAME));
        emailDataDto.setWhiteLabelURL(whiteLabelRs.getString(COL_WHITE_LABEL_URL));
        emailDataDto.setMailApiKey(whiteLabelRs.getString(COL_MAIL_API_KEY));
        emailDataDto.setWlNotificationEmail(whiteLabelRs.getString(COL_NOTIFICATION_EMAIL));
        emailDataDto.setWlSupportEmail(whiteLabelRs.getString(COL_SUPPORT_EMAIL));
        emailDataDto.setWlTransactionEmail(whiteLabelRs.getString(COL_TRANSACTIONAL_EMAIL));
        emailDataDto.setWlHostBaseURL(whiteLabelRs.getString(COL_HOST_BASE_URL));
        emailDataDto.setPlanName(whiteLabelRs.getString(COL_PLAN_NAME));
        emailDataDto.setWlLogoImg(whiteLabelRs.getString(COL_LOGO_IMAGE));
        emailDataDto.setWlHeaderLogoImg(whiteLabelRs.getString(COL_HEADER_LOGO_IMAGE));
        emailDataDto.setGetStarted(whiteLabelRs.getString(COL_GET_STARTED));
        emailDataDto.setHelpCenter(whiteLabelRs.getString(COL_HELP_CENTER_URL));
        emailDataDto.setPrivacyPolicy(whiteLabelRs.getString(COL_PRIVACY_POLICY));
        emailDataDto.setFacebookShareURL(whiteLabelRs.getString(COL_FACEBOOK_SHARE));
        emailDataDto.setTwitterShareURL(whiteLabelRs.getString(COL_TWITTER_SHARE));
        emailDataDto.setLinkedinShareURL(whiteLabelRs.getString(COL_LINKEDIN_SHARE));
        emailDataDto.setInstagramShareURL(whiteLabelRs.getString(COL_INSTAGRAM_SHARE));

    }
}
