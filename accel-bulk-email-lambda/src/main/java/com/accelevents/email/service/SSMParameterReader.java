package com.accelevents.email.service;

import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ssm.SsmClient;

public class SSMParameterReader {

    private final SsmClient ssmClient;


    public SSMParameterReader() {

        Region region = Region.US_EAST_1;
        AwsCredentialsProvider cp = DefaultCredentialsProvider.create();
        ssmClient = SsmClient.builder().build();
    }

    public String getParameterValue(String parameterName) {
        return ssmClient.getParameter(request -> request.name(parameterName).withDecryption(true)).parameter().value();
    }
}
