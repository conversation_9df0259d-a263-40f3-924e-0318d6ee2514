package com.accelevents.email.service;

public class EnvValuesConfiguration {

    private final String cloudinaryImageUrl = System.getenv("CLOUDINARY_IMG_URL");
    private final String imagePreFix = System.getenv("IMAGE_PREFIX");

    private final String blackLogo = "default_ae_images/Ae_Icn_700x350.png";
    private final String imagesAcceleventlogo = "default_ae_images/Smooth_Accelevents_Default_Event_Logo_Black.png";
    private final String defaultAcceleventsLogo = "default_ae_images/Smooth_Accelevents_dark_fill_icon.png";

    private final String defaultFacebookShare = "https://www.facebook.com/accelevents";
    private final String defaultTwitterShare = "https://twitter.com/accelevents";
    private final String defaultInstagramShare = "https://www.instagram.com/accelevents";
    private final String defaultLinkedInShare = "https://www.linkedin.com/company/accelevents";
    private final String defaultGetStarted = "https://www.accelevents.com/u/signup";
    private final String helpCenter="https://support.accelevents.com/en/";
    private final String privacyPolicy="https://www.accelevents.com/privacy/privacy";


    public String getCloudinaryImageUrl() {
        return cloudinaryImageUrl;
    }

    public String getCloudinaryURLWithImagePrefix() {
        return getCloudinaryImageUrl().concat(imagePreFix);
    }

    public String getImagePreFix() {
        return imagePreFix;
    }

    public String getBlackLogo() {
        return blackLogo;
    }

    public String getImagesAcceleventlogo() {
        return imagesAcceleventlogo;
    }

    public String getDefaultAcceleventsLogo() {
        return defaultAcceleventsLogo;
    }

    public String getDefaultFacebookShare() {
        return defaultFacebookShare;
    }

    public String getDefaultTwitterShare() {
        return defaultTwitterShare;
    }

    public String getDefaultInstagramShare() {
        return defaultInstagramShare;
    }

    public String getDefaultLinkedInShare() {
        return defaultLinkedInShare;
    }

    public String getDefaultGetStarted() {
        return defaultGetStarted;
    }

    public String getHelpCenter() {
        return helpCenter;
    }

    public String getPrivacyPolicy() {
        return privacyPolicy;
    }
}
