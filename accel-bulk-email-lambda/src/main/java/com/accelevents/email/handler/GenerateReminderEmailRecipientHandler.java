package com.accelevents.email.handler;

import com.accelevents.dto.lambda.ReminderEmailLambdaMessage;
import com.accelevents.email.mail.PrepareBulkEventTaskReminderEmail;
import com.accelevents.email.mail.PrepareBulkReminderEmail;
import com.accelevents.email.service.SSMParameterReader;
import com.accelevents.email.service.SentryConfiguration;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GenerateReminderEmailRecipientHandler implements RequestHandler<SQSEvent, String> {

    private static final Logger log = LoggerFactory.getLogger(GenerateReminderEmailRecipientHandler.class);

    @Override
    public String handleRequest(SQSEvent sqsEvent, Context context) {

        String envName = System.getenv("APP_ENV");
        if(StringUtils.isEmpty(envName)) {
            log.error("Environment variable 'APP_ENV' is not set.");
            return "Environment variable 'APP_ENV' is not set.";
        }

        SSMParameterReader ssmParameterReader = new SSMParameterReader();

        String jdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url");
        String roJdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url_ro");
        String userName = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_username");
        String password = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_password");

        String dsn = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/sentry_dsn_lambda");

        String awsS3bucket = System.getenv("S3_BUCKET");
        String bulkEmailSqsQueue = System.getenv("BULK_EMAIL_SQS_QUEUE");

        SentryConfiguration.init(envName, dsn);

        for(SQSEvent.SQSMessage msg : sqsEvent.getRecords()){
            String eventMessageString = msg.getBody();

            log.info("Msg => {}", eventMessageString);
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            ReminderEmailLambdaMessage reminderEmailLambdaMessage = null;
            try {
                reminderEmailLambdaMessage = objectMapper.readValue(eventMessageString, ReminderEmailLambdaMessage.class);

                if(reminderEmailLambdaMessage.getResendTicketingEmailId() != null && reminderEmailLambdaMessage.getResendTicketingEmailId() > 0) {
                    PrepareBulkReminderEmail prepareBulkReminderEmail = new PrepareBulkReminderEmail(jdbcUrl, roJdbcUrl, userName, password, awsS3bucket, bulkEmailSqsQueue);
                    prepareBulkReminderEmail.sendReminderEmailToAttendees(reminderEmailLambdaMessage.getResendTicketingEmailId(), reminderEmailLambdaMessage.getTestEmail());
                } else if(reminderEmailLambdaMessage.getEventTaskReminderEmailId() != null && reminderEmailLambdaMessage.getEventTaskReminderEmailId() > 0) {
                    PrepareBulkEventTaskReminderEmail prepareBulkEventTaskReminderEmail = new PrepareBulkEventTaskReminderEmail(jdbcUrl, roJdbcUrl, userName, password, bulkEmailSqsQueue);
                    prepareBulkEventTaskReminderEmail.sendReminderEmailToSpeakers(reminderEmailLambdaMessage.getEventTaskReminderEmailId(), reminderEmailLambdaMessage.getEmailSendType());
                } else {
                    log.info("ReminderTicketingEmailId/EventTaskReminderEmailId is not valid, ReminderTicketingEmailId = {}, EventTaskReminderEmailId = {}", reminderEmailLambdaMessage.getResendTicketingEmailId(), reminderEmailLambdaMessage.getEventTaskReminderEmailId());
                }

            } catch (Exception e) {
                log.error("Exception => ", e);
                Sentry.setExtra("eventMessageString", eventMessageString);
                Sentry.captureException(e);
            } finally {
                Sentry.flush(20000);
            }
        }

        return "Done";
    }
}
