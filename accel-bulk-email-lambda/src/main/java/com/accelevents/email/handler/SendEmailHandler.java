package com.accelevents.email.handler;

import com.accelevents.dto.lambda.BulkEmailLambdaMessage;
import com.accelevents.dto.lambda.EmailUserWithTicketDto;
import com.accelevents.email.mail.PrepareEventDataSendGridData;
import com.accelevents.email.service.SSMParameterReader;
import com.accelevents.email.service.SentryConfiguration;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Send<PERSON>mailHandler implements RequestHandler<SQSEvent, String> {

    private static final Logger log = LoggerFactory.getLogger(SendEmailHandler.class);

    @Override
    public String handleRequest(SQSEvent sqsEvent, Context context) {

        String envName = System.getenv("APP_ENV");
        if(StringUtils.isEmpty(envName)) {
            log.error("Environment variable 'APP_ENV' is not set.");
            return "Environment variable 'APP_ENV' is not set.";
        }

        SSMParameterReader ssmParameterReader = new SSMParameterReader();

        String jdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url");
        String roJdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url_ro");
        String userName = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_username");
        String password = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_password");

        String dsn = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/sentry_dsn_lambda");

        String awsS3bucket = System.getenv("S3_BUCKET");
        String sendGridDefaultApiKey = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/sendgrid_api_key");
        String beeFreeDefaultApiKey = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/beefree_api_token");


        SentryConfiguration.init(envName, dsn);

        for(SQSEvent.SQSMessage msg : sqsEvent.getRecords()){
            String eventMessageString = msg.getBody();

            log.info("Msg => {}", eventMessageString);
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;

            BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage = null;
            try {

                TypeReference<BulkEmailLambdaMessage<EmailUserWithTicketDto>> bulkEmailLambdaMessageTypeReference = new TypeReference<>() {};
                bulkEmailLambdaMessage = objectMapper.readValue(eventMessageString, bulkEmailLambdaMessageTypeReference);

                PrepareEventDataSendGridData prepareEventDataSendGridData = new PrepareEventDataSendGridData(jdbcUrl, roJdbcUrl, userName, password, awsS3bucket, envName, sendGridDefaultApiKey,beeFreeDefaultApiKey);

                switch (bulkEmailLambdaMessage.getBulkType()) {
                    case "ENGAGE":
                        prepareEventDataSendGridData.processEngageEmailUserDataAndSendEmail(bulkEmailLambdaMessage);
                        break;
                    case "REMINDER":
                        prepareEventDataSendGridData.processReminderEmailUserDataAndSendEmail(bulkEmailLambdaMessage);
                        break;
                    case "EVENT_TASK_REMINDER":
                        prepareEventDataSendGridData.processEventTaskReminderEmailUserDataAndSendEmail(bulkEmailLambdaMessage);
                        break;
                    default:
                        log.info("Bulk type is not valid for Bulk Email Lambda Message");
                }

            } catch (Exception e) {
                log.error("Exception: ", e);
                Sentry.setExtra("Msg", eventMessageString);
                Sentry.captureException(e);
            } finally {
                Sentry.flush(20000);
            }
        }

        return "Message Sent";
    }
}
