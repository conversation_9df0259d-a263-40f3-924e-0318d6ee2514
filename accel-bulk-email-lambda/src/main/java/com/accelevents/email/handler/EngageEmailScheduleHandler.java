package com.accelevents.email.handler;

import com.accelevents.email.mail.EngageEmailScheduler;
import com.accelevents.email.service.SSMParameterReader;
import com.accelevents.email.service.SentryConfiguration;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EngageEmailScheduleHandler implements RequestHandler<Object, String> {

    private static final Logger log = LoggerFactory.getLogger(EngageEmailScheduleHandler.class);

    @Override
    public String handleRequest(Object object, Context context) {

        String envName = System.getenv("APP_ENV");
        if(StringUtils.isEmpty(envName)) {
            log.error("Environment variable 'APP_ENV' is not set.");
            return "Environment variable 'APP_ENV' is not set.";
        }

        SSMParameterReader ssmParameterReader = new SSMParameterReader();

        String jdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url");
        String roJdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url_ro");
        String userName = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_username");
        String password = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_password");

        String dsn = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/sentry_dsn_lambda");
        SentryConfiguration.init(envName, dsn);

        String generateBulkEmailQueueName = System.getenv("GENERATE_BULK_EMAIL_QUEUE_NAME");

        try {
            EngageEmailScheduler engageEmailScheduler = new EngageEmailScheduler(jdbcUrl, roJdbcUrl, userName, password, generateBulkEmailQueueName);
            engageEmailScheduler.sendScheduledEngageEmail();

        } catch (Exception e) {
            log.error("Exception => ", e);
            Sentry.captureException(e);
        } finally {
            Sentry.flush(20000);
        }

        return "Complete";
    }
}
