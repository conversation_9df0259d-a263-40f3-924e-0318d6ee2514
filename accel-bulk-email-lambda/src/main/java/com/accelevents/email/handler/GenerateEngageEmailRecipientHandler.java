package com.accelevents.email.handler;

import com.accelevents.dto.lambda.EngageEmailLambdaMessage;
import com.accelevents.email.mail.PrepareEngageEmail;
import com.accelevents.email.service.SSMParameterReader;
import com.accelevents.email.service.SentryConfiguration;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GenerateEngageEmailRecipientHandler implements RequestHandler<SQSEvent, String> {

    private static final Logger log = LoggerFactory.getLogger(GenerateEngageEmailRecipientHandler.class);

    @Override
    public String handleRequest(SQSEvent sqsEvent, Context context) {

        String envName = System.getenv("APP_ENV");
        if(StringUtils.isEmpty(envName)) {
            log.error("Environment variable 'APP_ENV' is not set.");
            return "Environment variable 'APP_ENV' is not set.";
        }

        SSMParameterReader ssmParameterReader = new SSMParameterReader();

        String jdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url");
        String roJdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url_ro");
        String userName = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_username");
        String password = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_password");

        String dsn = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/sentry_dsn_lambda");

        String awsS3bucket = System.getenv("S3_BUCKET");
        String bulkEmailSqsQueue = System.getenv("BULK_EMAIL_SQS_QUEUE");

        SentryConfiguration.init(envName, dsn);

        for(SQSEvent.SQSMessage msg : sqsEvent.getRecords()){
            String eventMessageString = msg.getBody();

            log.info("Msg => {}", eventMessageString);
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            EngageEmailLambdaMessage engageEmailLambdaMessage = null;
            try {
                engageEmailLambdaMessage = objectMapper.readValue(eventMessageString, EngageEmailLambdaMessage.class);

                if(engageEmailLambdaMessage.getMessageToContactsId() != null && engageEmailLambdaMessage.getMessageToContactsId() > 0) {
                    PrepareEngageEmail prepareEngageEmail = new PrepareEngageEmail(jdbcUrl, roJdbcUrl, userName, password, awsS3bucket, bulkEmailSqsQueue);
                    prepareEngageEmail.sendEngageEmailToRecipients(engageEmailLambdaMessage.getMessageToContactsId(), engageEmailLambdaMessage.getTestEmail());
                }else {
                    log.info("MessageToContacts is not valid, id = {}", engageEmailLambdaMessage.getMessageToContactsId());
                }
            } catch (Exception e) {
                log.error("Exception => ", e);
                Sentry.setExtra("eventMessageString", eventMessageString);
                Sentry.captureException(e);
            } finally {
                Sentry.flush(20000);
            }
        }

        return "Done";
    }
}
