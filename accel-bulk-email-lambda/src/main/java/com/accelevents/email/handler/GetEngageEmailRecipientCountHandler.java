package com.accelevents.email.handler;

import com.accelevents.email.mail.EngageEmailCountService;
import com.accelevents.email.service.SSMParameterReader;
import com.accelevents.email.service.SentryConfiguration;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GetEngageEmailRecipientCountHandler implements RequestHandler<Long, Integer> {

    private static final Logger log = LoggerFactory.getLogger(GetEngageEmailRecipientCountHandler.class);

    @Override
    public Integer handleRequest(Long messageToContactId, Context context) {

        String envName = System.getenv("APP_ENV");
        if(StringUtils.isEmpty(envName)) {
            log.error("Environment variable 'APP_ENV' is not set.");
            return 0;
        }

        SSMParameterReader ssmParameterReader = new SSMParameterReader();

        String jdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url");
        String roJdbcUrl = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_url_ro");
        String userName = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_username");
        String password = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/mysql_password");

        String dsn = ssmParameterReader.getParameterValue("/app/"+envName+"/credential/sentry_dsn_lambda");
        SentryConfiguration.init(envName, dsn);

        log.info("MessageToContactId getRecipientCnt => {}", messageToContactId);

        try {

            EngageEmailCountService engageEmailCountService = new EngageEmailCountService(jdbcUrl, roJdbcUrl, userName, password);
            return engageEmailCountService.getEmailRecipientCount(messageToContactId);

        } catch (Exception e) {
            log.error("Exception => ", e);
            Sentry.setExtra("messageToContactId", messageToContactId.toString());
            Sentry.captureException(e);
        } finally {
            Sentry.flush(20000);
        }

        return 0;
    }
}
