package com.accelevents.email;

import com.accelevents.domain.enums.EMAIL_RECIPIENTS;
import com.accelevents.dto.lambda.*;
import com.accelevents.email.mail.PrepareBulkEventTaskReminderEmail;
import com.accelevents.email.mail.PrepareBulkReminderEmail;
import com.accelevents.email.mail.PrepareEngageEmail;
import com.accelevents.email.mail.PrepareEventDataSendGridData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import static com.accelevents.utils.Constants.MANUAL;

public class TestLambda {

    public static void main(String[] args) throws Exception {
        System.out.println("nik");

        String jdbcUrl = "***********************************************************************************************************************************************************************";
        String userName = "root";
        String password = "root";
        String awsS3bucket = "accelevents-dev-public-images";
        String bulkEmailSqsQueue = "test-bulk-queue";
        String environment = "dev";
        String sendGridDefaultApiKey = "test-mail-api-key";
        String beeFreeApiKey = "test-beefree-api-key";
        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        TypeReference<BulkEmailLambdaMessage<EmailUserWithTicketDto>> typeReference = new TypeReference<>() {};

/*

        EngageEmailLambdaMessage engageEmailLambdaMessage = new EngageEmailLambdaMessage();
        engageEmailLambdaMessage.setMessageToContactsId(23747L);

        System.out.println(objectMapper.writeValueAsString(engageEmailLambdaMessage));

        PrepareEngageEmail prepareEngageEmail = new PrepareEngageEmail(jdbcUrl, userName, password, awsS3bucket, bulkEmailSqsQueue);
        prepareEngageEmail.sendEngageEmailToRecipients(engageEmailLambdaMessage.getMessageToContactsId(), engageEmailLambdaMessage.getTestEmail());*/

        /*String eventMessageString = "{\"bulkType\":\"ENGAGE\",\"emailDataDto\":{\"templateId\":\"CONTACT_NOTIFICATION\",\"advanceEmailBuilder\":false,\"eventName\":\"NikTestEngageEmail\",\"eventURL\":\"niktestengageemail\",\"eventId\":32152,\"eventFormat\":\"HYBRID\",\"eventStartDate\":1726630200000,\"eventEndDate\":1726856940000,\"eventTimeZone\":\"Asia/Calcutta\",\"timeZoneId\":\"India Time\",\"eventAddress\":null,\"eventCalendarInvite\":null,\"latitude\":null,\"longitude\":null,\"eventLogoImg\":\"default_ae_images/Ae_Icn_700x350.png\",\"eventHeaderLogoImg\":\"default_ae_images/Smooth_Accelevents_Default_Event_Logo_Black.png\",\"eventCurrencySymbol\":\"$\",\"hideEventDates\":false,\"headerImgFullURL\":null,\"footerImgFullURL\":null,\"recurringEvent\":false,\"recurringEventId\":0,\"orgName\":\"Nikunj Undhad Test Org\",\"orgContactEmail\":\"<EMAIL>\",\"orgPageURL\":\"nikunjutest\",\"planName\":null,\"whiteLabelId\":0,\"whiteLabelFirmName\":null,\"whiteLabelURL\":null,\"wlHostBaseURL\":null,\"mailApiKey\":null,\"wlLogoImg\":null,\"wlHeaderLogoImg\":null,\"getStarted\":null,\"helpCenter\":null,\"privacyPolicy\":null,\"facebookShareURL\":null,\"twitterShareURL\":null,\"linkedinShareURL\":null,\"instagramShareURL\":null,\"messageBody\":null,\"messageBodyS3Key\":\"email_temp_body/32152/mtc/23747/time_1720498972915\",\"subject\":\"Custom Recipients\",\"substitutionData\":{},\"senderEmail\":null,\"senderName\":null,\"replyToEmail\":\"<EMAIL>\",\"wlNotificationEmail\":null,\"wlTransactionEmail\":null,\"wlSupportEmail\":null,\"resendTicketOrderText\":null,\"showEnterEventButtonInReminderTemplate\":true,\"totalTicketCount\":0,\"donationAmount\":0,\"ticketPdfDesign\":null,\"ticketChartKey\":null,\"templateName\":null},\"receiversData\":[{\"firstName\":null,\"lastName\":null,\"email\":\"<EMAIL>\",\"userId\":null,\"magicLink\":null,\"orderNumber\":0,\"orderStatus\":null,\"ticketNumber\":0,\"ticketTypeName\":null,\"ticketTypeDesc\":null,\"autoAssignedSeqNumber\":0,\"seatNumber\":null,\"ticketBarcodeId\":null,\"allowPDFDownload\":false,\"price\":null,\"purchaserName\":null,\"purchaserEmail\":null,\"purchaseDate\":null,\"holderEmail\":false,\"autoAssignedSeqNumberStr\":\"N/A\",\"orderNumberStr\":\"N/A\",\"ticketNumberStr\":\"N/A\",\"seatNumberStr\":\"N/A\"},{\"firstName\":null,\"lastName\":null,\"email\":\"<EMAIL>\",\"userId\":null,\"magicLink\":null,\"orderNumber\":0,\"orderStatus\":null,\"ticketNumber\":0,\"ticketTypeName\":null,\"ticketTypeDesc\":null,\"autoAssignedSeqNumber\":0,\"seatNumber\":null,\"ticketBarcodeId\":null,\"allowPDFDownload\":false,\"price\":null,\"purchaserName\":null,\"purchaserEmail\":null,\"purchaseDate\":null,\"holderEmail\":false,\"autoAssignedSeqNumberStr\":\"N/A\",\"orderNumberStr\":\"N/A\",\"ticketNumberStr\":\"N/A\",\"seatNumberStr\":\"N/A\"}],\"enumUnSubscribeLink\":\"STAFF\"}";

        BulkEmailLambdaMessage bulkEmailLambdaMessage = objectMapper.readValue(eventMessageString, BulkEmailLambdaMessage.class);

        PrepareEventDataSendGridData prepareEventDataSendGridData = new PrepareEventDataSendGridData(jdbcUrl, userName, password, awsS3bucket, environment, sendGridDefaultApiKey);
        prepareEventDataSendGridData.processEngageEmailUserDataAndSendEmail(bulkEmailLambdaMessage);*/

        /*EmailDataDto emailDataDto = new EmailDataDto(BEFREE_DEFAULT_REMINDER_MINIFIED);
        emailDataDto.setTemplateName(BEFREE_DEFAULT_REMINDER_MINIFIED.getValue());
        emailDataDto.setEventStartDate(new Date());
        Date endDate = new SimpleDateFormat("yyyy-MM-dd").parse("2024-08-08");
        emailDataDto.setEventEndDate(endDate);
        emailDataDto.setEventURL("nik-test-local");
        emailDataDto.setEventName("nik-event");
        emailDataDto.setEventTimeZone("Asia/Calcutta");
        emailDataDto.setSubject("Nik Test Reminder 1");
        emailDataDto.setEventId(2L);

        EmailUserWithTicketDto emailUserWithTicketDto = new EmailUserWithTicketDto();
        emailUserWithTicketDto.setEmail("<EMAIL>");
        emailUserWithTicketDto.setLastName("Und R1");
        emailUserWithTicketDto.setFirstName("Nik R1");

        List<EmailUserWithTicketDto> usersList = new ArrayList<>();
        usersList.add(emailUserWithTicketDto);

        BulkEmailLambdaMessage bulkEmailLambdaMessage = new BulkEmailLambdaMessage();
        bulkEmailLambdaMessage.setBulkType("REMINDER");
        bulkEmailLambdaMessage.setEmailDataDto(emailDataDto);
        bulkEmailLambdaMessage.setReceiversData(usersList);

        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String bulkEmailMessageString = objectMapper.writeValueAsString(bulkEmailLambdaMessage);

        System.out.println(bulkEmailMessageString);

        BulkEmailLambdaMessage bulkEmailLambdaMessage1 = objectMapper.readValue(bulkEmailMessageString, BulkEmailLambdaMessage.class);

        System.out.println(bulkEmailLambdaMessage1.getEmailDataDto().getEventStartDate());*/


        /*ReminderEmailLambdaMessage reminderEmailLambdaMessage = new ReminderEmailLambdaMessage();
        reminderEmailLambdaMessage.setResendTicketingEmailId(27077L);
        reminderEmailLambdaMessage.setTestEmail("<EMAIL>");

        PrepareBulkReminderEmail prepareBulkReminderEmail = new PrepareBulkReminderEmail(jdbcUrl, userName, password, awsS3bucket, bulkEmailSqsQueue);
        prepareBulkReminderEmail.sendReminderEmailToAttendees(reminderEmailLambdaMessage.getResendTicketingEmailId(), reminderEmailLambdaMessage.getTestEmail());*/

        /*String sendEmailSQS = "{\"bulkType\":\"REMINDER\",\"emailDataDto\":{\"templateId\":\"BEFREE_DEFAULT_REMINDER_MINIFIED\",\"advanceEmailBuilder\":true,\"eventName\":\"NikTestEngageEmail\",\"eventURL\":\"niktestengageemail\",\"eventId\":32152,\"eventFormat\":\"HYBRID\",\"eventStartDate\":1726630200000,\"eventEndDate\":1726856940000,\"eventTimeZone\":\"Asia/Calcutta\",\"timeZoneId\":\"India Time\",\"eventAddress\":null,\"eventCalendarInvite\":null,\"latitude\":null,\"longitude\":null,\"eventLogoImg\":\"default_ae_images/Ae_Icn_700x350.png\",\"eventHeaderLogoImg\":\"default_ae_images/Smooth_Accelevents_Default_Event_Logo_Black.png\",\"eventCurrencySymbol\":\"$\",\"hideEventDates\":false,\"headerImgFullURL\":\"https://d1jq2jmhdt3fy.cloudfront.net/default_ae_images/Ae_Icn_700x350.png\",\"footerImgFullURL\":\"https://d1jq2jmhdt3fy.cloudfront.net/default_ae_images/Smooth_Accelevents_Default_Event_Logo_Black.png\",\"recurringEvent\":false,\"recurringEventId\":0,\"orgName\":\"Nikunj Undhad Test Org\",\"orgContactEmail\":\"<EMAIL>\",\"orgPageURL\":\"nikunjutest\",\"planName\":null,\"whiteLabelId\":0,\"whiteLabelFirmName\":null,\"whiteLabelURL\":null,\"wlHostBaseURL\":null,\"mailApiKey\":null,\"wlLogoImg\":null,\"wlHeaderLogoImg\":null,\"getStarted\":null,\"helpCenter\":null,\"privacyPolicy\":null,\"facebookShareURL\":null,\"twitterShareURL\":null,\"linkedinShareURL\":null,\"instagramShareURL\":null,\"messageBody\":null,\"messageBodyS3Key\":\"email_temp_body/32152/rte/27077/time_1720535452364\",\"subject\":\"Welcome to NikTestEngageEmail\",\"substitutionData\":{},\"senderEmail\":null,\"senderName\":null,\"replyToEmail\":\"<EMAIL>\",\"wlNotificationEmail\":null,\"wlTransactionEmail\":null,\"wlSupportEmail\":null,\"resendTicketOrderText\":\"Thank you for buying tickets for Accelevents Virtual Event Conference! Please find your purchase summary below!\",\"showEnterEventButtonInReminderTemplate\":true,\"totalTicketCount\":0,\"donationAmount\":0.0,\"ticketPdfDesign\":null,\"ticketChartKey\":null,\"templateName\":\"REMINDER_EMAIL.ftl\"},\"receiversData\":[{\"firstName\":\"Nik\",\"lastName\":\"Test\",\"email\":\"<EMAIL>\",\"userId\":32083,\"magicLink\":null,\"orderNumber\":111,\"orderStatus\":\"PAID\",\"ticketNumber\":222,\"ticketTypeName\":\"TicketTypeName\",\"ticketTypeDesc\":\"Ticket type dummy description for the test email\",\"autoAssignedSeqNumber\":333,\"seatNumber\":444,\"ticketBarcodeId\":\"abc-def-test-barcode-id\",\"allowPDFDownload\":true,\"price\":100.0,\"purchaserFirstName\":\"Nik\",\"purchaserLastName\":\"Und\",\"purchaserEmail\":\"<EMAIL>\",\"purchaseDate\":1720535452329,\"holderEmail\":false,\"orderNumberStr\":\"111\",\"ticketNumberStr\":\"222\",\"seatNumberStr\":\"444\",\"autoAssignedSeqNumberStr\":\"333\"}],\"enumUnSubscribeLink\":\"STAFF\"}";

        BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage1 = objectMapper.readValue(sendEmailSQS, typeReference);

        PrepareEventDataSendGridData prepareEventDataSendGridData = new PrepareEventDataSendGridData(jdbcUrl, userName, password, awsS3bucket, environment, sendGridDefaultApiKey);
        prepareEventDataSendGridData.processReminderEmailUserDataAndSendEmail(bulkEmailLambdaMessage1);*/

       /* ReminderEmailLambdaMessage reminderEmailLambdaMessage1 = new ReminderEmailLambdaMessage();
        reminderEmailLambdaMessage1.setEventTaskReminderEmailId(2L);
        reminderEmailLambdaMessage1.setEmailSendType(MANUAL);

        PrepareBulkEventTaskReminderEmail prepareBulkEventTaskReminderEmail = new PrepareBulkEventTaskReminderEmail(jdbcUrl, userName, password, bulkEmailSqsQueue);
        prepareBulkEventTaskReminderEmail.sendReminderEmailToSpeakers(reminderEmailLambdaMessage1.getEventTaskReminderEmailId(), reminderEmailLambdaMessage1.getEmailSendType());
*/
       /* String sendEmailSQS = "{\"bulkType\":\"EVENT_TASK_REMINDER\",\"emailDataDto\":{\"templateId\":\"SPEAKER_AND_SESSION_TASK_REMINDER_EMAIL\",\"advanceEmailBuilder\":false,\"eventName\":\"Event 88\",\"eventURL\":\"event88\",\"eventId\":1798,\"eventFormat\":\"HYBRID\",\"eventStartDate\":1658156640000,\"eventEndDate\":1757525400000,\"eventTimeZone\":\"Asia/Calcutta\",\"timeZoneId\":\"India Time\",\"eventAddress\":\"Brilworks Software, Science City Road, near Shell Petrol Pump, Sola, Ahmedabad, Gujarat, India\",\"eventCalendarInvite\":null,\"latitude\":\"23.0790057\",\"longitude\":\"72.5013773\",\"eventLogoImg\":\"ad7040df-e0c0-4f70-b7ca-273833835dec\",\"eventHeaderLogoImg\":\"newheaderlogo.JPG\",\"eventCurrencySymbol\":\"$\",\"hideEventDates\":false,\"headerImgFullURL\":\"https://d1jq2jmhdt3fy.cloudfront.net/ad7040df-e0c0-4f70-b7ca-273833835dec\",\"footerImgFullURL\":\"https://d1jq2jmhdt3fy.cloudfront.net/newheaderlogo.JPG\",\"recurringEvent\":false,\"recurringEventId\":0,\"orgName\":\"EventHouse\",\"orgContactEmail\":\"<EMAIL>\",\"orgPageURL\":\"EventHouse\",\"planName\":null,\"whiteLabelId\":0,\"whiteLabelFirmName\":null,\"whiteLabelURL\":null,\"wlHostBaseURL\":null,\"mailApiKey\":null,\"wlLogoImg\":null,\"wlHeaderLogoImg\":null,\"getStarted\":null,\"helpCenter\":null,\"privacyPolicy\":null,\"facebookShareURL\":null,\"twitterShareURL\":null,\"linkedinShareURL\":null,\"instagramShareURL\":null,\"messageBody\":null,\"messageBodyS3Key\":null,\"subject\":\"\",\"substitutionData\":{},\"senderEmail\":null,\"senderName\":null,\"replyToEmail\":\"<EMAIL>\",\"wlNotificationEmail\":null,\"wlTransactionEmail\":null,\"wlSupportEmail\":null,\"resendTicketOrderText\":null,\"showEnterEventButtonInReminderTemplate\":true,\"totalTicketCount\":0,\"donationAmount\":0.0,\"ticketPdfDesign\":null,\"ticketChartKey\":null,\"taskType\":\"SESSION\",\"taskName\":\"My SPekaer Task to For SESSIOn\",\"templateName\":\"SPEAKER_AND_SESSION_TASK_REMINDER_EMAIL.ftl\",\"eventTaskOverDueEmail\":false},\"receiversData\":[{\"firstName\":\"ABC\",\"lastName\":null,\"email\":\"<EMAIL>\",\"userId\":4788,\"sessionName\":\"Test ing workshop session\"},{\"firstName\":\"Hub\",\"lastName\":null,\"email\":\"<EMAIL>\",\"userId\":4673,\"sessionName\":\"main\"},{\"firstName\":\"ABC\",\"lastName\":null,\"email\":\"<EMAIL>\",\"userId\":4788,\"sessionName\":\"Main Stage future\"}],\"enumUnSubscribeLink\":\"STAFF\"}";

        BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage2 = objectMapper.readValue(sendEmailSQS, typeReference);

        PrepareEventDataSendGridData prepareEventDataSendGridData = new PrepareEventDataSendGridData(jdbcUrl, userName, password, awsS3bucket, environment, sendGridDefaultApiKey);
        prepareEventDataSendGridData.processEventTaskReminderEmailUserDataAndSendEmail(bulkEmailLambdaMessage2);*/
    }
}
