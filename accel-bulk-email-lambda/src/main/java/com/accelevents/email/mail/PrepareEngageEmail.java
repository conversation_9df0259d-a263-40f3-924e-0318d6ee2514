package com.accelevents.email.mail;

import com.accelevents.domain.enums.EMAIL_RECIPIENTS;
import com.accelevents.domain.enums.MessageStatus;
import com.accelevents.dto.DontSendTo;
import com.accelevents.dto.lambda.BulkEmailLambdaMessage;
import com.accelevents.dto.lambda.EmailDataDto;
import com.accelevents.dto.lambda.EmailUserWithTicketDto;
import com.accelevents.email.db.DBRequest;
import com.accelevents.email.service.*;
import com.accelevents.email.utils.BulkLambdaConstants;
import com.accelevents.helpers.TemplateId;
import com.accelevents.messages.EnumUnSubscribeLink;
import com.accelevents.utils.DontSendToConverter;
import com.accelevents.utils.GeneralUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.sqs.SqsClient;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.email.utils.BulkLambdaConstants.*;
import static com.accelevents.utils.GeneralUtils.replace;
import static java.util.Map.entry;

public class PrepareEngageEmail {

    private static final Logger log = LoggerFactory.getLogger(PrepareEngageEmail.class);

    private final S3Client s3Client;
    private final SqsClient sqsClient;
    private final String AWS_S3_IMAGES_BUCKET;
    private final DBRequest dbRequest;
    private final String bulkMessageQueueURL;
    private final SqsMessageSender sqsMessageSender;
    private final RecipientListDetailsService recipientListDetailsService;
    private final QueryBuilder queryBuilder;
    private final OmitEmailAddressesFromDontSendServiceImpl omitEmailAddressesFromDontSendServiceImpl;

    public PrepareEngageEmail(String jdbcUrl, String roJdbcUrl, String userName, String password, String awsS3bucket, String bulkMessageQueueName) throws SQLException {
        dbRequest = new DBRequest(jdbcUrl, roJdbcUrl, userName, password);
        queryBuilder = new QueryBuilder();
        AdvancedFilterService advancedFilterService = new AdvancedFilterService(dbRequest, queryBuilder);
        this.AWS_S3_IMAGES_BUCKET = awsS3bucket;

        AwsCredentialsProvider cp = DefaultCredentialsProvider.create();

        Region region = Region.US_EAST_1;
        s3Client = S3Client.builder()
                .region(region)
                .credentialsProvider(cp)
                .build();

        sqsClient = SqsClient.builder()
                .region(region)
                .credentialsProvider(cp)
                .build();

        this.bulkMessageQueueURL = sqsClient.getQueueUrl(request -> request.queueName(bulkMessageQueueName)).queueUrl();
        this.sqsMessageSender = new SqsMessageSender(sqsClient);
        this.recipientListDetailsService = new RecipientListDetailsService(dbRequest, queryBuilder, advancedFilterService);
        this.omitEmailAddressesFromDontSendServiceImpl = new OmitEmailAddressesFromDontSendServiceImpl(dbRequest, new OmitEmailAddressQueryBuilder(), queryBuilder, advancedFilterService);
    }

    public void sendEngageEmailToRecipients(Long messageToContactsId, String testEmail) throws Exception {

        try {

            EmailDataDto emailDataDto = new EmailDataDto(TemplateId.CONTACT_NOTIFICATION);

            ResultSet messageToContactsRs = dbRequest.getQueryResult(queryBuilder.createMessageToContactTableQuery(messageToContactsId).toString());
            String messageBodyFromDB = "";
            String subjectFromDB = "";

            Map<String, Object> messageToContactMap = new HashMap<>();
            while (messageToContactsRs.next()) {
                messageBodyFromDB = messageToContactsRs.getString(COL_MESSAGE_BODY);
                subjectFromDB = messageToContactsRs.getString(COL_SUBJECT_LINE);
                emailDataDto.setEventId(messageToContactsRs.getLong(COL_EVENT_ID));
                emailDataDto.setReplyToEmail(messageToContactsRs.getString(COL_REPLY_TO_EMAIL));

                messageToContactMap.put(COL_EVENT_ID, messageToContactsRs.getLong(COL_EVENT_ID));
                messageToContactMap.put(COL_SEND_TO, messageToContactsRs.getString(COL_SEND_TO));
                messageToContactMap.put(COL_ALL_MEMBERS_SELECTED, messageToContactsRs.getBoolean(COL_ALL_MEMBERS_SELECTED));
                Long contactsListId = messageToContactsRs.getString(COL_CONTACTS_LIST_ID) != null ? messageToContactsRs.getLong(COL_CONTACTS_LIST_ID) : 0L;
                messageToContactMap.put(COL_CONTACTS_LIST_ID, contactsListId);
                messageToContactMap.put(COL_TICKET_TYPE_IDS, messageToContactsRs.getString(COL_TICKET_TYPE_IDS));
                messageToContactMap.put(COL_STATUS, messageToContactsRs.getString(COL_STATUS));
                String dontSendToJsonStr = messageToContactsRs.getString(DONT_SEND_TO);
                if (dontSendToJsonStr != null && !dontSendToJsonStr.isEmpty()) {
                    DontSendToConverter dontSendToConverter = new DontSendToConverter();
                    DontSendTo dontSendTo = dontSendToConverter.convertToEntityAttribute(dontSendToJsonStr);
                    messageToContactMap.put(DONT_SEND_TO, dontSendTo);
                } else {
                    messageToContactMap.put(DONT_SEND_TO, null);
                }
            }

            List<EmailUserWithTicketDto> emailRecipientList = new ArrayList<>();
            if (StringUtils.isNotEmpty(testEmail)) {
                ResultSet userDataRs = dbRequest.getQueryResult(queryBuilder.getUserDataFromEmailQuery(testEmail).toString());
                while (userDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = new EmailUserWithTicketDto(
                            userDataRs.getString(BulkLambdaConstants.COL_FIRST_NAME),
                            userDataRs.getString(BulkLambdaConstants.COL_LAST_NAME),
                            userDataRs.getString(BulkLambdaConstants.COL_EMAIL),
                            DUMMY_ORDER_NUMBER,
                            DUMMY_TICKET_NUMBER,
                            DUMMY_TICKET_NAME,
                            DUMMY_ASSIGNED_NUMBER);
                    emailRecipientList.add(emailUserWithTicketDto);
                }

                this.fetchEventDetailsAndPrepareEngageBulkEmail(messageToContactsId, emailDataDto, subjectFromDB, messageBodyFromDB, emailRecipientList);

            }else {
                if (MessageStatus.SCHEDULED.name().equals(messageToContactMap.get(COL_STATUS)) ||
                        MessageStatus.SENDING.name().equals(messageToContactMap.get(COL_STATUS))) {
                    this.updateMessageToContactStatus(messageToContactsId, MessageStatus.IN_PROGRESS, 0);

                    emailRecipientList = recipientListDetailsService.getRecipientList(messageToContactsId, EMAIL_RECIPIENTS.valueOf((String) messageToContactMap.get(COL_SEND_TO)),
                            emailDataDto.getEventId(), (Long) messageToContactMap.get(COL_CONTACTS_LIST_ID),
                            (Boolean) messageToContactMap.get(COL_ALL_MEMBERS_SELECTED),
                            (String) messageToContactMap.get(COL_TICKET_TYPE_IDS)
                    );

                    DontSendTo dontSendTo = (DontSendTo) messageToContactMap.get(DONT_SEND_TO);
                    if (!emailRecipientList.isEmpty() && dontSendTo != null) {
                        //IF email recipient list is not empty then we need to check if there is any email address that is in the omit email address list
                        List<String> omitEmailAddresses = omitEmailAddressesFromDontSendServiceImpl.getOmitEmailAddressList(messageToContactsId, EMAIL_RECIPIENTS.valueOf(dontSendTo.getEmailRecipients()),
                            emailDataDto.getEventId(), dontSendTo.getContactsListId(),
                            dontSendTo.isAllMembersSelected(),
                            GeneralUtils.convertLongListToCommaSeparated(dontSendTo.getTicketTypeIds())
                        );
                        log.info("PrepareEngageEmail | Omit Email Addresses Count: {}", omitEmailAddresses.size());

                        //If there is any email address that is in the omit email address list then we need to omit that email address from the list
                        emailRecipientList = emailRecipientList.stream()
                        .filter(e -> omitEmailAddresses.stream()
                                .map(String::toLowerCase)
                                .noneMatch(omitEmailAddress -> omitEmailAddress.equals(e.getEmail().toLowerCase())))
                        .collect(Collectors.toList());
                    }

                    this.fetchEventDetailsAndPrepareEngageBulkEmail(messageToContactsId, emailDataDto, subjectFromDB, messageBodyFromDB, emailRecipientList);

                    this.updateMessageToContactStatus(messageToContactsId, MessageStatus.SENT, emailRecipientList.size());
                    log.info("Email processing has been completed for the mtc id {}", messageToContactsId);

                } else {
                    log.info("Email not processed because mtc id {} status = {}", messageToContactsId, messageToContactMap.get(COL_STATUS));
                }
            }

        }  catch (Exception ex) {
            log.error("Exception prepare email: ", ex);
            this.updateMessageToContactStatus(messageToContactsId, MessageStatus.FAILED, 0);

            Sentry.setExtra("messageToContactId", messageToContactsId.toString());
            Sentry.captureException(ex);
        } finally {
            dbRequest.close();
            sqsClient.close();
            s3Client.close();
        }

    }

    private void fetchEventDetailsAndPrepareEngageBulkEmail(Long messageToContactsId, EmailDataDto emailDataDto, String subjectFromDB, String messageBodyFromDB, List<EmailUserWithTicketDto> emailRecipientList) throws SQLException, JsonProcessingException, InterruptedException {
        ResultSet eventRs = dbRequest.getQueryResult(queryBuilder.createEventWithDesignAndTicketingTableQuery(emailDataDto.getEventId()).toString());
        while (eventRs.next()) {
            ResultSetResolver.updateEventDataFromTheEventResultSet(eventRs, emailDataDto);
        }

        if (emailDataDto.getWhiteLabelId() > 0) {
            ResultSet whiteLabelRs = dbRequest.getQueryResult(queryBuilder.createWhiteLabelTableQuery(emailDataDto.getWhiteLabelId()).toString());
            while (whiteLabelRs.next()) {
                ResultSetResolver.updateWhiteLabelDataFromTheWhiteLabelResultSet(whiteLabelRs, emailDataDto);
            }
        }

        String subjectLine = this.replaceEventDetails(subjectFromDB, emailDataDto.getEventName());
        String messageBodyFinal = this.replaceEventDetails(messageBodyFromDB, emailDataDto.getEventName());

        emailDataDto.setSubject(subjectLine);

        String uploadKey = "email_temp_body/" + emailDataDto.getEventId() + "/mtc/" + messageToContactsId + "/time_" + new Date().getTime();

        LambdaS3Service s3UploadService = new LambdaS3Service(s3Client);

        s3UploadService.uploadInBucket(messageBodyFinal, uploadKey, AWS_S3_IMAGES_BUCKET);
        System.out.println("s3 key => " + uploadKey);

        emailDataDto.setMessageBodyS3Key(uploadKey);

        int size = emailRecipientList.size();
        int batchCount = 1;

        for (int i = 0; i < size; i += 10) {
            this.sendBulkEmailMessageToSqs(emailDataDto, new ArrayList<>(emailRecipientList.subList(i, Math.min(size, i + 10))));
            log.info("Batch Completed: {}", batchCount++);
            Thread.sleep(100);
        }
    }

    private void sendBulkEmailMessageToSqs(EmailDataDto emailDataDto, List<EmailUserWithTicketDto> emailRecipientList) throws JsonProcessingException {
        BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage = new BulkEmailLambdaMessage<>("ENGAGE", emailDataDto, emailRecipientList, EnumUnSubscribeLink.STAFF);

        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String bulkEmailMessageString = objectMapper.writeValueAsString(bulkEmailLambdaMessage);

        log.info("SQS Msg => {}", bulkEmailMessageString);
        sqsMessageSender.sendSQSMessage(this.bulkMessageQueueURL, bulkEmailMessageString);
    }


    private String replaceEventDetails(String messageBody, String eventName) {
        return replace(messageBody, "$EventName", eventName);
    }

    private void updateMessageToContactStatus(Long messageToContactId, MessageStatus messageStatus, int size) throws SQLException {
        Map<String, Object> entries;
        if(MessageStatus.SENT.equals(messageStatus)) {
            java.sql.Timestamp currentSqlDate = new java.sql.Timestamp(new Date().getTime());
            entries = Map.ofEntries(
                    entry(COL_STATUS, messageStatus.name()),
                    entry("sent_to_count", size),
                    entry("date_sent", currentSqlDate),
                    entry(COL_UPDATED_AT, currentSqlDate)
            );
        }else {
            entries = Map.ofEntries(
                    entry(COL_STATUS, messageStatus.name()),
                    entry(COL_UPDATED_AT, new java.sql.Timestamp(new Date().getTime()))
            );
        }

        dbRequest.updateMessageToContactStatus(messageToContactId, entries);
    }

}
