package com.accelevents.email.mail;

import com.accelevents.configuration.FreeMarkerConfiguration;
import com.accelevents.dto.lambda.EmailDataDto;
import com.accelevents.messages.EnumUnSubscribeLink;
import com.accelevents.utils.Constants;
import com.accelevents.utils.EmailUtils;
import com.accelevents.utils.SecurityUtils;
import com.sendgrid.*;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import io.sentry.Sentry;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.isValidEmailAddress;
import static org.apache.commons.lang3.StringUtils.EMPTY;

public class SendGridMailLambdaServiceImpl {

    private static final Logger log = LoggerFactory.getLogger(SendGridMailLambdaServiceImpl.class);

	private String sendGridDefaultApiKey;

    private String environment;

    private EmailDataDto emailDataDto;

	private EnumUnSubscribeLink enumUnSubscribeLink;

	public SendGridMailLambdaServiceImpl(EmailDataDto emailDataDto, EnumUnSubscribeLink enumUnSubscribeLink, String environment, String sendGridDefaultApiKey) {
		this.emailDataDto = emailDataDto;
		this.enumUnSubscribeLink = enumUnSubscribeLink;
        this.environment = environment;
        this.sendGridDefaultApiKey = sendGridDefaultApiKey;
	}

    private void prepareSubstitutionData(EmailDataDto emailDataDto) {
        emailDataDto.getSubstitutionData()
                .putAll(EmailUtils.getEmailDemographicDetails(
                        null!=emailDataDto.getWhiteLabelURL(),
                        null!=emailDataDto.getWhiteLabelFirmName() ? emailDataDto.getWhiteLabelFirmName() : EMPTY,
                        null!=emailDataDto.getPlanName() ? emailDataDto.getPlanName() : EMPTY));
    }

    private String getMessageBody(String messageBody) throws
            IOException, TemplateException {
        if(StringUtils.isNotBlank(this.emailDataDto.getTemplateName())){
            Configuration freemarkerMailConfiguration = FreeMarkerConfiguration.getInstance();
            if(this.emailDataDto.isAdvanceEmailBuilder() && StringUtils.isNotEmpty(messageBody)){
                Template template = new Template(BEEFREE_CUSTOM_TEMPLATE, new StringReader(messageBody),freemarkerMailConfiguration);
                StringWriter result = new StringWriter(1024);
                template.process(this.emailDataDto.getSubstitutionData(), result);
                return result.toString();
            }else {
                StringWriter result = new StringWriter(1024);
                Template template = freemarkerMailConfiguration.getTemplate(this.emailDataDto.getTemplateName());
                template.process(this.emailDataDto.getSubstitutionData(), result);
                return result.toString();
            }
        } else {
            return replaceWithData(messageBody);
        }
    }

    //this is only used for ticketing as of now
    //here we expect all value will be string for ticketing
    // we need to use some template later
    private String replaceWithData(String messageBody) {
        for(Entry<String, Object> data: this.emailDataDto.getSubstitutionData().entrySet()){
            if(data.getValue() instanceof String)
                messageBody = messageBody.replace("${" + data.getKey() + "}", (String) data.getValue());
        }
        return messageBody;
    }

	public void send(String receiver, String messageBody, List<Attachments> attachments) {
        send(receiver, messageBody, attachments, null);
    }

    public void send(String receiver, String messageBody, List<Attachments> attachments, Set<String> ccEmails) {
        try {
            if (isValidEmailAddress(receiver)) {

                String apiKey = sendGridDefaultApiKey;
                if (StringUtils.isNotEmpty(emailDataDto.getMailApiKey())) {
                    apiKey = emailDataDto.getMailApiKey();
                }
                SendGrid sg = new SendGrid(apiKey);
                prepareSubstitutionData(emailDataDto);

                Content content = new Content("text/html", getMessageBody(messageBody));

                handleUnSubscribeLink(receiver, content);

                Mail mail = prepareMail(content, receiver, ccEmails);

                this.addAttachments(mail, attachments);

                Request request = new Request();

                request.method = Method.POST;
                request.endpoint = "mail/send";
                request.body = mail.build();

                try {
                    sg.api(request);
                    log.info("Mail to => {} , subject => {}", receiver, emailDataDto.getSubject());
                } catch (IOException e) {
                    log.error("Mail to => {}, Mail content => {}", receiver, content.getValue());
                    throw e;
                }

            } else {
                log.warn("found invalid email address: {} and email subject: {} and eventId: {}", receiver, emailDataDto.getSubject(),emailDataDto.getEventId());
            }
        } catch (Exception ex) {
            log.error("SendMailThread", ex);

            Sentry.setExtra("EventId", emailDataDto.getEventId().toString());
            Sentry.setExtra("Subject", emailDataDto.getSubject());
            Sentry.setExtra("receiver", receiver);
            Sentry.captureException(ex);
        }
	}

    private void handleUnSubscribeLink(String receiver, Content content) {
        if(enumUnSubscribeLink!=null){
            Map<String,Object> substitutionData = this.emailDataDto.getSubstitutionData();
            Long eventId = (Long) substitutionData.get(Constants.EVENT_ID_UPPERCASE);
            String baseUrl = (String) substitutionData.get(Constants.BASE_URL);
            List<String> staffEmailList = (List<String>) substitutionData.get(Constants.STAFF_EMAIL_LIST);
            if(staffEmailList!=null && staffEmailList.contains(receiver)){
                String unSubscribeLink = enumUnSubscribeLink.getValue();
                unSubscribeLink = unSubscribeLink.replace("[base_url]", baseUrl);
                unSubscribeLink = unSubscribeLink.replace("[encodedString]", SecurityUtils.encode(Constants.EVENT_ID_UPPERCASE +"="+eventId+"&"+Constants.EMAIL+"="+receiver));

                String finalHtml = setUnSubscribeLink(content.getValue(),unSubscribeLink);
                content.setValue(finalHtml);
            } else {
                String finalHtml = setUnSubscribeLink(content.getValue());
                content.setValue(finalHtml);
            }
        } else {
            String finalHtml = setUnSubscribeLink(content.getValue());
            content.setValue(finalHtml);
        }
    }

    private Mail prepareMail(Content content, String receiver) {
        return prepareMail(content, receiver, null);
    }

    private Mail prepareMail(Content content, String receiver, Set<String> ccEmails) {

        String senderMail = EmailUtils.getSenderEmailBasedOnEnv(this.emailDataDto.getSenderEmail(),
                environment, this.emailDataDto.getTemplateId(),
               this.emailDataDto.getWlNotificationEmail(),
                this.emailDataDto.getWlTransactionEmail()
        );

        String senderName = EmailUtils.getEmailSenderName(this.emailDataDto.getSenderName(),
                this.emailDataDto.getWhiteLabelFirmName(),
                this.emailDataDto.getPlanName());

        String replyTo = EmailUtils.getReplyToEmailBasedOnWLOrEnv(environment, this.emailDataDto.getReplyToEmail(), null);

        Mail mail = new Mail(new Email(senderMail, senderName), this.emailDataDto.getSubject(), new Email(receiver), content);
        mail.setReplyTo(new Email(replyTo));

        // Add CC emails if provided
        if (CollectionUtils.isNotEmpty(ccEmails)) {
            Personalization personalization = new Personalization();

            // Remove receiver from CC list to avoid duplicate
            ccEmails.remove(receiver);

            // Add CC emails
            for (String ccEmail : ccEmails) {
                if (isValidEmailAddress(ccEmail)) {
                    personalization.addCc(new Email(ccEmail));
                    log.info("Added CC email: {}", ccEmail);
                } else {
                    log.warn("Invalid CC email address skipped: {}", ccEmail);
                }
            }

            // Add the main recipient
            personalization.addTo(new Email(receiver));
            mail.addPersonalization(personalization);
        }

        if(Constants.ENV_PROD.equals(environment) && this.emailDataDto.getWhiteLabelURL() == null){
            mail.setIpPoolId(EmailUtils.getEmailPool(this.emailDataDto.getTemplateId()));
        }

        if(null != this.emailDataDto.getEventId() && this.emailDataDto.getEventId() > 0){
            String eventId = String.valueOf(this.emailDataDto.getEventId());
            mail.addCustomArg(Constants.EVENT_ID,eventId);

            if(ENGAGE_EMAIL_POOL.equalsIgnoreCase(EmailUtils.getEmailPool(this.emailDataDto.getTemplateId()))) {
                String baseUrl = String.valueOf(this.emailDataDto.getSubstitutionData().get(BASE_URL));
                String encodedString = SecurityUtils.encode(EVENT_ID_UPPERCASE +STRING_EQUAL+eventId+AMPERSAND+EMAIL+STRING_EQUAL+receiver);
                String httpHeader = String.format("<%s>", baseUrl + UNSUBSCRIBE_LINK_VALUE +encodedString);
                String mailToHeader = String.format("<mailto:%s?subject=unsubscribe>",replyTo);

                mail.addHeader(LIST_UNSUBSCRIBE_POST,LIST_UNSUBSCRIBE_ONE_CLICK);
                mail.addHeader(LIST_UNSUBSCRIBE, mailToHeader+COMMA_SPACE+httpHeader);
            }

        }else{
            log.warn("EventId not found for Mail to {} and Subject {}", receiver, mail.getSubject());
        }
        return mail;
    }

    private void addAttachments(Mail mail, List<Attachments> attachments){
        if(CollectionUtils.isNotEmpty(attachments)){
            attachments.forEach(e -> {
                if(e != null){
                    mail.addAttachments(e);
                }
            });
        }
    }

    private String setUnSubscribeLink(String html, String text) {
        if(text!=null){
            return html.replace("[unsubscribe_link]",text);
        } else {
            return html.replace("[unsubscribe_link]","To unsubscribe, go to your email preferences page.");
        }
    }
    private String setUnSubscribeLink(String html) {
        return this.setUnSubscribeLink(html,null);
    }


}
