package com.accelevents.email.mail;

import com.accelevents.domain.ResendTicketingEmail;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.MessageStatus;
import com.accelevents.dto.lambda.BulkEmailLambdaMessage;
import com.accelevents.dto.lambda.EmailDataDto;
import com.accelevents.dto.lambda.EmailUserWithTicketDto;
import com.accelevents.email.db.DBRequest;
import com.accelevents.email.service.*;
import com.accelevents.helpers.BuyerAttributesMergeTagHelper;
import com.accelevents.helpers.EmailHelper;
import com.accelevents.helpers.EmailLogoImageHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.messages.EnumUnSubscribeLink;
import com.accelevents.utils.StringTools;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.sqs.SqsClient;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import static com.accelevents.email.utils.BulkLambdaConstants.*;
import static com.accelevents.helpers.TemplateId.BEFREE_DEFAULT_REMINDER_MINIFIED;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.replace;
import static java.util.Map.entry;

public class PrepareBulkReminderEmail {

    private static final Logger log = LoggerFactory.getLogger(PrepareBulkReminderEmail.class);

    private final S3Client s3Client;
    private final SqsClient sqsClient;
    private final String AWS_S3_IMAGES_BUCKET;
    private final DBRequest dbRequest;
    private final String bulkMessageQueueURL;
    private final SqsMessageSender sqsMessageSender;
    private final EnvValuesConfiguration envConfig;
    private final QueryBuilder queryBuilder;
    private final EmailLogoImageHelper emailLogoImageHelper;
    private final RecipientListDetailsService recipientListDetailsService;
    private final AdvancedFilterService advancedFilterService;

    public PrepareBulkReminderEmail(String jdbcUrl, String roJdbcUrl, String userName, String password, String awsS3bucket, String bulkMessageQueueName) throws SQLException {
        dbRequest = new DBRequest(jdbcUrl, roJdbcUrl, userName, password);
        envConfig = new EnvValuesConfiguration();
        queryBuilder = new QueryBuilder();
        advancedFilterService = new AdvancedFilterService(dbRequest,queryBuilder);
        emailLogoImageHelper = new EmailLogoImageHelper(envConfig.getImagePreFix(), envConfig.getBlackLogo(), envConfig.getImagesAcceleventlogo(), envConfig.getDefaultAcceleventsLogo());
        
        this.AWS_S3_IMAGES_BUCKET = awsS3bucket;

        AwsCredentialsProvider cp = DefaultCredentialsProvider.create();

        Region region = Region.US_EAST_1;
        s3Client = S3Client.builder()
                .region(region)
                .credentialsProvider(cp)
                .build();

        sqsClient = SqsClient.builder()
                .region(region)
                .credentialsProvider(cp)
                .build();

        this.bulkMessageQueueURL = sqsClient.getQueueUrl(request -> request.queueName(bulkMessageQueueName)).queueUrl();
        this.sqsMessageSender = new SqsMessageSender(sqsClient);
        this.recipientListDetailsService = new RecipientListDetailsService(dbRequest, queryBuilder, advancedFilterService);
    }

    public void sendReminderEmailToAttendees(Long resendTicketingEmailId, String testEmail) throws Exception {

        try {

            EmailDataDto emailDataDto = new EmailDataDto(TemplateId.BEFREE_DEFAULT_REMINDER_MINIFIED);

            ResultSet resendTicketingEmailRS = dbRequest.getQueryResult(queryBuilder.getResendTicketingEmailQuery(resendTicketingEmailId).toString());

            Map<String, Object> resendTicketingEmailMap = new HashMap<>();
            while (resendTicketingEmailRS.next()){
                emailDataDto.setEventId(resendTicketingEmailRS.getLong(COL_EVENT_ID));
                emailDataDto.setResendTicketOrderText(resendTicketingEmailRS.getString(COL_RESEND_TICKET_ORDER_TEXT));
                emailDataDto.setSubject(resendTicketingEmailRS.getString(COL_RESEND_TICKET_SUBJECT));

                resendTicketingEmailMap.put(COL_EVENT_ID, resendTicketingEmailRS.getLong(COL_EVENT_ID));
                resendTicketingEmailMap.put(COL_RECURRING_EVENT_ID, resendTicketingEmailRS.getLong(COL_RECURRING_EVENT_ID));
                resendTicketingEmailMap.put(COL_RESEND_TICKET_ORDER_TEXT, resendTicketingEmailRS.getString(COL_RESEND_TICKET_ORDER_TEXT));
                resendTicketingEmailMap.put(COL_RESEND_TICKET_SUBJECT, resendTicketingEmailRS.getString(COL_RESEND_TICKET_SUBJECT));
                resendTicketingEmailMap.put(COL_RESEND_TICKET_STATUS, resendTicketingEmailRS.getString(COL_RESEND_TICKET_STATUS));
                resendTicketingEmailMap.put(COL_RECIPIENT_TYPE, resendTicketingEmailRS.getString(COL_RECIPIENT_TYPE));
                resendTicketingEmailMap.put(COL_HTML_VALUE, resendTicketingEmailRS.getString(COL_HTML_VALUE));
                resendTicketingEmailMap.put(COL_JSON_VALUE, resendTicketingEmailRS.getString(COL_JSON_VALUE));
                resendTicketingEmailMap.put(COL_IS_ADVANCE_EMAIL_BUILDER, resendTicketingEmailRS.getBoolean(COL_IS_ADVANCE_EMAIL_BUILDER));
                resendTicketingEmailMap.put(COL_ALLOWED_TICKET_TYPES_FOR_REMINDER, resendTicketingEmailRS.getString(COL_ALLOWED_TICKET_TYPES_FOR_REMINDER));
            }

            List<EmailUserWithTicketDto> emailRecipientList = new ArrayList<>();
            if(StringUtils.isNotEmpty(testEmail)){
                ResultSet userDataRs = dbRequest.getQueryResult(queryBuilder.getUserDataFromEmailQuery(testEmail).toString());
                String  allowedTicketTypeIds = (String)resendTicketingEmailMap.get(COL_ALLOWED_TICKET_TYPES_FOR_REMINDER);
                ResultSet isAllowPdfDataRS = dbRequest.getQueryResult(queryBuilder.getAllowDownloadPdfTicketForTestEmailQuery(emailDataDto.getEventId(),allowedTicketTypeIds).toString());
                boolean isAllowPDFDownload = false;
                while(isAllowPdfDataRS.next()){
                    if(isAllowPdfDataRS.getBoolean("allow_pdf_download")) isAllowPDFDownload = true;
                }
                while (userDataRs.next()) {
                    EmailUserWithTicketDto emailUserWithTicketDto = new EmailUserWithTicketDto(
                            userDataRs.getString(COL_FIRST_NAME),
                            userDataRs.getString(COL_LAST_NAME),
                            userDataRs.getString(COL_EMAIL),
                            DUMMY_ORDER_NUMBER,
                            DUMMY_TICKET_NUMBER,
                            DUMMY_TICKET_NAME,
                            DUMMY_ASSIGNED_NUMBER);

                    emailUserWithTicketDto.setUserId(userDataRs.getLong(COL_USER_ID));
                    emailUserWithTicketDto.setTicketBarcodeId(DUMMY_BARCODE_ID);
                    emailUserWithTicketDto.setPurchaserFirstName(userDataRs.getString(COL_FIRST_NAME));
                    emailUserWithTicketDto.setPurchaserLastName(userDataRs.getString(COL_LAST_NAME));
                    emailUserWithTicketDto.setPurchaserEmail(userDataRs.getString(COL_EMAIL));
                    emailUserWithTicketDto.setAllowPDFDownload(isAllowPDFDownload);
                    emailUserWithTicketDto.setPurchaseDate(new Date());
                    emailUserWithTicketDto.setSeatNumber(DUMMY_SEAT_NUMBER);
                    emailUserWithTicketDto.setTicketTypeDesc("Ticket type dummy description for the test email");
                    emailUserWithTicketDto.setPrice(100.0);
                    emailUserWithTicketDto.setOrderStatus("PAID");
                    if (StringUtils.isNotEmpty((String) resendTicketingEmailMap.get(COL_HTML_VALUE))) {
                        replaceMergeTagForTestEmailsWithDummyValues((String) resendTicketingEmailMap.get(COL_HTML_VALUE),emailUserWithTicketDto.getBuyerAttributesValuesMap(),emailUserWithTicketDto.getHolderAttributesValuesMap());
                    }
                    emailRecipientList.add(emailUserWithTicketDto);
                }

                this.fetchEventDetailsAndPrepareBulkEmail(resendTicketingEmailId, emailDataDto, resendTicketingEmailMap, emailRecipientList);

            } else {

                if(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED.name().equals(resendTicketingEmailMap.get(COL_RESEND_TICKET_STATUS))) {
                    this.updateResendTicketingEmailStatus(resendTicketingEmailId, MessageStatus.IN_PROGRESS);
                    ResultSet listOfHolderRequiredAttribute=dbRequest.getQueryResult(queryBuilder.getAllTicketHolderRequiredAttributesByIdsAndEventId(emailDataDto.getEventId()).toString());
                    Map<Long,TicketHolderRequiredAttributes> ticketHolderRequiredAttributesMap = fetchTicketHolderRequiredAttributesMap(listOfHolderRequiredAttribute);
                    ResultSet listOfBuyerRequiredAttribute=dbRequest.getQueryResult(queryBuilder.getAllTicketHolderRequiredAttributesByIdsAndEventId(emailDataDto.getEventId()).toString());
                    Map<Long,TicketHolderRequiredAttributes> ticketBuyerRequiredAttributesMap = fetchTicketBuyerRequiredAttributesMap(listOfBuyerRequiredAttribute);
                    Map<String,TicketHolderRequiredAttributes> emailTicketBuyerRequiredAttributesMap = prepareEmailTicketBuyersMergeTags(ticketBuyerRequiredAttributesMap,(String)resendTicketingEmailMap.get(COL_HTML_VALUE));
                    Map<String,TicketHolderRequiredAttributes> emailTicketHolderRequiredAttributesMap = prepareEmailTicketHolderMergeTags(ticketHolderRequiredAttributesMap,(String)resendTicketingEmailMap.get(COL_HTML_VALUE));
                    emailRecipientList = recipientListDetailsService.getResendReminderEmailRecipientList(StringTools.getValueOrDefault((String) resendTicketingEmailMap.get(COL_ALLOWED_TICKET_TYPES_FOR_REMINDER), "''"),
                            StringTools.getValueOrDefault((String) resendTicketingEmailMap.get(COL_RECIPIENT_TYPE), "''"),
                            emailDataDto.getEventId(),emailTicketBuyerRequiredAttributesMap,emailTicketHolderRequiredAttributesMap
                    );

                    if (!emailRecipientList.isEmpty()) {
                        this.fetchEventDetailsAndPrepareBulkEmail(resendTicketingEmailId, emailDataDto, resendTicketingEmailMap, emailRecipientList);
                    }

                    this.updateResendTicketingEmailStatus(resendTicketingEmailId, MessageStatus.SENT);
                    log.info("Resend Ticketing Email processed completed for the id {}", resendTicketingEmailId);

                }else {
                    log.info("Resend Ticketing Email not processed because id {} status = {}", resendTicketingEmailId, resendTicketingEmailMap.get(COL_RESEND_TICKET_STATUS));
                }
            }

        }  catch (Exception ex) {
            log.error("Exception prepare email: ", ex);
            this.updateResendTicketingEmailStatus(resendTicketingEmailId, MessageStatus.FAILED);

            Sentry.setExtra("resendTicketingEmailId", resendTicketingEmailId.toString());
            Sentry.captureException(ex);
        } finally {
            dbRequest.close();
            sqsClient.close();
            s3Client.close();
        }

    }

    private void replaceMergeTagForTestEmailsWithDummyValues(String htmlValue, Map<String, Object> buyerAttributesValuesMap, Map<String, Object> holderAttributesValuesMap) {
        List<String> mergeTags = BuyerAttributesMergeTagHelper.findMergeTagsFromHtmlValueAndMergeTagType(htmlValue,MergeTagType.BUYER);
        List<String> holderMergeTags = BuyerAttributesMergeTagHelper.findMergeTagsFromHtmlValueAndMergeTagType(htmlValue,MergeTagType.HOLDER);
        boolean hasBuyerTagsAvailable = !CollectionUtils.isEmpty(mergeTags);
        boolean hasHolderTagsAvailable = !CollectionUtils.isEmpty(holderMergeTags);
        if (hasBuyerTagsAvailable && hasHolderTagsAvailable) {
            // replace buyer merge tags with dummy values
            BuyerAttributesMergeTagHelper.replaceMergeTagForTheTestEmails(mergeTags, buyerAttributesValuesMap);
            // replace holder merge tags with blank strings
            for (String holderMergeTag : holderMergeTags) {
                String tag = holderMergeTag.replace(DOLLAR.concat(OPEN_CURLY_BRACE), STRING_EMPTY).replace(CLOSE_CURLY_BRACE, STRING_EMPTY);
                holderAttributesValuesMap.put(tag, STRING_BLANK);
            }
        } else if (hasBuyerTagsAvailable) {
            BuyerAttributesMergeTagHelper.replaceMergeTagForTheTestEmails(mergeTags, holderAttributesValuesMap);
        } else if (hasHolderTagsAvailable) {
            BuyerAttributesMergeTagHelper.replaceMergeTagForTheTestEmails(holderMergeTags, holderAttributesValuesMap);
        }
    }

    private Map<String, TicketHolderRequiredAttributes> prepareEmailTicketHolderMergeTags(Map<Long, TicketHolderRequiredAttributes> ticketHolderRequiredAttributesMap, String htmlValue) {
        Map<String,TicketHolderRequiredAttributes> emailTicketHolderRequiredAttributesMap = new HashMap<>();
        if (StringUtils.isNotEmpty(htmlValue) && !CollectionUtils.isEmpty(ticketHolderRequiredAttributesMap)) {
            List<String> mergeTags = BuyerAttributesMergeTagHelper.findMergeTagsFromHtmlValueAndMergeTagType(htmlValue,MergeTagType.HOLDER);
            emailTicketHolderRequiredAttributesMap = getEmailTicketBuyerRequiredAttributesMap(ticketHolderRequiredAttributesMap, mergeTags,false);
        }
        return emailTicketHolderRequiredAttributesMap;
    }

    private Map<String, TicketHolderRequiredAttributes> prepareEmailTicketBuyersMergeTags(Map<Long, TicketHolderRequiredAttributes> ticketBuyerRequiredAttributesMap, String htmlValue) {
        Map<String,TicketHolderRequiredAttributes> emailTicketBuyerRequiredAttributesMap = new HashMap<>();
        if (StringUtils.isNotEmpty(htmlValue) && !CollectionUtils.isEmpty(ticketBuyerRequiredAttributesMap)) {
            List<String> mergeTags = BuyerAttributesMergeTagHelper.findMergeTagsFromHtmlValueAndMergeTagType(htmlValue,MergeTagType.BUYER);
            emailTicketBuyerRequiredAttributesMap = getEmailTicketBuyerRequiredAttributesMap(ticketBuyerRequiredAttributesMap, mergeTags,true);
        }
        return emailTicketBuyerRequiredAttributesMap;
    }

    private Map<String, TicketHolderRequiredAttributes> getEmailTicketBuyerRequiredAttributesMap(Map<Long, TicketHolderRequiredAttributes> ticketBuyerRequiredAttributesMap, List<String> mergeTags,boolean isForBuyer) {
        Map<String,TicketHolderRequiredAttributes> emailTicketBuyerRequiredAttributesMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(mergeTags)) {
            for (String tag : mergeTags) {
                String mergeTagType = isForBuyer ? AttendeeTypeEnum.BUYER.getValue() : AttendeeTypeEnum.HOLDER.getValue();
                String idString = tag.replace(DOLLAR.concat(OPEN_CURLY_BRACE).concat(mergeTagType).concat(STRING_UNDERSCORE), STRING_EMPTY).replace(CLOSE_CURLY_BRACE, STRING_EMPTY);
                try {
                    Long id = Long.parseLong(idString);
                    String mapKey = tag.replace(DOLLAR.concat(OPEN_CURLY_BRACE), STRING_EMPTY).replace(CLOSE_CURLY_BRACE, STRING_EMPTY);
                    if (ticketBuyerRequiredAttributesMap.containsKey(id)) {
                        TicketHolderRequiredAttributes attributes = ticketBuyerRequiredAttributesMap.getOrDefault(id, null);
                        if (attributes != null) {
                            emailTicketBuyerRequiredAttributesMap.put(mapKey, attributes);
                        }
                    } else {
                        emailTicketBuyerRequiredAttributesMap.put(mapKey, null);
                    }
                } catch (NumberFormatException e) {
                    log.error("Error while parsing buyer attribute id from merge tag: {}", tag, e);
                }
            }
        }
        return emailTicketBuyerRequiredAttributesMap;
    }

    private Map<Long, TicketHolderRequiredAttributes> fetchTicketBuyerRequiredAttributesMap(ResultSet listOfHolderRequiredAttribute) throws SQLException {
        Map<Long, TicketHolderRequiredAttributes> ticketBuyerRequiredAttributesMap = new HashMap<>();
        while (listOfHolderRequiredAttribute.next()) {
            if (listOfHolderRequiredAttribute.getBoolean("enabled_for_ticket_purchaser")) {
                TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
                ticketHolderRequiredAttributes.setName(listOfHolderRequiredAttribute.getString(COL_ATTRIBUTE_NAME));
                ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.valueOf(listOfHolderRequiredAttribute.getString(COL_ATTRIBUTE_VALUE_TYPE)));
                ticketBuyerRequiredAttributesMap.put(listOfHolderRequiredAttribute.getLong(COL_ID), ticketHolderRequiredAttributes);
            }
        }
        return ticketBuyerRequiredAttributesMap;
    }

    private Map<Long, TicketHolderRequiredAttributes> fetchTicketHolderRequiredAttributesMap(ResultSet listOfHolderRequiredAttribute) throws SQLException {
        Map<Long, TicketHolderRequiredAttributes> ticketBuyerRequiredAttributesMap = new HashMap<>();
        while (listOfHolderRequiredAttribute.next()) {
            if (listOfHolderRequiredAttribute.getBoolean("enabled_for_ticket_holder")) {
                TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
                ticketHolderRequiredAttributes.setName(listOfHolderRequiredAttribute.getString(COL_ATTRIBUTE_NAME));
                ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.valueOf(listOfHolderRequiredAttribute.getString(COL_ATTRIBUTE_VALUE_TYPE)));
                ticketBuyerRequiredAttributesMap.put(listOfHolderRequiredAttribute.getLong(COL_ID), ticketHolderRequiredAttributes);
            }
        }
        return ticketBuyerRequiredAttributesMap;
    }

    private void fetchEventDetailsAndPrepareBulkEmail(Long resendTicketingEmailId, EmailDataDto emailDataDto, Map<String, Object> resendTicketingEmailMap, List<EmailUserWithTicketDto> emailRecipientList) throws SQLException, JsonProcessingException, InterruptedException {
        ResultSet eventRs = dbRequest.getQueryResult(queryBuilder.createEventWithDesignAndTicketingTableQuery(emailDataDto.getEventId()).toString());
        while (eventRs.next()) {
            ResultSetResolver.updateEventDataFromTheEventResultSet(eventRs, emailDataDto);
        }

        if (emailDataDto.getWhiteLabelId() > 0) {
            ResultSet whiteLabelRs = dbRequest.getQueryResult(queryBuilder.createWhiteLabelTableQuery(emailDataDto.getWhiteLabelId()).toString());
            while (whiteLabelRs.next()) {
                ResultSetResolver.updateWhiteLabelDataFromTheWhiteLabelResultSet(whiteLabelRs, emailDataDto);
            }
        }

        String subjectLine = (String) resendTicketingEmailMap.get(COL_RESEND_TICKET_SUBJECT);
        emailDataDto.setSubject(subjectLine);
        emailDataDto.setTemplateName(BEFREE_DEFAULT_REMINDER_MINIFIED.getValue());

        emailDataDto.setHeaderImgFullURL(emailLogoImageHelper.getEventOrDefaultLogo(emailDataDto.getEventLogoImg()));
        emailDataDto.setFooterImgFullURL(emailLogoImageHelper.getHeaderEventLogo(emailDataDto.getEventHeaderLogoImg()));

        boolean isAdvanceEmailBuilder = (boolean) (resendTicketingEmailMap.get(COL_IS_ADVANCE_EMAIL_BUILDER) != null ? resendTicketingEmailMap.get(COL_IS_ADVANCE_EMAIL_BUILDER): false);
        emailDataDto.setAdvanceEmailBuilder(isAdvanceEmailBuilder);

        if(isAdvanceEmailBuilder && resendTicketingEmailMap.get(COL_HTML_VALUE) != null) {
            String emailMessageBody = (String) resendTicketingEmailMap.get(COL_HTML_VALUE);

            String uploadKey = "email_temp_body/" + emailDataDto.getEventId() + "/rte/" + resendTicketingEmailId + "/time_" + new Date().getTime();

            LambdaS3Service s3UploadService = new LambdaS3Service(s3Client);

            s3UploadService.uploadInBucket(emailMessageBody, uploadKey, AWS_S3_IMAGES_BUCKET);
            System.out.println("s3 key => " + uploadKey);

            emailDataDto.setMessageBodyS3Key(uploadKey);

            String advanceEmailBuilderJson = (String) resendTicketingEmailMap.get(COL_JSON_VALUE);
            if(advanceEmailBuilderJson != null){
                String headerImageSrc = EmailHelper.findDynamicValueFromBeeFreeJSON(advanceEmailBuilderJson, IMAGE_LOWER);
                String footerImageSrc = EmailHelper.findDynamicValueFromBeeFreeJSON(advanceEmailBuilderJson, IMAGE_FOOTER);

                if(StringUtils.isNotEmpty(headerImageSrc)){
                    emailDataDto.setHeaderImgFullURL(headerImageSrc);
                }

                if(StringUtils.isNotEmpty(footerImageSrc)){
                    emailDataDto.setFooterImgFullURL(footerImageSrc);
                }
            }
        }

        int size = emailRecipientList.size();
        int batchCount = 1;

        for (int i = 0; i < size; i += 10) {
            this.sendBulkEmailMessageToSqs(emailDataDto, new ArrayList<>(emailRecipientList.subList(i, Math.min(size, i + 10))));
            log.info("Batch Completed: {}", batchCount++);
            Thread.sleep(200);
        }
    }


    private void sendBulkEmailMessageToSqs(EmailDataDto emailDataDto, List<EmailUserWithTicketDto> emailRecipientList) throws JsonProcessingException {
        BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage = new BulkEmailLambdaMessage<>("REMINDER", emailDataDto, emailRecipientList, EnumUnSubscribeLink.STAFF);

        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String bulkEmailMessageString = objectMapper.writeValueAsString(bulkEmailLambdaMessage);

        log.info("SQS Msg => {}", bulkEmailMessageString);
        sqsMessageSender.sendSQSMessage(this.bulkMessageQueueURL, bulkEmailMessageString);
    }


    private String replaceEventDetails(String messageBody, String eventName) {
        return replace(messageBody, "$EventName", eventName);
    }

    private void updateResendTicketingEmailStatus(Long resendTicketingId, MessageStatus messageStatus) throws SQLException {
        Map<String, Object> entries;
        if(MessageStatus.SENT.equals(messageStatus)) {
            entries = Map.ofEntries(
                    entry(COL_RESEND_TICKET_STATUS, messageStatus.name()),
                    entry(COL_EXECUTION_TIME, new java.sql.Timestamp(new Date().getTime()))
            );
        }else {
            entries = Map.ofEntries(
                    entry(COL_RESEND_TICKET_STATUS, messageStatus.name())
            );
        }

        dbRequest.updateResendTicketingEmailStatus(resendTicketingId, entries);
        log.info("Reminder email status updated successfully. ");
    }
}
