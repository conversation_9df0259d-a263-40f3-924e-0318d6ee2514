package com.accelevents.email.mail;

import com.accelevents.configuration.FreeMarkerConfiguration;
import com.accelevents.domain.TicketingOrder;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.TaskType;
import com.accelevents.dto.TicketHolderPDFDto;
import com.accelevents.dto.TicketHolderPDFEventDataDto;
import com.accelevents.dto.lambda.BulkEmailLambdaMessage;
import com.accelevents.dto.lambda.EmailDataDto;
import com.accelevents.dto.lambda.EmailUserWithTicketDto;
import com.accelevents.dto.lambda.EventTaskUserEmailDto;
import com.accelevents.email.db.DBRequest;
import com.accelevents.email.service.EnvValuesConfiguration;
import com.accelevents.email.service.LambdaS3Service;
import com.accelevents.email.service.QueryBuilder;
import com.accelevents.helpers.EmailHelper;
import com.accelevents.helpers.EmailLogoImageHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.messages.LoginLink;
import com.accelevents.service.TicketingPdfCreator;
import com.accelevents.utils.*;
import com.itextpdf.text.DocumentException;
import com.sendgrid.Attachments;
import freemarker.template.TemplateException;
import io.sentry.Sentry;
import net.glxn.qrgen.QRCode;
import net.glxn.qrgen.image.ImageType;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.accelevents.domain.enums.SessionTypeFormat.IN_PERSON;
import static com.accelevents.email.utils.BulkLambdaConstants.*;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.*;
import static com.accelevents.utils.SecurityUtils.encode;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isNoneBlank;

public class PrepareEventDataSendGridData {

    @Value("${uiBaseurl}")
    private String uiBaseurl;

    private static final Logger log = LoggerFactory.getLogger(PrepareEventDataSendGridData.class);
    private static final Pattern ENGAGE_MERGE_TAG_PATTERN = Pattern.compile(ENGAGE_MERRGE_TAG_PATTERN);

    private final DBRequest dbRequest;
    private final String AWS_S3_IMAGES_BUCKET;
    private final S3Client s3Client;
    private final String environment;
    private final String mailDefaultAPIKey;
    private final EnvValuesConfiguration envConfig;
    private final QueryBuilder queryBuilder;
    private final EmailLogoImageHelper emailLogoImageHelper;
    private final TicketingPdfCreator ticketingPdfCreator;
    private final String beefreeAPIKey;

    public PrepareEventDataSendGridData(String jdbcUrl, String roJdbcUrl, String userName, String password, String awsS3bucket, String environment, String mailDefaultAPIKey,String beefreeApiKey) throws SQLException {
        this.environment = environment;
        this.mailDefaultAPIKey = mailDefaultAPIKey;
        envConfig = new EnvValuesConfiguration();
        queryBuilder = new QueryBuilder();
        emailLogoImageHelper = new EmailLogoImageHelper(envConfig.getImagePreFix(), envConfig.getBlackLogo(), envConfig.getImagesAcceleventlogo(), envConfig.getDefaultAcceleventsLogo());
        dbRequest = new DBRequest(jdbcUrl, roJdbcUrl, userName, password);
        ticketingPdfCreator = TicketingPdfCreator.getInstance();
        this.beefreeAPIKey = beefreeApiKey;

        this.AWS_S3_IMAGES_BUCKET = awsS3bucket;

        Region region = Region.US_EAST_1;
        s3Client = S3Client.builder()
                .region(region)
                .credentialsProvider(DefaultCredentialsProvider.create())
                .build();
    }

    public void processEngageEmailUserDataAndSendEmail(BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage) throws Exception {

        try {
            EmailDataDto emailDataDto = bulkEmailLambdaMessage.getEmailDataDto();

            LambdaS3Service lambdaS3Service = new LambdaS3Service(s3Client);
            String messageBody = lambdaS3Service.getContentFromS3(this.AWS_S3_IMAGES_BUCKET, emailDataDto.getMessageBodyS3Key());
            log.debug("S3Messaged Fetched");

            SendGridMailLambdaServiceImpl sendGridMailLambdaService = new SendGridMailLambdaServiceImpl(emailDataDto, bulkEmailLambdaMessage.getEnumUnSubscribeLink(), environment, mailDefaultAPIKey);

            for (EmailUserWithTicketDto emailUserWithTicketDto : bulkEmailLambdaMessage.getReceiversData()) {

                if (isNoneBlank(emailUserWithTicketDto.getEmail())) {
                    String newMessageBodyWithUserData = this.replaceEngageEmailData(messageBody, emailUserWithTicketDto, emailDataDto);

                    if(!newMessageBodyWithUserData.contains(UNSUBSCRIBE_LINK)) {
                        newMessageBodyWithUserData += this.getUnSubscribeUrlForEngage(emailDataDto, emailUserWithTicketDto.getEmail());
                    }

                    Map<String, Object> substitutionMap = emailDataDto.getSubstitutionData();
                    substitutionMap.put(Constants.EVENT_ID_UPPERCASE, emailDataDto.getEventId());
                    substitutionMap.put(Constants.BASE_URL, EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()));
                    substitutionMap.put(UNSUBSCRIBE_LINK, this.getUnSubscribeUrl(emailDataDto, emailUserWithTicketDto.getEmail()));

                    // CC emails are already populated in DTO during data preparation
                    Set<String> ccEmails = emailUserWithTicketDto.getCcEmails() != null ?
                                          emailUserWithTicketDto.getCcEmails() : new HashSet<>();

                    try {
                        sendGridMailLambdaService.send(emailUserWithTicketDto.getEmail(), newMessageBodyWithUserData, null, ccEmails);
                        log.debug("Engage email sent successfully with {} CC emails to: {}", ccEmails.size(), emailUserWithTicketDto.getEmail());
                    } catch (Exception e) {
                        // If CC sending fails, try sending without CC to ensure primary email is delivered
                        log.warn("Failed to send engage email with CC to {}: {}. Attempting to send without CC.", emailUserWithTicketDto.getEmail(), e.getMessage());
                        try {
                            sendGridMailLambdaService.send(emailUserWithTicketDto.getEmail(), newMessageBodyWithUserData, null);
                            log.info("Engage email sent successfully without CC to: {}", emailUserWithTicketDto.getEmail());
                        } catch (Exception fallbackException) {
                            log.error("Failed to send engage email even without CC to {}: {}", emailUserWithTicketDto.getEmail(), fallbackException.getMessage());
                            throw fallbackException;
                        }
                    }
                }
            }
        } catch (Exception ex){
            log.error("Exception send email: ", ex);
            Sentry.setExtra("eventId", bulkEmailLambdaMessage.getEmailDataDto().getEventId().toString());
            Sentry.setExtra("bulkType", bulkEmailLambdaMessage.getBulkType());
            Sentry.captureException(ex);
        } finally {
            s3Client.close();
            dbRequest.close();
        }

    }

    public void processReminderEmailUserDataAndSendEmail(BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage) throws Exception {
        try {
            LambdaS3Service lambdaS3Service = new LambdaS3Service(s3Client);

            EmailDataDto emailDataDto = bulkEmailLambdaMessage.getEmailDataDto();
            String messageBody = null;
            if(StringUtils.isNotEmpty(emailDataDto.getMessageBodyS3Key())) {
                 messageBody= lambdaS3Service.getContentFromS3(this.AWS_S3_IMAGES_BUCKET, emailDataDto.getMessageBodyS3Key());
                 emailDataDto.setMessageBody(messageBody);
                log.debug("S3Messaged Fetched");
            }
            SendGridMailLambdaServiceImpl sendGridMailLambdaService = new SendGridMailLambdaServiceImpl(emailDataDto, bulkEmailLambdaMessage.getEnumUnSubscribeLink(), environment, mailDefaultAPIKey);
            Map<String, Object> substitutionMap = emailDataDto.getSubstitutionData();
            this.prepareReminderEmailEventSubstitutionData(substitutionMap, bulkEmailLambdaMessage.getEmailDataDto());

            for (EmailUserWithTicketDto emailUserWithTicketDto : bulkEmailLambdaMessage.getReceiversData()) {

                if (isNoneBlank(emailUserWithTicketDto.getEmail())) {
                    this.prepareReminderEmailHolderSubstitutionData(substitutionMap, emailDataDto, emailUserWithTicketDto, lambdaS3Service);

                    List<Attachments> attachmentsList = new ArrayList<>();

                    Attachments attachments = this.generateEventTicketPDF(emailDataDto, emailUserWithTicketDto,beefreeAPIKey);
                    attachmentsList.add(attachments);

                    // Reminder emails do NOT include CC functionality - send normally
                    sendGridMailLambdaService.send(emailUserWithTicketDto.getEmail(), messageBody, attachmentsList);
                    log.info("Reminder email sent successfully to: {}", emailUserWithTicketDto.getEmail());
                }
            }
        } catch (Exception ex){
            log.error("Exception send email: ", ex);
            Sentry.setExtra("eventId", bulkEmailLambdaMessage.getEmailDataDto().getEventId().toString());
            Sentry.setExtra("bulkType", bulkEmailLambdaMessage.getBulkType());
            Sentry.captureException(ex);
        } finally {
            s3Client.close();
            dbRequest.close();
        }

    }

    public void processEventTaskReminderEmailUserDataAndSendEmail(BulkEmailLambdaMessage<EmailUserWithTicketDto> bulkEmailLambdaMessage) throws Exception {

        try {
            EmailDataDto emailDataDto = bulkEmailLambdaMessage.getEmailDataDto();
            SendGridMailLambdaServiceImpl sendGridMailLambdaService = new SendGridMailLambdaServiceImpl(emailDataDto, bulkEmailLambdaMessage.getEnumUnSubscribeLink(), environment, mailDefaultAPIKey);
            Map<String, Object> substitutionMap = emailDataDto.getSubstitutionData();

            this.prepareReminderEmailEventSubstitutionData(substitutionMap, bulkEmailLambdaMessage.getEmailDataDto());
            for (EventTaskUserEmailDto eventTaskUserEmailDto : bulkEmailLambdaMessage.getReceiversData()) {
                if (isNoneBlank(eventTaskUserEmailDto.getEmail())) {
                    this.prepareEventTaskReminderEmailUserSubstitutionData(substitutionMap, emailDataDto, eventTaskUserEmailDto);

                    // Event task reminder emails do NOT include CC functionality - send normally
                    sendGridMailLambdaService.send(eventTaskUserEmailDto.getEmail(), STRING_EMPTY, null);
                    log.info("Event task reminder email sent successfully to: {}", eventTaskUserEmailDto.getEmail());
                }
            }
        } catch (Exception ex){
            log.error("Exception send email: ", ex);
            Sentry.setExtra("eventId", bulkEmailLambdaMessage.getEmailDataDto().getEventId().toString());
            Sentry.setExtra("bulkType", bulkEmailLambdaMessage.getBulkType());
            Sentry.captureException(ex);
        } finally {
            s3Client.close();
            dbRequest.close();
        }

    }

    private void prepareReminderEmailEventSubstitutionData(Map<String, Object> substitutionMap, EmailDataDto emailDataDto){

        substitutionMap.put(IMAGE_LOWER, emailDataDto.getHeaderImgFullURL());
        substitutionMap.put(IMAGE_FOOTER,emailDataDto.getFooterImgFullURL());

        substitutionMap.put(CLOUDINARY_IMAGE_PREFIX, envConfig.getCloudinaryURLWithImagePrefix());
        substitutionMap.put(ConstantEmailDynamic.EVENT_ID, emailDataDto.getEventId());
        substitutionMap.put(ConstantEmailDynamic.EVENT_NAME, emailDataDto.getEventName());
        substitutionMap.put(ConstantEmailDynamic.BASE_URL, EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()));
        substitutionMap.put(IS_EVENT_DATE_HIDE, emailDataDto.isHideEventDates());
        substitutionMap.put(ConstantEmailDynamic.CUSTOM_TEXT,Constants.STRING_EMPTY); //Reminder email resend_ticket_order_text is used as ${headerText} in default template and not required dynamic template
        substitutionMap.put(ConstantEmailDynamic.TOTAL_TICKETS, emailDataDto.getTotalTicketCount());
        substitutionMap.put(ConstantEmailDynamic.EVENT_CURRENCY,emailDataDto.getEventCurrencySymbol());
        substitutionMap.put(ConstantEmailDynamic.DONATION_AMOUNT, emailDataDto.getDonationAmount());
        substitutionMap.put(IS_VIEW_EVENT_BUTTON_ENABLE,emailDataDto.isShowEnterEventButtonInReminderTemplate());

        Date eventStartDateTz = TimeZoneUtil.getDateInLocal(emailDataDto.getEventStartDate(), emailDataDto.getEventTimeZone());
        Date eventEndDateTz = TimeZoneUtil.getDateInLocal(emailDataDto.getEventEndDate(), emailDataDto.getEventTimeZone());

        substitutionMap.put(ConstantEmailDynamic.EVENT_START_DATE, StringTools.formatCalanderDate(eventStartDateTz, true, true));
        substitutionMap.put(ConstantEmailDynamic.EVENT_END_DATE, StringTools.formatCalanderDate(eventEndDateTz, true, true));

        substitutionMap.put(ConstantEmailDynamic.EVENT_START_TIME, StringTools.formatCalanderTime(eventStartDateTz, false));
        substitutionMap.put(ConstantEmailDynamic.EVENT_END_TIME, StringTools.formatCalanderTime(eventEndDateTz, false));

        String timezone = java.util.TimeZone.getTimeZone(emailDataDto.getEventTimeZone()).getDisplayName(false, java.util.TimeZone.SHORT);
        substitutionMap.put(ConstantEmailDynamic.EVENT_TIME_ZONE, timezone);

        String eventStartEndDateTime;
        if(eventStartDateTz.compareTo(eventEndDateTz) == 0){
            eventStartEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true).concat(" at ")
                    .concat(StringTools.formatCalanderTime(eventStartDateTz, false)).concat( "-")
                    .concat(StringTools.formatCalanderTime(eventEndDateTz, false));
        }else {
            eventStartEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true).concat( " at ")
                    .concat(StringTools.formatCalanderTime(eventStartDateTz, false)).concat( "-")
                    .concat(StringTools.formatCalanderDate(eventEndDateTz, true, true)).concat(" at ")
                    .concat(StringTools.formatCalanderTime(eventEndDateTz, false));
        }

        substitutionMap.put(ConstantEmailDynamic.EVENT_START_END_DATETIME,emailDataDto.isHideEventDates() ? "" : (eventStartEndDateTime+" "+emailDataDto.getEventTimeZone()));

        /*if(!emailDataDto.isRecurringEvent() && emailDataDto.getRecurringEventId() > 0){
        }*/


        String startDate = TimeZoneUtil.getFormattedDate(emailDataDto.getEventStartDate(),DATE_FORMAT_YYYYMMDD_T_HHMMSS_Z);
        String endDate = TimeZoneUtil.getFormattedDate(emailDataDto.getEventEndDate(),DATE_FORMAT_YYYYMMDD_T_HHMMSS_Z);
        String startDateForOutlook =TimeZoneUtil.getFormattedDate(emailDataDto.getEventStartDate(),UTC_DATE_FORMAT);
        String endDateForOutlook = TimeZoneUtil.getFormattedDate(emailDataDto.getEventEndDate(),UTC_DATE_FORMAT);
        String eventAddressForCal = StringTools.getValueOrDefault(emailDataDto.getEventAddress(), "Event address is not available");
        String seatsForCal = emailDataDto.getEventCalendarInvite() != null ? emailDataDto.getEventCalendarInvite() : this.getCalenderInvitation(emailDataDto.getWlHostBaseURL(),emailDataDto.getEventURL());


        substitutionMap.put(ConstantEmailDynamic.ADD_TO_CALENDAR, emailDataDto.isHideEventDates() ? "" : EmailUtils.addToCalendar(emailDataDto.getEventName(), startDate, endDate, seatsForCal, eventAddressForCal, true));
        substitutionMap.put(ConstantEmailDynamic.ADD_TO_GOOGLE_CALENDAR, EmailUtils.addToGoogleCalendar(emailDataDto.getEventName(), startDate, endDate, seatsForCal, eventAddressForCal));
        substitutionMap.put(ConstantEmailDynamic.ADD_TO_YAHOO_CALENDAR, EmailUtils.addToYahooCalendar(emailDataDto.getEventName(), startDate, endDate, seatsForCal, eventAddressForCal));
        substitutionMap.put(ConstantEmailDynamic.ADD_TO_OUTLOOK_CALENDAR, EmailUtils.addToOutlookCalendar(emailDataDto.getEventName(), startDateForOutlook, endDateForOutlook, seatsForCal, eventAddressForCal));
        substitutionMap.put(ConstantEmailDynamic.ADD_TO_ICAL_CALENDAR, EmailUtils.generateIcalDownloadUrl(EmailUtils.getAPIUrlByEnv(environment), emailDataDto.getEventURL(),emailDataDto.getEventId()));
        substitutionMap.put(ConstantEmailDynamic.IS_SHOW_EVENT_DATE, !emailDataDto.isHideEventDates());


        if (EventFormat.IN_PERSON.name().equals(emailDataDto.getEventFormat()) || EventFormat.HYBRID.name().equals(emailDataDto.getEventFormat())) {
            substitutionMap.put(ConstantEmailDynamic.EVENT_LOCATION, StringUtils.isBlank(emailDataDto.getEventAddress()) ? TO_BE_ANNOUNCED : Arrays.stream(emailDataDto.getEventAddress().split(",")).findFirst().get());
            substitutionMap.put(ConstantEmailDynamic.EVENT_ADDRESS, StringTools.getValueOrDefault(emailDataDto.getEventAddress(), TO_BE_ANNOUNCED));

            substitutionMap.put(JOINORVIEWEVENTLABEL, Constants.VIEW_EVENT_DETAILS_LABEL);
        } else {
            substitutionMap.put(ConstantEmailDynamic.EVENT_LOCATION, EnumEventVenue.ONLINE_VIRTUAL_EVENT.getValue());
            substitutionMap.put(ConstantEmailDynamic.EVENT_ADDRESS, EnumEventVenue.ONLINE_VIRTUAL_EVENT.getValue());

            substitutionMap.put(JOINORVIEWEVENTLABEL,eventStartDateTz.after(TimeZoneUtil.getDateInLocal(new Date(),emailDataDto.getEventTimeZone())) ? Constants.FUTURE_EVENT_LABEL:Constants.JOIN_THE_EVENT_LABEL);
        }

        if(EventFormat.IN_PERSON.name().equals(emailDataDto.getEventFormat())){
            substitutionMap.put(ConstantEmailDynamic.JOIN_EVENT_OR_VIEW_ORDER_BUTTON,"View your order");
            substitutionMap.put(IS_VIRTUAL_EVENT,false);
        }else {
            substitutionMap.put(ConstantEmailDynamic.JOIN_EVENT_OR_VIEW_ORDER_BUTTON,"Join the Event");
            substitutionMap.put(IS_VIRTUAL_EVENT,true);
        }

        if (StringUtils.isNotEmpty(emailDataDto.getLatitude()) && StringUtils.isNotBlank(emailDataDto.getLongitude())){
            substitutionMap.put(ConstantEmailDynamic.VIEW_ON_MAP, GOOGLE_MAP_URL.concat(emailDataDto.getLatitude()).concat(",").concat(emailDataDto.getLongitude()));
        }else {
            substitutionMap.put(ConstantEmailDynamic.VIEW_ON_MAP, STRING_DASH);
        }

        substitutionMap.put("isFacebookUrl", emailDataDto.getWhiteLabelId() <= 0 || StringUtils.isNotEmpty(emailDataDto.getFacebookShareURL()));
        substitutionMap.put("isTwitterUrl",emailDataDto.getWhiteLabelId() <= 0 || StringUtils.isNotEmpty(emailDataDto.getTwitterShareURL()));
        substitutionMap.put("isLinkedinUrl",emailDataDto.getWhiteLabelId() <= 0 || StringUtils.isNotEmpty(emailDataDto.getLinkedinShareURL()));
        substitutionMap.put("isInstagramUrl",emailDataDto.getWhiteLabelId() <= 0 || StringUtils.isNotEmpty(emailDataDto.getInstagramShareURL()));
        substitutionMap.put("isHelpCenter",emailDataDto.getWhiteLabelId() <= 0 || StringUtils.isNotEmpty(emailDataDto.getHelpCenter()));
        substitutionMap.put("isPrivacyPolicy",emailDataDto.getWhiteLabelId() <= 0 || StringUtils.isNotEmpty(emailDataDto.getPrivacyPolicy()));

        substitutionMap.put(ConstantEmailDynamic.FACEBOOK_SHARE, StringTools.getValueOrDefault(emailDataDto.getFacebookShareURL(), emailDataDto.getWhiteLabelId() > 0 ? STRING_BLANK : envConfig.getDefaultFacebookShare()));
        substitutionMap.put(ConstantEmailDynamic.TWITTER_SHARE, StringTools.getValueOrDefault(emailDataDto.getTwitterShareURL(), emailDataDto.getWhiteLabelId() > 0 ? STRING_BLANK : envConfig.getDefaultTwitterShare()));
        substitutionMap.put(ConstantEmailDynamic.LINKEDIN_SHARE, StringTools.getValueOrDefault(emailDataDto.getLinkedinShareURL(), emailDataDto.getWhiteLabelId() > 0 ? STRING_BLANK : envConfig.getDefaultLinkedInShare()));
        substitutionMap.put(ConstantEmailDynamic.INSTAGRAM_SHARE, StringTools.getValueOrDefault(emailDataDto.getInstagramShareURL(), emailDataDto.getWhiteLabelId() > 0 ? STRING_BLANK : envConfig.getDefaultInstagramShare()));
        substitutionMap.put(ConstantEmailDynamic.GET_STARTED, StringTools.getValueOrDefault(emailDataDto.getGetStarted(), emailDataDto.getWhiteLabelId() > 0 ? STRING_BLANK : envConfig.getDefaultGetStarted()));
        substitutionMap.put(ConstantEmailDynamic.HELP_CENTER, StringTools.getValueOrDefault(emailDataDto.getHelpCenter(), emailDataDto.getWhiteLabelId() > 0 ? STRING_BLANK : envConfig.getHelpCenter()));
        substitutionMap.put(ConstantEmailDynamic.PRIVACY_POLICY, StringTools.getValueOrDefault(emailDataDto.getPrivacyPolicy(), emailDataDto.getWhiteLabelId() > 0 ? STRING_BLANK : envConfig.getPrivacyPolicy()));

        substitutionMap.put(ConstantEmailDynamic.SENDER_NAME, StringTools.getValueOrDefault(emailDataDto.getSenderName(), ACCELEVENTS));
        substitutionMap.put(ConstantEmailDynamic.CREATE_EVENT,EmailUtils.getUiBaseURLByEnv(environment).concat("/events"));

        substitutionMap.put(ConstantEmailDynamic.ADMIN_TEXT, "");//it is set because while send custome email set to blank and in default we set in headerText

        substitutionMap.put(ConstantEmailDynamic.ORGANIZER_NAME,StringTools.getValueOrDefault(emailDataDto.getOrgName(), "Anonymous"));
        substitutionMap.put(ConstantEmailDynamic.ORGANIZER_EMAIL,StringTools.getValueOrDefault(emailDataDto.getOrgContactEmail(),
                StringTools.getValueOrDefault(emailDataDto.getReplyToEmail(), STRING_DASH))
        );

        String openContactFormUrl = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()).concat(getEventPath()).concat(emailDataDto.getEventURL()).concat( "?openContactForm=true");
        if(StringUtils.isNotBlank(emailDataDto.getOrgPageURL())) {
            openContactFormUrl = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()).concat("/o/")
                    .concat(emailDataDto.getOrgPageURL()).concat("/").concat(emailDataDto.getEventURL()).concat( "?openContactForm=true");
        }
        substitutionMap.put(ConstantEmailDynamic.OPEN_CONTACT_FORM_URL, openContactFormUrl);


    }

    private void prepareReminderEmailHolderSubstitutionData(Map<String, Object> substitutionMap, EmailDataDto emailDataDto, EmailUserWithTicketDto emailUserWithTicketDto, LambdaS3Service lambdaS3Service) throws SQLException {

        substitutionMap.put(PURCHASER_NAME, emailUserWithTicketDto.getPurchaserFirstName());
        substitutionMap.put(PURCHASER_LAST_NAME, emailUserWithTicketDto.getPurchaserLastName());
        substitutionMap.put(PURCHASER_EMAIL, (StringUtils.isNoneBlank(emailUserWithTicketDto.getPurchaserEmail())) ? emailUserWithTicketDto.getPurchaserEmail(): STRING_EMPTY);
        substitutionMap.put(TICKET_HOLDER_FIRST_NAME, StringTools.getStringEllipsis(emailUserWithTicketDto.getFirstName()));
        substitutionMap.put(ORDER_NUMBER, emailUserWithTicketDto.getOrderNumber());
        substitutionMap.put(ConstantEmailDynamic.TICKET_TYPE_NAME, emailUserWithTicketDto.getTicketTypeName());

        String eventFullUrl = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()).concat(getEventPath()).concat(emailDataDto.getEventURL());
        substitutionMap.put(ConstantEmailDynamic.EVENT_URL,eventFullUrl);

        String token = getOrCreateEventLevelMagicLinkToken(emailDataDto.getEventId(), emailUserWithTicketDto.getUserId(), AutoLoginCreationLocation.REGISTRATION_REMINDER_EMAIL);
        String eventUrlWithTokenizedLink = eventFullUrl.concat("?userKey=" ).concat(emailUserWithTicketDto.getUserId() != null ? EmailUtils.getEventUrlWithTokenizedLink(token) : "").concat(AND_EVENT_ID_EQUALS).concat(String.valueOf(emailDataDto.getEventId()));

        String magicLink = eventFullUrl + QUE_MARK_USER_KEY_EQUALS + EmailUtils.getEventUrlWithTokenizedLink(token) + AND_EVENT_ID_EQUALS + emailDataDto.getEventId() + AMPERSAND + EmailUtils.getUtmUrl(STRING_EMPTY, true).concat("&isPasswordLess=true");
        substitutionMap.put(ConstantEmailDynamic.MAGIC_LINK, magicLink);
        substitutionMap.put(ConstantEmailDynamic.EVENT_UC_URL, magicLink);
        substitutionMap.put(TOKEN_LOWER, magicLink);

        String headerText = STRING_EMPTY;
        if(StringUtils.isNotEmpty(emailDataDto.getResendTicketOrderText())) {
            headerText = EmailHelper.replaceDynamicKeyWithValueInStr(emailDataDto.getResendTicketOrderText(), "ticket_holder_first_name", StringTools.getValueOrDefault(emailUserWithTicketDto.getFirstName(), STRING_DASH));
            headerText = EmailHelper.replaceDynamicKeyWithValueInStr(headerText, "eventURL", eventUrlWithTokenizedLink);
            headerText = headerText.replace("http://magicLink", eventUrlWithTokenizedLink);

            //Validate the Enter Event functionality and it's actual usage.
/*        if(emailDataDto.isShowEnterEventButtonInReminderTemplate()){
            headerText = addMagicLink(headerText, eventUrlWithTokenizedLink);
        }*/

            Date eventStartDateTz = TimeZoneUtil.getDateInLocal(emailDataDto.getEventStartDate(), emailDataDto.getEventTimeZone());
            Date eventEndDateTz = TimeZoneUtil.getDateInLocal(emailDataDto.getEventEndDate(), emailDataDto.getEventTimeZone());
            String eventStartEndDateTime;
            if (eventStartDateTz.compareTo(eventEndDateTz) == 0) {
                eventStartEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true).concat(" at ")
                        .concat(StringTools.formatCalanderTime(eventStartDateTz, false)).concat("-")
                        .concat(StringTools.formatCalanderTime(eventEndDateTz, false));
            } else {
                eventStartEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true).concat(" at ")
                        .concat(StringTools.formatCalanderTime(eventStartDateTz, false)).concat("-")
                        .concat(StringTools.formatCalanderDate(eventEndDateTz, true, true)).concat(" at ")
                        .concat(StringTools.formatCalanderTime(eventEndDateTz, false));
            }

            headerText = EmailHelper.replaceDynamicKeyWithValueInStr(headerText, ConstantEmailDynamic.EVENT_NAME, emailDataDto.getEventName());
            headerText = EmailHelper.replaceDynamicKeyWithValueInStr(headerText, ConstantEmailDynamic.EVENT_START_END_DATETIME, emailDataDto.isHideEventDates() ? "" : eventStartEndDateTime);
        }
        substitutionMap.put(ConstantEmailDynamic.HEADER_TEXT, EmailHelper.applyFroalaStyle(headerText));

        substitutionMap.put(PURCHASE_DATE,
                String.format("%s",
                        StringTools.formatDateBasedOnPattern(
                                TimeZoneUtil.getDateInLocal(emailUserWithTicketDto.getPurchaseDate(), emailDataDto.getEventTimeZone()),
                                "MMM d, yyyy hh:mm a")));

        String link;
        String linkForDownloadTicketsWithNewTemplate;
        if (emailUserWithTicketDto.isHolderEmail()) {
            link = EmailUtils.getAPIUrlByEnv(environment).concat( "/rest/u/myticket/download/pass/").concat(emailUserWithTicketDto.getTicketBarcodeId());
            linkForDownloadTicketsWithNewTemplate = " " + link;
        } else {
            link = EmailUtils.getAPIUrlByEnv(environment) + "/rest/u/myticket/download/pass/order/" + emailUserWithTicketDto.getOrderNumber();
            linkForDownloadTicketsWithNewTemplate = " " + link;
        }

        substitutionMap.put(ConstantEmailDynamic.LINK_FOR_DOWNLOAD_TICKETS_WITH_NEW_TEMPLATE, linkForDownloadTicketsWithNewTemplate);

        String eventUrlWithOrderId = STRING_EMPTY;
        if(null != emailUserWithTicketDto.getOrderNumber() && emailUserWithTicketDto.getOrderNumber() > 0) {
            eventUrlWithOrderId = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()) + getOrderUrl() + emailUserWithTicketDto.getOrderNumber();
        }

        substitutionMap.put(ConstantEmailDynamic.ORDER_PDF_LINK, eventUrlWithOrderId);

        substitutionMap.put(UNSUBSCRIBE_LINK,getUnSubscribeUrl(emailDataDto,emailUserWithTicketDto.getEmail()));

        if(emailDataDto.isAdvanceEmailBuilder() && emailDataDto.getMessageBody() != null && emailDataDto.getMessageBody().contains(QR_CODE)){
            substitutionMap.put(QR_CODE, this.createAndUploadQRCodeImage(emailDataDto.getEventId(), emailUserWithTicketDto.getOrderNumber(), emailUserWithTicketDto.getTicketBarcodeId(), lambdaS3Service));
        }
        if (emailDataDto.isAdvanceEmailBuilder() && emailUserWithTicketDto.getBuyerAttributesValuesMap()!=null && !emailUserWithTicketDto.getBuyerAttributesValuesMap().isEmpty()) {
            substitutionMap.putAll(emailUserWithTicketDto.getBuyerAttributesValuesMap());
        }
        if (emailDataDto.isAdvanceEmailBuilder() && emailUserWithTicketDto.getHolderAttributesValuesMap()!=null && !emailUserWithTicketDto.getHolderAttributesValuesMap().isEmpty()) {
            substitutionMap.putAll(emailUserWithTicketDto.getHolderAttributesValuesMap());
        }
    }


    private String getOrCreateEventLevelMagicLinkToken(Long eventId, Long userId, String creationLocation) throws SQLException {
        log.info("getOrCreateEventLevelMagicLinkToken from lambda for eventId {} userId {} creationLocation {}", eventId, userId, creationLocation);
        if (userId == null || eventId == null) {
            return STRING_EMPTY;
        }
        String token = null;
        ResultSet resultSetAutoLogin = dbRequest.getQueryResult(queryBuilder.createAutoLoginQuery(userId, eventId).toString());

        if (resultSetAutoLogin.next()) {
            token = resultSetAutoLogin.getString(COL_TOKEN);
        }
        if (token == null || token.isEmpty()) {
            token = UUID.randomUUID().toString();
            dbRequest.saveAutoLogin(eventId, userId, LoginLink.MAGIC_LINK.name(), DateUtils.getAddedHours(new Date(), 0), token, LINK_TYPE_EVENT_LEVEL);
        }
        return token;
    }
    private String createAndUploadQRCodeImage(Long eventId, Long ticketOrderNumber, String barcode, LambdaS3Service lambdaS3Service) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        QRCode.from(barcode).to(ImageType.PNG).writeTo(byteArrayOutputStream);
        byte[] imageData = byteArrayOutputStream.toByteArray();
        // Upload barcode image to S3
        String uploadKey = QR_CODES_IMAGES + STRING_SLASH + eventId + STRING_SLASH + ticketOrderNumber + STRING_SLASH + barcode + STRING_DOT + PNG;
        lambdaS3Service.uploadInBucket(imageData, uploadKey, AWS_S3_IMAGES_BUCKET);
        String imageUrl = envConfig.getImagePreFix() + uploadKey;

        return "<div class=\"tac padding3\"><img src=\"" + imageUrl + "\" width=\"200\" height=\"200\" /></div>";
    }

    private String replaceEngageEmailData(String messageBody, EmailUserWithTicketDto emailUserWithTicketDto, EmailDataDto emailDataDto) throws SQLException {
        String eventUrl = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()) + getEventPath() + emailDataDto.getEventURL();
        // Handle both $FirstName and ${FirstName} formats
        messageBody = replace(messageBody, "$FirstName", emailUserWithTicketDto.getFirstName());
        messageBody = replace(messageBody, "${FirstName}", emailUserWithTicketDto.getFirstName());
        messageBody = replace(messageBody,"$LastName", emailUserWithTicketDto.getLastName());
        messageBody = replace(messageBody,"${LastName}", emailUserWithTicketDto.getLastName());
        messageBody = replace(messageBody,"$Email", emailUserWithTicketDto.getEmail());
        messageBody = replace(messageBody,"${Email}", emailUserWithTicketDto.getEmail());
        messageBody = replace(messageBody,"${Phone}", emailUserWithTicketDto.getPhoneNumber());

        messageBody = replace(messageBody,"$EventUrl", eventUrl);
        messageBody = replace(messageBody,"${EventUrl}", eventUrl);
        messageBody = replace(messageBody,"$EventName", emailDataDto.getEventName());
        messageBody = replace(messageBody,"${EventName}", emailDataDto.getEventName());
        boolean isMagicLinkNeedsToAdd = messageBody.contains("${MagicLink}");
        boolean isRegistrationPageLinkNeedsToAdd = messageBody.contains("${RegistrationPageForHolder}");
        String baseUrl = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL());

        ResultSet usersRs = dbRequest.getQueryResult(queryBuilder.getUserDataFromEmailQuery(emailUserWithTicketDto.getEmail()).toString());
        log.debug("isMagicLink {}",isMagicLinkNeedsToAdd);
        if (usersRs.next()){
            if (isMagicLinkNeedsToAdd) {
                Long userId = usersRs.getLong(COL_USER_ID);
                String token = getOrCreateEventLevelMagicLinkToken(emailDataDto.getEventId(), userId, AutoLoginCreationLocation.ENGAGE_REMINDER_EMAIL);
                String eventMagicLink = baseUrl.concat(getEventPath()).concat(emailDataDto.getEventURL());

                ResultSet eventAccessRS = dbRequest.getQueryResult(queryBuilder.createEventAccessStartedQuery(emailDataDto.getEventId()).toString());
                boolean isEventAccess = false;
                while (eventAccessRS.next()) {
                    isEventAccess = eventAccessRS.getBoolean(COL_ACCESS_STARTED);
                }
                if ((isEventAccess && !IN_PERSON.name().equals(emailDataDto.getEventFormat()))) {
                    eventMagicLink += "/portal";
                }
                String url = eventMagicLink + QUE_MARK_USER_KEY_EQUALS + EmailUtils.getEventUrlWithTokenizedLink(token) + AND_EVENT_ID_EQUALS + emailDataDto.getEventId() + AMPERSAND + EmailUtils.getUtmUrl(STRING_EMPTY, false).concat("&isPasswordLess=true");
                url = url + AMPERSAND + EVENT_TICKET_NUMBER + STRING_EQUAL + emailUserWithTicketDto.getTicketNumber() + AMPERSAND + ORDER_NUMBER + STRING_EQUAL + emailUserWithTicketDto.getOrderNumber();
                log.info("Engage event access magicLink {}", url);

                messageBody = replace(messageBody, "href=\"$MagicLink\"", "href=\"" + url + "\"");
                messageBody = replace(messageBody, "$MagicLink", "<a href=\"" + url + "\">Link</a>");
                messageBody = replace(messageBody, "href=\"${MagicLink}\"", "href=\"" + url + "\"");
                messageBody = replace(messageBody, "${MagicLink}", "<a href=\"" + url + "\">Link</a>");
            }

            if (isRegistrationPageLinkNeedsToAdd) {
                Long userId = usersRs.getLong(COL_USER_ID);
                String token = getAndCreatePlateFormLevelMagicLinkToken(emailDataDto.getEventId(), userId, AutoLoginCreationLocation.ENGAGE_REMINDER_EMAIL);

                String redirectUrl = baseUrl.concat(getMyProfileUrl());
                String platFormMagicLink = redirectUrl + QUE_MARK_USER_KEY_EQUALS + EmailUtils.getEventUrlWithTokenizedLink(token) +
                        QUE_IS_FROM_PLATFORM_EQUALS.concat("true") + AND_PASSWORD_LESS_EQUALS.concat("true") + "&redirectUrl=" + redirectUrl;
                log.info("Engage event access platFormMagicLink {}", platFormMagicLink);

                messageBody = replace(messageBody,"href=\"$RegistrationPageForHolder\"", "href=\""+platFormMagicLink+"\"");
                messageBody = replace(messageBody,"$RegistrationPageForHolder", "<a href=\""+platFormMagicLink+"\">Registration Page Link</a>");
                messageBody = replace(messageBody,"href=\"${RegistrationPageForHolder}\"", "href=\""+platFormMagicLink+"\"");
                messageBody = replace(messageBody,"${RegistrationPageForHolder}", "<a href=\""+platFormMagicLink+"\">Registration Page Link</a>");

            }
        }

            messageBody = replace(messageBody,"$OrderNumber",
                    emailUserWithTicketDto.getOrderNumberStr());
            messageBody = messageBody.replace("${OrderNumber}", emailUserWithTicketDto.getOrderNumberStr());
            messageBody = replace(messageBody,"$TicketNumber",
                    emailUserWithTicketDto.getTicketNumberStr());
            messageBody = replace(messageBody,"${TicketNumber}", emailUserWithTicketDto.getTicketNumberStr());
            messageBody = replace(messageBody,"$AutoAssignedSequenceNumber",
                    emailUserWithTicketDto.getAutoAssignedSeqNumberStr());
            messageBody = replace(messageBody,"${AutoAssignedSequenceNumber}", emailUserWithTicketDto.getAutoAssignedSeqNumberStr());
            messageBody = replace(messageBody,"$TicketTypeName",
                StringTools.getValueOrDefault(emailUserWithTicketDto.getTicketTypeName(), "N/A"));
            messageBody = replace(messageBody,"${TicketTypeName}", StringTools.getValueOrDefault(emailUserWithTicketDto.getTicketTypeName(), "N/A"));

        // Add PaymentLink merge tag only for unpaid tickets that actually require payment and not test email
        if (messageBody.contains("$PaymentLink") || messageBody.contains("${PaymentLink}")) {
            String paymentLink = STRING_EMPTY;
            if (requiresPayment(emailUserWithTicketDto) && !isTestEmail(emailUserWithTicketDto)) {
                paymentLink = buildPaymentLink(emailDataDto, emailUserWithTicketDto);
            }

            if (StringUtils.isNotEmpty(paymentLink)) {
                // First replace href attributes with just the URL
                messageBody = replace(messageBody, "href=\"$PaymentLink\"", "href=\"" + paymentLink + "\"");
                // Then replace any remaining $PaymentLink with clickable HTML anchor tag
                messageBody = replace(messageBody, "$PaymentLink", "<a href=\"" + paymentLink + "\">" + paymentLink + "</a>");
                messageBody = replace(messageBody, "href=\"${PaymentLink}\"", "href=\"" + paymentLink + "\"");
                messageBody = replace(messageBody, "${PaymentLink}", "<a href=\"" + paymentLink + "\">" + paymentLink + "</a>");
            }else {
                // Remove $PaymentLink placeholder when payment link is empty
                messageBody = replace(messageBody, "$PaymentLink", "");
                messageBody = replace(messageBody, "${PaymentLink}", EMPTY);
            }
        }

        // Collect all existing keys from various attribute sources
        Set<String> allExistingKeys = new HashSet<>();

        // Add contact custom attribute keys
        allExistingKeys.addAll(getContactCustomAttributeKeys(emailUserWithTicketDto));

        // Add buyer attribute keys
        if (emailUserWithTicketDto.getBuyerAttributesValuesMap() != null) {
            for (String key : emailUserWithTicketDto.getBuyerAttributesValuesMap().keySet()) {
                allExistingKeys.add(Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + key + Constants.CLOSE_CURLY_BRACE);
            }
        }

        // Add holder attribute keys
        if (emailUserWithTicketDto.getHolderAttributesValuesMap() != null) {
            for (String key : emailUserWithTicketDto.getHolderAttributesValuesMap().keySet()) {
                allExistingKeys.add(Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + key + Constants.CLOSE_CURLY_BRACE);
            }
        }

        // Replace contact custom attributes if available
        messageBody = replaceContactCustomAttributes(messageBody, emailUserWithTicketDto);

        //Replace buyer/seller attributes if available
        messageBody = replaceBuyerSellerCustomAttributes(messageBody, emailUserWithTicketDto);

        // Replace any unmatched merge tags after all merge tag processing is complete
        messageBody = replaceUnmatchedMergeTags(messageBody, allExistingKeys, isTestEmail(emailUserWithTicketDto));

        return messageBody;
    }

    private String replaceBuyerSellerCustomAttributes(String messageBody, EmailUserWithTicketDto emailUserWithTicketDto) {
        // Check if this is a test email
        boolean isTestMail = isTestEmail(emailUserWithTicketDto);

        // Replace buyer attributes if available
        if (emailUserWithTicketDto.getBuyerAttributesValuesMap() != null && !emailUserWithTicketDto.getBuyerAttributesValuesMap().isEmpty()) {
            messageBody = replaceAttributeMergeTags(messageBody, emailUserWithTicketDto.getBuyerAttributesValuesMap(), isTestMail, PREFIX_BUYER);
        }

        // Replace holder attributes if available
        if (emailUserWithTicketDto.getHolderAttributesValuesMap() != null && !emailUserWithTicketDto.getHolderAttributesValuesMap().isEmpty()) {
            messageBody = replaceAttributeMergeTags(messageBody, emailUserWithTicketDto.getHolderAttributesValuesMap(), isTestMail, PREFIX_HOLDER);
        }

        return messageBody;
    }



    /**
     * Generic method to replace attribute merge tags in the message body with their corresponding values.
     * Handles merge tags in the format ${key} where the key exists in attributesValuesMap.
     * For test emails, keeps the merge tag name instead of replacing with actual values.
     *
     * @param messageBody the email message body that may contain attribute merge tags
     * @param attributesValuesMap map containing attribute keys and their values
     * @param isTestMail whether this is a test email
     * @param logPrefix prefix for log messages to identify the attribute type
     * @return the message body with attribute merge tags replaced with their values or merge tag names
     */
    private String replaceAttributeMergeTags(String messageBody, Map<String, Object> attributesValuesMap, boolean isTestMail, String logPrefix) {
        if (StringUtils.isEmpty(messageBody) || attributesValuesMap == null || attributesValuesMap.isEmpty()) {
            return messageBody;
        }

        // Iterate through all attribute keys and replace them in the message body
        for (Map.Entry<String, Object> entry : attributesValuesMap.entrySet()) {
            String key = entry.getKey();
            Object valueObj = entry.getValue();

            // Create merge tag in ${key} format
            String mergeTag = Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + key + Constants.CLOSE_CURLY_BRACE;

            // Check if the merge tag exists in the message body
            if (messageBody.contains(mergeTag)) {
                String replacementValue;

                if (isTestMail) {
                    replacementValue = mergeTag;
                } else {
                    // For regular emails, convert Object to String and replace with actual value
                    String value = valueObj != null ? valueObj.toString() : null;
                    String displayValue = formatAttributeValueForDisplay(value, TICKET_BUYER);
                    replacementValue = StringUtils.isNotEmpty(displayValue) ? displayValue : Constants.STRING_EMPTY;
                    log.debug("Replaced {} attribute merge tag '{}' with value '{}' in message body", logPrefix, mergeTag, replacementValue);
                }

                messageBody = replace(messageBody, mergeTag, replacementValue);
            }
        }

        return messageBody;
    }

    /**
     * Checks if this is a test email based on dummy order/ticket numbers
     * @param emailUserWithTicketDto the email user data
     * @return true if this is a test email, false otherwise
     */
    private boolean isTestEmail(EmailUserWithTicketDto emailUserWithTicketDto) {
        return DUMMY_ORDER_NUMBER.equals(emailUserWithTicketDto.getOrderNumber()) ||
                DUMMY_TICKET_NUMBER.equals(emailUserWithTicketDto.getTicketNumber());
    }

    /**
     * Checks if the order status requires payment (excludes complimentary and already paid tickets)
     * @param emailUserWithTicketDto the email user data containing order information
     * @return true if the status requires payment, false otherwise
     */
    private boolean requiresPayment(EmailUserWithTicketDto emailUserWithTicketDto) {
        String orderStatus = emailUserWithTicketDto.getOrderStatus();

        if (orderStatus == null) {
            return false;
        }

        return checkTicketOrderBalanceDue(orderStatus);
    }

    /**
     * Checks if the given order status has balance due and requires payment
     * @param orderStatus the order status to check
     * @return true if the status has balance due and requires payment, false otherwise
     */
    private boolean checkTicketOrderBalanceDue(String orderStatus) {
        String status = orderStatus.toUpperCase();
        // Only send payment links for statuses that actually require payment
        return TicketingOrder.TicketingOrderStatus.UNPAID.name().equalsIgnoreCase(status) ||
                TicketingOrder.TicketingOrderStatus.PAID_DELETE.name().equalsIgnoreCase(status) ||
                TicketingOrder.TicketingOrderStatus.UNPAID_DELETE.name().equalsIgnoreCase(status) ||
                TicketingOrder.TicketingOrderStatus.PARTIAL.name().equalsIgnoreCase(status) ||
                TicketingOrder.TicketingOrderStatus.EXPIRED.name().equalsIgnoreCase(status) ||
                TicketingOrder.TicketingOrderStatus.PAYMENT_FAILED.name().equalsIgnoreCase(status);
    }
    
    /**
     * Fetches the order status from database for a given order number
     * @param orderNumber the order number to look up
     * @return the order status string, or null if not found
     * @throws SQLException if database query fails
     */
    private String getOrderStatusFromDatabase(Long orderNumber) throws SQLException {
        String query = "SELECT order_status FROM ticketing_order WHERE id = " + orderNumber;
        ResultSet resultSet = dbRequest.getQueryResult(query);

        if (resultSet.next()) {
            return resultSet.getString("order_status");
        }

        return null;
    }

    private String getUnSubscribeUrl(EmailDataDto emailDataDto, String email) {
        return EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL())
                + "/u/unsubscribe?key="
                + encode(
                Constants.EVENT_ID_UPPERCASE + "="+emailDataDto.getEventId() +"&"+
                        Constants.EMAIL + "=" + email);

    }

    //TODO: Once the unsubscribe text is added into the Engage email template please remove this temp implementation
    @Deprecated
    private String getUnSubscribeUrlForEngage(EmailDataDto emailDataDto, String email) {
        String unSubscribeUrl = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL())
                + "/u/unsubscribe?key="
                + encode(
                Constants.EVENT_ID_UPPERCASE + "="+emailDataDto.getEventId() +"&"+
                        Constants.EMAIL + "=" + email);

        return StringUtils.replace(Constants.UNSUBSCRIBE_CONTACT_TEXT,"[unsubscribe_url]",unSubscribeUrl);
    }

    private Attachments generateEventTicketPDF(EmailDataDto emailDataDto, EmailUserWithTicketDto emailUserWithTicketDto,String beefreeAPIKey) throws IOException, TemplateException, DocumentException {
        Attachments attachments = null;

        String ticketPdf = StringTools.getValueOrDefault(emailDataDto.getTicketPdfDesign(),
                FreeMarkerConfiguration.getInstance().getTemplate(TemplateId.TICKET_ORDER_PDF_ORIGINAL.getValue()).toString());

        log.info("Before Attachment Processing for ticket order: {}", emailUserWithTicketDto.getOrderNumber());

        if(emailUserWithTicketDto.isAllowPDFDownload()) {

            TicketHolderPDFDto ticketHolderPDFDto = new TicketHolderPDFDto(emailUserWithTicketDto.getPurchaserFirstName(),
                    emailUserWithTicketDto.getOrderNumber(),
                    emailUserWithTicketDto.getPurchaseDate(),
                    emailUserWithTicketDto.getFirstName() +" "+ emailUserWithTicketDto.getLastName(),
                    emailUserWithTicketDto.getTicketTypeName(),
                    emailUserWithTicketDto.isAllowPDFDownload(),
                    emailUserWithTicketDto.getTicketNumber(),
                    emailUserWithTicketDto.getPrice(),
                    emailUserWithTicketDto.getTicketBarcodeId(),
                    emailUserWithTicketDto.getTicketTypeDesc(),
                    emailUserWithTicketDto.getAutoAssignedSeqNumberStr(),
                    emailUserWithTicketDto.getSeatNumber(),
                    emailUserWithTicketDto.getTicketCode(),
                    emailUserWithTicketDto.getTicketPdfDesign()
                    );

            TicketHolderPDFEventDataDto ticketHolderPDFEventDataDto = new TicketHolderPDFEventDataDto(false, emailDataDto.getEventAddress(),emailDataDto.getEventId(),emailDataDto.getEventName(),
                    emailDataDto.getEventFormat(), emailDataDto.getEventTimeZone(),emailDataDto.getTimeZoneId(), emailDataDto.getEventCurrencySymbol(),
                    emailDataDto.getTicketChartKey(), emailDataDto.getEventStartDate(), emailDataDto.getEventEndDate(), emailUserWithTicketDto.getOrderStatus(),
                    ticketPdf, StringUtils.isNoneBlank((emailDataDto.getTicketPdfDesign())),false,
                    emailDataDto.isHideEventDates(), emailLogoImageHelper.getEventOrWhiteLabelLogoLocation(emailDataDto.getEventLogoImg(), emailDataDto.getWlLogoImg()),
                    envConfig.getImagePreFix() + envConfig.getDefaultAcceleventsLogo(),
                    emailDataDto.getWlHeaderLogoImg(),
                    emailDataDto.getWhiteLabelURL(), emailDataDto.getWhiteLabelFirmName(),emailDataDto.getEventLogoImg());
                    ticketHolderPDFEventDataDto.setBeeFreeAPIToken(beefreeAPIKey);
            attachments = ticketingPdfCreator.generatePDF(ticketHolderPDFEventDataDto, List.of(ticketHolderPDFDto));
        }

        log.info("Attachment is ready  for ticket order: {}", emailUserWithTicketDto.getOrderNumber());

        return attachments;
    }

    private void prepareEventTaskReminderEmailUserSubstitutionData(Map<String, Object> substitutionMap, EmailDataDto emailDataDto, EventTaskUserEmailDto eventTaskUserEmailDto) throws SQLException {

        substitutionMap.put(FIRST_OF_NAME, eventTaskUserEmailDto.getFirstName());

        String eventFullUrl = EmailUtils.getEventBaseUrl(environment, emailDataDto.getWlHostBaseURL()).concat(getEventPath()).concat(emailDataDto.getEventURL());
        String token = getOrCreateEventLevelMagicLinkToken(emailDataDto.getEventId(), eventTaskUserEmailDto.getUserId(), AutoLoginCreationLocation.EVENT_TASK_REMINDER_EMAIL);
        String portalMagicLink = eventFullUrl + (TaskType.EXHIBITOR.name().equals(emailDataDto.getTaskType()) ? PORTAL_EXHIBITOR_TASK_LINK : PORTAL_MY_TALK_TASK_LINK) + QUE_MARK_USER_KEY_EQUALS + EmailUtils.getEventUrlWithTokenizedLink(token) + AND_EVENT_ID_EQUALS + emailDataDto.getEventId() + AND_ATTENDEE_MAIL_EQUALS + eventTaskUserEmailDto.getEmail() + AND_PASSWORD_LESS_EQUALS.concat(String.valueOf(true))  ;

        String headerText = "You have incomplete tasks awaiting your attention.";
        String taskOverDuePostFixMsg = "Please complete it before the due date.";
        String speakerTaskMsg = "This is a reminder to complete the task \"${task_name}\" for the event \"${event_name}\".";

        String mailSubject = "Reminder: \"${task_name}\" Task Pending for ${event_name}.";
        if(TaskType.SESSION.name().equals(emailDataDto.getTaskType())) {
            headerText = "Your speaking session have incomplete tasks!";
            speakerTaskMsg = "This is a reminder to complete the task \"${task_name}\" for your speaking session \"${session_name}\" at the event \"${event_name}\".";
            speakerTaskMsg = EmailHelper.replaceKeyValueFromStrAndAddColorWithBoldStyle(speakerTaskMsg, TASK_NAME, emailDataDto.getTaskName());
            speakerTaskMsg = EmailHelper.replaceKeyValueFromStrAndAddColorWithBoldStyle(speakerTaskMsg, EVENT_UC_NAME, emailDataDto.getEventName());
            speakerTaskMsg = EmailHelper.replaceKeyValueFromStrAndAddColorWithBoldStyle(speakerTaskMsg, NAME_OF_SESSION, eventTaskUserEmailDto.getSessionName());

            // Replace value for Session Task Mail subject
            mailSubject = "Reminder: Incomplete Task for Your Session \"${session_name}\"";
            mailSubject = EmailHelper.replaceDynamicKeyWithValueInStr(mailSubject, NAME_OF_SESSION, eventTaskUserEmailDto.getSessionName());

        } else if(TaskType.SPEAKER.name().equals(emailDataDto.getTaskType()) || TaskType.EXHIBITOR.name().equals(emailDataDto.getTaskType())) {
            speakerTaskMsg = EmailHelper.replaceKeyValueFromStrAndAddColorWithBoldStyle(speakerTaskMsg, TASK_NAME, emailDataDto.getTaskName());
            speakerTaskMsg = EmailHelper.replaceKeyValueFromStrAndAddColorWithBoldStyle(speakerTaskMsg, EVENT_UC_NAME, emailDataDto.getEventName());

            mailSubject = EmailHelper.replaceDynamicKeyWithValueInStr(mailSubject, TASK_NAME, emailDataDto.getTaskName());
            mailSubject = EmailHelper.replaceDynamicKeyWithValueInStr(mailSubject, EVENT_UC_NAME, emailDataDto.getEventName());
        }

        speakerTaskMsg = emailDataDto.isEventTaskBeforeDueEmail() ? speakerTaskMsg.concat(STRING_BLANK).concat(taskOverDuePostFixMsg) : speakerTaskMsg;
        substitutionMap.put(ConstantEmailDynamic.HEADER_TEXT, headerText);
        substitutionMap.put(EVENT_TASK_CUSTOM_MESSAGE, speakerTaskMsg);
        substitutionMap.put(TASK_BUTTON_DESCRIPTION, String.format("Click the button below to see all your tasks on %s.", TaskType.EXHIBITOR.name().equals(emailDataDto.getTaskType()) ? "My Booth" : "Speaker Hub"));
        substitutionMap.put(TASK_URL, portalMagicLink);
        substitutionMap.put(UNSUBSCRIBE_LINK,getUnSubscribeUrl(emailDataDto,eventTaskUserEmailDto.getEmail()));

        emailDataDto.setSubject(mailSubject);
    }

    private String getCalenderInvitation(String wlHostBaseURL,String eventUrl) {
        return StringUtils.isNotBlank(wlHostBaseURL) ? String.format(Constants.calenderInvitationDescription,wlHostBaseURL,eventUrl) : String.format(Constants.calenderInvitationDescription,EmailUtils.getUiBaseURLByEnv(environment),eventUrl);
    }

    private String getAndCreatePlateFormLevelMagicLinkToken(Long eventId, Long userId, String creationLocation) throws SQLException {
        if (userId == null || eventId == null) {
            return STRING_EMPTY;
        }
        String token = UUID.randomUUID().toString();
        dbRequest.saveAutoLogin(eventId, userId, LoginLink.MAGIC_LINK.name(), DateUtils.getAddedHours(new Date(), 24 * 7 ), token, LINK_TYPE_PLATFORM_LEVEL);
        log.info("getAndCreatePlateFormLevelMagicLinkToken from Lambda for event {} userId {} creationLocation {}", eventId, userId, creationLocation);
        return token;
    }

    /**
     * Builds payment link for unpaid or partially paid tickets
     * Pattern: /e/u/checkout/{eventURL}/tickets/orderDetails/{encryptedOrderId}/ticket/{encryptedTicketId}
     */
    private String buildPaymentLink(EmailDataDto emailDataDto, EmailUserWithTicketDto emailUserWithTicketDto) {
        try {
            String baseUrl = EmailUtils.getUiBaseURLByEnv(environment);
            StringBuilder paymentLinkBuilder = new StringBuilder(baseUrl)
                    .append("/e/u/checkout/")
                    .append(emailDataDto.getEventURL())
                    .append("/tickets/orderDetails/")
                    .append(EncryptUtils.encryptUrlSafe(String.valueOf(emailUserWithTicketDto.getOrderNumber())))
                    .append("/ticket/")
                    .append(EncryptUtils.encryptUrlSafe(String.valueOf(emailUserWithTicketDto.getTicketNumber())));

            return paymentLinkBuilder.toString();
        } catch (Exception e) {
            log.error("Error building payment link for order {} ticket {}: {}",
                    emailUserWithTicketDto.getOrderNumber(),
                    emailUserWithTicketDto.getTicketNumber(),
                    e.getMessage());
            return STRING_EMPTY;
        }
    }

    /**
     * Gets all contact custom attribute keys from the emailUserWithTicketDto in merge tag format.
     * @param emailUserWithTicketDto the email user data containing contact attribute JSON
     * @return set of contact attribute keys in ${key} format
     */
    private Set<String> getContactCustomAttributeKeys(EmailUserWithTicketDto emailUserWithTicketDto) {
        Set<String> keys = new HashSet<>();

        // Check if contact attribute JSON value exists
        if (StringUtils.isEmpty(emailUserWithTicketDto.getContactAttributeJsonValue())) {
            return keys;
        }

        try {
            JSONObject jsonObject = new JSONObject(emailUserWithTicketDto.getContactAttributeJsonValue());

            // Check if attributes object exists
            if (!jsonObject.has(SALESFORCE_ATTRIBUTE)) {
                return keys;
            }

            JSONObject attributes = jsonObject.getJSONObject(SALESFORCE_ATTRIBUTE);
            Set<String> rawKeys = attributes.keySet();

            // Convert raw keys to merge tag format
            for (String rawKey : rawKeys) {
                String mergeTagKey = Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + rawKey + Constants.CLOSE_CURLY_BRACE;
                keys.add(mergeTagKey);
            }

        } catch (Exception e) {
            log.error("Error parsing contact attribute JSON for email {}: {}", emailUserWithTicketDto.getEmail(), e.getMessage());
        }

        return keys;
    }

    /**
     * Replaces contact custom attributes in the message body with their corresponding values
     * from the contactAttributeJsonValue field.
     *
     * @param messageBody the email message body that may contain custom attribute keys
     * @param emailUserWithTicketDto the email user data containing contact attributes
     * @return the message body with custom attribute keys replaced with their values
     */
    private String replaceContactCustomAttributes(String messageBody, EmailUserWithTicketDto emailUserWithTicketDto) {
        // Check if contact attribute JSON value exists
        if (StringUtils.isEmpty(emailUserWithTicketDto.getContactAttributeJsonValue())) {
            // No JSON data available, return message body as is
            return messageBody;
        }

        // Check if this is a test email
        boolean isTestMail = isTestEmail(emailUserWithTicketDto);

        try {
            // Parse the JSON string to extract attributes
            JSONObject jsonObject = new JSONObject(emailUserWithTicketDto.getContactAttributeJsonValue());

            // Check if attributes object exists
            if (!jsonObject.has(SALESFORCE_ATTRIBUTE)) {
                // No attributes found, return message body as is
                return messageBody;
            }

            JSONObject attributes = jsonObject.getJSONObject(SALESFORCE_ATTRIBUTE);
            Set<String> rawKeys = attributes.keySet();

            // Convert raw keys to curly brace format for consistent processing
            Set<String> existingKeys = new HashSet<>();
            for (String key : rawKeys) {
                String curlyBraceKey = Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + key + Constants.CLOSE_CURLY_BRACE; // ${FirstName} format
                existingKeys.add(curlyBraceKey);
            }

            // Iterate through all attribute keys and replace them in the message body
            for (String key : rawKeys) {
                String curlyBraceKey = Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + key + Constants.CLOSE_CURLY_BRACE; // ${FirstName} format
                String value = attributes.optString(key, null);

                if (messageBody.contains(curlyBraceKey)) {
                    // Handle ${FirstName} format
                    if (isTestMail) {
                        messageBody = replace(messageBody, curlyBraceKey, curlyBraceKey);
                        log.debug("Test email: Replaced contact attribute key '{}' with merge tag name '{}' in message body", curlyBraceKey, curlyBraceKey);
                    } else if (value == null) {
                        messageBody = replace(messageBody, curlyBraceKey, Constants.STRING_EMPTY);
                        log.debug("Replaced contact attribute key '{}' with null value in message body", curlyBraceKey);
                    } else {
                        // Convert SINGLE_CHECKBOX values from true/false to Checked/Unchecked
                        String displayValue = formatAttributeValueForDisplay(value, CONTACT);
                        messageBody = replace(messageBody, curlyBraceKey, displayValue);
                        log.debug("Replaced contact attribute key '{}' with value '{}' in message body", curlyBraceKey, displayValue);
                    }
                }
            }


        } catch (Exception e) {
            log.error("Error parsing contact attribute JSON for email {}: {}", emailUserWithTicketDto.getEmail(), e.getMessage());
        }

        return messageBody;
    }

    /**
     * Formats attribute values for display in emails based on their type and content.
     * Handles multiple attribute types:
     * - SINGLE_CHECKBOX: Converts "true"/"false" to "Checked"/"Unchecked"
     * - IMAGE: Converts UUID values to HTML img tags with appropriate URLs
     * - JSON_ARRAY: Extracts and joins name values from JSON arrays
     * - Other types: Returns the original value
     *
     * @param value the original value from the custom attribute
     * @param attributeType the type of attribute (CONTACT, TICKET_BUYER, TICKET_HOLDER)
     * @return formatted display value appropriate for email content
     */
    private String formatAttributeValueForDisplay(String value, String attributeType) {
        if (StringUtils.isEmpty(value) || value.replace("|", STRING_EMPTY).trim().isEmpty()) {
            return Constants.STRING_EMPTY;
        }
        try {
            if (TRUE.equalsIgnoreCase(value)) {
                return CHECKED;
            } else if (FALSE.equalsIgnoreCase(value)) {
                return UNCHECKED;
            } else if (isUUIDPattern(value) && attributeType.equalsIgnoreCase(CONTACT)) {

                String contactImageTag = "";
                String logoUrl = envConfig.getCloudinaryURLWithImagePrefix().concat(value);
                contactImageTag = "<img src=\"" + logoUrl + "\" height=\"200\" width=\"300\">";

                return contactImageTag;
            } else if (isUUIDPattern(value) && attributeType.equalsIgnoreCase(TICKET_BUYER)) {

                String buyerHolderImageTag = "";
                String imageUrl = envConfig.getImagePreFix().concat("ticket_buyer_uploads/").concat(value);
                buyerHolderImageTag = "<img src=\"" + imageUrl + "\" height=\"200\" width=\"300\">";

                return buyerHolderImageTag;
            }
            // Check if value is a JSON array like [{"id":"f16cdc12-a21a-477a-96dc-0b3ba77b58cb","name":"MUSIC"}]
            if (value.trim().startsWith(STRING_LEFT_SQUARE_BRACKET) && value.trim().endsWith(STRING_RIGHT_SQUARE_BRACKET)) {
                try {
                    JSONArray jsonArray = new JSONArray(value);
                    List<String> names = new ArrayList<>();

                    for (int i = 0; i < jsonArray.length(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if (jsonObject.has(NAME_SMALL)) {
                            String name = jsonObject.getString(NAME_SMALL);
                            if (StringUtils.isNotEmpty(name)) {
                                names.add(name);
                            }
                        }
                    }

                    // Join names with comma and space
                    return String.join(COMMA_SPACE, names);
                } catch (Exception jsonException) {
                    log.debug("Value looks like JSON array but failed to parse - Raw value: '{}', Error: {}", value, jsonException.getMessage());
                    // Continue with other checks if JSON parsing fails
                }
            }

            return value; // Return original value if it's not a boolean
        } catch (Exception e) {
            log.error("Error formatting attribute value for display - Raw value: '{}', Attribute type: '{}', Error: {}", value, attributeType, e.getMessage());
            return value;
        }
    }

    /**
     * Checks if the value matches an image pattern (UUID_filename.extension)
     * Uses existing codebase patterns for image extension validation
     * @param value the value to check
     * @return true if the value matches image pattern, false otherwise
     */
    private boolean isUUIDPattern(String value) {
        try {
            if (StringUtils.isEmpty(value)) {
                return false;
            }

            // Check for UUID pattern at the start: 8-4-4-4-12 hexadecimal characters separated by hyphens
            // Example: bfbf241b-fd06-4819-aa5b-47f78426e613_filename.jpg
            if (!value.matches(IMAGE_VALIDATION_PATTERN)) {
                return false;
            }

            return hasImageExtension(value);
        } catch (Exception e) {
            log.error("Error checking UUID pattern for value '{}': {}", value, e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the value has a valid image file extension
     * Uses the same image extensions as defined in RestUploadController.ALLOWED_EXTENSIONS
     * @param value the value to check
     * @return true if the value has an image extension, false otherwise
     */
    private boolean hasImageExtension(String value) {
        try {
            if (StringUtils.isEmpty(value)) {
                return false;
            }

            int lastDotIndex = value.lastIndexOf('.');
            if (lastDotIndex == -1 || lastDotIndex == value.length() - 1) {
                return false;
            }

            String extension = value.substring(lastDotIndex + 1).toLowerCase();

            // Using same image extensions as RestUploadController.ALLOWED_EXTENSIONS
            return extension.matches("^(jpeg|jpg|png|gif|bmp|webp|tiff|svg|ico)$");
        } catch (Exception e) {
            log.error("Error checking image extension for value '{}': {}", value, e.getMessage());
            return false;
        }
    }

    /**
     * Finds and replaces merge tags in the message body that follow the pattern $Name_id
     * but are not present in the existing attributes keySet.
     *
     * @param messageBody the email message body
     * @param existingKeys the set of existing attribute keys
     * @param isTestMail whether this is a test email
     * @return the message body with unmatched merge tags replaced
     */
    private String replaceUnmatchedMergeTags(String messageBody, Set<String> existingKeys, boolean isTestMail) {
        // Pattern to match merge tags like ${CustomTextTest_230}, ${Birthdate_216}, etc.
        // This matches: ${ followed by word characters, underscore, digits, and closing }
        Matcher matcher = ENGAGE_MERGE_TAG_PATTERN.matcher(messageBody);

        Set<String> foundMergeTags = new HashSet<>();
        while (matcher.find()) {
            String foundTag = matcher.group();
            foundMergeTags.add(foundTag);
        }

        // Replace merge tags that are not in existingKeys
        for (String mergeTag : foundMergeTags) {
            if (!existingKeys.contains(mergeTag)) {
                if (isTestMail) {
                    // For test emails, keep the merge tag name
                    messageBody = replace(messageBody, mergeTag, mergeTag);
                    log.debug("Test email: Unmatched merge tag '{}' kept as merge tag name in message body", mergeTag);
                } else {
                    // For regular emails, replace with null (empty string)
                    messageBody = replace(messageBody, mergeTag, Constants.STRING_EMPTY);
                    log.debug("Replaced unmatched merge tag '{}' with null value in message body", mergeTag);
                }
            }
        }

        return messageBody;
    }


}
