package com.accelevents.email.mail;

import com.accelevents.dto.lambda.ReminderEmailLambdaMessage;
import com.accelevents.email.db.DBRequest;
import com.accelevents.email.service.QueryBuilder;
import com.accelevents.email.utils.BulkLambdaConstants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

public class ReminderEmailScheduler {

    private static final Logger log = LoggerFactory.getLogger(ReminderEmailScheduler.class);

    private final SqsClient sqsClient;
    private final DBRequest dbRequest;
    private final String generateEmailQueueUrl;
    private final QueryBuilder queryBuilder;

    public ReminderEmailScheduler(String jdbcUrl, String roJdbcUrl, String userName, String password, String generateMessageQueueName) throws SQLException {
        dbRequest = new DBRequest(jdbcUrl, roJdbcUrl, userName, password);
        queryBuilder = new QueryBuilder();

        Region region = Region.US_EAST_1;
        AwsCredentialsProvider cp = DefaultCredentialsProvider.create();

        sqsClient = SqsClient.builder()
                .region(region)
                .credentialsProvider(cp)
                .build();

        this.generateEmailQueueUrl = sqsClient.getQueueUrl(request -> request.queueName(generateMessageQueueName)).queueUrl();
    }

    public void sendScheduledResendTicketingReminder() {

        try {
            ResultSet resendTicketingRs = dbRequest.getQueryResult(queryBuilder.createResendTicketingEmailTableScheduleQuery().toString());
            while (resendTicketingRs.next()) {
                Long resendTicketingReminderId = resendTicketingRs.getLong(BulkLambdaConstants.COL_ID);
                Timestamp scheduledAt = resendTicketingRs.getTimestamp(BulkLambdaConstants.COL_EVENT_TICKET_MAIL_RESEND_TIME);

                log.info("ResendTicketingId => {} scheduled_at => {}", resendTicketingReminderId, scheduledAt);
                ReminderEmailLambdaMessage reminderEmailLambdaMessage = new ReminderEmailLambdaMessage(resendTicketingReminderId);

                this.sendReminderEmailLambdaMessageToSqsQueue(reminderEmailLambdaMessage);
            }
            log.info("ReminderEmail schedule scan complete");

        } catch (Exception e) {
            log.error("Exception sendScheduledResendTicketingReminder => ", e);
            Sentry.captureException(e);
        }
    }

    public void sendScheduledEventTaskReminderEmail() {
        try {
            ResultSet eventTaskReminderRs = dbRequest.getQueryResult(queryBuilder.createEventTaskReminderEmailScheduleQuery().toString());
            while (eventTaskReminderRs.next()) {
                Long eventTaskReminderId = eventTaskReminderRs.getLong(BulkLambdaConstants.COL_ID);
                Timestamp scheduledAt = eventTaskReminderRs.getTimestamp(BulkLambdaConstants.COL_SCHEDULED_AT);

                log.info("eventTaskReminderId => {} scheduled_at => {}", eventTaskReminderId, scheduledAt);
                ReminderEmailLambdaMessage reminderEmailLambdaMessage = new ReminderEmailLambdaMessage().withEventTaskReminderEmailId(eventTaskReminderId);

                this.sendReminderEmailLambdaMessageToSqsQueue(reminderEmailLambdaMessage);
            }
            log.info("EventTaskReminderEmail schedule scan complete");

        } catch (Exception e) {
            log.error("Exception sendScheduledEventTaskReminderEmail => ", e);
            Sentry.captureException(e);
        }
    }

    private void sendReminderEmailLambdaMessageToSqsQueue(ReminderEmailLambdaMessage reminderEmailLambdaMessage) {

        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String reminderEmailLambdaMessageString = null;
        try {
            reminderEmailLambdaMessageString = objectMapper.writeValueAsString(reminderEmailLambdaMessage);
        } catch (JsonProcessingException e) {
            log.error("sendSQSMessageToProcessReminderEmails | JsonParsing Error", e);

            Sentry.setExtra("resendTicketingId", reminderEmailLambdaMessage.getResendTicketingEmailId().toString());
            Sentry.captureException(e);
        }

        SendMessageRequest sendMsgRequest = SendMessageRequest.builder()
                .queueUrl(generateEmailQueueUrl)
                .messageBody(reminderEmailLambdaMessageString)
                .delaySeconds(1)
                .build();

        log.info("Msg => {}", reminderEmailLambdaMessageString);
        SendMessageResponse sendMsgResponse = sqsClient.sendMessage(sendMsgRequest);
        log.info("Generate email Sqs response Message ID: {}", sendMsgResponse.messageId());
    }

}
