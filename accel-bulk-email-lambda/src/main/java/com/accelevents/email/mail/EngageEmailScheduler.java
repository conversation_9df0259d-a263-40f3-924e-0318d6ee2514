package com.accelevents.email.mail;

import com.accelevents.dto.lambda.EngageEmailLambdaMessage;
import com.accelevents.email.db.DBRequest;
import com.accelevents.email.service.QueryBuilder;
import com.accelevents.email.utils.BulkLambdaConstants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

public class EngageEmailScheduler {

    private static final Logger log = LoggerFactory.getLogger(EngageEmailScheduler.class);

    private final SqsClient sqsClient;
    private final DBRequest dbRequest;
    private final String generateEmailQueueUrl;
    private final QueryBuilder queryBuilder;

    public EngageEmailScheduler(String jdbcUrl, String roJdbcUrl, String userName, String password, String generateMessageQueueName) throws SQLException {
        dbRequest = new DBRequest(jdbcUrl, roJdbcUrl, userName, password);
        queryBuilder = new QueryBuilder();

        Region region = Region.US_EAST_1;
        AwsCredentialsProvider cp = DefaultCredentialsProvider.create();

        sqsClient = SqsClient.builder()
                .region(region)
                .credentialsProvider(cp)
                .build();

        this.generateEmailQueueUrl = sqsClient.getQueueUrl(request -> request.queueName(generateMessageQueueName)).queueUrl();
    }

    public void sendScheduledEngageEmail() throws SQLException {
        ResultSet messageToContactsRs = dbRequest.getQueryResult(queryBuilder.createMessageToContactTableScheduleQuery().toString());
        while (messageToContactsRs.next()){
            Long messageToContactId = messageToContactsRs.getLong(BulkLambdaConstants.COL_ID);
            Timestamp scheduledAt = messageToContactsRs.getTimestamp(BulkLambdaConstants.COL_SCHEDULED_AT);

            log.info("MessageToContactId => {} scheduled_at => {}", messageToContactId, scheduledAt);
            EngageEmailLambdaMessage engageEmailLambdaMessage = new EngageEmailLambdaMessage(messageToContactId);
            this.sendMessageIdToGenerateEmailSqsQueue(engageEmailLambdaMessage);
        }
        log.info("EngageEmail schedule scan complete");
    }

    private void sendMessageIdToGenerateEmailSqsQueue(EngageEmailLambdaMessage engageEmailLambdaMessage) {

        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String engageEmailMessageString = null;
        try {
            engageEmailMessageString = objectMapper.writeValueAsString(engageEmailLambdaMessage);
        } catch (JsonProcessingException e) {
            log.error("sendSQSMessageToProcessEngageEmails | JsonParsing Error", e);

            Sentry.setExtra("messageToContactId", engageEmailLambdaMessage.getMessageToContactsId().toString());
            Sentry.captureException(e);
        }

        SendMessageRequest sendMsgRequest = SendMessageRequest.builder()
                .queueUrl(generateEmailQueueUrl)
                .messageBody(engageEmailMessageString)
                .delaySeconds(1)
                .build();

        log.info("Msg => {}", engageEmailLambdaMessage);
        SendMessageResponse sendMsgResponse = sqsClient.sendMessage(sendMsgRequest);
        log.info("Generate email Sqs response Message ID: {}", sendMsgResponse.messageId());
    }

}
