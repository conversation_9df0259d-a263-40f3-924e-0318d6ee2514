package com.accelevents.email.mail;

import com.accelevents.domain.enums.MessageStatus;
import com.accelevents.dto.lambda.BulkEmailLambdaMessage;
import com.accelevents.dto.lambda.EmailDataDto;
import com.accelevents.dto.lambda.EventTaskUserEmailDto;
import com.accelevents.email.db.DBRequest;
import com.accelevents.email.service.*;
import com.accelevents.helpers.EmailLogoImageHelper;
import com.accelevents.messages.EnumUnSubscribeLink;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import static com.accelevents.email.utils.BulkLambdaConstants.*;
import static com.accelevents.helpers.TemplateId.SPEAKER_AND_SESSION_TASK_REMINDER_EMAIL;
import static com.accelevents.utils.Constants.MANUAL;
import static com.accelevents.utils.Constants.STRING_EMPTY;
import static java.util.Map.entry;

public class PrepareBulkEventTaskReminderEmail {

    private static final Logger log = LoggerFactory.getLogger(PrepareBulkEventTaskReminderEmail.class);

    private final SqsClient sqsClient;
    private final DBRequest dbRequest;
    private final String bulkMessageQueueURL;
    private final SqsMessageSender sqsMessageSender;
    private final EnvValuesConfiguration envConfig;
    private final QueryBuilder queryBuilder;
    private final EmailLogoImageHelper emailLogoImageHelper;
    private final RecipientListDetailsService recipientListDetailsService;

    public PrepareBulkEventTaskReminderEmail(String jdbcUrl, String roJdbcUrl, String userName, String password, String bulkMessageQueueName) throws SQLException {
        dbRequest = new DBRequest(jdbcUrl, roJdbcUrl, userName, password);
        envConfig = new EnvValuesConfiguration();
        queryBuilder = new QueryBuilder();
        AdvancedFilterService advancedFilterService = new AdvancedFilterService(dbRequest,queryBuilder);
        emailLogoImageHelper = new EmailLogoImageHelper(envConfig.getImagePreFix(), envConfig.getBlackLogo(), envConfig.getImagesAcceleventlogo(), envConfig.getDefaultAcceleventsLogo());


        AwsCredentialsProvider cp = DefaultCredentialsProvider.create();

        Region region = Region.US_EAST_1;
        sqsClient = SqsClient.builder()
                .region(region)
                .credentialsProvider(cp)
                .build();

        this.bulkMessageQueueURL = sqsClient.getQueueUrl(request -> request.queueName(bulkMessageQueueName)).queueUrl();
        this.sqsMessageSender = new SqsMessageSender(sqsClient);
        this.recipientListDetailsService = new RecipientListDetailsService(dbRequest, queryBuilder,advancedFilterService);
    }

    public void sendReminderEmailToSpeakers(Long eventTaskReminderId, String emailSendType) throws Exception {

        boolean isManualEmailSendType = MANUAL.equals(emailSendType);
        try {
            EmailDataDto emailDataDto = new EmailDataDto(SPEAKER_AND_SESSION_TASK_REMINDER_EMAIL);
            ResultSet eventTaskReminderEmailRS;

            if(isManualEmailSendType) {
                // Use to find the Event Task for sending reminder email on manually
                eventTaskReminderEmailRS = dbRequest.getQueryResult(queryBuilder.getEventTaskEmailForManualSendQuery(eventTaskReminderId).toString());
            } else {
                eventTaskReminderEmailRS = dbRequest.getQueryResult(queryBuilder.getEventTaskReminderEmailQuery(eventTaskReminderId).toString());
            }

            Map<String, Object> eventTaskReminderEmailMap = new HashMap<>();
            while (eventTaskReminderEmailRS.next()){
                emailDataDto.setEventId(eventTaskReminderEmailRS.getLong(COL_EVENT_ID));
                emailDataDto.setTaskName(eventTaskReminderEmailRS.getString(COL_TITLE));
                emailDataDto.setTaskType(eventTaskReminderEmailRS.getString(COL_TASK_TYPE));
                emailDataDto.setEventTaskBeforeDueEmail(eventTaskReminderEmailRS.getBoolean(COL_ALLOW_BEFORE_TASK_IS_DUE));

                eventTaskReminderEmailMap.put(COL_EVENT_ID, eventTaskReminderEmailRS.getLong(COL_EVENT_ID));
                eventTaskReminderEmailMap.put(COL_TASK_ID, eventTaskReminderEmailRS.getLong(COL_TASK_ID));
                eventTaskReminderEmailMap.put(COL_SCHEDULED_STATUS, eventTaskReminderEmailRS.getString(COL_SCHEDULED_STATUS));
            }

            if(MessageStatus.SCHEDULED.name().equals(eventTaskReminderEmailMap.get(COL_SCHEDULED_STATUS))) {

                this.updateResendTicketingEmailStatus(eventTaskReminderId, MessageStatus.IN_PROGRESS, isManualEmailSendType);
                List<EventTaskUserEmailDto> emailRecipientList = recipientListDetailsService.getEventTaskReminderEmailRecipientList(emailDataDto.getEventId(), Long.valueOf(eventTaskReminderEmailMap.get(COL_TASK_ID).toString()),emailDataDto.getTaskType());

                if (!emailRecipientList.isEmpty()) {
                    this.fetchEventDetailsAndPrepareBulkEmail(emailDataDto, emailRecipientList);
                }

                this.updateResendTicketingEmailStatus(eventTaskReminderId, MessageStatus.SENT, isManualEmailSendType);
            }else {
                log.info("Resend Ticketing Email not processed because id {} status = {} emailSendTYpe ={}", eventTaskReminderId, eventTaskReminderEmailMap.get(COL_SCHEDULED_STATUS), emailSendType);
            }


        }  catch (Exception ex) { // NOSONAR
            log.error("Exception prepare email: ", ex);
            this.updateResendTicketingEmailStatus(eventTaskReminderId, MessageStatus.FAILED, isManualEmailSendType);

            Sentry.setExtra("resendTicketingEmailId", eventTaskReminderId.toString());
            Sentry.captureException(ex);
        } finally {
            dbRequest.close();
            sqsClient.close();
        }

    }


    private void fetchEventDetailsAndPrepareBulkEmail(EmailDataDto emailDataDto, List<EventTaskUserEmailDto> emailRecipientList)  throws SQLException, JsonProcessingException, InterruptedException {
        ResultSet eventRs = dbRequest.getQueryResult(queryBuilder.createEventWithDesignAndTicketingTableQuery(emailDataDto.getEventId()).toString());
        while (eventRs.next()) {
            ResultSetResolver.updateEventDataFromTheEventResultSet(eventRs, emailDataDto);
        }

        if (emailDataDto.getWhiteLabelId() > 0) {
            ResultSet whiteLabelRs = dbRequest.getQueryResult(queryBuilder.createWhiteLabelTableQuery(emailDataDto.getWhiteLabelId()).toString());
            while (whiteLabelRs.next()) {
                ResultSetResolver.updateWhiteLabelDataFromTheWhiteLabelResultSet(whiteLabelRs, emailDataDto);
            }
        }

        emailDataDto.setSubject(STRING_EMPTY);
        emailDataDto.setTemplateName(SPEAKER_AND_SESSION_TASK_REMINDER_EMAIL.getValue());

        emailDataDto.setHeaderImgFullURL(emailLogoImageHelper.getEventOrDefaultLogo(emailDataDto.getEventLogoImg()));
        if(StringUtils.isNotEmpty(emailDataDto.getWlHeaderLogoImg())){
            emailDataDto.setFooterImgFullURL(emailLogoImageHelper.getHeaderEventLogo(emailDataDto.getWlHeaderLogoImg()));
        }
        else {
            emailDataDto.setFooterImgFullURL(emailLogoImageHelper.getHeaderEventLogo(emailDataDto.getEventHeaderLogoImg()));
        }
        emailDataDto.setAdvanceEmailBuilder(Boolean.FALSE);

        int size = emailRecipientList.size();
        int batchCount = 1;

        for (int i = 0; i < size; i += 10) {
            this.sendBulkEmailMessageToSqs(emailDataDto, new ArrayList<>(emailRecipientList.subList(i, Math.min(size, i + 10))));
            log.info("Batch Completed: {}", batchCount++);
            Thread.sleep(200);
        }
    }


    private void sendBulkEmailMessageToSqs(EmailDataDto emailDataDto, List<EventTaskUserEmailDto> emailRecipientList) throws JsonProcessingException {
        BulkEmailLambdaMessage<EventTaskUserEmailDto> bulkEmailLambdaMessage = new BulkEmailLambdaMessage<>("EVENT_TASK_REMINDER", emailDataDto, emailRecipientList, EnumUnSubscribeLink.STAFF);

        ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String bulkEmailMessageString = objectMapper.writeValueAsString(bulkEmailLambdaMessage);

        log.info("SQS Msg => {}", bulkEmailMessageString);
        sqsMessageSender.sendSQSMessage(this.bulkMessageQueueURL, bulkEmailMessageString);
    }

    private void updateResendTicketingEmailStatus(Long eventTaskReminderId, MessageStatus messageStatus, boolean isManualEmailSendType) throws SQLException {
       if(!isManualEmailSendType) {
           // update the DB row when the reminder is sending by Scheduler
           Map<String, Object> entries;
           if(MessageStatus.SENT.equals(messageStatus)) {
               entries = Map.ofEntries(
                       entry(COL_SCHEDULED_STATUS, messageStatus.name()),
                       entry(COL_EXECUTED_AT, new java.sql.Timestamp(new Date().getTime()))
               );
           }else {
               entries = Map.ofEntries(
                       entry(COL_SCHEDULED_STATUS, messageStatus.name())
               );
           }

           dbRequest.updateEventTaskReminderEmailStatus(eventTaskReminderId, entries);
       }
    }

}
