/*
package com.accelevents.repositories;

import com.accelevents.domain.Event;
import com.accelevents.domain.Stripe;

import com.accelevents.dto.*;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface StripeRepository extends CrudRepository<Stripe, Long> {

	@Query("SELECT NEW com.accelevents.dto.StripeDTO(stripe.CCFlatFee, stripe.CCPercentageFee, stripe.country,stripe.paymentGateway) " +
			" FROM Stripe stripe where stripe.event=:event AND stripe.defaultAccount IS TRUE")
    StripeDTO getStripeFeesByEvent(@Param("event") Event event);

}
*/
