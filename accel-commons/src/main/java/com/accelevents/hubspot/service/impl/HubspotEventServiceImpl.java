package com.accelevents.hubspot.service.impl;

import com.accelevents.configuration.HubspotConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.enums.BillingType;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.exceptions.ThirdPartyExceptions;
import com.accelevents.hubspot.dto.*;
import com.accelevents.hubspot.service.HubspotCustomLabelsAssociationService;
import com.accelevents.hubspot.service.HubspotEventBatchTrackRepoService;
import com.accelevents.hubspot.service.HubspotEventService;
import com.accelevents.hubspot.service.HubspotOrganizerService;
import com.accelevents.messages.CheckInSource;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventDesignDetailRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.ExhibitorRepoService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.impl.SpeakerRepoService;
import com.accelevents.ticketing.dto.TicketingDatesDto;
import com.accelevents.utils.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.*;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.enums.PlanConfigNames.*;
import static com.accelevents.utils.Constants.ENV_PROD;
import static com.accelevents.utils.Constants.HUBSPOT_MAX_RETRY_COUNT;

@Service
public class HubspotEventServiceImpl implements HubspotEventService {

    private static final Logger log = LoggerFactory.getLogger(HubspotEventServiceImpl.class);

    @Autowired
    private HubspotConfiguration hubspotConfiguration;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private EventService eventService;
    @Autowired
    private ROEventService roEventService;

    @Autowired
    private EventDesignDetailRepoService eventDesignDetailRepoService;

    @Autowired
    private VirtualEventService virtualEventService;

    @Autowired
    private ExhibitorRepoService exhibitorRepoService;

    @Autowired
    private SessionRepoService sessionRepoService;

    @Autowired
    private SpeakerRepoService speakerRepoService;

    @Autowired
    private RaffleService raffleService;

    @Autowired
    private CauseAuctionService causeAuctionService;

    @Autowired
    private PledgeService pledgeService;

    @Autowired
    private AuctionService auctionService;

    @Autowired
    private BillingEventsService billingEventsService;

    @Autowired
    private StripeTransactionService transactionService;

    @Autowired
    private CheckInAuditLogRepoService checkInAuditLogRepoService;

    @Autowired
    private EventRepoService eventRepoService;

	@Autowired private EventTicketsRepoService eventTicketsRepoService;

	@Autowired private HubspotEventBatchTrackRepoService trackRepoService;

    @Autowired
    private HubspotCustomLabelsAssociationService hubspotCustomLabelsAssociationService;

    @Lazy
    @Autowired
    private HubspotOrganizerService hubspotOrganizerService;

    @Autowired
    private TicketingTypeService ticketingTypeService;

    @Autowired
    private BadgesService badgesService;

    @Autowired
    private GetStreamService getStreamService;

    @Value("${app.profile}")
    private String appProfile;

    private final ObjectMapper mapper = new ObjectMapper();

    private static final long MAX_WAIT_MILLISECONDS = 5000;

    private String batchApiKeyParam = "/batch/create";

    private String hsUrlWithObject;

    @PostConstruct
    public void init() {
        hsUrlWithObject = hubspotConfiguration.getHubspotUrl() + Constants.OBJECTS;
    }

    @Override
    public void createEventSchema() {
        try {
            InputStream eventSchemaInput = this.getClass().getClassLoader().getResourceAsStream("hubspotEventSchema.json");


            JsonObject eventSchema = (JsonObject) JsonParser.parseReader(new InputStreamReader(eventSchemaInput, StandardCharsets.UTF_8));
            HttpEntity<String> schemaRequest = new HttpEntity<>(eventSchema.toString(), hubspotConfiguration.getHubspotHeaders());

            ResponseEntity<String> responseEntity = restTemplate.postForEntity(hubspotConfiguration.getHubspotUrl() + "/schemas", schemaRequest, String.class);

            if (responseEntity.getStatusCode().equals(HttpStatus.CREATED)) {
                log.info("Hubspot Event Schema created. Hubspot ObjectType => {}", eventSchema.get(Constants.NAME.toLowerCase()));
            }
        } catch (RestClientException e) {
            log.error("Error during creating HubspotEventSchema", e);
        }

    }

    @Override
    public boolean isEventSchemaExists() {
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .get(hubspotConfiguration.getHubspotUrl() + "/schemas/" + hubspotConfiguration.getEventObjectName())
                    .headers(hubspotConfiguration.getHubspotHeaders().toSingleValueMap())
                    .asJson();
            if (jsonResponse.getStatus() == 200) {
                return true;
            }
        } catch (UnirestException e) {
            log.error("Exception during checking EventSchema.", e);
        }
        return false;
    }


    @Override
    @Async
    public void createHubspotEventAsync(Event event, Ticketing ticketing, String createdSource) {
        createHubspotEvent(event, ticketing, createdSource);
    }

    @Override
    public void createHubspotEvent(Event event, Ticketing ticketing, String createdSource) {
        try {

            HubspotEventProperties eventProperties = getHubspotEventProperties(event, ticketing.getEventStartDate(), ticketing.getEventEndDate(), createdSource);

            Map<String, HubspotEventProperties> hubspotEventPropertiesMap = new HashMap<>();
            hubspotEventPropertiesMap.put(Constants.PROPERTIES, eventProperties);

            HttpEntity<String> eventRequest = new HttpEntity<>(mapper.writeValueAsString(hubspotEventPropertiesMap), hubspotConfiguration.getHubspotHeaders());
            HubspotEvent hubSpotEvent = executeHubspotCreateAeEventAPI(event.getEventId(), eventRequest, 1);

            if (hubSpotEvent != null) {
                String hubspotEventId = hubSpotEvent.getId();
                event.setHubspotObjectId(Long.valueOf(hubspotEventId));
                if (null != event.getOrganizerId()) {
                    hubspotOrganizerService.createOrDeleteEventToOrganizerAssociation(event.getOrganizerId(), hubspotEventId, Constants.CREATE);
                }
                associateOrganizerOrWLEventToCompany(event);
                log.info("Event created in hubspot. Hubspot Event ObjectId=>{}, EventId=>{}", hubSpotEvent.getId(), event.getEventId());
            }
        } catch (Exception e) {
            log.error("Error during creating hubspot event. EventId=>{}", event.getEventId(), e);
        }
    }

    @Nullable
    private HubspotEvent executeHubspotCreateAeEventAPI(Long eventId, HttpEntity<String> requestBody, int retryCount) {
        try {
            HubspotEvent hubSpotEvent = restTemplate.postForObject(hsUrlWithObject + hubspotConfiguration.getEventObjectName(),
                    requestBody, HubspotEvent.class);
            log.info("Hubspot event created for eventId {}", eventId);
            return hubSpotEvent;
        } catch (ResourceAccessException ex) {
            if(retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Error during creating hubspot event. EventId {} retryCount {}", eventId, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                return executeHubspotCreateAeEventAPI(eventId, requestBody, ++retryCount);
            }
            log.error("Error during creating hubspot event. EventId=>{}", eventId, ex);
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException ex) {
            if ((GeneralUtils.is5xxServerError(ex.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(ex.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Error during creating hubspot event. EventId {} retryCount {}", eventId, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                return executeHubspotCreateAeEventAPI(eventId, requestBody, ++retryCount);
            }
            log.error("Error during creating hubspot event. EventId=>{}", eventId, ex);
        }
        return null;
    }

    private HubspotEventProperties getHubspotEventProperties(Event event, Date eventStartDate, Date eventEndDate, String createdSource) {
        HubspotEventProperties eventProperties = new HubspotEventProperties();
        setHubspotUpdateEventProperties(eventProperties, event, eventStartDate, eventEndDate);
        eventProperties.setEventId(String.valueOf(event.getEventId()));
        eventProperties.setCreatedDate(String.valueOf(event.getCreatedDate().getTime()));
        eventProperties.setCreatedSource(createdSource);
        eventProperties.setDuplicateFrom(event.getCreatedFrom());
        eventProperties.setEventName(event.getName());
        return eventProperties;
    }

    @Override
    @Async
    public void createAssociationEventToContact(String objectId, String contactId) {
        try {
            if (StringUtils.isNotBlank(objectId) && StringUtils.isNotBlank(contactId)) {
                HttpResponse<String> jsonResponse = Unirest.put(hsUrlWithObject +
                                "{objectType}/{objectId}/associations/{toObjectType}/{toObjectId}/{associationType}")
                        .routeParam(Constants.OBJECT_TYPE, hubspotConfiguration.getEventObjectName())
                        .routeParam(Constants.OBJECT_ID, objectId)
                        .routeParam(Constants.TO_OBJECT_TYPE, Constants.CONTACT)
                        .routeParam(Constants.TO_OBJECT_ID, contactId)
                        .routeParam(Constants.ASSOCIATION_TYPE, Constants.AE_EVENTS_TO_CONTACT)
                        .headers(hubspotConfiguration.getHubspotHeaders().toSingleValueMap())
                        .asString();
                if (jsonResponse.getStatus() == 200) {
                    log.info("Association created in hubspot ObjectId =>{}, ContactId =>{}", objectId, contactId);
                } else {
                    log.info("Association not created in hubspot ObjectId =>{}, ContactId =>{}", objectId, contactId);
                }
            }
        } catch (UnirestException e) {
            log.error("Error during creating association in hubspot", e);
        }
    }

    public String getEventByEventIdWithRetry(Long eventId, int retryCount) {
        String errorMessage = "Error during find hubspot event. eventId=>{}";
        try {
            if(eventId == null) {
                log.info("event id is null while search hubspot event, eventId {}", eventId);
                return null;
            }

            Event event = roEventService.findEventByEventId(eventId);
            if (event != null && event.getHubspotObjectId() != null) {
                return event.getHubspotObjectId().toString();
            }

            String searchAPI = new StringBuilder(hsUrlWithObject).append(hubspotConfiguration.getEventObjectName()).append(Constants.STRING_SLASH).append(eventId).append(Constants.QUE_MARK_ID_PROPERTY_EQ)
                    .append(Constants.EVENT_ID_UPPERCASE.toLowerCase()).toString();

            HttpEntity<String> headers = new HttpEntity<>(hubspotConfiguration.getHubspotHeaders());
            ResponseEntity<String> response  =  restTemplate.exchange(searchAPI, HttpMethod.GET, headers, String.class);

            log.info("Hubspot Event find for evenId=>{}", eventId);
            HubspotEvent hubspotEvent = JsonMapper.stringtoObject(response.getBody(), HubspotEvent.class);
            String objectId = hubspotEvent.getId();
            // Store Hubspot ObjectId for event
            if (event != null) {
                Event eventFromDB = eventRepoService.getEventByURLOrThrowException(event.getEventURL());
                event.setEventId(eventFromDB.getEventId());
                event.setHubspotObjectId(Long.valueOf(objectId));
                eventRepoService.save(event);
            }
           return objectId;

        } catch (ResourceAccessException ex) {
            if (retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry during find hubspot event. eventId=>{} retryCount {}", eventId, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                return getEventByEventIdWithRetry(eventId, ++retryCount);
            }
            log.info(errorMessage, eventId, ex);
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException ex) {
            if (ex.getRawStatusCode() == HttpStatus.NOT_FOUND.value()) {
                log.info("Event not found in hubspot for eventId {}", eventId);
                return null;
            }
            if ((GeneralUtils.is5xxServerError(ex.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(ex.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry during find hubspot event. eventId=>{} retryCount {}", eventId, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                return getEventByEventIdWithRetry(eventId, ++retryCount);
            }
            log.info(errorMessage, eventId, ex);
        } catch (Exception ex) {
            log.info(errorMessage, eventId, ex);
        }
        return null;
    }

    @Override
    public String getEventByEventId(Long eventId) {
        return getEventByEventIdWithRetry(eventId, 1);
    }

    @Override
    @Async
    public void addEventCreatorContactWithLabelForEventToHubspot(Event event, String contactId) {
        String eventIdInHubspot = getEventByEventId(event.getEventId());
        hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, contactId, Arrays.asList(Constants.EVENT_CREATOR));
    }

    @Override
    @Async
    public void addEventAdminContactWithLabelForEventToHubspot(Event event, String eventAdminId) {
        if (null != eventAdminId) {
            String eventIdInHubspot = getEventByEventId(event.getEventId());
            hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, eventAdminId, Arrays.asList(Constants.EVENT_ADMIN));
        }
    }

    @Override
    public void addEventAdminContactWithLabelForEventToHubspot(String eventIdInHubspot, String eventAdminId) {
        if (null != eventAdminId) {
            hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, eventAdminId, Arrays.asList(Constants.EVENT_ADMIN));
        }
    }

    @Override
    public void addEventBillingContactWithLabelForEventToHubspot(Event event, String billingContactId) {
        if (null != billingContactId) {
            String eventIdInHubspot = getEventByEventId(event.getEventId());
            hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, billingContactId, Arrays.asList(Constants.EVENT_BILLING_CONTACT));
        }
    }

    @Override
    @Async
    public void updateEventToDeleteInHubspot(Event event, DeletedEvents deletedEvent, User user) {
        try {
            String objectId = getEventByEventId(event.getEventId());
            if (StringUtils.isBlank(objectId)) {
                log.info("Hubspot event not found for eventId=>{}", event.getEventId());
                return;
            }

            HubspotDeleteEventProperties deleteProperties = new HubspotDeleteEventProperties();
            deleteProperties.setDeletedReason(deletedEvent.getReasonForDeletingEvent());
            deleteProperties.setDeletedNotes(deletedEvent.getComment());
            deleteProperties.setDeletedDate(String.valueOf(deletedEvent.getTimeDeleted().getTime()));
            deleteProperties.setDeletedBy(user.getEmail());
            TicketingDatesDto ticketingDatesDto = ticketingService.findEventStartAndEndDateByEventid(event);
            if (ticketingDatesDto != null) {
                setHubspotUpdateEventProperties(deleteProperties, event, ticketingDatesDto.getEventStartDate(), eventService.getEventEndDate(event));
            }

            Map<String, HubspotDeleteEventProperties> deleteEventPropertiesMap = new HashMap<>();
            deleteEventPropertiesMap.put(Constants.PROPERTIES, deleteProperties);

            HttpEntity<String> updateRequest = new HttpEntity<>(mapper.writeValueAsString(deleteEventPropertiesMap), hubspotConfiguration.getHubspotHeaders());
            executeHubspotAeEventUpdateAPI(event, objectId, updateRequest, 1);
        } catch (Exception e) {
            log.error("Error during update event in hubspot | updateEventToDeleteInHubspot. EventId=>{}", event.getEventId(), e);
        }
    }

    @Override
    @Async
    public void updateHubspotEventDetails(Event event) {
        try {

            String objectId = getEventByEventId(event.getEventId());
            if (StringUtils.isBlank(objectId) || StringUtils.isBlank(event.getEventURL())) {
                log.info("Hubspot event not found for eventId=>{}, eventUrl=>{}", event.getEventId(), event.getEventURL());
                return;
            }

            Ticketing ticketing = ticketingService.findByEvent(event);
            HubspotUpdateEventProperties updateEventProperties = new HubspotUpdateEventProperties();
            if (null == ticketing) {
                log.info("Ticketing not found for eventId=>{}, eventUrl=>{}", event.getEventId(), event.getEventURL());
                return;
            }
            setHubspotUpdateEventProperties(updateEventProperties, event, ticketing.getEventStartDate(), ticketing.getEventEndDate());
            Map<String, HubspotUpdateEventProperties> updateEventPropertiesMap = new HashMap<>();
            updateEventPropertiesMap.put(Constants.PROPERTIES, updateEventProperties);

            HttpEntity<String> updateRequest = new HttpEntity<>(mapper.writeValueAsString(updateEventPropertiesMap), hubspotConfiguration.getHubspotHeaders());
            executeHubspotAeEventUpdateAPI(event, objectId, updateRequest, 1);
        } catch (Exception e) {
            log.error("Error during update Hubspot event details | updateHubspotEventDetails. EventId=>{}", event.getEventId(), e);
        }
    }

    private void executeHubspotAeEventUpdateAPI(Event event, String objectId, HttpEntity<String> requestBody, int retryCount) {
        try {

            Map<String, String> uriMap = new HashMap<>();
            uriMap.put(Constants.OBJECT_TYPE, hubspotConfiguration.getEventObjectName());
            uriMap.put(Constants.OBJECT_ID, objectId);

            restTemplate.patchForObject(hsUrlWithObject + "{objectType}/{objectId}",
                    requestBody, HubspotEventProperties.class, uriMap);
            log.info("Hubspot event updated. ObjectId=>{}, EventId=>{}", objectId, event.getEventId());
        } catch(ResourceAccessException ex){
            if(retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry during update details in Hubspot event. EventId {} objectId {} retryCount {}", event.getEventId(), objectId, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeHubspotAeEventUpdateAPI(event, objectId, requestBody, ++retryCount);
                return;
            }
            log.error("Error during update details in Hubspot event. EventId {}", event.getEventId(), ex);
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException ex) {
            // Add Logic for retry one time when have Exception from Hubspot server
            if ((GeneralUtils.is5xxServerError(ex.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(ex.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry during update details in Hubspot event. EventId {} objectId {} retryCount {}", event.getEventId(), objectId, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeHubspotAeEventUpdateAPI(event, objectId, requestBody, ++retryCount);
                return;
            }
            log.error("Error during update details in Hubspot event. EventId {}", event.getEventId(), ex);
        }
    }

    @Override
    public void updateHubspotEventDetailsWithoutAsync(Event event) {
        updateHubspotEventDetails(event);
    }

    private void setHubspotUpdateEventProperties(HubspotUpdateEventProperties updateEventProperties, Event event, Date startDate, Date endDate) {

        EventDesignDetail eventDesignDetail = eventDesignDetailRepoService.findByEvent(event);

        updateEventProperties.setEventUrl(event.getEventURL());
        updateEventProperties.setEventFormat(event.getEventFormat() != null ? event.getEventFormat().getFormat() : EventFormat.VIRTUAL.getFormat());
        if (eventDesignDetail != null) {
            updateEventProperties.setEventType(eventDesignDetail.getEventType() != null ? eventDesignDetail.getEventType().name() : null);
        }
        updateEventProperties.setEventTimezone(event.getTimezoneId());
        updateEventProperties.setEventStartDate(String.valueOf(startDate.getTime()));

        updateEventProperties.setEventStartTime(TimeZoneUtil.getDateInLocal(startDate, event.getEquivalentTimeZone(), Constants.TIME_FORMAT_WITH_ZONE_OFFSET));
        updateEventProperties.setEventEndDate(String.valueOf(endDate.getTime()));
        updateEventProperties.setEventEndTime(TimeZoneUtil.getDateInLocal(endDate, event.getEquivalentTimeZone(), Constants.TIME_FORMAT_WITH_ZONE_OFFSET));

        updateEventProperties.setEventStatus(event.getEventStatus() != null ? event.getEventStatus().name() : null);
        updateEventProperties.setEventListingVisibility(event.getEventListingStatus().name());

        updateEventProperties.setNumberOfRegistrants(eventTicketsRepoService.countDistinctTicketHolderByEvent(event.getEventId()));
        updateEventProperties.setNumberOfProExhibitors(virtualEventService.getTheNumberOfExhibitorsPurchased(event));
        updateEventProperties.setNumberOfExhibitors(exhibitorRepoService.findNumberOfExhibitorsByEventId(event));
        updateEventProperties.setNumberOfSessions(sessionRepoService.getSessionCountByEventId(event.getEventId()));
        updateEventProperties.setNumberOfSpeakers(speakerRepoService.getSpeakerCountByEventId(event.getEventId()));

        updateEventProperties.setBadgeDesignsCreated(badgesService.countByEventId(event.getEventId()));
        updateEventProperties.setTicketTypesCreated(ticketingTypeService.getTicketTypeCountByEventId(event.getEventId()));
        updateEventProperties.setIsEventComp(event.getBillingType() == BillingType.Comp ? "Yes" : "No");
        setAttendeeUpdateProperties(event.getEventId(), updateEventProperties);
        updateEventProperties.setEventName(CommonUtil.checkStringContainsHTML(event.getName()) ? GeneralUtils.removeXSSAttackWordsFromString(event.getName()) : event.getName());

        // Finding Total number fundraising Participant
        Auction auction = auctionService.findByEvent(event);
        Long totalFundRaisingParticipant = Long.valueOf(0);
        if (auction != null) {
            totalFundRaisingParticipant += auctionService.getTotalBidSubmittedUserCount(auction.getId());
        }
        CauseAuction causeAuction = causeAuctionService.findByEvent(event);
        if (causeAuction != null) {
            totalFundRaisingParticipant += pledgeService.countDistinctByCauseAuctionId(causeAuction.getId());
        }
        Raffle raffle = raffleService.findByEvent(event);
        if (raffle != null) {
            totalFundRaisingParticipant += raffleService.getTotalBuyers(raffle.getId());
        }
        updateEventProperties.setNumberOfFundraisingParticipants(totalFundRaisingParticipant);

        // Set Total AE revenue and Billing contact, Billing contact is one of the admin of event
        BillingEvents billingEvents = billingEventsService.findBillingEventsByEventId(event.getEventId());
        if (billingEvents != null) {
            updateEventProperties.setAeRevenue(billingEvents.getTotalAeRevenue());
            updateEventProperties.setPaymentStatus(billingEvents.getPaymentStatus());
            updateEventProperties.setBillingContact(transactionService.getBillingContact(event));
        }

        //Set White label name if exist
        WhiteLabel whiteLabel = event.getWhiteLabel();
        updateEventProperties.setWhiteLabelName(Constants.STRING_EMPTY);
        if (whiteLabel != null) {
            updateEventProperties.setWhiteLabelName(whiteLabel.getFirmName());
        }
    }

    private void setAttendeeUpdateProperties(Long eventId, HubspotUpdateEventProperties updateEventProperties) {
        updateEventProperties.setVirtualAttendees(getCountOfVirtualAttendees(eventId));
        updateEventProperties.setMobileAppAttendees(getCountOfMobileAppAttendees(eventId));
        updateEventProperties.setInPersonAttendees(getCountOfInPersonAttendees(eventId));
        updateEventProperties.setNumberOfAttendees(getCountOfMembersForEvent(eventId));
    }

    @Override
    @Async
    public void addHubspotEventInBatch(List<Event> eventList, Date createdDate, String from, String to) {
        try {
            List<Map<String, HubspotEventProperties>> eventPropertiesList = new ArrayList<>();
            eventList.forEach(event -> {
                Date endDate = eventService.getEventEndDate(event);
                if (event.getCreatedDate().after(createdDate)) {
                    Ticketing ticketing = ticketingService.findByEvent(event);
                    Map<String, HubspotEventProperties> eventPropertiesMap = new HashMap<>();
                    eventPropertiesMap.put(Constants.PROPERTIES, getHubspotEventProperties(event, ticketing.getEventStartDate(), endDate, null));
                    eventPropertiesList.add(eventPropertiesMap);
                }
            });
            Map<String, List<Map<String, HubspotEventProperties>>> batchListMap = new HashMap<>();
            batchListMap.put(Constants.INPUTS, eventPropertiesList);

            HttpEntity<String> createEvents = new HttpEntity<>(mapper.writeValueAsString(batchListMap), hubspotConfiguration.getHubspotHeaders());
            restTemplate.postForObject(hsUrlWithObject + hubspotConfiguration.getEventObjectName() + batchApiKeyParam,
                    createEvents, HubspotEventSearchResponseContainer.class);
        } catch (Exception e) {
            log.error("Error during create event in batch--------->{}, {} ,{}", from, to, e);
        }
    }

    @Override
    @Async
    public void updateAttendeeDetailsForEventInHubspot(Long from, Long to) {

        try {

            List<Object[]> eventList = eventRepoService.getAllEventsByFromAndTo(from, to);
            Map<String, String> objectIdMap = getObjectIdsByEventIdInBatch(eventList.stream().map(e-> Long.valueOf(e[0].toString())).collect(Collectors.toList()));
            List<Map<String, Object>> updateEventList = new ArrayList<>();
            List<BigInteger> auditLogEvents = checkInAuditLogRepoService.getEventIdByCheckInSourceIsNotNullAndTicketIdIsNotNullAndEventId(from, to);
            eventList.forEach(event -> {
                Long eventId = Long.valueOf(event[0].toString());
                try {
                    //Check event are exist in hubspot
                    Date createdDate = (Date) event[3];
                    if (createdDate.after(getDateOfAfterMarch()) && null != objectIdMap.get(eventId.toString())) {
                        Map<String, Object> updateEventPropertiesMap = new HashMap<>();
                        String billingType = event[6].toString();

                        HubspotUpdateEventProperties updateEventProperties = new HubspotUpdateEventProperties();
                        updateEventProperties.setBadgeDesignsCreated(badgesService.countByEventId(eventId));
                        updateEventProperties.setTicketTypesCreated(ticketingTypeService.getTicketTypeCountByEventId(eventId));
                        updateEventProperties.setIsEventComp(BillingType.Comp.name().equalsIgnoreCase(billingType) ? "Yes" : "No");
                        setAttendeeUpdateProperties(eventId, updateEventProperties);

                        if(updateEventProperties.getNumberOfAttendees() < 1 && !auditLogEvents.contains(BigInteger.valueOf(eventId))) {
                            updateEventProperties.setNumberOfAttendees(getStreamService.getCountOfMembersForEvent(eventId));
                        }

                        updateEventPropertiesMap.put(Constants.ID, objectIdMap.get(String.valueOf(eventId)));
                        updateEventPropertiesMap.put(Constants.PROPERTIES, updateEventProperties);
                        updateEventList.add(updateEventPropertiesMap);


                    }
                }catch (Exception e) {
                    log.error("Exception HubspotEventServiceImpl updateAttendeeDetailsForEventInHubspot eventId_ {}",eventId, e);
                }
            });

            updateHubspotEventInBatchApiCall(updateEventList, null, 1);
            log.info("HubspotEventServiceImpl updateAttendeeDetailsForEventInHubspot AE_events update in HS for eventId from {}, to {}", from, to);
            GeneralUtils.threadSleep(1000);

        } catch (Exception e) {
            log.error("Error during update Hubspot event attendees | updateAttendeeDetailsForEventInHubspot.", e);
        }

    }


    @Override
    public void updateEventNameForEventInHubspot(Long from, Long to) {
        try {
            List<Object[]> eventList = eventRepoService.getAllEventsByFromAndTo(from, to);
            Map<String, String> objectIdMap = getObjectIdsByEventIdInBatch(eventList.stream().map(e-> Long.valueOf(e[0].toString())).collect(Collectors.toList()));
            List<Map<String, Object>> updateEventList = new ArrayList<>();
            eventList.forEach(event -> {
                Long eventId = Long.valueOf(event[0].toString());
                try {
                    //Check event are exist in hubspot
                    Date createdDate = (Date) event[3];
                    if (createdDate.after(getDateOfAfterMarch()) && null != objectIdMap.get(eventId.toString())) {
                        Map<String, Object> updateEventPropertiesMap = new HashMap<>();
                        HubspotUpdateEventProperties updateEventProperties = new HubspotUpdateEventProperties();
                        updateEventProperties.setEventName(String.valueOf(event[7]));
                        updateEventPropertiesMap.put(Constants.ID, objectIdMap.get(String.valueOf(eventId)));
                        updateEventPropertiesMap.put(Constants.PROPERTIES, updateEventProperties);
                        updateEventList.add(updateEventPropertiesMap);

                    }
                }catch (Exception e) {
                    log.error("Exception HubspotEventServiceImpl updateAttendeeDetailsForEventInHubspot eventId_ {}",eventId, e);
                }
            });

            updateHubspotEventInBatchApiCall(updateEventList, null, 1);
            log.info("HubspotEventServiceImpl updateAttendeeDetailsForEventInHubspot AE_events update in HS for eventId from {}, to {}", from, to);
            GeneralUtils.threadSleep(1000);

        } catch (Exception e) {
            log.error("Error during update Hubspot event attendees | updateAttendeeDetailsForEventInHubspot.", e);
        }
    }

    @Override
    @Async
    public void updateHubSpotEventByEventIds(List<Long> eventList) {
        try {
            log.info("Hubspot AE events update by eventIds in batch process start. startTime->{}", new Date());

            for (int i = 0; i < eventList.size(); i += 100) {
                List<Long> subList = eventList.subList(i, Math.min(i + 100, eventList.size()));
                Map<String, String> objectIds = getObjectIdsByEventIdInBatch(subList);
                if (CollectionUtils.isEmpty(objectIds)) {
                    continue;
                }
                List<Long> filterSubList = subList.stream().filter(eventId -> null != objectIds.get(String.valueOf(eventId))).collect(Collectors.toList());

                List<Event> eventsFromDB = roEventService.findAllByIds(filterSubList);
                Map<Long, Event> eventMapFromDB = eventsFromDB.stream().collect(Collectors.toMap(Event::getEventId, event -> event));

                List<Map<String, Object>> updateEventList = new ArrayList<>();
                filterSubList.forEach(eventId -> {
                    Map<String, Object> updateEventPropertiesMap = new HashMap<>();
                    // If event deleted then not updated in hubspot
                    if(eventMapFromDB.containsKey(eventId)){
                        Event event = eventMapFromDB.get(eventId);
                        if (StringUtils.isNotBlank(event.getEventURL())) {
                            Date eventEndDate = eventService.getEventEndDate(event);
                            Ticketing ticketing = ticketingService.findByEvent(event);
                            if (null != ticketing) {
                                HubspotUpdateEventProperties updateEventProperties = new HubspotUpdateEventProperties();
                                setHubspotUpdateEventProperties(updateEventProperties, event, ticketing.getEventStartDate(), eventEndDate);
                                updateEventPropertiesMap.put(Constants.ID, objectIds.get(String.valueOf(eventId)));
                                updateEventPropertiesMap.put(Constants.PROPERTIES, updateEventProperties);
                                updateEventList.add(updateEventPropertiesMap);
                            }
                        }
                    }
                });
                updateHubspotEventInBatchApiCall(updateEventList, null, 1);
                log.info("Hubspot AE events updated by eventIds in batch for eventIds-->{}", subList);
                GeneralUtils.threadSleep(1000);
            }
            log.info("Hubspot AE events update by eventIds in batch process end. endTime->{}", new Date());
        } catch (Exception e) {
            log.error("Error during update AE event by eventIds in batch->{}", e.getMessage(), e);
        }
    }



    @Override
    @Async
    public void addAssociationInBatch(List<HubspotAssociationDto> contactList, String from, String to) {
        try {
            if (!contactList.isEmpty()) {
                Map<String, List<HubspotAssociationDto>> associationMap = new HashMap<>();
                associationMap.put(Constants.INPUTS, contactList);

                HttpEntity<String> createAssociation = new HttpEntity<>(mapper.writeValueAsString(associationMap), hubspotConfiguration.getHubspotHeaders());
                restTemplate.postForObject(hubspotConfiguration.getHubspotUrl() + "/associations/" + hubspotConfiguration.getEventObjectName() + "/" + Constants.CONTACT + batchApiKeyParam,
                        createAssociation, String.class);
            }

        } catch (Exception e) {
            log.error("Error during create association in batch--------->{}, {}, {}", from, to, e.getMessage());
        }
    }

    private Map<String, String> getObjectIdsByEventIdInBatchWithRetry(List<Long> eventIds, int retryCount) {
        try {
            List<Object[]> objectIdFromDB = eventRepoService.getHubspotOjectIdsByEventIds(eventIds);
            Map<String, String> finalObjectIdMap = new HashMap<>();

            if (!objectIdFromDB.isEmpty()) {
                Map<String, String> objectIdMap = objectIdFromDB.stream().collect(Collectors.toMap(key -> key[0].toString(), value -> value[1].toString()));
                eventIds = eventIds.stream().filter(e -> !objectIdMap.containsKey(e.toString())).collect(Collectors.toList());
                finalObjectIdMap.putAll(objectIdMap);
            }
            if (eventIds.isEmpty()) {
                return finalObjectIdMap;
            }
            List<HubspotIdDto> hubspotIdDtos = new ArrayList<>();

            eventIds.forEach(eventId -> hubspotIdDtos.add(new HubspotIdDto(String.valueOf(eventId))));

            Map<String, Object> searchMap = new HashMap<>();
            searchMap.put(Constants.INPUTS, hubspotIdDtos);
            searchMap.put(Constants.ID_PROPERTY, Constants.EVENT_ID_UPPERCASE.toLowerCase());

            HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(searchMap), hubspotConfiguration.getHubspotHeaders());

            HubspotEventSearchResponseContainer responseContainer = restTemplate.postForObject(hsUrlWithObject + hubspotConfiguration.getEventObjectName() +
                    "/batch/read?archived=false", request, HubspotEventSearchResponseContainer.class);

            if (!responseContainer.getResults().isEmpty()) { //NOSONAR
                Map<String, String> objectIdsFromHS = responseContainer.getResults().stream().collect(Collectors.toMap(e -> e.getProperties().getEventId(), HubspotEvent::getId));
                // Store Hubspot objectId In batch for Events
                List<Event> updatedEventList = roEventService.findAllByIds(eventIds).stream().map(event -> {
                    if (objectIdsFromHS.containsKey(String.valueOf(event.getEventId()))) {
                        event.setHubspotObjectId(Long.valueOf(objectIdsFromHS.get(String.valueOf(event.getEventId()))));
                    }
                    return event;
                }).collect(Collectors.toList());
                eventRepoService.saveAll(updatedEventList);
                log.info("Get ObjectId from HS and update event for eventIds=> {}", eventIds);
                finalObjectIdMap.putAll(objectIdsFromHS);
            }
            return finalObjectIdMap;
        } catch (ResourceAccessException ex) {
            if (retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                return getObjectIdsByEventIdInBatchWithRetry(eventIds, ++retryCount);
            }
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException e) {
            if ((GeneralUtils.is5xxServerError(e.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(e.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                return getObjectIdsByEventIdInBatchWithRetry(eventIds, ++retryCount);
            }
            log.error("Error during get object Ids by event id in batch eventIds=>{}", eventIds, e);
        } catch (Exception e) {
            log.error("Error during get object Ids by event id in batch={}", eventIds, e);
        }
        return Collections.emptyMap();
    }

    @Override
    public Map<String, String> getObjectIdsByEventIdInBatch(List<Long> eventIds) {
        return getObjectIdsByEventIdInBatchWithRetry(eventIds, 1);
    }

    @Override
    @Async
    public void updateAllEventInBatch() {
        try {
            log.info("Hubspot AE events update in batch process start. startTime->{}", new Date());
            Date startDate = new Date();
            Date endDate = DateUtils.getAddedHours(startDate, -6);

            List<Long> eventIds = eventRepoService.getEventIdsOfUpdatedInDay(startDate, endDate);
            List<Long> failedEvent = trackRepoService.getHubspotEventUpdateStatusIsFailed();
            eventIds.addAll(failedEvent);
            Set<Long> uniqueEventIds = new TreeSet<>(eventIds);
            List<Long> eventsId;
            if(ENV_PROD.equalsIgnoreCase(appProfile)) {
               eventsId = uniqueEventIds.stream().filter(e->e>=14646L).collect(Collectors.toList()); // In Prod HubSpot account don't have events before March 2019, So We remove eventIds from the list which are before March 2019
            } else {
                eventsId = uniqueEventIds.stream().collect(Collectors.toList());
            }

            for (int i = 0; i < eventsId.size(); i += 100) {
                List<Long> subList = eventsId.subList(i, Math.min(i + 100, eventsId.size()));

                // Store event which are update in current batch for tracking
                trackRepoService.saveAll(subList.stream().map(e ->
                                new HubspotEventBatchTrack(e, HubspotEventBatchTrack.ProcessStatus.START, DateUtils.getCurrentDate()))
                        .collect(Collectors.toList()));
                log.info("Hubspot AE events update start for eventIds---------->{}", subList);

                Map<String, String> objectIds = getObjectIdsByEventIdInBatch(subList);
                if (CollectionUtils.isEmpty(objectIds)) {
                    updateHubspotUpdateEventTrackStatus(subList, HubspotEventBatchTrack.ProcessStatus.FAILED);
                    log.info("Hubspot AE events failed to update in batch for eventIds-->{}", subList);
                    continue;
                }
                List<Long> filterSubList = subList.stream().filter(eventId -> null != objectIds.get(String.valueOf(eventId))).collect(Collectors.toList());

                List<Map<String, Object>> updateEventList = new ArrayList<>();
                filterSubList.forEach(eventId -> {
                    Map<String, Object> updateEventPropertiesMap = new HashMap<>();
                    // If event deleted then not updated in hubspot
                    roEventService.findOptionalEventById(eventId).ifPresent(event -> {
                        if (StringUtils.isNotBlank(event.getEventURL())) {
                            Date eventEndDate = eventService.getEventEndDate(event);
                            Ticketing ticketing = ticketingService.findByEvent(event);
                            if (null != ticketing) {
                                HubspotUpdateEventProperties updateEventProperties = new HubspotUpdateEventProperties();
                                setHubspotUpdateEventProperties(updateEventProperties, event, ticketing.getEventStartDate(), eventEndDate);
                                updateEventPropertiesMap.put(Constants.ID, objectIds.get(String.valueOf(eventId)));
                                updateEventPropertiesMap.put(Constants.PROPERTIES, updateEventProperties);
                                updateEventList.add(updateEventPropertiesMap);
                            }
                        }
                    });
                });
                updateHubspotEventInBatchApiCall(updateEventList, subList, 1);
                log.info("Hubspot AE events updated in batch for eventIds-->{}", subList);
                GeneralUtils.threadSleep(1000);
            }
            log.info("Hubspot AE events update in batch process end. endTime->{}", new Date());
        } catch (Exception e) {
            log.error("Error during update all event in batch---------->{}", new Date(), e);
        }
    }

    @Override
    public void updateHubspotEventInBatchApiCall(List<Map<String, Object>> updateEventList, List<Long> eventIds, int retryCount) {
        String errorMsg = "Error during calling hubspot batch update of event. EventIds--->{} ResponseBody ---> {}";
        try {
            log.info("Hubspot AE events update in batch API call start.");
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(Constants.INPUTS, updateEventList);

            HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(updateMap), hubspotConfiguration.getHubspotHeaders());
            restTemplate.postForObject(hsUrlWithObject + hubspotConfiguration.getEventObjectName()
                    + "/batch/update", request, String.class);
            updateHubspotUpdateEventTrackStatus(eventIds, HubspotEventBatchTrack.ProcessStatus.SUCCESS);
            log.info("Hubspot AE events update in batch API successfully called.");
        } catch (ResourceAccessException e) {
            if (retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry during calling hubspot batch update of event. retryCount {} EventIds--->{}", retryCount, eventIds);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                updateHubspotEventInBatchApiCall(updateEventList, eventIds, ++retryCount);
                return;
            }
            updateHubspotUpdateEventTrackStatus(eventIds, HubspotEventBatchTrack.ProcessStatus.FAILED);
            log.error(errorMsg, eventIds, e);
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException e) {
            // Add Logic for retry one time when have Exception from Hubspot server
            if ((GeneralUtils.is5xxServerError(e.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(e.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry during calling hubspot batch update of event. retryCount {} EventIds--->{}", retryCount, eventIds);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                updateHubspotEventInBatchApiCall(updateEventList, eventIds, ++retryCount);
                return;
            }
            updateHubspotUpdateEventTrackStatus(eventIds, HubspotEventBatchTrack.ProcessStatus.FAILED);
            log.error(errorMsg, eventIds, e.getResponseBodyAsString(), e);
        } catch (Exception ex) {
            updateHubspotUpdateEventTrackStatus(eventIds, HubspotEventBatchTrack.ProcessStatus.FAILED);
            log.error(errorMsg, eventIds, ex);
        }
    }

    private void updateHubspotUpdateEventTrackStatus(List<Long> eventIds, HubspotEventBatchTrack.ProcessStatus status) {
        List<HubspotEventBatchTrack> hubspotEventBatchTracks = trackRepoService.getAllUpdatedEventByEventIdAndCreatedAt(eventIds, DateUtils.getAddedHours(new Date(), -6));
        trackRepoService.saveAll(hubspotEventBatchTracks.stream().map(e -> {
            e.setStatus(status);
            e.setEndTime(DateUtils.getCurrentDate());
            return e;
        }).collect(Collectors.toList()));
    }
    public Long getCountOfMembersForEvent(Long eventId) {
        try {
            return checkInAuditLogRepoService.getCountOfAttendeesByCheckInSource(eventId, Arrays.asList(CheckInSource.IN_PERSON.name(), CheckInSource.HYBRID_CHECKIN.name(), CheckInSource.VIRTUAL_EVENT_PORTAL.name()));
        } catch (Exception e) {
            return 0l;
        }
    }

    private Long getCountOfVirtualAttendees(Long eventId) {
        return checkInAuditLogRepoService.getCountOfAttendeesByCheckInSource(eventId, Arrays.asList(CheckInSource.VIRTUAL_EVENT_PORTAL.name()));
    }

    private Long getCountOfMobileAppAttendees(Long eventId) {
        return checkInAuditLogRepoService.getCountOfMobileAppAttendees(eventId);
    }

    private Long getCountOfInPersonAttendees(Long eventId) {
        return checkInAuditLogRepoService.getCountOfAttendeesByCheckInSource(eventId, Arrays.asList(CheckInSource.IN_PERSON.name()));
    }


    @Override
    @Async
    public void associateOrganizerOrWLEventToCompany(Event event) {
        try {
            Organizer organizer = event.getOrganizer();
            WhiteLabel whiteLabel = event.getWhiteLabel();
            String hsOrganizerCompanyId = Constants.STRING_EMPTY;
            if (organizer != null && organizer.getHubSpotCompanyId() != null) {
                hsOrganizerCompanyId = organizer.getHubSpotCompanyId().toString();
            }
            String hubspotEventId = event.getHubspotObjectId() != null ? String.valueOf(event.getHubspotObjectId()) :
                    getEventByEventId(event.getEventId());
            if (StringUtils.isBlank(hubspotEventId)) {
                log.info("Event not found on Hubspot for eventId=>{}", event.getEventId());
                return;
            }
            addEventToCompaniesMappingForOrganizerOrWL(hubspotEventId, hsOrganizerCompanyId, whiteLabel, event.getEventId());
        } catch (Exception e) {
            log.error("Error during creating association between Company to AEevent. EventId=>{}", event.getEventId(), e);
        }
    }

    @Override
    public void addEventToCompaniesMappingForOrganizerOrWL(String hubspotEventId, String hsOrganizerCompanyId, WhiteLabel whiteLabel, long eventId) {
        String hsWhiteLabelCompanyId = Constants.STRING_EMPTY;
        if(whiteLabel != null && whiteLabel.getHubspotCompanyId() !=null) {
            hsWhiteLabelCompanyId = whiteLabel.getHubspotCompanyId().toString();
        }
        if(StringUtils.isNotBlank(hsOrganizerCompanyId) && StringUtils.isNotBlank(hsWhiteLabelCompanyId)) {
            // Organizer And WhiteLabel Event To Company
            associateOrganizerOrWLCompanyToEventsByPlan(hubspotEventId, hsOrganizerCompanyId, whiteLabel, hsWhiteLabelCompanyId, hsOrganizerCompanyId.equals(hsWhiteLabelCompanyId));
        } else {
            associateOrganizerOrWLCompanyToEventsByPlan(hubspotEventId, hsOrganizerCompanyId, whiteLabel, hsWhiteLabelCompanyId, false);
        }
        log.info("Association created between Company to AEevent for organizer/WhiteLabel. Organizer CompanyId {} WhiteLabel CompanyId {}, HubspotEventId {}, EventId {}", hsOrganizerCompanyId, hsWhiteLabelCompanyId, hubspotEventId, eventId);
    }

    private void associateOrganizerOrWLCompanyToEventsByPlan(String hubspotEventId, String hsOrganizerCompanyId, WhiteLabel whiteLabel, String hsWhiteLabelCompanyId, boolean isSameCompanyId) {
        //Only For WhiteLabel Plan
        if(whiteLabel != null) {
            if (ENTERPRISE.getName().equalsIgnoreCase(whiteLabel.getPlanConfig().getPlanName())
            || ENTERPRISE_PLAN_2023.getName().equalsIgnoreCase(whiteLabel.getPlanConfig().getPlanName())) {
                List<String> associationsLabel = isSameCompanyId ? Arrays.asList(Constants.HS_ORGANIZER_EVENTS_LABEL ,Constants.HS_ENTERPRISE_ACCOUNT_LABEL) : Arrays.asList(Constants.HS_ENTERPRISE_ACCOUNT_LABEL);
                hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.COMPANY, hubspotEventId, hsWhiteLabelCompanyId, associationsLabel);
            } else if (WHITE_LABEL_PLAN.getName().equalsIgnoreCase(whiteLabel.getPlanConfig().getPlanName())
                    || WHITELABEL_PLAN_2023.getName().equalsIgnoreCase(whiteLabel.getPlanConfig().getPlanName())) {
                List<String> associationsLabel = isSameCompanyId ? Arrays.asList(Constants.HS_ORGANIZER_EVENTS_LABEL ,Constants.HS_WL_ACCOUNT_LABEL) : Arrays.asList(Constants.HS_WL_ACCOUNT_LABEL);
                hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.COMPANY, hubspotEventId, hsWhiteLabelCompanyId, associationsLabel);
            }
        }
        if(!isSameCompanyId) {
            //Organizer Event To Company
            hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.COMPANY, hubspotEventId, hsOrganizerCompanyId, Arrays.asList(Constants.HS_ORGANIZER_EVENTS_LABEL));
        }
    }

    @Override
    @Async
    public void deleteAssociationOrganizerOrWLEventToCompanyInBatch(List<Long> eventIds, Long hubspotCompanyId, WhiteLabel whiteLabel, boolean deleteAllAssociation) {
        try {
            List<String> objectIds = new ArrayList<>();
            getObjectIdsByEventIdInBatchWithRetry(eventIds, 1).forEach((eventId, objectId) -> {
                if (StringUtils.isNotBlank(objectId)) {
                    objectIds.add(objectId);
                }
            });

            String wlPlanName = whiteLabel != null ? whiteLabel.getPlanConfig().getPlanName() : Constants.STRING_EMPTY;
            List<String> associationsLabel = new ArrayList<>();
            if (ENTERPRISE.getName().equalsIgnoreCase(wlPlanName) || ENTERPRISE_PLAN_2023.getName().equalsIgnoreCase(wlPlanName)) {
                associationsLabel.add(Constants.HS_ENTERPRISE_ACCOUNT_LABEL);
            } else if (WHITE_LABEL_PLAN.getName().equalsIgnoreCase(wlPlanName) || WHITELABEL_PLAN_2023.getName().equalsIgnoreCase(wlPlanName)) {
                associationsLabel.add(Constants.HS_WL_ACCOUNT_LABEL);
            }

            if(!objectIds.isEmpty() && StringUtils.isNotBlank(wlPlanName) && null != whiteLabel) {
                if(deleteAllAssociation) {
                    hubspotCustomLabelsAssociationService.executeDeleteAssociationObjectInBatch(hubspotConfiguration.getEventObjectName(), Constants.COMPANY, objectIds, hubspotCompanyId.toString());
                    log.info("Association deleted between all AE events and HS company for whiteLabel {} HubSpotCompanyId {}", whiteLabel.getId(), hubspotCompanyId);
                } else {
                    hubspotCustomLabelsAssociationService.executeDeleteAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.COMPANY, objectIds, hubspotCompanyId.toString(), associationsLabel);
                    log.info("Association deleted between all AE events and HS company for whiteLabel {} HubSpotCompanyId {} and association Label {}", whiteLabel.getId(), hubspotCompanyId, associationsLabel);
                }
            }
        } catch (Exception e) {
            log.error("Error during delete association between Company to AEevent. HubspotCompanyId=>{}  EventIds=>{}", hubspotCompanyId, eventIds, e);
        }
    }

    private Date getDateOfAfterMarch() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(2020, 02, 01, 00, 00, 00);
        calendar.getTime();
        return calendar.getTime();
    }

    @Override
    public void associateWhiteLabelEventToCompanyInBatch(WhiteLabel whiteLabel) {
        try {
            if (whiteLabel != null && whiteLabel.getHubspotCompanyId() != null) {
                List<Object[]> eventList = eventRepoService.getAllEventsByWhiteLabelIdAndCreatedDate(whiteLabel.getId(), getDateOfAfterMarch());
                Long hsCompanyId = whiteLabel.getHubspotCompanyId();

                if (!isCompanyExistInHubspot(hsCompanyId.toString())) {
                    throw new NotFoundException(NotFoundException.NotFound.HUBSPOT_COMPANY_NOT_FOUND);
                }

                eventList.forEach(e -> {
                    String hsOrgCompanyId= e[3] != null ? String.valueOf(e[3]) : Constants.STRING_EMPTY;
                    String hubspotEventId = e[2] != null ? String.valueOf(e[2]) :
                            getEventByEventId(Long.parseLong(e[0].toString()));
                    addEventToCompaniesMappingForOrganizerOrWL(hubspotEventId, hsOrgCompanyId, whiteLabel, Long.parseLong(e[0].toString()));
                });
                log.info("Association created between all AE events and HS company for whiteLabel {}", whiteLabel.getId());

            } else {
                throw new NotFoundException(NotFoundException.NotFound.HUBSPOT_COMPANY_NOT_FOUND);
            }
        } catch (Exception e) {
            throw new ThirdPartyExceptions(500, e.getMessage());
        }
    }


    @Override
    public void associateOrganizerEventToCompanyInBatch(Organizer organizer) {
        try {
            if (organizer != null && organizer.getHubSpotCompanyId() != null) {
                List<Object[]> eventList = eventRepoService.getAllEventsByOrganizerIdAndCreatedDate(organizer.getId(), getDateOfAfterMarch());
                Long hsCompanyId = organizer.getHubSpotCompanyId();

                if (!isCompanyExistInHubspot(hsCompanyId.toString())) {
                    throw new NotFoundException(NotFoundException.NotFound.HUBSPOT_COMPANY_NOT_FOUND);
                }
                WhiteLabel whiteLabel = organizer.getWhiteLabel();
                eventList.forEach(e -> {
                    String hubspotEventId = e[2] != null ? String.valueOf(e[2]) :
                            getEventByEventId(Long.parseLong(e[0].toString()));
                    addEventToCompaniesMappingForOrganizerOrWL(hubspotEventId, hsCompanyId.toString(), whiteLabel, Long.parseLong(e[0].toString()));
                });
                log.info("Association created between all AE events and HS company for organizer {}", organizer.getId());

            } else {
                throw new NotFoundException(NotFoundException.NotFound.HUBSPOT_COMPANY_NOT_FOUND);
            }
        } catch (Exception e) {
            throw new ThirdPartyExceptions(500, e.getMessage());
        }
    }


    @Override
    public boolean isCompanyExistInHubspot(String companyId) {

        if (StringUtils.isNotBlank(companyId)) {
            try {
                HttpResponse<String> stringHttpResponse = Unirest.get(hsUrlWithObject + "companies/" + companyId)
                        .headers(hubspotConfiguration.getHubspotHeaders().toSingleValueMap())
                        .asString();
                if (stringHttpResponse.getStatus() == 200 && null != stringHttpResponse.getBody()) {
                    return true;
                } else if (stringHttpResponse.getStatus() == 404) {
                    return false;
                }
            } catch (UnirestException e) {
                log.error("Exception during getting company by company id {} exception is {}.", companyId, e);
            }
        }
        return false;
    }

    @Override
    public void deleteHubspotEventByEventId(Long eventId, int retryCount) {
        try {
            String objectId = getEventByEventId(eventId);
            if (StringUtils.isNotBlank(objectId)) {
                Map<String, String> uriParam = new HashMap<>();
                uriParam.put(Constants.OBJECT_ID, objectId);
                HttpEntity<?> request = new HttpEntity<>(hubspotConfiguration.getHubspotHeaders());
                restTemplate.exchange(hsUrlWithObject + hubspotConfiguration.getEventObjectName() + "/{objectId}",HttpMethod.DELETE, request, String.class, uriParam);
                log.info("Hubspot Event deleted successfully, eventId=>{}", eventId);
            }
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException ex) {
            if ((GeneralUtils.is5xxServerError(ex.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(ex.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                deleteHubspotEventByEventId(eventId, ++retryCount);
                return;
            }
            log.error("HubspotEventServiceImpl | deleteHubspotEventByEventId | Hubspot Event not deleted for eventId=>{}", eventId, ex);
        } catch (Exception e) {
            log.error("Hubspot Event not deleted for eventId=>{}", eventId, e);
        }
    }
}
