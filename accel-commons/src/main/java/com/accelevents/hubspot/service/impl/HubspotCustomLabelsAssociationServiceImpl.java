package com.accelevents.hubspot.service.impl;

import com.accelevents.configuration.HubspotConfiguration;
import com.accelevents.hubspot.dto.HubspotCustomAssociationLabelsDto;
import com.accelevents.hubspot.service.HubspotCustomLabelsAssociationService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.*;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.accelevents.utils.Constants.*;

@Service
public class HubspotCustomLabelsAssociationServiceImpl implements HubspotCustomLabelsAssociationService {

    private static final Logger log = LoggerFactory.getLogger(HubspotCustomLabelsAssociationServiceImpl.class);

    @Autowired
    private HubspotConfiguration hubspotConfiguration;

    @Autowired
    private RestTemplate restTemplate;

    private final ObjectMapper mapper = new ObjectMapper();

    private static final Map<String, Integer> customAssociationLabelToTypeIdMap = new HashMap<>();

    private static final String ASSOCIATION_POSTFIX_URL = "{objectType}/{objectId}/associations/{toObjectType}/{toObjectId}";

    @PostConstruct
    public void init() {
        getAllCustomAssociationLabelsWithTypeId();
    }

    private void getAllCustomAssociationLabelsWithTypeId() {
        getAssociationsCustomLabels(Constants.CONTACT, hubspotConfiguration.getOrganizerObjectName(), 1);
        getAssociationsCustomLabels(Constants.COMPANY, hubspotConfiguration.getOrganizerObjectName(), 1);
        getAssociationsCustomLabels(hubspotConfiguration.getEventObjectName(), hubspotConfiguration.getOrganizerObjectName(), 1);
        getAssociationsCustomLabels(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, 1);
        getAssociationsCustomLabels(hubspotConfiguration.getEventObjectName(), Constants.COMPANY, 1);
        getAssociationsCustomLabels(Constants.COMPANY,Constants.CONTACT, 1);
    }

    private void getAssociationsCustomLabels(String fromObjectType, String toObjectType, int retryCount) {
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .get(hubspotConfiguration.getHubspotUrlV4() + Constants.ASSOCIATIONS + fromObjectType + "/" + toObjectType + "/labels")
                    .headers(hubspotConfiguration.getHubspotHeaders().toSingleValueMap())
                    .asJson();
            if (jsonResponse.getStatus() == 200 && null != jsonResponse.getBody().getObject()) {
                JSONArray results = jsonResponse.getBody().getObject().getJSONArray("results");
                if (results.length() > 0) {
                    for (int i = 0; i < results.length(); i++) {
                        JSONObject jsonObject = results.getJSONObject(i);
                        if (jsonObject.has("typeId") && jsonObject.has("label") && Constants.USER_DEFINED.equals(jsonObject.get("category"))) {
                            String key = fromObjectType + "_" + toObjectType + "_" + jsonObject.get("label");
                            customAssociationLabelToTypeIdMap.put(key, jsonObject.getInt("typeId"));
                        }
                    }
                }
            } else if ((GeneralUtils.is5xxServerError(jsonResponse.getStatus()) || GeneralUtils.isTooManyRequestError(jsonResponse.getStatus())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry for getAssociationsCustomLabels fromObjectType {}  toObjectType {} retryCount {}", fromObjectType, toObjectType, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                getAssociationsCustomLabels(fromObjectType, toObjectType, ++retryCount);
            } else {
                log.info("Error during getAssociationsCustomLabels fromObjectType {}  toObjectType {} JsonRes {}.", fromObjectType, toObjectType, jsonResponse.getStatusText());
            }
        } catch (UnirestException | JSONException e) {
            log.error("Exception during getAssociationsCustomLabels fromObjectType {}  toObjectType {} exception {}.", fromObjectType, toObjectType, e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void executeCreateAssociationObjectWithCustomLabel(String fromObjectType, String toObjectType, String fromObjectId, String toObjectId, String label) {
        executeCreateAssociationObjectWithCustomLabelWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, label, 1);
    }


    private void executeCreateAssociationObjectWithCustomLabelWithRetry(String fromObjectType, String toObjectType, String fromObjectId, String toObjectId, String label, int retryCount) {
        String key = fromObjectType + "_" + toObjectType + "_" + label;
        try {
            log.info("Starting for creating association for fromObjectId {} , toObjectId {} , label {} ", fromObjectId, toObjectId, key);
            Integer customLabelTypeId = customAssociationLabelToTypeIdMap.get(key);
            if (CommonUtil.isNotNullOrBlank(fromObjectId) && CommonUtil.isNotNullOrBlank(toObjectId) && null != customLabelTypeId) {
                HttpResponse<JsonNode> jsonResponse = Unirest.put(hubspotConfiguration.getHubspotUrlV4() + Constants.OBJECTS +
                                ASSOCIATION_POSTFIX_URL)
                        .routeParam(Constants.OBJECT_TYPE, fromObjectType)
                        .routeParam(Constants.OBJECT_ID, fromObjectId)
                        .routeParam(Constants.TO_OBJECT_TYPE, toObjectType)
                        .routeParam(Constants.TO_OBJECT_ID, toObjectId)
                        .headers(hubspotConfiguration.getHubspotHeaders().toSingleValueMap()).body(mapper.writeValueAsString(new JSONArray().put(new HubspotCustomAssociationLabelsDto(customLabelTypeId))))
                        .asJson();
                if (201 == jsonResponse.getStatus()) {
                    log.info("Association created in hubspot fromObjectId {} , toObjectId {} and association {} and association label id {}", fromObjectId, toObjectId, key, customLabelTypeId);
                    return;
                } else if ((GeneralUtils.is5xxServerError(jsonResponse.getStatus()) || GeneralUtils.isTooManyRequestError(jsonResponse.getStatus())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                    log.info("Retry for creating Association in hubspot fromObjectId {} , toObjectId {} and association {} retryCount {}", fromObjectId, toObjectId, key, retryCount);
                        GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                        executeCreateAssociationObjectWithCustomLabelWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, label, ++retryCount);
                        return;
                }
                log.info("Error during creating association in hubspot fromObjectId {}, toObjectId {} and association {} response {}", fromObjectId, toObjectId, key, jsonResponse.getBody());
            } else {
                log.info("Association not created in hubspot fromObjectId {}, toObjectId {} and association {} ", fromObjectId, toObjectId, key);
            }
        } catch (UnirestException | JsonProcessingException e) {
            log.error("Error during creating association in hubspot fromObjectId {}, toObjectId {} and association {} exception {}", fromObjectId, toObjectId, key, e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void executeCreateAssociationObjectWithCustomLabelInBatch(String fromObjectType, String toObjectType, String fromObjectId, String toObjectId, List<String> labels) {
        executeCreateAssociationObjectWithCustomLabelInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, labels, 1);
    }


    private void executeCreateAssociationObjectWithCustomLabelInBatchWithRetry(String fromObjectType, String toObjectType, String fromObjectId, String toObjectId, List<String> labels, int retryCount) {
        String errorMsg = "Exception during creating Association in batch with custom label in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} exception {}";
        try {
            if (CommonUtil.isNotNullOrBlank(fromObjectId) && CommonUtil.isNotNullOrBlank(toObjectId) && !labels.isEmpty()) {
                log.info("Starting for creating association in batch with custom label for fromObjectType {} , toObjectType {} ,fromObjectId {} ,toObjectId {} and labels {} ", fromObjectType, toObjectType, fromObjectId, toObjectId, labels);
                List<HubspotCustomAssociationLabelsDto> customLabelsList = new ArrayList<>();
                labels.forEach(label -> {
                    String key = fromObjectType + "_" + toObjectType + "_" + label;
                    Integer customLabelTypeId = customAssociationLabelToTypeIdMap.get(key);
                    if (null != customLabelTypeId)
                        customLabelsList.add(new HubspotCustomAssociationLabelsDto(customLabelTypeId));
                });

                if (customLabelsList.isEmpty()) {
                    log.info("CustomLabelTypeIds not found for fromObjectType {} , toObjectType {} ,fromObjectId {} ,toObjectId {} and labels {} ", fromObjectType, toObjectType, fromObjectId, toObjectId, labels);
                    return;
                }

                Map<String, Object> fromObjectIdMap = new HashMap<>();
                fromObjectIdMap.put(Constants.ID, fromObjectId);

                Map<String, Object> toObjectTdMap = new HashMap<>();
                toObjectTdMap.put(Constants.ID, toObjectId);

                Map<String, Object> combinedAllFieldMap = new HashMap<>();
                combinedAllFieldMap.put(TYPES, customLabelsList);
                combinedAllFieldMap.put(FROM, fromObjectIdMap);
                combinedAllFieldMap.put(TO, toObjectTdMap);

                List<Map<String, Object>> finalInputList = new ArrayList<>();
                finalInputList.add(combinedAllFieldMap);

                Map<String, Object> finalInputMap = new HashMap<>();
                finalInputMap.put(Constants.INPUTS, finalInputList);

                HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(finalInputMap), hubspotConfiguration.getHubspotHeaders());
                String url = hubspotConfiguration.getHubspotUrlV4() + Constants.ASSOCIATIONS + fromObjectType + "/" + toObjectType + "/batch/create";
                restTemplate.exchange(url, HttpMethod.POST, request, String.class);
                log.info("Association batch created in hubspot with custom label fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels);
            } else {
                log.info("Association batch not created in hubspot with custom label fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels);
            }
        } catch (ResourceAccessException e) {
            if (retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry for creating Association in batch with custom label in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} retryCount {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeCreateAssociationObjectWithCustomLabelInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, labels, ++retryCount);
                return;
            }
            log.info(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, labels, e.getMessage(), e);
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException ex) {
            // Add Logic for retry one time when have Exception from Hubspot server
            if ((GeneralUtils.is5xxServerError(ex.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(ex.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry for creating Association in batch with custom label in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} retryCount {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeCreateAssociationObjectWithCustomLabelInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, labels, ++retryCount);
                return;
            }
            log.info(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, labels, ex.getMessage(), ex);
        } catch (Exception e) {
            log.error(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, labels, e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void executeDeleteAssociation(String fromObjectType, String toObjectType, String fromObjectId, String toObjectId) {
        executeDeleteAssociationWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, 1);
    }

    private void executeDeleteAssociationWithRetry(String fromObjectType, String toObjectType, String fromObjectId, String toObjectId, int retryCount) {
        try {
            log.info("Starting for deleting association for fromObjectId {} , toObjectId {} , fromObjectType {} , toObjectType {} ", fromObjectId, toObjectId, fromObjectType, toObjectType);
            if (CommonUtil.isNotNullOrBlank(fromObjectId) && CommonUtil.isNotNullOrBlank(toObjectId)) {
                HttpResponse<JsonNode> jsonResponse = Unirest.delete(hubspotConfiguration.getHubspotUrlV4() + Constants.OBJECTS + ASSOCIATION_POSTFIX_URL)
                        .routeParam(Constants.OBJECT_TYPE, fromObjectType)
                        .routeParam(Constants.OBJECT_ID, fromObjectId)
                        .routeParam(Constants.TO_OBJECT_TYPE, toObjectType)
                        .routeParam(Constants.TO_OBJECT_ID, toObjectId)
                        .headers(hubspotConfiguration.getHubspotHeaders().toSingleValueMap()).asJson();
                if (jsonResponse.getStatus() == 204) {
                    log.info("Association deleted in hubspot fromObjectId {} , toObjectId {} , fromObjectType {} , toObjectType {} ", fromObjectId, toObjectId, fromObjectType, toObjectType);
                    return;
                } else if ((GeneralUtils.is5xxServerError(jsonResponse.getStatus()) || GeneralUtils.isTooManyRequestError(jsonResponse.getStatus())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                    log.info("Retry for deleting Association in hubspot fromObjectId {} , toObjectId {} , fromObjectType {} , toObjectType {} retryCount {}", fromObjectId, toObjectId, fromObjectType, toObjectType, retryCount);
                    GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                    executeDeleteAssociationWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, ++retryCount);
                    return;
                }
                log.info("Exception during deleting association in hubspot fromObjectId {}, toObjectId {} , fromObjectType {} , toObjectType {} response {}", fromObjectId, toObjectId, fromObjectType, toObjectType, jsonResponse.getBody());
            } else {
                log.info("Association not deleted in hubspot fromObjectId {}, toObjectId {} , fromObjectType {} , toObjectType {} ", fromObjectId, toObjectId, fromObjectType, toObjectType);
            }
        } catch (UnirestException e) {
            log.error("Exception during deleting association in hubspot fromObjectId {}, toObjectId {} , fromObjectType {} , toObjectType {} exception {}", fromObjectId, toObjectId, fromObjectType, toObjectType, e.getMessage(), e);
        }
    }

    @Override
    public void executeDeleteAssociationWithoutAsync(String fromObjectType, String toObjectType, String fromObjectId, String toObjectId) {
        executeDeleteAssociationWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, 1);
    }

    @Async
    @Override
    public void executeDeleteAssociationObjectWithCustomLabelInBatch(String fromObjectType, String toObjectType, List<String> fromObjectId, String toObjectId, List<String> labels) {
        executeDeleteAssociationObjectWithCustomLabelInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, labels, 1);
    }


    private void executeDeleteAssociationObjectWithCustomLabelInBatchWithRetry(String fromObjectType, String toObjectType, List<String> fromObjectId, String toObjectId, List<String> labels, int retryCount) {
        String errorMsg = "Exception during deleting Association in batch with custom label in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} exception {}";
        try {
            if (!CollectionUtils.isEmpty(fromObjectId) && CommonUtil.isNotNullOrBlank(toObjectId) && !labels.isEmpty()) {
                log.info("Starting for deleting association in batch with Custom label for fromObjectType {} , toObjectType {} ,fromObjectId {} ,toObjectId {} and labels {} ", fromObjectType, toObjectType, fromObjectId, toObjectId, labels);
                List<HubspotCustomAssociationLabelsDto> customLabelsList = new ArrayList<>();
                labels.forEach(label -> {
                    String key = fromObjectType + STRING_UNDERSCORE + toObjectType + STRING_UNDERSCORE + label;
                    Integer customLabelTypeId = customAssociationLabelToTypeIdMap.get(key);
                    if (null != customLabelTypeId)
                        customLabelsList.add(new HubspotCustomAssociationLabelsDto(customLabelTypeId));
                });

                if (customLabelsList.isEmpty()) {
                    log.info("CustomLabelTypeIds not found for fromObjectType {} , toObjectType {} ,fromObjectId {} ,toObjectId {} and labels {} ", fromObjectType, toObjectType, fromObjectId, toObjectId, labels);
                    return;
                }

                Map<String, Object> toObjectTdMap = new HashMap<>();
                toObjectTdMap.put(Constants.ID, toObjectId);

                List<Map<String, Object>> finalInputList = new ArrayList<>();

                for(String fromId : fromObjectId) {
                    Map<String, Object> fromObjectIdMapTemp = new HashMap<>();
                    fromObjectIdMapTemp.put(Constants.ID, fromId);
                    Map<String, Object> combinedAllFieldMapTemp = new HashMap<>();
                    combinedAllFieldMapTemp.put(Constants.TYPES, customLabelsList);
                    combinedAllFieldMapTemp.put(Constants.FROM, fromObjectIdMapTemp);
                    combinedAllFieldMapTemp.put(Constants.TO, toObjectTdMap);
                    finalInputList.add(combinedAllFieldMapTemp);
                }

                Map<String, Object> finalInputMap = new HashMap<>();
                finalInputMap.put(Constants.INPUTS, finalInputList);

                HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(finalInputMap), hubspotConfiguration.getHubspotHeaders());
                String url = hubspotConfiguration.getHubspotUrlV4() + Constants.ASSOCIATIONS + fromObjectType + "/" + toObjectType + "/batch/labels/archive";
                ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
                String responseBody = responseEntity.getBody();
                if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                    log.info("Association deleting batch with custom label is not completed in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} responseBody {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels, responseBody);
                } else {
                    log.info("Association deleted in batch with custom label completed from hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} responseBody {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels, responseBody);
                }
            } else {
                log.info("Association deleting batch with custom label is not completed in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels);
            }
        } catch (ResourceAccessException e) {
            if (retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry for deleting Association in batch with custom label in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} retryCount {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeDeleteAssociationObjectWithCustomLabelInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, labels, ++retryCount);
                return;
            }
            log.info(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, labels, e.getMessage(), e);
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException ex) {
            // Add Logic for retry one time when have Exception from Hubspot server
            if ((GeneralUtils.is5xxServerError(ex.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(ex.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry for deleting Association in batch with custom label in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and labels {} retryCount {}", fromObjectId, toObjectId, fromObjectType, toObjectType, labels, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeDeleteAssociationObjectWithCustomLabelInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, labels, ++retryCount);
                return;
            }
            log.info(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, labels, ex.getMessage(), ex);
        }
        catch (Exception e) {
            log.error(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, labels, e.getMessage(), e);
        }
    }

    @Async
    @Override
    public void executeDeleteAssociationObjectInBatch(String fromObjectType, String toObjectType, List<String> fromObjectId, String toObjectId) {
        executeDeleteAssociationObjectInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, 1);
    }

    private void executeDeleteAssociationObjectInBatchWithRetry(String fromObjectType, String toObjectType, List<String> fromObjectId, String toObjectId, int retryCount) {
        String errorMsg = "Exception during deleting Association in batch for hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and exception {}";
        try {
            if (!CollectionUtils.isEmpty(fromObjectId) && CommonUtil.isNotNullOrBlank(toObjectId)) {
                log.info("Starting for deleting association in batch for fromObjectType {} , toObjectType {} ,fromObjectId {} ,toObjectId {}", fromObjectType, toObjectType, fromObjectId, toObjectId);

                Map<String, String> toObjectTdMap = new HashMap<>();
                toObjectTdMap.put(Constants.ID, toObjectId);
                List<Map<String, String>> toObjectidList = List.of(toObjectTdMap);

                List<Map<String, Object>> finalInputList = new ArrayList<>();

                for(String fromId : fromObjectId) {
                    Map<String, String> fromObjectIdMapTemp = new HashMap<>();
                    fromObjectIdMapTemp.put(Constants.ID, fromId);
                    Map<String, Object> combinedAllFieldMapTemp = new HashMap<>();
                    combinedAllFieldMapTemp.put(Constants.FROM, fromObjectIdMapTemp);
                    combinedAllFieldMapTemp.put(Constants.TO, toObjectidList);
                    finalInputList.add(combinedAllFieldMapTemp);
                }

                Map<String, Object> finalInputMap = new HashMap<>();
                finalInputMap.put(Constants.INPUTS, finalInputList);

                HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(finalInputMap), hubspotConfiguration.getHubspotHeaders());
                String url = hubspotConfiguration.getHubspotUrlV4() + Constants.ASSOCIATIONS + fromObjectType + "/" + toObjectType + "/batch/archive";
                ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
                String responseBody = responseEntity.getBody();
                if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                    log.info("Association batch not deleted in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and responseBody {}", fromObjectId, toObjectId, fromObjectType, toObjectType, responseBody);
                } else {
                    log.info("Association deleted in batch from hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and responseBody {}", fromObjectId, toObjectId, fromObjectType, toObjectType, responseBody);
                }
            } else {
                log.info("Association batch not deleted in hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {}", fromObjectId, toObjectId, fromObjectType, toObjectType);
            }
        } catch (ResourceAccessException ex) {
            if (retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry for deleting Association in batch for hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and retryCount {}", fromObjectId, toObjectId, fromObjectType, toObjectType, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeDeleteAssociationObjectInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, ++retryCount);
                return;
            }
            log.info(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, ex.getMessage(), ex);
        } catch (HttpServerErrorException | HttpClientErrorException | UnknownHttpStatusCodeException ex) {
            // Add Logic for retry one time when have Exception from Hubspot server
            if ((GeneralUtils.is5xxServerError(ex.getRawStatusCode()) || GeneralUtils.isTooManyRequestError(ex.getRawStatusCode())) && retryCount <= HUBSPOT_MAX_RETRY_COUNT) {
                log.info("Retry for deleting Association in batch for hubspot fromObjectId {} , toObjectId {} and fromObjectType {} , toObjectType {} and retryCount {}", fromObjectId, toObjectId, fromObjectType, toObjectType, retryCount);
                GeneralUtils.threadSleep(MAX_WAIT_MILLISECONDS * retryCount);
                executeDeleteAssociationObjectInBatchWithRetry(fromObjectType, toObjectType, fromObjectId, toObjectId, ++retryCount);
                return;
            }
            log.info(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, ex.getMessage(), ex);
        } catch (Exception e) {
            log.error(errorMsg, fromObjectId, toObjectId, fromObjectType, toObjectType, e.getMessage(), e);
        }
    }
}
