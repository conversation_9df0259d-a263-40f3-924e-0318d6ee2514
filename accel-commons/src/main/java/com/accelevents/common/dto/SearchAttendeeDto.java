package com.accelevents.common.dto;

import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Size;

import java.util.Map;

import static com.accelevents.utils.ApiMessages.*;

@Schema(description ="SearchAttendeeDto")
public class SearchAttendeeDto {

    @Schema(description =FULL_NAME_DESC)
    @Size(max = Constants.FULL_NAME_SIZE, message = Constants.FULL_NAME_SIZE_MSG)
    private String fullName;

    @Schema(description =FIRST_NAME_DESC)
    @Size(max = Constants.NAME_SIZE, message = Constants.FIRST_NAME_SIZE_MSG)
    private String firstName;

    @Schema(description =LAST_NAME_DESC)
    @Size(max = Constants.NAME_SIZE, message = Constants.LAST_NAME_SIZE_MSG)
    private String lastName;

    @Schema(description ="Ticket Number")
    private  Long  ticketNumber;

    @Schema(description ="Email")
    private String email;

    @Schema(description ="Phone Number")
    private Long phoneNumber;

    private String source;

    private String sourceDescription;

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Long getTicketNumber() {
        return ticketNumber;
    }

    public void setTicketNumber(Long ticketNumber) {
        this.ticketNumber = ticketNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public Long getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(Long phoneNumber) { this.phoneNumber = phoneNumber; }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSourceDescription() {
        return sourceDescription;
    }

    public void setSourceDescription(String sourceDescription) {
        this.sourceDescription = sourceDescription;
    }
}
