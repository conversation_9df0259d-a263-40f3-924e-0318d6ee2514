package com.accelevents.common.dto;

import com.accelevents.constraint.CheckDateFormat;
import com.accelevents.domain.enums.TaskType;
import com.accelevents.domain.enums.TaskVerificationType;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Size;
import java.util.Date;

import static com.accelevents.utils.Constants.DATE_FORMAT;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;

public class EventTaskListDto {

    @Schema(description = "Task ID")
    private Long id;

    @Schema(description = "Task type, such as SPEAKER or SESSION")
    private TaskType taskType;

    @Schema(description = "Title of the task")
    @Size(max = 255, message = "Title cannot exceed 255 characters.")
    private String title;

    @Schema(description = "Number of assignees to whom task has assigned")
    private Long taskAssignedToCount;

    @Schema(description = "Number of assignees who have completed the task")
    private Long taskCompletedByAssigneesCount;

    @Schema(description = "Submission due date for the task in dd/MM/yyyy HH:mm:ss format")
    @CheckDateFormat(pattern = "dd/MM/yyyy HH:mm:ss", message = "Please enter date in dd/MM/yyyy HH:mm:ss format")
    private Date submissionDueDate;

    @Schema(description = "Indicates if the task allows publication")
    private boolean allowPublish;

    @Schema(description = "Indicates if the task allows draft")
    private boolean allowDraft;

    @Schema(description = "Task Verification Type , Manual Verification, System Verification")
    private TaskVerificationType taskVerificationType;

    public EventTaskListDto(Long id, TaskType taskType, String title,Long taskAssignedToCount,
                            Long taskCompletedByAssigneesCount, Date submissionDueDate, boolean allowPublish, boolean allowDraft, TaskVerificationType taskVerificationType) {
        this.id = id;
        this.taskType = taskType;
        this.title = title;
        this.taskAssignedToCount = taskAssignedToCount;
        this.taskCompletedByAssigneesCount = taskCompletedByAssigneesCount;
        this.submissionDueDate = submissionDueDate;
        this.allowPublish = allowPublish;
        this.allowDraft = allowDraft;
        this.taskVerificationType = taskVerificationType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(TaskType taskType) {
        this.taskType = taskType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getTaskAssignedToCount() {
        return taskAssignedToCount;
    }

    public void setTaskAssignedToCount(Long taskAssignedToCount) {
        this.taskAssignedToCount = taskAssignedToCount;
    }

    public Long getTaskCompletedByAssigneesCount() {
        return taskCompletedByAssigneesCount;
    }

    public void setTaskCompletedByAssigneesCount(Long taskCompletedByAssigneesCount) {
        this.taskCompletedByAssigneesCount = taskCompletedByAssigneesCount;
    }

    public Date getSubmissionDueDate() {
        return submissionDueDate;
    }

    public void setSubmissionDueDate(Date submissionDueDate) {
        this.submissionDueDate = submissionDueDate;
    }

    public boolean isAllowPublish() {
        return allowPublish;
    }

    public void setAllowPublish(boolean allowPublish) {
        this.allowPublish = allowPublish;
    }

    public boolean isAllowDraft() {
        return allowDraft;
    }

    public void setAllowDraft(boolean allowDraft) {
        this.allowDraft = allowDraft;
    }

    public TaskVerificationType getTaskVerificationType() {
        return taskVerificationType;
    }

    public void setTaskVerificationType(TaskVerificationType taskVerificationType) {
        this.taskVerificationType = taskVerificationType;
    }
}
