package com.accelevents.common.dto;

import com.accelevents.domain.enums.EventFormat;
import com.accelevents.registration.approval.dto.RegistrationRequestsDetailsDto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@Schema(description = "User application details for registration approval")
public class UserApplicationDto {

    private String eventLogoImage;

    private String eventName;

    private String eventUrl;

    private Date eventStartDate;

    private Date eventEndDate;

    private String eventLocation;

    private EventFormat eventFormat;

    private String equivalentTimezone;

    private List<RegistrationRequestsDetailsDto> registrationRequestsDetailsDtoList;

    public String getEventLogoImage() {
        return eventLogoImage;
    }

    public void setEventLogoImage(String eventLogoImage) {
        this.eventLogoImage = eventLogoImage;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventUrl() {
        return eventUrl;
    }

    public void setEventUrl(String eventUrl) {
        this.eventUrl = eventUrl;
    }

    public Date getEventStartDate() {
        return eventStartDate;
    }

    public void setEventStartDate(Date eventStartDate) {
        this.eventStartDate = eventStartDate;
    }

    public Date getEventEndDate() {
        return eventEndDate;
    }

    public void setEventEndDate(Date eventEndDate) {
        this.eventEndDate = eventEndDate;
    }

    public String getEventLocation() {
        return eventLocation;
    }

    public void setEventLocation(String eventLocation) {
        this.eventLocation = eventLocation;
    }

    public EventFormat getEventFormat() {
        return eventFormat;
    }

    public void setEventFormat(EventFormat eventFormat) {
        this.eventFormat = eventFormat;
    }

    public String getEquivalentTimezone() {
        return equivalentTimezone;
    }

    public void setEquivalentTimezone(String equivalentTimezone) {
        this.equivalentTimezone = equivalentTimezone;
    }

    public List<RegistrationRequestsDetailsDto> getRegistrationRequestsDetailsDtoList() {
        return registrationRequestsDetailsDtoList;
    }

    public void setRegistrationRequestsDetailsDtoList(List<RegistrationRequestsDetailsDto> registrationRequestsDetailsDtoList) {
        this.registrationRequestsDetailsDtoList = registrationRequestsDetailsDtoList;
    }


}
