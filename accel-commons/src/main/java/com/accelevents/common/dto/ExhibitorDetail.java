package com.accelevents.common.dto;

import com.accelevents.domain.Staff;
import com.accelevents.enums.StaffRole;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

@Schema(description ="exhibitorDetail")
public class ExhibitorDetail extends StaffDetail{

	@Schema(description ="id of exhibitor person associated with")
	private Long exhibitorId;
	 
	@Schema(description ="define if added staff is admin or lead retriver")
	private boolean isExhibitorAdmin;

	@Schema(description ="leads associated with staff")
	private Long leads;

	public ExhibitorDetail() {
	}

	public ExhibitorDetail(Staff staff,Long leads) {
		super(staff);
		this.leads = leads != null ? leads : 0;
		this.exhibitorId = staff.getExhibitorId();
		this.isExhibitorAdmin = StaffRole.exhibitoradmin.equals(staff.getRole());
	}

	public ExhibitorDetail(Staff staff, Long leads, Long previousLoggedInCount, Map<Long,Boolean> ticketTypeIds){
        super(staff,previousLoggedInCount,ticketTypeIds);
        this.leads = leads != null ? leads : 0;
        this.exhibitorId = staff.getExhibitorId();
        this.isExhibitorAdmin = StaffRole.exhibitoradmin.equals(staff.getRole());

    }

    public ExhibitorDetail(Staff staff, Long leads, Long previousLoggedInCount, Map<Long,Boolean> ticketTypeIds, Map<Long, String> ticketTypeNames){
        super(staff,previousLoggedInCount,ticketTypeIds,ticketTypeNames);
        this.leads = leads != null ? leads : 0;
        this.exhibitorId = staff.getExhibitorId();
        this.isExhibitorAdmin = StaffRole.exhibitoradmin.equals(staff.getRole());

    }

    public ExhibitorDetail(Staff staff){
        super(staff);
        if(staff.getUser() != null){
            this.setProfileImage(staff.getUser().getPhoto());
            this.setUserId(staff.getUser().getUserId());
        }
        this.exhibitorId = staff.getExhibitorId();
        this.isExhibitorAdmin = StaffRole.exhibitoradmin.equals(staff.getRole());

    }

	public Long getLeads() {
		return leads;
	}

	public void setLeads(Long leads) {
		this.leads = leads;
	}

	public Long getExhibitorId() {
		return exhibitorId;
	}

	public void setExhibitorId(Long exhibitorId) {
		this.exhibitorId = exhibitorId;
	}

	public boolean isExhibitorAdmin() {
		return isExhibitorAdmin;
	}

	public void setExhibitorAdmin(boolean isExhibitorAdmin) {
		this.isExhibitorAdmin = isExhibitorAdmin;
	}
	
}
