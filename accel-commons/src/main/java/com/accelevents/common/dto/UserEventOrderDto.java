package com.accelevents.common.dto;

import com.accelevents.domain.enums.EventFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

public class UserEventOrderDto {


    private String eventLogoImage;

    private String eventName;

    private String eventUrl;

    private Date eventStartDate;

    private Date eventEndDate;

    private long ticketOrderNumber;

    private double ticketOrderAmount;

    private Date purchaseDate;

    private String seatNumbers;

    private boolean allowAttendeeToEditInfo;

    private String currency;

    private List<TicketTypeNameQtyDto> ticketTypeNameQtyDto;

    @Schema(description ="Total purchased tickets in order")
    private long totalPurchasedTicketsInOrder;

    @Schema(description ="all attributes are for purchaser or for ticket holder")
    private Boolean holderAttribute;

    @Schema(description ="Event location")
    private String eventLocation;

    @Schema(description ="Is seating Type Order")
    private Boolean isSeating = Boolean.FALSE;

    @Schema(description ="event dates will be hide or not")
    private boolean hideEventDate;

    @Schema(description ="Pre Event Access time in minutes")
    private Integer preEventAccessMinutes;

    @Schema(description ="Event Format")
    private EventFormat eventFormat;

    @Schema(description ="this is timezone equivalent to custom timezone")
    private String equivalentTimezone;

    @Schema(description = "order status e.g. paid, partially paid or refunded")
    private String status;

    private boolean nonTransferable;

    private boolean allowToSendOrderConfirmationEmail;

    private Long recurringEventId;

    private String couponCode;

    public UserEventOrderDto() {
    }

    public String getEventLogoImage() {
        return eventLogoImage;
    }

    public void setEventLogoImage(String eventLogoImage) {
        this.eventLogoImage = eventLogoImage;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventUrl() {
        return eventUrl;
    }

    public void setEventUrl(String eventUrl) {
        this.eventUrl = eventUrl;
    }

    public Date getEventStartDate() {
        return eventStartDate;
    }

    public void setEventStartDate(Date eventStartDate) {
        this.eventStartDate = eventStartDate;
    }

    public Date getEventEndDate() {
        return eventEndDate;
    }

    public void setEventEndDate(Date eventEndDate) {
        this.eventEndDate = eventEndDate;
    }

    public long getTicketOrderNumber() {
        return ticketOrderNumber;
    }

    public void setTicketOrderNumber(long ticketOrderNumber) {
        this.ticketOrderNumber = ticketOrderNumber;
    }

    public double getTicketOrderAmount() {
        return ticketOrderAmount;
    }

    public void setTicketOrderAmount(double ticketOrderAmount) {
        this.ticketOrderAmount = ticketOrderAmount;
    }

    public Date getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Date purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public String getSeatNumbers() {
        return seatNumbers;
    }

    public void setSeatNumbers(String seatNumbers) {
        this.seatNumbers = seatNumbers;
    }

    public boolean isAllowAttendeeToEditInfo() {
        return allowAttendeeToEditInfo;
    }

    public void setAllowAttendeeToEditInfo(boolean allowAttendeeToEditInfo) {
        this.allowAttendeeToEditInfo = allowAttendeeToEditInfo;
    }

    public long getTotalPurchasedTicketsInOrder() {
        return totalPurchasedTicketsInOrder;
    }

    public void setTotalPurchasedTicketsInOrder(long totalPurchasedTicketsInOrder) {
        this.totalPurchasedTicketsInOrder = totalPurchasedTicketsInOrder;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Boolean getHolderAttribute() {
        return holderAttribute;
    }

    public void setHolderAttribute(Boolean holderAttribute) {
        this.holderAttribute = holderAttribute;
    }

    public String getEventLocation() {
        return eventLocation;
    }

    public void setEventLocation(String eventLocation) {
        this.eventLocation = eventLocation;
    }

    public Boolean getSeating() {
        return isSeating;
    }

    public void setSeating(Boolean seating) {
        isSeating = seating;
    }

    public boolean isHideEventDate() {
        return hideEventDate;
    }

    public void setHideEventDate(boolean hideEventDate) {
        this.hideEventDate = hideEventDate;
    }

    public List<TicketTypeNameQtyDto> getTicketTypeNameQtyDto() {
        return ticketTypeNameQtyDto;
    }

    public void setTicketTypeNameQtyDto(List<TicketTypeNameQtyDto> ticketTypeNameQtyDto) {
        this.ticketTypeNameQtyDto = ticketTypeNameQtyDto;
    }

    public Integer getPreEventAccessMinutes() {
        return preEventAccessMinutes;
    }

    public void setPreEventAccessMinutes(Integer preEventAccessMinutes) {
        this.preEventAccessMinutes = preEventAccessMinutes;
    }

    public EventFormat getEventFormat() {
        return eventFormat;
    }

    public void setEventFormat(EventFormat eventFormat) {
        this.eventFormat = eventFormat;
    }

    public String getEquivalentTimezone() {
        return equivalentTimezone;
    }

    public void setEquivalentTimezone(String equivalentTimezone) {
        this.equivalentTimezone = equivalentTimezone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isNonTransferable() {
        return nonTransferable;
    }

    public void setNonTransferable(boolean nonTransferable) {
        this.nonTransferable = nonTransferable;
    }

    public boolean isAllowToSendOrderConfirmationEmail() {return allowToSendOrderConfirmationEmail;}

    public void setAllowToSendOrderConfirmationEmail(boolean allowToSendOrderConfirmationEmail) {this.allowToSendOrderConfirmationEmail = allowToSendOrderConfirmationEmail;}

    public Long getRecurringEventId() {
        return recurringEventId;
    }

    public void setRecurringEventId(Long recurringEventId) {
        this.recurringEventId = recurringEventId;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }
}
