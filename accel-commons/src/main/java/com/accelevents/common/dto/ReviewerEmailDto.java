package com.accelevents.common.dto;

import com.accelevents.domain.ReviewerEmail;
import com.accelevents.domain.enums.ReviewerEmailType;

public class ReviewerEmailDto {

    private Long id;

    private Long eventId;

    private String subject;

    private String body;

    private boolean isAutoSendMail = Boolean.TRUE;

    private ReviewerEmailType type;

    public ReviewerEmailDto() {
    }

    public ReviewerEmailDto(boolean isAutoSendMail,ReviewerEmailType type) {
        this.isAutoSendMail = isAutoSendMail;
        this.type=type;
    }

    public ReviewerEmailDto(Long id, Long eventId, String subject, String body, boolean isAutoSendMail) {
        this.id = id;
        this.eventId = eventId;
        this.subject = subject;
        this.body = body;
        this.isAutoSendMail = isAutoSendMail;
    }
    public ReviewerEmailDto(Long id, Long eventId, String subject, String body, boolean isAutoSendMail,ReviewerEmailType type) {
        this.id = id;
        this.eventId = eventId;
        this.subject = subject;
        this.body = body;
        this.isAutoSendMail = isAutoSendMail;
        this.type=type;
    }

    public ReviewerEmail toEntity(){
        ReviewerEmail reviewerEmail = new ReviewerEmail();
        reviewerEmail.setEventId(this.getEventId());
        reviewerEmail.setSubject(this.getSubject());
        reviewerEmail.setBody(this.getBody());
        reviewerEmail.setAutoSendMail(this.isAutoSendMail());
        reviewerEmail.setType(this.type);
        return reviewerEmail;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public boolean isAutoSendMail() {
        return isAutoSendMail;
    }

    public void setAutoSendMail(boolean autoSendMail) {
        isAutoSendMail = autoSendMail;
    }

    public ReviewerEmailType getType() {
        return type;
    }

    public void setType(ReviewerEmailType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ReviewerEmailDto{" +
                "id=" + id +
                ", eventId=" + eventId +
                ", subject='" + subject + '\'' +
                ", body='" + body + '\'' +
                ", isAutoSendMail=" + isAutoSendMail +
                '}';
    }
}
