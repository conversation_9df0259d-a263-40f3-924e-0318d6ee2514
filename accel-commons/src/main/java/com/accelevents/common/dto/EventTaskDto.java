package com.accelevents.common.dto;

import com.accelevents.constraint.CheckDateFormat;
import com.accelevents.domain.EventTask;
import com.accelevents.domain.TaskSchedule;
import com.accelevents.domain.enums.TaskCompletionCriteria;
import com.accelevents.domain.enums.TaskType;
import com.accelevents.domain.enums.TaskVerificationType;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.DATE_FORMAT;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;

@Schema(description = "Data Transfer Object for Event Task")
public class EventTaskDto {

    @Schema(description = "Task ID")
    private Long id;

    @Schema(description = "Task type, such as SPEAKER or SESSION")
    private TaskType taskType;

    @Schema(description = "Title of the task")
    @Size(max = 255, message = "Title cannot exceed 255 characters.")
    @NotBlank
    private String title;

    @Schema(description = "Detailed description of the task")
    @Size(max = 1000, message = "Description cannot exceed 1000 characters.")
    @NotBlank
    private String description;

    @Schema(description = "Indicates if the task is assigned to all users")
    private boolean allowAllAssignees;

    @Schema(description = "Indicates if the task can be saved as a draft")
    private boolean allowDraft;

    @Schema(description = "Submission due date for the task in dd/MM/yyyy HH:mm:ss format")
    @CheckDateFormat(pattern = "dd/MM/yyyy HH:mm:ss", message = "Please enter date in dd/MM/yyyy HH:mm:ss format")
    private String submissionDueDate;

    @Schema(description = "List of users assigned to the task")
    private List<Long> assignedTo;

    @Schema(description = "Indicates if the task allows publication")
    private boolean allowPublish;

    @Schema(description = "Indicates if email notifications are allowed for the task")
    private boolean allowSendMail;

    @Schema(description = "List of reminder and notice schedule time")
    private List<TaskScheduleDto> taskScheduleDtoList;

    @Schema(description = "Indicates if the task allow reminder.")
    private boolean allowReminder;

    @Schema(description = "Indicates if the task allow overdue notice.")
    private boolean allowOverdueNotice;

    @Schema(description = "Task Verification Type , Manual Verification, System Verification")
    private TaskVerificationType taskVerificationType = TaskVerificationType.MANUAL_VERIFICATION;

    @Schema(description = "Task Completion Criteria")
    private TaskCompletionCriteria taskCompletionCriteria;

    @Schema(description = "survey Id")
    private Long surveyId;


    public void updateEventTaskDto(EventTask eventTask,String equivalentTimezone){
        this.setAllowPublish(eventTask.isAllowPublish());
        this.setAllowDraft(eventTask.isAllowDraft());
        this.setId(eventTask.getId());
        this.setDescription(eventTask.getDescription());
        this.setTitle(eventTask.getTitle());
        this.setAllowAllAssignees(eventTask.isAllowAllAssignees());
        this.setTaskType(eventTask.getTaskType());
        this.setSubmissionDueDate(getDateInLocal(eventTask.getSubmissionDueDate(),equivalentTimezone,DATE_FORMAT));
        this.setAllowReminder(eventTask.isAllowReminder());
        this.setAllowOverdueNotice(eventTask.isAllowOverdueNotice());
        this.setAllowPublish(eventTask.isAllowPublish());
    }

    public void updateTaskScheduleDto(List<TaskSchedule> taskScheduleList, EventTaskDto eventTaskDto) {
        if (taskScheduleList != null && !taskScheduleList.isEmpty()) {
            List<TaskScheduleDto> taskScheduleDtos = taskScheduleList.stream()
                    .map(this::getTaskScheduleDto)
                    .collect(Collectors.toList());
            eventTaskDto.setTaskScheduleDtoList(taskScheduleDtos);
        } else {
            eventTaskDto.setTaskScheduleDtoList(Collections.emptyList());
        }
    }

    @NotNull
    private TaskScheduleDto getTaskScheduleDto(TaskSchedule taskSchedule) {
        TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setId(taskSchedule.getId());
        taskScheduleDto.setTaskId(taskSchedule.getTaskId());
        taskScheduleDto.setAllowBeforeTaskIsDue(taskSchedule.isAllowBeforeTaskIsDue());
        taskScheduleDto.setScheduledStatus(taskSchedule.getScheduledStatus());
        taskScheduleDto.setTimePeriod(taskSchedule.getTimePeriod());
        taskScheduleDto.setTimeFrameType(taskSchedule.getTimeFrameType());
        return taskScheduleDto;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(TaskType taskType) {
        this.taskType = taskType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isAllowAllAssignees() {
        return allowAllAssignees;
    }

    public void setAllowAllAssignees(boolean allowAllAssignees) {
        this.allowAllAssignees = allowAllAssignees;
    }

    public boolean isAllowDraft() {
        return allowDraft;
    }

    public void setAllowDraft(boolean allowDraft) {
        this.allowDraft = allowDraft;
    }

    public String getSubmissionDueDate() {
        return submissionDueDate;
    }

    public void setSubmissionDueDate(String submissionDueDate) {
        this.submissionDueDate = submissionDueDate;
    }

    public List<Long> getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(List<Long> assignedTo) {
        this.assignedTo = assignedTo;
    }

    public boolean isAllowPublish() {
        return allowPublish;
    }

    public void setAllowPublish(boolean allowPublish) {
        this.allowPublish = allowPublish;
    }

    public boolean isAllowSendMail() {
        return allowSendMail;
    }

    public void setAllowSendMail(boolean allowSendMail) {
        this.allowSendMail = allowSendMail;
    }

    public List<TaskScheduleDto> getTaskScheduleDtoList() {
        return taskScheduleDtoList;
    }

    public void setTaskScheduleDtoList(List<TaskScheduleDto> taskScheduleDtoList) {
        this.taskScheduleDtoList = taskScheduleDtoList;
    }

    public boolean isAllowReminder() {
        return allowReminder;
    }

    public void setAllowReminder(boolean allowReminder) {
        this.allowReminder = allowReminder;
    }

    public boolean isAllowOverdueNotice() {
        return allowOverdueNotice;
    }

    public void setAllowOverdueNotice(boolean allowOverdueNotice) {
        this.allowOverdueNotice = allowOverdueNotice;
    }

    public TaskVerificationType getTaskVerificationType() {
        return taskVerificationType;
    }

    public void setTaskVerificationType(TaskVerificationType taskVerificationType) {
        this.taskVerificationType = taskVerificationType;
    }


    public TaskCompletionCriteria getTaskCompletionCriteria() {
        return taskCompletionCriteria;
    }

    public void setTaskCompletionCriteria(TaskCompletionCriteria taskCompletionCriteria) {
        this.taskCompletionCriteria = taskCompletionCriteria;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public EventTask toEntity(Long eventId, Long userId, String equivalentTimezone){
        EventTask eventTask = new EventTask();
        eventTask.setId(0L);
        eventTask.setEventId(eventId);
        eventTask.setCreatedBy(userId);
        eventTask.setTitle(getTitle());
        eventTask.setDescription(getDescription());
        eventTask.setAllowDraft(isAllowDraft());
        eventTask.setTaskType(getTaskType());
        eventTask.setAllowAllAssignees(isAllowAllAssignees());
        eventTask.setSubmissionDueDate(getDateInUTC(getSubmissionDueDate(),equivalentTimezone,DATE_FORMAT));
        eventTask.setAllowReminder(isAllowReminder());
        eventTask.setAllowOverdueNotice(isAllowOverdueNotice());
        eventTask.setAllowPublish(isAllowPublish());
        eventTask.setTaskVerificationType(getTaskVerificationType());
        eventTask.setTaskCompletionCriteria(getTaskCompletionCriteria());
        eventTask.setSurveyId(getSurveyId());
        return  eventTask;
    }


    @Override
    public String toString() {
        return "EventTaskDto{" +
                "id=" + id +
                ", taskType=" + taskType +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", allowAllAssignees=" + allowAllAssignees +
                ", allowDraft=" + allowDraft +
                ", submissionDueDate='" + submissionDueDate + '\'' +
                ", assignedTo=" + assignedTo +
                ", allowPublish=" + allowPublish +
                ", allowSendMail=" + allowSendMail +
                ", allowReminder=" + allowReminder +
                ", allowOverNotice=" + allowOverdueNotice +
                ", taskScheduleDtoList=" + taskScheduleDtoList +
                ", taskVerificationType=" + taskVerificationType +
                ", surveyId=" + surveyId +
                '}';
    }
}
