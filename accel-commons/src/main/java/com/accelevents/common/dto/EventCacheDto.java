package com.accelevents.common.dto;

import com.accelevents.constraint.CheckDateFormat;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.*;
import com.accelevents.utils.TimeZone;
import com.accelevents.utils.TimeZoneUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Schema(description ="Event Details")
public class EventCacheDto {

	@Schema(description ="Event Id")
	private long eventId;

	@Schema(description ="Event Name")
	private String name;

	@Schema(description ="Event Url")
	private String eventURL;

	@Schema(description ="Is auction enabled?")
	private boolean auctionEnabled;

	@Schema(description ="Is auction Module show?")
	private boolean auctionModuleShow;

	@Schema(description ="Auction Id")
	private long auctionId;

	@Schema(description ="Latitude")
	private String latitude;

	@Schema(description ="Longitude")
	private  String longitude;

	@Schema(description ="Is FundANeed enabled?")
	private boolean fundANeedEnabled;

	@Schema(description ="Is FunANeed Show?")
	private boolean fundANeedModuleShow;

	@Schema(description ="FundANeed Id")
	private long fundANeedId;

	@Schema(description ="Is raffle enabled?")
	private boolean raffleEnabled;

	@Schema(description ="Is raffle module show?")
	private boolean raffleModuleShow;

	@Schema(description ="Raffle Id")
	private long raffleId;

	@Schema(description ="Is ticketing enabled?")
	private boolean ticketingEnabled;

	@Schema(description ="Ticketing Id")
	private Long ticketingId;

	@Schema(description ="Is donation enabled?")
	private boolean donationEnabled;

	@Schema(description ="Is text to give enabled?")
	private boolean textToGiveEnabled;

	@Schema(description ="Fund raising goal")
	private int fundRaisingGoal;

	@Schema(description ="Goal Starting amount")
	private int goalStartingAmount;

	@Schema(description ="Timezone Id")
	private String timezoneId;

	@Schema(description ="Timezone offset")
	private String timezoneOffset;

	@Schema(description ="this is timezone equivalent to custom timezone")
	private String equivalentTimezone;

	@Schema(description ="Phone number")
	private long phoneNumber;

	@Schema(description ="Account Activated Trigger Status for facebook pixel")
	private AccountActivatedTriggerStatus accountActivatedTriggerStatus;

	@Schema(description ="Is credit card enabled?")
	private boolean creditCardEnabled;

	@Schema(description ="Is credit card required for bid confirmatino?")
	private boolean ccRequiredForBidConfirm;

	@Schema(description ="Tax Id")
	private String taxId;

	@Schema(description ="Creation date")
	private Date createdDate;

	@Schema(description ="Country code")
	private CountryCode countryCode;

	@Schema(description ="Currency")
	private Currency currency;

	@Schema(description ="Currency Symbol")
	private String currencySymbol;

	@Schema(description ="is processing fees to purchaser")
	private boolean processingFeesToPurchaser;

	@Schema(description ="is bidder registation enable")
	private boolean enableBidderRegistration;

	@Schema(description ="Auction end date in yyyy/MM/dd HH:mm format")
	private String auctionEndDate;

	@Schema(description ="Raffle end date in yyyy/MM/dd HH:mm format")
	private String raffleEndDate;

	@Schema(description ="FundANeed end date in yyyy/MM/dd HH:mm format")
	private String fundANeedEndDate;

	@Schema(description ="FundANeed end date in yyyy/MM/dd HH:mm format")
	private String ticketingEndDate;

	@Schema(description ="Event status")
	private EventStatus eventStatus;

	@Schema(description ="Event Design Detials")
	private EventDesignDetailDto eventDesignDetails;

	@Schema(description ="Event Google Analytics Id")
	private String analyticsId;

	@Schema(description ="Event Facebook tracking pixel Id")
	private String trackingPixelId;

	@Schema(description ="Live item Available")
	private boolean liveItemAvailable;

	@Schema(description ="Key for stripe or square")
	private String stripeKey;

	@Schema(description ="Location Id Of Square")
	private String squareLocationId;

	private boolean isStripeConnectRequiredToTicketing;

    @Schema(description ="Event Linkedin Tracking Partner ID")
    private String linkedinTrackinPartnerId;

    @Schema(description ="Event Linkedin Tracking Conversion ID")
    private String linkedinTrackinConversionId;

	@Schema(description ="true when payment gateway is connected else will be false")
	private boolean ccProcessingEnabled;

	@Schema(description = "This determines the payment gateway to which event host had connected the event to receive payments.", allowableValues="SQUARE,STRIPE")
	private String paymentGateway;

	@Schema(description ="event start date in yyyy/MM/dd HH:mm format")
	@CheckDateFormat(pattern = "dd/MM/yyyy HH:mm", message = "Please enter date in dd/MM/yyyy HH:mm format")
	private String startDate;

	@Schema(description ="event end date in yyyy/MM/dd HH:mm format")
	@CheckDateFormat(pattern = "dd/MM/yyyy HH:mm", message = "Please enter date in dd/MM/yyyy HH:mm format")
	private String endDate;

	@Schema(description = "ticketing venue")
	private String address;

	@Schema(description = "is recurring event")
	private boolean isRecurring;

	@Schema(description ="Organizer name")
	private String organizerName;

	@Schema(description ="Organizer page url")
	private String organizerPageURL;

    @Schema(description ="Organizer website")
    private String organizerWebsite;

    @Schema(description ="is Support Enabled")
    private boolean supportEnabled;

    @Schema(description ="is auto submit on raffle ticket purchase enabled")
    private boolean raffleAutoSubmitOnPurchase;

    @Schema(description ="event dates will be hide or not")
    private boolean hideEventDate;

    @Schema(description ="is attendee registration approval enabled")
    private boolean attendeeRegistrationApproval;

    @Schema(description ="is expo registration approval enabled")
    private boolean expoRegistrationApproval;

    @Schema(description = "is speaker registration approval enabled")
    private boolean speakerRegistrationApproval;

    @Schema(description = "is reviewer registration approval enabled")
    private boolean reviewerRegistrationApproval;

    @Schema(description ="Event Format")
    private EventFormat eventFormat;

    @Schema(description ="Show Session by event timezone")
    private boolean showSessionByTimezone;
    @Schema(description ="is Event Private")
    private boolean isEventPrivate;


	public EventCacheDto(Event event) {
		this.eventId = event.getEventId();
		this.name = event.getName();
		this.eventURL = event.getEventURL();
		this.auctionEnabled = event.isSilentAuctionEnabled();
		this.auctionId = event.getAuctionId();
		this.fundANeedEnabled = event.isCauseAuctionEnabled();
		this.fundANeedId = event.getCauseAuctionId();
		this.raffleEnabled = event.isRaffleEnabled();
		this.raffleId = event.getRaffleId();
		this.ticketingEnabled = event.isTicketingEnabled();
		this.ticketingId = event.getTicketingId();
		this.donationEnabled = event.isDonationEnabled();
		this.textToGiveEnabled = event.isTextToGiveEnabled();
		this.fundRaisingGoal = event.getFundRaisingGoal();
		this.goalStartingAmount = event.getGoalStartingAmount();
		this.timezoneId = event.getTimezoneId();
		TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());
		this.timezoneOffset = timeZone.getDisplayOffset();
		this.equivalentTimezone = timeZone.getEquivalentTimezone();
        this.showSessionByTimezone = event.isStaticTimezone();
		this.phoneNumber = event.getPhoneNumber();
		this.accountActivatedTriggerStatus = event.getAccountActivatedTriggerStatus();
		this.creditCardEnabled = event.isCreditCardEnabled();
		this.ccRequiredForBidConfirm = event.isCcRequiredForBidConfirm();
		this.taxId = event.getTaxId();
		this.createdDate = event.getCreatedDate();
		this.countryCode = event.getCountryCode();
		this.currency = event.getCurrency();
		this.currencySymbol = event.getCurrency().getSymbol();
		this.eventStatus = event.getEventStatus();
		this.enableBidderRegistration = event.getEnableBidderRegistration();
		this.analyticsId = event.getAnalyticsId();
		this.trackingPixelId =event.getTrackinPixelId();
		this.supportEnabled = event.getWhiteLabel() != null && StringUtils.isNotEmpty(event.getWhiteLabel().getSupportEmail());
        this.hideEventDate = event.isHideEventDate();
        this.eventFormat = event.getEventFormat();
	}

	public long getEventId() {
		return eventId;
	}

	public void setEventId(long eventId) {
		this.eventId = eventId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEventURL() {
		return eventURL;
	}

	public void setEventURL(String eventURL) {
		this.eventURL = eventURL;
	}

	public boolean isAuctionEnabled() {
		return auctionEnabled;
	}

	public void setAuctionEnabled(boolean auctionEnabled) {
		this.auctionEnabled = auctionEnabled;
	}

    public boolean isAuctionModuleShow() {
        return auctionModuleShow;
    }

    public void setAuctionModuleShow(boolean auctionModuleShow) {
        this.auctionModuleShow = auctionModuleShow;
    }

    public long getAuctionId() {
		return auctionId;
	}

	public void setAuctionId(long auctionId) {
		this.auctionId = auctionId;
	}

	public boolean isFundANeedEnabled() {
		return fundANeedEnabled;
	}

	public void setFundANeedEnabled(boolean fundANeedEnabled) {
		this.fundANeedEnabled = fundANeedEnabled;
	}

    public boolean isFundANeedModuleShow() { return fundANeedModuleShow; }

    public void setFundANeedModuleShow(boolean fundANeedModuleShow) { this.fundANeedModuleShow = fundANeedModuleShow;}

    public long getFundANeedId() {
		return fundANeedId;
	}

	public void setFundANeedId(long fundANeedId) {
		this.fundANeedId = fundANeedId;
	}

	public boolean isRaffleEnabled() {
		return raffleEnabled;
	}

	public void setRaffleEnabled(boolean raffleEnabled) {
		this.raffleEnabled = raffleEnabled;
	}

    public boolean isRaffleModuleShow() {
        return raffleModuleShow;
    }

    public void setRaffleModuleShow(boolean raffleModuleShow) {
        this.raffleModuleShow = raffleModuleShow;
    }

	public long getRaffleId() {
		return raffleId;
	}

	public void setRaffleId(long raffleId) {
		this.raffleId = raffleId;
	}

	public boolean isTicketingEnabled() {
		return ticketingEnabled;
	}

	public void setTicketingEnabled(boolean ticketingEnabled) {
		this.ticketingEnabled = ticketingEnabled;
	}

	public Long getTicketingId() {
		return ticketingId;
	}

	public void setTicketingId(long ticketingId) {
		this.ticketingId = ticketingId;
	}

	public boolean isDonationEnabled() {
		return donationEnabled;
	}

	public void setDonationEnabled(boolean donationEnabled) {
		this.donationEnabled = donationEnabled;
	}

	public int getFundRaisingGoal() {
		return fundRaisingGoal;
	}

	public void setFundRaisingGoal(int fundRaisingGoal) {
		this.fundRaisingGoal = fundRaisingGoal;
	}

	public int getGoalStartingAmount() {
		return goalStartingAmount;
	}

	public void setGoalStartingAmount(int goalStartingAmount) {
		this.goalStartingAmount = goalStartingAmount;
	}

	public String getTimezoneId() {
		return timezoneId;
	}

	public void setTimezoneId(String timezoneId) {
		this.timezoneId = timezoneId;
	}

	public String getTimezoneOffset() {
		return timezoneOffset;
	}

	public void setTimezoneOffset(String timezoneOffset) {
		this.timezoneOffset = timezoneOffset;
	}

	public String getEquivalentTimezone() {
		return equivalentTimezone;
	}

	public void setEquivalentTimezone(String equivalentTimezone) {
		this.equivalentTimezone = equivalentTimezone;
	}

	public long getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(long phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public AccountActivatedTriggerStatus getAccountActivatedTriggerStatus() {
		return accountActivatedTriggerStatus;
	}

	public void setAccountActivatedTriggerStatus(AccountActivatedTriggerStatus accountActivatedTriggerStatus) {
		this.accountActivatedTriggerStatus = accountActivatedTriggerStatus;
	}

	public boolean isCreditCardEnabled() {
		return creditCardEnabled;
	}

	public void setCreditCardEnabled(boolean creditCardEnabled) {
		this.creditCardEnabled = creditCardEnabled;
	}

	public boolean isCcRequiredForBidConfirm() {
		return ccRequiredForBidConfirm;
	}

	public void setCcRequiredForBidConfirm(boolean ccRequiredForBidConfirm) {
		this.ccRequiredForBidConfirm = ccRequiredForBidConfirm;
	}

	public String getTaxId() {
		return taxId;
	}

	public void setTaxId(String taxId) {
		this.taxId = taxId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public CountryCode getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(CountryCode countryCode) {
		this.countryCode = countryCode;
	}

	public Currency getCurrency() {
		return currency;
	}

	public void setCurrency(Currency currency) {
		this.currency = currency;
	}

	public String getCurrencySymbol() {
		return currencySymbol;
	}

	public void setCurrencySymbol(String currencySymbol) {
		this.currencySymbol = currencySymbol;
	}

	public boolean isProcessingFeesToPurchaser() {
		return processingFeesToPurchaser;
	}

	public void setProcessingFeesToPurchaser(boolean processingFeesToPurchaser) {
		this.processingFeesToPurchaser = processingFeesToPurchaser;
	}

	public boolean isEnableBidderRegistration() {
		return enableBidderRegistration;
	}

	public void setEnableBidderRegistration(boolean enableBidderRegistration) {
		this.enableBidderRegistration = enableBidderRegistration;
	}

    public String getAuctionEndDate() {
		return auctionEndDate;
	}

	public void setAuctionEndDate(String auctionEndDate) {
		this.auctionEndDate = auctionEndDate;
	}

	public String getRaffleEndDate() {
		return raffleEndDate;
	}

	public void setRaffleEndDate(String raffleEndDate) {
		this.raffleEndDate = raffleEndDate;
	}

	public String getFundANeedEndDate() {
		return fundANeedEndDate;
	}

	public void setFundANeedEndDate(String fundANeedEndDate) {
		this.fundANeedEndDate = fundANeedEndDate;
	}

	public String getTicketingEndDate() {
		return ticketingEndDate;
	}

	public void setTicketingEndDate(String ticketingEndDate) {
		this.ticketingEndDate = ticketingEndDate;
	}

	public EventStatus getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(EventStatus eventStatus) {
		this.eventStatus = eventStatus;
	}

	public EventDesignDetailDto getEventDesignDetails() {
		return eventDesignDetails;
	}

	public void setEventDesignDetails(EventDesignDetailDto eventDesignDetails) {
		this.eventDesignDetails = eventDesignDetails;
	}

	public String getAnalyticsId() {
		return analyticsId;
	}

	public void setAnalyticsId(String analyticsId) {
		this.analyticsId = analyticsId;
	}

	public String getStripeKey() {
		return stripeKey;
	}

	public void setStripeKey(String stripeKey) {
		this.stripeKey = stripeKey;
	}

	public boolean isCcProcessingEnabled() {
		return ccProcessingEnabled;
	}

	public void setCcProcessingEnabled(boolean ccProcessingEnabled) {
		this.ccProcessingEnabled = ccProcessingEnabled;
	}

	public boolean isTextToGiveEnabled() {
		return textToGiveEnabled;
	}

	public void setTextToGiveEnabled(boolean textToGiveEnabled) {
		this.textToGiveEnabled = textToGiveEnabled;
	}

	public boolean isStripeConnectRequiredToTicketing() {
		return isStripeConnectRequiredToTicketing;
	}

	public void setStripeConnectRequiredToTicketing(boolean stripeConnectRequiredToTicketing) {
		isStripeConnectRequiredToTicketing = stripeConnectRequiredToTicketing;
	}

	public boolean isLiveItemAvailable() {
		return liveItemAvailable;
	}

	public void setLiveItemAvailable(boolean liveItemAvailable) {
		this.liveItemAvailable = liveItemAvailable;
	}

	public String getPaymentGateway() {
		return paymentGateway;
	}

	public void setPaymentGateway(String paymentGateway) {
		this.paymentGateway = paymentGateway;

	}

	public String getSquareLocationId() {
		return squareLocationId;
	}

	public void setSquareLocationId(String squareLocationId) {
		this.squareLocationId = squareLocationId;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getTrackingPixelId() {
		return trackingPixelId;
	}

	public void setTrackingPixelId(String trackingPixelId) {
		this.trackingPixelId = trackingPixelId;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public boolean isRecurring() {
		return isRecurring;
	}

	public void setRecurring(boolean recurring) {
		isRecurring = recurring;
	}

	public String getOrganizerName() {
		return organizerName;
	}

	public void setOrganizerName(String organizerName) {
		this.organizerName = organizerName;
	}

	public String getOrganizerPageURL() {
		return organizerPageURL;
	}

	public void setOrganizerPageURL(String organizerPageURL) {
		this.organizerPageURL = organizerPageURL;
	}

    public String getOrganizerWebsite() {
        return organizerWebsite;
    }

    public void setOrganizerWebsite(String organizerWebsite) {
        this.organizerWebsite = organizerWebsite;
    }

    public boolean isSupportEnabled() {
        return supportEnabled;
    }

    public void setSupportEnabled(boolean supportEnabled) {
        this.supportEnabled = supportEnabled;
    }

    public boolean isRaffleAutoSubmitOnPurchase() {
        return raffleAutoSubmitOnPurchase;
    }

    public void setRaffleAutoSubmitOnPurchase(boolean raffleAutoSubmitOnPurchase) {
        this.raffleAutoSubmitOnPurchase = raffleAutoSubmitOnPurchase;
    }

    public String getLinkedinTrackinPartnerId() {
        return linkedinTrackinPartnerId;
    }

    public void setLinkedinTrackinPartnerId(String linkedinTrackinPartnerId) {
        this.linkedinTrackinPartnerId = linkedinTrackinPartnerId;
    }

    public String getLinkedinTrackinConversionId() {
        return linkedinTrackinConversionId;
    }

    public void setLinkedinTrackinConversionId(String linkedinTrackinConversionId) {
        this.linkedinTrackinConversionId = linkedinTrackinConversionId;
    }

    public boolean isHideEventDate() { return hideEventDate; }

    public void setHideEventDate(boolean hideEventDate) { this.hideEventDate = hideEventDate; }

    public boolean isAttendeeRegistrationApproval() {
        return attendeeRegistrationApproval;
    }

    public void setAttendeeRegistrationApproval(boolean attendeeRegistrationApproval) {
        this.attendeeRegistrationApproval = attendeeRegistrationApproval;
    }

    public boolean isExpoRegistrationApproval() {
        return expoRegistrationApproval;
    }

    public void setExpoRegistrationApproval(boolean expoRegistrationApproval) {
        this.expoRegistrationApproval = expoRegistrationApproval;
    }

    public boolean isSpeakerRegistrationApproval() {
        return speakerRegistrationApproval;
    }

    public void setSpeakerRegistrationApproval(boolean speakerRegistrationApproval) {
        this.speakerRegistrationApproval = speakerRegistrationApproval;
    }

    public EventFormat getEventFormat() {
        return eventFormat;
    }

    public void setEventFormat(EventFormat eventFormat) {
        this.eventFormat = eventFormat;
    }

    public boolean isShowSessionByTimezone() {
        return showSessionByTimezone;
    }

    public void setShowSessionByTimezone(boolean showSessionByTimezone) {
        this.showSessionByTimezone = showSessionByTimezone;
    }
    public boolean isEventPrivate() { return isEventPrivate; }
    public void setEventPrivate(boolean eventPrivate) { isEventPrivate = eventPrivate; }

    public boolean isReviewerRegistrationApproval() {
        return reviewerRegistrationApproval;
    }

    public void setReviewerRegistrationApproval(boolean reviewerRegistrationApproval) {
        this.reviewerRegistrationApproval = reviewerRegistrationApproval;
    }
}
