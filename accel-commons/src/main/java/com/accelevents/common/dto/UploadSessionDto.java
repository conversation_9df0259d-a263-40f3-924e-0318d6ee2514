package com.accelevents.common.dto;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumSessionStatus;
import com.accelevents.domain.enums.SessionTypeFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.TimeZoneUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import java.util.List;

import static com.accelevents.utils.Constants.LOCAL_DATE_FORMAT;

public class UploadSessionDto {

    @Schema(description ="Session id")
    private Long sessionId;

    @Schema(description ="Title")
    @NotNull
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String title;

    @Schema(description ="Format")
    @NotNull
    private EnumSessionFormat format;

    @Schema(description ="Session Type Format")
    private SessionTypeFormat sessionTypeFormat=SessionTypeFormat.HYBRID;

    @Schema(description = "Session start date in yyyy/MM/dd HH:mm format")
    @NotNull
    private String startDateTime;

    @Schema(description = "Session end date in yyyy/MM/dd HH:mm format")
    @NotNull
    private String endDateTime;

    @Schema(description ="Description")
    private String description;

    @Schema(description ="Session capacity")
    private int capacity;

    @Schema(description ="inValid session Title")
    private boolean inValidTitle = false;

    @Schema(description ="inValid session startTime")
    private boolean inValidSessionTime = false;

    @Schema(description ="inValid session format")
    private boolean inValidFormat = false;

    @Schema(description ="inValid session capacity")
    private boolean inValidCapacity = false;

    @Schema(description ="title Error Message for session")
    private String titleErrorMessage;

    @Schema(description ="time Error Message for session")
    private String timeErrorMessage;

    @Schema(description ="format Error Message for session")
    private String formatErrorMessage;

    @Schema(description ="capacity Error Message for session")
    private String capacityErrorMessage;


    @Schema(description ="main state session conflict")
    private boolean mainStageSessionConflict = false;

    @Schema(description ="Main stage session conflict message")
    private String SessionConflictErrorMessage;

    @Schema(description ="short description of session")
    private boolean isShortDescription;


    @Schema(description = "short_description_of_session")
    @Size(max = 150, message = "Only 150 Characters are allowed")
    private String shortDescriptionOfSession;

    @Schema(description ="Invalid session location")
    private boolean isInvalidSessionLocation=false;

    @Schema(description = "session location")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String sessionLocation;

    // this is original location id which is used to get data from EF as from DTOS
    @Schema(description = "session location id")
    private Long location;

    @Schema(description ="Session Tags")
    private String tags;

    @Schema(description ="Invalid Tags")
    private boolean invalidTags=false;

    @Schema(description ="Session Tracks")
    private String tracks;

    @Schema(description ="Invalid Tracks")
    private boolean invalidTracks=false;

    private boolean isTitleUpdated = false;

    private boolean isSessionTypeUpdated = false;

    private boolean invalidSessionTypeFormat = false;

    private String previousFormat;

    @Schema(description ="Tag and Track Error Message")
    private String tagAndTrackErrorMessage;

    private boolean isChatEnabled;

    @Schema(description ="Session Location Name")
    private String locationName;

    @Schema(description ="Session Location Name")
    private Long locationId;

    private String setlocationErrorMessage;

    @Schema(description ="Primary Speakers (comma-separated email addresses)")
    private String primarySpeakers;

    @Schema(description ="Secondary Speakers (comma-separated email addresses)")
    private String secondarySpeakers;

    @Schema(description ="Invalid Speakers (duplicate emails in primary and secondary)")
    private boolean invalidSpeakers = false;

    @Schema(description ="Speaker validation error message")
    private String speakerErrorMessage;
    
    private EnumSessionStatus status;

    @Schema(description = "Custom Attributes")
    private List<AttributeKeyValueDto> customAttributes;

    @Schema(description ="Custom Attributes Error Message")
    private List<AttributeKeyValueDto>  customAttributesErrorMessage;

    @Schema(description = "Survey Id")
    private Long surveyId;

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public UploadSessionDto() {
    }

    public UploadSessionDto(Session session) {
        title = session.getTitle();
        format = session.getFormat();
        startDateTime = null !=  session.getStartTime() ? DateUtils.getDateString(session.getStartTime(), LOCAL_DATE_FORMAT)  : null;
        endDateTime = null !=  session.getEndTime()  ? DateUtils.getDateString(session.getEndTime(), LOCAL_DATE_FORMAT)  : null;
        description = session.getDescription();
        capacity = session.getCapacity();
        sessionId = session.getId();
        shortDescriptionOfSession = session.getShortDescriptionOfSession();
        sessionTypeFormat=session.getSessionTypeFormat();
        isChatEnabled = session.isChatEnabled();
        locationId = session.getLocationId();
    }

    public Session updateExistingEntity(Event event,Session existSession){
        existSession.setId(getSessionId());
        existSession.setTitle(getTitle());
        existSession.setFormat(getFormat());
        existSession.setStartTime(TimeZoneUtil.getDateInUTC(getStartDateTime(),  event.getEquivalentTimeZone(), LOCAL_DATE_FORMAT));
        existSession.setEndTime(TimeZoneUtil.getDateInUTC(getEndDateTime(),  event.getEquivalentTimeZone(), LOCAL_DATE_FORMAT));
        existSession.setDescription(getDescription());
        existSession.setEventId(event.getEventId());
        existSession.setCapacity(getCapacity());
        existSession.setShortDescriptionOfSession(getShortDescriptionOfSession());
        existSession.setSessionTypeFormat(getSessionTypeFormat());
        existSession.setLocationId(getLocationId());
        existSession.setLocationId(getLocationId());
        return existSession;
    }

    public Session updateEntity(Event event){
        Session session = new Session();
        session.setTitle(getTitle());
        session.setFormat(getFormat());
        if(StringUtils.isNotBlank(getStartDateTime())){
            session.setStartTime(TimeZoneUtil.getDateInUTC(getStartDateTime(),  event.getEquivalentTimeZone(), LOCAL_DATE_FORMAT));
        }
        if (StringUtils.isNotBlank(getEndDateTime())) {
            session.setEndTime(TimeZoneUtil.getDateInUTC(getEndDateTime(),  event.getEquivalentTimeZone(), LOCAL_DATE_FORMAT));
        }
        session.setDescription(getDescription());
        session.setEventId(event.getEventId());
        session.setStatus(EnumSessionStatus.VISIBLE);
        session.setTicketTypesThatCanBeRegistered(Constants.STRING_EMPTY);
        session.setCapacity(getCapacity());
        session.setShortDescriptionOfSession(getShortDescriptionOfSession());
        session.setSessionTypeFormat(getSessionTypeFormat());
        session.setLocationId(getLocationId());
        return session;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public EnumSessionFormat getFormat() {
        return format;
    }

    public void setFormat(EnumSessionFormat format) {
        this.format = format;
    }

    public String getStartDateTime() {
        return startDateTime;
    }

    public void setStartDateTime(String startDateTime) {
        this.startDateTime = startDateTime;
    }

    public String getEndDateTime() {
        return endDateTime;
    }

    public void setEndDateTime(String endDateTime) {
        this.endDateTime = endDateTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCapacity() {
        return capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public boolean isInValidTitle() {
        return inValidTitle;
    }

    public void setInValidTitle(boolean inValidTitle) {
        this.inValidTitle = inValidTitle;
    }

    public boolean isInValidFormat() {
        return inValidFormat;
    }

    public void setInValidFormat(boolean inValidFormat) {
        this.inValidFormat = inValidFormat;
    }

    public boolean isInValidCapacity() {
        return inValidCapacity;
    }

    public void setInValidCapacity(boolean inValidCapacity) {
        this.inValidCapacity = inValidCapacity;
    }

    public boolean  isInValidSessionTime() {
        return inValidSessionTime;
    }

    public void setInValidSessionTime(boolean inValidSessionTime) {
        this.inValidSessionTime = inValidSessionTime;
    }

    public String getTitleErrorMessage() {
        return titleErrorMessage;
    }

    public void setTitleErrorMessage(String titleErrorMessage) {
        this.titleErrorMessage = titleErrorMessage;
    }

    public String getTimeErrorMessage() {
        return timeErrorMessage;
    }

    public void setTimeErrorMessage(String timeErrorMessage) {
        this.timeErrorMessage = timeErrorMessage;
    }

    public String getFormatErrorMessage() {
        return formatErrorMessage;
    }

    public void setFormatErrorMessage(String formatErrorMessage) {
        this.formatErrorMessage = formatErrorMessage;
    }

    public String getCapacityErrorMessage() {
        return capacityErrorMessage;
    }

    public void setCapacityErrorMessage(String capacityErrorMessage) {
        this.capacityErrorMessage = capacityErrorMessage;
    }

    public boolean isMainStageSessionConflict() {
        return mainStageSessionConflict;
    }

    public void setMainStageSessionConflict(boolean mainStageSessionConflict) {
        this.mainStageSessionConflict = mainStageSessionConflict;
    }

    public String getSessionConflictErrorMessage() {
        return SessionConflictErrorMessage;
    }

    public void setSessionConflictErrorMessage(String sessionConflictErrorMessage) {
        SessionConflictErrorMessage = sessionConflictErrorMessage;
    }

    public boolean isShortDescription() {
        return isShortDescription;
    }

    public void setShortDescription(boolean shortDescription) {
        this.isShortDescription = shortDescription;
    }

    public String getShortDescriptionOfSession() {
        return shortDescriptionOfSession;
    }

    public void setShortDescriptionOfSession(String shortDescriptionOfSession) {
        this.shortDescriptionOfSession = shortDescriptionOfSession;
    }

    public SessionTypeFormat getSessionTypeFormat() {
        return sessionTypeFormat;
    }

    public void setSessionTypeFormat(SessionTypeFormat sessionTypeFormat) {
        this.sessionTypeFormat = sessionTypeFormat;
    }

    public boolean isInvalidSessionLocation() {
        return isInvalidSessionLocation;
    }

    public void setInvalidSessionLocation(boolean invalidSessionLocation) {
        isInvalidSessionLocation = invalidSessionLocation;
    }

    public String getSessionLocation() {
        return sessionLocation;
    }

    public void setSessionLocation(String sessionLocation) {
        this.sessionLocation = sessionLocation;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getTracks() {
        return tracks;
    }

    public void setTracks(String tracks) {
        this.tracks = tracks;
    }

    public boolean isInvalidTags() {
        return invalidTags;
    }

    public void setInvalidTags(boolean invalidTags) {
        this.invalidTags = invalidTags;
    }

    public boolean isInvalidTracks() {
        return invalidTracks;
    }

    public void setInvalidTracks(boolean invalidTracks) {
        this.invalidTracks = invalidTracks;
    }

    public String getTagAndTrackErrorMessage() {
        return tagAndTrackErrorMessage;
    }

    public void setTagAndTrackErrorMessage(String tagAndTrackErrorMessage) {
        this.tagAndTrackErrorMessage = tagAndTrackErrorMessage;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public boolean isTitleUpdated() {
        return isTitleUpdated;
    }

    public void setTitleUpdated(boolean titleUpdated) {
        isTitleUpdated = titleUpdated;
    }

    public boolean isSessionTypeUpdated() {
        return isSessionTypeUpdated;
    }

    public void setSessionTypeUpdated(boolean sessionTypeUpdated) {
        isSessionTypeUpdated = sessionTypeUpdated;
    }

    public String getPreviousFormat() {
        return previousFormat;
    }

    public void setPreviousFormat(String previousFormat) {
        this.previousFormat = previousFormat;
    }

    public boolean isChatEnabled() {
        return isChatEnabled;
    }

    public void setChatEnabled(boolean chatEnabled) {
        isChatEnabled = chatEnabled;
    }

    public boolean isInvalidSessionTypeFormat() {
        return invalidSessionTypeFormat;
    }

    public void setInvalidSessionTypeFormat(boolean invalidSessionTypeFormat) {
        this.invalidSessionTypeFormat = invalidSessionTypeFormat;
    }

    public Long getLocation() {
        return location;
    }

    public void setLocation(Long location) {
        this.location = location;
    }

    public String getSetlocationErrorMessage() {
        return setlocationErrorMessage;
    }

    public void setSetlocationErrorMessage(String setlocationErrorMessage) {
        this.setlocationErrorMessage = setlocationErrorMessage;
    }

    public String getPrimarySpeakers() {
        return primarySpeakers;
    }

    public void setPrimarySpeakers(String primarySpeakers) {
        this.primarySpeakers = primarySpeakers;
    }

    public String getSecondarySpeakers() {
        return secondarySpeakers;
    }

    public void setSecondarySpeakers(String secondarySpeakers) {
        this.secondarySpeakers = secondarySpeakers;
    }

    public boolean isInvalidSpeakers() {
        return invalidSpeakers;
    }

    public void setInvalidSpeakers(boolean invalidSpeakers) {
        this.invalidSpeakers = invalidSpeakers;
    }

    public String getSpeakerErrorMessage() {
        return speakerErrorMessage;
    }

    public void setSpeakerErrorMessage(String speakerErrorMessage) {
        this.speakerErrorMessage = speakerErrorMessage;
    }

    public List<AttributeKeyValueDto> getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(List<AttributeKeyValueDto> customAttributes) {
        this.customAttributes = customAttributes;
    }

    public List<AttributeKeyValueDto> getCustomAttributesErrorMessage() {
        return customAttributesErrorMessage;
    }

    public void setCustomAttributesErrorMessage(List<AttributeKeyValueDto> customAttributesErrorMessage) {
        this.customAttributesErrorMessage = customAttributesErrorMessage;
    }

    public EnumSessionStatus getStatus() {
        return status;
    }

    public void setStatus(EnumSessionStatus status) {
        this.status = status;
    }
}
