package com.accelevents.common.dto;

import com.accelevents.domain.SurveyConfiguration;
import com.accelevents.domain.SurveyQuestions;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Schema(description ="Survey Config DTO")
public class SurveyConfigDto implements Serializable {

    @Schema(description ="Survey Id")
    private long surveyId;

    @Schema(description ="Event Id")
    private long eventId;

    @Schema(description ="Survey Name")
    private String surveyName;

    @Schema(description ="Survey Title")
    private String surveyTitle;

    @Schema(description ="Description")
    private String description;

    @Schema(description ="Survey Questions")
    private List<SurveyQuestionsDto> surveyQuestions;

    @Schema(description ="Survey Headline")
    private String surveyHeadline;

    @Schema(description ="Survey Feedback Message")
    private String surveyFeedbackMessage;

    @Schema(description = "is require login from user to participate in the survey")
    private boolean isLoginRequired = true;

    @Schema(description = "is survey quiz enabled to designate correct answer for the questions.")
    private boolean isSurveyQuizEnabled = false;

    private List<SurveySessionsBasicDto> sessions;

    private List<SurveySessionsDto> surveySessions;

    @Schema(description = "Survey set as Default and Auto Link for new Sessions")
    private boolean isDefaultSurvey = false;

    public boolean isDefaultSurvey() {
        return isDefaultSurvey;
    }

    public void setDefaultSurvey(boolean defaultSurvey) {
        isDefaultSurvey = defaultSurvey;
    }

    public SurveyConfigDto(){
    }

    public SurveyConfigDto(SurveyConfiguration surveyConfiguration, List<SurveyQuestions> surveyQuestionsList) {
        this.setSurveyId(surveyConfiguration.getSurveyId());
        this.setSurveyTitle(surveyConfiguration.getSurveyTitle());
        this.setSurveyName(surveyConfiguration.getSurveyName());
        this.setEventId(surveyConfiguration.getEventId());
        this.setDescription(surveyConfiguration.getDescription());
        this.setSurveyHeadline(surveyConfiguration.getSurveyHeadline());
        this.setSurveyFeedbackMessage(surveyConfiguration.getSurveyFeedbackMessage());
        this.setLoginRequired(surveyConfiguration.isLoginRequired());
        this.setSurveyQuizEnabled(surveyConfiguration.isSurveyQuizEnabled());
        this.setDefaultSurvey(surveyConfiguration.isDefaultSurvey());
        List<SurveyQuestionsDto> surveyQuestionsDtos = new ArrayList<>();
        for(SurveyQuestions surveyQuestion : surveyQuestionsList){
            if(surveyQuestion.isShow()) {
                surveyQuestionsDtos.add(new SurveyQuestionsDto(surveyQuestion));
            }
        }
        this.setSurveyQuestions(surveyQuestionsDtos);
    }

    public long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(long surveyId) {
        this.surveyId = surveyId;
    }

    public long getEventId() {
        return eventId;
    }

    public void setEventId(long eventId) {
        this.eventId = eventId;
    }

    public String getSurveyName() {
        return surveyName;
    }

    public void setSurveyName(String surveyName) {
        this.surveyName = surveyName;
    }

    public String getSurveyTitle() {
        return surveyTitle;
    }

    public void setSurveyTitle(String surveyTitle) {
        this.surveyTitle = surveyTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<SurveyQuestionsDto> getSurveyQuestions() {
        return surveyQuestions;
    }

    public void setSurveyQuestions(List<SurveyQuestionsDto> surveyQuestions) {
        this.surveyQuestions = surveyQuestions;
    }

    public String getSurveyHeadline() {return surveyHeadline;}

    public void setSurveyHeadline(String surveyHeadline) {this.surveyHeadline = surveyHeadline;}

    public String getSurveyFeedbackMessage() {return surveyFeedbackMessage;}

    public void setSurveyFeedbackMessage(String surveyFeedbackMessage) {this.surveyFeedbackMessage = surveyFeedbackMessage;}

    public boolean isLoginRequired() {
        return isLoginRequired;
    }

    public void setLoginRequired(boolean loginRequired) {
        isLoginRequired = loginRequired;
    }

    public boolean isSurveyQuizEnabled() {
        return isSurveyQuizEnabled;
    }

    public void setSurveyQuizEnabled(boolean surveyQuizEnabled) {
        isSurveyQuizEnabled = surveyQuizEnabled;
    }

    public List<SurveySessionsBasicDto> getSessions() {
        return sessions;
    }

    public void setSessions(List<SurveySessionsBasicDto> sessions) {
        this.sessions = sessions;
    }

    public List<SurveySessionsDto> getSurveySessions() {
        return surveySessions;
    }

    public void setSurveySessions(List<SurveySessionsDto> surveySessions) {
        this.surveySessions = surveySessions;
    }

    public SurveyConfiguration createEntity() {
        SurveyConfiguration surveyConfiguration = new SurveyConfiguration();
        surveyConfiguration.setCreatedDate(new Date());
        updateEntity(surveyConfiguration);
        return surveyConfiguration;
    }

    public SurveyConfiguration updateEntity(SurveyConfiguration surveyConfiguration) {
        surveyConfiguration.setDescription(getDescription());
        surveyConfiguration.setSurveyName(getSurveyName());
        surveyConfiguration.setSurveyTitle(getSurveyTitle());
        surveyConfiguration.setUpdateDate(new Date());
        surveyConfiguration.setSurveyHeadline(getSurveyHeadline());
        surveyConfiguration.setSurveyFeedbackMessage(getSurveyFeedbackMessage());
        surveyConfiguration.setLoginRequired(isLoginRequired());
        surveyConfiguration.setSurveyQuizEnabled(isSurveyQuizEnabled());
        return surveyConfiguration;
    }
}
