package com.accelevents.common.dto;

import com.accelevents.domain.Staff;
import com.accelevents.domain.User;
import com.accelevents.staff.dto.StaffTicketTypeDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import net.minidev.json.annotate.JsonIgnore;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.stream.Collectors;

import static com.accelevents.utils.ApiMessages.*;
@Schema(description ="staffDetail")
public class StaffDetail extends UserBasicDto {

	@Schema(description =ROLE_STAFF_DESC)
	@NotBlank
	@Pattern(regexp = "admin|staff|whitelabeladmin|exhibitoradmin|leadretriever|eventandorgadmin|eventcoordinator|eventbudgetowner|eventplanner",
			message = "role must be either admin or staff or whitelabeladmin")
	private String role;

	@Schema(description =ALLOW_TO_BOOK_MEETING)
	private boolean allowToBookMeeting;

    @Schema(description =ALLOW_DIRECT_MESSAGES)
    private boolean allowDirectMessages;

    @Schema(description ="isAllowAttendeeAccess")
    private boolean isLoggedInAtVEH;

    @Schema(description ="isAllowAttendeeAccess")
    private boolean isAllowAttendeeAccess = false;

    @Schema(description ="Ticket types for allow attendee access to staff")
    private List<StaffTicketTypeDto> ticketTypesForStaff;

	@JsonIgnore
	private Long id;

    @Schema(description ="is billing contact in chargebee")
    private boolean billingContact = false;

    private String profileImage;

    private Long userId;

    private  String photo;

    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String company;
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String title;

	public StaffDetail() {
        super();
	}

	public StaffDetail(User user, Staff staff) {
		super(user);
        this.photo = user.getPhoto();
        this.userId = user.getUserId();
		this.role = staff.getRole().name();
		this.allowToBookMeeting = staff.isAllowToBookMeeting();
        this.allowDirectMessages = staff.isAllowDirectMessages();
        this.isAllowAttendeeAccess = staff.getAllowAttendeeAccess();
	}

	public StaffDetail(Staff staff) {
		super(staff.getUser());
		User user = staff.getUser();
		this.role = staff.getRole().name();
		this.id = staff.getId();
		this.allowToBookMeeting = staff.isAllowToBookMeeting();
        this.allowDirectMessages = staff.isAllowDirectMessages();
        this.isAllowAttendeeAccess = staff.getAllowAttendeeAccess();

	}

    public StaffDetail(Staff staff, Long previousLoggedInCount, Map<Long,Boolean> ticketTypeIds) {
        super(staff.getUser());
        User user = staff.getUser();
        this.role = staff.getRole().name();
        this.id = staff.getId();
        this.allowToBookMeeting = staff.isAllowToBookMeeting();
        this.allowDirectMessages = staff.isAllowDirectMessages();
        this.isAllowAttendeeAccess = staff.getAllowAttendeeAccess();
        this.isLoggedInAtVEH = previousLoggedInCount!=null && previousLoggedInCount>0? true : false;
        if(!CollectionUtils.isEmpty(ticketTypeIds)) {
            this.ticketTypesForStaff = ticketTypeIds.entrySet().stream().sorted(Comparator.comparing(e -> e.getKey()))//NOSONAR
                    .map(e -> new StaffTicketTypeDto(staff.getUserId(),e.getKey(), e.getValue())).collect(Collectors.toList());
        }else {
            this.ticketTypesForStaff = null;
        }
    }

    public StaffDetail(Staff staff, Long previousLoggedInCount, Map<Long,Boolean> ticketTypeIds, Map<Long, String> ticketTypeNames) {
        super(staff.getUser());
        User user = staff.getUser();
        this.role = staff.getRole().name();
        this.id = staff.getId();
        this.allowToBookMeeting = staff.isAllowToBookMeeting();
        this.allowDirectMessages = staff.isAllowDirectMessages();
        this.isAllowAttendeeAccess = staff.getAllowAttendeeAccess();
        this.isLoggedInAtVEH = previousLoggedInCount!=null && previousLoggedInCount>0? true : false;
        if(!CollectionUtils.isEmpty(ticketTypeIds)) {
            this.ticketTypesForStaff = ticketTypeIds.entrySet().stream().sorted(Comparator.comparing(e -> e.getKey()))//NOSONAR
                    .map(e -> new StaffTicketTypeDto(staff.getUserId(),e.getKey(), e.getValue(), ticketTypeNames.get(e.getKey()))).collect(Collectors.toList());
        }else {
            this.ticketTypesForStaff = null;
        }
    }

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}


	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public boolean isAllowToBookMeeting() {
		return allowToBookMeeting;
	}

	public void setAllowToBookMeeting(boolean allowToBookMeeting) {
		this.allowToBookMeeting = allowToBookMeeting;
	}

    public boolean isAllowDirectMessages() {
        return allowDirectMessages;
    }

    public void setAllowDirectMessages(boolean allowDirectMessages) {
        this.allowDirectMessages = allowDirectMessages;
    }

    public Boolean getLoggedInAtVEH() { return isLoggedInAtVEH; }

    public void setLoggedInAtVEH(Boolean loggedInAtVEH) { isLoggedInAtVEH = loggedInAtVEH; }

    public List<StaffTicketTypeDto> getTicketTypesForStaff() { return ticketTypesForStaff; }

    public void setTicketTypesForStaff(List<StaffTicketTypeDto> ticketTypesForStaff) { this.ticketTypesForStaff = ticketTypesForStaff; }

    public boolean isAllowAttendeeAccess() { return isAllowAttendeeAccess; }

    public void setAllowAttendeeAccess(boolean allowAttendeeAccess) { isAllowAttendeeAccess = allowAttendeeAccess; }

    public boolean isBillingContact() {
        return billingContact;
    }

    public void setBillingContact(boolean billingContact) {
        this.billingContact = billingContact;
    }

    public String getProfileImage() {
        return profileImage;
    }

    public void setProfileImage(String profileImage) {
        this.profileImage = profileImage;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPhoto() { return photo; }

    public void setPhoto(String photo) { this.photo = photo; }

    public @Size(max = 255, message = "Only 255 Characters are allowed") String getCompany() {
        return company;
    }

    public void setCompany(@Size(max = 255, message = "Only 255 Characters are allowed") String company) {
        this.company = company;
    }

    public @Size(max = 255, message = "Only 255 Characters are allowed") String getTitle() {
        return title;
    }

    public void setTitle(@Size(max = 255, message = "Only 255 Characters are allowed") String title) {
        this.title = title;
    }
}
