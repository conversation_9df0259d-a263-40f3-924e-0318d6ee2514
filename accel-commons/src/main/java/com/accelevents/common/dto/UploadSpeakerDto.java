package com.accelevents.common.dto;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

public class UploadSpeakerDto {

    @Schema(description = "First Name")
    @NotNull
    @Size(max = Constants.NAME_SIZE, message = Constants.FIRST_NAME_SIZE_MSG)
    private String firstName;

    @Schema(description = "Last Name")
    @NotNull
    @Size(max = Constants.NAME_SIZE, message = Constants.LAST_NAME_SIZE_MSG)
    private String lastName;

    @Schema(description = "Email")
    @NotNull
    @Size(max = Constants.EMAIL_SIZE, message = Constants.EMAIL_SIZE_MSG)
    private String email;

    @Schema(description = "Pronouns")
    private String pronouns;

    @Schema(description = "Title")
    private String title;

    @Schema(description = "Company")
    private String company;

    @Schema(description = "Bio")
    private String bio;

    @Schema(description = "LinkedIn")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String linkedIn;

    @Schema(description = "Twitter")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String twitter;

    @Schema(description = "Instagram")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String instagram;

    @Schema(description = "Position")
    private double position;

    @Schema(description = "overrideProfileDetails")
    private Boolean overrideProfileDetails = false;

    @Schema(description = "emailAlreadyPresentFlag")
    private boolean isEmailAlreadyPresent;

    @Schema(description = "Primary Sessions IDs (comma separated)")
    private List<Long> primarySessions = new ArrayList<>();

    @Schema(description = "Secondary Sessions IDs (comma separated)")
    private List<Long> secondarySessions = new ArrayList<>();

    @Schema(description = "Indicates whether all session IDs are numeric or not.")
    private boolean isInvalidSessionIds;

    @Schema(description = "")
    private boolean isPrimarySessionIdIsInvalid;

    @Schema(description = "")
    private boolean isSecondarySessionIdIsInvalid;

    @Schema(description = "")
    private boolean isSessionIdsIsConflicts;

    @Schema(description = "")
    private boolean speakerIdIsInvalid;


    public boolean isNumbericSessionIds() {
        return isInvalidSessionIds;
    }

    public void setNumbericSessionIds(boolean numbericSessionIds) {
        isInvalidSessionIds = numbericSessionIds;
    }

    public boolean isPrimarySessionIdIsInvalid() {
        return isPrimarySessionIdIsInvalid;
    }

    public void setPrimarySessionIdIsInvalid(boolean primarySessionIdIsInvalid) {
        isPrimarySessionIdIsInvalid = primarySessionIdIsInvalid;
    }

    public boolean isSecondarySessionIdIsInvalid() {
        return isSecondarySessionIdIsInvalid;
    }

    public void setSecondarySessionIdIsInvalid(boolean secondarySessionIdIsInvalid) {
        isSecondarySessionIdIsInvalid = secondarySessionIdIsInvalid;
    }

    public boolean isSessionIdsIsConflicts() {
        return isSessionIdsIsConflicts;
    }

    public void setSessionIdsIsConflicts(boolean sessionIdsIsConflicts) {
        isSessionIdsIsConflicts = sessionIdsIsConflicts;
    }

    // For now this field is use in Zapier need to integrate with CSV in future.
    private String profileImage;

    private String operation = RecordStatus.CREATE.name();

    private Long speakerId;

    private List<AttributeKeyValueDto> attributeKeyValueDtos;

    public List<Long> getPrimarySessions() {
        return primarySessions;
    }

    public void setPrimarySessions(List<Long> primarySessions) {
        this.primarySessions = primarySessions;
    }

    public List<Long> getSecondarySessions() {
        return secondarySessions;
    }

    public void setSecondarySessions(List<Long> secondarySessions) {
        this.secondarySessions = secondarySessions;
    }

    public UploadSpeakerDto() {
    }

    public UploadSpeakerDto(Speaker speaker) {
        firstName = speaker.getFirstName();
        lastName = speaker.getLastName();
        email = speaker.getEmail();
        pronouns = speaker.getPronouns();
        title = speaker.getTitle();
        company = speaker.getCompany();
        bio = speaker.getBio();
        linkedIn = speaker.getLinkedIn();
        instagram = speaker.getInstagram();
        twitter = speaker.getTwitter();
        overrideProfileDetails = speaker.getAllowOverrideDetails() != null ? speaker.getAllowOverrideDetails() : false;
        speakerId = speaker.getId();
    }

    public UploadSpeakerDto(Speaker speaker,List<AttributeKeyValueDto> attributeKeyValueDtos) {
        this(speaker);
        this.attributeKeyValueDtos = attributeKeyValueDtos;
    }

    public UploadSpeakerDto(String firstName,String lastName,String email, String pronouns, String title, String company, String bio,String linkedIn,String twitter,String instagram,String overrideProfileDetails,boolean isEmailAlreadyPresent,List<Long> primarySessions,List<Long> secondarySessions,Long speakerId,List<AttributeKeyValueDto> attributeKeyValueDtoList
            , boolean IsNumbericSessionIds,boolean isPrimarySessionIdIsValid,boolean isSecondarySessionIdIsValid,boolean isSessionIdsIsNotConflicts,boolean speakerIdIsInvalid) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.pronouns = pronouns;
        this.title = title;
        this.company = company;
        this.bio = bio;
        this.linkedIn = linkedIn;
        this.twitter = twitter;
        this.instagram = instagram;
        this.overrideProfileDetails = overrideProfileDetails != null ? overrideProfileDetails.equalsIgnoreCase(Constants.Y_STRING) : false;
        this.isEmailAlreadyPresent=isEmailAlreadyPresent;
        this.primarySessions = primarySessions != null ? primarySessions : new ArrayList<>();
        this.secondarySessions = secondarySessions != null ? secondarySessions : new ArrayList<>();
        this.speakerId = speakerId;
        this.attributeKeyValueDtos = attributeKeyValueDtoList;
        this.isInvalidSessionIds = IsNumbericSessionIds;
        this.isPrimarySessionIdIsInvalid = isPrimarySessionIdIsValid;
        this.isSecondarySessionIdIsInvalid = isSecondarySessionIdIsValid;
        this.isSessionIdsIsConflicts = isSessionIdsIsNotConflicts;
        this.speakerIdIsInvalid = speakerIdIsInvalid;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getLinkedIn() {
        return linkedIn;
    }

    public void setLinkedIn(String linkedIn) {
        this.linkedIn = linkedIn;
    }

    public String getTwitter() {
        return twitter;
    }

    public void setTwitter(String twitter) {
        this.twitter = twitter;
    }

    public String getInstagram() {
        return instagram;
    }

    public void setInstagram(String instagram) {
        this.instagram = instagram;
    }

    public double getPosition() {
        return position;
    }

    public void setPosition(double position) {
        this.position = position;
    }

    public String getPronouns() {
        return pronouns;
    }

    public void setPronouns(String pronouns) {
        this.pronouns = pronouns;
    }

    public Boolean getOverrideProfileDetails() {
        return overrideProfileDetails;
    }

    public void setIsEmailAlreadyPresent(boolean isEmailAlreadyPresent) {
        this.isEmailAlreadyPresent = isEmailAlreadyPresent;
    }

    public boolean getIsEmailAlreadyPresent() {
        return isEmailAlreadyPresent;
    }

    public void setOverrideProfileDetails(Boolean overrideProfileDetails) {
        this.overrideProfileDetails = overrideProfileDetails;
    }

    public String getProfileImage() {
        return profileImage;
    }

    public void setProfileImage(String profileImage) {
        this.profileImage = profileImage;
    }

    public Speaker createEntity(Event event, User speakerUser) {
        Speaker speaker = new Speaker();
        speaker.setEventId(event.getEventId());
        speaker.setUserId(speakerUser.getUserId());
        return updateEntity(speaker);
    }

    public Speaker updateEntity(Speaker speaker) {
        speaker.setFirstName(getFirstName());
        speaker.setLastName(getLastName());
        speaker.setEmail(getEmail());
        speaker.setPronouns(getPronouns());
        speaker.setTitle(getTitle());
        speaker.setCompany(getCompany());
        speaker.setBio(getBio());
        speaker.setLinkedIn(getLinkedIn());
        speaker.setTwitter(getTwitter());
        speaker.setInstagram(getInstagram());
        speaker.setPosition(getPosition());
        speaker.setAllowOverrideDetails(false);
        if (null != speakerId) {
            speaker.setId(speakerId);
        }
        return speaker;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Long getSpeakerId() {
        return speakerId;
    }

    public void setSpeakerId(Long speakerId) {
        this.speakerId = speakerId;
    }

    public List<AttributeKeyValueDto> getAttributeKeyValueDtos() {
        return attributeKeyValueDtos;
    }

    public void setAttributeKeyValueDtos(List<AttributeKeyValueDto> attributeKeyValueDtos) {
        this.attributeKeyValueDtos = attributeKeyValueDtos;
    }

    public boolean isSpeakerIdIsInvalid() {
        return speakerIdIsInvalid;
    }

    public void setSpeakerIdIsInvalid(boolean speakerIdIsInvalid) {
        this.speakerIdIsInvalid = speakerIdIsInvalid;
    }
}