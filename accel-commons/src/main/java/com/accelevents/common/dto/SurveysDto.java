package com.accelevents.common.dto;


import com.accelevents.domain.SurveyConfiguration;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class SurveysDto implements Serializable {

    private Long surveyId;

    private Long eventId;

    private String surveyName;

    private Date createdDate;

    private Date updatedDate;

    private long submissions;

    private boolean isDefaultSurvey = false;

    private List<SurveySessionsDto> surveySessions;

    public boolean isDefaultSurvey() {
        return isDefaultSurvey;
    }

    public void setDefaultSurvey(boolean defaultSurvey) {
        isDefaultSurvey = defaultSurvey;
    }

    public SurveysDto(){

    }

    public SurveysDto(SurveyConfiguration surveyConfiguration) {
        this.surveyId = surveyConfiguration.getSurveyId();
        this.eventId = surveyConfiguration.getEventId();
        this.createdDate = surveyConfiguration.getCreatedDate();
        this.surveyName = surveyConfiguration.getSurveyName();
        this.updatedDate = surveyConfiguration.getUpdateDate();
        this.isDefaultSurvey = surveyConfiguration.isDefaultSurvey();
    }

    public SurveysDto(long surveyId, long submissions){
        this.surveyId = surveyId;
        this.submissions  = submissions;
    }

    public SurveysDto(Long surveyId, String surveyName) {
        this.surveyId = surveyId;
        this.surveyName = surveyName;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public long getSubmissions() {
        return submissions;
    }

    public void setSubmissions(long submissions) {
        this.submissions = submissions;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getSurveyName() {
        return surveyName;
    }

    public void setSurveyName(String surveyName) {
        this.surveyName = surveyName;
    }

    public List<SurveySessionsDto> getSurveySessions() {
        return surveySessions;
    }

    public void setSurveySessions(List<SurveySessionsDto> surveySessions) {
        this.surveySessions = surveySessions;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }
}
