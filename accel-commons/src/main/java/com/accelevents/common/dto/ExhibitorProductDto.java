package com.accelevents.common.dto;

import com.accelevents.domain.enums.DiscountType;
import com.accelevents.domain.exhibitors.ExhibitorProduct;
import com.accelevents.exceptions.NotAcceptableException;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Schema
public class ExhibitorProductDto implements Serializable {

    private static final long serialVersionUID = 459082016019242936L;

    @Schema(description = "Product id")
    private long id;

    @Schema(description ="Product name")
    @NotBlank(message = "Product Name Can Not Be Empty.")
    @Size(max = 40, message = "Only 40 Characters are allowed.")
    private String name;

    @Schema(description ="Product description")
    private String description;

    @Schema(description ="Product price")
    @NotNull
    @Min(value = 0, message = "price should be zero or greater than zero.")
    private double price;

    @Schema(description ="Product images")
    private List<ImageLocationDto> images;

    @Schema(description ="Discount amount")
    @NotNull
    @Min(value = 0, message = "Discount amount should be zero or greater than zero.")
    private double discountAmount = 0;

    @Schema(description ="Discount type, accepted values are PERCENTAGE or FLAT")
    private DiscountType discountType = DiscountType.PERCENTAGE;

    @Schema(description ="Product position")
    private Double position;

    @Schema(description ="Shopify Buy Button")
    private String shopifyBuyButton;

    @Schema(description ="Product Buy Button Link")
    private String productBuyButtonLink;

    public ExhibitorProductDto() {
    }

    public ExhibitorProductDto(ExhibitorProduct exhibitorProduct) {
        this.setId(exhibitorProduct.getId());
        this.setDescription(exhibitorProduct.getDescription());
        this.setName(exhibitorProduct.getName());
        this.setPrice(exhibitorProduct.getPrice());
        this.setImages(Collections.emptyList());
        this.setDiscountAmount(exhibitorProduct.getDiscountAmount());
        this.setDiscountType(exhibitorProduct.getDiscountType());
        this.setPosition(exhibitorProduct.getPosition());
        this.setShopifyBuyButton(exhibitorProduct.getShopifyBuyButton());
        this.setProductBuyButtonLink(exhibitorProduct.getProductBuyButtonLink());
    }

    public ExhibitorProductDto(long id, String name, String description, double price, List<ImageLocationDto> images,
                               double discountAmount, DiscountType discountType, Double position, String shopifyBuyButton, String productBuyButtonLink) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.price = price;
        this.images = images;
        this.discountAmount = discountAmount;
        this.discountType = discountType;
        this.position = position;
        this.shopifyBuyButton = shopifyBuyButton;
        this.productBuyButtonLink = productBuyButtonLink;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public List<ImageLocationDto> getImages() {
        return images;
    }

    public void setImages(List<ImageLocationDto> images) {
        this.images = images;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public DiscountType getDiscountType() {
        return discountType;
    }

    public void setDiscountType(DiscountType discountType) {
        this.discountType = discountType;
    }

    public Double getPosition() {
        return position;
    }

    public void setPosition(Double position) {
        this.position = position;
    }

    public String getShopifyBuyButton() {
        return shopifyBuyButton;
    }

    public void setShopifyBuyButton(String shopifyBuyButton) {
        this.shopifyBuyButton = shopifyBuyButton;
    }

    public String getProductBuyButtonLink() {
        return productBuyButtonLink;
    }

    public void setProductBuyButtonLink(String productBuyButtonLink) {
        this.productBuyButtonLink = productBuyButtonLink;
    }

    public ExhibitorProduct toEntity() {
        if (DiscountType.PERCENTAGE.equals(this.getDiscountType()) && this.getDiscountAmount() > 100) {
            throw new NotAcceptableException(NotAcceptableException.ExhibitorProductExceptionMsg.INVALID_PRODUCT_PERCENTAGE_DISCOUNT_AMOUNT);
        } else if (DiscountType.FLAT.equals(this.getDiscountType()) && this.getDiscountAmount() > this.getPrice()) {
            throw new NotAcceptableException(NotAcceptableException.ExhibitorProductExceptionMsg.INVALID_PRODUCT_FLAT_DISCOUNT_AMOUNT);
        }
        ExhibitorProduct exhibitorProduct = new ExhibitorProduct();
        exhibitorProduct.setDescription(this.getDescription());
        exhibitorProduct.setName(this.getName());
        exhibitorProduct.setPrice(this.getPrice());
        exhibitorProduct.setDiscountAmount(this.getDiscountAmount());
        exhibitorProduct.setDiscountType(this.getDiscountType());
        exhibitorProduct.setPosition(this.getPosition());
        exhibitorProduct.setShopifyBuyButton(this.getShopifyBuyButton());
        exhibitorProduct.setProductBuyButtonLink(this.getProductBuyButtonLink());
        return exhibitorProduct;
    }

    public String toString(){
        return "ExhibitorProductDto{" +
                "name=" + name +
                ", description= " +description+
                ", price=" +price+
                ", images=" +images+
                ", discountAmount" +discountAmount+
                ", discountType" +discountType+
                ", position=" +position+
                ", shopifyBuyButton=" +shopifyBuyButton+
                ", productBuyButtonLink=" +productBuyButtonLink+
                "}";
    }
}
