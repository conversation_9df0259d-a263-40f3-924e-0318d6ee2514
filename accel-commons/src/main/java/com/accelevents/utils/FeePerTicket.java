package com.accelevents.utils;

import com.accelevents.domain.TicketingType;
import com.accelevents.dto.SalesTaxFeeDto;
import com.accelevents.dto.StripeDTO;
import com.accelevents.dto.TicketTransferDueAmountDetailsDTO;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.StripeUtil;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.spreedly.dto.SpreedlyGatewayType;

import static com.accelevents.utils.GeneralUtils.convertCommaSeparatedToListLong;
import static com.accelevents.utils.GeneralUtils.getRoundValue;

public class FeePerTicket {

    // Input
    private TicketingType ticketingType;
    private StripeDTO stripeDTO;
    private SalesTaxFeeDto salesTaxFeeDto;
    private double ticketPrice;
    private int totalPaidTicketTypes;
    private int qtyOfCurrentTicketType;
    private boolean isInternational;
    private double vatTaxRate;
    int numberOfTicketsPerTable = 1;
    private double aeFee;
    private double wlAFee;
    private double wlBFee;
    private double ccFlatFee;
    private double salesTax;
    private double payLaterSalesTax;
    private double totalPayable;
    private double totalPayablePayLater;
    private double totalAdjustFee;
    private double processingFee;
    double capAmount=0;
    private double vatTax;
    private double payLaterVatTax;
    TicketTransferDueAmountDetailsDTO ticketTransferDueAmountDetailsDTO;
    private boolean isPayLaterPayment;
   private PLATEFORM_FEES_TYPE plateFormFeesType ;



    public FeePerTicket(TicketingType ticketingType,
                        boolean isPayLaterPayment,
                        StripeDTO stripeDTO,
                        SalesTaxFeeDto salesTaxFeeDto,
                        double ticketPrice,
                        int totalPaidTicketTypes,
                        int qtyOfCurrentTicketType,
                        boolean isInternational,
                        double capAmount, double vatTaxRate, PLATEFORM_FEES_TYPE PLATEFORM_FEES_TYPE) {
        this.ticketingType = ticketingType;
        this.ticketPrice = ticketPrice;
        this.stripeDTO = stripeDTO;
        this.salesTaxFeeDto = salesTaxFeeDto;
        this.totalPaidTicketTypes = totalPaidTicketTypes;
        this.qtyOfCurrentTicketType = qtyOfCurrentTicketType;
        this.isInternational = isInternational;
        this.capAmount=capAmount;
        numberOfTicketsPerTable = !TicketBundleType.INDIVIDUAL_TICKET.equals(ticketingType.getBundleType())
                ? ticketingType.getNumberOfTicketPerTable()
                : 1 ;
        this.vatTaxRate = vatTaxRate;
        this.isPayLaterPayment = isPayLaterPayment;
        this.plateFormFeesType = PLATEFORM_FEES_TYPE;
    }

    public double getAeFee() {
        return aeFee;
    }

    private void setAeFee(double aeFee){
        this.aeFee = aeFee;
    }

    public double getWlAFee() {
        return wlAFee;
    }

    private void setWlAFee(double wlAFee){
        this.wlAFee = wlAFee;
    }

    public double getWlBFee() {
        return wlBFee;
    }

    private void setWlBFee(double wlBFee){
        this.wlBFee = wlBFee;
    }

    public double getSalesTaxFee() {
        return salesTax;
    }

    private void setSalesTax(double salesTax){
        this.salesTax = salesTax;
    }

    public double getTotalPayable() {
        return  totalPayable;
    }

    public void setTotalPayable(double totalPayable){
        this.totalPayable = totalPayable;
    }


    public double getTotalPayablePayLater() {
        return totalPayablePayLater;
    }

    public void setTotalPayablePayLater(double totalPayablePayLater) {
        this.totalPayablePayLater = totalPayablePayLater;
    }

    public double getTotalAdjustFee() {
        return totalAdjustFee;
    }

    private void setTotalAdjustFee(double totalAdjustFee){
        this.totalAdjustFee = totalAdjustFee;
    }

    public double getCcFlatFeePerTicket() {
        return ccFlatFee;
    }

    private void setCCFlatPerTicket(double ccFlatFeePerTicket) {
        this.ccFlatFee = ccFlatFeePerTicket;
    }

    public double getTicketPrice() {
        return ticketPrice;
    }

    public double getProcessingFee() {
        return getRoundValue(processingFee > 0 ? processingFee : 0.0);
    }

    public double getServiceFee(){
        return (getAeFee()+getWlBFee()+getWlAFee());
    }

    public double getExpectedStripeNetSale(){
        return getTotalPayable() - getServiceFee() - getProcessingFee();
    }

    public double getActualNetSale(){
        return getExpectedStripeNetSale() - getSalesTaxFee();
    }

    public double getCapAmount() {
        return capAmount;
    }

    public void setCapAmount(double capAmount) {
        this.capAmount = capAmount;
    }

    public double getVatTaxRate() {
        return vatTaxRate;
    }

    public void setVatTaxRate(double vatTaxRate) {
        this.vatTaxRate = vatTaxRate;
    }

    public double getVatTax() {
        return vatTax;
    }

    public void setVatTax(double vatTax) {
        this.vatTax = vatTax;
    }


    public double getPayLaterSalesTax() {
        return payLaterSalesTax;
    }

    public void setPayLaterSalesTax(double payLaterSalesTax) {
        this.payLaterSalesTax = payLaterSalesTax;
    }

    public double getPayLaterVatTax() {
        return payLaterVatTax;
    }

    public void setPayLaterVatTax(double payLaterVatTax) {
        this.payLaterVatTax = payLaterVatTax;
    }

    public FeePerTicket invoke() {
        if(ticketPrice <=0){
            return this;
        }

        double aeFee = calculateAeFee(this.ticketingType, this.numberOfTicketsPerTable, this.ticketPrice);
        double wlAFee = calculateWlAFee(this.ticketingType, this.numberOfTicketsPerTable, this.ticketPrice);
        double wlBFee = calculateWlBFee(this.ticketingType, this.numberOfTicketsPerTable, this.ticketPrice);
        double ccFlatPerTicket = calculateCCFlatPerTicket(this.stripeDTO, this.totalPaidTicketTypes, this.qtyOfCurrentTicketType);

        double ticketPriceWithAEFee = calculateTicketPriceWithServiceFee(this.ticketingType, this.ticketPrice, aeFee, wlAFee, wlBFee);

        double ticketPriceWithAEFeeAndStripeFee = calculateStripePayableTicketPrice(this.ticketingType, this.stripeDTO, this.ticketPrice,
                ticketPriceWithAEFee + ccFlatPerTicket, this.isInternational);

		double[] salesTax = calculateSalesTax(this.ticketingType, this.salesTaxFeeDto, this.ticketPrice, ticketPriceWithAEFeeAndStripeFee);

        double ticketPriceWithAEFeeAndStripeFeeAndSalesTax = calculateStripePayableTicketPrice(this.ticketingType, this.stripeDTO, this.ticketPrice,
                ticketPriceWithAEFee + ccFlatPerTicket + salesTax[0], this.isInternational);

        double vatTax = calculateVatTax(this.vatTaxRate, ticketPriceWithAEFeeAndStripeFeeAndSalesTax, true);
        double ticketPriceWithAEFeeAndStripeFeeAndSalesTaxAndVatTax;

        double totalAdjustFees;
        double totalPayable;
        double totalPayablePayLater;

        double amountPlustCCFlatFee = 0;

        if (Boolean.TRUE.equals(ticketingType.isPassFeeVatToBuyer())) {
            amountPlustCCFlatFee = ticketPriceWithAEFee + ccFlatPerTicket + salesTax[0] + vatTax;
        } else {
            amountPlustCCFlatFee = ticketPriceWithAEFee + ccFlatPerTicket + salesTax[0];
        }

        ticketPriceWithAEFeeAndStripeFeeAndSalesTaxAndVatTax = calculateStripePayableTicketPrice(this.ticketingType, this.stripeDTO, this.ticketPrice,
                amountPlustCCFlatFee, this.isInternational);

        totalAdjustFees = calculateTotalAdjustFee(this.ticketingType, this.stripeDTO, this.numberOfTicketsPerTable,
                amountPlustCCFlatFee, ticketPriceWithAEFeeAndStripeFeeAndSalesTaxAndVatTax);


        totalPayable = calculateTotalPayable(this.ticketingType, this.stripeDTO, ccFlatPerTicket, ticketPriceWithAEFee,
                salesTax[0], totalAdjustFees, Boolean.TRUE.equals(ticketingType.isPassFeeVatToBuyer()) ? vatTax : 0, this.isInternational);

        if(isPayLaterPayment){
            /*Calculate sale tax and vat tax for Pay Later (Without CC fess)*/
            /*totalAdjustFees is used for adjust cc fees so no need to pass while pay later option calculate */

            double[] salesTaxForPayLater = calculateSalesTax(this.ticketingType, this.salesTaxFeeDto, this.ticketPrice, this.ticketPrice);

            double ticketPriceWithAEFeeAndSalesTaxForPayLater = this.ticketPrice  + salesTaxForPayLater[0];

            double vatTaxForPayLater = calculateVatTax(this.vatTaxRate, ticketPriceWithAEFeeAndSalesTaxForPayLater, false);
            setPayLaterVatTax(vatTaxForPayLater);
            setPayLaterSalesTax(salesTaxForPayLater[0]);

            totalPayablePayLater =  this.ticketPrice + salesTaxForPayLater[0] + (Boolean.TRUE.equals(ticketingType.isPassFeeVatToBuyer()) ? vatTaxForPayLater : 0);

        } else {
            totalPayablePayLater = totalPayable;
        }

        setAeFee(aeFee);
        setWlAFee(wlAFee);
        setWlBFee(wlBFee);
        setCCFlatPerTicket(ccFlatPerTicket);
        setSalesTax(salesTax[1]);
        setTotalPayable(totalPayable);
        /*totalPayablePayLater is used for pay later option*/
        setTotalPayablePayLater(totalPayablePayLater);
        setTotalAdjustFee(totalAdjustFees);
        setVatTax(vatTax);
        setProcessingFee(totalAdjustFees);
        return this;
    }

    private double calculateTotalPayable(TicketingType ticketingType, StripeDTO stripeDTO, double ccFlatFeePerTicket,
                                         double ticketPriceWithServiceFee, double salesTax, double totalAdjustFee, double vatTax, boolean isInternational) {
        double totalPayable;
        if(ticketingType.isPassfeetobuyer()){
            totalPayable = stripePayableAmount(stripeDTO, ticketPriceWithServiceFee + ccFlatFeePerTicket + salesTax + totalAdjustFee + vatTax, isInternational);
        } else {
            totalPayable = ticketPriceWithServiceFee + salesTax + vatTax;
        }

        return  totalPayable;
    }

    private double calculateCCFlatPerTicket(StripeDTO stripeDTO, int totalPaidTicketTypes, int qtyOfCurrentTicketType) {
       if(totalPaidTicketTypes == 0 ){
              return 0;
       }
        return  stripeDTO.getCCFlatFee() / totalPaidTicketTypes / qtyOfCurrentTicketType;
    }

    private double calculateAeFee(TicketingType ticketingType, int numberOfTicketsPerTable, double ticketPrice) {
        if(this.plateFormFeesType.equals(PLATEFORM_FEES_TYPE.NONE)){
            return 0;
        }
        double aeFeesPercentage=  ((ticketingType.getAeFeePercentage() * ticketPrice) / 100);
        if(capAmount>0 && capAmount<aeFeesPercentage){
            aeFeesPercentage=capAmount;
        }
        if(this.plateFormFeesType.equals(PLATEFORM_FEES_TYPE.PERCENTAGE)){
            return aeFeesPercentage;
        }
        return aeFeesPercentage+(ticketingType.getAeFeeFlat()*numberOfTicketsPerTable);
    }

    private double calculateWlAFee(TicketingType ticketingType, int numberOfTicketsPerTable, double ticketPrice) {
        if(this.plateFormFeesType.equals(PLATEFORM_FEES_TYPE.NONE)){
            return 0;
        }
        double wlAFeesPercentage=  ((ticketingType.getWlAFeePercentage() * ticketPrice) / 100);
        if(this.plateFormFeesType.equals(PLATEFORM_FEES_TYPE.PERCENTAGE)){
            return wlAFeesPercentage;
        }
        return (ticketingType.getWlAFeeFlat()*numberOfTicketsPerTable) + wlAFeesPercentage;
    }

    private double calculateWlBFee(TicketingType ticketingType, int numberOfTicketsPerTable, double ticketPrice) {
        if(this.plateFormFeesType.equals(PLATEFORM_FEES_TYPE.NONE)){
            return 0;
        }
        double wlBFeesPercentage=  ((ticketingType.getWlBFeePercentage() * ticketPrice) / 100);
        if(this.plateFormFeesType.equals(PLATEFORM_FEES_TYPE.PERCENTAGE)){
            return wlBFeesPercentage;
        }
        return (ticketingType.getWlBFeeFlat()*numberOfTicketsPerTable) + wlBFeesPercentage;
    }

    private double calculateTicketPriceWithServiceFee(TicketingType ticketingType, double ticketPrice, double aeFee, double wlAFee, double wlBFee){
        if (ticketingType.isPassfeetobuyer()) {
            return ticketPrice + aeFee + wlAFee + wlBFee;
        } else {
            return ticketPrice;
        }
    }

    private double calculateStripePayableTicketPrice(TicketingType ticketingType, StripeDTO stripeDTO, double ticketPrice,
                                                     double amountPlusCCFlatFee, boolean isInternational){
        if (ticketingType.isPassfeetobuyer()) {
            return stripePayableAmount(stripeDTO, amountPlusCCFlatFee, isInternational);
        } else {
            return ticketPrice;
        }
    }

    private double stripePayableAmount(StripeDTO stripeDTO, double amountPlusCCFlatFee, boolean isInternational) {
        return (amountPlusCCFlatFee)
                / (1 - percentToDecimal(isInternational ? (stripeDTO.getCCPercentageFee() + 1) : stripeDTO.getCCPercentageFee()));
    }


    private void setProcessingFee(double totalAdjustFees) {
        // remove totalAdjustFees from processing processing fees as it was making issue while store the processing fees
//        processingFee = ccFlatFee + (0.029 * totalPayable) + totalAdjustFees;
        if (EnumPaymentGateway.PAYFLOW.name().equals(stripeDTO.getPaymentGateway())) {
            processingFee = ccFlatFee;
        } else if(SpreedlyGatewayType.getSpreedlyGatewayTypeList().contains(stripeDTO.getPaymentGateway())) {
            processingFee = ccFlatFee + (percentToDecimal(stripeDTO.getCCPercentageFee()) * totalPayable) + totalAdjustFees;
        } else {
            processingFee = ccFlatFee + (0.029 * totalPayable);
        }
    }

    private double[] calculateSalesTax(TicketingType ticketingType, SalesTaxFeeDto salesTaxFeeDto, double ticketPrice, double stripePayableTicketPrice) {
        double salesTaxToPass = 0.0;
        double salesTaxToShow = 0.0;
        // https://stripe.com/docs/billing/taxes/tax-rates
        if (isTicketTypeHaveTaxApplicable(ticketingType, salesTaxFeeDto)) {
            if (salesTaxFeeDto.passTaxToBuyer()) {
                // Apply tax excluded formula
                salesTaxToShow = salesTaxToPass = getSalesTaxPassed(stripePayableTicketPrice, salesTaxFeeDto);
            } else {
                // TAX $ (INCLUDED) Amount - Amount / (1 + TaxRate)
                if (ticketingType.isPassfeetobuyer() && !salesTaxFeeDto.getAbsorbTax()) {
                    // Since host have configured tax included in ticket price, Use Tax included formula for ticket price, TAX $ (INCLUDED)
                    double salesTaxOnTicketPrice = getSalesTaxAbsorbed(ticketPrice, salesTaxFeeDto);

                    // But fee is passed, so tax of fee should always be with excluded formula
                    double totalPassedFee = stripePayableTicketPrice - ticketPrice;
                    salesTaxToPass = getSalesTaxPassed(totalPassedFee, salesTaxFeeDto);
                    salesTaxToShow = salesTaxOnTicketPrice + salesTaxToPass;
                } else {

                    // Apply Tax included formula
                    salesTaxToShow = getSalesTaxAbsorbed(stripePayableTicketPrice, salesTaxFeeDto);
                }
            }
        }
        return new double[] {salesTaxToPass, salesTaxToShow};
    }

    private double calculateVatTax(double vatTaxRate, double stripePayableTicketPrice, boolean isCCApplyOnVat) {
        double vatTaxToShow = 0.0;
        if(NumberUtils.isNumberGreaterThanZero(vatTaxRate)) {
            vatTaxToShow = getVatTaxPassed(stripePayableTicketPrice, vatTaxRate);
        }
        if (ticketTransferDueAmountDetailsDTO != null) {
            double remainingVatTaxFee = ticketTransferDueAmountDetailsDTO.getDueVatTaxOnOldTicket().doubleValue();
            // Remaining Vat tax on remainingAmount from old ticket type
            if (remainingVatTaxFee > 0d) {
                // IF isCCApplyOnVat then we calculate the CC percentage fee on the remainingVatTaxFee else we as it is.
                double dueVatTaxOnOldTicketTypeWithCCPercentage = isCCApplyOnVat ? calculateStripePayableTicketPrice(this.ticketingType, this.stripeDTO, remainingVatTaxFee,
                        remainingVatTaxFee, this.isInternational) : remainingVatTaxFee;
                vatTaxToShow += dueVatTaxOnOldTicketTypeWithCCPercentage;
            }
        }
        return vatTaxToShow;
    }

    public double getSalesTaxPassed(double amount, SalesTaxFeeDto salesTaxFeeDto){
        return amount * percentToDecimal(salesTaxFeeDto.getSalesTaxRate());
    }

    public double getVatTaxPassed(double amount, double vatTaxRate){
        return amount * percentToDecimal(vatTaxRate);
    }

    public double getSalesTaxAbsorbed(double amount, SalesTaxFeeDto salesTaxFeeDto){
        return amount - (amount / (1 + percentToDecimal(salesTaxFeeDto.getSalesTaxRate())));
    }

    private double calculateTotalAdjustFee(TicketingType ticketingType, StripeDTO stripeDTO, int numberOfTicketsPerTable,
                                           double amountPlusSalesPlusCCFlat, double totalPayable){
        double totalAdjustFee = 0.0;
        if(ticketingType.isPassfeetobuyer()) {
            if (!TicketBundleType.INDIVIDUAL_TICKET.equals(ticketingType.getBundleType())) {
                checkNumberOfTicket(numberOfTicketsPerTable, ticketingType.getBundleType());
                double preCCPerTableTicket = stripePayableAmount(stripeDTO, amountPlusSalesPlusCCFlat, isInternational)
                        / numberOfTicketsPerTable;
                double adjustAeFeePerTableTicket = StripeUtil.getFeeAdjustAmount(GeneralUtils.getRoundUP(preCCPerTableTicket),
                        stripeDTO.getCCPercentageFee(),
                        amountPlusSalesPlusCCFlat / numberOfTicketsPerTable);
                totalAdjustFee = adjustAeFeePerTableTicket * numberOfTicketsPerTable;
            } else {
                totalAdjustFee = StripeUtil.getFeeAdjustAmount(GeneralUtils.getRoundUP(totalPayable), stripeDTO.getCCPercentageFee(),
                        amountPlusSalesPlusCCFlat);
            }
        }
        return totalAdjustFee;
    }

    private void checkNumberOfTicket(int numberOfTicketsPerTable, TicketBundleType ticketBundleType) {
        if (numberOfTicketsPerTable <= 0){
            NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.NUMBER_OF_TICKET_MUST_BE_GREATER_THAN_ZERO;
            exception.setErrorMessage(Constants.NUMBER_OF_TICKET.replace("${ticketBundleType}", ticketBundleType.toString()));
            exception.setDeveloperMessage(Constants.NUMBER_OF_TICKET.replace("${ticketBundleType}", ticketBundleType.toString()));
            throw new NotAcceptableException(exception);
        }
    }

    private boolean isTicketTypeHaveTaxApplicable(TicketingType ticketingType, SalesTaxFeeDto salesTaxFeeDto) {
        return salesTaxFeeDto!=null
                && salesTaxFeeDto.getTicketingTypeIds()!=null
                && convertCommaSeparatedToListLong(salesTaxFeeDto.getTicketingTypeIds()).contains(ticketingType.getId());
    }

    private double percentToDecimal(double value) {
        return value / 100;
    }

    public enum PLATEFORM_FEES_TYPE {
        FULL,
        PERCENTAGE,
        NONE
    }

    public FeePerTicket withTicketTransferDueAmountDetailsDTO(TicketTransferDueAmountDetailsDTO ticketTransferDueAmountDetailsDTO) {
        this.ticketTransferDueAmountDetailsDTO = ticketTransferDueAmountDetailsDTO;
        return this;
    }
}
