package com.accelevents.utils;

import com.accelevents.domain.TicketingCoupon;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.DiscountType;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Discount {

    private long numberOfUsageLeft;
    private int totalPurchasedTicket;
    private TicketingCoupon ticketingCoupon;
    private TicketingType ticketingType;
    private int totalTickets;
    private double finalDiscount = 0;
    private boolean is100PerDiscountOnAllTickets;
    private boolean is100PercentOff;
    private double ticketPrice;
    private long countOfTicketOnWhichCouponApplied = 0;
    private int numberOfFullDiscountedTickets = 0;
    private double totalTicketPrice;
    private double totalAppliedDiscountAmount;

    public Discount(long numberOfUsageLeft, TicketingCoupon ticketingCoupon, TicketingType ticketingType, int totalPurchasedTicket, int totalTickets, double TotalTicketPrice, double totalAppliedDiscountAmount) {
        this.numberOfUsageLeft = numberOfUsageLeft;
        this.totalPurchasedTicket = totalPurchasedTicket;
        this.ticketingCoupon = ticketingCoupon;
        this.ticketingType = ticketingType;
        this.totalTickets = totalTickets;
        this.ticketPrice = ticketingType.getPrice();
        this.totalTicketPrice = TotalTicketPrice;
        this.totalAppliedDiscountAmount = totalAppliedDiscountAmount;
    }

    public double getFinalDiscount() {
        return finalDiscount;
    }

    public boolean isIs100PerDiscountOnAllTickets() {
        return is100PerDiscountOnAllTickets;
    }

    public long getCountOfTicketOnWhichCouponApplied() {
        return countOfTicketOnWhichCouponApplied;
    }

    public int getNumberOfFullDiscountedTickets(){
        return numberOfFullDiscountedTickets;
    }

    public double getTotalTicketPrice() {
        return totalTicketPrice;
    }

    public void setTotalTicketPrice(double totalTicketPrice) {
        this.totalTicketPrice = totalTicketPrice;
    }

    public Discount invoke() {
        boolean isForAllTickets = StringUtils.isBlank(ticketingCoupon.getEventTicketTypeId());
        // this should only applicable if coupon code is for ticket type
        List<String> couponTicketTypeList = new ArrayList<String>();
        if (!isForAllTickets) {
            couponTicketTypeList.addAll(Arrays.asList(ticketingCoupon.getEventTicketTypeId().split(Constants.STRING_COMMA)));
        }
        if (couponTicketTypeList.contains(String.valueOf(ticketingType.getId())) || isForAllTickets) {
            finalDiscount = (ticketingCoupon.getAmount() > totalAppliedDiscountAmount) ? (ticketingCoupon.getAmount() - totalAppliedDiscountAmount) : 0;
            // PERCENTAGE
            getPercentageDiscountType();

            if(numberOfUsageLeft < totalPurchasedTicket && TicketingCoupon.ApplicableTo.PER_TICKET.equals(ticketingCoupon.getApplicableTo())){
                countOfTicketOnWhichCouponApplied = numberOfUsageLeft;
            } else {
                countOfTicketOnWhichCouponApplied = totalPurchasedTicket;
            }

            if (TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo()) && ! DiscountType.PERCENTAGE.equals(ticketingCoupon.getDiscountType()) ) {
                finalDiscount = (ticketPrice * finalDiscount) / totalTicketPrice;
            }

            if (finalDiscount >= ticketPrice || is100PercentOff) {
                checkAllTicketsOf100PercentageDiscount();
            }
        }
        return this;
    }

    private void checkAllTicketsOf100PercentageDiscount() {
        finalDiscount = ticketPrice;
        numberOfFullDiscountedTickets = (int) countOfTicketOnWhichCouponApplied;

        if(countOfTicketOnWhichCouponApplied == totalPurchasedTicket){
            is100PerDiscountOnAllTickets = true;
        }
    }

    private void getPercentageDiscountType() {
        if (DiscountType.PERCENTAGE.equals(ticketingCoupon.getDiscountType())) {
            if (ticketingCoupon.getAmount() == 100) {
                is100PercentOff = true;
            } else {
                finalDiscount = (ticketPrice * ticketingCoupon.getAmount()) / 100;
            }
        }
    }
}
