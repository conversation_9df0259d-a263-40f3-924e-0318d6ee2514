package com.accelevents.session_speakers.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(content = JsonInclude.Include.NON_NULL)
@Schema(description ="uploaded Session Location response container")
public class UploadSessionLocationResponseContainer {

    @Schema(description = "message to be displayed")
    private String message;

    public UploadSessionLocationResponseContainer(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
