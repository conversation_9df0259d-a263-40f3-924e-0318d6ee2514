package com.accelevents.session_speakers.dto;

public class SessionConsolidatedVideoAnalyticsDto {

    private Long sessionId;
    private Integer totalUsers;
    private Integer totalLiveView;
    private Integer totalRecordingView;
    private Double totalWatchTime;
    private Double liveWatchTime;
    private Double recordingWatchTime;
    private Double avgLiveWatchTime;

    // Default constructor
    public SessionConsolidatedVideoAnalyticsDto() {}

    // Getters and Setters
    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public Integer getTotalUsers() {
        return totalUsers;
    }

    public void setTotalUsers(Integer totalUsers) {
        this.totalUsers = totalUsers;
    }

    public Integer getTotalLiveView() {
        return totalLiveView;
    }

    public void setTotalLiveView(Integer totalLiveView) {
        this.totalLiveView = totalLiveView;
    }

    public Integer getTotalRecordingView() {
        return totalRecordingView;
    }

    public void setTotalRecordingView(Integer totalRecordingView) {
        this.totalRecordingView = totalRecordingView;
    }

    public Double getTotalWatchTime() {
        return totalWatchTime;
    }

    public void setTotalWatchTime(Double totalWatchTime) {
        this.totalWatchTime = totalWatchTime;
    }

    public Double getLiveWatchTime() {
        return liveWatchTime;
    }

    public void setLiveWatchTime(Double liveWatchTime) {
        this.liveWatchTime = liveWatchTime;
    }

    public Double getRecordingWatchTime() {
        return recordingWatchTime;
    }

    public void setRecordingWatchTime(Double recordingWatchTime) {
        this.recordingWatchTime = recordingWatchTime;
    }

    public Double getAvgLiveWatchTime() {
        return avgLiveWatchTime;
    }

    public void setAvgLiveWatchTime(Double avgLiveWatchTime) {
        this.avgLiveWatchTime = avgLiveWatchTime;
    }
}
