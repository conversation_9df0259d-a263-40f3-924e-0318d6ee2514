package com.accelevents.session_speakers.dto;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;

import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;

public class AttendeeSession {

	private Long sessionId;
	private String title;
	private String startTime;
	private String endTime;
	private boolean attended;
	private String status;
    private String engagement;


	@JsonIgnore
	private Date startDate;
	@JsonIgnore
	private Date endDate;

	public Long getSessionId() {
		return sessionId;
	}

	public void setSessionId(Long sessionId) {
		this.sessionId = sessionId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(Event event) {
		this.startTime = null != this.startDate ? getDateInLocal(this.startDate
				, event.getEquivalentTimeZone(), null) : null;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(Event event) {
		this.endTime = null != this.endDate ? getDateInLocal(this.endDate
				, event.getEquivalentTimeZone(), null) : null;
	}

	public boolean isAttended() {
		return attended;
	}

	public void setAttended(boolean attended) {
		this.attended = attended;
	}

	public Date getStartDate() {
		return startDate;
	}

	public Date getEndDate() {
		return endDate;
	}


	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

    public String getEngagement() {
        return engagement;
    }

    public void setEngagement(String engagement) {
        this.engagement = engagement;
    }

    public AttendeeSession(Long sessionId, String title, Date startDate, Date endDate, EnumUserSessionStatus status, boolean attended) {
        this.sessionId = sessionId;
        this.title = title;
        this.startDate = startDate;
        this.endDate = endDate;
        this.status = status.toString();
        this.attended = attended;
    }

    public AttendeeSession(Long sessionId,
                           String title,
                           Date startDate,
                           Date endDate,
                           boolean attended) {
		this.sessionId = sessionId;
		this.title = title;
		this.startDate = startDate;
		this.endDate = endDate;
//		startTime = null != startDate ?
//				getDateInLocal(startDate, event.getTimezoneId(), null) : null; // NOSONAR
//		endTime = null != endDate ? getDateInLocal(endDate
//				, event.getTimezoneId(), null) : null;  // NOSONAR
		this.attended = attended;
	}
}
