package com.accelevents.session_speakers.dto;

public class EventConsolidatedVideoAnalyticsDto {

    private Integer totalSessions;
    private Integer totalRegisteredAttendees;
    private Double avgRegisteredAttendeePerSession;
    private Double sessionTotalDuration;
    private Double avgDurationPerSession;
    //Dynamo DB Data
    private Integer totalViewers;
    private Integer totalLiveView;
    private Integer totalRecordingView;
    private Double avgRecordingViewsPerSession;
    private Double totalWatchTime;
    private Double liveWatchTime;
    private Double avgLiveWatchTimePerSession;
    private Double recordingWatchTime;
    private Double avgWatchTimePerUser;

    // Default constructor
    public EventConsolidatedVideoAnalyticsDto() {}

    // Getters and Setters


    public Integer getTotalSessions() {
        return totalSessions;
    }

    public void setTotalSessions(Integer totalSessions) {
        this.totalSessions = totalSessions;
    }

    public Integer getTotalRegisteredAttendees() {
        return totalRegisteredAttendees;
    }

    public void setTotalRegisteredAttendees(Integer totalRegisteredAttendees) {
        this.totalRegisteredAttendees = totalRegisteredAttendees;
    }

    public Double getAvgRegisteredAttendeePerSession() {
        return avgRegisteredAttendeePerSession;
    }

    public void setAvgRegisteredAttendeePerSession(Double avgSessionRegisteredAttendee) {
        this.avgRegisteredAttendeePerSession = avgSessionRegisteredAttendee;
    }

    public Double getSessionTotalDuration() {
        return sessionTotalDuration;
    }

    public void setSessionTotalDuration(Double sessionTotalDuration) {
        this.sessionTotalDuration = sessionTotalDuration;
    }

    public Double getAvgDurationPerSession() {
        return avgDurationPerSession;
    }

    public void setAvgDurationPerSession(Double avgDurationPerSession) {
        this.avgDurationPerSession = avgDurationPerSession;
    }

    public Integer getTotalViewers() {
        return totalViewers;
    }

    public void setTotalViewers(Integer totalViewers) {
        this.totalViewers = totalViewers;
    }

    public Integer getTotalLiveView() {
        return totalLiveView;
    }

    public void setTotalLiveView(Integer totalLiveView) {
        this.totalLiveView = totalLiveView;
    }

    public Integer getTotalRecordingView() {
        return totalRecordingView;
    }

    public void setTotalRecordingView(Integer totalRecordingView) {
        this.totalRecordingView = totalRecordingView;
    }

    public Double getAvgRecordingViewsPerSession() {
        return avgRecordingViewsPerSession;
    }

    public void setAvgRecordingViewsPerSession(Double avgRecordingViewsPerSession) {
        this.avgRecordingViewsPerSession = avgRecordingViewsPerSession;
    }

    public Double getTotalWatchTime() {
        return totalWatchTime;
    }

    public void setTotalWatchTime(Double totalWatchTime) {
        this.totalWatchTime = totalWatchTime;
    }

    public Double getLiveWatchTime() {
        return liveWatchTime;
    }

    public void setLiveWatchTime(Double liveWatchTime) {
        this.liveWatchTime = liveWatchTime;
    }

    public Double getAvgLiveWatchTimePerSession() {
        return avgLiveWatchTimePerSession;
    }

    public void setAvgLiveWatchTimePerSession(Double avgLiveWatchTimePerSession) {
        this.avgLiveWatchTimePerSession = avgLiveWatchTimePerSession;
    }

    public Double getRecordingWatchTime() {
        return recordingWatchTime;
    }

    public void setRecordingWatchTime(Double recordingWatchTime) {
        this.recordingWatchTime = recordingWatchTime;
    }

    public Double getAvgWatchTimePerUser() {
        return avgWatchTimePerUser;
    }

    public void setAvgWatchTimePerUser(Double avgWatchTime) {
        this.avgWatchTimePerUser = avgWatchTime;
    }
}
