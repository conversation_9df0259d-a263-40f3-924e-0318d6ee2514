package com.accelevents.session_speakers.dto;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.StreamProvider;
import com.accelevents.domain.enums.SubStreamProvider;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.dto.KeyValueDto;
import com.accelevents.dto.WaitingMediaDto;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Size;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class DisplayPortalStageBreakoutSessionDTO extends DisplayPortalCommonSessionDTO {

    @Schema(description ="accel events studio")
    private boolean accelEventsStudio;

    @Schema(description ="closed caption url")
    private  String closedCaptionUrl;

    @Schema(description ="closed caption height")
    private  int closedCaptionHeight;

    @Schema(description ="Description")
    @Size(max = 65535, message = "Only 65024 Characters are allowed")
    private String description;

    @Schema(description ="Direct video auto start")
    private boolean directVideoAutoStart;

    @Schema(description ="is poll enabled")
    private boolean pollEnabled;

    @Schema(description ="is ques and ans enabled")
    private boolean quesAndAnsEnabled;

    @Schema(description ="is display donation")
    private boolean displayDonation;

    @Schema(description ="Session's documents. key=Uploaded document's key and value as document name")
    private List<KeyValueDto> documentKeyValue;

    @Schema(description ="add links for session store in key value")
    private List<KeyValueDto> linkKeyValue;

    @Schema(description ="Session location")
    private String location;

    @Schema(description ="Password for zoom meeting")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String meetingPassword;

    @Schema(description ="RTMP URL")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String rtmpUrl;

    @Schema(description ="Expandable speaker List")
    private List<SpeakerDTO> speakerList;

    @Schema(description ="Stream provider")
    private StreamProvider streamProvider;

    @Schema(description ="Sub Stream provider for accelevents")
    private SubStreamProvider subStreamProvider;

    @Schema(description ="Stream Url")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String streamUrl;

    @Schema(description ="Stream key")
    @Size(max = 255, message = "Only 255 Characters are allowed")
    private String streamKey;

    @Schema(description ="Subtitle file URL")
    private String subTitleFileUrl;

    @Schema(description ="Tracks")
    private List<IdNameDto> tracks;

    @Schema(description ="is Hide Video Controls")
    private boolean hideVideoControls;

    @Schema(description ="record session")
    private boolean recordSession = true;

    @Schema(description ="is_qna_private")
    private boolean isQnAPrivate;

    @Schema(description ="enable_attendee_list")
    private boolean enableAttendeeList;

    @Schema(description ="Post Session Call To Action")
    private PostSessionCallToActionDto postSessionCallToActionJson;

    @Schema(description ="Enable Session Waiting Media")
    private boolean enableSessionWaitingMedia = false;

    @Schema(description ="Session Waiting Media Details")
    private WaitingMediaDto waitingMedia;

    @Schema(description ="Session Live Captioning")
    private CaptionsDto captions;

    @Schema(description ="Show Session Survey at Session Start")
    private boolean isSurveySessionStart;

    @Schema(description = "Video caption is enabled or not")
    private boolean enableVideoCaption;

    public DisplayPortalStageBreakoutSessionDTO() {
    }

    public DisplayPortalStageBreakoutSessionDTO(Session session, Event event, Optional<SessionDetails> sessionDetailsOptional, List<SpeakerDTO> speakerList, List<IdNameDto> tracks) {
        super(session,event);
        accelEventsStudio = session.isAccelEventsStudio();
        closedCaptionUrl = session.getClosedCaptionUrl();
        closedCaptionHeight = session.getClosedCaptionHeight();
        description = session.getDescription();
        directVideoAutoStart = session.isDirectVideoAutoStart();
        pollEnabled =session.isPollEnabled();
        quesAndAnsEnabled= session.isQuesAndAnsEnabled();
        displayDonation = session.isDisplayDonation();
        meetingPassword = session.getMeetingPassword();
        documentKeyValue = getListOfKeyValueDto(session.getDocuments());
        linkKeyValue = getListOfKeyValueDto(session.getLinks());
        if(session.getSessionLocation() != null && StringUtils.isNotBlank(session.getSessionLocation().getName())){
            location = session.getSessionLocation().getName();
        }
        else{
            location = Constants.STRING_EMPTY;
        }
        streamProvider = session.getStreamProvider();
        subStreamProvider = session.getSubStreamProvider();
        streamUrl = session.getStreamUrl();
        streamKey = session.getStreamKey();
        rtmpUrl = session.getRtmpUrl();
        if (sessionDetailsOptional.isPresent()) {
            SessionDetails sessionDetails = sessionDetailsOptional.get();
            subTitleFileUrl = sessionDetails.getSubTitleFileUrl();
            hideVideoControls = sessionDetails.getHideVideoControls();
            recordSession = sessionDetails.isRecordSession();
            isQnAPrivate = sessionDetails.isQnAPrivate();
            enableAttendeeList = sessionDetails.isEnableAttendeeList();
            postSessionCallToActionJson = sessionDetails.getPostSessionCallToActionJson() != null ? CommonUtil.getDtoPostCallToActionJson(sessionDetails.getPostSessionCallToActionJson()) : new PostSessionCallToActionDto();
            enableSessionWaitingMedia = sessionDetails.isEnableSessionWaitingMedia();
            waitingMedia = null != sessionDetails.getWaitingMedia() ? WaitingMediaDto.convertJSONToObject(sessionDetails.getWaitingMedia()) : new WaitingMediaDto();
            captions = null != sessionDetails.getCaptions() ? CaptionsDto.convertJSONToObject(sessionDetails.getCaptions()) : new CaptionsDto();
            isSurveySessionStart = sessionDetails.isSurveySessionStart();
            this.enableVideoCaption = sessionDetails.isEnableVideoCaption();
        }
        this.speakerList = speakerList;
        this.tracks = tracks;
    }

    public boolean isAccelEventsStudio() {
        return accelEventsStudio;
    }

    public void setAccelEventsStudio(boolean accelEventsStudio) {
        this.accelEventsStudio = accelEventsStudio;
    }

    public String getClosedCaptionUrl() {
        return closedCaptionUrl;
    }

    public void setClosedCaptionUrl(String closedCaptionUrl) {
        this.closedCaptionUrl = closedCaptionUrl;
    }

    public int getClosedCaptionHeight() { return closedCaptionHeight; }

    public void setClosedCaptionHeight(int closedCaptionHeight) { this.closedCaptionHeight = closedCaptionHeight; }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isDirectVideoAutoStart() {
        return directVideoAutoStart;
    }

    public void setDirectVideoAutoStart(boolean directVideoAutoStart) {
        this.directVideoAutoStart = directVideoAutoStart;
    }

    public boolean isPollEnabled() {
        return pollEnabled;
    }

    public void setPollEnabled(boolean pollEnabled) {
        this.pollEnabled = pollEnabled;
    }

    public boolean isQuesAndAnsEnabled() {
        return quesAndAnsEnabled;
    }

    public void setQuesAndAnsEnabled(boolean quesAndAnsEnabled) {
        this.quesAndAnsEnabled = quesAndAnsEnabled;
    }

    public boolean isDisplayDonation() {
        return displayDonation;
    }

    public void setDisplayDonation(boolean displayDonation) {
        this.displayDonation = displayDonation;
    }

    public List<KeyValueDto> getDocumentKeyValue() {
        return documentKeyValue;
    }

    public void setDocumentKeyValue(List<KeyValueDto> documentKeyValue) {
        this.documentKeyValue = documentKeyValue;
    }

    public List<KeyValueDto> getLinkKeyValue() {
        return linkKeyValue;
    }

    public void setLinkKeyValue(List<KeyValueDto> linkKeyValue) {
        this.linkKeyValue = linkKeyValue;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMeetingPassword() {
        return meetingPassword;
    }

    public void setMeetingPassword(String meetingPassword) {
        this.meetingPassword = meetingPassword;
    }

    public String getRtmpUrl() {
        return rtmpUrl;
    }

    public void setRtmpUrl(String rtmpUrl) {
        this.rtmpUrl = rtmpUrl;
    }

    public List<SpeakerDTO> getSpeakerList() {
        return speakerList;
    }

    public void setSpeakerList(List<SpeakerDTO> speakerList) {
        this.speakerList = speakerList;
    }

    public StreamProvider getStreamProvider() {
        return streamProvider;
    }

    public void setStreamProvider(StreamProvider streamProvider) {
        this.streamProvider = streamProvider;
    }

    public SubStreamProvider getSubStreamProvider() {
        return subStreamProvider;
    }

    public void setSubStreamProvider(SubStreamProvider subStreamProvider) {
        this.subStreamProvider = subStreamProvider;
    }

    public String getStreamUrl() {
        return streamUrl;
    }

    public void setStreamUrl(String streamUrl) {
        this.streamUrl = streamUrl;
    }

    public String getStreamKey() {
        return streamKey;
    }

    public void setStreamKey(String streamKey) {
        this.streamKey = streamKey;
    }

    public String getSubTitleFileUrl() {
        return subTitleFileUrl;
    }

    public void setSubTitleFileUrl(String subTitleFileUrl) {
        this.subTitleFileUrl = subTitleFileUrl;
    }

    public List<IdNameDto> getTracks() {
        return tracks;
    }

    public void setTracks(List<IdNameDto> tracks) {
        this.tracks = tracks;
    }

    private List<KeyValueDto> getListOfKeyValueDto(String jsonInString) {
        ObjectMapper mapper = new ObjectMapper();
        List<KeyValueDto> linkKeyValueDtos = new ArrayList<>();
        if(StringUtils.isNotBlank(jsonInString)){
            try {
                linkKeyValueDtos = Arrays.asList(mapper.readValue(jsonInString, KeyValueDto[].class));
            } catch (IOException e) {
            }
        }
        return linkKeyValueDtos;
    }

    public boolean isHideVideoControls() {
        return hideVideoControls;
    }

    public void setHideVideoControls(boolean hideVideoControls) {
        this.hideVideoControls = hideVideoControls;
    }

    public boolean isRecordSession() { return recordSession; }

    public void setRecordSession(boolean recordSession) { this.recordSession = recordSession; }

    public boolean isQnAPrivate() {
        return isQnAPrivate;
    }

    public void setQnAPrivate(boolean qnAPrivate) {
        isQnAPrivate = qnAPrivate;
    }

    public boolean isEnableAttendeeList() {
        return enableAttendeeList;
    }

    public void setEnableAttendeeList(boolean enableAttendeeList) {
        this.enableAttendeeList = enableAttendeeList;
    }

    public PostSessionCallToActionDto getPostSessionCallToActionJson() {
        return postSessionCallToActionJson;
    }

    public void setPostSessionCallToActionJson(PostSessionCallToActionDto postSessionCallToActionJson) {
        this.postSessionCallToActionJson = postSessionCallToActionJson;
    }

    public boolean isEnableSessionWaitingMedia() {
        return enableSessionWaitingMedia;
    }

    public void setEnableSessionWaitingMedia(boolean enableSessionWaitingMedia) {
        this.enableSessionWaitingMedia = enableSessionWaitingMedia;
    }

    public WaitingMediaDto getWaitingMedia() {
        return waitingMedia;
    }

    public void setWaitingMedia(WaitingMediaDto waitingMedia) {
        this.waitingMedia = waitingMedia;
    }

    public CaptionsDto getCaptions() {
        return captions;
    }

    public void setCaptions(CaptionsDto captions) {
        this.captions = captions;
    }

    @Override
    public boolean isSurveySessionStart() {
        return isSurveySessionStart;
    }

    @Override
    public void setSurveySessionStart(boolean surveySessionStart) {
        isSurveySessionStart = surveySessionStart;
    }

    public boolean isEnableVideoCaption() {
        return enableVideoCaption;
    }

    public void setEnableVideoCaption(boolean enableVideoCaption) {
        this.enableVideoCaption = enableVideoCaption;
    }

}
