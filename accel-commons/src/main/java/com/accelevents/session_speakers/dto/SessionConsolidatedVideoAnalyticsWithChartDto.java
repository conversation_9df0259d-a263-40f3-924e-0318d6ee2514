package com.accelevents.session_speakers.dto;

import java.util.List;

public class SessionConsolidatedVideoAnalyticsWithChartDto extends SessionConsolidatedVideoAnalyticsDto{

    private List<ChartDataPoint> liveViewChart;
    private List<ChartDataPoint> recordingViewChart;
    private Integer totalChatCount;

    // Inner class for chart data points
    public static class ChartDataPoint {
        private Integer time;
        private Integer count;

        public ChartDataPoint() {}

        public ChartDataPoint(Integer time, Integer count) {
            this.time = time;
            this.count = count;
        }

        public Integer getTime() {
            return time;
        }

        public void setTime(Integer time) {
            this.time = time;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }

    // Default constructor
    public SessionConsolidatedVideoAnalyticsWithChartDto() {}

    public SessionConsolidatedVideoAnalyticsWithChartDto(SessionConsolidatedVideoAnalyticsDto sessionConsolidatedVideoAnalyticsDto) {
        this.setSessionId(sessionConsolidatedVideoAnalyticsDto.getSessionId());
        this.setTotalUsers(sessionConsolidatedVideoAnalyticsDto.getTotalUsers());
        this.setTotalLiveView(sessionConsolidatedVideoAnalyticsDto.getTotalLiveView());
        this.setTotalRecordingView(sessionConsolidatedVideoAnalyticsDto.getTotalRecordingView());
        this.setTotalWatchTime(sessionConsolidatedVideoAnalyticsDto.getTotalWatchTime());
        this.setLiveWatchTime(sessionConsolidatedVideoAnalyticsDto.getLiveWatchTime());
        this.setRecordingWatchTime(sessionConsolidatedVideoAnalyticsDto.getRecordingWatchTime());
        this.setAvgLiveWatchTime(sessionConsolidatedVideoAnalyticsDto.getAvgLiveWatchTime());
    }

    public List<ChartDataPoint> getLiveViewChart() {
        return liveViewChart;
    }

    public void setLiveViewChart(List<ChartDataPoint> liveViewChart) {
        this.liveViewChart = liveViewChart;
    }

    public List<ChartDataPoint> getRecordingViewChart() {
        return recordingViewChart;
    }

    public void setRecordingViewChart(List<ChartDataPoint> recordingViewChart) {
        this.recordingViewChart = recordingViewChart;
    }

    public Integer getTotalChatCount() {
        return totalChatCount;
    }

    public void setTotalChatCount(Integer totalChatCount) {
        this.totalChatCount = totalChatCount;
    }
}
