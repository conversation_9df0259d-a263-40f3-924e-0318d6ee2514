package com.accelevents.session_speakers.dto;

import com.accelevents.auction.dto.ExhibitorDropDownDto;
import com.accelevents.dto.KeyValueDto;

import java.util.List;

public class PostSessionCallToActionDropDownDto {

    private List<SessionIdNameTypeDto> sessions;

    private List<ExhibitorDropDownDto> exhibitors;

    private List<KeyValueDto> lounges;

    public PostSessionCallToActionDropDownDto() {
    }

    public PostSessionCallToActionDropDownDto(List<SessionIdNameTypeDto> sessions, List<ExhibitorDropDownDto> exhibitors, List<KeyValueDto> lounges) {
        this.sessions = sessions;
        this.exhibitors = exhibitors;
        this.lounges = lounges;
    }

    public List<SessionIdNameTypeDto> getSessions() {
        return sessions;
    }

    public void setSessions(List<SessionIdNameTypeDto> sessions) {
        this.sessions = sessions;
    }

    public List<ExhibitorDropDownDto> getExhibitors() {
        return exhibitors;
    }

    public void setExhibitors(List<ExhibitorDropDownDto> exhibitors) {
        this.exhibitors = exhibitors;
    }

    public List<KeyValueDto> getLounges() {
        return lounges;
    }

    public void setLounges(List<KeyValueDto> lounges) {
        this.lounges = lounges;
    }
}
