package com.accelevents.session_speakers.dto;

public class UserSessionEngagementDto {

    private Long userId;
    private String firstName;
    private String lastName;
    private String email;
    private String engagementScore;
    private Double liveWatchTime;
    private Double recordingWatchTime;

    public UserSessionEngagementDto() {
    }

    public UserSessionEngagementDto(Long userId, String firstName, String lastName, String email, String engagementScore,
                                    Double liveWatchTime, Double recordingWatchTime) {
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.engagementScore = engagementScore;
        this.liveWatchTime = liveWatchTime;
        this.recordingWatchTime = recordingWatchTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEngagementScore() {
        return engagementScore;
    }

    public void setEngagementScore(String engagementScore) {
        this.engagementScore = engagementScore;
    }

    public Double getLiveWatchTime() {
        return liveWatchTime;
    }

    public void setLiveWatchTime(Double liveWatchTime) {
        this.liveWatchTime = liveWatchTime;
    }

    public Double getRecordingWatchTime() {
        return recordingWatchTime;
    }

    public void setRecordingWatchTime(Double recordingWatchTime) {
        this.recordingWatchTime = recordingWatchTime;
    }
}
