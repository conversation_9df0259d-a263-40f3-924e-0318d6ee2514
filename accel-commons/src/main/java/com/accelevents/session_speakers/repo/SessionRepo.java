package com.accelevents.session_speakers.repo;

import com.accelevents.common.dto.SurveySessionsBasicDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.session_speakers.dto.*;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface SessionRepo extends CrudRepository<Session,Long> {

	@Query("SELECT s FROM Session s WHERE s.streamProvider =:streamProvider AND s.sessionStartStatus = 'STARTED' AND s.endTime > :now")
	List<Session> getActiveExtendedSessions(@Param("streamProvider") StreamProvider streamProvider, @Param("now") Date now);

	@Query("SELECT DISTINCT session.id FROM Session session " +
			" LEFT JOIN session.sessionTagAndTracks stt " +
			" LEFT JOIN session.sessionSpeakers sessionSpeaker " +
			" LEFT JOIN sessionSpeaker.speaker speaker " +
			" WHERE session.eventId=:eventId " +
			" AND ( stt.tagOrTrackId IN (:tagAndTrackIds) OR 0 IN (:tagAndTrackIds) )"+
			" AND	" +
			"	(" +
			"		( coalesce(:searchString, 1) = 1 " +
			"		  OR " +
			"		 	(session.title like %:searchString% " +
			"			OR session.description like %:searchString% " +
			"			OR speaker.firstName like %:searchString% " +
			"			OR speaker.lastName like %:searchString% " +
            "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
            "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
			"			 )" +
			"   	)" +
			"	)  " )
	List<Long> filterSession(@Param("eventId") Long eventId,
							 @Param("tagAndTrackIds") List<Long> tagAndTrackIds,
							 @Param("searchString") String searchString);

	@Query("SELECT DISTINCT session FROM Session session " +
            " LEFT JOIN session.sessionTagAndTracks stt " +
			" LEFT JOIN session.sessionSpeakers sessionSpeaker " +
			" LEFT JOIN sessionSpeaker.speaker speaker " +
			" WHERE session.eventId=:eventId " +
            " AND (stt.tagOrTrackId IN (:listOfTagAndTrackIds) OR 0 IN (:listOfTagAndTrackIds) )"+
			" AND	" +
			"	(" +
			"		( coalesce(:searchString, 1) = 1 " +
			"		  OR " +
			"		 	(session.title like %:searchString% " +
			"			OR session.description like %:searchString% " +
			"			OR speaker.firstName like %:searchString% " +
			"			OR speaker.lastName like %:searchString% " +
            "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
            "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
			"			 )" +
			"   	)" +
            "	) AND (session.sessionTypeFormat IS NULL OR session.sessionTypeFormat IN (:sessionTypeFormats))" +
			" AND (:#{#searchDates == null} = true OR (DATE(CONVERT_TZ((session.startTime),'UTC',"+":eventTimeZone"+")) IN (:searchDates))) " +
			" AND session.format IN (:sessionTypes) " +
            " AND (( true=:isPast  AND false=:isUpcoming AND session.endTime < CURRENT_TIMESTAMP) OR (true=:isUpcoming AND false=:isPast AND session.endTime > CURRENT_TIMESTAMP) OR (true=:isUpcoming AND true=:isPast) OR (false=:isUpcoming AND false=:isPast))" +
            "ORDER BY session.startTime ASC, session.endTime ASC, session.position ASC, BIN(session.title) ASC, session.id ASC" )
	Page<Session> getAllSessionForHost(@Param("eventId") Long eventId, @Param("searchString") String searchString,
									   Pageable pageable,@Param("sessionTypeFormats") List<SessionTypeFormat> sessionTypeFormats,@Param("searchDates") List<Date> searchDates,
                                       @Param("sessionTypes") List<EnumSessionFormat> sessionTypes,@Param("eventTimeZone") String eventTimeZone,
                                       @Param("listOfTagAndTrackIds")List<Long> listOfTagAndTrackIds,@Param("isPast")boolean isPast,@Param("isUpcoming")boolean isUpcoming);

    @Query("SELECT DISTINCT session FROM Session session " +
            " LEFT JOIN session.sessionSpeakers sessionSpeaker " +
            " LEFT JOIN sessionSpeaker.speaker speaker " +
            " LEFT JOIN session.sessionTagAndTracks stt " +
            " WHERE session.eventId=:eventId " +
            " AND ( stt.tagOrTrackId IN (:listOfTagAndTrackIds) OR 0 IN (:listOfTagAndTrackIds) )"+
            " AND	" +
            "	(" +
            "		( coalesce(:searchString, 1) = 1 " +
            "		  OR " +
            "		 	(session.title like %:searchString% " +
            "			OR session.description like %:searchString% " +
            "			OR speaker.firstName like %:searchString% " +
            "			OR speaker.lastName like %:searchString% " +
            "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
            "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
            "			 )" +
            "   	)" +
            "	)" +
            "AND (session.sessionTypeFormat IS NULL OR session.sessionTypeFormat IN (:sessionTypeFormats))" +
			" AND (:#{#searchDates == null} = true OR (DATE(CONVERT_TZ((session.startTime),'UTC',"+":eventTimeZone"+")) IN (:searchDates))) " +
            " AND (( true=:isPast  AND false=:isUpcoming AND session.endTime < CURRENT_TIMESTAMP) OR (true=:isUpcoming AND false=:isPast AND session.endTime > CURRENT_TIMESTAMP) OR (true=:isUpcoming AND true=:isPast) OR (false=:isUpcoming AND false=:isPast))" +
			" AND session.format IN (:sessionTypes) ")
    Page<Session> getAllSessionForHostWithSorting(@Param("eventId") Long eventId, @Param("searchString") String searchString,
                                       Pageable pageable,@Param("sessionTypeFormats") List<SessionTypeFormat> sessionTypeFormats,@Param("searchDates") List<Date> searchDates,@Param("sessionTypes") List<EnumSessionFormat> sessionTypes,@Param("eventTimeZone") String eventTimeZone,
                                                  @Param("listOfTagAndTrackIds")List<Long> listOfTagAndTrackIds,@Param("isPast")boolean isPast,@Param("isUpcoming")boolean isUpcoming);

    @Query("SELECT DISTINCT session FROM Session session WHERE session.eventId=:eventId AND session.format NOT IN (:enumSessionFormats) AND " +
            "(session.sessionTypeFormat IS NULL OR session.sessionTypeFormat IN (:sessionTypeFormats))" +
            " ORDER BY session.startTime ASC, session.endTime ASC, session.position ASC, BIN(session.title) ASC, session.id ASC" )
    Page<Session> getAllSessionForHost(@Param("eventId") Long eventId, @Param("enumSessionFormats") List<EnumSessionFormat> enumSessionFormats,
                                       Pageable pageable,@Param("sessionTypeFormats") List<SessionTypeFormat> sessionTypeFormats);

    @Query("SELECT DISTINCT session FROM Session session WHERE session.eventId=:eventId AND session.format NOT IN (:enumSessionFormats) AND (session.sessionTypeFormat IS NULL OR session.sessionTypeFormat IN (:sessionTypeFormats))")
    Page<Session> getAllSessionForHostWithSorting(@Param("eventId") Long eventId, @Param("enumSessionFormats") List<EnumSessionFormat> enumSessionFormats,
                                       Pageable pageable,@Param("sessionTypeFormats") List<SessionTypeFormat> sessionTypeFormats);

	@Query("SELECT session FROM Session session " +
            " LEFT JOIN FETCH session.sessionTagAndTracks " +
            " LEFT JOIN FETCH session.sessionLocation " +
			" WHERE session.id=:id" +
			" AND session.eventId= :eventId ")
	Optional<Session> findByIdJoinFetch(@Param("id") Long id, @Param("eventId") Long eventId);

	@Query(value =  " SELECT session.title, Count(networking.id) AS matches ," +
			"       networkingMatches.members, session.id" +
			" FROM   sessions session" +
			"       LEFT JOIN networking_matches AS networking " +
			"              ON session.id = networking.session_id " +
			"       LEFT JOIN (SELECT session_id, Count(DISTINCT user_id) as members" +
			"                  FROM   (SELECT session_id, user_id " +
			"                          FROM   networking_matches " +
			"                          UNION " +
			"                          SELECT session_id, user_id_of_match " +
			"                          FROM   networking_matches) AS x " +
			"                  GROUP  BY session_id) as networkingMatches" +
			"              ON session.id = networkingMatches.session_id " +
			" WHERE  session.format = 'MEET_UP' " +
			"       AND session.rec_status != 'DELETE' " +
			"       AND session.event_id = :eventId " +
			" GROUP  BY session.id; " ,nativeQuery = true)
	List<Object[]> getNetworkingSessionActivity(@Param("eventId") Long eventId);

	@Query(value = "SELECT session.title FROM Session session" +
			" WHERE session.eventId=:eventId" +
			" AND :sessionStartDateTime < endTime" +
			" AND :sessionEndDateTime > startTime" +
			" AND :currentSessionId <> id" +
			" AND session.status <> 'DRAFT'" +
			" AND session.format = :sessionFormat ")
	List<String> findSessionsWithConflictingSlot(@Param("eventId") Long eventId,
												 @Param("sessionStartDateTime") Date curSessionStartTime,
												 @Param("sessionEndDateTime") Date curSessionEndTime,
												 @Param("sessionFormat") EnumSessionFormat sessionFormat,
												 @Param("currentSessionId") Long currentSessionId);

	// TODO :
	@Query("SELECT CASE WHEN COUNT(session) > 0 THEN true ELSE false END FROM Session session" +
			" JOIN UserSession as userSession ON userSession.sessionId = session.id" +
            " WHERE userSession.userId IN (:userIds)" +
            " AND userSession.eventId=:eventId" +
			" AND ((session.startTime BETWEEN :startTime AND :endTime) " +
			" OR (session.endTime BETWEEN :startTime AND :endTime))" )
	Boolean isSessionsBookedByUserAndBetweenStartEndTime(@Param("userIds") List<Long> userIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("eventId") Long eventId);

	@Query("SELECT s FROM Session s JOIN Event e ON s.eventId=e.eventId WHERE s.format ='WORKSHOP' and s.meetingObj is not null AND (e.eventStatus <> 'EVENT_DELETED' OR e.eventStatus IS NULL)")
	List<Session> getAllWorkshopSessions();

	@Query("SELECT s FROM Session s JOIN Event e ON s.eventId=e.eventId WHERE s.meetingObj is not null AND (e.eventStatus <> 'EVENT_DELETED' OR e.eventStatus IS NULL)")
	List<Session> getAllMeetingObjSessions();

	@Query("SELECT s FROM Session s WHERE s.eventId=:eventId and s.id=:id")
	Session findByEventIdAndIdWithoutCache(@Param("eventId") Long eventId, @Param("id") Long id);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionIdTitleDto(session.id,session.title) FROM Session session" +
            " WHERE session.id IN (:ids)" )
    List<SessionIdTitleDto> getSessionIdAndTitleById(@Param("ids") List<Long> ids);

    Session findByStreamUrl(String streamUrl);

	///////////////CACHE ADD QUERY BEFORE THIS LINE//////////
	//@Cacheable(value = "findSessionByLiveStreamId", key = "#result.id", unless="#result == null")
	Session findByLiveStreamId(String liveStreamId);

    @Cacheable(value = "findByConfId", key = "#p0", unless="#result == null")
    @Query("SELECT session FROM Session session WHERE session.id = :confId")
    Optional<Session> findByConfId(@Param("confId") Long confId);

	@Cacheable(value = "sessionByEventAndSessionId", key = "#p0?.toString()+'_'+#p1?.toString()?:'null_null'", unless="#result == null")
	Session findByEventIdAndId(Long eventId, Long id);

	@Cacheable(value = "findAllSessionDatesByEvent", key = "#p0", unless="#result == null")
	@Query("SELECT DISTINCT session.startTime" +
			" FROM Session as session " +
			" WHERE session.eventId=:eventId" +
			" AND session.status = 'VISIBLE' "+
			" ORDER BY session.startTime ")
	List<Date> findAllSessionDatesByEvent(@Param("eventId") Long eventId);

    @Cacheable(value = "findAllSessionForAdminOrStaffDatesByEvent", key = "#p0", unless="#result == null")
    @Query("SELECT DISTINCT session.startTime" +
            " FROM Session as session " +
            " WHERE session.eventId=:eventId" +
            " ORDER BY session.startTime ")
    List<Date> findAllSessionForAdminOrStaffDatesByEvent(@Param("eventId") Long eventId);

	@Cacheable(value = "allSessionByEvent", key = "#p0", unless="#result == null")
	List<Session> findSessionByEventId(Long eventId);

	@Cacheable(value = "SessionByEventAndFormat", key = "#p0", unless="#result == null")
	@Query("SELECT session FROM Session as session " +
            " LEFT JOIN FETCH session.sessionLocation" +
			" WHERE session.eventId=:eventId" +
            " ORDER BY session.startTime ASC, session.endTime ASC, session.position ASC, BIN(session.title) ASC")
	List<Session> getSessionByEvent(@Param("eventId") Long eventId);

    @Query("SELECT COUNT(session.id) FROM Session as session WHERE session.eventId=:eventId AND session.format NOT IN (:enumSessionFormats)")
    long getNonVisualSessionsByEvent(@Param("eventId") Long eventId, @Param("enumSessionFormats") List<EnumSessionFormat> enumSessionFormats);

	List<Session> findSessionByEventIdOrderByIdAsc(Long eventId);

	@Caching(evict = {
			@CacheEvict(value = "findAllSessionDatesByEvent", key = "#p0.eventId"),
            @CacheEvict(value = "findAllSessionForAdminOrStaffDatesByEvent", key = "#p0.eventId"),
			@CacheEvict(value = "SessionByEventAndFormat", key = "#p0.eventId")
			,@CacheEvict(value = "allSessionByEvent", key = "#p0.eventId")
			,@CacheEvict(value = "sessionByEventAndSessionId", key = "#p0.eventId.toString()+'_'+#p0.id.toString()")
            ,@CacheEvict(value = "findByConfId", key = "#p0.id")
            ,@CacheEvict(value = "SessionTitleByEventIdOrderByIdAsc", key = "#p0.eventId")
            ,@CacheEvict(value = "getSessionEndDateTypeByEvent", key = "#p0.eventId"),
            @CacheEvict(value = "getAllHiddenSessionOfEventId", key = "#p0.eventId"),
            @CacheEvict(value = "getAllSessionsByEventIdAndHideFromAttendees", key = "#p0.eventId"),
            @CacheEvict(value = "getAllPrivateSessionByEventId", key = "#p0.eventId")
			//,@CacheEvict(value = "findSessionByLiveStreamId", key = "#p0.id")
	})
	Session save(Session session);

	@Caching(evict = {
			@CacheEvict(value = "findAllSessionDatesByEvent", key = "#p1"),
            @CacheEvict(value = "findAllSessionForAdminOrStaffDatesByEvent", key = "#p1"),
			@CacheEvict(value = "SessionByEventAndFormat", key = "#p1")
			,@CacheEvict(value = "allSessionByEvent", key = "#p1")
			,@CacheEvict(value = "sessionByEventAndSessionId", key = "#p1.toString()+'_'+#p0.toString()")
            ,@CacheEvict(value = "findByConfId", key = "#p0")
            ,@CacheEvict(value = "SessionTitleByEventIdOrderByIdAsc", key = "#p1")
            ,@CacheEvict(value = "getSessionEndDateTypeByEvent", key = "#p1"),
            @CacheEvict(value = "getAllHiddenSessionOfEventId", key = "#p1"),
            @CacheEvict(value = "getAllSessionsByEventIdAndHideFromAttendees", key = "#p1"),
            @CacheEvict(value = "getAllPrivateSessionByEventId", key = "#p1")
			//,@CacheEvict(value = "findSessionByLiveStreamId", key = "#p0")
	})
	@Modifying
	@Query(value = "UPDATE sessions SET rec_status=:status, updated_at=CURRENT_TIMESTAMP() WHERE id=:sessionId AND event_id=:eventId", nativeQuery = true)
	void updateStatusToDeleteBySessionId(@Param("sessionId") Long sessionId, @Param("eventId") Long eventId, @Param("status") String status);

    @Caching(evict = {
            @CacheEvict(value = "findAllSessionDatesByEvent", key = "#p0"),
            @CacheEvict(value = "findAllSessionForAdminOrStaffDatesByEvent", key = "#p0"),
            @CacheEvict(value = "SessionByEventAndFormat", key = "#p0")
            ,@CacheEvict(value = "allSessionByEvent", key = "#p0")
            ,@CacheEvict(value = "SessionTitleByEventIdOrderByIdAsc", key = "#p0")
            ,@CacheEvict(value = "getSessionEndDateTypeByEvent", key = "#p0")
    })
    @Modifying
    @Query("UPDATE Session set position=(position+:updateCount)"
            + " where eventId = :eventId"
            + " AND position> :startPosition AND position <:endPosition")
    void updatePositionSession(@Param("eventId") long eventId, @Param("startPosition") double startPosition, @Param("endPosition") double endPosition, @Param("updateCount") double updateCount);

    @Caching(evict = {
            @CacheEvict(value = "findAllSessionDatesByEvent", key = "#p1"),
            @CacheEvict(value = "findAllSessionForAdminOrStaffDatesByEvent", key = "#p1"),
            @CacheEvict(value = "SessionByEventAndFormat", key = "#p1")
            ,@CacheEvict(value = "allSessionByEvent", key = "#p1")
            ,@CacheEvict(value = "SessionTitleByEventIdOrderByIdAsc", key = "#p1")
            ,@CacheEvict(value = "getSessionEndDateTypeByEvent", key = "#p1")
    })
    @Modifying
    @Query(value = "UPDATE sessions AS session SET session.position=(session.position-:updateCount)" +
            " WHERE session.event_id=:eventId AND session.start_time=:startTime AND session.end_time=:endTime", nativeQuery = true)
    void updatePositionForAllSessionByEventId(@Param("updateCount") double updateCount, @Param("eventId") long eventId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Query("SELECT s FROM Session s WHERE s.eventId=:eventId AND s.format in(:format)")
    List<Session> findByEventAndFormat(@Param("eventId") long eventId,
                                       @Param("format") List<EnumSessionFormat> format);

    @Query("SELECT s FROM Session s WHERE s.eventId=:eventId")
    List<Session> findByEventId(@Param("eventId") long eventId);

    @Cacheable(value = "SessionTitleByEventIdOrderByIdAsc", key = "#p0", unless="#result == null")
    @Query("SELECT s.title FROM Session s WHERE s.eventId=:eventId")
    List<String> findTitleByEventIdOrderByIdAsc(@Param("eventId") Long eventId);

    @Query("SELECT count(s.id) FROM Session s WHERE s.eventId=:eventId AND s.title=:sessionName")
    long findSessionCountBySessionName(@Param("eventId") Long eventId,@Param("sessionName") String sessionName );

	///////////DO NOT ADD ANY QUERY AFTER THIS////////

    @Query("SELECT session FROM Session session WHERE session.id BETWEEN :from AND :to")
    List<Session> getSessionByFromTo(@Param("from") long from, @Param("to") long to);

    @Query("SELECT session.taskId FROM Session session WHERE session.id =:sessionId")
    String findTaskIdBySessionId(@Param("sessionId") Long sessionId);

	@Query("SELECT session.eventId FROM Session session WHERE session.id =:sessionId")
	Optional<Long> getEventIdBySessionId(@Param("sessionId") Long sessionId);

    Session findFirstByEventIdOrderByPositionDesc(Long eventId);

    Session findFirstByEventIdAndStartTimeAndEndTimeOrderByPositionDesc(Long eventId,Date startTime,Date endTime);

    Session findFirstByIdAndEventIdAndPositionLessThanOrderByPositionDesc(long id, Long eventId,Double currentPosition);

    Session findFirstByIdAndEventIdAndPositionGreaterThanOrderByPositionAsc(long id, Long eventId,Double currentPosition);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId AND session.startTime=:startTime AND session.endTime=:endTime ORDER BY session.title")
    List<Session> getSessionsByTimeASC(@Param("eventId") long eventId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId AND session.startTime=:startTime AND session.endTime=:endTime ORDER BY session.title DESC")
    List<Session> getSessionsByTimeDESC(@Param("eventId") long eventId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId GROUP BY session.startTime, session.endTime")
    List<Session> getSessionsGroupByStartTimeAndEndTime(@Param("eventId") long eventId);

    @Query("SELECT session "
            + "FROM Session AS session "
            + "WHERE session.eventId=:eventId AND session.startTime=:startTime AND session.endTime=:endTime "
            + "ORDER BY session.position DESC")
    List<Session> getLastConcurrentSessionForDataFix(@Param("eventId") Long eventId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionEventLiveStream(session.eventId,session.id,session.liveStreamId) "
            + " FROM Session AS session "
            + "WHERE session.liveStreamId IN (:liveStreamIds)")
    List<SessionEventLiveStream> findAllByLiveStreamIdsIn(@Param("liveStreamIds") List<String> liveStreamIds);

    @Caching(evict = {
            @CacheEvict(value = "findAllSessionDatesByEvent", key = "#p2"),
            @CacheEvict(value = "findAllSessionForAdminOrStaffDatesByEvent", key = "#p2"),
            @CacheEvict(value = "SessionByEventAndFormat", key = "#p2"),
            @CacheEvict(value = "allSessionByEvent", key = "#p2"),
            @CacheEvict(value = "SessionTitleByEventIdOrderByIdAsc", key = "#p2")
            ,@CacheEvict(value = "getSessionEndDateTypeByEvent", key = "#p2")
    })
    @Modifying
    @Query("UPDATE Session SET streamUrl=:streamUrl WHERE id=:sessionId AND eventId=:eventId AND streamProvider<>'ACCELEVENTS' ")
    void updatePlayBackUrlBySessionId(@Param("sessionId") Long sessionId, @Param("streamUrl") String streamUrl, @Param("eventId") Long eventId);


    @Modifying
    @Query("UPDATE Session SET recordStatus=:block WHERE id IN (:sessionIds)")
    void updateRecordStatus(@Param("sessionIds") List<Long> sessionIds, @Param("block") RecordStatus block);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId AND session.recordStatus=:block")
    List<Session> findByEventIdAndRecordStatus(@Param("eventId") Long eventId, @Param("block") RecordStatus block);

    @Query(value = "SELECT session FROM Session session WHERE eventId =:eventId " +
            "and session.format <> 'BREAK' AND " +
            "session.format <> 'OTHER' " +
            "and session.format <> 'EXPO'")
    List<Session> findSessionFormatBySessionName(@Param("eventId") Long eventId);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId AND id IN (:sessionIds)")
    List<Session> getAllSessionByIdsAndEventId(@Param("sessionIds") List<Long> sessionIds,@Param("eventId") Long eventId);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId AND id IN (:sessionIds) ORDER BY session.startTime ASC, session.endTime ASC, session.position ASC, BIN(session.title) ASC")
    List<Session> getAllSessionByIdsWithOrderByStartTimeEndTimeAndPosition(@Param("sessionIds") List<Long> sessionIds,@Param("eventId") Long eventId);

    @Query("SELECT CASE WHEN COUNT(s.id) > 0 THEN true ELSE false END FROM Session s WHERE s.eventId =:eventId AND s.sessionStartStatus = 'STARTED' AND s.format ='MAIN_STAGE' AND s.endTime > :now")
    Boolean isActiveMainStageSessionByEventId(@Param("eventId") Long eventId, @Param("now") Date now);

    @Query("SELECT session FROM Session session WHERE session.eventId=:eventId AND " +
            " session.startTime > :sessionStartDateTime AND " +
            " session.rtmpUrl=:rtmpUrl AND " +
            " session.streamProvider=:streamProvider AND " +
            " (session.sessionStartStatus is null OR session.sessionStartStatus = 'NULL') ")
    List<Session> findByEventIdAndEnabledLowLatency(@Param("eventId") Long eventId,
                                                    @Param("sessionStartDateTime") Date curSessionStartTime,
                                                    @Param("rtmpUrl") String rtmpUrl ,@Param("streamProvider") StreamProvider streamProvider);

    @Query("SELECT session from Session session WHERE session.meetingObj LIKE %:meetingId% AND session.recordStatus<>'DELETE'")
    Optional<Session> findByMeetingId(@Param("meetingId") String meetingId);

    @Query("SELECT session FROM Session as session WHERE session.eventId IN (:listOfEventIds)")
    List<Session> getSessionByEventIds(@Param("listOfEventIds") List<Long> listOfEventIds);

    Session findFirstByEventIdAndStartTimeAndEndTimeOrderByPositionAsc(Long eventId, Date startTime, Date endTime);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionIdNameTypeDto(session.id,session.title,session.format) FROM Session session WHERE session.event=:event AND session.startTime > :now")
    List<SessionIdNameTypeDto> getUpcomingSessionByEvent(@Param("event") Event event, @Param("now") Date currentDate);


    @Query("SELECT new com.accelevents.session_speakers.dto.SessionIdNameTypeDto(session.id,session.title,session.format) " +
            "FROM Session session WHERE session.event=:event AND (session.startTime > :now OR session.startTime = :now) AND session.status <> 'DRAFT'")
    List<SessionIdNameTypeDto> getUpcomingSessionByEventAndEndDateOfCurrentSession(@Param("event") Event event, @Param("now") Date currentDate);

    @Query("SELECT session FROM Session session WHERE session.format=:format AND session.id=:id AND session.endTime > :now")
    Session isSessionExistingOrUpcoming(@Param("format") EnumSessionFormat format, @Param("id") Long id, @Param("now") Date currentDate);

    @Cacheable(value = "getSessionEndDateTypeByEvent", key = "#p0.eventId", unless="#result == null")
    @Query("SELECT new com.accelevents.session_speakers.dto.SessionEndDateTypeDto(session.id,session.title,session.format,session.endTime,session.sessionStartStatus,session.streamProvider) FROM Session session WHERE session.event=:event")
    List<SessionEndDateTypeDto> getSessionEndDateTypeByEvent(@Param("event") Event event);

    @Caching(evict = {
            @CacheEvict(value = "findAllSessionDatesByEvent", key = "#p2"),
            @CacheEvict(value = "findAllSessionForAdminOrStaffDatesByEvent", key = "#p2"),
            @CacheEvict(value = "SessionByEventAndFormat", key = "#p2"),
            @CacheEvict(value = "allSessionByEvent", key = "#p2"),
            @CacheEvict(value = "SessionTitleByEventIdOrderByIdAsc", key = "#p2")
            ,@CacheEvict(value = "getSessionEndDateTypeByEvent", key = "#p2"),
            @CacheEvict(value = "getAllHiddenSessionOfEventId", key = "#p2"),
            @CacheEvict(value = "getAllSessionsByEventIdAndHideFromAttendees", key = "#p2"),
            @CacheEvict(value = "getAllPrivateSessionByEventId", key = "#p2")
    })
    @Modifying
    @Query("UPDATE Session SET status=:sessionStatus WHERE id IN (:sessionIds) AND status <> 'DRAFT' and eventId=:eventId")
    void setSessionStatusById(@Param("sessionIds") List<Long> sessionIds,@Param("sessionStatus") EnumSessionStatus enumSessionStatus, @Param("eventId") Long eventId);

    @Query("SELECT DISTINCT session.id FROM Session session " +
            " LEFT JOIN session.sessionTagAndTracks stt " +
            " LEFT JOIN session.sessionSpeakers sessionSpeaker " +
            " LEFT JOIN sessionSpeaker.speaker speaker " +
            " LEFT JOIN UserSession AS us ON session.id = us.sessionId " +
            " WHERE session.eventId=:eventId AND us.userId=:userId " +
            " AND ( stt.tagOrTrackId IN (:tagAndTrackIds) OR 0 IN (:tagAndTrackIds) )" +
            " AND " +
            " ( " +
            " ( coalesce(:searchString, 1) = 1 " +
            " OR " +
            " (session.title like %:searchString% " +
            " OR session.description like %:searchString% " +
            " OR speaker.firstName like %:searchString% " +
            " OR speaker.lastName like %:searchString% " +
            "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
            "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
            " )" +
            " )" +
            " ) ")
    List<Long> filterSessionByUserId(@Param("eventId") Long eventId, @Param("tagAndTrackIds") List<Long> tagAndTrackIds, @Param("searchString") String searchString, @Param("userId") Long userId);

    @Query("SELECT DISTINCT session FROM Session session " +
            " LEFT JOIN session.sessionSpeakers sessionSpeaker " +
            " LEFT JOIN sessionSpeaker.speaker speaker " +
            " WHERE session.eventId=:eventId " )
    Page<Session> getAllSortedSessionWithDateAndTimeForHost(@Param("eventId") Long eventId, Pageable pageable);

    @Query("SELECT s.title FROM Session s WHERE s.eventId=:eventId AND s.id=:sessionId")
    String findTitleBySessionId(@Param("sessionId") Long sessionId, @Param("eventId") Long eventId);

    @Query("SELECT session.format FROM Session session WHERE session.id =:sessionId AND session.eventId=:eventId")
    String findFormatBySessionId(@Param("sessionId") Long sessionId, @Param("eventId") Long eventId);

    @Query("SELECT session.liveStreamId FROM Session session WHERE session.id =:sessionId AND session.eventId=:eventId")
    String findLiveStreamIdBySessionId(@Param("sessionId") Long sessionId, @Param("eventId") Long eventId);

    @Cacheable(value = "getListOfSessionIds", key = "#p0.toString() + #p1.toString()", unless="#result == null")
    @Query("SELECT session.sessionId FROM UserSession session WHERE session.userId=:userId AND session.eventId=:eventId")
    List<Long> getListOfSessionIds(@Param("eventId") Long eventId, @Param("userId") Long userId);

    @Query("SELECT count(s.id) FROM Session s WHERE s.eventId=:eventId AND s.format=:format")
    long findSessionCountBySessionFormat(@Param("eventId") Long eventId,@Param("format") EnumSessionFormat format );

    boolean existsBySurveyIdAndEventId(Long surveyId, Long eventId);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionListDto(session.id,session.title) FROM Session session" +
            " WHERE session.eventId=:eventId" )
    List<SessionListDto> getSessionIdAndTitleByEventId(@Param("eventId") Long eventId);


    @Query("SELECT CASE WHEN COUNT(session) > 0 THEN true ELSE false END FROM Session session where session.id=:sessionId and session.sessionVisibilityType='PRIVATE'")
    boolean isSessionPrivate(@Param("sessionId") Long sessionId);

    @Query("SELECT COUNT(s.id) FROM Session s WHERE (s.startTime < :startTime OR s.endTime > :endTime) AND s.eventId=:eventId AND COALESCE(s.recordStatus,'') NOT IN ('DELETE')")
    long countByEventIdAndSessionStartTime(@Param("startTime") Date startTime,@Param("eventId") Long eventId,@Param("endTime") Date endTime);

    @Query(value = "SELECT new com.accelevents.session_speakers.dto.SessionBasicDetailsDTO(s.id,s.title,s.startTime,s.endTime,s.format,s.status) " +
            " FROM Session AS s " +
            " WHERE s.eventId =:eventId ")
    List<SessionBasicDetailsDTO> getAllSessionsByEventId(@Param("eventId") Long eventId);

    @Query("select session from Session session where session.eventId = :eventId and session.title like :title%")
    List<Session> findSessionByEventAndPrefixTitleLike(@Param("eventId") Long eventId, @Param("title") String title);

    @Query("SELECT DISTINCT session.surveyId FROM Session session " +
            "WHERE session.surveyEnabled IS TRUE AND session.id IN (:sessionIds)")
    List<Long> findSurveyIdBySessionIds(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT DISTINCT session.id FROM Session session " +
            " LEFT JOIN session.sessionTagAndTracks stt " +
            " WHERE session.eventId=:eventId " +
            " AND ( stt.tagOrTrackId IN (:tagAndTrackIds) OR 0 IN (:tagAndTrackIds) ) ")
    List<Long> filterSessionWithoutSearch(@Param("eventId") Long eventId,
                             @Param("tagAndTrackIds") List<Long> tagAndTrackIds);

    @Cacheable(value = "getAllHiddenSessionOfEventId", key = "#p0", unless="#result == null")
    @Query("SELECT s.id FROM Session s WHERE s.eventId= :eventId AND s.status = 'HIDDEN'")
    List<Long> getAllHiddenSessionOfEventId(@Param("eventId") long eventId);

    @Cacheable(value = "getAllSessionsByEventIdAndHideFromAttendees", key = "#eventId", unless = "#result == null")
    @Query("SELECT s FROM Session s WHERE s.eventId = :eventId AND s.hideSessionFromAttendees = true AND s.status <> 'HIDDEN' AND s.sessionVisibilityType = 'PUBLIC'")
    List<Session> getAllSessionsByEventIdAndHideFromAttendeesAndStatusNotHidden(@Param("eventId") long eventId);

    @Query("SELECT s FROM Session s WHERE s.eventId = :eventId AND s.sponsorExhibitorJson IS NOT NULL")
    List<Session> getAllSessionsByEventIdAndSponsorExhibitorJsonNotNull(@Param("eventId") long eventId);

    @Cacheable(value = "getAllPrivateSessionByEventId", key = "#p0", unless = "#result == null")
    @Query("SELECT s.id FROM Session s WHERE s.eventId = :eventId AND s.sessionVisibilityType = 'PRIVATE' AND s.status <> 'HIDDEN'")
    List<Long> getAllPrivateSessionByEventIdAndStatusNotHidden(@Param("eventId") long eventId);


    @Query("SELECT s FROM Session s WHERE s.id IN :ids")
    List<Session> findSessionsByIds(@Param("ids") List<Long> ids);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionDetailsSurveyDto(s.id,s.status,s.hideSessionFromAttendees, s.ticketTypesThatCanBeRegistered, s.surveyId) " +
            " FROM Session s" +
            " JOIN UserSession us ON us.session = s" +
            " WHERE s.event=:event AND s.surveyEnabled = true AND s.endTime < :currentDate" +
            " AND us.userId = :userId" +
            " AND us.checkInTime IS NOT NULL"
    )
    List<SessionDetailsSurveyDto> getAllPastSessionsByEventIdAndSurveyEnabledAndRequiresSurveyCheckin(@Param("event") Event event, @Param("currentDate") Date currentDate, @Param("userId") Long userId);


    @Query("SELECT new com.accelevents.session_speakers.dto.SessionDetailsSurveyDto(s.id,s.status,s.hideSessionFromAttendees, s.ticketTypesThatCanBeRegistered, s.surveyId) " +
            " FROM Session s WHERE s.event=:event AND s.surveyEnabled = true AND s.endTime < :currentDate ")
    List<SessionDetailsSurveyDto> getAllPastSessionsByEventIdAndSurveyEnabled(@Param("event") Event event, @Param("currentDate") Date currentDate);


    @Query("SELECT DISTINCT new com.accelevents.session_speakers.dto.SessionDetailsCalendarViewDto(session.id, session.title, session.startTime, session.endTime, session.format, session.status, session.locationId) FROM Session session " +
            " LEFT JOIN session.sessionTagAndTracks stt " +
            " LEFT JOIN session.sessionSpeakers sessionSpeaker " +
            " LEFT JOIN sessionSpeaker.speaker speaker " +
            " WHERE session.eventId=:eventId " +
            " AND (stt.tagOrTrackId IN (:listOfTagAndTrackIds) OR 0 IN (:listOfTagAndTrackIds) )"+
            " AND	" +
            "	(" +
            "		( coalesce(:searchString, 1) = 1 " +
            "		  OR " +
            "		 	(session.title like %:searchString% " +
            "			OR session.description like %:searchString% " +
            "			OR speaker.firstName like %:searchString% " +
            "			OR speaker.lastName like %:searchString% " +
            "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
            "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
            "			 )" +
            "   	)" +
            "	)  AND session.format IN (:sessionTypes) " +
            " AND (session.locationId IN (:locationIds) OR 0 IN (:locationIds)) " +
            " AND (sessionSpeaker.speakerId IN (:speakerIds) OR 0 IN (:speakerIds)) " +
            " AND (:startDate IS NULL OR :endDate IS NULL OR session.startTime BETWEEN :startDate AND :endDate) "+
            "ORDER BY session.startTime ASC, session.endTime ASC, session.position ASC, BIN(session.title) ASC" )
    List<SessionDetailsCalendarViewDto> findAllHostSessions(@Param("eventId") long eventId, @Param("searchString") String searchString, @Param("sessionTypes") List<EnumSessionFormat> sessionTypes, @Param("listOfTagAndTrackIds") List<Long> listOfTagAndTrackIds, @Param("speakerIds") List<Long> speakerIds, @Param("locationIds") List<Long> locationIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Query(
            value = "SELECT DISTINCT new com.accelevents.session_speakers.dto.SessionDetailsCalendarViewDto(" +
                    " session.id, session.title, session.startTime, session.endTime, " +
                    " session.format, session.status, session.locationId" +
                    " ) " +
                    " FROM Session session " +
                    " LEFT JOIN session.sessionTagAndTracks stt " +
                    " LEFT JOIN session.sessionSpeakers sessionSpeaker " +
                    " LEFT JOIN sessionSpeaker.speaker speaker " +
                    " WHERE session.eventId=:eventId " +
                    " AND (stt.tagOrTrackId IN (:listOfTagAndTrackIds) OR 0 IN (:listOfTagAndTrackIds)) " +
                    " AND	" +
                    "	(" +
                    "		( coalesce(:searchString, 1) = 1 " +
                    "		  OR " +
                    "		 	(session.title like %:searchString% " +
                    "			OR session.description like %:searchString% " +
                    "			OR speaker.firstName like %:searchString% " +
                    "			OR speaker.lastName like %:searchString% " +
                    "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
                    "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
                    "			 )" +
                    "   	)" +
                    "	)  AND session.format IN (:sessionTypes) " +
                    " AND (session.locationId IN (:locationIds) OR 0 IN (:locationIds)) " +
                    " AND (sessionSpeaker.speakerId IN (:speakerIds) OR 0 IN (:speakerIds))",
            countQuery = "SELECT COUNT(DISTINCT session.id) FROM Session session " +
                    " LEFT JOIN session.sessionTagAndTracks stt " +
                    " LEFT JOIN session.sessionSpeakers sessionSpeaker " +
                    " LEFT JOIN sessionSpeaker.speaker speaker " +
                    " WHERE session.eventId=:eventId " +
                    " AND (stt.tagOrTrackId IN (:listOfTagAndTrackIds) OR 0 IN (:listOfTagAndTrackIds)) " +
                    " AND	" +
                    "	(" +
                    "		( coalesce(:searchString, 1) = 1 " +
                    "		  OR " +
                    "		 	(session.title like %:searchString% " +
                    "			OR session.description like %:searchString% " +
                    "			OR speaker.firstName like %:searchString% " +
                    "			OR speaker.lastName like %:searchString% " +
                    "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
                    "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
                    "			 )" +
                    "   	)" +
                    "	)  AND session.format IN (:sessionTypes) " +
                    " AND (session.locationId IN (:locationIds) OR 0 IN (:locationIds)) " +
                    " AND (sessionSpeaker.speakerId IN (:speakerIds) OR 0 IN (:speakerIds))"
    )
    Page<SessionDetailsCalendarViewDto> getAllHostUnscheduledSessionByEventId(
            @Param("eventId") long eventId,
            @Param("searchString") String searchString,
            @Param("sessionTypes") List<EnumSessionFormat> sessionTypes,
            @Param("listOfTagAndTrackIds") List<Long> listOfTagAndTrackIds,
            @Param("speakerIds") List<Long> speakerIds,
            @Param("locationIds") List<Long> locationIds,
            Pageable pageable
    );

    @Query("SELECT new com.accelevents.common.dto.SurveySessionsBasicDto(session.id, session.title, session.surveyEnabled, session.surveyId) FROM Session as session " +
            " WHERE session.eventId=:eventId")
    List<SurveySessionsBasicDto> getSessionSurveyBasicDetailsByEvent(@Param("eventId") Long eventId);

    @Query("SELECT s FROM Session s WHERE s.id IN :ids and s.eventId = :eventId")
    List<Session> findSessionsByIdsAndEventId(@Param("ids") List<Long> ids, @Param("eventId") Long eventId);

}
