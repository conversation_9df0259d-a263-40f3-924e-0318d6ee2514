package com.accelevents.session_speakers.repo;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumKeyValueType;
import com.accelevents.domain.session_speakers.KeyValue;
import com.accelevents.session_speakers.dto.KeyValueDTO;
import com.accelevents.session_speakers.dto.KeyValueDetailsDto;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface KeyValueRepo extends CrudRepository<KeyValue,Long> {

    @Caching(evict = {
            @CacheEvict(value = "findByIdKeyValue", key = "#p0.id")
    })
    @CacheEvict(value = "getKeyValueUsingEventIdAndType",key = "#p0.eventId.toString() + #p0.type.toString()", allEntries = true)
    KeyValue save(KeyValue keyValue);

    @Modifying
    @Caching(evict = {
            @CacheEvict(value = "findByIdKeyValue", allEntries = true),  // Evict all cache for "findByIdKeyValue"
            @CacheEvict(value = "getKeyValueUsingEventIdAndType", allEntries = true)  // Evict all cache for "getKeyValueUsingEventIdAndType"
    })
    @Query("DELETE FROM KeyValue keyValue WHERE keyValue.id IN :ids AND keyValue.eventId = :event")
    void deleteKeyValuesByIds(@Param("ids") List<Long> ids, @Param("event") Event event);


    @Query("SELECT keyValue FROM KeyValue as keyValue"+
			" WHERE (coalesce(:searchString, 1) = 1 OR keyValue.name LIKE %:searchString% )" +
			" AND keyValue.eventId=:event " +
			" AND keyValue.type=:type ORDER BY position DESC " )
	Page<KeyValue> findByEventAndTypeAndSearchString(
													 @Param("event") Event event,
													 @Param("type") EnumKeyValueType type,
													 @Param("searchString") String searchString,
													 Pageable pageable);

	List<KeyValue> findByIdIn(List<Long> keyValueIds);

    @Cacheable(value = "findByIdKeyValue", key = "#p0", unless="#result == null")
	Optional<KeyValue> findById(Long keyValueId);

    @Query("SELECT keyValue FROM KeyValue as keyValue"+
            " WHERE  keyValue.id IN :keyValueIds ORDER BY position DESC " )
    List<KeyValue> findByIds(@Param("keyValueIds") List<Long> keyValueIds);

	List<KeyValue> findByEventId(Event event);

	@Cacheable(value = "getKeyValueUsingEventIdAndType" , key = "#p0.eventId.toString() + #p1.toString()", condition ="#p0.eventId!=null && #p1!=null")
	@Query("SELECT new com.accelevents.session_speakers.dto.KeyValueDTO(keyValue.id,keyValue.name,keyValue.type,keyValue.color,keyValue.backgroundColor,keyValue.description,keyValue.position,keyValue.parentId) " +
            " FROM KeyValue as keyValue"+
			" WHERE keyValue.eventId=:event AND keyValue.type=:type ORDER BY position DESC " )
    List<KeyValueDTO> getKeyValueInfoList( @Param("event") Event event, @Param("type") EnumKeyValueType type);

	@Query("SELECT COUNT(keyValue) > 0 FROM KeyValue AS keyValue WHERE keyValue.name = :name and keyValue.eventId=:event and keyValue.type=:type")
	boolean checkKeyValueAlreadyExistByName(@Param("name") String name,@Param("event")Event event,@Param("type") EnumKeyValueType type);

    KeyValue getByNameAndEventIdAndType(@Param("name") String name,@Param("event")Event event,@Param("type") EnumKeyValueType type);

	@Query("SELECT COUNT(keyValue) > 0 FROM KeyValue AS keyValue WHERE keyValue.name = :name and keyValue.eventId=:event and keyValue.id=:id")
	boolean checkKeyValueAlreadyExistByNameForUpdate(@Param("name") String name,@Param("event")Event event,@Param("id") Long id);

    KeyValue findFirstByEventIdAndTypeOrderByPositionDesc(Event eventId, EnumKeyValueType type);

    KeyValue findFirstByIdAndEventIdAndTypeAndPositionGreaterThanOrderByPosition(long id, Event eventId, EnumKeyValueType type, Double position);

    KeyValue findFirstByIdAndEventIdAndTypeAndPositionLessThanOrderByPositionDesc(long id, Event eventId, EnumKeyValueType type, Double position);

    @Caching(evict = {
            @CacheEvict(value = "getKeyValueUsingEventIdAndType", key = "#p0.toString() + #p4.toString()", condition ="#p0.eventId!=null && #p1!=null")
    })
    @Modifying
    @Query("UPDATE KeyValue SET position=(position+:updateCount)"
        + " WHERE eventId = :eventId"
        + " AND type = :type"
        + " AND position> :startPosition AND position <:endPosition")
    void updatePositionKeyValue(@Param("eventId") Event eventId,
                                 @Param("startPosition") double startPosition,
                                 @Param("endPosition") double endPosition,
                                 @Param("updateCount") double updateCount,
                                 @Param("type") EnumKeyValueType type);

    @Caching(evict = {
            @CacheEvict(value = "getKeyValueUsingEventIdAndType", key = "#p1.eventId.toString() + #p2.toString()", condition = "#p1.eventId!=null && #p2!=null")
    })
    @Modifying
    @Query("UPDATE KeyValue AS keyValue SET keyValue.position=(keyValue.position+:updateCount) WHERE keyValue.eventId = :event AND keyValue.type = :type")
    void updatePositionForAllKeyValueByEventIdAndType(@Param("updateCount") double updateCount,
                                                      @Param("event") Event event,
                                                      @Param("type") EnumKeyValueType type);
    @Query("SELECT keyValue FROM KeyValue as keyValue"+
            " WHERE keyValue.eventId=:event AND keyValue.type=:type" )
    List<KeyValue> getKeyValueListByEventAndType( @Param("event") Event event, @Param("type") EnumKeyValueType type);

    @Query("SELECT kv FROM KeyValue kv WHERE kv.eventId=:event AND kv.parentId IN :parentIds")
    List<KeyValue> findByEventAndParentIds( @Param("event") Event event,@Param("parentIds") List<Long> parentIds);

    @Query("SELECT kv.id " +
            "FROM com.accelevents.domain.session_speakers.KeyValue kv " +
            "WHERE kv.eventId = :event " +
            "AND (kv.id = :id OR kv.parentId = :id)")
    List<Long> getIdsByEventAndIdORParentId(@Param("event") Event event, @Param("id") Long id);


    @Query(value = "SELECT NEW com.accelevents.session_speakers.dto.KeyValueDetailsDto(" +
            "keyValue, " +
            "(SELECT COUNT(DISTINCT stt.sessionId) FROM SessionTagAndTrack stt WHERE stt.tagOrTrackId = keyValue.id), " +
            "(SELECT COUNT(kv.id) FROM KeyValue kv WHERE kv.eventId = :event AND kv.parentId = keyValue.id) " +
            ") " +
            "FROM KeyValue keyValue " +
            "WHERE keyValue.eventId = :event " +
            "AND keyValue.type = :type " +
            "AND keyValue.parentId IS NULL " +
            "ORDER BY keyValue.position DESC",
            countQuery = "SELECT COUNT(keyValue) " +
                    "FROM KeyValue keyValue " +
                    "WHERE  keyValue.eventId = :event " +
                    "AND keyValue.type = :type " +
                    "AND keyValue.parentId IS NULL")
    Page<KeyValueDetailsDto> findByEventAndTypeAndParentIdNULL(
            @Param("event") Event event,
            @Param("type") EnumKeyValueType type,
            Pageable pageable);

    @Query(value = "SELECT NEW com.accelevents.session_speakers.dto.KeyValueDetailsDto(" +
            "keyValue, " +
            "(SELECT COUNT(DISTINCT stt.sessionId) FROM SessionTagAndTrack stt WHERE stt.tagOrTrackId = keyValue.id), " +
            "(SELECT COUNT(kv.id) FROM KeyValue kv WHERE kv.eventId = :event AND kv.parentId = keyValue.id) " +
            ") " +
            "FROM KeyValue keyValue " +
            "WHERE (:searchString IS NULL OR LOWER(keyValue.name) LIKE LOWER(CONCAT('%', :searchString, '%'))) " +
            "AND keyValue.eventId = :event " +
            "AND keyValue.type = :type " +
            "ORDER BY keyValue.position DESC",
            countQuery = "SELECT COUNT(keyValue) " +
                    "FROM KeyValue keyValue " +
                    "WHERE (:searchString IS NULL OR LOWER(keyValue.name) LIKE LOWER(CONCAT('%', :searchString, '%'))) " +
                    "AND keyValue.eventId = :event " +
                    "AND keyValue.type = :type " )
    Page<KeyValueDetailsDto> findByEventAndTypeSearchString(
            @Param("event") Event event,
            @Param("type") EnumKeyValueType type,
            @Param("searchString") String searchString,
            Pageable pageable);


    @Query("SELECT NEW com.accelevents.session_speakers.dto.KeyValueDetailsDto( keyValue, " +
            " (SELECT COUNT(DISTINCT stt.sessionId) FROM SessionTagAndTrack stt WHERE stt.tagOrTrackId = keyValue.id) ) " +
            "FROM KeyValue keyValue " +
            "WHERE keyValue.eventId = :event " +
            "AND keyValue.parentId IN :parentIds " +
            "ORDER BY keyValue.position DESC")
    List<KeyValueDetailsDto> getKeyValueDetailsDtoByEventAndParentIds(
            @Param("event") Event event,
            @Param("parentIds") List<Long> parentIds);


    @Query("SELECT new com.accelevents.session_speakers.dto.KeyValueDTO(keyValue.id,keyValue.name,keyValue.type,keyValue.color,keyValue.backgroundColor,keyValue.description,keyValue.position,keyValue.parentId)" +
            "FROM KeyValue keyValue " +
            "WHERE keyValue.eventId = :event " +
            "AND (keyValue.id = :id OR keyValue.parentId = :id)")
    List<KeyValueDTO> getKeyValueDTOByEventAndIdORParentId(@Param("event") Event event, @Param("id") Long id);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.KeyValueDetailsDto( keyValue ) " +
            "FROM KeyValue keyValue " +
            "WHERE keyValue.eventId = :event " +
            "AND keyValue.parentId IS NULL " +
            "AND keyValue.id IN (:trackIds) "+
            "ORDER BY keyValue.position DESC")
    List<KeyValueDetailsDto> findByEventAndIdsAndParentIdNULL(@Param("event") Event event,@Param("trackIds") List<Long> trackIds);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.KeyValueDetailsDto( keyValue ) " +
            "FROM KeyValue keyValue " +
            "WHERE keyValue.eventId = :event " +
            "AND keyValue.parentId IN :parentIds " +
            "AND keyValue.id IN :trackIds "+
            "ORDER BY keyValue.position DESC")
    List<KeyValueDetailsDto> getKeyValueDetailsDtoByEventAndParentIdsAndIds(
            @Param("event") Event event,
            @Param("parentIds") List<Long> parentIds, @Param("trackIds") List<Long> trackIds);

    @Query("SELECT k.id, k.name FROM KeyValue k WHERE k.id IN :trackIds")
    List<Object[]> findTrackNamesByIds(@Param("trackIds") List<Long> trackIds);
}
