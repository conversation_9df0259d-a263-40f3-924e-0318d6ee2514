package com.accelevents.session_speakers.repo;

import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.session_speakers.dto.SpeakerBasicDTO;
import com.accelevents.session_speakers.dto.SpeakerCountDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.dto.SpeakerEmailIdDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface Speaker<PERSON><PERSON><PERSON> extends CrudRepository<Speaker,Long> {

    List<Speaker> findByEmail(String email);

    Optional<Long> findIdByEventIdAndUserId(Long eventId, Long userId);

    @Query("SELECT speaker FROM Speaker as speaker"+
            " WHERE " +
            "	(" +
            "		(coalesce(:searchString, 1) = 1 " +
            "		 OR " +
            "		(speaker.firstName like %:searchString% " +
            "			OR speaker.lastName like %:searchString% " +
            "           OR CONCAT(speaker.firstName,' ',speaker.lastName) LIKE %:searchString% " +
            "           OR CONCAT(speaker.lastName,' ',speaker.firstName) LIKE %:searchString% " +
            "			OR speaker.email like %:searchString% " +
            "           OR speaker.title like %:searchString% " +
            "           OR speaker.company like %:searchString% " +
            "		)" +
            "   ) ) " +
            " AND speaker.eventId=:eventId " +
            " ORDER BY speaker.position DESC")
    Page<Speaker> getAllSpeakerByEvent(@Param("searchString") String searchString,
                                       @Param("eventId") Long eventId, Pageable pageable);


    @Query("SELECT COUNT(speaker) > 0 FROM Speaker AS speaker WHERE speaker.email = :email and speaker.eventId=:eventId")
    boolean checkSpeakerAlreadyExistByEmail(@Param("email") String email,@Param("eventId")Long eventId);

    @Query("SELECT s.userId FROM Speaker s WHERE s.eventId=:event_id")
    long getUserId(@Param("event_id") long eventId);

    @Query("SELECT speaker FROM Speaker speaker WHERE speaker.id BETWEEN :from AND :to and speaker.userId=0 and email is not null")
    List<Speaker> getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(@Param("from") long from, @Param("to") long to);

    @Query("SELECT speaker "
            + " FROM Speaker AS speaker "
            + "	WHERE speaker.id=:speakerId AND speaker.eventId =:eventId"
            + " AND speaker.position > :currentPosition "
            + "	ORDER BY speaker.position ")
    List<Speaker> nextPositionSpeaker(@Param("speakerId") long speakerId,
                                      @Param("eventId") Long eventId,
                                      @Param("currentPosition") Double currentPosition);

    @Query("SELECT speaker "
            + " FROM Speaker AS speaker "
            + "	WHERE speaker.id=:speakerId AND speaker.eventId=:eventId"
            + " AND speaker.position<:currentPosition ORDER BY speaker.position DESC")
    List<Speaker> previousPositionSpeaker(@Param("speakerId") long speakerId,
                                          @Param("eventId") Long eventId,
                                          @Param("currentPosition") Double currentPosition);

    @Query("SELECT speaker.userId "
            + " FROM Speaker AS speaker"
            + "	WHERE speaker.id=:speakerId")
    Long getUserIdsById(@Param("speakerId") long speakerId);

    @Query("SELECT speaker "
            + " FROM Speaker AS speaker"
            + "	WHERE speaker.eventId=:eventId AND speaker.position =:position "
            + " ORDER BY speaker.position DESC")
    List<Speaker> getSpeakerBySamePosition(@Param("position") double position, @Param("eventId") Long eventId);

    Speaker findFirstByEventIdOrderByPositionDesc(Long eventId);

    @Query("SELECT userId FROM Speaker AS speaker" +
            " WHERE speaker.eventId=:eventId")
    List<Long> findSpeakerUserIdsByEventId(@Param("eventId") Long eventId);

    @Query("SELECT COUNT(1) FROM Speaker AS speaker" +
            " WHERE speaker.eventId=:eventId")
    Integer findSpeakersCountByEventId(@Param("eventId") Long eventId);

    Optional<Speaker> findByEventIdAndUserId(Long eventId, Long UserId);

    @Query("SELECT speaker FROM Speaker speaker WHERE speaker.userId=:userId")
    List<Speaker> findAllSpeakerIdByUserId(@Param("userId") Long userId);

    @Query(value = "select count(distinct(speaker.id)) from speakers as speaker" +
            " left join session_speaker as ss on speaker.id = ss.speaker_id and ss.is_show_moderator = true" +
            " where speaker.event_id =:eventId and (speaker.speaker_status <> 'DELETED' or speaker.speaker_status is NULL) ", nativeQuery = true)
    Long getEventSpeakerCount(@Param("eventId") Long eventId);

    @Query("SELECT speaker FROM Speaker speaker Left join User u on u.userId = speaker.userId where speaker.eventId=:eventId AND speaker.id in (:ids)")
    List<Speaker> findByEventIdAndSpeakerIds(@Param("eventId")Long eventId, @Param("ids")List<Long> ids);

    @Query("SELECT new com.accelevents.session_speakers.dto.SpeakerCountDTO(s.speakerOnBoardingStatus, COUNT(1)) " +
            " FROM Speaker s WHERE s.event =:event " +
            " GROUP BY s.speakerOnBoardingStatus ")
    List<SpeakerCountDTO> speakerCountBySpeakerOnBoardingStatus(@Param("event") Event event);

    /////////////////CACHE///////////////////////

    @Cacheable(value = "findByEventIdAndId", key = "#p0.toString() + #p1.toString()", condition="#p0!=null", unless="#result == null")
    Optional<Speaker> findByEventIdAndId(Long eventId, Long id);

    @Cacheable(value = "isSpeakerOfEvt", key = "#p0.toString() + #p1.toString()", condition="#p0!=null", unless="#result == null")
    @Query("SELECT CASE WHEN COUNT(speaker.id) > 0 THEN true ELSE false END" +
            " FROM Speaker AS speaker" +
            " WHERE speaker.eventId=:eventId AND speaker.userId=:userId")
    boolean findByIdEventIdAndUserId(@Param("eventId") Long eventId, @Param("userId") Long userId);

    @Cacheable(value = "speakersByEvent", key = "#p0", condition="#p0!=null", unless="#result == null")
    @Query("SELECT speaker FROM Speaker AS speaker" +
            " WHERE speaker.eventId=:eventId")
    List<Speaker> findSpeakerByEventId(@Param("eventId") Long eventId);

    @Cacheable(value = "allSpeakersByEventId", key = "#p0", condition="#p0!=null", unless="#result == null")
    @Query("SELECT speaker FROM Speaker AS speaker" +
            " WHERE speaker.eventId=:eventId")
    Page<Speaker> findSpeakerByEventId(@Param("eventId") Long eventId, Pageable pageable);

    @Caching(evict = {
            @CacheEvict(value = "isSpeakerOfEvt", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "findByEventIdAndId", key = "#p0.eventId.toString() + #p0.id.toString()"),
            @CacheEvict(value = "speakersByEvent", key = "#p0.eventId"),
            @CacheEvict(value = "speakersByEventPosition", key = "#p0.eventId"),
            @CacheEvict(value = "allSpeakersByEventId", key = "#p0.eventId")
    })
    Speaker save(Speaker speaker);

    @Caching(evict = {
            @CacheEvict(value = "speakersByEvent", key = "#p1"),
            @CacheEvict(value = "speakersByEventPosition", key = "#p1"),
            @CacheEvict(value = "allSpeakersByEventId", key = "#p1")
    })
    @Modifying
    @Query(value = "UPDATE speakers AS speaker SET speaker.position=(speaker.position+:updateCount)" +
            " WHERE speaker.event_id=:eventId", nativeQuery = true)
    void updatePositionForAllSpeakerByEventId(@Param("updateCount") double updateCount,
                                              @Param("eventId") long eventId);

    @Caching(evict = {
            @CacheEvict(value = "speakersByEvent", key = "#p0"),
            @CacheEvict(value = "speakersByEventPosition", key = "#p0"),
            @CacheEvict(value = "allSpeakersByEventId", key = "#p0")
    })
    @Modifying
    @Query("UPDATE Speaker set position=(position+:updateCount)"
            + " where eventId = :eventId"
            + " AND position> :startPosition AND position <:endPosition")
    void updatePositionSpeaker(@Param("eventId") long eventId,
                               @Param("startPosition") double startPosition,
                               @Param("endPosition") double endPosition,
                               @Param("updateCount") double updateCount);

    @Caching(evict = {
            @CacheEvict(value = "isSpeakerOfEvt", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "findByEventIdAndId", key = "#p0.eventId.toString() + #p0.id.toString()"),
            @CacheEvict(value = "speakersByEvent", key = "#p0.eventId"),
            @CacheEvict(value = "speakersByEventPosition", key = "#p0.eventId"),
            @CacheEvict(value = "allSpeakersByEventId", key = "#p0.eventId")
    })
    void delete(Speaker speaker);

    @Modifying
    @Caching(evict = {
            @CacheEvict(value = "isSpeakerOfEvt", allEntries = true),
            @CacheEvict(value = "findByEventIdAndId", allEntries = true),
            @CacheEvict(value = "speakersByEvent", allEntries = true),
            @CacheEvict(value = "speakersByEventPosition", allEntries = true),
            @CacheEvict(value = "allSpeakersByEventId", allEntries = true)
    })
    @Query("DELETE FROM Speaker speaker WHERE speaker.id IN (:speakerIds)")
    void deleteAllBySpeakerId(@Param("speakerIds") List<Long> speakerIds);

    @Query("SELECT speaker.email FROM Speaker speaker WHERE speaker.eventId=:eventId")
    List<String> findEmailByEventId(@Param("eventId") long eventId);

    @Query("SELECT new com.accelevents.session_speakers.dto.SpeakerEmailIdDTO(speaker.email, speaker.id) FROM Speaker speaker WHERE speaker.eventId=:eventId AND speaker.email IS NOT NULL AND TRIM(speaker.email) != ''")
    List<SpeakerEmailIdDTO> findEmailAndIdByEventId(@Param("eventId") long eventId);

    @Query("SELECT speaker.id FROM Speaker speaker WHERE speaker.eventId=:eventId AND speaker.userId=:userId")
    Optional<Long> findSpeakerIdByEventIdAndUserId(@Param("eventId") long eventId,@Param("userId") Long userId);

    @Query("SELECT speaker FROM Speaker speaker WHERE speaker.eventId=:eventId AND speaker.userId=:userId")
    List<Speaker> findAllSpeakerByUserId(@Param("eventId") long eventId,@Param("userId") long userId);

    @Query("SELECT new com.accelevents.session_speakers.dto.SpeakerDTO(speakers.userId,speakers.eventId) FROM Speaker speakers GROUP BY event_id,user_id HAVING COUNT(*)>1 ")
    List<SpeakerDTO> getAllSpeakerEventIdAndUserId();
    /////////////////CACHE END///////////////////////

    List<Speaker> findByIdIn(List<Long> speakerIds);

    @Modifying
    @Caching(evict = {
            @CacheEvict(value = "isSpeakerOfEvt", allEntries = true),
            @CacheEvict(value = "findByEventIdAndId", allEntries = true),
            @CacheEvict(value = "speakersByEvent", key = "#p0.eventId"),
            @CacheEvict(value = "speakersByEventPosition", key = "#p0.eventId"),
            @CacheEvict(value = "allSpeakersByEventId", key = "#p0.eventId")
    })
    @Query("UPDATE Speaker SET highlighted = :isHighlighted WHERE event = :event AND id in (:speakerIds)")
    void updateSpeakerHighlightedFlag(@Param("event") Event event, @Param("speakerIds") List<Long> speakerIds,
                                      @Param("isHighlighted") boolean isHighlighted);
    @Query(value = "select s.event_id, count(s.id) from speakers s " +
            "join users u on u.user_id=s.user_id " +
            "where s.speaker_status not in ('DELETE') and u.user_id not in (select user_id from users where email like '%accelevents.com' or email = '<EMAIL>' or email like '%brilworks.com' or email like '%acceleventsREMOVED.com') " +
            "and s.event_id in (:eventIds) group by s.event_id;",nativeQuery = true)
    List<Object[]> speakerCountByEventId(@Param("eventIds") List<Long> eventIds);

    @Query(value = "SELECT new com.accelevents.session_speakers.dto.SpeakerBasicDTO(s.id,s.firstName,s.lastName,s.email,s.imageUrl) " +
            " FROM Speaker AS s " +
            " WHERE s.eventId =:eventId" )
    List<SpeakerBasicDTO> getAllSpeakersByEventId(@Param("eventId") Long eventId);

    @Query("SELECT speaker FROM Speaker AS speaker WHERE speaker.email = :email and speaker.eventId=:eventId")
    List<Speaker> findSpeakerByEmailAndEvent(@Param("email") String email,@Param("eventId")Long eventId);

    @Query("SELECT speaker FROM Speaker AS speaker WHERE speaker.email IN :emails and speaker.eventId=:eventId")
    List<Speaker> findSpeakersByEmailsAndEvent(@Param("emails") List<String> emails, @Param("eventId") Long eventId);

    @Query("SELECT s.id FROM Speaker s WHERE s.eventId = :eventId")
    List<Long> findAllIdByEventId(@Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.common.dto.EventTaskEmailDto(s.firstName,s.email,s.userId) FROM Speaker s WHERE s.eventId=:eventId AND s.id IN (:speakerIds)")
    List<EventTaskEmailDto> findSpeakerEmailAndNameByEventIdAndSpeakerIds(@Param("eventId")Long eventId, @Param("speakerIds")List<Long> speakerIds);

    @Query("SELECT speaker.id FROM Speaker speaker WHERE speaker.eventId=:eventId AND speaker.userId=:userId")
    List<Long> findSpeakerIdsByEventIdAndUserId(@Param("eventId") long eventId,@Param("userId") Long userId);

    @Query("SELECT Distinct(s.email) " +
            "FROM Speaker s " +
            "WHERE s.event.eventId = :eventId " +
            "AND s.email IN :emails")
    List<String> findAllEmailsByEventIdAndEmails(@Param("eventId") Long eventId,
                                            @Param("emails") List<String> emails);

    @Query("SELECT s FROM Speaker s " +
            "LEFT JOIN User u ON u.userId = s.userId " +
            "LEFT JOIN FETCH s.speakerAttribute sa " +
            "WHERE s.eventId = :eventId AND s.id IN (:ids)")
    List<Speaker> getSpeakerWithUserAndAttributeDataByEventIdAndIds(
            @Param("eventId") Long eventId,
            @Param("ids") List<Long> ids);
}


