package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.NetworkingLoungeVideoDetails;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface NetworkingLoungeVideoDetailsRepo extends CrudRepository<NetworkingLoungeVideoDetails, Long> {
    NetworkingLoungeVideoDetails findByAssetId(Long assetId);
}
