package com.accelevents.session_speakers.repo;

import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.session_speakers.dto.IdDurationDto;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import com.accelevents.session_speakers.dto.NetworkingLoungeVideoDto;
import com.accelevents.session_speakers.dto.SessionEventLiveStream;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface MUXLivestreamAssetRepo extends CrudRepository<MUXLivestreamAssetDetails, Long> {

    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails SET duration=:duration WHERE id=:id")
    void updateAssetDuration(@Param("id") long id, @Param("duration") double duration);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndAssetId(Long sessionId, String assetId);

    Optional<MUXLivestreamAssetDetails> findById(Long id);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndPlaybackIdAndAssetType(Long sessionId, String playbackId, AssetType assetType);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndPlaybackIdAndAssetTypeAndS3AssetKeyIsNull(Long sessionId, String playbackId, AssetType assetType);

//    @Query("SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails  WHERE assetDetails.sessionId =:sessionId and assetDetails.assetType =:assetType and assetDetails.defaultPlayback is true")
    Optional<MUXLivestreamAssetDetails> findBySessionIdAndDefaultPlaybackTrueAndAssetType( Long sessionId, AssetType assetType);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndDefaultPlaybackTrueAndAssetTypeAndS3AssetKeyIsNull( Long sessionId, AssetType assetType);


    List<MUXLivestreamAssetDetails> findByAssetId(String assetId);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.MuxAssetDTO(assetDetails.id, assetDetails.createdAt, assetDetails.duration, assetDetails.playbackId, assetDetails.assetId, assetDetails.defaultPlayback, assetDetails.fileNameForDownload, assetDetails.playBackRestrictionToken, assetDetails.thumbnailRestrictionToken , assetDetails.s3AssetKey) FROM MUXLivestreamAssetDetails assetDetails where assetDetails.sessionId=:sessionId and assetDetails.assetType=:assetType")
    List<MuxAssetDTO> findAllLivestreamAssetsForSessionBySessionIdAndType(@Param("sessionId") long sessionId,@Param("assetType")  AssetType assetType);

    @Query("SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails  WHERE assetDetails.sessionId IN (:sessionIds)")
    List<MUXLivestreamAssetDetails> findBySessionIdsIn(@Param("sessionIds") List<Long> sessionIds);

    @Modifying
    @Transactional
    @Query("UPDATE MUXLivestreamAssetDetails SET defaultPlayback=true WHERE id=:id")
    void markAssetAsDefaultPlayBack(@Param("id") long id);

    @Modifying
    @Transactional
    @Query("UPDATE MUXLivestreamAssetDetails SET defaultPlayback=false WHERE sessionId=:sessionId and assetType=:assetType")
    void markAllAssetAsNonDefaultPlayBack(@Param("sessionId") Long sessionId, @Param("assetType") AssetType assetType);

    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails SET recordStatus=:status WHERE sessionId=:sessionId")
    void updateStatusToDeleteBySessionId(@Param("sessionId") long sessionId, @Param("status") RecordStatus status);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.MuxAssetDTO(assetDetails.id, assetDetails.createdAt, assetDetails.duration, assetDetails.playbackId, assetDetails.assetId, assetDetails.defaultPlayback,assetDetails.fileNameForDownload,assetDetails.createdBy, assetDetails.muxAssetStatus, assetDetails.playBackRestrictionToken,assetDetails.thumbnailRestrictionToken, assetDetails.s3AssetKey) FROM MUXLivestreamAssetDetails assetDetails where assetDetails.sessionId=:sessionId and assetDetails.assetType=:assetType and  defaultPlayback=true")
    MuxAssetDTO findDefaultPlayBackForSession(@Param("sessionId") long sessionId,@Param("assetType") AssetType assetType);

    Optional<MUXLivestreamAssetDetails> findFirstBySessionIdAndAssetTypeOrderByIdDesc(Long sessionId, AssetType assetType);

    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails SET fileNameForDownload=:fileName WHERE id=:id")
    void updateAssetFileNameForDownload(@Param("id") long id, @Param("fileName") String fileName);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdDurationDto( " +
            " assetDetails.sessionId, sum(assetDetails.duration) "+
            " )" +
            " FROM MUXLivestreamAssetDetails assetDetails " +
            " WHERE assetDetails.sessionId IN (:sessionIds)" +
            " GROUP BY assetDetails.sessionId ")
    List<IdDurationDto> countBySessionIdIn(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT sum(assetDetails.duration) FROM MUXLivestreamAssetDetails assetDetails " +
            " WHERE assetDetails.sessionId =:sessionId" +
            " GROUP BY assetDetails.sessionId ")
    Double getSessionVideoDurationBySessionId(@Param("sessionId")Long sessionId);

    MUXLivestreamAssetDetails getByEventIdAndAssetIdAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(Long eventId, String assetId);

    List<MUXLivestreamAssetDetails> findAllByEventIdAndDefaultPlaybackTrueAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(Long eventId);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.MuxAssetDTO(assetDetails.id, assetDetails.createdAt, assetDetails.duration, assetDetails.playbackId, assetDetails.assetId, assetDetails.defaultPlayback, assetDetails.fileNameForDownload, assetDetails.playBackRestrictionToken, assetDetails.thumbnailRestrictionToken, assetDetails.s3AssetKey) FROM MUXLivestreamAssetDetails assetDetails where assetDetails.eventId=:eventId" +
            " AND assetDetails.sessionId IS NULL" +
            " AND assetDetails.networkingLoungeId IS NULL" +
            " And assetDetails.eventStreamId IS NULL")
    List<MuxAssetDTO> findAllLiveStreamAssetsForVirtualEventSetting(@Param("eventId") Long eventId);

    MUXLivestreamAssetDetails getByNetworkingLoungeIdAndAssetIdAndSessionIdNull(String networkingLoungeId, String assetId);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.MuxAssetDTO(assetDetails.id, assetDetails.createdAt, assetDetails.duration, assetDetails.playbackId, assetDetails.assetId, assetDetails.defaultPlayback, assetDetails.fileNameForDownload, assetDetails.createdBy, assetDetails.muxAssetStatus, assetDetails.playBackRestrictionToken, assetDetails.thumbnailRestrictionToken, assetDetails.s3AssetKey) FROM MUXLivestreamAssetDetails assetDetails" +
            " WHERE assetDetails.networkingLoungeId=:loungeId" +
            " AND assetDetails.sessionId IS NULL" +
            " And assetDetails.eventStreamId IS NULL")
    List<MuxAssetDTO> findAllByNetworkingLoungeId(@Param("loungeId") String loungeId);

    @Query("SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails where assetDetails.eventId=:eventId" +
            " AND assetDetails.sessionId IS NULL" +
            " AND assetDetails.networkingLoungeId IS NULL" +
            " And assetDetails.eventStreamId IS NULL")
    List<MUXLivestreamAssetDetails> findAllLiveStreamAssetsForVirtualEventSettingByEvent(@Param("eventId") Long eventId);

    @Query("SELECT count(1) from MUXLivestreamAssetDetails assetDetails where assetDetails.networkingLoungeId=:loungeId and assetDetails.createdBy=:userId")
    long findAllByNetworkingLoungeIdAndUserId(@Param("loungeId") String loungeId, @Param("userId") Long userId);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionEventLiveStream(assetDetails.eventId, assetDetails.sessionId,assetDetails.assetId," +
            " assetId.playbackId)" +
            " FROM MUXLivestreamAssetDetails assetDetails " +
            " where assetDetails.assetId IN (:assetIds)" )
    List<SessionEventLiveStream> findAllByAssetIdsIn(@Param("assetIds") List<String> assetIds);

    @Query("SELECT assetDetails" +
            " FROM MUXLivestreamAssetDetails assetDetails " +
            " where assetDetails.sessionId IN (:blockedDirectUploadSessionsIds)" )
    List<MUXLivestreamAssetDetails> findAllBySessionIdIn(@Param("blockedDirectUploadSessionsIds") List<Long> blockedDirectUploadSessionsIds);

    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails SET recordStatus=:recordStatus WHERE assetId IN (:assetIdsToMarkPlayBackRemoved)")
    void updateRecordStatusToPlayBackDeleted(@Param("assetIdsToMarkPlayBackRemoved") List<String> assetIdsToMarkPlayBackRemoved,
                                             @Param("recordStatus") RecordStatus recordStatus);

    @Modifying
    @Query(value = "UPDATE mux_livestream_asset_details " +
            " SET rec_status = IF (s3_key IS NOT NULL, 'MOVED', 'DELETE'), " +
            " mux_asset_status = IF (s3_key IS NOT NULL, 'MOVED', 'DELETE') " +
            " where mux_asset_id = :assetIdToMarkMovedOrDelete ", nativeQuery = true)
    void updateRecordAndAssetStatusToMovedOrDelete(@Param("assetIdToMarkMovedOrDelete") String assetIdToMarkMovedOrDelete);

    @Query("SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails WHERE assetDetails.recordStatus=:recordStatus AND assetDetails.eventId=:eventId")
    List<MUXLivestreamAssetDetails> findMuxPlayBackRemovedLiveStreamAssetsByEventId(@Param("eventId") Long eventId,
                                             @Param("recordStatus") RecordStatus recordStatus);


    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails SET playbackId=:playBackId, recordStatus=:recordStatus WHERE assetId=:assetId")
    void updatePlayBackUrlByAssetId(@Param("assetId") String assetId, @Param("playBackId") String playBackId,
                                    @Param("recordStatus") RecordStatus recordStatus);

    @Modifying
    @Transactional
    @Query("UPDATE MUXLivestreamAssetDetails SET defaultPlayback=false WHERE eventId=:eventId AND sessionId IS NULL AND networkingLoungeId IS NULL AND eventStreamId IS NULL")
    void markAllAssetAsNonDefaultPlayBackByEventId(@Param("eventId") Long eventId);

    Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndDefaultPlaybackTrue(Long eventStreamId);

    Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndPlaybackId(Long eventStreamId, String playbackId);

    @Modifying
    @Transactional
    @Query("UPDATE MUXLivestreamAssetDetails SET defaultPlayback=false WHERE eventStreamId=:eventStreamId")
    void markAllAssetAsNonDefaultPlayBackByEventStreamId(@Param("eventStreamId") Long eventStreamId);

    Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndAssetId(Long eventStreamId, String assetId);

    Optional<MUXLivestreamAssetDetails> findFirstByEventStreamIdOrderByIdDesc(Long eventStreamId);

    @Query(value = "select mux_asset_id from mux_livestream_asset_details where created_at between '2021-12-03 00:00:00' and '2021-12-07 00:00:00' and playback_restriction_token is not null order by id desc", nativeQuery = true)
    List<String> getLivestreamAssetsDetailsBetweenDate();

    @Query("SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails  WHERE assetDetails.sessionId IN (:sessionIds) and assetDetails.assetType=:assetType")
    List<MUXLivestreamAssetDetails> findByAssetTypeAndSessionIdsIn(@Param("sessionIds") List<Long> sessionIds,@Param("assetType") AssetType assetType);

    @Query("SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails  WHERE assetDetails.sessionId IN (:sessionIds) and assetDetails.assetType=:assetType and assetDetails.visible=:visible")
    List<MUXLivestreamAssetDetails> findByAssetTypeAndVisibleStatusAndSessionIdsIn(@Param("sessionIds") List<Long> sessionIds,@Param("assetType") AssetType assetType, @Param("visible") boolean visible);

    @Query(value = "SELECT * FROM mux_livestream_asset_details assetDetails WHERE assetDetails.rec_status='DELETE' AND assetDetails.id Between :from AND :to", nativeQuery = true)
    List<MUXLivestreamAssetDetails> getMuxAssetsByRecordStatusDeletedAndAssetIdFromAndTo(@Param("from") Long from, @Param("to") Long to);

    List<MUXLivestreamAssetDetails> findAllMUXLivestreamAssetDetailsByEventId(Long eventId);

    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails SET muxAssetStatus=:muxAssetStatus WHERE assetId IN (:assetIdsToMarkPlayBackRemoved)")
    void updateMuxAssetStatus(@Param("assetIdsToMarkPlayBackRemoved") List<String> assetIds, @Param("muxAssetStatus") RecordStatus muxAssetStatus);

    @Query(value = "SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails  WHERE assetDetails.defaultPlayback IS :isDefaultPlayback " +
            " AND assetDetails.createdAt <=:createdDate AND assetDetails.networkingLoungeId IS NULL " +
            " AND assetDetails.assetType !='WELCOME_VIDEO_ASSET' AND assetDetails.id BETWEEN :from AND :to")
    List<MUXLivestreamAssetDetails> findAllMuxAssetByDefaultPlaybackAndCreatedDateBeforeAndIdFromTo(@Param("isDefaultPlayback") boolean isDefaultPlayback,
                                                                                                    @Param("createdDate") Date createdDate, @Param("from") Long from, @Param("to") Long to);

    @Query(value = "SELECT assetDetails FROM MUXLivestreamAssetDetails assetDetails" +
            " LEFT JOIN UserSession userSession ON assetDetails.sessionId=userSession.sessionId " +
            " WHERE assetDetails.createdAt<=:createdDate AND assetDetails.defaultPlayback IS TRUE" +
            " AND assetDetails.sessionId IS NOT NULL AND assetDetails.id BETWEEN :from AND :to" +
            " GROUP BY assetDetails.id, userSession.sessionId HAVING COUNT(assetDetails)<=5")
    List<MUXLivestreamAssetDetails> getAllMuxAssetByCreatedDateWithFiveAttendeeAndFromTo(@Param("createdDate") Date createdDate, @Param("from") Long from, @Param("to") Long to);

    @Query(value = "SELECT assetDetails.mux_asset_id,assetDetails.mux_asset_playback_id, assetDetails.asset_type FROM mux_livestream_asset_details assetDetails" +
            " JOIN events event ON assetDetails.event_id=event.event_id " +
            " WHERE event.updated_at < DATE_ADD(NOW(), INTERVAL -30 DAY) AND event.event_status='EVENT_DELETED' " +
            " AND (assetDetails.mux_asset_status<>'DELETE' or assetDetails.mux_asset_status IS NULL)",
            countQuery = "SELECT COUNT(assetDetails.mux_asset_id) FROM mux_livestream_asset_details assetDetails" +
                    " JOIN events event ON assetDetails.event_id=event.event_id " +
                    " WHERE event.updated_at < DATE_ADD(NOW(), INTERVAL -30 DAY) AND event.event_status='EVENT_DELETED' " +
                    " AND (assetDetails.mux_asset_status<>'DELETE' or assetDetails.mux_asset_status IS NULL)", nativeQuery = true)
    Page<Object[]> getAllMuxAssetByEventDeleteBeforeThirtyDays(Pageable pageable);

    @Query(value = "SELECT * FROM mux_livestream_asset_details assetDetails WHERE assetDetails.asset_type='WELCOME_VIDEO_ASSET' AND assetDetails.created_at <=:createdDate AND assetDetails.id BETWEEN :from AND :to", nativeQuery = true)
    List<MUXLivestreamAssetDetails> getAllWelcomeVideoByCreatedAtAndFromTo(@Param("createdDate") Date createdDate, @Param("from") Long from, @Param("to") Long to);
    List<MUXLivestreamAssetDetails> findByEventIdAndPlaybackId(@Param("eventId") Long eventId, @Param("playbackId") String playbackId);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.NetworkingLoungeVideoDto(assetDetails.id, assetDetails.createdAt, assetDetails.duration, assetDetails.playbackId, assetDetails.assetId, assetDetails.defaultPlayback, assetDetails.fileNameForDownload, assetDetails.createdBy, assetDetails.muxAssetStatus, assetDetails.playBackRestrictionToken, assetDetails.thumbnailRestrictionToken, assetDetails.s3AssetKey, user.firstName, user.lastName) FROM MUXLivestreamAssetDetails assetDetails " +
            " LEFT JOIN User user on assetDetails.createdBy = user.userId" +
            " WHERE assetDetails.networkingLoungeId=:loungeId" +
            " AND assetDetails.sessionId IS NULL" +
            " And assetDetails.eventStreamId IS NULL")
    List<NetworkingLoungeVideoDto> findAllLoungeVideoByNetworkingLoungeId(@Param("loungeId") String loungeId);

    MUXLivestreamAssetDetails findFirstBySessionIdAndEventIdOrderByPositionDesc(Long sessionId, Long eventId);

    @Query("select assetDetails from MUXLivestreamAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId and assetDetails.visible=true ORDER BY assetDetails.position , assetDetails.createdAt desc")
    List<MUXLivestreamAssetDetails> getVisibleAssetsBySessionIdAndEventId(@Param("sessionId") Long sessionId,
                                                              @Param("eventId") Long  eventId);
    @Query("select assetDetails from MUXLivestreamAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId ORDER BY assetDetails.position , assetDetails.createdAt desc")
    List<MUXLivestreamAssetDetails> findBySessionIdAndEventId(@Param("sessionId") Long sessionId,
                                             @Param("eventId") Long  eventId);

    @Query("select assetDetails FROM MUXLivestreamAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId AND assetDetails.position>:currentPosition ORDER BY assetDetails.position")
    List<MUXLivestreamAssetDetails> nextPositionItem(@Param("sessionId") long sessionId,
                                        @Param("eventId") Long eventId, @Param("currentPosition") double currentPosition);

    @Query("select assetDetails FROM MUXLivestreamAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId AND assetDetails.position<:currentPosition ORDER BY assetDetails.position DESC")
    List<MUXLivestreamAssetDetails> previousPositionItem(@Param("sessionId") long sessionId,
                                            @Param("eventId") long eventId, @Param("currentPosition") double currentPosition);

    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails set position=(position+:updateCount) where sessionId = :sessionId AND eventId =:eventId AND position> :startPosition AND position <:endPosition")
    void updatePositionItem(@Param("sessionId") long sessionId, @Param("eventId") long eventId,
                            @Param("startPosition") double startPosition, @Param("endPosition") double endPosition,
                            @Param("updateCount") double updateCount);

    @Modifying
    @Query("UPDATE MUXLivestreamAssetDetails set position=(position+:updateCount) where sessionId = :sessionId AND eventId =:eventId")
    void updatePositionForAllItem(@Param("sessionId") long sessionId, @Param("eventId") long eventId,
                                  @Param("updateCount") double updateCount);

    Optional<MUXLivestreamAssetDetails> findByAssetIdAndSessionId(@Param("assetId") String assetId, @Param("sessionId") Long sessionId);

    @Query("SELECT DISTINCT assetDetails.sessionId, true " +
            "FROM MUXLivestreamAssetDetails assetDetails " +
            "WHERE assetDetails.sessionId IN (:sessionIds) " +
            "AND assetDetails.assetType = :assetType " +
            "AND assetDetails.visible = :visible")
    List<Object[]> findSessionRecordingsBySessionIdsAndAssetTypeAndVisible(@Param("sessionIds") List<Long> sessionIds,
                                                                           @Param("assetType") AssetType assetType,
                                                                           @Param("visible") boolean visible);
}
