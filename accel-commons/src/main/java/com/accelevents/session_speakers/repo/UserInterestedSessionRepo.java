package com.accelevents.session_speakers.repo;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.UserInterestedSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface UserInterestedSessionRepo extends CrudRepository<UserInterestedSession,Long> {

    @Modifying
    void deleteBySessionIdAndUserId(Long sessionId, Long userId);

	@Query("  SELECT sessionId FROM UserInterestedSession " +
			" WHERE eventId=:eventId AND userId=:userId" )
	List<Long> findByEventIdAndUserId(@Param("eventId") long eventId,
											 @Param("userId") Long userId);


	@Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
			" sessionId, COUNT(1) "+
			" )" +
			" FROM UserInterestedSession " +
			" WHERE sessionId IN (:sessionIds)" +
			" GROUP BY sessionId ")
	List<IdCountDto> countBySessionIdIn(@Param("sessionIds") List<Long> sessionIds);

	BigInteger countBySessionId (Long sessionId);

    @Modifying
    @Query("UPDATE UserInterestedSession SET recordStatus=:status WHERE sessionId=:sessionId")
    void updateStatusToDeleteBySessionId(@Param("sessionId") Long sessionId, @Param("status") RecordStatus status);

	UserInterestedSession save(UserInterestedSession userSession);

}
