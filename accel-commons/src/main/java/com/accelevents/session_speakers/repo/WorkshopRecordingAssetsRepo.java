package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.WorkshopRecordingAssetDetails;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Repository
public interface WorkshopRecordingAssetsRepo extends CrudRepository<WorkshopRecordingAssetDetails, Long> {

    Optional<WorkshopRecordingAssetDetails> findByPipelineId(String pipelineId);

    @Modifying
    @Transactional
    @Query("UPDATE WorkshopRecordingAssetDetails SET defaultPlayback=false WHERE sessionId=:sessionId")
    void markAllWorkshopRecordingAssetAsNonDefaultPlayBack(@Param("sessionId") Long sessionId);

    @Modifying
    @Transactional
    @Query("UPDATE WorkshopRecordingAssetDetails SET defaultPlayback=true WHERE id=:id")
    void markWorkshopRecordingAssetAsDefaultPlayBack(@Param("id") Long id);

    @Query("SELECT new com.accelevents.session_speakers.dto.MuxAssetDTO(assetsDetails.id, assetsDetails.createdAt, assetsDetails.playBackId," +
            "assetsDetails.fileName, assetsDetails.defaultPlayback, assetsDetails.duration, assetsDetails.assetStatus) " +
            " FROM WorkshopRecordingAssetDetails assetsDetails where assetsDetails.sessionId=:sessionId and assetsDetails.defaultPlayback=true and assetsDetails.pipelineId IS NOT NULL")
    MuxAssetDTO findDefaultPlayBackForWorkShopSession(@Param("sessionId") Long sessionId);

    @Query("SELECT new com.accelevents.session_speakers.dto.MuxAssetDTO(assetsDetails.id, assetsDetails.createdAt, assetsDetails.playBackId, " +
            "assetsDetails.fileName, assetsDetails.defaultPlayback, assetsDetails.duration, assetsDetails.assetStatus)" +
            " FROM WorkshopRecordingAssetDetails assetsDetails where assetsDetails.sessionId=:sessionId and assetsDetails.pipelineId IS NOT NULL")
    List<MuxAssetDTO> getAllBySessionId(@Param("sessionId") Long sessionId);

    @Query("SELECT new com.accelevents.session_speakers.dto.MuxAssetDTO(" +
            "assetsDetails.id, assetsDetails.createdAt, assetsDetails.playBackId, " +
            "assetsDetails.fileName, assetsDetails.defaultPlayback, assetsDetails.duration, " +
            "assetsDetails.assetStatus, assetsDetails.sessionId, assetsDetails.visible) FROM WorkshopRecordingAssetDetails assetsDetails " +
            "where assetsDetails.sessionId in (:sessionId) and assetsDetails.pipelineId IS NOT NULL")
    List<MuxAssetDTO> getAllWorkshopAssetDetailsBySessionIds(@Param("sessionId") List<Long> sessionId);

    Optional<WorkshopRecordingAssetDetails> findById(Long id);

    List<WorkshopRecordingAssetDetails> findAllBySessionId(Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndPipelineIdIsNull(Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndPipelineId(Long sessionId, String pipelineId);

    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndAttendeeCountIsNotNull(Long sessionId);

    @Query(value = "SELECT asset FROM WorkshopRecordingAssetDetails asset WHERE asset.sessionId=:sessionId AND" +
            " (asset.attendeeCount IS NOT NULL OR asset.pipelineId IS NULL)")
    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndAttendeeCountIsNotNullOrPipelineIdIsNull(@Param("sessionId") Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findByFileName(String pipelineId);

    WorkshopRecordingAssetDetails findFirstBySessionIdAndEventIdOrderByPositionDesc(Long sessionId, Long eventId);

    @Query("select assetDetails from WorkshopRecordingAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId and assetDetails.visible=true ORDER BY assetDetails.position, assetDetails.id desc")
    List<WorkshopRecordingAssetDetails> getVisibleAssetsBySessionIdAndEventId(@Param("sessionId") Long sessionId,
                                                                  @Param("eventId") Long  eventId);
    @Query("select assetDetails from WorkshopRecordingAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId ORDER BY assetDetails.position, assetDetails.id desc")
    List<WorkshopRecordingAssetDetails> findBySessionIdAndEventId(@Param("sessionId") Long sessionId,
                                                              @Param("eventId") Long  eventId);

    @Query("select assetDetails FROM WorkshopRecordingAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId AND assetDetails.position>:currentPosition ORDER BY assetDetails.position")
    List<WorkshopRecordingAssetDetails> nextPositionItem(@Param("sessionId") long sessionId,
                                                     @Param("eventId") Long eventId, @Param("currentPosition") double currentPosition);

    @Query("select assetDetails FROM WorkshopRecordingAssetDetails assetDetails where assetDetails.sessionId = :sessionId AND assetDetails.eventId =:eventId AND assetDetails.position<:currentPosition ORDER BY assetDetails.position DESC")
    List<WorkshopRecordingAssetDetails> previousPositionItem(@Param("sessionId") long sessionId,
                                                         @Param("eventId") long eventId, @Param("currentPosition") double currentPosition);

    @Modifying
    @Query("UPDATE WorkshopRecordingAssetDetails set position=(position+:updateCount) where sessionId = :sessionId AND eventId =:eventId AND position> :startPosition AND position <:endPosition")
    void updatePositionItem(@Param("sessionId") long sessionId, @Param("eventId") long eventId,
                            @Param("startPosition") double startPosition, @Param("endPosition") double endPosition,
                            @Param("updateCount") double updateCount);

    @Modifying
    @Query("UPDATE WorkshopRecordingAssetDetails set position=(position+:updateCount) where sessionId = :sessionId AND eventId =:eventId")
    void updatePositionForAllItem(@Param("sessionId") long sessionId, @Param("eventId") long eventId,
                                  @Param("updateCount") double updateCount);

    @Query("SELECT new com.accelevents.session_speakers.dto.MuxAssetDTO(" +
            "assetsDetails.id, assetsDetails.createdAt, assetsDetails.playBackId, " +
            "assetsDetails.fileName, assetsDetails.defaultPlayback, assetsDetails.duration, " +
            "assetsDetails.assetStatus, assetsDetails.sessionId, assetsDetails.visible) FROM WorkshopRecordingAssetDetails assetsDetails " +
            "where assetsDetails.sessionId in (:sessionIds) and assetsDetails.visible=:visible and  assetsDetails.pipelineId IS NOT NULL")
    List<MuxAssetDTO> findWorkshopRecordingByVisibleStatusSessionIdsIn(@Param("sessionIds") List<Long> sessionIds,@Param("visible") boolean visible);


    @Query("SELECT DISTINCT assetsDetails.sessionId, true" +
            " FROM WorkshopRecordingAssetDetails assetsDetails " +
            "where assetsDetails.sessionId in (:sessionIds) and assetsDetails.visible=:visible and  assetsDetails.pipelineId IS NOT NULL")
    List<Object[]> findWorkshopRecordingsByVisibleStatusAndSessionIdsIn(@Param("sessionIds") List<Long> sessionIds,@Param("visible") boolean visible);
}
