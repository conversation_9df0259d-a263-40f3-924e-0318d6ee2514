package com.accelevents.session_speakers.repo;

import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketTypeFormat;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.messages.TicketType;
import com.accelevents.session_speakers.dto.AttendeeSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.RegisterdHolderUsers;
import com.accelevents.session_speakers.dto.WaitListedHolderUsers;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface UserSessionRepo extends CrudRepository<UserSession,Long> {

	List<UserSession> findByEventTicketIdAndSessionIdAndEventId(Long eventTicketId, Long sessionId, Long eventId);

    @Query("SELECT us.sessionId from UserSession us where us.userId = :userId " +
            "AND us.eventId =:eventId AND us.sessionId IN (:sessionIds) ")
    List<Long> findSessionIdByUserIdAndEventIdAndSessionIdIn(@Param("userId") Long userId,
                                                             @Param("eventId") long event,
                                                             @Param("sessionIds") List<Long> sessionIds);

	void deleteByEventTicketIdAndSessionIdAndEventId(Long eventTicketId, Long sessionId, Long eventId);

	@Query("SELECT case when (count(userSession.id) > 0)  then true else false end " +
			"FROM UserSession userSession " +
			"JOIN EventTickets eventTicket ON eventTicket.id=userSession.eventTicketId " +
			"WHERE userSession.sessionId=:sessionId AND userSession.eventId=:eventId AND eventTicket.ticketingTypeId.id IN (:ticketTypeIds) " +
			"AND userSession.userId=:userId AND userSession.checkInStatus=:state")
	boolean isUserAvailableStateForTicketTypesIn(@Param("eventId") long eventId,
												 @Param("userId") Long userId,
												 @Param("sessionId") Long sessionId,
												 @Param("state") EnumUserSessionStatus state,
												 @Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Query("SELECT case when (count(userSession.id) > 0)  then true else false end " +
            "FROM UserSession userSession " +
            "JOIN EventTickets eventTicket ON eventTicket.id=userSession.eventTicketId " +
            "WHERE userSession.sessionId=:sessionId AND userSession.eventId=:eventId AND eventTicket.ticketingTypeId.id IN (:ticketTypeIds) " +
            "AND userSession.sessionStatus=:state AND (userSession.checkInStatus IS NULL OR userSession.checkInStatus = 'CHECK_OUT')")
    boolean isAnyUsersRegisteredTicketTypesIn(@Param("eventId") long eventId,
                                                 @Param("sessionId") Long sessionId,
                                                  @Param("state") EnumUserSessionStatus state,
                                                 @Param("ticketTypeIds") List<Long> ticketTypeIds);
	
	@Query(" SELECT case when (count(userSession.id) > 0)  then true else false end " +
			" FROM UserSession userSession " +
			" JOIN EventTickets eventTicket ON eventTicket.id=userSession.eventTicketId " +
			" WHERE userSession.sessionId=:sessionId AND userSession.eventId=:eventId " +
			" AND userSession.userId=:userId AND userSession.checkInStatus=:state")
	boolean isUserAvailableState(@Param("eventId") long eventId,
												 @Param("userId") Long userId,
												 @Param("sessionId") Long sessionId,
												 @Param("state") EnumUserSessionStatus state);

    @Query(" SELECT case when (count(userSession.id) > 0)  then true else false end " +
            " FROM UserSession userSession " +
            " JOIN EventTickets eventTicket ON eventTicket.id=userSession.eventTicketId " +
            " WHERE userSession.sessionId=:sessionId AND userSession.eventId=:eventId " +
            " AND userSession.userId=:userId AND userSession.sessionStatus In :state")
    boolean isUserAvailableStateIn(@Param("eventId") long eventId,
                                 @Param("userId") Long userId,
                                 @Param("sessionId") Long sessionId,
                                 @Param("state") List<EnumUserSessionStatus> state);

	@Query(" SELECT DISTINCT(userSession.userId) " +
			" FROM UserSession userSession " +
			" JOIN EventTickets eventTicket ON eventTicket.id=userSession.eventTicketId " +
			" WHERE userSession.sessionId=:sessionId " +
			" AND userSession.eventId=:eventId " +
			" AND eventTicket.ticketingTypeId.id IN (:ticketTypeIds) " +
			" AND userSession.userId NOT IN (:userIds) AND userSession.checkInStatus=:state")
	List<Long> findByEventIdAndSessionIdAndSateAndNotUserIdAndTicketTypesIn(@Param("eventId") long eventId,
																			@Param("sessionId") Long sessionId,
																			@Param("state") EnumUserSessionStatus state,
																			@Param("userIds") List<Long> userIds,
																			@Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Query(" SELECT DISTINCT(uSession.userId)  from UserSession uSession where uSession.eventId=:eventId and uSession.sessionId=:sessionId")
    List<Long> findUserByEventIdAndSessionId( @Param("sessionId") Long sessionId, @Param("eventId") long eventId);

    @Query(" SELECT DISTINCT(uSession.userId)  from UserSession uSession where uSession.eventId=:eventId and uSession.sessionId=:sessionId and" +
            " uSession.checkInTime IS NOT NULL and (uSession.checkInStatus NOT IN ('CHECK_OUT', 'MEETING_LEFT') OR uSession.checkInStatus IS NULL)")
    List<Long> findUserByEventIdAndSessionIdAndCheckInStatus( @Param("sessionId") Long sessionId, @Param("eventId") long eventId);

	@Transactional
	@Modifying
    @Caching(evict = {
            @CacheEvict(value = "findAllBySessionId", key = "#p2",condition="#p2!=null")
    })
	@Query("UPDATE UserSession SET sessionStatus=:state WHERE eventId=:eventId AND sessionId=:sessionId AND userId IN :userIds")
	void updateUserState(@Param("state") EnumUserSessionStatus state,
										@Param("eventId") long eventId,
										@Param("sessionId") Long sessionId,
										@Param("userIds") List<Long> userIds);

    @Transactional
    @Modifying
    @Caching(evict = {
            @CacheEvict(value = "findAllBySessionId", key = "#p2",condition="#p2!=null")
    })
    @Query("UPDATE UserSession SET checkInStatus=:state WHERE eventId=:eventId AND sessionId=:sessionId AND userId IN :userIds")
    void updateUserCheckInState(@Param("state") EnumUserSessionStatus state,
                         @Param("eventId") long eventId,
                         @Param("sessionId") Long sessionId,
                         @Param("userIds") List<Long> userIds);

	@Query("  SELECT sessionId FROM UserSession " +
			" WHERE eventId=:eventId AND userId=:userId")
	List<Long> findByEventIdAndUserId(@Param("eventId") long eventId,
									  @Param("userId") Long userId);

	List<UserSession> findBySessionIdAndUserIdAndEventId(Long sessionId, Long userId, long event);

	@Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
			" sessionId, COUNT(1) "+
			" )" +
			" FROM UserSession " +
			" WHERE sessionId IN (:sessionIds)" +
            " AND sessionStatus = :sessionStatus " +
			" GROUP BY sessionId ")
	List<IdCountDto> getRegisterCountBySessionIdsAndSessionStatus(@Param("sessionIds") List<Long> sessionIds, @Param("sessionStatus") EnumUserSessionStatus sessionStatus);

	@Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
			" sessionId, COUNT(1) "+
			" )" +
			" FROM UserSession " +
			" WHERE sessionId IN (:sessionIds) " +
			" AND checkInTime IS NOT NULL AND (checkInStatus !='CHECK_OUT' OR checkInStatus IS NULL ) " +
			" GROUP BY sessionId ")
	List<IdCountDto> countSessionAttendeeBySessionIdIn(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
            " sessionId, COUNT(1) "+
            " )" +
            " FROM UserSession " +
            " WHERE sessionId IN (:sessionIds) " +
            " AND checkInTime IS NOT NULL AND (checkInStatus !='CHECK_OUT' AND checkInStatus != 'MEETING_LEFT')   " +
            " GROUP BY sessionId ")
    List<IdCountDto> countSessionAttendeeByNetworkingSessionIdIn(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
            " sessionId, COUNT(1) "+
            " )" +
            " FROM UserSession " +
            " WHERE sessionId IN (:sessionIds) " +
            " AND checkInTime IS NOT NULL AND (checkInStatus !='CHECK_OUT' OR checkInStatus IS NULL) " +
            " AND eventTicketId IS NOT NULL GROUP BY sessionId ")
    List<IdCountDto> countSessionAttendeeByEventTicketIdAndSessionIdIn(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT COUNT(1) FROM UserSession WHERE eventId =:eventId AND checkInTime IS NOT NULL AND (checkInStatus !='CHECK_OUT' OR checkInStatus IS NULL)")
    Integer countSessionAttendeeByEventId(@Param("eventId") Long eventId);

    //Moved to RO remove this method
    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
            " userId, COUNT(1) " +
            " )" +
            " FROM UserSession " +
            " WHERE eventId IN (:eventIds) " +
            " AND userId IN (:userIds) " +
            " AND checkInTime IS NOT NULL AND (checkInStatus !='CHECK_OUT' OR checkInStatus IS NULL) " +
            " GROUP BY userId ")
    List<IdCountDto> countSessionAttendeeByEventAndUserIds(@Param("eventIds") List<Long> eventIds, @Param("userIds") List<Long> userIds);


    @Query("SELECT COUNT(1) " +
            " FROM UserSession " +
            " WHERE sessionId =:sessionId " +
            " AND checkInTime IS NOT NULL AND (checkInStatus !='CHECK_OUT' OR checkInStatus IS NULL)")
    long countSessionAttendeeBySessionId(@Param("sessionId") Long sessionId);

    @Query("SELECT COUNT(1) " +
            " FROM UserSession " +
            " WHERE sessionId =:sessionId " +
            " AND checkInTime IS NOT NULL AND (checkInStatus NOT IN ('CHECK_OUT','MEETING_LEFT') OR checkInStatus IS NULL) AND eventTicketId IS NOT NULL")
    long countSessionAttendeeByEventTicketIdAndSessionId(@Param("sessionId") Long sessionId);

    @Query(" SELECT new com.accelevents.session_speakers.dto.RegisterdHolderUsers( " +
			"userSession.eventTicketId, userSession.userId," +
			"   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   userSession.isBookmarked," +
			"   CASE WHEN userSession.checkInTime IS NULL OR userSession.checkInStatus ='CHECK_OUT' then false ELSE true END "+
			" )" +
			" FROM UserSession userSession JOIN userSession.user user" +
			" WHERE userSession.sessionId=:sessionId ")
	List<RegisterdHolderUsers> findRegisteredUserBySessionId(@Param("sessionId") long sessionId);

    @Query(" SELECT new com.accelevents.session_speakers.dto.RegisterdHolderUsers( " +
            "userSession.eventTicketId, userSession.userId," +
            "   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   userSession.isBookmarked," +
            "   userSession.sessionStatus," +
            "   CASE WHEN userSession.checkInTime IS NULL OR userSession.checkInStatus ='CHECK_OUT' then false ELSE true END "+
            " )" +
            " FROM UserSession userSession JOIN userSession.user user" +
            " WHERE userSession.sessionId=:sessionId ")
    List<RegisterdHolderUsers> getRegisteredUserBySessionId(@Param("sessionId") long sessionId);

    @Query(" SELECT new com.accelevents.session_speakers.dto.RegisterdHolderUsers( " +
            "userSession.eventTicketId, userSession.userId," +
            "   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   userSession.isBookmarked," +
            "   CASE WHEN userSession.checkInTime IS NULL OR userSession.checkInStatus ='CHECK_OUT' then false ELSE true END "+
            " )" +
            " FROM UserSession userSession JOIN userSession.user user" +
            " WHERE userSession.sessionId=:sessionId ")
    Page<RegisterdHolderUsers> findRegisteredUserBySessionIdPage(@Param("sessionId") long sessionId, Pageable pageable);

    @Query(" SELECT new com.accelevents.session_speakers.dto.RegisterdHolderUsers( " +
            "userSession.eventTicketId, userSession.userId," +
            "   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   CASE WHEN userSession.checkInTime IS NULL then false ELSE true END ,"+
            "   CASE WHEN userSession.checkInStatus='CHECK_OUT' OR userSession.checkInStatus IS NULL THEN userSession.sessionStatus ELSE userSession.checkInStatus END ,"+
            "   ticketingTypeId.ticketTypeName," +
            "   ticketingTypeId.id," +
            "   ticketingTypeId.ticketType, " +
            "   eventTickets.barcodeId," +
            "   userSession.checkInTime" +
            " )" +
            " FROM UserSession userSession LEFT JOIN userSession.user user" +
            " LEFT JOIN userSession.eventTickets eventTickets" +
            " LEFT JOIN eventTickets.ticketingTypeId ticketingTypeId" +
            " WHERE userSession.sessionId =:sessionId and ((coalesce(:checkInState,1)='CHECK_OUT' AND userSession.checkInStatus IS NULL) OR userSession.checkInStatus IN (:checkInState)) AND userSession.sessionStatus IN (:sessionStatus) " +
            " and ( (coalesce(:ticketTypeIds, 1)=1 OR ticketingTypeId.id  in (:ticketTypeIds)) and (coalesce(:ticketTypes, 1)=1 OR ticketingTypeId.ticketType IN (:ticketTypes)))")
    List<RegisterdHolderUsers> findRegisteredUserSessionBySessionId(@Param("sessionId") Long sessionId, @Param("ticketTypeIds")List<Long> ticketTypeIds, @Param("ticketTypes")List<TicketType> ticketTypes,
                                                                    @Param("checkInState") List<EnumUserSessionStatus> checkInState, @Param("sessionStatus") List<EnumUserSessionStatus> sessionStatus);


    @Query(" SELECT count(distinct userSession)" +
            " FROM UserSession userSession WHERE userSession.sessionStatus IN (:sessionStatus) AND (userSession.checkInStatus IS NULL OR userSession.checkInStatus IN ('CHECK_OUT','MEETING_LEFT')) " +
            " AND userSession.sessionId = :sessionId AND userSession.eventTicketId IS NOT NULL ")
    Long countUserSessionBySessionStatusAndSessionId(@Param("sessionStatus") List<EnumUserSessionStatus> sessionStatus, @Param("sessionId") Long sessionId);

    @Query(" SELECT count(distinct userSession)" +
            " FROM UserSession userSession WHERE userSession.checkInStatus IN (:checkInStatus) AND userSession.sessionId = :sessionId AND userSession.eventTicketId IS NOT NULL")
    Long countUserSessionByCheckInStatusAndSessionId(@Param("checkInStatus") List<EnumUserSessionStatus> checkInStatus, @Param("sessionId") Long sessionId);

	@Query(" SELECT new com.accelevents.session_speakers.dto.AttendeeSession( " +
			"session.id,session.title,session.startTime, session.endTime, userSession.sessionStatus, " +
			"   CASE WHEN userSession.checkInTime IS NULL then false ELSE true END "+
			" )" +
			" FROM UserSession userSession JOIN userSession.user user" +
			" JOIN userSession.session session" +
			" WHERE userSession.eventId=:eventId" +
			" AND userSession.userId=:userId " +
            " AND userSession.sessionStatus <> 'BOOKMARKED' " +
            " AND (:searchString IS NULL OR LOWER(session.title) LIKE LOWER(CONCAT('%', :searchString, '%'))) ")
	List<AttendeeSession> findAttendeeSessionsByUserIdNotBookmarked(@Param("eventId") long eventId,
                                                                    @Param("userId") long userId,
                                                                    @Param("searchString") String searchString);

	@Query(" SELECT new com.accelevents.session_speakers.dto.AttendeeSession(" +
			" session.id,session.title,session.startTime, session.endTime," +
			" CASE WHEN userSession.checkInTime <> NULL AND (userSession.checkInStatus !='CHECK_OUT' OR userSession.checkOutTime IS NULL) THEN true ELSE false END"+
			" )" +
			" FROM UserSession userSession JOIN userSession.user user" +
			" JOIN userSession.session session" +
			" WHERE userSession.eventId=:eventId" +
			" AND userSession.userId=:userId" +
			" AND session.format=:meetUp")
	List<AttendeeSession> findAttendeeNetworkingSessionsByUserId(@Param("eventId") long eventId,
																 @Param("userId") long userId,
																 @Param("meetUp") EnumSessionFormat meetUp);


	@Query("SELECT userSession FROM UserSession userSession" +
			" WHERE userSession.userId=:userId" +
			" AND userSession.sessionId IN (:sessionIds)")
	List<UserSession> findRegisteredEventTicketIdByUserAndSessionId(@Param("userId") Long userId,
													   @Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT COUNT(userSession) FROM UserSession userSession" +
            " WHERE userSession.eventTicketId = :eventTicketId" +
            " AND userSession.sessionStatus = 'REGISTERED'")
    BigInteger countByEventTicketId(@Param("eventTicketId") Long eventTicketId);

    @Query("SELECT COUNT(userSession) FROM UserSession userSession WHERE userSession.sessionId = :sessionId AND userSession.sessionStatus = :sessionStatus")
    BigInteger getRegisterCountBySessionId(@Param("sessionId") Long sessionId, @Param("sessionStatus") EnumUserSessionStatus sessionStatus);

	@Query(" SELECT ticketingTypeId.id FROM UserSession userSession " +
			" JOIN userSession.eventTickets eventTickets " +
			" JOIN eventTickets.ticketingTypeId as ticketingTypeId " +
			" WHERE userSession.eventId=:eventId " +
			" AND userSession.userId=:userId " +
			" AND userSession.sessionId=:sessionId")
	List<Long> getTicketingTypeIdByEventIdAndUserIdAndSessionId(@Param("eventId") long eventId,
															   @Param("userId") Long userId,@Param("sessionId") Long sessionId);



	@Query(" SELECT eventTicketId FROM UserSession  " +
			" WHERE eventId=:eventId AND userId=:userId and sessionId=:sessionId and sessionStatus!='BOOKMARKED'")
	List<Long> getEventTicketIdsByEventIdAndUserIdAndSessionId(@Param("eventId") long eventId,
									  @Param("userId") Long userId,@Param("sessionId") Long sessionId);

	@Query(" SELECT userSession.eventTicketId FROM UserSession userSession " +
			"JOIN userSession.eventTickets eventTickets " +
			"JOIN eventTickets.ticketPurchaserId purchaser " +
			"JOIN eventTickets.holderUserId holder " +
			" WHERE userSession.eventId=:eventId " +
			" AND userSession.sessionId=:sessionId"+
			" AND (purchaser.userId =:userId OR holder.userId=:userId)" )
	List<Long> getEventTicketIdsByEventIdAndPurchaserUserIdAndSessionId(@Param("eventId") long eventId,
																@Param("userId") Long userId,
																@Param("sessionId") Long sessionId);

    @Query("SELECT userSession.eventTicketId FROM UserSession userSession" +
            " WHERE userSession.userId=:userId AND userSession.eventId=:eventId" )
    List<Long> findEventTicketIdByUserIdAndEventId(@Param("userId") Long userId, @Param("eventId") long eventId);


    @Query("SELECT count(sessionId) FROM UserSession WHERE eventId=:eventId AND userId=:userId")
    Long userSessionCount(@Param("eventId") long eventId, @Param("userId") Long userId);

    @Modifying
    @Caching(evict = {
            @CacheEvict(value = "findAllBySessionId", key = "#p0",condition="#p0!=null")
    })
    @Query("UPDATE UserSession SET recordStatus=:status WHERE sessionId=:sessionId")
    void updateStatusToDeleteBySessionId(@Param("sessionId") Long sessionId, @Param("status") RecordStatus status);

	@Query("SELECT COUNT(DISTINCT userSession.userId) FROM UserSession userSession" +
			" WHERE userSession.sessionId=:sessionId AND (userSession.checkInStatus='CHECK_IN_AVAILABLE' OR userSession.checkInStatus='IN_MEETING')")
	int getCheckInUserCountBySession(@Param("sessionId") Long sessionId);

    @Cacheable(value = "findAllBySessionId", key = "#p0",condition="#p0!=null", unless="#result == null")
    List<UserSession> findAllBySessionId(long sessionId);

	@Query("SELECT userSession FROM UserSession userSession" +
			" WHERE userSession.userId=:userId" +
			" AND userSession.sessionId =:sessionId and userSession.checkInStatus='CHECK_IN_AVAILABLE' ")
	List<UserSession> findUserByUserAndSessionIdAndCheckedInAvailable(@Param("userId") Long userId,
																	  @Param("sessionId") Long sessionId);

	@Query(value = " select distinct u.first_name,u.last_name,u.email ,ss.id,et.holder_user_id, " +
			" case when us.check_in_time IS NULL and us.session_status IS NOT NULL THEN 'REGISTERED' when us.check_in_time IS NOT NULL then 'ATTENDED' else '' end as status " +
			" from event_tickets et " +
			" JOIN event_ticket_type ett on ett.id=et.ticketing_type_id and (et.rec_status<>'CANCEL' or et.rec_status is NULL) " +
			" JOIN ticketing tt on ett.ticketing_id=tt.id " +
			" JOIN sessions ss on tt.event_id=ss.event_id and ss.rec_status<>'DELETE'" +
			" JOIN users u on et.holder_user_id=u.user_id "+
			" LEFT JOIN user_sessions us on et.holder_user_id=us.user_id and tt.event_id=us.event_id and ss.id=us.session_id and us.rec_status<>'DELETE' and us.user_id=u.user_id " +
			" where tt.event_id=:eventId group by ss.id,et.holder_user_id order by ss.id asc ", nativeQuery = true)
	List<Object[]> findAllAttendeeSessionsByEventId(@Param("eventId") Long eventId); // TODO : Refactor to spring data jpa query


    @Caching(evict = {
            @CacheEvict(value = "findAllBySessionId", key = "#p0.sessionId",condition="#p0!=null"),
            @CacheEvict(value = "getListOfSessionIds", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "getRegisteredSessionIdsOfUser", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "getBookmarkedSessionIdsOfUser", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "getWaitedListedSessionIdsOfUser", key = "#p0.eventId.toString() + #p0.userId.toString()")
    })
	UserSession save(UserSession userSession);

	@Caching(evict = {
			@CacheEvict(value = "findAllBySessionId", key = "#p0.sessionId",condition="#p0!=null"),
            @CacheEvict(value = "getListOfSessionIds", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "getRegisteredSessionIdsOfUser", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "getBookmarkedSessionIdsOfUser", key = "#p0.eventId.toString() + #p0.userId.toString()"),
            @CacheEvict(value = "getWaitedListedSessionIdsOfUser", key = "#p0.eventId.toString() + #p0.userId.toString()")
    })
    void delete(UserSession userSession);

    @Modifying
    @Caching(evict = {
        @CacheEvict(value = "findAllBySessionId", allEntries = true)
    })
    @Query("UPDATE UserSession SET recordStatus=:status WHERE userId=:userId")
    void updateStatusToAnonymizedByUserID(@Param("userId") Long userId, @Param("status") RecordStatus status);

    @Query("select count(us.sessionId) from UserSession us join Session s on us.sessionId=s.id where us.eventId=:eventId and s.format=:sessionFormat and us.userId=:userId ")
    Long getRegisteredSessionCountForUserBySessionFormat(@Param("eventId") long eventId,@Param("userId") Long userId,@Param("sessionFormat") EnumSessionFormat sessionFormat);

    void deleteByEventId(Long eventId);

    @Query("SELECT COUNT(DISTINCT userSession.userId) FROM UserSession userSession" +
            " WHERE userSession.sessionId=:sessionId AND userSession.eventId=:eventId")
    Integer getUserRegisteredSessionBySession(@Param("sessionId") Long sessionId,@Param("eventId") long eventId);

    @Query("SELECT userSession.sessionId, COUNT(DISTINCT userSession.userId) " +
           "FROM UserSession userSession " +
           "WHERE userSession.sessionId IN (:sessionIds) AND userSession.eventId = :eventId " +
           "GROUP BY userSession.sessionId")
    List<Object[]> getUserRegisteredSessionsBySessionIds(@Param("sessionIds") List<Long> sessionIds, 
                                                       @Param("eventId") long eventId);

    @Query("SELECT COUNT(1) FROM UserSession userSession" +
            " WHERE userSession.sessionId NOT IN (:sessionIds) AND userSession.userId=:userId and userSession.eventId = :eventId AND userSession.sessionStatus='REGISTERED'")
    Integer countBySessionIdsAndUserId(@Param("sessionIds") List<Long> sessionIds,@Param("userId") Long userId, @Param("eventId") Long eventId);


    @Query("select us.sessionId , CASE WHEN us.checkInTime IS NULL OR us.checkInStatus='CHECK_OUT' then false ELSE true END  FROM UserSession us" +
            " WHERE us.sessionId IN (:sessionIds) AND us.userId=:userId")
    List<Object[]> getCheckInSessionByUserIdAndSessionId(@Param("sessionIds") List<Long> sessionIds, @Param("userId") Long userId);

    List<UserSession> findByUserIdAndEventId(Long userId, Long eventId);

    List<UserSession> findByUserIdAndEventIdAndEventTicketId(Long userId, Long eventId, Long eventTicketId);

    @Query(value = "SELECT COUNT(DISTINCT userSession.userId) FROM UserSession userSession WHERE userSession.eventId=:eventId AND userSession.checkInTime IS NOT NULL" +
            " AND userSession.eventTicketId IS NOT NULL AND (userSession.checkInStatus IS NULL OR userSession.checkInStatus NOT IN ('CHECK_OUT','MEETING_LEFT'))")
    Long countUserSessionByEventIdAndCheckInStatusOrCheckInTimeIsNull(@Param("eventId") Long eventId);

    @Query(value = "SELECT COUNT(DISTINCT userSession.userId) FROM UserSession userSession WHERE userSession.eventId=:eventId AND userSession.eventTicketId IS NOT NULL")
    Long countUserRegisteredByEventIdAndEventTicketIdIsNotNull(@Param("eventId") Long eventId);


    @Query("SELECT COUNT(uSession.userId) from UserSession uSession where uSession.eventId=:eventId and uSession.sessionId=:sessionId and " +
            "  uSession.checkInTime IS NOT NULL AND (uSession.checkInStatus NOT IN ('CHECK_OUT', 'MEETING_LEFT') OR uSession.checkInStatus IS NULL)")
    Long countByEventIdAndSessionIdAndCheckInStatus(@Param("sessionId") Long sessionId, @Param("eventId") long eventId);


    @Query(" SELECT COUNT(uSession.userId)  from UserSession uSession " +
            " JOIN EventTickets eventTicket ON eventTicket.id=uSession.eventTicketId " +
            " JOIN TicketingType et on et.id=eventTicket.ticketingTypeOnlyId " +
            " where uSession.eventId=:eventId AND uSession.sessionId=:sessionId AND (uSession.checkInStatus NOT IN ('CHECK_OUT', 'MEETING_LEFT') OR uSession.checkInStatus IS NULL)" +
            " AND uSession.checkInTime IS NOT NULL AND (et.ticketTypeFormat IN (:ticketTypeFormat))")
    Long countByEventIdAndSessionIdAndCheckInStatusAndTicketTypeFormat(@Param("sessionId") Long sessionId, @Param("eventId") long eventId, @Param("ticketTypeFormat")List<TicketTypeFormat> ticketTypeFormats);


    @Query("SELECT COUNT(uSession.userId) from UserSession uSession where uSession.eventId=:eventId and uSession.sessionId=:sessionId and " +
            "  uSession.checkInTime IS NOT NULL AND (uSession.checkInStatus NOT IN ('CHECK_OUT', 'MEETING_LEFT') OR uSession.checkInStatus IS NULL) AND uSession.userId IN (:userIds) AND uSession.eventTicketId IS NULL")
    Long countByEventIdAndSessionIdAndCheckInStatusAndUserIdIn(@Param("sessionId") Long sessionId, @Param("eventId") long eventId, @Param("userIds") List<Long> userIds);

    @Query( "SELECT new com.accelevents.session_speakers.dto.RegisterdHolderUsers( " +
            "userSession.eventTicketId, userSession.userId," +
            "   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   CASE WHEN userSession.checkInTime IS NULL then false ELSE true END ,"+
            "   CASE WHEN userSession.checkInStatus='CHECK_OUT' OR userSession.checkInStatus IS NULL THEN userSession.sessionStatus ELSE userSession.checkInStatus END ,"+
            "   ticketingTypeId.ticketTypeName," +
            "   ticketingTypeId.id," +
            "   ticketingTypeId.ticketType, " +
            "   eventTickets.barcodeId," +
            "   userSession.checkInTime" +
            " )" +
            " FROM UserSession userSession LEFT JOIN userSession.user user" +
            " LEFT JOIN userSession.eventTickets eventTickets" +
            " LEFT JOIN eventTickets.ticketingTypeId ticketingTypeId" +
            " WHERE userSession.sessionId =:sessionId and ((coalesce(:checkInState,1)='CHECK_OUT' AND userSession.checkInStatus IS NULL) OR userSession.checkInStatus IN (:checkInState)) AND userSession.sessionStatus IN (:sessionStatus) " +
            " and ( (coalesce(:ticketTypeIds, 1)=1 OR ticketingTypeId.id  in (:ticketTypeIds)) and (coalesce(:ticketTypes, 1)=1 OR ticketingTypeId.ticketType IN (:ticketTypes)))")
    Page<RegisterdHolderUsers> findRegisteredUserSessionBySessionIdWithPagination(@Param("sessionId") Long sessionId, @Param("ticketTypeIds")List<Long> ticketTypeIds, @Param("ticketTypes")List<TicketType> ticketTypes,
                                                                                  @Param("checkInState") List<EnumUserSessionStatus> checkInState, @Param("sessionStatus") List<EnumUserSessionStatus> sessionStatus, Pageable pageable);

    @Query( "SELECT new com.accelevents.session_speakers.dto.RegisterdHolderUsers( " +
            "userSession.eventTicketId, userSession.userId," +
            "   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   CASE WHEN userSession.checkInTime IS NULL then false ELSE true END ,"+
            "   CASE WHEN userSession.checkInStatus='CHECK_OUT' OR userSession.checkInStatus IS NULL THEN userSession.sessionStatus ELSE userSession.checkInStatus END ,"+
            "   ticketingTypeId.ticketTypeName," +
            "   ticketingTypeId.id," +
            "   ticketingTypeId.ticketType, " +
            "   eventTickets.barcodeId," +
            "   userSession.checkInTime" +
            " )" +
            " FROM UserSession userSession LEFT JOIN userSession.user user" +
            " LEFT JOIN userSession.eventTickets eventTickets" +
            " LEFT JOIN eventTickets.ticketingTypeId ticketingTypeId" +
            " WHERE userSession.sessionId =:sessionId and ((coalesce(:checkInState,1)='CHECK_OUT' AND userSession.checkInStatus IS NULL) OR userSession.checkInStatus IN (:checkInState)) AND userSession.sessionStatus IN (:sessionStatus) " +
            " and ( (coalesce(:ticketTypeIds, 1)=1 OR ticketingTypeId.id  in (:ticketTypeIds)) and (coalesce(:ticketTypes, 1)=1 OR ticketingTypeId.ticketType IN (:ticketTypes)))" +
            "AND (cast(lower(CONCAT(eventTickets.holderFirstName,' ',eventTickets.holderLastName)) as string) LIKE %:searchStr% " +
            " OR cast(lower(eventTickets.holderEmail)as string) LIKE %:searchStr% " +
            " OR cast(eventTickets.holderPhoneNumber as string) LIKE %:searchStr% ) ")
    Page<RegisterdHolderUsers> findRegisteredUserSessionBySessionIdWithPaginationAndSearch(@Param("sessionId") Long sessionId, @Param("ticketTypeIds") List<Long> ticketTypeIds, @Param("ticketTypes")List<TicketType> ticketTypes,
                                                                                           @Param("checkInState") List<EnumUserSessionStatus> checkInState, @Param("sessionStatus") List<EnumUserSessionStatus> sessionStatus, Pageable pageable, @Param("searchStr") String search);


    @Query("SELECT COUNT(uSession.userId) from UserSession uSession where uSession.eventId=:eventId and uSession.sessionId=:sessionId and " +
            "  uSession.checkInTime IS NOT NULL AND (uSession.checkInStatus NOT IN ('CHECK_OUT', 'MEETING_LEFT') OR uSession.checkInStatus IS NULL) AND uSession.userId IN (:userIds)")
    Long countUserSessionByEventIdAndSessionIdAndUserIds(@Param("sessionId") Long sessionId, @Param("eventId") long eventId, @Param("userIds") List<Long> userIds);

    @Query(" SELECT COUNT(uSession.userId)  from UserSession uSession " +
            " JOIN EventTickets eventTicket ON eventTicket.id=uSession.eventTicketId " +
            " JOIN TicketingType et on et.id=eventTicket.ticketingTypeOnlyId " +
            " where uSession.eventId=:eventId AND uSession.sessionId=:sessionId AND (uSession.checkInStatus NOT IN ('CHECK_OUT', 'MEETING_LEFT') OR uSession.checkInStatus IS NULL)" +
            " AND uSession.checkInTime IS NOT NULL AND (et.ticketTypeFormat IN (:ticketTypeFormat)) " +
            " AND uSession.userId IN (:userIds)")
    Long countUserSessionByEventIdAndSessionIdAndUserIdsAndTicketTypeFormat(@Param("sessionId") Long sessionId, @Param("eventId") long eventId,@Param("userIds") List<Long> userIds ,@Param("ticketTypeFormat")List<TicketTypeFormat> ticketTypeFormats);



    @Query("SELECT uSession.eventTicketId, uSession.sessionId from UserSession uSession where uSession.eventId=:eventId and " +
            "  uSession.checkInTime IS NOT NULL AND uSession.checkInStatus=:checkInStatus AND uSession.userId IN (:userIds) AND uSession.eventTicketId IN (:eventTicketIds)")
    List<Object[]> findByEventIdAndEventTicketIdInAndUserIdInAndCheckInStatus(@Param("eventId") long eventId, @Param("eventTicketIds") List<Long> eventTicketIds, @Param("userIds") List<Long> userIds, @Param("checkInStatus") EnumUserSessionStatus checkInStatus);

    @Query("SELECT uSession from UserSession as uSession where uSession.eventId=:eventId and " +
            "  uSession.checkInTime IS NOT NULL AND uSession.sessionId=:sessionId AND uSession.userId =:userId AND uSession.eventTicketId =:eventTicketId")
    UserSession findBySessionIdAndUserIdAndEventIdAndTicketId(@Param("sessionId") long sessionId,@Param("userId") Long userId,@Param("eventId") long eventId, @Param("eventTicketId")Long eventTicketId);

    @Query("SELECT uSession from UserSession as uSession where uSession.eventId=:eventId " +
            " AND uSession.sessionId=:sessionId AND uSession.userId =:userId AND uSession.eventTicketId =:eventTicketId "+
            " AND (uSession.checkInStatus IS NULL OR uSession.checkInStatus IN ('CHECK_OUT','MEETING_LEFT')) " )
    UserSession findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(@Param("sessionId") long sessionId,@Param("userId") Long userId,@Param("eventId") long eventId, @Param("eventTicketId")Long eventTicketId);

    @Query("SELECT case when (count(userSession.id) > 0)  then true else false end "+
            "FROM UserSession userSession "+
            " WHERE userSession.sessionId=:sessionId AND userSession.userId=:userId AND userSession.eventId=:eventId AND userSession.sessionStatus='REGISTERED' ")
    boolean isUserRegisteredInSession(@Param("sessionId") long sessionId, @Param("userId") Long userId, @Param("eventId") long eventId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto( " +
            " sessionId, COUNT(1) "+
            " )" +
            " FROM UserSession " +
            " WHERE sessionId IN (:sessionIds)" +
            " AND eventId= :eventId" +
            " AND eventTicketId IS NOT NULL AND sessionStatus='REGISTERED' "+
            " GROUP BY sessionId ")
    List<IdCountDto> countBySessionIdInAndEventIdAndTicketIdIsNotNull(@Param("sessionIds") List<Long> sessionIds, @Param("eventId") long eventId);


    @Query("SELECT COUNT(1) "+
            " FROM UserSession " +
            " WHERE sessionId = :sessionId" +
            " AND eventId= :eventId" +
            " AND eventTicketId IS NOT NULL AND sessionStatus='REGISTERED' ")
    Integer countBySessionIdAndTicketIdIsNotNullAndEventId(@Param("sessionId") Long sessionId, @Param("eventId") long eventId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto(us.sessionId, COUNT(us)) " +
            "FROM UserSession us " +
            "WHERE us.sessionId IN (:sessionIds) " +
            "AND us.eventId= :eventId " +
            "AND us.isBookmarked = true " +
            "GROUP BY us.sessionId")
    List<IdCountDto> countSessionBookmarkedAttendeeBySessionIdsAndEventId(@Param("sessionIds") List<Long> sessionIds, @Param("eventId") long eventId);

    @Query(" SELECT new com.accelevents.session_speakers.dto.RegisterdHolderUsers( " +
            "userSession.eventTicketId, userSession.userId," +
            "   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   userSession.isBookmarked," +
            "   CASE WHEN userSession.checkInTime IS NULL OR userSession.checkInStatus ='CHECK_OUT' then false ELSE true END "+
            " )" +
            " FROM UserSession userSession JOIN userSession.user user" +
            " WHERE userSession.sessionId=:sessionId AND userSession.sessionStatus=:sessionStatus")
    List<RegisterdHolderUsers> findRegisteredUsersBySessionIdAndRegisteredStatus(@Param("sessionId") long sessionId,@Param("sessionStatus") EnumUserSessionStatus sessionStatus);

    @Query("SELECT uSession from UserSession as uSession where uSession.eventId=:eventId and " +
            " uSession.sessionId=:sessionId AND uSession.eventTicketId =:eventTicketId AND uSession.sessionStatus =:sessionStatus")
    List<UserSession> findRegisteredUserByEventTicketIdAndSessionIdAndEventId(@Param("eventTicketId") Long eventTicketId,@Param("sessionId")Long sessionId,@Param("eventId") Long eventId,@Param("sessionStatus") EnumUserSessionStatus sessionStatus);

    @Cacheable(value = "getRegisteredSessionIdsOfUser", key = "#p0.toString() + #p1.toString()", unless="#result == null")
    @Query("SELECT session.sessionId FROM UserSession session WHERE session.userId=:userId AND session.eventId=:eventId AND session.sessionStatus <> 'BOOKMARKED' AND session.sessionStatus <> 'WAITLISTED'")
    List<Long> getRegisteredSessionIdsByEventIdAndUserId(@Param("eventId") Long eventId, @Param("userId") Long userId);

    @Cacheable(value = "getBookmarkedSessionIdsOfUser", key = "#p0.toString() + #p1.toString()", unless="#result == null")
    @Query("SELECT session.sessionId FROM UserSession session WHERE session.userId=:userId AND session.eventId=:eventId AND session.isBookmarked = true")
    List<Long> getBookmarkedSessionIdsByEventIdAndUserId(@Param("eventId") Long eventId, @Param("userId") Long userId);


    @Query("SELECT userSession " +
            "FROM UserSession userSession " +
            "JOIN FETCH userSession.session s " +
            "WHERE userSession.userId = :userId " +
            "AND userSession.eventId = :eventId " +
            "AND userSession.sessionStatus IN (:sessionStatuses) " +
            "AND s.id <> :sessionId " +
            "AND (:checkCapacity = false OR s.capacity > 0) ")
    List<UserSession> findAllByEventIdAndUserIdAndSessionStatusesAndCapacity(
            @Param("sessionId") long sessionId,
            @Param("eventId") long eventId,
            @Param("userId") Long userId,
            @Param("sessionStatuses") List<EnumUserSessionStatus> sessionStatuses,
            @Param("checkCapacity") boolean checkCapacity
    );


    @Query("SELECT us.consumedTagTrackId, COUNT(us.sessionId) " +
            "FROM UserSession us " +
            "JOIN Session s ON us.sessionId = s.id " +
            "WHERE us.eventId = :eventId " +
            "AND us.userId = :userId " +
            "AND us.consumedTagTrackId IN (:trackIds) " +
            "AND us.sessionStatus = 'REGISTERED' "+
            "GROUP BY us.consumedTagTrackId ")
    List<Object[]> getRegisteredSessionCountForUserByTracks(@Param("userId") Long userId,
                                                            @Param("eventId") long eventId,
                                                            @Param("trackIds") List<Long> trackIds);

    @Query("SELECT sessionId, userId, checkInTime "+
            " FROM UserSession " +
            " WHERE sessionId IN (:sessionIds) " +
            " AND checkInTime IS NOT NULL"
            )
    List<Object[]> findAllUserSessionsByCheckInTimeNotNullSessionIdsIn(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT us.sessionId "+
            " FROM UserSession us" +
            " JOIN Session s ON us.sessionId = s.id " +
            " WHERE s.eventId = :eventId " +
            " AND us.userId = :userId" +
            " AND us.checkInTime IS NOT NULL "
    )
    List<Long> findCheckedInSessionIdByEventIdAndUserId(@Param("eventId") long eventId, @Param("userId") long userId);

    @Query("SELECT us FROM UserSession us WHERE us.sessionId = :sessionId AND us.userId = :userId AND us.eventId = :eventId AND us.sessionStatus = :sessionStatus")
    List<UserSession> findBySessionIdAndUserIdAndEventIdAndSessionStatus(@Param("sessionId") Long sessionId,
                                                                        @Param("userId") Long userId,
                                                                        @Param("eventId") Long eventId,
                                                                        @Param("sessionStatus") EnumUserSessionStatus sessionStatus);

    @Cacheable(value = "getWaitedListedSessionIdsOfUser", key = "#p0.toString() + #p1.toString()", unless="#result == null")
    @Query("SELECT session.sessionId FROM UserSession session WHERE session.userId=:userId AND session.eventId=:eventId AND session.sessionStatus='WAITLISTED'")
    List<Long> getWaitListedSessionIdsByEventIdAndUserId(@Param("eventId") Long eventId, @Param("userId") Long userId);

    @Query(" SELECT new com.accelevents.session_speakers.dto.WaitListedHolderUsers( " +
            "userSession.eventTicketId, userSession.userId," +
            "   user.firstName, user.lastName, user.email, user.phoneNumber," +
            "   userSession.registrationDate," +
            "   userSession.sessionStatus," +
            "   userSession.isBookmarked," +
            "   userSession.id" +
            " )" +
            " FROM UserSession userSession JOIN userSession.user user" +
            " WHERE userSession.sessionId=:sessionId AND userSession.sessionStatus='WAITLISTED'" +
            " AND (COALESCE(:searchStr, '') = '' OR " +
            "      LOWER(user.firstName) LIKE %:searchStr% OR " +
            "      LOWER(user.lastName) LIKE %:searchStr% OR " +
            "      CAST(LOWER(CONCAT(user.firstName, ' ', user.lastName)) AS string) LIKE %:searchStr% OR " +
            "      LOWER(user.email) LIKE %:searchStr% OR " +
            "      CAST(user.phoneNumber AS string) LIKE %:searchStr%)")
    Page<WaitListedHolderUsers> findWaitlistedUsersBySessionId(@Param("sessionId") long sessionId, Pageable pageable, @Param("searchStr") String searchStr);

    @Query("SELECT us.userId FROM UserSession us WHERE us.sessionId = :sessionId AND us.sessionStatus = 'WAITLISTED'")
    List<Long> findWaitlistedUserIdsBySessionId(@Param("sessionId") Long sessionId);

    @Query("SELECT us FROM UserSession us WHERE us.sessionId = :sessionId AND us.sessionStatus = 'WAITLISTED'")
    List<UserSession> findBySessionIdAndIsWaitlisted(@Param("sessionId") Long sessionId);

    @Query("SELECT us FROM UserSession us WHERE us.sessionId = :sessionId AND us.eventId = :eventId AND us.userId IN (:userIds)")
    List<UserSession> findBySessionIdAndEventIdAndUserIdIn(@Param("sessionId") Long sessionId,
                                                           @Param("eventId") Long eventId,
                                                           @Param("userIds") List<Long> userIds);
}
