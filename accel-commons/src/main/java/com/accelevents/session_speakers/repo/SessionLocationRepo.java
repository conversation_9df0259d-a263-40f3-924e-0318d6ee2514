package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.SessionLocation;
import com.accelevents.session_speakers.dto.SessionLocationDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface SessionLocationRepo extends CrudRepository<SessionLocation,Long>{

    SessionLocation findByIdAndEventId(Long id, Long eventId);

    @Query("SELECT sl FROM SessionLocation sl " +
            "WHERE sl.eventId = :eventId AND LOWER(sl.name) LIKE CONCAT('%', COALESCE(:searchString, ''), '%')")
    Page<SessionLocation> getSessionLocations(@Param("eventId") Long eventId, @Param("searchString") String searchString, Pageable pageable);

    @Query("SELECT sl.sourceUrl FROM SessionLocation sl WHERE sl.id = :id AND sl.eventId = :eventId")
    String findSourceUrlByIdAndEventId(@Param("id") Long id, @Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionLocationDTO(sl.id, sl.sourceUrl) FROM SessionLocation sl WHERE sl.id IN :id AND sl.eventId = :eventId")
    List<SessionLocationDTO> findSourceUrlByIdsAndEventId(@Param("id") List<Long> ids, @Param("eventId") Long eventId);

    @Query("SELECT s.locationId, COUNT(s.locationId) " +
            "FROM Session s " +
            "WHERE s.eventId = :eventId " +
            "AND s.locationId IN :locationIds " +
            "GROUP BY s.locationId")
    List<Object[]> countSessionsByLocationIds(@Param("eventId") Long eventId, @Param("locationIds") List<Long> locationIds);

    @Query("SELECT s.title" +
            " FROM Session s WHERE s.locationId = :location AND s.eventId = :eventId"+
            " AND (" +
                "(s.startTime <= :startTime AND s.endTime >= :endTime)" +
            ")"
    )
    List<String> findBookedSessionNamesByLocationId(
            @Param("location") long location,
            @Param("eventId") Long eventId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );


    @Query("SELECT sl FROM SessionLocation sl " +
            "WHERE sl.eventId = :eventId")
    List<SessionLocation> getSessionLocationByEventId(Long eventId);

    @Query("SELECT s.id FROM SessionLocation s WHERE s.name = :name AND s.eventId = :eventId")
    Long findIdByName(@Param("name") String name,@Param("eventId") Long eventId);

    @Query("SELECT CASE WHEN COUNT(sl) > 0 THEN true ELSE false END FROM SessionLocation sl WHERE sl.name = :name AND sl.eventId = :eventId")
    Boolean isLocationExistsByNameAndEventId(String name, Long eventId);

    Optional<SessionLocation> findById(Long id);

    @Modifying
    @Query("UPDATE Session s SET s.locationId = NULL WHERE s.locationId = :locationId AND s.eventId = :eventId")
    void removeLocationIdFromSessions(@Param("locationId") Long locationId, @Param("eventId") Long eventId);


    @CacheEvict(value = "SessionByEventAndFormat", allEntries = true)
    SessionLocation save(SessionLocation entity);
}
