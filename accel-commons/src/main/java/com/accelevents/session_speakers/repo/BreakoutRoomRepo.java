package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.BreakoutRoom;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BreakoutRoomRepo extends CrudRepository<BreakoutRoom, Long> {

    @Query("SELECT sbr FROM BreakoutRoom sbr WHERE sbr.eventId=:eventId AND sbr.sessionId=:sessionId")
    List<BreakoutRoom> getAllBreakoutRoomBySessionIdAndEventId(@Param("sessionId") Long sessionId, @Param("eventId") long eventId);

    @Query("SELECT CASE WHEN count (sbr.id) > 0 THEN true ELSE false END FROM BreakoutRoom AS sbr WHERE cast(sbr.roomName as string) =:name AND sbr.eventId=:eventId AND sbr.sessionId=:sessionId")
    boolean isBreakoutRoomPresentWithNameAndEventIdAndSessionId(@Param("name") String sessionBreakoutRoomName,@Param("eventId") long eventId,@Param("sessionId") Long sessionId);

    @Query("SELECT CASE WHEN count (sbr.id) > 0 THEN true ELSE false END FROM BreakoutRoom AS sbr WHERE sbr.id =:id AND sbr.eventId=:eventId AND sbr.sessionId=:sessionId")
    boolean isBreakoutRoomPresentWithIdAndEventIdAndSessionId(@Param("id") Long id,@Param("eventId") long eventId,@Param("sessionId") Long sessionId);

    Optional<BreakoutRoom> findFirstBySessionIdAndEventIdOrderByIdDesc(@Param("sessionId") Long sessionId, @Param("eventId") long eventId);

    Long countAllByEventIdAndSessionId(Long eventId, Long sessionId);
}
