package com.accelevents.session_speakers.repo;

import com.accelevents.common.dto.AttendeeBookedScheduleDto;
import com.accelevents.domain.enums.MeetingOrigin;
import com.accelevents.domain.enums.MeetingStatus;
import com.accelevents.domain.session_speakers.MeetingSchedule;
import com.accelevents.session_speakers.dto.IdCountDto;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface MeetingScheduleRepo extends CrudRepository<MeetingSchedule,Long> {

    @Caching(evict = {
            @CacheEvict(value = "isPreScheduleMeetingScheduled", key = "#p0.senderUserId", condition="#p0!=null"),
            @CacheEvict(value = "isPreScheduleMeetingScheduled", key = "#p0.receiverUserId", condition="#p0!=null"),
            @CacheEvict(value = "isAnyMeetingBookedByUserAndEventAndOrigin", key = "#p0.senderUserId.toString() + #p0.eventId.toString()", condition="#p0!=null"),
            @CacheEvict(value = "isAnyMeetingBookedByUserAndEventAndOrigin", key = "#p0.receiverUserId.toString() + #p0.eventId.toString()", condition="#p0!=null"),
    })
    MeetingSchedule save(MeetingSchedule meetingSchedule);

    @Query("SELECT new com.accelevents.common.dto.AttendeeBookedScheduleDto(meetingSchedule.meetingStartTime,meetingSchedule.meetingEndTime)" +
            " FROM MeetingSchedule meetingSchedule" +
            " WHERE meetingSchedule.eventId=:eventId" +
            " AND (meetingSchedule.senderUserId IN (:userIds) OR meetingSchedule.receiverUserId IN (:userIds))" +
            " AND meetingSchedule.status<>:status" +
            " AND (meetingSchedule.meetingStartTime BETWEEN :startDate AND :endDate OR meetingSchedule.meetingEndTime BETWEEN :startDate AND :endDate)")
    List<AttendeeBookedScheduleDto> findAllBookdedMeetingScheduleByUserIdAndStatusBookedFilterByMonthAndYear(@Param("userIds") List<Long> userIds, @Param("status") MeetingStatus status,
                                                                                                             @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("eventId") Long eventId);


    @Query("SELECT meetingSchedule FROM MeetingSchedule meetingSchedule" +
            " WHERE (meetingSchedule.senderUserId=:userId OR meetingSchedule.receiverUserId=:userId)" +
            " AND meetingSchedule.status=:status AND COALESCE(meetingSchedule.origin, 'NULL') IN (:originList) " +
            " AND meetingSchedule.eventId=:eventId " +
            " ORDER BY meetingSchedule.meetingStartTime DESC")
    Page<MeetingSchedule> findAllMeetingScheduleByUserIdAndEventIdAndStatus(@Param("userId") Long userId, @Param("eventId") Long eventId, @Param("status") MeetingStatus status,@Param("originList") List<MeetingOrigin> originList, Pageable pageable);

    @Query("SELECT meetingSchedule FROM MeetingSchedule meetingSchedule" +
            " WHERE (meetingSchedule.senderUserId=:userId OR meetingSchedule.receiverUserId=:userId)" +
            " AND meetingSchedule.eventId=:eventId AND COALESCE(meetingSchedule.origin, 'NULL') IN (:originList) " +
            " ORDER BY meetingSchedule.meetingStartTime DESC")
    Page<MeetingSchedule> findAllMeetingScheduleByUserIdAndEventId(@Param("userId") Long userId, @Param("eventId") Long eventId, @Param("originList") List<MeetingOrigin> originList, Pageable pageable);

    @Query("SELECT meetingSchedule FROM MeetingSchedule meetingSchedule" +
            " WHERE (meetingSchedule.senderUserId=:userId OR meetingSchedule.receiverUserId=:userId)" +
            " AND meetingSchedule.status IN (:statusList) And COALESCE(meetingSchedule.origin, 'NULL') IN (:originList) " +
            " AND meetingSchedule.eventId=:eventId" +
            " AND meetingSchedule.meetingStartTime >=:startTime"+
            " ORDER BY meetingSchedule.meetingStartTime DESC")
    Page<MeetingSchedule> findAllUpcomingMeetingScheduleByUserIdAndEventIdAndStatus(@Param("userId") Long userId, @Param("eventId") Long eventId,
                                                                            @Param("statusList") List<MeetingStatus> statusList,@Param("startTime") Date startTime, @Param("originList") List<MeetingOrigin> originList, Pageable pageable);

    @Query("SELECT CASE WHEN COUNT(meetingSchedule) > 0 THEN true ELSE false END" +
            " FROM MeetingSchedule meetingSchedule" +
            " WHERE meetingSchedule.eventId=:eventId" +
            " AND (meetingSchedule.senderUserId=:userId OR meetingSchedule.receiverUserId=:userId)" +
            " AND meetingSchedule.status<>'DECLINED'" +
            " AND meetingSchedule.meetingStartTime < :endDate " +
            " AND meetingSchedule.meetingEndTime > :startDate " )
    Boolean isMeetingScheduledBetweenDates(@Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("eventId") Long eventId);

    @Modifying
    @Query("UPDATE MeetingSchedule SET status=:status WHERE id=:id")
    void updateMeetingScheduleStatusById(@Param("id") long id, @Param("status") MeetingStatus status);

    Optional<MeetingSchedule> findByIdAndEventId(Long id, Long eventId);

    @Query("SELECT COUNT(id) FROM MeetingSchedule meetingSchedule"+
            " WHERE (meetingSchedule.senderUserId IN (:userIds) OR meetingSchedule.receiverUserId IN (:userIds))" +
            " AND meetingSchedule.eventId=:eventId")
    long meetingCount(@Param("userIds") List<Long> userIds, @Param("eventId")  Long eventId);

    @Query("SELECT CASE WHEN COUNT(meetingSchedule) > 0 THEN true ELSE false END" +
            " FROM MeetingSchedule meetingSchedule" +
            " WHERE meetingSchedule.eventId=:eventId" +
            " AND meetingSchedule.id<>:id " +
            " AND (meetingSchedule.senderUserId IN (:userIds) OR meetingSchedule.receiverUserId IN (:userIds))" +
            " AND meetingSchedule.status<>'DECLINED'" +
            " AND meetingSchedule.meetingStartTime < :endDate " +
            " AND meetingSchedule.meetingEndTime > :startDate " )
    Boolean isMeetingScheduledBetweenDatesByNotIdAndUserIdList(@Param("id") Long id, @Param("userIds") List<Long> userIds,
                                                               @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("eventId") Long eventId);

    @Query("SELECT meetingSchedule FROM MeetingSchedule meetingSchedule" +
        " WHERE meetingSchedule.senderUserId=:userId" +
        " AND meetingSchedule.status=:status AND COALESCE(meetingSchedule.origin, 'NULL') IN (:originList) " +
        " AND meetingSchedule.eventId=:eventId" +
        " ORDER BY meetingSchedule.meetingStartTime DESC")
    Page<MeetingSchedule> findAllCreatedMeetingScheduleByUserIdAndEventIdAndStatus(@Param("userId") Long userId, @Param("eventId") Long eventId, @Param("status") MeetingStatus status, @Param("originList") List<MeetingOrigin> meetingOriginList, Pageable pageable);

    @Query("SELECT meetingSchedule FROM MeetingSchedule meetingSchedule" +
        " WHERE meetingSchedule.rejectedBy=:userId" +
        " AND meetingSchedule.status=:status AND COALESCE(meetingSchedule.origin, 'NULL') IN (:originList) " +
        " AND meetingSchedule.eventId=:eventId" +
        " ORDER BY meetingSchedule.meetingStartTime DESC")
    Page<MeetingSchedule> findAllRejectedMeetingScheduleByUserIdAndEventIdAndStatus(@Param("userId") Long userId, @Param("eventId") Long eventId, @Param("status") MeetingStatus status, @Param("originList") List<MeetingOrigin> meetingOriginList, Pageable pageable);

    @Modifying
    @Query("DELETE FROM MeetingSchedule meetingSchedule WHERE meetingSchedule.senderUserId=:userId OR meetingSchedule.receiverUserId=:userId")
    void deleteMeetingScheduleByUserId(@Param("userId") Long userId);

    @Query("SELECT COUNT(1) FROM MeetingSchedule meeting"+
            " WHERE (meeting.origin <>:origin OR meeting.origin IS NULL) " +
            " AND meeting.status = 'BOOKED'"+
            " AND meeting.eventId =:eventId")
    long getTotalMeetingBooked(@Param("eventId") Long eventId, @Param("origin") MeetingOrigin origin);


    @Query("SELECT COUNT(1) FROM MeetingSchedule meeting"+
            " WHERE meeting.eventId =:eventId" +
            " AND meeting.origin =:origin" +
            " AND meeting.exhibitorId =:exhibitorId" +
            " AND meeting.status = 'BOOKED'")
    long getTotalMeetingBookedInExhibitor(@Param("eventId") Long eventId,
                                          @Param("origin") MeetingOrigin origin,
                                          @Param("exhibitorId") Long exhibitorId);

    @Query("SELECT COUNT(1) FROM MeetingSchedule meeting"+
            " WHERE meeting.eventId =:eventId" +
            " AND meeting.senderUserId =:userId" +
            " AND meeting.origin =:origin" +
            " AND meeting.status = 'BOOKED'")
    long getTotalBookedMeetingOfUserInOrigin(@Param("eventId") Long eventId,
                                             @Param("userId") Long userId,
                                             @Param("origin") MeetingOrigin origin);

    void deleteMeetingScheduleByEventId(Long eventId);

    @Query(value = "SELECT userId, SUM(t.userCount) from (" +
                " SELECT COUNT(sender_user_id) as userCount, sender_user_id as userId" +
                " FROM meeting_schedule m" +
                " WHERE m.sender_user_id in :userIds " +
                " AND event_id =:eventId " +
                " AND meeting_status = 'BOOKED' " +
                " GROUP BY m.sender_user_id" +
                " UNION ALL" +
                " SELECT COUNT(receiver_user_id) as userCount, receiver_user_id as userId " +
                " FROM meeting_schedule m" +
                " WHERE m.receiver_user_id in :userIds" +
                " AND event_id =:eventId " +
                " AND meeting_status = 'BOOKED' " +
                " GROUP BY m.receiver_user_id " +
                ") " +
            "as t GROUP BY userId", nativeQuery = true)
    List<Object[]> getBookedMeetingByUserIds(@Param("eventId") Long eventId,
                                               @Param("userIds") List<Long> userId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto(meeting.senderUserId, COUNT(1)) " +
            " FROM MeetingSchedule meeting" +
            " WHERE meeting.eventId =:eventId" +
            " AND meeting.senderUserId in :userIds" +
            " GROUP BY meeting.senderUserId")
    List<IdCountDto> getMeetingRequestSentByUserIds(@Param("eventId") Long eventId,
                                                    @Param("userIds") List<Long> userId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto(meeting.receiverUserId, COUNT(1)) " +
            " FROM MeetingSchedule meeting" +
            " WHERE meeting.eventId =:eventId" +
            " AND meeting.receiverUserId in :userIds" +
            " GROUP BY meeting.receiverUserId")
    List<IdCountDto> getMeetingRequestReceivedByUserIds(@Param("eventId") Long eventId,
                                                        @Param("userIds") List<Long> userId);

    @Query(value = " select ms.* from meeting_schedule ms  " +
            " left join users u on ms.sender_user_id=u.user_id  " +
            " left join users u1 on ms.receiver_user_id = u1.user_id " +
            " where ms.event_id = :eventId and ms.origin=:origin and meeting_status<>'CANCEL' " +
            " and (coalesce(:search,'') = '' || lower(u.first_name) like %:search% || lower(u.last_name) like %:search% || " +
            " lower(u1.first_name) like %:search% || concat(lower(u.first_name),' ',lower(u.last_name)) like %:search% || " +
            " concat(lower(u1.first_name),' ',lower(u1.last_name)) like %:search% || " +
            " lower(u1.last_name) like %:search% || lower(ms.meeting_title) like %:search%) " ,
            countQuery = "select count(*) from meeting_schedule ms  " +
                         " left join users u on ms.sender_user_id=u.user_id  " +
                         " left join users u1 on ms.receiver_user_id = u1.user_id " +
                         " where ms.event_id = :eventId and ms.origin=:origin and meeting_status<>'CANCEL' " +
                    " and (coalesce(:search,'') = '' || lower(u.first_name) like %:search% || lower(u.last_name) like %:search% || " +
                    " lower(u1.first_name) like %:search% || concat(lower(u.first_name),' ',lower(u.last_name)) like %:search% || " +
                    " concat(lower(u1.first_name),' ',lower(u1.last_name)) like %:search% || " +
                    " lower(u1.last_name) like %:search% || lower(ms.meeting_title) like %:search%) ", nativeQuery = true
    )
    Page<MeetingSchedule> findAllMeetingScheduleEventIdAndOrigin(@Param("eventId") Long eventId,
                                                                 @Param("origin") String origin,
                                                                 Pageable pageable,
                                                                 @Param("search") String searchString);

    @Query(value = "SELECT u.user_id as sender_user_id, u.first_name  as sender_first_name, u.last_name as sender_last_name, u.email as sender_email, " +
            " u1.user_id as receiver_user_id, u1.first_name as receiver_first_name, u1.last_name as receiver_last_name, u1.email as receiver_email, " +
            " ms.meeting_start_time, ms.meeting_end_time, ms.note, ms.equivalent_timezone " +
            " FROM meeting_schedule ms" +
            " join events e ON ms.event_id=e.event_id" +
            " left join users u ON ms.sender_user_id = u.user_id" +
            " left join users u1 ON ms.receiver_user_id = u1.user_id" +
            " where e.eventurl=:eventUrl and meeting_status=:status " +
            " order by ms.id desc", nativeQuery = true)
    List<Object[]> getMeetingByEventUrlAndStatus(@Param("eventUrl") String eventUrl,
                                                 @Param("status") String status);


    @Cacheable(value = "isPreScheduleMeetingScheduled", key = "#p1", condition="#p1!=null", unless="#result == null")
    @Query("SELECT CASE WHEN COUNT(1)>0 THEN TRUE ELSE FALSE END FROM MeetingSchedule meeting"+
            " WHERE meeting.eventId =:eventId" +
            " AND (meeting.senderUserId =:userId OR meeting.receiverUserId =:userId)" +
            " AND meeting.origin = 'ADMIN_MEETING_REQUEST'" +
            " AND meeting.status <> 'DECLINED'" +
            " AND meeting.status <> 'CANCEL'")
    boolean isPreScheduleMeetingScheduledForEventAndUser(@Param("eventId") Long eventId, @Param("userId") Long userId);

    @Cacheable(value = "isAnyMeetingBookedByUserAndEventAndOrigin", key = "#p0.toString() + #p1.toString()", condition="#p0!=null && #p1!=null", unless="#result == null")
    @Query("SELECT CASE WHEN COUNT(1)>0 THEN TRUE ELSE FALSE END FROM MeetingSchedule meeting"+
            " WHERE meeting.eventId =:eventId" +
            " AND (meeting.senderUserId =:userId OR meeting.receiverUserId =:userId)" +
            " AND COALESCE(meeting.origin, 'NULL') IN (:origin)" +
            " AND meeting.status = 'BOOKED'")
    boolean isAnyMeetingBookedByUserAndEventAndOrigin(@Param("userId") Long userId,@Param("eventId") Long eventId,
                                             @Param("origin") List<MeetingOrigin> origin);

    List<MeetingSchedule> findScheduledMeetingByEventId(Long eventId);


    @Query(value = "select meeting from MeetingSchedule meeting where meeting.eventId=:eventId and meeting.exhibitorId=:exhibitorId and meeting.origin=:origin " +
            " and (:#{#receiverUserId == null} = true or meeting.receiverUserId in (:receiverUserId) or meeting.senderUserId in (:receiverUserId)) and (:#{#status == null} = true or meeting.status in (:status)) and (:#{#location == null} = true or lower(meeting.location) in (:location))")
    List<MeetingSchedule> findByEventIdAndExhibitorIdAndOrigin(@Param("eventId") Long eventId,
                                                               @Param("exhibitorId") Long exhibitorId,
                                                               @Param("origin") MeetingOrigin origin,
                                                               @Param("receiverUserId") List<Long> receiverUserId,
                                                               @Param("status") List<MeetingStatus> status,
                                                               @Param("location") List<String> location);

    Optional<MeetingSchedule> findByExhibitorIdAndIdAndEventId(Long exhibitorId, Long meetingId, Long eventId);

    @Query(value = "select meeting from MeetingSchedule meeting where meeting.eventId IN (:eventIds) and meeting.reminderSent=false and meeting.status='BOOKED'")
    List<MeetingSchedule> findAcceptedScheduledMeetingByEventIdAndStatus(@Param("eventIds") List<Long> eventIds);
}
