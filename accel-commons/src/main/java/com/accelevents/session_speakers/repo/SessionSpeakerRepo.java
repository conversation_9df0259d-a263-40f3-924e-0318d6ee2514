package com.accelevents.session_speakers.repo;

import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumSessionStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.dto.SessionSpeakerIdDto;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface SessionSpeakerRepo extends CrudRepository<SessionSpeaker, Long> {


    @Query("  SELECT new com.accelevents.session_speakers.dto.SpeakerDTO(sSpeaker.speaker, sSpeaker.position, sSpeaker.moderator,sSpeaker.isShowModerator) " +
            " FROM SessionSpeaker AS sSpeaker  " +
            " WHERE sSpeaker.session.id=:sessionId  ORDER BY sSpeaker.position DESC ")
    List<SpeakerDTO> getSpeakersBySession(@Param("sessionId") Long sessionId);

    @Query("  SELECT sSpeaker.session " +
            " FROM SessionSpeaker AS sSpeaker  " +
            " WHERE sSpeaker.speaker.id=:speakerId ")
    List<Session> getSessionBySpeaker(@Param("speakerId") Long speakerId);

    List<Session> findSessionBySpeaker(Speaker speaker);

    @Caching(evict = {
            @CacheEvict(value = "getSessionSpeakerByEventId", allEntries = true),
            @CacheEvict(value = "speakerUserIdBySessionId", allEntries = true)
    })
    @Modifying
    @Query("DELETE FROM SessionSpeaker AS sSpeaker WHERE sSpeaker.speaker.id=:speakerId")
    void deleteBySpeakerId(@Param("speakerId") Long speakerId);

    @Caching(evict = {
            @CacheEvict(value = "getSessionSpeakerByEventId", allEntries = true),
            @CacheEvict(value = "speakerUserIdBySessionId", allEntries = true)
    })
    @Modifying
    @Query("DELETE FROM SessionSpeaker AS sSpeaker WHERE sSpeaker.speakerId in (:speakerId)")
    void deleteBySpeakerIds(@Param("speakerId") List<Long> speakerId);

    @Caching(evict = {
            @CacheEvict(value = "getSessionSpeakerByEventId", allEntries = true),
            @CacheEvict(value = "speakerUserIdBySessionId", key = "#p0",condition="#p0!=null")
    })
    @Modifying
    @Query("DELETE FROM SessionSpeaker AS sSession WHERE sSession.sessionId=:sessionId")
    void deleteBySessionId(@Param("sessionId") Long sessionId);

    @Modifying
    @Query("DELETE FROM SessionSpeaker AS sSession WHERE sSession.sessionId IN (:sessionIds)")
    void deleteBySessionIds(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT ss FROM SessionSpeaker ss WHERE ss.sessionId IN (:sessionIds) ORDER BY ss.position DESC ")
    List<SessionSpeaker> findBySessionIdsIn(@Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT ss FROM SessionSpeaker ss WHERE ss.sessionId =:sessionId ORDER BY ss.position DESC ")
    List<SessionSpeaker> getSpeakerUserIdBySessionId(@Param("sessionId") Long sessionId);

    @Query("  SELECT ss.session FROM SessionSpeaker ss " +
            " WHERE ss.speaker.userId=:userId " +
            " AND ss.speaker.eventId=:event" +
            " AND :currentDate < ss.session.endTime AND ss.session.format NOT IN (:enumSessionFormats) AND (:status is null OR ss.session.status = :status) " +
            " AND " +
            " (" +
            "   (coalesce(:searchString, 1) = 1 " +
            "       OR " +
            "   (ss.session.title like %:searchString% " +
            "       OR ss.session.sessionLocation.name like %:searchString% " +
            "       OR ss.session.description like %:searchString% " +
            "   )" +
            "   )" +
            " ) " +
            " ORDER BY ss.session.startTime "
    )
    Page<Session> findSessionBySpeakerUserIdFuture(@Param("event") Long eventId,
                                                   @Param("userId") Long userId,
                                                   @Param("searchString") String search,
                                                   @Param("currentDate") Date currentDate,
                                                   Pageable pageable,
                                                   @Param("enumSessionFormats") List<EnumSessionFormat> enumSessionFormats, @Param("status") EnumSessionStatus status);



    @Query("  SELECT ss.session FROM SessionSpeaker ss " +
            " WHERE ss.speaker.userId=:userId " +
            " AND ss.speaker.eventId=:event" +
            " AND :currentDate > ss.session.endTime AND ss.session.format NOT IN (:enumSessionFormats)" +
            " AND (:status is null OR ss.session.status = :status) AND " +
            "   (" +
            "       (coalesce(:searchString, 1) = 1 " +
            "       OR " +
            "       (ss.session.title like %:searchString% " +
            "           OR ss.session.sessionLocation.name like %:searchString% " +
            "           OR ss.session.description like %:searchString% " +
            "       )" +
            "   ) ) "
    )
    Page<Session> findSessionBySpeakerUserIdByPast(@Param("event") Long eventId,
                                                   @Param("userId") Long userId,
                                                   @Param("searchString") String search,
                                                   @Param("currentDate") Date currentDate,
                                                   Pageable pageable,
                                                   @Param("enumSessionFormats") List<EnumSessionFormat> enumSessionFormats, @Param("status") EnumSessionStatus status);

    Optional<SessionSpeaker> findBySessionIdAndSpeakerId(Long sessionId, Long speakerId);


    @Query("SELECT COUNT(1) FROM SessionSpeaker ss " +
            " WHERE ss.sessionId=:sessionId " +
            " AND ss.speaker.userId=:userId")
    BigInteger isUserSpeakerInSession(@Param("userId") Long userId,
                                      @Param("sessionId") Long sessionId);

    @Query("  SELECT ss.session FROM SessionSpeaker ss " +
            " LEFT JOIN ss.session.sessionLocation sl " +
            " WHERE ss.speaker.userId=:userId " +
            " AND ss.speaker.eventId=:event AND ss.session.format NOT IN (:enumSessionFormats) " +
            " AND (:status is null OR ss.session.status = :status) AND " +
            " (" +
            "   (coalesce(:searchString, 1) = 1 " +
            "       OR " +
            "       (ss.session.title like %:searchString% " +
            "           OR sl.name like %:searchString% " +
            "           OR ss.session.description like %:searchString% " +
            "       )" +
            "   ) )" +
            " ORDER BY ss.session.startTime "
    )
    Page<Session> findSessionBySpeakerUserId(@Param("event") Long eventId, @Param("userId") Long userId, @Param("searchString") String search,
                                             Pageable pageable, @Param("enumSessionFormats") List<EnumSessionFormat> enumSessionFormats, @Param("status") EnumSessionStatus status);


    @Query("  SELECT ss.session FROM SessionSpeaker ss " +
            " WHERE ss.speaker.userId=:userId " +
            " AND ss.speaker.eventId=:event AND ss.session.format NOT IN (:formats) ")
    List<Session> findSessionBySpeakerUserId(@Param("event") Long eventId,
                                             @Param("userId") Long userId,
                                             @Param("formats") List<EnumSessionFormat> sessionFormats);



    @Modifying
    @Query("UPDATE SessionSpeaker SET recordStatus=:status WHERE sessionId=:sessionId")
    void updateStatusToDeleteBySessionId(@Param("sessionId") Long sessionId, @Param("status") RecordStatus status);

    SessionSpeaker findFirstBySessionIdOrderByPositionDesc(Long sessionId);

    @Modifying
    @Query("UPDATE SessionSpeaker SET position=(position+:updateCount)"
            + " WHERE speakerId =:speakerId"
            + " AND position> :startPosition AND position <:endPosition")
    void updatePositionSessionSpeaker(@Param("speakerId") long speakerId,
                                      @Param("startPosition") double startPosition,
                                      @Param("endPosition") double endPosition,
                                      @Param("updateCount") double updateCount);

    @Modifying
    @Query(value = "UPDATE session_speaker AS speaker SET speaker.position=(speaker.position+:updateCount)" +
            " WHERE speaker.session_id=:sessionId", nativeQuery = true)
    void updatePositionForAllSessionSpeakerBySessionId(@Param("updateCount") double updateCount,
                                                       @Param("sessionId") long sessionId);

    @Query("SELECT sessionSpeaker "
            + " FROM SessionSpeaker AS sessionSpeaker "
            + "	WHERE sessionSpeaker.id=:sessionSpeakerId AND sessionSpeaker.sessionId =:sessionId"
            + " AND sessionSpeaker.position > :currentPosition "
            + "	ORDER BY sessionSpeaker.position ")
    List<SessionSpeaker> nextPositionSessionSpeaker(@Param("sessionSpeakerId") long sessionSpeakerId,
                                                    @Param("sessionId") Long sessionId,
                                                    @Param("currentPosition") Double currentPosition);

    @Query("SELECT sessionSpeaker "
            + " FROM SessionSpeaker AS sessionSpeaker "
            + "	WHERE sessionSpeaker.id=:sessionSpeakerId AND sessionSpeaker.sessionId =:sessionId"
            + " AND sessionSpeaker.position<:currentPosition ORDER BY sessionSpeaker.position DESC")
    List<SessionSpeaker> previousPositionSessionSpeaker(@Param("sessionSpeakerId") long sessionSpeakerId,
                                                        @Param("sessionId") Long sessionId,
                                                        @Param("currentPosition") Double currentPosition);

    @Query("SELECT ss FROM SessionSpeaker ss WHERE ss.sessionId =:id")
    List<SessionSpeaker> findBySessionIdsBetween(@Param("id") long id);

    @Query("SELECT ss FROM SessionSpeaker ss JOIN ss.session as session WHERE ss.speakerId IN (:speakerIds) AND session.format IN (:sessionFormats)")
    List<SessionSpeaker> findAllSessionSpeakerBySpeakerIdAndSessionFormat(@Param("speakerIds") List<Long> speakerIds, @Param("sessionFormats") List<EnumSessionFormat> sessionFormats);

    @Query("SELECT sessionSpeaker FROM SessionSpeaker AS sessionSpeaker JOIN sessionSpeaker.session AS session WHERE session.id BETWEEN :from AND :to AND (session.recordStatus <> 'DELETE' OR session.recordStatus is NULL) ORDER BY sessionSpeaker.id ASC")
    List<SessionSpeaker> getAllSessionSpeakerBetweenSessionIds(@Param("from") long from, @Param("to") long to);

    @Query("SELECT sessionSpeaker.sessionId FROM SessionSpeaker AS sessionSpeaker WHERE sessionSpeaker.speaker.id=:speakerId")
    List<Long> findAllSessionIdsBySpeakerId(@Param("speakerId") Long speakerId);

    @Query("SELECT ss FROM SessionSpeaker ss JOIN ss.session as session WHERE session.id=:sessionId AND session.eventId=:event ORDER BY ss.id ASC")
    List<SessionSpeaker> findBySessionIdAndEventId(@Param("sessionId") Long sessionId,
                                                   @Param("event") Long eventId);

    @Query("SELECT ss FROM SessionSpeaker ss JOIN ss.session as session WHERE session.eventId=:event AND ss.speakerId=:speakerId")
    List<SessionSpeaker> findBySpeakerIdAndEventId(@Param("speakerId") Long speakerId,
                                                   @Param("event") Long eventId);

    @Query("SELECT ss FROM SessionSpeaker ss JOIN ss.session as session WHERE session.eventId=:event AND ss.sessionId=:sessionId AND ss.speakerId=:speakerId")
    SessionSpeaker findBySessionIdAndSpeakerIdAndEventId(@Param("sessionId") Long sessionId,
                                                         @Param("speakerId") Long speakerId,
                                                         @Param("event") Long eventId);

    @Query("SELECT ss FROM SessionSpeaker ss " +
            " WHERE ss.sessionId=:sessionId " +
            " AND ss.speaker.userId=:userId")
    SessionSpeaker findBySessionIdAndSpeakerUserId(@Param("sessionId") Long sessionId,
                                        @Param("userId") Long userId);

    @Query("SELECT ss.moderator FROM SessionSpeaker ss " +
            " WHERE ss.sessionId=:sessionId " +
            " AND ss.speaker.userId=:userId")
    Boolean isSpeakerModeratorInSession(@Param("sessionId") Long sessionId,
                                        @Param("userId") Long userId);

    @Query("SELECT ss.speakerId FROM SessionSpeaker ss " +
            " WHERE ss.sessionId=:sessionId " +
            " AND ss.speaker.userId=:userId")
    Long getSpeakerIdBySessionIdAndUserId(@Param("sessionId") Long sessionId,
                                        @Param("userId") Long userId);

    @Query("SELECT ss.sessionId FROM SessionSpeaker ss " +
            " WHERE ss.speaker.userId=:userId" +
            " AND ss.session.eventId=:eventId")
    List<Long> findAllSessionIdBySpeakerUserId(@Param("userId") Long userId,
                                           @Param("eventId") Long eventId);


    @Cacheable(value = "getSessionSpeakerByEventId", key = "#p0",condition = "#p0!=null", unless="#result == null")
    @Query("SELECT ss FROM SessionSpeaker ss WHERE ss.session.eventId=:eventId ORDER BY ss.position DESC")
    List<SessionSpeaker> findByEventId(@Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.dto.SessionSpeakerIdDto(ss.sessionId,ss.speakerId) FROM SessionSpeaker ss WHERE ss.session.eventId=:eventId ")
    List<SessionSpeakerIdDto> findSessionSpeakerIdByEventId(@Param("eventId") Long eventId);

    @Query("SELECT COUNT(1)>0 FROM SessionSpeaker ss JOIN ss.session as session" +
            " WHERE ss.sessionId=:sessionId AND session.eventId=:eventId")
    boolean sessionSpeakerAlreadyAddedInThisSession(@Param("sessionId") Long sessionId, @Param("eventId") Long eventId);

    @Caching(evict = {
            @CacheEvict(value = "getSessionSpeakerByEventId", key = "#p0.speaker.eventId"),
            @CacheEvict(value = "speakerUserIdBySessionId", key = "#p0.sessionId")
    })
    SessionSpeaker save(SessionSpeaker sessionSpeaker);

    @Caching(evict = {
            @CacheEvict(value = "getSessionSpeakerByEventId", allEntries = true),
            @CacheEvict(value = "speakerUserIdBySessionId", allEntries = true)
    })
    @Modifying
    @Query("DELETE FROM SessionSpeaker AS sSpeaker WHERE sSpeaker.speakerId IN (:speakerIds) ")
    void deleteSessionSpeakerBySpeakerIds(@Param("speakerIds") List<Long> speakerIds);

    @Cacheable(value = "speakerUserIdBySessionId", key = "#p0",condition = "#p0!=null", unless="#result == null")
    @Query("SELECT s.userId FROM SessionSpeaker ss JOIN ss.speaker as s WHERE ss.sessionId =:sessionId")
    List<Long> findSpeakerUserIdBySessionId(@Param("sessionId") Long sessionId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdCountDto" +
            "(sessionId, COUNT(1))" +
            " FROM SessionSpeaker" +
            " WHERE sessionId IN (:sessionIds)" +
            " GROUP BY sessionId ")
    List<IdCountDto> countSessionSpeakerBySessionIdIn(@Param("sessionIds") List<Long> sessionIds);

    @Query("select (case when count(ss.id)>0 then true else false END ) as isAvailable  from SessionSpeaker As ss where ss.speakerId = :speakerId")
    boolean findIsSpeakerAvaibleBySpeakerId(@Param("speakerId")Long speakerId);

    @Transactional
    @Modifying
    @Query(value = "UPDATE session_speaker AS speaker SET speaker.speaker_id=:currentSpeakerId " +
            " WHERE speaker.speaker_id=:speakerId", nativeQuery = true)
    void updateSessionSpeakerBySpeakerId(@Param("speakerId") Long speakerId,
                                                       @Param("currentSpeakerId") long currentSpeakerId);

    @Query("SELECT COUNT(1) FROM SessionSpeaker ss " +
            " WHERE ss.session.eventId=:eventId " +
            " AND ss.speaker.userId=:userId")
    BigInteger isUserSpeakerInSessionByEventId(@Param("userId") Long userId,
                                      @Param("eventId") Long eventId);

    @Query("SELECT ss FROM SessionSpeaker ss WHERE ss.session.eventId=:eventId ORDER BY ss.position DESC")
    List<SessionSpeaker> findByEventIdWithoutCache(@Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.common.dto.EventTaskEmailDto(ss.speaker.firstName,ss.speaker.email,ss.session.title,ss.speaker.userId) FROM SessionSpeaker ss WHERE ss.session.eventId=:eventId AND sessionId IN (:sessionIds)")
    List<EventTaskEmailDto> findSpeakersEmailNameAndSessionNameByEventIdAndSessionIds(@Param("eventId") Long eventId, @Param("sessionIds") List<Long> sessionIds);


    @Modifying
    @Query("DELETE FROM SessionSpeaker AS sSpeaker WHERE sSpeaker.id=:id")
    void deleteSessionSpeakerById(@Param("id") long id);

    @Query("  SELECT ss.sessionId FROM SessionSpeaker ss " +
            " WHERE ss.speaker.userId=:userId " +
            " AND ss.speaker.eventId=:eventId ")
    List<Long> findSessionByEventIdAndSpeakerUserId(@Param("eventId") Long eventId,
                                             @Param("userId") Long userId);

    @Transactional
    @Modifying
    @Query("DELETE FROM SessionSpeaker ss WHERE ss.speakerId = :speakerId AND ss.sessionId IN :sessionIds")
    void deleteBySpeakerIdAndSessionIds(@Param("speakerId") Long speakerId,
                                        @Param("sessionIds") List<Long> sessionIds);
}
