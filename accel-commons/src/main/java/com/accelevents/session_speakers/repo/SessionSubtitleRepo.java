package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.SessionSubtitle;
import com.accelevents.session_speakers.dto.SessionSubtitleDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SessionSubtitleRepo extends CrudRepository<SessionSubtitle,Long> {

    List<SessionSubtitle> findBySessionIdAndMuxId(Long sessionId, Long muxId);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionSubtitleDTO(ss.id, ss.muxSubtitleTrackId, ss.languageCode, ss.subTitleFileUrl)" +
            " FROM SessionSubtitle ss " +
            " WHERE ss.sessionId = :sessionId " +
            " AND ss.muxId = :muxId ")
    List<SessionSubtitleDTO> getSubtitleListByMuxIdAndSessionId(@Param("sessionId") Long sessionId, @Param("muxId") Long muxId);

    @Query("SELECT new com.accelevents.session_speakers.dto.SessionSubtitleDTO(ss.id, ss.muxSubtitleTrackId, ss.languageCode, ss.subTitleFileUrl, ss.muxId)" +
            " FROM SessionSubtitle ss " +
            " WHERE ss.sessionId IN (:sessionIds) " +
            " AND ss.muxId IN (:muxIds) ")
    List<SessionSubtitleDTO> getSubtitleListByMuxIdListAndSessionId(@Param("muxIds") List<Long> muxIds, @Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT COUNT(ss) > 0 FROM SessionSubtitle ss " +
            " WHERE ss.sessionId = :sessionId " +
            " AND ss.muxId = :muxId " +
            " AND ss.languageCode = :languageCode " +
            " AND ss.id <> :id")
    boolean languageCodeExistByMuxIdAndSessionId(@Param("muxId")Long muxId, @Param("sessionId") Long sessionId, @Param("languageCode") String languageCode, @Param("id") Long subtitleId);
}
