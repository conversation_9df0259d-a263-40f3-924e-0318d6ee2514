package com.accelevents.session_speakers.repo;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.JoinMuxAssetWithArchive;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface JoinMuxAssetWithArchiveRepository extends CrudRepository<JoinMuxAssetWithArchive, Long> {

    Optional<JoinMuxAssetWithArchive> findByAssetIdAndRecordStatus(Long assetId, RecordStatus recordStatus);

    Optional<JoinMuxAssetWithArchive> findByAssetId(Long assetId);

    List<JoinMuxAssetWithArchive> findAllByEventIdAndRecordStatus(Long eventId, RecordStatus recordStatus);

    Optional<JoinMuxAssetWithArchive> findByJobId(String jobId);
}
