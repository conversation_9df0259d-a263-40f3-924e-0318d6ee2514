package com.accelevents.session_speakers.repo;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.enums.AttributeType;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.RegistrationAttributeType;
import com.accelevents.domain.session_speakers.CustomFormAttribute;
import com.accelevents.dto.KeyValueDto;
import com.accelevents.session_speakers.dto.CustomFormAttributeMappingDto;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomFormAttributeRepository extends CrudRepository<CustomFormAttribute, Long> {

    @Query("SELECT attributes FROM CustomFormAttribute attributes JOIN FETCH attributes.event as event WHERE attributes.eventId=:eventId AND attributes.recurringEventId=:recurringEventId AND attributes.attributeType=:attributeType AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<CustomFormAttribute> findAllAttributesByRecurringEventIdAndAttributeType(@Param("eventId") Long eventId, @Param("recurringEventId")  Long recurringEventId, @Param("attributeType") AttributeType attributeType);

    @Query("SELECT attribute FROM CustomFormAttribute as attribute JOIN FETCH attribute.event as event WHERE attribute.eventId=:eventId AND attribute.recurringEventId IS NULL AND attribute.attributeType=:attributeType  AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<CustomFormAttribute> findByEventIdAndRecurringEventIdNullAndAttributeType(@Param("eventId")Long eventId, @Param("attributeType") AttributeType attributeType);

    List<CustomFormAttribute> findByNameAndEventAndRecurringEventIdAndAttributeType(String name, Event event, Long recurringEventId, AttributeType attributeType);

    List<CustomFormAttribute> findByNameAndEventAndRecurringEventIdNullAndAttributeType(@Param("name") String name, @Param("event") Event event, @Param("attributeType") AttributeType attributeType);

    @Query("select MAX(attribute.order) from CustomFormAttribute as attribute where attribute.eventId=:eventId AND attribute.attributeType=:attributeType order by attribute.order")
    Integer findFirstByEventAndAttributeTypeOrderByOrderDesc(@Param("eventId") Long eventId, @Param("attributeType") AttributeType attributeType);

    CustomFormAttribute findByNameAndEventIdAndRecurringEventIdAndAttributeType(String name, Long eventId, Long recurringEventId, AttributeType attributeType);

    @Query("SELECT attribute FROM CustomFormAttribute as attribute WHERE attribute.eventId=:eventId AND attribute.attributeType=:attributeType AND attribute.parentQuestionId IN (:parentQueIds) ORDER BY attribute.order")
    List<CustomFormAttribute> findAllSubQueByParentIdsAndEventIdAndAttributeType(@Param("eventId") Long eventId, @Param("parentQueIds") List<Long> parentQueIds, @Param("attributeType") AttributeType attributeType);

    @Query("Select attributes FROM CustomFormAttribute attributes WHERE attributes.eventId=:eventId AND attributes.createdFrom=:createdFrom AND attributes.attributeType=:attributeType")
    List<CustomFormAttribute> findAllAttributesByCreatedFromAndAttributeTypeAndEventId(@Param("createdFrom") Long createdFrom, @Param("eventId") Long eventId, @Param("attributeType") AttributeType attributeType);

    @Query("SELECT attributes FROM CustomFormAttribute attributes WHERE attributes.eventId=:eventId" +
            " AND attributes.name=:attributeName AND attributes.attributeType=:attributeType  AND attributes.recurringEventId is not null")
    List<CustomFormAttribute> findByEventAndRecurringEventIdNotNullAndAttributeType(@Param("eventId") Long eventId, @Param("attributeName") String attributeName, @Param("attributeType") AttributeType attributeType);

    @Query("SELECT attributes FROM CustomFormAttribute attributes WHERE attributes.eventId=:eventId and attributes.createdFrom=:attributeId")
    List<CustomFormAttribute> findByCreatedFromIdAndEventId(@Param("attributeId") Long attributeId, @Param("eventId") Long eventId);

    @Query("SELECT attribute FROM CustomFormAttribute as attribute WHERE attribute.eventId=:eventId AND attribute.parentQuestionId=:parentQueId")
    List<TicketHolderRequiredAttributes> findAllSubQueByParentIdAndEventId(@Param("eventId") Long eventId, @Param("parentQueId") long parentQueId);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.CustomFormAttributeMappingDto(sa.id, sa.name, sa.attributeValueType, ra.customFormAttributeId, sa.defaultValue, sa.isDefaultAttribute) " +
            "FROM CustomFormAttribute sa " +
            "LEFT JOIN RegistrationAttribute ra on sa.id=ra.customFormAttributeId AND ra.type = :registrationAttributeType " +
            "JOIN sa.event as event " +
            "WHERE sa.eventId = :eventId  AND (sa.recurringEventId is NULL OR sa.recurringEventId = 0) " +
            "AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') " +
            "AND sa.enabled is TRUE " +
            "AND sa.attributeType=:attributeType " +
            "AND sa.attributeValueType IN :attributeValueTypeList ")
    List<CustomFormAttributeMappingDto> findByEventIdAndRecurringEventIdNullAndEnabledTrueAndAttributeValueTypeIn(@Param("eventId") Long eventId,
                                                                                                                  @Param("attributeValueTypeList")List<AttributeValueType> attributeValueTypeList,
                                                                                                                  @Param("attributeType") AttributeType attributeType,@Param("registrationAttributeType") RegistrationAttributeType registrationAttributeType);

    @Query("SELECT NEW com.accelevents.session_speakers.dto.CustomFormAttributeMappingDto(sa.id, sa.name, sa.attributeValueType, ra.customFormAttributeId, sa.defaultValue, sa.isDefaultAttribute) " +
            "FROM CustomFormAttribute sa " +
            "LEFT JOIN RegistrationAttribute ra on sa.id=ra.customFormAttributeId AND ra.type=:registrationAttributeType " +
            "JOIN sa.event as event " +
            "WHERE sa.eventId = :eventId  AND sa.recurringEventId=:recurringEventId " +
            "AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') " +
            "AND sa.enabled is TRUE " +
            "AND sa.attributeType=:attributeType " +
            "AND sa.attributeValueType IN :attributeValueTypeList ")
    List<CustomFormAttributeMappingDto> findByEventIdAndRecurringEventIdAndEnabledTrueAndAttributeValueTypeIn(@Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId,
                                                                                                              @Param("attributeValueTypeList")List<AttributeValueType> attributeValueTypeList,
                                                                                                              @Param("attributeType") AttributeType attributeType,
                                                                                                               @Param("registrationAttributeType") RegistrationAttributeType registrationAttributeType);


    @Query("SELECT NEW com.accelevents.dto.KeyValueDto(ra.name, attribute.name) FROM CustomFormAttribute as attribute " +
            " JOIN RegistrationAttribute ra on attribute.id = ra.customFormAttributeId " +
            " AND attribute.attributeValueType IN :attributeValueTypeList" +
            " AND ra.type = :registrationAttributeType " +
            " AND attribute.eventId = :eventId "
    )
    List<KeyValueDto> findByEventIdAndAttributeValueTypeIn(@Param("eventId") Long eventId,
                                                           @Param("attributeValueTypeList") List<AttributeValueType> attributeValueTypeList,
                                                           @Param("registrationAttributeType") RegistrationAttributeType registrationAttributeType);

    @Query("SELECT attributes FROM CustomFormAttribute attributes WHERE attributes.eventId=:eventId AND attributes.recurringEventId=:recurringEventId AND attributes.isDefaultAttribute = false and attributes.enabled = true and attributes.attributeType =:attributeType ORDER BY attributes.order")
    List<CustomFormAttribute> findAllCustomEnabledAttributesByRecurringEventIdOrderByOrder(@Param("eventId") Long eventId, @Param("recurringEventId")  Long recurringEventId, @Param("attributeType") AttributeType attributeType);

    @Query("SELECT attributes FROM CustomFormAttribute attributes WHERE attributes.eventId=:eventId AND attributes.recurringEventId is null and attributes.isDefaultAttribute = false and attributes.enabled = true and attributes.attributeType =:attributeType ORDER BY attributes.order")
    List<CustomFormAttribute> findAllCustomEnabledAttributesByEventIdAndRecurringEventIdNullOrderByOrder(@Param("eventId") Long eventId, @Param("attributeType") AttributeType attributeType);

    @Query("SELECT attributes FROM CustomFormAttribute attributes " +
            "WHERE attributes.eventId=:eventId " +
            "AND attributes.recurringEventId is null " +
            "AND attributes.enabled = true " +
            "AND attributes.attributeType =:attributeType " +
            "AND attributes.attributeValueType NOT IN :attributeValueType " +
            "ORDER BY attributes.order")
    List<CustomFormAttribute> findAllByEventIdAndRecurringEventIdIsNullAndEnabledTrueAndAttributeTypeAndAttributeValueTypesNotInOrderByOrder(
            @Param("eventId") Long eventId,
            @Param("attributeType") AttributeType attributeType,
            @Param("attributeValueType") List<AttributeValueType> attributeValueType);

    @Query("SELECT attributes FROM CustomFormAttribute attributes " +
            "WHERE attributes.eventId=:eventId " +
            "AND attributes.recurringEventId =:recurringEventId " +
            "AND attributes.enabled = true " +
            "AND attributes.attributeType =:attributeType " +
            "AND attributes.attributeValueType NOT IN :attributeValueType " +
            "ORDER BY attributes.order")
    List<CustomFormAttribute> findAllByEventIdAndRecurringEventIdAndEnabledTrueAndAttributeTypeAndAttributeValueTypeNotInOrderByOrder(
            @Param("eventId") Long eventId,
            @Param("recurringEventId") Long recurringEventId,
            @Param("attributeType") AttributeType attributeType,
            @Param("attributeValueType") List<AttributeValueType> attributeValueType);


    List<CustomFormAttribute> findByIdIn(List<Long> ids);

}
