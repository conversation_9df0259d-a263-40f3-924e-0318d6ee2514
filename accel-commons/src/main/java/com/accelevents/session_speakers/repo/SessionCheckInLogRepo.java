package com.accelevents.session_speakers.repo;

import com.accelevents.domain.enums.EnumSessionCheckInLogStatus;
import com.accelevents.domain.session_speakers.SessionCheckInLog;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SessionCheckInLogRepo extends CrudRepository<SessionCheckInLog, Long> {

    SessionCheckInLog findFirstByUserIdAndSessionIdOrderByIdDesc(Long userId, Long sessionId);

    List<SessionCheckInLog> findAllSessionCheckInLogBySessionIdOrderByAuditTimeAsc(Long sessionId);

    SessionCheckInLog findFirstByUserIdAndSessionIdOrderByIdAsc(Long userId,Long sessionId);

    @Query("select s from SessionCheckInLog s where s.sessionId = :sessionId and s.auditStatus = :auditStatus")
    List<SessionCheckInLog> findAllSessionLogsBySessionIdAndStatus(@Param("sessionId") Long sessionId,@Param("auditStatus")EnumSessionCheckInLogStatus auditStatus);

    SessionCheckInLog findFirstByUserIdAndSessionIdAndEventTicketIdOrderByIdDesc(Long userId, Long sessionId, Long eventTicketId);
}
