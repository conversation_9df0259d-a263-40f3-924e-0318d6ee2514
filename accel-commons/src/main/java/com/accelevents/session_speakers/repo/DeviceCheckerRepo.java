package com.accelevents.session_speakers.repo;

import com.accelevents.domain.enums.DeviceCheckerType;
import com.accelevents.domain.session_speakers.DeviceChecker;
import com.accelevents.session_speakers.dto.UserDeviceCheckedDto;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DeviceCheckerRepo  extends CrudRepository<DeviceChecker, Long> {

    @Query("SELECT new com.accelevents.session_speakers.dto.UserDeviceCheckedDto(userId, COUNT(dc.id) > 0) FROM DeviceChecker AS dc WHERE dc.userId In :userIds  group by dc.userId")
    List<UserDeviceCheckedDto> userIdAndRunDeviceChecker(@Param("userIds") List<Long> userIds);

    Optional<DeviceChecker> findFirstByUserIdAndEventIdAndMachineIdAndTypeOrderByIdDesc(Long userId, long eventId, String machineId, DeviceCheckerType type);
}
