package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.NetworkingLoungeDocumentLinkDetails;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;

public interface NetworkingLoungeDocumentLinkDetailsRepo extends CrudRepository<NetworkingLoungeDocumentLinkDetails, Long> {
    List<NetworkingLoungeDocumentLinkDetails> findAllByNetworkingLoungeId(String networkingLoungeId);

    Optional<NetworkingLoungeDocumentLinkDetails> findByNetworkingLoungeIdAndId(String networkingLoungeId, long id);
}
