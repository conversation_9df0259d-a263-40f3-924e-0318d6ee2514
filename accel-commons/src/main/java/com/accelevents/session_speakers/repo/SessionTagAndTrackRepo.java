package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionTagAndTrack;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
public interface SessionTagAndTrackRepo extends CrudRepository<SessionTagAndTrack,Long> {

	@Modifying
	@Transactional
	@Query("DELETE FROM SessionTagAndTrack WHERE sessionId=:sessionId AND tagOrTrackId IN (:tagIdOrTrackIds) ")
	void deleteBySessionIdAndTagOrTrackIdIn(@Param("sessionId") Long sessionId,@Param("tagIdOrTrackIds") Set<Long> tagIdOrTrackIds);

	@Query("SELECT tagOrTrackId FROM SessionTagAndTrack WHERE tagOrTrackId IN (:tagOrTrackIds)")
	List<Long> findByIds(@Param("tagOrTrackIds") List<Long> tagOrTrackIds);

    @Modifying
    @Transactional
    @Query("DELETE FROM SessionTagAndTrack WHERE tagOrTrackId IN (:tagIdOrTrackIds)")
    void deleteByTagOrTrackIds(@Param("tagIdOrTrackIds") List<Long> tagIdOrTrackIds);

    @Modifying
    @Query("DELETE FROM SessionTagAndTrack WHERE sessionId=:sessionId")
    void deleteBySessionId(@Param("sessionId") Long sessionId);

    List<SessionTagAndTrack> findBySessionId(Long sessionId);

    @Query("SELECT sessionId FROM SessionTagAndTrack WHERE tagOrTrackId IN (:tagOrTrackIds)")
    List<Long> findSessionIdsByIds(@Param("tagOrTrackIds") List<Long> tagOrTrackIds);

    @Query("SELECT session FROM SessionTagAndTrack st JOIN st.session as session WHERE st.tagOrTrackId IN (:tagOrTrackIds)")
    List<Session> findSessionsByTagOrTrackIds(@Param("tagOrTrackIds") List<Long> tagOrTrackIds);

    @Query("SELECT CASE WHEN COUNT(st.id) > 0 THEN true ELSE false END  FROM SessionTagAndTrack st WHERE st.tagOrTrackId = :tagOrTrackId AND st.sessionId = :sessionId")
    Boolean existsBySessionIdAndTagOrTrackId(@Param("sessionId") Long sessionId, @Param("tagOrTrackId") Long tagOrTrackId);

    @Query("SELECT st.id  FROM SessionTagAndTrack st WHERE st.tagOrTrackId in (:tagOrTrackId) AND st.sessionId = :sessionId")
    List<Long> existsBySessionIdAndTagOrTrackIds(@Param("sessionId") Long sessionId, @Param("tagOrTrackId") List<Long> tagOrTrackId);

    @Query("SELECT sessionTagAndTrack FROM SessionTagAndTrack sessionTagAndTrack WHERE sessionTagAndTrack.sessionId IN (:tagOrTrackIds)")
    List<SessionTagAndTrack> findByTagOrTrackIds(@Param("tagOrTrackIds") List<Long> tagOrTrackIds);

    @Query("SELECT stt.tagOrTrackId FROM SessionTagAndTrack stt JOIN stt.tagOrTrack as kv  WHERE stt.sessionId= :sessionId AND kv.parentId IS NULL")
    List<Long> findTagOrTrackIdsBySessionId(@Param("sessionId") long sessionId);

    @Query("SELECT stt.tagOrTrackId FROM SessionTagAndTrack stt " +
            "WHERE stt.tagOrTrackId IN (:tagOrTrackIds) " +
            "GROUP BY stt.tagOrTrackId " +
            "ORDER BY COUNT(stt.sessionId)")
    List<Long> findLeastSessionCountTrackIds(@Param("tagOrTrackIds") List<Long> tagOrTrackIds);

}
