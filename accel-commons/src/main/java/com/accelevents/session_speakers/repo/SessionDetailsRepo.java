package com.accelevents.session_speakers.repo;

import com.accelevents.domain.ChargebeeEventUsages;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.session_speakers.dto.IdDurationDto;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SessionDetailsRepo extends CrudRepository<SessionDetails,Long> {

    Optional<SessionDetails> findBySessionId(Long sessionId);

    @Query("SELECT sessionDetails.vonageSessionId FROM SessionDetails sessionDetails WHERE sessionDetails.session.id =:sessionId")
    String findVonageSessionIdBySessionId(@Param("sessionId") Long sessionId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdDurationDto( " +
            " session.session.id, session.videoDuration )" +
            " FROM SessionDetails session " +
            " WHERE session.session.id IN (:sessionIds)")
    List<IdDurationDto> getSessionDurationBySessionIds(@Param("sessionIds") List<Long> sessionIds);


    @Query("SELECT session.videoDuration FROM SessionDetails session WHERE session.session.id =:sessionId")
    Double getSessionVideoDurationBySessionId(@Param("sessionId") Long sessionId);



    @Caching(evict = {
            @CacheEvict(value = "allSessionDetailsByEvent", key = "#p0.session.eventId"),
            @CacheEvict(value = "allowedMinutesToJoinLateSession", key = "#p0.session.id")
    })
    SessionDetails save(SessionDetails sessionDetails);

    @Cacheable(value = "allSessionDetailsByEvent", key = "#p0", unless="#result == null")
    @Query("SELECT sessionDetails FROM SessionDetails sessionDetails WHERE sessionDetails.session.eventId=:eventId")
    List<SessionDetails> findAllSessionDetailsByEventId(@Param("eventId") Long eventId);

    @Query("SELECT sessionDetails FROM SessionDetails sessionDetails WHERE sessionDetails.session.eventId=:eventId")
    List<SessionDetails> getAllSessionDetailsByEventIdWithoutCache(@Param("eventId") Long eventId);

    @Modifying
    @Query("UPDATE SessionDetails sd SET sd.recordStatus='DELETE' WHERE sd.session.id=:sessionId")
    void deleteBySessionId(@Param("sessionId") Long sessionId);

    @Query("SELECT sessionDetails.subTitleFileUrl FROM SessionDetails sessionDetails WHERE sessionDetails.session.id =:sessionId")
    String findSubTitleFileUrlFromSessionDetailsBySession(@Param("sessionId") Long sessionId);

    @Query("SELECT sessionDetails.recordSession FROM SessionDetails sessionDetails WHERE sessionDetails.session.id =:sessionId")
    Boolean findSessionRecordBySession(@Param("sessionId") Long sessionId);

    @Query("SELECT sessionDetails.hideVideoControls FROM SessionDetails sessionDetails WHERE sessionDetails.session.id =:sessionId")
    Boolean findHideVideoControlsBySession(@Param("sessionId") Long sessionId);

    @Query("SELECT new com.accelevents.session_speakers.dto.IdDurationDto(session.id, sd.videoDuration) " +
            " FROM SessionDetails sd JOIN sd.session as session " +
            " WHERE session.eventId =:eventId")
    List<IdDurationDto> getSessionVideoDurationByEventId(@Param("eventId") Long eventId);

    @Query("SELECT sessionDetails FROM SessionDetails sessionDetails WHERE sessionDetails.session IN (:sessions)")
    List<SessionDetails> findSessionDetailsBySessionIds(@Param("sessions") List<Session> sessions);

    @Cacheable(value = "allowedMinutesToJoinLateSession", key = "#p0", unless="#result == null")
    @Query("SELECT session.allowedMinutesToJoinLate FROM SessionDetails session WHERE session.session.id =:sessionId")
    Integer getAllowedMinutesToJoinLate(@Param("sessionId") Long sessionId);

    @Query("SELECT sessionDetails FROM SessionDetails as sessionDetails WHERE  sessionDetails.captions is not null AND sessionDetails.id BETWEEN :from AND :to")
    List<SessionDetails> getSessionDetailsForDisableLiveCaptionsDataFix(@Param("from") Long from, @Param("to") Long to);

    @Query(value = "SELECT details FROM SessionDetails details WHERE details.postSessionCallToActionJson LIKE %:sessionId%")
    List<SessionDetails> getSessionDetailsByPostSessionCTASessionId(@Param("sessionId") Long sessionId);



}
