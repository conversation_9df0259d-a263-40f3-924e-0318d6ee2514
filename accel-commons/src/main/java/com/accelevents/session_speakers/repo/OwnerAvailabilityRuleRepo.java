package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.OwnerAvailabilityRule;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OwnerAvailabilityRuleRepo extends CrudRepository<OwnerAvailabilityRule,Long> {

    OwnerAvailabilityRule findByEventIdAndUserId(Long eventId, Long userId);

    @Query("SELECT ownerAvailabilityRule FROM OwnerAvailabilityRule AS ownerAvailabilityRule WHERE ownerAvailabilityRule.meetingRules IS NOT NULL AND (ownerAvailabilityRule.event.eventStatus <> 'EVENT_DELETED' OR ownerAvailabilityRule.event.eventStatus IS NULL)")
    List<OwnerAvailabilityRule> findByMeetingRulesNotNull();

    @Query("SELECT ownerAvailabilityRule FROM OwnerAvailabilityRule AS ownerAvailabilityRule WHERE ownerAvailabilityRule.eventId =:eventId AND ownerAvailabilityRule.userId IN (:attendeeIds)")
    List<OwnerAvailabilityRule> findByOwnerAvailabilityRuleList(@Param("eventId") Long eventId, @Param("attendeeIds") List<Long> attendeeIds);
}
