package com.accelevents.session_speakers.repo;

import com.accelevents.domain.session_speakers.UserSessionCalendar;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface UserSessionCalendarRepo extends CrudRepository<UserSessionCalendar,Long> {

    @Query("select us.sessionId FROM UserSessionCalendar us" +
            " WHERE us.sessionId IN (:sessionIds) AND us.userId=:userId AND us.eventId=:eventId")
    List<Long> getCheckInSessionByUserIdAndSessionId(@Param("sessionIds") List<Long> sessionIds, @Param("userId") Long userId, @Param("eventId")Long eventId);

    @Query("SELECT us.sessionId FROM UserSessionCalendar us WHERE us.userId=:userId AND us.eventId=:eventId")
    List<Long> getAddToCalendarSessionByUserIdAndSessionId(@Param("userId") Long userId, @Param("eventId")Long eventId);
}