package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.graphql.GraphQL;
import com.accelevents.utils.Constants;
import com.accelevents.utils.MuxEventType;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.core.task.SimpleAsyncTaskExecutor;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class BroadcastGraphQLHandler extends GraphQL {

	public BroadcastGraphQLHandler(GraphQLConfiguration graphQLConfiguration, String authToken){
		super(graphQLConfiguration.getAppsyncBroadcastApiUrl(), authToken);
	}

	public void handleCreateOrUpdateSessionStatusLogs(String inputStatus, Session session){
        new SimpleAsyncTaskExecutor().execute(() -> {
            handleCreateOrUpdateSessionStatusLogs(inputStatus, session, null);
        });
	}

	public boolean handleCreateOrUpdateSessionStatusLogs(String inputStatus, Session session, User user) {
		try {
			String actionUser = null;
			if(user!=null){
				actionUser = StringUtils.isNotBlank(user.getFirstName()) ?
						(user.getFirstName() +" "+ user.getLastName()) : user.getEmail();
			}

			logger.info("handleCreateOrUpdateSessionStatusLogs sessionId {} status {} ", session.getId(), inputStatus);
			Map<String, String> map = getSessionStatusLogs(session);
			if(StringUtils.isNotBlank(map.get(Constants.SESSION_ID))){
//				Long lastUpdatedAt = Long.valueOf(map.get("updatedAt"));//NOSONAR
//				String returnStatus = map.get("status");//NOSONAR
//				if( "STARTING".equals(inputStatus)//NOSONAR
//						&& StringUtils.isNotBlank(returnStatus)//NOSONAR
//						&& isLessThan20SecDiff(lastUpdatedAt)//NOSONAR
//						&& ("STARTING".equals(returnStatus) ||//NOSONAR
//							"video.live_stream.connected".equals(returnStatus) ||//NOSONAR
//						    "video.live_stream.recording".equals(returnStatus) ||//NOSONAR
//						    "video.live_stream.active".equals(returnStatus))){//NOSONAR
//					return false;//NOSONAR
//				} else {//NOSONAR
					updateSessionStatusLogs(inputStatus, session, actionUser);
                //}		//NOSONAR
			} else {
				createSessionStatusLogs("CREATED", session, actionUser);
				updateSessionStatusLogs(inputStatus, session, actionUser);
			}
		} catch (JSONException e) {
			logger.info("handleCreateOrUpdateSessionStatusLogsJSONException");
			e.printStackTrace();
		}
		return true;
	}


	public Map<String, String> getSessionStatusLogs(Session session) throws JSONException {
		String operationName = "getSessionStatusLog";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put("operationName",operationName);
		mainRequest.put(Constants.QUERY,"query getSessionStatusLog { getSessionStatusLog(sessionId: "+session.getId()+", eventId: "+session.getEventId()+") { sessionId updatedAt status } }");
		return executeSelect(mainRequest, operationName);
	}

	public boolean executeQuery(JSONObject mainRequest, String operationName) {
        Map<String, String> map = executeSelect(mainRequest, operationName);
		return StringUtils.isNotBlank(map.get(Constants.SESSION_ID));
	}

	private Map<String, String> executeSelect(JSONObject mainRequest, String operationName) {
		try {
			JSONObject finalResult = execute(mainRequest, operationName);
			Map<String, String> map = new HashMap<>();

			if (finalResult.has(Constants.SESSION_ID)) {
                map.put(Constants.SESSION_ID,String.valueOf(finalResult.get(Constants.SESSION_ID)));
            }
			if (finalResult.has("updatedAt")) {
                map.put("updatedAt",String.valueOf(finalResult.get("updatedAt")));
            }
            if (finalResult.has(Constants.STATUS)) {
                map.put(Constants.STATUS,finalResult.getString(Constants.STATUS));
            }

			return map;

		} catch (UnirestException e) {
			logger.info("GraphqlHandlerExecuteUnirest {} ", e.getMessage());
			e.printStackTrace();
		} catch (JSONException e) {
			logger.info("GraphqlHandlerExecuteJSONException {} ", e.getMessage());
			e.printStackTrace();
		}
		return Collections.emptyMap();
	}

	public boolean deleteSessionStatusLogs(Long sessionId, Long eventId) throws JSONException {
		JSONObject variable = new JSONObject();
		variable.put("deleteSessionStatusLogInput", deleteSessionInput(sessionId, eventId));

		String operationName = "deleteSessionStatusLog";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put(Constants.OPERATION_NAME,operationName);
		mainRequest.put(Constants.VARIABLES,variable);
		mainRequest.put(Constants.QUERY,"mutation deleteSessionStatusLog($deleteSessionStatusLogInput: DeleteSessionStatusLogInput!) { deleteSessionStatusLog(input: $deleteSessionStatusLogInput) { sessionId } }");

		return executeQuery(mainRequest, operationName);
	}

	public boolean createSessionStatusLogs(String status, Session session, String actionUser) throws JSONException {
		JSONObject variable = new JSONObject();
		variable.put("createSessionStatusLogInput", createSessionInput(status, session, actionUser));

		String operationName = "createSessionStatusLog";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put(Constants.OPERATION_NAME,operationName);
		mainRequest.put(Constants.VARIABLES,variable);
		mainRequest.put(Constants.QUERY,"mutation createSessionStatusLog($createSessionStatusLogInput: CreateSessionStatusLogInput!) { createSessionStatusLog(input: $createSessionStatusLogInput) { sessionId playbackUrl status actionUser } }");

		return executeQuery(mainRequest, operationName);
	}

	public boolean updateSessionStatusLogs(String status, Session session, String actionUser) throws JSONException {
		JSONObject variable = new JSONObject();
		variable.put("updateSessionStatusLogInput", updateSessionInput(session,status,session.getEventId(),actionUser));

		String operationName = "updateSessionStatusLog";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put(Constants.OPERATION_NAME,operationName);
		mainRequest.put(Constants.VARIABLES,variable);
        if(MuxEventType.isAssetWorkFlowEvents(status)){
            mainRequest.put(Constants.QUERY,"mutation updateSessionStatusLog($updateSessionStatusLogInput: UpdateSessionStatusLogInput!) { updateSessionStatusLog(input: $updateSessionStatusLogInput) { sessionId playbackUrl assetStatus actionUser secondaryFullStreamUrl } }");
        }else{
            mainRequest.put(Constants.QUERY,"mutation updateSessionStatusLog($updateSessionStatusLogInput: UpdateSessionStatusLogInput!) { updateSessionStatusLog(input: $updateSessionStatusLogInput) { sessionId playbackUrl status actionUser secondaryFullStreamUrl } }");
        }

		return executeQuery(mainRequest, operationName);
	}


	public JSONObject deleteSessionInput(Long sessionId, Long eventId) throws JSONException {
		JSONObject requestData = new JSONObject();
		requestData.put(Constants.SESSION_ID, sessionId);
		requestData.put("eventId", eventId);
		return requestData;
	}

    public JSONObject createSessionInput(String status, Session session, String actionUser) throws JSONException {
        JSONObject requestData = updateSessionInput(session, status, session.getEventId(), actionUser);
        return requestData;
    }

	public JSONObject updateSessionInput( Session session, String status, Long eventId, String actionUser) throws JSONException {
		JSONObject requestData = new JSONObject();
		requestData.put(Constants.SESSION_ID,session.getId());
		requestData.put("eventId",eventId);
        requestData.put("format", session.getFormat().name());
        if(status.equalsIgnoreCase("STARTING")){
             requestData.put("secondaryFullStreamUrl", "https://stream.cast.accelevents.com/"+session.getStreamUrl()+".m3u8?redundant_streams=true&token="+session.getPlayBackRestrictionToken());
        }
        requestData.put("playbackUrl", EnumSessionFormat.WORKSHOP.equals(session.getFormat()) ?
                new StringBuilder(Constants.WORKSHOP).append(Constants.STRING_UNDERSCORE).append(session.getId()).toString() : session.getStreamUrl());
		if(MuxEventType.isAssetWorkFlowEvents(status)){
            requestData.put(Constants.ASSET_STATUS,status);
        }else{
            requestData.put(Constants.STATUS,status);
        }
		if(StringUtils.isNotBlank(actionUser)){
			requestData.put("actionUser", actionUser);
		}
		return requestData;
	}


	public void getAllPollsQuestionsResultBySession(long sessionId) {
	    //NOSONAR
	}


}
