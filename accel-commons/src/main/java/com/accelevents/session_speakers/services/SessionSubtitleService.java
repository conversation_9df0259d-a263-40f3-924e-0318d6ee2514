package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.SessionSubtitle;
import com.accelevents.session_speakers.dto.SessionSubtitleDTO;

import java.util.List;
import java.util.Map;

public interface SessionSubtitleService {

    void save(SessionSubtitle sessionSubtitle);

    void saveAll(List<SessionSubtitle> sessionSubtitleList);

    void deleteById(Long subtitleId);

    List<SessionSubtitleDTO> getSubtitleListByMuxIdAndSessionId(Long sessionId, Long muxId);

    Map<Long,List<SessionSubtitleDTO>> getMapOfMuxIdAndSubtitleListByMuxIdListAndSessionId(List<Long> muxIds, List<Long> sessionIds);

    List<SessionSubtitle> findBySessionIdAndMuxId(Long sessionId, Long muxId);

    boolean languageCodeExistByMuxIdAndSessionId(Long muxId, Long sessionId, String languageCode, Long subtitleId);
}
