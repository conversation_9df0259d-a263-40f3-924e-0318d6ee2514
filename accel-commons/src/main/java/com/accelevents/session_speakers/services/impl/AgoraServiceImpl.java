package com.accelevents.session_speakers.services.impl;

import com.accelevents.session_speakers.agora.media.RtcTokenBuilder;
import com.accelevents.session_speakers.services.AgoraService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AgoraServiceImpl implements AgoraService {

	private static final Logger log = LoggerFactory.getLogger(AgoraServiceImpl.class);

	@Value("${agora.appId}")
	String appId;
	@Value("${agora.appCertificate}")
	String appCertificate;

	private static final int expirationTimeInSeconds = 86400;

	@Override
	public String generateToken(Long uid, Long sessionId) {
		log.info("Start Agora generateToken for uid {} , sessionId {}", uid, sessionId);
		RtcTokenBuilder tokenBuilder = new RtcTokenBuilder();
		int timestamp = (int)(System.currentTimeMillis() / 1000 + expirationTimeInSeconds);
		String channelName = "Session_" + sessionId;
		String result  = tokenBuilder.buildTokenWithUid(appId, appCertificate, channelName, uid.intValue(), RtcTokenBuilder.Role.Role_Publisher, timestamp);
		log.info("Successfully Agora token generated");
		return result;
	}


}
