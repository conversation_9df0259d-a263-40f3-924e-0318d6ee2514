package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.UserSessionDTO;

import java.util.List;

public interface UserInterestedSessionService {
	void markInterested(UserSessionDTO userSessionDTO, Long userId);

	void unMarkInterested(UserSessionDTO userSessionDTO, Long userId);

	List<Long> getMyInterestedSessions(User user, Event event);

	List<IdCountDto> getSessionStatsForIdsIn(List<Long> sessionIds);

    Integer countInterestedBySessionId(Long sessionId);

    void deleteBySessionId(Long sessionId);
}
