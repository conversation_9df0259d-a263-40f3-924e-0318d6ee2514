package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.WorkshopRecordingAssetDetails;
import com.accelevents.session_speakers.dto.MuxAssetDTO;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

public interface WorkshopRecordingAssetService {

    MuxAssetDTO findDefaultPlayBackForWorkshopSession(long sessionId);

    List<MuxAssetDTO> findAllLivestreamAssetsForWorkshopSession(long sessionId);

    String generateWorkshopRecordingAssetPreSignedUrl(Long assetId);

    void markAssetAsDefaultPlayBackForWorkshopSession(Session session, long id);

    void deleteWorkshopAssetFromS3Bucket(String objectKey);

    String getWorkshopRecordingPlayBackId(WorkshopRecordingAssetDetails assetsDetails);

    void saveWithSequence(WorkshopRecordingAssetDetails assetDetails);

    @Transactional(rollbackOn = {Exception.class})
    void updateWithSequence(Long id, Long topId, Long topBottomId, Event event, User user);

    List <WorkshopRecordingAssetDetails> findBySessionIdAndEventId(Long sessionId, Long eventId);

    void updateWorkshopAssetsVisibility(Long id, boolean isVisible, Event event, User user);

    void deleteWorkshopAssets(Long id, Event event, User user);

    void updateWorkshopAssets(MuxAssetDTO muxAssetDTO, Event event, User user);

    List<WorkshopRecordingAssetDetails> getVisibleAssetsBySessionIdAndEventId(Long sessionId, Long eventId);
    Map<Long,List<MuxAssetDTO>> getWorkshopRecordingAssetsMapByVisibleStatusAndSessionIds(List<Long> sessionIds);
}
