package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.WorkshopRecordingAssetDetails;
import com.accelevents.session_speakers.dto.ChimeMessageDetailsDto;

public interface WorkshopSessionRecordingService {

    void startChimeMediaCapturePipeLine(Session session, Event event, ChimeMessageDetailsDto chimeMessageDetailsDto);

    void stopChimeMediaCapturePipeLine(Session session, Event event, String chimeEventType, ChimeMessageDetailsDto chimeMessageDetailsDto);

    void updateWorkshopRecordingMetaData(WorkshopRecordingAssetDetails assetDetail);
}
