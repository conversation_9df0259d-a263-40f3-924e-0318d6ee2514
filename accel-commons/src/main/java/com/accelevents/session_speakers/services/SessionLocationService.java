package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.SessionLocation;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.session_speakers.dto.SessionLocationDTO;
import com.accelevents.session_speakers.dto.UploadSessionLocationResponseContainer;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface SessionLocationService {
    Long addSessionLocation(SessionLocationDTO sessionLocationDTO, Event event, User user);

    void updateSessionLocation(Long id,SessionLocationDTO sessionLocationDTO,Event event,User user);

    void deleteSessionLocation(Long id, Event event, User user);

    DataTableResponse getAllSessionLocations(Event event, Pageable pageable, String searchString);

    Optional<List<String>> checkSessionLocationAvailability(SessionLocationDTO sessionLocationDTO, Event event);

    SessionLocationDTO getSessionLocationById(Long id, Event event);

    List<SessionLocation> getSessionLocationByEventId(Event event);

    List<Object[]> countSessionsByLocationIds(Long eventId, List<Long> locationIds);

    SessionLocation getSessionLocationById(Long id);

    List<SessionLocationDTO> getAllSessionLocationsByEventId(Long eventId);

    List<UploadSessionLocationResponseContainer> importSessionLocationList(MultipartFile multiPartFile, Event event, User user) throws IOException;

    void removeLocationIdFromSessions(Long locationId, Event event);

    boolean isLocationExistsByNameAndEventId(String name, Long eventId);
}
