package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.JoinMuxAssetWithArchive;
import com.accelevents.session_speakers.repo.JoinMuxAssetWithArchiveRepository;
import com.accelevents.session_speakers.services.JoinMuxAssetWithArchiveRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class JoinMuxAssetWithArchiveRepoServiceImpl implements JoinMuxAssetWithArchiveRepoService {

    @Autowired
    JoinMuxAssetWithArchiveRepository joinMuxAssetWithArchiveRepository;


    @Override
    public Optional<JoinMuxAssetWithArchive> findByAssetIdAndRecordStatus(Long assetId, RecordStatus recordStatus) {
        return joinMuxAssetWithArchiveRepository.findByAssetIdAndRecordStatus(assetId, recordStatus);
    }

    @Override
    public Optional<JoinMuxAssetWithArchive> findByAssetId(Long assetId) {
        return joinMuxAssetWithArchiveRepository.findByAssetId(assetId);
    }

    @Override
    public JoinMuxAssetWithArchive save(JoinMuxAssetWithArchive joinMuxAssetWithArchive) {
        return joinMuxAssetWithArchiveRepository.save(joinMuxAssetWithArchive);
    }

    @Override
    public List<JoinMuxAssetWithArchive> findAllByEventIdAndRecordStatus(Long eventId, RecordStatus recordStatus) {
        return joinMuxAssetWithArchiveRepository.findAllByEventIdAndRecordStatus(eventId, recordStatus);
    }

    @Override
    public Optional<JoinMuxAssetWithArchive> findByJobId(String jobId) {
        return joinMuxAssetWithArchiveRepository.findByJobId(jobId);
    }
}
