package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.WorkshopRecordingAssetDetails;
import com.accelevents.session_speakers.dto.MuxAssetDTO;

import java.util.List;
import java.util.Optional;

public interface WorkshopRecordingAssetsRepoService {

    WorkshopRecordingAssetDetails save(WorkshopRecordingAssetDetails workshopRecordingAssetDetails);

    Optional<WorkshopRecordingAssetDetails> findByPipelineId(String pipelineId);

    void markAllWorkshopRecordingAssetAsNonDefaultPlayBack(Long sessionId);

    void markWorkshopRecordingAssetAsDefaultPlayBack(Long id);

    MuxAssetDTO findDefaultPlayBackForWorkShopSession(Long sessionId);

    List<MuxAssetDTO> getAllBySessionId(Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findById(Long id);

    List<WorkshopRecordingAssetDetails> findAllBySessionId(Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndPipelineIdIsNull(Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndPipelineId(Long sessionId, String pipelineId);

    List<MuxAssetDTO> findAllBySessionIds(List<Long> sessionIds);

    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndAttendeeCountIsNotNull(Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findBySessionIdAndAttendeeCountIsNotNullOrPipelineIdIsNull(Long sessionId);

    Optional<WorkshopRecordingAssetDetails> findByFileName(String pipelineId);

    List<WorkshopRecordingAssetDetails> findBySessionIdAndEventId(Long sessionId, Long eventId);

    WorkshopRecordingAssetDetails findFirstBySessionIdAndEventIdOrderByPositionDesc(Long sessionId, Long eventId);

    void updatePositionItem(long sessionId, long eventId, double startPosition, double endPosition, double addingPositionValue);

    void updatePositionForAllItem(Long sessionId, Long eventId, double sequence);

    List<WorkshopRecordingAssetDetails> nextPositionItem(Long sessionId, Long eventId, double position);

    List<WorkshopRecordingAssetDetails> previousPositionItem(Long sessionId, Long eventId, double position);

    List<WorkshopRecordingAssetDetails> getVisibleAssetsBySessionIdAndEventId(Long sessionId, Long eventId);
    List<MuxAssetDTO> findWorkshopRecordingByVisibleStatusSessionIdsIn(List<Long> sessionIds, boolean visible);
}
