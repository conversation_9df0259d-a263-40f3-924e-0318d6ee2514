package com.accelevents.session_speakers.services;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.JoinMuxAssetWithArchive;

import java.util.List;
import java.util.Optional;

public interface JoinMuxAssetWithArchiveRepoService {

    Optional<JoinMuxAssetWithArchive> findByAssetIdAndRecordStatus(Long assetId, RecordStatus recordStatus);

    Optional<JoinMuxAssetWithArchive> findByAssetId(Long assetId);

    JoinMuxAssetWithArchive save(JoinMuxAssetWithArchive joinMuxAssetWithArchive);

    List<JoinMuxAssetWithArchive> findAllByEventIdAndRecordStatus(Long eventId, RecordStatus recordStatus);

    Optional<JoinMuxAssetWithArchive> findByJobId(String jobId);
}
