package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.TicketingTypeTagAndTrack;
import com.accelevents.session_speakers.dto.KeyValueDTO;
import com.accelevents.session_speakers.services.TicketingTypeTagAndTrackRepoService;
import com.accelevents.session_speakers.services.TicketingTypeTagAndTrackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TicketingTypeTagAndTrackServiceImpl implements TicketingTypeTagAndTrackService {

    @Autowired
    private TicketingTypeTagAndTrackRepoService ticketingTypeTagAndTrackRepoService;

    @Override
    public void createRecords(Long ticketingTypeId, Set<Long> tagIdOrTrackIds) {
        if(CollectionUtils.isEmpty(tagIdOrTrackIds))
            return;
        this.saveAll(tagIdOrTrackIds.stream().map(e-> new TicketingTypeTagAndTrack(ticketingTypeId, e)).collect(Collectors.toList()));
    }

    @Override
    public void deleteRecords(Long ticketingTypeId, Set<Long> tagIdOrTrackIds) {
        if(CollectionUtils.isEmpty(tagIdOrTrackIds))
            return;
        ticketingTypeTagAndTrackRepoService.deleteByTicketIdAndTagOrTrackIdIn(ticketingTypeId,tagIdOrTrackIds);
    }

    @Override
    public Iterable<TicketingTypeTagAndTrack> saveAll(Iterable<TicketingTypeTagAndTrack> ticketTagAndTracks) {
        return ticketingTypeTagAndTrackRepoService.saveAll(ticketTagAndTracks);
    }

    @Override
    public List<TicketingTypeTagAndTrack> findByTicketingTypeId(Long ticketingTypeId) {
        return ticketingTypeTagAndTrackRepoService.findByTicketingTypeId(ticketingTypeId);
    }

    @Override
    public List<KeyValueDTO> findTagAndTrackByTicketingTypeIds(List<Long> ticketingTypeIds) {
        return !CollectionUtils.isEmpty(ticketingTypeIds) ? ticketingTypeTagAndTrackRepoService.findTagAndTrackByTicketingTypeIds(ticketingTypeIds) : Collections.emptyList();
    }
}
