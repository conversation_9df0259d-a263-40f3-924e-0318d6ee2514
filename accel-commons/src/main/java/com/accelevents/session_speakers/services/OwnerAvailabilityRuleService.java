package com.accelevents.session_speakers.services;

import com.accelevents.dto.OwnAvailabilityRuleDto;
import com.accelevents.domain.session_speakers.OwnerAvailabilityRule;

import java.util.List;

public interface OwnerAvailabilityRuleService {

    OwnerAvailabilityRule getOrCreateOwnerAvailabilityRuleByEventIdAndUserId(Long eventId, Long userId);

    OwnerAvailabilityRule save(OwnerAvailabilityRule ownerAvailabilityRule);

    OwnerAvailabilityRule updateAttendeeMeetingRulesAndDuration(Long availabilityDuration, List<OwnAvailabilityRuleDto> meetingRules, Long eventId, Long userId);

    void updateOwnerAvailabilityRule();

    List<OwnerAvailabilityRule> findByOwnerAvailabilityRuleList(Long eventId, List<Long> attendeeIds);
}
