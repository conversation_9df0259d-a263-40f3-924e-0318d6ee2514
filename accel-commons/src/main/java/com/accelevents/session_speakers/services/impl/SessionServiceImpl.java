package com.accelevents.session_speakers.services.impl;

import com.accelevents.auction.dto.ExhibitorDropDownDto;
import com.accelevents.auction.dto.ExhibitorStaffDTO;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.*;
import com.accelevents.domain.virtual.Sponsors;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.messages.EnumQNAType;
import com.accelevents.networking.services.NetworkingLounge;
import com.accelevents.networking.services.NetworkingRulesService;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.*;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROSessionService;
import com.accelevents.ro.neptune.RONeptuneAttendeeDetailService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.audience.Audience;
import com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsUtils;
import com.accelevents.services.elasticsearch.videoanalytics.VimeoService;
import com.accelevents.services.elasticsearch.videoanalytics.YoutubeService;
import com.accelevents.services.impl.AttendeeProfileServiceImpl;
import com.accelevents.services.impl.ExhibitorsServiceImpl;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.neptune.NeptuneNetworkingLoungeService;
import com.accelevents.services.repo.CacheService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.KeyValueRepoService;
import com.accelevents.services.repo.helper.SurveyConfigRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.*;
import com.accelevents.session_speakers.services.*;
import com.accelevents.ticketing.dto.TicketingDatesDto;
import com.accelevents.utils.*;
import com.accelevents.virtualevents.dto.GAUtils;
import com.accelevents.virtualevents.dto.RtmpDto;
import com.amazonaws.services.chimesdkmeetings.model.LimitExceededException;
import com.amazonaws.services.chimesdkmeetings.model.Meeting;
import com.cloudinary.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.opencsv.CSVReader;
import org.apache.commons.lang3.ObjectUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.data.domain.*;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.domain.enums.EnumKeyValueType.TAG;
import static com.accelevents.domain.enums.EnumKeyValueType.TRACK;
import static com.accelevents.domain.enums.EnumSessionFormat.*;
import static com.accelevents.domain.enums.EnumUserSessionStatus.REGISTERED;
import static com.accelevents.domain.enums.EventFormat.IN_PERSON;
import static com.accelevents.domain.enums.StreamProvider.DIRECT_UPLOAD;
import static com.accelevents.exceptions.NotAcceptableException.SessionExceptionMsg.*;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.CAN_NOT_SET_DEFAULT_PLAYBACK;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_SLOTS_COLLIDES;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.*;
import static com.accelevents.session_speakers.dto.UserSessionDTO.toEntity;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.DateUtils.getCurrentDate;
import static com.accelevents.utils.DateUtils.getFormattedDate;
import static com.accelevents.utils.GeneralUtils.*;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;
import static java.util.Collections.*;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.springframework.util.CollectionUtils.isEmpty;

@Service
public class SessionServiceImpl implements SessionService {

    private static final Logger log = LoggerFactory.getLogger(SessionServiceImpl.class);
    Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Autowired
    private SessionRepoService sessionRepoService;
    @Autowired
    private ROSessionService roSessionService;

    @Autowired
    private SurveyConfigRepoService surveyConfigRepoService;
    @Autowired
    private SessionSpeakerService sessionSpeakerService;
    @Autowired
    private KeyValueService keyValueService;
    @Autowired
    private UserSessionService userSessionService;
    @Autowired
    private UserInterestedSessionService userInterestedSessionService;
    @Autowired
    private NetworkingRulesService networkingRulesService;
    @Autowired
    private MUXLivestreamAssetService muxLivestreamAssetService;
    @Autowired
    private SessionTagAndTrackService sessionTagAndTrackService;
    @Autowired
    private ChimeService chimeService;
    @Autowired
    private SponsorsService sponsorsService;
    @Autowired
    private ExhibitorService exhibitorService;
    @Autowired
    private EventChecklistService eventChecklistService;
    @Autowired
    private SessionSpeakerRepoService sessionSpeakerRepoService;
    @Autowired
    private YoutubeService youtubeService;
    @Autowired
    private VimeoService vimeoService;
    @Autowired
    private SessionDetailsService sessionDetailsService;
    @Autowired
    private GetStreamService getStreamService;
    @Autowired
    TicketingRepository ticketingRepository;

    @Autowired
    TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Autowired
    private MuxService muxService;
    @Autowired
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;
    @Autowired
    private SpeakerService speakerService;
    @Autowired
    private VirtualEventPortalService virtualEventPortalService;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private ChargebeeService chargebeeService;
    @Autowired
    SessionDetailsRepoService sessionDetailsRepoService;
    @Autowired
    UserSessionRepoService userSessionRepoService;

    @Autowired
    private SessionSpeakerRepo sessionSpeakerRepo;
    @Autowired
    private StaffService staffService;
    @Autowired
    private SessionThirdPartyService sessionThirdPartyService;
    @Autowired
    private VonageService vonageService;
    @Lazy
    @Autowired
    private SessionDeleteHandler sessionDeleteHandler;
    @Autowired
    private ServiceHelper serviceHelper;
    @Autowired
    private WorkshopSessionRecordingService workshopSessionRecordingService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private TicketingTypeTagAndTrackRepoService ticketingTypeTagAndTrackRepoService;
    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;
    @Autowired
    private MessageToContactService messageToContactService;
    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;

    private static final double SEQUENCE = 1000;

    @Autowired
    private NeptuneNetworkingLoungeService neptuneNetworkingLoungeService;

    @Autowired
    private SessionRepo sessionRepo;

    @Autowired
    private UserSessionRepo userSessionRepo;

    @Autowired
    private WorkshopRecordingAssetService workshopRecordingAssetService;

    @Autowired
    private VirtualEventService virtualEventService;

    @Autowired
    private SpeakerHelperService speakerHelperService;

    @Autowired
    private WorkshopRecordingAssetsRepoService workshopRecordingAssetsRepoService;

    @Autowired
    @Lazy
    private SessionBroadCastService broadCastService;
    @Autowired
    private VirtualEventSessionLoggingService virtualEventSessionLoggingService;
    @Autowired
    @Lazy
    private S3UploadService s3UploadService;
    @Autowired
    private ExhibitorsServiceImpl exhibitorsServiceImpl;

    @Autowired private GraphQLConfiguration graphQLConfiguration;

    @Autowired
    private KeyValueRepoService keyValueRepoService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private MeetingScheduleService meetingScheduleService;
    @Autowired
    private AttendeeProfileServiceImpl attendeeProfileService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private EventTaskService eventTaskService;
    @Autowired
    private ConfirmationEmailRepository confirmationEmailRepository;

    @Autowired
    private GamificationCacheStoreService<String,Object> redisCacheService;

    @Autowired
    private MUXLivestreamAssetRepo muxLivestreamAssetRepo;

    @Autowired
    private WorkshopRecordingAssetsRepo workshopRecordingAssetsRepo;

    @Autowired
    private SessionLocationRepo sessionLocationRepo;

    @Autowired
    private SessionLocationService sessionLocationService;

    @Autowired
    private CustomFormAttributeDataRepository customFormAttributeDataRepository;
    
    @Autowired
    private CustomFormAttributeService customFormAttributeService;

    @Autowired
    private SpeakerRepoService speakerRepoService;

    @Value("${apiBaseUrl}")
    private String apiBaseUrl;

    private  static  final List<SessionTypeFormat> inPersonHybrid = Arrays.asList(SessionTypeFormat.IN_PERSON, SessionTypeFormat.HYBRID);
    private  static  final List<SessionTypeFormat> virtualHybrid = Arrays.asList(SessionTypeFormat.VIRTUAL, SessionTypeFormat.HYBRID);

    private SessionSubtitleRepoService sessionSubtitleRepoService;
    private SessionSubtitleService sessionSubtitleService;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private EventTicketsService eventTicketsService;

    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Autowired
    private TicketTypeTrackSessionLimitsRepository ticketTypeTrackSessionLimitsRepository;

    @Autowired
    private UserRepoService userRepoService;

    @Autowired
    private CustomFormAttributeRepository customFormAttributeRepository;

    @Autowired
    @Lazy
    private ContactService contactService;

    public static final String[] EXPECTED_HEADERS_WITH_SESSION_ID = {
            Constants.SESSION_CSV_ID, Constants.TITLE, Constants.FORMAT, Constants.SESSION_TYPE,
            Constants.SESSION_CSV_START_DATE, Constants.SESSION_CSV_START_TIME, Constants.SESSION_CSV_END_TIME,
            Constants.FULL_DETAIL, Constants.CAPACITY, Constants.SHORT_DESCRIPTION, Constants.TAGS, Constants.TRACKS,
            Constants.LOCATION_ID, Constants.PRIMARY_SPEAKERS, Constants.SECONDARY_SPEAKERS
    };

    public static final String[] EXPECTED_HEADERS_WITHOUT_SESSION_ID = {
            Constants.TITLE, Constants.FORMAT, Constants.SESSION_TYPE,
            Constants.SESSION_CSV_START_DATE, Constants.SESSION_CSV_START_TIME, Constants.SESSION_CSV_END_TIME,
            Constants.FULL_DETAIL, Constants.CAPACITY, Constants.SHORT_DESCRIPTION, Constants.TAGS, Constants.TRACKS,
            Constants.LOCATION_ID, Constants.PRIMARY_SPEAKERS, Constants.SECONDARY_SPEAKERS
    };

    @Autowired
    public SessionServiceImpl(SessionSubtitleRepoService sessionSubtitleRepoService,SessionSubtitleService sessionSubtitleService){
        this.sessionSubtitleRepoService = sessionSubtitleRepoService;
        this.sessionSubtitleService = sessionSubtitleService;
    }

    @Override
    public void saveAll(List<Session> sessions) {
        sessionRepoService.saveAll(sessions);
    }

    @Override
    public Session save(Session session) {
        session = sessionRepoService.save(session);

        return session;
    }


    @Override
    public Session getSessionById(Long id, Event event) {
        return sessionRepoService.getSessionById(id, event);
    }

    @Override
    @Transactional
    public Long createSession(Event event, SessionDTO dto) {
        validateDataForAcceleventStudio(dto);
        validateDateAndTimeForSession(event, dto.getStartTime(), dto.getEndTime(), EnumSessionStatus.DRAFT.equals(dto.getStatus()));
        throwErrorIfTimeSlotConflicts(event, dto.getFormat(), dto.getStartTime(), dto.getEndTime(), 0L, dto.getStatus());
        validateSurveyConfiguration(event,dto);
        if(dto.getSurveyId() == null) {
            SurveyConfiguration defaultSurveyByEventId = surveyConfigRepoService.findDefaultSurveyByEventId(event.getEventId());
            if(!ObjectUtils.isEmpty(defaultSurveyByEventId)) {
                dto.setSurveyId(defaultSurveyByEventId.getSurveyId());
                dto.setSurveyEnabled(true);
            }
        }
        Session session = dto.createEntity(event);
        setPositionForSession(session);

        if(dto.getCustomAttributeDetailsDto() != null){
            CustomFormAttributeData customFormAttributeData = customFormAttributeService.saveCustomAttributeData(dto.getCustomAttributeDetailsDto(), new CustomFormAttributeData());
            session.setSessionAttributeId(customFormAttributeData.getId());
        }

        session = sessionRepoService.save(session);
        if (EnumSessionFormat.MEET_UP.equals(session.getFormat())) {
            networkingRulesService.createOrUpdateNetworkingRuleWhenCreatingSession(event, session);
        }
        // Update event agenda added flag
        updateEventAgendaAddedChecklistFlag(event, true);
        //add in session_details table
        sessionDetailsService.saveSessionDetails(session, null, null, dto);
        eventTaskService.updateEventTaskAndAssignTaskToSession(event,Collections.singletonList(session.getId()));
        return session.getId();
    }

    private void validateSurveyConfiguration(Event event, SessionDTO dto) {
        if(dto.getSurveyId() != null){
            if(!dto.isSurveyEnabled()){
                throw new NotAcceptableException(NotAcceptableException.SurveyException.SURVEY_CONFIGURATION_IS_NOT_ENABLED);
            }
            if(!surveyConfigRepoService.isSurveyExist(event.getEventId(),dto.getSurveyId())){
                throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
            }
        } else if(dto.isSurveyEnabled()){
                throw new NotAcceptableException(NotAcceptableException.SurveyException.SURVEY_IS_NOT_PRESENT);
            }
    }

    @Override
    public Session setPositionForSession(Session session) {
        if (session.getId() == 0) {
            Session lastItem = getLastConcurrentSession(session);
            if (null != lastItem) {
                session.setPosition(lastItem.getPosition() != null ? lastItem.getPosition() + SEQUENCE : SEQUENCE);
            } else {
                session.setPosition(SEQUENCE);
            }
        }
        return session;
    }

    @Override
    public void updateChimeConfigDetails(Long sessionId, Event event, User user, ChimeConfigDto chimeConfigDto) {
        Session session = sessionRepoService.getSessionById(sessionId, event);
        sessionDetailsService.updateChimeConfigDetails(session, event, user, chimeConfigDto);
    }

    @Override
    public ChimeConfigDto getChimeConfigDetails(Long sessionId, Event event) {
        Session session = sessionRepoService.getSessionById(sessionId, event);
       return sessionDetailsService.getChimeConfigDetails(session);
    }

    private Session getLastConcurrentSession(Session session) {
        return sessionRepoService.getLastConcurrentSession(session.getEventId(), session.getStartTime(), session.getEndTime());
    }

    @Override
    public void checkNonVisualSessionCount(EnumSessionFormat sessionFormat, Event event, EnumSessionFormat oldSessionFormat, boolean forUpdateSession) {
        List<EnumSessionFormat> enumSessionFormats = Arrays.asList(BREAK, OTHER, EXPO);
        long nonVisualSessionsCount = sessionRepoService.getNonVisualSessionsByEvent(event.getEventId(), enumSessionFormats);
        if (forUpdateSession) {
            if (!enumSessionFormats.contains(sessionFormat) && enumSessionFormats.contains(oldSessionFormat) && nonVisualSessionsCount > 9) {
                checkMaxAgendaItemsByPlanType(event, nonVisualSessionsCount);
            }
        } else {
            if (!enumSessionFormats.contains(sessionFormat)) {
                checkMaxAgendaItemsByPlanType(event, nonVisualSessionsCount);
            }
        }
    }

    private void validateDateAndTimeForSession(Event event, String startTime, String endTime, Boolean isDraftSession) {
        if(Boolean.TRUE.equals(isDraftSession)){
            return;
        }
        if((StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) && !isDraftSession){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.INVALID_SESSION_DATE_TIME);
        }
        log.info("Session StartTime : {} | End Time : {}", startTime, endTime);
        TicketingDatesDto ticketing = ticketingRepository.findEventStartAndEndDateByEventid(event);
        Date startDate = DateUtils.getFormattedDate(startTime, LOCAL_DATE_FORMAT);
        Date endDate = DateUtils.getFormattedDate(endTime, LOCAL_DATE_FORMAT);
        if (null == startDate || null == endDate) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.INVALID_SESSION_DATE_TIME);
        }
        Date sessionStartDate = getDateInUTC(startTime, event.getEquivalentTimeZone());
        Date sessionEndDate = getDateInUTC(endTime, event.getEquivalentTimeZone());
        log.info("validateDateAndTimeForSession eventId {} sessionStartDate {} sessionEndDate {}",event.getEventId(),sessionStartDate,sessionEndDate);
        Date eventEndTime = startDate;
        Calendar calendarForEventEndTime=Calendar.getInstance();
        calendarForEventEndTime.setTime(eventEndTime);
        calendarForEventEndTime.set(Calendar.HOUR_OF_DAY, 0);
        calendarForEventEndTime.set(Calendar.MINUTE, 0);
        calendarForEventEndTime.set(Calendar.SECOND, 0);
        calendarForEventEndTime.set(Calendar.MILLISECOND, 0);
        //set 11:59:59 PM admin can create session up to 11:59 according to plan day
        calendarForEventEndTime.set(Calendar.MINUTE,  1440 - 1);
        eventEndTime= getDateInUTC(calendarForEventEndTime.getTime(), event.getEquivalentTimeZone());
        log.info("validateDateAndTimeForSession eventId {} eventEndTime {}",event.getEventId(),eventEndTime);
        if (!(sessionStartDate.after(ticketing.getEventStartDate()) && sessionEndDate.before(eventEndTime))) {
            if (sessionStartDate.equals(ticketing.getEventStartDate()) || sessionEndDate.equals(eventEndTime)) {
                return;
            }
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_END_TIME_SHOULD_BE_BEFORE_11_59PM);
        }
    }

    private void updateEventAgendaAddedChecklistFlag(Event event, boolean isEventAgendaAdded) {
        EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
        if (null != eventChecklist && isEventAgendaAdded != eventChecklist.isEventAgendaAdded()) {
            eventChecklist.setEventAgendaAdded(isEventAgendaAdded);
            this.eventChecklistService.save(eventChecklist);
        }
    }

    private void validateDataForAcceleventStudio(SessionDTO dto) {
        if (StreamProvider.ACCELEVENTS.equals(dto.getStreamProvider())
                && (StringUtils.isBlank(dto.getStreamKey())
                || StringUtils.isBlank(dto.getStreamUrl())
                || StringUtils.isBlank(dto.getRtmpUrl()))) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.REQUIRED_FOR_AE_STUDIO);
        }
    }

    private void throwErrorIfTimeSlotConflicts(Event event, EnumSessionFormat format, String startTime, String endTime, Long sessionId, EnumSessionStatus status) {
        if (!MAIN_STAGE.equals(format) || EnumSessionStatus.DRAFT.equals(status)) {
            return;
        }
        List<String> conflictingSessions = sessionRepoService.findSessionsWithConflictingSlot(event,
                startTime,
                endTime,
                sessionId);

        if (!isEmpty(conflictingSessions)) {
            String conflictingSessionName = conflictingSessions.get(0);
            NotAcceptableException.SessionSpeakerExceptionMsg exceptionMsg = SESSION_SLOTS_COLLIDES;
            String errorMessageTemplate = Constants.SESSION_SLOTS_COLLIDES;
            String errorMessage = errorMessageTemplate.replace(Constants.SESSION_NAME, conflictingSessionName);
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.SESSION_NAME, conflictingSessionName);
            exceptionMsg.setErrorMessage(errorMessage);
            exceptionMsg.setDeveloperMessage(errorMessage);
            exceptionMsg.setDefaultMessage(Constants.SESSION_SLOTS_COLLIDES);
            exceptionMsg.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new NotAcceptableException(exceptionMsg);
        }
    }

    private void validateDataForSelectedTicketTypeAndRegisteredUser (Session session,SessionDTO dto,Event event ){
        log.info("validateDataForSelectedTicketTypeAndRegisteredUser session : {} | dto : {} | event : {}",session ,dto,event);
        List<Long> selectedTicketTypeInDb = GeneralUtils.convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered());
         selectedTicketTypeInDb.removeAll(dto.getTicketTypesThatCanBeRegistered());
         if(!CollectionUtils.isEmpty(selectedTicketTypeInDb) && userSessionRepoService.isAnyUsersRegisteredTicketTypesIn(event.getEventId(),session.getId(),EnumUserSessionStatus.REGISTERED,selectedTicketTypeInDb)){
             throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.ALREADY_TICKET_TYPE_USED);
         }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public EnumChannelStatus updateSession(Long id, SessionDTO dto, Event event, User user) {

        Session session = sessionRepoService.getSessionByIdWithoutCache(id, event);
        validateDataForAcceleventStudio(dto);
        validateDraftSession(dto,session, event);
        validateDataForSelectedTicketTypeAndRegisteredUser(session, dto, event);
        validateSurveyConfiguration(event,dto);
        updateSessionPositionConcurrentSession(session);
        validateDateAndTimeForSession(event, dto.getStartTime(), dto.getEndTime(), EnumSessionStatus.DRAFT.equals(session.getStatus()));
        throwErrorIfTimeSlotConflicts(event, dto.getFormat(), dto.getStartTime(), dto.getEndTime(), id, dto.getStatus());
        stopBroadcastInLiveSessionIfChangedSessionFormatOrProvider(dto, event, user, session);
        EnumChannelStatus channelStatus = null;
        StreamProvider streamProvider = StreamProvider.NULL;
        String streamUrl = null;

        log.info("BEFORE UPDATE SESSION FETCH SESSION_ID--> {} | SESSION--> {} | SESSION_DTO--> {} ", session.getId(), session, dto);

        if (StringUtils.isNotBlank(dto.getTitle()) && !dto.getTitle().equals(session.getTitle())) {
            channelStatus = EnumChannelStatus.UPDATE;
        } else if ((MAIN_STAGE.equals(session.getFormat()) || BREAKOUT_SESSION.equals(session.getFormat()) || EnumSessionFormat.WORKSHOP.equals(session.getFormat())) && dto.isChatEnabled() != session.isChatEnabled()) {
            channelStatus = EnumChannelStatus.UPDATE;
        } else if (MEET_UP.equals(dto.getFormat()) && !MEET_UP.equals(session.getFormat())) {
            channelStatus = EnumChannelStatus.DELETE;
        } else if (!MEET_UP.equals(dto.getFormat()) && MEET_UP.equals(session.getFormat())) {
            channelStatus = EnumChannelStatus.CREATE;
        }

        if(dto.getStreamProvider() != null && StringUtils.isNotBlank(dto.getStreamUrl()) && (((!dto.getStreamUrl().equals(session.getStreamUrl()) || !dto.getStreamProvider().equals(session.getStreamProvider()))
                    && (StreamProvider.YOUTUBE.equals(dto.getStreamProvider()) || StreamProvider.VIMEO.equals(dto.getStreamProvider())))
                    || StreamProvider.VIDYARD.equals(dto.getStreamProvider()) || StreamProvider.WISTIA.equals(dto.getStreamProvider())
                    || StreamProvider.FACEBOOK.equals(dto.getStreamProvider()))) {
                streamProvider = dto.getStreamProvider();
                streamUrl = dto.getStreamUrl();

        }

        List<Long> selectedTicketTypeInDb = GeneralUtils.convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered());
        if(!CollectionUtils.isEmpty(dto.getTicketTypesThatCanBeRegistered())
                && !selectedTicketTypeInDb.containsAll(dto.getTicketTypesThatCanBeRegistered())) {
            raiseErrorIfRestrictedTicketTypeSelected(session, dto);
        }

        handleTagsAndTracks(session, dto,event);

        if(!CollectionUtils.isEmpty(dto.getTicketTypesThatCanBeRegistered())) {
            session.setSessionTagAndTracks(new HashSet<>(sessionTagAndTrackService.findBySessionId(session.getId())));
            dto.setTicketTypesThatCanBeRegistered(handleTicketTypesThatCanBeRegistered(session, dto.getTicketTypesThatCanBeRegistered(), dto));
            //Find the all dates(Main ticket types) based on the list of ticket type ids
            List<Long> allDatesTicketTypes = ticketingTypeRepository.findListOfTicketingTypeIdsAndRecurringEventIdIsNull(dto.getTicketTypesThatCanBeRegistered());
            List<Long> recurringTicketTypes = new ArrayList<>();
            if(!CollectionUtils.isEmpty(allDatesTicketTypes)){
                //fetching recurring ticket type ids based on the list of all dates ticket type ids
                 recurringTicketTypes = ticketingTypeRepository.findTicketTypeIdsByCreatedFroms(allDatesTicketTypes);
            }
            if(!CollectionUtils.isEmpty(recurringTicketTypes)) {
                //validating the recurring event ids for selected ticket types and registered user if there is purchased or bookmarked then we are not allowed to remove that ticket types from the session
                validateDataForSelectedTicketTypeAndRegisteredUserForRecurringEventTickets(session,recurringTicketTypes, event);
                log.info("Found recurring ticket types {} for selected ticket types {} for session : {}", recurringTicketTypes, dto.getTicketTypesThatCanBeRegistered(), session.getId());
                Set<Long> uniqueTicketTypes = new HashSet<>(allDatesTicketTypes);
                uniqueTicketTypes.addAll(recurringTicketTypes);
                //set the unique ticket type ids with recurring ticket type ids
                dto.setTicketTypesThatCanBeRegistered(new ArrayList<>(uniqueTicketTypes));
            }
            session.setTicketTypesThatCanBeRegistered(convertLongListToCommaSeparated(dto.getTicketTypesThatCanBeRegistered()));
        }

        if(StreamProvider.DIRECT_UPLOAD.equals(dto.getStreamProvider()) && dto.isDirectVideoAutoStart() && StringUtils.isEmpty(dto.getStreamUrl())){
            throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.NOT_POSSIBLE_TO_ACTIVATE_AUTOPLAY);
        }

        CustomFormAttributeData customFormAttributeData = new CustomFormAttributeData();
        
        if(dto.getCustomAttributeDetailsDto() != null){
            if (session.getSessionAttribute() != null){
                customFormAttributeData = session.getSessionAttribute();
            }

            customFormAttributeData = customFormAttributeService.saveCustomAttributeData(dto.getCustomAttributeDetailsDto(), customFormAttributeData);
            session.setSessionAttributeId(customFormAttributeData.getId());
        }

        session = sessionRepoService.save(dto.updateEntity(session, event, Boolean.TRUE));

        try {
            sessionDetailsService.saveSessionDetails(session, streamProvider, streamUrl, dto);
        }catch(Exception e){
            log.info("UPDATE SESSION FETCH || saveSessionDetails ||  SESSION_ID {} livestream {} ", session.getId(), session.getLiveStreamId());
        }


        if (EnumSessionFormat.MEET_UP.equals(session.getFormat())) {
            networkingRulesService.createOrUpdateNetworkingRuleWhenCreatingSession(event, session);
            session.setSessionSpeakers(null);
            sessionSpeakerRepoService.deleteBySessionId(session.getId());
        }
        log.info("AFTER UPDATE SESSION FETCH SESSION_ID--> {} | SESSION--> {} | SESSION_DTO--> {} ", session.getId(), session, dto);
        return channelStatus;
    }

    private void validateDraftSession(SessionDTO dto, Session session, Event event) {
        if(!EnumSessionStatus.DRAFT.equals(session.getStatus()) && EnumSessionStatus.DRAFT.equals(dto.getStatus())){
            Integer userRegisteredSessionBySession = userSessionRepoService.getUserRegisteredSessionBySession(session.getId(), event);
            if(NumberUtils.isNumberGreaterThanZero(userRegisteredSessionBySession)){
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.USER_ALREADY_REGISTER_IN_SESSION);
            }
        }
    }

    private void validateDataForSelectedTicketTypeAndRegisteredUserForRecurringEventTickets(Session session, List<Long> recurringTicketTypesId, Event event) {
        log.info("validateDataForSelectedTicketTypeAndRegisteredUser session : {} | recurringTicketTypesId : {} | event : {}",session ,recurringTicketTypesId,event);
        List<Long> selectedTicketTypeInDb = GeneralUtils.convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered());
        selectedTicketTypeInDb.removeAll(recurringTicketTypesId);
        if(!CollectionUtils.isEmpty(selectedTicketTypeInDb) && userSessionRepoService.isAnyUsersRegisteredTicketTypesIn(event.getEventId(),session.getId(),EnumUserSessionStatus.REGISTERED,selectedTicketTypeInDb)){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.ALREADY_TICKET_TYPE_USED);
        }
    }

    private void updatePostSessionDetailsToConfigureSession(SessionDTO dto, Session session) {
        if(session.getFormat() !=null && !session.getFormat().equals(dto.getFormat()) && !(EXPO.equals(dto.getFormat())|| BREAK.equals(dto.getFormat())) || OTHER.equals(dto.getFormat())) {
            List<SessionDetails> sessionDetailList = sessionDetailsRepoService.getSessionDetailsByPostSessionCTASessionId(session.getId());
            sessionDetailList.forEach(details ->{
                PostSessionCallToActionDto  postSessionCta = CommonUtil.getDtoPostCallToActionJson(details.getPostSessionCallToActionJson());
                if(!(PostSessionCallToActionTypeEnum.LOUNGE.equals(postSessionCta.getDestinationType()) || PostSessionCallToActionTypeEnum.EXHIBITOR.equals(postSessionCta.getDestinationType()))) {
                    log.info("Post session details updated for sessionId {} by session {}", details.getSession().getId(), session.getId());
                    postSessionCta.setDestinationType(PostSessionCallToActionTypeEnum.valueOf(dto.getFormat().name()));
                    details.setPostSessionCallToActionJson(CommonUtil.convertToGson(postSessionCta));
                }
            });
            if(!sessionDetailList.isEmpty()) {
                sessionDetailsRepoService.saveAll(sessionDetailList);
            }
        }
    }

    private void stopBroadcastInLiveSessionIfChangedSessionFormatOrProvider(SessionDTO dto, Event event, User user, Session session) {
        try{
            // Check session is live
            if (isSessionLive(session) && isSessionFormatOrStreamProviderChanged(dto, session) && (isSessionFormatChanged(dto, session))) {
                    if (isAcceleventStudioChanged(session, dto) || (session.getSubStreamProvider() != null && !session.getSubStreamProvider().equals(dto.getSubStreamProvider()))) {
                        broadCastService.stopBroadCast(session.getId(), String.valueOf(session.getId()), event, user);
                        log.info("Broadcast stop for streamProvide {} session {} eventId {} user {}", session.getStreamProvider(), session.getId(), event.getEventId(), user.getUserId());
                    } else if (DIRECT_UPLOAD.equals(session.getStreamProvider()) && !session.isDirectVideoAutoStart()) {
                        log.info("Broadcast stop for Direct upload sessionId {} eventId {} user {}", session.getId(), event.getEventId(), user.getUserId());
                        new SimpleAsyncTaskExecutor().execute(() -> new BroadcastGraphQLHandler(graphQLConfiguration, BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN)
                                .handleCreateOrUpdateSessionStatusLogs(VIDEO_LIVE_STREAM_IDLE, session, user));
                    } else if (EnumSessionFormat.WORKSHOP.equals(session.getFormat())) {
                        ChimeMessageDetailsDto chimeMessageDetailsDto = new ChimeMessageDetailsDto();
                        chimeMessageDetailsDto.setEventType(CHIME_MEETING_ENDED);
                        workshopSessionRecordingService.stopChimeMediaCapturePipeLine(session, event, CHIME_MEETING_ENDED, chimeMessageDetailsDto);
                        log.info("Workshop recording stop for sessionId {} eventId {} user {}", session.getId(), event.getEventId(), user.getUserId());
                    }

            }
        } catch (Exception e) {
            log.info("Error while stop broadcast when session-format/stream-provider change sessionId {}, eventId {} exception {}", session.getId(), event.getEventId(), e.getMessage(), e);
        }
    }

    private boolean isSessionLive(Session session) {
        return (session.getStartTime() != null && session.getEndTime() != null) && DateUtils.getCurrentDate().after(session.getStartTime()) && DateUtils.getCurrentDate().before(session.getEndTime());
    }

    private boolean isSessionFormatChanged(SessionDTO dto, Session session) {
        // If session format changed any other session type = true, if session format changed from main stage to break out , vice versa = false
        return !((MAIN_STAGE.equals(dto.getFormat()) && BREAKOUT_SESSION.equals(session.getFormat())) || (BREAKOUT_SESSION.equals(dto.getFormat()) && MAIN_STAGE.equals(session.getFormat())));
    }

    private boolean isSessionFormatOrStreamProviderChanged(SessionDTO dto, Session session) {
        // Check Session format/Stream provider changed, if we check Accelevents because For AE Studio and RTMP have same stream provider value
        return (session.getFormat() !=null && !session.getFormat().equals(dto.getFormat())) ||
                (session.getSubStreamProvider() != null && !session.getSubStreamProvider().equals(dto.getSubStreamProvider())) ||
                ((session.getStreamProvider() != null && !session.getStreamProvider().equals(dto.getStreamProvider())) || isAcceleventStudioChanged(session, dto));
    }

    private boolean isAcceleventStudioChanged(Session session, SessionDTO dto) {
        return StreamProvider.ACCELEVENTS.equals(session.getStreamProvider()) && session.isAccelEventsStudio() != dto.isAccelEventsStudio();
    }

    private void raiseErrorIfRestrictedTicketTypeSelected(Session session, SessionDTO dto) {

        Set<Long> dbTagOrTrackIds = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toSet());
        Set<Long> inputTagOrTrackIds = getIds(dto.getTags());
        inputTagOrTrackIds.addAll(getIds(dto.getTracks()));

        boolean nothingChanged = inputTagOrTrackIds.containsAll(dbTagOrTrackIds);

        if(nothingChanged) {
            List<Long> restrictedTicketTypes = isRestrictedTicketTypesExists(session, dto.getTicketTypesThatCanBeRegistered());
            if (!CollectionUtils.isEmpty(restrictedTicketTypes)) {
                List<String> restrictedTicketTypeList = ticketingTypeRepository.findByIdIn(restrictedTicketTypes);
                NotAcceptableException.TicketingExceptionMsg exceptionMsg = NotAcceptableException.TicketingExceptionMsg.TICKET_TYPE_RESTRICTED;
                exceptionMsg.setErrorMessage(exceptionMsg.getErrorMessage().replace("${ticketTypes}", org.apache.commons.lang3.StringUtils.join(restrictedTicketTypeList, ',')));
                exceptionMsg.setDeveloperMessage(
                        exceptionMsg.getDeveloperMessage().replace("${ticketTypes}", org.apache.commons.lang3.StringUtils.join(restrictedTicketTypeList, ',')));
                throw new NotAcceptableException(exceptionMsg);
            }
        }
    }

    private List<Long> handleTicketTypesThatCanBeRegistered(Session session, List<Long> ticketTypesThatCanBeRegistered, SessionDTO sessionDTO) {
        List<Long> updatedTicketTypesThatCanBeRegistered = new ArrayList<>(ticketTypesThatCanBeRegistered);
        Set<Long> tagOrTrackIds = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(toSet());
        if(!CollectionUtils.isEmpty(ticketTypesThatCanBeRegistered) && !CollectionUtils.isEmpty(tagOrTrackIds)){
            for(Long eventTicketingTypeId : ticketTypesThatCanBeRegistered){
                List<TicketingTypeTagAndTrack> ticketingTypeTagAndTracks = ticketingTypeTagAndTrackRepoService.findByTicketingTypeIdAndTagOrTrackIdIn(eventTicketingTypeId, tagOrTrackIds);

                if(!CollectionUtils.isEmpty(ticketingTypeTagAndTracks)){
                    updatedTicketTypesThatCanBeRegistered.remove(eventTicketingTypeId);
                    session.setHideSessionFromAttendees(true);
                    if(sessionDTO != null) {
                        sessionDTO.setHideSessionFromAttendees(true);
                    }
                }
            }
        }
        return updatedTicketTypesThatCanBeRegistered;
    }

    private void updateSessionPositionConcurrentSession(Session session) {
        if (session.getPosition().equals(SEQUENCE)) {
            Session lastItem = getLastConcurrentSession(session);
            Session firstItem = getFirstConcurrentSession(session);
            if (null != lastItem && firstItem != null && firstItem.getPosition() !=null && session.getPosition().equals(firstItem.getPosition()) && session.getId()!=firstItem.getId()) {
                session.setPosition(lastItem.getPosition() != null ? lastItem.getPosition() + SEQUENCE : SEQUENCE);
            } else {
                session.setPosition(SEQUENCE);
            }
        }
    }

    private void handleTagsAndTracks(Session session, SessionDTO dto,Event event) {
        Set<Long> dbTagOrTrackIds = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toSet());
        Set<Long> inputTagOrTrackIds = getIds(dto.getTags());
        inputTagOrTrackIds.addAll(getIds(dto.getTracks()));

        Set<Long> addNewList = inputTagOrTrackIds.stream().filter(inputTagId -> !dbTagOrTrackIds.contains(inputTagId)).collect(Collectors.toSet());
        sessionTagAndTrackService.createRecords(session.getId(), addNewList);

        dbTagOrTrackIds.removeAll(inputTagOrTrackIds);
        if (!dbTagOrTrackIds.isEmpty()) {
            // remove the records.
            sessionTagAndTrackService.deleteRecords(session.getId(), dbTagOrTrackIds);

            if(EventFormat.IN_PERSON.equals(event.getEventFormat())){
                List<Long> listOfTicketingTypeIds = ticketingTypeRepository.findListOfTicketingTypeIds(event.getEventId());
                dto.setTicketTypesThatCanBeRegistered(listOfTicketingTypeIds);
            }
        }
    }

    private Set<Long> getIds(List<IdNameDto> idNameDtos) {
        return null != idNameDtos ? idNameDtos.stream().filter(e -> null != e.getId()).map(IdNameDto::getId).collect(toSet()) : Collections.emptySet();
    }

    @Override
    public DataTableResponse getSessionHostList(String expand,
                                                Event event,
                                                String searchStr,
                                                Pageable pageable,
                                                boolean isFromBillingPage,
                                                List<SessionTypeFormat> sessionTypeFormats,List<Date> searchDate,List<EnumSessionFormat> sessionTypes,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming) {
        if (CollectionUtils.isEmpty(sessionTypeFormats)){
            sessionTypeFormats=new ArrayList<>(Arrays.asList(SessionTypeFormat.HYBRID,SessionTypeFormat.VIRTUAL,SessionTypeFormat.IN_PERSON));
        }
        Page<Session> sessionsPage = sessionRepoService.getAllHostSessionByEventId(event, searchStr, pageable, isFromBillingPage,searchDate,sessionTypes,listOfTagAndTrackIds,isPast,isUpcoming,sessionTypeFormats);
        return getSessionDataTable(event, null, expand, sessionsPage);
    }
    @Override
    public DataTableResponse getSessionHostListWithSorting(String expand,
                                                Event event,
                                                String searchStr,
                                                Pageable pageable,
                                                boolean isFromBillingPage,
                                                List<SessionTypeFormat> sessionTypeFormats,List<Date> searchDate,List<EnumSessionFormat> sessionTypes,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming) {
        if (CollectionUtils.isEmpty(sessionTypeFormats)){
            sessionTypeFormats=new ArrayList<>(Arrays.asList(SessionTypeFormat.HYBRID,SessionTypeFormat.VIRTUAL,SessionTypeFormat.IN_PERSON));
        }
        Page<Session> sessionsPage = sessionRepoService.getAllHostSessionByEventIdWithSorting(event, searchStr, pageable, isFromBillingPage,searchDate,sessionTypes,sessionTypeFormats,listOfTagAndTrackIds,isPast,isUpcoming);
        return getSessionDataTable(event, null, expand, sessionsPage);
    }

    @Override
    public DataTableResponse getSessionList(String expand,
                                            Event event,
                                            User user,
                                            Pageable pageable,
                                            SessionFilter sessionFilter, boolean isAdminOrStaff, String calledFrom) {
        Page<Session> sessionsPage = sessionRepoService.getAllSessionByEventId(event, pageable, sessionFilter, user, isAdminOrStaff, calledFrom);
        return getSessionDataTable(event, user, expand, sessionsPage);
    }

    private DataTableResponse getSessionDataTable(Event event, User loggedInUser, String expand, Page<Session> sessionsPage) {
        SessionExpandable expandable = new SessionExpandable(expand, sessionsPage, loggedInUser, event).invoke();
        List<Long> sessionIds = sessionsPage.getContent().stream().map(Session::getId).collect(toList());
        List<Object[]> listOfUserSession = new ArrayList<>();
        Map<Long, Boolean> finalMap = new HashMap<>();
        if(null != loggedInUser && (!CollectionUtils.isEmpty(sessionIds))) {
                listOfUserSession = userSessionRepoService.getCheckInSessionByUserIdAndSessionId(sessionIds, loggedInUser.getUserId());
                if (!CollectionUtils.isEmpty(listOfUserSession)) {
                    Map<Long, Boolean> map = listOfUserSession.stream().collect(Collectors.toMap(obj -> (Long) obj[0], obj -> (Boolean) obj[1], (first, second) -> first));
                    finalMap.putAll(map);
                }

        }
        log.info("Final  Register Sessions map  {} ",finalMap);

        List<SessionBasicDTO> sessionDTOS =
                sessionsPage
                        .getContent()
                        .stream()
                        .map(e -> new SessionDTO(e,
                                event,
                                expandable.getSessionSpeakers().get(e.getId()),
                                new SessionStatsDto(
                                        e.getId(),
                                        expandable.getRegisterSessionStats().get(e.getId()),
                                        expandable.getInterestedSessionStats().get(e.getId()),
                                        expandable.getSessionSpeakerStats().get(e.getId()),
                                        expandable.getAttendeeCount().get(e.getId()),
                                        expandable.getAttendeeBookmarkCount().get(e.getId())
                                ),
                                expandable.getCurrentUserSessionIdAndEventTicketIdMap().get(e.getId()),
                                expandable.getSessionSponsors(e.getSponsorExhibitorJson()),
                                expandable.getSessionAssetsDetail().get(e.getId()),
                                expandable.getSessionTagsAndTracks(e.getId()),
                                expandable.getSessionBookmarkCapacity().get(e.getId()),
                                expandable.getSessionBookmarked().get(e.getId()),
                                finalMap.get(e.getId()),
                                expandable.getSessionWaitlisted().get(e.getId()),
                                sessionDetailsService.getSessionDetailsBySession(e).isPresent() ? sessionDetailsService.getSessionDetailsBySession(e).get() : null))
                        .collect(toList());
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(sessionsPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(sessionsPage.getContent().size());
        dataTableResponse.setData(sessionDTOS);
        log.info("Final  Register Sessions list  {} ",dataTableResponse);

        return dataTableResponse;
    }

    @Override
    public SessionDTO getSessionInfoById(Long sessionId, Event event, User user, String expand) {
        Session session = sessionRepoService.getSessionByIdJoinFetchTagsTrack(sessionId, event);
        SessionDTO dto = new SessionDTO(session, event);
        dto.setShortDescriptionOfSession(session.getShortDescriptionOfSession());
        if (expand.contains(SPEAKER)) {
            dto.setSpeakerList(sessionSpeakerService.getSpeakerDtoBySession(session.getId()));
        }
        if (expand.contains("TAG") || expand.contains("TRACK")) {
            List<Long> keyValueIds = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toList());

            List<KeyValue> keyValue = keyValueService.findAllByIds(keyValueIds);
            dto.setTags(getIdNameDto(keyValue, TAG));
            dto.setTracks(getIdNameDto(keyValue, TRACK));
        }
        if (expand.contains("registerdHolderUsers")) {
            dto.setRegisterdHolderUsers(userSessionRepoService.findRegisteredUsersBySessionIdAndRegisteredStatus(session.getId(), EnumUserSessionStatus.REGISTERED));
        }

        String sponsorExhibitorJson = session.getSponsorExhibitorJson();
        if (expand.contains(Constants.EXHIBITOR_EXPAND)) {
            if (StringUtils.isNotBlank(sponsorExhibitorJson)) {
                List<Long> exhibitorIds = sponsorsService.getExhibitorIdsFromSponsorExhibitorJson(sponsorExhibitorJson);
                if(exhibitorIds != null && !exhibitorIds.isEmpty()){
                    dto.setExihiborList(exhibitorService.findExhibitorCarouselDetailByIds(exhibitorIds));
                }
                else {
                    dto.setExihiborList(Collections.emptyList());
                }
            } else {
                dto.setExihiborList(Collections.emptyList());
            }
        }

        if (expand.contains(Constants.SPONSOR_EXPAND)) {
            if (StringUtils.isNotBlank(sponsorExhibitorJson)) {
                List<Long> sponsorIds = sponsorsService.getSponsorIdsFromSponsorExhibitorJson(sponsorExhibitorJson);
                if(sponsorIds != null && !sponsorIds.isEmpty()){
                    dto.setSponsorList(sponsorsService.getSponsorsByIds(sponsorIds).stream().map(SponsorsDto::new).collect(Collectors.toList()));
                }
                else {
                    dto.setSponsorList(Collections.emptyList());
                }
            } else {
                dto.setSponsorList(Collections.emptyList());
            }
        }
        if (expand.contains("STATS")) {
            Integer registerCount = userSessionService.countRegisteredBySessionId(sessionId);
            Integer interestedCount = userInterestedSessionService.countInterestedBySessionId(sessionId);
            long attendeeCount = 0;
            if (expand.contains(SESSION_EXPAND.ATTENDEE_COUNT)) {
                attendeeCount = userSessionService.countSessionAttendeeBySessionId(sessionId);
            } else if(expand.contains(SESSION_EXPAND.CHECKIN_ATTENDEE_COUNT)) {
                attendeeCount = userSessionRepoService.countSessionAttendeeByEventTicketIdAndSessionId(sessionId);
            }
            dto.setSessionStats(new SessionStatsDto(registerCount, interestedCount, attendeeCount));
        }
        if (user!=null) {
            Map<Long, Boolean> bookmarkedSessionByUser = userSessionService.findBookmarkedSessionByUserIdAndSessionId(user.getUserId(), singletonList(sessionId));
            if (!bookmarkedSessionByUser.isEmpty()) {
                dto.setBookmarked(bookmarkedSessionByUser.get(sessionId));
            }

            Map<Long, Boolean> waitlistedSessionByUser = userSessionService.findWaitlistedSessionByUserIdAndSessionId(user.getUserId(), singletonList(sessionId));
            if (!waitlistedSessionByUser.isEmpty()) {
                dto.setWaitlisted(waitlistedSessionByUser.get(sessionId));
            }
        }
        if (expand.contains("duration")) {
            dto.setDuration(sessionDetailsService.getSessionVideoDurationBySession(session));
        }
        if (expand.contains("PLAYBACKS")) {
            if(EnumSessionFormat.WORKSHOP.equals(session.getFormat())) {
                dto.setAssetList(workshopRecordingAssetsRepoService.findWorkshopRecordingByVisibleStatusSessionIdsIn(singletonList(session.getId()), true));
            } else {
                dto.setAssetList(muxLivestreamAssetRepoService.getMuxAssetsByAssetTypeAndVisibleStatusAndSessionId(session.getId(), AssetType.SESSION_ASSET, true));
            }
        }
        if (user != null && expand.contains("currentUserRegisteredEventTicketId")) {
            dto.setCurrentUserRegisteredEventTicketId(userSessionService.getEventTicketIdsByEventIdAndUserIdAndSessionId(event.getEventId(), user.getUserId(), sessionId));
        }
        // Not In user : Remove
        if (user != null && expand.contains("purchaserUserRegisteredEventTicketId")) {
            dto.setPurchaserUserRegisteredEventTicketId(userSessionService.getEventTicketIdsByEventIdAndUserIdAndSessionPuchaserId(event.getEventId(), user.getUserId(), sessionId));
        }
        Integer registerCounts;
        if(session.getFormat().equals(EnumSessionFormat.WORKSHOP)){
            registerCounts = userSessionService.countRegisteredBySessionId(sessionId);
        }else {
            registerCounts = userSessionService.getSessionRegisterCountAndTicketIdIsNotNullAndEventId(sessionId, event.getEventId());
        }
        boolean isAllowSessionBookmarkCapacity = virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId());
        log.info("SessionServiceImpl getSessionInfoById isAllowSessionBookmarkCapacity {} and register counts {} ",isAllowSessionBookmarkCapacity,registerCounts);
        dto.setSessionBookmarkCapacityReached(isAllowSessionBookmarkCapacity && session.getCapacity() != 0 && registerCounts >= session.getCapacity());
        log.info("SessionServiceImpl getSessionInfoById sessionBookmarkCapacityReached {} ",dto.isSessionBookmarkCapacityReached());

        // set subtitleUrl
        Optional<SessionDetails> optionalSessionDetails = sessionDetailsService.getSessionDetailsBySession(session);
        if (optionalSessionDetails.isPresent()) {
            SessionDetails sessionDetails = optionalSessionDetails.get();
            dto.setHideVideoControls(sessionDetails.getHideVideoControls());
            dto.setRecordSession(sessionDetails.isRecordSession());
            if (expand.contains("CHIME-CONFIG") && EnumSessionFormat.WORKSHOP.equals(session.getFormat()) ) {
                dto.setChimeConfig(org.apache.commons.lang3.StringUtils.isNotBlank(sessionDetails.getChimeConfigJson())
                        ? ChimeConfigDto.convertJSONToObject(sessionDetails.getChimeConfigJson()) : new ChimeConfigDto());
            }
            dto.setAllowedMinutesToJoinLate(sessionDetails.getAllowedMinutesToJoinLate());
            dto.setQnAPrivate(sessionDetails.isQnAPrivate());
            dto.setEnableAttendeeList(sessionDetails.isEnableAttendeeList());
            dto.setPostSessionCallToActionJson(sessionDetails.getPostSessionCallToActionJson() != null ? CommonUtil.getDtoPostCallToActionJson(sessionDetails.getPostSessionCallToActionJson()) : new PostSessionCallToActionDto());
            dto.setCaptions(null != sessionDetails.getCaptions() ? CaptionsDto.convertJSONToObject(sessionDetails.getCaptions()) : new CaptionsDto());
            dto.setEnableSessionWaitingMedia(sessionDetails.isEnableSessionWaitingMedia());
            dto.setWaitingMedia(null != sessionDetails.getWaitingMedia() ? WaitingMediaDto.convertJSONToObject(sessionDetails.getWaitingMedia()) : new WaitingMediaDto());
            dto.setSelfCheckInAllowed(sessionDetails.isSelfCheckInAllowed());
            dto.setSurveySessionStart(sessionDetails.isSurveySessionStart());
            dto.setEnableVideoCaption(sessionDetails.isEnableVideoCaption());
        }
        dto.setCustomAttributeDisplayDto(customFormAttributeService.getCustomAttributeDetails(session.getSessionAttributeId(), event, user, AttributeType.SESSION));
        return dto;
    }

    private List<IdNameDto> getIdNameDto(List<KeyValue> keyValue, EnumKeyValueType tag) {
        return keyValue.stream().filter(e -> tag.equals(e.getType()))
                .map(IdNameDto::new).collect(toList());
    }

    @Override
    public DataTableResponse getSessionInformationBySessionIds(List<Long> interestedSessionIds, Event event, String expand, User loggedInUser, String sessionFormat, boolean showPastAndUpcoming, boolean isAdminStaff, String filterDate) {
        Page<Session> sessionsPage = getAllSessionPageByIdIn(interestedSessionIds, event, sessionFormat, loggedInUser, showPastAndUpcoming, isAdminStaff, filterDate);
        return getSessionDataTable(event, loggedInUser, expand, sessionsPage);
    }

    @Override
    public DataTableResponse getSpeakerTalks(Event event,
                                             User user,
                                             String search,
                                             Pageable pageable,
                                             Boolean pastSessions,
                                             String expand, boolean isAdminOrStaff) {
        Page<Session> sessionsPage = sessionSpeakerService.getSessionByEventIdAndSpeakerUserId(event,
                user.getUserId(), search, pageable, pastSessions, isAdminOrStaff);
        return getSessionDataTable(event, user, expand, sessionsPage);
    }

    private Page<Session> getAllSessionPageByIdIn(List<Long> sessionIds, Event event, String sessionFormat, User user, boolean showPastAndUpcoming, boolean isAdminOrStaff, String filterDate) {
        if (isEmpty(sessionIds)) {
            return new PageImpl<>(emptyList());
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sessionFormat)) {
            return sessionRepoService.findAllByEventIdAndSessionFormatAndIdIn(event, sessionIds, PageRequest.of(0, Integer.MAX_VALUE), sessionFormat, showPastAndUpcoming, isAdminOrStaff, user, filterDate);
        }
        return sessionRepoService.findAllByEventIdAndIdIn(event, sessionIds, PageRequest.of(0, Integer.MAX_VALUE), user, showPastAndUpcoming, isAdminOrStaff, filterDate);
    }

    @Override
    public DataTableResponse getUserRegisteredSessionsAndScheduleMeetings(Event event, User user, boolean showPastAndUpcoming, boolean isAdminOrStaff, String filterDate, SessionFilter sessionFilter, String expand,String equivalentTimeZone,boolean isCustomEvent) {

        List<Long> sessionIds = sessionRepo.getListOfSessionIds(event.getEventId(), user.getUserId());
        Page<Session> sessionsPage = new PageImpl<>(emptyList());
        List<UserSessionAndScheduledMeetingDto> userRegisteredSessions = new ArrayList<>();
        if (!sessionIds.isEmpty()) {
           sessionsPage = sessionRepoService.findAllByEventIdAndIdIn(event, sessionIds, PageRequest.of(0, Integer.MAX_VALUE), user, showPastAndUpcoming, isAdminOrStaff, filterDate);
           userRegisteredSessions = getSessionsAndScheduleMeetingsDataTable(event, user, expand, sessionsPage);
            log.info("get user registered sessions {} and eventId {} and userId {}", sessionIds, event.getEventId(), user.getUserId());
        }
        List<MeetingOrigin> meetingOrigins=getScheduleMeetings(event,user.getUserId());
        Page<MeetingSchedule> meetingSchedules= meetingScheduleService.getAllMeetingScheduleByUserIdAndEventIdAndStatus(user.getUserId(), event.getEventId(), meetingOrigins, PageRequest.of(0, Integer.MAX_VALUE));
        List<Long> meetingScheduleIds = meetingSchedules.getContent().stream().map(MeetingSchedule::getId).collect(Collectors.toList());
        log.info("get user meeting schedules {} and eventId {} and userId {}",meetingScheduleIds, event.getEventId(), user.getUserId());
        List<AttendeeProfileDto> attendeeProfileDtoList = getAttendeesDataForMeeting(meetingSchedules, event);
        List<UserSessionAndScheduledMeetingDto> userScheduledMeetings=prepareMeetingSchedules(user.getUserId(), equivalentTimeZone, meetingSchedules, attendeeProfileDtoList, event, isCustomEvent);
        List<UserSessionAndScheduledMeetingDto> userSessionAndScheduledMeetings = new ArrayList<>();
        userSessionAndScheduledMeetings.addAll(userScheduledMeetings);
        userSessionAndScheduledMeetings.addAll(userRegisteredSessions);
        userSessionAndScheduledMeetings.sort(Comparator.comparing(UserSessionAndScheduledMeetingDto::getTimeToSort));
        log.info("get user registered sessions and meeting schedules {} and eventId {} and userId {}", userSessionAndScheduledMeetings.size(), event.getEventId(), user.getUserId());
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(userSessionAndScheduledMeetings.size());
        dataTableResponse.setRecordsFiltered(userSessionAndScheduledMeetings.size());
        dataTableResponse.setData(userSessionAndScheduledMeetings);
        return dataTableResponse;
    }

    private List<MeetingOrigin> getScheduleMeetings(Event event, Long userId) {
        List<MeetingOrigin> meetingOrigins = new ArrayList<>();
        if(meetingScheduleService.isAllowAttendeeToScheduleMeetingByEventTicketType(event,userId)){
            meetingOrigins.addAll(Arrays.asList(MeetingOrigin.values()));
        } else {
            meetingOrigins.add(MeetingOrigin.ADMIN_MEETING_REQUEST);
        }
        return meetingOrigins;
    }

    private List<AttendeeProfileDto> getAttendeesDataForMeeting(Page<MeetingSchedule> meetingSchedules, Event event) {
        List<Long> userIds = meetingSchedules.stream().flatMap(c -> Stream.of(c.getReceiverUserId(), c.getSenderUserId())).distinct().collect(Collectors.toList());
        List<String> attendeeIds = userIds.stream().map(ids -> RONeptuneAttendeeDetailService.attendeeId(ids.toString())).collect(Collectors.toList());
        return attendeeProfileService.getAttendeesCompanyAndTitle(String.valueOf(event.getEventId()), attendeeIds);
    }

    private List<UserSessionAndScheduledMeetingDto> getSessionsAndScheduleMeetingsDataTable(Event event, User loggedInUser, String expand, Page<Session> sessionsPage) {
        SessionExpandable expandable = new SessionExpandable(expand, sessionsPage, loggedInUser, event).invoke();
        List<Long> sessionIds = sessionsPage.getContent().stream().map(Session::getId).collect(toList());
        Map<Long, Boolean> finalMap = new HashMap<>();
        if(null != loggedInUser && (!CollectionUtils.isEmpty(sessionIds))) {
            List<Object[]>  listOfUserSession = userSessionRepoService.getCheckInSessionByUserIdAndSessionId(sessionIds, loggedInUser.getUserId());
            if (!CollectionUtils.isEmpty(listOfUserSession)) {
                Map<Long, Boolean> map = listOfUserSession.stream().collect(Collectors.toMap(obj -> (Long) obj[0], obj -> (Boolean) obj[1], (first, second) -> first));
                finalMap.putAll(map);
            }

        }
        return sessionsPage
                        .getContent()
                        .stream()
                        .map(e -> new UserSessionAndScheduledMeetingDto(e,
                                event,
                                expandable.getSessionSpeakers().get(e.getId()),
                                new SessionStatsDto(e.getId(), expandable.getRegisterSessionStats().get(e.getId()), expandable.getInterestedSessionStats().get(e.getId()), expandable.getSessionSpeakerStats().get(e.getId()), expandable.getAttendeeCount().get(e.getId()),expandable.getAttendeeBookmarkCount().get(e.getId())),
                                expandable.getCurrentUserSessionIdAndEventTicketIdMap().get(e.getId()),
                                expandable.getSessionSponsors(e.getSponsorExhibitorJson()),
                                expandable.getSessionAssetsDetail().get(e.getId()),
                                expandable.getSessionTagsAndTracks(e.getId()),
                                expandable.getSessionBookmarkCapacity().get(e.getId()),
                                expandable.getSessionBookmarked().get(e.getId()),
                                finalMap.get(e.getId()),
                                sessionDetailsService.getSessionDetailsBySession(e).isPresent() ? sessionDetailsService.getSessionDetailsBySession(e).get() : null))
                        .collect(toList());
    }
    private Map<Long, User> getUserMapFromUserId(List<Long> userIds) {
        List<User> users = roUserService.getListOfUsersByUserIds(userIds);
        return users.stream().collect(Collectors.toMap(User::getUserId, user -> user));
    }
    private List<UserSessionAndScheduledMeetingDto> prepareMeetingSchedules(Long userId, String equivalentTimezone, Page<MeetingSchedule> meetingSchedules, List<AttendeeProfileDto> attendeeProfileDtos, Event event, boolean isCustomEvent) {
        List<MeetingSchedule> meetingDetails = meetingSchedules.getContent();
        Map<Long,String> holderCompanyName = new HashMap<>();
        Map<Long, ExhibitorStaffDTO> exhibitorDetailsById = new HashMap<>();
        List<Long> exhibitorStaffUserId = new ArrayList<>();
        Map<Long,List<Long>> staffByExpo = new HashMap<>();
        Map<Long, User> userDetail = null;

        if (!meetingDetails.isEmpty()) {
            List<Long> userIds = meetingDetails.stream().flatMap(c -> Stream.of(c.getReceiverUserId(), c.getSenderUserId()))
                    .collect(Collectors.toList());
            userDetail = getUserMapFromUserId(userIds);
            if(isCustomEvent){
                holderCompanyName = attendeeProfileService.mapHolderCompanyName(userIds, event);
                List<ExhibitorStaffDTO> exhibitorStaffs = staffService.findAllExhibitorUserIdsByEventId(event.getEventId());
                exhibitorStaffUserId = exhibitorStaffs.stream().map(ExhibitorStaffDTO::getUserId).collect(Collectors.toList());
                staffByExpo = exhibitorStaffs.stream().collect(Collectors.groupingBy(ExhibitorStaffDTO::getExhibitorId,Collectors.mapping(ExhibitorStaffDTO::getUserId,Collectors.toList())));
                exhibitorDetailsById = exhibitorStaffs.stream().collect(Collectors.toMap(ExhibitorStaffDTO::getExhibitorId, Function.identity(), (o, n) ->o));
            }
        }

        Map<Long, User> finalUserDetail = userDetail != null ? userDetail : Collections.emptyMap();
        Map<Long, String> finalHolderCompanyName = holderCompanyName;
        List<Long> finalExhibitorStaffUserId = exhibitorStaffUserId;
        Map<Long, ExhibitorStaffDTO> finalExhibitorDetailsById = exhibitorDetailsById;
        Map<Long, List<Long>> finalStaffByExpo = staffByExpo;
        return meetingDetails.stream().map(meetingSchedule ->{
            UserSessionAndScheduledMeetingDto userSessionAndScheduledMeetingDto = new UserSessionAndScheduledMeetingDto(meetingSchedule, equivalentTimezone, userId.equals(meetingSchedule.getSenderUserId()) ? finalUserDetail.get(meetingSchedule.getReceiverUserId()) : finalUserDetail.get(meetingSchedule.getSenderUserId()), attendeeProfileDtos);
            userSessionAndScheduledMeetingDto.setMeetingStartTime(isBlank(equivalentTimezone) ? DateUtils.getDateString(meetingSchedule.getMeetingStartTime(), Constants.DATE_FORMAT_WITH_AM_PM)  : TimeZoneUtil.getDateInLocal(meetingSchedule.getMeetingStartTime(),equivalentTimezone, Constants.DATE_FORMAT_WITH_AM_PM));
            userSessionAndScheduledMeetingDto.setMeetingEndTime(isBlank(equivalentTimezone) ? DateUtils.getDateString(meetingSchedule.getMeetingEndTime(), Constants.DATE_FORMAT_WITH_AM_PM) : TimeZoneUtil.getDateInLocal(meetingSchedule.getMeetingEndTime(),equivalentTimezone, Constants.DATE_FORMAT_WITH_AM_PM));
            if(isCustomEvent  && MeetingOrigin.EXPO_COMP_REPRESENTATIVE.equals(meetingSchedule.getOrigin()) && meetingSchedule.getExhibitorId() != null && meetingSchedule.getExhibitorId() > 0){
                if(finalHolderCompanyName.get(userSessionAndScheduledMeetingDto.getReceiver().getUserId()) != null){
                    userSessionAndScheduledMeetingDto.getReceiver().setHolderCompany(finalHolderCompanyName.get(userSessionAndScheduledMeetingDto.getReceiver().getUserId()));
                }
                if(finalExhibitorStaffUserId.contains(userSessionAndScheduledMeetingDto.getReceiver().getUserId())
                        && finalExhibitorDetailsById.get(meetingSchedule.getExhibitorId()) != null
                        && finalStaffByExpo.get(meetingSchedule.getExhibitorId()).contains(userSessionAndScheduledMeetingDto.getReceiver().getUserId())){
                    userSessionAndScheduledMeetingDto.getReceiver().setExpoName(finalExhibitorDetailsById.get(meetingSchedule.getExhibitorId()).getBoothName());
                }
            }

            return userSessionAndScheduledMeetingDto;
        }).collect(Collectors.toList());
    }

    @Override
    public DataTableResponse getMyRegisteredSessions(Event event, String expand, User loggedInUser, String sessionFormat, boolean showPastAndUpcoming, boolean isAdminOrStaff, String filterDate, SessionFilter sessionFilter) {
        if (isEmpty(sessionFilter.getTagOrTrackIds())) {
            sessionFilter.setTagOrTrackIds(singletonList(0L));
        }
        log.info("List My Registered Sessions of {} ",loggedInUser);

        List<Long> userSessionIds = sessionRepo.filterSessionByUserId(event.getEventId(), sessionFilter.getTagOrTrackIds(), sessionFilter.getSearch(), loggedInUser.getUserId());
        log.info("Filtered Register Sessions list {} ",userSessionIds);
        return getSessionInformationBySessionIds(userSessionIds, event, expand, loggedInUser, sessionFormat, showPastAndUpcoming, isAdminOrStaff, filterDate);
    }

    private class SessionExpandable {
        private User loggedInUser;
        private String expand;
        private Page<Session> sessionsPage;
        private Map<Long, List<SpeakerDTO>> finalSessionIdsSpeakers;
        private Map<Long, Long> interestedSessionStats;
        private Map<Long, Long> registerSessionStats;

        private Map<Long,Boolean> sessionBookmarkCapacity;
        private Map<Long, Boolean> sessionBookmarked;
        private Map<Long, Boolean> sessionWaitlisted;
        private Map<Long, List<Long>> currentUserSessionIdAndEventTicketIdMap;
        private Map<Long, Double> durationInSecond;
        private List<SponsorsDto> sponsorsDtos;
        private List<ExhibitorCarouselDetailDto> exhibitorCarouselDetailDtos;
        private Map<Long, List<SessionTagAndTrack>> finalSessionTagAndTracks;
        private Event event;
        private Session session;
        private Map<Long, List<SessionDetails>> sessionDetailsMap;
        private Map<Long, Long> sessionSpeakerCountMap;
        private Map<Long, List<MuxAssetDTO>> finalSessionAssets;
        private boolean analytics;
        private Map<Long, Long> attendeeCount;
        private Map<Long, Long> attendeeBookmarkCount;

        private SessionExpandable(String expand, Page<Session> sessionsPage, boolean analytics) {
            this.expand = expand;
            this.sessionsPage = sessionsPage;
            this.analytics = analytics;
        }

        private SessionExpandable(Session session, Event event, String expand, Page<Session> sessionsPage, boolean analytics) {
            this(event, expand, sessionsPage, analytics);
            this.session = session;
        }

        private SessionExpandable(Event event, String expand, Page<Session> sessionsPage, boolean analytics) {
            this(expand, sessionsPage, analytics);
            this.event = event;
        }

        private SessionExpandable(String expand, Page<Session> sessionsPage, User loggedInUser) {
            this.expand = expand;
            this.sessionsPage = sessionsPage;
            this.loggedInUser = loggedInUser;
        }

        private SessionExpandable(String expand, Page<Session> sessionsPage, User loggedInUser, Event event) {
            this(expand, sessionsPage, loggedInUser);
            this.event = event;
        }

        private Map<Long, List<SpeakerDTO>> getSessionSpeakers() {
            return finalSessionIdsSpeakers == null ? emptyMap() : finalSessionIdsSpeakers;
        }

        private Map<Long, List<MuxAssetDTO>> getSessionAssetsDetail() {
            return finalSessionAssets == null ? emptyMap() : finalSessionAssets;
        }

        public SessionExpandable invoke() {//NOSONAR
            List<Long> sessionIds = sessionsPage.getContent().stream().map(Session::getId).collect(toList());
            log.info("SessionServiceImpl getSessionIds {}",sessionIds);
            if (analytics) {
                expand = expand + "STATS,duration";
                List<IdCountDto> attendeeCountList = userSessionService.countSessionAttendeeBySessionIdIn(sessionIds);
                attendeeCount = getMap(attendeeCountList);
            }
            List<IdCountDto> attendeeBookmarkCountList = userSessionService.countSessionBookmarkedAttendeeBySessionIdInAndEventId(sessionIds, event.getEventId());
            attendeeBookmarkCount = getMap(attendeeBookmarkCountList);

            boolean isAllowSessionBookmarkCapacity = virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId());

            log.info("SessionServiceImpl getSessionInfoById isAllowSessionBookmarkCapacity {} ",isAllowSessionBookmarkCapacity);
            if (isAllowSessionBookmarkCapacity) {
                List<Long> workshopSessionIds = sessionsPage.getContent().stream().filter(e->e.getFormat().equals(EnumSessionFormat.WORKSHOP)).map(Session::getId).collect(toList());
                List<IdCountDto> registerCount;
                if(!CollectionUtils.isEmpty(workshopSessionIds)) {
                    registerCount = userSessionService.getSessionStatsForIdsIn(workshopSessionIds);
                    sessionIds.removeAll(workshopSessionIds);
                    registerCount.addAll(userSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds, event.getEventId()));
                    sessionIds.addAll(workshopSessionIds);
                }else{
                    registerCount = userSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds,event.getEventId());
                }
                registerSessionStats = getMap(registerCount);
                log.info("SessionServiceImpl getSessionInfoById registerSessionStats {} ",registerSessionStats);
                sessionBookmarkCapacity = sessionsPage.getContent().stream()
                        .collect(Collectors.toMap(
                                Session::getId,
                                sessionObj -> {
                                    if (registerSessionStats.containsKey(sessionObj.getId()) && sessionObj.getCapacity() != 0) {
                                        return registerSessionStats.get(sessionObj.getId()) >= sessionObj.getCapacity();
                                    } else {
                                        return false;
                                    }
                                }
                        ));
            } else {
                log.info("SessionServiceImpl getSessionInfoById session bookmark capacity reached is false for all the session of the event {}",event.getEventId());
                sessionBookmarkCapacity = sessionsPage.getContent().stream()
                        .collect(Collectors.toMap(
                                Session::getId,
                                sessionObj -> false
                        ));
            }
            log.info("SessionServiceImpl getSessionInfoById sessionBookmarkCapacity {} ",sessionBookmarkCapacity);

            if (StringUtils.isBlank(expand)) {
                return this;
            }

            Map<Long, List<SpeakerDTO>> sessionIdsSpeakers = null;
            Map<Long, List<SessionTagAndTrack>> sessionTagAndTracksMap = null;
            Map<Long, List<MuxAssetDTO>> sessionMuxAssetMap = null;

            if (expand.contains("duration")) {

                List<Long> sessionIdList = sessionsPage.getContent().stream()
                        .filter(e -> !SessionUtils.isAcceleventOrDirectUploadStreamProvider(e.getStreamProvider()))
                        .map(Session::getId).collect(toList());

                List<Long> muxSessionIdList = sessionsPage.getContent().stream()
                        .filter(e -> SessionUtils.isAcceleventOrDirectUploadStreamProvider(e.getStreamProvider()))
                        .map(Session::getId).collect(toList());

                List<IdDurationDto> durationList = new ArrayList<>();
                durationList.addAll(sessionDetailsService.getSessionDurationBySessionIds(sessionIdList));
                durationList.addAll(muxLivestreamAssetService.getSessionDurationForAnalyticsByIdsIn(muxSessionIdList));
                durationInSecond = getDurationMap(durationList);

                // For Workshop sessions use session length as their duration.
                Map<Long, Double> workshopDurations = sessionsPage.getContent().stream()
                        .filter(e -> EnumSessionFormat.WORKSHOP.equals(e.getFormat()))
                        .filter(e -> e.getStartTime() != null && e.getEndTime() != null)
                        .collect(Collectors.toMap(Session::getId, e -> VideoAnalyticsUtils.getSessionDuration(e.getStartTime().getTime(), e.getEndTime().getTime())));

                durationInSecond.putAll(workshopDurations);

                //If video duration is not present for any session, calculate it from start and end time.
                // This is happening for all the online video platforms like youtube, vimeo, wistia, workshop
                for (Session session : sessionsPage.getContent()) {
                    Long sessionId = session.getId();
                    Double duration = durationInSecond.get(sessionId);

                    double sessionTimeBasedDuration = 0.0;
                    // If actual session video time is higher than sessionEndTime - sessionStartTime then show the video total duration
                    // Else show the session time diff as duration.
                    // One more place this is majorly applicable when for workshop session is not recorded,
                    // When session is organised from the other video provider like WISTIA, YOUTUBE, VIMEO, FACEBOOK, ZOOM, VIDYARD, ZOOM_REDIRECT
                    if (session.getStartTime() != null && session.getEndTime() != null) {
                        sessionTimeBasedDuration  = VideoAnalyticsUtils.getSessionDuration(session.getStartTime().getTime(), session.getEndTime().getTime());
                    }
                    // Use session duration if video duration is null, 0, or less than session duration
                    if (duration == null || duration <= sessionTimeBasedDuration) {
                        durationInSecond.put(sessionId, sessionTimeBasedDuration);
                    }
                }
            }
            if (expand.contains(SPEAKER)) {
                sessionIdsSpeakers = sessionSpeakerService.getSessionSpeakerIdSessionDtoIds(sessionIds);
            }
            if (expand.contains("ASSETS")) {
                sessionMuxAssetMap = muxLivestreamAssetRepoService.getMuxAssetsMapByAssetTypeAndSessionIds(sessionIds, AssetType.SESSION_ASSET);
                Map<Long, List<MuxAssetDTO>> sessionWorkshopAssetMap = workshopRecordingAssetService.getWorkshopRecordingAssetsMapByVisibleStatusAndSessionIds(sessionIds);
                sessionMuxAssetMap.putAll(sessionWorkshopAssetMap);
            }

            if (expand.contains("SPONSOR")) {
                this.sponsorsDtos = sponsorsService.getAllSponsorsByEvent(event);
            }
            if (expand.contains("EXHIBITOR")) {
                this.exhibitorCarouselDetailDtos = exhibitorService.getAllExhibitorLogoForDisplay(event);
            }


            if (expand.contains("STATS")) {
                List<IdCountDto> interestedCount = userInterestedSessionService.getSessionStatsForIdsIn(sessionIds);
                List<IdCountDto> attendeeCountForHost = userSessionService.countSessionAttendeeBySessionIdIn(sessionIds);
                attendeeCount = getMap(attendeeCountForHost);

                if(expand.contains(SESSION_EXPAND.CHECKIN_ATTENDEE_COUNT)) {
                    List<Long> sessionIdList = new ArrayList<>(sessionIds);
                    List<Long> netWorkingSessionIds = sessionsPage.getContent().stream().filter(e->e.getFormat().equals(MEET_UP)).map(Session::getId).collect(toList());
                    sessionIdList.removeAll(netWorkingSessionIds);
                    List<IdCountDto> attendeeCountList = Optional.ofNullable(userSessionRepoService.countSessionAttendeeByEventTicketIdAndSessionIdIn(sessionIdList)).orElse(new ArrayList<>());
                    if(!CollectionUtils.isEmpty(netWorkingSessionIds)) {
                        List<IdCountDto> netWorkingAttendeeIds = userSessionRepoService.countSessionAttendeeByNetworkingSessionIdIn(netWorkingSessionIds);
                        if(CollectionUtils.isEmpty(sessionIdList)) {
                            attendeeCountList =netWorkingAttendeeIds;
                        } else {
                            attendeeCountList.addAll(netWorkingAttendeeIds);
                        }
                    }
                    attendeeCount = getMap(attendeeCountList);
                }
                if(!isAllowSessionBookmarkCapacity) {
                    List<IdCountDto> registerCounts = userSessionService.getSessionStatsForIdsIn(sessionIds);
                    registerSessionStats = getMap(registerCounts);
                }
                interestedSessionStats = getMap(interestedCount);
            }
            if (loggedInUser != null && expand.contains("currentUserRegisteredEventTicketId")) {
                log.info("expand contains currentUserRegisteredEventTicketId | logInUser {} | sessionIds {} ", loggedInUser.getUserId(), sessionIds);
                currentUserSessionIdAndEventTicketIdMap = userSessionService.findRegisteredEventTicketIdByUserAndSessionId(loggedInUser.getUserId(), sessionIds);
            }
            if (loggedInUser != null) {
                sessionBookmarked = userSessionService.findBookmarkedSessionByUserIdAndSessionId(loggedInUser.getUserId(), sessionIds);
                sessionWaitlisted = userSessionService.findWaitlistedSessionByUserIdAndSessionId(loggedInUser.getUserId(), sessionIds);
            }

            if (expand.contains("registerCount")) {
                List<IdCountDto> registerCounts = userSessionService.getSessionStatsForIdsIn(sessionIds);
                registerSessionStats = getMap(registerCounts);
            }
            if (expand.contains("TAG") || expand.contains("TRACK")) {
                List<SessionTagAndTrack> sessionTagAndTracks = sessionTagAndTrackService.findBySessionIds(sessionIds);
                sessionTagAndTracksMap = sessionTagAndTracks.stream().collect(Collectors.groupingBy(SessionTagAndTrack::getSessionId));
            }

            if (expand.contains(EXPAND_SESSION_SPEAKER_COUNT)) {
                List<IdCountDto> sessionSpeakerCount = sessionSpeakerService.getSessionIdAndSessionSpeakerCount(sessionIds);
                sessionSpeakerCountMap = getMap(sessionSpeakerCount);
            }

            List<SessionDetails> sessionDetails = sessionDetailsRepoService.getAllSessionDetailsByEventId(event.getEventId());
            sessionDetailsMap = sessionDetails.stream().collect(Collectors.groupingBy(e-> e.getSession().getId()));

            finalSessionIdsSpeakers = sessionIdsSpeakers;
            finalSessionTagAndTracks = sessionTagAndTracksMap;
            finalSessionAssets = sessionMuxAssetMap;
            return this;
        }


        private List<SponsorsDto> getSessionSponsors(String sponsorExhibitorJson) {
            if (!CollectionUtils.isEmpty(sponsorsDtos) && StringUtils.isNotBlank(sponsorExhibitorJson)) {
                List<Long> sponsorIds = sponsorsService.getSponsorIdsFromSponsorExhibitorJson(sponsorExhibitorJson);
                return sponsorsDtos.stream().filter(s -> sponsorIds.contains(s.getId())).collect(Collectors.toList());
            } else {
                return Collections.emptyList();
            }
        }

        private List<ExhibitorCarouselDetailDto> getExhibitors(String sponsorExhibitorJson) {
            if (!CollectionUtils.isEmpty(exhibitorCarouselDetailDtos) && StringUtils.isNotBlank(sponsorExhibitorJson)) {
                List<Long> exhibitorIds = sponsorsService.getExhibitorIdsFromSponsorExhibitorJson(sponsorExhibitorJson);
                return exhibitorCarouselDetailDtos.stream().filter(s -> exhibitorIds.contains(s.getId())).collect(Collectors.toList());
            } else {
                return Collections.emptyList();
            }
        }

        private List<KeyValue> getSessionTagsAndTracks(Long sessionId) {
            if (!CollectionUtils.isEmpty(finalSessionTagAndTracks) && finalSessionTagAndTracks.get(sessionId) != null) {
                List<Long> keyValueIds = finalSessionTagAndTracks.get(sessionId).stream().map(e -> e.getTagOrTrackId()).collect(Collectors.toList());
                return keyValueService.findAllByIds(keyValueIds);
            } else {
                return Collections.emptyList();
            }
        }

        private Map<Long, Long> getSessionSpeakerStats() {
            return sessionSpeakerCountMap != null ? sessionSpeakerCountMap : emptyMap();
        }

        private Map<Long, Long> getMap(List<IdCountDto> idCountDtos1) {
            return idCountDtos1.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount));
        }

        private Map<Long, Double> getDurationMap(List<IdDurationDto> idDurationDto) {
            return idDurationDto.stream().collect(Collectors.toMap(IdDurationDto::getId, IdDurationDto::getDuration));
        }

        private Map<Long, List<Long>> getCurrentUserSessionIdAndEventTicketIdMap() {
            return currentUserSessionIdAndEventTicketIdMap == null ? emptyMap() : currentUserSessionIdAndEventTicketIdMap;
        }

        public Map<Long, Long> getAttendeeCount() {
            return attendeeCount != null ? attendeeCount : emptyMap();
        }

        public Map<Long, Long> getAttendeeBookmarkCount() {
            return attendeeBookmarkCount!= null ? attendeeBookmarkCount : emptyMap();
        }

        private Map<Long, Long> getInterestedSessionStats() {
            return interestedSessionStats != null ? interestedSessionStats : emptyMap();
        }

        public void setInterestedSessionStats(Map<Long, Long> interestedSessionStats) {
            this.interestedSessionStats = interestedSessionStats;
        }

        private Map<Long, Long> getRegisterSessionStats() {
            return registerSessionStats != null ? registerSessionStats : emptyMap();
        }



        public void setRegisterSessionStats(Map<Long, Long> registerSessionStats) {
            this.registerSessionStats = registerSessionStats;
        }

        public Map<Long, Boolean> getSessionBookmarkCapacity() {
            return sessionBookmarkCapacity != null ? sessionBookmarkCapacity: emptyMap();
        }

        public void setSessionBookmarkCapacity(Map<Long, Boolean> sessionBookmarkCapacity) {
            this.sessionBookmarkCapacity = sessionBookmarkCapacity;
        }

        public Map<Long, Boolean> getSessionBookmarked() {
            return sessionBookmarked == null ? emptyMap() : sessionBookmarked;
        }

        public void setSessionBookmarked(Map<Long, Boolean> sessionBookmarked) {
            this.sessionBookmarked = sessionBookmarked;
        }

        public Map<Long, Boolean> getSessionWaitlisted() {
            return sessionWaitlisted == null ? emptyMap() : sessionWaitlisted;
        }

        public void setSessionWaitlisted(Map<Long, Boolean> sessionWaitlisted) {
            this.sessionWaitlisted = sessionWaitlisted;
        }

        private Map<Long, Double> getDurationInSecond() {
            return null != durationInSecond ? durationInSecond : emptyMap();
        }

        public void setDurationInSecond(Map<Long, Double> durationInSecond) {
            this.durationInSecond = durationInSecond;
        }

        public Map<Long, List<SessionDetails>> getSessionDetailsMap() {
            return sessionDetailsMap;
        }

        private boolean getSessionRecordBySession(long id) {
            if(!CollectionUtils.isEmpty(sessionDetailsMap) ) {
                List<SessionDetails> sessionDetails = sessionDetailsMap.get(id);
                if (sessionDetails != null) {
                    return sessionDetails.get(0).isRecordSession();
                }
            }
            return true;
        }

        private boolean enableWaitingMedia(long id) {
            if(!CollectionUtils.isEmpty(sessionDetailsMap) ) {
                List<SessionDetails> sessionDetails = sessionDetailsMap.get(id);
                if (sessionDetails != null) {
                    return sessionDetails.get(0).isEnableSessionWaitingMedia();
                }
            }
            return true;
        }
        private WaitingMediaDto getSessionWaitingMedia(long id) {
            if(!CollectionUtils.isEmpty(sessionDetailsMap) ) {
                List<SessionDetails> sessionDetails = sessionDetailsMap.get(id);
                if (sessionDetails != null) {
                    return null != sessionDetails.get(0).getWaitingMedia() ? WaitingMediaDto.convertJSONToObject(sessionDetails.get(0).getWaitingMedia()) : new WaitingMediaDto();
                }
            }
            return new WaitingMediaDto();
        }

        private boolean getHideVideoControlsBySession(long id) {
            if(!CollectionUtils.isEmpty(sessionDetailsMap) ) {
                List<SessionDetails> sessionDetails = sessionDetailsMap.get(id);
                if (sessionDetails != null) {
                    return sessionDetails.get(0).getHideVideoControls();
                }
            }
            return false;
        }
    }


    @Override
    @Transactional
    public Session updateDirectUploadIdAndFile(Long id, String streamUrl, StreamProvider streamProvider, Event event, String playBackRestrictionToken, String thumbnailRestrictionToken) {
        Session session = sessionRepoService.getSessionByIdWithoutCache(id, event);
        if (EnumSessionFormat.WORKSHOP.equals(session.getFormat())) {
            streamProvider = null;
        } else if (null == session.getStreamProvider()) {
            streamProvider = DIRECT_UPLOAD;
        } else {
            streamProvider = session.getStreamProvider();
        }
        if (StringUtils.isNotBlank(session.getStreamUrl()) && DIRECT_UPLOAD.equals(streamProvider)) {
            updateSessionWithStreamDetails(session, streamUrl, playBackRestrictionToken, thumbnailRestrictionToken);
            sessionRepoService.save(session);
        } else if (StringUtils.isBlank(session.getStreamUrl())) {
            GAUtils.handleRtmpTable(session, new RtmpDto(streamUrl, streamProvider));
            updateSessionWithStreamDetails(session, streamUrl, playBackRestrictionToken, thumbnailRestrictionToken);
            sessionRepoService.save(session);
        }
        return  session;
    }

    private void updateSessionWithStreamDetails(Session session, String streamUrl, String playBackRestrictionToken, String thumbnailRestrictionToken) {
        session.setStreamUrl(streamUrl);
        session.setPlayBackRestrictionToken(playBackRestrictionToken);
        session.setThumbnailRestrictionToken(thumbnailRestrictionToken);
    }

    @Override
    public MuxAssetDTO findDefaultPlayBackForSession(Long sessionId, AssetType assetType) {
        Optional<Session> session = sessionRepoService.findById(sessionId);
        MuxAssetDTO muxAssetDTO;
        if (session.isPresent() && EnumSessionFormat.WORKSHOP.equals(session.get().getFormat())) {
            muxAssetDTO = workshopRecordingAssetService.findDefaultPlayBackForWorkshopSession(sessionId);
            muxAssetDTO.setSessionId(sessionId);
            return muxAssetDTO;
        }
        muxAssetDTO = muxLivestreamAssetService.findDefaultPlayBackForSession(sessionId, assetType);
        muxAssetDTO.setSessionSubtitleDTOList(sessionSubtitleService.getSubtitleListByMuxIdAndSessionId(sessionId,muxAssetDTO.getId()));
        muxAssetDTO.setSessionId(sessionId);
        return muxAssetDTO;
    }

    @Override
    public List<MuxAssetDTO> getSessionPlayBacksForDisplaySide(Long sessionId, Event event) {
        String format = sessionRepoService.findFormatBySessionId(sessionId, event.getEventId());
        List<MuxAssetDTO> muxAssetDTOs = new ArrayList<>();
        if (StringUtils.isNotBlank(format) && EnumSessionFormat.WORKSHOP.name().equals(format)) {
            List<WorkshopRecordingAssetDetails> workshopRecordingAssetDetails = workshopRecordingAssetService.getVisibleAssetsBySessionIdAndEventId(sessionId, event.getEventId());
            workshopRecordingAssetDetails.forEach(asset -> muxAssetDTOs
                    .add(new MuxAssetDTO(asset)));
        }
        List<MUXLivestreamAssetDetails> assetDetails = muxLivestreamAssetService.getVisibleAssetsBySessionIdAndEventId(sessionId, event.getEventId());
        List<Long> muxIds = assetDetails.stream()
                .map(MUXLivestreamAssetDetails::getId)
                .collect(Collectors.toList());
        Map<Long,List<SessionSubtitleDTO>> muxIdAndSubtitleListMap = sessionSubtitleService.getMapOfMuxIdAndSubtitleListByMuxIdListAndSessionId(muxIds, List.of(sessionId));
        assetDetails.forEach(asset -> muxAssetDTOs
                .add(new MuxAssetDTO(asset, muxIdAndSubtitleListMap.get(asset.getId()))));

        return muxAssetDTOs;
    }

    @Override
    public List<MuxAssetDTO> getSessionPlayBacks(Long sessionId, Event event) {
        String format = sessionRepoService.findFormatBySessionId(sessionId, event.getEventId());
        List<MuxAssetDTO> muxAssetDTOs = new ArrayList<>();
        if (StringUtils.isNotBlank(format) && EnumSessionFormat.WORKSHOP.name().equals(format)) {
            List<WorkshopRecordingAssetDetails> workshopRecordingAssetDetails = workshopRecordingAssetService.findBySessionIdAndEventId(sessionId, event.getEventId());
            workshopRecordingAssetDetails.forEach(asset -> muxAssetDTOs
                    .add(new MuxAssetDTO(asset)));
        }
        List<MUXLivestreamAssetDetails> assetDetails = muxLivestreamAssetService.findBySessionIdAndEventId(sessionId, event.getEventId());
        List<Long> muxIds = assetDetails.stream()
                .map(MUXLivestreamAssetDetails::getId)
                .collect(Collectors.toList());
        Map<Long,List<SessionSubtitleDTO>> muxIdAndSubtitleListMap = sessionSubtitleService.getMapOfMuxIdAndSubtitleListByMuxIdListAndSessionId(muxIds, List.of(sessionId));

        assetDetails.forEach(asset -> muxAssetDTOs
                .add(new MuxAssetDTO(asset, muxIdAndSubtitleListMap.get(asset.getId()))));

        return muxAssetDTOs;
    }

    @Override
    public void setDefaultPlayBackForSession(Long id, Long playbackId, Event event) {
        Session session = sessionRepoService.getSessionById(id, event);
        if (session.getEndTime() != null && new Date().before(session.getEndTime())) {
            throw new NotAcceptableException(CAN_NOT_SET_DEFAULT_PLAYBACK);
        }
        if(EnumSessionFormat.WORKSHOP.equals(session.getFormat())) {
            workshopRecordingAssetService.markAssetAsDefaultPlayBackForWorkshopSession(session, playbackId);
        } else {
            muxLivestreamAssetService.markAssetAsDefaultPlayBackForSession(session, playbackId, AssetType
                    .SESSION_ASSET);
        }
    }


    @Override
    public SessionDetailsAnalytics getSessionsAnalyticsById(Long sessionId, String expand, Event event) {
        log.info("Start getSessionsAnalyticsById for sessionId {}, expand {}, eventId {} ", sessionId, expand, event.getEventId());
        Session session = sessionRepoService.getSessionById(sessionId, event);
        PageImpl<Session> page = new PageImpl<>(singletonList(session));
        SessionExpandable expandable = new SessionExpandable(session, event, expand, page, false).invoke();

        SessionDetailsAnalytics sessionDetailsAnalytics = new SessionDetailsAnalytics(
                session,
                event
        );
        log.info("End getSessionsAnalyticsById for sessionId {}, expand {}, eventId {} ", sessionId, expand, event.getEventId());
        return sessionDetailsAnalytics;
    }

    @Override
    public DataTableResponse getSessionsAnalytics(String search,
                                                  String expand,
                                                  Event event,
                                                  Pageable pageable) {
        Page<Session> sessionsPage  = sessionRepoService.getAllHostSessionByEventId(event,search,pageable,false, null,Collections.emptyList(),new ArrayList<>(),false,false);
        SessionExpandable expandable = new SessionExpandable(event, expand, sessionsPage, true).invoke();
        List<SessionAnalyticsDto> sessionDTOS =
                sessionsPage
                        .getContent()
                        .stream()
                        .map(e -> new SessionAnalyticsDto(e,
                                        event,
                                        expandable.getRegisterSessionStats().get(e.getId()),
                                        expandable.getAttendeeBookmarkCount().get(e.getId()),
                                        expandable.getAttendeeCount().get(e.getId()),
                                        expandable.getDurationInSecond().get(e.getId()),
                                        virtualEventSessionLoggingService.getUserTotalDocumentDownloadBySessionId(event.getEventId(),e.getId()),
                                        virtualEventSessionLoggingService.findUniqueVisitorsWithDocumentsBySessionId(event.getEventId(),e.getId()),
                                        virtualEventSessionLoggingService.uniqueDocumentDownloadsBySessionId(event.getEventId(),e.getId())
                                //set download and unique download
                                )
                        )
                        .collect(toList());

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(sessionsPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(sessionsPage.getContent().size());
        dataTableResponse.setData(sessionDTOS);
        return dataTableResponse;
    }

    @Override
    public List<SessionAnalyticsDto> getSessionsOverviewReportData(Event event) {
        Page<Session> sessionsPage = sessionRepoService.getAllHostSessionByEventId(event, null, PageRequest.of(0, Integer.MAX_VALUE), false, null, Collections.emptyList(),new ArrayList<>(),false,false);
        String exapnds = "duration,registerCount,watchTime" ;
        SessionExpandable expandable = new SessionExpandable(event, exapnds, sessionsPage, false).invoke();

        return sessionsPage
                        .getContent()
                        .stream()
                        .map(e -> new SessionAnalyticsDto(e,
                                        event,
                                        expandable.getAttendeeBookmarkCount().get(e.getId()),
                                        expandable.getRegisterSessionStats().get(e.getId()),
                                        expandable.getAttendeeCount().get(e.getId()),
                                        expandable.getDurationInSecond().get(e.getId()),
                                virtualEventSessionLoggingService.getUserTotalDocumentDownloadBySessionId(event.getEventId(),e.getId()),
                                virtualEventSessionLoggingService.findUniqueVisitorsWithDocumentsBySessionId(event.getEventId(),e.getId()),
                                virtualEventSessionLoggingService.uniqueDocumentDownloadsBySessionId(event.getEventId(),e.getId()),
                                virtualEventSessionLoggingService.docListDownloadedBySessionId(event.getEventId(),e.getId())
                                )
                        )
                        .collect(toList());
    }

    @Override
    @Async
    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    public void updateSessionTicketTypes(TicketingType ticketingType, List<Long> ticketingTypeIds, Event event, Boolean isNewTicket) {
        List<Session> sessionList = sessionRepoService.findSessionByEventId(event);
        if (isNewTicket && null != ticketingTypeIds) {
            ticketingTypeIds.remove(ticketingType.getId());
        }
        sessionList.forEach(session -> {
            List<Long> allowedEventTicketTypeIds = Arrays.stream(session.getTicketTypesThatCanBeRegistered().split(STRING_COMMA)).filter(s -> StringUtils.isNotBlank(session.getTicketTypesThatCanBeRegistered())).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            if (isNewTicket && allowedEventTicketTypeIds.containsAll(ticketingTypeIds)) {
                allowedEventTicketTypeIds.add(ticketingType.getId());
            }
            allowedEventTicketTypeIds = handleTicketTypesThatCanBeRegistered(session, allowedEventTicketTypeIds, null);
            session.setTicketTypesThatCanBeRegistered(convertLongListToCommaSeparated(allowedEventTicketTypeIds));
            if(!CollectionUtils.isEmpty(allowedEventTicketTypeIds)){
                List<Long> recurringTicketTypes = ticketingTypeRepository.findTicketTypeIdsByCreatedFroms(allowedEventTicketTypeIds);
                if (!CollectionUtils.isEmpty(recurringTicketTypes)) {
                    log.info("Found recurring ticket types {} for selected ticket types {} for session : {}", recurringTicketTypes, allowedEventTicketTypeIds, session.getId());
                    String registeredTicketTypes = session.getTicketTypesThatCanBeRegistered();
                    Set<String> uniqueTicketTypes = new HashSet<>(Arrays.asList(registeredTicketTypes.split(STRING_COMMA)));
                    uniqueTicketTypes.addAll(recurringTicketTypes.stream().map(String::valueOf).collect(Collectors.toSet()));
                    registeredTicketTypes = String.join(STRING_COMMA, uniqueTicketTypes);
                    session.setTicketTypesThatCanBeRegistered(registeredTicketTypes);
                }
            }

        });
        sessionRepoService.saveAll(sessionList);
    }

    @Override
    @Async
    @Transactional
    public void updateTicketTypesThatCanBeRegistered(long ticketingTypeId, Event event) {
        List<Session> sessionList = sessionRepoService.findSessionByEventId(event);

        sessionList.forEach(session -> {
            List<Long> allowedEventTicketTypeIds = Arrays.stream(session.getTicketTypesThatCanBeRegistered().split(STRING_COMMA)).filter(s -> StringUtils.isNotBlank(session.getTicketTypesThatCanBeRegistered())).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            if (allowedEventTicketTypeIds.contains(ticketingTypeId)) {
                allowedEventTicketTypeIds.remove(ticketingTypeId);
                session.setTicketTypesThatCanBeRegistered(convertLongListToCommaSeparated(allowedEventTicketTypeIds));
            }
        });
        sessionRepoService.saveAll(sessionList);
    }

    @Override
    public List<Session> findSessionByEventId(Event event) {
        return sessionRepoService.findSessionByEventId(event);
    }

    @Override
    public UploadSessionResponseContainer saveOrUpdateParseSessionCSV(MultipartFile multiPartFile, Event event, User user, Map<String, String> languageMap) throws IOException {
        UploadSessionResponseContainer response = new UploadSessionResponseContainer();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());

        try (CSVReader cr = new CSVReader(new BufferedReader(new InputStreamReader(multiPartFile.getInputStream())))) {
            UploadSessionDto invalidSessions = null;
            String errorMessage = null;
            String[] header = cr.readNext();

            if (header.length < 14 || Arrays.stream(header).anyMatch(Objects::isNull)) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_HEADER_NOT_CORRECT);
            }

            boolean hasSessionId = header[0] != null && header[0].trim().equalsIgnoreCase(SESSION_CSV_ID) ? true : false;
            int customHeaderCountFrom = hasSessionId ? 15 : 14;

            // normalize headers (lowercase + trim)
            List<String> customHeaders = Arrays.stream(header, customHeaderCountFrom, header.length)
                    .filter(Objects::nonNull)
                    .map(h -> h.trim().toLowerCase())
                    .collect(toList());

            List<CustomFormAttribute> customFormAttributes  = customFormAttributeService
                    .getCustomFormAttributeOfEvent(event, user, null, AttributeType.SESSION);
            // normalize attribute names (lowercase + trim)
            List<String> customAttributeNames = customFormAttributes
                    .stream()
                    .filter(CustomFormAttribute::isEnabled)
                    .map(CustomFormAttribute::getName)
                    .filter(Objects::nonNull)
                    .map(n -> n.trim().toLowerCase())
                    .collect(toList());

            // find invalid headers
            List<String> invalidCustomHeaders = customHeaders.stream()
                    .filter(h -> !customAttributeNames.contains(h))
                    .collect(toList());

            // if custom attribute headers are invalid, throw exception
            if (!invalidCustomHeaders.isEmpty()) {
                String exceptionErrorMessage = NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_CUSTOM_HEADER_NOT_CORRECT.getErrorMessage().replace("${headers}", String.join(", ", invalidCustomHeaders));
                NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_CUSTOM_HEADER_NOT_CORRECT.setErrorMessage(exceptionErrorMessage);
                NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_CUSTOM_HEADER_NOT_CORRECT.setDeveloperMessage(exceptionErrorMessage);
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_CUSTOM_HEADER_NOT_CORRECT);
            }

            log.info("SessionServiceImpl || parseSessionCSV || eventId {} ",null != event?event.getEventId():null);
            List<Session> existingSessions = sessionRepoService.findSessionByEventId(event);
            Map<Long, Session> existingSessionsMap = (hasSessionId) ? existingSessions.stream().collect(Collectors.toMap(Session::getId, Function.identity())) : null;
            log.info("SessionServiceImpl || parseSessionCSV || After existingSessions || eventId {} ",null != event?event.getEventId():null);

            List<Session> mainStageSessions = existingSessions.stream()
                    .filter(session -> session.getFormat().equals(MAIN_STAGE)
                            && ((!EnumSessionStatus.DRAFT.equals(session.getStatus())) || (session.getStartTime() != null && session.getEndTime() != null))).collect(toList());
            if (isValidCSVHeader(header, hasSessionId)) {

                // Fetch all speakers for the event once for batch processing (optimized to fetch only email and ID)
                Map<String, Long> eventSpeakerEmailIdMap = createEventSpeakerEmailIdMap(event);

                String[] nextItem;
                while ((nextItem = cr.readNext()) != null) {
                    int nextItemLength=nextItem.length;
                    if (nextItemLength > 1) {
                        Integer capacity = null;
                        Long locationId = null;
                        Session validSession;
                        int indexCount = hasSessionId ? 1 : 0;
                        AtomicReference<Long> sessionId = new AtomicReference<>(0L);
                        String title =   StringUtils.isNotBlank(nextItem[indexCount]) ? nextItem[indexCount].trim() : Constants.STRING_EMPTY;
                        String sessionTypeFormat =  StringUtils.isNotBlank(nextItem[indexCount + 2]) && isValidSessionTypeFormat(nextItem[indexCount + 2].trim()) ? nextItem[indexCount + 2].trim() : SessionTypeFormat.HYBRID.name();

                        // Extract speaker data early so it's available for error cases
                        String primarySpeakers = nextItemLength > (indexCount + 12) && null != nextItem[indexCount + 12] ? nextItem[indexCount + 12].trim() : STRING_EMPTY;
                        String secondarySpeakers = nextItemLength > (indexCount + 13) && null != nextItem[indexCount + 13] ? nextItem[indexCount + 13].trim() : STRING_EMPTY;

                        // Dynamic attributes
                        List<AttributeKeyValueDto> customAttributes = new ArrayList<>();
                        if (nextItemLength > (indexCount + 14)) {
                            int maxIndex = Math.min(nextItemLength, header.length);
                            for (int i = (indexCount + 14); i < nextItemLength; i++) {
                                String headerKey = (i < header.length) ? header[i] : null;
                                headerKey = headerKey == null ? null : headerKey.trim();
                                String value = GeneralUtils.checkAndGetFromArray(nextItem, i);
                                if (StringUtils.isNotBlank(headerKey)) {
                                    customAttributes.add(new AttributeKeyValueDto(headerKey, value));
                                }
                            }
                        }

                        // Validate speakers: check for duplicates and existence in event
                        String speakerValidationError = null;
                        try {
                            validateSpeakersExistInEvent(primarySpeakers, secondarySpeakers, eventSpeakerEmailIdMap);
                            validateSpeakerEmailsAndDuplicates(primarySpeakers, secondarySpeakers);
                        } catch (NotAcceptableException e) {
                            speakerValidationError = e.getMessage();
                            log.info("Speaker validation failed: {}", speakerValidationError);
                            errorMessage = speakerValidationError;
                        }

                        try {
                            sessionId.set(hasSessionId ? (StringUtils.isNotBlank(nextItem[0]) && !Constants.STRING_EMPTY.equals(nextItem[0]) ? Long.parseLong(nextItem[0].trim()) : 0): 0);
                        } catch (NumberFormatException ignored) {
                            log.info("NumberFormatException {}",ignored.getMessage());
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.ID));
                            log.info(errorMessage);
                            invalidSessions = new UploadSessionDto();
                            invalidSessions.setSessionId(sessionId.get());
                            invalidSessions.setTitle(title);
                            invalidSessions.setSessionTypeFormat(SessionTypeFormat.valueOf(sessionTypeFormat));
                            invalidSessions.setPrimarySpeakers(primarySpeakers);
                            invalidSessions.setSecondarySpeakers(secondarySpeakers);
                            invalidSessions.setCustomAttributes(customAttributes);
                            if (speakerValidationError != null) {
                                invalidSessions.setInvalidSpeakers(true);
                                invalidSessions.setSpeakerErrorMessage(speakerValidationError);
                            }
                            invalidSessions.setTimeErrorMessage(errorMessage);
                            // Use speakerErrorMessage if speaker validation failed, otherwise use errorMessage
                            String finalErrorMessage = (speakerValidationError != null) ? speakerValidationError : errorMessage;
                            response.addInvalidSession(invalidSessions, finalErrorMessage);
                            continue;
                        }
                        String format =  StringUtils.isNotBlank(nextItem[indexCount + 1]) && isValidFormat(nextItem[indexCount + 1].trim()) ? nextItem[indexCount + 1].trim() : Constants.STRING_EMPTY;
                        String startDate = nextItemLength > (indexCount + 3)  && StringUtils.isNotBlank(nextItem[indexCount + 3]) ? nextItem[indexCount + 3].trim() : Constants.STRING_EMPTY;
                        String startTime = nextItemLength > (indexCount + 4)  && StringUtils.isNotBlank(nextItem[indexCount + 4]) ? nextItem[indexCount + 4].trim() : Constants.STRING_EMPTY;
                        String endTime = nextItemLength > (indexCount + 5)  && StringUtils.isNotBlank(nextItem[indexCount + 5]) ? nextItem[indexCount + 5].trim() : Constants.STRING_EMPTY;
                        String fullDetail = nextItemLength > (indexCount + 6) && null != nextItem[indexCount + 6] ? nextItem[indexCount + 6].trim() : Constants.STRING_EMPTY;
                        String shortDescriptionOfSession = nextItemLength > (indexCount + 8) && null != nextItem[indexCount + 8] ? nextItem[indexCount + 8].trim() : Constants.STRING_EMPTY;
                        String tags = nextItemLength > (indexCount + 9) && null != nextItem[indexCount + 9] ? nextItem[indexCount + 9].trim() : Constants.STRING_EMPTY;
                        String tracks = nextItemLength > (indexCount + 10) && null != nextItem[indexCount + 10] ? nextItem[indexCount + 10].trim() : Constants.STRING_EMPTY;

                        try{
                            locationId = nextItemLength > (indexCount + 11) && StringUtils.isNotBlank(nextItem[indexCount + 11]) ? Long.valueOf(nextItem[indexCount + 11].trim()): 0L;
                        }catch(NumberFormatException ignored){
                            log.info("NumberFormatException {}",ignored.getMessage());
                        }
                        try {
                            capacity = nextItemLength > (indexCount + 7) && !Constants.STRING_EMPTY.equals(nextItem[indexCount + 7]) ? Integer.parseInt(nextItem[indexCount + 7].trim()) : 0;
                        } catch (NumberFormatException ignored) {
                            log.info("NumberFormatException {}",ignored.getMessage());
                        }
                        String existingTitle = null;
                        String existingFormat = null;
                        if(sessionId.get() > 0){
                            validSession = existingSessionsMap.get(sessionId.get());
                            if(Objects.isNull(validSession)){
                                errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.SESSION_NOT_FOUND));
                                log.info(errorMessage);
                                invalidSessions = new UploadSessionDto();
                                invalidSessions.setSessionId(sessionId.get());
                                invalidSessions.setTitle(title);
                                invalidSessions.setPrimarySpeakers(primarySpeakers);
                                invalidSessions.setSecondarySpeakers(secondarySpeakers);
                                invalidSessions.setCustomAttributes(customAttributes);
                                if (speakerValidationError != null) {
                                    invalidSessions.setInvalidSpeakers(true);
                                    invalidSessions.setSpeakerErrorMessage(speakerValidationError);
                                }
                                invalidSessions.setTimeErrorMessage(errorMessage);
                                // Use speakerErrorMessage if speaker validation failed, otherwise use errorMessage
                                String finalErrorMessage = (speakerValidationError != null) ? speakerValidationError : errorMessage;
                                response.addInvalidSession(invalidSessions, finalErrorMessage);
                                continue;
                            }
                            existingTitle = validSession.getTitle();
                            existingFormat = validSession.getFormat().name();
                        }
                        else{
                            validSession = new Session();
                        }

                        log.debug("Description");
                        validSession.setDescription(fullDetail);
                        if (StringUtils.isNotBlank(title)) {
                            validSession.setTitle(title);
                        }
                        if (StringUtils.isNotBlank(format)) {
                            if (format.equalsIgnoreCase(REGULAR_SESSION_ENUM)){
                                format = BREAKOUT_SESSION.name();
                            }else if (format.equalsIgnoreCase(MAIN_STAGE_SESSION_ENUM)){
                                format = MAIN_STAGE.name();
                            }
                            validSession.setFormat(EnumSessionFormat.valueOf(format));
                        }
                        if (capacity != null) {
                            validSession.setCapacity(capacity);
                        }
                        validSession.setShortDescriptionOfSession(shortDescriptionOfSession);
                        validSession.setSessionTypeFormat(SessionTypeFormat.valueOf(sessionTypeFormat));
                        String sessionStartDateTime = startDate +" "+startTime;
                        String sessionEndDateTime = startDate + " "+endTime;
                        boolean isValidDate = isValidDate(validSession, sessionStartDateTime, sessionEndDateTime, event);
                        invalidSessions = new UploadSessionDto(validSession);

                        // set tags and tracks in valid session
                        invalidSessions.setTags(tags);
                        invalidSessions.setTracks(tracks);

                        // set speaker data
                        invalidSessions.setPrimarySpeakers(primarySpeakers);
                        invalidSessions.setSecondarySpeakers(secondarySpeakers);
                        invalidSessions.setCustomAttributes(customAttributes);

                        // set speaker validation error if any
                        if (speakerValidationError != null) {
                            invalidSessions.setInvalidSpeakers(true);
                            invalidSessions.setSpeakerErrorMessage(speakerValidationError);
                        }

                        if (!SessionTypeFormat.HYBRID.name().equalsIgnoreCase(event.getEventFormat().name())  && !event.getEventFormat().name().equals(sessionTypeFormat)) {
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.SESSION_TYPE_FORMAT));
                            log.info("Session type format is not valid for current event {} user {}",event.getEventId(),user.getUserId());
                            log.info("SessionTypeFormat = {}", sessionTypeFormat);
                            invalidSessions.setInvalidSessionTypeFormat(true);
                            invalidSessions.setFormatErrorMessage(errorMessage);
                        }

                        if (!isValidDate) {
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.TIME));
                            log.info(errorMessage);
                            log.info("Time = {} ",sessionStartDateTime);
                            log.info("Time = {}",sessionEndDateTime);
                            invalidSessions.setInValidSessionTime(true);
                            invalidSessions.setTimeErrorMessage(errorMessage);
                        } else if (validSession.getFormat()!=null && validSession.getFormat().equals(MAIN_STAGE) && StringUtils.isNotBlank(validSession.getStartTime()) && StringUtils.isNotBlank(validSession.getEndTime() )) {
                            Date sessionStartDate = getDateInUTC(validSession.getStartTime(), event.getEquivalentTimeZone());
                            Date sessionEndDate = getDateInUTC(validSession.getEndTime(), event.getEquivalentTimeZone());
                            Optional<Session> mainStageCollideSession = mainStageSessions.stream().filter(mainStageSession -> mainStageSession.getEndTime().after(sessionStartDate) && mainStageSession.getStartTime().before(sessionEndDate) && !sessionId.get().equals(mainStageSession.getId())).findAny();
                            if (mainStageCollideSession.isPresent()) {
                                errorMessage = resourceBundle.getString(languageMap.get(Constants.SESSION_SLOTS_COLLIDES)).replace(SESSION_NAME, mainStageCollideSession.get().getTitle());
                                invalidSessions.setMainStageSessionConflict(true);
                                invalidSessions.setSessionConflictErrorMessage(errorMessage);
                            }
                        }
                        if (StringUtils.isBlank(validSession.getTitle()) || validSession.getTitle().length() >= 255) {
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.TITLE));
                            invalidSessions.setInValidTitle(true);
                            invalidSessions.setTitleErrorMessage(errorMessage);
                        }
                        if (validSession.getFormat() == null) {
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.FORMAT));
                            log.info(errorMessage);
                            log.info("Format = {}",format);
                            invalidSessions.setInValidFormat(true);
                            invalidSessions.setFormatErrorMessage(errorMessage);
                        }

                        if (capacity == null) {
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.CAPACITY));
                            log.info("Capacity {}",capacity);
                            invalidSessions.setInValidCapacity(true);
                        }
                        if(locationId == null){
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.LOCATION_ID));
                            log.info("Location ID {}",locationId);
                            invalidSessions.setInvalidSessionLocation(true);
                        }
                        if (shortDescriptionOfSession.length() > 150){
                            errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.SHORT_DESCRIPTION));
                            log.info(errorMessage);
                            log.info("shortDescriptionOfSession = {}",shortDescriptionOfSession);
                            invalidSessions.setFormatErrorMessage(errorMessage);
                        }

                        if(StringUtils.isNotBlank(tags)) {
                            Set<String> listOfTags = Arrays.stream(tags.split(STRING_COMMA)).map(String::trim).collect(Collectors.toSet());
                            for(String e : listOfTags) {
                                if (e.matches(REGEX_CONTAINS_ALPHA_NUMERIC) && e.length() > 50) {
                                    errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.TAGS_TRACKS_CHARACTER_LIMIT));
                                    log.info(errorMessage);
                                    log.info("Tags = {}",tags);
                                    invalidSessions.setInvalidTags(true);
                                    invalidSessions.setTagAndTrackErrorMessage(errorMessage);
                                    break;
                                }
                            }
                        }

                        if(StringUtils.isNotBlank(tracks)) {
                            Set<String> listOfTracks = Arrays.stream(tracks.split(STRING_COMMA)).map(String::trim).collect(Collectors.toSet());
                            for(String e : listOfTracks) {
                                if (e.matches(REGEX_CONTAINS_ALPHA_NUMERIC) && e.length() > 50) {
                                    errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.TAGS_TRACKS_CHARACTER_LIMIT));
                                    log.info(errorMessage);
                                    log.info("Tracks = {}",tracks);
                                    invalidSessions.setInvalidTracks(true);
                                    invalidSessions.setTagAndTrackErrorMessage(errorMessage);
                                    break;
                                }
                            }
                        }

                        if(NumberUtils.isNumberGreaterThanZero(locationId)){
                            SessionLocation location = sessionLocationRepo.findByIdAndEventId(locationId,event.getEventId());
                            if (location != null) {
                                validSession.setLocationId(locationId);
                            } else {
                                // handled for invalid session location id
                                errorMessage = resourceBundle.getString(languageMap.get(CSV_SESSION_ERROR_MESSAGE.SESSION_LOCATION));
                                log.info(errorMessage);
                                log.info("Session Location Id = {}",locationId);
                                invalidSessions.setInvalidSessionLocation(true);
                                invalidSessions.setSetlocationErrorMessage(errorMessage);
                            }
                        } else {
                            validSession.setLocationId(null);
                        }

                        // check custom attribute validation
                        if(!isEmpty(customAttributes)){
                            errorMessage = handleCustomAttributesErrors(customAttributes, customFormAttributes, event, invalidSessions, errorMessage);
                        }

                        if (invalidSessions != null && (invalidSessions.isInValidTitle() ||
                                invalidSessions.isInValidFormat() ||
                                invalidSessions.isInValidSessionTime() ||
                                invalidSessions.isInValidCapacity() ||
                                invalidSessions.isMainStageSessionConflict()||
                                invalidSessions.isShortDescription() ||
                                invalidSessions.isInvalidSessionLocation() ||
                                invalidSessions.isInvalidTags() ||
                                invalidSessions.isInvalidTracks() ||
                                invalidSessions.isInvalidSessionTypeFormat() ||
                                invalidSessions.isInvalidSpeakers() ||
                                !CollectionUtils.isEmpty(invalidSessions.getCustomAttributesErrorMessage())
                        )) {
                            // Use speakerErrorMessage if speaker validation failed, otherwise use errorMessage
                            String finalErrorMessage = (speakerValidationError != null) ? speakerValidationError : errorMessage;
                            response.addInvalidSession(invalidSessions, finalErrorMessage);
                        } else {
                            UploadSessionDto uploadSessionDto= new UploadSessionDto(validSession);
                            uploadSessionDto.setTags(tags);
                            uploadSessionDto.setTracks(tracks);
                            uploadSessionDto.setPrimarySpeakers(primarySpeakers);
                            uploadSessionDto.setSecondarySpeakers(secondarySpeakers);
                            uploadSessionDto.setCustomAttributes(customAttributes);
                            if(StringUtils.isNotBlank(existingTitle) && !existingTitle.equals(validSession.getTitle())){
                                uploadSessionDto.setTitleUpdated(true);
                            }
                            if(StringUtils.isNotBlank(existingFormat) && !existingFormat.equals(validSession.getFormat().name())){
                                uploadSessionDto.setPreviousFormat(existingFormat);
                                uploadSessionDto.setSessionTypeUpdated(true);
                            }
                            response.addValidSession(uploadSessionDto);
                        }
                    }
                }
                response.getValidSession().sort(Comparator.comparing(UploadSessionDto::getTitle,new AlphanumericSortComparator()));
                List<UploadSessionDto> uploadedValidSession = response.getValidSession().stream().filter(session -> session.getFormat().equals(MAIN_STAGE)).collect(toList());
                Iterator<UploadSessionDto> uploadMainStageSessionDtoIterator = uploadedValidSession.iterator();
                UploadSessionDto previousMainStageSessionDTO = null;
                while (uploadMainStageSessionDtoIterator.hasNext()) {
                    UploadSessionDto nextSessionDTO = uploadMainStageSessionDtoIterator.next();
                    if (previousMainStageSessionDTO != null && StringUtils.isNotBlank(previousMainStageSessionDTO.getStartDateTime()) && StringUtils.isNotBlank(previousMainStageSessionDTO.getEndDateTime())
                            && StringUtils.isNotBlank(nextSessionDTO.getStartDateTime()) && StringUtils.isNotBlank(nextSessionDTO.getEndDateTime())) {
                        Date previousSessionStartDate = getDateInUTC(previousMainStageSessionDTO.getStartDateTime(), event.getEquivalentTimeZone());
                        Date previousSessionEndDate = getDateInUTC(previousMainStageSessionDTO.getEndDateTime(), event.getEquivalentTimeZone());
                        Date nextSessionStartDate = getDateInUTC(nextSessionDTO.getStartDateTime(), event.getEquivalentTimeZone());
                        Date nextSessionEndDate = getDateInUTC(nextSessionDTO.getEndDateTime(), event.getEquivalentTimeZone());
                        if (previousSessionEndDate.after(nextSessionStartDate) && previousSessionStartDate.before(nextSessionEndDate)) {
                            errorMessage = resourceBundle.getString(languageMap.get(Constants.SESSION_SLOTS_COLLIDES)).replace(SESSION_NAME, previousMainStageSessionDTO.getTitle());
                            invalidSessions.setMainStageSessionConflict(true);
                            invalidSessions.setSessionConflictErrorMessage(errorMessage);
                            response.addInvalidSession(nextSessionDTO, errorMessage);
                            uploadMainStageSessionDtoIterator.remove();
                        }
                    }
                    previousMainStageSessionDTO = nextSessionDTO;
                }
                UploadSessionResponseContainer uploadSessionResponseContainer =  uploadSessionCSVOrZapier(response.getValidSession().toArray(new UploadSessionDto[response.getValidSession().size()]),event, true, user,true); // Skip validation - already done in CSV parsing
                uploadSessionResponseContainer.getInvalidSession().forEach(invalidSessionsList-> response.addInvalidSession(invalidSessionsList.invalidSession,invalidSessionsList.errorMessage));
                List<UploadSessionDto> validSessions = uploadSessionResponseContainer.getValidSession();

                if(!hasSessionId){
                    eventTaskService.updateEventTaskAndAssignTaskToSession(event, validSessions.stream()
                            .map(UploadSessionDto::getSessionId)
                            .collect(Collectors.toList()));
                    sessionThirdPartyService.createChannelByListOfSessionIds(validSessions, event);
                }
                else{

                    List<UploadSessionDto> newSesions=new ArrayList<>();
                    List<UploadSessionDto> updatedSessions=new ArrayList<>();

                    validSessions.forEach(validSession->{
                        Session sessionData =existingSessionsMap.get(validSession.getSessionId());
                        if(sessionData!=null){
                            updatedSessions.add(validSession);
                        }
                        else{
                            newSesions.add(validSession);
                        }
                    });


                    if(!newSesions.isEmpty()){
                        eventTaskService.updateEventTaskAndAssignTaskToSession(event, newSesions.stream()
                                .map(UploadSessionDto::getSessionId)
                                .collect(Collectors.toList()));
                        sessionThirdPartyService.createChannelByListOfSessionIds(newSesions, event);
                    }
                    if(!updatedSessions.isEmpty()){
                        updatedSessions.forEach(validSession-> {
                            EnumChannelStatus channelStatus = determineChannelStatus(validSession);

                            if (channelStatus != null) {
                                switch (channelStatus) {
                                    case UPDATE:
                                        sessionThirdPartyService.updateChannelName(validSession.getSessionId(),validSession.getTitle(),validSession.getFormat(),validSession.isChatEnabled(),event.getEventId());
                                        break;

                                    case CREATE:
                                        sessionThirdPartyService.createChannel(validSession.getSessionId(),validSession.getTitle(),validSession.getFormat(),event);
                                        break;

                                    case DELETE:
                                        sessionThirdPartyService.deleteSessionChannel(validSession.getSessionId(),event.getEventId());
                                        break;
                                }
                            }
                        });

                    }

                }

            } else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_HEADER_NOT_CORRECT);
            }
        }
        return response;
    }

    private EnumChannelStatus determineChannelStatus(UploadSessionDto validSession) {

        boolean isSessionTypeUpdated = validSession.isSessionTypeUpdated();
        EnumSessionFormat currentFormat = validSession.getFormat();

        if (isSessionTypeUpdated) {
            EnumSessionFormat previousFormat = EnumSessionFormat.valueOf(validSession.getPreviousFormat());
            if (EnumSet.of(MAIN_STAGE, BREAKOUT_SESSION, EnumSessionFormat.WORKSHOP).contains(previousFormat)) {
                return EnumChannelStatus.UPDATE;
            }
            if (MEET_UP.equals(currentFormat) && !MEET_UP.equals(previousFormat)) {
                return EnumChannelStatus.DELETE;
            }
            if (!MEET_UP.equals(currentFormat) && MEET_UP.equals(previousFormat)) {
                return EnumChannelStatus.CREATE;
            }
        }
        else if(validSession.isTitleUpdated()){
            return EnumChannelStatus.UPDATE;
        }

        return null;
    }


    private boolean isValidCSVHeader(String[] header, boolean hasSessionId) {
        if (header == null) {
            return false;
        }

        // check if any duplicate header found then return false
        boolean hasDuplicateHeaders = Arrays.stream(header)
                .filter(Objects::nonNull)
                .map(s -> s.trim().toLowerCase())
                .distinct()
                .count() < Arrays.stream(header)
                .filter(Objects::nonNull)
                .map(s -> s.trim().toLowerCase())
                .count();

        if(hasDuplicateHeaders)
            return false;

        int totalDefaultHeader = hasSessionId ? 15 : 14;

        String[] expectedHeaders;
        if(hasSessionId){
            expectedHeaders = EXPECTED_HEADERS_WITH_SESSION_ID;
        }
        else{
            expectedHeaders = EXPECTED_HEADERS_WITHOUT_SESSION_ID;
        }

        // Custom Attribute Header is included so this is not working
       /* if (header.length != expectedHeaders.length) {
            return false;
        }*/

        for (int i = 0; i < totalDefaultHeader ; i++) {
            if (!header[i].trim().equalsIgnoreCase(expectedHeaders[i])) {
                return false;
            }
        }

        return true;
    }

    private boolean isValidFormat(String format) {
        return MAIN_STAGE_SESSION_ENUM.equals(format) || EnumSessionFormat.MEET_UP.name().equals(format) || REGULAR_SESSION_ENUM.equals(format) || EnumSessionFormat.WORKSHOP.name().equals(format) || EnumSessionFormat.EXPO.name().equals(format) || BREAK.name().equals(format) || OTHER.name().equals(format);
    }

    private boolean isValidSessionTypeFormat(String format) {
        return SessionTypeFormat.HYBRID.name().equals(format) || SessionTypeFormat.IN_PERSON.name().equals(format) || SessionTypeFormat.VIRTUAL.name().equals(format);
    }

    private boolean isValidDate(Session session, String startTime, String endTime, Event event) {
        if(StringUtils.isEmpty(startTime.trim()) && StringUtils.isEmpty(endTime.trim())){
            return true;
        }
        if(StringUtils.isEmpty(startTime.trim()) || StringUtils.isEmpty(endTime.trim())) {
            return false;
        }
        TicketingDatesDto ticketing = ticketingRepository.findEventStartAndEndDateByEventid(event);
        Date startDate = DateUtils.getFormattedDate(startTime, DATE_FORMAT_DD_MM_YYYY_HH_MM);
        Date endDate = DateUtils.getFormattedDate(endTime, DATE_FORMAT_DD_MM_YYYY_HH_MM);
        Date currentDateAndTimeInEventTimeZone = getDateInLocal(getCurrentDate(), event.getEquivalentTimeZone());
        Date eventStartDateInLocal = getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone());
        Date eventEndDateInLocal = getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone());
        if (null == startDate || null == endDate) {
            return false;
        }
        if (Objects.requireNonNull(startDate).before(currentDateAndTimeInEventTimeZone)
                || Objects.requireNonNull(endDate).before(currentDateAndTimeInEventTimeZone)) {
            return false;
        }
        if(!(Objects.requireNonNull(startDate).after(eventStartDateInLocal) && Objects.requireNonNull(endDate).before(eventEndDateInLocal) && Objects.requireNonNull(endDate).after(Objects.requireNonNull(startDate)))) {
            if(Objects.requireNonNull(startDate).equals(eventStartDateInLocal) || Objects.requireNonNull(endDate).equals(eventEndDateInLocal)){
                session.setStartTime(startDate);
                session.setEndTime(endDate);
                return true;
            }
            return false;
        }
        session.setStartTime(startDate);
        session.setEndTime(endDate);
        return true;
    }

    @Override
        public UploadSessionResponseContainer uploadSessionCSVOrZapier(UploadSessionDto[] sessionDtos, Event event, Boolean areSpeakersValidated,boolean isSessionCustomAttributeDataValidated, User loggedInUser) {//NOSONAR
        return uploadSessionCSVOrZapier(sessionDtos, event, areSpeakersValidated, loggedInUser,isSessionCustomAttributeDataValidated);
    }

    public UploadSessionResponseContainer uploadSessionCSVOrZapier(UploadSessionDto[] sessionDtos, Event event, Boolean areSpeakersValidated, User loggedInUser,boolean isSessionCustomAttributeDataValidated) {//NOSONAR
        // Collect all speaker-session associations for bulk processing
        Map<String, Set<SpeakerSessionBatchDTO>> speakerSessionAssociations = new HashMap<>();
        SurveyConfiguration defaultSurveyByEventId = surveyConfigRepoService.findDefaultSurveyByEventId(event.getEventId());

        // Fetch all speakers for the event once for batch processing (optimized to fetch only email and ID)
        Map<String, Long> eventSpeakerEmailIdMap = new HashMap<>();

        List<Long> existingSessionIds = Arrays.stream(sessionDtos).filter(session-> NumberUtils.isNumberGreaterThanZero(session.getSessionId())).map(session->session.getSessionId()).collect(Collectors.toList());
        List<Session> sessions =  sessionRepoService.getAllSessionByIdsAndEventId(existingSessionIds,event);
        Map<Long,Session> sessionsMap = sessions.stream().collect(Collectors.toMap(Session::getId,Function.identity()));
        List<EnumSessionFormat> enumSessionFormats = Arrays.asList(BREAK, OTHER, EXPO);
        long nonVisualSessionsCount = sessionRepoService.getNonVisualSessionsByEvent(event.getEventId(), enumSessionFormats);
        long nonVisualUploadSessionDtos = 0;
        for (UploadSessionDto uploadSessionDto : sessionDtos) {
            log.info("uploadSessionCSVOrZapier: Uploading session with ID {}", uploadSessionDto.getSessionId());
            boolean isDraftExistingSession = NumberUtils.isNumberGreaterThanZero(uploadSessionDto.getSessionId())
                    && sessionsMap.containsKey(uploadSessionDto.getSessionId()) && EnumSessionStatus.DRAFT.equals(sessionsMap.get(uploadSessionDto.getSessionId()).getStatus());

            boolean isDraftNewSession = !NumberUtils.isNumberGreaterThanZero(uploadSessionDto.getSessionId())
                    && StringUtils.isEmpty(uploadSessionDto.getStartDateTime()) && StringUtils.isEmpty(uploadSessionDto.getEndDateTime());

            if(isDraftNewSession){
                uploadSessionDto.setStatus(EnumSessionStatus.DRAFT);
            }

            validateDateAndTimeForSession(event, uploadSessionDto.getStartDateTime(), uploadSessionDto.getEndDateTime(), (isDraftExistingSession || isDraftNewSession));

            if(uploadSessionDto.getShortDescriptionOfSession() != null && uploadSessionDto.getShortDescriptionOfSession().length() > 150){
                throw new NotAcceptableException(SHORT_DESCRIPTION_OF_SESSION);
            }
            // need to take care of this condition also
            if(org.apache.commons.lang3.StringUtils.isNotBlank(uploadSessionDto.getSessionLocation()) && uploadSessionDto.getSessionLocation().length()>255) {
                throw  new NotAcceptableException(SESSION_LOCATION_CHARACTER_LIMIT);
            }
            if (!enumSessionFormats.contains(uploadSessionDto.getFormat())) {
                nonVisualUploadSessionDtos++;
            }
        }
        checkMaxAgendaItemsByPlanType(event, nonVisualSessionsCount + nonVisualUploadSessionDtos);
        List<TicketingType> ticketingTypes = ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET));

        List<KeyValue> existingTags = keyValueRepoService.getKeyValueListByEventIdAndType(event, TAG);
        List<String> existingTagsNames = existingTags.stream().map(KeyValue::getName).collect(toList());

        List<KeyValue> existingTracks = keyValueRepoService.getKeyValueListByEventIdAndType(event, TRACK);
        List<String> existingTracksNames = existingTracks.stream().map(KeyValue::getName).collect(toList());

        Double lastTagItemPosition = keyValueRepoService.findMaxPositionByEventIdAndType(event, TAG);
        Double lastTrackItemPosition = keyValueRepoService.findMaxPositionByEventIdAndType(event, TRACK);

        List<SessionTagAndTrack> sessionTagAndTrackList = sessionTagAndTrackService.findBySessionIds(existingSessionIds);
        Map<Long, List<SessionTagAndTrack>> sessionTagAndTrackMap = sessionTagAndTrackList.stream().collect(Collectors.groupingBy(SessionTagAndTrack::getSessionId));
        UploadSessionResponseContainer response = new UploadSessionResponseContainer();
        for (UploadSessionDto uploadSessionDto : sessionDtos) {
            // Clear speaker error fields at the beginning to ensure clean state
            // They will only be set if actual speaker validation errors occur
            uploadSessionDto.setInvalidSpeakers(false);
            uploadSessionDto.setSpeakerErrorMessage(null);

            boolean isValidTags = false;
            try {
                validateSessionIdAndFormatForPostSession(uploadSessionDto,event,sessionsMap);

                // Not if already validated in CSV parsing
                if (!areSpeakersValidated) {
                    eventSpeakerEmailIdMap = createEventSpeakerEmailIdMap(event);
                    validateSpeakersExistInEvent(uploadSessionDto.getPrimarySpeakers(), uploadSessionDto.getSecondarySpeakers(), eventSpeakerEmailIdMap);
                    validateSpeakerEmailsAndDuplicates(uploadSessionDto.getPrimarySpeakers(), uploadSessionDto.getSecondarySpeakers());
                }

                EnumSessionStatus sessionStatus = uploadSessionDto.getStatus() != null ? uploadSessionDto.getStatus() : EnumSessionStatus.VISIBLE;

                throwErrorIfTimeSlotConflicts(event, uploadSessionDto.getFormat(), uploadSessionDto.getStartDateTime(), uploadSessionDto.getEndDateTime(), uploadSessionDto.getSessionId(), sessionStatus);

                List<KeyValue> finalTagsAndTracks = new ArrayList<>();
                if (StringUtils.isNotBlank(uploadSessionDto.getTags())) {
                    String uploadedTags = uploadSessionDto.getTags();
                    lastTagItemPosition = extractAndValidateTagsAndTracksFromCSVUpload(event, TAG, uploadedTags, existingTags, existingTagsNames, lastTagItemPosition, finalTagsAndTracks);
                    isValidTags = true;
                }
                if (StringUtils.isNotBlank(uploadSessionDto.getTracks())) {
                    String uploadedTracks = uploadSessionDto.getTracks();
                    lastTrackItemPosition = extractAndValidateTagsAndTracksFromCSVUpload(event, TRACK, uploadedTracks, existingTracks, existingTracksNames, lastTrackItemPosition, finalTagsAndTracks);
                }
                Session session;
                CustomAttributeDetailsDto customAttributeDetailsDto = new CustomAttributeDetailsDto();
                customAttributeDetailsDto.setAttributes(uploadSessionDto.getCustomAttributes());
                if(NumberUtils.isNumberGreaterThanZero(uploadSessionDto.getSessionId())){
                    session = sessionsMap.get(uploadSessionDto.getSessionId());
                    uploadSessionDto.updateExistingEntity(event,session);
                }
                else{
                    session = uploadSessionDto.updateEntity(event);

                    if(uploadSessionDto.getStatus() != null && EnumSessionStatus.DRAFT.equals(uploadSessionDto.getStatus())){
                        session.setStatus(EnumSessionStatus.DRAFT);
                    }
                    if(!ObjectUtils.isEmpty(defaultSurveyByEventId)) {
                        session.setSurveyId(defaultSurveyByEventId.getSurveyId());
                        session.setSurveyEnabled(true);
                    }
                }
                if(!isSessionCustomAttributeDataValidated){
                    List<CustomFormAttribute> customFormAttributes  = customFormAttributeService.getCustomFormAttributeOfEvent(event, null, null, AttributeType.SESSION);
                    if(!isEmpty(uploadSessionDto.getCustomAttributes()) && !STRING_EMPTY.equals(handleCustomAttributesErrors(customAttributeDetailsDto.getAttributes(), customFormAttributes, event, uploadSessionDto, STRING_EMPTY))){
                        throw  new NotAcceptableException(SESSION_CUSTOM_ATTRIBUTE_DATA_INVALID);
                    }
                }
                CustomFormAttributeData baseData = (session.getSessionAttribute() != null) ? session.getSessionAttribute() : new CustomFormAttributeData();

                CustomFormAttributeData savedData = customFormAttributeService.saveCustomAttributeData(customAttributeDetailsDto, baseData);
                session.setSessionAttributeId(savedData.getId());

                setPositionForSession(session);
                String ticketingTypeIds = getTicketTypeIdsForSessionUpload(ticketingTypes, session);

                if (StringUtils.isNotBlank(ticketingTypeIds)) {
                    session.setTicketTypesThatCanBeRegistered(ticketingTypeIds);
                }
                Session updatedSession = sessionRepoService.save(session);
                List<SessionTagAndTrack> sessionTagAndTracks = sessionTagAndTrackMap.get(updatedSession.getId());
                if(!CollectionUtils.isEmpty(sessionTagAndTracks)){
                    Set<Long> tagAndTrackIds = sessionTagAndTracks.stream().map(e->e.getTagOrTrackId()).collect(Collectors.toSet());
                    sessionTagAndTrackService.deleteRecords(session.getId(), tagAndTrackIds);
                }

                if (!CollectionUtils.isEmpty(finalTagsAndTracks)) {
                    sessionTagAndTrackService.createRecords(updatedSession.getId(), finalTagsAndTracks.stream().map(KeyValue::getId).collect(Collectors.toSet()));
                }

                saveSessionSettingDetails(session);
                if (EnumSessionFormat.MEET_UP.equals(updatedSession.getFormat())) {
                    networkingRulesService.createOrUpdateNetworkingRuleWhenCreatingSession(event, updatedSession);
                }

                uploadSessionDto.setSessionId(updatedSession.getId());

                // Collect speaker-session associations for bulk processing
                collectSpeakerSessionAssociations(uploadSessionDto, updatedSession, speakerSessionAssociations);

                response.addValidSession(uploadSessionDto);
            } catch (NotAcceptableException ne) {
                if (CSV_SESSION_ERROR_MESSAGE.TAGS_TRACKS_CHARACTER_LIMIT.equals(ne.getErrorMessage())) {
                    if (isValidTags) {
                        uploadSessionDto.setInvalidTracks(Boolean.TRUE);
                    } else {
                        uploadSessionDto.setInvalidTags(Boolean.TRUE);
                    }
                    uploadSessionDto.setTagAndTrackErrorMessage(ne.getMessage());
                } else if ("4068929".equals(ne.getErrorCode()) || "4072014".equals(ne.getErrorCode()) || "40600014".equals(ne.getErrorCode())) {
                    // Handle speaker validation errors: SESSION_SPEAKER_NOT_FOUND (4068929), SESSION_SPEAKER_DUPLICATE (4072014), NOT_VALID_EMAIL (40600014)
                    uploadSessionDto.setInvalidSpeakers(true);
                    uploadSessionDto.setSpeakerErrorMessage(ne.getMessage());
                } else {
                    uploadSessionDto.setSessionConflictErrorMessage(ne.getMessage());
                    uploadSessionDto.setMainStageSessionConflict(true);
                }
                response.addInvalidSession(uploadSessionDto, ne.getMessage());
            } catch (Exception e) {
                response.addInvalidSession(uploadSessionDto, e.getMessage());
            }

        }

        try {
            // Process all speaker-session associations after all sessions are created/updated
            // This method now returns only the newly created associations
            Map<String, Set<SpeakerSessionBatchDTO>> newSpeakerSessionAssociations = processAllSpeakerSessionAssociations(speakerSessionAssociations, areSpeakersValidated ? eventSpeakerEmailIdMap : null, event);

            // Send speaker invitations only to speakers with new associations if speaker invites are enabled
            sendSpeakerInvitationsAfterCSVUpload(newSpeakerSessionAssociations, event, loggedInUser);

        } catch (Exception e) {
            log.error("Error processing speaker-session associations or sending invitations for event {}: {}", event.getEventId(), e.getMessage(), e);
        }

        updateEventAgendaAddedChecklistFlag(event, true);
        return response;
    }

    public void validateSessionIdAndFormatForPostSession(UploadSessionDto sessionDto,Event event,Map<Long,Session> sessionMap){
            if(NumberUtils.isNumberGreaterThanZero(sessionDto.getSessionId())){
                Session session = sessionMap.get(sessionDto.getSessionId());
                if(Objects.isNull(session)){
                    throw new NotAcceptableException(SESSION_NOT_FOUND_FOR_THE_GIVEN_ID);
                }
            }
            if (!SessionTypeFormat.HYBRID.name().equalsIgnoreCase(event.getEventFormat().name())  && !event.getEventFormat().name().equals(sessionDto.getSessionTypeFormat().name())) {
                throw new NotAcceptableException(INVALID_SESSION_TYPE_FORMAT);
            }
    }

    public void updateSessionThroughZapier(UploadSessionDto uploadSessionDto, Event event, Session session) {
        validateDateAndTimeForSession(event, uploadSessionDto.getStartDateTime(), uploadSessionDto.getEndDateTime(), Boolean.FALSE);
        if(uploadSessionDto.getShortDescriptionOfSession() != null && uploadSessionDto.getShortDescriptionOfSession().length() > 150){
            throw new NotAcceptableException(SHORT_DESCRIPTION_OF_SESSION);
        }
        if(org.apache.commons.lang3.StringUtils.isNotBlank(uploadSessionDto.getSessionLocation()) && uploadSessionDto.getSessionLocation().length()>255) {
            throw  new NotAcceptableException(SESSION_LOCATION_CHARACTER_LIMIT);
        }

        List<KeyValue> existingTags = keyValueRepoService.getKeyValueListByEventIdAndType(event, TAG);
        List<String> existingTagsNames = existingTags.stream().map(KeyValue::getName).collect(toList());

        List<KeyValue> existingTracks = keyValueRepoService.getKeyValueListByEventIdAndType(event, TRACK);
        List<String> existingTracksNames = existingTracks.stream().map(KeyValue::getName).collect(toList());

        Double lastTagItemPosition = keyValueRepoService.findMaxPositionByEventIdAndType(event, TAG);
        Double lastTrackItemPosition = keyValueRepoService.findMaxPositionByEventIdAndType(event, TRACK);

        throwErrorIfTimeSlotConflicts(event, uploadSessionDto.getFormat(), uploadSessionDto.getStartDateTime(), uploadSessionDto.getEndDateTime(), session.getId(), session.getStatus());

        List<KeyValue> finalTagsAndTracks = new ArrayList<>();

        //Process Tags and decided add or remove accordingly
        extractAndValidateTagsAndTracksFromCSVUpload(event, TAG, uploadSessionDto.getTags(), existingTags, existingTagsNames, lastTagItemPosition, finalTagsAndTracks);
        List<String> uploadedTagsList = Arrays.stream(uploadSessionDto.getTags().split(COMMA_SPACE))
                .map(String::trim) // Remove any leading/trailing whitespace
                .collect(Collectors.toList());

        Set<Long> sessionTagIdToRemove =  session.getSessionTagAndTracks().stream()
                .filter(sessionTag -> sessionTag.getTagOrTrack().getType().equals(TAG))
                .filter(sessionTag -> !uploadedTagsList.contains(sessionTag.getTagOrTrack().getName()))
                .map(SessionTagAndTrack::getTagOrTrackId)
                .collect(Collectors.toSet());

        if(!sessionTagIdToRemove.isEmpty()){
            sessionTagAndTrackService.deleteRecords(session.getId(), sessionTagIdToRemove);
        }

        //Process Tracks and decided Add or remove accordingly.
        extractAndValidateTagsAndTracksFromCSVUpload(event, TRACK, uploadSessionDto.getTracks(), existingTracks, existingTracksNames, lastTrackItemPosition, finalTagsAndTracks);

        List<String> uploadedTracksList = Arrays.stream(uploadSessionDto.getTracks().split(COMMA_SPACE))
                .map(String::trim) // Remove any leading/trailing whitespace
                .collect(Collectors.toList());

        Set<Long> sessionTrackIdToRemove =  session.getSessionTagAndTracks().stream()
                .filter(sessionTrack -> sessionTrack.getTagOrTrack().getType().equals(TRACK))
                .filter(sessionTrack -> !uploadedTracksList.contains(sessionTrack.getTagOrTrack().getName()))
                .map(SessionTagAndTrack::getTagOrTrackId)
                .collect(Collectors.toSet());

        if(!sessionTrackIdToRemove.isEmpty()){
            sessionTagAndTrackService.deleteRecords(session.getId(), sessionTrackIdToRemove);
        }

        if (!CollectionUtils.isEmpty(finalTagsAndTracks)) {
            Set<Long> currentSessionTagsAndTrackId = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(toSet());
            Set<Long> createTagsAndTrackId = finalTagsAndTracks.stream()
                            .filter(tagsAndTrack -> !currentSessionTagsAndTrackId.contains(tagsAndTrack.getId()))
                                    .map(KeyValue::getId)
                                            .collect(toSet());
            sessionTagAndTrackService.createRecords(session.getId(), createTagsAndTrackId);
        }

        session.setTitle(uploadSessionDto.getTitle());
        session.setSessionTagAndTracks(new HashSet<>(sessionTagAndTrackService.findBySessionId(session.getId())));
        session.setFormat(uploadSessionDto.getFormat());
        session.setStartTime(TimeZoneUtil.getDateInUTC(uploadSessionDto.getStartDateTime(),  event.getEquivalentTimeZone(), LOCAL_DATE_FORMAT));
        session.setEndTime(TimeZoneUtil.getDateInUTC(uploadSessionDto.getEndDateTime(),  event.getEquivalentTimeZone(), LOCAL_DATE_FORMAT));
        session.setDescription(uploadSessionDto.getDescription());
        session.setCapacity(uploadSessionDto.getCapacity());
        session.setShortDescriptionOfSession(uploadSessionDto.getShortDescriptionOfSession());
        session.setSessionTypeFormat(uploadSessionDto.getSessionTypeFormat());

        if(StringUtils.isNotBlank(uploadSessionDto.getSessionLocation())) {
            Long existingLocationId = sessionLocationRepo.findIdByName(uploadSessionDto.getSessionLocation(),event.getEventId());
            if(NumberUtils.isNumberGreaterThanZero(existingLocationId)){
                session.setLocationId(existingLocationId);
            }else{
                SessionLocationDTO sessionLocationDTO = new SessionLocationDTO(0L, uploadSessionDto.getSessionLocation(), null);
                User dummyUser = User.dummyUser("Zapier", "Integration");
                Long locationId = sessionLocationService.addSessionLocation(sessionLocationDTO, event, dummyUser);
                session.setLocationId(locationId);
            }
        }


        this.save(session);

        if (EnumSessionFormat.MEET_UP.equals(session.getFormat())) {
            networkingRulesService.createOrUpdateNetworkingRuleWhenCreatingSession(event, session);
        }
    }

    private Double extractAndValidateTagsAndTracksFromCSVUpload(Event event,EnumKeyValueType keyValueType,  String uploadedTagsOrTracks, List<KeyValue> existingTagsOrTracks, List<String> existingTagsOrTracksNames, Double lastTagOrTrackItemPosition, List<KeyValue> finalTagsAndTracks) {
        Set<String> listOfTags = Arrays.stream(uploadedTagsOrTracks.split(STRING_COMMA)).map(String::trim).collect(toSet());
        listOfTags = listOfTags.stream().filter(e-> StringUtils.isNotBlank(e) && e.matches(REGEX_CONTAINS_ALPHA_NUMERIC)).collect(Collectors.toSet());
        List<KeyValue> needToAddTagsOrTrackInDB = new ArrayList<>();

        double sequence = 1000d;
        for(String tag : listOfTags) {
            if(tag.length() > 50) {
                throw new NotAcceptableException(TAGS_TRACKS_CHARACTER_LIMIT);
            }
            if (!existingTagsOrTracksNames.contains(tag)) {
                KeyValue keyValue = new KeyValue();
                keyValue.setEventId(event);
                keyValue.setName(tag);
                keyValue.setType(keyValueType);
                if(TRACK.equals(keyValueType)) {
                    keyValue.setColor(DEFAULT_COLOR_HAX_000000);
                    keyValue.setBackgroundColor(DEFAULT_COLOR_HAX_00000014);
                }
                lastTagOrTrackItemPosition +=sequence;
                keyValue.setPosition(lastTagOrTrackItemPosition);
                needToAddTagsOrTrackInDB.add(keyValue);
            } else {
                int index = existingTagsOrTracksNames.indexOf(tag);
                if(index != -1) {
                    finalTagsAndTracks.add(existingTagsOrTracks.get(index));
                }
            }
        }
        needToAddTagsOrTrackInDB = keyValueRepoService.saveAll(needToAddTagsOrTrackInDB);
        cacheService.evictSingleCacheValue("getKeyValueUsingEventIdAndType",event.getEventId()+keyValueType.name());

        // Add newly created tags in existing tags List at global level
        existingTagsOrTracks.addAll(needToAddTagsOrTrackInDB);
        existingTagsOrTracksNames.addAll(needToAddTagsOrTrackInDB.stream().map(KeyValue::getName).collect(Collectors.toSet()));

        finalTagsAndTracks.addAll(needToAddTagsOrTrackInDB);
        return lastTagOrTrackItemPosition;
    }

  private void saveSessionSettingDetails(Session session){
      SessionDetailsDto detailsDto = new SessionDetailsDto();
      detailsDto.setRecordSession(true);
      detailsDto.setAllowedMinutesToJoinLate(0);
      sessionDetailsService.save(session,detailsDto);
    }

    private String getTicketTypeIdsForSessionUpload(List<TicketingType> ticketingTypes, Session session) {
        if (!CollectionUtils.isEmpty(ticketingTypes)) {
            return  ticketingTypes.stream().filter(a -> (null != a.getTicketTypeFormat() && (virtualHybrid.contains(session.getSessionTypeFormat()) && a.getTicketTypeFormat().equals(TicketTypeFormat.VIRTUAL)
                    || (inPersonHybrid.contains(session.getSessionTypeFormat()) && a.getTicketTypeFormat().equals(TicketTypeFormat.IN_PERSON))
                    || a.getTicketTypeFormat().equals(TicketTypeFormat.HYBRID)))).map(a -> String.valueOf(a.getId())).collect(Collectors.joining(","));
        }
        return null;
    }

    @Override
    public MeetingAttendeeDto joinSession(Long sessionId, Event event, User user) {
        Session session = sessionRepoService.getSessionById(sessionId, event);
        if (null == session) {
            throw new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND);
        }
        List<Long> eventAdmins = staffService.findAllEventAdminId(event);
        List<SessionSpeaker> sessionSpeakers = sessionSpeakerRepoService.getSpeakerUserIdBySessionId(sessionId);
        List<Long> speakerUserIds = sessionSpeakers.stream().map(e -> e.getSpeaker().getUserId()).collect(Collectors.toList());
        if (StringUtils.isBlank(session.getMeetingObj())) {
            MeetingAttendeeDto meetingAndJoinAttendee = chimeService.createWorkshopAndJoinAttendee(session, user, event);
            session.setMeetingObj(meetingAndJoinAttendee.getMeeting());
            sessionRepoService.save(session);
            meetingAndJoinAttendee.setSessionId(session.getId());
            meetingAndJoinAttendee.setTitle(session.getTitle());
            meetingAndJoinAttendee.setEventAdmins(eventAdmins);
            meetingAndJoinAttendee.setSpeakersOrModeratorsUserIds(speakerUserIds);
            return meetingAndJoinAttendee;
        } else {
            try {
                Meeting meeting = SessionUtils.getMeeting(session.getMeetingObj());
                MeetingAttendeeDto meetingAndJoinAttendee = chimeService.joinAttendee(user, meeting.getMeetingId());
                session.setMeetingObj(meetingAndJoinAttendee.getMeeting());
                meetingAndJoinAttendee.setSessionId(session.getId());
                meetingAndJoinAttendee.setTitle(session.getTitle());
                meetingAndJoinAttendee.setEventAdmins(eventAdmins);
                meetingAndJoinAttendee.setSpeakersOrModeratorsUserIds(speakerUserIds);
                return meetingAndJoinAttendee;
            } catch (com.amazonaws.services.chimesdkmeetings.model.NotFoundException ne) {
                log.info("The meeting has ended", ne);
                if (ne.getErrorMessage().equalsIgnoreCase("The meeting has ended") || ne.getErrorCode().equalsIgnoreCase("NotFoundException")) {
                    MeetingAttendeeDto meetingAndJoinAttendee = chimeService.createWorkshopAndJoinAttendee(session, user, event);
                    session.setMeetingObj(meetingAndJoinAttendee.getMeeting());
                    sessionRepoService.save(session);
                    meetingAndJoinAttendee.setSessionId(session.getId());
                    meetingAndJoinAttendee.setTitle(session.getTitle());
                    meetingAndJoinAttendee.setEventAdmins(eventAdmins);
                    meetingAndJoinAttendee.setSpeakersOrModeratorsUserIds(speakerUserIds);
                    return meetingAndJoinAttendee;
                }
                throw new NotAcceptableException(ne);
            } catch (LimitExceededException ex) {
                log.info("The meeting limit exceed {} sessionId {}", ex, sessionId);
                throw new NotAcceptableException("4060012", ex.getErrorMessage(), ex.getErrorMessage());
            } catch (Exception e) {
                throw new NotAcceptableException(e);
            }
        }
    }


    @Override
    public void clearMeeting(Long sessionId, Event event) {
        Session session = sessionRepoService.getSessionById(sessionId, event);
        if (null != session) {
            session.setMeetingObj("");
            sessionRepoService.save(session);
        }
    }

    @Override
    @Transactional
    public Session duplicateSession(Long sessionId, Event event, User loggedInUser) {
        Session oldSession = sessionRepoService.getSessionByIdWithoutCache(sessionId, event);
        this.checkNonVisualSessionCount(oldSession.getFormat(), event, oldSession.getFormat(), false);
        Session duplicatedSession = (Session) oldSession.clone();
        duplicatedSession.setId(0);
        duplicatedSession.setTitle(oldSession.getTitle());
        duplicatedSession.setSessionSpeakers(Collections.emptySet());
        duplicatedSession.setSessionTagAndTracks(Collections.emptySet());
        duplicatedSession.setPlayBackRestrictionToken(null);


        duplicatedSession.setSessionStartStatus(EnumSessionStartStatus.NULL);
        duplicatedSession.setTaskId(null);
        duplicatedSession.setRecordStatus(RecordStatus.CREATE);
        duplicatedSession.setRtmpUrl(null);
        duplicatedSession.setLiveStreamId(null);
        duplicatedSession.setStreamKey(null);
        duplicatedSession.setStreamUrl(null);
        duplicatedSession.setMeetingPassword(null);
        duplicatedSession.setMeetingObj(null);
        duplicatedSession.setDirectVideoAutoStart(false);
        duplicatedSession.setSessionTypeFormat(oldSession.getSessionTypeFormat());
        if(NumberUtils.isNumberGreaterThanZero(duplicatedSession.getSessionAttributeId())){
            CustomFormAttributeData customFormAttributeData = customFormAttributeDataRepository.findById(duplicatedSession.getSessionAttributeId()).orElse(null);
            if(customFormAttributeData != null){
                CustomFormAttributeData newCustomFormAttributeData = (CustomFormAttributeData) customFormAttributeData.clone();
                newCustomFormAttributeData.setId(0L);
                duplicatedSession.setSessionAttributeId(customFormAttributeDataRepository.save(newCustomFormAttributeData).getId());
            }
            else{
                duplicatedSession.setSessionAttributeId(0L);
            }
        }
        sessionRepoService.save(duplicatedSession);

        createDuplicateSessionDetails(duplicatedSession,oldSession, Boolean.FALSE);

        if (EnumSessionFormat.MEET_UP.equals(oldSession.getFormat())) {
            networkingRulesService.createDuplicateNetworkingRule(event.getEventId(), oldSession.getId(), duplicatedSession.getId());
        }
        createDuplicateSessionSpeaker(oldSession.getSessionSpeakers(), duplicatedSession,event, loggedInUser);
        createDuplicateSessionTagAndTrack(oldSession.getSessionTagAndTracks(), duplicatedSession);
        createDuplicateSessionSponsorExhibitorJson(oldSession.getSponsorExhibitorJson(), duplicatedSession);

        return duplicatedSession;
    }

    private void throwErrorIfTimeSlotConflictsDuplicate(Event event, EnumSessionFormat format, Session duplicatedSession) {
        Date startTime = duplicatedSession.getStartTime();
        Date endTime = duplicatedSession.getEndTime();
        TicketingDatesDto ticketing = ticketingRepository.findEventStartAndEndDateByEventid(event);
        if (!MAIN_STAGE.equals(format)) {
            validateDateAndTimeForDuplicateSession(startTime, endTime, ticketing,event, EnumSessionStatus.DRAFT.equals(duplicatedSession.getStatus()));
        } else if( startTime != null && endTime != null) {
            while (true) {
                validateDateAndTimeForDuplicateSession(startTime, endTime, ticketing,event, EnumSessionStatus.DRAFT.equals(duplicatedSession.getStatus()));
                List<String> conflictingSessions = sessionRepo.findSessionsWithConflictingSlot(event.getEventId(), startTime, endTime, format, 0L);
                if (!isEmpty(conflictingSessions) && startTime != null && endTime != null) {
                    long difference = endTime.getTime() - startTime.getTime();
                    int differenceInMinutes = (int) TimeUnit.MILLISECONDS.toMinutes(difference);
                    startTime = DateUtils.getAddedMinutes(startTime, differenceInMinutes);
                    endTime = DateUtils.getAddedMinutes(endTime, differenceInMinutes);
                } else if( startTime != null && endTime != null) {
                    duplicatedSession.setStartTime(startTime);
                    duplicatedSession.setEndTime(endTime);
                    break;
                }
            }
        }
    }

    private void validateDateAndTimeForDuplicateSession(Date startTime, Date endTime, TicketingDatesDto ticketing,Event event, Boolean isDraft) {
        if(isDraft && ( startTime == null || endTime == null)){
            return;
        }
        Date sessionStartDate = getFormattedDate(DateUtils.getDateString(startTime, SESSION_TIME_FORMAT), SESSION_TIME_FORMAT);
        Date sessionEndDate = getFormattedDate(DateUtils.getDateString(endTime, SESSION_TIME_FORMAT), SESSION_TIME_FORMAT);
        Date eventEndTime = ticketing.getEventEndDate();
        log.info("validateDateAndTimeForDuplicateSession eventId {} eventEndTime {}",event.getEventId(),eventEndTime);
        Calendar calendarForEventEndTime = Calendar.getInstance();
        calendarForEventEndTime.setTime(eventEndTime);
        calendarForEventEndTime.set(Calendar.HOUR_OF_DAY, 0);
        calendarForEventEndTime.set(Calendar.MINUTE, 0);
        calendarForEventEndTime.set(Calendar.SECOND, 0);
        calendarForEventEndTime.set(Calendar.MILLISECOND, 0);
        calendarForEventEndTime.set(Calendar.MINUTE, 1440- 1);
        log.info("validateDateAndTimeForDuplicateSession eventId {} sessionStartDate {} sessionEndDate {}",event.getEventId(),sessionStartDate,sessionEndDate);
        eventEndTime=getDateInUTC(calendarForEventEndTime.getTime(),event.getEquivalentTimeZone());
        log.info("validateDateAndTimeForDuplicateSession after setting date into Calendar eventId {} eventEndTime {}",event.getEventId(),eventEndTime);
        if (!(sessionStartDate.after(ticketing.getEventStartDate()) && sessionEndDate.before(eventEndTime) && sessionStartDate.before(ticketing.getEventEndDate()))) {
            if (sessionStartDate.equals(ticketing.getEventStartDate()) || sessionEndDate.equals(eventEndTime)) {
                return;
            }
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_END_TIME_SHOULD_BE_BEFORE_11_59PM);
        }
    }

    private void createDuplicateSessionDetails(Session duplicatedSession, Session session, boolean copyCTA) {
        Optional<SessionDetails> sessionDetailsOptional = sessionDetailsService.getSessionDetailsBySession(session);
        if (sessionDetailsOptional.isPresent()) {
            SessionDetails newSessionDetails = new SessionDetails();
            SessionDetails sessionDetail = sessionDetailsOptional.get();
            if(StreamProvider.YOUTUBE.equals(duplicatedSession.getStreamProvider()) || StreamProvider.VIMEO.equals(duplicatedSession.getStreamProvider())) {
                newSessionDetails.setVideoDuration(sessionDetail.getVideoDuration());
            }else {
                newSessionDetails.setVideoDuration(0);
            }
            newSessionDetails.setRecordSession(sessionDetail.isRecordSession());
            newSessionDetails.setHideVideoControls(sessionDetail.getHideVideoControls());
            newSessionDetails.setAllowedMinutesToJoinLate(sessionDetail.getAllowedMinutesToJoinLate());
            newSessionDetails.setQnAPrivate(sessionDetail.isQnAPrivate());
            newSessionDetails.setEnableAttendeeList(sessionDetail.isEnableAttendeeList());
            if(copyCTA){
                newSessionDetails.setPostSessionCallToActionJson(sessionDetail.getPostSessionCallToActionJson());
            }
            newSessionDetails.setSession(duplicatedSession);
            if (SubStreamProvider.OPENTOK.equals(duplicatedSession.getSubStreamProvider())) {
                String vonageSession = vonageService.createVonageSession();
                newSessionDetails.setVonageSessionId(vonageSession);
            }
            sessionDetailsService.save(newSessionDetails);
        }
    }

    private void createDuplicateSessionSponsorExhibitorJson(String sponsorExhibitorJson, Session dupSession) {
        sponsorsService.addExhibitorsSponsorsToDuplicateSession(sponsorExhibitorJson, dupSession);
    }

    private void createDuplicateSessionTagAndTrack(Set<SessionTagAndTrack> sessionTagAndTracks, Session dupSession) {
        Set<SessionTagAndTrack> setSessionTagAndTrack = new HashSet<>();
        sessionTagAndTracks.forEach(stt -> {
            SessionTagAndTrack dupSessionTagAndTracks = (SessionTagAndTrack) stt.clone();
            dupSessionTagAndTracks.setId(0);
            dupSessionTagAndTracks.setSessionId(dupSession.getId());
            setSessionTagAndTrack.add(dupSessionTagAndTracks);
        });
        sessionTagAndTrackService.saveAll(setSessionTagAndTrack);
    }

    private void createDuplicateSessionSpeaker(Set<SessionSpeaker> sessionSpeakers, Session dupSession, Event event, User loggedInUser) {
        Set<SessionSpeaker> setSessionSpeakers = new HashSet<>();
        boolean isSpeakerInviteEnable = virtualEventService.isSpeakerInviteEnable(dupSession.getEvent());
        sessionSpeakers.forEach(ss -> {
            SessionSpeaker dupSessionSpeaker = (SessionSpeaker) ss.clone();
            dupSessionSpeaker.setId(0);
            dupSessionSpeaker.setSessionId(dupSession.getId());
            setSessionSpeakers.add(dupSessionSpeaker);
            if (isSpeakerInviteEnable) {
                speakerHelperService.sendInvite(dupSession.getEvent(), loggedInUser, dupSession.getId(), dupSessionSpeaker.getSpeakerId());
            }
        });
        sessionSpeakerService.saveAll(setSessionSpeakers);

        setSessionSpeakers.forEach(sessionSpeaker -> userSessionService.registerSpeakerUser(sessionSpeaker.getSpeaker().getUserId(),dupSession.getId(), event));
    }
    @Override
    public Set<String> getAllSessionDatesInEvent(Event event, Long timezoneOffset ,  boolean isAdminOrStaff) {
        long hours = Math.abs(timezoneOffset / 60);
        long minutes = Math.abs(timezoneOffset % 60);
        String positiveNegSign = timezoneOffset > 0 ? "+" : "-";
        String offsetInColonFormat = positiveNegSign + String.format("%02d", hours) + ":" + String.format("%02d", minutes);
        return sessionRepoService.findAllSessionDatesByEvent(event, offsetInColonFormat, isAdminOrStaff);
    }

    @Override
    @Transactional
    public void removeRecordedSessionVideo(Long muxLiveStreamAssetId) {
        muxLivestreamAssetService.removePastWelcomeMessageOrSessionVideo(muxLiveStreamAssetId);
    }

    @Override
    public void setAgendaAddedChecklistFlag(Event event) {
        long sessionCount = sessionRepoService.getSessionCountByEventId(event.getEventId());
        if (sessionCount == 0) {
            updateEventAgendaAddedChecklistFlag(event, false);
        }
    }

    @Override
    public List<Session> findSessionByEventIdOrderByIdAsc(Long eventId) {
        return sessionRepoService.findSessionByEventIdOrderByIdAsc(eventId);
    }

    @Override
    public DataTableResponse getUserRegisteredSessions(Event event, User user, boolean isAdminOrStaff) {
        List<Long> sessionIds = userSessionService.findByEventIdAndUserId(event.getEventId(), user.getUserId());
        return getSessionInformationBySessionIds(sessionIds, event, null, user, null, true, isAdminOrStaff, null);
    }

    @Override
    public List<SessionIdTitleDto> getSessionIdAndTitleById(List<Long> sessionId) {
        return sessionRepoService.getSessionIdAndTitleById(sessionId);
    }

    @Override
    public void updateSessionDocumentBySpeaker(Event event, User user, Long sessionId, List<KeyValueDto> keyValueDtos) {
        Session session = sessionRepoService.getSessionByIdWithoutCache(sessionId, event);
        SessionDTO sessionDTO = new SessionDTO(session, event);
        if (!CollectionUtils.isEmpty(keyValueDtos)) {
            sessionDTO.setDocumentKeyValue(keyValueDtos);
            session.setDocuments(sessionDTO.convertKeyValuesToJson(sessionDTO.getDocumentKeyValue()));
        } else {
            session.setDocuments(null);
        }
        sessionRepoService.save(session);
    }

    @Override
    public DataTableResponseForSession getDisplayPortalSessionList(String expand,
                                                                   Event event,
                                                                   User user,
                                                                   Pageable pageable,
                                                                   SessionFilter sessionFilter, boolean isAdminOrStaff, Boolean showPastAndUpcoming, String calledFrom) {
        log.info("getDisplayPortalSessionList eventId {}, expand {},isAdminOrStaff {},showPastAndUpcoming {},calledFrom {}",event.getEventId(),expand,isAdminOrStaff,showPastAndUpcoming,calledFrom);
        PastUpComingDto pastUpComingDto = getPastUpComingDtoDetail(event, sessionFilter, showPastAndUpcoming);
        Page<Session> sessionsPage = sessionRepoService.getAllSessionByEventId(event, pageable, sessionFilter, user, isAdminOrStaff, calledFrom);
        return getDisplayPortalSessionDataTable(event, user, expand, sessionsPage, pastUpComingDto);
    }

    private boolean isAcceleventSessionLive(SessionEndDateTypeDto session) {
        if (null != session && null != session.getStreamProvider() &&
                StreamProvider.ACCELEVENTS.name().equals(session.getStreamProvider()) &&
                session.getSessionStartStatus() != null && session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED.name())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    public PastUpComingDto getPastUpComingDtoDetail(Event event, SessionFilter sessionFilter, Boolean showPastAndUpcoming) {
        List<SessionEndDateTypeDto> sessions = sessionRepo.getSessionEndDateTypeByEvent(event);
        Date now = new Date();

        boolean isPastSession = false;
        boolean isUpComingSession = false;

        Stream<SessionEndDateTypeDto> sessionStream = sessions.stream();
        if (StringUtils.isNotBlank(sessionFilter.getSessionFormat())) {
            String sessionFormat = sessionFilter.getSessionFormat();
            sessionStream = sessionStream.filter(s -> s.getFormat() != null && s.getFormat().name().equals(sessionFormat));
        }

        for (SessionEndDateTypeDto session : sessionStream.collect(Collectors.toList())) {
            boolean isLive = isAcceleventSessionLive(session);
            if(session != null && session.getEndTime() != null){
                Date endTime = session.getEndTime();
                if (!isLive && endTime.before(now)) {
                    isPastSession = true;
                }
                if (isLive || endTime.after(now)) {
                    isUpComingSession = true;
                }

                if (isPastSession && isUpComingSession) {
                    break;
                }
            }
        }
        if (sessionFilter.getPast() == null && showPastAndUpcoming == null) {
            sessionFilter.setShowPastAndUpcoming(!isUpComingSession && isPastSession);
        }

        return new PastUpComingDto(isPastSession, isUpComingSession);
    }



    private void checkMaxAgendaItemsByPlanType(Event event, long sessionCount) {
        if (!IN_PERSON.equals(event.getEventFormat())){
            chargebeeService.checkMaxAgendaItemsByPlanType(event, sessionCount);
        }
    }

    public DataTableResponseForSession getDisplayPortalSessionDataTable(Event event, User loggedInUser, String expand, Page<Session> sessionsPage, PastUpComingDto pastUpComingDto) {
//        List<Long> sessionLocationIds = sessionsPage.getContent().stream().map(Session::getLocationId).collect(toList());
//        List<SessionLocationDTO> LocationSourceUrls = sessionLocationRepo.findSourceUrlByIdsAndEventId(sessionLocationIds, event.getEventId());
//        Map<Long, String> locationIdToSourceUrlMap = LocationSourceUrls.stream().collect(Collectors.toMap(SessionLocationDTO::getId, SessionLocationDTO::getSourceUrl));
        SessionExpandable expandable = new SessionExpandable(expand, sessionsPage, loggedInUser, event).invoke();
        Map<Long, Boolean> finalMap = new HashMap<>();
        List<Long> userSessionCalendarsIds=new ArrayList<>();
        if(null != loggedInUser){
            List<Long> sessionIds = sessionsPage.getContent().stream().map(Session::getId).collect(toList());
            if (!sessionIds.isEmpty()) {
                userSessionCalendarsIds = userSessionRepoService.getAddToCalendarEventSessionByUserIdAndSessionId(sessionIds, loggedInUser.getUserId(), event.getEventId());
                List<Object[]> listOfUserSession = userSessionRepoService.getCheckInSessionByUserIdAndSessionId(sessionIds, loggedInUser.getUserId());
                    if (!CollectionUtils.isEmpty(listOfUserSession)) {
                        Map<Long, Boolean> map = listOfUserSession.stream().collect(Collectors.toMap(obj -> (Long) obj[0], obj -> (Boolean) obj[1], (first, second) -> first));
                        finalMap.putAll(map);
                    }
            }
        }
        List<Long> finalUserSessionCalendarsIds = userSessionCalendarsIds;
        List<DisplayPortalSessionDTO> displayPortalSessionDTOS =
                sessionsPage
                        .getContent()
                        .stream()
                        .map(e -> new DisplayPortalSessionDTO(e,
                                event,
                                expandable.getSessionSpeakers().get(e.getId()),
                                expandable.getCurrentUserSessionIdAndEventTicketIdMap().get(e.getId()),
                                expandable.getSessionSponsors(e.getSponsorExhibitorJson()),
                                expandable.getSessionTagsAndTracks(e.getId()),
                                expandable.getSessionRecordBySession(e.getId()),
                                expandable.getHideVideoControlsBySession(e.getId()),
                                expandable.getExhibitors(e.getSponsorExhibitorJson()),
                                expandable.enableWaitingMedia(e.getId()),
                                expandable.getSessionWaitingMedia(e.getId()),
                                expandable.getSessionAssetsDetail().get(e.getId()),
                                expandable.getSessionBookmarkCapacity().get(e.getId()),
                                !finalUserSessionCalendarsIds.isEmpty() && finalUserSessionCalendarsIds.contains(e.getId())? true : false,
                                finalMap.get(e.getId()),
                                expandable.getSessionBookmarked().get(e.getId())
                        ))
                        .collect(toList());

        DataTableResponseForSession dataTableResponseForSession = new DataTableResponseForSession();
        dataTableResponseForSession.setRecordsTotal(sessionsPage.getTotalElements());
        dataTableResponseForSession.setRecordsFiltered(sessionsPage.getContent().size());
        dataTableResponseForSession.setData(displayPortalSessionDTOS);
        dataTableResponseForSession.setPastSession(pastUpComingDto.isPastSession());
        dataTableResponseForSession.setUpComingSession(pastUpComingDto.isUpComingSession());
        log.info("SessionServiceImpl | getDisplayPortalSessionDataTable | isPastSession : {} | isUpComingSession : {}", pastUpComingDto.isPastSession(), pastUpComingDto.isUpComingSession());

        return dataTableResponseForSession;
    }

    @Override
    public List<String> findSessionTitleByEventIdOrderByIdAsc(Long eventId) {
        return sessionRepoService.findSessionTitleByEventIdOrderByIdAsc(eventId);
    }

    public DisplayPortalCommonSessionDTO getDisplayPortalSessionInfoById(Long sessionId, Event event, User user, String expand, boolean isAdminOrStaff) {
        Session session = sessionRepoService.getSessionByIdJoinFetchTagsTrack(sessionId, event);
        if (user != null && !isAdminOrStaff && !isValidUser(user, event, session)) {
            if(speakerService.isSpeakerInEvent(event,user) || staffService.isEventExhibitorAdminOrLeadretriever(event,user)){
                throw new NotAcceptableException(STAFF_NOT_REGISTER_IN_SESSION);
            }

            log.warn("user can not access this hidden session with no ticket types selected {}", session.getId());
            throw new NotFoundException(NotFoundException.SessionNotFound.TICKET_TYPE_DOES_NOT_GRANT_ACCESS);
        }

        if(isDraftSession(session)){
            throw new NotAcceptableException(DRAFT_SESSION_NOT_ACCESSIBLE);
        }

        DisplayPortalCommonSessionDTO dto;
        if (EnumSessionFormat.MEET_UP.equals(session.getFormat())) {
            Optional<SessionDetails> sessionDetails = sessionDetailsService.getSessionDetailsBySession(session);
            dto = new DisplayPortalNetworkingSessionDTO(session, event, sessionDetails);
        } else {
            Optional<SessionDetails> sessionDetails = sessionDetailsService.getSessionDetailsBySession(session);
            List<SpeakerDTO> speakerList = expand.contains(SPEAKER) ? sessionSpeakerService.getSpeakerDtoBySession(session.getId()) : Collections.emptyList();
            List<Long> keyValueIds = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toList());
            List<IdNameDto> tracks = expand.contains("TRACK") ? getIdNameDto(keyValueService.findAllByIds(keyValueIds), TRACK) : Collections.emptyList();
            dto = new DisplayPortalStageBreakoutSessionDTO(session, event, sessionDetails, speakerList, tracks);
        }

        if (expand.contains("STATS")) {
            Integer registerCount = userSessionService.countRegisteredBySessionId(sessionId);
            Integer interestedCount = userInterestedSessionService.countInterestedBySessionId(sessionId);
            long attendeeCount = 0;
            if (expand.contains(SESSION_EXPAND.ATTENDEE_COUNT)) {
                attendeeCount = userSessionService.countSessionAttendeeBySessionId(sessionId);
            }
            dto.setSessionStats(new SessionStatsDto(registerCount, interestedCount, attendeeCount));
        }

        String sponsorExhibitorJson = session.getSponsorExhibitorJson();
        if (expand.contains(Constants.EXHIBITOR_EXPAND)) {
            if (StringUtils.isNotBlank(sponsorExhibitorJson)) {
                List<Long> exhibitorIds = sponsorsService.getExhibitorIdsFromSponsorExhibitorJson(sponsorExhibitorJson);
                List<ExhibitorCarouselDetailDto> exhibitorCarouselDetailDtos = exhibitorService.findExhibitorCarouselDetailByIds(exhibitorIds);
                dto.setExihiborList(exhibitorCarouselDetailDtos);
            } else {
                dto.setExihiborList(Collections.emptyList());
            }
        }

        if (expand.contains(Constants.SPONSOR_EXPAND)) {
            if (StringUtils.isNotBlank(sponsorExhibitorJson)) {
                List<Long> sponsorIds = sponsorsService.getSponsorIdsFromSponsorExhibitorJson(sponsorExhibitorJson);
                List<Sponsors> sponsors = sponsorsService.getSponsorsByIds(sponsorIds);
                dto.setSponsorList(sponsors.stream().map(SponsorsDto::new).collect(Collectors.toList()));
            } else {
                dto.setSponsorList(Collections.emptyList());
            }
        }

        if (expand.contains("registerdHolderUsers")) {
            dto.setRegisterdHolderUsers(userSessionService.findRegisteredUserInfo(session.getId()));
        }

        if (user != null && expand.contains("currentUserRegisteredEventTicketId")) {
            dto.setCurrentUserRegisteredEventTicketId(userSessionService.getEventTicketIdsByEventIdAndUserIdAndSessionId(event.getEventId(), user.getUserId(), sessionId));
            dto.setCurrentUserRemainingSessionRegistered(userSessionService.getUserRemainingRegisteredCountByEventIdAndUserId(event.getEventId(), user.getUserId()));
        }

        if (expand.contains("PLAYBACKS")) {
            dto.setAssetList(muxLivestreamAssetService.findAllLivestreamAssetsForSessionBySessionIdAndType(session.getId(), AssetType.SESSION_ASSET));
        }

        Integer registerCounts;
        if(session.getFormat().equals(EnumSessionFormat.WORKSHOP)){
            registerCounts = userSessionService.countRegisteredBySessionId(sessionId);
        }else {
            registerCounts = userSessionService.getSessionRegisterCountAndTicketIdIsNotNullAndEventId(sessionId, event.getEventId());
        }
        boolean isAllowSessionBookmarkCapacity = virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId());
        log.info("getDisplayPortalSessionInfoById | isAllowSessionBookmarkCapacity {}, register counts {}", isAllowSessionBookmarkCapacity,registerCounts);
        dto.setSessionBookmarkCapacityReached(isAllowSessionBookmarkCapacity && session.getCapacity() != 0 && registerCounts >= session.getCapacity());
        log.info("getDisplayPortalSessionInfoById | sessionBookmarkCapacityReached {}", dto.isSessionBookmarkCapacityReached());
        dto.setShortDescriptionOfSession(session.getShortDescriptionOfSession());
        dto.setSurveyEnabled(session.isSurveyEnabled());
        dto.setSurveyId(session.getSurveyId());
        return dto;
    }

    private boolean sessionVisibleForUser(Session session) {
        log.warn("sessionVisibleForUser sessionId {} sessionFormat {}", session.getId(),session.getStatus());
        return EnumSessionStatus.HIDDEN.equals(session.getStatus()) && SessionVisibilityType.PUBLIC.equals(session.getSessionVisibilityType());
    }

    @Override
    public boolean isSpeakerIsValidAttendee(User user, Event event, Session session, List<Long> userTicketTypes) {
        boolean isValidUser = EnumSessionStatus.HIDDEN.equals(session.getStatus()) ? Boolean.FALSE : Boolean.TRUE;
        log.warn("isValidUser sessionId {} sessionFormat {}", session.getId(), session.getStatus());
        if (!sessionVisibleForUser(session) && (CollectionUtils.isEmpty(userTicketTypes) || (session.isHideSessionFromAttendees()) || (EnumSessionStatus.HIDDEN.equals(session.getStatus())) || (speakerService.isSpeakerInEvent(event, user)))) {
                List<Long> eventTicketTypeIds = Stream.of(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                        .filter(ticketId -> !ticketId.isEmpty()).map(Long::parseLong)
                        .collect(Collectors.toList());
                log.warn("isValidUser Collection disjoint {}", !Collections.disjoint(eventTicketTypeIds, userTicketTypes));
                isValidUser = !Collections.disjoint(eventTicketTypeIds, userTicketTypes);

        }
        log.warn("isValidUser isValidUser {}", isValidUser);
        return isValidUser;
    }

    @Override
    public boolean isValidUser(User user, Event event, Session session)
    {
        boolean isValidUser = EnumSessionStatus.HIDDEN.equals(session.getStatus()) ? Boolean.FALSE : Boolean.TRUE;
        log.warn("isValidUser sessionId {} sessionFormat {}", session.getId(),session.getStatus());
        if (!sessionVisibleForUser(session)) {
            if(!MEET_UP.equals(session.getFormat()) && sessionSpeakerService.isUserSpeakerInSession(user.getUserId(),session.getId())) {
                return true;
            }
            List<Long> userTicketTypes;
            userTicketTypes = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user);
            if (CollectionUtils.isEmpty(userTicketTypes) || (session.isHideSessionFromAttendees()) || (EnumSessionStatus.HIDDEN.equals(session.getStatus())) || (speakerService.isSpeakerInEvent(event, user))) {
                List<Long> eventTicketTypeIds = Stream.of(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                        .filter(ticketId->!ticketId.isEmpty()).map(Long::parseLong)
                        .collect(Collectors.toList());
                log.warn("isValidUser Collection disjoint {}", !Collections.disjoint(eventTicketTypeIds, userTicketTypes));
                isValidUser = !Collections.disjoint(eventTicketTypeIds, userTicketTypes);
            }
        }
        log.warn("isValidUser isValidUser {}", isValidUser);
        return isValidUser;
    }

    @Override
    public boolean isValidUserForSession(Session session, List<Long> userTicketTypes, boolean isSpeakerInSession, boolean isSpeakerInEvent) {
        EnumSessionStatus status = session.getStatus();
        SessionVisibilityType visibilityType = session.getSessionVisibilityType();

        boolean isHidden = EnumSessionStatus.HIDDEN.equals(status);
        boolean isPubliclyVisible = isHidden && SessionVisibilityType.PUBLIC.equals(visibilityType);

        log.warn("isValidUserForSession sessionId {} sessionStatus {}", session.getId(), status);

        if (isHidden && !isPubliclyVisible) {
            return false;
        }

        if (!isPubliclyVisible) {
            if (!MEET_UP.equals(session.getFormat()) && isSpeakerInSession) {
                return true;
            }

            boolean ticketCheckRequired = CollectionUtils.isEmpty(userTicketTypes)
                    || session.isHideSessionFromAttendees()
                    || isHidden
                    || isSpeakerInEvent;

            if (ticketCheckRequired) {
                List<Long> sessionTicketTypeIds = Stream.of(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                        .filter(ticketId -> !ticketId.isEmpty())
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                return !Collections.disjoint(sessionTicketTypeIds, userTicketTypes);
            }
        }
        return true;
    }



    @Override
    public boolean isDraftSession(Session session){
        return EnumSessionStatus.DRAFT.equals(session.getStatus());
    }


    private void checkSessionIsExistOrNot(String sessionName, EnumSessionFormat sessionFormatName, List<Session> sessionList) {
        if (!BREAK.equals(sessionFormatName) && !EXPO.equals(sessionFormatName) && !OTHER.equals(sessionFormatName)) {
            throw new NotAcceptableException(SESSION_ALREADY_EXIST);
        } else {
                if(sessionList.stream().anyMatch(session -> session.getTitle().equals(sessionName))) {
                    throw new NotAcceptableException(SESSION_ALREADY_EXIST);
                }
         }
    }

    @Override
    public List<SessionIdTitleDto> getSessionDropDownList(Event event, List<EnumSessionFormat> sessionFormat) {

        List<Session> sessions = sessionRepoService.findByEventAndFormat(event.getEventId(), sessionFormat);
        sessions.removeIf(e->EnumSessionStatus.DRAFT.equals(e.getStatus()));
        List<SessionIdTitleDto> sessionIdTitleDtoList = new ArrayList<>();
        for (Session session : sessions) {
            List<Long> tags = new ArrayList<>();
            List<Long> tracks = new ArrayList<>();

            Set<SessionTagAndTrack> sessionTagAndTracks = session.getSessionTagAndTracks();
            if (!CollectionUtils.isEmpty(sessionTagAndTracks)) {
                for (SessionTagAndTrack sessionTagAndTrack : sessionTagAndTracks) {
                    if (TAG.equals(sessionTagAndTrack.getTagOrTrack().getType())) {
                        tags.add(sessionTagAndTrack.getTagOrTrackId());
                    } else if (TRACK.equals(sessionTagAndTrack.getTagOrTrack().getType())) {
                        tracks.add(sessionTagAndTrack.getTagOrTrackId());
                    }
                }
            }
            sessionIdTitleDtoList.add(new SessionIdTitleDto(session.getId(), session.getTitle(), tags, tracks, session.getFormat()));
        }
        return sessionIdTitleDtoList;
    }

    @Override
    @javax.transaction.Transactional
    public void updateSessionSequence(Long sessionId, Long topSessionId, Long topBottomSessionId, Event event) {
        Session sessionById = getSessionById(sessionId, event);
        updateWithSequence(sessionById, topSessionId, topBottomSessionId, event);
    }

    private void updateWithSequence(Session session, Long topId, Long topBottomId, Event event) {//NOSONAR

        Optional<Session> topSessionOpt = sessionRepoService.findById(topId);
        Optional<Session> topNextSessionOpt = sessionRepoService.findById(topBottomId);

        //move to middle
        if (topSessionOpt.isPresent() && topNextSessionOpt.isPresent() && session.getStartTime() != null && session.getEndTime() != null) {
            Session topNextSession = topNextSessionOpt.get();
            Session topSession = topSessionOpt.get();
            double position = (topNextSession.getPosition() + topSession.getPosition()) / 2;
            if ( topNextSession.getStartTime() != null && topNextSession.getEndTime() != null && topNextSession.getStartTime().compareTo(session.getStartTime()) == 0
                    && topNextSession.getEndTime().compareTo(session.getEndTime()) == 0) {
                if (1 == position) {
                    Session nextSession = getNextPositionItem(session, event);
                    Session prevSession = getPreviousPositionItem(session, event);
                    double positionDifferent = (nextSession.getPosition() - prevSession.getPosition()) / 2;
                    sessionRepoService.updatePositionSession(event.getEventId(), topSession.getPosition(), session.getPosition(), positionDifferent);
                    session.setPosition(topNextSession.getPosition());
                    save(session);
                } else {
                    session.setPosition(position);
                    save(session);
                }
            } else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_ALLOWED_FOR_CONCURRENT_SESSION);
            }
            //move to top
        } else if (!topSessionOpt.isPresent() && topNextSessionOpt.isPresent()) {
            Session topNextSession = topNextSessionOpt.get();
            if (topNextSession.getStartTime() != null && topNextSession.getEndTime() != null && topNextSession.getStartTime().compareTo(session.getStartTime()) == 0
                    && topNextSession.getEndTime().compareTo(session.getEndTime()) == 0) {
                session.setPosition(topNextSession.getPosition() - SEQUENCE);
                save(session);
            } else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_ALLOWED_FOR_CONCURRENT_SESSION);
            }
            //move at last
        } else if (topSessionOpt.isPresent()) {
            Session topSession = topSessionOpt.get();
            if (topSession.getStartTime() != null && topSession.getEndTime() != null && topSession.getStartTime().compareTo(session.getStartTime()) == 0
                    && topSession.getEndTime().compareTo(session.getEndTime()) == 0) {
                double posDiff = topSession.getPosition() - SEQUENCE;
                if (posDiff >= 1) {
                    sessionRepoService.updatePositionForAllSessionByEventId(SEQUENCE, event.getEventId(), topSession);
                }
                session.setPosition(topSession.getPosition() + SEQUENCE);
                save(session);
            } else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_ALLOWED_FOR_CONCURRENT_SESSION);
            }
        }
    }

    private Session getNextPositionItem(Session session, Event event) {
        return sessionRepoService.nextPositionSession(session.getId(), event.getEventId(), session.getPosition());
    }

    private Session getPreviousPositionItem(Session session, Event event) {
        return sessionRepoService.previousPositionSession(session.getId(), event.getEventId(), session.getPosition());
    }

    @Override
    @Transactional
    public void sortSessionPositionByTitle(Long eventId, String sortType) {

        List<Session> unique = sessionRepoService.getSessionsByStartTimeAndEndTimeUnique(eventId);

        if (null != unique && !unique.isEmpty()) {
            unique.forEach(element -> {
                List<Session> sessionsByTimeAndOrder = sessionRepoService.getSessionsByTimeAndSortType(eventId, element.getStartTime(), element.getEndTime(), sortType);
                if (null != sessionsByTimeAndOrder && !sessionsByTimeAndOrder.isEmpty()) {
                    double tempSequence = SEQUENCE;
                    for (Session subElement : sessionsByTimeAndOrder) {
                        subElement.setPosition(tempSequence);
                        tempSequence = tempSequence + SEQUENCE;
                    }
                    saveAll(sessionsByTimeAndOrder);
                }
            });
        }
    }

    @Override
    public SpeakerRegisterSessionCountDto speakerRegisterSessionCount(Event event, User user,EnumSessionFormat sessionFormat) {
        SpeakerRegisterSessionCountDto usSpeakerRegisterSessionCountDto = new SpeakerRegisterSessionCountDto();
        if (null != user && null != sessionFormat) {
            usSpeakerRegisterSessionCountDto.setRegisterSessionCount(userSessionService.getRegisteredSessionCountForUserBySessionFormat(event.getEventId(), user.getUserId(), sessionFormat));
        } else {
            if (user != null && user.getUserId() != null) {
                usSpeakerRegisterSessionCountDto.setRegisterSessionCount(userSessionService.getRegisteredSessionCountForUser(event.getEventId(), user.getUserId()));
            } else {
                usSpeakerRegisterSessionCountDto.setRegisterSessionCount(0L);
            }
        }
        usSpeakerRegisterSessionCountDto.setSpeakerCount(speakerService.getEventSpeakerCount(event.getEventId()));
        return usSpeakerRegisterSessionCountDto;
    }

    @Override
    @Transactional
    public ResponseDto sessionMassOperation(Event event, User user, EnumSessionMassOperation enumSessionMassOperation, List<Long> sessionIds, Map<String, String> languageMap, SessionMassOperationConfigDTO sessionMassOperationConfigDTO, HttpServletRequest httpRequest, String authToken) throws JSONException {//NOSONAR
        String message = "";
        int counter=0;
        if (!CollectionUtils.isEmpty(sessionIds)) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            log.info("SessionServiceImpl sessionMassOperation eventId {} enumSessionMassOperation {} and operation perform by {} ",event.getEventId(),enumSessionMassOperation.name(), user.getUserId());//NOSONAR
            switch (enumSessionMassOperation) {
                case DELETE:
                    log.info("SessionServiceImpl |  sessionMassOperation | eventId {} and sessions {} to be delete by {} ", event.getEventId(), sessionIds, user.getUserId());
                    for (Long sessionId : sessionIds) {
                        sessionDeleteHandler.deleteSession(sessionId, event, user, authToken);
                        sessionThirdPartyService.deleteSessionChannel(sessionId, event.getEventId());
                        messageToContactService.deleteSessionOrMeetingNotification(sessionId,event.getEventId(),EmailNotificationType.SESSION_NOTIFICATION);
                    }
                    eventTaskService.removeTaskParticipantsFromSpeakerInSession(event,sessionIds);
                    message = resourceBundle.getString(languageMap.get(SESSION_DELETED));
                    return new ResponseDto(SUCCESS, message);
                case DUPLICATE:
                    log.info("SessionServiceImpl |  sessionMassOperation | eventId {} and sessions {} to be duplicate by {} ", event.getEventId(), sessionIds, user.getUserId());
                    List<Long> duplicateSessionIds  = new ArrayList<>();
                    for (Long sessionId : sessionIds) {
                        try {
                            duplicateSessionIds.add(this.createDuplicateSessionBySessionIds(sessionId, event, user, sessionMassOperationConfigDTO, httpRequest));
                            counter++;
                        }catch (NotAcceptableException e){
                            if (e.getErrorCode().equals("4068925")){
                                message = resourceBundle.getString(languageMap.get(SESSION_TIME_ERROR));
                                return new ResponseDto(ERROR_MESSAGE, message);
                            }
                            if (e.getErrorCode().equals("4060075") || e.getErrorCode().equals("4060091") ){
                                if(counter>0){
                                    message = "{session_count} Agenda item not able to duplicate. ";
                                    message = message.replace("{session_count}", String.valueOf(sessionIds.size() - counter));
                                }
                                message += e.getErrorMessage();
                                return new ResponseDto(ERROR_MESSAGE, message);
                            }
                        }
                    }
                    if(!duplicateSessionIds.isEmpty()){
                        log.info("updateEventTaskAndAssignTaskToSpeakerInSession | eventId : {} , duplicateSessionIds : {} ",event.getEventId(),duplicateSessionIds);
                        eventTaskService.updateEventTaskAndAssignTaskToSession(event,duplicateSessionIds);
                    }
                    message = resourceBundle.getString(languageMap.get(SESSION_DUPLICATED));
                    return new ResponseDto(SUCCESS, message);
                case HIDE:
                    log.info("SessionServiceImpl |  sessionMassOperation | eventId {} and sessions {} to be hide by {} ", event.getEventId(), sessionIds, user.getUserId());
                    sessionRepoService.setSessionStatusById(sessionIds, EnumSessionStatus.HIDDEN, event.getEventId());
                    redisCacheService.clearSessionCache(event.getEventId());
                    message = resourceBundle.getString(languageMap.get(SESSION_HIDDEN));
                    return new ResponseDto(SUCCESS, message);
            }
        }
        return null;
    }
    @Override
    public List<Session> getAllSessionByIds(List<Long> sessionIds, Event event) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : sessionRepoService.getAllSessionByIdsAndEventId(sessionIds, event);
    }

    @Override
    @Transactional
    public Long createDuplicateSessionBySessionIds(Long sessionId, Event event, User loggedInUser, SessionMassOperationConfigDTO sessionMassOperationConfigDTO, HttpServletRequest httpRequest) {
        log.info("duplicatedSession  sessionId {}  DuplicateSession  event {} ", sessionId, event.getEventId());
        Session oldSession = sessionRepoService.getSessionByIdWithoutCache(sessionId, event);
        Session duplicatedSession = this.duplicateSessionById(event, oldSession);
        throwErrorIfTimeSlotConflictsDuplicate(event, duplicatedSession.getFormat(), duplicatedSession);
        sessionRepoService.save(duplicatedSession);
        SimpleAsyncTaskExecutor simpleAsyncTaskExecutor = new SimpleAsyncTaskExecutor();
        simpleAsyncTaskExecutor.execute(() -> copySessionData(sessionMassOperationConfigDTO, oldSession, duplicatedSession, event,httpRequest));



        createDuplicateSessionDetails(duplicatedSession, oldSession, sessionMassOperationConfigDTO.isCopyCTA());
        if (EnumSessionFormat.MEET_UP.equals(oldSession.getFormat())) {
            networkingRulesService.createDuplicateNetworkingRule(event.getEventId(), oldSession.getId(), duplicatedSession.getId());
        }

        if(sessionMassOperationConfigDTO.isCopySpeakers()){
            createDuplicateSessionSpeaker(oldSession.getSessionSpeakers(), duplicatedSession, event, loggedInUser);
        }

        createDuplicateSessionTagAndTrack(oldSession.getSessionTagAndTracks(), duplicatedSession);
        createDuplicateSessionSponsorExhibitorJson(oldSession.getSponsorExhibitorJson(), duplicatedSession);

        if (StreamProvider.ACCELEVENTS.equals(duplicatedSession.getStreamProvider())) {
            sessionThirdPartyService.createStreamKey(duplicatedSession.getId(), event, false, new CaptionsDto());
        }
        log.info("findAllEventAdminId event {} ", event.getEventId());
        List<Long> eventAdmins = staffService.findAllEventAdminId(event);
        sessionThirdPartyService.createChannelByListOfSessionIds(Collections.singletonList(duplicatedSession), eventAdmins);
        return  duplicatedSession.getId();
    }

    public void copySessionData(SessionMassOperationConfigDTO sessionMassOperationConfigDTO, Session oldSession, Session duplicatedSession, Event event, HttpServletRequest httpRequest) {
        sessionMassOperationConfigDTO = (sessionMassOperationConfigDTO != null)? sessionMassOperationConfigDTO :new SessionMassOperationConfigDTO();
        if(sessionMassOperationConfigDTO.isCopyPolls()){
            log.info("request received to duplicate Poll for Session {} ",oldSession.getTitle());
            copyPolls(oldSession, duplicatedSession,event,httpRequest);
        }
        if(sessionMassOperationConfigDTO.isCopyQandA()){
            log.info("request received to duplicate Q&A for Session {} ",oldSession.getTitle());
            copyQandA(oldSession, duplicatedSession,httpRequest);
        }
    }


    // Duplicating Polls while duplicating a Session
    private void copyPolls(Session oldSession, Session newSession, Event event, HttpServletRequest httpRequest){
        try{
            PollsQuestionsAnswers graphQLHandler = new PollsQuestionsAnswers(graphQLConfiguration, BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);

            LinkedHashMap<String, JSONObject> allPollQuestions = graphQLHandler.getAllPollQuestions(oldSession.getId(), EnumQNAType.SESSION.name());
            Map<String, JSONArray> pollQuestionResult = mapPollResultByQuestionId(graphQLHandler.getAllAnswersByTypeId(oldSession.getId(), EnumQNAType.SESSION.name()));

            log.info("Total Poll {} for Session {}",allPollQuestions.size(),oldSession.getTitle());
            allPollQuestions.forEach((questionId,questionData)->{
               try{
                   questionData.put(GRAPHQL_TYPEID, newSession.getId());
                   questionData.put(Constants.EVENT_ID, event.getEventId());
                   questionData.remove("createdAt");
                   String newQuestionId = graphQLHandler.createPollQuestions(questionData, httpRequest.getHeader("Authorization"));

                   if (pollQuestionResult.get(questionId) != null && pollQuestionResult.get(questionId).length() > 0) {
                       createPollResult(pollQuestionResult.get(questionId), newQuestionId, graphQLHandler, httpRequest.getHeader("Authorization"));
                   }

                   GeneralUtils.threadSleep(1000L);
               }
               catch (Exception e){
                   log.warn("Failed to duplicate Poll");
               }

            });
        }
        catch (JSONException exception){
            log.warn("error {}  while copying old Session {} polls to new Session {}", exception.getMessage(), oldSession.getId(), newSession.getId());
        }
    }

    private void createPollResult(JSONArray jsonArray, String newQuestionId, PollsQuestionsAnswers graphQLHandler, String authorization) throws JSONException{
        for(int index = 0; index < jsonArray.length(); index++){
            JSONObject jsonObject = jsonArray.getJSONObject(index);
            jsonObject.remove(Constants.ID);
            jsonObject.put("pollsQuestionId",newQuestionId);
            graphQLHandler.createPollResult(jsonObject,authorization);
        }
    }

    private Map<String, JSONArray> mapPollResultByQuestionId(JSONArray allAnswersByTypeId){

        Map<String, JSONArray> result = new HashMap<>();
        try{
            if(allAnswersByTypeId != null){
                for(int index=0; index<allAnswersByTypeId.length(); index++){
                    JSONObject jsonObject = allAnswersByTypeId.getJSONObject(index);
                    String id = jsonObject.getString("pollsQuestionId");
                    if(StringUtils.isNotBlank(id)){
                        if(result.get(id) == null){
                            result.put(id,new JSONArray());
                        }
                        JSONArray jsonArray = result.get(id);
                        jsonArray.put(jsonObject);
                    }
                }
            }
        }
        catch (JSONException e){
            log.warn("enable to map poll result {}",allAnswersByTypeId);
        }
        return result;
    }

    //Duplicating Q&A while duplicating a session
    private void copyQandA(Session oldSession, Session newSession, HttpServletRequest httpRequest){
        try{
            QuestionsAnswers graphQLHandler = new QuestionsAnswers(graphQLConfiguration, BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
            JSONArray questionsAnswers = graphQLHandler.getAllQuestions(oldSession.getId(), EnumQNAType.SESSION.name());
            log.info("Total {} Q&A for Session {}",questionsAnswers,oldSession.getTitle());
            for(int index=0; index<questionsAnswers.length(); index++){
                JSONObject jsonObject = questionsAnswers.getJSONObject(index);
                jsonObject.put(GRAPHQL_TYPEID,newSession.getId());
                graphQLHandler.createPollQuestions(sanitizeQandARequest(jsonObject),httpRequest.getHeader("Authorization"));
            }
        }
        catch (JSONException exception){
            log.warn("error {}  while copying old Session {} polls to new Session {}", exception.getMessage(), oldSession.getId(), newSession.getId());
        }
    }

    private JSONObject sanitizeQandARequest(JSONObject jsonObject)throws JSONException{
        JSONObject result = new JSONObject();
        sanitizeQandARequest("createdAt",jsonObject,result,"Number");
        sanitizeQandARequest("answer",jsonObject,result,"String");
        sanitizeQandARequest("answerType",jsonObject,result,"String");
        sanitizeQandARequest("typeId",jsonObject,result,"Number");
        sanitizeQandARequest("isPrivateQuestion",jsonObject,result,"Boolean");
        sanitizeQandARequest("type",jsonObject,result,"String");
        sanitizeQandARequest("userName",jsonObject,result,"String");
        sanitizeQandARequest("isPrivateResponse",jsonObject,result,"Boolean");
        sanitizeQandARequest("userId",jsonObject,result,"Number");
        sanitizeQandARequest("questionText",jsonObject,result,"String");
        sanitizeQandARequest("status",jsonObject,result,"String");
        sanitizeQandARequest("answeredBy",jsonObject,result,"Number");
        sanitizeQandARequest("answeredAt",jsonObject,result,"Number");

        return result;
    }

    private void sanitizeQandARequest(String key, JSONObject jsonObject, JSONObject result, String dataType) throws JSONException{
        if(jsonObject.has(key) && !jsonObject.isNull(key)){
            switch(dataType){
                case "Number":
                    result.put(key,jsonObject.getInt(key));
                    break;
                case "String":
                    result.put(key,jsonObject.getString(key));
                    break;
                case "Boolean":
                    result.put(key,jsonObject.getBoolean(key));
                    break;
            }
        }
    }

    private Session duplicateSessionById(Event event, Session oldSession) {
        this.checkNonVisualSessionCount(oldSession.getFormat(), event, oldSession.getFormat(), false);
        Session duplicatedSession = (Session) oldSession.clone();
        duplicatedSession.setId(0);
        duplicatedSession.setTitle(oldSession.getTitle());
        duplicatedSession.setSessionSpeakers(Collections.emptySet());
        duplicatedSession.setSessionTagAndTracks(Collections.emptySet());
        duplicatedSession.setPlayBackRestrictionToken(null);

        duplicatedSession.setSessionStartStatus(EnumSessionStartStatus.NULL);
        duplicatedSession.setTaskId(null);
        duplicatedSession.setRecordStatus(RecordStatus.CREATE);
        duplicatedSession.setRtmpUrl(null);
        duplicatedSession.setLiveStreamId(null);
        duplicatedSession.setStreamKey(null);
        duplicatedSession.setStreamUrl(null);
        duplicatedSession.setMeetingPassword(null);
        duplicatedSession.setMeetingObj(null);
        duplicatedSession.setDirectVideoAutoStart(false);
        duplicatedSession.setShortDescriptionOfSession(oldSession.getShortDescriptionOfSession());
        if(NumberUtils.isNumberGreaterThanZero(duplicatedSession.getSessionAttributeId())){
            CustomFormAttributeData customFormAttributeData = customFormAttributeDataRepository.findById(duplicatedSession.getSessionAttributeId()).orElse(null);
            if(customFormAttributeData != null){
                CustomFormAttributeData newCustomFormAttributeData = (CustomFormAttributeData) customFormAttributeData.clone();
                newCustomFormAttributeData.setId(0L);
                duplicatedSession.setSessionAttributeId(customFormAttributeDataRepository.save(newCustomFormAttributeData).getId());
            }
            else{
                duplicatedSession.setSessionAttributeId(0L);
            }
        }
        return duplicatedSession;
    }

    @Override
    public DataTableResponse getAllCommandCenterSessionList(String expand, Event event, User user, Pageable pageable, boolean isAdminOrStaff) {
        Page<Session> sessionsPage = sessionRepoService.getAllCommandCenterSessionList(event, pageable,  user, isAdminOrStaff);

        SessionExpandable expandable = new SessionExpandable(expand, sessionsPage, user, event).invoke();
        List<SessionDTO> sessionDTOS =
                sessionsPage
                        .getContent()
                        .stream()
                        .map(e -> new SessionDTO(e,
                                event,
                                expandable.getSessionSpeakers().get(e.getId()),
                                expandable.getSessionAssetsDetail().get(e.getId())))
                        .collect(toList());
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(sessionsPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(sessionsPage.getContent().size());
        dataTableResponse.setData(sessionDTOS);
        return dataTableResponse;
    }

    @Override
    public void resetSessionDates(List<Long> listOfEventId, int days) {
        log.info("Start of reset session dates for eventIds {} and for {} days",listOfEventId,days);
        List<Session> listOfSession = sessionRepoService.getSessionByEventIds(listOfEventId);
        List<Session> updatedList = new ArrayList<>();
        if(!listOfSession.isEmpty()){
            listOfSession.stream().filter(e->e.getStartTime() != null && e.getEndTime() != null).forEach(session -> {
                session.setStartTime(DateUtils.addDaysInDate(session.getStartTime(),days));
                session.setEndTime(DateUtils.addDaysInDate(session.getEndTime(),days));
                session.setUpdatedAt(new Date());
                updatedList.add(session);
            });
            sessionRepoService.saveAll(updatedList);
        }
        log.info("End of reset session dates for eventIds {} and for {} days",listOfEventId,days);
    }

    @Override
    public Session getFirstConcurrentSession(Session session) {
        return sessionRepoService.getFirstConcurrentSession(session.getEventId(), session.getStartTime(), session.getEndTime());
    }

    @Override
    public PostSessionCallToActionDropDownDto getFutureSessionExpoLoungesByEvent(Event event) {
        List<SessionIdNameTypeDto> sessions = sessionRepoService.getUpcomingSessionByEvent(event, DateUtils.getCurrentDate());
        List<ExhibitorDropDownDto> exhibitors = exhibitorService.getExhibitorDropDown(event);
        List<NetworkingLounge> networkingLounges = neptuneNetworkingLoungeService.getEventNetworkingLounges(event);
        List<KeyValueDto> loungesIdName = Collections.emptyList();
        if (networkingLounges != null) {
            loungesIdName = networkingLounges.stream().map(networkingLounge -> new KeyValueDto(networkingLounge.getId(), networkingLounge.getName())).collect(Collectors.toList());
        }
        log.info("SessionServiceImpl getFutureSessionExpoLoungesByEvent eventID {}",event.getEventId());
        return new PostSessionCallToActionDropDownDto(sessions, exhibitors, loungesIdName);
    }

    @Override
    public PostSessionCallToActionDropDownDto getFutureSessionExpoLoungesByEventAndSessionId(Event event, long sessionId) {

        Session currentSession = sessionRepoService.getSessionById(sessionId,event);
        List<SessionIdNameTypeDto> sessions = sessionRepoService.getUpcomingSessionByEventAndEndDateOfCurrentSession(event, currentSession.getEndTime());
        List<ExhibitorDropDownDto> exhibitors = exhibitorService.getExhibitorDropDown(event);
        List<NetworkingLounge> networkingLounges = neptuneNetworkingLoungeService.getEventNetworkingLounges(event);
        List<KeyValueDto> loungesIdName = Collections.emptyList();
        if (networkingLounges != null) {
            loungesIdName = networkingLounges.stream().map(networkingLounge -> new KeyValueDto(networkingLounge.getId(), networkingLounge.getName())).collect(Collectors.toList());
        }
        log.info("SessionServiceImpl getFutureSessionExpoLoungesByEvent eventID {}",event.getEventId());
        return new PostSessionCallToActionDropDownDto(sessions, exhibitors, loungesIdName);
    }

    @Override
    public PostSessionCallToActionDto sessionOrLoungeOrExpoExistingOrUpcoming(Event event, User user, Long sessionId, boolean isAdminOrStaff) {
        log.info("SessionServiceImpl sessionOrLoungeOrExpoExistingOrUpcoming eventID {} userID {} id {}",event.getEventId(),user.getUserId(),sessionId);
        Optional<SessionDetails> optionalSessionDetails = sessionDetailsRepoService.findBySessionId(sessionId);
        PostSessionCallToActionDto postSessionCallToActionDto = new PostSessionCallToActionDto();
        Boolean isValidSession = false;
        if(optionalSessionDetails.isPresent()) {
            SessionDetails sessionDetails = optionalSessionDetails.get();
            postSessionCallToActionDto = CommonUtil.getDtoPostCallToActionJson(sessionDetails.getPostSessionCallToActionJson());
            if(isDestinationAndDestinationType(postSessionCallToActionDto)) {
                PostSessionCallToActionTypeEnum destinationType = postSessionCallToActionDto.getDestinationType();
                String destinationSessionId = postSessionCallToActionDto.getDestination();
                if (PostSessionCallToActionTypeEnum.LOUNGE.equals(destinationType)) {
                    postSessionCallToActionDto.setValidSession(null != neptuneNetworkingLoungeService.getNetworkingLounge(destinationSessionId));
                    return postSessionCallToActionDto;
                } else if (PostSessionCallToActionTypeEnum.EXHIBITOR.equals(destinationType)) {
                    postSessionCallToActionDto.setValidSession(exhibitorService.isExhibitorExisting(destinationSessionId));
                    return postSessionCallToActionDto;
                }
                isValidSession = isValidUserForPostSessionCTA(event, user, isAdminOrStaff, isValidSession, destinationType, destinationSessionId);
            }
        }
        postSessionCallToActionDto.setValidSession(isValidSession);
        return postSessionCallToActionDto;
    }

    private Boolean isValidUserForPostSessionCTA(Event event, User user, boolean isAdminOrStaff, Boolean isValidSession, PostSessionCallToActionTypeEnum destinationType, String destinationSessionId) {
        Session session = sessionRepoService.isSessionExistingOrUpcoming(destinationType, destinationSessionId);
        if (session != null && (isAdminOrStaff || sessionSpeakerService.isUserSpeakerInSession(user.getUserId(), session.getId()) ||
                    sessionSpeakerService.isSpeakerModeratorInSession(user.getUserId(), session.getId()).booleanValue() || !CollectionUtils.isEmpty( userSessionRepoService.findBySessionIdAndUserIdAndEventId(session.getId(), user.getUserId(), event.getEventId()))) ) {
               isValidSession = Boolean.TRUE;
        } else if (isSessionPublicAndVisibleToAttendee(session)) {
                List<Long> userTicketTypes = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user);
                List<Long> ticketThatCanBeRegistered = GeneralUtils.convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered());
               isValidSession = Collections.disjoint(userTicketTypes, ticketThatCanBeRegistered) ? !session.isHideSessionFromAttendees() : Boolean.TRUE;
            }
        return isValidSession;
    }

    private boolean isDestinationAndDestinationType(PostSessionCallToActionDto postSessionCallToActionDto) {
        return StringUtils.isNotBlank(postSessionCallToActionDto.getDestination()) && StringUtils.isNotBlank(postSessionCallToActionDto.getDestinationType());
    }

    private boolean isSessionPublicAndVisibleToAttendee(Session session) {
        return session != null && SessionVisibilityType.PUBLIC.equals(session.getSessionVisibilityType()) && EnumSessionStatus.VISIBLE.equals(session.getStatus());
    }

    private List<Long> isRestrictedTicketTypesExists(Session session, List<Long> ticketTypesThatCanBeRegistered) {
        List<Long> restrictedTicketTypes = new ArrayList<>();
        Set<Long> tagOrTrackIds = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(toSet());
        if(!CollectionUtils.isEmpty(ticketTypesThatCanBeRegistered) && !CollectionUtils.isEmpty(tagOrTrackIds)){
            for(Long eventTicketingTypeId : ticketTypesThatCanBeRegistered){
                List<TicketingTypeTagAndTrack> ticketingTypeTagAndTracks = ticketingTypeTagAndTrackRepoService.findByTicketingTypeIdAndTagOrTrackIdIn(eventTicketingTypeId, tagOrTrackIds);
                if(!CollectionUtils.isEmpty(ticketingTypeTagAndTracks)){
                    restrictedTicketTypes.add(eventTicketingTypeId);
                }
            }
        }
        return restrictedTicketTypes;
    }

    @Override
    public String createWorkshopRecordAssetPlaybackToken(Long assetId) {
        return workshopRecordingAssetService.generateWorkshopRecordingAssetPreSignedUrl(assetId);
    }

    @Override
    public DataTableResponse getCommandCenterSessionList(Event event, User user, Pageable pageable, SessionFilterCommandCenter sessionFilter, String expand) {
        Page<Session> sessionsPage = sessionRepoService.getCommandCenterAllSessionByEventId(event, sessionFilter, pageable);
        return getSessionDataTable(event, null, expand, sessionsPage);
    }

    @Override
    public void startOrStopWorkshopRecording(Long sessionId, Event event, String recordStatus) {
        Session session = sessionRepoService.findById(sessionId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND));
        sessionDetailsRepoService.findBySessionId(sessionId).ifPresent(sessionDetails -> {
            boolean isRecord = START.equalsIgnoreCase(recordStatus);
            sessionDetails.setRecordSession(isRecord);
            sessionDetailsRepoService.save(sessionDetails);
        });
        if (isSessionLive(session) && EnumSessionFormat.WORKSHOP.equals(session.getFormat())) {
            workshopRecordingAssetsRepoService.findBySessionIdAndAttendeeCountIsNotNull(sessionId).ifPresent(assetDetails -> {
                    if (START.equalsIgnoreCase(recordStatus)) {
                        workshopSessionRecordingService.startChimeMediaCapturePipeLine(session, event, null);
                    } else {
                        ChimeMessageDetailsDto chimeMessageDetailsDto = new ChimeMessageDetailsDto();
                        chimeMessageDetailsDto.setEventType(CHIME_MEETING_ENDED);
                        workshopSessionRecordingService.stopChimeMediaCapturePipeLine(session, event, CHIME_MEETING_ENDED, chimeMessageDetailsDto);
                    }
            });

        }
    }

    @Override
    public DataTableResponse getSortedSessionWithDateAndTime(String expand,
                                                             Event event,
                                                             Pageable pageable,
                                                             String sortField, String sortDirection) {
        Pageable pageables = getSortablePage(pageable.getPageNumber(), pageable.getPageSize(), sortField, sortDirection);
        Page<Session> sessionsPage = sessionRepo.getAllSortedSessionWithDateAndTimeForHost(event.getEventId(), pageables);
        return getSessionDataTable(event, null, expand, sessionsPage);
    }

    private Pageable getSortablePage(int page, int size, String sortField, String sortDirection) {
        if (org.apache.commons.lang3.StringUtils.isNoneBlank(sortField)) {
            Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;
            return PageRequest.of(page, size, direction, "startTime");
        } else {
            return PageRequest.of(page, size, Sort.Direction.ASC, "startTime");
        }
    }

    @Override
    public SessionDashboardAnalyticsDto getSessionDashboardAnalytics(Event event, User loggedInUser) {
        log.info("Start of session dashboard analytics event {}, user {} ", event.getEventId(), loggedInUser.getUserId());
        SessionDashboardAnalyticsDto dashboardAnalyticsDto = new SessionDashboardAnalyticsDto();
        List<Session> sessions = findSessionByEventId(event);
        if (CollectionUtils.isEmpty(sessions)) {
            return dashboardAnalyticsDto;
        }

        List<Long> sessionIds = sessions.stream().map(Session::getId).collect(toList());
        double totalSessions = sessions.size();
        dashboardAnalyticsDto.setTotalSessions((long) totalSessions);

        Integer totalAttendee = userSessionService.countSessionAttendeeByEventId(event.getEventId());
        Double avgAttendee = DoubleHelper.roundValueTwoDecimal(totalAttendee / totalSessions);
        dashboardAnalyticsDto.setAvgAttendee(avgAttendee);

        Double totalDuration = sessionDetailsService.getSessionVideoDurationsByEvent(event).values().stream().collect(Collectors.summingDouble(e -> e));
        Double avgDuration = DoubleHelper.roundValueTwoDecimal(totalDuration / totalSessions);
        dashboardAnalyticsDto.setAvgDuration(avgDuration);


        log.info("Successfully session dashboard analytics fetched for event {}, Results totalSessions {}, avgAttendee {}, avgDuration {}, ", event.getEventId(), totalSessions, avgAttendee, avgDuration );
        return dashboardAnalyticsDto;
    }

    @Override
    public List<Session> findSessionsByTagOrTrackIds(List<Long> tagOrTrackIds) {
        return  CollectionUtils.isEmpty(tagOrTrackIds)  ? Collections.emptyList() : sessionTagAndTrackService.findSessionsByTagOrTrackIds(tagOrTrackIds);
    }

    @Override
    public VonageSessionDto getSessionStreamDetailsById(Long sessionId, Event event, User user) {
        VonageSessionDto vonageSessionDto = new VonageSessionDto();
        String liveStreamId = sessionRepo.findLiveStreamIdBySessionId(sessionId, event.getEventId());
        String broadcastId = sessionRepo.findTaskIdBySessionId(sessionId);
        String vonageSessionId = sessionDetailsRepoService.findVonageSessionIdBySessionId(sessionId);
        vonageSessionDto.setLiveStreamId(liveStreamId);
        vonageSessionDto.setVonageSessionId(vonageSessionId);
        vonageSessionDto.setBroadcastId(broadcastId);
        return vonageSessionDto;
    }

    @Override
    public List<Long> getUserRegisteredSessionsIdsList(Long eventId, Long userId) {
        List<Long> listOfSessionIds = sessionRepo.getListOfSessionIds(eventId, userId);
        return listOfSessionIds != null ? listOfSessionIds : new ArrayList<>();
    }


    @Override
    public String getSessionFormatBySessionId(Long sessionId, Event event, User user) {
        return sessionRepoService.findFormatBySessionId(sessionId, event.getEventId());
    }

    @Override
    public UserSessionAnalyticsDTO getSessionCountWithUserSessionRegistered(Event event) {
        List<Long> sessionIds = roSessionService.findAllSessionIdByEvent(event);
        return new UserSessionAnalyticsDTO(Long.valueOf(sessionIds.size()),
                userSessionRepoService.countUserSessionByEventIdAndCheckInStatusOrCheckInTimeIsNull(event.getEventId()),
                userSessionRepoService.countUserRegisteredByEventIdAndEventTicketIdIsNotNull(event.getEventId()));
    }

    @Override
    public void updateSubTitleFileInSession(Session session, MUXLivestreamAssetDetails muxLivestreamAssetDetails, String captionFileName, String captionFullUrl, String languageCode) {
        if (StringUtils.isNotBlank(captionFileName)) {
            muxService.addMuxSubTitleAndCaption(muxLivestreamAssetDetails, captionFullUrl, session.getTitle(), captionFileName, session.getId(), languageCode);
        }
    }

    @Override
    public void removeSubTitleFileInSession(Long sessionId, Long subtitleId, Event event) {
        Optional<SessionSubtitle> optionalSessionSubtitle = sessionSubtitleRepoService.findById(subtitleId);
        if (optionalSessionSubtitle.isPresent()) {
            SessionSubtitle sessionSubtitle = optionalSessionSubtitle.get();
            MUXLivestreamAssetDetails muxLivestreamAssetDetails = muxLivestreamAssetRepoService.findById(sessionSubtitle.getMuxId());
            if (StringUtils.isEmpty(muxLivestreamAssetDetails.getS3AssetKey())) {
                muxService.removeMuxSubTitleAndCaption(muxLivestreamAssetDetails.getAssetId(),sessionSubtitle);
            }else {
                sessionSubtitleService.deleteById(sessionSubtitle.getId());
            }
        }
    }

    @Override
    public List<Long> getListOfIdsForTagAndTrackForSorting(String tagIds,String trackIds){
        List<Long> tagOrTrackIds = new ArrayList<>();
        if(isNotBlank(tagIds)){
            tagOrTrackIds.addAll(convertCommaSeparatedToListLong(tagIds));
        }
        if(isNotBlank(trackIds)){
            tagOrTrackIds.addAll(convertCommaSeparatedToListLong(trackIds));
        }
        return tagOrTrackIds;
    }

    @Override
    public List<Session> getAllSessionByEvent(Event event){
        return sessionRepo.findByEventId(event.getEventId());
    }

    @Override
    public List<SessionListDto> getAllSessionByEventId(Event event) {
        return sessionRepo.getSessionIdAndTitleByEventId(event.getEventId());
    }

    @Override
    public MuxAssetDTO uploadSessionRecording(Long id, DirectUploadDto uploadDto, Event event, User user, String authToken) throws JSONException {
        MuxAssetDTO muxAssetDTO = sessionThirdPartyService.getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(id,event, uploadDto.getUploadId(), AssetType.SESSION_ASSET,  uploadDto.getTitle(), uploadDto.getDescription());
        if (muxAssetDTO != null) {
            log.info("uploadSessionRecording video eventUrl {} and uploadId {} playBackRestrictionToken {}", uploadDto.getEventUrl(), uploadDto.getUploadId(), uploadDto.getPlayBackRestrictionToken());
            Session session =updateDirectUploadIdAndFile(id, muxAssetDTO.getPlayBackId(), null,event, muxAssetDTO.getPlayBackRestrictionToken(), muxAssetDTO.getThumbnailRestrictionToken());
            log.info("uploadSessionRecording video for session {} with event {}", session.getId(), event.getEventId());
            if (null != muxAssetDTO.getAssetStatus()) {
                String assetStatus = muxAssetDTO.getAssetStatus();
                String status = assetStatus.equals("preparing") ? MuxEventType.VIDEO_ASSET_STATIC_RENDITIONS_PREPARING.getValue()
                        : MuxEventType.VIDEO_ASSET_READY.getValue();
                new BroadcastGraphQLHandler(graphQLConfiguration, authToken)
                        .handleCreateOrUpdateSessionStatusLogs(status, session, user);
            }
        } else {
            throw new NotFoundException(NotFoundException.NotFound.MUX_ASSET_DETAIL_NOT_FOUND);
        }
        return muxAssetDTO;
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public ResponseDto updateSessionsInBulk(Event event, User user,SessionBulkUpdatesDto sessionBulkUpdatesDto) throws JSONException {
        List<Long> sessionIds = sessionBulkUpdatesDto.getSessionIds();
        if(!sessionIds.isEmpty()){
            List<Session> sessionList= sessionRepoService.getAllSessionByIdsAndEventId(sessionIds, event);
            log.info("SessionServiceImpl updateSessionsInBulk eventId {} and sessionIds {} ",event.getEventId(),sessionIds);
            List<SessionDetails> sessionDetailsList = sessionDetailsRepoService.findSessionDetailsBySessionIds(sessionList);
            Map<Long, SessionDetails> sessionDetailsMap = sessionDetailsList.stream()
                    .collect(Collectors.toMap(details -> details.getSession().getId(), sessionDetails -> sessionDetails));
            log.info("SessionServiceImpl updateSessionsInBulk eventId {} and sessionDetailsMap {} ",event.getEventId(),sessionDetailsMap.size());
            Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap=sessionBulkUpdatesDto.getSessionBulkUpdatesAttributeValue();
            Long locationId = 0L;
            if(sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_LOCATION)!=null && StringUtils.isNotBlank(sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_LOCATION).getSessionLocation())){
                Long existingLocationId = sessionLocationRepo.findIdByName(sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_LOCATION).getSessionLocation(),event.getEventId());
                if(NumberUtils.isNumberGreaterThanZero(existingLocationId)) {
                    locationId = existingLocationId;
                }else{
                    SessionLocationDTO sessionLocationDTO = new SessionLocationDTO(0L, sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_LOCATION).getSessionLocation(), null);
                    locationId = sessionLocationService.addSessionLocation(sessionLocationDTO, event, user);
                }
            }
            List<Session> updatedSessionList = new ArrayList<>();
            for (Session session : sessionList) {
                EnumSessionBulkUpdates bulkUpdateType = sessionBulkUpdateMap.keySet().iterator().next();
                switch(bulkUpdateType){
                    case SESSION_FORMAT:
                        updateSessionFormatForBulkUpdate(sessionBulkUpdateMap, session, updatedSessionList,event);
                        break;
                    case SESSION_FORMAT_TYPE:
                        updateSessionFormatTypeForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList,event.getEventFormat());
                        break;
                    case SESSION_LOCATION:
                        updateSessionLocationForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList, locationId);
                        break;
                    case CAPACITY:
                        updateSessionCapacityForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList);
                        break;
                    case SESSION_DATE_TIME:
                        updateSessionDateForBulkUpdates(sessionBulkUpdateMap, session,event,updatedSessionList,sessionDetailsMap);
                        break;
                    case SESSION_WAITING_MEDIA:
                        updateSessionWaitingMediaForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList,event,sessionDetailsMap);
                        break;
                    case SESSION_VISIBILITY:
                        updateSessionVisibilityForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList);
                        break;
                    case MANAGE_SESSIONS_INTERACTIVITY:
                        updateManageSessionInteractivityForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList,sessionDetailsMap);
                        break;
                    case ADVERTISING:
                        updateSessionAdvertisingForBulkUpdates(sessionBulkUpdateMap, session);
                        break;
                    case IFRAME:
                        updateSessionIFameForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList);
                        break;
                    case UPLOAD_DOCUMENTS:
                        updateUploadDocumentsForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList);
                        break;
                    case TAGS:
                        updateTagsAndTracksForBulkUpdates(sessionBulkUpdateMap, session,true,event);
                        break;
                    case TRACKS:
                        updateTagsAndTracksForBulkUpdates(sessionBulkUpdateMap, session,false,event);
                        break;
                    case SPEAKERS:
                        updateSessionSpeakersForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList,event, user);
                        break;
                    case SURVEY:
                        updateSessionSurveyForBulkUpdates(sessionBulkUpdateMap, session, updatedSessionList);
                        break;
                    default:
                        log.info("SessionServiceImpl updateSessionsInBulk bulkUpdateType does not match with any cases eventId {} and sessionIds {} ",event.getEventId(),sessionIds);
                        break;
                }
            }
            SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionDate = sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_DATE_TIME);
            if(sessionBulkUpdatesSessionDate!=null && sessionBulkUpdatesSessionDate.getSessionDateAndTimeDtoList()!=null && !sessionBulkUpdatesSessionDate.getSessionDateAndTimeDtoList().isEmpty()){
                List<Session> existingSessionsList=sessionRepoService.findSessionByEventId(event);
                Map<Long, Session> sessionMap = new HashMap<>();
                for (Session session : existingSessionsList) {
                    if (MAIN_STAGE.equals(session.getFormat())) {
                        sessionMap.put(session.getId(), session);
                    }
                }
                for (Session session : updatedSessionList) {
                    if (MAIN_STAGE.equals(session.getFormat())) {
                        sessionMap.put(session.getId(), session);
                    }
                }
                List<Session> filteredSessionsList = new ArrayList<>(sessionMap.values());

                checkIfMainStageConflictWithOtherSessions(filteredSessionsList,event);
            }
            SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionFormat=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_FORMAT);
            if(sessionBulkUpdatesSessionFormat!=null && MEET_UP.equals(sessionBulkUpdatesSessionFormat.getSessionFormat())){
                sessionSpeakerRepoService.deleteBySessionIds(sessionIds);
            }
            sessionRepoService.saveAll(updatedSessionList);
            log.info("SessionServiceImpl updateSessionsInBulk save successfully eventId {} and updatedSessionList {} ",event.getEventId(),updatedSessionList.size());
        }
        return new ResponseDto(SUCCESS,SUCCESS);
    }

    private void checkIfMainStageConflictWithOtherSessions(List<Session> sessionList,Event event) {
        for (Session session : sessionList) {
            List<String> conflictingSessions = sessionRepo.findSessionsWithConflictingSlot(event.getEventId(), session.getStartTime(),session.getEndTime(), MAIN_STAGE, session.getId());
            if (!conflictingSessions.isEmpty()) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.MAIN_STAGE_SESSIONS_CAN_NOT_BE_OVERLAP_WHILE_BULK_UPDATE);
            }
        }
    }
    private void updateSessionDateForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, Event event,List<Session> updatedSessionList,Map<Long, SessionDetails> sessionDetailsMap) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionDate = sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_DATE_TIME);
        if(sessionBulkUpdatesSessionDate.getSessionDateAndTimeDtoList()!=null && !sessionBulkUpdatesSessionDate.getSessionDateAndTimeDtoList().isEmpty()){
            SessionDateAndTimeDto sessionDateAndTimeDtoData = sessionBulkUpdatesSessionDate.getSessionDateAndTimeDtoList().stream().filter(sessionDateAndTimeDto -> sessionDateAndTimeDto.getSessionId().equals(session.getId())).findFirst().orElse(null);
            if(sessionDateAndTimeDtoData!=null){
                String sessionStartTime = STRING_EMPTY;
                String sessionEndTime = STRING_EMPTY;
                if(sessionDateAndTimeDtoData.getSessionStartTime()!=null && sessionDateAndTimeDtoData.getSessionEndTime()!=null){
                    sessionStartTime = sessionDateAndTimeDtoData.getSessionStartTime();
                    sessionEndTime = sessionDateAndTimeDtoData.getSessionEndTime();
                    validateDateAndTimeForSession(event, sessionStartTime, sessionEndTime, EnumSessionStatus.DRAFT.equals(session.getStatus()));
                    validateSessionStartTimeAndEndTime(sessionStartTime, sessionEndTime, EnumSessionStatus.DRAFT.equals(session.getStatus()));
                    session.setStartTime(getDateInUTC(sessionStartTime, event.getEquivalentTimeZone()));
                    session.setEndTime(getDateInUTC(sessionEndTime, event.getEquivalentTimeZone()));
                }

                if (sessionDetailsMap.containsKey(session.getId())) {
                    SessionDetails sessionDetails = sessionDetailsMap.get(session.getId());
                    sessionDetails.setAllowedMinutesToJoinLate(sessionDateAndTimeDtoData.getAllowedMinutesToJoinLate());
                    sessionDetailsRepoService.save(sessionDetails);
                }
                log.info("update session date and session time for bulk updates updated session startTime {} and endTime {} and sessionId {}", sessionStartTime,sessionEndTime,session.getId());
            }
        }
        updatedSessionList.add(session);
    }

    private void validateSessionStartTimeAndEndTime(String sessionStartTime, String sessionEndTime, boolean isDraftSession) {

        if(isDraftSession){
            return;
        }

        log.info("validate session start time and end time sessionStartTime {} and sessionEndTime {}",sessionStartTime,sessionEndTime);
        if(sessionStartTime.compareTo(sessionEndTime) >= 0){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.START_TIME_SHOULD_BE_LESS_THAN_START_TIME);
        }
        if(sessionEndTime.compareTo(sessionStartTime) <= 0){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.END_TIME_SHOULD_BE_GREATER_THAN_START_TIME);
        }
    }


    private void updateSessionSurveyForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSurvey=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SURVEY);
        if(!SessionTypeFormat.IN_PERSON.equals(session.getSessionTypeFormat()) && sessionBulkUpdatesSurvey.isSurveyEnabled()) {
            session.setSurveyId(sessionBulkUpdatesSurvey.getSurveyId());
            session.setSurveyEnabled(sessionBulkUpdatesSurvey.isSurveyEnabled());
            log.info("update survey from the session bulk updates surveyId {} and sessionId {}",sessionBulkUpdatesSurvey.getSurveyId(),session.getId());
            updatedSessionList.add(session);
        }
    }

    private void updateSessionSpeakersForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList, Event event, User loggedInUser) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSpeakers=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SPEAKERS);
        if (sessionBulkUpdatesSpeakers.isOverrideSpeakersList()) {
            removeExistingSpeakersForTheSession(session, event);
        }
        List<Long> speakerIds=sessionBulkUpdatesSpeakers.getSpeakerIds();
        log.info("update speakers from the session bulk updates speakerIds {} and sessionId {}",speakerIds,session.getId());
        boolean isSpeakerInviteEnable = virtualEventService.isSpeakerInviteEnable(event);
        for (Long speakerId : speakerIds) {
            boolean isAdded = sessionSpeakerService.addSessionSpeaker(session.getId(),speakerId,event,isSpeakerInviteEnable);
            if (isAdded && isSpeakerInviteEnable) {
                speakerHelperService.sendInvite(event, loggedInUser, session.getId(),speakerId);
                log.info("sendInvite to speakers from session bulk updates speakerId {} and sessionId {}",speakerId,session.getId());
            }
        }
    }

    private void removeExistingSpeakersForTheSession(Session session, Event event) {
        List<SessionSpeaker> sessionSpeakers = sessionSpeakerRepoService.getSpeakerUserIdBySessionId(session.getId());
        for (SessionSpeaker sessionSpeaker : sessionSpeakers) {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(null, event);
            sessionSpeakerService.removeSessionSpeaker(session.getId(), sessionSpeaker.getSpeakerId(), event, languageMap, languageCode);
        }
    }

    private void updateTagsAndTracksForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, boolean isTags,Event event) {
        if(isTags) {
            log.info("update tags from the session bulk updates sessionId {}",session.getId());
            SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesTags = sessionBulkUpdateMap.get(EnumSessionBulkUpdates.TAGS);
            if (sessionBulkUpdatesTags.isOverrideTagsList()){
                removeExistingTagsFromTheSessions(session, TAG);
            }
            handleTagsAndTracksForSessionBulkUpdates(session, sessionBulkUpdatesTags.getTags());
        }else {
            log.info("update tracks from the session bulk updates sessionId {}",session.getId());
            SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesTracks=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.TRACKS);
            if (sessionBulkUpdatesTracks.isOverrideTracksList()){
                removeExistingTagsFromTheSessions(session, TRACK);
            }
            handleTagsAndTracksForSessionBulkUpdates(session, sessionBulkUpdatesTracks.getTracks());
        }
        session.setSessionTagAndTracks(new HashSet<>(sessionTagAndTrackService.findBySessionId(session.getId())));
    }

    private void removeExistingTagsFromTheSessions(Session session,EnumKeyValueType type) {
        Set<SessionTagAndTrack> filteredTags = new HashSet<>();
        if(EnumKeyValueType.TAG.equals(type)) {
            filteredTags = session.getSessionTagAndTracks().stream().filter(tagAndTrack -> tagAndTrack.getTagOrTrack().getType() == EnumKeyValueType.TAG).collect(Collectors.toSet());
        }else if (EnumKeyValueType.TRACK.equals(type)){
            filteredTags = session.getSessionTagAndTracks().stream().filter(tagAndTrack -> tagAndTrack.getTagOrTrack().getType() == EnumKeyValueType.TRACK).collect(Collectors.toSet());
        }
        Set<Long> dbTagOrTrackIds = filteredTags.stream().map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toSet());
        if (!dbTagOrTrackIds.isEmpty()) {
            // remove the records.
            sessionTagAndTrackService.deleteRecords(session.getId(), dbTagOrTrackIds);
        }
    }

    private static boolean containsKeyValue(List<KeyValueDto> list, KeyValueDto keyValue) {
        for (KeyValueDto existingKeyValue : list) {
            if (existingKeyValue.equals(keyValue)) {
                return true;
            }
        }
        return false;
    }

    private void updateUploadDocumentsForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesUploadDocuments=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.UPLOAD_DOCUMENTS);
        if (sessionBulkUpdatesUploadDocuments.getDocumentKeyValue()!=null && !sessionBulkUpdatesUploadDocuments.getDocumentKeyValue().isEmpty()) {
            List<KeyValueDto> updatedDocumentList=updateUploadDocument(session,sessionBulkUpdatesUploadDocuments);
            session.setDocuments(convertKeyValuesToJson(updatedDocumentList));
            log.info("update upload documents from the session bulk updates documents {} and sessionId {}",session.getDocuments(),session.getId());
        }
        if (sessionBulkUpdatesUploadDocuments.getLinkKeyValue()!=null && !sessionBulkUpdatesUploadDocuments.getLinkKeyValue().isEmpty()) {
            List<KeyValueDto> updatedLinksList=updateUploadLinks(session,sessionBulkUpdatesUploadDocuments);
            session.setLinks(convertKeyValuesToJson(updatedLinksList));
            log.info("update upload links from the session bulk updates links {} and sessionId {}",session.getLinks(),session.getId());
        }
        updatedSessionList.add(session);
    }

    private List<KeyValueDto> updateUploadLinks(Session session, SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesUploadDocuments) {
        List<KeyValueDto> existingLinkKeyValue = getListOfKeyValueDto(session.getLinks());
        if(sessionBulkUpdatesUploadDocuments.isOverrideLinkList()){
            existingLinkKeyValue = new ArrayList<>();
        }
        List<KeyValueDto> newLinkKeyValue=sessionBulkUpdatesUploadDocuments.getLinkKeyValue();
        List<KeyValueDto> combinedList = new ArrayList<>();
        for (KeyValueDto keyValue : existingLinkKeyValue) {
            if (!containsKeyValue(combinedList, keyValue)) {
                combinedList.add(keyValue);
            }
        }
        for (KeyValueDto keyValue : newLinkKeyValue) {
            if (!containsKeyValue(combinedList, keyValue)) {
                combinedList.add(keyValue);
            }
        }
        return combinedList;
    }

    private List<KeyValueDto> updateUploadDocument(Session session,SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesUploadDocuments) {
        List<KeyValueDto> existingDocumentKeyValue = getListOfKeyValueDto(session.getDocuments());
        if(sessionBulkUpdatesUploadDocuments.isOverrideDocumentList()){
            existingDocumentKeyValue = new ArrayList<>();
        }
        List<KeyValueDto> newDocumentKeyValue=sessionBulkUpdatesUploadDocuments.getDocumentKeyValue();
        List<KeyValueDto> combinedList = new ArrayList<>();
        for (KeyValueDto keyValue : existingDocumentKeyValue) {
            if (!containsKeyValue(combinedList, keyValue)) {
                combinedList.add(keyValue);
            }
        }
        for (KeyValueDto keyValue : newDocumentKeyValue) {
            if (!containsKeyValue(combinedList, keyValue)) {
                combinedList.add(keyValue);
            }
        }
        return combinedList;
    }

    private void updateSessionIFameForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesIframe=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.IFRAME);
        if(sessionBulkUpdatesIframe!=null && sessionBulkUpdatesIframe.getClosedCaptionUrl() !=null) {
            session.setClosedCaptionHeight(sessionBulkUpdatesIframe.getClosedCaptionHeight());
            session.setClosedCaptionUrl(sessionBulkUpdatesIframe.getClosedCaptionUrl());
            log.info("update IFrame from the session bulk updates closedCaptionUrl {} and sessionId {}",sessionBulkUpdatesIframe.getClosedCaptionUrl(),session.getId());
            updatedSessionList.add(session);
        }
    }

    private void updateSessionAdvertisingForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesAdvertising=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.ADVERTISING);
        SponsorExhibitorDto sponsorExhibitorDto = null;
        isOverrideExpoAndSponsorList(sessionBulkUpdatesAdvertising, session, sponsorExhibitorDto);
        addNewExpoAndSponsorList(sessionBulkUpdatesAdvertising, session, sponsorExhibitorDto);
    }

    private void addNewExpoAndSponsorList(SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesAdvertising, Session session, SponsorExhibitorDto sponsorExhibitorDto) {
        if(sessionBulkUpdatesAdvertising.getExpoIds()!=null && !sessionBulkUpdatesAdvertising.getExpoIds().isEmpty()) {
            List<Long> expoIds = sessionBulkUpdatesAdvertising.getExpoIds();
            for (Long expoId : expoIds) {
                exhibitorService.addExhibitorToSessionFromTheSessionBulkUpdates(session, expoId);
            }
            log.info("update expo from the session bulk updates expoIds {} and sessionId {}",expoIds,session.getId());
        }
        if (sessionBulkUpdatesAdvertising.getSponsorIds()!=null && !sessionBulkUpdatesAdvertising.getSponsorIds().isEmpty()) {
            List<Long> sponsorIds = sessionBulkUpdatesAdvertising.getSponsorIds();
            for (Long sponsorId : sponsorIds) {
                sponsorsService.addSponsorToSessionFromTheSessionBulkUpdates(session, sponsorId);
            }
            log.info("update sponsors from the session bulk updates sponsorIds {} and sessionId {}",sponsorIds,session.getId());
        }
    }

    private void isOverrideExpoAndSponsorList(SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesAdvertising, Session session, SponsorExhibitorDto sponsorExhibitorDto) {
        if(sessionBulkUpdatesAdvertising.isOverrideExpoList() && session.getSponsorExhibitorJson()!=null){
            sponsorExhibitorDto = exhibitorsServiceImpl.parseJsonToObject(session.getSponsorExhibitorJson());
            if(sponsorExhibitorDto!=null) {
                removeExistingExpoFromTheSession(sponsorExhibitorDto, session);
            }
            log.info("update expo from the session bulk updates overrideExpoList sessionId {}",session.getId());
        }
        if(sessionBulkUpdatesAdvertising.isOverrideSponsorsList() && session.getSponsorExhibitorJson()!=null) {
            sponsorExhibitorDto = exhibitorsServiceImpl.parseJsonToObject(session.getSponsorExhibitorJson());
            if(sponsorExhibitorDto!=null) {
                removeExistingSponsorFromTheSession(sponsorExhibitorDto, session);
            }
            log.info("update sponsor from the session bulk updates overrideSponsorList sessionId {}",session.getId());
        }
    }

    private void removeExistingSponsorFromTheSession(SponsorExhibitorDto sponsorExhibitorDto, Session session) {
        HashMap<String, List<Long>> sponsorsMap = sponsorExhibitorDto.getSponsors();
        List<Long> sponsorIds=sponsorsMap.get(Constants.SPONSORS);
        for(Long sponsorId: sponsorIds) {
            sponsorsService.removeSponsorFromSessionBySessionsBulkUpdates(session, sponsorId);
        }
    }

    private void removeExistingExpoFromTheSession(SponsorExhibitorDto sponsorExhibitorDto, Session session) {
        HashMap<String, List<Long>> exhibitorMap = sponsorExhibitorDto.getExhibitors();
        List<Long> exhibitorIds=exhibitorMap.get(EXHIBITORS);
        for (Long exhibitorId: exhibitorIds) {
            exhibitorService.removeExhibitorFromSessionBySessionsBulkUpdates(session, exhibitorId);
        }
    }

    private void updateManageSessionInteractivityForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList, Map<Long, SessionDetails> sessionDetailsMap) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesPostSessionCallToAction=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.MANAGE_SESSIONS_INTERACTIVITY);
        session.setChatEnabled(sessionBulkUpdatesPostSessionCallToAction.isAttendeeChatEnabled());
        session.setPollEnabled(sessionBulkUpdatesPostSessionCallToAction.isPollsEnabled());
        session.setQuesAndAnsEnabled(sessionBulkUpdatesPostSessionCallToAction.isQAEnabled());
        if (sessionDetailsMap.containsKey(session.getId()) && sessionDetailsMap.get(session.getId()) != null) {
            SessionDetails sessionDetails = sessionDetailsMap.get(session.getId());
            sessionDetails.setEnableAttendeeList(sessionBulkUpdatesPostSessionCallToAction.isAttendeeListEnabled());
            if (session.isQuesAndAnsEnabled()) {
                sessionDetails.setQnAPrivate(sessionBulkUpdatesPostSessionCallToAction.isSendQuestionsInPrivateEnabled());
            }
            sessionDetailsRepoService.save(sessionDetails);
            log.info("update session interactivity from bulk updates sessionDetails {} and sessionId {}",sessionDetails,session.getId());
        }
        updatedSessionList.add(session);
    }

    private void updateSessionVisibilityForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionVisibility=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_VISIBILITY);
        if(sessionBulkUpdatesSessionVisibility.getSessionVisibilityType()!=null) {
            session.setSessionVisibilityType(sessionBulkUpdatesSessionVisibility.getSessionVisibilityType());
            log.info("update session visibility from the bulk updates session visibility {} and sessionId {}",sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_VISIBILITY).getSessionVisibilityType(),session.getId());
            updatedSessionList.add(session);
        }
    }

    private void updateSessionWaitingMediaForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList, Event event, Map<Long, SessionDetails> sessionDetailsMap) throws JSONException {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionWaitingMedia=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_WAITING_MEDIA);
        if (sessionDetailsMap.containsKey(session.getId()) && sessionDetailsMap.get(session.getId()) != null) {
            SessionDetails sessionDetails = sessionDetailsMap.get(session.getId());
            WaitingMediaDto waitingMediaDto = new WaitingMediaDto();
            if (sessionBulkUpdatesSessionWaitingMedia.isVideo()) {
                MuxAssetDTO muxAssetDTO = sessionThirdPartyService.getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(session.getId(), event, sessionBulkUpdatesSessionWaitingMedia.getVideoUploadId(), AssetType.WAITING_MEDIA_ASSET);
                if(null!= muxAssetDTO){
                    log.info("Session Waiting video muxAssetDTO : {}", muxAssetDTO);
                    waitingMediaDto.setVideo(true);
                    waitingMediaDto.setMediaUrl(muxAssetDTO.getPlayBackId());
                    waitingMediaDto.setPlayBackRestrictionToken(muxAssetDTO.getPlayBackRestrictionToken());
                    waitingMediaDto.setThumbnailRestrictionToken(muxAssetDTO.getThumbnailRestrictionToken());
                    waitingMediaDto.setAssetId(muxAssetDTO.getAssetId());
                    waitingMediaDto.setDuration(muxAssetDTO.getDuration());
                    sessionDetails.setEnableSessionWaitingMedia(true);
                    sessionDetails.setWaitingMedia(JsonMapper.convertToString(waitingMediaDto));
                    sessionDetailsRepoService.save(sessionDetails);
                }else{
                    log.info("Session Waiting video asset not found : {}", waitingMediaDto);
                }

            } else {
                WaitingMediaUploadDto waitingMediaUploadDto=getWaitingMediaUploadFromSessionBulkUpdate(sessionBulkUpdatesSessionWaitingMedia);
                log.info("Session Waiting image waitingMediaUploadDto : {}", waitingMediaUploadDto);
                ImageUploadDto imageUploadDto = s3UploadService.imageUploadInS3(new ImageUploadDto(waitingMediaUploadDto));
                waitingMediaDto.setMediaUrl(imageUploadDto.getCroppedImgUrl());
                sessionDetails.setWaitingMedia(JsonMapper.convertToString(waitingMediaDto));
                sessionDetails.setEnableSessionWaitingMedia(true);
                sessionDetailsRepoService.save(sessionDetails);
            }

        }
        updatedSessionList.add(session);
    }

    private void updateSessionCapacityForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesCapacity=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.CAPACITY);
        if(sessionBulkUpdatesCapacity.getCapacity() > 0) {
            session.setCapacity(sessionBulkUpdatesCapacity.getCapacity());
            log.info("update session capacity from the bulk updates session capacity {} and sessionId {}",sessionBulkUpdateMap.get(EnumSessionBulkUpdates.CAPACITY).getCapacity(),session.getId());
            updatedSessionList.add(session);
        }
    }

    private void updateSessionLocationForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList, Long locationId) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionLocation=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_LOCATION);
        if(!SessionTypeFormat.VIRTUAL.equals(session.getSessionTypeFormat()) && sessionBulkUpdatesSessionLocation.getSessionLocation()!=null && NumberUtils.isNumberGreaterThanZero(locationId)) {
            session.setLocationId(locationId);
            log.info("update session location from the bulk updates session location {} and sessionId {}",sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_LOCATION).getSessionLocation(),session.getId());
            updatedSessionList.add(session);
        }
    }

    private void updateSessionFormatTypeForBulkUpdates(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList,EventFormat eventFormat) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionFormatType=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_FORMAT_TYPE);
        if(EventFormat.HYBRID.equals(eventFormat) && sessionBulkUpdatesSessionFormatType.getSessionTypeFormat()!=null) {
            session.setSessionTypeFormat(sessionBulkUpdatesSessionFormatType.getSessionTypeFormat());
            log.info("update session format from the bulk updates session type format {} and sessionId {}",sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_FORMAT_TYPE).getSessionTypeFormat(),session.getId());
            updatedSessionList.add(session);
        }
    }

    private void updateSessionFormatForBulkUpdate(Map<EnumSessionBulkUpdates, SessionBulkUpdatesAttributeValueDto> sessionBulkUpdateMap, Session session, List<Session> updatedSessionList,Event event) {
        SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionFormat=sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_FORMAT);
        if(sessionBulkUpdatesSessionFormat.getSessionFormat()!=null) {
            SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
            Date startDate = null;
            Date endDate = null;
            try {
                if(session.getStartTime() != null && session.getEndTime() != null){
                    startDate = inputDateFormat.parse(session.getStartTime().toString());
                    endDate = inputDateFormat.parse(session.getEndTime().toString());
                }
            } catch (ParseException e) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DATE_FORMAT_NOT_VALID);
            }
            if(startDate != null || endDate != null){
                SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
                String startDateString = outputDateFormat.format(startDate);
                String endDateString = outputDateFormat.format(endDate);
                throwErrorIfTimeSlotConflicts(event,sessionBulkUpdatesSessionFormat.getSessionFormat(), startDateString,endDateString, session.getId(), session.getStatus());
                session.setFormat(sessionBulkUpdatesSessionFormat.getSessionFormat());
                if(MEET_UP.equals(sessionBulkUpdatesSessionFormat.getSessionFormat())){
                    session.setSessionSpeakers(null);
                }
                log.info("update session format from bulk updates session format {} and sessionId {}",sessionBulkUpdateMap.get(EnumSessionBulkUpdates.SESSION_FORMAT).getSessionFormat(),session.getId());
            }
            updatedSessionList.add(session);
        }
    }


    private void handleTagsAndTracksForSessionBulkUpdates(Session session, List<IdNameDto>  idNameDtoList) {
        Set<Long> dbTagOrTrackIds = session.getSessionTagAndTracks().stream().map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toSet());
        Set<Long> inputTagOrTrackIds = getIds(idNameDtoList);

        Set<Long> addNewList = inputTagOrTrackIds.stream().filter(inputTagId -> !dbTagOrTrackIds.contains(inputTagId)).collect(Collectors.toSet());
        sessionTagAndTrackService.createRecords(session.getId(), addNewList);
    }

    public String convertKeyValuesToJson(List<KeyValueDto> keyValueDtos) {
        if(CollectionUtils.isEmpty(keyValueDtos)){
            return null;
        }
        Gson gson = new Gson();
        return gson.toJson(keyValueDtos);
    }

    private List<KeyValueDto> getListOfKeyValueDto(String jsonInString) {
        ObjectMapper mapper = new ObjectMapper();
        List<KeyValueDto> linkKeyValueDtos = new ArrayList<>();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(jsonInString)){
            try {
                linkKeyValueDtos = Arrays.asList(mapper.readValue(jsonInString, KeyValueDto[].class));
            } catch (IOException e) {
                log.error("getting error while trying to get ListOfKeyValueDto {}", e.getMessage());
            }
        }
        return linkKeyValueDtos;
    }

    private WaitingMediaUploadDto getWaitingMediaUploadFromSessionBulkUpdate(SessionBulkUpdatesAttributeValueDto sessionBulkUpdatesSessionWaitingMedia) {
        WaitingMediaUploadDto waitingMediaUploadDto=new WaitingMediaUploadDto();
        waitingMediaUploadDto.setVideoUploadId(sessionBulkUpdatesSessionWaitingMedia.getVideoUploadId());
        waitingMediaUploadDto.setVideo(sessionBulkUpdatesSessionWaitingMedia.isVideo());
        waitingMediaUploadDto.setCroppedImgName(sessionBulkUpdatesSessionWaitingMedia.getCroppedImgName());
        waitingMediaUploadDto.setCroppedImgUrl(sessionBulkUpdatesSessionWaitingMedia.getCroppedImgUrl());
        waitingMediaUploadDto.setFaviconImage(sessionBulkUpdatesSessionWaitingMedia.getFaviconImage());
        waitingMediaUploadDto.setCroppedImgName(sessionBulkUpdatesSessionWaitingMedia.getCroppedImgName());
        waitingMediaUploadDto.setFileName(sessionBulkUpdatesSessionWaitingMedia.getFileName());
        waitingMediaUploadDto.setFromOrganizerPage(sessionBulkUpdatesSessionWaitingMedia.isFromOrganizerPage());
        waitingMediaUploadDto.setSecureUrl(sessionBulkUpdatesSessionWaitingMedia.getSecureUrl());
        waitingMediaUploadDto.setWidth(sessionBulkUpdatesSessionWaitingMedia.getWidth());
        waitingMediaUploadDto.setHeight(sessionBulkUpdatesSessionWaitingMedia.getHeight());
        waitingMediaUploadDto.setX(sessionBulkUpdatesSessionWaitingMedia.getX());
        waitingMediaUploadDto.setY(sessionBulkUpdatesSessionWaitingMedia.getY());
        return waitingMediaUploadDto;
    }

    @Override
    public DataTableResponse getAllSessionsListByEvent(Event event){
        List<SessionBasicDetailsDTO> sessionBasicDetailsDTOList = sessionRepoService.getAllSessionsByEventId(event.getEventId());
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(sessionBasicDetailsDTOList);
        dataTableResponse.setRecordsFiltered(sessionBasicDetailsDTOList.size());
        dataTableResponse.setRecordsTotal(sessionBasicDetailsDTOList.size());
        log.info("getAllSessionsByEventPage | Size of sessionBasicDetailsDTOList : {} " , sessionBasicDetailsDTOList.size());
        return dataTableResponse;
    }

    @Override
    public List<Session> findSessionByEventAndPrefixTitleLike(Event event, String title) {
        return sessionRepo.findSessionByEventAndPrefixTitleLike(event.getEventId(), title);
    }

    @Override
    public List<Long> findSurveyIdBySessionIds(List<Long> sessionIds) {
        return sessionRepoService.findSurveyIdBySessionIds(sessionIds);
    }

    @Override
    public RegisteredBookmarkedSessionDto getUserRegisteredBookmarkedSessions(Event event, User user, boolean isAdminOrStaff) {
        if (user == null) {
            return new RegisteredBookmarkedSessionDto(
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList());
        }

        Long userId = user.getUserId();
        Long eventId = event.getEventId();

        List<Long> bookmarkedSessionIds = userSessionRepoService.getBookmarkedSessionIdsByEventIdAndUserId(eventId, userId);
        List<Long> registeredSessionIds = userSessionRepoService.getRegisteredSessionIdsByEventIdAndUserId(eventId, userId);
        List<Long> calenderAddedSessionIds = userSessionRepoService.getAddToCalendarSessionByUserIdAndSessionId(eventId, userId);
        List<Long> waitlistedSessionIds = userSessionRepoService.getWaitListedSessionIdsByEventIdAndUserId(eventId, userId);

        return new RegisteredBookmarkedSessionDto(
                bookmarkedSessionIds,
                registeredSessionIds,
                calenderAddedSessionIds,
                waitlistedSessionIds
        );
    }

    private List<Long> fetchHiddenSessionsOfUser(Event event, User user, boolean isAdminOrStaff) {
        // Get hidden sessions if the user is not an admin or staff
        List<Long> hiddenSessionIds = isAdminOrStaff
                ? sessionRepo.getAllHiddenSessionOfEventId(event.getEventId()) : new ArrayList<>();
        if (user != null) {
            // Get user ticket types if visibility restrictions apply
            List<Long> userTicketTypes = (sessionRepoService.allSessionVisibleForUser(user, event) && isAdminOrStaff)
                    ? new ArrayList<>()
                    : eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(event, user);

            // Fetch sessions hidden from attendees
            List<Session> hiddenFromAttendeesSessions = sessionRepo.getAllSessionsByEventIdAndHideFromAttendeesAndStatusNotHidden(event.getEventId());

            for (Session session : hiddenFromAttendeesSessions) {
                // Skip processing if userTicketTypes is empty and session has no ticket type restrictions
                if (userTicketTypes.isEmpty() || isBlank(session.getTicketTypesThatCanBeRegistered())) {
                    hiddenSessionIds.add(session.getId());
                    continue;
                }

                // Convert ticket type string to list of IDs
                List<Long> sessionTicketTypeIds = Arrays.stream(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                // Add session to hidden list only if there's an intersection with userTicketTypes
                if (!Collections.disjoint(sessionTicketTypeIds, userTicketTypes)) {
                    hiddenSessionIds.add(session.getId());
                }
            }
        }


        //filterPrivateSessions
        List<Long> privateSessionIds = sessionRepo.getAllPrivateSessionByEventIdAndStatusNotHidden(event.getEventId());
        if (!isAdminOrStaff && !CollectionUtils.isEmpty(privateSessionIds) && user != null) {
            Set<Long> userAccessibleSessionIds = new HashSet<>(userSessionRepo.findSessionIdByUserIdAndEventIdAndSessionIdIn(
                    user.getUserId(), event.getEventId(), privateSessionIds));

            userAccessibleSessionIds.addAll(sessionSpeakerRepo.findAllSessionIdBySpeakerUserId(user.getUserId(), event.getEventId()));


            privateSessionIds.stream()
                    .filter(userAccessibleSessionIds::contains)
                    .forEach(hiddenSessionIds::add);
        } else {
            hiddenSessionIds.addAll(privateSessionIds);
        }

        return hiddenSessionIds;
    }


    @Override
    public HttpEntity<byte[]> calendarIcalForSession(String sessionName, String startDate, String endDate, Long customEmailId, String sessionAddress, Event event){
        String buffer;
        // add this condition to save a seat confirmation email which have no address in session and need to download ical calender
        sessionAddress = ("location".equals(sessionAddress)) ? STRING_EMPTY : sessionAddress;

        Optional<CustomTemplates> customTemplatesOptional = confirmationEmailRepository.findById(customEmailId);
        String description = (customTemplatesOptional.isPresent() && StringUtils.isNotBlank(customTemplatesOptional.get().getCalendarInvitation())) ? customTemplatesOptional.get().getCalendarInvitation() : String.format(DEFAULT_CALENDER_INVITATION,apiBaseUrl,event.getEventURL());

        if (null != event.getOrganizer()) {
            buffer = EmailUtils.getCalendarText(sessionName, startDate, endDate, description, sessionAddress, event.getOrganizer().getName() != null ? event.getOrganizer().getName() : STRING_EMPTY, event.getOrganizer().getContactEmailAddress() != null ? event.getOrganizer().getContactEmailAddress() : STRING_EMPTY,null);
        } else {
            buffer = EmailUtils.getCalendarText(sessionName, startDate, endDate, description, sessionAddress, STRING_EMPTY, STRING_EMPTY,null);
        }
        HttpHeaders header = new HttpHeaders();
        header.setContentType(new MediaType("text", "calendar"));
        header.set(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=calendar.ics");
        header.setContentLength(buffer.getBytes().length);

        log.info("Successfully downloaded iCal calendar for session {}, customEmailId {}",sessionName,customEmailId);
        return new HttpEntity<>(buffer.getBytes(), header);
    }

    @Override
    @Deprecated
    public DataTableResponseForSession getRedisDisplayPortalSessionList(Event event, User user, Pageable pageable, SessionFilter sessionFilter, boolean isAdminOrStaff, Boolean showPastAndUpcoming) {
        Long userId = user != null ? user.getUserId(): 0L;
        Long eventId = event.getEventId();
        log.info("getRedisDisplayPortalSessionList | event {} | user {}", eventId, userId);
        int sessionFilterHashCode = sessionFilter.hashCode();
        String basePrefix = SESSIONS_UNDERSCORE + eventId + STRING_UNDERSCORE;

        String redisAggregatedKey = basePrefix + USER_UNDERSCORE + userId + STRING_UNDERSCORE + sessionFilterHashCode +STRING_UNDERSCORE+ pageable.getPageNumber() +STRING_UNDERSCORE+ pageable.getPageSize();

        // Try fetching from Redis first
        DataTableResponseForSession cachedResponse = getAggregateDataFromRedis(redisAggregatedKey);
        if (Objects.nonNull(cachedResponse)) {
            log.info("return from the redis cached data");
            return cachedResponse;
        }

        String redisPublicKey = basePrefix + PUBLIC_UNDERSCORE + sessionFilterHashCode +STRING_UNDERSCORE+ pageable.getPageNumber() +STRING_UNDERSCORE+ pageable.getPageSize();
        String redisPrivateKey = basePrefix + PRIVATE_UNDERSCORE + userId + STRING_UNDERSCORE + sessionFilterHashCode +STRING_UNDERSCORE+ pageable.getPageNumber() +STRING_UNDERSCORE+ pageable.getPageSize();
        String userPrivateSessionIdsKey = basePrefix + PRIVATE_IDS_UNDERSCORE + userId;
        log.info("all the redis keys aggregate key {}, public key {}, private ids key {}, private session key {}", redisAggregatedKey, redisPublicKey, userPrivateSessionIdsKey, redisPrivateKey);

        // Fetch past/upcoming session info
        PastUpComingDto pastUpComingDto = getPastUpComingDtoDetail(event, sessionFilter, showPastAndUpcoming);

        // Fetch public sessions from Redis, or DB if not cached
        DataTableResponseForSession dataTableResponseForPublicSession = getAggregateDataFromRedis(redisPublicKey);
        if (Objects.isNull(dataTableResponseForPublicSession)) {
            log.info("Not found in redis public sessions of the user {} and event id {}", userId, eventId );
            Page<Session> sessionsPage = sessionRepoService.getAllPublicSessionByEventId(event, pageable, sessionFilter);
            dataTableResponseForPublicSession = getDisplayPortalSessionDataTable(event, sessionsPage, pastUpComingDto);
            redisCacheService.set(redisPublicKey, JsonMapper.convertToString(dataTableResponseForPublicSession), 5, TimeUnit.MINUTES);
        }

        //If logged in user not present then return the public sessions only
        if(userId == 0L){
            return dataTableResponseForPublicSession;
        }

        // Fetch private session IDs from Redis or compute if missing
        List<Long> privateSessionIdsOfUser = getPrivateSessionIdsFromRedis(userPrivateSessionIdsKey);
        if (Objects.isNull(privateSessionIdsOfUser)) {
            log.info("Not found in redis private sessions id of the user {} and event id {}", userId, eventId );
            privateSessionIdsOfUser = fetchHiddenSessionsOfUser(event, user, isAdminOrStaff);
            redisCacheService.set(userPrivateSessionIdsKey, JsonMapper.convertToString(privateSessionIdsOfUser), 5, TimeUnit.MINUTES);
        }

        log.info("fetch private session Ids {}",  privateSessionIdsOfUser);

        // Fetch private sessions from Redis or DB
        DataTableResponseForSession dataTableResponseForPrivateSession = getAggregateDataFromRedis(redisPrivateKey);
        if (Objects.isNull(dataTableResponseForPrivateSession) && !CollectionUtils.isEmpty(privateSessionIdsOfUser)) {
            log.info("Not found in redis private sessions of the user {} and event id {}", userId, eventId);
            List<Session> userSpecificSessions = sessionRepo.getAllSessionByIdsWithOrderByStartTimeEndTimeAndPosition(privateSessionIdsOfUser, eventId);
            Page<Session>  privateSessionPage = sessionRepoService.getAllCachePrivateSessionByEventId(event, pageable, sessionFilter, userSpecificSessions);
            dataTableResponseForPrivateSession = getDisplayPortalSessionDataTable(event, privateSessionPage, pastUpComingDto);
            redisCacheService.set(redisPrivateKey, JsonMapper.convertToString(dataTableResponseForPrivateSession), 5, TimeUnit.MINUTES);
        }


        // Merge & Sort sessions
        if(null != dataTableResponseForPrivateSession) {
            DataTableResponseForSession dataTableResponseForPublicAndPrivateSession = mergeAndSortSessions(dataTableResponseForPublicSession, dataTableResponseForPrivateSession, pastUpComingDto);
            // Store aggregated result in Redis
            redisCacheService.set(redisAggregatedKey, JsonMapper.convertToString(dataTableResponseForPublicAndPrivateSession), 5, TimeUnit.MINUTES);
            return dataTableResponseForPublicAndPrivateSession;
        }
        return dataTableResponseForPublicSession;
    }

    // Optimized getPrivateSessionIdsFromRedis
    private List<Long> getPrivateSessionIdsFromRedis(String key) {
        String json = (String) redisCacheService.get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return new ObjectMapper().readValue(json, new TypeReference<List<Long>>() {});
        } catch (JsonProcessingException e) {
            log.error("Error deserializing private session IDs from Redis for key: {}", key, e);
            return Collections.emptyList();
        }
    }

    // Optimized getAggregateDataFromRedis
    private DataTableResponseForSession getAggregateDataFromRedis(String key) {
        String json = (String) redisCacheService.get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JsonMapper.stringtoObject(json, DataTableResponseForSession.class);
        } catch (Exception e) {
            log.error("Error deserializing aggregate data from Redis for key: {}", key, e);
            return null;
        }
    }

    // Optimized mergeAndSortSessions
    public DataTableResponseForSession mergeAndSortSessions(DataTableResponseForSession publicSessions, DataTableResponseForSession privateSessions,PastUpComingDto pastUpComingDto) {
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<DisplayPortalSessionDTO> allSessions = new ArrayList<>();
        allSessions.addAll(convertToDTOList(publicSessions.getData()));
        allSessions.addAll(convertToDTOList(privateSessions.getData()));

        allSessions.sort(Comparator.comparing(DisplayPortalSessionDTO::getStartTime)
                .thenComparing(DisplayPortalSessionDTO::getEndTime)
                .thenComparing(DisplayPortalSessionDTO::getPosition, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DisplayPortalSessionDTO::getTitle));

        DataTableResponseForSession dataTableResponseForSession = new DataTableResponseForSession();
        dataTableResponseForSession.setData(allSessions);
        dataTableResponseForSession.setRecordsFiltered(allSessions.size());
        dataTableResponseForSession.setRecordsTotal(allSessions.size());
        dataTableResponseForSession.setPastSession(pastUpComingDto.isPastSession());
        dataTableResponseForSession.setUpComingSession(pastUpComingDto.isUpComingSession());
        stopWatch.stop();
        log.info("Execution time Merge and sort public and private session {}", stopWatch.getTotalTimeMillis());
        return dataTableResponseForSession;
    }

    private List<DisplayPortalSessionDTO> convertToDTOList(List<?> data) {
            if (data == null || data.isEmpty()) {
                return Collections.emptyList();
            }
            return data.stream()
                    .map(item -> {
                        if (item instanceof LinkedHashMap) {
                            return MAPPER.convertValue(item, DisplayPortalSessionDTO.class);
                        }
                        return (DisplayPortalSessionDTO) item;
                    })
                    .collect(Collectors.toList());
    }


    public DataTableResponseForSession getDisplayPortalSessionDataTable(Event event, Page<Session> sessionsPage, PastUpComingDto pastUpComingDto) {
        List<SessionDetails> sessionDetails = sessionDetailsRepoService.getAllSessionDetailsByEventId(event.getEventId());
        Map<Long, List<SessionDetails>> sessionDetailsMap = sessionDetails.stream().collect(Collectors.groupingBy(e-> e.getSession().getId()));
        Map<Long,Boolean> sessionBookmarkCapacity = countCapacityReachedForSessions(event, sessionsPage);
        Map<Long, Boolean> sessionViewRecording = sessionViewRecordingsButton(event, sessionsPage);
        List<DisplayPortalSessionDTO> displayPortalSessionDTOS = sessionsPage.getContent().stream()
                .map(session -> createDisplayPortalSessionDTO(session, event, sessionDetailsMap, sessionBookmarkCapacity.get(session.getId()),sessionViewRecording.getOrDefault(session.getId(),false)))
                .collect(toList());

        DataTableResponseForSession dataTableResponseForSession = new DataTableResponseForSession();
        dataTableResponseForSession.setRecordsTotal(sessionsPage.getTotalElements());
        dataTableResponseForSession.setRecordsFiltered(sessionsPage.getContent().size());
        dataTableResponseForSession.setData(displayPortalSessionDTOS);
        dataTableResponseForSession.setPastSession(pastUpComingDto.isPastSession());
        dataTableResponseForSession.setUpComingSession(pastUpComingDto.isUpComingSession());

        return dataTableResponseForSession;
    }

    private Map<Long, Boolean> sessionViewRecordingsButton(Event event, Page<Session> sessionsPage) {
        List<Long> sessionIds = sessionsPage.getContent().stream()
                .map(Session::getId)
                .collect(Collectors.toList());

        List<Object[]> recordingsWithSessionIds = muxLivestreamAssetRepo.findSessionRecordingsBySessionIdsAndAssetTypeAndVisible(
                sessionIds, AssetType.SESSION_ASSET, true);

        List<Object[]> workshopRecordingsSessionIds = workshopRecordingAssetsRepo.findWorkshopRecordingsByVisibleStatusAndSessionIdsIn(
                sessionIds, true);

        Stream<Object[]> combinedResults = Stream.concat(recordingsWithSessionIds.stream(), workshopRecordingsSessionIds.stream());

        return combinedResults.collect(Collectors.toMap(
                row -> (Long) row[0],          // sessionId
                row -> (Boolean) row[1],       // isPresent
                (existing, replacement) -> existing
        ));
    }


    private Map<Long,Boolean> countCapacityReachedForSessions(Event event, Page<Session> sessionsPage) {
        List<Session> sessions = sessionsPage.getContent();
        List<Long> sessionIds = sessions.stream().map(Session::getId).collect(toList());

        boolean isAllowSessionBookmarkCapacity = virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId());
        Map<Long,Boolean> sessionBookmarkCapacity;
        log.info("countCapacityReachedForSessions getSessionInfoById isAllowSessionBookmarkCapacity {} ",isAllowSessionBookmarkCapacity);
        if (isAllowSessionBookmarkCapacity) {
            List<Long> workshopSessionIds = sessions.stream().filter(e->e.getFormat().equals(EnumSessionFormat.WORKSHOP)).map(Session::getId).collect(toList());
            List<IdCountDto> registerCount;
            if(!CollectionUtils.isEmpty(workshopSessionIds)) {
                registerCount = userSessionService.getSessionStatsForIdsIn(workshopSessionIds);
                sessionIds.removeAll(workshopSessionIds);
                registerCount.addAll(userSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds, event.getEventId()));
                sessionIds.addAll(workshopSessionIds);
            }else{
                registerCount = userSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds, event.getEventId());
            }
            Map<Long, Long> registerSessionStats = getMap(registerCount);
            log.info("countCapacityReachedForSessions getSessionInfoById registerSessionStats {} ",registerSessionStats);
            sessionBookmarkCapacity = sessions.stream()
                    .collect(Collectors.toMap(
                            Session::getId,
                            session -> {
                                Long registeredCount = registerSessionStats.getOrDefault(session.getId(), 0L);
                                return registeredCount != 0 && session.getCapacity() != 0 && registeredCount >= session.getCapacity();
                            }
                    ));
        } else {
            log.info("countCapacityReachedForSessions getSessionInfoById session bookmark capacity reached is false for all the session of the event {}",event.getEventId());
            sessionBookmarkCapacity = sessions.stream()
                    .collect(Collectors.toMap(Session::getId, session -> false));
        }
        log.info("countCapacityReachedForSessions getSessionInfoById sessionBookmarkCapacity {} ",sessionBookmarkCapacity);
        return sessionBookmarkCapacity;
    }

    private Map<Long, Long> getMap(List<IdCountDto> idCountDtos1) {
        return idCountDtos1.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount));
    }

    private DisplayPortalSessionDTO createDisplayPortalSessionDTO(Session session, Event event, Map<Long, List<SessionDetails>> sessionDetailsMap, Boolean sessionBookmarkCapacityReached, Boolean isViewRecording) {
        List<SessionDetails> sessionDetails = sessionDetailsMap.getOrDefault(session.getId(), List.of());
        SessionDetails details = sessionDetails.isEmpty() ? null : sessionDetails.get(0);

        return new DisplayPortalSessionDTO(
                session,
                event,
                details != null && details.isRecordSession(),
                convertWaitingMedia(details),
                details != null && details.isEnableSessionWaitingMedia(),
                details != null && details.getHideVideoControls(),
                sessionBookmarkCapacityReached,
                isViewRecording
        );
    }

    private WaitingMediaDto convertWaitingMedia(SessionDetails details) {
        if (details == null || details.getWaitingMedia() == null) {
            return new WaitingMediaDto();
        }
        return WaitingMediaDto.convertJSONToObject(details.getWaitingMedia());
    }

    @Override
    public List<PostSessionSurveyDto> getPostSessionsSurveysDetails(Event event, User user, boolean isAdminOrStaff) {

        List<PostSessionSurveyDto> postSessionSurveyDtos = new ArrayList<>();
        List<SessionDetailsSurveyDto> pastSessions;
        boolean isSurveyRequiresCheckIn = roEventLevelSettingService.isSurveyRequiresCheckin(event.getEventId());
        if(Boolean.TRUE.equals(isSurveyRequiresCheckIn)){
            pastSessions = sessionRepo.getAllPastSessionsByEventIdAndSurveyEnabledAndRequiresSurveyCheckin(event, new Date(), user.getUserId());
        } else {
            pastSessions = sessionRepo.getAllPastSessionsByEventIdAndSurveyEnabled(event, new Date());
        }

        List<SessionDetailsSurveyDto> finalSessions = new ArrayList<>();

        List<Long> surveyIds = new ArrayList<>();

        List<Long> userTicketTypes = isAdminOrStaff ? null : eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(event, user);

        for (SessionDetailsSurveyDto session : pastSessions) {
            if (isAdminOrStaff) {
                    finalSessions.add(session);
                    surveyIds.add(session.getSurveyId());
            } else {
                if (!CollectionUtils.isEmpty(userTicketTypes)
                        && isNotBlank(session.getTicketTypesThatCanBeRegistered())) {
                    List<Long> eventTicketTypeIds = Stream.of(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    if (!Collections.disjoint(eventTicketTypeIds, userTicketTypes)) {
                        finalSessions.add(session);
                        surveyIds.add(session.getSurveyId());
                    }
                }
            }
        }

        List<SurveySessionsBasicDto> surveySubmittedByUser = surveyResponseRepository.findSubmittedSurveyByEventIdAndUserIdAndSurveyIds(event.getEventId(),user.getUserId(),surveyIds);

        for(SessionDetailsSurveyDto session : finalSessions){
            boolean isSurveyCompleted =  surveySubmittedByUser.stream()
                    .anyMatch(survey -> survey.getSurveyId() == session.getSurveyId() && (survey.getSessionId() != null && survey.getSessionId().equals(session.getId())));

            postSessionSurveyDtos.add(new PostSessionSurveyDto(session.getId(), session.getSurveyId(), isSurveyCompleted));
        }

        return postSessionSurveyDtos;
    }

    @Override
    public List<SessionCalendarViewDTO> getSessionCalendarViewList(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, String speakerIds, String locationIds, String date, String filter) {
        log.info("getSessionCalendarViewList | event {} | filter {}", event.getEventId(), filter);

        if (CollectionUtils.isEmpty(sessionTypes)){
            sessionTypes=new ArrayList<>(Arrays.asList(MAIN_STAGE,
                    BREAKOUT_SESSION,
                    MEET_UP,
                    EnumSessionFormat.WORKSHOP,
                    EXPO,
                    BREAK,
                    OTHER));
        }

        if (isEmpty(listOfTagAndTrackIds)) {
            listOfTagAndTrackIds.add(0L);
        }
        List<Long> speakerIdList = new ArrayList<>();

        if(StringUtils.isEmpty(speakerIds)){
            speakerIdList.add(0L);
        }else{
           speakerIdList =  GeneralUtils.convertCommaSeparatedToListLong(speakerIds);
        }

        List<Long> locationIdList = new ArrayList<>();

        if(StringUtils.isEmpty(locationIds)){
            locationIdList.add(0L);
        }else{
            locationIdList =  GeneralUtils.convertCommaSeparatedToListLong(locationIds);
        }

        switch(filter){
            case EVENT:
                return getSessionsGroupedByEventDates(event,null ,null,searchString, sessionTypes, listOfTagAndTrackIds,speakerIdList, locationIdList);

            case DAY:
                OffsetDateTime startDate = isNotBlank(date) ? DateUtils.getDateInUTCFromTimeZoneStr(date) : null;
                Date filterStartDate =  Date.from(startDate != null ? startDate.toInstant() : null);
                OffsetDateTime endDate = (startDate != null) ? DateUtils.add24Hours1SecLess(startDate) : null;
                Date filterEndDate = Date.from(endDate != null ? endDate.toInstant() : null);
                return getSessionsGroupedByEventDates(event, filterStartDate, filterEndDate, searchString, sessionTypes, listOfTagAndTrackIds,speakerIdList, locationIdList);

            case ROOM:
                return getSessionsGroupedByRoom(event, searchString, sessionTypes, listOfTagAndTrackIds,speakerIdList, locationIdList);

            default:
                return new ArrayList<>();
        }
    }

    @Override
    public DataTableResponse getUnscheduledSessionsOfEvent(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, String speakerIds, String locationIds, int page, int size) {
        log.info("getUnscheduledSessionsOfEvent | event {} | ", event.getEventId());

        if (CollectionUtils.isEmpty(sessionTypes)){
            sessionTypes=new ArrayList<>(Arrays.asList(MAIN_STAGE,
                    BREAKOUT_SESSION,
                    MEET_UP,
                    EnumSessionFormat.WORKSHOP,
                    EXPO,
                    BREAK,
                    OTHER));
        }

        if (isEmpty(listOfTagAndTrackIds)) {
            listOfTagAndTrackIds.add(0L);
        }
        List<Long> speakerIdList = new ArrayList<>();

        if(StringUtils.isEmpty(speakerIds)){
            speakerIdList.add(0L);
        }else{
            speakerIdList =  GeneralUtils.convertCommaSeparatedToListLong(speakerIds);
        }

        List<Long> locationIdList = new ArrayList<>();

        if(StringUtils.isEmpty(locationIds)){
            locationIdList.add(0L);
        }else{
            locationIdList =  GeneralUtils.convertCommaSeparatedToListLong(locationIds);
        }

        Page<SessionDetailsCalendarViewDto> sessions = sessionRepoService.getAllHostUnscheduledSessionByEventId(event,searchString, sessionTypes, listOfTagAndTrackIds, speakerIdList,locationIdList, PageRequest.of(page, size));
        return getUnscheduledSessionDataTable(sessions, event);
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public void updateSessionWithDragAndDrop(Long id, SessionDragAndDropDto dto, Event event, User user) {
        log.info("updateSessionWithDragAndDrop session id {} and event {} and user {} and dto {}", id,event.getEventId(), user.getUserId(),dto);
        Session session = sessionRepoService.getSessionByIdWithoutCache(id, event);

        if (dto.getStartTime() != null && dto.getEndTime() != null) {
            log.info("updateSessionWithDragAndDrop | start time and end time not null session Id {} and event {}", id,event.getEventId());
            validateDateAndTimeForSession(event, dto.getStartTime(), dto.getEndTime(), EnumSessionStatus.DRAFT.equals(session.getStatus()));
            throwErrorIfTimeSlotConflicts(event, session.getFormat(), dto.getStartTime(), dto.getEndTime(), id, session.getStatus());

            session.setStartTime(getDateInUTC(dto.getStartTime(), event.getEquivalentTimeZone()));
            session.setEndTime(getDateInUTC(dto.getEndTime(), event.getEquivalentTimeZone()));
        }

        if (dto.getLocationId() != null) {
            log.info("updateSessionWithDragAndDrop | location not null session Id {} and event {}", id,event.getEventId());
            session.setLocationId(dto.getLocationId());
        }
        sessionRepoService.save(session);
        log.info("updateSessionWithDragAndDrop | session update successfully session id{}", id);
    }

    @Override
    public SessionRegistrationLimitDTO getSessionRegistrationLimits(User user, Event event, boolean isAdminOrStaff) {

        SessionRegistrationLimitDTO sessionRegistrationLimitDTO = new SessionRegistrationLimitDTO();
        if(isAdminOrStaff){
            return sessionRegistrationLimitDTO;
        }

        List<Long> eventTicketIds = eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(user.getUserId(), event.getEventId());

        if (eventTicketIds.isEmpty()) {
            return sessionRegistrationLimitDTO;
        }

        EventTickets eventTickets = eventTicketsService
                .findEventTicketById(eventTicketIds.get(0))
                .orElseThrow(() -> new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.TICKET_NOT_FOUND));
        TicketingType ticketingType = eventTickets.getTicketingTypeId();

        Integer maxSession = ticketingType.getMaxSessionRegisterUser();
        sessionRegistrationLimitDTO.setTotalEventLimits(maxSession != null ? Long.valueOf(maxSession) : null);

        long totalEventLimit = ticketingType.getMaxSessionRegisterUser() != null
                ? Long.valueOf(ticketingType.getMaxSessionRegisterUser())
                : 0L;

        // Get how many sessions the user has already registered
        BigInteger registeredCount = userSessionRepoService.countByEventTicketId(eventTickets.getId());
        long usedCount = registeredCount != null ? registeredCount.longValue() : 0L;
        sessionRegistrationLimitDTO.setUsedEventLimits(usedCount);
        sessionRegistrationLimitDTO.setLimitReached(totalEventLimit > 0 && usedCount >= totalEventLimit);



        if (!ticketingType.isTracksSessionsLimitAllowed()) {
            log.info("getSessionRegistrationLimits | Session registration limits not enabled on the tracks for the ticket type {} and event {} and user {}", ticketingType.getId(), event.getEventId(), user.getUserId());
            return sessionRegistrationLimitDTO;
        }


        List<TicketTypeTrackSessionLimits> ticketTypeTrackSessionLimits = ticketTypeTrackSessionLimitsRepository.findByTicketTypeIdAndEventId(ticketingType.getId(), event.getEventId());
        List<Long> limitedTrackIds =  ticketTypeTrackSessionLimits.stream()
                .map(TicketTypeTrackSessionLimits::getTagOrTrackId)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(limitedTrackIds)) {
            log.info("getSessionRegistrationLimits | No limit in any track for the user {} and event id {} and ticket type {}", user.getUserId(), event.getEventId(), ticketingType.getId());
            return sessionRegistrationLimitDTO;
        }

        log.info("getSessionRegistrationLimits | limited track for the user {} and event id {} and ticket type {} and tracks size {}", user.getUserId(), event.getEventId(), ticketingType.getId(), limitedTrackIds.size());
        List<Object[]> registeredSessionOfTrack = userSessionRepoService.getRegisteredSessionCountForUserByTracks(user, event, limitedTrackIds);
        Map<Long, Long> usedLimitMap = registeredSessionOfTrack.stream()
                .collect(Collectors.toMap(
                        row -> (Long) row[0],
                        row -> (Long) row[1]
                ));

        List<TrackLimitWithAvailability> trackLimitWithAvailabilities = ticketTypeTrackSessionLimits.stream()
                .map(limit -> {
                    long trackId = limit.getTagOrTrackId();
                    long totalLimit = limit.getSessionLimit();
                    long used = usedLimitMap.getOrDefault(trackId, 0L);
                    return new TrackLimitWithAvailability(trackId, totalLimit, used);
                })
                .collect(Collectors.toList());

        sessionRegistrationLimitDTO.setTrackLimits(trackLimitWithAvailabilities);
        return sessionRegistrationLimitDTO;
    }


    private DataTableResponse getUnscheduledSessionDataTable(Page<SessionDetailsCalendarViewDto> sessionsPage, Event event) {
        log.info("getUnscheduledSessionDataTable | event {} | size {}", event.getEventId(), sessionsPage.getTotalElements());
        List<SessionDetailsCalendarViewDto> sessionList = sessionsPage.getContent();

        List<Long> sessionIds = sessionList.stream().map(SessionDetailsCalendarViewDto::getId).collect(toList());
        Map<Long, List<Long>> sessionSpeakerMap = sessionSpeakerService.findBySessionIdsIn(sessionIds)
                .stream()
                .collect(Collectors.groupingBy(
                        SessionSpeaker::getSessionId,
                        Collectors.mapping(SessionSpeaker::getSpeakerId, Collectors.toList())
                ));

        Map<Long, List<Long>> sessionTagAndTracksMap = sessionTagAndTrackService.findBySessionIds(sessionIds)
                .stream()
                .collect(Collectors.groupingBy(
                        SessionTagAndTrack::getSessionId,
                        Collectors.mapping(SessionTagAndTrack::getTagOrTrackId, Collectors.toList())
                ));


        sessionList.forEach(session -> {
            List<Long> speakers = sessionSpeakerMap.getOrDefault(session.getId(), Collections.emptyList());
            List<Long> tagAndTracks = sessionTagAndTracksMap.getOrDefault(session.getId(), Collections.emptyList());
            session.setSpeakers(speakers);
            session.setTagsAndTracks(tagAndTracks);
        });

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(sessionsPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(sessionList.size());
        dataTableResponse.setData(sessionList);
        return dataTableResponse;
    }

    private List<SessionCalendarViewDTO> getSessionsGroupedByRoom(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, List<Long> speakerIds, List<Long> locationIds) {
        log.info("getSessionsGroupedByRoom | event {}", event.getEventId());
        List<SessionDetailsCalendarViewDto> sessions = sessionRepoService.findAllHostSessions(event, searchString, sessionTypes, listOfTagAndTrackIds, speakerIds, locationIds, null, null);
        if (CollectionUtils.isEmpty(sessions)) {
            return Collections.emptyList();
        }

        attachSpeakersAndTagsTracksToSessionsDetails(sessions, event);

        Map<Long, List<SessionDetailsCalendarViewDto>> groupedByLocation = sessions.stream()
                .filter(session -> session.getLocation() != null)
                .collect(Collectors.groupingBy(
                        SessionDetailsCalendarViewDto::getLocation,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        return groupedByLocation.entrySet()
                .stream()
                .map(entry -> new SessionCalendarViewDTO(
                        String.valueOf(entry.getKey()),
                        entry.getValue()
                ))
                .collect(Collectors.toList());
    }

    private List<SessionCalendarViewDTO> getSessionsGroupedByEventDates(Event event, Date startDate, Date endDate, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, List<Long> speakerIds, List<Long> locationIds) {
        log.info("getSessionsGroupedByEventDates | event {} ", event.getEventId());

        List<SessionDetailsCalendarViewDto> sessions = sessionRepoService.findAllHostSessions(event, searchString, sessionTypes, listOfTagAndTrackIds, speakerIds, locationIds, startDate, endDate);
        if (CollectionUtils.isEmpty(sessions)) {
            return Collections.emptyList();
        }

        attachSpeakersAndTagsTracksToSessionsDetails(sessions, event);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        ZoneId eventZoneId = ZoneId.of(event.getEquivalentTimeZone());

        Map<String, List<SessionDetailsCalendarViewDto>> groupedByDate = sessions.stream().filter(session -> session.getStartTime() != null && session.getEndTime() != null)
                .collect(Collectors.groupingBy(
                        session -> session.getStartTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .withZoneSameInstant(eventZoneId)
                                .toLocalDate()
                                .format(formatter),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        return groupedByDate.entrySet()
                .stream()
                .map(entry -> new SessionCalendarViewDTO(
                        String.valueOf(entry.getKey()),
                        entry.getValue()
                ))
                .collect(Collectors.toList());

    }

    private void attachSpeakersAndTagsTracksToSessionsDetails(List<SessionDetailsCalendarViewDto> sessions, Event event) {
        log.info("attachSpeakersAndTagsTracksToSessionsDetails event {} and session size {}", event, sessions.size());
        List<Long> sessionIds = sessions.stream().map(SessionBasicDetailsDTO::getId).collect(toList());
        Map<Long, List<Long>> sessionSpeakerMap = sessionSpeakerService.findBySessionIdsIn(sessionIds)
                .stream()
                .collect(Collectors.groupingBy(
                        SessionSpeaker::getSessionId,
                        Collectors.mapping(SessionSpeaker::getSpeakerId, Collectors.toList())
                ));

        Map<Long, List<Long>> sessionTagAndTracksMap = sessionTagAndTrackService.findBySessionIds(sessionIds)
                .stream()
                .collect(Collectors.groupingBy(
                        SessionTagAndTrack::getSessionId,
                        Collectors.mapping(SessionTagAndTrack::getTagOrTrackId, Collectors.toList())
                ));


        sessions.forEach(session -> {
            List<Long> speakers = sessionSpeakerMap.getOrDefault(session.getId(), Collections.emptyList());
            List<Long> sessiontagAndTrack = sessionTagAndTracksMap.getOrDefault(session.getId(), Collections.emptyList());
            session.setSpeakers(speakers);
            session.setTagsAndTracks(sessiontagAndTrack);
        });
    }

    @Override
    public DataTableResponse getWaitlistedUserDetailsBySessionId(Long sessionId, Event event, User user,PageSizeSearchObj pageSizeSearchObj, String search) {
        try{
            Session session = sessionRepoService.getSessionById(sessionId, event);
            if (session == null) {
                throw new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND);
            }
            Pageable pageable = PageRequest.of(pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize());
            Page<WaitListedHolderUsers> waitlistedUsersPage = userSessionRepo.findWaitlistedUsersBySessionId(session.getId(), pageable, search);

            DataTableResponse response = new DataTableResponse();
            response.setData(waitlistedUsersPage.getContent());
            response.setRecordsTotal(waitlistedUsersPage.getTotalElements());
            response.setRecordsFiltered(waitlistedUsersPage.getTotalElements());
            return response;
        }catch (Exception exception){
            log.info("Exception occurred while getWaitlistedUserDetailsBySessionId eventId {} userId {} exception {}", event.getEventId(), user.getUserId(), exception.getMessage());
            return null;
        }
    }

    @Override
    public void registerWaitlistedUserByUserSessionId(Event event, User user, Long userSessionId) {
        log.info("registerWaitlistedUserByUserSessionId eventId {} userId {} userSessionId {}", event.getEventId(), user.getUserId(), userSessionId);
        try {
            Optional<UserSession> userSessionOptional = userSessionRepo.findById(userSessionId);
            if(userSessionOptional.isPresent()){
                UserSession userSession = userSessionOptional.get();
                userSession.setSessionStatus(REGISTERED);
                userSession.setRegistrationDate(new Date());
                userSessionRepoService.save(userSession);

                // Send email confirmation after successful registration
                try {
                    if (userSession.getUser() != null && userSession.getSession() != null) {
                        log.info("Sending save a seat email for registered waitlisted user. UserSessionId: {}, UserId: {}, SessionId: {}", userSessionId, userSession.getUser().getUserId(), userSession.getSession().getId());
                        sendGridMailPrepareService.sendSaveASeatEmail(userSession.getUser(), userSession.getSession(), event, true, false);
                    } else {
                        log.warn("Cannot send email - User or Session is null. UserSessionId: {}, User: {}, Session: {}", userSessionId, userSession.getUser(), userSession.getSession());
                    }
                } catch (Exception emailException) {
                    log.error("Failed to send email for registered waitlisted user. UserSessionId: {}, Error: {}", userSessionId, emailException.getMessage(), emailException);
                }
            }
        }catch (Exception exception){
            log.info("Exception occurred while registerWaitlistedUserByUserSessionId eventId {} userId {} exception {}", event.getEventId(), user.getUserId(), exception.getMessage());
        }
    }

    @Override
    public void removeWaitlistedUserByUserSessionId(Event event, User user, Long userSessionId) {
        log.info("removeWaitlistedUserByUserSessionId eventId {} userId {} userSessionId {}", event.getEventId(), user.getUserId(), userSessionId);
        try {
            Optional<UserSession> userSessionOptional = userSessionRepo.findById(userSessionId);
            userSessionOptional.ifPresent(userSession -> {
                if (EnumUserSessionStatus.WAITLISTED.equals(userSession.getSessionStatus()) && userSession.isBookmarked()) {
                    // When user is bookmarked and waitlisted, remove waitlist status but keep bookmark
                    userSession.setSessionStatus(EnumUserSessionStatus.BOOKMARKED);
                    userSessionRepo.save(userSession);
                    log.info("Updated session status for bookmarked waitlisted user userSessionId {}", userSessionId);
                } else {
                    // If user is not bookmarked, delete the user session
                    userSessionRepoService.delete(userSession);
                    log.info("Deleted userSession userSessionId {}", userSessionId);
                }
            });
        } catch (Exception exception) {
            log.info("Exception occurred while removeWaitlistedUserByUserSessionId eventId {} userId {} exception {}", event.getEventId(), user.getUserId(), exception.getMessage());
        }
    }


    @Override
    public void addAttendeesToWaitlistBySessionId(Event event, List<Long> userIds, Long sessionId) {
        log.info("addAttendeeToWaitlistBySessionId eventId {} sessionId {}", event.getEventId(), sessionId);
        try {
            Session session = sessionRepoService.getSessionById(sessionId, event);
            if (session == null) {
                throw new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND);
            }

            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }

            // Get userId to eventTicketId mapping efficiently
            Map<Long, Long> userIdToEventTicketIdMap = eventTicketsService.findUserIdToEventTicketIdMapByHolderUserIdsAndEventId(userIds, event.getEventId());

            List<UserSession> existingUserSessions = userSessionRepoService.findBySessionIdAndEventIdAndUserIdIn(sessionId, event.getEventId(), userIds);

            // Create a map for quick lookup of existing sessions by userId
            Map<Long, UserSession> existingSessionMap = existingUserSessions.stream()
                    .collect(Collectors.toMap(UserSession::getUserId, Function.identity(), (existing, replacement) -> existing));

            List<UserSession> sessionsToSave = new ArrayList<>();
            Date currentDate = new Date();

            for(Long userId : userIds){
                UserSession userSession = existingSessionMap.get(userId);
                boolean isExistingSession = userSession != null;

                if (isExistingSession) {
                    // Use existing user session
                    log.info("addAttendeeToWaitlistBySessionId | updating existing userSession for userId: {} and sessionId: {}", userId, sessionId);
                } else {
                    // Create new user session
                    UserSessionDTO userSessionDTO = new UserSessionDTO();
                    userSessionDTO.setUserId(userId);
                    userSessionDTO.setSessionId(session.getId());
                    userSessionDTO.setEventId(event.getEventId());
                    userSession = toEntity(userSessionDTO);
                    log.info("addAttendeeToWaitlistBySessionId | creating new userSession for userId: {} and sessionId: {}", userId, sessionId);
                }

                userSession.setSessionStatus(EnumUserSessionStatus.WAITLISTED);
                userSession.setRegistrationDate(currentDate);

                // Set eventTicketId if available for this user and not already set
                Long eventTicketId = userIdToEventTicketIdMap.get(userId);
                if (eventTicketId != null && (userSession.getEventTicketId() == null || !isExistingSession)) {
                    userSession.setEventTicketId(eventTicketId);
                }

                sessionsToSave.add(userSession);
            }

            // Batch save all user sessions
            if (!sessionsToSave.isEmpty()) {
                userSessionRepoService.saveAll(sessionsToSave);
                log.info("addAttendeeToWaitlistBySessionId | batch saved {} userSessions", sessionsToSave.size());
            }

            log.info("Successfully added in waitlist from portal in session {}", session.getId());
        }catch (Exception exception){
            log.info("Exception occurred while addAttendeeToWaitlistBySessionId eventId {} exception {}", event.getEventId(), exception.getMessage());
        }
    }

    @Override
    public DataTableResponse getAllAttendeeListByEvent(Event event, PageSizeSearchObj pageSizeSearchObj, Long sessionId) {
        log.info("getAllAttendeeListByEvent | event {} | page {} | size {} | search {}", event.getEventId(), pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize(), pageSizeSearchObj.getSearch());

        // Get all attendees excluding registered, checked in, checked out, waitlisted etc.
        Page<User> usersPage = userRepoService.findAllAttendeesWithOnlyBookmarkedSessions(
                event.getEventId(),
                pageSizeSearchObj.getSearch(),
                PageRequest.of(pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize()),
                sessionId
        );

        List<Audience> audienceList = usersPage.getContent().stream()
                .map(user -> {
                    Audience audience = new Audience();
                    audience.setUserId(user.getUserId());
                    audience.setFirstName(user.getFirstName());
                    audience.setLastName(user.getLastName());
                    audience.setEmail(user.getEmail());
                    audience.setPhoto(user.getPhoto());
                    return audience;
                })
                .collect(Collectors.toList());

        log.info("getAllAttendeeListByEvent | audienceList size {} ", audienceList.size());

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(audienceList);
        dataTableResponse.setRecordsTotal(usersPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(usersPage.getTotalElements());
        return dataTableResponse;
    }

    @Override
    public BulkSessionStatusUpdateResultDto updateSessionsStatusInBulk(Event event, User user, List<Long> sessionIds, EnumSessionStatus status) {
        log.info("updateSessionsStatusInBulk eventId {} userId {} sessionIds {} status {}", event.getEventId(), user.getUserId(), sessionIds, status);
        boolean isVisibleStatus = EnumSessionStatus.VISIBLE.equals(status);

         List<Long> updatedSessionIds = new ArrayList<>();
         List<Long> failedSessionIds = new ArrayList<>();

        // Fetch all sessions in one query
        List<Session> sessions = sessionRepoService.getAllSessionByIdsAndEventId(sessionIds, event);
        if (CollectionUtils.isEmpty(sessions)) {
            log.info("No sessions found for event {} and sessionIds {}", event.getEventId(), sessionIds);
            return new BulkSessionStatusUpdateResultDto(updatedSessionIds, failedSessionIds);
        }

        List<Session> mainstageSessions = isVisibleStatus
                ? sessionRepoService.findByEventAndFormat(event.getEventId(), singletonList(MAIN_STAGE))
                : Collections.emptyList();

        Map<Long,Integer> userSessionCountMap = isVisibleStatus
                ? Collections.emptyMap()
                : userSessionRepoService.getUserRegisteredSessionsBySessionIds(sessionIds, event);

        // Separate sessions that pass validation
        List<Session> validSessions = new ArrayList<>();

        for (Session session : sessions) {
            try {
                boolean isValid = isVisibleStatus
                        ? isValidForVisibility(session, mainstageSessions, sessionIds)
                        : isValidForDraft(session, userSessionCountMap);

                if (!isValid) {
                    failedSessionIds.add(session.getId());
                    log.info("failed session update for sessionId: {} and event id: {}", session.getId(), event.getEventId());
                    continue;
                }

                session.setStatus(status);
                validSessions.add(session);
                updatedSessionIds.add(session.getId());

            } catch (Exception e) {
                log.info("Error updating session status for sessionId: {}", session.getId(), e);
                failedSessionIds.add(session.getId());
            }
        }

        // Save all valid sessions in one batch
        if (!validSessions.isEmpty()) {
            log.info("Saving {} sessions status in bulk for event {}", validSessions.size(), event.getEventId());
            sessionRepoService.saveAll(validSessions);
        }
        return new BulkSessionStatusUpdateResultDto(updatedSessionIds, failedSessionIds);
    }

    private boolean isValidForDraft(Session session, Map<Long,Integer> userSessionCountMap) {
        Integer count = userSessionCountMap.get(session.getId());
        return !NumberUtils.isNumberGreaterThanZero(count);
    }


    private boolean isValidForVisibility(Session session, List<Session> mainstageSessions, List<Long> sessionIds) {
        // Check required fields for VISIBLE status
        if (StringUtils.isBlank(session.getTitle()) || session.getStartTime() == null || session.getEndTime() == null) {
            return false;
        }
        if (MAIN_STAGE.equals(session.getFormat())) {
            return mainstageSessions.stream()
                    .filter(s->s.getStartTime() != null && s.getEndTime() != null)
                    .filter(s -> s.getId() != session.getId())// Exclude current session
                    .filter(s -> (s.getStatus().equals(EnumSessionStatus.VISIBLE) || sessionIds.contains(s.getId())))
                    .noneMatch(s -> isTimeOverlap(session, s)); // Return true if no conflicts
        }
        return true;
    }

    /**
     * Create a new speaker from email address using existing service methods
     * @param email Speaker email
     * @param event Event
     * @return Created speaker
     */
    private Speaker createSpeakerFromEmail(String email, Event event) {
        try {
            // Get user info from email if user exists
            FirstLastNameDto userInfo = speakerService.getUserInfoByEmail(email);

            // Create SpeakerDTO with available information
            SpeakerDTO speakerDTO = new SpeakerDTO();
            speakerDTO.setEmail(email);

            if (userInfo != null) {
                speakerDTO.setFirstName(userInfo.getFirstName());
                speakerDTO.setLastName(userInfo.getLastName());
            } else {
                // Extract name from email if no user found
                String emailPrefix = email.substring(0, email.indexOf("@"));
                speakerDTO.setFirstName(emailPrefix);
                speakerDTO.setLastName("");
            }

            // Use existing createSpeaker method, but handle the "already exists" exception
            try {
                Long speakerId = speakerService.createSpeaker(speakerDTO, event);
                return speakerService.getSpeakerById(speakerId, event);
            } catch (NotAcceptableException e) {
                // If speaker already exists, try to find it again
                List<Speaker> existingSpeakers = speakerService.getSpeakerByEventIdAndEmail(event, email);
                if (!CollectionUtils.isEmpty(existingSpeakers)) {
                    return existingSpeakers.get(0);
                }
                throw e;
            }
        } catch (Exception e) {
            log.error("Error creating new speaker with email {}: {}", email, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Create a map of speaker emails to Speaker IDs for efficient lookup
     * Optimized version that fetches only email and ID from database instead of full Speaker objects
     * @param event The event to fetch speakers for
     * @return Map of lowercase email addresses to Speaker IDs
     */
    private Map<String, Long> createEventSpeakerEmailIdMap(Event event) {
        List<SpeakerEmailIdDTO> eventSpeakers = speakerRepoService.findEmailAndIdByEventId(event.getEventId());
        return eventSpeakers.stream()
                .filter(speaker -> StringUtils.isNotBlank(speaker.getEmail()))
                .collect(Collectors.toMap(
                        speaker -> speaker.getEmail().toLowerCase().trim(),
                        SpeakerEmailIdDTO::getSpeakerId,
                        (existing, replacement) -> existing // Keep first occurrence in case of duplicates
                ));
    }

    /**
     * Validate that all emails in a comma-separated string are valid
     * @param emailString Comma-separated email addresses
     * @return true if all emails are valid, false otherwise
     */
    private boolean areAllEmailsValid(String emailString) {
        if (StringUtils.isBlank(emailString)) {
            return true; // Empty string is considered valid (no emails to validate)
        }

        // Use existing utility method to split comma-separated string
        List<String> emails = GeneralUtils.convertCommaSeparatedToList(emailString);
        for (String email : emails) {
            String trimmedEmail = email != null ? email.trim() : "";
            if (StringUtils.isNotBlank(trimmedEmail) && !GeneralUtils.isValidEmailAddress(trimmedEmail)) {
                return false; // Found an invalid email
            }
        }
        return true; // All emails are valid
    }

    /**
     * Validate that speakers exist in the event (optimized version using email-ID map)
     * @param primarySpeakers Comma-separated primary speaker emails
     * @param secondarySpeakers Comma-separated secondary speaker emails
     * @param eventSpeakerEmailIdMap Map of speaker emails to Speaker IDs for the event
     * @throws NotAcceptableException if speakers don't exist in the event
     */
    private void validateSpeakersExistInEvent(String primarySpeakers, String secondarySpeakers, Map<String, Long> eventSpeakerEmailIdMap) {
        List<String> nonExistentSpeakers = new ArrayList<>();

        // Check primary speakers
        if (StringUtils.isNotBlank(primarySpeakers)) {
            Arrays.stream(primarySpeakers.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .map(String::toLowerCase)
                    .filter(email -> !eventSpeakerEmailIdMap.containsKey(email))
                    .forEach(nonExistentSpeakers::add);
        }

        // Check secondary speakers
        if (StringUtils.isNotBlank(secondarySpeakers)) {
            Arrays.stream(secondarySpeakers.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .map(String::toLowerCase)
                    .filter(email -> !eventSpeakerEmailIdMap.containsKey(email))
                    .forEach(nonExistentSpeakers::add);
        }

        if (!nonExistentSpeakers.isEmpty()) {
            String errorMessage = "Speaker(s) not found in event: " + String.join(", ", nonExistentSpeakers);
            NotAcceptableException.SessionSpeakerExceptionMsg exceptionMsg = NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_SPEAKER_NOT_FOUND;
            exceptionMsg.setErrorMessage(errorMessage);
            exceptionMsg.setDeveloperMessage(errorMessage);
            throw new NotAcceptableException(exceptionMsg);
        }
    }

    /**
     * Validate speaker emails for proper syntax and ensure no duplicates between primary and secondary speakers
     * @param primarySpeakers Comma-separated primary speaker emails
     * @param secondarySpeakers Comma-separated secondary speaker emails
     * @throws NotAcceptableException if invalid email syntax found or duplicate emails found between columns
     */
    private void validateSpeakerEmailsAndDuplicates(String primarySpeakers, String secondarySpeakers) {
        if (StringUtils.isBlank(primarySpeakers) || StringUtils.isBlank(secondarySpeakers)) {
            return; // No validation needed if either column is empty
        }

        // Validate individual emails in comma-separated strings
        if (!areAllEmailsValid(primarySpeakers) || !areAllEmailsValid(secondarySpeakers)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_EMAIL);
        }

        // Parse and normalize primary speaker emails
        Set<String> primaryEmails = Arrays.stream(primarySpeakers.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(String::toLowerCase) // Normalize to lowercase for comparison
                .collect(Collectors.toSet());

        // Parse and normalize secondary speaker emails
        Set<String> secondaryEmails = Arrays.stream(secondarySpeakers.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(String::toLowerCase) // Normalize to lowercase for comparison
                .collect(Collectors.toSet());

        // Find duplicates
        Set<String> duplicates = primaryEmails.stream()
                .filter(secondaryEmails::contains)
                .collect(Collectors.toSet());

        if (!duplicates.isEmpty()) {
            String duplicateList = String.join(", ", duplicates);
            String errorMessage = SESSION_SPEAKER_DUPLICATE.getErrorMessage().replace("{duplicateList}", duplicateList);

            throw new NotAcceptableException(SESSION_SPEAKER_DUPLICATE.getStatusCode(), errorMessage, errorMessage);
        }
    }



    private boolean isTimeOverlap(Session s1, Session s2) {
        return s1.getStartTime().before(s2.getEndTime()) &&
                s1.getEndTime().after(s2.getStartTime());
    }

    /**
     * Process speaker emails and add them to session associations
     * @param speakerEmails Comma-separated speaker emails
     * @param sessionId Session ID to associate speakers with
     * @param isModerator Whether these speakers should be moderators
     * @param speakerType Type of speakers (for logging) - "primary" or "secondary"
     * @param speakerSessionAssociations Map to store speaker-session associations
     */
    private void processSpeakerEmails(String speakerEmails, Long sessionId, boolean isModerator, String speakerType, Map<String, Set<SpeakerSessionBatchDTO>> speakerSessionAssociations) {
        // Enforce business rule: Only primary speakers can be moderators
        if ("secondary".equals(speakerType) && isModerator) {
            log.warn("Attempted to set secondary speaker as moderator for session {}. Secondary speakers cannot be moderators.", sessionId);
            isModerator = false; // Force secondary speakers to be non-moderators
        }
        if (StringUtils.isBlank(speakerEmails)) {
            return;
        }

        String[] emails = speakerEmails.split(",");
        for (String email : emails) {
            email = email != null ? email.trim().toLowerCase() : ""; // Normalize email safely
            if (StringUtils.isNotBlank(email) && isValidEmailAddress(email)) {
                speakerSessionAssociations.computeIfAbsent(email, k -> new HashSet<>())
                          .add(new SpeakerSessionBatchDTO(sessionId, isModerator));
            } else if (StringUtils.isNotBlank(email)) {
                log.warn("Invalid {} speaker email '{}' for session {}", speakerType, email, sessionId);
            }
        }
    }

    /**
     * Collect primary and secondary speaker associations for bulk processing
     * Business Rule: Only primary speakers can be moderators, secondary speakers are always non-moderators
     * Only processes speakers for MAIN_STAGE, BREAKOUT_SESSION, and WORKSHOP formats
     */
    private void collectSpeakerSessionAssociations(UploadSessionDto uploadSessionDto, Session session, Map<String, Set<SpeakerSessionBatchDTO>> speakerSessionAssociations) {
        if (uploadSessionDto == null || session == null) {
            log.warn("Invalid parameters for speaker data collection: uploadSessionDto={}, session={}", uploadSessionDto != null, session != null);
            return;
        }

        // Only process speakers for specific session formats
        if (!EnumSet.of(MAIN_STAGE, BREAKOUT_SESSION, EnumSessionFormat.WORKSHOP).contains(session.getFormat())) {
            log.debug("Skipping speaker processing for session {} with format {} - speakers only supported for MAIN_STAGE, BREAKOUT_SESSION, and WORKSHOP", session.getId(), session.getFormat());
            return;
        }

        // Process primary speakers (only primary speakers can be moderators)
        processSpeakerEmails(uploadSessionDto.getPrimarySpeakers(), session.getId(), true, "primary", speakerSessionAssociations);

        // Process secondary speakers (secondary speakers are never moderators)
        processSpeakerEmails(uploadSessionDto.getSecondarySpeakers(), session.getId(), false, "secondary", speakerSessionAssociations);
    }

    /**
     * Process all collected speaker-session associations with optimized bulk operations (optimized version)
     * Links existing speakers to sessions with proper moderator status using email-ID map
     * @return Map containing only the newly created speaker-session associations for invitation purposes
     */
    private Map<String, Set<SpeakerSessionBatchDTO>> processAllSpeakerSessionAssociations(Map<String, Set<SpeakerSessionBatchDTO>> speakerSessionAssociations, Map<String, Long> eventSpeakerEmailIdMap, Event event) {
        if (speakerSessionAssociations.isEmpty()) {
            log.info("No speaker-session associations to process for event {}", event.getEventId());
            return new HashMap<>();
        }

        try {
            log.info("Processing {} unique speakers with optimized bulk operations for event {}", speakerSessionAssociations.size(), event.getEventId());

            // Create session-speaker associations (all speakers are already validated to exist)
            // This method now returns only the newly created associations
            Map<String, Set<SpeakerSessionBatchDTO>> newAssociations = createAllSessionSpeakerAssociationsOptimized(speakerSessionAssociations, eventSpeakerEmailIdMap, event);

            int totalAssociations = speakerSessionAssociations.values().stream().mapToInt(Set::size).sum();
            int newAssociationsCount = newAssociations.values().stream().mapToInt(Set::size).sum();
            log.info("Successfully processed {} existing speakers with {} session associations ({} new associations for invitations)", speakerSessionAssociations.size(), totalAssociations, newAssociationsCount);

            return newAssociations;

        } catch (Exception e) {
            log.error("Error processing speaker-session associations for event {}: {}", event.getEventId(), e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * Create all session-speaker associations with optimized bulk operations (optimized version)
     * Links speakers to sessions with proper moderator status using email-ID map
     * @return Map containing only the newly created speaker-session associations for invitation purposes
     */
    private Map<String, Set<SpeakerSessionBatchDTO>> createAllSessionSpeakerAssociationsOptimized(Map<String, Set<SpeakerSessionBatchDTO>> speakerSessionAssociations, Map<String, Long> eventSpeakerEmailIdMap, Event event) {
        try {
            // Get speaker email-ID mapping
            Map<String, Long> speakerEmailIdMap = (eventSpeakerEmailIdMap != null && !eventSpeakerEmailIdMap.isEmpty())
                    ? eventSpeakerEmailIdMap
                    : createEventSpeakerEmailIdMap(event);

            // Collect all session IDs and speaker IDs for batch fetching
            Set<Long> sessionIds = new HashSet<>();
            Set<Long> speakerIds = new HashSet<>();

            for (Map.Entry<String, Set<SpeakerSessionBatchDTO>> entry : speakerSessionAssociations.entrySet()) {
                String email = entry.getKey();
                Long speakerId = speakerEmailIdMap.get(email);
                if (speakerId != null) {
                    speakerIds.add(speakerId);
                    for (SpeakerSessionBatchDTO info : entry.getValue()) {
                        sessionIds.add(info.getSessionId());
                    }
                }
            }

            // Fetch existing associations for the event and filter by our session/speaker IDs
            Map<String, SessionSpeaker> existingAssociations = new HashMap<>();
            Map<String, SessionSpeaker> allExistingAssociationsForSessions = new HashMap<>();
            if (!sessionIds.isEmpty() && !speakerIds.isEmpty()) {
                List<SessionSpeaker> allEventAssociations = sessionSpeakerRepoService.findByEventIdWithoutCache(event.getEventId());
                for (SessionSpeaker ss : allEventAssociations) {
                    String key = ss.getSessionId() + HYPHEN + ss.getSpeakerId();
                    // Store all existing associations for the sessions we're processing
                    if (sessionIds.contains(ss.getSessionId())) {
                        allExistingAssociationsForSessions.put(key, ss);
                    }
                    // Store only the ones that match our current speaker-session combinations
                    if (sessionIds.contains(ss.getSessionId()) && speakerIds.contains(ss.getSpeakerId())) {
                        existingAssociations.put(key, ss);
                    }
                }
            }

            List<SessionSpeaker> sessionSpeakersToSave = new ArrayList<>();
            Set<String> processedAssociations = new HashSet<>();

            // Track newly created associations for invitation purposes
            Map<String, Set<SpeakerSessionBatchDTO>> newSpeakerSessionAssociations = new HashMap<>();

            for (Map.Entry<String, Set<SpeakerSessionBatchDTO>> entry : speakerSessionAssociations.entrySet()) {
                String email = entry.getKey();
                Set<SpeakerSessionBatchDTO> sessionInfos = entry.getValue();
                Long speakerId = speakerEmailIdMap.get(email);

                if (speakerId != null) {
                    for (SpeakerSessionBatchDTO info : sessionInfos) {
                        String associationKey = info.getSessionId() + HYPHEN + speakerId;

                        if (processedAssociations.contains(associationKey)) {
                            log.warn("Skipping duplicate session-speaker association: Session {} - Speaker {} ({})", info.getSessionId(), speakerId, email);
                            continue;
                        }

                        SessionSpeaker sessionSpeaker = existingAssociations.get(associationKey);

                        if (sessionSpeaker != null) {
                            // Update existing association - no invitation needed
                            sessionSpeaker.setModerator(info.isModerator());
                            sessionSpeaker.setShowModerator(true);
                            log.debug("Updated existing session-speaker association: Session {} - Speaker {} ({}) - Moderator: {}", info.getSessionId(), speakerId, email, info.isModerator());
                        } else {
                            // Create new association - invitation needed
                            sessionSpeaker = new SessionSpeaker(info.getSessionId(), speakerId, 1000.0, null, true);
                            sessionSpeaker.setModerator(info.isModerator());
                            sessionSpeaker.setShowModerator(true);
                            log.debug("Created new session-speaker association: Session {} - Speaker {} ({}) - Moderator: {}", info.getSessionId(), speakerId, email, info.isModerator());

                            // Track this as a new association for invitation
                            newSpeakerSessionAssociations.computeIfAbsent(email, k -> new HashSet<>()).add(info);
                        }

                        sessionSpeakersToSave.add(sessionSpeaker);
                        processedAssociations.add(associationKey);
                    }
                } else {
                    log.warn("Speaker not found for email {} in event {}, skipping associations", email, event.getEventId());
                }
            }

            // Bulk save all session-speaker associations (both new and updated)
            if (!sessionSpeakersToSave.isEmpty()) {
                sessionSpeakerRepoService.saveAll(sessionSpeakersToSave);
                log.info("Bulk saved {} session-speaker associations for event {}", sessionSpeakersToSave.size(), event.getEventId());
            }

            // Handle soft deletion of associations that are no longer needed
            List<SessionSpeaker> associationsToDelete = new ArrayList<>();
            for (Map.Entry<String, SessionSpeaker> entry : allExistingAssociationsForSessions.entrySet()) {
                String existingKey = entry.getKey();
                SessionSpeaker existingAssociation = entry.getValue();

                // If this existing association is not in our processed associations, it should be soft deleted
                if (!processedAssociations.contains(existingKey)) {
                    existingAssociation.setRecordStatus(RecordStatus.DELETE);
                    associationsToDelete.add(existingAssociation);
                    log.debug("Marking session-speaker association for soft deletion: Session {} - Speaker {}", existingAssociation.getSessionId(), existingAssociation.getSpeakerId());
                }
            }

            // Bulk save soft deletions
            if (!associationsToDelete.isEmpty()) {
                sessionSpeakerRepoService.saveAll(associationsToDelete);
                log.info("Soft deleted {} session-speaker associations for event {}", associationsToDelete.size(), event.getEventId());
            }

            // Return only the newly created associations for invitation purposes
            return newSpeakerSessionAssociations;

        } catch (Exception e) {
            log.error("Error creating/updating session-speaker associations for event {}: {}", event.getEventId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Send speaker invitations after CSV upload processing is complete (optimized version)
     * Only sends invitations to speakers with newly created session associations
     * Only sends invitations if speaker invites are enabled for the event
     * Uses async processing to improve performance and avoid blocking the main thread
     */
    private void sendSpeakerInvitationsAfterCSVUpload(Map<String, Set<SpeakerSessionBatchDTO>> newSpeakerSessionAssociations, Event event, User loggedInUser) {
        // Check if speaker invitations are enabled for this event
        boolean isSpeakerInviteEnable = virtualEventService.isSpeakerInviteEnable(event);
        if (!isSpeakerInviteEnable) {
            log.info("Speaker invitations are disabled for event {}, skipping email sending", event.getEventId());
            return;
        }

        if (newSpeakerSessionAssociations.isEmpty()) {
            log.info("No new speaker associations found for event {}, skipping invitation emails", event.getEventId());
            return;
        }

        try {
            log.info("Sending speaker invitations for {} newly added speakers in event {} using async batch processing", newSpeakerSessionAssociations.size(), event.getEventId());

            // Process invitations asynchronously using only newly created associations
            processInvitationsAsync(newSpeakerSessionAssociations, event, loggedInUser);

            log.info("Initiated async speaker invitation processing for {} new speakers in event {}", newSpeakerSessionAssociations.size(), event.getEventId());

        } catch (Exception e) {
            log.error("Error initiating speaker invitations for event {}: {}", event.getEventId(), e.getMessage(), e);
        }
    }

    /**
     * Process speaker invitations asynchronously to improve performance (optimized version)
     * This method uses async processing to send invitations without blocking the main thread
     * and minimizes database calls by processing in batches
     * Only processes newly created speaker-session associations to avoid duplicate invitations
     */
    @Async
    private void processInvitationsAsync(Map<String, Set<SpeakerSessionBatchDTO>> newSpeakerSessionAssociations, Event event, User loggedInUser) {

        int totalInvitations = 0;
        int successfulInvitations = 0;

        try {
            // Get speaker email-ID map for efficient lookup
            Map<String, Long> speakerEmailIdMap = createEventSpeakerEmailIdMap(event);

            for (Map.Entry<String, Set<SpeakerSessionBatchDTO>> entry : newSpeakerSessionAssociations.entrySet()) {
                String speakerEmail = entry.getKey();
                Set<SpeakerSessionBatchDTO> sessionAssociations = entry.getValue();

                Long speakerId = speakerEmailIdMap.get(speakerEmail.toLowerCase().trim());
                if (speakerId == null) {
                    log.warn("Speaker not found for email {} in event {}, skipping invitation", speakerEmail, event.getEventId());
                    continue;
                }

                // Send invitation for each session this speaker is associated with
                for (SpeakerSessionBatchDTO sessionInfo : sessionAssociations) {
                    totalInvitations++;
                    try {
                        // Use the existing speakerHelperService.sendInvite method
                        // This method handles all the email template logic and async sending
                        speakerHelperService.sendInvite(event, loggedInUser, sessionInfo.getSessionId(), speakerId);
                        successfulInvitations++;

                        log.debug("Sent invitation to speaker {} (ID: {}) for session {} in event {}", speakerEmail, speakerId, sessionInfo.getSessionId(), event.getEventId());
                    } catch (Exception e) {
                        log.error("Failed to send invitation to speaker {} (ID: {}) for session {} in event {}: {}", speakerEmail, speakerId, sessionInfo.getSessionId(), event.getEventId(), e.getMessage(), e);
                    }
                }
            }

            log.info("Completed async speaker invitation processing for event {}: {}/{} invitations sent successfully", event.getEventId(), successfulInvitations, totalInvitations);

        } catch (Exception e) {
            log.error("Error in async speaker invitation processing for event {}: {}", event.getEventId(), e.getMessage(), e);
        }
    }

    // to check custom attribute validation
    private List<AttributeKeyValueDto> validCustomAttribute(List<AttributeKeyValueDto> customAttributes , List<CustomFormAttribute> customFormAttributes,Event event){
        List<AttributeKeyValueDto> customAttributesErrorMessage = new ArrayList<>();
        for(AttributeKeyValueDto attributeKeyValueDto: customAttributes) {
            if (!StringUtils.isBlank(attributeKeyValueDto.getKey())) {
                String key = attributeKeyValueDto.getKey();
                String fieldValue = attributeKeyValueDto.getValue();
                AttributeValueType attributeType = customFormAttributes.stream().filter(e -> e.getName().equalsIgnoreCase(key)).findFirst().get().getAttributeValueType();
                List<String> validValuesForMultipleChoiceAndDropDownFields = new ArrayList<>();
                // if attribute type is dropdown or multiple choice then get default values of the fields
                if(attributeType.equals(AttributeValueType.DROPDOWN) || attributeType.equals(AttributeValueType.MULTIPLE_CHOICE)){
                    List<CustomFormAttribute> attributeFields  = customFormAttributeRepository.findByNameAndEventAndRecurringEventIdNullAndAttributeType(key,event,AttributeType.SESSION);
                    if(!CollectionUtils.isEmpty(attributeFields)){
                        CustomFormAttribute customFormAttribute = attributeFields.get(0);
                        // get the list of valid values of attribute type dropdown and multiple choice
                        if(customFormAttribute != null) {
                            validValuesForMultipleChoiceAndDropDownFields = contactService.parseOptionsFromDefaultValue(customFormAttribute.getDefaultValue());
                        }
                    }
                }
                if (!StringUtils.isBlank(fieldValue) && !isValidFieldValueByType(fieldValue, attributeType,validValuesForMultipleChoiceAndDropDownFields)) {
                    switch (attributeType) {
                        case TEXT:
                            customAttributesErrorMessage.add(new AttributeKeyValueDto(key,"Header " + key + " Value must be text with a maximum of 100 characters."));
                            break;
                        case NUMBER:
                            customAttributesErrorMessage.add(new AttributeKeyValueDto(key,"Header " + key + " Value must be a valid numeric."));
                            break;
                        case DATE:
                            customAttributesErrorMessage.add(new AttributeKeyValueDto(key,"Header " + key + " Value must be a valid date in the format MM/dd/yyyy."));
                            break;
                        case DROPDOWN:
                            customAttributesErrorMessage.add(new AttributeKeyValueDto(key,"Header " + key + " Value must be one of the allowed options → "
                                    + validValuesForMultipleChoiceAndDropDownFields.stream().collect(Collectors.joining(" | "))));
                            break;
                        case MULTIPLE_CHOICE:
                            customAttributesErrorMessage.add(new AttributeKeyValueDto(key,"Header " + key + " Each value must be chosen from → "
                                    + validValuesForMultipleChoiceAndDropDownFields.stream().collect(Collectors.joining(" | "))
                                    + " (separate multiple choices with '|')."));
                            break;
                    }
                }
            }
        }

        return customAttributesErrorMessage;
    }

    private boolean isValidFieldValueByType(String fieldValue, AttributeValueType fieldType,List<String> validValuesForMultipleChoiceAndDropDownFields) {

        try {
            switch (fieldType) {
                case TEXT:
                    return fieldValue.length() <= 100;
                case NUMBER:
                    return fieldValue.length() <= 500 && fieldValue.matches("\\d+");
                case DATE:
                    return DateUtils.isValidDate(fieldValue, DATE_FORMAT_ONLY_MONTH);
                case DROPDOWN:
                    return validValuesForMultipleChoiceAndDropDownFields.contains(fieldValue);
                case MULTIPLE_CHOICE:
                    return Arrays.stream(fieldValue.split("\\|"))
                            .map(String::trim)
                            .allMatch(validValuesForMultipleChoiceAndDropDownFields::contains);
                default:
                    return true;
            }
        } catch (Exception e) {
            log.error("Error validating fieldValue {} of type {} : |  errorMsg : {}", fieldValue, fieldType, e.getMessage());
            return false;
        }
    }
    /**
     * Handles validation of custom attributes and appends any validation errors
     * to the provided errorMessage while also setting them in the invalid session DTO.
     *
     * @param attributeKeyValueDtos list of attribute key-value DTOs from session upload
     * @param customFormAttributes  list of form attributes for validation
     * @param event                 current event
     * @param invalidSessions       DTO holding invalid session details
     * @param errorMessage          existing error message string
     * @return updated error message string including custom attribute errors (if any)
     */
    private String handleCustomAttributesErrors(List<AttributeKeyValueDto> attributeKeyValueDtos,
                                                List<CustomFormAttribute> customFormAttributes,
                                                Event event,
                                                UploadSessionDto invalidSessions,
                                                String errorMessage) {

        if (CollectionUtils.isEmpty(attributeKeyValueDtos)) {
            return errorMessage;
        }

        List<AttributeKeyValueDto> customAttributesErrorMessage =
                validCustomAttribute(attributeKeyValueDtos, customFormAttributes, event);

        if (!CollectionUtils.isEmpty(customAttributesErrorMessage)) {
            // Collect error messages (confirm if you need getValue() or getErrorMessage())
            String joinedErrors = customAttributesErrorMessage.stream()
                    .map(AttributeKeyValueDto::getValue)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(", "));

            if (StringUtils.isNotBlank(joinedErrors)) {
                errorMessage = StringUtils.isNotBlank(errorMessage)
                        ? errorMessage.concat(", ").concat(joinedErrors)
                        : joinedErrors;
            }

            // Store structured errors in session DTO
            invalidSessions.setCustomAttributesErrorMessage(customAttributesErrorMessage);
        }

        return errorMessage;
    }


}
