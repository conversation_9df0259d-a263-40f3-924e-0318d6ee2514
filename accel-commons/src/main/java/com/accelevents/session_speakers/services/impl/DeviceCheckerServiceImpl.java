package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.DeviceCheckerType;
import com.accelevents.domain.session_speakers.DeviceChecker;
import com.accelevents.session_speakers.DeviceCheckerRepoService;
import com.accelevents.session_speakers.dto.DeviceCheckerDto;
import com.accelevents.session_speakers.dto.UserDeviceCheckedDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DeviceCheckerServiceImpl implements DeviceCheckerService {

    private Logger log = LoggerFactory.getLogger(DeviceCheckerServiceImpl.class);

    @Autowired
    private DeviceCheckerRepoService deviceCheckerRepoService;

    @Override
    public void addSpeakerDeviceChecker(Long userId, Event event, DeviceCheckerDto deviceCheckerDto) {
            DeviceChecker deviceChecker = new DeviceChecker();
            deviceChecker.setBrowserName(deviceCheckerDto.getBrowserName());
            deviceChecker.setBrowserVersion(deviceCheckerDto.getBrowserVersion());
            deviceChecker.setMachineId(deviceCheckerDto.getMachineId());
            deviceChecker.setCreatedAt(new Date());
            deviceChecker.setType(deviceCheckerDto.getType());
            deviceChecker.setEventId(event.getEventId());
            deviceChecker.setUserId(userId);
            deviceCheckerRepoService.save(deviceChecker);
            log.info("DeviceCheckerServiceImpl addSpeakerDeviceChecker event {} userId {} ",event.getEventId(), userId);
    }

    @Override
    public Map<Long, Boolean> speakerIdAndRunDeviceChecker(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return Collections.emptyMap();
        }
        List<UserDeviceCheckedDto> getAllUserIdDeviceChecker = deviceCheckerRepoService.userIdAndRunDeviceChecker(userIds);
        Map<Long, Boolean> speakerIdDeviceChecker=null;
        if (!CollectionUtils.isEmpty(getAllUserIdDeviceChecker)){
            speakerIdDeviceChecker = getAllUserIdDeviceChecker.stream().collect(Collectors.toMap(UserDeviceCheckedDto::getUserId, UserDeviceCheckedDto::getDeviceChecked));
        }else {
            return Collections.emptyMap();
        }
        log.info("DeviceCheckerServiceImpl speakerIdAndRunDeviceChecker userIds {} ",userIds);
        return speakerIdDeviceChecker;
    }

    @Override
    public String checkSpeakerDeviceCheckerWithDeviceId(Long id, Event event, String machineId, DeviceCheckerType deviceCheckerType,String languageCode) {
        return deviceCheckerRepoService.checkUserDeviceCheckerWithDeviceId(id,event,machineId,deviceCheckerType,languageCode);
    }
}
