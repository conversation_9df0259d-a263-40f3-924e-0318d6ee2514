package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.enums.EnumSessionCheckInLogStatus;
import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.domain.session_speakers.SessionCheckInLog;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.session_speakers.services.SessionCheckInLogRepoService;
import com.accelevents.session_speakers.services.SessionCheckInLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class SessionCheckInLogServiceImpl implements SessionCheckInLogService {

    private static final Logger log = LoggerFactory.getLogger(SessionCheckInLogServiceImpl.class);

    @Autowired
    private SessionCheckInLogRepoService sessionCheckInLogRepoService;

    private static final List<EnumUserSessionStatus> checkOutStatus = Arrays.asList(EnumUserSessionStatus.CHECK_OUT, EnumUserSessionStatus.MEETING_LEFT);


    @Override
    public void createSessionCheckInLog(UserSession userSession, Long auditByUserId, String device) {
        if (userSession != null && userSession.getCheckInTime() != null) {
            SessionCheckInLog sessionCheckInLogOptional = sessionCheckInLogRepoService.findFirstByUserIdAndSessionIdAndEventTicketIdOrderByIdDesc(userSession.getUserId(), userSession.getSessionId(), userSession.getEventTicketId());

            if(sessionCheckInLogOptional != null && !checkOutStatus.contains(userSession.getCheckInStatus()) && EnumSessionCheckInLogStatus.CHECK_IN.equals(sessionCheckInLogOptional.getAuditStatus())) {
                sessionCheckInLogOptional.setAuditTime(new Date());
                sessionCheckInLogRepoService.save(sessionCheckInLogOptional);
                log.info("Session CheckIn Log updated for sessionId {} userId {} auditByUserId {}", userSession.getSessionId(), userSession.getUserId(), auditByUserId);
            }else {
                SessionCheckInLog sessionCheckInLog = new SessionCheckInLog();
                sessionCheckInLog.setSessionId(userSession.getSessionId());
                sessionCheckInLog.setEventId(userSession.getEventId());
                sessionCheckInLog.setUserId(userSession.getUserId());
                sessionCheckInLog.setEventTicketId(userSession.getEventTicketId());

                if (checkOutStatus.contains(userSession.getCheckInStatus())) {
                    sessionCheckInLog.setAuditStatus(EnumSessionCheckInLogStatus.CHECK_OUT);
                } else {
                    sessionCheckInLog.setAuditStatus(EnumSessionCheckInLogStatus.CHECK_IN);
                }
                sessionCheckInLog.setAuditBy(auditByUserId);
                sessionCheckInLog.setDevice(device);
                sessionCheckInLogRepoService.save(sessionCheckInLog);
            }
        }

    }
}
