package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.WorkshopRecordingAssetDetails;
import com.accelevents.enums.UserRole;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.ChimeService;
import com.accelevents.services.S3Wrapper;
import com.accelevents.session_speakers.dto.ChimeMessageDetailsDto;
import com.accelevents.session_speakers.services.*;
import com.accelevents.session_speakers.services.impl.cache.ChimeRecordingGraphQLHandler;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.SessionUtils;
import com.amazonaws.services.chimesdkmeetings.model.Meeting;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class WorkshopSessionRecordingServiceImpl implements WorkshopSessionRecordingService {

    private static final Logger logger = LoggerFactory.getLogger(WorkshopSessionRecordingServiceImpl.class);

    @Autowired
    private SessionRepoService sessionRepoService;

    @Autowired
    private ChimeService chimeService;

    @Autowired
    private WorkshopRecordingAssetsRepoService workshopRecordingAssetsRepoService;

    @Autowired
    private SessionDetailsRepoService sessionDetailsRepoService;

    @Autowired
    private WorkshopRecordingAssetService workshopRecordingAssetService;

    @Autowired
    private ChimeRecordingGraphQLHandler chimeRecordingGraphQLHandler;

    @Autowired
    private GraphQLConfiguration graphQLConfiguration;

    @Autowired
    private ROUserService roUserService;
    @Autowired
    private SessionSpeakerService sessionSpeakerService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private S3Wrapper s3Wrapper;
    @Value("${cloud.aws.s3.bucket.workshopRecording}")
    private String workShopRecordingBucket;

    private BroadcastGraphQLHandler broadcastGraphQLHandler;
    private CommandCenterGraphQLHandler commandCenterGraphQLHandler;

    @Autowired
    public WorkshopSessionRecordingServiceImpl(GraphQLConfiguration graphQLConfiguration) {
        this.broadcastGraphQLHandler = new BroadcastGraphQLHandler(graphQLConfiguration, Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
        this.commandCenterGraphQLHandler = new CommandCenterGraphQLHandler(graphQLConfiguration, Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
    }


    @Override
    public void startChimeMediaCapturePipeLine(Session session, Event event, ChimeMessageDetailsDto chimeMessageDetailsDto) {
        try {
            boolean isRecord = sessionDetailsRepoService.getSessionRecordBySession(session.getId());
            Optional<WorkshopRecordingAssetDetails> assetDetailsOptional = workshopRecordingAssetsRepoService.findBySessionIdAndAttendeeCountIsNotNullOrPipelineIdIsNull(session.getId());
            logger.info("startChimeMediaCapturePipeLine,sessionId=>{}, Workshop recording asset present=>{}", assetDetailsOptional.isPresent(), session.getId());
            WorkshopRecordingAssetDetails assetDetails = assetDetailsOptional.isPresent() ? assetDetailsOptional.get() : new WorkshopRecordingAssetDetails(session.getId(), event.getEventId());
            String pipelineId = assetDetails.getPipelineId();
            logger.info("startChimeMediaCapturePipeLine call for sessionId=>{}, pipelineId=>{} Recording=>{}", session.getId(), pipelineId, isRecord);
            if (StringUtils.isNotBlank(session.getMeetingObj()) && StringUtils.isBlank(pipelineId) && isRecord) {
                Meeting meeting = SessionUtils.getMeeting(session.getMeetingObj());
                String meetingHostId = Constants.WORKSHOP + Constants.STRING_UNDERSCORE + event.getEventId() + Constants.STRING_UNDERSCORE + session.getId();
                chimeService.createMediaCaptureForWorkshop(meetingHostId, meeting.getMeetingId(), session, true, chimeMessageDetailsDto);
                // Set Status recording means workshop is being recorded,it is use to inform the attendee
                broadcastGraphQLHandler.handleCreateOrUpdateSessionStatusLogs(Constants.VIDEO_LIVE_STREAM_RECORDING, session); // Set Status recording means workshop is being recorded,it is use to show status on Portal page
                logger.info("Chime media capture pipeline stared, Session=>{}, eventId=>{}, pipelineId=>{}", session.getId(), event.getEventId(), session.getTaskId());
            } else {
                if (null != chimeMessageDetailsDto) {
                    addAttendeeInAssetDetails(chimeMessageDetailsDto, assetDetails);
                }
            }
            if(chimeMessageDetailsDto != null && chimeMessageDetailsDto.getExternalUserId() !=null){
                sendCommandCenterNotification(chimeMessageDetailsDto.getExternalUserId(),session,Boolean.TRUE);
            }
        } catch (Exception ex) {
            logger.error("Error While Start media capture pipeline, SessionId=>{}", session.getId(), ex);
        }
    }

    private void addAttendeeInAssetDetails(ChimeMessageDetailsDto chimeMessageDetailsDto, WorkshopRecordingAssetDetails assetDetails) {
        List<String> attendeeList = GeneralUtils.convertCommaSeparatedToList(assetDetails.getAttendeeCount());
        if (!attendeeList.contains(chimeMessageDetailsDto.getAttendeeId())) {
            attendeeList.add(chimeMessageDetailsDto.getAttendeeId());
            String attendeeListString = attendeeList.stream().collect(Collectors.joining(Constants.STRING_COMMA));
            assetDetails.setAttendeeCount(attendeeListString);
            workshopRecordingAssetsRepoService.save(assetDetails);
        }
        logger.info("Attendee joined meeting meetingId=>{}, attendeeId=>{}, total attendee =>{}", chimeMessageDetailsDto.getMeetingId(), chimeMessageDetailsDto.getAttendeeId(), attendeeList);
    }

    @Override
    public void stopChimeMediaCapturePipeLine(Session session, Event event, String chimeEventType, ChimeMessageDetailsDto chimeMessageDetailsDto) {
        try {
            workshopRecordingAssetsRepoService.findBySessionIdAndAttendeeCountIsNotNull(session.getId()).ifPresent(assetsDetails -> {
                logger.info("Attendee left from meeting process started for meetingId {}, attendeeId {} attendees {}", chimeMessageDetailsDto.getMeetingId(), chimeMessageDetailsDto.getAttendeeId(), assetsDetails.getAttendeeCount());
                List<String> attendeeList = GeneralUtils.convertCommaSeparatedToList(assetsDetails.getAttendeeCount());
                if (attendeeList.size() > 1 && !Constants.CHIME_MEETING_ENDED.equals(chimeMessageDetailsDto.getEventType())) {
                    attendeeList.remove(chimeMessageDetailsDto.getAttendeeId());
                    String attendeeListString = attendeeList.stream().collect(Collectors.joining(Constants.STRING_COMMA));
                    assetsDetails.setAttendeeCount(attendeeListString);
                    workshopRecordingAssetsRepoService.save(assetsDetails);
                } else {
                    String pipelineId = assetsDetails.getPipelineId();
                    stopMediaCapture(session, pipelineId);
                    if (StringUtils.isBlank(chimeMessageDetailsDto.getMeetingId())) {
                        workshopRecordingAssetService.deleteWorkshopAssetFromS3Bucket(assetsDetails.getFileLocation());
                        assetsDetails.setFileLocation(null);
                        assetsDetails.setPipelineId(null);
                    } else {
                        if (StringUtils.isNotBlank(pipelineId)) {
                            String concatenationPipeline = chimeService.startChimeMediaConcatenationPipeline(pipelineId, assetsDetails.getFileLocation(), session.getId());
                            if(StringUtils.isNotBlank(concatenationPipeline)) {
                                chimeRecordingGraphQLHandler.handleCreateOrUpdateSessionStatusLogsForWorkshopAssetWithAsync(Constants.PROCESSING, assetsDetails.getId(), assetsDetails.getSessionId());
                                long duration = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis() - assetsDetails.getCreatedAt().getTime());
                                assetsDetails.setDuration(duration);
                                assetsDetails.setFileName(concatenationPipeline);
                                assetsDetails.setPlayBackId(workshopRecordingAssetService.getWorkshopRecordingPlayBackId(assetsDetails));
                                assetsDetails.setAssetStatus(WorkshopRecordingAssetDetails.ProcessStatus.PROCESSING);
                            }
                        }
                        assetsDetails.setAttendeeCount(null);
                    }
                    workshopRecordingAssetsRepoService.save(assetsDetails);
                    logger.info("Workshop session recording stop for sessionId=>{}", session.getId());
                }
                logger.info("Attendee left from meeting meetingId=>{}, attendeeId=>{}, total attendee =>{}", chimeMessageDetailsDto.getMeetingId(),
                        chimeMessageDetailsDto.getAttendeeId(), attendeeList);
            });
            if(StringUtils.isNotEmpty(chimeMessageDetailsDto.getExternalUserId())){
                sendCommandCenterNotification(chimeMessageDetailsDto.getExternalUserId(),session,Boolean.FALSE);
            }
        } catch (Exception e) {
            logger.error("Error While Stopping Workshop session recording. SessionId=> {}", session.getId(), e);
        }
    }

    private void stopMediaCapture(Session session, String pipelineId) {
        try {
            if (StringUtils.isNotBlank(pipelineId)) {
                broadcastGraphQLHandler.handleCreateOrUpdateSessionStatusLogs(Constants.VIDEO_LIVE_STREAM_IDLE, session); // Set Status idle means recording is stopped
                chimeService.deleteMediaCapturePipeline(pipelineId);
            }
        } catch (Exception ne) {
            logger.info("Chime media capture pipeline not found", ne);
        }
    }

    @Override
    public void updateWorkshopRecordingMetaData(WorkshopRecordingAssetDetails assetDetail) {
        StringBuilder playbackId = new StringBuilder(assetDetail.getFileLocation()).append(Constants.SLASH_PROCESSED_SLASH).append(Constants.COMPOSITED_VIDEO).append(Constants.STRING_SLASH)
                .append(assetDetail.getFileName()).append(Constants.DOT_MP4);
        s3Wrapper.updateS3ObjectMetadataSingleObject(workShopRecordingBucket, playbackId.toString());
        logger.info("Workshop Recording s3 object metadata update for assetId {} fileLocation {} fileName {}", assetDetail.getId(), assetDetail.getFileLocation(), assetDetail.getFileName());
    }

    private void sendCommandCenterNotification(String externalUserId, Session session, Boolean isJoined ){
        Long userId = getUserIdFromExternalUserId(externalUserId);
        Optional<User> userOptional = roUserService.findByIdOp(userId);

        if(userOptional.isPresent() && session != null && session.getEvent() != null){
            User user = userOptional.get();

            UserRole userRole = roStaffService.getUserRole(user, session.getEvent(), null);
            boolean eventAdmin = ((userRole!=null) && (userRole.equals(UserRole.ROLE_ADMIN) || userRole.equals(UserRole.ROLE_WHITELABELADMIN) || userRole.equals(UserRole.ROLE_EVENT_COORDINATOR)));
            boolean speakerModeratorInSession = sessionSpeakerService.isSpeakerModeratorInSession(session.getId(), user.getUserId());
            boolean isSpeaker = sessionSpeakerService.isUserSpeakerInSession(userId,session.getId());
            String userName = user.getFirstName() + " " + user.getLastName();

            if(isJoined){
                if(eventAdmin || speakerModeratorInSession){
                    commandCenterNotificationActivity(session.getId(), user.getPhoto(), user.getFirstName(), user.getLastName(), session.getEventId(), session.getTitle(), Boolean.TRUE, userId,"The moderator " + userName + " has joined the workshop session");
                }
                else if(isSpeaker) {
                    commandCenterNotificationActivity(session.getId(), user.getPhoto(), user.getFirstName(), user.getLastName(), session.getEventId(), session.getTitle(), Boolean.TRUE,userId, "The speaker " + userName + " has joined the workshop session");
                }
            }
            else {
                if(eventAdmin || speakerModeratorInSession){
                    commandCenterNotificationActivity(session.getId(), user.getPhoto(), user.getFirstName(), user.getLastName(), session.getEventId(), session.getTitle(), Boolean.FALSE, userId,"The moderator " + userName + " has left the workshop session");
                }
                else if(isSpeaker) {
                    commandCenterNotificationActivity(session.getId(), user.getPhoto(), user.getFirstName(), user.getLastName(), session.getEventId(), session.getTitle(), Boolean.FALSE,userId, "The speaker " + userName + " has left the workshop session");
                }
            }
        }

    }

    private Long getUserIdFromExternalUserId(String externalUserId){
        long userId = 0L;
        try{
            if(externalUserId.contains(Constants.STRING_UNDERSCORE)){
                userId =  Long.parseLong(externalUserId.substring(externalUserId.lastIndexOf(Constants.STRING_UNDERSCORE)+1));
            }
        }
        catch (Exception exception){
            logger.info("invalid External Id {}",externalUserId);
        }
        return userId;
    }

    private void commandCenterNotificationActivity(Long sessionId, String avatarUrl, String firstName, String lastName, Long eventId, String title, Boolean isOnline, Long userId, String action) {
        try {
            commandCenterGraphQLHandler.createCommandCenterNotification(eventId, sessionId, title, action, firstName, lastName, avatarUrl,isOnline, userId);
        } catch (Exception e) {
            logger.info("Exception WebHookController commandCenterNotificationActivity error {}", e.getMessage());
        }
    }
}
