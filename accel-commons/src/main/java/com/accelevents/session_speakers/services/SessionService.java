package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.*;
import com.accelevents.domain.Event;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.*;
import com.accelevents.session_speakers.dto.*;
import org.json.JSONException;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SessionService {

	Session save(Session session);

	void saveAll(List<Session> sessions);

    Session getSessionById(Long id, Event event);

	Long createSession(Event event, SessionDTO sessionDTO);

    EnumChannelStatus updateSession(Long id, SessionDTO sessionDTO, Event event, User user);

	DataTableResponse getSessionHostList(String expand,
                                         Event event,
                                         String searchStr, Pageable pageable, boolean isFromBillingPage,List<SessionTypeFormat> sessionTypeFormats, List<Date> searchDate,List<EnumSessionFormat> sessionTypes,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming);

    DataTableResponse getSessionHostListWithSorting(String expand,
                                         Event event,
                                         String searchStr, Pageable pageable, boolean isFromBillingPage,List<SessionTypeFormat> sessionTypeFormats,List<Date> searchDate,List<EnumSessionFormat> sessionTypes,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming);

	DataTableResponse getSessionList(String expand, Event event,
                                     User user, Pageable pageable,
                                     SessionFilter sessionFilter, boolean isAdminOrStaff, String calledFrom);

	SessionDTO getSessionInfoById(Long sessionId, Event event, User user, String expand);

	String getSessionFormatBySessionId(Long sessionId, Event event, User user);

	DataTableResponse getSessionInformationBySessionIds(List<Long> interestedSessionIds, Event event, String expand, User loggedInUser, String sessionFormat, boolean showPastAndUpcoming, boolean isAdminStaff, String filterDate);

	DataTableResponse getSpeakerTalks(Event event,
									  User user,
									  String search,
									  Pageable pageable,
									  Boolean pastEvents, String expand, boolean isAdminOrStaff);

	DataTableResponse getMyRegisteredSessions(Event event, String expand, User loggedInUser, String sessionFormat, boolean showPastAndUpcoming, boolean isAdminOrStaff, String filterDate, SessionFilter sessionFilter);

	Session updateDirectUploadIdAndFile(Long id, String streamUrl,StreamProvider streamProvider, Event event, String playBackRestrictionToken, String thumbnailRestrictionToken);

    MuxAssetDTO findDefaultPlayBackForSession(Long id, AssetType sessionAsset);

    List<MuxAssetDTO> getSessionPlayBacksForDisplaySide(Long sessionId, Event event);

    List<MuxAssetDTO> getSessionPlayBacks(Long sessionId, Event event);

    void setDefaultPlayBackForSession(Long id, Long playbackId, Event event);

	SessionDetailsAnalytics getSessionsAnalyticsById(Long sessionId,
													 String expand,
													 Event event);

	DataTableResponse getSessionsAnalytics(String search, String expand,
										   Event event,
										   Pageable pageable);

    SessionDashboardAnalyticsDto getSessionDashboardAnalytics(Event event, User loggedInUser);

    List<SessionAnalyticsDto> getSessionsOverviewReportData(Event event);

    void updateSessionTicketTypes(TicketingType ticketingType, List<Long> ticketingTypeIds, Event event, Boolean isNewTicket);

	void updateTicketTypesThatCanBeRegistered(long ticketingTypeId , Event event);

    UploadSessionResponseContainer saveOrUpdateParseSessionCSV(MultipartFile multiPartFile, Event event,User user, Map<String,String> languageMap) throws IOException;

    UploadSessionResponseContainer uploadSessionCSVOrZapier(UploadSessionDto[] sessionDtos, Event event, Boolean areSpeakersValidated,boolean isSessionCustomAttributeDataValidated, User user);

    void updateSessionThroughZapier(UploadSessionDto uploadSessionDto, Event event, Session session);

    MeetingAttendeeDto joinSession(Long sessionId, Event event, User user);

	void clearMeeting(Long sessionId, Event event);

    Session duplicateSession(Long sessionId, Event event, User loggedInUser);

    List<Session> findSessionByEventId(Event event);

    Set<String> getAllSessionDatesInEvent(Event event, Long timezoneOffset , boolean isAdminOrStaff);

    void removeRecordedSessionVideo(Long playbackId);

	void setAgendaAddedChecklistFlag(Event event);

	List<Session> findSessionByEventIdOrderByIdAsc(Long eventId);

	DataTableResponse getUserRegisteredSessions(Event event, User user, boolean isAdminOrStaff);

    List<SessionIdTitleDto> getSessionIdAndTitleById(List<Long> sessionId);

	void updateSessionDocumentBySpeaker(Event event,User user ,Long sessionId,List<KeyValueDto> keyValueDtos);

    DataTableResponseForSession getDisplayPortalSessionList(String expand, Event event,
                                                            User user, Pageable pageable,
                                                            SessionFilter sessionFilter, boolean isAdminOrStaff, Boolean showPastAndUpcoming, String calledFrom);

    List<String> findSessionTitleByEventIdOrderByIdAsc(Long eventId);

    DisplayPortalCommonSessionDTO getDisplayPortalSessionInfoById(Long sessionId, Event event, User user, String expand, boolean isAdminOrStaff);

    boolean isDraftSession(Session session);

    List<SessionIdTitleDto> getSessionDropDownList(Event event, List<EnumSessionFormat> sessionFormat);

    void checkNonVisualSessionCount(EnumSessionFormat format, Event event, EnumSessionFormat oldSessionFormat, boolean forUpdateSession);

    void updateSessionSequence(Long sessionId, Long topSessionId, Long topBottomSessionId, Event event);

    Session setPositionForSession(Session session);

    void sortSessionPositionByTitle(Long eventId, String sortType);

    void updateChimeConfigDetails(Long sessionId, Event event, User user, ChimeConfigDto chimeConfigDto);

    ChimeConfigDto getChimeConfigDetails(Long sessionId, Event event);

    SpeakerRegisterSessionCountDto speakerRegisterSessionCount(Event event, User user,EnumSessionFormat sessionFormat);

    ResponseDto sessionMassOperation(Event event, User user, EnumSessionMassOperation enumSessionMassOperation, List<Long> sessionIds, Map<String, String> languageMap, SessionMassOperationConfigDTO sessionMassOperationConfigDTO, HttpServletRequest httpRequest, String authToken) throws JSONException;

    List<Session> getAllSessionByIds(List<Long> sessionIds, Event event);

    Long createDuplicateSessionBySessionIds(Long sessionId, Event event, User loggedInUser, SessionMassOperationConfigDTO sessionMassOperationConfigDTO, HttpServletRequest httpRequest);

    void copySessionData(SessionMassOperationConfigDTO sessionMassOperationConfigDTO, Session oldSession, Session duplicatedSession, Event event, HttpServletRequest httpRequest);

    DataTableResponse getAllCommandCenterSessionList(String expand, Event event, User user, Pageable pageable, boolean isAdminOrStaff);

    void resetSessionDates(List<Long> listOfEventId, int days);

    Session getFirstConcurrentSession(Session session);

    PostSessionCallToActionDropDownDto getFutureSessionExpoLoungesByEvent(Event event);

    PostSessionCallToActionDropDownDto getFutureSessionExpoLoungesByEventAndSessionId(Event event,long sessionId);

    PostSessionCallToActionDto sessionOrLoungeOrExpoExistingOrUpcoming(Event event, User user, Long sessionId, boolean isAdminOrStaff);

    boolean isSpeakerIsValidAttendee(User user, Event event, Session session, List<Long> userTicketTypes);

    boolean isValidUser(User user, Event event, Session session);

    boolean isValidUserForSession(Session session,List<Long> userTicketTypes, boolean isSpeakerInSession, boolean isSpeakerInEvent);

    String createWorkshopRecordAssetPlaybackToken(Long assetId);

    DataTableResponse getCommandCenterSessionList(Event event, User user, Pageable pageable, SessionFilterCommandCenter sessionFilter, String expand);

    void startOrStopWorkshopRecording(Long sessionId, Event event, String recordStatus);

    DataTableResponse getSortedSessionWithDateAndTime(String expand,
                                                      Event event,
                                                      Pageable pageable, String sortField, String sortDirection);

    List<Session> findSessionsByTagOrTrackIds(List<Long> tagOrTrackIds);

    VonageSessionDto getSessionStreamDetailsById(Long sessionId, Event event, User user);

    List<Long> getUserRegisteredSessionsIdsList(Long eventId, Long userId);

    UserSessionAnalyticsDTO getSessionCountWithUserSessionRegistered(Event event);

    void updateSubTitleFileInSession(Session session, MUXLivestreamAssetDetails muxLivestreamAssetDetails, String captionFileName, String captionFullUrl, String languageCode);

    void removeSubTitleFileInSession(Long sessionId, Long subtitleId, Event event);

    public List<Long> getListOfIdsForTagAndTrackForSorting(String tagId, String trackId);

    List<Session> getAllSessionByEvent(Event event);

    List<SessionListDto> getAllSessionByEventId(Event event);

    MuxAssetDTO uploadSessionRecording(Long id, DirectUploadDto uploadDto, Event event, User user, String authToken) throws JSONException;

    ResponseDto updateSessionsInBulk(Event event, User user,SessionBulkUpdatesDto sessionBulkUpdatesDto) throws JSONException;

    DataTableResponse getUserRegisteredSessionsAndScheduleMeetings(Event event, User user, boolean showPastAndUpcoming, boolean isAdminOrStaff, String filterDate, SessionFilter sessionFilter, String expand, String equivalentTimeZone,boolean isCustomEvent);

    DataTableResponse getAllSessionsListByEvent(Event event);

    List<Session> findSessionByEventAndPrefixTitleLike(Event event, String title);

    List<Long> findSurveyIdBySessionIds(List<Long> sessionIds);

    RegisteredBookmarkedSessionDto getUserRegisteredBookmarkedSessions(Event event, User user, boolean isAdminOrStaff);

    HttpEntity<byte[]> calendarIcalForSession(String sessionName, String startDate, String endDate, Long templateId, String sessionAddress, Event event);

    DataTableResponseForSession getRedisDisplayPortalSessionList(Event event, User user, Pageable of, SessionFilter sessionFilter, boolean isAdminOrStaff, Boolean showPastAndUpcoming);


    List<PostSessionSurveyDto> getPostSessionsSurveysDetails(Event event, User user, boolean isAdminOfStaff);

    List<SessionCalendarViewDTO> getSessionCalendarViewList(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, String speakerIds, String locationIds, String date, String filter);

    DataTableResponse getUnscheduledSessionsOfEvent(Event event, String searchString, List<EnumSessionFormat> sessionTypeFormats, List<Long> listOfTagAndTrackIds, String speakerIds, String locationIds, int page, int size);

    void updateSessionWithDragAndDrop(Long id, SessionDragAndDropDto sessionDTO, Event event, User user);

    SessionRegistrationLimitDTO getSessionRegistrationLimits(User user, Event event, boolean isAdminOfStaff);

    DataTableResponse getWaitlistedUserDetailsBySessionId(Long sessionId, Event event, User user,PageSizeSearchObj pageSizeSearchObj, String search);

    void registerWaitlistedUserByUserSessionId(Event event, User user,Long userSessionId);

    void removeWaitlistedUserByUserSessionId(Event event, User user,Long userSessionId);

    void addAttendeesToWaitlistBySessionId(Event event, List<Long> userIds,Long sessionId);

    DataTableResponse getAllAttendeeListByEvent(Event event, PageSizeSearchObj pageSizeSearchObj, Long sessionId);

    BulkSessionStatusUpdateResultDto updateSessionsStatusInBulk(Event event, User user, List<Long> sessionIds, EnumSessionStatus status);
}
