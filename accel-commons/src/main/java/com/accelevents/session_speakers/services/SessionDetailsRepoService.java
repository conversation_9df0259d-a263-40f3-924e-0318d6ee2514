package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.session_speakers.dto.IdDurationDto;

import java.util.List;
import java.util.Optional;

public interface SessionDetailsRepoService
{
    SessionDetails save(SessionDetails sessionsDetails);

    Optional<SessionDetails> findBySessionId(Long sessionDetailsId);

    String findVonageSessionIdBySessionId(Long sessionId);

    List<IdDurationDto> getSessionDurationBySessionIds(List<Long> sessionIds);

    Double getSessionVideoDurationBySessionId(Long sessionId);

    void deleteBySessionId(Long sessionId);

    String findSubTitleFileUrlFromSessionDetailsBySession(Long sessionId);

    boolean getSessionRecordBySession(Long sessionId);

    boolean getHideVideoControlsBySession(Long sessionId);

    List<IdDurationDto> getSessionVideoDurationByEventId(Long eventId);

    List<SessionDetails> getAllSessionDetailsByEventId(Long eventId);

    List<SessionDetails> findSessionDetailsBySessionIds(List<Session> sessions);

    Integer getAllowedMinutesToJoinLate(Long sessionDetailsId);

    List<SessionDetails> getSessionDetailsForDisableLiveCaptionsDataFix(Long from, Long to);

    void saveAll(List<SessionDetails> sessionDetails);

    List<SessionDetails> getSessionDetailsByPostSessionCTASessionId(Long sessionId);

    List<SessionDetails> getAllSessionDetailsByEventIdWithoutCache(Long eventId);
}
