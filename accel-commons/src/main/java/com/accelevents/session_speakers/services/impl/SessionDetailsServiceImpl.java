package com.accelevents.session_speakers.services.impl;

import com.accelevents.common.dto.ChimeConfigDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.StreamProvider;
import com.accelevents.domain.enums.SubStreamProvider;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.services.VonageService;
import com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsUtils;
import com.accelevents.services.elasticsearch.videoanalytics.VimeoService;
import com.accelevents.services.elasticsearch.videoanalytics.YoutubeService;
import com.accelevents.session_speakers.dto.CaptionsDto;
import com.accelevents.session_speakers.dto.IdDurationDto;
import com.accelevents.session_speakers.dto.SessionDTO;
import com.accelevents.session_speakers.dto.SessionDetailsDto;
import com.accelevents.session_speakers.services.MUXLivestreamAssetService;
import com.accelevents.session_speakers.services.SessionDetailsRepoService;
import com.accelevents.session_speakers.services.SessionDetailsService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.JsonMapper;
import com.accelevents.utils.SessionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SessionDetailsServiceImpl implements SessionDetailsService {

    private static final Logger log = LoggerFactory.getLogger(SessionDetailsServiceImpl.class);

	@Autowired
	private SessionDetailsRepoService sessionDetailsRepoService;
    @Autowired
    private MUXLivestreamAssetService muxLivestreamAssetService;
    @Autowired
    private YoutubeService youtubeService;
    @Autowired
    private VimeoService vimeoService;
    @Autowired
    private VonageService vonageService;
    @Autowired
    private SessionRepoService sessionRepoService;


    public SessionDetails save(Session session, SessionDetailsDto sessionDetailsDto) {
      return save(session,sessionDetailsDto,true);
    }
    @Override
	public SessionDetails save(Session session, SessionDetailsDto sessionDetailsDto,boolean isCsvUpload) {
        Optional<SessionDetails> optionalSessionDetails =  (!isCsvUpload || session.getId() != 0) ?getSessionDetailsBySession(session) : Optional.empty() ;
        SessionDetails sessionDetails;
        if(optionalSessionDetails.isPresent()){
            sessionDetails = optionalSessionDetails.get();
            SessionDetailsDto.toEntity(sessionDetails, sessionDetailsDto);
        } else {
            sessionDetails = new SessionDetails();
            SessionDetailsDto.toEntity(sessionDetails, sessionDetailsDto);
            sessionDetails.setChimeConfigJson(JsonMapper.parseToJsonString(sessionDetailsDto.getChimeConfigJson()));
            sessionDetails.setSession(session);
        }
        if (SubStreamProvider.OPENTOK.equals(session.getSubStreamProvider())) {
            if (StringUtils.isEmpty(sessionDetails.getVonageSessionId())) {
                String vonageSession = vonageService.createVonageSession();
                sessionDetails.setVonageSessionId(vonageSession);
            }
        } else {
            sessionDetails.setVonageSessionId(null);
        }
        if (EnumSessionFormat.WORKSHOP.equals(session.getFormat())) {
            sessionDetails.setChimeConfigJson( JsonMapper.parseToJsonString(null != sessionDetailsDto.getChimeConfigJson() ?sessionDetailsDto.getChimeConfigJson() : new ChimeConfigDto()));
        }
        sessionDetails.setPostSessionCallToActionJson(CommonUtil.convertToGson(sessionDetailsDto.getPostSessionCallToActionJson()));
        sessionDetails.setCaptions(JsonMapper.parseToJsonString(sessionDetailsDto.getCaptions()));
        sessionDetails.setWaitingMedia(sessionDetailsDto.getWaitingMedia());
        sessionDetails.setSurveySessionStart(sessionDetailsDto.isSurveySessionStart());
        return sessionDetailsRepoService.save(sessionDetails);
	}

	@Override
	public Optional<SessionDetails> getSessionDetailsBySession(Session session){
		return sessionDetailsRepoService.findBySessionId(session.getId());
	}

    @Override
    public SessionDetails getSessionDetailsBySession1(Session session){
        return sessionDetailsRepoService.findBySessionId(session.getId()).orElse(null);
    }




    @Override
    public String findVonageSessionIdBySessionId(Long sessionId) {
        return sessionDetailsRepoService.findVonageSessionIdBySessionId(sessionId);
    }

    @Override
	public List<IdDurationDto> getSessionDurationBySessionIds(List<Long> sessionIds){
		if(CollectionUtils.isEmpty(sessionIds)){
			return Collections.emptyList();
		}
		return sessionDetailsRepoService.getSessionDurationBySessionIds(sessionIds);
	}

    @Override
    public Double getSessionVideoDurationBySessionId(Long sessionId) {
        return sessionDetailsRepoService.getSessionVideoDurationBySessionId(sessionId);
    }

    @Override
    public double getSessionVideoDurationBySession(Session session) {
        Double duration;
        if(session.getFormat().equals(EnumSessionFormat.WORKSHOP)){
            return  (double) (session.getEndTime().getTime() - session.getStartTime().getTime()) /1000;
        }
        if (SessionUtils.isAcceleventOrDirectUploadStreamProvider(session.getStreamProvider())) {
            duration = muxLivestreamAssetService.getSessionVideoDurationBySessionId(session.getId());
        } else {
            duration = getSessionVideoDurationBySessionId(session.getId());
        }
        return duration != null ? duration : 0;
    }

    @Override
    public void saveSessionDetails(Session session, StreamProvider streamProvider, String streamUrl, SessionDTO sessionDTO) {
        SessionDetailsDto sessionDetailsDto = new SessionDetailsDto();
        if(streamProvider != null && streamUrl != null){
            double duration = getDuration(streamProvider, streamUrl, sessionDTO);
            sessionDetailsDto.setVideoDuration(duration);
        } else if(session.getStreamProvider() == null || session.getStreamUrl() == null||session.getStreamUrl().isEmpty()){
            sessionDetailsDto.setVideoDuration(0);
        }
        else{
            Optional<SessionDetails> optionalSessionDetails =  getSessionDetailsBySession(session);
            if(optionalSessionDetails.isPresent()){
                SessionDetails sessionDetails = optionalSessionDetails.get();
                sessionDetailsDto.setVideoDuration(sessionDetails.getVideoDuration());
            }
        }
        if(sessionDTO != null) {
            if(null != sessionDTO.getChimeConfig()){
                sessionDetailsDto.setChimeConfigJson(sessionDTO.getChimeConfig());
            }
            if ((Arrays.asList(EnumSessionFormat.MAIN_STAGE, EnumSessionFormat.BREAKOUT_SESSION).contains(sessionDTO.getFormat())
                    && StreamProvider.ACCELEVENTS.equals(sessionDTO.getStreamProvider()))
                    || EnumSessionFormat.WORKSHOP.equals(sessionDTO.getFormat())) {
                sessionDetailsDto.setRecordSession(sessionDTO.isRecordSession());
            }
            if (sessionDTO.getStreamProvider() != null && sessionDTO.getStreamProvider().equals(StreamProvider.ZOOM_REDIRECT)) {
                sessionDetailsDto.setEnableSessionWaitingMedia(false);
            } else {
                sessionDetailsDto.setEnableSessionWaitingMedia(sessionDTO.isEnableSessionWaitingMedia());
            }
            sessionDetailsDto.setHideVideoControls(sessionDTO.getHideVideoControls());
            sessionDetailsDto.setAllowedMinutesToJoinLate(sessionDTO.getAllowedMinutesToJoinLate());
            sessionDetailsDto.setQnAPrivate(sessionDTO.isQnAPrivate());
            sessionDetailsDto.setEnableAttendeeList(sessionDTO.isEnableAttendeeList());
            sessionDetailsDto.setPostSessionCallToActionJson(sessionDTO.getPostSessionCallToActionJson());
            sessionDetailsDto.setCaptions(sessionDTO.getCaptions());
            sessionDetailsDto.setWaitingMedia(null != sessionDTO.getWaitingMedia()? JsonMapper.convertToString(sessionDTO.getWaitingMedia()) : null);
            sessionDetailsDto.setSelfCheckInAllowed(sessionDTO.isSelfCheckInAllowed());
            sessionDetailsDto.setSurveySessionStart(sessionDTO.isSurveySessionStart());
            sessionDetailsDto.setEnableVideoCaption(sessionDTO.isEnableVideoCaption());
        }
        save(session, sessionDetailsDto,false);
    }

    private double getDuration(StreamProvider streamProvider, String streamUrl, SessionDTO dto) {
        double duration = 0;
        switch (streamProvider) {
            case YOUTUBE:
                duration = youtubeService.getYoutubeVideoDuration(streamUrl);
                break;
            case VIMEO:
                duration = vimeoService.getVideoDuration(streamUrl);
                break;
            case VIDYARD:
            case WISTIA:
            case FACEBOOK:
                if (dto.getDuration() != null) {
                    duration = dto.getDuration();
                } else {
                    log.warn("Duration is not found in dto session {} streamUrl {}", dto.getSessionId(), streamUrl);
                    duration = 0;
                }
                break;
            default:
                duration = 0;
        }
        return duration;
    }

    @Override
    public void deleteBySessionId(Long sessionId) {
        sessionDetailsRepoService.deleteBySessionId(sessionId);
    }

    @Override
    public void save(SessionDetails sessionDetails) {
        sessionDetailsRepoService.save(sessionDetails);
    }

    @Override
    public String getSubTitleFileUrlFromSessionDetailsBySession(Session session) {
        return sessionDetailsRepoService.findSubTitleFileUrlFromSessionDetailsBySession(session.getId());
    }

    @Override
    public void updateChimeConfigDetails(Session session, Event event, User user, ChimeConfigDto chimeConfigDto) {
        Optional<SessionDetails> optionalSessionDetails = getSessionDetailsBySession(session);
        if (optionalSessionDetails.isPresent() && null != chimeConfigDto) {
            log.info("UPDATE Chime Config In Session | {} By User |{} ",session.getId(), user.getUserId());
            SessionDetails sessionDetails = optionalSessionDetails.get();
            sessionDetails.setChimeConfigJson(JsonMapper.parseToJsonString(chimeConfigDto));
            sessionDetailsRepoService.save(sessionDetails);
        }
    }

    @Override
    public ChimeConfigDto getChimeConfigDetails(Session session) {
        Optional<SessionDetails> optionalSessionDetails = getSessionDetailsBySession(session);
        if (optionalSessionDetails.isPresent()  ) {
            SessionDetails sessionDetailsObj = optionalSessionDetails.get();
            return org.apache.commons.lang3.StringUtils.isNotBlank(sessionDetailsObj.getChimeConfigJson())
                    ? ChimeConfigDto.convertJSONToObject(sessionDetailsObj.getChimeConfigJson()) : new ChimeConfigDto();
        }
        return null;
    }

    @Override
    public  Map<Long, Double> getSessionVideoDurationsByEvent(Event event) {
        log.info("Start get session video duration event {}", event.getEventId());
        List<Session> sessions = sessionRepoService.findSessionByEventId(event);
        log.info("Fetched sessions size {} ", sessions.size());

        List<Long> muxSessionIds = new ArrayList<>();
        List<Long> sessionIds = new ArrayList<>();
        Map<Long, Double> workshopDurations = new HashMap<>();

        if (!CollectionUtils.isEmpty(sessions)) {
            sessions.forEach(session -> {
                if (SessionUtils.isAcceleventOrDirectUploadStreamProvider(session.getStreamProvider())) {
                    muxSessionIds.add(session.getId());
                } else if (EnumSessionFormat.WORKSHOP.equals(session.getFormat())) {
                    // For Workshop sessions use session length as their duration.
                    workshopDurations.put(session.getId(), VideoAnalyticsUtils.getSessionDuration(session.getStartTime().getTime(), session.getEndTime().getTime()));
                } else {
                    sessionIds.add(session.getId());
                }
            });
        }
        List<IdDurationDto> durationList = new ArrayList<>();

        List<IdDurationDto> muxSessionDurations = muxLivestreamAssetService.getSessionDurationForAnalyticsByIdsIn(muxSessionIds);
        log.info("Fetched mux session durations size {}", muxSessionDurations.size());

        List<IdDurationDto> sessionDurations = getSessionDurationBySessionIds(sessionIds);
        log.info("Fetched session durations from session_details size {}", sessionDurations.size());

        durationList.addAll(sessionDurations);
        durationList.addAll(muxSessionDurations);

        Map<Long, Double> durationMap = durationList.stream().collect(Collectors.toMap(IdDurationDto::getId, IdDurationDto::getDuration));
        durationMap.putAll(workshopDurations);

        log.info("End get session video duration durationMap size {}, event {}", durationMap.size(), event.getEventId());
        return durationMap;
    }

    @Override
    public Integer getAllowedMinutesToJoinLate(Session session){
        return sessionDetailsRepoService.getAllowedMinutesToJoinLate(session.getId());
    }

    @Override
    public List<SessionDetails> getAllSessionDetailsByEventId(Long eventId) {
        return sessionDetailsRepoService.getAllSessionDetailsByEventId(eventId);
    }

    @Override
    public List<SessionDetails> getAllSessionDetailsByEventIdWithoutCache(Long eventId) {
        return sessionDetailsRepoService.getAllSessionDetailsByEventIdWithoutCache(eventId);
    }

    @Override
    public void updateSessionDetailOnMuxStreamKeyCreation(Session session, CaptionsDto captions) {
        Optional<SessionDetails> optionalSessionDetails = getSessionDetailsBySession(session);
        if (optionalSessionDetails.isPresent() && null != captions) {
            log.info("updateSessionDetailOnMuxStreamKeyCreation | {} ",session.getId());
            SessionDetails sessionDetails = optionalSessionDetails.get();
            sessionDetails.setCaptions(JsonMapper.parseToJsonString(captions));
            sessionDetailsRepoService.save(sessionDetails);
        }
    }
}
