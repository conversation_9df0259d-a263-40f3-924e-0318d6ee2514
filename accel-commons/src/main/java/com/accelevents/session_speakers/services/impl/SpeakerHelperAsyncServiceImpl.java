package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.EventDesignDetail;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.SessionTypeFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.EmailMessage;
import com.accelevents.dto.SessionSpeakerIdDto;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.messages.UtmTerm;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.utils.*;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.Constants.AutoLoginCreationLocation.SPEAKER_INVITE_EMAIL;
import static com.accelevents.utils.GeneralUtils.getEventPath;

@Service
public class SpeakerHelperAsyncServiceImpl {
    private static final Logger log = LoggerFactory.getLogger(SpeakerHelperAsyncServiceImpl.class);

    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private SendGridMailService sendGridMailService;
    @Autowired
    private ServiceHelper serviceHelper;
    @Autowired
    private SpeakerRepoService speakerRepoService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;
    @Autowired
    private EventDesignDetailService eventDesignDetailService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private VirtualEventService virtualEventService;
    @Autowired
    private EventService eventService;
    @Autowired
    private AutoLoginService autoLoginService;

    @Value("${apiBaseUrl}")
    private String apiBaseUrl;

    @Async
    public void sendInvite(Event event, User loggedInUser, Session session, Long speakerId) { //NOSONAR
        log.info("Sending invitation to the speaker with speakerId {} for sessionId {}", speakerId, session.getId());
        speakerRepoService.findSpeakerByIdAndEventOptional(event, speakerId)
                .ifPresent(e ->
                        roUserService.getUserById(e.getUserId())
                                .ifPresent(speakerUser -> {
                                    String utmContent = getUtmContentRoleWise(event, speakerUser);
                                    String portalLink = getPortalLinkMyTalks(event);
                                    String studioUrl = null;
                                    if ((EnumSessionFormat.MAIN_STAGE.equals(session.getFormat()) || EnumSessionFormat.BREAKOUT_SESSION.equals(session.getFormat())) && session.isAccelEventsStudio()) {
                                        String studioLink = getPortalLinkStudio(event);
                                        studioUrl = studioLink + "/" + session.getId();
                                    }

                                    if (EventFormat.IN_PERSON.equals(event.getEventFormat()) || SessionTypeFormat.IN_PERSON.equals(session.getSessionTypeFormat())) {
                                        if (null != studioUrl && studioUrl.contains("/portal")) {
                                            studioUrl = studioUrl.split(IN_PERSON_PORTAL_REPLACE)[0];
                                        }
                                    }

                                    String token = null;
                                    if (StringUtils.isBlank(speakerUser.getPassword())) {
                                        token = UUID.randomUUID().toString() + System.nanoTime();
                                        speakerUser.setPasswordResetToken(token);
                                        userService.save(speakerUser);
                                    }

                                    boolean isOnboarded = SpeakerOnBoardingStatus.ONBOARDED.equals(e.getSpeakerOnBoardingStatus());

                                    String customizeEmail = virtualEventService.getCustomizeSpeakerInviteEmailByEventId(event.getEventId());

                                    String desc = prepareDescription(session.getTitle(), event, loggedInUser.getUserId(), portalLink, studioUrl, token, speakerUser, speakerId, customizeEmail, speakerUser.getEmail(), utmContent, session.getSessionTypeFormat(), isOnboarded);
                                    String calDesc = prepareCalDescription(session.getTitle(), event, portalLink, studioUrl, token, speakerUser.getUserId(), speakerUser.getEmail());

                                    Set<String> receivers = new HashSet<>();
                                    receivers.add(speakerUser.getEmail());
                                    EmailMessage emailMessage = new EmailMessage(TemplateId.EVENTS_ACCOUNT_DUMMY);
                                    emailMessage.setTemplate(desc);

                                    String replyToEmail = null;
                                    if (event.getWhiteLabel() != null) {
                                        Optional<WhiteLabel> whiteLabelOptional = roWhiteLabelService.getWhiteLabelById(event.getWhiteLabel().getId());
                                        if (whiteLabelOptional.isPresent()) {
                                            WhiteLabel whiteLabel = whiteLabelOptional.get();
                                            replyToEmail = whiteLabel.getNotificationEmail();
                                            emailMessage.setWhiteLabel(whiteLabel);
                                        }
                                    }
                                    EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
                                    if (StringUtils.isEmpty(replyToEmail)) {
                                        replyToEmail = eventDesignDetail.getReplyToEmail();
                                    }
                                    emailMessage.setReplyToEmail(replyToEmail);
                                    emailMessage.setSenderNameFromEvent(eventDesignDetail.getEmailSenderName());
                                    emailMessage.setSenderMailFromEvent(eventDesignDetail.getNotificationEmail());
                                    Date sessionStartDate = TimeZoneUtil.getDateInLocal(session.getStartTime(), event.getEquivalentTimeZone());
                                    Date sessionEndDate = TimeZoneUtil.getDateInLocal(session.getEndTime(), event.getEquivalentTimeZone());
                                    StringBuilder singleItems = new StringBuilder();
                                    singleItems.append(session.getTitle());
                                    singleItems.append(" @ ");
                                    singleItems.append(StringTools.formatCalanderDate(sessionStartDate, true, true));
                                    singleItems.append(" from ");
                                    singleItems.append(StringTools.formatCalanderTime(sessionStartDate, false));
                                    singleItems.append(" to ");
                                    singleItems.append(StringTools.formatCalanderTime(sessionEndDate, false));
                                    singleItems.append(" (");
                                    singleItems.append(event.getTimezoneId());
                                    singleItems.append(")");

                                    emailMessage.setSubject(singleItems.toString());

                                    String startDate = String.format("%s", StringTools.formatDateBasedOnPattern(
                                            session.getStartTime(),
                                            "yyyyMMdd'T'HHmmss'Z'"));
                                    String endDate = String.format("%s", StringTools.formatDateBasedOnPattern(
                                            session.getEndTime(),
                                            "yyyyMMdd'T'HHmmss'Z'"));

                                    if (null != event.getOrganizer()) {
                                        emailMessage.addAttachments(EmailUtils.addToCalendarICSLink(event.getName(), startDate, endDate,
                                                calDesc, portalLink, event.getOrganizer().getName() != null ? event.getOrganizer().getName() : STRING_EMPTY, event.getOrganizer().getContactEmailAddress() != null ? event.getOrganizer().getContactEmailAddress() : STRING_EMPTY,null));
                                    } else {
                                        emailMessage.addAttachments(EmailUtils.addToCalendarICSLink(event.getName(), startDate, endDate,
                                                calDesc, portalLink, STRING_EMPTY, STRING_EMPTY,null));
                                    }
                                    Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();
                                    substitutionMap.put(Constants.EVENT_ID_UPPERCASE, event.getEventId());
                                    sendGridMailService.sendTemplateMail(emailMessage, receivers);

                                    if (!isOnboarded && !SpeakerOnBoardingStatus.INVITED.equals(e.getSpeakerOnBoardingStatus())) {
                                        e.setSpeakerOnBoardingStatus(SpeakerOnBoardingStatus.INVITED);
                                        speakerRepoService.saveWithoutAPIGWClearCache(e);
                                    }

                                })
                );
        log.info("Invitation sent successfully for speaker with speakerId {} and sessionId {}", speakerId, session.getId());
    } // nosonar

    @Transactional
    @Async
    public void sendInviteWithPastEmail(Event event, Long speakerId, String pastSpeakerEmail) {
        log.info("SpeakerHelperServiceImpl | sendInviteWithPastEmail eventId {}", event.getEventId());
        EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        speakerRepoService.findSpeakerByIdAndEventOptional(event, speakerId)
                .ifPresent(e ->
                        roUserService.getUserById(e.getUserId())
                                .ifPresent(speakerUser -> {
                                    String utmContent = getUtmContentRoleWise(event, speakerUser);
                                    String portalLink = getPortalLinkMyTalks(event);

                                    String token = null;
                                    if (StringUtils.isBlank(speakerUser.getPassword())) {
                                        token = UUID.randomUUID().toString() + System.nanoTime();
                                        speakerUser.setPasswordResetToken(token);
                                        userService.save(speakerUser);
                                    }

                                    String desc = prepareDescription(event, portalLink, token, speakerUser.getUserId(), speakerId, speakerUser.getEmail(), pastSpeakerEmail, utmContent);

                                    Set<String> receivers = new HashSet<>();
                                    receivers.add(speakerUser.getEmail());
                                    EmailMessage emailMessage = new EmailMessage(TemplateId.TRANSACTIONAL_DUMMY);
                                    emailMessage.setTemplate(desc);
                                    StringBuilder singleItems = new StringBuilder();
                                    singleItems.append("Updated Login Email For The Event");

                                    emailMessage.setSubject(singleItems.toString());
                                    emailMessage.setReplyToEmail(eventDesignDetail.getReplyToEmail());
                                    emailMessage.setSenderNameFromEvent(eventDesignDetail.getEmailSenderName());
                                    emailMessage.setSenderMailFromEvent(eventDesignDetail.getNotificationEmail());
                                    emailMessage.setWhiteLabel(event.getWhiteLabel());
                                    sendGridMailService.sendTemplateMail(emailMessage, receivers);

                                })
                );
    }

    private String prepareDescription(String sessionName, Event event, Long magicLinkCreatorUserId, String portalLink, String studioLink, String token, User speakerUser, Long speakerId, String customizeEmail, String speakerUserEmail, String utmContent, SessionTypeFormat sessionTypeFormat, boolean isOnboarded) { //NOSONAR

        String studio = StringUtils.isNotBlank(studioLink) ? "<div sle=\"margin-top:20px;\"> You can join the talk directly by visiting: </div><a href=\"" + studioLink + getResetTokenForNewSpeaker(event,token, speakerUser.getUserId()) + AMPERSAND + EmailUtils.getDynamicUtmUrl(utmContent, UtmTerm.Speaker_Invite) + "\" target=\"_blank\">" + studioLink + "</a>" : ""; //NOSONAR
        StringBuilder addCustomizeEmail = new StringBuilder();
        if (customizeEmail != null && !customizeEmail.isEmpty()) {
            addCustomizeEmail.append("<head><style type=\"text/css\">")
                    .append(".fr-clearfix::after{clear:both;display:block;content:\"\";height:0}.fr-hide-by-clipping{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}.fr-view img.fr-rounded,.fr-view .fr-img-caption.fr-rounded img{border-radius:10px;-moz-border-radius:10px;-webkit-border-radius:10px;-moz-background-clip:padding;-webkit-background-clip:padding-box;background-clip:padding-box}.fr-view img.fr-shadow,.fr-view .fr-img-caption.fr-shadow img{-webkit-box-shadow:10px 10px 5px 0px #cccccc;-moz-box-shadow:10px 10px 5px 0px #cccccc;box-shadow:10px 10px 5px 0px #cccccc}.fr-view img.fr-bordered,.fr-view .fr-img-caption.fr-bordered img{border:solid 5px #CCC}.fr-view img.fr-bordered{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}.fr-view .fr-img-caption.fr-bordered img{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.fr-view{word-wrap:break-word}.fr-view span[style~=\"color:\"] a{color:inherit}.fr-view strong{font-weight:700}.fr-view table[border='0'] td:not([class]),.fr-view table[border='0'] th:not([class]),.fr-view table[border='0'] td[class=\"\"],.fr-view table[border='0'] th[class=\"\"]{border-width:0px}.fr-view table{border:none;border-collapse:collapse;empty-cells:show;max-width:100%}.fr-view table td{min-width:5px}.fr-view table.fr-dashed-borders td,.fr-view table.fr-dashed-borders th{border-style:dashed}.fr-view table.fr-alternate-rows tbody tr:nth-child(2n){background:whitesmoke}.fr-view table td,.fr-view table th{border:1px solid #DDD}.fr-view table td:empty,.fr-view table th:empty{height:20px}.fr-view table td.fr-highlighted,.fr-view table th.fr-highlighted{border:1px double red}.fr-view table td.fr-thick,.fr-view table th.fr-thick{border-width:2px}.fr-view table th{background:#ececec}.fr-view hr{clear:both;user-select:none;-o-user-select:none;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;break-after:always;page-break-after:always}.fr-view .fr-file{position:relative}.fr-view .fr-file::after{position:relative;content:\"\\1F4CE\";font-weight:normal}.fr-view pre{white-space:pre-wrap;word-wrap:break-word;overflow:visible}.fr-view[dir=\"rtl\"] blockquote{border-left:none;border-right:solid 2px #5E35B1;margin-right:0;padding-right:5px;padding-left:0}.fr-view[dir=\"rtl\"] blockquote blockquote{border-color:#00BCD4}.fr-view[dir=\"rtl\"] blockquote blockquote blockquote{border-color:#43A047}.fr-view blockquote{border-left:solid 2px #5E35B1;margin-left:0;padding-left:5px;color:#5E35B1}.fr-view blockquote blockquote{border-color:#00BCD4;color:#00BCD4}.fr-view blockquote blockquote blockquote{border-color:#43A047;color:#43A047}.fr-view span.fr-emoticon{font-weight:normal;font-family:\"Apple Color Emoji\",\"Segoe UI Emoji\",\"NotoColorEmoji\",\"Segoe UI Symbol\",\"Android Emoji\",\"EmojiSymbols\";display:inline;line-height:0}.fr-view span.fr-emoticon.fr-emoticon-img{background-repeat:no-repeat !important;font-size:inherit;height:1em;width:1em;min-height:20px;min-width:20px;display:inline-block;margin:-.1em .1em .1em;line-height:1;vertical-align:middle}.fr-view .fr-text-gray{color:#AAA !important}.fr-view .fr-text-bordered{border-top:solid 1px #222;border-bottom:solid 1px #222;padding:10px 0}.fr-view .fr-text-spaced{letter-spacing:1px}.fr-view .fr-text-uppercase{text-transform:uppercase}.fr-view .fr-class-highlighted{background-color:#ffff00}.fr-view .fr-class-code{border-color:#cccccc;border-radius:2px;-moz-border-radius:2px;-webkit-border-radius:2px;-moz-background-clip:padding;-webkit-background-clip:padding-box;background-clip:padding-box;background:#f5f5f5;padding:10px;font-family:\"Courier New\", Courier, monospace}.fr-view .fr-class-transparency{opacity:0.5}.fr-view img{position:relative;max-width:100%}.fr-view img.fr-dib{margin:5px auto;display:block;float:none;vertical-align:top}.fr-view img.fr-dib.fr-fil{margin-left:0;text-align:left}.fr-view img.fr-dib.fr-fir{margin-right:0;text-align:right}.fr-view img.fr-dii{display:inline-block;float:none;vertical-align:bottom;margin-left:5px;margin-right:5px;max-width:calc(100% - (2 * 5px))}.fr-view img.fr-dii.fr-fil{float:left;margin:5px 5px 5px 0;max-width:calc(100% - 5px)}.fr-view img.fr-dii.fr-fir{float:right;margin:5px 0 5px 5px;max-width:calc(100% - 5px)}.fr-view span.fr-img-caption{position:relative;max-width:100%}.fr-view span.fr-img-caption.fr-dib{margin:5px auto;display:block;float:none;vertical-align:top}.fr-view span.fr-img-caption.fr-dib.fr-fil{margin-left:0;text-align:left}.fr-view span.fr-img-caption.fr-dib.fr-fir{margin-right:0;text-align:right}.fr-view span.fr-img-caption.fr-dii{display:inline-block;float:none;vertical-align:bottom;margin-left:5px;margin-right:5px;max-width:calc(100% - (2 * 5px))}.fr-view span.fr-img-caption.fr-dii.fr-fil{float:left;margin:5px 5px 5px 0;max-width:calc(100% - 5px)}.fr-view span.fr-img-caption.fr-dii.fr-fir{float:right;margin:5px 0 5px 5px;max-width:calc(100% - 5px)}.fr-view .fr-video{text-align:center;position:relative}.fr-view .fr-video.fr-rv{padding-bottom:56.25%;padding-top:30px;height:0;overflow:hidden}.fr-view .fr-video.fr-rv>iframe,.fr-view .fr-video.fr-rv object,.fr-view .fr-video.fr-rv embed{position:absolute !important;top:0;left:0;width:100%;height:100%}.fr-view .fr-video>*{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;max-width:100%;border:none}.fr-view .fr-video.fr-dvb{display:block;clear:both}.fr-view .fr-video.fr-dvb.fr-fvl{text-align:left}.fr-view .fr-video.fr-dvb.fr-fvr{text-align:right}.fr-view .fr-video.fr-dvi{display:inline-block}.fr-view .fr-video.fr-dvi.fr-fvl{float:left}.fr-view .fr-video.fr-dvi.fr-fvr{float:right}.fr-view a.fr-strong{font-weight:700}.fr-view a.fr-green{color:green}.fr-view .fr-img-caption{text-align:center}.fr-view .fr-img-caption .fr-img-wrap{padding:0;margin:auto;text-align:center;width:100%}.fr-view .fr-img-caption .fr-img-wrap a{display:block}.fr-view .fr-img-caption .fr-img-wrap img{display:block;margin:auto;width:100%}.fr-view .fr-img-caption .fr-img-wrap>span{margin:auto;display:block;padding:5px 5px 10px;font-size:14px;font-weight:initial;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-opacity:0.9;-moz-opacity:0.9;opacity:0.9;-ms-filter:\"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)\";width:100%;text-align:center}.fr-view button.fr-rounded,.fr-view input.fr-rounded,.fr-view textarea.fr-rounded{border-radius:10px;-moz-border-radius:10px;-webkit-border-radius:10px;-moz-background-clip:padding;-webkit-background-clip:padding-box;background-clip:padding-box}.fr-view button.fr-large,.fr-view input.fr-large,.fr-view textarea.fr-large{font-size:24px}a.fr-view.fr-strong{font-weight:700}a.fr-view.fr-green{color:green}img.fr-view{position:relative;max-width:100%}img.fr-view.fr-dib{margin:5px auto;display:block;float:none;vertical-align:top}img.fr-view.fr-dib.fr-fil{margin-left:0;text-align:left}img.fr-view.fr-dib.fr-fir{margin-right:0;text-align:right}img.fr-view.fr-dii{display:inline-block;float:none;vertical-align:bottom;margin-left:5px;margin-right:5px;max-width:calc(100% - (2 * 5px))}img.fr-view.fr-dii.fr-fil{float:left;margin:5px 5px 5px 0;max-width:calc(100% - 5px)}img.fr-view.fr-dii.fr-fir{float:right;margin:5px 0 5px 5px;max-width:calc(100% - 5px)}span.fr-img-caption.fr-view{position:relative;max-width:100%}span.fr-img-caption.fr-view.fr-dib{margin:5px auto;display:block;float:none;vertical-align:top}span.fr-img-caption.fr-view.fr-dib.fr-fil{margin-left:0;text-align:left}span.fr-img-caption.fr-view.fr-dib.fr-fir{margin-right:0;text-align:right}span.fr-img-caption.fr-view.fr-dii{display:inline-block;float:none;vertical-align:bottom;margin-left:5px;margin-right:5px;max-width:calc(100% - (2 * 5px))}span.fr-img-caption.fr-view.fr-dii.fr-fil{float:left;margin:5px 5px 5px 0;max-width:calc(100% - 5px)}span.fr-img-caption.fr-view.fr-dii.fr-fir{float:right;margin:5px 0 5px 5px;max-width:calc(100% - 5px)}")
                    .append("</style></head>");
        }
        addCustomizeEmail.append("<div style=\"margin-top:20px;\"><b> On the day of the event, sign in with the email address ").append(speakerUserEmail).append("</b> .</div>");
        addCustomizeEmail.append("<div style=\"margin-top:20px;\">  You are being invited to be a speaker for the session ").append(sessionName).append(" for the event ").append(event.getName()).append(".</div>");
        if (StringUtils.isNotBlank(customizeEmail)) {
            addCustomizeEmail = addCustomizeEmail.append("<div class=\"fr-view\">").append("<div style=\"margin-top:20px;\"> ").append(customizeEmail).append("</div>").append("</div>");
        }
        addCustomizeEmail.append(" <div style=\"margin-top:20px;\">You can access a list of your talks here: </div><a href=\"").append(portalLink).append(autoLoginService.getOrCreateEventLevelMagicLinkUrl(speakerUser,event, magicLinkCreatorUserId, SPEAKER_INVITE_EMAIL)).append("\" target=\"_blank\">").append(portalLink).append("</a>").append(studio);  //NOSONAR
        if (isOnboarded) {
            return addCustomizeEmail.append(" <div style=\"margin-top:20px;\">Thank you for completing the Speaker Onboarding process. See the details above for information about this session.</div>").toString();
        }
        return addCustomizeEmail.append(" <div style=\"margin-top:20px;\">You can complete the speaker onboarding directly by visiting: </div><a href=\"").append(getSpeakerOnBoardingLink(event, speakerId)).append(getResetTokenForNewSpeaker(event,token, speakerUser.getUserId())).append(AMPERSAND).append(EmailUtils.getDynamicUtmUrl(utmContent, UtmTerm.Speaker_Invite)).append("\" target=\"_blank\">").append(getSpeakerOnBoardingLink(event, speakerId)).append("</a>").toString();  //NOSONAR
    } // nosonar

    private String prepareDescription(Event event, String portalLink, String token, Long speakerUserId, Long speakerId, String speakerUserEmail, String pastSpeakerEmail, String utmContent) {
        log.info("SpeakerHelperServiceImpl | prepareDescription eventId {}", event.getEventId());
        StringBuilder addCustomizeEmail = new StringBuilder();
        addCustomizeEmail.append("<div style=\"margin-top:20px;\"> Your login email for the event <b>").append(event.getName()).append("</b> has been changed from <b>").append(pastSpeakerEmail).append("</b> to <b>").append(speakerUserEmail).append("</b> . </div>");
        addCustomizeEmail.append("<div style=\"margin-top:20px;\"><b> On the day of the event, sign in with the email address ").append(speakerUserEmail).append("</b> .</div>");
        if (EventFormat.IN_PERSON.equals(event.getEventFormat())) {
            return addCustomizeEmail.toString();
        } else {
            addCustomizeEmail.append(" <div style=\"margin-top:20px;\">You can access a list of your talks here: </div><a href=\"").append(portalLink).append(getResetTokenForNewSpeaker(event,token, speakerUserId)).append(AMPERSAND).append(EmailUtils.getDynamicUtmUrl(utmContent, UtmTerm.Speaker_Invite)).append("\" target=\"_blank\">").append(portalLink).append("</a>");  //NOSONAR
            return addCustomizeEmail.append(" <div style=\"margin-top:20px;\">You can complete the speaker onboarding directly by visiting: </div><a href=\"").append(getSpeakerOnBoardingLink(event, speakerId)).append(getResetTokenForNewSpeaker(event,token, speakerUserId)).append(AMPERSAND).append(EmailUtils.getDynamicUtmUrl(utmContent, UtmTerm.Speaker_Invite)).append("\" target=\"_blank\">").append(getSpeakerOnBoardingLink(event, speakerId)).append("</a>").toString();  //NOSONAR
        }
    }

    /**
     * FallFormal is inviting you to be a speaker at Finance and Leadership For Students during FallFormal. You can access a list of your talks here: {{portalLinkWithToken}}.
     * You can join the talk directly by visiting: {{studioLinkWithToken}}.
     */
    private String prepareCalDescription(String sessionName, Event event, String portalLink, String studioLink, String token, Long speakerUserId, String speakerUserEmail) {

        String resetTokenForNewSpeaker = getResetTokenForNewSpeaker(event,token, speakerUserId);

        String portalLinkDesc;
        String studioLinkDesc;

        String portalLinkWithToken = STRING_EMPTY;
        String studioLinkWithToken = null;

        // For In_Person event we do not provided portal page link
        if (EventFormat.IN_PERSON.equals(event.getEventFormat())) {
            portalLinkDesc = LANDING_PAGE_LINK_FOR_ADD_TO_CALENDER;
            studioLinkDesc = STUDIO_LINK_FOR_IN_PERSON_FOR_ADD_TO_CALENDER;
            if (StringUtils.isNotBlank(portalLink)) {
                portalLinkWithToken = portalLink;
            }
            if (StringUtils.isNotBlank(studioLink)) {
                studioLinkWithToken = studioLink;
            }

        } else {
            portalLinkDesc = PORTAL_LINK_FOR_ADD_TO_CALENDER;
            studioLinkDesc = STUDIO_LINK_FOR_ADD_TO_CALENDER;
            if (StringUtils.isNotBlank(portalLink)) {
                portalLinkWithToken = portalLink.concat(resetTokenForNewSpeaker);
            }
            if (StringUtils.isNotBlank(studioLink)) {
                studioLinkWithToken = studioLink.concat(resetTokenForNewSpeaker);
            }
        }

        portalLinkDesc = portalLinkDesc.replace("{{eventName}}", event.getName()).replace("{{sessionName}}", sessionName)
                .replace("{{eventName}}", event.getName()).replace("{{portalLinkWithToken}}", portalLinkWithToken).replace("{{speakerUserEmail}}", speakerUserEmail);

        StringBuilder builder = new StringBuilder();
        builder.append(portalLinkDesc);

        if (StringUtils.isNotBlank(studioLink)) {
            studioLinkDesc = studioLinkDesc.replace("{{studioLinkWithToken}}", studioLinkWithToken);
            builder.append(studioLinkDesc);
        }
        return builder.toString();
    }

    public String getPortalLinkMyTalks(Event event) {
        return serviceHelper.getEventBaseUrl(event) + getEventPath() + event.getEventURL() + "/portal/mytalks";
    }

    public String getSpeakerOnBoardingLink(Event event, Long speakerId) {
        return serviceHelper.getEventBaseUrl(event) + getEventPath() + event.getEventURL() + "/portal/speaker-onboarding/" + speakerId;
    }

    public String getPortalLinkStudio(Event event) {
        return serviceHelper.getEventBaseUrl(event) + getEventPath() + event.getEventURL() + "/portal/studio";
    }

    private String getResetTokenForNewSpeaker(Event event, String token, Long speakerUserId) {
        if (StringUtils.isNotBlank(token)) {
            return "?token=" + token;
        } else {
            if (eventService.isSaleForceRequiredForLoginByEventUrl(event.getEventURL())) {
                return "?";
            } else {
                return "?userKey=" + SecurityUtils.encodeUserid(speakerUserId);
            }
        }
    }

    public String getUtmContentRoleWise(Event event, User user) {
        if (roStaffService.isEventAdmin(event, user)) {
            return IS_ADMIN;
        } else if (roStaffService.isEventStaff(event, user)) {
            return IS_STAFF;
        }
        return STRING_EMPTY;
    }

    @Async
    public void sendInviteToAllSpeakers(List<SessionSpeakerIdDto> sessionSpeakerIdDtoList, String eventUrl, String authToken) {
        if(CollectionUtils.isNotEmpty(sessionSpeakerIdDtoList)){
            int count = 0;
            for (SessionSpeakerIdDto sessionSpeakerIdDto : sessionSpeakerIdDtoList) {
                count++;
                if (count % 3 == 0) {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        log.error("Error occurred during sleep in sendInviteToAllSpeakers: {}", e.getMessage());
                    }
                }

                try {
                    log.info("ServiceHelper | sendInviteToAllSpeakers | sessionId {} and speakerId {}",sessionSpeakerIdDto.getSessionId(), sessionSpeakerIdDto.getSpeakerId());

                    Unirest.post(apiBaseUrl + "/rest/host/event/" + eventUrl + "/speaker/" + sessionSpeakerIdDto.getSpeakerId() + "/session/" + sessionSpeakerIdDto.getSessionId() + "/sendInvite")
                            .header("Authorization", authToken)
                            .header("User-Agent", "Accelevents API")
                            .asJson();
                } catch (UnirestException e) {
                    log.error("Error occurred during sending session speaker invite email for sessionId {} and speakerId {}: {}", sessionSpeakerIdDto.getSessionId(), sessionSpeakerIdDto.getSpeakerId(), e.getMessage());
                }
            }
            log.info("ServiceHelper | sendInviteToAllSpeakers | completed for eventUrl {}", eventUrl);
        }
    }
}