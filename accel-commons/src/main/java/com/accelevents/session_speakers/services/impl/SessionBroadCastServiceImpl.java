package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionStartStatus;
import com.accelevents.domain.enums.StreamProvider;
import com.accelevents.domain.enums.SubStreamProvider;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.StartBroadcast;
import com.accelevents.dto.StopBroadcast;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.services.*;
import com.accelevents.session_speakers.services.SessionBroadCastService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.SessionUtils;
import com.amazonaws.services.chimesdkmeetings.model.Meeting;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.jsoup.helper.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

import static com.accelevents.domain.enums.EnumSessionStartStatus.STARTED;
import static com.accelevents.domain.enums.EnumSessionStartStatus.STOPPED;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class SessionBroadCastServiceImpl implements SessionBroadCastService {

	private static final Logger logger = LoggerFactory.getLogger(SessionBroadCastServiceImpl.class);
	@Autowired private VoxeetService voxeetService;
	@Autowired private MuxService muxService;
	@Autowired private SessionService sessionService;
	@Autowired private CacheStoreService cacheStoreService;
	@Autowired private LambdaService lambdaService;
	@Autowired private GraphQLConfiguration graphQLConfiguration;
	@Autowired private VonageService vonageService;
    @Autowired
    private SessionRepoService sessionRepoService;

	private static final String MEETING_PAGE_URL = "/e/digi/portal/viewer/";
	@Value("${uiBaseurl}")
	private String uiBaseurl;


	@Override
	public void stopBroadCast(Long sessionId, String confId, Event event, User user){
		Session session = sessionRepoService.getSessionByIdWithoutCache(sessionId, event);
        logger.info("SessionBroadCastServiceImpl stopBroadCast sessionId {} confId {} eventId {}", sessionId, confId, event.getEventId());
        if ((null == session.getSessionStartStatus() || session.getSessionStartStatus().equals(EnumSessionStartStatus.NULL)) || !session.getSessionStartStatus().equals(STOPPED)) {
            if (SubStreamProvider.CHIME.equals(session.getSubStreamProvider())) {
                if (StringUtil.isBlank(session.getTaskId())) {
                    logger.info("stopBroadCast session {} Chime stop broadcast failed no task id found", sessionId);
                } else {
                    Object stopLamdaBroadcastResult = lambdaService.stopBroadCast(getStopBroadcast(session));
                    Gson gson = new Gson();
                    JsonElement jsonElement = new JsonParser().parse(gson.toJson(stopLamdaBroadcastResult));
                    logger.info("stopBroadCast session {} lamda result {} ", sessionId, jsonElement);
                    stopMuxBroadcast(session);
                }
            } else if (SubStreamProvider.OPENTOK.equals(session.getSubStreamProvider())) {
                String broadcastId = vonageService.stopConference(session.getTaskId(), sessionId);
                if (StringUtils.isNotBlank(broadcastId)) {
                    stop(user, session);
                }
                logger.info("stopBroadCast session {} OpenTok stop broadcast {}", sessionId, broadcastId);
            } else if (SubStreamProvider.AGORA.equals(session.getSubStreamProvider())) {
                stop(user, session);
                logger.info("Agora Stream stopped  {}", sessionId);
            } else {
                boolean stopped = voxeetService.stopConference(confId, session, user);
                if (stopped) {
                    logger.info("Voxeet Stream stopped  {}", sessionId);
                    stop(user, session);
                    logger.info("Stop conf set session to stopped  {}", sessionId);
                } else {
                    logger.info("Failed to stop voxeet stream {}", sessionId);
                    Map<String, String> map = null;
                    try {
                        logger.info("Query appsync {}", sessionId);
                        map = new BroadcastGraphQLHandler(graphQLConfiguration, Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN).getSessionStatusLogs(session);
                    } catch (JSONException e) {
                        logger.info("Error Query appsync {}", sessionId);
                    }
                    if (map != null
                            && StringUtils.isNotBlank(map.get(Constants.SESSION_ID))
                            && !"STOPPING".equals(map.get("status"))) {
                        if ("video.live_stream.idle".equals(map.get("status"))) {
                            logger.info("Stopping mux stream for session {}", sessionId);
                            stop(user, session);
                        } else {
                            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.STREAM_DISRUPT);
                        }
                    }
                }
            }
            commandCenterNotificationActivity(user, session, "Live broadcast ended",Boolean.TRUE, user.getUserId());
            logActivity(user, session, "STOP_BROADCAST");
            logger.info("SessionBroadCastServiceImpl stopBroadCast sessionId {} userId {} date {} success", sessionId, user.getUserId(), new Date());
        } else {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.BROADCAST_ALREADY_STOPPED);
        }
    }

    private void commandCenterNotificationActivity(User user, Session session, String message,Boolean isOnline, Long userId) {
        try {
            new CommandCenterGraphQLHandler(graphQLConfiguration, Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN).createCommandCenterNotification(session.getEventId(), session.getId(), session.getTitle(), message, user.getFirstName(), user.getLastName(), user.getPhoto(), isOnline, userId);
        } catch (Exception e) {
            logger.info("SessionBroadCastServiceImpl commandCenterNotificationActivity error {}", e.getMessage());
        }
    }

    private void logActivity(User user, Session session, String message) {
	    try{
            new ActivityLogAppsyncHandler(graphQLConfiguration, Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN).createActivityLog(session, message, user);
        } catch (Exception e){
            logger.info("SessionBroadCastServiceImpl logActivity error {}", e.getMessage());
        }

    }

    private void stop(User user, Session session) {
        stopMuxBroadcast(session);

        new SimpleAsyncTaskExecutor().execute(() -> {
            new BroadcastGraphQLHandler(graphQLConfiguration, Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN)
                    .handleCreateOrUpdateSessionStatusLogs("STOPPING", session, user);
        });
    }

    public long getPendingDuration(Long lastUpdatedAt) {
        long diff =  System.currentTimeMillis() - (lastUpdatedAt*1000) ;
        if (diff < (20*1000)){
            return diff/1000;
        } else {
            return -1;
        }
    }
	private void stopMuxBroadcast(Session session) {
		muxService.markDisableLiveStream(session.getLiveStreamId());
		session.setSessionStartStatus(EnumSessionStartStatus.STOPPED);
		session.setTaskId(null);
		sessionService.save(session);
	}

	private StopBroadcast getStopBroadcast(Session session) {
		StopBroadcast stopBroadcast = new StopBroadcast();
		stopBroadcast.setAction("stop");
		stopBroadcast.setTaskId(session.getTaskId());
		return stopBroadcast;
	}


	@Override
	public void startBroadCast(Long sessionId, String confId, Event event, User user, String authToken) {
		Session session = sessionService.getSessionById(sessionId, event);
		validateSessionEnded(session);
        validateAccelEventStreamProvider(session);

        logger.info("make session re enable before start if it is disable state.");
        if ((null == session.getSessionStartStatus() || session.getSessionStartStatus().equals(EnumSessionStartStatus.NULL)) || !session.getSessionStartStatus().equals(STARTED)) {
            commandCenterNotificationActivity(user, session, "Live broadcast started",Boolean.TRUE, user.getUserId());
            muxService.makeReEnabledLiveStream(session.getLiveStreamId());
            logger.info("startBroadCast|sessionId={}||confId ={}||event ={}||StreamProvider={}||isNotBlank(session.getStreamKey())={}|", sessionId, confId, event.getEventId(), session.getStreamProvider(), isNotBlank(session.getStreamKey()));
            if (StreamProvider.ACCELEVENTS.equals(session.getStreamProvider()) &&
                    isNotBlank(session.getStreamKey())) {
                if (SubStreamProvider.CHIME.equals(session.getSubStreamProvider())) {
                    Meeting meeting = SessionUtils.getMeeting(session.getMeetingObj());
                    logger.info("meeting id {} fetched for session {} ", meeting.getMeetingId(), sessionId);
                    String startResult = lambdaService.startBroadCast(getStartBroadcast(session, meeting));
                    logger.info("broadcastLambdaService result {} fetched for session {} ", startResult, sessionId);
                    session.setTaskId(getTaskIdFromLamdaResult(startResult));
                    session.setSessionStartStatus(STARTED);
                    sessionService.save(session);
                    logger.info("Chime Session is set to started with sessionId {}", sessionId);
                } else if (SubStreamProvider.OPENTOK.equals(session.getSubStreamProvider())) {
                    String broadcastId = vonageService.startBroadCast(session.getId(), session.getStreamKey(), session.getRtmpUrl(),event, user);
                    if (StringUtils.isNotBlank(broadcastId)) {
                        session.setTaskId(broadcastId);
                        session.setSessionStartStatus(STARTED);
                        sessionService.save(session);
                        vonageService.sentSignal(sessionId, Constants.UPDATE_STREAM, "", null);
                        new SimpleAsyncTaskExecutor().execute(() -> {
                            new BroadcastGraphQLHandler(graphQLConfiguration, authToken)
                                    .handleCreateOrUpdateSessionStatusLogs("STARTING", session, user);
                        });
                        logger.info("OpenTok Session is set to started with sessionId {}", sessionId);
                    }
                } else {
                    logger.info("Stream details present for session {}", sessionId);

                    new SimpleAsyncTaskExecutor().execute(() -> {
                        new BroadcastGraphQLHandler(graphQLConfiguration, authToken)
                                .handleCreateOrUpdateSessionStatusLogs("STARTING", session, user);
                    });

                    voxeetService.startConferenceWithRtmpUrl(
                            getRtmpEndpoint(session),
                            confId, session, user);
                    session.setSessionStartStatus(STARTED);
                    sessionService.save(session);

                    logger.info("Session is set to started with sessionId {}", sessionId);

                }
                logActivity(user, session, "START_BROADCAST");
                logger.info("SessionBroadCastServiceImpl startBroadCast sessionId {} userId {} date {}", sessionId, user.getUserId(), new Date());
            }
        } else {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.BROADCAST_ALREADY_STARTED);
        }
    }

    private void validateAccelEventStreamProvider(Session session) {
        if(!(StreamProvider.ACCELEVENTS.equals(session.getStreamProvider()) && session.isAccelEventsStudio())) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.STREAM_PROVIDER_CHANGED_FOR_SESSION);
        }
    }

    private StartBroadcast getStartBroadcast(Session session, Meeting meeting) {
		StartBroadcast startBroadcast = new StartBroadcast();
		startBroadcast.setAction("start");
		startBroadcast.setMeetingURL(uiBaseurl + MEETING_PAGE_URL + meeting.getMeetingId());
		startBroadcast.setRtmpEndpoint(getRtmpEndpoint(session));
		return startBroadcast;
	}

	private String getRtmpEndpoint(Session session) {
		return session.getRtmpUrl() +"/"+session.getStreamKey();
	}


	private String getTaskIdFromLamdaResult(String startResult) {
		String[] tokens = startResult.split("/");
		if(tokens.length == 3) {
			return tokens[2].replace("\"","");
		}
		return null;
	}

	private void validateSessionEnded(Session session) {
		if (session.getEndTime() != null && new Date().compareTo(DateUtils.getAddedMinutes(session.getEndTime(), 20)) > 0) {
			throw new NotFoundException(NotFoundException.SessionNotFound.SESSION_ENDED);
		}
	}

}
