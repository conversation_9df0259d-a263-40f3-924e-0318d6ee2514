package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.EventStreaming;
import com.accelevents.domain.User;
import com.accelevents.graphql.GraphQL;
import com.accelevents.utils.Constants;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.core.task.SimpleAsyncTaskExecutor;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class BroadcastEventStreamGraphQLHandler extends GraphQL {

    public BroadcastEventStreamGraphQLHandler(GraphQLConfiguration graphQLConfiguration, String authToken) {
        super(graphQLConfiguration.getAppsyncBroadcastEventStreamApiUrl(), authToken);
    }

    public void handleCreateOrUpdateEventStreamingStatusLogs(String inputStatus, EventStreaming eventStreaming, double videoDuration) {
        new SimpleAsyncTaskExecutor().execute(() -> {
            handleCreateOrUpdateEventStreamingStatusLogs(inputStatus, eventStreaming, null, videoDuration);
        });
    }

    public void handleCreateOrUpdateEventStreamingStatusLogs(String inputStatus, EventStreaming eventStreaming, User user, double videoDuration) {
        try {
            String actionUser = null;
            if (user != null) {
                actionUser = StringUtils.isNotBlank(user.getFirstName()) ?
                        (user.getFirstName() + " " + user.getLastName()) : user.getEmail();
            }

            logger.info("handleCreateOrUpdateSessionStatusLogs EventStreamId {} status {} type {} actionUser {} ", eventStreaming.getId(), inputStatus, eventStreaming.getType().name(), actionUser);
            Map<String, String> map = getEventStreamingStatusLogs(eventStreaming);
            if (!StringUtils.isNotBlank(map.get(Constants.EVENT_ID))) {
                createEventStreamingStatusLogs(Constants.CREATED, eventStreaming, actionUser, videoDuration);
            }
            updateEventStreamingStatusLogs(inputStatus, eventStreaming, actionUser, videoDuration);
        } catch (JSONException e) {
            logger.error("handleCreateOrUpdateSessionStatusLogsJSONException {}", e);
        }
    }


    public Map<String, String> getEventStreamingStatusLogs(EventStreaming session) throws JSONException {
        String operationName = Constants.GET_EVENT_STREAM_STATUS_LOG;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.QUERY, "query getEventStreamStatusLog { getEventStreamStatusLog(eventId: " + session.getEventId() + ", type: \"" + session.getType().name() + "\") { eventId type updatedAt status } }");
        return executeSelect(mainRequest, operationName);
    }

    public boolean executeQuery(JSONObject mainRequest, String operationName) {
        Map<String, String> map = executeSelect(mainRequest, operationName);
        return StringUtils.isNotBlank(map.get(Constants.EVENT_ID));
    }

    private Map<String, String> executeSelect(JSONObject mainRequest, String operationName) {
        try {
            JSONObject finalResult = execute(mainRequest, operationName);
            Map<String, String> map = new HashMap<>();

            if (finalResult.has(Constants.EVENT_ID)) {
                map.put(Constants.EVENT_ID, String.valueOf(finalResult.get(Constants.EVENT_ID)));
            }
            if (finalResult.has(Constants.UPDATEDAT)) {
                map.put(Constants.UPDATEDAT, String.valueOf(finalResult.get(Constants.UPDATEDAT)));
            }
            if (finalResult.has(Constants.STATUS)) {
                map.put(Constants.STATUS, finalResult.getString(Constants.STATUS));
            }
            if (finalResult.has(Constants.TYPE_LOWER_CASE)) {
                map.put(Constants.TYPE_LOWER_CASE, finalResult.getString(Constants.TYPE_LOWER_CASE));
            }

            return map;

        } catch (UnirestException e) {
            logger.error("GraphqlHandlerExecuteUnirest {} ", e);
        } catch (JSONException e) {
            logger.error("GraphqlHandlerExecuteJSONException {} ", e);
        }
        return Collections.emptyMap();
    }


    public boolean createEventStreamingStatusLogs(String status, EventStreaming session, String actionUser, double videoDuration) throws JSONException {
        JSONObject variable = new JSONObject();
        variable.put(Constants.CREATED_EVENT_STREAM_STATUS_LOG_INPUT, createSessionInput(status, session, actionUser, videoDuration, session.getPlayBackRestrictionToken()));

        String operationName = Constants.CREATED_EVENT_STREAM_STATUS_LOG;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, Constants.CREATE_EVENT_STREAM_STATUS_LOG_MUTATION);

        return executeQuery(mainRequest, operationName);
    }

    public boolean updateEventStreamingStatusLogs(String status, EventStreaming eventStreaming, String actionUser, double videoDuration) throws JSONException {
        JSONObject variable = new JSONObject();
        variable.put(Constants.UPDATE_EVENT_STREAM_STATUS_LOG_INPUT, updateSessionInput(eventStreaming.getType().toString(), status, eventStreaming.getEventId(), actionUser, eventStreaming.getStreamUrl(), videoDuration, eventStreaming.getPlayBackRestrictionToken()));

        String operationName = Constants.UPDATE_EVENT_STREAM_STATUS_LOG;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, Constants.UPDATE_EVENT_STREAM_STATUS_LOG_MUTATION);
        return executeQuery(mainRequest, operationName);
    }


    public JSONObject createSessionInput(String status, EventStreaming session, String actionUser, double videoDuration, String playBackRestrictionToken) throws JSONException {
        JSONObject requestData = updateSessionInput(session.getType().name(), status, session.getEventId(), actionUser, session.getStreamUrl(), videoDuration, playBackRestrictionToken);//NOSONAR
        return requestData;
    }

    public JSONObject updateSessionInput(String type, String status, Long eventId, String actionUser, String streamUrl, double videoDuration, String playBackRestrictionToken) throws JSONException {
        JSONObject requestData = new JSONObject();
        requestData.put(Constants.EVENT_ID, eventId);
        requestData.put(Constants.STATUS, status);
        requestData.put(Constants.TYPE_LOWER_CASE, type);
        requestData.put(Constants.PLAYBACK_URL, StringUtils.isNotBlank(streamUrl) ? streamUrl : Constants.STRING_EMPTY);
        requestData.put(Constants.PLAYBACK_RESTRICTION_TOKEN, StringUtils.isNotBlank(playBackRestrictionToken) ? playBackRestrictionToken : null);
        requestData.put("videoDuration",videoDuration);
        if (StringUtils.isNotBlank(actionUser)) {
            requestData.put(Constants.ACTION_USER, actionUser);
        }
        return requestData;
    }


}
