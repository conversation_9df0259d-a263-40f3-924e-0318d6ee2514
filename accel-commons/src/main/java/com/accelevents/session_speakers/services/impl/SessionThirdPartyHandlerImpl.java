package com.accelevents.session_speakers.services.impl;

import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.ChannelInfoDto;
import com.accelevents.common.dto.UploadSessionDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.MuxDirectUploadIdAndUrlDto;
import com.accelevents.dto.MuxJwtDto;
import com.accelevents.dto.StreamKeyAndRtmpUrlDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.services.*;
import com.accelevents.session_speakers.dto.CaptionsDto;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import com.accelevents.session_speakers.services.MUXLivestreamAssetService;
import com.accelevents.session_speakers.services.SessionDetailsService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionThirdPartyService;
import com.accelevents.utils.MuxUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.CAN_NOT_GENERATE_STREAM_IN_PROGRESS;
import static com.accelevents.utils.Constants.*;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class SessionThirdPartyHandlerImpl implements SessionThirdPartyService {

    private static final Logger log = LoggerFactory.getLogger(SessionThirdPartyHandlerImpl.class);

	private final SessionRepoService sessionService;
	private final MuxService muxService;
	private final GetStreamService getStreamService;
	private final VoxeetService voxeetService;
	private final StaffService staffService;
	private final CacheStoreService cacheStoreService;
    private final MUXLivestreamAssetService muxLivestreamAssetService;

    private final SessionDetailsService sessionDetailsService;

    private final EventPlanConfigService eventPlanConfigService;


	@Autowired
	public SessionThirdPartyHandlerImpl(SessionRepoService sessionService,
										MuxService muxService,
										GetStreamService getStreamService,
										VoxeetService voxeetService,
										StaffService staffService,
										CacheStoreService cacheStoreService,
                                        MUXLivestreamAssetService muxLivestreamAssetService,
                                        SessionDetailsService sessionDetailsService,EventPlanConfigService eventPlanConfigService){
		this.sessionService = sessionService;
		this.muxService = muxService;
		this.getStreamService = getStreamService;
		this.voxeetService = voxeetService;
		this.staffService = staffService;
		this.cacheStoreService = cacheStoreService;
        this.muxLivestreamAssetService = muxLivestreamAssetService;
        this.sessionDetailsService = sessionDetailsService;
        this.eventPlanConfigService = eventPlanConfigService;
	}


	@Override
	public StreamKeyAndRtmpUrlDto createStreamKey(Long sessionId, Event event, boolean regenerate, CaptionsDto captions) {

        eventPlanConfigService.validateTheOrganizerOrWhiteLabelPlan(event);
		Session session = sessionService.getSessionByIdWithoutCache(sessionId, event);
		log.info("createStreamKey {} liveStreamId {} ",sessionId, session.getLiveStreamId());
		if((isBlank(session.getStreamKey())
				|| isBlank(session.getRtmpUrl()))
				|| isBlank(session.getStreamUrl())
				|| regenerate){
			if(regenerate){
				String status = muxService.checkStreamStatus(session.getLiveStreamId());
				if("active".equals(status)){
					throw new NotAcceptableException(CAN_NOT_GENERATE_STREAM_IN_PROGRESS);
				}
			}
            StreamKeyAndRtmpUrlDto dto = muxService.createStreamKey(event, "SESSION_" + sessionId, captions);
			dto.setAccelEventsStudio(session.isAccelEventsStudio());
			sessionService.updateSessionOnMuxStreamKeyCreation(session, dto);
            sessionDetailsService.updateSessionDetailOnMuxStreamKeyCreation(session, captions);
			return dto;
		}
		return null;
	}

	@Override
	public MuxDirectUploadIdAndUrlDto createUploadUrlForSession(Long sessionId, Event event) {
        String metaData = muxService.buildUploadMetaData(sessionId, event.getEventId());
        MuxDirectUploadIdAndUrlDto muxDirectUploadIdAndUrlDto = muxService.createDirectUploadUrl(true, metaData);
        log.info("Created upload url and ID for session ={}, upload details ={}" , sessionId , muxDirectUploadIdAndUrlDto);

        return muxDirectUploadIdAndUrlDto;
	}

	@Override
	public String getPlaybackIdByUploadId(String uploadId) {
		String assetIdByDirectUploadId = muxService.getAssetIdByDirectUploadId(uploadId);
		return muxService.getPlayBackIdByAssetId(assetIdByDirectUploadId);
	}
    @Override
    public MuxAssetDTO getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(Long sessionId, Event event, String uploadId, AssetType assetType) throws JSONException {
        return getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(sessionId, event, uploadId, assetType, null, null);
    }
    @Override
    public MuxAssetDTO getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(Long sessionId, Event event, String uploadId, AssetType assetType, String title, String description) throws JSONException {
	    String assetIdByDirectUploadId = muxService.getAssetIdByDirectUploadId(uploadId);
        JSONObject assetDetailsJson = muxService.getAssetDetails(assetIdByDirectUploadId);
        if (assetDetailsJson != null){
            MuxAssetDTO muxAssetDTO = MuxUtils.buildMuxAssetDataForDirectUpload(assetDetailsJson);
            MuxJwtDto muxJwtDto = muxService.generateDomainPlayBackRestrictionToken(muxAssetDTO.getPlayBackId(), event);
            muxAssetDTO.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
            muxAssetDTO.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());
            muxAssetDTO.setTitle(title);
            muxAssetDTO.setDescription(description);
            Session session = sessionService.getSessionByIdWithoutCache(sessionId, event);
            muxLivestreamAssetService.storeDirectUploadAssetDetails(session, muxAssetDTO, assetType);
            return muxAssetDTO;
        }
        return null;
    }

    @Override
    public void createChannelByListOfSessionIds(List<UploadSessionDto> sessions, Event event) {

        List<Long> eventAdmins = staffService.findAllEventAdminId(event);
        List<ChannelInfoDto> sessionChannels = new ArrayList<>();

        sessions.forEach(session -> {
            sessionChannels.add(new ChannelInfoDto(SESSION_.concat(String.valueOf(session.getSessionId())), session.getTitle(), LIVESTREAM, eventAdmins, false));
            if (EnumSessionFormat.MAIN_STAGE.equals(session.getFormat()) || EnumSessionFormat.BREAKOUT_SESSION.equals(session.getFormat())){
                sessionChannels.add(new ChannelInfoDto(BACKSTAGE_.concat(String.valueOf(session.getSessionId())), session.getTitle(), LIVESTREAM, eventAdmins, false));
            }
        });
        getStreamService.createChannelsInBatch(sessionChannels, 1L, event.getEventId());

    }

	@Override
	@Async
	public void createChannelByListOfSessionIds(Collection<Session> sessions, List<Long> eventAdmins) {
	    if(CollectionUtils.isEmpty(sessions)) {
	        return;
        }
        List<ChannelInfoDto> sessionChannels = new ArrayList<>();

		sessions.forEach(session-> {
            if (!EnumSessionFormat.MEET_UP.equals(session.getFormat())) {
                sessionChannels.add(new ChannelInfoDto(SESSION_.concat(String.valueOf(session.getId())), session.getTitle(), LIVESTREAM, eventAdmins,!session.isChatEnabled()));
                if (EnumSessionFormat.MAIN_STAGE.equals(session.getFormat()) || EnumSessionFormat.BREAKOUT_SESSION.equals(session.getFormat())){
                    sessionChannels.add(new ChannelInfoDto(BACKSTAGE_.concat(String.valueOf(session.getId())), session.getTitle(), LIVESTREAM, eventAdmins,!session.isChatEnabled()));
                }
            }
		});
        getStreamService.createChannelsInBatch(sessionChannels, 1L, sessions.stream().findFirst().get().getEventId());
	}

	@Override
	@Async
	public void createChannel(Long sessionId, String channelName, EnumSessionFormat sessionFormat, Event event) {
		List<Long> eventAdmins = staffService.findAllEventAdminId(event);
		getStreamService.createChannelWithChannelName(SESSION_.concat(String.valueOf(sessionId)), channelName, LIVESTREAM,1L, eventAdmins, false, event.getEventId());
		if(EnumSessionFormat.MAIN_STAGE.equals(sessionFormat) || EnumSessionFormat.BREAKOUT_SESSION.equals(sessionFormat)){
			getStreamService.createChannelWithChannelName(BACKSTAGE_.concat(String.valueOf(sessionId)), channelName, LIVESTREAM,1L, eventAdmins, false, event.getEventId());
		}
	}

	@Async
    @Override
    public void deleteSessionChannel(Long sessionId, Long eventId) {
        List<String> sessionIds = new ArrayList<>();
        sessionIds.add(SESSION_.concat(String.valueOf(sessionId)));
        sessionIds.add(BACKSTAGE_.concat(String.valueOf(sessionId)));
        getStreamService.deleteChannelsInBatch(sessionIds, eventId);
    }

    @Override
	public void stopConference(String confId) {
		//voxeetService.stopConference(confId, , );
	}

	@Override
	public void updateChannelName(Long id, String title, EnumSessionFormat sessionFormat, boolean disableChat, Long eventId) {
		getStreamService.updateChannelName(SESSION_.concat(String.valueOf(id)), title, LIVESTREAM, disableChat, eventId);
		if(EnumSessionFormat.MAIN_STAGE.equals(sessionFormat) || EnumSessionFormat.BREAKOUT_SESSION.equals(sessionFormat)){
			getStreamService.updateChannelName(BACKSTAGE_.concat(String.valueOf(id)), title, LIVESTREAM, false, eventId);
            log.info("Update the channel id: {} name: {} with disable chat toggle: {} of the eventId: {} ", BACKSTAGE_.concat(String.valueOf(id)), title, false, eventId);
		}
	}

	@Override
	public Integer setChatCountBySessionId(Long sessionId) {
		String key = "SESSION_CHAT_COUNT"+sessionId;
		Integer count = (Integer) cacheStoreService.get(key);
		if(count == null){
            Optional<Session> sessionOptional =  sessionService.findById(sessionId);
            if(sessionOptional.isEmpty()){
                return 0;
            }
			count =  getStreamService.getChannelMsgCount(SESSION_.concat(Long.toString(sessionId)),
					sessionOptional.get().getTitle(), LIVESTREAM);
			cacheStoreService.set(key, count, 60, TimeUnit.SECONDS);
		}
        return count;
	}

}
