
package com.accelevents.session_speakers.services.impl;

import com.accelevents.common.dto.RedisChallengeAreaDTO;
import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionStartStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.StreamProvider;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.networking.services.NetworkingMatcheQueueRepoService;
import com.accelevents.networking.services.NetworkingMatchesService;
import com.accelevents.networking.services.NetworkingRulesService;
import com.accelevents.repositories.ExternalEventSyncTrackingRepository;
import com.accelevents.repositories.RtmpSessionAuditLogsRepo;
import com.accelevents.services.ChallengeConfigService;
import com.accelevents.services.EventCECriteriaService;
import com.accelevents.services.EventTaskService;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.session_speakers.dto.PostSessionCallToActionDto;
import com.accelevents.session_speakers.services.*;
import com.accelevents.utils.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class SessionDeleteHandler {

    private static final Logger log = LoggerFactory.getLogger(SessionDeleteHandler.class);

	@Autowired private SessionSpeakerService sessionSpeakerService;
	@Autowired private UserSessionService userSessionService;
	@Autowired private UserInterestedSessionService userInterestedSessionService;
	@Autowired private NetworkingRulesService networkingRulesService;
	@Autowired private RtmpSessionAuditLogsRepo rtmpSessionAuditLogsRepo;
	@Autowired private NetworkingMatchesService networkingMatchesService;
	@Autowired private NetworkingMatcheQueueRepoService networkingMatchesQueueRepoService;
	@Autowired private MUXLivestreamAssetService muxLivestreamAssetService;
	@Autowired private SessionRepoService sessionRepoService;
	@Autowired private SessionTagAndTrackService sessionTagAndTrackService;
	@Autowired private SessionBroadCastService sessionBroadCastService;
	@Autowired private GraphQLConfiguration graphQLConfiguration;
	@Autowired private SessionService sessionService;
    @Autowired private SessionDetailsService sessionDetailsService;
    @Autowired private ChallengeConfigService challengeService;
    @Autowired private ExternalEventSyncTrackingRepository externalEventSyncTrackingRepository;
    @Autowired private EventTaskService eventTaskService;
    @Autowired private EventCECriteriaService eventCECriteriaService;

    @Autowired
    private GamificationCacheStoreService<String,Object> redisCacheService;

	@Transactional
	public void deleteSession(Long sessionId, Event event, User user, String authToken) {
		Session session = sessionRepoService.getSessionById(sessionId, event);
        log.info("request Received for delete session {}, Event {}, user {}",session.getId(),event.getEventId(), user.getUserId());
        try {
            if (StreamProvider.ACCELEVENTS.equals(session.getStreamProvider())
                    && session.isAccelEventsStudio()
                    && EnumSessionStartStatus.STARTED.equals(session.getSessionStartStatus())) {
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_CAN_NOT_BE_DELETE);
            } else {
                new BroadcastGraphQLHandler(graphQLConfiguration, authToken)
                        .deleteSessionStatusLogs(sessionId, event.getEventId());
                sessionRepoService.updateStatusToDeleteBySessionId(sessionId, event.getEventId(), RecordStatus.DELETE);
                redisCacheService.clearSessionCache(event.getEventId());
            }
        } catch (Exception e) {
            throw new NotAcceptableException(e);
        }
		networkingMatchesQueueRepoService.updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);
		userInterestedSessionService.deleteBySessionId(sessionId);
		userSessionService.deleteBySessionId(sessionId);
		rtmpSessionAuditLogsRepo.updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);
		networkingMatchesService.deleteRecordsBySessionId(sessionId);
		sessionSpeakerService.deleteSessionSpeakerBySession(sessionId);
		networkingRulesService.deleteNetworkingRuleBySessionId(sessionId);
		muxLivestreamAssetService.updateStatusToDeleteBySessionId(sessionId);
		sessionTagAndTrackService.deleteBySessionId(sessionId);
        sessionDetailsService.deleteBySessionId(sessionId);
        challengeService.removeTriggerFromChallengeOnDelete(sessionId, RedisChallengeAreaDTO.getTriggerNameBySessionFormat(session.getFormat()), event);
        eventCECriteriaService.removeSessionOrTrackFromCriteriaTrigger(List.of(sessionId), RedisChallengeAreaDTO.getTriggerNameBySessionFormat(session.getFormat()), event);


        List<SessionDetails> sessionDetails = sessionDetailsService.getAllSessionDetailsByEventIdWithoutCache(event.getEventId());

        if(sessionDetails != null){
            for(SessionDetails sessionDetail: sessionDetails){
                PostSessionCallToActionDto postSessionCallToActionJson = sessionDetail.getPostSessionCallToActionJson() != null ? CommonUtil.getDtoPostCallToActionJson(sessionDetail.getPostSessionCallToActionJson()) : new PostSessionCallToActionDto();
                if(postSessionCallToActionJson.isEnablePostSessionCallToAction() && CommonUtil.convertToLongValue(postSessionCallToActionJson.getDestination()).equals(sessionId)) {
                    postSessionCallToActionJson.setEnablePostSessionCallToAction(Boolean.FALSE);
                    postSessionCallToActionJson.setDestinationType(null);
                    postSessionCallToActionJson.setDescription(null);
                    postSessionCallToActionJson.setButtonLabelText(null);
                    postSessionCallToActionJson.setButtonLabelText(null);
                    postSessionCallToActionJson.setDescription(null);
                    sessionDetail.setPostSessionCallToActionJson(CommonUtil.convertToGson(postSessionCallToActionJson));
                    sessionDetailsService.save(sessionDetail);
                }
            }
        }
        sessionDetailsService.getSessionDetailsBySession(session);

		// Update event checklist flag as false
		sessionService.setAgendaAddedChecklistFlag(event);

		//TODO: APIGW_UN  //NOSONAR
		//clearSessionAPIGatewayCache.clearEventAllSessionCache(event.getEventURL());//nosonar
        externalEventSyncTrackingRepository.deleteByEventIdAndAeIdAndExternalSourceType(event.getEventId(), sessionId, "SESSION");
        eventTaskService.removeTaskParticipantsFromSpeakerInSession(event, Collections.singletonList(sessionId));
        log.info("Session {} Deleted by user {}",session.getId(), user.getUserId());
	}
}
