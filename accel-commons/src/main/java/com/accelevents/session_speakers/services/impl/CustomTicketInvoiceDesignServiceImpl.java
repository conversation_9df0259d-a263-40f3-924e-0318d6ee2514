package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.notification.services.impl.SendGridMailPrepareServiceImpl;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.registration.approval.repositories.RegistrationRequestTicketRepository;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.repositories.ConfirmationEmailRepository;
import com.accelevents.repositories.ConfirmationPagesRepository;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.ro.event.service.ROConfirmationEmailService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.services.ConfirmationEmailService;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.services.TicketingService;
import com.accelevents.services.impl.ConfirmationEmailServiceImpl;
import com.accelevents.services.repo.helper.EventDesignDetailRepoService;
import com.accelevents.session_speakers.services.CustomTicketInvoiceDesignService;
import com.accelevents.utils.*;
import com.cloudinary.utils.StringUtils;
import freemarker.template.Configuration;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;

@Service
public class CustomTicketInvoiceDesignServiceImpl implements CustomTicketInvoiceDesignService {

    @Autowired
    private ConfirmationPagesRepository confirmationPagesRepository;

    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;

    @Autowired
    private EventDesignDetailRepoService eventDesignDetailRepoService;

    @Autowired
    private ServiceHelper serviceHelper;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private Configuration freemarkerMailConfiguration;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;
    @Autowired
    private ImageConfiguration imageConfiguration;

    @Autowired
    private SendGridMailPrepareServiceImpl sendGridMailPrepareService;

    @Autowired
    private RegistrationRequestsService registrationRequestsService;

    @Autowired
    private RegistrationRequestTicketRepository registrationRequestTicketRepository;

    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Autowired
    private ROVirtualEventService roVirtualEventService;

    @Autowired
    private RegistrationAttributeRepository registrationAttributeRepository;

    @Autowired
    private ConfirmationEmailService confirmationEmailService;
    @Autowired
    private ROConfirmationEmailService roConfirmationEmailService;

    @Autowired
    private ConfirmationEmailRepository confirmationEmailRepository;

    @Value("${uiBaseurl}")
    private String uiBaseUrl;

    @Autowired
    private ConfirmationPagesServiceImpl confirmationPagesService;

    @Autowired
    private ConfirmationEmailServiceImpl confirmationEmailServiceImpl;

    private static final Logger log = LoggerFactory.getLogger(CustomTicketInvoiceDesignServiceImpl.class);

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public ResponseDetailDto createCustomDesign(Event event, Long userId, CustomTemplatesDto customTemplatesDto, boolean isDefault,TemplateType templateType) {
        CustomTemplates customTemplates =new CustomTemplates();
        Long customDesignId = null;
        if (isDefault){
            log.info("Default custom design creating for event: {} and created by: {} and template type : {} ",event.getEventId(),userId,templateType);
            customDesignId = createDefaultCustomDesign(event, customTemplates,templateType,customTemplatesDto);
                log.info("default custom design created for event: {} and created by: {} and ticket type : {} ",event.getEventId(),userId,templateType);
        } else {
            log.info("Custom design creating for event: {} and created by: {} : template Type : {} ",event.getEventId(),userId,templateType);
            customDesignId = createCustomDesign(event, customTemplatesDto, customTemplates,templateType);
        }
        log.info("Custome design created for event: {} and isDefault: {} and created by: {} : template type : {} ",event.getEventId(),isDefault,userId,templateType);
        return new ResponseDetailDto(SUCCESS,SUCCESS,customDesignId);
    }

    private Long createCustomDesign(Event event, CustomTemplatesDto customTemplatesDto, CustomTemplates customTemplates,TemplateType templateType) {
        if(customTemplatesDto!=null) {
            checkIsCustomeDesignNameUnique(event, customTemplatesDto.getPageName().trim(),null,false,templateType);

            customTemplatesDto.setTemplateType(templateType);

            customTemplatesDto.setPageType(PageType.INTERNAL);
            customTemplates = customTemplatesDto.toEntity(event, customTemplates);
            customTemplates.setCreatedAt(new Date());
            customTemplates.setDefaultPage(false);
            customTemplates = confirmationPagesRepository.save(customTemplates);
            if(!(TemplateType.INVOICE_PDF_DESIGN).equals(templateType))
            updateCustomTemplateIdForEventTicketTypes(customTemplatesDto.getAllowedTicketTypes(),customTemplates,event,templateType);
            log.info("Custom design created for event: {} and pageName: {} and template type : {} ",event.getEventId(),customTemplatesDto.getPageName(),templateType);
            return customTemplates.getId();
        }
        return null;
    }

    private void updateCustomTemplateIdForEventTicketTypes(List<Long> ticketTypesToBeAllowed, CustomTemplates customTemplates, Event event, TemplateType templateType) {
        List<TicketingType> existingTicketingTypeList = null;
        if ((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
            existingTicketingTypeList = ticketingTypeRepository.findAllTicketTypesByCustomTicketPdfDesignId(event, customTemplates.getId());
        else
            log.info("updateCustomTemplateIdForEventTicketTypes : Template type not found for custom design");

            List<TicketingType> updatedTicketingTypeList = new ArrayList<>();
            updatedTicketingTypeList = confirmationPagesService.getUpdatedTickets(ticketTypesToBeAllowed, event,updatedTicketingTypeList);

            // when create new custom desing then added ticket types for the allowed ticket types
            if (existingTicketingTypeList.isEmpty() && !ticketTypesToBeAllowed.isEmpty()) {
                List<TicketingType> ticketingTypes = ticketingTypeRepository.findByIds(ticketTypesToBeAllowed);
                if((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
                ticketingTypes.forEach(ticket -> ticket.setCustomTicketPdfDesignId(customTemplates.getId()));
                else
                    log.info("updateCustomTemplateIdForEventTicketTypes : Template type not found for custom design");

                updatedTicketingTypeList.addAll(ticketingTypes);
                log.info("Setting allowed ticket types for custom design for event: {} and number of ticketingTypes {} and template type {} ", customTemplates.getEvent().getEventId(), ticketingTypes.size(), templateType);
            } else {

                // Identify excluded ticketing types and set custom template id to default template for excluded ticket types
                List<TicketingType> excludedTicketTypeList = existingTicketingTypeList.stream()
                        .filter(ticket -> !ticketTypesToBeAllowed.contains(ticket.getId()))
                        .collect(Collectors.toList());

                CustomTemplates defaultCustomTemplates = confirmationPagesRepository.findByEventAndIsDefaultPageAndTemplateType(customTemplates.getEvent(), true, templateType);
                if (defaultCustomTemplates != null) {
                    if((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
                    excludedTicketTypeList.forEach(ticket -> ticket.setCustomTicketPdfDesignId(defaultCustomTemplates.getId()));
                    else
                        log.info("updateCustomTemplateIdForEventTicketTypes : Template type not found for custom design");

                    updatedTicketingTypeList.addAll(excludedTicketTypeList);
                    log.info("excluded ticketing types and set custom template id {} to default template for excluded ticket types {} and template type {} ", customTemplates.getId(), excludedTicketTypeList.size(), templateType);
                }
                List<Long> newlyAddedTicketTypeIds = confirmationPagesService.getNewlyAddedTicketTypeIds(ticketTypesToBeAllowed, existingTicketingTypeList);
                if (!newlyAddedTicketTypeIds.isEmpty()) {
                    List<TicketingType> newlyAddedTicketTypeList = ticketingTypeRepository.findByIds(newlyAddedTicketTypeIds);
                    if((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
                    newlyAddedTicketTypeList.forEach(ticket -> ticket.setCustomTicketPdfDesignId(customTemplates.getId()));
                    else
                        log.info("updateCustomTemplateIdForEventTicketTypes : Template type not found for custom design");

                    updatedTicketingTypeList.addAll(newlyAddedTicketTypeList);
                }
                log.info("Setting newly added ticket types for custom design {} and number of ticketingTypes {} and template type {} ", customTemplates.getId(), newlyAddedTicketTypeIds.size(), templateType);
            }

        // Save all updated ticketing types
        if(!CollectionUtils.isEmpty(updatedTicketingTypeList))
        ticketingTypeRepository.saveAll(updatedTicketingTypeList);
        log.info("Setting allowed ticket types for custom design {} and updatedTicketingTypeList size {} and template type {} ", customTemplates.getId(), updatedTicketingTypeList.size(), templateType);

    }

    private Long createDefaultCustomDesign(Event event, CustomTemplates customTemplates,TemplateType templateType,CustomTemplatesDto customTemplatesDto) {
        log.info("default custom design creating for event: {} and template type : {} ",event.getEventId(),templateType);
        customTemplatesDto.setPageType(PageType.INTERNAL);

        // if page name exist in the request then assign that name
        if(!ObjectUtils.isEmpty(customTemplatesDto) && !ObjectUtils.isEmpty(customTemplatesDto.getPageName()))
            customTemplatesDto.setPageName(customTemplatesDto.getPageName().trim());
        else if(templateType.equals(TemplateType.TICKET_PDF_DESIGN))
            customTemplatesDto.setPageName(DEFAULT_TICKET_PDF_DESIGN);
        else if(templateType.equals(TemplateType.INVOICE_PDF_DESIGN))
            customTemplatesDto.setPageName(DEFAULT_TICKET_INVOICE_DESIGN);
        else
            log.info("template type not found for custom design");

        checkIsCustomeDesignNameUnique(event, customTemplatesDto.getPageName().trim() ,null,false,templateType);

        customTemplatesDto.setTemplateType(templateType);

        customTemplatesDto.setCreatedAt(new Date());
        customTemplates = customTemplatesDto.toEntity(event, customTemplates);
        customTemplates.setDefaultPage(true);

        // if already default design page exist then set to false
        setDefaultPageToFalseForOtherCustomDesign(event, templateType);
        customTemplates = confirmationPagesRepository.save(customTemplates);

        // if allowed ticket type exist in the request then set to ticket types otherwise assign id to all ticket types
        if(!ObjectUtils.isEmpty(customTemplatesDto) && !ObjectUtils.isEmpty(customTemplatesDto.getAllowedTicketTypes())
        && (!(TemplateType.INVOICE_PDF_DESIGN).equals(templateType)))
            updateCustomTemplateIdForEventTicketTypes(customTemplatesDto.getAllowedTicketTypes(),customTemplates,event,templateType);
        else if ((!(TemplateType.INVOICE_PDF_DESIGN).equals(templateType) && customTemplates.getPageName().equals(DEFAULT_TICKET_PDF_DESIGN)))
            setAllTicketTypesForDefaultCustomDesign(event, customTemplates, templateType);

        return customTemplates.getId();

    }

    private void checkIsCustomeDesignNameUnique(Event event, String pageName, Long confirmationPageId, boolean isPageUpdate,TemplateType templateType) {
        if (!StringUtils.isBlank(pageName)) {
            CustomTemplates customTemplates = confirmationPagesRepository.findByEventAndPageNameAndTemplateType(event.getEventId(), pageName,templateType);
            if (customTemplates != null) {
                if (!isPageUpdate || !confirmationPageId.equals(customTemplates.getId())) {
                    log.info("Custom Design name already exists for event: {} and pageName: {} and template type: {}", event.getEventId(), pageName,templateType);
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CUSTOM_DESIGN_NAME_ALREADY_EXISTS);
                }
            }
        }
    }

    private void generateDefaultConfirmationPageHtmlAndJson(CustomTemplatesDto customTemplatesDto , TemplateType templateType) {
        String defaultHtml=null;
        String defaultJson=null;
        if((TemplateType.TICKET_PDF_DESIGN).equals(templateType)) {
            defaultHtml =getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_TICKET_ORDER_PDF_HTML));
            defaultJson = getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_TICKET_ORDER_PDF_JSON));
        } else if (TemplateType.INVOICE_PDF_DESIGN.equals(templateType)) {
            defaultHtml =getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_INVOICE_ORDER_PDF_HTML));
            defaultJson = getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_INVOICE_ORDER_PDF_JSON));
        }
        else
            log.info("template type not found for custom design");

        customTemplatesDto.setBeeFreeHtml(defaultHtml);
        customTemplatesDto.setBeeFreeJson(defaultJson);

    }

    public String getHtmlAndJsonFromPath(String path) {
        String defaultHtmlJson = null;
        try {
            defaultHtmlJson = FileReaderUtilsClass.getBeeFreeTemplate(path);
        } catch (IOException ioException) {
            log.error("Error while reading default custom design html and json",ioException);
        }
        return defaultHtmlJson;
    }

    @Override
    public DataTableResponse getAllCustomDesigns(Event event,TemplateType templateType,PageSizeSearchObj pageSizeSearchObj) {
        Page<CustomEmailsTemplateDto> customEmailTemplateDetailsDtos = confirmationEmailRepository.getConfirmationEmailsByEvent(event,pageSizeSearchObj.getPageable(), pageSizeSearchObj.getSearch(),Arrays.asList(templateType));
        Ticketing ticketing=ticketingService.findByEvent(event);
        DataTableResponse dataTableResponse = new DataTableResponse();
        List<CustomEmailsTemplateDto> customTemplateDetailsDtoList=customEmailTemplateDetailsDtos.getContent();
        for (CustomTemplateDetailsDto customTemplateDetailsDto:customTemplateDetailsDtoList) {
            List<TicketingType> ticketingTypeList= null;
            if((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
            ticketingTypeList = ticketingTypeRepository.findAllTicketTypesByCustomTicketPdfDesignId(event,customTemplateDetailsDto.getId());

            List<Long> allowedTicketTypes= setAllowedTicketTypeForCustomTemplatesPage(ticketing, ticketingTypeList);
            customTemplateDetailsDto.setAllowedTicketTypes(allowedTicketTypes);
        }
        dataTableResponse.setRecordsTotal(customEmailTemplateDetailsDtos.getTotalElements());
        dataTableResponse.setRecordsFiltered(customEmailTemplateDetailsDtos.getNumberOfElements());
        dataTableResponse.setData(customEmailTemplateDetailsDtos.getContent());
        return dataTableResponse;
    }

    @Override
    public ResponseDto updateCustomDesign(Event event, CustomTemplatesDto customTemplatesDto, Long confirmationPageId, User user,TemplateType templateType,boolean defaultDesign) {
        log.info("Custom Design updating confirmationPageId:{} for event: {} and updated by: {} and template type: {}",confirmationPageId,event.getEventId(),user.getUserId(),templateType);
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findByIdAndTemplateType(confirmationPageId,templateType);
        if (customTemplatesOptional.isPresent() && customTemplatesDto!=null) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            checkIsCustomeDesignNameUnique(event, customTemplatesDto.getPageName().trim(),confirmationPageId,true,templateType);
            customTemplates = customTemplatesDto.toEntity(event,customTemplates);
            customTemplates.setUpdatedAt(new Date());
            customTemplates.setPageUpdated(true);
            customTemplates.setPageType(PageType.INTERNAL);
            // if user save the page for the actual use then we update the html value and unPublishedHtml value as same
            customTemplates.setUnPublishedHtml(customTemplatesDto.getBeeFreeHtml());

            // if default design is true then set default page to true and if any default page already exist then set to false
            if(defaultDesign) {
                customTemplates.setDefaultPage(true);
                setDefaultPageToFalseForOtherCustomDesign(event, templateType);
            }
            confirmationPagesRepository.save(customTemplates);
            if(!(TemplateType.INVOICE_PDF_DESIGN).equals(templateType))
            updateCustomTemplateIdForEventTicketTypes(customTemplatesDto.getAllowedTicketTypes(),customTemplates,event,templateType);
            log.info("Custom design updated confirmationPageId:{} for event: {} and updated by: {} and template type: {}",confirmationPageId,event.getEventId(),user.getUserId(),templateType);
            return new ResponseDto(SUCCESS, SUCCESS);
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CUSTOM_DESIGN_NOT_FOUND);
        }
    }

    private void setDefaultPageToFalseForOtherCustomDesign(Event event, TemplateType templateType) {
        CustomTemplates customTemplates = confirmationPagesRepository.findByEventAndIsDefaultPageAndTemplateType(event, true, templateType);
       // if not found any default design then return
        if(ObjectUtils.isEmpty(customTemplates))
           return;
        customTemplates.setDefaultPage(false);
        confirmationPagesRepository.save(customTemplates);
    }

    @Override
    public CustomTemplatesDto getCustomDesign(Event event, Long confirmationPageId,TemplateType templateType) {
        Ticketing ticketing=ticketingService.findByEvent(event);
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findByIdAndTemplateType(confirmationPageId,templateType);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            CustomTemplatesDto customTemplatesDto=CustomTemplatesDto.toDto(customTemplates);
            if(customTemplatesDto.getBeeFreeHtml() == null || customTemplatesDto.getBeeFreeJson() == null){
                generateDefaultConfirmationPageHtmlAndJson(customTemplatesDto,templateType);
            }
            List<TicketingType> ticketingTypeList = null;
            if ((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
                ticketingTypeList =ticketingTypeRepository.findAllTicketTypesByCustomTicketPdfDesignId(event,customTemplates.getId());

            if(!(TemplateType.INVOICE_PDF_DESIGN).equals(templateType)) {
                List<Long> allowTicketTypes = setAllowedTicketTypeForCustomTemplatesPage(ticketing, ticketingTypeList);
                customTemplatesDto.setAllowedTicketTypes(allowTicketTypes);
            }

            // replace the event Logo widget related merge tags with the actual event logo for Host side Page builder
            confirmationPagesService. replaceMergeTagsForHost(customTemplatesDto, event);
            return customTemplatesDto;
        } else {
            throw new NotFoundException(NotFoundException.NotFound.CUSTOM_DESIGN_NOT_FOUND);
        }
    }

    public List<Long> setAllowedTicketTypeForCustomTemplatesPage(Ticketing ticketing, List<TicketingType> ticketingTypeList) {
        List<Long> allowedTicketTypes=new ArrayList<>();
        if(!CollectionUtils.isEmpty(ticketingTypeList)) {
            for (TicketingType ticketingType : ticketingTypeList) {
                if (ticketing.isRecurringEvent()) {
                    // added ticket types for the recurring event
                    if (ticketingType.getRecurringEventId() == null || ticketingType.getCreatedFrom().equals(-1L)) {
                        allowedTicketTypes.add(ticketingType.getId());
                    }
                } else {
                    allowedTicketTypes.add(ticketingType.getId());
                }
            }
        }
        return allowedTicketTypes;
    }

    @Override
    public ResponseDto deleteCustomDesign(Event event, Long confirmationPageId,User user , TemplateType templateType,Long assignToCustomDesignId) {
        log.info("Custom Design deleting confirmationPageId:{} for event: {} and deletedBy: {} and template type: {}",confirmationPageId,event.getEventId(),user.getUserId(),templateType);
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findByIdAndTemplateType(confirmationPageId,templateType);

        Optional<CustomTemplates> assignToCustomDesign = confirmationPagesRepository.findByIdAndTemplateType(assignToCustomDesignId,templateType);

        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            if (customTemplates.isDefaultPage()) {
                log.info("Default Custom design cannot be deleted custom design id :{} for event: {} and deletedBy: {} and template type: {} ",confirmationPageId,event.getEventId(),user.getUserId(),templateType);
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DEFAULT_CUSTOM_DESIGN_CANNOT_BE_DELETED);
            }
            List<TicketingType> attachedTicketingTypeList = null;
            if(templateType.equals(TemplateType.TICKET_PDF_DESIGN))
                attachedTicketingTypeList =ticketingTypeRepository.findAllTicketTypesByCustomTicketPdfDesignId(event, customTemplates.getId());
             if (CollectionUtils.isNotEmpty(attachedTicketingTypeList)) {
                    if (assignToCustomDesign.isEmpty())
                        throw new NotFoundException(NotFoundException.NotFound.ASSIGNED_CUSTOM_DESIGN_NOT_FOUND);

                    attachedTicketMoveToTheAssignedCustomDesignPage(event, attachedTicketingTypeList, templateType, assignToCustomDesign.get());
                }

            customTemplates.setRecStatus(RecordStatus.DELETE);
            confirmationPagesRepository.save(customTemplates);
            log.info("Custom design deleted custom design id :{} for event: {} and deletedBy: {} and template type: {}",confirmationPageId,event.getEventId(),user.getUserId(),templateType);
            return new ResponseDto(SUCCESS, SUCCESS);
        } else {
            throw new NotFoundException(NotFoundException.NotFound.CUSTOM_DESIGN_NOT_FOUND);
        }
    }

    private void attachedTicketMoveToTheAssignedCustomDesignPage(Event event, List<TicketingType> attchedTicketingTypeList,TemplateType templateType, CustomTemplates assignToCustomDesignId) {

        // ticket assigned to the new selected custom design page
        if((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
        attchedTicketingTypeList.forEach(ticket -> ticket.setCustomTicketPdfDesignId(assignToCustomDesignId.getId()));

        if(!CollectionUtils.isEmpty(attchedTicketingTypeList))
        ticketingTypeRepository.saveAll(attchedTicketingTypeList);
        log.info("Attached ticketing types moved to default custom design for event: {} and number of ticketingTypes {} and template type: {}",event.getEventId(),attchedTicketingTypeList.size(),templateType);
    }

    @Override
    public ResponseDto resetDefaultCustomDesign(Event event, User user,Long customDesignId,TemplateType templateType) {
        return confirmationEmailService.resetDefaultCustomEmailTemplate(event, user, customDesignId, templateType);
    }

    @Override
    public ResponseDetailDto duplicateCustomDesign(Event event, Long confirmationPageId,String pageName, List<Long> allowedTicketTypes,User user,String pageUrl,TemplateType templateType) {

        checkIsCustomeDesignNameUnique(event, pageName.trim(),null,false,templateType);

        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findByIdAndTemplateType(confirmationPageId,templateType);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customConfirmationAndReminderTemplate = customTemplatesOptional.get();
            CustomTemplates duplicateCustomTemplates =null;
            duplicateCustomTemplates =(CustomTemplates) customConfirmationAndReminderTemplate.clone();
            duplicateCustomTemplates.setPageName(pageName);
            duplicateCustomTemplates.setId(null);
            duplicateCustomTemplates.setDefaultPage(false);
            duplicateCustomTemplates.setPageUpdated(false);
            if (PageType.EXTERNAL.equals(duplicateCustomTemplates.getPageType()) && !StringUtils.isBlank(pageUrl)){
                duplicateCustomTemplates.setPageUrl(pageUrl);
            }
            duplicateCustomTemplates = confirmationPagesRepository.save(duplicateCustomTemplates);
            if(!(TemplateType.INVOICE_PDF_DESIGN).equals(templateType))
            setTicketTypesForDuplicateCustomDesignId(allowedTicketTypes, duplicateCustomTemplates,templateType);
            log.info("Custom design duplicated from custom design id :{} for event: {} and duplicated by:{} and template type: {} ",confirmationPageId,event.getEventId(),user.getUserId(),templateType);
            return new ResponseDetailDto(SUCCESS,SUCCESS,duplicateCustomTemplates.getId());
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CUSTOM_DESIGN_NOT_FOUND);
        }
    }

    public void setTicketTypesForDuplicateCustomDesignId(List<Long> allowedTicketTypes, CustomTemplates duplicateCustomTemplates,TemplateType templateType) {
        if (!allowedTicketTypes.isEmpty() && duplicateCustomTemplates!=null){
            List<TicketingType> ticketingTypes=ticketingTypeRepository.findByIds(allowedTicketTypes);
            if((TemplateType.TICKET_PDF_DESIGN).equals(templateType)) {
                ticketingTypes.forEach(ticketingType -> {
                    ticketingType.setCustomTicketPdfDesignId(duplicateCustomTemplates.getId());
                });
            }
            if(!ObjectUtils.isEmpty(ticketingTypes))
            ticketingTypeRepository.saveAll(ticketingTypes);
            log.info("Setting allowed ticket types for duplicate custom design id for event: {} and number of ticketingTypes {} and template type: {} ",duplicateCustomTemplates.getEvent().getEventId(),ticketingTypes.size(),templateType);
        }
    }

    @Override
    public List<CustomTemplateDetailsDto> getCustomDesignList(Event event,TemplateType templateType) {
        return confirmationEmailServiceImpl.getAllCustomEmailTemplatesList(event,templateType);
    }

    @Override
    public String getDuplicateCustomDesignByCustomDesignId(Event event, Long pageId, User user,TemplateType templateType) {
        Optional<CustomTemplates> customTemplatesOptional=confirmationPagesRepository.findByIdAndTemplateType(pageId,templateType);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            return confirmationPagesService.getCopyPageName(customTemplates.getPageName().trim(),event,0,templateType);
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CUSTOM_DESIGN_NOT_FOUND);
        }
    }

    @Override
    public ResponseDto updateUnpublishedHtml(Event event, Long customDesignId, BeeFreePageDto beeFreePageDto,TemplateType templateType) {
        Optional<CustomTemplates> customTemplatesOptional=confirmationPagesRepository.findByIdAndTemplateType(customDesignId,templateType);
        if (customTemplatesOptional.isPresent() && beeFreePageDto!=null) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            customTemplates.setUnPublishedHtml(beeFreePageDto.getBeeFreeHtml());
            confirmationPagesRepository.save(customTemplates);
            log.info("Custom design unpublished html updated custom design id :{} for event: {} and template type: {} ",customDesignId,event.getEventId(),templateType);
        }
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @Override
    public CustomTemplatesDto getCustomDesignPreviewById(Event event, Long customDesignId,TemplateType  templateType) {
        log.info("Custom design preview request for the custom design id :{} for event: {} and template type: {} ",customDesignId,event.getEventId(),templateType);
        CustomTemplatesDto customTemplatesDto = confirmationPagesRepository.getConfirmationPageByPageIdAndEventIdAndTemplateType(customDesignId, event.getEventId(),templateType);
        if(customTemplatesDto == null) {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_PAGE_NOT_FOUND);
        }
        if(customTemplatesDto.getBeeFreeHtml() == null){
            String defaultHtml = "";
            if((TemplateType.TICKET_PDF_DESIGN).equals(templateType))
                defaultHtml = ""; //getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_TICKET_PDF_DESIGN_HTML));
            else
               log.info("template type not found for custom design preview");

            customTemplatesDto.setBeeFreeHtml(defaultHtml);
        }
        customTemplatesDto.setBeeFreeHtml(org.apache.commons.lang3.StringUtils.isBlank(customTemplatesDto.getUnPublishedHtml()) ? customTemplatesDto.getBeeFreeHtml() : customTemplatesDto.getUnPublishedHtml());
        return confirmationPagesService.replaceMergeTagsForHost(customTemplatesDto, event);
    }



    private void setAllTicketTypesForDefaultCustomDesign(Event event, CustomTemplates customTemplates,TemplateType templateType) {
        List<TicketingType> ticketingTypes=ticketingTypeRepository.findAllTicketTypeByEventAndDataType(event, DataType.TICKET);
        if((TemplateType.TICKET_PDF_DESIGN).equals(templateType)) {
            ticketingTypes.forEach(t -> t.setCustomTicketPdfDesignId(customTemplates.getId()));
            if(!CollectionUtils.isEmpty(ticketingTypes))
            ticketingTypeRepository.saveAll(ticketingTypes);
        }
        log.info("Setting all ticket types for default custom design for event: {} and number of ticketingTypes {} and templateType {}",event.getEventId(),ticketingTypes.size(),templateType);
    }

    @Transactional
    @Override
    public ResponseDto toggleActiveStatus(Event event, TemplateType templateType, Long customDesignId){
        if((TemplateType.INVOICE_PDF_DESIGN).equals(templateType)){
            Optional<CustomTemplates> customTemplates = confirmationPagesRepository.findByIdAndTemplateType(customDesignId,templateType);

            if (customTemplates.isEmpty())
                throw new NotFoundException(NotFoundException.NotFound.CUSTOM_DESIGN_NOT_FOUND);

                setDefaultPageToFalseForOtherCustomDesign(event, templateType);
                customTemplates.get().setDefaultPage(true);
                confirmationPagesRepository.save(customTemplates.get());
            }
            else
            log.info("template type not found for custom design");

        return new ResponseDto(SUCCESS,SUCCESS);
    }


}