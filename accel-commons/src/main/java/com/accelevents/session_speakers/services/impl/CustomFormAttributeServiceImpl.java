package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.CustomFormAttribute;
import com.accelevents.domain.session_speakers.CustomFormAttributeData;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.dto.KeyValueDto;
import com.accelevents.dto.NestedQuestionsDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.CustomFormAttributeRepository;
import com.accelevents.session_speakers.services.CustomFormAttributeDataRepository;
import com.accelevents.session_speakers.services.CustomFormAttributeService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.ticketing.dto.CustomAttribute;
import com.accelevents.ticketing.dto.CustomAttributeCommon;
import com.accelevents.ticketing.dto.DisplayAttributeDto;
import com.accelevents.ticketing.dto.DisplayNestedQueDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.NumberUtils;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.util.Streamable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;
import static java.util.stream.Collectors.toList;

@Service
public class CustomFormAttributeServiceImpl implements CustomFormAttributeService {

    private static final Logger log = LoggerFactory.getLogger(CustomFormAttributeServiceImpl.class);

    private final CustomFormAttributeRepository customFormAttributeRepository;
    private final TicketingHelperService ticketingHelperService;
    private final SpeakerService speakerService;
    private final CustomFormAttributeDataRepository customFormAttributeDataRepository;
    private final SessionService sessionService;
    private final TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;



    @Autowired
    public CustomFormAttributeServiceImpl(CustomFormAttributeRepository customFormAttributeRepository, TicketingHelperService ticketingHelperService, SpeakerService speakerService, CustomFormAttributeDataRepository customFormAttributeDataRepository,@Lazy SessionService sessionService, TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService) {
        this.customFormAttributeRepository = customFormAttributeRepository;
        this.ticketingHelperService = ticketingHelperService;
        this.speakerService = speakerService;
        this.customFormAttributeDataRepository = customFormAttributeDataRepository;
        this.sessionService = sessionService;
        this.ticketHolderRequiredAttributesService = ticketHolderRequiredAttributesService;
    }

    /***
     *
     * @param event
     * @param user
     * @param recurringEventId
     * @return List of Speaker attributes
     */

    @Override
    public List<CustomFormAttributeDTO> getCustomFormAttributes(Event event, User user, Long recurringEventId, AttributeType attributeType) {
        List<CustomFormAttributeDTO> customFormAttributes = new ArrayList<>();

        List<CustomFormAttribute> customFormAttributeList = getCustomFormAttributeOfEvent(event, user, recurringEventId,attributeType);
        customFormAttributeList.sort(Comparator.comparingInt(CustomFormAttribute::getOrder));

        if(AttributeType.SPEAKER.equals(attributeType)){
            List<Speaker> speakers = speakerService.getSpeakerByEventId(event);
            customFormAttributeList.forEach(customAttributes -> {
                CustomFormAttributeDTO customFormAttributeDTO = new CustomFormAttributeDTO(customAttributes);
                customFormAttributeDTO.setUsageCount(getCustomAttributeUsageCount(customAttributes, event, speakers,null,attributeType));
                customFormAttributes.add(customFormAttributeDTO);
            });
        }else if(AttributeType.SESSION.equals(attributeType)){
            List<Session> sessions = sessionService.getAllSessionByEvent(event);
            customFormAttributeList.forEach(customAttributes -> {
                CustomFormAttributeDTO customFormAttributeDTO = new CustomFormAttributeDTO(customAttributes);
                customFormAttributeDTO.setUsageCount(getCustomAttributeUsageCount(customAttributes, event, null,sessions,attributeType));
                customFormAttributes.add(customFormAttributeDTO);
            });
        }else if(AttributeType.CONTACT.equals(attributeType)){
            // For CONTACTLIST, we don't need to calculate usage count as it's for contact list attributes
            customFormAttributeList.forEach(customAttributes -> {
                CustomFormAttributeDTO customFormAttributeDTO = new CustomFormAttributeDTO(customAttributes);
                customFormAttributeDTO.setUsageCount(0); // Contact list attributes don't have usage count
                customFormAttributes.add(customFormAttributeDTO);
            });
        }

        return customFormAttributes;
    }

    @Override
    public List<CustomFormAttribute> getCustomFormAttributeOfEvent(Event event, User user, Long recurringEventId, AttributeType attributeType){
        Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());
        List<CustomFormAttribute> customAttributes;
        if(ticketing.isRecurringEvent() && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
            customAttributes = this.getAllCustomFormAttributesExcludingSubQueByRecurringEventId(event, recurringEventId,attributeType);
        }
        else{
            customAttributes = customFormAttributeRepository.findByEventIdAndRecurringEventIdNullAndAttributeType(event.getEventId(),attributeType);
        }

        if (CollectionUtils.isEmpty(customAttributes)){
            customAttributes = this.addDefaultAttributes(event, user, recurringEventId, null,attributeType);
        }
        return customAttributes.stream().filter(e-> !NumberUtils.isNumberGreaterThanZero(e.getParentQuestionId())).collect(Collectors.toList());
    }

    @Override
    public List<CustomFormAttribute> addDefaultAttributes(Event event, User user, Long recurringEventId, Long createdFromEventId, AttributeType attributeType) {
        log.info("request received to add default attributes for attributeType:{}, in event: {}", event.getEventId(),attributeType);
        List<CustomFormAttribute> customAttributes = null;
        if(AttributeType.SPEAKER.equals(attributeType)){
            customAttributes = List.of(
                    addDefaultAttributes(event, Constants.FIRST_NAME, AttributeValueType.TEXT, null, recurringEventId, 1000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.LAST_NAME, AttributeValueType.TEXT, null, recurringEventId, 2000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.EMAIL, AttributeValueType.TEXT, null, recurringEventId, 3000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.PRONOUNS, AttributeValueType.TEXT, null, recurringEventId, 4000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.TITLE, AttributeValueType.TEXT, null, recurringEventId, 5000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.COMPANY, AttributeValueType.TEXT, null, recurringEventId, 6000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.BIO, AttributeValueType.TEXT, null, recurringEventId, 7000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.LINKEDIN_URL, AttributeValueType.TEXT, null, recurringEventId, 8000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.INSTAGRAM_HANDLE, AttributeValueType.TEXT, null, recurringEventId, 9000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.TWITTER_HANDLE, AttributeValueType.TEXT, null, recurringEventId, 10000, createdFromEventId, attributeType, null, null)
            );
        }else if(AttributeType.SESSION.equals(attributeType)){
            customAttributes = List.of(
                    addDefaultAttributes(event, Constants.TITLE, AttributeValueType.TEXT, null, recurringEventId, 1000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.SHORT_DESCRIPTION, AttributeValueType.TEXT, null, recurringEventId, 2000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.FULL_DETAIL, AttributeValueType.TEXT, null, recurringEventId, 3000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.SESSION_TYPE, AttributeValueType.TEXT, null, recurringEventId, 4000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.LOCATION, AttributeValueType.TEXT, null, recurringEventId, 5000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.START_DATE_TIME, AttributeValueType.DATE, null, recurringEventId, 6000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.END_DATE_TIME, AttributeValueType.DATE, null, recurringEventId, 7000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.CAPACITY, AttributeValueType.NUMBER, null, recurringEventId, 8000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.TAGS, AttributeValueType.MULTIPLE_CHOICE, null, recurringEventId, 9000, createdFromEventId, attributeType, null, null),
                    addDefaultAttributes(event, Constants.TRACKS, AttributeValueType.MULTIPLE_CHOICE, null, recurringEventId, 10000, createdFromEventId, attributeType, null, null)
            );
        }else if(AttributeType.CONTACT.equals(attributeType)){
            customAttributes = List.of(
                    addDefaultAttributes(event, Constants.FIRST_NAME, AttributeValueType.TEXT, null, recurringEventId, 1000, createdFromEventId, AttributeType.CONTACT, true, true),
                    addDefaultAttributes(event, Constants.LAST_NAME, AttributeValueType.TEXT, null, recurringEventId, 2000, createdFromEventId, AttributeType.CONTACT, true, true),
                    addDefaultAttributes(event, Constants.EMAIL, AttributeValueType.EMAIL, null, recurringEventId, 4000, createdFromEventId, AttributeType.CONTACT, true, true),
                    addDefaultAttributes(event, Constants.PHONE, AttributeValueType.PHONE_NUMBER, null, recurringEventId, 5000, createdFromEventId, AttributeType.CONTACT, true, true)
            );
        }

        return Streamable.of(customFormAttributeRepository.saveAll(customAttributes)).toList();
    }

    @Override
    public List<CustomFormAttribute> getAllCustomFormAttributesExcludingSubQueByRecurringEventId(Event event, Long recurringEventId, AttributeType attributeType) {
        List<CustomFormAttribute> listOfAttr =  customFormAttributeRepository.findAllAttributesByRecurringEventIdAndAttributeType(event.getEventId(), recurringEventId,attributeType);
        return listOfAttr.stream().filter(e-> !NumberUtils.isNumberGreaterThanZero(e.getParentQuestionId())).collect(Collectors.toList());
    }

    /***
     * Create Speaker Custom Attribute
     * @param event
     * @param user
     * @param recurringEventId
     * @param customFormCustomAttributeDto
     */

    @Override
    @Transactional
    public void createCustomFormAttribute(Event event, User user, Long recurringEventId, CustomFormCustomAttributeDto customFormCustomAttributeDto) {

        boolean customAttributeConflict = isConflictingWithReservedAttributes(customFormCustomAttributeDto.getCustomAttributeCommons());
        if(customAttributeConflict) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.RESERVED_SESSION_ATTRIBUTE_NAME);
        }

        boolean isConditionalQuestion = AttributeValueType.CONDITIONAL_QUE.equals(customFormCustomAttributeDto.getAttributeValueType());
        if(isConditionalQuestion){
            validateConditionalQuestion(customFormCustomAttributeDto);
        }

        Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());
        boolean isRecurring = ticketing.isRecurringEvent();

        List<CustomAttributeCommon> customAttributeCommons = customFormCustomAttributeDto.getCustomAttributeCommons();

        Map<Long, Long> parentAndSubQuestionMap = new HashMap<>();
        List<CustomFormAttribute> customFormAttributes = new ArrayList<>();
        List<CustomFormAttribute> allCreatedAttributes = new ArrayList<>();

        for (CustomAttributeCommon customAttribute : customAttributeCommons) {
            CustomFormAttribute existingCustomAttributes = createCommonAttribute(event, customFormCustomAttributeDto, recurringEventId, isRecurring, customAttribute);
            if (isConditionalQuestion) {
                parentAndSubQuestionMap.put(customAttribute.getId(), existingCustomAttributes.getId());
                customFormAttributes.add(existingCustomAttributes);
            }
            allCreatedAttributes.add(existingCustomAttributes);
        }

        if (isConditionalQuestion) {
            customFormAttributes.forEach(e -> e.setParentQuestionId(null != parentAndSubQuestionMap.get(e.getParentQuestionId()) ? parentAndSubQuestionMap.get(e.getParentQuestionId()) : 0L));
        }

        customFormAttributeRepository.saveAll(customFormAttributes);
        // After saving all custom form attributes, update existing JSON records for CONTACTS only
        if (AttributeType.CONTACT.equals(customFormCustomAttributeDto.getAttributeType())) {
            for (CustomFormAttribute createdAttribute : allCreatedAttributes) {
                updateExistingContactAttributeDataJson(event, createdAttribute);
            }
        }
    }

    public static boolean isConflictingWithReservedAttributes(List<CustomAttributeCommon> customAttributes) {
        List<String> customAttributesName = customAttributes
                .stream().map(attr -> attr.getAttributeName().toLowerCase(Locale.ROOT)).collect(toList());
        List<String> reservedColumns = List.of(PRIMARY_SESSIONS.toLowerCase(Locale.ROOT), SECONDARY_SESSIONS.toLowerCase(Locale.ROOT), SPEAKER_SPACE_ID.toLowerCase(Locale.ROOT));
        boolean customAttributeConflict = customAttributesName.stream()
                .anyMatch(reservedColumns::contains);
        return customAttributeConflict;
    }

    private CustomFormAttribute createCommonAttribute(Event event, CustomFormCustomAttributeDto customAttributeDto, Long recurringEventId, boolean isRecurring, CustomAttributeCommon customAttributeCommon) {
        String customAttributeName = customAttributeCommon.getAttributeName().trim().replaceAll(" +", " ");
        CustomFormAttribute customFormAttribute;
        if(isRecurring && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
            customFormAttribute = createCustomAttributesForRecurring(event, customAttributeDto, recurringEventId, customAttributeCommon, customAttributeName);
        }
        else{
            customFormAttribute = updateCustomAttributesForNonRecurringAndSpecificRecEvent(event, customAttributeDto, recurringEventId, customAttributeCommon, isRecurring, customAttributeName);
        }
        return customFormAttribute;
    }

    private CustomFormAttribute createCustomAttributesForRecurring(Event event, CustomFormCustomAttributeDto customAttributeDto, Long recurringEventId, CustomAttributeCommon customAttributeCommon, String customAttributeName) {
        CustomFormAttribute customFormAttribute;
        List<CustomFormAttribute> attributes = customFormAttributeRepository.findByNameAndEventAndRecurringEventIdAndAttributeType(customAttributeName, event,  recurringEventId,customAttributeDto.getAttributeType());
        if (!CollectionUtils.isEmpty(attributes) && AttributeType.SPEAKER.equals(customAttributeDto.getAttributeType())) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_EXISTS);
        }else if(!CollectionUtils.isEmpty(attributes) && AttributeType.SESSION.equals(customAttributeDto.getAttributeType())){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_ATTRIBUTE_EXISTS);
        }else if(!CollectionUtils.isEmpty(attributes) && AttributeType.CONTACT.equals(customAttributeDto.getAttributeType())){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CONTACT_ATTRIBUTE_EXISTS);
        }else{
            customFormAttribute = saveAttribute(event, customAttributeCommon, customAttributeDto, recurringEventId, true, customAttributeName);
        }
        return customFormAttribute;
    }

    private CustomFormAttribute updateCustomAttributesForNonRecurringAndSpecificRecEvent(Event event, CustomFormCustomAttributeDto customFormCustomAttributeDto, Long recurringEventId, CustomAttributeCommon customAttributeCommon, boolean isRecurring, String customAttributeName) {
        CustomFormAttribute customFormAttribute;
        if(!AttributeType.SPEAKER.equals(customFormCustomAttributeDto.getAttributeType()) && !AttributeType.SESSION.equals(customFormCustomAttributeDto.getAttributeType()) && !AttributeType.CONTACT.equals(customFormCustomAttributeDto.getAttributeType())){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.ATTRIBUTE_TYPE_INVALID);
        }

        List<CustomFormAttribute> attributes = customFormAttributeRepository.findByNameAndEventAndRecurringEventIdNullAndAttributeType(customAttributeName, event, customFormCustomAttributeDto.getAttributeType());
        if (!CollectionUtils.isEmpty(attributes) && AttributeType.SPEAKER.equals(customFormCustomAttributeDto.getAttributeType())) {
            if (isRecurring) {
                updateAttributeForRecurringEventWithDefault(event, customAttributeName, customFormCustomAttributeDto.getAttributeType());
            }
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_EXISTS);
        }else if(!CollectionUtils.isEmpty(attributes) && AttributeType.SESSION.equals(customFormCustomAttributeDto.getAttributeType())){
            if (isRecurring) {
                updateAttributeForRecurringEventWithDefault(event, customAttributeName, customFormCustomAttributeDto.getAttributeType());
            }
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_ATTRIBUTE_EXISTS);
        }else if(!CollectionUtils.isEmpty(attributes) && AttributeType.CONTACT.equals(customFormCustomAttributeDto.getAttributeType())){
            if (isRecurring) {
                updateAttributeForRecurringEventWithDefault(event, customAttributeName, customFormCustomAttributeDto.getAttributeType());
            }
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CONTACT_ATTRIBUTE_EXISTS);
        }
        else{
            customFormAttribute = saveAttribute(event, customAttributeCommon, customFormCustomAttributeDto, recurringEventId, isRecurring, customAttributeName);
        }
        return customFormAttribute;
    }



    private void updateAttributeForRecurringEventWithDefault(Event event, String customAttributeName,AttributeType attributeType) {
        List<CustomFormAttribute> attributes = customFormAttributeRepository.findByEventAndRecurringEventIdNotNullAndAttributeType(event.getEventId(), customAttributeName,attributeType);
        attributes = attributes.stream().filter(e -> e.getCreatedFrom() != -1).collect(Collectors.toList());

        attributes.forEach(attribute -> attribute.setCreatedFrom(-1L));

        customFormAttributeRepository.saveAll(attributes);
    }

    private CustomFormAttribute saveAttribute(Event event, CustomAttributeCommon customAttributeCommon, CustomFormCustomAttributeDto customAttributeDto, Long recurringEventId, boolean isRecurringEvent, String customAttributeName) {
        int highestBuyerAttributePosition = findHighestAttributePosition(event,customAttributeDto.getAttributeType()) + 1000;
        CustomFormAttribute customFormAttribute = new CustomFormAttribute();
        customFormAttribute.setOrder(highestBuyerAttributePosition);
        customFormAttribute.setAttributeValueType(customAttributeDto.getAttributeValueType());
        customFormAttribute.setAttributeType(customAttributeDto.getAttributeType());
        customFormAttribute.setEvent(event);
        customFormAttribute.setEventId(event.getEventId());
        customFormAttribute.setName(customAttributeName);
        customFormAttribute.setEnabled(customAttributeDto.isEnabled());
        customFormAttribute.setRequired(customAttributeDto.isRequired());
        customFormAttribute.setAttribute(customAttributeDto.isAttribute());

        customFormAttribute.setDefaultValue(customAttributeCommon.getDefaultValueJson());
        customFormAttribute.setSelectedAnswer(customAttributeCommon.getSelectedAnsId());
        customFormAttribute.setParentQuestionId(customAttributeCommon.getParentQuestionId());


        if(isRecurringEvent && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
            customFormAttribute.setRecurringEventId(recurringEventId);
        }

        return customFormAttributeRepository.save(customFormAttribute);
    }

    /***
     * Get a particular Custom Attribute
     * @param attributeId
     * @param event
     * @return
     */
    @Override
    public List<CustomAttribute> getCustomAttribute(Long attributeId, Event event,AttributeType attributeType) {
        Optional<CustomFormAttribute> optAttributes = customFormAttributeRepository
                .findById(attributeId);
        if (optAttributes.isPresent()) {

            CustomFormAttribute parentCustomAttribute = optAttributes.get();
            List<CustomAttribute> queList = new ArrayList<>();
            queList.add(mapAttributesToDto(parentCustomAttribute));

            if (AttributeValueType.CONDITIONAL_QUE.equals(parentCustomAttribute.getAttributeValueType())) {
                List<CustomFormAttribute> subQueListFinal = getCustomAttributesForConditionalQuestions(parentCustomAttribute.getId(), event,attributeType);

                subQueListFinal.forEach(attribute -> queList.add(mapAttributesToDto(attribute)));
            }
            return queList;
        } else {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CUSTOM_ATTRIBUTE_NOT_EXISTS);
        }
    }

    public List<CustomFormAttribute> getCustomAttributesForConditionalQuestions(long attributeId, Event event, AttributeType attributeType) {
        List<CustomFormAttribute> subQuestions = new ArrayList<>();
        List<Long> listDb = new ArrayList<>();
        listDb.add(attributeId);
        do {
            List<CustomFormAttribute> subQueList = customFormAttributeRepository.findAllSubQueByParentIdsAndEventIdAndAttributeType(event.getEventId(), listDb,attributeType);
            subQuestions.addAll(subQueList);
            listDb = subQueList.stream().map(CustomFormAttribute::getId).collect(toList());

        } while (!CollectionUtils.isEmpty(listDb));
        return subQuestions;
    }

    private CustomAttribute mapAttributesToDto(CustomFormAttribute customFormAttribute) {
        CustomAttribute customAttribute = new CustomAttribute();
        customAttribute.setId(customFormAttribute.getId());
        customAttribute.setAtrributeName(customFormAttribute.getName());
        customAttribute.setAttribute(customFormAttribute.isAttribute());
        customAttribute.setAttributeFormType (customFormAttribute.getAttributeType());
        customAttribute.setAttributeType(customFormAttribute.getAttributeValueType());
        customAttribute.setDefaultValue(customFormAttribute.getDefaultValue());
        customAttribute.setParentQuestionId(null != customFormAttribute.getParentQuestionId() ? customFormAttribute.getParentQuestionId() : 0L);
        customAttribute.setSelectedAnswer(customFormAttribute.getSelectedAnswer());
        return customAttribute;
    }

    /***
     * Update a Custom Attribute
     * @param attributeId
     * @param customFormCustomAttributeDto
     * @param event
     * @param recurringEventId
     */

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void updateCustomAttribute(Long attributeId, CustomFormCustomAttributeDto customFormCustomAttributeDto, Event event, Long recurringEventId) {
        log.info("updateCustomCustomAttribute | eventId {} | recurringEventId {}", event.getEventId(),recurringEventId);

        boolean customAttributeConflict = isConflictingWithReservedAttributes(customFormCustomAttributeDto.getCustomAttributeCommons());
        if(customAttributeConflict) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.RESERVED_SESSION_ATTRIBUTE_NAME);
        }

        if(AttributeValueType.CONDITIONAL_QUE.equals(customFormCustomAttributeDto.getAttributeValueType())){
            validateConditionalQuestion(customFormCustomAttributeDto);
        }

        Optional<CustomFormAttribute> optAttributes = customFormAttributeRepository
                .findById(attributeId);

        if(optAttributes.isEmpty()){
            if(AttributeType.SPEAKER.equals(customFormCustomAttributeDto.getAttributeType())){
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_NOT_EXISTS);
            }else if(AttributeType.SESSION.equals(customFormCustomAttributeDto.getAttributeType())){
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_ATTRIBUTE_NOT_EXISTS);
            }else{
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CUSTOM_ATTRIBUTE_NOT_EXISTS);
            }

        }

        CustomFormAttribute attribute = optAttributes.get();

        if(!customFormCustomAttributeDto.getAttributeValueType().equals(attribute.getAttributeValueType())
                && AttributeValueType.CONDITIONAL_QUE.equals(customFormCustomAttributeDto.getAttributeValueType())){ //NOSONAR
            deleteSubQuestion(attribute, event, customFormCustomAttributeDto.getAttributeType());
        }

        boolean isConditionalQue = AttributeValueType.CONDITIONAL_QUE.equals(customFormCustomAttributeDto.getAttributeValueType());
        List<CustomAttributeCommon> customAttributeCommons = customFormCustomAttributeDto.getCustomAttributeCommons();
        Map<Long, Long> mapOldAndNewParentQueId = new HashMap<>();
        List<CustomFormAttribute> attributesList = new ArrayList<>();


        boolean isRecurringEvent = ticketingHelperService.findTicketingByEvent(event).isRecurringEvent();

        for (CustomAttributeCommon attributeCommon : customAttributeCommons){

            log.info("updateCustomAttribute | eventId {} | CustomAttributeCommon {} | AttributeType {}",event.getEventId(), attributeCommon, customFormCustomAttributeDto.getAttributeType());
            CustomFormAttribute attributeDb = updateCustomAttribute(attributeCommon.getId(), attributeCommon,
                    customFormCustomAttributeDto, event, recurringEventId, isNumberGreaterThanZero(recurringEventId));

            if(isConditionalQue){
                mapOldAndNewParentQueId.put(attributeCommon.getId(), attributeDb.getId());
                attributesList.add(attributeDb);
            }

            //when we update anything for default questions then that same change should reflect to all que of rec ids which are created from that.
            updateCustomAttributeForRecurringEvents(customFormCustomAttributeDto, event, recurringEventId, isRecurringEvent, attributeCommon);
        }

        if(isConditionalQue){
            attributesList.forEach(e-> e.setParentQuestionId(null != mapOldAndNewParentQueId.get(e.getParentQuestionId())
                    ? mapOldAndNewParentQueId.get(e.getParentQuestionId()) : 0L));
        }
        customFormAttributeRepository.saveAll(attributesList);
    }

    private void updateCustomAttributeForRecurringEvents(CustomFormCustomAttributeDto customFormCustomAttributeDto, Event event, Long recurringEventId,
                                                         boolean isRecurringEvent, CustomAttributeCommon attributeCommon) {
        if (isRecurringEvent && !isNumberGreaterThanZero(recurringEventId)) {
            List<CustomFormAttribute> customAttributes = customFormAttributeRepository.findAllAttributesByCreatedFromAndAttributeTypeAndEventId(attributeCommon.getId(), event.getEventId(), customFormCustomAttributeDto.getAttributeType()); // get all but only match with name
            for (CustomFormAttribute attributes : customAttributes) {
                updateCustomAttribute(attributes, attributeCommon, customFormCustomAttributeDto, event);
            }
        }
    }

    private CustomFormAttribute updateCustomAttribute(Long attributeId, CustomAttributeCommon customAttributeCommon,
                                                      CustomFormCustomAttributeDto customFormCustomAttributeDto,
                                                      Event event, Long recurringEventId, boolean isRecursiveEvent) {

        if(!customAttributeCommon.isNewSubQue()){
            Optional<CustomFormAttribute> optAttributes = customFormAttributeRepository.findById(attributeId);
            if(optAttributes.isPresent()){
                CustomFormAttribute attribute = optAttributes.get();
                if(!attribute.getName().equalsIgnoreCase(customAttributeCommon.getAttributeName())){
                    CustomFormAttribute attributes = null;
                    if(isRecursiveEvent && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
                        attributes = customFormAttributeRepository.findByNameAndEventIdAndRecurringEventIdAndAttributeType(customAttributeCommon.getAttributeName(), event.getEventId(), recurringEventId, customFormCustomAttributeDto.getAttributeType());
                    }
                    else{
                        List<CustomFormAttribute> attributeList = customFormAttributeRepository.findByNameAndEventAndRecurringEventIdNullAndAttributeType(customAttributeCommon.getAttributeName(), event, customFormCustomAttributeDto.getAttributeType());
                        if(!CollectionUtils.isEmpty(attributeList)){
                            attributes = attributeList.get(0);
                        }
                    }
                    if(attributes != null){
                        if(AttributeType.SPEAKER.equals(customFormCustomAttributeDto.getAttributeType())){
                            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_EXISTS);
                        }else if(AttributeType.SESSION.equals(customFormCustomAttributeDto.getAttributeType())){
                            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_ATTRIBUTE_EXISTS);
                        }else{
                            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CONTACT_ATTRIBUTE_EXISTS);
                        }

                    }
                }
                updateCustomTicketAttribute(attribute,customAttributeCommon, customFormCustomAttributeDto, event, isRecursiveEvent);
                return attribute;
            }else {
                if(AttributeType.SPEAKER.equals(customFormCustomAttributeDto.getAttributeType())){
                    throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_NOT_EXISTS);
                }else if(AttributeType.SESSION.equals(customFormCustomAttributeDto.getAttributeType())){
                    throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_ATTRIBUTE_NOT_EXISTS);
                }else{
                    throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CUSTOM_ATTRIBUTE_NOT_EXISTS);
                }

            }
        }else {
            CustomFormAttribute attributes = customFormAttributeRepository.findByNameAndEventIdAndRecurringEventIdAndAttributeType(customAttributeCommon.getAttributeName(), event.getEventId(),  recurringEventId, customFormCustomAttributeDto.getAttributeType());

            if(attributes != null){
                if(AttributeType.SPEAKER.equals(customFormCustomAttributeDto.getAttributeType())){
                    throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_EXISTS);
                }else if(AttributeType.SESSION.equals(customFormCustomAttributeDto.getAttributeType())){
                    throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_ATTRIBUTE_EXISTS);
                }else{
                    throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CONTACT_ATTRIBUTE_EXISTS);
                }
            }
            attributes = new CustomFormAttribute();

            int highestBuyerAttributePosition = findHighestAttributePosition(event, customFormCustomAttributeDto.getAttributeType()) + 1000;
            attributes.setOrder(highestBuyerAttributePosition);
            attributes.setName(customAttributeCommon.getAttributeName());
            attributes.setAttributeValueType(customFormCustomAttributeDto.getAttributeValueType());
            if(isNumberGreaterThanZero(recurringEventId)){
                attributes.setRecurringEventId(recurringEventId);
            }
            updateCustomTicketAttribute(attributes,customAttributeCommon, customFormCustomAttributeDto, event, isRecursiveEvent);
            return attributes;
        }
    }

    private void deleteSubQuestion(CustomFormAttribute attribute, Event event, AttributeType attributeType) {
        log.info("deleteSubQuestion eventId {} attributeId {}",attribute.getEventId(),attribute.getId());
        List<CustomFormAttribute> subQueList = customFormAttributeRepository.findAllSubQueByParentIdsAndEventIdAndAttributeType(event.getEventId(), List.of(attribute.getId()),attributeType);
        if (!CollectionUtils.isEmpty(subQueList)) {
            subQueList.forEach(e -> {

                customFormAttributeRepository.deleteById(e.getId());
                deleteSubQuestion(e, event,attributeType);
            });
        }
    }

    private void updateCustomAttribute(CustomFormAttribute customFormAttribute,
                                       CustomAttributeCommon attributeCommon, CustomFormCustomAttributeDto customFormCustomAttributeDto,
                                       Event event) {

        updateCustomAttribute(attributeCommon, customFormCustomAttributeDto, event, customFormAttribute, false);
    }

    public void updateCustomTicketAttribute(CustomFormAttribute customFormAttribute, CustomAttributeCommon customAttributeCommon,
                                            CustomFormCustomAttributeDto customFormCustomAttributeDto, Event event, boolean isRecursiveEvent) {
        updateCustomAttribute(customAttributeCommon, customFormCustomAttributeDto, event, customFormAttribute, isRecursiveEvent);
    }

    private void updateCustomAttribute(CustomAttributeCommon customAttributeCommon, CustomFormCustomAttributeDto customFormCustomAttributeDto, Event event,
                                       CustomFormAttribute customFormAttribute, boolean isRecursiveEvent) {
        String oldName = customFormAttribute.getName();

        // Validation: Prevent updating default CONTACT fields (First Name, Last Name, Email)
        if (AttributeType.CONTACT.equals(customFormCustomAttributeDto.getAttributeType()) &&
            (Constants.FIRST_NAME.equals(oldName) || Constants.LAST_NAME.equals(oldName) || Constants.EMAIL.equals(oldName))) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CONTACT_DEFAULT_ATTRIBUTE_UPDATE_NOT_ALLOWED);
        }

        customFormAttribute.setAttribute(customFormCustomAttributeDto.isAttribute());
        customFormAttribute.setAttributeValueType(customFormCustomAttributeDto.getAttributeValueType());
        customFormAttribute.setDefaultValue(customAttributeCommon.getDefaultValueJson());
        customFormAttribute.setParentQuestionId(customAttributeCommon.getParentQuestionId());
        customFormAttribute.setSelectedAnswer(customAttributeCommon.getSelectedAnsId());
        customFormAttribute.setEnabled(customFormCustomAttributeDto.isEnabled());
        customFormAttribute.setRequired(customFormCustomAttributeDto.isRequired());
        customFormAttribute.setEvent(event);
        customFormAttribute.setEventId(event.getEventId());
        customFormAttribute.setName(customAttributeCommon.getAttributeName());
        customFormAttribute.setAttributeType(customFormCustomAttributeDto.getAttributeType());

        if(isRecursiveEvent){
            customFormAttribute.setCreatedFrom(-1L);
        }
        log.info("updateCustomAttributes | CustomFormAttribute before save {}", customFormAttribute);
        customFormAttributeRepository.save(customFormAttribute);

        updateSpeakerCustomAttributeValue(oldName, customAttributeCommon.getAttributeName(), event, customFormAttribute);
    }

    public void updateSpeakerCustomAttributeValue(String jsonOldName, String jsonNewName, Event event, CustomFormAttribute customFormAttribute) {

        if(!customFormAttribute.getAttributeValueType().equals(AttributeValueType.CONDITIONAL_QUE)){
            jsonOldName = "\"" + jsonOldName.trim() + "\"" + ":";
            jsonNewName = "\""+jsonNewName+"\""+":";
        }else {
            jsonOldName = "\"name\":\""+jsonOldName+"\"";
            jsonNewName = "\"name\":\""+jsonNewName+"\"";

        }
        if(AttributeType.SPEAKER.equals(customFormAttribute.getAttributeType())){
            customFormAttributeDataRepository.updateAttributeForSpeaker(jsonOldName, event.getEventId(), jsonNewName);
        }
        else if(AttributeType.SESSION.equals(customFormAttribute.getAttributeType())){
            customFormAttributeDataRepository.updateAttributeForSession(jsonOldName, event.getEventId(), jsonNewName);
        }

    }

    /***
     * Delete a Particular speaker attribute
     * @param attributeId
     * @param recurringEventId
     * @param event
     */
    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void deleteCustomFormAttribute(long attributeId, Long recurringEventId, Event event) {
        Optional<CustomFormAttribute> customFormAttributeOptional = customFormAttributeRepository.findById(attributeId);
        if(customFormAttributeOptional.isEmpty()){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_NOT_EXISTS);
        }

        deleteCustomFormAttribute(customFormAttributeOptional.get(), recurringEventId, event);
    }

    private void deleteCustomFormAttribute(CustomFormAttribute customFormAttribute, Long recurringEventId, Event event) {
        List<CustomFormAttribute> recurringAttributes = new ArrayList<>();
        if(!isNumberGreaterThanZero(recurringEventId)) {
            recurringAttributes = customFormAttributeRepository.findByCreatedFromIdAndEventId(customFormAttribute.getId(), event.getEventId());
        }

        processForDeleteCustomFormAttribute(customFormAttribute,recurringAttributes);
    }

    private void processForDeleteCustomFormAttribute(CustomFormAttribute customFormAttribute, List<CustomFormAttribute> recurringAttributes) {
        log.info("processForDeleteSubQuestion eventId {} attributeId {}",customFormAttribute.getEvent(),customFormAttribute.getId());

        recurringAttributes.add(customFormAttribute);
        recurringAttributes.forEach(e->e.setStatus(RecordStatus.DELETE));
        customFormAttributeRepository.saveAll(recurringAttributes);
    }

    /***
     *  Get Count of a attribute in speaker form
     * @param attributeId
     * @param event
     * @return
     */

    @Override
    public int getCustomAttributeUsageCount(Long attributeId, Event event,AttributeType attributeType) {
        Optional<CustomFormAttribute> optAttributes = customFormAttributeRepository
                .findById(attributeId);
        if (optAttributes.isEmpty()) {
            if(AttributeType.SPEAKER.equals(attributeType)){
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ATTRIBUTE_NOT_EXISTS);
            }
            else if(AttributeType.SESSION.equals(attributeType)){
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SESSION_ATTRIBUTE_NOT_EXISTS);
            }
            else{
                throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CUSTOM_ATTRIBUTE_NOT_EXISTS);
            }

        }

        if(AttributeType.SPEAKER.equals(attributeType)){
            List<Speaker> speakers = speakerService.getSpeakerByEventId(event);
            return getCustomAttributeUsageCount(optAttributes.get(), event, speakers,null,attributeType);
        } else if(AttributeType.SESSION.equals(attributeType)){
            List<Session> sessions = sessionService.getAllSessionByEvent(event);
            return getCustomAttributeUsageCount(optAttributes.get(), event, null,sessions,attributeType);
        }
        else{
            return 0;
        }

    }

    private int getCustomAttributeUsageCount(CustomFormAttribute customFormAttribute, Event event, List<Speaker> speakers, List<Session> sessions, AttributeType attributeType){
        String attributeName = customFormAttribute.getName();
        AtomicInteger count = new AtomicInteger(0);
        if(!Objects.isNull(speakers) && AttributeType.SPEAKER.equals(attributeType)){
            for(Speaker speaker: speakers){
                CustomAttributesResponseDto customAttributeResponseDto = getCustomAttributeResponseDto(speaker.getSpeakerAttribute());
                if(!CollectionUtils.isEmpty(customAttributeResponseDto.getAttributes())){
                    customAttributeResponseDto.getAttributes().entrySet().stream().filter(e -> attributeName.equals(e.getKey()) && StringUtils.isNotEmpty(e.getValue())).findFirst().ifPresent(e->count.incrementAndGet());
                }
            }
        }else{
            for(Session session: sessions){
                if(!Objects.isNull(session.getSessionAttribute())){
                    CustomAttributesResponseDto customAttributeResponseDto = getCustomAttributeResponseDto(session.getSessionAttribute());
                    if(!CollectionUtils.isEmpty(customAttributeResponseDto.getAttributes())){
                        customAttributeResponseDto.getAttributes().entrySet().stream().filter(e -> attributeName.equals(e.getKey()) && StringUtils.isNotEmpty(e.getValue())).findFirst().ifPresent(e->count.incrementAndGet());
                    }
                }

            }
        }
        return count.get();
    }

    @Override
    public int findHighestAttributePosition(Event event,AttributeType attributeType) {

        Integer order = customFormAttributeRepository.findFirstByEventAndAttributeTypeOrderByOrderDesc(event.getEventId(),attributeType);
        if (NumberUtils.isNumberGreaterThanZero(order)) {
            return order;
        }
        return 0;
    }

    private void validateConditionalQuestion(CustomFormCustomAttributeDto customFormCustomAttributeDto) {
        List<CustomAttributeCommon> customAttributeCommons = customFormCustomAttributeDto.getCustomAttributeCommons();
        Long parentQuestionIdCount = customAttributeCommons.stream().filter(e -> (!isNumberGreaterThanZero(e.getParentQuestionId()))).count();
        if (!isNumberGreaterThanZero(parentQuestionIdCount)) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.PARENT_QUESTION_NOT_FOUND);
        }
        Long selectedAnsId = customAttributeCommons.stream().filter(e -> (!isNumberGreaterThanZero(e.getSelectedAnsId()))).count();
        if (!isNumberGreaterThanZero(selectedAnsId)) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.CONDITIONAL_QUESTION_SELECTED_ANS_ID);
        }
        List<CustomAttributeCommon> parentQuestionId = customAttributeCommons.stream().filter(e -> (e.getParentQuestionId().equals(0L) && e.getSelectedAnsId().equals(0L))).collect(Collectors.toList());

        List<CustomAttributeCommon> subQuestionList = customAttributeCommons.stream().filter(e -> e.getParentQuestionId().equals(parentQuestionId.get(0).getId())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(subQuestionList) && subQuestionList.isEmpty()) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.MINIMUM_ONE_SUB_QUESTION_NEEDED);
        }//nosonar
    }

    /**
     * Unified method to create default attributes for all types (SPEAKER, SESSION, CONTACT)
     * @param event The event
     * @param name Attribute name
     * @param attributeValueType Type of attribute value
     * @param defaultValue Default value (optional)
     * @param recurringEventId Recurring event ID (optional)
     * @param order Display order
     * @param createdFrom Created from event ID (optional)
     * @param attributeType Type of attribute (SPEAKER, SESSION, or CONTACT)
     * @param enabled Whether attribute is enabled (used for CONTACT type)
     * @param required Whether attribute is required (used for CONTACT type)
     * @return CustomFormAttribute instance
     */
    private CustomFormAttribute addDefaultAttributes(Event event, String name, AttributeValueType attributeValueType,
                                                   String defaultValue, Long recurringEventId, int order,
                                                   Long createdFrom, AttributeType attributeType,
                                                   Boolean enabled, Boolean required) {
        CustomFormAttribute customFormAttribute = new CustomFormAttribute();
        customFormAttribute.setEventId(event.getEventId());
        customFormAttribute.setName(name);
        customFormAttribute.setAttributeValueType(attributeValueType);
        customFormAttribute.setAttribute(true);
        customFormAttribute.setDefaultAttribute(true);

        // Set type-specific properties
        if (AttributeType.CONTACT.equals(attributeType)) {
            customFormAttribute.setAttributeType(AttributeType.CONTACT);
            customFormAttribute.setEnabled(enabled != null ? enabled : true);
            customFormAttribute.setRequired(required != null ? required : false);
        } else {
            // For SPEAKER and SESSION types
            customFormAttribute.setAttributeType(attributeType);
            customFormAttribute.setEnabled(true);
            customFormAttribute.setRequired(false);
        }

        if(StringUtils.isNotEmpty(defaultValue)){
            customFormAttribute.setDefaultValue(defaultValue);
        }
        if(NumberUtils.isNumberGreaterThanZero(recurringEventId)) {
            customFormAttribute.setRecurringEventId(recurringEventId);
        }
        customFormAttribute.setOrder(order);

        if(NumberUtils.isNumberGreaterThanZero(createdFrom)) {
            customFormAttribute.setCreatedFrom(createdFrom);
        }
        return customFormAttribute;
    }

    @Override
    public List<CustomFormAttributeMappingDto> getSessionRegistrationAttributeMappingList(Event event, Long recurringEventId, AttributeType attributeType) {
        List<CustomFormAttributeMappingDto> customFormAttributeMappingDtoList;

        // For CONTACTLIST, return empty list as it doesn't have registration attribute mapping
        if(AttributeType.CONTACT.equals(attributeType)){
            return new ArrayList<>();
        }

        RegistrationAttributeType registrationAttributeType = (AttributeType.SESSION.equals(attributeType)) ? RegistrationAttributeType.SPEAKER_DETAILS :RegistrationAttributeType.SPEAKER_INFO;
        Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());
        List<AttributeValueType> attributeValueTypeList = (AttributeType.SPEAKER.equals(attributeType)) ? Arrays.asList(AttributeValueType.TEXT, AttributeValueType.LONG_TEXT)
                : Arrays.asList(AttributeValueType.TEXT, AttributeValueType.LONG_TEXT,AttributeValueType.DROPDOWN,AttributeValueType.NUMBER,AttributeValueType.DATE,AttributeValueType.MULTIPLE_CHOICE,AttributeValueType.IMAGE);

        if (ticketing.isRecurringEvent() && NumberUtils.isNumberGreaterThanZero(recurringEventId)) {
            customFormAttributeMappingDtoList = customFormAttributeRepository.findByEventIdAndRecurringEventIdAndEnabledTrueAndAttributeValueTypeIn(event.getEventId(), recurringEventId, attributeValueTypeList,attributeType,registrationAttributeType);
        } else {
            customFormAttributeMappingDtoList = customFormAttributeRepository.findByEventIdAndRecurringEventIdNullAndEnabledTrueAndAttributeValueTypeIn(event.getEventId(), attributeValueTypeList,attributeType,registrationAttributeType);
        }
        return customFormAttributeMappingDtoList;
    }

    @Override
    public List<CustomFormAttribute> getSpeakerEnabledAttributesOrderByAttributeOrder(Event event, Long recurringEventId) {
        return (isNumberGreaterThanZero(recurringEventId)) ?
                customFormAttributeRepository.findAllCustomEnabledAttributesByRecurringEventIdOrderByOrder(event.getEventId(), recurringEventId,AttributeType.SPEAKER) :
                customFormAttributeRepository.findAllCustomEnabledAttributesByEventIdAndRecurringEventIdNullOrderByOrder(event.getEventId(),AttributeType.SPEAKER);
    }

    @Override
    public List<KeyValueDto> getSpeakerAttributeByEventAndAttributeValueTypeList(Event event, List<AttributeValueType> attributeValueTypeList, RegistrationAttributeType registrationAttributeType){
        return customFormAttributeRepository.findByEventIdAndAttributeValueTypeIn(event.getEventId(),attributeValueTypeList, registrationAttributeType);
    }

    @Override
    public CustomFormAttributeData saveCustomAttributeData(CustomAttributeDetailsDto customAttributeDetailsDto, CustomFormAttributeData customFormAttributeData) {
        Map<String, Object> attributeMap = new HashMap<>();
        attributeMap.put(Constants.TICKETING.ATTRIBUTES, this.listToMap(customAttributeDetailsDto.getAttributes()));
        attributeMap.put(Constants.TICKETING.QUESTIONS, this.listToMap(customAttributeDetailsDto.getQuestions()));
        attributeMap.put(Constants.TICKETING.NESTEDQUESTIONS, (!CollectionUtils.isEmpty(customAttributeDetailsDto.getNestedQuestions()) ? customAttributeDetailsDto.getNestedQuestions() : null));
        customFormAttributeData.setJsonValue(new Gson().toJson(attributeMap, Map.class));
        return customFormAttributeDataRepository.save(customFormAttributeData);
    }

    @Override
    public CustomFormAttributeData saveCustomAttributeDataWithFormattedKeys(CustomAttributeDetailsDto customAttributeDetailsDto, CustomFormAttributeData customFormAttributeData, Event event, AttributeType attributeType) {
        // Get custom form attributes for this event to create name-to-ID mapping
        List<CustomFormAttribute> eventAttributes = getCustomFormAttributeOfEvent(event, null, null, attributeType);
        Map<String, Long> attributeNameToIdMap = eventAttributes.stream()
                .collect(Collectors.toMap(CustomFormAttribute::getName, CustomFormAttribute::getId));

        Map<String, Object> attributeMap = new HashMap<>();
        attributeMap.put(Constants.TICKETING.ATTRIBUTES, this.listToMapWithFormattedKeys(customAttributeDetailsDto.getAttributes(), attributeNameToIdMap));
        attributeMap.put(Constants.TICKETING.QUESTIONS, this.listToMapWithFormattedKeys(customAttributeDetailsDto.getQuestions(), attributeNameToIdMap));
        attributeMap.put(Constants.TICKETING.NESTEDQUESTIONS, (!CollectionUtils.isEmpty(customAttributeDetailsDto.getNestedQuestions()) ? customAttributeDetailsDto.getNestedQuestions() : null));
        customFormAttributeData.setJsonValue(new Gson().toJson(attributeMap, Map.class));
        return customFormAttributeDataRepository.save(customFormAttributeData);
    }

    protected Map<String, String> listToMap(List<AttributeKeyValueDto> attributeKeyValueDto) {
        Map<String, String> attributeMap = new HashMap<>();
        if (attributeKeyValueDto != null && !attributeKeyValueDto.isEmpty()) {
            attributeKeyValueDto.forEach(attribute -> attributeMap.put(attribute.getKey(), attribute.getValue()));
        }
        return attributeMap;
    }

    /**
     * Converts list of AttributeKeyValueDto to map with formatted keys like $CustomTextTest_201
     * @param attributeKeyValueDto List of attribute key-value pairs
     * @param attributeNameToIdMap Map of attribute names to their IDs
     * @return Map with formatted keys
     */
    protected Map<String, String> listToMapWithFormattedKeys(List<AttributeKeyValueDto> attributeKeyValueDto, Map<String, Long> attributeNameToIdMap) {
        Map<String, String> attributeMap = new HashMap<>();
        if (attributeKeyValueDto != null && !attributeKeyValueDto.isEmpty()) {
            attributeKeyValueDto.forEach(attribute -> {
                String originalKey = attribute.getKey();
                Long attributeId = attributeNameToIdMap.get(originalKey);

                if (attributeId != null) {
                    // Transform key to format: $ExampleTest_101
                    String formattedKey = formatAttributeKey(originalKey, attributeId);
                    attributeMap.put(formattedKey, attribute.getValue());
                } else {
                    // If no ID found, use original key as fallback
                    log.warn("No attribute ID found for key: {}, using original key", originalKey);
                    attributeMap.put(originalKey, attribute.getValue());
                }
            });
        }
        return attributeMap;
    }

    /**
     * Formats attribute key to CamelCaseName_ID format using GeneralUtils (without $ prefix)
     * @param attributeName Original attribute name (e.g., "Custom text test")
     * @param attributeId Attribute ID (e.g., 201)
     * @return Formatted key (e.g., "CustomTextTest_201")
     */
    private String formatAttributeKey(String attributeName, Long attributeId) {
        return GeneralUtils.formatAttributeKey(attributeName, attributeId);
    }

    /**
     * Updates existing custom_form_attribute_data JSON records for contacts by adding the new field with empty string value
     * @param event The event
     * @param customFormAttribute The newly created custom form attribute
     */
    private void updateExistingContactAttributeDataJson(Event event, CustomFormAttribute customFormAttribute) {
        // Format the key like Test_1201
        String formattedKey = formatAttributeKey(customFormAttribute.getName(), customFormAttribute.getId());

        // Update JSON for contacts only
        customFormAttributeDataRepository.updateAttributeForContact(event.getEventId(), formattedKey);

        log.info("Successfully updated contact attribute data JSON for event {} with field key: {}", event.getEventId(), formattedKey);
    }

    @Override
    public CustomAttributeDisplayDto getCustomAttributeDetails(Long customAttributeId, Event event, User user, AttributeType attributeType) {
        List<DisplayAttributeDto> questionDto = new ArrayList<>();
        List<DisplayAttributeDto> attributeDtos = new ArrayList<>();
        List<DisplayNestedQueDto> displayNestedQueDtoList= new ArrayList<>();
        CustomFormAttributeData customFormAttributeData = null;

        if(NumberUtils.isNumberGreaterThanZero(customAttributeId)){
            customFormAttributeData = customFormAttributeDataRepository.findById(customAttributeId).orElse(null);
        }

        if(customFormAttributeData != null && StringUtils.isNotEmpty(customFormAttributeData.getJsonValue())){
            List<CustomFormAttribute> customFormAttributeList = getCustomFormAttributeOfEvent(event, user, -1L,attributeType);
            CustomAttributesResponseDto customAttributeResponseDto = getCustomAttributeResponseDto(customFormAttributeData);

            for (CustomFormAttribute customFormAttribute : customFormAttributeList){
                if(customFormAttribute.isEnabled() && !customFormAttribute.isDefaultAttribute()){
                    DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                    displayAttributeDto.setName(customFormAttribute.getName());
                    displayAttributeDto.setType(customFormAttribute.getAttributeValueType().getType());
                    displayAttributeDto.setDefaultValue(customFormAttribute.getDefaultValue());

                    displayAttributeDto.setValue(getAttendeeAttributeValue(customAttributeResponseDto,
                            displayAttributeDto.getName(),  customFormAttribute.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(customFormAttribute.getAttributeValueType()) || isNumberGreaterThanZero(customFormAttribute.getParentQuestionId())));
                    displayAttributeDto.setPosition(customFormAttribute.getOrder());

                    if (customFormAttribute.isAttribute()) {
                        setAttendeeAttributesData(attributeDtos, displayNestedQueDtoList, customFormAttribute, displayAttributeDto);
                    } else {
                        questionDto.add(displayAttributeDto);
                    }
                }
            }
        }
        return new CustomAttributeDisplayDto(attributeDtos, questionDto, displayNestedQueDtoList);
    }

    private void setAttendeeAttributesData(List<DisplayAttributeDto> purchaserAttributesDto, List<DisplayNestedQueDto> displayNestedQueDtoList, CustomFormAttribute customFormAttribute, DisplayAttributeDto displayAttributeDto) {
        if(AttributeValueType.CONDITIONAL_QUE.equals(customFormAttribute.getAttributeValueType()) || isNumberGreaterThanZero(customFormAttribute.getParentQuestionId())){
            DisplayNestedQueDto displayNestedQueDto=new DisplayNestedQueDto(displayAttributeDto);
            displayNestedQueDto.setId(customFormAttribute.getId());
            displayNestedQueDto.setParentQueId(customFormAttribute.getParentQuestionId());
            displayNestedQueDto.setSelectedAnsId(customFormAttribute.getSelectedAnswer());
            displayNestedQueDtoList.add(displayNestedQueDto);
        } else {
            purchaserAttributesDto.add(displayAttributeDto);
        }
    }

    public static String getAttendeeAttributeValue(CustomAttributesResponseDto customAttributesResponseDto,
                                                   String attributeName,
                                                   boolean isAttribute, boolean isConditionalQuestion) {
        if(customAttributesResponseDto != null) {
            if (isAttribute) {
                if (isConditionalQuestion && customAttributesResponseDto.getNestedQuestions() != null) {
                    List<NestedQuestionsDto> nestedQuestions = customAttributesResponseDto.getNestedQuestions();
                    for (NestedQuestionsDto questionsDto : nestedQuestions) {
                        boolean isEnterValue = questionsDto.getName().equals(attributeName);
                        if (isEnterValue) {
                            return attributeValueCheck(questionsDto.getValue());
                        }
                    }
                } else if(customAttributesResponseDto.getAttributes() != null){
                    Map<String, String> purchaserAttribute = customAttributesResponseDto.getAttributes();
                    if (purchaserAttribute != null && !CollectionUtils.isEmpty(purchaserAttribute)) {
                        for (Map.Entry<String, String> entry : purchaserAttribute.entrySet()) {
                            if (attributeName.equalsIgnoreCase(entry.getKey()) || (EMAIL.equalsIgnoreCase(attributeName) && EMAIL_ADDRESS.equalsIgnoreCase(entry.getKey()))) {
                                return attributeValueCheck(entry.getValue());
                            }
                        }
                    }
                }
            } else if(customAttributesResponseDto.getQuestions() != null){
                Map<String, String> purchaserQue = customAttributesResponseDto.getQuestions();
                if (purchaserQue != null && !CollectionUtils.isEmpty(purchaserQue)) {
                    for (Map.Entry<String, String> entry : purchaserQue.entrySet()) {
                        if (attributeName.equalsIgnoreCase(entry.getKey())) {
                            return attributeValueCheck(entry.getValue());
                        }
                    }
                }
            }
        }
        return null;
    }

    private static String attributeValueCheck(String attributeValue) {
        if ("null".equalsIgnoreCase(attributeValue) || StringUtils.isBlank(attributeValue)) {
            return null;
        } else {
            return attributeValue;
        }
    }


    @Override
    public CustomAttributesResponseDto getCustomAttributeResponseDto(CustomFormAttributeData customFormAttributeData) {
        Gson gson = new Gson();
        try {
            CustomAttributesResponseDto dto = gson.fromJson(customFormAttributeData.getJsonValue(), CustomAttributesResponseDto.class);
            if (dto == null) {
                dto = new CustomAttributesResponseDto();
            }
            return dto;
        } catch (Exception e) {
            log.info("Exception raised while converting custom attribute to dto {} ",e.getMessage(), e);
            return new CustomAttributesResponseDto();
        }
    }

    @Override
    public List<CustomFormAttributeData> getCustomFormAttributeDataByIds(List<Long> contactAttributeIds) {
        if (CollectionUtils.isEmpty(contactAttributeIds)) {
            log.warn("Empty or null contact attribute IDs list provided");
            return new ArrayList<>();
        }

        log.info("Fetching custom form attribute data for IDs: {}", contactAttributeIds);

        try {
            List<CustomFormAttributeData> result = customFormAttributeDataRepository.findByIdIn(contactAttributeIds);
            log.info("Successfully fetched {} custom form attribute data records for {} IDs",
                    result.size(), contactAttributeIds.size());
            return result;
        } catch (Exception e) {
            log.error("Error retrieving custom form attribute data for IDs {}: {}", contactAttributeIds, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<CustomFormAttribute> getContactEnabledAttributesOrderByAttributeOrder(Event event, Long recurringEventId) {
        return (isNumberGreaterThanZero(recurringEventId)) ?
                customFormAttributeRepository.findAllCustomEnabledAttributesByRecurringEventIdOrderByOrder(event.getEventId(), recurringEventId,AttributeType.CONTACT) :
                customFormAttributeRepository.findAllCustomEnabledAttributesByEventIdAndRecurringEventIdNullOrderByOrder(event.getEventId(),AttributeType.CONTACT);
    }

    @Override
    public List<CustomEngageEmailMergeTagDto> getContactListMergeTagsDto(Event event, User user, Long recurringEventId, EngageEmailMergeTagType engageEmailMergeTagType) {
        try {
            if(EngageEmailMergeTagType.CONTACT.equals(engageEmailMergeTagType)){
                List<CustomFormAttribute> customFormAttributeList = this.getCustomFormAttributeOfEvent(event, user, recurringEventId, AttributeType.CONTACT);

                // Convert to DTO list
                List<CustomFormAttributeDTO> customFormAttributeDTOList = customFormAttributeList.stream()
                        .map(CustomFormAttributeDTO::new)
                        .collect(Collectors.toList());

                return extractContactListMergeTags(customFormAttributeDTOList);
            }else if(EngageEmailMergeTagType.BUYER.equals(engageEmailMergeTagType)){
                // Get BUYER merge tags
                return getBuyerMergeTags(event, recurringEventId);
            }else if(EngageEmailMergeTagType.HOLDER.equals(engageEmailMergeTagType)){
                // Get HOLDER merge tags
                return getHolderMergeTags(event, recurringEventId);
            }

        } catch (Exception e) {
            log.info("Exception raised while fetching contact list merge tags {} ", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    private static List<CustomEngageEmailMergeTagDto> extractContactListMergeTags(List<CustomFormAttributeDTO> customFormAttributeList) {
        if (CollectionUtils.isEmpty(customFormAttributeList)) {
            return Collections.emptyList();
        }

        return customFormAttributeList.stream()
                .filter(attribute -> StringUtils.isNotBlank(attribute.getFieldName()) && attribute.isEnabled()) // Only include enabled attributes with non-blank field names
                .map(attribute -> {
                    // For default fields, don't add underscore and ID concatenation
                    String value = convertFieldNameToMergeTag(
                            attribute.getFieldName(),
                            attribute.isDefaultAttribute() ? null : attribute.getId());
                    return new CustomEngageEmailMergeTagDto(
                            value, // description = "First Name"
                            attribute.getFieldName() // value = "${FirstName}" for default fields, "${CustomField_207}" for custom fields
                    );
                })
                .collect(Collectors.toList());
    }

    /**
     * Converts field name to merge tag format using GeneralUtils
     * Example: "First Name" -> "$FirstName"
     * @param fieldName The field name to convert
     * @return The merge tag format with $ prefix and camelCase
     */
    private static String convertFieldNameToMergeTag(String fieldName, Long id) {
        if (StringUtils.isBlank(fieldName)) {
            return Constants.STRING_EMPTY;
        }

        // Use GeneralUtils for consistent camelCase conversion with curly braces
        String camelCaseFieldName = GeneralUtils.toCamelCase(fieldName);

        // If ID is provided, include it inside the curly braces
        if (id != null) {
            return Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + camelCaseFieldName + STRING_UNDERSCORE + id + Constants.CLOSE_CURLY_BRACE;
        } else {
            return Constants.DOLLAR + Constants.OPEN_CURLY_BRACE + camelCaseFieldName + Constants.CLOSE_CURLY_BRACE;
        }
    }

    private List<CustomEngageEmailMergeTagDto> getBuyerMergeTags(Event event, Long recurringEventId) {
        List<CustomEngageEmailMergeTagDto> mergeTags = new ArrayList<>();
        List<AttributeValueType> attributeValueTypeList = List.of(AttributeValueType.CONDITIONAL_QUE, AttributeValueType.TEXT_BLOCK);
        List<TicketHolderRequiredAttributes> fields = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesHavingEnableForBuyerOrderByAttributeOrder(event, recurringEventId, DataType.TICKET);

        for (TicketHolderRequiredAttributes field : fields) {
            if (!attributeValueTypeList.contains(field.getAttributeValueType())) {
                addBuyerHolderAttributeToMergeTags(mergeTags, field, Constants.AttendeeTypeEnum.BUYER);
            }
        }
        return mergeTags;
    }

    private List<CustomEngageEmailMergeTagDto> getHolderMergeTags(Event event, Long recurringEventId) {
        List<CustomEngageEmailMergeTagDto> mergeTags = new ArrayList<>();
        List<AttributeValueType> attributeValueTypeList = List.of(AttributeValueType.CONDITIONAL_QUE, AttributeValueType.TEXT_BLOCK);
        boolean collectTicketHolderAttributesEnable = ticketingHelperService.isCollectTicketHolderAttributesByEvent(event);

        if (collectTicketHolderAttributesEnable) {
            List<TicketHolderRequiredAttributes> fields = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesHavingEnableForHolderOrderByAttributeOrder(event, recurringEventId, DataType.TICKET);

            for (TicketHolderRequiredAttributes field : fields) {
                if (!attributeValueTypeList.contains(field.getAttributeValueType())) {
                    addBuyerHolderAttributeToMergeTags(mergeTags, field, Constants.AttendeeTypeEnum.HOLDER);
                }
            }
        }
        return mergeTags;
    }

    private void addBuyerHolderAttributeToMergeTags(List<CustomEngageEmailMergeTagDto> mergeTags, TicketHolderRequiredAttributes field, Constants.AttendeeTypeEnum attendeeType) {
        CustomEngageEmailMergeTagDto attributeKeyValueDto = new CustomEngageEmailMergeTagDto();
        attributeKeyValueDto.setDescription(field.getName());
        attributeKeyValueDto.setId(Constants.DOLLAR.concat(Constants.OPEN_CURLY_BRACE).concat(attendeeType.getValue()).concat(Constants.STRING_UNDERSCORE).concat(Long.toString(field.getId())).concat(Constants.CLOSE_CURLY_BRACE));
        mergeTags.add(attributeKeyValueDto);
    }
}
