package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.BreakoutRoom;

import java.util.List;

public interface BreakoutRoomRepoService {
    void saveAll(List<BreakoutRoom> breakoutRooms);

    List<BreakoutRoom> getAllBreakoutRoom(Long sessionId, long eventId);


    BreakoutRoom findBreakoutRoomById(Long id);

    void save(BreakoutRoom breakoutRoom);

    boolean isBreakoutRoomPresentWithNameAndEventIdAndSessionId(String sessionBreakoutRoomName, long eventId, Long sessionId);

    boolean isBreakoutRoomPresentWithIdAndEventIdAndSessionId(Long roomId, long eventId, Long sessionId);

    int findBreakoutRoomCounterBySessionIdAndEventIdOrderByIdDesc(Long sessionId, long eventId);

    Long countAllByEventIdAndSessionId(Long eventId, Long sessionId);
}
