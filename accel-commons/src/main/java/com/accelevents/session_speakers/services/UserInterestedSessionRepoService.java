package com.accelevents.session_speakers.services;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.UserInterestedSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.List;

public interface UserInterestedSessionRepoService {

    void deleteBySessionIdAndUserId(Long sessionId, Long userId);

    List<Long> findByEventIdAndUserId(Long eventId, Long userId);

    List<IdCountDto> countBySessionIdIn(List<Long> sessionIds);

    BigInteger countBySessionId (Long sessionId);

    void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status);

    UserInterestedSession save(UserInterestedSession userSession);
}
