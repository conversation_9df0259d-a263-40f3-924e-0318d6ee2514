package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.UploadSessionDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.MuxDirectUploadIdAndUrlDto;
import com.accelevents.dto.StreamKeyAndRtmpUrlDto;
import com.accelevents.session_speakers.dto.CaptionsDto;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import org.json.JSONException;
import org.springframework.scheduling.annotation.Async;

import java.util.Collection;
import java.util.List;

public interface SessionThirdPartyService {
	StreamKeyAndRtmpUrlDto createStreamKey(Long sessionId, Event event, boolean regenerate, CaptionsDto autoGenerateLiveCaptions);

    MuxDirectUploadIdAndUrlDto createUploadUrlForSession(Long sessionId, Event event);

	String getPlaybackIdByUploadId(String uploadId);

    MuxAssetDTO getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(Long sessionId, Event event, String uploadId, AssetType assetType) throws JSONException;

    MuxAssetDTO getPlaybackIdAndStoreAssetDetailsForDifferentAssetType(Long sessionId, Event event, String uploadId, AssetType assetType, String title, String description) throws JSONException;

    void createChannelByListOfSessionIds(List<UploadSessionDto> sessionId, Event event);

	void createChannelByListOfSessionIds(Collection<Session> sessions, List<Long> eventAdmins);

    void createChannel(Long sessionId, String channelName, EnumSessionFormat sessionFormat, Event event);

	void stopConference(String valueOf);

	void updateChannelName(Long id, String title, EnumSessionFormat sessionFormat, boolean disableChat, Long eventId);

	@Async
	void deleteSessionChannel(Long sessionId, Long eventId);

	Integer setChatCountBySessionId(Long sessionId);
}
