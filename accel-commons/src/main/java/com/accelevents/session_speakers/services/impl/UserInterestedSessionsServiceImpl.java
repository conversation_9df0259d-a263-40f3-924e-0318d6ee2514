package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.UserInterestedSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.UserSessionDTO;
import com.accelevents.session_speakers.services.UserInterestedSessionRepoService;
import com.accelevents.session_speakers.services.UserInterestedSessionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Service
public class UserInterestedSessionsServiceImpl implements UserInterestedSessionService {

	private final UserInterestedSessionRepoService userInterestedSessionRepoService;

	@Autowired
	public UserInterestedSessionsServiceImpl(UserInterestedSessionRepoService userInterestedSessionRepoService){
		this.userInterestedSessionRepoService = userInterestedSessionRepoService;
	}

	@Override
	@Transactional
	public void markInterested(UserSessionDTO userSessionDTO, Long userId) {
		UserInterestedSession userSession = new UserInterestedSession();
		userSession.setEventId(userSessionDTO.getEventId());
		userSession.setUserId(userId);
		userSession.setSessionId(userSessionDTO.getSessionId());
		userInterestedSessionRepoService.save(userSession);
	}

	@Override
	@Transactional
	public void unMarkInterested(UserSessionDTO dto, Long userId) {
		userInterestedSessionRepoService.deleteBySessionIdAndUserId(dto.getSessionId(), userId);
	}


	@Override
	public List<Long> getMyInterestedSessions(User user, Event event) {
		return userInterestedSessionRepoService.findByEventIdAndUserId(event.getEventId(),user.getUserId());
	}

	@Override
	public List<IdCountDto> getSessionStatsForIdsIn(List<Long> sessionIds) {
		return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : userInterestedSessionRepoService.countBySessionIdIn(sessionIds);
	}

	@Override
	public Integer countInterestedBySessionId(Long sessionId) {
		return userInterestedSessionRepoService.countBySessionId(sessionId).intValue();
	}

    @Override
    @Transactional
    public void deleteBySessionId(Long sessionId) {
		userInterestedSessionRepoService.updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);
    }

}
