package com.accelevents.session_speakers.services.impl;


import com.accelevents.billing.chargebee.dto.CAPUsageDto;
import com.accelevents.billing.chargebee.service.impl.EventPlanConfigServiceImpl;
import com.accelevents.common.dto.GetStreamUserInfoDto;
import com.accelevents.common.dto.UploadSpeakerDto;
import com.accelevents.common.dto.UploadedSpeakerResponseContainer;
import com.accelevents.common.dto.UserBasicDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.*;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.ConflictException.UserExceptionConflictMsg;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.repositories.ExternalEventSyncTrackingRepository;
import com.accelevents.repositories.TicketingTypeCommonRepo;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.session.ROSessionRepoService;
import com.accelevents.ro.session.ROSessionSpeakerService;
import com.accelevents.ro.speaker.ROSpeakerService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneSyncService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.dto.cache.SpeakerCacheBasicDTO;
import com.accelevents.session_speakers.dto.cache.SpeakerCacheDTO;
import com.accelevents.session_speakers.services.*;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.NumberUtils;
import com.chargebee.APIException;
import com.google.gson.Gson;
import com.opencsv.CSVReader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.exceptions.NotFoundException.SessionSpeakerNotFound.SPEAKER_NOT_FOUND;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.NumberUtils.isValidPositiveNumber;
import static java.util.stream.Collectors.toList;

@Service
public class SpeakerServiceImpl implements SpeakerService {

    private static final Logger log = LoggerFactory.getLogger(SpeakerServiceImpl.class);

    private SpeakerRepoService speakerRepoService;
    private ROSpeakerService roSpeakerService;
    private SessionSpeakerService sessionSpeakerService;
    private ROSessionSpeakerService roSessionSpeakerService;
    private UserService userService;
    private UserSessionService userSessionService;
    private GetStreamService getStreamService;
    private CheckInAuditLogService checkInAuditLogService;
    private SessionRepoService sessionRepoService;
    private DeviceCheckerService deviceCheckerService;
    private TicketingOrderManagerService ticketingOrderManagerService;
    private EventTicketsService eventTicketsService;
    private ROSessionRepoService roSessionRepoService;
    private SessionSpeakerRepoService sessionSpeakerRepoService;


    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    private TicketHolderAttributesService ticketHolderAttributesService;
    private CommonEventService commonEventService;
    private ROEventService roEventService;
    private SessionDetailsRepoService sessionDetailsRepoService;
    private SpeakerHelperService speakerHelperService;
    private SessionTagAndTrackService sessionTagAndTrackService;
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    private VirtualEventService virtualEventService;
    private SessionService sessionService;
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;
    private WorkshopRecordingAssetService workshopRecordingAssetService;
    private EventPlanConfigServiceImpl eventPlanConfigService;
    private final ExternalEventSyncTrackingRepository externalEventSyncTrackingRepository;

    @Autowired
    private CustomFormAttributeDataRepository customFormAttributeDataRepository;

    @Autowired
    private  TicketHolderEditAttributesService ticketHolderEditAttributesService;

    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private ROVirtualEventService roVirtualEventService;

    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;

    @Autowired
    private AttendeeProfileService attendeeProfileService;

    @Autowired
    private CustomFormAttributeService customFormAttributeService;
    @Autowired
    private ROUserService roUserService;

    @Autowired
    private NeptuneSyncService neptuneSyncService;
    private Map<Long,Boolean> sessionBookmarkCapacity;

    private final EventTaskService eventTaskService;

    @Autowired
    public SpeakerServiceImpl(SpeakerRepoService speakerRepoService, ROSpeakerService roSpeakerService,
                              SessionSpeakerService sessionSpeakerService,
                              ROSessionSpeakerService roSessionSpeakerService,
                              UserService userService,
                              UserSessionService userSessionService,
                              GetStreamService getStreamService,
                              CheckInAuditLogService checkInAuditLogService,
                              SessionRepoService sessionRepoService,
                              DeviceCheckerService deviceCheckerService,
                              TicketingOrderManagerService ticketingOrderManagerService,
                              EventTicketsService eventTicketsService,
                              CommonEventService commonEventService, ROEventService roEventService,
                              SessionDetailsRepoService sessionDetailsRepoService,
                              SpeakerHelperService speakerHelperService,
                              SessionTagAndTrackService sessionTagAndTrackService,
                              TicketingTypeCommonRepo ticketingTypeCommonRepo,
                              VirtualEventService virtualEventService,
                              @Lazy SessionService sessionService,
                              MUXLivestreamAssetRepoService muxLivestreamAssetRepoService,
                              WorkshopRecordingAssetService workshopRecordingAssetService,
                              EventPlanConfigServiceImpl eventPlanConfigService, ExternalEventSyncTrackingRepository externalEventSyncTrackingRepository, TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService,
                              TicketHolderAttributesService ticketHolderAttributesService,
                              EventTaskService eventTaskService,SessionSpeakerRepoService sessionSpeakerRepoService,
                              ROSessionRepoService roSessionRepoService) {

        this.speakerRepoService = speakerRepoService;
        this.roSpeakerService = roSpeakerService;
        this.sessionSpeakerService = sessionSpeakerService;
        this.roSessionSpeakerService = roSessionSpeakerService;
        this.userService = userService;
        this.userSessionService = userSessionService;
        this.getStreamService = getStreamService;
        this.checkInAuditLogService = checkInAuditLogService;
        this.sessionRepoService=sessionRepoService;
        this.deviceCheckerService = deviceCheckerService;
        this.ticketingOrderManagerService = ticketingOrderManagerService;
        this.eventTicketsService = eventTicketsService;
        this.commonEventService = commonEventService;
        this.roEventService = roEventService;
        this.sessionDetailsRepoService = sessionDetailsRepoService;
        this.speakerHelperService = speakerHelperService;
        this.sessionTagAndTrackService=sessionTagAndTrackService;
        this.ticketingTypeCommonRepo=ticketingTypeCommonRepo;
        this.virtualEventService=virtualEventService;
        this.sessionService= sessionService;
        this.muxLivestreamAssetRepoService = muxLivestreamAssetRepoService;
        this.workshopRecordingAssetService = workshopRecordingAssetService;
        this.eventPlanConfigService = eventPlanConfigService;
        this.externalEventSyncTrackingRepository = externalEventSyncTrackingRepository;
        this.ticketHolderRequiredAttributesService = ticketHolderRequiredAttributesService;
        this.ticketHolderAttributesService = ticketHolderAttributesService;
        this.eventTaskService = eventTaskService;
        this.roSessionRepoService = roSessionRepoService;
        this.sessionSpeakerRepoService = sessionSpeakerRepoService;
    }

    private double sequence = 1000;

    Pattern allZeros = Pattern.compile("0+");

    @Override
    public Speaker save(Speaker speaker) {
        return speakerRepoService.save(speaker);
    }

    @Override
    public void saveAll(List<Speaker> speaker) {
        speakerRepoService.saveAll(speaker);
    }

    @Override
    public Speaker getSpeakerById(Long id, Event event) {
        return speakerRepoService.findByEventIdAndId(event.getEventId(),id).orElseThrow(()-> new NotFoundException(SPEAKER_NOT_FOUND));
    }

    @Override
    public List<Speaker> getSpeakerByIds(List<Long> ids, Event event) {
        return  CollectionUtils.isEmpty(ids) ? Collections.emptyList() : speakerRepoService.findByEventIdAndIds(event.getEventId(),ids);
    }

    @Override
    public SpeakerDTO getSpeakerByDto(Long id, Event event, String expand, User loggedInUser,boolean isAdminOrStaff) {
        Map<Long, Long> registerSessionStats; //NOSONAR
        Speaker speaker = getSpeakerById(id, event);
        Map<Long, List<Session>> speakerIdsSessions = null;
        if (expand.contains(SESSION.toUpperCase())) {
            speakerIdsSessions = roSessionSpeakerService.getSessionSpeakerIdSpeakerIds(Collections.singletonList(id), true,isAdminOrStaff);
        }
        Map<Long, List<Long>> currentUserSessionIdAndEventTicketIdMap = null;
        Map<Long, List<MuxAssetDTO>> sessionMuxAssetMap = new HashMap<>();
        if (loggedInUser != null && expand.contains("SESSION.currentUserRegisteredEventTicketId")) {
            List<Long> sessionIds = null;
            if(!CollectionUtils.isEmpty(speakerIdsSessions)){
                sessionIds = speakerIdsSessions.get(id).stream().map(Session::getId).collect(Collectors.toList());
            }
            currentUserSessionIdAndEventTicketIdMap = userSessionService.findRegisteredEventTicketIdByUserAndSessionId(
                    loggedInUser.getUserId(), sessionIds);
        }
        currentUserSessionIdAndEventTicketIdMap = currentUserSessionIdAndEventTicketIdMap!=null?currentUserSessionIdAndEventTicketIdMap : Collections.emptyMap();
        speakerIdsSessions = speakerIdsSessions != null ? speakerIdsSessions : Collections.emptyMap();
        List<Session> sessionsToReturn = speakerIdsSessions.get(id);
        if (!isAdminOrStaff) {
            sessionsToReturn = sessionRepoService.filterPrivateSessions(event, loggedInUser, sessionsToReturn);
        }
        List<SpeakerTicketTypeDTO> ticketTypesForUser = eventTicketsService.findUserTicketsByEventAndUserId(event, speaker.getUserId());
        if (!CollectionUtils.isEmpty(ticketTypesForUser)) {
            ticketTypesForUser = getSpeakerTicketUnique(ticketTypesForUser);
        }
        List <SessionDetails> sessionDetails = sessionDetailsRepoService.findSessionDetailsBySessionIds(sessionsToReturn);

        List<SessionTagAndTrack> sessionTagAndTracks=null;
        if (expand.contains("TAG") || expand.contains("TRACK")) {
            sessionsToReturn = null != sessionsToReturn ? sessionsToReturn : Collections.emptyList();
            log.info("SpeakerServiceImpl | getSpeakerByDto | before sessionIds");
            List<Long> sessionIds = sessionsToReturn.stream().filter(Objects::nonNull).map(Session::getId).collect(Collectors.toList());
            log.info("SpeakerServiceImpl | getSpeakerByDto | after sessionIds");
            sessionTagAndTracks = sessionTagAndTrackService.findBySessionIds(sessionIds);
        }

        if (expand.contains("ASSETS")) {
            List<Long> sessionIds = sessionsToReturn.stream().filter(Objects::nonNull).map(Session::getId).collect(Collectors.toList());
            sessionMuxAssetMap.putAll(muxLivestreamAssetRepoService.getMuxAssetsMapByAssetTypeAndSessionIds(sessionIds, AssetType.SESSION_ASSET));
            sessionMuxAssetMap.putAll(workshopRecordingAssetService.getWorkshopRecordingAssetsMapByVisibleStatusAndSessionIds(sessionIds));
        }

        boolean isAllowSessionBookmarkCapacity = virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId());
        log.info("SpeakerServiceImpl | getSpeakerByDto | isAllowSessionBookmarkCapacity =>{}",isAllowSessionBookmarkCapacity);
        if(!CollectionUtils.isEmpty(sessionsToReturn)) {
            if (isAllowSessionBookmarkCapacity) {
                List<Long> sessionIds = sessionsToReturn.stream().filter(Objects::nonNull).map(Session::getId).collect(Collectors.toList());
                List<Long> workshopSessionIds = sessionsToReturn.stream().filter(e->e.getFormat().equals(EnumSessionFormat.WORKSHOP)).map(Session::getId).collect(toList());
                List<IdCountDto> registerCount;
                if(!CollectionUtils.isEmpty(workshopSessionIds)) {
                    registerCount = userSessionService.getSessionStatsForIdsIn(workshopSessionIds);
                    sessionIds.removeAll(workshopSessionIds);
                    registerCount.addAll(userSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds,event.getEventId()));
                }else{
                    registerCount = userSessionService.getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(sessionIds,event.getEventId());
                }
                registerSessionStats = registerCount.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount));
                log.info("SpeakerServiceImpl | getSpeakerByDto | registerSessionStats =>{}",registerSessionStats);
                sessionBookmarkCapacity = sessionsToReturn.stream()
                        .collect(Collectors.toMap(
                                Session::getId,
                                sessionObj -> {
                                    if (registerSessionStats.containsKey(sessionObj.getId()) && sessionObj.getCapacity() != 0) {
                                        return registerSessionStats.get(sessionObj.getId()) >= sessionObj.getCapacity();
                                    } else {
                                        return false;
                                    }
                                }
                        ));
            } else {
                log.info("SpeakerServiceImpl | getSpeakerByDto | session bookmark capacity reached is false for event {} and speaker {}",event.getEventId(),speaker.getId());
                sessionBookmarkCapacity = sessionsToReturn.stream()
                        .collect(Collectors.toMap(
                                Session::getId,
                                sessionObj -> false
                        ));
            }
        }
        log.info("SpeakerServiceImpl | getSpeakerByDto | sessionBookmarkCapacity =>{}",sessionBookmarkCapacity);

        SpeakerDTO speakerDTO = new SpeakerDTO(speaker, event, sessionsToReturn, sessionMuxAssetMap,currentUserSessionIdAndEventTicketIdMap,checkInAuditLogService.countPreviousCheckIn(event,speaker.getUserId()),ticketTypesForUser, sessionDetails,sessionTagAndTracks,sessionBookmarkCapacity);
        speakerDTO.setSpeakerAttributeDisplayDTO(customFormAttributeService.getCustomAttributeDetails(speaker.getSpeakerAttributeId(), event, loggedInUser, AttributeType.SPEAKER));
        return speakerDTO;
    }

    @Override
    public List<Speaker> getSpeakerByEventId(Event event) {
        return speakerRepoService.findSpeakerByEventId(event.getEventId());
    }

    @Override
    public List<Speaker> getSpeakerByEventIdAndEmail(Event event, String email) {
        return speakerRepoService.findSpeakerByEmailAndEvent(email, event.getEventId());
    }

    @Override
    public Long createSpeaker(SpeakerDTO speakerDTO, Event event) {
        if(speakerDTO.getSpeakerAttributeDetailsDto() != null && !CollectionUtils.isEmpty(speakerDTO.getSpeakerAttributeDetailsDto().getAttributes())){
            List<AttributeKeyValueDto> attributes = speakerDTO.getSpeakerAttributeDetailsDto().getAttributes();
            prepareSpeakerDTOFromAttributes(attributes, speakerDTO);
        }
        checkSpeakerAlreadyExistByEmail(speakerDTO, event);
        User user = getOrCreateUser(speakerDTO, event);
        Speaker speaker = speakerRepoService.findByEventIdAndUserId(event.getEventId(), user.getUserId());
        if(null != speaker){
            NotAcceptableException.SessionSpeakerExceptionMsg exception = NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ALREADY_EXIST_WITH_EMAIL;
            String errorMessage = Constants.SPEAKER_ALREADY_EXIST_WITH_EMAIL.replace(Constants.EMAIL_ADDRESS_PARAMETER, speaker.getEmail());
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.EMAIL_ADDRESS_PARAMETER,speaker.getEmail());
            exception.setErrorMessage(errorMessage);
            exception.setDeveloperMessage(errorMessage);
            exception.setDefaultMessage(Constants.SPEAKER_ALREADY_EXIST_WITH_EMAIL);
            exception.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new NotAcceptableException(exception);
        }else{
            this.restrictSpeakerCountBasedOnPlanType(event, 1);
            speaker  = speakerDTO.createEntity(event, user);
        }

        speaker.setEmail(user.getEmail());
        setPositionForSpeaker(speaker);
        if(StringUtils.isBlank(speaker.getImageUrl()))
        {
            speaker.setImageUrl(user.getPhoto());
        }
        List<SpeakerTicketTypeDTO> currentTickets = eventTicketsService.findUserTicketsByEventAndUserId(event, speaker.getUserId());
        if (!currentTickets.isEmpty()) {
            speaker.setAllowAttendeeAccess(true);
        }

        if(speakerDTO.getSpeakerAttributeDetailsDto() != null){
            CustomFormAttributeData customFormAttributeData = customFormAttributeService.saveCustomAttributeData(speakerDTO.getSpeakerAttributeDetailsDto(), new CustomFormAttributeData());
            speaker.setSpeakerAttributeId(customFormAttributeData.getId());
        }
        speaker = save(speaker);
        neptuneSyncService.addStaffInNeptuneAsync(user.getUserId(), event, checkInAuditLogService.countPreviousCheckIn(event, user.getUserId()) == 0);

        GetStreamUserInfoDto userInfoDto = new GetStreamUserInfoDto(speaker.getFirstName().concat(" ").concat(speaker.getLastName()), speaker.getUserId(), event.getEventId(), speaker.getImageUrl(),CommonUtil.getSplitName(speaker.getFirstName()).concat(" ").concat(CommonUtil.getSplitName(speaker.getLastName())));
        Set<GetStreamUserInfoDto> userInfoDtoSet = new HashSet<>();
        userInfoDtoSet.add(userInfoDto);
        getStreamService.createChatUsersInBatch(userInfoDtoSet, event.getEventURL());
        eventTaskService.updateEventTaskAndAssignTaskToSpeaker(event,Collections.singletonList(speaker.getId()));
        return speaker.getId();
    }

    private void prepareSpeakerDTOFromAttributes(List<AttributeKeyValueDto> attributes, SpeakerDTO speakerDTO) {
        for(AttributeKeyValueDto attributeKeyValueDto: attributes){
            if(FIRST_NAME.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setFirstName(attributeKeyValueDto.getValue());
            }
            else if(LAST_NAME.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setLastName(attributeKeyValueDto.getValue());
            }
            else if(EMAIL_ADDRESS.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setEmail(attributeKeyValueDto.getValue());
            }
            else if(PRONOUNS.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setPronouns(attributeKeyValueDto.getValue());
            }
            else if(TITLE.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setTitle(attributeKeyValueDto.getValue());
            }
            else if(COMPANY.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setCompany(attributeKeyValueDto.getValue());
            }
            else if(BIO.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setBio(attributeKeyValueDto.getValue());
            }
            else if(LINKEDIN_URL.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setLinkedIn(attributeKeyValueDto.getValue());
            }
            else if(INSTAGRAM_HANDLE.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setInstagram(attributeKeyValueDto.getValue());
            }
            else if(TWITTER_HANDLE.equals(attributeKeyValueDto.getKey())){
                speakerDTO.setTwitter(attributeKeyValueDto.getValue());
            }
        }
    }

    private void restrictSpeakerCountBasedOnPlanType(Event event, int uploadCount){
        EventPlanConfig eventPlanConfig= eventPlanConfigService.findByEventId(event.getEventId());
        if(PlanConfigNames.FREE_PLAN.getName().equals(eventPlanConfig.getPlanConfig().getPlanName()))
        {
            CAPUsageDto capUsageDto = CAPUsageDto.convertJSONToObject(eventPlanConfig.getCapUsageConfigJson());
            Integer speakersCount = speakerRepoService.findSpeakersCountByEventId(event.getEventId());
            if((speakersCount+uploadCount) > capUsageDto.getMaxSpeakers()) {
                NotAcceptableException.SessionSpeakerExceptionMsg exception = NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_REQUIRED_UPDATE_PLAN;
                exception.setErrorMessage(exception.getErrorMessage().replace("{XX}", "100"));
                exception.setDeveloperMessage(exception.getDeveloperMessage().replace("{XX}", "100"));
                throw new NotAcceptableException(exception);
            }
        }
    }

    User getOrCreateUser(SpeakerDTO speakerDTO, Event event) {
        UserSignupDto userSignupDto = new UserSignupDto();
        Optional<User> optEmailUser = roUserService.getUserByEmail(speakerDTO.getEmail());
        if (optEmailUser.isPresent()){
            User user = optEmailUser.get();
            userSignupDto.setFirstName(null!=user.getFirstName()?user.getFirstName():speakerDTO.getFirstName());
            userSignupDto.setLastName(null!=user.getLastName()?user.getLastName():speakerDTO.getLastName());
        }else {
            userSignupDto.setFirstName(speakerDTO.getFirstName());
            userSignupDto.setLastName(speakerDTO.getLastName());
        }
        userSignupDto.setEmail(speakerDTO.getEmail());
        return userService.signUpBidderUserAndReturnUser(userSignupDto, event, true, false, false, false,
                true);
    }

    private void checkSpeakerAlreadyExistByEmail(SpeakerDTO speakerDTO, Event event){
        if (speakerRepoService.checkSpeakerAlreadyExistByEmail(speakerDTO.getEmail(), event.getEventId())) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ALREADY_EXIST);
        }
    }

    @Override
    public ResponseDto updateOnBoardingSpeaker(Long id, SpeakerDTO speakerDTO, Event event, User user) {
        Speaker speaker = getSpeakerById(id, event);
        log.info("SpeakerServiceImpl | updateOnBoardingSpeaker | speakerDTO =>{}",speakerDTO);
        if(StringUtils.isBlank(speakerDTO.getFirstName())){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.FIRST_NAME_NOT_BE_NULL);
        }
        if(StringUtils.isBlank(speakerDTO.getLastName())){
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.LAST_NAME_NOT_BE_NULL);
        }
        updateSpeakerProfile(speakerDTO, speaker);
        speaker = speakerDTO.updateEntity(speaker);
        log.info("SpeakerServiceImpl | updateOnBoardingSpeaker | speaker =>{}",speaker);
        save(speaker);
        return new ResponseDto(SUCCESS,SPEAKER_UPDATED);
    }

    @Override
    public ResponseDto updateSpeaker(Long id, SpeakerDTO speakerDTO, Event event,User staffUser, String languageCode, Map<String, String> languageMap) {//NOSONAR
        boolean isCreateGetstreamUser = false;
        List<Long> sessionIds = new ArrayList<>();

        if(speakerDTO.getSpeakerAttributeDetailsDto() != null && !CollectionUtils.isEmpty(speakerDTO.getSpeakerAttributeDetailsDto().getAttributes())){
            List<AttributeKeyValueDto> attributes = speakerDTO.getSpeakerAttributeDetailsDto().getAttributes();
            prepareSpeakerDTOFromAttributes(attributes, speakerDTO);

            Map<String, String> holderAttributes = speakerDTO.getSpeakerAttributeDetailsDto().getAttributes().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e -> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));
            Map<String, String> holderQue = speakerDTO.getSpeakerAttributeDetailsDto().getQuestions().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e -> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));

            Map<String, Object> speakerAttributes = new HashMap<>();


            speakerAttributes.put(TICKETING.ATTRIBUTES, holderAttributes);
            speakerAttributes.put(TICKETING.QUESTIONS, holderQue);
        }

        Speaker speaker = getSpeakerById(id, event);
        Speaker dtoSpeaker = null;
        Optional<User> optEmailUser = roUserService.getUserByEmail(speakerDTO.getEmail());
        if(optEmailUser.isPresent()){
            dtoSpeaker = speakerRepoService.findByEventIdAndUserId(event.getEventId(), optEmailUser.get().getUserId());
            if(null != dtoSpeaker && id != dtoSpeaker.getId()){
                NotAcceptableException.SessionSpeakerExceptionMsg exception = NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ALREADY_EXIST_WITH_EMAIL;
                String errorMessage = Constants.SPEAKER_ALREADY_EXIST_WITH_EMAIL.replace(Constants.EMAIL_ADDRESS_PARAMETER, dtoSpeaker.getEmail());
                Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
                defaultMessageParamMap.put(Constants.EMAIL_ADDRESS_PARAMETER,dtoSpeaker.getEmail());
                exception.setErrorMessage(errorMessage);
                exception.setDeveloperMessage(errorMessage);
                exception.setDefaultMessage(Constants.SPEAKER_ALREADY_EXIST_WITH_EMAIL);
                exception.setDefaultMessageParamMap(defaultMessageParamMap);
                throw new NotAcceptableException(exception);
            }
        }

        boolean emailFlag = false;
        String pastSpeakerEmail = null;

        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        List<Long> currentTickets = null;

        List<EventTickets> eventTicketsList = eventTicketsService.findAllByEventAndUserIdAndTicketStatusAndIsSpeakerOrder(event, speaker.getUserId());

        List<String> attributeNames = Arrays.asList(ORGANIZATION, JOB_TITLE);
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService
                .findAllByAttributeNamesAndEventIdAndRecurringEventIdIsNULLAndDataType(attributeNames, event, DataType.TICKET);

        if (ticketHolderRequiredAttributes != null && !ticketHolderRequiredAttributes.isEmpty()) {
            log.info("updateSpeaker : total ticket holder attributes : {}",ticketHolderRequiredAttributes.size());
            List<TicketHolderAttributes> ticketHolderAttributesListUpdated = eventTicketsList.stream()
                    .map(EventTickets::getTicketHolderAttributesId)
                    .filter(Objects::nonNull)
                    .filter(ticketHolderAttributes -> ticketHolderAttributes.getJsonValue() != null)
                    .peek(ticketHolderAttributes -> {
                        TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper
                                .parseJsonToObject(ticketHolderAttributes.getJsonValue());

                        Map<String, String> buyerAttributesMap = (Map<String, String>) ticketAttributeValueDto.getPurchaser().get(TICKETING.ATTRIBUTES);
                        Map<String, String> holderAttributesMap = (Map<String, String>) ticketAttributeValueDto.getHolder().get(TICKETING.ATTRIBUTES);

                        ticketHolderRequiredAttributes.forEach(e -> {
                            String attributeName = e.getName();
                            boolean updateBuyer = e.getEnabledForTicketPurchaser();
                            boolean updateHolder = e.getEnabledForTicketHolder();

                            if (ORGANIZATION.equalsIgnoreCase(attributeName)) {
                                if (updateBuyer) buyerAttributesMap.put(ORGANIZATION, speakerDTO.getCompany());
                                if (updateHolder) holderAttributesMap.put(ORGANIZATION, speakerDTO.getCompany());
                            } else if (JOB_TITLE.equalsIgnoreCase(attributeName)) {
                                if (updateBuyer) buyerAttributesMap.put(JOB_TITLE, speakerDTO.getTitle());
                                if (updateHolder) holderAttributesMap.put(JOB_TITLE, speakerDTO.getTitle());
                            }
                        });

                        // Save updated JSON after modifying the attributes
                        ticketHolderAttributes.setJsonValue(ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto));
                    })
                    .collect(Collectors.toList());

            if (!ticketHolderAttributesListUpdated.isEmpty()) {
                log.info("updateSpeaker : total ticket holder attributes updated : {}",ticketHolderAttributesListUpdated.size());
                ticketHolderAttributesService.saveAll(ticketHolderAttributesListUpdated);
            }
        }

        if(!speaker.getEmail().equalsIgnoreCase(speakerDTO.getEmail())){
            log.info("updateSpeaker : speaker email changed from {} to {}",speaker.getEmail(),speakerDTO.getEmail());
            pastSpeakerEmail = speaker.getEmail();
            long isLoggedIn = checkInAuditLogService.findAuditLogByUserId(speaker.getUserId());
            if(isLoggedIn > 0 && (null == dtoSpeaker  || (dtoSpeaker.getId() == id && !speaker.getEmail().equalsIgnoreCase(dtoSpeaker.getEmail())))){
                throw new ConflictException(UserExceptionConflictMsg.USER_HAS_ALREADY_LOGGED_IN);
            }
            checkSpeakerAlreadyExistByEmail(speakerDTO, event);

            User user = getOrCreateUser(speakerDTO,event);

            if (null!=user && !speaker.getUserId().equals(user.getUserId())) {
                sessionIds = sessionSpeakerService.getAllSessionIdsBySpeakerId(speaker.getId());
                isCreateGetstreamUser = removeOldSpeakerFromChannels(isCreateGetstreamUser, sessionIds, speaker);

                log.info("updateSpeaker : total event tickets speaker have : {}",eventTicketsList.size());
                currentTickets = eventTicketsList.stream()
                        .map(eventTickets -> eventTickets.getTicketingTypeId().getId())
                        .collect(Collectors.toList());
                Set<Long> ticketingOrderIds = eventTicketsList.stream()
                        .map(eventTickets -> eventTickets.getTicketingOrder().getId())
                        .collect(Collectors.toSet());
                UserBasicDto userBasicDto = new UserBasicDto(user);
                ticketingOrderIds.forEach(orderId -> {
                    log.info("transfer event tickets to new speaker using orderId : {}", orderId);
                    ticketHolderEditAttributesService.transferTicket(userBasicDto, Collections.singletonList(orderId), staffUser, false);
                });
                speaker.setEmail(user.getEmail());
                speaker.setUserId(user.getUserId());
            }
            emailFlag = true;

        }

        speaker = speakerDTO.updateEntity(speaker);
        speaker = updateSpeakerProfile(speakerDTO, speaker);

        CustomFormAttributeData customFormAttributeData = new CustomFormAttributeData();

        if(speakerDTO.getSpeakerAttributeDetailsDto() != null){
            if (speaker.getSpeakerAttribute() != null){
                customFormAttributeData = speaker.getSpeakerAttribute();
            }

            customFormAttributeData = customFormAttributeService.saveCustomAttributeData(speakerDTO.getSpeakerAttributeDetailsDto(), customFormAttributeData);
            speaker.setSpeakerAttributeId(customFormAttributeData.getId());
        }

        createOrUpdateGetStreamUser(isCreateGetstreamUser, speaker, sessionIds, event);
        log.info("SpeakerServiceImpl | updateSpeaker | save");
        save(speaker);
        log.info("SpeakerServiceImpl | updateSpeaker | save | successfully");
        if(speaker.getUserId() != null && checkInAuditLogService.countPreviousCheckIn(event, speaker.getUserId()) == 0) {
            neptuneSyncService.updateSpeakerDetailsInNeptune(speaker);
        }
        if(emailFlag) {
            if(virtualEventService.isSpeakerInviteEnable(event)) {
                speakerHelperService.sendInvite(event, speaker.getId(), pastSpeakerEmail);
            }
            if(CollectionUtils.isEmpty(currentTickets)){
                assignAttendeeAccessToSpeaker(event,Collections.emptyList(), speaker.getId(), staffUser, false);
                return new ResponseDto(SUCCESS,resourceBundle.getString(languageMap.get(SPEAKER_EMAIL_CHANGE_NOT_HAVE_ATTENDEE_ACCESS)));
            }else{
                assignAttendeeAccessToSpeaker(event,currentTickets, speaker.getId(), staffUser, true);
            }
        }

        return new ResponseDto(SUCCESS,resourceBundle.getString(languageMap.get(SPEAKER_UPDATED)));
    }

    private Speaker updateSpeakerProfile(SpeakerDTO speakerDTO, Speaker speaker) {
        String speakerProfilePicUrl = null;
        if(StringUtils.isNotBlank(speakerDTO.getImageUrl()) && !speakerDTO.getImageUrl().equals(speaker.getImageUrl())) {
            speakerProfilePicUrl = speakerDTO.getImageUrl();
            speaker.setImageUrl(speakerProfilePicUrl);
        }

        Optional<User> userOptional = roUserService.getUserByEmail(speakerDTO.getEmail());
        if(userOptional.isPresent()){
            User e = userOptional.get();
            if(StringUtils.isBlank(e.getPhoto()) && StringUtils.isNotBlank(speakerProfilePicUrl)){
               e.setPhoto(speakerProfilePicUrl);
               userService.save(e);
            }
            if(StringUtils.isBlank(speakerProfilePicUrl)&&StringUtils.isNotBlank(e.getPhoto())&&StringUtils.isBlank(speaker.getImageUrl())){
                speaker.setImageUrl(e.getPhoto());
            }

        }
        return speaker;
    }

    private boolean removeOldSpeakerFromChannels(boolean isCreateGetstreamUser, List<Long> sessionIds, Speaker speaker) {
        if (null != sessionIds && !sessionIds.isEmpty()) {
            isCreateGetstreamUser = true;
            sessionIds.forEach(sessionId -> {
                getStreamService.removeMemberFromChannel(SESSION_.concat(String.valueOf(sessionId)), LIVESTREAM, Collections.singletonList(speaker.getUserId()));
                getStreamService.removeMemberFromChannel(BACKSTAGE_.concat(String.valueOf(sessionId)), LIVESTREAM, Collections.singletonList(speaker.getUserId()));
            });
        }
        return isCreateGetstreamUser;
    }


    private void createOrUpdateGetStreamUser(boolean isCreateUser, Speaker speaker, List<Long> sessionIds, Event event) {

        if (isCreateUser) {
            GetStreamUserInfoDto userInfoDto = new GetStreamUserInfoDto(speaker.getFirstName() + " " + speaker.getLastName(), speaker.getUserId(), event.getEventId(), speaker.getImageUrl(),CommonUtil.getSplitName(speaker.getFirstName()) + " " + CommonUtil.getSplitName(speaker.getLastName()));
            sessionIds.forEach(sessionId -> {
                getStreamService.addUserAsMemberOrModerator(SESSION_.concat(String.valueOf(sessionId)), LIVESTREAM,
                        Collections.singletonList(userInfoDto), null, true, true);
                getStreamService.addUserAsMemberOrModerator(BACKSTAGE_.concat(String.valueOf(sessionId)), LIVESTREAM,
                        Collections.singletonList(userInfoDto), null, true, true);
            });

        } else {
            User userInfo = new User();
            userInfo.setUserId(speaker.getUserId());
            userInfo.setFirstName(speaker.getFirstName());
            userInfo.setLastName(speaker.getLastName());
            userInfo.setPhoto(speaker.getImageUrl());
            getStreamService.updateChatUser(userInfo, false, null, event.getEventId());
        }

    }

    Page<Speaker> getAllSpeakerByEventId(Event event, String search, Pageable pageable) {
        return speakerRepoService.getAllSpeakerByEvent(search,event.getEventId(), pageable);
    }

    @Override
    public DataTableResponse getSpeakerList(Event event, String search, String expand, Pageable pageable, boolean isAdminOrStaff) {
        Page<Speaker> speakerPage = getAllSpeakerByEventId(event, search, pageable);
        return getSpeakerDataTable(event, expand, speakerPage,isAdminOrStaff);

    }

    private DataTableResponse getSpeakerDataTable(Event event, String expand, Page<Speaker> speakerPage,boolean isAdminOrStaff) {
        List<Long> speakerIds = speakerPage.getContent().stream().map(Speaker::getId).collect(Collectors.toList());

        Map<Long, List<Session>> speakerIdsSessions = new HashMap<>();
        if(expand.contains(SESSION.toUpperCase())){
            speakerIdsSessions = roSessionSpeakerService.getSessionSpeakerIdSpeakerIds(speakerIds,isAdminOrStaff);
        }
        List<Long> speakerUserIds = speakerPage.getContent().stream().map(Speaker::getUserId).collect(Collectors.toList());
        Map<Long, Boolean> speakerDeviceChecker = deviceCheckerService.speakerIdAndRunDeviceChecker(speakerUserIds);
        List<IdCountDto> userCheckInList =  !speakerUserIds.isEmpty()?checkInAuditLogService.countPreviousCheckInUserList(event,speakerUserIds):Collections.emptyList();
        Map<Long, Map<Long, Boolean>> userIdAndTickets =  !speakerUserIds.isEmpty()?getSpeakerTickets(event, speakerUserIds):Collections.emptyMap();
        Map<Long, Long> userCheckInMap = null != userCheckInList ? userCheckInList.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount)) : Collections.emptyMap();

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(speakerPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(speakerPage.getContent().size());
        Map<Long, List<Session>> finalSpeakerIdsSessions = speakerIdsSessions;
        dataTableResponse.setData(speakerPage.getContent().stream()
                .map(e->new SpeakerDTO(e, event, finalSpeakerIdsSessions.get(e.getId()),speakerDeviceChecker.get(e.getUserId()),userCheckInMap.get(e.getUserId()),userIdAndTickets.get(e.getUserId())))
                .collect(Collectors.toList()));
        return dataTableResponse;
    }

    @Override
    public List<SpeakerBasicDTO> getSpeakersBasicInfo(Event event) {
        return speakerRepoService.findSpeakerByEventId(event.getEventId()).stream().map(SpeakerBasicDTO::new).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public void deleteSpeakerAlongWithSessionSpeaker(Long speakerId, Event event) {
            Speaker speaker = getSpeakerById(speakerId, event);
            User speakerUser = userService.findById(speaker.getUserId());
            List<Session> sessionBySpeakerUserId = sessionSpeakerService.findSessionBySpeakerUserId(event, speaker.getUserId());
            if (!CollectionUtils.isEmpty(sessionBySpeakerUserId)) {
                List<Long> userTicketTypes = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, speakerUser);
                sessionBySpeakerUserId.forEach(session -> {
                    if (null != session && !sessionService.isSpeakerIsValidAttendee(speakerUser, event, session, userTicketTypes)) {
                        userSessionService.unRegisterUserWithoutEventTicket(session.getId(), speakerUser.getUserId(), event);
                    }
                });
            }
            sessionSpeakerService.deleteSessionSpeakerBySpeaker(speaker.getId(), event);

            try {
                speakerRepoService.deleteSpeaker(speaker, event);
            } catch (Exception e) {
                log.info("Exception occurred while deleteSpeakerAlongWithSessionSpeaker {}", e.getMessage());
            }

        externalEventSyncTrackingRepository.deleteByEventIdAndAeIdAndExternalSourceType(event.getEventId(), speakerId, "SPEAKER");
        eventTaskService.removeTaskParticipantsFromSpeaker(event,Collections.singletonList(speakerId));
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public void deleteSpeakersAlongWithSessionSpeaker(DeleteSpeakersDTO deleteSpeakersDTO, Event event) {
        List<Speaker> speakers = getSpeakerByIds(deleteSpeakersDTO.getSpeakerIds(), event);
        List<User> userList = new ArrayList<>();
        speakers.forEach(speaker-> userList.add(userService.findById(speaker.getUserId())));
        userList.forEach(user->{
            List<Session> sessionBySpeakerUserId = sessionSpeakerService.findSessionBySpeakerUserId(event, user.getUserId());
            if (!CollectionUtils.isEmpty(sessionBySpeakerUserId)) {
                List<Long> userTicketTypes = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user);
                sessionBySpeakerUserId.forEach(session -> {
                    if (null != session && !sessionService.isSpeakerIsValidAttendee(user, event, session, userTicketTypes)) {
                        userSessionService.unRegisterUserWithoutEventTicket(session.getId(), user.getUserId(), event);
                    }
                });
            }
        });
        sessionSpeakerService.deleteSessionSpeakerBySpeakers(speakers, event);
        try {
            speakerRepoService.deleteSpeakers(speakers, event);

            List<Long> speakerIds = speakers.stream().map(Speaker::getId).collect(Collectors.toList());
            externalEventSyncTrackingRepository.deleteByEventIdAndExternalSourceTypeAndAeIdIn(event.getEventId(), "SPEAKER", speakerIds);
            eventTaskService.removeTaskParticipantsFromSpeaker(event,speakerIds);
        } catch (Exception e) {
            log.info("Exception occurred while deleteSpeakerAlongWithSessionSpeaker {}", e.getMessage());
        }
    }

	@Override
	public Long getSpeakerIdForTheUserInEvent(Event event, User user) {
		return speakerRepoService.findIdByEventIdAndUserId(event.getEventId(),user.getUserId())
        .orElse(null);
	}

	@Override
	public boolean isSpeakerInEvent(Event event, User user) {
        return speakerRepoService.findByIdEventIdAndUserId(event.getEventId(),user.getUserId());
    }

    @Override
    public long getUserId(long eventId) {
        return speakerRepoService.getUserId(eventId);
    }

    @Override
	public List<Speaker> getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(long from, long to) {
        return speakerRepoService.getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(from, to);
    }

    private boolean isValidSessionField(List<Long> sessionIds, List<Long> allSessionIds, StringBuilder duplicateMsg, StringBuilder errorMsg) {
        boolean allValid = true;

        // 1. Check for invalid IDs (not in event sessions)
        List<Long> invalidIds = sessionIds.stream()
                .filter(id -> !allSessionIds.contains(id))
                .collect(Collectors.toList());

        if (!invalidIds.isEmpty()) {
            errorMsg.append(invalidIds.toString());
            return false;
        }

        Set<Long> seen = new HashSet<>();
        Set<Long> duplicates = sessionIds.stream()
                .filter(id -> !seen.add(id))
                .collect(Collectors.toSet());

        if (!duplicates.isEmpty()) {
            duplicateMsg.append(duplicates.toString()); // store duplicate IDs to show later
            return false;
        }

        return allValid;
    }

    boolean isValidSpeakerCsvFileHeaderAttribute(String[] header) {
        if (header == null || header.length < 14) {
            return false; // must have at least the 12 fixed columns
        }

        Set<String> seen = new HashSet<>();
        for (String col : header) {
            String normalized = col == null ? "" : col.trim().toLowerCase();
            if (!seen.add(normalized)) {
                return false; // duplicate column found
            }
        }
        return header[0].trim().equalsIgnoreCase(Constants.SPEAKER_SPACE_ID) &&
                header[1].trim().equalsIgnoreCase(Constants.FIRST_NAME) &&
                header[2].trim().equalsIgnoreCase(Constants.LAST_NAME) &&
                header[3].trim().equalsIgnoreCase(Constants.EMAIL) &&
                header[4].trim().equalsIgnoreCase(Constants.PRONOUNS) &&
                header[5].trim().equalsIgnoreCase(Constants.TITLE) &&
                header[6].trim().equalsIgnoreCase(Constants.COMPANY) &&
                header[7].trim().equalsIgnoreCase(Constants.BIO) &&
                header[8].trim().equalsIgnoreCase(Constants.LINKEDIN_URL) &&
                header[9].trim().equalsIgnoreCase(Constants.INSTAGRAM_HANDLE) &&
                header[10].trim().equalsIgnoreCase(Constants.TWITTER_HANDLE) &&
                header[11].trim().equalsIgnoreCase(Constants.OVERRIDE_PROFILE_DETAILS) &&
                header[12].trim().equalsIgnoreCase(PRIMARY_SESSIONS) &&
                header[13].trim().equalsIgnoreCase(SECONDARY_SESSIONS);
    }

    private boolean isEmailAddressAlreadyExistingInDB(String email, Event event) {
        return speakerRepoService.checkSpeakerAlreadyExistByEmail(email, event.getEventId());
    }

    @Override
    public UploadedSpeakerResponseContainer parseSpeakerCSV(
            MultipartFile multiPartFile,
            Event event,
            User user,
            Map<String, String> languageMap
    ) throws IOException {

        UploadedSpeakerResponseContainer response = new UploadedSpeakerResponseContainer();
        final Long eventId = event.getEventId();

        try (CSVReader cr = new CSVReader(
                new BufferedReader(new InputStreamReader(multiPartFile.getInputStream(), StandardCharsets.UTF_8)))) {

            // 1) Header
            String[] header = cr.readNext();
            if (!isValidSpeakerCsvFileHeaderAttribute(header)) {
                throw new NotAcceptableException(
                        NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_HEADER_NOT_CORRECT
                );
            }

            final List<String[]> rows = new ArrayList<>();
            final List<String> emailsForCreateRows = new ArrayList<>();
            final Map<String, Integer> emailCountsForCreateRowsNorm = new HashMap<>();
            final List<String> speakerIdStrings = new ArrayList<>();

            String[] row;
            while ((row = cr.readNext()) != null) {
                rows.add(row);

                String rawEmail = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(row, 3));
                String normEmail = rawEmail == null ? null : rawEmail.trim();

                String rawSid = GeneralUtils.checkAndGetFromArray(row, 0);
                String sidClean = rawSid == null ? null : rawSid.replaceAll("[\\uFEFF\\u200B\\u00A0]", "").trim();

                if (StringUtils.isNotBlank(sidClean) && !allZeros.matcher(sidClean).matches()) {
                    if (isValidPositiveNumber(sidClean)) {
                        long parsedId = Long.parseLong(sidClean.replaceFirst("^0+", ""));
                        speakerIdStrings.add(String.valueOf(parsedId));
                    } else {
                        log.info("Invalid non-numeric speakerId cell found: '{}'", sidClean);
                    }
                }

                if (StringUtils.isNotBlank(rawEmail)) {
                    emailsForCreateRows.add(rawEmail.trim());
                    emailCountsForCreateRowsNorm.merge(normEmail, 1, Integer::sum);
                }
            }

            // 2) Fetch existing speakers for update IDs
            final List<Long> speakerIdsInFile = speakerIdStrings.stream()
                    .map(s -> {
                        try {
                            return Long.valueOf(s);
                        } catch (NumberFormatException ex) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            final List<Speaker> existingSpeakers = speakerIdsInFile.isEmpty()
                    ? Collections.emptyList()
                    : speakerRepoService.getSpeakerWithUserAndAttributeDataByEventIdAndIds(eventId, speakerIdsInFile);

            final Map<Long, Speaker> existingSpeakerMap = existingSpeakers.stream()
                    .collect(Collectors.toMap(Speaker::getId, s -> s, (a, b) -> a));

            // Normalize existing emails
            final List<String> existingEmailsForCreates = emailsForCreateRows.isEmpty()
                    ? Collections.emptyList()
                    : speakerRepoService.findAllEmailsByEventIdAndEmails(eventId, emailsForCreateRows);

            // 3) Build DTOs
            final List<UploadSpeakerDto> uploadSpeakerDtoList = new ArrayList<>();
            final Set<Long> seenSpeakerIds = new HashSet<>();

            for (String[] nextItem : rows) {
                // Fixed columns
                String speakerIdStr = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 0));
                String firstName    = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 1));
                String lastName     = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 2));
                String email        = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 3));
                String pronouns     = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 4));
                String title        = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 5));
                String company      = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 6));
                String bio          = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 7));
                String linkedIn     = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 8));
                String insta        = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 9));
                String twitter      = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 10));
                String override     = StringUtils.trimToNull(GeneralUtils.checkAndGetFromArray(nextItem, 11));
                String primarySessions = GeneralUtils.checkAndGetFromArray(nextItem, 12);
                String secondarySessions = GeneralUtils.checkAndGetFromArray(nextItem,13);



                // Dynamic attributes
                List<AttributeKeyValueDto> extraAttributes = new ArrayList<>();
                if (nextItem.length > 14) {
                    for (int i = 14; i < nextItem.length; i++) {
                        String headerKey = (i < header.length) ? header[i] : null;
                        headerKey = headerKey == null ? null : headerKey.trim();
                        String value = GeneralUtils.checkAndGetFromArray(nextItem, i);
                        if (StringUtils.isNotBlank(headerKey)) {
                            extraAttributes.add(new AttributeKeyValueDto(headerKey, value));
                        }
                    }
                }

                boolean isPrimarySessionInvalid = !NumberUtils.isCommaSeparatedNumbers(primarySessions);
                boolean isSecondarySessionInvalid = !NumberUtils.isCommaSeparatedNumbers(secondarySessions);
                boolean containsInvalidSessionIds = isPrimarySessionInvalid || isSecondarySessionInvalid;
                List<Long> primaryIds = isPrimarySessionInvalid
                        ? new ArrayList<>()
                        : GeneralUtils.convertCommaSeparatedToListLong(primarySessions);

                List<Long> secondaryIds = isSecondarySessionInvalid
                        ? new ArrayList<>()
                        : GeneralUtils.convertCommaSeparatedToListLong(secondarySessions);

                Long speakerIdLong = null;
                boolean isUpdateRow = false;

                if (StringUtils.isNotBlank(speakerIdStr) && !allZeros.matcher(speakerIdStr).matches()) {
                    isUpdateRow = true;
                    if (isValidPositiveNumber(speakerIdStr)) {
                        try {
                            speakerIdLong = Long.valueOf(speakerIdStr.trim());
                        } catch (NumberFormatException nfe) {
                            response.addInvalidSpeakers(
                                    new UploadSpeakerDto(firstName, lastName, email, pronouns, title, company, bio,
                                            linkedIn, twitter, insta, override, false,primaryIds, secondaryIds, 0L, extraAttributes, containsInvalidSessionIds, false, false, false,true),
                                    Constants.SpeakerImportMessage.SPEAKER_ID_MUST_BE_NUMERIC.getStatus()
                            );
                            continue;
                        }
                    } else {
                        response.addInvalidSpeakers(
                                new UploadSpeakerDto(firstName, lastName, email, pronouns, title, company, bio,
                                        linkedIn, twitter, insta, override, false,primaryIds, secondaryIds, 0L, extraAttributes,containsInvalidSessionIds, false, false,false,true),
                                Constants.SpeakerImportMessage.SPEAKER_ID_MUST_BE_NUMERIC.getStatus()
                        );
                        continue;
                    }
                }

                if (isUpdateRow) {
                    if (seenSpeakerIds.contains(speakerIdLong)) {
                        response.addInvalidSpeakers(
                                new UploadSpeakerDto(firstName, lastName, email, pronouns, title, company, bio,
                                        linkedIn, twitter, insta, override, false, primaryIds, secondaryIds, speakerIdLong, extraAttributes, containsInvalidSessionIds, false, false,false,true),
                                Constants.SpeakerImportMessage.DUPLICATE_SPEAKER_ID.getStatus()
                        );
                        continue;
                    } else {
                        seenSpeakerIds.add(speakerIdLong);
                    }

                    if (!existingSpeakerMap.containsKey(speakerIdLong)) {
                        response.addInvalidSpeakers(
                                new UploadSpeakerDto(firstName, lastName, email, pronouns, title, company, bio,
                                        linkedIn, twitter, insta, override, false, primaryIds, secondaryIds, speakerIdLong, extraAttributes, containsInvalidSessionIds, false, false, false,true),
                                Constants.SpeakerImportMessage.SPEAKER_ID_NOT_FOUND_TO_UPDATE_FOR_EVENT.getStatus()
                        );
                        continue;
                    }
                }

                // Validation
                boolean validRow = isValidFirstNameAndLastNameAndEmailAndTitleAndCompany(
                        response, firstName, lastName, email, pronouns, title, company, bio,
                        linkedIn, insta, twitter, override, event, user, languageMap,
                        isUpdateRow, existingEmailsForCreates, emailCountsForCreateRowsNorm, speakerIdLong, extraAttributes,
                        primaryIds, secondaryIds, isPrimarySessionInvalid,isSecondarySessionInvalid
                );
                if (!validRow) continue;

                UploadSpeakerDto dto = new UploadSpeakerDto(
                        firstName, lastName, email, pronouns, title, company, bio,
                        linkedIn, twitter, insta, override, false,  primaryIds, secondaryIds, speakerIdLong, extraAttributes, containsInvalidSessionIds, false , false, false,false);

                if (isUpdateRow) {
                    dto.setIsEmailAlreadyPresent(false);
                    dto.setOperation(RecordStatus.UPDATE.name());
                    dto.setSpeakerId(speakerIdLong);
                } else {
                    boolean emailExists = email != null && existingEmailsForCreates.contains(email);
                    dto.setIsEmailAlreadyPresent(emailExists);
                    dto.setOperation(RecordStatus.CREATE.name());
                }

                response.addValidSpeaker(dto);
                uploadSpeakerDtoList.add(dto);
            }

            // 4) Push to service
            uploadSpeakerCSVOrZapier(uploadSpeakerDtoList.toArray(new UploadSpeakerDto[0]), event, user, languageMap)
                    .getInvalidSpeakers()
                    .forEach(invalid -> response.addInvalidSpeakers(invalid.invalidSpeakers, invalid.errorMessage));

        }catch (NotAcceptableException e) {
            log.info("CSV upload failed: {} | EventId={} | UserId={} ",
                    e.getMessage(), event.getEventId(), user.getUserId(), e);
            throw e;
        } catch (IOException e) {
            log.error("IO error reading CSV for eventId {}: {}", eventId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error parsing CSV for eventId {}: {}",    eventId, e.getMessage(), e);
            throw new IOException("Unexpected error parsing CSV: " + e.getMessage(), e);
        }

        return response;
    }


    boolean isValidFirstNameAndLastNameAndEmailAndTitleAndCompany( // NOSONAR
                                                                   UploadedSpeakerResponseContainer response,
                                                                   String firstName, String lastName, String email, String pronouns,
                                                                   String title, String company, String bio, String linkedIn,
                                                                   String instagramHandle, String twitterHandle, String overrideProfileDetails,
                                                                   Event event, User user, Map<String, String> languageMap,
                                                                   boolean isUpdateRow,
                                                                   List<String> existingEmailsForCreates,
                                                                   Map<String, Integer> emailCountsForCreateRowsNormalized,
                                                                   Long speakerIdLong,
                                                                   List<AttributeKeyValueDto> attributeKeyValueDtoList,
                                                                   List<Long> primaryIds,List<Long> secondaryIds, boolean isPrimarySessionInvalid, boolean isSecondarySessionInvalid) {
        String errorMessage = null;

        String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode);
        List<Long> allSessionsIdsRelatedToEventId = roSessionRepoService.findSessionIdsByEventId(event.getEventId());

        boolean isFirstNameIsValid = StringUtils.isNotBlank(firstName)
                && StringUtils.defaultIfBlank(firstName, Constants.STRING_EMPTY).length() <= 50;

        boolean isLasNameIsValid = StringUtils.isNotBlank(lastName)
                && StringUtils.defaultIfBlank(lastName, Constants.STRING_EMPTY).length() <= 50;

        boolean isEmailIsValid = StringUtils.isNotBlank(email)
                && StringUtils.defaultIfBlank(email, Constants.STRING_EMPTY).length() <= 75;

        boolean isTitleIsValid = StringUtils.defaultIfBlank(title, Constants.STRING_EMPTY).length() <= 255;
        boolean isCompanyIsValid = StringUtils.defaultIfBlank(company, Constants.STRING_EMPTY).length() <= 255;

        boolean isEmailValidFormat = GeneralUtils.isValidEmailAddress(
                StringUtils.defaultIfBlank(email, Constants.STRING_EMPTY));

        // CSV duplicate detection (CREATE rows only)
        boolean isEmailDuplicateInCsv = false;
        if (!isUpdateRow && StringUtils.isNotBlank(email) && emailCountsForCreateRowsNormalized != null) {
            String norm = StringUtils.lowerCase(StringUtils.trim(email));
            Integer cnt = emailCountsForCreateRowsNormalized.get(norm);
            isEmailDuplicateInCsv = (cnt != null && cnt > 1);
        }

        // DB duplicate (CREATE rows only)
        boolean isEmailAlreadyExistingInDB = !isUpdateRow &&
                existingEmailsForCreates != null &&
                existingEmailsForCreates.contains(StringUtils.defaultIfBlank(email, Constants.STRING_EMPTY).trim());

        boolean isLinkedIsValid = !StringUtils.isNotBlank(linkedIn) || CommonUtil.checkSpeakerLinkedUrlIsValidOrNot(linkedIn);
        boolean isInstagramIsValid = !StringUtils.isNotBlank(instagramHandle) || CommonUtil.checkSpeakerInstagramUrlIsValidOrNot(instagramHandle);
        boolean isTwitterIsValid = !StringUtils.isNotBlank(twitterHandle) || CommonUtil.checkSpeakerTwitterUrlIsValidOrNot(twitterHandle);

        boolean containsInvalidSessionIds = isPrimarySessionInvalid || isSecondarySessionInvalid;
        boolean isPrimarySessionIdIsValid = false;
        boolean isSecondarySessionIdIsValid = false;
        boolean isSessionIdsIsNotConflicts = false;
        StringBuilder primaryDuplicateMsg = new StringBuilder();
        StringBuilder secondaryDuplicateMsg = new StringBuilder();
        StringBuilder primaryInvalidMsg = new StringBuilder();
        StringBuilder secondaryInvalidMsg = new StringBuilder();
        if (!isPrimarySessionInvalid) {
            isPrimarySessionIdIsValid = isValidSessionField(primaryIds, allSessionsIdsRelatedToEventId, primaryDuplicateMsg, primaryInvalidMsg);
        }

        if (!isSecondarySessionInvalid) {
            isSecondarySessionIdIsValid = isValidSessionField(secondaryIds, allSessionsIdsRelatedToEventId, secondaryDuplicateMsg, secondaryInvalidMsg);
        }

        if (isPrimarySessionIdIsValid && isSecondarySessionIdIsValid) {
            isSessionIdsIsNotConflicts = Collections.disjoint(primaryIds, secondaryIds);
        }

        boolean isFileIsInValid =
                !isFirstNameIsValid ||
                        !isLasNameIsValid ||
                        !isEmailIsValid ||
                        !isEmailValidFormat ||
                        !isTitleIsValid ||
                        !isCompanyIsValid ||
                        !isLinkedIsValid ||
                        !isInstagramIsValid ||
                        !isTwitterIsValid ||
                        isEmailDuplicateInCsv ||
                        isEmailAlreadyExistingInDB ||
                        !isPrimarySessionIdIsValid ||
                        !isSecondarySessionIdIsValid ||
                        !isSessionIdsIsNotConflicts;

        String keyName = null;

        if (!isFirstNameIsValid) {
            keyName = Constants.FIRST_NAME;
            errorMessage = resourceBundle.getString(languageMap.get(FIRST_NAME_NOT_BE_NULL));
        } else if (!isLasNameIsValid) {
            keyName = Constants.LAST_NAME;
            errorMessage = resourceBundle.getString(languageMap.get(LAST_NAME_NOT_BE_NULL));
        } else if (!isEmailIsValid) {
            keyName = EMAIL;
            errorMessage = resourceBundle.getString(languageMap.get(EMAIL_NOT_BE_NULL));
        } else if (!isEmailValidFormat) {
            keyName = EMAIL;
            errorMessage = resourceBundle.getString(languageMap.get(EMAIL_NOT_VALID));
        }  else if (!isTitleIsValid) {
            keyName = Title_WITH_BIG_CASE;
            errorMessage = resourceBundle.getString(languageMap.get(TITAL_NOT_BE_NULL));
        } else if (!isCompanyIsValid) {
            keyName = COMPANY;
            errorMessage = resourceBundle.getString(languageMap.get(COMPANY_MUST_BE_NOT_NULL));
        } else if (!isLinkedIsValid) {
            keyName = LINKEDIN_URL;
            errorMessage = resourceBundle.getString(languageMap.get(LINKDIN_URL_NOT_VALID));
        } else if (!isInstagramIsValid) {
            keyName = INSTAGRAM_HANDLE;
            errorMessage = resourceBundle.getString(languageMap.get(INSTAGRAM_URL_NOT_VALID));
        } else if (!isTwitterIsValid) {
            keyName = TWITTER_HANDLE;
            errorMessage = resourceBundle.getString(languageMap.get(TWITTER_URL_NOT_VALID));
        } else if (isEmailAlreadyExistingInDB) {
            keyName = EMAIL;
            errorMessage = resourceBundle.getString(languageMap.get(EMAIL_ALREADY_DB))
                    .replace(EMAIL_ADDRESS_PARAMETER, email);
        }else if (containsInvalidSessionIds) {
            keyName = SESSIONS_IDS;
            errorMessage = resourceBundle.getString(languageMap.get(SESSION_IDS_INVALID));
        } else if (!isPrimarySessionIdIsValid) {
            keyName = PRIMARY_SESSIONS;
            if(primaryInvalidMsg.length() > 0) {
                errorMessage = resourceBundle.getString(languageMap.get(PRIMARY_SESSION_INVALID)) + " Invalid IDs: " + primaryInvalidMsg;
            } else {
                errorMessage = resourceBundle.getString(languageMap.get(PRIMARY_SESSION_DUPLICATE)) + " Duplicate IDs: " + primaryDuplicateMsg;
            }
        } else if (!isSecondarySessionIdIsValid) {
            keyName = SECONDARY_SESSIONS;
            if(secondaryInvalidMsg.length() > 0) {
                errorMessage = resourceBundle.getString(languageMap.get(SECONDARY_SESSION_INVALID)) + " Invalid IDs: " + secondaryInvalidMsg;
            } else {
                errorMessage = resourceBundle.getString(languageMap.get(SECONDARY_SESSION_DUPLICATE)) + " Duplicate IDs: " + secondaryDuplicateMsg;
            }
        } else if (!isSessionIdsIsNotConflicts) {
            keyName = SESSIONS_IDS;
            errorMessage = resourceBundle.getString(languageMap.get(PRIMARY_SECONDARY_CONFLICT));
        } else if (isEmailDuplicateInCsv) {
            keyName = Constants.EMAIL;
            errorMessage = resourceBundle.getString(languageMap.get(Constants.EMAIL_ALREADY_IN_CSV_FILE)).replace(Constants.EMAIL_ADDRESS_PARAMETER, email);
        }

        if (isFileIsInValid) {
            log.info(errorMessage);
            log.info("{} = {}", keyName, email);
            response.addInvalidSpeakers(
                    new UploadSpeakerDto(firstName, lastName, email, pronouns, title, company, bio,
                            linkedIn, twitterHandle, instagramHandle, overrideProfileDetails, isEmailAlreadyExistingInDB,primaryIds, secondaryIds,speakerIdLong,attributeKeyValueDtoList,containsInvalidSessionIds,!isPrimarySessionIdIsValid, !isSecondarySessionIdIsValid, !isSessionIdsIsNotConflicts,false),
                    errorMessage
            );
        }
        return !isFileIsInValid;
    }

    private boolean isEmailAddressAlreadyPresent(String email, UploadedSpeakerResponseContainer response) {
        return response.getValidSpeakers().stream().anyMatch(uploadSpeakerDto -> uploadSpeakerDto.getEmail().contentEquals(email));
    }

    @Override
    @Transactional
    public UploadedSpeakerResponseContainer uploadSpeakerCSVOrZapier(
            UploadSpeakerDto[] speakerDTOS,
            Event event,
            User oldUser,
            Map<String, String> languageMap
    ) {
        UploadedSpeakerResponseContainer response = new UploadedSpeakerResponseContainer();
        List<Long> speakerIds = new ArrayList<>();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(oldUser, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode);
        Map<Speaker, UploadSpeakerDto> speakersToMap = new HashMap<>();

        this.restrictSpeakerCountBasedOnPlanType(event, speakerDTOS.length);

        List<Long> allSessionsIdsRelatedToEventId = roSessionRepoService.findSessionIdsByEventId(event.getEventId());

        // 1) Collect all update speakerIds
        List<Long> speakerIdsInFile = Arrays.stream(speakerDTOS)
                .filter(dto -> dto.getSpeakerId() != null && NumberUtils.isValidPositiveNumber(String.valueOf(dto.getSpeakerId())))
                .map(UploadSpeakerDto::getSpeakerId)
                .collect(Collectors.toList());

        // 2) Fetch all existing speakers in one DB call
        final List<Speaker> existingSpeakers = speakerIdsInFile.isEmpty()
                ? Collections.emptyList()
                : speakerRepoService.getSpeakerWithUserAndAttributeDataByEventIdAndIds(event.getEventId(), speakerIdsInFile);

        final Map<Long, Speaker> existingSpeakerMap = existingSpeakers.stream()
                .collect(Collectors.toMap(Speaker::getId, s -> s, (a, b) -> a));

        List<CustomFormAttribute> customFormAttributeList = customFormAttributeService.getCustomFormAttributeOfEvent(event, oldUser, null, AttributeType.SPEAKER);

        // 3) Process
        for (UploadSpeakerDto uploadSpeakerDto : speakerDTOS) {
            try {
                boolean updateSpeaker = uploadSpeakerDto.getSpeakerId() != null && NumberUtils.isValidPositiveNumber(String.valueOf(uploadSpeakerDto.getSpeakerId()));
                boolean linkedInValid = StringUtils.isBlank(uploadSpeakerDto.getLinkedIn())
                        || CommonUtil.checkSpeakerLinkedUrlIsValidOrNot(uploadSpeakerDto.getLinkedIn());
                 boolean instagramValid = StringUtils.isBlank(uploadSpeakerDto.getInstagram())
                        || CommonUtil.checkSpeakerInstagramUrlIsValidOrNot(uploadSpeakerDto.getInstagram());
                boolean twitterValid = StringUtils.isBlank(uploadSpeakerDto.getTwitter())
                        || CommonUtil.checkSpeakerTwitterUrlIsValidOrNot(uploadSpeakerDto.getTwitter());
                boolean isPrimarySessionIdIsValid = false;
                boolean isSecondarySessionIdIsValid = false;
                boolean isSessionIdsIsNotConflicts = false;
                StringBuilder primaryDuplicateMsg = new StringBuilder();
                StringBuilder secondaryDuplicateMsg = new StringBuilder();
                StringBuilder primaryInvalidMsg = new StringBuilder();
                StringBuilder secondaryInvalidMsg = new StringBuilder();
                isPrimarySessionIdIsValid = isValidSessionField(uploadSpeakerDto.getPrimarySessions(), allSessionsIdsRelatedToEventId,primaryDuplicateMsg,primaryInvalidMsg);
                isSecondarySessionIdIsValid = isValidSessionField(uploadSpeakerDto.getSecondarySessions(), allSessionsIdsRelatedToEventId,secondaryDuplicateMsg, secondaryInvalidMsg);
                isSessionIdsIsNotConflicts = Collections.disjoint(uploadSpeakerDto.getPrimarySessions(), uploadSpeakerDto.getSecondarySessions());
                uploadSpeakerDto.setIsEmailAlreadyPresent(false);
                uploadSpeakerDto.setSpeakerIdIsInvalid(false);
                if (!linkedInValid || !instagramValid || !twitterValid || !isPrimarySessionIdIsValid || !isSecondarySessionIdIsValid || !isSessionIdsIsNotConflicts) {
                    UploadSpeakerDto speakerDTO = new UploadSpeakerDto(createSpeakerForCsvUpload(uploadSpeakerDto),uploadSpeakerDto.getAttributeKeyValueDtos());
                    String errorMessage = null;
                    if (!linkedInValid) {
                        errorMessage = resourceBundle.getString(languageMap.get(LINKDIN_URL_NOT_VALID));
                    } else if (!instagramValid) {
                        errorMessage = resourceBundle.getString(languageMap.get(INSTAGRAM_URL_NOT_VALID));
                    } else if (!twitterValid) {
                        errorMessage = resourceBundle.getString(languageMap.get(TWITTER_URL_NOT_VALID));
                    } else if (uploadSpeakerDto.isNumbericSessionIds()) {
                        errorMessage = resourceBundle.getString(languageMap.get(SESSION_IDS_INVALID));
                    } else if (!isPrimarySessionIdIsValid) {
                        if(primaryInvalidMsg.length() > 0) {
                            errorMessage = resourceBundle.getString(languageMap.get(PRIMARY_SESSION_INVALID)) + " Invalid IDs: " + primaryInvalidMsg;
                        } else {
                            errorMessage = resourceBundle.getString(languageMap.get(PRIMARY_SESSION_DUPLICATE)) + " Duplicate IDs: " + primaryDuplicateMsg;
                        }
                    } else if (!isSecondarySessionIdIsValid) {
                        if(secondaryInvalidMsg.length() > 0) {
                            errorMessage = resourceBundle.getString(languageMap.get(SECONDARY_SESSION_INVALID)) + " Invalid IDs: " + secondaryInvalidMsg;
                        } else {
                            errorMessage = resourceBundle.getString(languageMap.get(SECONDARY_SESSION_DUPLICATE)) + " Duplicate IDs: " + secondaryDuplicateMsg;
                        }
                    } else if (!isSessionIdsIsNotConflicts) {
                        errorMessage = resourceBundle.getString(languageMap.get(PRIMARY_SECONDARY_CONFLICT));
                    }
                    speakerDTO.setPrimarySessionIdIsInvalid(!isPrimarySessionIdIsValid);
                    speakerDTO.setSecondarySessionIdIsInvalid(!isSecondarySessionIdIsValid);
                    speakerDTO.setSessionIdsIsConflicts(!isSessionIdsIsNotConflicts);
                    response.addInvalidSpeakers(speakerDTO, errorMessage);
                    continue;
                }

                // NEW: Email + event-specific validation
                try {
                    validateSpeakerEmailForEvent(uploadSpeakerDto, event);
                } catch (IllegalArgumentException ex) {
                    response.addInvalidSpeakers(uploadSpeakerDto, ex.getMessage());
                    continue; // skip this row
                }


                // UPDATE flow
                if (updateSpeaker
                        && uploadSpeakerDto.getSpeakerId() != null) {

                    Speaker existingSpeaker = existingSpeakerMap.get(uploadSpeakerDto.getSpeakerId());
                    if (existingSpeaker == null) {
                        response.addInvalidSpeakers(uploadSpeakerDto, Constants.SpeakerImportMessage.SPEAKER_ID_NOT_FOUND_TO_UPDATE_FOR_EVENT.getStatus());
                        continue;
                    }

                    // Apply updates (non-email fields)
                    existingSpeaker.setFirstName(uploadSpeakerDto.getFirstName());
                    existingSpeaker.setLastName(uploadSpeakerDto.getLastName());
                    existingSpeaker.setPronouns(uploadSpeakerDto.getPronouns());
                    existingSpeaker.setTitle(uploadSpeakerDto.getTitle());
                    existingSpeaker.setCompany(uploadSpeakerDto.getCompany());
                    existingSpeaker.setBio(uploadSpeakerDto.getBio());
                    existingSpeaker.setLinkedIn(StringUtils.trimToNull(uploadSpeakerDto.getLinkedIn()));
                    existingSpeaker.setInstagram(StringUtils.trimToNull(uploadSpeakerDto.getInstagram()));
                    existingSpeaker.setTwitter(StringUtils.trimToNull(uploadSpeakerDto.getTwitter()));
                    existingSpeaker.setAllowOverrideDetails(
                            uploadSpeakerDto.getOverrideProfileDetails() != null
                                    ? uploadSpeakerDto.getOverrideProfileDetails()
                                    : Boolean.FALSE
                    );
                    existingSpeaker.setSpeakerAttributeId(createOrUpdateSpeakerCustomAttributes(existingSpeaker, uploadSpeakerDto.getAttributeKeyValueDtos(), customFormAttributeList));
                    uploadSpeakerDto.setPrimarySessionIdIsInvalid(!isPrimarySessionIdIsValid);
                    uploadSpeakerDto.setSecondarySessionIdIsInvalid(!isSecondarySessionIdIsValid);
                    uploadSpeakerDto.setSessionIdsIsConflicts(!isSessionIdsIsNotConflicts);
                    save(existingSpeaker);

                    speakersToMap.put(existingSpeaker, uploadSpeakerDto);
                    createOrUpdateSpeakerCustomAttributes(existingSpeaker, uploadSpeakerDto.getAttributeKeyValueDtos(), customFormAttributeList);
                    speakerIds.add(existingSpeaker.getId());
                    response.addValidSpeaker(uploadSpeakerDto);
                    continue;
                }

                // CREATE flow: normalize email and check uniqueness
                String uploadedEmailRaw = uploadSpeakerDto.getEmail().trim();

                if (StringUtils.isNotBlank(uploadedEmailRaw)
                        && speakerRepoService.checkSpeakerAlreadyExistByEmail(uploadedEmailRaw, event.getEventId())) {
                    UploadSpeakerDto speakerDTO = new UploadSpeakerDto(createSpeakerForCsvUpload(uploadSpeakerDto),uploadSpeakerDto.getAttributeKeyValueDtos());
                    String errorMessage;
                    try {
                        errorMessage = resourceBundle.getString(languageMap.get(EMAIL_ALREADY_EXIST));
                        errorMessage = errorMessage.replace(EMAIL_ADDRESS_PARAMETER, speakerDTO.getEmail());
                    } catch (Exception ex) {
                        errorMessage = speakerDTO.getEmail() + " Email Already exist.";
                    }
                    speakerDTO.setIsEmailAlreadyPresent(Boolean.TRUE);
                    response.addInvalidSpeakers(speakerDTO, errorMessage);
                    continue;
                }

                // Create user & speaker
                User user = getOrCreateUserFromUploadSpeakerDto(uploadSpeakerDto, event);
                Speaker speaker = uploadSpeakerDto.createEntity(event, user);
                speaker.setEmail(user.getEmail());
                speaker.setImageUrl(user.getPhoto());
                speaker.setAllowOverrideDetails(uploadSpeakerDto.getOverrideProfileDetails() != null ? uploadSpeakerDto.getOverrideProfileDetails() : Boolean.FALSE);
                setPositionForSpeaker(speaker);

                speaker.setSpeakerAttributeId(createOrUpdateSpeakerCustomAttributes(speaker, uploadSpeakerDto.getAttributeKeyValueDtos(), customFormAttributeList));
                speaker = save(speaker);
                speakerIds.add(speaker.getId());
                speakersToMap.put(speaker, uploadSpeakerDto);

                neptuneSyncService.addStaffInNeptuneAsync(user.getUserId(), event,
                        checkInAuditLogService.countPreviousCheckIn(event, user.getUserId()) == 0);

                getStreamService.addMemberToChannel(
                        ADMIN_DES_.concat(String.valueOf(event.getEventId())),
                        DES_MESSAGING,
                        Collections.singletonList(user.getUserId())
                );

                uploadSpeakerDto.setIsEmailAlreadyPresent(Boolean.FALSE);
                response.addValidSpeaker(uploadSpeakerDto);

            } catch (Exception e) {
                log.error("Error processing speaker row: {}", e.getMessage(), e);
                response.addInvalidSpeakers(uploadSpeakerDto, "Failed to process row: " + (e.getMessage() == null ? "Unexpected error" : e.getMessage()));
            }
        }
        if (!speakersToMap.isEmpty()) {
            saveSessionSpeakersFromCsvUpload(speakersToMap);
        }

        if (!speakerIds.isEmpty()) {
            log.info("updateEventTaskAndAssignTaskToSpeaker | eventId : {} , speakerIds : {}", event.getEventId(), speakerIds);
            eventTaskService.updateEventTaskAndAssignTaskToSpeaker(event, speakerIds);
        }
        return response;
    }

    @Transactional
    public void saveSessionSpeakersFromCsvUpload(Map<Speaker, UploadSpeakerDto> uploadSpeakerDtoMap) {
        if (uploadSpeakerDtoMap == null || uploadSpeakerDtoMap.isEmpty()) {
            return;
        }

        for (Map.Entry<Speaker, UploadSpeakerDto> entry : uploadSpeakerDtoMap.entrySet()) {
            Speaker speaker = entry.getKey();
            UploadSpeakerDto dto = entry.getValue();

            // 1. Fetch existing mappings for this speaker
            List<SessionSpeaker> dbMappings = roSessionSpeakerService.findBySpeakerId(speaker.getId());

            // Split DB into primary/secondary
            Set<Long> dbPrimary = dbMappings.stream()
                    .filter(SessionSpeaker::isModerator)
                    .map(SessionSpeaker::getSessionId)
                    .collect(Collectors.toSet());

            Set<Long> dbSecondary = dbMappings.stream()
                    .filter(ss -> !ss.isModerator())
                    .map(SessionSpeaker::getSessionId)
                    .collect(Collectors.toSet());

            // 2. CSV-provided IDs (normalize null → empty set)
            Set<Long> csvPrimary = dto.getPrimarySessions() == null ? Collections.emptySet()
                    : new HashSet<>(dto.getPrimarySessions());
            Set<Long> csvSecondary = dto.getSecondarySessions() == null ? Collections.emptySet()
                    : new HashSet<>(dto.getSecondarySessions());

            // 3. Diff analysis
            // (a) Sessions to remove → in DB but not in CSV at all
            Set<Long> toRemove = new HashSet<>();
            toRemove.addAll(dbPrimary);
            toRemove.addAll(dbSecondary);
            toRemove.removeAll(csvPrimary);
            toRemove.removeAll(csvSecondary);

            // (b) Moderator flips
            Set<Long> flipToPrimary = new HashSet<>(csvPrimary);
            flipToPrimary.retainAll(dbSecondary); // move secondary → primary

            Set<Long> flipToSecondary = new HashSet<>(csvSecondary);
            flipToSecondary.retainAll(dbPrimary); // move primary → secondary

            // (c) New inserts → CSV has, DB doesn’t, and not in flip sets
            Set<Long> toAddPrimary = new HashSet<>(csvPrimary);
            toAddPrimary.removeAll(dbPrimary);
            toAddPrimary.removeAll(flipToPrimary); // exclude flips

            Set<Long> toAddSecondary = new HashSet<>(csvSecondary);
            toAddSecondary.removeAll(dbSecondary);
            toAddSecondary.removeAll(flipToSecondary); // exclude flips

            // 4. Apply changes
            double position = 1000d;

            // Remove obsolete mappings
            if (!toRemove.isEmpty()) {
                sessionSpeakerRepoService.deleteBySpeakerIdAndSessionIds(
                        speaker.getId(),
                        new ArrayList<>(toRemove)
                );
                dbMappings.removeIf(ss -> toRemove.contains(ss.getSessionId()));
                log.info("Removed {} obsolete sessions for speakerId={}", toRemove.size(), speaker.getId());
            }

            // Prepare new inserts
            List<SessionSpeaker> toInsert = new ArrayList<>();

            for (Long sessionId : toAddPrimary) {
                toInsert.add(new SessionSpeaker(sessionId, speaker.getId(), position, true));
                position += 1000d;
            }
            for (Long sessionId : toAddSecondary) {
                toInsert.add(new SessionSpeaker(sessionId, speaker.getId(), position, false));
                position += 1000d;
            }

            // Apply flips (update existing rows)
            for (Long sessionId : flipToPrimary) {
                dbMappings.stream()
                        .filter(ss -> ss.getSessionId().equals(sessionId) && !ss.isModerator())
                        .forEach(ss -> ss.setModerator(true));
            }
            for (Long sessionId : flipToSecondary) {
                dbMappings.stream()
                        .filter(ss -> ss.getSessionId().equals(sessionId) && ss.isModerator())
                        .forEach(ss -> ss.setModerator(false));
            }

            // Save updates and inserts
            if (!dbMappings.isEmpty()) {
                sessionSpeakerService.saveAll(new HashSet<>(dbMappings)); // updates
            }
            if (!toInsert.isEmpty()) {
                sessionSpeakerService.saveAll(new HashSet<>(toInsert));   // inserts
            }

            log.info(
                    "Synced sessions for speakerId={} → Added: {}, Removed: {}, FlippedToPrimary: {}, FlippedToSecondary: {}",
                    speaker.getId(),
                    toInsert.size(),
                    toRemove.size(),
                    flipToPrimary.size(),
                    flipToSecondary.size()
            );
        }
    }



    private void validateSpeakerEmailForEvent(UploadSpeakerDto uploadSpeakerDto, Event event) {
        Long speakerId = uploadSpeakerDto.getSpeakerId();
        String email = StringUtils.trimToNull(uploadSpeakerDto.getEmail());

        // UPDATE case
        if (speakerId != null && speakerId != 0) {
            Optional<Speaker> speakerOptional =
                    roSpeakerService.findByIdAndEventId(speakerId, event.getEventId());

            if (speakerOptional.isEmpty()) {
                uploadSpeakerDto.setSpeakerIdIsInvalid(true);
                throw new IllegalArgumentException(
                        "Speaker ID " + speakerId + " not found for this event"
                );
            }

            Speaker existingSpeaker = speakerOptional.get();
            if (email == null) {
                throw new IllegalArgumentException("Email required when updating speakerId=" + speakerId);
            }

            if (!existingSpeaker.getEmail().equalsIgnoreCase(email)) {
                throw new IllegalArgumentException("Email change not allowed for speakerId=" + speakerId);
            }
            return;
        }

        // CREATE case
        if (email == null) {
            throw new IllegalArgumentException("Email required for new speaker row");
        }

        if (roSpeakerService.existsByEmailAndEventId(email, event.getEventId())) {
            uploadSpeakerDto.setIsEmailAlreadyPresent(true);
            throw new IllegalArgumentException("Email " + email + " already exists for this event");
        }
    }


    private Speaker createSpeakerForCsvUpload(UploadSpeakerDto speakerDTO) {
        Speaker updatedSpeaker = new Speaker();
        updatedSpeaker.setFirstName(speakerDTO.getFirstName());
        updatedSpeaker.setLastName(speakerDTO.getLastName());
        updatedSpeaker.setEmail(speakerDTO.getEmail());
        updatedSpeaker.setBio(speakerDTO.getBio());
        updatedSpeaker.setAllowOverrideDetails(speakerDTO.getOverrideProfileDetails() != null ? speakerDTO.getOverrideProfileDetails() : Boolean.FALSE);
        updatedSpeaker.setLinkedIn(speakerDTO.getLinkedIn());
        updatedSpeaker.setInstagram(speakerDTO.getInstagram());
        updatedSpeaker.setTwitter(speakerDTO.getTwitter());
        updatedSpeaker.setCompany(speakerDTO.getCompany());
        updatedSpeaker.setTitle(speakerDTO.getTitle());
        return updatedSpeaker;
    }

    public User getOrCreateUserFromUploadSpeakerDto(UploadSpeakerDto speakerDTO, Event event) {
        UserSignupDto userSignupDto = new UserSignupDto();
        userSignupDto.setEmail(speakerDTO.getEmail());
        userSignupDto.setFirstName(speakerDTO.getFirstName());
        userSignupDto.setLastName(speakerDTO.getLastName());
        return userService.signUpBidderUserAndReturnUser(userSignupDto, event, true, false, false, false,
                true);
    }

    @Override
    @Transactional
    public void updateSpeakerSequence(Long speakerId, Long topSpeakerId, Long topBottomSpeakerId, Event event, User user) {
        Speaker exhibitor = getSpeakerById(speakerId, event);

        updateWithSequence(exhibitor, topSpeakerId, topBottomSpeakerId, event,user);
    }

    @Override
    public List<Speaker> findSpeakerByEventId(Event event) {
        return speakerRepoService.findSpeakerByEventId(event.getEventId());
    }

    @Override
    public List<Long> findSpeakerUserIdsByEventId(Event event) {
        return speakerRepoService.findSpeakerUserIdsByEventId(event.getEventId());
    }

    private void updateWithSequence(Speaker speaker,
                                    Long topId,
                                    Long topBottomId,
                                    Event event, User user) {

        Optional<Speaker> topSpeakerOpt = speakerRepoService.findById(topId);
        Optional<Speaker> topNextSpeakerOpt = speakerRepoService.findById(topBottomId);

        log.info("request received for change speaker position|updateWithSequence|Event {}|speaker {}| current Position {}| user {}",event.getEventId(),speaker.getId(),speaker.getPosition(),user.getUserId());
        //move to middle
        if (topSpeakerOpt.isPresent() && topNextSpeakerOpt.isPresent()) {
            Speaker topNextSpeaker = topNextSpeakerOpt.get();
            Speaker topSpeaker = topSpeakerOpt.get();
            double position = (topNextSpeaker.getPosition() + topSpeaker.getPosition()) / 2;
            if (1 == position) {
                Speaker nextSpeaker = getNextPositionItem(speaker,event);
                Speaker prevSpeaker = getPreviousPositionItem(speaker,event);
                if(nextSpeaker != null && prevSpeaker != null){
                    double positionDifferent = (nextSpeaker.getPosition() - prevSpeaker.getPosition()) / 2;
                    speakerRepoService.updatePositionSpeaker(event.getEventId(),topSpeaker.getPosition(), speaker.getPosition(), positionDifferent);
                    speaker.setPosition(topNextSpeaker.getPosition());
                    save(speaker);
                }
            } else {
                speaker.setPosition(position);
                save(speaker);
            }
            //move to top
        } else if (!topSpeakerOpt.isPresent() && topNextSpeakerOpt.isPresent()) {
            Speaker topNextSpeaker = topNextSpeakerOpt.get();
            speaker.setPosition(topNextSpeaker.getPosition() + sequence);
            save(speaker);
            //move at last
        } else if (topSpeakerOpt.isPresent()) {
            Speaker topSpeaker = topSpeakerOpt.get();
            double posDiff = topSpeaker.getPosition() - sequence;
            if (posDiff <= 1) {
                speakerRepoService.updatePositionForAllSpeakerByEventId(sequence, event.getEventId());
            }
            speaker.setPosition(sequence);
            save(speaker);
        }
        log.info("successfully changed speaker position|updateWithSequence|Event {}|speaker {}| updated Position {}| user {}",event.getEventId(),speaker.getId(),speaker.getPosition(),user.getUserId());
    }

    private Speaker getNextPositionItem(Speaker speaker,Event event) {
        List<Speaker> nextPositionItemList = speakerRepoService.nextPositionSpeaker(speaker.getId(),event.getEventId(),speaker.getPosition());
        return nextPositionItemList.isEmpty() ? null: nextPositionItemList.get(0);
    }

    private Speaker getPreviousPositionItem(Speaker speaker,Event event) {
        List<Speaker> previousPositionExhibitorList = speakerRepoService.previousPositionSpeaker(speaker.getId(),event.getEventId(),speaker.getPosition());
        return  previousPositionExhibitorList.isEmpty() ? null: previousPositionExhibitorList.get(0);
    }

    @Override
    public Speaker setPositionForSpeaker(Speaker speaker) {
        if (speaker.getId() == 0) {

            Speaker lastItem = getLastItem(speaker);
            speaker.setPosition(lastItem != null ? lastItem.getPosition() + sequence : sequence);
        }
        return speaker;
    }

    @Override
    public List<Speaker> getSpeakerBySamePosition(double position, Event event) {
        return speakerRepoService.getSpeakerBySamePosition(position, event.getEventId());
    }

    private Speaker getLastItem(Speaker speaker) {
        return speakerRepoService.findFirstByEventIdOrderByPositionDesc(speaker.getEventId());
    }


    @Override
    public List<Speaker> findSpeakerByEmail(String email) {
        return speakerRepoService.findByEmail(email);
    }

    @Override
    public FirstLastNameDto getUserInfoByEmail(String email) {
        return userService.getUserInfoByEmail(email);
    }

    @Override
    public DataTableResponse updateSpeakerSequenceSortByName(Event event, String columnName, boolean isAscending, int page, int size) {
        Sort by;
        if (isAscending) {
            by = Sort.by(columnName);
        } else {
            by = Sort.by(columnName).descending();
        }
        List<Speaker> speakers = findSpeakerByEventId(event);
        AtomicInteger counter = new AtomicInteger();

        Comparator<Speaker> speakerComparator = getSpeakerComparator(columnName);
        if (isAscending) {
            speakerComparator = speakerComparator.reversed();
        }
        List<Speaker> speakerList = speakers.stream().sorted(speakerComparator).collect(Collectors.toList());
        if (!speakerList.isEmpty()) {
            speakerList.forEach(speaker -> {
                speaker.setPosition(sequence);
                sequence = counter.incrementAndGet() + sequence;
                speakerRepoService.save(speaker);
            });
        }
        speakerList.sort(Comparator.comparing(Speaker::getPosition).reversed());
        PageImpl<Speaker> speakerPage = PageUtil.getPageable(speakerList, PageRequest.of(Math.max(page, 0), (size>0)?size:10, by));
        List<Long> speakerUserIds = speakerPage.getContent().stream().map(Speaker::getUserId).collect(Collectors.toList());
        Map<Long, Boolean> speakerDeviceChecker = deviceCheckerService.speakerIdAndRunDeviceChecker(speakerUserIds);
        List<SpeakerDTO> speakerDTOs = speakerPage.getContent().stream()
                .map(e -> new SpeakerDTO(e, event, null,  speakerDeviceChecker.get(e.getUserId()), 0L, null))
                .collect(Collectors.toList());

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(speakerPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(speakerPage.getContent().size());
        dataTableResponse.setData(speakerDTOs);
        return dataTableResponse;
    }

    private Comparator<Speaker> getSpeakerComparator(String columnName) {
        if (columnName == null || columnName.equals(Constants.STRING_FIRST_NAME))
            return Comparator.comparing(Speaker::getFirstName, Comparator.nullsLast(Comparator.naturalOrder()));
        else if (columnName.equals(STRING_LAST_NAME))
            return Comparator.comparing(Speaker::getLastName, Comparator.nullsLast(Comparator.naturalOrder()));
        else if (columnName.equals(SMALL_CASE_COMPANY))
            return Comparator.comparing(Speaker::getCompany, Comparator.nullsLast(Comparator.naturalOrder()));
        else if (columnName.equals(SMALL_CASE_TITLE))
            return Comparator.comparing(Speaker::getTitle, Comparator.nullsLast(Comparator.naturalOrder()));
        else
            return Comparator.comparing(Speaker::getFirstName, Comparator.nullsLast(Comparator.naturalOrder()));
    }

    @Override
    public Long getEventSpeakerCount(Long eventId) {
        return speakerRepoService.getEventSpeakerCount(eventId);
    }

    @Override
    public Long findSpeakerIdByEventIdAndUserId(Event event, User user) {
        Optional<Long> speakerIdByEventIdAndUserId = speakerRepoService.findSpeakerIdByEventIdAndUserId(event.getEventId(), user.getUserId());
        return speakerIdByEventIdAndUserId.orElse(null);
    }

    private Map<Long, Map<Long, Boolean>> getSpeakerTickets(Event event, List<Long> userIds) {
        List<SpeakerTicketTypeDTO> idCountDtos = eventTicketsService.findUsersTicketsByEventAndUserIds(event, userIds);
        Map<Long, Map<Long, Boolean>> userTickets = new HashMap<>();
        if (null != idCountDtos && !idCountDtos.isEmpty()) {
            idCountDtos.forEach(idCountDto -> {
                Long userId = idCountDto.getUserId();
                Map<Long, Boolean> ticketList;
                if (userTickets.containsKey(userId)) {
                    ticketList = userTickets.get(userId);
                } else {
                    ticketList = new HashMap<>();
                }
                if (ticketList.containsKey(idCountDto.getTicketTypeId())) {
                    if (!ticketList.get(idCountDto.getTicketTypeId()) && idCountDto.getSpeakerOrder()) {//NOSONAR
                        ticketList.put(idCountDto.getTicketTypeId(), idCountDto.getSpeakerOrder());//NOSONAR
                    }
                } else {
                    ticketList.put(idCountDto.getTicketTypeId(), idCountDto.getSpeakerOrder());
                }

                userTickets.put(userId, ticketList);
            });
        }

        return userTickets;
    }
    @Override
    public List<SpeakerTicketTypeDTO> assignAttendeeAccessToSpeaker(Event event, List<Long> selectedTicketTypes, Long speakerId, User staffUser, Boolean isAllowAttendeeAccess) throws APIException {
        Speaker speaker = getSpeakerById(speakerId, event);
        List<SpeakerEventTicketDto> currentTicketsDtos = eventTicketsService.findUserTicketAndOrderIdByEventAndUserId(event, speaker.getUserId());
        List<Long> currentTickets = currentTicketsDtos.stream().filter(SpeakerEventTicketDto::getSpeakerOrder).map(SpeakerEventTicketDto::getTicketTypeId).collect(Collectors.toList());//NOSONAR
        List<SpeakerEventTicketDto> currentSpeakerTicketsDtos = currentTicketsDtos.stream().filter(SpeakerEventTicketDto::getSpeakerOrder).map(e -> new SpeakerEventTicketDto(e.getEventTicketId(),e.getTicketTypeId(),e.getOrderId(),e.getSpeakerOrder())).collect(Collectors.toList());//NOSONAR
        List<SpeakerTicketTypeDTO> modifiedTicketsDtos = new ArrayList<>();
        if (isAllowAttendeeAccess != null && !isAllowAttendeeAccess.booleanValue()) {
            deleteSpeakerOrder(speaker, event, currentSpeakerTicketsDtos);
            modifiedTicketsDtos = currentTicketsDtos.stream().filter(eticket ->  !eticket.getSpeakerOrder())
                    .map(eticket -> new  SpeakerTicketTypeDTO(speaker.getUserId(),eticket.getTicketTypeId(),eticket.getSpeakerOrder())).collect(Collectors.toList());
        } else {
            List<Long> speakerTicketTypes = null != selectedTicketTypes ? new ArrayList<>(selectedTicketTypes) : new ArrayList<>();//NOSONAR
            if (null != selectedTicketTypes) {
                selectedTicketTypes.removeAll(currentTickets);
            }
            currentTickets.removeAll(speakerTicketTypes);
            if (null != selectedTicketTypes && !selectedTicketTypes.isEmpty()) {
                log.info("Order need to create for speakerUserId {}", speaker.getUserId());
                ticketingOrderManagerService.createOrderForSpeakerOrStaff(speaker, selectedTicketTypes, event, staffUser);
                modifiedTicketsDtos.addAll(selectedTicketTypes.stream().map(e -> new SpeakerTicketTypeDTO(speaker.getUserId(),e,true)).collect(Collectors.toList()));
            }
            if (!currentTickets.isEmpty()) {
                List<SpeakerEventTicketDto> speakerTicketsDtos =
                        currentSpeakerTicketsDtos.stream()
                                .filter(e -> currentTickets.contains(e.getTicketTypeId())).collect(Collectors.toList());
                List<SpeakerEventTicketDto> notSpeakerTicketsDtos =
                        currentTicketsDtos.stream()
                                .filter(e -> currentTickets.contains(e.getTicketTypeId())&& !e.getSpeakerOrder()).collect(Collectors.toList());
                deleteSpeakerOrder(speaker, event, speakerTicketsDtos);
                modifiedTicketsDtos.addAll(notSpeakerTicketsDtos.stream().map(e -> new SpeakerTicketTypeDTO(speaker.getUserId(),e.getTicketTypeId(),e.getSpeakerOrder())).collect(Collectors.toList()));
            }
            if(!speakerTicketTypes.isEmpty()){
                modifiedTicketsDtos.addAll(speakerTicketTypes.stream().map(e -> new SpeakerTicketTypeDTO(speaker.getUserId(),e,true)).collect(Collectors.toList()));
            }
        }
        log.info("after delete or create order for speaker get modified ticket types");
        speaker.setAllowAttendeeAccess(isAllowAttendeeAccess);
        save(speaker);
        return getSpeakerTicketUnique(modifiedTicketsDtos);
    }
    private void deleteSpeakerOrder(Speaker speaker, Event event, List<SpeakerEventTicketDto> currentTickets) {
        if (!currentTickets.isEmpty()) {
            try {
                currentTickets.forEach(e -> {//NOSONAR
                        commonEventService.updateEventTicketsOrAddOnToDelete(event, e.getOrderId(), e.getEventTicketId(), false, null);
                    });
            } catch (Exception ex) {
                log.info("Error while deleting order for speaker {}", speaker.getId());
            }
        }
    }
    private List<SpeakerTicketTypeDTO> getSpeakerTicketUnique(List<SpeakerTicketTypeDTO> currentTickets) {
        List<SpeakerTicketTypeDTO> currentTicketsUnique = new ArrayList<>();
        Map<Long, List<SpeakerTicketTypeDTO>> ticketTypesForUserMap = currentTickets
                .stream()
                .collect(Collectors.groupingBy(SpeakerTicketTypeDTO::getTicketTypeId));
        log.info("In function get speaker data with distinct Ticket type ");
        for (Map.Entry<Long, List<SpeakerTicketTypeDTO>> entry : ticketTypesForUserMap.entrySet()) {//NOSONAR
            List<SpeakerTicketTypeDTO> tempList = entry.getValue();
            Optional<SpeakerTicketTypeDTO> optTrue = tempList.stream().filter(SpeakerTicketTypeDTO::getSpeakerOrder).findFirst();//NOSONAR
            Optional<SpeakerTicketTypeDTO> optFalse = tempList.stream().filter(sp -> !sp.getSpeakerOrder()).findFirst();
            currentTicketsUnique.add(optTrue.orElseGet(optFalse::get));//NOSONAR
        }
        return currentTicketsUnique;
    }

    @Override
    @Transactional
    public void removeSpeakerProfilePicture(Long speakerId,Event event) {
        Speaker speaker = getSpeakerById(speakerId, event);
        if (speaker.getImageUrl() != null){
            speaker.setImageUrl(null);
            save(speaker);
        }else {
            throw new NotFoundException(NotFoundException.SessionSpeakerNotFound.N0_SPEAKER_PROFILE_PRESENT);
        }
    }

    @Override
    public Boolean checkOverrideProfileEnableForSpeaker(long eventId, Long userId) {
        Speaker speaker = speakerRepoService.findByEventIdAndUserId(eventId, userId);
        return speaker !=null && speaker.getAllowOverrideDetails() !=null ? speaker.getAllowOverrideDetails() : Boolean.FALSE;
    }

    @Override
    public List<Speaker> getSpeakerByEventIDAndUserId(Long eventId, Long userId){
        return speakerRepoService.findSpeakerByEventIdAndUserId(eventId, userId);
    }

    @CacheEvict(value = "findByEventIdAndId",allEntries = true)
    @Override
    public void updateSpeakerEmailAndSendEmailToEventAdmin(String oldEmail, String newEmail) {
        List<Speaker> speakers = speakerRepoService.findByEmail(oldEmail);
        if(!CollectionUtils.isEmpty(speakers)){
            speakers.forEach(e ->e.setEmail(newEmail));
        }
        speakerRepoService.saveAll(speakers);
        speakerHelperService.sendInvite(speakers, oldEmail);

    }

    @Override
    @Transactional
    public void completeSessionOnBoarding(Long speakerId, Event event) {
        log.info("request received to complete session onboarding process");
        Speaker speaker = speakerRepoService.findSpeakerByIdAndEventOptional(event, speakerId).orElseThrow(()->new NotFoundException(SPEAKER_NOT_FOUND));
        speaker.setSpeakerOnBoardingStatus(SpeakerOnBoardingStatus.ONBOARDED);
        speakerRepoService.save(speaker);
        log.info("successfully speker onborded");

    }

    @Override
    public List<SpeakerCountDTO> speakerCountBySpeakerOnBoardingStatus(Event event) {
          return speakerRepoService.speakerCountBySpeakerOnBoardingStatus(event);
    }

    @Override
    public SpeakerWidgetSettingsDTO getSpeakerWidgetSettings(Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());

        SpeakerWidgetSettingsDTO speakerWidgetSettingsDTO = new SpeakerWidgetSettingsDTO();
        if(virtualEventSettings != null){
            speakerWidgetSettingsDTO.setSpeakerWidgetLayout(virtualEventSettings.getSpeakerWidgetLayout());
            speakerWidgetSettingsDTO.setSpeakerWidgetHighlightSpecificSpeaker(virtualEventSettings.isSpeakerWidgetHighlightSpecificSpeaker());
            speakerWidgetSettingsDTO.setSpeakerWidgetShowSocialLinks(virtualEventSettings.isSpeakerWidgetShowSocialLinks());
            speakerWidgetSettingsDTO.setSpeakerWidgetProfileSize(virtualEventSettings.getSpeakerWidgetProfileSize());
        }
        return speakerWidgetSettingsDTO;
    }

    @Override
    public void updateSpeakerWidgetSettings(Event event, SpeakerWidgetSettingsDTO speakerWidgetSettingsDTO) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());

        virtualEventSettings.setSpeakerWidgetLayout(speakerWidgetSettingsDTO.getSpeakerWidgetLayout());
        virtualEventSettings.setSpeakerWidgetHighlightSpecificSpeaker(speakerWidgetSettingsDTO.isSpeakerWidgetHighlightSpecificSpeaker());
        virtualEventSettings.setSpeakerWidgetShowSocialLinks(speakerWidgetSettingsDTO.isSpeakerWidgetShowSocialLinks());
        virtualEventSettings.setSpeakerWidgetProfileSize(speakerWidgetSettingsDTO.getSpeakerWidgetProfileSize());
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    @Transactional
    public void updateSpeakerHighlightedFlag(Event event, Map<String, List<Long>> speakerIds) {
        if (!CollectionUtils.isEmpty(speakerIds)) {
            speakerIds.entrySet().stream().filter(e -> !CollectionUtils.isEmpty(e.getValue())).forEach(e ->
                    speakerRepoService.updateSpeakerHighlightedFlag(event, e.getValue(), e.getKey().equals(SHOW))
            );
        }
    }

    @Override
    public List<SpeakerCacheBasicDTO> getSpeakerList(Event event, String searchString) {//NOSONAR

        List<SpeakerCacheBasicDTO> speakerCacheBasicDTOList = new ArrayList<>();
        List<Speaker> allSpeakersByEventId = roSpeakerService.getAllSpeakerByEventId(event);

        if(StringUtils.isNotBlank(searchString)) {
            allSpeakersByEventId = allSpeakersByEventId.stream().filter(e->e.getFirstName().toLowerCase().contains(searchString.toLowerCase()) ||
                    e.getLastName().toLowerCase().contains(searchString.toLowerCase()) ||
                    (e.getFirstName()+ Constants.SINGLE_WHITE_SPACE + e.getLastName()).toLowerCase().contains(searchString.toLowerCase()))
                    .collect(toList());
        }

        if (!allSpeakersByEventId.isEmpty()) {
            log.info("Total {} speakers in event {} ", allSpeakersByEventId.size(), event.getEventId());

            List<SessionSpeaker> sessionSpeakers = sessionSpeakerService.findByEventIdWithoutCache(event.getEventId());
            Map<Long, List<SessionSpeaker>> sessionSpeakerMap = sessionSpeakers.stream().collect(Collectors.groupingBy(SessionSpeaker::getSpeakerId));
            if (sessionSpeakerMap.size() > 0) {
                log.info("Total {} session speaker in event {} ", sessionSpeakerMap.size(), event.getEventId());
                allSpeakersByEventId.forEach(speaker -> {
                    if (sessionSpeakerMap.containsKey(speaker.getId())) {
                        List<Boolean> speakerShowModeratorList = sessionSpeakerMap.get(speaker.getId()).stream().map(SessionSpeaker::isShowModerator).collect(Collectors.toList());//NOSONAR
                        if (speakerShowModeratorList.contains(true)){
                            speakerCacheBasicDTOList.add(new SpeakerCacheDTO(speaker, null));
                        }else {
                            log.info("speaker {} is show from speakerlist in event {}", speaker.getId(), event.getEventId());
                        }
                    } else {
                        log.info("speaker {} is not added any session in event {}", speaker.getId(), event.getEventId());
                        speakerCacheBasicDTOList.add(new SpeakerCacheDTO(speaker, null));
                    }
                });
            } else {
                speakerCacheBasicDTOList.addAll(allSpeakersByEventId.stream().map(e -> new SpeakerCacheDTO(e, null)).collect(Collectors.toList()));
            }
        }
        return speakerCacheBasicDTOList;
    }

    @Override
    public List<SpeakerDTO> getSpeakerListWithSessionDetails(Event event, User user) {

        List<Speaker> speakerList = findSpeakerByEventId(event);

        List<Long> speakerIds = new ArrayList<>();
        List<Long> speakerUserIds = new ArrayList<>();

        speakerList.forEach(speaker -> {
            speakerIds.add(speaker.getId());
            speakerUserIds.add(speaker.getUserId());
        });

        Map<Long, List<Session>> speakerIdsSessions = roSessionSpeakerService.getSessionSpeakerIdSpeakerIds(speakerIds,true);
        Map<Long, Boolean> speakerDeviceChecker = deviceCheckerService.speakerIdAndRunDeviceChecker(speakerUserIds);

        List<IdCountDto> userCheckInList =  !speakerUserIds.isEmpty()?checkInAuditLogService.countPreviousCheckInUserList(event,speakerUserIds):Collections.emptyList();
        Map<Long, Map<Long, Boolean>> userIdAndTickets =  !speakerUserIds.isEmpty()?getSpeakerTickets(event, speakerUserIds):Collections.emptyMap();
        Map<Long, Long> userCheckInMap = !CollectionUtils.isEmpty(userCheckInList) ? userCheckInList.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount)) : Collections.emptyMap();

        return speakerList.stream()
                .map(e->new SpeakerDTO(e, event, speakerIdsSessions.get(e.getId()),speakerDeviceChecker.get(e.getUserId()),userCheckInMap.get(e.getUserId()),userIdAndTickets.get(e.getUserId())))
                .collect(Collectors.toList());

    }

    public DataTableResponse getAllSpeakersListByEvent(Event event){
        List<SpeakerBasicDTO> speakerBasicDTOList = speakerRepoService.getAllSpeakersByEventId(event.getEventId());
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(speakerBasicDTOList);
        dataTableResponse.setRecordsFiltered(speakerBasicDTOList.size());
        dataTableResponse.setRecordsTotal(speakerBasicDTOList.size());
        log.info("getAllSpeakersOfEventPage | Size of speakerBasicDTOList : {}" , speakerBasicDTOList.size());
        return dataTableResponse;
    }

    private long createOrUpdateSpeakerCustomAttributes(
            Speaker speaker,
            List<AttributeKeyValueDto> AttributeKeyValueDtos,
            List<CustomFormAttribute> customFormAttributeList
    ) {
        // Initialize or reuse existing attribute data
        CustomFormAttributeData customFormAttributeData = speaker.getSpeakerAttribute();
        if (customFormAttributeData == null) {
            customFormAttributeData = new CustomFormAttributeData();
        }

        // Convert existing attributes to editable map
        CustomAttributesResponseDto speakerAttributeResponseDto = customFormAttributeService.getCustomAttributeResponseDto(customFormAttributeData);

        Map<String, String> existingAttributes =
                Optional.ofNullable(speakerAttributeResponseDto.getAttributes())
                        .orElseGet(HashMap::new);

        // Only proceed if new attributes and definitions are available
        if (!CollectionUtils.isEmpty(customFormAttributeList)
                && !CollectionUtils.isEmpty(AttributeKeyValueDtos)) {

            Map<String, String> newAttributesMap =
                    convertAttributeKeyValueDtoToMap(AttributeKeyValueDtos);

            for (CustomFormAttribute customFormAttribute : customFormAttributeList) {
                String attrName = customFormAttribute.getName();
                String value = Optional.ofNullable(newAttributesMap.get(attrName))
                        .map(v -> v.length() > 100 ? v.substring(0, 100) : v)
                        .orElse(STRING_EMPTY);

                if (existingAttributes.containsKey(attrName) || newAttributesMap.containsKey(attrName)) {
                    existingAttributes.put(attrName, value);
                }
            }
        }

        // Update response DTO
        speakerAttributeResponseDto.setAttributes(existingAttributes);

        // Build attribute JSON map
        Map<String, Object> attributeMap = new HashMap<>();
        attributeMap.put(TICKETING.ATTRIBUTES, CollectionUtils.isEmpty(speakerAttributeResponseDto.getAttributes())
                         ? new ArrayList<>()
                         : speakerAttributeResponseDto.getAttributes());
        attributeMap.put(TICKETING.QUESTIONS, CollectionUtils.isEmpty(speakerAttributeResponseDto.getQuestions())
                        ? new ArrayList<>()
                        : speakerAttributeResponseDto.getQuestions()
        );
        attributeMap.put(TICKETING.NESTEDQUESTIONS, CollectionUtils.isEmpty(speakerAttributeResponseDto.getNestedQuestions())
                        ? new ArrayList<>()
                        : speakerAttributeResponseDto.getNestedQuestions()
        );


        // Save updated JSON
        customFormAttributeData.setJsonValue(new Gson().toJson(attributeMap, Map.class));
       return customFormAttributeDataRepository.save(customFormAttributeData).getId();
    }

    // ---------- helpers ----------
    private Map<String, String> convertAttributeKeyValueDtoToMap(List<AttributeKeyValueDto> attributeKeyValueDtoList) {
        if (CollectionUtils.isEmpty(attributeKeyValueDtoList)) {
            return Collections.emptyMap();
        }

        // Keep the last value for duplicate keys; trim keys/values and skip blank keys
        return attributeKeyValueDtoList.stream()
                .filter(Objects::nonNull)
                .map(dto -> new AbstractMap.SimpleEntry<>(
                        dto.getKey() == null ? null : dto.getKey().trim(),
                        dto.getValue() == null ? null : dto.getValue().trim())
                )
                .filter(entry -> StringUtils.isNotBlank(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldVal, newVal) -> newVal, // keep last
                        LinkedHashMap::new // preserve insertion order (optional)
                ));
    }
}