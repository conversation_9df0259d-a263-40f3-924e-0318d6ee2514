package com.accelevents.session_speakers.services;

import com.accelevents.domain.TicketingTypeTagAndTrack;
import com.accelevents.session_speakers.dto.KeyValueDTO;

import java.util.List;
import java.util.Set;

public interface TicketingTypeTagAndTrackRepoService
{
    Iterable<TicketingTypeTagAndTrack> saveAll(Iterable<TicketingTypeTagAndTrack> ticketTagAndTracks);

    void deleteByTicketIdAndTagOrTrackIdIn(Long ticketingTypeId, Set<Long> tagIdOrTrackIds);

    List<TicketingTypeTagAndTrack> findByTicketingTypeId(Long ticketingTypeId);

    List<KeyValueDTO> findTagAndTrackByTicketingTypeIds(List<Long> ticketingTypeIds);

    List<TicketingTypeTagAndTrack> findByTicketingTypeIdAndTagOrTrackIdIn(Long eventTicketingTypeId, Set<Long> tagAndTrackIds);

    void deleteByTagOrTrackId(List<Long> ids);
}
