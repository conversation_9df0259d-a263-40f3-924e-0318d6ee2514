package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionTagAndTrack;
import com.accelevents.ro.event.service.ROSessionTagAndTrackService;
import com.accelevents.session_speakers.services.SessionTagAndTrackRepoService;
import com.accelevents.session_speakers.services.SessionTagAndTrackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SessionTagAndTrackServiceImpl implements SessionTagAndTrackService {

	@Autowired
	private SessionTagAndTrackRepoService sessionTagAndTrackRepoService;
    @Autowired
    private ROSessionTagAndTrackService roSessionTagAndTrackService;

	@Override
	public void createRecords(Long sessionId, Set<Long> tagIdOrTrackIds){
		if(CollectionUtils.isEmpty(tagIdOrTrackIds))
			return;
		this.saveAll(tagIdOrTrackIds.stream().map(e-> new SessionTagAndTrack(sessionId, e)).collect(Collectors.toList()));
	}

	@Override
	public void deleteRecords(Long sessionId, Set<Long> tagIdOrTrackIds){
		if(CollectionUtils.isEmpty(tagIdOrTrackIds))
			return;
		sessionTagAndTrackRepoService.deleteBySessionIdAndTagOrTrackIdIn(sessionId,tagIdOrTrackIds);
	}

	@Override
	public List<Long> findByIds(List<Long> tagOrTrackIds) {
		return CollectionUtils.isEmpty(tagOrTrackIds)  ? Collections.emptyList() : sessionTagAndTrackRepoService.findByIds(tagOrTrackIds);
	}

    @Override
    public void deleteByTagOrTrackIds(List<Long> tagIdOrTrackIds){
		sessionTagAndTrackRepoService.deleteByTagOrTrackIds(tagIdOrTrackIds);
    }

    @Override
    @Transactional
    public void deleteBySessionId(Long sessionId){
		sessionTagAndTrackRepoService.deleteBySessionId(sessionId);
    }

	@Override
	public Iterable<SessionTagAndTrack> saveAll(Iterable<SessionTagAndTrack> sessionTagAndTracks) {

        return sessionTagAndTrackRepoService.saveAll(sessionTagAndTracks);
	}

	@Override
    public List<SessionTagAndTrack> findBySessionIds(List<Long> sessionIds){
	    return  CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : roSessionTagAndTrackService.findBySessionIds(sessionIds);
    }

    @Override
	public List<SessionTagAndTrack> findBySessionId(Long sessionId){
		return sessionTagAndTrackRepoService.findBySessionId(sessionId);
	}

    @Override
    public List<Long> findSessionIdsByIds(List<Long> tagOrTrackIds) {
        return CollectionUtils.isEmpty(tagOrTrackIds)  ? Collections.emptyList() : sessionTagAndTrackRepoService.findSessionIdsByIds(tagOrTrackIds);
    }

    @Override
    public List<Session> findSessionsByTagOrTrackIds(List<Long> tagOrTrackIds) {
        return  CollectionUtils.isEmpty(tagOrTrackIds)  ? Collections.emptyList() : sessionTagAndTrackRepoService.findSessionsByTagOrTrackIds(tagOrTrackIds);
    }

    @Override
    public Boolean existsBySessionIdAndTagOrTrackId(Long sessionId, Long tagOrTrackId) {
        return sessionTagAndTrackRepoService.existsBySessionIdAndTagOrTrackId(sessionId, tagOrTrackId);
    }

    @Override
    public List<Long> existsBySessionIdAndTagOrTrackIds(Long sessionId, List<Long> tagOrTrackId) {
        return sessionTagAndTrackRepoService.existsBySessionIdAndTagOrTrackIds(sessionId, tagOrTrackId);
    }
}
