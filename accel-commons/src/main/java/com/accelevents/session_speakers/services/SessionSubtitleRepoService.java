package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.SessionSubtitle;
import com.accelevents.session_speakers.dto.SessionSubtitleDTO;

import java.util.List;
import java.util.Optional;

public interface SessionSubtitleRepoService {

    void save(SessionSubtitle sessionSubtitle);

    List<SessionSubtitle> findBySessionIdAndMuxId(Long sessionId, Long muxId);

    Iterable<SessionSubtitle> saveAll(Iterable<SessionSubtitle> sessionSubtitleList);

    Optional<SessionSubtitle> findById(Long subtitleId);

    void deleteById(Long subtitleId);

    List<SessionSubtitleDTO> getSubtitleListByMuxIdAndSessionId(Long sessionId, Long muxId);

    List<SessionSubtitleDTO> getSubtitleListByMuxIdListAndSessionId(List<Long> muxIds, List<Long> sessionIds);

    boolean languageCodeExistByMuxIdAndSessionId(Long muxId, Long sessionId, String languageCode, Long subtitleId);
}
