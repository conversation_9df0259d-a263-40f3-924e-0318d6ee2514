package com.accelevents.session_speakers.services.impl;

import com.accelevents.common.dto.AttendeeBookedScheduleDto;
import com.accelevents.domain.enums.MeetingOrigin;
import com.accelevents.domain.enums.MeetingStatus;
import com.accelevents.domain.session_speakers.MeetingSchedule;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.repo.MeetingScheduleRepo;
import com.accelevents.session_speakers.services.MeetingScheduleRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class MeetingScheduleRepoServiceImpl implements MeetingScheduleRepoService {

    @Autowired
    private MeetingScheduleRepo meetingScheduleRepo;

    @Override
    public List<Long> findSenderAndReceiverOfMeeting(Long meetingId, Long eventId){
        MeetingSchedule meetingSchedule = findByIdAndEventId(meetingId,eventId);
        List<Long> meetingUsers = new ArrayList<>();
        meetingUsers.add(meetingSchedule.getSenderUserId());
        meetingUsers.add(meetingSchedule.getReceiverUserId());
        return meetingUsers;
    }

    @Override
    public List<AttendeeBookedScheduleDto> getAllBookdedMeetingScheduleByUserIdAndStatusBookedFilterByMonthAndYear(List<Long> userIds, MeetingStatus status, Date startDate, Date endDate, Long eventId) {
        return meetingScheduleRepo.findAllBookdedMeetingScheduleByUserIdAndStatusBookedFilterByMonthAndYear(userIds, status, startDate, endDate, eventId);
    }

    @Override
    public MeetingSchedule save(MeetingSchedule meetingSchedule) {
        return meetingScheduleRepo.save(meetingSchedule);
    }

    @Override
    public void saveAll(List<MeetingSchedule> meetingSchedule) {
        meetingScheduleRepo.saveAll(meetingSchedule);
    }

    @Override
    public Boolean isMeetingScheduledBetweenDates(Long receiverUser, Date startTime, Date endTime, Long eventId) {
        return meetingScheduleRepo.isMeetingScheduledBetweenDates(receiverUser, startTime, endTime, eventId);
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepo.findAllMeetingScheduleByUserIdAndEventIdAndStatus(userId, eventId, status, meetingOriginList, pageable);
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatusList(Long userId, long eventId, List<MeetingStatus> statusList, List<MeetingOrigin> meetingOriginList, Date startTime, Pageable pageable) {
        return meetingScheduleRepo.findAllUpcomingMeetingScheduleByUserIdAndEventIdAndStatus(userId, eventId, statusList, startTime, meetingOriginList, pageable);
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventId(Long userId, long eventId, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepo.findAllMeetingScheduleByUserIdAndEventId(userId, eventId, meetingOriginList, pageable);
    }

    @Override
    public Boolean isMeetingScheduledBetweenDatesByNotIdAndUserIdList(Long id, List<Long> userList, Date startTime, Date endTime, Long eventId) {
        return meetingScheduleRepo.isMeetingScheduledBetweenDatesByNotIdAndUserIdList(id, userList, startTime, endTime, eventId);
    }

    @Override
    public MeetingSchedule getMeetingScheduleById(long id) {
        return meetingScheduleRepo.findById(id).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.MEETING_SCHEDULE_NOT_FOUND));
    }

    @Override
    public MeetingSchedule findByIdAndEventId(Long scheduleId, Long eventId) {
        return meetingScheduleRepo.findByIdAndEventId(scheduleId, eventId).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.MEETING_SCHEDULE_NOT_FOUND));
    }

    @Override
    public void cancelMeetingSchedule(long id) {
        meetingScheduleRepo.updateMeetingScheduleStatusById(id, MeetingStatus.CANCEL);

    }

    @Override
    public Page<MeetingSchedule> getAllCreatedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepo.findAllCreatedMeetingScheduleByUserIdAndEventIdAndStatus(userId, eventId, status, meetingOriginList, pageable);
    }

    @Override
    public Page<MeetingSchedule> getAllRejectedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepo.findAllRejectedMeetingScheduleByUserIdAndEventIdAndStatus(userId, eventId, status, meetingOriginList, pageable);
    }

    @Override
    public void deleteMeetingScheduleByUserId(Long userId) {
        meetingScheduleRepo.deleteMeetingScheduleByUserId(userId);
    }

    @Override
    public long getTotalMeetingBooked(Long eventId, MeetingOrigin origin) {
        return meetingScheduleRepo.getTotalMeetingBooked(eventId, origin);
    }

    @Override
    public long getTotalMeetingBookedInExhibitor(Long eventId, MeetingOrigin origin, Long exhibitorId) {
        return meetingScheduleRepo.getTotalMeetingBookedInExhibitor(eventId, origin, exhibitorId);
    }

    @Override
    public long getTotalBookedMeetingOfUserInOrigin(Long eventId, Long userId, MeetingOrigin origin) {
        return meetingScheduleRepo.getTotalBookedMeetingOfUserInOrigin(eventId, userId, origin);
    }

    @Override
    @Transactional
    public void deleteMeetingScheduleByEventId(Long eventId) {
        meetingScheduleRepo.deleteMeetingScheduleByEventId(eventId);
    }

    @Override
    public List<Object[]> getBookedMeetingByUserIds(Long eventId, List<Long> userIds) {
        return meetingScheduleRepo.getBookedMeetingByUserIds(eventId, userIds);
    }

    @Override
    public List<IdCountDto> getMeetingRequestSentByUserIds(Long eventId, List<Long> userIds) {
        return meetingScheduleRepo.getMeetingRequestSentByUserIds(eventId, userIds);
    }

    @Override
    public List<IdCountDto> getMeetingRequestReceivedByUserIds(Long eventId, List<Long> userIds) {
        return meetingScheduleRepo.getMeetingRequestReceivedByUserIds(eventId, userIds);
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingSchedulesCreatedByAdmin(Long eventId, String meetingOrigin, Pageable pageable, String searchString) {
        return meetingScheduleRepo.findAllMeetingScheduleEventIdAndOrigin(eventId, meetingOrigin, pageable, searchString);
    }
    @Override
    public List<Object[]> getMeetingByEventIdAndStatus(String eventUrl, String status) {
        return meetingScheduleRepo.getMeetingByEventUrlAndStatus(eventUrl, status);
    }

    @Override
    public boolean isPreScheduleMeetingScheduledForEventAndUser(Long eventId, Long userId) {
        return meetingScheduleRepo.isPreScheduleMeetingScheduledForEventAndUser(eventId, userId);
    }

    @Override
    public boolean isAnyMeetingBookedByUserAndEventAndOrigin(Long userId, Long eventId, List<MeetingOrigin> origin) {
        return meetingScheduleRepo.isAnyMeetingBookedByUserAndEventAndOrigin(userId, eventId, origin);
    }

    @Override
    public List<MeetingSchedule> findScheduledMeetingByEventId(Long eventId) {
        return meetingScheduleRepo.findScheduledMeetingByEventId(eventId);
    }

    @Override
    public List<MeetingSchedule> findScheduledMeetingOfABooth(Long eventId, Long expoId, List<Long> receiverId,List<MeetingStatus> status,
                                                              List<String> location) {
        return meetingScheduleRepo.findByEventIdAndExhibitorIdAndOrigin(eventId, expoId, MeetingOrigin.EXPO_COMP_REPRESENTATIVE, receiverId, status, location);
    }

    @Override
    public Optional<MeetingSchedule> findByExhibitorIdAndIdAndEventId(Long expoId, Long meetingId, Long eventId) {
        return meetingScheduleRepo.findByExhibitorIdAndIdAndEventId(expoId, meetingId, eventId);
    }

    @Override
    public List<MeetingSchedule> findAcceptedScheduledMeetingByEventIdAndStatus(List<Long> eventIds) {
        return meetingScheduleRepo.findAcceptedScheduledMeetingByEventIdAndStatus(eventIds);
    }
}
