package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SessionSpeakerService {

    boolean addSessionSpeaker(Long sessionId, Long speakerId, Event event, boolean isSpeakerInviteEnable);

    List<SpeakerDTO> getSpeakerDtoBySession(Long sessionId);

    Page<Session> getSessionByEventIdAndSpeakerUserId(Event event, Long userId, String search, Pageable pageable, Boolean past, Boolean isAdminOrStaff);

    void deleteSessionSpeakerBySpeaker(Long speakerId, Event event);

    void deleteSessionSpeakerBySpeakers(List<Speaker> speakerId, Event event);

    void deleteSessionSpeakerBySession(Long sessionId);

    Map<Long, List<Speaker>> getSessionSpeakerIdSessionIds(List<Long> sessionIds);

    Map<Long, List<SpeakerDTO>> getSessionSpeakerIdSessionDtoIds(List<Long> sessionIds);

    String removeSessionSpeaker(Long sessionId, Long speakerId, Event event, Map<String, String> languageMap, String languageCode);

    void save(SessionSpeaker sessionSpeaker);

    boolean isUserSpeakerInSession(Long userId, Long sessionId);

    boolean isUserSpeakerInSessionByEventId(Long userId, Long eventId);

    void saveAll(Set<SessionSpeaker> sessionSpeakers);

    void updateSessionSpeakerPosition(Long Id, Long topSessionSpeakerId, Long topBottomSessionSpeakerId, Long sessionId);

    List<SessionSpeaker> getAllBySessionId(long id);

    SessionSpeaker findFirstBySessionIdOrderByPositionDesc(Long sessionId);

    List<SessionSpeaker> findBySessionIdsIn(List<Long> sessionIds);

    Map<Long, List<Long>> getSpeakerSessionIdBySpeakerIdAndSessionFormat(List<Long> speakerIds, List<EnumSessionFormat> sessionFormats);

    List<Session> findSessionBySpeakerUserId(Event event, Long userId);

    List<Long> getAllSessionIdsBySpeakerId(Long speakerId);

    List<SessionSpeaker> getAllSessionSpeakerBetweenSessionIds(long from, long to);

    void setModeratorForPassedSessionIdAndSpeakerId(Long sessionId, Long speakerId, Event event, boolean isModerator);

    Boolean isSpeakerModeratorInSession(Long sessionId, Long userId);

    Long getSpeakerIdBySessionIdAndUserId(Long sessionId, Long userId);

    void isShowModeratorForSessionIdAndSpeakerId(Long sessionId, Long speakerId, Event event, boolean isShowModerator);

    List<IdCountDto> getSessionIdAndSessionSpeakerCount(List<Long> sessionIds);

    List<Long> findSpeakerUserIdBySessionId(Long sessionId);

    List<SessionSpeaker> findByEventId(Long eventId);

    boolean isSpeakerAvailable(Long speakerId);

    void updateSessionSpeakerBySpeakerId(Long speakerId,Long currentSpeakerId);

    List<Long> findAllSessionIdBySpeakerUserIdWithOutWorkshop(Long userId, Long eventId);

    List<SessionSpeaker> findByEventIdWithoutCache(long eventId);

    List<EventTaskEmailDto> findSpeakersEmailNameAndSessionNameByEventIdAndSessionIds(Long eventId, List<Long> sessionIds);

    List<Long> findSessionByEventIdAndSpeakerUserId(Long eventId, Long userId);
}
