package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.domain.session_speakers.Speaker;

import java.util.List;
import java.util.Set;

public interface SpeakerHelperService {
    void sendInvite(Event event, User loggedInUser, Long sessionId, Long speakerId);
    void sendInvite(Event event, Long speakerId,String pastSpeakerEmail);
    void sendInviteToAllSpeakers(Event event, String authToken, User loggedInUser);

    void sendInviteToListOfSpeaker(Event event, User loggedInUser, Set<SessionSpeaker> sessionSpeakers);

    void sendInvite(List<Speaker> speakers, String oldEmail);
}
