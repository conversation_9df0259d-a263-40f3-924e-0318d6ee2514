package com.accelevents.session_speakers.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.session_speakers.dto.SpeakerBasicDTO;
import com.accelevents.session_speakers.dto.SpeakerCountDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.dto.SpeakerEmailIdDTO;
import com.accelevents.session_speakers.repo.SpeakerRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.accelevents.exceptions.NotFoundException.SessionSpeakerNotFound.SPEAKER_NOT_FOUND;

@Service
public class SpeakerRepoServiceImpl implements SpeakerRepoService {

	@Autowired private SpeakerRepo speakerRepo;
    @Autowired private ClearAPIGatewayCache clearAPIGatewayCache;

	@Override
	public Speaker save(Speaker speaker){
		Speaker savedSpeaker = speakerRepo.save(speaker);

		//TODO: APIGW_UN
		clearAPIGatewayCache.clearAPIGwSpeakerPortalCache(speaker.getEventId());
		return  savedSpeaker;
	}

    @Override
    public Speaker saveWithoutAPIGWClearCache(Speaker speaker){
        return speakerRepo.save(speaker);
    }

	@Override
	public Optional<Speaker> findById(Long speakerId){
		return speakerRepo.findById(speakerId);
	}

	@Override
	public Iterable<Speaker> saveAll(Iterable<Speaker> speakers){
		Iterable<Speaker> savedSpeakers = speakerRepo.saveAll(speakers);

		//TODO: APIGW_UN
		clearAPIGatewayCache.clearAPIGwAllSpeakerPortalCache(speakers);
		return savedSpeakers;
	}

	@Override
	public Speaker getSpeakerByIdOrThrowNotFound(Long speakreId, Event event) {
		return speakerRepo.findByEventIdAndId(event.getEventId(),speakreId).orElseThrow(()-> new NotFoundException(SPEAKER_NOT_FOUND));
	}

	@Override
	public Optional<Speaker> findSpeakerByIdAndEventOptional(Event event, Long speakerId) {
		return speakerRepo.findByEventIdAndId(event.getEventId(), speakerId);
	}

	@Override
	public void deleteSpeaker(Speaker speaker, Event event){

		speakerRepo.delete(speaker);
		clearAPIGatewayCache.clearAPIGwSpeakerPortalCache(event.getEventURL());
	}

    @Transactional
    @Override
    public void deleteSpeakers(List<Speaker> speaker, Event event){
        speakerRepo.deleteAll(speaker);
        clearAPIGatewayCache.clearAPIGwSpeakerPortalCache(event.getEventURL());
    }

	@Override
	public Optional<Speaker> findByEventIdAndId(Long eventId, Long id) {
		return speakerRepo.findByEventIdAndId(eventId, id);
	}

    @Override
    public List<Speaker> findByEventIdAndIds(Long eventId, List<Long> ids){
        return speakerRepo.findByEventIdAndSpeakerIds(eventId, ids);
    }

	@Override
	public List<Speaker> findSpeakerByEventId(Long eventId) {
		return speakerRepo.findSpeakerByEventId(eventId);
	}

	@Override
	public Page<Speaker> findAllSpeakersByEventId(Long eventId, Pageable pageable) {
		return speakerRepo.findSpeakerByEventId(eventId, pageable);
	}

	@Override
	public boolean checkSpeakerAlreadyExistByEmail(String email, Long eventId) {
		return speakerRepo.checkSpeakerAlreadyExistByEmail(email, eventId);
	}

    @Override
    public List<Speaker> findSpeakerByEmailAndEvent(String email, Long eventId){
        return speakerRepo.findSpeakerByEmailAndEvent(email, eventId);
    }

    @Override
    public List<Speaker> findSpeakersByEmailsAndEvent(List<String> emails, Long eventId) {
        return speakerRepo.findSpeakersByEmailsAndEvent(emails, eventId);
    }

	@Override
	public Page<Speaker> getAllSpeakerByEvent(String searchString, Long eventId, Pageable pageable) {
		return speakerRepo.getAllSpeakerByEvent(searchString, eventId, pageable);
	}

	@Override
	public Optional<Long> findIdByEventIdAndUserId(Long eventId, Long userId) {
		return speakerRepo.findIdByEventIdAndUserId(eventId, userId);
	}

	@Override
	public boolean findByIdEventIdAndUserId(Long eventId, Long userId) {
		return speakerRepo.findByIdEventIdAndUserId(eventId, userId);
	}

	@Override
	public List<Speaker> getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(long from, long to) {
		return speakerRepo.getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(from, to);
	}

	@Override
	public List<Long> findSpeakerUserIdsByEventId(Long eventId) {
		return speakerRepo.findSpeakerUserIdsByEventId(eventId);
	}

    @Override
    public Integer findSpeakersCountByEventId(Long eventId){
        return speakerRepo.findSpeakersCountByEventId(eventId);
    }

	@Override
	public void updatePositionSpeaker(Long eventId, Double startPosition, Double endPosition, Double updateCount) {

		speakerRepo.updatePositionSpeaker(eventId,startPosition,endPosition,updateCount);
	}

	@Override
	public void updatePositionForAllSpeakerByEventId(Double updateCount, Long eventId) {

		speakerRepo.updatePositionForAllSpeakerByEventId(updateCount,eventId);
	}

	@Override
	public List<Speaker> nextPositionSpeaker(Long speakerId, Long eventId, Double currentPosition) {
		return speakerRepo.nextPositionSpeaker(speakerId,eventId,currentPosition);
	}

	@Override
	public List<Speaker> previousPositionSpeaker(Long speakerId, Long eventId, Double currentPosition) {
		return speakerRepo.previousPositionSpeaker(speakerId,eventId,currentPosition);
	}

	@Override
	public List<Speaker> getSpeakerBySamePosition(Double position, Long eventId) {
		return speakerRepo.getSpeakerBySamePosition(position,eventId);
	}

	@Override
	public Speaker findFirstByEventIdOrderByPositionDesc(Long eventId) {
		return speakerRepo.findFirstByEventIdOrderByPositionDesc(eventId);
	}

	@Override
	public Long getUserId(Long eventId) {
		return speakerRepo.getUserId(eventId);
	}

	@Override
	public List<Speaker> findByEmail(String email) {
		return speakerRepo.findByEmail(email);
	}

    @Override
    public Speaker findByEventIdAndUserId(long eventId, long userId) {
        return  speakerRepo.findByEventIdAndUserId(eventId,userId).orElse(null);
    }

    @Override
    public List<Speaker> getAllSpeakerIdByUserId(Long userId) {
        return speakerRepo.findAllSpeakerIdByUserId(userId);
    }

    @Override
    public void deleteAllBySpeakerIds(List<Long> speakerIds) {
	    speakerRepo.deleteAllBySpeakerId(speakerIds);
    }


    @Override
    public Long getEventSpeakerCount(Long eventId) {
        return speakerRepo.getEventSpeakerCount(eventId);
    }

    @Override
    public List<String> findEmailByEventId(long eventId) {
        return speakerRepo.findEmailByEventId(eventId);
    }

    @Override
    public List<SpeakerEmailIdDTO> findEmailAndIdByEventId(long eventId) {
        return speakerRepo.findEmailAndIdByEventId(eventId);
    }

    @Override
    public Optional<Long> findSpeakerIdByEventIdAndUserId(long eventId, Long userId) {
        return speakerRepo.findSpeakerIdByEventIdAndUserId(eventId, userId);
    }

    @Override
    public List<Speaker> findSpeakerByEventIdAndUserId(long eventId,long userId){
	    return  speakerRepo.findAllSpeakerByUserId(eventId,userId);
    }

    @Override
    public List<SpeakerDTO> getAllSpeakerEventIdAndUserId(){
	    return speakerRepo.getAllSpeakerEventIdAndUserId();
    }

    @Override
    public List<SpeakerCountDTO> speakerCountBySpeakerOnBoardingStatus(Event event) {
	    return speakerRepo.speakerCountBySpeakerOnBoardingStatus(event);
    }

    @Override
    public void updateSpeakerHighlightedFlag(Event event, List<Long> speakerIds, boolean isHighlighted) {
        speakerRepo.updateSpeakerHighlightedFlag(event, speakerIds, isHighlighted);
    }

    @Override
    public Map<Integer, Integer> speakerCountByEventId(List<Long> eventIdList) {
        List<Object[]> list = speakerRepo.speakerCountByEventId(eventIdList);
        Map<Integer, Integer> map = new HashMap<>();
        for (Object[] obj : list) {
            map.put(((BigInteger) obj[0]).intValue(), ((BigInteger) obj[1]).intValue());
        }
        return map;
    }

    @Override
    public List<SpeakerBasicDTO> getAllSpeakersByEventId(Long eventId){
       return speakerRepo.getAllSpeakersByEventId(eventId);
    }

    @Override
    public List<EventTaskEmailDto> findSpeakerEmailAndNameByEventIdAndSpeakerIds(Long eventId, List<Long> speakerIds){
       return speakerRepo.findSpeakerEmailAndNameByEventIdAndSpeakerIds(eventId,speakerIds);
    }

    @Override
    public long getSpeakerCountByEventId(Long eventId) {
        long count = 0;
        List<Speaker> speakers = this.findSpeakerByEventId(eventId);
        if (!CollectionUtils.isEmpty(speakers)) {
            count = speakers.size();
        }
        return count;
    }

    @Override
    public List<String> findAllEmailsByEventIdAndEmails(Long eventId, List<String> emailsForCreateRows){
        return speakerRepo.findAllEmailsByEventIdAndEmails(eventId, emailsForCreateRows);
    }
    @Override
    public List<Speaker> getSpeakerWithUserAndAttributeDataByEventIdAndIds(Long eventId, List<Long> ids){
        return speakerRepo.getSpeakerWithUserAndAttributeDataByEventIdAndIds(eventId, ids);
    }
}
