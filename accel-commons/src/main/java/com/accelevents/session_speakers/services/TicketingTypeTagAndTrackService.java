package com.accelevents.session_speakers.services;

import com.accelevents.domain.TicketingTypeTagAndTrack;
import com.accelevents.session_speakers.dto.KeyValueDTO;

import java.util.List;
import java.util.Set;

public interface TicketingTypeTagAndTrackService {

    void createRecords(Long ticketingTypeId, Set<Long> addNewList);

    void deleteRecords(Long ticketingTypeId, Set<Long> dbTagOrTrackIds);

    Iterable<TicketingTypeTagAndTrack> saveAll(Iterable<TicketingTypeTagAndTrack> sessionTagAndTracks);

    List<TicketingTypeTagAndTrack> findByTicketingTypeId(Long id);

    List<KeyValueDTO> findTagAndTrackByTicketingTypeIds(List<Long> ticketingTypeIds);
}
