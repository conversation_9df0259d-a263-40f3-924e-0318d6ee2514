package com.accelevents.session_speakers.services;

import com.accelevents.domain.enums.EnumSessionCheckInLogStatus;
import com.accelevents.domain.session_speakers.SessionCheckInLog;

import java.util.List;

public interface SessionCheckInLogRepoService {

    SessionCheckInLog save(SessionCheckInLog sessionCheckInLog);

    SessionCheckInLog findFirstByUserIdAndSessionIdOrderByIdDesc(Long userId, Long sessionId);

    List<SessionCheckInLog> findAllSessionCheckInLogBySessionIdOrderByAuditTimeAsc(Long sessionId);

    SessionCheckInLog findUserIdAndSessionIdOrderByIdAsc(Long userId, Long sessionId);

    List<SessionCheckInLog> findAllSessionLogsBySessionIdAndStatus(Long sessionId,EnumSessionCheckInLogStatus auditStatus);

    SessionCheckInLog findFirstByUserIdAndSessionIdAndEventTicketIdOrderByIdDesc(Long userId, Long sessionId, Long eventTicketId);
}
