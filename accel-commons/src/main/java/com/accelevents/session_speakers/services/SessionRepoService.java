package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.SurveySessionsBasicDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.StreamKeyAndRtmpUrlDto;
import com.accelevents.session_speakers.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface SessionRepoService {

	Session save(Session sessions);

	void saveAll(List<Session> sessions);

	Optional<Session> findById(Long sessionId);

    Optional<Session> findByConfIdCacheData(Long confId);

	Session getSessionById(Long id, Event event);

	Session getSessionByIdWithoutCache(Long id, Event event);

	Session getSessionByIdJoinFetchTagsTrack(Long id, Event event);

	List<String> findSessionsWithConflictingSlot(Event event, String startTime, String endTime, Long sessionId);

	Page<Session> findAllByEventIdAndIdIn(Event event, List<Long> sessionIds,
                                          Pageable pageRequest, User user, boolean showPastAndUpcoming, boolean isAdminOrStaff, String filterDate);

	List<Session> getAllSessionByEventId(Event event, EnumSessionFormat sessionFormat, boolean isAdminOrStaff);

	Page<Session> getAllHostSessionByEventId(Event event,
                                             String searchStr,
                                             Pageable pageable, boolean isFromBillingPage, List<Date> searchDate,List<EnumSessionFormat> sessionTypes,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming, List<SessionTypeFormat>... sessionTypeFormats);

    Page<Session> getAllHostSessionByEventIdWithSorting(Event event,
                                             String searchStr,
                                             Pageable pageable, boolean isFromBillingPage,List<Date> searchDate,List<EnumSessionFormat> sessionTypes,List<SessionTypeFormat> sessionTypeFormats,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming);

	Page<Session> getAllSessionByEventId(Event event,
                                         Pageable pageable,
                                         SessionFilter filter, User user, boolean isAdminOrStaff, String calledFrom);

    List<Session> filterPrivateSessions(Event event, User user, List<Session> sessionsToReturn);

    List<Session> getActiveExtendedSessions(StreamProvider streamProvider, Date now);

	Boolean isSessionStarted(Long sessionId, Event event);

    Optional<Session> findByLiveStreamId(String liveStreamId);

    Optional<Session> findByStreamUrl(String streamUrl);

	void updateStatusToDeleteBySessionId(Long sessionId, Long eventId, RecordStatus delete);

	List<Object[]> getNetworkingSessionActivity(long eventId);

	Boolean isSessionsBookedByUserAndBetweenStartEndTime(List<Long> userIds, Date startTime, Date endTime, Long eventId);

    List<Session> findAllSessionIdByEventAndSessionFormatNotIn(Event event, Set<EnumSessionFormat> sessionFormat);

    List<Session> findSessionByEventId(Event event);

	@Transactional
	Session updateSessionOnMuxStreamKeyCreation(Session session,
												StreamKeyAndRtmpUrlDto dto);

    Set<String> findAllSessionDatesByEvent(Event event, String timezoneOffset, boolean isAdminOrStaff);

	Page<Session> findAllByEventIdAndSessionFormatAndIdIn(Event event, List<Long> sessionIds, Pageable pageable, String sessionFormat, boolean showPastAndUpcoming, boolean isAdminOrStaff, User user, String filterDate);

	long getSessionCountByEventId(Long eventId);

	List<Session> findSessionByEventIdOrderByIdAsc(Long eventId);

    List<SessionIdTitleDto> getSessionIdAndTitleById(List<Long> sessionId);

    List<Session> findSessionByFromTo(Long from, Long to);

    List<String> findSessionTitleByEventIdOrderByIdAsc(Long eventId);

    long findSessionCountBySessionName(Long eventId,String sessionName);

    String findTaskIdBySessionId(Long sessionId);

	Long getEventIdBySessionId(Long sessionId);

    List<Session> findByEventAndFormat(Long eventId, List<EnumSessionFormat> sessionFormat);

    long getNonVisualSessionsByEvent(Long eventId, List<EnumSessionFormat> enumSessionFormats);

    Session nextPositionSession(Long id, Long eventId, Double position);

    Session previousPositionSession(Long id, Long eventId, Double position);

    void updatePositionSession(Long eventId, Double startPosition, Double endPosition, Double updateCount);

    void updatePositionForAllSessionByEventId(Double sequence, Long eventId, Session topSession);

    Session findFirstByEventIdOrderByPositionDesc(Long eventId);

    Session getLastConcurrentSession(Long eventId, Date startTime, Date endTime);

    List<Session> getSessionsByTimeAndSortType(long eventId, Date startTime, Date endTime, String sortType);

    List<Session> getSessionsByStartTimeAndEndTimeUnique(long eventId);

    List<Session> getLastConcurrentSessionForDataFix(Long eventId, Date startTime, Date endTime);

    List<SessionEventLiveStream> findAllByLiveStreamIdsIn(List<String> liveStreamIds);

    void updatePlayBackUrlBySessionId(Long key, String value, Long eventId);

    void updateRecordStatus(List<Long> sessionIdsToBeMarkedAsPlaybackDeleted, RecordStatus block);

    List<Session> findByEventIdAndRecordStatus(Long eventId, RecordStatus block);

   List<Session> findSessionFormatBySessionName(Long eventId);

    List<Session> getAllSessionByIdsAndEventId(List<Long> sessionIds, Event event);

    List<Session> findByEventIdAndEnabledLowLatency(Event event,String rtmpUrl, StreamProvider streamProvider);

    Boolean isActiveMainStageSessionByEventId(Long eventId, Date now);

    Optional<Session> findByMeetingId(String meetingId);

    Page<Session> getAllCommandCenterSessionList(Event event, Pageable pageRequest, User user, boolean isAdminOrStaff);
    List<Session> getSessionByEventIds(List<Long> listOfEventId);

    Session getFirstConcurrentSession(Long eventId, Date startTime, Date endTime);

    List<SessionIdNameTypeDto> getUpcomingSessionByEvent(Event event, Date currentDate);

    List<SessionIdNameTypeDto> getUpcomingSessionByEventAndEndDateOfCurrentSession(Event event, Date currentDate);

    Session isSessionExistingOrUpcoming(PostSessionCallToActionTypeEnum postSessionCallToActionTypeEnum, String id);

    void setSessionStatusById(List<Long> sessionIds, EnumSessionStatus hidden, Long eventId);

    Page<Session> getCommandCenterAllSessionByEventId(Event event, SessionFilterCommandCenter sessionFilter, Pageable pageable);

    String findTitleBySessionId(Long sessionId, Long eventId);

    String findFormatBySessionId(Long sessionId, Long eventId);

    long getSessionCountByEventIdAndSessionFormat(Long eventId,EnumSessionFormat sessionFormat);

    boolean existsBySurveyIdAndEventId(Long surveyId, Long eventId);

    List<SessionListDto> getAllSessionByEventId(Long eventId);

    boolean isSessionPrivate(Long sessionId);

    long countByEventIdAndSessionStartTime(Date startTime, Long eventId,Date endTime);

    List<SessionBasicDetailsDTO> getAllSessionsByEventId(Long eventId);

    List<Long> findSurveyIdBySessionIds(List<Long> sessionIds);

    Page<Session> getAllPublicSessionByEventId(Event event, Pageable pageable, SessionFilter sessionFilter);

    boolean allSessionVisibleForUser(User user, Event event);

    List<Session> getAllSessionsByEventIdAndSponsorExhibitorJsonNotNull(Event event);

    Page<Session> getAllCachePrivateSessionByEventId(Event event, Pageable pageable, SessionFilter sessionFilter, List<Session> sessions);

    List<Session> findAllByIds(List<Long> sessionIds);

    List<SessionDetailsCalendarViewDto> findAllHostSessions(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, List<Long> speakerIds, List<Long> locationIds, Date startDate, Date endDate);

    Page<SessionDetailsCalendarViewDto> getAllHostUnscheduledSessionByEventId(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, List<Long> speakerIds, List<Long> locationIds, Pageable pageable);

    List<SurveySessionsBasicDto> getSessionSurveyBasicDetailsByEvent(Long eventId);

    abstract List<Session> findAllByIdsAndEventId(List<Long> sessionIds, Long eventId);

    List<Session> findSessionsBySurveyIdAndEventId(Long surveyId, Long eventId);
}
