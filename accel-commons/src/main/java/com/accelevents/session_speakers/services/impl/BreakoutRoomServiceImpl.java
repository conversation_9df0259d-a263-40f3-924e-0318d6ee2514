package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.BreakoutRoom;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.services.ChimeService;
import com.accelevents.services.StaffService;
import com.accelevents.session_speakers.dto.BreakoutRoomResponseDto;
import com.accelevents.session_speakers.dto.MeetingAttendeeDto;
import com.accelevents.session_speakers.dto.BreakoutRoomDto;
import com.accelevents.session_speakers.repo.SessionSpeakerRepo;
import com.accelevents.session_speakers.services.*;
import com.accelevents.utils.Constants;
import com.accelevents.utils.SessionUtils;
import com.amazonaws.services.chimesdkmeetings.model.LimitExceededException;
import com.amazonaws.services.chimesdkmeetings.model.Meeting;
import com.cloudinary.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class BreakoutRoomServiceImpl implements BreakoutRoomService {

    private static final Logger log = LoggerFactory.getLogger(BreakoutRoomServiceImpl.class);

    private static final Integer BREAKOUT_ROOM_LIMIT=100;

    @Autowired
    private SessionService sessionService;
    @Autowired
    private BreakoutRoomRepoService breakoutRoomRepoService;
    @Autowired
    private ChimeService chimeService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private SessionSpeakerRepoService sessionSpeakerRepoService;
    @Autowired
    private SessionSpeakerRepo sessionSpeakerRepo;

    @Override
    public BreakoutRoomResponseDto createBreakoutRoom(Long sessionId, Event event, Long breakoutRoomCount) {
        //breakoutRoomNameCounter For set default name breakoutRoom with Index Ex: breakoutRoom 1
        Integer breakoutRoomNameCounter = breakoutRoomRepoService.findBreakoutRoomCounterBySessionIdAndEventIdOrderByIdDesc(sessionId, event.getEventId());
        breakoutRoomNameCounter = breakoutRoomNameCounter + 1;

        Long breakoutRoomValidateCount = validateBreakoutRoomCount(breakoutRoomCount, event.getEventId(), sessionId);

        List<BreakoutRoom> breakoutRooms = new ArrayList<>();
        for (int roomCount = 1; roomCount <= breakoutRoomValidateCount; roomCount++) {
            BreakoutRoom breakoutRoom = new BreakoutRoom();
            breakoutRoom.setRoomName(Constants.BREAKOUT_ROOM + breakoutRoomNameCounter);
            breakoutRoom.setBreakOutRoomCounter(breakoutRoomNameCounter);
            breakoutRoom.setCreatedAt(new Date());
            breakoutRoom.setRecordStatus(RecordStatus.CREATE);
            breakoutRoom.setSessionId(sessionId);
            breakoutRoom.setEventId(event.getEventId());
            breakoutRooms.add(breakoutRoom);
            breakoutRoomNameCounter++;
        }
        breakoutRoomRepoService.saveAll(breakoutRooms);
        log.info("BreakoutRoomServiceImpl createBreakoutRoom eventId {} sessionId {} breakoutRoomCount {} ", event.getEventId(), sessionId, breakoutRoomCount);
        return new BreakoutRoomResponseDto(breakoutRooms.stream().map(BreakoutRoomDto::new).collect(Collectors.toList()),breakoutRoomCount,breakoutRoomValidateCount,(breakoutRoomCount-breakoutRoomValidateCount));
    }

    private Long validateBreakoutRoomCount(Long breakoutRoomCount, Long eventId, Long sessionId){
        Long totalExistBreakoutRoom = breakoutRoomRepoService.countAllByEventIdAndSessionId(eventId, sessionId);

        if((breakoutRoomCount+totalExistBreakoutRoom) >= BREAKOUT_ROOM_LIMIT){
            if(BREAKOUT_ROOM_LIMIT - totalExistBreakoutRoom >= 0){
                long remainingRooms = BREAKOUT_ROOM_LIMIT - totalExistBreakoutRoom;
                return (remainingRooms >= 0 ) ? remainingRooms : 0;
            }
            else {
                return 0L;
            }
        }
        return breakoutRoomCount;
    }

    @Override
    public boolean checkIsBreakoutRoomAvailable(Long sessionId, Long breakoutRoomId, Event event) {
        return breakoutRoomRepoService.isBreakoutRoomPresentWithIdAndEventIdAndSessionId(breakoutRoomId, event.getEventId(), sessionId);
    }

    @Override
    public void updateBreakoutRoom(BreakoutRoomDto breakoutRoomDto, Long sessionId, Event event) {
        checkBreakoutRoomNameIfPresentThrowException(breakoutRoomDto.getRoomName(), sessionId, event.getEventId());
        BreakoutRoom breakoutRoom = breakoutRoomRepoService.findBreakoutRoomById(breakoutRoomDto.getBreakoutRoomId());
        breakoutRoom.setRoomName(breakoutRoomDto.getRoomName());
        breakoutRoomRepoService.save(breakoutRoom);
        log.info("BreakoutRoomServiceImpl updateBreakoutRoom eventId {} sessionId {} breakoutRoomName {} ", event.getEventId(), sessionId, breakoutRoomDto.getRoomName());
    }

    private void checkBreakoutRoomNameIfPresentThrowException(String breakoutRoomName, Long sessionId, long eventId) {
        boolean isBreakoutRoomNamePresent = breakoutRoomRepoService.isBreakoutRoomPresentWithNameAndEventIdAndSessionId(breakoutRoomName, eventId, sessionId);
        if (isBreakoutRoomNamePresent) {
            NotAcceptableException.NotAceptableExeceptionMSG ex = NotAcceptableException.NotAceptableExeceptionMSG.BREAKOUT_ROOM_ALREADY_EXIST;
            String constantMessage = Constants.BREAKOUT_ROOM_ALREADY_EXIST.replace(Constants.VAR_BREAKOUT_ROOM_NAME, breakoutRoomName);
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.VAR_BREAKOUT_ROOM_NAME,breakoutRoomName);
            ex.setErrorMessage(constantMessage);
            ex.setDeveloperMessage(constantMessage);
            ex.setDefaultMessage(Constants.BREAKOUT_ROOM_ALREADY_EXIST);
            ex.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new NotAcceptableException(ex);
        }
    }

    @Override
    public List<BreakoutRoomDto> getAllBreakoutRoom(Long sessionId, Event event) {
        List<BreakoutRoom> breakoutRooms = breakoutRoomRepoService.getAllBreakoutRoom(sessionId, event.getEventId());
        if (!CollectionUtils.isEmpty(breakoutRooms)) {
            List<BreakoutRoomDto> breakoutRoomDtos = new ArrayList<>();
            for (BreakoutRoom breakoutRoom : breakoutRooms) {
                breakoutRoomDtos.add(new BreakoutRoomDto(breakoutRoom));
            }
            log.info("BreakoutRoomServiceImpl getAllBreakoutRoom eventId {} sessionId {} ", event.getEventId(), sessionId);
            return breakoutRoomDtos;
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public void deleteBreakoutRoom(Long id) {
        BreakoutRoom breakoutRoom = breakoutRoomRepoService.findBreakoutRoomById(id);
        breakoutRoom.setRecordStatus(RecordStatus.DELETE);
        if (StringUtils.isNotBlank(breakoutRoom.getMeetingInfo())) {
            Meeting meeting = SessionUtils.getMeeting(breakoutRoom.getMeetingInfo());
            if (null != meeting)
                chimeService.deleteMeeting(meeting.getMeetingId());
        }
        breakoutRoomRepoService.save(breakoutRoom);
        log.info("BreakoutRoomServiceImpl deleteBreakoutRoom eventId {} sessionId {} BreakoutRoomById {} ", breakoutRoom.getEventId(), breakoutRoom.getSessionId(), id);
    }

    @Override
    public MeetingAttendeeDto joinBreakoutRoom(Long sessionId, Long breakoutRoomId, Event event, User user) {
        BreakoutRoom breakoutRoom = breakoutRoomRepoService.findBreakoutRoomById(breakoutRoomId);
        if (StringUtils.isBlank(breakoutRoom.getMeetingInfo())) {
            MeetingAttendeeDto meetingAndJoinAttendee = chimeService.createBreakoutRoomAndJoinAttendee(breakoutRoom, user, event);
            log.info("BreakoutRoomServiceImpl joinBreakoutRoom eventId {} sessionId {} BreakoutRoomById {} ", breakoutRoom.getEventId(), breakoutRoom.getSessionId(), breakoutRoom.getId());
            return setMeetingAndJoinAttendeeDetails(meetingAndJoinAttendee, breakoutRoom, event, sessionId);
        } else {
            try {
                Meeting meeting = SessionUtils.getMeeting(breakoutRoom.getMeetingInfo());
                MeetingAttendeeDto meetingAndJoinAttendee = chimeService.joinAttendee(user, meeting.getMeetingId());
                log.info("BreakoutRoomServiceImpl joinBreakoutRoom eventId {} sessionId {} BreakoutRoomById {} meetingId {} ", breakoutRoom.getEventId(), breakoutRoom.getSessionId(), breakoutRoom.getId(), meeting.getMeetingId());
                return setMeetingAndJoinAttendeeDetails(meetingAndJoinAttendee, breakoutRoom, event, sessionId);
            } catch (com.amazonaws.services.chimesdkmeetings.model.NotFoundException ne) {
                log.info("The meeting has ended", ne);
                if ((ne.getErrorMessage().equalsIgnoreCase("The meeting has ended") || ne.getErrorCode().equalsIgnoreCase("NotFoundException"))) {
                    MeetingAttendeeDto meetingAndJoinAttendee = chimeService.createBreakoutRoomAndJoinAttendee(breakoutRoom, user, event);
                    return setMeetingAndJoinAttendeeDetails(meetingAndJoinAttendee, breakoutRoom, event, sessionId);
                }
                throw new NotAcceptableException(ne);
            } catch (LimitExceededException ex) {
                log.info("The meeting limit exceed {} breakoutRoomId {}", ex, breakoutRoomId);
                throw new NotAcceptableException("4060012", ex.getErrorMessage(), ex.getErrorMessage());
            } catch (Exception e) {
                throw new NotAcceptableException(e);
            }
        }
    }

    private MeetingAttendeeDto setMeetingAndJoinAttendeeDetails(MeetingAttendeeDto meetingAndJoinAttendee, BreakoutRoom breakoutRoom, Event event, Long sessionId) {
        List<Long> eventAdmins = staffService.findAllEventAdminId(event);
        List<Long> speakerUserIds = sessionSpeakerRepo.findSpeakerUserIdBySessionId(sessionId);

        meetingAndJoinAttendee.setTitle(breakoutRoom.getRoomName());
        meetingAndJoinAttendee.setEventAdmins(eventAdmins);
        meetingAndJoinAttendee.setSpeakersOrModeratorsUserIds(speakerUserIds);

        breakoutRoom.setMeetingInfo(meetingAndJoinAttendee.getMeeting());
        breakoutRoomRepoService.save(breakoutRoom);
        return meetingAndJoinAttendee;
    }
}
