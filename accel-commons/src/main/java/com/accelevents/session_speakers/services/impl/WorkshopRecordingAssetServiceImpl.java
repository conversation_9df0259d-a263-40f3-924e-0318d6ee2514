package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.WorkshopRecordingAssetDetails;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.services.S3Wrapper;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import com.accelevents.session_speakers.services.WorkshopRecordingAssetService;
import com.accelevents.session_speakers.services.WorkshopRecordingAssetsRepoService;
import com.accelevents.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.accelevents.exceptions.NotFoundException.SessionNotFound.DEFAULT_PLAYBACK_NOT_FOUND;

@Service
public class WorkshopRecordingAssetServiceImpl implements WorkshopRecordingAssetService {

    private static final Logger logger = LoggerFactory.getLogger(WorkshopRecordingAssetServiceImpl.class);

    @Autowired
    private WorkshopRecordingAssetsRepoService workshopRecordingAssetsRepoService;

    @Autowired
    private S3Wrapper s3client;

    @Value("${cloud.aws.s3.bucket.workshopRecording}")
    private String bucketName;
    private double sequence = 1000;


    @Override
    public MuxAssetDTO findDefaultPlayBackForWorkshopSession(long sessionId) {
        MuxAssetDTO workshopRecordingAssetDetailsDTO = workshopRecordingAssetsRepoService.findDefaultPlayBackForWorkShopSession(sessionId);
        if (null == workshopRecordingAssetDetailsDTO) {
            throw new NotFoundException(DEFAULT_PLAYBACK_NOT_FOUND);
        }

        return workshopRecordingAssetDetailsDTO;
    }

    @Override
    public List<MuxAssetDTO> findAllLivestreamAssetsForWorkshopSession(long sessionId) {
        return workshopRecordingAssetsRepoService.getAllBySessionId(sessionId);
    }

    @Override
    public String generateWorkshopRecordingAssetPreSignedUrl(Long assetId) {
        try{
            Optional<WorkshopRecordingAssetDetails> details = Optional.ofNullable(workshopRecordingAssetsRepoService.findById(assetId).orElseThrow(
                    () -> new NotFoundException(DEFAULT_PLAYBACK_NOT_FOUND)
            ));
            if(details.isPresent()) {
                WorkshopRecordingAssetDetails workshopRecordingAsset = details.get();
                String playbackId= getWorkshopRecordingPlayBackId(workshopRecordingAsset);
                workshopRecordingAsset.setPlayBackId(playbackId);
                workshopRecordingAssetsRepoService.save(workshopRecordingAsset);
                return playbackId;
            }
        }catch (Exception e) {
            throw new NotAcceptableException(e);
        }

        return null;
    }

    @Override
    public void markAssetAsDefaultPlayBackForWorkshopSession(Session session, long id) {

        workshopRecordingAssetsRepoService.markAllWorkshopRecordingAssetAsNonDefaultPlayBack(session.getId());
        if (id > 0) {
            workshopRecordingAssetsRepoService.markWorkshopRecordingAssetAsDefaultPlayBack(id);
        }
    }

    @Override
    public void deleteWorkshopAssetFromS3Bucket(String objectKey) {
        if(StringUtils.isNotBlank(objectKey)) {
            s3client.deleteWorkshopRecordingFromS3Bucket(bucketName, objectKey);
        }
    }

    @Override
    public String getWorkshopRecordingPlayBackId(WorkshopRecordingAssetDetails assetsDetails) {
        StringBuilder playbackId = new StringBuilder(String.format(Constants.S3_BUCKET_URL_FORMATER, bucketName))
                .append(assetsDetails.getFileLocation()).append(Constants.SLASH_PROCESSED_SLASH).append(Constants.COMPOSITED_VIDEO).append(Constants.STRING_SLASH);

        return playbackId.append(assetsDetails.getFileName()).append(Constants.DOT_MP4).toString();
    }

    @Override
    public void saveWithSequence(WorkshopRecordingAssetDetails assetDetails) {
        WorkshopRecordingAssetDetails lastItemCategory = getLastAssetDetail(assetDetails);
        if (lastItemCategory != null) {
            assetDetails.setPosition(lastItemCategory.getPosition() + sequence);
        } else {
            assetDetails.setPosition(sequence);
        }
        workshopRecordingAssetsRepoService.save(assetDetails);
    }

    @Override
    @Transactional(rollbackOn = {Exception.class})
    public void updateWithSequence(Long id, Long topId, Long topBottomId, Event event, User user) {
        WorkshopRecordingAssetDetails assetDetails;
        Optional<WorkshopRecordingAssetDetails> optAssetDetails = this.findById(id);
        if (optAssetDetails.isPresent()) {
            assetDetails = optAssetDetails.get();
        } else {
            throw new NotFoundException(NotFoundException.SessionNotFound.PLAYBACK_NOT_FOUND);
        }
        WorkshopRecordingAssetDetails topItem = this.findById(topId).orElse(null);
        WorkshopRecordingAssetDetails topNextItem = this.findById(topBottomId).orElse(null);
        logger.info("request received for change workshop asset position|updateWithSequence|event {}|assetId {}| current Position {}| user {}", event.getEventId(), assetDetails.getId(), assetDetails.getPosition(), user.getUserId());
        if (topItem != null && topNextItem != null) {
            double position = (topNextItem.getPosition() + topItem.getPosition()) / 2;
            if (position == 1) {
                WorkshopRecordingAssetDetails itemNextPositionItem = getNextPositionItem(assetDetails);
                WorkshopRecordingAssetDetails itemPreviousPositionItem = getPreviousPositionItem(assetDetails);
                double positionDifferent = (itemNextPositionItem.getPosition() - itemPreviousPositionItem.getPosition()) / 2;
                updatePositionInGroup(topItem.getPosition(), assetDetails.getPosition(), positionDifferent, itemNextPositionItem.getSessionId(), itemNextPositionItem.getEventId());
                assetDetails.setPosition(topNextItem.getPosition());
                workshopRecordingAssetsRepoService.save(assetDetails);
            } else {
                assetDetails.setPosition(position);
                workshopRecordingAssetsRepoService.save(assetDetails);
            }
        } else if (topItem == null && topNextItem != null) {
            double posDiff = topNextItem.getPosition() - sequence;
            if (posDiff <= 1) {
                workshopRecordingAssetsRepoService.updatePositionForAllItem(assetDetails.getSessionId(), assetDetails.getEventId(), sequence);
            }
            assetDetails.setPosition(sequence);
            workshopRecordingAssetsRepoService.save(assetDetails);

        } else if (topItem != null) {
            assetDetails.setPosition(topItem.getPosition() + sequence);
            workshopRecordingAssetsRepoService.save(assetDetails);
        }
        logger.info("successfully changed workshop assets position|updateWithSequence|event {} |assetId {}| updated Position {}| user {}", event.getEventId(), assetDetails.getId(), assetDetails.getPosition(), user.getUserId());
    }

    private WorkshopRecordingAssetDetails getNextPositionItem(WorkshopRecordingAssetDetails assetDetails) {
        return workshopRecordingAssetsRepoService
                .nextPositionItem(assetDetails.getSessionId(), assetDetails.getEventId(), assetDetails.getPosition()).get(0);
    }

    private WorkshopRecordingAssetDetails getPreviousPositionItem(WorkshopRecordingAssetDetails assetDetails) {
        return workshopRecordingAssetsRepoService
                .previousPositionItem(assetDetails.getSessionId(), assetDetails.getEventId(), assetDetails.getPosition()).get(0);
    }

    private void updatePositionInGroup(double startPosition, double endPosition, double addingPositionValue,
                                       long sessionId, long eventId) {
        workshopRecordingAssetsRepoService.updatePositionItem(sessionId, eventId, startPosition, endPosition, addingPositionValue);
    }


    private Optional<WorkshopRecordingAssetDetails> findById(Long id) {
        return workshopRecordingAssetsRepoService.findById(id);
    }

    public WorkshopRecordingAssetDetails getLastAssetDetail(WorkshopRecordingAssetDetails assetDetails) {
        return workshopRecordingAssetsRepoService.findFirstBySessionIdAndEventIdOrderByPositionDesc(assetDetails.getSessionId(),
                assetDetails.getEventId());
    }

    @Override
    public List <WorkshopRecordingAssetDetails> findBySessionIdAndEventId(Long sessionId, Long eventId){
        return workshopRecordingAssetsRepoService.findBySessionIdAndEventId(sessionId, eventId);
    }

    @Override
    public void updateWorkshopAssetsVisibility(Long id, boolean isVisible, Event event, User user) {
        Optional<WorkshopRecordingAssetDetails> assetDetailsOpt = workshopRecordingAssetsRepoService.findById(id);
        if (assetDetailsOpt.isPresent()) {
            WorkshopRecordingAssetDetails assetDetails = assetDetailsOpt.get();
            assetDetails.setVisible(isVisible);
            workshopRecordingAssetsRepoService.save(assetDetails);
            logger.info("Workshop Asset Visibility updated for id=>{}, isVisible=>{}, updatedBy=> {}", id, isVisible, user.getUserId());
        }
    }

    @Override
    public void deleteWorkshopAssets(Long id, Event event, User user) {
        Optional<WorkshopRecordingAssetDetails> assetDetailsOpt = workshopRecordingAssetsRepoService.findById(id);
        if (assetDetailsOpt.isPresent()) {
            WorkshopRecordingAssetDetails assetDetails = assetDetailsOpt.get();
            assetDetails.setRecordStatus(RecordStatus.DELETE);
            workshopRecordingAssetsRepoService.save(assetDetails);
            logger.info("Workshop Asset deleted for id=>{}, status=>{}, updatedBy=> {}", id, RecordStatus.DELETE, user.getUserId());
        }
    }

    @Override
    public void updateWorkshopAssets(MuxAssetDTO muxAssetDTO, Event event, User user) {
        Optional<WorkshopRecordingAssetDetails> assetDetailsOpt = workshopRecordingAssetsRepoService.findById(muxAssetDTO.getId());
        if (assetDetailsOpt.isPresent()) {
            WorkshopRecordingAssetDetails assetDetails = assetDetailsOpt.get();
            assetDetails.setTitle(muxAssetDTO.getTitle());
            assetDetails.setDescription(muxAssetDTO.getDescription());
            workshopRecordingAssetsRepoService.save(assetDetails);
            logger.info("Workshop Asset updated for assetId=>{}, sessionId=>{}, updatedBy=> {}", assetDetails.getId(), assetDetails.getSessionId(), user.getUserId());
        }
    }

    @Override
    public List<WorkshopRecordingAssetDetails> getVisibleAssetsBySessionIdAndEventId(Long sessionId, Long eventId) {
        return workshopRecordingAssetsRepoService.getVisibleAssetsBySessionIdAndEventId(sessionId, eventId);
    }

    @Override
    public Map<Long,List<MuxAssetDTO>> getWorkshopRecordingAssetsMapByVisibleStatusAndSessionIds(List<Long> sessionIds) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyMap();
        }
        return workshopRecordingAssetsRepoService.findWorkshopRecordingByVisibleStatusSessionIdsIn(sessionIds, true).stream().collect(Collectors.groupingBy(MuxAssetDTO::getSessionId));
    }
}

