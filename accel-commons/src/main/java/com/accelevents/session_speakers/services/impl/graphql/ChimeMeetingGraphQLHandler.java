package com.accelevents.session_speakers.services.impl.graphql;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.graphql.GraphQL;
import com.accelevents.utils.Constants;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ChimeMeetingGraphQLHandler extends GraphQL {


    public ChimeMeetingGraphQLHandler(GraphQLConfiguration graphQLConfiguration) {
        super(graphQLConfiguration.getAppsyncChimeMeetingApiUrl(), Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
    }

    public Map<String, String> getChimeAttendee(String attendeeId, String meetingId) throws JSONException {
        String operationName = "getAccelChimeMeeting";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.QUERY, "query getAccelChimeMeeting { getAccelChimeMeeting(attendeeId:\"" +attendeeId + "\", meetingId: \""+meetingId+"\") { attendeeId meetingId attendeeName } }");
        return executeSelect(mainRequest, operationName);
    }


    public void handleCreateChimeMeetingAttendeeWithAsync(String meetingId, String attendeeId, String attendeeName, String sourceId, Long eventId, boolean isAdminStaffOrSuperAdmin) {
        new SimpleAsyncTaskExecutor().execute(() -> handleCreateChimeMeeting(meetingId, attendeeId, attendeeName,sourceId,eventId, isAdminStaffOrSuperAdmin));
    }

    public void handleDeleteChimeMeetingAttendeeWithAsync(String attendeeId, String meetingId) {
        new SimpleAsyncTaskExecutor().execute(() -> handleDeleteChimeMeeting(attendeeId, meetingId));
    }

    public boolean handleCreateChimeMeeting(String meetingId, String attendeeId, String attendeeName, String sourceId, Long eventId, boolean isAdminStaffOrSuperAdmin) {
        try {

            Map<String, String> map = getChimeAttendee(attendeeId, meetingId);
            if (StringUtils.isNotBlank(map.get(Constants.ATTENDEE_ID)) && StringUtils.isNotBlank(map.get(Constants.MEETING_ID))) {
                logger.info("handleCreateChimeMeeting updateChimeMeetingAttendee eventId {} sessionId {} assetId {} status {} sourceId {}",eventId, meetingId, attendeeId, attendeeName,sourceId);
                updateChimeMeetingAttendee(attendeeId,meetingId,Constants.JOINED);
            } else if(StringUtils.isNotBlank(sourceId)) {
                logger.info("handleCreateChimeMeeting createChimeMeetingAttendee  eventId {} , sessionId {} assetId {} status {} sourceId {} ", eventId, meetingId, attendeeId, attendeeName,sourceId);
                createChimeMeetingAttendee(meetingId, attendeeId, attendeeName,sourceId,eventId,isAdminStaffOrSuperAdmin);
            }
        } catch (JSONException e) {
            logger.info("Error While handleCreateChimeMeeting eventId=>{}, meetingId=>{}, attendeeId=>{}, attendeeName=>{} , sourceId=>{}",eventId, meetingId, attendeeId, attendeeName,sourceId);
        }
        return true;
    }

    public boolean handleDeleteChimeMeeting(String attendeeId, String meetingId) {
        try {
            if (StringUtils.isNotBlank(attendeeId) && StringUtils.isNotBlank(meetingId)) {
                logger.info("handleDeleteChimeMeeting meetingId {} |  attendeeId {}",meetingId, attendeeId);
                deleteChimeMeetingAttendee(attendeeId, meetingId);
            }
        } catch (JSONException e) {
            logger.info("Error While handleDeleteChimeMeeting meetingId=>{}, attendeeId=>{}",meetingId, attendeeId);
        }
        return true;
    }

    public boolean executeQuery(JSONObject mainRequest, String operationName) {
        Map<String, String> map = executeSelect(mainRequest, operationName);
        return StringUtils.isNotBlank(map.get(Constants.ATTENDEE_ID));
    }

    private Map<String, String> executeSelect(JSONObject mainRequest, String operationName) {
        try {
            JSONObject finalResult = execute(mainRequest, operationName);
            Map<String, String> map = new HashMap<>();

            if (finalResult.has(Constants.ATTENDEE_ID)) {
                map.put(Constants.ATTENDEE_ID, String.valueOf(finalResult.get(Constants.ATTENDEE_ID)));
            }
            if (finalResult.has(Constants.ATTENDEE_NAME)) {
                map.put(Constants.ATTENDEE_NAME, String.valueOf(finalResult.get(Constants.ATTENDEE_NAME)));
            }
            if (finalResult.has(Constants.MEETING_ID)) {
                map.put(Constants.MEETING_ID, finalResult.getString(Constants.MEETING_ID));
            }
            if (finalResult.has(Constants.SOURCE_ID)) {
                map.put(Constants.SOURCE_ID, finalResult.getString(Constants.SOURCE_ID));
            }
            if (finalResult.has(Constants.IS_ADMIN_STAFF_OR_SUPER_ADMIN)) {
                map.put(Constants.IS_ADMIN_STAFF_OR_SUPER_ADMIN, String.valueOf(finalResult.getBoolean(Constants.IS_ADMIN_STAFF_OR_SUPER_ADMIN)));
            }
            if (finalResult.has(Constants.STATUS)) {
                map.put(Constants.STATUS, String.valueOf(finalResult.getString(Constants.STATUS)));
            }
            return map;

        } catch (UnirestException e) {
            logger.error("GraphqlHandlerExecuteUnirest", e);
        } catch (JSONException e) {
            logger.error("GraphqlHandlerExecuteJSONException", e);
        }
        return Collections.emptyMap();
    }


    public boolean createChimeMeetingAttendee(String meetingId, String attendeeId, String attendeeName, String sourceId, Long eventId, boolean isAdminStaffOrSuperAdmin) throws JSONException {
        JSONObject variable = new JSONObject();
        logger.info("[MUTATION] CREATE | meetingId={} attendeeId={} attendeeName={} sourceId={} eventId={} isAdmin={}",
                meetingId, attendeeId, attendeeName, sourceId, eventId, isAdminStaffOrSuperAdmin);
        variable.put(Constants.CREATE_ACCEL_CHIME_MEETING_INPUT, setChimeMeetingStatusInput(meetingId, attendeeId, attendeeName,sourceId,eventId,isAdminStaffOrSuperAdmin));
        String operationName = Constants.CREATE_ACCEL_CHIME_MEETING;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, "mutation createAccelChimeMeeting($createAccelChimeMeetingInput: CreateAccelChimeMeetingInput!) { createAccelChimeMeeting(input: $createAccelChimeMeetingInput) { meetingId attendeeId attendeeName sourceId status,eventId isAdminStaffOrSuperAdmin } }");

        return executeQuery(mainRequest, operationName);
    }

    public JSONObject deleteAttendeeInput(String attendeeId, String meetingId) throws JSONException {
        JSONObject requestData = new JSONObject();
        requestData.put(Constants.ATTENDEE_ID, attendeeId);
        requestData.put(Constants.MEETING_ID, meetingId);
        return requestData;
    }

    public boolean deleteChimeMeetingAttendee(String attendeeId,String meetingId) throws JSONException {
        JSONObject variable = new JSONObject();
        variable.put(Constants.DELETE_ACCEL_CHIME_MEETING_INPUT, deleteAttendeeInput(attendeeId, meetingId));
        logger.info("[MUTATION] DELETE | meetingId={} attendeeId={}", meetingId, attendeeId);
        String operationName = Constants.DELETE_ACCEL_CHIME_MEETING;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, "mutation deleteAccelChimeMeeting($deleteAccelChimeMeetingInput: DeleteAccelChimeMeetingInput!) { deleteAccelChimeMeeting(input: $deleteAccelChimeMeetingInput) { meetingId attendeeId attendeeName sourceId status,eventId isAdminStaffOrSuperAdmin  } }");
        return executeQuery(mainRequest, operationName);
    }

    public boolean updateChimeMeetingAttendee(String attendeeId,String meetingId,String status) throws JSONException {
        JSONObject variable = new JSONObject();
        JSONObject requestData = new JSONObject();
        logger.info("[MUTATION] UPDATE | meetingId={} attendeeId={} status={}", meetingId, attendeeId, status);
        requestData.put(Constants.ATTENDEE_ID, attendeeId);
        requestData.put(Constants.MEETING_ID, meetingId);
        requestData.put(Constants.STATUS, status);
        variable.put("updateAccelChimeMeetingInput", requestData);

        String operationName = "updateAccelChimeMeeting";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, "mutation updateAccelChimeMeeting($updateAccelChimeMeetingInput: UpdateAccelChimeMeetingInput!) { updateAccelChimeMeeting(input: $updateAccelChimeMeetingInput) { meetingId attendeeId attendeeName sourceId status eventId isAdminStaffOrSuperAdmin } }");
        return executeQuery(mainRequest, operationName);
    }


    public JSONObject setChimeMeetingStatusInput(String meetingId, String attendeeId, String attendeeName, String sourceId, Long eventId, boolean isAdminStaffOrSuperAdmin) throws JSONException {
        JSONObject requestData = new JSONObject();
        requestData.put(Constants.MEETING_ID, meetingId);
        requestData.put(Constants.ATTENDEE_ID, attendeeId);
        requestData.put(Constants.ATTENDEE_NAME, attendeeName);
        requestData.put(Constants.EVENT_ID, eventId);
        requestData.put(Constants.SOURCE_ID, sourceId);
        requestData.put(Constants.STATUS, Constants.CREATED);
        requestData.put(Constants.IS_ADMIN_STAFF_OR_SUPER_ADMIN, isAdminStaffOrSuperAdmin);
        return requestData;
    }

    public List<JSONObject> getChimeMeetingAttendees(String sourceId) {
        String operationName = "listAccelChimeMeetings";
        try {
            JSONObject mainRequest = new JSONObject();
            mainRequest.put("operationName", operationName);
            // Set the variables
            mainRequest.put("variables", "{ \"sourceId\": \"" + sourceId + "\", \"nextToken\": null , \"limit\": " + 300 + " }");
            mainRequest.put("query",
                    "query listAccelChimeMeetings($sourceId: String!, $nextToken: String, $limit: Int) {  " +
                            "  listAccelChimeMeetings(sourceId: $sourceId, nextToken: $nextToken, limit: $limit) {  " +
                            "    items {  " +
                            "      attendeeId  " +
                            "      meetingId  " +
                            "      attendeeName  " +
                            "      sourceId  " +
                            "      eventId  " +
                            "      isAdminStaffOrSuperAdmin  " +
                            "      status  " +
                            "    }  " +
                            "    nextToken  " +
                            "  }  " +
                            "}");

            JSONArray jsonArray = executeItemList(mainRequest, operationName);
            List<JSONObject> jsonObjectList = new ArrayList<>();
            if (null != jsonArray && jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = (JSONObject) jsonArray.get(i);
                    if (null != jsonObject && jsonObject.has(Constants.IS_ADMIN_STAFF_OR_SUPER_ADMIN) && !jsonObject.getBoolean(Constants.IS_ADMIN_STAFF_OR_SUPER_ADMIN)) {
                        jsonObjectList.add((JSONObject) jsonArray.get(i));
                    }
                }
            }
            return jsonObjectList;
        } catch (JSONException e) {
            logger.error("getChimeMeetingAttendees --> {}", e);
            throw new RuntimeException(e);
        }
    }


    public void deleteMeetingAttendeesByMeetingId(String meetingId) {
        String operationName = "listAccelChimeMeetingsByMeetingId";
        try {
            JSONObject mainRequest = new JSONObject();
            mainRequest.put("operationName", operationName);
            // Set the variables
            mainRequest.put("variables", "{ \"meetingId\": \"" + meetingId + "\", \"nextToken\": null , \"limit\": " + 300 + " }");
            mainRequest.put("query",
                    "query listAccelChimeMeetingsByMeetingId($meetingId: String!, $nextToken: String, $limit: Int) {  " +
                            "  listAccelChimeMeetingsByMeetingId(meetingId: $meetingId, nextToken: $nextToken, limit: $limit) {  " +
                            "    items {  " +
                            "      attendeeId  " +
                            "      meetingId  " +
                            "      attendeeName  " +
                            "      sourceId  " +
                            "      eventId  " +
                            "      isAdminStaffOrSuperAdmin  " +
                            "      status  " +
                            "    }  " +
                            "    nextToken  " +
                            "  }  " +
                            "}");

            JSONArray jsonArray = executeItemList(mainRequest, operationName);
            if (null != jsonArray && jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = (JSONObject) jsonArray.get(i);
                    if (null != jsonObject && jsonObject.has(Constants.ATTENDEE_ID) ) {
                        deleteChimeMeetingAttendee(jsonObject.getString(Constants.ATTENDEE_ID),meetingId);
                    }
                }
            }

        } catch (JSONException e) {
            logger.error("deleteMeetingAttendeesByMeetingId | meetingId--->{} | Exception", meetingId, e);
            throw new RuntimeException(e);
        }
    }
}
