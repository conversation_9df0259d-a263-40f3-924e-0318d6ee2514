package com.accelevents.session_speakers.services;

import com.accelevents.auction.dto.UploadSessionAttendeeResponseContainer;
import com.accelevents.common.dto.AttendeeAnalyticCountDTO;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventTickets;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionCheckInLogStatus;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.exceptions.BaseException;
import com.accelevents.session_speakers.dto.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;


public interface UserSessionService {
	void unRegisterUser(UserSessionDTO userSessionDTO, Long eventTicketId, Event event, boolean isAdminOrStaff, boolean isUnBookmarked, User user);

    void unRegisterUserWithoutEventTicket(Long sessionId, Long userId, Event event);

    void checkOutUserSession(UserSessionDTO dto, Long eventTicketId, Event event, String device, User staffUser, boolean offlineData, String source, String sourceDescription, boolean isAppLogin);

    UserSession registerUser(Long eventTicketId, User loggedInUser, Session session, Event event);

    UserSession registerUserWithoutTicket(User loggedInUser, Session session, Event event);

    boolean isUserAvailableStateAndTicketTypesIn(long userId, long eventId, long sessionId, List<Long> ticketTypeIds);

	List<IdCountDto> getSessionStatsForIdsIn(List<Long> sessionIds);

	List<IdCountDto> countSessionAttendeeBySessionIdIn(List<Long> sessionIds);

    List<IdCountDto> countSessionAttendeeByEventAndUserIds(List<Long> eventIds, List<Long> userIds);

    long countSessionAttendeeBySessionId(Long sessionId);

	List<RegisterdHolderUsers> findRegisteredUserInfo(long sessionId);

    List<RegisterdHolderUsers> getRegisteredUserInfo(long sessionId);

    List<AttendeeSession> findAttendeeSessionsByUserId(Event event, long userId, boolean fetchEngagement, String searchString);

    Map<Long, List<Long>> findRegisteredEventTicketIdByUserAndSessionId(Long userId, List<Long> sessionIds);

	void updateStatus(Long sessionId, Long userId, String status, Event event, String region, String device);

	void updateUserSessionState(long sessionId, long eventId, List<Long> userIds, EnumUserSessionStatus state);

    void updateUserSessionCheckInState(long sessionId,long eventId, List<Long> userIds, EnumUserSessionStatus state);

	Integer countRegisteredBySessionId(Long sessionId);

    List<Long> getEventTicketIdsByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId);

	List<Long> getTicketingTypeIdByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId);

	boolean registerUserFromPortal( User user, Session session, Boolean joinSession, Event event, Boolean pastJoinSession, Boolean isAdminOrStaff, String device, boolean isBookmarked, boolean isSaveASeat, boolean joinWaitlist);

    void registerUserFromStaff(User user, UserSessionDTO userSessionDTO, Boolean join, Event event, Boolean pastJoinSession, String device, User staffuser, boolean isAppLogin, String source, String sourceDescription, StringBuilder barcode);

    boolean isEventStaffOrSpeaker(User user, Session session, Event event);

    void registerUserFromHost(UserSessionDTO userSessionDTO, Session session, Event event);

    UploadSessionAttendeeResponseContainer registerUserFromHostInBulk(Long sessionId, MultipartFile multiPartFile, Event event,String languageCode);

    void registerSpeakerUser(Long speakerUserId, Long sessionId, Event event);

    void unRegisterSpeakerUser(Long speakerUserId, Long sessionId, Event event);

    List<Long> getUserSessionByUserId(Long userId, Long eventId);

	List<Long> findByEventIdAndUserId(long eventId, Long userId);

	List<Long> findActiveUsersByTicketTypesInAndNotInUserId(long sessionId, long eventId, List<Long> ticketTypeIds,
			List<Long> userId);

	boolean isUserAvailableState(long userId, long eventId, long sessionId);

    int getCheckInUserCountBySession(Long sessionId, Event event);

    Long getRegisteredSessionCountForUser(long eventId, Long userId);

    void deleteBySessionId(Long sessionId);

	List<Long> getEventTicketIdsByEventIdAndUserIdAndSessionPuchaserId(long eventId, Long userId, Long sessionId);

	List<UserSession> getAllUserSessionBySessionId(long sessionId);

    List<RegisterdHolderUsers> findSessionAttendedUserInfo(long sessionId);

    DataTableResponse findSessionAttendedUserInfoPage(long sessionId, int page, int size);

	String getUserRegionByUserAndSessionIdAndCheckedInAvailable(Long userId, Long sessionId);

	List<AttendeeAnalyticsDTO> findAllAttendeeSessionsByEventId(Long eventId,Map<Long, List<Map<Long,String>>> savedAttendeeDetail);

    List<AttendeeSession> findAttendeeNetworkingSessionsByUserId(long eventId, long userId, EnumSessionFormat meetUp);

    Long getRegisteredSessionCountForUserBySessionFormat(long eventId, Long userId, EnumSessionFormat sessionFormat);

    void deleteByEventId(Long eventId);

    Integer getUserRegisteredSessionBySession(Long sessionId, Event event);

    Integer getUserRemainingRegisteredCountByEventIdAndUserId(Long eventId, Long userId);

    Integer countSessionAttendeeByEventId(Long eventId);

    void checkInSessionUsingBarcodeId(Event event, String barcode, UserSessionDTO userSessionDTO, Boolean join, Boolean pastJoinSession, String device, User staffUser, boolean offlineData, boolean isAppLogin, String source, String sourceDescription, boolean isRFID);

    void updateUserSessionUserId(Event event, User newUser, User oldUserId, Long eventTicketId);

    void userSessionAddToCalendar(Long eventId,Long userId, Long sessionId);

    AttendeeAnalyticCountDTO getSessionAttedneesAndRegisteredCount(Long sessionId);

    UserSessionBasicDetailsDTO isUserRegisteredInSession(Long sessionId, String barcodeId, Event event);

    List<IdCountDto> getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(List<Long> sessionIds, long eventId);

    Integer getSessionRegisterCountAndTicketIdIsNotNullAndEventId(Long sessionId, long eventId);

    Map<Long, Boolean> findBookmarkedSessionByUserIdAndSessionId(Long userId, List<Long> sessionIds);

    Map<Long, Boolean> findWaitlistedSessionByUserIdAndSessionId(Long userId, List<Long> sessionIds);

    List<IdCountDto> countSessionBookmarkedAttendeeBySessionIdInAndEventId(List<Long> sessionIds, long eventId);

    void sendSaveASeatTestEmail(Event event,String email, boolean isAdminOrStaff);

    boolean canUserViewSessionDocsAndLinks(User user, Session session, Event event);

    UploadSessionAttendeeResponseContainer registerBulkUsersFromHost(Session session, Event event, String userIds, String languageCode);

    void removeUserFromWaitlist(UserSessionDTO userSessionDTO, Event event, boolean isUnBookmarked);

    void createSessionCheckinCheckoutActivityLog(Event event, User user, String barcodeId, EnumSessionCheckInLogStatus sessionCheckInLogStatus, boolean success, BaseException baseException, boolean isRfidCheckIn, String source, String sourceDescription, boolean isAppLogin, EventTickets eventTickets);
}
