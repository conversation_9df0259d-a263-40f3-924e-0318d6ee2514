package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.enums.EnumSessionCheckInLogStatus;
import com.accelevents.domain.session_speakers.SessionCheckInLog;
import com.accelevents.session_speakers.repo.SessionCheckInLogRepo;
import com.accelevents.session_speakers.services.SessionCheckInLogRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SessionCheckInLogRepoServiceImpl implements SessionCheckInLogRepoService {

    @Autowired
    private SessionCheckInLogRepo sessionCheckInLogRepo;

    @Override
    public SessionCheckInLog save(SessionCheckInLog sessionCheckInLog) {
        return sessionCheckInLogRepo.save(sessionCheckInLog);
    }

    @Override
    public SessionCheckInLog findFirstByUserIdAndSessionIdOrderByIdDesc(Long userId, Long sessionId) {
        return sessionCheckInLogRepo.findFirstByUserIdAndSessionIdOrderByIdDesc(userId, sessionId);
    }

    @Override
    public List<SessionCheckInLog> findAllSessionCheckInLogBySessionIdOrderByAuditTimeAsc(Long sessionId) {
        return sessionCheckInLogRepo.findAllSessionCheckInLogBySessionIdOrderByAuditTimeAsc(sessionId);
    }
    @Override
    public SessionCheckInLog findUserIdAndSessionIdOrderByIdAsc(Long userId, Long sessionId){
        return sessionCheckInLogRepo.findFirstByUserIdAndSessionIdOrderByIdAsc(userId,sessionId);
    }

    @Override
    public List<SessionCheckInLog> findAllSessionLogsBySessionIdAndStatus(Long sessionId,EnumSessionCheckInLogStatus auditStatus) {
        return sessionCheckInLogRepo.findAllSessionLogsBySessionIdAndStatus(sessionId,auditStatus);
    }

    @Override
    public SessionCheckInLog findFirstByUserIdAndSessionIdAndEventTicketIdOrderByIdDesc(Long userId, Long sessionId,Long eventTicketId) {
        return sessionCheckInLogRepo.findFirstByUserIdAndSessionIdAndEventTicketIdOrderByIdDesc(userId,sessionId,eventTicketId);
    }
}
