package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionTagAndTrack;

import java.util.List;
import java.util.Set;

public interface SessionTagAndTrackRepoService
{
    void deleteBySessionIdAndTagOrTrackIdIn(Long sessionId,Set<Long> tagIdOrTrackIds);

    List<Long> findByIds(List<Long> tagOrTrackIds);

    void deleteByTagOrTrackIds(List<Long> tagIdOrTrackId);

    void deleteBySessionId(Long sessionId);

    List<SessionTagAndTrack> findBySessionId(Long sessionId);

    Iterable<SessionTagAndTrack> saveAll(Iterable<SessionTagAndTrack> sessionTagAndTracks);

    List<Long> findSessionIdsByIds(List<Long> tagOrTrackIds);

    List<Session> findSessionsByTagOrTrackIds(List<Long> tagOrTrackIds);

    Boolean existsBySessionIdAndTagOrTrackId(Long sessionId, Long tagOrTrackId);

    List<Long> existsBySessionIdAndTagOrTrackIds(Long sessionId, List<Long> tagOrTrackId);

}
