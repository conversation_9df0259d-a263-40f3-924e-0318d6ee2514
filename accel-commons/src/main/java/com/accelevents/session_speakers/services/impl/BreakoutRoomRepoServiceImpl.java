package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.BreakoutRoom;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.session_speakers.repo.BreakoutRoomRepo;
import com.accelevents.session_speakers.services.BreakoutRoomRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class BreakoutRoomRepoServiceImpl implements BreakoutRoomRepoService {

    @Autowired
    private BreakoutRoomRepo breakoutRoomRepo;

    @Override
    public void saveAll(List<BreakoutRoom> breakoutRooms) {
        breakoutRoomRepo.saveAll(breakoutRooms);
    }

    @Override
    public List<BreakoutRoom> getAllBreakoutRoom(Long sessionId, long eventId) {
        return breakoutRoomRepo.getAllBreakoutRoomBySessionIdAndEventId(sessionId,eventId);
    }

    @Override
    public BreakoutRoom findBreakoutRoomById(Long id) {
        return breakoutRoomRepo.findById(id).orElseThrow(()->new NotFoundException(NotFoundException.BreakoutRoomNotFound.SESSION_BREAKOUT_ROOM_NOT_FOUND));
    }

    @Override
    public void save(BreakoutRoom breakoutRoom) {
        breakoutRoomRepo.save(breakoutRoom);
    }

    @Override
    public boolean isBreakoutRoomPresentWithNameAndEventIdAndSessionId(String sessionBreakoutRoomName, long eventId, Long sessionId) {
        return breakoutRoomRepo.isBreakoutRoomPresentWithNameAndEventIdAndSessionId(sessionBreakoutRoomName, eventId, sessionId);
    }

    @Override
    public boolean isBreakoutRoomPresentWithIdAndEventIdAndSessionId(Long roomId, long eventId, Long sessionId) {
        return breakoutRoomRepo.isBreakoutRoomPresentWithIdAndEventIdAndSessionId(roomId, eventId, sessionId);
    }

    @Override
    public int findBreakoutRoomCounterBySessionIdAndEventIdOrderByIdDesc(Long sessionId, long eventId) {
        Optional<BreakoutRoom> getLastBreakoutRoomCounter = breakoutRoomRepo.findFirstBySessionIdAndEventIdOrderByIdDesc(sessionId, eventId);
        return getLastBreakoutRoomCounter.map(BreakoutRoom::getBreakOutRoomCounter).orElse(0);
    }

    @Override
    public Long countAllByEventIdAndSessionId(Long eventId, Long sessionId) {
        return breakoutRoomRepo.countAllByEventIdAndSessionId(eventId,sessionId);
    }
}
