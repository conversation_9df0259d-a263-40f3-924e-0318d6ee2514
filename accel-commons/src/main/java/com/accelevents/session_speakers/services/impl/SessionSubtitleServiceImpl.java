package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.SessionSubtitle;
import com.accelevents.session_speakers.dto.SessionSubtitleDTO;
import com.accelevents.session_speakers.services.SessionSubtitleRepoService;
import com.accelevents.session_speakers.services.SessionSubtitleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SessionSubtitleServiceImpl implements SessionSubtitleService {

    private final SessionSubtitleRepoService sessionSubtitleRepoService;

    @Autowired
    public SessionSubtitleServiceImpl(SessionSubtitleRepoService sessionSubtitleRepoService){
        this.sessionSubtitleRepoService = sessionSubtitleRepoService;
    }

    @Override
    public void save(SessionSubtitle sessionSubtitle){
        sessionSubtitleRepoService.save(sessionSubtitle);
    }

    @Override
    public void saveAll(List<SessionSubtitle> sessionSubtitleList){
        sessionSubtitleRepoService.saveAll(sessionSubtitleList);
    }

    @Override
    public void deleteById(Long subtitleId){
        sessionSubtitleRepoService.deleteById(subtitleId);
    }

    @Override
    public List<SessionSubtitleDTO> getSubtitleListByMuxIdAndSessionId(Long sessionId, Long muxId){
        return sessionSubtitleRepoService.getSubtitleListByMuxIdAndSessionId(sessionId, muxId);
    }

    @Override
    public Map<Long,List<SessionSubtitleDTO>> getMapOfMuxIdAndSubtitleListByMuxIdListAndSessionId(List<Long> muxIds, List<Long> sessionIds){
        Map<Long,List<SessionSubtitleDTO>> muxIdAndSessionSubtitleMap = Collections.emptyMap();
        List<SessionSubtitleDTO> sessionSubtitleDTOList = sessionSubtitleRepoService.getSubtitleListByMuxIdListAndSessionId(muxIds, sessionIds);
        if (!CollectionUtils.isEmpty(sessionSubtitleDTOList)) {
            muxIdAndSessionSubtitleMap = sessionSubtitleDTOList.stream()
                    .collect(Collectors.groupingBy(SessionSubtitleDTO::getMuxId));
        }
        return muxIdAndSessionSubtitleMap;
    }

    @Override
    public List<SessionSubtitle> findBySessionIdAndMuxId(Long sessionId, Long muxId){
       return sessionSubtitleRepoService.findBySessionIdAndMuxId(sessionId, muxId);
    }

    @Override
    public boolean languageCodeExistByMuxIdAndSessionId(Long muxId, Long sessionId, String languageCode, Long subtitleId){
        return sessionSubtitleRepoService.languageCodeExistByMuxIdAndSessionId(muxId, sessionId, languageCode, subtitleId);
    }

}
