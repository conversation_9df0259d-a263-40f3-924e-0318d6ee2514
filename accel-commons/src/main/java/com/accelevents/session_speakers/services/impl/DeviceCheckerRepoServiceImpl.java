package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.DeviceCheckerType;
import com.accelevents.domain.enums.EnumLabelLanguageCode;
import com.accelevents.domain.session_speakers.DeviceChecker;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.session_speakers.DeviceCheckerRepoService;
import com.accelevents.session_speakers.dto.UserDeviceCheckedDto;
import com.accelevents.session_speakers.repo.DeviceCheckerRepo;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.TimeZoneUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;

@Service
public class DeviceCheckerRepoServiceImpl implements DeviceCheckerRepoService {

    @Autowired
    private DeviceCheckerRepo deviceCheckerRepo;

    @Override
    public void save(DeviceChecker deviceChecker) {
        deviceCheckerRepo.save(deviceChecker);
    }

    @Autowired
    private ROVirtualEventService roVirtualEventService;
    Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

    @Override
    public List<UserDeviceCheckedDto> userIdAndRunDeviceChecker(List<Long> userIds) {
        return deviceCheckerRepo.userIdAndRunDeviceChecker(userIds);
    }

    @Override
    public String checkUserDeviceCheckerWithDeviceId(Long userId, Event event, String machineId, DeviceCheckerType deviceCheckerType,String languageCode) {
        Optional<DeviceChecker> deviceChecker = deviceCheckerRepo.findFirstByUserIdAndEventIdAndMachineIdAndTypeOrderByIdDesc(userId, event.getEventId(), machineId, deviceCheckerType);
        if (deviceChecker.isPresent()){
            if (null == languageCode) {
                languageCode = roVirtualEventService.findLabelLanguageCodeByEventId(event.getEventId());
            }
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
            String date = TimeZoneUtil.getDateInLocal(deviceChecker.get().getCreatedAt(), event.getEquivalentTimeZone(), Constants.DATE__TIME_FORMAT_MONTH);
            String deviceCheckerWithDeviceID = resourceBundle.getString(languageMap.get(Constants.DEVICE_CHECKED_WITH_DEVICE_ID));
            return deviceCheckerWithDeviceID.replace("${DATE}", date);
        }else {
            return null;
        }
    }
}
