package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.WorkshopRecordingAssetDetails;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import com.accelevents.session_speakers.repo.WorkshopRecordingAssetsRepo;
import com.accelevents.session_speakers.services.WorkshopRecordingAssetsRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class WorkshopRecordingAssetsRepoServiceImpl implements WorkshopRecordingAssetsRepoService {

    @Autowired
    private WorkshopRecordingAssetsRepo workshopRecordingAssetsRepo;


    @Override
    public WorkshopRecordingAssetDetails save(WorkshopRecordingAssetDetails workshopRecordingAssetDetails) {
        return workshopRecordingAssetsRepo.save(workshopRecordingAssetDetails);
    }

    @Override
    public Optional<WorkshopRecordingAssetDetails> findByPipelineId(String pipelineId) {
        return workshopRecordingAssetsRepo.findByPipelineId(pipelineId);
    }

    @Override
    public void markAllWorkshopRecordingAssetAsNonDefaultPlayBack(Long sessionId) {
        workshopRecordingAssetsRepo.markAllWorkshopRecordingAssetAsNonDefaultPlayBack(sessionId);
    }

    @Override
    public void markWorkshopRecordingAssetAsDefaultPlayBack(Long id) {
        workshopRecordingAssetsRepo.markWorkshopRecordingAssetAsDefaultPlayBack(id);
    }

    @Override
    public MuxAssetDTO findDefaultPlayBackForWorkShopSession(Long sessionId) {
        return workshopRecordingAssetsRepo.findDefaultPlayBackForWorkShopSession(sessionId);
    }

    @Override
    public List<MuxAssetDTO> getAllBySessionId(Long sessionId) {
        return workshopRecordingAssetsRepo.getAllBySessionId(sessionId);
    }

    @Override
    public Optional<WorkshopRecordingAssetDetails> findById(Long id) {
        return workshopRecordingAssetsRepo.findById(id);
    }

    @Override
    public List<WorkshopRecordingAssetDetails> findAllBySessionId(Long sessionId) {
        return workshopRecordingAssetsRepo.findAllBySessionId(sessionId);
    }

    @Override
    public Optional<WorkshopRecordingAssetDetails> findBySessionIdAndPipelineIdIsNull(Long sessionId) {
        return workshopRecordingAssetsRepo.findBySessionIdAndPipelineIdIsNull(sessionId);
    }

    @Override
    public Optional<WorkshopRecordingAssetDetails> findBySessionIdAndPipelineId(Long sessionId, String pipelineId) {
        return workshopRecordingAssetsRepo.findBySessionIdAndPipelineId(sessionId, pipelineId);
    }

    @Override
    public List<MuxAssetDTO> findAllBySessionIds(List<Long> sessionIds) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyList();
        }
        return workshopRecordingAssetsRepo.getAllWorkshopAssetDetailsBySessionIds(sessionIds);
    }
    @Override
    public Optional<WorkshopRecordingAssetDetails> findBySessionIdAndAttendeeCountIsNotNull(Long sessionId) {
        return workshopRecordingAssetsRepo.findBySessionIdAndAttendeeCountIsNotNull(sessionId);
    }
    @Override
    public Optional<WorkshopRecordingAssetDetails> findBySessionIdAndAttendeeCountIsNotNullOrPipelineIdIsNull(Long sessionId) {
        return workshopRecordingAssetsRepo.findBySessionIdAndAttendeeCountIsNotNullOrPipelineIdIsNull(sessionId);
    }

    @Override
    public Optional<WorkshopRecordingAssetDetails> findByFileName(String pipelineId) {
        return workshopRecordingAssetsRepo.findByFileName(pipelineId);
    }

    @Override
    public List<WorkshopRecordingAssetDetails> findBySessionIdAndEventId(Long sessionId, Long eventId) {
        return workshopRecordingAssetsRepo.findBySessionIdAndEventId(sessionId, eventId);
    }

    @Override
    public WorkshopRecordingAssetDetails findFirstBySessionIdAndEventIdOrderByPositionDesc(Long sessionId, Long eventId) {
        return workshopRecordingAssetsRepo.findFirstBySessionIdAndEventIdOrderByPositionDesc(sessionId, eventId);
    }

    @Override
    public void updatePositionItem(long sessionId, long eventId, double startPosition, double endPosition, double addingPositionValue) {
        workshopRecordingAssetsRepo.updatePositionItem(sessionId, eventId, startPosition, endPosition, addingPositionValue);
    }

    @Override
    public void updatePositionForAllItem(Long sessionId, Long eventId, double sequence) {
        workshopRecordingAssetsRepo.updatePositionForAllItem(sessionId, eventId, sequence);
    }

    @Override
    public  List<WorkshopRecordingAssetDetails> nextPositionItem(Long sessionId, Long eventId, double position) {
        return workshopRecordingAssetsRepo.nextPositionItem(sessionId, eventId, position);
    }

    @Override
    public List<WorkshopRecordingAssetDetails> previousPositionItem(Long sessionId, Long eventId, double position) {
        return workshopRecordingAssetsRepo.previousPositionItem(sessionId, eventId, position);
    }

    @Override
    public List<WorkshopRecordingAssetDetails> getVisibleAssetsBySessionIdAndEventId(Long sessionId, Long eventId) {
        return workshopRecordingAssetsRepo.getVisibleAssetsBySessionIdAndEventId(sessionId, eventId);
    }

    @Override
    public List<MuxAssetDTO> findWorkshopRecordingByVisibleStatusSessionIdsIn(List<Long> sessionIds, boolean visible) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyList();
        }
        return workshopRecordingAssetsRepo.findWorkshopRecordingByVisibleStatusSessionIdsIn(sessionIds, visible);
    }
}
