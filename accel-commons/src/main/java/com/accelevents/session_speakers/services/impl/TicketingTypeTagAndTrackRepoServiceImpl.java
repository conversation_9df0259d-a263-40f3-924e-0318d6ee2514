package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.TicketingTypeTagAndTrack;
import com.accelevents.repositories.TicketingTypeTagAndTrackRepo;
import com.accelevents.session_speakers.dto.KeyValueDTO;
import com.accelevents.session_speakers.services.TicketingTypeTagAndTrackRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public class TicketingTypeTagAndTrackRepoServiceImpl implements TicketingTypeTagAndTrackRepoService
{
    @Autowired private TicketingTypeTagAndTrackRepo ticketingTypeTagAndTrackRepo;

    @Override
    public Iterable<TicketingTypeTagAndTrack> saveAll(Iterable<TicketingTypeTagAndTrack> ticketTagAndTracks) {
        return ticketingTypeTagAndTrackRepo.saveAll(ticketTagAndTracks);
    }

    @Override
    public void deleteByTicketIdAndTagOrTrackIdIn(Long ticketingTypeId, Set<Long> tagIdOrTrackIds) {
        ticketingTypeTagAndTrackRepo.deleteByTicketIdAndTagOrTrackIdIn(ticketingTypeId, tagIdOrTrackIds);
    }

    @Override
    public List<TicketingTypeTagAndTrack> findByTicketingTypeId(Long ticketingTypeId) {
        return ticketingTypeTagAndTrackRepo.findByTicketingTypeId(ticketingTypeId);
    }

    @Override
    public List<KeyValueDTO> findTagAndTrackByTicketingTypeIds(List<Long> ticketingTypeIds) {
        return ticketingTypeTagAndTrackRepo.findTagAndTrackByTicketingTypeIds(ticketingTypeIds);
    }

    @Override
    public List<TicketingTypeTagAndTrack> findByTicketingTypeIdAndTagOrTrackIdIn(Long eventTicketingTypeId, Set<Long> tagIdOrTrackIds) {
        return ticketingTypeTagAndTrackRepo.findByTicketingTypeIdAndTagOrTrackIdIn(eventTicketingTypeId, tagIdOrTrackIds);
    }

    @Override
    public void deleteByTagOrTrackId(List<Long> ids) {
        ticketingTypeTagAndTrackRepo.deleteByTagOrTrackId(ids);
    }
}
