package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.UserInterestedSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.repo.UserInterestedSessionRepo;
import com.accelevents.session_speakers.services.UserInterestedSessionRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;

@Service
public class UserInterestedSessionRepoServiceImpl  implements UserInterestedSessionRepoService
{

    @Autowired
    private UserInterestedSessionRepo userInterestedSessionRepo;

    @Override
    public void deleteBySessionIdAndUserId(Long sessionId, Long userId) {

        userInterestedSessionRepo.deleteBySessionIdAndUserId(sessionId,userId);
    }

    @Override
    public List<Long> findByEventIdAndUserId(Long eventId, Long userId) {
        return userInterestedSessionRepo.findByEventIdAndUserId(eventId,userId);
    }

    @Override
    public List<IdCountDto> countBySessionIdIn(List<Long> sessionIds) {
        return userInterestedSessionRepo.countBySessionIdIn(sessionIds);
    }

    @Override
    public BigInteger countBySessionId(Long sessionId) {
        return userInterestedSessionRepo.countBySessionId(sessionId);
    }

    @Override
    public void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status) {
        userInterestedSessionRepo.updateStatusToDeleteBySessionId(sessionId,status);
    }

    @Override
    public UserInterestedSession save(UserInterestedSession userSession) {
        return userInterestedSessionRepo.save(userSession);
    }
}
