package com.accelevents.session_speakers.services.impl;

import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumSessionStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.dto.SessionSpeakerIdDto;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.repo.SessionSpeakerRepo;
import com.accelevents.session_speakers.services.SessionSpeakerRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class SessionSpeakerRepoServiceImpl implements SessionSpeakerRepoService {

    @Autowired private SessionSpeakerRepo sessionSpeakerRepo;

    @Override
    public SessionSpeaker save(SessionSpeaker sessionSpeaker){
        SessionSpeaker savedSessionSpeaker = sessionSpeakerRepo.save(sessionSpeaker);

        return savedSessionSpeaker;
    }

    @Override
    public Iterable<SessionSpeaker> saveAll(Iterable<SessionSpeaker> sessionSpeakers){
        return sessionSpeakerRepo.saveAll(sessionSpeakers);
    }

    @Override
    public List<Long> findAllSessionIdBySpeakerUserId(Long userId, Long eventId){
        return sessionSpeakerRepo.findAllSessionIdBySpeakerUserId(userId, eventId);
    }

    @Override
    public void deleteBySessionId(Long sessionId) {
        sessionSpeakerRepo.deleteBySessionId(sessionId);
    }

    @Override
    public void deleteBySessionIds(List<Long> sessionId) {
        sessionSpeakerRepo.deleteBySessionIds(sessionId);
    }

    @Override
    public Optional<SessionSpeaker> findBySessionIdAndSpeakerId(Long sessionId, Long speakerId) {
        return sessionSpeakerRepo.findBySessionIdAndSpeakerId(sessionId,speakerId);
    }

    @Override
    public boolean sessionSpeakerAlreadyAddedInThisSession(Long sessionId, Long eventId) {
        return sessionSpeakerRepo.sessionSpeakerAlreadyAddedInThisSession(sessionId,eventId);
    }

    @Override
    public List<SpeakerDTO> getSpeakersBySession(Long sessionId) {
        return sessionSpeakerRepo.getSpeakersBySession(sessionId);
    }

    @Override
    public Page<Session> findSessionBySpeakerUserIdFuture(Long eventId, Long userId, String search, Date currentDate, Pageable pageable, List<EnumSessionFormat> enumSessionFormats, EnumSessionStatus status) {
        return sessionSpeakerRepo.findSessionBySpeakerUserIdFuture(eventId, userId, search, currentDate, pageable, enumSessionFormats, status);
    }

    @Override
    public void deleteBySpeakerId(Long speakerId) {
        sessionSpeakerRepo.deleteBySpeakerId(speakerId);
    }

    @Override
    public List<SessionSpeaker> findBySpeakerIdAndEventId(Long speakerId, Long eventId) {
        return sessionSpeakerRepo.findBySpeakerIdAndEventId(speakerId,eventId);
    }

    @Override
    public List<SessionSpeaker> findBySessionIdsIn(List<Long> sessionIds) {
        return sessionSpeakerRepo.findBySessionIdsIn(sessionIds);
    }

    @Override
    public void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status) {

        sessionSpeakerRepo.updateStatusToDeleteBySessionId(sessionId,status);
    }

    @Override
    public void deleteById(Long id) {
        sessionSpeakerRepo.deleteById(id);
    }

    @Override
    public List<SessionSpeaker> findBySessionIdAndEventId(Long sessionId, Long eventId) {
        return sessionSpeakerRepo.findBySessionIdAndEventId(sessionId,eventId);
    }

    @Override
    public BigInteger isUserSpeakerInSession(Long userId, Long sessionId) {
        return sessionSpeakerRepo.isUserSpeakerInSession(userId,sessionId);
    }

    @Override
    public List<SessionSpeaker> nextPositionSessionSpeaker(Long sessionSpeakerId, Long sessionId, Double currentPosition) {
        return sessionSpeakerRepo.nextPositionSessionSpeaker(sessionSpeakerId,sessionId,currentPosition);
    }

    @Override
    public List<SessionSpeaker> previousPositionSessionSpeaker(Long sessionSpeakerId, Long sessionId, Double currentPosition) {
        return sessionSpeakerRepo.previousPositionSessionSpeaker(sessionSpeakerId,sessionId,currentPosition);
    }

    @Override
    public void updatePositionSessionSpeaker(Long speakerId, Double startPosition, Double endPosition, Double updateCount) {

        sessionSpeakerRepo.updatePositionSessionSpeaker(speakerId,startPosition,endPosition,updateCount);
    }

    @Override
    public void updatePositionForAllSessionSpeakerBySessionId(double updateCount, long sessionId) {

        sessionSpeakerRepo.updatePositionForAllSessionSpeakerBySessionId(updateCount,sessionId);
    }

    @Override
    public List<SessionSpeaker> findBySessionIdsBetween(Long id) {
        return sessionSpeakerRepo.findBySessionIdsBetween(id);
    }

    @Override
    public SessionSpeaker findFirstBySessionIdOrderByPositionDesc(Long sessionId) {
        return sessionSpeakerRepo.findFirstBySessionIdOrderByPositionDesc(sessionId);
    }

    @Override
    public List<SessionSpeaker> findAllSessionSpeakerBySpeakerIdAndSessionFormat(List<Long> speakerIds, List<EnumSessionFormat> sessionFormats) {
        return sessionSpeakerRepo.findAllSessionSpeakerBySpeakerIdAndSessionFormat(speakerIds,sessionFormats);
    }

    @Override
    public List<Session> findSessionBySpeakerUserId(Long eventId, Long userId, List<EnumSessionFormat> sessionFormats) {
        return sessionSpeakerRepo.findSessionBySpeakerUserId(eventId,userId,sessionFormats);
    }
    @Override
    public void deleteBySpeakerIds(List<Long> speakerId) {
        sessionSpeakerRepo.deleteBySpeakerIds(speakerId);
    }

    @Override
    public List<Long> getAllSessionIdsBySpeakerId(Long speakerId) {
        return sessionSpeakerRepo.findAllSessionIdsBySpeakerId(speakerId);
    }

    @Override
    public Page<Session> findSessionBySpeakerUserId(Long eventId, Long userId, String search, Pageable pageable, List<EnumSessionFormat> enumSessionFormats, EnumSessionStatus status) {
        return sessionSpeakerRepo.findSessionBySpeakerUserId(eventId,userId,search,pageable,enumSessionFormats, status);
    }

    @Override
    public List<SessionSpeaker> getAllSessionSpeakerBetweenSessionIds(Long from, Long to) {
        return sessionSpeakerRepo.getAllSessionSpeakerBetweenSessionIds(from,to);
    }

    @Override
    public SessionSpeaker findBySessionIdAndSpeakerIdAndEventId(Long sessionId, Long speakerId, Long eventId) {
        return sessionSpeakerRepo.findBySessionIdAndSpeakerIdAndEventId(sessionId,speakerId,eventId);
    }

    @Override
    public SessionSpeaker findBySessionIdAndSpeakerUserId(Long sessionId, Long speakerIdUserId) {
        return sessionSpeakerRepo.findBySessionIdAndSpeakerUserId(sessionId,speakerIdUserId);
    }

    @Override
    public Boolean isSpeakerModeratorInSession(Long sessionId, Long userId) {
        return sessionSpeakerRepo.isSpeakerModeratorInSession(sessionId,userId);
    }

    @Override
    public Long getSpeakerIdBySessionIdAndUserId(Long sessionId, Long userId){
         return sessionSpeakerRepo.getSpeakerIdBySessionIdAndUserId(sessionId,userId);
    }

    @Override
    public Page<Session> findSessionBySpeakerUserIdByPast(Long eventId, Long userId, String search, Date currentDate, Pageable pageable, List<EnumSessionFormat> enumSessionFormats, EnumSessionStatus status) {
        return sessionSpeakerRepo.findSessionBySpeakerUserIdByPast(eventId, userId, search, currentDate, pageable, enumSessionFormats, status);
    }

    @Override
    public List<SessionSpeaker> getSpeakerUserIdBySessionId(Long sessionId) {
        return sessionSpeakerRepo.getSpeakerUserIdBySessionId(sessionId);
    }

    @Transactional
    @Override
    public void deleteSessionSpeakerBySpeakerIds(List<Long> speakerIds) {
        sessionSpeakerRepo.deleteSessionSpeakerBySpeakerIds(speakerIds);
    }

    @Override
    public List<IdCountDto> countSessionSpeakerBySessionIdIn(List<Long> sessionIds) {
        return sessionSpeakerRepo.countSessionSpeakerBySessionIdIn(sessionIds);
    }

    @Override
    public List<Long> findSpeakerUserIdBySessionId(Long sessionId) {
        return sessionSpeakerRepo.findSpeakerUserIdBySessionId(sessionId);
    }

    @Override
    public List<SessionSpeaker> findByEventId(Long eventId) {
        return sessionSpeakerRepo.findByEventId(eventId);
    }

    @Override
    public List<SessionSpeakerIdDto> findSessionSpeakerIdByEventId(Long eventId) {
        return sessionSpeakerRepo.findSessionSpeakerIdByEventId(eventId);
    }


    @Override
    public List<SessionSpeaker> findByEventIdWithoutCache(Long eventId) {
        return sessionSpeakerRepo.findByEventIdWithoutCache(eventId);
    }

    @Override
    public boolean isSpeakerAvailable(Long speaker){
         return sessionSpeakerRepo.findIsSpeakerAvaibleBySpeakerId(speaker);
    }

    @Override
    public void updateSessionSpeakerBySpeakerId(Long speakerId,Long currentSpeakerId){
         sessionSpeakerRepo.updateSessionSpeakerBySpeakerId(speakerId,currentSpeakerId);
    }

    @Override
    public BigInteger isUserSpeakerInSessionByEventId(Long userId, Long eventId) {
        return sessionSpeakerRepo.isUserSpeakerInSessionByEventId(userId,eventId);
    }

    @Override
    public List<EventTaskEmailDto> findSpeakersEmailNameAndSessionNameByEventIdAndSessionIds(Long eventId, List<Long> sessionIds){
        return sessionSpeakerRepo.findSpeakersEmailNameAndSessionNameByEventIdAndSessionIds( eventId, sessionIds);
    }


    @Override
    public void deleteSessionSpeakerById(long id) {
        sessionSpeakerRepo.deleteSessionSpeakerById(id);
    }

    @Override
    public List<Long> findSessionByEventIdAndSpeakerUserId(Long eventId, Long userId) {
        return sessionSpeakerRepo.findSessionByEventIdAndSpeakerUserId(eventId, userId);
    }

    @Override
    public void deleteBySpeakerIdAndSessionIds(Long speakerId, List<Long> sessionIds) {
        sessionSpeakerRepo.deleteBySpeakerIdAndSessionIds(speakerId, sessionIds);
    }
}
