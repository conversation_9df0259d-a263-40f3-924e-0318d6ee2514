package com.accelevents.session_speakers.services;

import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.session_speakers.dto.IdDurationDto;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import com.accelevents.session_speakers.dto.NetworkingLoungeVideoDto;
import com.accelevents.session_speakers.dto.SessionEventLiveStream;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface MUXLivestreamAssetRepoService {
    Optional<MUXLivestreamAssetDetails> findBySessionIdAndAssetId(Long sessionId, String assetId);

    List<MUXLivestreamAssetDetails> findByAssetId(String assetId);

    void saveWithDefaultSequence(MUXLivestreamAssetDetails assetDetails);

    void save(MUXLivestreamAssetDetails assetDetails);

    void saveAll(List<MUXLivestreamAssetDetails> assetDetails);

    void updateAssetDuration(Long id, Double duration);

    void updateAssetFileNameForDownload(Long id, String fileName);

    List<MuxAssetDTO> findAllLivestreamAssetsForSessionBySessionIdAndType(Long sessionId, AssetType assetType);

    List<MUXLivestreamAssetDetails> findBySessionIdsIn(List<Long> sessionIds);

    void markAllAssetAsNonDefaultPlayBack(Long id, AssetType assetType);

    void markAssetAsDefaultPlayBack(Long id);

    void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status);

    MuxAssetDTO findDefaultPlayBackForSession(Long sessionId, AssetType assetType);

    Optional<MUXLivestreamAssetDetails> findFirstBySessionIdAndAssetTypeOrderByIdDesc(Long id, AssetType assetType);

    List<IdDurationDto> countBySessionIdIn(List<Long> sessionIds);

    Double getSessionVideoDurationBySessionId(Long sessionId);

    MUXLivestreamAssetDetails getByEventIdAndAssetIdAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(Long eventId, String assetId);

    List<MUXLivestreamAssetDetails> findAllByEventIdAndDefaultPlaybackTrueAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(Long eventId);

    void markAllAssetAsNonDefaultPlayBackByEventId(Long id);

    List<MuxAssetDTO> findAllLiveStreamAssetsForVirtualEventSetting(Long eventId);

    MUXLivestreamAssetDetails findById(Long muxLiveStreamAssetId);

    MUXLivestreamAssetDetails getByNetworkingLoungeIdAndAssetIdAndSessionIdNull(String loungeId, String assetId);

    List<MuxAssetDTO> findAllByNetworkingLoungeId(String loungeId);

    List<MUXLivestreamAssetDetails> findAllLiveStreamAssetsForVirtualEventSettingByEvent(Long eventId);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndPlaybackIdAndAssetType(Long sessionId, String playbackId, AssetType assetType);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndPlaybackIdAndAssetTypeAndS3AssetKeyIsNull(Long sessionId, String playbackId, AssetType assetType);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndDefaultPlaybackTrue(Long sessionId, AssetType assetType);

    Optional<MUXLivestreamAssetDetails> findBySessionIdAndDefaultPlaybackTrueAndAssetTypeAndS3AssetKeyIsNull(Long sessionId, AssetType assetType);

    String findAllByNetworkingLoungeIdAndUserId(String loungeId, Long userId);

    List<SessionEventLiveStream> findAllByAssetIdsIn(List<String> assetIds);

    List<MUXLivestreamAssetDetails> findAllBySessionIdIn(List<Long> blockedDirectUploadSessionsIds);

    void updateRecordStatusToPlayBackDeleted(List<String> assetIdsToMarkPlayBackRemoved, RecordStatus recordStatus);
    void updateRecordAndAssetStatusToMovedOrDelete(String muxAssetId);
    List<MUXLivestreamAssetDetails> findMuxPlayBackRemovedLiveStreamAssetsByEventId(Long eventId);

    void updatePlayBackUrlByAssetId(String assetId, String playBackId);

    Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndDefaultPlaybackTrue(Long eventStreamId);

    Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndPlaybackId(Long eventStreamId, String playbackId);

    void markAllAssetAsNonDefaultPlayBackByEventStreamId(Long id);

    Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndAssetId(Long eventStreamId, String assetId);

    Optional<MUXLivestreamAssetDetails> findFirstByEventStreamIdOrderByIdDesc(Long eventStreamId);

    Map<Long, List<MuxAssetDTO>> getMuxAssetsMapByAssetTypeAndSessionIds(List<Long> sessionIds, AssetType sessionAsset);

    List<String> getLivestreamAssetsDetailsBetweenDate();

    List<MUXLivestreamAssetDetails> getMuxAssetsByRecordStatusDeletedAndAssetIdFromAndTo(Long from, Long to);

    List<MUXLivestreamAssetDetails> findAllMUXLivestreamAssetDetailsByEventId(Long eventId);

    void updateMuxAssetStatus(List<String> assetIds, RecordStatus assetStatus);

    List<MUXLivestreamAssetDetails> findAllMuxAssetByDefaultPlaybackAndCreatedDateBeforeAndIdFromTo(boolean isDefaultPlayback, Date createdDate, Long from, Long to);

    List<MUXLivestreamAssetDetails> getAllMuxAssetByCreatedDateWithFiveAttendeeAndFromTo(Date createdDate, Long from, Long to);

    Page<Object[]> getAllMuxAssetByEventDeleteBeforeThirtyDays(Pageable pageable);

    List<MUXLivestreamAssetDetails> getAllWelcomeVideoByCreatedAtAndFromTo(Date createdDate, Long from, Long to);

    List<MUXLivestreamAssetDetails> findByEventIdAndPlayBackId(Long eventId, String playBackId);

    List<NetworkingLoungeVideoDto> findAllLoungeVideoByNetworkingLoungeId(String loungeId);
    List<MuxAssetDTO> getMuxAssetsByAssetTypeAndVisibleStatusAndSessionId(Long sessionId, AssetType assetType, boolean visible);

    String getDescriptionByAssetIdAndSessionId(String assetId,Long sessionId);
}
