package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.graphql.GraphQL;
import com.accelevents.utils.Constants;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CommandCenterGraphQLHandler extends GraphQL {

    private static final Logger log = LoggerFactory.getLogger(CommandCenterGraphQLHandler.class);

    public CommandCenterGraphQLHandler(GraphQLConfiguration graphQLConfiguration, String authToken) {
        super(graphQLConfiguration.getAppsyncCommandCenterApiUrl(), authToken);
    }


    public void createCommandCenterNotification(long eventId, long sessionId, String title, String action, String firstName, String lastName, String avatarUrl, Boolean isOnline, Long userId) {
        log.info("CommandCenterGraphQLHandler createCommandCenterNotification eventId {} sessionId  {} ", eventId, sessionId);
        JSONObject variable = new JSONObject();
        try {
            variable.put(Constants.CREATE_COMMAND_CENTER_NOTIFICATION_INPUT, getMainRequestData(eventId, sessionId, title, action, firstName, lastName, avatarUrl,isOnline, userId));

            String operationName = Constants.CREATE_COMMAND_CENTER_NOTIFICATION_NAME;
            JSONObject mainRequest = new JSONObject();
            mainRequest.put(Constants.OPERATION_NAME, operationName);
            mainRequest.put(Constants.VARIABLES, variable);
            mainRequest.put(Constants.QUERY, "mutation createCommandCenterNotification($createCommandCenterNotificationInput: CreateCommandCenterNotificationInput!) { createCommandCenterNotification(input: $createCommandCenterNotificationInput) { eventId action avatarUrl firstName lastName sessionId title isOnline userId date} }");
            execute(mainRequest, operationName);
        } catch (UnirestException | JSONException e) {
            log.info("Error while create command center notification with error {} ", e.getMessage());
        }
    }

    private JSONObject getMainRequestData(long eventId, long sessionId, String title, String action, String firstName, String lastName, String avatarUrl, Boolean isOnline, Long userId) {
        log.info("CommandCenterGraphQLHandler getMainRequestData eventId {} sessionId {} title {} action {} ", eventId, sessionId, title, action);
        JSONObject requestData = new JSONObject();
        try {
            if (StringUtils.isNotBlank(action)) {
                requestData.put("action", action);
            }
            if (StringUtils.isNotBlank(avatarUrl)) {
                requestData.put("avatarUrl", avatarUrl);
            }
            requestData.put("eventId", eventId);
            requestData.put("firstName", firstName);
            requestData.put("lastName", lastName);
            requestData.put("sessionId", sessionId);
            requestData.put("title", title);
            requestData.put("isOnline", isOnline);
            requestData.put("userId", userId);
        } catch (JSONException e) {
            log.info("Error while prepare request data for command center notification for eventId {} sessionId {} with error {} ", eventId,sessionId, e.getMessage());
        }
        return requestData;
    }

}
