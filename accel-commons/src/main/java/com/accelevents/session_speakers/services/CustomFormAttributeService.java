package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.AttributeType;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.EngageEmailMergeTagType;
import com.accelevents.domain.enums.RegistrationAttributeType;
import com.accelevents.domain.session_speakers.CustomFormAttribute;
import com.accelevents.domain.session_speakers.CustomFormAttributeData;
import com.accelevents.dto.KeyValueDto;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.ticketing.dto.CustomAttribute;

import java.util.List;

public interface CustomFormAttributeService {

    List<CustomFormAttribute> getAllCustomFormAttributesExcludingSubQueByRecurringEventId(Event event, Long recurringEventIde, AttributeType attributeType);

    List<CustomFormAttribute> getCustomFormAttributeOfEvent(Event event, User user, Long recurringEventId, AttributeType attributeType);

    Iterable<CustomFormAttribute> addDefaultAttributes(Event event, User user, Long recurringEventId, Long createdFromEventId, AttributeType attributeType);

    List<CustomFormAttributeDTO> getCustomFormAttributes(Event event, User user, Long recurringEventId, AttributeType attributeType);

    void createCustomFormAttribute(Event event, User user, Long recurringEventId, CustomFormCustomAttributeDto customFormCustomAttributeDto);

    void deleteCustomFormAttribute(long attributeId, Long recurringEventId, Event event);

    int findHighestAttributePosition(Event event,AttributeType attributeType);

    List<CustomAttribute> getCustomAttribute(Long attributeId, Event event, AttributeType attributeType);

    int getCustomAttributeUsageCount(Long attributeId, Event event, AttributeType attributeType);

    void updateCustomAttribute(Long attributeId, CustomFormCustomAttributeDto customFormCustomAttributeDto, Event event, Long recurringEventId);

    List<CustomFormAttributeMappingDto> getSessionRegistrationAttributeMappingList(Event event, Long recurringEventId, AttributeType attributeType);

    List<CustomFormAttribute> getSpeakerEnabledAttributesOrderByAttributeOrder(Event event, Long recurringEventId);

    List<KeyValueDto> getSpeakerAttributeByEventAndAttributeValueTypeList(Event event, List<AttributeValueType> attributeValueTypeList, RegistrationAttributeType registrationAttributeType );

    CustomFormAttributeData saveCustomAttributeData(CustomAttributeDetailsDto customAttributeDetailsDto, CustomFormAttributeData customFormAttributeData);

    CustomFormAttributeData saveCustomAttributeDataWithFormattedKeys(CustomAttributeDetailsDto customAttributeDetailsDto, CustomFormAttributeData customFormAttributeData, Event event, AttributeType attributeType);

    CustomAttributeDisplayDto getCustomAttributeDetails(Long customAttributeId, Event event, User user, AttributeType attributeType);

    CustomAttributesResponseDto getCustomAttributeResponseDto(CustomFormAttributeData customFormAttributeData);

    List<CustomFormAttributeData> getCustomFormAttributeDataByIds(List<Long> contactAttributeIds);

    List<CustomFormAttribute> getContactEnabledAttributesOrderByAttributeOrder(Event event, Long recurringEventId);

    List<CustomEngageEmailMergeTagDto> getContactListMergeTagsDto(Event event, User user, Long recurringEventId, EngageEmailMergeTagType mergeTagType);

}
