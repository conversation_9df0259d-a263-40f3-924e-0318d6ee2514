package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.BuyerAttributesMergeTagHelper;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.notification.services.impl.SendGridMailPrepareServiceImpl;
import com.accelevents.registration.approval.domain.RegistrationAttribute;
import com.accelevents.registration.approval.domain.RegistrationRequest;
import com.accelevents.registration.approval.domain.RegistrationRequestTicket;
import com.accelevents.registration.approval.dto.RegistrationResponseDto;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.registration.approval.repositories.RegistrationRequestTicketRepository;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.repositories.ConfirmationPagesRepository;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.ro.event.service.ROConfirmationEmailService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.services.ConfirmationEmailService;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.services.TicketingService;
import com.accelevents.services.repo.helper.EventDesignDetailRepoService;
import com.accelevents.session_speakers.services.ConfirmationPagesService;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.utils.*;
import com.cloudinary.utils.StringUtils;
import com.google.gson.Gson;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.EventFormat.HYBRID;
import static com.accelevents.domain.enums.EventFormat.IN_PERSON;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.getEventPath;

@Service
public class ConfirmationPagesServiceImpl implements ConfirmationPagesService {

    @Autowired
    private ConfirmationPagesRepository confirmationPagesRepository;

    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;

    @Autowired
    private EventDesignDetailRepoService eventDesignDetailRepoService;

    @Autowired
    private ServiceHelper serviceHelper;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private Configuration freemarkerMailConfiguration;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;
    @Autowired
    private ImageConfiguration imageConfiguration;

    @Autowired
    private SendGridMailPrepareServiceImpl sendGridMailPrepareService;

    @Autowired
    private RegistrationRequestsService registrationRequestsService;

    @Autowired
    private RegistrationRequestTicketRepository registrationRequestTicketRepository;

    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Autowired
    private ROVirtualEventService roVirtualEventService;

    @Autowired
    private RegistrationAttributeRepository registrationAttributeRepository;

    @Autowired
    private ConfirmationEmailService confirmationEmailService;
    @Autowired
    private ROConfirmationEmailService roConfirmationEmailService;

    @Value("${uiBaseurl}")
    private String uiBaseUrl;

    private static final Logger log = LoggerFactory.getLogger(ConfirmationPagesServiceImpl.class);

    @Override
    public ResponseDto createConfirmationPage(Event event, Long userId, CustomTemplatesDto customTemplatesDto, boolean isDefault) {
        CustomTemplates customTemplates =new CustomTemplates();
        if (isDefault){
            log.info("Default confirmation page creating for event: {} and created by: {}",event.getEventId(),userId);
            CustomTemplates templates=findByEventAndIsDefaultPage(event, true);
            if (templates!=null) {
                log.info("Default confirmation page already exists for event: {}",event.getEventId());
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DEFAULT_CONFIRMATION_PAGE_ALREADY_EXISTS);
            } else {
                createDefaultConfirmationPage(event, customTemplates);
                log.info("default confirmation page created for event: {} and created by: {}",event.getEventId(),userId);
            }
        } else {
            log.info("Custom confirmation page creating for event: {} and created by: {}",event.getEventId(),userId);
            createCustomConfirmationPage(event, customTemplatesDto, customTemplates);
        }
        log.info("Confirmation page created for event: {} and isDefault: {} and created by: {}",event.getEventId(),isDefault,userId);
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    private void createCustomConfirmationPage(Event event, CustomTemplatesDto customTemplatesDto, CustomTemplates customTemplates) {
        if(customTemplatesDto!=null) {
            checkIsConfirmationNameUnique(event, customTemplatesDto.getPageName().trim(),null,false);
            checkValidationForExternalPage(customTemplatesDto,null,false,event);
            validateApproval(customTemplatesDto, event);
            setPositionsForCustomTemplates(event,customTemplates,Arrays.asList(TemplateType.CONFIRMATION_PAGE));
            customTemplatesDto.setTemplateType(TemplateType.CONFIRMATION_PAGE);
            customTemplates = customTemplatesDto.toEntity(event, customTemplates);
            customTemplates.setCreatedAt(new Date());
            customTemplates.setDefaultPage(false);
            confirmationPagesRepository.save(customTemplates);
            updateCustomTemplateIdForEventTicketTypes(customTemplatesDto.getAllowedTicketTypes(),customTemplates,event);
            log.info("Custom confirmation page created for event: {} and pageName: {}",event.getEventId(),customTemplatesDto.getPageName());

        }
    }

    private void updateCustomTemplateIdForEventTicketTypes(List<Long> ticketTypesToBeAllowed, CustomTemplates customTemplates, Event event) {
        List<TicketingType> existingTicketingTypeList = ticketingTypeRepository.findAllTicketTypesByCustomTemplateId(event,customTemplates.getId());
        List<TicketingType> updatedTicketingTypeList = new ArrayList<>();

        updatedTicketingTypeList = getUpdatedTickets(ticketTypesToBeAllowed, event,updatedTicketingTypeList);

        // when create new confirmation page then added ticket types for the allowed ticket types
        if (existingTicketingTypeList.isEmpty() && !ticketTypesToBeAllowed.isEmpty()) {
            List<TicketingType> ticketingTypes = ticketingTypeRepository.findByIds(ticketTypesToBeAllowed);
            ticketingTypes.forEach(ticket -> ticket.setCustomTemplateId(customTemplates.getId()));
            updatedTicketingTypeList.addAll(ticketingTypes);
            log.info("Setting allowed ticket types for confirmation page for event: {} and number of ticketingTypes {}", customTemplates.getEvent().getEventId(), ticketingTypes.size());
        } else {

            // Identify excluded ticketing types and set custom template id to default template for excluded ticket types
            List<TicketingType> excludedTicketTypeList = existingTicketingTypeList.stream()
                    .filter(ticket -> !ticketTypesToBeAllowed.contains(ticket.getId()))
                    .collect(Collectors.toList());

            CustomTemplates defaultCustomTemplates = confirmationPagesRepository.findByEventAndIsDefaultPage(customTemplates.getEvent(), true);
            if (defaultCustomTemplates!=null) {
                excludedTicketTypeList.forEach(ticket -> ticket.setCustomTemplateId(defaultCustomTemplates.getId()));
                updatedTicketingTypeList.addAll(excludedTicketTypeList);
                log.info("excluded ticketing types and set custom template id {} to default template for excluded ticket types {} ", customTemplates.getId(), excludedTicketTypeList.size());
            }

            List<Long> newlyAddedTicketTypeIds = getNewlyAddedTicketTypeIds(ticketTypesToBeAllowed, existingTicketingTypeList);

            if (!newlyAddedTicketTypeIds.isEmpty()) {
                List<TicketingType> newlyAddedTicketTypeList = ticketingTypeRepository.findByIds(newlyAddedTicketTypeIds);
                newlyAddedTicketTypeList.forEach(ticket -> ticket.setCustomTemplateId(customTemplates.getId()));
                updatedTicketingTypeList.addAll(newlyAddedTicketTypeList);
            }
            log.info("Setting newly added ticket types for confirmation page {} and number of ticketingTypes {}", customTemplates.getId(), newlyAddedTicketTypeIds.size());
        }

        // Save all updated ticketing types
        ticketingTypeRepository.saveAll(updatedTicketingTypeList);
        //get updated ticket types ids list from the existing ticketing types
        List<Long> updatedTicketingTypeListIds = updatedTicketingTypeList.stream().map(TicketingType::getId).collect(Collectors.toList());
        log.info("Setting allowed ticket types for confirmation page {} and updatedTicketingTypeListIds {}", customTemplates.getId(), updatedTicketingTypeListIds);

    }

    public List<TicketingType> getUpdatedTickets(List<Long> ticketTypesToBeAllowed , Event event, List<TicketingType> updatedTicketingTypeList) {
        Ticketing ticketing = ticketingService.findByEvent(event);
        // added ticket types for the recurring event
        if (ticketing.isRecurringEvent() && !ticketTypesToBeAllowed.isEmpty()) {
            List<TicketingType> ticketTypesBelongToRecurring = ticketingTypeRepository.findByCreatedFrom(ticketTypesToBeAllowed);
            log.info("ticketTypesBelongToRecurring for event: {} and number of ticketingTypes {}", event.getEventId(), ticketTypesBelongToRecurring.size());
            ticketTypesToBeAllowed.addAll(ticketTypesBelongToRecurring.stream().map(TicketingType::getId).collect(Collectors.toList()));
        }
        return updatedTicketingTypeList;
    }

    public List<Long> getNewlyAddedTicketTypeIds(List<Long> ticketTypesToBeAllowed, List<TicketingType> existingTicketingTypeList) {
        // get existing ticket types ids list from the existing ticketing types
        List<Long> existingTicketTypeIds = existingTicketingTypeList.stream()
                .map(TicketingType::getId)
                .collect(Collectors.toList());

        // Fetch newly added ticket types and set custom template id
        List<Long> newlyAddedTicketTypeIds = ticketTypesToBeAllowed.stream()
                .filter(ticketTypeId -> !existingTicketTypeIds.contains(ticketTypeId))
                .collect(Collectors.toList());
        return newlyAddedTicketTypeIds;
    }



    private void createDefaultConfirmationPage(Event event, CustomTemplates customTemplates) {
        CustomTemplatesDto customTemplatesDto =new CustomTemplatesDto();
        log.info("default confirmation page creating for event: {} ",event.getEventId());
        customTemplatesDto.setPageType(PageType.INTERNAL);
        customTemplatesDto.setPageName(DEFAULT_ORDER_CONFIRMATION_PAGE);
        customTemplatesDto.setCreatedAt(new Date());
        setPositionsForCustomTemplates(event, customTemplates,Arrays.asList(TemplateType.CONFIRMATION_PAGE));
        customTemplatesDto.setTemplateType(TemplateType.CONFIRMATION_PAGE);
        customTemplates = customTemplatesDto.toEntity(event, customTemplates);
        customTemplates.setDefaultPage(true);
        customTemplates.setCalendarInvitation(roConfirmationEmailService.getCalenderInvitation(event));
        confirmationPagesRepository.save(customTemplates);
        setAllTicketTypesForDefaultConfirmationPage(event, customTemplates);
    }

    public void setPositionsForCustomTemplates(Event event,CustomTemplates customTemplates,List<TemplateType> templateTypes) {
        double sequence = 1000;
        CustomTemplates lastItem=confirmationPagesRepository.findFirstByEventAndOrderByPositionDesc(event,templateTypes);
        if (lastItem!=null){
            customTemplates.setPosition(lastItem.getPosition()+sequence);
        }else {
            customTemplates.setPosition(sequence);
        }
    }

    private void setAllTicketTypesForDefaultConfirmationPage(Event event, CustomTemplates customTemplates) {
        List<TicketingType> ticketingTypes=ticketingTypeRepository.findAllTicketTypeByEventAndDataType(event, DataType.TICKET);
        for (TicketingType ticketingType:ticketingTypes) {
            ticketingType.setCustomTemplateId(customTemplates.getId());
        }
        ticketingTypeRepository.saveAll(ticketingTypes);
        log.info("Setting all ticket types for default confirmation page for event: {} and number of ticketingTypes {}",event.getEventId(),ticketingTypes.size());
    }

    private void checkValidationForExternalPage(CustomTemplatesDto customTemplatesDto, Long customPageId,boolean isPageUpdate,Event event) {
        if (PageType.EXTERNAL.equals(customTemplatesDto.getPageType())) {
            if(!customTemplatesDto.getPageUrl().isEmpty()){
                log.info("External confirmation page url validation for pageUrl: {}",customTemplatesDto.getPageUrl());
                isValidConfirmationPageURL(customTemplatesDto.getPageUrl().trim());
                isPageURLNotAttachedWithAnyOtherPage(customTemplatesDto.getPageUrl().trim(),customPageId,isPageUpdate,event);
            }else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CONFIRMATION_PAGE_URL_REQUIRED);
            }
        }
    }

    private void isPageURLNotAttachedWithAnyOtherPage(String pageUrl, Long customPageId, boolean isPageUpdate,Event event) {
        CustomTemplates customTemplates = confirmationPagesRepository.findByPageUrlAndEvent(pageUrl,event);
        if (customTemplates != null && (!isPageUpdate || !customTemplates.getId().equals(customPageId))) {
            log.info("Confirmation page url already exists for pageUrl: {}",pageUrl);
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CONFIRMATION_PAGE_URL_ALREADY_EXISTS);
        }
    }

    public boolean isValidConfirmationPageURL(String urlStr) {
        // Regular expression for URL format validation
        String regex = Constants.URL_VALIDATION;

        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(urlStr).matches();
    }

    private void checkIsConfirmationNameUnique(Event event, String pageName, Long confirmationPageId, boolean isPageUpdate) {
        if (!StringUtils.isBlank(pageName)) {
            CustomTemplates customTemplates = confirmationPagesRepository.findByEventAndPageName(event.getEventId(), pageName);
            if (customTemplates != null) {
                if (!isPageUpdate || !confirmationPageId.equals(customTemplates.getId())) {
                    log.info("Confirmation page name already exists for event: {} and pageName: {}", event.getEventId(), pageName);
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CONFIRMATION_PAGE_NAME_ALREADY_EXISTS);
                }
            }
        }
    }



    private void generateDefaultConfirmationPageHtmlAndJson(CustomTemplatesDto customTemplatesDto) {
        String defaultHtml = getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_ORDER_CONFIRMATION_PAGE_HTML));
        String defaultJson = getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_ORDER_CONFIRMATION_PAGE_JSON));
        customTemplatesDto.setBeeFreeHtml(defaultHtml);
        customTemplatesDto.setBeeFreeJson(defaultJson);

    }

    public String getHtmlAndJsonFromPath(String path) {
        String defaultHtmlJson = null;
        try {
            defaultHtmlJson = FileReaderUtilsClass.getBeeFreeTemplate(path);
        } catch (IOException ioException) {
            log.error("Error while reading default confirmation page html and json",ioException);
        }
        return defaultHtmlJson;
    }

    @Override
    public DataTableResponse getAllConfirmationPages(Event event,PageSizeSearchObj pageSizeSearchObj) {
        Page<CustomTemplateDetailsDto> confirmationPageDetailsDtos=confirmationPagesRepository.getConfirmationPagesByEvent(event,pageSizeSearchObj.getPageable(), pageSizeSearchObj.getSearch());
        Ticketing ticketing=ticketingService.findByEvent(event);
        DataTableResponse dataTableResponse = new DataTableResponse();
        List<CustomTemplateDetailsDto> customTemplateDetailsDtoList=confirmationPageDetailsDtos.getContent();
        for (CustomTemplateDetailsDto customTemplateDetailsDto:customTemplateDetailsDtoList) {
            List<TicketingType> ticketingTypeList=ticketingTypeRepository.findAllTicketTypeByCustomTemplateId(customTemplateDetailsDto.getId());
            List<Long> allowedTicketTypes= setAllowedTicketTypeForCustomTemplatesPage(ticketing, ticketingTypeList);
            customTemplateDetailsDto.setAllowedTicketTypes(allowedTicketTypes);
        }
        dataTableResponse.setRecordsTotal(confirmationPageDetailsDtos.getTotalElements());
        dataTableResponse.setRecordsFiltered(confirmationPageDetailsDtos.getNumberOfElements());
        dataTableResponse.setData(confirmationPageDetailsDtos.getContent());
        return dataTableResponse;
    }

    @Override
    public ResponseDto updateConfirmationPages(Event event, CustomTemplatesDto customTemplatesDto, Long confirmationPageId, User user) {
        log.info("Confirmation page updating confirmationPageId:{} for event: {} and updated by: {}",confirmationPageId,event.getEventId(),user.getUserId());
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findById(confirmationPageId);
        if (customTemplatesOptional.isPresent() && customTemplatesDto!=null) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            checkIsConfirmationNameUnique(event, customTemplatesDto.getPageName().trim(),confirmationPageId,true);
            checkValidationForExternalPage(customTemplatesDto,confirmationPageId,true,event);
            validateApproval(customTemplatesDto, event);
            customTemplates = customTemplatesDto.toEntity(event,customTemplates);
            customTemplates.setUpdatedAt(new Date());
            customTemplates.setPageUpdated(true);
            // if user save the page for the actual use then we update the html value and unPublishedHtml value as same
            customTemplates.setUnPublishedHtml(customTemplatesDto.getBeeFreeHtml());
            confirmationPagesRepository.save(customTemplates);
            updateCustomTemplateIdForEventTicketTypes(customTemplatesDto.getAllowedTicketTypes(),customTemplates,event);
            log.info("Confirmation page updated confirmationPageId:{} for event: {} and updated by: {}",confirmationPageId,event.getEventId(),user.getUserId());
            return new ResponseDto(SUCCESS, SUCCESS);
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CONFIRMATION_PAGE_NOT_FOUND);
        }
    }

    private void validateApproval(CustomTemplatesDto customTemplatesDto, Event event) {
        if(customTemplatesDto.getConfirmationApprovalType() != null){
            ConfirmationApprovalType confirmationApprovalType = customTemplatesDto.getConfirmationApprovalType();
            VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
            if(confirmationApprovalType.equals(ConfirmationApprovalType.SPEAKER) && virtualEventSettings != null && !virtualEventSettings.isSpeakerRegistrationApproval()){
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.SPEAKER_REGISTRATION_APPROVAL_NOT_ENABLED);
            }
            else if(confirmationApprovalType.equals(ConfirmationApprovalType.EXPO) && virtualEventSettings != null && !virtualEventSettings.isExhibitorRegistrationApproval()){
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.EXPO_REGISTRATION_APPROVAL_NOT_ENABLED);
            }
        }
    }

    @Override
    public CustomTemplatesDto getConfirmationPage(Event event, Long confirmationPageId) {
        Ticketing ticketing=ticketingService.findByEvent(event);
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findById(confirmationPageId);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            CustomTemplatesDto customTemplatesDto=CustomTemplatesDto.toDto(customTemplates);
            if(customTemplatesDto.getBeeFreeHtml() == null || customTemplatesDto.getBeeFreeJson() == null){
                generateDefaultConfirmationPageHtmlAndJson(customTemplatesDto);
            }
            List<TicketingType> ticketingTypeList=ticketingTypeRepository.findAllTicketTypeByCustomTemplateId(customTemplates.getId());
            List<Long> allowTicketTypes= setAllowedTicketTypeForCustomTemplatesPage(ticketing, ticketingTypeList);
            customTemplatesDto.setAllowedTicketTypes(allowTicketTypes);

            // replace the event Logo widget related merge tags with the actual event logo for Host side Page builder
            replaceMergeTagsForHost(customTemplatesDto, event);
            return customTemplatesDto;
        } else {
            throw new NotFoundException(NotFoundException.NotFound.CONFIRMATION_PAGE_NOT_FOUND);
        }
    }

    public List<Long> setAllowedTicketTypeForCustomTemplatesPage(Ticketing ticketing, List<TicketingType> ticketingTypeList) {
        List<Long> allowedTicketTypes=new ArrayList<>();
        for (TicketingType ticketingType : ticketingTypeList) {
            if (ticketing.isRecurringEvent()) {
                // added ticket types for the recurring event
                if (ticketingType.getRecurringEventId() == null || ticketingType.getCreatedFrom().equals(-1L)) {
                    allowedTicketTypes.add(ticketingType.getId());
                }
            } else {
                allowedTicketTypes.add(ticketingType.getId());
            }
        }
        return allowedTicketTypes;
    }

    @Override
    public ResponseDto deleteConfirmationPages(Event event, Long confirmationPageId,User user) {
        log.info("Confirmation page deleting confirmationPageId:{} for event: {} and deletedBy: {}",confirmationPageId,event.getEventId(),user.getUserId());
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findById(confirmationPageId);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            if (customTemplates.isDefaultPage()) {
                log.info("Default confirmation page cannot be deleted confirmationPageId:{} for event: {} and deletedBy: {}",confirmationPageId,event.getEventId(),user.getUserId());
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DEFAULT_CONFIRMATION_PAGE_CANNOT_BE_DELETED);
            }
            List<TicketingType> attachedTicketingTypeList = ticketingTypeRepository.findAllTicketTypesByCustomTemplateId(event,customTemplates.getId());
            if (!attachedTicketingTypeList.isEmpty()) {
                attachedTicketMoveToTheDefaultConfirmationPage(event,attachedTicketingTypeList);
            }
            customTemplates.setRecStatus(RecordStatus.DELETE);
            confirmationPagesRepository.save(customTemplates);
            log.info("Confirmation page deleted confirmationPageId:{} for event: {} and deletedBy: {}",confirmationPageId,event.getEventId(),user.getUserId());
            return new ResponseDto(SUCCESS, SUCCESS);
        } else {
            throw new NotFoundException(NotFoundException.NotFound.CONFIRMATION_PAGE_NOT_FOUND);
        }
    }

    private void attachedTicketMoveToTheDefaultConfirmationPage(Event event, List<TicketingType> attchedTicketingTypeList) {
        CustomTemplates defaultCustomTemplates = confirmationPagesRepository.findByEventAndIsDefaultPage(event, true);
        attchedTicketingTypeList.forEach(ticket -> ticket.setCustomTemplateId(defaultCustomTemplates.getId()));
        ticketingTypeRepository.saveAll(attchedTicketingTypeList);
        log.info("Attached ticketing types moved to default confirmation page for event: {} and number of ticketingTypes {}",event.getEventId(),attchedTicketingTypeList.size());
    }

    @Override
    public ResponseDto resetDefaultConfirmationPage(Event event, User user,Long confirmationPageId) {
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findById(confirmationPageId);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            customTemplates.setHtmlValue(null);
            customTemplates.setJsonValue(null);
            customTemplates.setPageUpdated(false);
            confirmationPagesRepository.save(customTemplates);
            log.info("Confirmation page reset confirmationPageId:{} for event: {} and reset by: {}",confirmationPageId,event.getEventId(),user.getUserId());
            return new ResponseDto(SUCCESS, SUCCESS);
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CONFIRMATION_PAGE_NOT_FOUND);
        }
    }

    @Override
    public CustomTemplates findByEventAndIsDefaultPage(Event event, boolean isDefaultPage) {
        return confirmationPagesRepository.findByEventAndIsDefaultPage(event, isDefaultPage);
    }

    @Override
    public ResponseDto duplicateConfirmationPage(Event event, Long confirmationPageId,String pageName, List<Long> allowedTicketTypes,User user,String pageUrl) {
        Optional<CustomTemplates> customTemplatesOptional = confirmationPagesRepository.findById(confirmationPageId);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customConfirmationAndReminderTemplate = customTemplatesOptional.get();
            CustomTemplates duplicateCustomTemplates =null;
            duplicateCustomTemplates =(CustomTemplates) customConfirmationAndReminderTemplate.clone();
            duplicateCustomTemplates.setPageName(pageName);
            duplicateCustomTemplates.setId(null);
            duplicateCustomTemplates.setDefaultPage(false);
            duplicateCustomTemplates.setPageUpdated(false);
            if (PageType.EXTERNAL.equals(duplicateCustomTemplates.getPageType()) && !StringUtils.isBlank(pageUrl)){
                duplicateCustomTemplates.setPageUrl(pageUrl);
            }
            setPositionsForDuplicateCustomTemplatesPage(duplicateCustomTemplates, event,Arrays.asList(TemplateType.CONFIRMATION_PAGE));
            confirmationPagesRepository.save(duplicateCustomTemplates);
            setTicketTypesForDuplicateConfirmationPage(allowedTicketTypes, duplicateCustomTemplates);
            log.info("Confirmation page duplicated from confirmationPageId:{} for event: {} and duplicated by:{} ",confirmationPageId,event.getEventId(),user.getUserId());
            return new ResponseDto(SUCCESS, SUCCESS);
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CONFIRMATION_PAGE_NOT_FOUND);
        }
    }

    public void setPositionsForDuplicateCustomTemplatesPage(CustomTemplates duplicateCustomTemplatesPage, Event event,List<TemplateType> templateTypes) {
        double sequence = 1000;
        CustomTemplates lastItem=confirmationPagesRepository.findFirstByEventAndOrderByPositionDesc(event,templateTypes);
        if (lastItem!=null){
            duplicateCustomTemplatesPage.setPosition(lastItem.getPosition()+sequence);
        }else {
            duplicateCustomTemplatesPage.setPosition(sequence);
        }
    }

    public void setTicketTypesForDuplicateConfirmationPage(List<Long> allowedTicketTypes, CustomTemplates duplicateCustomTemplates) {
        if (!allowedTicketTypes.isEmpty() && duplicateCustomTemplates!=null){
            List<TicketingType> ticketingTypes=ticketingTypeRepository.findByIds(allowedTicketTypes);
            ticketingTypes.forEach(ticketingType -> {
                ticketingType.setCustomTemplateId(duplicateCustomTemplates.getId());
            });
            ticketingTypeRepository.saveAll(ticketingTypes);
            log.info("Setting allowed ticket types for duplicate confirmation page for event: {} and number of ticketingTypes {}",duplicateCustomTemplates.getEvent().getEventId(),ticketingTypes.size());
        }
    }

    @Override
    public List<CustomTemplateDetailsDto> getAllConfirmationPagesList(Event event) {
        List<CustomTemplates> customTemplatesList= confirmationPagesRepository.findByEvent(event);
        List<CustomTemplateDetailsDto> customTemplateDetailsDtoList = new ArrayList<>();
        for (CustomTemplates customTemplates:customTemplatesList) {
            CustomTemplateDetailsDto customTemplateDetailsDto = new CustomTemplateDetailsDto();
            customTemplateDetailsDto.setId(customTemplates.getId());
            customTemplateDetailsDto.setPageName(customTemplates.getPageName());
            customTemplateDetailsDto.setDefaultPage(customTemplates.isDefaultPage());
            customTemplateDetailsDtoList.add(customTemplateDetailsDto);
        }
        return customTemplateDetailsDtoList;
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public ResponseDto updatePosition(Event event, Long confirmationPageId, Long topPageId, Long bottomPageId,TemplateType templateType) {
        double sequence = 1000;
        Optional<CustomTemplates> topConfirmationPage=confirmationPagesRepository.findById(topPageId);
        Optional<CustomTemplates> bottomConfirmationPage=confirmationPagesRepository.findById(bottomPageId);
        Optional<CustomTemplates> confirmationPage=confirmationPagesRepository.findById(confirmationPageId);
        //move to middle
        if (topConfirmationPage.isPresent() && bottomConfirmationPage.isPresent() && confirmationPage.isPresent()) {
            CustomTemplates topCustomTemplates = topConfirmationPage.get();
            CustomTemplates bottomCustomTemplates = bottomConfirmationPage.get();
            CustomTemplates customTemplates = confirmationPage.get();
            moveCustomTemplateToMiddle(event, topCustomTemplates, bottomCustomTemplates, customTemplates,templateType);
        } //move to top
        else if(topConfirmationPage.isEmpty() && bottomConfirmationPage.isPresent() && confirmationPage.isPresent()){
            CustomTemplates bottomCustomTemplates = bottomConfirmationPage.get();
            CustomTemplates customTemplates = confirmationPage.get();
            double position = bottomCustomTemplates.getPosition() + sequence;
            customTemplates.setPosition(position);
            confirmationPagesRepository.save(customTemplates);
        } //move to bottom
        else if(topConfirmationPage.isPresent() && bottomConfirmationPage.isEmpty() && confirmationPage.isPresent()) {
            CustomTemplates topCustomTemplates = topConfirmationPage.get();
            CustomTemplates customTemplates = confirmationPage.get();
            double positionDifference = topCustomTemplates.getPosition() - sequence;
            if (positionDifference <= 1) {
                confirmationPagesRepository.updatePositionForAllCustomTemplates(event.getEventId(), sequence,templateType.toString());
            }
            customTemplates.setPosition(sequence);
            confirmationPagesRepository.save(customTemplates);
        }
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @Override
    public String getDuplicatePageNameByCustomPageId(Event event, Long pageId, User user,TemplateType templateType) {
        Optional<CustomTemplates> customTemplatesOptional=confirmationPagesRepository.findById(pageId);
        if (customTemplatesOptional.isPresent()) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            return getCopyPageName(customTemplates.getPageName().trim(),event,0,templateType);
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CONFIRMATION_PAGE_NOT_FOUND);
        }
    }

    @Override
    public ResponseDto updateUnpublishedHtml(Event event, Long confirmationPageId, BeeFreePageDto beeFreePageDto) {
        Optional<CustomTemplates> customTemplatesOptional=confirmationPagesRepository.findById(confirmationPageId);
        if (customTemplatesOptional.isPresent() && beeFreePageDto!=null) {
            CustomTemplates customTemplates = customTemplatesOptional.get();
            customTemplates.setUnPublishedHtml(beeFreePageDto.getBeeFreeHtml());
            confirmationPagesRepository.save(customTemplates);
            log.info("Confirmation page unpublished html updated confirmationPageId:{} for event: {}",confirmationPageId,event.getEventId());
        }
        return new ResponseDto(SUCCESS, SUCCESS);
    }

    @Override
    public CustomTemplatesDto getConfirmationPagePreviewByPageId(Event event, Long confirmationPageId) {
        CustomTemplatesDto customTemplatesDto = confirmationPagesRepository.getConfirmationPageByPageIdAndEventId(confirmationPageId, event.getEventId());
        if(customTemplatesDto == null) {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_PAGE_NOT_FOUND);
        }
        if(customTemplatesDto.getBeeFreeHtml() == null){
            String defaultHtml = getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_ORDER_CONFIRMATION_PAGE_HTML));
            customTemplatesDto.setBeeFreeHtml(defaultHtml);
        }
        customTemplatesDto.setBeeFreeHtml(org.apache.commons.lang3.StringUtils.isBlank(customTemplatesDto.getUnPublishedHtml()) ? customTemplatesDto.getBeeFreeHtml() : customTemplatesDto.getUnPublishedHtml());
        return replaceMergeTagsForConfirmationPagePreview(customTemplatesDto, event);
    }

    @Override
    public CustomTemplatesDto getConfirmationPageByOrder(Event event, Long orderId) {
        List<EventTickets> eventTicketsList=eventTicketsRepository.findEventTicketsByOrderId(orderId);
        List<TicketingType> ticketingTypeList=eventTicketsList.stream().map(EventTickets::getTicketingTypeId).collect(Collectors.toList());
        List<Long> customTemplateIds=ticketingTypeList.stream().map(TicketingType::getCustomTemplateId).collect(Collectors.toList());
        List<CustomTemplates> customTemplatesList = confirmationPagesRepository.findByEventAndCustomTemplatesIdIn(event.getEventId(), customTemplateIds);
        CustomTemplatesDto customTemplatesDto=new CustomTemplatesDto();
        // find max position custom template for the order
        findMaxPositionCustomTemplate(customTemplatesList, customTemplatesDto, event,eventTicketsList, null);
        return customTemplatesDto;
    }

    @Override
    public List<TicketTypeDto> getTicketTypesForConfirmationPage(Event event) {
        Ticketing ticketing=ticketingService.findByEvent(event);
        List<TicketingType> filteredTicketingTypes = new ArrayList<>();
        List<TicketingType> ticketingTypeList=ticketingTypeRepository.findAllByEventAndDataType(event,DataType.TICKET);
        if (ticketing.isRecurringEvent()) {
            for (TicketingType ticketingType : ticketingTypeList) {
                // Added the parent ticket to the list for the recurring event, with its recurring event ID set to null, and added the child ticket for the specific date within the recurring event.
                if (ticketingType.getRecurringEventId() == null || ticketingType.getCreatedFrom().equals(-1L)) {
                    filteredTicketingTypes.add(ticketingType);
                }
            }
        } else {
            filteredTicketingTypes.addAll(ticketingTypeList);
        }
        return getTicketTypeDtoList(filteredTicketingTypes);
    }

    public List<TicketTypeDto> getTicketTypeDtoList(List<TicketingType> filteredTicketingTypes) {
        List<TicketTypeDto> ticketTypeDtoList = new ArrayList<>();
        for (TicketingType ticketingType : filteredTicketingTypes) {
            TicketTypeDto ticketTypeDto = new TicketTypeDto();
            ticketTypeDto.setTypeId(ticketingType.getId());
            ticketTypeDto.setName(ticketingType.getTicketTypeName());
            ticketTypeDto.setPayLater(ticketingType.isPayLater());
            ticketTypeDtoList.add(ticketTypeDto);
        }
        return ticketTypeDtoList;
    }

    private void findMaxPositionCustomTemplate(List<CustomTemplates> customTemplatesList, CustomTemplatesDto customTemplatesDto, Event event,List<EventTickets> eventTicketsList, RegistrationRequest registrationRequest) {
        if(!customTemplatesList.isEmpty()) {
            CustomTemplates maxPositionCustomTemplate = customTemplatesList.stream().max(Comparator.comparingDouble(CustomTemplates::getPosition)).orElseThrow();
            if (PageType.INTERNAL.equals(maxPositionCustomTemplate.getPageType())) {
                String htmlValue = maxPositionCustomTemplate.getHtmlValue();
                if(htmlValue == null){
                     htmlValue = getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_ORDER_CONFIRMATION_PAGE_HTML));
                }
                EventDesignDetail eventDesignDetail = eventDesignDetailRepoService.findByEvent(event);
                htmlValue = replaceEventLogoAndEventSrcUrlInHTML(htmlValue, event, eventDesignDetail);
                String finalHtmlString = updateMergesValueForConfirmationPage(customTemplatesDto.getId(), customTemplatesDto.getPageName(), htmlValue, event, eventDesignDetail,eventTicketsList, registrationRequest, maxPositionCustomTemplate);
                customTemplatesDto.setBeeFreeHtml(finalHtmlString);
                log.info("internal Confirmation page getting by orderId:{} for event: {}",customTemplatesDto.getId(),event.getEventId());
            } else {
                customTemplatesDto.setPageUrl(maxPositionCustomTemplate.getPageUrl());
                log.info("external Confirmation page getting by orderId:{} for event: {}",customTemplatesDto.getId(),event.getEventId());
            }
            customTemplatesDto.setId(maxPositionCustomTemplate.getId());
            customTemplatesDto.setPageType(maxPositionCustomTemplate.getPageType());
            customTemplatesDto.setDefaultPage(maxPositionCustomTemplate.isDefaultPage());
            customTemplatesDto.setConfirmationApprovalType(maxPositionCustomTemplate.getConfirmationApprovalType());
        } else {
            // if no custom template found for the add-ons order then get the default confirmation page
            CustomTemplates customTemplates=confirmationPagesRepository.findByEventAndIsDefaultPage(event, true);
            if (customTemplates!=null) {
                customTemplatesDto.setId(customTemplates.getId());
                customTemplatesDto.setPageType(customTemplates.getPageType());
                String htmlValue = customTemplates.getHtmlValue();
                if(htmlValue == null){
                     htmlValue = getHtmlAndJsonFromPath(TEMPLATES_SLASH.concat(PATH_OF_DEFAULT_ORDER_CONFIRMATION_PAGE_HTML));
                }
                EventDesignDetail eventDesignDetail = eventDesignDetailRepoService.findByEvent(event);
                htmlValue = replaceEventLogoAndEventSrcUrlInHTML(htmlValue, event, eventDesignDetail);
                String finalHtmlString = updateMergesValueForConfirmationPage(customTemplatesDto.getId(), customTemplatesDto.getPageName(), htmlValue, event, eventDesignDetail,eventTicketsList, registrationRequest, customTemplates);
                customTemplatesDto.setBeeFreeHtml(finalHtmlString);
                log.info("Default Confirmation page getting by orderId:{} for event: {}",customTemplatesDto.getId(),event.getEventId());
            }
        }
    }

    public CustomTemplatesDto replaceMergeTagsForHost(CustomTemplatesDto customTemplatesDto, Event event) {
        String htmlValue = customTemplatesDto.getBeeFreeHtml();
        String jsonValue = customTemplatesDto.getBeeFreeJson();
        if (htmlValue != null && jsonValue!=null) {
            EventDesignDetail eventDesignDetail = eventDesignDetailRepoService.findByEvent(event);
            htmlValue = replaceEventLogoAndEventSrcUrlInHTML(htmlValue, event, eventDesignDetail);
            jsonValue = replaceEventLogoAndEventSrcUrlInJSON(jsonValue, event, eventDesignDetail);
            customTemplatesDto.setBeeFreeHtml(htmlValue);
            customTemplatesDto.setBeeFreeJson(jsonValue);
        }
        return customTemplatesDto;
    }

    private CustomTemplatesDto replaceMergeTagsForConfirmationPagePreview(CustomTemplatesDto customTemplatesDto, Event event) {
        String htmlValue = customTemplatesDto.getBeeFreeHtml();
        if (htmlValue == null) {
            log.info("replaceMergeTagsForDisplay found html value is null for eventId {} pageId {}", event.getEventId(), customTemplatesDto.getId());
            return customTemplatesDto;
        }
        EventDesignDetail eventDesignDetail = eventDesignDetailRepoService.findByEvent(event);
        htmlValue = replaceEventLogoAndEventSrcUrlInHTML(htmlValue, event, eventDesignDetail);
        String finalHtmlString = updateMergesValueForConfirmationPage(customTemplatesDto.getId(), customTemplatesDto.getPageName(), htmlValue, event, eventDesignDetail,null, null, null);
        customTemplatesDto.setBeeFreeHtml(finalHtmlString);
        return customTemplatesDto;
    }

    private String updateMergesValueForConfirmationPage(Long pageId, String pageName, String htmlValue, Event event, EventDesignDetail eventDesignDetail,List<EventTickets> eventTicketsList, RegistrationRequest registrationRequest, CustomTemplates customTemplates) {
        String templateString = htmlValue;
        try {
            String eventDomain = serviceHelper.getEventBaseUrl(event);
            Map<String, Object> substitutionMap = new HashMap<>();
            if(registrationRequest == null){
                sendGridMailPrepareService.updateMergeTagsForBuyerAttributes(htmlValue,substitutionMap,eventTicketsList, null, event.getEventId(), null,null,null,false,null);
            }
            else{
                RegistrationAttributeType registrationAttributeType = ConfirmationApprovalType.SPEAKER.equals(customTemplates.getConfirmationApprovalType())?RegistrationAttributeType.SPEAKER_INFO:RegistrationAttributeType.EXPO_INFO;

                List<RegistrationAttribute> registrationAttributes = registrationAttributeRepository.findByEventAndTypeAndRecurringEventIdNull(event, registrationAttributeType);
                RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
                sendGridMailPrepareService.updateMergeTagsForBuyerAttributes(htmlValue,substitutionMap,null, registrationAttributes, event.getEventId(), registrationResponseDto, customTemplates.getConfirmationApprovalType(),null,false,null);
            }
            substitutionMap.put(Constants.EVENT_URL, eventDomain.concat(getEventPath()).concat(event.getEventURL()));
            substitutionMap.put(Constants.EVENT_UC_NAME, event.getName());
            substitutionMap.put(Constants.EVENT_TIME_ZONE, java.util.TimeZone.getTimeZone(event.getEquivalentTimeZone()).getDisplayName(false, java.util.TimeZone.SHORT));
            substitutionMap.put(EVENT_DOMAIN, eventDomain);
            substitutionMap.put(ADD_TO_CALENDAR_BUTTON, String.format(DOLLAR_OPEN_CLOSE_CURLY_BRACES, ADD_TO_CALENDAR_BUTTON));

            String eventTagLine = STRING_EMPTY;
            String eventDescription = STRING_EMPTY;
            if (eventDesignDetail != null) {
                eventTagLine = DEFAULT_EVENT_TAGE_LINE.equalsIgnoreCase(eventDesignDetail.getEventTagLine()) ? STRING_EMPTY : eventDesignDetail.getEventTagLine();
                eventDescription= org.apache.commons.lang3.StringUtils.isBlank(eventDesignDetail.getDesc()) ? STRING_EMPTY : eventDesignDetail.getDesc();

            }
            substitutionMap.put(EVENT_SLOGAN, eventTagLine);
            substitutionMap.put(EVENT_DESCRIPTION, eventDescription);
            Ticketing ticketing = ticketingService.findByEvent(event);
            if (ticketing != null) {
                replaceEventLocationValueWithMergeTag(event, substitutionMap, ticketing);
                replaceEventStartAndEndDateTimeValueWithMergeTag(event, substitutionMap, ticketing);
                if (null != ticketing.getLatitude() && null != ticketing.getLongitude()) {
                    substitutionMap.put(VIEW_ON_MAP, GOOGLE_MAP_URL + ticketing.getLatitude() + STRING_COMMA + ticketing.getLongitude());
                } else {
                    substitutionMap.put(VIEW_ON_MAP, STRING_EMPTY);
                }
            }
            Template template = new Template(pageName, new StringReader(htmlValue), freemarkerMailConfiguration);
            templateString = FreeMarkerTemplateUtils.processTemplateIntoString(template, substitutionMap);
        } catch (Exception e) {
            log.error("Error While replacing Merge tags on Bee free page, PageId -> {} and exception: {} ", pageId,e.getMessage());
        }
        return templateString;
    }

    private String replaceEventLogoAndEventSrcUrlInHTML(String htmlValue, Event event, EventDesignDetail eventDesignDetail) {
        String eventLogo = getEventOrWhiteLabelLogo(eventDesignDetail, event.getWhiteLabel());
        htmlValue = htmlValue != null ? htmlValue.replace(EVENT_SRC_URL, event.getEventURL()).replace(EVENT_LOGO_URL, eventLogo).replace(EVENT_IFRAME_DOMAIN, uiBaseUrl) : STRING_EMPTY;
        return htmlValue;
    }

    private String replaceEventLogoAndEventSrcUrlInJSON(String jsonValue, Event event, EventDesignDetail eventDesignDetail) {
        String eventLogo = getEventOrWhiteLabelLogo(eventDesignDetail, event.getWhiteLabel());
        jsonValue = jsonValue != null ? jsonValue.replace(EVENT_SRC_URL, event.getEventURL()).replace(EVENT_LOGO_URL, eventLogo).replace(EVENT_IFRAME_DOMAIN, uiBaseUrl) : STRING_EMPTY;
        return jsonValue;
    }

    private String getEventOrWhiteLabelLogo(EventDesignDetail eventDesignDetail, WhiteLabel whiteLabel) {
        String logo = eventDesignDetail.getLogoImage();
        if (org.apache.commons.lang3.StringUtils.isBlank(logo) && whiteLabel != null && org.apache.commons.lang3.StringUtils.isNotBlank(whiteLabel.getLogoImage())) {
            logo = whiteLabel.getLogoImage();
        }
        return imageConfiguration.getImagePrefix().concat(org.apache.commons.lang3.StringUtils.isBlank(logo) ? imageConfiguration.getBlackLogo() : logo);
    }

    public void replaceEventStartAndEndDateTimeValueWithMergeTag(Event event, Map<String, Object> substitutionMap, Ticketing ticketing) {
        Date eventStartDate = TimeZoneUtil.getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone());
        Date eventEndDate = TimeZoneUtil.getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone());

        SimpleDateFormat formatter = new SimpleDateFormat(E_MM_DD_YYYY_DATE_FORMAT);
        substitutionMap.put(START_DATE, formatter.format(eventStartDate));
        if (org.apache.commons.lang3.time.DateUtils.isSameDay(eventStartDate, eventEndDate)){
            substitutionMap.put(END_DATE,STRING_EMPTY);
        } else {
            substitutionMap.put(END_DATE,formatter.format(eventEndDate));
        }
        SimpleDateFormat timeFormatter = new SimpleDateFormat(HH_MM_A_TIME_FORMAT);
        String startTime = timeFormatter.format(eventStartDate);
        substitutionMap.put(START_TIME, startTime);
        String endTime = timeFormatter.format(eventEndDate);
        substitutionMap.put(END_TIME, endTime);

        String eventStartEndDateTime;

        if (eventStartDate.compareTo(eventEndDate) == 0) {
            eventStartEndDateTime = StringTools.formatCalanderDateForOrderEmail(eventStartDate, true, true).concat(SPACE_AT_SPACE).concat(StringTools.formatCalanderTime(eventStartDate, false)).concat(STRING_DASH).concat(StringTools.formatCalanderTime(eventEndDate, false));
        } else if (DateUtils.getNumberDaysInclusiveBothTheDatesInEvent(ticketing.getEventStartDate(), ticketing.getEventEndDate()) == 1L) {
            eventStartEndDateTime = StringTools.formatCalanderDateForOrderEmail(eventStartDate, true, true).concat(SPACE_AT_SPACE).concat(StringTools.formatCalanderTime(eventStartDate, false)).concat(SPACE_TO_SPACE).concat(StringTools.formatCalanderTime(eventEndDate, false));
        } else {
            eventStartEndDateTime = StringTools.formatCalanderDateForOrderEmail(eventStartDate, true, true).concat(SPACE_AT_SPACE).concat(StringTools.formatCalanderTime(eventStartDate, false)).concat(SPACE_TO_SPACE).concat(StringTools.formatCalanderDateForOrderEmail(eventEndDate, true, true)).concat(SPACE_AT_SPACE).concat(StringTools.formatCalanderTime(eventEndDate, false));
        }
        substitutionMap.put(EVENT_START_END_DATETIME, eventStartEndDateTime);
    }

    public void replaceEventLocationValueWithMergeTag(Event event, Map<String, Object> substitutionMap, Ticketing ticketing) {
        if (IN_PERSON.equals(event.getEventFormat()) || HYBRID.equals(event.getEventFormat())) {
            String location = org.apache.commons.lang3.StringUtils.isBlank(ticketing.getEventAddress()) ? TO_BE_ANNOUNCED : Arrays.stream(ticketing.getEventAddress().split(",")).findFirst().get();
            substitutionMap.put(EVENT_LOCATION, location);
        } else {
            substitutionMap.put(EVENT_LOCATION, ONLINE_EVENT);
        }
    }

    public String getCopyPageName(String pageName, Event event,int count,TemplateType templateType) {
        long pageCount = confirmationPagesRepository.findCustomPageCountByName(event.getEventId(), pageName,templateType);
        if (pageCount > 0) {
            log.info("getCopyPageName for event {} notAvailableName {} count {}", event.getEventId(), pageName, count);
            String nameCopy = pageName.startsWith("Copy_") ? String.format("Copy_%d%s", count+1, pageName.contains(STRING_BLANK) ? pageName.substring(pageName.indexOf(STRING_BLANK)) : STRING_EMPTY) : String.format("Copy_1 %s", pageName);
            if(nameCopy.length() > 250) {
                nameCopy = nameCopy.substring(0, 250);
            }
            count++;
            return getCopyPageName(nameCopy.trim(),event,count,templateType);
        } else {
            log.info("getCopyPageName for event {} availableName {} count {}", event.getEventId(), pageName, count);
            return pageName;
        }
    }

    private void moveCustomTemplateToMiddle(Event event, CustomTemplates topCustomTemplates, CustomTemplates bottomCustomTemplates,CustomTemplates customTemplates,TemplateType templateType) {
        double position = (topCustomTemplates.getPosition() + bottomCustomTemplates.getPosition()) / 2;
        if (position == 1) {
            CustomTemplates customTemplatesNextPositionItem = getNextPositionItem(customTemplates,event);
            CustomTemplates customTemplatesPreviousPositionItem = getPreviousPositionItem(customTemplates,event);
            if (customTemplatesNextPositionItem != null && customTemplatesPreviousPositionItem != null) {
                double positionDifference = (customTemplatesNextPositionItem.getPosition() - customTemplatesPreviousPositionItem.getPosition())
                        / 2;
                updateCustomTemplatePositionInGroup(event, topCustomTemplates.getPosition(), customTemplates.getPosition(), positionDifference,templateType);
                customTemplates.setPosition(bottomCustomTemplates.getPosition());
                confirmationPagesRepository.save(customTemplates);
            }
        } else {
            customTemplates.setPosition(position);
            confirmationPagesRepository.save(customTemplates);
        }
    }

    private void updateCustomTemplatePositionInGroup(Event event, double startPosition, double endPosition, double positionDifference,TemplateType templateType) {
        confirmationPagesRepository.updatePositionCustomTemplate(event.getEventId(),startPosition,endPosition,positionDifference,templateType.toString());
    }

    private CustomTemplates getPreviousPositionItem(CustomTemplates customTemplates, Event event) {
        List<CustomTemplates> previousPositionCustomTemplatesList=confirmationPagesRepository.previousPositionConfirmationPages(customTemplates.getId(),event.getEventId(),customTemplates.getPosition());
        return previousPositionCustomTemplatesList.isEmpty() ? null: previousPositionCustomTemplatesList.get(0);
    }

    private CustomTemplates getNextPositionItem(CustomTemplates customTemplates, Event event) {
        List<CustomTemplates> nextPositionCustomTemplatesList=confirmationPagesRepository.nextPositionConfirmationPages(customTemplates.getId(),event.getEventId(),customTemplates.getPosition());
        return nextPositionCustomTemplatesList.isEmpty() ? null: nextPositionCustomTemplatesList.get(0);
    }

    @Override
    public CustomTemplatesDto getConfirmationPageByRegistrationRequest(Event event, Long registrationRequestId, RegistrationRequestType registrationRequestType) {
        CustomTemplatesDto customTemplatesDto = new CustomTemplatesDto();
        RegistrationRequest registrationRequest = registrationRequestsService.getRegistrationRequestsOrThrowError(event, registrationRequestId);
        List<CustomTemplates> customTemplatesList;
        if(RegistrationRequestType.ATTENDEE.equals(registrationRequestType)){
            List<RegistrationRequestTicket> ticketTypeList = registrationRequestTicketRepository.findByRegistrationRequest(registrationRequest);
            List<Long> customTemplateIds=ticketTypeList.stream().map(e->e.getTicketType().getCustomTemplateId()).collect(Collectors.toList());
            customTemplatesList = confirmationPagesRepository.findByEventAndCustomTemplatesIdIn(event.getEventId(), customTemplateIds);
        }
        else {
            ConfirmationApprovalType confirmationApprovalType = RegistrationRequestType.SPEAKER.equals(registrationRequestType) ? ConfirmationApprovalType.SPEAKER : ConfirmationApprovalType.EXPO;
            customTemplatesList = confirmationPagesRepository.findByEventIdAndConfirmationApprovalType(event.getEventId(), confirmationApprovalType);
        }

        findMaxPositionCustomTemplate(customTemplatesList, customTemplatesDto,event, null, registrationRequest);
        return customTemplatesDto;
    }

    private String updateMergesValueForConfirmationPageForApproval(Long pageId, String pageName, String htmlValue, Event event, EventDesignDetail eventDesignDetail, RegistrationRequest registrationRequest) {
        String templateString = htmlValue;
        try {
            String eventDomain = serviceHelper.getEventBaseUrl(event);
            Map<String, Object> substitutionMap = new HashMap<>();
            updateMergeTagsForRegistrationApprovalBuyerAttributes(htmlValue,substitutionMap,registrationRequest.getTicketHolderAttributes(), event.getEventId());
            substitutionMap.put(Constants.EVENT_URL, eventDomain.concat(getEventPath()).concat(event.getEventURL()));
            substitutionMap.put(Constants.EVENT_UC_NAME, event.getName());
            substitutionMap.put(Constants.EVENT_TIME_ZONE, java.util.TimeZone.getTimeZone(event.getEquivalentTimeZone()).getDisplayName(false, java.util.TimeZone.SHORT));
            substitutionMap.put(EVENT_DOMAIN, eventDomain);
            substitutionMap.put(ADD_TO_CALENDAR_BUTTON, String.format(DOLLAR_OPEN_CLOSE_CURLY_BRACES, ADD_TO_CALENDAR_BUTTON));

            String eventTagLine = STRING_EMPTY;
            String eventDescription = STRING_EMPTY;
            if (eventDesignDetail != null) {
                eventTagLine = DEFAULT_EVENT_TAGE_LINE.equalsIgnoreCase(eventDesignDetail.getEventTagLine()) ? STRING_EMPTY : eventDesignDetail.getEventTagLine();
                eventDescription= org.apache.commons.lang3.StringUtils.isBlank(eventDesignDetail.getDesc()) ? STRING_EMPTY : eventDesignDetail.getDesc();

            }
            substitutionMap.put(EVENT_SLOGAN, eventTagLine);
            substitutionMap.put(EVENT_DESCRIPTION, eventDescription);
            Ticketing ticketing = ticketingService.findByEvent(event);
            if (ticketing != null) {
                replaceEventLocationValueWithMergeTag(event, substitutionMap, ticketing);
                replaceEventStartAndEndDateTimeValueWithMergeTag(event, substitutionMap, ticketing);
                if (null != ticketing.getLatitude() && null != ticketing.getLongitude()) {
                    substitutionMap.put(VIEW_ON_MAP, GOOGLE_MAP_URL + ticketing.getLatitude() + STRING_COMMA + ticketing.getLongitude());
                } else {
                    substitutionMap.put(VIEW_ON_MAP, STRING_EMPTY);
                }
            }
            Template template = new Template(pageName, new StringReader(htmlValue), freemarkerMailConfiguration);
            templateString = FreeMarkerTemplateUtils.processTemplateIntoString(template, substitutionMap);
        } catch (Exception e) {
            log.error("Error While replacing Merge tags on Bee free page, PageId -> {} and exception: {} ", pageId,e.getMessage());
        }
        return templateString;
    }

    public void updateMergeTagsForRegistrationApprovalBuyerAttributes(String htmlValue, Map<String, Object> substitutionMap, TicketHolderAttributes ticketHolderAttributes, Long eventId) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(htmlValue)) {
            log.info("Update buyer attributes in substitution map for confirmation email and eventId: {} and orderId : {}", eventId, ticketHolderAttributes);
            List<String> mergeTags = BuyerAttributesMergeTagHelper.findMergeTagsFromHtmlValueAndMergeTagType(htmlValue, MergeTagType.BUYER);
            if (CollectionUtils.isEmpty(mergeTags)) {
                List<Long> ids = BuyerAttributesMergeTagHelper.extractIdsFromMergeTags(mergeTags, MergeTagType.BUYER);
                if (!ids.isEmpty()) {
                    replaceMergeValueInSubstitutionMap(substitutionMap, ids, ticketHolderAttributes, eventId, mergeTags);
                }
            }
        }
    }

    private void replaceMergeValueInSubstitutionMap(Map<String, Object> substitutionMap, List<Long> ids, TicketHolderAttributes ticketHolderAttributes, Long eventId, List<String> mergeTags) {
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService.findByIdsIn(ids);
        log.info("Buyer attributes found for confirmation email and eventId: {} and ticketHolderRequiredAttributes: {} and orderId: {}", eventId,ticketHolderRequiredAttributes.size(),ticketHolderAttributes);
        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(ticketHolderAttributes);
        if(registrationResponseDto != null){
            Map<String, String> buyerAttributesMap = registrationResponseDto.getAttributes();
            for (TicketHolderRequiredAttributes attribute : ticketHolderRequiredAttributes) {
                String key = AttendeeTypeEnum.BUYER.getValue().concat(STRING_UNDERSCORE) + attribute.getId();
                sendGridMailPrepareService.updateSubstitutionMapForAttribute(substitutionMap, key, attribute, buyerAttributesMap,true);
            }
        }
        else {
            BuyerAttributesMergeTagHelper.replaceMergeTagForTheTestEmails(mergeTags,substitutionMap);
        }
    }

    public RegistrationResponseDto getRegistrationRequestDto(TicketHolderAttributes ticketHolderAttributes) {
        Gson gson = new Gson();
        try {
            RegistrationResponseDto dto = gson.fromJson(ticketHolderAttributes.getJsonValue(), RegistrationResponseDto.class);
            if (dto == null) {
                dto = new RegistrationResponseDto();
            }
            return dto;
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return new RegistrationResponseDto();
        }
    }
}