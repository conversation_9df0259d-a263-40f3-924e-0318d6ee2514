package com.accelevents.session_speakers.services.impl.cache;

import com.accelevents.common.dto.ExhibitorCarouselDetailDto;
import com.accelevents.common.dto.SponsorsDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumKeyValueType;
import com.accelevents.domain.session_speakers.*;
import com.accelevents.ro.event.service.ROSessionService;
import com.accelevents.ro.session.ROSessionSpeakerService;
import com.accelevents.ro.speaker.ROSpeakerService;
import com.accelevents.services.ExhibitorService;
import com.accelevents.services.SponsorsService;
import com.accelevents.services.VirtualEventService;
import com.accelevents.session_speakers.comparator.TitleComparator;
import com.accelevents.session_speakers.dto.ExhibitorsSessionDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.dto.SpeakersSessionDTO;
import com.accelevents.session_speakers.dto.SponsorsSessionDTO;
import com.accelevents.session_speakers.dto.cache.SessionBasicCacheDTO;
import com.accelevents.session_speakers.dto.cache.SessionCacheDto;
import com.accelevents.session_speakers.dto.cache.SpeakerCacheBasicDTO;
import com.accelevents.session_speakers.dto.cache.SpeakerCacheDTO;
import com.accelevents.session_speakers.repo.MUXLivestreamAssetRepo;
import com.accelevents.session_speakers.repo.SessionLocationRepo;
import com.accelevents.session_speakers.repo.SessionRepo;
import com.accelevents.session_speakers.repo.WorkshopRecordingAssetsRepo;
import com.accelevents.session_speakers.services.*;
import com.accelevents.session_speakers.services.impl.SessionServiceImpl;
import com.accelevents.session_speakers.services.impl.SpeakerRepoService;
import com.accelevents.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.EnumKeyValueType.TAG;
import static com.accelevents.domain.enums.EnumKeyValueType.TRACK;
import static java.util.stream.Collectors.toList;

@Service
public class SessionCacheServiceImpl implements SessionCacheService {

    private Logger logger = LoggerFactory.getLogger(SessionCacheServiceImpl.class);

	@Autowired private SessionRepoService sessionRepoService;
    @Autowired private ROSessionService roSessionService;

    @Autowired
    private ExhibitorService exhibitorService;

    @Autowired
    private SponsorsService sponsorsService;

    @Autowired
    private SessionServiceImpl sessionServiceImpl;

    @Autowired
    private SessionDetailsRepoService sessionDetailsRepoService;

    @Autowired
    private MUXLivestreamAssetRepo muxLivestreamAssetRepo;

    @Autowired
    private WorkshopRecordingAssetsRepo workshopRecordingAssetsRepo;
	@Autowired private SpeakerRepoService speakerRepoService;
	@Autowired private ROSpeakerService roSpeakerService;
	@Autowired private SessionSpeakerService sessionSpeakerService;
    @Autowired private ROSessionSpeakerService roSessionSpeakerService;
	@Autowired private KeyValueService keyValueService;
	@Autowired private UserSessionService userSessionService;

    @Autowired
    private VirtualEventService virtualEventService;
    @Autowired
    private SessionRepo repo;
	@Autowired private UserInterestedSessionService userInterestedSessionService;
	@Autowired private SessionTagAndTrackService sessionTagAndTrackService;
    @Autowired private SessionLocationRepo sessionLocationRepo;

	@Override
	public List<SessionCacheDto> getSessionListMainStage(Event event, boolean isAdminOrStaff) {
		List<Session> sessions = sessionRepoService.getAllSessionByEventId(event, null,isAdminOrStaff);
		return sessions.stream().map(session-> getSessionDetails(event, session)).collect(Collectors.toList());
	}

	public List<SessionBasicCacheDTO> getSessionBasicCacheDTOS(Event event, List<Session> sessionsPage) {
	    Collections.sort(sessionsPage,new TitleComparator().sessionTitleComparator);
        sessionsPage.sort(Comparator.comparing(Session::getStartTime));
		List<Long> sessionIds = sessionsPage.stream().map(Session::getId).collect(Collectors.toList());
		Map<Long, List<Speaker>> sessionIdsSpeakers = sessionSpeakerService.getSessionSpeakerIdSessionIds(sessionIds);

		return sessionsPage.stream().map(e-> new SessionBasicCacheDTO(e, event, sessionIdsSpeakers.get(e.getId())))
				.collect(Collectors.toList());
	}

	@Override
	public SessionCacheDto getSessionDetailsById(Event event, Long sessionId) {
		Session session = sessionRepoService.getSessionByIdJoinFetchTagsTrack(sessionId,event);
		return this.getSessionDetails(event, session);
	}

	private SessionCacheDto getSessionDetails(Event event, Session session){
		Map<Long, List<Speaker>> sessionIdsSpeakers = sessionSpeakerService.getSessionSpeakerIdSessionIds(Collections.singletonList(session.getId()));
		List<Long> keyValueIds = sessionTagAndTrackService.findBySessionId(session.getId()).stream().map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toList());
		List<KeyValue> keyValue = keyValueService.findAllByIds(keyValueIds);

		SessionCacheDto dto =  new SessionCacheDto(session,event,
				sessionIdsSpeakers.get(session.getId()),
				getTagOrTrackIds(keyValue, TAG),
				getTagOrTrackIds(keyValue, TRACK));

		return dto;
	}


    @Override
    public List<SpeakerCacheBasicDTO> getSpeakerList(Event event, String searchString) {//NOSONAR

        List<SpeakerCacheBasicDTO> speakerCacheBasicDTOList = new ArrayList<>();
        List<Speaker> allSpeakersByEventId = roSpeakerService.getAllSpeakerByEventId(event);

        if(StringUtils.isNotBlank(searchString)) {
            allSpeakersByEventId = allSpeakersByEventId.stream().filter(e->e.getFirstName().toLowerCase().contains(searchString.toLowerCase()) ||
                    e.getLastName().toLowerCase().contains(searchString.toLowerCase()) ||
                    (e.getFirstName()+ Constants.SINGLE_WHITE_SPACE + e.getLastName()).toLowerCase().contains(searchString.toLowerCase()))
                    .collect(toList());
        }

        if (!allSpeakersByEventId.isEmpty()) {
            logger.info("Total {} speakers in event {} ", allSpeakersByEventId.size(), event.getEventId());

            List<SessionSpeaker> sessionSpeakers = sessionSpeakerService.findByEventId(event.getEventId());
            Map<Long, List<SessionSpeaker>> sessionSpeakerMap = sessionSpeakers.stream().collect(Collectors.groupingBy(SessionSpeaker::getSpeakerId));
            if (sessionSpeakerMap.size() > 0) {
                logger.info("Total {} session speaker in event {} ", sessionSpeakerMap.size(), event.getEventId());
                allSpeakersByEventId.forEach(speaker -> {
                    if (sessionSpeakerMap.containsKey(speaker.getId())) {
                        List<Boolean> speakerShowModeratorList = sessionSpeakerMap.get(speaker.getId()).stream().map(SessionSpeaker::isShowModerator).collect(Collectors.toList());//NOSONAR
                        if (speakerShowModeratorList.contains(true)){
                            speakerCacheBasicDTOList.add(new SpeakerCacheDTO(speaker, null));
                        }else {
                            logger.info("speaker {} is show from speakerlist in event {}", speaker.getId(), event.getEventId());
                        }
                    } else {
                        logger.info("speaker {} is not added any session in event {}", speaker.getId(), event.getEventId());
                        speakerCacheBasicDTOList.add(new SpeakerCacheDTO(speaker, null));
                    }
                });
            } else {
                speakerCacheBasicDTOList.addAll(allSpeakersByEventId.stream().map(e -> new SpeakerCacheDTO(e, null)).collect(Collectors.toList()));
            }
        }
        return speakerCacheBasicDTOList;
    }

	@Override
	public SpeakerCacheDTO getSpeakerDetailsById(Event event, Long speakerId) {
		Speaker speaker = speakerRepoService.getSpeakerByIdOrThrowNotFound(speakerId,event);
		Map<Long, List<Session>> speakerIdsSessions = roSessionSpeakerService.getSessionSpeakerIdSpeakerIds(Collections.singletonList(speakerId),true);
		return new SpeakerCacheDTO(speaker, speakerIdsSessions.get(speakerId));
	}

	@Override
	public List<SessionBasicCacheDTO> getSpeakerTalks(Event event, User user) {
		List<Session> sessions = sessionSpeakerService.findSessionBySpeakerUserId(event, user.getUserId());
		return getSessionBasicCacheDTOS(event, sessions);
	}

	@Override
	public List<SessionBasicCacheDTO> getMyRegisteredSessions(Event event, User user, boolean isAdminOrStaff) {
		List<Long> interestedSessionIds = userSessionService.findByEventIdAndUserId(event.getEventId(), user.getUserId());
		List<Session> sessions = sessionRepoService.findAllByEventIdAndIdIn(event, interestedSessionIds, PageRequest.of(0, Integer.MAX_VALUE), user,true,  isAdminOrStaff, null).getContent();
		return getSessionBasicCacheDTOS(event, sessions);
	}

	@Override
	public List<SessionBasicCacheDTO> getMyInterestedSessions(Event event, User user, boolean isAdminOrStaff) {
		List<Long> interestedSessionIds = userInterestedSessionService.getMyInterestedSessions(user, event);
		List<Session> sessions = sessionRepoService.findAllByEventIdAndIdIn(event, interestedSessionIds, PageRequest.of(0, Integer.MAX_VALUE), user,true, isAdminOrStaff, null).getContent();
		return getSessionBasicCacheDTOS(event, sessions);
	}

    @Override
    public List<SpeakersSessionDTO> getAllSessionSpeakers(Event event) {
        List<Long> sessionIds = roSessionService.findAllSessionIdByEvent(event);
        Map<Long, List<SpeakerDTO>> groupedSpeakers = sessionSpeakerService.getSessionSpeakerIdSessionDtoIds(sessionIds);
        return groupedSpeakers.entrySet().stream()
                .map(entry -> new SpeakersSessionDTO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }


    @Override
    public List<SponsorsSessionDTO> getAllSessionSponsors(Event event) {
        List<Session> sessions = sessionRepoService.getAllSessionsByEventIdAndSponsorExhibitorJsonNotNull(event);
        List<SponsorsDto> sponsorsDtos = sponsorsService.getAllSponsorsByEvent(event);

        if (CollectionUtils.isEmpty(sponsorsDtos) || CollectionUtils.isEmpty(sessions)) {
            return Collections.emptyList();
        }

        Map<Long, SponsorsDto> sponsorMap = sponsorsDtos.stream()
                .collect(Collectors.toMap(SponsorsDto::getId, sponsor -> sponsor));

        return sessions.stream()
                .filter(session -> StringUtils.isNotBlank(session.getSponsorExhibitorJson()))
                .map(session -> {
                    List<Long> sponsorIds = sponsorsService.getSponsorIdsFromSponsorExhibitorJson(session.getSponsorExhibitorJson());
                    List<SponsorsDto> sponsorsForSession = sponsorIds.stream()
                            .map(sponsorMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    return new SponsorsSessionDTO(session.getId(), sponsorsForSession);
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<ExhibitorsSessionDTO> getAllSessionExhibitors(Event event) {
        List<Session> sessions = sessionRepoService.getAllSessionsByEventIdAndSponsorExhibitorJsonNotNull(event);
        List<ExhibitorCarouselDetailDto> exhibitorCarouselDetailDtos = exhibitorService.getAllExhibitorLogoForDisplay(event);

        if (CollectionUtils.isEmpty(exhibitorCarouselDetailDtos) || CollectionUtils.isEmpty(sessions)) {
            return Collections.emptyList();
        }

        Map<Long, ExhibitorCarouselDetailDto> exhibitorMap = exhibitorCarouselDetailDtos.stream()
                .collect(Collectors.toMap(ExhibitorCarouselDetailDto::getId, exhibitor -> exhibitor));

        return sessions.stream()
                .filter(session -> StringUtils.isNotBlank(session.getSponsorExhibitorJson()))
                .map(session -> {
                    List<Long> exhibitorIds = sponsorsService.getExhibitorIdsFromSponsorExhibitorJson(session.getSponsorExhibitorJson());
                    List<ExhibitorCarouselDetailDto> exhibitorsForSession = exhibitorIds.stream()
                            .map(exhibitorMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    return new ExhibitorsSessionDTO(session.getId(), exhibitorsForSession);
                })
                .collect(Collectors.toList());
    }

    public List<Long> getTagOrTrackIds(List<KeyValue> keyValue, EnumKeyValueType tag) {
		return keyValue.stream().filter(e -> tag.equals(e.getType()))
				.map(KeyValue::getId).collect(toList());
	}

}
