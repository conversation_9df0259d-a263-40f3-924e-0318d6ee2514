package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.AttendeeBookedScheduleDto;
import com.accelevents.common.dto.EventNameLookUpDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.MeetingOrigin;
import com.accelevents.domain.enums.MeetingStatus;
import com.accelevents.domain.session_speakers.MeetingSchedule;
import com.accelevents.dto.AttendeeMeetingCreationConfigDTO;
import com.accelevents.session_speakers.dto.IdCountDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface MeetingScheduleService {

    List<AttendeeBookedScheduleDto> getAllBookdedMeetingScheduleByUserIdAndStatusBookedFilterByMonthAndYear(List<Long> userIds, MeetingStatus booked, Date startTime, Date endTime, Long eventId);

    MeetingSchedule createMeetingSchedule(AttendeeBookedScheduleDto attendeeBookedScheduleDto, Long senderUser, Long receiverUser, Event event, User createdBy);

    Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable);

    void acceptMeetingSchedule(long id);

    void rejectMeetingSchedule(long id, Long userId);

    void save(MeetingSchedule meetingSchedule);

    MeetingSchedule updateMeetingSchedule(AttendeeBookedScheduleDto attendeeBookedScheduleDto, Long senderUser, Long receiverUser, Long scheduleId, Event event);

    void cancelMeetingSchedule(long id, boolean sendPushNotification, User currentUser);

    Page<MeetingSchedule> getAllCreatedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable);

    void acceptRejectedMeetingSchedule(long id);

    Page<MeetingSchedule> getAllRejectedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable);

    long getTotalMeetingBooked(Long eventId);


    /**
     * Get total meeting booked in exhibitor
     * @param eventId
     * @param exhibitorId
     * @return total count of booked meeting
     */
    long getTotalMeetingBookedInExhibitor(Long eventId, Long exhibitorId);

    /**
     * Get total booked meeting of user from given meeting origin
     * @param eventId id of event
     * @param userId user id to get meeting booked count
     * @param origin origin of the meeting
     * @return count of meeting booked for given user and meeting origin
     */
    long getTotalBookedMeetingOfUserInOrigin(Long eventId, Long userId, MeetingOrigin origin);

    void deleteMeetingScheduleByEventId(long id);

    Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatusList(Long userId, long eventId, List<MeetingStatus> statusList, List<MeetingOrigin> meetingOriginList, Date startTime, Pageable pageable);

    Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, List<MeetingOrigin> meetingOriginList,  Pageable pageable);

    List<Object[]> getBookedMeetingByUserIds(Long eventId, List<Long> userIds);

    List<IdCountDto> getMeetingRequestSentByUserIds(Long eventId, List<Long> userIds);

    List<IdCountDto> getMeetingRequestReceivedByUserIds(Long eventId, List<Long> userIds);

    Page<MeetingSchedule> getAllMeetingSchedulesByEventIdAndOrigin(Long eventId, String meetingOrigin, Pageable pageable, String searchString);

    MeetingSchedule getMeetingScheduleInfoById(long eventId, long id);

    List<Object[]> getMeetingByEventIdAndStatus(String eventUrl, String status);

    boolean isAllowAttendeeToScheduleMeetingByEventTicketType(Event event, Long userId);
    Map<Long, Boolean> isAllowAttendeeToScheduleMeetingByEventTicketType(Event event, List<Long> userList);
    AttendeeMeetingCreationConfigDTO getPreScheduleMeetingAndEventTicketMeetingCreationStatus(Event event, Long userId);

    List<MeetingSchedule> findScheduledMeetingOfABooth(Long eventId, Long expoId, List<Long> receiverId,List<MeetingStatus> status,
                                                       List<String> location);

    Optional<MeetingSchedule> findByExhibitorIdAndIdAndEventId(Long expoId, Long meetingId, Long eventId);

    void remindMeetingScheduleBeforeMeetingStart(String eventId);

    String getUserName(User user);

    void sendMessage(String meetingReminderContextForFirstUser, String meetingReminderContextForSecondUser, User senderUser, User receiverUser, EventNameLookUpDto eventNameLookUpDto, long meetingId,  String firstUserRawContent, String secondUserRawContent, String title, Long loggedInUserId);
}
