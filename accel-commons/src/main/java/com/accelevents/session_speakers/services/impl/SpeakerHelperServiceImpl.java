package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.Staff;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.dto.EmailMessage;
import com.accelevents.dto.SessionSpeakerIdDto;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.EventDesignDetailService;
import com.accelevents.services.SendGridMailService;
import com.accelevents.services.StaffService;
import com.accelevents.services.TicketingService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionSpeakerRepoService;
import com.accelevents.session_speakers.services.SpeakerHelperService;
import com.accelevents.utils.Constants;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.SecurityUtils.encode;

@Service
public class SpeakerHelperServiceImpl implements SpeakerHelperService {
    private static final Logger log = LoggerFactory.getLogger(SpeakerHelperServiceImpl.class);

    @Autowired
    private SendGridMailService sendGridMailService;
    @Autowired
    private ServiceHelper serviceHelper;
    @Autowired
    private SessionRepoService sessionRepoService;
    @Autowired
    private EventDesignDetailService eventDesignDetailService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private SessionSpeakerRepoService sessionSpeakerRepoService;
    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private TextMessageUtils textMessageUtils;
    @Autowired
    private ImageConfiguration imageConfiguration;
    @Autowired
    private SpeakerHelperAsyncServiceImpl speakerHelperAsyncServiceImpl;
    @Autowired
    private TicketingService ticketingService;

    @Value("${uiBaseurl}")
    private String uiBaseurl;

    @Override
    @Transactional
    public void sendInvite(Event event, User loggedInUser, Long sessionId, Long speakerId) {
        log.info("SpeakerHelperServiceImpl | sendInvite | speakerId {} sessionId {}", speakerId, sessionId);
        Session session = sessionRepoService.getSessionById(sessionId, event);
        speakerHelperAsyncServiceImpl.sendInvite(event, loggedInUser, session, speakerId);
    }

    @Override
    @Transactional
    public void sendInvite(Event event, Long speakerId, String pastSpeakerEmail) {
        log.info("SpeakerHelperServiceImpl | sendInvite | With Past speakerEmail | speakerId {} PastSpeakerEmail {}", speakerId, pastSpeakerEmail);
        speakerHelperAsyncServiceImpl.sendInviteWithPastEmail(event, speakerId, pastSpeakerEmail);
    }

    @Override
    public void sendInviteToAllSpeakers(Event event, String authToken,User loggedInUser) {
        log.info("SpeakerHelperServiceImpl | sendInviteToAllSpeakers | eventId {}", event.getEventId());

        ticketingService.checkEventPostEnangeEmailAccessPermitIsPassed(event, loggedInUser);

        List<SessionSpeakerIdDto> sessionSpeakerIdDtoList = sessionSpeakerRepoService.findSessionSpeakerIdByEventId(event.getEventId());
        if(CollectionUtils.isNotEmpty(sessionSpeakerIdDtoList)){
            speakerHelperAsyncServiceImpl.sendInviteToAllSpeakers(sessionSpeakerIdDtoList, event.getEventURL(), authToken);
        } else {
            throw new NotFoundException(NotFoundException.SessionSpeakerNotFound.SESSION_SPEAKER_NOT_FOUND);
        }

    }

    @Override
    @Transactional
    public void sendInviteToListOfSpeaker(Event event, User loggedInUser, Set<SessionSpeaker> sessionSpeakers) {
        log.info("SpeakerHelperServiceImpl | sendInviteToSpeakerList eventId {}", event.getEventId());
        sessionSpeakers.stream().forEach(sessionSpeaker -> speakerHelperAsyncServiceImpl.sendInvite(event, loggedInUser, sessionSpeaker.getSession(), sessionSpeaker.getSpeakerId()));
    }

    public String getUtmContentRoleWise(Event event, User user) {
        if (roStaffService.isEventAdmin(event, user)) {
            return IS_ADMIN;
        } else if (roStaffService.isEventStaff(event, user)) {
            return IS_STAFF;
        }
        return STRING_EMPTY;
    }

    @Async
    @Override
    public void sendInvite(List<Speaker> speakers, String oldEmail) {

        List<StaffRole> role = new ArrayList<>(Arrays.asList(StaffRole.admin, StaffRole.whitelabeladmin, StaffRole.eventcoordinator));

        speakers.forEach(e -> {
            Event event = roEventService.getEventByIdIfPresentOrNull(e.getEventId());
            if (null != event) {
                List<Staff> allStaffByEventId = staffService.findAllByEventId(event.getEventId());
                allStaffByEventId = allStaffByEventId.stream().filter(allStaff -> role.contains(allStaff.getRole())).collect(Collectors.toList());
                List<User> user = allStaffByEventId.stream().map(Staff::getUser).collect(Collectors.toList());

                String eventBaseUrl = serviceHelper.getEventBaseUrl(event);

                user.forEach(adminUser -> {
                    Set<String> mailSendTo = new HashSet<>();
                    mailSendTo.add(adminUser.getEmail());
                    EmailMessage emailMessage = new EmailMessage(TemplateId.EVENT_SPEAKER_EMAIL_CHANGE);
                    emailMessage.setTemplateName(TemplateId.EVENT_SPEAKER_EMAIL_CHANGE.getValue());
                    StringBuilder singleItems = new StringBuilder();
                    singleItems.append("Speaker changed his email address");
                    emailMessage.setSubject(singleItems.toString());
                    emailMessage.setWhiteLabel(event.getWhiteLabel());

                    Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();
                    substitutionMap.put(IMAGE_LOWER, eventDesignDetailService.getAccelEVentOrWhiteLabelLogo(event));
                    substitutionMap.put("adminName", textMessageUtils.capitalizeFirstCharacter(adminUser.getFirstName()));
                    substitutionMap.put("eventName", event.getName());
                    substitutionMap.put("speakerName", e.getFirstName() + " " + e.getLastName());
                    substitutionMap.put("oldEmail", oldEmail);
                    substitutionMap.put("newEmail", e.getEmail());
                    substitutionMap.put(EVENT_ID_UPPERCASE, event.getEventId());
                    substitutionMap.put("privacy_policy", "https://www.accelevents.com/privacy/privacy");
                    substitutionMap.put("help_center", "https://support.accelevents.com/en/");
                    substitutionMap.put("create_event", uiBaseurl + "/host/eventsetup");
                    substitutionMap.put("view_user_profile", eventBaseUrl + "/host/event-ticketing/agenda");
                    substitutionMap.put("facebook_icon", imageConfiguration.getImagePreFixWithCloudinaryUrlForSpeakerInviteEmail(imageConfiguration.getImagePrefix()).concat(DEFAULT_AE_IMAGES).concat(EMAIL_SVG_FACEBOOK));
                    substitutionMap.put("instagram_icon", imageConfiguration.getImagePreFixWithCloudinaryUrlForSpeakerInviteEmail(imageConfiguration.getImagePrefix()).concat(DEFAULT_AE_IMAGES).concat(EMAIL_SVG_INSTAGRAM));
                    substitutionMap.put("linkedin_icon", imageConfiguration.getImagePreFixWithCloudinaryUrlForSpeakerInviteEmail(imageConfiguration.getImagePrefix()).concat(DEFAULT_AE_IMAGES).concat(EMAIL_SVG_LINKEDIN));
                    substitutionMap.put("twitter_icon", imageConfiguration.getImagePreFixWithCloudinaryUrlForSpeakerInviteEmail(imageConfiguration.getImagePrefix()).concat(DEFAULT_AE_IMAGES).concat(EMAIL_SVG_TWITTER));
                    substitutionMap.put("hand_wave", imageConfiguration.getImagePreFixWithCloudinaryUrlForSpeakerInviteEmail(imageConfiguration.getImagePrefix()).concat(DEFAULT_AE_IMAGES).concat(EMAIL_SVG_HAND_WAVE));
                    substitutionMap.put("unsubscribe_link", eventBaseUrl + "/u/unsubscribe?key=" + encode(Constants.EVENT_ID + "=" + event.getEventId() + "&" + Constants.EMAIL + "=" + adminUser.getEmail()));
                    sendGridMailPrepareService.addDefaultOrWhiteLabelMailSubstitution(event, substitutionMap);
                    sendGridMailService.sendTemplateMail(emailMessage, mailSendTo);
                });


            }
        });

    }
}