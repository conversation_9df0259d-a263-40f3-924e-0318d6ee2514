package com.accelevents.session_speakers.services.impl;

import com.accelevents.common.dto.AttendeeCheckInAnalyticsDto;
import com.accelevents.domain.Event;
import com.accelevents.dto.KeyValueDto;
import com.accelevents.services.CheckInAuditLogService;
import com.accelevents.services.ExhibitorService;
import com.accelevents.services.GetStreamService;
import com.accelevents.services.StaffService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.session_speakers.services.VirtualEventAnalyticsService;
import com.accelevents.utils.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class VirtualEventAnalyticsServiceImpl implements VirtualEventAnalyticsService {


	@Autowired private CheckInAuditLogService checkInAuditLogService;
	@Autowired private GetStreamService getStreamService;
	@Autowired private SpeakerService speakerService;
	@Autowired private ExhibitorService exhibitorService;
	@Autowired private StaffService staffService;
    @Autowired private EventCommonRepoService eventCommonRepoService;

	@Override
	public AttendeeCheckInAnalyticsDto getAttendeeCheckInAnalyticsForEvent(Event event, Date date, Long ticketTypeId) {
		AttendeeCheckInAnalyticsDto attendeeCheckInAnalyticsDto = new AttendeeCheckInAnalyticsDto();
        TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());
        String eventTimeZone = timeZone.getEquivalentTimezone();

        Date startDateUTC = null;
        Date endDateUTC = null;
        if(null != date){
            startDateUTC = TimeZoneUtil.getStartOfDay(date);
            endDateUTC = TimeZoneUtil.getEndOfDay(date);
            startDateUTC = TimeZoneUtil.getDateInUTC(startDateUTC,eventTimeZone);
            endDateUTC = TimeZoneUtil.getDateInUTC(endDateUTC,eventTimeZone);
        }

        // set total number of checked in attendees
        Long allCheckedInAttendee = eventCommonRepoService.findAllCheckedInAttendees(event.getEventId(),ticketTypeId,startDateUTC,endDateUTC);
        attendeeCheckInAnalyticsDto.setNoOfCheckedInAttendee(allCheckedInAttendee);
		//set number of logged in users
		List<BigInteger> userIds = checkInAuditLogService.getAllUserIdsByEventIdAndDateAndTicketTypeId(event,date,eventTimeZone,ticketTypeId);

        List<Long> longUserIds = userIds.stream()
                .map(s -> ((BigInteger)s).longValue())
                .collect(Collectors.toList());

		attendeeCheckInAnalyticsDto.setNoOfLoggedInUser(userIds.size());
		//get number of all attendee
		List<BigInteger> ticketHolderuserIds = checkInAuditLogService.getAllUserIdsOfTicketHolderByEventIdAndDateAndTicketTypeId(event,date,eventTimeZone, ticketTypeId);
        List<Long> longTicketHolderuserIds = ticketHolderuserIds.stream()
                .map(s -> ((BigInteger)s).longValue())
                .collect(Collectors.toList());
		//set number of speaker
		List<Long> listSpeakeruserIds = speakerService.findSpeakerUserIdsByEventId(event);

        if(!listSpeakeruserIds.isEmpty())
        {
            listSpeakeruserIds =   listSpeakeruserIds.stream().filter(speaker -> longUserIds.contains(speaker)).collect(Collectors.toList());
        }
		//Set a number of Exhibitor
		List<Long> staffsExp = staffService.findExhibitorUserIdsByEventId(event);
		if(!staffsExp.isEmpty())
			{
                staffsExp = staffsExp.stream().distinct().collect(Collectors.toList());
                staffsExp = staffsExp.stream().filter(staff -> longUserIds.contains(staff)).collect(Collectors.toList());
		    }
		//Set number of Event Staff user
		List<Long> eventStaffUserIds = staffService.findAllStaffUserIdsByEvent(event);
		if(!eventStaffUserIds.isEmpty())
		{
            eventStaffUserIds  = eventStaffUserIds.stream().filter(staff -> longUserIds.contains(staff)).collect(Collectors.toList());
            attendeeCheckInAnalyticsDto.setNoOfCheckedInByAdminOrStaff(eventStaffUserIds.size());
		}

        //Removing eventStaff,exhibitor, speaker from ticketHolder if exist
        List<Long> speakerExhibitorStaffs = new ArrayList<>();
        Stream.of(eventStaffUserIds, staffsExp, listSpeakeruserIds).forEach(speakerExhibitorStaffs::addAll);
        longTicketHolderuserIds.removeAll(new HashSet(speakerExhibitorStaffs));

        attendeeCheckInAnalyticsDto.setNoOfCheckedInByTicketHolder(longTicketHolderuserIds.size());

        //Removing eventStaff,exhibitor from speaker if exist
        List<Long> speakerExhibitor = new ArrayList<>();
        Stream.of(eventStaffUserIds, staffsExp).forEach(speakerExhibitor::addAll);
        listSpeakeruserIds.removeAll(new HashSet(speakerExhibitor));

        attendeeCheckInAnalyticsDto.setNoOfCheckedInBySpeaker(listSpeakeruserIds.size());

        //Removing eventStaff from exhibitor if exist
        staffsExp.removeAll(new HashSet(eventStaffUserIds));
        attendeeCheckInAnalyticsDto.setNoOfCheckedInByExhibitor(staffsExp.size());

        return attendeeCheckInAnalyticsDto;
	}
}
