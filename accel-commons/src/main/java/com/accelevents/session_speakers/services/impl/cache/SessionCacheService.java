package com.accelevents.session_speakers.services.impl.cache;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.session_speakers.dto.ExhibitorsSessionDTO;
import com.accelevents.session_speakers.dto.SpeakersSessionDTO;
import com.accelevents.session_speakers.dto.SponsorsSessionDTO;
import com.accelevents.session_speakers.dto.cache.SessionBasicCacheDTO;
import com.accelevents.session_speakers.dto.cache.SessionCacheDto;
import com.accelevents.session_speakers.dto.cache.SpeakerCacheBasicDTO;
import com.accelevents.session_speakers.dto.cache.SpeakerCacheDTO;

import java.util.List;

public interface SessionCacheService {
	List<SessionCacheDto> getSessionListMainStage(Event event, boolean isAdminOrStaff);

	SessionCacheDto getSessionDetailsById(Event event, Long sessionId);

	List<SpeakerCacheBasicDTO> getSpeakerList(Event event, String searchString);

	SpeakerCacheDTO getSpeakerDetailsById(Event event, Long speakerId);

	List<SessionBasicCacheDTO> getSpeakerTalks(Event event, User user);

	List<SessionBasicCacheDTO> getMyRegisteredSessions(Event event, User user, boolean isAdminOrStaff);

	List<SessionBasicCacheDTO> getMyInterestedSessions(Event event, User user, boolean isAdminOrStaff);

    List<SpeakersSessionDTO> getAllSessionSpeakers(Event event);

    List<SponsorsSessionDTO> getAllSessionSponsors(Event event);

    List<ExhibitorsSessionDTO> getAllSessionExhibitors(Event event);
}
