package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionTagAndTrack;
import org.springframework.data.repository.query.Param;

import javax.management.Query;
import java.util.List;
import java.util.Set;

public interface SessionTagAndTrackService {
	void createRecords(Long sessionId, Set<Long> tagIdOrTrackIds);

	void deleteRecords(Long sessionId, Set<Long> tagIdOrTrackIds);

	List<Long> findByIds(List<Long> tagOrTrackIds);

    void deleteByTagOrTrackIds(List<Long> tagIdOrTrackIds);

    Iterable<SessionTagAndTrack> saveAll(Iterable<SessionTagAndTrack> sessionTagAndTracks);

    void deleteBySessionId(Long sessionId);

    List<SessionTagAndTrack> findBySessionIds(List<Long> sessionIds);

    List<SessionTagAndTrack> findBySessionId(Long sessionId);

    List<Long> findSessionIdsByIds(List<Long> tagOrTrackIds);

    List<Session> findSessionsByTagOrTrackIds(List<Long> tagOrTrackIds);

    Boolean existsBySessionIdAndTagOrTrackId(Long sessionId, Long tagOrTrackId);

    List<Long> existsBySessionIdAndTagOrTrackIds(Long sessionId, List<Long> tagOrTrackId);
}
