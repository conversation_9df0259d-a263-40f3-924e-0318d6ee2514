package com.accelevents.session_speakers.services.impl;

import com.accelevents.auction.dto.ExhibitorStaffDTO;
import com.accelevents.common.dto.AttendeeBookedScheduleDto;
import com.accelevents.common.dto.EventNameLookUpDto;
import com.accelevents.common.dto.ExhibitorDetail;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.MeetingOrigin;
import com.accelevents.domain.enums.MeetingStatus;
import com.accelevents.domain.enums.TargetAudienceType;
import com.accelevents.domain.session_speakers.MeetingOptions;
import com.accelevents.domain.session_speakers.MeetingSchedule;
import com.accelevents.dto.AttendeeMeetingCreationConfigDTO;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.EventRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.StaffRoleService;
import com.accelevents.services.StaffService;
import com.accelevents.services.TicketingTypeTicketService;
import com.accelevents.services.UserService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.MeetingOptionsRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.services.MeetingScheduleRepoService;
import com.accelevents.session_speakers.services.MeetingScheduleService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.utils.*;
import com.google.common.collect.ImmutableMap;
import com.google.firebase.messaging.BatchResponse;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.MulticastMessage;
import com.google.firebase.messaging.Notification;
import io.getstream.client.Client;
import io.getstream.core.exceptions.StreamException;
import io.getstream.core.models.Activity;
import io.getstream.core.models.FeedID;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.net.MalformedURLException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.utils.GeneralUtils.convertCommaSeparatedToList;

@Service
public class MeetingScheduleServiceImpl implements MeetingScheduleService {

    private static final Logger log = LoggerFactory.getLogger(MeetingScheduleServiceImpl.class);

    @Autowired
    private MeetingScheduleRepoService meetingScheduleRepoService;

    @Autowired
    private SessionRepoService sessionService;

    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;

    @Autowired
    private TicketingTypeTicketService ticketingTypeTicketService;

    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;

    @Autowired
    private StaffService staffService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private StaffRoleService staffRoleService;
    @Autowired
    private ROStaffRoleService roStaffRoleService;
    @Autowired
    private MeetingOptionsRepoService meetingOptionsRepoService;
    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private ROEventService roEventService;

    private static final String MEETING_NOTIFICATION_DATA = "meetingNotificationData";
    private static final String VERB_JOIN_MEETING = "JOIN_MEETING";

    Client client;
    public MeetingScheduleServiceImpl() {
    }

    @Autowired
    public MeetingScheduleServiceImpl(@Value("${getStream.apiKey}") String streamApiKey,
                                      @Value("${getStream.secretKey}") String streamSecretKey) {
        try {
            client = Client.builder(
                    streamApiKey,
                    streamSecretKey
            ).build();
        } catch (MalformedURLException e) {
            log.error("Get client : ", e);
            throw new NotAcceptableException(NotAcceptableException.FeedExceptionMsg.UNABLE_TO_GET_CLIENT);
        }
    }

    @Override
    public List<AttendeeBookedScheduleDto> getAllBookdedMeetingScheduleByUserIdAndStatusBookedFilterByMonthAndYear(List<Long> userIds, MeetingStatus status, Date startDate, Date endDate, Long eventId) {
        return meetingScheduleRepoService.getAllBookdedMeetingScheduleByUserIdAndStatusBookedFilterByMonthAndYear(userIds, status, startDate, endDate, eventId);
    }

    @Override
    public MeetingSchedule createMeetingSchedule(AttendeeBookedScheduleDto attendeeBookedScheduleDto, Long senderUser, Long receiverUser, Event event, User createdBy) {
        checkTimeSlotAvailable(receiverUser, senderUser, attendeeBookedScheduleDto, event.getEventId());
        log.info("Meeting created by => {} with => {} for event => {} and attendeeBookedScheduleDto => {} ", senderUser, receiverUser, event.getEventId(), attendeeBookedScheduleDto);
        MeetingSchedule meetingSchedule = attendeeBookedScheduleDto.toEntity(senderUser, receiverUser, event.getEventId(), createdBy.getUserId());
        if(event.getEventFormat() != EventFormat.VIRTUAL) {
            meetingSchedule.setLocation(attendeeBookedScheduleDto.getLocation());
        }
        return meetingScheduleRepoService.save(meetingSchedule);
    }

    private void checkTimeSlotAvailable(Long receiverUser, Long senderUser, AttendeeBookedScheduleDto attendeeBookedScheduleDto, Long eventId) {
        Date startTime = TimeZoneUtil.getDateInUTC(attendeeBookedScheduleDto.getStartTime(), attendeeBookedScheduleDto.getEquivalentTimeZone());
        Date endTime = TimeZoneUtil.getDateInUTC(attendeeBookedScheduleDto.getEndTime(), attendeeBookedScheduleDto.getEquivalentTimeZone());

        if (senderUser.equals(receiverUser) || Boolean.TRUE.equals(meetingScheduleRepoService.isMeetingScheduledBetweenDates(receiverUser, startTime, endTime, eventId))) {
            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.MEETING_SCHEDULE_SLOT_NOT_AVAILABLE);
        }
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatusList(Long userId, long eventId, List<MeetingStatus> statusList, List<MeetingOrigin> meetingOriginList,
                                                                                      Date startTime, Pageable pageable) {
        return meetingScheduleRepoService.getAllMeetingScheduleByUserIdAndEventIdAndStatusList(userId, eventId, statusList, meetingOriginList, startTime, pageable);
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepoService.getAllMeetingScheduleByUserIdAndEventId(userId, eventId, meetingOriginList, pageable);
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepoService.getAllMeetingScheduleByUserIdAndEventIdAndStatus(userId, eventId, status, meetingOriginList, pageable);
    }

    @Override
    public void acceptMeetingSchedule(long id) {
        log.info("Accepting meeting request for id => {}", id);
        MeetingSchedule meetingSchedule = getMeetingScheduleById(id);
        validateSenderAndReceiverUserIsAllowedToScheduleMeetings(meetingSchedule.getEventId(), meetingSchedule.getSenderUserId(), meetingSchedule.getReceiverUserId(), meetingSchedule.getOrigin());
        validateMeetingRequest(meetingSchedule.getStatus());
        checkTimeSlotAvailableForAcceptMeeting(meetingSchedule);
        meetingSchedule.setStatus(MeetingStatus.BOOKED);
        this.save(meetingSchedule);
        log.info("Meeting Request is successfully accept by user id => {} for event => {} ", meetingSchedule.getReceiverUserId(), meetingSchedule.getEventId());
    }

    private void checkTimeSlotAvailableForAcceptMeeting(MeetingSchedule meetingSchedule) {
        List<Long> userList = Arrays.asList(meetingSchedule.getSenderUserId(), meetingSchedule.getReceiverUserId());
        if (sessionService.isSessionsBookedByUserAndBetweenStartEndTime(userList, meetingSchedule.getMeetingStartTime(), meetingSchedule.getMeetingEndTime(), meetingSchedule.getEventId())
                || meetingScheduleRepoService.isMeetingScheduledBetweenDatesByNotIdAndUserIdList(meetingSchedule.getId(), userList, meetingSchedule.getMeetingStartTime(), meetingSchedule.getMeetingEndTime(), meetingSchedule.getEventId())) {
            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.MEETING_SCHEDULE_SLOT_NOT_AVAILABLE);
        }
    }

    @Override
    public void rejectMeetingSchedule(long id, Long userId) {
        MeetingSchedule meetingSchedule = getMeetingScheduleById(id);
        validateSenderAndReceiverUserIsAllowedToScheduleMeetings(meetingSchedule.getEventId(), meetingSchedule.getSenderUserId(), meetingSchedule.getReceiverUserId(), meetingSchedule.getOrigin());
        validateMeetingRequest(meetingSchedule.getStatus());
        meetingSchedule.setRejectedBy(userId);
        meetingSchedule.setStatus(MeetingStatus.DECLINED);
        this.save(meetingSchedule);
    }

    private void validateSenderAndReceiverUserIsAllowedToScheduleMeetings(Long eventId, Long senderUser, Long receiverUser, MeetingOrigin origin) {
        Map<Long, Boolean> allowAttendeeToScheduleMeeting = isAllowAttendeeToScheduleMeetingByEventTicketType(roEventService.findEventByEventId(eventId), Arrays.asList(senderUser, receiverUser));
        if(Boolean.FALSE.equals(allowAttendeeToScheduleMeeting.getOrDefault(senderUser, Boolean.FALSE) && allowAttendeeToScheduleMeeting.getOrDefault(receiverUser, Boolean.FALSE)) && !MeetingOrigin.ADMIN_MEETING_REQUEST.equals(origin)){
            throw new NotAcceptableException(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_NOT_ALLOWED_TO_SCHEDULE_MEETING);
        }
    }

    private void validateMeetingRequest(MeetingStatus status) {
        if (MeetingStatus.BOOKED.equals(status)) {
            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.MEETING_ALREADY_BOOKED);
        } else if (MeetingStatus.DECLINED.equals(status)) {
            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.MEETING_ALREADY_DECLINED);
        }
    }

    @Override
    public void save(MeetingSchedule meetingSchedule) {
        meetingScheduleRepoService.save(meetingSchedule);
    }

    private MeetingSchedule getMeetingScheduleById(long id) {
        return meetingScheduleRepoService.getMeetingScheduleById(id);
    }

    @Override
    public MeetingSchedule updateMeetingSchedule(AttendeeBookedScheduleDto attendeeBookedScheduleDto, Long senderUser, Long receiverUser, Long scheduleId, Event event) {
        MeetingSchedule meetingSchedule = meetingScheduleRepoService.findByIdAndEventId(scheduleId, event.getEventId());

        if(!MeetingOrigin.ADMIN_MEETING_REQUEST.equals(meetingSchedule.getOrigin()) && !isAllowAttendeeToScheduleMeetingByEventTicketType(event, senderUser)) {
            throw new NotAcceptableException(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_NOT_ALLOWED_TO_SCHEDULE_MEETING);
        }
        if(!attendeeBookedScheduleDto.isUpdateParticipants()){
            List<ExhibitorDetail> exhibitorDetails = (List<ExhibitorDetail>) staffService.getExhibitorStaffList(meetingSchedule.getExhibitorId(),event).getData();
            Map<Long, ExhibitorDetail> exhibitorDetailById = exhibitorDetails.stream().collect(Collectors.toMap(ExhibitorDetail::getUserId, Function.identity(), (o,n)->n));
            List<Long> eventAdmins = staffService.findAllEventAdminId(event);
            if(exhibitorDetailById.get(meetingSchedule.getSenderUserId()) != null && exhibitorDetailById.get(meetingSchedule.getReceiverUserId()) != null && eventAdmins.contains(senderUser)){
                senderUser = meetingSchedule.getReceiverUserId();
                receiverUser = meetingSchedule.getSenderUserId();
            }
            else if((exhibitorDetailById.get(meetingSchedule.getSenderUserId()) != null && exhibitorDetailById.get(meetingSchedule.getReceiverUserId()) == null && (exhibitorDetailById.get(senderUser) != null))
                    || (exhibitorDetailById.get(meetingSchedule.getSenderUserId()) != null && (eventAdmins.contains(senderUser) && !senderUser.equals(meetingSchedule.getReceiverUserId())))
                    || (exhibitorDetailById.get(meetingSchedule.getSenderUserId()) != null && exhibitorDetailById.get(meetingSchedule.getReceiverUserId()) != null && (senderUser.equals(meetingSchedule.getSenderUserId())))){
                senderUser= meetingSchedule.getSenderUserId();
                receiverUser= meetingSchedule.getReceiverUserId();
            }
            else {
                senderUser = meetingSchedule.getReceiverUserId();
                receiverUser = meetingSchedule.getSenderUserId();
            }
        }

        if(!MeetingOrigin.ADMIN_MEETING_REQUEST.equals(attendeeBookedScheduleDto.getOrigin()) || (MeetingOrigin.ADMIN_MEETING_REQUEST.equals(attendeeBookedScheduleDto.getOrigin()) && isMeetingTimeChanged(meetingSchedule, attendeeBookedScheduleDto))) {
            checkTimeSlotAvailable(receiverUser, senderUser, attendeeBookedScheduleDto, event.getEventId());
        }
        log.info("Meeting updated by => {} with => {} for event => {} and updated attendeeBookedScheduleDto => {} ", senderUser, receiverUser, event.getEventId(), attendeeBookedScheduleDto);
        MeetingSchedule meetingScheduleEntity = attendeeBookedScheduleDto.toEntityForUpdate(senderUser, receiverUser, meetingSchedule);

        if(event.getEventFormat() != EventFormat.VIRTUAL) {
            meetingScheduleEntity.setLocation(attendeeBookedScheduleDto.getLocation());
        }
        return meetingScheduleRepoService.save(meetingScheduleEntity);
    }

    @Override
    public void cancelMeetingSchedule(long id, boolean sendPushNotification, User currentUser) {
        MeetingSchedule meetingSchedule = getMeetingScheduleById(id);
        meetingSchedule.setStatus(MeetingStatus.CANCEL);
        meetingScheduleRepoService.save(meetingSchedule);
        EventNameLookUpDto  eventNameLookUpDto = new EventNameLookUpDto(meetingSchedule.getEventId(), meetingSchedule.getEvent().getName(), meetingSchedule.getEvent().getEventURL());
        Map<Long, User> userById = roUserService.getListOfUsersByUserIds(Arrays.asList(meetingSchedule.getSenderUserId(), meetingSchedule.getReceiverUserId())).stream().collect(Collectors.toMap(User::getUserId, Function.identity()));
        if (sendPushNotification) {
            String meetingStartTime = StringUtils.isBlank(meetingSchedule.getEquivalentTimeZone()) ? DateUtils.getDateString(meetingSchedule.getMeetingStartTime(), Constants.DATE_FORMAT_WITH_AM_PM)  : TimeZoneUtil.getDateInLocal(meetingSchedule.getMeetingStartTime(),meetingSchedule.getEquivalentTimeZone(), Constants.DATE_FORMAT_WITH_AM_PM);

            sendMobilePushNotification(meetingStartTime, userById.get(meetingSchedule.getSenderUserId()), eventNameLookUpDto, currentUser, id, Constants.EMPTY_JSON_BODY, Constants.MEETING_CANCEL_TITLE);
            sendMobilePushNotification(meetingStartTime, userById.get(meetingSchedule.getReceiverUserId()), eventNameLookUpDto, currentUser, id, Constants.EMPTY_JSON_BODY, Constants.MEETING_CANCEL_TITLE);

            //Send Notification to both users in web app
            sendWebPushNotificationToWeb(meetingStartTime, eventNameLookUpDto, id, userById.get(meetingSchedule.getSenderUserId()), currentUser, Constants.EMPTY_JSON_BODY, Constants.MEETING_CANCEL_TITLE);
            sendWebPushNotificationToWeb(meetingStartTime, eventNameLookUpDto, id, userById.get(meetingSchedule.getReceiverUserId()), currentUser, Constants.EMPTY_JSON_BODY, Constants.MEETING_CANCEL_TITLE);
        }
    }

    @Override
    public Page<MeetingSchedule> getAllCreatedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepoService.getAllCreatedMeetingScheduleByUserIdAndEventIdAndStatus(userId, eventId, status, meetingOriginList, pageable);
    }

    @Override
    public void acceptRejectedMeetingSchedule(long id) {
        MeetingSchedule meetingSchedule = getMeetingScheduleById(id);
        if (MeetingStatus.DECLINED.equals(meetingSchedule.getStatus())) {
            checkTimeSlotAvailableForAcceptMeeting(meetingSchedule);
            meetingSchedule.setStatus(MeetingStatus.BOOKED);
            this.save(meetingSchedule);
        } else {
            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.NOT_A_REJECTED_MEETING);
        }
    }

    @Override
    public Page<MeetingSchedule> getAllRejectedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable) {
        return meetingScheduleRepoService.getAllRejectedMeetingScheduleByUserIdAndEventIdAndStatus(userId, eventId, status, meetingOriginList, pageable);
    }

    @Override
    public long getTotalMeetingBooked(Long eventId) {
        return meetingScheduleRepoService.getTotalMeetingBooked(eventId, MeetingOrigin.EXPO_COMP_REPRESENTATIVE);
    }

    @Override
    public long getTotalMeetingBookedInExhibitor(Long eventId, Long exhibitorId) {
        return meetingScheduleRepoService.getTotalMeetingBookedInExhibitor(eventId, MeetingOrigin.EXPO_COMP_REPRESENTATIVE, exhibitorId);
    }

    @Override
    public long getTotalBookedMeetingOfUserInOrigin(Long eventId, Long userId, MeetingOrigin origin) {
        return meetingScheduleRepoService.getTotalBookedMeetingOfUserInOrigin(eventId, userId, origin);
    }

    @Override
    public void deleteMeetingScheduleByEventId(long id) {
        meetingScheduleRepoService.deleteMeetingScheduleByEventId(id);
    }

    @Override
    public List<Object[]> getBookedMeetingByUserIds(Long eventId, List<Long> userIds) {
        return CollectionUtils.isNotEmpty(userIds) ? meetingScheduleRepoService.getBookedMeetingByUserIds(eventId, userIds) : Collections.emptyList();
    }

    @Override
    public List<IdCountDto> getMeetingRequestSentByUserIds(Long eventId, List<Long> userIds) {
        return CollectionUtils.isNotEmpty(userIds) ? meetingScheduleRepoService.getMeetingRequestSentByUserIds(eventId, userIds) : Collections.emptyList();
    }

    @Override
    public List<IdCountDto> getMeetingRequestReceivedByUserIds(Long eventId, List<Long> userIds) {
        return CollectionUtils.isNotEmpty(userIds) ? meetingScheduleRepoService.getMeetingRequestReceivedByUserIds(eventId, userIds) : Collections.emptyList();
    }

    @Override
    public Page<MeetingSchedule> getAllMeetingSchedulesByEventIdAndOrigin(Long eventId, String meetingOrigin, Pageable pageable, String searchString) {
        return meetingScheduleRepoService.getAllMeetingSchedulesCreatedByAdmin(eventId, meetingOrigin, pageable, searchString);
    }

    @Override
    public MeetingSchedule getMeetingScheduleInfoById(long eventId, long id) {
        return meetingScheduleRepoService.findByIdAndEventId(id, eventId);
    }

    private boolean isMeetingTimeChanged(MeetingSchedule meetingSchedule, AttendeeBookedScheduleDto attendeeBookedScheduleDto) {
        Date meetingStartTime = TimeZoneUtil.getDateInUTC(attendeeBookedScheduleDto.getStartTime(), attendeeBookedScheduleDto.getEquivalentTimeZone());
        Date meetingEndTime = TimeZoneUtil.getDateInUTC(attendeeBookedScheduleDto.getEndTime(), attendeeBookedScheduleDto.getEquivalentTimeZone());
        if(meetingSchedule.getMeetingStartTime().compareTo(meetingStartTime)!=0 || meetingSchedule.getMeetingEndTime().compareTo(meetingEndTime)!=0) {
            return true;
        }
        return false;
    }

    @Override
    public List<Object[]> getMeetingByEventIdAndStatus(String eventUrl, String status){
        return meetingScheduleRepoService.getMeetingByEventIdAndStatus(eventUrl, status);
    }

    @Override
    public boolean isAllowAttendeeToScheduleMeetingByEventTicketType(Event event, Long userId) {
        // if event is allowed attendee to schedule meeting then we will check to allow attendee to schedule meeting by ticket type
        if(virtualEventSettingsRepoService.isAllowAttendeeToScheduleMeeting(event.getEventId())) {
            Optional<User> optionalUser = roUserService.getUserById(userId);
            if(optionalUser.isPresent()) {
                User user = optionalUser.get();
                if (Permissions.ADMIN_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(user, event))) {
                    log.info("Attendee {} is admin or higher role for event {},Allowed  to schedule meeting", userId, event.getEventId());
                    return true;
                }
                // if we found any ticket type purchased for user then we will check to if any tickets have enabled allowMeetingCreation flag at ticket type level
                List<Long> ticketTypeId = eventTicketsRepoService.getAllEventTicketTypeIdsByEventIdUserIdAndNotCanceled(event.getEventId(), userId);
                if (CollectionUtils.isNotEmpty(ticketTypeId)) {
                    log.info("Attendee {} have ticket for event {} that allows meeting scheduling ", userId, event.getEventId());
                    return ticketingTypeTicketService.isUserAllowedToCreateMeetingByTicketTypeIds(ticketTypeId);
                }
                // if no ticket type found for user then we will check to if user have role of higher than attendee role, if we don't found any role then we will don't allow to schedule meeting
                log.info("Attendee {} is neither admin nor holds a ticket in event {} , checking for higher roles than attendee", userId, event.getEventId());
                return staffService.checkUserHasAdminStaffSpeakerExhibitorAccess(event.getEventId(), userId);
            }
        }
        return false;
    }

    /*
    * this is used when we have find user is can create/accept meeting based on their ticket type by given list of user
    * return the Map where key is UserId and value is boolean (show the status of user can create meeting/accept meeting)
     */
    @Override
    public Map<Long, Boolean> isAllowAttendeeToScheduleMeetingByEventTicketType(Event event, List<Long> userList) {

        Map<Long, Boolean> allowedUserToCreateMeetingMap = new HashMap<>();
        // if event is allowed attendee to schedule meeting then we will check to allow attendee to schedule meeting by ticket type
        if(event !=null && CollectionUtils.isNotEmpty(userList) && virtualEventSettingsRepoService.isAllowAttendeeToScheduleMeeting(event.getEventId())) {

            // Fetch user purchased ticket type ID by list of userId, Object[] -> object[0]=UserId, object[1] = ticketTypeId
            List<Object[]> ticketTypeWithUserIdList = eventTicketsRepoService.getAllEventTicketTypeIdsByEventIdUserIdInAndNotCanceled(event.getEventId(), userList);
            Map<Long, List<Long>> userIdWithTicketType = ticketTypeWithUserIdList.stream().collect(Collectors.groupingBy(e -> (Long) e[0], Collectors.mapping(e -> (Long) e[1], Collectors.toList())));

            // fetch ticket type with allowMeetingCreation status by list of ticketTypeIds
            List<Long> allTicketTypeId = ticketTypeWithUserIdList.stream().map(e -> (Long) e[1]).collect(Collectors.toList());
            List<Object[]> allowedTicketList = ticketingTypeTicketService.getTicketTypeMeetingCreationStatusByTicketTypeIds(allTicketTypeId);
            Map<Long, Boolean> allowedTicketMap = allowedTicketList.stream().collect(Collectors.toMap(e -> (Long) e[0], e -> (Boolean) e[1]));

            // fetch user role by event and list of userId
            Map<Long, UserRole> userRoleByEventAndUserIds = staffRoleService.getUserRoleByEventAndUserIds(userList, event);

            // get map of user and their role has admin/speaker/exhibitor access
            Map<Long, Boolean> speakerExhibitorAccessMap = staffService.getUserHasAdminStaffSpeakerExhibitorAccessByUserIds(event.getEventId(), userList);

            for (Long userId : userList) {
                allowedUserToCreateMeetingMap.put(userId, false);
                if (Permissions.ADMIN_OR_HIGHER_ROLE(userRoleByEventAndUserIds.get(userId))) {
                    log.info("Attendee {} is admin or higher role for event {},Allowed  to schedule meeting", userId, event.getEventId());
                    allowedUserToCreateMeetingMap.put(userId, true);
                    continue;
                }
                // if we found any ticket type purchased for user then we will check to if any tickets have enabled allowMeetingCreation flag at ticket type level
                List<Long> ticketTypeId = userIdWithTicketType.get(userId);
                if (CollectionUtils.isNotEmpty(ticketTypeId)) {
                    log.info("Attendee {} have ticket for event {} that allows meeting scheduling ", userId, event.getEventId());
                    allowedUserToCreateMeetingMap.put(userId, checkIsUserAllowedToCreateMeetingByTicketTypeIds(allowedTicketMap, ticketTypeId));
                } else {
                    // if no ticket type found for user then we will check to if user have role of higher than attendee role, if we don't found any role then we will don't allow to schedule meeting
                    log.info("Attendee {} is neither admin nor holds a ticket in event {} , checking for higher roles than attendee", userId, event.getEventId());
                    allowedUserToCreateMeetingMap.put(userId, speakerExhibitorAccessMap.getOrDefault(userId, false));
                }
            }
        }
        return allowedUserToCreateMeetingMap;
    }

    private boolean checkIsUserAllowedToCreateMeetingByTicketTypeIds(Map<Long, Boolean> allowedTicketMap, List<Long> ticketTypeIds) {
        boolean isAllowed=false;
        for (Long ticketTypeId : ticketTypeIds) {
            isAllowed = allowedTicketMap.getOrDefault(ticketTypeId, false);
            if (isAllowed) {
                break;
            }
        }
        return isAllowed;

    }

    @Override
    public AttendeeMeetingCreationConfigDTO getPreScheduleMeetingAndEventTicketMeetingCreationStatus(Event event, Long userId) {
        AttendeeMeetingCreationConfigDTO attendeeMeetingCreationConfigDTO = new AttendeeMeetingCreationConfigDTO();
        attendeeMeetingCreationConfigDTO.setPreScheduleMeetingScheduled(meetingScheduleRepoService.isPreScheduleMeetingScheduledForEventAndUser(event.getEventId(), userId));
        attendeeMeetingCreationConfigDTO.setAllowMeetingCreationOnEventTicket(isAllowAttendeeToScheduleMeetingByEventTicketType(event, userId));
        return attendeeMeetingCreationConfigDTO;
    }


    @Override
    public List<MeetingSchedule> findScheduledMeetingOfABooth(Long eventId, Long expoId, List<Long> receiverId,List<MeetingStatus> status,
                                                              List<String> location) {
        return meetingScheduleRepoService.findScheduledMeetingOfABooth(eventId, expoId, receiverId, status, location);
    }

    @Override
    public Optional<MeetingSchedule> findByExhibitorIdAndIdAndEventId(Long expoId, Long meetingId, Long eventId) {
        return meetingScheduleRepoService.findByExhibitorIdAndIdAndEventId(expoId, meetingId, eventId);
    }

    @Override
    @Async
    public void remindMeetingScheduleBeforeMeetingStart(String customEventId) {
        List<Long> eventIds= new ArrayList<>();
        if(StringUtils.isNotEmpty(customEventId)){
            try{
                eventIds = GeneralUtils.convertCommaSeparatedToListLong(customEventId);
            }
            catch (Exception ignore){
                log.info("Invalid event id: {}",customEventId);
            }
        }
        log.info("remindMeetingScheduleBeforeMeetingStart started");
        List<EventNameLookUpDto> eventNameLookUps = populateEventObject(eventRepository.findAllActiveEvents());
        log.info("Number of active events: {}", eventNameLookUps.size());
        Map<Long,EventNameLookUpDto> eventById = eventNameLookUps.stream().collect(Collectors.toMap(EventNameLookUpDto::getEventId, Function.identity()));
        List<Long> activeEventIds = eventNameLookUps.stream().map(EventNameLookUpDto::getEventId).collect(Collectors.toList());
        Map<Long,MeetingOptions> meetingOptionsMap = meetingOptionsRepoService.findFutureScheduledMeetings(activeEventIds).stream().collect(Collectors.toMap(MeetingOptions::getEventId, Function.identity()));
        List<MeetingSchedule> scheduledMeetingLists = meetingScheduleRepoService.findAcceptedScheduledMeetingByEventIdAndStatus(activeEventIds);
        List<Long> userIds = scheduledMeetingLists.stream().flatMap(c -> Stream.of(c.getReceiverUserId(), c.getSenderUserId())).distinct().collect(Collectors.toList());
        Map<Long,List<MeetingSchedule>> meetings = scheduledMeetingLists.stream().collect(Collectors.groupingBy(MeetingSchedule::getEventId));
        Map<Long, User> userMap = roUserService.getListOfUsersByUserIds(userIds).stream().collect(Collectors.toMap(User::getUserId, Function.identity()));

        ExecutorService executorService = Executors.newFixedThreadPool(10);

        for(Map.Entry<Long, List<MeetingSchedule>> entry: meetings.entrySet()){
            Boolean isCustomEventFound = Boolean.FALSE;
            Map<Long,ExhibitorStaffDTO> exhibitorDetailsById = new HashMap<>();
            List<Long> exhibitorStaffUserId = new ArrayList<>();
            Long eventId = entry.getKey();
            List<MeetingSchedule> scheduleMeetings = entry.getValue();
            Map<Long,List<Long>> staffByExpo = new HashMap<>();
            if(!CollectionUtils.isEmpty(eventIds) && eventIds.contains(eventId)){
                isCustomEventFound = Boolean.TRUE;
                List<ExhibitorStaffDTO> exhibitorStaffs = staffService.findAllExhibitorUserIdsByEventId(eventId);
                exhibitorStaffUserId = exhibitorStaffs.stream().map(ExhibitorStaffDTO::getUserId).collect(Collectors.toList());
                staffByExpo = exhibitorStaffs.stream().collect(Collectors.groupingBy(ExhibitorStaffDTO::getExhibitorId,Collectors.mapping(ExhibitorStaffDTO::getUserId,Collectors.toList())));
                exhibitorDetailsById = exhibitorStaffs.stream().collect(Collectors.toMap(ExhibitorStaffDTO::getExhibitorId, Function.identity(), (o,n) ->o));
            }

            Long meetingReminderTime = 5L;
            String meetingReminderContext = Constants.DEFAULT_MEETING_REMINDER_CONTEXT;
            if (meetingOptionsMap.get(eventId) != null) {
                meetingReminderTime= (NumberUtils.isNumberGreaterThanZero(meetingOptionsMap.get(eventId).getMeetingReminderTime())) ? meetingOptionsMap.get(eventId).getMeetingReminderTime() : 5L;
                meetingReminderContext= (StringUtils.isNotBlank(meetingOptionsMap.get(eventId).getMeetingReminderContext())) ? meetingOptionsMap.get(eventId).getMeetingReminderContext() : Constants.DEFAULT_MEETING_REMINDER_CONTEXT;
            }
            Map<Long, Long> meetingStartsIn = new HashMap<>();
            List<MeetingSchedule> filteredMeetings = filterMeetings(scheduleMeetings, meetingReminderTime, meetingStartsIn);

            for (MeetingSchedule meetingSchedule : filteredMeetings) {
                log.info("Sending meeting reminder for meeting id {} and event id {}", meetingSchedule.getId(), eventId);
                User receiverUser = userMap.get(meetingSchedule.getReceiverUserId());
                User senderUser = userMap.get(meetingSchedule.getSenderUserId());
                JSONObject firstUserRawContent = new JSONObject();
                JSONObject secondUserRawContent = new JSONObject();
                
                Long meetingStartsInTime = (meetingStartsIn.get(meetingSchedule.getId()) != null) ? meetingStartsIn.get(meetingSchedule.getId()) : meetingReminderTime;
                String meetingReminderContextForFirstUser = prepareMessage(meetingReminderContext,receiverUser, meetingSchedule, meetingStartsInTime, firstUserRawContent, isCustomEventFound, exhibitorDetailsById, staffByExpo, exhibitorStaffUserId, Boolean.TRUE);
                String meetingReminderContextForSecondUser = prepareMessage(meetingReminderContext,senderUser, meetingSchedule, meetingStartsInTime, secondUserRawContent, isCustomEventFound, exhibitorDetailsById, staffByExpo, exhibitorStaffUserId, Boolean.FALSE);

                executorService.submit(()->this.sendMessage(meetingReminderContextForFirstUser, meetingReminderContextForSecondUser, senderUser, receiverUser, eventById.get(eventId), meetingSchedule.getId(), firstUserRawContent.toString(), secondUserRawContent.toString(),Constants.MEETING_REMINDER_SUBJECT, null));
            }
        }
    }

    private List<EventNameLookUpDto> populateEventObject(List<Object[]> eventObjects) {
        List<EventNameLookUpDto> eventList = new ArrayList<>();
        for (Object[] eventObject : eventObjects) {
            EventNameLookUpDto eventObj = new EventNameLookUpDto();
            eventObj.setEventId(((BigInteger) eventObject[0]).longValue());
            eventObj.setEventURL((String) eventObject[1]);

            eventList.add(eventObj);
        }
        return eventList;
    }

    private List<MeetingSchedule> filterMeetings(List<MeetingSchedule> scheduleMeetings, Long meetingReminderTime, Map<Long, Long> meetingStartsIn) {
        List<MeetingSchedule> filteredMeetings = new ArrayList<>();

        for (MeetingSchedule meetingSchedule : scheduleMeetings) {
            Date meetingStartTime = meetingSchedule.getMeetingStartTime();
            Date currentTime = new Date();
            long diff = meetingStartTime.getTime() - currentTime.getTime();
            long diffMinutes = TimeUnit.MILLISECONDS.toMinutes(diff);
            if ((diffMinutes > 0 && diffMinutes <= meetingReminderTime)) {
                filteredMeetings.add(meetingSchedule);
                meetingStartsIn.put(meetingSchedule.getId(), diffMinutes);
            }
        }
        if(!filteredMeetings.isEmpty()){
            filteredMeetings.forEach(e -> e.setReminderSent(Boolean.TRUE));
            meetingScheduleRepoService.saveAll(filteredMeetings);
        }
        log.info("Total {} meetings found for reminder", filteredMeetings.size());
        return filteredMeetings;
    }

    private String prepareMessage(String meetingReminderContext, User receiverUser, MeetingSchedule meetingSchedule, Long reminderTime, JSONObject userRawContent, Boolean isCustomEventFound, Map<Long, ExhibitorStaffDTO> exhibitorDetails, Map<Long, List<Long>> staffByExpo, List<Long> exhibitorStaffUserId, Boolean isSenderUser) {
        String rawContent = meetingReminderContext;
        String name = "";
        if(StringUtils.isEmpty(meetingReminderContext)) {
            meetingReminderContext = Constants.DEFAULT_MEETING_REMINDER_CONTEXT;
        }
        if(Boolean.TRUE.equals(isCustomEventFound && !staffByExpo.isEmpty()
                && meetingSchedule.getExhibitorId() != null && meetingSchedule.getExhibitorId() > 0
                && exhibitorStaffUserId.contains(((isSenderUser)?meetingSchedule.getReceiverUserId():meetingSchedule.getSenderUserId()))
                && staffByExpo.get(meetingSchedule.getExhibitorId()) != null)
                && (isSenderUser && staffByExpo.get(meetingSchedule.getExhibitorId()).contains(meetingSchedule.getReceiverUserId()) && !staffByExpo.get(meetingSchedule.getExhibitorId()).contains(meetingSchedule.getSenderUserId())
                    || !isSenderUser && staffByExpo.get(meetingSchedule.getExhibitorId()).contains(meetingSchedule.getSenderUserId()) && !staffByExpo.get(meetingSchedule.getExhibitorId()).contains(meetingSchedule.getReceiverUserId()))){

                name = exhibitorDetails.get(meetingSchedule.getExhibitorId()).getBoothName();
        }
        if (StringUtils.isEmpty(name)){
            name = this.getUserName(receiverUser);
        }
        String time = getTimeFromDate(meetingSchedule.getMeetingStartTime(), meetingSchedule.getEquivalentTimeZone(), true);
        
        meetingReminderContext = meetingReminderContext.replace(Constants.DEFAULT_MEETING_REMINDER_MACRO_PARTICIPANT_NAME, name);
        meetingReminderContext = meetingReminderContext.replace(Constants.DEFAULT_MEETING_REMINDER_MACRO_MEETING_TIME, time);
        String location = Constants.STRING_EMPTY;
        
        if(StringUtils.isNotEmpty(meetingSchedule.getLocation())){
            location = meetingSchedule.getLocation();
            meetingReminderContext = meetingReminderContext.replace(Constants.DEFAULT_MEETING_REMINDER_MACRO_MEETING_LOCATION, location);
        }
        else {
            if(meetingReminderContext.contains(" at "+Constants.DEFAULT_MEETING_REMINDER_MACRO_MEETING_LOCATION)){
                meetingReminderContext = meetingReminderContext.replace(" at "+Constants.DEFAULT_MEETING_REMINDER_MACRO_MEETING_LOCATION, Constants.STRING_EMPTY);
            }
            else {
                meetingReminderContext = meetingReminderContext.replace(Constants.DEFAULT_MEETING_REMINDER_MACRO_MEETING_LOCATION, location);
            }
        }
        try{
            userRawContent.put(Constants.NAME_SMALL, name);
            userRawContent.put(Constants.TIME_SMALL, getTimeFromDate(meetingSchedule.getMeetingStartTime(), meetingSchedule.getEquivalentTimeZone(), false));
            userRawContent.put(Constants.LOCATION_SMALL, location);
            userRawContent.put(Constants.RAW_BODY, rawContent);
            userRawContent.put("meetingStartsIn", reminderTime);
            userRawContent.put("meetingTimezone", meetingSchedule.getEquivalentTimeZone());

        }
        catch (Exception exception){
            log.info("Error while parsing date to time", exception);
        }
        

        return meetingReminderContext + " starts in " + reminderTime + ((reminderTime > 1)?" minutes":" minute");
    }

    private String getTimeFromDate(Date date, String equivalentTimeZone, boolean timezoneRequired) {
        try{
            if(timezoneRequired){
                return StringUtils.isBlank(equivalentTimeZone) ? DateUtils.getDateString(date, "hh:mm a") + " (UTC) "  : TimeZoneUtil.getDateInLocal(date,equivalentTimeZone, "hh:mm a") + " (" + equivalentTimeZone + ") ";
            }
            else {
                return StringUtils.isBlank(equivalentTimeZone) ? DateUtils.getDateString(date, "hh:mm a") : TimeZoneUtil.getDateInLocal(date,equivalentTimeZone, "hh:mm a");
            }
        }
        catch (Exception exception){
            log.error("Error while parsing date to time", exception);
        }
        return date.toString();
    }

    @Override
    public String getUserName(User user) {
        String name = Constants.STRING_EMPTY;
        if(user != null) {
            if(!StringUtils.isEmpty(user.getFirstName())){
                name = name + user.getFirstName() + Constants.STRING_BLANK;
            }
            if(!StringUtils.isEmpty(user.getLastName())){
                name = name + user.getLastName();
            }
        }
        return name;
    }

    @Override
    public void sendMessage(String meetingReminderContextForFirstUser, String meetingReminderContextForSecondUser, User senderUser, User receiverUser, EventNameLookUpDto eventNameLookUpDto, long meetingId, String firstUserRawContent, String secondUserRawContent, String title, Long loggedInUserId) {
        //send push notification to both users in mobile app
        if(loggedInUserId == null || !loggedInUserId.equals(senderUser.getUserId())){
            sendMobilePushNotification(meetingReminderContextForFirstUser, senderUser, eventNameLookUpDto, receiverUser, meetingId, firstUserRawContent, title);
            sendWebPushNotificationToWeb(meetingReminderContextForFirstUser, eventNameLookUpDto, meetingId, senderUser, receiverUser, firstUserRawContent, title);
        }
        if(loggedInUserId == null || !loggedInUserId.equals(receiverUser.getUserId())){
            sendMobilePushNotification(meetingReminderContextForSecondUser, receiverUser, eventNameLookUpDto, senderUser, meetingId, secondUserRawContent, title);
            sendWebPushNotificationToWeb(meetingReminderContextForSecondUser, eventNameLookUpDto, meetingId, receiverUser, senderUser, secondUserRawContent, title);
        }
    }

    private void sendWebPushNotificationToWeb(String meetingReminderContext, EventNameLookUpDto eventNameLookUpDto, long meetingId, User user, User receiverUser, String rawContent, String title) {
        if(user != null){
            try{
                Activity activity = Activity.builder()
                        .actor(Constants.SINGLE_WHITE_SPACE)
                        .verb(VERB_JOIN_MEETING)
                        .time(new Date())
                        .object(String.valueOf(eventNameLookUpDto.getEventId()))
                        .foreignID("foreign_".concat(String.valueOf(eventNameLookUpDto.getEventId())))
                        .extra(new ImmutableMap.Builder<String, Object>()
                                .put(MEETING_NOTIFICATION_DATA, new ImmutableMap.Builder<String, Object>()
                                        .put(Constants.EVENT_ID, eventNameLookUpDto.getEventId())
                                        .put(Constants.MODULE_TYPE_ID, meetingId)
                                        .put(Constants.MODULE_TYPE, Constants.MEETING.toUpperCase())
                                        .put(Constants.SMALL_CASE_TITLE,title)
                                        .put(Constants.MESSAGE_LOWER, meetingReminderContext)
                                        .put(Constants.TARGET_AUDIENCE_TYPE, TargetAudienceType.All.name())
                                        .put(Constants.NOTIFICATION_ICON, Constants.STRING_EMPTY)
                                        .put(Constants.RAW_BODY_CONTENT,rawContent)
                                        .put(Constants.PROFILE_IMAGE, (receiverUser != null && StringUtils.isNotEmpty(receiverUser.getPhoto()) ? receiverUser.getPhoto() : Constants.STRING_EMPTY))
                                        .put(Constants.NAME_SMALL, this.getUserName(receiverUser))
                                        .build())
                                .build())
                        .build();

                getFeedIdsArray(String.valueOf(user.getUserId()),String.valueOf(eventNameLookUpDto.getEventId())).forEach(feedIDS -> {
                    try {
                        client.batch().addToMany(activity, feedIDS).join();
                        log.info("Notification sent to user {} for event {} for meeting {}", user.getUserId(), eventNameLookUpDto.getEventId(), meetingId);
                    } catch (StreamException e) {
                        log.error("Exception occurs when create feed in addToMany batch", e);
                    }
                });
            }
            catch (Exception exception){
                log.info("Error while sending web push notification", exception);
            }
        }
    }

    private ArrayList<FeedID[]> getFeedIdsArray(String userIds, String eventId) {
        FeedID[] feedIdArray = new FeedID[1];
        feedIdArray[0] = new FeedID("notification", userIds.concat("_").concat(eventId));
        return new ArrayList<>(Collections.singleton(feedIdArray));
    }

    private void sendMobilePushNotification(String meetingReminderContext, User user, EventNameLookUpDto eventNameLookUpDto, User receiverUser, Long meetingId, String rawContent, String title) {
        if (user != null && StringUtils.isNotEmpty(user.getPushNotificationToken())) {
            try{
                List<String> token = convertCommaSeparatedToList(user.getPushNotificationToken());
                List<List<String>> partitionTokensList = ListUtils.partition(token, 100);
                if(CollectionUtils.isNotEmpty(partitionTokensList)){
                    for (List<String> tokenList : partitionTokensList) {
                        MulticastMessage message = MulticastMessage.builder()
                                .setNotification(Notification.builder().setTitle(Constants.MEETING_REMINDER_SUBJECT).setBody(meetingReminderContext).build())
                                .putData(Constants.CONTENT, title)
                                .putData(Constants.BODY, meetingReminderContext)
                                .putData(Constants.RAW_BODY_CONTENT, rawContent)
                                .putData(Constants.EVENT_ID, String.valueOf(eventNameLookUpDto.getEventId()))
                                .putData(Constants.EVENT_URL, eventNameLookUpDto.getEventURL())
                                .putData(Constants.NOTIFICATION_ICON, Constants.STRING_EMPTY)
                                .putData(Constants.MODULE_TYPE_ID, String.valueOf(meetingId))
                                .putData(Constants.MODULE_TYPE, Constants.MEETING.toUpperCase())
                                .putData(Constants.PROFILE_IMAGE, (receiverUser != null && StringUtils.isNotEmpty(receiverUser.getPhoto()) ? receiverUser.getPhoto() : Constants.STRING_EMPTY))
                                .putData(Constants.NAME_SMALL, this.getUserName(receiverUser))
                                .addAllTokens(tokenList)
                                .build();
                        BatchResponse response = FirebaseMessaging.getInstance().sendEachForMulticast(message);
                        log.info("Successfully sent message: {}", response);
                    }
                }
            }
            catch (Exception exception){
                log.error("Error while sending push notification", exception);
            }
        }
    }
}
