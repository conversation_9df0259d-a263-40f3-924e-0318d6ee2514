package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.session_speakers.dto.BreakoutRoomResponseDto;
import com.accelevents.session_speakers.dto.MeetingAttendeeDto;
import com.accelevents.session_speakers.dto.BreakoutRoomDto;

import java.util.List;

public interface BreakoutRoomService {

    BreakoutRoomResponseDto createBreakoutRoom(Long sessionId, Event event, Long sessionBreakoutRoomCount);

    boolean checkIsBreakoutRoomAvailable(Long sessionId, Long breakoutRoomId, Event event);

    void updateBreakoutRoom(BreakoutRoomDto breakoutRoomDto, Long sessionId, Event event);

    List<BreakoutRoomDto> getAllBreakoutRoom(Long sessionId, Event event);

    void deleteBreakoutRoom(Long id);

    MeetingAttendeeDto joinBreakoutRoom(Long sessionId, Long breakoutRoomId, Event event, User user);
}
