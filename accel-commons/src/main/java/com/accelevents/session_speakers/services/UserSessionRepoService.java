package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketTypeFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.messages.TicketType;
import com.accelevents.session_speakers.dto.AttendeeSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.RegisterdHolderUsers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

public interface UserSessionRepoService {

    List<UserSession> findByEventTicketIdAndSessionIdAndEventId(Long eventTicketId, Long sessionId, Long eventId);

    void deleteAll(List<UserSession> userSessions);

    UserSession save(UserSession userSession);

    BigInteger countByEventTicketId(Long eventTicketId);

    boolean isUserAvailableStateForTicketTypesIn(Long eventId, Long userId, Long sessionId, EnumUserSessionStatus state, List<Long> ticketTypeIds);

    boolean isAnyUsersRegisteredTicketTypesIn(Long eventId, Long sessionId, EnumUserSessionStatus state, List<Long> ticketTypeIds);

    boolean isUserAvailableState(Long eventId, Long userId, Long sessionId, EnumUserSessionStatus state);

    boolean isUserAvailableStateIn(Long eventId, Long userId, Long sessionId, List<EnumUserSessionStatus> state);

    int getCheckInUserCountBySession(Long sessionId);

    List<Long> findByEventIdAndSessionIdAndSateAndNotUserIdAndTicketTypesIn(Long eventId, Long sessionId, EnumUserSessionStatus state, List<Long> userIds, List<Long> ticketTypeIds);

    void updateUserState(EnumUserSessionStatus state, Long eventId, Long sessionId, List<Long> userIds);

    void updateUserCheckInState(EnumUserSessionStatus state, Long eventId, Long sessionId, List<Long> userIds);

    List<IdCountDto> registerCountBySessionIdsAndSessionStatus(List<Long> sessionIds,EnumUserSessionStatus sessionStatus);

    List<IdCountDto> countSessionAttendeeBySessionIdIn(List<Long> sessionIds);

    List<IdCountDto> countSessionAttendeeByEventAndUserIds(List<Long> eventIds, List<Long> userIds);

    long countSessionAttendeeBySessionId(Long sessionId);

    List<RegisterdHolderUsers> findRegisteredUserBySessionId(Long sessionId);

    List<RegisterdHolderUsers> getRegisteredUserBySessionId(Long sessionId);

    Page<RegisterdHolderUsers> findRegisteredUserBySessionIdPage(Long sessionId, Pageable pageable);

    List<RegisterdHolderUsers> findRegisteredUserSessionBySessionId(Long sessionId, List<Long> ticketTypeIds, List<TicketType> ticketTypes,List<EnumUserSessionStatus> checkInStatus, List<EnumUserSessionStatus> sessionStatus);

    List<AttendeeSession> findAttendeeSessionsByUserId(Long eventId, Long userId, String searchString);

    List<UserSession> findRegisteredEventTicketIdByUserAndSessionId(Long userId, List<Long> sessionIds);

    BigInteger registerCountBySessionId(Long sessionId,EnumUserSessionStatus enumUserSessionStatus);

    List<Long> findUserIdsByEventIdAndSessionId(Long sessionId, long eventId);

    List<Long> getEventTicketIdsByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId);

    List<Long> getTicketingTypeIdByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId);

    List<Long> findEventTicketIdByUserIdAndEventId(Long userId, Long eventId);

    List<Long> findByEventIdAndUserId(Long eventId, Long userId);

    Long userSessionCount(Long eventId, Long userId);

    void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status);

    List<Long> getEventTicketIdsByEventIdAndPurchaserUserIdAndSessionId(Long eventId, Long userId, Long sessionId);

    List<UserSession> findAllBySessionId(long sessionId);

    List<UserSession> findUserByUserAndSessionIdAndCheckedInAvailable(Long userId, Long sessionId);

    List<Object[]> findAllAttendeeSessionsByEventId(Long eventId);

    List<UserSession> findBySessionIdAndUserIdAndEventId(long id, Long userId, Long eventId);

    List<AttendeeSession> findAttendeeNetworkingSessionsByUserId(long eventId, long userId, EnumSessionFormat meetUp);

    void updateAllByUserId(Long userId);

    Long getRegisteredSessionCountForUserBySessionFormat(long eventId, Long userId, EnumSessionFormat sessionFormat);

    void deleteByEventId(Long eventId);

    Integer getUserRegisteredSessionBySession(Long sessionId, Event event);

    Map<Long, Integer> getUserRegisteredSessionsBySessionIds(List<Long> sessionIds, Event event);

    Integer countBySessionIdsAndUserId(List<Long> sessionIds, Long userId, Long eventId);

    Integer countSessionAttendeeByEventId(Long eventId);

    List<Object[]> getCheckInSessionByUserIdAndSessionId(List<Long> sessionIds, Long userId);

    List<UserSession> findByUserIdAndEventId(Long userId, long eventId);

    List<UserSession> findByUserIdAndEventIdAndEventTicketId(Long userId, long eventId, Long eventTicketId);

   void  saveAll(List<UserSession> userSessions);

    Long countUserSessionByCheckInStatusAndSessionId(Long sessionId);

    Long countUserSessionBySessionStatusAndSessionId(Long sessionId);

    List<Long> getAddToCalendarEventSessionByUserIdAndSessionId(List<Long> sessionIds, Long userId, Long eventId);

    List<IdCountDto> countSessionAttendeeByEventTicketIdAndSessionIdIn(List<Long> sessionIds);

    long countSessionAttendeeByEventTicketIdAndSessionId(Long sessionId);

    List<IdCountDto> countSessionAttendeeByNetworkingSessionIdIn(List<Long> sessionIds);

    List<Long> findUserByEventIdAndSessionIdAndCheckInStatus(Long sessionId, Long eventId);
    Long countUserSessionByEventIdAndCheckInStatusOrCheckInTimeIsNull(Long eventId);
    Long countUserRegisteredByEventIdAndEventTicketIdIsNotNull(Long eventId);

    Long findUserSessionCountByEventIdAndSessionIdAndCheckInStatus(long sessionId, long eventId);

    Long findUserSessionCountByEventIdAndSessionIdAndCheckInStatusAndTicketTypeFormat(long sessionId, long eventId, List<TicketTypeFormat> ticketTypeFormats);

    Long findUserSessionCountByEventIdAndSessionIdAndCheckInStatusAndUserIdIn(long sessionId, long eventId, List<Long> allStaffAndAdminUserIds);

    Page<RegisterdHolderUsers> findRegisteredUserSessionBySessionIdWithPagination(Long sessionId, List<Long> ticketTypeIds, List<TicketType> ticketTypes, List<EnumUserSessionStatus> checkInStatus, List<EnumUserSessionStatus> userSessionStatuses, Pageable of);

    Page<RegisterdHolderUsers> findRegisteredUserSessionBySessionIdWithPaginationAndSearch(Long sessionId, List<Long> ticketTypeIds, List<TicketType> ticketTypes, List<EnumUserSessionStatus> checkInStatus, List<EnumUserSessionStatus> userSessionStatuses, Pageable of, String search);

    Long findUserSessionCountByEventIdAndSessionIdAndUserIdIn(long id, long eventId, List<Long> allStaffAndAdminUserIds);

    Long findUserSessionCountByEventIdAndSessionIdAndUserIdInAndTicketTypeFormat(long id, long eventId, List<Long> allStaffAndAdminUserIds, List<TicketTypeFormat> ticketTypeFormats);

    List<Object[]> findByEventIdAndEventTicketIdInAndUserIdInAndCheckInStatus(long eventId, List<Long> eventTicketIdsList, List<Long> userIdsList, EnumUserSessionStatus checkInAvailable);

    UserSession findBySessionIdAndUserIdAndEventIdAndTicketId(long id, Long userId, long eventId, Long eventTicketId);

    UserSession findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(long id, Long userId, long eventId, Long eventTicketId);

    boolean isUserRegisteredInSession(long id, Long userId, Event event);

    List<IdCountDto> countBySessionIdInAndEventIdAndTicketIdIsNotNull(List<Long> sessionIds, long eventId);

    Integer countBySessionIdAndTicketIdIsNotNullAndEventId(Long sessionId, long eventId);

    List<IdCountDto> countSessionBookmarkedAttendeeBySessionIdsAndEventId(List<Long> sessionIds, long eventId);

    List<RegisterdHolderUsers> findRegisteredUsersBySessionIdAndRegisteredStatus(long sessionId, EnumUserSessionStatus sessionStatus);

    List<UserSession> findRegisteredUserByEventTicketIdAndSessionIdAndEventId(Long eventTicketId, Long sessionId, Long eventId,EnumUserSessionStatus sessionStatus);

    List<Long> getRegisteredSessionIdsByEventIdAndUserId(long eventId, Long userId);

    List<Long> getBookmarkedSessionIdsByEventIdAndUserId(long eventId, Long userId);

    List<Long> getAddToCalendarSessionByUserIdAndSessionId(long eventId, Long userId);

    void delete(UserSession userSession);

    List<UserSession> findAllByEventIdAndUserIdAndSessionStatusesAndCapacity(Session session, Event event, Long userId, boolean checkCapacity);

    List<Object[]> getRegisteredSessionCountForUserByTracks(User user, Event event, List<Long> sessionTracksIds);

    List<UserSession> findBySessionIdAndUserIdAndEventIdAndSessionStatus(long id, Long userId, Long eventId, EnumUserSessionStatus sessionStatus);

    List<Long> getWaitListedSessionIdsByEventIdAndUserId(long eventId, Long userId);

    List<UserSession> findAllBySessionIdAndIsWaitlisted(long sessionId);

    List<UserSession> findBySessionIdAndEventIdAndUserIdIn(Long sessionId, Long eventId, List<Long> userIds);

}

