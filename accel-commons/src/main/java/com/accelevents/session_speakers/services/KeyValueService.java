package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumKeyValueType;
import com.accelevents.domain.session_speakers.KeyValue;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.session_speakers.dto.KeyValueDTO;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface KeyValueService {


    Long createKeyValue(KeyValueDTO keyValueDTO, Event event);

    void updateKeyValue(Long id, KeyValueDTO keyValueDTO, Event event);

    void deleteKeyValue(Long id, Event event);

    DataTableResponse getKeyValueList(Event event, String searchString, String expand, Pageable pageable, EnumKeyValueType type);

	List<KeyValue> findAllByIds(List<Long> keyValueIds);

    List<KeyValueDTO>  getKeyValueInfoList(Event event, EnumKeyValueType type);

    void updateKeyValuePosition(Long keyValueId, Long topKeyValueId, Long topBottomKeyValueId, Event event);

    KeyValue getByNameAndEventIdAndType(String name, Event event, EnumKeyValueType type);

    KeyValueDTO getKeyValueById(Event event,Long id);
}
