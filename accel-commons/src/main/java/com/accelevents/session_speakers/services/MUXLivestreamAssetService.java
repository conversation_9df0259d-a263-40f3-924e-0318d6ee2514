package com.accelevents.session_speakers.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.EventStreaming;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.MuxJwtDto;
import com.accelevents.session_speakers.dto.*;

import javax.transaction.Transactional;
import java.util.List;

public interface MUXLivestreamAssetService {

    void saveWithSequence(MUXLivestreamAssetDetails assetDetails);

    @Transactional(rollbackOn = {Exception.class})
    void updateWithSequence(Long id, Long topId, Long topBottomId, Event event, User user);

    List <MUXLivestreamAssetDetails> findBySessionIdAndEventId(Long sessionId, Long eventId);

    void storeLivestreamAssetDetails(Session session, MuxAssetDTO muxAssetDTO);

    void storeEventLivestreamAssetDetails(EventStreaming eventStreaming, MuxAssetDTO muxAssetDTO);

    void storeDirectUploadAssetDetails(Session session, MuxAssetDTO muxAssetDTO, AssetType sessionAsset);

    @Transactional
    void storeDirectUploadAssetDetailsForEventStream(EventStreaming eventStreaming, MuxAssetDTO muxAssetDTO);

    void updateLivestreamAssetDuration(Session session, MuxAssetDTO muxAssetDTO);

    @Transactional
    void updateLivestreamAssetVideoName(Session session, String assetId, String fileName);

    @Transactional
    void updateEventLivestreamAssetVideoName(EventStreaming eventStreaming, String assetId, String fileName);

    List<MuxAssetDTO> findAllLivestreamAssetsForSessionBySessionIdAndType(long sessionId, AssetType sessionAsset);

    void markAssetAsDefaultPlayBackForSession(Session session, long id, AssetType sessionAsset);

    void updateStatusToDeleteBySessionId(long sessionId);

    MuxAssetDTO findDefaultPlayBackForSession(long sessionId, AssetType assetType);

    void setMostRecentAssetAsDefaultPlayBackForSession(Session session, AssetType sessionAsset);

    void setMostRecentAssetAsDefaultPlayBackForEventStreaming(EventStreaming eventStreaming);

    List<IdDurationDto> getSessionDurationForAnalyticsByIdsIn(List<Long> sessionIds);

    Double getSessionVideoDurationBySessionId(Long sessionId);

    void storeLivestreamAssetDetailsForVirtualEventSetting(Long virtualEventSettingId, String assetId, String playBackId, long eventId, MuxJwtDto muxJwtDto);

    List<MuxAssetDTO> findAllLiveStreamAssetsForVirtualEventSetting(Long eventId);

    void save(MUXLivestreamAssetDetails muxLivestreamAssetDetails);

    void removePastWelcomeMessageOrSessionVideo(Long muxLiveStreamAssetId);

    MUXLivestreamAssetDetails getMUXLivestreamAssetDetails(Long muxLiveStreamAssetId);

    void storeLivestreamAssetDetailsForNetworkingLounge(String loungeId, String assetIdByUploadId, String playBackId, long eventId, Long userId, NetworkingLoungeVideoDetailsDto dto);

    List<MuxAssetDTO> findAllByNetworkingLoungeId(String loungeId);

    void removeNetworkingLoungeVideo(Long muxLiveStreamAssetId, long userId, boolean isAdmin);

    List<MUXLivestreamAssetDetails> getAllLiveStreamAssetsForVirtualEventSettingByEvent(Long eventId);

    List<SessionEventLiveStream> findAllByAssetIdsIn(List<String> liveStreamIds);

    void updateRecordStatusToPlayBackDeleted(List<String> assetIdsToMarkPlayBackRemoved, RecordStatus recordStatus);

    void markAssetAsDefaultPlayBackForEventStreaming(EventStreaming eventStreaming, long id);

    List<MUXLivestreamAssetDetails> findByAssetId(String assetId);

    void updateMuxAssetStatus(List<String> assetIds, RecordStatus assetStatus);

    void deleteMuxAssetByEventDeletedBeforeThirtyDays();

    void updateMasterAccessRecordForAsset(String assetId, String type);

    boolean uploadS3FileInMux(MUXLivestreamAssetDetails muxLivestreamAssetDetails, Event event);

    List<MUXLivestreamAssetDetails> findByEventIdAndPlayBackId(Long eventId, String playBackId);

    List<NetworkingLoungeVideoDto> findAllLoungeVideoByNetworkingLoungeId(String loungeId);

    void updateMuxAssetsVisibility(Long id, boolean isVisible, Event event, User user);

    void deleteMuxAssets(Long id, Event event, User user);

    void updateMuxAssets( MuxAssetDTO muxAssetDTO, Event event, User user);

    List<MUXLivestreamAssetDetails> getVisibleAssetsBySessionIdAndEventId(Long sessionId, Long eventId);
}
