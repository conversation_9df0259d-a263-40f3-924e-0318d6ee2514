package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumKeyValueType;
import com.accelevents.domain.session_speakers.KeyValue;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.services.TicketTypeTrackSessionLimitsService;
import com.accelevents.services.neptune.NeptuneInterestServiceImpl;
import com.accelevents.services.repo.helper.KeyValueRepoService;
import com.accelevents.session_speakers.dto.KeyValueDTO;
import com.accelevents.session_speakers.dto.KeyValueDetailsDto;
import com.accelevents.session_speakers.services.KeyValueService;
import com.accelevents.session_speakers.services.SessionTagAndTrackService;
import com.accelevents.session_speakers.services.TicketingTypeTagAndTrackRepoService;
import com.accelevents.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class KeyValueServiceImpl implements KeyValueService {
    private static final Logger log = LoggerFactory.getLogger(KeyValueServiceImpl.class);

    @Autowired
    private KeyValueRepoService keyValueRepoService;

    @Autowired
    private SessionTagAndTrackService sessionTagAndTrackService;

    @Autowired
    private NeptuneInterestServiceImpl neptuneInterestService;

    @Autowired
    private TicketingTypeTagAndTrackRepoService ticketingTypeTagAndTrackRepoService;

    @Autowired
    private TicketTypeTrackSessionLimitsService ticketTypeTrackSessionLimitsService;

    private double sequence = 1000d;

    @Override
    @Transactional
    public Long createKeyValue(KeyValueDTO keyValueDTO, Event event) {
        throwExceptionIfKeyValueExists(keyValueDTO, event);
        KeyValue keyValue = new KeyValue();
        keyValue.setName(keyValueDTO.getName());
        keyValue.setEventId(event);
        keyValue.setType(keyValueDTO.getType());
        keyValue.setColor(keyValueDTO.getColor());
        keyValue.setBackgroundColor(keyValueDTO.getBackgroundColor());
        keyValue.setDescription(keyValueDTO.getDescription());
        setPositionForKeyValue(keyValue);

        keyValue = keyValueRepoService.save(keyValue);
        createChildKeyValue(keyValueDTO,keyValue,event);
        return keyValue.getId();
    }


    private KeyValue setPositionForKeyValue(KeyValue keyValue) {
        if (keyValue.getId() != 0) {
            return keyValue;
        } else {
            KeyValue lastItem = getLastItem(keyValue);
            if (lastItem != null) {
                keyValue.setPosition(lastItem.getPosition() + sequence);
            } else {
                keyValue.setPosition(sequence);
            }
            return keyValue;
        }
    }

    private KeyValue getLastItem(KeyValue keyValue) {
        return keyValueRepoService.getFirstByEventIdAndTypeOrderByPositionDesc(keyValue.getEventId(),keyValue.getType());
    }

    @Override
    @Transactional
    public void updateKeyValue(Long id, KeyValueDTO keyValueDTO, Event event) {
        throwExceptionIfKeyValueExistsForUpdate(keyValueDTO, event, id);
        KeyValue keyValue = getById(id);

        updateParentKeyValue(keyValue, keyValueDTO);

        if (EnumKeyValueType.TRACK.equals(keyValueDTO.getType()) && keyValueDTO.getChildTracks() != null) {
            updateChildKeyValues(keyValue, keyValueDTO.getChildTracks(), event);
        }
    }

    private void updateParentKeyValue(KeyValue keyValue, KeyValueDTO keyValueDTO) {
        log.info("updateParentKeyValue  id : {} , keyValueDTO : {}",keyValue.getId(),keyValueDTO);
        keyValue.setName(keyValueDTO.getName());
        keyValue.setColor(keyValueDTO.getColor());
        keyValue.setBackgroundColor(keyValueDTO.getBackgroundColor());
        keyValue.setDescription(keyValueDTO.getDescription());
        keyValueRepoService.save(keyValue);
    }

    private void updateChildKeyValues(KeyValue keyValue, List<KeyValueDTO> childDTOs, Event event) {
        log.info("Updating child key values for eventId: {}, childDTOs: {}", event.getEventId(), childDTOs);
        Map<Long, KeyValueDTO> updatedChildMap = new HashMap<>();
        List<KeyValue> keyValueList = new ArrayList<>();

        for (KeyValueDTO childDTO : childDTOs) {
            if (childDTO.getId() == null || childDTO.getId() == 0) {
                addNewChildKeyValue(keyValueList, keyValue, childDTO, event);
            } else {
                updatedChildMap.put(childDTO.getId(), childDTO);
            }
        }
        if (!updatedChildMap.isEmpty()) {
            List<KeyValue> existingChildKeyValues = keyValueRepoService.findByIdIn(new ArrayList<>(updatedChildMap.keySet()));
            for (KeyValue existingChild : existingChildKeyValues) {
                KeyValueDTO toUpdateKeyValue = updatedChildMap.get(existingChild.getId());
                if (toUpdateKeyValue != null) {
                    updateExistingChildKeyValue(keyValueList, toUpdateKeyValue, existingChild);
                }
            }
        }
        deleteChildKeyValue(event, keyValue,updatedChildMap.keySet());
        if (!keyValueList.isEmpty()) {
            keyValueRepoService.saveAll(keyValueList);
        }
    }


    private void addNewChildKeyValue(List<KeyValue> keyValueList,KeyValue keyValue, KeyValueDTO childDTO, Event event) {
        KeyValue childKeyValue = new KeyValue();
        childKeyValue.setName(childDTO.getName());
        childKeyValue.setEventId(event);
        childKeyValue.setType(childDTO.getType());
        childKeyValue.setColor(childDTO.getColor());
        childKeyValue.setBackgroundColor(childDTO.getBackgroundColor());
        childKeyValue.setDescription(childDTO.getDescription());
        childKeyValue.setPosition(1000D);
        childKeyValue.setParentId(keyValue.getId());
        keyValueList.add(childKeyValue);
    }

    private void updateExistingChildKeyValue(List<KeyValue> keyValueList,KeyValueDTO toUpdateKeyValue, KeyValue existingChildKeyValue) {
        log.info("updateExistingChildKeyValue : toUpdateKeyValue : {}",toUpdateKeyValue);
            existingChildKeyValue.setName(toUpdateKeyValue.getName());
            existingChildKeyValue.setColor(toUpdateKeyValue.getColor());
            existingChildKeyValue.setBackgroundColor(toUpdateKeyValue.getBackgroundColor());
            existingChildKeyValue.setDescription(toUpdateKeyValue.getDescription());
            keyValueList.add(existingChildKeyValue);
    }


    private void deleteChildKeyValue(Event event, KeyValue keyValue, Set<Long> updatedChildIds) {
        List<KeyValue> existingChildKeyValue = keyValueRepoService.findByEventAndParentIds(event,Collections.singletonList(keyValue.getId()));
        List<Long> childKeyValueToDeleteIds = existingChildKeyValue.stream()
                .map(KeyValue::getId)
                .filter(id -> !updatedChildIds.contains(id))
                .collect(Collectors.toList());
        log.info("deleteChildKeyValue | deleting child keyvalue  eventId : {} and  childKeyValueToDeleteIds : {}", event.getEventId(), childKeyValueToDeleteIds);
        if (!childKeyValueToDeleteIds.isEmpty()) {
            sessionTagAndTrackService.deleteByTagOrTrackIds(childKeyValueToDeleteIds);
            keyValueRepoService.deleteKeyValueByIds(childKeyValueToDeleteIds, event);
            log.info("Deleted successfully.");
        }
    }

    private void throwExceptionIfKeyValueExists(KeyValueDTO keyValueDTO, Event event) {
        // Check for parent KeyValue
        if (keyValueRepoService.checkKeyValueAlreadyExistByName(keyValueDTO.getName(), event, keyValueDTO.getType())) {
            throwNotAcceptableException(keyValueDTO);
        }

        // Check for child KeyValues if any
        if (keyValueDTO.getChildTracks() != null) {
            for (KeyValueDTO childDTO : keyValueDTO.getChildTracks()) {
                if (childDTO != null && keyValueRepoService.checkKeyValueAlreadyExistByName(childDTO.getName(), event, childDTO.getType())) {
                    throwNotAcceptableException(childDTO);
                }
            }
        }
    }

    private void throwNotAcceptableException(KeyValueDTO keyValueDTO) {
        NotAcceptableException.SessionSpeakerExceptionMsg exceptionMsg = NotAcceptableException.SessionSpeakerExceptionMsg.KEY_VALUE_ALREADY_EXIST;
        String message = Constants.KEY_VALUE_ALREADY_EXIST;
        message = message.replace(Constants.KEY_CAPITAL, keyValueDTO.getType().name());

        Map<String, String> defaultMessageParamMap = new LinkedHashMap<>();
        defaultMessageParamMap.put(Constants.KEY_CAPITAL, keyValueDTO.getType().name());

        exceptionMsg.setErrorMessage(message);
        exceptionMsg.setDeveloperMessage(message);
        exceptionMsg.setDefaultMessage(Constants.KEY_VALUE_ALREADY_EXIST);
        exceptionMsg.setDefaultMessageParamMap(defaultMessageParamMap);

        throw new NotAcceptableException(exceptionMsg);
    }


    private void throwExceptionIfKeyValueExistsForUpdate(KeyValueDTO keyValueDTO, Event event,Long id){
        if(!keyValueRepoService.checkKeyValueAlreadyExistByNameForUpdate(keyValueDTO.getName(),event,id) && keyValueRepoService.checkKeyValueAlreadyExistByName(keyValueDTO.getName(),event,keyValueDTO.getType()))
        {
            NotAcceptableException.SessionSpeakerExceptionMsg exceptionMsg = NotAcceptableException.SessionSpeakerExceptionMsg.KEY_VALUE_ALREADY_EXIST;
            String message = Constants.KEY_VALUE_ALREADY_EXIST;
            message = message.replace(Constants.KEY_CAPITAL, keyValueDTO.getType().name());
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.KEY_CAPITAL, keyValueDTO.getType().name());
            exceptionMsg.setErrorMessage(message);
            exceptionMsg.setDeveloperMessage(message);
            exceptionMsg.setDefaultMessage(Constants.KEY_VALUE_ALREADY_EXIST);
            exceptionMsg.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new NotAcceptableException(exceptionMsg);
        }
    }

    public KeyValue getById(Long id) {
        return keyValueRepoService.findById(id).orElseThrow(()-> new NotFoundException(NotFoundException.SessionSpeakerNotFound.TAG_TRACK_NOT_FOUND));
    }

    @Override
    @Transactional
    public void deleteKeyValue(Long id, Event event) {
        List<Long> childKeyValueIds = keyValueRepoService.getIdsByEventAndIdORParentId(event,id);
        childKeyValueIds.add(id);
        log.info("deleteKeyValue | deleting keyValue eventId : {} , childKeyValueIds : {} ",event.getEventId(),childKeyValueIds);
        sessionTagAndTrackService.deleteByTagOrTrackIds(childKeyValueIds);
        ticketingTypeTagAndTrackRepoService.deleteByTagOrTrackId(childKeyValueIds);
        keyValueRepoService.deleteKeyValueByIds(childKeyValueIds,event);
        ticketTypeTrackSessionLimitsService.deleteByTagOrTrackIdAndEventId(id, event.getEventId());
        log.info("deleteKeyValue | deleted successfully");
    }

    @Override
    public DataTableResponse getKeyValueList(Event event,
                                             String searchString,
                                             String expand,
                                             Pageable pageable,
                                             EnumKeyValueType type) {
        // Step 1: Fetch parent key-values with pagination
        Page<KeyValueDetailsDto> parentKeyValues;
        if (StringUtils.isBlank(searchString)) {
            parentKeyValues = keyValueRepoService.findByEventAndTypeAndParentIdNULL(event, type, pageable);
            List<Long> parentIds = parentKeyValues.getContent().stream()
                    .map(KeyValueDetailsDto::getId)
                    .collect(Collectors.toList());
            //Fetch child key-values if type is TRACK
            Map<Long, List<KeyValueDetailsDto>> childKeyValueMap = EnumKeyValueType.TRACK.equals(type) && !parentIds.isEmpty()
                    ? fetchChildKeyValues(event, parentIds)
                    : Collections.emptyMap();
            parentKeyValues.getContent().forEach(parentKeyValueDetailsDto -> createKeyValueDto(parentKeyValueDetailsDto, childKeyValueMap));
        } else {
            parentKeyValues = keyValueRepoService.findByEventAndTypeSearchString(event, type, searchString, pageable);
        }
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(parentKeyValues.getTotalElements());
        dataTableResponse.setRecordsFiltered(parentKeyValues.getContent().size());
        dataTableResponse.setData(parentKeyValues.getContent());

        return dataTableResponse;
    }

    private Map<Long, List<KeyValueDetailsDto>> fetchChildKeyValues(Event event, List<Long> parentIds) {
        List<KeyValueDetailsDto> childKeyValues = keyValueRepoService.getKeyValueDetailsDtoByEventAndParentIds(event, parentIds);
        return childKeyValues == null ? Collections.emptyMap(): childKeyValues.stream()
                .collect(Collectors.groupingBy(KeyValueDetailsDto::getParentId));
    }

    private void createKeyValueDto(KeyValueDetailsDto keyValueDetailsDto,
                                   Map<Long, List<KeyValueDetailsDto>> childKeyValueDetailsDtoMap) {
        if (childKeyValueDetailsDtoMap.containsKey(keyValueDetailsDto.getId())) {
            keyValueDetailsDto.setChildKeyValueDetailsDto(childKeyValueDetailsDtoMap.get(keyValueDetailsDto.getId()));
        }

    }

    @Override
	public List<KeyValue> findAllByIds(List<Long> keyValueIds) {
        return CollectionUtils.isEmpty(keyValueIds) ? Collections.emptyList() : keyValueRepoService.findByIdIn(keyValueIds);
	}

    @Override
    public List<KeyValueDTO> getKeyValueInfoList(Event event, EnumKeyValueType type) {
        return keyValueRepoService.getKeyValueInfoList(event, type);
    }



    @Override
    @Transactional
    public void updateKeyValuePosition(Long keyValueId, Long topKeyValueId, Long topBottomKeyValueId, Event event) {
        KeyValue keyValue = getById(keyValueId);
        updateWithSequence(keyValue, topKeyValueId, topBottomKeyValueId, event);
    }

    private void updateWithSequence(KeyValue keyValue, Long topKeyValueId, Long topBottomKeyValueId, Event event) {
        log.info("KeyValueServiceImpl | updateWithSequence | keyValueId | {} | topKeyValueId | {} | topBottomKeyValueId | {} | keyValuePositionBeforeUpdate | {}",keyValue.getId(),topKeyValueId,topBottomKeyValueId,keyValue.getPosition());
        Optional<KeyValue> topKeyValueOpt = keyValueRepoService.findById(topKeyValueId);
        Optional<KeyValue> topNextKeyValueOpt = keyValueRepoService.findById(topBottomKeyValueId);

        //move to middle
        if (topKeyValueOpt.isPresent() && topNextKeyValueOpt.isPresent()) {
            KeyValue topNextKeyValue = topNextKeyValueOpt.get();
            KeyValue topKeyValue = topKeyValueOpt.get();
            double position = (topNextKeyValue.getPosition() + topKeyValue.getPosition()) / 2;
            if (1 == position) {
                KeyValue keyValueNextKeyValue = getNextPositionItem(keyValue, event);
                KeyValue keyValuePrevKeyValue = getPreviousPositionItem(keyValue, event);
                double positiondifferent = (keyValueNextKeyValue.getPosition() - keyValuePrevKeyValue.getPosition())
                    / 2;
                keyValueRepoService.updatePositionKeyValue(event, topKeyValue.getPosition(), keyValue.getPosition(), keyValue.getType(), positiondifferent);
                keyValue.setPosition(topNextKeyValue.getPosition());
                keyValueRepoService.save(keyValue);
            } else {
                keyValue.setPosition(position);
                keyValueRepoService.save(keyValue);
            }
            //move to top
        } else if (!topKeyValueOpt.isPresent() && topNextKeyValueOpt.isPresent()) {
            KeyValue topNextKeyValue = topNextKeyValueOpt.get();
            keyValue.setPosition(topNextKeyValue.getPosition() + sequence);
            keyValueRepoService.save(keyValue);
            //move at last
        } else if (topKeyValueOpt.isPresent() && !topNextKeyValueOpt.isPresent()) {   //NOSONAR
            KeyValue topKeyValue = topKeyValueOpt.get();
            double posDiff = topKeyValue.getPosition() - sequence;
            if (posDiff <= 1) {
                keyValueRepoService.updatePositionForAllKeyValueByEventIdAndType(sequence, event, keyValue.getType());
            }
            keyValue.setPosition(sequence);
            keyValueRepoService.save(keyValue);
        }
        log.info("KeyValueServiceImpl | updateWithSequence | keyValueId | {} | topKeyValueId | {} | topBottomKeyValueId | {} | keyValuePositionAfterUpdate | {}",keyValue.getId(),topKeyValueId,topBottomKeyValueId,keyValue.getPosition());
    }

    private KeyValue getPreviousPositionItem(KeyValue keyValue, Event event) {
        return keyValueRepoService.getFirstByIdAndEventIdAndTypeAndPositionLessThanOrderByPositionDesc(keyValue.getId(), event, keyValue.getType(), keyValue.getPosition());
    }

    private KeyValue getNextPositionItem(KeyValue keyValue, Event event) {
        return keyValueRepoService.getFirstByIdAndEventIdAndTypeAndPositionGreaterThanOrderByPosition(keyValue.getId(), event, keyValue.getType(), keyValue.getPosition());
    }

    @Override
    public KeyValue getByNameAndEventIdAndType(String name, Event event, EnumKeyValueType type) {
        return keyValueRepoService.getByNameAndEventIdAndType(name, event, type);
    }

    @Override
    public KeyValueDTO getKeyValueById(Event event, Long id) {
        List<KeyValueDTO> keyValueList = keyValueRepoService.getKeyValueDTOByEventAndIdORParentId(event, id);
        KeyValueDTO keyValueDTO = null;
        if (!keyValueList.isEmpty()) {
            log.info("getKeyValueById | eventId : {} , id : {} and keyValueList size : {}",event.getEventId(),id,keyValueList.size());
            keyValueDTO = keyValueList.stream()
                    .filter(e -> e.getParentId() == null)
                    .findFirst()
                    .orElse(null);

            if (keyValueDTO != null) {
                List<KeyValueDTO> childTracks = keyValueList.stream()
                        .filter(e -> id.equals(e.getParentId()))
                        .collect(Collectors.toList());
                keyValueDTO.setChildTracks(childTracks);
            }else {
                keyValueDTO = keyValueList.stream()
                        .filter(e -> Objects.equals(e.getId(), id))
                        .findFirst()
                        .orElse(null);
            }
        } else {
            log.info("No data found | eventId : {} , id : {}", event.getEventId(), id);
        }
        return keyValueDTO;
    }


    private void createChildKeyValue(KeyValueDTO keyValueDTO,KeyValue keyValue,Event event){
        if (EnumKeyValueType.TRACK.equals(keyValueDTO.getType()) && keyValueDTO.getChildTracks() != null && !keyValueDTO.getChildTracks().isEmpty()) {
            log.info("createChildKeyValue eventId : {} , keyValueDTO : {} ",event.getEventId(),keyValueDTO);
            KeyValue finalKeyValue = keyValue;
            List<KeyValue> keyValueList = keyValueDTO.getChildTracks().stream().filter(Objects::nonNull)
                    .map(childDTO -> {
                        KeyValue childKeyValue = new KeyValue();
                        childKeyValue.setName(childDTO.getName());
                        childKeyValue.setEventId(event);
                        childKeyValue.setType(childDTO.getType());
                        childKeyValue.setColor(childDTO.getColor());
                        childKeyValue.setBackgroundColor(childDTO.getBackgroundColor());
                        childKeyValue.setDescription(childDTO.getDescription());
                        childKeyValue.setPosition(1000D);
                        childKeyValue.setParentId(finalKeyValue.getId());
                        return childKeyValue;
                    })
                    .collect(Collectors.toList());

            if (!keyValueList.isEmpty()) {
                keyValueRepoService.saveAll(keyValueList);
            }
        }
    }
}
