package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.session_speakers.dto.IdDurationDto;
import com.accelevents.session_speakers.repo.SessionDetailsRepo;
import com.accelevents.session_speakers.services.SessionDetailsRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class SessionDetailsRepoServiceImpl implements SessionDetailsRepoService
{
    @Autowired private SessionDetailsRepo sessionDetailsRepo;

    @Override
    public SessionDetails save(SessionDetails sessionsDetails) {

        try {
            return sessionDetailsRepo.save(sessionsDetails);
        } catch (DataIntegrityViolationException e) {
            NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.DUPLICATE_SESSION_DETAIL_ID_ENTRY;
            throw new NotAcceptableException(exception);
        }
    }

    @Override
    public Optional<SessionDetails> findBySessionId(Long sessionDetailsId) {
        return sessionDetailsRepo.findBySessionId(sessionDetailsId);
    }

    @Override
    public String findVonageSessionIdBySessionId(Long sessionId) {
        return sessionDetailsRepo.findVonageSessionIdBySessionId(sessionId);
    }

    @Override
    public List<IdDurationDto> getSessionDurationBySessionIds(List<Long> sessionIds) {
        return sessionDetailsRepo.getSessionDurationBySessionIds(sessionIds);
    }

    @Override
    public Double getSessionVideoDurationBySessionId(Long sessionId) {
        return sessionDetailsRepo.getSessionVideoDurationBySessionId(sessionId);
    }

    @Override
    public void deleteBySessionId(Long sessionId) {
        sessionDetailsRepo.deleteBySessionId(sessionId);
    }

    @Override
    public String findSubTitleFileUrlFromSessionDetailsBySession(Long sessionId) {
        return sessionDetailsRepo.findSubTitleFileUrlFromSessionDetailsBySession(sessionId);
    }

    @Override
    public boolean getSessionRecordBySession(Long sessionId) {
        Boolean isRecordSession =  sessionDetailsRepo.findSessionRecordBySession(sessionId);
        return null != isRecordSession ? isRecordSession : false;
    }

    @Override
    public boolean getHideVideoControlsBySession(Long sessionId) {
        Boolean isHideVideoControls = sessionDetailsRepo.findHideVideoControlsBySession(sessionId);
        return null != isHideVideoControls && isHideVideoControls;
    }

    @Override
    public List<IdDurationDto> getSessionVideoDurationByEventId(Long eventId) {
        return sessionDetailsRepo.getSessionVideoDurationByEventId(eventId);
    }

    @Override
    public List<SessionDetails> getAllSessionDetailsByEventId(Long eventId){
        return sessionDetailsRepo.findAllSessionDetailsByEventId(eventId);
    }

    @Override
    public List<SessionDetails> findSessionDetailsBySessionIds(List<Session> sessions) {
        return sessions != null && !sessions.isEmpty() ? sessionDetailsRepo.findSessionDetailsBySessionIds(sessions) : Collections.emptyList();
    }

    @Override
    public Integer getAllowedMinutesToJoinLate(Long sessionDetailsId) {
        return sessionDetailsRepo.getAllowedMinutesToJoinLate(sessionDetailsId);
    }

    @Override
    public List<SessionDetails> getSessionDetailsForDisableLiveCaptionsDataFix(Long from, Long to) {
        return sessionDetailsRepo.getSessionDetailsForDisableLiveCaptionsDataFix(from , to);
    }
    @Override
    public void saveAll(List<SessionDetails> sessionDetails) {
        sessionDetailsRepo.saveAll(sessionDetails);
    }

    @Override
    public List<SessionDetails> getSessionDetailsByPostSessionCTASessionId(Long sessionId) {
        return sessionDetailsRepo.getSessionDetailsByPostSessionCTASessionId(sessionId);
    }

    @Override
    public List<SessionDetails> getAllSessionDetailsByEventIdWithoutCache(Long eventId) {
        return sessionDetailsRepo.getAllSessionDetailsByEventIdWithoutCache(eventId);
    }
}
