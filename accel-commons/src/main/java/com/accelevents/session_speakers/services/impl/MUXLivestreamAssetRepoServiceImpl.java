package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.MUXLivestreamAssetRepo;
import com.accelevents.session_speakers.services.MUXLivestreamAssetRepoService;
import com.accelevents.session_speakers.services.SessionSubtitleService;
import com.accelevents.utils.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MUXLivestreamAssetRepoServiceImpl implements MUXLivestreamAssetRepoService {

    @Autowired
    MUXLivestreamAssetRepo muxLivestreamAssetRepo;

    @Autowired
    SessionSubtitleService sessionSubtitleService;

    @Override
    public Optional<MUXLivestreamAssetDetails> findBySessionIdAndAssetId(Long sessionId, String assetId) {
        return muxLivestreamAssetRepo.findBySessionIdAndAssetId(sessionId, assetId);

    }

    @Override
    public List<MUXLivestreamAssetDetails> findByAssetId(String assetId) {
        return muxLivestreamAssetRepo.findByAssetId(assetId);
    }

    @Override
    public void saveWithDefaultSequence(MUXLivestreamAssetDetails assetDetails) {
        assetDetails.setPosition(1000);
        this.save(assetDetails);
    }

    @Override
    public void save(MUXLivestreamAssetDetails assetDetails) {
        muxLivestreamAssetRepo.save(assetDetails);

    }

    @Override
    public void saveAll(List<MUXLivestreamAssetDetails> assetDetails) {
        muxLivestreamAssetRepo.saveAll(assetDetails);
    }

    @Override
    public void updateAssetDuration(Long id, Double duration) {
        muxLivestreamAssetRepo.updateAssetDuration(id, duration);
    }

    @Override
    public void updateAssetFileNameForDownload(Long id, String fileName) {
        muxLivestreamAssetRepo.updateAssetFileNameForDownload(id, fileName);
    }

    @Override
    public List<MuxAssetDTO> findAllLivestreamAssetsForSessionBySessionIdAndType(Long sessionId, AssetType assetType) {
        return muxLivestreamAssetRepo.findAllLivestreamAssetsForSessionBySessionIdAndType(sessionId, assetType);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findBySessionIdsIn(List<Long> sessionIds) {
        return muxLivestreamAssetRepo.findBySessionIdsIn(sessionIds);
    }

    @Override
    public List<MuxAssetDTO> getMuxAssetsByAssetTypeAndVisibleStatusAndSessionId(Long sessionId, AssetType assetType, boolean visible) {
        if(sessionId == null){
            return Collections.emptyList();
        }
        List<MUXLivestreamAssetDetails> muxAsset = muxLivestreamAssetRepo.findByAssetTypeAndVisibleStatusAndSessionIdsIn(Collections.singletonList(sessionId), assetType, visible);

        List<Long> muxIds = muxAsset.stream()
                .map(MUXLivestreamAssetDetails::getId)
                .collect(Collectors.toList());
        Map<Long,List<SessionSubtitleDTO>> muxIdAndSubtitleListMap = sessionSubtitleService.getMapOfMuxIdAndSubtitleListByMuxIdListAndSessionId(muxIds, List.of(sessionId));
        return muxAsset.stream()
                .map(asset -> new MuxAssetDTO(asset, muxIdAndSubtitleListMap.get(asset.getId())))
                .collect(Collectors.toList());
    }

    private List<MUXLivestreamAssetDetails> findByAssetTypeAndVisibleStatusAndSessionIdsIn(List<Long> sessionIds,AssetType assetType) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyList();
        }
        return muxLivestreamAssetRepo.findByAssetTypeAndVisibleStatusAndSessionIdsIn(sessionIds, assetType, true);
    }

    @Override
    public void markAllAssetAsNonDefaultPlayBack(Long id, AssetType assetType) {
        muxLivestreamAssetRepo.markAllAssetAsNonDefaultPlayBack(id, assetType);
    }

    @Override
    public void markAssetAsDefaultPlayBack(Long id) {
        muxLivestreamAssetRepo.markAssetAsDefaultPlayBack(id);
    }

    @Override
    public void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status) {
        muxLivestreamAssetRepo.updateStatusToDeleteBySessionId(sessionId, status);
    }

    @Override
    public MuxAssetDTO findDefaultPlayBackForSession(Long sessionId, AssetType assetType) {
        return muxLivestreamAssetRepo.findDefaultPlayBackForSession(sessionId, assetType);
    }

    @Override
    public Optional<MUXLivestreamAssetDetails> findFirstBySessionIdAndAssetTypeOrderByIdDesc(Long id, AssetType assetType) {
        return muxLivestreamAssetRepo.findFirstBySessionIdAndAssetTypeOrderByIdDesc(id, assetType);
    }

    @Override
    public List<IdDurationDto> countBySessionIdIn(List<Long> sessionIds) {
        return muxLivestreamAssetRepo.countBySessionIdIn(sessionIds);
    }

    @Override
    public Double getSessionVideoDurationBySessionId(Long sessionId) {
        return muxLivestreamAssetRepo.getSessionVideoDurationBySessionId(sessionId);
    }

    @Override
    public MUXLivestreamAssetDetails getByEventIdAndAssetIdAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(Long eventId, String assetId) {
        return muxLivestreamAssetRepo.getByEventIdAndAssetIdAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(eventId, assetId);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findAllByEventIdAndDefaultPlaybackTrueAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(Long eventId) {
        return muxLivestreamAssetRepo.findAllByEventIdAndDefaultPlaybackTrueAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(eventId);
    }

    @Override
    public void markAllAssetAsNonDefaultPlayBackByEventId(Long id) {
            muxLivestreamAssetRepo.markAllAssetAsNonDefaultPlayBackByEventId(id);
    }

    @Override
    public List<MuxAssetDTO> findAllLiveStreamAssetsForVirtualEventSetting(Long eventId) {
        return muxLivestreamAssetRepo.findAllLiveStreamAssetsForVirtualEventSetting(eventId);
    }

    @Override
    public MUXLivestreamAssetDetails findById(Long muxLiveStreamAssetId) {
        return muxLivestreamAssetRepo.findById(muxLiveStreamAssetId).orElseThrow(() -> new NotFoundException(NotFoundException.VirtualEventNotFound.DEFAULT_PLAYBACK_NOT_FOUND_FOR_VIDEO));

    }

    @Override
    public MUXLivestreamAssetDetails getByNetworkingLoungeIdAndAssetIdAndSessionIdNull(String loungeId, String assetId) {
        return muxLivestreamAssetRepo.getByNetworkingLoungeIdAndAssetIdAndSessionIdNull(loungeId, assetId);
    }

    @Override
    public List<MuxAssetDTO> findAllByNetworkingLoungeId(String loungeId) {
        return muxLivestreamAssetRepo.findAllByNetworkingLoungeId(loungeId);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findAllLiveStreamAssetsForVirtualEventSettingByEvent(Long eventId) {
        return muxLivestreamAssetRepo.findAllLiveStreamAssetsForVirtualEventSettingByEvent(eventId);
    }

    @Override
    public Optional<MUXLivestreamAssetDetails> findBySessionIdAndPlaybackIdAndAssetType(Long sessionId, String playbackId, AssetType assetType) {
        return muxLivestreamAssetRepo.findBySessionIdAndPlaybackIdAndAssetType(sessionId, playbackId, assetType);

    }

    @Override
    public Optional<MUXLivestreamAssetDetails> findBySessionIdAndPlaybackIdAndAssetTypeAndS3AssetKeyIsNull(Long sessionId, String playbackId, AssetType assetType) {
        return muxLivestreamAssetRepo.findBySessionIdAndPlaybackIdAndAssetTypeAndS3AssetKeyIsNull(sessionId, playbackId, assetType);

    }

    @Override
    public  Optional<MUXLivestreamAssetDetails> findBySessionIdAndDefaultPlaybackTrue(Long sessionId, AssetType assetType) {
        return muxLivestreamAssetRepo.findBySessionIdAndDefaultPlaybackTrueAndAssetType(sessionId, assetType);

    }

    @Override
    public  Optional<MUXLivestreamAssetDetails> findBySessionIdAndDefaultPlaybackTrueAndAssetTypeAndS3AssetKeyIsNull(Long sessionId, AssetType assetType) {
        return muxLivestreamAssetRepo.findBySessionIdAndDefaultPlaybackTrueAndAssetTypeAndS3AssetKeyIsNull(sessionId, assetType);

    }

    @Override
    public String findAllByNetworkingLoungeIdAndUserId(String loungeId, Long userId) {
        return String.valueOf(muxLivestreamAssetRepo.findAllByNetworkingLoungeIdAndUserId(loungeId,userId));
    }

    @Override
    public List<SessionEventLiveStream> findAllByAssetIdsIn(List<String> assetIds) {
        return muxLivestreamAssetRepo.findAllByAssetIdsIn(assetIds);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findAllBySessionIdIn(List<Long> blockedDirectUploadSessionsIds) {
        if(CollectionUtils.isEmpty(blockedDirectUploadSessionsIds)){
            return Collections.emptyList();
        }
        return muxLivestreamAssetRepo.findAllBySessionIdIn(blockedDirectUploadSessionsIds);
    }

    @Override
    public void updateRecordStatusToPlayBackDeleted(List<String> assetIdsToMarkPlayBackRemoved, RecordStatus recordStatus) {
        muxLivestreamAssetRepo.updateRecordStatusToPlayBackDeleted(assetIdsToMarkPlayBackRemoved, recordStatus);
    }

    @Transactional
    @Override
    public void updateRecordAndAssetStatusToMovedOrDelete(String muxAssetId) {
        muxLivestreamAssetRepo.updateRecordAndAssetStatusToMovedOrDelete(muxAssetId);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findMuxPlayBackRemovedLiveStreamAssetsByEventId(Long eventId) {
        return muxLivestreamAssetRepo.findMuxPlayBackRemovedLiveStreamAssetsByEventId(eventId, RecordStatus.PLAYBACK_REMOVED);
    }

    @Override
    public void updatePlayBackUrlByAssetId(String assetId, String playBackId) {
        muxLivestreamAssetRepo.updatePlayBackUrlByAssetId(assetId, playBackId, RecordStatus.CREATE);
    }

    @Override
    public Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndDefaultPlaybackTrue(Long eventStreamId) {
        return muxLivestreamAssetRepo.findByEventStreamIdAndDefaultPlaybackTrue(eventStreamId);
    }

    @Override
    public Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndPlaybackId(Long eventStreamId, String playbackId) {
        return muxLivestreamAssetRepo.findByEventStreamIdAndPlaybackId(eventStreamId, playbackId);
    }

    @Override
    public void markAllAssetAsNonDefaultPlayBackByEventStreamId(Long id) {
        muxLivestreamAssetRepo.markAllAssetAsNonDefaultPlayBackByEventStreamId(id);
    }

    @Override
    public Optional<MUXLivestreamAssetDetails> findByEventStreamIdAndAssetId(Long eventStreamId, String assetId) {
        return muxLivestreamAssetRepo.findByEventStreamIdAndAssetId(eventStreamId, assetId);
    }

    @Override
    public Optional<MUXLivestreamAssetDetails> findFirstByEventStreamIdOrderByIdDesc(Long eventStreamId) {
        return muxLivestreamAssetRepo.findFirstByEventStreamIdOrderByIdDesc(eventStreamId);
    }

    @Override
    public Map<Long, List<MuxAssetDTO>> getMuxAssetsMapByAssetTypeAndSessionIds(List<Long> sessionIds, AssetType sessionAsset) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyMap();
        }
        List<MUXLivestreamAssetDetails> muxLivestreamAssetDetails = findByAssetTypeAndVisibleStatusAndSessionIdsIn(sessionIds, sessionAsset);
        Map<Long, List<MuxAssetDTO>> muxAssetsMap = new HashMap<>();
        List<Long> muxIds = muxLivestreamAssetDetails.stream()
                .map(MUXLivestreamAssetDetails::getId)
                .collect(Collectors.toList());
        Map<Long,List<SessionSubtitleDTO>> muxIdAndSubtitleListMap = sessionSubtitleService.getMapOfMuxIdAndSubtitleListByMuxIdListAndSessionId(muxIds, sessionIds);
        muxLivestreamAssetDetails.forEach(muxLiveStreamAsset -> {
            List<MuxAssetDTO> muxAssetDTOList = muxAssetsMap.get(muxLiveStreamAsset.getSessionId());
            if (muxAssetDTOList == null) {
                muxAssetDTOList = new ArrayList<>();
            }
            MuxAssetDTO muxAssetDTO = new MuxAssetDTO(muxLiveStreamAsset.getId(), muxLiveStreamAsset.getCreatedAt(), muxLiveStreamAsset.getDuration(),
                    muxLiveStreamAsset.getPlaybackId(), muxLiveStreamAsset.getAssetId(),muxLiveStreamAsset.isDefaultPlayback(), muxLiveStreamAsset.getFileNameForDownload(),muxLiveStreamAsset.getPlayBackRestrictionToken(), muxLiveStreamAsset.getThumbnailRestrictionToken(), muxLiveStreamAsset.getS3AssetKey(), muxIdAndSubtitleListMap.get(muxLiveStreamAsset.getId()));
            muxAssetDTO.setSessionId(muxLiveStreamAsset.getSessionId());
            muxAssetDTO.setVisible(muxLiveStreamAsset.isVisible());
            muxAssetDTOList.add(muxAssetDTO);
            muxAssetsMap.put(muxLiveStreamAsset.getSessionId(), muxAssetDTOList);
        });

        return muxAssetsMap;
    }

    @Override
    public List<String> getLivestreamAssetsDetailsBetweenDate() {
        return muxLivestreamAssetRepo.getLivestreamAssetsDetailsBetweenDate();
    }

    @Override
    public List<MUXLivestreamAssetDetails> getMuxAssetsByRecordStatusDeletedAndAssetIdFromAndTo(Long from, Long to) {
        return muxLivestreamAssetRepo.getMuxAssetsByRecordStatusDeletedAndAssetIdFromAndTo(from, to);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findAllMUXLivestreamAssetDetailsByEventId(Long eventId) {
        return muxLivestreamAssetRepo.findAllMUXLivestreamAssetDetailsByEventId(eventId);
    }

    @Override
    public void updateMuxAssetStatus(List<String> assetIds, RecordStatus assetStatus) {
        muxLivestreamAssetRepo.updateMuxAssetStatus(assetIds, assetStatus);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findAllMuxAssetByDefaultPlaybackAndCreatedDateBeforeAndIdFromTo(boolean isDefaultPlayback, Date createdDate, Long from, Long to) {
        return muxLivestreamAssetRepo.findAllMuxAssetByDefaultPlaybackAndCreatedDateBeforeAndIdFromTo(isDefaultPlayback, createdDate, from, to);
    }

    @Override
    public List<MUXLivestreamAssetDetails> getAllMuxAssetByCreatedDateWithFiveAttendeeAndFromTo(Date createdDate, Long from, Long to) {
        return muxLivestreamAssetRepo.getAllMuxAssetByCreatedDateWithFiveAttendeeAndFromTo(createdDate, from, to);
    }

    @Override
    public Page<Object[]> getAllMuxAssetByEventDeleteBeforeThirtyDays(Pageable pageable) {
        return muxLivestreamAssetRepo.getAllMuxAssetByEventDeleteBeforeThirtyDays(pageable);
    }

    @Override
    public List<MUXLivestreamAssetDetails> getAllWelcomeVideoByCreatedAtAndFromTo(Date createdDate, Long from, Long to) {
        return muxLivestreamAssetRepo.getAllWelcomeVideoByCreatedAtAndFromTo(createdDate, from, to);
    }

    @Override
    public List<MUXLivestreamAssetDetails> findByEventIdAndPlayBackId(Long eventId, String playBackId) {
        return muxLivestreamAssetRepo.findByEventIdAndPlaybackId(eventId, playBackId);
    }

    @Override
    public List<NetworkingLoungeVideoDto> findAllLoungeVideoByNetworkingLoungeId(String loungeId) {
        return muxLivestreamAssetRepo.findAllLoungeVideoByNetworkingLoungeId(loungeId);
    }

    @Override
    public String getDescriptionByAssetIdAndSessionId(String assetId, Long sessionId) {
        Optional<MUXLivestreamAssetDetails> muxLivestreamAssetDetails = muxLivestreamAssetRepo.findByAssetIdAndSessionId(assetId, sessionId);
        return muxLivestreamAssetDetails.isPresent() ? muxLivestreamAssetDetails.get().getDescription() : Constants.STRING_EMPTY;
    }
}
