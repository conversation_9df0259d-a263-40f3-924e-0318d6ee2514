package com.accelevents.session_speakers.services.impl.cache;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.graphql.GraphQL;
import com.accelevents.utils.Constants;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Service
public class ChimeRecordingGraphQLHandler extends GraphQL {

    public ChimeRecordingGraphQLHandler(GraphQLConfiguration graphQLConfiguration){
        super(graphQLConfiguration.getAppsyncWorkshopRecordingAssetApiUrl(), Constants.BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
    }

    public Map<String, String> getChimeSessionRecordingAssetStatus(Long assetId, Long sessionId) throws JSONException {
        String operationName = Constants.GET_CHIME_RECORDING_ASSET_STATUS;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME,operationName);
        mainRequest.put(Constants.QUERY,"query getChimeSessionRecordingAssetStatus { getChimeSessionRecordingAssetStatus(sessionId: "+sessionId+", assetId: "+assetId+") { sessionId assetId updatedAt status } }");
        return executeSelect(mainRequest, operationName);
    }


    public void handleCreateOrUpdateSessionStatusLogsForWorkshopAssetWithAsync(String inputStatus,Long assetId, Long sessionId){
        new SimpleAsyncTaskExecutor().execute(() -> handleCreateOrUpdateSessionStatusLogsForWorkshopAsset(inputStatus, assetId, sessionId));
    }

    public boolean handleCreateOrUpdateSessionStatusLogsForWorkshopAsset(String inputStatus, Long assetId, Long sessionId) {
        try {
            logger.info("handleCreateOrUpdateSessionStatusLogsForWorkshopAsset sessionId {} assetId {} status {} ", sessionId, assetId, inputStatus);
            Map<String, String> map = getChimeSessionRecordingAssetStatus(assetId, sessionId);
            if(StringUtils.isNotBlank(map.get(Constants.SESSION_ID)) && StringUtils.isNotBlank(map.get(Constants.ASSETID))){
                updateChimeSessionRecordingAssetStatus(sessionId, assetId, inputStatus);
            } else {
                createChimeSessionRecordingAssetStatus(sessionId, assetId, inputStatus);
                updateChimeSessionRecordingAssetStatus(sessionId, assetId, inputStatus);
            }
        } catch (JSONException e) {
            logger.info("Error While handleCreateOrUpdateSessionStatusLogsForWorkshopAsset sessionId=>{}, assetId=>{}, status=>{}", sessionId, assetId, inputStatus);
        }
        return true;
    }

    public boolean executeQuery(JSONObject mainRequest, String operationName) {
        Map<String, String> map = executeSelect(mainRequest, operationName);
        return StringUtils.isNotBlank(map.get(Constants.ASSETID));
    }

    private Map<String, String> executeSelect(JSONObject mainRequest, String operationName) {
        try {
            JSONObject finalResult = execute(mainRequest, operationName);
            Map<String, String> map = new HashMap<>();

            if (finalResult.has(Constants.SESSION_ID)) {
                map.put(Constants.SESSION_ID, String.valueOf(finalResult.get(Constants.SESSION_ID)));
            }
            if (finalResult.has(Constants.UPDATEDAT)) {
                map.put(Constants.UPDATEDAT, String.valueOf(finalResult.get(Constants.UPDATEDAT)));
            }
            if (finalResult.has(Constants.STATUS)) {
                map.put(Constants.STATUS, finalResult.getString(Constants.STATUS));
            }
            if (finalResult.has(Constants.ASSETID)) {
                map.put(Constants.ASSETID, String.valueOf(finalResult.get(Constants.ASSETID)));
            }

            return map;

        } catch (UnirestException e) {
            logger.error("GraphqlHandlerExecuteUnirest", e);
        } catch (JSONException e) {
            logger.error("GraphqlHandlerExecuteJSONException", e);
        }
        return Collections.emptyMap();
    }


    public boolean createChimeSessionRecordingAssetStatus(Long sessionId, Long assetId, String status) throws JSONException {
        JSONObject variable = new JSONObject();
        variable.put(Constants.CREATED_CHIME_RECORDING_ASSET_STATUS_INPUT, setChimeSessionRecordingAssetStatusInput(sessionId, assetId, status));

        String operationName = Constants.CREATED_CHIME_RECORDING_ASSET_STATUS;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, Constants.CREATE_CHIME_RECORDING_ASSET_STATUS_MUTATION);

        return executeQuery(mainRequest, operationName);
    }

    public boolean updateChimeSessionRecordingAssetStatus(Long sessionId, Long assetId, String status) throws JSONException {
        JSONObject variable = new JSONObject();
        variable.put(Constants.UPDATE_CHIME_RECORDING_ASSET_STATUS_INPUT, setChimeSessionRecordingAssetStatusInput(sessionId, assetId, status));

        String operationName = Constants.UPDATE_CHIME_RECORDING_ASSET_STATUS;
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, Constants.UPDATE_CHIME_RECORDING_ASSET_STATUS_MUTATION);
        return executeQuery(mainRequest, operationName);
    }


    public JSONObject setChimeSessionRecordingAssetStatusInput(Long sessionId, Long assetId, String status) throws JSONException {
        JSONObject requestData = new JSONObject();
        requestData.put(Constants.SESSION_ID, sessionId);
        requestData.put(Constants.STATUS, status);
        requestData.put(Constants.ASSETID, assetId);
        return requestData;
    }
}
