package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.DeviceCheckerType;
import com.accelevents.session_speakers.dto.BreakoutRoomDto;
import com.accelevents.session_speakers.dto.DeviceCheckerDto;

import java.util.List;
import java.util.Map;

public interface DeviceCheckerService {
    void addSpeakerDeviceChecker(Long userId, Event event, DeviceCheckerDto deviceCheckerDto);

    Map<Long, Boolean> speakerIdAndRunDeviceChecker(List<Long> speakerIds);

    String checkSpeakerDeviceCheckerWithDeviceId(Long id, Event event, String deviceId, DeviceCheckerType deviceCheckerType,String languageCode);
}
