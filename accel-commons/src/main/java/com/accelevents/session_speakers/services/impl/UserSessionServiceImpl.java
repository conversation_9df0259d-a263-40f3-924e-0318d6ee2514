package com.accelevents.session_speakers.services.impl;

import com.accelevents.auction.dto.UploadSessionAttendeeResponseContainer;
import com.accelevents.billing.chargebee.repositories.EventPlanConfigRepository;
import com.accelevents.common.dto.AttendeeAnalyticCountDTO;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionLocation;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.domain.session_speakers.UserSessionCalendar;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.TrackLimitWithAvailability;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.BaseException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.helpers.UserSessionHelper;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.TicketTypeTrackSessionLimitsRepository;
import com.accelevents.repositories.UserRolesRepository;
import com.accelevents.ro.event.repository.ROEventLevelSettingsRepo;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.dynamodb.analytics.ConsolidatedAnalyticsService;
import com.accelevents.services.dynamodb.user.activity.UserActivityService;
import com.accelevents.services.elasticsearch.videoanalytics.AttendeeSessionVideoAnalyticsData;
import com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsService;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.services.tray.io.tracking.TrayTrackingService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.SessionTagAndTrackRepo;
import com.accelevents.session_speakers.repo.UserSessionCalendarRepo;
import com.accelevents.session_speakers.repo.UserSessionRepo;
import com.accelevents.session_speakers.services.*;
import com.accelevents.utils.*;
import com.google.common.base.Strings;
import com.opencsv.CSVReader;
import org.apache.commons.collections4.CollectionUtils;
import org.jsoup.helper.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.EnumSessionFormat.*;
import static com.accelevents.domain.enums.EnumUserSessionStatus.*;
import static com.accelevents.domain.enums.EventFormat.*;
import static com.accelevents.enums.PlanConfigNames.FREE_PLAN;
import static com.accelevents.enums.PlanConfigNames.STARTER;
import static com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG.ATTENDEE_TICKET_NOT_GRANT_ACCESS_TO_THIS_SESSION;
import static com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG.USER_ALREADY_CHECK_IN_SESSION_WITH_TICKET;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.*;
import static com.accelevents.session_speakers.dto.UserSessionDTO.toEntity;
import static com.accelevents.utils.Constants.ATTENDEE;
import static com.accelevents.utils.GeneralUtils.convertCommaSeparatedToListLong;


@Service
public class UserSessionServiceImpl implements UserSessionService {
	private static final Logger log = LoggerFactory.getLogger(UserSessionServiceImpl.class);

    private UserSessionRepoService userSessionRepoService;
	private EventTicketsService eventTicketsService;
	private SessionSpeakerService sessionSpeakerService;
	private EventPlanConfigRepository eventPlanConfigRepository;
    private SessionDetailsService sessionDetailsService;
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    private ROStaffRoleService roStaffRoleService;
    private StaffService staffService;

    private SpeakerService speakerService;
    private ROStaffService roStaffService;
    private EventTicketsRepoService eventTicketsRepoService;

    private VirtualEventService virtualEventService;
    private VideoAnalyticsService videoAnalyticsService;
    private SessionRepoService sessionRepoService;
    private SessionService sessionService;
    private SpeakerRepoService speakerRepoService;
    private UserService userService;
    private ROUserService roUserService;
    private EventCommonRepoService eventCommonRepoService;
    private TicketingCheckInService ticketingCheckInService;
    private TrayTrackingService trayTrackingService;

    private UserSessionCalendarRepo userSessionCalendarRepo;
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    private SessionCheckInLogService sessionCheckInLogService;
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Autowired
    private GamificationCacheStoreService<String,Object> redisCacheService;


	@Value("${chime.region}")
	private String region;

    @Value("${app.profile}")
    private String environmentProfile;

    private static final List<EnumUserSessionStatus> SESSION_CHECKOUT_STATUS = Arrays.asList(CHECK_OUT,MEETING_LEFT);

    private static final List<SessionTypeFormat> SESSION_IN_PERSON_HYBRID = Arrays.asList(SessionTypeFormat.IN_PERSON, SessionTypeFormat.HYBRID);
    private final UserRolesRepository userRolesRepository;
    @Autowired
    private ROEventLevelSettingsRepo rOEventLevelSettingsRepo;
    @Autowired
    private SessionTagAndTrackRepo sessionTagAndTrackRepo;
    @Autowired
    private TicketTypeTrackSessionLimitsRepository ticketTypeTrackSessionLimitsRepository;
    @Autowired
    private UserSessionRepo userSessionRepo;;
    @Autowired
    private UserActivityService userActivityService;
    @Autowired
    private ConsolidatedAnalyticsService consolidatedAnalyticsService;
    @Autowired
    private final ROVirtualEventService roVirtualEventService;

    @Autowired
	public UserSessionServiceImpl(UserSessionRepoService userSessionRepoService, EventTicketsService eventTicketsService,
                                  SessionSpeakerService sessionSpeakerService,
                                  EventPlanConfigRepository eventPlanConfigRepository,
                                  SessionDetailsService sessionDetailsService,
                                  VirtualEventSettingsRepoService virtualEventSettingsRepoService,
                                  ROStaffRoleService roStaffRoleService, StaffService staffService,
                                  ROStaffService roStaffService,
                                  EventTicketsRepoService eventTicketsRepoService,
                                  SessionRepoService sessionRepoService,
                                  VideoAnalyticsService videoAnalyticsService,
                                  @Lazy SessionService sessionService,
                                  SpeakerRepoService speakerRepoService,
                                  UserService userService,
                                  ROUserService roUserService,
                                  EventCommonRepoService eventCommonRepoService,
                                  UserSessionCalendarRepo userSessionCalendarRepo,
                                  @Lazy TicketingCheckInService ticketingCheckInService,
                                  @Lazy AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService,
                                  SessionCheckInLogService sessionCheckInLogService,
                                  TrayTrackingService trayTrackingService,
                                  VirtualEventService virtualEventService,
                                  UserRolesRepository userRolesRepository,
                                  SendGridMailPrepareService sendGridMailPrepareService,
                                  @Lazy SpeakerService speakerService,
                                  ROVirtualEventService roVirtualEventService){
		this.userSessionRepoService = userSessionRepoService;
		this.eventTicketsService = eventTicketsService;
		this.sessionSpeakerService = sessionSpeakerService;
		this.eventPlanConfigRepository = eventPlanConfigRepository;
		this.sessionDetailsService = sessionDetailsService;
		this.virtualEventSettingsRepoService = virtualEventSettingsRepoService;
		this.roStaffRoleService = roStaffRoleService;
        this.staffService= staffService;
        this.roStaffService = roStaffService;
        this.eventTicketsRepoService = eventTicketsRepoService;
        this.sessionRepoService = sessionRepoService;
        this.videoAnalyticsService = videoAnalyticsService;
        this.sessionService = sessionService;
        this.speakerRepoService = speakerRepoService;
        this.userService = userService;
        this.roUserService = roUserService;
        this.eventCommonRepoService = eventCommonRepoService;
        this.ticketingCheckInService = ticketingCheckInService;
        this.userSessionCalendarRepo=userSessionCalendarRepo;
        this.afterTaskIntegrationTriggerService = afterTaskIntegrationTriggerService;
        this.sessionCheckInLogService = sessionCheckInLogService;
        this.trayTrackingService = trayTrackingService;
        this.virtualEventService = virtualEventService;
        this.userRolesRepository = userRolesRepository;
        this.sendGridMailPrepareService = sendGridMailPrepareService;
        this.speakerService = speakerService;
        this.roVirtualEventService =  roVirtualEventService;
    }


	@Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
	public void unRegisterUser(UserSessionDTO dto, Long eventTicketId, Event event, boolean isAdminOrStaff, boolean isUnBookmarked, User user) {
        List<UserSession> userSessions;
        if(NumberUtils.isNumberGreaterThanZero(eventTicketId)){
            userSessions = userSessionRepoService.findByEventTicketIdAndSessionIdAndEventId(eventTicketId, dto.getSessionId(), event.getEventId());
        } else {
            userSessions = userSessionRepoService.findBySessionIdAndUserIdAndEventId(dto.getSessionId(),dto.getUserId(), event.getEventId());
        }

        try {
            if(!userSessions.isEmpty()) {
                UserSession userSession = userSessions.get(0);
                log.info("Unregistration initiated: user {} is removing session {} associated with user {}.",
                        user != null ? user.getUserId() : "",
                        userSession.getSessionId(),
                        userSession.getUserId()
                );
                if (isUnBookmarked && userSession.isBookmarked()) {
                        if (BOOKMARKED.equals(userSession.getSessionStatus())) {
                            userSessionRepoService.delete(userSession);
                        } else if (REGISTERED.equals(userSession.getSessionStatus())) {
                            userSession.setBookmarked(false);
                            userSessionRepoService.save(userSession);
                        }else if(WAITLISTED.equals(userSession.getSessionStatus())){
                            userSession.setBookmarked(false);
                            userSessionRepoService.save(userSession);
                        }
                } else {
                    if (REGISTERED.equals(userSession.getSessionStatus()) && !userSession.isBookmarked()) {
                        userSessionRepoService.delete(userSession);
                    } else if (userSession.isBookmarked()) {
                        userSession.setSessionStatus(BOOKMARKED);
                        userSessionRepoService.save(userSession);

                    }
                    afterTaskIntegrationTriggerService.userUnRegisterSession(event, userSessions, isAdminOrStaff);
                }

                boolean privateSession = sessionRepoService.isSessionPrivate(userSession.getSessionId());
                if(privateSession){
                    redisCacheService.clearSessionCache(event.getEventId());
                }
            }
        }catch (Exception exception){
            log.info("Exception occurred while unRegisterUser eventId {} userId {} exception {}", event.getEventId(), dto.getUserId(), exception.getMessage());
        }
	}


    @Override
    @Transactional
    public void unRegisterUserWithoutEventTicket(Long sessionId, Long userId, Event event) {
        List<UserSession> userSessions = userSessionRepoService.findBySessionIdAndUserIdAndEventId(sessionId, userId, event.getEventId());
        if(!userSessions.isEmpty()) {
            userSessionRepoService.deleteAll(userSessions);
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public void checkOutUserSession(UserSessionDTO dto, Long eventTicketId, Event event, String device, User staffUser, boolean offlineData, String source, String sourceDescription, boolean isAppLogin){
        List<UserSession> userSessions;
        log.info("User check out from session {} eventTicketId {} userId {}", dto.getSessionId(), eventTicketId, dto.getUserId());
        if(NumberUtils.isNumberGreaterThanZero(eventTicketId)){
            userSessions = userSessionRepoService.findByEventTicketIdAndSessionIdAndEventId(eventTicketId, dto.getSessionId(), event.getEventId());
        } else {
            userSessions = userSessionRepoService.findBySessionIdAndUserIdAndEventId(dto.getSessionId(),dto.getUserId(), event.getEventId());
        }
        try {
            if(!userSessions.isEmpty()) {
                userSessions.forEach(userSession -> {
                    userSession.setCheckInStatus(CHECK_OUT);
                    if (offlineData) {
                        userSession.setCheckOutTime(dto.getCheckoutTime());
                    }else {
                        userSession.setCheckOutTime(new Date());
                    }
                    sessionCheckInLogService.createSessionCheckInLog(userSession, staffUser.getUserId(), device);
                });
                userSessionRepoService.saveAll(userSessions);
                EventTickets eventTickets = eventCommonRepoService.findById(eventTicketId);
                if(eventTickets != null){
                    createSessionCheckinCheckoutActivityLog(event, staffUser, eventTickets.getBarcodeId(), EnumSessionCheckInLogStatus.CHECK_OUT, true, null, false, source, sourceDescription, isAppLogin, eventTickets);
                }
                log.info("Successfully user check out from session {} eventTicketId {} userId {}", dto.getSessionId(), eventTicketId, dto.getUserId());
            }
        }catch (Exception exception){
            log.info("Exception occurred while checkOutUserSession eventId {} userId {} exception {}", event.getEventId(), dto.getUserId(), exception.getMessage());
        }
    }

	@Override
	public UserSession registerUser(Long eventTicketId, User loggedInUser, Session session, Event event) {
        UserSessionDTO userSessionDTO = new  UserSessionDTO ();
//        Integer registeredCount = countRegisteredBySessionId(session.getId());
//         if (null != registeredCount && session.getCapacity() > 0 && session.getCapacity() <= registeredCount) {
//            throw new NotAcceptableException(EXCEED_SESSION_CAPACITY);
//        }

        userSessionDTO.setUserId(loggedInUser != null ? loggedInUser.getUserId() :eventTicketsService.findUserIdByEventTicketId(eventTicketId));
        validateMatchingTicketType(loggedInUser,session, eventTicketId,event);
        Long consumedTagTrackId = getConsumedTrackOrvalidateSessionTracksLimit(userSessionDTO.getUserId(), session, event, eventTicketId);

        userSessionDTO.setSessionId(session.getId());
        userSessionDTO.setEventId(event.getEventId());
        userSessionDTO.setEventTicketId(eventTicketId);
        UserSession userSession = toEntity(userSessionDTO);
        userSession.setConsumedTagTrackId(consumedTagTrackId);
        log.info("registerUser  | saving userSession : {}",userSession);
		return userSessionRepoService.save(userSession);
	}

    public UserSession waitlistUser(Long eventTicketId, User loggedInUser, Session session, Event event) {
        UserSessionDTO userSessionDTO = new  UserSessionDTO ();
        userSessionDTO.setUserId(loggedInUser != null ? loggedInUser.getUserId() :eventTicketsService.findUserIdByEventTicketId(eventTicketId));
        validateMatchingTicketType(loggedInUser,session, eventTicketId,event);
        Long consumedTagTrackId = getConsumedTrackOrvalidateSessionTracksLimit(userSessionDTO.getUserId(), session, event, eventTicketId);

        userSessionDTO.setSessionId(session.getId());
        userSessionDTO.setEventId(event.getEventId());
        userSessionDTO.setEventTicketId(eventTicketId);
        UserSession userSession = toEntity(userSessionDTO);
        userSession.setConsumedTagTrackId(consumedTagTrackId);
        userSession.setSessionStatus(WAITLISTED);
        log.info("waitlistUser  | saving userSession : {}",userSession);
        return userSessionRepoService.save(userSession);
    }

    @Override
    public UserSession registerUserWithoutTicket(User loggedInUser, Session session, Event event) {

	    if(!(isNotMeetUpSession(session)
                && (isEventStaffOrSpeaker(loggedInUser, session, event)))){
            log.info("This user is not allowed to register without ticket userId {} sessionId {} ", loggedInUser.getUserId(), session.getId());
            throw new NotAcceptableException(USER_NOT_ALLOWED_TO_REGITER_WITHOUT_TICKET);
        }

        UserSessionDTO userSessionDTO = new  UserSessionDTO ();

        List<UserSession> sessionIds = userSessionRepoService.findBySessionIdAndUserIdAndEventId(session.getId(), loggedInUser.getUserId(), event.getEventId());
        if(!sessionIds.isEmpty()){
            log.info("User is already registered in session {} " ,session.getId());
            return sessionIds.get(0);
        }
        validateCapacity(session,event,0L,false);
//        Integer registeredCount = countRegisteredBySessionId(session.getId());
//        if (null != registeredCount && session.getCapacity() > 0 && session.getCapacity() <= registeredCount) {
//            throw new NotAcceptableException(EXCEED_SESSION_CAPACITY);
//        }

        userSessionDTO.setUserId(loggedInUser.getUserId());
        userSessionDTO.setSessionId(session.getId());
        userSessionDTO.setEventId(event.getEventId());
        UserSession userSession = toEntity(userSessionDTO);
        return userSessionRepoService.save(userSession);
    }

	public void validateMatchingTicketType(User user,Session session, Long eventTicketId, Event event) {

        log.info("validateMatchingTicketType | getTicketTypesThatCanBeRegistered : {} ",session.getTicketTypesThatCanBeRegistered());
        if(StringUtils.isEmpty(session.getTicketTypesThatCanBeRegistered())){
            throw new NotAcceptableException(TICKET_TYPE_NOT_MATCHED);
        }

        log.info("sessionId {}, eventTicketId {} ",session.getId() ,eventTicketId);

        // Query always give a single result
        List<Object[]> ticketingTypeAttribute = eventTicketsService.findTicketingTypeById(eventTicketId);
        Long ticketingTypeId = null != ticketingTypeAttribute.get(0)[0] ? (Long) ticketingTypeAttribute.get(0)[0] : null;
        Integer maxRegisterUserAllowed = null != ticketingTypeAttribute.get(0)[1] ? (Integer) ticketingTypeAttribute.get(0)[1] : null;
        log.info("validateMatchingTicketType || ticketingType ticketingTypeId {}  maxSessionRegisterUser {} ", ticketingTypeId,maxRegisterUserAllowed);

        if(!convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered()).contains(ticketingTypeId)){
                throw new NotAcceptableException(TICKET_TYPE_NOT_MATCHED);
        }

        List<Long> sessionIds = sessionSpeakerService.findAllSessionIdBySpeakerUserIdWithOutWorkshop(user.getUserId(), event.getEventId());

        int countWithOutSpeakerSessionByventTicketId = 0;
        if(!CollectionUtils.isEmpty(sessionIds)){
            countWithOutSpeakerSessionByventTicketId = userSessionRepoService.countBySessionIdsAndUserId(sessionIds,user.getUserId(),event.getEventId()).intValue();
        }else{
            countWithOutSpeakerSessionByventTicketId = userSessionRepoService.countByEventTicketId(eventTicketId).intValue();
        }

        if(!sessionSpeakerService.isUserSpeakerInSession(user.getUserId(),session.getId()) && maxRegisterUserAllowed!=null && countWithOutSpeakerSessionByventTicketId >= maxRegisterUserAllowed && isUserAlreadyRegistered(session, eventTicketId, event)){
			NotAcceptableException.SessionSpeakerExceptionMsg exceptionMsg = REGISTER_MAX_LIMIT_REACHED;
            String errorMessageTemplate = Constants.REGISTER_MAX_LIMIT_REACHED;
            String errorMessage = errorMessageTemplate.replace(Constants.NUMBER_OF_SESSION_PERMITTED, String.valueOf(maxRegisterUserAllowed));
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.NUMBER_OF_SESSION_PERMITTED,String.valueOf(maxRegisterUserAllowed));
			exceptionMsg.setErrorMessage(errorMessage);
			exceptionMsg.setDeveloperMessage(errorMessage);
            exceptionMsg.setDefaultMessage(Constants.REGISTER_MAX_LIMIT_REACHED);
            exceptionMsg.setDefaultMessageParamMap(defaultMessageParamMap);
			throw new NotAcceptableException(exceptionMsg);
		}
	}

    private boolean isUserAlreadyRegistered(Session session, Long eventTicketId, Event event) {
        List<UserSession> currentUserSessions = userSessionRepoService.findRegisteredUserByEventTicketIdAndSessionIdAndEventId(eventTicketId, session.getId(), event.getEventId(), REGISTERED);
        return !(!currentUserSessions.isEmpty() && (null == currentUserSessions.get(0).getCheckInStatus() || Arrays.asList(CHECK_OUT, MEETING_LEFT).contains(currentUserSessions.get(0).getCheckInStatus())));
    }

    public boolean validateMatchingTicketTypeForBulkUpload(Session session, Long eventTicketId, UploadSessionAttendeeResponseContainer container, String firstName, String lastName,String languageCode) {
	    if(StringUtils.isEmpty(session.getTicketTypesThatCanBeRegistered())) {
            container.addInvalidAttendees(firstName, lastName, 2,languageCode);
            return false;
        }

        Optional<EventTickets> optionalEventTickets = eventTicketsService.findEventTicketById(eventTicketId);
        if (optionalEventTickets.isPresent()) {
            EventTickets eventTickets = optionalEventTickets.get();
            TicketingType ticketingType = eventTickets.getTicketingTypeId();

            if(!convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered())
                    .contains(ticketingType.getId())){
                container.addInvalidAttendees(firstName, lastName, 2,languageCode);
                return false;
            }

            Integer maxRegisterUserAllowed = ticketingType.getMaxSessionRegisterUser();
            if(maxRegisterUserAllowed!=null &&
                    userSessionRepoService.countByEventTicketId(eventTicketId).intValue()
                            >= maxRegisterUserAllowed){
                container.addInvalidAttendees(firstName, lastName, 6,languageCode);
                return false;
            }
        } else {
            container.addInvalidAttendees(firstName, lastName, 5,languageCode);
            return false;
        }
        return true;
    }

	@Override
	public boolean isUserAvailableStateAndTicketTypesIn(long userId, long eventId, long sessionId, List<Long> ticketTypeIds) {
		return userSessionRepoService.isUserAvailableStateForTicketTypesIn(eventId, userId, sessionId, CHECK_IN_AVAILABLE, ticketTypeIds);
	}

	@Override
	public boolean isUserAvailableState(long userId, long eventId, long sessionId) {
		return userSessionRepoService.isUserAvailableState(eventId, userId, sessionId, CHECK_IN_AVAILABLE);
	}

	@Override
	public int getCheckInUserCountBySession(Long sessionId, Event event) {
		return userSessionRepoService.getCheckInUserCountBySession(sessionId);
	}

	@Override
	public List<Long> findActiveUsersByTicketTypesInAndNotInUserId(long sessionId, long eventId, List<Long> ticketTypeIds, List<Long> userIds){
		return userSessionRepoService.findByEventIdAndSessionIdAndSateAndNotUserIdAndTicketTypesIn(eventId, sessionId, CHECK_IN_AVAILABLE, userIds, ticketTypeIds);
	}

	@Override
	public void updateUserSessionState(long sessionId,long eventId, List<Long> userIds, EnumUserSessionStatus state) {
		userSessionRepoService.updateUserState(state, eventId, sessionId, userIds);
	}

    @Override
    public void updateUserSessionCheckInState(long sessionId,long eventId, List<Long> userIds, EnumUserSessionStatus state) {
        userSessionRepoService.updateUserCheckInState(state, eventId, sessionId, userIds);
    }

	@Override
	public List<IdCountDto> getSessionStatsForIdsIn(List<Long> sessionIds){
		return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() :  userSessionRepoService.registerCountBySessionIdsAndSessionStatus(sessionIds, REGISTERED);
	}

	@Override
	public List<IdCountDto> countSessionAttendeeBySessionIdIn(List<Long> sessionIds){
		return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : userSessionRepoService.countSessionAttendeeBySessionIdIn(sessionIds);
	}

    @Override
    @Deprecated
    //Moved to RO remove this method
    public List<IdCountDto> countSessionAttendeeByEventAndUserIds(List<Long> eventIds, List<Long> userIds) {
        return CollectionUtils.isEmpty(eventIds) || CollectionUtils.isEmpty(userIds) ? Collections.emptyList()
                : userSessionRepoService.countSessionAttendeeByEventAndUserIds(eventIds, userIds);
    }

    @Override
    public long countSessionAttendeeBySessionId(Long sessionId) {
        return userSessionRepoService.countSessionAttendeeBySessionId(sessionId);
    }

    @Override
	public List<RegisterdHolderUsers> findRegisteredUserInfo(long sessionId) {
		return userSessionRepoService.findRegisteredUserBySessionId(sessionId);
	}

    @Override
    public List<RegisterdHolderUsers> getRegisteredUserInfo(long sessionId) {
        return userSessionRepoService.getRegisteredUserBySessionId(sessionId);
    }


    @Override
	public List<AttendeeSession> findAttendeeSessionsByUserId(Event event, long userId, boolean fetchEngagement, String searchString) {
        log.info("Start find attendee sessions by user {}, event {}", userId, event.getEventId());
        String planName = null;
        Optional<EventPlanConfig> optionalEventPlanConfig = eventPlanConfigRepository.findByEventId(event.getEventId());
        if (optionalEventPlanConfig.isPresent()) {
            planName = optionalEventPlanConfig.get().getPlanConfig().getPlanName();
        }
        if ((planName != null && (planName.equals(FREE_PLAN.getName()) || planName.equals(STARTER.getName()))) && !IN_PERSON.equals(event.getEventFormat())) {
            log.error("Admin with the current plan is not authorized to access find attendee sessions user {}, event {} ", userId, event.getEventId());
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_AUTHORIZED_TO_ACCESS_SESSION_USER_ANALYTICS);
        }
        List<AttendeeSession> attendeeSessions = userSessionRepoService.findAttendeeSessionsByUserId(event.getEventId(), userId, searchString);
        log.info("Fetched attendee sessions size {}, user {}, event {}", attendeeSessions.size(), userId, event.getEventId());

        if(fetchEngagement) {
            Map<Long, AttendeeSessionVideoAnalyticsData> userSessionEngagement = videoAnalyticsService.getUserSessionsWatchTimeData(event, userId);
            log.info("Fetched Session Engagement from ES size {}, user {}, event {}", userSessionEngagement.size(), userId, event.getEventId());

            Map<Long, Double> durationMap = sessionDetailsService.getSessionVideoDurationsByEvent(event);
            log.info("Fetched video duration  size {}, user {}, event {}", durationMap.size(), userId, event.getEventId());

            attendeeSessions.forEach(attendeeSession -> {
                attendeeSession.setEndTime(event);
                attendeeSession.setStartTime(event);
                setAttendeeSessionEngagementData(attendeeSession, userSessionEngagement, durationMap);
            });
            log.info("End get attendee sessions size {}, user {}, event {}", attendeeSessions.size(), userId, event.getEventId());
        }
        return attendeeSessions;
    }

    protected void setAttendeeSessionEngagementData(AttendeeSession attendeeSession,//NOSONAR
                                                  Map<Long, AttendeeSessionVideoAnalyticsData> userSessionEngagement,
                                                  Map<Long, Double> durationMap) {

        String status = attendeeSession.isAttended() ? Constants.ATTENDED : attendeeSession.getStatus();
        String engagement = "0%";

        AttendeeSessionVideoAnalyticsData attendeeSessionVideoAnalyticsData = userSessionEngagement.get(attendeeSession.getSessionId());
        if (attendeeSessionVideoAnalyticsData != null) {
            Double duration = durationMap.get(attendeeSession.getSessionId());
            if (attendeeSessionVideoAnalyticsData.getLiveWatchTime() > 0) {
                if (duration != null && duration > 0) {
                    Double engagePercentage = DoubleHelper.roundValueTwoDecimal((attendeeSessionVideoAnalyticsData.getLiveWatchTime()* 100 / duration));
                    engagement = (engagePercentage > 100 ? 100 : engagePercentage) + "%";
                }
            } else if (attendeeSessionVideoAnalyticsData.getRecordingWatchTime() > 0) {
                if (duration != null && duration > 0) {
                    Double engagePercentage = DoubleHelper.roundValueTwoDecimal((attendeeSessionVideoAnalyticsData.getRecordingWatchTime()* 100 / duration));
                    engagement = (engagePercentage > 100 ? 100 : engagePercentage) + "%";
                }
            }
        }
        attendeeSession.setEngagement(engagement);
        attendeeSession.setStatus(status);
    }



	@Override
	public Map<Long, List<Long>> findRegisteredEventTicketIdByUserAndSessionId(Long userId, List<Long> sessionIds) {
		if(CollectionUtils.isEmpty(sessionIds)){
			return Collections.emptyMap();
		}

		List<UserSession> userSessions = userSessionRepoService.findRegisteredEventTicketIdByUserAndSessionId(userId,sessionIds);
		Map<Long, List<Long>> userSessionIds = new HashMap<>();
		userSessions.forEach(userSession->{
			List<Long> list = userSessionIds.get(userSession.getSessionId());
			if(list == null){
				list = new ArrayList<>();
			}
			list.add(userSession.getEventTicketId());
            log.info("userSession details with user id {} | user session id {} | user event ticket id {} ",userSession.getUserId(), userSession.getSessionId(), userSession.getEventTicketId());
			if (!BOOKMARKED.equals(userSession.getSessionStatus())) {
                userSessionIds.put(userSession.getSessionId(), list);
            }
		});

		log.info("Final currentUserSessionIdAndEventTicketIdMap {}", userSessionIds);
        return userSessionIds;
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED,rollbackFor = {Exception.class})
	public void updateStatus(Long sessionId, Long userId, String status, Event event, String region, String device) {
		List<UserSession> userSessions = userSessionRepoService.findBySessionIdAndUserIdAndEventId(sessionId, userId,event.getEventId());
		if(!CollectionUtils.isEmpty(userSessions)){
			UserSession  userSession = userSessions.get(0);
            if(REGISTERED.name().equals(status)) {
                userSession.setSessionStatus(REGISTERED);
            } else {
                userSession.setCheckInStatus(EnumUserSessionStatus.valueOf(status));
                if (EnumUserSessionStatus.valueOf(status).equals(CHECK_IN_AVAILABLE)) {
                    userSession.setCheckInTime(new Date());
                    userSession.setRegion(region);
                }
                sessionCheckInLogService.createSessionCheckInLog(userSession, null, device);
            }
			try{
			    userSessionRepoService.save(userSession);

                if(userSession.getCheckInStatus().equals(CHECK_IN_AVAILABLE)){
                    afterTaskIntegrationTriggerService.userRegisterOrAttendSession(event, userSession, false);
                }
			}
			catch (Exception exception){
			    log.info("Exception occurred while disconnect/update user session {}",exception.getMessage());
            }
		}
	}

	@Override
	public Integer countRegisteredBySessionId(Long sessionId) {
        BigInteger count = userSessionRepoService.registerCountBySessionId(sessionId, REGISTERED);
		return count != null ? count.intValue() : 0;
	}

	@Override
	public List<Long> getEventTicketIdsByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId) {
		List<Long> eventTicketIdsByEventIdAndUserIdAndSessionId = userSessionRepoService.getEventTicketIdsByEventIdAndUserIdAndSessionId(eventId, userId, sessionId);
		return eventTicketIdsByEventIdAndUserIdAndSessionId.isEmpty()? Collections.emptyList():eventTicketIdsByEventIdAndUserIdAndSessionId;
	}

	@Override
	public List<Long> getTicketingTypeIdByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId) {
		List<Long> ticketTypeIds = userSessionRepoService.getTicketingTypeIdByEventIdAndUserIdAndSessionId(eventId, userId, sessionId);
		return ticketTypeIds.isEmpty()? Collections.emptyList():ticketTypeIds;
	}

    @Override
    public void registerUserFromStaff(User userToBeCheckedIn, UserSessionDTO userSessionDTO, Boolean join, Event event, Boolean pastJoinSession, String device, User staffUser, boolean isAppLogin, String source, String sourceDescription, StringBuilder barcode) {
        Long eventTicketId = userSessionDTO.getEventTicketId();
        log.info("registerUserFromStaff sessionId {} userId {} Event {} eventTicketId {}", userSessionDTO.getSessionId(), userToBeCheckedIn.getUserId(), eventTicketId, event);
        EventTickets  eventTicket = null;
        if(eventTicketId == null) {
            log.info("registerUserFromStaff eventTicketId is null");
        } else {
            Optional<EventTickets> optionalEventTickets = eventTicketsRepoService.findByEventTicketId(eventTicketId);
            if(!optionalEventTickets.isPresent()) {
                log.info("No ticket found with ticket id {}", eventTicketId);
            } else {
                eventTicket = optionalEventTickets.get();
            }
        }
        if(eventTicket != null){
            barcode.append(eventTicket.getBarcodeId() != null ? eventTicket.getBarcodeId() : Constants.STRING_EMPTY);
        }
        checkInUserInSessionWithEventTicketIdOrBarcodeId(userToBeCheckedIn, event, eventTicket , userSessionDTO, join, pastJoinSession, device, staffUser,false, false, false, source, sourceDescription);

    }

    private boolean isUserCanBookmarkOrCheckInSession(Event event, Session session, TicketTypeFormat eventTicketTypeFormat,boolean isFromAdmin, boolean isUserBookMark) {
        boolean isTicketAllowed = false;
        if (IN_PERSON.equals(event.getEventFormat()) && SessionTypeFormat.IN_PERSON.equals(session.getSessionTypeFormat()) && eventTicketTypeFormat.equals(TicketTypeFormat.IN_PERSON) && checkUserRequiredInPersonCheckIn(session, isFromAdmin)) {
            isTicketAllowed = true;
        } else if (VIRTUAL.equals(event.getEventFormat()) && SessionTypeFormat.VIRTUAL.equals(session.getSessionTypeFormat()) && eventTicketTypeFormat.equals(TicketTypeFormat.VIRTUAL)) {
            isTicketAllowed = true;
        } else if (HYBRID.equals(event.getEventFormat())) {
            isTicketAllowed = isTicketAllowedForHybridEvent(session, eventTicketTypeFormat, isFromAdmin, isUserBookMark, isTicketAllowed);
        }
        return isTicketAllowed;
    }

    private boolean isTicketAllowedForHybridEvent(Session session, TicketTypeFormat eventTicketTypeFormat, boolean isFromAdmin, boolean isUserBookMark, boolean isTicketAllowed) {
        if (SessionTypeFormat.VIRTUAL.equals(session.getSessionTypeFormat()) && !eventTicketTypeFormat.equals(TicketTypeFormat.IN_PERSON)) {
            isTicketAllowed = true;
        } else if (SessionTypeFormat.IN_PERSON.equals(session.getSessionTypeFormat()) && Arrays.asList(TicketTypeFormat.IN_PERSON, TicketTypeFormat.HYBRID).contains(eventTicketTypeFormat) && checkUserRequiredInPersonCheckIn(session, isFromAdmin)) {
                isTicketAllowed = true;
        } else if (SessionTypeFormat.HYBRID.equals(session.getSessionTypeFormat())) {
            isTicketAllowed = true;
            if (TicketTypeFormat.IN_PERSON.equals(eventTicketTypeFormat) && (checkUserIsBookMarkWithInPersonTicket(isUserBookMark, isFromAdmin) || !checkUserRequiredInPersonCheckIn(session, isFromAdmin))) {
                isTicketAllowed = false;
            }
        }
        return isTicketAllowed;
    }

    private boolean checkUserRequiredInPersonCheckIn(Session session, boolean isFromAdmin) {
        return !session.isSessionCheckInRequired() || isFromAdmin;
    }

    private boolean checkUserIsBookMarkWithInPersonTicket(boolean isUserBookMark, boolean isFromAdmin) {
        return !isFromAdmin && isUserBookMark;
    }


    @Override
	public boolean registerUserFromPortal(User user, Session session, Boolean join, Event event, Boolean pastJoinSession, Boolean isAdminOrStaff, String device, boolean isBookmarked, boolean isSaveASeat, boolean joinWaitlist) {
        UserSession userSession = null;
        boolean isEventStaff = Boolean.TRUE.equals(isAdminOrStaff);
        boolean isSpeakerInSession = sessionSpeakerService.isUserSpeakerInSession(user.getUserId(), session.getId());
        boolean isSpeakerInEvent = speakerService.isSpeakerInEvent(event, user);

        log.warn("registerUserFromPortal Event {}", (event.getEventId()));
        validateTicketTypeAndEventAdminOrStaff(user, session, event, isEventStaff, isSpeakerInSession, isSpeakerInEvent);
        validateTimeAccessElapsedForVirtual(session, event, user, join, pastJoinSession, isEventStaff, isSpeakerInSession);

        if(!isBookmarked){
            validateRegisterCapacityAndConcurrentSessionRegistrationRulesForVirtual(session,event,user.getUserId(), isEventStaff, isSpeakerInSession, joinWaitlist);
        }
        try {
            userSession = tryAndRegisterUserWithDifferentTicketType(user, session, event,join,isEventStaff,isBookmarked, isSpeakerInSession, joinWaitlist);
            if (!isBookmarked && !Boolean.TRUE.equals(join)  && !Boolean.TRUE.equals(pastJoinSession)  && isSaveASeat && !joinWaitlist) {
                sendGridMailPrepareService.sendSaveASeatEmail(user, session, event, isEventStaff, Boolean.FALSE);
            }
            isEventStaff = isSpeakerInSession || Boolean.TRUE.equals(isAdminOrStaff);


        } catch (NotAcceptableException e){
            log.info("registerUserFromPortal | isEventStaff : {} , userSession : {} ",isEventStaff, userSession);
            if(!e.getErrorCode().equalsIgnoreCase(EXCEED_WORKSHOP_SESSION_CAPACITY.getStatusCode()) && !e.getErrorCode().equalsIgnoreCase((REGISTER_MAX_LIMIT_REACHED.getStatusCode()))){
                log.info("absorbExceptionForAdminAndThrowErrorForAttendee | eventId : {} , sessionId : {} , userId : {}  , isEventStaff : {} errorCode : {} ",
                        event.getEventId(),session.getId(),user.getUserId(),isEventStaff,e.getErrorCode());
                if(!isEventStaff || MEET_UP.equals(session.getFormat())){
                    log.info("absorbExceptionForAdminAndThrowErrorForAttendee | throwing exception");
                    throw e;
                }

            }else{
                log.info("registerUserFromPortal | throwing exception");
                throw e;
            }
        }

        boolean isRegister = false;

        if (userSession != null && !joinWaitlist) {
            saveUserSession(join, pastJoinSession, userSession,  new Date());
            isRegister =  true;
        }
        if (userSession == null && isEventStaff && isNotMeetUpSession(session) && !joinWaitlist) {

            UserSessionDTO userSessionDTO = new UserSessionDTO();
            userSessionDTO.setUserId(user.getUserId());
            userSessionDTO.setSessionId(session.getId());
            userSessionDTO.setEventId(event.getEventId());
            userSessionDTO.setJoin(Boolean.TRUE.equals(join));
            userSessionDTO.setPastJoin(Boolean.TRUE.equals(pastJoinSession));
            userSession = toEntity(userSessionDTO);
            userSession = userSessionRepoService.save(userSession);

            isRegister =true;
        }

        if(userSession != null && isRegister){
            List<Map<String,String>> trackingDataList = new ArrayList<>();
            HashMap<String,String> trackingData = new HashMap<>();
            trackingData.put(Constants.FIRST_NAME,(userSession.getUser() != null)? userSession.getUser().getFirstName(): user.getFirstName());
            trackingData.put(Constants.LAST_NAME,(userSession.getUser() != null)? userSession.getUser().getLastName(): user.getLastName());
            trackingData.put(Constants.EMAIL,(userSession.getUser() != null)? userSession.getUser().getEmail(): user.getEmail());
            trackingDataList.add(trackingData);

            trayTrackingService.trackAttendeeSession(event,session,trackingDataList, join, pastJoinSession);
        }


        if(!isRegister && !joinWaitlist) {
            log.info("Registration failed for User {}  from portal in session {}", user.getUserId(), session.getId());
            throw new NotAcceptableException(REGISTER_FAILED);
        }else {
            if(joinWaitlist){
                log.info("User {} successfully added in waitlist from portal in session {}", user.getUserId(), session.getId());
            }else{
                log.info("User {} successfully register from portal in session {}", user.getUserId(), session.getId());
                afterTaskIntegrationTriggerService.userRegisterOrAttendSession(event, userSession, true);
                sessionCheckInLogService.createSessionCheckInLog(userSession, null, device);
            }
        }
        return true;
	}

    void validateTimeAccessElapsedForVirtual(Session session, Event event, User user,
                                                     boolean joinLive, boolean pastJoinSession,
                                                     Boolean isAdminOrStaff,
                                                     boolean isSpeakerInSession) {
        // Skip validation for unsupported formats, admins, or speakers
        if (!EnumSet.of(MAIN_STAGE, BREAKOUT_SESSION, WORKSHOP).contains(session.getFormat())
                || Boolean.TRUE.equals(isAdminOrStaff)
                || isSpeakerInSession) {
            return;
        }

        // Skip if user has already checked in
        boolean hasCheckedIn = userSessionRepoService.findBySessionIdAndUserIdAndEventId(session.getId(), user.getUserId(), event.getEventId()).stream().anyMatch(
                us -> (us.getCheckInStatus() == null || !CHECK_OUT.equals(us.getCheckInStatus()))
                        && us.getCheckInTime() != null);

        if (hasCheckedIn) {
            return;
        }

        // Skip if late join is allowed
        if (Boolean.TRUE.equals(virtualEventSettingsRepoService.isAllowLateJoin(event.getEventId()))) {
            return;
        }

        Integer allowedMinutesToJoinLate = sessionDetailsService.getAllowedMinutesToJoinLate(session);
        if (!NumberUtils.isNumberGreaterThanZero(allowedMinutesToJoinLate)) {
            return;
        }

        Date allowedEnterTime = DateUtils.getAddedMinutes(session.getStartTime(), allowedMinutesToJoinLate);
        if (DateUtils.getCurrentDate().after(allowedEnterTime)) {
            if (joinLive) {
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.TIME_ACCESS_ELAPSED_LIVE);
            } else if (pastJoinSession) {
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.TIME_ACCESS_ELAPSED_RECORDING);
            }
        }
    }


    public Long getConsumedTrackOrvalidateSessionTracksLimit(Long userId, Session session, Event event, Long ticketId) {
        log.info("getConsumedTrackOrvalidateSessionTracksLimit user {}, event {}, session {} and ticketId {}", userId, event.getEventId(), session.getId(), ticketId);
        User user = userService.findByUserId(userId);
        Optional<EventTickets> eventTicketsOptional = eventTicketsService.findEventTicketById(ticketId);
        if (!eventTicketsOptional.isPresent()) {
            log.info("Ticket is not present ticket id {} and event {} and user {}", ticketId, event.getEventId(), userId);
            return null;
        }

        EventTickets eventTickets = eventTicketsOptional.get();
        TicketingType ticketingType = eventTickets.getTicketingTypeId();

        if (!ticketingType.isTracksSessionsLimitAllowed()) {
            log.info("Session registration limits not enabled on the tracks for the ticket type {} and event {}", ticketingType.getId(), event.getEventId());
            return null;
        }

        List<Long> sessionTagTracksIds = sessionTagAndTrackRepo.findTagOrTrackIdsBySessionId(session.getId());
        if(CollectionUtils.isEmpty(sessionTagTracksIds)){
            return null;
        }

        List<TicketTypeTrackSessionLimits> ticketTypeTrackSessionLimits =
                ticketTypeTrackSessionLimitsRepository.findByTicketTypeIdAndEventIdAndTagTrackIdsIn(
                        ticketingType.getId(), event.getEventId(), sessionTagTracksIds);

        List<Long> noLimitTracks;
        List<Long> limitedTrackIds = new ArrayList<>();

        if (CollectionUtils.isEmpty(ticketTypeTrackSessionLimits)) {
            log.info("Session has not limit on the track session id {}, user id {} and ticket type id {}", session.getId(), user.getUserId(), ticketingType.getId());
            noLimitTracks = new ArrayList<>(sessionTagTracksIds);
        } else {
            limitedTrackIds = ticketTypeTrackSessionLimits.stream()
                    .map(TicketTypeTrackSessionLimits::getTagOrTrackId)
                    .collect(Collectors.toList());

            Set<Long> limitedTrackIdSet = new HashSet<>(limitedTrackIds);
            noLimitTracks = sessionTagTracksIds.stream()
                    .filter(trackId -> !limitedTrackIdSet.contains(trackId))
                    .collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(noLimitTracks)) {
            log.info("One limitless track is available in the session {} and user {} and ticket type {}", session.getId(), userId, ticketingType.getId());
            return null; // At least one track is limitless
        }

        List<Object[]> registeredSessionOfTrack = userSessionRepoService.getRegisteredSessionCountForUserByTracks(user, event, limitedTrackIds);
        Map<Long, Long> usedLimitMap = registeredSessionOfTrack.stream()
                .collect(Collectors.toMap(
                        row -> (Long) row[0],
                        row -> (Long) row[1]
                ));

        List<TrackLimitWithAvailability> availableTrackLimits = ticketTypeTrackSessionLimits.stream()
                .map(limit -> {
                    long trackId = limit.getTagOrTrackId();
                    long totalLimit = limit.getSessionLimit();
                    long used = usedLimitMap.getOrDefault(trackId, 0L);
                    long available = totalLimit - used;
                    return new TrackLimitWithAvailability(trackId, available);
                })
                .filter(track -> track.getAvailableLimit() > 0)
                .sorted(Comparator.comparingLong(TrackLimitWithAvailability::getAvailableLimit).reversed()) // reverse ordering based on the available limit
                .collect(Collectors.toList());

        if (availableTrackLimits.isEmpty()) {
            // All tracks are limited and no availability
            throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_REGISTRATION_NOT_ALLOWED_WITH_TRACK_LIMIT);
        }

        TrackLimitWithAvailability topTrack = availableTrackLimits.get(0);
        long maxAvailable = topTrack.getAvailableLimit();

        // Collect tied tracks with same available limit at the top
        List<Long> tiedTrackIds = availableTrackLimits.stream()
                .takeWhile(track -> track.getAvailableLimit() == maxAvailable)
                .map(TrackLimitWithAvailability::getTrackId)
                .collect(Collectors.toList());

        if (tiedTrackIds.size() == 1) {
            return tiedTrackIds.get(0);
        }

        List<Long> leastSessionTracks = sessionTagAndTrackRepo.findLeastSessionCountTrackIds(tiedTrackIds);
        return leastSessionTracks.isEmpty() ? null : leastSessionTracks.get(0);
    }


    /**
     * Validates if a user is allowed to register for a session based on event-level settings,
     * session capacity, user role, and overlapping session rules.
     */
    void validateRegisterCapacityAndConcurrentSessionRegistrationRules(Session session, Event event, Long userId) {

        validateConcurrentSessionRegistration(session, event, userId);

        if (!virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId())) {
            return;
        }

        User user = userService.findByUserId(userId);
        UserRole userRole = roStaffService.getUserRole(user, event, null);

        log.info("validateBookmarkCapacity | session {} event {}", session.getId(), event.getEventId());

        List<Long> sessionSpeaker = sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId());
        if (!Permissions.GtEq_EVENT_LEVEL_STAFF_PERMISSION.contains(userRole) && !sessionSpeaker.contains(userId)) {
            Integer registerCounts = userSessionRepoService.countBySessionIdAndTicketIdIsNotNullAndEventId(session.getId(), event.getEventId());
            log.info("validateBookmarkCapacity | register count {}, user {}", registerCounts, userId);
            if (session.getCapacity() != 0 && registerCounts >= session.getCapacity() && !userSessionRepoService.isUserRegisteredInSession(session.getId(), userId, event)) {
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_BOOKMARK_CAPACITY_EXCEEDED);
            }
        }

    }


    void validateRegisterCapacityAndConcurrentSessionRegistrationRulesForVirtual(Session session, Event event, Long userId, boolean isEventStaff, boolean isSpeakerInSession, boolean joinWaitlist) {

        validateConcurrentSessionRegistration(session, event, userId);

        if (!virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId())) {
            return;
        }

        log.info("validateBookmarkCapacity | session {} event {}", session.getId(), event.getEventId());

        if (!isEventStaff && !isSpeakerInSession) {
            Integer registerCounts = userSessionRepoService.countBySessionIdAndTicketIdIsNotNullAndEventId(session.getId(), event.getEventId());
            log.info("validateBookmarkCapacity | register count {}, user {}", registerCounts, userId);
            if (session.getCapacity() != 0 && registerCounts >= session.getCapacity() && !userSessionRepoService.isUserRegisteredInSession(session.getId(), userId, event)) {
                if(joinWaitlist){
                    // Don't throw exception, if join waitlist is enabled, for the session
                    log.info("Session capacity exceeded but waitlist is enabled for user {} in session {}", userId, session.getId());
                }else{
                    throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_BOOKMARK_CAPACITY_EXCEEDED);
                }
            }
        }

    }

    private void validateConcurrentSessionRegistration(Session session, Event event, Long userId){
        EventLevelSettings eventLevelSettings = rOEventLevelSettingsRepo
                .findByEventId(event.getEventId())
                .orElseGet(EventLevelSettings::new);

        SessionTimeConflictsEnum conflictRule = eventLevelSettings.getSessionTimeConflicts();

        if (SessionTimeConflictsEnum.ALLOW_OVERRIDE.equals(conflictRule) && !eventLevelSettings.isAllowSessionOverlap()) {
            return;
        }

        // If override is NOT allowed, we need to check session capacity
        boolean checkCapacity = SessionTimeConflictsEnum.CANNOT_ALLOW_OVERRIDE_CAPACITY.equals(conflictRule);

        // Fetch all sessions this user has already registered for (only those that require registration rules checks)
        List<UserSession> registeredSessions = userSessionRepoService
                .findAllByEventIdAndUserIdAndSessionStatusesAndCapacity(session, event, userId, checkCapacity);

        List<Session> sessions = registeredSessions.stream()
                .map(UserSession::getSession)
                .collect(Collectors.toList());

        // Get start and end time of the session the user is trying to register for
        long newSessionStart = session.getStartTime().getTime();
        long newSessionEnd = session.getEndTime().getTime();

        boolean checkOverlap = SessionTimeConflictsEnum.ALLOW_OVERRIDE.equals(conflictRule) && eventLevelSettings.isAllowSessionOverlap();
        boolean overlapAllowed = SessionTimeConflictsEnum.ALLOW_OVERRIDE.equals(conflictRule);

        long allowedMillis = checkOverlap
                ? eventLevelSettings.getConcurrentSessionOverlapTime() * 60L * 1000
                : 0;

        // Check for overlap with each already registered session
        for (Session registeredSession : sessions) {
            long regStart = registeredSession.getStartTime().getTime();
            long regEnd = registeredSession.getEndTime().getTime();

            // fetching actual overlap millis based on the registered session's time and requested for register session's time.
            long overlapMillis = Math.min(regEnd, newSessionEnd) - Math.max(regStart, newSessionStart);
            if (overlapMillis <= 0) {
                continue; // No overlap, skip
            }

            if (checkOverlap && overlapMillis > allowedMillis) {
                // If overlapping time exceeds allowed time, disallow registration
                log.info("Registration blocked due to exceeding overlap. User: {}, Session: {}, OverlapMillis: {}", userId, session.getId(), overlapMillis);
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_REGISTRATION_NOT_ALLOWED);
            }
            if (!overlapAllowed) {
                // Rule: CANNOT_ALLOW_OVERRIDE → disallow any overlapping session
                log.info("Registration blocked due to session overlap. User: {}, Session: {}, ConflictingSession: {}", userId, session.getId(), registeredSession.getId());
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_REGISTRATION_NOT_ALLOWED);
            }
        }
    }


    private void validateTicketTypeAndEventAdminOrStaff(User user, Session session, Event event, boolean isAdminOrStaff, boolean isSpeakerInSession, boolean isSpeakerInEvent) {
        List<Long> userTicketTypes = eventTicketsRepoService.getAllEventTicketTypeIdsByEventUserANDNotCanceled(event, user);

        boolean isValidUser = sessionService.isValidUserForSession(session, userTicketTypes, isSpeakerInSession, isSpeakerInEvent);

        if (!isAdminOrStaff && !isValidUser) {
            log.warn("registerUserFromPortal EventType {} SessionId {}", event.getEventFormat(), session.getId());

            if (CollectionUtils.isEmpty(userTicketTypes)) {
                if (isSpeakerInEvent || staffService.isEventExhibitorAdminOrLeadretriever(event, user)) {
                    throw new NotAcceptableException(STAFF_NOT_REGISTER_IN_SESSION);
                }
                throw new NotAcceptableException(REGISTER_FAILED);
            } else if (!(IN_PERSON.equals(event.getEventFormat()) || SESSION_IN_PERSON_HYBRID.contains(session.getSessionTypeFormat()))) {
                throw new NotFoundException(NotFoundException.SessionNotFound.TICKET_TYPE_DOES_NOT_GRANT_ACCESS);
            }
        } else if (isAdminOrStaff && MEET_UP.equals(session.getFormat())) {
            if (CollectionUtils.isEmpty(userTicketTypes)) {
                throw new NotAcceptableException(REGISTER_FAILED);
            } else {
                List<Long> userTicketTypesIds;
                userTicketTypesIds = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user);
                if(CollectionUtils.isEmpty(userTicketTypesIds)){
                    log.warn("networkMatchesUsersDTO | (for Admin) user can not access this hidden session with no ticket types selected {}", session.getId());
                    throw new NotFoundException(NotFoundException.SessionNotFound.TICKET_TYPE_DOES_NOT_GRANT_ACCESS);
                }
            }
        }
    }

    protected void saveUserSession(Boolean join, Boolean pastJoinSession, UserSession userSession, Date checkInTime) {
        if(Boolean.TRUE.equals(join) && (userSession.getCheckInTime() == null || CHECK_OUT.equals(userSession.getCheckInStatus()))){
            userSession.setCheckInStatus(CHECK_IN_AVAILABLE);
            userSession.setCheckInTime(checkInTime);
            userSessionRepoService.save(userSession);
        }else if(Boolean.FALSE.equals(join) && Boolean.TRUE.equals(pastJoinSession) && (userSession.getCheckInTime() == null || CHECK_OUT.equals(userSession.getCheckInStatus()))){
            userSession.setCheckInStatus(PAST_SESSION_CHECK_IN);
            userSession.setCheckInTime(checkInTime);
            userSessionRepoService.save(userSession);
        }
    }



    @Override
    public boolean isEventStaffOrSpeaker(User user, Session session, Event event) {
        return sessionSpeakerService.isUserSpeakerInSession(user.getUserId(), session.getId())
                ||   Permissions.STAFF_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(user,event));
    }

    public void validateTimeAccessElapsed(Session session, Event event, User user, boolean joinLive, boolean pastJoinSession, boolean offlineData) {
        if (!Arrays.asList(MAIN_STAGE, BREAKOUT_SESSION, WORKSHOP).contains(session.getFormat()) || isEventStaffOrSpeaker(user,session,event)){
	        return;
        }
        List<UserSession> userSessionList = userSessionRepoService.findBySessionIdAndUserIdAndEventId(session.getId(), user.getUserId(), event.getEventId());

        long checkInUserCount = userSessionList.stream().filter(e -> (!CHECK_OUT.equals(e.getCheckInStatus()) || e.getCheckInStatus() == null) && e.getCheckInTime() != null).count();

        if(checkInUserCount==0) {
            boolean isAllowLatejoin = virtualEventSettingsRepoService.isAllowLateJoin(event.getEventId());
            if (!isAllowLatejoin) {
                Integer allowedMinutesToJoinLate = sessionDetailsService.getAllowedMinutesToJoinLate(session);
                if (NumberUtils.isNumberGreaterThanZero(allowedMinutesToJoinLate)) {
                    Date allowedEnterTime = DateUtils.getAddedMinutes(session.getStartTime(), allowedMinutesToJoinLate);
                    if (DateUtils.getCurrentDate().compareTo(allowedEnterTime) > 0 && !offlineData) {
                        if (joinLive) {
                            throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.TIME_ACCESS_ELAPSED_LIVE);
                        }
                        if (pastJoinSession) {
                            throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.TIME_ACCESS_ELAPSED_RECORDING);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void registerUserFromHost(UserSessionDTO userSessionDTO, Session session, Event event) {
        List<Map<String,String>> registeredUser = new ArrayList<>();
        log.info("registerUserFromHost | eventId : {} | sessionId : {} , userSessionDTO : {}", event.getEventId(), session.getId(), userSessionDTO);
        List<EventTicketsIdsDto> eventTicketIdsAndTicketingTypeId = eventTicketsService.findEventTicketIdAndTicketingTypeIdByHolderUserIdAndEventId(userSessionDTO.getUserId(), userSessionDTO.getEventId());
        List<Long> eventTicketIds = eventTicketIdsAndTicketingTypeId.stream().map(EventTicketsIdsDto::getEventTicketsId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> ticketingTypeId = eventTicketIdsAndTicketingTypeId.stream().map(EventTicketsIdsDto::getTicketingTypeId).filter(Objects::nonNull).collect(Collectors.toList());
        validateRegisterCapacityAndConcurrentSessionRegistrationRules(session,event,userSessionDTO.getUserId());
        Integer registeredCount = countRegisteredBySessionId(session.getId());

        boolean allowSessionBookmarkCapacity = roVirtualEventService.hasSessionBookmarkCapacity(event.getEventId());
        log.info("registerUserFromHost | eventId : {} | sessionId : {} | session capacity : {} | registeredCount : {}  | allowSessionBookmarkCapacity : {}",
                event.getEventId(), session.getId(), session.getCapacity(), registeredCount,allowSessionBookmarkCapacity);

        if (allowSessionBookmarkCapacity && (null != registeredCount && session.getCapacity() > 0 && session.getCapacity() <= registeredCount)) {
            throw new NotAcceptableException(EXCEED_SESSION_CAPACITY);
        }

        List<Long> ticketTypesThatCanBeRegisteredList = convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered());
        List<Long> matchTicket = ticketTypesThatCanBeRegisteredList.stream().filter(ticketingTypeId::contains).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(matchTicket) && !IN_PERSON.equals(event.getEventFormat())){
            throw new NotAcceptableException(TICKET_TYPE_NOT_MATCHED);
        }

        Iterator<Long> iterator = eventTicketIds.iterator();
        while (iterator.hasNext()) {
            Long ticketId = iterator.next();
            List<Long> sessionIds = findByEventIdAndUserId(userSessionDTO.getEventId(), userSessionDTO.getUserId());
            if (sessionIds.contains(userSessionDTO.getSessionId())) {
                log.info("User is already registered in session {id} ={}" , userSessionDTO.getSessionId());
                return;
            }
            sessionIds.add(userSessionDTO.getSessionId());

            log.info("UserSessionServiceImpl || registerUserFromHost ||  sessionId {}, eventTicketId {} ",session.getId() ,ticketId);

            // Query always give a single result
            List<Object[]> ticketingTypeAttribute = eventTicketsService.findTicketingTypeById(ticketId);
            Integer maxRegisterUserAllowed = null != ticketingTypeAttribute.get(0)[1] ? (Integer) ticketingTypeAttribute.get(0)[1] : null;
            log.info("UserSessionServiceImpl || registerUserFromHost  maxSessionRegisterUser {} ", ticketingTypeId,maxRegisterUserAllowed);

            if(maxRegisterUserAllowed!=null &&
                    userSessionRepoService.countByEventTicketId(ticketId).intValue()
                            >= maxRegisterUserAllowed){
                NotAcceptableException.SessionSpeakerExceptionMsg exceptionMsg = REGISTER_MAX_LIMIT_REACHED;
                String errorMessageTemplate = Constants.REGISTER_MAX_LIMIT_REACHED;
                String errorMessage = errorMessageTemplate.replace(Constants.NUMBER_OF_SESSION_PERMITTED, String.valueOf(maxRegisterUserAllowed));
                Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
                defaultMessageParamMap.put(Constants.NUMBER_OF_SESSION_PERMITTED,String.valueOf(maxRegisterUserAllowed));
                exceptionMsg.setErrorMessage(errorMessage);
                exceptionMsg.setDeveloperMessage(errorMessage);
                exceptionMsg.setDefaultMessage(Constants.REGISTER_MAX_LIMIT_REACHED);
                exceptionMsg.setDefaultMessageParamMap(defaultMessageParamMap);
                throw new NotAcceptableException(exceptionMsg);
            }

            UserSession userSession = toEntity(userSessionDTO);
            Long consumedTagTrackId = getConsumedTrackOrvalidateSessionTracksLimit(userSessionDTO.getUserId(), session, event, ticketId);
            userSession.setEventTicketId(ticketId);
            userSession.setConsumedTagTrackId(consumedTagTrackId);
            userSession = userSessionRepoService.save(userSession);
            redisCacheService.clearSessionCache(userSession.getEventId());
            afterTaskIntegrationTriggerService.userRegisterOrAttendSession(event, userSession, false);
            User user = userService.findByUserId(userSession.getUserId());
            HashMap<String,String> trackingData = new HashMap<>();
            trackingData.put(Constants.FIRST_NAME,(userSession.getUser() != null)? userSession.getUser().getFirstName():user.getFirstName());
            trackingData.put(Constants.LAST_NAME,(userSession.getUser() != null)? userSession.getUser().getLastName(): user.getLastName());
            trackingData.put(Constants.EMAIL,(userSession.getUser() != null)? userSession.getUser().getEmail(): user.getEmail());

            registeredUser.add(trackingData);

            trayTrackingService.trackAttendeeSession(event,session,registeredUser, Boolean.FALSE, Boolean.FALSE);
            break;
        }
    }

    @Override
    @Transactional
    public UploadSessionAttendeeResponseContainer registerUserFromHostInBulk(Long sessionId, MultipartFile inputFile, Event event,String languageCode) {

        try {
            UserSessionDTO userSessionDTO = new UserSessionDTO();
            userSessionDTO.setSessionId(sessionId);
            userSessionDTO.setEventId(event.getEventId());
            Session session = sessionRepoService.getSessionById(userSessionDTO.getSessionId(), event);
            log.info("registerUserFromHostInBulk using eventId {} , sessionId {} , userId {} , sessionVisibilityType {}",event.getEventId(),session.getId(),userSessionDTO.getUserId(),session.getSessionVisibilityType());
            if (session.getSessionVisibilityType().equals(SessionVisibilityType.PUBLIC)) {
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_IS_NOT_PRIVATE);
            }
            return handleRegisterUserFromHostInBulk(inputFile, userSessionDTO, session,languageCode, event);
        } catch (IOException e) {
            log.info("registerUserFromHostInBulk| ", e);
            throw new NotAcceptableException(e);
        }
    }

    protected UploadSessionAttendeeResponseContainer handleRegisterUserFromHostInBulk(MultipartFile inputFile, UserSessionDTO userSessionDTO, Session session, String languageCode, Event event) throws IOException { //NOSONAR
        UploadSessionAttendeeResponseContainer returnValue = new UploadSessionAttendeeResponseContainer();
        List<Map<String, String>> trackingUser = new ArrayList<>();
        int sessionCapacity = session.getCapacity();
        try (CSVReader cr = new CSVReader(new BufferedReader(new InputStreamReader(inputFile.getInputStream())))) {
            String[] header = cr.readNext();
            log.info("handleRegisterUserFromHostInBulk with  eventId {} , sessionId {} , headers {}", userSessionDTO.getEventId(), session.getId(), header);
            if ((null != header && header.length == 3 && isValidHeaders(header))) {
                String[] nextItem;

                int totalElement = 0;
                while ((nextItem = cr.readNext()) != null) {
                    totalElement++;
                    if (nextItem.length == 3) {
                        String firstName = nextItem[0];
                        String lastName = nextItem[1];
                        String email = nextItem[2];
                        log.info(" elementNo: {} in CSV and register attendee's email: {}", totalElement, email);
                        User userFromDB = roUserService.findByEmail(email);
                        if (null != userFromDB) {
                            userSessionDTO.setUserId(userFromDB.getUserId());

                            if (sessionCapacity > 0) {
                                int registeredCount = countRegisteredBySessionId(session.getId());
                                if (registeredCount >= sessionCapacity) {
                                    returnValue.addInvalidAttendees(firstName, lastName, 3, languageCode);
                                    continue;
                                }
                            }
                            try {
                                validateRegisterCapacityAndConcurrentSessionRegistrationRules(session, event, userFromDB.getUserId());
                            } catch (NotAcceptableException exception) {
                                if (NotAcceptableException.SessionExceptionMsg.SESSION_REGISTRATION_NOT_ALLOWED.getErrorMessage().equalsIgnoreCase(exception.getErrorMessage())) {
                                    returnValue.addInvalidAttendees(firstName, lastName, 8, languageCode);
                                }
                                continue;
                            }
                            List<Long> sessionIds = findByEventIdAndUserId(userSessionDTO.getEventId(), userSessionDTO.getUserId());
                            if (sessionIds.contains(userSessionDTO.getSessionId())) {
                                returnValue.addInvalidAttendees(firstName, lastName, 4, languageCode);
                            } else {
                                sessionIds.add(userSessionDTO.getSessionId());
                                registerUserToSessionWithTicketTypes(userSessionDTO, session, returnValue, firstName, lastName, email, languageCode, trackingUser, event);
                            }
                        } else {
                            returnValue.addInvalidAttendees(firstName, lastName, 0, languageCode);
                        }
                    } else {
                        returnValue.addInvalidAttendees(String.join(", ", nextItem), 7, languageCode);
                    }
                }
                if (totalElement == 0) {
                    throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.CAN_NOT_UPLOAD_EMPTY_FILE);
                } else {
                    trayTrackingService.trackAttendeeSession(session.getEvent(), session, trackingUser, Boolean.FALSE, Boolean.FALSE);
                    returnValue.setMessage(!StringUtil.isBlank(languageCode) && "EN".equalsIgnoreCase(languageCode) ?
                            ((totalElement - returnValue.getInvalidAttendees().size()) + " of " + totalElement + " Attendees were successfully registered for the session.") :
                            ((totalElement - returnValue.getInvalidAttendees().size()) + " de " + totalElement + " Los asistentes se registraron correctamente para la sesión."));
                }
            } else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_HEADER_NOT_CORRECT);
            }
        }
        log.info("Invalid attendees size {}", returnValue.getInvalidAttendees().size());
        redisCacheService.clearSessionCache(session.getEventId());
        return returnValue;
    }

    protected boolean isValidHeaders(String[] header) {
        return header[0].trim().equalsIgnoreCase(Constants.FIRST_NAME)
                && header[1].trim().equalsIgnoreCase(Constants.LAST_NAME)
                && header[2].trim().equalsIgnoreCase(Constants.EMAIL);
    }

    public boolean isNotMeetUpSession(Session session) {
        return !MEET_UP.equals(session.getFormat());
    }

    protected boolean isNotWorkshopSession(Session session) {
        return !WORKSHOP.equals(session.getFormat());
    }

    boolean isWorkshopSession(Session session) {
        return WORKSHOP.equals(session.getFormat());
    }

    @Override
    public void registerSpeakerUser(Long speakerUserId, Long sessionId, Event event) {
        Session session = sessionRepoService.getSessionById(sessionId, event);
        if (null != session && isWorkshopSession(session)) {
            User speakerUser = userService.findById(speakerUserId);
            registerUserFromPortal(speakerUser, session, false, event, false, true, null, false, Boolean.FALSE, false);
        }
    }

    @Override
    @Transactional
    public void unRegisterSpeakerUser(Long speakerUserId, Long sessionId, Event event) {
        Session session = sessionRepoService.getSessionById(sessionId, event);
        User speakerUser = userService.findById(speakerUserId);
        if (null != session && !sessionService.isValidUser(speakerUser, event, session)) {
            unRegisterUserWithoutEventTicket(sessionId, speakerUserId, event);
        }
    }


    public void validateCapacity(Session session, Event event, Long eventTicketId,boolean isFromStaffCheckIn) {
        Long registeredUserCount;
        List<Long> speakerUserIdsBySessionId = sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId());
        TicketTypeFormat ticketTypeFormat = eventTicketsService.getTicketTypeFormatByEventTicketId(eventTicketId);

        if (null == environmentProfile || Constants.STAGE_STRING.equalsIgnoreCase(environmentProfile) || Constants.ENV_PROD.equals(environmentProfile)) {
            registeredUserCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatus(session.getId(), event.getEventId());
        } else {
            List<TicketTypeFormat> ticketTypeFormats = TicketTypeFormat.VIRTUAL.equals(ticketTypeFormat) ? Collections.singletonList(TicketTypeFormat.VIRTUAL) : Arrays.asList(TicketTypeFormat.HYBRID, TicketTypeFormat.IN_PERSON);
            registeredUserCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatusAndTicketTypeFormat(session.getId(), event.getEventId(), ticketTypeFormats);
        }
        if (isWorkshopSession(session) && registeredUserCount >= 250) {
            throw new NotAcceptableException(EXCEED_WORKSHOP_SESSION_CAPACITY);
        }
        if (!session.getFormat().equals(MEET_UP)) {
            List<Long> allStaffAndAdminUserIdList = staffService.findAllStaffUserIdsByEvent(event);
            List<Long> allStaffAndAdminUserIds = allStaffAndAdminUserIdList.stream()
                    .filter(item -> !speakerUserIdsBySessionId.contains(item))
                    .collect(Collectors.toList());
            if (!allStaffAndAdminUserIds.isEmpty()) {
                if (!isFromStaffCheckIn) {
                    if (null == environmentProfile || Constants.STAGE_STRING.equalsIgnoreCase(environmentProfile) || Constants.ENV_PROD.equals(environmentProfile)) {
                        Long staffAdminCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndUserIdIn(session.getId(), event.getEventId(), allStaffAndAdminUserIds);
                        registeredUserCount -= staffAdminCount;
                    } else {
                        List<TicketTypeFormat> ticketTypeFormats = TicketTypeFormat.VIRTUAL.equals(ticketTypeFormat) ? Collections.singletonList(TicketTypeFormat.VIRTUAL) : Arrays.asList(TicketTypeFormat.HYBRID, TicketTypeFormat.IN_PERSON);
                        Long staffAdminCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndUserIdInAndTicketTypeFormat(session.getId(), event.getEventId(), allStaffAndAdminUserIds, ticketTypeFormats);
                        registeredUserCount -= staffAdminCount;
                    }
                } else {
                    Long staffAdminCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatusAndUserIdIn(session.getId(), event.getEventId(), allStaffAndAdminUserIds);
                    registeredUserCount -= staffAdminCount;
                }
            }
        }
        if (!speakerUserIdsBySessionId.isEmpty()) {
            if (!isFromStaffCheckIn) {
                if (null == environmentProfile || Constants.STAGE_STRING.equalsIgnoreCase(environmentProfile) || Constants.ENV_PROD.equals(environmentProfile)) {
                    Long speakerCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndUserIdIn(session.getId(), event.getEventId(), speakerUserIdsBySessionId);
                    registeredUserCount -= speakerCount;
                } else {
                    List<TicketTypeFormat> ticketTypeFormats = TicketTypeFormat.VIRTUAL.equals(ticketTypeFormat) ? Collections.singletonList(TicketTypeFormat.VIRTUAL) : Arrays.asList(TicketTypeFormat.HYBRID, TicketTypeFormat.IN_PERSON);
                    Long speakerCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndUserIdInAndTicketTypeFormat(session.getId(), event.getEventId(), speakerUserIdsBySessionId, ticketTypeFormats);
                    registeredUserCount -= speakerCount;
                }
            } else {
                if (!speakerUserIdsBySessionId.isEmpty()) {
                    Long speakerCount = userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatusAndUserIdIn(session.getId(), event.getEventId(), speakerUserIdsBySessionId);
                    registeredUserCount -= speakerCount;
                }
            }
        }

        int sessionCapacity = getSessionCapacity(session, ticketTypeFormat);
        if (sessionCapacity > 0 && sessionCapacity <= registeredUserCount) {
            throw new NotAcceptableException(EXCEED_SESSION_CAPACITY);
        }
    }

    private int getSessionCapacity(Session session, TicketTypeFormat ticketingTypeFormat) {
        if ((SessionTypeFormat.HYBRID.equals(session.getSessionTypeFormat()) && Constants.ENV_DEV.equals(environmentProfile))
                || Constants.ENV_LOCAL.equals(environmentProfile)) {
            return TicketTypeFormat.VIRTUAL.equals(ticketingTypeFormat) ? session.getCapacity() : session.getAllowSeats();
        } else {
            return session.getCapacity();
        }
    }


    public UserSession tryAndRegisterUserWithDifferentTicketType(User user, Session session, Event event, Boolean join, Boolean isAdminOrStaff, boolean isBookmarked, boolean isSpeakerInSession, boolean joinWaitlist) {
        UserSession userSession = null;
        List<UserSession> sessionIds = userSessionRepoService.findBySessionIdAndUserIdAndEventId(session.getId(), user.getUserId(),event.getEventId());
        if(!sessionIds.isEmpty()){
            log.info("User is already registered in session {} userId {} " ,session.getId(), user.getUserId());
            userSession = sessionIds.get(0);
        }

        if (userSession != null && ((WAITLISTED.equals(userSession.getSessionStatus()) && !isBookmarked) || (isUserAlreadyCheckedInSession(userSession) && !isUserBookMarkSessionWithInPersonTicket(userSession) && !isBookmarked))) {
            return userSession;
        }

        if(Boolean.TRUE.equals(!isAdminOrStaff) && SessionVisibilityType.PRIVATE.equals(session.getSessionVisibilityType()) && sessionIds.isEmpty()){
            throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_IS_PRIVATE);
        }
        if (isSpeakerInSession) {
            if (userSession == null) {
                UserSessionDTO userSessionDTO = new UserSessionDTO();
                userSessionDTO.setUserId(user.getUserId());
                userSessionDTO.setSessionId(session.getId());
                userSessionDTO.setEventId(event.getEventId());
                userSessionDTO.setJoin(Boolean.TRUE.equals(join));
                if (isBookmarked) {
                    userSessionDTO.setBookmarked(true);
                }
                userSession = toEntity(userSessionDTO);
            } else {
                if (isBookmarked) {
                    userSession.setBookmarked(true);
                } else {
                    userSession.setSessionStatus(REGISTERED);
                    userSession.setRegistrationDate(new Date());
                }
            }
            return userSessionRepoService.save(userSession);
        }

        return tryAndRegisterInSessionWithTicketType(user, session, event, userSession, isBookmarked, isAdminOrStaff, joinWaitlist);
    }

    private UserSession tryAndRegisterInSessionWithTicketType(User user, Session session, Event event, UserSession userSession, boolean isBookmarked, boolean isAdminOrStaff, boolean joinWaitlist) {
        List<Long> eventTicketIds = eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(user.getUserId(), event.getEventId());
        log.info("tryAndRegisterInSessionWithTicketType |  eventTicketIds : {} , userSession : {}",eventTicketIds,userSession);
        if (isAdminOrStaff && eventTicketIds.isEmpty()){
            userSession = bookmarkAndRegisterInSessionForAdminOrStaffWithoutEventTicket(user, session, event, userSession, isBookmarked);
        }
        Iterator<Long> iterator = eventTicketIds.iterator();
        while(iterator.hasNext()) {
            Long id = iterator.next();
            try {
                // If joinWaitlist is true, directly call waitlistUser without other validations
                if (joinWaitlist) {
                    if (userSession == null) {
                        userSession = waitlistUser(id, user, session, event);
                    } else if (userSession.isBookmarked() || BOOKMARKED.equals(userSession.getSessionStatus())) {
                        userSession.setSessionStatus(WAITLISTED);
                        userSession.setRegistrationDate(new Date());
                        userSessionRepoService.save(userSession);
                    }
                    break;
                }

                if (!isBookmarked) {
                    // If the user joins or registers for a session they have already registered for the same session, return the user's session directly.
                    if (userSession!=null && REGISTERED.equals(userSession.getSessionStatus()) && userSession.getSessionId().equals(session.getId()) && userSession.getUserId().equals(user.getUserId())) {
                        break;
                    }
                    validateCapacityAndTicketType(userSession, id, session, event);
                }
                if(userSession == null) {
                    userSession = isBookmarked ? bookmarkedUser(id, user, session, event) : registerUser(id, user, session, event);
                } else {
                    if (!isBookmarked) {
                        User registerUser = roUserService.findByEmail(user.getEmail());
                        log.info("tryAndRegisterInSessionWithTicketType | register user {}", registerUser.getUserId());
                        validateMatchingTicketType(registerUser,session,id,event);
                        Long consumedTagTrackId = getConsumedTrackOrvalidateSessionTracksLimit(registerUser.getUserId(), session, event, id);
                        userSession.setSessionStatus(REGISTERED);
                        userSession.setRegistrationDate(new Date());
                        userSession.setConsumedTagTrackId(consumedTagTrackId);
                        userSessionRepoService.save(userSession);
                    } else {
                        userSession.setBookmarked(true);
                        userSessionRepoService.save(userSession);
                    }
                }
                break;
            } catch (NotAcceptableException | NotFoundException ex) {
                log.info("Error while registration of {} , with ticket id {} , is next element is present ? : {} . Try next registration. Exception: {} ", user.getEmail(), id, iterator.hasNext(), ex.getErrorMessage());
                if (!iterator.hasNext() || (userSession != null && REGISTERED.equals(userSession.getSessionStatus()))) {
                    log.info("Failed to register with tickets {}. No more available ticket types to check for {}.", eventTicketIds, user.getEmail());
                    throw ex;
                }
                if(ex.getErrorCode().equalsIgnoreCase(NotFoundException.SessionNotFound.TICKET_TYPE_DOES_NOT_GRANT_ACCESS.getStatusCode())) {
                    log.info("Failed to register with eventTicketId {}. Ticket type does not have access to this session {} for user {}.", id, session.getId(), user.getEmail());
                    throw ex;
                }
            }  catch (Exception ex) {
                log.info("Error while registration of {}, with ticket id {}. Try next registration. Exception {}", user.getEmail(), id, ex.getMessage());
            }
        }
        return userSession;
    }

    private UserSession bookmarkAndRegisterInSessionForAdminOrStaffWithoutEventTicket(User user, Session session, Event event, UserSession userSession, boolean isBookmarked) {
        if(userSession == null) {
            if (isBookmarked) {
                userSession = bookmarkedUser(null, user, session, event);
            } else {
                UserSessionDTO userSessionDTO = new  UserSessionDTO ();
                userSessionDTO.setUserId(user.getUserId());
                userSessionDTO.setSessionId(session.getId());
                userSessionDTO.setEventId(event.getEventId());
                userSession = toEntity(userSessionDTO);
            }
            log.info("userSession is null. Admin user with id: {} bookmarked in session with id: {} Bookmark status: {}", user.getUserId(), session.getId(), isBookmarked);
        } else {
            if (!isBookmarked) {
                userSession.setSessionStatus(REGISTERED);
                userSession.setRegistrationDate(new Date());
            } else {
                userSession.setBookmarked(true);
            }
            log.info("userSession is not null. Admin user with id: {} updated session with id: {} bookmark status: {}", user.getUserId(), session.getId(), isBookmarked);
        }
       return userSessionRepoService.save(userSession);
    }

    private UserSession bookmarkedUser(Long eventTicketId, User user, Session session, Event event) {
        UserSessionDTO userSessionDTO = new  UserSessionDTO ();
        userSessionDTO.setUserId(user != null ? user.getUserId() :eventTicketsService.findUserIdByEventTicketId(eventTicketId));
        userSessionDTO.setSessionId(session.getId());
        userSessionDTO.setEventId(event.getEventId());
        userSessionDTO.setBookmarked(true);
        UserSession userSession = toEntity(userSessionDTO);
        userSession.setEventTicketId(eventTicketId);
        log.info("Bookmarked user {} in session {} with eventTicketId {}", userSessionDTO.getUserId(), session.getId(), eventTicketId);
        return userSessionRepoService.save(userSession);
    }

    private void validateCapacityAndTicketType(UserSession userSession, Long id, Session session, Event event) {
        boolean isUserBookMark = false;
        if(userSession != null && userSession.getEventTicketId() != null && !BOOKMARKED.equals(userSession.getSessionStatus())) {
            id = userSession.getEventTicketId();
            isUserBookMark = true;
        }
        EventTickets eventTickets = eventTicketsRepoService.getEventTicketByEventTicketIdAndNotCanceled(id);

        validateCapacity(session, event, id,false);

        if(session.getSessionTypeFormat()!=null || !StringUtils.isEmpty(session.getSessionTypeFormat())) {
            boolean isTicketAllowed = isUserCanBookmarkOrCheckInSession(event, session, eventTickets.getTicketingTypeId().getTicketTypeFormat(), false, isUserBookMark);
            if (!isTicketAllowed) {
                throw new NotFoundException(NotFoundException.SessionNotFound.TICKET_TYPE_DOES_NOT_GRANT_ACCESS);
            }
        }
    }

    public UserSession tryAndRegisterUserWithEventTicketId(User user, Session session, Event event, Long eventTicketId) {
        log.info("Try to register user {} in session {} with eventTicketId {}", user.getEmail(), session.getId(), eventTicketId);
        UserSession userSession = null;
        userSession = userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(session.getId(), user.getUserId(), event.getEventId(), eventTicketId);
        UserSession uSession = userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketId(session.getId(),user.getUserId(),event.getEventId(),eventTicketId);
        try {
            if ( uSession != null && uSession.getEventTicketId().equals(eventTicketId) && isUserAlreadyCheckedInSession(uSession)) {
                throw new NotAcceptableException(USER_ALREADY_CHECK_IN_SESSION_WITH_TICKET);
            }else if(userSession == null){
                validateCapacity(session, event, eventTicketId, true);
                userSession = registerUser(eventTicketId, user, session, event);
            }
        } catch (NotAcceptableException ex) {
            log.info("Failed to register with tickets {}. No more available ticket types to check for {}.", eventTicketId, user.getEmail());
            throw ex;

        } catch (Exception ex) {
            log.info("Error while registration of {}, with ticket id {}. Try next registration. Exception {}", user.getEmail(), eventTicketId, ex);
        }


        return userSession;
    }

    public  boolean isUserAlreadyCheckedInSession(UserSession userSession) {
        return userSession.getCheckInTime() != null && !SESSION_CHECKOUT_STATUS.contains(userSession.getCheckInStatus());
    }

    private boolean isUserBookMarkSessionWithInPersonTicket(UserSession userSession) {
        if(userSession.getEventTicketId() != null && userSession.getEventTickets() != null && userSession.getEventTickets().getTicketingTypeId() != null) {
            TicketTypeFormat ticketTypeFormat = userSession.getEventTickets().getTicketingTypeId().getTicketTypeFormat();
            return TicketTypeFormat.IN_PERSON.equals(ticketTypeFormat);
        }
        return false;
    }

    @Override
	public List<Long> getUserSessionByUserId(Long userId, Long eventId){
	    return userSessionRepoService.findEventTicketIdByUserIdAndEventId(userId, eventId);
    }

	@Override
	public List<Long> findByEventIdAndUserId(long eventId, Long userId) {
		return userSessionRepoService.findByEventIdAndUserId(eventId, userId);
	}

    @Override
    public Long getRegisteredSessionCountForUser(long eventId, Long userId) {
        return userSessionRepoService.userSessionCount(eventId, userId);
    }

    @Override
    @Transactional
    public void deleteBySessionId(Long sessionId) {
        userSessionRepoService.updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);
    }

	@Override
	public List<Long> getEventTicketIdsByEventIdAndUserIdAndSessionPuchaserId(long eventId, Long userId, Long sessionId) {
		return userSessionRepoService.getEventTicketIdsByEventIdAndPurchaserUserIdAndSessionId(eventId, userId, sessionId);
    }

	@Override
	public List<UserSession> getAllUserSessionBySessionId(long sessionId) {
		return userSessionRepoService.findAllBySessionId(sessionId);
	}

    @Override
    public List<RegisterdHolderUsers> findSessionAttendedUserInfo(long sessionId) {
	    log.info("Start findSessionAttendedUserInfo for sessionId {}",sessionId);
        List<RegisterdHolderUsers> registerdHolderUsers = userSessionRepoService.findRegisteredUserBySessionId(sessionId);
        Session session = sessionRepoService.findById(sessionId).orElseThrow(() -> new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND));
        double duration = sessionDetailsService.getSessionVideoDurationBySession(session);

        List<UserSessionEngagementDto> userSessionEngagementDtoList = consolidatedAnalyticsService.getUserSessionEngagementDetails(session.getEvent(), sessionId);
        Map<Long , Double> userSessionEngagementMap = userSessionEngagementDtoList.stream()
                .collect(Collectors.toMap(UserSessionEngagementDto::getUserId, UserSessionEngagementDto::getLiveWatchTime));

        List<RegisterdHolderUsers> filteredData = new ArrayList<>();
        registerdHolderUsers.forEach(registerUser -> {
            if(registerUser.isAttended()){
                String engagement = "0%";
                double watchTime = userSessionEngagementMap.getOrDefault(registerUser.getUserId(), 0.0);
                if ( duration > 0) {
                    Double engagePercentage = DoubleHelper.roundValueTwoDecimal((watchTime * 100 / duration));
                    engagement = (engagePercentage > 100 ? 100 : engagePercentage) + "%";
                }
                registerUser.setEngagement(engagement);
                filteredData.add(registerUser);
            }
        });
        log.info("end findSessionAttendedUserInfo for sessionId {} and size is {}",sessionId,filteredData.size());
        return filteredData;
    }

    @Override
    public DataTableResponse findSessionAttendedUserInfoPage(long sessionId, int page, int size) {
        log.info("Start findSessionAttendedUserInfo for sessionId {}",sessionId);
        Page<RegisterdHolderUsers> registeredHolderUsers = userSessionRepoService.findRegisteredUserBySessionIdPage(sessionId, PageRequest.of(page, size));

        Session session = sessionRepoService.findById(sessionId).orElseThrow(() -> new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND));
        double duration = sessionDetailsService.getSessionVideoDurationBySession(session);

        Set<Long> userIds = registeredHolderUsers.stream().map(RegisterdHolderUsers::getUserId).collect(Collectors.toSet());

        List<UserSessionEngagementDto> userSessionEngagementDynamoDtoList = consolidatedAnalyticsService.getUserSessionEngagementDetails(session.getEvent(), sessionId, userIds);
        Map<Long , UserSessionEngagementDto> userSessionEngagementMap = userSessionEngagementDynamoDtoList.stream()
                .collect(Collectors.toMap(UserSessionEngagementDto::getUserId, Function.identity()));

        List<UserSessionEngagementDto> filteredData = new ArrayList<>();
        registeredHolderUsers.forEach(registerUser -> {
            if(registerUser.isAttended()){
                UserSessionEngagementDto userSessionEngagementDto = userSessionEngagementMap.get(registerUser.getUserId());
                String engagement = "0%";
                double watchTime = userSessionEngagementDto != null ? userSessionEngagementDto.getLiveWatchTime() : 0.0;
                if ( duration > 0) {
                    Double engagePercentage = DoubleHelper.roundValueTwoDecimal((watchTime * 100 / duration));
                    engagement = (engagePercentage > 100 ? 100 : engagePercentage) + "%";
                }
                registerUser.setEngagement(engagement);
                filteredData.add(new UserSessionEngagementDto(registerUser.getUserId(), registerUser.getFirstName(), registerUser.getLastName(), registerUser.getEmail(),
                        engagement,
                        userSessionEngagementDto != null ? userSessionEngagementDto.getLiveWatchTime() : null,
                        userSessionEngagementDto != null ? userSessionEngagementDto.getRecordingWatchTime() : null));
            }
        });
        log.info("end findSessionAttendedUserInfo for sessionId {} and size is {}",sessionId,filteredData.size());
        DataTableResponse dataTableResponse = new DataTableResponse();
        if(!filteredData.isEmpty()){
            dataTableResponse.setData(filteredData);
            dataTableResponse.setRecordsTotal(registeredHolderUsers.getTotalElements());
            dataTableResponse.setRecordsFiltered(registeredHolderUsers.getNumberOfElements());
        }
        return dataTableResponse;
    }

	@Override
	public String getUserRegionByUserAndSessionIdAndCheckedInAvailable(Long userId, Long sessionId) {
		List<UserSession> userSessions = userSessionRepoService.findUserByUserAndSessionIdAndCheckedInAvailable(userId, sessionId);
		if(!CollectionUtils.isEmpty(userSessions)){
			UserSession  userSession = userSessions.get(0);
			return 	userSession.getRegion();
		}else{
			return region;
		}
	}

	/**
	 * Method used for return attendee detail with all attended session and setting its value
	 * @param eventId
	 * @param savedAttendeeDetail
	 * @return
	 */
	@Override
	public List<AttendeeAnalyticsDTO> findAllAttendeeSessionsByEventId(Long eventId,Map<Long, List<Map<Long,String>>> savedAttendeeDetail) {
		List<AttendeeAnalyticsDTO> attendeeAnalyticsDTOs = eventTicketsService.findAllAttendeeSessionsDetailsByEventId(eventId);
        log.info("findAllAttendeeSessionsByEventId | attendeeAnalyticsDTOs size |={}| Event ID ={}|" ,attendeeAnalyticsDTOs.size(), eventId);
        return UserSessionHelper.updateAttendeeSessionDetail(attendeeAnalyticsDTOs,savedAttendeeDetail);
	}

    @Override
    public List<AttendeeSession> findAttendeeNetworkingSessionsByUserId(long eventId, long userId, EnumSessionFormat meetUp) {
        return userSessionRepoService.findAttendeeNetworkingSessionsByUserId(eventId, userId, meetUp);
    }

    @Override
    public Long getRegisteredSessionCountForUserBySessionFormat(long eventId, Long userId, EnumSessionFormat sessionFormat) {
        Long registeredSessionCount = userSessionRepoService.getRegisteredSessionCountForUserBySessionFormat(eventId, userId, sessionFormat);
        log.info("getRegisteredSessionCountForUserBySessionFormat | registeredSessionCount |={}|" ,registeredSessionCount);
        return registeredSessionCount;
    }

    @Override
    @Transactional
    public void deleteByEventId(Long sessionId) {
        userSessionRepoService.deleteByEventId(sessionId);
    }

    @Override
    public Integer getUserRegisteredSessionBySession(Long sessionId, Event event) {
        return userSessionRepoService.getUserRegisteredSessionBySession(sessionId,event);
    }

    @Override
    public Integer getUserRemainingRegisteredCountByEventIdAndUserId(Long eventId, Long userId) {

        List<Long> eventTickets = eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(userId, eventId);
        Iterator<Long> iterator = eventTickets.iterator();
        Integer remainingCount = 0;
        while (iterator.hasNext()) {
            Long eventTicketId = iterator.next();
            if (eventTicketId != null) {
                List<Object[]> ticketingTypeAttribute = eventTicketsService.findTicketingTypeById(eventTicketId);
                Integer maxRegisterUserAllowed = null != ticketingTypeAttribute.get(0)[1] ? (Integer) ticketingTypeAttribute.get(0)[1] : null;
                if (maxRegisterUserAllowed != null) {
                    Integer userSessionCount = userSessionRepoService.countByEventTicketId(eventTicketId).intValue();
                        remainingCount += maxRegisterUserAllowed - userSessionCount;
                }else{
                    remainingCount = -1;
                    break;
                }

            }
        }
        log.info("getUserRemainingRegisteredCountByEventTickets | userRemainingRegisteredCount =>{}|", remainingCount);
        return remainingCount;
    }

    @Override
    public Integer countSessionAttendeeByEventId(Long eventId) {
        return userSessionRepoService.countSessionAttendeeByEventId(eventId);
    }


    /**
     * API called from kiosk mode to check in user by scanning QR code
     * @param barcodeId barcodeID can be either userId or barcodeId
     * @param isAppLogin
     * @param source
     * @param sourceDescription
     * @param isRFID
     */
    @Override
    public void checkInSessionUsingBarcodeId(Event event, String barcodeId, UserSessionDTO userSessionDTO, Boolean join, Boolean pastJoinSession, String device, User staffUser, boolean offlineData, boolean isAppLogin, String source, String sourceDescription, boolean isRFID) {
        log.info("checkInSessionUsingBarcodeId started, sessionId {} barcodeId {}", event, barcodeId);

        if (Strings.isNullOrEmpty(barcodeId)) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
        }

        User userToBeCheckedIn = null;
        EventTickets eventTicket;
        if(!CommonUtil.checkStringIsNumber(barcodeId)) {
            //barcode id is indeed barcode id
            if(isRFID){
                eventTicket= eventTicketsRepoService.getEventTicketByRfIdTagAndNotCanceled(barcodeId, event.getEventId());
            }
            else {
                eventTicket= eventTicketsRepoService.getEventTicketByBarcodeIdAndNotCanceled(barcodeId);
            }

            if(eventTicket != null) {
                userToBeCheckedIn = eventTicket.getHolderUserId();
                log.info("Ticket {} found with barcode ID {} having holder user {}", eventTicket.getId(), barcodeId, userToBeCheckedIn.getUserId());
            } else {
                log.info("No Ticket found with barcode ID {}", barcodeId);
            }
        } else {
            userToBeCheckedIn = userService.findByUserId(Long.valueOf(barcodeId));
            eventTicket = eventTicketsService.findSingleEventTicketsByHolderUserIdAndEventId(userToBeCheckedIn, event);
        }
        checkInUserInSessionWithEventTicketIdOrBarcodeId(userToBeCheckedIn, event, eventTicket, userSessionDTO, join, pastJoinSession, device, staffUser, offlineData, isRFID, isAppLogin, source, sourceDescription);

    }

    private void checkInUserInSessionWithEventTicketIdOrBarcodeId(User userToBeCheckedIn, Event event, EventTickets eventTicket, UserSessionDTO userSessionDTO, Boolean join, Boolean pastJoinSession, String device, User staffUser, boolean offlineData, boolean isRFID, boolean isAppLogin, String source, String sourceDescription) { //NOSONAR
        if (eventTicket == null) {
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.EVENT_TICKETS_NOT_FOUND);
        }
        if (userToBeCheckedIn == null) {
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
        if (RecordStatus.BLOCK.equals(eventTicket.getRecordStatus())) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.BLOCKED_USER_ADMIN_MSG);
        }
        boolean isEventTicketBooked = !TicketStatus.CHECKED_IN.equals(eventTicket.getTicketStatus());

        if(isEventTicketBooked){
            List<String> userAllRoles = roStaffService.getAllUserRoles(event.getEventId(), eventTicket.getHolderUserId().getUserId());
            boolean isOnlyAttendee = CollectionUtils.isEmpty(userAllRoles) || (userAllRoles.size()==1 && userAllRoles.contains(ATTENDEE.toLowerCase()));
            ticketingCheckInService.throwExceptionIfEventTicketIsUnpaid(eventTicket, event.getEventId(), !isOnlyAttendee);
        }

        Session session = sessionRepoService.getSessionById(userSessionDTO.getSessionId(), event);
        if(SessionVisibilityType.PRIVATE.equals(session.getSessionVisibilityType())){
            userSessionRepoService.findBySessionIdAndUserIdAndEventId(session.getId(), userToBeCheckedIn.getUserId(), event.getEventId())
                    .stream()
                    .findFirst()
                    .orElseThrow(() -> new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_IS_PRIVATE));
        }
        TicketTypeFormat eventTicketTypeFormat = eventTicket.getTicketingTypeId().getTicketTypeFormat();
        boolean isTicketAllowed = isUserCanBookmarkOrCheckInSession(event, session, eventTicketTypeFormat, true, false);

        if (!isTicketAllowed || (EnumSessionStatus.HIDDEN.equals(session.getStatus()) && !this.roStaffService.hasStaffAccessForEvent(staffUser, event))) {
            throw new NotAcceptableException(ATTENDEE_TICKET_NOT_GRANT_ACCESS_TO_THIS_SESSION);
        }
        validateTimeAccessElapsed(session, event, userToBeCheckedIn, join, pastJoinSession, offlineData);
        validateRegisterCapacityAndConcurrentSessionRegistrationRules(session,event,userToBeCheckedIn.getUserId());
        UserSession userSession;
        try {
            userSession = tryAndRegisterUserWithEventTicketId(userToBeCheckedIn, session, event, eventTicket.getId());
        } catch (NotAcceptableException e) {
            throw TICKET_TYPE_NOT_MATCHED.getStatusCode().equals(e.getErrorCode()) ? new NotAcceptableException(ATTENDEE_TICKET_NOT_GRANT_ACCESS_TO_THIS_SESSION) : e;
        }

        if (userSession == null) {
            throw new NotAcceptableException(REGISTER_FAILED);
        }

        Date checkInTime = new Date();
        if(offlineData){
            checkInTime = userSessionDTO.getCheckInTime();
            userSession.setRegistrationDate(checkInTime);
        }

        saveUserSession(join, pastJoinSession, userSession, checkInTime);
        HashMap<String, String> trackingData = new HashMap<>();
        trackingData.put(Constants.FIRST_NAME, userToBeCheckedIn.getFirstName());
        trackingData.put(Constants.LAST_NAME, userToBeCheckedIn.getLastName());
        trackingData.put(Constants.EMAIL, userToBeCheckedIn.getEmail());

        List<Map<String, String>> holderData = Collections.singletonList(trackingData);
        trayTrackingService.trackAttendeeSession(event, session, holderData, join, pastJoinSession);

        log.info("User {} successfully register from staff page in session {}", userToBeCheckedIn.getUserId(), session.getId());
        afterTaskIntegrationTriggerService.userRegisterOrAttendSession(event, userSession, true);
        sessionCheckInLogService.createSessionCheckInLog(userSession, staffUser.getUserId(), device);
        if(isEventTicketBooked) {
            try {
                ticketingCheckInService.checkInTicket(null, (isRFID)?eventTicket.getRfidId() : eventTicket.getBarcodeId(), staffUser, event, device,isAppLogin, userSessionDTO.getSource(),userSessionDTO.getSourceDescription(), isRFID);
            } catch (IOException | NotAcceptableException | AuthorizationException e) {
                log.error("Error while checking in ticket from checking session eventTicket Id {} for user {} in event {}", eventTicket.getId(), userToBeCheckedIn.getUserId(), event.getEventId());
            }
        }

        createSessionCheckinCheckoutActivityLog(event, staffUser, (isRFID)?eventTicket.getRfidId() : eventTicket.getBarcodeId() , EnumSessionCheckInLogStatus.CHECK_IN, true, null, isRFID, source, sourceDescription, isAppLogin, eventTicket);
    }

    @Override
    public void updateUserSessionUserId(Event event, User newUser, User oldUserId, Long eventTicketId) {
        List<UserSession> userSessions = userSessionRepoService.findByUserIdAndEventIdAndEventTicketId(oldUserId.getUserId(), event.getEventId(), eventTicketId);
        userSessions.forEach(userSession -> userSession.setUserId(newUser.getUserId()));
        userSessionRepoService.saveAll(userSessions);
    }

    @Override
    public void userSessionAddToCalendar(Long eventId, Long userId, Long sessionId) {
        UserSessionCalendar userSessionCalendar=new UserSessionCalendar();
        userSessionCalendar.setEventId(eventId);
        userSessionCalendar.setCreatedDate(new Date());
        userSessionCalendar.setUserId(userId);
        userSessionCalendar.setSessionId(sessionId);
        userSessionCalendarRepo.save(userSessionCalendar);
    }

    @Override
    public AttendeeAnalyticCountDTO getSessionAttedneesAndRegisteredCount(Long sessionId) {
        AttendeeAnalyticCountDTO attendeeAnalyticCountDTO = new AttendeeAnalyticCountDTO();

        attendeeAnalyticCountDTO.setTotalAttendee(userSessionRepoService.countSessionAttendeeBySessionId(sessionId));
        attendeeAnalyticCountDTO.setRegisterAttendee(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(sessionId));
        return attendeeAnalyticCountDTO;
    }

    @Override
    public UserSessionBasicDetailsDTO isUserRegisteredInSession(Long sessionId, String barcodeId, Event event) {
        EventTickets eventTicket = null;
        log.info("isUserRegisteredInSession, checking user registered by eventTicketId {} sessionId {}", barcodeId, sessionId);
        List<UserSession> userSessions;

        if(!CommonUtil.checkStringIsNumberOrNot(barcodeId)) {
            eventTicket = eventTicketsRepoService.findByBarcodeIdAndEventId(barcodeId, event.getEventId());

        } else {
            //if number then treat as user id.
           try {
                Optional<User> userOne= roUserService.getUserById(Long.valueOf(barcodeId));
                if(userOne.isPresent()) {
                    eventTicket = eventTicketsService.findSingleEventTicketsByHolderUserIdAndEventId(userOne.get(), event);
                }
           }catch (NumberFormatException e){
               log.info("Barcode Id {} is not valid. ErrorMessage: {} Exception:",barcodeId,e.getMessage(), e);
               throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_VALID);
           }

        }
        boolean isUserRegistered = false;
        boolean isUserCheckedIn = false;

        if(eventTicket == null) {
            log.info("isUserRegisteredInSession, event ticket not found for eventTicketId {} sessionId {}", barcodeId, sessionId);
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.EVENT_TICKETS_NOT_FOUND);
        }  else {
            userSessions = userSessionRepoService.findByEventTicketIdAndSessionIdAndEventId(eventTicket.getId(), sessionId, event.getEventId());
            if(userSessions != null){
                isUserRegistered = !userSessions.isEmpty();
                isUserCheckedIn = userSessions.stream()
                        .anyMatch(userSession -> userSession.getCheckInTime() != null && !SESSION_CHECKOUT_STATUS.contains(userSession.getCheckInStatus()));
            }
        }

       return new UserSessionBasicDetailsDTO(eventTicket, isUserRegistered, isUserCheckedIn);

    }

    @Override
    public List<IdCountDto> getSessionStatsForIdsInAndEventIdAndTicketIdIsNotNull(List<Long> sessionIds, long eventId) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() :  userSessionRepoService.countBySessionIdInAndEventIdAndTicketIdIsNotNull(sessionIds, eventId);
    }

    @Override
    public Integer getSessionRegisterCountAndTicketIdIsNotNullAndEventId(Long sessionId, long eventId) {
        return userSessionRepoService.countBySessionIdAndTicketIdIsNotNullAndEventId(sessionId, eventId);
    }

    /**
     * Check if a user with a specific ticket can view documents/links for a session
     * @param user The user to check
     * @param session The session containing the documents/links
     * @param event The event containing the session
     * @return true if the user can view documents/links, false otherwise
     */
    @Override
    public boolean canUserViewSessionDocsAndLinks(User user, Session session, Event event) {
        // Admin or staff can always view docs/links
        if (user != null && Permissions.STAFF_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(user, event))) {
            return true;
        }
        // Speaker of the session can view docs/links
        else if (user != null && sessionSpeakerService.isUserSpeakerInSession(user.getUserId(), session.getId())) {
            return true;
        }

        // Check if the user has a valid ticket for this session
        if (user != null) {
            List<Long> userTicketTypes = eventTicketsRepoService.getAllEventTicketTypeIdsByEventUserANDNotCanceled(event, user);
            if (CollectionUtils.isEmpty(userTicketTypes)) {
                return false;
            }

            // Check if the user's ticket type is allowed for this session
            if (!org.apache.commons.lang3.StringUtils.isEmpty(session.getTicketTypesThatCanBeRegistered())) {
                List<Long> ticketTypesThatCanBeRegisteredList = convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered());
                List<Long> matchTicket = ticketTypesThatCanBeRegisteredList.stream()
                        .filter(userTicketTypes::contains)
                        .collect(Collectors.toList());

                return !CollectionUtils.isEmpty(matchTicket);
            }
        }

        return false;
    }

    @Override
    public Map<Long, Boolean> findBookmarkedSessionByUserIdAndSessionId(Long userId, List<Long> sessionIds) {
        if(CollectionUtils.isEmpty(sessionIds)){
            return Collections.emptyMap();
        }
        List<UserSession> userSessions = userSessionRepoService.findRegisteredEventTicketIdByUserAndSessionId(userId,sessionIds);
        Map<Long, Boolean> isBookMarkedUserSessionsMap = new HashMap<>();
        userSessions.forEach(userSession -> isBookMarkedUserSessionsMap.put(userSession.getSessionId(), userSession.isBookmarked()));
        return isBookMarkedUserSessionsMap;
    }

    @Override
    public Map<Long, Boolean> findWaitlistedSessionByUserIdAndSessionId(Long userId, List<Long> sessionIds) {
        if(CollectionUtils.isEmpty(sessionIds)){
            return Collections.emptyMap();
        }
        List<UserSession> userSessions = userSessionRepoService.findRegisteredEventTicketIdByUserAndSessionId(userId,sessionIds);
        Map<Long, Boolean> isWaitlistedUserSessionsMap = new HashMap<>();
        userSessions.forEach(userSession -> isWaitlistedUserSessionsMap.put(userSession.getSessionId(),
            WAITLISTED.equals(userSession.getSessionStatus())));
        return isWaitlistedUserSessionsMap;
    }

    @Override
    public List<IdCountDto> countSessionBookmarkedAttendeeBySessionIdInAndEventId(List<Long> sessionIds, long eventId) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : userSessionRepoService.countSessionBookmarkedAttendeeBySessionIdsAndEventId(sessionIds, eventId);
    }

    @Override
    public void sendSaveASeatTestEmail(Event event, String email, boolean isAdminOrStaff){
        SessionLocation sessionLocation = new SessionLocation();
        sessionLocation.setName("New York");
        sessionLocation.setId(0L);

        Session session = new Session();
        session.setId(0);
        session.setTitle("Session Name");
        session.setStartTime(new Date());
        session.setSessionLocation(sessionLocation);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(session.getStartTime());
        calendar.add(Calendar.HOUR, 2);
        session.setEndTime(calendar.getTime());
        User user = roUserService.findByEmail(email);
        sendGridMailPrepareService.sendSaveASeatEmail(user,session,event,isAdminOrStaff,Boolean.TRUE);
    }

    @Override
    public UploadSessionAttendeeResponseContainer registerBulkUsersFromHost(Session session, Event event, String userIds, String languageCode) {
        log.info("registerBulkUsersFromHost session  {} and event {} and userIds {}", session.getId(), event.getEventId(), userIds);
        if (SessionVisibilityType.PUBLIC.equals(session.getSessionVisibilityType())) {
            throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_IS_NOT_PRIVATE);
        }

        List<Long> userIdList = convertCommaSeparatedToListLong(userIds);
        if(CollectionUtils.isEmpty(userIdList)){
            return new UploadSessionAttendeeResponseContainer();
        }

        List<Long> registeredUserIdList = userSessionRepoService.findUserIdsByEventIdAndSessionId(session.getId(), event.getEventId());
        List<Long> pendingUserIdList = userIdList.stream().filter(e-> !registeredUserIdList.contains(e)).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(pendingUserIdList)){
            return new UploadSessionAttendeeResponseContainer();
        }

        List<User> users = roUserService.getListOfUsersByUserIds(pendingUserIdList);
        UploadSessionAttendeeResponseContainer responseContainer = new UploadSessionAttendeeResponseContainer();
        log.info("registerBulkUsersFromHost session {} and pending users {}", session.getId(), users.size());
        List<Map<String,String>> trackingUser = new ArrayList<>();
        int sessionCapacity = session.getCapacity();

        for(User user: users) {
            UserSessionDTO userSessionDTO = new UserSessionDTO();
            userSessionDTO.setUserId(user.getUserId());
            userSessionDTO.setSessionId(session.getId());
            userSessionDTO.setEventId(event.getEventId());

            String firstName = user.getFirstName();
            String lastName = user.getLastName();

            if (sessionCapacity > 0) {
                int registeredCount = countRegisteredBySessionId(session.getId());
                if (registeredCount >= sessionCapacity) {
                    responseContainer.addInvalidAttendees(firstName, lastName, 3, languageCode);
                    continue;
                }
            }

            try {
                validateRegisterCapacityAndConcurrentSessionRegistrationRules(session, event, user.getUserId());
            } catch (NotAcceptableException exception) {
                if (NotAcceptableException.SessionExceptionMsg.SESSION_REGISTRATION_NOT_ALLOWED.getErrorMessage().equalsIgnoreCase(exception.getErrorMessage())) {
                    responseContainer.addInvalidAttendees(firstName, lastName, 8, languageCode);
                }
                continue;
            }
            registerUserToSessionWithTicketTypes(userSessionDTO,session ,responseContainer, firstName, lastName, user.getEmail(),languageCode, trackingUser, event);
        }

        int successCount = users.size() - responseContainer.getInvalidAttendees().size();
        trayTrackingService.trackAttendeeSession(session.getEvent(),session,trackingUser,Boolean.FALSE,Boolean.FALSE);
        responseContainer.setMessage(!StringUtil.isBlank(languageCode)  && "EN".equalsIgnoreCase(languageCode) ?
                (successCount + " of " + users.size() + " Attendees were successfully registered for the session.") :
                (successCount + " de " + users.size() + " Los asistentes se registraron correctamente para la sesión."));


        log.info("RegisterBulkUsersFromHost Invalid attendees size {}",responseContainer.getInvalidAttendees().size());
        redisCacheService.clearSessionCache(session.getEventId());
        return responseContainer;
    }

    private void registerUserToSessionWithTicketTypes(UserSessionDTO userSessionDTO, Session session, UploadSessionAttendeeResponseContainer returnValue, String firstName, String lastName, String email,String languageCode, List<Map<String, String>> trackingUser, Event event) {
        List<Long> eventTicketIds = eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(userSessionDTO.getUserId(), userSessionDTO.getEventId());
        if (null != eventTicketIds && !eventTicketIds.isEmpty()) {
            Iterator<Long> iterator = eventTicketIds.iterator();
            while (iterator.hasNext()) {
                Long ticketId = iterator.next();
                if (validateMatchingTicketTypeForBulkUpload(session, ticketId, returnValue, firstName, lastName, languageCode)) {
                    Long consumedTagTrackId = null;
                    try {
                        consumedTagTrackId = getConsumedTrackOrvalidateSessionTracksLimit(userSessionDTO.getUserId(), session, event, ticketId);
                    } catch (NotAcceptableException e) {
                        returnValue.addInvalidAttendees(firstName, lastName, 9, languageCode);
                    }
                    UserSession userSession = toEntity(userSessionDTO);
                    userSession.setEventTicketId(ticketId);
                    userSession.setConsumedTagTrackId(consumedTagTrackId);
                    userSessionRepoService.save(userSession);
                    HashMap<String, String> trackingData = new HashMap<>();
                    trackingData.put(Constants.FIRST_NAME, firstName);
                    trackingData.put(Constants.LAST_NAME, lastName);
                    trackingData.put(Constants.EMAIL, email);
                    trackingUser.add(trackingData);
                    break;
                }
            }
        }else {
            returnValue.addInvalidAttendees(firstName, lastName, 1, languageCode);
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public void removeUserFromWaitlist(UserSessionDTO dto, Event event, boolean isUnWaitlisted) {
        log.info("removeUserFromWaitlist sessionId {} userId {} isUnWaitlisted {}", dto.getSessionId(), dto.getUserId(), isUnWaitlisted);
        List<UserSession> userSessions = userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndSessionStatus(dto.getSessionId(), dto.getUserId(), event.getEventId(), WAITLISTED);
        try {
            if (!userSessions.isEmpty()) {
                UserSession userSession = userSessions.get(0);

                if (isUnWaitlisted && userSession.isBookmarked()) {
                    userSession.setSessionStatus(BOOKMARKED);
                    userSession.setBookmarked(true);
                    userSessionRepoService.save(userSession);
                } else if (!userSession.isBookmarked()) {
                    userSessionRepoService.delete(userSession);
                } else {
                    userSession.setSessionStatus(BOOKMARKED);
                    userSessionRepoService.save(userSession);
                }

                boolean privateSession = sessionRepoService.isSessionPrivate(userSession.getSessionId());
                if (privateSession) {
                    redisCacheService.clearSessionCache(event.getEventId());
                }
            }
        } catch (Exception exception) {
            log.info("Exception occurred while removeUserFromWaitlist eventId {} userId {} exception {}", event.getEventId(), dto.getUserId(), exception.getMessage());
        }
    }

    @Override
    public void createSessionCheckinCheckoutActivityLog(Event event, User user, String barcodeId, EnumSessionCheckInLogStatus sessionCheckInLogStatus, boolean success, BaseException baseException, boolean isRfidCheckIn, String source, String sourceDescription, boolean isAppLogin, EventTickets eventTickets){
        try{
            if(eventTickets == null && isAppLogin){
                if (isRfidCheckIn){
                    eventTickets = eventTicketsRepoService.findByBarcodeIdAndEventIdWithoutJoin(barcodeId, event.getEventId());
                } else {
                    eventTickets = eventCommonRepoService.findByBarcodeId(barcodeId);
                }
            }
            if (eventTickets != null){
                String sourceName;
                if(org.apache.commons.lang3.StringUtils.isNotEmpty(source)){
                    sourceName = source;
                }
                else{
                    sourceName = (isAppLogin) ? "MobileApp" : "Desktop";
                }
                HashMap<String, Object> userActivity = new HashMap<>();
                if(sourceDescription != null ){
                    try {
                        String decodedParam = URLDecoder.decode(sourceDescription, StandardCharsets.UTF_8.name());
                        Map<String, Object> sourceDescriptionMap = JsonMapper.stringtoObject(decodedParam, Map.class);
                        if (sourceDescriptionMap != null) {
                            userActivity.putAll(sourceDescriptionMap);
                        }

                    } catch (UnsupportedEncodingException e) {
                        log.info("Error decoding source description: {}", e.getMessage());
                    }
                }

                if(isAppLogin){
                    userActivity.put(isRfidCheckIn ? "rfIdTag" : "barcodeId", barcodeId);
                }

                String errorCode = null;

                if(Boolean.FALSE.equals(success)){
                    userActivity.put("error", baseException != null ? baseException.getErrorMessage() : "Unknown error");
                    errorCode = (baseException != null) ? baseException.getErrorCode() : null;
                }

                String activityName = EnumSessionCheckInLogStatus.CHECK_IN.equals(sessionCheckInLogStatus) ? "Session CheckIn" : "Session CheckOut";

                userActivityService.createUserActivity(userActivity, event.getEventId(), eventTickets.getUserId(), activityName, sourceName, errorCode, (user != null)?user.getUserId():null );
            }
        }
        catch (Exception exception) {
            log.info("Exception occurred while creating entry exit activity log: {}", exception.getMessage(), exception);
        }
    }
}
