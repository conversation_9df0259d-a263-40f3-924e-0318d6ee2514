package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.utils.Constants;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class GraphQLDataFixHandler {

	private static final Logger logger = LoggerFactory.getLogger(GraphQLDataFixHandler.class);

	private String graphQlUrl;
	private String graphQlApiKey;

	public GraphQLDataFixHandler(String graphQlUrl, String graphQlApiKey){
		this.graphQlUrl = graphQlUrl;
		this.graphQlApiKey = graphQlApiKey;
	}

	public void handleCreateOrUpdateSessionStatusLogs(String status, Session session) {
//		try {
//			logger.info("handleCreateOrUpdateSessionStatusLogs sessionId {} status {} ", session.getId(), status);
////			if(getAllQuestions()){
////				updateSessionStatusLogs(status, session);
////			} else {
////				createSessionStatusLogs("CREATED", session);
////				updateSessionStatusLogs(status, session);
////			}
//		} catch (JSONException e) {
//			logger.info("handleCreateOrUpdateSessionStatusLogsJSONException");
//			e.printStackTrace();
//		}
	}

	public static void main(String[] args) throws JSONException {
//		appsync.session.broadcast.api.url=https://qr4adeff3jeejh5lsghvklnysq.appsync-api.us-east-1.amazonaws.com/graphql
//		appsync.broadcast.api.key=da2-qonyiqkueza6pcanfy6oyrqcoy

//		GraphQLDataFixHandler g = new GraphQLDataFixHandler("https://qr4adeff3jeejh5lsghvklnysq.appsync-api.us-east-1.amazonaws.com/graphql",
//				"da2-qonyiqkueza6pcanfy6oyrqcoy");
//
//		g.getAllAnswers(g.getAllQuestions());
	}

	public Map<String, Integer> getAllQuestions() throws JSONException {
		String operationName = "listPollsQuestions";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put(Constants.OPERATION_NAME,operationName);
		mainRequest.put(Constants.VARIABLES,"{\n" +
				"\t\"filter\": null,\n" +
				"\t\"limit\": 6000,\n" +
				"\t\"nextToken\": null\n" +
				"}");
		mainRequest.put(Constants.QUERY,"query listPollsQuestions($filter: TablePollsQuestionsFilterInput, $limit: Int, $nextToken: String) {\n" +
				"  listPollsQuestions(filter: $filter, limit: $limit, nextToken: $nextToken) {\n" +
				"    items {\n" +
				"      id\n" +
				"      sessionId\n" +
				"      eventId\n" +
				"    }\n" +
				" }\n" +
				"}");
		return executeAllQuestions(mainRequest);
	}

	public Map<String, Integer> getAllAnswers(Map<String, Integer> allQuestions) throws JSONException {
		String operationName = "listPollsResult";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put(Constants.OPERATION_NAME,operationName);
		mainRequest.put(Constants.VARIABLES,"{\n" +
				"\t\"filter\": null,\n" +
				"\t\"limit\": 6000,\n" +
				"\t\"nextToken\": null\n" +
				"}");
		mainRequest.put(Constants.QUERY,"query listPollsResult($filter: TablePollsResultFilterInput, $limit: Int, $nextToken: String) {\n" +
				"  listPollsResults(filter: $filter, limit: $limit, nextToken: $nextToken) {\n" +
				"    items {\n" +
				"      pollsQuestionId\n" +
				"      id\n" +
				"      answers\n" +
				"      userId\n" +
				"    }\n" +
				"  }\n" +
				"}");
		return executeAllAnswers(mainRequest, allQuestions);
	}

	private Map<String, Integer> executeAllQuestions(JSONObject mainRequest) {
		try {
			HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
					.headers( getHeaders())
					.body(mainRequest.toString())
					.asJson();
			JSONObject res = response.getBody().getObject();
			JSONObject data = res.getJSONObject("data");
			JSONObject j = (JSONObject) data.get("listPollsQuestions");
			JSONArray items = j.getJSONArray("items");
			Map<String, Integer> map = new HashMap<>();
			for(int i = 0; i< items.length();i++){
				JSONObject jsonObject = (JSONObject) items.get(i);
				Integer sessionId = jsonObject.getInt(Constants.SESSION_ID);
				String questionId = jsonObject.getString("id");
				map.put(questionId, sessionId);
			}
			return map;
		} catch (UnirestException e) {
			logger.info("GraphqlHandlerExecuteUnirest {} ", e.getMessage());//NOSONAR
			e.printStackTrace();
		} catch (JSONException e) {
			logger.info("GraphqlHandlerExecuteJSONException {} ", e.getMessage());//NOSONAR
			e.printStackTrace();
		}
		return null;
	}

	private Map<String, Integer> executeAllAnswers(JSONObject mainRequest, Map<String, Integer> map) {
		try {
			HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
					.headers( getHeaders())
					.body(mainRequest.toString())
					.asJson();
			JSONObject res = response.getBody().getObject();
			JSONObject data = res.getJSONObject("data");
			JSONObject j = (JSONObject) data.get("listPollsResults");
			JSONArray items = j.getJSONArray("items");
			for(int i = 0; i< items.length();i++){
				JSONObject jsonObject = (JSONObject) items.get(i);
				String questionId = jsonObject.getString("pollsQuestionId");
				String id = jsonObject.getString("id");

				Integer sessionId = map.get(questionId);
				updateResult(sessionId,id,questionId);

			}
			return null;
		} catch (UnirestException e) {
			logger.info("GraphqlHandlerExecuteUnirest {} ", e.getMessage());
			e.printStackTrace();
		} catch (JSONException e) {
			logger.info("GraphqlHandlerExecuteJSONException {} ", e.getMessage());
			e.printStackTrace();
		}
		return null;
	}

	private boolean execute(JSONObject mainRequest, String operationName) {
		try {
			HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
					.headers( getHeaders())
					.body(mainRequest.toString())
					.asJson();
			JSONObject res = response.getBody().getObject();
			JSONObject data = res.getJSONObject("data");
			JSONObject finalResult = data.getJSONObject(operationName);
			return finalResult.get(Constants.SESSION_ID) != null;
		} catch (UnirestException e) {
			logger.info("GraphqlHandlerExecuteUnirest {} ", e.getMessage());
			e.printStackTrace();
		} catch (JSONException e) {
			logger.info("GraphqlHandlerExecuteJSONException {} ", e.getMessage());
			e.printStackTrace();
		}
		return false;
	}

	public boolean updateResult(Integer sessionId, String id, String pollsQuestionId)  {
		try {
			JSONObject variable = new JSONObject();
			variable.put("updatePollsResultInput", updatePollsResultInput(sessionId, id, pollsQuestionId));

			String operationName = "updatePollsResult";
			JSONObject mainRequest = new JSONObject();
			mainRequest.put(Constants.OPERATION_NAME,operationName);
			mainRequest.put(Constants.VARIABLES,variable);
			mainRequest.put(Constants.QUERY,"mutation updatePollsResult($updatePollsResultInput : UpdatePollsResultInput!){\n" +
					"  updatePollsResult(input : $updatePollsResultInput){\n" +
					"     id\n" +
					"    pollsQuestionId\n" +
					"    sessionId\n" +
					"  }\n" +
					"}");
			return execute(mainRequest, operationName);
		} catch (Exception e){
			e.printStackTrace();
		}

		return false;
	}


	public Map<String,String> getHeaders() {
		Map<String,String> headers = new HashMap<>();
		headers.put("Content-Type", "application/json");
		headers.put("x-api-key", graphQlApiKey);
		return headers;
	}


	public JSONObject updatePollsResultInput(Integer sessionId, String id, String pollsQuestionId) throws JSONException {
		JSONObject requestData = new JSONObject();
		requestData.put(Constants.SESSION_ID,sessionId);
		requestData.put("id",id);
		requestData.put("pollsQuestionId",pollsQuestionId);
		return requestData;
	}


}
