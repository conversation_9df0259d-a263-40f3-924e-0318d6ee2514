package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketTypeFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.messages.TicketType;
import com.accelevents.session_speakers.dto.AttendeeSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.RegisterdHolderUsers;
import com.accelevents.session_speakers.repo.UserSessionCalendarRepo;
import com.accelevents.session_speakers.repo.UserSessionRepo;
import com.accelevents.session_speakers.services.UserSessionRepoService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.EnumUserSessionStatus.*;

@Service
public class UserSessionRepoServiceImpl implements UserSessionRepoService
{

    @Autowired
    private UserSessionRepo userSessionRepo;

    @Autowired
    private UserSessionCalendarRepo userSessionCalendarRepo;
    @Override
    public List<UserSession> findByEventTicketIdAndSessionIdAndEventId(Long eventTicketId, Long sessionId, Long eventId) {
        return userSessionRepo.findByEventTicketIdAndSessionIdAndEventId(eventTicketId,sessionId,eventId);
    }

    @Override
    public void deleteAll(List<UserSession> userSessions) {
        userSessions.forEach(userSession -> userSessionRepo.delete(userSession));
    }

    @Override
    public UserSession save(UserSession userSession) {
        return userSessionRepo.save(userSession);
    }

    @Override
    public BigInteger countByEventTicketId(Long eventTicketId) {
        return userSessionRepo.countByEventTicketId(eventTicketId);
    }

    @Override
    public boolean isUserAvailableStateForTicketTypesIn(Long eventId, Long userId, Long sessionId, EnumUserSessionStatus state, List<Long> ticketTypeIds) {
        return userSessionRepo.isUserAvailableStateForTicketTypesIn(eventId,userId,sessionId,state,ticketTypeIds);
    }

    @Override
    public boolean isAnyUsersRegisteredTicketTypesIn(Long eventId, Long sessionId, EnumUserSessionStatus state, List<Long> ticketTypeIds) {
        return userSessionRepo.isAnyUsersRegisteredTicketTypesIn(eventId,sessionId,state,ticketTypeIds);
    }

    @Override
    public boolean isUserAvailableState(Long eventId, Long userId, Long sessionId, EnumUserSessionStatus state) {
        return userSessionRepo.isUserAvailableState(eventId,userId,sessionId,state);
    }
    @Override
    public boolean isUserAvailableStateIn(Long eventId, Long userId, Long sessionId, List<EnumUserSessionStatus> state) {
        return userSessionRepo.isUserAvailableStateIn(eventId,userId,sessionId,state);
    }


    @Override
    public int getCheckInUserCountBySession(Long sessionId) {
        return userSessionRepo.getCheckInUserCountBySession(sessionId);
    }

    @Override
    public List<Long> findByEventIdAndSessionIdAndSateAndNotUserIdAndTicketTypesIn(Long eventId, Long sessionId, EnumUserSessionStatus state, List<Long> userIds, List<Long> ticketTypeIds) {
        return userSessionRepo.findByEventIdAndSessionIdAndSateAndNotUserIdAndTicketTypesIn(eventId, sessionId, state, userIds, ticketTypeIds);
    }

    @Override
    public void updateUserState(EnumUserSessionStatus state, Long eventId, Long sessionId, List<Long> userIds) {
        userSessionRepo.updateUserState(state,eventId,sessionId,userIds);
    }

    @Override
    public void updateUserCheckInState(EnumUserSessionStatus state, Long eventId, Long sessionId, List<Long> userIds) {
        userSessionRepo.updateUserCheckInState(state,eventId,sessionId,userIds);
    }

    @Override
    public List<IdCountDto> registerCountBySessionIdsAndSessionStatus(List<Long> sessionIds,EnumUserSessionStatus sessionStatus) {
        return userSessionRepo.getRegisterCountBySessionIdsAndSessionStatus(sessionIds,sessionStatus);
    }

    @Override
    public List<IdCountDto> countSessionAttendeeBySessionIdIn(List<Long> sessionIds) {
        return userSessionRepo.countSessionAttendeeBySessionIdIn(sessionIds);
    }

    @Override
    @Deprecated
    //Moved to RO remove this method
    public List<IdCountDto> countSessionAttendeeByEventAndUserIds(List<Long> eventIds, List<Long> userIds) {
        return userSessionRepo.countSessionAttendeeByEventAndUserIds(eventIds, userIds);
    }

    @Override
    public long countSessionAttendeeBySessionId(Long sessionId) {
        return userSessionRepo.countSessionAttendeeBySessionId(sessionId);
    }

    @Override
    public List<RegisterdHolderUsers> findRegisteredUserBySessionId(Long sessionId) {
        return userSessionRepo.findRegisteredUserBySessionId(sessionId);
    }

    @Override
    public List<RegisterdHolderUsers> getRegisteredUserBySessionId(Long sessionId) {
        return userSessionRepo.getRegisteredUserBySessionId(sessionId);
    }

    @Override
    public Page<RegisterdHolderUsers> findRegisteredUserBySessionIdPage(Long sessionId, Pageable pageable) {
        return userSessionRepo.findRegisteredUserBySessionIdPage(sessionId, pageable);
    }

    @Override
    public List<RegisterdHolderUsers> findRegisteredUserSessionBySessionId(Long sessionId, List<Long> ticketTypeIds, List<TicketType> ticketTypes, List<EnumUserSessionStatus> checkInStatus, List<EnumUserSessionStatus> sessionStatus) {
        return userSessionRepo.findRegisteredUserSessionBySessionId(sessionId, ticketTypeIds, ticketTypes, checkInStatus, sessionStatus);
    }

    @Override
    public List<AttendeeSession> findAttendeeSessionsByUserId(Long eventId, Long userId, String searchString) {
        return userSessionRepo.findAttendeeSessionsByUserIdNotBookmarked(eventId, userId, searchString);
    }

    @Override
    public List<UserSession> findRegisteredEventTicketIdByUserAndSessionId(Long userId, List<Long> sessionIds) {
        return userSessionRepo.findRegisteredEventTicketIdByUserAndSessionId(userId,sessionIds);
    }

    @Override
    public BigInteger registerCountBySessionId(Long sessionId, EnumUserSessionStatus enumUserSessionStatus) {
        return userSessionRepo.getRegisterCountBySessionId(sessionId,enumUserSessionStatus);
    }

    @Override
    public List<Long> findUserIdsByEventIdAndSessionId(Long sessionId, long eventId) {
        return userSessionRepo.findUserByEventIdAndSessionId(sessionId, eventId);
    }

    @Override
    public List<Long> getEventTicketIdsByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId) {
        return userSessionRepo.getEventTicketIdsByEventIdAndUserIdAndSessionId(eventId,userId,sessionId);
    }

    @Override
    public List<Long> getTicketingTypeIdByEventIdAndUserIdAndSessionId(Long eventId, Long userId, Long sessionId) {
        return userSessionRepo.getTicketingTypeIdByEventIdAndUserIdAndSessionId(eventId,userId,sessionId);
    }

    @Override
    public List<Long> findEventTicketIdByUserIdAndEventId(Long userId, Long eventId) {
        return userSessionRepo.findEventTicketIdByUserIdAndEventId(userId,eventId);
    }

    @Override
    public List<Long> findByEventIdAndUserId(Long eventId, Long userId) {
        return userSessionRepo.findByEventIdAndUserId(eventId,userId);
    }

    @Override
    public Long userSessionCount(Long eventId, Long userId) {
        return userSessionRepo.userSessionCount(eventId,userId);
    }

    @Override
    public void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status) {
        userSessionRepo.updateStatusToDeleteBySessionId(sessionId,status);
    }

    @Override
    public List<Long> getEventTicketIdsByEventIdAndPurchaserUserIdAndSessionId(Long eventId, Long userId, Long sessionId) {
        return userSessionRepo.getEventTicketIdsByEventIdAndPurchaserUserIdAndSessionId(eventId,userId,sessionId);
    }

    @Override
    public List<UserSession> findAllBySessionId(long sessionId) {
        return userSessionRepo.findAllBySessionId(sessionId);
    }

    @Override
    public List<UserSession> findUserByUserAndSessionIdAndCheckedInAvailable(Long userId, Long sessionId) {
        return userSessionRepo.findUserByUserAndSessionIdAndCheckedInAvailable(userId,sessionId);
    }

    @Override
    public List<Object[]> findAllAttendeeSessionsByEventId(Long eventId) {
        return userSessionRepo.findAllAttendeeSessionsByEventId(eventId);
    }

    @Override
    public List<UserSession> findBySessionIdAndUserIdAndEventId(long id, Long userId, Long eventId) {
        return userSessionRepo.findBySessionIdAndUserIdAndEventId(id,userId,eventId);
    }

    @Override
    public List<AttendeeSession> findAttendeeNetworkingSessionsByUserId(long eventId, long userId, EnumSessionFormat meetUp){
        return userSessionRepo.findAttendeeNetworkingSessionsByUserId(eventId, userId, meetUp);
    }

    @Override
    public void updateAllByUserId(Long userId) {
        userSessionRepo.updateStatusToAnonymizedByUserID(userId, RecordStatus.ANONYMIZED);
    }

    @Override
    public Long getRegisteredSessionCountForUserBySessionFormat(long eventId, Long userId, EnumSessionFormat sessionFormat) {
        return userSessionRepo.getRegisteredSessionCountForUserBySessionFormat(eventId,userId,sessionFormat);
    }

    @Override
    @Transactional
    public void deleteByEventId(Long eventId) {
        userSessionRepo.deleteByEventId(eventId);
    }

    @Override
    public Integer getUserRegisteredSessionBySession(Long sessionId, Event event) {
        return userSessionRepo.getUserRegisteredSessionBySession(sessionId,event.getEventId());
    }

    @Override
    public Map<Long, Integer> getUserRegisteredSessionsBySessionIds(List<Long> sessionIds, Event event) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyMap();
        }

        // Get the list of session ID and user count pairs from the repository
        List<Object[]> results = userSessionRepo.getUserRegisteredSessionsBySessionIds(sessionIds, event.getEventId());

        // Convert the results to a map for easy lookup
        Map<Long, Integer> sessionCountMap = results.stream()
                .collect(Collectors.toMap(
                        row -> ((Number) row[0]).longValue(), //sessionId
                        row -> ((Number) row[1]).intValue() //user count
                ));


        // Ensure all requested session IDs are in the result map, even if they have no registrations
        sessionIds.forEach(id -> sessionCountMap.putIfAbsent(id, 0));


        return sessionCountMap;
    }

    @Override
    public Integer countBySessionIdsAndUserId(List<Long> sessionIds, Long userId, Long eventId) {
        return userSessionRepo.countBySessionIdsAndUserId(sessionIds,userId,eventId);
    }

    @Override
    public Integer countSessionAttendeeByEventId(Long eventId) {
        return userSessionRepo.countSessionAttendeeByEventId(eventId);
    }

    @Override
    public List<Object[]> getCheckInSessionByUserIdAndSessionId(List<Long> sessionIds, Long userId) {
        return userSessionRepo.getCheckInSessionByUserIdAndSessionId(sessionIds,userId);
    }

    @Override
    public List<UserSession> findByUserIdAndEventId(Long userId, long eventId) {
        return userSessionRepo.findByUserIdAndEventId(userId, eventId);
    }

    @Override
    public List<UserSession> findByUserIdAndEventIdAndEventTicketId(Long userId, long eventId, Long eventTicketId) {
        return userSessionRepo.findByUserIdAndEventIdAndEventTicketId(userId, eventId, eventTicketId);
    }

    @Override
    public void saveAll(List<UserSession> userSessions) {
        userSessionRepo.saveAll(userSessions);
    }

    @Override
    public Long countUserSessionByCheckInStatusAndSessionId(Long sessionId) {
        return userSessionRepo.countUserSessionByCheckInStatusAndSessionId(Arrays.asList(CHECK_IN_AVAILABLE, PAST_SESSION_CHECK_IN, IN_MEETING), sessionId);
    }

    @Override
    public Long countUserSessionBySessionStatusAndSessionId(Long sessionId) {
        return userSessionRepo.countUserSessionBySessionStatusAndSessionId(Arrays.asList(REGISTERED), sessionId);
    }

    @Override
    public List<Long> getAddToCalendarEventSessionByUserIdAndSessionId(List<Long> sessionIds, Long userId, Long eventId) {
            return userSessionCalendarRepo.getCheckInSessionByUserIdAndSessionId(sessionIds,userId,eventId);
    }

    @Override
    public List<IdCountDto> countSessionAttendeeByEventTicketIdAndSessionIdIn(List<Long> sessionIds) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : userSessionRepo.countSessionAttendeeByEventTicketIdAndSessionIdIn(sessionIds);
    }

    @Override
    public long countSessionAttendeeByEventTicketIdAndSessionId(Long sessionId) {
        return userSessionRepo.countSessionAttendeeByEventTicketIdAndSessionId(sessionId);
    }

    @Override
    public List<IdCountDto> countSessionAttendeeByNetworkingSessionIdIn(List<Long> sessionIds) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : userSessionRepo.countSessionAttendeeByNetworkingSessionIdIn(sessionIds);
    }

    @Override
    public List<Long> findUserByEventIdAndSessionIdAndCheckInStatus(Long sessionId, Long eventId) {
        return userSessionRepo.findUserByEventIdAndSessionIdAndCheckInStatus(sessionId, eventId);
    }

    @Override
    public Long countUserSessionByEventIdAndCheckInStatusOrCheckInTimeIsNull(Long eventId) {
        return userSessionRepo.countUserSessionByEventIdAndCheckInStatusOrCheckInTimeIsNull(eventId);
    }

    @Override
    public Long countUserRegisteredByEventIdAndEventTicketIdIsNotNull(Long eventId) {
        return userSessionRepo.countUserRegisteredByEventIdAndEventTicketIdIsNotNull(eventId);
    }


    @Override
    public Long findUserSessionCountByEventIdAndSessionIdAndCheckInStatus(long sessionId, long eventId) {
        return userSessionRepo.countByEventIdAndSessionIdAndCheckInStatus(sessionId,eventId);
    }

    @Override
    public Long findUserSessionCountByEventIdAndSessionIdAndCheckInStatusAndTicketTypeFormat(long sessionId, long eventId, List<TicketTypeFormat> ticketTypeFormats) {
        return userSessionRepo.countByEventIdAndSessionIdAndCheckInStatusAndTicketTypeFormat(sessionId,eventId,ticketTypeFormats);
    }

    @Override
    public Long findUserSessionCountByEventIdAndSessionIdAndCheckInStatusAndUserIdIn(long sessionId, long eventId, List<Long> allStaffAndAdminUserIds) {
        return userSessionRepo.countByEventIdAndSessionIdAndCheckInStatusAndUserIdIn(sessionId,eventId,allStaffAndAdminUserIds);
    }

    @Override
    public Page<RegisterdHolderUsers> findRegisteredUserSessionBySessionIdWithPagination(Long sessionId, List<Long> ticketTypeIds, List<TicketType> ticketTypes, List<EnumUserSessionStatus> checkInStatus, List<EnumUserSessionStatus> userSessionStatuses, Pageable pageable) {
        return userSessionRepo.findRegisteredUserSessionBySessionIdWithPagination(sessionId,ticketTypeIds,ticketTypes,checkInStatus,userSessionStatuses,pageable);
    }

    @Override
    public Page<RegisterdHolderUsers> findRegisteredUserSessionBySessionIdWithPaginationAndSearch(Long sessionId, List<Long> ticketTypeIds, List<TicketType> ticketTypes, List<EnumUserSessionStatus> checkInStatus, List<EnumUserSessionStatus> userSessionStatuses, Pageable pageable, String search) {
        return userSessionRepo.findRegisteredUserSessionBySessionIdWithPaginationAndSearch(sessionId,ticketTypeIds,ticketTypes,checkInStatus,userSessionStatuses,pageable,search);

    }

    @Override
    public Long findUserSessionCountByEventIdAndSessionIdAndUserIdIn(long id, long eventId, List<Long> allStaffAndAdminUserIds) {
        return userSessionRepo.countUserSessionByEventIdAndSessionIdAndUserIds(id,eventId, allStaffAndAdminUserIds);
    }

    @Override
    public Long findUserSessionCountByEventIdAndSessionIdAndUserIdInAndTicketTypeFormat(long id, long eventId, List<Long> allStaffAndAdminUserIds, List<TicketTypeFormat> ticketTypeFormats) {
        return userSessionRepo.countUserSessionByEventIdAndSessionIdAndUserIdsAndTicketTypeFormat(id,eventId, allStaffAndAdminUserIds,ticketTypeFormats);
    }

    @Override
    public List<Object[]> findByEventIdAndEventTicketIdInAndUserIdInAndCheckInStatus(long eventId, List<Long> eventTicketIdsList, List<Long> userIdsList, EnumUserSessionStatus checkInStatus) {
        return userSessionRepo.findByEventIdAndEventTicketIdInAndUserIdInAndCheckInStatus(eventId,eventTicketIdsList,userIdsList,checkInStatus);
    }

    @Override
    public UserSession findBySessionIdAndUserIdAndEventIdAndTicketId(long sessionId, Long userId, long eventId, Long eventTicketId) {
        return userSessionRepo.findBySessionIdAndUserIdAndEventIdAndTicketId(sessionId,userId,eventId,eventTicketId);
    }

    @Override
    public UserSession findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(long sessionId, Long userId, long eventId, Long eventTicketId) {
        return userSessionRepo.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(sessionId,userId,eventId,eventTicketId);
    }

    @Override
    public boolean isUserRegisteredInSession(long sessionId, Long userId, Event event) {
        return userSessionRepo.isUserRegisteredInSession(sessionId,userId,event.getEventId());
    }

    @Override
    public List<IdCountDto> countBySessionIdInAndEventIdAndTicketIdIsNotNull(List<Long> sessionIds, long eventId) {
        return userSessionRepo.countBySessionIdInAndEventIdAndTicketIdIsNotNull(sessionIds, eventId);
    }

    @Override
    public Integer countBySessionIdAndTicketIdIsNotNullAndEventId(Long sessionId, long eventId) {
        return userSessionRepo.countBySessionIdAndTicketIdIsNotNullAndEventId(sessionId, eventId);
    }

    @Override
    public List<IdCountDto> countSessionBookmarkedAttendeeBySessionIdsAndEventId(List<Long> sessionIds, long eventId) {
        return userSessionRepo.countSessionBookmarkedAttendeeBySessionIdsAndEventId(sessionIds, eventId);
    }

    @Override
    public List<RegisterdHolderUsers> findRegisteredUsersBySessionIdAndRegisteredStatus(long sessionId, EnumUserSessionStatus sessionStatus) {
        return userSessionRepo.findRegisteredUsersBySessionIdAndRegisteredStatus(sessionId,sessionStatus);
    }

    @Override
    public List<UserSession> findRegisteredUserByEventTicketIdAndSessionIdAndEventId(Long eventTicketId, Long sessionId, Long eventId,EnumUserSessionStatus sessionStatus) {
        return userSessionRepo.findRegisteredUserByEventTicketIdAndSessionIdAndEventId(eventTicketId,sessionId,eventId,sessionStatus);
    }

    @Override
    public List<Long> getRegisteredSessionIdsByEventIdAndUserId(long eventId, Long userId) {
        return userSessionRepo.getRegisteredSessionIdsByEventIdAndUserId(eventId, userId);
    }

    @Override
    public List<Long> getBookmarkedSessionIdsByEventIdAndUserId(long eventId, Long userId) {
        return userSessionRepo.getBookmarkedSessionIdsByEventIdAndUserId(eventId, userId);
    }

    @Override
    public List<Long> getAddToCalendarSessionByUserIdAndSessionId(long eventId, Long userId) {
        return userSessionCalendarRepo.getAddToCalendarSessionByUserIdAndSessionId(userId,eventId);
    }

    @Override
    public void delete(UserSession userSession) {
         userSessionRepo.delete(userSession);
    }

    @Override
    public List<UserSession> findAllByEventIdAndUserIdAndSessionStatusesAndCapacity(Session session, Event event, Long userId, boolean checkCapacity) {
        return userSessionRepo.findAllByEventIdAndUserIdAndSessionStatusesAndCapacity(session.getId(),event.getEventId(), userId, Arrays.asList(REGISTERED,CHECK_IN_AVAILABLE,IN_MEETING), checkCapacity);
    }


    @Override
    public List<Object[]> getRegisteredSessionCountForUserByTracks(User user, Event event, List<Long> sessionTracksIds) {
        return userSessionRepo.getRegisteredSessionCountForUserByTracks(user.getUserId(), event.getEventId(), sessionTracksIds);
    }

    @Override
    public List<UserSession> findBySessionIdAndUserIdAndEventIdAndSessionStatus(long sessionId, Long userId, Long eventId, EnumUserSessionStatus sessionStatus) {
        return userSessionRepo.findBySessionIdAndUserIdAndEventIdAndSessionStatus(sessionId,userId,eventId,sessionStatus);
    }

    @Override
    public List<Long> getWaitListedSessionIdsByEventIdAndUserId(long eventId, Long userId) {
        return userSessionRepo.getWaitListedSessionIdsByEventIdAndUserId(eventId, userId);
    }

    @Override
    public List<UserSession> findAllBySessionIdAndIsWaitlisted(long sessionId) {
        return userSessionRepo.findBySessionIdAndIsWaitlisted(sessionId);
    }

    @Override
    public List<UserSession> findBySessionIdAndEventIdAndUserIdIn(Long sessionId, Long eventId, List<Long> userIds) {
        return userSessionRepo.findBySessionIdAndEventIdAndUserIdIn(sessionId, eventId, userIds);
    }
}
