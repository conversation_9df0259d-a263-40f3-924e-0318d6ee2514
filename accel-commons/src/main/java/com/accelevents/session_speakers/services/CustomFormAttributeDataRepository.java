package com.accelevents.session_speakers.services;

import com.accelevents.domain.session_speakers.CustomFormAttributeData;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomFormAttributeDataRepository extends CrudRepository<CustomFormAttributeData,Long> {

    @Modifying
    @Query(nativeQuery=true,
            value = " UPDATE custom_form_attribute_data sad " +
                    " JOIN speakers s on sad.id = s.speaker_attribute " +
                    " SET sad.json_value =REPLACE(sad.json_value,:jsonOldName,:jsonNewName) " +
                    " WHERE s.event_id = :eventId and sad.id > 0")
    void updateAttributeForSpeaker(@Param("jsonOldName") String jsonOldName,
                         @Param("eventId") Long eventId,
                         @Param("jsonNewName") String jsonNewName);

    @Modifying
    @Query(nativeQuery=true,
            value = " UPDATE custom_form_attribute_data sad " +
                    " JOIN sessions s on sad.id = s.session_attribute " +
                    " SET sad.json_value =REPLACE(sad.json_value,:jsonOldName,:jsonNewName) " +
                    " WHERE s.event_id = :eventId and sad.id > 0")
    void updateAttributeForSession(@Param("jsonOldName") String jsonOldName,
                         @Param("eventId") Long eventId,
                         @Param("jsonNewName") String jsonNewName);

    List<CustomFormAttributeData> findByIdIn(List<Long> ids);

    @Modifying
    @Query(nativeQuery = true,
            value = "UPDATE custom_form_attribute_data sad " +
                    "JOIN contacts c ON sad.id = c.contact_attribute " +
                    "SET sad.json_value = JSON_SET(sad.json_value, CONCAT('$.attributes.', :fieldKey), '') " +
                    "WHERE c.event_id = :eventId AND sad.id > 0")
    void updateAttributeForContact(@Param("eventId") Long eventId,
                                           @Param("fieldKey") String fieldKey);
}
