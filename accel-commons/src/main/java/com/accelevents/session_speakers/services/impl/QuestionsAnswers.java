package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.User;
import com.accelevents.dto.QuestionAnswerDTO;
import com.accelevents.graphql.GraphQL;
import com.accelevents.messages.EnumQNAType;
import com.accelevents.utils.Constants;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QuestionsAnswers extends GraphQL {

	public QuestionsAnswers(GraphQLConfiguration configuration, String authToken){
		super(configuration.getAppsyncQAApiUrl(), authToken);
	}

	public JSONArray getAllQuestionsAnswers(Long typeId, String type) throws JSONException {
			String operationName = "listQuestionAnswers";
            type = "\"" + type + "\"";
			JSONObject mainRequest = new JSONObject();
			mainRequest.put("operationName",operationName);
            mainRequest.put("variables", "{ \"filter\": {\n \"type\": {\n \"eq\": " + type + "\n},\n \"status\": {\n \"ne\": \"DELETE\"\n}}, \"limit\": 6000, \"nextToken\": null }");

			mainRequest.put("query","query listQuestionAnswers($filter: TableQuestionAnswersFilterInput, $limit: Int, $nextToken: String) {  " +
					"listQuestionAnswers(typeId: "+typeId+", filter: $filter, limit: $limit, nextToken: $nextToken) {  " +
					"  items {  " +
                    "    questionId  " +
                    "    typeId  " +
					"    userName  " +
					"    questionText  " +
					"    answer  " +
					"    status  " +
					"    answerType  " +
					"    userId  " +
					"    }  " +
					"  }  " +
					"    }");
			return   executeItemList(mainRequest, operationName);
	}

    JSONArray getAllQuestions(Long typeId, String type) throws JSONException {
        String operationName = "listQuestionAnswers";
        type = "\"" + type + "\"";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put(Constants.OPERATION_NAME,operationName);
        mainRequest.put(Constants.VARIABLES, "{ \"filter\": {\n \"type\": {\n \"eq\": " + type + "\n},\n \"status\": {\n \"ne\": \"DELETE\"\n}}, \"limit\": 6000, \"nextToken\": null }");

        mainRequest.put(Constants.QUERY,"query listQuestionAnswers($filter: TableQuestionAnswersFilterInput, $limit: Int, $nextToken: String) {  " +
                "listQuestionAnswers(typeId: "+typeId+", filter: $filter, limit: $limit, nextToken: $nextToken) {  " +
                "  items {  " +
                "    typeId  " +
                "    questionText  " +
                "    answer  " +
                "    answerType  " +
                "    isPrivateQuestion  " +
                "    isPrivateResponse  " +
                "    status  " +
                "    createdAt  " +
                "    type  " +
                "    userId  " +
                "    userName  " +
                "    answeredBy  " +
                "    answeredAt  " +
                "    }  " +
                "  }  " +
                "    }");
        return   executeItemList(mainRequest, operationName);
    }

    void createPollQuestions(JSONObject questionAnswers, String authorization){

        try{

            Map<String,String> header = new HashMap<String,String>(){{
                put("Authorization",authorization);
            }};

            JSONObject variable = new JSONObject();
            variable.put("cloneQuestionAnswersInput", questionAnswers);

            JSONObject mainRequest = new JSONObject();
            String operationName = "cloneQuestionAnswers";
            mainRequest.put(Constants.OPERATION_NAME,operationName);
            mainRequest.put(Constants.VARIABLES,variable);
            mainRequest.put(Constants.QUERY,"mutation cloneQuestionAnswers($cloneQuestionAnswersInput: CloneQuestionAnswersInput!) { cloneQuestionAnswers(input: $cloneQuestionAnswersInput) { typeId } }");

            createQuestionAnswer(mainRequest, operationName,header);

        }
        catch (JSONException ignore){
            logger.warn("unable to create question poll {}",questionAnswers);
        }
    }

	protected JSONArray executeItemList(JSONObject mainRequest, String operationName) {
		try {
			HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
					.headers( getHeaders())
					.body(mainRequest.toString())
					.asJson();
			JSONObject res = response.getBody().getObject();
			JSONObject data = res.getJSONObject("data");
			JSONObject operationData = (JSONObject) data.get(operationName);
			return operationData.getJSONArray("items");
		} catch (UnirestException e) {
            logger.info("UnirestException | operationName => {} and mainRequest => {}", operationName, mainRequest);
			logger.info("QuestionsExecuteUnirest {} ", e.getMessage());
			e.printStackTrace();
		} catch (JSONException e) {
            logger.info("JSONException | operationName => {} and mainRequest => {}", operationName, mainRequest);
			logger.info("QuestionsExecuteJSONException {} ", e.getMessage());
			e.printStackTrace();
		}
		return null;
	}

    public void updateDeleteStatusForQNAByEvent(long eventId, List<Long> sessionIds, List<Long> exhibitorIds) throws JSONException {
        List<JSONObject> jsonObjectList = new ArrayList<>();

        for (Long id : sessionIds) {
            JSONArray jsonArray = getAllQuestionsAnswers(id, EnumQNAType.SESSION.name());
            if (null != jsonArray) {
                int length = jsonArray.length();
                if (length > 0) {
                    logger.info("Update qna for event => {} and sessionId => {} with length => {}", eventId, id, length);
                    for (int i = 0; i < length; i++) {
                        jsonObjectList.add((JSONObject) jsonArray.get(i));
                    }
                } else {
                    logger.info("Update qna is empty for event => {} and sessionId => {} ", eventId, id);
                }
            }
        }

        for (Long id : exhibitorIds) {
            JSONArray jsonArray = getAllQuestionsAnswers(id, EnumQNAType.EXPO.name());
            if (null != jsonArray) {
                int length = jsonArray.length();
                if (length > 0) {
                    logger.info("Update qna for event => {} and exhibitorId => {} with length => {}", eventId, id, length);
                    for (int i = 0; i < length; i++) {
                        jsonObjectList.add((JSONObject) jsonArray.get(i));
                    }
                } else {
                    logger.info("Update qna is empty for event => {} and exhibitorId => {} ", eventId, id);
                }
            }
        }
        logger.info("Total qna need to update for event => {} with length => {}", eventId, jsonObjectList.size());
        for (JSONObject object : jsonObjectList) {
            updateQNAStatus(object);
        }
    }

    private void updateQNAStatus(JSONObject qnaObject) throws JSONException {
        JSONObject requestData = new JSONObject();
        requestData.put("questionId", qnaObject.get("questionId"));
        requestData.put("typeId", qnaObject.get("typeId"));
        requestData.put("status", Constants.DELETE);

        JSONObject variable = new JSONObject();
        variable.put("updateQuestionAnswersInput", requestData);

        JSONObject mainRequest = new JSONObject();
        String operationName = "updateQuestionAnswersStatus";
        mainRequest.put("operationName", operationName);
        mainRequest.put("variables", variable);
        mainRequest.put("query", "mutation updateQuestionAnswersStatus($updateQuestionAnswersInput: UpdateQuestionAnswersInput!) { updateQuestionAnswersStatus(input: $updateQuestionAnswersInput) { questionId typeId status } }");

        executeQNAOperation(mainRequest, operationName);
    }

    private void executeQNAOperation(JSONObject mainRequest, String operationName) {
        try {
            HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
                    .headers(getHeaders())
                    .body(mainRequest.toString())
                    .asJson();
            JSONObject res = response.getBody().getObject();
            JSONObject data = res.getJSONObject("data");
            JSONObject operationData = (JSONObject) data.get(operationName);
        } catch (UnirestException e) {
            logger.info("UnirestException | operationName => {} and mainRequest => {}", operationName, mainRequest);
            logger.info("QuestionsExecuteUnirest {} ", e.getMessage());
        } catch (JSONException e) {
            logger.info("JSONException | operationName => {} and mainRequest => {}", operationName, mainRequest);
            logger.info("QuestionsExecuteJSONException {} ", e.getMessage());
        }
    }

    private void createQuestionAnswer(JSONObject mainRequest, String operationName, Map<String, String> header) {
        try {
            header.putAll(getHeaders());
            HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
                    .headers(header)
                    .body(mainRequest.toString())
                    .asJson();
            JSONObject res = response.getBody().getObject();
            logger.info("createQuestionAnswer {}",res.toString());
            JSONObject data = res.getJSONObject("data");
            JSONObject operationData = (JSONObject) data.get(operationName);
        } catch (UnirestException e) {
            logger.info("UnirestException | operationName => {} and mainRequest => {}", operationName, mainRequest);
            logger.info("QuestionsExecuteUnirest {} ", e.getMessage());
        } catch (JSONException e) {
            logger.info("JSONException | operationName => {} and mainRequest => {}", operationName, mainRequest);
            logger.info("QuestionsExecuteJSONException {} ", e.getMessage());
        }
    }


    public void upvoteQuestionAnswer(User user, QuestionAnswerDTO questionAnswerDTO) {
	    try{
	        logger.info("Request received to upvote questin for type {} and type id {} by user {}", questionAnswerDTO.getType(), questionAnswerDTO.getTypeId(), user.getUserId());

            JSONObject questionsAnswer = getQuestionsAnswer(questionAnswerDTO.getTypeId(), questionAnswerDTO.getQuestionId());
            if(questionsAnswer.isNull("upvote")){
                questionsAnswer.put("upvote",new JSONArray());
            }
            JSONArray upvotes = questionsAnswer.getJSONArray("upvote");
            List<Long> upvote = new ObjectMapper().readValue(upvotes.toString(),new TypeReference<List<Long>>(){});

            if(upvote.contains(user.getUserId())){
                logger.info("Upvote already exists by user {} for questin id {} so removed from upvotes.", user.getUserId(), questionAnswerDTO.getQuestionId());
                upvote.remove(user.getUserId());
            }
            else {
                upvote.add(user.getUserId());
            }
            upvote(questionAnswerDTO.getQuestionId(), questionAnswerDTO.getTypeId(), questionAnswerDTO.getType().getType(), new JSONArray(upvote),questionsAnswer.getString("status"));
        }
	    catch (Exception exception){
	        logger.info(exception.getMessage());
        }
    }

    public JSONObject getQuestionsAnswer(Long typeId, String questionId) throws JSONException {
        String operationName = "getQuestionAnswers";
        questionId = "\"" + questionId + "\"";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put("operationName",operationName);

        mainRequest.put("query","query getQuestionAnswers {\n" +
                "  getQuestionAnswers(questionId: "+questionId+", typeId: "+typeId+") {\n" +
                "    upvote\n" +
                "    status\n" +
                "  }\n" +
                "}");
        return   executeAndGetQuestionAnswer(mainRequest, operationName);
    }

    private void upvote(String questionId, Long typeId,String type, JSONArray upvotes, String status) throws JSONException {

        JSONObject requestData = new JSONObject();
        requestData.put("questionId", questionId);
        requestData.put(Constants.GRAPHQL_TYPEID, typeId);
        requestData.put("upvote", upvotes);
        requestData.put(Constants.TYPE_LOWER_CASE, type);
        requestData.put(Constants.STATUS, status);

        JSONObject variable = new JSONObject();
        variable.put("updateQuestionAnswersInput", requestData);

        JSONObject mainRequest = new JSONObject();
        String operationName = "upvoteQuestionAnswers";
        mainRequest.put(Constants.OPERATION_NAME, operationName);
        mainRequest.put(Constants.VARIABLES, variable);
        mainRequest.put(Constants.QUERY, "mutation upvoteQuestionAnswers($updateQuestionAnswersInput: UpdateQuestionAnswersInput!) { upvoteQuestionAnswers(input: $updateQuestionAnswersInput) { type typeId questionId userId questionText userName status answerType answer isPrivateResponse isPrivateQuestion upvote answeredBy answeredAt} }");

        executeQNAOperation(mainRequest, operationName);
    }

    private JSONObject executeAndGetQuestionAnswer(JSONObject mainRequest, String operationName) {
        try {
            HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
                    .headers( getHeaders())
                    .body(mainRequest.toString())
                    .asJson();
            JSONObject res = response.getBody().getObject();
            JSONObject data = res.getJSONObject("data");
            JSONObject operationData = (JSONObject) data.get(operationName);
            return operationData;
        } catch (UnirestException e) {
            logger.info("UnirestException | operationName => {} and mainRequest => {}", operationName, mainRequest);
            logger.info("QuestionsExecuteUnirest {} ", e.getMessage());
            e.printStackTrace();
        } catch (JSONException e) {
            logger.info("JSONException | operationName => {} and mainRequest => {}", operationName, mainRequest);
            logger.info("QuestionsExecuteJSONException {} ", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
}
