package com.accelevents.session_speakers.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.common.dto.GetStreamUserInfoDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumLabelLanguageCode;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumSessionStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.session.ROSessionSpeakerService;
import com.accelevents.services.GetStreamService;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.services.SessionSpeakerRepoService;
import com.accelevents.session_speakers.services.SessionSpeakerService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigInteger;
import java.util.*;

import static com.accelevents.domain.enums.EnumSessionFormat.*;
import static com.accelevents.utils.Constants.*;

@Service
public class SessionSpeakerServiceImpl implements SessionSpeakerService {

    private static final Logger logger = LoggerFactory.getLogger(SessionSpeakerServiceImpl.class);

    @Autowired private SpeakerRepoService speakerRepoService;
    @Autowired private GetStreamService getStreamService;
    @Autowired private SessionSpeakerRepoService sessionSpeakerRepoService;
    @Autowired private ROSessionSpeakerService roSessionSpeakerService;
    @Autowired private ClearAPIGatewayCache clearAPIGatewayCache;
    @Autowired private UserSessionService userSessionService;
    private double sequence = 1000;

    @Override
    public boolean addSessionSpeaker(Long sessionId, Long speakerId, Event event, boolean isSpeakerInviteEnable) {
        if (!sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, speakerId).isPresent()) {
            Speaker speaker = speakerRepoService.getSpeakerByIdOrThrowNotFound(speakerId, event);

            double position = getPositionForSessionSpeaker(sessionId);
            boolean sessionSpeakerAlreadyAdded = sessionSpeakerRepoService.sessionSpeakerAlreadyAddedInThisSession(sessionId, event.getEventId());
            clearAPIGatewayCache.clearAPIGwSpeakerPortalCache(event.getEventURL());
            save(new SessionSpeaker(sessionId, speakerId, position, speaker, !sessionSpeakerAlreadyAdded));

            GetStreamUserInfoDto userInfoDto = new GetStreamUserInfoDto(speaker.getFirstName() + " " + speaker.getLastName(), speaker.getUserId(), event.getEventId(), speaker.getImageUrl(),CommonUtil.getSplitName(speaker.getFirstName()) + " " + CommonUtil.getSplitName(speaker.getLastName()));
            getStreamService.addUserAsMemberOrModerator(SESSION_.concat(String.valueOf(sessionId)),LIVESTREAM,
                    Collections.singletonList(userInfoDto), null, false, true);
            getStreamService.addUserAsMemberOrModerator(BACKSTAGE_.concat(String.valueOf(sessionId)),LIVESTREAM,
                    Collections.singletonList(userInfoDto), null, false, true);
            getStreamService.addMemberToChannel(ADMIN_DES_.concat(String.valueOf(event.getEventId())), DES_MESSAGING,
                    Collections.singletonList(userInfoDto.getUserId()));

            userSessionService.registerSpeakerUser(speaker.getUserId(),sessionId, event);
            return true;
        }
        return false;
    }

    @Override
    public List<SpeakerDTO> getSpeakerDtoBySession(Long sessionId) {
        List<SpeakerDTO> speakerListBySession = sessionSpeakerRepoService.getSpeakersBySession(sessionId);
        return CollectionUtils.isEmpty(speakerListBySession) ? Collections.emptyList() : speakerListBySession;
    }

    @Override
    public Page<Session> getSessionByEventIdAndSpeakerUserId(Event event, Long userId,
															 String search,
															 Pageable pageable,
															 Boolean past, Boolean isAdminOrStaff){
        List<EnumSessionFormat> enumSessionFormats = Arrays.asList(BREAK,OTHER,EXPO);
        if(past == null){
            return sessionSpeakerRepoService.findSessionBySpeakerUserId(event.getEventId(),userId,search, pageable,enumSessionFormats, Boolean.FALSE.equals(isAdminOrStaff) ? EnumSessionStatus.VISIBLE : null);
        }

        Date currentDate = new Date();
        if(Boolean.TRUE.equals(past)){
            return sessionSpeakerRepoService.findSessionBySpeakerUserIdByPast(event.getEventId(),userId,search, currentDate, pageable, enumSessionFormats, Boolean.FALSE.equals(isAdminOrStaff) ? EnumSessionStatus.VISIBLE : null);
        } else {
            return sessionSpeakerRepoService.findSessionBySpeakerUserIdFuture(event.getEventId(),userId,search, currentDate, pageable,enumSessionFormats, Boolean.FALSE.equals(isAdminOrStaff) ? EnumSessionStatus.VISIBLE : null);
        }
    }

    @Override
    public void deleteSessionSpeakerBySpeaker(Long speakerId, Event event) {
        sessionSpeakerRepoService.deleteBySpeakerId(speakerId);
    }

    @Override
    public void deleteSessionSpeakerBySpeakers(List<Speaker> speakers, Event event) {
        List<Long> speakerIds = new ArrayList<>();
        speakers.forEach(speaker -> speakerIds.add(speaker.getId()));
        if(!CollectionUtils.isEmpty(speakerIds)) {
            sessionSpeakerRepoService.deleteBySpeakerIds(speakerIds);
        }
    }

    @Override
    public void deleteSessionSpeakerBySession(Long sessionId) {
        sessionSpeakerRepoService.updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);
    }

    @Override
    public Map<Long, List<Speaker>> getSessionSpeakerIdSessionIds(List<Long> sessionIds) {
        if(CollectionUtils.isEmpty(sessionIds)){
            return Collections.emptyMap();
        }
        List<SessionSpeaker> sessionSpeakers = findBySessionIdsIn(sessionIds);
        logger.info("getSessionSpeakerIdSessionIds|sessionIds|{}",sessionIds);
        Map<Long, List<Speaker>> sessionIdSpeakers = new HashMap<>();
        sessionSpeakers.forEach(sessionSpeaker -> {
            List<Speaker> speakers = sessionIdSpeakers.get(sessionSpeaker.getSessionId());

            if (speakers == null) {
                speakers = new ArrayList<>();
            }

            Speaker speaker = sessionSpeaker.getSpeaker();
            if(sessionSpeaker.isModerator() && sessionSpeaker.getSpeakerId().equals(speaker.getId())){
                speaker.setModerator(true); // This field is @Transient
            }
            speakers.add(speaker);
            sessionIdSpeakers.put(sessionSpeaker.getSessionId(), speakers);
        });

        logger.info("getSessionSpeakerIdSessionIds|sessionIdSpeakers|{}",sessionIdSpeakers);
        return sessionIdSpeakers;
    }

    @Override
    public Map<Long, List<SpeakerDTO>> getSessionSpeakerIdSessionDtoIds(List<Long> sessionIds) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyMap();
        }
        List<SessionSpeaker> sessionSpeakers = findBySessionIdsIn(sessionIds);
        logger.info("getSessionSpeakerIdSessionDtoIds |sessionIds|{}", sessionIds);
        Map<Long, List<SpeakerDTO>> sessionIdSpeakers = new HashMap<>();
        sessionSpeakers.forEach(sessionSpeaker -> {
            List<SpeakerDTO> speakers = sessionIdSpeakers.get(sessionSpeaker.getSessionId());

            if (speakers == null) {
                speakers = new ArrayList<>();
            }

            Speaker speaker = sessionSpeaker.getSpeaker();
            if (sessionSpeaker.isModerator() && sessionSpeaker.getSpeakerId().equals(speaker.getId())) {
                speaker.setModerator(true); // This field is @Transient
            }
            speakers.add(new SpeakerDTO(speaker, speaker.getPosition(), speaker.getModerator(), sessionSpeaker.isShowModerator()));
            sessionIdSpeakers.put(sessionSpeaker.getSessionId(), speakers);
        });

        return sessionIdSpeakers;
    }

    @Override
    @Transactional
    @CacheEvict(value = "getSessionSpeakerByEventId", allEntries = true)
    public String removeSessionSpeaker(Long sessionId, Long speakerId, Event event,Map<String, String> languageMap,String languageCode) {

        Optional<SessionSpeaker> sessionSpeakerOp = sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, speakerId);

        logger.info("removeSessionSpeaker|sessionId|{}|speakerId|{}|eventId|{}|sessionSpeakerOpIsPresent|{}",sessionId,speakerId,event.getEventId(),sessionSpeakerOp.isPresent());

        if(sessionSpeakerOp.isPresent()){
            SessionSpeaker sessionSpeaker = sessionSpeakerOp.get();
            Speaker speakerOrg = speakerRepoService.getSpeakerByIdOrThrowNotFound(speakerId, event);
            clearAPIGatewayCache.clearAPIGwSpeakerPortalCache(event.getEventURL());
            sessionSpeakerRepoService.deleteSessionSpeakerById(sessionSpeaker.getId());

            if (null != speakerOrg) {
                getStreamService.removeMemberFromChannel(SESSION_.concat(String.valueOf(sessionId)), LIVESTREAM,
                        Collections.singletonList(speakerOrg.getUserId()));
                getStreamService.removeMemberFromChannel(BACKSTAGE_.concat(String.valueOf(sessionId)), LIVESTREAM,
                        Collections.singletonList(speakerOrg.getUserId()));

                if (sessionSpeakerRepoService.findAllSessionIdBySpeakerUserId(speakerOrg.getUserId(), event.getEventId()).isEmpty()) {
                    getStreamService.removeMemberFromChannel(ADMIN_DES_.concat(String.valueOf(event.getEventId())), DES_MESSAGING,
                            Collections.singletonList(speakerOrg.getUserId()));
                }
            }

            if(sessionSpeaker.isModerator()){
                List<SessionSpeaker> speakerList = sessionSpeakerRepoService.findBySessionIdAndEventId(sessionId, event.getEventId());

                boolean isModeratorAvailable = speakerList.stream().anyMatch(SessionSpeaker::isModerator);
                logger.info("removeSessionSpeaker|speakerListSize|{}|isModeratorAvailable|{}",speakerList.size(),isModeratorAvailable);

                if(!CollectionUtils.isEmpty(speakerList) && !isModeratorAvailable) {

                    //Get Oldest Moderator
                    SessionSpeaker moderatorSpeaker = speakerList.stream().min(Comparator.comparing(SessionSpeaker::getId)).get();
                    logger.info("removeSessionSpeaker|Oldest speaker|{}",moderatorSpeaker.getSpeakerId());
                    //Set Moderator for the session
                    setModeratorForPassedSessionIdAndSpeakerId(sessionId,moderatorSpeaker.getSpeakerId(), event, true);
                    logger.info("removeSessionSpeaker|Make speaker as moderator|{}",moderatorSpeaker.getSpeakerId());
                    ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
                    String autoCreateModrator = resourceBundle.getString(languageMap.get(Constants.AUTO_CREATE_MODERATOR));
                    autoCreateModrator = autoCreateModrator.replace(FIRSTNAME,moderatorSpeaker.getSpeaker().getFirstName())
                                      .replace(LASTNAME,moderatorSpeaker.getSpeaker().getLastName());
                    return autoCreateModrator;
                }
            }
            userSessionService.unRegisterSpeakerUser(speakerOrg.getUserId(), sessionId, event);
        }
        return SUCCESS;
    }

    @Override
    public void save(SessionSpeaker sessionSpeaker) {
        sessionSpeaker.setUpdatedAt(new Date());
        sessionSpeakerRepoService.save(sessionSpeaker);
    }



    @Override
    public boolean isUserSpeakerInSession(Long userId, Long sessionId){
        BigInteger returnValue = sessionSpeakerRepoService.isUserSpeakerInSession(userId,sessionId);
        return returnValue != null && returnValue.intValue() > 0;
    }

    @Override
    public boolean isUserSpeakerInSessionByEventId(Long userId, Long eventId){
        BigInteger returnValue = sessionSpeakerRepoService.isUserSpeakerInSessionByEventId(userId,eventId);
        return returnValue != null && returnValue.intValue() > 0;
    }

    @Override
    public void saveAll(Set<SessionSpeaker> sessionSpeakers) {
            sessionSpeakerRepoService.saveAll(sessionSpeakers);
    }

    public double getPositionForSessionSpeaker(Long sessionId) {
            SessionSpeaker lastItem = findFirstBySessionIdOrderByPositionDesc(sessionId);
            if (lastItem != null) {
                return lastItem.getPosition() + sequence;
            }
            return sequence;
    }

    protected SessionSpeaker getNextPositionItem(SessionSpeaker sessionSpeaker) {
        List<SessionSpeaker> nextPositionItemList = sessionSpeakerRepoService.nextPositionSessionSpeaker(sessionSpeaker.getId(),sessionSpeaker.getSessionId(),sessionSpeaker.getPosition());
        return nextPositionItemList.isEmpty() ? null: nextPositionItemList.get(0);
    }

    protected SessionSpeaker getPreviousPositionItem(SessionSpeaker sessionSpeaker) {
        List<SessionSpeaker> previousPositionExhibitorList = sessionSpeakerRepoService.previousPositionSessionSpeaker(sessionSpeaker.getId(),sessionSpeaker.getSessionId(),sessionSpeaker.getPosition());
        return  previousPositionExhibitorList.isEmpty() ? null: previousPositionExhibitorList.get(0);
    }

    public void updateWithSequence(SessionSpeaker sessionSpeaker, Long topId, Long topBottomId, Long sessionId) {

        Optional<SessionSpeaker> topSessionSpeakerOpt = sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, topId);
        Optional<SessionSpeaker> topNextSessionSpeakerOpt = sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, topBottomId);

        //move to middle
        if (topSessionSpeakerOpt.isPresent() && topNextSessionSpeakerOpt.isPresent()) {
            SessionSpeaker topNextSessionSpeaker = topNextSessionSpeakerOpt.get();
            SessionSpeaker topSessionSpeaker = topSessionSpeakerOpt.get();
            double position = (topNextSessionSpeaker.getPosition() + topSessionSpeaker.getPosition()) / 2;

            if (1 == position) {
                SessionSpeaker nextSessionSpeaker = getNextPositionItem(sessionSpeaker);
                SessionSpeaker prevSessionSpeaker = getPreviousPositionItem(sessionSpeaker);
                double positionDifferent = (nextSessionSpeaker.getPosition() - prevSessionSpeaker.getPosition()) / 2;
                sessionSpeakerRepoService.updatePositionSessionSpeaker(sessionSpeaker.getSpeakerId(),topSessionSpeaker.getPosition(), sessionSpeaker.getPosition(), positionDifferent);
                sessionSpeaker.setPosition(topNextSessionSpeaker.getPosition());
                sessionSpeakerRepoService.save(sessionSpeaker);
            } else {
                sessionSpeaker.setPosition(position);
                sessionSpeakerRepoService.save(sessionSpeaker);
            }
            //move to top
        } else if (!topSessionSpeakerOpt.isPresent() && topNextSessionSpeakerOpt.isPresent()) {
            SessionSpeaker topNextSessionSpeaker = topNextSessionSpeakerOpt.get();
            sessionSpeaker.setPosition(topNextSessionSpeaker.getPosition() + sequence);
            sessionSpeakerRepoService.save(sessionSpeaker);
            //move at last
        } else if (topSessionSpeakerOpt.isPresent()) {
            SessionSpeaker topSessionSpeaker = topSessionSpeakerOpt.get();
            double posDiff = topSessionSpeaker.getPosition() - sequence;
            if (posDiff <= 1) {
                sessionSpeakerRepoService.updatePositionForAllSessionSpeakerBySessionId(sequence, sessionSpeaker.getSessionId());
            }
            sessionSpeaker.setPosition(sequence);
            sessionSpeakerRepoService.save(sessionSpeaker);
        }
    }

    @Override
    @Transactional
    public void updateSessionSpeakerPosition(Long id, Long topSessionSpeakerId, Long topBottomSessionSpeakerId, Long sessionId) {
        sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, id).ifPresent(sessionSpeaker -> updateWithSequence(sessionSpeaker, topSessionSpeakerId, topBottomSessionSpeakerId, sessionId));
    }

    @Override
    public List<SessionSpeaker> getAllBySessionId(long id) {
        return sessionSpeakerRepoService.findBySessionIdsBetween(id);
    }

    @Override
    public SessionSpeaker findFirstBySessionIdOrderByPositionDesc(Long sessionId) {
        return sessionSpeakerRepoService.findFirstBySessionIdOrderByPositionDesc(sessionId);
    }

    @Override
    public List<SessionSpeaker> findBySessionIdsIn(List<Long> sessionIds) {
        return sessionSpeakerRepoService.findBySessionIdsIn(sessionIds);
    }

    @Override
    public Map<Long, List<Long>> getSpeakerSessionIdBySpeakerIdAndSessionFormat(List<Long> speakerIds, List<EnumSessionFormat> sessionFormats) {
        if(CollectionUtils.isEmpty(speakerIds)){
            return Collections.emptyMap();
        }
        List<SessionSpeaker> sessionSpeakers = sessionSpeakerRepoService.findAllSessionSpeakerBySpeakerIdAndSessionFormat(speakerIds, sessionFormats);
        Map<Long, List<Long>> speakerIdSessions = new HashMap<>();
        sessionSpeakers.forEach(e -> {
            List<Long> sessionIdList = speakerIdSessions.get(e.getSpeakerId());
            if (sessionIdList == null) {
                sessionIdList = new ArrayList<>();
            }
            sessionIdList.add(e.getSession().getId());

            speakerIdSessions.put(e.getSpeakerId(), sessionIdList);
        });

        return speakerIdSessions;
    }

    @Override
    public List<Session> findSessionBySpeakerUserId(Event event, Long userId) {
        return sessionSpeakerRepoService.findSessionBySpeakerUserId(event.getEventId(),userId, Arrays.asList(BREAK,OTHER,EXPO));
    }


    @Override
    public List<SessionSpeaker> getAllSessionSpeakerBetweenSessionIds(long from, long to) {
        return  sessionSpeakerRepoService.getAllSessionSpeakerBetweenSessionIds(from, to);
    }

    @Override
    public void setModeratorForPassedSessionIdAndSpeakerId(Long sessionId, Long speakerId, Event event, boolean isModerator) {
        SessionSpeaker sessionSpeakers = sessionSpeakerRepoService.findBySessionIdAndSpeakerIdAndEventId(sessionId,speakerId,event.getEventId());

        logger.info("setModeratorForPassedSessionIdAndSpeakerId|sessionId|{}|speakerId|{}|event|{}|isModerator|{}",sessionId,speakerId,event.getEventId(),isModerator);

        if(null == sessionSpeakers){
            throw new NotFoundException(NotFoundException.SessionSpeakerNotFound.SESSION_SPEAKER_NOT_FOUND);
        }

        sessionSpeakers.setModerator(isModerator);
        save(sessionSpeakers);
    }

    @Override
    public Boolean isSpeakerModeratorInSession(Long sessionId, Long userId) {
        Boolean returnValue = sessionSpeakerRepoService.isSpeakerModeratorInSession(sessionId, userId);
        return null != returnValue && returnValue;
    }

    @Override
    public Long getSpeakerIdBySessionIdAndUserId(Long sessionId, Long userId) {
        return sessionSpeakerRepoService.getSpeakerIdBySessionIdAndUserId(sessionId, userId);
    }

    @Override
    public void isShowModeratorForSessionIdAndSpeakerId(Long sessionId, Long speakerId, Event event, boolean isShowModerator) {
        SessionSpeaker sessionSpeakers = sessionSpeakerRepoService.findBySessionIdAndSpeakerIdAndEventId(sessionId, speakerId, event.getEventId());

        logger.info("isShowModeratorForSessionIdAndSpeakerId|sessionId|{}|speakerId|{}|event|{}|isShowModerator|{}", sessionId, speakerId, event.getEventId(), isShowModerator);

        if (null == sessionSpeakers) {
            throw new NotFoundException(NotFoundException.SessionSpeakerNotFound.SESSION_SPEAKER_NOT_FOUND);
        }

        sessionSpeakers.setShowModerator(isShowModerator);
        clearAPIGatewayCache.clearAPIGwSpeakerPortalCache(event.getEventURL());
        save(sessionSpeakers);
    }

    @Override
    public List<Long> getAllSessionIdsBySpeakerId(Long speakerId) {
        return sessionSpeakerRepoService.getAllSessionIdsBySpeakerId(speakerId);
    }

    @Override
    public List<IdCountDto> getSessionIdAndSessionSpeakerCount(List<Long> sessionIds) {
        return CollectionUtils.isEmpty(sessionIds) ? Collections.emptyList() : sessionSpeakerRepoService.countSessionSpeakerBySessionIdIn(sessionIds);
    }

    @Override
    public List<Long> findSpeakerUserIdBySessionId(Long sessionId) {
        return sessionSpeakerRepoService.findSpeakerUserIdBySessionId(sessionId);
    }

    @Override
    public List<SessionSpeaker> findByEventId(Long eventId) {
        return sessionSpeakerRepoService.findByEventId(eventId);
    }

    @Override
    public boolean isSpeakerAvailable(Long speakerId){
        return sessionSpeakerRepoService.isSpeakerAvailable(speakerId);
    }

    @Override
    public void updateSessionSpeakerBySpeakerId(Long speakerId,Long currentSpeakerId){
        sessionSpeakerRepoService.updateSessionSpeakerBySpeakerId(speakerId,currentSpeakerId);
    }

    @Override
    public List<Long> findAllSessionIdBySpeakerUserIdWithOutWorkshop(Long userId, Long eventId) {
        return sessionSpeakerRepoService.findAllSessionIdBySpeakerUserId(userId,eventId);
    }

    @Override
    public List<SessionSpeaker> findByEventIdWithoutCache(long eventId) {
        return sessionSpeakerRepoService.findByEventIdWithoutCache(eventId);
    }

    @Override
    public List<EventTaskEmailDto> findSpeakersEmailNameAndSessionNameByEventIdAndSessionIds(Long eventId, List<Long> sessionIds){
        return sessionSpeakerRepoService.findSpeakersEmailNameAndSessionNameByEventIdAndSessionIds(eventId, sessionIds);
    }

    @Override
    public List<Long> findSessionByEventIdAndSpeakerUserId(Long eventId, Long userId) {
        return sessionSpeakerRepoService.findSessionByEventIdAndSpeakerUserId(eventId,userId);
    }
}
