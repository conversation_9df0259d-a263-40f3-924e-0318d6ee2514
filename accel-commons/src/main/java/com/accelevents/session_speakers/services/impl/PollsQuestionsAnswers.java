package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.graphql.GraphQL;
import com.accelevents.session_speakers.dto.PollAnsDto;
import com.accelevents.session_speakers.dto.PollQuesDto;
import com.accelevents.session_speakers.dto.PollsResultDto;
import com.accelevents.session_speakers.dto.PollsResultWrapper;
import com.accelevents.utils.Constants;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class PollsQuestionsAnswers extends GraphQL {

	public PollsQuestionsAnswers(GraphQLConfiguration configuration, String authToken){
		super(configuration.getAppsyncPollsApiUrl(), authToken);
	}

	public PollsResultWrapper getPollsResult(Long typeId, String type){
		try {
            PollsResultWrapper p = new PollsResultWrapper();
			Map<String, String> questionMap = getAllQuestions(typeId, type);
			if (CollectionUtils.isEmpty(questionMap)){
			    return p;
            }
            JSONArray jsonArray = getAllAnswersByTypeId(typeId, type);

			Map<Integer, List<PollsResultDto>> map = new HashMap<>();
			if (null != jsonArray){
                for(int i=0;i<jsonArray.length();i++){
                    JSONObject pollResult = (JSONObject) jsonArray.get(i);
                    String questionId = pollResult.getString("pollsQuestionId");
                    if (null != questionMap.get(questionId)) {
                        Integer userId = pollResult.getInt("userId");
                        List<PollsResultDto> pollResultList = map.get(userId);
                        if(pollResultList==null){
                            pollResultList = new ArrayList<>();
                        }
                        pollResultList.add(new PollsResultDto(pollResult, questionMap.get(questionId)));
                        map.put(userId, pollResultList);
                    }
                }
            }
			p.setQuestionMap(questionMap);
			p.setUserAnswersMap(map);
			return p;
		} catch (JSONException e) {
			e.printStackTrace();
		}
		return new PollsResultWrapper();
	}

	public Map<String, String> getAllQuestions(Long typeId, String type) throws JSONException {
        type = "\"" + type + "\"";
        String operationName = "listPollsQuestionsByTypeId";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put("operationName",operationName);
        mainRequest.put("variables", "{ \"filter\": {\n \"type\": {\n \"eq\": " + type + "\n},\n \"status\": {\n \"ne\": \"DELETE\"\n}}, \"limit\": 6000, \"nextToken\": null }");

        mainRequest.put("query","query listPollsQuestionsByTypeId($filter: TablePollsQuestionsFilterInput, $limit: Int, $nextToken: String) {\n" +
                "  listPollsQuestionsByTypeId(typeId: " + typeId + ", filter: $filter, limit: $limit, nextToken: $nextToken) {\n" +
				"    items {\n" +
				"      id\n" +
                "      typeId\n" +
				"      eventId\n" +
				"      questionText\n" +
				"    }\n" +
				"  }\n" +
				"}");
		JSONArray jsonArray = executeItemList(mainRequest, operationName);
		Map<String, String> questionIdQuestionTextMap = new HashMap<>();
		if (null == jsonArray){
            return questionIdQuestionTextMap;
        }
		for(int i = 0; i<jsonArray.length();i++){
			JSONObject pollQuestions = (JSONObject) jsonArray.get(i);
			String questionId  = pollQuestions.getString("id");
			String questionText  = pollQuestions.getString("questionText");
			questionIdQuestionTextMap.put(questionId,questionText);
		}
		return questionIdQuestionTextMap;
	}

    public JSONArray getAllAnswersByTypeId(Long typeId, String type) throws JSONException {
        type = "\"" + type + "\"";
		String operationName = "listPollsResults";
		JSONObject mainRequest = new JSONObject();
		mainRequest.put("operationName",operationName);
        mainRequest.put("variables", "{ \"filter\": {\n \"type\": {\n \"eq\": " + type + "\n}\n}, \"limit\": 6000, \"nextToken\": null }");
		mainRequest.put("query","query listPollsResults($filter: TablePollsResultFilterInput, $limit: Int, $nextToken: String) {\n" +
                "  listPollsResults(typeId: " + typeId + ", filter: $filter, limit: $limit, nextToken: $nextToken) {\n" +
				"    items { " +
				"      pollsQuestionId " +
				"      id " +
				"      answers " +
				"      userId " +
				"    } " +
				"  } " +
				"}");
		return executeItemList(mainRequest, operationName);

	}

    public JSONObject getAllQuestionByIdAndTypeId(String id, Long typeId) throws JSONException {
        id = "\"" + id + "\"";
        String operationName = "getPollsQuestions";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put("operationName",operationName);
        mainRequest.put("query","query getPollsQuestions {\n" +
                "  getPollsQuestions(id: " +id+",typeId: " + typeId + ") {\n" +
                "      id " +
                "      choices " +
                "      questionText " +
                "      type " +
                "      typeId " +
                "  } " +
                "}");
        return executePollsOperation(mainRequest, operationName);

    }

	protected JSONArray executeItemList(JSONObject mainRequest, String operationName) {
		try {
			HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
					.headers( getHeaders())
					.body(mainRequest.toString())
					.asJson();
			JSONObject res = response.getBody().getObject();
			JSONObject data = res.getJSONObject("data");
			JSONObject operationData = (JSONObject) data.get(operationName);
			return operationData.getJSONArray("items");
		} catch (UnirestException e) {
			logger.info("PollsQuestionsExecuteUnirest {} ", e.getMessage());
			e.printStackTrace();
		} catch (JSONException e) {
			logger.info("PollsQuestionsExecuteJSONException {} ", e.getMessage());
			e.printStackTrace();
		}
		return null;
	}
    public Map<String, PollQuesDto> getAllPollQuestionsByTypeAndTypeId(Long typeId, String type) throws JSONException {
        type = "\"" + type + "\"";
        String operationName = "listPollsQuestionsByTypeId";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put("operationName", operationName);
        mainRequest.put("variables", "{ \"filter\": {\n \"type\": {\n \"eq\": " + type + "\n},\n \"status\": {\n \"ne\": \"DELETE\"\n}}, \"limit\": 6000, \"nextToken\": null }");

        mainRequest.put("query", "query listPollsQuestionsByTypeId($filter: TablePollsQuestionsFilterInput, $limit: Int, $nextToken: String) {\n" +
                "  listPollsQuestionsByTypeId(typeId: " + typeId + ", filter: $filter, limit: $limit, nextToken: $nextToken) {\n" +
                "    items {\n" +
                "      id\n" +
                "      typeId\n" +
                "      eventId\n" +
                "      questionText\n" +
                "      multipleChoiceQuestion\n" +
                "      choices\n" +
                "      status\n" +
                "    }\n" +
                "  }\n" +
                "}");
        JSONArray jsonArray = executeItemList(mainRequest, operationName);
        Map<String, PollQuesDto> questionIdQuestionTextMap = new HashMap<>();
        if (null == jsonArray) {
            return questionIdQuestionTextMap;
        }
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject pollQuestions = (JSONObject) jsonArray.get(i);
            String questionId = pollQuestions.getString("id");
            String questionText = pollQuestions.getString("questionText");
            boolean isMultiChoice = pollQuestions.getBoolean("multipleChoiceQuestion");
            JSONArray choicesArray = pollQuestions.getJSONArray("choices");
            String choices = "";
            if (choicesArray != null && choicesArray.length() > 0) {
                for (int j=0; j < choicesArray.length(); j++) {
                    if (j == 0) {
                        choices = choicesArray.getString(j);
                    } else {
                        choices = choices + "," + choicesArray.getString(j);
                    }
                }
            }
            String status = pollQuestions.getString("status");
            PollQuesDto pollQuesDto = new PollQuesDto(questionText, isMultiChoice ? "MULTIPLE CHOICE" : "SINGLE CHOICE", choices, status);
            questionIdQuestionTextMap.put(questionId, pollQuesDto);
        }
        return questionIdQuestionTextMap;
    }

    public LinkedHashMap<String, JSONObject> getAllPollQuestions(Long typeId, String type) throws JSONException {
        type = "\"" + type + "\"";
        String operationName = "listPollsQuestionsByTypeId";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put("operationName", operationName);
        mainRequest.put("variables", "{ \"filter\": {\n \"type\": {\n \"eq\": " + type + "\n},\n \"status\": {\n \"ne\": \"DELETE\"\n}}, \"limit\": 6000, \"nextToken\": null }");

        mainRequest.put("query", "query listPollsQuestionsByTypeId($filter: TablePollsQuestionsFilterInput, $limit: Int, $nextToken: String) {\n" +
                "  listPollsQuestionsByTypeId(typeId: " + typeId + ", filter: $filter, limit: $limit, nextToken: $nextToken) {\n" +
                "    items {\n" +
                "      id\n" +
                "      typeId\n" +
                "      type\n" +
                "      eventId\n" +
                "      questionText\n" +
                "      multipleChoiceQuestion\n" +
                "      choices\n" +
                "      showResultToUsers\n" +
                "      createdAt\n" +
                "      createdBy\n" +
                "      status\n" +
                "      attendeesNotifyBy\n" +
                "      showResultByPercentage\n" +
                "      showResultByNumber\n" +
                "    }\n" +
                "  }\n" +
                "}");
        JSONArray jsonArray = executeItemList(mainRequest, operationName);
        LinkedHashMap<String, JSONObject> questionIdQuestionTextMap = new LinkedHashMap<>();

        if(jsonArray == null){
            jsonArray = new JSONArray();
        }
        JSONArray sortedPollQuestions = sortJsonArray(jsonArray);
        for(int index =0; index<sortedPollQuestions.length(); index++){
            JSONObject jsonObject = sortedPollQuestions.getJSONObject(index);
            questionIdQuestionTextMap.put(String.valueOf(jsonObject.remove(Constants.ID)),jsonObject);
        }

        return questionIdQuestionTextMap;
    }

    private JSONArray sortJsonArray(JSONArray jsonArray) throws JSONException {
        JSONArray sortedJsonArray = new JSONArray();

        List<JSONObject> pollQuestions = new ArrayList<>();
        for (int i = 0; i < jsonArray.length(); i++) {
            pollQuestions.add(jsonArray.getJSONObject(i));
        }

        pollQuestions.sort(new Comparator<JSONObject>() {
            private static final String KEY_NAME = "createdAt";
            @Override
            public int compare(JSONObject a, JSONObject b) {

                try {
                    if(a.has(KEY_NAME) && b.has(KEY_NAME)){
                        return Long.compare(a.getLong(KEY_NAME),b.getLong(KEY_NAME));
                    }
                } catch (JSONException ignore) {
                }
                return 0;
            }
        });
        for (int i = 0; i < jsonArray.length(); i++) {
            sortedJsonArray.put(pollQuestions.get(i));
        }
        return sortedJsonArray;
    }

    String createPollQuestions(JSONObject pollQuestion, String authorization){

	    try{
	        Map<String,String> headers = new HashMap<String,String>(){{
	            put("Authorization",authorization);
            }};

            JSONObject mainRequest = new JSONObject();
            String operationName = "clonePollsQuestions";
            mainRequest.put(Constants.OPERATION_NAME,operationName);
            JSONObject variable = new JSONObject();
            variable.put("clonepollsquestionsinput", pollQuestion);

            mainRequest.put(Constants.VARIABLES,variable);
            mainRequest.put(Constants.QUERY,"mutation clonePollsQuestions($clonepollsquestionsinput: ClonePollsQuestionsInput!) {  clonePollsQuestions(input: $clonepollsquestionsinput) {id typeId type eventId questionText choices status multipleChoiceQuestion showResultToUsers createdAt __typename }}");
            JSONObject jsonObject = executePollsOperation(mainRequest, operationName, headers);

            return (jsonObject != null)? jsonObject.getString("id") :Constants.STRING_EMPTY;
        }
	    catch (JSONException ignore){
	        logger.warn("unable to create question poll {}",pollQuestion);
        }
	    return Constants.STRING_EMPTY;
    }
    void createPollResult(JSONObject pollResult, String authorization){

        try{

            Map<String,String> headers = new HashMap<String,String>(){{
                put("Authorization",authorization);
            }};

            JSONObject mainRequest = new JSONObject();
            String operationName = "clonePollsResult";
            mainRequest.put(Constants.OPERATION_NAME,operationName);
            JSONObject variable = new JSONObject();
            variable.put("clonePollsResult", pollResult);

            mainRequest.put(Constants.VARIABLES,variable);
            mainRequest.put(Constants.QUERY,"mutation  clonePollsResult($clonePollsResult:   ClonePollsResultInput!) {  clonePollsResult(input: $clonePollsResult)   {    pollsQuestionId    answers    userId    userMetaData    __typename }}");
            executePollsOperation(mainRequest, operationName,headers);

        }
        catch (JSONException ignore){
            logger.warn("unable to create question poll {}",pollResult);
        }
    }

    public Map<Long, List<PollAnsDto>> getAllPollResultByTypeAndTypeId(Long typeId, String type, List<String> questionIds) throws JSONException {
        JSONArray jsonArray = getAllAnswersByTypeId(typeId, type);
        Map<Long, List<PollAnsDto>> pollAnsMap = new HashMap<>();
        if (null != jsonArray) {
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject pollResult = (JSONObject) jsonArray.get(i);
                String questionId = pollResult.getString("pollsQuestionId");
                if (questionIds.contains(questionId)) {
                    Long userId = pollResult.getLong("userId");
                    List<PollAnsDto> pollAnsDtoList = pollAnsMap.get(userId);
                    String answers = "";
                    JSONArray ansArray = pollResult.getJSONArray("answers");
                    if (pollAnsDtoList != null && ansArray != null) {
                        Optional<PollAnsDto> pollAnsDtoOpt = pollAnsDtoList.stream().filter(pollAns -> pollAns.getQuestionId().equals(questionId)).findFirst();
                        if (pollAnsDtoOpt.isPresent()) {
                            PollAnsDto pollAnsDto = pollAnsDtoOpt.get();
                            pollAnsDtoList.remove(pollAnsDto);
                            String prevAns = pollAnsDto.getAnswer();
                            answers = ansArray.getString(0);
                            pollAnsDto.setAnswer(prevAns + "," + answers);
                            pollAnsDtoList.add(pollAnsDto);
                        } else {
                            answers = ansArray.getString(0);
                            pollAnsDtoList.add(new PollAnsDto(questionId, answers));
                        }
                    } else {
                        pollAnsDtoList = new ArrayList<>();
                        if (ansArray != null) {
                            answers = ansArray.getString(0);
                        }
                        pollAnsDtoList.add(new PollAnsDto(questionId, answers));
                    }
                    pollAnsMap.put(userId, pollAnsDtoList);
                }
        }
        }
        return pollAnsMap;
    }

    public void updateDeleteStatusForAllPollsByEvent(long eventId, String nextToken) throws JSONException {

        if (nextToken != null && !nextToken.equals("") && !nextToken.equals("\"null\"")) {
            JSONObject PollsToBeDelete = getAllPollsQuestionsByEventId(eventId, 1000, nextToken);
            if (null != PollsToBeDelete) {
                JSONArray jsonArrayOfPolls = PollsToBeDelete.getJSONArray("items");
                if (null != jsonArrayOfPolls) {
                    int length = jsonArrayOfPolls.length();
                    logger.info("Update polls for event => {} with length => {}",eventId, length);
                    for (int i = 0; i < length; i++) {
                        updatePollQuestionsStatus((JSONObject) jsonArrayOfPolls.get(i));
                    }
                }
                    nextToken = "\"" + PollsToBeDelete.get("nextToken").toString() + "\"";
                    logger.info("Next token ==> {}", nextToken);
                    updateDeleteStatusForAllPollsByEvent(eventId, nextToken);
            }
        }

    }

    private void updatePollQuestionsStatus(JSONObject pollQuestions) throws JSONException {
            JSONObject requestData = new JSONObject();
            requestData.put("id",pollQuestions.get("id"));
            requestData.put("typeId",pollQuestions.get("typeId"));
            requestData.put("status", Constants.DELETE);

            JSONObject variable = new JSONObject();
            variable.put("updatePollsQuestionsInput", requestData);

            JSONObject mainRequest = new JSONObject();
            String operationName = "updatePollsQuestionsStatus";
            mainRequest.put("operationName",operationName);
            mainRequest.put("variables",variable);
            mainRequest.put("query","mutation updatePollsQuestionsStatus($updatePollsQuestionsInput: UpdatePollsQuestionsInput!) { updatePollsQuestionsStatus(input: $updatePollsQuestionsInput) { id typeId eventId status } }");
            executePollsOperation(mainRequest, operationName);
    }

    private JSONObject getAllPollsQuestionsByEventId(long eventId, long limit, String nextToken) throws JSONException{
        String operationName = "listPollsQuestionsByEventId";
        JSONObject mainRequest = new JSONObject();
        mainRequest.put("operationName",operationName);
        mainRequest.put("variables","{ \"filter\": {\"status\" : {\"ne\" : \"DELETE\"}}, \"limit\": "+limit+", \"nextToken\": "+nextToken+" }");

        mainRequest.put("query", "query listPollsQuestionsByEventId($filter: TablePollsQuestionsFilterInput, $limit: Int, $nextToken: String) {\n" +
                "  listPollsQuestionsByEventId(eventId: " + eventId + ", filter: $filter, limit: $limit, nextToken: $nextToken) {\n" +
                "    items {\n" +
                "      id\n" +
                "      typeId\n" +
                "      eventId\n" +
                "      status\n" +
                "    }\n" +
                "    nextToken\n" +
                "  }\n" +
                "}");
        JSONObject operationData = executePollsOperation(mainRequest, operationName);
        return operationData;
    }

    private JSONObject executePollsOperation(JSONObject mainRequest, String operationName) {
        try {
            HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
                    .headers( getHeaders())
                    .body(mainRequest.toString())
                    .asJson();
            JSONObject res = response.getBody().getObject();
            JSONObject data = res.getJSONObject("data");
            JSONObject operationData = (JSONObject) data.get(operationName);
            return operationData;
        } catch (UnirestException e) {
            logger.info("UnirestException | operationName => {}  mainRequest => {}", operationName, mainRequest);
            logger.info("PollsQuestionsExecuteUnirest {} ", e.getMessage());
            e.printStackTrace();
        } catch (JSONException e) {
            logger.info("JSONException | operationName => {}  mainRequest => {}", operationName, mainRequest);
            logger.info("PollsQuestionsExecuteJSONException {} ", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    private JSONObject executePollsOperation(JSONObject mainRequest, String operationName,Map<String,String> headers) {
        try {

            headers.putAll(getHeaders());
            HttpResponse<JsonNode> response = Unirest.post(graphQlUrl)
                    .headers( headers)
                    .body(mainRequest.toString())
                    .asJson();
            JSONObject res = response.getBody().getObject();
            JSONObject data = res.getJSONObject("data");
            logger.info("executePollsOperation {}",res.toString());
            JSONObject operationData = (JSONObject) data.get(operationName);
            return operationData;
        } catch (UnirestException e) {
            logger.info("UnirestException | operationName => {}  mainRequest => {}", operationName, mainRequest);
            logger.info("PollsQuestionsExecuteUnirest {} ", e.getMessage());
            e.printStackTrace();
        } catch (JSONException e) {
            logger.info("JSONException | operationName => {}  mainRequest => {}", operationName, mainRequest);
            logger.info("PollsQuestionsExecuteJSONException {} ", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
}
