package com.accelevents.session_speakers.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.apigateway.MuxToS3ApiCall;
import com.accelevents.billing.chargebee.dto.ResponseDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventStreaming;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.domain.session_speakers.NetworkingLoungeVideoDetails;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSubtitle;
import com.accelevents.dto.MuxDirectUploadIdAndUrlDto;
import com.accelevents.dto.MuxJwtDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.services.MuxService;
import com.accelevents.services.S3Wrapper;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.MUXLivestreamAssetRepo;
import com.accelevents.session_speakers.repo.NetworkingLoungeVideoDetailsRepo;
import com.accelevents.session_speakers.services.MUXLivestreamAssetRepoService;
import com.accelevents.session_speakers.services.MUXLivestreamAssetService;
import com.accelevents.session_speakers.services.SessionDetailsRepoService;
import com.accelevents.session_speakers.services.SessionSubtitleService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.accelevents.exceptions.NotFoundException.SessionNotFound.DEFAULT_PLAYBACK_NOT_FOUND;
import static com.accelevents.utils.Constants.*;

@Service
public class MUXLivestreamAssetServiceImpl implements MUXLivestreamAssetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MUXLivestreamAssetServiceImpl.class);

    private final MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;

    private final MuxService muxService;

    private final SessionSubtitleService sessionSubtitleService;

    @Autowired
    private SessionDetailsRepoService sessionDetailsRepoService;

    @Autowired
    private EventRepoService eventRepoService;

    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private MuxToS3ApiCall muxToS3ApiCall;

    @Autowired
    private S3Wrapper s3Wrapper;

    @Autowired
    private MUXLivestreamAssetRepo muxLivestreamAssetRepo;

    @Autowired
    private NetworkingLoungeVideoDetailsRepo networkingLoungeVideoDetailsRepo;

    @Value("${mux.asset.glacier.multipart.upload.size}")
    private String partSize;

    private double sequence = 1000;

    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;

    @Autowired
    public MUXLivestreamAssetServiceImpl(MuxService muxService, MUXLivestreamAssetRepoService muxLivestreamAssetRepoService, SessionSubtitleService sessionSubtitleService) {
        this.muxLivestreamAssetRepoService = muxLivestreamAssetRepoService;
        this.muxService = muxService;
        this.sessionSubtitleService = sessionSubtitleService;
    }
   @Override
    public void saveWithSequence(MUXLivestreamAssetDetails assetDetails) {
//        MUXLivestreamAssetDetails lastItemCategory = getLastAssetDetail(assetDetails);
//        if (lastItemCategory != null) {
//            assetDetails.setPosition(lastItemCategory.getPosition() + sequence);
//        } else {
            assetDetails.setPosition(sequence);
//        }
        muxLivestreamAssetRepoService.save(assetDetails);
    }

    @Override
    @Transactional(rollbackOn = {Exception.class})
    public void updateWithSequence(Long id, Long topId, Long topBottomId, Event event, User user) {
        MUXLivestreamAssetDetails assetDetails;
        Optional<MUXLivestreamAssetDetails> optAssetDetails = this.findById(id);
        if (optAssetDetails.isPresent()) {
            assetDetails = optAssetDetails.get();
        } else {
            throw new NotFoundException(NotFoundException.SessionNotFound.PLAYBACK_NOT_FOUND);
        }
        MUXLivestreamAssetDetails topItem = this.findById(topId).orElse(null);
        MUXLivestreamAssetDetails topNextItem = this.findById(topBottomId).orElse(null);
        LOGGER.info("request received for change asset position | updateWithSequence | event {} | assetId {} | current Position {} | user {}", event.getEventId(), assetDetails.getAssetId(), assetDetails.getPosition(), user.getUserId());
        if (topItem != null && topNextItem != null) {
            double position = (topNextItem.getPosition() + topItem.getPosition()) / 2;
            if (position == 1) {
                MUXLivestreamAssetDetails itemNextPositionItem = getNextPositionItem(assetDetails);
                MUXLivestreamAssetDetails itemPreviousPositionItem = getPreviousPositionItem(assetDetails);
                double positionDifferent = (itemNextPositionItem.getPosition() - itemPreviousPositionItem.getPosition()) / 2;
                updatePositionInGroup(topItem.getPosition(), assetDetails.getPosition(), positionDifferent, itemNextPositionItem.getSessionId(), itemNextPositionItem.getEventId());
                assetDetails.setPosition(topNextItem.getPosition());
                muxLivestreamAssetRepoService.save(assetDetails);
            } else {
                assetDetails.setPosition(position);
                muxLivestreamAssetRepoService.save(assetDetails);
            }
        } else if (topItem == null && topNextItem != null) {
            double posDiff = topNextItem.getPosition() - sequence;
            if (posDiff <= 1) {
                muxLivestreamAssetRepo.updatePositionForAllItem(assetDetails.getSessionId(), assetDetails.getEventId(), sequence);
            }
            assetDetails.setPosition(sequence);
            muxLivestreamAssetRepoService.save(assetDetails);

        } else if (topItem != null) {
            assetDetails.setPosition(topItem.getPosition() + sequence);
            muxLivestreamAssetRepoService.save(assetDetails);
        }
        LOGGER.info("successfully changed mux assets position|updateWithSequence|event {} |assetId {}| updated Position {}| user {}", event.getEventId(), assetDetails.getAssetId(), assetDetails.getPosition(), user.getUserId());
    }

    private MUXLivestreamAssetDetails getNextPositionItem(MUXLivestreamAssetDetails assetDetails) {
        return muxLivestreamAssetRepo
                .nextPositionItem(assetDetails.getSessionId(), assetDetails.getEventId(), assetDetails.getPosition()).get(0);
    }

    private MUXLivestreamAssetDetails getPreviousPositionItem(MUXLivestreamAssetDetails assetDetails) {
        return muxLivestreamAssetRepo
                .previousPositionItem(assetDetails.getSessionId(), assetDetails.getEventId(), assetDetails.getPosition()).get(0);
    }

    private void updatePositionInGroup(double startPosition, double endPosition, double addingPositionValue,
                                       long sessionId, long eventId) {
        muxLivestreamAssetRepo.updatePositionItem(sessionId, eventId, startPosition, endPosition, addingPositionValue);
    }


    private Optional<MUXLivestreamAssetDetails> findById(Long id) {
       return muxLivestreamAssetRepo.findById(id);
    }

    public MUXLivestreamAssetDetails getLastAssetDetail(MUXLivestreamAssetDetails assetDetails) {
        return muxLivestreamAssetRepo.findFirstBySessionIdAndEventIdOrderByPositionDesc(assetDetails.getSessionId(),
                assetDetails.getEventId());
    }

    @Override
    public List <MUXLivestreamAssetDetails> findBySessionIdAndEventId(Long sessionId, Long eventId){
        return muxLivestreamAssetRepo.findBySessionIdAndEventId(sessionId, eventId);
    }


    @Override
    @Transactional(rollbackOn = {Exception.class})
    public void storeLivestreamAssetDetails(Session session, MuxAssetDTO muxAssetDTO) {
        if(sessionDetailsRepoService.getSessionRecordBySession(session.getId())) {

            LOGGER.info("StoreLivestreamAssetDetails called for session id {} with asset details {}", session.getId(), muxAssetDTO);
            Optional<MUXLivestreamAssetDetails> optionalMUXLivestreamAssetDetails =
                muxLivestreamAssetRepoService.findBySessionIdAndAssetId(session.getId(), muxAssetDTO.getAssetId());
            MUXLivestreamAssetDetails assetDetails;
            if (optionalMUXLivestreamAssetDetails.isPresent()) {
                assetDetails = optionalMUXLivestreamAssetDetails.get();
                assetDetails.setDuration(muxAssetDTO.getDuration());
                muxLivestreamAssetRepoService.save(assetDetails);
            } else {
                assetDetails = muxAssetDTO.toEntity(muxAssetDTO);
                assetDetails.setSessionId(session.getId());
                assetDetails.setEventId(session.getEventId());
                assetDetails.setAssetType(AssetType.SESSION_ASSET);
                if(null != muxAssetDTO.getAssetStatus()){
                    String assetStatus = muxAssetDTO.getAssetStatus();
                    assetDetails.setMuxAssetStatus(assetStatus.equals("preparing") ? RecordStatus.PROCESSING:RecordStatus.CREATE);
                }
                if (!StringUtils.isBlank(muxAssetDTO.getPlayBackId()) && muxAssetDTO.getSigned()) {
                    MuxJwtDto muxJwtDto = muxService.generateDomainPlayBackRestrictionToken(muxAssetDTO.getPlayBackId(), session.getEventId());
                    assetDetails.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
                    assetDetails.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());
                }
                muxLivestreamAssetRepoService.saveWithDefaultSequence(assetDetails);
            }

        }
    }

    @Override
    public void storeEventLivestreamAssetDetails(EventStreaming eventStreaming, MuxAssetDTO muxAssetDTO) {
        LOGGER.info("StoreEventLivestreamAssetDetails called for Event id {} with asset details {}", eventStreaming.getEventId(), muxAssetDTO);
        Optional<MUXLivestreamAssetDetails> optionalMUXLivestreamAssetDetails =
                muxLivestreamAssetRepoService.findByEventStreamIdAndAssetId(eventStreaming.getId(), muxAssetDTO.getAssetId());
        MUXLivestreamAssetDetails assetDetails;
        if (optionalMUXLivestreamAssetDetails.isPresent()) {
            assetDetails = optionalMUXLivestreamAssetDetails.get();
            assetDetails.setDuration(muxAssetDTO.getDuration());
        } else {
            assetDetails = muxAssetDTO.toEntity(muxAssetDTO);
            assetDetails.setEventStreamId(eventStreaming.getId());
            assetDetails.setEventId(eventStreaming.getEventId());
            assetDetails.setAssetType(AssetType.EVENT_STREAMING_ASSET);
            if (!StringUtils.isBlank(muxAssetDTO.getPlayBackId()) && muxAssetDTO.getSigned()) {
                MuxJwtDto muxJwtDto = muxService.generateDomainPlayBackRestrictionToken(muxAssetDTO.getPlayBackId(), eventStreaming.getEventId());
                assetDetails.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
                assetDetails.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());
            }
        }
        muxLivestreamAssetRepoService.save(assetDetails);
    }

    private void deletePreviousAssets(MUXLivestreamAssetDetails muxLivestreamAssetDetails) {
        if (muxLivestreamAssetDetails != null && StringUtils.isNotBlank(muxLivestreamAssetDetails.getAssetId())) {
            LOGGER.info("Removing the previous uploaded  asset =>{}", muxLivestreamAssetDetails.getAssetId());
            try {
                JSONObject assetDetails = muxService.getAssetDetails(muxLivestreamAssetDetails.getAssetId());
                if (null != assetDetails && assetDetails.has("upload_id")) {
                    ResponseDto responseDto = muxService.deleteAssetOnly(muxLivestreamAssetDetails.getAssetId());
                    if (null != responseDto && responseDto.getMessage().equalsIgnoreCase(Constants.ASSET_DELETE)) {
                        muxLivestreamAssetDetails.setMuxAssetStatus(RecordStatus.DELETE);
                        muxLivestreamAssetDetails.setRecordStatus(RecordStatus.DELETE);
                        muxLivestreamAssetRepoService.save(muxLivestreamAssetDetails);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Exception while the deletePreviousAssets assetId=>{} errorMessage=>{}", muxLivestreamAssetDetails.getAssetId(),e.getMessage());
            }
        }
    }

    @Override
    @Transactional
    public synchronized void storeDirectUploadAssetDetails(Session session, MuxAssetDTO muxAssetDTO, AssetType assetType) {
        LOGGER.info("storeDirectUploadAssetDetails called for session id {} with asset details {}", session != null ? session.getId() : null, muxAssetDTO);

        if (session != null) {
            Optional<MUXLivestreamAssetDetails> optionalOldMUXLivestreamAssetDetails = muxLivestreamAssetRepoService.findBySessionIdAndDefaultPlaybackTrue(session.getId(), assetType);
            if (optionalOldMUXLivestreamAssetDetails.isPresent()) {
                MUXLivestreamAssetDetails muxLivestreamAssetDetails = optionalOldMUXLivestreamAssetDetails.get();
                if (!muxLivestreamAssetDetails.getAssetId().equals(muxAssetDTO.getAssetId())) {
//                    LOGGER.info("Removing the previous assets with assetId=>{}", muxLivestreamAssetDetails.getAssetId());
//                    this.deletePreviousAssets(muxLivestreamAssetDetails);
                    muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBack(session.getId(), assetType);
                } else {
                    return;
                }
            }
            Optional<MUXLivestreamAssetDetails> optionalMUXLivestreamAssetDetails = muxLivestreamAssetRepoService.findBySessionIdAndAssetId(session.getId(), muxAssetDTO.getAssetId());
            if (!optionalMUXLivestreamAssetDetails.isPresent()) {
                this.createMuxLiveStreamAssetDuringDirectUpload(muxAssetDTO, assetType, session.getEventId(), session.getId(), null);
            }

        }
    }

    private void createMuxLiveStreamAssetDuringDirectUpload(MuxAssetDTO muxAssetDTO, AssetType assetType,
                                                            Long eventId, Long sessionId, Long eventStreamingId) {

        MUXLivestreamAssetDetails assetDetails = muxAssetDTO.toEntity(muxAssetDTO);
        assetDetails.setPlayBackRestrictionToken(muxAssetDTO.getPlayBackRestrictionToken());
        assetDetails.setThumbnailRestrictionToken(muxAssetDTO.getThumbnailRestrictionToken());
        assetDetails.setEventId(eventId);
        assetDetails.setDefaultPlayback(true);
        assetDetails.setAssetType(assetType);
        assetDetails.setSessionId(sessionId);
        assetDetails.setDescription(muxAssetDTO.getDescription());
        assetDetails.setTitle(muxAssetDTO.getTitle());
        assetDetails.setEventStreamId(eventStreamingId);
        if(null != muxAssetDTO.getAssetStatus()){
            String assetStatus = muxAssetDTO.getAssetStatus();
            assetDetails.setMuxAssetStatus(assetStatus.equals("preparing") ? RecordStatus.PROCESSING:RecordStatus.CREATE);
        }
        muxLivestreamAssetRepoService.saveWithDefaultSequence(assetDetails);
    }


    @Override
    @Transactional
    public synchronized void storeDirectUploadAssetDetailsForEventStream(EventStreaming eventStreaming, MuxAssetDTO muxAssetDTO) {
        LOGGER.info("storeDirectUploadAssetDetailsForEventStream called for eventStreaming id {} with asset details {}", eventStreaming != null ? eventStreaming.getId() : null, muxAssetDTO);
        if (eventStreaming != null) {
            Optional<MUXLivestreamAssetDetails> optionalOldMUXLivestreamAssetDetails = Optional.empty();
            if (StreamProvider.DIRECT_UPLOAD.equals(eventStreaming.getStreamProvider())) {
                optionalOldMUXLivestreamAssetDetails = muxLivestreamAssetRepoService.findFirstByEventStreamIdOrderByIdDesc(eventStreaming.getId());
            }
            if (optionalOldMUXLivestreamAssetDetails.isPresent()) {
                LOGGER.info("Yes it's present AssetId {}, Playback {}", optionalOldMUXLivestreamAssetDetails.get().getAssetId(), optionalOldMUXLivestreamAssetDetails.get().getPlaybackId());
                    MUXLivestreamAssetDetails muxLivestreamAssetDetails = optionalOldMUXLivestreamAssetDetails.get();
                    if (!muxLivestreamAssetDetails.getAssetId().equals(muxAssetDTO.getAssetId())) {
                        LOGGER.info("Removing the previous assets with assetId=>{}", muxLivestreamAssetDetails.getAssetId());
                        this.deletePreviousAssets(muxLivestreamAssetDetails);
                        muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBackByEventStreamId(eventStreaming.getId());
                    } else {
                        return;
                    }
            }

            if (null != muxAssetDTO) {
                Optional<MUXLivestreamAssetDetails> optionalMUXLivestreamAssetDetails = muxLivestreamAssetRepoService.findByEventStreamIdAndAssetId(eventStreaming.getId(), muxAssetDTO.getAssetId());
                if (!optionalMUXLivestreamAssetDetails.isPresent()) {
                    this.createMuxLiveStreamAssetDuringDirectUpload(muxAssetDTO, AssetType.EVENT_STREAMING_ASSET, eventStreaming.getEventId(), null, eventStreaming.getId());
                }
            }
        }
    }

    @Override
    @Transactional
    public void updateLivestreamAssetDuration(Session session, MuxAssetDTO muxAssetDTO) {
        LOGGER.info("updateLivestreamAssetDuration called for session id {} and asset id  {}", session.getId(), muxAssetDTO.getAssetId());
        muxLivestreamAssetRepoService.findBySessionIdAndAssetId(session.getId(), muxAssetDTO.getAssetId())
                .ifPresent(assetDetails -> {
                    muxService.enableMP4SupportForDownload(assetDetails.getAssetId(), Constants.MAX_RETRIES_FOR_MP4_SUPPORT);
                    muxLivestreamAssetRepoService.updateAssetDuration(assetDetails.getId(), muxAssetDTO.getDuration());
                });
    }

    @Override
    @Transactional
    public void updateLivestreamAssetVideoName(Session session, String assetId, String fileName) {
        LOGGER.info("updateLivestreamAssetVideoName called for session id {} and asset id {} filename {}", session.getId(), assetId, fileName);
        muxLivestreamAssetRepoService.findBySessionIdAndAssetId(session.getId(), assetId)
                .ifPresent(assetDetails -> muxLivestreamAssetRepoService.updateAssetFileNameForDownload(assetDetails.getId(), fileName));
    }

    @Override
    @Transactional
    public void updateEventLivestreamAssetVideoName(EventStreaming eventStreaming, String assetId, String fileName) {
        LOGGER.info("updateEventLivestreamAssetVideoName called for Event id {} and asset id {} filename {}", eventStreaming.getEventId(), assetId, fileName);
        muxLivestreamAssetRepoService.findByEventStreamIdAndAssetId(eventStreaming.getId(), assetId)
                .ifPresent(assetDetails -> muxLivestreamAssetRepoService.updateAssetFileNameForDownload(assetDetails.getId(), fileName));
    }

    @Override
    public List<MuxAssetDTO> findAllLivestreamAssetsForSessionBySessionIdAndType(long sessionId, AssetType assetType) {
        List<MuxAssetDTO> muxAssetDTOList = muxLivestreamAssetRepoService.findAllLivestreamAssetsForSessionBySessionIdAndType(sessionId, assetType);
        List<Long> muxIds = muxAssetDTOList.stream().map(MuxAssetDTO::getId).collect(Collectors.toList());
        Map<Long,List<SessionSubtitleDTO>> muxIdAndSubtitleListMap = sessionSubtitleService.getMapOfMuxIdAndSubtitleListByMuxIdListAndSessionId(muxIds, List.of(sessionId));
        muxAssetDTOList.forEach(muxAssetDTO -> muxAssetDTO.setSessionSubtitleDTOList(muxIdAndSubtitleListMap.get(muxAssetDTO.getId())));
        return muxAssetDTOList;
    }

    @Override
    public void markAssetAsDefaultPlayBackForSession(Session session, long id, AssetType assetType) {
        muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBack(session.getId(), assetType);
        if (id > 0) {
            muxLivestreamAssetRepoService.markAssetAsDefaultPlayBack(id);
        }
    }
    
    /*
    * We Remove Subtitle file url if Asset exist for given EventStreaming and set newly created asset as default playback
    * */
    @Override
    public void markAssetAsDefaultPlayBackForEventStreaming(EventStreaming eventStreaming, long id) {
        muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBackByEventStreamId(eventStreaming.getId());
        if (id > 0) {
            muxLivestreamAssetRepoService.markAssetAsDefaultPlayBack(id);
        }
    }

    @Transactional
    @Override
    public void updateStatusToDeleteBySessionId(long sessionId) {
        muxLivestreamAssetRepoService.updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);
    }

    @Override
    public MuxAssetDTO findDefaultPlayBackForSession(long sessionId, AssetType assetType) {
        MuxAssetDTO muxAssetDTO = muxLivestreamAssetRepoService.findDefaultPlayBackForSession(sessionId, assetType);
        if (null == muxAssetDTO) {
            throw new NotFoundException(DEFAULT_PLAYBACK_NOT_FOUND);
        }
        return muxAssetDTO;
    }

    @Override
    public void setMostRecentAssetAsDefaultPlayBackForSession(Session session, AssetType assetType) {
        muxLivestreamAssetRepoService.findFirstBySessionIdAndAssetTypeOrderByIdDesc(session.getId(), assetType)
                .ifPresent(details -> markAssetAsDefaultPlayBackForSession(session, details.getId(), assetType));
    }

    @Override
    public void setMostRecentAssetAsDefaultPlayBackForEventStreaming(EventStreaming eventStreaming) {
        muxLivestreamAssetRepoService.findFirstByEventStreamIdOrderByIdDesc(eventStreaming.getId())
                .ifPresent(details -> markAssetAsDefaultPlayBackForEventStreaming(eventStreaming, details.getId()));
    }

    @Override
    public List<IdDurationDto> getSessionDurationForAnalyticsByIdsIn(List<Long> sessionIds) {
        return (CollectionUtils.isEmpty(sessionIds)) ? Collections.emptyList() : muxLivestreamAssetRepoService.countBySessionIdIn(sessionIds);
    }

    @Override
    public Double getSessionVideoDurationBySessionId(Long sessionId) {
        return muxLivestreamAssetRepoService.getSessionVideoDurationBySessionId(sessionId);
    }

    @Override
    public void storeLivestreamAssetDetailsForVirtualEventSetting(Long virtualEventSettingId, String assetId, String playBackId, long eventId, MuxJwtDto muxJwtDto) {
        if (null == muxLivestreamAssetRepoService.getByEventIdAndAssetIdAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(eventId, assetId)) {
            muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBackByEventId(eventId);
            MUXLivestreamAssetDetails assetDetails = new MUXLivestreamAssetDetails();
            assetDetails.setAssetId(assetId);
            assetDetails.setCreatedAt(new Date());
            assetDetails.setPlaybackId(playBackId);
            assetDetails.setDuration(muxService.getDurationByAssetId(assetId));
            assetDetails.setFileNameForDownload("welcomeMessageVideo.mp4");
            assetDetails.setAssetType(AssetType.WELCOME_VIDEO_ASSET);
            assetDetails.setEventId(eventId);
            assetDetails.setDefaultPlayback(true);
            assetDetails.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
            assetDetails.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());
            LOGGER.info("Store livestreamAssetDetails For VirtualEventSetting {} ", assetDetails.getId());
            save(assetDetails);
            clearAPIGatewayCache.clearAPIGwVESettingsCache(assetDetails.getEventId());
        }
    }

    @Override
    public List<MuxAssetDTO> findAllLiveStreamAssetsForVirtualEventSetting(Long eventId) {
        return muxLivestreamAssetRepoService.findAllLiveStreamAssetsForVirtualEventSetting(eventId);
    }

    @Override
    public void save(MUXLivestreamAssetDetails muxLivestreamAssetDetails) {
        muxLivestreamAssetRepoService.save(muxLivestreamAssetDetails);
    }

    @Override
    public void removePastWelcomeMessageOrSessionVideo(Long muxLiveStreamAssetId) {
        MUXLivestreamAssetDetails muxLivestreamAssetDetails = getMUXLivestreamAssetDetails(muxLiveStreamAssetId);
        LOGGER.info("Past video recordStatus set DELETE for muxLivestreamAssetDetails {}", muxLivestreamAssetDetails.getId());
        muxLivestreamAssetDetails.setRecordStatus(RecordStatus.DELETE);
        save(muxLivestreamAssetDetails);
    }

    @Override
    public MUXLivestreamAssetDetails getMUXLivestreamAssetDetails(Long muxLiveStreamAssetId) {
        return muxLivestreamAssetRepoService.findById(muxLiveStreamAssetId);
    }

    @Override
    @Async
    public void storeLivestreamAssetDetailsForNetworkingLounge(String loungeId, String assetId, String playBackId, long eventId, Long userId, NetworkingLoungeVideoDetailsDto dto) {
        MuxJwtDto muxJwtDto = muxService.generateDomainPlayBackRestrictionToken(playBackId, eventId);
        if (null == muxLivestreamAssetRepoService.getByNetworkingLoungeIdAndAssetIdAndSessionIdNull(loungeId, assetId)) {

            MUXLivestreamAssetDetails assetDetails = new MUXLivestreamAssetDetails();
            assetDetails.setAssetId(assetId);
            assetDetails.setCreatedAt(new Date());
            assetDetails.setPlaybackId(playBackId);
            assetDetails.setDuration(muxService.getDurationByAssetId(assetId));
            assetDetails.setFileNameForDownload("NetworkingLoungeVideo.mp4");
            assetDetails.setEventId(eventId);
            assetDetails.setNetworkingLoungeId(loungeId);
            assetDetails.setAssetType(AssetType.NETWORKING_LOUNGE_ASSET);
            assetDetails.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
            assetDetails.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());
            assetDetails.setCreatedBy(userId);
            assetDetails = muxLivestreamAssetRepo.save(assetDetails);
            LOGGER.info("Store livestreamAssetDetails For Networking Lounge {}", assetDetails.getId());
            if(StringUtils.isNotEmpty(dto.getVideoName()) || StringUtils.isNotEmpty(dto.getDescription())) {
                NetworkingLoungeVideoDetails networkingLoungeVideoDetails = networkingLoungeVideoDetailsRepo.findByAssetId(assetDetails.getId());
                if (networkingLoungeVideoDetails == null) {
                    networkingLoungeVideoDetails = new NetworkingLoungeVideoDetails();
                    networkingLoungeVideoDetails.setAssetId(assetDetails.getId());
                }
                networkingLoungeVideoDetails.setVideoLabel(dto.getVideoName());
                networkingLoungeVideoDetails.setShortDescription(dto.getDescription());
                networkingLoungeVideoDetails.setRecStatus(RecordStatus.CREATE);
                networkingLoungeVideoDetailsRepo.save(networkingLoungeVideoDetails);
            }
        }
    }

    @Override
    public List<MuxAssetDTO> findAllByNetworkingLoungeId(String loungeId) {
        return muxLivestreamAssetRepoService.findAllByNetworkingLoungeId(loungeId);
    }

    @Override
    public void removeNetworkingLoungeVideo(Long muxLiveStreamAssetId, long userId, boolean isAdmin) {
        MUXLivestreamAssetDetails muxLivestreamAssetDetails = getMUXLivestreamAssetDetails(muxLiveStreamAssetId);
        LOGGER.info("Past video recordStatus set DELETE for muxLivestreamAssetDetails {} ", muxLivestreamAssetDetails.getId());
        Long createdUserId = muxLivestreamAssetDetails.getCreatedBy();
        if (!isAdmin && (null == createdUserId || !muxLivestreamAssetDetails.getCreatedBy().equals(userId))) {
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_VIDEO);
        }
        deletePreviousAssets(muxLivestreamAssetDetails);
        deleteNetworkingLoungeVideoDetails(muxLiveStreamAssetId);
    }

    @Override
    public List<MUXLivestreamAssetDetails> getAllLiveStreamAssetsForVirtualEventSettingByEvent(Long eventId) {
        return muxLivestreamAssetRepoService.findAllLiveStreamAssetsForVirtualEventSettingByEvent(eventId);
    }

    @Override
    public List<SessionEventLiveStream> findAllByAssetIdsIn(List<String> assetIds) {
        return muxLivestreamAssetRepoService.findAllByAssetIdsIn(assetIds);
    }
    public List<MUXLivestreamAssetDetails> findByAssetId(String assetId){
        return muxLivestreamAssetRepoService.findByAssetId(assetId);
    }

    @Override
    @Transactional
    public void updateMuxAssetStatus(List<String> assetIds, RecordStatus assetStatus) {
        if(CollectionUtils.isEmpty(assetIds)){
            return;
        }
        muxLivestreamAssetRepoService.updateMuxAssetStatus(assetIds, assetStatus);
    }

    @Override
    @Transactional
    public void updateRecordStatusToPlayBackDeleted(List<String> assetIdsToMarkPlayBackRemoved, RecordStatus recordStatus) {
        if(CollectionUtils.isEmpty(assetIdsToMarkPlayBackRemoved)){
            return;
        }
        muxLivestreamAssetRepoService.updateRecordStatusToPlayBackDeleted(assetIdsToMarkPlayBackRemoved, recordStatus);
    }

    @Override
    @Transactional
    @Async
    public void deleteMuxAssetByEventDeletedBeforeThirtyDays() {
        try {
            LOGGER.info("Batch Started  for deleteMuxAssetByEventDeletedBeforeThirtyDays startTime=>{}", System.currentTimeMillis());
            int page = 0;
            int size = 100;
            int totalPage = 0;
            do {
                Pageable pageable = PageRequest.of(page, size);
                Page<Object[]> pageData = muxLivestreamAssetRepoService.getAllMuxAssetByEventDeleteBeforeThirtyDays(pageable);
                totalPage = pageData.getTotalPages();
                List<String> playbackDeleteList = new ArrayList<>();
                List<String> assetDeleteList = new ArrayList<>();
                AtomicInteger requestCount = new AtomicInteger(0);
                pageData.getContent().forEach(assetDetails -> {
                    String assetId = String.valueOf(assetDetails[0]);
                    String playbackId = String.valueOf(assetDetails[1]);
                    AssetType assetType = AssetType.valueOf(String.valueOf(assetDetails[2]));
                    LOGGER.info("Started Mux Asset Deletion for assetId=>{}, assetType=>{}", assetId, assetType.name());
                    if(AssetType.WELCOME_VIDEO_ASSET.equals(assetType) && isWelcomeVideoNeedToDelete(assetId, playbackId)){
                        return;
                    }
                    ResponseDto responseDto = deleteMuxAssetByAssetId(playbackDeleteList, assetDeleteList, assetId);
                    LOGGER.info("Completed Mux Asset Deletion for Asset status=>{} assetId=>{} ", responseDto.getMessage(), assetId);
                    requestCount.getAndIncrement();
                    if(requestCount.get() >= 5) {
                        GeneralUtils.threadSleep(2000);
                        requestCount.set(0);
                    }
                });
                updateRecordStatusToPlayBackDeleted(assetDeleteList, RecordStatus.DELETE);
                updateRecordStatusToPlayBackDeleted(playbackDeleteList, RecordStatus.PLAYBACK_REMOVED);
                updateMuxAssetStatus(assetDeleteList, RecordStatus.DELETE);
                updateMuxAssetStatus(playbackDeleteList, RecordStatus.PLAYBACK_REMOVED);
                LOGGER.info("Mux Asset Delete by event deleted before 30 days, page=>{}, totalPage=>{}, filterAsset=>{}", page, totalPage, pageData.getNumberOfElements());
                page++;
            } while (page < totalPage);
            LOGGER.info("Batch Successfully Completed for deleteMuxAssetByEventDeletedBeforeThirtyDays, totalPage=>{}, endTime=>{}", totalPage, System.currentTimeMillis());
        } catch (Exception ex) {
            LOGGER.error("Exception | deleteMuxAssetByEventDeletedBeforeThirtyDays ", ex);
        }
    }

    private boolean isWelcomeVideoNeedToDelete(String assetId, String playbackId) {
        List<Event> activeEventList = eventRepoService.getActiveEventsByWelcomeVideo(playbackId);
        if(!activeEventList.isEmpty()) {
            boolean isUsedInWelcomeVideo = virtualEventSettingsRepoService.isWelComeVideoPlaybackIdUsed(playbackId);
            if(isUsedInWelcomeVideo) {
                LOGGER.info("deleteMuxAssetByEventDeletedBeforeThirtyDays, Welcome Video Used In other Event for assetId=>{}", assetId);
                return true;
            }
            List<Long> eventIdList = activeEventList.stream().map(Event::getEventId).collect(Collectors.toList());
            List<SessionEventLiveStream> assetUsedList = muxLivestreamAssetRepoService.findAllByAssetIdsIn(Arrays.asList(assetId));
            List<Long> assetLiveEventList  = assetUsedList.stream().map(SessionEventLiveStream::getEventId).collect(Collectors.toList());
            if(!Collections.disjoint(eventIdList, assetLiveEventList)) {
                LOGGER.info("deleteMuxAssetByEventDeletedBeforeThirtyDays, Welcome Video Used In other Event for assetId=>{}", assetId);
                return true;
            }
        }
        return false;
    }

    private ResponseDto deleteMuxAssetByAssetId(List<String> playbackDeleteList, List<String> assetDeleteList, String assetId) {
        ResponseDto responseDto = muxService.deleteAssetWithoutAsync(assetId);
        if (ProcessingStatus.SUCCESS.equals(responseDto.getProcessingStatus()) || ASSET_NOT_FOUND.equals(responseDto.getMessage())) {
            if (ASSET_PLAYBACK_DELETE.equals(responseDto.getMessage())) {
                playbackDeleteList.add(assetId);
            } else {
                assetDeleteList.add(assetId);
            }
        }
        return responseDto;
    }

    @Override
    @Transactional
    public void updateMasterAccessRecordForAsset(String assetId, String type) {
        final S3KeyRecordStatus masterAssetStatus = S3KeyRecordStatus.getMasterAssetStatus(type);
        List<MUXLivestreamAssetDetails> muxLivestreamAssetDetailsList = muxLivestreamAssetRepoService.findByAssetId(assetId);
        for (MUXLivestreamAssetDetails muxLivestreamAssetDetails: muxLivestreamAssetDetailsList) {
            muxLivestreamAssetDetails.setMasterAssetStatus(masterAssetStatus);
            muxLivestreamAssetRepoService.save(muxLivestreamAssetDetails);
            muxToS3ApiCall.callMuxToS3UploadApi(assetId, masterAssetStatus);
        }
    }

    @Override
    public boolean uploadS3FileInMux(MUXLivestreamAssetDetails muxLivestreamAssetDetails, Event event)  {

        String s3AssetKey = muxLivestreamAssetDetails.getS3AssetKey();
        try {
            ObjectMetadata objectMetadata = s3Wrapper.getObjectMetaDataByKey(s3AssetKey);
            Long objectSize = objectMetadata.getContentLength();

            MuxDirectUploadIdAndUrlDto muxDirectUploadIdAndUrlDto = muxService.createDirectUploadUrl(false, EMPTY_JSON_BODY);

            long downloadChunkSize = Long.parseLong(partSize);
            long startRange = 0l;
            long endRange = (downloadChunkSize >= objectSize) ? objectSize : downloadChunkSize - 1;
            do {
                byte[] bytes = s3Wrapper.getObjectInChunks(s3AssetKey, startRange, endRange);
                uploadS3ChunkInMux(s3AssetKey, muxDirectUploadIdAndUrlDto.getDirectUploadUrl(), objectSize, bytes, startRange);
                startRange = startRange + Long.valueOf(bytes.length);
                endRange = ((endRange + downloadChunkSize) > objectSize) ? objectSize : (endRange + downloadChunkSize);

            } while (endRange <= objectSize && startRange < objectSize);

            String assetIdByUploadId = muxService.getAssetIdByDirectUploadId(muxDirectUploadIdAndUrlDto.getDirectUploadId());
            LOGGER.info("Welcome message video assetId {}", assetIdByUploadId);
            String playBackId = muxService.getPlayBackIdByAssetId(assetIdByUploadId);
            LOGGER.info("Welcome message video playbackId {}", playBackId);
            MuxJwtDto muxJwtDto = muxService.generateDomainPlayBackRestrictionToken(playBackId, event);
            muxLivestreamAssetDetails.setAssetId(assetIdByUploadId);
            muxLivestreamAssetDetails.setPlaybackId(playBackId);
            muxLivestreamAssetDetails.setMuxAssetStatus(null);
            muxLivestreamAssetDetails.setRecordStatus(RecordStatus.CREATE);
            muxLivestreamAssetDetails.setMasterAssetStatus(null);
            muxLivestreamAssetDetails.setS3AssetStatus(null);
            muxLivestreamAssetDetails.setS3AssetKey(null);
            muxLivestreamAssetDetails.setS3KeyCreatedAt(null);
            muxLivestreamAssetDetails.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
            muxLivestreamAssetDetails.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());
            LOGGER.info("S3 mux asset uploaded into mux s3AssetKey {}", s3AssetKey );
            return Boolean.TRUE;

        }catch (Exception e) {
            LOGGER.error("Error while upload s3 mux asset into mux s3AssetKey {}", s3AssetKey, e);
        }
        return Boolean.FALSE;
    }

    @Override
    public List<MUXLivestreamAssetDetails> findByEventIdAndPlayBackId(Long eventId, String playBackId) {
        return muxLivestreamAssetRepoService.findByEventIdAndPlayBackId(eventId, playBackId);
    }

    private void uploadS3ChunkInMux(String key, String uploadUrl, Long totalSize,byte[] chunkByte, long startPosition) throws UnirestException {
        long archiveSizeInByte = totalSize;
        long endPosition = startPosition + chunkByte.length;
        String contentRange = new StringBuilder("bytes ").append(startPosition).append(Constants.STRING_DASH).append(endPosition-1)
                .append(Constants.STRING_SLASH).append(archiveSizeInByte).toString();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Range", contentRange);
        HttpResponse<String> response = Unirest.put(uploadUrl)
                .headers(header)
                .body(chunkByte)
                .asString();
        if (GeneralUtils.is2xxSuccess(response.getStatus()) || response.getStatus() == 308) {
            LOGGER.info("S3 asset chunk uploading in mux, s3AssetKey=>{}, sessionId=>{}, chunkSize=>{}", key, totalSize, chunkByte.length);
        } else {
            LOGGER.info("S3 asset chunk uploading failed in mux, s3AssetKey=>{}, sessionId=>{}, chunkSize=>{}", key, totalSize, chunkByte.length);
        }
    }

    @Override
    public List<NetworkingLoungeVideoDto> findAllLoungeVideoByNetworkingLoungeId(String loungeId) {
        return muxLivestreamAssetRepoService.findAllLoungeVideoByNetworkingLoungeId(loungeId);
    }

    @Override
    public void updateMuxAssetsVisibility(Long id, boolean isVisible, Event event, User user) {
        MUXLivestreamAssetDetails assetDetails = muxLivestreamAssetRepoService.findById(id);
        assetDetails.setVisible(isVisible);
        muxLivestreamAssetRepoService.save(assetDetails);
        LOGGER.info("Mux Asset Visibility updated for id=>{}, isVisible=>{}, updatedBy=> {}", id, isVisible, user.getUserId());
    }

    @Override
    public void deleteMuxAssets(Long id, Event event, User user) {
        MUXLivestreamAssetDetails assetDetails = muxLivestreamAssetRepoService.findById(id);
        assetDetails.setRecordStatus(RecordStatus.DELETE);
        muxLivestreamAssetRepoService.save(assetDetails);
        List<SessionSubtitle> sessionSubtitleList = sessionSubtitleService.findBySessionIdAndMuxId(assetDetails.getSessionId(),assetDetails.getId());
        sessionSubtitleList.forEach(sessionSubtitle -> sessionSubtitle.setRecordStatus(RecordStatus.DELETE));
        sessionSubtitleService.saveAll(sessionSubtitleList);
        LOGGER.info("Mux Asset deleted for id=>{}, status=>{}, updatedBy=> {}", id, RecordStatus.DELETE, user.getUserId());
    }

    @Override
    public void updateMuxAssets(MuxAssetDTO muxAssetDTO, Event event, User user) {
        Optional<MUXLivestreamAssetDetails> assetDetailsOpt = muxLivestreamAssetRepoService.findBySessionIdAndAssetId(muxAssetDTO.getSessionId(), muxAssetDTO.getAssetId());
        if (assetDetailsOpt.isPresent()) {
            MUXLivestreamAssetDetails assetDetails = assetDetailsOpt.get();
            assetDetails.setTitle(muxAssetDTO.getTitle());
            assetDetails.setDescription(muxAssetDTO.getDescription());
            muxLivestreamAssetRepoService.save(assetDetails);
            LOGGER.info("Mux Asset updated for assetId=>{}, sessionId=>{}, updatedBy=> {}", assetDetails.getAssetId(), assetDetails.getSessionId(), user.getUserId());
        }
    }

    @Override
    public List<MUXLivestreamAssetDetails> getVisibleAssetsBySessionIdAndEventId(Long sessionId, Long eventId) {
        return muxLivestreamAssetRepo.getVisibleAssetsBySessionIdAndEventId(sessionId, eventId);
    }

    private void deleteNetworkingLoungeVideoDetails(Long muxLiveStreamAssetId) {
        NetworkingLoungeVideoDetails networkingLoungeVideoDetails = networkingLoungeVideoDetailsRepo.findByAssetId(muxLiveStreamAssetId);
        if(networkingLoungeVideoDetails!=null) {
            networkingLoungeVideoDetails.setRecStatus(RecordStatus.DELETE);
            networkingLoungeVideoDetailsRepo.save(networkingLoungeVideoDetails);
        }
    }
}
