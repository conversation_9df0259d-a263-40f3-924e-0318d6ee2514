package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.SessionSubtitle;
import com.accelevents.session_speakers.dto.SessionSubtitleDTO;
import com.accelevents.session_speakers.repo.SessionSubtitleRepo;
import com.accelevents.session_speakers.services.SessionSubtitleRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class SessionSubtitleRepoServiceImpl implements SessionSubtitleRepoService {

    @Autowired
    SessionSubtitleRepo sessionSubtitleRepo;

    @Override
    public void save(SessionSubtitle sessionSubtitle){
        sessionSubtitleRepo.save(sessionSubtitle);
    }

    @Override
    public List<SessionSubtitle> findBySessionIdAndMuxId(Long sessionId, Long muxId){
        return sessionSubtitleRepo.findBySessionIdAndMuxId(sessionId, muxId);
    }

    @Override
    public Iterable<SessionSubtitle> saveAll(Iterable<SessionSubtitle> sessionSubtitleList){
        return sessionSubtitleRepo.saveAll(sessionSubtitleList);
    }

    @Override
    public Optional<SessionSubtitle> findById(Long subtitleId){
        return sessionSubtitleRepo.findById(subtitleId);
    }

    @Override
    public void deleteById(Long subtitleId){
        sessionSubtitleRepo.deleteById(subtitleId);
    }

    @Override
    public List<SessionSubtitleDTO> getSubtitleListByMuxIdAndSessionId(Long sessionId, Long muxId ){
        return sessionSubtitleRepo.getSubtitleListByMuxIdAndSessionId(sessionId, muxId);
    }

    @Override
    public List<SessionSubtitleDTO> getSubtitleListByMuxIdListAndSessionId(List<Long> muxIds, List<Long> sessionIds){
        return sessionSubtitleRepo.getSubtitleListByMuxIdListAndSessionId(muxIds, sessionIds);
    }

    @Override
    public boolean languageCodeExistByMuxIdAndSessionId(Long muxId, Long sessionId, String languageCode, Long subtitleId) {
        return sessionSubtitleRepo.languageCodeExistByMuxIdAndSessionId(muxId, sessionId, languageCode, subtitleId);
    }

}
