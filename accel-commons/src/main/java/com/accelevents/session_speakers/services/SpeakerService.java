package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.UploadSpeakerDto;
import com.accelevents.common.dto.UploadedSpeakerResponseContainer;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.domain.session_speakers.CustomFormAttributeData;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.ResponseDto;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.dto.cache.SpeakerCacheBasicDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface SpeakerService {

    Speaker save(Speaker speaker);

    void saveAll(List<Speaker> speaker);

    Speaker getSpeakerById(Long id, Event event);

    List<Speaker> getSpeakerByIds(List<Long> ids, Event event);

    SpeakerDTO getSpeakerByDto(Long id, Event event, String expand, User loggedInUser, boolean isAdminOrStaff);

    List<Speaker> getSpeakerByEventId(Event event);

    List<Speaker> getSpeakerByEventIdAndEmail(Event event, String email);

    Long createSpeaker(SpeakerDTO speakerDTO, Event event);

    ResponseDto updateOnBoardingSpeaker(Long id, SpeakerDTO speakerDTO, Event event, User user);

    ResponseDto updateSpeaker(Long id, SpeakerDTO speakerDTO, Event event, User user, String languageCode, Map<String, String> languageMap);

    DataTableResponse getSpeakerList(Event event, String search, String expand, Pageable pageable, boolean isAdminOrStaff);

    List<SpeakerBasicDTO> getSpeakersBasicInfo(Event event);

    void deleteSpeakerAlongWithSessionSpeaker(Long speakerId, Event event);

    void deleteSpeakersAlongWithSessionSpeaker(DeleteSpeakersDTO deleteSpeakersDTO, Event event);

	Long getSpeakerIdForTheUserInEvent(Event event, User user);

	boolean isSpeakerInEvent(Event event, User user);

    long getUserId(long eventId);

    List<Speaker> getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(long from, long to);

    UploadedSpeakerResponseContainer parseSpeakerCSV(MultipartFile multiPartFile, Event event, User user, Map<String,String> languageMap) throws IOException;

    UploadedSpeakerResponseContainer uploadSpeakerCSVOrZapier(UploadSpeakerDto[] speakerDTOS, Event event, User user, Map<String,String> languageMap);

    User getOrCreateUserFromUploadSpeakerDto(UploadSpeakerDto speakerDTO, Event event);

    void updateSpeakerSequence(Long speakerId, Long topSpeakerId, Long topBottomSpeakerId, Event event, User user);

    List<Speaker> findSpeakerByEventId(Event event);

    Speaker setPositionForSpeaker(Speaker speaker);

    List<Speaker> getSpeakerBySamePosition(double position, Event event);

    List<Speaker> findSpeakerByEmail(String email);

    List<Long> findSpeakerUserIdsByEventId(Event event);

    FirstLastNameDto getUserInfoByEmail(String email);

    DataTableResponse updateSpeakerSequenceSortByName(Event event, String columnName, boolean isAscending, int page, int size);

    Long getEventSpeakerCount(Long eventId);

    Long findSpeakerIdByEventIdAndUserId(Event event, User user);

    List<SpeakerTicketTypeDTO> assignAttendeeAccessToSpeaker(Event event, List<Long> selectedTicketTypes, Long speakerId, User staffUser, Boolean isAllowAttendeeAccess);

    void removeSpeakerProfilePicture(Long speakerId,Event event);

    Boolean checkOverrideProfileEnableForSpeaker(long eventId, Long userId);

    List<Speaker> getSpeakerByEventIDAndUserId(Long eventId, Long userId);

    void updateSpeakerEmailAndSendEmailToEventAdmin(String oldEmail,String newEmail);

    void completeSessionOnBoarding(Long speakerId, Event event);

    List<SpeakerCountDTO> speakerCountBySpeakerOnBoardingStatus(Event event);

    SpeakerWidgetSettingsDTO getSpeakerWidgetSettings(Event event);

    void updateSpeakerWidgetSettings(Event event, SpeakerWidgetSettingsDTO speakerWidgetSettingsDTO);

    void updateSpeakerHighlightedFlag(Event event, Map<String, List<Long>> speakerIds);

    List<SpeakerCacheBasicDTO> getSpeakerList(Event event, String searchString);

    List<SpeakerDTO> getSpeakerListWithSessionDetails(Event event, User user);

    DataTableResponse getAllSpeakersListByEvent(Event event);
}
