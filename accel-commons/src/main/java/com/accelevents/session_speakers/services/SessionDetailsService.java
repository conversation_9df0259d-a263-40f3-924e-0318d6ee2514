package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.ChimeConfigDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.StreamProvider;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.session_speakers.dto.CaptionsDto;
import com.accelevents.session_speakers.dto.IdDurationDto;
import com.accelevents.session_speakers.dto.SessionDTO;
import com.accelevents.session_speakers.dto.SessionDetailsDto;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface SessionDetailsService {

    SessionDetails save(Session session, SessionDetailsDto sessionDetailsDto,boolean isCsvSessionUpload);
    
    SessionDetails save(Session session, SessionDetailsDto sessionDetailsDto);

    Optional<SessionDetails> getSessionDetailsBySession(Session session);

    SessionDetails getSessionDetailsBySession1(Session session);

    String findVonageSessionIdBySessionId(Long sessionId);

	List<IdDurationDto> getSessionDurationBySessionIds(List<Long> sessionIds);

	Double getSessionVideoDurationBySessionId(Long sessionId);

    double getSessionVideoDurationBySession(Session session);

    void saveSessionDetails(Session session, StreamProvider streamProvider, String streamUrl, SessionDTO sessionDTO);

    void deleteBySessionId(Long sessionId);

    public void save(SessionDetails sessionDetails);

    String getSubTitleFileUrlFromSessionDetailsBySession(Session session);

    void updateChimeConfigDetails(Session session, Event event, User user, ChimeConfigDto chimeConfigDto);

    ChimeConfigDto getChimeConfigDetails(Session session);

    Map<Long, Double> getSessionVideoDurationsByEvent(Event event);

    Map<Long, Double> getSessionVideoDurationsBySessionIds(Event event, List<Long> sessionIds);

    Integer getAllowedMinutesToJoinLate(Session session);

    List<SessionDetails> getAllSessionDetailsByEventId(Long eventId);

    List<SessionDetails> getAllSessionDetailsByEventIdWithoutCache(Long eventId);

    void updateSessionDetailOnMuxStreamKeyCreation(Session session, CaptionsDto captions);
}
