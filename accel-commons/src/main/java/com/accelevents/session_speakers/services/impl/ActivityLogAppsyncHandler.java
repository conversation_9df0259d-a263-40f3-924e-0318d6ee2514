package com.accelevents.session_speakers.services.impl;

import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.graphql.GraphQL;
import com.accelevents.utils.Constants;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.core.task.SimpleAsyncTaskExecutor;

public class ActivityLogAppsyncHandler extends GraphQL {

	public ActivityLogAppsyncHandler(GraphQLConfiguration graphQLConfiguration, String authToken){
		super(graphQLConfiguration.getAppsyncActivityLogApiUrl(), authToken);
	}

	public boolean createActivityLog(Long eventId,
                                     String type,
                                     Long actualTypeId,
                                     String message,
                                     String otherData,
                                     String createdBy,
                                     Long createdById) {

        try {
            JSONObject variable = new JSONObject();
            variable.put("createSessionActivityLogsInput", getMainRequestData(eventId, type, actualTypeId, message, otherData, createdBy, createdById));

            String operationName = "createSessionActivityLogs";
            JSONObject mainRequest = new JSONObject();
            mainRequest.put(Constants.OPERATION_NAME,operationName);
            mainRequest.put(Constants.VARIABLES,variable);
            mainRequest.put(Constants.QUERY,"mutation createSessionActivityLogs($createSessionActivityLogsInput: CreateSessionActivityLogsInput!) { createSessionActivityLogs(input: $createSessionActivityLogsInput) { eventId sessionId} }");
            JSONObject jsonObject = execute(mainRequest, operationName);
        } catch (UnirestException | JSONException e) {
            e.printStackTrace();
        }

        return true;
    }

    private JSONObject getMainRequestData(Long eventId, String type, Long actualTypeId, String message, String otherData, String createdBy, Long createdById) {
        JSONObject requestData = new JSONObject();
        try {
            requestData.put("sessionId", actualTypeId);
            requestData.put("eventId", eventId);
            requestData.put("message", message);
            requestData.put("otherData", otherData);
            requestData.put("createdByName", createdBy);
            requestData.put("createdById", createdById);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return requestData;
    }

    public void createActivityLog(Session session, String message,String otherData, User user) {
        createActivityLog(session.getId(),"SESSION",session.getEventId(),message,otherData,user);
    }

    public void createActivityLog(Long sessionId, String type,Long eventId, String message,String otherData, User user) {
        new SimpleAsyncTaskExecutor().execute(() -> {
            createActivityLog(eventId, type, sessionId, message, otherData, getCreatedBy(user), user.getUserId());
        });
    }

    public void createActivityLog(Session session, String message, User user) {
        createActivityLog(session, message,null,user);
	}

    private String getCreatedBy(User user) {
        return user.getFirstName()+ " "+ user.getLastName();
    }
}
