package com.accelevents.session_speakers.services;

import com.accelevents.auction.dto.ExhibitorExpoAnalyticsDto;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface VirtualEventLoggingService {
	List<Object[]> findAllByExhibitorId(Long exhibitorId);

    long getExhibitorBoothViewed(long eventId);

    Long getUserTotalExhibitorVisit(Long eventId, Long userId);

    Long getUserTotalDocumentDownload(Long eventId, Long userId);

    List<Object[]> findAllByEventId(long eventId);

    void deleteVirtualLoggingByEventId (long eventId);
}
