package com.accelevents.session_speakers.services.impl;

import com.accelevents.dto.FromToDto;
import com.accelevents.dto.OwnAvailabilityRuleDto;
import com.accelevents.domain.session_speakers.OwnerAvailabilityRule;
import com.accelevents.session_speakers.repo.OwnerAvailabilityRuleRepo;
import com.accelevents.session_speakers.services.OwnerAvailabilityRuleService;
import com.accelevents.utils.Constants;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OwnerAvailabilityRuleServiceImpl implements OwnerAvailabilityRuleService {
    private static final Logger log = LoggerFactory.getLogger(OwnerAvailabilityRuleServiceImpl.class);

    @Autowired
    private OwnerAvailabilityRuleRepo repo;


    @Override
    public OwnerAvailabilityRule getOrCreateOwnerAvailabilityRuleByEventIdAndUserId(Long eventId, Long userId) {
        log.info("Get ownerAvailabilityRule of Attendee => {} for event => {}", userId, eventId);
        OwnerAvailabilityRule ownerAvailabilityRule = repo.findByEventIdAndUserId(eventId, userId);
        log.info("OwnerAvailabilityRule is present by Attendee {} for event {}, OwnerAvailabilityRule{} ",userId,eventId,ownerAvailabilityRule != null ? ownerAvailabilityRule.getId() : "not found");
        if (null == ownerAvailabilityRule){
            ownerAvailabilityRule = new OwnerAvailabilityRule();
            ownerAvailabilityRule.setEventId(eventId);
            ownerAvailabilityRule.setUserId(userId);
            ownerAvailabilityRule.setMeetingRules(Constants.DEFAULT_SLOT);
            ownerAvailabilityRule =  save(ownerAvailabilityRule);
        }
        log.info("Get or create OwnerAvailabilityRule id {} ",ownerAvailabilityRule.getId());
        return ownerAvailabilityRule;
    }

    @Override
    public OwnerAvailabilityRule save(OwnerAvailabilityRule ownerAvailabilityRule) {
        try {
            log.info("Save ownerAvailabilityRule => {} ", ownerAvailabilityRule);
           return repo.save(ownerAvailabilityRule);
        }catch (DataIntegrityViolationException e){
            log.info("Duplicate OwnerAvailabilityRule entry found for user => {} | event => {} | Exception Message => {}", ownerAvailabilityRule.getUserId(), ownerAvailabilityRule.getEventId(), e.getLocalizedMessage());
            return repo.findByEventIdAndUserId(ownerAvailabilityRule.getEventId(), ownerAvailabilityRule.getUserId());
        }
    }

    @Override
    public OwnerAvailabilityRule updateAttendeeMeetingRulesAndDuration(Long availabilityDuration, List<OwnAvailabilityRuleDto> meetingRules, Long eventId, Long userId) {
        log.info("Update attendee meeting rule of Attendee => {} for event => {}, meeting rules => {} and availabilityDuration => {} ", userId, eventId, meetingRules, availabilityDuration);
        OwnerAvailabilityRule ownerAvailabilityRule = getOrCreateOwnerAvailabilityRuleByEventIdAndUserId(eventId, userId);
        ownerAvailabilityRule.setAvailabilityDuration(null != availabilityDuration ? availabilityDuration : 20L);
        meetingRules = getMeetingRulesWithoutError(meetingRules);//NOSONAR
        ownerAvailabilityRule.setMeetingRules(getRulesInJson(meetingRules));
        save(ownerAvailabilityRule);
        return ownerAvailabilityRule;
    }

    private List<OwnAvailabilityRuleDto> getMeetingRulesWithoutError(List<OwnAvailabilityRuleDto> meetingRules) {
        if (CollectionUtils.isEmpty(meetingRules)) {
            return meetingRules;
        }
        List<FromToDto> fromToDtosWithError = meetingRules.stream().filter(m -> null != m.getIntervals())
            .flatMap(e -> e.getIntervals().stream()).filter(i-> null != i.getError())
            .collect(Collectors.toList());

        log.info("Attendee meeting rule intervals have issue {} ",fromToDtosWithError);
        if (CollectionUtils.isEmpty(fromToDtosWithError)) {
            return meetingRules;
        }

        meetingRules.stream().forEach(rule->{
            List<FromToDto> fromToDtoList = rule.getIntervals();
            if (!CollectionUtils.isEmpty(fromToDtoList)){
                fromToDtoList.removeAll(fromToDtosWithError);
                rule.setIntervals(fromToDtoList);
            }
        });

        return meetingRules;
    }

    private String getRulesInJson(List<OwnAvailabilityRuleDto> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return null;
        }
        Gson gson = new Gson();
        return gson.toJson(rules);
    }

    @Override
    public void updateOwnerAvailabilityRule() {
        List<OwnerAvailabilityRule> ownerAvailabilityRuleList = repo.findByMeetingRulesNotNull();
        if (!CollectionUtils.isEmpty(ownerAvailabilityRuleList)){
            List<String> days = new ArrayList<>();
            List<OwnAvailabilityRuleDto> ownAvailabilityRuleDtos = new ArrayList<>();
            ownerAvailabilityRuleList.forEach(e -> {
                days.clear();
                ownAvailabilityRuleDtos.clear();
                days.addAll(Arrays.asList("sunday","monday","tuesday","wednesday","thursday","friday","saturday"));
                int daysLength = days.size();
                ownAvailabilityRuleDtos.addAll(getRules(e.getMeetingRules()));
                List<String> wdays  = ownAvailabilityRuleDtos.stream().filter(d -> d.getType().equalsIgnoreCase("wday")).map(d->d.getWday()).distinct().collect(Collectors.toList());
                days.removeAll(wdays);
                if (daysLength == days.size()){
                    ownAvailabilityRuleDtos.addAll(getRules(Constants.DEFAULT_SLOT));
                }else {
                    List<OwnAvailabilityRuleDto> missingOwnAvailabilityRuleDtos = createRemainingRules(days);
                    ownAvailabilityRuleDtos.addAll(missingOwnAvailabilityRuleDtos);
                }
                e.setMeetingRules(getRulesInJson(ownAvailabilityRuleDtos));
            });

            repo.saveAll(ownerAvailabilityRuleList);
        }
    }

    private List<OwnAvailabilityRuleDto> createRemainingRules(List<String> days) {
        List<OwnAvailabilityRuleDto> ruleDtos = new ArrayList<>();
        List<FromToDto> intervals = Collections.singletonList(new FromToDto(Constants.DEFAULT_START_TIME, Constants.DEFAULT_END_TIME));
        days.forEach(day -> {
            OwnAvailabilityRuleDto ownAvailabilityRuleDto = new OwnAvailabilityRuleDto();
            ownAvailabilityRuleDto.setIntervals(intervals);
            ownAvailabilityRuleDto.setType("wday");
            ownAvailabilityRuleDto.setWday(day);
            ownAvailabilityRuleDto.setDate(Constants.STRING_EMPTY);
            ruleDtos.add(ownAvailabilityRuleDto);
        });
        return ruleDtos;
    }

    private List<OwnAvailabilityRuleDto> getRules(String jsonInString) {
        ObjectMapper mapper = new ObjectMapper();
        List<OwnAvailabilityRuleDto> ownAvailabilityRules = new ArrayList<>();
        if (StringUtils.isNotBlank(jsonInString)) {
            try {
                ownAvailabilityRules = Arrays.asList(mapper.readValue(jsonInString, OwnAvailabilityRuleDto[].class));
            } catch (IOException e) {
                log.error("Rules|getListOfOwnAvailabilityRule|Exception {}",e.getMessage());
            }
        }
        return ownAvailabilityRules;
    }

    @Override
    public List<OwnerAvailabilityRule> findByOwnerAvailabilityRuleList(Long eventId, List<Long> attendeeIds) {
        return repo.findByOwnerAvailabilityRuleList(eventId, attendeeIds);
    }
}
