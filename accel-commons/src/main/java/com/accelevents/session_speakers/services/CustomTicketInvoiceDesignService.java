package com.accelevents.session_speakers.services;

import java.util.List;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.TemplateType;
import com.accelevents.dto.*;

public interface CustomTicketInvoiceDesignService {

    ResponseDetailDto createCustomDesign(Event event, Long userId, CustomTemplatesDto customTemplatesDto,
                                         boolean isDefault, TemplateType templateType);

	DataTableResponse getAllCustomDesigns(Event event,TemplateType templateType,PageSizeSearchObj pageSizeSearchObj);

	ResponseDto updateCustomDesign(Event event, CustomTemplatesDto customTemplatesDto, Long customDesignId,
			User user,TemplateType templateType,boolean defaultDesign);

	CustomTemplatesDto getCustomDesign(Event event, Long customDesignId,TemplateType templateType);

	ResponseDto deleteCustomDesign(Event event, Long customDesignId, User user,TemplateType templateType,Long assignToCustomDesignId);

	ResponseDto resetDefaultCustomDesign(Event event, User user, Long customDesignId,TemplateType templateType);

    ResponseDetailDto duplicateCustomDesign(Event event, Long customDesignId, String pageName,
			List<Long> allowedTicketTypes, User user, String pageUrl,TemplateType templateType);

	List<CustomTemplateDetailsDto> getCustomDesignList(Event event,TemplateType templateType);

	String getDuplicateCustomDesignByCustomDesignId(Event event, Long pageId, User user, TemplateType templateType);

	ResponseDto updateUnpublishedHtml(Event event, Long customDesignId, BeeFreePageDto beeFreePageDto,TemplateType templateType);

	CustomTemplatesDto getCustomDesignPreviewById(Event event, Long customDesignId,TemplateType templateType);

    ResponseDto toggleActiveStatus(Event event,TemplateType templateType,Long customDesignId);
}
