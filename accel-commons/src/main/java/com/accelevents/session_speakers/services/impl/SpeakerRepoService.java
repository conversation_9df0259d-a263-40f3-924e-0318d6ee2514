package com.accelevents.session_speakers.services.impl;

import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.session_speakers.dto.SpeakerBasicDTO;
import com.accelevents.session_speakers.dto.SpeakerCountDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.dto.SpeakerEmailIdDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface SpeakerRepoService {

	Speaker save(Speaker speaker);
    Speaker saveWithoutAPIGWClearCache(Speaker speaker);

	Optional<Speaker> findById(Long speakerId);

	Iterable<Speaker> saveAll(Iterable<Speaker> speakers);

	Speaker getSpeakerByIdOrThrowNotFound(Long id, Event event);

	Optional<Speaker> findSpeakerByIdAndEventOptional(Event event, Long speakerId);

	void deleteSpeaker(Speaker speaker, Event event);

    void deleteSpeakers(List<Speaker> speaker, Event event);

    Optional<Speaker> findByEventIdAndId(Long eventId, Long id);

    List<Speaker> findByEventIdAndIds(Long eventId, List<Long> ids);

    List<Speaker> findSpeakerByEventId(Long eventId);

	Page<Speaker> findAllSpeakersByEventId(Long eventId, Pageable pageable);

	boolean checkSpeakerAlreadyExistByEmail(String email,Long eventId);

    List<Speaker> findSpeakerByEmailAndEvent(String email, Long eventId);

    List<Speaker> findSpeakersByEmailsAndEvent(List<String> emails, Long eventId);

    Page<Speaker> getAllSpeakerByEvent(String searchString, Long eventId, Pageable pageable);

	Optional<Long> findIdByEventIdAndUserId(Long eventId, Long userId);

	boolean findByIdEventIdAndUserId(Long eventId,Long userId);

	List<Speaker> getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(long from, long to);

	List<Long> findSpeakerUserIdsByEventId(Long eventId);

    Integer findSpeakersCountByEventId(Long eventId);

	void updatePositionSpeaker(Long eventId, Double startPosition, Double endPosition, Double updateCount);

	void updatePositionForAllSpeakerByEventId(Double updateCount, Long eventId);

	List<Speaker> nextPositionSpeaker(Long speakerId, Long eventId, Double currentPosition);

	List<Speaker> previousPositionSpeaker(Long speakerId, Long eventId, Double currentPosition);

	List<Speaker> getSpeakerBySamePosition(Double position, Long eventId);

	Speaker findFirstByEventIdOrderByPositionDesc(Long eventId);

	Long getUserId(Long eventId);

	List<Speaker> findByEmail(String email);

    Speaker findByEventIdAndUserId(long eventId, long userId);

    List<Speaker> getAllSpeakerIdByUserId(Long userId);

    void deleteAllBySpeakerIds(List<Long> speakerIds);

    Long getEventSpeakerCount(Long eventId);

    List<String> findEmailByEventId(long eventId);

    List<SpeakerEmailIdDTO> findEmailAndIdByEventId(long eventId);

	Optional<Long> findSpeakerIdByEventIdAndUserId(long eventId, Long userId);

    List<Speaker> findSpeakerByEventIdAndUserId(long eventId,long userId);

    List<SpeakerDTO> getAllSpeakerEventIdAndUserId();

    List<SpeakerCountDTO> speakerCountBySpeakerOnBoardingStatus(Event event);

    void updateSpeakerHighlightedFlag(Event event, List<Long> speakerIds, boolean isHighlighted);

    Map<Integer, Integer> speakerCountByEventId(List<Long> eventIdList);

    List<SpeakerBasicDTO> getAllSpeakersByEventId(Long eventId);

    List<EventTaskEmailDto> findSpeakerEmailAndNameByEventIdAndSpeakerIds(Long eventId, List<Long> speakerIds);

    long getSpeakerCountByEventId(Long eventId);

    List<String> findAllEmailsByEventIdAndEmails(Long eventId, List<String> emailsForCreateRows);

    List<Speaker> getSpeakerWithUserAndAttributeDataByEventIdAndIds(Long eventId, List<Long> ids);
}
