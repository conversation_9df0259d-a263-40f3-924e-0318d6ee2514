package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.EventTaskEmailDto;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumSessionStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.dto.SessionSpeakerIdDto;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface SessionSpeakerRepoService {

    SessionSpeaker save(SessionSpeaker sessionSpeaker);

    Iterable<SessionSpeaker> saveAll(Iterable<SessionSpeaker> sessionSpeakers);

    List<Long> findAllSessionIdBySpeakerUserId(Long userId, Long eventId);

    void deleteBySessionId(Long sessionId);

    void deleteBySessionIds(List<Long> sessionId);

    Optional<SessionSpeaker> findBySessionIdAndSpeakerId(Long sessionId, Long speakerId);

    boolean sessionSpeakerAlreadyAddedInThisSession(Long sessionId, Long eventId);

    List<SpeakerDTO> getSpeakersBySession(Long sessionId);

    Page<Session> findSessionBySpeakerUserIdFuture(Long eventId, Long userId, String search, Date currentDate, Pageable pageable, List<EnumSessionFormat> enumSessionFormats, EnumSessionStatus status);

    void deleteBySpeakerId(Long speakerId);

    void deleteBySpeakerIds(List<Long> speakerId);
    List<SessionSpeaker> findBySpeakerIdAndEventId(Long speakerId, Long eventId);

    List<SessionSpeaker> findBySessionIdsIn(List<Long> sessionIds);

    void updateStatusToDeleteBySessionId(Long sessionId, RecordStatus status);

    void deleteById(Long id);

    List<SessionSpeaker> findBySessionIdAndEventId(Long sessionId, Long eventId);

    BigInteger isUserSpeakerInSession(Long userId, Long sessionId);

    List<SessionSpeaker> nextPositionSessionSpeaker(Long sessionSpeakerId, Long sessionId, Double currentPosition);

    List<SessionSpeaker> previousPositionSessionSpeaker(Long sessionSpeakerId, Long sessionId, Double currentPosition);

    void updatePositionSessionSpeaker(Long speakerId, Double startPosition, Double endPosition, Double updateCount);

    void updatePositionForAllSessionSpeakerBySessionId(double updateCount, long sessionId);

    List<SessionSpeaker> findBySessionIdsBetween(Long id);

    SessionSpeaker findFirstBySessionIdOrderByPositionDesc(Long sessionId);

    List<SessionSpeaker> findAllSessionSpeakerBySpeakerIdAndSessionFormat(List<Long> speakerIds, List<EnumSessionFormat> sessionFormats);

    List<Session> findSessionBySpeakerUserId(Long eventId, Long userId, List<EnumSessionFormat> sessionFormats);

    Page<Session> findSessionBySpeakerUserId(Long eventId, Long userId, String search, Pageable pageable, List<EnumSessionFormat> enumSessionFormats, EnumSessionStatus status);

    List<SessionSpeaker> getAllSessionSpeakerBetweenSessionIds(Long from, Long to);

    SessionSpeaker findBySessionIdAndSpeakerIdAndEventId(Long sessionId, Long speakerId, Long eventId);

    SessionSpeaker findBySessionIdAndSpeakerUserId(Long sessionId, Long speakerIdUserId);

    Boolean isSpeakerModeratorInSession(Long sessionId, Long userId);

    Long getSpeakerIdBySessionIdAndUserId(Long sessionId, Long userId);

    Page<Session> findSessionBySpeakerUserIdByPast(Long eventId, Long userId, String search, Date currentDate, Pageable pageable, List<EnumSessionFormat> enumSessionFormats, EnumSessionStatus status);

    List<Long> getAllSessionIdsBySpeakerId(Long speakerId);

    List<SessionSpeaker> getSpeakerUserIdBySessionId(Long sessionId);

    void deleteSessionSpeakerBySpeakerIds(List<Long> speakerIds);

    List<IdCountDto> countSessionSpeakerBySessionIdIn(List<Long> sessionIds);

    List<Long> findSpeakerUserIdBySessionId(Long sessionId);

    List<SessionSpeaker> findByEventId(Long eventId);

    List<SessionSpeakerIdDto> findSessionSpeakerIdByEventId(Long eventId);

    List<SessionSpeaker> findByEventIdWithoutCache(Long eventId);

    boolean isSpeakerAvailable(Long speakerId);

    void updateSessionSpeakerBySpeakerId(Long speakerId,Long currentSpeakerId);

    BigInteger isUserSpeakerInSessionByEventId(Long userId, Long eventId);

    List<EventTaskEmailDto> findSpeakersEmailNameAndSessionNameByEventIdAndSessionIds(Long eventId, List<Long> sessionIds);

    void deleteSessionSpeakerById(long id);

    List<Long> findSessionByEventIdAndSpeakerUserId(Long eventId, Long userId);

    public void deleteBySpeakerIdAndSessionIds(Long speakerId, List<Long> sessionIds);
}
