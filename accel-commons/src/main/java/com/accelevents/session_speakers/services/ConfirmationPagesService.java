package com.accelevents.session_speakers.services;


import com.accelevents.domain.CustomTemplates;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RegistrationRequestType;
import com.accelevents.domain.enums.TemplateType;
import com.accelevents.dto.*;
import com.accelevents.domain.ticketing.TicketTypeDto;

import java.util.List;

public interface ConfirmationPagesService {

    ResponseDto createConfirmationPage(Event event, Long userId, CustomTemplatesDto customTemplatesDto, boolean isDefault);

    DataTableResponse getAllConfirmationPages(Event event, PageSizeSearchObj pageSizeSearchObj);

    ResponseDto updateConfirmationPages(Event event, CustomTemplatesDto customTemplatesDto, Long confirmationPageId, User user);

    CustomTemplatesDto getConfirmationPage(Event event, Long confirmationPageId);

    ResponseDto deleteConfirmationPages(Event event, Long confirmationPageId,User user);

    ResponseDto resetDefaultConfirmationPage(Event event, User user, Long confirmationPageId);

    CustomTemplates findByEventAndIsDefaultPage(Event event, boolean isDefaultPage);

    ResponseDto duplicateConfirmationPage(Event event, Long confirmationPageId,String pageName, List<Long> allowedTicketTypes,User user,String pageUrl);

    List<CustomTemplateDetailsDto> getAllConfirmationPagesList(Event event);

    ResponseDto updatePosition(Event event, Long confirmationPageId, Long topPageId, Long bottomPageId, TemplateType templateType);

    String getDuplicatePageNameByCustomPageId(Event event, Long pageId, User user,TemplateType templateType);

    ResponseDto updateUnpublishedHtml(Event event, Long confirmationPageId, BeeFreePageDto beeFreePageDto);

    CustomTemplatesDto getConfirmationPagePreviewByPageId(Event event, Long confirmationPageId);

    CustomTemplatesDto getConfirmationPageByOrder(Event event, Long orderId);

    List<TicketTypeDto> getTicketTypesForConfirmationPage(Event event);

    CustomTemplatesDto getConfirmationPageByRegistrationRequest(Event event, Long registrationRequestId, RegistrationRequestType registrationRequestType);
}
