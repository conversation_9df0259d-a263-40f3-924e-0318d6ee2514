package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionTagAndTrack;
import com.accelevents.session_speakers.repo.SessionTagAndTrackRepo;
import com.accelevents.session_speakers.services.SessionTagAndTrackRepoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public class SessionTagAndTrackRepoServiceImpl implements SessionTagAndTrackRepoService
{

    @Autowired private SessionTagAndTrackRepo sessionTagAndTrackRepo;


    @Override
    public void deleteBySessionIdAndTagOrTrackIdIn(Long sessionId, Set<Long> tagIdOrTrackIds) {
        sessionTagAndTrackRepo.deleteBySessionIdAndTagOrTrackIdIn(sessionId,tagIdOrTrackIds);
    }

    @Override
    public List<Long> findByIds(List<Long> tagOrTrackIds) {
        return sessionTagAndTrackRepo.findByIds(tagOrTrackIds);
    }

    @Override
    public void deleteByTagOrTrackIds(List<Long> tagIdOrTrackIds) {
        sessionTagAndTrackRepo.deleteByTagOrTrackIds(tagIdOrTrackIds);
    }

    @Override
    public void deleteBySessionId(Long sessionId) {
        sessionTagAndTrackRepo.deleteBySessionId(sessionId);
    }

    @Override
    public List<SessionTagAndTrack> findBySessionId(Long sessionId) {
        return sessionTagAndTrackRepo.findBySessionId(sessionId);
    }

    @Override
    public Iterable<SessionTagAndTrack> saveAll(Iterable<SessionTagAndTrack> sessionTagAndTracks) {
        return sessionTagAndTrackRepo.saveAll(sessionTagAndTracks);
    }

    @Override
    public List<Long> findSessionIdsByIds(List<Long> tagOrTrackIds) {
        return sessionTagAndTrackRepo.findSessionIdsByIds(tagOrTrackIds);
    }

    @Override
    public List<Session> findSessionsByTagOrTrackIds(List<Long> tagOrTrackIds) {
        return sessionTagAndTrackRepo.findSessionsByTagOrTrackIds(tagOrTrackIds);
    }

    @Override
    public Boolean existsBySessionIdAndTagOrTrackId(Long sessionId, Long tagOrTrackId) {
        return sessionTagAndTrackRepo.existsBySessionIdAndTagOrTrackId(sessionId,tagOrTrackId);
    }

    @Override
    public List<Long> existsBySessionIdAndTagOrTrackIds(Long sessionId, List<Long> tagOrTrackId) {
        return sessionTagAndTrackRepo.existsBySessionIdAndTagOrTrackIds(sessionId,tagOrTrackId);
    }
}
