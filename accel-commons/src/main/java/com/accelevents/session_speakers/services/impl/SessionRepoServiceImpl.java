package com.accelevents.session_speakers.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.SurveySessionsBasicDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.StreamKeyAndRtmpUrlDto;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.services.StaffService;
import com.accelevents.services.UserService;
import com.accelevents.services.VirtualEventPortalService;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.SessionRepo;
import com.accelevents.session_speakers.repo.SessionSpeakerRepo;
import com.accelevents.session_speakers.repo.UserSessionRepo;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionSpeakerService;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.utils.*;
import com.accelevents.virtualevents.dto.GAUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.domain.enums.EnumSessionFormat.*;
import static com.accelevents.utils.Constants.DISPLAY;
import static com.accelevents.utils.Constants.STRING_COMMA;
import static com.accelevents.dto.PageUtil.getPageable;
import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.springframework.util.CollectionUtils.isEmpty;

@Service
public class SessionRepoServiceImpl implements SessionRepoService {

    private static final Logger log = LoggerFactory.getLogger(SessionRepoServiceImpl.class);
    @Autowired
    private SessionRepo repo;
    @Autowired
    private StaffService staffService;
    @Autowired
    private SpeakerService speakerService;
    @Autowired
    private SessionSpeakerService sessionSpeakerService;
    @Autowired
    private VirtualEventPortalService virtualEventPortalService;
    @Autowired
    private UserService userService;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private UserSessionRepo userSessionRepo;
    @Autowired
    private SessionSpeakerRepo sessionSpeakerRepo;

    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;

    @Autowired
    private GamificationCacheStoreService<String,Object> redisCacheService;

    @Override
    public Session save(Session sessions) {
        Session session = repo.save(sessions);
        log.info("saveSession {} liveStreamId {} streamUrl {} taskId=>{},taskIdAfterSave=>{}", session.getId(), session.getLiveStreamId(), session.getStreamUrl(), sessions.getTaskId(), session.getTaskId());
        log.info(GeneralUtils.getStackTrace());
        //TODO: APIGW_UN
        //clearSessionAPIGatewayCache.clearSessionCache(sessions);
        if(null != session.getEventId()) {
            clearAPIGatewayCache.clearAPIGwSessionTrackCache(session.getEventId());
            clearAPIGatewayCache.clearAPIGwSessionSponsorsCacheByEventId(session.getEventId());
            clearAPIGatewayCache.clearAPIGwSessionExhibitorCacheByEventId(session.getEventId());
            //clearAPIGatewayCache.clearAPIGwSessionsCacheByEventId(session.getEventId());
            redisCacheService.clearSessionCache(session.getEventId());
        }
        return session;
    }

    @Caching(evict = {
            @CacheEvict(value = "findAllSessionDatesByEvent", allEntries = true),
            @CacheEvict(value = "findAllSessionForAdminOrStaffDatesByEvent", allEntries = true),
            @CacheEvict(value = "SessionByEventAndFormat", allEntries = true),
            @CacheEvict(value = "allSessionByEvent",allEntries = true),
            @CacheEvict(value = "sessionByEventAndSessionId", allEntries = true),
            @CacheEvict(value = "findByConfId", allEntries = true),
            @CacheEvict(value = "SessionTitleByEventIdOrderByIdAsc",allEntries = true),
            @CacheEvict(value = "getSessionEndDateTypeByEvent", allEntries = true),
            @CacheEvict(value = "getAllHiddenSessionOfEventId", allEntries = true),
            @CacheEvict(value = "getAllSessionsByEventIdAndHideFromAttendees", allEntries = true),
            @CacheEvict(value = "getAllPrivateSessionByEventId", allEntries = true)
    })
    @Override
    public void saveAll(List<Session> sessions) {

        if(!CollectionUtils.isEmpty(sessions) && null != sessions.get(0) && NumberUtils.isNumberGreaterThanZero(sessions.get(0).getEventId())) {
            repo.saveAll(sessions);
            Long eventId = sessions.get(0).getEventId();
            clearAPIGatewayCache.clearAPIGwSessionTrackCache(eventId);
            clearAPIGatewayCache.clearAPIGwSessionSponsorsCacheByEventId(eventId);
            clearAPIGatewayCache.clearAPIGwSessionExhibitorCacheByEventId(eventId);
            //clearAPIGatewayCache.clearAPIGwSessionsCacheByEventId(session.getEventId());
            redisCacheService.clearSessionCache(eventId);
        }

        //TODO: APIGW_UN
        //clearSessionAPIGatewayCache.clearSessionCache(sessions);

    }

    @Override
    public Optional<Session> findById(Long sessionId) {
        return repo.findById(sessionId);
    }

    @Override
    public Optional<Session> findByConfIdCacheData(Long confId) {
        return repo.findByConfId(confId);
    }

    @Override
    public Session getSessionById(Long id, Event event) {
        Session session = repo.findByEventIdAndId(event.getEventId(), id);
        if (session == null) {
            throw new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND);
        }
        return session;
    }

    @Override
    public Session getSessionByIdWithoutCache(Long id, Event event) {
        Session session = repo.findByEventIdAndIdWithoutCache(event.getEventId(), id);
        if (session == null) {
            throw new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND);
        }
        return session;
    }

    @Override
    public Session getSessionByIdJoinFetchTagsTrack(Long id, Event event) {
        return repo.findByIdJoinFetch(id, event.getEventId())
                .orElseThrow(() -> new NotFoundException(NotFoundException.SessionNotFound.SESSION_NOT_FOUND));
    }

    @Override
    public List<String> findSessionsWithConflictingSlot(Event event, String startTime,
                                                        String endTime, Long sessionId) {
        return repo.findSessionsWithConflictingSlot(event.getEventId(),
                getDateInUTC(startTime, event.getEquivalentTimeZone()),
                getDateInUTC(endTime, event.getEquivalentTimeZone()),
                MAIN_STAGE,
                sessionId);
    }

    @Override
    public Page<Session> findAllByEventIdAndIdIn(Event event, List<Long> sessionIds,
                                                 Pageable pageRequest, User user, boolean showPastAndUpcoming, boolean isAdminOrStaff, String filterDate) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return new PageImpl<>(emptyList());
        }
        List<Session> sessions = repo.getSessionByEvent(event.getEventId());
        List<Session> sessionsToReturn = filteredSessions(sessions, new HashSet<>(sessionIds), null, null, user, null, filterDate, showPastAndUpcoming, isAdminOrStaff, Constants.STRING_EMPTY, event);
        return getPageable(sessionsToReturn, pageRequest);
    }


    @Override
    public List<Session> getAllSessionByEventId(Event event, EnumSessionFormat sessionFormat, boolean isAdminOrStaff) {
        String format = sessionFormat != null ? sessionFormat.name() : null;
        List<Session> sessions = repo.getSessionByEvent(event.getEventId());
        return filteredSessions(sessions, null, null, null, null, format, null, true, isAdminOrStaff, Constants.STRING_EMPTY, event);
    }


    @Override
    public Page<Session> getAllHostSessionByEventId(Event event,
                                                    String searchStr,
                                                    Pageable pageable,
                                                    boolean isFromBillingPage,
                                                    List<Date> searchDate, List<EnumSessionFormat> sessionTypes,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming,List<SessionTypeFormat>... sessionTypeFormats) {

        List<SessionTypeFormat> typeFormats = new ArrayList<>();
        if (CollectionUtils.isEmpty(Arrays.asList(sessionTypeFormats))){
            typeFormats=new ArrayList<>(Arrays.asList(SessionTypeFormat.HYBRID,SessionTypeFormat.VIRTUAL,SessionTypeFormat.IN_PERSON));
        }else {
            typeFormats=sessionTypeFormats[0];
        }
        if (isFromBillingPage) {
            List<EnumSessionFormat> enumSessionFormats = Arrays.asList(BREAK, OTHER, EXPO);
            return repo.getAllSessionForHost(event.getEventId(), enumSessionFormats, pageable,typeFormats);
        } else {
            if (CollectionUtils.isEmpty(sessionTypes)){
                sessionTypes=new ArrayList<>(Arrays.asList(MAIN_STAGE,
                        BREAKOUT_SESSION,
                        MEET_UP,
                        WORKSHOP,
                        EXPO,
                        BREAK,
                        OTHER));
            }
            if (isEmpty(listOfTagAndTrackIds)) {
                listOfTagAndTrackIds.add(0L);
            }
            return repo.getAllSessionForHost(event.getEventId(), searchStr, pageable,typeFormats, searchDate, sessionTypes, event.getEquivalentTimeZone(),listOfTagAndTrackIds,isPast,isUpcoming);
        }
    }

    @Override
    public Page<Session> getAllHostSessionByEventIdWithSorting(Event event,
                                                    String searchStr,
                                                    Pageable pageable,
                                                    boolean isFromBillingPage,
                                                    List<Date> searchDate,List<EnumSessionFormat> sessionTypes,
                                                    List<SessionTypeFormat> sessionTypeFormats,List<Long> listOfTagAndTrackIds,boolean isPast,boolean isUpcoming) {
        if (isFromBillingPage) {
            List<EnumSessionFormat> enumSessionFormats = Arrays.asList(BREAK, OTHER, EXPO);
            return repo.getAllSessionForHostWithSorting(event.getEventId(), enumSessionFormats, pageable,sessionTypeFormats);
        } else {
            if (CollectionUtils.isEmpty(sessionTypes)){
                sessionTypes=new ArrayList<>(Arrays.asList(MAIN_STAGE,
                        BREAKOUT_SESSION,
                        MEET_UP,
                        WORKSHOP,
                        EXPO,
                        BREAK,
                        OTHER));
            }
            if (isEmpty(listOfTagAndTrackIds)) {
                listOfTagAndTrackIds.add(0L);
            }
            return repo.getAllSessionForHostWithSorting(event.getEventId(), searchStr, pageable,sessionTypeFormats, searchDate, sessionTypes, event.getEquivalentTimeZone(),listOfTagAndTrackIds,isPast,isUpcoming);
        }
    }

    @Override
    public Page<Session> getAllSessionByEventId(Event event,
                                                Pageable pageable,
                                                SessionFilter filter, User user, boolean isAdminOrStaff, String calledFrom) {
        List<Long> filterSessionIds = new ArrayList<>();
        String searchString = filter.getSearch();
        if (!isEmpty(filter.getTagOrTrackIds()) || isNotBlank(searchString) || !CollectionUtils.isEmpty(filter.getIds())) {
            if (isEmpty(filter.getTagOrTrackIds())) {
                filter.setTagOrTrackIds(singletonList(0L));
            }
            searchString = searchString.trim();
            if(!CollectionUtils.isEmpty(filter.getIds())){
                filterSessionIds = filter.getIds();
            }
            else{
                if(StringUtils.isNotBlank(searchString)){
                    filterSessionIds = repo.filterSession(event.getEventId(), filter.getTagOrTrackIds(), searchString);
                }else{
                    filterSessionIds = repo.filterSessionWithoutSearch(event.getEventId(), filter.getTagOrTrackIds());
                }
            }
            if (filterSessionIds.isEmpty()) {
                return new PageImpl(emptyList());
            }
        }

        Set<Long> finalFilterSessionIds = !filterSessionIds.isEmpty() ? new HashSet<>(filterSessionIds) : null;

        List<Long> userTicketTypes = null;
        if (null != user) {
            if (!allSessionVisibleForUser(user, event) || !isAdminOrStaff) {
                userTicketTypes = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(event, user);
            }
        }

        List<Session> sessions = repo.getSessionByEvent(event.getEventId());
        List<Session> sessionsToReturn = new ArrayList<>();

        if (null != filter.getFilterDates()) {
            for (String date : filter.getFilterDates()) {
                sessionsToReturn.addAll(filteredSessions(sessions, finalFilterSessionIds, userTicketTypes, filter.getPast(), user, filter.getSessionFormat(), date, filter.isShowPastAndUpcoming(), isAdminOrStaff, calledFrom, event));
            }
        } else {
            sessionsToReturn = filteredSessions(sessions, finalFilterSessionIds, userTicketTypes, filter.getPast(), user, filter.getSessionFormat(), null, filter.isShowPastAndUpcoming(), isAdminOrStaff, calledFrom, event);
        }
        if (isAdminOrStaff) {
            return getPageable(sessionsToReturn, pageable);
        } else {
            List<Session> filteredSession = filterPrivateSessions(event, user, sessionsToReturn);

            return getPageable(filteredSession, pageable);
        }
    }

    @Override
    public List<Session> filterPrivateSessions(Event event, User user, List<Session> sessionsToReturn) {
        List<Session> filteredSession = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sessionsToReturn)) {
            List<Session> privateSessions = new ArrayList<>();
            List<EnumSessionFormat> enumSessionFormats = Arrays.asList(WORKSHOP, MAIN_STAGE, BREAKOUT_SESSION, MEET_UP);

            sessionsToReturn.stream().forEach(session -> {
                if (null != user && enumSessionFormats.contains(session.getFormat()) && SessionVisibilityType.PRIVATE.equals(session.getSessionVisibilityType())) {
                    privateSessions.add(session);
                } else if (!SessionVisibilityType.PRIVATE.equals(session.getSessionVisibilityType())) {
                    filteredSession.add(session);
                }
            });

            if (!CollectionUtils.isEmpty(privateSessions)) {
                List<Long> privateSessionIds = privateSessions.stream().map(session -> session.getId()).collect(Collectors.toList());
                List<Long> userSessionIds = userSessionRepo.findSessionIdByUserIdAndEventIdAndSessionIdIn(user.getUserId(), event.getEventId(), privateSessionIds);
                privateSessions.stream().forEach(session -> {
                    if (userSessionIds.contains(session.getId()) || sessionSpeakerService.isUserSpeakerInSession(user.getUserId(), session.getId())
                            || sessionSpeakerService.isSpeakerModeratorInSession(user.getUserId(), session.getId())) {
                        filteredSession.add(session);
                    }
                });
            }
        }
        filteredSession.sort(Comparator.comparing(Session::getStartTime));
        return filteredSession;
    }

    private boolean canSelectRecord(Session session, Date currentDate, Boolean past, OffsetDateTime startDate, OffsetDateTime endDate, boolean showPastAndUpcoming) {
        if (startDate != null && endDate != null) {
            boolean withinDateRange = isWithinDateRange(session, startDate, endDate);
            if (showPastAndUpcoming) {
                return withinDateRange;
            }
            return withinDateRange && isUpcomingOrStarted(session, currentDate);
        }

        if (past == null) {
            return showPastAndUpcoming || isUpcomingOrStarted(session, currentDate);
        }

        return past ? session.getEndTime().before(currentDate) : isUpcomingOrStarted(session, currentDate);
    }

    private boolean isWithinDateRange(Session session, OffsetDateTime startDate, OffsetDateTime endDate) {
        Date start = Date.from(startDate.toInstant());
        Date end = Date.from(endDate.toInstant());
        return !start.after(session.getStartTime()) && !end.before(session.getStartTime());
    }

    private boolean isUpcomingOrStarted(Session session, Date currentDate) {
        return (session.getSessionStartStatus() != null && session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED))
                || session.getEndTime().after(currentDate);
    }

    private List<Session> filteredSessions(List<Session> sessions, Set<Long> finalFilterSessionIds, List<Long> userTicketTypes, Boolean past,//NOSONAR
                                           User user,
                                           String sessionFormat, String filterDate,
                                           boolean showPastAndUpcoming, boolean isAdminOrStaff, String calledFrom, Event event) {
        List<Session> sessionsToReturn = new ArrayList<>();
        List<Long> allSessionIdBySpeakerUserId = null;
        Date currentDate = new Date();
        OffsetDateTime startDate = isNotBlank(filterDate) ? DateUtils.getDateInUTCFromTimeZoneStr(filterDate) : null;
        OffsetDateTime endDate = isNotBlank(filterDate) ? (startDate != null ? DateUtils.add24Hours1SecLess(startDate) : null) : null;
        boolean isLoginUser = user != null;
        if (isLoginUser) {
            allSessionIdBySpeakerUserId = sessionSpeakerRepo.findAllSessionIdBySpeakerUserId(user.getUserId(), event.getEventId());
        }
        List<Long> finalAllSessionIdBySpeakerUserId = allSessionIdBySpeakerUserId;
        sessions.forEach(session -> {
            if (!DISPLAY.equalsIgnoreCase(calledFrom) && !isLoginUser && session.isHideSessionFromAttendees()) {
                return;
            }
            if (isNotBlank(sessionFormat) && null != session.getFormat() && !sessionFormat.equals(session.getFormat().name())) {
                return;
            }
            boolean selected = isSelected(finalFilterSessionIds, past, showPastAndUpcoming, currentDate, startDate, endDate, session);
            if (selected && !EnumSessionStatus.DRAFT.equals(session.getStatus())) {
                    if (EnumSessionStatus.HIDDEN.equals(session.getStatus())) {
                        log.info("session Status {}", session.getStatus());
                        if (isAdminOrStaff) {
                            sessionsToReturn.add(session);
                        }
                    } else {
                        if (!CollectionUtils.isEmpty(userTicketTypes) && session.isHideSessionFromAttendees()
                                && isNotBlank(session.getTicketTypesThatCanBeRegistered())) {
                            List<Long> eventTicketTypeIds = Stream.of(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                                    .map(Long::parseLong)
                                    .collect(Collectors.toList());
                            if (!Collections.disjoint(eventTicketTypeIds, userTicketTypes)) {
                                sessionsToReturn.add(session);
                            } else if (isAdminOrStaff || (!CollectionUtils.isEmpty(finalAllSessionIdBySpeakerUserId) && finalAllSessionIdBySpeakerUserId.contains(session.getId()))) {
                                sessionsToReturn.add(session);
                            }
                        } else if (!CollectionUtils.isEmpty(userTicketTypes) && session.isHideSessionFromAttendees()
                                && isBlank(session.getTicketTypesThatCanBeRegistered())) {
                            log.info("user can not access this hidden session with no ticket types selected {}", session.getId());
                        }else {
                            sessionsToReturn.add(session);
                        }
                    }
                }
        });
        return sessionsToReturn;
    }

    private boolean isPurchaseTicketTypeFoundInSessionAllowTicketTypes(List<Long> userTicketTypes, List<Session> sessionsToReturn, Session session) {
        List<Long> eventTicketTypeIds = Stream.of(session.getTicketTypesThatCanBeRegistered().trim().split(STRING_COMMA))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        if (!Collections.disjoint(eventTicketTypeIds, userTicketTypes)) {
            return true;
        }
        return false;
    }

    private boolean isSelected(Set<Long> finalFilterSessionIds, Boolean past, boolean showPastAndUpcoming, Date currentDate, OffsetDateTime startDate, OffsetDateTime endDate, Session session) {
        boolean selected = false;

        if (finalFilterSessionIds != null) {
            if (finalFilterSessionIds.contains(session.getId())) {
                selected = canSelectRecord(session, currentDate, past, startDate, endDate, showPastAndUpcoming);
            }
        } else {
            selected = canSelectRecord(session, currentDate, past, startDate, endDate, showPastAndUpcoming);
        }
        return selected;
    }

    public boolean allSessionVisibleForUser(User user, Event event) {
        return speakerService.isSpeakerInEvent(event, user) || virtualEventPortalService.isUserExhibitorAdminOrLeadRetriever(user, event);
    }

    @Override
    public List<Session> getAllSessionsByEventIdAndSponsorExhibitorJsonNotNull(Event event) {
        return repo.getAllSessionsByEventIdAndSponsorExhibitorJsonNotNull(event.getEventId());
    }

    @Override
    public Page<Session> getAllCachePrivateSessionByEventId(Event event, Pageable pageable, SessionFilter filter, List<Session> userSpecificSessions) {
        List<Long> filterSessionIds = new ArrayList<>();
        String searchString = filter.getSearch();
        if (!isEmpty(filter.getTagOrTrackIds()) || isNotBlank(searchString) || !CollectionUtils.isEmpty(filter.getIds())) {
            if (isEmpty(filter.getTagOrTrackIds())) {
                filter.setTagOrTrackIds(singletonList(0L));
            }
            if (!CollectionUtils.isEmpty(filter.getIds())) {
                filterSessionIds = filter.getIds();
            } else {
                if (StringUtils.isNotBlank(searchString)) {
                    filterSessionIds = repo.filterSession(event.getEventId(), filter.getTagOrTrackIds(), searchString.trim());
                } else {
                    filterSessionIds = repo.filterSessionWithoutSearch(event.getEventId(), filter.getTagOrTrackIds());
                }
            }
            if (filterSessionIds.isEmpty()) {
                return new PageImpl(emptyList());
            }
        }

        Set<Long> finalFilterSessionIds = !filterSessionIds.isEmpty() ? new HashSet<>(filterSessionIds) : null;

        List<Session> sessionsToReturn;

        if (null != filter.getFilterDate()) {
            sessionsToReturn = filteredCachePrivateSessions(userSpecificSessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), filter.getFilterDate(), filter.isShowPastAndUpcoming());
        } else {
            sessionsToReturn = filteredCachePrivateSessions(userSpecificSessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), null, filter.isShowPastAndUpcoming());
        }
        return getPageable(sessionsToReturn, PageRequest.of(0, Integer.MAX_VALUE));
    }

    @Override
    public List<Session> findAllByIds(List<Long> sessionIds) {
        return repo.findSessionsByIds(sessionIds);
    }

    @Override
    public List<SessionDetailsCalendarViewDto> findAllHostSessions(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, List<Long> speakerIds, List<Long> locationIds, Date startDate, Date endDate) {
        return repo.findAllHostSessions(event.getEventId(), searchString, sessionTypes, listOfTagAndTrackIds, speakerIds, locationIds, startDate, endDate);
    }

    @Override
    public Page<SessionDetailsCalendarViewDto> getAllHostUnscheduledSessionByEventId(Event event, String searchString, List<EnumSessionFormat> sessionTypes, List<Long> listOfTagAndTrackIds, List<Long> speakerIds, List<Long> locationIds, Pageable pageable) {
        return repo.getAllHostUnscheduledSessionByEventId(event.getEventId(), searchString, sessionTypes, listOfTagAndTrackIds, speakerIds,locationIds, pageable);
    }

    @Override
    public List<Session> getActiveExtendedSessions(StreamProvider streamProvider, Date now) {
        return repo.getActiveExtendedSessions(streamProvider, now);
    }

    @Override
    public Boolean isSessionStarted(Long sessionId, Event event) {
        Session session = repo.findByEventIdAndIdWithoutCache(event.getEventId(), sessionId);
        if (session != null) {
            return EnumSessionStartStatus.STARTED.equals(session.getSessionStartStatus());
        }
        return false;
    }

    @Override
    public Optional<Session> findByLiveStreamId(String liveStreamId) {
        return Optional.ofNullable(repo.findByLiveStreamId(liveStreamId));
    }

    @Override
    public Optional<Session> findByStreamUrl(String streamUrl) {
        return Optional.ofNullable(repo.findByStreamUrl(streamUrl));
    }

    @Override
    public void updateStatusToDeleteBySessionId(Long sessionId, Long eventId, RecordStatus delete) {
        repo.updateStatusToDeleteBySessionId(sessionId, eventId, delete.name());
    }


    @Override
    public List<Object[]> getNetworkingSessionActivity(long eventId) {
        return repo.getNetworkingSessionActivity(eventId);
    }

    @Override
    public Boolean isSessionsBookedByUserAndBetweenStartEndTime(List<Long> userIds, Date startTime, Date endTime, Long eventId) {
        return repo.isSessionsBookedByUserAndBetweenStartEndTime(userIds, startTime, endTime, eventId);
    }

    @Override
    public List<Session> findAllSessionIdByEventAndSessionFormatNotIn(Event event, Set<EnumSessionFormat> sessionFormat) {
        List<Session> sessions = repo.getSessionByEvent(event.getEventId());
        if (sessions != null && !sessions.isEmpty()) {
            return sessions;
        }
        return Collections.emptyList();
    }

    @Override
    public List<Session> findSessionByEventId(Event event) {
        log.info("SessionRepoServiceImpl || findSessionByEventId || existingSessions || eventId {} ", null != event?event.getEventId():null);
        return null!=event?repo.getSessionByEvent(event.getEventId()):Collections.emptyList();
    }


    @Override
    @Transactional
    public Session updateSessionOnMuxStreamKeyCreation(Session session, StreamKeyAndRtmpUrlDto dto) {
        GAUtils.handleRtmpTable(session, dto);
        session.setLiveStreamId(dto.getLiveStreamId());
        session.setPlayBackRestrictionToken(dto.getPlayBackRestrictionToken());
        session.setThumbnailRestrictionToken(dto.getThumbnailRestrictionToken());
        log.info("updateSessionOnMuxStreamKeyCreation sessionId {} liveStreamId {} playBackRestrictionToken {}", session.getId(), session.getLiveStreamId(), session.getPlayBackRestrictionToken());
        return save(session);
    }

    @Override
    public Set<String> findAllSessionDatesByEvent(Event event, String timezoneOffset, boolean isAdminOrStaff) {
        //return repo.findAllSessionDatesByEvent(event.getEventId(),timezoneOffset);
        String returnOffset = timezoneOffset.replace(":", "");
        ZoneOffset zoneOffSet = ZoneOffset.of(timezoneOffset);
        List<Date> eventAllDate = isAdminOrStaff ? repo.findAllSessionForAdminOrStaffDatesByEvent(event.getEventId()) : repo.findAllSessionDatesByEvent(event.getEventId());
        return eventAllDate.stream().map(date ->
                TimeZoneUtil.getDateInLocal(date, zoneOffSet, "yyyy-MM-dd") + "T00:00:00" + returnOffset).collect(Collectors.toCollection(TreeSet::new));
    }

    @Override
    public Page<Session> findAllByEventIdAndSessionFormatAndIdIn(Event event, List<Long> sessionIds, Pageable pageable, String sessionFormat, boolean showPastAndUpcoming, boolean isAdminOrStaff, User user, String filterDate) {
        List<Session> sessions = repo.findSessionByEventId(event.getEventId());
        List<Session> sessionsToReturn = filteredSessions(sessions, new HashSet<>(sessionIds), null, null, user, sessionFormat, filterDate, showPastAndUpcoming,isAdminOrStaff,Constants.STRING_EMPTY,event);
        return getPageable(sessionsToReturn, pageable);
    }

    @Override
    public long getSessionCountByEventId(Long eventId) {
        long count = 0;
        List<Session> sessions = repo.getSessionByEvent(eventId);
        if (!CollectionUtils.isEmpty(sessions)) {
            count = sessions.size();
        }
        return count;
    }

    @Override
    public long getNonVisualSessionsByEvent(Long eventId, List<EnumSessionFormat> enumSessionFormats) {
        return repo.getNonVisualSessionsByEvent(eventId, enumSessionFormats);
    }

    @Override
    public List<Session> findSessionByEventIdOrderByIdAsc(Long eventId) {
        return repo.findSessionByEventIdOrderByIdAsc(eventId);
    }

    @Override
    public List<SessionIdTitleDto> getSessionIdAndTitleById(List<Long> sessionId) {
        return repo.getSessionIdAndTitleById(sessionId);
    }

    @Override
    public List<Session> findSessionByFromTo(Long from, Long to) {
        return repo.getSessionByFromTo(from, to)
                .stream().filter(session -> null != session.getSponsorExhibitorJson()
                        && RecordStatus.CREATE.equals(session.getRecordStatus())).collect(Collectors.toList());
    }

    @Override
    public List<String> findSessionTitleByEventIdOrderByIdAsc(Long eventId) {
        return repo.findTitleByEventIdOrderByIdAsc(eventId);
    }

    @Override
    public long findSessionCountBySessionName(Long eventId, String sessionName) {
        return repo.findSessionCountBySessionName(eventId, sessionName);
    }

    @Override
    public String findTaskIdBySessionId(Long sessionId) {
        return repo.findTaskIdBySessionId(sessionId);
    }

    @Override
    public Long getEventIdBySessionId(Long sessionId) {
        long eventId = 0;
        Optional<Long> eventIdBySessionId = repo.getEventIdBySessionId(sessionId);
        if (eventIdBySessionId.isPresent()) {
            eventId = eventIdBySessionId.get();
        } else {
            log.error("not able to find eventId for sessionId {}", sessionId);
        }
        return eventId;
    }

    @Override
    public List<Session> findByEventAndFormat(Long eventId, List<EnumSessionFormat> sessionFormat) {
        return repo.findByEventAndFormat(eventId, sessionFormat);
    }

    @Override
    public Session nextPositionSession(Long sessionId, Long eventId, Double currentPosition) {
        return repo.findFirstByIdAndEventIdAndPositionGreaterThanOrderByPositionAsc(sessionId, eventId, currentPosition);
    }

    @Override
    public Session previousPositionSession(Long sessionId, Long eventId, Double currentPosition) {
        return repo.findFirstByIdAndEventIdAndPositionLessThanOrderByPositionDesc(sessionId, eventId, currentPosition);
    }

    @Override
    public void updatePositionSession(Long eventId, Double startPosition, Double endPosition, Double updateCount) {
        repo.updatePositionSession(eventId, startPosition, endPosition, updateCount);
    }

    @Override
    public void updatePositionForAllSessionByEventId(Double updateCount, Long eventId, Session topSession) {
        repo.updatePositionForAllSessionByEventId(updateCount, eventId, topSession.getStartTime(), topSession.getEndTime());
    }

    @Override
    public Session findFirstByEventIdOrderByPositionDesc(Long eventId) {
        return repo.findFirstByEventIdOrderByPositionDesc(eventId);
    }

    @Override
    public Session getLastConcurrentSession(Long eventId, Date startTime, Date endTime) {
        return repo.findFirstByEventIdAndStartTimeAndEndTimeOrderByPositionDesc(eventId, startTime, endTime);
    }

    @Override
    public List<Session>    getSessionsByTimeAndSortType(long eventId, Date startTime, Date endTime, String sortType) {
        if (sortType.equals("ASC"))
            return repo.getSessionsByTimeASC(eventId, startTime, endTime);
        else
            return repo.getSessionsByTimeDESC(eventId, startTime, endTime);
    }

    @Override
    public List<Session> getSessionsByStartTimeAndEndTimeUnique(long eventId) {
        return repo.getSessionsGroupByStartTimeAndEndTime(eventId);
    }

    @Override
    public List<Session> getLastConcurrentSessionForDataFix(Long eventId, Date startTime, Date endTime) {
        return repo.getLastConcurrentSessionForDataFix(eventId, startTime, endTime);
    }

    @Override
    public List<SessionEventLiveStream> findAllByLiveStreamIdsIn(List<String> liveStreamIds) {
        return repo.findAllByLiveStreamIdsIn(liveStreamIds);
    }

    @Override
    public void updatePlayBackUrlBySessionId(Long key, String value, Long eventId) {
        repo.updatePlayBackUrlBySessionId(key, value, eventId);
    }

    @Override
    @Transactional
    public void updateRecordStatus(List<Long> sessionIdsToBeMarkedAsPlaybackDeleted, RecordStatus block) {
        if (CollectionUtils.isEmpty(sessionIdsToBeMarkedAsPlaybackDeleted)) {
            return;
        }

        repo.updateRecordStatus(sessionIdsToBeMarkedAsPlaybackDeleted, block);
    }

    @Override
    public List<Session> findByEventIdAndRecordStatus(Long eventId, RecordStatus block) {
        return repo.findByEventIdAndRecordStatus(eventId, block);
    }

    @Override
    public List<Session> findSessionFormatBySessionName(Long eventId) {
        return repo.findSessionFormatBySessionName(eventId);
    }

    @Override
    public List<Session> getAllSessionByIdsAndEventId(List<Long> sessionIds, Event event) {
        return repo.getAllSessionByIdsAndEventId(sessionIds, event.getEventId());
    }

    @Override
    public List<Session> findByEventIdAndEnabledLowLatency(Event event, String rtmpUrl, StreamProvider streamProvider) {
        return repo.findByEventIdAndEnabledLowLatency(event.getEventId(), DateUtils.getCurrentDate(), rtmpUrl, streamProvider);
    }

    @Override
    public Boolean isActiveMainStageSessionByEventId(Long eventId, Date now) {
        return repo.isActiveMainStageSessionByEventId(eventId, now);
    }

    @Override
    public Optional<Session> findByMeetingId(String meetingId) {
        return repo.findByMeetingId(meetingId);
    }

    @Override
    public Page<Session> getAllCommandCenterSessionList(Event event, Pageable pageRequest, User user, boolean isAdminOrStaff) {
        List<EnumSessionFormat> enumSessionFormats = Arrays.asList(BREAK, OTHER, EXPO,MEET_UP);
        Page<Session> allSessionForHost = repo.getAllSessionForHost(event.getEventId(), enumSessionFormats, pageRequest,null);
        return allSessionForHost;
    }

    @Override
    public List<Session> getSessionByEventIds(List<Long> listOfEventId) {
        return repo.getSessionByEventIds(listOfEventId);
    }

    @Override
    public Session getFirstConcurrentSession(Long eventId, Date startTime, Date endTime) {
        return repo.findFirstByEventIdAndStartTimeAndEndTimeOrderByPositionAsc(eventId, startTime, endTime);
    }

    @Override
    public List<SessionIdNameTypeDto> getUpcomingSessionByEvent(Event event, Date currentDate) {
        return repo.getUpcomingSessionByEvent(event,currentDate);
    }

    @Override
    public List<SessionIdNameTypeDto> getUpcomingSessionByEventAndEndDateOfCurrentSession(Event event, Date currentDate) {
        return repo.getUpcomingSessionByEventAndEndDateOfCurrentSession(event,currentDate);
    }

    @Override
    public Session isSessionExistingOrUpcoming(PostSessionCallToActionTypeEnum postSessionCallToActionTypeEnum, String id) {
        return repo.isSessionExistingOrUpcoming(EnumSessionFormat.valueOf(postSessionCallToActionTypeEnum.name()), Long.valueOf(id), DateUtils.getCurrentDate());
    }

    @Override
    @Transactional
    public void setSessionStatusById(List<Long> sessionIds, EnumSessionStatus enumSessionStatus, Long eventId) {
        repo.setSessionStatusById(sessionIds,enumSessionStatus, eventId);
    }

    @Override
    public Page<Session> getCommandCenterAllSessionByEventId(Event event, SessionFilterCommandCenter filter, Pageable pageable) {
        List<Long> filterSessionIds = new ArrayList<>();
        String searchString = filter.getSearch();
        List<Long> tagOrTrackIds = filter.getTagOrTrackIds();
        if (!isEmpty(tagOrTrackIds) || isNotBlank(searchString)) {
            if (isEmpty(tagOrTrackIds)) {
                tagOrTrackIds = singletonList(0L);
            }

            if(StringUtils.isNotBlank(searchString)){
                searchString =  searchString.trim().replaceAll(" +", " ");
                filterSessionIds = repo.filterSession(event.getEventId(), tagOrTrackIds, searchString);
            }else{
                filterSessionIds = repo.filterSessionWithoutSearch(event.getEventId(), tagOrTrackIds);
            }
            if (filterSessionIds.isEmpty()) {
                return new PageImpl(emptyList());
            }
        }

        Set<Long> finalFilterSessionIds = (null != filterSessionIds && !filterSessionIds.isEmpty()) ? new HashSet<>(filterSessionIds) : null;

        List<Session> sessions = repo.getSessionByEvent(event.getEventId());
        List<Session> sessionsToReturn = filteredCommandCenterSessions(sessions, finalFilterSessionIds, filter.getSessionFormatList(), filter.getFilterDateList(), filter.getEnumCommandCenterFilter());
        return getPageable(sessionsToReturn, pageable);
    }

    private List<Session> filteredCommandCenterSessions(List<Session> sessions, Set<Long> finalFilterSessionIds, List<String> sessionFormatList, List<String> filterDate, EnumCommandCenterFilter enumCommandCenterFilter) {
        List<Session> sessionsToReturn = new ArrayList<>();
        sessions.forEach(session -> {
            boolean selected = isSelected(finalFilterSessionIds, enumCommandCenterFilter, filterDate, session);
            boolean selectSession = false;
            if (!CollectionUtils.isEmpty(sessionFormatList)) {
                for (String sessionFormat : sessionFormatList) {
                    if (isNotBlank(sessionFormat) && null != session.getFormat() && sessionFormat.equals(session.getFormat().name())) {
                        selectSession = true;
                        break;
                    }
                }
            } else {
                if (selected) {
                    sessionsToReturn.add(session);
                }
            }
            if (selected && selectSession) {
                sessionsToReturn.add(session);
            }
        });
        return sessionsToReturn.stream().filter(session -> !session.getFormat().equals(MEET_UP)).collect(Collectors.toList());
    }

    private boolean isSelected(Set<Long> finalFilterSessionIds, EnumCommandCenterFilter enumCommandCenterFilter, List<String> filterDateList, Session session) {
        boolean selected = false;
        if (finalFilterSessionIds != null) {
            if (finalFilterSessionIds.contains(session.getId())) {
                selected = canCommandCenterRecord(session, filterDateList, enumCommandCenterFilter);
            }
        } else {
            selected = canCommandCenterRecord(session, filterDateList, enumCommandCenterFilter);
        }
        return selected;
    }

    private boolean canCommandCenterRecord(Session session, List<String> filterDateList, EnumCommandCenterFilter enumCommandCenterFilter) {
        boolean selected = false;
        Date currentDate = new Date();
        if (null != enumCommandCenterFilter && enumCommandCenterFilter.equals(EnumCommandCenterFilter.UPCOMING)) {
            selected = canSelectUpcomingRecord(session, currentDate, filterDateList);
            return selected;
        } else if (null != enumCommandCenterFilter && enumCommandCenterFilter.equals(EnumCommandCenterFilter.LIVE)) {
            selected = canSelectLiveRecord(session, currentDate, filterDateList);
            return selected;
        } else if (null != enumCommandCenterFilter && enumCommandCenterFilter.equals(EnumCommandCenterFilter.DELAYED)) {
            selected = canSelectDelayedRecord(session, currentDate, filterDateList);
        } else if (null != enumCommandCenterFilter && enumCommandCenterFilter.equals(EnumCommandCenterFilter.ENDED)) {
            selected = canSelectEndedRecord(session, currentDate, filterDateList);
            return selected;
        } else {
            if (!CollectionUtils.isEmpty(filterDateList)) {
                for (String filterDate : filterDateList) {
                    CommandCenterDateFilterDto commandCenterDateFilter = getStartDateEndDateForDateFilter(filterDate);
                    if (commandCenterDateFilter.getStartDate() != null && commandCenterDateFilter.getEndDate() != null) {
                        selected = !Date.from(commandCenterDateFilter.getStartDate().toInstant()).after(session.getStartTime())
                                && !Date.from(commandCenterDateFilter.getEndDate().toInstant()).before(session.getStartTime());
                        if (selected){
                            break;
                        }
                    } else {
                        selected = true;
                    }
                }
            } else {
                selected = true;
            }
        }
        return selected;
    }

    private CommandCenterDateFilterDto getStartDateEndDateForDateFilter(String filterDate) {
        Date currentDate = new Date();
        OffsetDateTime startDate = isNotBlank(filterDate) ? DateUtils.getDateInUTCFromTimeZoneStr(filterDate) : null;
        OffsetDateTime endDate = isNotBlank(filterDate) ? startDate != null ? DateUtils.add24Hours1SecLess(startDate) : null : null;
        return new CommandCenterDateFilterDto(startDate, endDate, currentDate);
    }

    private boolean canSelectEndedRecord(Session session, Date currentDate, List<String> filterDateList) {
        if (!CollectionUtils.isEmpty(filterDateList)) {
            boolean select = false;
            for (String filterDate : filterDateList) {
                CommandCenterDateFilterDto commandCenterDateFilter = getStartDateEndDateForDateFilter(filterDate);
                if (commandCenterDateFilter.getStartDate() != null && commandCenterDateFilter.getEndDate() != null) {
                    select = !Date.from(commandCenterDateFilter.getStartDate().toInstant()).after(session.getStartTime())
                            && !Date.from(commandCenterDateFilter.getEndDate().toInstant()).before(session.getStartTime())
                            && !session.getEndTime().after(currentDate);
                    if (select) {
                        break;
                    }
                }
            }
            return select;
        }
        return session.getEndTime().before(currentDate);
    }

    private boolean canSelectDelayedRecord(Session session, Date currentDate, List<String> filterDateList) {
        if (null != session.getStreamProvider() && session.getStreamProvider().equals(StreamProvider.ACCELEVENTS)) {
            if (!CollectionUtils.isEmpty(filterDateList)) {
                boolean select = false;
                for (String filterDate : filterDateList) {
                    CommandCenterDateFilterDto commandCenterDateFilter = getStartDateEndDateForDateFilter(filterDate);
                    if (commandCenterDateFilter.getStartDate() != null && commandCenterDateFilter.getEndDate() != null) {
                        select = !Date.from(commandCenterDateFilter.getStartDate().toInstant()).after(session.getStartTime())
                                && !Date.from(commandCenterDateFilter.getEndDate().toInstant()).before(session.getStartTime())
                                && DateUtils.getAddSeconds(currentDate, -30).after(session.getStartTime())
                                && !session.getEndTime().before(currentDate)
                                && (null == session.getSessionStartStatus()
                                || session.getSessionStartStatus().equals(EnumSessionStartStatus.NULL)
                                || !session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED));
                        if (select) {
                            break;
                        }
                    }
                }
                return select;
            }
            return DateUtils.getAddSeconds(currentDate, -30).after(session.getStartTime())
                    && !session.getEndTime().before(currentDate)
                    && (null == session.getSessionStartStatus()
                    || session.getSessionStartStatus().equals(EnumSessionStartStatus.NULL)
                    || !session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED));

        }
        return false;
    }

    private boolean canSelectLiveRecord(Session session, Date currentDate, List<String> filterDateList) {  //NOSONAR
        if (!CollectionUtils.isEmpty(filterDateList)) {
            boolean select = false;
            for (String filterDate : filterDateList) {
                CommandCenterDateFilterDto commandCenterDateFilter = getStartDateEndDateForDateFilter(filterDate);
                if (commandCenterDateFilter.getStartDate() != null && commandCenterDateFilter.getEndDate() != null) {
                    if (null != session.getStreamProvider() && session.getStreamProvider().equals(StreamProvider.ACCELEVENTS)) {
                        select = !Date.from(commandCenterDateFilter.getStartDate().toInstant()).after(session.getStartTime())
                                && !Date.from(commandCenterDateFilter.getEndDate().toInstant()).before(session.getStartTime())
                                && session.getStartTime().after(currentDate)
                                && currentDate.before(session.getEndTime())
                                && currentDate.after(session.getStartTime())
                                && null != session.getSessionStartStatus()
                                && session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED);
                        if (select) {
                            break;
                        }
                    } else {
                        return !Date.from(commandCenterDateFilter.getStartDate().toInstant()).after(session.getStartTime())
                                && !Date.from(commandCenterDateFilter.getEndDate().toInstant()).before(session.getStartTime())
                                && session.getStartTime().after(currentDate)
                                && currentDate.before(session.getEndTime())
                                && currentDate.after(session.getStartTime());
                    }
                }
            }
            return select;
        }
        if (null != session.getStreamProvider() && session.getStreamProvider().equals(StreamProvider.ACCELEVENTS)) {
            return
                    currentDate.after(session.getStartTime())
                            && (null != session.getSessionStartStatus()
                            && session.getSessionStartStatus().equals(EnumSessionStartStatus.STARTED));

        } else {
            return currentDate.before(session.getEndTime())
                    && currentDate.after(session.getStartTime());
        }
    }

    private boolean canSelectUpcomingRecord(Session session, Date currentDate, List<String> filterDateList) {
        if (!CollectionUtils.isEmpty(filterDateList)) {
            boolean select = false;
            for (String filterDate : filterDateList) {
                CommandCenterDateFilterDto commandCenterDateFilter = getStartDateEndDateForDateFilter(filterDate);
                if (commandCenterDateFilter.getStartDate() != null && commandCenterDateFilter.getEndDate() != null) {
                    select = !Date.from(commandCenterDateFilter.getStartDate().toInstant()).after(session.getStartTime())
                            && !Date.from(commandCenterDateFilter.getEndDate().toInstant()).before(session.getStartTime())
                            && !session.getEndTime().before(currentDate) && !session.getStartTime().before(currentDate);
                    if (select) {
                        break;
                    }
                }
            }
            return select;
        }
        return session.getEndTime().after(currentDate) && session.getStartTime().after(currentDate);
    }

    @Override
    public String findTitleBySessionId(Long sessionId, Long eventId) {
        return repo.findTitleBySessionId(sessionId, eventId);
    }

    @Override
    public String findFormatBySessionId(Long sessionId, Long eventId) {
        return repo.findFormatBySessionId(sessionId, eventId);
    }

    @Override
    public long getSessionCountByEventIdAndSessionFormat(Long eventId,EnumSessionFormat sessionFormat) {
        return repo.findSessionCountBySessionFormat(eventId, sessionFormat);
    }

    @Override
    public boolean existsBySurveyIdAndEventId(Long surveyId, Long eventId) {
        return repo.existsBySurveyIdAndEventId(surveyId, eventId);
    }

    @Override
    public List<SessionListDto> getAllSessionByEventId(Long eventId) {
        return repo.getSessionIdAndTitleByEventId(eventId);
    }

    @Override
    public boolean isSessionPrivate(Long sessionId) {
        return repo.isSessionPrivate(sessionId);
    }

    @Override
    public long countByEventIdAndSessionStartTime(Date startTime, Long eventId, Date endTime) {
        log.info("countByEventIdAndSessionStartTime eventId {} startTime {} endTime {}", eventId, startTime, endTime);
        long count = repo.countByEventIdAndSessionStartTime(startTime, eventId, endTime);
        log.info("countByEventIdAndSessionStartTime eventId {} count {}", eventId, count);
        return count;
    }

    @Override
    public List<SessionBasicDetailsDTO> getAllSessionsByEventId(Long eventId){
        return repo.getAllSessionsByEventId(eventId);
    }

    @Override
    public List<Long> findSurveyIdBySessionIds(List<Long> sessionIds) {
        return repo.findSurveyIdBySessionIds(sessionIds);
    }

    @Override
    public Page<Session> getAllPublicSessionByEventId(Event event, Pageable pageable, SessionFilter filter) {
        List<Long> filterSessionIds = new ArrayList<>();
        String searchString = filter.getSearch();
        if (!isEmpty(filter.getTagOrTrackIds()) || isNotBlank(searchString) || !CollectionUtils.isEmpty(filter.getIds())) {
            if (isEmpty(filter.getTagOrTrackIds())) {
                filter.setTagOrTrackIds(singletonList(0L));
            }
            if (!CollectionUtils.isEmpty(filter.getIds())) {
                filterSessionIds = filter.getIds();
            } else {
                if (StringUtils.isNotBlank(searchString)) {
                    filterSessionIds = repo.filterSession(event.getEventId(), filter.getTagOrTrackIds(), searchString.trim());
                } else {
                    filterSessionIds = repo.filterSessionWithoutSearch(event.getEventId(), filter.getTagOrTrackIds());
                }
            }
            if (filterSessionIds.isEmpty()) {
                return new PageImpl(emptyList());
            }
        }

        Set<Long> finalFilterSessionIds = !filterSessionIds.isEmpty() ? new HashSet<>(filterSessionIds) : null;

        List<Session> sessions = repo.getSessionByEvent(event.getEventId());
        List<Session> sessionsToReturn;

        if (null != filter.getFilterDate()) {
            sessionsToReturn = filteredPublicCacheSessions(sessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), filter.getFilterDate(), filter.isShowPastAndUpcoming());
        } else {
            sessionsToReturn = filteredPublicCacheSessions(sessions, finalFilterSessionIds, filter.getPast(), filter.getSessionFormat(), null, filter.isShowPastAndUpcoming());
        }

        return getPageable(sessionsToReturn, PageRequest.of(0,Integer.MAX_VALUE));
    }

    private List<Session> filteredPublicCacheSessions(List<Session> sessions, Set<Long> finalFilterSessionIds, Boolean past, String sessionFormat, String filterDate, boolean showPastAndUpcoming) {
        List<Session> sessionsToReturn = new ArrayList<>();
        Date currentDate = new Date();
        OffsetDateTime startDate = isNotBlank(filterDate) ? DateUtils.getDateInUTCFromTimeZoneStr(filterDate) : null;
        OffsetDateTime endDate = (startDate != null) ? DateUtils.add24Hours1SecLess(startDate) : null;
        sessions.forEach(session -> {
            if ((isNotBlank(sessionFormat) && session.getFormat() != null && !sessionFormat.equals(session.getFormat().name())) ||
                    session.isHideSessionFromAttendees() || session.getSessionVisibilityType().equals(SessionVisibilityType.PRIVATE) || EnumSessionStatus.HIDDEN.equals(session.getStatus())) {
                return;
            }
            boolean selected = isSelected(finalFilterSessionIds, past, showPastAndUpcoming, currentDate, startDate, endDate, session);
            if (selected && !EnumSessionStatus.DRAFT.equals(session.getStatus())) {
                sessionsToReturn.add(session);
            }
        });
        return sessionsToReturn;

    }


    private List<Session> filteredCachePrivateSessions(List<Session> sessions, Set<Long> finalFilterSessionIds, Boolean past, String sessionFormat, String filterDate, boolean showPastAndUpcoming) {
        List<Session> sessionsToReturn = new ArrayList<>();
        Date currentDate = new Date();
        OffsetDateTime startDate = isNotBlank(filterDate) ? DateUtils.getDateInUTCFromTimeZoneStr(filterDate) : null;
        OffsetDateTime endDate = (startDate != null) ? DateUtils.add24Hours1SecLess(startDate) : null;
        sessions.forEach(session -> {
            if (isNotBlank(sessionFormat) && null != session.getFormat() && !sessionFormat.equals(session.getFormat().name())) {
                return;
            }
            boolean selected = isSelected(finalFilterSessionIds, past, showPastAndUpcoming, currentDate, startDate, endDate, session);
            if (selected && !EnumSessionStatus.DRAFT.equals(session.getStatus())) {
                sessionsToReturn.add(session);
            }
        });
        return sessionsToReturn;

    }

    @Override
    public List<SurveySessionsBasicDto> getSessionSurveyBasicDetailsByEvent(Long eventId){
        return repo.getSessionSurveyBasicDetailsByEvent(eventId);
    }

    @Override
    public List<Session> findAllByIdsAndEventId(List<Long> sessionIds, Long eventId) {
        return repo.findSessionsByIdsAndEventId(sessionIds, eventId);
    }

}