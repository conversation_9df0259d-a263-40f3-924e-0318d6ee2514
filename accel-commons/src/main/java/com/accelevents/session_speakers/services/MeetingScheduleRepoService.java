package com.accelevents.session_speakers.services;

import com.accelevents.common.dto.AttendeeBookedScheduleDto;
import com.accelevents.domain.enums.MeetingOrigin;
import com.accelevents.domain.enums.MeetingStatus;
import com.accelevents.domain.session_speakers.MeetingSchedule;
import com.accelevents.session_speakers.dto.IdCountDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface MeetingScheduleRepoService {

    List<Long> findSenderAndReceiverOfMeeting(Long meetingId, Long eventId);

    List<AttendeeBookedScheduleDto> getAllBookdedMeetingScheduleByUserIdAndStatusBookedFilterByMonthAndYear(List<Long> userIds, MeetingStatus status, Date startDate, Date endDate, Long eventId);

    MeetingSchedule save(MeetingSchedule meetingSchedule);

    void saveAll(List<MeetingSchedule> meetingSchedule);

    Boolean isMeetingScheduledBetweenDates(Long receiverUser, Date startTime, Date endTime, Long eventId);

    Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable);

    Boolean isMeetingScheduledBetweenDatesByNotIdAndUserIdList(Long id, List<Long> userList, Date startTime, Date endTime, Long eventId);

    MeetingSchedule getMeetingScheduleById(long id);

    MeetingSchedule findByIdAndEventId(Long scheduleId, Long eventId);

    void cancelMeetingSchedule(long id);

    Page<MeetingSchedule> getAllCreatedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status, List<MeetingOrigin> meetingOriginList, Pageable pageable);

    Page<MeetingSchedule> getAllRejectedMeetingScheduleByUserIdAndEventIdAndStatus(Long userId, long eventId, MeetingStatus status,List<MeetingOrigin> meetingOriginList, Pageable pageable);

    void deleteMeetingScheduleByUserId(Long userId);

    /**
     * Get total number of meeting booked for given eventId except given origin
     * @param eventId id of the event to fetch meeting book count
     * @param origin origin of meeting to not include in total meeting book count
     * @return
     */
    long getTotalMeetingBooked(Long eventId, MeetingOrigin origin);

    long getTotalMeetingBookedInExhibitor(Long eventId, MeetingOrigin origin, Long exhibitorId);

    long getTotalBookedMeetingOfUserInOrigin(Long eventId, Long userId, MeetingOrigin origin);

    void deleteMeetingScheduleByEventId(Long eventId);

    Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventIdAndStatusList(Long userId, long eventId, List<MeetingStatus> statusList, List<MeetingOrigin> meetingOriginList, Date startTime, Pageable pageable);

    Page<MeetingSchedule> getAllMeetingScheduleByUserIdAndEventId(Long userId, long eventId, List<MeetingOrigin> meetingOriginList, Pageable pageable);

    List<Object[]> getBookedMeetingByUserIds(Long eventId, List<Long> userIds);

    List<IdCountDto> getMeetingRequestSentByUserIds(Long eventId, List<Long> userIds);

    List<IdCountDto> getMeetingRequestReceivedByUserIds(Long eventId, List<Long> userIds);

    Page<MeetingSchedule> getAllMeetingSchedulesCreatedByAdmin(Long eventId, String meetingOrigin, Pageable pageable, String searchString);

    List<Object[]> getMeetingByEventIdAndStatus(String eventUrl, String status);
    boolean isPreScheduleMeetingScheduledForEventAndUser(Long eventId, Long userId);

    boolean isAnyMeetingBookedByUserAndEventAndOrigin(Long userId, Long eventId,List<MeetingOrigin> origin);

    List<MeetingSchedule> findScheduledMeetingByEventId(Long eventId);

    List<MeetingSchedule> findScheduledMeetingOfABooth(Long eventId, Long expoId, List<Long> receiverId,List<MeetingStatus> status,
                                                       List<String> location);

    Optional<MeetingSchedule> findByExhibitorIdAndIdAndEventId(Long expoId, Long meetingId, Long eventId);

    List<MeetingSchedule> findAcceptedScheduledMeetingByEventIdAndStatus(List<Long> eventId);
}
