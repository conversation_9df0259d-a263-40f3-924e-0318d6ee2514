package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.SessionLocation;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.session_speakers.dto.SessionLocationDTO;
import com.accelevents.session_speakers.dto.UploadSessionLocationResponseContainer;
import com.accelevents.session_speakers.repo.SessionLocationRepo;
import com.accelevents.session_speakers.services.SessionLocationService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.NumberUtils;
import com.opencsv.CSVReader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;
import static java.util.stream.Collectors.toList;

@Service
public class SessionLocationServiceImpl implements SessionLocationService {
    private static final Logger log = LoggerFactory.getLogger(SessionLocationServiceImpl.class);

    @Autowired
    SessionLocationRepo sessionLocationRepo;

    @Override
    @Transactional
    public Long addSessionLocation(SessionLocationDTO sessionLocationDTO, Event event, User user) {
        String name = sessionLocationDTO.getName();
        if(StringUtils.isNotBlank(name)){
            boolean isLocationExists = this.isLocationExistsByNameAndEventId(name,event.getEventId());
            if(isLocationExists){
                throw new NotAcceptableException(NotAcceptableException.LocationExceptionMsg.LOCATION_ALREADY_EXISTS);
            }
        }
        SessionLocation sessionLocation = sessionLocationDTO.createEntity(event);
        sessionLocation = sessionLocationRepo.save(sessionLocation);
        log.info("SessionLocationServiceImpl | addSessionLocation | SessionLocation {} added by user {}",sessionLocation.getId(),user.getUserId());
        return sessionLocation.getId();
    }

    @Override
    @Transactional
    public void updateSessionLocation(Long id,SessionLocationDTO sessionLocationDTO, Event event, User user) {
        SessionLocation oldSessionLocation =  sessionLocationRepo.findByIdAndEventId(id, event.getEventId());
        if(oldSessionLocation == null){
            throw new NotFoundException(NotFoundException.SessionLocationNotFound.SESSION_LOCATION_NOT_FOUND);
        }
        String oldLocationName = oldSessionLocation.getName();
        String newLocationName = sessionLocationDTO.getName();

        if(StringUtils.isNotBlank(newLocationName)){
            if(!oldLocationName.equals(newLocationName)){
                boolean isLocationExists = this.isLocationExistsByNameAndEventId(newLocationName,event.getEventId());
                if(isLocationExists){
                    throw new NotAcceptableException(NotAcceptableException.LocationExceptionMsg.LOCATION_ALREADY_EXISTS);
                }
            }
        }
        sessionLocationDTO.updateEntity(oldSessionLocation);
        sessionLocationRepo.save(oldSessionLocation);
        log.info("SessionLocationServiceImpl | updateSessionLocation | SessionLocation {} updated by user {}",oldSessionLocation.getId(),user.getUserId());
    }

    @Override
    @Transactional
    public void deleteSessionLocation(Long id, Event event, User user) {
        SessionLocation sessionLocation =  sessionLocationRepo.findByIdAndEventId(id, event.getEventId());
        if(sessionLocation == null){
            throw new NotFoundException(NotFoundException.SessionLocationNotFound.SESSION_LOCATION_NOT_FOUND);
        }
        sessionLocation.setRecordStatus(RecordStatus.DELETE);
        this.removeLocationIdFromSessions(id,event);
        sessionLocationRepo.save(sessionLocation);
        log.info("SessionLocationServiceImpl | deleteSessionLocation | SessionLocation {} deleted by user : {}",sessionLocation.getId(),user.getUserId());
    }


    @Override
    public DataTableResponse getAllSessionLocations(Event event, Pageable pageable, String searchString) {
        Page<SessionLocation> sessionLocationPage = sessionLocationRepo.getSessionLocations(event.getEventId(),searchString,pageable);

        List<Long> sessionLocationIds = sessionLocationPage.getContent().stream().map(SessionLocation::getId).collect(toList());
        List<Object[]> sessionToLocationMapping = this.countSessionsByLocationIds(event.getEventId(), sessionLocationIds);
        Map<Long,Long> locationIdToSessionCountMap = sessionToLocationMapping.stream().collect(Collectors.toMap(e -> (Long) e[0], e -> (Long) e[1]));

        List<SessionLocationDTO> sessionLocationDTOS =
                sessionLocationPage
                        .getContent()
                        .stream()
                        .map(e -> new SessionLocationDTO(
                                e.getId(),
                                e.getName(),
                                e.getSourceUrl(),
                                locationIdToSessionCountMap.getOrDefault(e.getId(),null)
                        ))
                        .collect(toList());

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(sessionLocationPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(sessionLocationPage.getContent().size());
        dataTableResponse.setData(sessionLocationDTOS);

        return  dataTableResponse;
    }

    @Override
    public Optional<List<String>> checkSessionLocationAvailability(SessionLocationDTO sessionLocationDTO, Event event) {
        Date sessionStartTime = getDateInUTC(sessionLocationDTO.getStartTime(), event.getEquivalentTimeZone());
        Date sessionEndTime = getDateInUTC(sessionLocationDTO.getEndTime(), event.getEquivalentTimeZone());

        List<String> sessionNameList = sessionLocationRepo.findBookedSessionNamesByLocationId(sessionLocationDTO.getId(), event.getEventId(),sessionStartTime, sessionEndTime);
        if(sessionNameList.isEmpty()){
            return Optional.empty();
        }
        log.info("SessionLocationServiceImpl | checkSessionLocationAvailability | Location {} already has a session between given time and date",sessionLocationDTO.getId());
        return Optional.of(sessionNameList);
    }

    @Override
    public List<UploadSessionLocationResponseContainer> importSessionLocationList(MultipartFile inputFile, Event event, User user) throws IOException {
        List<UploadSessionLocationResponseContainer> returnValue = new ArrayList<>();
        try (CSVReader cr = new CSVReader(new BufferedReader(new InputStreamReader(inputFile.getInputStream())))) {
            String[] header = cr.readNext();
            log.info("Request Received to Import/update Location for event {}, headers {}", event.getEventId(),  header);
            Map<String, SessionLocationDTO> sessionLocationMap = new LinkedHashMap<>();
            Map<String, SessionLocation> existingLocations = sessionLocationRepo.getSessionLocationByEventId(event.getEventId()).stream().collect(Collectors.toMap(SessionLocation::getName, e -> e, (old1, new1) -> new1));
            if ((null != header && header.length == 2 && isValidHeaders(header))) {
                String[] nextItem;
                while ((nextItem = cr.readNext()) != null) {
                    if (nextItem.length == 2) {
                        SessionLocationDTO sessionLocationDTO = new SessionLocationDTO(nextItem[0].trim(), nextItem[1].trim());
                        if(StringUtils.isEmpty(sessionLocationDTO.getName())){
                            returnValue.add(new UploadSessionLocationResponseContainer("Location is required to importing the location"));
                            continue;
                        }
                        if(sessionLocationMap.get(sessionLocationDTO.getName()) != null){
                            returnValue.add(new UploadSessionLocationResponseContainer("Duplicate Location found in the file"));
                            continue;
                        }
                        if(existingLocations.get(sessionLocationDTO.getName()) != null){
                            sessionLocationDTO.setId(existingLocations.get(sessionLocationDTO.getName()).getId());
                        }
                        sessionLocationMap.put(sessionLocationDTO.getName(), sessionLocationDTO);
                    }
                }
                List<SessionLocation> qualifiedSession = new ArrayList<>();
                if(!CollectionUtils.isEmpty(sessionLocationMap)){
                    sessionLocationMap.values().forEach(e -> {
                        SessionLocation sessionLocation = new SessionLocation();
                        if(NumberUtils.isNumberGreaterThanZero(e.getId())){
                            sessionLocation.setId(e.getId());
                        }
                        sessionLocation.setName(e.getName());
                        sessionLocation.setSourceUrl(e.getSourceUrl());
                        sessionLocation.setEventId(event.getEventId());
                        sessionLocation.setRecordStatus(RecordStatus.CREATE);
                        qualifiedSession.add(sessionLocation);
                    });
                    sessionLocationRepo.saveAll(qualifiedSession);
                }
                else{
                    throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.CAN_NOT_UPLOAD_EMPTY_FILE);
                }
            }
            else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_HEADER_NOT_CORRECT);
            }
        }
        return returnValue;
    }
    protected boolean isValidHeaders(String[] header) {
        return header[0].trim().equalsIgnoreCase(Constants.LOCATION)
                && header[1].trim().equalsIgnoreCase(Constants.SOURCE_URL);
    }

    @Override
    public void removeLocationIdFromSessions(Long locationId, Event event) {
        sessionLocationRepo.removeLocationIdFromSessions(locationId,event.getEventId());
    }

    @Override
    public boolean isLocationExistsByNameAndEventId(String name, Long eventId) {
        return sessionLocationRepo.isLocationExistsByNameAndEventId(name,eventId);
    }

    @Override
    public SessionLocationDTO getSessionLocationById(Long id, Event event) {
        SessionLocation sessionLocation = sessionLocationRepo.findByIdAndEventId(id,event.getEventId());
        if(sessionLocation == null){
            throw new NotFoundException(NotFoundException.SessionLocationNotFound.SESSION_LOCATION_NOT_FOUND);
        }
        return SessionLocationDTO.toDto(sessionLocation);
    }

    @Override
    public List<SessionLocation> getSessionLocationByEventId(Event event) {
        return sessionLocationRepo.getSessionLocationByEventId(event.getEventId());
    }

    @Override
    public List<Object[]> countSessionsByLocationIds(Long eventId, List<Long> locationIds) {
        return sessionLocationRepo.countSessionsByLocationIds(eventId,locationIds);
    }

    @Override
    public SessionLocation getSessionLocationById(Long id) {
        return sessionLocationRepo.findById(id).orElseThrow(() -> new NotFoundException(NotFoundException.SessionLocationNotFound.SESSION_LOCATION_NOT_FOUND));
    }

    @Override
    public List<SessionLocationDTO> getAllSessionLocationsByEventId(Long eventId) {
        return sessionLocationRepo.getSessionLocationByEventId(eventId).stream().map(SessionLocationDTO::toDto).collect(toList());
    }
}