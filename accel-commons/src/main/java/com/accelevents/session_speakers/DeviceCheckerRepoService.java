package com.accelevents.session_speakers;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.DeviceCheckerType;
import com.accelevents.domain.session_speakers.DeviceChecker;
import com.accelevents.session_speakers.dto.UserDeviceCheckedDto;

import java.util.List;

public interface DeviceCheckerRepoService {

    void save(<PERSON><PERSON><PERSON><PERSON><PERSON> deviceChecker);

    List<UserDeviceCheckedDto> userIdAndRunDeviceChecker(List<Long> userIds);

    String checkUserDeviceCheckerWithDeviceId(Long userId, Event eventId, String deviceId, DeviceCheckerType deviceCheckerType,String languageCode);
}
