package com.accelevents.session_speakers;


import com.accelevents.repositories.VirtualEventLoggingRepository;
import com.accelevents.session_speakers.services.VirtualEventLoggingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class VirtualEventLoggingServiceImpl implements VirtualEventLoggingService {

	@Autowired
	private VirtualEventLoggingRepository repository;


	@Override
	public List<Object[]> findAllByExhibitorId(Long exhibitorId){
		return repository.findUserDetailsAnalyticsByExpoId(exhibitorId);
	}

	@Override
	public long getExhibitorBoothViewed(long eventId) {
		return repository.getExhibitorBoothViewed(eventId);
	}

	@Override
	public Long getUserTotalExhibitorVisit(Long eventId, Long userId) {
		return repository.getUserTotalExhibitorVisit(eventId, userId);
	}

	@Override
	public Long getUserTotalDocumentDownload(Long eventId, Long userId) {
		return repository.getUserTotalDocumentDownload(eventId, userId);
	}

    @Override
    public List<Object[]> findAllByEventId(long eventId) {
        return repository.findUserDetailsAnalyticsByEventId(eventId);
    }

    @Transactional
    @Override
    public void deleteVirtualLoggingByEventId(long eventId) {
        repository.deleteByEventId(eventId);
    }


}
