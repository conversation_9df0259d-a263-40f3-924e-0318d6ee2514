package com.accelevents.session_speakers.util;

import com.google.gson.JsonObject;

import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

public class ThirdPartyUtils {

	public static String BASIC_AUTH(String key, String secret){
		return "Basic "+DatatypeConverter.printBase64Binary(
			(key +":"+secret).getBytes(StandardCharsets.UTF_8));
	}

	public static void main(String[] args) {

//		voxeet.consumerSecret=NmppN3BjNXZyY2l1cnNyZDhicmNlcXAwM3I=
//				voxeet.consumerKey=ZXNoNXI5NW8zMzI0aQ==l
		System.out.println(BASIC_AUTH("********************",
				"/dKr+ZBC8VhWHP9o3NABWjleAbdwA92CpbjQh9h6"));
	}

	public static String SingletonToJson(String value, String key) {
		JsonObject jsonObject = new JsonObject();
		jsonObject.addProperty(key, value);
		return jsonObject.toString();
	}
}
