package com.accelevents.session_speakers.util;

import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.services.impl.MuxServiceImpl;
import com.accelevents.services.impl.VoxeetServiceImpl;
import com.accelevents.session_speakers.dto.CaptionsDto;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class TestClass {

	public static void main(String[] args) throws JSONException {
		//stop();
		start();
//		create();
//		checkStatus();
		//checkGraphql();

		//test1();
	}


	public static void stop() {
		VoxeetServiceImpl voxeetService = new VoxeetServiceImpl("NmppN3BjNXZyY2l1cnNyZDhicmNlcXAwM3I=",
				"ZXNoNXI5NW8zMzI0aQ==",null);
		String confId = "252";

		voxeetService.stopConference(confId, null, null);
	}

	public static void start() {
		VoxeetServiceImpl voxeetService = new VoxeetServiceImpl("MWwzczNubG00cW9uNWFidDZjOWVoa3F0NHA=",
				"cGR0bG9tbTg2OHRs",null);
		String confId = "2589";
		Session session = new Session();
		session.setRtmpUrl("rtmp://**************:1935/session1");
		session.setStreamKey("live1");

		voxeetService.startConferenceWithRtmpUrl(session.getRtmpUrl()
						+ "/" + session.getStreamKey(),
				confId, null, null);
	}

	public static void create(){

		MuxServiceImpl muxService = new MuxServiceImpl(
				"D+XBlanK0lVJYc2ZBcBNMOoNiZfD9M/wUgRcP4gEtv+Zoiz5AWntaip2FjFf9Eqb/ADI6uKQPLF",
				"19200217-ec51-4d03-87fa-05f76d41cb82",null);
		muxService.createStreamKey("https://res.cloudinary.com/accelevents/image/fetch/c_fill_pad,dpr_2.0,g_auto,h_350,w_700/https://v2-s3-prod-accelevents.s3.amazonaws.com/bosebabesoverlayimage.png", "1",false, null, new CaptionsDto());
		muxService.createStreamKey(null, "TEST_1", new CaptionsDto());
	}

	public static void checkStatus(){

		MuxServiceImpl muxService = new MuxServiceImpl(
				"D+XBlanK0lVJYc2ZBcBNMOoNiZfD9M/wUgRcP4gEtv+Zoiz5AWntaip2FjFf9Eqb/ADI6uKQPLF",
				"19200217-ec51-4d03-87fa-05f76d41cb82",null);
		muxService.checkStreamStatus("UJgBxmOuYjVjNkAC74nTFyIduKMWNbA012ncI01cAwCPM");
	}


	public static void checkGraphql() throws JSONException {

		JSONObject requestData = new JSONObject();
		requestData.put("sessionId",1);
		requestData.put("eventId",2);
		requestData.put("format","MAIN_STAGE");
		requestData.put("playbackUrl","url");
		requestData.put("status","idle");

		JSONObject variable = new JSONObject();
		variable.put("createSessionStatusLogInput", requestData);

		JSONObject mainRequest = new JSONObject();
		mainRequest.put("operationName","createSessionStatusLog");
		mainRequest.put("variables",variable);
		mainRequest.put("query","mutation createSessionStatusLog($createSessionStatusLogInput: CreateSessionStatusLogInput!) { createSessionStatusLog(input: $createSessionStatusLogInput) { sessionId playbackUrl status } }");


		Map headers = new HashMap();
		headers.put("Content-Type", "application/json");
		headers.put("x-api-key", "da2-jln2lpufxzfv5mmpoizf22vrxi");

		try {
			HttpResponse<JsonNode> response = Unirest.post("" +
					"https://ywbm6uwanff3ho2jq2wysj56e4.appsync-api.us-east-1.amazonaws.com/graphql")
					.headers(headers)
					.body(mainRequest.toString())
					.asJson();
//			JSONObject jsonObject =(JSONObject) response.getBody().getObject().getString("data");
//			jsonObject.get("operationame")
		} catch (UnirestException e) {
			e.printStackTrace();
		}

	}

	public static void test1(){
		Event event = new Event();
		event.setEventId(2);
		Session session = new Session();
		session.setId(1);
		session.setEventId(event.getEventId());

//		new BroadcastGraphQLHandler("https://ywbm6uwanff3ho2jq2wysj56e4.appsync-api.us-east-1.amazonaws.com/graphql"
//		).handleCreateOrUpdateSessionStatusLogs(
//						"live",session
//		);
	}
}
