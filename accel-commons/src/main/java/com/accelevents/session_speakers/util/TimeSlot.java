package com.accelevents.session_speakers.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class TimeSlot {
	private Date beginDate;
	private Date endDate;

	public TimeSlot(Date beginDate, Date endDate) {
		this.beginDate = beginDate;
		this.endDate = endDate;
	}

	public boolean collidesWith(TimeSlot timeSlot) {

		if (timeSlot.beginDate.getTime() > beginDate.getTime()
				&& timeSlot.beginDate.getTime() < endDate.getTime()) {
			return true;
		}

		return timeSlot.endDate.getTime() > beginDate.getTime()
				&& timeSlot.endDate.getTime() < endDate.getTime();
	}

	public static TimeSlot createNewSlot(String beginDate, String endDate) {
		SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm");
		try {
			return new TimeSlot(format.parse(beginDate), format.parse(endDate));
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public String toString() {
		return "Begin Date = " + beginDate.toString() + ", End Date = " + endDate.toString();
	}
}
