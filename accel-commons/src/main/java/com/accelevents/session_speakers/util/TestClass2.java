package com.accelevents.session_speakers.util;

import com.accelevents.services.impl.MuxServiceImpl;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class TestClass2 {

	public static void main(String[] args) {
		Map map = new HashMap();
		map.put("61836660-40c1-b041-a76f-03a987b2049a",284);
		map.put("310ae073-c945-014e-cf8d-fd83c823e74c",291);
		map.put("22b9fb6e-524d-b1bb-7d98-6b5a5deefe40",293);
		map.put("1446e4fb-4122-fc34-0c5f-331c2ea17171",294);
		map.put("eb30beca-c9d2-ac60-0c74-5f51396b688d",297);
		map.put("35ff481c-2ec0-3f2e-76ff-bafa584e8771",306);
		map.put("0f53beec-13cb-4085-639e-55d056137374",308);
		map.put("687dc991-7db6-3d68-d2b2-2d0d797692a0",319);
		map.put("aafa90a1-6e17-c845-e177-b05755fec251",345);
		map.put("9fd51fd2-b5d7-6640-f57b-8fb9751e64bf",346);
		map.put("4e6d8c4d-1566-c180-09d9-e4aebb0fbc46",347);
		map.put("c394dbf7-473f-645f-e450-e58c25ea5111",348);
		map.put("e31f245f-4888-c170-07f3-39b68b1fd8e6",351);
		map.put("8aa04ef8-22d4-6df5-561a-fb3d308edb72",352);
		map.put("bd92723c-df7f-bba2-b967-a09dfb6d83cf",356);
		map.put("e2be19ae-5a07-6157-447b-da870d264ddb",357);
		map.put("f5fff2dd-6916-4c23-0027-97de79ee09aa",358);
		map.put("d98adf40-1767-6d94-a814-6d845f43d9f0",360);
		map.put("9ace4de3-b5ea-8d73-96e5-18f3c2840f7d",361);
		map.put("002ddca7-83d1-bb15-69cf-7d796d843317",362);
		map.put("7bd3234b-fed8-8c94-0346-bcc04ad3a268",363);
		map.put("30633cf9-0cf6-ff5d-e84c-f4ba1c6994cf",365);
		map.put("2189a1cc-c3fe-83fb-9e01-c86d7080e9ab",367);
		map.put("c1617cf5-08d1-a91a-d8ae-23a3c1e6b2eb",368);
		map.put("1cb308e5-784e-065c-6116-bb24c51b6fe5",369);
		map.put("a432ac54-6bc5-47f7-6da0-68e630c11f1e",373);
		map.put("30815c32-d6e8-b91d-1ae7-55855b8392d9",379);
		map.put("b9f6ca22-4f86-ae7f-516e-7c5fbe0e24d4",380);
		map.put("2d0d14a6-d79d-c6b9-7140-9cf70e884df1",381);
		map.put("6d48c1d2-1541-f155-707e-0c2b299054dc",387);
		map.put("c38da4ca-18a9-3ebd-93fa-2b5c64b1899a",391);
		map.put("b29a92ea-6125-dc1e-60a8-92f13f4d9aef",392);
		map.put("c0a5f921-a2f3-8810-ba4e-34eb438686a0",410);
		map.put("a17949c1-6841-139f-0ff5-2b52a8eb53c9",415);
		map.put("212f5caa-1744-7480-0737-7ed0130ef67a",416);
		map.put("d346be09-37bd-5261-920f-0391c4fd6b16",417);
		map.put("137b855c-353b-34fd-0a6e-cf5d84eca5bc",418);
		map.put("24a6cc39-3b9d-00e0-6eaf-bfb6ba3c2dc5",444);
		map.put("55e24e88-29df-726e-aef5-cf59ea2691e5",448);
		map.put("4bc95e70-692a-3f0b-1a2f-5ff8130cb482",450);
		map.put("29ae6e17-3fc9-9a65-5c72-6abf6e8f96f6",451);
		map.put("e347a6af-b29b-9134-5f2e-ffbac7a15e42",452);
		map.put("279d91a2-20af-3551-846e-4c9c958796ec",453);
		map.put("288a60b7-0de0-6d89-d652-c95af6e4dc2e",455);
		map.put("07ea97be-b4ea-61fd-a986-49ad1a6193d3",456);
		map.put("e4f4be58-7250-d024-b384-fc0d74fdb073",457);
		map.put("ca2777c8-683c-396f-73d1-9678981c9621",458);
		map.put("594b3dc0-3e78-cd84-acd9-f0be85c94770",459);
		map.put("c2a46237-4446-caaf-f2f3-67f2139b0e37",460);
		map.put("83755f95-b663-d113-9c90-ba1d76c7d213",485);
		map.put("572c260f-3243-2006-ef77-4363f7a24d4b",487);
		map.put("4e6d6f68-a652-7406-2a36-065356624efd",488);
		map.put("66f16a69-65e0-4e34-da20-08ae9a093ab9",490);
		map.put("60103e8b-2958-72fa-8391-f3a9ec2f4528",491);
		map.put("83a965c5-8ee6-d7c5-1c39-f95b1de953c5",499);
		map.put("99316a8e-873a-75ca-0688-8e1566efab07",523);
		map.put("fea8fa0c-a4f2-ee75-cbbd-e7c8855267cd",531);
		map.put("ac064cdc-97a5-575d-4bb5-e9bf852ecc84",538);
		map.put("205605d1-95da-db09-5ce3-2adf2413e2be",542);
		map.put("04b022b2-1fb6-2df6-4765-d57573b0f3d2",559);
		map.put("35d0bb1e-e380-7aed-c497-ae9def4bb400",593);
		map.put("b84583d3-f191-8356-c6f9-ac27030d6618",596);
		map.put("c9b1ea0f-ee59-4826-2e64-bccd797e7e71",597);
		map.put("9ae4b8e6-6207-de09-ef3e-bb28f355b0dd",598);
		map.put("b3597313-c020-c6f8-ce6a-b951d7a619f5",599);
		map.put("404c7929-3e04-ec32-9234-69623d5cc79f",600);
		map.put("b9e8923e-00e2-27dc-a6b7-7678aa6175e6",601);
		map.put("7f64abd5-0b4c-9198-388f-65145ea99bc6",602);
		map.put("f404897a-0868-a95f-da34-05fb1191a14d",605);
		map.put("49dc0849-b80b-0137-7303-142034af42a1",606);
		map.put("46a076bf-ae5f-6239-9f4a-ad5ffd70feb5",607);
		map.put("006d9cf7-66ed-6aef-98fe-f7948a28c98f",608);
		map.put("26a1b3d6-8f95-7b13-ba1b-706ed0a970a4",610);
		map.put("fabb4a3e-02c7-d5a4-337f-71f4056a33f1",626);
		map.put("43ce785a-a8b6-3a30-f889-7723aeac510e",627);
		map.put("6c6f657e-b774-743a-b8a3-47c1e99743f0",628);
		map.put("a4f4b9b6-c16e-3f4b-6ba4-59e1cdae8108",638);
		map.put("b949b300-94d1-48bb-b38f-f662812ff8c4",642);
		map.put("0f91dfc3-cddb-c603-9ea5-804ba3ac454a",643);
		map.put("487dfcec-5f7c-58ad-cc4b-d0ee372c12ba",647);
		map.put("4a2cb514-8788-edff-2d47-34d54e5c59c8",649);

		Map map1 = new HashMap();

		MuxServiceImpl muxService = new MuxServiceImpl(
				"D+XBlanK0lVJYc2ZBcBNMOoNiZfD9M/wUgRcP4gEtv+Zoiz5AWntaip2FjFf9Eqb/ADI6uKQPLF",
				"19200217-ec51-4d03-87fa-05f76d41cb82",null);
		boolean notFound = false;
		int i =0;
		while (!notFound){
			Map liveStreamMap = muxService.getAllLiveStreams(i);
			if(liveStreamMap == null || liveStreamMap.isEmpty()){
				notFound = true;
			} else {
				for(Map.Entry entry : (Set<Map.Entry<String,String>>)liveStreamMap.entrySet()){
					String streamKey = (String) entry.getKey();
					if(map.containsKey(streamKey)){
						map1.put(streamKey, entry.getValue());
					}
				}
			}
			i++;
		}

		for(Map.Entry entry : (Set<Map.Entry<String,String>>)map1.entrySet()){
			System.out.println("UPDATE Sessions SET live_stream_id="+entry.getValue() +" WHERE id="+map.get(entry.getKey()));
		}
	}
}
