package com.accelevents.registration.approval.services;

import com.accelevents.common.dto.UserApplicationDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderAttributes;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.registration.approval.domain.RegistrationRequest;
import com.accelevents.registration.approval.dto.*;
import com.accelevents.ticketing.dto.AttendeeAttributeWithTypeAndTableDto;
import com.accelevents.ticketing.dto.RegistrationRequestResponseDTO;
import com.accelevents.ticketing.dto.SessionRegistrationRequestBasicDetailDto;
import com.squareup.square.exceptions.ApiException;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.bind.JAXBException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

public interface RegistrationRequestsService {

    RegistrationRequestServiceHandler getRegistrationRequestServiceHandler(RegistrationRequestType registrationRequestType);

    RegistrationRequestServiceHandler getRegistrationRequestServiceHandler(RegistrationAttributeType registrationAttributeType);

    RegistrationRequestResponseDTO createRegistrationRequest(Event event, RegistrationRequestType registrationRequestType, RegistrationRequestDto registrationRequestDto, User user, Long recurringEventId, Boolean isWithoutLogin, boolean postProcessRequired);

    List<RegistrationRequest> getRegistrationRequestsByRegistrationType(Event event, Long recurringEventId, RegistrationRequestType registrationRequestType, User user);

    List<RegistrationRequest> getRegistrationRequestsExceptDeniedStatus(Event event, Long recurringEventId, RegistrationRequestType registrationRequestType, User user);

    DataTableResponse getRegistrationRequest(Event event, User loggedInUser, Long recurringEventId, RegistrationRequestType type, List<RegistrationRequestStatus> status,
                                             int page, int size, String sortColumn, boolean isAsc, String searchString);

    RegistrationRequestApprovalResponseDTO updateRegistrationRequest(Event event, User user, List<Long> requestIds, RegistrationRequestStatus status);

    @Transactional(rollbackFor = { Exception.class })
    void updateRegistrationRequestForResendCheckoutLink(Event event, User user, List<Long> requestIds);

    @Transactional(rollbackFor = { Exception.class })
    void resendCheckoutLink(Event event, User user);

    @Transactional(rollbackFor = { Exception.class })
    void resendCheckoutLink(Event event, Long requestId);

    void updateRegistrationRequestNote(Event event, User user, Long id, String note);

    RegistrationRequest getRegistrationRequestsOrThrowError(Event event, Long id);

    List<RegistrationRequestStatusChangeDto> getRegistrationRequestHistory(Event event, Long id);

    void deleteRegistrationRequest(Event event, User user, List<Long> requestIds);

    void downloadRegistrationRequests(Event event, PrintWriter writer, List<Long> requestIds) throws JAXBException;

    RegistrationDisplayAttributesDto getRegistrantDetails(long requestId, long recurringEventId, Event event) throws JAXBException;

    List<RegistrantBasicDetailDto> getRegistrants(Event event, RegistrationRequestType type);

    TicketDisplayPageDto getTicketTypesDetailsForRegistrationRequestListCheckout(Event event, String decode, Long recurringEventId);

    boolean getAutoSendEmailOrNot(Event event, RegistrationRequestStatus registrationRequestStatus, RegistrationRequestType registrationRequestType);

    AttendeeAttributeWithTypeAndTableDto getFormAttributes(Event event, Long recurringEventId, RegistrationAttributeType type);

    boolean isRegistrationRequestExists(Event event, Long recurringEventId, RegistrationRequestType type);

    AnalyticsArea getAreaFromType(RegistrationRequestType requestType);

    @Transactional(rollbackFor = { Exception.class })
    void generateAndSendCheckoutListForRegistrationRequest(Long regRequestId, Event event);

    void checkRequestCheckoutExpiration();

    Long getCountOfTicketingTypes(long ticketTypeId);

    List<RegistrationRequest> findByEventIdAndRequestByUserId(Long eventId, User user);
    void changeStatusOfRegistrationRequest(Event event, User user);

    void createOrUpdateHolderAttributeMapping(Event event, RegistrationApprovalFieldHolderAttributeMappingDto mappingOfHolderAttributeDtos);

    void approveRegistrationRequestAndCreateOrder(User user, Event event, Long requestId, boolean isAnyPaidTicket, String couponCode);

    void updateRegistrantPurchaserDetails(Long requestId, AttendeeAttributeValueDto attendeeAttributesDto, Event event, User user, Long recurringEventId);

    List<NameValueDto> getAllDynamicMergeTagsListForApproval(Event event, Long recurringEventId, ConfirmationApprovalType confirmationApprovalType);

    RegistrationResponseDto getRegistrationRequestDto(TicketHolderAttributes ticketHolderAttributes);

    void updateEventSpeakerSettings(Event event, User user, SessionApprovalSettingDto sessionApprovalSettingDto);

    SessionApprovalSettingDto getSessionFormSettings(Event event);

    void deleteSpeakerFromRegistration(Long requestId, String email, Event event, User user);

    List<SessionRegistrationRequestBasicDetailDto> getSessionRegistrationRequestBasicDetails(Event event);

    Map<Long, RegistrationDisplayAttributesDto> getSessionRegistrationRequestDetailMap(Event event, List<RegistrationRequest> registrationRequestList);

    List<UserApplicationDto> getRegistrationApplicationByUser(User user);

    void downloadAllRegistrationRequests(Event event, User user, PrintWriter writer,AnalyticsArea area);

    Map<Long,Long> countByEventAndTrackingLinkIds(List<Long> trackingLinkIds, Long eventID);

    void downloadAttendeeRequestHolderBuyerCSV(Event event, User user, PrintWriter writer);

    void removeDiscountCodeFromRegistrationRequest(Long requestId, Event event, Long userId);
}