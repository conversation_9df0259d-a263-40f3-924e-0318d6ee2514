package com.accelevents.registration.approval.services.impl;

import com.accelevents.auction.dto.ExhibitorDto;
import com.accelevents.column.selection.ColumnSelectionConstants;
import com.accelevents.column.selection.dto.ColumnDto;
import com.accelevents.column.selection.dto.UserColumnSelectionDto;
import com.accelevents.column.selection.services.AnalyticsUserColumnSelectionService;
import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.exhibitors.Exhibitor;
import com.accelevents.domain.session_speakers.CustomFormAttributeData;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.dto.*;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.StripeUtil;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.registration.approval.domain.*;
import com.accelevents.registration.approval.dto.*;
import com.accelevents.registration.approval.repositories.*;
import com.accelevents.registration.approval.services.RegistrationApprovalEmailService;
import com.accelevents.registration.approval.services.RegistrationAttributeService;
import com.accelevents.registration.approval.services.RegistrationRequestServiceHandler;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.repositories.*;
import com.accelevents.repositories.require.attributes.TicketHolderAttributesRepository;
import com.accelevents.repositories.require.attributes.TicketRequiresAttributesRepo;
import com.accelevents.ro.audience.service.ROAnalyticsUserColumnSelectionService;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.session.ROSessionApprovalSettingService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.impl.TicketingPurchaseServiceImpl;
import com.accelevents.services.repo.helper.ChangeHistoryRepoService;
import com.accelevents.session_speakers.RegistrationApprovalSpeakerDto;
import com.accelevents.session_speakers.dto.CustomAttributeDetailsDto;
import com.accelevents.session_speakers.dto.CustomAttributesResponseDto;
import com.accelevents.session_speakers.dto.SessionDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.services.*;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.*;
import com.google.gson.Gson;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import com.twilio.rest.lookups.v1.PhoneNumber;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.domain.enums.RegistrationAttributeType.SPEAKER_DETAILS;
import static com.accelevents.domain.enums.RegistrationAttributeType.SPEAKER_INFO;
import static com.accelevents.dto.TicketHolderHelper.isAttributeFirstnameLastnameOrEmail;
import static com.accelevents.dto.TicketHolderHelper.isAttributeMandatoryForTicketingType;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.*;
import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;
import static org.springframework.util.CollectionUtils.isEmpty;

@Service
public class RegistrationRequestsServiceImpl implements RegistrationRequestsService {

    private static final Logger log = LoggerFactory.getLogger(RegistrationRequestsServiceImpl.class);
    private static final Logger auditLogger = LoggerFactory.getLogger("audit-logger");

    @Autowired
    private RegistrationRequestRepository registrationRequestRepository;
    @Autowired
    private  RegistrationRequestHolderDetailsRepository registrationRequestHolderDetailsRepository;
    @Autowired
    private TicketHolderAttributesRepository ticketHolderAttributesRepository;
    @Autowired
    @Lazy
    private TicketingPurchaseServiceImpl ticketingPurchaseService;
    @Autowired
    private ChangeHistoryRepoService changeHistoryRepoService;
    @Autowired
    private DownloadService downloadService;
    @Autowired
    private RegistrationIntroductoryConfigRepository registrationIntroductoryConfigRepository;
    @Autowired
    private RegistrationApprovalEmailService registrationApprovalEmailService;
    @Autowired
    private RegistrationAttributeService registrationAttributeService;
    @Autowired
    private TicketHolderAttributesService ticketHolderAttributesService;
    @Autowired
    private TwilioPhoneNumberValidateService twilioPhoneNumberValidateService;
    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private RegistrationRequestTicketRepository registrationRequestTicketRepository;
    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private SalesTaxService salesTaxService;
    @Autowired
    private StripeService stripeService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private VatTaxService vatTaxService;
    @Autowired
    private TicketingRepository ticketingRepository;
    @Autowired
    private SeatsIoService seatsIoService;
    @Autowired
    private TicketingTypeService ticketingTypeService;
    @Autowired
    private SeatingCategoryService seatingCategoryService;
    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;
    private RegistrationRequestServiceHandler registrationRequestServiceHandler;
    @Autowired @Qualifier("speakerRegRequestService")
    private RegistrationRequestServiceHandler speakerRegistrationRequestsService;
    @Autowired @Qualifier("attendeeRegRequestService")
    private RegistrationRequestServiceHandler attendeeRegistrationRequestsService;
    @Autowired @Qualifier("expoRegRequestService")
    private RegistrationRequestServiceHandler expoRegistrationRequestsService;
    @Autowired @Qualifier("reviewerRegRequestService")
    private RegistrationRequestServiceHandler reviewerRegistrationRequestsService;
    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Autowired
    private RegistrationAttributeRepository registrationAttributeRepository;
    @Autowired
    private AnalyticsUserColumnSelectionService columnSelectionService;
    @Autowired
    private ROAnalyticsUserColumnSelectionService roAnalyticsUserColumnSelectionService;
    @Autowired
    private ExhibitorService exhibitorService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Value("${cloud.aws.s3.bucket.ticketBuyerUploads.url}")
    private String uploadsUrl;
    @Value("${image.prefix}")
    private String imagePrefix;
    @Autowired
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Autowired
    private EventTicketsService eventTicketsService;
    @Autowired
    private RegistrationApprovalEmailRepository registrationApprovalEmailRepository;
    @Autowired
    private TicketRequiresAttributesRepo ticketRequiresAttributesRepo;

    @Autowired
    private  TicketingService ticketingService;

    @Autowired
    private SpeakerService speakerService;

    @Autowired
    private SessionService sessionService;

    @Autowired
    private VirtualEventService virtualEventService;

    @Autowired
    private SessionSpeakerService sessionSpeakerService;

    @Autowired
    private SpeakerHelperService speakerHelperService;

    @Autowired
    private EventLevelSettingService eventLevelSettingService;
    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;

    @Autowired
    private CustomFormAttributeService customFormAttributeService;

    @Autowired
    private ReviewerFormResponseRepository reviewerFormResponseRepository;

    @Autowired
    private ReviewerFormQuestionRepository reviewerFormQuestionRepository;

    @Autowired
    private ReviewerRepository reviewerRepository;

    @Autowired
    private ReviewerService reviewerService;

    @Autowired
    private ReviewProcessService reviewProcessService;

    @Autowired
    private FormRuleEngineService formRuleEngineService;

    @Autowired
    private CustomFormAttributeDataRepository customFormAttributeDataRepository;

    @Autowired
    private EventDesignDetailService eventDesignDetailService;

    @Autowired
    private ReviewProcessRegistrationMappingRepository reviewProcessRegistrationMappingRepository;

    @Autowired
    private AnalyticsUserColumnSelectionService userColumnSelectionService;

    @Autowired
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;

    @Autowired
    private ROSessionApprovalSettingService roSessionApprovalSettingService;

    @Autowired
    private SessionApprovalSettingService sessionApprovalSettingService;

    @Autowired
    private TrackingLinksService trackingLinksService;

    @Autowired
    private RegistrationRequestAndStripeCustomerMappingService registrationRequestAndStripeCustomerMappingService;

    @Autowired
    private ROStripeService roStripeService;

    @Autowired
    private TicketingCouponService ticketingCouponService;

    @Autowired
    private TicketingCouponCodeService ticketingCouponCodeService;

    @Autowired
    private TicketingDisplayService ticketingDisplayService;

    private static final int TIME_TO_RESPOND = 4320;

    private static final List<String> DEFAULT_BUYER_ATTR_NAMES = Arrays.asList(STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME, EMAIL);

    private EnumMap<RegistrationRequestType, RegistrationRequestServiceHandler> servicesByCode;

    private static final Pattern PATTERN_FOR_NUMBER = Pattern.compile("-?\\d*\\.?\\d+");

    @PostConstruct
    private void postConstruct() {
        servicesByCode = new EnumMap<>(RegistrationRequestType.class);
        servicesByCode.put(RegistrationRequestType.ATTENDEE, attendeeRegistrationRequestsService);
        servicesByCode.put(RegistrationRequestType.SPEAKER, speakerRegistrationRequestsService);
        servicesByCode.put(RegistrationRequestType.EXPO, expoRegistrationRequestsService);
        servicesByCode.put(RegistrationRequestType.REVIEWER, reviewerRegistrationRequestsService);
    }

    @Override
    public RegistrationRequestServiceHandler getRegistrationRequestServiceHandler(RegistrationRequestType registrationRequestType) {
        return servicesByCode.get(registrationRequestType);
    }

    @Override
    public RegistrationRequestServiceHandler getRegistrationRequestServiceHandler(RegistrationAttributeType registrationAttributeType) {
        EnumMap<RegistrationAttributeType, RegistrationRequestType> regAttrTypes = new EnumMap<>(RegistrationAttributeType.class);
        regAttrTypes.put(RegistrationAttributeType.ATTENDEE, RegistrationRequestType.ATTENDEE);
        regAttrTypes.put(SPEAKER_INFO, RegistrationRequestType.SPEAKER);
        regAttrTypes.put(RegistrationAttributeType.SPEAKER_DETAILS, RegistrationRequestType.SPEAKER);
        regAttrTypes.put(RegistrationAttributeType.EXPO_INFO, RegistrationRequestType.EXPO);
        regAttrTypes.put(RegistrationAttributeType.BOOTH_DETAILS, RegistrationRequestType.EXPO);
        regAttrTypes.put(RegistrationAttributeType.REVIEWER_INFO, RegistrationRequestType.REVIEWER);

        return servicesByCode.get(regAttrTypes.get(registrationAttributeType));
    }

    @Override
    public RegistrationRequestResponseDTO createRegistrationRequest(Event event, RegistrationRequestType registrationRequestType,
                                          RegistrationRequestDto registrationRequestDto, User user, Long recurringEventId, Boolean isWithoutLogin, boolean postProcessRequired) {
        log.info("Received request to register new attendee: {} ", registrationRequestDto);
        RegistrationRequestResponseDTO registrationRequestResponseDTO = new RegistrationRequestResponseDTO();
        Optional<RegistrationRequest>  registrationRequestOptional = registrationRequestRepository.findByEventAndId(event,registrationRequestDto.getRequestId());
        if(registrationRequestOptional.isPresent()) {
            RegistrationRequest registrationRequest = registrationRequestOptional.get();
            registrationRequestResponseDTO = updateExistingRegistrationRequest(event,registrationRequestType,registrationRequestDto,user, recurringEventId, isWithoutLogin, registrationRequest);
            registrationRequestResponseDTO.setRegistrationRequestId(registrationRequest.getId());
            return registrationRequestResponseDTO;
        } else {
            if (RegistrationRequestType.SPEAKER.equals(registrationRequestType) && !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                formRuleEngineService.validateFormRulesOfSessionApproval(event, registrationRequestDto.getSessionDetail());
            }else if(RegistrationRequestType.ATTENDEE.equals(registrationRequestType)){
                formRuleEngineService.validateFormRulesOfHolderRegistration(event, registrationRequestDto.getAttendees(), recurringEventId);
                formRuleEngineService.validateFormRulesOfBuyerRegistration(event,registrationRequestDto.getAttributes(),recurringEventId);
            }

            TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();

            if (registrationRequestDto != null && (!CollectionUtils.isEmpty(registrationRequestDto.getAttributes()) || RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus()))) {
                for (AttributeKeyValueDto attributeKeyValueDto : registrationRequestDto.getAttributes()) {
                    if (attributeKeyValueDto.getKey().equalsIgnoreCase(BIRTHDAY) && StringUtils.isNotEmpty(attributeKeyValueDto.getValue()) && !isValidDateFormat(attributeKeyValueDto.getValue())) {
                        processForBirthDateTypeAttribute(attributeKeyValueDto, attributeKeyValueDto.getValue());
                    }
                    if (!RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                        ticketingPurchaseService.validateHolderBookingData(attributeKeyValueDto);
                    }
                }

                Optional<AttributeKeyValueDto> countryCodeOptional = registrationRequestDto.getAttributes().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(COUNTRY_CODE_KEY)).findFirst();
                Optional<AttributeKeyValueDto> phoneNumberOptional = registrationRequestDto.getAttributes().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(PHONE_NUMBER_KEY)).findFirst();
                if (countryCodeOptional.isPresent() && phoneNumberOptional.isPresent() &&
                        !twilioPhoneNumberValidateService.isValidPhoneNumber(countryCodeOptional.get().getValue(), phoneNumberOptional.get().getValue())) {
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PHONE_NUMBER);
                }
                User regUser = null;
                if (!CollectionUtils.isEmpty(registrationRequestDto.getAttributes())) {
                    Optional<AttributeKeyValueDto> emailOpt = registrationRequestDto.getAttributes().stream()
                            .filter(e -> (EMAIL_ADDRESS.equalsIgnoreCase(e.getKey()) || EMAIL.equalsIgnoreCase(e.getKey()))
                                    && e.getValue() != null && !e.getValue().trim().isEmpty()).findFirst();
                    if (emailOpt.isPresent()) {
                        regUser = getOrCreateUser(event, registrationRequestDto);
                        raiseErrorIfRequestIsAlreadyExists(event, recurringEventId, registrationRequestType, registrationRequestDto, regUser);
                        addUserPhone(regUser, registrationRequestDto.getAttributes());
                    }
                }
                Map hashMap = new HashMap();
                try {
                    hashMap.put(TICKETING.ATTRIBUTES, this.listToMap(registrationRequestDto.getAttributes()));
                    hashMap.put(TICKETING.QUESTIONS, this.listToMap(registrationRequestDto.getQuestions()));
                    hashMap.put(TICKETING.NESTEDQUESTIONS, (!CollectionUtils.isEmpty(registrationRequestDto.getNestedQuestions()) ? registrationRequestDto.getNestedQuestions() : null));
                    if (RegistrationRequestType.EXPO.equals(registrationRequestType)) {
                        hashMap.put(BOOTH_DETAIL, this.listToMap(registrationRequestDto.getBoothDetail()));
                    }
                    if (RegistrationRequestType.SPEAKER.equals(registrationRequestType)) {
                        hashMap.put(SESSION_DETAIL, this.listToMap(registrationRequestDto.getSessionDetail()));
                        hashMap.put(SPEAKER_DETAIL, this.mapSpeaker(registrationRequestDto.getSpeakers()));
                    } else if (RegistrationRequestType.REVIEWER.equals(registrationRequestType)) {
                        hashMap.put(REVIEWER_DETAIL, this.listToMap(registrationRequestDto.getReviewerDetail()));
                    }
                    ticketHolderAttributes.setJsonValue(new Gson().toJson(hashMap, Map.class));
                    ticketHolderAttributes = ticketHolderAttributesRepository.save(ticketHolderAttributes);
                } catch (Exception e) {
                    log.error("Error while converting to json : {}, exception: {}", registrationRequestDto, e.getMessage());
                }
                if (Boolean.TRUE.equals(isWithoutLogin) && !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                    UserSignupDto userSignupDto = new UserSignupDto();
                    List<AttributeKeyValueDto> listOfKeyValue = registrationRequestDto.getAttributes();
                    AttributeKeyValueDto firstNameDto = listOfKeyValue.get(0);
                    if (Constants.STRING_FIRST_SPACE_NAME.equalsIgnoreCase(firstNameDto.getKey())) {
                        userSignupDto.setFirstName(firstNameDto.getValue());
                    }
                    AttributeKeyValueDto lastNameDto = listOfKeyValue.get(1);
                    if (Constants.STRING_LAST_SPACE_NAME.equalsIgnoreCase(lastNameDto.getKey())) {
                        userSignupDto.setLastName(lastNameDto.getValue());
                    }
                    AttributeKeyValueDto emailDto = listOfKeyValue.get(2);
                    if (Constants.EMAIL_ADDRESS.equalsIgnoreCase(emailDto.getKey())) {
                        userSignupDto.setEmail(emailDto.getValue());
                    }
                    user = userService.signUpBidderUserAndReturnUser(userSignupDto, event, false, false, false, false, true);
                }
                RegistrationRequest registrationRequest = new RegistrationRequest();
                registrationRequest.setEventId(event.getEventId());
                registrationRequest.setRequestedByUser(user);
                registrationRequest.setRequestedForUser(regUser);
                registrationRequest.setRecurringEventId(recurringEventId);
                registrationRequest.setType(registrationRequestType);
                registrationRequest.setTicketHolderAttributes(ticketHolderAttributes);
                registrationRequest.setStatus(registrationRequestDto.getRegistrationRequestStatus());
                registrationRequest.setCreatedBy(user);
                registrationRequest.setApprovalDisclaimer(registrationRequestDto.getApprovalDisclaimer());
                registrationRequest.setApprovalWLDisclaimer(registrationRequestDto.getApprovalWLDisclaimer());
                if (StringUtils.isNotEmpty(registrationRequestDto.getTrackingUrl())) {
                    TrackingLinks trackingLinks = trackingLinksService.getTrackingLink(event, registrationRequestDto.getTrackingUrl());
                    if (trackingLinks != null) {
                        registrationRequest.setTrackingLinkId(trackingLinks.getId());
                        log.info("createRegistrationRequest | trackingLink ID :  {}", trackingLinks.getId());
                    }
                }
                if (RegistrationRequestType.ATTENDEE.equals(registrationRequestType)) {
                    if (!Objects.isNull(registrationRequestDto.getUtmTrackSourceDto()) && !GeneralUtils.hasOnlyNullFields(registrationRequestDto.getUtmTrackSourceDto())) {
                        log.info("Received UTM tracking data for registration: {} ", registrationRequestDto.getUtmTrackSourceDto());
                        registrationRequest.setRecSource(JsonMapper.parseToJsonString(registrationRequestDto.getUtmTrackSourceDto()));
                    }
                    if (StringUtils.isNotEmpty(registrationRequestDto.getCouponCode())) {
                        Long couponCodeId = ticketingCouponService.getCouponCodeIdByEventIdAndCouponCode(event.getEventId(), registrationRequestDto.getCouponCode(), recurringEventId);
                        registrationRequest.setTicketingCouponId(couponCodeId);
                    }
                }

                RegistrationRequest saveRegistrationRequest = registrationRequestRepository.save(registrationRequest);

                if (RegistrationRequestType.ATTENDEE.equals(registrationRequestType) && !MapUtils.isEmpty(registrationRequestDto.getTicketTypes())) {
                    List<RegistrationRequestTicket> registrationRequestTickets = createRegistrationTickets(registrationRequestDto, saveRegistrationRequest);
                    if (registrationRequest.getTicketingCouponId() != null) {
                        boolean isAnyPaidTicket = registrationRequestTickets.stream()
                                .anyMatch(ticket -> TicketType.PAID.equals(ticket.getTicketType().getTicketType()));
                        if(!isAnyPaidTicket) {
                            registrationRequest.setTicketingCouponId(null);
                            registrationRequestRepository.save(registrationRequest);
                            log.info("createRegistrationRequest | coupon code removed as no paid ticket in registration request");
                        }
                    }
                }
                saveRegistrationRequestHolderDetails(registrationRequestDto, saveRegistrationRequest);
                if (!RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                    sendRegistrationEmailWhenAuto(event, user, registrationRequest, registrationRequestType);
                }
                if (RegistrationRequestStatus.PENDING.equals(registrationRequest.getStatus()) && isApprovalRequestEmailsEnabled(event)) {
                    this.sendApprovalEmailToRegistrationApprovers(event, regUser, registrationRequestType);
                }
                registrationRequestResponseDTO.setRegistrationRequestId(registrationRequest.getId());
                if (RegistrationRequestType.SPEAKER.equals(registrationRequestType) && reviewerService.isApplicationApprovalEnable(event) && !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                    reviewProcessService.createReviewerProcessAndSessionRegistrationRequestMapping(event, saveRegistrationRequest);
                }

                postProcessAfterRegistrationRequestCreation(List.of(saveRegistrationRequest), event, (postProcessRequired)? TrayIOWebhookActionType.REGISTRATION_REQUEST_CREATE:  TrayIOWebhookActionType.REGISTRATION_REQUEST_RE_SYNC);
            }
            return registrationRequestResponseDTO;
        }
    }

    private void postProcessAfterRegistrationRequestCreation(List<RegistrationRequest> registrationRequests, Event event, TrayIOWebhookActionType eventType) {
        List<Long> attendeeRegistrationRequests = registrationRequests.stream()
                .filter(registrationRequest -> RegistrationRequestType.ATTENDEE.equals(registrationRequest.getType()))
                .map(RegistrationRequest::getId).collect(Collectors.toList());

        afterTaskIntegrationTriggerService.onTicketRegistrationRequest(attendeeRegistrationRequests, event, eventType);
    }

    private RegistrationRequestResponseDTO updateExistingRegistrationRequest(Event event, RegistrationRequestType registrationRequestType,RegistrationRequestDto registrationRequestDto, User user, Long recurringEventId, Boolean isWithoutLogin, RegistrationRequest registrationRequest) {
        log.info("update the existing registration request id:{}",registrationRequest.getId());
        RegistrationRequestResponseDTO registrationRequestResponseDTO = new RegistrationRequestResponseDTO();
        if(RegistrationRequestType.SPEAKER.equals(registrationRequestType) && !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())){
            formRuleEngineService.validateFormRulesOfSessionApproval(event, registrationRequestDto.getSessionDetail());
        }

        TicketHolderAttributes ticketHolderAttributes = ticketHolderAttributesRepository.findByid(registrationRequest.getTicketHolderAttributes().getId());
        if (registrationRequestDto != null && (!CollectionUtils.isEmpty(registrationRequestDto.getAttributes()) || RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus()))) {
            for (AttributeKeyValueDto attributeKeyValueDto:registrationRequestDto.getAttributes()){
                if (attributeKeyValueDto.getKey().equalsIgnoreCase(BIRTHDAY) && StringUtils.isNotEmpty(attributeKeyValueDto.getValue()) && !isValidDateFormat(attributeKeyValueDto.getValue())) {
                    processForBirthDateTypeAttribute(attributeKeyValueDto,attributeKeyValueDto.getValue());
                }
                if(!RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                    ticketingPurchaseService.validateHolderBookingData(attributeKeyValueDto);
                }
            }

            Optional<AttributeKeyValueDto> countryCodeOptional = registrationRequestDto.getAttributes().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(COUNTRY_CODE_KEY)).findFirst();
            Optional<AttributeKeyValueDto> phoneNumberOptional = registrationRequestDto.getAttributes().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(PHONE_NUMBER_KEY)).findFirst();
            if (countryCodeOptional.isPresent() && phoneNumberOptional.isPresent() &&
                    !twilioPhoneNumberValidateService.isValidPhoneNumber(countryCodeOptional.get().getValue(), phoneNumberOptional.get().getValue())) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PHONE_NUMBER);
            }
            User regUser = null;
            if(!CollectionUtils.isEmpty(registrationRequestDto.getAttributes())) {
                Optional<AttributeKeyValueDto> emailOpt = registrationRequestDto.getAttributes().stream()
                        .filter(e -> (EMAIL_ADDRESS.equalsIgnoreCase(e.getKey()) || EMAIL.equalsIgnoreCase(e.getKey()))
                                && e.getValue() != null && !e.getValue().trim().isEmpty()).findFirst();
                if (emailOpt.isPresent()) {
                    regUser = getOrCreateUser(event, registrationRequestDto);
                    raiseErrorIfRequestIsAlreadyExists(event, recurringEventId, registrationRequestType, registrationRequestDto, regUser);
                    addUserPhone(regUser, registrationRequestDto.getAttributes());
                }
            }
            Map hashMap = new HashMap();
            try {
                hashMap.put(TICKETING.ATTRIBUTES, this.listToMap(registrationRequestDto.getAttributes()));
                hashMap.put(TICKETING.QUESTIONS, this.listToMap(registrationRequestDto.getQuestions()));
                hashMap.put(TICKETING.NESTEDQUESTIONS, (!CollectionUtils.isEmpty(registrationRequestDto.getNestedQuestions()) ? registrationRequestDto.getNestedQuestions() : null));
                if (RegistrationRequestType.EXPO.equals(registrationRequestType)) {
                    hashMap.put(BOOTH_DETAIL, this.listToMap(registrationRequestDto.getBoothDetail()));
                }
                if (RegistrationRequestType.SPEAKER.equals(registrationRequestType)) {
                    hashMap.put(SESSION_DETAIL, this.listToMap(registrationRequestDto.getSessionDetail()));
                    hashMap.put(SPEAKER_DETAIL, this.mapSpeaker(registrationRequestDto.getSpeakers()));
                } else if (RegistrationRequestType.REVIEWER.equals(registrationRequestType)) {
                    hashMap.put(REVIEWER_DETAIL, this.listToMap(registrationRequestDto.getReviewerDetail()));
                }
                ticketHolderAttributes.setJsonValue(new Gson().toJson(hashMap, Map.class));
                ticketHolderAttributes = ticketHolderAttributesRepository.save(ticketHolderAttributes);
                log.info("update ticket holder attributes id: {}",ticketHolderAttributes.getId());
            } catch (Exception e) {
                log.error("Error while converting to json : {}, exception: {}", registrationRequestDto, e.getMessage());
            }
            if (Boolean.TRUE.equals(isWithoutLogin) && !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                UserSignupDto userSignupDto=new UserSignupDto();
                List<AttributeKeyValueDto> listOfKeyValue = registrationRequestDto.getAttributes();
                AttributeKeyValueDto firstNameDto=listOfKeyValue.get(0);
                if (Constants.STRING_FIRST_SPACE_NAME.equalsIgnoreCase(firstNameDto.getKey())) {
                    userSignupDto.setFirstName(firstNameDto.getValue());
                }
                AttributeKeyValueDto lastNameDto=listOfKeyValue.get(1);
                if (Constants.STRING_LAST_SPACE_NAME.equalsIgnoreCase(lastNameDto.getKey())) {
                    userSignupDto.setLastName(lastNameDto.getValue());
                }
                AttributeKeyValueDto emailDto = listOfKeyValue.get(2);
                if (Constants.EMAIL_ADDRESS.equalsIgnoreCase(emailDto.getKey())) {
                    userSignupDto.setEmail(emailDto.getValue());
                }
                user = userService.signUpBidderUserAndReturnUser(userSignupDto, event, false, false, false, false, true);
            }
            registrationRequest.setEventId(event.getEventId());
            registrationRequest.setRequestedByUser(user);
            registrationRequest.setRequestedForUser(regUser);
            registrationRequest.setRecurringEventId(recurringEventId);
            registrationRequest.setType(registrationRequestType);
            registrationRequest.setTicketHolderAttributes(ticketHolderAttributes);
            registrationRequest.setStatus(registrationRequestDto.getRegistrationRequestStatus());
            registrationRequest.setCreatedBy(user);
            registrationRequest.setApprovalDisclaimer(registrationRequestDto.getApprovalDisclaimer());
            registrationRequest.setApprovalWLDisclaimer(registrationRequestDto.getApprovalWLDisclaimer());
            if(RegistrationRequestType.ATTENDEE.equals(registrationRequestType) && !Objects.isNull(registrationRequestDto.getUtmTrackSourceDto()) && !GeneralUtils.hasOnlyNullFields(registrationRequestDto.getUtmTrackSourceDto()) ){
                log.info("Received UTM tracking data for registration: {} ", registrationRequestDto.getUtmTrackSourceDto());
                registrationRequest.setRecSource(JsonMapper.parseToJsonString(registrationRequestDto.getUtmTrackSourceDto()));
            }

            RegistrationRequest saveRegistrationRequest = registrationRequestRepository.save(registrationRequest);

            if (RegistrationRequestType.ATTENDEE.equals(registrationRequestType) && !MapUtils.isEmpty(registrationRequestDto.getTicketTypes())) {
                createRegistrationTickets(registrationRequestDto, saveRegistrationRequest);
            }
            saveRegistrationRequestHolderDetails(registrationRequestDto, saveRegistrationRequest);
            if(!RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())) {
                sendRegistrationEmailWhenAuto(event, user, registrationRequest, registrationRequestType);
            }
            if(RegistrationRequestStatus.PENDING.equals(registrationRequest.getStatus()) && isApprovalRequestEmailsEnabled(event)){
                this.sendApprovalEmailToRegistrationApprovers(event,regUser,registrationRequestType);
            }
            registrationRequestResponseDTO.setRegistrationRequestId(registrationRequest.getId());
            if(RegistrationRequestType.SPEAKER.equals(registrationRequestType) && reviewerService.isApplicationApprovalEnable(event) && !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())){
                reviewProcessService.createReviewerProcessAndSessionRegistrationRequestMapping(event,saveRegistrationRequest);
            }
        }
        return registrationRequestResponseDTO;
    }

    private void saveRegistrationRequestHolderDetails(RegistrationRequestDto registrationRequestDto, RegistrationRequest saveRegistrationRequest) {
        List<HolderBookingDto> attendees=registrationRequestDto.getAttendees();
        if (!CollectionUtils.isEmpty(attendees)) {
            TicketAttributeValueDto ticketAttributeValueDto = new TicketAttributeValueDto();
            List<RegistrationRequestHolderDetails> registrationRequestHolderDetailsList= new ArrayList<>();
            for (HolderBookingDto attendee : attendees) {
                RegistrationRequestHolderDetails registrationRequestHolderDetails = new RegistrationRequestHolderDetails();
                registrationRequestHolderDetails.setEventId(saveRegistrationRequest.getEventId());
                registrationRequestHolderDetails.setTicketTypeId(attendee.getTickettypeid());
                registrationRequestHolderDetails.setRegistrationRequestId(saveRegistrationRequest.getId());
                setRegistrationRequestJsonValues(attendee, ticketAttributeValueDto,registrationRequestDto,registrationRequestHolderDetails);
                registrationRequestHolderDetails.setRecStatus(RecordStatus.CREATE);
                registrationRequestHolderDetailsList.add(registrationRequestHolderDetails);
            }
            registrationRequestHolderDetailsRepository.saveAll(registrationRequestHolderDetailsList);
        }

    }

    private void setRegistrationRequestJsonValues(HolderBookingDto attendee, TicketAttributeValueDto ticketAttributeValueDto,RegistrationRequestDto registrationRequestDto,RegistrationRequestHolderDetails registrationRequestHolderDetails) {
        ValueDto buyerDetails=new ValueDto();
        List<AttributeKeyValueDto> attributeKeyValueDtos = registrationRequestDto.getAttributes();
        String countryCode = null;
        for (AttributeKeyValueDto e : attributeKeyValueDtos) {
            if (STRING_COUNTRY_CODE.equalsIgnoreCase(e.getKey()) && StringUtils.isNotBlank(e.getValue())) {
                countryCode = e.getValue();
            }
            if (STRING_CELL_SPACE_PHONE.equalsIgnoreCase(e.getKey()) && StringUtils.isNotBlank(e.getValue()) && !e.getValue().contains("|")) {
                String newCellNumber = e.getValue() + "|" + countryCode;
                e.setValue(newCellNumber);
            }
        }
        buyerDetails.setAttributes(registrationRequestDto.getAttributes());
        buyerDetails.setQuestions(registrationRequestDto.getQuestions());
        if (!CollectionUtils.isEmpty(registrationRequestDto.getNestedQuestions()) && ticketingPurchaseService.isValidNestedQuestions(registrationRequestDto.getNestedQuestions())) {
            buyerDetails.setNestedQuestions(registrationRequestDto.getNestedQuestions());
        }
        ticketAttributeValueDto.setPurchaser(buyerDetails);
        ValueDto holderDetails=new ValueDto();
        holderDetails.setAttributes(attendee.getAttributes());
        holderDetails.setQuestions(attendee.getQuestions());
        if (!CollectionUtils.isEmpty(attendee.getNestedQuestions()) && ticketingPurchaseService.isValidNestedQuestions(attendee.getNestedQuestions())) {
            holderDetails.setNestedQuestions(attendee.getNestedQuestions());
        }
        ticketAttributeValueDto.setHolder(holderDetails);
        registrationRequestHolderDetails.setJsonValue(ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto));
    }

    private void processForBirthDateTypeAttribute(AttributeKeyValueDto attributeKeyValueDto, String inputDateString) {
        SimpleDateFormat inputDateFormat = new SimpleDateFormat(DATE_TIME_FORMAT_FULL_STANDARD_UTC);
        inputDateFormat.setTimeZone(TimeZone.getTimeZone(UTC_TIMEZONE));
        try {
            Date inputDate = inputDateFormat.parse(inputDateString);
            if (inputDate != null) {
                SimpleDateFormat outputDateFormat = new SimpleDateFormat(DATE_FORMAT_ONLY_MONTH);
                String newDateFormat = outputDateFormat.format(inputDate);
                //if date is in future and attribute is birthday then throw the error because we don't allow future date for birthday attribute in holder attributes
                if (inputDate.after(new Date())){
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.BIRTH_DATE_NOT_ALLOWED_IN_FUTURE);
                }else {
                    attributeKeyValueDto.setValue(newDateFormat);
                }
            }
        } catch (NotAcceptableException | ParseException e) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.BIRTH_DATE_NOT_ALLOWED_IN_FUTURE);
        }
    }

    protected void addUserPhone(User loginUser, List<AttributeKeyValueDto> attributeKeyValueDtos) {
        Optional<AttributeKeyValueDto> phoneNumber = attributeKeyValueDtos.stream()
                .filter(e ->
                        Constants.STRING_CELL_SPACE_PHONE.equalsIgnoreCase(e.getKey()) && StringUtils.isNotBlank(e.getValue()))
                .findFirst();
        if (phoneNumber.isPresent()) {
            String[] phoneNumberAndCountryCode = phoneNumber.get().getValue().split("\\" + ADD_SEPARATOR);
            if (phoneNumberAndCountryCode.length >= 2 && StringUtils.isNotBlank(phoneNumberAndCountryCode[0])) {
                CountryCode cc = CountryCode.valueOf(StringUtils.upperCase(phoneNumberAndCountryCode[1]));
                Optional<User> optionalUser = roUserService.getUserByAEPhoneNumber
                        (new AccelEventsPhoneNumber(cc, Long.parseLong(phoneNumberAndCountryCode[0])));
                if (!optionalUser.isPresent()) {
                    loginUser.setPhoneNumber(Long.parseLong(phoneNumberAndCountryCode[0]));
                    loginUser.setCountryCode(cc);
                    userService.save(loginUser);
                }
            }
        }
    }

    private void raiseErrorIfRequestIsAlreadyExists(Event event, Long recurringEventId, RegistrationRequestType registrationRequestType,
                                                    RegistrationRequestDto registrationRequestDto, User user) {

        if (!MapUtils.isEmpty(registrationRequestDto.getTicketTypes())) {
            List<RegistrationRequest> registrationRequestList = getRegistrationRequestsByRegistrationType(event, recurringEventId, registrationRequestType, user);

            if (!CollectionUtils.isEmpty(registrationRequestList) && (RegistrationRequestType.EXPO.equals(registrationRequestType)
                    || RegistrationRequestType.SPEAKER.equals(registrationRequestType)
                    || isRequestExistsForRequestedTicketType(registrationRequestDto, registrationRequestList))) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.REGISTRATION_REQUEST_ALREADY_EXISTS);
            }
        } else if (registrationRequestType == RegistrationRequestType.EXPO || registrationRequestType == RegistrationRequestType.REVIEWER) {
            List<RegistrationRequest> registrationRequestList = getRegistrationRequestsExceptDeniedStatus(event, recurringEventId, registrationRequestType, user);
            if (!CollectionUtils.isEmpty(registrationRequestList)) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.REGISTRATION_REQUEST_ALREADY_EXISTS);
            }
        }
        else if(registrationRequestType == RegistrationRequestType.SPEAKER && !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(registrationRequestDto.getRegistrationRequestStatus())){
            if(CollectionUtils.isEmpty(registrationRequestDto.getSpeakers())){
                throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.SESSION_REGISTRATION_REQUEST_REQUIRED_SPEAKER);
            }
            List<SessionRegistrationSpeakerDTO> speakers = registrationRequestDto.getSpeakers();
            SessionApprovalSetting sessionApprovalSetting = getOrCreateDefaultEventSperakerSettings(event);
            long maxSpeaker = sessionApprovalSetting.isAllowToAddAdditionalSpeaker()
                    ? (sessionApprovalSetting.getMaxNumberOfSpeakerPerApplication() != null
                    ? sessionApprovalSetting.getMaxNumberOfSpeakerPerApplication() : Integer.MAX_VALUE) : 1L;

            if(speakers.size() > maxSpeaker){
                throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.MAX_SPEAKER_PER_APPLICATION);
            }
            Set<String> uniqueSpeakerEmails = new HashSet<>();
            for(SessionRegistrationSpeakerDTO speaker : speakers){
                speaker.getAttributes().forEach(e->{
                    if(EMAIL_ADDRESS.equalsIgnoreCase(e.getKey())){
                        uniqueSpeakerEmails.add(e.getValue());
                    }
                });
            }
            if(speakers.size() != uniqueSpeakerEmails.size()){
                throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.SESSION_REGISTRATION_REQUEST_UNIQUE_SPEAKER);
            }
        }
    }

    @Override
    public List<RegistrationRequest> getRegistrationRequestsByRegistrationType(Event event, Long recurringEventId, RegistrationRequestType registrationRequestType, User user) {
        if (!com.accelevents.utils.NumberUtils.isNumberGreaterThanZero(recurringEventId)) {
            return registrationRequestRepository.findByEventAndUserAndRecurringEventIdAndRegistrationTypeAndReqStatus(event, user, recurringEventId, registrationRequestType);
        }
        return registrationRequestRepository.findByEventIdAndUserAndType(event.getEventId(), user, registrationRequestType);
    }

    @Override
    public List<RegistrationRequest> getRegistrationRequestsExceptDeniedStatus(Event event, Long recurringEventId, RegistrationRequestType registrationRequestType, User user) {
        return registrationRequestRepository.findAllByEventIdAndUserAndTypeAndStatusNotDenied(event.getEventId(), user, registrationRequestType);
    }

    private User getOrCreateUser(Event event, RegistrationRequestDto registrationRequestDto) {
        Optional<AttributeKeyValueDto> emailOpt = registrationRequestDto.getAttributes().stream().filter(e -> EMAIL_ADDRESS.equalsIgnoreCase(e.getKey()) || EMAIL.equalsIgnoreCase(e.getKey())).findFirst();
        if (!emailOpt.isPresent()) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_EMAIL);
        }
        Optional<User> optionalUser = roUserService.findOpUserByEmail(emailOpt.get().getValue());
        if (optionalUser.isPresent()) {
            return optionalUser.get();
        }

        User user = new User();
        registrationRequestDto.getAttributes().stream().filter(e -> FIRST_NAME.equalsIgnoreCase(e.getKey())).findFirst().ifPresent(e -> user.setFirstName(e.getValue()));
        registrationRequestDto.getAttributes().stream().filter(e -> LAST_NAME.equalsIgnoreCase(e.getKey())).findFirst().ifPresent(e -> user.setLastName(e.getValue()));
        registrationRequestDto.getAttributes().stream().filter(e -> COUNTRY_CODE_KEY.equalsIgnoreCase(e.getKey())).findFirst().ifPresent(e -> user.setCountryCode(CountryCode.valueOf(e.getValue().toUpperCase())));
        registrationRequestDto.getAttributes().stream().filter(e -> PHONE_NUMBER_KEY.equalsIgnoreCase(e.getKey())).findFirst().ifPresent(e -> user.setPhoneNumber(Long.parseLong(e.getValue())));
        user.setEmail(emailOpt.get().getValue());
        user.setMostRecentEventId(event.getEventId());
        return userService.save(user);
    }

    private boolean isRequestExistsForRequestedTicketType(RegistrationRequestDto registrationRequestDto, List<RegistrationRequest> registrationRequestList) {
        List<Long> requestIds = registrationRequestList.stream().map(RegistrationRequest::getId).collect(Collectors.toList());
        List<Long> ticketingTypeIds = registrationRequestTicketRepository.findTicketingTypesByRegistrationRequestIds(requestIds);

        return registrationRequestDto.getTicketTypes().entrySet().stream().allMatch(e -> ticketingTypeIds.contains(e.getKey()));
    }

    private List<RegistrationRequestTicket> createRegistrationTickets(RegistrationRequestDto registrationRequestDto, RegistrationRequest saveRegistrationRequest) {
        List<RegistrationRequestTicket> registrationRequestTickets = new ArrayList<>();
        registrationRequestDto.getTicketTypes().forEach((key, value) -> {
            RegistrationRequestTicket registrationRequestTicket = new RegistrationRequestTicket();
            registrationRequestTicket.setTicketType(getTicketingTypeOrThrowError(key));
            registrationRequestTicket.setRegistrationRequest(saveRegistrationRequest);
            registrationRequestTicket.setNumberOfTicket(value);
            registrationRequestTickets.add(registrationRequestTicket);
        });
        registrationRequestTicketRepository.saveAll(registrationRequestTickets);
        return registrationRequestTickets;
    }

    private TicketingType getTicketingTypeOrThrowError(Long ticketTypeId) {
        Optional<TicketingType> optionalTicketingType = ticketingTypeRepository.findById(ticketTypeId);
        if (!optionalTicketingType.isPresent()) {
            throw new NotFoundException(NotFoundException.NotFound.TICKET_TYPE_NOT_FOUND);
        }
        return optionalTicketingType.get();
    }

    @Override
    public DataTableResponse getRegistrationRequest(Event event, User loggedInUser, Long recurringEventId, RegistrationRequestType registrationRequestType,
                                                    List<RegistrationRequestStatus> status, int page, int size, String sortColumn,
                                                    boolean isAsc, String searchString) {

        List<String> defaultColumns = new ArrayList<>();
        List<String> customColumns = new ArrayList<>();
        List<Long> ticketAttributeIds = new ArrayList<>();
        List<Long> reviewFormQuestionIds = new ArrayList<>();

        UserColumnSelectionDto columnSelectionDto = roAnalyticsUserColumnSelectionService.getDefaultOrUserColumnSelection(AnalyticsSource.EVENT,
                event.getEventId(),
                getAreaFromType(registrationRequestType),
                loggedInUser.getUserId(),
                -1L);
        roAnalyticsUserColumnSelectionService.setDefaultAndCustomiseColumns(defaultColumns, customColumns, ticketAttributeIds,reviewFormQuestionIds, columnSelectionDto.getColumnSelection());

        Pageable pageable = PageRequest.of(Math.max(page, 0), (size > 0) ? size : 10,
                isAsc ? Sort.Direction.ASC : Sort.Direction.DESC, "addedDate".equals(sortColumn) ? "createdAt" : sortColumn);

        if (CollectionUtils.isEmpty(status)) {
            status = Arrays.asList(RegistrationRequestStatus.APPROVED, RegistrationRequestStatus.DENIED, RegistrationRequestStatus.PENDING, RegistrationRequestStatus.WAITLISTED, RegistrationRequestStatus.REGISTERED, RegistrationRequestStatus.PRELIMINARY_APPROVAL, RegistrationRequestStatus.REVIEW_COMPLETED);
        }
        Page<RegistrationRequestsDetailsDto> registrationRequests = registrationRequestRepository.getRegistrationRequests(event.getEventId(), searchString,
                status, registrationRequestType, pageable);

        List<RegistrationRequestsDetailsDto> content = registrationRequests.getContent();

        List<Long> registrationRequestIds = content.stream()
                .map(dto -> dto.getId())
                .collect(Collectors.toList());
        List<RegistrationRequestTicket> registrationRequestTickets =
                registrationRequestIds.isEmpty() ? Collections.emptyList() :
                        registrationRequestTicketRepository.findAllByRegistrationRequestIds(registrationRequestIds);
        Map<Long, List<RegistrationRequestTicket>> registrationRequestToTicketsMap = registrationRequestTickets.stream()
                .collect(Collectors.groupingBy(registrationRequestTicket -> registrationRequestTicket.getRegistrationRequest().getId()));

        List<Long> registrationRequestIdIsPresentInCaptureCardData;
        Map<Long, String> couponCodeIdAndNameMap;
        if (RegistrationRequestType.ATTENDEE.equals(registrationRequestType)) {
            registrationRequestIdIsPresentInCaptureCardData = registrationRequestAndStripeCustomerMappingService.checkRegistrationRequestIdIsPresent(registrationRequestIds);
            List<Long> couponIds = content.stream().map(RegistrationRequestsDetailsDto::getTicketingCouponId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            couponCodeIdAndNameMap = ticketingCouponService.createMapOfCouponNameAndIdByIds(couponIds);
        } else {
            couponCodeIdAndNameMap = Collections.emptyMap();
            registrationRequestIdIsPresentInCaptureCardData = Collections.emptyList();
        }
        content.forEach(dto -> {
            Long registrationRequestId = dto.getId();
            List<RegistrationRequestTicket> ticketsForRequest = registrationRequestToTicketsMap.get(registrationRequestId);
            if (ticketsForRequest != null) {
                Map<Long, TicketDetailsDto> ticketTypes = new HashMap<>();
                ticketsForRequest.forEach(registrationRequestTicket -> {
                    TicketDetailsDto ticketDetailsDto = new TicketDetailsDto();
                    ticketDetailsDto.setTicketCount(registrationRequestTicket.getNumberOfTicket());
                    ticketDetailsDto.setTicketName(registrationRequestTicket.getTicketType().getTicketTypeName());
                    ticketDetailsDto.setTicketType(registrationRequestTicket.getTicketType().getTicketType());
                    ticketTypes.put(registrationRequestTicket.getTicketType().getId(), ticketDetailsDto);
                });
                dto.setTicketTypes(ticketTypes);
            }
            dto.setApprovedAndAssignEnabledForPaidTicket(registrationRequestIdIsPresentInCaptureCardData.contains(registrationRequestId));
            dto.setCouponCode(couponCodeIdAndNameMap.get(dto.getTicketingCouponId()));
        });
        Map<Long, RegistrationRequest> registrationRequestMap = getRegistrationRequestMap(event, content);
        setSessionAndExpoDefaultValue(registrationRequestType, content, registrationRequestMap);
        if (RegistrationRequestType.ATTENDEE.equals(registrationRequestType)){
            setCustomColumnValuesForAttendees(event, loggedInUser, ticketAttributeIds, content, registrationRequestMap);
        } else {
            setCustomColumnValues(event, loggedInUser, ticketAttributeIds,reviewFormQuestionIds,defaultColumns.contains(DEFAULT_COLUMN_REVIEW_STATUS), content, registrationRequestMap);
        }
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(registrationRequests.getTotalElements());
        dataTableResponse.setRecordsFiltered(registrationRequests.getNumberOfElements());
        dataTableResponse.setData(content);

        return dataTableResponse;
    }

    private void setCustomColumnValuesForAttendees(Event event, User loggedInUser, List<Long> ticketAttributeIds, List<RegistrationRequestsDetailsDto> registrationRequestDtos, Map<Long, RegistrationRequest> registrationRequestMap) {
        log.info("Start of set custom column values for registration approval attendees {}, event {}",  loggedInUser.getUserId(), event.getEventId());
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes =ticketHolderRequiredAttributesService.findByEventAndIds(event, ticketAttributeIds);
        if (CollectionUtils.isEmpty(ticketHolderRequiredAttributes)) {
            return;
        }
        for (RegistrationRequestsDetailsDto registrationRequestDto : registrationRequestDtos) {
            RegistrationRequest registrationRequest = registrationRequestMap.get(registrationRequestDto.getId());
            if (registrationRequest != null) {
                    RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
                    Map<String, Object> customColumnValues = new HashMap<>();
                    for (TicketHolderRequiredAttributes attribute : ticketHolderRequiredAttributes) {
                        customColumnValues.put(attribute.getName(),
                                getAttributeValue(registrationResponseDto,
                                        attribute.getName(),
                                        attribute.isAttribute(),
                                        AttributeValueType.CONDITIONAL_QUE.equals(attribute.getAttributeValueType()),
                                        RegistrationAttributeType.ATTENDEE));
                    }
                    registrationRequestDto.setCustomColumnValues(customColumnValues);
                }
            }
        log.info("Successfully set custom column values for registration approval attendees {}, event {}, result size {}",  loggedInUser.getUserId(), event.getEventId(), registrationRequestDtos.size());
    }

    private void setSessionAndExpoDefaultValue(RegistrationRequestType requestType, List<RegistrationRequestsDetailsDto> registrationRequestDtos, Map<Long, RegistrationRequest> registrationRequestMap) {
        for (RegistrationRequestsDetailsDto registrationRequestDto : registrationRequestDtos) {
            RegistrationRequest registrationRequest = registrationRequestMap.get(registrationRequestDto.getId());
            if (registrationRequest != null) {
                RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
                if (RegistrationRequestType.SPEAKER.equals(requestType)) {
                    String sessionName = getAttributeValue(registrationResponseDto, SPEAKER_APPROVAL_SESSION_TOPIC, true, false, RegistrationAttributeType.SPEAKER_DETAILS);
                    registrationRequestDto.setSessionName(sessionName);
                    String sessionDescription = getAttributeValue(registrationResponseDto, SPEAKER_APPROVAL_SESSION_DESCRIPTION, true, false, RegistrationAttributeType.SPEAKER_DETAILS);
                    registrationRequestDto.setSessionDescription(sessionDescription);
                    registrationRequestDto.setPrimarySpeaker(getPrimarySpeaker(registrationResponseDto));

                } else if (RegistrationRequestType.EXPO.equals(requestType)) {
                    String boothName = getAttributeValue(registrationResponseDto, EXHIBITOR_APPROVAL_BOOTH_NAME, true, false, RegistrationAttributeType.BOOTH_DETAILS);
                    registrationRequestDto.setBoothName(boothName);
                }
            }
        }
    }

    private String getPrimarySpeaker(RegistrationResponseDto registrationRequestDto) {
        String primarySpeakerName = STRING_EMPTY;
        List<RegistrationSpeakerResponseDto> speakerDetail = registrationRequestDto.getSpeakerDetail();
        if(!CollectionUtils.isEmpty(speakerDetail)){
            RegistrationSpeakerResponseDto registrationSpeakerResponseDto = speakerDetail.stream().filter(RegistrationSpeakerResponseDto::isPrimary).findFirst().orElse(null);
            if(registrationSpeakerResponseDto != null && registrationSpeakerResponseDto.getAttributes() != null){
                Map<String, String> attributes = registrationSpeakerResponseDto.getAttributes();
                if(StringUtils.isNotEmpty(attributes.get(FIRST_NAME)) && StringUtils.isNotEmpty(attributes.get(LAST_NAME))){
                    primarySpeakerName = attributes.get(FIRST_NAME) + Constants.STRING_BLANK + attributes.get(LAST_NAME);
                }
            }
        }
        if(StringUtils.isEmpty(primarySpeakerName) && registrationRequestDto.getAttributes() != null){
            if(StringUtils.isNotEmpty(registrationRequestDto.getAttributes().get(FIRST_NAME)) && StringUtils.isNotEmpty(registrationRequestDto.getAttributes().get(LAST_NAME))){
                primarySpeakerName = registrationRequestDto.getAttributes().get(FIRST_NAME) + Constants.STRING_BLANK + registrationRequestDto.getAttributes().get(LAST_NAME);
            }
        }
        return primarySpeakerName;
    }

    private void setCustomColumnValues(Event event, User user, List<Long> attributeIds, List<Long> reviewFormQuestionIds,
                                       boolean isReviewStatusAdded,
                                       List<RegistrationRequestsDetailsDto> registrationRequestDtos,
                                       Map<Long, RegistrationRequest> registrationRequestMap) {
        log.info("Start of set custom column values for user {}, event {}", user.getUserId(), event.getEventId());

        List<RegistrationAttribute> attributeList = registrationAttributeService.findByEventAndIds(event, attributeIds);
        List<Long> reviewerFormQuestionIds = getReviewerFormQuestionIds(event, reviewFormQuestionIds);
        Map<Long, List<ReviewerFormResponse>> requestReviewerFormResponseMap = getReviewerFormResponseMap(reviewerFormQuestionIds, registrationRequestMap,isReviewStatusAdded);

        if (!CollectionUtils.isEmpty(attributeList) || !CollectionUtils.isEmpty(reviewFormQuestionIds) || isReviewStatusAdded) {
            for (RegistrationRequestsDetailsDto registrationRequestDto : registrationRequestDtos) {
                RegistrationRequest registrationRequest = registrationRequestMap.get(registrationRequestDto.getId());
                if (registrationRequest == null) {
                    continue;
                }
                getCustomColumnDataForRegistrationRequest(registrationRequestDto, registrationRequest, attributeList,
                        reviewerFormQuestionIds, requestReviewerFormResponseMap, isReviewStatusAdded);
            }

            log.info("Successfully set custom column values for user {}, event {}, result size {}",
                    user.getUserId(), event.getEventId(), registrationRequestDtos.size());
        }

    }

    private List<Long> getReviewerFormQuestionIds(Event event, List<Long> reviewFormQuestionIds) {
        if (CollectionUtils.isEmpty(reviewFormQuestionIds)) {
            return Collections.emptyList();
        }
        return reviewerFormQuestionRepository.getIdsByIdsAndEventIdAndTypes(
                reviewFormQuestionIds, event.getEventId(),
                Arrays.asList(AttributeValueType.STAR_RATING, AttributeValueType.NUMBER_RATING));
    }

    private Map<Long, List<ReviewerFormResponse>> getReviewerFormResponseMap(List<Long> reviewerFormQuestionIds,
                                                                             Map<Long, RegistrationRequest> registrationRequestMap, boolean isReviewStatusAdded) {
        if (!CollectionUtils.isEmpty(reviewerFormQuestionIds) || isReviewStatusAdded) {
            List<ReviewerFormResponse> reviewerFormResponseList = reviewerFormResponseRepository.findAllByRegistrationRequestIds(new ArrayList<>(registrationRequestMap.keySet()));
            return CollectionUtils.isEmpty(reviewerFormResponseList)
                    ? Collections.emptyMap()
                    : reviewerFormResponseList.stream().collect(Collectors.groupingBy(ReviewerFormResponse::getRegistrationRequestId));
        } else {
            return Collections.emptyMap();
        }
    }

    private void getCustomColumnDataForRegistrationRequest(RegistrationRequestsDetailsDto registrationRequestDto,
                                            RegistrationRequest registrationRequest,
                                            List<RegistrationAttribute> attributeList,
                                            List<Long> reviewerFormQuestionIds,
                                            Map<Long, List<ReviewerFormResponse>> requestReviewerFormResponseMap,
                                            boolean isReviewStatusAdded) {
        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
        Map<String, Object> customColumnValues = new HashMap<>();
        Map<Long, Long> reviewFormQuestionColumnValues = reviewerFormQuestionIds.stream()
                .collect(Collectors.toMap(id -> id, id -> 0L));

        if (!CollectionUtils.isEmpty(requestReviewerFormResponseMap)) {
            getReviewerResponsesDataForCustomColumn(registrationRequestDto, requestReviewerFormResponseMap.get(registrationRequest.getId()),
                    reviewFormQuestionColumnValues, isReviewStatusAdded);
        }

        attributeList.forEach(attribute -> customColumnValues.put(attribute.getName(),
                getAttributeValue(registrationResponseDto, attribute.getName(), attribute.isAttribute(),
                        AttributeValueType.CONDITIONAL_QUE.equals(attribute.getValueType()), attribute.getType())));

        reviewFormQuestionColumnValues.forEach((key, value) ->
                customColumnValues.put(String.valueOf(key), value)
        );
        registrationRequestDto.setCustomColumnValues(customColumnValues);
    }

    private void getReviewerResponsesDataForCustomColumn(RegistrationRequestsDetailsDto registrationRequestDto,
                                          List<ReviewerFormResponse> responseList,
                                          Map<Long, Long> reviewFormQuestionColumnValues,
                                          boolean isReviewStatusAdded) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }

        long totalCompletedReviewCount = responseList.stream()
                .filter(response -> isReviewStatusAdded && WorkflowStatus.COMPLETED.equals(response.getStatus()))
                .count();

        responseList.stream()
                .map(ReviewerFormResponse::getResponse)
                .map(response -> JsonMapper.parseJsonArray(response, SurveyQuestionsKeyValueDto.class))
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(dto -> reviewFormQuestionColumnValues.containsKey(dto.getQuestionId()) && StringUtils.isNotBlank(dto.getValue()))
                .forEach(dto -> reviewFormQuestionColumnValues.computeIfPresent(dto.getQuestionId(), (k, v) -> v + 1));

        registrationRequestDto.setTotalCompletedCount(totalCompletedReviewCount);
        registrationRequestDto.setTotalReviewerCount(responseList.size());
    }

    private Map<Long, RegistrationRequest> getRegistrationRequestMap(Event event, List<RegistrationRequestsDetailsDto> registrationRequestDtos) {
        List<Long> ids = registrationRequestDtos.stream().map(RegistrationRequestsDetailsDto::getId).collect(Collectors.toList());
        List<RegistrationRequest> registrationRequests = !CollectionUtils.isEmpty(ids) ? registrationRequestRepository.findAllByEventAndIds(event, ids) : Collections.emptyList();
        Map<Long, RegistrationRequest> registrationRequestMap = registrationRequests.stream().collect(Collectors.toMap(RegistrationRequest::getId, registrationRequest -> registrationRequest));
        log.info("Fetch Registration info data result size {} " , registrationRequestMap.size());
        return registrationRequestMap;
    }

    @Override
    @Transactional
    public RegistrationRequestApprovalResponseDTO updateRegistrationRequest(Event event, User user, List<Long> requestIds, RegistrationRequestStatus status) {
        RegistrationRequestApprovalResponseDTO registrationRequestApprovalResponseDTO = new RegistrationRequestApprovalResponseDTO();
        if (!CollectionUtils.isEmpty(requestIds)) {
            List<Long> availableTicketType = new ArrayList<>();
            List<RegistrationRequestTicketTypeDTO> registrationRequestTickets = registrationRequestTicketRepository.findTicketingTypeIdAndRegistrationIdByRegistrationRequestIds(requestIds);
            Set<Long> registeredTicketTypes = registrationRequestTickets.stream().map(RegistrationRequestTicketTypeDTO::getTicketTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
            if(!CollectionUtils.isEmpty(registeredTicketTypes)){
                availableTicketType = ticketingTypeRepository.findByIds(new ArrayList<>(registeredTicketTypes)).stream().map(TicketingType::getId).collect(Collectors.toList());
            }

            List<Long> availableTicketsInEvent = ticketingTypeService.findIdAllByEventIdRecurringIdNull(event, List.of(DataType.TICKET));

            //noinspection unchecked
            List<Long> unavailableTicketTypes = new ArrayList<>(org.apache.commons.collections.CollectionUtils.disjunction(registeredTicketTypes,availableTicketType));

            List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByEventAndIds(event, requestIds);

            Ticketing ticketing = ticketingService.findByEvent(event);
            boolean isSpeakerInviteEnable = virtualEventService.isSpeakerInviteEnable(event);

            List<RegistrationRequest> finalRegistrationRequest = new ArrayList<>();
            registrationRequests.forEach(registrationRequest -> {
                Long requestId = registrationRequest.getId();
                if(RegistrationRequestStatus.APPROVED.equals(status) ){
                    if(RegistrationRequestType.ATTENDEE.equals(registrationRequest.getType())){

                        Long registrationRequestTicketTypeId = registrationRequestTickets.stream().filter(
                                requestTicketTypeDTO->requestTicketTypeDTO.getRegistrationRequestId().equals(requestId))
                                .findFirst().get().getTicketTypeId();
                        if(!unavailableTicketTypes.contains(registrationRequestTicketTypeId)){
                            addHistory(registrationRequest, status, user);
                            registrationRequest.setRecStatus(RecordStatus.UPDATE);
                            registrationRequest.setUpdatedAt(new Date());
                            registrationRequest.setStatus(status);
                            registrationRequest.setUpdatedBy(user);
                            registrationRequest.setRecentApprovedAt(new Date());

                            finalRegistrationRequest.add(registrationRequest);
                            log.info("Registration request  {} approved by {}.",requestId,user);

                        }
                        else {
                            registrationRequestApprovalResponseDTO.addAttendeeRejectedList(registrationRequest.getId());
                        }
                    }
                    else{
                        if(RegistrationRequestType.REVIEWER.equals(registrationRequest.getType())){
                            User reviewerUser = registrationRequest.getRequestedForUser();
                            if(reviewerUser != null) {
                                boolean isReviewerExists =  reviewerRepository.emailExistForEvent(event.getEventId(), reviewerUser.getEmail());
                                if(isReviewerExists){
                                    throw new NotAcceptableException(NotAcceptableException.ReviewerExceptionMsg.EMAIL_ALREADY_REGISTERED_AS_REVIEWER);
                                }
                            }
                        }
                        addHistory(registrationRequest, status, user);
                        registrationRequest.setRecStatus(RecordStatus.UPDATE);
                        registrationRequest.setUpdatedAt(new Date());
                        registrationRequest.setStatus(status);
                        registrationRequest.setUpdatedBy(user);
                        registrationRequest.setRecentApprovedAt(new Date());

                        finalRegistrationRequest.add(registrationRequest);
                        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
                        if(registrationResponseDto != null){
                            postProcessAfterApproveSpeakerExhibitorRequest(registrationRequest, registrationResponseDto, event, user, ticketing, availableTicketsInEvent, isSpeakerInviteEnable, registrationRequestApprovalResponseDTO);
                        }
                    }
                }
                else{
                    addHistory(registrationRequest, status, user);
                    registrationRequest.setRecStatus(RecordStatus.UPDATE);
                    registrationRequest.setUpdatedAt(new Date());
                    registrationRequest.setStatus(status);
                    registrationRequest.setUpdatedBy(user);
                    finalRegistrationRequest.add(registrationRequest);
                    log.info("Status {} was assigned to registration request {} by {}.", status, requestId, user);

                }
            });
            if (!CollectionUtils.isEmpty(finalRegistrationRequest)) {
                registrationRequestRepository.saveAll(finalRegistrationRequest).forEach(e -> {
                    if (!(RegistrationRequestStatus.PRELIMINARY_APPROVAL.equals(e.getStatus()) || RegistrationRequestStatus.REVIEW_COMPLETED.equals(e.getStatus()))) {
                        sendRegistrationEmailWhenAuto(event, user, e, e.getType());
                    }
                });
            }
            postProcessAfterRegistrationRequestCreation(finalRegistrationRequest, event, TrayIOWebhookActionType.REGISTRATION_REQUEST_UPDATE);
        }
        return registrationRequestApprovalResponseDTO;
    }

    private void postProcessAfterApproveSpeakerExhibitorRequest(RegistrationRequest registrationRequest, RegistrationResponseDto registrationResponseDto, Event event, User user, Ticketing ticketing, List<Long> availableTicketsInEvent, boolean isSpeakerInviteEnable, RegistrationRequestApprovalResponseDTO registrationRequestApprovalResponseDTO) {
        if(RegistrationRequestType.SPEAKER.equals(registrationRequest.getType())){
            SpeakerApprovalResponseDTO speakerApprovalResponseDTO = new SpeakerApprovalResponseDTO();
            List<Speaker> speakers = registerSpeaker(registrationRequest, registrationResponseDto, event, user, speakerApprovalResponseDTO);
            if(!CollectionUtils.isEmpty(speakers)){
                Long sessionId = createSession(registrationRequest.getId(), registrationResponseDto.getSessionDetail(), registrationResponseDto.getAttributes(), event, user, ticketing, availableTicketsInEvent, speakerApprovalResponseDTO);
                if(NumberUtils.isNumberGreaterThanZero(sessionId)){
                    for(Speaker speaker : speakers){
                        boolean sessionSpeaker = sessionSpeakerService.addSessionSpeaker(sessionId, speaker.getId(), event, isSpeakerInviteEnable);
                        log.info("speaker {} successfully added in a session {} as moderator by user {}", speaker.getId(), sessionId, user.getUserId());
                        speakerApprovalResponseDTO.setSpeakerAddedInSession(true);
                        if(sessionSpeaker && isSpeakerInviteEnable){
                            speakerHelperService.sendInvite(event, user, sessionId, speaker.getId() );
                            log.info("Invitation mail successfully send to speaker {}", speaker.getId());
                            speakerApprovalResponseDTO.setSpeakerInvitationSent(true);
                        }
                    }
                }
            }
            registrationRequestApprovalResponseDTO.addSpeakerApprovalResponse(speakerApprovalResponseDTO);
        }
        else if (RegistrationRequestType.EXPO.equals(registrationRequest.getType())){
            ExhibitorApprovalResponseDTO exhibitorApprovalResponseDTO = new ExhibitorApprovalResponseDTO(registrationRequest.getId());
            StaffDetail staffDetail = new StaffDetail();
            ExhibitorDto exhibitorDto = createBooth(registrationRequest, registrationResponseDto, event, user, exhibitorApprovalResponseDTO, staffDetail);
            if(exhibitorDto != null){
                exhibitorApprovalResponseDTO.setRegistrationId(registrationRequest.getId());
                exhibitorApprovalResponseDTO.setBoothName(exhibitorDto.getExhibitorName());
                log.info("Exhibitor Successfully created after registration approval {}, Booth name {}", registrationRequest.getId(), exhibitorDto.getExhibitorName());
                Staff staff = staffService.addStaff(event, staffDetail, user, exhibitorDto.getId());
                exhibitorApprovalResponseDTO.setExhibitorStaffName(staffDetail.getFirstName() + STRING_BLANK + staffDetail.getLastName());
                exhibitorApprovalResponseDTO.setExhibitorStaffEmail(staffDetail.getEmail());
                log.info("Exhibitor Admin {} successfully added in booth {}", staff.getId(), exhibitorDto.getExhibitorName());
            }
            registrationRequestApprovalResponseDTO.addExhibitorApprovalResponse(exhibitorApprovalResponseDTO);
        }
    }

    private List<Speaker> registerSpeaker(RegistrationRequest registrationRequest, RegistrationResponseDto registrationResponseDto, Event event, User user, SpeakerApprovalResponseDTO speakerApprovalResponseDTO) {
        List<Speaker> speakerList = new ArrayList<>();
        log.info("request received to register speaker after approve registration request {} by user {}", registrationRequest.getId(), user.getUserId());
        List<KeyValueDto> speakerAttributeMappedList = customFormAttributeService.getSpeakerAttributeByEventAndAttributeValueTypeList(event,Arrays.asList(AttributeValueType.TEXT,AttributeValueType.LONG_TEXT), SPEAKER_INFO);
        List<RegistrationApprovalSpeakerDto> speakerDTOs = prepareSpeakerDTO(registrationResponseDto,speakerAttributeMappedList);
        speakerDTOs.sort(Comparator.comparing(RegistrationApprovalSpeakerDto::isPrimarySpeaker).reversed());
        for(RegistrationApprovalSpeakerDto speakerDTO : speakerDTOs ){
            if(StringUtils.isNotEmpty(speakerDTO.getFirstName()) && StringUtils.isNotEmpty(speakerDTO.getLastName()) && StringUtils.isNotEmpty(speakerDTO.getEmail())) {
                List<Speaker> speakers = speakerService.getSpeakerByEventIdAndEmail(event, speakerDTO.getEmail());
                if(CollectionUtils.isEmpty(speakers)){
                    Long speakerId= speakerService.createSpeaker(speakerDTO, event);
                    speakerApprovalResponseDTO.setSpeakerCreated(true);
                    speakerApprovalResponseDTO.setSpeakerName(speakerDTO.getFirstName()+" "+speakerDTO.getLastName());
                    speakerApprovalResponseDTO.setSpeakerEmail(speakerDTO.getEmail());
                    log.info("Speaker successfully created after approved registration request by user {}, speaker Id {}, registration Id {}", user.getUserId(), speakerId, registrationRequest.getId());
                    speakerList.add(speakerService.getSpeakerById(speakerId, event));
                }
                else if((!speakerDTO.getFirstName().equals(speakers.get(0).getFirstName()))
                        || (!speakerDTO.getLastName().equals(speakers.get(0).getLastName()))){
                    Speaker speaker = speakers.get(0);
                    speaker.setFirstName(speakerDTO.getFirstName());
                    speaker.setLastName(speakerDTO.getLastName());

                    speakerApprovalResponseDTO.setSpeakerUpdated(true);
                    speakerApprovalResponseDTO.setSpeakerName(speaker.getFirstName()+" "+speaker.getLastName());
                    speakerApprovalResponseDTO.setSpeakerEmail(speakerDTO.getEmail());
                    log.info("Speaker successfully updated after approved registration request by user {}, speaker {}, Registration Id {}", user.getUserId(), speaker.getId(), registrationRequest.getId());
                    speakerList.add(speakerService.save(speaker));
                }
                else{
                    log.info("No need to create/update speaker because in this event speaker {} already exists with same details for registrationId {}", speakers.get(0).getId(), registrationRequest.getId());
                    speakerApprovalResponseDTO.setSpeakerExists(true);
                    speakerApprovalResponseDTO.setSpeakerName(speakers.get(0).getFirstName()+" "+speakers.get(0).getLastName());
                    speakerApprovalResponseDTO.setSpeakerEmail(speakerDTO.getEmail());
                    speakerList.add(speakers.get(0));
                    saveCustomAttributeValue(speakers.get(0), speakerDTO);
                }
            }
        }
        return speakerList;
    }

    private void saveCustomAttributeValue(Speaker speaker, SpeakerDTO speakerDTO) {
        CustomFormAttributeData customFormAttributeData = speaker.getSpeakerAttribute();
        if(customFormAttributeData == null){
            customFormAttributeData = new CustomFormAttributeData();
        }
        CustomAttributesResponseDto speakerAttributeResponseDto = customFormAttributeService.getCustomAttributeResponseDto(customFormAttributeData);
        Map<String, String> existingAttributes = speakerAttributeResponseDto.getAttributes();

        if(existingAttributes == null){
            existingAttributes = new HashMap<>();
        }

        if ( speakerDTO.getSpeakerAttributeDetailsDto() != null && !CollectionUtils.isEmpty(speakerDTO.getSpeakerAttributeDetailsDto().getAttributes())) {
            List<AttributeKeyValueDto> newAttributes = speakerDTO.getSpeakerAttributeDetailsDto().getAttributes();
            for (AttributeKeyValueDto attributeKeyValueDto : newAttributes) {
                existingAttributes.put(attributeKeyValueDto.getKey(), attributeKeyValueDto.getValue());
            }
        }
        speakerAttributeResponseDto.setAttributes(existingAttributes);

        Map<String, Object> attributeMap = new HashMap<>();
        attributeMap.put(TICKETING.ATTRIBUTES, speakerAttributeResponseDto.getAttributes());
        attributeMap.put(TICKETING.QUESTIONS, speakerAttributeResponseDto.getQuestions());
        attributeMap.put(TICKETING.NESTEDQUESTIONS, (!CollectionUtils.isEmpty(speakerAttributeResponseDto.getNestedQuestions()) ? speakerAttributeResponseDto.getNestedQuestions() : null));
        customFormAttributeData.setJsonValue(new Gson().toJson(attributeMap, Map.class));

        customFormAttributeDataRepository.save(customFormAttributeData);
    }

    private List<RegistrationApprovalSpeakerDto> prepareSpeakerDTO(RegistrationResponseDto registrationResponseDto,List<KeyValueDto> speakerAttributeMappedList){
        List<RegistrationApprovalSpeakerDto> speakerDTOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(registrationResponseDto.getSpeakerDetail()) && registrationResponseDto.getAttributes() != null){
            RegistrationApprovalSpeakerDto speakerDetails = getSpeakerDetails(registrationResponseDto.getAttributes(),speakerAttributeMappedList);
            speakerDetails.setPrimarySpeaker(true);
            speakerDTOList.add(speakerDetails);
        }
        else{
            registrationResponseDto.getSpeakerDetail().forEach(speakerDetail -> {
                RegistrationApprovalSpeakerDto speakerDetails = getSpeakerDetails(speakerDetail.getAttributes(),speakerAttributeMappedList);
                speakerDetails.setPrimarySpeaker(speakerDetail.isPrimary());
                speakerDTOList.add(speakerDetails);
            });

        }
        if(!CollectionUtils.isEmpty(speakerDTOList)){
            if (speakerDTOList.stream().anyMatch(RegistrationApprovalSpeakerDto::isPrimarySpeaker)) {
                return speakerDTOList;
            }
            speakerDTOList.get(0).setPrimarySpeaker(true);
        }
        return speakerDTOList;
    }

    private RegistrationApprovalSpeakerDto getSpeakerDetails(Map<String, String> attributes,List<KeyValueDto> speakerAttributeMappedList){
        RegistrationApprovalSpeakerDto speakerDTO = new RegistrationApprovalSpeakerDto();
        if(StringUtils.isNotEmpty(attributes.get(FIRST_NAME))){
            speakerDTO.setFirstName(attributes.get(FIRST_NAME));
        }
        if(StringUtils.isNotEmpty(attributes.get(LAST_NAME))){
            speakerDTO.setLastName(attributes.get(LAST_NAME));
        }
        if(StringUtils.isNotEmpty(attributes.get(EMAIL))){
            speakerDTO.setEmail(attributes.get(EMAIL));
        }
        if(StringUtils.isNotEmpty(attributes.get(EMAIL_ADDRESS))) {
            speakerDTO.setEmail(attributes.get(EMAIL_ADDRESS));
        }
        if(!CollectionUtils.isEmpty(speakerAttributeMappedList)) {
            setSpeakerAttributeMapped(speakerDTO, attributes, speakerAttributeMappedList);
        }
        return speakerDTO;
    }

    private void setSpeakerAttributeMapped(RegistrationApprovalSpeakerDto speakerDTO,
                                           Map<String, String> attributes,
                                           List<KeyValueDto> speakerAttributeMappedList) {
        CustomAttributeDetailsDto speakerAttributeDetailsDto = new CustomAttributeDetailsDto();
        List<AttributeKeyValueDto> attributeKeyValueList = new ArrayList<>();

        for (KeyValueDto speakerAttribute : speakerAttributeMappedList) {
            if (attributes.containsKey(speakerAttribute.getKey())) {
                attributeKeyValueList.add(new AttributeKeyValueDto(
                        speakerAttribute.getValue(), attributes.get(speakerAttribute.getKey())
                ));
            }
        }
        speakerAttributeDetailsDto.setAttributes(attributeKeyValueList);
        speakerDTO.setSpeakerAttributeDetailsDto(speakerAttributeDetailsDto);
    }

    private void setSessionAttributeMapped(SessionDTO speakerDTO,
                                           Map<String, String> attributes,
                                           List<KeyValueDto> speakerAttributeMappedList) {
        CustomAttributeDetailsDto speakerAttributeDetailsDto = new CustomAttributeDetailsDto();
        List<AttributeKeyValueDto> attributeKeyValueList = new ArrayList<>();

        for (KeyValueDto speakerAttribute : speakerAttributeMappedList) {
            if (attributes.containsKey(speakerAttribute.getKey())) {
                attributeKeyValueList.add(new AttributeKeyValueDto(
                        speakerAttribute.getValue(), attributes.get(speakerAttribute.getKey())
                ));
            }
        }
        speakerAttributeDetailsDto.setAttributes(attributeKeyValueList);
        speakerDTO.setCustomAttributeDetailsDto(speakerAttributeDetailsDto);
    }

    private Long createSession(Long registrationReqId, Map<String, String> sessionDetail, Map<String, String> registrantAttribute, Event event, User user, Ticketing ticketing, List<Long> availableTicketsInEvent, SpeakerApprovalResponseDTO speakerApprovalResponseDTO){
        if(!CollectionUtils.isEmpty(sessionDetail)
                && StringUtils.isNotEmpty(sessionDetail.get(SPEAKER_APPROVAL_SESSION_TOPIC))
                && StringUtils.isNotEmpty(sessionDetail.get(SPEAKER_APPROVAL_SESSION_DESCRIPTION))){

            log.info("request received to create a session for registration request {} by user {}", registrationReqId, user.getUserId());
            List<AttributeValueType> attributeValueTypes = Arrays.asList(AttributeValueType.TEXT, AttributeValueType.LONG_TEXT, AttributeValueType.DROPDOWN, AttributeValueType.NUMBER, AttributeValueType.IMAGE, AttributeValueType.DATE, AttributeValueType.MULTIPLE_CHOICE);
            List<KeyValueDto> sessionAttributeMappedList = customFormAttributeService.getSpeakerAttributeByEventAndAttributeValueTypeList(event,attributeValueTypes, SPEAKER_DETAILS);

            String sessionTitle = sessionDetail.get(SPEAKER_APPROVAL_SESSION_TOPIC);
            String speakerName = Constants.STRING_EMPTY;
            if(!CollectionUtils.isEmpty(registrantAttribute)){
                speakerName = registrantAttribute.get(FIRST_NAME) + STRING_BLANK + registrantAttribute.get(LAST_NAME);
            }

            List<Session> sessions = sessionService.findSessionByEventAndPrefixTitleLike(event, sessionTitle);
            sessionTitle = getSessionTitle(sessionTitle, sessions, speakerName, 0);
            speakerApprovalResponseDTO.setSessionName(sessionTitle);

            SessionDTO sessionDTO = getSessionDTO(sessionTitle, sessionDetail, ticketing, speakerApprovalResponseDTO, event, sessionAttributeMappedList);
            sessionDTO.setTicketTypesThatCanBeRegistered(availableTicketsInEvent);

            try {
                sessionDTO.setStatus(EnumSessionStatus.DRAFT);
                Long sessionId = sessionService.createSession(event, sessionDTO);
                log.info("Session {} successfully create for registration request {}, by user {}", sessionDTO.getTitle(), registrationReqId, user.getUserId());
                return sessionId;
            }
            catch (Exception e){
                log.info("failed to create Session {}, error {}", sessionDTO.getTitle(), e.getMessage());
                speakerApprovalResponseDTO.addFailureMessage(e.getMessage());
            }
        }
        log.info("Session Details missing in registration request {}", registrationReqId);
        speakerApprovalResponseDTO.addFailureMessage("Session Details not Found in Registration Request");
        return null;
    }

    private SessionDTO getSessionDTO(String sessionTitle, Map<String, String> sessionDetails, Ticketing ticketing, SpeakerApprovalResponseDTO speakerApprovalResponseDTO, Event event, List<KeyValueDto> sessionAttributeMappedList) {
        SessionDTO sessionDTO = new SessionDTO();
        sessionDTO.setTitle(sessionTitle);
        sessionDTO.setDescription(sessionDetails.get(SPEAKER_APPROVAL_SESSION_DESCRIPTION));
        setSessionTime(ticketing, sessionDTO, sessionDetails.get(SPEAKER_APPROVAL_SESSION_DURATION), speakerApprovalResponseDTO, event);
        sessionDTO.setFormat(EnumSessionFormat.BREAKOUT_SESSION);
        if(StringUtils.isNotEmpty(sessionDetails.get(SPEAKER_APPROVAL_SESSION_DOCUMENTS))){
            try {
                JSONArray documents = new JSONArray(sessionDetails.get(SPEAKER_APPROVAL_SESSION_DOCUMENTS));
                List<KeyValueDto> docuemntList = new ArrayList<>();
                for(int index=0; index<documents.length(); index++){
                    JSONObject jsonObject = documents.getJSONObject(index);
                    if((jsonObject.has(ID) && !jsonObject.isNull(ID) && StringUtils.isNotEmpty(jsonObject.getString(ID)))
                            && (jsonObject.has(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME) && !jsonObject.isNull(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME) && StringUtils.isNotEmpty(jsonObject.getString(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME)))){
                        docuemntList.add(new KeyValueDto(jsonObject.getString(ID), jsonObject.getString(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME)));
                    }
                }
                if(!CollectionUtils.isEmpty(docuemntList)){
                    sessionDTO.setDocumentKeyValue(docuemntList);
                }
            }
            catch (Exception e){
                log.info("failed to upload document for session {}", sessionTitle);
            }
        }
        if(!CollectionUtils.isEmpty(sessionAttributeMappedList)) {
            setSessionAttributeMapped(sessionDTO, sessionDetails, sessionAttributeMappedList);
        }
        return sessionDTO;
    }

    private void setSessionTime(Ticketing ticketing, SessionDTO sessionDTO, String sessionDuration, SpeakerApprovalResponseDTO speakerApprovalResponseDTO, Event event){
        String startDate = DateUtils.getDateString(TimeZoneUtil.getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone()),LOCAL_DATE_FORMAT);
        sessionDTO.setStartTime(startDate);

        int duration = 60;
        if(StringUtils.isNotEmpty(sessionDuration) && org.apache.commons.lang3.math.NumberUtils.isParsable(sessionDuration)){
            duration = org.apache.commons.lang3.math.NumberUtils.createInteger(sessionDuration.strip());
        }
        Calendar sessionEndTime = Calendar.getInstance();
        sessionEndTime.setTime(TimeZoneUtil.getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone()));
        sessionEndTime.add(Calendar.MINUTE, duration);

        String endDate = DateUtils.getDateString(sessionEndTime.getTime(),LOCAL_DATE_FORMAT);
        sessionDTO.setEndTime(endDate);

        speakerApprovalResponseDTO.setSessionStartDateTime(ticketing.getEventStartDate().getTime());
        speakerApprovalResponseDTO.setSessionEndDateTime(ticketing.getEventStartDate().getTime());
    }

    private String getSessionTitle(String title, List<Session> sessions, String speakerName, int index) {
        if(CollectionUtils.isEmpty(sessions)){
            return title;
        }
        else{
            if (index == 0){
                Optional<Session> optionalSession = sessions.stream().filter(session -> title.equalsIgnoreCase(session.getTitle())).findFirst();
                if(optionalSession.isEmpty()){
                    return title;
                }
                else{
                    String finalTitle = title + " - " + speakerName;
                    optionalSession = sessions.stream().filter(session -> finalTitle.equalsIgnoreCase(session.getTitle())).findFirst();
                    if(optionalSession.isEmpty()){
                        return finalTitle;
                    }
                    else{
                        return getSessionTitle(finalTitle, sessions, speakerName, ++index);
                    }
                }
            }
            else{
                String finalTitle = title + " - "+index;
                Optional<Session> optionalSession = sessions.stream().filter(session -> finalTitle.equalsIgnoreCase(session.getTitle())).findFirst();
                if(optionalSession.isEmpty()){
                    return finalTitle;
                }
                else{
                    return getSessionTitle(title, sessions, speakerName, ++index);
                }
            }
        }
    }

    /***
     * Register Exhibitor & Exhibitor Staff after request approve
     * @param registrationRequest
     * @param registrationResponseDto
     * @param event
     * @param user
     * @param exhibitorApprovalResponseDTO
     * @param staffDetail
     */

    private ExhibitorDto createBooth(RegistrationRequest registrationRequest, RegistrationResponseDto registrationResponseDto, Event event, User user, ExhibitorApprovalResponseDTO exhibitorApprovalResponseDTO, StaffDetail staffDetail){
        log.info("request received to create exhibitor after approve registration request {} by user {}", registrationRequest.getId(), user.getUserId());
        ExhibitorDto exhibitorDto= prepareExpoDTO(registrationResponseDto.getBoothDetail(), registrationResponseDto.getAttributes(), staffDetail, event);
        if(StringUtils.isNotEmpty(exhibitorDto.getExhibitorName())){

            return exhibitorService.addExhibitor(exhibitorDto, event);
        }
        exhibitorApprovalResponseDTO.addFailureMessage("Expo Details not Found in Registration Request");
        return null;
    }

    private ExhibitorDto prepareExpoDTO(Map<String, String> boothDetail, Map<String, String> attributes, StaffDetail staffDetail, Event event) {
        ExhibitorDto exhibitorDto = new ExhibitorDto();

        prepareExhibitorStaffDTO(attributes, staffDetail);

        if(StringUtils.isNotEmpty(staffDetail.getFirstName()) && StringUtils.isNotEmpty(staffDetail.getLastName())
                && StringUtils.isNotEmpty(staffDetail.getEmail())){
            List<Exhibitor> exhibitors = exhibitorService.findAllByEvent(event);
            if(StringUtils.isNotEmpty(boothDetail.get(EXHIBITOR_APPROVAL_BOOTH_NAME))){
                exhibitorDto.setExhibitorName(getExpoName(boothDetail.get(EXHIBITOR_APPROVAL_BOOTH_NAME), exhibitors, staffDetail.getFirstName() +" - " + staffDetail.getLastName(),0));
            }

            if(StringUtils.isNotEmpty(boothDetail.get(EXHIBITOR_APPROVAL_BOOTH_NAME))){
                exhibitorDto.setLogo(boothDetail.get(EXHIBITOR_APPROVAL_BOOTH_LOGO));
            }

            if(StringUtils.isNotEmpty(boothDetail.get(EXHIBITOR_APPROVAL_BOOTH_DOCUMENTS))){
                try {
                    JSONArray documents = new JSONArray(boothDetail.get(EXHIBITOR_APPROVAL_BOOTH_DOCUMENTS));
                    List<KeyValueDto> docuemntList = new ArrayList<>();
                    for(int index=0; index<documents.length(); index++){
                        JSONObject jsonObject = documents.getJSONObject(index);
                        if((jsonObject.has(ID) && !jsonObject.isNull(ID) && StringUtils.isNotEmpty(jsonObject.getString(ID)))
                                && (jsonObject.has(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME) && !jsonObject.isNull(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME) && StringUtils.isNotEmpty(jsonObject.getString(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME)))){
                            docuemntList.add(new KeyValueDto(jsonObject.getString(ID), jsonObject.getString(SPEAKER_APPROVAL_SESSION_DOCUMENTS_NAME)));
                        }
                    }
                    if(!CollectionUtils.isEmpty(docuemntList)){
                        exhibitorDto.setDocumentKeyValue(docuemntList);
                    }
                }
                catch (Exception e){
                    log.info("failed to upload document for session {}", exhibitorDto.getExhibitorName());
                }
            }
        }

        return exhibitorDto;
    }


    private StaffDetail prepareExhibitorStaffDTO(Map<String, String> exhibitorAttribute, StaffDetail staffDetail){
        if(StringUtils.isNotEmpty(exhibitorAttribute.get(FIRST_NAME))){
            staffDetail.setFirstName(exhibitorAttribute.get(FIRST_NAME));
        }
        if(StringUtils.isNotEmpty(exhibitorAttribute.get(LAST_NAME))){
            staffDetail.setLastName(exhibitorAttribute.get(LAST_NAME));
        }
        if(StringUtils.isNotEmpty(exhibitorAttribute.get(EMAIL))){
            staffDetail.setLastName(exhibitorAttribute.get(EMAIL));
        }
        if(StringUtils.isNotEmpty(exhibitorAttribute.get(EMAIL_ADDRESS))){
            staffDetail.setEmail(exhibitorAttribute.get(EMAIL_ADDRESS));
        }
        staffDetail.setRole(StaffRole.exhibitoradmin.name());
        return staffDetail;
    }

    private String getExpoName(String title, List<Exhibitor> exhibitors, String expoAdminName, int index) {
        if(CollectionUtils.isEmpty(exhibitors)){
            return title;
        }
        else{
            if (index == 0){
                Optional<Exhibitor> optionalExhibitor = exhibitors.stream().filter(exhibitor -> title.equalsIgnoreCase(exhibitor.getName())).findFirst();
                if(optionalExhibitor.isEmpty()){
                    return title;
                }
                else{
                    String finalTitle = title + " - " + expoAdminName;
                    optionalExhibitor = exhibitors.stream().filter(exhibitor -> finalTitle.equalsIgnoreCase(exhibitor.getName())).findFirst();
                    if(optionalExhibitor.isEmpty()){
                        return finalTitle;
                    }
                    else{
                        return getExpoName(finalTitle, exhibitors, expoAdminName, ++index);
                    }
                }
            }
            else{
                String finalTitle = title + " - "+index;
                Optional<Exhibitor> optionalExhibitor = exhibitors.stream().filter(session -> finalTitle.equalsIgnoreCase(session.getName())).findFirst();
                if(optionalExhibitor.isEmpty()){
                    return finalTitle;
                }
                else{
                    return getExpoName(title, exhibitors, expoAdminName, ++index);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void updateRegistrationRequestForResendCheckoutLink(Event event, User user, List<Long> requestIds) {
        if (!CollectionUtils.isEmpty(requestIds)) {
            List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByEventAndIds(event, requestIds);
            registrationRequests.forEach(e -> {
                        e.setRecStatus(RecordStatus.UPDATE);
                        e.setUpdatedAt(new Date());
                        e.setUpdatedBy(user);
                        e.setCheckoutLinkExpired(false);
                        e.setRecentApprovedAt(new Date());
                    }
            );
            registrationRequestRepository.saveAll(registrationRequests).forEach(request ->
                    generateAndSendCheckoutListForRegistrationRequest(request.getId(), event)
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void resendCheckoutLink(Event event, User user) {
        List<RegistrationRequest> registrationRequests = registrationRequestRepository.findByEventIdAndUserAndType(event.getEventId(), user, RegistrationRequestType.ATTENDEE);
        if(CollectionUtils.isEmpty(registrationRequests)){
            throw new NotFoundException(NotFoundException.NotFound.REGISTRATION_NOT_EXISTS);
        }
        RegistrationRequest registrationRequest = registrationRequests.get(0);
        if(!RegistrationRequestStatus.APPROVED.equals(registrationRequest.getStatus())){
            throw new NotFoundException(NotFoundException.NotFound.REGISTRATION_REQUEST_NOT_APPROVED);
        }
        generateAndSendCheckoutListForRegistrationRequest(registrationRequest.getId(), event);
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void resendCheckoutLink(Event event, Long regRequestId) {
        RegistrationRequest registrationRequest = getRegistrationRequestsOrThrowError(event, regRequestId);
        if (!RegistrationRequestStatus.APPROVED.equals(registrationRequest.getStatus())) {
            throw new NotFoundException(NotFoundException.NotFound.REGISTRATION_REQUEST_NOT_APPROVED);
        }
        generateAndSendCheckoutListForRegistrationRequest(registrationRequest.getId(), event);
    }

    private void sendRegistrationEmailWhenAuto(Event event, User loggedInUser, RegistrationRequest registrationRequest, RegistrationRequestType registrationRequestType) {
        if(RegistrationRequestStatus.APPROVED.equals(registrationRequest.getStatus())) {
            generateAndSendCheckoutListForRegistrationRequest(registrationRequest.getId(), event);
        }
        if (getAutoSendEmailOrNot(event, registrationRequest.getStatus(), registrationRequestType)) {
            registrationApprovalEmailService.sendRegistrationMail(event, loggedInUser, registrationRequest.getId());
        }
    }

    private void addHistory(RegistrationRequest registrationRequest, RegistrationRequestStatus status, User user) {
        ChangeHistory changeHistory = new ChangeHistory(registrationRequest.getId(), "registration_request", "status",
                registrationRequest.getStatus().name(), status.name(), new Date(), user);
        changeHistoryRepoService.save(changeHistory);
    }

    @Override
    public void updateRegistrationRequestNote(Event event, User user, Long id, String note) {

        RegistrationRequest registrationRequest = getRegistrationRequestsOrThrowError(event, id);

        registrationRequest.setNotes(note);
        registrationRequest.setUpdatedBy(user);
        registrationRequest.setUpdatedAt(new Date());
        registrationRequest.setRecStatus(RecordStatus.UPDATE);
        registrationRequestRepository.save(registrationRequest);
    }

    @Override
    public RegistrationRequest getRegistrationRequestsOrThrowError(Event event, Long id) {
        return registrationRequestRepository.findByEventAndId(event, id)
                .orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.REGISTRATION_NOT_EXISTS));
    }

    @Override
    public List<RegistrationRequestStatusChangeDto> getRegistrationRequestHistory(Event event, Long id) {
        getRegistrationRequestsOrThrowError(event, id);

        return changeHistoryRepoService.findByRecordIdAndTableNameAndFieldName(id, "registration_request", "status");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRegistrationRequest(Event event, User user, List<Long> requestIds) {
        if (!CollectionUtils.isEmpty(requestIds)) {
            log.info("deleteRegistrationRequest | eventId {}  userId {} requestIds {}", event.getEventId(), user.getUserId(), requestIds);
            List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByEventAndIds(event, requestIds);
            registrationRequests.forEach(e -> {
                        e.setRecStatus(RecordStatus.DELETE);
                        e.setUpdatedBy(user);
                        e.setUpdatedAt(new Date());
                    }
            );
            registrationRequestRepository.saveAll(registrationRequests);
            if (!CollectionUtils.isEmpty(registrationRequests)) {
                reviewerFormResponseRepository.deleteByRegistrationRequestIds(registrationRequests.stream().map(RegistrationRequest::getId).collect(Collectors.toList()));
                Set<ReviewProcessRegistrationMapping> reviewProcessRegistrationMappings = reviewProcessRegistrationMappingRepository.findByRegistrationRequestIds(new HashSet<>(requestIds));
                if (!CollectionUtils.isEmpty(reviewProcessRegistrationMappings)) {
                    for (ReviewProcessRegistrationMapping mapping : reviewProcessRegistrationMappings) {
                        mapping.setRecStatus(RecordStatus.DELETE);

                        // Break shared reference
                        if (mapping.getReviewProcessReviewerMapping() != null) {
                            mapping.setReviewProcessReviewerMapping(new ArrayList<>(mapping.getReviewProcessReviewerMapping()));
                        }
                    }
                    reviewProcessRegistrationMappingRepository.saveAll(reviewProcessRegistrationMappings);
                    log.info("deleteRegistrationRequest | reviewProcessRegistrationMappings deleted successfully");
                }

            }
        }
    }

    @Override
    public void downloadRegistrationRequests(Event event, PrintWriter writer, List<Long> requestIds) throws JAXBException {

        List<RegistrationRequestsDetailsDto> registrationRequests = registrationRequestRepository.findAllByEventIdAndRequestIds(event.getEventId(), requestIds);
        for (RegistrationRequestsDetailsDto dto : registrationRequests) {
            RegistrationRequest registrationRequest = getRegistrationRequestsOrThrowError(event, dto.getId());
            downloadTicketHolderData(writer, event, registrationRequest);
        }
    }

    public void downloadTicketHolderData(PrintWriter writer, Event event, RegistrationRequest registrationRequest) throws JAXBException {

        List<String> headerList = new ArrayList<>();
        List<List<String>> registrantData = new ArrayList<>();

        log.info("setRequestCSVDataForEvent -> start : {}" , System.currentTimeMillis());
        setRequestCSVDataForEvent(event, headerList, registrantData, registrationRequest);
        log.info("setRequestCSVDataForEvent -> End : {}" , System.currentTimeMillis());

        log.info("downloadCSVFile -> Start : {}" , System.currentTimeMillis());
        downloadService.downloadCSVFile(writer, headerList, registrantData);
        log.info("downloadCSVFile -> End : {}" , System.currentTimeMillis());
    }

    public void setRequestCSVDataForEvent(Event event, List<String> headerList, List<List<String>> registrantData,
                                          RegistrationRequest registrationRequest) throws JAXBException {

        long startTime = System.currentTimeMillis();
        List<RegistrationAttribute> holderRequiredAttributes =
                registrationAttributeService.getRegistrationRequiredAttributesOrderByAttributeOrder(event, registrationRequest.getRecurringEventId(), registrationRequest.getType());

        if (CollectionUtils.isEmpty(holderRequiredAttributes)) {
            holderRequiredAttributes = registrationAttributeService.addDefaultAttributes(event, registrationRequest.getRecurringEventId(), registrationRequest.getType());
        }

        log.info("ticketHolderRequiredAttributesService -> Query : {}", (System.currentTimeMillis() - startTime));
        if (holderRequiredAttributes.isEmpty()) {
            return;
        }

        headerList.add("Request Id");
        // set csv headers
        List<RegistrationAttribute> listOfAttributesHavingOnlyEnable = Collections.emptyList();
        if (!holderRequiredAttributes.isEmpty()) {
            listOfAttributesHavingOnlyEnable = addRequiredFieldsOfCsvByAttribute(headerList, holderRequiredAttributes);
        }
        TicketingModuleDTO ticketingDto = ticketingHelperService.findTicketingByEventId(event);
        boolean isSeatingEnabled = StringUtils.isNotBlank(ticketingDto.getChartKey());
        addConditionalHeaders(headerList, ticketingDto.isRecurringEvent(), isSeatingEnabled);

        RegistrationDisplayAttributesDto registrationDisplayAttributesDto = getRegistrantDetails(registrationRequest.getId(), registrationRequest.getRecurringEventId(), registrationRequest.getEvent());
        if (registrationDisplayAttributesDto != null && !CollectionUtils.isEmpty(listOfAttributesHavingOnlyEnable)) {
            List<String> data = new ArrayList<>();
            data.add(String.valueOf(registrationRequest.getId()));

            listOfAttributesHavingOnlyEnable.removeIf(attribute -> isNumberGreaterThanZero(attribute.getParentQuestionId()));

            for (RegistrationAttribute attribute : listOfAttributesHavingOnlyEnable) {
                prepareDataList(data, registrationDisplayAttributesDto, attribute);
            }

            Map<Long, String> recurringIdAndDateMap = recurringEventsScheduleService.getRecurringEventByIdsIn(
                    new HashSet<>(Collections.singletonList(registrationRequest.getRecurringEventId())));
            String recurringStartDate = recurringIdAndDateMap != null && recurringIdAndDateMap.get(registrationRequest.getRecurringEventId()) != null ?
                    recurringIdAndDateMap.get(registrationRequest.getRecurringEventId()) : "";
            data.add(recurringStartDate);
            registrantData.add(data);
            log.info("Loop 3 ->  Time :{}", (System.currentTimeMillis() - startTime));
        }
        log.info("Successfully CSV data prepared for Ticket Holder CSV, Loop execution time : {}", (System.currentTimeMillis() - startTime));

    }

    public void prepareDataList(List<String> ticketHolderData, RegistrationDisplayAttributesDto registrationDisplayAttributesDto,
                                RegistrationAttribute attribute) {

        if (attribute.getName().equals(BIRTHDAY)) {
            String birthday = getAttributeValue1(registrationDisplayAttributesDto, attribute);

            if (null != birthday) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
                LocalDate date = LocalDate.parse(birthday, formatter);
                ticketHolderData.add(date.toString());
                return;
            }
        }

        if (AttributeValueType.DATE.equals(attribute.getValueType())) {
            String inputDateString = getAttributeValue1(registrationDisplayAttributesDto, attribute);

            if(StringUtils.isEmpty(inputDateString)){
                ticketHolderData.add(STRING_EMPTY);
                return;
            }
           else{
               ticketHolderData.add(inputDateString);
            }
        }else if (STRING_CELL_SPACE_PHONE.equals(attribute.getName())) {
            for (DisplayAttributeDto attributeDto : registrationDisplayAttributesDto.getAttributes()) {
                if (STRING_CELL_SPACE_PHONE.equals(attributeDto.getName())) {
                    String cellPhoneString = attributeValueCheck(attributeDto.getValue());
                    if(StringUtils.isNotEmpty(cellPhoneString) && cellPhoneString.length() > 4){
                        cellPhoneString = !StringUtils.isEmpty(cellPhoneString) ? cellPhoneString.substring(0, cellPhoneString.length() - 3) : cellPhoneString;
                    }else{
                        cellPhoneString = STRING_EMPTY;
                    }
                   ticketHolderData.add(cellPhoneString);
                    break;
                }
            }
        } else if (attribute.getValueType().getType().equalsIgnoreCase(AttributeValueType.IMAGE.getType())) {
            String url;
            url = getAttributeValue1(registrationDisplayAttributesDto, attribute);
            if (StringUtils.isNotEmpty(url)) {
                url = uploadsUrl.replace("ticket_buyer_uploads/", StringUtils.EMPTY) + url;
            }
            ticketHolderData.add(url);
        } else if (attribute.getValueType().getType().equalsIgnoreCase(AttributeValueType.UPLOAD.getType())) { //NOSONAR
            String url = getAttributeValue1(registrationDisplayAttributesDto, attribute);
            if (StringUtils.isNotBlank(url)) {
                url = uploadsUrl.replace("ticket_buyer_uploads/", StringUtils.EMPTY) + url;
            }
            ticketHolderData.add(url);
        } else if (attribute.getValueType().getType().equalsIgnoreCase(AttributeValueType.SINGLE_CHECKBOX.getType())) { //NOSONAR
            String singleCheckBoxValue = getAttributeValue1(registrationDisplayAttributesDto, attribute);
            if (singleCheckBoxValue == null) {
                String defaultValueJson = attribute.getDefaultValueJson();
                if (defaultValueJson != null) {
                    JSONObject jsonObjectHolder;
                    try {
                        jsonObjectHolder = new JSONObject(defaultValueJson);
                        singleCheckBoxValue = jsonObjectHolder.get("defaultValue").toString();
                    } catch (JSONException e) {
                        log.info("singleCheckBoxValue parseIssue attributeId {} error {}", attribute.getId(), e.getMessage());
                    }
                }
            }
            ticketHolderData.add(TRUE.equalsIgnoreCase(singleCheckBoxValue) ? "Checked" : "Unchecked");
        } else if (registrationDisplayAttributesDto != null && attribute.isAttribute() && AttributeValueType.CONDITIONAL_QUE.equals(attribute.getValueType())
                && (attribute.getParentQuestionId() == null || attribute.getParentQuestionId() == 0)) {
            List<DisplayNestedQueDto> holderSubQueAttribute = registrationDisplayAttributesDto.getNestedQuestions();
            if (!CollectionUtils.isEmpty(holderSubQueAttribute)) {
                for (DisplayNestedQueDto displayNestedQueDto : holderSubQueAttribute) {
                    if (displayNestedQueDto.getId().equals(attribute.getId()) || (displayNestedQueDto.getParentQueId() != null && displayNestedQueDto.getParentQueId().equals(attribute.getId()))) {
                        String value = attributeValueCheck(displayNestedQueDto.getValue());
                        ticketHolderData.add(value);
                    }
                }

            }
        } else {
            String value = getAttributeValue1(registrationDisplayAttributesDto, attribute);
            ticketHolderData.add(value);
        }
    }

    public static String getAttributeValue1(RegistrationDisplayAttributesDto registrationDisplayAttributesDto, RegistrationAttribute attribute) {
        if (registrationDisplayAttributesDto != null) {
            if (attribute.isAttribute()) {
                List<DisplayAttributeDto> holderAttribute = new ArrayList<>();
                if (RegistrationAttributeType.BOOTH_DETAILS.equals(attribute.getType())) {
                    holderAttribute = registrationDisplayAttributesDto.getBoothDetail();
                } else if (RegistrationAttributeType.SPEAKER_DETAILS.equals(attribute.getType())) {
                    holderAttribute = registrationDisplayAttributesDto.getSessionDetail();
                } else if (RegistrationAttributeType.SPEAKER_INFO.equals(attribute.getType())) {
                    List<SpeakerRequestDisplayAttributesDto> speakerInfo = registrationDisplayAttributesDto.getSpeakerDetails();
                    for (SpeakerRequestDisplayAttributesDto speakerInfoDTO : speakerInfo) {
                        if (speakerInfoDTO.getAttributes() != null && !CollectionUtils.isEmpty(speakerInfoDTO.getAttributes())) {
                            holderAttribute.addAll(speakerInfoDTO.getAttributes());
                        }
                    }
                } else {
                    holderAttribute = registrationDisplayAttributesDto.getAttributes();
                }
                if (holderAttribute != null && !CollectionUtils.isEmpty(holderAttribute)) {
                    for (DisplayAttributeDto attributeDto : holderAttribute) {
                        if (attribute.getName().equalsIgnoreCase(attributeDto.getName()) || (attribute.getType().equals(RegistrationAttributeType.ATTENDEE) && (EMAIL_ADDRESS.equalsIgnoreCase(attributeDto.getName()) || EMAIL.equalsIgnoreCase(attributeDto.getName())))) {
                            return attributeValueCheck(attributeDto.getValue());
                        }
                    }
                }
            } else {
                List<DisplayAttributeDto> holderQue = registrationDisplayAttributesDto.getQuestions();
                if (holderQue != null && !CollectionUtils.isEmpty(holderQue)) {
                    for (DisplayAttributeDto entry : holderQue) {
                        if (attribute.getName().equalsIgnoreCase(entry.getName())) {
                            return attributeValueCheck(entry.getValue());
                        }
                    }
                }
            }
        }
        return null;
    }

    public static boolean isBillingOrShipping(RegistrationAttribute attributes) {
        return AttributeValueType.BILLING_ADDRESS.equals(attributes.getValueType()) ||
                AttributeValueType.SHIPPING_ADDRESS.equals(attributes.getValueType());
    }

    private void addConditionalHeaders(List<String> headerList, boolean isRecurringEvent, boolean isSeatingEnabled) {
        if (isSeatingEnabled) {
            headerList.add("Seat Number");
        }
        if (isRecurringEvent) {
            headerList.add("Recurring Event Date");
        }
    }

    public List<RegistrationAttribute> addRequiredFieldsOfCsvByAttribute(List<String> headerList, List<RegistrationAttribute> registrationAttributes) {
        List<RegistrationAttribute> listOfEnabledAttributes = registrationAttributes.stream().filter(RegistrationAttribute::isEnabled).collect(Collectors.toList());
        listOfEnabledAttributes.forEach(attribute -> headerList.add(attribute.getName()));
        return listOfEnabledAttributes;
    }

    @Override
    public RegistrationDisplayAttributesDto getRegistrantDetails(long requestId, long recurringEventId, Event event) throws JAXBException {
        RegistrationRequest registrationRequest = getRegistrationRequestsOrThrowError(event, requestId);
        if(RegistrationRequestType.ATTENDEE.equals(registrationRequest.getType())){
            RegistrationDisplayAttributesDto registrationDisplayAttributesDto = getAttendeeRegistrantAttributeDto(event, recurringEventId, registrationRequest);
            // Set holder details for the attendee request
            List<AttendeeAttributeWithTypeAndTableDto> holders = setHolderAttributesDetailsForRegistrationApproval(event,registrationRequest);
            registrationDisplayAttributesDto.setHolders(holders);
            return registrationDisplayAttributesDto;
        }
        return getRegistrantAttributeDto(event, recurringEventId, registrationRequest);
    }

    private List<AttendeeAttributeWithTypeAndTableDto> setHolderAttributesDetailsForRegistrationApproval(Event event, RegistrationRequest registrationRequest) {
        List<AttendeeAttributeWithTypeAndTableDto> attendeeAttributeList = new ArrayList<>();
        List<RegistrationRequestTicket> tickets = registrationRequestTicketRepository.findByRegistrationRequest(registrationRequest);
        log.info("Start fetching holder attributes for RegistrationRequest ID: {}, Event ID: {} and Total tickets found: {}",registrationRequest.getId(), event.getEventId(),tickets.size());
        List<Long> ticketTypeIds = tickets.stream().map(t -> t.getTicketType().getId()).distinct().collect(Collectors.toList());
        List<RegistrationRequestHolderDetails> allHolderDetails = registrationRequestHolderDetailsRepository.findByRegistrationRequestId(registrationRequest.getId());
        log.info("Total holder details found: {}", allHolderDetails.size());
        Map<Long, List<RegistrationRequestHolderDetails>> holderDetailsByTicketType = allHolderDetails.stream().collect(Collectors.groupingBy(RegistrationRequestHolderDetails::getTicketTypeId));

        List<TicketHolderRequiredAttributes> requiredAttributes =
                (registrationRequest.getRecurringEventId() != null && isNumberGreaterThanZero(registrationRequest.getRecurringEventId()))
                        ? ticketRequiresAttributesRepo.findAllAttributesByEventAndRecurringEventId(event, registrationRequest.getRecurringEventId())
                        : ticketHolderRequiredAttributesService.findByEventId(event);

        Map<Long, Integer> ticketTypeCountMap = tickets.stream().collect(Collectors.groupingBy(t -> t.getTicketType().getId(), Collectors.summingInt(RegistrationRequestTicket::getNumberOfTicket)));
        List<TicketingType> ticketingTypes = ticketingTypeService.findByidInAndEvent(ticketTypeIds, event);
        Map<Long, TicketingType> ticketingTypeMap = ticketingTypes.stream().collect(Collectors.toMap(TicketingType::getId, Function.identity()));
        ticketTypeCountMap.forEach((ticketTypeId, numberOfTickets) -> {
            List<RegistrationRequestHolderDetails> holderDetails = holderDetailsByTicketType.get(ticketTypeId);
            if (!CollectionUtils.isEmpty(holderDetails)) {
                TicketingType ticketingType = ticketingTypeMap.get(ticketTypeId);
                for (int i = 0; i < numberOfTickets && i < holderDetails.size(); i++) {
                    RegistrationRequestHolderDetails detail = holderDetails.get(i);
                    TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(detail.getJsonValue());
                    Map<String, String> holderAttributesMap = (Map<String, String>) ticketAttributeValueDto.getHolder().get(Constants.TICKETING.ATTRIBUTES);

                    List<DisplayAttributeDto> attributesList = new ArrayList<>();
                    List<DisplayNestedQueDto> nestedQueList = new ArrayList<>();
                    List<DisplayAttributeDto> questionList = new ArrayList<>();

                    requiredAttributes.stream().sorted(Comparator.comparingInt(TicketHolderRequiredAttributes::getHolderAttributeOrder))
                            .forEach(holderRequiredAttribute -> {
                                if (!holderRequiredAttribute.getEnabledForTicketHolder()) return;
                                if (holderRequiredAttribute.getAttributeValueType().equals(AttributeValueType.CONDITIONAL_QUE)
                                        || isNumberGreaterThanZero(holderRequiredAttribute.getParentQuestionId())) {
                                    DisplayNestedQueDto nestedQue = getRegistrationDisplayNestedQue(holderRequiredAttribute, ticketTypeIds, ticketAttributeValueDto);
                                    if (holderRequiredAttribute.isAttribute()) nestedQueList.add(nestedQue);
                                    else questionList.add(nestedQue);
                                } else {
                                    DisplayAttributeDto attribute = getDisplayAttribute(holderRequiredAttribute, ticketTypeIds, holderAttributesMap);
                                    if (Constants.STRING_CELL_SPACE_PHONE.equals(attribute.getName())) {
                                        setCellPhoneValueForHolderData(attribute,holderAttributesMap);
                                    }
                                    if (holderRequiredAttribute.isAttribute()) attributesList.add(attribute);
                                    else questionList.add(attribute);
                                }
                            });
                    AttendeeAttributeWithTypeAndTableDto attendeeAttributeWithTypeAndTableDto = new AttendeeAttributeWithTypeAndTableDto();
                    attendeeAttributeWithTypeAndTableDto.setAttributes(filterAttributes(attributesList, ticketTypeId));
                    attendeeAttributeWithTypeAndTableDto.setNestedQuestion(filterNestedQue(nestedQueList, ticketTypeId));
                    attendeeAttributeWithTypeAndTableDto.setQuestions(questionList);
                    attendeeAttributeWithTypeAndTableDto.setHeader(ticketingType.getTicketTypeName() + " - " + (i + 1));
                    attendeeAttributeWithTypeAndTableDto.setTickettypeid(ticketTypeId);
                    attendeeAttributeList.add(attendeeAttributeWithTypeAndTableDto);
                }
            }
        });
        log.info("Completed fetching attendee attributes. Total DTOs created: {}", attendeeAttributeList.size());
        return attendeeAttributeList;
    }

    private void setCellPhoneValueForHolderData(DisplayAttributeDto attribute, Map<String, String> holderAttributesMap) {
        String phoneNumber = holderAttributesMap.getOrDefault(STRING_PHONE_NUMBER, STRING_EMPTY);
        String countryCodeValue = holderAttributesMap.getOrDefault(STRING_COUNTRY_CODE, STRING_EMPTY);
        String countryCode = STRING_EMPTY;
        if (StringUtils.isNotBlank(countryCodeValue)) {
            try {
                countryCode = CountryCode.valueOf(countryCodeValue.trim().toUpperCase()).getCode();
            } catch (IllegalArgumentException e) {
                log.warn("Invalid country code '{}' received. Defaulting to empty.", countryCodeValue);
            }
        }
        attribute.setValue(String.join(Constants.PIPE, countryCode, phoneNumber));
    }

    private List<DisplayAttributeDto> filterAttributes(List<DisplayAttributeDto> holderAttributesDto, long ticketTypeId) {
        log.info("Filtering attributes for ticketTypeId: {}, total attributes received: {}", ticketTypeId, holderAttributesDto.size());
        List<DisplayAttributeDto> filteredHolderAttributesDto = new ArrayList<>();

        for (DisplayAttributeDto attributeDto : holderAttributesDto) {
            DisplayAttributeDto displayAttributeDto = attributeDto.clone();
            displayAttributeDto.setMandatory(isAttributeMandatoryForTicketingType(ticketTypeId, displayAttributeDto.getRequiredTicketTypeId()));

            if (StringUtils.isNotBlank(displayAttributeDto.getOptionalTicketTypeId()) || StringUtils.isNotBlank(displayAttributeDto.getRequiredTicketTypeId())) {
                List<String> array = Stream.of(
                                GeneralUtils.convertCommaSeparatedToList(displayAttributeDto.getOptionalTicketTypeId()),
                                GeneralUtils.convertCommaSeparatedToList(displayAttributeDto.getRequiredTicketTypeId()),
                                GeneralUtils.convertCommaSeparatedToList(displayAttributeDto.getHiddenTicketTypeId()))
                        .flatMap(Collection::stream).collect(Collectors.toList());

                try {
                    boolean ticketTypeMatches = array.stream().filter(StringUtils::isNotBlank).anyMatch(arr -> Long.parseLong(arr) == ticketTypeId);
                    if (ticketTypeMatches) {
                        filteredHolderAttributesDto.add(displayAttributeDto);
                    }
                } catch (NumberFormatException e) {
                    log.error("Error while converting attributes string value to long", e);
                }
            } else {
                filteredHolderAttributesDto.add(displayAttributeDto);
            }
        }
        return filteredHolderAttributesDto;
    }

    private List<DisplayNestedQueDto> filterNestedQue(List<DisplayNestedQueDto> holderNestedQueDto, long ticketTypeId) {
        List<DisplayNestedQueDto> filteredHolderAttributesDto = new ArrayList<>();
        for (DisplayNestedQueDto attributeDto : holderNestedQueDto) {
            attributeDto.setMandatory(isAttributeMandatoryForTicketingType(ticketTypeId,attributeDto.getRequiredTicketTypeId()));
            if (StringUtils.isNotBlank(attributeDto.getOptionalTicketTypeId()) || StringUtils.isNotBlank(attributeDto.getRequiredTicketTypeId())) {
                List<String> array = Stream.of(
                        GeneralUtils.convertCommaSeparatedToList(attributeDto.getOptionalTicketTypeId()),
                        GeneralUtils.convertCommaSeparatedToList(attributeDto.getRequiredTicketTypeId()),
                        GeneralUtils.convertCommaSeparatedToList(attributeDto.getHiddenTicketTypeId())
                ).flatMap(java.util.Collection::stream).collect(Collectors.toList());

                try {
                    boolean ticketTypeMatches = array.stream().filter(StringUtils::isNotBlank).anyMatch(arr -> Long.parseLong(arr) == ticketTypeId);
                    if (ticketTypeMatches) {
                        filteredHolderAttributesDto.add(attributeDto);
                    }
                } catch (NumberFormatException e) {
                    log.error("Error while converting nested questions string value to long", e);
                }
            } else {
                filteredHolderAttributesDto.add(attributeDto);
            }
        }
        return filteredHolderAttributesDto;
    }

    private DisplayNestedQueDto getRegistrationDisplayNestedQue(TicketHolderRequiredAttributes requiredAttribute,List<Long> ticketingTypeIds,TicketAttributeValueDto1 ticketAttributeValueDto) {
        DisplayNestedQueDto displayNestedQueDto = new DisplayNestedQueDto();
        Map<Long, Map<String, Object>> holderNestedAttributesMap = new HashMap<>();
        List<Map<String, Object>> holderNestedAttributeList = (List<Map<String, Object>>) ticketAttributeValueDto.getHolder().get(Constants.TICKETING.NESTEDQUESTIONS);
        if(!CollectionUtils.isEmpty(holderNestedAttributeList)) {
            holderNestedAttributesMap = holderNestedAttributeList.stream().collect(Collectors.toMap(e -> (long)Double.parseDouble(String.valueOf(e.get("id"))), e -> e, (old1, new1) -> new1));
        }
        if (requiredAttribute.getAttributeValueType().equals(AttributeValueType.CONDITIONAL_QUE) || (isNumberGreaterThanZero(requiredAttribute.getParentQuestionId()))) {
            displayNestedQueDto.setName(requiredAttribute.getName());
            displayNestedQueDto.setType(requiredAttribute.getAttributeValueType().getType());
            displayNestedQueDto.setDefaultValue(requiredAttribute.getDefaultValueJsonHolder());
            displayNestedQueDto.setIsHidden(requiredAttribute.isHiddenForHolder());
            displayNestedQueDto.setHiddenInRegistrationForm(requiredAttribute.isHiddenForHolderRegistration());
            displayNestedQueDto.setMandatory(isAttributeMandatoryForTicketingType(ticketingTypeIds,requiredAttribute.getHolderRequiredTicketTypeId()));
            displayNestedQueDto.setPosition(requiredAttribute.getHolderAttributeOrder());
            displayNestedQueDto.setDefaultValue(requiredAttribute.getDefaultValueJsonHolder());
            displayNestedQueDto.setIsHidden(requiredAttribute.isInvisibleForHolder());
            displayNestedQueDto.setRequiredTicketTypeId(requiredAttribute.getHolderRequiredTicketTypeId());
            displayNestedQueDto.setOptionalTicketTypeId(requiredAttribute.getHolderOptionalTicketTypeId());
            displayNestedQueDto.setHiddenTicketTypeId(requiredAttribute.getHolderRegistrationHiddenTicketTypeId());
            displayNestedQueDto.setParentQueId(requiredAttribute.getParentQuestionId());
            displayNestedQueDto.setSelectedAnsId(requiredAttribute.getSelectedAnswer());
            displayNestedQueDto.setId(requiredAttribute.getId());
            Map<String, Object> nestedValue = holderNestedAttributesMap.get(requiredAttribute.getId());
            if (nestedValue != null && nestedValue.get(VALUE_SMALL) != null) {
                displayNestedQueDto.setValue(String.valueOf(nestedValue.get(VALUE_SMALL)));
            }
        }
        return displayNestedQueDto;
    }

    protected DisplayAttributeDto getDisplayAttribute(TicketHolderRequiredAttributes holderRequiredAttribute,List<Long> ticketingTypeIds,Map<String, String> holderAttributesMap) {
        DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
        displayAttributeDto.setId(holderRequiredAttribute.getId());
        displayAttributeDto.setName(holderRequiredAttribute.getName());
        displayAttributeDto.setValue(holderAttributesMap.get(displayAttributeDto.getName()));
        displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
        displayAttributeDto.setMandatory(isAttributeMandatoryForTicketingType(ticketingTypeIds,holderRequiredAttribute.getHolderRequiredTicketTypeId()));
        displayAttributeDto.setPosition(holderRequiredAttribute.getHolderAttributeOrder());
        displayAttributeDto.setDefaultValue(holderRequiredAttribute.getDefaultValueJsonHolder());
        displayAttributeDto.setIsHidden(holderRequiredAttribute.isInvisibleForHolder());
        displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getHolderRequiredTicketTypeId());
        displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getHolderOptionalTicketTypeId());
        displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getHolderRegistrationHiddenTicketTypeId());
        return displayAttributeDto;
    }


    public Map<Long, RegistrationDisplayAttributesDto> getRegistrantDetailsForCSV(List<RegistrationRequest> registrationRequests, Event event, RegistrationRequestType registrationRequestType){
        if(RegistrationRequestType.ATTENDEE.equals(registrationRequestType)){
            return getAttendeeRegistrantAttributeDetails(event, registrationRequests);
        }
        return getSessionExpoRegistrantDetails(event, registrationRequests, registrationRequestType);
    }



    @Override
    public List<RegistrantBasicDetailDto> getRegistrants(Event event, RegistrationRequestType type) {
        return registrationRequestRepository.findByEventAndRegistrationRequestTypeOrderByPhotoDesc(event, type, RegistrationRequestStatus.APPROVED);
    }

    @Override
    public TicketDisplayPageDto getTicketTypesDetailsForRegistrationRequestListCheckout(Event event, String reqIds, Long recurringEventId) {

        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId());
        if (StringUtils.isBlank(reqIds)) {
            throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.INVALID_REQ_LIST_IDS);
        }

        StripeDTO stripe = stripeService.getStripeFeesByEvent(event);

        TicketDisplayPageDto displayPageDto = new TicketDisplayPageDto();

        setTicketingDetails(event, displayPageDto);

        Long reqLongId = Long.parseLong((StringUtils.isNoneBlank(reqIds) && !"null".equalsIgnoreCase(reqIds)) ? reqIds.trim() : "0");
        String eventKey = seatsIoService.getEventKey(event.getEventId(), recurringEventId);
        RegistrationRequest request = registrationRequestRepository.findAllByEventAndIdsAndStatus(event, reqLongId, List.of(RegistrationRequestStatus.APPROVED,RegistrationRequestStatus.REGISTERED));

        if (request == null){
            throw new NotFoundException(NotFoundException.NotFound.REGISTRATION_REQUEST_NOT_AVAILABLE);
        }

        if (request.isCheckoutLinkExpired()) {
            throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.CHECKOUT_LINK_EXPIRE);
        }
        List<Long> ticketTypeIds = registrationRequestTicketRepository.findTicketingTypesByRegistrationRequestIds(Collections.singletonList(reqLongId));
        List<TicketingType> ticketingTypes = this.ticketingTypeService.findByidInAndEventForWaitList(ticketTypeIds, event);
        Ticketing ticketing=ticketingRepository.findByEventid(event);
        List<TicketingType> listOfAddons=ticketingTypeService.findAllAddonByTicketingId(ticketing);
        ticketingTypes.addAll(listOfAddons);
        displayPageDto.setCategories(seatingCategoryService.getCategoriesByEventWithDataType(event));
        for (TicketingType ticketingType : ticketingTypes) {
            long totalPurchaserCount=0;
            int totalRegisterRequestCount=0;
            if (!ticketingTypes.isEmpty() && Objects.nonNull(request) && DataType.TICKET.equals(ticketingType.getDataType())) {
                totalPurchaserCount = eventTicketsService.getEventTicketsByTicketingTypeIdAndPurchaserId(ticketingType,request.getRequestedForUser());
                RegistrationRequestTicket registrationRequestTickets = registrationRequestTicketRepository.findRequestTicketByTicketTypeAndRegistrationRequestIds(ticketingType, reqLongId);
                totalRegisterRequestCount = registrationRequestTickets.getNumberOfTicket();
            }
            long remainingTicket = ticketingStatisticsService.getRemainingTicketCount(ticketingType);

            TicketTypeDto ticketType = new TicketTypeDto();
            ticketType.setTypeId(ticketingType.getId());
            if (!DataType.TICKET.equals(ticketingType.getDataType())) {
                ticketType.setRemaniningTickets(remainingTicket);
                ticketType.setMinTickerPerBuyer(ticketingType.getMinTicketsPerBuyer());
                ticketType.setMaxTickerPerBuyer(ticketingType.getMaxTicketsPerBuyer());
            }else {
                ticketType.setMinTickerPerBuyer(0);
                if (totalPurchaserCount > 0) {
                    ticketType.setMaxTickerPerBuyer(totalRegisterRequestCount - totalPurchaserCount);
                } else {
                    ticketType.setMaxTickerPerBuyer(totalRegisterRequestCount);
                }
            }
            ticketType.setName(ticketingType.getTicketTypeName());
            ticketType.setCategoryId(ticketingType.getCategoryId());
            addTicketingTypeDetails(ticketingType, ticketType, ticketType.getMaxTickerPerBuyer(), stripe, salesTaxFeeDto, event);

            // If no ticket type configured then only return the remaining tickets.
            if (isEmpty(ticketTypeIds)) {
                if (remainingTicket > 0) {
                    displayPageDto.add(ticketType);
                }
            } else {
                displayPageDto.add(ticketType);
            }
        }
        displayPageDto.setTicketingFee(getTicketFeesLogic(event));
        displayPageDto.setEventKey(eventKey);
        return displayPageDto;
    }

    @Override
    public boolean getAutoSendEmailOrNot(Event event, RegistrationRequestStatus registrationRequestStatus, RegistrationRequestType registrationRequestType) {
        RegistrationApprovalEmail registrationApprovalEmail = registrationApprovalEmailRepository.findByEventIdAndRegistrationTypeAndEmailStatus(event.getEventId(), registrationRequestType, registrationRequestStatus);
        return registrationApprovalEmail == null || registrationApprovalEmail.isAutoSendMail();
    }

    protected List<TicketingFeeDto> getTicketFeesLogic(Event event) {
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = transactionFeeConditionalLogicService.getRecordByEvent(event);
        if (!transactionFeeConditionalLogics.isEmpty()) {
            List<TicketingFeeDto> ticketingFeeDtos = new ArrayList<>();
            CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);

            transactionFeeConditionalLogics.forEach(e -> ticketingFeeDtos.add(new TicketingFeeDto(e, creditCardChargesDto)));

            return ticketingFeeDtos;
        }
        TicketingFeeDto ticketingFeeDto = new TicketingFeeDto();
        ticketingFeeDto.setAeFeeFlat(AE_FLAT_FEE_ONE);
        ticketingFeeDto.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
        CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);
        ticketingFeeDto.setCreditCardProcessingFlat(creditCardChargesDto.getCreditCardFlat());
        ticketingFeeDto.setCreditCardProcessingPercentage(creditCardChargesDto.getCreditCardPercentage());

        if (event.getWhiteLabel() != null) {
            ticketingFeeDto.setWlFeeFlat(WL_FEE_FLAT);
            ticketingFeeDto.setWlFeePercentage(WL_FEE_PERCENTAGE);
        } else {
            ticketingFeeDto.setWlFeeFlat(0);
            ticketingFeeDto.setWlFeePercentage(0);
        }

        return (Collections.singletonList(ticketingFeeDto));
    }

    protected void setTicketingDetails(Event event, TicketDisplayPageDto displayPageDto) {
        Ticketing ticketing = this.ticketingRepository.findByEventid(event);
        displayPageDto.setStartDate(ticketing.getEventStartDate());
        displayPageDto.setEndDate(ticketing.getEventEndDate());
        displayPageDto.setAddress(ticketing.getEventAddress());
        displayPageDto.setShowRemainingTickets(ticketing.isShowRemainingTickets());
        displayPageDto.setSeatingChartKey(ticketing.getChartKey());
        displayPageDto.setHolderAttributeRequired(ticketing.getCollectTicketHolderAttributes());
        displayPageDto.setShowMemberCountInCheckout(ticketing.isShowMemberCountInCheckout());
        displayPageDto.setUniqueTicketHolderEmail(ticketing.getUniqueTicketHolderEmail());
        displayPageDto.setUniqueTicketBuyerEmail(ticketing.isUniqueTicketBuyerEmail());
    }

    protected void addTicketingTypeDetails(TicketingType ticketingType, TicketTypeDto ticketType, long remainingTicket, StripeDTO stripe, SalesTaxFeeDto salesTaxFeeDto,Event event) {
        if (DataType.TICKET.equals(ticketingType.getDataType())) {
            ticketType.setRemaniningTickets(remainingTicket);
        }
        ticketType.setBundleType(ticketingType.getBundleType());
        ticketType.setPosition(ticketingType.getPosition());
        ticketType.setTicketTypeDescription(ticketingType.getTicketTypeDescription());
        ticketType.setEnableTicketDescription(ticketingType.getEnableTicketDescription());
        ticketType.setTicketType(ticketingType.getTicketType());
        ticketType.setPassFeesToBuyer(ticketingType.isPassfeetobuyer());
        if (!TicketBundleType.INDIVIDUAL_TICKET.equals(ticketingType.getBundleType())) {
            ticketType.setTicketsPerTable(ticketingType.getNumberOfTicketPerTable());
        }

        double capAmount=transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(ticketingType.getTicketTypeFormat(),event);
        double vatTaxRate = vatTaxService.getVatTaxByTicketTypeOrEvent(event.getEventId(),ticketingType);
        FeePerTicket feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, ticketingType.getPrice(), 1, 1, false,capAmount, vatTaxRate,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();
        ticketType.setFee(GeneralUtils.getRoundValue(feePerTicket.getTotalPayable() - ticketingType.getPrice()));
        ticketType.setVatTax(feePerTicket.getVatTax());
        ticketType.setSalesTax(feePerTicket.getSalesTaxFee());
        ticketType.setEndDate(ticketingType.getEndDate());
        ticketType.setStartDate(ticketingType.getStartDate());
        ticketType.setPrice(ticketingType.getPrice());
        ticketType.setDataType(ticketingType.getDataType());
        ticketType.setPayLater(ticketingType.isPayLater());
        ticketType.setTicketTypeFormat(ticketingType.getTicketTypeFormat());
        ticketType.setAllTicketTypesSelectedForAddOn(ticketingType.isAllTicketTypesSelectedForAddOn());
        ticketType.setListOfTicketTypesForAddOn(GeneralUtils.convertCommaSeparatedToListLong(ticketingType.getListOfTicketTypesForAddOn()));
        ticketType.setDepositType(ticketingType.getDepositType());
        ticketType.setDepositAmount(ticketingType.getDepositAmount());
        ticketType.setRequireDepositAmount(ticketingType.isRequireDepositAmount());
        log.info("paylater option is {} for event {}",(!ticketingType.isPayLater())?"Disable":"Enable", event.getEventId());
    }

    private RegistrationDisplayAttributesDto getAttendeeRegistrantAttributeDto(Event event, long recurringEventId, RegistrationRequest registrationRequest) {

        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME,STRING_LAST_SPACE_NAME,STRING_EMAIL_SPACE);

        List<TicketHolderRequiredAttributes> holderRequiredAttributes =  ticketHolderRequiredAttributesService
                .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, recurringEventId, DataType.TICKET);

        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());

        List<RegistrationRequestTicket> registrationRequestTickets = registrationRequestTicketRepository.findByRegistrationRequest(registrationRequest);

        Set<Long> ticketingTypeIdSet = registrationRequestTickets.stream().map(e->e.getTicketType().getId()).collect(Collectors.toSet());


        List<DisplayAttributeDto> purchaserQuestionDto = new ArrayList<>();
        List<DisplayAttributeDto> attributeDtos = new ArrayList<>();
        List<DisplayNestedQueDto> displayNestedQueDtoList= new ArrayList<>();

        for (TicketHolderRequiredAttributes holderRequiredAttribute : holderRequiredAttributes) {
            if (holderRequiredAttribute.getEnabledForTicketPurchaser() && !holderRequiredAttribute.getDeletedForBuyer()
                    && (defaultAttributes.contains(holderRequiredAttribute.getName().equals(EMAIL) ?
                    holderRequiredAttribute.getName().toUpperCase() : holderRequiredAttribute.getName())
                    || ticketingTypeIdSet.stream().anyMatch(GeneralUtils.convertCommaSeparatedToListLong(holderRequiredAttribute.getBuyerOptionalTicketTypeId())::contains)
                    || ticketingTypeIdSet.stream().anyMatch(GeneralUtils.convertCommaSeparatedToListLong(holderRequiredAttribute.getBuyerRequiredTicketTypeId())::contains)
                    || ticketingTypeIdSet.stream().anyMatch(GeneralUtils.convertCommaSeparatedToListLong(holderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId())::contains))) {
                DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                displayAttributeDto.setName(holderRequiredAttribute.getName());
                displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
                displayAttributeDto.setDefaultValue(holderRequiredAttribute.getDefaultValueJsonPurchaser());
                displayAttributeDto.setIsHidden(holderRequiredAttribute.isInvisibleForPurchaser());
                displayAttributeDto.setMandatory(isAttributeFirstnameLastnameOrEmail(holderRequiredAttribute.getName()) ||
                        isAttributeMandatoryForTicketingType(ticketingTypeIdSet, holderRequiredAttribute.getBuyerRequiredTicketTypeId()));
                displayAttributeDto.setValue(getAttendeeAttributeValue(registrationResponseDto,
                        displayAttributeDto.getName(),  holderRequiredAttribute.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()) || isNumberGreaterThanZero(holderRequiredAttribute.getParentQuestionId())));
                displayAttributeDto.setPosition(holderRequiredAttribute.getBuyerAttributeOrder());
                if ( Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
                    setAttendeePhoneNumber(registrationRequest, registrationResponseDto, holderRequiredAttribute, displayAttributeDto);
                }

                if (holderRequiredAttribute.isAttribute()) {
                    setAttendeeAttributesData(attributeDtos, displayNestedQueDtoList, holderRequiredAttribute, displayAttributeDto);

                } else {
                    purchaserQuestionDto.add(displayAttributeDto);
                }
            }
        }

        return setRegistrationRequestDetails(attributeDtos, purchaserQuestionDto, displayNestedQueDtoList, new ArrayList<>(), new ArrayList<>(), registrationRequestTickets, new ArrayList<>(), event, recurringEventId);
    }

    private Map<Long,RegistrationDisplayAttributesDto> getAttendeeRegistrantAttributeDetails(Event event, List<RegistrationRequest> registrationRequests) {

        Map<Long, RegistrationDisplayAttributesDto> registrationDisplayAttributesDtos = new HashMap<>();

        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME,STRING_LAST_SPACE_NAME,STRING_EMAIL_SPACE);

        List<TicketHolderRequiredAttributes> holderRequiredAttributes =  ticketHolderRequiredAttributesService
                .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, -1L, DataType.TICKET);

        List<RegistrationRequestTicket> registrationRequestTickets = registrationRequestTicketRepository.findByRegistrationRequestIn(registrationRequests);
        Map<Long, List<RegistrationRequestTicket>> registrationTicketByRegReqId = registrationRequestTickets.stream().collect(Collectors.groupingBy(e->e.getRegistrationRequest().getId()));

        for (RegistrationRequest registrationRequest: registrationRequests){
            RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());

            List<DisplayAttributeDto> purchaserQuestionDto = new ArrayList<>();
            List<DisplayAttributeDto> attributeDtos = new ArrayList<>();
            List<DisplayNestedQueDto> displayNestedQueDtoList= new ArrayList<>();

            for (TicketHolderRequiredAttributes holderRequiredAttribute : holderRequiredAttributes) {
                if (holderRequiredAttribute.getEnabledForTicketPurchaser() && !holderRequiredAttribute.getDeletedForBuyer()) {

                    DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                    displayAttributeDto.setName(holderRequiredAttribute.getName());
                    displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
                    displayAttributeDto.setValue(getAttendeeAttributeValue(registrationResponseDto,
                            displayAttributeDto.getName(),  holderRequiredAttribute.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()) || isNumberGreaterThanZero(holderRequiredAttribute.getParentQuestionId())));

                    if ( Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
                        setAttendeePhoneNumber(registrationRequest, registrationResponseDto, holderRequiredAttribute, displayAttributeDto);
                    }

                    if (holderRequiredAttribute.isAttribute()) {
                        setAttendeeAttributesData(attributeDtos, displayNestedQueDtoList, holderRequiredAttribute, displayAttributeDto);

                    } else {
                        purchaserQuestionDto.add(displayAttributeDto);
                    }
                }
            }

            RegistrationDisplayAttributesDto displayAttributesDto = new RegistrationDisplayAttributesDto(attributeDtos, purchaserQuestionDto, displayNestedQueDtoList);
            displayAttributesDto.setTicketTypes(getTicketTypeInfo(registrationTicketByRegReqId.get(registrationRequest.getId())));
            registrationDisplayAttributesDtos.put(registrationRequest.getId(), displayAttributesDto);
        }
        return registrationDisplayAttributesDtos;
    }

    private Map<Long, TicketDetailsDto> getTicketTypeInfo(List<RegistrationRequestTicket> registrationRequestTickets){
        Map<Long, TicketDetailsDto> ticketTypes = new HashMap<>();

        if(!CollectionUtils.isEmpty(registrationRequestTickets)){
            registrationRequestTickets.forEach(registrationRequestTicket -> {
                TicketDetailsDto ticketDetailsDto = new TicketDetailsDto();
                ticketDetailsDto.setTicketCount(registrationRequestTicket.getNumberOfTicket());
                ticketDetailsDto.setTicketName(registrationRequestTicket.getTicketType().getTicketTypeName());
                ticketDetailsDto.setTicketType(registrationRequestTicket.getTicketType().getTicketType());
                ticketTypes.put(registrationRequestTicket.getTicketType().getId(), ticketDetailsDto);
            });
        }

        return ticketTypes;
    }

    public RegistrationDisplayAttributesDto getRegistrantAttributeDto(Event event, long recurringEventId, RegistrationRequest registrationRequest) {

        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME, STRING_EMAIL_SPACE);
        List<RegistrationAttribute> requiredAttributes = registrationAttributeService.getRegistrationRequiredAttributesOrderByAttributeOrder(event, recurringEventId, registrationRequest.getType());

        if(isAddDefaultAttributes(requiredAttributes)) {
            requiredAttributes = registrationAttributeService.addDefaultAttributes(event, recurringEventId,registrationRequest.getType());
        }
        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());

        List<DisplayAttributeDto> attributeDtos = new ArrayList<>();
        List<DisplayAttributeDto> questionDto = new ArrayList<>();
        List<DisplayNestedQueDto> displayNestedQueDtoList= new ArrayList<>();
        List<DisplayAttributeDto> sessionAttributeDtos = new ArrayList<>();
        List<DisplayAttributeDto> boothAttributeDtos = new ArrayList<>();

        for (RegistrationAttribute registrationAttribute : requiredAttributes) {
            if (defaultAttributes.contains(registrationAttribute.getName().equals(EMAIL_ADDRESS) ?
                    registrationAttribute.getName().toUpperCase() : registrationAttribute.getName()) || registrationAttribute.isEnabled()) {
                DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                displayAttributeDto.setName(registrationAttribute.getName());
                displayAttributeDto.setType(registrationAttribute.getValueType().getType());
                displayAttributeDto.setDefaultValue(registrationAttribute.getDefaultValueJson());
                displayAttributeDto.setMandatory(registrationAttribute.isRequired());
                displayAttributeDto.setValue(
                        getAttributeValue(registrationResponseDto, displayAttributeDto.getName(), registrationAttribute.isAttribute(),
                                (AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType()) || isNumberGreaterThanZero(registrationAttribute.getParentQuestionId())),
                                registrationAttribute.getType(), registrationAttribute.getId())
                );
                displayAttributeDto.setPosition(registrationAttribute.getPosition());
                if (Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
                    setPhoneNumber(registrationRequest, registrationResponseDto, registrationAttribute, displayAttributeDto);
                }

                if (registrationAttribute.isAttribute()) {
                    if (RegistrationAttributeType.BOOTH_DETAILS.equals(registrationAttribute.getType())) {
                        setHolderAttributesData(boothAttributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                    } else if (RegistrationAttributeType.SPEAKER_DETAILS.equals(registrationAttribute.getType())) {
                        setHolderAttributesData(sessionAttributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                    } else {
                        setHolderAttributesData(attributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                    }
                } else {
                    questionDto.add(displayAttributeDto);
                }
            }
        }

        List<RegistrationRequestTicket> registrationRequestTickets = registrationRequestTicketRepository.findByRegistrationRequest(registrationRequest);
        List<SpeakerRequestDisplayAttributesDto> speakerList = new ArrayList<>();
        if(RegistrationRequestType.SPEAKER.equals(registrationRequest.getType()) && !CollectionUtils.isEmpty(registrationResponseDto.getSpeakerDetail())){
            speakerList =  getSpeakerRegistrantAttributeDto(registrationRequest, requiredAttributes, registrationResponseDto.getSpeakerDetail());
        }

        return setRegistrationRequestDetails(attributeDtos, questionDto, displayNestedQueDtoList, sessionAttributeDtos, boothAttributeDtos, registrationRequestTickets, speakerList, event, recurringEventId);
    }

    public Map<Long, RegistrationDisplayAttributesDto> getSessionExpoRegistrantDetails(Event event, List<RegistrationRequest> registrationRequests, RegistrationRequestType registrationRequestType) {

        Map<Long, RegistrationDisplayAttributesDto> registrationDisplayAttributesDtoMap = new HashMap<>();

        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME, STRING_EMAIL_SPACE);
        List<RegistrationAttribute> requiredAttributes = registrationAttributeService.getRegistrationRequiredAttributesOrderByAttributeOrder(event, -1L, registrationRequestType);

        if(isAddDefaultAttributes(requiredAttributes)) {
            requiredAttributes = registrationAttributeService.addDefaultAttributes(event, -1L, registrationRequestType);
        }

        for(RegistrationRequest registrationRequest: registrationRequests){
            RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());

            List<DisplayAttributeDto> attributeDtos = new ArrayList<>();
            List<DisplayAttributeDto> questionDto = new ArrayList<>();
            List<DisplayNestedQueDto> displayNestedQueDtoList= new ArrayList<>();
            List<DisplayAttributeDto> sessionAttributeDtos = new ArrayList<>();
            List<DisplayAttributeDto> boothAttributeDtos = new ArrayList<>();

            for (RegistrationAttribute registrationAttribute : requiredAttributes) {
                if (defaultAttributes.contains(registrationAttribute.getName().equals(EMAIL_ADDRESS) ?
                        registrationAttribute.getName().toUpperCase() : registrationAttribute.getName()) || registrationAttribute.isEnabled()) {
                    DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                    displayAttributeDto.setName(registrationAttribute.getName());
                    displayAttributeDto.setType(registrationAttribute.getValueType().getType());
                    displayAttributeDto.setValue(
                            getAttributeValue(registrationResponseDto, displayAttributeDto.getName(), registrationAttribute.isAttribute(),
                                    (AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType()) || isNumberGreaterThanZero(registrationAttribute.getParentQuestionId())), registrationAttribute.getType())
                    );

                    if (Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
                        setPhoneNumber(registrationRequest, registrationResponseDto, registrationAttribute, displayAttributeDto);
                    }

                    if (registrationAttribute.isAttribute()) {
                        if (RegistrationAttributeType.BOOTH_DETAILS.equals(registrationAttribute.getType())) {
                            setHolderAttributesData(boothAttributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                        } else if (RegistrationAttributeType.SPEAKER_DETAILS.equals(registrationAttribute.getType())) {
                            setHolderAttributesData(sessionAttributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                        } else {
                            setHolderAttributesData(attributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                        }
                    } else {
                        questionDto.add(displayAttributeDto);
                    }
                }
            }

            List<SpeakerRequestDisplayAttributesDto> speakerList;
            if(RegistrationRequestType.SPEAKER.equals(registrationRequestType) && !CollectionUtils.isEmpty(registrationResponseDto.getSpeakerDetail())){
                speakerList =  getSpeakerRegistrantAttributeDto(registrationRequest, requiredAttributes, registrationResponseDto.getSpeakerDetail());
                registrationDisplayAttributesDtoMap.put(registrationRequest.getId(), new RegistrationDisplayAttributesDto(sessionAttributeDtos, speakerList));

            }
            else if (RegistrationRequestType.EXPO.equals(registrationRequestType)){
                registrationDisplayAttributesDtoMap.put(registrationRequest.getId(), new RegistrationDisplayAttributesDto(boothAttributeDtos, attributeDtos, questionDto, displayNestedQueDtoList));
            }
            else if(RegistrationRequestType.REVIEWER.equals(registrationRequestType)){
                registrationDisplayAttributesDtoMap.put(registrationRequest.getId(), new RegistrationDisplayAttributesDto(attributeDtos, questionDto, displayNestedQueDtoList));
            }
        }
        return registrationDisplayAttributesDtoMap;
    }

    private RegistrationDisplayAttributesDto setRegistrationRequestDetails(List<DisplayAttributeDto> attributeDtos, List<DisplayAttributeDto> questionDto, List<DisplayNestedQueDto> displayNestedQueDtoList, List<DisplayAttributeDto> sessionAttributeDtos, List<DisplayAttributeDto> boothAttributeDtos, List<RegistrationRequestTicket> registrationRequestTickets, List<SpeakerRequestDisplayAttributesDto> speakerList, Event event, Long recurringEventId){
        RegistrationDisplayAttributesDto displayAttributesDto = new RegistrationDisplayAttributesDto();
        displayAttributesDto.setAttributes(attributeDtos);
        displayAttributesDto.setQuestions(questionDto);
        displayAttributesDto.setNestedQuestions(displayNestedQueDtoList);
        displayAttributesDto.setSessionDetail(sessionAttributeDtos);
        displayAttributesDto.setBoothDetail(boothAttributeDtos);
        displayAttributesDto.setSpeakerDetails(speakerList);
        Map<Long, TicketDetailsDto> ticketTypes = new HashMap<>();

        registrationRequestTickets.forEach(registrationRequestTicket -> {
            TicketDetailsDto ticketDetailsDto = new TicketDetailsDto();
            ticketDetailsDto.setTicketCount(registrationRequestTicket.getNumberOfTicket());
            ticketDetailsDto.setTicketName(registrationRequestTicket.getTicketType().getTicketTypeName());
            ticketDetailsDto.setTicketType(registrationRequestTicket.getTicketType().getTicketType());
            ticketTypes.put(registrationRequestTicket.getTicketType().getId(), ticketDetailsDto);
        });
        displayAttributesDto.setTicketTypes(ticketTypes);
        String eventKey = String.valueOf(event.getEventId());
        if(NumberUtils.isNumberGreaterThanZero(recurringEventId)){
            eventKey = TicketingUtils.getEventKey(event.getEventId(), true, recurringEventId);
        }
        displayAttributesDto.setEventKey(eventKey);

        return displayAttributesDto;
    }

    public List<SpeakerRequestDisplayAttributesDto> getSpeakerRegistrantAttributeDto(RegistrationRequest registrationRequest, List<RegistrationAttribute> requiredAttributes, List<RegistrationSpeakerResponseDto> speakerDetail) {

        List<SpeakerRequestDisplayAttributesDto> speakerList = new ArrayList<>();

        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME, STRING_EMAIL_SPACE);

        for (RegistrationSpeakerResponseDto speakerApprovalResponseDTO : speakerDetail){

            List<DisplayAttributeDto> attributeDtos = new ArrayList<>();
            List<DisplayAttributeDto> questionDto = new ArrayList<>();
            List<DisplayNestedQueDto> displayNestedQueDtoList= new ArrayList<>();
            for (RegistrationAttribute registrationAttribute : requiredAttributes) {
                if (RegistrationAttributeType.SPEAKER_INFO.equals(registrationAttribute.getType()) && (defaultAttributes.contains(registrationAttribute.getName().equals(EMAIL_ADDRESS) ?
                        registrationAttribute.getName().toUpperCase() : registrationAttribute.getName()) || registrationAttribute.isEnabled()))  {
                    DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                    displayAttributeDto.setName(registrationAttribute.getName());
                    displayAttributeDto.setType(registrationAttribute.getValueType().getType());
                    displayAttributeDto.setDefaultValue(registrationAttribute.getDefaultValueJson());
                    displayAttributeDto.setMandatory(registrationAttribute.isRequired());
                    displayAttributeDto.setValue(
                            getSpeakerAttributeValue(speakerApprovalResponseDTO, displayAttributeDto.getName(), registrationAttribute.isAttribute(),
                                    AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType()), registrationAttribute.getType())
                    );
                    displayAttributeDto.setPosition(registrationAttribute.getPosition());
                    if (Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
                        setSpeakerPhoneNumber(registrationRequest, speakerApprovalResponseDTO, registrationAttribute, displayAttributeDto);
                    }

                    if (registrationAttribute.isAttribute()) {
                        setHolderAttributesData(attributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                    } else {
                        questionDto.add(displayAttributeDto);
                    }
                }
            }
            SpeakerRequestDisplayAttributesDto speakerRequestDisplayAttributesDto = new SpeakerRequestDisplayAttributesDto();
            speakerRequestDisplayAttributesDto.setAttributes(attributeDtos);
            speakerRequestDisplayAttributesDto.setQuestions(questionDto);
            speakerRequestDisplayAttributesDto.setNestedQuestions(displayNestedQueDtoList);
            speakerRequestDisplayAttributesDto.setPrimarySpeaker(speakerApprovalResponseDTO.isPrimary());
            speakerList.add(speakerRequestDisplayAttributesDto);
        }

        return speakerList;
    }

    public String getAttributeValue(RegistrationResponseDto registrationResponseDto, String attributeName,
                                     boolean isAttribute, boolean isConditionalQuestion, RegistrationAttributeType attributeType) {
        if (registrationResponseDto != null) {
            if (isAttribute) {
                if (isConditionalQuestion) {
                    List<NestedQuestionsDto> nestedQuestions = registrationResponseDto.getNestedQuestions();
                    if (!CollectionUtils.isEmpty(nestedQuestions)) {
                        for (NestedQuestionsDto questionsDto : nestedQuestions) {
                            boolean isEnterValue = questionsDto.getName().equals(attributeName);
                            if (isEnterValue) {
                                return attributeValueCheck(questionsDto.getValue());
                            }
                        }
                    }
                } else {
                    Map<String, String> attributes;
                    if(RegistrationAttributeType.BOOTH_DETAILS.equals(attributeType)) {
                        attributes = registrationResponseDto.getBoothDetail();
                    }else if(RegistrationAttributeType.SPEAKER_DETAILS.equals(attributeType)){
                        attributes = registrationResponseDto.getSessionDetail();
                    }else{
                        attributes = registrationResponseDto.getAttributes();
                    }

                    if (!CollectionUtils.isEmpty(attributes)) {
                        for (Map.Entry<String, String> attribute : attributes.entrySet()) {
                            if (attributeName.equalsIgnoreCase(attribute.getKey())) {
                                return attributeValueCheck(attribute.getValue());
                            }
                        }
                    }
                }
            } else {
                Map<String, String> questions = registrationResponseDto.getQuestions();
                if (!CollectionUtils.isEmpty(questions)) {
                    for (Map.Entry<String, String> question : questions.entrySet()) {
                        if (attributeName.equalsIgnoreCase(question.getKey())) {
                            return attributeValueCheck(question.getValue());
                        }
                    }
                }
            }
        }
        return null;
    }

    public String getAttributeValue(RegistrationResponseDto registrationResponseDto, String attributeName,
                                    boolean isAttribute, boolean isConditionalQuestion,
                                    RegistrationAttributeType attributeType, Long attributeId) {
        if (registrationResponseDto != null) {
            if (isAttribute) {
                if (isConditionalQuestion) {
                    List<NestedQuestionsDto> nestedQuestions = registrationResponseDto.getNestedQuestions();
                    if (!CollectionUtils.isEmpty(nestedQuestions)) {
                        for (NestedQuestionsDto questionsDto : nestedQuestions) {
                            boolean isEnterValue = questionsDto.getId().equals(attributeId);
                            if (isEnterValue) {
                                return attributeValueCheck(questionsDto.getValue());
                            }
                        }
                    }
                } else {
                    Map<String, String> attributes;
                    if(RegistrationAttributeType.BOOTH_DETAILS.equals(attributeType)) {
                        attributes = registrationResponseDto.getBoothDetail();
                    }else if(RegistrationAttributeType.SPEAKER_DETAILS.equals(attributeType)){
                        attributes = registrationResponseDto.getSessionDetail();
                    }else{
                        attributes = registrationResponseDto.getAttributes();
                    }

                    if (!CollectionUtils.isEmpty(attributes)) {
                        for (Map.Entry<String, String> attribute : attributes.entrySet()) {
                            if (attributeName.equalsIgnoreCase(attribute.getKey())) {
                                return attributeValueCheck(attribute.getValue());
                            }
                        }
                    }
                }
            } else {
                Map<String, String> questions = registrationResponseDto.getQuestions();
                if (!CollectionUtils.isEmpty(questions)) {
                    for (Map.Entry<String, String> question : questions.entrySet()) {
                        if (attributeName.equalsIgnoreCase(question.getKey())) {
                            return attributeValueCheck(question.getValue());
                        }
                    }
                }
            }
        }
        return null;
    }

    private static String attributeValueCheck(String attributeValue) {
        if ("null".equalsIgnoreCase(attributeValue) || StringUtils.isBlank(attributeValue)) {
            return null;
        } else {
            return attributeValue;
        }
    }

    @Override
    public RegistrationResponseDto getRegistrationRequestDto(TicketHolderAttributes ticketHolderAttributes) {
        Gson gson = new Gson();
        try {
            RegistrationResponseDto dto = gson.fromJson(ticketHolderAttributes.getJsonValue(), RegistrationResponseDto.class);
            if (dto == null) {
                dto = new RegistrationResponseDto();
            }
            return dto;
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return new RegistrationResponseDto();
        }
    }


    private void setPhoneNumber(RegistrationRequest registrationRequest, RegistrationResponseDto registrationRequestDto,
                                RegistrationAttribute registrationAttribute, DisplayAttributeDto displayAttributeDto) {
        CountryCode code = registrationRequest.getRequestedByUser().getCountryCode();
        String cellNumber = String.valueOf(registrationRequest.getRequestedForUser().getPhoneNumber());
        String countryCode = null;
        if (null == displayAttributeDto.getValue() && null == code) {
            displayAttributeDto.setValue(
                    getAttributeValue(registrationRequestDto, Constants.STRING_PHONE_NUMBER, registrationAttribute.isAttribute(),
                            AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType()), registrationAttribute.getType())
            );
            if (null != displayAttributeDto.getValue()) {
                String numberWithCountryCode = displayAttributeDto.getValue();
                PhoneNumber phoneDetail = twilioPhoneNumberValidateService.getPhoneDetail(null, numberWithCountryCode);
                if (null != phoneDetail) {
                    countryCode = phoneDetail.getCountryCode();
                    cellNumber = ticketingHelperService.prepareNumber(phoneDetail.getNationalFormat());
                }
            }
        }
        if (null == countryCode) {
            countryCode = null != code ? code.name() : CountryCode.US.name();
        }
        if (null != displayAttributeDto.getValue() && displayAttributeDto.getValue().contains("|")) {
            cellNumber = displayAttributeDto.getValue().substring(displayAttributeDto.getValue().indexOf("|") + 1);
            countryCode = displayAttributeDto.getValue().substring(0, displayAttributeDto.getValue().indexOf("|"));
        }
        displayAttributeDto.setValue(countryCode + ADD_SEPARATOR + cellNumber);
    }

    private void setSpeakerPhoneNumber(RegistrationRequest registrationRequest, RegistrationSpeakerResponseDto registrationRequestDto,
                                RegistrationAttribute registrationAttribute, DisplayAttributeDto displayAttributeDto) {
        CountryCode code = registrationRequest.getRequestedByUser().getCountryCode();
        String cellNumber = String.valueOf(registrationRequest.getRequestedForUser().getPhoneNumber());
        String countryCode = null;
        if (null == displayAttributeDto.getValue() && null == code) {
            displayAttributeDto.setValue(
                    getSpeakerAttributeValue(registrationRequestDto, Constants.STRING_PHONE_NUMBER, registrationAttribute.isAttribute(),
                            AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType()), registrationAttribute.getType())
            );
            if (null != displayAttributeDto.getValue()) {
                String numberWithCountryCode = displayAttributeDto.getValue();
                PhoneNumber phoneDetail = twilioPhoneNumberValidateService.getPhoneDetail(null, numberWithCountryCode);
                if (null != phoneDetail) {
                    countryCode = phoneDetail.getCountryCode();
                    cellNumber = ticketingHelperService.prepareNumber(phoneDetail.getNationalFormat());
                }
            }
        }
        if (null == countryCode) {
            countryCode = null != code ? code.name() : CountryCode.US.name();
        }
        if (null != displayAttributeDto.getValue() && displayAttributeDto.getValue().contains("|")) {
            cellNumber = displayAttributeDto.getValue().substring(displayAttributeDto.getValue().indexOf("|") + 1);
            countryCode = displayAttributeDto.getValue().substring(0, displayAttributeDto.getValue().indexOf("|"));
        }
        displayAttributeDto.setValue(countryCode + ADD_SEPARATOR + cellNumber);
    }

    private void setAttendeePhoneNumber(RegistrationRequest registrationRequest, RegistrationResponseDto registrationRequestDto,
                                TicketHolderRequiredAttributes ticketHolderRequiredAttributes, DisplayAttributeDto displayAttributeDto) {
        CountryCode code = registrationRequest.getRequestedByUser().getCountryCode();
        String cellNumber = String.valueOf(registrationRequest.getRequestedForUser().getPhoneNumber());
        String countryCode = null;
        if (null == displayAttributeDto.getValue() && null == code) {
            displayAttributeDto.setValue(
                    getAttendeeAttributeValue(registrationRequestDto,
                            displayAttributeDto.getName(),  ticketHolderRequiredAttributes.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType()) || isNumberGreaterThanZero(ticketHolderRequiredAttributes.getParentQuestionId())));

            if (null != displayAttributeDto.getValue()) {
                String numberWithCountryCode = displayAttributeDto.getValue();
                PhoneNumber phoneDetail = twilioPhoneNumberValidateService.getPhoneDetail(null, numberWithCountryCode);
                if (null != phoneDetail) {
                    countryCode = phoneDetail.getCountryCode();
                    cellNumber = ticketingHelperService.prepareNumber(phoneDetail.getNationalFormat());
                }
            }
        }
        if (null == countryCode) {
            countryCode = null != code ? code.name() : CountryCode.US.name();
        }
        if (null != displayAttributeDto.getValue() && displayAttributeDto.getValue().contains("|")) {
            cellNumber = displayAttributeDto.getValue().substring(displayAttributeDto.getValue().indexOf("|") + 1);
            countryCode = displayAttributeDto.getValue().substring(0, displayAttributeDto.getValue().indexOf("|"));
        }
        displayAttributeDto.setValue(countryCode + ADD_SEPARATOR + cellNumber);
    }

    private void setHolderAttributesData(List<DisplayAttributeDto> attributeDtos, List<DisplayNestedQueDto> displayNestedQueDtoList,
                                         RegistrationAttribute registrationAttribute, DisplayAttributeDto displayAttributeDto) {
        if (displayAttributeDto.getType() != null && (displayAttributeDto.getType().equalsIgnoreCase(AttributeValueType.IMAGE.getType()) || displayAttributeDto.getType().equalsIgnoreCase(AttributeValueType.UPLOAD.getType()))) {
            String url = displayAttributeDto.getValue();
            if (StringUtils.isNotEmpty(url)) {
                if (RegistrationAttributeType.ATTENDEE.equals(registrationAttribute.getType())) {
                    url = uploadsUrl.concat(url);
                } else if(displayAttributeDto.getType().equalsIgnoreCase(AttributeValueType.UPLOAD.getType()) && SPEAKER_DETAILS.equals(registrationAttribute.getType())){
                    try {
                        JSONArray jsonArray = new JSONArray(url);;
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        String docUrl = jsonObject.getString("docUrl");
                        url = imagePrefix.concat(docUrl);
                    } catch (JSONException e) {
                        log.info("JSON Format Exception".concat(e.getMessage()));
                    }
                }else {
                    url = imagePrefix.concat(url);
                }
            }
            displayAttributeDto.setValue(url);
        }
        if (AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType())  || isNumberGreaterThanZero(registrationAttribute.getParentQuestionId())) {
            DisplayNestedQueDto displayNestedQueDto = new DisplayNestedQueDto(displayAttributeDto);
            displayNestedQueDto.setId(registrationAttribute.getId());
            displayNestedQueDto.setParentQueId(registrationAttribute.getParentQuestionId());
            displayNestedQueDto.setSelectedAnsId(registrationAttribute.getSelectedAnswer());
            displayNestedQueDto.setQuestionType(registrationAttribute.getType());
            displayNestedQueDtoList.add(displayNestedQueDto);
        } else {
            attributeDtos.add(displayAttributeDto);
        }
    }

    private void setAttendeeAttributesData(List<DisplayAttributeDto> purchaserAttributesDto, List<DisplayNestedQueDto> displayNestedQueDtoList, TicketHolderRequiredAttributes holderRequiredAttribute, DisplayAttributeDto displayAttributeDto) {
        if (displayAttributeDto.getType() != null && (displayAttributeDto.getType().equalsIgnoreCase(AttributeValueType.IMAGE.getType()) || displayAttributeDto.getType().equalsIgnoreCase(AttributeValueType.UPLOAD.getType()))) {
            String url = displayAttributeDto.getValue();
            if (StringUtils.isNotEmpty(url)) {
                url = uploadsUrl.concat(url);
            }
            displayAttributeDto.setValue(url);
        }
        if(AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()) || isNumberGreaterThanZero(holderRequiredAttribute.getParentQuestionId())){
            DisplayNestedQueDto displayNestedQueDto=new DisplayNestedQueDto(displayAttributeDto);
            displayNestedQueDto.setId(holderRequiredAttribute.getId());
            displayNestedQueDto.setParentQueId(holderRequiredAttribute.getParentQuestionId());
            displayNestedQueDto.setSelectedAnsId(holderRequiredAttribute.getSelectedAnswer());
            displayNestedQueDto.setEventTicketTypeId(holderRequiredAttribute.getBuyerEventTicketTypeId());
            displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getBuyerOptionalTicketTypeId());
            displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId());
            displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getBuyerRequiredTicketTypeId());
            displayNestedQueDtoList.add(displayNestedQueDto);
        } else {
            purchaserAttributesDto.add(displayAttributeDto);
        }
    }

    protected Map<String, String> listToMap(List<AttributeKeyValueDto> attributeKeyValueDto) {
        Map<String, String> attributeMap = new HashMap<>();
        if (attributeKeyValueDto != null && !attributeKeyValueDto.isEmpty()) {
            attributeKeyValueDto.forEach(attribute -> attributeMap.put(attribute.getKey(), attribute.getValue()));
        }
        return attributeMap;
    }

    protected List<Map<String, Object>> mapSpeaker(List<SessionRegistrationSpeakerDTO> sessionRegistrationSpeakerDTOList) {
        List<Map<String, Object>> speakerInformation = new ArrayList<>();
        boolean isPrimarySpeaker=false;
        for(SessionRegistrationSpeakerDTO speakerDTO: sessionRegistrationSpeakerDTOList){
            Map<String, Object> map = new HashMap<>();
                map.put(TICKETING.ATTRIBUTES, this.listToMap(speakerDTO.getAttributes()));
            map.put(TICKETING.QUESTIONS, this.listToMap(speakerDTO.getQuestions()));
            map.put(TICKETING.NESTEDQUESTIONS, (!CollectionUtils.isEmpty(speakerDTO.getNestedQuestions()) ? speakerDTO.getNestedQuestions() : null));
            if(!isPrimarySpeaker && speakerDTO.isPrimarySpeaker()){
                isPrimarySpeaker=true;
                map.put(IS_PRIMARY_SPEAKER, true);
            }
            else{
                map.put(IS_PRIMARY_SPEAKER, false);
            }
            speakerInformation.add(map);
        }
        if(!isPrimarySpeaker && !CollectionUtils.isEmpty(speakerInformation)){
            speakerInformation.get(0).put(IS_PRIMARY_SPEAKER, true);
        }
        return speakerInformation;
    }

    @Override
    public AttendeeAttributeWithTypeAndTableDto getFormAttributes(Event event, Long recurringEventId, RegistrationAttributeType attributeType) {

        List<DisplayAttributeDto> attributesDto = new ArrayList<>();
        List<DisplayNestedQueDto> nestedQueDto = new ArrayList<>();
        List<DisplayAttributeDto> question = new ArrayList<>();

        AttendeeAttributeWithTypeAndTableDto attendeeAttributeWithTypeAndTableDto = new AttendeeAttributeWithTypeAndTableDto();

        // get all attributes with hidden
        Map<Long, DisplayAttributeDto> mapHolderAttributesDto = new HashMap<>();
        Map<Long, DisplayNestedQueDto> mapHolderNestedQueDto = new HashMap<>();
        List<RegistrationAttribute> requiredAttributes = getAttendeeRegistrationRequiredAttributes(event, recurringEventId, attributeType);

        if(isAddDefaultAttributes(requiredAttributes)) {
            requiredAttributes = getRegistrationRequestServiceHandler(attributeType).addDefaultAttributes(event, recurringEventId, attributeType);
        }
        requiredAttributes.sort(Comparator.comparingInt(RegistrationAttribute::getPosition));

        List<String> firstLastNameEmailList = Arrays.asList(FIRST_NAME, LAST_NAME, EMAIL_ADDRESS);

        for (RegistrationAttribute attribute : requiredAttributes) {

            if (attribute.isEnabled() || firstLastNameEmailList.contains(attribute.getName()) && !attribute.isHidden()) {
                if (attribute.getValueType().equals(AttributeValueType.CONDITIONAL_QUE) || isNumberGreaterThanZero(attribute.getParentQuestionId())) {
                    DisplayNestedQueDto displayNestedQueDto = getRegDisplayNestedQue(attribute);
                    if (attribute.isAttribute()) {
                        mapHolderNestedQueDto.put(attribute.getId(), displayNestedQueDto);
                    } else {
                        question.add(displayNestedQueDto);
                    }
                } else {

                    DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                    displayAttributeDto.setId(attribute.getId());
                    displayAttributeDto.setName(attribute.getName());
                    displayAttributeDto.setType(attribute.getValueType().getType());
                    displayAttributeDto.setDefaultValue(attribute.getDefaultValueJson());
                    displayAttributeDto.setEventTicketTypeId(attribute.getTicketTypeIds());
                    displayAttributeDto.setMandatory(attribute.isRequired());
                    displayAttributeDto.setPosition(attribute.getPosition());

                    if (attribute.isAttribute()) {
                        mapHolderAttributesDto.put(attribute.getId(), displayAttributeDto);
                    } else {
                        question.add(displayAttributeDto);
                    }
                }
            }
        }

        //Sort by Holder Attribute Order for attendees attribute
        if (!CollectionUtils.isEmpty(mapHolderAttributesDto) && !CollectionUtils.isEmpty(requiredAttributes)) {
            List<RegistrationAttribute> requiredAttributesOrderByHolderAttributeOrder = new ArrayList<>(requiredAttributes);
            requiredAttributesOrderByHolderAttributeOrder.sort(Comparator.comparingInt(RegistrationAttribute::getPosition));
            requiredAttributesOrderByHolderAttributeOrder.forEach(attribute -> {
                if (mapHolderAttributesDto.containsKey(attribute.getId())) {
                    attributesDto.add(mapHolderAttributesDto.get(attribute.getId()));
                }
                if (mapHolderNestedQueDto.containsKey(attribute.getId())) {
                    nestedQueDto.add(mapHolderNestedQueDto.get(attribute.getId()));
                }
            });
        }

        if(RegistrationAttributeType.SPEAKER_INFO.equals(attributeType) || RegistrationAttributeType.SPEAKER_DETAILS.equals(attributeType)){
            SessionApprovalSettingDto sessionApprovalSettingDto = getSessionFormSettings(event);
            attendeeAttributeWithTypeAndTableDto.setAllowToAddAdditionalSpeaker(sessionApprovalSettingDto.isAllowToAddAdditionalSpeaker());
            attendeeAttributeWithTypeAndTableDto.setMaxNumberOfSpeakerPerApplication(sessionApprovalSettingDto.getMaxNumberOfSpeakerPerApplication());
        }

        attendeeAttributeWithTypeAndTableDto.setAttributes(attributesDto);
        attendeeAttributeWithTypeAndTableDto.setNestedQuestion(nestedQueDto);
        attendeeAttributeWithTypeAndTableDto.setQuestions(question);
        return attendeeAttributeWithTypeAndTableDto;
    }

    @Override
    public boolean isRegistrationRequestExists(Event event, Long recurringEventId, RegistrationRequestType type) {
        Long numberOfRequest =  (isNumberGreaterThanZero(recurringEventId)) ?
                registrationRequestRepository.countByEventAndTypeAndRecurringEventId(event, recurringEventId, type) :
                registrationRequestRepository.countByEventAndTypeAndRecurringEventIdNull(event, type);
        return numberOfRequest != null && numberOfRequest > 0;
    }

    @Override
    public void checkRequestCheckoutExpiration() {
        List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByStatusAndCheckoutLinkNotExpired(RegistrationRequestStatus.APPROVED);
        registrationRequests.forEach(request -> {
            if (request.getRecentApprovedAt() != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(request.getRecentApprovedAt());
                calendar.add(Calendar.MINUTE, TIME_TO_RESPOND);
                if (calendar.getTime().before(new Date())) {
                    request.setCheckoutLinkExpired(true);
                    registrationRequestRepository.save(request);
                }
            }
        });
    }

    protected DisplayNestedQueDto getRegDisplayNestedQue(RegistrationAttribute requiredAttribute) {
        DisplayNestedQueDto displayNestedQueDto = new DisplayNestedQueDto();
        if (requiredAttribute.getValueType().equals(AttributeValueType.CONDITIONAL_QUE) || isNumberGreaterThanZero(requiredAttribute.getParentQuestionId())) {
            displayNestedQueDto.setName(requiredAttribute.getName());
            displayNestedQueDto.setType(requiredAttribute.getValueType().getType());
            displayNestedQueDto.setDefaultValue(requiredAttribute.getDefaultValueJson());
            displayNestedQueDto.setEventTicketTypeId(requiredAttribute.getTicketTypeIds());
            displayNestedQueDto.setMandatory(requiredAttribute.isRequired());
            displayNestedQueDto.setPosition(requiredAttribute.getPosition());
            displayNestedQueDto.setParentQueId(requiredAttribute.getParentQuestionId());
            displayNestedQueDto.setSelectedAnsId(requiredAttribute.getSelectedAnswer());
            displayNestedQueDto.setId(requiredAttribute.getId());
        }
        return displayNestedQueDto;
    }

    private List<RegistrationAttribute> getAttendeeRegistrationRequiredAttributes(Event event, Long recurringEventId, RegistrationAttributeType attributeType) {
        return (isNumberGreaterThanZero(recurringEventId)) ?
                registrationAttributeRepository.findByEventAndTypeAndRecurringEventId(event, recurringEventId, attributeType) :
                registrationAttributeRepository.findByEventAndTypeAndRecurringEventIdNull(event, attributeType);
    }

    @Override
    public AnalyticsArea getAreaFromType(RegistrationRequestType requestType) {
        switch (requestType) {
            case SPEAKER:
                return AnalyticsArea.REGISTRATION_APPROVAL_SPEAKER;
            case EXPO:
                return AnalyticsArea.REGISTRATION_APPROVAL_EXPO;
            case REVIEWER:
                return AnalyticsArea.REGISTRATION_APPROVAL_REVIEWER;
            default:
                return AnalyticsArea.REGISTRATION_APPROVAL_ATTENDEE;
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void generateAndSendCheckoutListForRegistrationRequest(Long regRequestId, Event event) {
        RegistrationRequest registrationRequest = registrationRequestRepository.findAllByEventAndIdsAndStatusWithTrackingLink(regRequestId, event.getEventId(), RegistrationRequestStatus.APPROVED);
        List<RegistrationRequestTicket> registrationRequestTickets = registrationRequestTicketRepository.findAllByRegistrationRequestIds(Collections.singletonList(regRequestId));

        Map<String, WaitListMailData> mailMap = new HashMap<>();
         for(RegistrationRequestTicket registrationRequestTicket : registrationRequestTickets){

            int totalTicketsAvailable = getAvailableTickets(registrationRequestTicket.getTicketType().getId(), event);
            Long numberOfTicketsInApprovedStatus = registrationRequestTicketRepository.approvedTicketCount(
                   event, RegistrationRequestStatus.APPROVED,  registrationRequestTicket.getTicketType());

            int availableTickets = totalTicketsAvailable -(numberOfTicketsInApprovedStatus == null ? 0 : numberOfTicketsInApprovedStatus.intValue());

            if(availableTickets <= 0 || registrationRequestTicket.getNumberOfTicket() > availableTickets){
                throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.APPROVE_REQUEST_LIMIT_EXCEED);
            }

            if (availableTickets > 0) {
                availableTickets = availableTickets - 1;

                WaitListMailData waitListMailData = mailMap.get(registrationRequest.getRequestedForUser().getEmail());
                String checkoutLink = String.valueOf(registrationRequest.getId());
                if (!registrationRequestTickets.isEmpty() && waitListMailData==null) {
                    waitListMailData = new WaitListMailData(checkoutLink, registrationRequestTickets.size());
                }
                mailMap.put(registrationRequest.getRequestedForUser().getEmail(), waitListMailData);
            }

        }

        mailMap.forEach((email, waitListMailData) -> {
            String checkoutLink = waitListMailData.getCheckoutLink();
            sendGridMailPrepareService.sendRegistrationCheckoutEmail(email, event, waitListMailData.getNumberOfTickets(), SecurityUtils.encode(checkoutLink), registrationRequest.getId());
            registrationRequestRepository.markRegistrationRequestEmailSentTrue(registrationRequest.getId());
        });
        registrationRequestRepository.markRegistrationRequestCheckoutLinkExpiredFalse(regRequestId);
    }

    private int getAvailableTickets(long ticketingTypeId, Event event) {
        if(ticketingTypeId > 0) {
            return ticketingStatisticsService.getAvailableTicketsForTicketType(event, ticketingTypeId);
        } else {
            return ticketingStatisticsService.getAvailableTicketsForFullEvent(event);
        }
    }

    private boolean isAddDefaultAttributes(List<RegistrationAttribute> registrationAttributes) {
        return registrationAttributes.stream().noneMatch(RegistrationAttribute :: isDefault);
    }

    @Override
    public Long getCountOfTicketingTypes(long ticketTypeId){
        return registrationRequestTicketRepository.getCountOfTicketType(ticketTypeId);
    }

    @Override
    public List<RegistrationRequest> findByEventIdAndRequestByUserId(Long eventId, User user) {
        return registrationRequestRepository.findByEventIdAndRequestByUserId(eventId,user, RegistrationRequestType.ATTENDEE);
    }

    @Override
    public void changeStatusOfRegistrationRequest(Event event, User user) {
        List<RegistrationRequest> registrationRequests = this.findByEventIdAndRequestByUserId(event.getEventId(), user);
        List<Long> registrationRequestIdsOfMapping = Collections.emptyList();
        if (registrationRequests != null && !registrationRequests.isEmpty()) {
            List<Long> registrationRequestIds = registrationRequests.stream()
                    .map(RegistrationRequest::getId)
                    .collect(Collectors.toList());
            registrationRequestIdsOfMapping = registrationRequestAndStripeCustomerMappingService.checkRegistrationRequestIdIsPresent(registrationRequestIds);
        }
        List<Long> needToDeleteFromMapping = new ArrayList<>();

        if (!registrationRequests.isEmpty()) {
            log.info("changeStatusOfRegistrationRequest | eventId {} | userId {} |registrationRequest size : {}",event.getEventId(),user.getUserId(),registrationRequests.size());
            for (RegistrationRequest registrationRequest : registrationRequests) {
                List<RegistrationRequestTicket> registrationRequestTickets = registrationRequestTicketRepository.findByRegistrationRequest(registrationRequest);
                int totalRegisterRequestCount = registrationRequestTickets.stream().mapToInt(RegistrationRequestTicket::getNumberOfTicket).sum();
                log.info("changeStatusOfRegistrationRequest | eventId {} | userId {} | totalRegisterRequestCount {} | requestId {}",event.getEventId(),user.getUserId(),totalRegisterRequestCount,registrationRequest.getId());
                List<TicketingType> ticketTypeList = registrationRequestTickets.stream().map(RegistrationRequestTicket::getTicketType).collect(Collectors.toList());
                long totalPurchaserCount=0;
                if (!ticketTypeList.isEmpty()) {
                    totalPurchaserCount = eventTicketsService.getEventTicketsByTicketingIdAndPurchaserId(ticketTypeList, user);
                    log.info("changeStatusOfRegistrationRequest | eventId {} | userId {} | totalPurchaserCount {} | requestId {}",event.getEventId(),user.getUserId(),totalPurchaserCount,registrationRequest.getId());
                }
                if (totalPurchaserCount >= totalRegisterRequestCount) {
                    log.info("changeStatusOfRegistrationRequest totalRegisterRequestCount and totalPurchaserCount are equal for registrationRequest id {} and status is changed to REGISTERED",registrationRequest.getId());
                    registrationRequest.setStatus(RegistrationRequestStatus.valueOf(REGISTERED_CAPITAL));
                    if (!registrationRequestIdsOfMapping.isEmpty() && registrationRequestIdsOfMapping.contains(registrationRequest.getId())) {
                        log.info("changeStatusOfRegistrationRequest registrationRequest id {} is present in registrationRequestAndStripeCustomerMapping and need to delete from mapping", registrationRequest.getId());
                        needToDeleteFromMapping.add(registrationRequest.getId());
                    }
                    registrationRequestRepository.save(registrationRequest);
                }
            }
            if (!needToDeleteFromMapping.isEmpty()) {
                registrationRequestAndStripeCustomerMappingService.deleteAllByRegistrationId(needToDeleteFromMapping);
            }
        }
    }

    @Override
    public void createOrUpdateHolderAttributeMapping(Event event, RegistrationApprovalFieldHolderAttributeMappingDto mappingOfHolderAttributeDto) {
        if (mappingOfHolderAttributeDto!=null) {
            log.info("Mapping of holder attribute for event id {} and registration attribute id {}",event.getEventId(),mappingOfHolderAttributeDto.getRegistrationAttributeId());
            Optional<TicketHolderRequiredAttributes> ticketHolderAttributes=Optional.empty();
            if (mappingOfHolderAttributeDto.getHolderAttributeId()!=null) {
                ticketHolderAttributes=ticketRequiresAttributesRepo.findById(mappingOfHolderAttributeDto.getHolderAttributeId());
            }
            Optional<RegistrationAttribute> attribute=registrationAttributeService.findRegistrationAttributeByEventAndId(event,mappingOfHolderAttributeDto.getRegistrationAttributeId());
           if (attribute.isPresent()) {
               if (ticketHolderAttributes.isPresent()) {
                   TicketHolderRequiredAttributes ticketHolderRequiredAttributes = ticketHolderAttributes.get();
                   if (!ticketHolderRequiredAttributes.getEnabledForTicketHolder() && !ticketHolderRequiredAttributes.getEnabledForTicketPurchaser()) {
                       log.info("Attribute is not enabled for holder and purchaser attribute id {} and holder attribute id {} and event id {} ", mappingOfHolderAttributeDto.getRegistrationAttributeId(), mappingOfHolderAttributeDto.getHolderAttributeId(), event.getEventId());
                       throw new NotAcceptableException(NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_NOT_ENABLED_FOR_HOLDER_AND_PURCHASER);
                   }
               }
               RegistrationAttribute attributeData=attribute.get();
               attributeData.setHolderAttributeId(mappingOfHolderAttributeDto.getHolderAttributeId());
               registrationAttributeRepository.save(attributeData);
               log.info("Mapping of holder attribute for event id {} and registration attribute id {} is done",event.getEventId(),mappingOfHolderAttributeDto.getRegistrationAttributeId());
           }else {
               throw new NotFoundException(NotFoundException.NotFound.REGISTRATION_ATTRIBUTE_NOT_FOUND);
           }
        }
    }

    @Override
    public void approveRegistrationRequestAndCreateOrder(User user, Event event, Long requestId, boolean isAnyPaidTicket, String couponCode) {
        log.info("approveRegistrationRequestAndCreateOrder for event id {} and request id {} userId {} isAnyPaidTicket {} couponCode {}",event.getEventId(),requestId,user.getUserId(),isAnyPaidTicket, couponCode);

        List<RegistrationRequestTicket> registrationRequestTickets = registrationRequestTicketRepository.findAllByRegistrationRequestIds(Collections.singletonList(requestId));
        if (!isAnyPaidTicket) {
            List<TicketType> ticketTypes = registrationRequestTickets.stream()
                    .map(RegistrationRequestTicket::getTicketType)
                    .map(TicketingType::getTicketType)
                    .collect(Collectors.toList());
            if (ticketTypes.contains(TicketType.PAID)) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_ALLOW_FOR_PAID_TICKETS);
            }
        } else if (StringUtils.isNotBlank(couponCode)) {
            RegistrationRequest registrationRequest = registrationRequestRepository.findById(requestId).orElse(null);
            User purchaser = registrationRequest != null ? registrationRequest.getRequestedByUser() : null;
            ticketingCouponCodeService.applyCouponCodeInApprovalTicket(event, new CouponCodeApprovalTicketDto(couponCode, registrationRequest != null ? registrationRequest.getRecurringEventId() : 0, requestId), purchaser, user.getUserId());
        }
        try {
            List<RegistrationRequest> registrationRequests = updateRegistrationRequestStatus(user, event, requestId);
            log.info("update registration request status for free tickets event id {} and request id {} userId {} is done",event.getEventId(),requestId,user.getUserId());
            createBookTicketOrderForRegistrationApproval(user, event, registrationRequestTickets, requestId, isAnyPaidTicket, couponCode);

            postProcessAfterRegistrationRequestCreation(registrationRequests, event, TrayIOWebhookActionType.REGISTRATION_REQUEST_UPDATE);
        } catch (StripeException e) {
            log.error("approveRegistrationRequestAndCreateOrder stripe exception {}", e.getStripeError().getMessage());
            throw new NotAcceptableException(e.getStripeError().getCode(),e.getStripeError().getMessage(),e.getStripeError().getMessage());
        } catch (Exception e) {
            log.error("approveRegistrationRequestAndCreateOrderForFreeTickets exception {}", e.getMessage());
            throw new NotAcceptableException(e);
        }

    }

    private void createBookTicketOrderForRegistrationApproval(User user, Event event, List<RegistrationRequestTicket> registrationRequestTickets, Long requestId, boolean isAnyPaidTicket, String couponCode) throws StripeException, JAXBException, ApiException {
        Optional<RegistrationRequest> registrationRequest = registrationRequestRepository.findByIdAndEventWithTrackingLink(requestId,event.getEventId());
        List<TicketingOrderDto> ticketingOrderDtos = new ArrayList<>();
        for (RegistrationRequestTicket registrationRequestTicket : registrationRequestTickets) {
            TicketingOrderDto ticketingOrderDto = new TicketingOrderDto();
            ticketingOrderDto.setTicketTypeId(registrationRequestTicket.getTicketType().getId());
            ticketingOrderDto.setNumberOfTicket(registrationRequestTicket.getNumberOfTicket());
            ticketingOrderDto.setPrice(registrationRequestTicket.getTicketType().getPrice());
            ticketingOrderDtos.add(ticketingOrderDto);
        }
        String tokenOrIntentId = STRING_EMPTY;
        if (isAnyPaidTicket){
            RegistrationRequestAndStripeCustomerMapping registrationRequestAndStripeCustomerMapping = registrationRequestAndStripeCustomerMappingService.findByRegistrationRequestId(requestId);
            if (registrationRequestAndStripeCustomerMapping == null) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CARD_DETAILS_NOT_PRESENT);
            }
            tokenOrIntentId = getTokenOrIntentId(event, registrationRequestAndStripeCustomerMapping);
            log.info("Card details for registration requestId {} | tokenOrIntentId {}", requestId, tokenOrIntentId);
        }
        TicketingOrderRequestDto ticketingOrderRequestDto = new TicketingOrderRequestDto();
        ticketingOrderRequestDto.setTicketings(ticketingOrderDtos);
        ticketingOrderRequestDto.setClientDate(String.valueOf(new Date()));
        if (registrationRequest.isPresent()) {
            RegistrationRequest registrationRequestData = registrationRequest.get();
            log.info("Received UTM tracking data for Ticketing Order : {} and registrationRequest type : {} ", registrationRequestData.getRecSource(),registrationRequestData.getType());
            if(RegistrationRequestType.ATTENDEE.equals(registrationRequestData.getType()) && StringUtils.isNotBlank(registrationRequestData.getRecSource())){
                log.info("UTM tracking data for Ticketing Order: {} ", registrationRequestData.getRecSource());
                ticketingOrderRequestDto.setUtmTrackSourceDto(JsonMapper.stringtoObject(registrationRequestData.getRecSource(),UTMTrackSourceDto.class));
            }

            OrderDto orderDto = ticketingPurchaseService.bookTicket(
                    event, ticketingOrderRequestDto.getTicketings(), user, new Date(), TicketingOrder.OrderType.CARD, false, STRING_EMPTY, null,STRING_EMPTY, registrationRequestData.getRecurringEventId(),ticketingOrderRequestDto.getUtmTrackSourceDto(), registrationRequestData.getRequestedForUser());
            if (orderDto != null) {
                // apply coupon code
                if (StringUtils.isNotBlank(couponCode)) {
                    TicketingOrder ticketingOrder = ticketingDisplayService.setPurchaserInTicketingOrder(registrationRequestData.getRequestedForUser(), orderDto.getOrderId(), event);
                    ticketingCouponCodeService.applyCouponCode(couponCode, orderDto.getOrderId(), event, registrationRequestData.getRecurringEventId(), Collections.emptyList(), ticketingOrder);
                    log.info("Coupon code {} applied for order id {} for event id {} and request id {}", couponCode, orderDto.getOrderId(), event.getEventId(), requestId);
                }
                TicketingCheckOutDto ticketingCheckOutDto=formAttributes(registrationRequestData.getRequestedForUser(), event, orderDto.getOrderId(), false, false,registrationRequestData.getId());
                createMappingBetweenRegistrationAttributesAndHolderAttributes(ticketingCheckOutDto,registrationRequestData,event,orderDto.getOrderId(),registrationRequestTickets,tokenOrIntentId);
            }
        }
    }

    private String getTokenOrIntentId(Event event, RegistrationRequestAndStripeCustomerMapping registrationRequestAndStripeCustomerMapping) throws StripeException {
        String tokenOrIntentId = STRING_EMPTY;
        Stripe stripe = roStripeService.findByEvent(event);
        if(EnumPaymentGateway.STRIPE.value().equalsIgnoreCase(stripe.getPaymentGateway())) {
            PaymentIntentResDTO paymentIntentResDTO = stripeService.setUpPaymentIntentAuthentication(event, new PaymentIntentBaseDTO(registrationRequestAndStripeCustomerMapping.getStripeCardId(), false, false));
            tokenOrIntentId = paymentIntentResDTO.getPaymentIntentId();
        } else if (StripeUtil.isPaymentGatewayFromSpreedly(stripe.getPaymentGateway())) {
            tokenOrIntentId = registrationRequestAndStripeCustomerMapping.getStripeCardId();
        }
        return tokenOrIntentId;
    }

    private void createMappingBetweenRegistrationAttributesAndHolderAttributes(TicketingCheckOutDto ticketingCheckOutDto, RegistrationRequest registrationRequestData, Event event,Long orderId,List<RegistrationRequestTicket> registrationRequestTickets, String tokenOrIntentId) throws StripeException, JAXBException, ApiException {
        TicketBookingDto ticketBookingDto=new TicketBookingDto();
        PurchaserBookingDto purchaserBookingDto=setPurchaserDetails(event,registrationRequestData);
        List<HolderBookingDto> holderBookingDtoList=setHolderAttributesDetails(event,registrationRequestData,registrationRequestTickets,purchaserBookingDto);
        ticketBookingDto.setPurchaser(purchaserBookingDto);
        ticketBookingDto.setHolder(holderBookingDtoList);
        ticketBookingDto.setClientDate(String.valueOf(new Date()));
        ticketBookingDto.setPayLater(false);
        ticketBookingDto.setHasholderattributes(ticketingCheckOutDto.getTicketAttribute().isHasHolderAttributes());
        ticketBookingDto.setDisclaimer(registrationRequestData.getApprovalDisclaimer());
        ticketBookingDto.setWlDisclaimer(registrationRequestData.getApprovalWLDisclaimer());
        ticketBookingDto.setTokenOrIntentId(tokenOrIntentId);
        String trackingLinks = registrationRequestData.getTrackingLinkId()!= null ? registrationRequestData.getTrackingLinks().getLinkUrl() : STRING_EMPTY;
        ticketingPurchaseService.purchaseTicket(event,ticketBookingDto,orderId,registrationRequestData.getRequestedForUser(),null,null,trackingLinks,STRING_EMPTY,false,false,false,CheckoutFrom.ATTENDEE_CHECKOUT,false, false, 0l);
        log.info("createMappingBetweenRegistrationAttributesAndHolderAttributes for event id {} and order id {} is done",event.getEventId(),orderId);

    }

    private List<HolderBookingDto> setHolderAttributesDetails(Event event,RegistrationRequest registrationRequest,List<RegistrationRequestTicket> registrationRequestTickets,PurchaserBookingDto purchaserBookingDto) {
        List<HolderBookingDto> holderBookingDtos = new ArrayList<>();
        List<RegistrationRequestHolderDetails> registrationRequestHolderDetailsForAllTickets = registrationRequestHolderDetailsRepository.findByRegistrationRequestId(registrationRequest.getId());
        Map<Long,List<RegistrationRequestHolderDetails>> registrationRequestHolderDetailsWithTicketTypeId = registrationRequestHolderDetailsForAllTickets.stream().collect(Collectors.groupingBy(RegistrationRequestHolderDetails::getTicketTypeId));
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes;
        if (registrationRequest.getRecurringEventId() != null && isNumberGreaterThanZero(registrationRequest.getRecurringEventId())) {
            ticketHolderRequiredAttributes = ticketRequiresAttributesRepo.findAllAttributesByEventAndRecurringEventId(event, registrationRequest.getRecurringEventId());
        } else {
            ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService.findByEventId(event);
        }
        for (RegistrationRequestTicket registrationRequestTicket : registrationRequestTickets) {
            List<RegistrationRequestHolderDetails> registrationRequestHolderDetails = registrationRequestHolderDetailsWithTicketTypeId.get(registrationRequestTicket.getTicketType().getId());
            if (!CollectionUtils.isEmpty(registrationRequestHolderDetails)) {
                for (RegistrationRequestHolderDetails registrationRequestHolderDetail : registrationRequestHolderDetails) {
                    TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(registrationRequestHolderDetail.getJsonValue());
                    Map<String, String> holderAttributesMap = (Map<String, String>) ticketAttributeValueDto.getHolder().get(Constants.TICKETING.ATTRIBUTES);
                    List<AttributeKeyValueDto> holderAttributes = new ArrayList<>();
                    for (TicketHolderRequiredAttributes ticketHolderRequiredAttribute : ticketHolderRequiredAttributes) {
                        if (ticketHolderRequiredAttribute.getEnabledForTicketHolder()) {
                            AttributeKeyValueDto attributeKeyValueDto = new AttributeKeyValueDto();
                            if (ticketHolderRequiredAttribute.getName().equalsIgnoreCase(CELL_PHONE)) {
                                processCellPhoneAttribute(holderAttributesMap, holderAttributes);
                            } else {
                                attributeKeyValueDto.setKey(ticketHolderRequiredAttribute.getName());
                                setHolderAttributeValues(ticketHolderRequiredAttribute, holderAttributesMap, attributeKeyValueDto,registrationRequestHolderDetail.getTicketTypeId());
                                holderAttributes.add(attributeKeyValueDto);
                            }
                        }
                    }
                    HolderBookingDto holderBookingDto = new HolderBookingDto();
                    holderBookingDto.setAttributes(holderAttributes);
                    setHolderNestedQuestionsWhileAssignAndApproveTicket(holderBookingDto, ticketAttributeValueDto);
                    holderBookingDto.setTickettypeid(registrationRequestTicket.getTicketType().getId());
                    holderBookingDtos.add(holderBookingDto);
                }
            } else {
                //if ticket holder attributes are not collected then set purchaser attributes as holder attributes same as normal ticket purchase flow
                Ticketing ticketing=ticketingService.findByEvent(event);
                if (ticketing!=null && !ticketing.getCollectTicketHolderAttributes() && purchaserBookingDto!=null) {
                    HolderBookingDto holderBookingDto = new HolderBookingDto();
                    holderBookingDto.setAttributes(purchaserBookingDto.getAttributes());
                    holderBookingDto.setNestedQuestions(purchaserBookingDto.getNestedQuestions());
                    holderBookingDto.setTickettypeid(registrationRequestTicket.getTicketType().getId());
                    holderBookingDtos.add(holderBookingDto);
                }
            }
        }
        return holderBookingDtos;
    }

    private void setHolderAttributeValues(TicketHolderRequiredAttributes ticketHolderRequiredAttribute, Map<String, String> holderAttributesMap,AttributeKeyValueDto attributeKeyValueDto,Long ticketingTypeId) {
        if (ticketHolderRequiredAttribute.getName().equalsIgnoreCase("email")) {
            attributeKeyValueDto.setValue(holderAttributesMap.get(EMAIL));
        } else if (ticketHolderRequiredAttribute.getAttributeValueType().equals(AttributeValueType.DATE)) {
            String inputDateString = holderAttributesMap.get(ticketHolderRequiredAttribute.getName());
            if (inputDateString != null && !inputDateString.isEmpty()) {
                attributeKeyValueDto.setValue(isValidDateFormat(inputDateString) ? inputDateString : processForDateTypeAttribute(ticketHolderRequiredAttribute, attributeKeyValueDto, inputDateString));
            }
        } else {
            if (holderAttributesMap.get(ticketHolderRequiredAttribute.getName()) != null) {
                attributeKeyValueDto.setValue(holderAttributesMap.get(ticketHolderRequiredAttribute.getName()));
            } else if (isAttributeMandatoryForTicketingType(ticketingTypeId,ticketHolderRequiredAttribute.getHolderRequiredTicketTypeId())){
                attributeKeyValueDto.setValue(STRING_EMPTY);
            }
        }
    }

    private void setHolderNestedQuestionsWhileAssignAndApproveTicket(HolderBookingDto holderBookingDto, TicketAttributeValueDto1 ticketAttributeValueDto) {
        Map<Long, Map<String, Object>> holderNestedAttributesMap = new HashMap<>();
        List<Map<String, Object>> holderNestedAttributeList = (List<Map<String, Object>>) ticketAttributeValueDto.getHolder().get(Constants.TICKETING.NESTEDQUESTIONS);
        if(!CollectionUtils.isEmpty(holderNestedAttributeList)) {
            holderNestedAttributesMap = holderNestedAttributeList.stream().collect(Collectors.toMap(e -> (long)Double.parseDouble(String.valueOf(e.get("id"))), e -> e, (old1, new1) -> new1));
        }
        if (!CollectionUtils.isEmpty(holderNestedAttributesMap)){
            List<NestedQuestionsDto> nestedQuestionsDtoList=holderNestedAttributesMap.values().stream().map(e -> {
                NestedQuestionsDto nestedQuestionsDto = new NestedQuestionsDto();
                nestedQuestionsDto.setId((long)Double.parseDouble(String.valueOf(e.get(ID))));
                nestedQuestionsDto.setName(String.valueOf(e.get(NAME_SMALL)));
                nestedQuestionsDto.setValue(String.valueOf(e.get(VALUE_SMALL)));
                nestedQuestionsDto.setType(String.valueOf(e.get(TYPE_LOWER_CASE)));
                nestedQuestionsDto.setParentQueId((long)Double.parseDouble(String.valueOf(e.get(PARENT_QUESTION_ID))));
                return nestedQuestionsDto;
            }).collect(Collectors.toList());
            holderBookingDto.setNestedQuestions(nestedQuestionsDtoList);
        }
    }

    public static boolean isValidDateFormat(String dateToCheck) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
        try {
            formatter.parse(dateToCheck);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private String processForDateTypeAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttribute, AttributeKeyValueDto attributeKeyValueDto, String inputDateString) {
        SimpleDateFormat inputDateFormat = new SimpleDateFormat(DATE_TIME_FORMAT_FULL_STANDARD_UTC);
        inputDateFormat.setTimeZone(TimeZone.getTimeZone(UTC_TIMEZONE));
        try {
            Date inputDate = inputDateFormat.parse(inputDateString);
            if (inputDate != null) {
                SimpleDateFormat outputDateFormat = new SimpleDateFormat(DATE_FORMAT_ONLY_MONTH);
                String newDateFormat = outputDateFormat.format(inputDate);
                //if date is in future and attribute is birthday then set empty string because we don't allow future date for birthday attribute in holder attributes
                if (inputDate.after(new Date()) && ticketHolderRequiredAttribute.getName().equalsIgnoreCase(BIRTHDAY)){
                    attributeKeyValueDto.setValue(STRING_EMPTY);
                }else {
                    attributeKeyValueDto.setValue(newDateFormat);
                }
            }
        } catch (NotAcceptableException | ParseException e) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DATE_FORMAT_NOT_VALID);
        }
        return attributeKeyValueDto.getValue();
    }

    private void processCellPhoneAttribute(Map<String, String> attributes, List<AttributeKeyValueDto> holderAttributes) {
        String cellPhoneAndCountryCode = attributes.get(CELL_PHONE);
        String phoneNumber= attributes.get(PHONE_NUMBER_KEY);
        String countryCode= attributes.get(COUNTRY_CODE_KEY);
        if (cellPhoneAndCountryCode != null) {
            String[] splitValues = cellPhoneAndCountryCode.split("\\|");
            phoneNumber = splitValues[0];
            countryCode = (splitValues.length > 1) ? splitValues[1] : null;
        }
        holderAttributes.add(createAttributeKeyValueDto(PHONE_NUMBER_KEY, phoneNumber));
        holderAttributes.add(createAttributeKeyValueDto(COUNTRY_CODE_KEY, countryCode));
        if(phoneNumber!=null &&  countryCode!=null) {
            holderAttributes.add(createAttributeKeyValueDto(CELL_PHONE, phoneNumber + ADD_SEPARATOR + countryCode));
        }else {
            holderAttributes.add(createAttributeKeyValueDto(CELL_PHONE,STRING_EMPTY));
        }
    }

    private AttributeKeyValueDto createAttributeKeyValueDto(String key, String value) {
        AttributeKeyValueDto attributeKeyValueDto = new AttributeKeyValueDto();
        attributeKeyValueDto.setKey(key);
        attributeKeyValueDto.setValue(value);
        return attributeKeyValueDto;
    }

    private PurchaserBookingDto setPurchaserDetails(Event event,RegistrationRequest registrationRequest) {
        PurchaserBookingDto purchaserBookingDto=new PurchaserBookingDto();
        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
        List<Long> ticketingTypeIds = registrationRequestTicketRepository.findTicketingTypesByRegistrationRequestIds(Collections.singletonList(registrationRequest.getId()));
        Map<String, String> attributes= registrationResponseDto.getAttributes();
        List<NestedQuestionsDto> nestedQuestions=registrationResponseDto.getNestedQuestions();
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes;
        if(registrationRequest.getRecurringEventId()!=null && isNumberGreaterThanZero(registrationRequest.getRecurringEventId())){
            ticketHolderRequiredAttributes=ticketRequiresAttributesRepo.findAllAttributesByEventAndRecurringEventId(event,registrationRequest.getRecurringEventId());
        }else {
            ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService.findByEventId(event);
        }
        List<AttributeKeyValueDto> purchaserAttributes=new ArrayList<>();
        for (TicketHolderRequiredAttributes ticketHolderRequiredAttribute : ticketHolderRequiredAttributes) {
            if (ticketHolderRequiredAttribute.getEnabledForTicketPurchaser()) {
                AttributeKeyValueDto attributeKeyValueDto = new AttributeKeyValueDto();
                attributeKeyValueDto.setKey(ticketHolderRequiredAttribute.getName());
                if (ticketHolderRequiredAttribute.getName().equalsIgnoreCase(CELL_PHONE)) {
                    processCellPhoneAttribute(attributes, purchaserAttributes);
                }else {
                    if (ticketHolderRequiredAttribute.getName().equalsIgnoreCase("email")) {
                        attributeKeyValueDto.setValue(attributes.get(EMAIL));
                    } else if (ticketHolderRequiredAttribute.getAttributeValueType().equals(AttributeValueType.DATE)) {
                        String inputDateString=attributes.get(ticketHolderRequiredAttribute.getName());
                        if (inputDateString != null && !inputDateString.isEmpty()) {
                            attributeKeyValueDto.setValue(isValidDateFormat(inputDateString) ? inputDateString : processForDateTypeAttribute(ticketHolderRequiredAttribute, attributeKeyValueDto, inputDateString));
                        }
                    } else {
                        if (attributes.get(ticketHolderRequiredAttribute.getName())!=null) {
                            attributeKeyValueDto.setValue(attributes.get(ticketHolderRequiredAttribute.getName()));
                        } else if (isAttributeMandatoryForTicketingType(ticketingTypeIds,ticketHolderRequiredAttribute.getBuyerRequiredTicketTypeId())) {
                                attributeKeyValueDto.setValue(STRING_EMPTY);
                        }
                    }
                    purchaserAttributes.add(attributeKeyValueDto);
                }
            }
        }
        purchaserBookingDto.setAttributes(purchaserAttributes);
        if (!CollectionUtils.isEmpty(nestedQuestions)) {
            purchaserBookingDto.setNestedQuestions(nestedQuestions);
        }
        return purchaserBookingDto;
    }

    private TicketingCheckOutDto formAttributes(User user, Event event, Long orderId, boolean isStaff, boolean payLater, Long registrationRequestIdLong) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        TicketingCheckOutDto ticketingCheckOutDto = new TicketingCheckOutDto();
        TicketAttributeDto ticketAttribute=ticketingPurchaseService.getFormAttributes(payLater,event,orderId,user,isStaff,registrationRequestIdLong, false);
        ticketingCheckOutDto.setTicketAttribute(ticketAttribute);
        if (user != null && user.getUserId() != 0) {
            ticketingCheckOutDto.setPurchaserDetail(this.userService.extractUserInfoForEvent(user.getUserId(), event, true));
        }
        ticketingCheckOutDto.setCustomDisclaimer(ticketing.getCustomDisclaimer());
        return ticketingCheckOutDto;
    }


    private List<RegistrationRequest> updateRegistrationRequestStatus(User user, Event event, Long requestId) {
        List<Long> rejectedRegistrationRequest = new ArrayList<>();
        if(requestId!=null){
            List<Long> availableTicketType = new ArrayList<>();
            List<RegistrationRequestTicketTypeDTO> registrationRequestTickets = registrationRequestTicketRepository.findTicketingTypeIdAndRegistrationIdByRegistrationRequestIds(Collections.singletonList(requestId));
            List<Long> registeredTicketTypes = registrationRequestTickets.stream().map(RegistrationRequestTicketTypeDTO::getTicketTypeId).filter(Objects::nonNull).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(registeredTicketTypes)){
                availableTicketType = ticketingTypeRepository.findByIds(registeredTicketTypes).stream().map(TicketingType::getId).collect(Collectors.toList());
            }
            List<Long> unavailableTicketTypes = new ArrayList<>(org.apache.commons.collections.CollectionUtils.disjunction(registeredTicketTypes,availableTicketType));
            List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByEventAndIds(event, Collections.singletonList(requestId));
            List<RegistrationRequest> finalRegistrationRequest = new ArrayList<>();
            registrationRequests.forEach(e -> {
                if (e.getType().equals(RegistrationRequestType.ATTENDEE)) {
                    Long registrationRequestTicketTypeId = registrationRequestTickets.stream()
                            .filter(rrt -> rrt.getRegistrationRequestId().equals(requestId))
                            .map(RegistrationRequestTicketTypeDTO::getTicketTypeId)
                            .findFirst()
                            .orElse(null);
                    if (registrationRequestTicketTypeId != null && !unavailableTicketTypes.contains(registrationRequestTicketTypeId)) {
                        approveRegistrationRequest(e, user);
                        finalRegistrationRequest.add(e);
                        log.info("Registration request {} for TicketTypeId {} approved by {}.",requestId,registrationRequestTicketTypeId,user);
                    } else {
                        rejectedRegistrationRequest.add(e.getId());
                        log.info("Registration request {} for TicketTypeId {} rejected by {}.",requestId,registrationRequestTicketTypeId,user);
                    }
                } else {
                    approveRegistrationRequest(e, user);
                    finalRegistrationRequest.add(e);
                }
            });
            return finalRegistrationRequest;
        }
        return Collections.emptyList();
    }

    private void approveRegistrationRequest(RegistrationRequest registrationRequest, User user) {
        addHistory(registrationRequest, RegistrationRequestStatus.APPROVED, user);
        registrationRequest.setRecStatus(RecordStatus.UPDATE);
        registrationRequest.setUpdatedAt(new Date());
        registrationRequest.setStatus(RegistrationRequestStatus.APPROVED);
        registrationRequest.setUpdatedBy(user);
        registrationRequest.setRecentApprovedAt(new Date());
    }
    private boolean isApprovalRequestEmailsEnabled(Event event){
        return  ticketingService.isApprovalRequestEmailsEnabled(event);
    }

    private void sendApprovalEmailToRegistrationApprovers(Event event, User user, RegistrationRequestType registrationRequestType) {
        try {
            log.info("sendApprovalEmailToRegistrationApprovers eventId: {}", event.getEventId());
            List<Long> userIdList = GeneralUtils.convertCommaSeparatedToListLong(ticketingService.findApprovalEmailReceiverIdsByEvent(event));
            log.info("Users ids : {}", userIdList);
            if (!userIdList.isEmpty()) {
                List<User> receivers = roUserService.getListOfUsersByUserIds(userIdList);
                registrationApprovalEmailService.sendApprovalEmailToRegistrationApprovers(event, user, receivers, registrationRequestType);
            }
        } catch (Exception ex) {
            log.error("sendApprovalEmailToRegistrationApprovers eventId: {} errorMsg: {}", event.getEventId(), ex.getMessage());
        }
    }

    public static String getAttendeeAttributeValue(RegistrationResponseDto registrationResponseDto,
                                            String attributeName,
                                            boolean isAttribute,boolean isConditionalQuestion) {
        if(registrationResponseDto != null) {
            if (isAttribute) {
                if (isConditionalQuestion && registrationResponseDto.getNestedQuestions() != null) {
                    List<NestedQuestionsDto> nestedQuestions = registrationResponseDto.getNestedQuestions();
                    for (NestedQuestionsDto questionsDto : nestedQuestions) {
                        boolean isEnterValue = questionsDto.getName().equals(attributeName);
                        if (isEnterValue) {
                            return attributeValueCheck(questionsDto.getValue());
                        }
                    }
                } else if(registrationResponseDto.getAttributes() != null){
                    Map<String, String> purchaserAttribute = registrationResponseDto.getAttributes();
                    if (purchaserAttribute != null && !CollectionUtils.isEmpty(purchaserAttribute)) {
                        for (Map.Entry<String, String> entry : purchaserAttribute.entrySet()) {
                            if (attributeName.equalsIgnoreCase(entry.getKey()) || (EMAIL.equalsIgnoreCase(attributeName) && EMAIL_ADDRESS.equalsIgnoreCase(entry.getKey()))) {
                                return attributeValueCheck(entry.getValue());
                            }
                        }
                    }
                }
            } else if(registrationResponseDto.getQuestions() != null){
                Map<String, String> purchaserQue = registrationResponseDto.getQuestions();
                if (purchaserQue != null && !CollectionUtils.isEmpty(purchaserQue)) {
                    for (Map.Entry<String, String> entry : purchaserQue.entrySet()) {
                        if (attributeName.equalsIgnoreCase(entry.getKey())) {
                            return attributeValueCheck(entry.getValue());
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void updateRegistrantPurchaserDetails(Long requestId, AttendeeAttributeValueDto attendeeAttributesDto, Event event, User requestedUser, Long recurringEventId) {

        log.info("update registration request received by user {} for registration Id {}, data {}", requestedUser.getEmail(), requestId, attendeeAttributesDto);
        RegistrationRequest registrationRequest = getRegistrationRequestsOrThrowError(event, requestId);

        List<TicketHolderRequiredAttributes> requiredAttributes = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(event, recurringEventId);

        Map<String, TicketHolderRequiredAttributes> holderAttributesMap = requiredAttributes.stream().filter(TicketHolderRequiredAttributes::getEnabledForTicketPurchaser).collect(Collectors.toMap(TicketHolderRequiredAttributes::getName, Function.identity()));
        Optional<AttributeKeyValueDto> countryCodeOptional = attendeeAttributesDto.getAttributes().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(COUNTRY_CODE_KEY)).findFirst();

        Map<String, String> attributesMap = new HashMap<>();

        attendeeAttributesDto.getAttributes().forEach(attribute -> {
            if (FIRST_NAME.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 50) {
                throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.FIRST_NAME_SIZE_LIMIT);
            } else if (LAST_NAME.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 50) {
                throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.LAST_NAME_SIZE_LIMIT);
            } else if (EMAIL.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 75) {
                throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.EMAIL_SIZE_LIMIT);
            } else if (EMAIL.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && !GeneralUtils.isValidEmailAddress(attribute.getValue().trim())) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_EMAIL);
            } else if (PHONE_NUMBER_KEY.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && countryCodeOptional.isPresent()) {
                if (!twilioPhoneNumberValidateService.isValidPhoneNumber(countryCodeOptional.get().getValue(), attribute.getValue())) {
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PHONE_NUMBER);
                }
            }
            if (!CELL_PHONE.equalsIgnoreCase(attribute.getKey())) {
                validateTicketAttributeValuesBeforeUpdate(holderAttributesMap, attribute);
            }
            attributesMap.put(attribute.getKey(), attribute.getValue());
        });

        User newUser = null;
        User oldUser = registrationRequest.getRequestedForUser();
        long oldPhoneNumber = oldUser.getPhoneNumber();
        String newMobileStr = attributesMap.get(Constants.STRING_PHONE_NUMBER);

        long newPhoneNumber = StringUtils.isNotBlank(newMobileStr) ? Long.parseLong(GeneralUtils.replaceAllNonNumericCharacters(newMobileStr)): oldPhoneNumber;

        if(oldUser.getEmail() != null && attributesMap.get(EMAIL) != null && !oldUser.getEmail().equals(attributesMap.get(EMAIL))){
            UserSignupDto userSignupDto = new UserSignupDto();
            userSignupDto.setEmail(attributesMap.get(EMAIL));
            userSignupDto.setFirstName(attributesMap.get(FIRST_NAME));
            userSignupDto.setLastName(attributesMap.get(LAST_NAME));
            User user = userService.signUpBidderUserAndReturnUser(userSignupDto, null, true, false, false, false,
                    true);
            if(!oldUser.getUserId().equals(user.getUserId())){
                newUser = user;
            }
        }
        else if (newPhoneNumber > 0 && oldPhoneNumber != newPhoneNumber) {
            Optional<User> userOpt = roUserService.getUserById(oldUser.getUserId());
            if(userOpt.isPresent()) {
                User user = userOpt.get();
                Optional<User> existingUser = roUserService.getUserByAEPhoneNumber(new AccelEventsPhoneNumber(user.getCountryCode(), newPhoneNumber));
                if (existingUser.isPresent()) {
                    throw new ConflictException(ConflictException.UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_DIFFERENT_ACCOUNT);
                } else {
                    user.setPhoneNumber(newPhoneNumber);
                    userService.updateUserPhoneNumber(user);
                }
            }
        }

        if (newUser != null) {
            registrationRequest.setRequestedForUser(newUser);
            registrationRequestRepository.save(registrationRequest);
        }

        if(newUser == null){
            oldUser.setFirstName(attributesMap.get(FIRST_NAME));
            oldUser.setLastName(attributesMap.get(LAST_NAME));
            userService.save(oldUser);
        }

        this.updateTicketPurchaserData(attendeeAttributesDto, registrationRequest);

    }

    private void validateTicketAttributeValuesBeforeUpdate(Map<String, TicketHolderRequiredAttributes> holderAttributesMap, AttributeKeyValueDto attribute) {
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = holderAttributesMap.get(attribute.getKey());
        if (ticketHolderRequiredAttributes != null) {
            if (StringUtils.isBlank(attribute.getValue())) {
                NotAcceptableException.NotAceptableExeceptionMSG mandatoryFieldException = NotAcceptableException.NotAceptableExeceptionMSG.MANDATORY_FIELD;
                String errorMessage = mandatoryFieldException.getErrorMessage().replace("${field}",ticketHolderRequiredAttributes.getName());
                mandatoryFieldException.setErrorMessage(errorMessage);
                mandatoryFieldException.setDeveloperMessage(errorMessage);
                throw  new NotAcceptableException(mandatoryFieldException);
            }
            if (ticketHolderRequiredAttributes.getAttributeValueType().equals(AttributeValueType.NUMBER) && StringUtils.isNotBlank(attribute.getValue()) && !PATTERN_FOR_NUMBER.matcher(attribute.getValue()).matches()) {
                NotAcceptableException.NotAceptableExeceptionMSG mismatchValueWithFieldTypeException = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                String errorMessage = mismatchValueWithFieldTypeException.getErrorMessage().replace("${field}",ticketHolderRequiredAttributes.getName()).replace("${type}",ticketHolderRequiredAttributes.getAttributeValueType().getType());
                mismatchValueWithFieldTypeException.setErrorMessage(errorMessage);
                mismatchValueWithFieldTypeException.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(mismatchValueWithFieldTypeException);
            }
        }
    }

    private void updateTicketPurchaserData(AttendeeAttributeValueDto attendeeAttributesDto, RegistrationRequest registrationRequest) {
        TicketHolderAttributes ticketHolderAttributes = registrationRequest.getTicketHolderAttributes();
        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());

        Map<String, String> purchaserAttributes = attendeeAttributesDto.getAttributes().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e-> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));
        Map<String, String> purchaserQue = attendeeAttributesDto.getQuestions().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e-> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));

        registrationResponseDto.setAttributes(purchaserAttributes);
        registrationResponseDto.setQuestions(purchaserQue);
        registrationResponseDto.setNestedQuestions(updateBillingAndShippingAddress(attendeeAttributesDto));

        handleJSONValue(ticketHolderAttributes, registrationResponseDto);
    }

    private List<NestedQuestionsDto> updateBillingAndShippingAddress(AttendeeAttributeValueDto attendeeAttributesDto) {
        List<NestedQuestionsDto> nestedQuestionsDtos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(attendeeAttributesDto.getNestedQuestions())){
            List<UpdateNestedQueAnsDto> updateNestedQueAnsDtos = attendeeAttributesDto.getNestedQuestions();

            updateNestedQueAnsDtos.forEach(nestedQue->{
                NestedQuestionsDto nestedQuestionsDto = new NestedQuestionsDto();
                nestedQuestionsDto.setId(nestedQue.getId());
                nestedQuestionsDto.setName(nestedQue.getName());
                nestedQuestionsDto.setParentQueId(nestedQue.getParentQueId());
                nestedQuestionsDto.setValue(nestedQue.getValue());
                nestedQuestionsDto.setType(nestedQue.getType());
                nestedQuestionsDtos.add(nestedQuestionsDto);
            });
            log.info("mapForHolderAndAddons {}",nestedQuestionsDtos);
        }
        return nestedQuestionsDtos;
    }

    private void handleJSONValue(TicketHolderAttributes ticketHolderAttributes, RegistrationResponseDto registrationResponseDto) {
        ticketHolderAttributes.setJsonValue(parseToJsonString(registrationResponseDto));
        ticketHolderAttributesService.save(ticketHolderAttributes);
    }

    public String parseToJsonString(RegistrationResponseDto registrationResponseDto){
        Gson gson = new Gson();
        return gson.toJson(registrationResponseDto, RegistrationResponseDto.class);
    }


    @Override
    public List<NameValueDto> getAllDynamicMergeTagsListForApproval(Event event, Long recurringEventId, ConfirmationApprovalType confirmationApprovalType) {
        List<NameValueDto> mergeTags = new ArrayList<>();

        List<RegistrationAttributeType> registrationAttributeType=getRegistrationAttributeTypeByConfirmationApprovalType(confirmationApprovalType);

        List<RegistrationAttribute> registrationAttributes;
        if (NumberUtils.isNumberGreaterThanZero(recurringEventId)) {
            registrationAttributes = registrationAttributeRepository.findByEventAndRecurringEventIdAndTypesOrderByPosition(event, recurringEventId, registrationAttributeType);
        } else {
            registrationAttributes = registrationAttributeRepository.findByEventAndRecurringEventIdNullAndTypesOrderByPosition(event, registrationAttributeType);
        }

        if (ConfirmationApprovalType.EXPO_INFO_AND_DETAILS.equals(confirmationApprovalType)
                || ConfirmationApprovalType.SPEAKER_INFO_AND_DETAILS.equals(confirmationApprovalType)
                || ConfirmationApprovalType.REVIEWER_INFO_AND_DETAILS.equals(confirmationApprovalType)) {
            registrationAttributes.sort(Comparator
                    .comparing((RegistrationAttribute attr) -> {
                        if (RegistrationAttributeType.SPEAKER_INFO.equals(attr.getType()) || RegistrationAttributeType.EXPO_INFO.equals(attr.getType())|| RegistrationAttributeType.REVIEWER_INFO.equals(attr.getType())) {
                            return 1;
                        } else if (RegistrationAttributeType.SPEAKER_DETAILS.equals(attr.getType()) || RegistrationAttributeType.BOOTH_DETAILS.equals(attr.getType())) {
                            return 2;
                        } else {
                            return 3;
                        }
                    })
                    .thenComparing(RegistrationAttribute::getPosition));
        } else {
            registrationAttributes.sort(Comparator.comparingInt(RegistrationAttribute::getPosition));
        }
        String key = getKey(confirmationApprovalType);
        for (RegistrationAttribute registrationAttribute : registrationAttributes) {
            if(registrationAttribute.isEnabled() && !AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType())) {
                NameValueDto attributeKeyValueDto=new NameValueDto();
                attributeKeyValueDto.setName(registrationAttribute.getName());
                attributeKeyValueDto.setValue(DOLLAR.concat(OPEN_CURLY_BRACE).concat(key).concat(STRING_UNDERSCORE).concat(Long.toString(registrationAttribute.getId())).concat(CLOSE_CURLY_BRACE));
                mergeTags.add(attributeKeyValueDto);
            }
        }
        return mergeTags;
    }

    public String getKey(ConfirmationApprovalType confirmationApprovalType) {
        if (confirmationApprovalType.equals(ConfirmationApprovalType.SPEAKER) || confirmationApprovalType.equals(ConfirmationApprovalType.SPEAKER_INFO_AND_DETAILS)) {
            return SESSION.toLowerCase(Locale.ROOT);
        } else if (confirmationApprovalType.equals(ConfirmationApprovalType.REVIEWER_INFO_AND_DETAILS)) {
            return REVIEWER.toLowerCase(Locale.ROOT);
        } else {
            return EXHIBITOR.toLowerCase(Locale.ROOT);
        }
    }
    private List<RegistrationAttributeType> getRegistrationAttributeTypeByConfirmationApprovalType(ConfirmationApprovalType confirmationApprovalType) {
        List<RegistrationAttributeType> registrationAttributeTypes=new ArrayList<>();
        switch (confirmationApprovalType) {
            case EXPO:
                registrationAttributeTypes.add(RegistrationAttributeType.EXPO_INFO);
                break;
            case SPEAKER:
            case SPEAKER_INFO_AND_DETAILS:
                registrationAttributeTypes.add(RegistrationAttributeType.SPEAKER_DETAILS);
                break;
            case EXPO_INFO_AND_DETAILS:
                registrationAttributeTypes.addAll(Arrays.asList(RegistrationAttributeType.EXPO_INFO, RegistrationAttributeType.BOOTH_DETAILS));
                break;
            case REVIEWER_INFO_AND_DETAILS:
                registrationAttributeTypes.add(RegistrationAttributeType.REVIEWER_INFO);
                break;
            default:
                log.error("getRegistrationAttributeTypeByConfirmationApprovalType | invalid confirmationApprovalType : {} ", confirmationApprovalType);
                return Collections.emptyList();
        }
        return registrationAttributeTypes;
    }

    @Override
    public SessionApprovalSettingDto getSessionFormSettings(Event event) {
        Optional<SessionApprovalSetting> sessionApprovalSettingByEventId = roSessionApprovalSettingService.getSessionApprovalSettingByEventId(event);
        if(sessionApprovalSettingByEventId.isPresent()){
            SessionApprovalSetting sessionApprovalSetting = sessionApprovalSettingByEventId.get();
            return new SessionApprovalSettingDto(
                    sessionApprovalSetting.isAllowToAddAdditionalSpeaker(),
                    sessionApprovalSetting.getMaxNumberOfSpeakerPerApplication()
            );
        }
        else{
            SessionApprovalSetting sessionApprovalSetting = new SessionApprovalSetting();
            sessionApprovalSetting.setAllowToAddAdditionalSpeaker(true);
            sessionApprovalSetting.setEventId(event.getEventId());
            sessionApprovalSettingService.save(sessionApprovalSetting);
            return new SessionApprovalSettingDto(true, null);
        }
    }

    @Override
    public void updateEventSpeakerSettings(Event event, User user, SessionApprovalSettingDto sessionApprovalSettingDto) {
        log.info("updateEventSpeakerSettings | request to update updateEventSpeakerSettings for event : {} by user : {}",event.getEventId(), user.getUserId());
        SessionApprovalSetting sessionApprovalSetting = getOrCreateDefaultEventSperakerSettings(event);
        sessionApprovalSetting.setAllowToAddAdditionalSpeaker(sessionApprovalSettingDto.isAllowToAddAdditionalSpeaker());
        sessionApprovalSetting.setMaxNumberOfSpeakerPerApplication(sessionApprovalSettingDto.getMaxNumberOfSpeakerPerApplication());
        sessionApprovalSettingService.save(sessionApprovalSetting);
        auditLogger.info("updateEventSpeakerSettings | updated successfully updated for event : {} updated sessionApprovalSettingDto : {} by user : {}",event.getEventId(), sessionApprovalSettingDto, user.getUserId());
        log.info("updateEventSpeakerSettings | updated successfully updated for event : {} by user : {}",event.getEventId(), user.getUserId());
    }

    private SessionApprovalSetting getOrCreateDefaultEventSperakerSettings(Event event){
        Optional<SessionApprovalSetting> eventSpeakerSettingByEventId = roSessionApprovalSettingService.getSessionApprovalSettingByEventId(event);
        SessionApprovalSetting sessionApprovalSetting;
        if(eventSpeakerSettingByEventId.isPresent()){
            sessionApprovalSetting = eventSpeakerSettingByEventId.get();
        }else{
            sessionApprovalSetting = new SessionApprovalSetting();
            sessionApprovalSetting.setAllowToAddAdditionalSpeaker(true);
            sessionApprovalSetting.setEventId(event.getEventId());
        }
        return sessionApprovalSetting;
    }

    public String getSpeakerAttributeValue(RegistrationSpeakerResponseDto registrationSpeakerResponseDto, String attributeName,
                                    boolean isAttribute, boolean isConditionalQuestion, RegistrationAttributeType attributeType) {
        if (registrationSpeakerResponseDto != null) {
            if (isAttribute) {
                if (isConditionalQuestion) {
                    List<NestedQuestionsDto> nestedQuestions = registrationSpeakerResponseDto.getNestedQuestions();
                    if (!CollectionUtils.isEmpty(nestedQuestions)) {
                        for (NestedQuestionsDto questionsDto : nestedQuestions) {
                            boolean isEnterValue = questionsDto.getName().equals(attributeName);
                            if (isEnterValue) {
                                return attributeValueCheck(questionsDto.getValue());
                            }
                        }
                    }
                } else {
                    Map<String, String> attributes = registrationSpeakerResponseDto.getAttributes();
                    if (!CollectionUtils.isEmpty(attributes)) {
                        for (Map.Entry<String, String> attribute : attributes.entrySet()) {
                            if (attributeName.equalsIgnoreCase(attribute.getKey())) {
                                return attributeValueCheck(attribute.getValue());
                            }
                        }
                    }
                }
            } else {
                Map<String, String> questions = registrationSpeakerResponseDto.getQuestions();
                if (!CollectionUtils.isEmpty(questions)) {
                    for (Map.Entry<String, String> question : questions.entrySet()) {
                        if (attributeName.equalsIgnoreCase(question.getKey())) {
                            return attributeValueCheck(question.getValue());
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    @Transactional
    public void deleteSpeakerFromRegistration(Long requestId, String email, Event event, User user) {
        RegistrationRequest registrationRequest = getRegistrationRequestsOrThrowError(event, requestId);
        TicketHolderAttributes ticketHolderAttributes = registrationRequest.getTicketHolderAttributes();
        RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
        if(registrationResponseDto != null){
            List<RegistrationSpeakerResponseDto> speakers = registrationResponseDto.getSpeakerDetail();
            if(speakers != null){
                if(speakers.size() ==1 ){
                    throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.REGISTRATION_APPROVAL_WORKFLOW_UNABLE_TO_DELETE_SPEAKER);
                }
                boolean speakerFound = speakers.removeIf(speaker -> speaker.getAttributes() != null && speaker.getAttributes().get(EMAIL_ADDRESS) != null && speaker.getAttributes().get(EMAIL_ADDRESS).equalsIgnoreCase(email));
                if(!speakerFound){
                    throw new NotAcceptableException(NotAcceptableException.RegistrationRequestExceptionMsg.REGISTRATION_APPROVAL_WORKFLOW_SPEAKER_NOT_FOUND);
                }
                boolean primarySpeakerExist = speakers.stream().anyMatch(RegistrationSpeakerResponseDto::isPrimary);
                if(!primarySpeakerExist){
                    speakers.get(0).setPrimary(true);
                }

                handleJSONValue(ticketHolderAttributes, registrationResponseDto);
            }
        }
    }

    @Override
    public List<SessionRegistrationRequestBasicDetailDto> getSessionRegistrationRequestBasicDetails(Event event) {
        return Optional.ofNullable(
                registrationRequestRepository.getSessionRegistrationRequestBasicDetailDtoByEventId(
                        event.getEventId(), RegistrationRequestType.SPEAKER
                )
        ).orElseGet(Collections::emptyList);
    }

    @Override
    public Map<Long, RegistrationDisplayAttributesDto> getSessionRegistrationRequestDetailMap(Event event, List<RegistrationRequest> registrationRequestList) {

        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME, STRING_EMAIL_SPACE);


        List<RegistrationAttribute> requiredAttributes = registrationAttributeService.getRegistrationRequiredByEventAndAttributeOrder(event);

        Map<Long, List<RegistrationAttribute>> registrationAttributeMap = requiredAttributes.stream()
                .collect(Collectors.groupingBy(attr ->
                        attr.getRecurringEventId() != null ? attr.getRecurringEventId() : 0L));

        registrationAttributeMap.replaceAll((recurringEventId, attributes) ->
                isAddDefaultAttributes(attributes) ? registrationAttributeService.addDefaultAttributes(event, recurringEventId, RegistrationRequestType.SPEAKER) : attributes);

        Map<Long, RegistrationDisplayAttributesDto> registrationDisplayAttributesDtoMap = new HashMap<>();

        for (RegistrationRequest registrationRequest : registrationRequestList) {
            RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(registrationRequest.getTicketHolderAttributes());
            List<RegistrationAttribute> registrationAttributes;
            if(NumberUtils.isNumberGreaterThanZero(registrationRequest.getRecurringEventId())){
                registrationAttributes = registrationAttributeMap.get(registrationRequest.getRecurringEventId());
            }else{
                registrationAttributes = registrationAttributeMap.get(0L);
            }
            List<DisplayAttributeDto> attributeDtos = new ArrayList<>();
            List<DisplayAttributeDto> questionDto = new ArrayList<>();
            List<DisplayNestedQueDto> displayNestedQueDtoList = new ArrayList<>();
            List<DisplayAttributeDto> sessionAttributeDtos = new ArrayList<>();

            List<SpeakerRequestDisplayAttributesDto> speakerList = processAttributes(registrationAttributes, defaultAttributes, registrationResponseDto,
                    registrationRequest, attributeDtos, questionDto, displayNestedQueDtoList, sessionAttributeDtos
            );

            RegistrationDisplayAttributesDto displayAttributesDto = setSessionRegistrationRequestDetails(
                    attributeDtos, questionDto, displayNestedQueDtoList, sessionAttributeDtos, speakerList
            );

            registrationDisplayAttributesDtoMap.put(registrationRequest.getId(), displayAttributesDto);
        }
        return registrationDisplayAttributesDtoMap;
    }

    private List<SpeakerRequestDisplayAttributesDto> processAttributes(
            List<RegistrationAttribute> requiredAttributes, List<String> defaultAttributes,
            RegistrationResponseDto registrationResponseDto, RegistrationRequest registrationRequest,
            List<DisplayAttributeDto> attributeDtos, List<DisplayAttributeDto> questionDto,
            List<DisplayNestedQueDto> displayNestedQueDtoList, List<DisplayAttributeDto> sessionAttributeDtos) {

        List<SpeakerRequestDisplayAttributesDto> speakerList = new ArrayList<>();

        for (RegistrationAttribute registrationAttribute : requiredAttributes) {
            if (shouldIncludeAttribute(registrationAttribute, defaultAttributes)) {
                DisplayAttributeDto displayAttributeDto = createDisplayAttributeDto(registrationAttribute, registrationResponseDto, registrationRequest);
                if (registrationAttribute.isAttribute()) {
                    if (RegistrationAttributeType.SPEAKER_DETAILS.equals(registrationAttribute.getType())) {
                        setHolderAttributesData(sessionAttributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                    } else {
                        setHolderAttributesData(attributeDtos, displayNestedQueDtoList, registrationAttribute, displayAttributeDto);
                    }
                } else {
                    questionDto.add(displayAttributeDto);
                }
            }
        }

        if (!CollectionUtils.isEmpty(registrationResponseDto.getSpeakerDetail())) {
            speakerList = getSpeakerRegistrantAttributeDto(registrationRequest, requiredAttributes, registrationResponseDto.getSpeakerDetail());
        }

        return speakerList;
    }

    private boolean shouldIncludeAttribute(RegistrationAttribute attribute, List<String> defaultAttributes) {
        String attributeName = attribute.getName().equals(EMAIL_ADDRESS) ? attribute.getName().toUpperCase() : attribute.getName();
        return defaultAttributes.contains(attributeName) || attribute.isEnabled();
    }

    private DisplayAttributeDto createDisplayAttributeDto(RegistrationAttribute registrationAttribute,
                                                          RegistrationResponseDto registrationResponseDto,
                                                          RegistrationRequest registrationRequest) {
        DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
        displayAttributeDto.setName(registrationAttribute.getName());
        displayAttributeDto.setType(registrationAttribute.getValueType().getType());
        displayAttributeDto.setDefaultValue(registrationAttribute.getDefaultValueJson());
        displayAttributeDto.setMandatory(registrationAttribute.isRequired());
        displayAttributeDto.setValue(getAttributeValue(registrationResponseDto,
                displayAttributeDto.getName(), registrationAttribute.isAttribute(),
                AttributeValueType.CONDITIONAL_QUE.equals(registrationAttribute.getValueType()),
                registrationAttribute.getType()));
        displayAttributeDto.setPosition(registrationAttribute.getPosition());

        if (Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
            setPhoneNumber(registrationRequest, registrationResponseDto, registrationAttribute, displayAttributeDto);
        }
        return displayAttributeDto;
    }
    private RegistrationDisplayAttributesDto setSessionRegistrationRequestDetails(
            List<DisplayAttributeDto> attributeDtos, List<DisplayAttributeDto> questionDto,
            List<DisplayNestedQueDto> displayNestedQueDtoList, List<DisplayAttributeDto> sessionAttributeDtos,
            List<SpeakerRequestDisplayAttributesDto> speakerList) {

        RegistrationDisplayAttributesDto displayAttributesDto = new RegistrationDisplayAttributesDto();
        displayAttributesDto.setAttributes(attributeDtos);
        displayAttributesDto.setQuestions(questionDto);
        displayAttributesDto.setNestedQuestions(displayNestedQueDtoList);
        displayAttributesDto.setSessionDetail(sessionAttributeDtos);
        displayAttributesDto.setSpeakerDetails(speakerList);
        return displayAttributesDto;
    }

    @Override
    public List<UserApplicationDto> getRegistrationApplicationByUser(User user) {
        log.info("Fetching registration applications for user id: {}", user.getUserId());
        List<RegistrationRequest> registrationRequestList = registrationRequestRepository.findByRequestByRequestedUserId(user, RegistrationRequestType.SPEAKER);

        if (registrationRequestList.isEmpty()) {
           return new ArrayList<>();
        }
        log.info("total registration requests found for user: {}", registrationRequestList.size());
        /*
          TODO: Currently adding null check for the event, as sometimes the event has been deleted but the request still exists.
           Need to delete such requests as well when deleting the event.
        */
        Map<Event, List<RegistrationRequest>> eventGroupedMap = registrationRequestList.stream().filter(req -> req.getEvent() != null).collect(Collectors.groupingBy(RegistrationRequest::getEvent));
        Set<Event> eventSet = eventGroupedMap.keySet();
        List<EventDesignDetail> eventDesignDetails = eventDesignDetailService.findByEventList(new ArrayList<>(eventSet));
        Map<Long, EventDesignDetail> designDetailMap = eventDesignDetails.stream().collect(Collectors.toMap(eventDesignDetail -> eventDesignDetail.getEvent().getEventId(), Function.identity()));
        Map<Event, Ticketing> ticketingMap = eventSet.stream().collect(Collectors.toMap(Function.identity(),
                event -> ticketingHelperService.findTicketingByEvent(event)));

        List<UserApplicationDto> userApplicationDtos = new ArrayList<>();
        for (Map.Entry<Event, List<RegistrationRequest>> entry : eventGroupedMap.entrySet()) {
            Event event = entry.getKey();
            List<RegistrationRequest> requests = entry.getValue();
            UserApplicationDto dto = new UserApplicationDto();
            EventDesignDetail eventDesignDetail = designDetailMap.get(event.getEventId());
            Ticketing ticketing = ticketingMap.get(event);
            dto.setEventName(event.getName());
            dto.setEventUrl(event.getEventURL());
            dto.setEventLogoImage(eventDesignDetail != null ? eventDesignDetail.getLogoImage() : null);
            if (ticketing != null) {
                dto.setEventStartDate(ticketing.getEventStartDate());
                dto.setEventEndDate(ticketing.getEventEndDate());
                dto.setEventLocation(ticketing.getEventAddress());
            }
            dto.setEventFormat(event.getEventFormat());
            dto.setEquivalentTimezone(event.getEquivalentTimeZone());

            List<RegistrationRequestsDetailsDto> requestDtos = requests.stream().map(request -> {
                RegistrationRequestsDetailsDto detailDto = new RegistrationRequestsDetailsDto();
                detailDto.setId(request.getId());
                detailDto.setFirstName(request.getRequestedByUser().getFirstName());
                detailDto.setLastName(request.getRequestedByUser().getLastName());
                detailDto.setEmail(request.getRequestedByUser().getEmail());
                detailDto.setStatus(request.getStatus());
                detailDto.setAddedDate(request.getCreatedAt());
                RegistrationResponseDto registrationResponseDto = getRegistrationRequestDto(request.getTicketHolderAttributes());
                String sessionName = getAttributeValue(registrationResponseDto, SPEAKER_APPROVAL_SESSION_TOPIC, true, false, RegistrationAttributeType.SPEAKER_DETAILS);
                detailDto.setSessionName(sessionName);
                String sessionDescription = getAttributeValue(registrationResponseDto, SPEAKER_APPROVAL_SESSION_DESCRIPTION, true, false, RegistrationAttributeType.SPEAKER_DETAILS);
                detailDto.setSessionDescription(sessionDescription);
                detailDto.setPrimarySpeaker(getPrimarySpeaker(registrationResponseDto));
                return detailDto;
            }).collect(Collectors.toList());
            dto.setRegistrationRequestsDetailsDtoList(requestDtos);
            log.info("event id: {}, number of registration requests: {}", event.getEventId(), requestDtos.size());
            userApplicationDtos.add(dto);
        }
        log.info("total user application entries grouped by event: {}", userApplicationDtos.size());
        return userApplicationDtos;
    }

    @Override
    public void downloadAllRegistrationRequests(Event event,User user, PrintWriter writer,AnalyticsArea area) {
        log.info("Starting download of registration requests for eventId={}, userId={}, area={}", event.getEventId(), user.getUserId(), area);

        RegistrationRequestType requestType =  getRequestTypeFromArea(area);

        List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByEventIdAndType(event.getEventId(),requestType);
        List<String> headerList = new ArrayList<>();
        List<List<String>> registrantData = new ArrayList<>();

        if (RegistrationRequestType.ATTENDEE.equals(requestType)) {
            downloadAttendeeRegistrationsCSV(event, user, writer, area, requestType);
        } else {
            if(requestType.equals(RegistrationRequestType.SPEAKER)) {
                registrationRequests = registrationRequests.stream()
                        .filter(request -> !RegistrationRequestStatus.SAVE_AS_DRAFT.equals(request.getStatus()))
                        .collect(Collectors.toList());
            }

            log.info("registration requests size {}  for type {}", registrationRequests.size(), requestType);

            UserColumnSelectionDto userColumnSelectionDto = userColumnSelectionService.getUserColumnSelection(AnalyticsSource.EVENT, event.getEventURL(), area, user.getUserId(), -1L);

            List<RegistrationAttribute> sortedRegistrationRequiredAttributes =  registrationAttributeService.getRegistrationRequiredAttributesOrderByAttributeOrder(event, 0L, requestType)
                    .stream().filter(holderRequiredAttribute -> !holderRequiredAttribute.getType().equals(SPEAKER_INFO)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(sortedRegistrationRequiredAttributes)) {
                sortedRegistrationRequiredAttributes = registrationAttributeService.addDefaultAttributes(event, -1L, requestType);
            }


            // set csv headers
            List<RegistrationAttribute> listOfAttributesHavingOnlyEnable = Collections.emptyList();
            if (!sortedRegistrationRequiredAttributes.isEmpty()) {
                listOfAttributesHavingOnlyEnable = addHeadersAndGetEnabledAttributes(headerList, sortedRegistrationRequiredAttributes, userColumnSelectionDto, requestType);
            }

            if(!CollectionUtils.isEmpty(registrationRequests)){
                Map<Long, RegistrationDisplayAttributesDto> registrantDetailsForCSV = getRegistrantDetailsForCSV(registrationRequests, event, requestType);
                for(RegistrationRequest registrationRequest:registrationRequests){
                    if (registrantDetailsForCSV !=null && registrantDetailsForCSV.get(registrationRequest.getId()) !=null) {
                        RegistrationDisplayAttributesDto registrationDisplayAttributesDto =  registrantDetailsForCSV.get(registrationRequest.getId());
                        List<String> data = new ArrayList<>();
                        listOfAttributesHavingOnlyEnable.removeIf(attribute -> isNumberGreaterThanZero(attribute.getParentQuestionId()));
                        for (RegistrationAttribute attribute : listOfAttributesHavingOnlyEnable) {
                            prepareDataList(data, registrationDisplayAttributesDto, attribute);
                        }
                        setDefaultRegistrationAttributeData(data,registrationRequest,registrationDisplayAttributesDto,null);
                        registrantData.add(data);
                    }
                }
            }
        }

        log.info("Finalizing CSV download with {} data rows and {} headers", registrantData.size(), headerList.size());
        downloadService.downloadCSVFile(writer, headerList, registrantData);
        log.info("CSV download completed for eventId={}", event.getEventId());

    }

    private void downloadAttendeeRegistrationsCSV(Event event, User user, PrintWriter writer, AnalyticsArea area, RegistrationRequestType requestType) {
        List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByEventIdAndType(event.getEventId(), requestType);
        List<String> headerList = new ArrayList<>();
        List<List<String>> registrantData = new ArrayList<>();

        UserColumnSelectionDto userColumnSelectionDto = userColumnSelectionService.getUserColumnSelection(AnalyticsSource.EVENT, event.getEventURL(), area, user.getUserId(), -1L);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesHavingEnableForBuyerOrderByAttributeOrder(event, 0L, DataType.TICKET);

        List<TicketHolderRequiredAttributes> enabledAttributes = addAttendeeHeaders(headerList, userColumnSelectionDto, event, null);
        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME, EMAIL);

        List<TicketHolderRequiredAttributes> filteredAttributes = ticketHolderRequiredAttributes.stream()
                .filter(attr -> defaultAttributes.contains(attr.getName()) ||
                        enabledAttributes.stream().anyMatch(enabled -> attr.getId() == enabled.getId())).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(registrationRequests)) {
            Map<Long, RegistrationDisplayAttributesDto> registrantDetailsForCSV = getRegistrantDetailsForCSV(registrationRequests, event, requestType);

            // Separate default and additional columns
            List<TicketHolderRequiredAttributes> defaultHeaderAttributes = new ArrayList<>();
            List<TicketHolderRequiredAttributes> customOrAdditionalAttributes = new ArrayList<>();

            for (TicketHolderRequiredAttributes attr : filteredAttributes) {
                if (defaultAttributes.contains(attr.getName())) {
                    defaultHeaderAttributes.add(attr);
                } else {
                    customOrAdditionalAttributes.add(attr);
                }
            }
            if(!CollectionUtils.isEmpty(enabledAttributes)) {
                enabledAttributes.removeIf(attr -> isNumberGreaterThanZero(attr.getParentQuestionId()));
            }
            for (RegistrationRequest registrationRequest : registrationRequests) {
                RegistrationDisplayAttributesDto dto = registrantDetailsForCSV.get(registrationRequest.getId());
                if (dto != null) {
                    Map<Long, TicketDetailsDto> requestedTicketDetails = dto.getTicketTypes();
                    if (requestedTicketDetails != null && !requestedTicketDetails.isEmpty()) {
                        for (TicketDetailsDto ticket : requestedTicketDetails.values()) {
                            List<String> data = new ArrayList<>();
                            for (TicketHolderRequiredAttributes attribute : defaultHeaderAttributes) {
                                prepareAttendeeDataList(data, dto, attribute);
                            }
                            setDefaultRegistrationAttributeData(data, registrationRequest, dto, ticket);
                            for (TicketHolderRequiredAttributes attribute : customOrAdditionalAttributes) {
                                prepareAttendeeDataList(data, dto, attribute);
                            }
                            registrantData.add(data);
                        }
                    }
                }
            }
        }
        log.info("Finalizing CSV download: {} data rows, {} headers", registrantData.size(), headerList.size());
        downloadService.downloadCSVFile(writer, headerList, registrantData);
    }


    public void prepareAttendeeDataList(List<String> ticketHolderData,
                                        RegistrationDisplayAttributesDto dto,
                                        TicketHolderRequiredAttributes attribute) {

        String attributeName = attribute.getName();
        AttributeValueType attrType = attribute.getAttributeValueType();

        // Handle birthday formatting
        if (BIRTHDAY.equals(attributeName)) {
            String birthday = getAttributeValueForAttendee(dto, attribute);
            if (birthday != null) {
                try {
                    LocalDate date = LocalDate.parse(birthday, DateTimeFormatter.ofPattern("MM/dd/yyyy"));
                    ticketHolderData.add(date.toString());
                } catch (DateTimeParseException e) {
                    ticketHolderData.add(STRING_EMPTY);
                }
            } else {
                ticketHolderData.add(STRING_EMPTY);
            }
            return;
        }

        // Handle date attributes
        if (AttributeValueType.DATE.equals(attrType)) {
            ticketHolderData.add(StringUtils.defaultString(getAttributeValueForAttendee(dto, attribute)));
            return;
        }

        // Handle phone number (masked)
        if (STRING_CELL_SPACE_PHONE.equals(attributeName)) {
            for (DisplayAttributeDto attrDto : dto.getAttributes()) {
                if (STRING_CELL_SPACE_PHONE.equals(attrDto.getName())) {
                    String cellPhone = attributeValueCheck(attrDto.getValue());
                    if (StringUtils.isNotEmpty(cellPhone) && cellPhone.length() > 4) {
                        cellPhone = cellPhone.substring(0, cellPhone.length() - 3);
                    } else {
                        cellPhone = STRING_EMPTY;
                    }
                    ticketHolderData.add(cellPhone);
                    return;
                }
            }
        }


        if (attrType != null && (AttributeValueType.IMAGE.getType().equalsIgnoreCase(attrType.getType()) || AttributeValueType.UPLOAD.getType().equalsIgnoreCase(attrType.getType()))) {
            String url = getAttributeValueForAttendee(dto, attribute);
            ticketHolderData.add(url);
            return;
        }

        if (AttributeValueType.SINGLE_CHECKBOX.getType().equalsIgnoreCase(attrType.getType())) {
            String value = getAttributeValueForAttendee(dto, attribute);
            if (value == null) {
                String defaultJson = attribute.getDefaultValueJsonPurchaser();
                if (defaultJson != null) {
                    try {
                        JSONObject json = new JSONObject(defaultJson);
                        value = json.optString("defaultValue", STRING_EMPTY);
                    } catch (JSONException e) {
                        log.info("Checkbox default parse failed for attributeId={}, error={}", attribute.getId(), e.getMessage());
                    }
                }
            }
            ticketHolderData.add(TRUE.equalsIgnoreCase(value) ? CHECKED : UNCHECKED);
            return;
        }
        if (dto != null) {
            List<DisplayNestedQueDto> nestedQuestions = dto.getNestedQuestions();
            if (!CollectionUtils.isEmpty(nestedQuestions)) {
                if (AttributeValueType.CONDITIONAL_QUE.equals(attrType)
                        && (attribute.getParentQuestionId() == null || attribute.getParentQuestionId() == 0)) {
                    // Parent conditional questions
                    for (DisplayNestedQueDto nested : nestedQuestions) {
                        if (nested.getId().equals(attribute.getId())) {
                            ticketHolderData.add(attributeValueCheck(nested.getValue()));
                            return;
                        }
                    }
                } else if (attribute.getParentQuestionId() != null) {
                    // Child questions
                    for (DisplayNestedQueDto nested : nestedQuestions) {
                        if (nested.getId().equals(attribute.getId())
                                && nested.getParentQueId() != null
                                && nested.getParentQueId().equals(attribute.getParentQuestionId())) {
                            ticketHolderData.add(attributeValueCheck(nested.getValue()));
                            return;
                        }
                    }
                }
            }
        }
        ticketHolderData.add(StringUtils.defaultString(getAttributeValueForAttendee(dto, attribute)));
    }

    public static String getAttributeValueForAttendee(RegistrationDisplayAttributesDto registrationDisplayAttributesDto, TicketHolderRequiredAttributes attribute) {
        if (registrationDisplayAttributesDto != null) {
            if (attribute.isAttribute()) {
                List<DisplayAttributeDto> holderAttribute = registrationDisplayAttributesDto.getAttributes();
                if (holderAttribute != null && !CollectionUtils.isEmpty(holderAttribute)) {
                    for (DisplayAttributeDto attributeDto : holderAttribute) {
                        if (attribute.getName().equalsIgnoreCase(attributeDto.getName()) || (attribute.getAttributeValueType().equals(RegistrationAttributeType.ATTENDEE) && (EMAIL_ADDRESS.equalsIgnoreCase(attributeDto.getName()) || EMAIL.equalsIgnoreCase(attributeDto.getName())))) {
                            return attributeValueCheck(attributeDto.getValue());
                        }
                    }
                }
            } else {
                List<DisplayAttributeDto> holderQue = registrationDisplayAttributesDto.getQuestions();
                if (holderQue != null && !CollectionUtils.isEmpty(holderQue)) {
                    for (DisplayAttributeDto entry : holderQue) {
                        if (attribute.getName().equalsIgnoreCase(entry.getName())) {
                            return attributeValueCheck(entry.getValue());
                        }
                    }
                }
            }
        }
        return null;
    }


    private void setDefaultRegistrationAttributeData(List<String> data, RegistrationRequest registrationRequest,RegistrationDisplayAttributesDto registrationDisplayAttributesDto,TicketDetailsDto ticket){

        log.debug("Starting default attribute data for request ID: {}, type: {}", registrationRequest.getId(), registrationRequest.getType());
        if(RegistrationRequestType.ATTENDEE.equals(registrationRequest.getType())){
            String createdDate = String.valueOf(registrationRequest.getCreatedAt());
            data.add(GeneralUtils.getDateAndTimeFormatString(createdDate));
            data.add(registrationRequest.getStatus().name());
            // Add the specific ticket for this row
            data.add(String.valueOf(ticket.getTicketName()));
            data.add(String.valueOf(ticket.getTicketCount()));
            data.add(registrationRequest.getNotes());
            if (null != registrationRequest.getTrackingLinks()){
                data.add(registrationRequest.getTrackingLinks().getLinkUrl());
            }
        }
        else{
            String createdDate = String.valueOf(registrationRequest.getCreatedAt());
            data.add(GeneralUtils.getDateAndTimeFormatString(createdDate));
            data.add(registrationRequest.getStatus().name());
            if(RegistrationRequestType.SPEAKER.equals(registrationRequest.getType())){
                registrationDisplayAttributesDto.getSessionDetail().forEach(attribute -> {
                    if(SPEAKER_APPROVAL_SESSION_TOPIC.equals(attribute.getName()) || SPEAKER_APPROVAL_SESSION_DESCRIPTION.equals(attribute.getName())){
                        data.add(attribute.getValue());
                    }
                });
                registrationDisplayAttributesDto.getSpeakerDetails().stream().filter(SpeakerRequestDisplayAttributesDto::isPrimarySpeaker).forEach(attribute -> {
                    String speakerName = String.join(" ",
                            attribute.getAttributes().stream().filter(e -> e.getName().equalsIgnoreCase(FIRST_NAME)).findFirst().map(DisplayAttributeDto::getValue).orElse(""),
                            attribute.getAttributes().stream().filter(e -> e.getName().equalsIgnoreCase(LAST_NAME)).findFirst().map(DisplayAttributeDto::getValue).orElse("")
                    );
                    data.add(speakerName);
                });
            }
            if (null != registrationRequest.getTrackingLinks()){
                data.add(registrationRequest.getTrackingLinks().getLinkUrl());
            }
        }
        log.debug("Completed default attribute data for request ID: {}", registrationRequest.getId());
    }

    private RegistrationRequestType getRequestTypeFromArea(AnalyticsArea area) {
        RegistrationRequestType requestType;
        switch (area) {
            case REGISTRATION_APPROVAL_SPEAKER:
                requestType = RegistrationRequestType.SPEAKER;
                break;
            case REGISTRATION_APPROVAL_EXPO:
                requestType = RegistrationRequestType.EXPO;
                break;
            case REGISTRATION_APPROVAL_REVIEWER:
                requestType = RegistrationRequestType.REVIEWER;
                break;
            default:
                requestType = RegistrationRequestType.ATTENDEE;
        }
        return requestType;
    }

    public List<TicketHolderRequiredAttributes>  addAttendeeHeaders(List<String> headerList, UserColumnSelectionDto userColumnSelectionDto, Event event,List<TicketHolderRequiredAttributes> enabledHolderAttributes) {
        log.info("Starting to add required CSV headers for ATTENDEE");
        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME,STRING_LAST_SPACE_NAME,STRING_EMAIL_SPACE);
        List<TicketHolderRequiredAttributes> listOfEnabledAttributes = new ArrayList<>();
        // Add common ATTENDEE-specific headers
        headerList.addAll(defaultAttributes);
        headerList.add(CSV_SUBMITTED_DATE);
        headerList.add(CSV_STATUS);
        headerList.add(CSV_TICKET_TYPE);
        headerList.add(CSV_TICKET_QTY);
        headerList.add(CSV_NOTES);
        if (Objects.isNull(userColumnSelectionDto)) {
            log.warn("UserColumnSelectionDto is null, skipping attendee-specific headers");
            return new ArrayList<>();
        }

        List<String> defaultColumns = new ArrayList<>();
        List<String> customColumns = new ArrayList<>();
        List<Long> ticketAttributeIds = new ArrayList<>();
        List<Long> reviewFormQuestionIds = new ArrayList<>();

        // Get selected ticketAttributeIds from column selection
        roAnalyticsUserColumnSelectionService.setDefaultAndCustomiseColumns(defaultColumns, customColumns, ticketAttributeIds, reviewFormQuestionIds, userColumnSelectionDto.getColumnSelection());

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService.findByEventAndIds(event, ticketAttributeIds);

        if (!CollectionUtils.isEmpty(ticketHolderRequiredAttributes)) {
            Set<Long> validAttributeIds = userColumnSelectionDto.getColumnSelection().stream()
                    .filter(column -> column.getType() != ColumnSelectionConstants.ColumnType.DEFAULT)
                    .map(ColumnDto::getKey).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
            listOfEnabledAttributes = ticketHolderRequiredAttributes.stream().filter(TicketHolderRequiredAttributes::getEnabledForTicketPurchaser).collect(Collectors.toList());
            ticketHolderRequiredAttributes.stream().filter(attr -> validAttributeIds.contains(attr.getId())).forEach(attr -> headerList.add(attr.getName()));
        }
        if (enabledHolderAttributes != null && !enabledHolderAttributes.isEmpty()) {
            for (TicketHolderRequiredAttributes attr : enabledHolderAttributes) {
                if (StringUtils.isNotBlank(attr.getName())) {
                    headerList.add(HOLDER_HEADER.concat(STRING_BLANK).concat(attr.getName())); // Prefix added for clarity
                }
            }
        }
        headerList.add(TRACKING_LINK);
        log.info("Completed adding CSV headers for ATTENDEE. Total headers: {}", headerList.size());
        return listOfEnabledAttributes;
    }


    public List<RegistrationAttribute> addHeadersAndGetEnabledAttributes(List<String> headerList, List<RegistrationAttribute> registrationAttributes,UserColumnSelectionDto userColumnSelectionDto,RegistrationRequestType requestType) {
        log.info("Starting to add required CSV headers for request type: {}", requestType);

        List<RegistrationAttribute> listOfEnabledAttributes = null;
        if(!Objects.isNull(userColumnSelectionDto) && RegistrationRequestType.SPEAKER.equals(requestType)){
                List<ColumnDto> filteredColumns = filteredColumns = userColumnSelectionDto.getColumnSelection().stream()
                        .filter(columnSelection -> columnSelection.getType() != ColumnSelectionConstants.ColumnType.DEFAULT)
                        .collect(Collectors.toList());
                Set<Long> validAttributeIds = filteredColumns.stream()
                        .map(ColumnDto::getKey)
                        .filter(StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toSet());
                listOfEnabledAttributes = registrationAttributes.stream()
                        .filter(attr -> attr.isEnabled() && validAttributeIds.contains(attr.getId()))
                        .collect(Collectors.toList());

        }else{
            listOfEnabledAttributes = registrationAttributes.stream()
                    .filter(RegistrationAttribute::isEnabled)
                    .collect(Collectors.toList());
        }
        listOfEnabledAttributes.forEach(attribute -> headerList.add(attribute.getName()));
        headerList.add(CSV_SUBMITTED_DATE);
        headerList.add(CSV_STATUS);
        if(RegistrationRequestType.SPEAKER.equals(requestType)){
            headerList.add(CSV_SESSION_NAME);
            headerList.add(CSV_SESSION_DESCRIPTION);
            headerList.add(CSV_PRIMARY_SPEAKER_NAME);
            headerList.add(CSV_REVIEW_STATUS);
        }else if(RegistrationRequestType.ATTENDEE.equals(requestType)){
            headerList.add(CSV_TICKET_TYPE);
            headerList.add(CSV_TICKET_QTY);
            headerList.add(CSV_NOTES);
        }
        headerList.add(TRACKING_LINK);
        log.info("Completed adding CSV headers. Total headers: {}", headerList.size());
        return listOfEnabledAttributes;
    }

    @Override
    public Map<Long,Long> countByEventAndTrackingLinkIds(List<Long> trackingLinkIds, Long eventID) {
        if (CollectionUtils.isEmpty(trackingLinkIds)) {
            return Collections.emptyMap();
        }
        List<Object[]> countAndTrackingLinkIds = registrationRequestRepository.countByEventAndTrackingLinkIds(
                trackingLinkIds, eventID,
                List.of(RegistrationRequestStatus.PENDING,RegistrationRequestStatus.APPROVED,
                        RegistrationRequestStatus.WAITLISTED, RegistrationRequestStatus.DENIED));

        return countAndTrackingLinkIds.stream().collect(Collectors.toMap(
                        row -> (Long) row[0],
                        row -> (Long) row[1]));
    }

    @Override
    public void downloadAttendeeRequestHolderBuyerCSV(Event event, User user, PrintWriter writer) {
        List<RegistrationRequest> registrationRequests = registrationRequestRepository.findAllByEventIdAndType(event.getEventId(), RegistrationRequestType.ATTENDEE);
        List<List<String>> registrantData = new ArrayList<>();
        List<String> headerList = new ArrayList<>();

        if (CollectionUtils.isEmpty(registrationRequests)) {
            log.info("No registration requests found while downloading buyer and holder approval CSV for event: {}", event.getEventId());
            downloadService.downloadCSVFile(writer, headerList, registrantData);
            return;
        }

        UserColumnSelectionDto userColumnSelectionDto = userColumnSelectionService.getUserColumnSelection(AnalyticsSource.EVENT, event.getEventURL(), AnalyticsArea.REGISTRATION_APPROVAL_ATTENDEE, user.getUserId(), -1L);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes =  ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event, -1L, DataType.TICKET);
        List<TicketHolderRequiredAttributes> buyerAttributes = ticketHolderRequiredAttributes.stream().filter(TicketHolderRequiredAttributes::getEnabledForTicketPurchaser).collect(Collectors.toList());
        List<TicketHolderRequiredAttributes> holderAttributes = ticketHolderRequiredAttributes.stream().filter(TicketHolderRequiredAttributes::getEnabledForTicketHolder).collect(Collectors.toList());
        List<TicketHolderRequiredAttributes> enabledBuyerAttributes = addAttendeeHeaders(headerList, userColumnSelectionDto, event, holderAttributes);

        List<TicketHolderRequiredAttributes> filteredBuyerAttributes = filterBuyerAttributes(buyerAttributes, enabledBuyerAttributes);
        List<TicketHolderRequiredAttributes> defaultBuyerAttrs = getDefaultBuyerAttributes(filteredBuyerAttributes);
        List<TicketHolderRequiredAttributes> customBuyerAttrs = getCustomBuyerAttributes(filteredBuyerAttributes);

        Map<Long, RegistrationDisplayAttributesDto> registrantDetailsForCSV = getRegistrantDetailsForCSV(registrationRequests, event, RegistrationRequestType.ATTENDEE);
        List<Long> requestIds = registrationRequests.stream().map(RegistrationRequest::getId).collect(Collectors.toList());
        List<RegistrationRequestHolderDetails> allHolderDetails = registrationRequestHolderDetailsRepository.findByRegistrationRequestIdIn(requestIds);
        Map<Long, List<RegistrationRequestHolderDetails>> holderDetailsMap = allHolderDetails.stream().collect(Collectors.groupingBy(RegistrationRequestHolderDetails::getRegistrationRequestId));

        for (RegistrationRequest request : registrationRequests) {
            RegistrationDisplayAttributesDto dto = registrantDetailsForCSV.get(request.getId());
            if (dto == null) continue;

            List<RegistrationRequestHolderDetails> holderDetails = holderDetailsMap.getOrDefault(request.getId(), Collections.emptyList());

            if (CollectionUtils.isEmpty(holderDetails)) continue;
            Map<Long, TicketDetailsDto> requestedTicketDetails = dto.getTicketTypes();
            List<TicketDetailsDto> tickets = new ArrayList<>(requestedTicketDetails.values());
            for (int i = 0; i < holderDetails.size(); i++) {
                RegistrationRequestHolderDetails holderDetail = holderDetails.get(i);
                TicketDetailsDto ticket = setTicketTypeDetailsDto(holderDetail.getTicketingType());
                List<String> row = new ArrayList<>();
                // Add buyer default attributes
                addBuyerAttributes(row, dto, defaultBuyerAttrs);
                setDefaultRegistrationAttributeDataForHolderAndBuyerCSV(row, request, ticket, ticket.getTicketCount());
                addBuyerAttributes(row, dto, customBuyerAttrs);
                // Add holder attributes
                addHolderAttributes(row, holderDetail, holderAttributes);
                registrantData.add(row);
            }
        }

        log.info("Finalizing CSV download for approval requests holder and buyer: {} data rows, {} headers", registrantData.size(), headerList.size());
        downloadService.downloadCSVFile(writer, headerList, registrantData);
    }

    private TicketDetailsDto setTicketTypeDetailsDto(TicketingType ticketingType) {
        TicketDetailsDto ticket = new TicketDetailsDto();
        ticket.setTicketCount(1);
        ticket.setTicketName(ticketingType.getTicketTypeName());
        ticket.setTicketType(ticketingType.getTicketType());
        return ticket;
    }

    private void setDefaultRegistrationAttributeDataForHolderAndBuyerCSV(List<String> data, RegistrationRequest registrationRequest, TicketDetailsDto ticket, int ticketCount) {
        String createdDate = String.valueOf(registrationRequest.getCreatedAt());
        data.add(GeneralUtils.getDateAndTimeFormatString(createdDate));
        data.add(registrationRequest.getStatus().name());
        data.add(ticket.getTicketName());
        data.add(String.valueOf(ticketCount));
        data.add(registrationRequest.getNotes());
        if (registrationRequest.getTrackingLinks() != null) {
            data.add(registrationRequest.getTrackingLinks().getLinkUrl());
        }
    }



    private List<TicketHolderRequiredAttributes> filterBuyerAttributes(List<TicketHolderRequiredAttributes> allBuyerAttrs,List<TicketHolderRequiredAttributes> enabledAttrs) {
        return allBuyerAttrs.stream().filter(attr -> DEFAULT_BUYER_ATTR_NAMES.contains(attr.getName()) || enabledAttrs.stream().anyMatch(enabled -> enabled.getId() == attr.getId())).collect(Collectors.toList());
    }

    private List<TicketHolderRequiredAttributes> getDefaultBuyerAttributes(List<TicketHolderRequiredAttributes> attrs) {
        return attrs.stream().filter(attr -> DEFAULT_BUYER_ATTR_NAMES.contains(attr.getName())).collect(Collectors.toList());
    }

    private List<TicketHolderRequiredAttributes> getCustomBuyerAttributes(List<TicketHolderRequiredAttributes> attrs) {
        return attrs.stream().filter(attr -> !DEFAULT_BUYER_ATTR_NAMES.contains(attr.getName())).collect(Collectors.toList());
    }

    private void addBuyerAttributes(List<String> dataRow, RegistrationDisplayAttributesDto dto, List<TicketHolderRequiredAttributes> attributes) {
        for (TicketHolderRequiredAttributes attribute : attributes) {
            prepareAttendeeDataList(dataRow, dto, attribute);
        }
    }

    private static Long asParentQuestionIdLong(Object object) {
        if (object == null) return null;
        if (object instanceof Number) return ((Number) object).longValue();
        String s = object.toString().trim();
        if (s.endsWith(".0")) s = s.substring(0, s.length() - 2);
        try {
            return Long.parseLong(s);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private void addHolderAttributes(List<String> dataRow, RegistrationRequestHolderDetails holderDetail, List<TicketHolderRequiredAttributes> holderAttrs) {
        TicketAttributeValueDto1 attrDto = TicketHolderAttributesHelper.parseJsonToObject(holderDetail.getJsonValue());
        if (attrDto != null && attrDto.getHolder() != null) {
            Map<String, String> holderValues = (Map<String, String>) attrDto.getHolder().get(Constants.TICKETING.ATTRIBUTES);
            List<Map<String, String>> holderSubQueAttribute = (List<Map<String, String>>) attrDto.getHolder().get(Constants.TICKETING.NESTEDQUESTIONS);
            for (TicketHolderRequiredAttributes attr : holderAttrs) {
                String value;
                if (AttributeValueType.CONDITIONAL_QUE.equals(attr.getAttributeValueType())) {
                    boolean matched = false;
                    if (holderSubQueAttribute != null && !holderSubQueAttribute.isEmpty()) {
                        for (Map<String, String> mapHolder : holderSubQueAttribute) {
                            boolean isMatch = attr.getName().equals(mapHolder.get(NAME_SMALL));
                            if (isMatch) {
                                value = mapHolder.get(VALUE_SMALL);
                                dataRow.add(value != null ? value : STRING_EMPTY);
                                matched = true;
                                break;
                            }
                        }
                    }
                    if (!matched) {
                        dataRow.add(STRING_EMPTY);
                    }
                } else if (attr.getParentQuestionId() != null && holderSubQueAttribute != null) {
                    boolean matched = false;
                    Long wantedParent = attr.getParentQuestionId();
                    for (Map<String, String> map : holderSubQueAttribute) {
                        Long parentId = asParentQuestionIdLong(map.get(PARENT_QUESTION_ID));
                        if (Objects.equals(wantedParent, parentId) && attr.getName().equals(String.valueOf(map.get(NAME_SMALL)))) {
                            value = String.valueOf(map.get(VALUE_SMALL));
                            dataRow.add(value != null ? value : STRING_EMPTY);
                            matched = true;
                            break;
                        }
                    }
                    if (!matched) {
                        dataRow.add(STRING_EMPTY);
                    }
                }
                else if (AttributeValueType.SINGLE_CHECKBOX.equals(attr.getAttributeValueType())) {
                    value = holderValues != null ? holderValues.get(attr.getName()) : STRING_EMPTY;
                    dataRow.add(TRUE.equalsIgnoreCase(value) ? CHECKED : UNCHECKED);
                    break;
                } else {
                    value = holderValues != null ? holderValues.get(attr.getName()) : STRING_EMPTY;
                    dataRow.add(value != null ? value : STRING_EMPTY);
                }
            }
        }
    }

    @Override
    public void removeDiscountCodeFromRegistrationRequest(Long requestId, Event event, Long userId) {
        log.info("removeDiscountCodeFromRegistrationRequest | eventId {}  requestId {} userId {}", event.getEventId(), userId, requestId);
        Optional<RegistrationRequest> optionalRegistrationRequest = registrationRequestRepository.findByEventAndId(event,requestId);
        if(optionalRegistrationRequest.isPresent()){
            RegistrationRequest registrationRequest = optionalRegistrationRequest.get();
            registrationRequest.setTicketingCouponId(null);
            registrationRequestRepository.save(registrationRequest);
            log.info("removeDiscountCodeFromRegistrationRequest | eventId {}  requestId {} userId {} couponCode removed successfully", event.getEventId(), requestId, userId);
        }
    }


}

