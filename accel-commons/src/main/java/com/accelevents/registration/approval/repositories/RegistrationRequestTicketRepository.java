package com.accelevents.registration.approval.repositories;

import com.accelevents.common.dto.RegistrationRequestTicketTypeDTO;
import com.accelevents.domain.Event;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.RegistrationRequestStatus;
import com.accelevents.registration.approval.domain.RegistrationRequest;
import com.accelevents.registration.approval.domain.RegistrationRequestTicket;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface RegistrationRequestTicketRepository extends CrudRepository<RegistrationRequestTicket, Long> {

    @Query("SELECT rt.ticketType.id FROM RegistrationRequestTicket AS rt WHERE rt.registrationRequest.id in (:requestIds)" )
    List<Long> findTicketingTypesByRegistrationRequestIds(@Param("requestIds") List<Long> requestIds);

    @Query("SELECT new com.accelevents.common.dto.RegistrationRequestTicketTypeDTO( rt.ticketType.id, rt.registrationRequest.id) FROM RegistrationRequestTicket AS rt WHERE rt.registrationRequest.id in (:requestIds)" )
    List<RegistrationRequestTicketTypeDTO> findTicketingTypeIdAndRegistrationIdByRegistrationRequestIds(@Param("requestIds") List<Long> requestIds);

    List<RegistrationRequestTicket> findByRegistrationRequest(RegistrationRequest registrationRequest);

    List<RegistrationRequestTicket> findByRegistrationRequestIn(List<RegistrationRequest> registrationRequests);

    @Query("SELECT rt FROM RegistrationRequestTicket AS rt WHERE rt.registrationRequest.id in (:requestIds)" )
    List<RegistrationRequestTicket> findAllByRegistrationRequestIds(@Param("requestIds") List<Long> requestIds);

    @Query("Select sum(reg.numberOfTicket) From RegistrationRequestTicket as reg WHERE reg.ticketType = :ticketingType " +
            " AND reg.registrationRequest.event = :event AND reg.registrationRequest.status = :status " +
            " AND reg.registrationRequest.recStatus <> 'DELETE' AND reg.registrationRequest.checkoutLinkExpired =false " +
            " AND reg.registrationRequest.checkoutEmailSent = true")
    Long approvedTicketCount(@Param("event") Event event, @Param("status") RegistrationRequestStatus status,
                             @Param("ticketingType") TicketingType ticketingType);

    @Query("Select count(rrt.ticketType) from RegistrationRequestTicket as rrt "+
            "JOIN FETCH RegistrationRequest as reg ON reg.id = rrt.registrationRequest.id "+
            "WHERE rrt.ticketType.id =:ticketTypeId AND reg.recStatus <> 'DELETE'")
    Long getCountOfTicketType(@Param("ticketTypeId") Long ticketTypeId);

    @Query("SELECT rt FROM RegistrationRequestTicket AS rt WHERE rt.registrationRequest.id =:requestId and rt.ticketType = :ticketType" )
    RegistrationRequestTicket findRequestTicketByTicketTypeAndRegistrationRequestIds(@Param("ticketType") TicketingType ticketType,@Param("requestId") Long reqLongId);

    @Query(" SELECT new com.accelevents.common.dto.RegistrationRequestTicketTypeDTO( rt.ticketType.id, rt.registrationRequest.id, rt.numberOfTicket) " +
            " FROM RegistrationRequestTicket AS rt " +
            " WHERE rt.registrationRequest.id = :requestId " +
            " AND rt.registrationRequest.recStatus <> 'DELETE' " )
    List<RegistrationRequestTicketTypeDTO> findTicketingTypeIdAndRegistrationIdByRegistrationRequestIds(@Param("requestId") Long requestId);
}