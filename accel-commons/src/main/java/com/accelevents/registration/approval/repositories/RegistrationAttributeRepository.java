package com.accelevents.registration.approval.repositories;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.RegistrationAttributeType;
import com.accelevents.domain.enums.RuleFormType;
import com.accelevents.registration.approval.domain.RegistrationAttribute;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RegistrationAttributeRepository extends CrudRepository<RegistrationAttribute, Long> {

    RegistrationAttribute findByValueTypeAndEvent(AttributeValueType attributeValueType, Event event);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute " +
            "JOIN FETCH attribute.event as event " +
            "LEFT JOIN attribute.customFormAttribute sa ON sa.id=attribute.customFormAttributeId AND sa.status <> 'DElETE' " +
            "WHERE attribute.event= :event AND attribute.type= :type AND attribute.recurringEventId IS NULL " +
            "AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') ORDER BY attribute.position")
    List<RegistrationAttribute> findByEventAndTypeAndRecurringEventIdNull(@Param("event") Event event, @Param("type") RegistrationAttributeType type);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute " +
            "JOIN FETCH attribute.event as event " +
            "LEFT JOIN attribute.customFormAttribute sa ON sa.id=attribute.customFormAttributeId AND sa.status <> 'DElETE' " +
            "WHERE attribute.event= :event AND attribute.type= :type " +
            "AND COALESCE(attribute.recurringEventId,0) = COALESCE(:recurringEventId,0) " +
            "AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') ORDER BY attribute.position")
    List<RegistrationAttribute> findByEventAndTypeAndRecurringEventId(@Param("event") Event event,  @Param("recurringEventId") Long recurringEventId,
                                                                      @Param("type") RegistrationAttributeType type);

    @Query("SELECT attr FROM RegistrationAttribute attr WHERE attr.event=:event AND COALESCE(attr.recurringEventId,0) = COALESCE(:recurringEventId,0) ")
    List<RegistrationAttribute> findByEventidAndRecurringEventIdNull(@Param("event") Event event, @Param("recurringEventId") Long recurringEventId);

    RegistrationAttribute findByNameAndEventAndTypeAndRecurringEventId(String name, Event event, RegistrationAttributeType type, Long recurringEventId);

    @Query("SELECT attr FROM RegistrationAttribute attr WHERE attr.event=:event AND attr.name=:name AND attr.type=:type AND attr.recurringEventId IS NULL ")
    RegistrationAttribute findByNameAndEventAndTypeAndRecurringEventIdNull(@Param("name") String name, @Param("type") RegistrationAttributeType type, @Param("event") Event event);

    @Query("Select attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event AND attributes.createdFrom=:createdFrom")
    List<RegistrationAttribute> findAllAttributesByCreatedFromAndEventId(@Param("createdFrom") Long createdFrom, @Param("event") Event event);

    RegistrationAttribute findFirstByEventOrderByPositionDesc(Event event);

    @Query("SELECT attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event AND attributes.recurringEventId is null ORDER BY attributes.position")
    List<RegistrationAttribute> findByEventAndRecurringEventIdNullOrderByPosition(@Param("event") Event event);

    @Query("SELECT attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event AND enabled = true AND attributes.recurringEventId is null AND type in (:types) ORDER BY attributes.type desc, attributes.position")
    List<RegistrationAttribute> findByEventAndRecurringEventIdNullAndTypesOrderByPosition(@Param("event") Event event, @Param("types") List<RegistrationAttributeType> types);

    @Query("SELECT attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event AND enabled = true AND attributes.recurringEventId = :recurringEventId AND type in (:types) ORDER BY attributes.type desc, attributes.position")
    List<RegistrationAttribute> findByEventAndRecurringEventIdAndTypesOrderByPosition(@Param("event") Event event, @Param("recurringEventId") Long recurringEventId, @Param("types") List<RegistrationAttributeType> types);

    @Query("select attributes FROM RegistrationAttribute attributes where attributes.event =:event AND attributes.position>:currentPosition ORDER BY attributes.position")
    List<RegistrationAttribute> nextPositionForHolderAttribute(@Param("event") Event event, @Param("currentPosition") int currentPosition);

    @Query("select attributes FROM RegistrationAttribute attributes where attributes.event = :event AND attributes.position<:currentPosition ORDER BY attributes.position DESC")
    List<RegistrationAttribute> previousPositionForHolderAttribute(@Param("event") Event event, @Param("currentPosition") int currentPosition);

    @Modifying
    @Query("UPDATE RegistrationAttribute set position=(position+:updateCount) where event = :event AND position> :startPosition AND position <:endPosition")
    void updatePositionForHolderItem(@Param("event") Event event, @Param("startPosition") int startPosition, @Param("endPosition") int endPosition,
                                     @Param("updateCount") int updateCount);

    @Modifying
    @Query("UPDATE RegistrationAttribute set position=(position+:updateCount) where event= :event")
    void updatePositionForAllHolderAttribute(@Param("event") Event event, @Param("updateCount") int updateCount);

    @Query("Select attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event AND attributes.recurringEventId IS NOT NULL")
    List<RegistrationAttribute> findAllWhichHavingRecurringEventNotNull(@Param("event") Event event);

    @Query("SELECT attributes FROM RegistrationAttribute attributes WHERE attributes.event=:eventId" +
            " AND attributes.name=:attributeName AND attributes.recurringEventId is not null")
    List<RegistrationAttribute> findByEventAndRecurringEventIdNotNull(@Param("eventId") Event eventId, @Param("attributeName") String attributeName);

    @Query("SELECT attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event and attributes.createdFrom=:attributeId")
    List<RegistrationAttribute> findByCreatedFromIdAndEvent(@Param("attributeId") Long attributeId, @Param("event") Event event);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute JOIN FETCH attribute.event as event " +
            "WHERE attribute.event = :event AND attribute.recurringEventId IS NULL AND type = 'ATTENDEE'")
    List<RegistrationAttribute> findRegistrationRequiredByEventAndRecurringEventIdNull(@Param("event") Event event);

    @Query("SELECT attributes FROM RegistrationAttribute attributes JOIN FETCH attributes.event as event " +
            "WHERE attributes.event = :event AND attributes.recurringEventId=:recurringEventId AND type = 'ATTENDEE'")
    List<RegistrationAttribute> findRegistrationRequiredByRecurringEventId(@Param("event") Event event, @Param("recurringEventId") Long recurringEventId);

    @Modifying
    @Query("DELETE FROM RegistrationAttribute attributes WHERE attributes.event=:event AND attributes.recurringEventId IN (:recurringEventsToBeDelete) ")
    void deleteByRecurringIdAndEventId(@Param("recurringEventsToBeDelete") List<Long> recurringEventsToBeDelete, @Param("event") Event event);

    @Query("SELECT attributes FROM RegistrationAttribute attributes JOIN FETCH attributes.event as event WHERE attributes.event=:event " +
            " AND attributes.recurringEventId=:recurringEventId AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<RegistrationAttribute> findAllAttributesByRecurringEventId(@Param("event") Event event, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event AND attributes.id IN (:ids) ")
    List<RegistrationAttribute> findByEventAndIds(@Param("event") Event event, @Param("ids") List<Long> ids);

    @Query("SELECT attributes FROM RegistrationAttribute attributes WHERE attributes.event=:event")
    List<RegistrationAttribute> findByEvent(@Param("event") Event event);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute WHERE attribute.event=:event " +
            "AND attribute.parentQuestionId IN (:parentQueIds)")
    List<RegistrationAttribute> findAllSubQueByParentIdsAndEvent(@Param("event") Event event, @Param("parentQueIds") List<Long> parentQueIds);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute WHERE attribute.event = :event AND attribute.id IN (:ids)")
    List<RegistrationAttribute> findAllParentQueByIdsAndEvent(@Param("event") Event event, @Param("ids") List<Long> ids);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute WHERE attribute.event = :event AND attribute.parentQuestionId=:parentQueId")
    List<RegistrationAttribute> findAllSubQueByParentIdAndEvent(@Param("event") Event event, @Param("parentQueId") long parentQueId);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute WHERE attribute.event = :event AND attribute.id=:id")
    Optional<RegistrationAttribute> findRegistrationAttributeByEventAndId(@Param("event") Event event, @Param("id") long id);

    @Query("SELECT attribute FROM RegistrationAttribute as attribute WHERE attribute.type = :type AND attribute.id=:id")
    Optional<RegistrationAttribute> findRegistrationAttributeByIdAndType(@Param("id") Long id, @Param("type") RegistrationAttributeType type);

    @Query("SELECT attributes FROM RegistrationAttribute attributes " +
            "WHERE attributes.event=:event AND enabled = true " +
            "AND attributes.type in (:types) " +
            "ORDER BY attributes.position")
    List<RegistrationAttribute> getRegistrationRequiredByEventAndAttributeOrder(@Param("event") Event event,@Param("types") List<RegistrationAttributeType> types);

    @Query(" SELECT CASE WHEN COUNT(attributes.id) > 0 THEN TRUE ELSE FALSE END from RegistrationAttribute attributes " +
            "WHERE attributes.event=:event AND (attributes.recurringEventId IS NULL OR  attributes.recurringEventId=0) " +
            "AND attributes.customFormAttributeId In :customFormAttributeIds " +
            "AND attributes.type in (:types) ")
    boolean existsByEventAndRecurringEventIsNullAndCustomFormAttributeIdInAndRegistrationAttributeTypeIn(@Param("event") Event event,@Param("customFormAttributeIds")List<Long> customFormAttributeIds,@Param("types") List<RegistrationAttributeType> types);

    @Query(" SELECT CASE WHEN COUNT(attributes.id) > 0 THEN TRUE ELSE FALSE END from RegistrationAttribute attributes " +
            "WHERE attributes.event = :event AND attributes.recurringEventId=:recurringEventId " +
            "AND attributes.customFormAttributeId In :customFormAttributeIds " +
            "AND attributes.type in (:types) ")
    boolean existsByEventAndRecurringIdAndEAndCustomFormAttributeIdInAndRegistrationAttributeTypeIn(@Param("event") Event event, @Param("recurringEventId") Long recurringEventId,@Param("customFormAttributeIds")List<Long> customFormAttributeIds,@Param("types") List<RegistrationAttributeType> types);

    @Query("SELECT attributes FROM RegistrationAttribute attributes " +
            "WHERE attributes.eventId=:eventId AND enabled = true " +
            "AND attributes.type in (:registrationAttributeTypes) " +
            "AND attributes.valueType in (:attributeValueTypes) " +
            "ORDER BY attributes.position ")
    List<RegistrationAttribute> findByEventIdAndRegistrationAttributeTypeAndAttributeValueType(@Param("eventId") Long eventId,@Param("registrationAttributeTypes") List<RegistrationAttributeType> registrationAttributeTypes,@Param("attributeValueTypes") List<AttributeValueType> attributeValueTypes);


    @Query("SELECT ra.id FROM RegistrationAttribute ra WHERE ra.eventId = :eventId AND ra.type = :attributeType")
    List<Long> findAllIdByEventAndTypeAndRecurringEventIdNull(@Param("eventId") Long eventId, @Param("attributeType") RegistrationAttributeType attributeType);

    @Query("SELECT ra.id, ra.name FROM RegistrationAttribute ra WHERE ra.eventId = :eventId AND ra.type = :attributeType")
    List<Object[]> findAllIdAndNameByEventAndType(@Param("eventId") Long eventId, @Param("attributeType") RegistrationAttributeType attributeType);

    @Query("SELECT ra.id FROM RegistrationAttribute ra WHERE ra.eventId = :eventId AND ra.type = :attributeType AND ra.recurringEventId = :recurringEventId")
    List<Long> findAllIdByEventAndTypeAndRecurringEventId(@Param("eventId") Long eventId, @Param("attributeType") RegistrationAttributeType registrationAttributeType, @Param("recurringEventId") Long recurringEventId);
}
