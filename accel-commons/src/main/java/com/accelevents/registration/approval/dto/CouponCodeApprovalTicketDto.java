package com.accelevents.registration.approval.dto;

import com.accelevents.common.dto.UserBasicDto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

public class CouponCodeApprovalTicketDto extends UserBasicDto {

    @Schema(description = "Coupon code")
    private String couponCode;

    @Schema(description = "Map of requested ticket types and ticket count")
    private Map<Long, Integer> ticketTypes;

    @Schema(description = "Recurring event id")
    private Long recurringEventId;

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public Map<Long, Integer> getTicketTypes() {
        return ticketTypes;
    }

    public void setTicketTypes(Map<Long, Integer> ticketTypes) {
        this.ticketTypes = ticketTypes;
    }

    public Long getRecurringEventId() {
        return recurringEventId;
    }

    public void setRecurringEventId(Long recurringEventId) {
        this.recurringEventId = recurringEventId;
    }

    @Override
    public String toString() {
        return "CouponCodeApprovalTicketDto{" +
                "couponCode='" + couponCode + '\'' +
                ", ticketTypes=" + ticketTypes +
                ", recurringEventId=" + recurringEventId +
                '}';
    }
}
