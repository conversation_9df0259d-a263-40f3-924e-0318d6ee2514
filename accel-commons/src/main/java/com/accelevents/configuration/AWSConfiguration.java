package com.accelevents.configuration;

import com.accelevents.utils.Constants;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.cloudfront.AmazonCloudFront;
import com.amazonaws.services.cloudfront.AmazonCloudFrontClientBuilder;
import com.amazonaws.services.glacier.AmazonGlacier;
import com.amazonaws.services.glacier.AmazonGlacierClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.sns.AmazonSNS;
import com.amazonaws.services.sns.AmazonSNSClientBuilder;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.amazonaws.services.sqs.AmazonSQSAsyncClientBuilder;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.opentok.OpenTok;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.ui.freemarker.FreeMarkerConfigurationFactoryBean;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.lambda.LambdaClient;
import software.amazon.awssdk.services.sqs.SqsClient;

@Configuration
public class AWSConfiguration {

	@Value("${cloud.aws.credentials.accessKey}")
	private String accessKey;

	@Value("${cloud.aws.credentials.secretKey}")
	private String secretKey;

    @Value("${vonageApi.key}")
    private int vonageApiKey;

    @Value("${vonageApi.secret}")
    private String vonageApiSecret;

    @Value("${app.profile}")
    private String appProfile;

	@Bean
	public AWSCredentialsProvider awsCredentialsProvider() {
        // If APP profile is local, use the access key and secret key from the application-local.properties file else use the default credentials provider chain
		return Constants.ENV_LOCAL.equalsIgnoreCase(appProfile) ? new AWSStaticCredentialsProvider( new BasicAWSCredentials(accessKey, secretKey)) : new DefaultAWSCredentialsProviderChain();
	}

    @Bean
    public AwsCredentialsProvider awsCredentialsProvider2(){
        return Constants.ENV_LOCAL.equalsIgnoreCase(appProfile) ? StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey)) : DefaultCredentialsProvider.create();
    }

	@Bean
	public AmazonS3 amazonS3(AWSCredentialsProvider awsCredentialsProvider) {
		return AmazonS3ClientBuilder.standard().withCredentials(awsCredentialsProvider)
                .withRegion(Regions.US_EAST_1).build();
	}

	@Bean
	public AmazonSQS amazonSQS(AWSCredentialsProvider awsCredentialsProvider) {
		return AmazonSQSClientBuilder.standard().withCredentials(awsCredentialsProvider)
                .withRegion(Regions.US_EAST_1).build();
	}

    @Bean
    public SqsClient sqsClient(AwsCredentialsProvider awsCredentialsProvider){
        return SqsClient.builder()
                    .region(Region.US_EAST_1)
                    .credentialsProvider(awsCredentialsProvider)
                    .build();
    }

    @Bean
    public LambdaClient lambdaClient(AwsCredentialsProvider awsCredentialsProvider){
        return LambdaClient.builder()
                .region(Region.US_EAST_1)
                .credentialsProvider(awsCredentialsProvider)
                .build();
    }


    @Bean
    public AmazonCloudFront amazonCloudFront(AWSCredentialsProvider awsCredentialsProvider){
        return  AmazonCloudFrontClientBuilder.standard().withCredentials(awsCredentialsProvider)
                .withRegion(Regions.US_EAST_1).build();
    }

    @Bean
    @Primary
    public AmazonSQSAsync amazonSQSAsync(AWSCredentialsProvider awsCredentialsProvider){
        return AmazonSQSAsyncClientBuilder.standard().withCredentials(awsCredentialsProvider)
                .withRegion(Regions.US_EAST_1).build();

    }


    @Bean
	public FreeMarkerConfigurationFactoryBean freemarkerMailConfiguration() {
		FreeMarkerConfigurationFactoryBean freeMarkerConfigurationFactoryBean = new FreeMarkerConfigurationFactoryBean();
		freeMarkerConfigurationFactoryBean.setTemplateLoaderPath("classpath:/templates/");
		return freeMarkerConfigurationFactoryBean;
	}

    public OpenTok openTok() {
        return new OpenTok.Builder(vonageApiKey, vonageApiSecret).build();
    }

    @Bean
    public AmazonGlacier amazonGlacier(AWSCredentialsProvider awsCredentialsProvider) {
       return AmazonGlacierClientBuilder.standard()
                .withCredentials(awsCredentialsProvider)
                .withRegion(Regions.US_EAST_1).build();
    }

    @Bean
    public AmazonSNS amazonSNS(AWSCredentialsProvider awsCredentialsProvider){
        return AmazonSNSClientBuilder.standard().withCredentials(awsCredentialsProvider)
                    .withRegion(Regions.US_EAST_1).build();
    }

    @Bean
    public DynamoDbClient dynamoDbClient(AwsCredentialsProvider awsCredentialsProvider) {
        return DynamoDbClient.builder()
                .region(Region.US_EAST_1)
                .credentialsProvider(awsCredentialsProvider)
                .build();
    }

}