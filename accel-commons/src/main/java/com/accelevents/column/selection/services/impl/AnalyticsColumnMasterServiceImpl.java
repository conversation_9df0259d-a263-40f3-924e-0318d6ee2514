package com.accelevents.column.selection.services.impl;

import com.accelevents.column.selection.dto.AnalyticsColumnMasterDto;
import com.accelevents.column.selection.dto.ColumnDto;
import com.accelevents.column.selection.services.AnalyticsColumnMasterService;
import com.accelevents.domain.Event;
import com.accelevents.domain.ReviewerFormQuestion;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.analytics.AnalyticsColumnMaster;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.AdvanceFilterStaticAttributesDto;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.registration.approval.domain.RegistrationAttribute;
import com.accelevents.registration.approval.services.RegistrationAttributeService;
import com.accelevents.repositories.ReviewerFormQuestionRepository;
import com.accelevents.ro.audience.service.ROAnalyticsColumnMasterService;
import com.accelevents.services.EventLevelSettingService;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.accelevents.column.selection.ColumnSelectionConstants.ColumnType;
import static com.accelevents.utils.Constants.STRING_UNDERSCORE;

@Service
public class AnalyticsColumnMasterServiceImpl implements AnalyticsColumnMasterService {

    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Autowired
    private RegistrationAttributeService registrationAttributeService;
    @Autowired
    private EventLevelSettingService eventLevelSettingService;
    @Autowired
    private ReviewerFormQuestionRepository reviewerFormQuestionRepository;
    @Autowired
    private ROAnalyticsColumnMasterService roAnalyticsColumnMasterService;


    @Override
    @Deprecated //USE RO method
    public AnalyticsColumnMasterDto getColumnMasterJson(AnalyticsArea area) {
        AnalyticsColumnMaster columnMaster = roAnalyticsColumnMasterService.getColumnMaster(area)
                .orElseThrow(() -> new NotFoundException(NotFoundException.UserColumnSelectionNotFound.COLUMN_MASTER_NOT_FOUND));
        return AnalyticsColumnMasterDto.toDto(columnMaster);
    }

    @Override
    public AnalyticsColumnMasterDto getColumnMasterJson(AnalyticsArea area, Event event) {
        AnalyticsColumnMasterDto columnMasterDto = roAnalyticsColumnMasterService.getColumnMasterJson(area);
        if (AnalyticsArea.ATTENDEE_ANALYTICS.equals(area)) {
            columnMasterDto.setCustomiseColumns(getTicketBuyerAndHolderAttributes(event, columnMasterDto.getCustomiseColumns()));
        } else if (AnalyticsArea.REGISTRATION_APPROVAL_SPEAKER.equals(area)
                || AnalyticsArea.REGISTRATION_APPROVAL_EXPO.equals(area)
                || AnalyticsArea.REGISTRATION_APPROVAL_REVIEWER.equals(area)) {
            columnMasterDto.setCustomiseColumns(getRegistrationApprovalColumns(event, columnMasterDto.getCustomiseColumns(), area));
        } else if (AnalyticsArea.REGISTRATION_APPROVAL_ATTENDEE.equals(area)) {
            columnMasterDto.setCustomiseColumns(getRegistrationApprovalColumnsForAttendee(event, columnMasterDto.getCustomiseColumns()));
        }
        return columnMasterDto;
    }

    private String getRegistrationApprovalColumnsForAttendee(Event event, String customColumns) {
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);
        List<ColumnDto> ticketAttributeColumns = new ArrayList<>();

        for (TicketHolderRequiredAttributes attribute : holderRequiredAttributes) {
            if (Constants.FIRST_NAME.equals(attribute.getName()) || Constants.LAST_NAME.equals(attribute.getName()) || Constants.EMAIL.equals(attribute.getName())) {
                continue;
            }
            if (attribute.getEnabledForTicketPurchaser()) {
                ColumnDto buyerColumn = new ColumnDto(String.valueOf(attribute.getId()),attribute.getName(),getRegistrationAttributeTypeFromArea(RegistrationAttributeType.ATTENDEE));
                ticketAttributeColumns.add(buyerColumn);
            }
        }
        List<ColumnDto> customColumnsDtos = JsonMapper.parseJsonArray(customColumns, ColumnDto.class);
        customColumnsDtos.addAll(ticketAttributeColumns);
        return JsonMapper.convertToString(customColumnsDtos);
    }

    @Override
    public AnalyticsColumnMasterDto getColumnMasterJsonIfEventNotExist(AnalyticsArea area) {
        return getColumnMasterJson(area);
    }

    /**
     * Add additional columns for attendee analytics
     * Method will add ticket holder and buyer attribute as columns
     * @param event
     * @param customColumns
     */
    private String getTicketBuyerAndHolderAttributes(Event event, String customColumns) {
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);
        List<ColumnDto> ticketAttributeColumns = new ArrayList<>();
        for (TicketHolderRequiredAttributes attribute : holderRequiredAttributes) {
            if (attribute.getEnabledForTicketHolder()) {
                ColumnDto holderColumn = new ColumnDto(String.valueOf(attribute.getId()), Constants.PREFIX_HOLDER + attribute.getName(), ColumnType.HOLDER);
                ticketAttributeColumns.add(holderColumn);
            }
            if (attribute.getEnabledForTicketPurchaser()) {
                ColumnDto buyerColumn = new ColumnDto(String.valueOf(attribute.getId()), Constants.PREFIX_BUYER + attribute.getName(), ColumnType.BUYER);
                ticketAttributeColumns.add(buyerColumn);
            }
        }
        List<ColumnDto> customColumnsDtos = JsonMapper.parseJsonArray(customColumns, ColumnDto.class);
        customColumnsDtos.addAll(ticketAttributeColumns);
        return JsonMapper.convertToString(customColumnsDtos);
    }

    /**
     * Add additional columns for Registration Approval Speaker and Exhibitor table
     * Method will add ticket Registration Approval Attributes
     * @param event
     * @param customColumns
     * @param area
     * @return
     */
    private String getRegistrationApprovalColumns(Event event, String customColumns, AnalyticsArea area) {
        RegistrationRequestType type = null;
        if (AnalyticsArea.REGISTRATION_APPROVAL_SPEAKER.equals(area)) {
            type = RegistrationRequestType.SPEAKER;
        } else if (AnalyticsArea.REGISTRATION_APPROVAL_EXPO.equals(area)) {
            type = RegistrationRequestType.EXPO;
        } else if (AnalyticsArea.REGISTRATION_APPROVAL_ATTENDEE.equals(area)) {
            type = RegistrationRequestType.ATTENDEE;
        } else if (AnalyticsArea.REGISTRATION_APPROVAL_REVIEWER.equals(area)) {
            type = RegistrationRequestType.REVIEWER;
        }
        List<RegistrationAttribute> attributeDtos = registrationAttributeService.getRegistrationRequiredAttributesOrderByAttributeOrder(event, 0L, type);
        List<ColumnDto> ticketAttributeColumns = new ArrayList<>();
        for (RegistrationAttribute attribute : attributeDtos) {
            // Skipping common attributes because they already added in Default Columns
            if(!RegistrationRequestType.SPEAKER.equals(type)){
                if (Constants.FIRST_NAME.equals(attribute.getName()) || Constants.LAST_NAME.equals(attribute.getName()) || Constants.EMAIL_ADDRESS.equals(attribute.getName())) {
                    continue;
                }
            }
            else {
                if ((RegistrationAttributeType.SPEAKER_INFO.equals(attribute.getType())) || (Constants.SPEAKER_APPROVAL_SESSION_TOPIC.equals(attribute.getName()) || Constants.SPEAKER_APPROVAL_SESSION_DESCRIPTION.equals(attribute.getName()))) {
                    continue;
                }
            }
            ticketAttributeColumns.add(new ColumnDto(String.valueOf(attribute.getId()), attribute.getName(), getRegistrationAttributeTypeFromArea(attribute.getType())));
        }

        if (RegistrationRequestType.SPEAKER.equals(type) && eventLevelSettingService.isReviewerRegistrationApproval(event.getEventId())) {
            List<ReviewerFormQuestion> reviewerFormQuestionList = reviewerFormQuestionRepository.findByEventIdAndTypes(
                    event.getEventId(), Arrays.asList(AttributeValueType.STAR_RATING, AttributeValueType.NUMBER_RATING));

            reviewerFormQuestionList.forEach(q -> ticketAttributeColumns.add(
                    new ColumnDto(String.valueOf(q.getId()), q.getQuestionTitle(), ColumnType.REVIEW_FORM_QUESTION)));
        }

        List<ColumnDto> customColumnsDtos = JsonMapper.parseJsonArray(customColumns, ColumnDto.class);
        customColumnsDtos.addAll(ticketAttributeColumns);
        return JsonMapper.convertToString(customColumnsDtos);
    }

    /**
     * Get Column Master Json in Key value pair
     * Key as column key and in value as column label
     * @param area Analytics area for fetch columns
     * @return return a map in key label pair of columns
     */
    @Override
    public Map<String, String> getColumnKeyLabelMap(AnalyticsArea area, Event event) {
        AnalyticsColumnMasterDto columnMasterJson = getColumnMasterJson(area,event);
        List<ColumnDto> defaultColumns = JsonMapper.parseJsonArray(columnMasterJson.getDefaultColumns(), ColumnDto.class);
        defaultColumns.addAll(JsonMapper.parseJsonArray(columnMasterJson.getCustomiseColumns(), ColumnDto.class));
        return  defaultColumns.stream().collect(Collectors.toMap(column -> {
            String key = column.getKey();
            if (ColumnType.HOLDER.equals(column.getType()) || ColumnType.BUYER.equals(column.getType())) {
                key = key.concat(STRING_UNDERSCORE).concat(column.getType().name());
            }
            return key;
            },ColumnDto::getLabel));
    }

    private ColumnType getRegistrationAttributeTypeFromArea(RegistrationAttributeType attributeType) {
        switch (attributeType) {
            case ATTENDEE:
                return ColumnType.REGISTRATION_APPROVAL_ATTENDEE;
            case EXPO_INFO:
                return ColumnType.REGISTRATION_APPROVAL_EXPO_INFO;
            case SPEAKER_DETAILS:
                return ColumnType.REGISTRATION_APPROVAL_SPEAKER_DETAILS;
            case BOOTH_DETAILS:
                return ColumnType.REGISTRATION_APPROVAL_BOOTH_DETAILS;
            case SPEAKER_INFO:
                return ColumnType.REGISTRATION_APPROVAL_SPEAKER_INFO;
            case REVIEWER_INFO:
                return ColumnType.REGISTRATION_APPROVAL_REVIEWER;
            default:
                return ColumnType.REGISTRATION_APPROVAL_ATTENDEE;
        }
    }

    @Override
    public List<AdvanceFilterStaticAttributesDto> getAdvancedFilterStaticAttributes() {
        AnalyticsColumnMasterDto columnMasterData = roAnalyticsColumnMasterService.getColumnMasterJson(AnalyticsArea.ATTENDEE_ANALYTICS);
        return JsonMapper.parseJsonArray(columnMasterData.getStaticColumns(), AdvanceFilterStaticAttributesDto.class);
    }
}
