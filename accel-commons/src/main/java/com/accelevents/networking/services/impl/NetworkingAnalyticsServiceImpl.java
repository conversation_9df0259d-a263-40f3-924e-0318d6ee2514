package com.accelevents.networking.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.networking.dto.*;
import com.accelevents.networking.services.NetworkingAnalyticsService;
import com.accelevents.networking.services.NetworkingMatchesService;
import com.accelevents.repositories.UserRepository;
import com.accelevents.services.dynamodb.analytics.ConsolidatedAnalyticsService;
import com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsUtils;
import com.accelevents.services.neptune.NeptuneAttendeeDetailService;
import com.accelevents.session_speakers.dto.AttendeeSession;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.services.MeetingScheduleService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.utils.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class NetworkingAnalyticsServiceImpl implements NetworkingAnalyticsService {

    private final Logger log = LoggerFactory.getLogger(NetworkingAnalyticsServiceImpl.class);

    @Autowired
    private UserSessionService userSessionService;
    @Autowired
    private NetworkingMatchesService networkingMatchesService;
    @Autowired
    private NeptuneAttendeeDetailService neptuneAttendeeDetailService;
    @Autowired
    private MeetingScheduleService meetingScheduleService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ConsolidatedAnalyticsService consolidatedAnalyticsService;


    /**
     * Get analytics data for Networking
     * @param event
     * @param user
     * @return
     */
    @Override
    public NetworkAnalyticsDTO getNetworkingAnalytics(Event event, User user) {
        log.info("Start get networking analytics user {}, event {} ", user.getUserId(), event.getEventId());
        NetworkAnalyticsDTO networkAnalyticsDTO = new NetworkAnalyticsDTO();
        long matches = networkingMatchesService.getMatchesCountByEventId(event.getEventId());
        Long members = userRepository.countByEvent(event.getEventId());
        long meetings = meetingScheduleService.getTotalMeetingBooked(event.getEventId());
        Long connections = neptuneAttendeeDetailService.getTotalConnectionCountMade(String.valueOf(event.getEventId()));
        double avgConnectionPerAttendee = NumberUtils.isNumberGreaterThanZero(members) ? DoubleHelper.roundValueTwoDecimal((double) connections / (double) members) : 0;

        log.info("Fetched Networking analytics user {}, event {}, matches {}, members {}, meetings {}, avgConnectionPerAttendee {}",
                user.getUserId(),
                event.getEventId(),
                matches,
                members,
                meetings,
                avgConnectionPerAttendee);

        networkAnalyticsDTO.setConnections(connections);
        networkAnalyticsDTO.setMatches(matches);
        networkAnalyticsDTO.setMeetings(meetings);
        networkAnalyticsDTO.setAvgConnectionPerAttendee(avgConnectionPerAttendee);
        log.info("End get networking analytics user {}, event {} ", user.getUserId(), event.getEventId());
        return networkAnalyticsDTO;
    }

    /**
     * Get networking analytics of given user
     * @param event
     * @param user
     * @return
     */
    @Override
    public NetworkingAttendeeAnalyticsDto getNetworkingAnalyticsByUser(Event event, User user) {
        log.info("Start get networking analytics for user {}", user.getUserId());

        List<AttendeeSession> attendeeSessions = userSessionService.findAttendeeNetworkingSessionsByUserId(event.getEventId(), user.getUserId(), EnumSessionFormat.MEET_UP);
        log.info("Get networking analytics found attendeeSessions size {}", attendeeSessions.size());

        List<NetworkMatchesUsersDTO> networkingMatches = networkingMatchesService.getNetworkMatchesUsersByEventAndUser(event.getEventId(), user);
        log.info("Get networking analytics found networkingMatches size {}", networkingMatches.size());
        // Connected Users by event
        List<AttendeeProfileDto> connectedAttendees = neptuneAttendeeDetailService.getAllConnectionFromEvent(String.valueOf(user.getUserId()), String.valueOf(event.getEventId()));
        Map<Long, List<NetworkMatchesUsersDTO>> networkingMatchesMap = networkingMatches.stream().collect(Collectors.groupingBy(NetworkMatchesUsersDTO::getSessionId));

        NetworkingAttendeeAnalyticsDto networkingAttendeeAnalyticsDto = new NetworkingAttendeeAnalyticsDto();

        List<NetworkingAnalyticsDto> networkingAnalyticsDtos =
                attendeeSessions
                        .stream()
                        .map(attendeeSession -> new NetworkingAnalyticsDto(
                                        attendeeSession,
                                        networkingMatchesMap.get(attendeeSession.getSessionId()),
                                        neptuneAttendeeDetailService.getAllConnectionFromNetworkingSession(String.valueOf(user.getUserId()), String.valueOf(event.getEventId()), String.valueOf(attendeeSession.getSessionId()))
                                )
                        ).collect(Collectors.toList());

        networkingAttendeeAnalyticsDto.setSessions(networkingAnalyticsDtos);
        networkingAttendeeAnalyticsDto.setTotalMatch((long) networkingMatches.size());
        networkingAttendeeAnalyticsDto.setAttendedSessions((long) attendeeSessions.size());
        networkingAttendeeAnalyticsDto.setTotalConnection((long) connectedAttendees.size());
        networkingAttendeeAnalyticsDto.setConnectedAttendees(connectedAttendees);
        log.info("End get networking analytics total attended session {}, total match {},  user {}", attendeeSessions.size(), networkingMatches.size(), user.getUserId());
        return networkingAttendeeAnalyticsDto;
    }


    @Override
    public List<NetworkingMatchDTO> getNetworkingMatches(Long evenId, Long sessionId) {
        List<NetworkingMatchDTO> sessionMatches = networkingMatchesService.getSessionMatches(evenId, sessionId);
        log.info("Successfully fetched networking matches for session {}, size {}", sessionId, sessionMatches.size());
        return sessionMatches;
    }


    @Override
    public DataTableResponse getConnectionAnalytics(Event event, String searchString, int page, int size) {
        log.info("Start of get connection analytics event {}", event.getEventId());
        DataTableResponse dataTableResponse = new DataTableResponse();
        Page<User> userList = userRepository.getAllAttendeeByEvent(event.getEventId(), searchString, PageRequest.of(page, size));
        List<Long> userIds = userList.getContent().stream().map(User::getUserId).collect(Collectors.toList());

        List<Object[]> bookedMeetingByUserIds = meetingScheduleService.getBookedMeetingByUserIds(event.getEventId(), userIds);
        Map<Long, Long> bookingMap =  bookedMeetingByUserIds.stream().collect(Collectors.toMap(e -> ((BigInteger) e[0]).longValue(), e-> ((BigDecimal) e[1]).longValue()));

        List<IdCountDto> meetingRequestSentByUserIds = meetingScheduleService.getMeetingRequestSentByUserIds(event.getEventId(), userIds);
        Map<Long, Long> requestSentMap = meetingRequestSentByUserIds.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount));

        List<IdCountDto> meetingRequestReceivedByUserIds = meetingScheduleService.getMeetingRequestReceivedByUserIds(event.getEventId(), userIds);
        Map<Long, Long> requestReceivedMap = meetingRequestReceivedByUserIds.stream().collect(Collectors.toMap(IdCountDto::getId, IdCountDto::getCount));

        Map<Long, Long> connectionCounts = neptuneAttendeeDetailService.getConnectionCountByAttendeeIds(userIds, String.valueOf(event.getEventId()));

        log.info("Fetched schedule meeting data bookedMeetingByUserIds {}, meetingRequestSentByUserIds {}, meetingRequestReceivedByUserIds {}, connectionCounts {}",
                bookedMeetingByUserIds.size(), meetingRequestSentByUserIds.size(), meetingRequestReceivedByUserIds.size(), connectionCounts.size());

        List<MeetingTimeAnalyticsDTO> meetingTimeAnalyticsDtoList = consolidatedAnalyticsService.getUserMeetingAnalyticsData(event, userIds);
        Map<Long, MeetingTimeAnalyticsDTO> meetingAnalytics = meetingTimeAnalyticsDtoList.stream().collect(
                Collectors.toMap(MeetingTimeAnalyticsDTO::getUserId, m -> m)
        );

        List<ConnectionAnalyticsDTO> connectionAnalyticDTOS = userList.getContent().stream()
                .map(e -> {
                    ConnectionAnalyticsDTO dto = new ConnectionAnalyticsDTO(e.getUserId(), e.getFirstName(), e.getLastName(), e.getEmail());
                    dto.setConnections(connectionCounts.getOrDefault(e.getUserId(), 0L));
                    dto.setMeetings(bookingMap.getOrDefault(e.getUserId(), 0L));
                    dto.setMeetingRequestsSent(requestSentMap.getOrDefault(e.getUserId(), 0L));
                    dto.setMeetingRequestReceived(requestReceivedMap.getOrDefault(e.getUserId(), 0L));
                    dto.setMeetingTime(VideoAnalyticsUtils.getSecondsInHHMMSS(meetingAnalytics.getOrDefault(e.getUserId(), new MeetingTimeAnalyticsDTO(e.getUserId(), 0.0, 0.0)).getTotalMeetingTime()));
                    dto.setAvgMeetingTime(VideoAnalyticsUtils.getSecondsInHHMMSS(meetingAnalytics.getOrDefault(e.getUserId(), new MeetingTimeAnalyticsDTO(e.getUserId(), 0.0, 0.0)).getAvgMeetingTime()));
                    return dto;
                })
                .collect(Collectors.toList());

        dataTableResponse.setData(connectionAnalyticDTOS);
        dataTableResponse.setRecordsTotal(userList.getTotalElements());
        dataTableResponse.setRecordsFiltered(userList.getContent().size());

        log.info("Successfully set data in connection analytics dto size {}, totalElements {}, filteredElements {}",connectionAnalyticDTOS.size(), userList.getTotalElements(), userList.getContent().size());
        return dataTableResponse;
    }

    @Override
    public List<AttendeeProfileDto> getConnectedAttendeeByUser(Long userId, Event event) {
        return neptuneAttendeeDetailService.getAllConnectionFromEvent(String.valueOf(userId), String.valueOf(event.getEventId()));
    }



}
