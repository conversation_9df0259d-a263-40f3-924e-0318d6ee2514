package com.accelevents.networking.services.impl;

import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EMAIL_RECIPIENTS;
import com.accelevents.domain.enums.NetworkingLoungeDocType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.domain.session_speakers.NetworkingLoungeDocumentLinkDetails;
import com.accelevents.domain.session_speakers.NetworkingLoungeVideoDetails;
import com.accelevents.dto.*;
import com.accelevents.dto.zapier.LoungesDto;
import com.accelevents.dto.zapier.LoungesNameDto;
import com.accelevents.dto.zapier.NetworkingLoungesDto;
import com.accelevents.dto.zapier.NetworkingLoungesWithStatusAndMemberDto;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.EnumLoungeSorting;
import com.accelevents.messages.EnumLoungeStatus;
import com.accelevents.messages.EnumLoungeTab;
import com.accelevents.networking.dto.LoungeSettingDto;
import com.accelevents.networking.dto.LoungeTimeslotDto;
import com.accelevents.networking.dto.NetworkLoungesSettingDto;
import com.accelevents.networking.dto.NetworkingLoungeAnalyticsDto;
import com.accelevents.networking.repository.LoungesRepository;
import com.accelevents.networking.repository.LoungesTimeslotRepository;
import com.accelevents.networking.repository.NetworkingLoungesSettingRepo;
import com.accelevents.networking.services.NetworkingLounge;
import com.accelevents.networking.services.NetworkingLoungeService;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.perfomance.dto.UserPrimaryInfoDto;
import com.accelevents.repositories.MessageToContactsRepository;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneNetworkingLoungeService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.NetworkingLoungeDocumentLinkDetailsRepo;
import com.accelevents.session_speakers.repo.NetworkingLoungeVideoDetailsRepo;
import com.accelevents.session_speakers.services.MUXLivestreamAssetService;
import com.accelevents.session_speakers.services.SessionSpeakerService;
import com.accelevents.session_speakers.services.impl.graphql.ChimeMeetingGraphQLHandler;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.Permissions;
import com.amazonaws.services.chimesdkmeetings.model.Attendee;
import com.amazonaws.services.chimesdkmeetings.model.LimitExceededException;
import com.amazonaws.services.chimesdkmeetings.model.Meeting;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import javax.ws.rs.NotAuthorizedException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;
import static java.util.Comparator.reverseOrder;
import static org.apache.commons.lang3.StringUtils.contains;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class NetworkingLoungeServiceImpl implements NetworkingLoungeService {

    private static final Logger log = LoggerFactory.getLogger(NetworkingLoungeServiceImpl.class);

    @Autowired
    private NeptuneNetworkingLoungeService neptuneNetworkingLoungeService;

    @Autowired
    private SessionSpeakerService sessionSpeakerService;

    @Autowired
    private ROStaffRoleService roStaffRoleService;

    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;

    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;

    @Autowired
    private ROVirtualEventService roVirtualEventService;

    @Autowired
    private AttendeeProfileService attendeeProfileService;

    @Autowired
    private MuxService muxService;

    @Autowired
    private MUXLivestreamAssetService muxLivestreamAssetService;

    @Autowired
    private ChimeService chimeService;

    @Autowired
    private GetStreamService getStreamService;

    @Autowired
    private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;

    @Autowired
    private NetworkingLoungeVideoDetailsRepo networkingLoungeVideoDetailsRepo;

    @Autowired
    private NetworkingLoungeDocumentLinkDetailsRepo networkingLoungeDocumentLinkDetailsRepo;
    @Autowired
    private NetworkingLoungesSettingRepo networkingLoungesSettingRepo;
    @Autowired
    private LoungesTimeslotRepository loungesTimeslotRepository;

    @Autowired
    private LoungesRepository loungesRepository;

    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Autowired
    private ChimeMeetingGraphQLHandler chimeMeetingGraphQLHandler;

    @Autowired
    private MessageToContactsRepository messageToContactsRepository;

    @Autowired
    S3Wrapper s3client;


    private double sequence = 1000;

    private static final String DOCUMENT = "${documentType}";

    @Transactional
    @Override
    public String createNetworkingLounge(NetworkingLoungesDto networkingLoungesDto, User user, Event event) {
        NetworkingLounge networkingLounge = new NetworkingLounge(networkingLoungesDto);
        String neptuneLoungeId = null;
        EnumLoungeStatus loungeStatus;
        try {
            log.info("Creating Networking Lounges with EventId {} UserId {} ", event.getEventId(), user.getUserId());
            neptuneLoungeId = setPositionForNetworkingLounge(event, networkingLounge, user.getUserId());
            log.info("After creation of lounge in Neptune with NeptuneLoungeId {} ", neptuneLoungeId);
            List<Long> eventAdminIdList = staffService.findAllEventAdminId(event);

            if (eventAdminIdList.contains(user.getUserId())) {
                loungeStatus = EnumLoungeStatus.APPROVED;
            } else {
                loungeStatus = EnumLoungeStatus.PENDING;
                eventAdminIdList.add(user.getUserId());
            }
            Lounges lounges = createLoungeEntity(loungeStatus, neptuneLoungeId, networkingLoungesDto, user.getUserId(), event);
            log.info("Complete creation of Lounge in the Networking Lounges with Lounge Status {} ", loungeStatus);
            loungesRepository.save(lounges);
            getStreamService.createChannelWithChannelName(neptuneLoungeId.toUpperCase(Locale.ROOT), networkingLounge.getName(), LIVESTREAM, 1L, eventAdminIdList, false, event.getEventId());
            if(lounges.getLoungeStatus().equals(EnumLoungeStatus.APPROVED.getValue())){
                checkLoungesExistInMessageToContactsAndUpdateIt(event.getEventId(),lounges.getNeptuneLoungeId(),ADD);
            }
        } catch (NotAcceptableException notAcceptableException) {
            throw notAcceptableException;
        } catch (Exception ex) {
            log.error(" Error creating lounge in Neptune  NeptuneLoungeId {} and error msg {}", neptuneLoungeId, ex.getMessage());
        }
        return neptuneLoungeId;
    }

    private String setPositionForNetworkingLounge(Event event, NetworkingLounge networkingLounge, Long userId) {
        List<NetworkingLounge> networkingLounges = neptuneNetworkingLoungeService.getEventNetworkingLounges(event);
        networkingLounge.setName(networkingLounge.getName().trim().replaceAll("\\s+", " "));
        double updatedPosition = 1000d;
        if (!networkingLounges.isEmpty()) {
            checkIfLoungeNameAlreadyExist(networkingLounges, networkingLounge.getName(), event);
            Optional<NetworkingLounge> lastLounge = networkingLounges.stream().max(Comparator.comparingDouble(NetworkingLounge::getPosition));
            if (lastLounge.isPresent()) {
                NetworkingLounge lounge = lastLounge.get();
                updatedPosition = lounge.getPosition() + sequence;
                networkingLounge.setPosition(updatedPosition);
            }
        }
        networkingLounge.setPosition(updatedPosition);
        return neptuneNetworkingLoungeService.addNetworkingLounge(networkingLounge, String.valueOf(userId), String.valueOf(event.getEventId()));
    }

    @Override
    public void removeFromNetworkingLounge(String id, Event event, User user) {
        boolean loungeRemoveFlag;
        try {
            log.info("Removing Lounge with EventId {} UserId {} NeptuneLoungeId {}", event.getEventId(), user.getUserId(), id);
            if (roStaffService.hasHostAccessForEvent(user, event)) {
                loungeRemoveFlag = true;
            } else {

                loungeRemoveFlag = loungesRepository.existsLoungesByNeptuneLoungeIdAndCreatedBy(id, user.getUserId());

            }
            log.info("Removing Lounge with eventId {} , UserId {} , NeptuneLoungeId {} , LoungeRemovalFlag {}", event.getEventId(), user.getUserId(), id, loungeRemoveFlag);
            if (loungeRemoveFlag) {
                neptuneNetworkingLoungeService.removeNetworkingLounge(id);
                log.info(" After removing Lounge from the Neptune with EventId {} NeptuneLoungeId {}", event.getEventId(), id);
                getStreamService.deleteChannelsInBatch(Collections.singletonList(id.toUpperCase(Locale.ROOT)), event.getEventId());
                Optional<Lounges> lounges = loungesRepository.findByNeptuneLoungeId(id);
                if (lounges.isPresent()) {
                    lounges.get().setRecStatus(RecordStatus.DELETE.toString());
                    loungesRepository.save(lounges.get());
                }
                checkLoungesExistInMessageToContactsAndUpdateIt(event.getEventId(),id,DELETE);
            } else {
                throw new NotAuthorizedException(NOT_AUTHORIZE);
            }
        } catch (NotAuthorizedException notAuthorizedException) {
            throw notAuthorizedException;
        } catch (Exception ex) {
            log.error("Error while removing Lounge with EventId {} NeptuneLoungeId {} ErrorMsg {}", event.getEventId(), id, ex.getMessage());
        }

    }

    @Override
    public void updateNetworkingLounge(NetworkingLoungesDto networkingLoungesDto, String id, User user, Event event) {

        boolean loungeRemoveFlag = false;
        try {
            log.info("Updating Lounge with EventId {} , UserId {} , NeptuneLoungeId {}", event.getEventId(), user.getUserId(), id);
            if (roStaffService.hasHostAccessForEvent(user, event)) {
                loungeRemoveFlag = true;
            } else {

                loungeRemoveFlag = loungesRepository.existsLoungesByNeptuneLoungeIdAndCreatedBy(id, user.getUserId());

            }
            if (loungeRemoveFlag) {
                NetworkingLounge netLounge = neptuneNetworkingLoungeService.getNetworkingLounge(id);
                List<NetworkingLounge> networkingLounges = neptuneNetworkingLoungeService.getEventNetworkingLounges(event);
                NetworkingLounge networkingLounge = new NetworkingLounge(networkingLoungesDto);
                networkingLounge.setName(networkingLounge.getName().trim().replaceAll("\\s+", " "));
                if (netLounge == null) {
                    throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
                } else if (!netLounge.getName().equalsIgnoreCase(networkingLounge.getName())) {
                    checkIfLoungeNameAlreadyExist(networkingLounges, networkingLounge.getName(), event);
                    getStreamService.updateChannelName(id.toUpperCase(Locale.ROOT), networkingLounge.getName(), LIVESTREAM, false, event.getEventId());
                    log.info("Update the channel id: {} name: {} with disable chat toggle: {} of the eventId: {} ", id.toUpperCase(Locale.ROOT), networkingLounge.getName(), false, event.getEventId());
                }
                networkingLounge.setPosition(netLounge.getPosition());
                neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, id, netLounge.getCreatedBy(), String.valueOf(event.getEventId()));
                Optional<Lounges> optionalLounges = loungesRepository.findByNeptuneLoungeId(id);
                if (optionalLounges.isPresent()) {
                    optionalLounges.get().setTags(!CollectionUtils.isEmpty(networkingLoungesDto.getTags()) ? networkingLoungesDto.getTags().stream()
                            .map(str -> GeneralUtils.convertCommaSeparatedToList(str).get(0).trim().toUpperCase())
                            .collect(Collectors.joining(",")) : null);
                    optionalLounges.get().setVideoOccupancy(networkingLoungesDto.getVideoOccupancy());
                    if (optionalLounges.get().getCreatedBy().equals(user.getUserId())) {
                        optionalLounges.get().setModeratorLiveForumAt(StringUtils.isNotBlank(networkingLoungesDto.getModeratorLiveForumAt()) ? getDateInUTC(networkingLoungesDto.getModeratorLiveForumAt(), event.getEquivalentTimeZone()) : null);
                    }
                    loungesRepository.save(optionalLounges.get());
                }
            } else {
                throw new NotAuthorizedException(NOT_AUTHORIZE);
            }
        } catch (NotAcceptableException notAcceptableException) {
            throw notAcceptableException;
        } catch (Exception ex) {
            log.error("Updating Lounge with EventId {} , UserId {} , NeptuneLoungeId {} , loungeRemoveFlag {} and ErrorMsg {}", event.getEventId(), user.getUserId(), id, loungeRemoveFlag, ex.getMessage());
        }


    }

    @Override
    public DataTableResponse getAllNetworkingLoungeData(Event event, int size, int page, String searchString) {
        return getData(neptuneNetworkingLoungeService.getEventNetworkingLounges(event), isNotBlank(searchString) ? searchString : null);
    }

    private List<NetworkingLounge> getNetworkingLoungesByLoungesList(List<Lounges> filteredLoungesByTabAndTags, List<NetworkingLounge> allEventLounges) {
        if (!CollectionUtils.isEmpty(allEventLounges)) {
            Map<String, Lounges> loungeIDtoLounge = filteredLoungesByTabAndTags.stream().collect(Collectors.toMap(Lounges::getNeptuneLoungeId, Function.identity()));
            return allEventLounges.stream()
                    .filter(networkingLounge -> loungeIDtoLounge.containsKey(networkingLounge.getId()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public DataTableResponse getNetworkingLoungeDataWithPagination(Event event, User user, int size, int page, String searchString, EnumLoungeTab tab, EnumLoungeSorting sortColumn, boolean isAsc, List<String> tags) {
        log.info("Fetch the all networking lounges of eventId {} , UserId {} , size {} , page {} SearchString {} , tab{} , sortColumn {} , isAsc {} Tags {}", event.getEventId(), user.getUserId(), size, page, searchString, tab, sortColumn, isAsc, tags);
        List<NetworkingLoungesWithStatusAndMemberDto> networkingLoungesWithStatusAndMemberDto = null;
        List<Lounges> filteredLoungesByTabAndTags;
        boolean isAdminOrAbove = roStaffService.hasHostAccessForEvent(user, event);
        List<NetworkingLounge> networkingLounges;
        int count = 0;
        int from = 0;
        int to = 0;
        try {
            List<Long> eventAdminIdList = staffService.findAllEventAdminId(event);
            log.info("Getting  AdminId list Size {} with EventId {} from method findAllEventAdminId", eventAdminIdList.size(), event.getEventId());
            filteredLoungesByTabAndTags = getAllLoungesFromTabAndTags(isAdminOrAbove, tab, tags, user.getUserId(), event.getEventId());
            if (!filteredLoungesByTabAndTags.isEmpty()) {
                networkingLounges = getNetworkingLoungesByLoungesList(filteredLoungesByTabAndTags, neptuneNetworkingLoungeService.getEventNetworkingLounges(event));
            } else {
                return getMergeLoungeData(Collections.emptyList(), count);
            }
            log.info("Getting Networking lounges from Neptune with EventId {} , NetworkingLounges {}", event.getEventId(), networkingLounges.size());
            if (isNotBlank(searchString)) {
                String lowercaseSearchStr = searchString.toLowerCase();
                networkingLounges = networkingLounges.stream()
                        .filter(e -> contains(e.getName().toLowerCase(), lowercaseSearchStr) || (contains(e.getDescription().toLowerCase(), lowercaseSearchStr)))
                        .collect(Collectors.toList());
            }
            if (networkingLounges.isEmpty()) {
                return getMergeLoungeData(Collections.emptyList(), count);
            }
            count = networkingLounges.size();
            page = Math.max(page, 0);
            size = size < 0 ? 10 : size;
            from = page * size;
            to = from + size;
            if (to > count) {
                to = count;
            }
            if (EnumLoungeSorting.POSITION.equals(sortColumn)) {
                sortByLoungePosition(networkingLounges, isAsc);
                networkingLounges = networkingLounges.subList(from, to);
            }

            if (EnumLoungeSorting.NO_OF_MEMBERS.equals(sortColumn)) {
                log.info("Fetching lounge members count and user information which are connected with lounge of event {} , LoungesSize {}", event.getEventId(), networkingLounges.size());
                List<NetworkingLoungesAndMembersDto> networkingLoungesAndMembersDto = neptuneNetworkingLoungeService.getNetworkingLoungeMembersCountAndUserId(networkingLounges, event, user.getUserId());
                log.info("Prepare the lounge data with members count and user details for eventId {} , networkingLoungesAndMembersSize {}", event.getEventId(), networkingLoungesAndMembersDto.size());
                networkingLoungesWithStatusAndMemberDto = mergeLoungeAndMembersData(filteredLoungesByTabAndTags, networkingLoungesAndMembersDto, eventAdminIdList, event.getEquivalentTimeZone());
            } else {
                networkingLoungesWithStatusAndMemberDto = mergeLoungesWithNetworkingLounges(filteredLoungesByTabAndTags, networkingLounges, eventAdminIdList, event.getEquivalentTimeZone());
            }

            log.info("Sorting the Lounges Data for eventId {} , networkingLoungesWithStatusAndMemberSize {}", event.getEventId(), networkingLoungesWithStatusAndMemberDto.size());
            networkingLoungesWithStatusAndMemberDto = sortByColumnName(networkingLoungesWithStatusAndMemberDto, sortColumn, isAsc, user.getUserId(), event);
            log.info("Accessing elements with Parameters from {} , to {} , count {}", from, to, count);

            if (from > count) {
                log.info("Returning empty list because of accessing elements more then the present from {} to {} count {}", from, to, count);
                return getMergeLoungeData(Collections.emptyList(), count);
            }
        } catch (Exception ex) {
            log.error("Error while getting All lounges EventId {} UserId {} ErrorMsg {}", event.getEventId(), user.getUserId(), ex.getMessage());
        }
        return getMergeLoungeData(count > 0 ? (EnumLoungeSorting.POSITION.equals(sortColumn) ? networkingLoungesWithStatusAndMemberDto : networkingLoungesWithStatusAndMemberDto.subList(from, to))
                : Collections.emptyList(), count);
    }



    private DataTableResponse getMergeLoungeData(List<NetworkingLoungesWithStatusAndMemberDto> networkingLoungesWithStatusAndMemberDto, int count) {
        DataTableResponse dataTableResponse = new DataTableResponse();
        if (networkingLoungesWithStatusAndMemberDto != null) {
            dataTableResponse.setData(networkingLoungesWithStatusAndMemberDto);
            dataTableResponse.setRecordsTotal(count);
            dataTableResponse.setRecordsFiltered(networkingLoungesWithStatusAndMemberDto.size());
        }
        return dataTableResponse;
    }

    private DataTableResponse getData(List<NetworkingLounge> networkingLounges, String searchStr) {
        DataTableResponse dataTableResponse = new DataTableResponse();
        if (networkingLounges != null) {
            long totalRecord = networkingLounges.size();
            if (isNotBlank(searchStr)) {
                String lowercaseSearchStr = searchStr.toLowerCase();
                networkingLounges = networkingLounges.stream()
                        .filter(e -> contains(e.getName().toLowerCase(), lowercaseSearchStr) || (contains(e.getDescription().toLowerCase(), lowercaseSearchStr)))
                        .collect(Collectors.toList());
            }
            networkingLounges = networkingLounges.stream().sorted(Collections.reverseOrder(Comparator.comparingDouble(NetworkingLounge::getPosition))).collect(Collectors.toList());
            dataTableResponse.setData(networkingLounges);
            dataTableResponse.setRecordsTotal(totalRecord);
            dataTableResponse.setRecordsFiltered(networkingLounges.size());
        }
        return dataTableResponse;
    }

    @Override
    public NetworkingLounge getNetworkingLoungeById(String id) {
        NetworkingLounge netLounge = neptuneNetworkingLoungeService.getNetworkingLounge(id);
        if (netLounge == null) {
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
        }
        return netLounge;
    }

    @Override
    public ResponseDto joinNetworkingLounge(User user, Event event, String neptuneLoungeId, boolean isAdminOrStaff) {
        log.info("Join Lounge with LoungeId {} , UserId {} , EventId {}", neptuneLoungeId, user.getUserId(), event.getEventId());
        Lounges lounges = loungesRepository.findByNeptuneLoungeId(neptuneLoungeId).orElseThrow(()-> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_NOT_FOUND));
        boolean adminOrHigherRole = Permissions.ADMIN_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(user, event));
        if(!adminOrHigherRole) {
            checkLoungeAttendeeCapacity(event, lounges, user);
        }
        if (!isAdminOrStaff || !adminOrHigherRole) {
            boolean flag = true;
            if (sessionSpeakerService.isUserSpeakerInSessionByEventId(user.getUserId(), event.getEventId()) || !Permissions.ADMIN_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(user, event))) {
                List<Long> userTicketTypesWithOutInPersonEvent = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user);
                if (CollectionUtils.isEmpty(userTicketTypesWithOutInPersonEvent)) {
                    flag = false;
                }
            }
            List<Long> userTicketTypes = eventTicketsRepoService.getAllEventTicketTypeIdsWithoutLoungeRestrictedTicketsByEventUserANDNotCanceled(event, user);
            if (CollectionUtils.isEmpty(userTicketTypes) && flag) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.TICKET_DOES_NOT_ALLOW_LOUNGES);
            }
        }
        if (roUserService.isSuperAdminUser(user) && !roVirtualEventService.isPortalAccessibleByUser(user, event)) {
            return new ResponseDto(Constants.SUCCESS, Constants.NETWORKING_LOUNGE_JOINED_MSG);
        }
        neptuneNetworkingLoungeService.joinNetworkingLounge(String.valueOf(user.getUserId()), String.valueOf(event.getEventId()), neptuneLoungeId);
        return new ResponseDto(Constants.SUCCESS, Constants.NETWORKING_LOUNGE_JOINED_MSG);

    }

    @Override
    public DataTableResponse getAllConnectedAttendeeById(String id, Event event, User user, int page, int size, String searchString) {
        List<String> roles = roStaffService.getAllUserRoles(event.getEventId(), user.getUserId());
        if (roUserService.isSuperAdminUser(user) || !roles.isEmpty()) {
            long from = page * size;
            long to = from + size;
            getNetworkingLoungeById(id);
            return attendeeProfileService.getDataWithoutEmail(neptuneNetworkingLoungeService.getConnectedMembers(id, String.valueOf(event.getEventId()), String.valueOf(user.getUserId()), from, to), searchString, event.getEventId());
        } else {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_PART_OF_THE_EVENT_ADMIN);
        }
    }

    @Override
    public MuxDirectUploadIdAndUrlDto createUploadUrlForNetworkingLoungeVideo() {
        MuxDirectUploadIdAndUrlDto muxDirectUploadIdAndUrlDto = muxService.createDirectUploadUrl(false, EMPTY_JSON_BODY);
        log.info("Created upload url for Networking Lounge, upload details {}", muxDirectUploadIdAndUrlDto);
        return muxDirectUploadIdAndUrlDto;
    }

    @Override
    @Transactional
    public void networkingLoungeVideoUploadIdAndFile(String loungeId, NetworkingLoungeVideoDetailsDto dto, Event event, Long userId) {
        String assetIdByUploadId = muxService.getAssetIdByDirectUploadId(dto.getUploadId());
        log.info("Networking Lounge video assetId : {}", assetIdByUploadId);
        String playBackId = muxService.getPlayBackIdByAssetId(assetIdByUploadId);
        log.info("Networking Lounge video playbackId : {}", playBackId);

        muxLivestreamAssetService.storeLivestreamAssetDetailsForNetworkingLounge(loungeId, assetIdByUploadId, playBackId, event.getEventId(), userId, dto);
    }

    @Override
    public List<NetworkingLoungeVideoDto> getAllLiveStreamAssetsForNetworkingLounge(String loungeId) {
        getNetworkingLoungeById(loungeId);
        List<NetworkingLoungeVideoDto> networkingLoungeVideoDtos = muxLivestreamAssetService.findAllLoungeVideoByNetworkingLoungeId(loungeId);
        if (!CollectionUtils.isEmpty(networkingLoungeVideoDtos)) {
            for (NetworkingLoungeVideoDto loungeVideoDto : networkingLoungeVideoDtos) {
                NetworkingLoungeVideoDetails networkingLoungeVideoDetails = networkingLoungeVideoDetailsRepo.findByAssetId(loungeVideoDto.getId());
                if (networkingLoungeVideoDetails != null) {
                    loungeVideoDto.setVideoName(networkingLoungeVideoDetails.getVideoLabel());
                    loungeVideoDto.setDescription(networkingLoungeVideoDetails.getShortDescription());
                }
            }
        }
        log.info("Get all networking lounge videos muxAssetDTOS {}", networkingLoungeVideoDtos);
        return networkingLoungeVideoDtos;
    }


    @Override
    public void uploadNetworkingLoungeImage(String loungeId, String croppedImgUrl, boolean isProfileImage, Long userId, boolean isBannerImage) {
        NetworkingLounge networkingLounge = getNetworkingLoungeById(loungeId);
        if (isProfileImage) {
            networkingLounge.setProfileImage(croppedImgUrl);
        } else if (isBannerImage) {
            networkingLounge.setBannerImage(croppedImgUrl);
        } else {
            String croppedImgUrlWithUserId = getCroppedImgUrlWithUserId(croppedImgUrl, userId);
            String photosUrl = StringUtils.isBlank(networkingLounge.getPhotos()) ? croppedImgUrlWithUserId : networkingLounge.getPhotos() + Constants.STRING_COMMA + croppedImgUrlWithUserId;
            networkingLounge.setPhotos(photosUrl);
        }
        neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, loungeId, networkingLounge.getCreatedBy(), networkingLounge.getEventId());
    }

    private String getCroppedImgUrlWithUserId(String croppedImgUrl, Long userId) {
        return userId + Constants.STRING_UNDERSCORE + croppedImgUrl;
    }

    @Override
    public DataTableResponse getAllImagesForNetworkingLounge(String id) {
        NetworkingLounge networkingLounge = getNetworkingLoungeById(id);
        List<String> photosUrlList = StringUtils.isEmpty(networkingLounge.getPhotos()) ? Collections.emptyList() : GeneralUtils.convertCommaSeparatedToList(networkingLounge.getPhotos());

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(photosUrlList.stream().map(url -> new KeyValueDto(removeUserIdFromPhotosUrl(url), getUserIdFromPhotosUrl(url))).collect(Collectors.toList()));
        dataTableResponse.setRecordsTotal(photosUrlList.size());
        dataTableResponse.setRecordsFiltered(photosUrlList.size());
        return dataTableResponse;
    }

    private String getUserIdFromPhotosUrl(String url) {
        return url.contains(Constants.STRING_UNDERSCORE) ? url.substring(0, url.indexOf(Constants.STRING_UNDERSCORE)) : Constants.STRING_EMPTY;
    }

    private String removeUserIdFromPhotosUrl(String url) {
        return url.contains(Constants.STRING_UNDERSCORE) ? url.substring(url.indexOf(Constants.STRING_UNDERSCORE) + 1) : url;
    }

    @Override
    public void removeNetworkingLoungePhoto(String id, String photoUrl, Long userId, boolean isAdmin) {
        NetworkingLounge networkingLounge = getNetworkingLoungeById(id);
        List<String> photosUrlList = StringUtils.isEmpty(networkingLounge.getPhotos()) ? Collections.emptyList() : GeneralUtils.convertCommaSeparatedToList(networkingLounge.getPhotos());

        if (!photosUrlList.isEmpty()) {
            Optional<String> pUrl = photosUrlList.stream().filter(u -> u.contains(photoUrl)).findFirst();
            if (pUrl.isPresent()) {
                String url = pUrl.get();
                if (!isAdmin && (!url.contains(Constants.STRING_UNDERSCORE) || !url.substring(0, url.indexOf(Constants.STRING_UNDERSCORE)).equals(userId.toString()))) {
                    throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_PHOTO);
                }
                photosUrlList.remove(url);
                networkingLounge.setPhotos(GeneralUtils.convertListToCommaSeparated(photosUrlList));
                neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, id, networkingLounge.getCreatedBy(), networkingLounge.getEventId());
            }
        }
    }

    @Override
    @Transactional
    public void removeNetworkingLoungeVideo(Long muxLiveStreamAssetId, long userId, boolean isAdmin) {
        muxLivestreamAssetService.removeNetworkingLoungeVideo(muxLiveStreamAssetId, userId, isAdmin);
    }

    @Override
    public void leaveNetworkingLounge(Long userId, long eventId, String loungeId) {
        neptuneNetworkingLoungeService.leaveNetworkingLounge(String.valueOf(userId), String.valueOf(eventId), loungeId);
    }

    @Override
    public MeetingAttendeeDto joinLoungeMeeting(String loungeId, Event event, User user, boolean isAdmin) {
        NetworkingLounge networkingLounge = getNetworkingLoungeById(loungeId);
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.setFieldNamingPolicy(FieldNamingPolicy.UPPER_CAMEL_CASE);
        Gson gson = gsonBuilder.create();

        MeetingAttendeeDto meetingAndJoinAttendee = null;
        List<Long> eventAdmins = staffService.findAllEventAdminId(event);
        if (com.cloudinary.utils.StringUtils.isBlank(networkingLounge.getMeetingObj())) {
            meetingAndJoinAttendee = chimeService.createNetworkingLoungeAndJoinAttendee(loungeId, user, event);
            networkingLounge.setMeetingObj(meetingAndJoinAttendee.getMeeting());
            meetingAndJoinAttendee.setTitle(networkingLounge.getName());
            neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, loungeId, networkingLounge.getCreatedBy(), String.valueOf(event.getEventId()));
            meetingAndJoinAttendee.setEventAdmins(eventAdmins);

        } else {
            try {
                String meetingStr = networkingLounge.getMeetingObj();
                Meeting meeting = gson.fromJson(meetingStr, Meeting.class);
                meetingAndJoinAttendee = chimeService.joinAttendee(user, meeting.getMeetingId());
                networkingLounge.setMeetingObj(meetingAndJoinAttendee.getMeeting());
                meetingAndJoinAttendee.setTitle(networkingLounge.getName());
                meetingAndJoinAttendee.setEventAdmins(eventAdmins);

            } catch (com.amazonaws.services.chimesdkmeetings.model.NotFoundException ne) { //NOSONAR
                log.info("The meeting has ended", ne);
                if ((ne.getErrorMessage().equalsIgnoreCase("The meeting has ended") || ne.getErrorCode().equalsIgnoreCase("NotFoundException"))) {
                    meetingAndJoinAttendee = chimeService.createNetworkingLoungeAndJoinAttendee(loungeId, user, event);
                    networkingLounge.setMeetingObj(meetingAndJoinAttendee.getMeeting());
                    meetingAndJoinAttendee.setTitle(networkingLounge.getName());
                    neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, loungeId, networkingLounge.getCreatedBy(), String.valueOf(event.getEventId()));
                    meetingAndJoinAttendee.setEventAdmins(eventAdmins);
                } else {
                    throw new NotAcceptableException(ne);
                }
            } catch (LimitExceededException ex) {
                log.info("The meeting limit exceed {} loungeId {}", ex, loungeId);
                throw new NotAcceptableException("4060012", ex.getErrorMessage(), ex.getErrorMessage());
            } catch (Exception e) {
                throw new NotAcceptableException(e);
            }
        }
        Attendee attendee = gson.fromJson(meetingAndJoinAttendee.getAttendee(), Attendee.class);
        Meeting meeting = gson.fromJson(meetingAndJoinAttendee.getMeeting(), Meeting.class);
        boolean isAdminStaffOrSuperAdmin = isAdmin || user.getUserId().toString().equals(networkingLounge.getCreatedBy());
        List<JSONObject> chimeMeetingAttendees = chimeMeetingGraphQLHandler.getChimeMeetingAttendees(loungeId);
        Boolean attendeesAllow = networkingLoungesSettingRepo.isAttendeesAllow(event.getEventId());
        if (attendeesAllow && !isAdminStaffOrSuperAdmin) {
            loungesRepository.findByNeptuneLoungeId(loungeId).ifPresent(lounges -> {
                if (lounges.getVideoOccupancy() != null && lounges.getVideoOccupancy() > 0 && chimeMeetingAttendees.size() >= lounges.getVideoOccupancy()) {
                    throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_ATTENDEE_NOT_ALLOWED_JOINED);
                }
            });

        }
        chimeMeetingGraphQLHandler.handleCreateChimeMeetingAttendeeWithAsync(meeting.getMeetingId(), attendee.getAttendeeId(), attendee.getExternalUserId(), loungeId, event.getEventId(), isAdminStaffOrSuperAdmin);
        return meetingAndJoinAttendee;
    }

    @Override
    public void updateLoungePosition(String id, String topLougeId, String topBottomLougeId, Event event) {
        NetworkingLounge networkingLounge = getNetworkingLoungeById(id);
        updateWithSequence(networkingLounge, topLougeId, topBottomLougeId, event);
    }

    private void updateWithSequence(NetworkingLounge networkingLounge, String topLougeId, String topBottomLougeId, Event event) {
        NetworkingLounge topLounge = neptuneNetworkingLoungeService.getNetworkingLounge(topLougeId);
        NetworkingLounge topNextLounge = neptuneNetworkingLoungeService.getNetworkingLounge(topBottomLougeId);
        List<NetworkingLounge> networkingLoungeList = neptuneNetworkingLoungeService.getEventNetworkingLounges(event);

        //move to middle
        if (null != topLounge && null != topNextLounge) {
            double position = (topNextLounge.getPosition() + topLounge.getPosition()) / 2;
            if (1 == position) {
                NetworkingLounge nextPositionLounge = getNextPositionItem(networkingLounge, networkingLoungeList);
                NetworkingLounge prevPositionLounge = getPreviousPositionItem(networkingLounge, networkingLoungeList);
                if (nextPositionLounge != null && prevPositionLounge != null) {
                    double positiondifferent = (nextPositionLounge.getPosition() - prevPositionLounge.getPosition())
                            / 2;
                    List<NetworkingLounge> filterLounges = networkingLoungeList.stream().filter(nl -> nl.getPosition() > topLounge.getPosition()
                            && nl.getPosition() < networkingLounge.getPosition()).collect(Collectors.toList());
                    updatePositionNetworkingLouge(filterLounges, positiondifferent);
                    networkingLounge.setPosition(topNextLounge.getPosition());
                    neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, networkingLounge.getId(), networkingLounge.getCreatedBy(), networkingLounge.getEventId());
                }
            } else {
                networkingLounge.setPosition(position);
                neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, networkingLounge.getId(), networkingLounge.getCreatedBy(), networkingLounge.getEventId());
            }
            //move to top
        } else if (null == topLounge && null != topNextLounge) {
            double updatedPosition = topNextLounge.getPosition() + sequence;
            networkingLounge.setPosition(updatedPosition);
            neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, networkingLounge.getId(), networkingLounge.getCreatedBy(), networkingLounge.getEventId());
            //move at last
        } else if (null != topLounge) {
            double posDiff = topLounge.getPosition() - sequence;
            if (posDiff <= 1) {
                updatePositionNetworkingLouge(networkingLoungeList, sequence);
            }
            networkingLounge.setPosition(sequence);
            neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, networkingLounge.getId(), networkingLounge.getCreatedBy(), networkingLounge.getEventId());
        }
    }

    private void updatePositionNetworkingLouge(List<NetworkingLounge> networkingLoungeList, double positiondifferent) {
        networkingLoungeList.stream().forEach(nl -> {
            double updatedPosition = nl.getPosition() + positiondifferent;
            nl.setPosition(updatedPosition);
            neptuneNetworkingLoungeService.updateNetworkingLounge(nl, nl.getId(), nl.getCreatedBy(), nl.getEventId());
        });
    }

    private NetworkingLounge getPreviousPositionItem(NetworkingLounge networkingLounge, List<NetworkingLounge> networkingLoungeList) {
        Optional<NetworkingLounge> networkingLoungeOptional = networkingLoungeList.stream().filter(
                nl -> nl.getPosition() < networkingLounge.getPosition()).sorted(Collections.reverseOrder(
                Comparator.comparingDouble(NetworkingLounge::getPosition))).findFirst();
        return networkingLoungeOptional.isPresent() ? networkingLoungeOptional.get() : null;
    }

    private NetworkingLounge getNextPositionItem(NetworkingLounge networkingLounge, List<NetworkingLounge> networkingLoungeList) {
        Optional<NetworkingLounge> networkingLoungeOptional = networkingLoungeList.stream()
                .filter(nl -> nl.getPosition() > networkingLounge.getPosition())
                .sorted(Comparator.comparingDouble(NetworkingLounge::getPosition)).findFirst();
        return networkingLoungeOptional.isPresent() ? networkingLoungeOptional.get() : null;
    }

    @Override
    public List<NetworkingLounge> findByEvent(Event event) {
        return neptuneNetworkingLoungeService.getEventNetworkingLounges(event);
    }

    @Override
    public List<AttendeeProfileDto> getAllLoungeAttendeeProfileByEvent(Event event) {
        List<AttendeeProfileDto> allAttendee = new ArrayList<>();
        List<NetworkingLounge> networkingLounges = findByEvent(event);
        networkingLounges.stream().forEach(networkingLounge -> {
            List<AttendeeProfileDto> connectedAttendees = neptuneNetworkingLoungeService.getConnectedAttendees(networkingLounge.getId(), event.getEventId());
            if (!connectedAttendees.isEmpty()) {
                allAttendee.addAll(connectedAttendees);
            }
        });
        return allAttendee;
    }

    @Override
    public DataTableResponse getAllNetworkingLoungeDataForAnalytics(Event event) {
        return getDataWithAttendeeCount(neptuneNetworkingLoungeService.getEventNetworkingLounges(event));
    }

    @Override
    public List<User> getAllConnectedAttendeeByLoungeAndEventId(String loungeId, long eventId) {
        log.info("getAllConnectedAttendeeByLoungeAndEventId for loungeId {} and eventId {}", loungeId, eventId);
        List<AttendeeProfileDto> attendeeProfileDto = neptuneNetworkingLoungeService.getConnectedAttendees(loungeId, eventId, new String[]{"userId"});
        List<Long> userIds = attendeeProfileDto.stream().map(AttendeeProfileDto::getUserId).collect(Collectors.toList());
        List<User> users = new ArrayList<>();
        if (null != userIds && !userIds.isEmpty()) {
            users = roUserService.getListOfUsersByUserIds(userIds);
        }
        return users;
    }

    private DataTableResponse getDataWithAttendeeCount(List<NetworkingLounge> eventNetworkingLounges) {
        DataTableResponse dataTableResponse = new DataTableResponse();
        List<NetworkingLoungeAnalyticsDto> analyticsDtoList = new ArrayList<>();
        if (null != eventNetworkingLounges && !eventNetworkingLounges.isEmpty()) {
            log.info("In getDataWithAttendeeCount lounge count {}", eventNetworkingLounges.size());
            eventNetworkingLounges.forEach(networkingLounge -> {
                NetworkingLoungeAnalyticsDto networkingLoungeAnalyticsDto = new NetworkingLoungeAnalyticsDto(networkingLounge.getId(), networkingLounge.getEventId(), networkingLounge.getName(), networkingLounge.getCreateAt());
                networkingLoungeAnalyticsDto.setAttendeeCount(neptuneNetworkingLoungeService.getAttendeeCountByLoungeIdAndEventId(networkingLounge.getId(), networkingLounge.getEventId()));
                networkingLoungeAnalyticsDto.setPhotoCount(!StringUtils.isEmpty(networkingLounge.getPhotos()) ? Arrays.stream((networkingLounge.getPhotos()).split(Constants.STRING_COMMA)).count() : 0);
                networkingLoungeAnalyticsDto.setVideoCount(muxLivestreamAssetService.findAllByNetworkingLoungeId(networkingLounge.getId()).stream().count());
                analyticsDtoList.add(networkingLoungeAnalyticsDto);
            });
            dataTableResponse.setData(analyticsDtoList);
            dataTableResponse.setRecordsTotal(eventNetworkingLounges.size());
            dataTableResponse.setRecordsFiltered(eventNetworkingLounges.size());

            return dataTableResponse;
        } else {
            throw new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_DATA_NOT_FOUND);
        }
    }

    @Override
    public Map<Long, List<String>> getPhotosMapByLoungeId(String loungeId, String eventId) {
        log.info("Get details of images which is uploaded in loungeId {} and eventId {}", loungeId, eventId);
        NetworkingLounge networkingLounge = getNetworkingLoungeById(loungeId);
        if (networkingLounge.getEventId().equals(eventId)) {
            List<String> photosUrlList = StringUtils.isEmpty(networkingLounge.getPhotos()) ? Collections.emptyList() : GeneralUtils.convertCommaSeparatedToList(networkingLounge.getPhotos());
            Map<Long, List<String>> photoMapByUser = new HashMap<>();
            photosUrlList.forEach(url -> {
                Long userId = Long.parseLong(getUserIdFromPhotosUrl(url));
                String imageURL = removeUserIdFromPhotosUrl(url);
                List<String> imageList = null;
                if (!photoMapByUser.isEmpty() && photoMapByUser.containsKey(userId)) {
                    imageList = photoMapByUser.get(userId);
                } else {
                    imageList = new ArrayList<>();
                }
                imageList.add(imageURL);
                photoMapByUser.put(userId, imageList);
            });
            return photoMapByUser;
        } else {
            log.info("Networking lounge not found for loungeId {} and eventId {} ", loungeId, eventId);
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
        }
    }

    @Override
    public List<NetworkingLounge> findLoungesByUserIdAndEventId(String userId, Event event) {
        return neptuneNetworkingLoungeService.findLoungesByUserIdAndEventId(userId, event);
    }

    @Override
    public List<UserPrimaryInfoDto> getLoungeAttendees(String loungeId, Event event) {
        log.info("Start downloadNetworkingLoungeAttendanceReport start for networking lounge {} and eventId {}", loungeId, event.getEventId());
        List<User> usersList = getAllConnectedAttendeeByLoungeAndEventId(loungeId, event.getEventId());
        List<UserPrimaryInfoDto> userPrimaryInfoDtoList = new ArrayList<>();
        usersList.forEach(user -> {
            UserPrimaryInfoDto userInfo = new UserPrimaryInfoDto(user.getUserId(), user.getFirstName(), user.getLastName());
            userPrimaryInfoDtoList.add(userInfo);
        });
        return userPrimaryInfoDtoList;
    }

    @Override
    public List<AttendeeLounge> attendeeLoungeData(Event event, Long userId) {
        log.info("Attendee Lounges data by userId {} and eventId {}", userId, event.getEventId());
        List<NetworkingLounge> networkingLoungeList = findLoungesByUserIdAndEventId(String.valueOf(userId), event);
        List<AttendeeLounge> attendeeLoungeList = new ArrayList<>();
        if (!networkingLoungeList.isEmpty()) {
            networkingLoungeList.stream().forEach(networkingLounge -> {
                AttendeeLounge lounge = new AttendeeLounge(networkingLounge);
                attendeeLoungeList.add(lounge);
            });
        } else {
            log.error("Networking lounge not found for userId {} and eventId {} ", userId, event.getEventId());
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
        }
        return attendeeLoungeList;
    }

    @Override
    public boolean checkIfLoungeNameAlreadyExist(List<NetworkingLounge> networkingLounges, String loungeName, Event event) {
        if (networkingLounges.stream().filter(lounge -> lounge.getName().trim().replaceAll("\\s+", " ").equalsIgnoreCase(loungeName)).collect(Collectors.toList()).isEmpty()) {
            return true;
        } else {
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_WITH_SAME_NAME_ALREADY_EXIST);
        }
    }

    @Override
    public List<Object> getAllNetworkingLoungeIdsByUserId(String userId) {
        return neptuneNetworkingLoungeService.getAllLoungeIdByUserId(userId);
    }

    @Override
    public void removeNetworkingLoungePhotoByEventIds(List<Long> eventIds) {
        if (!eventIds.isEmpty()) {
            List<String> loungeIds = new ArrayList<>();
            eventIds.forEach(eventId -> loungeIds.addAll(neptuneNetworkingLoungeService.getNetworkingLoungesOfEvent(String.valueOf(eventId))));
            if (!loungeIds.isEmpty()) {
                loungeIds.forEach(this::removeNetworkingLoungePhotoById);
            }
        }
    }

    private void removeNetworkingLoungePhotoById(String loungeId) {
        NetworkingLounge networkingLounge = getNetworkingLoungeById(loungeId);
        networkingLounge.setPhotos(STRING_EMPTY);
        neptuneNetworkingLoungeService.updateNetworkingLounge(networkingLounge, loungeId, networkingLounge.getCreatedBy(), networkingLounge.getEventId());
    }

    @Override
    public void removeNetworkingLoungeVideoByEventIds(List<Long> eventIds) {
        if (!eventIds.isEmpty()) {
            List<String> loungeIds = new ArrayList<>();
            eventIds.forEach(eventId -> loungeIds.addAll(neptuneNetworkingLoungeService.getNetworkingLoungesOfEvent(String.valueOf(eventId))));
            if (!loungeIds.isEmpty()) {
                loungeIds.forEach(this::removeNetworkingLoungeVideoById);
            }
        }

    }

    private void removeNetworkingLoungeVideoById(String loungeId) {
        List<MuxAssetDTO> muxAssetDTOS = muxLivestreamAssetService.findAllByNetworkingLoungeId(loungeId);
        muxAssetDTOS.forEach(muxAssetDTO -> {
            MUXLivestreamAssetDetails muxLivestreamAssetDetails = muxLivestreamAssetService.getMUXLivestreamAssetDetails(muxAssetDTO.getId());
            log.info("Past video recordStatus set DELETE for muxLivestreamAssetDetails {} ", muxLivestreamAssetDetails.getId());
            muxLivestreamAssetDetails.setRecordStatus(RecordStatus.DELETE);
            muxLivestreamAssetService.save(muxLivestreamAssetDetails);
        });
    }

    @Override
    public void saveNetworkingLoungeDocumentAndLink(String networkingLoungeId,
                                                    NetworkingLoungeDocumentLinkDto networkingLoungeDocumentLinkDto, Event event, User user) {
        if (StringUtils.isEmpty(networkingLoungeDocumentLinkDto.getDocumentName())) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DOCUMENT_NAME_CANNOT_BE_BLANK);
        }
        NetworkingLoungeDocumentLinkDetails networkingLoungeDocumentLinkDetails = new NetworkingLoungeDocumentLinkDetails();
        networkingLoungeDocumentLinkDetails.setEvent(event);
        networkingLoungeDocumentLinkDetails.setEventId(event.getEventId());
        networkingLoungeDocumentLinkDetails.setName(networkingLoungeDocumentLinkDto.getDocumentName());
        networkingLoungeDocumentLinkDetails.setDocumentUrlOrLink(networkingLoungeDocumentLinkDto.getDocumentUrlOrLink());
        networkingLoungeDocumentLinkDetails.setDocumentSize(networkingLoungeDocumentLinkDto.getDocumentSize());
        networkingLoungeDocumentLinkDetails.setNetworkingLoungeId(networkingLoungeId);
        networkingLoungeDocumentLinkDetails.setNetworkingLoungeDocType(networkingLoungeDocumentLinkDto.getDocType());
        networkingLoungeDocumentLinkDetails.setCreatedByUser(user);
        networkingLoungeDocumentLinkDetails.setCreatedBy(user.getUserId());
        networkingLoungeDocumentLinkDetailsRepo.save(networkingLoungeDocumentLinkDetails);
    }

    @Override
    public List<NetworkingLoungeDocumentLinkDto> getNetworkingLoungeDocumentAndLink(String networkingLoungeId) {
        List<NetworkingLoungeDocumentLinkDetails> networkingLoungeDocumentLinkDetails = networkingLoungeDocumentLinkDetailsRepo.findAllByNetworkingLoungeId(networkingLoungeId);
        return !CollectionUtils.isEmpty(networkingLoungeDocumentLinkDetails)
                ? networkingLoungeDocumentLinkDetails.stream().map(NetworkingLoungeDocumentLinkDto::new).collect(Collectors.toList())
                : Collections.emptyList();
    }

    @Override
    public void updateNetworkingLoungeDocumentAndLink(String networkingLoungeId, NetworkingLoungeDocumentLinkDto networkingLoungeDocumentLinkDto, long documentId, User user, boolean isAdmin) {
        Optional<NetworkingLoungeDocumentLinkDetails> optNetworkingLoungeDocumentLinkDetails = networkingLoungeDocumentLinkDetailsRepo.findByNetworkingLoungeIdAndId(networkingLoungeId, documentId);
        if (!optNetworkingLoungeDocumentLinkDetails.isPresent()) {
            String message = NETWORKING_LOUNGE_DOCUMENT_NAME_NOT_FOUND.replace(DOCUMENT, StringUtils.lowerCase(networkingLoungeDocumentLinkDto.getDocType().name()));
            NotAcceptableException.NotAceptableExeceptionMSG documentNameNotFound = NotAcceptableException.NotAceptableExeceptionMSG.NETWORKING_LOUNGE_DOCUMENT_NAME_NOT_FOUND;
            documentNameNotFound.setErrorMessage(message);
            documentNameNotFound.setDeveloperMessage(message);
            throw new NotAcceptableException(documentNameNotFound);
        }
        NetworkingLoungeDocumentLinkDetails networkingLoungeDocumentLinkDetails = optNetworkingLoungeDocumentLinkDetails.get();
        if (!isAdmin && !networkingLoungeDocumentLinkDetails.getCreatedBy().equals(user.getUserId())) {
            String message = YOU_CAN_NOT_EDIT_NETWORKING_DOCUMENT.replace(DOCUMENT, StringUtils.lowerCase(networkingLoungeDocumentLinkDto.getDocType().name()));
            NotAcceptableException.NotAceptableExeceptionMSG canNotDeleteNetworkingDocument = NotAcceptableException.NotAceptableExeceptionMSG.YOU_CAN_NOT_EDIT_NETWORKING_DOCUMENT;
            canNotDeleteNetworkingDocument.setErrorMessage(message);
            canNotDeleteNetworkingDocument.setDeveloperMessage(message);
            throw new NotAcceptableException(canNotDeleteNetworkingDocument);
        }
        networkingLoungeDocumentLinkDetails.setNetworkingLoungeDocType(networkingLoungeDocumentLinkDto.getDocType());
        if (!networkingLoungeDocumentLinkDto.getDocumentUrlOrLink().equalsIgnoreCase(networkingLoungeDocumentLinkDetails.getDocumentUrlOrLink())) {
            s3client.delete(networkingLoungeDocumentLinkDetails.getDocumentUrlOrLink());
            networkingLoungeDocumentLinkDetails.setDocumentUrlOrLink(networkingLoungeDocumentLinkDto.getDocumentUrlOrLink());
        }
        networkingLoungeDocumentLinkDetails.setDocumentSize(networkingLoungeDocumentLinkDto.getDocumentSize());
        networkingLoungeDocumentLinkDetails.setName(networkingLoungeDocumentLinkDto.getDocumentName());
        networkingLoungeDocumentLinkDetails.setRecordStatus(RecordStatus.UPDATE);
        networkingLoungeDocumentLinkDetailsRepo.save(networkingLoungeDocumentLinkDetails);
    }

    @Override
    public String deleteNetworkingLoungeDocumentAndLink(String networkingLoungeId, long documentId, User user, boolean isAdmin) {
        Optional<NetworkingLoungeDocumentLinkDetails> optNetworkingLoungeDocumentLinkDetails = networkingLoungeDocumentLinkDetailsRepo.findByNetworkingLoungeIdAndId(networkingLoungeId, documentId);
        if (!optNetworkingLoungeDocumentLinkDetails.isPresent()) {
            String message = NETWORKING_LOUNGE_DOCUMENT_NAME_NOT_FOUND.replace(DOCUMENT, StringUtils.lowerCase(NetworkingLoungeDocType.DOCUMENT.name()));
            NotAcceptableException.NotAceptableExeceptionMSG documentNameNotFound = NotAcceptableException.NotAceptableExeceptionMSG.NETWORKING_LOUNGE_DOCUMENT_NAME_NOT_FOUND;
            documentNameNotFound.setErrorMessage(message);
            documentNameNotFound.setDeveloperMessage(message);
            throw new NotAcceptableException(documentNameNotFound);
        }
        NetworkingLoungeDocumentLinkDetails networkingLoungeDocumentLinkDetails = optNetworkingLoungeDocumentLinkDetails.get();
        if (!isAdmin && !networkingLoungeDocumentLinkDetails.getCreatedBy().equals(user.getUserId())) {
            String message = YOU_CAN_NOT_DELETE_NETWORKING_DOCUMENT.replace(DOCUMENT, StringUtils.lowerCase(networkingLoungeDocumentLinkDetails.getNetworkingLoungeDocType().name()));
            NotAcceptableException.NotAceptableExeceptionMSG canNotDeleteNetworkingDocument = NotAcceptableException.NotAceptableExeceptionMSG.YOU_CAN_NOT_DELETE_NETWORKING_DOCUMENT;
            canNotDeleteNetworkingDocument.setErrorMessage(message);
            canNotDeleteNetworkingDocument.setDeveloperMessage(message);
            throw new NotAcceptableException(canNotDeleteNetworkingDocument);
        }
        networkingLoungeDocumentLinkDetails.setRecordStatus(RecordStatus.DELETE);
        networkingLoungeDocumentLinkDetailsRepo.save(networkingLoungeDocumentLinkDetails);
        return networkingLoungeDocumentLinkDetails.getNetworkingLoungeDocType().name();
    }

    @Override
    public void updateNetworkingLoungeVideoDetails(Long playbackId, long userId, NetworkingLoungeVideoDto networkingLoungeVideoDto) {
        log.info("request received for update N/W lounge video title/description bu user {}", userId);
        NetworkingLoungeVideoDetails networkingLoungeVideoDetails = networkingLoungeVideoDetailsRepo.findByAssetId(playbackId);
        if (networkingLoungeVideoDetails != null) {
            networkingLoungeVideoDetails.setVideoLabel(networkingLoungeVideoDto.getVideoName());
            networkingLoungeVideoDetails.setShortDescription(networkingLoungeVideoDto.getDescription());
            networkingLoungeVideoDetailsRepo.save(networkingLoungeVideoDetails);
        } else {
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_VIDEO_NOT_FOUND);
        }
        log.info("successfully updated N/W lounge video title/description bu user {}", userId);
    }


    @Override
    public NetworkLoungesSettingDto getLoungesSetting(Event event) {

        NetworkLoungesSettingDto networkLoungesSettingDto = new NetworkLoungesSettingDto();
        List<LoungeTimeslot> loungeTimeslot;
        try {
            log.info(" Fetching Event lounge setting for current Event  with EventId {}", event.getEventId());
            Optional<LoungesSetting> loungesSetting = networkingLoungesSettingRepo.findByEventId(event.getEventId());
            if (loungesSetting.isPresent()) {
                networkLoungesSettingDto.setLoungeSettingDto(new LoungeSettingDto(loungesSetting.get()));
                log.info("isLoungeScheduleEnabled {} ", loungesSetting.get().getLoungeScheduleEnabled());
                if (BooleanUtils.isTrue(loungesSetting.get().getLoungeScheduleEnabled())) {
                    loungeTimeslot = loungesTimeslotRepository.findAllByEventLoungesSettingId(loungesSetting.get().getId());
                    networkLoungesSettingDto.setLoungeTimeslotDtos(loungeTimeslot.isEmpty() ? Collections.emptyList() : getLoungeTimeslotDtoFromEntity(loungeTimeslot, event.getEquivalentTimeZone()));

                }
            } else {
                log.info("No lounge setting found for current Event so creating default setting EventId {}", event.getEventId());
                LoungesSetting defaultLoungeSetting = loungesSettingWithDefaultValue(event.getEventId());
                LoungesSetting newLoungeSetting = networkingLoungesSettingRepo.save(defaultLoungeSetting);
                networkLoungesSettingDto.setLoungeSettingDto(new LoungeSettingDto(newLoungeSetting));
                networkLoungesSettingDto.setLoungeTimeslotDtos(Collections.emptyList());
            }
        } catch (Exception ex) {
            log.error("Error while getting Lounge setting EventId {}", event.getEventId());
        }
        return networkLoungesSettingDto;
    }


    public ResponseDto updateLoungesSetting(LoungeSettingDto loungeSettingDto, Event event) {

        try {
            log.info("Updating Event Lounges Setting with EventId {} , loungeSettingDto {}", event.getEventId(), loungeSettingDto);
            Optional<LoungesSetting> optionalLoungesSetting = networkingLoungesSettingRepo.findByIdAndEventId(loungeSettingDto.getId(), event.getEventId());
            if (optionalLoungesSetting.isPresent()) {
                // As of now commenting below code
                // Jira :- https://accelevents.atlassian.net/browse/DEV-30042
                /*LoungesSetting loungesSetting = optionalLoungesSetting.get();
                Long oldMaxVideoOccupancy = loungesSetting.getMaxVideoOccupancyLimit();*/
                networkingLoungesSettingRepo.save(createLoungeSettingEntity(optionalLoungesSetting.get(), loungeSettingDto));
                /*if (loungesSetting.getMaxVideoOccupancyLimit() < oldMaxVideoOccupancy) {
                    log.info("Updating video Occupancy of the lounges with loungesSettingId {} , Event Id {} , Old Video Occupancy Value {} , New Video Occupancy Value {} ", loungesSetting.getId(), loungesSetting.getEventId(), oldMaxVideoOccupancy, loungeSettingDto.getMaxVideoOccupancyLimit());
                    updateLoungesVideoOccupancy(loungesSetting.getEventId(), loungesSetting.getMaxVideoOccupancyLimit());
                }*/
            } else {
                throw new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_SETTING_NOT_FOUND);
            }

        } catch (Exception ex) {
            log.error("Error while updating Lounge setting EventId {} , loungeSettingDto {} , ErrorMsg {}", event.getEventId(), loungeSettingDto, ex.getMessage());
            return new ResponseDto(FAIL, COMMON_ERROR_MESSAGE);
        }
        return new ResponseDto(SUCCESS, NETWORK_LOUNGE_SETTING_UPDATED_SUCCESSFULLY);
    }


    @Override
    public ResponseDto updateLoungeStatus(String neptuneLoungeId, EnumLoungeStatus loungeStatus, Event event, NetworkingLoungesDto networkingLoungeDto) {
        try {
            log.info("Update lounge status with EventId {} , NeptuneLoungeId {} , LoungeStatus {}", event.getEventId(), neptuneLoungeId, loungeStatus);
            if (EnumLoungeStatus.APPROVED.equals(loungeStatus) && BooleanUtils.isTrue(isLoungesApprovedLimitReached(event))) {
                handleMaxApprovedLimitExceeded(event);
            }
            Lounges lounges = loungesRepository.findByNeptuneLoungeId(neptuneLoungeId)
                    .orElseThrow(() -> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_DATA_NOT_FOUND));
            NetworkingLounge networkLounge = neptuneNetworkingLoungeService.getNetworkingLounge(neptuneLoungeId);
            lounges.setLoungeStatus(loungeStatus.getValue());

            if (EnumLoungeStatus.DENIED.equals(loungeStatus)) {
                lounges.setStatusDeniedReason(networkingLoungeDto.getStatusDeniedReason());
            }
            loungesRepository.save(lounges);
            User user = userService.findByUserId(lounges.getCreatedBy());
            String userEmail = user.getEmail();

            if (StringUtils.isNotBlank(userEmail)) {
                sendGridMailPrepareService.sendMailToLoungeCreator(event, user, loungeStatus, networkLounge.getName(), neptuneLoungeId, networkingLoungeDto.getStatusDeniedReason());
            } else {
                throw new NotFoundException(NotFoundException.NotFound.USER_EMAIL_NOT_FOUND);
            }
            if(EnumLoungeStatus.APPROVED.equals(loungeStatus)){
                checkLoungesExistInMessageToContactsAndUpdateIt(event.getEventId(),lounges.getNeptuneLoungeId(),ADD);
            }

        } catch (NotAcceptableException | NotFoundException notAcceptableException) {
            throw notAcceptableException;
        } catch (Exception ex) {
            log.error("Error while updating the Lounge Status with EventId {} , NeptuneLoungeId {} , LoungeStatus {} and ErrorMsg {} ", event.getEventId(), neptuneLoungeId, loungeStatus, ex.getMessage());
            return new ResponseDto(FAIL, COMMON_ERROR_MESSAGE);
        }
        return new ResponseDto(SUCCESS, NETWORK_LOUNGE_STATUS_UPDATED_SUCCESSFULLY);
    }

    @Override
    public NetworkingLoungesDto getNetworkingLoungeDtoByLoungeId(User user, Event event, String id, boolean isAdminOrStaff) {
        log.info("Getting all NetworkingLoungeDto with UserId {} EventId {} NeptuneLoungeId {} ", user.getUserId(), event.getEventId(), id);
        if (!isAdminOrStaff || !Permissions.ADMIN_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(user, event))) {
            boolean flag = true;
            if (sessionSpeakerService.isUserSpeakerInSessionByEventId(user.getUserId(), event.getEventId()) || !Permissions.ADMIN_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(user, event))) {
                List<Long> userTicketTypesWithOutInPersonEvent = eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user);
                if (CollectionUtils.isEmpty(userTicketTypesWithOutInPersonEvent)) {
                    flag = false;
                }
            }
            List<Long> userTicketTypes = eventTicketsRepoService.getAllEventTicketTypeIdsWithoutLoungeRestrictedTicketsByEventUserANDNotCanceled(event, user);
            if (CollectionUtils.isEmpty(userTicketTypes) && flag) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.TICKET_DOES_NOT_ALLOW_LOUNGES);
            }
        }
        List<Long> eventAdminIdList = staffService.findAllEventAdminId(event);
        NetworkingLounge netLounge = neptuneNetworkingLoungeService.getNetworkingLounge(id);
        Optional<Lounges> optionalLounges = loungesRepository.findByNeptuneLoungeId(id);
        if (netLounge == null || !optionalLounges.isPresent()) {
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
        } else {
            return new NetworkingLoungesDto(optionalLounges.get(), netLounge, eventAdminIdList.contains(optionalLounges.get().getCreatedBy()), event.getEquivalentTimeZone());
        }
    }


    @Override
    public ResponseDto updateTimeslots(List<LoungeTimeslotDto> loungeTimeslotDto, Event event) {
        try {
            log.info("Updating Timeslot with  EventId {}", event.getEventId());
            List<LoungeTimeslot> loungeTimeslots = loungesTimeslotRepository.findByIdIn(loungeTimeslotDto.stream().map(LoungeTimeslotDto::getId).collect(Collectors.toList()));
            log.info("After fetching  Timeslot by Ids with EventId {}", event.getEventId());
            List<LoungeTimeslot> loungeTimeslot = createLoungeTimeslotEntities(loungeTimeslots, loungeTimeslotDto, event.getEquivalentTimeZone());
            loungesTimeslotRepository.saveAll(loungeTimeslot);
        } catch (Exception ex) {
            log.error("Error while updating Timeslot with EventId {} ErrorMsg {}", event.getEventId(), ex.getMessage());
            return new ResponseDto(FAIL, COMMON_ERROR_MESSAGE);
        }
        return new ResponseDto(SUCCESS, NETWORK_LOUNGE_SETTING_TIMESLOT_UPDATED_SUCCESSFULLY);
    }

    @Override
    public ResponseDto addNewTimeslotToLoungesSetting(LoungeTimeslotDto loungeTimeslotDto, Event event) {
        try {
            log.info("Adding new Timeslot to Event Lounges Setting with EventId {}", event.getEventId());
            LoungeTimeslot loungeTimeslot = createLoungeTimeslotEntity(loungeTimeslotDto, event.getEquivalentTimeZone());
            log.info("After creating Lounge Timeslot Entity with EventId {}", loungeTimeslot.getEventLoungesSettingId());
            loungesTimeslotRepository.save(loungeTimeslot);
        } catch (Exception ex) {
            log.error("Error while adding new Timeslot to the Lounges EventId {} EventsLoungesId {} ErrorMsg {}", event.getEventId(), loungeTimeslotDto.getEventLoungesSettingId(), ex.getMessage());
            return new ResponseDto(FAIL, COMMON_ERROR_MESSAGE);
        }
        return new ResponseDto(SUCCESS, NETWORK_LOUNGE_SETTING_TIMESLOT_ADDED_SUCCESSFULLY);
    }

    @Override
    public ResponseDto removeTimeSlotFromLounges(Long loungeTimeslotId) {
        log.info("Removing Timeslot from Event Lounge Setting with EventLoungesTimeslotId {}", loungeTimeslotId);
        try {
            LoungeTimeslot loungeTimeslot = loungesTimeslotRepository.findById(loungeTimeslotId)
                    .orElseThrow(() -> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_TIMESLOT_NOT_FOUND));

            loungeTimeslot.setTimeslotStatus(RecordStatus.DELETE.toString());
            loungesTimeslotRepository.save(loungeTimeslot);

        } catch (NotFoundException notFoundException) {
            throw notFoundException;
        } catch (Exception ex) {
            log.error("Removing Timeslot from Event Lounge Setting with EventLoungesTimeslotId {}", loungeTimeslotId);
            return new ResponseDto(FAIL, COMMON_ERROR_MESSAGE);
        }
        return new ResponseDto(SUCCESS, NETWORK_LOUNGE_SETTING_TIMESLOT_DELETED_SUCCESSFULLY);
    }

    public LoungesSetting loungesSettingWithDefaultValue(Long eventId) {
        log.info("Creating Lounge Setting with Default Value with EventId {}", eventId);
        return new LoungesSetting(eventId);
    }


    Lounges createLoungeEntity(EnumLoungeStatus loungeStatus, String neptuneLoungeId, NetworkingLoungesDto networkingLoungesDto, Long userId, Event event) {

        Lounges lounges = new Lounges();
        try {
            log.info("Creating Lounge Entity  with EventId {} , UserId {} , NeptuneLoungeId {} LoungeStatus {} , Tags {}, ModeratorLiveForumAt {}", event.getEventId(), userId, neptuneLoungeId, loungeStatus,networkingLoungesDto.getTags(),networkingLoungesDto.getModeratorLiveForumAt());
            lounges.setEventId(event.getEventId());
            lounges.setLoungeStatus(loungeStatus.getValue());
            lounges.setNeptuneLoungeId(neptuneLoungeId);
            lounges.setTags(!CollectionUtils.isEmpty(networkingLoungesDto.getTags()) ? networkingLoungesDto.getTags().stream()
                    .map(str -> GeneralUtils.convertCommaSeparatedToList(str).get(0).trim().toUpperCase())
                    .collect(Collectors.joining(",")) : null);
            lounges.setVideoOccupancy(networkingLoungesDto.getVideoOccupancy());
            lounges.setCreatedBy(userId);
            lounges.setModeratorLiveForumAt(StringUtils.isNotBlank(networkingLoungesDto.getModeratorLiveForumAt())?
                    getDateInUTC(networkingLoungesDto.getModeratorLiveForumAt(), event.getEquivalentTimeZone()) : null);
            lounges.setAttendeeCapacity(-1L);
        } catch (Exception ex) {
            log.error("Creating Lounge Entity with EventId {} , UserId {} , NeptuneLoungeId {} LoungeStatus {} , Tags {}, ModeratorLiveForumAt {}, ErrorMsg {} ", event.getEventId(), userId, neptuneLoungeId, loungeStatus,networkingLoungesDto.getTags(),networkingLoungesDto.getModeratorLiveForumAt(), ex.getMessage());
        }
        return lounges;

    }


    private List<NetworkingLoungesWithStatusAndMemberDto> mergeLoungeAndMembersData(List<Lounges> lounges, List<NetworkingLoungesAndMembersDto> networkingLoungesAndMembersDto, List<Long> eventAdminIdList, String equivalentTimeZone) {
        log.info("Merge Lounges and Members Data with Lounge Data Size {} , NetworkingLoungesAndMembers Size {} ,  eventAdminIdList Size {}", lounges.size(), networkingLoungesAndMembersDto.size(), eventAdminIdList.size());
        return networkingLoungesAndMembersDto.stream()
                .map(p -> {
                    Lounges n;
                    n = lounges.stream()
                            .filter(m -> m.getNeptuneLoungeId().equalsIgnoreCase(p.getNetworkingLounge().getId()))
                            .findFirst()
                            .orElse(null);
                    return n != null ? new NetworkingLoungesWithStatusAndMemberDto(
                            new LoungesDto(n, eventAdminIdList.contains(n.getCreatedBy()), equivalentTimeZone), p
                    ) : null;

                }).collect(Collectors.toList());

    }

    private List<NetworkingLoungesWithStatusAndMemberDto> mergeLoungesWithNetworkingLounges(
            List<Lounges> lounges,
            List<NetworkingLounge> networkingLounges,
            List<Long> eventAdminIdList,
            String equivalentTimeZone
    ) {
        log.info("Merge Lounges and NetworkingLounges Data with Lounge Data Size {} , networkingLounges Size {} , eventAdminIdList Size {}",
                lounges.size(), networkingLounges.size(), eventAdminIdList.size());

        Map<String, NetworkingLounge> loungeIdToNetworkingLounge = networkingLounges.stream()
                .collect(Collectors.toMap(NetworkingLounge::getId, nl -> nl));

        return lounges.stream()
                .map(lounge -> {
                    NetworkingLounge networkingLounge = loungeIdToNetworkingLounge.get(lounge.getNeptuneLoungeId());
                    if (networkingLounge == null) return null;
                    LoungesDto loungesDto = new LoungesDto(lounge, eventAdminIdList.contains(lounge.getCreatedBy()), equivalentTimeZone);
                    // NetworkingLoungesAndMembersDto is null or empty (since no members)
                    return new NetworkingLoungesWithStatusAndMemberDto(loungesDto, new NetworkingLoungesAndMembersDto(networkingLounge));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    List<LoungeTimeslotDto> getLoungeTimeslotDtoFromEntity(List<LoungeTimeslot> loungeTimeslots, String equivalentTimeZone) {
        log.info("Getting lounge timeslot Dto from  LoungeTimeslot entity");
        return loungeTimeslots.stream().map(k -> createLoungeTimeslotDtoFromEntity(k, equivalentTimeZone)).collect(Collectors.toList());
    }

    LoungesSetting createLoungeSettingEntity(LoungesSetting loungesSetting, LoungeSettingDto loungeSettingDto) {
        try {
            log.info("Creating Lounge Setting Entity from LoungeSettingDto");
            loungesSetting.setId(loungeSettingDto.getId());
            loungesSetting.setEventId(loungeSettingDto.getEventId());
            loungesSetting.setLoungeScheduleEnabled(loungeSettingDto.getLoungeScheduleEnabled());
            loungesSetting.setLimitApprovedEnabled(loungeSettingDto.getLimitApprovedEnabled());
            loungesSetting.setMaxApprovedLimit(loungeSettingDto.getMaxApprovedLimit());
            loungesSetting.setReqAcceptable(loungeSettingDto.getReqAcceptable());
            loungesSetting.setAttendeesAllow(loungeSettingDto.getAttendeesAllow());
            loungesSetting.setMaxVideoOccupancyLimit(loungeSettingDto.getMaxVideoOccupancyLimit());
            loungesSetting.setAllowTextBasedLounges(loungeSettingDto.getAllowTextBasedLounges());
        } catch (Exception ex) {
            log.error("Error while creating Lounge Setting Entity ErrorMsg {}", ex.getMessage());
        }
        return loungesSetting;
    }

    List<LoungeTimeslot> createLoungeTimeslotEntities(List<LoungeTimeslot> loungeTimeslot, List<LoungeTimeslotDto> loungeSettingDto, String equivalentTimeZone) {
        log.info("Creating Lounge Timeslot Entities with EventLoungesSettingId {}", loungeTimeslot.get(0).getEventLoungesSettingId());
        return loungeTimeslot.stream().map(m -> createLoungeTimeslotEntity(m, loungeSettingDto.stream().filter(l -> l.getId() == m.getId()).collect(Collectors.toList()).get(0), equivalentTimeZone)).collect(Collectors.toList());

    }

    LoungeTimeslot createLoungeTimeslotEntity(LoungeTimeslot loungeTimeslot, LoungeTimeslotDto loungeTimeslotDto, String equivalentTimeZone) {

        try {
            log.info("Creating Lounge Timeslot entity");
            loungeTimeslot.setId(loungeTimeslotDto.getId());
            loungeTimeslot.setEventLoungesSettingId(loungeTimeslotDto.getEventLoungesSettingId());
            loungeTimeslot.setStartDate(getDateInUTC(loungeTimeslotDto.getStartDate(), equivalentTimeZone));
            loungeTimeslot.setEndDate(getDateInUTC(loungeTimeslotDto.getEndDate(), equivalentTimeZone));
        } catch (Exception ex) {
            log.error("Creating Lounge Timeslot entity ErrorMsg {}", ex.getMessage());
        }

        return loungeTimeslot;
    }

    LoungeTimeslot createLoungeTimeslotEntity(LoungeTimeslotDto loungeTimeslotDto, String equivalentTimeZone) {
        LoungeTimeslot loungeTimeslot = new LoungeTimeslot();
        try {
            log.info("Creating Event Lounge Timeslot with EventLoungeSettingId {} ", loungeTimeslotDto.getEventLoungesSettingId());
            loungeTimeslot.setId(loungeTimeslotDto.getId() != null ? loungeTimeslotDto.getId() : 0L);
            loungeTimeslot.setEventLoungesSettingId(loungeTimeslotDto.getEventLoungesSettingId());
            loungeTimeslot.setStartDate(getDateInUTC(loungeTimeslotDto.getStartDate(), equivalentTimeZone));
            loungeTimeslot.setEndDate(getDateInUTC(loungeTimeslotDto.getEndDate(), equivalentTimeZone));
        } catch (Exception ex) {
            log.error("Creating Event Lounge Timeslot with EventLoungeSettingId {} , ErrorMsg {} ", loungeTimeslotDto.getEventLoungesSettingId(), ex.getMessage());
        }

        return loungeTimeslot;
    }

    @Override
    public Map<String, Integer> getTagsWithCount(Event event, EnumLoungeTab tab, User user) {
        List<String> allEventTags;
        Map<String, Integer> tagsCountMap = null;
        try {
            log.info("Get Tags Filter with EventId {} , UserId {}  Tab {} ", event.getEventId(), user.getUserId(), tab.getValue());
            if (roStaffService.hasHostAccessForEvent(user, event)) {
                switch (tab) {
                    case ALL_LOUNGES:
                        allEventTags = loungesRepository.findAllTagsByEventId(event.getEventId());
                        break;
                    case MY_LOUNGES:
                        allEventTags = loungesRepository.findAllTagsByEventIdAndCreatedBy(event.getEventId(), user.getUserId());
                        break;
                    default:
                        allEventTags = loungesRepository.findAllTagsByEventIdAndLoungeStatus(event.getEventId(), tab.getValue());
                        break;


                }
            } else {
                switch (tab) {
                    case ALL_LOUNGES:
                        if (BooleanUtils.isTrue(networkingLoungesSettingRepo.isAttendeesAllow(event.getEventId()))) {
                            allEventTags = loungesRepository.findAllTagsByEventIdAndCreatedByOrLoungeStatus(event.getEventId(), user.getUserId(), EnumLoungeTab.APPROVED.getValue());
                        } else {
                            allEventTags = loungesRepository.findAllTagsByEventIdAndLoungeStatus(event.getEventId(), EnumLoungeTab.APPROVED.getValue());
                        }
                        break;
                    case MY_LOUNGES:
                        allEventTags = loungesRepository.findAllTagsByEventIdAndCreatedBy(event.getEventId(), user.getUserId());
                        break;
                    default:
                        allEventTags = Collections.emptyList();
                        break;


                }

            }
            log.info("After Fetching ALl Tags by Tab");

            List<String> tagsList = new ArrayList<>();
            if (allEventTags != null) {
                allEventTags.forEach(m -> tagsList.addAll(GeneralUtils.convertCommaSeparatedToList(m)));

                tagsCountMap = tagsList.stream()
                        .collect(Collectors.toMap(String::new, k -> 1, Integer::sum));

                tagsCountMap = tagsCountMap.entrySet()
                        .stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .collect(
                                LinkedHashMap::new,
                                (map, entry) -> map.put(entry.getKey(), entry.getValue()),
                                LinkedHashMap::putAll
                        );

            }
        } catch (Exception ex) {
            log.error("Error While fetching Tags EventId {} Tab {}  UserId {} ErrorMsg {}", event.getEventId(), tab, user.getUserId(), ex.getMessage());
        }
        return tagsCountMap;
    }

    public List<Lounges> getAllLoungesFromTabAndTags(boolean isAdminOrAbove, EnumLoungeTab tab, List<String> tags, Long userId, Long eventId) {

        List<Lounges> loungesList = null;
        boolean noTagFlag = tags.isEmpty();
        String tagsRegex = "";
        try {
            log.info("Get all lounges by tab and tags with isAdminOrStaff {} tab {} tags {} userId {} eventId {}", isAdminOrAbove, tab, tags, userId, eventId);
            if (!noTagFlag) {
                tagsRegex = generateRegexForTags(tags);
                log.info("Generated Regex for the given tags {} are RegexTags {} ", tags, tagsRegex);
            }
            if (isAdminOrAbove) {
                switch (tab) {
                    case ALL_LOUNGES:
                        loungesList = noTagFlag ? loungesRepository.findAllByEventId(eventId) : loungesRepository.findAllByEventIdAndTags(eventId, tagsRegex);
                        break;
                    case MY_LOUNGES:
                        loungesList = noTagFlag ? loungesRepository.findAllByEventIdAndCreatedBy(eventId, userId) : loungesRepository.findAllByEventIdAndCreatedByAndTags(eventId, userId, tagsRegex);
                        break;
                    default:
                        loungesList = noTagFlag ? loungesRepository.findAllByEventIdAndLoungeStatus(eventId, tab.getValue()) : loungesRepository.findAllByEventIdAndLoungeStatusAndTags(eventId, tab.getValue(), tagsRegex);
                        break;


                }
            } else {
                switch (tab) {
                    case ALL_LOUNGES:
                        Boolean isAttendeesAllow = networkingLoungesSettingRepo.isAttendeesAllow(eventId);
                        if (BooleanUtils.isTrue(isAttendeesAllow) && !noTagFlag) {
                            loungesList = loungesRepository.findAllByEventIdAndLoungeStatusOrCreatedByAndTags(eventId, EnumLoungeTab.APPROVED.getValue(), userId, tagsRegex);
                        } else if (BooleanUtils.isTrue(isAttendeesAllow)) {
                            loungesList = loungesRepository.findAllByEventIdAndLoungeStatusOrCreatedBy(eventId, EnumLoungeTab.APPROVED.getValue(), userId);
                        } else if (!noTagFlag) {
                            loungesList = loungesRepository.findAllByEventIdAndLoungeStatusAndTags(eventId, EnumLoungeTab.APPROVED.getValue(), tagsRegex);
                        } else {
                            loungesList = loungesRepository.findAllByEventIdAndLoungeStatus(eventId, EnumLoungeTab.APPROVED.getValue());
                        }
                        break;
                    case MY_LOUNGES:
                        loungesList = noTagFlag ? loungesRepository.findAllByEventIdAndCreatedBy(eventId, userId) :
                                loungesRepository.findAllByEventIdAndCreatedByAndTags(eventId, userId, tagsRegex);
                        break;

                    default:
                        loungesList = Collections.emptyList();
                        break;

                }


            }

        } catch (Exception ex) {
            log.error("Error while getting Lounges from tab and tags with  isAdminOrStaff {} tab {} tags {} userId {} eventId {} ErrorMsg {}", isAdminOrAbove, tab, tags, userId, eventId, ex.getMessage());

        }
        return loungesList;
    }

    private void sortByLoungePosition(List<NetworkingLounge> networkingLounges,boolean isAsc) {
        Comparator<NetworkingLounge> defaultComparator = Comparator.comparingDouble(NetworkingLounge::getPosition);
        networkingLounges.sort(isAsc ? defaultComparator : defaultComparator.reversed());
    }

    public List<NetworkingLoungesWithStatusAndMemberDto> sortByColumnName(List<NetworkingLoungesWithStatusAndMemberDto> networkingLoungesWithStatusAndMemberDto, EnumLoungeSorting sortColumn, boolean isAsc, Long userId, Event event) {
        String eventId = networkingLoungesWithStatusAndMemberDto.get(0).getNetworkingLoungesAndMembersDto().getNetworkingLounge().getEventId();
        String neptuneLoungeId = networkingLoungesWithStatusAndMemberDto.get(0).getNetworkingLoungesAndMembersDto().getNetworkingLounge().getId();
        try {
            log.info("Sorting Lounge Data with EventId {} , NeptuneLoungeId {} , sortColumn {} ", eventId, neptuneLoungeId, sortColumn);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
            Map<String, Integer> statusMap = new HashMap<>();
            statusMap.put(EnumLoungeStatus.valueOf(APPROVED).getValue(), 1);
            statusMap.put(EnumLoungeStatus.valueOf(PENDING).getValue(), 2);
            statusMap.put(EnumLoungeStatus.valueOf(DENIED).getValue(), 3);

            switch (sortColumn) {
                case NAME:
                    Comparator<NetworkingLoungesWithStatusAndMemberDto> nameComparator = Comparator.comparing((NetworkingLoungesWithStatusAndMemberDto o) -> o.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getName().trim().toLowerCase(), isAsc ? Comparator.naturalOrder() : reverseOrder());
                    networkingLoungesWithStatusAndMemberDto.sort(nameComparator);
                    break;

                case MODERATOR:
                    Comparator<NetworkingLoungesWithStatusAndMemberDto> moderatorComparator = Comparator.comparing((NetworkingLoungesWithStatusAndMemberDto k) -> k.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getCreatedBy().equals(String.valueOf(userId)) ? 1 : 2, isAsc ? Comparator.naturalOrder() : reverseOrder())
                            .thenComparingInt(k -> statusMap.get(k.getLoungesDto().getLoungeStatus()))
                            .thenComparing(k -> k.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getName().trim().toLowerCase());
                    networkingLoungesWithStatusAndMemberDto.sort(moderatorComparator);
                    break;

                case NO_OF_MEMBERS:
                    Comparator<NetworkingLoungesWithStatusAndMemberDto> memberJoinedComparator = Comparator.comparing((NetworkingLoungesWithStatusAndMemberDto k) -> k.getNetworkingLoungesAndMembersDto().getMemberCount(), isAsc ? Comparator.naturalOrder() : reverseOrder())
                            .thenComparingInt(k -> statusMap.get(k.getLoungesDto().getLoungeStatus()))
                            .thenComparing(k -> k.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getName().trim().toLowerCase());
                    networkingLoungesWithStatusAndMemberDto.sort(memberJoinedComparator);

                    break;

                case STATUS:
                    Comparator<NetworkingLoungesWithStatusAndMemberDto> statusComparator = Comparator.comparing((NetworkingLoungesWithStatusAndMemberDto n) -> statusMap.get(n.getLoungesDto().getLoungeStatus()), isAsc ? Comparator.naturalOrder() : reverseOrder())
                            .thenComparing(n -> n.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getName().trim().toLowerCase());
                    networkingLoungesWithStatusAndMemberDto.sort(statusComparator);
                    break;

                case CREATED_DATE:
                    Comparator<NetworkingLoungesWithStatusAndMemberDto> createdAtDateComparator = Comparator.comparing(k -> {
                        try {
                            return dateFormat.parse(k.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getCreateAt());
                        } catch (ParseException e) {
                            log.error("While parsing date with EventId {} , NeptuneLoungeId {} , sortColumn {} parsingDate {} and ErrorMsg {}", eventId, neptuneLoungeId, sortColumn, k.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getCreateAt(), e.getMessage());
                            throw new RuntimeException("Error While parsing date");
                        }
                    });
                    networkingLoungesWithStatusAndMemberDto.sort(isAsc ? createdAtDateComparator : createdAtDateComparator.reversed());
                    break;

                case POSITION:
                    Comparator<NetworkingLoungesWithStatusAndMemberDto> defaultComparator = Comparator.comparingDouble(k -> k.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getPosition());
                    networkingLoungesWithStatusAndMemberDto.sort(defaultComparator.reversed());
                    break;

                case MODERATOR_LIVE_FORUM_AT:
                    Long lCurrentTime = getDateInLocal(Calendar.getInstance().getTime(), event.getEquivalentTimeZone()).getTime();
                    Comparator<NetworkingLoungesWithStatusAndMemberDto> moderatorLiveForumAtComparator = Comparator.comparingLong(k -> {
                        String moderatorLiveForumAt = k.getLoungesDto().getModeratorLiveForumAt();
                        try {
                            if (null == moderatorLiveForumAt) {
                                return isAsc ? Long.MAX_VALUE : Long.MIN_VALUE;
                            } else {
                                Long lModeratorLiveForumAt = dateFormat.parse(moderatorLiveForumAt).getTime();
                                Long diffInTime = lModeratorLiveForumAt - lCurrentTime;
                                if (isAsc && diffInTime < 0) {
                                    return Long.MAX_VALUE - Math.abs(diffInTime);
                                }
                                return diffInTime;
                            }
                        } catch (ParseException e) {
                            log.error("While parsing date with EventId {} , NeptuneLoungeId {} , sortColumn {} parsingDate {} and ErrorMsg {}", eventId, neptuneLoungeId, sortColumn, k.getNetworkingLoungesAndMembersDto().getNetworkingLounge().getCreateAt(), e.getMessage());
                            throw new RuntimeException("Error While parsing date");
                        }

                    });
                    networkingLoungesWithStatusAndMemberDto.sort(isAsc ? moderatorLiveForumAtComparator : moderatorLiveForumAtComparator.reversed());
                    break;


            }
        } catch (Exception ex) {
            log.error("While sorting Lounge Data with EventId {} , NeptuneLoungeId {} , sortColumn {} and ErrorMsg {}", eventId, neptuneLoungeId, sortColumn, ex.getMessage());
        }
        return networkingLoungesWithStatusAndMemberDto;
    }

    LoungeTimeslotDto createLoungeTimeslotDtoFromEntity(LoungeTimeslot loungeTimeslot, String equivalentTimeZone) {
        log.info("Creating LoungeTimeslotDto with EventTimeslotId {} ", loungeTimeslot.getId());
        LoungeTimeslotDto loungeTimeslotDto = new LoungeTimeslotDto();
        try {
            loungeTimeslotDto.setId(loungeTimeslot.getId());
            loungeTimeslotDto.setEventLoungesSettingId(loungeTimeslot.getEventLoungesSettingId());
            loungeTimeslotDto.setStartDate(getDateInLocal(loungeTimeslot.getStartDate(), equivalentTimeZone, null));
            loungeTimeslotDto.setEndDate(getDateInLocal(loungeTimeslot.getEndDate(), equivalentTimeZone, null));

        } catch (Exception ex) {
            log.error("Creating LoungeTimeslotDto with EventTimeslotId {} ErrorMsg {}", loungeTimeslot.getId(), ex.getMessage());
        }

        return loungeTimeslotDto;

    }

    Boolean isLoungesApprovedLimitReached(Event event) {
        log.info("Is Lounges Max Approved Limit Reached with EventId {} ", event.getEventId());
        List<Long> eventAdminIdList = staffService.findAllEventAdminId(event);
        return networkingLoungesSettingRepo.isLoungesApprovedLimitReached(event.getEventId(), eventAdminIdList);
    }

    @Transactional
    public void copyEventNetworkingLounges(Event oldEvent, Event newEvent, String userId, Map<String, String> oldNewLoungeMap) {
        log.info("Copying Networking Lounge from OldEvent {} to NewEvent {} ,UserId {} ", oldEvent.getEventId(), newEvent.getEventId(), userId);
        try {
            List<Long> eventAdminIdList = staffService.findAllEventAdminId(oldEvent);
            log.info(" List of EventAdminList size {} from findAllEventAdminId method", eventAdminIdList.size());
            duplicateEventLoungesSetting(oldEvent, newEvent);
            List<Lounges> loungesList = loungesRepository.findAllByEventIdAndCreatedBys(oldEvent.getEventId(), eventAdminIdList);
            log.info(" List of LoungesList size {} from findAllByEventIdAndCreatedBys method using OldEventId {} and EventAdminList size {}", loungesList.size(), oldEvent.getEventId(), eventAdminIdList.size());
            List<NetworkingLounge> networkingLoungeList = neptuneNetworkingLoungeService.getAllNetworkingLoungesById(loungesList.stream().map(Lounges::getNeptuneLoungeId).collect(Collectors.toList()));
            log.info(" List of networkingLoungeList size {} from getAllNetworkingLoungesById method using OldEventId {} and loungesList size {}", networkingLoungeList.size(), oldEvent.getEventId(), loungesList.size());
            Map<String, Lounges> loungesMap = loungesList.stream()
                    .collect(Collectors.toMap(Lounges::getNeptuneLoungeId, Function.identity()));

            networkingLoungeList.forEach(networkingLounge -> {
                networkingLounge.setPhotos(null);
                Lounges lounge = loungesMap.get(networkingLounge.getId());
                if (lounge != null) {
                    saveLoungesAndNetworkingLounges(lounge, networkingLounge, newEvent.getEventId(), userId, oldNewLoungeMap);
                }
            });

        } catch (Exception ex) {
            log.error("Error while copy Networking Lounge from OldEvent {} to NewEvent {} , ErrorMsg {} ", oldEvent.getEventId(), newEvent.getEventId(), ex.getStackTrace());
        }
    }


    public void saveLoungesAndNetworkingLounges(Lounges lounge, NetworkingLounge networkingLounge, Long newEventId, String userId, Map<String, String> oldNewLoungeMap) {
        try {
            Lounges newLounge = new Lounges();
            log.info("Saving Lounge and Networking Lounge with OldEventId {} , NewEventId {} , NeptuneLoungeId {} , UserId {}", networkingLounge.getEventId(), newEventId, networkingLounge.getId(), userId);
            newLounge.setCreatedBy(Long.valueOf(userId));
            newLounge.setEventId(newEventId);
            newLounge.setNeptuneLoungeId(neptuneNetworkingLoungeService.addNetworkingLounge(networkingLounge, userId, String.valueOf(newEventId)));
            newLounge.setLoungeStatus(lounge.getLoungeStatus());
            newLounge.setRecStatus(lounge.getRecStatus());
            newLounge.setTags(lounge.getTags());
            newLounge.setVideoOccupancy(lounge.getVideoOccupancy());
            newLounge.setAttendeeCapacity(lounge.getAttendeeCapacity());
            loungesRepository.save(newLounge);
            oldNewLoungeMap.put(lounge.getNeptuneLoungeId(), newLounge.getNeptuneLoungeId());
        } catch (Exception ex) {
            log.error("Error while saving Lounge and Networking Lounge with OldEventId {} , NewEventId {} , NeptuneLoungeId {} , UserId {} , ErrorMsg {}", networkingLounge.getEventId(), newEventId, networkingLounge.getId(), userId, ex.getStackTrace());
        }

    }

    /**
     * @param tags as List of tags
     * @return A regex matching the list of tags passed. for
     * <AUTHOR> Patel
     * <p>
     * Symbol Used for this method :- (,),^,|,$.
     * "( )" :- Used to group sub-expressions or to provide a so-called back-reference for capturing and extracting matches.
     * "|" :- "|" represents the OR operator.
     * "^" :-  "^" match the beginning of the input string.
     * "$" :- "$" match the ending of the input string.
     * <p>
     * For Exp:-  Input Tags -> "Apple"
     * Above tags With Regex  "(^|,)Apple(,|$)"
     * Input Tags with List of tags -> ["Apple","BGMI","Java"]
     * Above list of tags With Regex  "(^|,)Apple(,|$)|(^|,)BGMI(,|$)|(^|,)Java(,|$)"
     * <p>
     * *Note:- "(^|,)SINGLE_TAG(,|$)" will be single Regex for the tags.
     * For Multiple Tags single Regex will be added followed by "|" Symbol.
     */
    public String generateRegexForTags(List<String> tags) {
        log.info("Generating Regex Expression for Tags {} ", tags);
        String regexExpTags = "";
        try {
            regexExpTags = tags.stream()
                    .map(tag -> "(^|,)" + tag + "(,|$)")
                    .collect(Collectors.joining("|"));
        } catch (Exception ex) {
            log.error("Error while creating Regex Expression for Tags tags {} , ErrorMsg {}", tags, ex.getStackTrace());
        }
        return regexExpTags;
    }

    // As of now commenting below code
    // Jira :- https://accelevents.atlassian.net/browse/DEV-30042
   /* public void updateLoungesVideoOccupancy(Long eventId, Long newVideoOccupancy) {
        log.info("Updating video occupancy with EventId {} new Video Occupancy {}", eventId, newVideoOccupancy);
        try {
            List<Lounges> loungesList = loungesRepository.findAllByEventIdAndVideoOccupancyGreaterThan(eventId, newVideoOccupancy);
            if (!loungesList.isEmpty()) {
                loungesList.forEach(lounge -> lounge.setVideoOccupancy(newVideoOccupancy));
                loungesRepository.saveAll(loungesList);
                log.info("Updated {} lounges inside Event with EventId {} to new Video Occupancy {}", loungesList.size(), eventId, newVideoOccupancy);
            } else {
                log.info("No lounges found to update for Event with EventId {} and new Video Occupancy {}", eventId, newVideoOccupancy);
            }
        } catch (Exception ex) {
            log.error("Error while updating video Occupancy with EventId {} , new Video Occupancy {} , ErrorMsg {} ", eventId, newVideoOccupancy, ex.getMessage());
        }

    }
*/
    public void handleMaxApprovedLimitExceeded(Event event) {
        log.info("Handling Max Approved Limit  with EventId {} ", event.getEventId());
        Optional<LoungesSetting> loungesSetting = networkingLoungesSettingRepo.findByEventId(event.getEventId());
        if (loungesSetting.isPresent()) {
            String constantMessage = Constants.NETWORKING_LOUNGE_MAX_APPROVED_LIMIT.replace("${count}", String.valueOf(loungesSetting.get().getMaxApprovedLimit()));
            NotAcceptableException.NetworkingLoungeExceptionMsg ex = NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_MAX_APPROVED_LIMIT;
            ex.setErrorMessage(constantMessage);
            ex.setDeveloperMessage(constantMessage);
            throw new NotAcceptableException(ex);
        }
    }


    @Override
    public ResponseDto saveModeratorLiveForumDate(String neptuneLoungeId, User user, Event event, NetworkingLoungesDto networkingLoungeDto) {
        try {
            log.info("Updating Moderator Live Forum Date having NeptuneLoungesId {} , UserId {} , EventId {} , ModeratorLiveForumDate {} ", neptuneLoungeId, user.getUserId(), event.getEventId(), networkingLoungeDto.getModeratorLiveForumAt());
            Lounges lounges = loungesRepository.findByCreatedByAndEventIdAndNeptuneLoungeId(user.getUserId(), event.getEventId(), neptuneLoungeId)
                    .orElseThrow(() -> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_DATA_NOT_FOUND));
            lounges.setModeratorLiveForumAt(getDateInUTC(networkingLoungeDto.getModeratorLiveForumAt(), event.getEquivalentTimeZone()));
            loungesRepository.save(lounges);

        } catch (NotFoundException notFoundException) {
            throw notFoundException;
        } catch (Exception ex) {
            log.error("Updating Moderator Live Forum Date having NeptuneLoungesId {} , UserId {} , EventId {} , ModeratorLiveForumDate {} , ErrorMsg {}", neptuneLoungeId, user.getUserId(), event.getEventId(), networkingLoungeDto.getModeratorLiveForumAt(), ex.getStackTrace());
            return new ResponseDto(FAIL, COMMON_ERROR_MESSAGE);
        }
        return new ResponseDto(SUCCESS, NETWORK_LOUNGE_MODERATOR_LIVE_FORUM_DATE_SAVED_SUCCESSFULLY);

    }

    @Override
    public List<LoungesNameDto> getActiveLoungesName(Event event) {

        log.info("getActiveLoungesName eventId {}", event.getEventId());

        List<String> neptuneLoungesIdList = loungesRepository.getNeptuneLoungeIdByEventIdAndLoungesStatus(event.getEventId(), EnumLoungeStatus.APPROVED.getValue());
        log.info("neptuneLoungesIdList {}", neptuneLoungesIdList);

        List<NetworkingLounge> networkingLounges = neptuneNetworkingLoungeService.getAllNetworkingLoungesById(neptuneLoungesIdList);
        log.info("networkingLounges size {}", networkingLounges.size());

        List<LoungesNameDto> loungesNameDtoList = new ArrayList<>();

        networkingLounges.forEach(networkingLounge -> loungesNameDtoList.add(new LoungesNameDto(networkingLounge.getId(), networkingLounge.getName())));

        return loungesNameDtoList;
    }

    @Override
    public void updateLoungeAttendeeCapacity(Event event, User user, String neptuneLoungeId, Long attendeeCapacity) {
        log.info("updateLoungeAttendeeCapacity with eventId: {} , userId: {} , neptuneLoungeId: {}", event.getEventId(), user.getUserId(), neptuneLoungeId);
        boolean isAdminOrLoungeCreator = false;
        if (roStaffService.hasHostAccessForEvent(user, event)) {
            isAdminOrLoungeCreator = true;
        } else {
            isAdminOrLoungeCreator = loungesRepository.existsLoungesByNeptuneLoungeIdAndCreatedBy(neptuneLoungeId, user.getUserId());
        }
        if (isAdminOrLoungeCreator) {
            Lounges lounge = loungesRepository.findByNeptuneLoungeId(neptuneLoungeId)
                    .orElseThrow(() -> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_NOT_FOUND));
            lounge.setAttendeeCapacity(attendeeCapacity);
            loungesRepository.save(lounge);
        }else {
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @Override
    public void pinnedMessageIntoFeed(Event event, User user, String neptuneLoungeId,String messageId) {
        log.info("pinnedMessageIntoFeed with eventId: {} , userId: {} , neptuneLoungeId: {} , messageId: {}",
                event.getEventId(), user.getUserId(), neptuneLoungeId, messageId);

        Lounges lounge = loungesRepository.findByNeptuneLoungeId(neptuneLoungeId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_NOT_FOUND));

        if (isAdminOrLoungeCreator(event, user, neptuneLoungeId)) {
            List<String> feedPinnedMessageList = GeneralUtils.convertCommaSeparatedToList(lounge.getFeedPinnedMessage());
            if (feedPinnedMessageList.isEmpty()) {
                log.info("No pinned message present in the feed.");
                feedPinnedMessageList = Collections.singletonList(messageId);
            }
            updateFeedPinnedMessages(feedPinnedMessageList, messageId, true,lounge);
        } else {
            throw new NotAuthorizedException(NOT_AUTHORIZE);
        }
    }

    @Override
    public void unPinnedMessageIntoFeed(Event event, User user, String neptuneLoungeId, String messageId) {
        log.info("unPinnedMessageIntoFeed with eventId: {} , userId: {} , neptuneLoungeId: {} , messageId: {}",
                event.getEventId(), user.getUserId(), neptuneLoungeId, messageId);

        Lounges lounge = loungesRepository.findByNeptuneLoungeId(neptuneLoungeId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_NOT_FOUND));

        if (isAdminOrLoungeCreator(event, user, neptuneLoungeId)) {
            List<String> feedPinnedMessageList = GeneralUtils.convertCommaSeparatedToList(lounge.getFeedPinnedMessage());
            if(feedPinnedMessageList.isEmpty()){
                log.info("unPinnedMessageIntoFeed: No existing pinned message found in the feed");
            }else {
                updateFeedPinnedMessages(feedPinnedMessageList, messageId, false,lounge);
            }
        } else {
            throw new NotAuthorizedException(NOT_AUTHORIZE);
        }

    }
    @Override
    public void removeMessageIdFromFeed(Event event, User user, String neptuneLoungeId, KeyValueDto keyValueDto){
        log.info("removeMessageIdFromFeed with eventId: {} , userId: {} , neptuneLoungeId: {} ",
                event.getEventId(), user.getUserId(), neptuneLoungeId);
        Lounges lounge = loungesRepository.findByNeptuneLoungeId(neptuneLoungeId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.LoungeDataNotFound.LOUNGE_NOT_FOUND));
        if(StringUtils.isNotBlank(keyValueDto.getValue())){
            List<String> feedPinnedMessageList = GeneralUtils.convertCommaSeparatedToList(lounge.getFeedPinnedMessage());
            if(feedPinnedMessageList.isEmpty()){
                log.info("removeMessageIdFromFeed: No existing pinned message found in the feed");
            }else {
                String decodeMessageId = new String(Base64.getDecoder().decode(keyValueDto.getValue()));
                log.info("removeMessageIdFromFeed decoded feed message id: {}",decodeMessageId);
                updateFeedPinnedMessages(feedPinnedMessageList, decodeMessageId, false,lounge);
            }
        }

    }

    private void updateFeedPinnedMessages(List<String> feedPinnedMessageList,String messageId, boolean isPinning,Lounges lounge) {
        log.info("updateFeedPinnedMessages with neptuneLoungeId: {} , isPinning: {} and old feedPinnedMessageList: {} ",lounge.getNeptuneLoungeId(), isPinning,messageId);
        if(isPinning && !feedPinnedMessageList.contains(messageId)){
            feedPinnedMessageList.add(messageId);
        }else if(!isPinning && feedPinnedMessageList.contains(messageId)){
            feedPinnedMessageList.remove(messageId);
        }else{
            log.info("No operation performed based on old feed messages and new feed message id.");
        }
        lounge.setFeedPinnedMessage(GeneralUtils.convertListToCommaSeparated(feedPinnedMessageList));
        loungesRepository.save(lounge);
        log.info("updateFeedPinnedMessages : successfully save");
    }
    public void duplicateEventLoungesSetting(Event oldEvent, Event newEvent) {
        try {
            log.info("Duplicating Event Lounges Setting using Old EventId {} , New EventId {}", oldEvent.getEventId(), newEvent.getEventId());
            Optional<LoungesSetting> oldLoungesSetting = networkingLoungesSettingRepo.findByEventId(oldEvent.getEventId());
            if (oldLoungesSetting.isPresent()) {
                LoungesSetting newLoungesSetting = new LoungesSetting();
                newLoungesSetting.setEventId(newEvent.getEventId());
                createLoungeSettingEntity(oldLoungesSetting.get(), newLoungesSetting);
                networkingLoungesSettingRepo.save(newLoungesSetting);
            } else {
                log.info("No Lounges Setting found for Event Id {}", oldEvent.getEventId());
            }
        } catch (Exception ex) {
            log.error("Duplicating Event Lounges Setting using Old EventId {} , New EventId {} , ErrorMsg {}", oldEvent.getEventId(), newEvent.getEventId(), ex.getMessage());
        }

    }

    public void createLoungeSettingEntity(LoungesSetting oldLoungesSetting, LoungesSetting newLoungesSetting) {
        try {
            log.info("Creating Lounge Setting Entity from Old LoungesSetting having old event id {}",oldLoungesSetting.getEventId());
            newLoungesSetting.setLoungeScheduleEnabled(Boolean.FALSE);
            newLoungesSetting.setLimitApprovedEnabled(oldLoungesSetting.getLimitApprovedEnabled());
            newLoungesSetting.setMaxApprovedLimit(oldLoungesSetting.getMaxApprovedLimit());
            newLoungesSetting.setReqAcceptable(oldLoungesSetting.getReqAcceptable());
            newLoungesSetting.setAttendeesAllow(oldLoungesSetting.getAttendeesAllow());
            newLoungesSetting.setMaxVideoOccupancyLimit(oldLoungesSetting.getMaxVideoOccupancyLimit());
        } catch (Exception ex) {
            log.error("Creating Lounge Setting Entity from Old LoungesSetting having old event id {} and ErrorMsg {}",oldLoungesSetting.getEventId(),ex.getMessage());
        }
    }

    private  void checkLoungesExistInMessageToContactsAndUpdateIt(Long eventId,String neptuneLoungeId,String operation){
        List<MessageToContacts> messageToContactsList;
        log.info("checkLoungesExistInMessageToContactsAndUpdateIt eventId {} , neptuneLoungeId {} , operation {}",eventId,neptuneLoungeId,operation);
        try {
            if (ADD.equals(operation)) {
                    messageToContactsList = messageToContactsRepository.findAllByEventIdAndSentTOAndStatusNotSENTAndAllMemberSelected(eventId, EMAIL_RECIPIENTS.LOUNGES);
                    updateMessageToContactsIfRequired(neptuneLoungeId,messageToContactsList,operation);

            } else if (DELETE.equals(operation)) {
                    messageToContactsList = messageToContactsRepository.findAllByEventIdAndSentTOAndStatusNotSENT(eventId, EMAIL_RECIPIENTS.LOUNGES);
                   updateMessageToContactsIfRequired(neptuneLoungeId,messageToContactsList,operation);
            }
        }catch (Exception ex){
            log.info("checkLoungesExistInMessageToContactsAndUpdateIt eventId {} , neptuneLoungeId {} , operation {} , errorMsg {}",eventId,neptuneLoungeId,operation,ex.getMessage());
        }
    }

    private  void  updateMessageToContactsIfRequired(String neptuneLoungeId,List<MessageToContacts> messageToContactsList,String operation){
        log.info("messageToContactsList size {}",messageToContactsList.size());
        if(!messageToContactsList.isEmpty())
        {
            if(ADD.equals(operation)){
                messageToContactsList.forEach( messageToContacts -> {
                    messageToContacts.setMembersSelected(messageToContacts.getMembersSelected().concat(",").concat(neptuneLoungeId));
                    log.info("ADD updated {}",messageToContacts.getMembersSelected());
                });
            }else if (DELETE.equals(operation)) {
                messageToContactsList.forEach( messageToContacts -> {
                    messageToContacts.setMembersSelected(updateMemberSelected(messageToContacts.getMembersSelected(),neptuneLoungeId));
                    log.info("DELETE updated {}",messageToContacts.getMembersSelected());
                });
            }
            messageToContactsRepository.saveAll(messageToContactsList);
        }
    }
    private String updateMemberSelected(String commaSeparatedMembersSelected,String neptuneLoungeId) {
        log.info("Update List commaSeparatedMembersSelected {} and  neptuneLoungeId {}",commaSeparatedMembersSelected,neptuneLoungeId);
        List<String> updatedList = GeneralUtils.convertCommaSeparatedToList(commaSeparatedMembersSelected);
        updatedList.remove(neptuneLoungeId);
        return GeneralUtils.convertListToCommaSeparated(updatedList);
    }

    private void checkLoungeAttendeeCapacity(Event event, Lounges lounges, User user) {
        log.info("checkLoungeAttendeeCapacity using eventId: {} , neptuneLoungeId: {} , userId: {}", event.getEventId(), lounges.getNeptuneLoungeId(), user.getUserId());
        if (lounges.getAttendeeCapacity() != null && lounges.getAttendeeCapacity() > 0) {
            List<Long> joinedAttendees = neptuneNetworkingLoungeService.getAllUniqueUserIdsByLoungesIds(Collections.singletonList(lounges.getNeptuneLoungeId()));
            List<Long> eventAdminIdList = userService.getAllUserIdByEventIdAndWhiteLabelIdOrOrganizerId(event.getEventId(), event.getWhiteLabelId(), event.getOrganizerId());
            joinedAttendees.removeAll(eventAdminIdList);
            if (joinedAttendees.size() >= lounges.getAttendeeCapacity()) {
                String constantMessage = Constants.NETWORKING_LOUNGE_MAX_ATTENDEE_CAPACITY.replace("${count}", String.valueOf(lounges.getAttendeeCapacity()));
                NotAcceptableException.NetworkingLoungeExceptionMsg ex = NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_MAX_ATTENDEE_CAPACITY;
                ex.setErrorMessage(constantMessage);
                ex.setDeveloperMessage(constantMessage);
                throw new NotAcceptableException(ex);
            }
        }
    }

    private boolean isAdminOrLoungeCreator(Event event, User user, String neptuneLoungeId) {
        return roStaffService.isEventAdmin(event, user) ||
                loungesRepository.existsLoungesByNeptuneLoungeIdAndCreatedBy(neptuneLoungeId, user.getUserId());
    }

    /**
     * Retrieves member details for the specified networking lounge IDs for a given event and user.
     * @param event              the event object containing metadata like event ID
     * @param user               the authenticated user making the request
     * @param neptuneLoungeIds   a comma-separated string of Neptune lounge IDs (e.g., "lounge1,lounge2")
     * @return a list of {@link NetworkingLoungesAndMembersDto} containing member count and participation details for each lounge
     */
    @Override
    public List<NetworkingLoungesAndMembersDto> getMemberDetailsByNetworkingLoungeIds(Event event, User user, String neptuneLoungeIds) {
        log.info("getMemberDetailsByNetworkingLoungeIds using eventId: {} , userId: {} neptuneLoungeIds: {}", event.getEventId(), user.getUserId(), neptuneLoungeIds);
        List<NetworkingLoungesAndMembersDto> networkingLoungesAndMembersDto = new ArrayList<>();
        if (!StringUtils.isEmpty(neptuneLoungeIds)) {
            List<String> loungeIds = GeneralUtils.convertCommaSeparatedToList(neptuneLoungeIds);
            if (!CollectionUtils.isEmpty(loungeIds)) {
                log.info("Fetching lounge members count and user information which are connected with lounge of event {} , loungeIds {}", event.getEventId(), loungeIds);
                networkingLoungesAndMembersDto = neptuneNetworkingLoungeService.getLoungeMembersDetailsByNetworkingLoungeIds(String.valueOf(event.getEventId()), String.valueOf(user.getUserId()), loungeIds);

            }
        }
        return networkingLoungesAndMembersDto;
    }

}

