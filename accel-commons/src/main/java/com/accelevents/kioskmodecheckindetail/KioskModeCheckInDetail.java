package com.accelevents.kioskmodecheckindetail;

import com.accelevents.BaseAuditInfoUser;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

import static com.accelevents.domain.enums.RecordStatus.CREATE;

@Entity
@Table(name = "kiosk_mode_check_in_detail")
@Where(clause="rec_status<>'DELETE'")
public class KioskModeCheckInDetail extends BaseAuditInfoUser implements Cloneable, Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private long id;

    @Column(name = "event_id", nullable = false)
    private long eventId;

    @ManyToOne(fetch = FetchType.LAZY,targetEntity = Event.class)
    @JoinColumn(name = "event_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private Event event;

    @Column(name = "session_id", nullable = false)
    private Long sessionId;

    @ManyToOne(fetch = FetchType.LAZY,targetEntity = Session.class)
    @JoinColumn(name = "session_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private Session session;

    @Type(type = "text")
    @Column(name = "kiosk_detail_json", columnDefinition = "LONGTEXT")
    private String kioskDetailJson;

    @Column(name ="rec_status")
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus = CREATE;

    public KioskModeCheckInDetail() {
    }

    public KioskModeCheckInDetail(Long eventId, Event event, Long sessionId, Session session, String kioskDetailJson, RecordStatus recordStatus) {
        this.eventId = eventId;
        this.event = event;
        this.sessionId = sessionId;
        this.session = session;
        this.kioskDetailJson = kioskDetailJson;
        this.recordStatus = recordStatus;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public Session getSession() {
        return session;
    }

    public void setSession(Session session) {
        this.session = session;
    }

    public String getKioskDetailJson() {
        return kioskDetailJson;
    }

    public void setKioskDetailJson(String kioskDetailJson) {
        this.kioskDetailJson = kioskDetailJson;
    }

    public RecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    @Override
    public String toString() {
        return "KioskModeCheckInDetail{" +
                "id=" + id +
                ", eventId=" + eventId +
                ", event=" + event +
                ", sessionId=" + sessionId +
                ", session=" + session +
                ", kioskDetailJson='" + kioskDetailJson + '\'' +
                ", recordStatus=" + recordStatus +
                '}';
    }

    @Override
    public Object clone() {  //NOSONAR
        Object result;
        try {
            result = super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            result = null;
        }
        return result;
    } //NOSONAR

}
