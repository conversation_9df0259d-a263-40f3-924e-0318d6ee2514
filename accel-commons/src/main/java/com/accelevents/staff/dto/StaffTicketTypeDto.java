package com.accelevents.staff.dto;

import com.accelevents.domain.Staff;

public class StaffTicketTypeDto {

    Long userId;
    Long ticketTypeId;
    Boolean isStaffOrder;
    String ticketType;

    public StaffTicketTypeDto(){}

    public StaffTicketTypeDto(Long userId, Long ticketTypeId, Boolean isStaffOrder){
        this.userId = userId;
        this.ticketTypeId = ticketTypeId;
        this.isStaffOrder = isStaffOrder;
    }

    public StaffTicketTypeDto(Long userId, Long ticketTypeId, Boolean isStaffOrder, String ticketType){
        this.userId = userId;
        this.ticketTypeId = ticketTypeId;
        this.isStaffOrder = isStaffOrder;
        this.ticketType = ticketType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTicketTypeId() {
        return ticketTypeId;
    }

    public void setTicketTypeId(Long ticketTypeId) {
        this.ticketTypeId = ticketTypeId;
    }

    public Boolean getStaffOrder() {
        return isStaffOrder;
    }

    public void setStaffOrder(Boolean staffOrder) {
        isStaffOrder = staffOrder;
    }

    public String getTicketType() {
        return ticketType;
    }

    public void setTicketType(String ticketType) {
        this.ticketType = ticketType;
    }
}
