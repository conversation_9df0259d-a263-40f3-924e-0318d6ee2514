package com.accelevents.domain.enums;

public enum TrayIOWebhookActionType {
    PURCHASE_TICKET, VIRTUAL_EVENT_CHECK_IN, CANCEL, UPDATE_HOLDER_DETAILS, CHECKOUT_HOLDER_FROM_CHECKIN, TRACKING_ATTENDEE_ACTIVITY, RET<PERSON>EVE_EVENT_KEYS, MAP_EVENT, CREATE_EVENT, CREATE_TICKET_TYPES, UPDATE_TICKET_TYPES, DELETE_TICKET_TYPES,REPORT, EDIT_<PERSON><PERSON><PERSON>ZER, CREATE_ORGANIZER, GET_OR<PERSON>NIZER, CREATE_TEMPLATE, EDIT_TEMPLATE, <PERSON>LETE_TEMPLATE, GET_TEMPLATE, RETRIEVE_FIELDS, MATCH_FIELDS, UPDATE_DEAL_AMOUNT, MAP_TRAVEL, UPDATE_PAYMENT_STATUS, RE_SYNC, REGISTRATION_REQUEST_CREATE, REGISTRATION_REQUEST_UPDATE, <PERSON><PERSON><PERSON>TRAT<PERSON>_REQUEST_RE_SYNC, <PERSON><PERSON><PERSON>_USER_FIELDS, <PERSON>ETCH_USER_DETAILS
}
