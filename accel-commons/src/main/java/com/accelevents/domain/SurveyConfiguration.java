package com.accelevents.domain;

import com.accelevents.domain.enums.RecordStatus;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@Entity
@Where(clause="rec_status<>'DELETE'")
@Table(name = "survey_configuration")
public class SurveyConfiguration implements Serializable, Cloneable {

    @Id
    @Column(name = "survey_id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long surveyId;

    @NotNull
    @Column(name = "event_id")
    private long eventId;

    @NotNull
    @Column(name = "created_by")
    private long createdBy;

    @NotNull(message = "Survey Name Cannot Be Empty.")
    @Size(max = 255, message = "Only 255 Characters are allowed.")
    @Column(name = "survey_name")
    private String surveyName;

    @NotNull(message = "Survey Title Cannot Be Empty.")
    @Size(max = 255, message = "Only 255 Characters are allowed.")
    @Column(name = "survey_title")
    private String surveyTitle;

    @Column(name = "description")
    @Type(type = "text")
    private String description;

    @Column(name = "created_date")
    private Date createdDate;

    @Column(name = "updated_date")
    private Date updateDate;

    @NotNull(message = "Survey Headline Cannot Be Empty.")
    @Size(max = 255, message = "Only 255 Characters are allowed.")
    @Column(name = "survey_headline")
    private String surveyHeadline;
    @Size(max = 1000, message = "Only 1000 Characters are allowed.")
    @Column(name = "survey_feedback_message")
    private String surveyFeedbackMessage;

    @Enumerated(EnumType.STRING)
    @Column(name = "rec_status")
    private RecordStatus recordStatus = RecordStatus.CREATE;

    @Column(name = "is_login_required")
    private boolean isLoginRequired = true;

    @Column(name = "is_survey_quiz_enabled")
    private boolean isSurveyQuizEnabled = false;

    @Column(name = "is_default_survey")
    private boolean isDefaultSurvey = false;

    public boolean isDefaultSurvey() {
        return isDefaultSurvey;
    }

    public void setDefaultSurvey(boolean defaultSurvey) {
        isDefaultSurvey = defaultSurvey;
    }

    public long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(long surveyId) {
        this.surveyId = surveyId;
    }

    public long getEventId() {
        return eventId;
    }

    public void setEventId(long eventId) {
        this.eventId = eventId;
    }

    public long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(long createdBy) {
        this.createdBy = createdBy;
    }

    public String getSurveyName() {
        return surveyName;
    }

    public void setSurveyName(String surveyName) {
        this.surveyName = surveyName;
    }

    public String getSurveyTitle() {
        return surveyTitle;
    }

    public void setSurveyTitle(String surveyTitle) {
        this.surveyTitle = surveyTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public RecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(RecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getSurveyHeadline() {return surveyHeadline;}

    public void setSurveyHeadline(String surveyHeadline) {this.surveyHeadline = surveyHeadline;}

    public String getSurveyFeedbackMessage() {return surveyFeedbackMessage;}

    public void setSurveyFeedbackMessage(String surveyFeedbackMessage) {this.surveyFeedbackMessage = surveyFeedbackMessage;}

    public boolean isLoginRequired() {
        return isLoginRequired;
    }

    public void setLoginRequired(boolean loginRequired) {
        this.isLoginRequired = loginRequired;
    }

    public boolean isSurveyQuizEnabled() {
        return isSurveyQuizEnabled;
    }

    public void setSurveyQuizEnabled(boolean surveyQuizEnabled) {
        isSurveyQuizEnabled = surveyQuizEnabled;
    }

    @Override
    public Object clone(){
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;
        }
    }
}
