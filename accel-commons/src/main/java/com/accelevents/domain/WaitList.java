package com.accelevents.domain;

import com.accelevents.domain.enums.CountryCode;
import com.accelevents.dto.WaitListDto;
import com.accelevents.dto.WaitListUpdateDto;
import com.accelevents.messages.WaitListStatus;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * To store all the wait list details.
 */
@Entity
@Table(name = "waitlist")
@Where(clause="status<>'DELETED' or status is NULL ")
public class WaitList implements Serializable {


	/**
	 * 
	 */
	private static final long serialVersionUID = -4468280530421531493L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long waitListId;

	@Column(name = "event_id")
	private long eventId;

	@Column(name = "time_added_to_list")
	private Date timeAddedToList;

	@Column(name = "time_tickets_became_available")
	private Date timeTicketsBecameAvailable;

	@Column(name = "first_name")
	@NotNull
	private String firstName;

	@Column(name = "last_name")
	@NotNull
	private String lastName;

	@Column(name = "email")
	@NotNull
	private String email;

	@Column(name = "phone_number")
	private long phoneNumber;

	@Enumerated(EnumType.STRING)
	@Column(name = "status")
	private WaitListStatus status;

	@Enumerated(EnumType.STRING)
	@Column(name = "country_code", length = 5)
	private CountryCode countryCode;

	@Column(name = "ticketing_type_id")
	private long ticketingTypeId;

	@Column(name = "is_email_sent")
	private boolean emailSent;

    @Column(name = "position")
    private Double position = 1000d;

	public long getWaitListId() {
		return waitListId;
	}

	public void setWaitListId(long waitListId) {
		this.waitListId = waitListId;
	}

	public long getEventId() {
		return eventId;
	}

	public void setEventId(long eventId) {
		this.eventId = eventId;
	}

	public Date getTimeAddedToList() {
		return timeAddedToList;
	}

	public void setTimeAddedToList(Date timeAddedToList) {
		this.timeAddedToList = timeAddedToList;
	}

	public Date getTimeTicketsBecameAvailable() {
		return timeTicketsBecameAvailable;
	}

	public void setTimeTicketsBecameAvailable(Date timeTicketsBecameAvailable) {
		this.timeTicketsBecameAvailable = timeTicketsBecameAvailable;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		if(null != email){
			email = email.trim();
		}
		this.email = email;
	}

	public long getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(long phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public WaitListStatus getStatus() {
		return status;
	}

	public void setStatus(WaitListStatus status) {
		this.status = status;
	}

	public CountryCode getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(CountryCode countryCode) {
		this.countryCode = countryCode;
	}

	public long getTicketingTypeId() {
		return ticketingTypeId;
	}

	public void setTicketingTypeId(long ticketingTypeId) {
		this.ticketingTypeId = ticketingTypeId;
	}

	public boolean isEmailSent() {
		return emailSent;
	}

	public void setEmailSent(boolean emailSent) {
		this.emailSent = emailSent;
	}

    public Double getPosition() {
		return position;
	}

	public void setPosition(Double position) {
		this.position = position;
	}

	public WaitList() {
	}

	public WaitList(WaitListUpdateDto waitListDto) {
		this.email = waitListDto.getEmail();
		this.firstName = waitListDto.getFirstName();
		this.lastName = waitListDto.getLastName();
		this.countryCode = waitListDto.getCountryCode();
		this.phoneNumber = waitListDto.getPhoneNumber();
		this.status = waitListDto.getStatus();
		this.timeAddedToList = waitListDto.getTimeAddedToList();
		this.timeTicketsBecameAvailable = waitListDto.getTimeTicketsBecameAvailable();
		this.waitListId = waitListDto.getWaitListId();
		this.ticketingTypeId = waitListDto.getTicketingTypeId();
        this.position = waitListDto.getPosition();
	}

	public WaitList(WaitListDto waitListDto) {
		this.email = waitListDto.getEmail();
		this.firstName = waitListDto.getFirstName();
		this.lastName = waitListDto.getLastName();
		this.countryCode = waitListDto.getCountryCode();
		this.phoneNumber = waitListDto.getPhoneNumber();
		this.status = WaitListStatus.WAITING;
		this.timeAddedToList = new Date();
		this.ticketingTypeId = waitListDto.getTicketingTypeId();
        this.position = waitListDto.getPosition();
	}

}
