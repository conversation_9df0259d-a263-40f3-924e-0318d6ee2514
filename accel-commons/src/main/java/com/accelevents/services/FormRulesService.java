package com.accelevents.services;


import com.accelevents.domain.User;
import com.accelevents.domain.enums.RuleFormType;
import com.accelevents.dto.FormRulesDetailsAttributesDto;
import com.accelevents.dto.FormRulesDetailsDto;

import java.util.List;
import java.util.Map;

public interface FormRulesService {
    Long createFormRule(User user, Long eventIdOrEventRequestFormId, FormRulesDetailsDto formRulesDetailsDto, Long recurringEventId);

    void updateFormRule(User user, Long eventIdOrEventRequestFormId, Long ruleId, FormRulesDetailsDto formRulesDetailsDto, Long recurringEventId);

    void deleteFormRule(User user, Long eventIdOrEventRequestFormId, Long ruleId, Long recurringEventId);

    FormRulesDetailsDto getFormRule(User user, Long eventIdOrEventRequestFormId, Long ruleId);

    List<FormRulesDetailsDto> getAllFormRules(User user, Long eventIdOrEventRequestFormId, RuleFormType ruleFormType, Long recurringEventId);

    List<FormRulesDetailsAttributesDto> getAllFormRulesByAttributes(User user, Long eventIdOrEventRequestFormId, RuleFormType ruleFormType, Long recurringEventId);

    void checkAttributeAttachedWithFormRule(Long eventIdOrEventRequestFormId, long attributeId, RuleFormType type);

    void duplicateFormRule(User user, Long oldEventIdOrEventRequestFormId,Long newEventIdOrEventRequestFormId,  RuleFormType ruleFormType, Map<Long, Long> oldNewAttributeIdMap);

}
