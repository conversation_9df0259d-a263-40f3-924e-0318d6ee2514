package com.accelevents.services;

import com.accelevents.common.dto.EventDesignDetailDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventDesignDetail;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.event.CalendarInviteDto;
import com.accelevents.event.EventDesignSettingsDto;
import com.accelevents.virtualevents.dto.VirtualEventTabsDTO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface EventDesignDetailService {

	// DesignEvent getEventDesingByName(String name);//NOSONAR

	void save(EventDesignDetail event);
	
	EventDesignDetail findById(long id);

	EventDesignDetail findByEvent(Event event);

    EventDesignDetailDto findEventDesignDetail(Event event);

	boolean isTotalFundRaisedShow(Event event);

    boolean isEnableAutoAssignedSequence(Event event);

    EventDesignSettingsDto getEventSettings(Event event, Map<String,String> languageMap, User user);
	
	String getEventOrWhiteLabelLogoLocation(Event event);

	String getEventOrDefaultLogo(Event event);

	String getEventOrWhiteLabelLogoLocationWithDefault(Event event, String size);

    String getLogo(Event event);

    String getWLHeaderLogo(Event event);

    String getEventOrWhiteLabelHeaderLogoLocation(Event event);

    void updateEventDesignLogo(Event event, String logoImageUrl);

    void updateEventDesignLogo(Event event, String logoImageUrl, String secureUrl);

	void updateEventDesignBanner(Event event, String logoImageUrl);

    String getWhiteLabelLogoLocationWithoutSize(WhiteLabel whiteLabel);

    String getWhiteLabelLogoLocation(WhiteLabel whiteLabel, String size);

    String getWhiteLabelHeaderLogoLocation(WhiteLabel whiteLabel);

    String getLogo(String size, String logo);

    String getEventOrWhiteLabelLogoLocationWithOutsideSize(Event event);

    String getAccelEVentOrWhiteLabelLogo(Event event);

	Optional<EventDesignDetail> findByNeonEventId(String neonEventId);

    Long getViewScrollSpeedByEvent(Event event);

    String getSponsorDetailsByEvent(Event event);

    void updateSessionsSpeakersBasedOnEventType(boolean isEnable, Event event);

	boolean checkEndEventEnd(Event event);

    CalendarInviteDto getEventCalenderInviteByEvent(Event event);

    void updateEventCalenderInviteByEvent(Event event, CalendarInviteDto calendarInviteDto);

    boolean findIntercomActivatedByEventId(long eventId);

    List<EventDesignDetail> findByEventList(List<Event> events);

    Iterable<EventDesignDetail> saveAll(List<EventDesignDetail> eventDesignDetails);

    List<VirtualEventTabsDTO> getListOfConfigureTabsAsList(String json);

    String getEventOrOrganizerLogoLocationForInvoice(Event event);

    void updateEventDesignBanner(Event event, String croppedImgUrl, String secureUrl);

    void updateEventDesignVenueMap(Event event, String venueMapImage, String secureUrl);

    String getOrganizerLogoOrDefaultAeLogo(Event event);

    void updateEventCaptcha(Event event,  boolean enableCaptcha);

    boolean isEnabledCaptchaSettingForEvent(Event event);
    void updateEventAdvancedWebsiteLayoutStatus(Event event,boolean advancedWebsiteLayout);

    Map<Long, String> findAllByEvents(List<Long> events);
}
