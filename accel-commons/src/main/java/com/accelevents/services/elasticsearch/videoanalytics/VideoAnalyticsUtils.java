package com.accelevents.services.elasticsearch.videoanalytics;

import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregation;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.Sum;

import static com.accelevents.utils.Constants.NOT_APPLICABLE;

public class VideoAnalyticsUtils {


    public static double getAttandanceValueFromBucket(CompositeAggregation.Bucket bucket, String aggregation, String key) {
        double watchTime = 0;
        if(bucket == null){
            return watchTime;
        }
        Terms terms = bucket.getAggregations().get(aggregation);
        for (Terms.Bucket termsBucket : terms.getBuckets()) {
            if(key.equals(termsBucket.getKeyAsString())){
                Sum sum =  termsBucket.getAggregations().get(VideoAnalyticsConstant.AGGREGATION.WATCH_TIME);
                watchTime = sum.getValue();
            }
        }
        return watchTime;
    }

    public static String getSecondsInHHMMSS(Double seconds) {

        if(seconds == null ) return  NOT_APPLICABLE;

        if(seconds == 0) return  "00:00:00";

        long elapsedTime = seconds.longValue();
        long minutesInseconds = 60;
        long hoursInseconds = minutesInseconds * 60;
        long elapsedHours = elapsedTime / hoursInseconds;
        elapsedTime = elapsedTime % hoursInseconds;
        long elapsedMinutes = elapsedTime / minutesInseconds;
        elapsedTime = elapsedTime % minutesInseconds;
        long elapsedSeconds = elapsedTime;
        return String.format("%02d:%02d:%02d", (int)elapsedHours, (int)elapsedMinutes, (int)elapsedSeconds);
    }

    /**
     * Returns session duration calculated by session end time - start time
     * @param startTime Start time of session in millis
     * @param endTime End time of session in millis
     * @return Return duration in seconds
     */
    public static double getSessionDuration(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return (endTime - startTime) / 1000;
    }

}
