package com.accelevents.services.elasticsearch.videoanalytics;

public interface VideoAnalyticsConstant {

    String EVENT_ID = "eventId";
    String SESSION_ID = "sessionId";
    String LIVE_VIEW = "liveView";
    String RECORDING_VIEW = "recordingView";

    interface ELATICSEARCH_INDEX {
        String VIDEO_ANALYTICS = "videoanalytics";
        String VIDEO_VIEWS  = "video_views";
        String TYPE = "progresslogs";
    }

    interface AGGREGATION {
        int SESSION_AGG_SIZE = 1000;
        int BUCKET_SIZE = 10000;

        String SESSION_ID = "sessionId";
        String IS_SESSION_LIVE = "isSessionLive";
        String USER_ID = "userId";
        String WATCH_TIME = "watchTime";
        String VIDEO_SECOND_END = "videoSecondEnd";
        String UUID = "uuid";
        String AVG_ATTENDED_DURATION = "avgAttendedDuration";
        String LIVE_VIEWS = "liveViews";
        String RECORDING_VIEWS = "recordingViews";
    }

    interface FIELD {
        String SESSION_ID = "sessionId";
        String IS_SESSION_LIVE = "isSessionLive";
        String USER_ID = "userId";
        String WATCH_TIME = "watchTime";
        String DATE = "date";
        String VIDEO_SECOND_END = "videoSecondEnd";
        String UUID_KEYWORD = "uuid.keyword";
    }

    interface BUCKET_PATH {
        String TOTAL_WATCH_TIME = "totalWatchTime";
        String TOTAL_USER = "totalUser";
    }

    // in seconds
    interface DURATION {
        double FIVE_MIN = 300;
        double TEN_MIN = 600;
        double THIRTY_MIN = 1800;
        double TWO_HOUR = 7200;
        double FOUR_HOUR = 14400;
    }

    // in seconds
    interface  GRAPH_INTERVAL {
        int FIVE_SEC = 5;
        int TEN_SEC = 10;
        int THIRTY_SEC = 30;
        int ONE_MINUTES = 60;
        int TWO_MINUTES = 120;
        int FIVE_MIN = 300;
    }

    interface CSV_HEADER{
        String NAME = "Name";
        String FIRST_NAME = "Attendee First Name";
        String LAST_NAME = "Attendee Last Name";
        String EMAIL = "Attendee Email";
        String IS_BOOKMARKED = "Is Bookmarked";
        String REGISTERED_DATE = "Registered Date";
        String ATTENDED_LIVE = "Attended Live";
        String WATCHED_RECORDING = "Watched Recording";
        String DURATION_TIME = "Duration Time";
        String DURATION_TIME_LIVE = "Live Watch Time";
        String DURATION_TIME_RECORDED = "Recording Watch Time";
        String SESSION_DURATION ="Session Duration";
        String LIVE_ATTENDANCE = "Live Attendance";
        String ENGAGEMENT = "Engagement";
        String TICKET_TYPE_NAME =  "Ticket Type";
        String DOCUMENT="Documents";
        String UNIQUE_DOWNLOADS= "Unique Document Downloads";
        String DOCUMENT_DOWNLOADED_LIST="Documents Downloaded";
        String TOTAL_DOCUMENT_DOWNLOADED= "Total Document Downloads";
        String CHECKED_IN_DATE="Check-in Date";
        String  CHECKED_OUT_DATE="Check-out Date";
        String  USER_SESSION_STATUS="User session status";
    }

}
