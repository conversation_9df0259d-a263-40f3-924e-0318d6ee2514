package com.accelevents.services.elasticsearch.videoanalytics;

public class VideoAnalyticsQueryConstants //NOSONAR
{
    static final String VIDEO_ANALYTICS_TABLE_NAME = "videoanalytics";

    static final String IS_SESSION_LIVE = "isSessionLive";

    public static final String SESSION_ID = "sessionId";

    public static final String USER_ID = "userId";

    static final String AVG_WATCH_TIME = "avgWatchTime";

    public static final String WATCH_TIME = "watchTime";

    public static final String TOTAL_WATCH_TIME = "totalWatchTime";

    static final String AVG_MEETING_TIME = "avgMeetingTime";

    static final String TOTAL_MEETING_TIME = "totalMeetingTime";

    static final String TOTAL_USER = "totalUsers";

    static final String SECONDS = "seconds";

    public static final String TRUE = "true";

    public static final String LIVE_VIEW = "liveView";

    public static final String RECORDING_VIEW = "recordingView";

    public static final String RECORDING_VIEWS = "recordingViews";

    static final String MEETING_TIME = "meetingTime";

    static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    static final String TIME_UNIT_MILLISECONDS = "MILLISECONDS";

    static final int BATCH_SIZE = 100;

    static final String GRAPH_X_AXIS_LABELS = "labels";

    static final String X_AXIS = "x";

    static final String Y_AXIS = "y";

    public static final String LAST_ACTIVITY_DATE = "lastActivityDate";

    public static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

    public static final String CONTINUING_ED_DATE_FORMAT = "MMM, dd yyyy 'at' hh:mm aa ";




}
