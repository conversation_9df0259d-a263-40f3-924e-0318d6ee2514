package com.accelevents.services.elasticsearch.leaderboard;

import com.accelevents.common.dto.*;
import com.accelevents.continuing.ed.ContinuingEdConstants;
import com.accelevents.continuing.ed.dto.ContinuingEdProgressDTO;
import com.accelevents.continuing.ed.service.ContinuingEdService;
import com.accelevents.domain.*;
import com.accelevents.domain.challenge.UserChallengeRewardsTracker;
import com.accelevents.domain.enums.ChallengeType;
import com.accelevents.domain.session_speakers.KeyValue;
import com.accelevents.domain.virtual.LeaderboardPointSetting;
import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.EventCECriteriaDTO;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.ChallengeAndTierMappingRepository;
import com.accelevents.repositories.EventChallengeRepository;
import com.accelevents.repositories.EventChallengeTierRepository;
import com.accelevents.repositories.UserChallengeRewardsTrackerRepository;
import com.accelevents.ro.attendee.profile.ROAttendeeProfileService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.elasticsearch.sponsor.SponsorAnalyticsConstant;
import com.accelevents.services.repo.helper.LeaderBoarPointSettingRepoService;
import com.accelevents.services.repo.helper.UserChallengeRewardsTrackerRepoService;
import com.accelevents.session_speakers.dto.KeyValueDTO;
import com.accelevents.session_speakers.services.KeyValueService;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.JsonMapper;
import com.accelevents.dto.PageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.composite.*;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import static com.accelevents.continuing.ed.ContinuingEdConstants.REWARD_IMAGE;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.*;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.FIELD.POINTS_ELIGIBLE;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.Constants.HEADER.TOTAL_POINTS_CAMEL_CASE;

@Service
public class LeaderboardService {

    @Autowired
    private CacheStoreService cacheStoreService;

    @Autowired
    private AttendeeProfileService attendeeProfileService;

    @Autowired
    private ROAttendeeProfileService roAttendeeProfileService;

    @Autowired
    private RestHighLevelClient client;

    @Autowired
    private RestHighLevelClient gamificationESClient;

    @Autowired
    private LeaderBoarPointSettingRepoService repoService;

    @Autowired
    private UserService userService;

    @Autowired
    private ROUserService roUserService;

    @Autowired
    private TicketingHelperService ticketingHelperService;

    @Autowired
    private ChallengeConfigService challengeConfigService;

    @Autowired
    private RaffleService raffleService;

    @Autowired
    private VirtualEventService virtualEventService;

    @Autowired
    private EventChallengeRepository eventChallengeRepository;

    @Autowired
    private EventService eventService;

    @Autowired
    private KeyValueService keyValueService;

    @Autowired
    private ContinuingEdService continuingEdService;

    @Autowired
    private EventTicketsService eventTicketsService;

    @Autowired
    private UserChallengeRewardsTrackerRepoService challengeRewardsTrackerRepoService;

    @Autowired
    private Environment environment;

    private static String INDICES_TRACKING = "tracking";
    private static String INDICES_CLICKSTREAM = "clickstream";
    private static String TYPE_POINTS = "points";
    private static String TYPE_RAW = "rawdata";
    private static final String EVENT_ID_SSB = "eventId";
    private static String ATTENDEE_NOT_FOUND_LOG= "Attendee not found event {}, user {}";//NOSONAR

    private Map<Long, Map<Long, Map<Long, LeaderboardPointCount>>> cachedTrackingData = new ConcurrentHashMap<>();
    private Map<Long, Date> cachedTrackingTime = new ConcurrentHashMap<>();
    private Map<Long, Date> lastQueryTime = new ConcurrentHashMap<>();
    private Map<Long, LeaderboardPointSetting> leaderboardSettingMap = new ConcurrentHashMap<>();

    private final Logger log = LoggerFactory.getLogger(LeaderboardService.class);
    @Autowired
    private EventChallengeTierRepository eventChallengeTierRepository;
    @Autowired
    private ChallengeAndTierMappingRepository challengeAndTierMappingRepository;
    @Autowired
    private UserChallengeRewardsTrackerRepository userChallengeRewardsTrackerRepository;
    @Autowired
    private EventCECriteriaService eventCECriteriaService;


    public List<Leaderboard> findAllByEventId(Event event) throws IOException {

        String leaderBoard = (String) cacheStoreService.get("LEADER_BOARD_"+event.getEventId());
        if(StringUtils.isNotBlank(leaderBoard)){
            return JsonMapper.stringtoObject(leaderBoard,List.class);
        }

        Long eventId = event.getEventId();
        log.info("findAllByEventId event {} ", eventId);
        Date lastQueryDate = lastQueryTime.get(eventId);

        if (lastQueryDate != null && lastQueryDate.after(DateUtils.addMinutes(new Date(), -1))) {
            log.info("return cached data for event {} ", eventId);
            return prepareResponse(eventId);
        }

        final Date[] lastDate = {cachedTrackingTime.get(eventId)};
        if (lastDate[0] == null) {
            Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
            lastDate[0] = ticketing.getEventStartDate();
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                .must(QueryBuilders.matchQuery(EVENT_ID_SSB, eventId))
                .must(QueryBuilders.rangeQuery(DATE).gte(lastDate[0])))
                .sort("date", SortOrder.ASC);

        List<TrackingData> trackingData = getElasticTrackingDataFromES(eventId, lastDate[0],searchSourceBuilder);
        log.info("ES query result process start event {} ", eventId);
        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();
        trackingData.forEach(e->{
            Date leaderboardDate = e.getDate();
            if (null != leaderboardDate && (lastDate[0] == null || lastDate[0].before(leaderboardDate))) {
                lastDate[0] = leaderboardDate;
            }
        });
        stopWatchESResultProcess.stop();
        log.info("ES result process end event {} time taken {}", eventId, stopWatchESResultProcess.getTotalTimeMillis());

        if (!trackingData.isEmpty()) {
            cachedTrackingTime.put(eventId, lastDate[0]);
            Map<Long, Map<Long, LeaderboardPointCount>> savedTrackingData = cachedTrackingData.get(eventId);
            if (savedTrackingData == null) {
                savedTrackingData = new HashMap<>();
                cachedTrackingData.put(eventId, savedTrackingData);
            }
            updateUserTrackingDetail(trackingData, savedTrackingData, eventId);
        }
        lastQueryTime.put(eventId, new Date());

        List<Leaderboard> listLeaderBoard = prepareResponse(eventId);

        cacheStoreService.set("LEADER_BOARD_"+event.getEventId(), JsonMapper.convertToString(listLeaderBoard), 1, TimeUnit.MINUTES);
        return  listLeaderBoard;
    }

    List<TrackingData> getESTrackingDataForCSV(Long eventId, Date lastDate, SearchSourceBuilder searchSourceBuilder) throws IOException {
        if(virtualEventService.isNewGamificationEnabled(eventId)){
            return getGamificationESTrackingFromClickStream(eventId, lastDate, searchSourceBuilder);
        } else {
            return getElasticTrackingDataFromES(eventId, lastDate,searchSourceBuilder);
        }
    }

    private List<TrackingData> getElasticTrackingDataFromES(Long eventId, Date lastDate, SearchSourceBuilder searchSourceBuilder) throws IOException {

        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));

        log.info("get data from es for event {} lastDate {} ", eventId, lastDate);
        log.info(ES_QUERY_START_EVENT, eventId);
        final StopWatch stopWatchES = new StopWatch();
        stopWatchES.start();
        SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING);
        searchRequest.scroll(scroll);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.size(ELASTIC_SEARCH_BATCH_SIZE);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        stopWatchES.stop();
        log.info(ES_QUERY_START_EVENT_TIME_TAKEN, eventId, stopWatchES.getTotalTimeMillis());

        List<TrackingData> trackingData = new ArrayList<>();
        mapSearchResult(searchResponse, trackingData);
        String scrollId = searchResponse.getScrollId();

        while (searchResponse.getHits().getHits().length > 0) {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            mapSearchResult(searchResponse, trackingData);
        }

        clearScroll(scrollId, false);

        return trackingData;
    }

    private List<TrackingData> getGamificationESTrackingFromClickStream(Long eventId,Date lastDate, SearchSourceBuilder searchSourceBuilder) throws IOException {

        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));

        log.info("ES query start event for getGamificationESTrackingFromClickStream {} ", eventId);
        final StopWatch stopWatchES = new StopWatch();
        stopWatchES.start();
        SearchRequest searchRequest = new SearchRequest(INDICES_CLICKSTREAM).types(TYPE_RAW);
        searchRequest.scroll(scroll);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.size(ELASTIC_SEARCH_BATCH_SIZE);
        SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
        stopWatchES.stop();
        log.info("ES query end event {} time taken {} for getGamificationESTrackingFromClickStream", eventId, stopWatchES.getTotalTimeMillis());

        List<TrackingData> trackingData = new ArrayList<>();
        mapSearchResult(searchResponse, trackingData);
        String scrollId = searchResponse.getScrollId();

        while (searchResponse.getHits().getHits().length > 0) {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            searchResponse = gamificationESClient.scroll(scrollRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            mapSearchResult(searchResponse, trackingData);
        }

        clearScroll(scrollId, true);

        return trackingData;
    }

    private boolean clearScroll(String scrollId, boolean isGamification) {
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        ClearScrollResponse clearScrollResponse = null;
        try {
            clearScrollResponse = isGamification? gamificationESClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT) : client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            return clearScrollResponse.isSucceeded();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    private void mapSearchResult(SearchResponse searchResponse, List<TrackingData> trackingData) {
        for (SearchHit searchHit : searchResponse.getHits().getHits()) {
            TrackingData leaderboard = JsonMapper.stringtoObject(searchHit.getSourceAsString(), TrackingData.class);
            trackingData.add(leaderboard);
        }
    }

    /*Commenting this as it is not used      //NOSONAR
    private SearchResponse getElasticSearchResponse(Long eventId, Date lastDate, SearchSourceBuilder searchSourceBuilder) throws IOException {
        log.info("get data from es fro event {} lastDate {} ", eventId, lastDate);
        log.info("ES query start event {} ", eventId);
        final StopWatch stopWatchES = new StopWatch();
        stopWatchES.start();
        SearchRequest searchRequest = new SearchRequest(INDICES);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.size(10000);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        stopWatchES.stop();
        log.info("ES query end event {} time taken {}", eventId, stopWatchES.getTotalTimeMillis());
        return searchResponse;
    }*/


    public List<Leaderboard> findAllByEventIdWithoutCache(Long eventId) throws IOException {
        log.info("findAllByEventIdWithoutCache event {} ", eventId);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                .must(QueryBuilders.matchQuery(EVENT_ID_SSB, eventId)))
                .sort(DATE, SortOrder.ASC);
        log.info("findAllByEventIdWithoutCache query {} ", searchSourceBuilder);

        final Date[] lastDate = {new Date()};
        List<TrackingData> trackingData = getElasticTrackingDataFromES(eventId, lastDate[0], searchSourceBuilder);

        if (trackingData != null && !trackingData.isEmpty()) {
            Map<Long, Map<Long, LeaderboardPointCount>> savedTrackingData = new HashMap<>();
            cachedTrackingData.put(eventId, savedTrackingData);
            updateUserTrackingDetail(trackingData, savedTrackingData, eventId);
        }
        return prepareResponse(eventId);
    }

    public List<TrackingData> findAllByTrackingData(Long eventId) throws IOException {
        log.info("findAllByTrackingData event {} ", eventId);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                        .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                        .must(QueryBuilders.matchQuery(EVENT_ID_SSB, eventId)))
                .sort(DATE, SortOrder.ASC);
        final Date[] lastDate = {new Date()};
        return getElasticTrackingDataFromES(eventId, lastDate[0], searchSourceBuilder);
    }

    /*Commenting this as it is not used    //NOSONAR
    private List<TrackingData> getDataFromES(Long eventId, Date lastDate) throws IOException {
        log.info("get data from es fro event {} lastDate {} ", eventId, lastDate);
        SearchRequest searchRequest = new SearchRequest(INDICES);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (lastDate == null) {
            searchSourceBuilder.query(QueryBuilders.matchQuery(EVENT_ID_CAMEL_CASE, eventId));
        } else {
            searchSourceBuilder.query(QueryBuilders.boolQuery().must(QueryBuilders.matchQuery(EVENT_ID_CAMEL_CASE, eventId))
                    .must(QueryBuilders.rangeQuery(DATE).gte(lastDate)));
        }
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        List<TrackingData> trackingData = new ArrayList<>();
        for (SearchHit searchHit : searchResponse.getHits().getHits()) {
            TrackingData leaderboard = JsonMapper.stringtoObject(searchHit.getSourceAsString(), TrackingData.class);
            Date leaderboardDate = leaderboard.getDate();
            if (leaderboardDate != null) {
                if (lastDate == null) {
                    lastDate = leaderboardDate;
                } else if (lastDate.before(leaderboardDate)) {
                    lastDate = leaderboardDate;
                }
            }
            trackingData.add(leaderboard);
        }
        return trackingData;
    }*/

    private void updateUserTrackingDetail(List<TrackingData> trackingData, Map<Long, Map<Long, LeaderboardPointCount>> savedTrackingData, long eventId) {
        log.info("updateUserTrackingDetail start event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        trackingData.forEach(tracking -> {
            Map<Long, LeaderboardPointCount> leaderboardExpoMap = savedTrackingData.get(tracking.getUserId());
            if(leaderboardExpoMap == null){
                leaderboardExpoMap = new HashMap();
                savedTrackingData.put(tracking.getUserId(), leaderboardExpoMap);
            }
            LeaderboardPointCount leaderboardPointCount = leaderboardExpoMap.get(tracking.getExpoId());
            List<String> documents = tracking.getDocumentDownloads();
            Set<String> uniqueDocument = new HashSet<>();
            if (documents != null && !documents.isEmpty()) {
                uniqueDocument = documents.stream().collect(Collectors.toSet());
            }
            uniqueDocument = getUniqueDocumentSet(documents, uniqueDocument);
            if (leaderboardPointCount == null) {
                leaderboardPointCount = new LeaderboardPointCount();
                leaderboardPointCount.setMaxTimeInBooth(tracking.pullPrimitiveTimeInBooth());
                leaderboardPointCount.setChat(tracking.pullPrimitiveChat());
                leaderboardPointCount.setTotalVisit(1l);
                if (uniqueDocument != null) {
                    leaderboardPointCount.setUniqueDownload(uniqueDocument);
                }
                leaderboardPointCount.setUserId(tracking.getUserId());
                leaderboardExpoMap.put(tracking.getExpoId(), leaderboardPointCount);
            } else {
                if (leaderboardPointCount.getMaxTimeInBooth() < tracking.pullPrimitiveTimeInBooth()) {
                    leaderboardPointCount.setMaxTimeInBooth(tracking.pullPrimitiveTimeInBooth());
                }
                if (!leaderboardPointCount.isChat()) {
                    leaderboardPointCount.setChat(tracking.pullPrimitiveChat());
                }
                leaderboardPointCount.setTotalVisit(leaderboardPointCount.getTotalVisit() + 1);
                getStoredDocuments(leaderboardPointCount, uniqueDocument);
            }
        });
        stopWatch.stop();
        log.info("updateUserTrackingDetail end event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
    }

    private void getStoredDocuments(LeaderboardPointCount leaderboardPointCount, Set<String> uniqueDocument) {
        if (uniqueDocument != null) {
            Set<String> storedDocuments = leaderboardPointCount.getUniqueDownload();
            if (storedDocuments != null) {
                storedDocuments.addAll(uniqueDocument);
            } else {
                leaderboardPointCount.setUniqueDownload(uniqueDocument);
            }
        }
    }

    private List<Leaderboard> prepareResponse(Long eventId) {
        log.info("start prepareResponse event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Leaderboard> leaderboards = new ArrayList<>();
        Map<Long, Map<Long, LeaderboardPointCount>> savedTrackingData = cachedTrackingData.get(eventId);
        if (savedTrackingData != null && !savedTrackingData.isEmpty()) {
            LeaderboardPointSetting setting = getPointSetting(eventId);
            String skipUserIds = setting.getSkipUserIds();
            Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileService.getAllAttendeeProfileMapWithUserDetailsByEvent(eventId);
            savedTrackingData.values().forEach(pointCountMap -> {
                List<LeaderboardPointCount> pointCounts = new ArrayList<>(pointCountMap.values());
                LeaderboardPointCount pointCount = pointCounts.get(0);
                if(pointCount.getUserId() != null){
                    if (skipUserIds != null && skipUserIds.contains(String.valueOf(pointCount.getUserId()))) {
                        return;
                    }
                    AttendeeProfileDto attendeeProfileDto = attendeeMap.get(pointCount.getUserId());
                    if(null != attendeeProfileDto) {
                        Leaderboard leaderboard = new Leaderboard();
                        leaderboard.setFirstName(attendeeProfileDto.getFirstName());
                        leaderboard.setLastName(attendeeProfileDto.getLastName());
                        leaderboard.setEmail(attendeeProfileDto.getEmail());
                        leaderboard.setCompany(attendeeProfileDto.getCompany());
                        leaderboard.setTitle(attendeeProfileDto.getTitle());
                        leaderboard.setPhoto(attendeeProfileDto.getPhoto());
                        leaderboard.setUserId(pointCount.getUserId());
                        leaderboard.setPoint(calculatePoints(pointCounts, setting));
                        leaderboards.add(leaderboard);
                    } else {
                        log.warn(ATTENDEE_NOT_FOUND_LOG, eventId, pointCount.getUserId());
                    }
                }
            });
        }
        List<Leaderboard> points = leaderboards.stream().sorted((o1, o2) -> o2.getPoint().compareTo(o1.getPoint())).collect(Collectors.toList());
        stopWatch.stop();
        log.info("end prepareResponse event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
        return points;
    }

    private List<LeaderboardDetailed> prepareDetailedResponse(Long eventId, Map<Long, Map<Long, LeaderboardPointCount>> savedTrackingData) {
        log.info("start prepareResponse for export event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<LeaderboardDetailed> leaderboards = new ArrayList<>();
        if (savedTrackingData != null && !savedTrackingData.isEmpty()) {
            LeaderboardPointSetting setting = getPointSetting(eventId);
            String skipUserIds = setting.getSkipUserIds();
            Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileService.getAllAttendeeProfileMapWithUserDetailsByEvent(eventId);
            savedTrackingData.values().forEach(pointCountMap -> {
                List<LeaderboardPointCount> pointCounts = new ArrayList<>(pointCountMap.values());
                LeaderboardPointCount pointCount = pointCounts.get(0);
                if(pointCount.getUserId() != null){
                    if (skipUserIds != null && skipUserIds.contains(String.valueOf(pointCount.getUserId()))) {
                        return;
                    }
                    AttendeeProfileDto attendeeProfileDto = attendeeMap.get(pointCount.getUserId());
                    if(null != attendeeProfileDto) {
                        LeaderboardDetailed leaderboardDetailed = new LeaderboardDetailed();
                        leaderboardDetailed.setFirstName(attendeeProfileDto.getFirstName());
                        leaderboardDetailed.setLastName(attendeeProfileDto.getLastName());
                        leaderboardDetailed.setEmail(attendeeProfileDto.getEmail());
                        leaderboardDetailed.setCompany(attendeeProfileDto.getCompany());
                        leaderboardDetailed.setTitle(attendeeProfileDto.getTitle());
                        leaderboardDetailed.setPhoto(attendeeProfileDto.getPhoto());
                        leaderboardDetailed.setUserId(pointCount.getUserId());
                        leaderboardDetailed.setEmail(attendeeProfileDto.getEmail());
                        calculatePointsDetail(pointCounts, setting, leaderboardDetailed);
                        leaderboards.add(leaderboardDetailed);
                    } else {
                        log.warn(ATTENDEE_NOT_FOUND_LOG, eventId, pointCount.getUserId());
                    }
                }
            });
        }
        List<LeaderboardDetailed> points = leaderboards.stream().sorted((o1, o2) -> o2.getPoint().compareTo(o1.getPoint())).collect(Collectors.toList());
        stopWatch.stop();
        log.info("end prepareResponse for export event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
        return points;
    }
    private LeaderboardPointSetting getPointSetting(long eventId){
        LeaderboardPointSetting setting = leaderboardSettingMap.get(eventId);
        if(setting == null) {
            setting = repoService.findByEventId(eventId);
            if(setting != null) {
                leaderboardSettingMap.put(eventId, setting);
            }
        }
        if(setting == null) {
            setting = new LeaderboardPointSetting();
            setting.setChatPoint(20);
            setting.setDownloadDocumentPoint(5);
            setting.setEventId(eventId);
            setting.setEveryVisitPoint(1);
            setting.setMinVisitTime(20);
            setting.setVisitPoint(5);
            repoService.save(setting);
            leaderboardSettingMap.put(eventId, setting);
        }
        return setting;
    }
    /*Commented this method as it is not used   //NOSONAR
    private LeaderboardPointSetting getPointSettingDummy(long eventId){
        LeaderboardPointSetting setting;
        setting = new LeaderboardPointSetting();
        setting.setChatPoint(20);
        setting.setDownloadDocumentPoint(5);
        setting.setEventId(eventId);
        setting.setEveryVisitPoint(1);
        setting.setMinVisitTime(20);
        setting.setVisitPoint(5);

        return setting;
    }*/         //NOSONAR

    //    visting --> 20 seconds, 5 points
//    activity (visit) --> 1 point per additional visit
//    downloading --> 5 points for each download
//    chat --> 20 points
    private long calculatePoints(List<LeaderboardPointCount> pointCountList, LeaderboardPointSetting setting) {
        long points = 0;
        for(LeaderboardPointCount pointCount : pointCountList){
            points = points + (pointCount.getMaxTimeInBooth() > setting.getMinVisitTime() ? setting.getVisitPoint() : 0)
                    + (pointCount.getTotalVisit() * setting.getEveryVisitPoint())
                    + (pointCount.getUniqueDownload() != null ? (pointCount.getUniqueDownload().size() * setting.getDownloadDocumentPoint()) : 0)
                    + (pointCount.isChat() ? setting.getChatPoint() : 0);
        }
        return points;
    }

    private void calculatePointsDetail(List<LeaderboardPointCount> pointCountList, LeaderboardPointSetting setting, LeaderboardDetailed leaderboardDetailed) {
        long pointsEarnedViaVistingBooth = 0;
        long pointsEarnedViaRevisitingBooth = 0;
        long pointsEarnedBViaDownloadingDocuments = 0;
        long pointsEarnedViaChat = 0;

        for(LeaderboardPointCount pointCount : pointCountList){
            pointsEarnedViaVistingBooth = pointsEarnedViaVistingBooth + (pointCount.getMaxTimeInBooth() > setting.getMinVisitTime() ? setting.getVisitPoint() : 0);
            pointsEarnedViaRevisitingBooth = pointsEarnedViaRevisitingBooth + (pointCount.getTotalVisit() * setting.getEveryVisitPoint());
            pointsEarnedBViaDownloadingDocuments = pointsEarnedBViaDownloadingDocuments + (pointCount.getUniqueDownload() != null ? (pointCount.getUniqueDownload().size() * setting.getDownloadDocumentPoint()) : 0);
            pointsEarnedViaChat = pointsEarnedViaChat +(pointCount.isChat() ? setting.getChatPoint() : 0);
        }
        leaderboardDetailed.setTotalPointsVisitingBooth(pointsEarnedViaVistingBooth + pointsEarnedViaRevisitingBooth);
        leaderboardDetailed.setPointsRevisitingBooth(pointsEarnedViaRevisitingBooth);
        leaderboardDetailed.setPointsDownloadingDocuments(pointsEarnedBViaDownloadingDocuments);
        leaderboardDetailed.setPointsChat(pointsEarnedViaChat);
        leaderboardDetailed.setPoint(pointsEarnedViaVistingBooth + pointsEarnedViaRevisitingBooth + pointsEarnedBViaDownloadingDocuments + pointsEarnedViaChat);
    }


    private AttendeeProfileDto dummyData() {
        AttendeeProfileDto attendeeDto = new AttendeeProfileDto();
        attendeeDto.setLastName("last");
        attendeeDto.setFirstName("first");
        attendeeDto.setTitle("title");
        attendeeDto.setCompany("Bril");
        attendeeDto.setCountry("IN");
        return attendeeDto;
    }
    public void flushSettingData(long eventId){
        leaderboardSettingMap.remove(eventId);
    }

    public void resetGamificationData(long eventId){
        cachedTrackingTime.remove(eventId);
        lastQueryTime.remove(eventId);
        cachedTrackingData.remove(eventId);
    }

    public List<LeaderboardDetailed> findLeaderboardScoreDetailsForCsv(Event event) throws IOException {
        Long eventId = event.getEventId();
        log.info("findLeaderboardScoreDetailsForCsv event {} ", eventId);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Date lastDate = ticketing.getEventStartDate();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                .must(QueryBuilders.matchQuery(EVENT_ID_SSB, eventId))
                .must(QueryBuilders.rangeQuery(DATE).gte(lastDate)))
                .sort("date", SortOrder.ASC);

        log.info("ES query result process start for export event {} ", eventId);
        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();
        List<TrackingData> trackingData = getESTrackingDataForCSV(eventId,lastDate,searchSourceBuilder);
        stopWatchESResultProcess.stop();
        log.info(ES_RESULT_PROCESS_EVENT_TIME_TAKEN_FOR_EXPORT, eventId, stopWatchESResultProcess.getTotalTimeMillis());

        Map<Long, Map<Long, LeaderboardPointCount>> savedTrackingData = null;

        if (trackingData != null && !trackingData.isEmpty()) {
            savedTrackingData = new HashMap<>();
            updateUserTrackingDetail(trackingData, savedTrackingData, eventId);
        }
        return prepareDetailedResponse(eventId, savedTrackingData);
    }


    private List<TrackingData> findTrackingDataForGamificationCSV(Event event) throws IOException {

        final StopWatch stopWatchES = new StopWatch();
        stopWatchES.start();
        Date eventStartDate = getEventStartDate(event);
        log.info("findTrackingDataForGamificationCSV ES query start for eventId {} ", event.getEventId());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                .must(QueryBuilders.matchQuery(EVENT_ID_SSB, event.getEventId()))
                .must(QueryBuilders.rangeQuery(DATE).gte(eventStartDate.getTime()))
                .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0)));
        searchSourceBuilder.size(ELASTIC_SEARCH_BATCH_SIZE);

        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));

        SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).types(TYPE_POINTS);
        searchRequest.scroll(scroll);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);

        List<TrackingData> trackingData = new ArrayList<>();
        mapSearchResult(searchResponse, trackingData);
        String scrollId = searchResponse.getScrollId();

        while (searchResponse.getHits().getHits().length > 0) {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            searchResponse = gamificationESClient.scroll(scrollRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            mapSearchResult(searchResponse, trackingData);
        }
        clearScroll(scrollId, true);
        stopWatchES.stop();
        log.info("findTrackingDataForGamificationCSV ES query end for eventId {} time taken {}, trackingData size {}", event.getEventId(), stopWatchES.getTotalTimeMillis(), trackingData.size());

        return trackingData;
    }

    public List<LeaderboardDetailed> getLeaderboardScoreDetails(Event event) throws IOException {
        Long eventId = event.getEventId();
        log.info("Start fetch leaderboard score details eventId {}", eventId);

        List<TrackingData> trackingDataList = findTrackingDataForGamificationCSV(event);

        log.info("TrackingData successfully fetched size {}", trackingDataList.size());
        // Create user wise tracking data list
        Map<Long, List<TrackingData>> userTrackingDataMap = trackingDataList.stream().collect(Collectors.groupingBy(TrackingData::getUserId));
        log.info("User wise TrackingData list generated size {}", userTrackingDataMap.size());
        // Get challenges
        List<EventChallengeDTO> challengeDTOS = challengeConfigService.getAllEventChallengeDetails(event);
        Map<Long, EventChallengeDTO> eventChallengeDetailMap = challengeDTOS.stream().collect(Collectors.toMap(e ->e.getEventChallengeId(), e -> e));
        Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileService.getAllAttendeeProfileMapWithUserDetailsByEvent(eventId);

        List<LeaderboardDetailed> leaderboardList = new ArrayList<>();
        userTrackingDataMap.forEach((userId,trackingData) -> {
            AttendeeProfileDto attendeeProfileDto = attendeeMap.get(userId);
            if(null != attendeeProfileDto) {
                LeaderboardDetailed leaderboardDetailed = new LeaderboardDetailed();
                leaderboardDetailed.setUserId(userId);
                leaderboardDetailed.setFirstName(attendeeProfileDto.getFirstName());
                leaderboardDetailed.setLastName(attendeeProfileDto.getLastName());
                leaderboardDetailed.setTitle(attendeeProfileDto.getTitle());
                leaderboardDetailed.setPhoto(attendeeProfileDto.getPhoto());
                leaderboardDetailed.setCompany(attendeeProfileDto.getCompany());
                leaderboardDetailed.setEmail(attendeeProfileDto.getEmail());
                calculateChallengePointsDetail(trackingData, eventChallengeDetailMap, leaderboardDetailed, eventId);
                leaderboardList.add(leaderboardDetailed);
            } else {
                log.warn(ATTENDEE_NOT_FOUND_LOG, eventId, userId);
            }
        });
        log.info("End fetch leaderboard score details leaderboardList size {}", leaderboardList.size());
        return leaderboardList;
    }

    private void calculateChallengePointsDetail(List<TrackingData> trackingDataList,//NOSONAR
                                               Map<Long, EventChallengeDTO> eventChallengeDetailMap,
                                               LeaderboardDetailed leaderboardDetailed, Long eventId) {

        log.info("Start calculate challenge points for userId {}  eventId {}", leaderboardDetailed.getUserId(), eventId);
        long pointsEarnedViaVistingBooth = 0;
        long pointsEarnedViaRevisitingBooth = 0;
        long pointsEarnedViaDownloadingDocuments = 0;
        long pointsEarnedViaChat = 0;

        long pointsEarnedViaSessionVistingBooth = 0;
        long pointsEarnedViaSessionRevisitingBooth = 0;
        long pointsEarnedViaSessionVideoWatch = 0;
        long pointsEarnedViaSessionRecordingVideoWatch = 0;
        long pointsEarnedViaSessionChat = 0;
        long pointsEarnedViaSessionAskQuestion = 0;
        long pointsEarnedViaSessionPoll = 0;

        long pointsEarnedViaMatch = 0;
        long pointsEarnedViaConnection = 0;
        long pointsEarnedViaVideoWatch = 0;
        long pointsEarnedViaLinkClicked = 0;
        long pointsEarnedViaRequestMeeting = 0;

        long pointsEarnedViaTicketPurchase = 0;
        long pointsEarnedViaEventCheckIn = 0;

        long pointsEarnedViaQRScan = 0;
        long pointsEarnedViaUserConnection = 0;
        long pointsEarnedViaCompletingProfile = 0;

        long pointsEarnedViaSurveySubmission = 0;

        long totalPoints = 0;

        for (TrackingData trackingData : trackingDataList) {

            Long challengeId = (Long) trackingData.getChallengeId();
            EventChallengeDTO challengeDTO = null!=eventChallengeDetailMap.get(challengeId) ? eventChallengeDetailMap.get(challengeId) : new EventChallengeDTO();

            // Points for each challenge action
            int visitPoint = 0;
            int perVisitPoint = 0;
            int documentDownloadPoint = 0;
            int linkClickedPoint = 0;
            int chatPoint = 0;
            int requestMeetingPoint = 0;
            int videoWatchPoint = 0;
            int recordingWatchPoint = 0;
            int ticketPurchasePoint = 0;
            int checkInPoint = 0;
            int askQuestionPoint = 0;
            int pollPoint = 0;
            int qrScanPoint =0;
            int connectionPoint = 0;
            int completingProfilePoint = 0;
            int surveySubmitPoint = 0;

            // Get challenge action point and set values
            for (Map<String, Object> action : challengeDTO.getAction()) {
                if(ChallengeConstants.ACTION_VISIT.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    visitPoint = (int) action.get(POINT);
                    perVisitPoint = (int) action.get(PER_VISIT_POINT);
                } else if(ChallengeConstants.ACTION_WATCH.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    videoWatchPoint = (int) action.get(POINT);
                    recordingWatchPoint = (int) action.getOrDefault(ChallengeConstants.RECORDING_WATCH_POINT, 0);
                } else if(ChallengeConstants.ACTION_CHAT.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    chatPoint = (int) action.get(POINT);
                }  else if(ChallengeConstants.ACTION_LINK_CLICK.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    linkClickedPoint = (int) action.get(POINT);
                } else if(ChallengeConstants.ACTION_DOCUMENT_DOWNLOAD.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    documentDownloadPoint = (int) action.get(POINT);
                } else if(ChallengeConstants.ACTION_REQUEST_MEETING.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    requestMeetingPoint = (int) action.get(POINT);
                } else if(ChallengeConstants.TICKET_PURCHASE.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    ticketPurchasePoint = (int) action.get(POINT);
                } else if(ChallengeConstants.CHECK_IN.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))){
                    checkInPoint = (int) action.get(POINT);
                }else if (ChallengeConstants.ACTION_ASKQUESTION.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))) {
                    askQuestionPoint = (int) action.get(POINT);
                }else if (ChallengeConstants.ACTION_POLL.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))) {
                    pollPoint = (int) action.get(POINT);
                }else if (ChallengeConstants.QRSCAN.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))) {
                    qrScanPoint = (int) action.get(POINT);
                }else if (ChallengeConstants.ACTION_USER_CONNECTION.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))) {
                    connectionPoint = (int) action.get(POINT);
                }else if (ChallengeConstants.ACTION_COMPLETE_PROFILE.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))) {
                    completingProfilePoint = (int) action.get(POINT);
                }else if (ChallengeConstants.ACTION_SUBMIT.equalsIgnoreCase(String.valueOf(action.get(ChallengeConstants.NAME)))) {
                    surveySubmitPoint = (int) action.get(POINT);
                }
            }
            log.info("Successfully get configured points from challenges for challengeId {}, eventId {}", challengeId, eventId);

            // Make double points for scavenger hunt challenge
            if (ChallengeType.SCAVENGER_HUNT.equals(challengeDTO.getType())) {
                visitPoint *= 2;
                perVisitPoint *= 2;
                videoWatchPoint *= 2;
                recordingWatchPoint *= 2;
                documentDownloadPoint *= 2;
                linkClickedPoint *= 2;
                chatPoint *= 2;
                requestMeetingPoint *= 2;
                askQuestionPoint*=2;
                pollPoint*=2;
                qrScanPoint*=2;
                connectionPoint*=2;
                completingProfilePoint*=2;
                surveySubmitPoint *=2;
            }

            log.info("Start calculating points for challengeId {} eventId {}", challengeId, eventId);
            // Calculate points earned by Expo challenge action
            pointsEarnedViaDownloadingDocuments += documentDownloadPoint * (CollectionUtils.isNotEmpty(trackingData.getDocumentDownloads()) ? trackingData.getDocumentDownloads().size() : 0);
            pointsEarnedViaLinkClicked += linkClickedPoint * (CollectionUtils.isNotEmpty(trackingData.getLinkClicked()) ? trackingData.getLinkClicked().size() : 0);
            pointsEarnedViaRequestMeeting += Boolean.TRUE.equals(trackingData.getRequestMeeting()) ? requestMeetingPoint : 0;

            if(LeaderBoardConstant.AREA.EXPO.equalsIgnoreCase(trackingData.getArea())) {
                pointsEarnedViaChat += Boolean.TRUE.equals(trackingData.getChat()) ?  chatPoint : 0;
                if(Boolean.TRUE.equals(trackingData.getTimeInBoothPointsSet())) {
                    pointsEarnedViaVistingBooth += visitPoint;
                }
                pointsEarnedViaRevisitingBooth += trackingData.getTotalVisit() != null && trackingData.getTotalVisit() > 0 ? (trackingData.getTotalVisit() * perVisitPoint) :0;
                pointsEarnedViaVideoWatch += Boolean.TRUE.equals(trackingData.getVideoPointsSet()) ? videoWatchPoint : 0;
            } else if(LeaderBoardConstant.AREA.SESSIONS.equalsIgnoreCase(trackingData.getArea())) {
                // Calculate points earned by Session challenge action
                if(Boolean.TRUE.equals(trackingData.getTimeInBoothPointsSet())) {
                    pointsEarnedViaSessionVistingBooth += visitPoint;
                }
                pointsEarnedViaSessionRevisitingBooth += trackingData.getTotalVisit() != null && trackingData.getTotalVisit() > 0 ? (trackingData.getTotalVisit() * perVisitPoint) :0;
                pointsEarnedViaSessionVideoWatch += Boolean.TRUE.equals(trackingData.getVideoPointsSet()) ? videoWatchPoint : 0;
                pointsEarnedViaSessionRecordingVideoWatch += Boolean.TRUE.equals(trackingData.getRecordingVideoPointsSet()) ? recordingWatchPoint : 0;
                pointsEarnedViaSessionChat += Boolean.TRUE.equals(trackingData.getChat()) ?  chatPoint : 0;
                pointsEarnedViaSessionAskQuestion += Boolean.TRUE.equals(trackingData.getAskQuestion()) ? askQuestionPoint : 0;
                pointsEarnedViaSessionPoll += Boolean.TRUE.equals(trackingData.getPoll()) ? pollPoint : 0;

            }else if(AREA.PROFILE.equalsIgnoreCase(trackingData.getArea())){
                pointsEarnedViaUserConnection += trackingData.getTotalConnections() != null && trackingData.getTotalConnections() > 0 ? (trackingData.getTotalConnections() * connectionPoint) :0;
                pointsEarnedViaCompletingProfile += trackingData.getCompleteProfile() != null && trackingData.getCompleteProfile() ? completingProfilePoint : 0;
            }else if(AREA.SURVEY.equalsIgnoreCase(trackingData.getArea())){
                pointsEarnedViaSurveySubmission += trackingData.getSubmitSurvey() != null && trackingData.getSubmitSurvey() ? surveySubmitPoint :0;
            }

            // Calculate points earned by networking challenge action
            pointsEarnedViaMatch += (trackingData.getMatchPoints() != null ? trackingData.getMatchPoints() : 0);
            pointsEarnedViaConnection += (trackingData.getConnectionPoints() != null  ? trackingData.getConnectionPoints() : 0);

            // Calculate Early bird challenge points
            pointsEarnedViaTicketPurchase += Boolean.TRUE.equals(trackingData.getTicketPurchase()) ? ticketPurchasePoint : 0;
            pointsEarnedViaEventCheckIn += Boolean.TRUE.equals(trackingData.getEventCheckIn()) ? checkInPoint : 0;
            pointsEarnedViaQRScan += Boolean.TRUE.equals(trackingData.getQrScan()) ? qrScanPoint : 0;

            totalPoints += trackingData.getTotalPoints();
            log.info("End calculating points for challengeId {}, eventId {}", eventId);
        };

        // Set final calculated data into LeaderboardDetailed
        leaderboardDetailed.setTotalPointsVisitingBooth(pointsEarnedViaVistingBooth);
        leaderboardDetailed.setPointsRevisitingBooth(pointsEarnedViaRevisitingBooth);
        leaderboardDetailed.setPointsVideoWatch(pointsEarnedViaVideoWatch);
        leaderboardDetailed.setPointsDownloadingDocuments(pointsEarnedViaDownloadingDocuments);
        leaderboardDetailed.setPointsChat(pointsEarnedViaChat);
        leaderboardDetailed.setPointsLinkClick(pointsEarnedViaLinkClicked);
        leaderboardDetailed.setPointsRequestMeeting(pointsEarnedViaRequestMeeting);

        leaderboardDetailed.setPointsVisitingSession(pointsEarnedViaSessionVistingBooth);
        leaderboardDetailed.setPointsRevisitingSession(pointsEarnedViaSessionRevisitingBooth);
        leaderboardDetailed.setPointsVideoWatchSession(pointsEarnedViaSessionVideoWatch);
        leaderboardDetailed.setPointsRecordingVideoWatchSession(pointsEarnedViaSessionRecordingVideoWatch);
        leaderboardDetailed.setPointsSessionChat(pointsEarnedViaSessionChat);
        leaderboardDetailed.setPointsSessionAskQuestion(pointsEarnedViaSessionAskQuestion);
        leaderboardDetailed.setPointsSessionPoll(pointsEarnedViaSessionPoll);

        leaderboardDetailed.setPointsNetworkingMatch(pointsEarnedViaMatch);
        leaderboardDetailed.setPointsNetworkingConnection(pointsEarnedViaConnection);
        leaderboardDetailed.setPointsEventCheckIn(pointsEarnedViaEventCheckIn);
        leaderboardDetailed.setPointsTicketPurchase(pointsEarnedViaTicketPurchase);
        leaderboardDetailed.setPointsQRScan(pointsEarnedViaQRScan);
        leaderboardDetailed.setPointsConnection(pointsEarnedViaUserConnection);
        leaderboardDetailed.setPointsProfileCompletion(pointsEarnedViaCompletingProfile);

        leaderboardDetailed.setPointsSurveySubmission(pointsEarnedViaSurveySubmission);

        leaderboardDetailed.setPoint(totalPoints);

        log.info("End calculate challenge points for userId {}  eventId {}, totalPoints {}", leaderboardDetailed.getUserId(), eventId, totalPoints);
    }

    /**
     * Method used for return List of User details who have attended Exhibition and return user's Exhibition activity like DocumentDownload,totalTimeInBooth,linkClicked,video played or not
     * @param event
     * @param expoId
     * @return
     * @throws IOException
     */
    public List<AttendeeExpoEngagementDetail> getAttendeeExpoEngagementDetail(Event event, Long expoId) {
        Long eventId = event.getEventId();
        log.info("getAttendeeExpoEngagementDetail event {} ", eventId);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Date lastDate = ticketing.getEventStartDate();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .must(QueryBuilders.matchQuery("eventId", eventId))
                .must(QueryBuilders.matchQuery("expoId", expoId))
                .must(QueryBuilders.rangeQuery("date").gte(lastDate)))
                .sort("date", SortOrder.ASC);

        log.info("ES query result process start for export event {} and expoId {} ", eventId,expoId);
        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();
        List<TrackingData> trackingData = null;
        try {
            trackingData = getESTrackingDataForCSV(eventId,lastDate,searchSourceBuilder);
            stopWatchESResultProcess.stop();
            log.info("ES result process end event {} time taken for export {}", eventId, stopWatchESResultProcess.getTotalTimeMillis());

            Map<Long, AttendeeExpoEngagementDetail> savedTrackingData = null;

            if (trackingData != null && !trackingData.isEmpty()) {
                savedTrackingData = new HashMap<>();
                updateAttendeeExpoEngagementDetail(trackingData, savedTrackingData, eventId);
            }
            return prepareAttendeeExpoEngagementDetail(eventId, savedTrackingData);
        } catch (IOException e) {
            e.printStackTrace();
        }


        return Collections.emptyList();
    }

    private void updateAttendeeExpoEngagementDetail(List<TrackingData> trackingData, Map<Long, AttendeeExpoEngagementDetail> savedTrackingData, long eventId) {
        log.info("updateAttendeeExpoEngagementDetail start event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        trackingData.forEach(tracking -> {
            AttendeeExpoEngagementDetail attendeeExpoEngagementDetail = savedTrackingData.get(tracking.getUserId());
            List<String> documents = tracking.getDocumentDownloads();
            Set<String> uniqueDocument = new HashSet<>();
            uniqueDocument = getUniqueDocumentSet(documents, uniqueDocument);
            List<String> linkClicked = tracking.getLinkClicked();
            Set<String> uniqueLinkClicked = new HashSet<>();
            uniqueLinkClicked = getUniqueLinkClicked(linkClicked, uniqueLinkClicked);

            if (attendeeExpoEngagementDetail == null) {
                attendeeExpoEngagementDetail = new AttendeeExpoEngagementDetail();
                attendeeExpoEngagementDetail.setUniqueDocumentDownload(uniqueDocument);
                attendeeExpoEngagementDetail.setTotalTimeInBooth(tracking.pullPrimitiveTimeInBooth());
                attendeeExpoEngagementDetail.setUserId(tracking.getUserId());
                attendeeExpoEngagementDetail.setUniqueLinkClicked(uniqueLinkClicked);
                attendeeExpoEngagementDetail.setGameType(tracking.getGameType());
                attendeeExpoEngagementDetail.setChat(tracking.pullPrimitiveChat());
                attendeeExpoEngagementDetail.setVideoPlayed(tracking.pullPrimitiveVideoPlayed());
                savedTrackingData.put(tracking.getUserId(), attendeeExpoEngagementDetail);
            } else {
                attendeeExpoEngagementDetail.setTotalTimeInBooth(attendeeExpoEngagementDetail.getTotalTimeInBooth() + tracking.pullPrimitiveTimeInBooth());
                attendeeExpoEngagementDetail.getUniqueDocumentDownload().addAll(uniqueDocument);
                attendeeExpoEngagementDetail.getUniqueLinkClicked().addAll(uniqueLinkClicked);
                if(StringUtils.isNotBlank(tracking.getGameType()) && StringUtils.isNotBlank(attendeeExpoEngagementDetail.getGameType())){
                    attendeeExpoEngagementDetail.setGameType(attendeeExpoEngagementDetail.getGameType().concat(" , "+tracking.getGameType()));
                }else{
                    attendeeExpoEngagementDetail.setGameType(tracking.getGameType());
                }
                if(Boolean.FALSE.equals(attendeeExpoEngagementDetail.getChat())){
                    attendeeExpoEngagementDetail.setChat(tracking.pullPrimitiveChat());
                }
                if(Boolean.FALSE.equals(attendeeExpoEngagementDetail.getVideoPlayed())){
                    attendeeExpoEngagementDetail.setVideoPlayed(tracking.pullPrimitiveVideoPlayed());
                }
                savedTrackingData.put(tracking.getUserId(), attendeeExpoEngagementDetail);
            }
        });
        stopWatch.stop();
        log.info("updateAttendeeExpoEngagementDetail end event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
    }

    private Set<String> getUniqueLinkClicked(List<String> linkClicked, Set<String> uniqueLinkClicked) {
        if (linkClicked != null && !linkClicked.isEmpty()) {
            uniqueLinkClicked = linkClicked.stream().collect(Collectors.toSet());
        }
        return uniqueLinkClicked;
    }

    private Set<String> getUniqueDocumentSet(List<String> documents, Set<String> uniqueDocument) {
        if (documents != null && !documents.isEmpty()) {
            uniqueDocument = documents.stream().collect(Collectors.toSet());
        }
        return uniqueDocument;
    }

    private List<AttendeeExpoEngagementDetail> prepareAttendeeExpoEngagementDetail(Long eventId, Map<Long, AttendeeExpoEngagementDetail> savedTrackingData) {
        log.info("start prepareAttendeeExpoEngagementDetail for export event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AttendeeExpoEngagementDetail> attendeeExpoEngagementDetails = new ArrayList<>();
        if (savedTrackingData != null && !savedTrackingData.isEmpty()) {
            Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileService.getAllAttendeeProfileMapWithUserDetailsByEvent(eventId);
            savedTrackingData.values().forEach(attendeeCountMap -> {
                if(attendeeCountMap.getUserId()!=null) {
                    AttendeeProfileDto attendeeProfileDto = attendeeMap.get(attendeeCountMap.getUserId());
                    if(null != attendeeProfileDto) {
                        AttendeeExpoEngagementDetail attendeeExpoEngagementDetail = null;
                        attendeeExpoEngagementDetail = attendeeExpoEngagementDetail.toEntity(attendeeProfileDto, attendeeCountMap);
                        attendeeExpoEngagementDetails.add(attendeeExpoEngagementDetail);
                    } else {
                        log.warn(ATTENDEE_NOT_FOUND_LOG, eventId, attendeeCountMap.getUserId());
                    }
                }else{
                    log.info("userId null received from ES {}", GeneralUtils.getStackTrace());
                }
            });
        }
        List<AttendeeExpoEngagementDetail> attendeeExpoEngagementDetailList = attendeeExpoEngagementDetails.stream()
                .sorted(nameComprator()).collect(Collectors.toList());
        stopWatch.stop();
        log.info("end prepareAttendeeExpoEngagementDetail for export event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
        return attendeeExpoEngagementDetailList;
    }

    private Comparator<AttendeeExpoEngagementDetail> nameComprator() {
        return (o1, o2) -> o2.getFirstName().compareTo(o1.getFirstName());
    }

    private Comparator<ChallengeLeaderBoardData> challengeLeaderBoardDataComparator(){
        return (o1, o2) -> o2.getFirstName().compareTo(o1.getFirstName());
    }

    public DataTableResponse getLeaderBoardDataByChallengeId(Event event, Long challengeId, PageSizeSearchObj pageObj) throws IOException {
        Long eventId = event.getEventId();
        log.info("getLeaderBoardDataByChallengeId event {} challengeId {}", eventId, challengeId);

        Optional<EventChallengeDetail> eventChallengeDetailOptional = eventChallengeRepository.findById(challengeId);
        if (eventChallengeDetailOptional.isEmpty()) {
            return new DataTableResponse();
        }


        Map<Long, Long> userPointsMap = new LinkedHashMap<>();
        Map<String, Object> afterKey = null;
        if (eventChallengeDetailOptional.isPresent()) {
            EventChallengeDetail eventChallengeDetail = eventChallengeDetailOptional.get();
            while (true) {
                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                BoolQueryBuilder query = QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                        .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                        .must(QueryBuilders.matchQuery(CHALLENGE_ID, challengeId))
                        .must(QueryBuilders.rangeQuery(DATE).lte(eventChallengeDetail.getEndDate()).gte(eventChallengeDetail.getStartDate()))
                        .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0));

                searchSourceBuilder.query(query);
                searchSourceBuilder.size(0);

                CompositeAggregationBuilder compositeAgg = AggregationBuilders.composite(ES_COMPOSITE_AGGREGATION, List.of(
                                new TermsValuesSourceBuilder(USER_ID).field(USER_ID)
                        ))
                        .subAggregation(AggregationBuilders.sum(TOTAL_POINTS_CAMEL_CASE).field(TOTAL_POINTS_CAMEL_CASE))
                        .size(ES_COMPOSITE_AGG_SIZE);

                if (afterKey != null) {
                    compositeAgg.aggregateAfter(afterKey);
                }

                searchSourceBuilder.aggregation(compositeAgg);
                SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).source(searchSourceBuilder);
                SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);

                ParsedComposite aggregation = searchResponse.getAggregations().get(ES_COMPOSITE_AGGREGATION);
                for (CompositeAggregation.Bucket bucket : aggregation.getBuckets()) {
                    long userId = ((Number) bucket.getKey().get(USER_ID)).longValue();
                    long totalPoints = (long) ((ParsedSum) bucket.getAggregations().get(TOTAL_POINTS_CAMEL_CASE)).getValue();
                    userPointsMap.put(userId, totalPoints);
                }
                afterKey = aggregation.afterKey();
                if (afterKey == null || aggregation.getBuckets().isEmpty()) {
                    break;
                }
            }

            LinkedHashMap<Long, Long> sortedUserPointMap = userPointsMap.entrySet()
                    .stream()
                    .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new
                    ));

            log.info("getLeaderBoardDataByChallengeId complete. eventId {}, challengeId {}, users fetched {}", eventId, challengeId, userPointsMap.size());

            return getAggregateLeaderBoardData(eventId, sortedUserPointMap, pageObj);
        } else {
            return new DataTableResponse();
        }
    }

    public List<ChallengesTotalPoint> getLeaderBoardDataByUser(Event event,Long userId) throws IOException {
        Long eventId = event.getEventId();
        log.info("getLeaderBoardDataByUser event {} userId {}", eventId,userId);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                .must(QueryBuilders.matchQuery(FIELD.USER_ID, userId))
                .must(QueryBuilders.matchQuery(FIELD.EVENT_ID, eventId))
                .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0)))
                .sort(TOTAL_POINTS_CAMEL_CASE, SortOrder.ASC);
        TermsAggregationBuilder pointAggregation = AggregationBuilders.terms(FIELD.CHALLENGE_ID).field(FIELD.CHALLENGE_ID)
                .subAggregation(AggregationBuilders.sum(TOTAL_POINTS_CAMEL_CASE).field(TOTAL_POINTS_CAMEL_CASE))
                .size(ELASTIC_SEARCH_BATCH_SIZE); // Aggregation's default size is 10 so manually added size

        searchSourceBuilder.aggregation(pointAggregation);
        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();

        SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).types(TYPE_POINTS);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.size(ELASTIC_SEARCH_BATCH_SIZE);
        SearchResponse response = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
        stopWatchESResultProcess.stop();
        log.info("ES result process end event {} userId {} time taken for get user challenge point {}", eventId, userId, stopWatchESResultProcess.getTotalTimeMillis());
        return mapChallenge(mapUserPoint(response),eventId);

    }

    private List<ChallengesTotalPoint> mapChallenge(Map<Long, Long> mapUserPoint,Long eventId) {

        List<ChallengesTotalPoint> challengesTotalPoints = new ArrayList<>();
        List<ChallengeDetail> challengeDetails = eventChallengeRepository.findAllByEventId(eventId);
        challengeDetails.forEach(challengeDetail -> {
            if(mapUserPoint.get(challengeDetail.getEventChallengeId()) != null){
                challengesTotalPoints.add(new ChallengesTotalPoint(challengeDetail.getEventChallengeId(),challengeDetail.getName(),mapUserPoint.get(challengeDetail.getEventChallengeId())));
            }
        });

        return challengesTotalPoints;
    }

    private Map<Long,Long> mapUserPoint(SearchResponse response){
        Map<Long, Long> userPoint = new HashMap<>();
        if(response.getAggregations()!=null && response.getAggregations().get(FIELD.CHALLENGE_ID)!=null) {
            Terms userIdTerms = response.getAggregations().get(FIELD.CHALLENGE_ID);
            if (userIdTerms != null) {
                for (Terms.Bucket entry : userIdTerms.getBuckets()) {
                    Sum pointAggregationResult = entry.getAggregations().get(TOTAL_POINTS_CAMEL_CASE);
                    userPoint.put((Long) entry.getKey(),  (long)pointAggregationResult.getValue());
                }
            }
        }
        return userPoint;
    }

    public DataTableResponse getLeaderBoardDataByEventId(Event event, PageSizeSearchObj pageObj,boolean executeEs,boolean executeDbBeforeEs,boolean executeDbAfterEs) throws IOException {
        Long eventId = event.getEventId();
        Date lastDate = getEventStartDate(event);
        log.info("getLeaderBoardDataByEventId using composite agg, event {} lastDate {}", eventId, lastDate);

// Check if the current Spring profile is either 'dev' or 'stage'
// If so, run diagnostic logic specific to development or staging environments
        Set<String> targetProfiles = Set.of("local","dev", "stage");
        boolean isDevOrStage = Arrays.stream(environment.getActiveProfiles())
                .map(String::toLowerCase)
                .anyMatch(targetProfiles::contains);
        if (isDevOrStage) {
            runDevStageDiagnostics(event,executeEs,executeDbBeforeEs,executeDbAfterEs); // only runs if profile is dev or stage
        }
// End of dev/stage environment check and diagnostics execution

        Map<Long, Long> userPointMap = new HashMap<>();

        Map<String, Object> afterKey = null;
        boolean hasMorePages = true;

        while (hasMorePages) {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                            .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                            .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                            .must(QueryBuilders.matchQuery(EVENT_ID, eventId))
                            .must(QueryBuilders.rangeQuery(DATE).gte(lastDate))
                            .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0)))
                    .size(0);

            List<CompositeValuesSourceBuilder<?>> sources = List.of(
                    new TermsValuesSourceBuilder(USER_ID).field(USER_ID)
            );

            CompositeAggregationBuilder compositeAgg = AggregationBuilders
                    .composite(ES_COMPOSITE_AGGREGATION, sources)
                    .size(ES_COMPOSITE_AGG_SIZE);

            if (afterKey != null) {
                compositeAgg.aggregateAfter(afterKey);
            }

            compositeAgg.subAggregation(
                    AggregationBuilders.sum(TOTAL_POINTS_CAMEL_CASE).field(TOTAL_POINTS_CAMEL_CASE)
            );

            searchSourceBuilder.aggregation(compositeAgg);

            SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING)
                    .types(TYPE_POINTS)
                    .source(searchSourceBuilder);

            SearchResponse response = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
            ParsedComposite aggregation = response.getAggregations().get(ES_COMPOSITE_AGGREGATION);

            for (CompositeAggregation.Bucket bucket : aggregation.getBuckets()) {
                Long userId = Long.valueOf(bucket.getKey().get(USER_ID).toString());
                double totalPoints = ((ParsedSum) bucket.getAggregations().get(TOTAL_POINTS_CAMEL_CASE)).getValue();
                userPointMap.put(userId, (long) totalPoints);
            }

            afterKey = aggregation.afterKey();
            hasMorePages = (afterKey != null);
            if(aggregation.getBuckets().isEmpty()){
                break;
            }
        }
        LinkedHashMap<Long, Long> sortedUserPointMap = userPointMap.entrySet()
                .stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
        log.info("ES composite agg result completed for eventId {} total user entries {}", eventId, userPointMap.size());
        return getAggregateLeaderBoardData(eventId, sortedUserPointMap, pageObj);
    }

    private void runDevStageDiagnostics(Event event,boolean executeEs,boolean executeDbBeforeEs,boolean executeDbAfterEs) {
        Long eventId = event.getEventId();

        try {
            // 🔍 Extra DB call before ES
            if(executeDbBeforeEs) {
                long preDbStart = System.currentTimeMillis();
                log.info("[DEV/STAGE] Running extra DB call BEFORE ES for eventId {}", eventId);
                List<ChallengeDetail> preEsData = challengeConfigService.getAllEventChallenges(event);
                long preDbEnd = System.currentTimeMillis();
                log.info("[DEV/STAGE] DB call BEFORE ES completed | Time taken: {} ms", preDbEnd - preDbStart);
            }
            if (executeEs) {
                // ⏳ Artificial ES delay
                SearchSourceBuilder delaySource = new SearchSourceBuilder()
                        .query(QueryBuilders.functionScoreQuery(ScoreFunctionBuilders.randomFunction())) // fake document iteration
                        .size(10000) // simulate more docs
                        .aggregation(
                                AggregationBuilders.scriptedMetric("delay_metric")
                                        .initScript(new Script("state.x = 0.0;"))
                                        .mapScript(new Script(
                                                // Each doc runs ~5-10ms of math, so 10,000 docs = ~20-30s
                                                "for (int i = 0; i < 300000; i++) { " +
                                                        "  state.x += Math.sin(i) * Math.log(i + 1); " +
                                                        "}"
                                        ))
                                        .combineScript(new Script("return state.x;"))
                                        .reduceScript(new Script("double sum = 0.0; for (s in states) { sum += s } return sum;"))
                        );

                SearchRequest delayRequest = new SearchRequest("tracking");
                delayRequest.source(delaySource);


// Execute and log
                long start = System.currentTimeMillis();
                SearchResponse response = client.search(delayRequest, RequestOptions.DEFAULT);
                long end = System.currentTimeMillis();
                log.info("[DEV/STAGE] Delay query completed. Took: {} ms", end - start);

            }
            if(executeDbAfterEs) {
                // 🔍 Extra DB call after ES
                long postDbStart = System.currentTimeMillis();
                log.info("[DEV/STAGE] Running extra DB call AFTER ES for eventId {}", eventId);
                List<ChallengeDetail> postEsData = challengeConfigService.getAllEventChallenges(event);
                long postDbEnd = System.currentTimeMillis();
                log.info("[DEV/STAGE] DB call AFTER ES completed | Time taken: {} ms", postDbEnd - postDbStart);
            }

        } catch (Exception e) {
            log.info("[DEV/STAGE] Diagnostic block failed (safe to ignore in prod): {}", e.getMessage(), e);

        }
    }



    /**
     * Get event start date with adjusted Pre Event Access minutes
     */
    private Date getEventStartDate(Event event) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(ticketing.getEventStartDate());
        calendar.add(Calendar.MINUTE, -ticketing.getPreEventAccessMinutes());
        return calendar.getTime();
    }

    private Map<Long, Long> getElasticTrackingDataByAggregation(Long eventId, SearchSourceBuilder searchSourceBuilder) throws IOException {
        Map<Long, Long> userPointMap = new LinkedHashMap<>();
        log.info("get data from es from event {} ", eventId);
        log.info("ES query start event {} ", eventId);
        final StopWatch stopWatchES = new StopWatch();
        stopWatchES.start();
        SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).types(TYPE_POINTS);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.size(0);
        SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
        mapAggregateResult(searchResponse, userPointMap);
        stopWatchES.stop();
        log.info("ES query end event {} time taken {}", eventId, stopWatchES.getTotalTimeMillis());
        return userPointMap;
    }

    private Map<Long, Map<Long, Long>> getElasticTrackingDataByChallengeIdAggregation(Long eventId, SearchSourceBuilder searchSourceBuilder) throws IOException {
        Map<Long,Map<Long, Long>> userChallengePointMap = new HashMap<>();
        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));
        log.info("get data from es from method getElasticTrackingDataByChallengeIdAggregation from event {} ", eventId);
        log.info("ES query start event {} ", eventId);
        final StopWatch stopWatchES = new StopWatch();
        stopWatchES.start();
        SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).types(TYPE_POINTS);
        searchRequest.scroll(scroll);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.size(ELASTIC_SEARCH_BATCH_SIZE);
        SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
        mapAggregateResultChallengeWise(searchResponse, userChallengePointMap);
        String scrollId = searchResponse.getScrollId();
        while (searchResponse.getHits().getHits().length > 0) {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            searchResponse = gamificationESClient.scroll(scrollRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            mapAggregateResultChallengeWise(searchResponse, userChallengePointMap);
        }
        clearScroll(scrollId, true);
        stopWatchES.stop();
        log.info("ES query from method getElasticTrackingDataByChallengeIdAggregation end event {} time taken {}", eventId, stopWatchES.getTotalTimeMillis());
        return userChallengePointMap;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private DataTableResponse getAggregateLeaderBoardData(Long eventId, Map<Long, Long> userPoints, PageSizeSearchObj pageObj) {
        log.info("start getLeaderBoardListByEventId event {} ", eventId);
        if (MapUtils.isEmpty(userPoints)) {
            log.info("No points found for users returning. userPoints {}", userPoints.size());
            return new DataTableResponse();
        }
        boolean isSearchRequest = StringUtils.isNotBlank(pageObj.getSearchWithEscapeSpecialChars());
        Map<Long, Boolean> userPresentMap = new HashMap<>();
        if (isSearchRequest) {
            userPresentMap = userService.findAttendeeIdsByEvent(eventId).stream().collect(Collectors.toMap(e -> Long.valueOf(e.longValue()), e -> true));
        }
        List<Leaderboard> allLeaderboardUsers = new ArrayList<>();
        Map<Long, Integer> userRankMap = new HashMap<>();
        int rank = 0;
        if (isSearchRequest) {
            for (Map.Entry<Long, Long> entry : userPoints.entrySet()) {
                Long userId = entry.getKey();
                if (userPresentMap.containsKey(userId)) {
                    allLeaderboardUsers.add(new Leaderboard(userId, entry.getValue(), rank++));
                    userRankMap.put(userId, rank);
                }
            }
        } else {
            for (Map.Entry<Long, Long> entry : userPoints.entrySet()) {
                allLeaderboardUsers.add(new Leaderboard(entry.getKey(), entry.getValue()));
            }
        }
        DataTableResponse dataTableResponse = prepareLeaderboard(eventId, userPoints, userRankMap, pageObj, allLeaderboardUsers);
        log.info("end getLeaderBoardListByEventId event {} result size {}", eventId, dataTableResponse.getData().size());
        return dataTableResponse;
    }

    private DataTableResponse prepareLeaderboard(Long eventId,
                                                 Map<Long, Long> userPoints,
                                                 Map<Long, Integer> userRankMap,
                                                 PageSizeSearchObj pageObj,
                                                 List<Leaderboard> allLeaderboardUsers) {
        log.info("Start preparing leaderboard event {}", eventId);
        List<Leaderboard> leaderboardUsers = new ArrayList<>();
        // Paginate the data
        PageImpl<Leaderboard> pageable = PageUtil.getPageable(allLeaderboardUsers, PageRequest.of(pageObj.getPage(), pageObj.getSize()));
        List<Long> userIds = pageable.getContent().stream().map(Leaderboard::getUserId).collect(Collectors.toList());

        long totalUsers = userPoints.size();
        if (StringUtils.isNotBlank(pageObj.getSearchWithEscapeSpecialChars())) {
            Page<User> allAttendeeByEvent = userService.getAllAttendeeByEvent(eventId, pageObj.getSearchWithEscapeSpecialChars(), pageObj.getPage(), pageObj.getSize());
            List<User> users = allAttendeeByEvent.getContent();
            totalUsers = allAttendeeByEvent.getTotalElements();
            // Get attendee profile data like Job title and company from neptune
            Map<Long, AttendeeProfileDto> attendeeMap = roAttendeeProfileService.getAttendeeProfileMapWithUserDetails(eventId, users);
            log.info("Fetched users info userIds size {}, attendeeMap size {} ", userIds.size(), attendeeMap.size());
            // Prepare leaderboard
            for (User user : users) {
                AttendeeProfileDto attendeeProfile = attendeeMap.get(user.getUserId());
                if (attendeeProfile != null && userPoints.containsKey(user.getUserId())) {
                    Leaderboard leaderboardUser = new Leaderboard();
                    Leaderboard.setAttendeeProfileInfo(leaderboardUser, attendeeProfile);
                    leaderboardUser.setEmail(StringUtils.isEmpty(attendeeProfile.getEmail()) ? user.getEmail() : attendeeProfile.getEmail());
                    leaderboardUser.setRank(userRankMap.get(user.getUserId()));
                    leaderboardUser.setPoint(userPoints.get(user.getUserId()));
                    leaderboardUsers.add(leaderboardUser);
                } else {
                    log.warn(ATTENDEE_NOT_FOUND_LOG, eventId, user.getUserId());
                }
            }
        } else {
            List<User> users = userService.findAttendeesByEventAndUserIds(eventId, userIds);
            // Get attendee profile data like Job title and company from neptune
            Map<Long, AttendeeProfileDto> attendeeMap = roAttendeeProfileService.getAttendeeProfileMapWithUserDetails(eventId, users);
            log.info("Fetched users info userIds size {}, attendeeMap size {} ", userIds.size(), attendeeMap.size());
            // Prepare leaderboard
            for (Leaderboard leaderboardUser : pageable.getContent()) {
                AttendeeProfileDto attendeeProfile = attendeeMap.get(leaderboardUser.getUserId());
                if (attendeeProfile != null) {
                    Leaderboard.setAttendeeProfileInfo(leaderboardUser, attendeeProfile);
                    leaderboardUser.setEmail(attendeeProfile.getEmail());
                    leaderboardUsers.add(leaderboardUser);
                } else {
                    log.warn(ATTENDEE_NOT_FOUND_LOG, eventId, leaderboardUser.getUserId());
                }
            }
        }
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(leaderboardUsers);
        dataTableResponse.setRecordsTotal(totalUsers);
        dataTableResponse.setRecordsFiltered(leaderboardUsers.size());
        log.info("Successfully prepared leaderboard user list for event {},  result size {}", eventId, leaderboardUsers.size());
        return dataTableResponse;
    }

    public List<ChallengeLeaderBoardData> getLeaderBoardDataByEventIdForCSV(Event event) throws IOException {
        Long eventId = event.getEventId();
        Date lastDate = getEventStartDate(event);
        log.info("getLeaderBoardDataByEventIdForCSV event {}  lastDate {}", eventId, lastDate);

        Map<Long, Map<Long, Long>> userChallengePointMap = new HashMap<>();
        Map<String, Object> afterKey = null;

        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();

        do {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                    .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                    .must(QueryBuilders.matchQuery(EVENT_ID, eventId))
                    .must(QueryBuilders.rangeQuery(DATE).gte(lastDate))
                    .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0))
            );
            searchSourceBuilder.size(0);

            List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
            sources.add(new TermsValuesSourceBuilder(USER_ID).field(USER_ID));
            sources.add(new TermsValuesSourceBuilder(CHALLENGE_ID).field(CHALLENGE_ID));

            CompositeAggregationBuilder compositeAgg = AggregationBuilders.composite(ES_COMPOSITE_AGGREGATION, sources).size(ES_COMPOSITE_AGG_SIZE);

            if (afterKey != null) {
                compositeAgg.aggregateAfter(afterKey);
            }

            compositeAgg.subAggregation(AggregationBuilders.sum(TOTAL_POINTS_CAMEL_CASE).field(TOTAL_POINTS_CAMEL_CASE));
            searchSourceBuilder.aggregation(compositeAgg);

            SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).source(searchSourceBuilder);
            SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);

            ParsedComposite parsedComposite = searchResponse.getAggregations().get(ES_COMPOSITE_AGGREGATION);

            if (parsedComposite == null || parsedComposite.getBuckets().isEmpty()) {
                break;
            } else {
                log.info("ParsedComposite aggregation found. Bucket count: {}", parsedComposite.getBuckets().size());
                for (CompositeAggregation.Bucket bucket : parsedComposite.getBuckets()) {
                    Object userIdObj = bucket.getKey().get(USER_ID);
                    Object challengeIdObj = bucket.getKey().get(CHALLENGE_ID);

                    if (userIdObj == null || challengeIdObj == null) {
                        continue;
                    }
                    try {
                        Long userId = ((Number) userIdObj).longValue();
                        Long challengeId = ((Number) challengeIdObj).longValue();

                        ParsedSum sumAgg = bucket.getAggregations().get(TOTAL_POINTS_CAMEL_CASE);
                        if (sumAgg == null) {
                            continue;
                        }
                        long points = (long) sumAgg.getValue();
                        log.info("Parsed bucket: userId={}, challengeId={}, points={}", userId, challengeId, points);

                        userChallengePointMap.computeIfAbsent(userId, k -> new HashMap<>()).put(challengeId, points);
                    } catch (Exception e) {
                        log.error("getLeaderBoardDataByEventIdForCSV | Error parsing bucket key or aggregation value. Bucket key: {}, error: {}", bucket.getKey(), e.getMessage(), e);
                    }
                }
                if (parsedComposite.afterKey() != null) {
                    afterKey = parsedComposite.afterKey();
                    log.info("Next afterKey for pagination: {}", parsedComposite.afterKey());
                } else {
                    afterKey = null;
                    log.info("No more pages to fetch (afterKey is null)");
                }
            }

        } while (afterKey != null);
        stopWatchESResultProcess.stop();
        log.info("getLeaderBoardDataByEventIdForCSV | ES result process complete for event {}. Time taken: {} ms", eventId, stopWatchESResultProcess.getTotalTimeMillis());
        return getAggregateLeaderBoardDataChallengeWise(eventId, userChallengePointMap);
    }


    private List<ChallengeLeaderBoardData> getAggregateLeaderBoardDataChallengeWise(Long eventId,Map<Long, Map<Long, Long>> userChallengePointMap) {
        log.info("start getAggregateLeaderBoardDataChallengeWise event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<ChallengeLeaderBoardData> listChallengeLeaderBoardData = new ArrayList<>();
        if (MapUtils.isNotEmpty(userChallengePointMap)) {
            Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileService.getAllAttendeeProfileMapWithUserDetailsByEvent(eventId);
            userChallengePointMap.entrySet().forEach(pointCountMap -> {
                AttendeeProfileDto attendeeProfileDto = attendeeMap.get(pointCountMap.getKey());
                if(null != attendeeProfileDto) {
                    ChallengeLeaderBoardData challengeLeaderBoardData = ChallengeLeaderBoardData.toEntity(attendeeProfileDto,pointCountMap.getValue(),pointCountMap.getKey());
                    listChallengeLeaderBoardData.add(challengeLeaderBoardData);
                } else {
                    log.warn(ATTENDEE_NOT_FOUND_LOG, eventId, pointCountMap.getKey());
                }
            });
        }
        List<ChallengeLeaderBoardData> userChallengePointList = listChallengeLeaderBoardData.stream()
                .sorted(challengeLeaderBoardDataComparator()).collect(Collectors.toList());
        stopWatch.stop();
        log.info("end getAggregateLeaderBoardDataChallengeWise event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
        return userChallengePointList;
    }

    public ChallengeLeaderBoardData getChallengeTotalPointsByEventIdUserId(Event event, long userId) throws IOException {
        log.info("Start challenge wise points for user {} event {}", userId , event.getEventId());
        Map<Long, Long> userChallengePointMap = getUserChallengePoints(event, userId);
        ChallengeLeaderBoardData challengeLeaderBoardData = getAggregateTotalPointsChallengeWise(event.getEventId(), userId, userChallengePointMap);
        log.info("End challenge wise points for user {} event {}, userChallengePointMap size {}", userId, event.getEventId(), userChallengePointMap);
        return challengeLeaderBoardData;
    }

    public Map<Long, Long> getUserChallengePoints(Event event, long userId) throws IOException {
        log.info("Start get user challenge points for user {} event {}", userId, event.getEventId());
        Long eventId = event.getEventId();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                .must(QueryBuilders.matchQuery(EVENT_ID, eventId))
                .must(QueryBuilders.matchQuery(USER_ID, userId))
                .must(QueryBuilders.rangeQuery(DATE).gte(getEventStartDate(event)))
                .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0)));

        TermsAggregationBuilder pointAggregation = AggregationBuilders.terms(CHALLENGE_ID).field(CHALLENGE_ID)
                .subAggregation(AggregationBuilders.sum(TOTAL_POINTS_CAMEL_CASE).field(TOTAL_POINTS_CAMEL_CASE))
                .size(ELASTIC_SEARCH_BATCH_SIZE);
        searchSourceBuilder.aggregation(pointAggregation);

        Map<Long, Long> userChallengePointMap = getChallengeWiseTotalPointsAggregation(eventId, searchSourceBuilder);
        log.info("End get user challenge points for user {} event {}, userChallengePointMap {}", userId, eventId, userChallengePointMap.size());
        return userChallengePointMap;
    }


    public Map<Long, Long> getUserChallengePointsWithoutCheckingEligiblePoints(Event event, long userId) throws IOException {
        log.info("Start get user challenge points for user {} event {}", userId, event.getEventId());
        Long eventId = event.getEventId();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .must(QueryBuilders.matchQuery(EVENT_ID, eventId))
                .must(QueryBuilders.matchQuery(USER_ID, userId))
                .must(QueryBuilders.rangeQuery(DATE).gte(getEventStartDate(event)))
                .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0)));

        TermsAggregationBuilder pointAggregation = AggregationBuilders.terms(CHALLENGE_ID).field(CHALLENGE_ID)
                .subAggregation(AggregationBuilders.sum(TOTAL_POINTS_CAMEL_CASE).field(TOTAL_POINTS_CAMEL_CASE))
                .size(ELASTIC_SEARCH_BATCH_SIZE);
        searchSourceBuilder.aggregation(pointAggregation);

        Map<Long, Long> userChallengePointMap = getChallengeWiseTotalPointsAggregation(eventId, searchSourceBuilder);
        log.info("End get user challenge points for user {} event {}, userChallengePointMap {}", userId, eventId, userChallengePointMap.size());
        return userChallengePointMap;
    }

    private Map<Long, Long> getChallengeWiseTotalPointsAggregation(Long eventId, SearchSourceBuilder searchSourceBuilder) throws IOException {
        Map<Long, Long> userChallengePointMap = new HashMap<>();
        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));
        log.info("get data from es from method getChallengeWiseTotalPointsAggregation from event {} ", eventId);
        log.info("ES query start event {} ", eventId);
        final StopWatch stopWatchES = new StopWatch();
        stopWatchES.start();
        SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).types(TYPE_POINTS);
        searchRequest.scroll(scroll);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.size(ELASTIC_SEARCH_BATCH_SIZE);
        SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
        mapAggregateTotalPointsChallengeWise(searchResponse, userChallengePointMap);
        String scrollId = searchResponse.getScrollId();
        while (searchResponse.getHits().getHits().length > 0) {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            searchResponse = gamificationESClient.scroll(scrollRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            mapAggregateTotalPointsChallengeWise(searchResponse, userChallengePointMap);
        }
        clearScroll(scrollId, true);
        stopWatchES.stop();
        log.info("ES query from method getChallengeWiseTotalPointsAggregation end event {} time taken {}", eventId, stopWatchES.getTotalTimeMillis());
        return userChallengePointMap;
    }

    private Map<Long,Long> mapAggregateResult(SearchResponse response,Map<Long,Long> userPointMap){
        if(response.getAggregations()!=null && response.getAggregations().get("userId")!=null) {
            Terms userId = response.getAggregations().get("userId");
            if (userId != null) {
                for (Terms.Bucket entry : userId.getBuckets()) {
                    Sum pointAggregation = entry.getAggregations().get("totalPoints");
                    userPointMap.put((Long) entry.getKey(), (long) pointAggregation.getValue());
                }
            }
        }
        return userPointMap;
    }

    private Map<Long, Map<Long, Long>> mapAggregateResultChallengeWise(SearchResponse response, Map<Long,Map<Long,Long>> userChallengePointMap){
        if(response.getAggregations()!=null && response.getAggregations().get("userId")!=null) {
            Terms userId = response.getAggregations().get("userId");
            if (userId != null) {
                for (Terms.Bucket entry : userId.getBuckets()) {
                    Map<Long,Long> challengePointMap = new HashMap<>();
                    Terms challengeId = entry.getAggregations().get(CHALLENGE_ID);
                    for(Terms.Bucket challengeAggrEntry : challengeId.getBuckets()){
                        Sum pointAggregation = challengeAggrEntry.getAggregations().get("totalPoints");
                        challengePointMap.put((Long) challengeAggrEntry.getKey(), (long) pointAggregation.getValue());
                    }
                    userChallengePointMap.put((Long) entry.getKey(),challengePointMap);
                }
            }
        }
        return userChallengePointMap;
    }

    private Map<Long, Long> mapAggregateTotalPointsChallengeWise(SearchResponse response, Map<Long,Long> userChallengePointMap){
        if(response.getAggregations()!=null && response.getAggregations().get(CHALLENGE_ID)!=null) {
            Terms challengeId = response.getAggregations().get(CHALLENGE_ID);
            if (challengeId != null) {
                for (Terms.Bucket entry : challengeId.getBuckets()) {
                    Sum pointAggregation = entry.getAggregations().get("totalPoints");
                    userChallengePointMap.put((Long) entry.getKey(), (long) pointAggregation.getValue());
                }
            }
        }
        return userChallengePointMap;
    }

    private ChallengeLeaderBoardData getAggregateTotalPointsChallengeWise(Long eventId,Long userId,Map<Long, Long> userChallengePointMap) {
        log.info("start getAggregateTotalPointsChallengeWise event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AttendeeProfileDto attendeeProfileDto = attendeeProfileService.getAttendeeProfile(String.valueOf(userId),String.valueOf(eventId));
        ChallengeLeaderBoardData challengeLeaderBoardData = ChallengeLeaderBoardData.toEntity(attendeeProfileDto,userChallengePointMap,userId);
        stopWatch.stop();
        log.info("end getAggregateTotalPointsChallengeWise event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
        return challengeLeaderBoardData;
    }

    public List<ChallengeDetail> getChallengeDetails(Event event) {
        Long eventId = event.getEventId();
        log.info("getChallengeDetails event {} ", eventId);
        return challengeConfigService.getAllEventChallenges(event);
    }

    public DataTableResponse getLeaderBoardData(Event event, PageSizeSearchObj pageObj,boolean executeEs,boolean executeDbBeforeEs,boolean executeDbAfterEs) throws IOException {
        if(virtualEventService.isNewGamificationEnabled(event.getEventId())){
            return getLeaderBoardDataByEventId(event, pageObj, executeEs, executeDbBeforeEs, executeDbAfterEs);
        } else {
            List<Leaderboard> leaderboardList = findAllByEventId(event);
            DataTableResponse dataTableResponse = new DataTableResponse();
            dataTableResponse.setData(leaderboardList);
            dataTableResponse.setRecordsTotal(leaderboardList.size());
            dataTableResponse.setRecordsFiltered(leaderboardList.size());
            return dataTableResponse;
        }
    }

    public List<TrackingData> getUserExpoTrackingData(Event event, Long expoId, Long userId){
        Long eventId = event.getEventId();
        log.info("Start getAttendeeExpoTrackingData event {} ", eventId);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Date lastDate = ticketing.getEventStartDate();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .must(QueryBuilders.matchQuery(FIELD.EVENT_ID, eventId))
                .must(QueryBuilders.matchQuery(FIELD.EXPO_ID, expoId))
                .must(QueryBuilders.rangeQuery(FIELD.DATE).gte(lastDate))
                .must(QueryBuilders.matchQuery(FIELD.USER_ID, userId))
                .mustNot(QueryBuilders.existsQuery(FIELD.VIDEO_DURATION))
                .mustNot(QueryBuilders.existsQuery(FIELD.TIME_IN_BOOTH));

        searchSourceBuilder.query(query).sort(FIELD.DATE, SortOrder.DESC);

        log.info("ES query result process start for export event {} and expoId {} ", eventId,expoId);
        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();
        List<TrackingData> trackingData = null;
        try {
            trackingData = getESTrackingDataForCSV(eventId,lastDate,searchSourceBuilder);
            stopWatchESResultProcess.stop();
            log.info("ES result process end event {} time taken for export {}", eventId, stopWatchESResultProcess.getTotalTimeMillis());
        } catch (IOException e) {
            stopWatchESResultProcess.stop();
            log.error("Exception while getAttendeeExpoTrackingData ES query for eventId {} time taken {} Exception {}", eventId, stopWatchESResultProcess.getTotalTimeMillis(), e);
        }
        log.info("End getAttendeeExpoTrackingData event {} ", eventId);
        return trackingData;
    }


    /**
     * Get total count of attendee exhibitor engagement through chat for given user
     * @param eventId
     * @param userId
     * @return
     */
    public Long getAttendeeExhibitorEngageThroughChat(Long eventId, Long userId) {
        log.info("Start Get attendee exhibitor engagement through chat event {} userId {}", eventId, userId);
        SearchRequest searchRequest = new SearchRequest(INDICES_CLICKSTREAM).types(TYPE_RAW);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery(FIELD.EVENT_ID, eventId))
                .must(QueryBuilders.matchQuery(FIELD.USER_ID, userId))
                .must(QueryBuilders.matchQuery(FIELD.AREA, LeaderBoardConstant.AREA.EXPO))
                .must(QueryBuilders.matchQuery(FIELD.CHAT, true));

        searchSourceBuilder.query(query).size(0);

        CardinalityAggregationBuilder aggregation = AggregationBuilders
                .cardinality(FIELD.EXPO_ID)
                .field(FIELD.EXPO_ID);

        searchSourceBuilder.aggregation(aggregation);
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = null;
        try {
            searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
            log.info("Successfully ES query executed of get attendee exhibitor engage through chat event {}, userId {}", eventId, userId);
        } catch (IOException e) {
            log.error("Exception in ES query while get attendee exhibitor engage through chat for eventId {}, userId {},  Exception {}", eventId, userId, e);
        }

        if (searchResponse == null){
            log.warn("Null search response found of get attendee exhibitor engage through chat event {}, userId {}", eventId, userId);
            return 0l;
        }

        Cardinality cardinalityAgg = searchResponse.getAggregations().get(FIELD.EXPO_ID);
        long exhibitorCount = cardinalityAgg.getValue();

        log.info("End Get attendee exhibitor engagement through chat event {}, userId {}, total exhibitors count {}", eventId, userId, exhibitorCount);
        return exhibitorCount;

    }

    /**
     * Delete tracking index documents by challenge id
     * @param challengeId
     * @param eventId
     * @param userId
     * @throws IOException
     */
    public void deleteTrackingDataByChallengeId(Long challengeId, Long eventId, Long userId) throws IOException {
        log.info("Start Delete tracking data ES query challengeId {}, eventId {}, userId {}", challengeId, eventId, userId);
        UpdateByQueryRequest request = new UpdateByQueryRequest(INDICES_TRACKING);
        request.setQuery(new TermQueryBuilder(CHALLENGE_ID, challengeId));

        Script script = new Script(ScriptType.INLINE,
                "painless",
                "ctx._source.recStatus = '" + REC_STATUS_DELETE + "'",
                Collections.emptyMap());
        request.setScript(script);
        BulkByScrollResponse bulkByScrollResponse = gamificationESClient.updateByQuery(request, RequestOptions.DEFAULT);
        log.info("Successfully deleted tracking data total delete records {}, challengeId {}, eventId {}, userId {}",bulkByScrollResponse.getUpdated(), challengeId, eventId, userId);
    }


    /**
     * Get leaderboard Challenge information along with top 5 points earner
     * @param event
     * @param challengeId
     * @return
     */
    public ChallengeDetail getLeaderboardChallengeDetails(Event event, Long challengeId) {
        log.info("Start Get leaderboard challenge details event {}, challenge {}", event.getEventId(), challengeId);
        EventChallengeDTO challenge = challengeConfigService.findById(challengeId, event);
        ChallengeDetail challengeDetail = new ChallengeDetail(challenge.getEventChallengeId(), challenge.getName(), challenge.getDescription(),
                challenge.getGoal(), challenge.getStartDateUtc(), challenge.getEndDateUtc(), challenge.getRewards());

        if (MapUtils.isNotEmpty(challenge.getTrigger())) {
            List<Long> trackIds = challenge.getTrigger().get(ContinuingEdConstants.TRACK);
            List<KeyValue> keyValueList = keyValueService.findAllByIds(trackIds);
            List<KeyValueDTO> keyValueDTOList = keyValueList.stream()
                    .map(track -> new KeyValueDTO(track.getId(), track.getName(), track.getType(), track.getColor(), track.getDescription()))
                    .collect(Collectors.toList());
            challengeDetail.setTracks(keyValueDTOList);
        }
        Map<Long, Long> userPointsMap = getTopNUserByChallenge(event.getEventId(), challengeId, 5);
        log.info("Top 5 points earner fetched from ES userPointsMap size {}",  userPointsMap.size());
        List<Long> userIds = new ArrayList<>(userPointsMap.keySet());

        List<User> users = roUserService.getListOfUsersByUserIds(userIds);
        Map<Long, AttendeeProfileDto> attendeeProfileMap = roAttendeeProfileService.getAttendeeProfileMapWithUserDetails(event.getEventId(), users);
        log.info("Fetched attendee profile information attendeeProfileMap size {} ", attendeeProfileMap.size());
        List<Leaderboard> leaderboards = new ArrayList<>(userPointsMap.size());

        userPointsMap.forEach((key, value) -> {
            AttendeeProfileDto attendeeProfileDto = attendeeProfileMap.get(key);
            if (null != attendeeProfileDto) {
                Leaderboard leaderboard = new Leaderboard();
                leaderboard.setFirstName(attendeeProfileDto.getFirstName());
                leaderboard.setLastName(attendeeProfileDto.getLastName());
                leaderboard.setEmail(attendeeProfileDto.getEmail());
                leaderboard.setCompany(attendeeProfileDto.getCompany());
                leaderboard.setTitle(attendeeProfileDto.getTitle());
                leaderboard.setPhoto(attendeeProfileDto.getPhoto());
                leaderboard.setUserId(key);
                leaderboard.setPoint(value);
                leaderboards.add(leaderboard);
            } else {
                log.warn("Attendee not found event {}, user {}", event.getEventId(), key);
            }
        });
        challengeDetail.setUsers(leaderboards);
        log.info("End Get leaderboard challenge details event {}, challenge {}",event.getEventId(), challengeId);
        return challengeDetail;
    }

    /**
     * Get top N points earners by challengeId
     * @param eventId
     * @param challengeId
     * @param topNUser
     * @return
     */
    public Map<Long, Long>  getTopNUserByChallenge(Long eventId, Long challengeId, int topNUser) {
        log.info("Start Get top N users by challenge event {} challenge {}", eventId, challengeId);

        Map<Long, Long> userPointsMap = new HashMap<>();
        Map<String, Object> afterKey = null;
        boolean hasMorePages = true;

        while (hasMorePages) {
            BoolQueryBuilder query = QueryBuilders.boolQuery()
                    .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                    .mustNot(QueryBuilders.termQuery(FIELD.POINTS_ELIGIBLE, false))
                    .must(QueryBuilders.matchQuery(FIELD.EVENT_ID, eventId))
                    .must(QueryBuilders.matchQuery(FIELD.CHALLENGE_ID, challengeId))
                    .must(QueryBuilders.rangeQuery(FIELD.TOTAL_POINTS).gt(0));

            List<CompositeValuesSourceBuilder<?>> sources = List.of(
                    new TermsValuesSourceBuilder(FIELD.USER_ID).field(FIELD.USER_ID)
            );

            CompositeAggregationBuilder compositeAgg = AggregationBuilders
                    .composite(ES_COMPOSITE_AGGREGATION, sources)
                    .size(ES_COMPOSITE_AGG_SIZE)
                    .subAggregation(AggregationBuilders.sum(FIELD.TOTAL_POINTS).field(FIELD.TOTAL_POINTS));

            if (afterKey != null) {
                compositeAgg.aggregateAfter(afterKey);
            }
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(query)
                    .aggregation(compositeAgg)
                    .size(0);

            SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING)
                    .source(sourceBuilder);

            SearchResponse response = null;
            try {
                response = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                log.warn("Exception while executing elasticsearch error message {}, Exception {}", e.getMessage(), e);

            }

            ParsedComposite aggregation = response.getAggregations().get(ES_COMPOSITE_AGGREGATION);
            for (CompositeAggregation.Bucket bucket : aggregation.getBuckets()) {
                long userId = ((Number) bucket.getKey().get(FIELD.USER_ID)).longValue();
                double totalPoints = ((ParsedSum) bucket.getAggregations().get(FIELD.TOTAL_POINTS)).getValue();
                userPointsMap.merge(userId, (long) totalPoints, Long::sum);
            }

            afterKey = aggregation.afterKey();
            hasMorePages = (afterKey != null);
            if(aggregation.getBuckets().isEmpty()){
                break;
            }
        }

        // Sort by points descending and get top N users
        return userPointsMap.entrySet().stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .limit(topNUser)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (a, b) -> b,
                        LinkedHashMap::new
                ));
    }


    private Map<Long, Long> prepareTopNUsersResponse(SearchResponse searchResponse, int topNUser) {
        if(searchResponse == null) {
            return Collections.emptyMap();
        }
        Map<Long, Long> userPointsMap = new LinkedHashMap<>();
        Terms agg = searchResponse.getAggregations().get(AGGREGATION.USER_ID);
        int totalBucket = Math.min(agg.getBuckets().size() , topNUser);
        for (int i = 0; i < totalBucket; i++) {
            Terms.Bucket bucket = agg.getBuckets().get(i);
            Sum pointAggregation = bucket.getAggregations().get(AGGREGATION.TOTAL_POINTS);
            userPointsMap.put(bucket.getKeyAsNumber().longValue(), (long) pointAggregation.getValue());
        }
        return userPointsMap;
    }


    public Map<Long, Map<Long, AttendeeExpoEngagementDetail>> getAttendeeExpoEngagementDetailByEvent(Event event) {
        Long eventId = event.getEventId();
        log.info("getAttendeeExpoEngagementDetailByEvent event {} ", eventId);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Date lastDate = ticketing.getEventStartDate();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                .must(QueryBuilders.matchQuery("eventId", eventId))
                .must(QueryBuilders.existsQuery("expoId"))
                .must(QueryBuilders.rangeQuery("date").gte(lastDate)))
                .sort("date", SortOrder.ASC);

        log.info("ES query result process start for export event {} ", eventId);
        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();
        List<TrackingData> trackingData = null;
        try {
            trackingData = getESTrackingDataForCSV(eventId,lastDate,searchSourceBuilder);
            stopWatchESResultProcess.stop();
            log.info("ES result process end event {} time taken for export {}", eventId, stopWatchESResultProcess.getTotalTimeMillis());

            Map<Long, AttendeeExpoEngagementDetail> savedTrackingData = null;

            if (trackingData != null && !trackingData.isEmpty()) {
                savedTrackingData = new HashMap<>();
                return updateAttendeeExpoEngagementDetailForAll(trackingData, savedTrackingData, eventId);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Collections.emptyMap();
    }

    private Map<Long, Map<Long, AttendeeExpoEngagementDetail>> updateAttendeeExpoEngagementDetailForAll(List<TrackingData> trackingData, Map<Long, AttendeeExpoEngagementDetail> savedTrackingData, long eventId) {
        log.info("updateAttendeeExpoEngagementDetailForAll start event {} ", eventId);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<Long, List<TrackingData>> attendeeExpoEngagementMap = trackingData.stream().collect(Collectors.groupingBy(TrackingData::getExpoId));
        Map<Long, Map<Long, AttendeeExpoEngagementDetail>> attendeeExpoEngageByExpoIdMap = new HashMap<>();
        attendeeExpoEngagementMap.entrySet().forEach(attendeeExpoEngagement -> {
            Map<Long, AttendeeExpoEngagementDetail> savedTrackingDataMap = new HashMap<>();
            Long expoId = attendeeExpoEngagement.getKey();
            updateAttendeeExpoEngagementDetail(attendeeExpoEngagement.getValue(), savedTrackingDataMap, eventId);
            attendeeExpoEngageByExpoIdMap.put(expoId,savedTrackingDataMap);
        });
        stopWatch.stop();
        log.info("updateAttendeeExpoEngagementDetailForAll end event {} time taken {}", eventId, stopWatch.getTotalTimeMillis());
        return attendeeExpoEngageByExpoIdMap;
    }


    /**
     * Get number of time exhibitor's products viewed by all user
     * @param eventId id of event
     * @param exhibitorId id of exhibitor
     * @return total product views count
     * @throws IOException
     */
    public long getProductViewsByExhibitor(Long eventId, Long exhibitorId) throws IOException {

        log.info("Start get product views of exhibitor exhibitor {}, event {}", exhibitorId, eventId);
        SearchRequest searchRequest = new SearchRequest(INDICES_CLICKSTREAM).types(TYPE_RAW);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        searchSourceBuilder.query(QueryBuilders
                .boolQuery()
                .must(QueryBuilders.matchQuery(FIELD.EVENT_ID, eventId))
                .must(QueryBuilders.matchQuery(FIELD.EXPO_ID, exhibitorId))
                .must(QueryBuilders.matchQuery(FIELD.AREA, AREA.EXPO))
                .must(QueryBuilders.existsQuery(FIELD.PRODUCT_VIEW)));

        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(AggregationBuilders.count(AGGREGATION.USER_ID).field(FIELD.USER_ID));
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);

        log.info("Successfully elasticsearch query executed for get product views of exhibitor {}, event {} ", exhibitorId, eventId);

        ValueCount agg = searchResponse.getAggregations().get(AGGREGATION.USER_ID);
        long productViews = agg.getValue();
        log.info("Successfully fetched product views for exhibitor {}, event {}, product views {}", exhibitorId, eventId, productViews);
        return productViews;
    }

    /**
     * Get total product views of user (number of time user viewed products from all expo booth)
     * @param eventId id of event
     * @param userId id of user
     * @return returns product views count of user
     * @throws IOException
     */
    public long getProductViewsByUser(Long eventId, Long userId) throws IOException {

        log.info("Start get product views of user {}, event {}", userId, eventId);
        SearchRequest searchRequest = new SearchRequest(INDICES_CLICKSTREAM).types(TYPE_RAW);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        searchSourceBuilder.query(QueryBuilders
                .boolQuery()
                .must(QueryBuilders.matchQuery(FIELD.EVENT_ID, eventId))
                .must(QueryBuilders.matchQuery(FIELD.USER_ID, userId))
                .must(QueryBuilders.matchQuery(FIELD.AREA, AREA.EXPO))
                .must(QueryBuilders.existsQuery(FIELD.PRODUCT_VIEW)));

        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(AggregationBuilders.count(AGGREGATION.USER_ID).field(FIELD.USER_ID));
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);
        log.info("Successfully Elasticsearch query executed for get product views of user {}, event {} ", userId, eventId);

        ValueCount agg = searchResponse.getAggregations().get(AGGREGATION.USER_ID);
        long productViews = agg.getValue();
        log.info("Successfully fetched product views for user {}, event {}, product views {}", userId, eventId, productViews);

        return productViews;
    }

    public void deleteTrackingDataByEventId(long eventId) {
        log.info("Start Delete tracking data ES query, eventId {}", eventId);
        DeleteByQueryRequest request = new DeleteByQueryRequest(INDICES_TRACKING);
        request.setQuery(QueryBuilders.matchQuery(SponsorAnalyticsConstant.EVENT_ID, eventId));
        try {
            gamificationESClient.deleteByQuery(request, RequestOptions.DEFAULT);
        }catch (Exception e) {
            e.printStackTrace();
            log.info("Error while deleting tracking data eventId {}", eventId);
        }
        log.info("Successfully deleted tracking data eventId {}", eventId);
    }


    public List<ContinuingEdProgressDTO> getContinuingEdChallengesProgress(User user, Event event) {
        List<EventCECriteriaDTO> continuingEdChallenges = eventCECriteriaService.getCriteriaDTOsByUser(user,event);
        return continuingEdService.getContinuingEdChallengesProgress(user, event, continuingEdChallenges);
    }

    public ChallengeAndTierDetailsDTO getLeaderboardTierDetails(long tierId, Event event, User user, boolean tierLocked) throws IOException, JSONException {
        log.info("getLeaderboardTierDetails | event {} | user {} and tierId {}", event.getEventId(), user.getUserId(), tierId);
        Optional<EventChallengeTiers> eventChallengeTiersOpt = eventChallengeTierRepository.findByIdAndEventId(tierId, event.getEventId());
        ChallengeAndTierDetailsDTO challengeAndTierDetailsDTO = new ChallengeAndTierDetailsDTO();
        if (eventChallengeTiersOpt.isPresent()) {
            EventChallengeTiers eventChallengeTiers = eventChallengeTiersOpt.get();
            Map<Long, Long> userChallengePointMap;
            userChallengePointMap = getUserChallengePointsWithoutCheckingEligiblePoints(event, user.getUserId());
            log.info("getLeaderboardTierDetails userChallengePointMap {}", userChallengePointMap);
            List<Long> tierChallengeIds = challengeAndTierMappingRepository.getChallengeIdsByEventIdAndTierId(event.getEventId(), tierId);
            List<EventChallengeDetail> eventChallengeDetails = eventChallengeRepository.findChallengesByEventIdAndIdIn(event.getEventId(), tierChallengeIds);
            log.info("getLeaderboardTierDetails tier Id {} event {} user {} challenges size {}", tierId, event.getEventId(), user.getUserId(), eventChallengeDetails.size());
            int completedChallengesCount = 0;

            List<EventChallengeAndTierDTO> challenges = new ArrayList<>();
            List<GamificationRewardDTO> rewards = new ArrayList<>();

            //prepare DTO for the tiers challenges details and rewards
            for (EventChallengeDetail challenge : eventChallengeDetails) {
                boolean challengeCompleted = false;
                int challengeEarnedPoints = tierLocked ? 0 : Math.toIntExact(userChallengePointMap.getOrDefault(challenge.getId(), 0L));
                if (challenge.getGoalPoints() <= challengeEarnedPoints) {
                    completedChallengesCount++;
                    challengeCompleted = true;
                }

                //prepare challenges rewards
                rewards.addAll(prepareRewardsOfTierAndChallenges(challenge.getRewards(),event ,user,null, challengeCompleted, challenge));

                challenges.add(new EventChallengeAndTierDTO(challenge.getId(), challenge.getChallengeName(), challenge.getChallengeDesc(), challenge.getGoalPoints(), challenge.getImage(), challengeCompleted, challengeEarnedPoints, String.valueOf(challenge.getType())
                ));
            }

            boolean tierCompleted = false;
            if (!challenges.isEmpty()) {
                log.info("getLeaderboardTierDetails | challenges are present tier  {}", tierId);
                tierCompleted = completedChallengesCount == challenges.size();
            }

            //prepare tier rewards
            rewards.addAll(prepareRewardsOfTierAndChallenges(eventChallengeTiers.getRewards(),event,user,eventChallengeTiers, tierCompleted, null));

            challengeAndTierDetailsDTO.setEventChallengeAndTiers(challenges);
            challengeAndTierDetailsDTO.setGamificationRewardsOfTiers(rewards);
            challengeAndTierDetailsDTO.setTotalChallenges(challenges.size());
            challengeAndTierDetailsDTO.setCompletedChallenges(completedChallengesCount);
            log.info("getLeaderboardTierDetails | tier {} user {} event {} completedChallenges {}", eventChallengeTiers.getId(),user.getUserId(), event.getEventId(), completedChallengesCount);
            return challengeAndTierDetailsDTO;
        } else {
            throw new NotFoundException(NotFoundException.EventChallengeTierNotFound.EVENT_CHALLENGE_TIER_NOT_FOUND);
        }
    }

    public List<GamificationRewardDTO> prepareRewardsOfTierAndChallenges(String rewards, Event event,User user ,EventChallengeTiers tier, boolean isCompleted, EventChallengeDetail challenge) throws JSONException {
        log.info("prepareRewardsOfTierAndChallenges | event {} | rewards {} ", event.getEventId(),rewards);
        List<GamificationRewardDTO> rewardDTOs = new ArrayList<>();

        List<UserChallengeRewardsTracker> userChallengeRewardsTrackers = new ArrayList<>();

        if (StringUtils.isNotBlank(rewards)) {
            List<UserChallengeRewardsTracker> rewardsTrackers = challengeRewardsTrackerRepoService.getByUserIdAndEventId(user.getUserId(), event.getEventId());
            Map<Long, List<UserChallengeRewardsTracker>> challengeRewardsTrackersMap = rewardsTrackers.stream()
                    .collect(Collectors.groupingBy(tracker ->
                            tracker.getChallengeId() != null ? tracker.getChallengeId() : -1L
                    ));

            Map<Long, List<UserChallengeRewardsTracker>> tierRewardsTrackersMap = rewardsTrackers.stream()
                    .collect(Collectors.groupingBy(tracker ->
                            tracker.getTierId() != null ? tracker.getTierId() : -1L
                    ));

            Raffle raffle = raffleService.findByEvent(event);
            boolean isRaffleActivate = raffle.isActivated() && event.isRaffleEnabled();
            JSONArray rewardList = new JSONArray(rewards);
            for (int i = 0; i < rewardList.length(); i++) {
                JSONObject reward = rewardList.getJSONObject(i);
                if (reward != null) {
                    GamificationRewardDTO gamificationRewardDTO = new GamificationRewardDTO();
                    challengeConfigService.setGamificationRewardsDetails(gamificationRewardDTO, reward);

                    if (challenge != null) {

                        //get all the reward trackers of the challenge
                        List<UserChallengeRewardsTracker> challengeRewardsTrackers = challengeRewardsTrackersMap.getOrDefault(challenge.getId(), Collections.emptyList());


                            UserChallengeRewardsTracker userChallengeRewardsTracker = challengeRewardsTrackers.stream()
                                    .filter(rewardTracker -> Boolean.TRUE.equals(rewardTracker.getRewarded()) && rewardTracker.getChallengeId() == challenge.getId())
                                    .findAny()
                                    .orElse(null);

                        processRewardIfNotRewardedAndCompleted(event,user,reward,challenge.getId(),null, isCompleted, isRaffleActivate, raffle, userChallengeRewardsTracker, userChallengeRewardsTrackers);


                        gamificationRewardDTO.setChallengeId(challenge.getId());
                        gamificationRewardDTO.setChallengeName(challenge.getChallengeName());
                        gamificationRewardDTO.setEndDate(challenge.getEndDate());
                    } else if (tier != null) {

                        //get all the reward trackers of the tiers
                        List<UserChallengeRewardsTracker> tierRewardsTrackers = tierRewardsTrackersMap.getOrDefault(tier.getId(), Collections.emptyList());

                            UserChallengeRewardsTracker userChallengeRewardsTracker = tierRewardsTrackers.stream()
                                    .filter(rewardTracker -> Boolean.TRUE.equals(rewardTracker.getRewarded()) && rewardTracker.getTierId() == tier.getId())
                                    .findAny()
                                    .orElse(null);
                        processRewardIfNotRewardedAndCompleted(event,user,reward,null,tier.getId(), isCompleted, isRaffleActivate, raffle, userChallengeRewardsTracker, userChallengeRewardsTrackers);

                        gamificationRewardDTO.setTierId(tier.getId());
                        gamificationRewardDTO.setTierName(tier.getTierName());
                    }
                    gamificationRewardDTO.setCompleted(isCompleted);
                    if(!CollectionUtils.isEmpty(userChallengeRewardsTrackers)) {
                        log.info("prepareRewardsOfTierAndChallenges | assigned reward to the user {} and size {}", user.getUserId(), userChallengeRewardsTrackers.size());
                        userChallengeRewardsTrackerRepository.saveAll(userChallengeRewardsTrackers);
                    }
                    rewardDTOs.add(gamificationRewardDTO);
                }
            }
        }
        log.info("prepareRewardsOfTierAndChallenges | size of rewards {}", rewardDTOs.size());
        return rewardDTOs;
    }


    private void processRewardIfNotRewardedAndCompleted(Event event, User user, JSONObject reward,
                                                        Long challengeId, Long tierId, boolean isCompleted, boolean isRaffleActivated, Raffle raffle,
                                                        UserChallengeRewardsTracker userChallengeRewardsTracker,
                                                        List<UserChallengeRewardsTracker> userChallengeRewardsTrackers) {
        log.info("processRewardIfNotRewardedAndCompleted |  event {} user {}, tierId {} challenge id {} enabled {} activated {}",
                event.getEventId(),user.getUserId(),tierId, challengeId,  event.isRaffleEnabled(), raffle.isActivated());
        boolean isRewarded = userChallengeRewardsTracker != null;

        if (!isRewarded && isCompleted) {
            String rewardType = reward.optString(REWARD_CONSTANT.TYPE);
            String rewardImage = reward.optString(REWARD_IMAGE);
            String rewardName = reward.optString(REWARD_CONSTANT.NAME);

            if (REWARD_CONSTANT.RAFFLE.equalsIgnoreCase(rewardType)) {
                if (isRaffleActivated) {
                    int noOfTickets = reward.optInt(REWARD_CONSTANT.TICKETS);
                    long creditedTickets = raffleService.creditGamificationRewardRaffleTicket(
                            raffle,
                            challengeConfigService.prepareRaffleTicket(raffle.getId(), user.getUserId(), noOfTickets)
                    );

                    if (creditedTickets > 0) {
                        userChallengeRewardsTrackers.add(
                                challengeConfigService.prepareUserChallengeRewardsTracker(
                                        event.getEventId(),
                                        challengeId,
                                        user.getUserId(),
                                        rewardType,
                                        rewardImage,
                                        tierId,
                                        rewardName
                                )
                        );
                    }
                } else {
                    log.info("processRewardIfNotRewardedAndCompleted | Raffle module is not activated event {}, raffle {} enabled {} activated {}",
                            event.getEventId(), raffle.getId(), event.isRaffleEnabled(), raffle.isActivated());
                }
            } else if (REWARD_CONSTANT.BADGE.equalsIgnoreCase(rewardType)) {
                userChallengeRewardsTrackers.add(
                        challengeConfigService.prepareUserChallengeRewardsTracker(
                                event.getEventId(),
                                challengeId,
                                user.getUserId(),
                                rewardType,
                                rewardImage,
                                tierId,
                                rewardName
                        )
                );
            }
        }
    }


    public void makePointsEligibleForUnlockedTierChallenges(User user, Event event, List<Long> challengesCompletedOfCompletedTiers) throws IOException {
        log.info("Start updating pointsEligible field for unlocked tier challenges, userId {}, eventId {}, challengeIds {}",
                user.getUserId(), event.getEventId(), challengesCompletedOfCompletedTiers);

        // Create the UpdateByQueryRequest
        UpdateByQueryRequest request = new UpdateByQueryRequest(INDICES_TRACKING);

        // Construct the query to match documents based on userId, eventId, and challengeIds
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("userId", user.getUserId()))
                .must(QueryBuilders.termQuery("eventId", event.getEventId()))
                .must(QueryBuilders.termsQuery("challengeId", challengesCompletedOfCompletedTiers));

        request.setQuery(query);

        // Define the script to set pointsEligible to true
        String scriptSource = "if (ctx._source.containsKey('pointsEligible') && ctx._source.pointsEligible == false) {" +
                "    ctx._source.pointsEligible = true;" +
                "}";

        // Set the script
        Script script = new Script(ScriptType.INLINE, "painless", scriptSource, Collections.emptyMap());
        request.setScript(script);

        // Execute the update-by-query request
        BulkByScrollResponse bulkByScrollResponse = gamificationESClient.updateByQuery(request, RequestOptions.DEFAULT);
        log.info("Successfully updated pointsEligible for {} records, userId {}, eventId {}",
                bulkByScrollResponse.getUpdated(), user.getUserId(), event.getEventId());
    }

    public void makePointsEligibleForChallenges(Event event, List<Long> challengesRemovedFromTier) throws IOException {
        log.info("Start updating pointsEligible field for unlocked tier challenges, eventId {}, challengeIds {}",
                event.getEventId(), challengesRemovedFromTier);

        // Create the UpdateByQueryRequest
        UpdateByQueryRequest request = new UpdateByQueryRequest(INDICES_TRACKING);

        // Construct the query to match documents based on eventId and challengeIds (no userId filtering)
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("eventId", event.getEventId())) // Match eventId
                .must(QueryBuilders.termsQuery("challengeId", challengesRemovedFromTier)); // Match challengeIds

        request.setQuery(query);

        // Define the script to set pointsEligible to true if it is false
        String scriptSource = "if (ctx._source.containsKey('pointsEligible') && ctx._source.pointsEligible == false) {" +
                "    ctx._source.pointsEligible = true;" +
                "}";

        // Set the script
        Script script = new Script(ScriptType.INLINE, "painless", scriptSource, Collections.emptyMap());
        request.setScript(script);

        // Execute the update-by-query request
        BulkByScrollResponse bulkByScrollResponse = gamificationESClient.updateByQuery(request, RequestOptions.DEFAULT);
        log.info("Successfully updated pointsEligible for {} records, eventId {}",
                bulkByScrollResponse.getUpdated(), event.getEventId());
    }

    public List<ChallengeLeaderBoardData> getTierLeaderBoardDataByChallengeIds(Event event, List<Long> challengeIds, PageSizeSearchObj pageSizeSearchObj) throws IOException {
        Long eventId = event.getEventId();
        Date lastDate = getEventStartDate(event);
        log.info("getTierLeaderBoardDataByChallengeIds event {}  lastDate {} challengeIds {}", eventId, lastDate, challengeIds.size());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        Map<Long, Map<Long, Long>> userChallengePointMap = new HashMap<>();
        Map<String, Object> afterKey = null;

        final StopWatch stopWatchESResultProcess = new StopWatch();
        stopWatchESResultProcess.start();

        do {
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                            .mustNot(QueryBuilders.matchQuery(FIELD.REC_STATUS, REC_STATUS_DELETE))
                            .mustNot(QueryBuilders.termQuery(POINTS_ELIGIBLE, false))
                            .must(QueryBuilders.matchQuery(EVENT_ID, eventId))
                            .must(QueryBuilders.rangeQuery(DATE).gte(lastDate))
                            .must(QueryBuilders.rangeQuery(TOTAL_POINTS_CAMEL_CASE).gt(0))
                            .must(QueryBuilders.termsQuery(CHALLENGE_ID, challengeIds)))
                    .sort("totalPoints", SortOrder.ASC);
            searchSourceBuilder.size(0);

            List<CompositeValuesSourceBuilder<?>> sources = new ArrayList<>();
            sources.add(new TermsValuesSourceBuilder(USER_ID).field(USER_ID));
            sources.add(new TermsValuesSourceBuilder(CHALLENGE_ID).field(CHALLENGE_ID));

            CompositeAggregationBuilder compositeAgg = AggregationBuilders.composite(ES_COMPOSITE_AGGREGATION, sources).size(ES_COMPOSITE_AGG_SIZE);

            if (afterKey != null) {
                compositeAgg.aggregateAfter(afterKey);
            }

            compositeAgg.subAggregation(AggregationBuilders.sum(TOTAL_POINTS_CAMEL_CASE).field(TOTAL_POINTS_CAMEL_CASE));

            searchSourceBuilder.aggregation(compositeAgg);

            SearchRequest searchRequest = new SearchRequest(INDICES_TRACKING).source(searchSourceBuilder);
            SearchResponse searchResponse = gamificationESClient.search(searchRequest, RequestOptions.DEFAULT);

            ParsedComposite parsedComposite = searchResponse.getAggregations().get(ES_COMPOSITE_AGGREGATION);

            if (parsedComposite == null || parsedComposite.getBuckets().isEmpty()) {
                log.info("ParsedComposite aggregation 'user_challenge_aggregation' not found in the response");
                break;
            } else {
                log.info("ParsedComposite aggregation found. Bucket count: {}", parsedComposite.getBuckets().size());
                for (CompositeAggregation.Bucket bucket : parsedComposite.getBuckets()) {
                    Object userIdObj = bucket.getKey().get(USER_ID);
                    Object challengeIdObj = bucket.getKey().get(CHALLENGE_ID);

                    if (userIdObj == null || challengeIdObj == null) {
                        continue;
                    }
                    try {
                        Long userId = ((Number) userIdObj).longValue();
                        Long challengeId = ((Number) challengeIdObj).longValue();
                        ParsedSum sumAgg = bucket.getAggregations().get(TOTAL_POINTS_CAMEL_CASE);
                        if (sumAgg == null) {
                            continue;
                        }
                        long points = (long) sumAgg.getValue();
                        log.info("Parsed bucket: userId={}, challengeId={}, points={}", userId, challengeId, points);
                        userChallengePointMap.computeIfAbsent(userId, k -> new HashMap<>()).put(challengeId, points);
                    } catch (Exception e) {
                        log.error("getTierLeaderBoardDataByChallengeIds | Error parsing bucket key or aggregation value. Bucket key: {}, error: {}", bucket.getKey(), e.getMessage(), e);
                    }
                }
                if (parsedComposite.afterKey() != null) {
                    afterKey = parsedComposite.afterKey();
                    log.info("Next afterKey for pagination: {}", parsedComposite.afterKey());
                } else {
                    afterKey = null;
                    log.info("No more pages to fetch (afterKey is null)");
                }
            }
        } while (afterKey != null);
        stopWatchESResultProcess.stop();
        log.info("getTierLeaderBoardDataByChallengeIds | ES result process complete for event {}. Time taken: {} ms", eventId, stopWatchESResultProcess.getTotalTimeMillis());
        return getAggregateLeaderBoardDataChallengeWise(eventId, userChallengePointMap);
    }

}