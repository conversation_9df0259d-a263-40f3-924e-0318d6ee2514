package com.accelevents.services;

import com.accelevents.badges.BadgesImage;
import com.accelevents.domain.CertificateImages;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RegistrationRequestType;
import com.accelevents.domain.exhibitors.VirtualPortalImage;
import com.accelevents.dto.AttendeeDetailsDto;
import com.accelevents.dto.ImageUploadDto;
import com.accelevents.dto.WLLogoImageUploadDto;
import com.accelevents.dto.WaitingMediaUploadDto;
import org.json.JSONException;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface S3UploadService {

    ImageUploadDto uploadSponsorCarouselImage(Long sponsorId, ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadExhibitorCarouselImage(Long exhibitorId, ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadLogoImageFiles (ImageUploadDto imageUploadDto, Event event);

	ImageUploadDto uploadBannerImageFiles(ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadLobbyBannerImageFiles(ImageUploadDto imageUploadDto, Event event);

    WLLogoImageUploadDto uploadWLImageFiles(ImageUploadDto imageUploadDto, String whiteLabelUrl);

    ImageUploadDto imageUploadInS3(ImageUploadDto imageUploadDto);

    ImageUploadDto imageUploadInS3(ImageUploadDto imageUploadDto, String bucketName);

    ImageUploadDto customThermometerImage(ImageUploadDto imageUploadDto, Event event);

	ImageUploadDto donationViewBackgroundImage(ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadExhibitorLogoImage(Long exhibitorId, ImageUploadDto imageUploadDto);

    VirtualPortalImage uploadLeftSideExpoBannerImage(ImageUploadDto imageUploadDto, Event event, String imagePosition);

    BadgesImage uploadBadgeImage(ImageUploadDto imageUploadDto, Event event, User user, Long badgeId, Long badgeMasterId);

    ImageUploadDto uploadUserImage(User user, ImageUploadDto imageUploadDto, Event event, boolean isFromSpeaker);

    ImageUploadDto uploadSponsorLogoImage(Long sponsorId, ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto commonImageUploadOnS3(ImageUploadDto imageUploadDto);

    ImageUploadDto uploadNetworkingLoungeImage(String id, ImageUploadDto imageUploadDto, boolean isProfileImage, Long userId,boolean isBannerImage);

    ImageUploadDto uploadExhibitorExpoCard(long id, ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadSponsorCardImage(long id, ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadExhibitorExpoBannerImage(long id, ImageUploadDto imageUploadDto);

    ImageUploadDto uploadUserVirtualBackgroundImage(long id, ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadCoverPhoto(User user, ImageUploadDto imageUploadDto, Event event);

    WaitingMediaUploadDto uploadSessionWaitingImageOrVideo(WaitingMediaUploadDto waitingMediaUploadDto, Event event, long sessionId) throws JSONException;

    ImageUploadDto uploadVenueMapImageFiles(ImageUploadDto imageUploadDto, Event event);

    ImageUploadDto uploadIntroductoryPageBannerImage(Event event, ImageUploadDto imageUploadDto,  RegistrationRequestType type);

    String generatePreSignedUrl(String uploadKey, String fileName);

    String uploadSessionCaptionFile(Event event,Long sessionId, String assetId, MultipartFile uploadFile, String languageCode, Long subtitleId);

    ImageUploadDto uploadAttendeeMobileAppImage(long id, ImageUploadDto imageUploadDto, Event event);

    void uploadUserProfileFromCsvUpload(List<AttendeeDetailsDto> registeredAttendees, Map<String, String> attendeeProfileImage);

    CertificateImages uploadCertificateImage(ImageUploadDto imageUploadDto, Event event, User user, Long certificateId);

    String uploadSpeakerProfilePicture(String imageURL);

    void uploadUserProfileImageFromCsv(Map<String, String> attendeeProfileImage);

    boolean uploadMobileIconMasterImageFiles( String iconName,ImageUploadDto imageUploadDto);

    ImageUploadDto uploadMobileIconImageFilesByEventId( Long eventId, Long userId,ImageUploadDto imageUploadDto);

}
