package com.accelevents.services;

import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.perfomance.dto.TicketingBuyerDataWithAmount;
import com.accelevents.ticketing.TicketingBeeFreeDto;
import com.accelevents.ticketing.dto.*;
import com.itextpdf.text.DocumentException;
import freemarker.template.TemplateException;

import java.io.IOException;
import java.text.ParseException;
import java.util.*;

public interface TicketingService {

	Ticketing save(Ticketing ticketing);

    Optional<Ticketing> findById(Long ticketingId);

    Ticketing findByEvent(Event event);

	CreateTicketingDto getTicketing(Event event, Boolean isHidden, Long recurringEventId, List<DataType> dataTypes,boolean ContainsDonationType);

	List<TicketHolderRequiredAttributes> getTicketingAttributes(Event event, Long recurringEventId);

	TicketingBuyerDataWithAmount getAllTicketBuyers(Event event, PageSizeSearchObj pageSizeSearchObj, Long recurringEventId, DataType dataType);

    double getFee(TicketingBuyerDataFromDB ticketingOrder, StripeDTO stripe);

    void deleteEventType(long ticketingtypeid, Event event);

	// TODO refactor methods

    String getStringDateWithAddedRecurringEventEndTime(Date recurringEventStartDate, Integer minutes);

    void updateListOFTicketTypeOnAssociation(Event event, Long recurringEventId);

    TicketSettingGetDto getTicketSettingAttributes(Event event, Long recurringEventId);

    TicketSettingGetDto getTicketSettingAttributesWithSubquestions(Event event, Long recurringEventId);

    void saveTicketingAttributes(TicketSettingDto ticketSetting, Event event, User loggedInUser, Boolean isForBuyer, Long recurringEventId);

    void updateAttributesByToggle(TicketSettingDto ticketSetting, Event event, User loggedInUser, Boolean isForBuyer, Long recurringEventId, List<TicketingType> ticketTypes, boolean toBeCustomAttribute);

    void deleteCustomAttribute(long attributeId, boolean isDeletedFromBuyer, Long recurringEventId, Event event);
	
	List<CustomAttribute> getCustomAttribute(long attributeId, boolean isForBuyer, Event event);

	void updateTicketTypeSequence(Long typeId, Long topTypeId, Long topBottomTypeId, Event event, User user);

	List<TicketingDownloadPurchase> findAllTicketingDownloadPurchase();

	TicketingEmailDto getTicketEmailAttributes(Event event);

    void sendTestEmail(String email, Event event,Long customEmailId) throws DocumentException, TemplateException, IOException;

    void sendTestEmail(String email, Event event, Optional<Long> resendTicketEmailOptional, boolean isSendTestReminder,Long customEmailId) throws DocumentException, TemplateException, IOException;

    void sendTestReminderEmailSqs(String email, Long resendTicketingId);

	UserOrdersDto getUserActiveOrders(User user, PageSizeSearchObj pageSizeSearchObj, Long orderId);

    List<EventMyTicketOrderDto> getUserTickets(User user,Event event);

	UserOrdersDto getUserPastOrders(User user, PageSizeSearchObj pageSizeSearchObj);

	List<UserEventTicketDto> getUserEventTickets(long ticketOrderNumber, User user);

    void changeSeat(Long eventId, Long eventTicketingId, String newSeatNumber);

    String updateOrderNote(long orderId, Event event, String note);

	AttendeeDtoV2 getHolderData(Event event, long ticketingHolderId);

    String createCustomer(String email, Event event);

    void saveTicketing(TicketingDto ticketing, Event event);

    EnumEventVenue getEventVenue(Ticketing ticketing);

    CreateTicketingDto getTicketing(Event event);

	List<EventCategoryDto> getCategories(Event event, String search);

	void saveTicketingPdf(String ticketPdfDesign, Event event);

    String getTicketPdfByHost(Event event, Boolean onlyMainBody) throws IOException;

    Set<String> getEventsByEventKeyAndFromTo(long from, long to);

    void updateChartTickets(Event event);

    boolean isRecurringEvent(Event event);

    void mapTicketTypes(Event event, TicketingType createTicket, TrayIOWebhookActionType actionType);

    List<Event> getListOfEventByEventIdsAndRecurringEnable(long from, long to);

    List<UserEventTicketDto> getEventTicketsByOrderIdAndHolderEmail(Long orderId, String holderEmail);

    UserOrdersDto getUserActiveOrdersByOrderIdAndHolderEmail(PageSizeSearchObj pageSizeSearchObj, Long orderId, String email, User user);

    void requireToCallUpdateChartTableModes(Event event);

    DisplayFeeDto calculateFeesByTicketIdAndPrice(HostTicketTypeDto hostTicketTypeDtoList, Event event);

	List<TicketSettingAttributeDto> getAllAddonCustomAttributes(Event event, boolean isForBuyer, Long recurringEventId);

    List<Event> getListOfEventByEventIdsAndRecurringEnableAndChartKeyNotNull(long from, long to);

    UserEventOrderDto getOrderAndEventDetailsByOrderId(Long orderId, User user);

    void setEventToOnlineEvent(Event event);

	List<Event> findAllOnlineEvents();

	TicketingDatesDto findEventStartAndEndDateByEventid(Event event);

	TicketingDatesDto findEventStartAndEndDateAndPreEventAccessMinuteByEventid(Event event);

    EnableOrderConfirmationDto isOrderConfirmEmailDisabled(Long ticketingId);

    EnableOrderConfirmationDto setOderConfirmationEmail(EnableOrderConfirmationDto enableOrderConfirmationDto, Long ticketingId);

   boolean setShowMemberCountInCheckout(boolean isShowMemberCountInCheckout , Long ticketingId);

    void updateTicketAddedChecklistFlag(Event event, boolean isTicketAdded);

    void updateBeefreeTemplateFromSuperAdminSide(Long beefreeTemplateId, BeeFreeTemplateDto beeFreeTemplateDto);

    BeeFreeTemplateDto getBeeFreeTemplateByEmailType(EmailType emailType, User user);

    void saveBeefree(BeeFree beeFree);

    BeeFree getBeeFreeByEvent(Event event);

    List<BeeFree> getAllBeeFreeByEvent(Event event);

    BeeFree getBeeFreeByEmailTypeAndEventIdZero(EmailType emailType);

    void activateTicketingModule(Event event, EventTicketingDto ticketingDto);

    boolean setAccessOfBeeFreeTemplate(boolean allowToAccess, Event event, User user,String emailType);

    TicketingBeeFreeDto isAccessOfBeeFreeTemplate(Long ticketingId);

    void saveBeeFreeTemplate(BeeFreeTemplateDto beeFreeTemplateDto, Event event, User user);

    void saveBeeFreeTemplateByEmailType(BeeFreeTemplateDto beeFreeTemplateDto, User user);

    void updateBeeFreeTemplate(Long beeFreeTemplateId, BeeFreeTemplateDto beeFreeTemplateDto, Event event, User user);

    BeeFreeTemplateDto getBeeFreeTemplate(Event event, String path,User user);

    boolean isShowRegistrationButton(Event event);

    void updateRecStatus(Long eventId, RecordStatus recordStatus);

    void updateRecordStatusOfTicketingByEventIds(List<Long> eventIds, RecordStatus recordStatus);

    void resetEventDates(List<Long> listOfEventId, int days);

    void sendTestEmailForBeeFreeTemplate(EmailType emailType, User superAdminUser);

    ResponseDto deleteBeefreeTemplateByIdAndEmailType(Long beefreeTemplateId, User user, String emailType);

    boolean isCollectTicketHolderAttributesEnable(Long eventId);

    void updateCustomTemplateEnabled(EmailType type,long beeFreeId,boolean isCustomTemplateEnabled);

   List<BeeFreeTemplateDto> findByEventIdZeroBeeFreeTemplate();

   BeeFree findByEmailTypeAndEvent(Event event,EmailType type);

   List<KeyValueDto> getTicketHolderOrBuyerEnabledAttribute(Event event);

    boolean isValidEmailType(String emailType);

    double getFeeTicketWise(TicketingBuyerDataFromDB ticketingOrder, double ccPercentageFee, double ccFlatFee, String paymentGateway);

    boolean isEnableAutoAssignedSequence(Event event);

    public BeeFree getBeeFreeByEventAndId(Long beeFreeTemplateId , Event event);

    String generateEngageEmailTemplate(String engageEmail);

    double getProcessingFeesExcludingTax(TicketingBuyerDataFromDB ticketingOrder);

    Date getEventStartDate(Event event);

    boolean setEnterEventButtonInReminderEmail(boolean isVisibleEnterEventButton , Long ticketingId);

    boolean isEnabledAccessEventButtonInReminderTemplate(Long ticketingId);

    void disableInterestAttribute(Event event, User user);

    List<TicketSettingAttributeDto> getConditionalQuestions(Event event);

    OrderAuditLogsHistoryDto getOrderAuditLogsHistory(Event event, Long orderId);

    ResponseDto resetEventLevelBeeFreeTemplate(Long beeFreeTemplateId,String emailType,Event event);

    Map<Long,Ticketing> findTicketingByEventIds(List<Long> eventIds);

    List<Ticketing> findByEventIds(List<Long> eventIds);

    ResponseDto checkSessionOutsideEventTime(Event event, String startTime, String endTime) throws ParseException;

    void resendTicketExchangeEmail(long eventTicketId, Event event, User loggedInUser);

    void updateAllowAttendeeToCheckInWithUnPaidTicket(Event event, boolean allowAttendeeToCheckInWithUnPaidTicket);

    boolean findIsLimitEventCapacityByEventId(Event eventId);

    Double findLimitEventCapacityByEventId(Event event);

    boolean isApprovalRequestEmailsEnabled(Event event);

    String findApprovalEmailReceiverIdsByEvent(Event event);

    RegistrationApprovalSettingsDto getRegistrationApprovalSettingByEvent(Event event);

    List<NameValueDto> getAllDynamicMergeTagsList(Event event, Long recurringEventId);

    List<TicketingDatesDto> findEventStartAndEndDateByEventsIds(List<Long> eventsIds);

    List<StripeCreditCardDto> getCreditCardDetailByEventTicketId(Event event, User staffUser, Long eventTicketId);

    void eventTicketNonTransferable(Event event, User user, Long ticketId, boolean nonTransferable);

    List<OrderExchangeTicketTypesDto> getTicketTypesForOrderExchange(Event event);

    List<AttributesDefaultValuesDto> getAttributesWithDefaultValues(Event event);

    void checkEventPostEnangeEmailAccessPermitIsPassed(Event event,User loggedInUser);
}
