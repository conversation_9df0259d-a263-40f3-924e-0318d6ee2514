package com.accelevents.services;

import com.accelevents.domain.CustomTemplates;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.TemplateType;
import com.accelevents.dto.*;

import java.util.List;
import java.util.Optional;

public interface ConfirmationEmailService {

    ResponseDto createCustomEmailTemplate(Event event, Long userId,CustomEmailsTemplateDto customEmailsTemplateDto, boolean isDefaultEmail, TemplateType templateType);

    CustomTemplates findByEventAndIsDefaultEmail(Event event, boolean isDefaultEmail,TemplateType templateType);

    ResponseDto updateCustomEmailTemplate(Event event, CustomEmailsTemplateDto customEmailsTemplateDto, Long customEmailId, User user, TemplateType templateType,Long resendTicketEmailId);

    ResponseDto deleteCustomEmailTemplate(Event event, Long customEmailId, User user, TemplateType templateType,Long resendTicketEmailId);

    List<CustomTemplateDetailsDto> getAllCustomEmailTemplatesList(Event event,TemplateType templateType);

    CustomEmailsTemplateDto getCustomEmailTemplateById(Event event, Long confirmationEmailId, TemplateType templateType,Long resendTicketEmailId);

    DataTableResponse getAllCustomEmailTemplates(Event event, PageSizeSearchObj pageSizeSearchObj, TemplateType templateType,String sortDirection);

    ResponseDto resetDefaultCustomEmailTemplate(Event event, User user, Long confirmationEmailId, TemplateType templateType);

    ResponseDto duplicateCustomEmailTemplate(Event event, Long confirmationEmailId, String confirmationEmailName, List<Long> allowedTicketTypes, User user,TemplateType templateType);

    ResponseDto updatePositionForEmailTemplates(Event event, Long customEmailId, Long topEmailTemplateId, Long bottomEmailTemplateId,TemplateType templateType);

    List<CustomTemplates> findAllByIdBetween(long from, long to);

    ResponseDto updateReminderEmailToggle(Event event, User user, boolean isReminderEmailEnable, TemplateType templateType);

    boolean isReminderEmailsEnable(Event event, User user, TemplateType templateType);

    Optional<CustomTemplates> findAllById(Long customEmailId);

    CustomTemplates getConfirmationEmailByOrder(Event event, Long orderId);

    List<Long> findEventIdByEventListAndTemplateType(List<Event> events, TemplateType templateType);
}
