package com.accelevents.services;

import com.accelevents.domain.*;
import com.accelevents.dto.HolderEmailDiscountDto;
import com.accelevents.registration.approval.dto.CouponCodeApprovalTicketDto;
import com.accelevents.services.impl.TicketingAppliedCouponForExistingOrderDto;
import com.accelevents.ticketing.dto.TicketingAppliedCouponDto;
import com.accelevents.ticketing.dto.TicketingOrderDto;

import java.util.List;
import java.util.Map;

public interface TicketingCouponCodeService {

    TicketingAppliedCouponDto applyCouponCode(String couponcode, Long orderid, Event event,Long recurringEventId, TicketingOrder ticketingOrder);

    void removeCouponFromTicketingOrder(String eventURL, Long orderid);

    void removeCouponFromTicketingOrder(Event event, TicketingOrder ticketingOrder);

    TicketingAppliedCouponDto applyCouponCode(String couponCode, Long orderid, Event event, Long recurringEventId, List<HolderEmailDiscountDto> ticketTypeIdAndNoOfTicketsDtoList, TicketingOrder ticketingOrder);

    List<TicketingCoupon> getCouponCodeByTicketingTypeId(List<Long> eventTicketTypeIds, Long eventId);

    Double getDiscountedAmountForOrder(TicketingCoupon ticketingCoupon, TicketingOrder ticketingOrder, Event event, Long recurringEventId, List<HolderEmailDiscountDto> holderEmailDiscountDtos);

    double applyCouponCodeForTicketExchange(String couponCode, Long orderId, Event event, Long recurringEventId, TicketingOrderManager ticketingOrderManager);

    TicketingAppliedCouponForExistingOrderDto applyCouponCodeForOldOrder(Long orderId, Event event, Long recurringEventId, List<HolderEmailDiscountDto> ticketTypeIdAndNoOfTicketsDtoList, List<TicketingOrderManager> ticketingOrderManagers, Map<Long, TicketingOrderManager> ticketTypeIdToOrderManagerMap);

    void removeCouponFromTicketingOrderWhilePurchase(Event event, TicketingOrder ticketingOrder, List<TicketingOrderManager> ticketingOrdersManager);

    TicketingAppliedCouponDto applyCouponCodeInApprovalTicket(Event event, CouponCodeApprovalTicketDto couponCodeApprovalTicketDto, User purchaser);
}
