package com.accelevents.services.integration.cvent.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.IntegrationSourceType;
import com.accelevents.domain.enums.IntegrationType;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.AttendeeAttributeValueDto;
import com.accelevents.dto.AttendeeInfoDTO;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.dto.cvent.APIUserDetails;
import com.accelevents.dto.cvent.CVentObjectDTO;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.messages.LoginLink;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.accelevents.services.integration.UploadAttendeeTrackService;
import com.accelevents.services.integration.cvent.CventConstants;
import com.accelevents.services.integration.cvent.CventService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.session_speakers.util.ThirdPartyUtils;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.NumberUtils;
import com.google.gson.*;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.services.integration.cvent.CventConstants.GRANT_TYPE_CLIENT_CREDENTIALS;
import static com.accelevents.services.integration.cvent.CventJsonElementConstants.*;
import static com.accelevents.services.integration.cvent.CventJsonElementConstants.EventType.*;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.getEventPath;

@Service
public class CventServiceImpl implements CventService {

    private static final Logger log = LoggerFactory.getLogger(CventServiceImpl.class);

    private S3Wrapper s3Wrapper;
    private IntegrationService integrationService;
    private ROEventService roEventService;
    private UploadAttendeeTrackService uploadAttendeeTrackService;
    private TicketingPurchaseFromAttendeeUploadService ticketingPurchaseFromAttendeeUploadService;
    private TicketHolderEditAttributesService ticketHolderEditAttributesService;
    private CommonEventService commonEventService;
    private TicketingOrderRepoService ticketingOrderRepoService;
    private TicketingTypeService ticketingTypeService;
    private EventCommonRepoService eventCommonRepoService;
    private String environment;
    private AutoLoginService autoLoginService;
    private ServiceHelper serviceHelper;
    private final List<Long> customWorkflowWhiteLabelId;
    private static final int MAX_RETRY_COUNT = 3;
    private static final String PAGE_SIZE = "200"; // Cvent API allows max 200 records per page

    @Autowired
    public CventServiceImpl(S3Wrapper s3Wrapper,
                            IntegrationService integrationService,
                            ROEventService roEventService,
                            UploadAttendeeTrackService uploadAttendeeTrackService,
                            TicketingPurchaseFromAttendeeUploadService ticketingPurchaseFromAttendeeUploadService,
                            TicketHolderEditAttributesService ticketHolderEditAttributesService,
                            CommonEventService commonEventService,
                            TicketingOrderRepoService ticketingOrderRepoService,
                            TicketingTypeService ticketingTypeService,
                            EventCommonRepoService eventCommonRepoService,
                            AutoLoginService autoLoginService,
                            ServiceHelper serviceHelper,
                            @Value("${application.environment}") String environment,
                            @Value("${cvent.custom.integration.wl.ids}") String customWorkflowWhiteLabelId) {
        this.s3Wrapper = s3Wrapper;
        this.integrationService = integrationService;
        this.roEventService = roEventService;
        this.uploadAttendeeTrackService = uploadAttendeeTrackService;
        this.ticketingPurchaseFromAttendeeUploadService = ticketingPurchaseFromAttendeeUploadService;
        this.ticketHolderEditAttributesService = ticketHolderEditAttributesService;
        this.commonEventService = commonEventService;
        this.ticketingOrderRepoService = ticketingOrderRepoService;
        this.ticketingTypeService = ticketingTypeService;
        this.eventCommonRepoService = eventCommonRepoService;
        this.autoLoginService = autoLoginService;
        this.serviceHelper = serviceHelper;
        this.environment = environment;
        this.customWorkflowWhiteLabelId = GeneralUtils.convertCommaSeparatedToListLong(customWorkflowWhiteLabelId);
    }

    @Override
    public void validateIntegration(String token){
        Optional<Integration> integrationO = integrationService.findEnabledByApiKeyAndType(token, IntegrationType.CVENT);
        if(integrationO.isPresent()){
            Integration integration = integrationO.get();
            if(!integration.isEnabled()){
                throw new NotFoundException(NotFoundException.NotFound.INTEGRATION_NOT_FOUND);
            }
            if(integration.getTicketingType() == null){
                throw new NotFoundException(NotFoundException.NotFound.INTEGRATION_TICKET_TYPE_NOT_FOUND);
            }
        }else {
            throw new NotFoundException(NotFoundException.NotFound.INTEGRATION_NOT_FOUND);
        }
    }

    @Async
    @Override
    public void backupCventIncomingMessage(String jsonMessage, Integer length, String token){
        try {
            String eventIdString = "NO_EVENT";
            Optional<Integration> integrationO = integrationService.findEnabledByApiKeyAndType(token, IntegrationType.CVENT);
            if(integrationO.isPresent()) {
                eventIdString = Long.toString(integrationO.get().getIntegrationSourceId());
            }
            JsonObject cventJson = new JsonParser().parse(jsonMessage).getAsJsonObject();
            String bucketName = "accel-integration/cvent/" + environment + "/" + eventIdString;

            Optional<JsonElement> eventTypeElementOptional = Optional.ofNullable(cventJson.get(eventType));
            String uploadKey = "KeyNotFound_" + System.currentTimeMillis();
            StringBuilder sbKey = new StringBuilder();
            eventTypeElementOptional.ifPresent(eventType -> {
                sbKey.append(eventType.getAsString());
                sbKey.append("_");

                Optional.ofNullable(cventJson.getAsJsonArray(message)).ifPresent(messageElementArray -> {
                    String confirmationNumberValue = this.getMessageFieldValue(messageElementArray.get(0).getAsJsonObject(), confirmationNumber);
                    sbKey.append(confirmationNumberValue);
                });
                sbKey.append("_");
                sbKey.append(cventJson.get(messageTime).getAsString());
            });

            if(StringUtils.isNotBlank(sbKey.toString())){
                uploadKey = sbKey.toString();
            }

            s3Wrapper.uploadInBucket(IOUtils.toInputStream(jsonMessage, Charset.defaultCharset()), uploadKey, bucketName, "application/json",Constants.STRING_EMPTY);
            log.info("#CVENT: backup upload complete");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void processCventMessage(String message, String token){
        Optional<Integration> integrationO = integrationService.findEnabledByApiKeyAndType(token, IntegrationType.CVENT);
        if(integrationO.isPresent()) {
            if(integrationO.get().getTicketingType() == null){
                throw new NotFoundException(NotFoundException.NotFound.INTEGRATION_TICKET_TYPE_NOT_FOUND);
            }

            JsonObject cventJson = new JsonParser().parse(message).getAsJsonObject();
            Optional<JsonElement> eventTypeElementOptional = Optional.ofNullable(cventJson.get(eventType));

            eventTypeElementOptional.ifPresent(eventTypeElement -> {
                switch (eventTypeElement.getAsString()) {
                    case InviteeOrGuestAccepted:
                        this.processMessageElementArray(cventJson, integrationO.get(), InviteeOrGuestAccepted);
                        break;
                    case InviteeOrGuestModified:
                        this.processMessageElementArray(cventJson, integrationO.get(), InviteeOrGuestModified);
                        break;
                    case InviteeOrGuestCancelled:
                        this.processMessageElementArray(cventJson, integrationO.get(), InviteeOrGuestCancelled);
                        break;
                    case AttendeeManualSynced:
                        this.processMessageElementArray(cventJson, integrationO.get(), AttendeeManualSynced);
                    default:
                        log.info("#CVENT: Not matching any supported type");
                }
            });
        }else {
            throw new NotFoundException(NotFoundException.NotFound.INTEGRATION_NOT_FOUND);
        }

    }

    private void processMessageElementArray(JsonObject cventJson, Integration integration, String eventType){
        Optional.ofNullable(cventJson.getAsJsonArray(message)).ifPresent(messageElementArray -> {
            messageElementArray.forEach( messageElement -> this.processMessageElementBasedOnEventType(messageElement.getAsJsonObject(), integration, eventType));
        });
    }

    private void processMessageElementBasedOnEventType(JsonObject messageObject, Integration integration, String eventType){
        validateInviteeMessageContainsConfirmationNumber(messageObject);
        switch (eventType) {
            case InviteeOrGuestAccepted:
                this.processAttendeeUpload(messageObject, integration);
                break;
            case InviteeOrGuestModified:
                this.processOrderModify(messageObject, integration);
                break;
            case InviteeOrGuestCancelled:
                this.processOrderCancel(messageObject, integration);
                break;
            case AttendeeManualSynced:
                this.processAttendeeManuallySynced(messageObject, integration);
                break;
            default:
                log.info("#CVENT: Not matching any supported type");
        }
    }

    private String getMessageFieldValue(JsonObject messageObject, String key){
        return Optional.ofNullable(messageObject.get(key)).map(JsonElement::getAsString).orElse("");
    }

    private void validateInviteeMessageContainsConfirmationNumber(JsonObject messageObject) {
        String confirmationNumberValue = this.getMessageFieldValue(messageObject, confirmationNumber);
        if(StringUtils.isBlank(confirmationNumberValue)){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CVENT_CONFIRMATION_NUMBER_NOT_PRESENT);
        }else {
            log.info("#CVENT: Incoming message contains confirmationNumber => " + confirmationNumberValue);
        }
    }

    private void processAttendeeUpload(JsonObject messageObject, Integration integration) {
        AttendeeInfoDTO attendeeInfoDTO = new AttendeeInfoDTO();
        attendeeInfoDTO.setQuantity(1L);
        attendeeInfoDTO.setTicketTypeName(integration.getTicketingType().getTicketTypeName());
        attendeeInfoDTO.setTicketTypeId(integration.getTicketingType().getId());
        attendeeInfoDTO.setUploadSource(UploadAttendeeTrack.UploadSource.CVENT);

        attendeeInfoDTO.setFirstName(this.getMessageFieldValue(messageObject, firstName));
        attendeeInfoDTO.setLastName(this.getMessageFieldValue(messageObject, lastName));
        attendeeInfoDTO.setEmail(this.getMessageFieldValue(messageObject, email));
        attendeeInfoDTO.setExternalOrderId(this.getMessageFieldValue(messageObject, confirmationNumber));

        attendeeInfoDTO.setAttributes(this.generateAttributesKeyValueDtoFromMessageObject(messageObject));

        List<AttendeeInfoDTO> attendeeInfoDTOList = new ArrayList<>();
        attendeeInfoDTOList.add(attendeeInfoDTO);

        List<TicketingType> qualifiedTicketTypes = new ArrayList<>();
        Event event = roEventService.findEventByEventId(integration.getIntegrationSourceId());

        Map<Long,TicketingType> ticketingTypes = ticketingTypeService.findAllByEventId(event, false).stream().collect(Collectors.toMap(TicketingType::getId, Function.identity(), (u,v)->u));

        if(StringUtils.isNotBlank(integration.getData()) && messageObject.has(ADMISSION_ID)
                && StringUtils.isNotBlank(messageObject.get(ADMISSION_ID).getAsString())){
            String admissionId = messageObject.get(ADMISSION_ID).getAsString();
            try{
                Gson gson = new Gson();
                APIUserDetails apiUserDetails = gson.fromJson(integration.getData(), APIUserDetails.class);
                if(!CollectionUtils.isEmpty(apiUserDetails.getTicketTypeMapping())
                        && apiUserDetails.getTicketTypeMapping().get(admissionId) != null
                        && NumberUtils.isNumberGreaterThanZero(apiUserDetails.getTicketTypeMapping().get(admissionId))
                        && ticketingTypes.containsKey(apiUserDetails.getTicketTypeMapping().get(admissionId))){
                    attendeeInfoDTO.setTicketTypeId(apiUserDetails.getTicketTypeMapping().get(admissionId));
                    qualifiedTicketTypes.add(ticketingTypes.get(apiUserDetails.getTicketTypeMapping().get(admissionId)));
                }
            }
            catch (Exception ignore){
                log.info("#CVENT: Error parsing integration data");
            }
        }
        if(CollectionUtils.isEmpty(ticketingTypes)){
            qualifiedTicketTypes.add(integration.getTicketingType());
        }


        ticketingPurchaseFromAttendeeUploadService.purchaseTicketsFromCventIntegration(integration, event, attendeeInfoDTOList, qualifiedTicketTypes);
    }

    public List<AttributeKeyValueDto> generateAttributesKeyValueDtoFromMessageObject(JsonObject messageObject){
        List<AttributeKeyValueDto> attributes = new ArrayList<>();

        String valueCellPhone = this.getMessageFieldValue(messageObject, mobilePhone);
        String valueCountryCode = this.getMessageFieldValue(messageObject, workCountryCode);
        attributes.add(new AttributeKeyValueDto(Constants.PHONE_NUMBER, valueCellPhone));
        attributes.add(new AttributeKeyValueDto(Constants.COUNTRY_CODE_TXT, valueCountryCode));

        if(StringUtils.isNotBlank(valueCellPhone) && StringUtils.isNotBlank(valueCountryCode)){
            attributes.add(new AttributeKeyValueDto(Constants.STRING_CELL_SPACE_PHONE, valueCellPhone.concat("|").concat(valueCountryCode)));
        }

        attributes.add(new AttributeKeyValueDto(Constants.ORGANIZATION, this.getMessageFieldValue(messageObject, company)));
        attributes.add(new AttributeKeyValueDto(Constants.COMPANY, this.getMessageFieldValue(messageObject, company)));
        attributes.add(new AttributeKeyValueDto(Constants.Title_WITH_BIG_CASE, this.getMessageFieldValue(messageObject, title)));

        attributes.add(new AttributeKeyValueDto(Constants.BILLING_ADDRESS, this.getWorkAddressAsBillingAddress(messageObject)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_ADDRESS_1, this.getMessageFieldValue(messageObject, workAddress1)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_ADDRESS_2, this.getMessageFieldValue(messageObject, workAddress2)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_ADDRESS_3, this.getMessageFieldValue(messageObject, workAddress3)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_COUNTRY, this.getMessageFieldValue(messageObject, workCountry)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_COUNTRY_CODE, this.getMessageFieldValue(messageObject, workCountryCode)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_STATE, this.getMessageFieldValue(messageObject, workState)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_STATE_CODE, this.getMessageFieldValue(messageObject, workStateCode)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_PHONE, this.getMessageFieldValue(messageObject, workPhone)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_ZIP_CODE, this.getMessageFieldValue(messageObject, workZipcode)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_CITY, this.getMessageFieldValue(messageObject, workCity)));
        attributes.add(new AttributeKeyValueDto(Constants.WORK_FAX, this.getMessageFieldValue(messageObject, workFax)));

        Optional<JsonArray> customFieldsJsonArrayOptional = Optional.ofNullable(messageObject.getAsJsonArray(customFields));
        customFieldsJsonArrayOptional.ifPresent( customFieldsJsonArray -> {
            for (JsonElement customField: customFieldsJsonArray){
                Optional<AttributeKeyValueDto> customFieldKeyValueDtoOptional = this.processCustomField(customField.getAsJsonObject());
                customFieldKeyValueDtoOptional.ifPresent(customFieldKeyValueDto -> {
                    attributes.add(customFieldKeyValueDto);
                });
            }
        });

        return attributes;

    }

    private Optional<AttributeKeyValueDto> processCustomField(JsonObject customField){
        return Optional.ofNullable(customField.get(name)).map( customFieldNameElement -> {
            AttributeKeyValueDto attributeKeyValueDto = new AttributeKeyValueDto();
            attributeKeyValueDto.setKey(this.filterCustomFieldName(customFieldNameElement.getAsString()));
            attributeKeyValueDto.setValue(customField.get(value).getAsString());
            return attributeKeyValueDto;
        });
    }

    private String filterCustomFieldName(String customFieldName){
        return customFieldName.replace("[inactive] ", "");
    }


    private String getWorkAddressAsBillingAddress(JsonObject messageObject){
        StringBuilder billingAddress = new StringBuilder();
        Optional.ofNullable(messageObject.get(workAddress1)).ifPresent(
                workAddress1 -> billingAddress.append(workAddress1.getAsString())
        );
        billingAddress.append(" ");
        Optional.ofNullable(messageObject.get(workAddress2)).ifPresent(
                workAddress2 -> billingAddress.append(workAddress2.getAsString())
        );
        billingAddress.append("|");
        Optional.ofNullable(messageObject.get(workAddress3)).ifPresent(
                workAddress3 -> billingAddress.append(workAddress3.getAsString())
        );
        billingAddress.append("|");
        Optional.ofNullable(messageObject.get(workCity)).ifPresent(
                workCity -> billingAddress.append(workCity.getAsString())
        );
        billingAddress.append("|");
        Optional.ofNullable(messageObject.get(workState)).ifPresent(
                workstate -> billingAddress.append(workstate.getAsString())
        );
        billingAddress.append("|");
        Optional.ofNullable(messageObject.get(workZipcode)).ifPresent(
                worzipCode -> billingAddress.append(worzipCode.getAsString())
        );
        billingAddress.append("|");
        Optional.ofNullable(messageObject.get(workCountryCode)).ifPresent(
                workcountryCode -> billingAddress.append(workcountryCode.getAsString())
        );

        return billingAddress.toString();
    }

    public void processOrderModify(JsonObject messageObject, Integration integration){
        String confirmationNumberValue = this.getMessageFieldValue(messageObject, confirmationNumber);
        Optional<UploadAttendeeTrack> uploadAttendeeTrackOptional = uploadAttendeeTrackService.findByEventIdAndExternalOrderId(integration.getIntegrationSourceId(),  confirmationNumberValue);
        if(uploadAttendeeTrackOptional.isPresent()){
            try {
                AttendeeAttributeValueDto attendeeAttributeValueDto = new AttendeeAttributeValueDto();

                List<AttributeKeyValueDto> attributeKeyValueDtos = this.generateAttributesKeyValueDtoFromMessageObject(messageObject);

                attributeKeyValueDtos.add(new AttributeKeyValueDto(Constants.FIRST_NAME, this.getMessageFieldValue(messageObject, firstName)));
                attributeKeyValueDtos.add(new AttributeKeyValueDto(Constants.LAST_NAME, this.getMessageFieldValue(messageObject, lastName)));
                attributeKeyValueDtos.add(new AttributeKeyValueDto(Constants.EMAIL, this.getMessageFieldValue(messageObject, email)));

                attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
                attendeeAttributeValueDto.setQuestions(new ArrayList<>());


                ticketHolderEditAttributesService.updateTicketPurchaserDataByOrder(uploadAttendeeTrackOptional.get().getOrderIdAccel(), null, attendeeAttributeValueDto);
            } catch (Exception ex){
                log.error("#CVENT: Exception during modify order", ex);
                ex.printStackTrace();
            }
        }else {
            throw new NotFoundException(NotFoundException.NotFound.CVENT_ORDER_FOR_CONFIRMATION_NOT_FOUND);
        }
    }

    private void processOrderCancel(JsonObject messageObject, Integration integration){
        String confirmationNumberValue = this.getMessageFieldValue(messageObject, confirmationNumber);
        Optional<UploadAttendeeTrack> uploadAttendeeTrackOptional = uploadAttendeeTrackService.findByEventIdAndExternalOrderId(integration.getIntegrationSourceId(), confirmationNumberValue);
        if(uploadAttendeeTrackOptional.isPresent()){
            Event event = roEventService.findEventByEventId(integration.getIntegrationSourceId());
            commonEventService.updateEventTicketsOrAddOnToDelete(event, uploadAttendeeTrackOptional.get().getOrderIdAccel(), 0L, true, -1L);

            if(NumberUtils.isNumberGreaterThanZero(event.getWhiteLabelId()) && customWorkflowWhiteLabelId.contains(event.getWhiteLabelId())){
                EventTickets eventTickets = eventCommonRepoService.findByBarcodeId(confirmationNumberValue);
                if(eventTickets != null && TicketStatus.DELETED.equals(eventTickets.getTicketStatus())){
                    String barcode = eventTickets.getBarcodeId() + STRING_UNDERSCORE + UUID.randomUUID().toString();
                    if(barcode.length() > 255){
                        barcode = UUID.randomUUID().toString();
                    }
                    eventTickets.setBarcodeId(barcode);
                    eventCommonRepoService.save(eventTickets);
                }
            }
        }else {
            log.info("#CVENT: Order to cancel not found for confirmation number: " + confirmationNumberValue);
            throw new NotFoundException(NotFoundException.NotFound.CVENT_ORDER_FOR_CONFIRMATION_NOT_FOUND);
        }
    }

    private void processAttendeeManuallySynced(JsonObject messageObject, Integration integration){
        String confirmationNumberValue = this.getMessageFieldValue(messageObject, confirmationNumber);
        Optional<UploadAttendeeTrack> uploadAttendeeTrackOptional = uploadAttendeeTrackService.findByEventIdAndTicketTypeIdAndExternalOrderId(integration.getIntegrationSourceId(), integration.getTicketingType().getId(), confirmationNumberValue);
        if(uploadAttendeeTrackOptional.isPresent()){
            this.processOrderModify(messageObject, integration);
        }else {
            this.processAttendeeUpload(messageObject, integration);
        }
    }

    @Override
    public APIUserDetails getCventAPIUserDetails(Event event){
        APIUserDetails apiUserDetails = null;
        Optional<Integration> optionalIntegration = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getEventId(), IntegrationSourceType.EVENT, IntegrationType.CVENT);
        if(optionalIntegration.isPresent()){
            Integration integration = optionalIntegration.get();
            Gson gson = new Gson();
            apiUserDetails = gson.fromJson(integration.getData(), APIUserDetails.class);
        }
        return apiUserDetails;
    }

    @Override
    public void validateAndUpdateCventAPIUserDetails(APIUserDetails apiUserDetails, Event event){
        String accessToken = getAccessToken(apiUserDetails, MAX_RETRY_COUNT);
        if(StringUtils.isNotBlank(accessToken)){
            integrationService.updateCventAPIUserDetails(apiUserDetails, event);
        }else {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CVENT_API_USER_DETAILS_NOT_VALID);
        }
    }

    @Async
    @Override
    public void processUserCheckIn(EventTickets eventTickets){
        Optional<TicketingOrder> ticketingOrderOptional = ticketingOrderRepoService.findById(eventTickets.getTicketingOrderId());
        ticketingOrderOptional.ifPresent(ticketingOrder -> {
            Optional<Integration> optionalIntegration = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(ticketingOrder.getEventid().getEventId(), IntegrationSourceType.EVENT, IntegrationType.CVENT);
            optionalIntegration.ifPresent(integration -> {
                log.info("##Process CheckIn in Cvent");
                Gson gson = new Gson();
                APIUserDetails apiUserDetails = gson.fromJson(integration.getData(), APIUserDetails.class);
                if(apiUserDetails != null && StringUtils.isNotBlank(apiUserDetails.getClientId())
                        && StringUtils.isNotBlank(apiUserDetails.getClientSecret())
                        && StringUtils.isNotBlank(apiUserDetails.getServer())){

                    Optional<UploadAttendeeTrack> uploadAttendeeTrackOptional = uploadAttendeeTrackService.findByOrderIdAccel(ticketingOrder.getId());
                    uploadAttendeeTrackOptional.ifPresent(uploadAttendeeTrack -> {
                        String accessToken = getAccessToken(apiUserDetails, MAX_RETRY_COUNT);
                        if(StringUtils.isNotBlank(accessToken)){
                            AtomicReference<String> accessTokenRef = new AtomicReference<>(accessToken);
                            processCheckin(apiUserDetails, Collections.singletonList(uploadAttendeeTrack.getExternalOrderId()), accessTokenRef, MAX_RETRY_COUNT);
                        }
                    });
                }

            });
        });
    }

    @Async
    @Override
    public void syncCventCheckInManually(Event event){
        Optional<Integration> optionalIntegration = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getEventId(), IntegrationSourceType.EVENT, IntegrationType.CVENT);
        optionalIntegration.ifPresent(integration -> {
            log.info("##Process Manually CheckIn in Cvent");
            Gson gson = new Gson();
            APIUserDetails apiUserDetails = gson.fromJson(integration.getData(), APIUserDetails.class);
            if(apiUserDetails != null && StringUtils.isNotBlank(apiUserDetails.getClientId())
                    && StringUtils.isNotBlank(apiUserDetails.getClientSecret())
                    && StringUtils.isNotBlank(apiUserDetails.getServer())){

                List<String> externalCheckedInOrders = uploadAttendeeTrackService
                        .findExternalOrderIdsListWithCheckInEventTicketsByEventIdAndType(event, UploadAttendeeTrack.UploadSource.CVENT);
                if (!CollectionUtils.isEmpty(externalCheckedInOrders)) {
                    String accessToken = getAccessToken(apiUserDetails, MAX_RETRY_COUNT);
                    if(StringUtils.isNotBlank(accessToken)){
                        AtomicReference<String> accessTokenRef = new AtomicReference<>(accessToken);
                        List<List<String>> partition = ListUtils.partition(externalCheckedInOrders, 100);
                        for (List<String> currentExternalOrder : partition) {
                            processCheckin(apiUserDetails, currentExternalOrder, accessTokenRef, MAX_RETRY_COUNT);
                        }
                    }
                }
            } else {
                throw new NotFoundException(NotFoundException.NotFound.CVENT_API_USER_DETAILS_NOT_FOUND);
            }
        });
    }

    /**
     * Process Check-in in Cvent system
     * @param apiUserDetails API User Details
     * @param cventAttendeeIds List of Cvent Attendee Ids
     * @param accessToken Access Token
     */

    private void processCheckin(APIUserDetails apiUserDetails, List<String> cventAttendeeIds, AtomicReference<String> accessToken, int retryCount) {
        try {
            if (StringUtils.isNotBlank(accessToken.get()) && !CollectionUtils.isEmpty(cventAttendeeIds)) {

                String filter = String.format(
                        CventConstants.ATTENDEE_CONFIRMATION_FILTER_STRING,
                        cventAttendeeIds.stream()
                                .map(id -> CventConstants.SINGLE_QUOTE + id + CventConstants.SINGLE_QUOTE)
                                .collect(Collectors.joining(Constants.STRING_COMMA))
                );

                String url = CventConstants.Server.valueOf(apiUserDetails.getServer()).getServer()
                        + CventConstants.SEARCH_ATTENDEE_URL;

                HashMap<String, String> headers = new HashMap<>();
                headers.put(Constants.Authorization, Constants.BEARER + accessToken);
                headers.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);

                JSONObject jsonObject = new JSONObject()
                        .put(CventConstants.FILTER, filter);

                HttpPost httpPost = new HttpPost(url);
                StringEntity entity = new StringEntity(jsonObject.toString(), StandardCharsets.UTF_8);
                entity.setContentType(Constants.APPLICATION_JSON);
                httpPost.setEntity(entity);

                HttpResponse response = executeRequest(url, headers, httpPost);
                if (response != null && response.getEntity()!= null && response.getEntity().getContent() != null){
                    String responseContent = getResponseContent(response);
                    if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){

                        Map<String, List<Map<String,String>>> checkInObject = getAttendeeId(responseContent);

                        if(!CollectionUtils.isEmpty(checkInObject)){
                            checkInAttendees(checkInObject, accessToken.get(), apiUserDetails);
                        }
                        else{
                            log.info("attendee Not found in Cvent server. {}", responseContent);
                        }
                    }
                    else if(response.getStatusLine().getStatusCode() == HttpStatus.SC_UNAUTHORIZED || response.getStatusLine().getStatusCode() == 429){
                        if(retryCount<MAX_RETRY_COUNT){
                            log.info("Access token expired or too many requests, fetching new access token. Retry Count: {}", retryCount);
                            String newAccessToken = getAccessToken(apiUserDetails, MAX_RETRY_COUNT);
                            if(StringUtils.isNotBlank(newAccessToken)){
                                accessToken.set(newAccessToken);
                                processCheckin(apiUserDetails, cventAttendeeIds, accessToken, ++retryCount);
                            }
                        }
                    }
                    else {
                        log.info("Error response from Cvent server while fetching attendees by confirmationNumber {}. {}", cventAttendeeIds, responseContent);
                    }
                }

            }
        } catch (Exception exception) {
            log.info("Exception in processCheckin of CventServiceImpl ", exception);
        }
    }

    /**
     * Check-in Attendees in Cvent system
     * @param checkInObject Map of Event Id and List of Attendee Ids
     * @param accessToken Access Token
     * @param apiUserDetails API User Details
     */
    private void checkInAttendees(Map<String, List<Map<String, String>>> checkInObject, String accessToken, APIUserDetails apiUserDetails) {
        try{
            for(String eventId : checkInObject.keySet()){
                List<Map<String,String>> attendeeList = checkInObject.get(eventId);
                checkInAttendees(eventId, attendeeList , accessToken, apiUserDetails, 1);
            }
        }
        catch (Exception exception){
            log.info("Exception in checkInAttendees of CventServiceImpl ", exception);
        }

    }


    /**
     * Recursive method to retry check-in in case of failure
     * @param eventId Event Id
     * @param attendeeList List of Attendee Ids
     * @param accessToken Access Token
     * @param apiUserDetails API User Details
     * @param retry Retry count
     */
    private void checkInAttendees(String eventId, List<Map<String, String>> attendeeList, String accessToken, APIUserDetails apiUserDetails, int retry){

        try{
            String url = CventConstants.Server.valueOf(apiUserDetails.getServer()).getServer()
                    + CventConstants.CHECK_IN_ATTENDEE_URL.replace(CventConstants.EVENT_URL_MACRO, eventId);

            HashMap<String, String> headers = new HashMap<>();
            headers.put(Constants.Authorization, Constants.BEARER + accessToken);
            headers.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);

            JSONArray jsonObject = new JSONArray(attendeeList);

            HttpPost httpPost = new HttpPost(url);
            StringEntity entity = new StringEntity(jsonObject.toString(), StandardCharsets.UTF_8);
            entity.setContentType(Constants.APPLICATION_JSON);
            httpPost.setEntity(entity);

            HttpResponse response = executeRequest(url, headers, httpPost);
            if(response != null && response.getEntity()!= null && response.getEntity().getContent() != null){
                String responseContent = getResponseContent(response);
                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK || response.getStatusLine().getStatusCode() == HttpStatus.SC_MULTI_STATUS){
                    log.info("Attendees checked-in successfully in Cvent for eventId {}. {}", eventId, responseContent);
                } else if(response.getStatusLine().getStatusCode() == HttpStatus.SC_UNAUTHORIZED || response.getStatusLine().getStatusCode() == 429){
                    log.info("Error response from Cvent server while checking-in attendees for eventId {}. {}", eventId, responseContent);
                    if(retry > 0){
                        log.info("Retrying to check-in attendees for eventId {}. Remaining retries: {}", eventId, retry-1);
                        checkInAttendees(eventId, attendeeList, getAccessToken(apiUserDetails, MAX_RETRY_COUNT), apiUserDetails, retry-1);
                    }
                }

            }
        }
        catch (Exception exception){
            log.info("Exception in checkInAttendees of CventServiceImpl ", exception);
        }
    }

    /**
     * Get Attendee Ids from Cvent response
     * @param responseContent Response Content from Cvent
     * @return Map of Event Id and List of Attendee Ids
     */
    private Map<String, List<Map<String,String>>> getAttendeeId(String responseContent) {
        Map<String, List<Map<String,String>>> result = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject(responseContent);
            if (jsonObject.has(CventConstants.DATA) && !jsonObject.isNull(CventConstants.DATA)
                    && jsonObject.getJSONArray(CventConstants.DATA).length() > 0) {

                JSONArray attendees = jsonObject.getJSONArray(CventConstants.DATA);

                for (int i = 0; i < attendees.length(); i++) {
                    JSONObject attendee = attendees.getJSONObject(i);

                    if (attendee.has(CventConstants.ID)
                            && StringUtils.isNotBlank(attendee.getString(CventConstants.ID))
                            && attendee.has(CventConstants.EVENT) && !attendee.isNull(CventConstants.EVENT)
                            && attendee.getJSONObject(CventConstants.EVENT).has(CventConstants.ID)
                            && !attendee.getJSONObject(CventConstants.EVENT).isNull(CventConstants.ID)
                            && StringUtils.isNotBlank(attendee.getJSONObject(CventConstants.EVENT).getString(CventConstants.ID))) {

                        String cventEventId = attendee.getJSONObject(CventConstants.EVENT).getString(CventConstants.ID);

                        if(!result.containsKey(cventEventId)){
                            result.put(cventEventId, new ArrayList<>());
                        }
                        Map<String,String> checkIn = new HashMap<>();
                        checkIn.put(CventConstants.ID, attendee.getString(CventConstants.ID));
                        checkIn.put(CventConstants.CHECK_IN, DateTimeFormatter.ISO_INSTANT.format(Instant.now()));
                        result.get(cventEventId).add(checkIn);
                    }
                }
            }
        } catch (Exception exception) {
            log.info("Exception in getAttendeeId of CventServiceImpl ", exception);
        }
        return result;
    }


    @Override
    @Async
    public void reSyncOrderInCVent(Event event, TicketingOrder ticketingOrder) {
        List<String> externalOrderId = uploadAttendeeTrackService.findByEventIdAndUploadSourceAndAndOrderIdAccel(event.getEventId(), UploadAttendeeTrack.UploadSource.CVENT, ticketingOrder.getId());
        if(!CollectionUtils.isEmpty(externalOrderId) && StringUtils.isNotBlank(externalOrderId.get(0))){
            APIUserDetails cventAPIUserDetails = getCventAPIUserDetails(event);
            if(null != cventAPIUserDetails){
                String qrCode = externalOrderId.get(0);
                if (StringUtils.isNotBlank(qrCode)) {
                    List<EventTickets> eventTickets = eventCommonRepoService.findAllByListOrderIds(List.of(ticketingOrder.getId()));

                    String accessToken = getAccessToken(cventAPIUserDetails, MAX_RETRY_COUNT);

                    if(StringUtils.isNotBlank(accessToken) && !CollectionUtils.isEmpty(eventTickets)){
                        EventTickets eventTicket = eventTickets.get(0);
                        log.info("CusomWorkflowWhiteLabelId {} ", customWorkflowWhiteLabelId);
                        log.info("Event WhiteLabelId {} ", event.getWhiteLabelId());
                        if(NumberUtils.isNumberGreaterThanZero(event.getWhiteLabelId()) && customWorkflowWhiteLabelId.contains(event.getWhiteLabelId())){
                            List<EventTickets> existingTicket = eventCommonRepoService.findAllTicketsBarcodeId(qrCode);
                            if(CollectionUtils.isEmpty(existingTicket)){
                                eventTicket.setBarcodeId(qrCode);
                                eventCommonRepoService.save(eventTicket);
                            }
                            else{
                                List<EventTickets> needToUpdateUUID = existingTicket.stream().filter(e->TicketStatus.DELETED.equals(e.getTicketStatus()))
                                        .collect(Collectors.toList());

                                needToUpdateUUID.forEach(e-> {
                                    e.setBarcodeId(UUID.randomUUID().toString());
                                });

                                eventCommonRepoService.saveAll(needToUpdateUUID);

                                if(needToUpdateUUID.size() == existingTicket.size()){
                                    eventTicket.setBarcodeId(qrCode);
                                    needToUpdateUUID.add(eventTicket);
                                }
                                else{
                                    log.info("Barcode Id {} already exists in system. Hence not updating the same for, eventTicket {}", qrCode , eventTicket.getId());
                                }

                                if(!CollectionUtils.isEmpty(needToUpdateUUID)){
                                    eventCommonRepoService.saveAll(needToUpdateUUID);
                                }
                                else{
                                    log.info("Barcode Id {} already exists in system. Hence not updating the same for, eventTicket {}", qrCode , eventTicket.getId());
                                }

                            }
                        }

                        Map<String, String> cventMappedFields = cventAPIUserDetails.getCventMappedFields();
                        String cventAttendeeId = getCVentAttendeeIdByConfirmationId(accessToken, qrCode, cventAPIUserDetails, MAX_RETRY_COUNT);

                        if(!CollectionUtils.isEmpty(cventMappedFields) && StringUtils.isNotBlank(cventAttendeeId)){
                            if(cventMappedFields.get(CventConstants.AE_ORDER_ID) != null && StringUtils.isNotBlank(cventMappedFields.get(CventConstants.AE_ORDER_ID))){
                                updateInternalInfo(cventAttendeeId, ticketingOrder.getId(), cventMappedFields.get(CventConstants.AE_ORDER_ID), cventAPIUserDetails, MAX_RETRY_COUNT);
                            }
                            if(cventMappedFields.get(CventConstants.AE_TICKET_ID) != null && StringUtils.isNotBlank(cventMappedFields.get(CventConstants.AE_TICKET_ID))){
                                updateInternalInfo(cventAttendeeId, eventTicket.getId(), cventMappedFields.get(CventConstants.AE_TICKET_ID), cventAPIUserDetails, MAX_RETRY_COUNT);
                            }
                            if(cventMappedFields.get(CventConstants.AE_EVENT_NAME) != null && StringUtils.isNotBlank(cventMappedFields.get(CventConstants.AE_EVENT_NAME))){
                                updateInternalInfo(cventAttendeeId, event.getName(), cventMappedFields.get(CventConstants.AE_EVENT_NAME), cventAPIUserDetails, MAX_RETRY_COUNT);
                            }
                            if(cventMappedFields.get(CventConstants.AE_TICKET_TYPE_NAME) != null && StringUtils.isNotBlank(cventMappedFields.get(CventConstants.AE_TICKET_TYPE_NAME))){
                                updateInternalInfo(cventAttendeeId, eventTicket.getTicketingTypeId().getTicketTypeName(), cventMappedFields.get(CventConstants.AE_TICKET_TYPE_NAME), cventAPIUserDetails, MAX_RETRY_COUNT);
                            }
                            if(cventMappedFields.get(CventConstants.AE_BARCODE) != null && StringUtils.isNotBlank(cventMappedFields.get(CventConstants.AE_BARCODE))){
                                updateInternalInfo(cventAttendeeId, eventTicket.getBarcodeId(), cventMappedFields.get(CventConstants.AE_BARCODE), cventAPIUserDetails, MAX_RETRY_COUNT);
                            }
                            if(cventMappedFields.get(CventConstants.AE_MAGICLINK) != null && StringUtils.isNotBlank(cventMappedFields.get(CventConstants.AE_MAGICLINK))){
                                updateInternalInfo(cventAttendeeId, setOrCreateMagicLink(eventTicket, event), cventMappedFields.get(CventConstants.AE_MAGICLINK), cventAPIUserDetails, MAX_RETRY_COUNT);
                            }
                        }

                    }
                }
                else{
                    log.info("Cvent API User Details or Mapped Fields not found for eventId {}", event.getEventId());
                }
            }
            else{
                log.info("Cvent Integration not found for eventId {}", event.getEventId());
            }
        }
        else{
            log.info("Upload Attendee Track not found for eventId {} and orderId {}", event.getEventId(), ticketingOrder.getId());
        }
    }

    private void updateInternalInfo(String attendeeId, Object value, String cventQuestionKey, APIUserDetails cventAPIUserDetails, int retry) {

        try{
            String accessToken = getAccessToken(cventAPIUserDetails, MAX_RETRY_COUNT);
            if(StringUtils.isNotBlank(accessToken)){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(CventConstants.QUESTION, new JSONObject().put(CventConstants.ID, cventQuestionKey));
                jsonObject.put(CventConstants.VALUE, new JSONArray().put(value));

                String url = CventConstants.Server.valueOf(cventAPIUserDetails.getServer()).getServer()
                        + CventConstants.ATTENDEE_UPDATE_URL.replace(CventConstants.ATTENDEE_ID_MACRO, attendeeId);

                HashMap<String, String> headers = new HashMap<>();
                headers.put(Constants.Authorization, Constants.BEARER + accessToken);
                headers.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);


                HttpPut httpPost = new HttpPut(url);
                StringEntity entity = new StringEntity(jsonObject.toString(), StandardCharsets.UTF_8);
                entity.setContentType(Constants.APPLICATION_JSON);
                httpPost.setEntity(entity);

                HttpResponse response = executeRequest(url, headers, httpPost);

                if (response != null && response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                    log.info("Successfully updated internal info in Cvent for attendeeId {}", attendeeId);
                } else if(response != null && response.getStatusLine().getStatusCode() == 429){
                    if(retry > 0){
                        GeneralUtils.threadSleep(ThreadLocalRandom.current().nextInt(1000, 2001));
                        updateInternalInfo(attendeeId, value, cventQuestionKey, cventAPIUserDetails, --retry);
                    }
                }
                else if(response != null && response.getEntity()!=null) {
                    log.info("Failed to update internal info in Cvent for attendeeId {} with response {}", attendeeId, getResponseContent(response));
                }
                else{
                    log.info("Failed to update internal info in Cvent for attendeeId {} with status code {}", attendeeId, response != null ? response.getStatusLine().getStatusCode() : "null response");
                }
            }
        }
        catch (Exception exception){
            log.info("Exception in updateInternalInfo of CventServiceImpl ", exception);
        }
    }

    private String getCVentAttendeeIdByConfirmationId(String accessToken, String confirmationId, APIUserDetails cventAPIUserDetails, int retry){

        try{
            String filter = String.format(
                    CventConstants.ATTENDEE_CONFIRMATION_FILTER_STRING,CventConstants.SINGLE_QUOTE + confirmationId + CventConstants.SINGLE_QUOTE
            );

            String url = CventConstants.Server.valueOf(cventAPIUserDetails.getServer()).getServer()
                    + CventConstants.SEARCH_ATTENDEE_URL;

            HashMap<String, String> headers = new HashMap<>();
            headers.put(Constants.Authorization, Constants.BEARER + accessToken);
            headers.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);

            JSONObject jsonObject = new JSONObject()
                    .put(CventConstants.FILTER, filter);

            HttpPost httpPost = new HttpPost(url);
            StringEntity entity = new StringEntity(jsonObject.toString(), StandardCharsets.UTF_8);
            entity.setContentType(Constants.APPLICATION_JSON);
            httpPost.setEntity(entity);

            HttpResponse response = executeRequest(url, headers, httpPost);

            if (response != null && response.getEntity()!= null && response.getEntity().getContent() != null){
                if(response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                    JSONObject cventAttendee = new JSONObject(getResponseContent(response));
                    if(cventAttendee.has(CventConstants.DATA) && !cventAttendee.isNull(CventConstants.DATA) && cventAttendee.getJSONArray(CventConstants.DATA).length() > 0
                            && cventAttendee.getJSONArray(CventConstants.DATA).getJSONObject(0).has(CventConstants.ID)){

                        return cventAttendee.getJSONArray(CventConstants.DATA).getJSONObject(0).getString(CventConstants.ID);
                    }

                }
                else if(response.getStatusLine().getStatusCode() == HttpStatus.SC_UNAUTHORIZED) {
                    if(retry > 0){
                        String newAccessToken = getAccessToken(cventAPIUserDetails, MAX_RETRY_COUNT);
                        if(StringUtils.isNotBlank(newAccessToken)){
                            return getCVentAttendeeIdByConfirmationId(newAccessToken, confirmationId, cventAPIUserDetails, retry-1);
                        }
                    }
                }
                else if(response.getStatusLine().getStatusCode() == 429 && (retry > 0)){
                        GeneralUtils.threadSleep(ThreadLocalRandom.current().nextInt(200, 1001));
                        return getCVentAttendeeIdByConfirmationId(accessToken, confirmationId, cventAPIUserDetails, retry-1);

                }
            }
        }
        catch (Exception exception){
            log.info("Exception in reSyncOrderInCVent of CventServiceImpl ", exception);
        }
        return accessToken;
    }

    public String setOrCreateMagicLink(EventTickets eventTickets, Event event) {
        try{
            GeneralUtils.threadSleep(1000); // to ensure that user creation is completed before we create magic link
            String eventUrl = getEventBaseUrl(event);
            boolean isNewUser = StringUtils.isBlank(eventTickets.getHolderUserId().getPassword());
            if(!EventFormat.IN_PERSON.equals(event.getEventFormat())){
                eventUrl += "/portal";
                isNewUser = false;
            }
            String token = autoLoginService.getOrCreateEventLevelMagicLinkToken(event, eventTickets.getHolderUserId().getUserId(), null, AutoLoginCreationLocation.TRAY_IO_INTEGRATION_WEBHOOK);
            return eventUrl.concat(QUE_MARK_USER_KEY_EQUALS).concat(token).concat(AND_WAY_TO_LOGIN_EQUALS).concat(LoginLink.MAGIC_LINK.name()).concat(AND_ATTENDEE_MAIL_EQUALS).concat(eventTickets.getHolderUserId().getEmail()).concat(AND_NEW_USER_EQUALS).concat(String.valueOf(isNewUser)).concat(AND_EVENT_ID_EQUALS).concat(String.valueOf(event.getEventId()));
        }
        catch (Exception e){
            log.info("Error in setOrCreateMagicLink for event ticket id event {}",eventTickets.getId(), e);
        }
        return null;
    }

    private String getEventBaseUrl(Event event) {
        return serviceHelper.getEventBaseUrl(event).concat(getEventPath()).concat(event.getEventURL());
    }
    /***
     * Get Registration Custom Fields and Admission Types from Cvent
     * @param event Event object
     * @return CVentObjectDTO containing Custom Fields and Admission Types
     */



    @Override
    public CVentObjectDTO getRegistrationCustomFieldAndAdmissionTypes(Event event) {

        CVentObjectDTO cVentObjectDTO = new CVentObjectDTO();
        APIUserDetails cventAPIUserDetails = getCventAPIUserDetails(event);
        if (cventAPIUserDetails != null) {
            String accessToken = getAccessToken(cventAPIUserDetails, MAX_RETRY_COUNT);
            if (StringUtils.isNotBlank(accessToken)) {
                String admissionURL = CventConstants.Server.valueOf(cventAPIUserDetails.getServer()).getServer()
                        + CventConstants.ADMISSION_ITEMS_FILTER;

                String customFieldsURL = CventConstants.Server.valueOf(cventAPIUserDetails.getServer()).getServer()
                        + CventConstants.CUSTOM_QUESTION;

                String eventListUrl = CventConstants.Server.valueOf(cventAPIUserDetails.getServer()).getServer()
                        + CventConstants.EVENT_LIST_URL;

                AtomicReference<String> accessTokenRef = new AtomicReference<>(accessToken);

                Map<String, String> cVentEvents = getCVentEvents(event, accessTokenRef, cventAPIUserDetails, eventListUrl);

                List<String> totalEventIds = new ArrayList<>(cVentEvents.keySet());

                Map<String, Map<String, String>> admissionTypes = new HashMap<>();

                Map<String, Map<String, String>> customQuestions = new HashMap<>();

                String cventKeys = String.format(CventConstants.EVENT_ID_FILTER, totalEventIds.stream()
                        .map(k -> "'" + k + "'")
                        .collect(Collectors.joining(",")));

                getCVentGlobalObjects(event, accessTokenRef, cventAPIUserDetails, admissionURL, CventConstants.NAME, false, cventKeys, admissionTypes);

                List<List<String>> eventIdBatch = ListUtils.partition(totalEventIds, 100);

                for (List<String> currentExternalOrder : eventIdBatch) {
                    cventKeys = String.format(CventConstants.EVENT_ID_FILTER, currentExternalOrder.stream()
                            .map(k -> "'" + k + "'")
                            .collect(Collectors.joining(",")));

                    getCVentGlobalObjects(event, accessTokenRef, cventAPIUserDetails, customFieldsURL, CventConstants.TEXT, true, cventKeys, customQuestions);

                }

                cVentObjectDTO.setAdmissions(mapCVentObjectByEvent(admissionTypes, cVentEvents));

                cVentObjectDTO.setCustomQuestions(mapCVentObjectByEvent(customQuestions, cVentEvents));

                log.info("Fetched Admission Types: {} and Custom Fields: {} for eventId: {}", admissionTypes, customQuestions, event.getEventId());

            }
        }
        return cVentObjectDTO;
    }

    /***
     * Map CVent Objects by Event Name
     * @param cventObjectByEvent Map of Event Id and Map of CVent Object Id and Name
     * @param cVentEvents Map of Event Id and Event Name
     * @return Map of CVent Object Id and "Event Name/CVent Object Name"
     */

    private Map<String, String> mapCVentObjectByEvent(Map<String, Map<String, String>> cventObjectByEvent, Map<String, String> cVentEvents) {
        Map<String, String> result = new HashMap<>();

        if (CollectionUtils.isEmpty(cventObjectByEvent) || CollectionUtils.isEmpty(cVentEvents)) {
            return result;
        }

        cVentEvents.forEach((eventId, eventName) -> {
            Map<String, String> cventObjects = cventObjectByEvent.get(eventId);
            if (cventObjects != null) {
                cventObjects.forEach((key, value) -> result.put(key, eventName + "/" + value));
            }
        });

        return result;
    }
    /***
     * Get CVent Global Objects like Admission Types and Custom Fields
     * @param event Event Object
     * @param accessToken Atomic Reference of Access Token
     * @param cventAPIUserDetails Cvent API User Details
     * @param url URL to fetch the data
     * @param fieldName Field Name to be fetched from response
     * @param isGetRequest Boolean to indicate if the request is GET or POST
     * @param results Map to store the results
     * @return Map of Event Id and Map of CVent Object Id and Name
     */
    private Map<String, Map<String, String>> getCVentGlobalObjects(Event event,
                                                                   AtomicReference<String> accessToken,
                                                                   APIUserDetails cventAPIUserDetails, String url, String fieldName, boolean isGetRequest, String cventKeys, Map<String, Map<String, String>> results) {

        try {
            String nextPageToken = null;

            int maxObject = 10;

            do {
                HttpRequestBase requestBase;
                if(isGetRequest){
                    URIBuilder uriBuilder = new URIBuilder(url)
                            .addParameter(LIMIT, PAGE_SIZE)
                            .addParameter(CventConstants.FILTER, cventKeys);
                    if (StringUtils.isNotBlank(nextPageToken)) {
                        uriBuilder.addParameter(Constants.TOKEN_LOWER, nextPageToken);
                    }
                    requestBase = new HttpGet(uriBuilder.build());
                }
                else{
                    URIBuilder uriBuilder = new URIBuilder(url)
                            .addParameter(LIMIT, PAGE_SIZE);
                    if( StringUtils.isNotBlank(nextPageToken)){
                        uriBuilder.addParameter(TOKEN_LOWER, nextPageToken);
                    }

                    HttpPost httpPost = new HttpPost(uriBuilder.build());
                    JSONObject params = new JSONObject();
                    params.put(CventConstants.FILTER, cventKeys);


                    StringEntity entity = new StringEntity(params.toString(), StandardCharsets.UTF_8);
                    entity.setContentType(Constants.APPLICATION_JSON);
                    httpPost.setEntity(entity);
                    requestBase = httpPost;
                }


                String responseContent = fetchPage(requestBase.getURI().toString(), accessToken, cventAPIUserDetails, requestBase, MAX_RETRY_COUNT);

                if (StringUtils.isNotBlank(responseContent)) {
                    JSONObject jsonObject = new JSONObject(responseContent);

                    if (jsonObject.has(CventConstants.DATA) && !jsonObject.isNull(CventConstants.DATA)
                            && jsonObject.getJSONArray(CventConstants.DATA).length() > 0) {

                        JSONArray admissionTypesArray = jsonObject.getJSONArray(CventConstants.DATA);
                        for (int i = 0; i < admissionTypesArray.length(); i++) {
                            JSONObject admissionType = admissionTypesArray.getJSONObject(i);
                            if (admissionType.has(CventConstants.ID)
                                    && StringUtils.isNotBlank(admissionType.getString(CventConstants.ID))
                                    && admissionType.has(fieldName)
                                    && StringUtils.isNotBlank(admissionType.getString(fieldName))
                                    && admissionType.has(CventConstants.EVENT) && !admissionType.isNull(CventConstants.EVENT)
                                    && admissionType.getJSONObject(CventConstants.EVENT).has(CventConstants.ID)
                                    && StringUtils.isNotBlank(admissionType.getJSONObject(CventConstants.EVENT).getString(CventConstants.ID))) {

                                String cventEventId = admissionType.getJSONObject(CventConstants.EVENT).getString(CventConstants.ID);
                                Map<String, String> cventAdmission =
                                        results.getOrDefault(cventEventId, new HashMap<>());
                                cventAdmission.put(admissionType.getString(CventConstants.ID),
                                        admissionType.getString(fieldName));
                                results.put(cventEventId, cventAdmission);
                            }
                        }
                    }

                    if (jsonObject.has(CventConstants.PAGING) && !jsonObject.isNull(CventConstants.PAGING)
                            && jsonObject.getJSONObject(CventConstants.PAGING).has(CventConstants.NEXT_PAGE)
                            && StringUtils.isNotBlank(jsonObject.getJSONObject(CventConstants.PAGING).getString(CventConstants.NEXT_PAGE))) {

                        JSONObject paging = jsonObject.getJSONObject(CventConstants.PAGING);
                        nextPageToken = paging.getString(CventConstants.NEXT_PAGE);
                    } else {
                        nextPageToken = null;
                    }
                    maxObject = maxObject - 1;
                } else {
                    log.warn("Empty response content for eventId {}", event.getEventId());
                    break;
                }
            } while (nextPageToken != null && maxObject > 0);

        } catch (Exception exception) {
            log.error("Exception in getAdmission of CventServiceImpl ", exception);
        }

        return results;
    }
    /***
     * Get CVent Events
     * @param event Event Object
     * @param accessToken Atomic Reference of Access Token
     * @param cventAPIUserDetails Cvent API User Details
     * @param url URL to fetch the data
     * @return Map of Event Id and Event Name
     */
    private Map<String, String> getCVentEvents(Event event, AtomicReference<String> accessToken,
                                               APIUserDetails cventAPIUserDetails, String url) {
        Map<String, String> events = new HashMap<>();

        try {
            String nextPageToken = null;

            int maxPage = 10;

            do {

                URIBuilder uriBuilder = new URIBuilder(url)
                        .addParameter(CventConstants.FILTER, CventConstants.EVENT_FILTER)
                        .addParameter(LIMIT, PAGE_SIZE);
                if (StringUtils.isNotBlank(nextPageToken)) {
                    uriBuilder.addParameter(Constants.TOKEN_LOWER, nextPageToken);
                }

               HttpGet  httpGet = new HttpGet(uriBuilder.build());

                String responseContent = fetchPage(httpGet.getURI().toString(), accessToken, cventAPIUserDetails, httpGet, MAX_RETRY_COUNT);

                if (StringUtils.isNotBlank(responseContent)) {
                    JSONObject jsonObject = new JSONObject(responseContent);

                    if (jsonObject.has(CventConstants.DATA) && !jsonObject.isNull(CventConstants.DATA)
                            && jsonObject.getJSONArray(CventConstants.DATA).length() > 0) {

                        JSONArray admissionTypesArray = jsonObject.getJSONArray(CventConstants.DATA);
                        for (int i = 0; i < admissionTypesArray.length(); i++) {
                            JSONObject admissionType = admissionTypesArray.getJSONObject(i);
                            if (admissionType.has(CventConstants.ID)
                                    && StringUtils.isNotBlank(admissionType.getString(CventConstants.ID))
                                    && admissionType.has(CventConstants.TITLE)
                                    && StringUtils.isNotBlank(admissionType.getString(CventConstants.TITLE))
                                    && admissionType.has(CventConstants.CODE)
                                    && StringUtils.isNotBlank(admissionType.getString(CventConstants.CODE))) {
                                events.put(admissionType.getString(CventConstants.ID),
                                        admissionType.getString(CventConstants.TITLE) + " (" + admissionType.getString(CventConstants.CODE) + ")"
                                );
                            }
                        }
                    }

                    if (jsonObject.has(CventConstants.PAGING) && !jsonObject.isNull(CventConstants.PAGING)
                            && jsonObject.getJSONObject(CventConstants.PAGING).has(CventConstants.NEXT_PAGE)
                            && StringUtils.isNotBlank(jsonObject.getJSONObject(CventConstants.PAGING).getString(CventConstants.NEXT_PAGE))) {

                        JSONObject paging = jsonObject.getJSONObject(CventConstants.PAGING);
                        nextPageToken = paging.getString(CventConstants.NEXT_PAGE);
                    } else {
                        nextPageToken = null;
                    }
                    maxPage = maxPage - 1;
                } else {
                    log.warn("Empty response content for eventId {}", event.getEventId());
                    break;
                }
            } while (nextPageToken != null && maxPage > 0);

        } catch (Exception exception) {
            log.error("Exception in getAdmission of CventServiceImpl ", exception);
        }

        return events;
    }

    /***
     * Fetch Page
     * @param url URL to fetch the data
     * @param bearerToken Atomic Reference of Bearer Token
     * @param userDetails Cvent API User Details
     * @param requestBase HTTP Request Base
     * @param retried Boolean to check if the request has been retried
     * @return Response Content as String
     */
    private String fetchPage(String url,
                             AtomicReference<String> bearerToken,
                             APIUserDetails userDetails,
                             HttpRequestBase requestBase,
                             int retried) {
        try {
            HashMap<String, String> headers = new HashMap<>();
            headers.put(Constants.Authorization, Constants.BEARER + bearerToken.get());
            headers.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);

            HttpResponse response = executeRequest(url, headers, requestBase);

            if (response != null && response.getEntity() != null && response.getEntity().getContent() != null) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseContent = getResponseContent(response);

                if (statusCode == HttpStatus.SC_OK) {
                    return responseContent;

                } else if ((statusCode == HttpStatus.SC_UNAUTHORIZED || statusCode == 429) && retried > 0) {
                    String newBearerToken = getAccessToken(userDetails, MAX_RETRY_COUNT);
                    if (StringUtils.isNotBlank(newBearerToken)) {
                        bearerToken.set(newBearerToken);
                        return fetchPage(url, bearerToken, userDetails, requestBase, retried-1);
                    } else {
                        log.info("Failed to refresh token");
                    }
                } else {
                    log.info("Unexpected error {} from Cvent. {}", statusCode, responseContent);
                }
            }
        } catch (Exception e) {
            log.info("Exception while fetching page from Cvent", e);
        }
        return null;
    }




    /**
     * Get Access Token from Cvent
     * @param apiUserDetails API User Details
     * @return Access Token
     */
    private String getAccessToken(APIUserDetails apiUserDetails, long retryCount){
        String accessToken = null;
        try{
            String url = CventConstants.Server.valueOf(apiUserDetails.getServer()).getServer() + CventConstants.TOKEN_URL;
            HashMap<String,String> headers = new HashMap<>();

            headers.put(Constants.CONTENT_TYPE, CventConstants.CVENT_CONTENT_TYPE_URL_ENCODE );
            headers.put(Constants.Authorization, ThirdPartyUtils.BASIC_AUTH(apiUserDetails.getClientId(), apiUserDetails.getClientSecret()));

            HttpPost httpPost = new HttpPost();

            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair(Constants.GRANT_TYPE, GRANT_TYPE_CLIENT_CREDENTIALS));
            params.add(new BasicNameValuePair(Constants.CLIENT_ID, apiUserDetails.getClientId()));

            httpPost.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));


            HttpResponse response = executeRequest(url, headers, httpPost );
            if (response != null && response.getEntity()!= null && response.getEntity().getContent() != null){
                String responseContent = getResponseContent(response);
                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                    JSONObject jsonObject = new JSONObject(responseContent);
                    if(jsonObject.has(Constants.ACCESS_TOKEN)
                            && !jsonObject.isNull(Constants.ACCESS_TOKEN)
                            && StringUtils.isNotBlank(jsonObject.getString(Constants.ACCESS_TOKEN))){
                        accessToken = jsonObject.getString(Constants.ACCESS_TOKEN);
                   }
                    else{
                        log.info("access token not found in response from cvent server. {}", responseContent);
                        throw new NotAcceptableException("406", responseContent,responseContent);
                    }
                }
                else if(response.getStatusLine().getStatusCode() == 429){
                    if (retryCount > 0) {
                        GeneralUtils.threadSleep(ThreadLocalRandom.current().nextInt(200, 1001));
                        return getAccessToken(apiUserDetails, retryCount - 1);
                    } else {
                        log.error("Max retry attempts reached (3). Aborting.");
                        throw new NotAcceptableException("429", "Too Many Requests", responseContent);
                    }
                }
                else {
                    throw new NotAcceptableException(String.valueOf(response.getStatusLine().getStatusCode()), responseContent,responseContent);
                }
            }

        }
        catch (Exception e){
            log.info("exception raised while getting access token for cvent integration.");
        }
        return accessToken;
    }


    /**
     * Execute HTTP Request
     * @param url URL to which request is to be made
     * @param headers Headers to be added to the request
     * @param requestBase HTTP Request (GET/POST/PUT/DELETE)
     * @return HTTP Response
     */
    private HttpResponse executeRequest(String url, HashMap<String, String> headers, HttpRequestBase requestBase) {
        log.info("request received for executing request for url : {} for CVent integration", url);
        HttpResponse response = null;
        try{
            URIBuilder builder = new URIBuilder(url);
            HttpClient httpclient = HttpClientBuilder.create().disableRedirectHandling().build();
            if(!CollectionUtils.isEmpty(headers)){
                headers.forEach((requestBase::setHeader));
            }
            requestBase.setURI(builder.build());
            response = httpclient.execute(requestBase);
        }
        catch (Exception e){
            log.info("exception raised while executing request for url : {}  for cadmium integration.", url);
        }
        return response;
    }

    /**
     * Get Response Content from HTTP Response
     * @param response HTTP Response
     * @return Response Content as String
     */
    private String getResponseContent(HttpResponse response){
        StringBuilder result = new StringBuilder();
        if(response.getEntity() != null){
            try {
                BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
                String line;
                while ((line = rd.readLine()) != null) {
                    result.append(line);
                }
            }
            catch (Exception exception){
                log.info("Exception while read content from response {}", exception.getMessage());
            }
        }
        return result.toString();
    }

}
