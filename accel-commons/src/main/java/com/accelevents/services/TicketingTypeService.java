package com.accelevents.services;

import com.accelevents.common.dto.TicketTypeNameQtyDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.TicketingTypeIdAndCategoryDTO;
import com.accelevents.dto.tray.io.connector.TicketTypeDetails;
import com.accelevents.messages.TicketType;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface TicketingTypeService {

    boolean isAllFreeTicket(List<Long> tickettypeids, Event event);

	TicketingType save(TicketingType ticketingType);

	TicketingType setPositionForTicketingTypeAndsaveTicketingType(TicketingType ticketingType);

	List<TicketingType> findByTicketing_EventidAndCreatedFromNotNullAndDataType(Event event, DataType dataType);

	void updatewithsequence(TicketingType ticketingType,
                            Long topId,
                            Long topBottomId,
                            Event event, User user);

	void delete(TicketingType ticketingType);

	List<TicketingType> getTicketingTypes(Event event,
										  Boolean fetchHiddenTicketsOnly,
										  Long recurringEventId,
										  Ticketing ticketing,
										  DataType dataType);

    List<TicketingType> getTicketingTypesOrAddOnTypes(Event event,
                                                         Boolean fetchHiddenTicketsOnly,
                                                         RecurringEvents recurringEvents,
                                                         Ticketing ticketing,
                                                         List<DataType> dataTypes);

	List<TicketingType> getTicketingTypesDisplay(Event event,
												 Boolean excludeHidden,
												 Long recurringEventId,
												 Ticketing ticketing);

	void deleteAll(List<TicketingType> ticketingTypeList);

	List<TicketingType> getAllByTicketingAndCreatedFromNullOrderByPosition(Event event);

    List<Long> findIdAllByEventIdRecurringIdNull(Event event, List<DataType> dataTypes);

    List<TicketingType> getAllByEventAndCreatedFromIsNotNull(Event event);

	void deleteByRecurringEventId(Long recuringEventId);

	void deleteByRecurringEventIdIn(List<Long> recuringEventIds);

	List<TicketingType> getAllByEventIdAndRecurringId(Event event, Long recurringEventsId, DataType dataType);

	Set<Long> getMainEventTicketTypeIds(List<Long> ticketingTypeIds);

	List<TicketingType> getTicketTypeByCreateFrom(Long typeId);

	List<TicketingType> findAllByTicketing(Long recurringEventId, Event event);

	TicketingType findByidAndEvent(long tickettypeid, Event event);

	Optional<TicketingType> findById(long ticketingTypeId);

	List<TicketingType> findByidInAndEvent(List<Long> tickettypeids, Event event);

	List<TicketingType> findByidInAndEventForWaitList(List<Long> tickettypeids, Event event);

	List<TicketingType> findAllByEventId(Event event, Boolean excludeHidden);

	List<TicketingType> findAllByTicketTypeIdsAndCreatedFrom(List<Long> ticketTypeIds);

	List<TicketingType> findAllByEventIdHost(Event event, boolean fetchHiddenOnly, DataType dataType);

    List<TicketingType> findAllByEventIdAndDataType(Event event, List<DataType> dataType);

    List<Long> findAllIdByEventId(Event event);

    List<Long> findAllTypeIdByEvent(Event event, DataType dataType);

    List<Long> findAllIdByEventAndDataTypeAndExcludeFormatAndDonation(Event event, DataType dataType);

    void saveAll(List<TicketingType> newTicketTypes);

	List<TicketingType> getByTicketingTypeIds(List<Long> availableTicketTypeIds);

    boolean userHadPurchasedTicket(List<Long> tickettypeids, Event event);

	List<Long> getListOfTicketingTypeIds(Long eventId);

    void updateTicketingTypeRecordStatusToCancel(List<Long> ids);

    boolean isAllPaidTicket(List<Long> tickettypeids, Event event);

	long getTicketTypeCountByEventId(Event event);

    List<TicketingType> getAllTicketingTypeByEvent(Event event);

    List<TicketingType> findAllByTicketingAndDataTypes(Long recurringEventId, Event event, List<DataType> dataTypes);

    public List<TicketingType> getAllFreeTicketingTypeByEvent(Event event);

    List<TicketingType> findByRecurringEventId(Long recuringEventId);

    TicketingType findByid(Long id);

    void updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(Ticketing ticketing);

    void resetTicketTypesDateByDays(List<Long> listOfEventId , int days);

    List<TicketTypeDetails> getTicketTypeDetails(Event event);

    List<TicketTypeDetails> getRegistrationRequestEnabledTicketTypeDetails(Event event);

    List<TicketingType> getAllTicketingAndAddonByTicketingAndCreatedFromNullOrderByPosition(Event event);

    long getTicketTypeCountByEventId(Long eventId);

    List<Long> findAllByEventIdNumberOfTickets(Event event);

    List<TicketingType> findAllTicketTypeByTicketing(Ticketing ticketing);

    List<TicketingType> findAllAddonByTicketingId(Ticketing ticketing);

    List<TicketingType> findAllTicketingTypeByCategoryId(Long categoryId);
    List<Object[]> findTicketPriceRangeExcludingGivenTicketTypes(Long eventId, List<String> ticketTypes);

    List<TicketTypeDetails> getAllTicketTypeDetailsByEvent(@Param("event") Event event);

    List<Long> getAllTicketTypeIdsByRecurringEventIdAndDataType(Long recurringEventId);

    List<Long> getAllTicketTypeIdsByRecurringEventIdAndDataTypeAndCreatedFrom(Long recurringEventId,String commaSeparateTicketTypes);

    List<TicketingType> findAllByTicketTypeIds(List<Long> ticketTypeIds);

    List<TicketingType> findAllTicketingTypeByOrganiser(Organizer organiser);

    TicketingType findTicketTypeWithSameCreatedFromAndInRecurringEventId(Long createdFrom, Long recurringEventId);

    List<Object[]> findTicketPriceRangeExcludingGivenTicketTypesAndHiddenTypes(Long eventId, List<TicketType> ticketTypes);

    List<TicketingTypeIdAndCategoryDTO> findTicketingTypeIdAndCategoryIdByEventAndDataType(Event event);

    List<TicketingType> findAllByEventIdAndDataType(Long eventId, List<DataType> dataTypes);

    List<Long> findTicketTypeIdByEventIdAndTicketTypeNames(Long eventId, List<String> ticketTypeNames);

    List<TicketingType> findAllByEventIdAndTicketTypeIdsAndDataTypes(Long eventId,List<Long> ticketTypeIds,List<DataType> dataTypes);

    List<TicketTypeNameQtyDto> findFreeTicketTypeIdAndNumberOfTickets(Event event);

    List<String> findNameByTicketTypeIds(List<Long> ticketTypeIds);
}
