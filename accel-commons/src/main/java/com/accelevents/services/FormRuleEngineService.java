package com.accelevents.services;

import com.accelevents.domain.Event;
import com.accelevents.dto.*;

import java.util.List;

public interface FormRuleEngineService {
    void validateFormRulesOfSessionApproval(Event event, List<AttributeKeyValueDto> attributes);
    void validateFormRulesOfEventRequestForm(Long eventRequestFormId, List<EventRequestFormAttributeDto> attributes);

    void validateFormRulesOfHolderRegistration(Event event, List<HolderBookingDto> holders, long recurringEventId);

    void validateFormRulesOfBuyerRegistration(Event event, List<AttributeKeyValueDto> attributes, long recurringEventId);

    void validateFormRulesUpdateRegistration(Event event, List<AttributeKeyValueDto> attributes, Long recurringEventId);
}
