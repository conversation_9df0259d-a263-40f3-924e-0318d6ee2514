package com.accelevents.services;

import com.accelevents.domain.*;
import com.accelevents.dto.AttendeeDetailsDto;
import com.accelevents.dto.AttendeeInfoDTO;
import com.accelevents.dto.ResponseDto;
import com.accelevents.virtualevents.dto.UploadCSVInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface TicketingPurchaseFromAttendeeUploadService {
    UploadCSVInfo purchaseTicketsFromUploadedCSV(List<Long> ticketTypeIds, MultipartFile multiPartFile, Event event, User user, User staffUser, String mappings) throws IOException;

    void sendNotificationEmailToHostAboutAttendeeUploads(User user, Event event);

    List<AttendeeDetailsDto> checkoutProcessForAttendeeUpload(Event event, User hostUser, Ticketing ticketing, List<TicketingType> ticketingType, List<AttendeeInfoDTO> attendeeListWithOrderIdZERO, List<AttendeeInfoDTO> attendeesWithOrderIdsAllowToCreateOrder, EventBillingAddOns eventBillingAddOns);

    UploadCSVInfo checkCSVHaveCorrectRecords(MultipartFile multiPartFile, Event event, List<Long> ticketTypeIds, String mappings) throws IOException;

    void purchaseTicketsFromCventIntegration(Integration integration, Event event, List<AttendeeInfoDTO> infoDTOList, List<TicketingType> ticketTypeIds);

    void purchaseTicketsFromSchedIntegration(Integration integration, Event event, List<AttendeeInfoDTO> infoDTOList);

    void purchaseTicketsFromZapierIntegration(TicketingType ticketingType, Event event, List<AttendeeInfoDTO> infoDTOList);

    void purchaseTicketsFromTrayConnectorIntegration(TicketingType ticketingType, Event event, List<AttendeeInfoDTO> attendeeInfoDTOList);

    void purchaseTicketsFromCadmiumIntegration(TicketingType ticketingType, Event event, List<AttendeeInfoDTO> infoDTOList);

    List<String>  getAttendeeUploadDefaultFields(Long eventId, Long recurringEventId);


}
