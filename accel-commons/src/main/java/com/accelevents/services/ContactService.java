package com.accelevents.services;

import com.accelevents.auction.dto.ContactDto;
import com.accelevents.auction.dto.ContactsListDto;
import com.accelevents.auction.dto.ContactsListNameDto;
import com.accelevents.auction.dto.ContactsNameDto;
import com.accelevents.domain.Contacts;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.dto.ResponseDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ContactService {
    Contacts addContact(ContactDto contactDto, Event event,Long contactsListId);

    boolean isEmailRegistered(String email, Event event);

    boolean isPhoneRegistered(long phoneNumber, Event event);

    ContactDto getContact(Long contactId, Event event);
    void removeContactFromContactsList(Long contactId, Event event,Long contactsListId);

    void removeContactsFromContactsList(List<Long> contactIds, Event event, boolean deleteAll,Long contactsListId);

    void updateContact(ContactDto contactDto, Event event);

    void deleteContact(Long contactId, Event event);

    void deleteContacts(List<Long> contactIds, Event event, boolean deleteAll);

    DataTableResponse getAllContactsDataByContactsListId(Event event,User user,Long contactsListId,PageSizeSearchObj pageSizeSearchObj);

    void validate(ContactDto contactDto, Event event);

    void deleteContactsByEventId(long eventId);

    List<ContactDto> getContactsByIdsAndContactsListId(Event event, List<Long> ids,Long contactsListId);

    List<ContactDto> createContactList(MultipartFile multiPartFile, String listName, Event event, User user) throws IOException;

    List<ContactDto> uploadContactsCSVIntoContactsList(Long contactsListId,MultipartFile multipartFile, Event event) throws IOException;

    ResponseDto renameContactsList(ContactsListDto contactsListDto,Event event,User user);

    ResponseDto deleteContactsList(Event event,User user,Long contactsListId);

    DataTableResponse getAllContactsListData(Event event, PageSizeSearchObj pageSizeSearchObj,User user);

    List<ContactsListNameDto> getContactsListName(Event event,User user);

    List<ContactsNameDto> getContactsName(Event event, Long contactsListId);


    Long getAudienceCount(Event event, Long contactsListId,User user);

    List<String> parseOptionsFromDefaultValue(String defaultValue);

}
