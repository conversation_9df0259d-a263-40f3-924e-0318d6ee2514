package com.accelevents.services;

import com.accelevents.auction.dto.MessageToContactDto;
import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.dto.*;
import com.accelevents.dto.zapier.AppleDto;
import com.accelevents.dto.zapier.LinkedinDto;
import com.accelevents.dto.zapier.MicrosoftDto;
import com.accelevents.session_speakers.dto.FirstLastNameDto;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;

public interface UserService {

	User save(User user);

    User getOrCreateUser(UserBasicDto speakerDTO, Event event);

    Optional<User> getUserPasswordResetToken(String token);

	void save(UserRole role);

    String getUserEmailById(Long userId);

    String processNameText(TwilioMessage twilioMessage);

	User getOrInsertByPhoneNumberAndEvent(AccelEventsPhoneNumber accelEventsPhoneNumber, long eventId);

    AccessTokenModel getUserDetailForUnsubscribe(User user, Event event);

	List<UserProfileMapping> getUserActivityDetails(User user, Event currentEvent);

	boolean isSalesAdmin(User user, List<String> userRoles);

    boolean isCustomerSupportLead(User user, List<String> userRoles);

    boolean isBillingTypeAdmin(User user, List<String> userRoles);

    void checkIsBillingTypeAdminOrThrowError(User user);

    boolean isBillingTypeAdmin(User user);

	void saveCustomAttribute(User user, Event event, String name, String value);

	String processNameTextForSweepstakes(TwilioMessage twilioMessage, Raffle raff, User user);

	boolean nameIsPresent(String message);

	String[] parseStringToNames(String message);

	void deleteUser(User user);

	List<UserCustomAttribute> findUserCustomAttributesByEventId(long eventId);

	boolean hasCustomAttribute(Event event, User user, String attributeName);

	void addUserRole(User user, String userRole);

	void deleteUserRole(User user, String role);

	void processResetUserPasswordRequest(UserEmailOrPhoneNumberDto userEmailOrPhoneNumberDto, String whiteLabelURL, boolean requiredCode);

    void resendUser2FACodeRequest(User user, WhiteLabel whiteLabel, Event event1);

	User resetNewUserPasswordAndReturnRedirectUrl(String encodedPassword, Optional<User> optionalUser);

    User addAdminRoleForUserAndReturnUser(User user);

	User signUpBidderUserAndReturnUser(UserSignupDto newUser, Event event, boolean isLogin, boolean socialSignUp,
			boolean bidderSignUp, boolean isStaffSignup, boolean skipPassword);

	void setupUserForEvent(User user, Event event);

	User updateUserMostResentEvent(User user, Event event);

	void saveName(String firstName, String lastName, User user);

	void saveEmail(String email, User user);

	Optional<User> getUserBySignupDtoPhoneNumber(UserSignupDto userSignupDto);

    void addUserAsWhiteLabelStaff(User user, WhiteLabel whiteLabel, Event event);

	Optional<User> findUserByPhoneNumber(long phoneNumber);

	boolean isCCRequired(ModuleType moduleType, User user, Event event, boolean isBidPage);


    String getTags(User user, Event event);

	User getOrCreateUserForTicketing(TicketBookingDto ticketBookingDto, Event event,
                                     String ipAddress);

	AccessTokenModel getBidderUserDetailForLoginSignup(UserSignupDto userSignupDto, String eventurl,
                                                       boolean socialSignUp, String ipAddress, boolean skipPassword);

	AccessTokenModel signUpAdminUserAndReturnAccessTokenModel(AdminSignupDto adminSignupDto,boolean isAppleLogin);

	EventUserInfoDto extractUserInfoForEvent(Long userId, Event event, boolean addStripeLinkedCard);

	User getUserByEMailOrCellNumber(String email, String phonenumber, String countryCode);

	UserProfileDto getUserProfileInfo(User user);

    List<EventEndInfoDto>  getUserEventsInfo(User user, String source, String eventStatus, String searchString, Date searchDate);

    List<EventEndInfoDto> getAllEventsForUserInWhitelabel(User user, String source, Long whitelabelId);

    User updateUserProfileField(String fieldName, String value, User user);

    boolean isEventAdmin(Event event, User user);

    EventUserInfoDto extractUserInfoForEvent(User user, Event event, boolean addStripeLinkedCard, ModuleType moduleType);

    @Transactional
    SocialLoginAccessTokenModel addAdminUserToLinkedin(LinkedinDto linkedinDto);

    SocialLoginAccessTokenModel addAdminUserToApple(AppleDto appleDto);

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    AccessTokenModel addUserLinkedin(LinkedinDto linkedinDto, String eventUrl, String ipAddress);

    AccessTokenModel addUserToApple(AppleDto appleDto, String eventUrl);

    AccessTokenModel addUserFacebook(FacebookDto facebookDto, String eventUrl, String ipAddress);

    AccessTokenModel addUserGoogle(GoogleDto googleDto, String eventUrl, String ipAddress);

    SocialLoginAccessTokenModel addAdminUserToMicrosoft(MicrosoftDto microsoftDto);
    AccessTokenModel addUserMicrosoft(MicrosoftDto microsoftDto,String eventUrl,String ipAddress);

	void saveAddress(User user, AddressDto addressDto);

	User getUserForStaffCheckout(String email, String phonenumber, String countryCode, Event event);

	AccessTokenModel createNewEventForAdmin(AdminSignupDto adminSignupDto, String whiteLabelUrl);

	boolean isUserAdminOfAnyWhiteLabelEvent(User user);

	User mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(User user, String countrycode, String phoneNumber,
			String email, CheckOutUserDetail checkOutUserDetail);


	void sendCode(PhoneNumberDto phoneNumberDto, String eventUrl);

	void validateOtp(PhoneNumberDto phoneNumberDto, String code);

	SocialLoginAccessTokenModel addAdminUserToFacebook(String accessToken);

    SocialLoginAccessTokenModel addAdminUserToGoogle(String accessToken);

	List<EventLinkedCard> getAllLinkedCards(User user);

	void addUserCard(User user, Event event, String newToken, boolean isDefault, boolean absorbCardFoundException) throws StripeException, ApiException;

	void setDefaultCard(User user, Event event, String cardId);

    List<StripeSubscriptionDto> getAllSubscribedStripePlans(Event event, User user);

    void unSubscribeStripePlanToCustomer(Event event, String subscriptionId) throws StripeException;

    LinkedCardListDto getLinkedCreditCard(Event event, User user);

	AccessTokenModel signUpWLEventAdminUserAndReturnAccessTokenModel(AdminSignupDto signupDto, String whiteLabelURL);

	String registrationLinkText(TwilioMessage twilioMessage);

	SmsEventUserInfoDto getSmsUserInfoDetail(User user, Event event);

	void addUserAddressAndCard(User user, Event event, UserAddressCardDto useAddressCardDetail);

	AccessTokenModel getAccessTokenModel(UnsubscribeInfoDto unsubscribeInfoDto);

    UserProfileMapping getUserActivityDetailsByEvent(Event event, User user);

    User updateUserProfile(UserProfileUpdateDto updateDto, User user, Event event);


    User updateUserPhoneNumber(User user);

	List<Object[]> getUserDetailsForNeonAccount(Event event);

    void validateOtpAndRemovePhoneNumber(PhoneNumberDto phoneNumberDto, String code);

    Set<User> findUserByEmailIn(Set<String> emails);

	List<User> getUserByFromTo(long from, long to);

	User getUserByEmailOrCellPhone(String emailOrCell, Optional<String> countryCode, Event event);

    boolean validatePasswordRestCode(PasswordResetCodeDto passwordResetCodeDto);

    boolean validateTwoFactorCode(User user, TwoFactorCodeDto twoFactorCodeDto, String authToken);
    User updateUserProfilePhoto(User user, String photoUrl, Event event);
    User findById(Long userId);

    AttendeeProfileDto getUserInfo(Long userId);

	List<BasicUserDto> findCountryCodeByByEmailIn(List<String> emailList);

	Long findUserIdByEmail(String holderEmail);

    List<BigInteger> findAttendeeByEvent(Event event, int size, int page);

    List<BigInteger> findAttendeeIdsByEvent(Long eventId);

    AccessTokenModel getOrCreateUserForSSO(SSOUserDTO ssoUserDTO);

    void updateUserCardRecStatus(User user, Event event, String cardId) throws StripeException;

    FirstLastNameDto getUserInfoByEmail(String email);

    List<BasicUserEventBillingDto> findByUserIdIn(List<Long> userIds);

    List<BigInteger> findAttendeeIdsBySearchAndByEvent(Long eventId, String search);

    void validatePassword(String password);

    UserCheckDto checkUserAndPasswordArePresent(String email);

    Page<User> getAllAttendeeByEvent(Long eventId , String searchString, int from, int size);

    Page<User> getAllAttendeeByEvent(Long eventId, String searchString, int page, int size, String sortParam);

    Page<Object[]> getAllAttendeesByTicketType(Long eventId, String searchString, List<Long> ticketTypeIds, int page, int size);

    boolean validateMagicLinkCode(MagicLinkCodeDto magicLinkCodeDto);

    void deleteUserRoleIfNoEventRemains(User user);

    AccessTokenModel getBidderUserDetailForLoginSignupWithoutEvent(UserSignupDto userSignupDto,
                                                                   boolean socialSignUp, boolean skipPassword);

    User findByUserId(Long userId);

    void sendPushNotificationToEventUsers(MessageToContactDto messageToContactDto, Event event, List<Long> eventUsers) throws FirebaseMessagingException;

    void sendPushNotificationForCheckin(Map<String, String> checkInStatus);

    User savePushNotificationTokenForUser(User user, String token);

    void removePushNotificationTokenForUser(User user, String token);

    String validatePhoneNumberAndEmail(String body,AccelEventsPhoneNumber phoneNumber,Event event);

    int updatePhoneNumber(ConfirmEmailInfoDto confirmEmailInfoDto);

    List<Object[]> findUserNameByUserIdIn(List<Long> userIds);

    void sendMagicLinkToRegisterAttendee(Event event, String email);

    void sendResetPasswordTestEmail(User superAdminUser);

    List<User> findPushNotificationAndUserIdByUserId(List<Long> userIds);

    List<Long> getAllEventUsers(Event event, String ticketTypeIds);

    User updateProfilePicForParticularUser(User logedinUser, String photoUrl, Event event, Long userId);

    User uploadUserCoverPhoto(User user, String coverPhotoUrl, Event event);

    AccessTokenModel signUpApiUserAndReturnAccessTokenModel(AdminSignupDto adminSignupDto, List<Event> eventList, WhiteLabel whiteLabel, Organizer organizer);

    User findUserByIdWithoutCache(Long userId);

    void updateShowProfile(User user, boolean showProfile);

    Optional<UserBasicDto> findUserDetailsByBarcodeId(Long barcodeId);

    List<User> findAttendeesByEventAndUserIds(Long eventId, List<Long> userIds);

    boolean isOrganizerSuperAdminRole(User user, List<String> userRoles);

    void setPasswordForUserEmails(String emails);

    List<Object[]> getUsersGroupByTicketTypeId(Long eventId, List<Long> ticketTypeIds);

    AddressDto getAddressRelatedInformation(User user , Event event);

    AddressDto getUserAddress(String email);

    List<ChimeAttendeeProfileDTO> getUserProfile(List<Long> userIds);

    void saveVirtualBackground(User user, VirtualBackGroundSettingDto virtualBackGroundSettingDto);

    String getVirtualBackground(User user);

    void saveVirtualBackgroundDetails(User user, String virtualBackgroundDetails);

    String getVirtualBackgroundDetails(User user);

    List<User> findByEmails(Set<String> receivers);

    boolean validateCurrentPassword(String currentPasswordFromDto, String currentPasswordFromUser);

    void sendSessionMagicLinkForRegisteredAttendee(Event event, SessionMagicLinkDto sessionMagicLinkDto);

    void sendAutoSignInMagicLink(String email);

    void sendPushNotificationOnMobileAppToTicketHolder(Event event,List<Long> purchaserIds) throws FirebaseMessagingException;

    boolean isUserExistByEmail(String email);

    void checkRequestedUserBelongsToOrganizerOrWhiteLabelOrNot(User user, String whiteLabelUrl, String organizerPageUrl);

    List<Integer> findAllUniqueBrilworksAndAccelEventsUserIds();

    void resendMagicLink(String email, Event event, String expiredMagicLink,String role);

    Optional<User> getUserByEventMagicLink(String userKey, String wayToLogin);

    boolean isUserLoggedInAsAdminForEvent(String userKey, User user, Long eventId);

    void sendPlatformLevelMagicLink(String email,String redirectUrl, Long wlId, Long eventId, boolean isRequireFourDigitCode,String eventUrl);

    ResponseDto sendFourDigitCodeToAccessPlatformLevel(User user);

    void confirmFourDigitCodeToAccessPlatformLevel(User user, Long code);

    void sendAuthenticationCodeForProfileEmailUpdate(User user);

    void createNewPasswordFromResetCode(User user,PasswordDto passwordDto);

    List<Long> getAllUserIdByEventIdAndWhiteLabelIdOrOrganizerId(Long eventId, Long whiteLabelId, Long organizerId);

    String getMagicLinkOfUser(Event event, User user,String email);

    MagicLinkResponseDto expireOldAndGenerateNewMagicLink(Event event, User user, String email, boolean generateNewLink);

    List<User> findUsersByEmailIn(ArrayList<String> emails);

    void saveAll(List<User> users);

    void saveChatExpanded(User user, Boolean isChatExpanded);

    boolean validateUserDomain(String email);

    AccessTokenModel loginUserWithSalesforce(String code, String codeVerifier, String clientId,String eventUrl);

    AccessTokenModel loginUserWithSalesforceUsingSSOToken(String token);

    String getAndCreatePlatformLevelMagicLink(User user, Event event);

    String getHostBaseRedirectUrl(User user, Event event, String hostBaseUrl,String hostBaseRedirectUrl);

    List<UserAttendeeDTO> getUserBasicDetailsByUserIds(List<Long> userIds);

    void resetUsersMostRecentEventAsZero(Event event);
}
