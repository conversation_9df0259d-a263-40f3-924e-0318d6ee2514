package com.accelevents.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.WaitList;
import com.accelevents.dto.CountByTicketType;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.WaitListDto;
import com.accelevents.dto.WaitListUpdateDto;
import com.accelevents.messages.WaitListStatus;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface WaitListService {

    void reorderWaitList(Event event, Long waitlistId, Long topId, Long bottomId);

    void normalizePositions(Long eventId);

    List<WaitListUpdateDto> getWaitListByEvent(Event event);

    DataTableResponse getWaitListByEvent(Event event, Pageable pageRequest);

    void save(Event event, WaitListDto waitListDto,Long recurringEventId);

    void update(Long waitListId, Event event, WaitListUpdateDto waitListDto);

    void remove(Long waitListId, Event event);

    void checkWaitList();

    void checkWaitListExpiration();

    void releaseWaitList(List<Long> waitListIds,Event event);

    List<WaitList> findByEventAndStatus(Event event, WaitListStatus waitListStatus);

    int countByEventIdAndStatus(Event event, WaitListStatus waitListStatus);

    List<WaitList> findByEventAndStatusAndIsEmailSent(Event event, WaitListStatus waitListStatus, boolean isEmailSent);

    int countByEventIdAndStatusAndEmailSentAndTicketingTypeId(Event event, WaitListStatus waitListStatus, boolean isEmailSent, long ticketingTypeId);

    int countByEventIdAndStatusAndEmailSent(Event event, WaitListStatus waitListStatus, boolean isEmailSent);

    List<WaitList> findByEventAndStatusAndEmail(Event event, WaitListStatus waitListStatus, String email);

    List<WaitList> findByEventAndStatusAndEmailAndTicketingTypeId(Event event, WaitListStatus waitListStatus, String email, long ticketingTypeId);

    void checkTicketsAvailability() throws InterruptedException;
    void handleWaitListTriggerForGivenId(Long waitListSettingId);

    void updateWaitListStatus(List<WaitList> waitListsToUpdate, WaitListStatus waitListStatus);

    List<WaitList> findByIdInAndEventAndStatus(List<Long> waitListIds, Event event, WaitListStatus status);

    List<WaitList> findByEventAndStatusAndTicketingTypeId(Event event, WaitListStatus waiting, long waitListTrigger);

    List<CountByTicketType> countByEventIdAndStatusAndTicketingTypeIdIn(Event event, WaitListStatus waitListStatus, List<Long> ticketTypeIds);

    List<WaitList> findByidInAndEventAndTicketTypeIdAndStatus(List<Long> waitListIds, Event event, long ticketTypeId, WaitListStatus waitListStatus);

    void updateStatusDeleted(Long eventId, WaitListStatus deleted);

    void updateStatusDeletedByEventIds(List<Long> eventIds, WaitListStatus deleted);

    List<WaitList> findWaitListByEmail(String email);
}
