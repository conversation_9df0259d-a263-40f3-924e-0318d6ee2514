package com.accelevents.services;

import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.EnumUserSessionStatus;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.domain.ticketing.TicketingCacheTicketTypeContainerDto;
import com.accelevents.dto.*;
import com.accelevents.messages.TicketType;
import com.accelevents.perfomance.dto.TicketReportData;
import com.accelevents.ticketing.dto.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface TicketingDisplayService {

    TicketDisplayPageDto getDisplayPageSettingData(Event event, boolean accessHiddenTicketType, String accessCode, Long recurringEventId,boolean isFromCheckInAttendeePage);

    Boolean checkAvalableAccessCode(Event event);

    List<CategoryDto> getSeatingCategories(Event event, Long recurringEventId);

    boolean isBookBySeat(List<TicketingType> ticketingTypes, Long categoryId);

    List<TicketingType> getTicketType(Event event, Long recurringEventId);

    TicketingOrder setPurchaserInTicketingOrder(User user, Long orderid, Event event);

    AttendeeResponseContainer getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus(Event event, List<TicketType> filter, PageSizeSearchObj pageSizeSearchObj, List<String> ticketStatus, Long recurringEventId, List<Long> ticketTypeIdsString, DataType dataType, boolean isUnique, boolean isBulkPrintPage, boolean sessionCheckinStatus);

    AttendeeResponseContainer getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(Event event, List<EnumUserSessionStatus> checkInStatus, PageSizeSearchObj pageSizeSearchObj, Long recurringEventId, List<Long> ticketTypeIds, Long sessionId);

    List<TicketReportData> getAllTicketsReport(Event event, Long recurringEventId, DataType dataType);

    AttendeeResponseContainer getCheckInAttendeeTicketStats(Event event, List<Long> ticketTypeIds, List<TicketType> ticketTypes, DataType dataType, Long recurringEventId);

    List<AttendeeDto> getAllAttendeesByEvent(Event event);

    List<String> getAccessCode(Long ticketTypeId, Event event);

    void unhideTicketType(Long id, Event event);

    UserRoleDto isStaffOrAdmin(User user, Event event);

    boolean isTicketTypeIsRelevantToAccessCode(Event event, String accessCode, Long recurringEventId, Long ticketTypeId);

    List<EventSearchDetailsDto> getEventSearchDetailsDtoWithTicketTypes(List<EventSearchDetailsDto> event);

    List<EventInfoDto> getEventInfoDtoWithTicketTypes(List<EventInfoDto> eventInfoDtoList);

    Map<Long, List<TicketingType>> getTicketingListMapByEventIds(List<Long> eventIds);

    List<DisplayFeeDto> calculateFeeByTicketTypeId(List<TicketTypeAndQuantityDto> ticketTypeAndQuantityDto, Event event);

    List<TicketTypeDto> getTicketTypeDtoListByTicketTypesList(List<TicketingType> ticketingTypes, Map<Long, BigDecimal> ticketSoldCount);

    List<EventTicketFeesDto> calculateFeeForEventsTickets(List<EventTickets> eventTickets, Event event);

    TicketingSettingDto getTicketingDisplayPageSetting(Event event, String accessCode, Long recurringEventId);

    TicketingBasicSettingDto getTicketingDisplayPageSettingPortal(Event event);

    TicketingSettingPriceDto getTicketingDisplayPageSettingDisplay(Event event, String accessCode, Long recurringEventId, User user);

     OrderUserInfoDto getUsersDataOfOrders(Event event, Long recurringEventId);

    void addCustomAttributeNew(Event eventByURL, CustomAttributeNew customAttribute, Long recurringEventId, DataType dataType);

    void updateCustomAttribute(Long attributeId, CustomAttributeNew customAttribute, Event event, boolean updateForBuyer, Long recurringEventId, DataType dataType);

    PayFlowOrderDto generatePayFlowSecureToken(Event event, Double amount);

    String getOrderStatus(Long orderId);

    List<DiscountCouponDto> getAllDiscountCodes(Event event, Long recurringEventId);

    OrderInvoiceDto getPayLaterOrder(long orderId, Event event);
    TicketingCacheSettingDto getTicketingDisplayPageSettings(Event event, Long recurringEventId);
    TicketingCacheTicketTypeContainerDto getTicketingCacheTicketTypeContainerDto(Event event, Long recurringEventId, String accessCode, String displayCode);

    TicketingCacheTicketTypeContainerDto getAllTicketTypesWithHiddenTicketTypes(Event event, Long recurringEventId);

    TicketingCacheFeeContainerDto getTicketingCacheFeeContainerDto(Event event);
    TicketingCacheCategoriesContainerDto getTicketingCacheEventCategoriesContainerDto(Event event);
    TicketingTypeCodeAndCapacityInfo getTicketingTypeAccessCodeAndAvailableCapacityInfo(Event event, Long recurringEventId);
    Set<String> checkUniqueEmailForHolders(Event event, List<String> holderEmails);

    Set<Long> checkUniqueEmailForBuyer(Event event, String holderEmails);

    TicketInvoiceDto getTicketDetailForCheckOut(long orderId, Event event, long eventTicketId);

    DataTableResponse parseCustomAttributeOptionsCsvFile(MultipartFile multiPartFile);

    List<AttendeeProfileFilterResponseDTO> enableAttributeForPeopleFilter(List<AttendeeProfileFilterDTO> attributeId, Event event, User user);

    List<PeopleAttributeFilterDTO> getAttendeeProfileFilter(Event event, User user);

    List<Map<Long, List<TicketPurchasedDetailsDto>>> getAllPurchasedTicketsByUser(Event event, User user, String ticketingTypeId, Long recurringEventId);

    List<AttendeeDtoV3> getAttendeesList(Event event,List<Long> eventTicketIds);

    TicketTypesCacheDescriptionAndFeesDto getDisplayTicketTypesDescriptionAndFees(Event event, Long recurringEventId);

    List<TicketTypesRemainingCountDto> getTicketTypesRemainingCount(Event event,long recurringEventId, String waitList);
}
