package com.accelevents.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderAttributes;
import com.accelevents.domain.User;
import com.accelevents.dto.PurchaserBookingDto;
import com.accelevents.dto.TicketAttributeValueDto;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.dto.ValueDto;
import com.accelevents.exhibitors.dto.TicketHolderAttributeDto;
import com.accelevents.ticketing.dto.TicketSettingDto;
import com.fasterxml.jackson.core.JsonProcessingException;

import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.math.BigInteger;
import java.util.List;
import java.util.Optional;

public interface TicketHolderAttributesService {

    TicketHolderAttributes save(TicketHolderAttributes ticketHolderAttributes);

    Unmarshaller getUnmarshaller();

    TicketAttributeValueDto unmashler(String xml, Unmarshaller unmarshaller) throws JAXBException;

	String marshaller(TicketAttributeValueDto ticketAttributeValueDto) throws JAXBException;

    String marshaller1(TicketAttributeValueDto1 ticketAttributeValueDto) throws JAXBException;

    TicketAttributeValueDto1 convertTo1(TicketAttributeValueDto ticketAttributeValueDto);

	String parseToJsonString(TicketAttributeValueDto ticketAttributeValueDto);

	String parseToJsonString(TicketAttributeValueDto1 ticketAttributeValueDto1);

    String parseToJsonString(ValueDto valueDto);

	TicketHolderAttributes findById(long ticketHolderAttributesId);
	
	List<BigInteger> findSameForMultipleTicket();

	List<TicketHolderAttributes> getTicketsWithNullValue();

	String parseToJsonString(TicketSettingDto attributes);

    String parseJsonToXML(TicketAttributeValueDto1 ticketAttributeValueDto);

    void updateAttributes(String jsonOldName, long eventId, String jsonNewName);

	void updateCustomAttributeSequence(Long customAttributeId, Long topAttributeId, Long topBottomAttributeId, Long recurringEventId, Event event, User user, boolean isBuyerAttribute);

	List<TicketHolderAttributes> findMailingAddress();

	List<TicketHolderAttributeDto> findByEventTicketIds(List<Long> eventTicketIds);

	void updateTicketHolderAttribute(List<Long> ids);

    List<TicketHolderAttributes> findByIdIn(List<Long> ids);

    void saveAll(List<TicketHolderAttributes> ticketHolderAttributesList);

    void updateFirstNameAndLastName(String firstName, String lastName, List<Long> ticketHolderAttributeIds);

    void updateHolderAttributes(String firstName, String lastName, String wantsToLearn, List<Long> ticketHolderAttributeIds);

    Optional<TicketHolderAttributes> findLatestEventTicketHolderAttributeByOrderId(Long orderId);

    PurchaserBookingDto findLatestPurchaserByOrderId(Long orderId);
}
