package com.accelevents.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.RecurringEvents;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.messages.TicketType;
import com.accelevents.ticketing.dto.CategoryDto;

import java.util.List;
import java.util.Set;

public interface TicketingTypeTicketService {

    TicketingType setPositionForTicketingType(TicketingType ticketingType);

    boolean isAnyTicketExistsWithDonationType(Event event);

    TicketingType getLastItem(TicketingType ticketingType);

    List<TicketingType> getByListOfRecurringEvents(List<RecurringEvents> recurringEventsList, Boolean fetchHiddenTicketsOnly);

    List<TicketingType> getAllByTicketingAndCreatedFromNullOnlyPaidOrderByPosition(Event event);

    List<Object[]> getNumberOfTotalTickets(Set<Long> eventIds);

    Long getNumberOfTotalTicketsInEvent(Long eventId, DataType dataType);

    List<TicketingType> getAllTicketingTypesByRecuurringEvent(Long recurringEventId);

    List<TicketingType> findAllByRecurringEventsDisplay(Long recurringEventId, Boolean excludeHidden, DataType dataType);

    List<TicketingType> findAllByRecurringEvents(Long recurringEventId, Boolean fetchHiddenTicketsOnly, DataType dataType);

    List<TicketingType> findAllTicketTypeOrAddOnByRecurringEvents(List<Long> recurringEventsList, Boolean fetchHiddenTicketsOnly, List<DataType> dataTypes);

    List<TicketingType> getTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(Long ticketTypeId, Event event);

    List<TicketingType> findAllByTicketingAndRecuringEvent(Long recurringEventId, Event event);

    List<TicketingType> findAllTicketTypeByEventAndRecurringEvent(Long recurringEventId, Event event);

    List<TicketingType> findAllByEventIdAndRecurringEventIdPresent(Event event, boolean hidden);

    List<TicketingType> findAllByEvent(Event event);

    List<TicketingType> findTicketingByEventIdList(List<Long> eventIds);

    List<Long> findAllIdByEventIdAndRecurringEvent(Event event, Long recurringEventId);

    void createGeneralAdmissionTicketIfNotExists(Ticketing ticketing, Event event);

    TicketingType createFreeAdmissionTicketIfNotExists(Ticketing ticketing, Event event);

    List<Long> getTicketTypeIdsByCreatedFromsAndInRecurringEventId(List<Long> createdFroms, Long recurringEventId);

    void markAsHiddenByTicketTypeIds(List<Long> newTicketIds);

    List<TicketingType> getTicketingTypesByRecurringList(List<Long> oldReIdsList);

    List<Long> findRecurringEventTypeIdByEventAndCreatedFrom(Event event, Long createdFrom);

    List<Long> getTicketTypeIdsByCreatedFroms(List<Long> createdFrom);

    List<TicketingType> findByCategoryId(Long categoryId);

    List<CategoryDto> getAllTicketingTypes(Long eventId);

    void updateTicketingTypeRecStatusByRecurringEventIds(List<Long> recurringEventIds, RecordStatus status);

    List<Long> getTicketTypeIdsByCreatedFromAndInRecurringEventIdAndDataType(List<Long> createdFroms, Long recurringEventId, DataType dataType);

    List<TicketingType> getAllTicketingTypesByRecurringEventBasedOnAllowSeating(Long recurringEventId, List<TicketType> ticketTypesList);

    boolean isUserAllowedToCreateMeetingByTicketTypeIds(List<Long> ticketTypeIds);
    List<Object[]> getTicketTypeMeetingCreationStatusByTicketTypeIds(List<Long> ticketingTypeIds);

}
