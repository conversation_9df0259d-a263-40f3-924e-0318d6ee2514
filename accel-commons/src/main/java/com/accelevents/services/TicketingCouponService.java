package com.accelevents.services;

import com.accelevents.auction.dto.UploadedTicketingCouponResponseContainer;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.ticketing.dto.DiscountCouponDto;
import com.accelevents.ticketing.dto.TicketingCouponDto;
import com.accelevents.ticketing.dto.TicketingCouponUploadDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;


public interface TicketingCouponService {

	List<TicketingCoupon> getAllByEventId(long eventId);

	void save(TicketingCouponDto ticketingCoupon, Event event,Long recurringEventId);

	TicketingCoupon getByRecurringEventIdAndCouponCode(Long recurringEventId, String couponCode);

	List<DiscountCouponDto> getAllCoupons(Event event,Long recurringEventId);

    DataTableResponse getAllCoupon(Event event, Long recurringEventId, String searchString, int page, int size);

    List<TicketingCoupon> getAllIntegrationEnabledCouponByEventId(long eventId, Long recurringEventId);

    TicketingCoupon getByEventIdAndCouponCode(long eventId, String couponcode, Long recurringEventId);

    TicketingCoupon getByEventIdAndCouponCode(long eventId, long couponId, Long recurringEventId);

    List<TicketingCoupon> getTicketingCouponList(Event event, Long recurringEventId, Ticketing ticketing);

    long couponUsed(TicketingCoupon ticketingCoupon, Event event, User purchaser, Long recurringEventId);

    long getRemainingCount(TicketingCoupon ticketingCoupon, TicketingOrder ticketingOrder);

    long couponUsedPerUser(TicketingCoupon ticketingCoupon, Event event, User purchaser,Long recurringEventId);

    void deleteCouponCode(Event event, long id, Long recurringEventId);

	void updateCouponCode(Event event, long id, TicketingCouponDto ticketingCouponToUpdate,Long recurringEventId);

    TicketingCoupon save(TicketingCoupon newCC);

    List<TicketingCoupon> getAllByEventIdAndRecurringEventIdIsNull(Event event);

	void deleteByRecurringIdAndEventId(List<Long> recurringEventsToBeDelete, Event event);

    void saveAll(List<TicketingCoupon> couponList);

    List<TicketingCoupon> getAllWhichHavingRecurringEventIdNotNull(Event event);

    void updateCreatedFrom(TicketingCouponDto ticketingCouponToUpdate, TicketingCoupon ticketingCoupon);

    List<TicketingCoupon> getTicketingCouponByRecurringEventList(List<Long> oldReIdsList);

    UploadedTicketingCouponResponseContainer parseMultipleDiscountCodes(MultipartFile multiPartFile, Event event, Long recurringEventId);

    void saveMultipleDiscountCodes(TicketingCouponUploadDto ticketingCouponDto, Event event, Long recurringEventId);

    Long countByEventOrRecurringEvent(Event event, Long recurringEventId);

    void updateTicketCouponRecStatusByRecurringEventIds(List<Long> recurringEventIds, long eventId, RecordStatus status);

    Optional<TicketingCoupon> findById(Long id);

    void resetCouponDates(List<Long> listOfEventId, int days);

    Long countByEventTicketingTypeId(Long eventTicketTypeId);

    boolean isEnableThirdPartyValidation(Long eventId);

    boolean isTicketingCouponAvailableForEvent(Event event, Long recurringEventId);

    Long getCouponCodeIdByEventIdAndCouponCode(long eventId, String couponCode, Long recurringEventId);

    Map<Long, String> createMapOfCouponNameAndIdByIds(List<Long> ids);

    String getCouponCodeNameById(Long couponId);
}
