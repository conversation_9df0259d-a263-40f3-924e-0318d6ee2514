package com.accelevents.services;

import com.accelevents.domain.ContactModuleSettings;
import com.accelevents.domain.Event;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.WhiteLabel;

import java.util.Optional;

public interface ContactModuleSettingsService {

	void save(ContactModuleSettings contactModuleSettings);

	Optional<ContactModuleSettings> findContactModuleSettingsByEvent(Event event);

    void updateAllEventEngageEmailLimit(WhiteLabel whiteLabel, Organizer organizer);

    void updateEventEngageEmailLimit(Event event);
}
