package com.accelevents.services.neptune;

import com.accelevents.domain.Event;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.networking.services.NetworkingLounge;
import com.accelevents.networking.services.SequenceCountService;
import com.accelevents.ro.neptune.NeptuneDBService;
import com.accelevents.ro.neptune.RONeptuneAttendeeDetailService;
import com.accelevents.services.UserService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tinkerpop.gremlin.process.traversal.P;
import org.apache.tinkerpop.gremlin.process.traversal.Scope;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.T;
import org.apache.tinkerpop.gremlin.structure.VertexProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.tinkerpop.gremlin.process.traversal.P.within;
import static org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.__.*;

@Service
public class NeptuneNetworkingLoungeService {

    @Autowired
    private NeptuneDBService neptuneDBService;

    private static final Logger log = LoggerFactory.getLogger(NeptuneNetworkingLoungeService.class);

    @Autowired
    private SequenceCountService sequenceCountService;

    @Autowired
    private UserService userService;

    @Autowired
    private NeptuneInterestServiceImpl neptuneInterestService;

    @Autowired
    private NeptuneAttendeeDetailService neptuneAttendeeDetailService;

    private GraphTraversalSource g;

    private GraphTraversalSource rog;

    public static final String USER_ID = "userId";
    public static final String NL_ID = "nlId";
    public static final String NAME = "name";
    public static final String EVENT_ID = "eventId";
    public static final String DESCRIPTION = "description";
    public static final String CREATED_BY = "createdBy";
    public static final String PROFILE_IMAGE = "profileImage";
    public static final String PHOTOS = "photos";
    public static final String CREATED_AT = "createdAt";
    public static final String MEETING_OBJ = "meetingObj";
    public static final String BANNER_IMAGE = "bannerImage";
    public static final String POSITION = "position";
    public static final String AUTO_GENERATE_LIVE_CAPTIONS = "autoGenerateLiveCaptions";

    @PostConstruct
    public void init() {
        g = neptuneDBService.getGraphTraversalSource();
        rog = neptuneDBService.getReadOnlyGraphTraversalSource();
    }

    @CacheEvict(value = "getNetworkingLoungeByEventId", allEntries = true)
    public String addNetworkingLounge(NetworkingLounge networkingLounge, String userId, String eventId) {
        String nlId = sequenceCountService.getMyLoungeCountWithIncrement();
        g.V(eventId).fold().coalesce(unfold(), addV(NeptuneDBService.EVENT_LABEL).property(T.id, eventId)).next();
        if(!g.V(nlId).hasNext()) {

            g.V(eventId).as(NeptuneDBService.EVENT_LABEL)
                    .addV(NeptuneDBService.NETWORKING_LOUNGE)
                    .property(T.id, nlId)
                    .property(NL_ID, nlId)
                    .property(NAME, networkingLounge.getName())
                    .property(EVENT_ID, eventId)
                    .property(DESCRIPTION, networkingLounge.getDescription() != null ? networkingLounge.getDescription() : Constants.STRING_EMPTY)
                    .property(CREATED_BY, userId)
                    .property(PROFILE_IMAGE, networkingLounge.getProfileImage() != null ? networkingLounge.getProfileImage() : Constants.STRING_EMPTY)
                    .property(PHOTOS, networkingLounge.getPhotos() != null ? networkingLounge.getPhotos() : Constants.STRING_EMPTY)
                    .property(CREATED_AT, DateUtils.getFormattedDateString(new Date()))
                    .property(BANNER_IMAGE, networkingLounge.getBannerImage() != null ? networkingLounge.getBannerImage() : Constants.STRING_EMPTY)
                    .property(POSITION, String.valueOf(networkingLounge.getPosition()))
                    .as(NeptuneDBService.NETWORKING_LOUNGE)
                    .addE(NeptuneDBService.NETWORKING_LOUNGE_ATTACHED)
                    .from(NeptuneDBService.NETWORKING_LOUNGE)
                    .to(NeptuneDBService.EVENT_LABEL)
                    .next();
        }
        log.info("Create a new networking lounge for event: {}",eventId);
        return nlId;
    }

    @Cacheable(value = "getNetworkingLoungeByLoungeId", key = "#p0", condition="#p0!=null", unless = "#result == null")
    public NetworkingLounge getNetworkingLounge(String id) {
        NetworkingLounge networkingLounge = null;
        GraphTraversal attendee = rog.V(id).valueMap();
        if (attendee.hasNext()) {
            Map<String, List<String>> networkingLoungeProperties = (Map<String, List<String>>) attendee.next();
            if (null != networkingLoungeProperties && !networkingLoungeProperties.isEmpty()) {
                networkingLounge = new NetworkingLounge(networkingLoungeProperties);
            }
        }
            return networkingLounge;
    }

    @Caching(evict = {
            @CacheEvict(value = "getNetworkingLoungeByEventId", allEntries = true),
            @CacheEvict(value = "getNetworkingLoungeByLoungeId", key = "#p0")
    })
    public void removeNetworkingLounge(String id) {
        g.V(id).drop().iterate();
        log.info("Remove networking lounge: {}",id);
    }

    @Caching(evict = {
            @CacheEvict(value = "getNetworkingLoungeByEventId", allEntries = true),
            @CacheEvict(value = "getNetworkingLoungeByLoungeId", key = "#p1")
    })
    public void updateNetworkingLounge(NetworkingLounge netLounge, String id, String userId, String eventId) {
        Map<String, String> networkingLoungeProperties = getNetworkingLoungeProperties(netLounge,userId,eventId,id);
        g.V(id).property(VertexProperty.Cardinality.single, CREATED_BY, networkingLoungeProperties.get(CREATED_BY))
                .property(VertexProperty.Cardinality.single, NL_ID, networkingLoungeProperties.get(NL_ID))
                .property(VertexProperty.Cardinality.single, EVENT_ID, networkingLoungeProperties.get(EVENT_ID))
                .property(VertexProperty.Cardinality.single, NAME, networkingLoungeProperties.get(NAME))
                .property(VertexProperty.Cardinality.single, DESCRIPTION, networkingLoungeProperties.get(DESCRIPTION))
                .property(VertexProperty.Cardinality.single, PROFILE_IMAGE, networkingLoungeProperties.get(PROFILE_IMAGE))
                .property(VertexProperty.Cardinality.single, PHOTOS, networkingLoungeProperties.get(PHOTOS))
                .property(VertexProperty.Cardinality.single, BANNER_IMAGE, networkingLoungeProperties.get(BANNER_IMAGE))
                .property(VertexProperty.Cardinality.single, MEETING_OBJ, networkingLoungeProperties.get(MEETING_OBJ))
                .property(VertexProperty.Cardinality.single, POSITION, networkingLoungeProperties.get(POSITION)).next();
        log.info("update networking lounge: {} from event: {}",id,eventId);
    }

    private Map<String, String> getNetworkingLoungeProperties(NetworkingLounge netLounge, String userId, String eventId, String id) {
        Map<String, String> networkingLoungeProperties = new HashMap<>();
        networkingLoungeProperties.put(NL_ID,id);
        networkingLoungeProperties.put(CREATED_BY,userId);
        networkingLoungeProperties.put(EVENT_ID,eventId);
        networkingLoungeProperties.put(NAME, StringUtils.defaultString(netLounge.getName()));
        networkingLoungeProperties.put(DESCRIPTION, StringUtils.defaultString(netLounge.getDescription()));
        networkingLoungeProperties.put(PROFILE_IMAGE, StringUtils.defaultString(netLounge.getProfileImage()));
        networkingLoungeProperties.put(PHOTOS, StringUtils.defaultString(netLounge.getPhotos()));
        networkingLoungeProperties.put(MEETING_OBJ, StringUtils.defaultString(netLounge.getMeetingObj()));
        networkingLoungeProperties.put(BANNER_IMAGE, StringUtils.defaultString(netLounge.getBannerImage()));
        networkingLoungeProperties.put(POSITION, String.valueOf(netLounge.getPosition()));
        return networkingLoungeProperties;
    }

    @Cacheable(value = "getNetworkingLoungeByEventId", key = "#p0.eventId", condition="#p0!=null", unless = "#result == null")
    public List<NetworkingLounge> getEventNetworkingLounges(Event event) {
        List<NetworkingLounge> networkingLounges = new ArrayList<>();
        GraphTraversal netLounges = rog.V(String.valueOf(event.getEventId()))
                .in(NeptuneDBService.NETWORKING_LOUNGE_ATTACHED)
                .valueMap();
        while (netLounges.hasNext()) {
            Map<String, List<String>> networkingLoungeProperties = (Map<String, List<String>>) netLounges.next();
            if (null != networkingLoungeProperties && !networkingLoungeProperties.isEmpty()) {
                networkingLounges.add(new NetworkingLounge(networkingLoungeProperties,event.getEquivalentTimeZone()));
            }
        }
        log.info("Get all networking lounges of event: {}",event.getEventId());
        return networkingLounges;
    }

    public List<NetworkingLoungesAndMembersDto> getNetworkingLoungeMembersCountAndUserId(List<NetworkingLounge> networkingLounges, Event event, Long userId) {
        long startTime = System.currentTimeMillis();
        log.info("Starting getNetworkingLoungeMembersCountAndUserId with {} lounges", networkingLounges != null ? networkingLounges.size() : 0);

        if (networkingLounges == null || networkingLounges.isEmpty()) {
            return Collections.emptyList();
        }

        // Get all lounge IDs
        List<String> loungeIds = networkingLounges.stream()
                .map(NetworkingLounge::getId)
                .collect(Collectors.toList());

        // Create a map for quick lookup of NetworkingLounge by ID
        Map<String, NetworkingLounge> loungeMap = networkingLounges.stream()
                .collect(Collectors.toMap(NetworkingLounge::getId, Function.identity()));

        List<NetworkingLoungesAndMembersDto> results = getLoungeMembersDetailsByNetworkingLoungeIds(String.valueOf(event.getEventId()),String.valueOf(userId), loungeIds);

        if (!CollectionUtils.isEmpty(results)) {
            results.forEach(result -> {
                if (result != null && result.getNetworkingLoungeId() != null) {
                    result.setNetworkingLounge(loungeMap.get(result.getNetworkingLoungeId()));
                }
            });
        }

        long endTime = System.currentTimeMillis();
        log.info("Completed getNetworkingLoungeMembersCountAndUserId in {} ms for {} lounges",
                (endTime - startTime), networkingLounges.size());

        return results;
    }

    public List<NetworkingLoungesAndMembersDto> getLoungeMembersDetailsByNetworkingLoungeIds(String eventId,String userIdStr, List<String> networkingLoungeIds) {
        // Batch process lounges to reduce number of queries
        int batchSize = 15;
        List<List<String>> batches = new ArrayList<>();

        for (int i = 0; i < networkingLoungeIds.size(); i += batchSize) {
            batches.add(networkingLoungeIds.subList(i, Math.min(i + batchSize, networkingLoungeIds.size())));
        }

        log.info("Split {} networkingLoungeIds into {} batches of size {}", networkingLoungeIds, batches.size(), batchSize);

        // Results container
        List<NetworkingLoungesAndMembersDto> results = Collections.synchronizedList(new ArrayList<>());

        // Process batches in parallel
        batches.parallelStream().forEach(batch -> {
            long batchStartTime = System.currentTimeMillis();
            try {
                // Build a more efficient query that gets all the data we need in one go
                List<Map<String, Object>> batchResults = new ArrayList<>();

                rog.V().hasId(P.within(batch))
                        .has(EVENT_ID, eventId)
                        .project(Constants.ID, Constants.MEMBERS)
                        .by(T.id)
                        .by(
                                bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION)
                                        .otherV()
                                        .and(
                                                not(has(NeptuneAttendeeDetailService.DELETED_ATTENDEE, String.valueOf(true))),
                                                not(bothE(NeptuneDBService.ATTENDING_LABEL).has(NeptuneAttendeeDetailService.BLOCKED, String.valueOf(true)).otherV().hasId(eventId)),
                                                bothE(NeptuneDBService.ATTENDING_LABEL).otherV().hasId(eventId)
                                        )
                                        .dedup()
                                        .values(USER_ID)
                                        .fold()
                        )
                        .forEachRemaining(batchResults::add);

                // Temporary storage for this batch
                List<NetworkingLoungesAndMembersDto> batchDtos = new ArrayList<>();
                Map<String, List<Long>> loungeUserIdsMap = new HashMap<>();

                // First pass: create DTOs and collect user IDs
                for (Map<String, Object> item : batchResults) {
                    String loungeId = (String) item.get(Constants.ID);
                    List<String> memberUserIds = (List<String>) item.get(Constants.MEMBERS);

                    // Create result object
                    NetworkingLoungesAndMembersDto dto = new NetworkingLoungesAndMembersDto();
                    dto.setMemberCount(memberUserIds.size());
                    dto.setUserJoined(memberUserIds.contains(userIdStr));
                    dto.setNetworkingLoungeId(loungeId);
                    // Store the user IDs we need to fetch for this lounge
                    if (!memberUserIds.isEmpty()) {
                        int count = Math.min(memberUserIds.size(), 5);
                        List<Long> userIdsToFetch = memberUserIds.subList(0, count).stream()
                                .map(Long::parseLong)
                                .collect(Collectors.toList());

                        loungeUserIdsMap.put(loungeId, userIdsToFetch);
                    } else {
                        dto.setUserDetailsDtos(Collections.emptyList());
                    }

                    batchDtos.add(dto);
                }

                // Collect all user IDs from all lounges in the batch
                Set<Long> allUserIdsToFetch = loungeUserIdsMap.values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());

                // Execute a single query to get all users
                Map<Long, UserAttendeeDTO> userMap = new HashMap<>();
                if (!allUserIdsToFetch.isEmpty()) {
                    try {
                        List<UserAttendeeDTO> allUsers = userService.getUserBasicDetailsByUserIds(new ArrayList<>(allUserIdsToFetch));
                        userMap = allUsers.stream().collect(Collectors.toMap(UserAttendeeDTO::getUserId, Function.identity(), (u1, u2) -> u1));
                    } catch (Exception e) {
                        log.error("Error fetching user details for batch: {}", e.getMessage());
                    }
                }

                // Second pass: populate the DTOs with user details
                for (NetworkingLoungesAndMembersDto dto : batchDtos) {
                    String loungeId = dto.getNetworkingLoungeId();
                    List<Long> userIds = loungeUserIdsMap.get(loungeId);

                    if (userIds != null && !userIds.isEmpty()) {
                        List<UserDetailsDto> userDetailsDtos = userIds.stream()
                                .filter(userMap::containsKey)
                                .map(userMap::get)
                                .map(UserDetailsDto::new)
                                .collect(Collectors.toList());

                        dto.setUserDetailsDtos(userDetailsDtos);
                    }
                }

                // Add all DTOs from this batch to the results
                results.addAll(batchDtos);

                long batchEndTime = System.currentTimeMillis();
                log.info(" getNetworkingLoungeMembersCountAndUserId | Processed batch of {} lounges in {} ms", batch.size(), (batchEndTime - batchStartTime));
            } catch (Exception e) {
                log.error("Error processing batch of lounges: {}", e.getMessage(), e);
            }
        });
        return results;
    }
    /**
     *This method is used for datafix but if you need list of networking lounges of event then used it
     */
    public List<String> getNetworkingLoungesOfEvent(String eventId) {
        List<String> networkingLounges = new ArrayList<>();
        GraphTraversal netLounges = rog.V().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).has(EVENT_ID,eventId).id();
        while (netLounges.hasNext()) {
            networkingLounges.add(netLounges.next().toString());
        }
        return networkingLounges;
    }

    /**
     *This method is used for datafix of networking lounge's connection only
     */
    public List<String> joinedAttendeeWithLounge(String loungeId) {
        List<String> attachedUsers = new ArrayList<>();
        GraphTraversal attchedUsersTraversal = rog.V(loungeId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).otherV().values(USER_ID).dedup();
        while (attchedUsersTraversal.hasNext()) {
            attachedUsers.add(attchedUsersTraversal.next().toString());
        }
        return attachedUsers;
    }
    /**
     * This method is used for datafix of networking lounge connection
     */
    public void createNetworkingLoungeConnection(String eventId){
        List<String> networkingLounges = getNetworkingLoungesOfEvent(eventId);
        for(String loungeId: networkingLounges){
            joinNetworkingLoungesWithMultiple(joinedAttendeeWithLounge(loungeId),eventId,loungeId);
        }
    }

    /**
     *This method is used for bulk query execution of join networking lounge connection
     */
    public void joinNetworkingLoungesWithMultiple(List<String> userId, String eventId, String loungeId) {
//        GraphTraversal traversal = g.inject(0);
//        int count = 0;
        for(int i = 0; i < userId.size();i++) {
            if (g.V().hasId(RONeptuneAttendeeDetailService.attendeeId(userId.get(i))).hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasNext()) {
                log.info("user {} existing at the time of create networking lounge", userId.get(i));
                if (!g.V(loungeId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).filter(otherV().hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasId(RONeptuneAttendeeDetailService.attendeeId(userId.get(i)))).hasNext()) {
                    g.V(loungeId).addE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).to(V(RONeptuneAttendeeDetailService.attendeeId(userId.get(i))).hasLabel(NeptuneDBService.ATTENDEE_LABEL)).next();
                    log.info("User {} joined networking lounge", userId.get(i));
//                    count++;
                }
            }
//            if(count == 100){
//                executeBatchQuery(traversal, loungeId);
//                traversal = g.inject(0);
//                count = 0;
//            }
//        }
//        if(count!=0) {
//            executeBatchQuery(traversal, eventId);
//        }
        }
        log.info("Attndees join the networking lounge {} of event {}",loungeId,eventId);
    }

//    private void executeBatchQuery(GraphTraversal traversal, String loungeId) {
//        try {
//            traversal.next();
//        } catch (Exception e) {
//            log.error("Failed to update networking lounge: {} , Reason: {}", loungeId, Arrays.toString(e.getStackTrace()));
//        }
//    }

    public void joinNetworkingLounge(String userId, String eventId, String loungeId) {
        String attendeeId = RONeptuneAttendeeDetailService.attendeeId(userId);
        if(!g.V().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).hasId(loungeId).hasNext()){
            log.info("NeptuneNetworkingLoungeService | join the networking lounge: {} of event: {}", loungeId, eventId);
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_DELETED_BY_THE_ADMIN);
        }else {
            if (g.V().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).hasId(loungeId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).otherV().hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasId(attendeeId).has(EVENT_ID, eventId).hasNext()) {
                throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_ALREADY_CONNECTED);
            } else if (g.V().hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasId(attendeeId).hasNext()) {
                g.V(loungeId)
                        .addE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION)
                        .to(V(attendeeId).hasLabel(NeptuneDBService.ATTENDEE_LABEL)).next();
                log.info("Attnedee: {} join the networking lounge: {} of event: {}", attendeeId, loungeId, eventId);
            } else {
                log.warn("Attendee {} data not found in neptune", attendeeId);
            }
        }
    }

    public List<AttendeeProfileDto> getConnectedAttendees(String loungeId, Long eventId, String... propsArr) {
        List<AttendeeProfileDto> attendeeProfiles = new ArrayList<>();
        GraphTraversal attendees = rog.V(loungeId)
                .has(EVENT_ID,String.valueOf(eventId))
                .bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION)
                .inV()
                .valueMap(propsArr);
        while (attendees.hasNext()) {
            Map<String, List<String>> attendeeProperties = (Map<String, List<String>>) attendees.next();
            if (null != attendeeProperties && !attendeeProperties.isEmpty()) {
                attendeeProfiles.add(new AttendeeProfileDto(attendeeProperties));
            }
        }
        return attendeeProfiles;
    }

    public AttendeePeoplePageDto getConnectedMembers(String loungeId, String eventId,String userId, long from, long to) {
        log.info("Fetch connected attendees of lounge {} and event {}", loungeId,eventId);
        List<AttendeeProfilePeoplePageDetailsDto> attendeeProfiles = new ArrayList<>();
        AttendeePeoplePageDto attendeePeoplePageDto = new AttendeePeoplePageDto();
        Map<String, Object> attendees = rog.V(loungeId).has(EVENT_ID,eventId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).otherV()
                .and(
                not(has(NeptuneAttendeeDetailService.DELETED_ATTENDEE,String.valueOf(true))),
                not(bothE(neptuneDBService.ATTENDING_LABEL).has(NeptuneAttendeeDetailService.BLOCKED,String.valueOf(true)).otherV().hasId(eventId)),
                bothE(neptuneDBService.ATTENDING_LABEL).otherV().hasId(eventId)
                ).dedup()
                .valueMap().fold().as(Constants.ATTENDEES, Constants.COUNT_STRING).select(Constants.ATTENDEES, Constants.COUNT_STRING).by(range(Scope.local, from, to)).by(count(Scope.local)).next();;
        setOtherDataInAttendeeProfileForLounge(attendees, attendeeProfiles, userId, eventId);
        attendeePeoplePageDto.setAttendeeProfilePeoplePageDetailsDtoList(attendeeProfiles);
        attendeePeoplePageDto.setCount(Long.parseLong(attendees.get(Constants.COUNT_STRING).toString()));
        return attendeePeoplePageDto;
    }

    public void setOtherDataInAttendeeProfileForLounge(Map<String, Object> attendees, List<AttendeeProfilePeoplePageDetailsDto> attendeeProfilePeoplePageDetailsDtos, String userId, String eventId) {
        List<String> attendeeIds = new ArrayList<>();
        ArrayList<Map<String,List<String>>> totalAttendees = (ArrayList<Map<String, List<String>>>) attendees.get(Constants.ATTENDEES);
        for (int i = 0; i < totalAttendees.size();i++) {
            Map<String, List<String>> attendeeProperties = totalAttendees.get(i);
            if (!CollectionUtils.isEmpty(attendeeProperties)) {
                AttendeeProfilePeoplePageDetailsDto attendeeProfilePeoplePageDetailsDto = new AttendeeProfilePeoplePageDetailsDto(attendeeProperties);
                attendeeIds.add(RONeptuneAttendeeDetailService.attendeeId(String.valueOf(attendeeProfilePeoplePageDetailsDto.getUserId())));
                attendeeProfilePeoplePageDetailsDtos.add(attendeeProfilePeoplePageDetailsDto);
            }
        }
        Map<String, String> connectionStatusMap = neptuneAttendeeDetailService.connectionStatusMap(userId, eventId, attendeeIds);
        if(connectionStatusMap != null && !connectionStatusMap.isEmpty()) {
            attendeeProfilePeoplePageDetailsDtos.forEach(attendeeProfile->
                    attendeeProfile.setStatus(connectionStatusMap.get(attendeeProfile.getId()))
            );
        }
        neptuneAttendeeDetailService.setRejectedBy(attendeeIds, userId, attendeeProfilePeoplePageDetailsDtos);
        neptuneAttendeeDetailService.setRequest(eventId, attendeeIds, userId, attendeeProfilePeoplePageDetailsDtos);

    }

    public void leaveNetworkingLounge(String userId, String eventId, String loungeId) {
        log.info("leaveNetworkingLounge eventId {}, loungeId {} , userId {}",eventId,loungeId,userId);
        try {
            String attendeeId = RONeptuneAttendeeDetailService.attendeeId(userId);
            g.V().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).hasId(loungeId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).filter(otherV().hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasId(attendeeId)).drop().iterate();
        }catch (Exception ex){
            log.error("leaveNetworkingLounge eventId {}, loungeId {} , userId {} , errorMsg {}",eventId,loungeId,userId,ex.getMessage());
        }
    }


    public List<NetworkingLounge> getAllNetworkingLoungesById(List<String> loungIds) {
        long startTime = System.currentTimeMillis();
        log.info("Starting getAllNetworkingLoungesById with {} lounge IDs", loungIds != null ? loungIds.size() : 0);

        if (loungIds == null || loungIds.isEmpty()) {
            return Collections.emptyList();
        }

        // Process in smaller batches to avoid overwhelming Neptune
        int batchSize = 5;
        List<List<String>> batches = new ArrayList<>();

        // Split the loungeIds into smaller batches
        for (int i = 0; i < loungIds.size(); i += batchSize) {
            batches.add(loungIds.subList(i, Math.min(i + batchSize, loungIds.size())));
        }

        log.info("Split {} lounge IDs into {} batches of size {}", loungIds.size(), batches.size(), batchSize);

        // Process each batch in parallel
        List<NetworkingLounge> networkingLounges = Collections.synchronizedList(new ArrayList<>());

        try {
            // Use parallel streams to process batches concurrently
            batches.parallelStream().forEach(batch -> {
                long batchStartTime = System.currentTimeMillis();
                try {
                   GraphTraversal netLounges = rog.V()
                            .hasId(P.within(batch))
                            .valueMap();

                    while (netLounges.hasNext()) {
                        Map<String, List<String>> networkingLoungeProperties = (Map<String, List<String>>) netLounges.next();
                        if (null != networkingLoungeProperties && !networkingLoungeProperties.isEmpty()) {
                            networkingLounges.add(new NetworkingLounge(networkingLoungeProperties, "Universal Coordinated Time (UTC)"));
                        }
                    }

                    long batchEndTime = System.currentTimeMillis();
                    log.info("Processed batch of {} lounges in {} ms", batch.size(), (batchEndTime - batchStartTime));
                } catch (Exception e) {
                    log.error("Error processing batch of lounges: {}", e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("Error in parallel processing of lounges: {}", e.getMessage(), e);
        }

        long endTime = System.currentTimeMillis();
        log.info("Completed getAllNetworkingLoungesById in {} ms, found {} lounges out of {} requested",
                (endTime - startTime), networkingLounges.size(), loungIds.size());

        return networkingLounges;
    }



    public void removeAttendeeFromNetworkingLounge(String userId, String eventId) {
        String attendeeId = RONeptuneAttendeeDetailService.attendeeId(userId);
        GraphTraversal networkingLounges = rog.V().hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasId(attendeeId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).otherV().has(EVENT_ID,eventId).id();
        while(networkingLounges.hasNext()){
            g.V().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).hasId(networkingLounges.next().toString()).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).filter(otherV().hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasId(attendeeId)).drop().iterate();
        }
        log.info("Attendee: {} remove from networking lounges of event: {}",attendeeId,eventId);
    }

    public Long getAttendeeCountByLoungeIdAndEventId(String loungeId, String eventId) {
        long attendeeCount = 0;
        GraphTraversal networkingLounges = rog.V(loungeId).has(EVENT_ID,eventId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).otherV().dedup().count();
        while (networkingLounges.hasNext()) {
            attendeeCount = (Long)networkingLounges.next();
        }
        return attendeeCount;
    }

    public List<NetworkingLounge> findLoungesByUserIdAndEventId(String userId, Event event) {
        List<NetworkingLounge> networkingLounges = new ArrayList<>();
        GraphTraversal netLounges = rog.V(RONeptuneAttendeeDetailService.attendeeId(userId))
                .bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION)
                .otherV()
                .has(EVENT_ID,String.valueOf(event.getEventId()))
                .valueMap();
        while (netLounges.hasNext()) {
            Map<String, List<String>> networkingLoungeProperties = (Map<String, List<String>>) netLounges.next();
            if (null != networkingLoungeProperties && !networkingLoungeProperties.isEmpty()) {
                networkingLounges.add(new NetworkingLounge(networkingLoungeProperties,event.getTimezoneId()));
            }
        }
        log.info("Get all networking lounges of event {} by userId {}",event.getEventId(),userId);
        return networkingLounges;
    }

    public List<Object> getAllLoungeIdByUserId(String userId) {
        return g.V().hasLabel(NeptuneDBService.ATTENDEE_LABEL).hasId(RONeptuneAttendeeDetailService.attendeeId(userId)).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).otherV().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).id().toList();
    }

    public void removeAllAttendeesFromAllLoungesOfEvent( String eventId) {
          log.info("Fetch all networking lounges of event {}",eventId);
          List<String> networkingLoungeIds =  getNetworkingLoungesOfEvent(eventId);
          for(String loungeId : networkingLoungeIds){
                removeAllAttendeesFromLounge(loungeId);
          }
    }

    private void removeAllAttendeesFromLounge(String loungeId) {
        if(rog.V(loungeId).hasNext()){
        g.V().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).hasId(loungeId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).drop().iterate();
       }
        log.info("Remove networking lounges all members from lounge {}",loungeId);
    }

    public void copyEventNetworkingLounges(Event olderEvent, Event newEvent,String userId) {
        List<NetworkingLounge> networkingLoungeList = getEventNetworkingLounges(olderEvent);
        networkingLoungeList.forEach( lounge -> {
            lounge.setPhotos(null);
            addNetworkingLounge(lounge,userId,String.valueOf(newEvent.getEventId()));
        });
    }

    public List<Long> getAllUniqueUserIdsByLoungesIds(List<String> loungesIdsList) {
        log.info("getAllUniqueUserIdsByLoungesIds loungesIdsList {}", loungesIdsList);
        List<Object> userIdsObj = rog.V().hasLabel(NeptuneDBService.NETWORKING_LOUNGE).hasId(within(loungesIdsList)).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).otherV().hasLabel(NeptuneDBService.ATTENDEE_LABEL).values(USER_ID).dedup().toList();
        return userIdsObj.stream()
                .map(obj -> Long.valueOf((String) obj))
                .collect(Collectors.toList());
    }

    public Long fetchJoinedAttendeesCountOfLounge(String neptuneLoungeId){
        log.info("fetchJoinedAttendeesCountOfLounge neptuneLoungeId {}", neptuneLoungeId);
        return rog.V(neptuneLoungeId).bothE(NeptuneDBService.NETWORKING_LOUNGE_CONNECTION).count().next();
    }
}
