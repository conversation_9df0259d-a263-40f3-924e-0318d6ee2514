package com.accelevents.services;

import com.accelevents.billing.chargebee.dto.BillingSummerDto;
import com.accelevents.billing.chargebee.dto.EventCreditDistributionDto;
import com.accelevents.common.dto.OrganizerDto;
import com.accelevents.common.dto.WhiteLabelDto;
import com.accelevents.domain.*;
import com.accelevents.dto.*;
import com.accelevents.dto.whitelabel.WhiteLabelSecuritySettingDto;
import com.accelevents.spreedly.dto.gateways.AuthMode;
import com.accelevents.spreedly.dto.gateways.GatewayRequestDto;
import com.accelevents.ticketing.dto.EntitlementsAvailabilityDTO;

import java.util.List;
import java.util.Set;

public interface WhiteLabelService {

	Set<Event> findEvents(User user);

	void createWhiteLabel(String whiteLabelURL);

	WhiteLabel findWhiteLabel(String whiteLabelURL);

	WhiteLabel findWhiteLabel(User user);

	boolean isWhiteLabelUrlPresent(String whiteLabelURL);

    Event addNewEvent(WhiteLabel whiteLabel, User user);

	void saveSettings(WhiteLabelSuperAdminSettingsDTO whiteLabelSuperAdminSettingsDTO, boolean isSuperAdminUser, User user);

    CaptchaDto getWhiteLabelCaptchaDetail(String whiteLabelURL);

    void updateWhiteLabelCaptchaDetail(String whiteLabelURL, CaptchaDto captchaDto);

    Iterable<WhiteLabel> getAllWhiteLabels();

	WhiteLabelSettingDto getWhiteLabelSetting(String whiteLabelURL, boolean isSuperAdmin);

	List<String> findAllWhiteLabelUrl();

    DataTableResponse getAllWhiteLabelUrls(int page, int size, String searchString, String sortField, String sortDirection);

    WhiteLabelDto getWhiteLabelDtoByWhiteLabel(WhiteLabel whiteLabel);
    WhiteLabelDto getWhiteLabelDetailDto(String whiteLabelURL);
    WhiteLabelDto getWhiteLabelDtoByHostBaseUrl(String hostBaseUrl);
    WhiteLabelDto getWhiteLabelDtoByHostBaseUrlAndEventUrl(String hostBaseUrl, String eventUrl);
    WhiteLabelDto getWhiteLabelDetailDataDtoByOrganizerURL(String hostBaseUrl, String organizerURL);

    List<WhiteLabelURLDto> getWhiteLabelByUserAndRole(User user);

	String connectStripe(User whiteLabelUser, WhiteLabel whiteLabel, String header);

//	void connectToStripeAccountAccount(Stripe stripe, User user, WhiteLabel whiteLabel) throws StripeException;

	void authorizeStripe(String code, User user, Staff staff, Event event, Stripe stripe);

	void disconnectStripe(User whiteLabelUser, WhiteLabel whiteLabel);

	Event createConditionalLogicRecordAndAttachUser(User user, WhiteLabel whiteLabel, Event createdEvent, List<String> roles);

    String getFavDirectoryName(String whiteLabelUrl);

	Event createNewWLEvent(String whiteLabelURL, User user, String organizerPageUrl, boolean syncIntegration);

    DataTableResponse getWhiteLabelEventDetails(WhiteLabel whiteLabel, Boolean isPastEvent,PageSizeSearchObj pageSizeSearchObj);

    void sendNotificationEmailForCreateDuplicateEvent(WhiteLabel whiteLabel, Event oldEvent, Event duplicateEvent, User user);

    WhiteLabel getWhiteLabelByIdOrNotFound(Long whiteLabelId);

	void updateEventDesignDetailsUsingWhiteLabelDetails(Event event, WhiteLabel whiteLabel, User user);

    WhiteLabel getWhiteLabelDetailsByHostBaseUrlOrThrowError(String hostBaseUrl);

    void addBaseRecordForWhiteLabelInTransactionTableByUrl(WhiteLabel whiteLabel);

    OrganizerDto createWLOrganizer(String organizerName, WhiteLabel whiteLabel, OrganizerDto organizerDto, User whiteLabelUser, Boolean needToMapIntegration);

    BillingSummerDto getBillingSummeryForWhiteLabel(WhiteLabel whiteLabel);

    List<WLBasicDetailsDto> getWhiteLabelBasicDetails();

    void updateWhiteLabelBillingSettings(User user, WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto,boolean isChargeBeeConfigAdminRole,User superAdminUser);

    WhiteLabelBillingSettingsDto getWhiteLabelBillingSettings(String whiteLabelURL,boolean isChargebeeConfigAdminRole,User superAdminUser);

    WhiteLabel findByWhiteLabelURLThrowException(String whiteLabelURL);

    List<EventCreditDistributionDto> whitelabelCreditDistributionBasedOnEventDays(WhiteLabel whiteLabel);
    AccessTokenModel generateAccessTokenModelForApiUser(WhiteLabel whiteLabel);

    String retrieveApiKeyIfExistForWhiteLabel(long whiteLabelId);

    String retrieveApiKeyAndInsertAuditLog(long whitelabelId, User user);

    ApiKeyDto getApiKeyAuditDetails(long whiteLabelId);

    String generateAccessKey(WhiteLabel whiteLabel, String accessToken, Long userId, User whitelabelUser);

    String rotateApiKeyForWhiteLabel(WhiteLabel whiteLabel, User user);

    void deactivateApiKeyForWhiteLabel(long whiteLabelId, User user);

    void attachStripeToPayFlowWL(String whiteLabelURL, Event event, User user);

    List<Long> getWhiteLabelIdsByHostBaseUrlOrThrowError(String hostBaseUrl);

    void updateWLLogoImageExtension(String whiteLabelUrl, String logoType, String imgExtension);

    void sendMailForNewWLEvent(String eventURL,String whiteLabelURL,User user);

    void markAsBillingContactForWL(Long teamId, boolean mark, User whitelabelUser);

    WhiteLabelSecuritySettingDto getWhiteLabelSecuritySettingDto(String whiteLabelUrl);
    void updateWhiteLabelSecuritySettingDto(String whiteLabelUrl, WhiteLabelSecuritySettingDto whiteLabelSecuritySettingDto);

    EntitlementsAvailabilityDTO getEntitlementsBasedOnWhiteLabelUrl(WhiteLabel whiteLabel);

    List<WhiteLabel> findAllWhiteLabelWhoHaveLatestPlan();

    void saveAll(List<WhiteLabel> whiteLabelListToSave);

    String getWhiteLabelUrlByHostBaseUrl(String hostBaseUrl);

    void updateSalesforceLoginFlag(String whiteLabelURL,boolean isEnable);
    List<AuthMode> getPaymentGatewayAuthModeFieldForWL(String paymentGateway);
    void connectSpreedlyPaymentGatewayForWL(WhiteLabel whiteLabel, User user, GatewayRequestDto gatewayRequestDto);

    void updateEventClosingFormToggle(String whiteLabelURL, boolean isEnabled,User user);
}
