package com.accelevents.services.zapier.impl;

import com.accelevents.common.dto.UploadSessionDto;
import com.accelevents.common.dto.UploadSessionResponseContainer;
import com.accelevents.domain.Event;
import com.accelevents.domain.Integration;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EnumSessionStatus;
import com.accelevents.domain.enums.SessionTypeFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.IntegrationService;
import com.accelevents.services.TicketingTypeService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.zapier.ZapierSessionSpeakerService;
import com.accelevents.services.zapier.ZapierSessionUploadService;
import com.accelevents.services.zapier.dto.ZapierUploadSessionResponseDto;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.accelevents.domain.enums.EnumSessionFormat.BREAKOUT_SESSION;
import static com.accelevents.domain.enums.EnumSessionFormat.MAIN_STAGE;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.convertLongListToCommaSeparated;
import static com.accelevents.utils.ZapierJsonUtils.getValueLongFromJSONOrNull;
import static com.accelevents.utils.ZapierJsonUtils.getValueStringFromJSONOrEmptyString;

@Service
public class ZapierSessionUploadServiceImpl implements ZapierSessionUploadService {

    private static final Logger log = LoggerFactory.getLogger(ZapierSessionUploadServiceImpl.class);

    private IntegrationService integrationService;
    private ROEventService roEventService;
    private SessionService sessionService;
    private TicketingTypeService ticketingTypeService;
    private ZapierSessionSpeakerService zapierSessionSpeakerService;

    @Autowired
    public ZapierSessionUploadServiceImpl(IntegrationService integrationService,
                                          ROEventService roEventService,
                                          SessionService sessionService,
                                          TicketingTypeService ticketingTypeService,
                                          ZapierSessionSpeakerService zapierSessionSpeakerService) {
        this.integrationService = integrationService;
        this.roEventService = roEventService;
        this.sessionService = sessionService;
        this.ticketingTypeService = ticketingTypeService;
        this.zapierSessionSpeakerService = zapierSessionSpeakerService;
    }

    private String getValueStringFromJSONArrayOrEmptyString(JSONObject jsonObject, String key){
        if(jsonObject.has(key)){
            try {
                JSONArray jsonArray = jsonObject.getJSONArray(key);
                if(jsonArray.length() > 0){
                    List<String> list = new ArrayList<>();
                    for (int i = 0; i < jsonArray.length(); i++) {
                        list.add(jsonArray.getString(i)); // Assuming the array contains strings
                    }

                    // Convert List to comma-separated String
                    return String.join(COMMA_SPACE, list);

                }else {
                    return "";
                }
            } catch (JSONException e) {
                log.info("Zapier Error during GetString from Json for Key {}", key);
                return Constants.STRING_EMPTY;
            }
        }else {
            return Constants.STRING_EMPTY;
        }
    }

    private List<String> getValueStringListFromJSONArrayOrEmptyList(JSONObject jsonObject, String key){
        if(jsonObject.has(key)){
            try {
                JSONArray jsonArray = jsonObject.getJSONArray(key);
                if(jsonArray.length() > 0){
                    List<String> list = new ArrayList<>();
                    for (int i = 0; i < jsonArray.length(); i++) {
                        list.add(jsonArray.getString(i)); // Assuming the array contains strings
                    }

                    return list;

                }else {
                    return new ArrayList<>();
                }
            } catch (JSONException e) {
                log.info("Zapier Error during GetString from Json for Key {}", key);
                return new ArrayList<>();
            }
        }else {
            return new ArrayList<>();
        }
    }

    private Integer getValueIntegerFromJSONOrZero(JSONObject jsonObject, String key){
        if(jsonObject.has(key)){
            try {
                return jsonObject.getInt(key);
            } catch (JSONException e) {
                log.info("Zapier Error during GetInt from Json for Key {}", key);
                return 0;
            }
        }else {
            return 0;
        }
    }

    private Boolean getValueBooleanFromJSONOrFalse(JSONObject jsonObject, String key){
        if(jsonObject.has(key)){
            try {
                return jsonObject.getBoolean(key);
            } catch (JSONException e) {
                log.info("Zapier Error during GetBoolean from Json for Key {}", key);
                return false;
            }
        }else {
            return false;
        }
    }

    @Override
    public ZapierUploadSessionResponseDto processSessionUpload(String zapierSessionUploadBody, String zapApiKey){
        JSONObject zapierSessionUploadJson;
        try {
            zapierSessionUploadJson = new JSONObject(zapierSessionUploadBody);
        } catch (JSONException e) {
            log.error("Error during zapier session upload JSON parsing, Error {}", e.getMessage());
            throw new RuntimeException(e);
        }

        ZapierUploadSessionResponseDto sessionUploadResponseDto = new ZapierUploadSessionResponseDto(Constants.FAIL, "Session upload failed");
        Optional<Integration> integrationOptional = integrationService.findEnabledZapierByApiKey(zapApiKey);
        if (integrationOptional.isPresent()){
            Integration integration = integrationOptional.get();
            Optional<Event> optionalEvent = roEventService.findOptionalEventById(integration.getIntegrationSourceId());
            optionalEvent.ifPresent(event -> {
                String sessionFormat = getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Constants.Zapier.SESSION_FORMAT_TYPE);
                if(!isValidFormat(sessionFormat)){
                    sessionUploadResponseDto.setRec_message("Not a valid session format");
                    return;
                }

                Long sessionId = getValueLongFromJSONOrNull(zapierSessionUploadJson, Zapier.SESSION_ID);

                if(null != sessionId && sessionId > 0L) {
                    Session session = sessionService.getSessionById(sessionId, event);
                    if(null == session){
                        sessionUploadResponseDto.setRec_message("No session found for the given session id.");
                    } else {
                        //Update existing session
                        Boolean isSessionVisible = this.getValueBooleanFromJSONOrFalse(zapierSessionUploadJson, Zapier.IS_VISIBLE);
                        if(isSessionVisible){
                            session.setStatus(EnumSessionStatus.VISIBLE);
                        }else {
                            session.setStatus(EnumSessionStatus.HIDDEN);
                        }
                        sessionService.updateSessionThroughZapier(this.createUploadSessionDtoFromJsonObj(zapierSessionUploadJson, event), event, session);

                        sessionUploadResponseDto.setRec_type(Constants.SUCCESS);
                        sessionUploadResponseDto.setRec_message("Session updated successfully");
                        this.afterSessionCreateOrUpdateTasks(event, session, zapierSessionUploadJson, sessionUploadResponseDto);
                    }
                } else {
                    UploadSessionDto uploadSessionDto = this.createUploadSessionDtoFromJsonObj(zapierSessionUploadJson, event);
                    // Upload CSV to create session
                    UploadSessionResponseContainer uploadSessionResponseContainer = sessionService.uploadSessionCSVOrZapier(new UploadSessionDto[]{uploadSessionDto}, event,false,false, null);
                    if(!uploadSessionResponseContainer.getInvalidSession().isEmpty()){
                        sessionUploadResponseDto.setRec_message(uploadSessionResponseContainer.getInvalidSession().get(0).errorMessage);
                        return;
                    }
                    UploadSessionDto validSession = uploadSessionResponseContainer.getValidSession().get(0);
                   //Post Session update after create session from CSV infra
                    Session session = sessionService.getSessionById(validSession.getSessionId(), event);
                    this.updateSessionVisibility(zapierSessionUploadJson, session);
                    sessionUploadResponseDto.setRec_type(Constants.SUCCESS);
                    sessionUploadResponseDto.setRec_message("Session created successfully");
                    this.afterSessionCreateOrUpdateTasks(event, session, zapierSessionUploadJson, sessionUploadResponseDto);
                }

            });
        }
        return sessionUploadResponseDto;
    }

    private void afterSessionCreateOrUpdateTasks(Event event, Session session, JSONObject zapierSessionUploadJson, ZapierUploadSessionResponseDto sessionUploadResponseDto) {
        this.updateTicketingTypeThatCanBeRegisteredForSession(session, event.getEventId(), zapierSessionUploadJson);
        List<String> speakerEmails = this.getValueStringListFromJSONArrayOrEmptyList(zapierSessionUploadJson, Zapier.SPEAKER_EMAILS);
        zapierSessionSpeakerService.updateSessionSpeakerThroughZapier(event, session, speakerEmails);
        this.fillZapierUploadSessionResponseDtoUsingSession(sessionUploadResponseDto, session, event.getEquivalentTimeZone());
    }

    private void updateSessionVisibility(JSONObject zapierSessionUploadJson, Session session) {
        Boolean isSessionVisible = this.getValueBooleanFromJSONOrFalse(zapierSessionUploadJson, Zapier.IS_VISIBLE);
        if(isSessionVisible){
            session.setStatus(EnumSessionStatus.VISIBLE);
        }else {
            session.setStatus(EnumSessionStatus.HIDDEN);
        }
        sessionService.save(session);
    }

    private void fillZapierUploadSessionResponseDtoUsingSession(ZapierUploadSessionResponseDto zapierUploadSessionResponseDto, Session session, String equivalentTimezone){
        zapierUploadSessionResponseDto.setSessionId(session.getId());
        zapierUploadSessionResponseDto.setTitle(session.getTitle());
        zapierUploadSessionResponseDto.setFormat(session.getFormat());
        zapierUploadSessionResponseDto.setSessionTypeFormat(session.getSessionTypeFormat());
        zapierUploadSessionResponseDto.setStartDateTime(TimeZoneUtil.getDateInLocal(session.getStartTime(),equivalentTimezone, null));
        zapierUploadSessionResponseDto.setEndDateTime(TimeZoneUtil.getDateInLocal(session.getEndTime(), equivalentTimezone, null));
        zapierUploadSessionResponseDto.setDescription(session.getDescription());
        zapierUploadSessionResponseDto.setCapacity(session.getCapacity());
        zapierUploadSessionResponseDto.setShortDescriptionOfSession(session.getShortDescriptionOfSession());
        if(session.getSessionLocation() != null && StringUtils.isNotEmpty(session.getSessionLocation().getName())){
            zapierUploadSessionResponseDto.setSessionLocation(session.getSessionLocation().getName());
        }
        else{
            zapierUploadSessionResponseDto.setSessionLocation(STRING_EMPTY);
        }

        zapierUploadSessionResponseDto.setVisibilityStatus(session.getStatus().name());
    }

    public UploadSessionDto createUploadSessionDtoFromJsonObj(JSONObject zapierSessionUploadJson, Event event){
        UploadSessionDto uploadSessionDto = new UploadSessionDto();
        uploadSessionDto.setTitle(getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Constants.Zapier.TITLE));
        uploadSessionDto.setSessionTypeFormat(SessionTypeFormat.valueOf(event.getEventFormat().name()));

        String format = getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Constants.Zapier.SESSION_FORMAT_TYPE);
        if (format.equalsIgnoreCase(REGULAR_SESSION_ENUM)){
            format = BREAKOUT_SESSION.name();
        }else if (format.equalsIgnoreCase(MAIN_STAGE_SESSION_ENUM)){
            format = MAIN_STAGE.name();
        }
        uploadSessionDto.setFormat(EnumSessionFormat.valueOf(format));
        uploadSessionDto.setSessionLocation(getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Constants.Zapier.SESSION_LOCATION));

        String sessionStartDateTime = getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Constants.Zapier.START_DATE_TIME);
        String sessionEndDateTime = getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Zapier.END_DATE_TIME);

        uploadSessionDto.setStartDateTime(this.convertDateFormat(sessionStartDateTime, event.getEquivalentTimeZone()));
        uploadSessionDto.setEndDateTime(this.convertDateFormat(sessionEndDateTime, event.getEquivalentTimeZone()));
        uploadSessionDto.setDescription(getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Constants.Zapier.DESCRIPTION));
        uploadSessionDto.setCapacity(this.getValueIntegerFromJSONOrZero(zapierSessionUploadJson, Constants.Zapier.CAPACITY));
        uploadSessionDto.setShortDescriptionOfSession(getValueStringFromJSONOrEmptyString(zapierSessionUploadJson, Constants.Zapier.SHORT_DESC));
        uploadSessionDto.setTags(this.getValueStringFromJSONArrayOrEmptyString(zapierSessionUploadJson, Zapier.TAGS));
        uploadSessionDto.setTracks(this.getValueStringFromJSONArrayOrEmptyString(zapierSessionUploadJson, Constants.Zapier.TRACKS));

        return uploadSessionDto;
    }

    private boolean isValidFormat(String format) {
        return EnumSessionFormat.MAIN_STAGE.name().equals(format) || Constants.MAIN_STAGE_SESSION_ENUM.equals(format) ||
                EnumSessionFormat.MEET_UP.name().equals(format) ||
                EnumSessionFormat.BREAKOUT_SESSION.name().equals(format) || Constants.REGULAR_SESSION_ENUM.equals(format) ||
                EnumSessionFormat.WORKSHOP.name().equals(format) || EnumSessionFormat.EXPO.name().equals(format) ||
                EnumSessionFormat.BREAK.name().equals(format) || EnumSessionFormat.OTHER.name().equals(format);
    }

    private String convertDateFormat(String inputDateTimeStr, String equivalentTimezone) {
        // Parse the input string into an OffsetDateTime
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(inputDateTimeStr);

        return TimeZoneUtil.getDateInLocal(Date.from(offsetDateTime.toInstant()), equivalentTimezone, LOCAL_DATE_FORMAT);

    }

    private void updateTicketingTypeThatCanBeRegisteredForSession(Session session, Long eventId, JSONObject zapierSessionUploadJson){
        List<String> ticketTypeNames = this.getValueStringListFromJSONArrayOrEmptyList(zapierSessionUploadJson, Zapier.TICKET_TYPE_NAMES);
        if(CollectionUtils.isNotEmpty(ticketTypeNames)){
            if(ticketTypeNames.size() == 1 && ticketTypeNames.get(0).equalsIgnoreCase(Zapier.NO_TICKET_TYPES)){
                session.setTicketTypesThatCanBeRegistered(STRING_EMPTY);
                sessionService.save(session);
                return;
            }
            List<Long> ticketTypeIds  = ticketingTypeService.findTicketTypeIdByEventIdAndTicketTypeNames(eventId, ticketTypeNames);
            if(CollectionUtils.isNotEmpty(ticketTypeIds)){
                String ticketTypeIdsStr = convertLongListToCommaSeparated(ticketTypeIds);
                session.setTicketTypesThatCanBeRegistered(ticketTypeIdsStr);
                sessionService.save(session);
            }
        }
    }
}
