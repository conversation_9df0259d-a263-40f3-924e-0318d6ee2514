package com.accelevents.services.zapier.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.dto.zapier.*;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.messages.TicketType;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.services.zapier.ZapierService;
import com.accelevents.utils.*;
import com.accelevents.webhook.data.TriggerEventTopic;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.getEventPath;
import static com.accelevents.dto.TicketHolderHelper.isAttributeFirstnameLastnameOrEmail;
import static com.accelevents.dto.TicketHolderHelper.isAttributeMandatoryForTicketingType;

@Service
public class    ZapierServiceImpl implements ZapierService {

    private static final Logger log = LoggerFactory.getLogger(ZapierServiceImpl.class);

    private ROEventService roEventService;
    private IntegrationService integrationService;
    private TicketingOrderRepoService ticketingOrderRepoService;
    private EventTicketsRepoService eventTicketsRepoService;
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    private WebhookService webhookService;
    private TicketingTypeService ticketingTypeService;
    private TicketingAccessCodeService ticketingAccessCodeService;
    private TicketingOrderManagerService ticketingOrderManagerService;
    private TicketingHelperService ticketingHelperService;
    private TicketingStatisticsService ticketingStatisticsService;
    private StripeService stripeService;
    private AutoLoginService autoLoginService;
    private ServiceHelper serviceHelper;
    private TrackingLinksService trackingLinksService;
    private SalesTaxService salesTaxService;

    @Autowired
    @Lazy
    public ZapierServiceImpl(ROEventService roEventService,
                             IntegrationService integrationService,
                             TicketingOrderRepoService ticketingOrderRepoService,
                             EventTicketsRepoService eventTicketsRepoService,
                             TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService,
                             WebhookService webhookService,
                             TicketingTypeService ticketingTypeService,
                             TicketingAccessCodeService ticketingAccessCodeService,
                             TicketingOrderManagerService ticketingOrderManagerService,
                             TicketingHelperService ticketingHelperService,
                             TicketingStatisticsService ticketingStatisticsService,
                             StripeService stripeService,
                             AutoLoginService autoLoginService,
                             ServiceHelper serviceHelper,
                             TrackingLinksService trackingLinksService,
                             SalesTaxService salesTaxService){
        this.roEventService = roEventService;
        this.integrationService = integrationService;
        this.ticketingOrderRepoService = ticketingOrderRepoService;
        this.eventTicketsRepoService = eventTicketsRepoService;
        this.ticketHolderRequiredAttributesService = ticketHolderRequiredAttributesService;
        this.webhookService = webhookService;
        this.ticketingTypeService = ticketingTypeService;
        this.ticketingAccessCodeService = ticketingAccessCodeService;
        this.ticketingOrderManagerService = ticketingOrderManagerService;
        this.ticketingHelperService = ticketingHelperService;
        this.ticketingStatisticsService = ticketingStatisticsService;
        this.stripeService = stripeService;
        this.autoLoginService = autoLoginService;
        this.serviceHelper = serviceHelper;
        this.trackingLinksService = trackingLinksService;
        this.salesTaxService = salesTaxService;
    }

    @Override
    public List<TicketPurchaseInfoZapDto> getLastTicketPurchasedInfo(String zapApiKey, String zapierEvent) {
        Optional<Integration> integrationOptional = integrationService.findEnabledZapierByApiKey(zapApiKey);
        List<TicketPurchaseInfoZapDto> ticketPurchaseInfoZapDtoList = new ArrayList<>();
        Optional<Event> optionalEvent = Optional.empty();
        if (integrationOptional.isPresent()){
            Integration integration = integrationOptional.get();
            optionalEvent = roEventService.findOptionalEventById(integration.getIntegrationSourceId());
            List<TicketingOrder> ticketingOrders = ticketingOrderRepoService.findLatestPaidTicketingOrdersByEventIdWithLimit(integration.getIntegrationSourceId(), 5);

            for (TicketingOrder ticketingOrder: ticketingOrders){
                List<EventTickets> eventTickets = eventTicketsRepoService.findByTicketingOrder(ticketingOrder);
                TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto = createTicketPurchaseInfoZapDto(ticketingOrder, eventTickets);
                setTicketPurchaseInfoZapDtoStatus(ticketPurchaseInfoZapDto, zapierEvent);
                ticketPurchaseInfoZapDtoList.add(ticketPurchaseInfoZapDto);
            }
        }
        if(ticketPurchaseInfoZapDtoList.isEmpty()){
            ticketPurchaseInfoZapDtoList = getDummyTicketPurchaseInfo(optionalEvent, zapierEvent);
        }
        return ticketPurchaseInfoZapDtoList;
    }

    @Override
    public TicketPurchaseInfoZapDto createTicketPurchaseInfoZapDto(TicketingOrder ticketingOrder, List<EventTickets> eventTicketsList){
        log.info("createTicketPurchaseInfoZapDto ticketingOrder :{} | ",
                ticketingOrder);

        TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto = new TicketPurchaseInfoZapDto();
        ticketPurchaseInfoZapDto.setId(ticketingOrder.getId());
        ticketPurchaseInfoZapDto.setBuyerEmail(ticketingOrder.getBuyerEmail());
        if(null != ticketingOrder.getTrackingLinkId() && ticketingOrder.getTrackingLinkId() > 0){
            TrackingLinks trackingLinks = trackingLinksService.findById(ticketingOrder.getTrackingLinkId());
            if (trackingLinks != null) {
                ticketPurchaseInfoZapDto.setTrackingLink(trackingLinks.getLinkUrl());
            }
        }
        if(ticketingOrder.getCheckoutFrom() != null){
            ticketPurchaseInfoZapDto.setCheckoutFrom(ticketingOrder.getCheckoutFrom().name());
        }

        TicketHolderAttributes ticketHolderAttributes = eventTicketsList.get(0).getTicketHolderAttributesId();

        TicketAttributeValueDto1 ticketAttributeValueDto = JsonMapper.stringToObjectUsingGSON(ticketHolderAttributes.getJsonValue(), TicketAttributeValueDto1.class);
        List<TicketHolderRequiredAttributes> ticketPurchaserEnabledAttributesList =  ticketHolderRequiredAttributesService.getAllAttributesEnabledForTicketPurchaser(ticketingOrder.getEventid());

        ticketPurchaserEnabledAttributesList.sort(Comparator.comparingLong(TicketHolderRequiredAttributes::getId));
        Map<String, String> purchaserAttributesMap = (Map<String, String>) ticketAttributeValueDto.getPurchaser().get(Constants.TICKETING.ATTRIBUTES);
        Map<Long, Map<String, Object>> purchaserNestedAttributesMap = new HashMap<>();
        try{
            List<Map<String, Object>> purchaserNestedAttributeList = (List<Map<String, Object>>) ticketAttributeValueDto.getPurchaser().get(Constants.TICKETING.NESTEDQUESTIONS);

            if(!CollectionUtils.isEmpty(purchaserNestedAttributeList)){
                purchaserNestedAttributesMap = purchaserNestedAttributeList.stream().collect(Collectors.toMap(e -> (long)Double.parseDouble(String.valueOf(e.get("id"))), e -> e, (old1, new1) -> new1));
            }
        }
        catch (Exception exception){
            log.info("No nested attributes found for ticket holder attributes {}", exception.getMessage());
        }

        Map<String, String> purchaserData = new LinkedHashMap<>();
        for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : ticketPurchaserEnabledAttributesList){
            if(ticketHolderRequiredAttributes.getEnabledForTicketPurchaser() && (ticketHolderRequiredAttributes.getAttributeValueType() != AttributeValueType.CONDITIONAL_QUE && (ticketHolderRequiredAttributes.getParentQuestionId() == null || ticketHolderRequiredAttributes.getParentQuestionId() == 0))){
                if(ticketHolderRequiredAttributes.getName().equalsIgnoreCase(Constants.CELL_PHONE)){
                    purchaserData.put(ticketHolderRequiredAttributes.getName(), purchaserAttributesMap.get(Constants.PHONE_NUMBER_KEY));
                }
                else {
                    purchaserData.put(ticketHolderRequiredAttributes.getName(), purchaserAttributesMap.get(ticketHolderRequiredAttributes.getName()));
                }
            }
            else if(ticketHolderRequiredAttributes.getEnabledForTicketPurchaser()){
                try{
                    purchaserData.put(ticketHolderRequiredAttributes.getName(), null); //As conditional question is present pass it through both option questions, default will be null.
                    if (purchaserNestedAttributesMap.get(ticketHolderRequiredAttributes.getId()) != null) {
                        Map<String, Object> conditionalQuestion = purchaserNestedAttributesMap.get(ticketHolderRequiredAttributes.getId());
                        if(!CollectionUtils.isEmpty(conditionalQuestion) && conditionalQuestion.get(Constants.VALUE_SMALL) != null){
                            purchaserData.put(ticketHolderRequiredAttributes.getName(), String.valueOf(conditionalQuestion.get(Constants.VALUE_SMALL)));
                        }
                    }
                }
                catch (Exception exception){
                    log.info("exception raised while parsing nested/conditional question for ticket purchaser {}", exception.getMessage());
                }
            }
        }
        ticketPurchaseInfoZapDto.setAttributesCount(purchaserData.keySet().size());

        ticketPurchaseInfoZapDto.setPurchaser(purchaserData);
        User purchaser = ticketingOrder.getPurchaser();

        if(purchaser != null) {
            ticketPurchaseInfoZapDto.setFirstName(purchaser.getFirstName());
            ticketPurchaseInfoZapDto.setLastName(purchaser.getLastName());
            ticketPurchaseInfoZapDto.setCellNumber(purchaser.getPhoneNumber());

            String countryCode = purchaser.getCountryCode() != null ? purchaser.getCountryCode().toString() : "";
            ticketPurchaseInfoZapDto.setCountryCode(countryCode);
        }

        Long totalEventTickets = eventTicketsList.stream().filter(eventTicket -> TicketType.DONATION  != eventTicket.getTicketingTypeId().getTicketType()).count();
        Double orderPaidAmount = eventTicketsList.stream().mapToDouble(EventTickets::getPaidAmount).sum();
        ticketPurchaseInfoZapDto.setOrderPaidAmount(orderPaidAmount);
        ticketPurchaseInfoZapDto.setOrderDate(DateUtils.getFormattedDateWithPattern(ticketingOrder.getOrderDate(), DATE_TIME_FORMAT_FULL_STANDARD_UTC));

        TicketingCoupon ticketingCoupon = ticketingOrder.getTicketingCoupon();

        List<EventTicketsZapDto> eventTicketsZapDtoList = new ArrayList<>();

        List<TicketHolderRequiredAttributes> ticketHolderEnabledAttributesList =  ticketHolderRequiredAttributesService.getAllAttributesEnabledForTicketHolder(ticketingOrder.getEventid());

        eventTicketsList.forEach(eventTickets -> this.createEventTicketZapDto(totalEventTickets,ticketingOrder,eventTickets, ticketHolderEnabledAttributesList, ticketingCoupon, purchaserData, eventTicketsZapDtoList));
        setDiscount(ticketingOrder, ticketingCoupon, ticketingOrder.getEventid(), ticketPurchaseInfoZapDto);

        ticketPurchaseInfoZapDto.setEventTickets(eventTicketsZapDtoList);

        return  ticketPurchaseInfoZapDto;
    }

    private void createEventTicketZapDto(Long totalEventTickets, TicketingOrder ticketingOrder, EventTickets eventTickets,
                                         List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList,
                                         TicketingCoupon ticketingCoupon, Map<String, String> purchaserData,
                                         List<EventTicketsZapDto> eventTicketsZapDtoList){
        EventTicketsZapDto eventTicketsZapDto = new EventTicketsZapDto();
        eventTicketsZapDto.setTicketId(eventTickets.getId());
        eventTicketsZapDto.setHolderFirstName(eventTickets.getHolderFirstName());
        eventTicketsZapDto.setHolderLastName(eventTickets.getHolderLastName());
        eventTicketsZapDto.setHolderEmail(eventTickets.getHolderEmail());
        eventTicketsZapDto.setHolderCountryCode(eventTickets.getHolderCountryCode());
        eventTicketsZapDto.setHolderCellNumber(eventTickets.getHolderPhoneNumber());
        eventTicketsZapDto.setHolderAddress(eventTickets.getAddress());
        eventTicketsZapDto.setTicketPaidAmount(eventTickets.getPaidAmount());
        eventTicketsZapDto.setRefundAmount(eventTickets.getRefundedAmount());
        eventTicketsZapDto.setTicketType(eventTickets.getTicketingTypeId().getTicketTypeName());
        eventTicketsZapDto.setPaidAmount(eventTickets.getPaidAmount());
        eventTicketsZapDto.setDiscountAmount(eventTickets.getDiscountAmount());
        eventTicketsZapDto.setMagicLink(createMagicLinkWithFullDomainUrl(eventTickets, ticketingOrder.getEventid()));
        eventTicketsZapDto.setCreateDate(DateUtils.getFormattedDateWithPattern(eventTickets.getCreatedAt(), DATE_TIME_FORMAT_FULL_STANDARD_UTC));
        eventTicketsZapDto.setUpdateDate(DateUtils.getFormattedDateWithPattern(eventTickets.getUpdatedAt(), DATE_TIME_FORMAT_FULL_STANDARD_UTC));

        TicketingType ticketType = eventTickets.getTicketingTypeId();

        TicketTypeDto ticketTypeDto = new TicketTypeDto(ticketType.getId(), ticketType.getTicketTypeName(), ticketType.getPrice(), ticketType.getBundleType(), ticketType.getTicketType(), ticketType.getDataType());

        eventTicketsZapDto.setTicketTypeInfo(ticketTypeDto);

        TicketHolderAttributes ticketHolderAttributes  = eventTickets.getTicketHolderAttributesId();

        TicketAttributeValueDto1 ticketAttributeValueDto1 = JsonMapper.stringToObjectUsingGSON(ticketHolderAttributes.getJsonValue(), TicketAttributeValueDto1.class);

        Map<String, String> holderAttributesMap = (Map<String, String>) ticketAttributeValueDto1.getHolder().get(Constants.TICKETING.ATTRIBUTES);

        Map<Long, Map<String, Object>> holderNestedAttributesMap = new HashMap<>();
        try{
            List<Map<String, Object>> holderNestedAttributeList = (List<Map<String, Object>>) ticketAttributeValueDto1.getHolder().get(Constants.TICKETING.NESTEDQUESTIONS);

            if(!CollectionUtils.isEmpty(holderNestedAttributeList)){
                holderNestedAttributesMap = holderNestedAttributeList.stream().collect(Collectors.toMap(e -> (long)Double.parseDouble(String.valueOf(e.get("id"))), e -> e, (old1, new1) -> new1));
            }
        }
        catch (Exception exception){
            log.info("No nested attributes found for ticket holder attributes {}",exception.getMessage());
        }
        Map<String, String> holderData = new LinkedHashMap<>();

        if(MapUtils.isNotEmpty(holderAttributesMap) && ticketType.getDataType() != DataType.ADDON) {
            for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : ticketHolderRequiredAttributesList) {
                if(ticketHolderRequiredAttributes.getEnabledForTicketHolder() && (ticketHolderRequiredAttributes.getAttributeValueType() != AttributeValueType.CONDITIONAL_QUE && (ticketHolderRequiredAttributes.getParentQuestionId() == null || ticketHolderRequiredAttributes.getParentQuestionId() == 0))){
                    if(Constants.CELL_PHONE.equals(ticketHolderRequiredAttributes.getName())){
                        holderData.put(ticketHolderRequiredAttributes.getName(), holderAttributesMap.get(Constants.PHONE_NUMBER_KEY));
                    }else{
                        holderData.put(ticketHolderRequiredAttributes.getName(), holderAttributesMap.get(ticketHolderRequiredAttributes.getName()));
                    }
                }
                else if(ticketHolderRequiredAttributes.getEnabledForTicketHolder()){
                    try{
                        holderData.put(ticketHolderRequiredAttributes.getName(), null); //As conditional question is present pass it through both option questions, default will be null.
                        if (holderNestedAttributesMap.get(ticketHolderRequiredAttributes.getId()) != null) {
                            Map<String, Object> conditionalQuestion = holderNestedAttributesMap.get(ticketHolderRequiredAttributes.getId());
                            if(!CollectionUtils.isEmpty(conditionalQuestion) && conditionalQuestion.get(Constants.VALUE_SMALL) != null){
                                holderData.put(ticketHolderRequiredAttributes.getName(), String.valueOf(conditionalQuestion.get(Constants.VALUE_SMALL)));
                            }
                        }
                    }
                    catch (Exception exception){
                        log.info("exception raised while parsing nested/conditional question for ticket holder {}", exception.getMessage());
                    }
                }
            }
        }
        else if(ticketType.getDataType() == DataType.ADDON){
            holderData.putAll(purchaserData);
        }

        if (null != ticketingCoupon && null != eventTickets.getTicketingTypeId() && isTicketCouponExitsForParticularTicketType(ticketingCoupon,eventTickets.getTicketingTypeId()) && totalEventTickets > 0 && NumberUtils.isNumberGreaterThanZero(eventTickets.getDiscountAmount())) {
            eventTicketsZapDto.setCouponCode(ticketingCoupon.getName());
        }
        eventTicketsZapDto.setAccessCode(getTicketingAccessCodeExitsForParticularTicketType(ticketingOrder.getTicketingAccessCodeId(), eventTickets.getTicketingTypeId(), (totalEventTickets != null) ? totalEventTickets.intValue() : 0));
        eventTicketsZapDto.setBarcodeId(eventTickets.getBarcodeId());
        eventTicketsZapDto.setHolder(holderData);
        eventTicketsZapDto.setAttributesCount(holderData.keySet().size());
        eventTicketsZapDto.setTicketStatus(getEventTicketStatus(eventTickets, ticketingOrder));
        eventTicketsZapDtoList.add(eventTicketsZapDto);
    }

    private String createMagicLinkWithFullDomainUrl(EventTickets eventTickets, Event event) {
        String magicLink = STRING_EMPTY;
        if(eventTickets != null && eventTickets.getHolderUserId() != null){
            String eventLevelMagicLink = autoLoginService.getOrCreateEventLevelMagicLinkUrl(eventTickets.getHolderUserId(), event, null, ZAPIER.toLowerCase());
            if(StringUtils.isNotEmpty(eventLevelMagicLink)){
                String eventUrl = getEventBaseUrl(event);
                if(!EventFormat.IN_PERSON.equals(event.getEventFormat())){
                    eventUrl += "/portal";
                }
                magicLink =  eventUrl.concat(eventLevelMagicLink);
            }
        }
        return magicLink;
    }

    private String getEventBaseUrl(Event event) {
        return serviceHelper.getEventBaseUrl(event).concat(getEventPath()).concat(event.getEventURL());
    }

    public String getEventTicketStatus(EventTickets eventTickets, TicketingOrder ticketingOrder) {

        String status = Constants.PAID;

        if (ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.COMPLIMENTARY)
                && !TicketPaymentStatus.REFUNDED.equals(eventTickets.getTicketPaymentStatus())
                && eventTickets.getRefundedAmount() <= 0) {
            status = Constants.COMPLIMENTARY;
        }
        else if(ticketingOrder.getStatus() == TicketingOrder.TicketingOrderStatus.PARTIAL && !(TicketPaymentStatus.REFUNDED.equals(eventTickets.getTicketPaymentStatus()))){
            status = "PARTIALLY PAID";
        }
        else{
            if (TicketPaymentStatus.PAID.equals(eventTickets.getTicketPaymentStatus())) {
                status = TicketPaymentStatus.PAID.name();
            }
            else if (TicketPaymentStatus.UNPAID.equals(eventTickets.getTicketPaymentStatus())) {
                status = TicketPaymentStatus.UNPAID.name();
            }

            if(TicketStatus.CANCELED.equals(eventTickets.getTicketStatus())){
                status = Constants.CANCELED;
            }else if (TicketPaymentStatus.REFUNDED.equals(eventTickets.getTicketPaymentStatus())) {
                status = Constants.REFUNDED;
            } else if (eventTickets.getRefundedAmount() > 0) {
                status = STATUS_TO_DISPLAY_PARTIALLY_REFUNDED;
            }
        }
        return status;
    }

    private void setDiscount(TicketingOrder ticketingOrder, TicketingCoupon ticketingCoupon, Event event, TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto){
        List<Map<String,Object>> ticketTypeInfo = new ArrayList<>();

        boolean isCardPayment = !(ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.CASH) || ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.PAY_LATER) || ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.UNPAID));

        List<TicketingOrderManager> ticketingOrderManagers = ticketingOrderManagerService
                .getAllByOrderId(ticketingOrder);

        int numberOfTicketTypeExcludingFreeType = ticketingHelperService.getTotalNoOfTicketTypeExcludingFreeType(ticketingOrderManagers);

        TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto = ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(ticketingOrderManagers, ticketingCoupon);

        StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);

        double totalDiscountedAmount = 0.0;

        double totalPayedAmount = 0.0;

        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(ticketingOrder.getEventid().getEventId());

        for (TicketingOrderManager ticketingOrderManager : ticketingOrderManagers) {
            List<TicketPriceDetails> ticketPriceDetails = ticketingHelperService.getTicketPriceDetails(ticketingOrderManager.getNumberOfDiscountedTicket(),
                    ticketingOrderManager,
                    isCardPayment,
                    ticketingCoupon,
                    ticketingOrderManager.getTicketType(),
                    numberOfTicketTypeExcludingFreeType,
                    totalTicketsAndTotalTicketPriceDto.getTotalTickets(), ticketingOrder, stripeDTO, false, false, salesTaxFeeDto,
                    totalTicketsAndTotalTicketPriceDto.getTotalTicketPrice(), 0);

            double discountedAmount = ticketPriceDetails.stream().mapToDouble(TicketPriceDetails::getDiscountedAmount).sum();
            double payedAmount = ticketPriceDetails.stream().mapToDouble(t->t.getPriceWithFee()+t.getDiscountedAmount()).sum();

            totalPayedAmount += payedAmount;
            totalDiscountedAmount += discountedAmount;

            Map<String,Object> ticketTypeMap = new HashMap<>();
            ticketTypeMap.put("ticketTypeId", ticketingOrderManager.getTicketType().getId());
            ticketTypeMap.put("ticketTypeName", ticketingOrderManager.getTicketType().getTicketTypeName());
            ticketTypeMap.put("totalPurchased", ticketingOrderManager.getNumberofticket());
            ticketTypeMap.put("totalPaidAmount", payedAmount);
            ticketTypeMap.put("totalDiscountedAmount", discountedAmount );
            ticketTypeMap.put("dataType", ticketingOrderManager.getTicketType().getDataType());
            if(null != ticketingCoupon && ticketingOrderManager.getTicketType().getDataType() == DataType.ADDON){
                ticketTypeMap.put("discountApplicable", Boolean.FALSE);
            }
            else {
                ticketTypeMap.put("discountApplicable",isTicketCouponExitsForParticularTicketType(ticketingCoupon,ticketingOrderManager.getTicketType()));
            }

            ticketTypeInfo.add(ticketTypeMap);
        }
        ticketPurchaseInfoZapDto.setTotalPaidAmount(totalPayedAmount);
        ticketPurchaseInfoZapDto.setTotalDiscountAmount(totalDiscountedAmount);
        ticketPurchaseInfoZapDto.setPurchasedTicketTypeInfo(ticketTypeInfo);
    }

    protected boolean isTicketCouponExitsForParticularTicketType(TicketingCoupon ticketingCoupon, TicketingType ticketingTypeId) {
        List<String> ticketingCouponEventTypeId;
        if(null != ticketingCoupon && !TicketType.DONATION.equals(ticketingTypeId.getTicketType()) && !TicketType.FREE.equals(ticketingTypeId.getTicketType())){
            ticketingCouponEventTypeId = GeneralUtils.convertCommaSeparatedToList(ticketingCoupon.getEventTicketTypeId());
            return ticketingCouponEventTypeId.contains(String.valueOf(ticketingTypeId.getId()));
        }else{
            return false;
        }
    }

    private String getTicketingAccessCodeExitsForParticularTicketType(Long ticketingAccessCodeId, TicketingType ticketingType, int numberOfTicket) {
        if (NumberUtils.isNumberGreaterThanZero(ticketingAccessCodeId)) {
            Optional<TicketingAccessCode> ticketingAccessCodeOpt = ticketingAccessCodeService.getById(ticketingAccessCodeId);
            if(ticketingAccessCodeOpt.isPresent()){
                return isTicketAccessCodeExitsForParticularTicketType(ticketingAccessCodeOpt.get(), ticketingType) && numberOfTicket > 0 ? ticketingAccessCodeOpt.get().getCode() : STRING_EMPTY;
            }
        }
        return STRING_EMPTY;
    }

    private boolean isTicketAccessCodeExitsForParticularTicketType(TicketingAccessCode ticketingAccessCode, TicketingType ticketingType) {
        List<String> ticketingAccessCodeEventTypeId = GeneralUtils.convertCommaSeparatedToList(ticketingAccessCode.getEventTicketTypeId());
        return ticketingAccessCodeEventTypeId.contains(String.valueOf(ticketingType.getId()));
    }

    private void setTicketPurchaseInfoZapDtoStatus(TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto, String zapierEvent){
        if (zapierEvent.equals(TriggerEventTopic.TICKET_INFO_UPDATE_EVENT.getEventName())) {
            ticketPurchaseInfoZapDto.setStatus(Constants.Zapier.UPDATE);
        } else {
            ticketPurchaseInfoZapDto.setStatus(Constants.Zapier.CREATE);
        }
    }

    private List<TicketPurchaseInfoZapDto> getDummyTicketPurchaseInfo(Optional<Event> optionalEvent, String zapierEvent) {
        List<TicketHolderRequiredAttributes> ticketPurchaserEnabledAttributesList =  new ArrayList<>();

        if(optionalEvent.isPresent()){
            ticketPurchaserEnabledAttributesList =  ticketHolderRequiredAttributesService.getAllAttributesEnabledForTicketPurchaser(optionalEvent.get());
        }


        List<TicketPurchaseInfoZapDto> ticketPurchaseInfoZapDtos = new ArrayList<>();
        TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto2 = new TicketPurchaseInfoZapDto(2L, "<EMAIL>", "Jonathan", "Kazarian",
                "US", 9999999999L, ticketPurchaserEnabledAttributesList);

        ticketPurchaseInfoZapDto2.getPurchaser().put(Constants.STRING_FIRST_SPACE_NAME, "Jonathan");
        ticketPurchaseInfoZapDto2.getPurchaser().put(Constants.STRING_LAST_SPACE_NAME, "Kazarian");

        ticketPurchaseInfoZapDto2.setAttributesCount(3);

        List<EventTicketsZapDto> eventTicketsZapDtoList2 = new ArrayList<>();

        List<TicketHolderRequiredAttributes> ticketHolderEnabledAttributesList =  ticketHolderRequiredAttributesService.getAllAttributesEnabledForTicketPurchaser(optionalEvent.get());

        EventTicketsZapDto etz11 = new EventTicketsZapDto(11L, "<EMAIL>", "Jon1",
                "Kazarian1", null, "US", 9999999999L, 12.0, "type2", ticketHolderEnabledAttributesList);

        etz11.getHolder().put(Constants.STRING_FIRST_SPACE_NAME, "Jon1");
        etz11.getHolder().put(Constants.STRING_LAST_SPACE_NAME, "Kazarian1");

        etz11.setAttributesCount(5);

        eventTicketsZapDtoList2.add(etz11);

        EventTicketsZapDto etz12 = new EventTicketsZapDto(12L, "<EMAIL>", "Nik",
                "Und", null, "US", 9999999999L, 12.0, "type2", ticketHolderEnabledAttributesList);

        etz12.getHolder().put(Constants.STRING_FIRST_SPACE_NAME, "Nik");
        etz12.getHolder().put(Constants.STRING_LAST_SPACE_NAME, "Und");

        etz12.setAttributesCount(5);

        eventTicketsZapDtoList2.add(etz12);
        ticketPurchaseInfoZapDto2.setOrderPaidAmount(24.0);

        ticketPurchaseInfoZapDto2.setEventTickets(eventTicketsZapDtoList2);
        ticketPurchaseInfoZapDto2.setTrackingLink("TestTrackingLink");
        setTicketPurchaseInfoZapDtoStatus(ticketPurchaseInfoZapDto2, zapierEvent);


        ticketPurchaseInfoZapDtos.add(ticketPurchaseInfoZapDto2);

        TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto1 = new TicketPurchaseInfoZapDto(1L, "<EMAIL>", "Nikunj", "Undhad",
                "IN", 8888888888L, ticketPurchaserEnabledAttributesList);

        ticketPurchaseInfoZapDto1.getPurchaser().put(Constants.STRING_FIRST_SPACE_NAME, "Nikunj");
        ticketPurchaseInfoZapDto1.getPurchaser().put(Constants.STRING_CELL_SPACE_PHONE, "8888888888|IN");


        EventTicketsZapDto etz22 = new EventTicketsZapDto(22L, "<EMAIL>", "Jon2",
                "Kazarian2", "MG|pune|SP|MH|US|412307", "US", 9898989898L, 11.11, "type21", ticketHolderEnabledAttributesList);

        etz22.getHolder().put(Constants.STRING_FIRST_SPACE_NAME, "Jon2");
        etz22.getHolder().put(Constants.STRING_LAST_SPACE_NAME, "Kazarian2");

        etz22.setAttributesCount(5);

        List<EventTicketsZapDto> eventTicketsZapDtoList1 = new ArrayList<>();
        eventTicketsZapDtoList1.add(etz22);

        ticketPurchaseInfoZapDto1.setOrderPaidAmount(11.11);
        ticketPurchaseInfoZapDto1.setEventTickets(eventTicketsZapDtoList1);
        setTicketPurchaseInfoZapDtoStatus(ticketPurchaseInfoZapDto1, zapierEvent);

        ticketPurchaseInfoZapDtos.add(ticketPurchaseInfoZapDto1);

        return ticketPurchaseInfoZapDtos;
    }

    @Override
    public List<UserCheckInDetailsZapDto> getLatestOrDummyEventTicketsCheckInDetails(String zapAPIKey){
        Optional<Integration> integrationOptional = integrationService.findEnabledZapierByApiKey(zapAPIKey);
        List<UserCheckInDetailsZapDto> userCheckInDetailsZapDtoList = new ArrayList<>();
        if(integrationOptional.isPresent()){
            Integration integration = integrationOptional.get();
            List<EventTickets> eventTicketsList = eventTicketsRepoService.findLatestCheckedInEventTickets(integration.getIntegrationSourceId());
            List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList;
            if(null != eventTicketsList && !eventTicketsList.isEmpty()){
                ticketHolderRequiredAttributesList =  ticketHolderRequiredAttributesService.getAllAttributesEnabledForTicketHolder(eventTicketsList.get(0).getEvent());
                for (EventTickets eventTickets: eventTicketsList){
                    userCheckInDetailsZapDtoList.add(createUserCheckInZapDto(eventTickets, ticketHolderRequiredAttributesList));
                }
            }
        }

        if(userCheckInDetailsZapDtoList.isEmpty()){
            userCheckInDetailsZapDtoList = dummyUserCheckInDetailsZapDtos();
        }
        return userCheckInDetailsZapDtoList;
    }

    private UserCheckInDetailsZapDto createUserCheckInZapDto(EventTickets eventTickets, List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList){
        User holder = eventTickets.getHolderUserId();
        String checkInType = eventTickets.getCheckInSource() != null ? eventTickets.getCheckInSource().toString() : "";
        String countryCode = holder.getCountryCode() != null ? holder.getCountryCode().toString() : "";
        UserCheckInDetailsZapDto userCheckInDetailsZapDto = new UserCheckInDetailsZapDto(eventTickets.getId(), StringTools.formatDateToUTCPattern(eventTickets.getCheckInDate()),
                holder.getEmail(), holder.getFirstName(), holder.getLastName(), countryCode, holder.getPhoneNumber(), checkInType, eventTickets.getTicketStatus().getStatus());

        TicketHolderAttributes ticketHolderAttributes  = eventTickets.getTicketHolderAttributesId();

        TicketAttributeValueDto1 ticketAttributeValueDto1 = JsonMapper.stringToObjectUsingGSON(ticketHolderAttributes.getJsonValue(), TicketAttributeValueDto1.class);

        Map<String, String> holderAttributesMap = (Map<String, String>) ticketAttributeValueDto1.getHolder().get(Constants.TICKETING.ATTRIBUTES);

        Map<Long, Map<String, Object>> holderNestedAttributesMap = new HashMap<>();
        try{
            List<Map<String, Object>> holderNestedAttributeList = (List<Map<String, Object>>) ticketAttributeValueDto1.getHolder().get(Constants.TICKETING.NESTEDQUESTIONS);

            if(!CollectionUtils.isEmpty(holderNestedAttributeList)){
                holderNestedAttributesMap = holderNestedAttributeList.stream().collect(Collectors.toMap(e -> (long)Double.parseDouble(String.valueOf(e.get("id"))), e -> e, (old1, new1) -> new1));
            }
        }
        catch (Exception exception){
            log.info("No nested attributes found for ticket holder attributes {}",exception.getMessage());
        }

        Map<String, String> holderData = new LinkedHashMap<>();

        if(MapUtils.isNotEmpty(holderAttributesMap) && eventTickets.getTicketingTypeId().getDataType() != DataType.ADDON) {
            for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : ticketHolderRequiredAttributesList) {
                if(ticketHolderRequiredAttributes.getEnabledForTicketHolder() && (ticketHolderRequiredAttributes.getAttributeValueType() != AttributeValueType.CONDITIONAL_QUE && (ticketHolderRequiredAttributes.getParentQuestionId() == null || ticketHolderRequiredAttributes.getParentQuestionId() == 0))){
                    if(Constants.CELL_PHONE.equals(ticketHolderRequiredAttributes.getName())){
                        holderData.put(ticketHolderRequiredAttributes.getName(), holderAttributesMap.get(Constants.PHONE_NUMBER_KEY));
                    }else{
                        holderData.put(ticketHolderRequiredAttributes.getName(), holderAttributesMap.get(ticketHolderRequiredAttributes.getName()));
                    }
                }
                else if(ticketHolderRequiredAttributes.getEnabledForTicketHolder()){
                    try{
                        holderData.put(ticketHolderRequiredAttributes.getName(), null); //As conditional question is present pass it through both option questions, default will be null.
                        if (holderNestedAttributesMap.get(ticketHolderRequiredAttributes.getId()) != null) {
                            Map<String, Object> conditionalQuestion = holderNestedAttributesMap.get(ticketHolderRequiredAttributes.getId());
                            if(!CollectionUtils.isEmpty(conditionalQuestion) && conditionalQuestion.get(Constants.VALUE_SMALL) != null){
                                holderData.put(ticketHolderRequiredAttributes.getName(), String.valueOf(conditionalQuestion.get(Constants.VALUE_SMALL)));
                            }
                        }
                    }
                    catch (Exception exception){
                        log.info("exception raised while parsing nested/conditional question for ticket holder {}", exception.getMessage());
                    }
                }
            }
        }

        userCheckInDetailsZapDto.setHolderData(holderData);

        return userCheckInDetailsZapDto;
    }

    private List<UserCheckInDetailsZapDto> dummyUserCheckInDetailsZapDtos(){
        List<UserCheckInDetailsZapDto> userCheckInDetailsZapDtoList = new ArrayList<>();
        Map<String, String> holderData = new HashMap<>();
        holderData.put("Custom Question 1", "Custom Answer");
        holderData.put("Twitter", "dummyTwitterUrl");
        holderData.put("Interest", null);

        UserCheckInDetailsZapDto userCheckInDetailsZapDto1 = new UserCheckInDetailsZapDto(2L, "9999-12-31T11:59:59Z", "<EMAIL>", "Jonathan", "Kazarian",
                "US", 9999999999L, "VIRTUAL_EVENT_PORTAL", TicketStatus.CHECKED_IN.getStatus());
        userCheckInDetailsZapDto1.setHolderData(holderData);

        UserCheckInDetailsZapDto userCheckInDetailsZapDto2 = new UserCheckInDetailsZapDto(1L, "9999-12-31T11:59:59Z","<EMAIL>", "Nikunj", "Undhad",
                "IN", 8888888888L, "IN_PERSON", TicketStatus.CHECKED_IN.getStatus());
        userCheckInDetailsZapDto2.setHolderData(holderData);

        userCheckInDetailsZapDtoList.add(userCheckInDetailsZapDto1);
        userCheckInDetailsZapDtoList.add(userCheckInDetailsZapDto2);

        return userCheckInDetailsZapDtoList;
    }

    @Async
    @Override
    public void triggerZapierRestHookOnTicketPurchase(List<EventTickets> eventTicketsList, Event event, TicketingOrder ticketingOrder) {
        try {
            Optional<Integration> optionalIntegration = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getEventId(), IntegrationSourceType.EVENT, IntegrationType.ZAPIER);
            optionalIntegration.ifPresent(integration -> {
                log.info("#Zapier new ticket purchase trigger");
                List<Webhook> webhooks = webhookService.findZapierWebhookByZapierEventNameAndIntegrationId(optionalIntegration.get().getId(), TriggerEventTopic.TICKET_PURCHASE_EVENT.getEventName());
                webhooks.forEach(webhook -> executeTicketPurchaseTrigger(eventTicketsList, ticketingOrder, webhook));
            });
        }catch (Exception ex){
            MDC.put(EVENT_ID, String.valueOf(event.getEventId()));
            MDC.put(TICKET_ORDER_ID, String.valueOf(ticketingOrder.getId()));
            log.error("Zapier TicketPurchase {}", ex.getMessage());
        }
    }

    private void executeTicketPurchaseTrigger(List<EventTickets> eventTicketsList, TicketingOrder ticketingOrder, Webhook webhook) {
        log.info("#Zapier ntp hook found Id: {}", webhook.getTargetUrl());
        try {
            TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto = createTicketPurchaseInfoZapDto(ticketingOrder, eventTicketsList);
            ticketPurchaseInfoZapDto.setStatus(Constants.Zapier.CREATE);

            String json = new ObjectMapper().writeValueAsString(ticketPurchaseInfoZapDto);
            log.info("#ZapData ntp => {}", json);
            HttpResponse<String> httpResponse = Unirest.post(webhook.getTargetUrl())
                    .header(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON)
                    .body(json).asString();
            log.info("#Zap ntp post response => {}", httpResponse.getBody());

        } catch (JsonProcessingException | UnirestException e1) {
            MDC.put(TICKET_ORDER_ID, String.valueOf(ticketingOrder.getId()));
            MDC.put(WEBHOOK_ID, String.valueOf(webhook.getId()));
            log.error("Zapier New Ticket Purchase Error : {}", e1.getMessage());
        }
    }

    @Async
    @Override
    public void triggerZapierRestHookWhenUserCheckIn(EventTickets et){
        try{
            Long orderId = et.getTicketingOrderId();
            Optional<TicketingOrder> ticketingOrderOptional = ticketingOrderRepoService.findById(orderId);
            ticketingOrderOptional.ifPresent(ticketingOrder -> {
                Optional<Integration> optionalIntegration = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(ticketingOrder.getEventid().getEventId(), IntegrationSourceType.EVENT, IntegrationType.ZAPIER);
                optionalIntegration.ifPresent(integration -> {
                    List<Webhook> webhooks  = webhookService.findZapierWebhookByZapierEventNameAndIntegrationId(optionalIntegration.get().getId(), TriggerEventTopic.USER_CHECK_IN.getEventName());
                    webhooks.forEach(webhook -> {
                        log.info("#Zapier UCI hook found Id: {}", webhook.getTargetUrl());
                        Optional<EventTickets> eventTicketsOptional = eventTicketsRepoService.findByEventTicketId(et.getId());
                        eventTicketsOptional.ifPresent(eventTickets -> {
                            try {
                                List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList =  ticketHolderRequiredAttributesService.getAllAttributesEnabledForTicketHolder(eventTickets.getEvent());
                                UserCheckInDetailsZapDto userCheckInDetailsZapDto = createUserCheckInZapDto(eventTickets, ticketHolderRequiredAttributesList);
                                String json = new ObjectMapper().writeValueAsString(userCheckInDetailsZapDto);
                                log.info("#ZapData UCI => {}", json);
                                HttpResponse<String> httpResponse = Unirest.post(webhook.getTargetUrl())
                                        .header(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON)
                                        .body(json).asString();
                                log.info("#Zap UCI post response => {}", httpResponse.getBody());

                            } catch (JsonProcessingException | UnirestException e1) {
                                MDC.put(TICKET_ID, String.valueOf( eventTickets.getId()));
                                log.error("Zapier User Check In Error Id : {}", e1.getMessage());
                            }
                        });

                    });

                });
            });
        }catch (Exception ex){
            MDC.put(EVENT_ID, String.valueOf(et.getEventId()));
            MDC.put(TICKET_ORDER_ID, String.valueOf(et.getTicketingOrderId()));
            MDC.put(TICKET_ID, String.valueOf(et.getId()));
            log.error("Zapier UserCheckIn {}", ex.getMessage());
        }

    }

    @Async  
    @Override
    public void triggerZapierRestHookOnTicketInfoUpdate(List<EventTickets> eventTicketsList, Event event, TicketingOrder ticketingOrder) {
        try {
            Optional<Integration> optionalIntegration = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getEventId(), IntegrationSourceType.EVENT, IntegrationType.ZAPIER);
            optionalIntegration.ifPresent(integration -> {
                List<Webhook> optionalWebhook = webhookService.findZapierWebhookByZapierEventNameAndIntegrationId(optionalIntegration.get().getId(), TriggerEventTopic.TICKET_INFO_UPDATE_EVENT.getEventName());
                optionalWebhook.forEach(webhook -> {
                    try {
                        log.info("#Zapier tiu hook found Id: {}", webhook.getTargetUrl());
                        TicketPurchaseInfoZapDto ticketPurchaseInfoZapDto = createTicketPurchaseInfoZapDto(ticketingOrder, eventTicketsList);
                        ticketPurchaseInfoZapDto.setStatus(Constants.Zapier.UPDATE);

                        String json = new ObjectMapper().writeValueAsString(ticketPurchaseInfoZapDto);
                        log.info("#ZapData tiu => {}", json);
                        HttpResponse<String> httpResponse = Unirest.post(webhook.getTargetUrl())
                                .header(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON)
                                .body(json).asString();
                        log.info("#Zap tiu post response => {}", httpResponse.getBody());

                    } catch (JsonProcessingException | UnirestException e1) {
                        MDC.put(EVENT_ID, String.valueOf(event.getEventId()));
                        MDC.put(TICKET_ORDER_ID, String.valueOf(ticketingOrder.getId()));
                        MDC.put(WEBHOOK_ID, String.valueOf(webhook.getId()));
                        log.error("Zapier Ticket Info Update Error Order : {}", e1.getMessage());
                    }
                });
            });
        }catch (Exception ex){
            MDC.put(EVENT_ID, String.valueOf(event.getEventId()));
            MDC.put(TICKET_ORDER_ID, String.valueOf(ticketingOrder.getId()));
            log.error("Zapier TicketInfoUpdate {}", ex.getMessage());
        }
    }

    @Override
    public List<TicketTypeDto> getTicketTypeInfo(String zapApiKey) {
        Optional<Integration> integrationOptional = integrationService.findEnabledZapierByApiKey(zapApiKey);
        List<TicketTypeDto> ticketTypeDtoList = new ArrayList<>();
        if (integrationOptional.isPresent()){
            Integration integration = integrationOptional.get();
            Optional<Event> optionalEvent = roEventService.findOptionalEventById(integration.getIntegrationSourceId());
            optionalEvent.ifPresent(event -> {
                List<TicketingType> ticketingTypes = ticketingTypeService.getAllByTicketingAndCreatedFromNullOrderByPosition(event);
                ticketingTypes.forEach(ticketingType -> ticketTypeDtoList.add(new TicketTypeDto(ticketingType.getId(), ticketingType.getTicketTypeName())));
            });
        }
        return ticketTypeDtoList;
    }

    @Override
    public List<ImportTicketHolderCustomField> getImportAttendeeOrderFormInfo(String zapApiKey) {
        Optional<Integration> integrationOptional = integrationService.findEnabledZapierByApiKey(zapApiKey);
        List<ImportTicketHolderCustomField> ticketHolderCustomFieldList = new ArrayList<>();

        integrationOptional.ifPresent( integration -> {
            Optional<Event> optionalEvent = roEventService.findOptionalEventById(integration.getIntegrationSourceId());
            optionalEvent.ifPresent(event -> {
                List<Long> ticketTypeIds = ticketingTypeService.findAllIdByEventId(event);
                List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList =  ticketHolderRequiredAttributesService.getAllAttributes(optionalEvent.get());
                for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : ticketHolderRequiredAttributesList){
                    if(ticketHolderRequiredAttributes.getEnabledForTicketPurchaser()){
                        if(ticketHolderRequiredAttributes.getName().equals(Constants.CELL_PHONE)){
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(ticketHolderRequiredAttributes,isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(Constants.Zapier.COUNTRY_CODE_CAMEL_WITH_UNDERSCORE, "Country Code (Two Letter alpha-2) used with phone number", isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                        } else if(ticketHolderRequiredAttributes.getName().equals(Constants.BILLING_ADDRESS) ||
                                    ticketHolderRequiredAttributes.getName().equals(Constants.SHIPPING_ADDRESS)){
                            String addressPrefixKey = ticketHolderRequiredAttributes.getName().replace(Constants.SINGLE_WHITE_SPACE, Constants.STRING_UNDERSCORE);
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(ticketHolderRequiredAttributes,isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(addressPrefixKey+Constants.Zapier.UNDERSCORE_2, ticketHolderRequiredAttributes.getName() + " 2", isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(addressPrefixKey+Constants.Zapier.UNDERSCORE_CITY_CAMEL, ticketHolderRequiredAttributes.getName() + " City", isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(addressPrefixKey+Constants.Zapier.UNDERSCORE_STATE_CAMEL, ticketHolderRequiredAttributes.getName() + " State", isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(addressPrefixKey+Constants.Zapier.UNDERSCORE_COUNTRY_CAMEL, ticketHolderRequiredAttributes.getName() + " Country", isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(addressPrefixKey+Constants.Zapier.UNDERSCORE_POSTAL_CODE_CAMEL, ticketHolderRequiredAttributes.getName() + " Postal Code", isAttributeMandatoryForTicketingType(ticketTypeIds,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                        }else {
                            ticketHolderCustomFieldList.add(new ImportTicketHolderCustomField(ticketHolderRequiredAttributes,isAttributeFirstnameLastnameOrEmail(ticketHolderRequiredAttributes.getName()) || isAttributeMandatoryForTicketingType(ticketTypeIds, ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())));
                        }
                    }
                }
            });
        });
        return ticketHolderCustomFieldList;
    }

}
