package com.accelevents.services.repo.helper;

import com.accelevents.common.dto.SurveysDto;
import com.accelevents.domain.SurveyConfiguration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface SurveyConfigRepoService {
    SurveyConfiguration save(SurveyConfiguration surveyConfiguration);

    List<SurveyConfiguration> saveAll(List<SurveyConfiguration> surveyConfigurations);

    Optional<SurveyConfiguration> findById(long id);

    boolean isSurveyNameIsExistInEvent(String surveyName, long eventId);

    Page<SurveyConfiguration> findByEventIdAndSearchString(long eventId, String searchString, Pageable pageable);

    Page<SurveyConfiguration> findByEventId(long eventId, Pageable pageable);

    boolean isSurveyExist(Long eventId,Long surveyId);

    List<SurveyConfiguration> findSurveysByEventId(long eventId);

    List<SurveysDto> getAllSurveysByEvent(Long eventId);

    boolean isAnySurveyQuizAble(List<Long> surveyIds);

    List<Long> getAllQuizAbleSurveyIds(Long eventId);

    List<SurveyConfiguration> findSurveysByIds(List<Long> surveyIds);

    List<SurveysDto> getSurveysByIds(List<Long> surveyIds);

    public SurveyConfiguration findDefaultSurveyByEventId(long eventId);
}
