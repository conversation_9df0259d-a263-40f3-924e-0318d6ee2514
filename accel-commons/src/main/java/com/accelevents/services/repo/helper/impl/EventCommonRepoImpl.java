package com.accelevents.services.repo.helper.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.TicketTypeSoldAndBookCountDto;
import com.accelevents.dto.stats.TicketStats;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repository.ROEventTicketsCommonRepo;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.ticketing.dto.TicketingBuyerDataFromDB;
import com.accelevents.ticketing.dto.TicketingHomeDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EventCommonRepoImpl implements EventCommonRepoService {

    @Autowired
    private EventTicketsCommonRepo commonRepo;

    @Autowired
    private ROEventTicketsCommonRepo roEventTicketsCommonRepo;


    @Override
    public void save(EventTickets eventTickets) {
        commonRepo.save(eventTickets);
    }


    @Override
    public void saveAll(List<EventTickets> eventTickets) {
        commonRepo.saveAll(eventTickets);
    }

    @Override
    public boolean isTypePurchased(TicketingType ticketingType) {
        return commonRepo.isTicketTypePurchased(ticketingType.getId());
    }

    @Override
    public EventTickets findByOrderWithOneTicket(TicketingOrder ticketingOrder) {
        List<EventTickets> eventTickets = commonRepo.findByTicketingOrder(ticketingOrder, PageRequest.of(0, 1)).getContent();
        if (!eventTickets.isEmpty()) {
            return eventTickets.get(0);
        }
        return null;
    }

    @Override
    public List<EventTickets> findByOrder(TicketingOrder ticketingOrder) {
        List<EventTickets> eventTicketsList = commonRepo.findByTicketingOrder(ticketingOrder, PageRequest.of(0, Integer.MAX_VALUE)).getContent();
        return new ArrayList<>(eventTicketsList);
    }

    @Override
    public List<EventTickets> findByTicketingOrderWithPaidAmountAndNotRefundAmount(List<TicketingOrder> ticketingOrder) {
        if (ticketingOrder.isEmpty()) {
            return Collections.emptyList();
        }
        List<EventTickets> eventTicketsList = commonRepo.findByTicketingOrderWithPaidAmountAndNotRefundAmount(ticketingOrder, PageRequest.of(0, Integer.MAX_VALUE)).getContent();
        return new ArrayList<>(eventTicketsList);
    }

    @Override
    public List<EventTickets> findByOrderAndHolderEmail(TicketingOrder ticketingOrder, String holderEmail) {
        return commonRepo.findByTicketingOrderAndHolderEmail(ticketingOrder, holderEmail);
    }


    @Override
    public List<EventTickets> findByOrder(long ticketingOrderId) {
        return commonRepo.findByTicketingOrder(ticketingOrderId);
    }

    @Override
    public List<EventTickets> findByOrderAndHolderEmailAndNotDeleted(TicketingOrder ticketingOrder, String holderEmail) {
        return commonRepo.findByTicketingOrderAndHolderEmailAndNotDeleted(ticketingOrder, holderEmail);
    }

    @Override
    @Transactional
    public List<EventTickets> getHolderEventTicketsANDNotINRecordStatus(User updatedUser) {
        return commonRepo.getHolderEventTicketsANDNotINRecordStatus(updatedUser.getUserId(), TicketStatus.DELETED);
    }

    @Override
    @Transactional
    public List<EventTickets> getHolderEventTicketsANDNotINRecordStatusAndEventNotDeleted(User updatedUser) {
        return commonRepo.getHolderEventTicketsANDNotINRecordStatusAndEventNotDeleted(updatedUser, TicketStatus.DELETED);
    }


    @Override
    public EventTickets findByIdAndTicketingOrderAndNotInTicketStatus(long eventTicketId, TicketingOrder ticketingOrder, Collection<TicketStatus> ticketStatus) {
        return commonRepo.findByIdAndTicketingOrderAndNotInTicketStatus(eventTicketId, ticketingOrder, ticketStatus);
    }

    @Override
    public Long findByTypeIdAndStatusIdNotIn(TicketingType ticketingType,
                                             Collection<TicketStatus> ticketStatus) {
        return commonRepo.findByTicketingTypeIdAndTicketStatusNotIn(ticketingType, ticketStatus);
    }

    @Override
    public List<EventTickets> findByTicketingOrder(long orderId) {
        return commonRepo.findByTicketingOrder(orderId);
    }

    @Override
    public TicketStats countByTypeIdAndStatusIdNotInAndStatusNot(TicketingType ticketingType, TicketStatus ticketStatus) {

        return commonRepo.countByTicketingTypeIdAndTicketStatusNotInAndStatusNot(ticketingType.getId(),  Arrays.asList(TicketStatus.DELETED, ticketStatus));
    }

    @Override
    public Map<Long, BigDecimal> findSoldCountGroupByType(Long eventId, Long ticketingId) {
        List<Object[]> soldCt = roEventTicketsCommonRepo.getSoldCountByTicketingId(eventId,
                ticketingId,  "CREATE");

        Map<Long, BigDecimal> map = new HashMap<>();

        soldCt.stream().forEach(e -> {
            Long ticketTypeId = ((BigInteger) e[0]).longValue();
            BigDecimal soldCount = (BigDecimal) e[1];
            map.put(ticketTypeId, soldCount);
        });

        return map;
    }

    @Override
    public Map<Long, BigDecimal> findSoldCountGroupByTypeAndTicketTypeId(Long eventId, Long ticketingId, List<Long> ticketTypeIds) {
        return roEventTicketsCommonRepo.getSoldCountByTicketingId(eventId, ticketingId, "CREATE")
                .stream()
                .map(e -> new AbstractMap.SimpleEntry<>(((BigInteger) e[0]).longValue(), (BigDecimal) e[1]))
                .filter(entry -> ticketTypeIds.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public BigDecimal findSoldCountByTicketTypeId(Long ticketTypeId) {
        return commonRepo.getTicketSoldCountByTicketTypeId(ticketTypeId);
    }

    @Override
    public BigDecimal findSoldCountByEvent(Event event) {
        return commonRepo.getTicketSoldCountByEvent(event.getEventId(),  DataType.TICKET);
    }

    @Override
    public BigDecimal findSoldCountByEventId(Long eventId) {
        return commonRepo.getTicketSoldCountByEvent(eventId, DataType.TICKET);
    }

    @Override
    public List<EventTickets> findByOrderAndStatusIdNotIn(TicketingOrder ticketingOrder,
                                                          Collection<TicketStatus> ticketStatus) {
        return commonRepo.findByTicketingOrderAndTicketStatusNotIn(ticketingOrder, ticketStatus);
    }


    @Override
    public Page<EventTickets> findByEventIdAndTypeAndStatus(Event event,
                                                            List<Long> ticketTypeIds, List<TicketStatus> ticketStatus,
                                                            List<TicketPaymentStatus> ticketPaymentStatuses,
                                                            List<TicketType> ticketType,
                                                            Long recurringEventId,
                                                            Pageable pageable) {
        return commonRepo.findByEventidAndTicketTypeAndTicketStatus(event, ticketType, ticketPaymentStatuses, ticketStatus, recurringEventId, ticketTypeIds, DataType.TICKET, pageable);
    }

    @Override
    public Long findAllCheckedInAttendees(Long eventId, Long ticketTypeId, Date startDate, Date endDate) {
        return commonRepo.findallCheckedInAttendee(eventId,ticketTypeId, startDate,endDate);
    }


    @Override
    public Page<EventTickets> findByEventIdAndTypeAndStatusAndSearchStr(Event event,
                                                                        List<TicketType> ticketTypes,
                                                                        List<TicketStatus> ticketStatus,
                                                                        List<TicketPaymentStatus> eventTicketStatus,
                                                                        String searchStr,
                                                                        Long recurringEventId,
                                                                        List<Long> ticketTypeIds,
                                                                        Pageable pageable) {
        return commonRepo.findByEventidAndTicketTypeAndTicketStatusAndSearchStr(
                event, ticketTypes, eventTicketStatus, ticketStatus, searchStr.toLowerCase(), recurringEventId, ticketTypeIds, pageable);

    }


    @Override
    public List<EventTickets> findByTypeAndStatusNotDeleted(TicketingType ticketingType) {
        return commonRepo.findByTicketingTypeAndStatusNotDeleted(ticketingType.getId());
    }

    @Override
    public EventTickets findByBarcodeId(String barcodeId) {
        return commonRepo.findByBarcodeId(barcodeId);
    }

    @Override
    public List<EventTickets> findAllTicketsBarcodeId(String barcodeId) {
        return commonRepo.findAllTicketsBarcodeId(barcodeId);
    }

    @Override
    public List<EventTickets> findByBarcodeIds(List<String> barcodeId) {
        return commonRepo.findByBarcodeIds(barcodeId);
    }

    @Override
    public List<EventTickets> findEventTicketByBarcodeIdsAndEventId(List<String> barcodeIds, Long eventId) {
        return commonRepo.findEventTicketByBarcodeIdsAndEventId(barcodeIds, eventId);
    }

    @Override
    public EventTickets findById(long eventTicketId) {
        return commonRepo.findById(eventTicketId);
    }

    @Override
    public EventTickets findByIdJoinFetch(long eventTicketId) {
        return commonRepo.findByIdJoinFetch(eventTicketId);
    }

    @Override
    public void updatePurchaserByNewUser(User user, User newUser) {
        commonRepo.updatePurchaserByNewUser(user, newUser);
    }

    @Override
    //@Transactional(rollbackFor = { Exception.class })
    public void updateStaffByNewUser(User user, User newUser) {
        commonRepo.updateStaffByNewUser(user, newUser);
    }

    @Override
    public Page<TicketingBuyerDataFromDB> getBuyerDataSearch(long eventId, String searchStr, Long recurringEventId, DataType dataType, Pageable pageable) {
        return this.commonRepo.getTicketingBuyerDataSearch(eventId, pageable,  searchStr, recurringEventId, TicketStatus.DELETED, dataType);
    }

    @Override
    public Page<TicketingBuyerDataFromDB> getTicketBuyerDataSearch(long eventId, String searchStr, Long recurringEventId, DataType dataType, Pageable pageable) {
        Page<Object[]> ticketBuyerListFromDB = commonRepo.getTicketBuyerDataSearch(eventId,  searchStr, recurringEventId, dataType, pageable);
        List<TicketingBuyerDataFromDB> filteredBuyerList = ticketBuyerListFromDB.getContent().stream()
                .map(e ->
                        new TicketingBuyerDataFromDB(((BigInteger) e[0]).longValue(),
                                (Date) e[1],
                                ((Double) e[2]).doubleValue(),
                                ((Double) e[3]).doubleValue(),
                                ((BigInteger) e[4]).longValue(),
                                ((BigDecimal) e[5]).longValue(),
                                ((BigDecimal)e[6]).longValue(),
                                TicketingOrder.TicketingOrderStatus.valueOf(e[7].toString())))
                .collect(Collectors.toList());
        return new PageImpl<>(filteredBuyerList, ticketBuyerListFromDB.getPageable(), ticketBuyerListFromDB.getTotalElements());
    }

    @Override
    public List<EventTickets> findByEventIdJoinFetch(Event event, Long recurringEventId, boolean exitIntentPopupTriggered, DataType dataType, Date startTime, Date endTime) {
        return commonRepo.findByEventidJoinFetch(event, recurringEventId, exitIntentPopupTriggered, dataType, startTime, endTime);
    }


    @Override
    public List<EventTickets> findByEventIdsJoinFetch(List<Event> events, Date startTime, Date endTime, List<TicketingOrder.OrderType> orderTypes) {
        return commonRepo.findByEventidsJoinFetch(events,  startTime, endTime, orderTypes);
    }

    @Override
    public List<EventTickets> findByEventAndSessionIdJoinFetch(Event event, Long sessionId, Long recurringEventId, boolean exitIntentPopupTriggered, DataType dataType) {
        return commonRepo.findByEventAndSessionIdJoinFetch(event, sessionId, recurringEventId, exitIntentPopupTriggered, dataType);
    }

    @Override
    public List<EventTickets> findDeletedEventTicketByEventIdAndTicketingStatusJoinFetch(Event event, Long recurringEventsId) {
        return commonRepo.findDeletedEventTicketByEventIdAndTicketingStatusJoinFetch(event, recurringEventsId);
    }

    @Override
    public List<EventTickets> findByEventIdAndTicketingStatus(Event event, List<TicketingOrder.TicketingOrderStatus> orderStatuses, Long recurringEventsId, DataType dataType, Date startTime, Date endTime) {
        return commonRepo.findByEventIdAndTicketingStatus(event, orderStatuses, recurringEventsId, dataType, startTime, endTime);
    }

    @Override
    public List<EventTickets> findByEventAndTicketStatusAndHolderUserIds(Event event, List<Long> userIds, Long recurringEventsId, DataType dataType) {
        return !CollectionUtils.isEmpty(userIds) ? roEventTicketsCommonRepo.findByEventAndTicketStatusAndHolderUserIds(event.getEventId(), userIds, recurringEventsId, dataType) : Collections.emptyList();
    }

    @Override
    public Long countByStatus(Event event, List<TicketType> ticketTypes, TicketStatus ticketStatus, DataType dataType, List<Long> ticketTypeIds) {
        return commonRepo.countTicketsByTicketStatus(event.getEventId(), ticketTypes, new ArrayList<>(Arrays.asList( TicketPaymentStatus.UNPAID)), ticketStatus, dataType, ticketTypeIds);
    }

    @Override
    public Long countTicketsByTicketStatus(Event event, List<TicketType> ticketTypes, DataType dataType) {
        return commonRepo.countTicketsByTicketStatus(event.getEventId(), ticketTypes, TicketPaymentStatus.UNPAID, dataType);
    }


    @Override
    public List<EventTickets> findByTicketsHaveOnlyBundleTypeTicketByOrderId(TicketingOrder ticketingOrder) {
        return commonRepo.findByTicketsHaveOnlyBundleTypeTicketByOrderId(ticketingOrder);
    }

    @Override
    public List<TicketingHomeDto> getSalesDataForDashboard(long ticketingId, long recurringEventId, double percentage, double fixed, String paymentGetWay, List<String> dataType) {
        List<Object[]> objects = commonRepo.getTicketingSalesDataForDashboard(ticketingId, recurringEventId, percentage, fixed, EnumPaymentGateway.STRIPE.name().equals(paymentGetWay), dataType);
        return objects.stream().map(object -> {
            BigDecimal soldPerTicketType = (BigDecimal) object[0];
            BigInteger ticketingTypeId = (BigInteger) object[1];
            TicketType ticketType = TicketType.valueOf((String) object[2]);
            TicketBundleType ticketBundleType = TicketBundleType.valueOf((String) object[3]);
            Double netSalePerTicketType = (Double) object[4];
            BigInteger numberOfTickets = (BigInteger) object[5];
            String dType = (String) object[6];
            return new TicketingHomeDto(soldPerTicketType.doubleValue(), ticketingTypeId.longValue(), ticketType, ticketBundleType, netSalePerTicketType.doubleValue(), numberOfTickets.longValue(), dType);
        }).collect(Collectors.toList());
    }

    @Override
    public void updateTypeOfEventTicketsOrAddOn(Long orderId, TicketingType newTicketType,
                                                long oldTicketTypeid, long oldRecurringEventId, long newRecurringEventId) {
        commonRepo.updateTicketTypeOfEventTickets(orderId, newTicketType, oldTicketTypeid, oldRecurringEventId, newRecurringEventId);
    }

    @Override
    public List<EventTickets> findAllByOrder(long ticketingOrderId) {
        return commonRepo.findAllByTicketingOrder(ticketingOrderId);
    }

    @Override
    public List<EventTickets> findAllByOrderWithoutRecurring(long ticketingOrderId) {
        return commonRepo.findAllByTicketingOrderWithoutRecurring(ticketingOrderId);
    }

    @Override
    public List<EventTickets> findAllByListOrderIds(List<Long> ticketingOrderIds) {
        return !CollectionUtils.isEmpty(ticketingOrderIds) ? commonRepo.findAllByListOfTicketingOrders(ticketingOrderIds) : Collections.emptyList();
    }

    @Override
    public List<EventTickets> findAllByTicketingOrderIdsAndFormattedDateAfter(List<Long> ticketingOrderIds,Date formattedDate) {
        return !CollectionUtils.isEmpty(ticketingOrderIds) ? commonRepo.findAllByTicketingOrderIdsAndFormattedDateAfter(ticketingOrderIds,formattedDate) : Collections.emptyList();
    }


    @Override
    public List<EventTickets> findTicketRegisterForCheckIn(User holderUser, Event event) {
        return commonRepo.checkNotCheckedInNotRefundedTicketExists1(holderUser.getUserId(), event.getEventId(), Arrays.asList(TicketStatus.CANCELED, TicketStatus.DELETED));
    }

    @Override
    public List<EventTickets> findTicketRegisterForUser(User holderUser, Event event) {
        return commonRepo.findTicketRegisterForUser(holderUser.getUserId(), event.getEventId());
    }

    @Override
    public List<Object[]> findEmailAndHolderIdByTicketingOrder(long ticketingOrderId) {
        return commonRepo.findEmailAndHolderIdByTicketingOrder(ticketingOrderId);
    }

    @Override
    public List<EventTickets> findOtherEventTicketForSameUserANDEvent(Event event, User user, Long eventTicketId) {
        return commonRepo.findOtherEventTicketForSameUserANDEvent(event.getEventId(), user.getUserId(), eventTicketId);
    }

    @Override
    public List<String> findBlockUserByEmail(Event event, List<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptyList();
        }
        // Get all blocked emails for the event
        List<String> blockedEmails = commonRepo.findBlockedEmailsForEvent(event.getEventId(), RecordStatus.BLOCK);
        if (CollectionUtils.isEmpty(blockedEmails)) {
            return Collections.emptyList();
        }
        // Find intersection between provided emails and blocked emails
        return emails.stream()
                .filter(blockedEmails::contains)
                .collect(Collectors.toList());
    }

    @Override
    public List<EventTickets> findAllTicketsByEventIdJoinFetch(Event event) {
        return commonRepo.findAllTicketsByEventIdJoinFetch(event);
    }

    @Override
    public List<EventTickets> findByIdsAndHolderUserId(List<Long> ids, Long holderUserId) {
        return commonRepo.findByIdsAndHolderUserId(ids, holderUserId);
    }

    @Override
    public List<EventTickets> findAllHolderUsersByListOrderIds(List<Long> ticketingOrderIds) {
        return commonRepo.findAllHolderUsersByListOrderIds(ticketingOrderIds);
    }

    @Override
    public List<EventTickets> findByHolderUserIdAndStatusNot(User holderUser, TicketStatus status) {
        return commonRepo.findByHolderUserIdAndStatusNot(holderUser.getUserId(), status);
    }

    @Override
    public List<Object> findUnSuccessTicketsForPayFlowByOrderId(long orderId) {
        return commonRepo.findUnSuccessTicketsForPayFlowByOrderId(orderId);
    }

    @Override
    public void updateEventTicketsForPayFlow(String recStatus, String status,String chargeId, long id) {
        commonRepo.updateEventTicketsForPayFlow(recStatus, status,chargeId ,id);
    }

    @Override
    public List<EventTickets> findByTicketingOrderAndByUserJoinFetch(TicketingOrder ticketingOrder, User user) {
        return commonRepo.findByTicketingOrderAndByUserJoinFetch(ticketingOrder, user);
    }

    @Override
    public Page<User> findAllTicketHolderUserByEvent(Event event, String searchString, Pageable pageable) {
        return commonRepo.findAllTicketHolderUserByEvent(event, searchString, pageable);
    }


    @Override
    public Long findIdByBarcodeIdAndEventId(String barcodeId, Long eventId) {
        return commonRepo.findIdByBarcodeIdAndEventId(barcodeId, eventId);
    }

    @Override
    public List<EventTickets> findByEventIdAndTicketingStatusJoinFetchAndEmails(Event event, List<TicketingOrder.TicketingOrderStatus> orderStatuses, Set<String> emailList) {
        return commonRepo.findByEventIdAndTicketingStatusJoinFetchAndEmails(event, orderStatuses, emailList);
    }

    @Override
    public Set<String> findHolderEmailsByEventIdAndNotRefunded(Long eventId) {
        return commonRepo.findHolderEmailsByEventIdAndNotRefunded(eventId);
    }

    @Override
    public Set<String> findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(Long eventId, Collection<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptySet();
        }

        return commonRepo.findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(eventId, emails);
    }

    @Override
    public Set<String> findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndIdNotInAndTicketStatusNotIn(
            Long eventId,
            Collection<String> emails,
            List<Long> ticketIds) {

        if (CollectionUtils.isEmpty(emails) || CollectionUtils.isEmpty(ticketIds)) {
            return Collections.emptySet();
        }

        return commonRepo.findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndIdNotInAndTicketStatusNotIn(
                eventId,
                emails,
                ticketIds);
    }

    @Override
    public List<EventTickets> findByEventIdWithTicketingOrderStatusNotDeleted(Event event, Long recurringEventId, boolean exitIntentPopupTriggered, DataType dataType, Date startTime, Date endTime) {
        return commonRepo.findByEventIdWithTicketingOrderStatusNotDeleted(event, recurringEventId, exitIntentPopupTriggered, dataType, startTime, endTime);
    }

    @Override
    public List<EventTickets> findByIds(List<Long> ids) {
        return commonRepo.findByIds(ids);
    }

    @Override
    public Long findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(Long eventId)
    {
        return commonRepo.findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(eventId);
    }

    @Override
    public BigDecimal findAddonSoldCountByEvent(Event event) {
        return commonRepo.getTicketSoldCountByEvent(event.getEventId(), DataType.ADDON);
    }

    @Override
    public List<EventTickets> findAllByIdAndHolderUserIdOrPurchaserUserId(List<Long> ticketIds, Long userId) {
        return commonRepo.findAllByIdAndHolderUserIdOrPurchaserUserId(ticketIds,userId);
    }

    @Override
    public List<EventTickets> findAllEventTicketByOrderIds(List<Long> orderIds) {
        return !CollectionUtils.isEmpty(orderIds) ? commonRepo.findAllEventTicketByOrderIds(orderIds) : Collections.emptyList();

    }

    @Override
    public List<EventTickets> findEventTicketsOnlyByEventIdAndHolderUserId(Long eventId, Long holderUserId) {
        return commonRepo.findEventTicketsOnlyByEventIdAndHolderUserId(eventId, holderUserId, Arrays.asList(TicketStatus.CANCELED, TicketStatus.DELETED));
    }

    @Override
    public List<TicketTypeSoldAndBookCountDto> soldTicketCountByTicketingTypeId(List<Long> ticketingTypeOnlyIds, TicketStatus ticketStatus) {
        return commonRepo.soldTicketCountByTicketingTypeId(ticketingTypeOnlyIds, Arrays.asList(TicketStatus.DELETED, ticketStatus));
    }

    @Override
    public Set<Long> findBuyerOrderIdByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(Long eventId, String buyerEmail) {
        return commonRepo.findBuyerOrderIdByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(eventId, buyerEmail);
    }

    @Override
    public Set<String> findBuyerEmailsByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(Long eventId, Collection<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptySet();
        }

        return commonRepo.findBuyerEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(eventId, emails);
    }
}
