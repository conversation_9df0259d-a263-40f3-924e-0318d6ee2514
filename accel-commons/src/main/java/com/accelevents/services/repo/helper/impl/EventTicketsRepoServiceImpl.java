package com.accelevents.services.repo.helper.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.*;
import com.accelevents.domain.TicketingOrder.OrderType;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.AccelFeesDTO;
import com.accelevents.dto.EventTicketsDto;
import com.accelevents.dto.stats.EventStats;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketType;
import com.accelevents.perfomance.dto.TicketTypeReportData;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingOrderRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO;
import com.accelevents.ticketing.dto.TicketingSale;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EventTicketsRepoServiceImpl implements EventTicketsRepoService {

	@Autowired
	private EventTicketsRepository eventTicketsRepository;

	@Autowired
	private EventTicketsCommonRepo eventTicketsCommonRepo;

	@Autowired
	private TicketingOrderRepository ticketingOrderRepository;

	@Autowired
	private TicketingService ticketingService;

	@Autowired
	private TimeZoneService timeZoneService;

	@Autowired
	private TicketingOrderService ticketingOrderService;

	@Autowired
	private RecurringEventsScheduleBRService recurringEventsScheduleService;

	@Autowired
	private TicketingRefundService ticketingRefundService;

    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;

	@Override
	public Page<EventTickets> findByEventid(Event event, Pageable pageable) {
		return eventTicketsRepository.findByEventid(event, pageable,TicketStatus.DELETED);
	}

	@Override
	public TicketingSale getTicketingCardSale(Event event) {
		return eventTicketsRepository.getTicketingSale(event, OrderType.CARD);
	}

	@Override
	public TicketingSale getTicketingCashSale(Event event) {
		return eventTicketsRepository.getTicketingSale(event, OrderType.CASH);
	}


	@Override
	public List<TicketTypeReportData> getAllTicketTypeReports(Event event, Long recurringEventId) {
		return eventTicketsRepository.getAllTicketingReportData(event,TicketType.DONATION, recurringEventId);
	}

    @Override
    public List<TicketTypeReportData> getAllTicketTypeReportsForNonRec(Event event) {
        return eventTicketsRepository.getAllTicketingReportData(event,TicketType.DONATION);
    }

	@Override
	public List<EventTicketsDto> findEventTicketsByEventAndStatus(Event event, Long recurringEventId, User user) {

		List<EventTickets> eventTicketsList = findByEventIdAndTicketingStatusJoinFetch(user,event,
				Arrays.asList(TicketingOrder.TicketingOrderStatus.PAID,
				TicketingOrder.TicketingOrderStatus.UNPAID), recurringEventId);
		List<EventTicketsDto> eventTicketsDtoList = new ArrayList<>();
		for(EventTickets et : eventTicketsList){
			eventTicketsDtoList.add(new EventTicketsDto(et.getId(), et.getPaidAmount(), et.getTicketingOrder().getId(), et.getRefundedAmount(), et.getSeatNumber(), et.getRecurringEventId()));
		}
		return eventTicketsDtoList;
	}

	@Override
	public List<EventTickets> findByEventIdAndTicketingStatusJoinFetch(User user, Event event, List<TicketingOrder.TicketingOrderStatus> asList, Long recurringEventsId) {
		return eventTicketsRepository.findByEventIdAndTicketingStatusJoinFetch(event, asList, user, recurringEventsId);
	}

	@Override
	public Long countTicketsByTicketStatusRecurringEventId(Event event, List<TicketType> ticketTypes, TicketStatus ticketStatus,long recurringEventId) {
		return eventTicketsRepository.countTicketsByTicketStatusRecurringEventId(event,ticketTypes,new ArrayList<>(Arrays.asList(TicketPaymentStatus.UNPAID)), ticketStatus,recurringEventId);
	}

    @Override
    public Long countTicketsByStatusRecurringEventId(Event event, List<TicketType> ticketTypes,long recurringEventId) {
        return eventTicketsRepository.countTicketsByStatusRecurringEventId(event,ticketTypes,TicketPaymentStatus.UNPAID,recurringEventId);
    }



	@Override
	public boolean checkRecurringTicketSold(Long recurringEventId) {
		BigInteger count = eventTicketsRepository.countByRecurringEventIdNotRefundedAndNotDeleted(recurringEventId, Arrays.asList(TicketStatus.CANCELED,TicketStatus.DELETED));
		return count != null && count.intValue() > 0;
	}

	@Override
	public List<Long> getRecurringIdsForScheduleWithSoldTickets(Long scheduleId){
		List<Object> recurringIds = eventTicketsRepository.getRecurringIdsHavingTicketSold(scheduleId);
		return recurringIds.stream().map(e-> ((BigInteger)e).longValue()).collect(Collectors.toList());
	}

	@Override
	public List<Long> findSoldRecurringEventIds(RecurringEvents recurringEvent) {
		return eventTicketsRepository.getSoldRecurringEventIds(recurringEvent, Arrays.asList(TicketStatus.CANCELED,TicketStatus.DELETED));
	}

	@Override
	public BigInteger getRecurringHavingTicketSold(Long recurringEventId){
		return eventTicketsRepository.getRecurringHavingTicketSold(recurringEventId);
	}

    @Override
    public BigInteger getSoldCountByRecurringId(Event event, Long recurringEventId){
        return eventTicketsCommonRepo.getTicketSoldCountByEvent(event.getEventId(), DataType.TICKET, recurringEventId);
    }

	@Override
	public List<RecurringEventsTicketingTypeAndSoldCountDTO> getSoldCountByListOfRecurringList(List<Long> recurringEventIds){
		return CollectionUtils.isEmpty(recurringEventIds) ? Collections.emptyList() :
				eventTicketsRepository.findSoldCountByListOfRecurringList(recurringEventIds, Arrays.asList(TicketStatus.CANCELED,TicketStatus.DELETED));
	}

    @Override
    public List<RecurringEventsTicketingTypeAndSoldCountDTO> getSoldCountByListOfRecurringListExcludingEventStaff(List<Long> recurringEventIds){
        return CollectionUtils.isEmpty(recurringEventIds) ? Collections.emptyList() :
                eventTicketsRepository.findSoldCountByListOfRecurringListExcludingEventStaff(recurringEventIds,Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }


    @Override
    public List<RecurringEventsTicketingTypeAndSoldCountDTO> getSoldCountByForEachRecDateByEventId(Event event) {
        return eventTicketsRepository.findSoldCountByForEachRecDateByEventId(event,Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
	public List<Object[]> getRecurringEventGrossSaleByIds(Set<Long> reccuringEventId) {
		return eventTicketsRepository.getRecurringEventGrossSaleById(reccuringEventId);
	}

	@Override
	public void saveAll(List<EventTickets> eventTicketsToProcess) {
		eventTicketsRepository.saveAll(eventTicketsToProcess);
        if(null!=eventTicketsToProcess.get(0).getEvent()) {
            clearAPIGatewayCache.clearAPIGwTicketTypes(eventTicketsToProcess.get(0).getEvent().getEventURL());
        }
	}

    @Override
    public void save(EventTickets eventTicket) {
        eventTicketsRepository.save(eventTicket);
        if(null!=eventTicket.getEvent()) {
            clearAPIGatewayCache.clearAPIGwTicketTypes(eventTicket.getEvent().getEventURL());
        }
    }

	@Override
	public EventStats countByEventIdAndTicketStatusIdNotInAndStatusNot(Event event, TicketStatus ticketStatus) {

		return eventTicketsRepository.countByEventIdAndTicketStatusIdNotInAndStatusNot(event, Arrays.asList(ticketStatus, TicketStatus.DELETED));
	}

    @Override
    public List<EventStats> countByEventIdAndTicketStatusIdNotInAndStatusNot(List<Long> eventIds, TicketStatus ticketStatus) {

        return eventTicketsRepository.countByEventIdsAndTicketStatusIdNotInAndStatusNot(eventIds, Arrays.asList(ticketStatus, TicketStatus.DELETED));
    }

	@Override
	public List<EventTickets> getByEventIdAndCountryCodeMissingAndPhoneNumberIsPresent(long eventId) {
		return eventTicketsRepository.findByEventIdAndCountryCodeMissingAndPhoneNumberIsPresent(eventId);
	}

	@Override
	public boolean checkEventTicketsSold(Event event) {
		 BigInteger count = eventTicketsRepository.countByEventTicketIdNotRefundedAndNotDeleted(event,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
		return count != null && count.intValue() > 0;
	}

	@Override
	public List<RecurringEventsTicketingTypeAndSoldCountDTO> countNumberOfSoldTicketForRecurringEventsByEventId(long eventId) {
		return eventTicketsRepository.countNumberOfSoldTicketForRecurringEventsByEventId(eventId,  Arrays.asList(TicketStatus.CANCELED,TicketStatus.DELETED));
	}

	@Override
	public List<EventTickets> findAllByTicketingOrderIdAndTicketStatusBooked(long ticketingOrderId, TicketStatus ticketStatus) {
		return eventTicketsRepository.findAllByTicketingOrderIdAndTicketStatusBooked(ticketingOrderId,ticketStatus,TicketPaymentStatus.UNPAID);
	}


	@Override
	public EventTickets findByBarcodeIdAndEventId(String barcodeId, long eventId) {
		return eventTicketsRepository.findByBarcodeIdAndEventId(barcodeId,eventId);
	}

    @Override
    public EventTickets findByRfidTagAndEventId(String rfidTag, long eventId) {
        return eventTicketsRepository.findByRfidTagAndEventId(rfidTag, eventId);
    }

    @Override
    public EventTickets findByRfidTagAndEventIdWithoutJoin(String rfidTag, long eventId) {
        return eventTicketsRepository.findByRfidTagAndEventIdWithoutJoin(rfidTag, eventId);
    }

    @Override
    public EventTickets findByBarcodeIdAndEventIdWithoutJoin(String rfidTag, long eventId) {
        return eventTicketsRepository.findByBarcodeIdAndEventIdWithoutJoin(rfidTag, eventId);
    }

    @Override
    public List<EventTickets> findEventTicketsByTicketingOrderId(Long ticketingOrderId) {
        return eventTicketsRepository.findEventTicketsByTicketingOrderId(ticketingOrderId);
    }

    @Override
	public void updateTicketStatusCheckInStaffAndCheckInDateByEventTickets(List<Long> eventTicketIds, TicketStatus ticketStatus, User checkInStaff, Date date) {
		eventTicketsRepository.updateTicketStatusCheckInStaffAndCheckInDateByEventTickets(eventTicketIds,ticketStatus,checkInStaff,date);
	}

	@Override
	public boolean countSoldTicketForSchedule(Long scheduleId) {
		return eventTicketsRepository.countSoldTicketForSchedule(scheduleId, Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED)).intValue() > 0;
	}

    @Override
    public boolean userAlreadyPurchasedTicketInEvent(Event event, User user) {
        return eventTicketsRepository.userAlreadyPurchasedTicketInEvent(event, user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public Set<String> getAllHoldersEmailByEventAndExcludeGuestOfBuyer(Event event) {
        return eventTicketsRepository.getAllHoldersEmailByEventAndExcludeGuestOfBuyer(event,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

	@Override
	public List<User> checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(Event event, Set<User> holderUsers, TicketStatus status) {
		return eventTicketsRepository.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(event,holderUsers, Arrays.asList( TicketStatus.DELETED,status));
	}

	@Override
	public List<EventTickets> findByTicketingOrder(TicketingOrder ticketingOrder){
		return eventTicketsRepository.findByTicketingOrder(ticketingOrder.getId());
	}

	@Override
	public List<EventTickets> findLatestCheckedInEventTickets(Long eventId){
		Pageable pageable = PageRequest.of(0, 5);
		return eventTicketsRepository.findLatestCheckedInEventTicketsByEventId(eventId, TicketStatus.CHECKED_IN, pageable);
	}

	@Override
	public List<Long> getEventTicketTypeIdsByEventUserANDNotCanceled(Event event, User user) {
		return eventTicketsRepository.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
	}


    @Override
    public List<Long> getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(Event event, User user) {
        return eventTicketsRepository.getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(event, user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<Long> getAllEventTicketTypeIdsByEventUserANDNotCanceled(Event event, User user) {
        return eventTicketsRepository.getAllEventTicketTypeIdsByEventUserANDNotCanceled(event, user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<Long> getAllEventTicketTypeIdsByEventIdUserIdAndNotCanceled(Long eventId, Long userId) {
        return eventTicketsRepository.getAllEventTicketTypeIdsByEventIdUserIdAndNotCanceled(eventId, userId,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<Object[]> getAllEventTicketTypeIdsByEventIdUserIdInAndNotCanceled(Long eventId, List<Long> userIds) {
        return eventTicketsRepository.getAllEventTicketTypeIdsByEventIdUserIdInAndNotCanceled(eventId, userIds,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public EventTickets getEventTicketByEventTicketIdAndNotCanceled(Long eventTicketId) {
        return eventTicketsRepository.getEventTicketByEventTicketIdAndNotCanceled(eventTicketId,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public EventTickets getEventTicketByBarcodeIdAndNotCanceled(String barcodeId) {
        return eventTicketsRepository.getEventTicketByBarcodeIdAndNotCanceled(barcodeId,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public EventTickets getEventTicketByRfIdTagAndNotCanceled(String barcodeId, Long eventId) {
        return eventTicketsRepository.getEventTicketByRfIdTagAndNotCanceled(barcodeId,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED), eventId);
    }

    @Override
    public List<Long> getAllEventTicketTypeIdsWithoutLoungeRestrictedTicketsByEventUserANDNotCanceled(Event event, User user) {
        return eventTicketsRepository.getAllEventTicketTypeIdsWithoutLoungeRestrictedTicketsByEventUserANDNotCanceled(event, user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<Long> getAllEventTicketTypeIdsWithoutExpoRestrictedTicketsByEventUserANDNotCanceled(Event event, User user) {
        return eventTicketsRepository.getAllEventTicketTypeIdsWithoutExpoRestrictedTicketsByEventUserANDNotCanceled(event, user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

	@Override
	public BigDecimal getSumOfAEFeesOfPaidTicketsToBeCharge(Long ticketingId, double feesPerAttendee) {
		return eventTicketsRepository.getSumOfAEFeesOfPaidTicketsToBeCharge(ticketingId, feesPerAttendee);
	}

    @Override
    public BigDecimal getSumOfAEFeesOfPaidTicketsToBeChargeForDistinctUsers(Long ticketingId, double feesPerAttendee) {
        long sumOfAEFeesRemains = 0;
        List<Object[]> aeFeesChargedForUsers = eventTicketsRepository.getAEFeesOfPaidTicketsToBeChargeForDistinctUser(ticketingId);
        for (Object[] obj : aeFeesChargedForUsers) {
            double aeFeesPerUser = Double.parseDouble(obj[1].toString());
            if (aeFeesPerUser < feesPerAttendee) {
                sumOfAEFeesRemains += feesPerAttendee - aeFeesPerUser;
            }
        }
        return BigDecimal.valueOf(sumOfAEFeesRemains);
    }

    @Override
    public List<EventTickets> getEventTicketsByEvent(Event event) {
        return eventTicketsRepository.getEventTicketsByEvent(event);
    }
    @Override
    public BigDecimal getAEFeesOfPaidTicketsToBeCharge( double feesPerAttendee,long eventTicketId) {
        return eventTicketsRepository.getAEFeesOfPaidTicketsToBeCharge( feesPerAttendee,eventTicketId);
    }

    @Override
    public boolean checkUserHadPurchasedVirtualTicketInOrder(long orderid, String email, TicketTypeFormat ticketTypeFormat) {
        return eventTicketsRepository.checkUserHadPurchasedVirtualTicketInOrder(orderid,email,ticketTypeFormat);
    }
    public List<Long>  countSoldTicketForTicketTypeIdAndCouponCOde(List<Long> ticketingTypeId,long ticketCouponId){
        return ticketingTypeId.isEmpty() ? Collections.emptyList() : eventTicketsRepository.countSoldTicketForTicketTypeIdAndCouponCOde(ticketingTypeId,ticketCouponId);

    }

    @Override
    public boolean checkUserHadPurchasedInPersonTicketInEvent(Event event, User user) {
        return eventTicketsRepository.checkUserHadPurchasedInPersonTicketInEvent(event, user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public Long getTicketPurchaserCountByEventAndCreatedAfter(Long eventId, Date createdDate) {
        return eventTicketsRepository.getTicketPurchaserCountByEventAndCreatedAfter(eventId, createdDate, Arrays.asList(StaffRole.admin, StaffRole.whitelabeladmin, StaffRole.eventcoordinator, StaffRole.staff));
    }

    @Override
    public List<EventTickets> findByRecurringEventId(Long recurringEventsId) {
        return eventTicketsRepository.findByRecurringEventId(recurringEventsId);
    }

    @Override
    public List<Long> findAllUserIdByEvent(Event event, List<Long> ticketingTypeIdsIn) {
        if (!CollectionUtils.isEmpty(ticketingTypeIdsIn)) {
            return eventTicketsRepository.findAllUserIdByEvent(event, ticketingTypeIdsIn,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
        }
        return Collections.emptyList();
    }

    @Override
    public List<AccelFeesDTO> findNetAETicketFeeByEvent(Date startDate,Date endDate) {
        return eventTicketsRepository.findNetAETicketFeeEventWise(startDate,endDate);
    }

    @Override
    public Long countDistinctTicketHolderByEvent(Long eventId) {
        return eventTicketsRepository.countDistinctTicketHolderByEvent(eventId).longValue();
    }

    @Override
    public List<Boolean> getUserChatRestrictions(User user, Event event) {
        return eventTicketsRepository.getUserChatRestrictions(user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED), event);
    }

    @Override
    public TicketingOrder findByIdAndPurchaserIdAndTicketingId(long orderId, Long userId, Long eventTicketingId) {
        TicketingOrder ticketingOrder = eventTicketsRepository.findByIdAndPurchaserIdAndTicketingId(orderId, userId, eventTicketingId);
        if (ticketingOrder == null) {
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND);
        }
        return ticketingOrder;
    }

    @Override
    @Deprecated
    //Moved to RO service remove this method
    public List<Object[]> getLastEventRegistrationInfo(List<Long> eventIds, List<Long> userIds) {
	    if(CollectionUtils.isEmpty(eventIds) || CollectionUtils.isEmpty(userIds)) {
	        return Collections.emptyList();
        }
        return eventTicketsRepository.getLastEventRegistrationInfo(eventIds, userIds);
    }

    @Override
    @Deprecated
    //Moved to RO service remove this method
    public List<Object[]> getFirstEventRegistrationInfo(List<Long> eventIds, List<Long> userIds) {
        if(CollectionUtils.isEmpty(eventIds) || CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return eventTicketsRepository.getFirstEventRegistrationInfo(eventIds, userIds);
    }

    @Override
    public Optional<EventTickets> findByEventTicketId(Long eventTicketId) {
        return eventTicketsRepository.findById(eventTicketId);
    }

    @Override
    @Deprecated
    //Moved to RO service remove this method
    public List<Object[]> getTotalPaidAmountByUserIdsAndEventIdsObjects(List<Long> eventIds, List<Long> userIds) {
        if(CollectionUtils.isEmpty(eventIds) || CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
	    return eventTicketsRepository.getTotalPaidAmountByUserIdsAndEventIds(eventIds, userIds);
    }


    @Override
    public List<RecurringEvents> getRecurringEventByIds(List<Long> recurringEventsIds) {
        if (recurringEventsIds != null && !recurringEventsIds.isEmpty()) {
            return eventTicketsRepository.getRecurringEventsByIds(recurringEventsIds);
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public void updateEventTicketLog(Long eventId, String result, long fromCheckInId, Long toCheckInId){
        eventTicketsRepository.updateEventTicketLog(eventId, result, fromCheckInId,toCheckInId);
    }

    @Override
    public Integer getSoldEventTicketAndActiveOrderCountUnionTotalWithoutRecurringEvent(Long eventId){
        return eventTicketsCommonRepo.getNonRefundedSoldEventTicketAndActiveOrderCountUnionTotalWithoutRecurringEvent(eventId, DataType.TICKET.name());
    }

    @Override
    public List<EventTickets> getEventTicketsWithTicketingTableByTicketingOrderId(long id) {
        return eventTicketsRepository.getEventTicketsWithTicketingTableByTicketingOrderId(id);
    }

    @Override
    public List<EventTickets> findAllEventTicketsByHolderUserIdsAndEventId(List<Long> userIds, Long eventId) {
        return eventTicketsRepository.findAllEventTicketsByHolderUserIdsAndEventId(userIds, eventId);
    }

    @Override
    public List<EventTickets> findAllByEventAndUserAndTicketingTypeIdIn(Event event, User user, List<Long> allowedTicketTypeIdForAddon) {
        return eventTicketsRepository.findAllByEventAndUserAndTicketingTypeIdIn(event, user, allowedTicketTypeIdForAddon,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<TicketingType> getAddonTicketingTypeByEventIdAndTicketIdForAddon(Long eventId, Long ticketIdForAddon) {
        return eventTicketsRepository.getAddonTicketingTypeByEventIdAndTicketIdForAddon(eventId, ticketIdForAddon,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<EventTickets> findAddonTicketsByEventIdAndTicketId(Long eventId,Long ticketId) {
        return eventTicketsRepository.findAddonTicketsByEventIdAndTicketId(eventId,ticketId,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<Object[]> getAddonTicketingTypeByEventIdAndTicketIdForAddons(Long eventId, List<Long> ticketIdForAddon) {
        return eventTicketsRepository.getAddonTicketingTypeByEventIdAndTicketIdForAddons(eventId, ticketIdForAddon,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public List<EventTickets> getAllEventTicketsWithTicketingTableByTicketingOrderIds(List <Long> orderIds) {
        return eventTicketsRepository.getAllEventTicketsWithTicketingTableByTicketingOrderIds( orderIds);
    }

    @Override
    public int countDeletedTicketByOrderIdAndTicketTypeId(Long orderId, Long ticketTypeId) {
        Integer deletedTicketCount= eventTicketsRepository.countDeletedTicketByOrderIdAndTicketTypeId(orderId, ticketTypeId);
        if (deletedTicketCount == null) {
            return 0;
        }
        return deletedTicketCount;
    }

    @Override
    public boolean isTicketHolderPresentInAnyEventOfOrgOrWhiteLabel(Integration integration, Long userId){
        if(integration.getSourceType().equals(IntegrationSourceType.ORGANIZER)) {
            return eventTicketsRepository.isTicketHolderPresentInAnyEventOfOrg(integration.getIntegrationSourceId(), userId).intValue() == 1 ;
        }else if(integration.getSourceType().equals(IntegrationSourceType.WHITE_LABEL)){
            return eventTicketsRepository.isTicketHolderPresentInAnyEventOfWhiteLabel(integration.getIntegrationSourceId(), userId).intValue() == 1;
        }else {
            return false;
        }
    }

    @Override
    public boolean checkUserHadPurchasedOnlyInPersonTypeTicketInTheEvent(Long eventId, User user){
        return eventTicketsRepository.checkUserHadPurchasedOnlyInPersonTypeTicketInTheEvent(eventId, user, TicketStatus.CANCELED);
    }

    @Override
    public Optional<User> findPurchaserUserByEventTicketIdAndEventId(Long eventTicketId, Long eventId) {
        return eventTicketsRepository.findPurchaserUserByEventTicketIdAndEventId(eventTicketId, eventId);
    }

    @Override
    public Optional<EventTickets> findByEventTicketIdWithAllFetch(Long eventTicketId) {
        return eventTicketsRepository.findByEventTicketIdWithAllFetch(eventTicketId);
    }

    @Override
    public List<EventTickets> findByIds(List<Long> eventTicketIds) {
        if (CollectionUtils.isEmpty(eventTicketIds)) {
            return Collections.emptyList();
        }
        return eventTicketsRepository.findByIdIn(eventTicketIds);
    }

    @Override
    public Page<EventTickets> findByEventIdOrderByAsc(Event event, Pageable pageable) {
        return eventTicketsRepository.findByEventIdOrderByAsc(event, pageable,TicketStatus.DELETED);
    }

    @Override
    public List<EventTickets> getEventTicketByIdsAndNotCanceled(List<Long> eventTicketIds) {
        return eventTicketsRepository.getEventTicketByIdsAndNotCanceled(eventTicketIds,  Arrays.asList(TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public Set<Long> findUserIdsWithPurchasedTickets(Event event, List<Long> users) {
        return eventTicketsRepository.findUserIdsWithPurchasedTickets(event, users,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }
}
