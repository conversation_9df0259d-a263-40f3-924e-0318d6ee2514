package com.accelevents.services.repo.helper.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.EventNameLookUpDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.PlanConfig;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.AttendeesUploadCharge;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.EventListingStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.enums.BillingType;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.EventRepository;
import com.accelevents.repositories.OrganizerRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.JoinEventWithPDProductService;
import com.accelevents.services.repo.CacheService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.WhiteLabelRepoService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.EventServiceUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.utils.Constants.UTF8;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class EventRepoServiceImpl implements EventRepoService {

    private static final Logger log = LoggerFactory.getLogger(EventRepoServiceImpl.class);

    private EventRepository eventRepository;

    @Autowired
    public EventRepoServiceImpl(EventRepository eventRepository) {
        this.eventRepository = eventRepository;
    }

    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;

    @Autowired
    private JoinEventWithPDProductService joinEventWithPDProductService;

    @Autowired
    private WhiteLabelRepoService whiteLabelRepoService;

    @Autowired
    private OrganizerRepository organizerRepository;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ROEventService roEventService;

    public EventRepoServiceImpl() {

    }

    @Override
    public Event findAllWithDeletedEventsByEventId(Long eventId){
        List<Object[]> eventDto = eventRepository.findAllWithDeletedEventsByEventId(eventId);
        Optional<Organizer> organizer = Optional.empty();
        Optional<WhiteLabel> whiteLabel = Optional.empty();
        Event event = new Event();
        for(Object[] obj : eventDto){
            event.setEventId(((BigInteger)(obj[0])).longValue());
            event.setName(obj[1].toString());
            event.setEventURL(obj[2].toString());
            event.setAutoBilling((Boolean) obj[3]);
            if(null != obj[4])
                organizer = organizerRepository.findById(((BigInteger)(obj[4])).longValue());
            if(null != obj[5])
                whiteLabel = whiteLabelRepoService.findById(((BigInteger)(obj[5])).longValue());
            event.setOrganizer(organizer.isPresent() ? organizer.get() : null);
            event.setWhiteLabel(whiteLabel.isPresent() ? whiteLabel.get() : null);
            event.setEventFormat(EventFormat.valueOf(obj[6].toString()));
        }
        return event;
    }

    @Override
    public EventListingStatus getEventListingStatusByEventId(Long eventId){
        return eventRepository.findEventListingStatusByEventId(eventId);
    }

    @Override
    public Event getEventByURLOrThrowException(String url) {
        try {
            url = URLEncoder.encode(url, UTF8);
        } catch (UnsupportedEncodingException e) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.URL_NOT_SUPPORTED);
        }
        Event event = eventRepository.findEventByEventURL(url);
        if (event != null) {
            return event;
        } else {
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }

    }

    @Override
    public Event findEventByIdOrThrowError(Long eventId){
        return eventRepository.findById(eventId).orElseThrow(()-> new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND));
    }

    @Override
    @Transactional
    public void save(Event event) {
        if(isBlank(event.getOldEventURL())) {
            event.setOldEventURL(event.getEventURL());
        }

        log.info("Event Object save : {}", event.getEventId());
        eventRepository.save(event);
        //need to restore latest eventData in Memcache otherwise in clearAPIGatewayCache take old one becuse it is async
        eventRepository.findEventByEventURL(event.getEventURL());
        // Setting latest event Data in MemCache for value eventById
        roEventService.findEventByEventId(event.getEventId());
        log.info("Temp log for debugging to fetch Event Object : {}",event.getEventId());
        EventServiceUtils.addEventIdToSessionEventIdSet(event.getEventId());
        clearAPIGatewayCache.clearAPIGwEventCache(event.getEventURL());
        clearAPIGatewayCache.clearAPIGwBeeFreePageCacheByEvent(event);
    }

    @Async
    @Override
    @Transactional
    public void postDeleteEventProcess(Event event){
        threeSecondGap();
        log.info("postDeleteEventProcess eventUrl {}", event.getEventURL());
        clearAPIGatewayCache.clearAPIGwEventCache(event.getEventURL());
    }

    @Async
    @Override
    @Transactional
    public void clearCacheEventDetailsProcess(String eventUrl){
        threeSecondGap();
        log.info("clearCacheEventDetailsProcess eventUrl {}", eventUrl);
        // Not able to clear virtual Event Settings cache; Therefore, Clear Events Details cache
        clearAPIGatewayCache.clearAPIGwEventCache(eventUrl);
        log.info("clearCacheEventDetailsProcess eventUrl {}", eventUrl);
    }

    @Async
    @Override
    @Transactional
    public void postDuplicateEventProcess(long eventId) {
        threeSecondGap();
        log.info("postDuplicateEventProcess eventId {}", eventId);
        Event event = findEventByIdOrThrowError(eventId);
        log.info("postDuplicateEventProcess {}", event);
        save(event);

    }

    private void threeSecondGap() {
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            log.warn("DuplicateEventServiceImpl | TwoSecondGap {}",e.getMessage());
        }
    }


    @Override
    public List<Long> findEventIdsOfUserParticipated(Long userId) {
        return eventRepository.findEventIdsOfUserParticipated(userId, Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Override
    public String findOrganizerPageUrlByEventId(Long eventId) {
        return eventRepository.findOrganizerPageUrlByEventId(eventId);
    }

    @Override
    public List<Long> getEventIdsOfUpdatedInDay(Date startDate, Date endDate) {
        List<BigInteger> eventIds = eventRepository.getEventIdsOfUpdatedInDayFromPortal(startDate, endDate);
        eventIds.addAll(eventRepository.getEventIdsOfUpdatedInDayFromHost(startDate, endDate));
        eventIds.addAll(eventRepository.getEventIdsOfUpdatedInDayFromModules(startDate, endDate));
        Set<BigInteger> eventIdsSet = new HashSet<>(eventIds);
        List<Long> longEventIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(eventIdsSet)) {
            longEventIds = eventIds.stream().filter(Objects::nonNull)
                    .map(e -> Long.valueOf(e.longValue())).collect(Collectors.toList());
        }
        return longEventIds;
    }

    @Override
    public List<Event> findActiveEventsByWhiteLabel(List<EventListingStatus> eventListingStatus, WhiteLabel whiteLabel) {
        return eventRepository.findActiveEventsByWhiteLabel(eventListingStatus, whiteLabel);
    }

    @Override
    public List<Event> findOnlineEventsWithNoPaidTicketsByOrganizerId(long organizerId, EventFormat eventFormat) {
        return eventRepository.findOnlineEventsWithNoPaidTicketsByOrganizerId(organizerId, eventFormat, TicketType.PAID);
    }

    @Override
    public List<Event> findOnlineEventsWithNoPaidTicketsByWhitelabelId(long whitelabelId, EventFormat eventFormat) {
        return eventRepository.findOnlineEventsWithNoPaidTicketsByWhiteLabelId(whitelabelId, eventFormat, TicketType.PAID);
    }

    @Override
    public List<Event> findNonActiveEventsByOrganizerURL(String organizerPageURL) {
        List<Event> events = new ArrayList<>();
        List<String> liveEventStatusList = Stream.of(CommonUtil.getLiveEventStatuses().stream().toArray(EventListingStatus[]::new))
                .map(EventListingStatus::name)
                .collect(Collectors.toList());
        List<Object[]> nonActiveEvents = eventRepository.findNonActiveEventsByOrganizerURL(organizerPageURL, liveEventStatusList);
        if (!CollectionUtils.isEmpty(nonActiveEvents)) {
            for (Object[] obj : nonActiveEvents) {
                events.add(eventRepository.findById(Long.parseLong(obj[8].toString())).get());
            }
        }
        return events;
    }

    @Override
    public Organizer findOrganizerByEventId(long eventId) {
        return eventRepository.findOrganizerByEventId(eventId);
    }

    @Override
    public List<Event> getAllByAuctionIds(List<Long> auctionIds) {
        return eventRepository.findAllByAuctionIds(auctionIds);
    }

    @Override
    public List<Object[]> findActiveEventsByOrganizerURL(String organizerPageURL) {
        return eventRepository.findActiveEventsByOrganizerURL(organizerPageURL,false);
    }

    @Override
    public void saveAll(List<Event> events) {
        eventRepository.saveAll(events);
    }

    @Override
    public void saveAllAndClearCache(List<Event> events) {
        eventRepository.saveAll(events);
        events.forEach(event -> {
            if(isBlank(event.getOldEventURL())) {
                event.setOldEventURL(event.getEventURL());
            }
            cacheService.evictSingleCacheValue("eventByUrl", event.getOldEventURL());
            cacheService.evictSingleCacheValue("eventById", String.valueOf(event.getEventId()));
            log.info("Clearing cache for event {} while saving events in batch", event.getEventURL());

            //need to restore latest eventData in Memcache otherwise in clearAPIGatewayCache take old one becuse it is async
            eventRepository.findEventByEventURL(event.getEventURL());
            EventServiceUtils.addEventIdToSessionEventIdSet(event.getEventId());
            clearAPIGatewayCache.clearAPIGwEventCache(event.getEventURL());
            clearAPIGatewayCache.clearAPIGwBeeFreePageCacheByEvent(event);
        });
    }

    @Override
    public List<EventNameLookUpDto> getAllEventsByOrganizerId(Long organizerId) {
        Optional<Organizer> organiserOpt = organizerRepository.findByOrganizerId(organizerId);
        if (organiserOpt.isPresent()) {
            Organizer organiser = organiserOpt.get();
            return eventRepository.getAllEventsByOrganizerId(organiser.getId());
        }
        throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
    }

    @Override
    public BillingType findBillingTypeByEventId(Long eventId) {
        return eventRepository.findBillingTypeByEventId(eventId);
    }

    @Override
    public List<Event> findByEventIdsAndNotOrganizerPlanConfig(List<Long> eventIds, PlanConfig planConfig) {
        return eventRepository.findByEventIdsAndNotOrganizerPlanConfig(eventIds, planConfig);
    }

    @Override
    public List<Object[]> getHubspotOjectIdsByEventIds(List<Long> eventIds) {
        return eventRepository.getHubspotOjectIdsByEventIds(eventIds);
    }

    @Override
    public List<Object[]> getAllEventsByFromAndTo(Long from, Long to) {
        return eventRepository.getAllEventsByFromAndTo(from, to);
    }

    @Override
    public List<Event> getDeletedEventByUpdatedDateBeforeAndEventIdFromAndTo(Long from, Long to, Date updatedDate) {
        return eventRepository.getDeletedEventByUpdatedDateBeforeAndEventIdFromAndTo(from, to, updatedDate);
    }

    @Override
    public List<Event> getActiveEventsByWelcomeVideo(String playbackId) {
        return eventRepository.getActiveEventsByWelcomeVideo(playbackId);
    }

    @Override
    public List<Object[]> getAllEventsByWhiteLabelIdAndCreatedDate(Long whiteLabelId, Date createdDate) {
        return eventRepository.getAllEventsByWhiteLabelIdAndCreatedDate(whiteLabelId, createdDate);
    }

    @Override
    public List<Object[]> getAllEventsByOrganizerIdAndCreatedDate(Long organizerId, Date createdDate) {
        return eventRepository.getAllEventsByOrganizerIdAndCreatedDate(organizerId, createdDate);
    }

    @Override
    public AttendeesUploadCharge getAttendeeUploadCharge(Long eventId) {
        return eventRepository.findAttendeeUploadCharge(eventId);
    }

    @Override
    public List<Event> findAllByIdsAndOrganiserId(List<Long> ids, Long organizerId) {
        return eventRepository.findAllByIdsAndOrganiserId(ids, organizerId);
    }

    @Override
    public List<Event> findAllFutureEventWithActiveModuleAndPhoneNumberFromTo(Long from, Long to, Long phoneNumber) {
        return eventRepository.findAllFutureEventWithActiveModuleAndPhoneNumberFromTo(from, to, phoneNumber);
    }

    @Override
    public Event findFutureEventWithActiveModuleByEventId(Long eventId) {
        return eventRepository.findFutureEventWithActiveModuleByEventId(eventId);
    }
    @Override
    public List<EventNameLookUpDto> getAllEventsByOrganizerIdOrWhiteLabelIdAndSubscriptionId(Long whiLabelId,Long organizerId, String subscriptionId) {
        List<EventNameLookUpDto> eventNameLookUpDtoList = new ArrayList<>();
        List<Object[]> eventsData = new ArrayList<>();
        if (whiLabelId != null) {
            eventsData = eventRepository.getAllEventsByWhiteLabelIdAndSubscriptionId(whiLabelId, StringUtils.trim(subscriptionId));
        } else if (organizerId != null) {
            eventsData = eventRepository.getAllEventsByOrganizerIdAndSubscriptionId(organizerId, StringUtils.trim(subscriptionId));
        }
        eventsData.forEach(eventData -> {
            EventNameLookUpDto eventNameLookUpDto = new EventNameLookUpDto();
            eventNameLookUpDto.setEventId(((BigInteger) eventData[0]).longValue());
            eventNameLookUpDto.setName(eventData[1].toString());
            eventNameLookUpDto.setEventURL(eventData[2].toString());
            eventNameLookUpDtoList.add(eventNameLookUpDto);
        });
        log.info("getAllEventsByOrganizerIdOrWhiteLabelIdAndSubscriptionId whiLabelId {} organizerId {} ListSize {} ",whiLabelId, organizerId, eventNameLookUpDtoList.size());
        return eventNameLookUpDtoList;
    }
    @Override
    public Map<Long, Event> findAllEventsByEventIds(List<Long> eventIds) {
        List<Event> events = eventRepository.findAllByIds(eventIds);
        Map<Long, Event> eventMap = new HashMap<>();
        events.forEach(event -> eventMap.put(event.getEventId(), event));
        return eventMap;
    }

    @Override
    public List<BigInteger> findAllByActiveAndUpcomingEventsByWhiteLabel(Long whiteLabelId) {
        return eventRepository.findAllByActiveAndUpcomingEventsByWhiteLabel(whiteLabelId);
    }
}
