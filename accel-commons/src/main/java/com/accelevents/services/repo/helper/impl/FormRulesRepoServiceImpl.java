package com.accelevents.services.repo.helper.impl;

import com.accelevents.domain.FormRules;
import com.accelevents.domain.enums.RuleFormType;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.FormRulesRepository;
import com.accelevents.services.repo.helper.FormRulesRepoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FormRulesRepoServiceImpl implements FormRulesRepoService {

    private static final Logger log = LoggerFactory.getLogger(FormRulesRepoServiceImpl.class);


    private final FormRulesRepository formRulesRepository;

    @Autowired
    public FormRulesRepoServiceImpl(FormRulesRepository formRulesRepository){
        this.formRulesRepository = formRulesRepository;
    }

    @Override
    public FormRules save(FormRules formRule) {
        return formRulesRepository.save(formRule);
    }

    @Override
    public FormRules findById(Long ruleId) {
        return formRulesRepository.findById(ruleId).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.FORM_RULES_NOT_FOUND));
    }

    @Override
    public void delete(FormRules formRule) {
         formRulesRepository.delete(formRule);
    }

    @Override
    public List<FormRules> findByEventIdOrEventRequestFormIdBy(long eventIdOrEventRequestFormId,RuleFormType ruleFormType) {
       if(RuleFormType.EVENT_REQUEST_FORM.equals(ruleFormType)) {
           return formRulesRepository.findByEventRequestFormIdAndType(eventIdOrEventRequestFormId, ruleFormType);
       }else {
           return formRulesRepository.findByEventIdAndTypeAndRecurringEventIdIsNull(eventIdOrEventRequestFormId, ruleFormType);
       }
    }

    @Override
    public boolean isAttributeExistsInAnyRule(long eventId, long attributeId, RuleFormType type) {
        return formRulesRepository.isAttributeExistsInAnyRule(eventId,attributeId,type );
    }

    @Override
    public void saveAll(List<FormRules> formRulesList) {
        formRulesRepository.saveAll(formRulesList);
    }

    @Override
    public List<FormRules> findAllByEventIdAndCreatedFrom(Long eventId, long id) {
       return formRulesRepository.findAllByEventIdAndCreatedFrom(eventId, id);
    }

    @Override
    public void deleteAll(List<FormRules> formRules) {
        formRulesRepository.deleteAll(formRules);
    }

    @Override
    public List<FormRules> findByEventIdAndTypeAndRecurringEventId(Long eventIdOrEventRequestFormId, RuleFormType ruleFormType, Long recurringEventId) {
        return formRulesRepository.findByEventIdAndTypeAndRecurringEventId(eventIdOrEventRequestFormId, ruleFormType, recurringEventId);
    }

}
