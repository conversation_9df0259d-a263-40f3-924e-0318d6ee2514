package com.accelevents.services.repo.helper;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.TicketTypeSoldAndBookCountDto;
import com.accelevents.dto.stats.TicketStats;
import com.accelevents.messages.TicketType;
import com.accelevents.ticketing.dto.TicketingBuyerDataFromDB;
import com.accelevents.ticketing.dto.TicketingHomeDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

public interface EventCommonRepoService {

    void save(EventTickets eventTickets);

    EventTickets findByOrderWithOneTicket(TicketingOrder ticketingOrder);

    List<EventTickets> findByOrder(TicketingOrder ticketingOrder);

    List<EventTickets> findByTicketingOrderWithPaidAmountAndNotRefundAmount(List<TicketingOrder> ticketingOrder);

    List<EventTickets> findByOrderAndHolderEmail(TicketingOrder ticketingOrder, String holderEmail);

    List<EventTickets> findByOrder(long ticketingOrderId);


    List<EventTickets> getHolderEventTicketsANDNotINRecordStatus(User updatedUser);

    @Transactional
    List<EventTickets> getHolderEventTicketsANDNotINRecordStatusAndEventNotDeleted(User updatedUser);

    EventTickets findByIdAndTicketingOrderAndNotInTicketStatus(long eventTicketId, TicketingOrder ticketingOrder, Collection<TicketStatus> ticketStatus);

    void saveAll(List<EventTickets> eventTickets);

    boolean isTypePurchased(TicketingType ticketingType);

    EventTickets findByBarcodeId(String barcodeId);

    List<EventTickets> findAllTicketsBarcodeId(String barcodeId);

    List<EventTickets> findByBarcodeIds(List<String> barcodeId);

    List<EventTickets> findEventTicketByBarcodeIdsAndEventId(List<String> barcodeIds, Long eventId);

    TicketStats countByTypeIdAndStatusIdNotInAndStatusNot(TicketingType ticketingType,
                                                          TicketStatus ticketStatus);

    // TODO : Check with jon
    Map<Long, BigDecimal> findSoldCountGroupByType(Long eventId, Long ticketingId);

    Map<Long, BigDecimal> findSoldCountGroupByTypeAndTicketTypeId(Long eventId, Long ticketingId, List<Long> ticketTypeIds);

    BigDecimal findSoldCountByTicketTypeId(Long ticketTypeId);

    BigDecimal findSoldCountByEvent(Event event);

    BigDecimal findAddonSoldCountByEvent(Event event);

    BigDecimal findSoldCountByEventId(Long eventId);

    List<EventTickets> findByOrderAndStatusIdNotIn(TicketingOrder ticketingOrder,
                                                   Collection<TicketStatus> ticketStatus);

    EventTickets findById(long eventTicketId);

    EventTickets findByIdJoinFetch(long eventTicketId);

    void updatePurchaserByNewUser(User user, User newUser);

    void updateStaffByNewUser(User user, User newUser);

    Page<TicketingBuyerDataFromDB> getBuyerDataSearch(long eventId, String searchStr, Long recurringEventId,
                                                      DataType dataType, Pageable pageable);

    Page<TicketingBuyerDataFromDB> getTicketBuyerDataSearch(long eventId, String searchStr, Long recurringEventId, DataType dataType, Pageable pageable);

    List<EventTickets> findByEventIdJoinFetch(Event event, Long recurringEventId, boolean exitIntentPopupTriggered, DataType dataType, Date startTime, Date endTime);

    List<EventTickets> findByEventIdsJoinFetch(List<Event> events, Date startTime, Date endTime, List<TicketingOrder.OrderType> orderTypes);

    List<EventTickets> findByEventAndSessionIdJoinFetch(Event event, Long sessionId, Long recurringEventId, boolean exitIntentPopupTriggered, DataType dataType);


    Page<EventTickets> findByEventIdAndTypeAndStatus(Event event,
                                                     List<Long> ticketTypeIds, List<TicketStatus> ticketStatus,
                                                     List<TicketPaymentStatus> ticketPaymentStatuses,
                                                     List<TicketType> ticketType,
                                                     Long recurringEventId,
                                                     Pageable pageable);

    Long findAllCheckedInAttendees(Long eventId, Long ticketTypeId, Date startDate,Date endDate);

    Page<EventTickets> findByEventIdAndTypeAndStatusAndSearchStr(Event event,
                                                                 List<TicketType> ticketTypes,
                                                                 List<TicketStatus> ticketStatus,
                                                                 List<TicketPaymentStatus> ticketPaymentStatuses,
                                                                 String searchStr,
                                                                 Long recurringEventId,
                                                                 List<Long> ticketTypeIds,
                                                                 Pageable pageable);

    List<EventTickets> findByTypeAndStatusNotDeleted(TicketingType ticketingType);

    // TODO : Check with jon
    List<TicketingHomeDto> getSalesDataForDashboard(long ticketingId, long recurringEventId,
                                                    double percentage, double fixed,
                                                    String paymentGetWay, List<String> dataType);

    void updateTypeOfEventTicketsOrAddOn(Long orderId, TicketingType newTicketType, long oldTicketTypeid,
                                         long oldRecurringEventId, long newRecurringEventId);

    Long countTicketsByTicketStatus(Event event, List<TicketType> ticketTypes, DataType dataType);

    List<EventTickets> findByTicketsHaveOnlyBundleTypeTicketByOrderId(TicketingOrder ticketingOrder);

    List<EventTickets> findByOrderAndHolderEmailAndNotDeleted(TicketingOrder ticketingOrder, String holderEmail);

    List<EventTickets> findAllByOrder(long ticketingOrderId);

    List<EventTickets> findAllByOrderWithoutRecurring(long ticketingOrderId);

    List<EventTickets> findAllByListOrderIds(List<Long> ticketingOrderIds);

    List<EventTickets> findAllByTicketingOrderIdsAndFormattedDateAfter(List<Long> ticketingOrderIds,Date formattedDate);

    List<EventTickets> findDeletedEventTicketByEventIdAndTicketingStatusJoinFetch(Event event, Long recurringEventsId);

    List<EventTickets> findByEventIdAndTicketingStatus(Event event, List<TicketingOrder.TicketingOrderStatus> orderStatuses, Long recurringEventsId, DataType dataType, Date startTime, Date endTime);

    List<EventTickets> findByEventAndTicketStatusAndHolderUserIds(Event event, List<Long> userIds, Long recurringEventsId, DataType dataType);

    Long countByStatus(Event event, List<TicketType> ticketTypes, TicketStatus ticketStatus, DataType dataType, List<Long> ticketTypeIds);

    Long findByTypeIdAndStatusIdNotIn(TicketingType ticketingType, Collection<TicketStatus> ticketStatus);

    List<EventTickets> findByTicketingOrder(long orderId);

    List<EventTickets> findTicketRegisterForCheckIn(User holderUser, Event event);

    List<EventTickets> findTicketRegisterForUser(User holderUser, Event event);

    List<Object[]> findEmailAndHolderIdByTicketingOrder(long ticketingOrderId);

    List<EventTickets> findOtherEventTicketForSameUserANDEvent(Event event, User user, Long eventTicketId);

    List<String> findBlockUserByEmail(Event event, List<String> email);

    List<EventTickets> findAllTicketsByEventIdJoinFetch(Event event);

    List<EventTickets> findByIdsAndHolderUserId(List<Long> ids, Long holderUserId);

    List<EventTickets> findAllHolderUsersByListOrderIds(List<Long> ticketingOrderIds);

    List<EventTickets> findByHolderUserIdAndStatusNot(User holderUserId, TicketStatus status);

    List<Object> findUnSuccessTicketsForPayFlowByOrderId(long orderId);

    void updateEventTicketsForPayFlow(String recStatus, String status,String chargeId,  long id);

    List<EventTickets> findByTicketingOrderAndByUserJoinFetch(TicketingOrder ticketingOrder, User user);

    Page<User> findAllTicketHolderUserByEvent(Event event, String searchString, Pageable pageable);

    Long findIdByBarcodeIdAndEventId(String barcodeId, Long eventId);

    List<EventTickets> findByEventIdAndTicketingStatusJoinFetchAndEmails(Event event, List<TicketingOrder.TicketingOrderStatus> orderStatuses, Set<String> emailList);

    Set<String> findHolderEmailsByEventIdAndNotRefunded(Long eventId);

    Set<String> findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(Long eventId, Collection<String> emails);

    Set<String> findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndIdNotInAndTicketStatusNotIn(Long eventId, Collection<String> emails,List<Long> ticketIds);

    List<EventTickets> findByEventIdWithTicketingOrderStatusNotDeleted(Event event,Long recurringEventId,boolean exitIntentPopupTriggered,DataType dataType,Date startTime, Date endTime);

    List<EventTickets> findByIds(List<Long> ids);

    Long findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(Long eventId);

    List<EventTickets> findAllByIdAndHolderUserIdOrPurchaserUserId(List<Long> ticketIds, Long userId);

    List<EventTickets> findAllEventTicketByOrderIds(List<Long> orderIds);

    List<EventTickets> findEventTicketsOnlyByEventIdAndHolderUserId(Long eventId, Long holderUserId);

    List<TicketTypeSoldAndBookCountDto> soldTicketCountByTicketingTypeId(List<Long> ticketingTypeOnlyIds, TicketStatus ticketStatus);

    Set<Long> findBuyerOrderIdByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(Long eventId, String buyerEmail);

    Set<String> findBuyerEmailsByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(Long eventId, Collection<String> emails);
}
