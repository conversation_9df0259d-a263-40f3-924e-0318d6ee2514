package com.accelevents.services.repo.helper;

import com.accelevents.domain.FormRules;
import com.accelevents.domain.enums.RuleFormType;

import java.util.List;

public interface FormRulesRepoService {
    FormRules save(FormRules formRule);

    FormRules findById(Long ruleId);

    void delete(FormRules formRule);

    List<FormRules> findByEventIdOrEventRequestFormIdBy(long eventIdOrEventRequestFormId,RuleFormType ruleFormType);

    boolean isAttributeExistsInAnyRule(long eventId, long attributeId, RuleFormType type);

    void saveAll(List<FormRules> formRulesList);

    List<FormRules> findAllByEventIdAndCreatedFrom(Long eventId, long id);

    void deleteAll(List<FormRules> formRules);

    List<FormRules> findByEventIdAndTypeAndRecurringEventId(Long eventIdOrEventRequestFormId, RuleFormType ruleFormType, Long recurringEventId);
}
