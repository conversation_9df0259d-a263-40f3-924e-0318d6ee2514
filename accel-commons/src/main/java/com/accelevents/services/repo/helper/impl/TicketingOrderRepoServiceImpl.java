package com.accelevents.services.repo.helper.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.AccessCodeDto;
import com.accelevents.domain.*;
import com.accelevents.domain.TicketingOrder.TicketingOrderStatus;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.dto.EventMyTicketOrderDto;
import com.accelevents.dto.EventTicketOrderDto;
import com.accelevents.dto.EventTicketingAndEventDesignDetailDto;
import com.accelevents.repositories.TicketingOrderRepository;
import com.accelevents.services.impl.TicketingPurchaseServiceImpl;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.utils.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TicketingOrderRepoServiceImpl implements TicketingOrderRepoService {

    private static final Logger log = LoggerFactory.getLogger(TicketingOrderRepoServiceImpl.class);
	@Autowired
	private TicketingOrderRepository ticketingOrderRepository;

    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;

	@Override
	public Optional<TicketingOrder> findById(long ticketOrderId) {
		return ticketingOrderRepository.findById(ticketOrderId);
	}

    @Override
    public TicketingOrder save(TicketingOrder ticketingOrder) {
        TicketingOrder savedOrder = ticketingOrderRepository.save(ticketingOrder);
        try {
            if (savedOrder.getEventid() != null ) {
                clearAPIGatewayCache.clearAPIGwTicketTypesRemainingCountCache(savedOrder.getEventid().getEventURL());
            }
        } catch (Exception e) {
            log.error("Error clearing API Gateway cache errorMsg: {}",e.getMessage());
        }

        return savedOrder;
    }

    @Override
    public void saveAll(List<TicketingOrder> ticketingOrders) {
        Iterable<TicketingOrder> savedOrdersIterable = ticketingOrderRepository.saveAll(ticketingOrders);
        List<TicketingOrder> savedOrders = new ArrayList<>();
        savedOrdersIterable.forEach(savedOrders::add);

        try {
            if (!savedOrders.isEmpty()
                    && savedOrders.get(0).getEventid() != null) {
                clearAPIGatewayCache.clearAPIGwTicketTypesRemainingCountCache(
                        savedOrders.get(0).getEventid().getEventURL()
                );
            }
        } catch (Exception e) {
            log.error("saveAll | Error clearing API Gateway cache errorMsg: {}",e.getMessage());
        }
    }



    @Override
	public List<TicketingOrder> findByTicketingCouponAndEventid(TicketingCoupon ticketingCoupon, Event event) {
		return ticketingOrderRepository.findByTicketingCouponAndEventid(ticketingCoupon, event);
	}

	@Override
	public List<TicketingOrder> findByTicketingCouponInAndEventId(List<TicketingCoupon> couponList, Long eventId) {
		return ticketingOrderRepository.findByTicketingCouponInAndEventId(couponList, eventId);
	}

	@Override
	//@Transactional(rollbackFor = { Exception.class })
	public void updateByNewUser(User user, User newUser) {
		ticketingOrderRepository.updateByNewUser(user, newUser);
	}

	@Override
	public List<TicketingOrder> getTicketOrdersByTrackingLinks(TrackingLinks trackingLinks) {
		return ticketingOrderRepository.findByTrackingLinks(trackingLinks);
	}

	@Override
	public List<TicketingOrder> findByEventId(Event event) {
		return ticketingOrderRepository.findByEventid(event);
	}

	@Override
	public List<TicketingOrder> findByPurchaserIdAndEventIdAndOrderStatus(Event event, User user, TicketingOrderStatus create) {
		return ticketingOrderRepository.findByPurchaseridAndEventidAndOrderStatus(event, user, create);
	}

	@Override
	public List<TicketingOrder> findByEventidAndExportedToNeonFalse(Event event){
		return  ticketingOrderRepository.findByEventidAndExportedToNeonFalse(event);
	}

	@Override
	@Transactional(rollbackFor = { Exception.class })
	public boolean setExitIntentPopupTriggered(long orderId,Event event) {
		ticketingOrderRepository.updateExitIntenetTrigger(true ,event,orderId);
		return true;
	}

	@Override
	public Optional<TicketingOrder> findFirstByEventidAndStatusOrderByOrderDateAsc(Event event, TicketingOrder.TicketingOrderStatus status){
		return ticketingOrderRepository.findFirstByEventidAndStatusOrderByOrderDateAsc(event, status);
	}


    @Override
    public List<TicketingOrder> findListByEventIdAndStatusHavingStatusCreateAndExpired(Event event){
        return ticketingOrderRepository.findListByEventIdAndStatus(event);
    }

	@Override
	public TicketingOrder findOrderAndFetchByUserAndStaff(long orderId){
		return ticketingOrderRepository.findOrderAndFetchByUserAndStaff(orderId);
	}

	@Override
	public void updateTicketingOrderRecStatusByOrderIds(List<Long> orderIds, RecordStatus status) {
        if (!CollectionUtils.isEmpty(orderIds)){
            ticketingOrderRepository.updateTicketingOrderRecStatusByOrderIds(orderIds,status);
        }
	}

    @Order
    public List<TicketingOrder> findLatestPaidTicketingOrdersByEventIdWithLimit(Long eventId, int limit){
		Pageable pageable = PageRequest.of(0, limit);
		return ticketingOrderRepository.findByEventIdOrderByIdDesc(eventId, TicketingOrderStatus.PAID, pageable);
	}

    @Override
    public Integer getAccessCodeUsed(Long eventId, Long ticketingAccessCodeId) {
        return ticketingOrderRepository.getAccessCodeUsed(eventId,ticketingAccessCodeId,new Date());
    }

    @Override
    public Page<EventTicketOrderDto> findPastOrderByPurchaserAndStatus(User user, Pageable pageable) {
        return ticketingOrderRepository.findPastOrderByPurchaserAndStatus(user, TicketingOrderStatus.PAID, pageable);
    }

    public List<EventTicketingAndEventDesignDetailDto> findEventsTickitingAndEventDesignDetailsByEventIds(List<Long> eventIds){
        return ticketingOrderRepository.findEventsTickitingAndEventDesignDetailsByEventIds(eventIds);
    }

    @Override
    public List<EventTicketOrderDto> findActiveOrderByPurchaserAndOrderId(User user, List<Long> orderId, Pageable pageable) {
        List<TicketingOrderStatus> ticketingOrderStatusList = Arrays.asList(TicketingOrderStatus.PAID,TicketingOrderStatus.UNPAID,TicketingOrderStatus.PARTIAL);
        return ticketingOrderRepository.findActiveOrderByPurchaserAndOrderId(user, ticketingOrderStatusList, orderId);
    }

    @Override
    public Page<Long> findActiveOrderIdsByPurchaser(User user, Pageable pageable) {
        List<TicketingOrderStatus> ticketingOrderStatusList = Arrays.asList(TicketingOrderStatus.PAID, TicketingOrderStatus.UNPAID, TicketingOrderStatus.PARTIAL);
        return ticketingOrderRepository.findActiveOrderIdsByPurchaser(user, ticketingOrderStatusList, pageable);
    }

    @Override
    public List<EventMyTicketOrderDto> findHolderByEventAndHolderUser(User user, Event event) {
        List<TicketingOrderStatus> ticketingOrderStatusList = Arrays.asList(TicketingOrderStatus.PAID,TicketingOrderStatus.UNPAID,TicketingOrderStatus.PARTIAL,TicketingOrderStatus.CREATE);
        return ticketingOrderRepository.findHolderByEventAndHolderUser(user, ticketingOrderStatusList, event);
    }

    @Override
    public List<EventMyTicketOrderDto> findHolderByEventAndHolderUserIgnoreDonation(Long userId, Long eventId) {
        List<String> ticketingOrderStatusList = Arrays.asList(TicketingOrderStatus.PAID.name(),TicketingOrderStatus.UNPAID.name(),TicketingOrderStatus.PARTIAL.name(),TicketingOrderStatus.CREATE.name());
        List<Object[]> holderByEventAndHolderUserNative = ticketingOrderRepository.findHolderByEventAndHolderUserIgnoreDonation(userId, ticketingOrderStatusList, eventId);
        return holderByEventAndHolderUserNative.stream()
                .map(row -> new EventMyTicketOrderDto(
                        row[0] != null ? ((BigInteger) row[0]).longValue() : null,      // ticketOrderId
                        row[1] != null ? ((BigInteger) row[1]).longValue() : null,      // eventId
                        row[2] != null ? ((BigInteger) row[2]).longValue() : null,      // ticketId
                        row[3] != null ? (String) row[3] : null,                        // holderFirstName
                        row[4] != null ? (String) row[4] : null,                        // holderLastName
                        row[5] != null ? (String) row[5] : null,                        // holderEmail
                        row[6] != null ? (String) row[6] : null,                        // eventName
                        row[7] != null ? (String) row[7] : null,                        // ticketTypeName
                        row[8] != null ? (Date) row[8] : null,                          // eventStartDate
                        row[9] != null ? (Date) row[9] : null,                          // eventEndDate
                        row[10] != null ? (String) row[10] : null,                      // eventAddress
                        row[11] != null ? (String) row[11] : null,                      // barcodeId
                        row[12] != null && (Boolean) row[12],                    // guestOfBuyer
                        row[13] != null ? TicketPaymentStatus.valueOf(row[13].toString()) : null, // ticketPaymentStatus
                        row[14] != null ? DataType.valueOf(row[14].toString()) : null  // dataType
                ))
                .collect(Collectors.toList());
    }

    @Override
    public Page<EventTicketOrderDto> getUserActiveOrdersByOrderIdAndHolderEmail(Long orderId, String holderEmail, Pageable pageable) {
        return ticketingOrderRepository.getUserActiveOrdersByOrderIdAndHolderEmail(orderId, TicketingOrderStatus.PAID, holderEmail, pageable);
    }
    @Override
    @Transactional
    public List<TicketingOrder> getOrderByPurchaseridAndRecordStatusNotDeleted(User purchaser) {
        return ticketingOrderRepository.getOrderByPurchaseridAndRecordStatusNotDeleted(purchaser.getUserId());
    }

    @Override
    @Transactional
    public void updateTicketingOrderbuyerEmailByOrderIds(List<Long> orderIds, String email) {
         ticketingOrderRepository.updateTicketingOrderbuyerEmailByOrderIds(orderIds,email);
    }

    @Override
    @Transactional
    public Page<TicketingOrder> findListByEventIdAndNotDeletedStatus(Event event,List<TicketingOrderStatus> status,Long recurringEventId){
        Pageable pageable = PageRequest.of(0, Constants.RECORD_SIZE_FOR_SHOW_USERDATA);
        return ticketingOrderRepository.findListByEventIdAndNotDeletedStatus(event,status,recurringEventId, pageable);
    }
    @Override
    @Transactional
    public Long getUniqueRegisteredUserCount(Event event, List<TicketingOrderStatus> status,Long recurringEventId){
        return ticketingOrderRepository.getUniqueRegisteredUserCount(event,status,recurringEventId);
    }
    @Override
    @Transactional
    public List<AccessCodeDto> getAccessCodeForUsed(long eventId, List<Long> accessCodeId){
       List<AccessCodeDto> accessCodeDtoList= ticketingOrderRepository.getAccessCodeForUsed(eventId,accessCodeId);
       return CollectionUtils.isEmpty(accessCodeDtoList) ? Collections.emptyList() :  accessCodeDtoList;
    }

    @Override
    public List<TicketingOrder> findByOrderIds(List<Long> orderIds) {
        return ticketingOrderRepository.findByOrderIds(orderIds);
    }

    @Override
    public List<TicketingOrder> findOrdersByEventIds(List<Event> events, List<TicketingOrderStatus> statusList) {
        return ticketingOrderRepository.findOrdersByEventIds(events, statusList);
    }

    @Override
    public List<TicketingOrder> findOrdersByEventIdsAndOrderType(List<Event> events, TicketingOrder.OrderType orderType) {
        return ticketingOrderRepository.findOrdersByEventIdsAndOrderType(events, orderType);
    }

    @Override
    public List<TicketingOrder> findAllByEventIdAndStatusOrOrderTypeOrderByOrderDateDesc(Event event, List<TicketingOrderStatus> status, Long recurringEventId){
	    return  ticketingOrderRepository.findAllByEventIdAndStatusOrOrderTypeOrderByOrderDateDesc(event,status,recurringEventId);
    }

    @Override
    public List<Long> findListByTicketingCouponAndEventidAndPurchaser(TicketingCoupon ticketingCoupon, Event event, User purchaser) {
        return ticketingOrderRepository.findListByTicketingCouponAndEventidAndPurchaser(ticketingCoupon, event, purchaser);
    }

    @Override
    public List<Long> findListByTicketingCouponAndEventid(TicketingCoupon ticketingCoupon, Event event) {
        return ticketingOrderRepository.findListByTicketingCouponAndEventidAndPurchaser(ticketingCoupon, event, null);
    }
}