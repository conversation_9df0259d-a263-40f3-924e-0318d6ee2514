package com.accelevents.services.repo.helper;

import com.accelevents.common.dto.SurveysDto;
import com.accelevents.domain.SurveyResponse;
import com.accelevents.domain.User;
import com.accelevents.dto.EventCECriteriaDTO;
import com.accelevents.repositories.SurveyResponseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SurveyResponseRepoServiceImpl implements SurveyResponseRepoService {

    @Autowired
    SurveyResponseRepository surveyResponseRepository;

    @Override
    public void save(SurveyResponse surveyResponse) {
        surveyResponseRepository.save(surveyResponse);
    }

    @Override
    public boolean findBySurveyIdAndUserId(Long surveyId, Long userId) {
        return surveyResponseRepository.findBySurveyIdAndUserId(surveyId,userId);
    }

    @Override
    public boolean findBySurveyIdAndUserIdAndSessionId(Long surveyId, Long userId, Long sessionId) {
        return surveyResponseRepository.findBySurveyIdAndUserIdAndSessionId(surveyId,userId, sessionId);
    }

    @Override
    public boolean findBySurveyIdAndAnonymousUuidAndEventId(Long surveyId, String anonymousUserUuid, Long eventId) {
        return surveyResponseRepository.findBySurveyIdAndUserId(surveyId,anonymousUserUuid,eventId);
    }

    @Override
    public int findCountBySurveyId(long surveyId) {
        return surveyResponseRepository.findCountBySurveyId(surveyId);
    }

    @Override
    public List<SurveysDto> findResponseCountByEventId(long eventId) {
        return surveyResponseRepository.findResponseCountByEventId(eventId);
    }

    @Override
    public List<SurveyResponse> findAllBySurveyId(long id) {
        return surveyResponseRepository.findAllBySurveyId(id);
    }

    @Override
    public Optional<SurveyResponse> findFirstSurveyResponseBySurveyIdAndUserId(long surveyId, long userId) {
        return surveyResponseRepository.findFirstSurveyResponseBySurveyIdAndUserId(surveyId,userId);
    }
    @Override
    public Optional<SurveyResponse> findFirstSurveyResponseBySurveyIdAndAnonymousUserUuid(long surveyId, String  anonymousUserUuid) {
        return surveyResponseRepository.findFirstSurveyResponseBySurveyIdAndAnonymousUserUuid(surveyId,anonymousUserUuid);
    }

    /*
     We need to pass SurveyIds list with unique value
     */
    @Override
    @NotNull
    public Map<Long, Integer> getAllUserSurveyScoreBySurveyIds(List<Long> surveyIds, EventCECriteriaDTO eventCECriteriaDTO) {
        List<Object[]> userList = surveyResponseRepository.getAllUserSurveyScoreBySurveyIds(surveyIds, eventCECriteriaDTO.getStartDateUtc(), eventCECriteriaDTO.getEndDateUtc());
        if (!CollectionUtils.isEmpty(userList)) {
            return userList.stream()
                    .collect(Collectors.groupingBy(
                            objects -> Long.parseLong(String.valueOf(objects[0])),
                            Collectors.collectingAndThen(
                                    Collectors.toMap(
                                            obj -> Long.parseLong(String.valueOf(obj[1])), // Map to BigDecimal
                                            obj -> BigDecimal.valueOf(Double.parseDouble(String.valueOf(obj[2]))),
                                            (existing, replacement) -> replacement
                                    ),
                                    values -> {
                                        // Calculate the sum and average using BigDecimal
                                        BigDecimal sum = values.values().stream()
                                                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, RoundingMode.HALF_UP);
                                        BigDecimal average = sum.divide(
                                                BigDecimal.valueOf(surveyIds.size()),
                                                RoundingMode.HALF_UP // Rounding mode
                                        );
                                        return average.intValue(); // Convert to Integer
                                    }
                            )
                    ));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<Object[]> getAllSurveyResponseUserScoreAndSubmissionDateByUserIdAndEventId(Long userId, long eventId){
        return surveyResponseRepository.getAllSurveyResponseUserScoreAndSubmissionDateByUserIdAndEventId(userId, eventId);
    }


    @Override
    public List<SurveyResponse> getCountOfSurveyResponseAndLatestSubmissionDateBySessionIds(List<Long> sessionIds, long eventId,Date challengeStartDate, Date challengeEndDate){
        return surveyResponseRepository.getCountOfSurveyResponseAndLatestSubmissionDateBySessionIds(sessionIds, eventId,challengeStartDate,challengeEndDate);
    }

    @Override
    public Integer getUserSurveyScoreBySurveyIdsAndUserIdAndEvent(List<Long> surveyIds, Long userId, long eventId, EventCECriteriaDTO eventCECriteriaDTO, List<Long> sessionIds) {
        List<Integer> surveyQuizScoreOfUser  = surveyResponseRepository.getUserSurveyScoreBySurveyIdsAndUserIdAndEventId(surveyIds, userId, eventId, eventCECriteriaDTO.getStartDateUtc(), eventCECriteriaDTO.getEndDateUtc(),sessionIds);

        if(sessionIds.size() == surveyQuizScoreOfUser.size()){
            return (int) Math.round(
                    surveyQuizScoreOfUser.stream()
                            .mapToInt(Integer::intValue)
                            .average()
                            .orElse(0)
            );
        }
        return null;

    }

    @Override
    public long getCountOfSurveyResponseAndLatestSubmissionDateBySessionIdsAndUserId(List<Long> sessionIds, EventCECriteriaDTO eventCECriteriaDTO, User user) {
        return surveyResponseRepository.getCountOfSurveyResponseAndLatestSubmissionDateBySessionIdsAndUserId(sessionIds, eventCECriteriaDTO.getStartDateUtc(), eventCECriteriaDTO.getEndDateUtc(), user.getUserId() );
    }

    @Override
    public List<SurveyResponse> findAllByEventId(long eventId) {
        return surveyResponseRepository.findAllByEventId(eventId);
    }

    @Override
    public List<Object[]> getAllSurveyResponseUserScoreAndSubmissionDateByUserIdsAndEventId(List<Long> userIds, long eventId) {
        return surveyResponseRepository.getAllSurveyResponseUserScoreAndSubmissionDateByUserIdsAndEventId(userIds ,eventId);
    }

    @Override
    public List<SurveyResponse> getSurveyCompletionDataBySurveyIds(List<Long> surveyIds, long eventId, Date challengeStartDate, Date challengeEndDate) {
        return surveyResponseRepository.getSurveyCompletionDataBySurveyIdsAndEventId(surveyIds,eventId, challengeStartDate, challengeEndDate);
    }

    @Override
    public List<SurveyResponse> findAllBySurveyIdAndUserIds(long id, List<Long> userIds) {
        return surveyResponseRepository.findAllBySurveyIdAndUserIdIn(id, userIds);
    }
}
