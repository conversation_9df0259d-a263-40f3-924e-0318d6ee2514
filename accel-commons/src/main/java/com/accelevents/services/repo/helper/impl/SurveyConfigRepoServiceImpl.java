package com.accelevents.services.repo.helper.impl;

import com.accelevents.common.dto.SurveysDto;
import com.accelevents.domain.SurveyConfiguration;
import com.accelevents.repositories.SurveyConfigRepository;
import com.accelevents.services.repo.helper.SurveyConfigRepoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class SurveyConfigRepoServiceImpl implements SurveyConfigRepoService {

    @Autowired
    private SurveyConfigRepository surveyConfigRepository;

    @Override
    public SurveyConfiguration save(SurveyConfiguration surveyConfiguration){
       return surveyConfigRepository.save(surveyConfiguration);
    }

    @Override
    public Optional<SurveyConfiguration> findById(long id){
        return surveyConfigRepository.findById(id);
    }

    @Override
    public boolean isSurveyNameIsExistInEvent(String surveyName, long eventId){
        return surveyConfigRepository.isSurveyNameIsExistInEvent(surveyName,eventId);
    }

    @Override
    public Page<SurveyConfiguration> findByEventIdAndSearchString(long eventId, String searchString, Pageable pageable) {
        searchString = StringUtils.isNotBlank(searchString) ? searchString:"";
        return surveyConfigRepository.findByEventIdAndSearchString(eventId, searchString.toLowerCase(), pageable);
    }

    @Override
    public Page<SurveyConfiguration> findByEventId(long eventId, Pageable pageable) {
        return surveyConfigRepository.findByEventId(eventId, pageable);
    }

    @Override
    public boolean isSurveyExist(Long eventId,Long surveyId) {
        return surveyConfigRepository.isSurveyExistById(eventId,surveyId);
    }

    @Override
    public List<SurveyConfiguration> findSurveysByEventId(long eventId) {
        return  surveyConfigRepository.findSurveysByEventId(eventId);
    }

    @Override
    public List<SurveysDto> getAllSurveysByEvent(Long eventId){
        return surveyConfigRepository.getAllSurveysByEvent(eventId);
    }

    @Override
    public boolean isAnySurveyQuizAble(List<Long> surveyIds) {
        return surveyConfigRepository.isAnySurveyQuizAble(surveyIds);
    }

    @Override
    public List<Long> getAllQuizAbleSurveyIds(Long eventId){
        return surveyConfigRepository.getAllQuizAbleSurveyIds(eventId);
    }

    @Override
    public List<SurveyConfiguration> saveAll(List<SurveyConfiguration> surveyConfigurations) {
        Iterable<SurveyConfiguration> saveList = surveyConfigRepository.saveAll(surveyConfigurations);
        List<SurveyConfiguration> list = new ArrayList<>();
        saveList.forEach(list::add);
        return list;
    }

    @Override
    public List<SurveyConfiguration> findSurveysByIds(List<Long> surveyIds) {
        return  surveyConfigRepository.findSurveysByIds(surveyIds);
    }

    @Override
    public List<SurveysDto> getSurveysByIds(List<Long> surveyIds){
        return surveyConfigRepository.getSurveysByIds(surveyIds);
    }

    @Override
    public SurveyConfiguration findDefaultSurveyByEventId(long eventId){
        return surveyConfigRepository.findDefaultSurveyByEventId(eventId);
    }
}
