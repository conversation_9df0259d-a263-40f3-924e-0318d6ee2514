package com.accelevents.services;

import com.accelevents.domain.CertificateImages;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.CertificateTypes;
import com.accelevents.dto.CertificateBasicDto;
import com.accelevents.dto.CertificatesDto;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.ResponseDto;

import java.util.HashMap;
import java.util.List;

public interface CertificatesService {
    DataTableResponse getAllCertificatesOfEvent(Event event, String searchString, int page, int size, String sortColumn, boolean isAsc);

    CertificatesDto createOrUpdateCertificate(CertificatesDto certificatesDto, Event event, User user);

    CertificatesDto getCertificate(Long id, Event event, User user);

    void deleteCertificate(Long id, Event event, User user);

    CertificatesDto cloneCertificates(User user, Event event, Long certificateId);

    CertificateImages saveCertificateImageWhileUpload(Event event, String imgUrl, String fileName, User user, Long certificateId);

    ResponseDto updateCertificateImageHideFlag(Long id, boolean hideImage, Event event, User user);

    List<CertificateImages> getAllImagesOfCertificate(Event event, Long id);

    ResponseDto removeCertificateImage(Long id, Long imageId, User user, Event event);

    void upsertCustomFields(Event event, User user, Long certificateId, List<HashMap<String, Object>> customAttributes);

    List<CertificateBasicDto> getAllCertificatesBasicDetails(Event event, boolean allTypes);
}
