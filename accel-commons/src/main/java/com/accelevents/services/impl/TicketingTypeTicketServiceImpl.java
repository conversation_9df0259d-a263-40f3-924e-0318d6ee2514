package com.accelevents.services.impl;

import com.accelevents.common.dto.TicketTypeNameQtyDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.RecurringEvents;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketTypeFormat;
import com.accelevents.dto.SeatingCategoryDto;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.TicketingTypeCommonRepo;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.services.*;
import com.accelevents.ticketing.dto.CategoryDto;
import com.accelevents.ticketing.dto.EventCategoryDto;
import com.accelevents.utils.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.FREE_ADMISSION;
import static com.accelevents.utils.Constants.GENERAL_ADMISSION;
import static com.accelevents.utils.DateUtils.getCurrentDate;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;

@Service
public class TicketingTypeTicketServiceImpl implements TicketingTypeTicketService {

    @Autowired
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;

    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;

    @Autowired
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Autowired
    private TimeZoneService timeZoneService;

    @Autowired
    @Lazy
    private TicketingTypeService ticketingTypeService;

    @Autowired
    TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Autowired
    TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Autowired
    private TicketingManageService ticketingManageService;

    @Autowired
    private SeatingCategoryService seatingCategoryService;

	@Override
    public TicketingType setPositionForTicketingType(TicketingType ticketingType) {
        if (ticketingType.getId() != 0) {
            return ticketingType;
        } else {
            TicketingType lastItem = getLastItem(ticketingType);
			double sequence = 1000;
			if (lastItem != null) {
                ticketingType.setPosition(lastItem.getPosition() + sequence);
            } else {
                ticketingType.setPosition(sequence);
            }
            return ticketingType;
        }
    }

    public TicketingType getLastItem(TicketingType ticketingType) {
        if(NumberUtils.isNumberGreaterThanZero(ticketingType.getRecurringEventId())){
            return ticketingTypeRepository.findFirstByEventAndRecurringEventIdOrderByPositionDesc(ticketingType.getEventId(), ticketingType.getRecurringEventId());
        } else {
            return ticketingTypeRepository.findFirstByEventIdAndRecurringEventIdIsNullOrderByPositionDesc(ticketingType.getEventId());
        }
    }

    @Override
    public boolean isAnyTicketExistsWithDonationType(Event event) {
        return ticketingTypeRepository.findTicketTypeAsCategoryByEvent(event, TicketType.DONATION).size() > 0;
    }

    @Override
    public List<TicketingType> getByListOfRecurringEvents(List<RecurringEvents> recurringEventsList, Boolean fetchHiddenTicketsOnly) {
        List<Long> recurringEventIdList = recurringEventsList.stream().map(e->e.getId()).collect(Collectors.toList());

        return ticketingTypeRepository.findByListOfRecurringEvents(recurringEventIdList, fetchHiddenTicketsOnly ? true : null, null);
    }

    @Override
    public List<TicketingType> getAllByTicketingAndCreatedFromNullOnlyPaidOrderByPosition(Event event) {
        return ticketingTypeRepository.findAllByEventIdRecurringIdNullOnlyPaid(event, TicketType.PAID);
    }

    public List<CategoryDto> getAllTicketingTypes(Long eventId) {
        return ticketingTypeRepository.findAllTicketingTypes(eventId, TicketType.PAID);
    }

    @Override
    public List<Object[]> getNumberOfTotalTickets(Set<Long> eventIds) {
        if(eventIds == null || eventIds.isEmpty())
            return Collections.emptyList();

        return ticketingTypeRepository.findNumberOfTotalTickets(eventIds);
    }

    @Override
    public Long getNumberOfTotalTicketsInEvent(Long eventId, DataType dataType) {
        return ticketingTypeRepository.findNumberOfTotalTicketsInEvent(eventId, dataType);
    }

    @Override
    public List<TicketingType> getAllTicketingTypesByRecuurringEvent(Long recurringEventId){
        return ticketingTypeRepository.findAllTicketingTypesByRecuurringEvent(recurringEventId, TicketType.PAID);
    }
    @Override
    public List<TicketingType> findAllByRecurringEventsDisplay(Long recurringEventId, Boolean excludeHidden, DataType dataType) {
        RecurringEvents recurringEvents = recurringEventsScheduleService.getRecurringEvents(recurringEventId);
        return ticketingTypeRepository.findByListOfRecurringEventsOrAddOns(Collections.singletonList(recurringEvents.getId()),
                Boolean.TRUE.equals(excludeHidden) ? false : null, dataType);
    }

    @Override
    public List<TicketingType> findAllByRecurringEvents(Long recurringEventId, Boolean fetchHiddenTicketsOnly, DataType dataType) {
        RecurringEvents recurringEvents = recurringEventsScheduleService.getRecurringEvents(recurringEventId);
        return ticketingTypeRepository.findByListOfRecurringEvents(Collections.singletonList(recurringEvents.getId()),
                fetchHiddenTicketsOnly ? true : null, dataType);
    }

    @Override
    public List<TicketingType> findAllTicketTypeOrAddOnByRecurringEvents(List<Long> recurringEventsList, Boolean fetchHiddenTicketsOnly, List<DataType> dataTypes) {
        return ticketingTypeRepository.findAllTicketTypeOrAddOnByRecurringEvents(recurringEventsList,
                fetchHiddenTicketsOnly ? Boolean.TRUE : null, dataTypes);
    }

    @Override
    public List<TicketingType> getTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(Long ticketTypeId, Event event) {
        Date currentDateAndTimeInEventTimeZone = getDateInLocal(getCurrentDate(), event.getEquivalentTimeZone());
        return ticketingTypeRepository.findTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(ticketTypeId, currentDateAndTimeInEventTimeZone);
    }

    @Override
    public List<TicketingType> findAllByTicketingAndRecuringEvent(Long recurringEventId, Event event) {
        return ticketingTypeRepository.findAllByEventIdRecurring(event, recurringEventId);
    }

    @Override
    public List<TicketingType> findAllTicketTypeByEventAndRecurringEvent(Long recurringEventId, Event event) {
        return ticketingTypeRepository.findAllTicketTypeByEventAndRecurringEvent(event, recurringEventId);
    }

    @Override
    public List<TicketingType> findAllByEventIdAndRecurringEventIdPresent(Event event, boolean hidden) {
        List<DataType> dataTypes = Arrays.asList(DataType.TICKET, DataType.ADDON);
        return ticketingTypeRepository.findAllByEventIdAndRecurringIdIsNotNullAndHidden(event, hidden, dataTypes);
    }

    @Override
    public List<TicketingType> findAllByEvent(Event event) {
        Date currentDate = getCurrentDate();
        return ticketingTypeRepository.findAllByEventId(event, currentDate,Arrays.asList(DataType.TICKET,DataType.ADDON));
    }

    @Override
    public List<TicketingType> findTicketingByEventIdList(List<Long> eventIds) {
        return eventIds.isEmpty() ? new ArrayList<>() : ticketingTypeRepository.findTicketingByEventIdList(eventIds);
    }

    @Override
    public List<Long> findAllIdByEventIdAndRecurringEvent(Event event, Long recurringEventId) {
        return ticketingTypeRepository.findAllIdByEventIdAndRecurringEvent(event,recurringEventId);
    }

   /* @Override
    public void createGeneralAdmissionTicketIfNotExists(Ticketing ticketing, Event event) {
        List<TicketingType> ticketTypes  = ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId());
        if(ticketTypes.isEmpty()){
            TicketingType ticketingType = new TicketingType();
            ticketingType.setTicketTypeName(GENERAL_ADMISSION);
            ticketingType.setTicketType(TicketType.PAID);
            ticketingType.setPrice(50);
            ticketingType.setEndDate(ticketing.getEventEndDate());
            ticketingType.setStartDate(new Date());
            ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
            ticketingType.setRecurringEventSalesEndTime(60);
            ticketingType.setNumberOfTickets(100);
            ticketingType.setMaxTicketsPerBuyer(10);
            ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
            ticketingType.setTicketing(ticketing);
            transactionFeeConditionalLogicService.applyFeeInTicketType(event, ticketingType);
            ticketingType = ticketingTypeService.save(ticketingType);

            List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService.findByEventId(event);
            for (TicketHolderRequiredAttributes ticketHolderRequiredAttribute : ticketHolderRequiredAttributes) {
                boolean isEnabledForTicketPurchaser = ticketHolderRequiredAttribute.getEnabledForTicketPurchaser();
                boolean isEnabledForTicketHolder = ticketHolderRequiredAttribute.getEnabledForTicketHolder();
                boolean isFirstNameAllow = Constants.STRING_FIRST_SPACE_NAME.equals(ticketHolderRequiredAttribute.getName());
                boolean isLastNameAllow = Constants.STRING_LAST_SPACE_NAME.equals(ticketHolderRequiredAttribute.getName());
                boolean isEmailAllow = Constants.STRING_EMAIL_SPACE.equalsIgnoreCase(ticketHolderRequiredAttribute.getName());
                boolean isPhoneNumberAllow = Constants.STRING_CELL_SPACE_PHONE.equals(ticketHolderRequiredAttribute.getName());

                if (isEnabledForTicketPurchaser
                        && !isFirstNameAllow && !isLastNameAllow && !isEmailAllow && !isPhoneNumberAllow) {


                    String buyerTicketTypeId = ticketHolderRequiredAttribute.getBuyerEventTicketTypeId();
                    if (buyerTicketTypeId != null) {
                        buyerTicketTypeId = buyerTicketTypeId + "," + ticketingType.getId();
                    } else {
                        buyerTicketTypeId = String.valueOf(ticketingType.getId());
                    }

                    ticketHolderRequiredAttribute.setBuyerEventTicketTypeId(buyerTicketTypeId);

                }

                if(isEnabledForTicketHolder){
                    String holderTicketTypeId = ticketHolderRequiredAttribute.getHolderEventTicketTypeId();
                    if (holderTicketTypeId != null) {
                        holderTicketTypeId = holderTicketTypeId + "," + ticketingType.getId();
                    } else {
                        holderTicketTypeId = String.valueOf(ticketingType.getId());
                    }

                    ticketHolderRequiredAttribute.setHolderEventTicketTypeId(holderTicketTypeId);
                    ticketHolderRequiredAttributesService.save(ticketHolderRequiredAttribute);
                }
            }
        }
    }
*/

    @Override
    public List<Long> getTicketTypeIdsByCreatedFromsAndInRecurringEventId(List<Long> createdFroms, Long recurringEventId) {
        return createdFroms.isEmpty() ? Collections.EMPTY_LIST : ticketingTypeRepository.getTicketTypeIdsByCreatedFromsAndInRecurringEventId(createdFroms, recurringEventId);
    }

    @Override
    public void markAsHiddenByTicketTypeIds(List<Long> newTicketIds) {
            ticketingTypeRepository.markAsHiddenByTicketTypeIds(newTicketIds, true);
    }

    @Override
    public List<TicketingType> getTicketingTypesByRecurringList(List<Long> oldReIdsList) {
        return ticketingTypeRepository.findTicketingTypesByRecurringList(oldReIdsList);
    }

    @Override
    public List<Long> findRecurringEventTypeIdByEventAndCreatedFrom(Event event, Long createdFrom) {
        return ticketingTypeRepository.findRecurringEventTypeIdByEventAndCreatedFrom(event,createdFrom);
    }

    @Override
    public List<Long> getTicketTypeIdsByCreatedFroms(List<Long> createdFrom) {
        return ticketingTypeRepository.findTicketTypeIdsByCreatedFroms(createdFrom);
    }

    @Override
    public void updateTicketingTypeRecStatusByRecurringEventIds(List<Long> recurringEventIds, RecordStatus status) {
        ticketingTypeRepository.updateTicketingTypeRecStatusByRecurringEventIds(recurringEventIds,status);
    }

    @Override
    public List<TicketingType> findByCategoryId(Long categoryId){
        return ticketingTypeRepository.findByCategoryId(categoryId, Arrays.asList(DataType.TICKET, DataType.ADDON));
    }


    @Override
    public void createGeneralAdmissionTicketIfNotExists(Ticketing ticketing, Event event) {
        List<TicketingType> ticketTypes  = ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, event.getEventId());
        if(ticketTypes.isEmpty()){
            String name = GENERAL_ADMISSION;
            double price = 50;
            int quantity = 100;

           // EventCategoryDto eventCategoryDto = createDefaultCategory(event, name, price, quantity);
            TicketingType ticketingType = createDefaultTicket(ticketing, event, name, price, quantity);

            ticketingManageService.updateHolderRequiredAttributesForNewTicket(event,ticketingType);
            ticketingManageService.updateRegistrationRequiredAttributesForNewTicket(event, ticketingType);
        }

    }

    private TicketingType createDefaultTicket(Ticketing ticketing, Event event, String name, double price, int quantity) {
        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(name);
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setPrice(price);
        ticketingType.setDataType(DataType.TICKET);
        ticketingType.setEndDate(ticketing.getEventEndDate());
        ticketingType.setStartDate(new Date());
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(60);
        ticketingType.setNumberOfTickets(quantity);
        ticketingType.setNumberOfTicketPerTable(1);
        ticketingType.setMaxTicketsPerBuyer(10);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType.setTicketing(ticketing);
        ticketingType.setEventId(event.getEventId());
        ticketingType.setTicketTypeFormat(EventFormat.VIRTUAL.equals(event.getEventFormat())?
                                            TicketTypeFormat.VIRTUAL : TicketTypeFormat.IN_PERSON);
        transactionFeeConditionalLogicService.applyFeeInTicketType(event, ticketingType,
                EventFormat.VIRTUAL.equals(event.getEventFormat()) || EventFormat.HYBRID.equals(event.getEventFormat()),
                false);
        ticketingType = ticketingTypeService.save(ticketingType);
        return ticketingType;
    }

    private EventCategoryDto createDefaultCategory(Event event, String generalAdmission, double price, int quantity) {
        SeatingCategoryDto categoryDto = new SeatingCategoryDto();
        categoryDto.setName(generalAdmission);
        categoryDto.setHavingVariations(false);
        categoryDto.setPrice(price);
        categoryDto.setQuantity(quantity);
        return seatingCategoryService.addCategory(event, categoryDto,false);
    }

    @Override
    public TicketingType createFreeAdmissionTicketIfNotExists(Ticketing ticketing, Event event) {
        List<TicketingType> ticketTypes  = ticketingTypeRepository.findByTicketTypeNameAndEventId(FREE_ADMISSION, event.getEventId());
        if(ticketTypes.isEmpty()){
            String name = FREE_ADMISSION;
            double price = 0;
            int quantity = 100;

            TicketingType ticketingType = createFreeTicket(ticketing, event, name, price, quantity);

            ticketingManageService.updateHolderRequiredAttributesForNewTicket(event,ticketingType);
            ticketingManageService.updateRegistrationRequiredAttributesForNewTicket(event, ticketingType);
            return ticketingType;
        }
        return ticketTypes.get(0);
    }

    private TicketingType createFreeTicket(Ticketing ticketing, Event event, String name, double price, int quantity) {
        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(name);
        ticketingType.setTicketType(TicketType.FREE);
        ticketingType.setPrice(price);
        ticketingType.setDataType(DataType.TICKET);
        ticketingType.setEndDate(ticketing.getEventEndDate());
        ticketingType.setStartDate(new Date());
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(60);
        ticketingType.setNumberOfTickets(quantity);
        ticketingType.setNumberOfTicketPerTable(1);
        ticketingType.setMaxTicketsPerBuyer(10);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType.setTicketing(ticketing);
        ticketingType.setEventId(event.getEventId());
        if (EventFormat.VIRTUAL.equals(event.getEventFormat())){
            ticketingType.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
            ticketingType.setAllowPDFDownload(false);
            ticketingType.setAllowInvoicePDFDownload(false);
        }else if (EventFormat.IN_PERSON.equals(event.getEventFormat())){
            ticketingType.setTicketTypeFormat(TicketTypeFormat.IN_PERSON);
            ticketingType.setAllowPDFDownload(true);
            ticketingType.setAllowInvoicePDFDownload(true);
        }else {
            ticketingType.setTicketTypeFormat(TicketTypeFormat.HYBRID);
            ticketingType.setAllowPDFDownload(true);
            ticketingType.setAllowInvoicePDFDownload(true);
        }
        transactionFeeConditionalLogicService.applyFeeInTicketType(event, ticketingType,
                EventFormat.VIRTUAL.equals(event.getEventFormat()) || EventFormat.HYBRID.equals(event.getEventFormat()), false);
        ticketingType = ticketingTypeService.save(ticketingType);
        return ticketingType;
    }

    @Override
    public List<Long> getTicketTypeIdsByCreatedFromAndInRecurringEventIdAndDataType(List<Long> createdFroms, Long recurringEventId, DataType dataType) {
        return createdFroms.isEmpty() ? Collections.EMPTY_LIST : ticketingTypeRepository.getTicketTypeIdsByCreatedFromAndInRecurringEventIdAndDataType(createdFroms, recurringEventId, dataType);
    }

    @Override
    public List<TicketingType> getAllTicketingTypesByRecurringEventBasedOnAllowSeating(Long recurringEventId, List<TicketType> ticketTypesList) {
        return ticketingTypeRepository.getAllTicketingTypesByRecurringEventBasedOnAllowSeating(recurringEventId, ticketTypesList);

    }

    @Override
    public boolean isUserAllowedToCreateMeetingByTicketTypeIds(List<Long> ticketTypeIds){
        return ticketingTypeRepository.isUserAllowedToCreateMeetingByTicketTypeIds(ticketTypeIds);
    }

    @Override
    public List<Object[]> getTicketTypeMeetingCreationStatusByTicketTypeIds(List<Long> ticketingTypeIds) {
        return ticketingTypeRepository.getTicketTypeMeetingCreationStatusByTicketTypeIds(ticketingTypeIds);
    }


}
