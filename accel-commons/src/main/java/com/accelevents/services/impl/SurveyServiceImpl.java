package com.accelevents.services.impl;

import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.event.service.ROUserSessionService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.SurveyConfigRepoService;
import com.accelevents.services.repo.helper.SurveyQuestionsRepoService;
import com.accelevents.services.repo.helper.SurveyResponseRepoService;
import com.accelevents.services.tray.io.tracking.TrayTrackingService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.JsonMapper;
import com.accelevents.utils.NumberUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.convertCommaSeparatedToListLong;
import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;

@Service
public class SurveyServiceImpl implements SurveyService {

    private static final Logger log = LoggerFactory.getLogger(SurveyServiceImpl.class);

    @Autowired
    SurveyConfigRepoService surveyConfigRepoService;

    @Autowired
    ROStaffService roStaffService;

    @Autowired
    SurveyQuestionsRepoService surveyQuestionsRepoService;

    @Autowired
    SurveyResponseRepoService surveyResponseRepoService;

    @Autowired
    UserService userService;

    @Autowired
    DownloadService downloadService;

    @Autowired
    private SessionRepoService sessionRepoService;

    @Autowired
    private TrayTrackingService trayTrackingService;

    @Autowired
    private ChallengeConfigService challengeService;

    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;

    @Autowired
    private ROUserSessionService roUserSessionService;

    @Autowired
    private EventTaskService eventTaskService;

    @Override
    @Transactional
    public SurveyConfigDto createSurvey(SurveyConfigDto surveyConfigDto, User user, Event event){
        log.info("Start createSurvey | event {} | user {} ", event.getEventId(),user.getUserId());
        checkSurveyNameExistOrNotEmpty(surveyConfigDto.getSurveyName(),event.getEventId());
        checkSurveyHeadlineNotEmpty(surveyConfigDto.getSurveyHeadline(), event.getEventId());
        List<SurveyQuestionsDto> surveyQuestionsDtos = surveyConfigDto.getSurveyQuestions();
        if( surveyQuestionsDtos == null || surveyQuestionsDtos.isEmpty()){
            throw new NotAcceptableException(NotAcceptableException.SurveyException.ADD_ATLEAST_ONE_QUESTION);
        }
        SurveyConfiguration surveyConfiguration = surveyConfigDto.createEntity();
        surveyConfiguration.setEventId(event.getEventId());
        surveyConfiguration.setCreatedBy(user.getUserId());
        surveyConfiguration.setDefaultSurvey(surveyConfigDto.isDefaultSurvey());
        if(surveyConfigDto.isDefaultSurvey()) {
            unsetExistingDefaultSurvey(event.getEventId());
        }
        surveyConfiguration = surveyConfigRepoService.save(surveyConfiguration);
        log.info("Save survey configuration successfully | event {} | user {} | survey {}",event.getEventId(),user.getUserId(),surveyConfiguration.getSurveyId());
        List<SurveyQuestions> surveyQuestionsList = new ArrayList<>();
        for(SurveyQuestionsDto surveyQuestionsDto : surveyQuestionsDtos){
            SurveyQuestions surveyQuestions =surveyQuestionsDto.createEntity();
            surveyQuestions.setSurveyId(surveyConfiguration.getSurveyId());
            updateSurveyQuestionAllowedDescription(surveyQuestions,surveyQuestionsDto);
            surveyQuestions.setEventId(event.getEventId());
            surveyQuestionsList.add(surveyQuestions);
        }
        List<SurveyQuestions> surveyQuestionsListUpdated = surveyQuestionsRepoService.saveAll(surveyQuestionsList);
        surveyConfigDto.setSurveyId(surveyConfiguration.getSurveyId());
        mapSessionsToSurvey(surveyConfigDto, event, user);
        log.info("Save survey questions successfully | event {} | user {} | survey {}",event.getEventId(),user.getUserId(),surveyConfiguration.getSurveyId());
        return new SurveyConfigDto(surveyConfiguration,surveyQuestionsListUpdated);

    }

    private void unsetExistingDefaultSurvey(long eventId) {
        SurveyConfiguration existingDefaultSurveyByEventId = surveyConfigRepoService.findDefaultSurveyByEventId(eventId);
        if (existingDefaultSurveyByEventId != null) {
            existingDefaultSurveyByEventId.setDefaultSurvey(false);
            surveyConfigRepoService.save(existingDefaultSurveyByEventId);
        }
    }

    private void updateSurveyQuestionAllowedDescription(SurveyQuestions surveyQuestions, SurveyQuestionsDto surveyQuestionsDto) {
        if(AttributeValueType.NPS.name().equalsIgnoreCase(surveyQuestions.getType().toString()) || AttributeValueType.STAR_RATING.name().equalsIgnoreCase(surveyQuestions.getType().toString())) {
            surveyQuestions.setAllowedDescription(surveyQuestionsDto.isAllowedDescription());
            surveyQuestions.setOptions(null);
        } else if (AttributeValueType.DROPDOWN.name().equalsIgnoreCase(surveyQuestions.getType().toString())
                || AttributeValueType.MULTIPLE_CHOICE.name().equalsIgnoreCase(surveyQuestions.getType().toString())
                || AttributeValueType.SINGLE_CHECKBOX.name().equalsIgnoreCase(surveyQuestions.getType().toString())) {
            surveyQuestions.setAllowedDescription(false);
            surveyQuestions.setOptions(surveyQuestionsDto.getOptions());
        } else {
            surveyQuestions.setAllowedDescription(false);
            surveyQuestions.setOptions(null);
        }
    }

    private void checkSurveyNameExistOrNotEmpty(String surveyName, long eventId) {
        log.info("start checkSurveyNameExistOrNotEmpty");
        if(StringUtils.isNotEmpty(surveyName)) {
            log.info("check survey name is exist or empty | surveyName {} | event id {}", surveyName,eventId);
            if (surveyConfigRepoService.isSurveyNameIsExistInEvent(surveyName, eventId)) {
                throw new ConflictException(ConflictException.SurveyExceptionMsg.SURVEY_NAME_ALREADY_EXIST);
            }
        }
        else{
            throw new NotAcceptableException(NotAcceptableException.SurveyException.SURVEY_NAME_CANNOT_BE_EMPTY);
        }
    }

    private void checkSurveyHeadlineNotEmpty(String surveyHeadline, long eventId) {
        log.info("start checkSurveyHeadlineNotEmpty");
        if(StringUtils.isEmpty(surveyHeadline)) {
            throw new NotAcceptableException(NotAcceptableException.SurveyException.SURVEY_HEADLINE_CANNOT_BE_EMPTY);
        }
    }


    @Override
     public SurveyConfigDto getSurvey(long id,User user, Event event){
        log.info("get Survey | event {} | id {}", event.getEventId(),id);
        if(!surveyConfigRepoService.isSurveyExist(event.getEventId(),id)){
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
        Optional<SurveyConfiguration> surveyConfigurationOptional = surveyConfigRepoService.findById(id);
        if(surveyConfigurationOptional.isPresent()) {
            Map<Long, String> surveys = surveyConfigRepoService.getAllSurveysByEvent(event.getEventId()).stream().collect(Collectors.toMap(SurveysDto::getSurveyId, SurveysDto::getSurveyName, (oldValue, newValue) -> oldValue));

            List<SurveySessionsBasicDto> eventSessions = sessionRepoService.getSessionSurveyBasicDetailsByEvent(event.getEventId());
            for (SurveySessionsBasicDto session : eventSessions) {
                if(session.isSurveyEnabled() && NumberUtils.isNumberGreaterThanZero(session.getSurveyId()) && surveys.get(session.getSurveyId()) != null) {
                    session.setSurveyName(surveys.get(session.getSurveyId()));
                }
            }
            SurveyConfiguration surveyConfiguration = surveyConfigurationOptional.get();
            checkLoginRequiredFlagAndUserAuth(surveyConfiguration,user);
            List<SurveyQuestions> surveyQuestionsList = surveyQuestionsRepoService.findBySurveyId(id);
            SurveyConfigDto surveyConfigDto = new SurveyConfigDto(surveyConfiguration, surveyQuestionsList);
            surveyConfigDto.setDefaultSurvey(surveyConfiguration.isDefaultSurvey());
            surveyConfigDto.setSessions(eventSessions);
            surveyConfigDto.setSurveySessions(eventSessions.stream().filter(session -> session.isSurveyEnabled() && NumberUtils.isNumberGreaterThanZero(session.getSurveyId()) && session.getSurveyId() == id).map(session -> new SurveySessionsDto(session.getSessionId(), session.getSessionTitle())).collect(Collectors.toList()));
            return surveyConfigDto;
        }
        else{
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
    }

    @Override
    public void deleteSurvey(long id, User user, Event event) {
        log.info("delete Survey | event {} | user {} | id {}", event.getEventId(), user.getUserId(), id);
        Optional<SurveyConfiguration> surveyConfigurationOptional = surveyConfigRepoService.findById(id);
        if (surveyConfigurationOptional.isPresent()) {
            List<Session> sessionsBySurveyId = sessionRepoService.findSessionsBySurveyIdAndEventId(id, event.getEventId());
            if(!CollectionUtils.isEmpty(sessionsBySurveyId)) {
                for(Session session : sessionsBySurveyId) {
                    session.setSurveyEnabled(false);
                    session.setSurveyId(null);
                }
                sessionRepoService.saveAll(sessionsBySurveyId);
            }
            SurveyConfiguration surveyConfiguration = surveyConfigurationOptional.get();
            surveyConfiguration.setRecordStatus(RecordStatus.DELETE);
            surveyConfigRepoService.save(surveyConfiguration);
            challengeService.removeTriggerFromChallengeOnDelete(surveyConfiguration.getSurveyId(),RedisChallengeAreaDTO.Survey.survey.name(),event);
            log.info("delete survey successfully | event {} | user {} | survey {}", event.getEventId(), user.getUserId(), id);

        } else {
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
    }

    @Override
    public DataTableResponse getSurveys(User user, Event event, PageSizeSearchObj pageSizeSearchObj, boolean submission) {
        log.info("Getting List of Surveys of event {} by user {} with search string {} ", event.getEventId(),user.getUserId(), pageSizeSearchObj.getSearchWithEscapeSpecialChars());
        Page<SurveyConfiguration> surveyConfigurationPage;
        Sort.Direction direction = pageSizeSearchObj.getSortDirection() != null ? pageSizeSearchObj.getSortDirection(): Sort.Direction.ASC;
        if(StringUtils.isNotBlank(pageSizeSearchObj.getSearchWithEscapeSpecialChars())) {
            surveyConfigurationPage = surveyConfigRepoService.findByEventIdAndSearchString(event.getEventId(),pageSizeSearchObj.getSearchWithEscapeSpecialChars(), PageRequest.of(pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize(), direction, "createdDate"));
        } else {
            surveyConfigurationPage = surveyConfigRepoService.findByEventId(event.getEventId(), PageRequest.of(pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize(),direction, "createdDate"));
        }
        Map<Long, List<Session>> sessionsBySurveyId = sessionRepoService.findSessionByEventId(event).stream().filter(s -> s.isSurveyEnabled() && NumberUtils.isNumberGreaterThanZero(s.getSurveyId()))
                .collect(Collectors.groupingBy(Session::getSurveyId, Collectors.toList()));

        List<SurveyConfiguration> surveyConfigurations = surveyConfigurationPage.getContent().stream().distinct().collect(Collectors.toList());
        List<SurveysDto> surveysDtos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(surveyConfigurations)) {
            Map<Long, Long> countBySurveys = new HashMap<>();
            if(submission) {
               List<SurveysDto>  surveysDtoList = surveyResponseRepoService.findResponseCountByEventId(event.getEventId());
               surveysDtoList.forEach(e ->countBySurveys.put(e.getSurveyId(),e.getSubmissions()));

            }
            for (SurveyConfiguration surveyConfiguration : surveyConfigurations) {
                SurveysDto surveysDto = new SurveysDto(surveyConfiguration);
                surveysDto.setSubmissions(countBySurveys.getOrDefault(surveyConfiguration.getSurveyId(),0L));
                surveysDtos.add(surveysDto);
                if (!CollectionUtils.isEmpty(sessionsBySurveyId.get(surveyConfiguration.getSurveyId()))) {
                    List<SurveySessionsDto> surveySessionsDto = sessionsBySurveyId.get(surveyConfiguration.getSurveyId()).stream()
                            .map(session -> new SurveySessionsDto(session.getId(), session.getTitle()))
                            .collect(Collectors.toList());
                    surveysDto.setSurveySessions(surveySessionsDto);
                }
            }
        }
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(surveysDtos);
        dataTableResponse.setRecordsTotal(surveyConfigurationPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(surveyConfigurations.size());
        return dataTableResponse;
    }

    @Override
    public SurveyConfigDto updateSurvey(SurveyConfigDto surveyConfigDto, User user, Event event, long id) {
        log.info("updateSurvey | event {} | user {} | surveyConfigDto {}", event.getEventId(), user.getUserId(), surveyConfigDto);
        Optional<SurveyConfiguration> surveyConfigurationOptional = surveyConfigRepoService.findById(id);
        List<SurveyQuestionsDto> surveyQuestionsDtos = surveyConfigDto.getSurveyQuestions();
        if( surveyQuestionsDtos == null || surveyQuestionsDtos.isEmpty()){
            throw new NotAcceptableException(NotAcceptableException.SurveyException.ADD_ATLEAST_ONE_QUESTION);
        }
        SurveyConfiguration surveyConfiguration;
        if (surveyConfigurationOptional.isPresent()) {
            if(!surveyConfigDto.getSurveyName().equalsIgnoreCase(surveyConfigurationOptional.get().getSurveyName())) {
                checkSurveyNameExistOrNotEmpty(surveyConfigDto.getSurveyName(), event.getEventId());
            }
            surveyConfiguration = surveyConfigDto.updateEntity(surveyConfigurationOptional.get());
            if(surveyConfigDto.isDefaultSurvey()) {
                unsetExistingDefaultSurvey(event.getEventId());
            }
            surveyConfiguration.setDefaultSurvey(surveyConfigDto.isDefaultSurvey());
            surveyConfiguration.setRecordStatus(RecordStatus.UPDATE);
            surveyConfiguration = surveyConfigRepoService.save(surveyConfiguration);

            mapSessionsToSurvey(surveyConfigDto, event, user);

            log.info("Save updated survey configuration successfully | event {} | user {} | survey {}", event.getEventId(), user.getUserId(), surveyConfiguration.getSurveyId());
        }
        else{
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }

        List<SurveyQuestions> surveyQuestionsList = new ArrayList<>();
        List<Long> oldQuestionsList = surveyQuestionsRepoService.findIdsBySurveyId(surveyConfiguration.getSurveyId());
        updateSurveyQuestions(surveyQuestionsList,surveyConfiguration,event,oldQuestionsList,surveyQuestionsDtos);
        List<SurveyQuestions> surveyQuestionsListUpdated = surveyQuestionsRepoService.saveAll(surveyQuestionsList);
        log.info("Save survey questions successfully | event {} | user {} | survey {}",event.getEventId(),user.getUserId(),surveyConfiguration.getSurveyId());
        return new SurveyConfigDto(surveyConfiguration,surveyQuestionsListUpdated);
    }

    @Override
    public void submitSurvey(long id, User user, Event event, SurveyResponseDto surveyResponseDto,String role) {
        log.info("Submit Survey | event {} | user {} | Survey Id {}", event.getEventId(), user.getUserId(), id);
        List<String> userRoles = roStaffService.getAllUserRoles(event.getEventId(), user.getUserId());
        log.info("Submit Survey | event {} | user {} | Survey Id {} | user roles {}", event.getEventId(), user.getUserId(), id, userRoles);
        SurveyConfiguration surveyConfiguration = surveyConfigRepoService.findById(id)
                .orElseThrow(() -> new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND));
        boolean isSurveyRequiresCheckIn =  roEventLevelSettingService.isSurveyRequiresCheckin(event.getEventId());

        if (surveyConfiguration.isLoginRequired() && CollectionUtils.isEmpty(userRoles) && org.springframework.util.StringUtils.isEmpty(role)) {
            log.info("Not registered for this event {} | user {} | survey {}", event.getEventId(), user.getUserId(),id);
            NotAcceptableException.AttendeeExceptionMsg exception = NotAcceptableException.AttendeeExceptionMsg.NOT_REGISTERED_FOR_THIS_EVENT;
            String developerMessage = Constants.NOT_REGISTERED_FOR_THIS_EVENT
                    .replace(Constants.EMAIL_ADDRESS_PARAMETER, user.getEmail());
            exception.setErrorMessage(user.getEmail());
            exception.setDeveloperMessage(developerMessage);
            throw new NotAcceptableException(exception);
        }
        validateSurveyExistence(id,event.getEventId());

        if(NumberUtils.isNumberGreaterThanZero(surveyResponseDto.getSessionId())){
            if(isSurveyRequiresCheckIn){
                Boolean isUserCheckinInSession = roUserSessionService.checkIfUserIsCheckinInSession(user.getUserId(),event.getEventId(),surveyResponseDto.getSessionId());
                if(!isUserCheckinInSession){
                    throw new NotAcceptableException(NotAcceptableException.SurveyException.SURVEY_SUBMISSION_IS_ONLY_ALLOWED_AFTER_SESSION_CHECKIN);
                }
            }
            if(surveyResponseRepoService.findBySurveyIdAndUserIdAndSessionId(id,user.getUserId(), surveyResponseDto.getSessionId())){
                throw new ConflictException(ConflictException.SurveyExceptionMsg.SURVEY_RESPONSE_ALREADY_SUBMITTED);
            }
        }
        else{
            if(surveyResponseRepoService.findBySurveyIdAndUserId(id,user.getUserId())){
                throw new ConflictException(ConflictException.SurveyExceptionMsg.SURVEY_RESPONSE_ALREADY_SUBMITTED);
            }
        }
        if (surveyConfiguration != null) {
            log.info("Save survey response | event {} | user {} | survey {}", event.getEventId(), user.getUserId(), id);
            SurveyResponse surveyResponse = prepareSurveyResponse(user.getUserId(),event,id,surveyResponseDto);
            String jsonValue = prepareSurveyQuestionsAnswers(user.getUserId(),surveyResponseDto.getSurveyQuestionsKeyValueDtos());
            int calculatedSurveyScore = this.getCalculatedSurveyScore(surveyConfiguration.getSurveyId(), surveyConfiguration.isSurveyQuizEnabled(), surveyResponseDto.getSurveyQuestionsKeyValueDtos(), user.getUserId());
            log.info("Save survey response | event {} | user {} | survey {} | jsonValue {}", event.getEventId(), user.getUserId(), id, jsonValue);
            surveyResponse.setJsonValue(jsonValue);
            surveyResponse.setUserQuizScore(calculatedSurveyScore);
            surveyResponseRepoService.save(surveyResponse);

            trayTrackingService.submitSurvey(event, user,surveyResponseDto);
            eventTaskService.autoCompleteSystemGeneratedTasks(id, event.getEventId(), user.getUserId());
        }
    }

    @Override
    public void submitSurveyThroughAnonymousUser(long id, String anonymousUserUuid, Event event, SurveyResponseDto surveyResponseDto) {
        log.info("Submit Survey | event {} | anonymousUserUuid {} | Survey Id {}", event.getEventId(),anonymousUserUuid,id);
        checkAnonymousUserUuidIsNotBlank(anonymousUserUuid);
        validateSurveyExistence(id,event.getEventId());
        if(surveyResponseRepoService.findBySurveyIdAndAnonymousUuidAndEventId(id,anonymousUserUuid,event.getEventId())){
            throw new ConflictException(ConflictException.SurveyExceptionMsg.SURVEY_RESPONSE_ALREADY_SUBMITTED);
        }
        Optional<SurveyConfiguration> surveyConfigurationOptional = surveyConfigRepoService.findById(id);
        if (surveyConfigurationOptional.isPresent()) {
            log.info("Save survey response | event {} | | anonymousUserUuid {} | survey {}", event.getEventId(), anonymousUserUuid, id);
            SurveyResponse surveyResponse = prepareSurveyResponse(0L,event,id,surveyResponseDto);
            surveyResponse.setAnonymousUserUuid(anonymousUserUuid);
            surveyResponse.setJsonValue(JsonMapper.convertToString(surveyResponseDto.getSurveyQuestionsKeyValueDtos()));
            log.info("Save survey response | event {} | anonymousUserUuid {} | survey {} | jsonValue {}", event.getEventId(),anonymousUserUuid, id,surveyResponse.getJsonValue());
            surveyResponseRepoService.save(surveyResponse);
        }
        else{
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
    }

    private void validateSurveyExistence(long id, Long eventId) {
        if (!surveyConfigRepoService.isSurveyExist(eventId,id)) {
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
    }

    private void checkAnonymousUserUuidIsNotBlank(String anonymousUserUuid) {
        if (StringUtils.isBlank(anonymousUserUuid)) {
            throw new NotAcceptableException(NotAcceptableException.SurveyException.ANONYMOUS_USER_UUID_CANNOT_BE_BLANK);
        }
    }
    private SurveyResponse prepareSurveyResponse(long userId, Event event, long surveyId, SurveyResponseDto surveyResponseDto) {
        SurveyResponse surveyResponse = new SurveyResponse();
        surveyResponse.setUserId(userId);
        surveyResponse.setEventId(event.getEventId());
        surveyResponse.setSurveyId(surveyId);
        surveyResponse.setSubmissionDate(new Date());
        surveyResponse.setTrigger(surveyResponseDto.getTrigger());
        surveyResponse.setSessionId(surveyResponseDto.getSessionId());
        surveyResponseDto.getSurveyQuestionsKeyValueDtos().forEach(e->{
            if(DATE.equalsIgnoreCase(e.getType())){
                e.setValue(StringUtils.isNotBlank(e.getValue()) ? getDateInUTC(e.getValue(), event.getEquivalentTimeZone()).toString() : null);
            }
        });
        checkSurveyQuestionsWithConfiguration(surveyId,surveyResponseDto.getSurveyQuestionsKeyValueDtos());
        return surveyResponse;
    }

    public int getCalculatedSurveyScore(Long surveyId, boolean isSurveyQuizEnabled, List<SurveyQuestionsKeyValueDto> surveyQuestionsKeyValueDtoList, Long userId) {
        log.info("Calculating survey score for survey Id=> {} userId=>{} quizableSurvey {}", surveyId, userId, isSurveyQuizEnabled);
        int scorePercentage = 0;
        if (surveyId > 0 && isSurveyQuizEnabled) {
            List<SurveyQuestions> dropDownQuestionList = surveyQuestionsRepoService.findDropDownSurveyQuestionsBySurveyIdAndIsUnScoredFalse(surveyId);
            int totalDropdownQuestion = dropDownQuestionList.size();

            if (!CollectionUtils.isEmpty(dropDownQuestionList)) {
                Map<Long, SurveyQuestionsKeyValueDto> dropDownQuestionResponseMap = surveyQuestionsKeyValueDtoList.stream()
                        .filter(dto -> AttributeValueType.DROPDOWN.name().equals(dto.getType()))
                        .collect(Collectors.toMap(SurveyQuestionsKeyValueDto::getQuestionId, dto -> dto));

                long totalUserCorrectAnswers = dropDownQuestionList.stream()
                        .filter(question -> {
                            SurveyQuestionsKeyValueDto userQuestionAnswerResponse = dropDownQuestionResponseMap.get(question.getId());
                            if (userQuestionAnswerResponse != null) {
                                String correctAnswer = getDropDownQuestionCorrectAnswer(question.getOptions());
                                return StringUtils.isNotBlank(correctAnswer) && correctAnswer.equals(userQuestionAnswerResponse.getValue());
                            }
                            return false;
                        })
                        .count();
                scorePercentage = this.calculateScorePercentage(totalUserCorrectAnswers, totalDropdownQuestion);
            }
        }
        log.info("Calculated Score for survey Id {} userId {} score {}", surveyId, userId, scorePercentage);
        return scorePercentage;
    }

    private int calculateScorePercentage(long correctAnswers, int totalQuestions) {
        if (totalQuestions == 0) {
            return 0;
        }
        BigDecimal totalScore = BigDecimal.valueOf(correctAnswers)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalQuestions), RoundingMode.HALF_UP);
        return totalScore.intValue();
    }

    private String getDropDownQuestionCorrectAnswer(String optionsString) {
        List<Map<String, Object>> optionsList = JsonMapper.stringToObjectWithTypeReference(optionsString, JsonMapper.listOfMapTypeReference);
        if (CollectionUtils.isEmpty(optionsList)) {
            return STRING_EMPTY;
        }
        return optionsList.stream()
                .filter(option -> Boolean.TRUE.equals(option.get(IS_CORRECT_ANSWER)))
                .map(option -> String.valueOf(option.getOrDefault(VALUE_SMALL, STRING_EMPTY)))
                .findFirst()
                .orElse(STRING_EMPTY);
    }


    @Override
    public void downloadSurveysCsv(User user, Event event, HttpServletResponse response) {
        log.info("Start Download surveys csv | event {} | user {} ", event.getEventId(), user.getUserId());
        List<SurveyConfiguration> surveyConfigurations = surveyConfigRepoService.findSurveysByEventId(event.getEventId());
        if (surveyConfigurations.isEmpty()) {
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
        List<SurveyConfigDto> surveyConfigDtos = new ArrayList<>();
        List<SurveyQuestions> surveyQuestionsList = surveyQuestionsRepoService.findByEventId(event.getEventId());
        surveyConfigurations.forEach(surveyConfiguration -> {
            List<SurveyQuestions> surveyQuestionsListUpdated = new ArrayList<>();
            surveyQuestionsList.forEach(surveyQuestion -> {
                if(surveyConfiguration.getSurveyId() == surveyQuestion.getSurveyId()) {
                    surveyQuestionsListUpdated.add(surveyQuestion);
                }
            });
            surveyConfigDtos.add(new SurveyConfigDto(surveyConfiguration, surveyQuestionsListUpdated));
        });

        //submission data fetch for all the surveys
        Map<Long, Long> countBySurveys = new HashMap<>();
        List<SurveysDto> surveysDtoList = surveyResponseRepoService.findResponseCountByEventId(event.getEventId());
        surveysDtoList.forEach(e ->countBySurveys.put(e.getSurveyId(), e.getSubmissions()));
        log.info("Prepared data for the Survey CSV file, user {} | event {}", user.getUserId(),event.getEventId());
        downloadService.downloadAllSurveys(user, event, surveyConfigDtos, countBySurveys, response);


    }

    @Override
    public void downloadSubmissionsCsv(User user, Event event, long id, HttpServletResponse response) {
        log.info("Start Download submissions csv | event {} | user {} | survey {} ", event.getEventId(),user.getUserId(),id);
        Optional<SurveyConfiguration> surveyConfigurationOptional = surveyConfigRepoService.findById(id);
        if(!surveyConfigurationOptional.isPresent()){
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
        List<SurveyResponse> surveyResponses = surveyResponseRepoService.findAllBySurveyId(id);
        log.info("Prepared data for the Survey submission CSV file, user {} | event {} | survey response size {}", user.getUserId(),event.getEventId(), surveyResponses.size());
        downloadService.downloadSurveySubmissions(surveyResponses,surveyConfigurationOptional.get(),user,event, response);

    }

    @Override
    public void downloadSurveySubmissionsCsv(User user, Event event, String ids, HttpServletResponse response) {
        log.info("Start Download submissions csv | event {} | user {} ", event.getEventId(),user.getUserId());
        List<Long> surveyIds = convertCommaSeparatedToListLong(ids);
        List<SurveysDto> surveysDtoList = surveyConfigRepoService.getSurveysByIds(surveyIds);
        if (surveysDtoList.isEmpty()) {
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
        downloadService.downloadSurveySubmissionsData(surveysDtoList,event, response);
    }

    @Override
    public boolean isSurveySubmittedByUser(long id, User user, Event event, Long sessionId) {
        log.info("Check the is survey submitted by user user {} | event {} | survey {}", user.getUserId(),event.getEventId(),id);
        if(NumberUtils.isNumberGreaterThanZero(sessionId)){
            return surveyResponseRepoService.findBySurveyIdAndUserIdAndSessionId(id,user.getUserId(), sessionId);
        }
        else{
            return surveyResponseRepoService.findBySurveyIdAndUserId(id,user.getUserId());
        }

    }

    @Override
    public boolean isSurveySubmittedByAnonymousUser(long id, Event event, String anonymousUserUuid) {
        checkAnonymousUserUuidIsNotBlank(anonymousUserUuid);
        log.info("Check the is survey submitted by anonymous user uuid anonymousUserUuid  {} | event {} | survey {}", anonymousUserUuid,event.getEventId(),id);
        return surveyResponseRepoService.findBySurveyIdAndAnonymousUuidAndEventId(id,anonymousUserUuid,event.getEventId());
    }

    private void checkSurveyQuestionsWithConfiguration(long id, List<SurveyQuestionsKeyValueDto> surveyQuestionsKeyValueDtos) {
        log.info("check survey questions with configuration | survey id {} | surveyQuestionsKeyValueDtos {}",id,surveyQuestionsKeyValueDtos.size());
        List<SurveyQuestions> surveyQuestions = surveyQuestionsRepoService.findBySurveyIdAndIsShowFalse(id);
        List<Long> surveyConfigureQuestionsIds = new ArrayList<>();
        List<Long> requiredQuestionsIds = new ArrayList<>();
        surveyQuestions.forEach(surveyQuestion -> {
            surveyConfigureQuestionsIds.add(surveyQuestion.getId());
                    if(surveyQuestion.isRequired()){
                        requiredQuestionsIds.add(surveyQuestion.getId());
                    }
                }
        );
        if(surveyQuestions.size() != surveyQuestionsKeyValueDtos.size()) {
            throw new NotAcceptableException(NotAcceptableException.SurveyException.SURVEY_QUESTIONS_NOT_MATCHED);
        }
            surveyQuestionsKeyValueDtos.forEach(e -> {
                if(requiredQuestionsIds.contains(e.getQuestionId()) && StringUtils.isEmpty(e.getValue())){
                    throw new NotAcceptableException(NotAcceptableException.SurveyException.ANSWER_THE_REQUIRED_QUESTIONS);
                }

               if(!(AttributeValueType.STAR_RATING.name().equalsIgnoreCase(e.getType()) || AttributeValueType.NPS.name().equalsIgnoreCase(e.getType()))){
                   e.setDescription(Constants.STRING_EMPTY);
               }
                if(!surveyConfigureQuestionsIds.contains(e.getQuestionId())){
                    throw new NotFoundException(NotFoundException.SurveyNotFound.QUESTION_NOT_FOUND_IN_SURVEY);
                }
            });
    }


    public String prepareSurveyQuestionsAnswers(Long userId,List<SurveyQuestionsKeyValueDto> surveyQuestionsKeyValueDtos){
        User user = userService.findUserByIdWithoutCache(userId);
        if(user != null) {
            surveyQuestionsKeyValueDtos.add(new SurveyQuestionsKeyValueDto(Constants.EMAIL,user));
            surveyQuestionsKeyValueDtos.add(new SurveyQuestionsKeyValueDto(Constants.FIRST_NAME,user));
            surveyQuestionsKeyValueDtos.add(new SurveyQuestionsKeyValueDto(Constants.LAST_NAME,user));
        }
        return JsonMapper.convertToString(surveyQuestionsKeyValueDtos);
    }

    public void deleteSurveyQuestions(long id, List<Long> ids, Event event) {
        log.info("delete survey question | event {} | survey id {} | questions id {}", event.getEventId(), id, ids);
        List<SurveyQuestions> surveyQuestions = surveyQuestionsRepoService.findByIds(ids);

        /* if question deleted after the submission of survey then we are not showing that question to users but not marked that question as DELETE
        Need to show that question answer in submission CSV file.
         */
        if(surveyResponseRepoService.findCountBySurveyId(id) > 0){
            surveyQuestions.forEach(e-> e.setShow(false));
        }
        else {
            surveyQuestions.forEach(e -> {
                e.setShow(false);
                e.setRecordStatus(RecordStatus.DELETE);
            });
        }
        surveyQuestionsRepoService.saveAll(surveyQuestions);
        log.info("delete surveyquestions successfully | event {} | survey {}", event.getEventId(), id);
    }

    private void updateSurveyQuestions(List<SurveyQuestions> surveyQuestionsList, SurveyConfiguration surveyConfiguration,Event event,List<Long> oldQuestionsList,List<SurveyQuestionsDto> surveyQuestionsDtos) {
        log.info("update survey questions | event {} | survey {} | old questions {} | new questions {}",event.getEventId(),surveyConfiguration.getSurveyId(), oldQuestionsList, surveyQuestionsList);
        for (SurveyQuestionsDto newQuestionDto : surveyQuestionsDtos){
            if(newQuestionDto.getId() == null){
                SurveyQuestions newSurveyQuestion = newQuestionDto.createEntity();
                newSurveyQuestion.setSurveyId(surveyConfiguration.getSurveyId());
                updateSurveyQuestionAllowedDescription(newSurveyQuestion,newQuestionDto);
                newSurveyQuestion.setEventId(event.getEventId());
                surveyQuestionsList.add(newSurveyQuestion);
                log.info("added new survey questions successfully | event {} | survey {}",event.getEventId(),surveyConfiguration.getSurveyId());
            }else {
                    if (oldQuestionsList.contains(newQuestionDto.getId())) {
                        Optional<SurveyQuestions> newSurveyQuestionOptional = surveyQuestionsRepoService.findById(newQuestionDto.getId());
                        boolean flag = true;
                        if(newSurveyQuestionOptional.isPresent()) {
                            SurveyQuestions newSurveyQuestions = newSurveyQuestionOptional.get();
                            if(surveyResponseRepoService.findCountBySurveyId(surveyConfiguration.getSurveyId()) > 0 && !newSurveyQuestions.getType().equals(newQuestionDto.getType())) {
                                    SurveyQuestions newSurveyQuestion = newQuestionDto.createEntity();
                                    newSurveyQuestion.setSurveyId(surveyConfiguration.getSurveyId());
                                    updateSurveyQuestionAllowedDescription(newSurveyQuestion,newQuestionDto);
                                    newSurveyQuestion.setEventId(event.getEventId());
                                    surveyQuestionsList.add(newSurveyQuestion);
                                    flag = false;
                            }

                            SurveyQuestions newSurveyQuestion = newQuestionDto.updateEntity(newSurveyQuestionOptional.get(),flag);
                            updateSurveyQuestionAllowedDescription(newSurveyQuestion,newQuestionDto);
                            newSurveyQuestion.setRecordStatus(RecordStatus.UPDATE);
                            surveyQuestionsList.add(newSurveyQuestion);
                            log.info("updated survey questions successfully | event {} | survey {} | question {}",event.getEventId(),surveyConfiguration.getSurveyId(),newQuestionDto.getId());
                            oldQuestionsList.remove(newQuestionDto.getId());
                        }
                    }
            }
        }
        if(!oldQuestionsList.isEmpty()){
            deleteSurveyQuestions(surveyConfiguration.getSurveyId(), oldQuestionsList, event);
        }

    }

    @Override
    public List<SurveyQuestionsKeyValueDto> getSurveyResponseBySurveyIdAndUserId(long surveyId, User user, Event event) {
        log.info("get survey response by survey id and userId | event {} | survey {} | user {}",event.getEventId(),surveyId,user.getUserId());
        Optional<SurveyResponse> surveyResponses = surveyResponseRepoService.findFirstSurveyResponseBySurveyIdAndUserId(surveyId,user.getUserId());
        List<SurveyQuestionsKeyValueDto> surveyResponseDtos = new ArrayList<>();
        surveyResponses.ifPresent(e -> surveyResponseDtos.addAll(JsonMapper.parseJsonArray(e.getJsonValue(), SurveyQuestionsKeyValueDto.class)));
        log.info("get survey response by survey id successfully | event {} | survey {} | user {}",event.getEventId(),surveyId,user.getUserId());
        return surveyResponseDtos;
    }

    @Override
    public List<SurveyQuestionsKeyValueDto> getSurveyResponseBySurveyIdAndAnonymousUserUuid(long surveyId, String anonymousUserUuid, Event event) {
        log.info("get survey response by survey id and Anonymous User | event {} | survey {} | anonymous user uuid {}",event.getEventId(),surveyId,anonymousUserUuid);
        checkAnonymousUserUuidIsNotBlank(anonymousUserUuid);
        Optional<SurveyResponse> surveyResponses = surveyResponseRepoService.findFirstSurveyResponseBySurveyIdAndAnonymousUserUuid(surveyId,anonymousUserUuid);
        List<SurveyQuestionsKeyValueDto> surveyResponseDtos = new ArrayList<>();
        surveyResponses.ifPresent(e -> surveyResponseDtos.addAll(JsonMapper.parseJsonArray(e.getJsonValue(), SurveyQuestionsKeyValueDto.class)));
        return surveyResponseDtos;
    }

    private void checkLoginRequiredFlagAndUserAuth(SurveyConfiguration surveyConfiguration,User user){
        if(surveyConfiguration.isLoginRequired() && user == null){
            throw new AuthorizationException(NOT_AUTHORIZE);
        }
    }

    @Override
    public List<SurveysDto> getAllSurveysByEvent(User user, Event event){
        log.info("Getting List of Surveys of event {} by user {}",event.getEventId(),user.getUserId());
        return surveyConfigRepoService.getAllSurveysByEvent(event.getEventId());
    }

    @Override
    public void duplicateSurveys(List<Long> surveyIds, Event event, long count) {
        if (surveyIds.size() != 1) {
            throw new NotFoundException(NotFoundException.SurveyNotFound.CAN_NOT_DUPLICATE_MORE_THEN_ONE_SURVEY);
        }
        Optional<SurveyConfiguration> surveyConfigurationOpt = surveyConfigRepoService.findById(surveyIds.get(0));
        if (surveyConfigurationOpt.isPresent()) {
            SurveyConfiguration oldSurveyConfiguration = surveyConfigurationOpt.get();
            List<SurveyQuestions> oldSurveyQuestions = surveyQuestionsRepoService.findBySurveyId(oldSurveyConfiguration.getSurveyId());
            List<SurveyConfiguration> newSurveyList = duplicateSurvey(oldSurveyConfiguration, count, event.getEventId());
            List<SurveyQuestions> newSurveyQuestions = new ArrayList<>();
            newSurveyList.forEach(newSurveyConfiguration -> {
                oldSurveyQuestions.forEach(surveyQuestion -> {
                    SurveyQuestions surveyQuestions = (SurveyQuestions) surveyQuestion.clone();
                    surveyQuestions.setId(0L);
                    surveyQuestions.setSurveyId(newSurveyConfiguration.getSurveyId());
                    newSurveyQuestions.add(surveyQuestions);
                });
            });
            surveyQuestionsRepoService.saveAll(newSurveyQuestions);
        } else {
            throw new NotFoundException(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND);
        }
    }

    private List<SurveyConfiguration> duplicateSurvey(SurveyConfiguration oldSurveyConfiguration, long count, long eventId) {

        List<String> newSurveyNames = new ArrayList<>();
        List<SurveyConfiguration> newSurveyList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            SurveyConfiguration newSurveyConfiguration = (SurveyConfiguration) oldSurveyConfiguration.clone();
            newSurveyConfiguration.setSurveyId(0);
            String newSurveyName = getCopySurveyName(oldSurveyConfiguration.getSurveyName(), eventId, newSurveyNames);
            newSurveyNames.add(newSurveyName);
            newSurveyConfiguration.setSurveyName(newSurveyName);
            newSurveyConfiguration.setCreatedDate(new Date());
            newSurveyConfiguration.setUpdateDate(new Date());
            newSurveyConfiguration.setDefaultSurvey(false);
            newSurveyList.add(newSurveyConfiguration);
        }
        newSurveyList = surveyConfigRepoService.saveAll(newSurveyList);
        return newSurveyList;
    }

    protected String getCopySurveyName(String name, Long eventId, List<String> newSurveyNames) {
        int countOfTry = 1;
        String nameOrUrl = name;
        do {
            nameOrUrl = "COPY" + nameOrUrl;
            nameOrUrl = nameOrUrl.replace("COPYCOPY", "COPY");
            nameOrUrl = incrementLastNumber(nameOrUrl, countOfTry);
            countOfTry++;
        } while (surveyConfigRepoService.isSurveyNameIsExistInEvent(nameOrUrl, eventId) || newSurveyNames.contains(nameOrUrl));
        return nameOrUrl;
    }
    public static String incrementLastNumber(String str,int countOfTry) {
        if (str.matches(DUPLICATE_SURVEY_REG)) {
            String number = str.replaceAll(DUPLICATE_SURVEY_REG, "$1");
            int num = Integer.parseInt(number);
            int countDigits = countDigits(num);
            num++; // Increment number

            // Replace only last occurrence of number
            int lastIndex = str.lastIndexOf(number);
            String newString = "";
            if (lastIndex > 50) {
                newString = str.substring(0, (50 - countDigits)) + num ;
            } else {
                newString = str.substring(0, lastIndex) + num + str.substring(lastIndex + number.length());
            }
            return newString;
        } else {
            // If no number found, add 1 at end
            if (str.length() > 49) {
                return str.substring(0, 49) + countOfTry;
            }
            return str + countOfTry;
        }
    }
    public static int countDigits(int num) {
        int count = 0;

        // If number is 0, it has 1 digit
        if(num == 0) {
            return 1;
        }

        while(num != 0) {
            num = num / 10; // Remove last digit
            count++;        // Increase count
        }
        return count;
    }

    private void mapSessionsToSurvey(SurveyConfigDto surveyConfigDto, Event event, User user) {
        log.info("Request received to associate survey {} to session {} for event by user {}", surveyConfigDto.getSurveyId(), surveyConfigDto.getSurveySessions(), user.getUserId());
        List<Long> sessionId = surveyConfigDto.getSurveySessions().stream().map(SurveySessionsDto::getSessionId).collect(Collectors.toList());
        List<Session> sessions = sessionRepoService.findSessionByEventId(event);
        if(!CollectionUtils.isEmpty(sessions)) {
            sessions.stream().filter(session -> session.isSurveyEnabled() && session.getSurveyId().equals(surveyConfigDto.getSurveyId())).forEach(e->{
                e.setSurveyEnabled(false);
                e.setSurveyId(null);
            });

            sessions.forEach(session -> {
                if(sessionId.contains(session.getId())){
                    session.setSurveyEnabled(true);
                    session.setSurveyId(surveyConfigDto.getSurveyId());
                }
            });
            sessionRepoService.saveAll(sessions);
            log.info("Survey {} associated to session {} for event by user {}", surveyConfigDto.getSurveyId(), sessionId, user.getUserId());
        }
    }

    @Override
    public List<SurveySessionsBasicDto> getSessionsByEvent(Event event) {
        Map<Long, String> surveys = surveyConfigRepoService.getAllSurveysByEvent(event.getEventId()).stream().collect(Collectors.toMap(SurveysDto::getSurveyId, SurveysDto::getSurveyName, (oldValue, newValue) -> oldValue));

        List<SurveySessionsBasicDto> eventSessions = sessionRepoService.getSessionSurveyBasicDetailsByEvent(event.getEventId());
        for (SurveySessionsBasicDto session : eventSessions) {
            if(session.isSurveyEnabled() && NumberUtils.isNumberGreaterThanZero(session.getSurveyId()) && surveys.get(session.getSurveyId()) != null) {
                session.setSurveyName(surveys.get(session.getSurveyId()));
            }
        }
        return eventSessions;
    }

    @Override
    public SurveysDto getDefaultSurvey(User user, Event event){
        SurveyConfiguration defaultSurveyByEventId = surveyConfigRepoService.findDefaultSurveyByEventId(event.getEventId());
        SurveysDto surveysDto = null;
        if(!ObjectUtils.isEmpty(defaultSurveyByEventId)) {
            surveysDto = new SurveysDto(defaultSurveyByEventId);
        }
        return surveysDto;
    }
}
