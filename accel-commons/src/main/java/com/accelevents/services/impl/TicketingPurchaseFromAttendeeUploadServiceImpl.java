package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.repo.EventPlanConfigRepoService;
import com.accelevents.billing.chargebee.repo.OrganizerRepoService;
import com.accelevents.billing.chargebee.service.ChargebeeAttendeeUploadService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TemplateId;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.repositories.OrganizerRepository;
import com.accelevents.repositories.WhiteLabelRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.schedulers.BuyTicketsFromAttendeeUpload;
import com.accelevents.services.*;
import com.accelevents.services.integration.UploadAttendeeTrackService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.services.repo.helper.WhiteLabelRepoService;
import com.accelevents.ticketing.dto.OrderDto;
import com.accelevents.ticketing.dto.TicketingOrderDto;
import com.accelevents.ticketing.dto.UTMTrackSourceDto;
import com.accelevents.utils.*;
import com.accelevents.virtualevents.dto.UploadCSVInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.opencsv.CSVReader;
import com.stripe.exception.StripeException;
import com.twilio.rest.lookups.v1.PhoneNumber;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.bind.JAXBException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.enums.PlanConfigNames.FREE_PLAN;
import static com.accelevents.utils.Constants.*;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class TicketingPurchaseFromAttendeeUploadServiceImpl implements TicketingPurchaseFromAttendeeUploadService {

    private static final Logger logger = LoggerFactory.getLogger(TicketingPurchaseFromAttendeeUploadServiceImpl.class);

    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private UploadAttendeeTrackService uploadAttendeeTrackService;
    @Autowired
    @Lazy
    private TicketingTypeService ticketingTypeService;
    @Lazy
    @Autowired
    private TicketingPurchaseService ticketingPurchaseService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private EventService eventService;
    @Autowired
    private StaffServiceImpl staffService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private AttendeeProfileService attendeeProfileService;
    @Autowired
    private SendGridMailService sendGridMailService;
    @Autowired
    private PaymentHandlerService paymentHandlerService;
    @Autowired
    private BuyTicketsFromAttendeeUpload buyTicketsFromAttendeeUpload;
    @Autowired
    private EventBillingAddOnsService eventBillingAddOnsService;
    @Autowired
    private StripeTransactionService stripeTransactionService;
    @Autowired
    private EventBillingService eventBillingService;
    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Autowired
    private ChargebeeAttendeeUploadService chargebeeAttendeeUploadService;
    @Autowired
    private OrganizerRepository organizerRepository;
    @Autowired
    private WhiteLabelRepository whiteLabelRepository;
    @Autowired
    private TwilioPhoneNumberValidateService phoneNumberValidateService;
    @Autowired
    private OrganizerRepoService organizerRepoService;
    @Autowired
    private WhiteLabelRepoService whiteLabelRepoService;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private EventDesignDetailService eventDesignDetailService;
    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

    @Override
    @Transactional
    public UploadCSVInfo purchaseTicketsFromUploadedCSV(List<Long> ticketTypeIds,
                                                      MultipartFile multiPartFile,
                                                      Event event,
                                                      User hostUser, User staffUser,String mappings) throws IOException {


        // if event engage email time is passed then host can not upload the data except super admin
        ticketingService.checkEventPostEnangeEmailAccessPermitIsPassed(event,staffUser);

        Ticketing ticketing = ticketingService.findByEvent(event);
        List<TicketingType> ticketingTypeList = ticketingTypeService.findByidInAndEvent(ticketTypeIds,event);


        UploadCSVInfo uploadCSVInfo = parseAttendeeCSV(multiPartFile, event,ticketTypeIds,mappings);
        List<AttendeeInfoDTO> infoDTOList = uploadCSVInfo.getValidAttendeeList();
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(event.getEventId());
        PlanConfig planConfig = eventPlanConfig.getPlanConfig();
        if (FREE_PLAN.getName().equals(planConfig.getPlanName())) {
            Long attendeeImportLimit = eventPlanConfig.getAttendeeImportLimit();
            if (attendeeImportLimit != 0) {
                long eventSoldTickets = eventCommonRepoService.findSoldCountByEvent(event).longValue();
                double eventCapacity = ticketing.getEventCapacity() != null ? ticketing.getEventCapacity() : 100;
                if (eventSoldTickets >= eventCapacity) {
                    List<AttendeeInfoDTO> invalidAttendeeList =  uploadCSVInfo.getInvalidAttendeeList();
                    invalidAttendeeList.addAll(uploadCSVInfo.getValidAttendeeList());
                    uploadCSVInfo.setErrorMessage("You cannot import more attendees to this event. For more information, please contact Support");
                    uploadCSVInfo.setValidAttendeeList(new ArrayList<>());
                    return uploadCSVInfo;
                }
                long eventSoldTicketsByCSV = eventCommonRepoService.findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(event.getEventId()).longValue();
                Long leftAttendeeImportLimit = attendeeImportLimit - eventSoldTicketsByCSV;
                leftAttendeeImportLimit = leftAttendeeImportLimit > 0 ? leftAttendeeImportLimit : 0;
                if (infoDTOList.size() > leftAttendeeImportLimit) {
                    List<AttendeeInfoDTO> invalidAttendeeList =  uploadCSVInfo.getInvalidAttendeeList();
                    invalidAttendeeList.addAll(uploadCSVInfo.getValidAttendeeList());
                    uploadCSVInfo.setErrorMessage("You cannot import more attendees to this event. For more information, please contact Support");
                    uploadCSVInfo.setValidAttendeeList(new ArrayList<>());
                    return uploadCSVInfo;
                }
            }
        }

        // to check if someone try to import more than 10,000 attendee exclude super admin then throw error
        if (!roUserService.isSuperAdminUser(staffUser)) {
            long eventSoldTicketsByCSV = eventCommonRepoService.findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(event.getEventId()).longValue();
            // total already uploaded attendee + current upload attendee
            if (eventSoldTicketsByCSV + infoDTOList.size() > 10000) {
                List<AttendeeInfoDTO> invalidAttendeeList =  uploadCSVInfo.getInvalidAttendeeList();
                invalidAttendeeList.addAll(uploadCSVInfo.getValidAttendeeList());
                uploadCSVInfo.setErrorMessage("You cannot import more than 10,000 attendees to this event. For more information, please contact Support");
                uploadCSVInfo.setValidAttendeeList(new ArrayList<>());
                return uploadCSVInfo;
            }
        }

        if (!CollectionUtils.isEmpty(infoDTOList)) {
            this.uploadAttendeeThroughCSV(event, hostUser, true, ticketing, uploadCSVInfo, ticketingTypeList, UploadAttendeeTrack.UploadSource.CSV);

        } else {
            throw new NotFoundException(NotFoundException.NotFound.ATTENDEE_LIST_NOT_FOUND);
        }
        uploadCSVInfo.setSuccessMessage("CSV uploaded successfully, We will notify you soon by email.");
        return uploadCSVInfo;
    }

    private void validateNoDuplicateHolderAndBuyerEmails(Ticketing ticketing, List<String> attendeeEmails, Event event, List<String> uniqueBuyers, List<String> uniqueHolders) {
        if ((ticketing.getUniqueTicketHolderEmail() || ticketing.isUniqueTicketBuyerEmail()) && !attendeeEmails.isEmpty() ) {
            // Filter out null values
            List<String> normalizedEmails = attendeeEmails.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // Find duplicates within the new holder emails
            Set<String> duplicatesInNewEmails = findDuplicatesInList(normalizedEmails);

            boolean duplicateEmails = false;

            Set<String> existingEmails = new HashSet<>();
            // Find emails that already exist in the database
            if (ticketing.getUniqueTicketHolderEmail()) {
                uniqueHolders.addAll(eventCommonRepoService.findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(
                        event.getEventId(), normalizedEmails));
                if (null != duplicatesInNewEmails) {
                    uniqueHolders.addAll(duplicatesInNewEmails);
                    duplicateEmails = true;
                }
            }
            if (ticketing.isUniqueTicketBuyerEmail()) {
                uniqueBuyers.addAll(eventCommonRepoService.findBuyerEmailsByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(
                        event.getEventId(), normalizedEmails));
                if (null != duplicatesInNewEmails) {
                    if (!duplicateEmails) {
                        uniqueBuyers.addAll(duplicatesInNewEmails);
                    }
                }
            }
        }
    }



    private String getEventBillingNote(UploadAttendeeTrack.UploadSource uploadSource){
        String uploadNote;
        switch(uploadSource) {
            case CSV:
                uploadNote = UNPAID_ATTENDEE_UPLOAD_CSV_NOTE;
                break;
            case ZAPIER:
                uploadNote = Zapier.EVENT_BILLING_NOTE;
                break;
            case CVENT:
                uploadNote = CVENT_EVENT_BILLING_NOTE;
                break;
            case TRAY_IO_CONNECTOR:
            case HUBSPOT:
            case MARKETO:
            case SALESFORCE:
                uploadNote = TRAY_IO_CONNECTOR_EVENT_BILLING_NOTE;
                break;
            default:
                uploadNote = "This is the uploaded and we have charged it.";
                break;
        }
        return uploadNote;
    }

    private EventBillingAddOns getEventBillingAddOnsObject(Event event, User hostUser, UploadAttendeeTrack.UploadSource uploadSource, double amount){
        Optional<EventBillingAddOns> eventBillingAddOnsOptional = Optional.empty();
        if(uploadSource.equals(UploadAttendeeTrack.UploadSource.ZAPIER)) {
            eventBillingAddOnsOptional = eventBillingAddOnsService.findUnpaidUploadEventBillingOfEventForGivenNote(event.getEventId(), Zapier.EVENT_BILLING_NOTE);
        } else if(uploadSource.equals(UploadAttendeeTrack.UploadSource.CVENT)){
            eventBillingAddOnsOptional = eventBillingAddOnsService.findUnpaidUploadEventBillingOfEventForGivenNote(event.getEventId(), CVENT_EVENT_BILLING_NOTE);
        } else if(isTrayIntegration(uploadSource)){
            eventBillingAddOnsOptional = eventBillingAddOnsService.findUnpaidUploadEventBillingOfEventForGivenNote(event.getEventId(), TRAY_IO_CONNECTOR_EVENT_BILLING_NOTE);
        }

        EventBillingAddOns billingAddOns;
        if(eventBillingAddOnsOptional.isPresent()){
            billingAddOns = eventBillingAddOnsOptional.get();
            billingAddOns.setAmount(billingAddOns.getAmount() + amount);
            billingAddOns.setUpdatedDate(new Date());
        } else {
            billingAddOns = new EventBillingAddOns();
            billingAddOns.setAddOnType(AddOnType.UPLOAD);
            billingAddOns.setAmount(amount);
            billingAddOns.setEventId(event.getEventId());
            billingAddOns.setRate((double) 0);
            billingAddOns.setNotes(this.getEventBillingNote(uploadSource));
            billingAddOns.setRecordEnteredByEmployeeId(hostUser.getUserId());
            billingAddOns.setCreatedDate(new Date());
        }

        return billingAddOns;
    }

    private void uploadAttendeeThroughCSV(Event event, User hostUser,boolean isAllowToCollectPayment, Ticketing ticketing,UploadCSVInfo uploadCSVInfo,List<TicketingType> ticketingTypeList, UploadAttendeeTrack.UploadSource uploadSource){

        List<AttendeeInfoDTO> infoDTOList = uploadCSVInfo.getValidAttendeeList();

        List<AttendeeInfoDTO> attendeeListWithBlankOrderId = infoDTOList.stream().filter(e -> StringUtils.isBlank(e.getTransactionId())).collect(toList());
        List<AttendeeInfoDTO> attendeeListWithNotBlankOrderId = infoDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getTransactionId())).collect(toList());

        //Collect payment from here. So we can validate the fields too.
        double numberOfTotalAttendee = uploadCSVInfo.getValidAttendeeList().size();
        double amount;
        if(null != event.getWhiteLabel()){
            WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto = whiteLabelRepoService.findWhiteLabelSettingsById(event.getWhiteLabel().getId());
            amount = numberOfTotalAttendee * (null != whiteLabelBillingSettingsDto ? whiteLabelBillingSettingsDto.getAttendeeUploadCharge() : 1);
        }else if(null != event.getOrganizer()){
            Organizer organizer = organizerRepoService.findByIdThrowException(event.getOrganizerId());
            amount = numberOfTotalAttendee * (null != organizer ? organizer.getAttendeeUploadCharge() : 1);
        }else {
            amount = numberOfTotalAttendee;
        }
        EventBillingAddOns billingAddOns = this.getEventBillingAddOnsObject(event, hostUser, uploadSource, amount);

        PaymentStatus paymentStatus = checkWaiveOffFlag(event);
        billingAddOns.setPaymentStatus(paymentStatus);

        executorServiceForCheckoutProcess(event, hostUser, ticketing, ticketingTypeList, attendeeListWithBlankOrderId, attendeeListWithNotBlankOrderId, billingAddOns);
    }

    private List<String> uploadAttendee(Event event, User hostUser,boolean isAllowToCollectPayment, Ticketing ticketing, List<AttendeeInfoDTO> infoDTOList,List<TicketingType> ticketingTypeList, UploadAttendeeTrack.UploadSource uploadSource) {
        List<String> inValidPhoneList = new ArrayList<>();
        infoDTOList = validationForPhoneNumberAndCountryCode(infoDTOList, inValidPhoneList);
        List<Long> ticketTypeIds = ticketingTypeList.stream().map(TicketingType::getId).collect(toList());
        if (infoDTOList.isEmpty()) {
            return inValidPhoneList;
        }

        List<AttendeeInfoDTO> attendeeListWithBlankOrderId = infoDTOList.stream().filter(e -> StringUtils.isBlank(e.getExternalOrderId())).collect(toList());
        List<AttendeeInfoDTO> attendeeListWithNotBlankOrderId = infoDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getExternalOrderId())).collect(toList());


        List<String> orderIdsAllowToCreateOrder = removeAttendeeListWhichHaveAlreadyOrderCreatedByOrderId(attendeeListWithNotBlankOrderId, event, ticketTypeIds, attendeeListWithBlankOrderId);

        List<AttendeeInfoDTO> attendeesWithOrderIdsAllowToCreateOrder;
        if (CollectionUtils.isEmpty(orderIdsAllowToCreateOrder)) {
            attendeesWithOrderIdsAllowToCreateOrder = Collections.emptyList();
        } else {
            attendeesWithOrderIdsAllowToCreateOrder = attendeeListWithNotBlankOrderId.stream().filter(e -> orderIdsAllowToCreateOrder.contains(e.getExternalOrderId())).collect(toList());
        }

        validationForNumberOfTicketsAreAvailableToSold(ticketingTypeList, infoDTOList);

        //Collect payment from here. So we can validate the fields too.
        double numberOfTotalAttendee = attendeeListWithBlankOrderId.size() + attendeesWithOrderIdsAllowToCreateOrder.size();//NOSONAR
        double amount;
        if(null != event.getWhiteLabel()){
            WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto = whiteLabelRepoService.findWhiteLabelSettingsById(event.getWhiteLabel().getId());
            amount = numberOfTotalAttendee * (null != whiteLabelBillingSettingsDto ? whiteLabelBillingSettingsDto.getAttendeeUploadCharge() : 1);
        }else if(null != event.getOrganizer()){
            Organizer organizer = organizerRepoService.findByIdThrowException(event.getOrganizerId());
            amount = numberOfTotalAttendee * (null != organizer ? organizer.getAttendeeUploadCharge() : 1);
        }else {
            amount = numberOfTotalAttendee;
        }
        EventBillingAddOns billingAddOns = this.getEventBillingAddOnsObject(event, hostUser, uploadSource, amount);

        PaymentStatus paymentStatus = checkWaiveOffFlag(event);
        if (isAllowToCollectPayment) {
            billingAddOns.setPaymentStatus(paymentStatus);
        } else {
            billingAddOns.setPaymentStatus(PaymentStatus.PAID);
        }
        executorServiceForCheckoutProcess(event, hostUser, ticketing, ticketingTypeList, attendeeListWithBlankOrderId, attendeesWithOrderIdsAllowToCreateOrder, billingAddOns);
        return inValidPhoneList;
    }

    public List<String> validateAddOnTicket(
            AttendeeInfoDTO attendeeInfoDTO,
            List<String> invalidListOfAddOn,
            Map<String, Long> emailToUserMap,
            Set<Long> usersWithPurchasedTickets,
            boolean userHadPurchaseTicket) {

        Long buyerUser = emailToUserMap.get(attendeeInfoDTO.getEmail());
        boolean alreadyPurchasedTicket = buyerUser != null && usersWithPurchasedTickets.contains(buyerUser);
        if (!alreadyPurchasedTicket && !userHadPurchaseTicket) {
            invalidListOfAddOn.add(attendeeInfoDTO.getEmail());
        }
        return  invalidListOfAddOn;
    }

    private List<AttendeeInfoDTO> validationForPhoneNumberAndCountryCode(List<AttendeeInfoDTO> validateForPhone, List<String> inValidPhoneList) {

        if (!validateForPhone.isEmpty()) {
            validateForPhone.forEach(element -> {
                List<AttributeKeyValueDto> attributes = element.getAttributes();
                Optional<AttributeKeyValueDto> phoneNumberOptional = attributes.stream().filter(e -> e.getKey().equalsIgnoreCase("phoneNumber")).findFirst();
                Optional<AttributeKeyValueDto> optionalCountryCode = attributes.stream().filter(e -> e.getKey().equalsIgnoreCase("CountryCode")).findFirst();

                if (optionalCountryCode.isPresent() && phoneNumberOptional.isPresent()) {
                    AttributeKeyValueDto countryCodeDTO = optionalCountryCode.get();
                    String countryCode = countryCodeDTO.getValue();
                    AttributeKeyValueDto phoneNumberDTO = phoneNumberOptional.get();
                    String phoneNumber = phoneNumberDTO.getValue();
                    if ((null != countryCode && !countryCode.isEmpty()) && (null != phoneNumber && !phoneNumber.isEmpty())) {
                        boolean isValidPhone = phoneNumberValidateService.isValidPhoneNumber(countryCode, phoneNumber);
                        if (!isValidPhone) {
                            inValidPhoneList.add(element.getEmail());
                        }
                    }
                }

                if (optionalCountryCode.isEmpty() && phoneNumberOptional.isPresent()) {
                    AttributeKeyValueDto phoneNumberDTO = phoneNumberOptional.get();
                    String phoneNumber = phoneNumberDTO.getValue();
                    if (null != phoneNumber && !phoneNumber.isEmpty()) {
                        boolean isValidPhone = phoneNumberValidateService.validatePhoneNumber(phoneNumber);
                        if (!isValidPhone) {
                            inValidPhoneList.add(element.getEmail());
                        } else {
                            PhoneNumber phoneDetail = phoneNumberValidateService.getPhoneDetail(phoneNumber);
                            if (phoneDetail != null) {
                                String countryCode = phoneDetail.getCountryCode();
                                String nationalFormat = prepareNumber(phoneDetail.getNationalFormat());
                                AttributeKeyValueDto updated = new AttributeKeyValueDto(phoneNumberDTO.getKey(), nationalFormat);
                                attributes.set(attributes.indexOf(phoneNumberDTO), updated);
                                AttributeKeyValueDto countryCodeAttribute = new AttributeKeyValueDto("CountryCode", countryCode);
                                attributes.add(countryCodeAttribute);
                                AttributeKeyValueDto cellPhoneAttribute = new AttributeKeyValueDto("Cell Phone", nationalFormat + "|" + countryCode);
                                attributes.add(cellPhoneAttribute);
                            }
                        }
                    }
                }
            });
        }

        return validateForPhone.stream().filter(element -> !inValidPhoneList.contains(element.getEmail())).collect(toList());

    }

    private String prepareNumber(String nationalFormat) {
        if (nationalFormat != null){
            if ("0".equals(nationalFormat.substring(0,1))) {
                nationalFormat = nationalFormat.substring(1);
            }
            nationalFormat = nationalFormat.replaceAll("\\s", "");
            return  nationalFormat;
        }
        logger.info("National Format Number{}",nationalFormat);
        return STRING_EMPTY;
    }

    public PaymentStatus checkWaiveOffFlag(Event event){

        AttendeesUploadCharge attendeeUploadCharge = eventService.getAttendeesUploadCharge(event.getEventId());
        if (!AttendeesUploadCharge.CHARGE.equals(attendeeUploadCharge)){
            return PaymentStatus.WAIVE_OFF;
        }
        if(event.isUploadBillingPending())
            return PaymentStatus.PENDING;
        return PaymentStatus.UNPAID;
    }

    @Override
    public UploadCSVInfo checkCSVHaveCorrectRecords(MultipartFile multiPartFile, Event event, List<Long> ticketTypeIds,String mappings) throws IOException {//NOSONAR

        String languageCode = roEventService.getLanguageCodeByUserOrEvent(null,event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());

        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

        Map<String, String> mapping = JsonMapper.stringToObjectWithTypeReference(mappings, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(mapping)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_JSON_MAPPING_FORMAT_FOR_ATTENDEE_CSV);
        }

        if(ticketingService.isRecurringEvent(event)){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.VIRTUAL_EVENTS_ARE_NOT_SUPPORT_WITH_RECURRING_EVENT);
        }
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<TicketingType> ticketingTypeList = ticketingTypeService.findByidInAndEvent(ticketTypeIds,event);
        List<String> attendeeList = new ArrayList<>();
        if(ticketingTypeList.isEmpty()){
            throw new NotFoundException(NotFoundException.NotFound.TICKET_TYPE_NOT_FOUND);
        }
        StripeTransaction stripeTransaction = stripeTransactionService.getEventBillingDetailByEvent(event);
        UploadCSVInfo uploadCSVInfo = new UploadCSVInfo();

        double totalAttendee=0;
        List<NestedQuestionsDto> nestedQuestionsDtos = new ArrayList<>();
        List<AttributeKeyValueDto> attributes = new ArrayList<>();
        String valueCellPhone = Constants.STRING_EMPTY;
        String valueCountryCode = Constants.STRING_EMPTY;
        boolean isAllowToAddCellPhone = true;
        List<AttendeeInfoDTO> validAttendeeList = new ArrayList<>();
        List<AttendeeInfoDTO> inValidAttendeeList = new ArrayList<>();
        boolean profileImageFound = isProfileImageUrlHeaderPresent(mapping);
        try (CSVReader cr = new CSVReader(new BufferedReader(new InputStreamReader(multiPartFile.getInputStream())))) {
            String[] header = cr.readNext();

            if (header == null  || header.length < 4 ||  header[0] == null || header[1] == null || header[2] == null || header[3]== null){
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.UPLOADED_FILE_SHOULD_HAVE_REQUIRE_HEADER);
            }

            validateAttendeeCSVHeaders(header,mapping);

            Map<String, Integer> headerIndexMap = new HashMap<>();
            for (int i = 0; i < header.length; i++) {
                headerIndexMap.put(header[i].trim(), i);
            }

            int transactionIdIndex = Optional.ofNullable(mapping.get(TRANSACTION_ID)).map(headerIndexMap::get).orElse(-1);
            int firstNameIndex = Optional.ofNullable(mapping.get(FIRST_NAME)).map(headerIndexMap::get).orElse(-1);
            int lastNameIndex = Optional.ofNullable(mapping.get(LAST_NAME)).map(headerIndexMap::get).orElse(-1);
            int emailIndex = Optional.ofNullable(mapping.get(EMAIL)).map(headerIndexMap::get).orElse(-1);
            int profileImageUrlIndex = profileImageFound ? Optional.ofNullable(mapping.get(PROFILE_IMAGE_URL)).map(headerIndexMap::get).orElse(-1) : -1;

            String[] nextItem;
            AttendeeInfoDTO processedAttendeeDto;

            for (TicketingType ticketingType : ticketingTypeList) {
                Map<String, TicketHolderRequiredAttributes> mapOfNameAndAttribute = getStringTicketHolderRequiredAttributesMap(event, ticketingType.getDataType());

                while ((nextItem = cr.readNext()) != null) {
                    String transactionId = transactionIdIndex > -1 ? StringUtils.defaultIfEmpty(nextItem[transactionIdIndex], Constants.STRING_EMPTY) : STRING_EMPTY;
                    String firstName = firstNameIndex > -1 ? StringUtils.defaultIfEmpty(nextItem[firstNameIndex], Constants.STRING_EMPTY) : STRING_EMPTY;
                    String lastName = lastNameIndex > -1 ? StringUtils.defaultIfEmpty(nextItem[lastNameIndex], Constants.STRING_EMPTY) : STRING_EMPTY;
                    String email = emailIndex > -1 ? StringUtils.defaultIfEmpty(nextItem[emailIndex], Constants.STRING_EMPTY) : STRING_EMPTY;
                    String profileImage = profileImageUrlIndex > -1 ? StringUtils.defaultIfEmpty(nextItem[profileImageUrlIndex], Constants.STRING_EMPTY) : STRING_EMPTY;

                    processedAttendeeDto = new AttendeeInfoDTO(transactionId, firstName, lastName, email, profileImageFound ? profileImage : null);
                    if (StringUtils.isBlank(firstName)) {
                        processedAttendeeDto.setInValidFirstName(true);
                        processedAttendeeDto.setInvalidFirstNameErrorMessage(resourceBundle.getString(languageMap.get(Constants.CSV_ATTENDEE_ERROR_MESSAGE.FIRST_NAME_BLANK_MESSAGE)));
                    }
                    if (StringUtils.isBlank(lastName)) {
                        processedAttendeeDto.setInValidLastName(true);
                        processedAttendeeDto.setInvalidLastNameErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.LAST_NAME_BLANK_MESSAGE)));
                    }
                    if (StringUtils.isBlank(email)) {
                        processedAttendeeDto.setInValidEmail(true);
                        processedAttendeeDto.setInvalidEmailErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_BLANK_MESSAGE)));
                    } else if (GeneralUtils.convertCommaSeparatedToList(email.trim()).size() > 1 || email.trim().split(" ").length > 1) {
                        processedAttendeeDto.setInValidEmail(true);
                        processedAttendeeDto.setInvalidEmailErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_LIMIT_MESSAGE)));
                    } else if (!GeneralUtils.isValidEmailAddress(email.trim())) {
                        processedAttendeeDto.setInValidEmail(true);
                        processedAttendeeDto.setInvalidEmailErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_NOT_VALID_MESSAGE)));
                    }

                    if (profileImageFound && StringUtils.isNotBlank(profileImage) && !GeneralUtils.isValidURL(profileImage)) {
                        processedAttendeeDto.setInValidProfileImage(true);
                        processedAttendeeDto.setInValidProfileImageErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.PROFILE_IMAGE_NOT_VALID_MESSAGE)));
                    }
                    if (firstName.length() > 50) {
                        processedAttendeeDto.setInValidFirstName(true);
                        processedAttendeeDto.setInvalidFirstNameErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.FIRST_NAME_SIZE_MSG)));
                    } else if (lastName.length() > 50) {
                        processedAttendeeDto.setInValidLastName(true);
                        processedAttendeeDto.setInvalidLastNameErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.LAST_NAME_SIZE_MSG)));
                    } else if (email.length() > 75) {
                        processedAttendeeDto.setInValidEmail(true);
                        processedAttendeeDto.setInvalidEmailErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.EMAIL_SIZE_MSG)));
                    }
                    totalAttendee++;

                    validateTransactionId(transactionId, processedAttendeeDto, resourceBundle);


                    int customQuestionStartingIndex = (profileImageFound ? 5 : 4);
                    for (int i = customQuestionStartingIndex; header.length > customQuestionStartingIndex && i < header.length; i++) {
                        if (i < nextItem.length) {
                            String attributeNameCsv = getKeyAttribute(header[i]).trim();

                            if ("Payment Status".equalsIgnoreCase(attributeNameCsv)) {
                                processedAttendeeDto.setPaymentStatus(StringUtils.upperCase(nextItem[i]));
                            } else if (!CollectionUtils.isEmpty(mapOfNameAndAttribute) && null != (mapOfNameAndAttribute.get(attributeNameCsv)) && StringUtils.isNotBlank(mapOfNameAndAttribute.get(attributeNameCsv).getName())) {

                                TicketHolderRequiredAttributes nestedQue = mapOfNameAndAttribute.get(attributeNameCsv);
                                NestedQuestionsDto nestedQuestionsDto = new NestedQuestionsDto();
                                nestedQuestionsDto.setId(nestedQue.getId());
                                nestedQuestionsDto.setName(nestedQue.getName());
                                nestedQuestionsDto.setType(AttributeValueType.CONDITIONAL_QUE.name());
                                nestedQuestionsDto.setValue(nextItem[i]);
                                nestedQuestionsDto.setParentQueId(nestedQue.getParentQuestionId());
                                nestedQuestionsDtos.add(nestedQuestionsDto);

                            } else {
                                AttributeKeyValueDto dto = new AttributeKeyValueDto();
                                dto.setKey(getKeyAttribute(header[i].trim()));
                                dto.setValue(nextItem[i]);
                                attributes.add(dto);

                                if (Constants.PHONE_NUMBER.equalsIgnoreCase(header[i].trim()) || Constants.CELL_PHONE.equalsIgnoreCase(header[i].trim())) {
                                    valueCellPhone = nextItem[i];
                                } else if (Constants.COUNTRY_CODE_TXT.equalsIgnoreCase(header[i].trim())) {
                                    valueCountryCode = nextItem[i];
                                }

                                if (StringUtils.isNotBlank(valueCellPhone) && StringUtils.isNotBlank(valueCountryCode) && isAllowToAddCellPhone) {
                                    AttributeKeyValueDto dtoCellPhone = new AttributeKeyValueDto();
                                    dtoCellPhone.setKey(Constants.STRING_CELL_SPACE_PHONE);
                                    dtoCellPhone.setValue(valueCellPhone.concat("|").concat(valueCountryCode));
                                    attributes.add(dtoCellPhone);
                                    isAllowToAddCellPhone = false;
                                }
                            }
                        }
                    }

                    processedAttendeeDto.setUploadSource(UploadAttendeeTrack.UploadSource.CSV);
                    processedAttendeeDto.setQuantity(1L);
                    processedAttendeeDto.setTicketTypeId(ticketingType.getId());
                    processedAttendeeDto.setAttributes(attributes);
                    processedAttendeeDto.setNestedQuestions(nestedQuestionsDtos);

                    if (processedAttendeeDto.isInValidTransactionId() ||
                            processedAttendeeDto.isInValidFirstName() ||
                            processedAttendeeDto.isInValidLastName() ||
                            processedAttendeeDto.isInValidEmail()) {
                        inValidAttendeeList.add(processedAttendeeDto);
                    } else {
                        validAttendeeList.add(processedAttendeeDto);
                    }

                    if (StringUtils.isNotBlank(email) && GeneralUtils.isValidEmailAddress(email.trim())
                            && GeneralUtils.convertCommaSeparatedToList(email.trim()).size() == 1
                            && email.trim().split(" ").length == 1) {
                        attendeeList.add(email.trim());
                    }
                }
            }
        }

        // if the transaction id is not blank then check if the transaction id is already created order
        List<String> externalOrderIdsFromDb = uploadAttendeeTrackService.findExternalOrderIdsForEventAndTicketTypeIds(event.getEventId(), ticketTypeIds);

        List<String> uniqueBuyers  = new ArrayList<>();
        List<String> uniqueHolders   = new ArrayList<>();
        validateNoDuplicateHolderAndBuyerEmails(ticketing,attendeeList,event,uniqueBuyers,uniqueHolders);

        Map<String, Long> emailToUserMap = roUserService.findAllByEmails(attendeeList).stream()
                .filter(row -> row != null && row[0] != null && row[1] != null)
                .collect(Collectors.toMap( row -> (String) row[0], row -> (Long) row[1]));

        Set<Long> usersWithPurchasedTickets = eventTicketsRepoService
                .findUserIdsWithPurchasedTickets(event, new ArrayList<>(emailToUserMap.values()));

        boolean userHadPurchaseTicket = ticketingTypeService.userHadPurchasedTicket(ticketTypeIds, event);

        List<String> invalidAddOnList = new ArrayList<>();

        Iterator<AttendeeInfoDTO> it = validAttendeeList.iterator();
        while (it.hasNext()) {
            AttendeeInfoDTO attendee = it.next();
            if(!uniqueBuyers.isEmpty() &&
                    uniqueBuyers.contains(attendee.getEmail().trim())){
                attendee.setUniqueBuyerExists(true);
                attendee.setAttendeeAlreadyExistsErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.BUYER_ATTENDEE_ALREADY_EXISTS)));
            }

            if(!uniqueHolders.isEmpty() &&
                    uniqueHolders.contains(attendee.getEmail().trim())){
                attendee.setUniqueHolderExists(true);
                attendee.setAttendeeAlreadyExistsErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.HOLDER_ATTENDEE_ALREADY_EXISTS)));
            }

            if(attendee.isUniqueBuyerExists() && attendee.isUniqueHolderExists()){
                attendee.setAttendeeAlreadyExistsErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.BUYER_AND_HOLDER_WITH_SAME_EMAIL_ALREADY_EXISTS)));
            }

            if (isNotBlank(attendee.getTransactionId()) && externalOrderIdsFromDb.contains(attendee.getTransactionId())){
                attendee.setInValidTransactionId(true);
                attendee.setInvalidTransactionIdErrorMessage(resourceBundle.getString(languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.TRANSACTION_ID_ALREADY_CREATED_ORDER)));
            }

            if(attendee.isInValidTransactionId() || attendee.isUniqueHolderExists() || attendee.isUniqueBuyerExists()){
                inValidAttendeeList.add(attendee);
                it.remove();
            }

            invalidAddOnList = validateAddOnTicket(attendee,invalidAddOnList,emailToUserMap,usersWithPurchasedTickets,userHadPurchaseTicket);
        }

        List<String> finalInvalidAddOnList = invalidAddOnList;

        List<AttendeeInfoDTO> invalidAddOnAttendeeList;
        if(!validAttendeeList.isEmpty()){
            invalidAddOnAttendeeList =  validAttendeeList.stream()
                    .filter(element -> !finalInvalidAddOnList.contains(element.getEmail()))
                    .collect(Collectors.toList());

            if(invalidAddOnAttendeeList.isEmpty()){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.ADD_ON_TICKET_WITHOUT_EVENT_TICKET_EXCEPTION);
            }

            if(!invalidAddOnList.isEmpty()){
                Iterator<AttendeeInfoDTO> invalidAddonListIterator = validAttendeeList.iterator();
                while (invalidAddonListIterator.hasNext()) {
                    AttendeeInfoDTO attendee = invalidAddonListIterator.next();
                    if(invalidAddOnList.contains(attendee.getEmail())){
                        attendee.setAddonErrorMessage("An event ticket is required to purchase add-on tickets.");
                        inValidAttendeeList.add(attendee);
                        invalidAddonListIterator.remove();
                    }
                }
            }
        }

        if(!validAttendeeList.isEmpty()){
            List<String> invalidPhoneList = new ArrayList<>();
            List<AttendeeInfoDTO> invalidPhoneNumberAttendeeList;
            invalidPhoneNumberAttendeeList = validationForPhoneNumberAndCountryCode(validAttendeeList,invalidPhoneList);
            if (invalidPhoneNumberAttendeeList.isEmpty()) {
                inValidAttendeeList.addAll(validAttendeeList);
                validAttendeeList.clear();
                for (AttendeeInfoDTO attendee : inValidAttendeeList) {
                    attendee.setInValidPhoneNumber(true);
                    attendee.setInValidPhoneNumberErrorMessage("Invalid Phone number or Country Code");
                }
            } else {
                Iterator<AttendeeInfoDTO> invalidPhoneNumberListIterator = validAttendeeList.iterator();
                while (invalidPhoneNumberListIterator.hasNext()) {
                    AttendeeInfoDTO attendee = invalidPhoneNumberListIterator.next();
                    if(invalidPhoneList.contains(attendee.getEmail())){
                        attendee.setInValidPhoneNumberErrorMessage("Invalid Phone number or Country Code");
                        inValidAttendeeList.add(attendee);
                        invalidPhoneNumberListIterator.remove();
                    }
                }
            }
        }

        for (TicketingType ticketingType : ticketingTypeList) {
            if (!DataType.ADDON.equals(ticketingType.getDataType())) {
                checkEventCapacity(event, validAttendeeList.size());
            }
        }

        validationForNumberOfTicketsAreAvailableToSold(ticketingTypeList, validAttendeeList);


        StripeCreditCardDto cardDto = eventBillingService.getCardDetailsWhomActivatedModule(stripeTransaction);

        uploadCSVInfo.setAllowToUpload(true);
        uploadCSVInfo.setLast4(null != cardDto ? cardDto.getLast4() : STRING_EMPTY);
        uploadCSVInfo.setWaiveOffAttendeesUploadFee(isWaiveOffAttendeesUploadFee(event));
        uploadCSVInfo.setInvalidAttendeeList(inValidAttendeeList);
        uploadCSVInfo.setValidAttendeeList(validAttendeeList);

        if(null != event.getWhiteLabel()){
            WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto = whiteLabelRepoService.findWhiteLabelSettingsById(event.getWhiteLabel().getId());
            uploadCSVInfo.setAmount(totalAttendee * (null != whiteLabelBillingSettingsDto ? whiteLabelBillingSettingsDto.getAttendeeUploadCharge() : 1));
            uploadCSVInfo.setAttendeeUploadCharge(null != whiteLabelBillingSettingsDto ? whiteLabelBillingSettingsDto.getAttendeeUploadCharge() : 1);
        }else if(null != event.getOrganizer()){
            Organizer organizer = organizerRepoService.findByIdThrowException(event.getOrganizerId());
            uploadCSVInfo.setAmount(totalAttendee * (null != organizer ? organizer.getAttendeeUploadCharge() : 1));
            uploadCSVInfo.setAttendeeUploadCharge(null != organizer ? organizer.getAttendeeUploadCharge() : 1);
        }else{
            uploadCSVInfo.setAmount(totalAttendee);
            uploadCSVInfo.setAttendeeUploadCharge((double)1);
        }

        return uploadCSVInfo;
    }

    private void validateTransactionId(String transactionId, AttendeeInfoDTO processedAttendeeDto, ResourceBundle resourceBundle) {
        if (isNotBlank(transactionId)) {
            try {
                Double.parseDouble(transactionId);
            } catch (NumberFormatException nfe) {
                processedAttendeeDto.setInValidTransactionId(true);
                processedAttendeeDto.setInvalidTransactionIdErrorMessage(resourceBundle.getString((languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.TRANSACTION_ID_NOT_VALID_MESSAGE))));

            }
            if (transactionId.length() > 45) {
                processedAttendeeDto.setInValidTransactionId(true);
                processedAttendeeDto.setInvalidTransactionIdErrorMessage(resourceBundle.getString((languageMap.get(CSV_ATTENDEE_ERROR_MESSAGE.TRANSACTION_ID_SIZE_MESSAGE))));
            }
        }
    }

    private boolean isProfileImageUrlHeaderPresent(Map<String, String> mapping){
        String profileImageHeader = mapping.get(PROFILE_IMAGE_URL);
        return StringUtils.isNotBlank(profileImageHeader);
    }

    private void checkEventCapacity(Event event, double totalAttendee) {
        Ticketing ticketing = ticketingService.findByEvent(event);
        Long soldCount = ticketingPurchaseService.checkEventCapacityForNonRecurring(ticketing);

        logger.info("TicketingPurchaseFromAttendeeUploadServiceImpl checkEventCapacity ticketing {} soldCount {} totalAttendee {} eventCapacity {} ",ticketing, soldCount, totalAttendee, ticketing.getEventCapacity());

        if(ticketing.isLimitEventCapacity() && soldCount + totalAttendee > ticketing.getEventCapacity()){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.EVENT_DOES_NOT_HAVE_ENOUGH_TICKETS_UPLOAD_CSV_CAPACITY_REACH);
        }

    }

    private boolean isWaiveOffAttendeesUploadFee(Event event) {
        return PaymentStatus.WAIVE_OFF.equals(checkWaiveOffFlag(event));
    }

    /*0   1  2   3    4  5
     * 1 FN LN Email  1  age
     * */

    public UploadCSVInfo parseAttendeeCSV(MultipartFile multiPartFile, Event event,List<Long> ticketingTypeIds,String mappings) throws IOException {
        return checkCSVHaveCorrectRecords(multiPartFile,event,ticketingTypeIds,mappings);
    }

    private void validateAttendeeCSVHeaders(String[] headers,Map<String, String> mappings) {
        if (null != headers) {
            Set<String> headerSet = Arrays.stream(headers)
                    .map(String::trim)
                    .collect(Collectors.toSet());

            for (Map.Entry<String, String> entry : mappings.entrySet()) {
                String mappedColumn = entry.getValue();
                if (!headerSet.contains(mappedColumn)) {
                    NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_HEADER_MISSING_FOR_ATTENDEE_CSV;
                    exception.setErrorMessage(Constants.UPLOAD_FILE_HEADER_MISSING_FOR_ATTENDEE_CSV.replace("${columnName}", mappedColumn));
                    exception.setDeveloperMessage(Constants.UPLOAD_FILE_HEADER_MISSING_FOR_ATTENDEE_CSV.replace("${columnName}", mappedColumn));
                    throw new NotAcceptableException(exception);
                }
            }
        }
    }

    public Map<String, TicketHolderRequiredAttributes> getStringTicketHolderRequiredAttributesMap(Event event, DataType dataType) {
        List<TicketHolderRequiredAttributes> conditionalQuestions = ticketingPurchaseService.getAllConditionalQuestionByEventAndDataType(event, dataType);
        if(CollectionUtils.isEmpty(conditionalQuestions))return null;

        Map<String, TicketHolderRequiredAttributes> mapOfQues = conditionalQuestions.stream().collect(Collectors.toMap(TicketHolderRequiredAttributes::getName, Function.identity()));
        Map<String, TicketHolderRequiredAttributes> mapOfNameAndAttribute = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        mapOfNameAndAttribute.putAll(mapOfQues);

        return mapOfNameAndAttribute;
    }

    private String getKeyAttribute(String attributeName) {
        if(Constants.PHONE_NUMBER.equalsIgnoreCase(attributeName) || CELL_PHONE.equalsIgnoreCase(attributeName)){
            return Constants.STRING_PHONE_NUMBER;
        }else if(Constants.COUNTRY_CODE_TXT.equalsIgnoreCase(attributeName)){
            return Constants.STRING_COUNTRY_CODE;
        }else {
            return attributeName;
        }
    }

    public void executorServiceForCheckoutProcess(Event event, User hostUser, Ticketing ticketing, List<TicketingType> ticketingTypeList, List<AttendeeInfoDTO> attendeeListWithOrderIdZERO, List<AttendeeInfoDTO> attendeesWithOrderIdsAllowToCreateOrder, EventBillingAddOns billingAddOns){

        buyTicketsFromAttendeeUpload.runAttendeeUpload(event, hostUser, ticketing, ticketingTypeList, attendeeListWithOrderIdZERO, attendeesWithOrderIdsAllowToCreateOrder, billingAddOns);

    }

    @Override
    public void sendNotificationEmailToHostAboutAttendeeUploads(User user, Event event) {


        String userEmail = userService.getUserEmailById(user.getUserId());

        if (isNotBlank(userEmail)) {
            String desc = "Your attendee upload for "+event.getName()+" is complete and can now be reviewed in Attendees > Orders.";

            Set<String> receivers = new HashSet<>();
            receivers.add(userEmail);
            EmailMessage emailMessage = new EmailMessage(TemplateId.TRANSACTIONAL_DUMMY);
            emailMessage.setTemplate(desc);

            String subject = "Your Attendee Upload is Complete for "+event.getName();

            emailMessage.setSubject(subject);
            EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
            emailMessage.setReplyToEmail(eventDesignDetail.getReplyToEmail());
            emailMessage.setSenderNameFromEvent(eventDesignDetail.getEmailSenderName());
            emailMessage.setSenderMailFromEvent(eventDesignDetail.getNotificationEmail());
            emailMessage.setWhiteLabel(event.getWhiteLabel());
            Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();
            substitutionMap.put(Constants.EVENT_ID_UPPERCASE, event.getEventId());

            sendGridMailService.sendTemplateMail(emailMessage, receivers);

        } else {
            logger.error("sendNotificationEmailToHostAboutCsvUploads| User not found for userId {}",user.getUserId());
        }
    }

    @Override
    public List<AttendeeDetailsDto> checkoutProcessForAttendeeUpload(Event event, User hostUser, Ticketing ticketing, List<TicketingType> ticketingTypeList, List<AttendeeInfoDTO> attendeeListWithBlankExternalOrderId, List<AttendeeInfoDTO> attendeeListWithExternalOrderId, EventBillingAddOns eventBillingAddOns) {
        Map<String, List<AttendeeInfoDTO>> attendeeListMapByOrderId = attendeeListWithExternalOrderId.stream().collect(Collectors.groupingBy(AttendeeInfoDTO::getExternalOrderId));

        long processStartTime = System.currentTimeMillis();

        List<AttendeeDetailsDto> registeredAttendeeList = new ArrayList<>();

//      setting default values for attributes
        List<AttributeKeyValueDto> attributesForPurchaser = new ArrayList<>();
        List<NestedQuestionsDto> nestedQuestionsForPurchaser = new ArrayList<>();
        List<AttributeKeyValueDto> attributesForHolder = new ArrayList<>();
        List<NestedQuestionsDto> nestedQuestionsForHolder = new ArrayList<>();
        PurchaserBookingDto addOnAttributes = new PurchaserBookingDto();

        setAttributeDefaultValuesForHolderAndBuyerAndAddOn(event, ticketing, ticketingTypeList, attributesForPurchaser, nestedQuestionsForPurchaser, attributesForHolder, nestedQuestionsForHolder, addOnAttributes);

        for (String externalOrderId : attendeeListMapByOrderId.keySet()) {

            List<AttendeeInfoDTO> attendeeInfoDTOS = attendeeListMapByOrderId.get(externalOrderId);
            User buyerUser = getUserByEmail(attendeeInfoDTOS.get(0), event);

            //   prepareAttendeeProfile(event, attendeeInfoDTOS.get(0), buyerUser);//NOSONAR

            int numberOfTickets = attendeeInfoDTOS.stream().mapToInt(e -> e.getQuantity().intValue()).sum();
            boolean isPayLater = ticketingTypeList.get(0).isPayLater() && (attendeeInfoDTOS.stream().anyMatch(e -> TicketingOrder.OrderType.UNPAID.name().equals(e.getPaymentStatus())));
            OrderDto orderDto = createOrder(event, hostUser, ticketingTypeList, numberOfTickets,buyerUser, isPayLater, attendeeInfoDTOS.get(0).getUtmTrackSourceDto());
            try {
                logger.info("checkoutProcessForAttendeeUpload| Processing for event {} and user {}", event.getEventId(), buyerUser.getEmail());
                StaffTicketBookingDto ticketBookingDto = getStaffTicketBookingDtoWithOrderId(ticketing, ticketingTypeList, attendeeInfoDTOS, attributesForPurchaser, nestedQuestionsForPurchaser, attributesForHolder, nestedQuestionsForHolder);
                ticketBookingDto.setAddOnAttributes(addOnAttributes);
                ticketBookingDto.setPayLater(isPayLater);
                boolean isTrayIntegration = isTrayIntegration(attendeeInfoDTOS.get(0).getUploadSource());

                for(TicketingType ticketingType : ticketingTypeList) {
                    UploadAttendeeTrack uploadAttendeeTrack = new UploadAttendeeTrack();
                    uploadAttendeeTrack.setEventId(event.getEventId());
                    uploadAttendeeTrack.setTicketTypeId(ticketingType.getId());
                    uploadAttendeeTrack.setCreatedDate(new Date());
                    uploadAttendeeTrack.setExternalOrderId(attendeeInfoDTOS.get(0).getExternalOrderId());
                    uploadAttendeeTrack.setUploadSource(attendeeInfoDTOS.get(0).getUploadSource());
                    uploadAttendeeTrack.setOrderIdAccel(orderDto.getOrderId());
                    uploadAttendeeTrackService.save(uploadAttendeeTrack);

                }
                List<AttendeeDetailsDto> attendeeDetailsDtos = purchaseTicketFromStaffForExternalTransaction(event, hostUser, buyerUser, orderDto, ticketBookingDto, isTrayIntegration, !isTrayIntegration);
                if(!CollectionUtils.isEmpty(attendeeDetailsDtos)){
                    registeredAttendeeList.addAll(attendeeDetailsDtos);
                    eventBillingAddOnsService.save(eventBillingAddOns);
                }
            }
            catch (Exception e) {
                logger.info("checkoutProcessForAttendeeUpload| Error in purchaseTicketFromStaffForExternalTransaction for event {} and externalOrderId {} and message {}", event.getEventId(), externalOrderId, e.getMessage());
            }
        }

        attendeeListWithBlankExternalOrderId.forEach(attendee -> {

            User buyerUser = getUserByEmail(attendee, event);

            //  prepareAttendeeProfile(event, attendee, buyerUser);//NOSONAR
            try{
                logger.info("checkoutProcessForAttendeeUpload| Processing for event {} and user {}", event.getEventId(), attendee.getEmail());
                OrderDto orderDto = createOrder(event, hostUser, ticketingTypeList, attendee.getQuantity().intValue(),buyerUser, ticketingTypeList.get(0).isPayLater(), attendee.getUtmTrackSourceDto());

                StaffTicketBookingDto ticketBookingDto = getStaffTicketBookingDtoForWithOutOrderId(ticketing, ticketingTypeList, attendee, attributesForPurchaser, nestedQuestionsForPurchaser, attributesForHolder, nestedQuestionsForHolder);
                ticketBookingDto.setAddOnAttributes(addOnAttributes);

                boolean isTrayIntegration = isTrayIntegration(attendee.getUploadSource());
                List<AttendeeDetailsDto> attendeeDetailsDtos = purchaseTicketFromStaffForExternalTransaction(event, hostUser, buyerUser, orderDto, ticketBookingDto, isTrayIntegration, !isTrayIntegration);

                if(!CollectionUtils.isEmpty(attendeeDetailsDtos)){
                    registeredAttendeeList.addAll(attendeeDetailsDtos);
                }
            }
            catch (Exception e) {
                logger.info("checkoutProcessForAttendeeUpload| Error in purchaseTicketFromStaffForExternalTransaction for event {} and externalOrderId {} and message {}", event.getEventId(), attendee.getExternalOrderId(), e.getMessage());
            }
        });
        logger.info("checkoutProcessForAttendeeUpload| Time taken for checkoutProcessForAttendeeUpload for event {} is {} ms",event.getEventId(),System.currentTimeMillis()-processStartTime);
        return registeredAttendeeList;
    }

    private void setAttributeDefaultValuesForHolderAndBuyerAndAddOn(Event event, Ticketing ticketing, List<TicketingType> ticketingTypeList, List<AttributeKeyValueDto> attributesForPurchaser, List<NestedQuestionsDto> nestedQuestionsForPurchaser, List<AttributeKeyValueDto> attributesForHolder, List<NestedQuestionsDto> nestedQuestionsForHolder, PurchaserBookingDto addOnAttributes) {
        Long ticketTypeId = ticketingTypeList.stream().filter(t -> DataType.TICKET.equals(t.getDataType())).findFirst().get().getId();
        List<TicketHolderRequiredAttributes> ticketRequiredAttributesForPurchaser = ticketHolderRequiredAttributesService.findAllWithDefaultValueNotNullForPurchaser(event.getEventId(), ticketTypeId);
        setAttributeDefaultValuesForHolderAndBuyer(ticketRequiredAttributesForPurchaser, attributesForPurchaser, nestedQuestionsForPurchaser, true);

        if (ticketing.getCollectTicketHolderAttributes()) {
            List<TicketHolderRequiredAttributes> ticketRequiredAttributesForHolder = ticketHolderRequiredAttributesService.findAllWithDefaultValueNotNullForHolder(event.getEventId(), ticketTypeId);
            setAttributeDefaultValuesForHolderAndBuyer(ticketRequiredAttributesForHolder, attributesForHolder, nestedQuestionsForHolder, false);
        }

        if(ticketing.isCollectAddOnAttributes()){
            List<AttributeKeyValueDto> attributesForAddon = new ArrayList<>();
            List<NestedQuestionsDto> nestedQuestionsForAddon = new ArrayList<>();
            ticketingTypeList.stream()
                    .filter(t -> DataType.ADDON.equals(t.getDataType()))
                    .map(TicketingType::getId)
                    .findFirst()
                    .ifPresent(addonTicketTypeId -> {
                        List<TicketHolderRequiredAttributes> addonAttributes = ticketHolderRequiredAttributesService.findAllAddOnAttributeWithDefaultValueNotNullForPurchaser(event.getEventId(), addonTicketTypeId);
                        setAttributeDefaultValuesForHolderAndBuyer(addonAttributes, attributesForAddon, nestedQuestionsForAddon, true);
                    });
            addOnAttributes.setAttributes(attributesForAddon);
            addOnAttributes.setNestedQuestions(nestedQuestionsForAddon);
        }
    }

    private void setAttributeDefaultValuesForHolderAndBuyer(List<TicketHolderRequiredAttributes> ticketRequiredAttributes, List<AttributeKeyValueDto> attributes, List<NestedQuestionsDto> nestedQuestions, boolean isPurchaser) {
        String fieldName = isPurchaser ? DEFAULT_VALUE_FOR_PURCHASER : DEFAULT_VALUE_FOR_HOLDER;
        for (TicketHolderRequiredAttributes ticketHolderRequiredAttribute : ticketRequiredAttributes) {
            if (!ticketHolderRequiredAttribute.getAttributeValueType().equals(AttributeValueType.CONDITIONAL_QUE)) {
                AttributeKeyValueDto attributeKeyValueDto = new AttributeKeyValueDto();
                attributeKeyValueDto.setKey(ticketHolderRequiredAttribute.getName());
                attributeKeyValueDto.setValue(extractDefaultValue(isPurchaser ? ticketHolderRequiredAttribute.getDefaultValueJsonPurchaser() : ticketHolderRequiredAttribute.getDefaultValueJsonHolder(), fieldName, null));
                attributes.add(attributeKeyValueDto);
            } else {
                NestedQuestionsDto nestedQuestionsDto = new NestedQuestionsDto();
                nestedQuestionsDto.setId(ticketHolderRequiredAttribute.getId());
                nestedQuestionsDto.setName(ticketHolderRequiredAttribute.getName());
                nestedQuestionsDto.setValue(extractDefaultValue(isPurchaser ? ticketHolderRequiredAttribute.getDefaultValueJsonPurchaser() : ticketHolderRequiredAttribute.getDefaultValueJsonHolder(), fieldName, null));
                nestedQuestionsDto.setParentQueId(ticketHolderRequiredAttribute.getParentQuestionId());
                nestedQuestionsDto.setType(AttributeValueType.CONDITIONAL_QUE.getType());
                nestedQuestions.add(nestedQuestionsDto);
            }
        }
    }

    private boolean isTrayIntegration(UploadAttendeeTrack.UploadSource uploadSource) {
        if(uploadSource == null){
            return false;
        }
        List<UploadAttendeeTrack.UploadSource> trayUploadSource =List.of(UploadAttendeeTrack.UploadSource.TRAY_IO_CONNECTOR, UploadAttendeeTrack.UploadSource.SALESFORCE, UploadAttendeeTrack.UploadSource.MARKETO, UploadAttendeeTrack.UploadSource.HUBSPOT);
        return trayUploadSource.contains(uploadSource);
    }

    private String extractDefaultValue(String json, String fieldName, String defaultValue) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(json);
            return root.path(fieldName).asText(defaultValue); // null if missing
        } catch (Exception e) {
            logger.info("extractDefaultValue | Error parsing json: {}", json);
            return null;
        }
    }

    private List<AttendeeDetailsDto> purchaseTicketFromStaffForExternalTransaction(Event event, User hostUser, User buyerUser, OrderDto orderDto, StaffTicketBookingDto ticketBookingDto, boolean isTrayIntegration, boolean isExternalOrder) {
        try {
            return ticketingPurchaseService.purchaseTicketFromStaff(event, ticketBookingDto, orderDto.getOrderId(), buyerUser, "", hostUser, null,
                    "", false, false, DeliveryModes.EMAIL, CheckoutFrom.ATTENDEE_UPLOAD, isTrayIntegration, isExternalOrder);
        } catch (JAXBException | StripeException e) {
            logger.info("checkoutProcessForAttendeeUpload| Error in purchaseTicketFromStaffForExternalTransaction for event {} and ticketBookingDto {} and message {}",
                    event.getEventId(), ticketBookingDto, e.getMessage());
        } catch (Exception e) {
            logger.info("checkoutProcessForAttendeeUpload| Error in purchaseTicketFromStaffForExternalTransaction for event {} and ticketBookingDto {} and message {}",
                    event.getEventId(), ticketBookingDto, e.getMessage());
        }
        return null;
    }

    private User getUserByEmail(AttendeeInfoDTO attendee, Event event) {
        User buyerUser = roUserService.findByEmail(attendee.getEmail());
        if (null == buyerUser) {
            buyerUser = new User();
            buyerUser.setEmail(attendee.getEmail());
            buyerUser.setFirstName(attendee.getFirstName());
            buyerUser.setLastName(attendee.getLastName());
            buyerUser.setMostRecentEventId(event.getEventId());
            userService.save(buyerUser);
        }
        return buyerUser;
    }

    private StaffTicketBookingDto getStaffTicketBookingDtoWithOrderId(Ticketing ticketing, List<TicketingType> ticketingTypeList,
                                                                      List<AttendeeInfoDTO> attendee, List<AttributeKeyValueDto> attributesForPurchaser, List<NestedQuestionsDto> nestedQuestionsForPurchaser,
                                                                      List<AttributeKeyValueDto> attributesForHolder, List<NestedQuestionsDto> nestedQuestionsForHolder) {
        StaffTicketBookingDto ticketBookingDto = new StaffTicketBookingDto();

        ticketBookingDto.setClientDate(DateUtils.getFormattedDateString(new Date()));
        ticketBookingDto.setHasholderattributes(ticketing.getCollectTicketHolderAttributes());
        ticketBookingDto.setHolder(new ArrayList<>());

        List<HolderBookingDto> holder = new ArrayList<>();


        PurchaserBookingDto purchaserBookingDto = new PurchaserBookingDto();
        List<AttributeKeyValueDto> attributes = new ArrayList<>();

        AttributeKeyValueDto attributeFirstName = createAttributeKeyValue(FIRST_NAME, attendee.get(0).getFirstName());
        attributes.add(attributeFirstName);

        AttributeKeyValueDto attributeLastName =createAttributeKeyValue(LAST_NAME, attendee.get(0).getLastName());
        attributes.add(attributeLastName);

        AttributeKeyValueDto attributeEmailName = createAttributeKeyValue(EMAIL, attendee.get(0).getEmail());
        attributes.add(attributeEmailName);

        if (ticketing.getCollectTicketHolderAttributes()) {

            for(AttendeeInfoDTO infoDTO : attendee){
                List<AttributeKeyValueDto> dtosForHolder = new ArrayList<>();
                AttributeKeyValueDto attributeFirstNameHolder = createAttributeKeyValue(FIRST_NAME, infoDTO.getFirstName());
                dtosForHolder.add(attributeFirstNameHolder);

                AttributeKeyValueDto attributeLastNameHolder =createAttributeKeyValue(LAST_NAME, infoDTO.getLastName());
                dtosForHolder.add(attributeLastNameHolder);

                AttributeKeyValueDto attributeEmailNameHolder = createAttributeKeyValue(EMAIL, infoDTO.getEmail());
                dtosForHolder.add(attributeEmailNameHolder);

                if(!CollectionUtils.isEmpty(infoDTO.getAttributes())){
                    dtosForHolder.addAll(infoDTO.getAttributes());
                    if (!CollectionUtils.isEmpty(attributesForHolder)) {
                        Set<String> existingKeys = infoDTO.getAttributes().stream()
                                .map(AttributeKeyValueDto::getKey)
                                .collect(Collectors.toSet());

                        attributesForHolder.stream()
                                .filter(attr -> existingKeys.add(attr.getKey()))
                                .forEach(dtosForHolder::add);
                    }
                } else if (!CollectionUtils.isEmpty(attributesForHolder)) {
                    dtosForHolder.addAll(attributesForHolder);
                }

                HolderBookingDto holderBookingDto = new HolderBookingDto();
                holderBookingDto.setTickettypeid(null!=infoDTO.getTicketTypeId()?infoDTO.getTicketTypeId():ticketingTypeList.get(0).getId());
                holderBookingDto.setAttributes(dtosForHolder);
                holderBookingDto.setNestedQuestions(nestedQuestionsForHolder);
                holderBookingDto.setProfilePic(infoDTO.getProfilePic());
                holder.add(holderBookingDto);
            }
            ticketBookingDto.setHolder(holder);
        }

        if(!CollectionUtils.isEmpty(attendee.get(0).getAttributes())){

            List<String> attributeKeys = attendee.get(0).getAttributes().stream().map(AttributeKeyValueDto::getKey).collect(toList());

            if(!attributeKeys.contains(BILLING_ADDRESS)){
                attributes.add(createAttributeKeyValue(BILLING_ADDRESS, ADDRESS_SEPARATOR));
            }

            if(!attributeKeys.contains(SHIPPING_ADDRESS)){
                attributes.add(createAttributeKeyValue(SHIPPING_ADDRESS, ADDRESS_SEPARATOR));
            }

            attributes.addAll(attendee.get(0).getAttributes());
        }else {
            attributes.add(createAttributeKeyValue(BILLING_ADDRESS, ADDRESS_SEPARATOR));
            attributes.add(createAttributeKeyValue(SHIPPING_ADDRESS, ADDRESS_SEPARATOR));
        }

        if (!CollectionUtils.isEmpty(attributesForPurchaser)){
            attributes.addAll(attributesForPurchaser);
        }

        purchaserBookingDto.setAttributes(attributes);
        purchaserBookingDto.setNestedQuestions(nestedQuestionsForPurchaser);

        ticketBookingDto.setPurchaser(purchaserBookingDto);
        ticketBookingDto.setPaymentType(EXTERNAL_TRANSACTION);
        return ticketBookingDto;
    }

    private StaffTicketBookingDto getStaffTicketBookingDtoForWithOutOrderId(Ticketing ticketing, List<TicketingType> ticketingTypeList, AttendeeInfoDTO attendee,
                                                                            List<AttributeKeyValueDto> attributesForPurchaser, List<NestedQuestionsDto> nestedQuestionsForPurchaser,
                                                                            List<AttributeKeyValueDto> attributesForHolder, List<NestedQuestionsDto> nestedQuestionsForHolder) {
        StaffTicketBookingDto ticketBookingDto = new StaffTicketBookingDto();

        ticketBookingDto.setClientDate(DateUtils.getFormattedDateString(new Date()));
        ticketBookingDto.setHasholderattributes(ticketing.getCollectTicketHolderAttributes());

        List<HolderBookingDto> holder = new ArrayList<>();


        List<AttributeKeyValueDto> dtosForHolder = new ArrayList<>();
        List<NestedQuestionsDto> nestedQuestionsDtosForHolder = new ArrayList<>();
        List<NestedQuestionsDto> nestedQuestionsDtosForPurchaser = new ArrayList<>();
        ticketBookingDto.setHolder(new ArrayList<>());


        PurchaserBookingDto purchaserBookingDto = new PurchaserBookingDto();
        List<AttributeKeyValueDto> attributes = new ArrayList<>();

        AttributeKeyValueDto attributeFirstName = createAttributeKeyValue(FIRST_NAME, attendee.getFirstName());
        attributes.add(attributeFirstName);

        AttributeKeyValueDto attributeLastName =createAttributeKeyValue(LAST_NAME, attendee.getLastName());
        attributes.add(attributeLastName);

        AttributeKeyValueDto attributeEmailName = createAttributeKeyValue(EMAIL, attendee.getEmail());
        attributes.add(attributeEmailName);



        if (ticketing.getCollectTicketHolderAttributes()) {
            dtosForHolder.add(attributeFirstName);
            dtosForHolder.add(attributeLastName);
            dtosForHolder.add(attributeEmailName);

            if(!CollectionUtils.isEmpty(attendee.getAttributes())){
                dtosForHolder.addAll(attendee.getAttributes());
                if (!CollectionUtils.isEmpty(attributesForHolder)) {
                    Set<String> existingKeys = attendee.getAttributes().stream()
                            .map(AttributeKeyValueDto::getKey)
                            .collect(Collectors.toSet());

                    attributesForHolder.stream()
                            .filter(attr -> !existingKeys.contains(attr.getKey()))
                            .forEach(dtosForHolder::add);
                }
            } else if (!CollectionUtils.isEmpty(attributesForHolder)) {
                dtosForHolder.addAll(attributesForHolder);
            }

            if(!CollectionUtils.isEmpty(attendee.getNestedQuestions())){
                nestedQuestionsDtosForHolder.addAll(attendee.getNestedQuestions());
                if (!CollectionUtils.isEmpty(nestedQuestionsForHolder)) {
                    Set<Long> existingKeys = attendee.getNestedQuestions().stream()
                            .map(NestedQuestionsDto::getId)
                            .collect(Collectors.toSet());

                    nestedQuestionsForHolder.stream()
                            .filter(nestedQuestions -> !existingKeys.contains(nestedQuestions.getId()))
                            .forEach(nestedQuestionsDtosForHolder::add);
                }
            }else if (!CollectionUtils.isEmpty(nestedQuestionsForHolder)){
                nestedQuestionsDtosForHolder.addAll(nestedQuestionsForHolder);
            }

            HolderBookingDto holderBookingDto = new HolderBookingDto();
            holderBookingDto.setTickettypeid(ticketingTypeList.get(0).getId());
            holderBookingDto.setAttributes(dtosForHolder);
            holderBookingDto.setNestedQuestions(nestedQuestionsDtosForHolder);
            holder.add(holderBookingDto);
            ticketBookingDto.setHolder(holder);
        }



        if(!CollectionUtils.isEmpty(attendee.getAttributes())){

            List<String> attributeKeys = attendee.getAttributes().stream().map(AttributeKeyValueDto::getKey).collect(toList());

            if(!attributeKeys.contains(BILLING_ADDRESS)){
                attributes.add(createAttributeKeyValue(BILLING_ADDRESS, ADDRESS_SEPARATOR));
            }

            if(!attributeKeys.contains(SHIPPING_ADDRESS)){
                attributes.add(createAttributeKeyValue(SHIPPING_ADDRESS, ADDRESS_SEPARATOR));
            }

            attributes.addAll(attendee.getAttributes());
            if (!CollectionUtils.isEmpty(attributesForPurchaser)) {
                attributesForPurchaser.stream()
                        .filter(attr -> !attributeKeys.contains(attr.getKey()))
                        .forEach(attributes::add);
            }
        }else {
            attributes.add(createAttributeKeyValue(BILLING_ADDRESS, ADDRESS_SEPARATOR));
            attributes.add(createAttributeKeyValue(SHIPPING_ADDRESS, ADDRESS_SEPARATOR));
            if (!CollectionUtils.isEmpty(attributesForPurchaser)) {
                attributes.addAll(attributesForPurchaser);
            }
        }

        if(!CollectionUtils.isEmpty(attendee.getNestedQuestions())){
            nestedQuestionsDtosForPurchaser.addAll(attendee.getNestedQuestions());
            if (!CollectionUtils.isEmpty(nestedQuestionsForPurchaser)) {
                Set<Long> existingKeys = attendee.getNestedQuestions().stream()
                        .map(NestedQuestionsDto::getId)
                        .collect(Collectors.toSet());

                nestedQuestionsForPurchaser.stream()
                        .filter(nestedQuestions -> !existingKeys.contains(nestedQuestions.getId()))
                        .forEach(nestedQuestionsDtosForPurchaser::add);
            }
        }else if (!CollectionUtils.isEmpty(nestedQuestionsForPurchaser)){
            nestedQuestionsDtosForPurchaser.addAll(nestedQuestionsForPurchaser);
        }

        purchaserBookingDto.setAttributes(attributes);
        purchaserBookingDto.setNestedQuestions(nestedQuestionsDtosForPurchaser);
        ticketBookingDto.setPurchaser(purchaserBookingDto);
        ticketBookingDto.setPaymentType(EXTERNAL_TRANSACTION);
        return ticketBookingDto;
    }

    private AttributeKeyValueDto createAttributeKeyValue(String key, String value){
        AttributeKeyValueDto attributeBillingAddress = new AttributeKeyValueDto();
        attributeBillingAddress.setKey(key);
        attributeBillingAddress.setValue(value);
        return attributeBillingAddress;
    }

    private OrderDto createOrder(Event event, User hostUser, List<TicketingType> ticketingTypeList, int numberOfTickets, User buyerUser, boolean isPayLater, UTMTrackSourceDto utmTrackSourceDto) {
        List<TicketingOrderDto> orderTicketing = new ArrayList<>();
        TicketingOrderDto ticketingOrderDto;
        for(TicketingType ticketingType : ticketingTypeList) {
            ticketingOrderDto = new TicketingOrderDto();
            ticketingOrderDto.setNumberOfTicket(numberOfTickets);
            ticketingOrderDto.setTicketTypeId(ticketingType.getId());
            ticketingOrderDto.setPrice(0);
            orderTicketing.add(ticketingOrderDto);
        }

        return ticketingPurchaseService.bookTicket(event, orderTicketing, hostUser, new Date(),
                !isPayLater ? TicketingOrder.OrderType.EXTERNAL_TRANSACTION : TicketingOrder.OrderType.PAY_LATER, true,
                "This is external tickets",STRING_EMPTY,STRING_EMPTY,0L,checkUTMSource(utmTrackSourceDto), buyerUser);
    }

    private UTMTrackSourceDto checkUTMSource(UTMTrackSourceDto utmTrackSourceDto) {
        if(utmTrackSourceDto == null){
            return null;
        }
        return (StringUtils.isNotEmpty(utmTrackSourceDto.getUtmSource()) || StringUtils.isNotEmpty(utmTrackSourceDto.getUtmCampaign()) || StringUtils.isNotEmpty(utmTrackSourceDto.getUtmContent()) || StringUtils.isNotEmpty(utmTrackSourceDto.getUtmTerm()) || StringUtils.isNotEmpty(utmTrackSourceDto.getUtmMedium()) || StringUtils.isNotEmpty(utmTrackSourceDto.getUtmReferrer()))? utmTrackSourceDto:null;
    }

    private List<String> removeAttendeeListWhichHaveAlreadyOrderCreatedByOrderId(List<AttendeeInfoDTO> attendeeListWithNotBlankExternalOrderId, Event event, List<Long> ticketTypeId,
                                                                                 List<AttendeeInfoDTO> attendeeListWithBlankOrderId) {

        List<String> attendeeInfoHaveNotOrderCreated = new ArrayList<>();

        List<String> listOfOrderIdsForCurrentUpload = attendeeListWithNotBlankExternalOrderId.stream().map(AttendeeInfoDTO::getExternalOrderId).collect(toList());

        List<String> externalOrderIdsFromDb = uploadAttendeeTrackService.findExternalOrderIdsForEventAndTicketTypeIds(event.getEventId(), ticketTypeId);

        listOfOrderIdsForCurrentUpload.removeAll(externalOrderIdsFromDb);

        if (CollectionUtils.isEmpty(listOfOrderIdsForCurrentUpload) && CollectionUtils.isEmpty(attendeeListWithBlankOrderId)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ALL_THE_TRANSACTIONIDS_FROM_HAVE_ALREADY_ORDER_CREATED);
        }

        if (!CollectionUtils.isEmpty(listOfOrderIdsForCurrentUpload)) {
            attendeeInfoHaveNotOrderCreated.addAll(listOfOrderIdsForCurrentUpload);
        }
        return attendeeInfoHaveNotOrderCreated;
    }

    private void validationForNumberOfTicketsAreAvailableToSold(List<TicketingType> ticketingTypeList, List<AttendeeInfoDTO> infoDTOList) {

        for(TicketingType ticketingType: ticketingTypeList) {
            Long quantityOfTicketsFromCSV = infoDTOList.stream().filter(e-> e.getTicketTypeId().equals(ticketingType.getId())).mapToLong(AttendeeInfoDTO::getQuantity).sum();

            BigDecimal ticketSoldCount = eventCommonRepoService.findSoldCountByTicketTypeId(ticketingType.getId());
            Long soldCount = NumberUtils.isNumberGreaterThanZero(ticketSoldCount) ? ticketSoldCount.longValue() : 0L;

            long numberOfTickets = TicketBundleType.INDIVIDUAL_TICKET.equals(ticketingType.getBundleType())
                    ? ticketingType.getNumberOfTickets() : (ticketingType.getNumberOfTickets() * ticketingType.getNumberOfTicketPerTable());

            long remainingTicket = numberOfTickets - soldCount;

            logger.info("Total quantity of tickets in CSV is more than remaining tickets, Sold Count {}, Remaining Ticket {}, Quantity of Tickets from CSV {}",
                    soldCount, remainingTicket, quantityOfTicketsFromCSV);

            if (quantityOfTicketsFromCSV > remainingTicket) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TOTAL_QUANTITY_OF_TICKETS_IN_CSV_IS_MORE_THAN_REMAINING_TICKETS);
            }
        }
    }

    @Override
    public void purchaseTicketsFromCventIntegration(Integration integration, Event event, List<AttendeeInfoDTO> infoDTOList, List<TicketingType> ticketingTypes){
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<Staff> staffList = staffService.findStaffByEventAndRole(event, StaffRole.admin);
        User eventAdminUser = null;
        if(staffList != null && !staffList.isEmpty()){
            Staff staff = staffList.get(0);
            eventAdminUser = staff.getUser();
        }

        List<String> attendeeEmails = infoDTOList.stream().map(AttendeeInfoDTO::getEmail).collect(toList());

        if(CollectionUtils.isEmpty(ticketingTypes)){
            ticketingTypes.add(integration.getTicketingType());
        }

        if ((ticketing.getUniqueTicketHolderEmail() || ticketing.isUniqueTicketBuyerEmail()) && !attendeeEmails.isEmpty()){
            List<AttendeeInfoDTO> filteredInfoDTOList = removeDuplicate(infoDTOList, event, ticketing);
            if (!filteredInfoDTOList.isEmpty()) {
                this.uploadAttendee(event, eventAdminUser, false, ticketing, filteredInfoDTOList, ticketingTypes, UploadAttendeeTrack.UploadSource.CVENT);
            } else {
                logger.info("No valid attendees to upload after filtering duplicates.");
            }
        }
        else{
            this.uploadAttendee(event, eventAdminUser, false, ticketing, infoDTOList, ticketingTypes, UploadAttendeeTrack.UploadSource.CVENT);
        }
    }

    @Override
    public void purchaseTicketsFromSchedIntegration(Integration integration, Event event, List<AttendeeInfoDTO> infoDTOList){
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<Staff> staffList = staffService.findStaffByEventAndRole(event, StaffRole.admin);
        User eventAdminUser = null;
        if(staffList != null && !staffList.isEmpty()){
            Staff staff = staffList.get(0);
            eventAdminUser = staff.getUser();
        }

        List<String> attendeeEmails = infoDTOList.stream().map(AttendeeInfoDTO::getEmail).collect(toList());

        if ((ticketing.getUniqueTicketHolderEmail() || ticketing.isUniqueTicketBuyerEmail()) && !attendeeEmails.isEmpty()){
            List<AttendeeInfoDTO> filteredInfoDTOList = removeDuplicate(infoDTOList, event, ticketing);
            if (!filteredInfoDTOList.isEmpty()) {
                this.uploadAttendee(event, eventAdminUser, false, ticketing, filteredInfoDTOList, new ArrayList<>(List.of(integration.getTicketingType())), UploadAttendeeTrack.UploadSource.SCHED);
            } else {
                logger.info("No valid attendees to upload after filtering duplicates.");
            }
        }
        else{
            this.uploadAttendee(event, eventAdminUser, false, ticketing, infoDTOList, new ArrayList<>(List.of(integration.getTicketingType())), UploadAttendeeTrack.UploadSource.SCHED);
        }
    }

    @Override
    public void purchaseTicketsFromZapierIntegration(TicketingType ticketingType, Event event, List<AttendeeInfoDTO> infoDTOList){
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<Staff> staffList = staffService.findStaffByEventAndRole(event, StaffRole.admin);
        User eventAdminUser = null;
        if(staffList != null && !staffList.isEmpty()){
            Staff staff = staffList.get(0);
            eventAdminUser = staff.getUser();
        }
        List<String> attendeeEmails = infoDTOList.stream().map(AttendeeInfoDTO::getEmail).collect(toList());

        if ((ticketing.getUniqueTicketHolderEmail() || ticketing.isUniqueTicketBuyerEmail()) && !attendeeEmails.isEmpty()){
            List<AttendeeInfoDTO> filteredInfoDTOList = removeDuplicate(infoDTOList, event, ticketing);

            if (!filteredInfoDTOList.isEmpty()) {
                this.uploadAttendee(event, eventAdminUser, false, ticketing, filteredInfoDTOList, new ArrayList<>(List.of(ticketingType)), UploadAttendeeTrack.UploadSource.ZAPIER);
            } else {
                logger.info("No valid attendees to upload after filtering duplicates.");
            }
        }
        else{
            this.uploadAttendee(event, eventAdminUser, false, ticketing, infoDTOList, new ArrayList<>(List.of(ticketingType)), UploadAttendeeTrack.UploadSource.ZAPIER);
        }
    }

    @Override
    public void purchaseTicketsFromTrayConnectorIntegration(TicketingType ticketingType, Event event, List<AttendeeInfoDTO> infoDTOList) {
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<Staff> staffList = staffService.findStaffByEventAndRole(event, StaffRole.admin);
        User eventAdminUser = null;
        if(staffList != null && !staffList.isEmpty()){
            Staff staff = staffList.get(0);
            eventAdminUser = staff.getUser();
        }

        List<String> attendeeEmails = infoDTOList.stream().map(AttendeeInfoDTO::getEmail).collect(toList());

        if ((ticketing.getUniqueTicketHolderEmail() || ticketing.isUniqueTicketBuyerEmail()) && !attendeeEmails.isEmpty()){
            List<AttendeeInfoDTO> filteredInfoDTOList = removeDuplicate(infoDTOList, event, ticketing);

            if (!filteredInfoDTOList.isEmpty()) {
                this.uploadAttendee(event, eventAdminUser, false, ticketing, filteredInfoDTOList,
                        new ArrayList<>(List.of(ticketingType)),
                        UploadAttendeeTrack.UploadSource.TRAY_IO_CONNECTOR);
            } else {
                logger.info("No valid attendees to upload after filtering duplicates.");
            }
        }
        else{
            this.uploadAttendee(event, eventAdminUser, false, ticketing, infoDTOList,
                    new ArrayList<>(List.of(ticketingType)),
                    UploadAttendeeTrack.UploadSource.TRAY_IO_CONNECTOR);
        }
    }

    @Override
    public void purchaseTicketsFromCadmiumIntegration(TicketingType ticketingType, Event event, List<AttendeeInfoDTO> infoDTOList){
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<Staff> staffList = staffService.findStaffByEventAndRole(event, StaffRole.admin);
        User eventAdminUser = null;
        if(staffList != null && !staffList.isEmpty()){
            Staff staff = staffList.get(0);
            eventAdminUser = staff.getUser();
        }

        List<String> attendeeEmails = infoDTOList.stream().map(AttendeeInfoDTO::getEmail).collect(toList());

        if ((ticketing.getUniqueTicketHolderEmail() || ticketing.isUniqueTicketBuyerEmail()) && !attendeeEmails.isEmpty()){
            List<AttendeeInfoDTO> filteredInfoDTOList = removeDuplicate(infoDTOList, event, ticketing);

            if (!filteredInfoDTOList.isEmpty()) {
                this.uploadAttendee(event, eventAdminUser, false, ticketing, filteredInfoDTOList, List.of(ticketingType), UploadAttendeeTrack.UploadSource.CADMIUM);
            } else {
                logger.info("No valid attendees to upload after filtering duplicates.");
            }
        }
        else{
            this.uploadAttendee(event, eventAdminUser, false, ticketing, infoDTOList, List.of(ticketingType), UploadAttendeeTrack.UploadSource.CADMIUM);
        }
    }

    @Override
    public List<String> getAttendeeUploadDefaultFields(Long eventId, Long recurringEventId) {
        recurringEventId = NumberUtils.isNumberGreaterThanZero(recurringEventId) ? recurringEventId : null;
        List<String> requiredAttributeNames = new ArrayList<>();
        requiredAttributeNames.add(Constants.TRANSACTION_ID);
        requiredAttributeNames.addAll(ticketHolderRequiredAttributesService.findAllRequiredAttributeNames(eventId,recurringEventId));
        return requiredAttributeNames;
    }

    /**
     * Finds duplicate strings in a list
     * @param list The list to check for duplicates
     * @return A set of duplicate strings found in the list
     */
    private Set<String> findDuplicatesInList(List<String> list) {
        Set<String> items = new HashSet<>();
        return list.stream()
                .filter(item -> !items.add(item))
                .collect(Collectors.toSet());
    }

    private List<AttendeeInfoDTO> removeDuplicate(List<AttendeeInfoDTO> infoDTOList, Event event, Ticketing ticketing) {

        if (infoDTOList == null || infoDTOList.isEmpty()) return Collections.emptyList();


        List<String> attendeeEmails = infoDTOList.stream().map(AttendeeInfoDTO::getEmail).filter(Objects::nonNull).collect(toList());

        // Find duplicates within the new holder emails
        Set<String> duplicatesInNewEmails = findDuplicatesInList(attendeeEmails);

        // Find emails that already exist in the database
        Set<String> existingEmails = new HashSet<>();
        if(ticketing.getUniqueTicketHolderEmail()){
            existingEmails.addAll(eventCommonRepoService.findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(
                    event.getEventId(), attendeeEmails));
        }
        if(ticketing.isUniqueTicketBuyerEmail()){
            existingEmails.addAll(eventCommonRepoService.findBuyerEmailsByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(
                    event.getEventId(), attendeeEmails));
        }

        // Combine both sets of duplicates
        Set<String> duplicateEmails = new HashSet<>(duplicatesInNewEmails);
        duplicateEmails.addAll(existingEmails);

        List<AttendeeInfoDTO> filteredInfoDTOList = infoDTOList.stream()
                .filter(dto -> dto.getEmail() != null && !existingEmails.contains(dto.getEmail()))
                .collect(Collectors.toList());


        if (!duplicateEmails.isEmpty()) {
            logger.info("Duplicate emails found and skipped: {}", duplicateEmails);
        }
        return filteredInfoDTOList;
    }
}
