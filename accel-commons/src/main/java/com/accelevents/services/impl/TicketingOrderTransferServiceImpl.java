package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.repositories.EventPlanConfigRepository;
import com.accelevents.common.dto.TicketExchangeMappingRuleDTO;
import com.accelevents.common.dto.TicketTransferTypesDto;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.*;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.messages.MagicLinkType;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.impl.SendGridMailPrepareServiceImpl;
import com.accelevents.repositories.BeeFreeRepository;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingOrderRepository;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.service.PDFCreator;
import com.accelevents.service.TicketingPdfCreator;
import com.accelevents.service.impl.PDFCreatorImpl;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.ticketing.dto.RefundInfo;
import com.accelevents.ticketing.dto.TickeingInfo;
import com.accelevents.ticketing.dto.TickeingTableEmail;
import com.accelevents.utils.*;
import com.itextpdf.text.DocumentException;
import com.sendgrid.Attachments;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import freemarker.template.TemplateException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.bind.JAXBException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.EventFormat.*;
import static com.accelevents.enums.PlanConfigNames.*;
import static com.accelevents.exceptions.NotAcceptableException.PaymentCreationExceptionMsg.OTHER_PAYMENT_METHOD_NOT_ALLOWED_FOR_PARTIAL_PAID_ORDER;
import static com.accelevents.helpers.TemplateId.BEFREE_DEFAULT_REMINDER_MINIFIED;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.getEventPath;
import static com.accelevents.utils.GeneralUtils.getOrderUrl;
import static com.accelevents.utils.SecurityUtils.encode;

@Service
public class TicketingOrderTransferServiceImpl implements TicketingOrderTransferService {
    private static final Logger log = LoggerFactory.getLogger(TicketingOrderTransferServiceImpl.class);

    @Autowired
    private TicketingOrderRepository ticketingOrderRepository;

    @Autowired
    private TicketingOrderRepoService ticketingOrderRepoService;

    @Autowired
    private TicketingTypeService ticketingTypeService;

    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;

    @Autowired
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Autowired
    private TicketingOrderService ticketingOrderService;

    @Autowired
    private EventTicketsService eventTicketsService;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;

    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Autowired
    private SendGridMailPrepareServiceImpl sendGridMailPrepareServiceImpl;
    @Autowired
    private AutoLoginService autoLoginService;
    @Autowired
    private ROUserService roUserService;

    @Autowired
    private ServiceHelper serviceHelper;

    @Autowired
    private ImageConfiguration imageConfiguration;
    @Autowired
    private EventPlanConfigRepository eventPlanConfigRepository;

    private String apiBaseUrl = "https://api.accelevents.com";

    @Autowired
    private ConfirmationEmailService confirmationEmailService;

    @Value("${uiBaseurl}")
    private String uiBaseurl;

    private EmailLogoImageHelper emailLogoImageHelper;

    @Autowired
    private BeeFreeRepository beeFreeRepository;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private EventDesignDetailService eventDesignDetailService;

    @Autowired
    private SendGridMailService sendGridMailService;

    @Autowired
    private AttendeeSequenceNumberService attendeeSequenceNumberService;

    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private EventTicketTransferDetailService eventTicketTransferDetailService;

    @Autowired
    private TicketingRefundService ticketingRefundService;


    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;
    @Autowired
    private TableRefundService tableRefundService;

    @Autowired
    private StripeService stripeService;

    @Autowired
    private SalesTaxService salesTaxService;

    @Autowired
    private VatTaxService vatTaxService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    @Autowired
    private TicketingPurchaseService ticketingPurchaseService;

    @Autowired
    private StripeTransactionService stripeTransactionService;
    @Autowired
    private TicketExchangeRuleService ticketExchangeRuleService;

    @Autowired
    private TicketingCouponService ticketingCouponService;

    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;

    private TicketingPdfCreator ticketingPdfCreator = TicketingPdfCreator.getInstance();


    protected void checkQuantityAvailableToTransfer(TicketingType ticketingType, long numberOfTicket) {

        if (numberOfTicket > ticketingStatisticsService.getRemainingTicketCount(ticketingType)) {
            if (null != ticketingType.getRecurringEventId()) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_NOT_AVAILABLE);
            }
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_NOT_AVAILABLE_SALE);
        }
    }

    private TicketingType getNewTicketingType(long newTicketTypeId, TicketingType oldRecurringEventTicketType) {
        Long recurringEventId = oldRecurringEventTicketType.getRecurringEventId();
        if (recurringEventId != null) {
            TicketingType newRecurringEventTicketType = ticketingTypeService.findTicketTypeWithSameCreatedFromAndInRecurringEventId(newTicketTypeId, recurringEventId);
            if (null == newRecurringEventTicketType) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TYPE_NOT_EXIST);
            }
            return newRecurringEventTicketType;
        }
        return ticketingTypeService.findById(newTicketTypeId).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.TICKET_TYPE_NOT_FOUND));
    }

    private EventTickets getValidEventTicket(long eventTicketId) {
        EventTickets eventTicket = eventTicketsService.findEventTicketById(eventTicketId).orElseThrow(() -> new NotFoundException(NotFoundException.SessionSpeakerNotFound.EVENT_TICKET_NOT_FOUND));
        if (TicketStatus.DELETED.equals(eventTicket.getTicketStatus())) {
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.TICKET_NOT_FOUND);
        }
        if(TicketStatus.CANCELED.equals(eventTicket.getTicketStatus()) || TicketPaymentStatus.REFUNDED.equals(eventTicket.getTicketPaymentStatus()) || TicketPaymentStatus.PARTIALLY_REFUNDED.equals(eventTicket.getTicketPaymentStatus())){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_ALREADY_CANCELED_OR_REFUNDED);
        }
        return eventTicket;
    }
    private TicketingOrder getValidTicketOrder(long orderId) {
        TicketingOrder ticketingOrder = ticketingOrderService.findByid(orderId);
        if (TicketingOrder.TicketingOrderStatus.PAID_DELETE.equals(ticketingOrder.getStatus()) || TicketingOrder.TicketingOrderStatus.UNPAID_DELETE.equals(ticketingOrder.getStatus())  ) {
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND);
        }
        if(TicketingOrder.TicketingOrderStatus.PROCESSING.equals(ticketingOrder.getStatus())){
            throw new NotAcceptableException(NotAcceptableException.PaymentCreationExceptionMsg.PREVIOUS_PAYMENT_STILL_IN_PROCESSING);
        }
        return ticketingOrder;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void transferEventTicket(Long orderId,TicketExchangeDto ticketExchangeDto, Event event, User user) throws TemplateException, JAXBException, DocumentException, IOException, StripeException, ApiException {
        EventTickets eventTicket = getValidEventTicket(ticketExchangeDto.getEventTicketId());
        validateTicketExchangeIsAllowed(event);
        boolean isComplementaryOrder = TicketingOrder.OrderType.COMPLIMENTARY.equals(eventTicket.getTicketingOrder().getOrderType()) || COMPLIMENTARY.equals(ticketExchangeDto.getPaymentDetails().getPaymentType()) ;
        boolean is100PercentDiscount = ( eventTicket.getTicketingTypeId().getTicketType().equals(TicketType.DONATION) || eventTicket.getTicketingTypeId().getTicketType().equals(TicketType.PAID)) && eventTicket.getTicketPrice() == 0 ;
        boolean isPaidTicketWithZeroAmount =  is100PercentDiscount;
        TicketingType oldTicketingType = eventTicket.getTicketingTypeId();
        // Need to find new Ticket type details from event_ticket_type table
        TicketingType newTicketingType = getNewTicketingType(ticketExchangeDto.getNewTicketTypeId(), eventTicket.getTicketingTypeId());
        // Need to validate new Ticket type for available tickets from event_ticket_type table
        checkQuantityAvailableToTransfer(newTicketingType, 1);
        if(isComplementaryOrder){
            // Call for Complementary Order exchange
            transferComplementaryEventTicket(orderId, eventTicket, oldTicketingType, newTicketingType, event, user,ticketExchangeDto.getSentMailToAttendee(), ticketExchangeDto.getPaymentDetails().getPaymentType());
        }else if ((TicketType.FREE.equals(oldTicketingType.getTicketType()) || isPaidTicketWithZeroAmount) && TicketType.FREE.equals(newTicketingType.getTicketType())) {
            // Call Free to free
            transferFreeEventTicket(orderId, eventTicket, oldTicketingType, newTicketingType, event, user,isPaidTicketWithZeroAmount,ticketExchangeDto.getSentMailToAttendee());
        } else if ((TicketType.FREE.equals(oldTicketingType.getTicketType())|| isPaidTicketWithZeroAmount) && TicketType.PAID.equals(newTicketingType.getTicketType())) {
            // Call Free to paid
            transferFreeEventTicketToPaidTicket(orderId, eventTicket, oldTicketingType, newTicketingType, event, user,isPaidTicketWithZeroAmount,ticketExchangeDto);
        } else if ((TicketType.PAID.equals(oldTicketingType.getTicketType()) || TicketType.DONATION.equals(oldTicketingType.getTicketType())) && TicketType.FREE.equals(newTicketingType.getTicketType())) {
            // Call Paid to free
            transferPaidEventTicketToFreeTicket(orderId, eventTicket, oldTicketingType, newTicketingType, event, user,ticketExchangeDto.getSentMailToAttendee());
        } else if ((TicketType.PAID.equals(oldTicketingType.getTicketType()) || TicketType.DONATION.equals(oldTicketingType.getTicketType())) && (TicketType.PAID.equals(newTicketingType.getTicketType()) ||  TicketType.DONATION.equals(newTicketingType.getTicketType()))) {
            // Call Paid to paid
            transferPaidEventTicketToPaidTicket(orderId, eventTicket, oldTicketingType, newTicketingType, event, user, ticketExchangeDto);
        }

        afterTaskIntegrationTriggerService.onTicketTypeTransferEvent(eventTicket, oldTicketingType);

    }

    private void transferComplementaryEventTicket(Long orderId, EventTickets eventTicket, TicketingType oldTicketingType, TicketingType newTicketingType, Event event, User user,boolean isMailSendToHolder, String paymentType) {
        log.info("transferComplementaryEventTicket -> transferFreeEventTicket -> eventTicketId -> {} -> oldTicketTypeId -> {} -> newTicketTypeId -> {} -> eventId -> {} -> userId -> {}",
                eventTicket.getId(), oldTicketingType.getId(), newTicketingType.getId(), event.getEventId(), user.getUserId());
        TicketingOrder ticketingOrder = getValidTicketOrder(orderId);

        // update ticketing_order_manager table with new ticket type id and status
        updateTicketTypeInTicketingOrderManager(ticketingOrder, newTicketingType, eventTicket);
        // update event_ticket  table with new ticket type id available tickets
        ArrayList<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicket.setTicketingTypeId(newTicketingType);
        if(COMPLIMENTARY.equals(paymentType)) {
            eventTicket.setTicketStatus(TicketStatus.REGISTERED);
            eventTicket.setTicketPaymentStatus(TicketPaymentStatus.PAID);
            eventTicketsRepository.save(eventTicket);

            // Update the order status for non-complimentary orders where tickets were exchanged without payment.
            updateTicketOrderBasedOnTicketTypeChanged(ticketingOrder);
        }else {
            eventTicketsRepository.save(eventTicket);
            eventTicket.setChargeId(null);
        }

        eventTicketTransferDetailService.storeFreeEventTicketTransferDetail(orderId, eventTicket, oldTicketingType.getId(), newTicketingType.getId(), event, user);
        eventTicketsListForEmail.add(eventTicket);

        // Add log in order audit log table for ticket transfer
        StringBuilder refundReason = new StringBuilder();
        refundReason.append("Purchased order had been updated from ").append(oldTicketingType.getTicketTypeName()).append(" to ").append(newTicketingType.getTicketTypeName());
        ticketingRefundService.orderAuditLog(event, user, orderId, STRING_EMPTY, STRING_EMPTY, "" + refundReason.toString() + " by " + user.getFirstName() + " " + user.getLastName());

        //Sent mail to attendee for update ticketing type
        TicketTransferTypesDto ticketTransferTypesDto=new TicketTransferTypesDto();
        ticketTransferTypesDto.setComplementaryTicketTransfer(true);
        Ticketing ticketing = ticketingService.findByEvent(event);
        String mailSubject = "Your Updated Order Confirmation for " + event.getName();
        handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, eventTicket,
                false, false, mailSubject,true,ticketTransferTypesDto,isMailSendToHolder, user);
    }

    public void updateTicketTypeInTicketingOrderManager(TicketingOrder ticketingOrder, TicketingType newTicketingType, EventTickets eventTicket) {
        TicketingOrderManager oldTicketingOrderManager = ticketingOrderManagerService.getTicketTypeByOrderIdAndTicketTypeId(eventTicket.getTicketingTypeId(), ticketingOrder);
        if(oldTicketingOrderManager == null){
            log.info("TicketingOrderTransferServiceImpl | updateTicketTypeInTicketingOrderManager | not found");
            throw new NotFoundException(NotFoundException.NotFound.TICKET_TYPE_NOT_FOUND);
        }
        boolean isDiscountedTicket = eventTicket.getDiscountAmount() > 0;
        TicketingOrderManager newTicketingOrderManager = ticketingOrderManagerService.getTicketTypeByOrderIdAndTicketTypeId(newTicketingType, ticketingOrder);
        if (oldTicketingOrderManager.getNumberofticket() > 1) {
            oldTicketingOrderManager.setNumberofticket(oldTicketingOrderManager.getNumberofticket() - 1);
            if(isDiscountedTicket){
                oldTicketingOrderManager.setNumberOfDiscountedTicket(Math.max(0,oldTicketingOrderManager.getNumberOfDiscountedTicket() - 1));
            }
            if (newTicketingOrderManager == null) {
                newTicketingOrderManager = (TicketingOrderManager) oldTicketingOrderManager.clone();
                newTicketingOrderManager.setId(0);
                newTicketingOrderManager.setTicketType(newTicketingType);
                newTicketingOrderManager.setNumberofticket(1);
                newTicketingOrderManager.setNumberOfDiscountedTicket(0);
            } else {
                newTicketingOrderManager.setNumberofticket(newTicketingOrderManager.getNumberofticket() + 1);
            }

            ticketingOrderManagerService.save(newTicketingOrderManager);
            ticketingOrderManagerService.save(oldTicketingOrderManager);
        } else {
            if (newTicketingOrderManager == null) {
                oldTicketingOrderManager.setTicketType(newTicketingType);
                oldTicketingOrderManager.setNumberOfDiscountedTicket(0);
                ticketingOrderManagerService.save(oldTicketingOrderManager);

            } else {
                newTicketingOrderManager.setNumberofticket(newTicketingOrderManager.getNumberofticket() + 1);
                ticketingOrderManagerService.save(newTicketingOrderManager);
                ticketingOrderManagerService.deleteByTicketTypeAndTicketingOrder(oldTicketingOrderManager.getTicketType(), ticketingOrder);
            }
        }
    }

    private void updateTicketOrderBasedOnTicketTypeChanged(TicketingOrder ticketingOrder) {
        List<TicketingOrderManager> ticketingOrderManagerList = ticketingOrderManagerService.getAllByOrderId(ticketingOrder);
        boolean allFreeTicket = ticketingOrderManagerList.stream().allMatch(ticketType -> ticketType.getTicketType().getTicketType().equals(TicketType.FREE));
        int numberOfDiscountedTicket = ticketingOrderManagerList.stream().mapToInt(TicketingOrderManager::getNumberOfDiscountedTicket).sum();
        List<EventTickets> eventTicketsList = eventTicketsService.findEventTicketsByOrderId(ticketingOrder.getId());
        boolean allTicketArePaid = eventTicketsList.stream().filter(e -> !e.getTicketingTypeId().getTicketType().equals(TicketType.FREE)).allMatch(eventTickets -> TicketPaymentStatus.PAID.equals(eventTickets.getTicketPaymentStatus()));
        boolean allTicketAreUnPaid = eventTicketsList.stream().filter(e -> !e.getTicketingTypeId().getTicketType().equals(TicketType.FREE)).allMatch(eventTickets -> TicketPaymentStatus.UNPAID.equals(eventTickets.getTicketPaymentStatus()));
        if (allFreeTicket) {
            ticketingOrder.setTicketingCoupon(null);
            ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
            ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        } else if (allTicketArePaid) {
            ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        }else if(allTicketAreUnPaid){
            ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        }
        if(numberOfDiscountedTicket == 0){
            ticketingOrder.setTicketingCoupon(null);
        }
        ticketingOrderRepository.save(ticketingOrder);
    }

    public void transferFreeEventTicket(Long orderId, EventTickets eventTickets, TicketingType oldTicketingType, TicketingType newTicketingType, Event event, User user,boolean isPaidTicketWithZeroAmount,boolean isMailSendToHolder) throws TemplateException, JAXBException, DocumentException, IOException {
        log.info("TicketingOrderTransferServiceImpl -> transferFreeEventTicket -> eventTicketId -> {} -> oldTicketTypeId -> {} -> newTicketTypeId -> {} -> eventId -> {} -> userId -> {}",
                eventTickets.getId(), oldTicketingType.getId(), newTicketingType.getId(), event.getEventId(), user.getUserId());
        TicketingOrder ticketingOrder = getValidTicketOrder(orderId);

        // update ticketing_order_manager table with new ticket type id and status
        updateTicketTypeInTicketingOrderManager(ticketingOrder, newTicketingType, eventTickets);

        // update event_ticket  table with new ticket type id available tickets
        eventTicketTransferDetailService.storeFreeEventTicketTransferDetail(orderId, eventTickets, oldTicketingType.getId(), newTicketingType.getId(), event, user);
        ArrayList<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTickets.setTicketingTypeId(newTicketingType);
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setChargeId(null);
        eventTickets.setTicketPrice(0);
        eventTickets.setPaidAmount(0);
        eventTickets.setAeFeeAmount(0);
        eventTickets.setSalesTaxFee(0);
        eventTickets.setVatTaxFee(0);
        eventTickets.setWlAFeeAmount(0);
        eventTickets.setWlBFeeAmount(0);
        eventTickets.setDiscountAmount(0);
        eraseEventTicketFees(eventTickets);
        eventTicketsRepository.save(eventTickets);
        eventTicketsListForEmail.add(eventTickets);

        //Sent mail to attendee for update ticketing type
        updateTicketOrderBasedOnTicketTypeChanged(ticketingOrder);

        // Add log in order audit log table for ticket transfer
        StringBuilder refundReason = new StringBuilder();
        refundReason.append("Purchased order had been updated from ").append(oldTicketingType.getTicketTypeName()).append(" to ").append(newTicketingType.getTicketTypeName());
        ticketingRefundService.orderAuditLog(event, user, orderId, STRING_EMPTY, STRING_EMPTY, "" + refundReason.toString() + " by " + user.getFirstName() + " " + user.getLastName());

        //Sent mail to attendee for update ticketing type
        TicketTransferTypesDto ticketTransferTypesDto=new TicketTransferTypesDto();
        ticketTransferTypesDto.setFreeToFreeTicketTransfer(true);
        Ticketing ticketing = ticketingService.findByEvent(event);
        String mailSubject = "Your Updated Order Confirmation for " + event.getName();
        handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, eventTickets,
                false, false, mailSubject,isPaidTicketWithZeroAmount,ticketTransferTypesDto,isMailSendToHolder, user);
    }

    public void transferPaidEventTicketToFreeTicket(Long orderId, EventTickets eventTickets, TicketingType oldTicketingType, TicketingType newTicketingType, Event event, User user,boolean isMailSendToHolder) throws TemplateException, JAXBException, DocumentException, IOException {

        log.info("TicketingOrderTransferServiceImpl | transferFreeEventTicket | eventTickets | {} | oldTicketTypeId | {} | newTicketTypeId | {} | eventId | {} | userId | {}",
                eventTickets.getId(), oldTicketingType.getId(), newTicketingType.getId(), event.getEventId(), user.getUserId());
        TicketingOrder ticketingOrder = getValidTicketOrder(orderId);

        // Need to validate new Ticket type for available tickets from event_ticket_type table
        double refundAmount = eventTickets.getPaidAmount() - eventTickets.getRefundedAmount();
        proceedRefund(eventTickets, event, user, ticketingOrder, refundAmount, null,false);

        // update ticketing_order_manager table with new ticket type id and status
        updateTicketTypeInTicketingOrderManager(ticketingOrder, newTicketingType, eventTickets);

        // update event_ticket  table with new ticket type id available tickets
        EventTickets updatedEventTicket = eventTicketsService.findEventTicketById(eventTickets.getId()).orElseThrow(() -> new NotFoundException(NotFoundException.SessionSpeakerNotFound.EVENT_TICKET_NOT_FOUND));
        eventTicketTransferDetailService.storeFreeEventTicketTransferDetail(orderId, updatedEventTicket, oldTicketingType.getId(), newTicketingType.getId(), event, user);

        // Set data for the free ticket
        updatedEventTicket.setTicketingTypeId(newTicketingType);
        updatedEventTicket.setTicketStatus(TicketStatus.REGISTERED);
        updatedEventTicket.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        updatedEventTicket.setPaidAmount(0);
        updatedEventTicket.setTicketPrice(0);
        updatedEventTicket.setAeFeeAmount(0);
        updatedEventTicket.setSalesTaxFee(0);
        updatedEventTicket.setVatTaxFee(0);
        updatedEventTicket.setWlAFeeAmount(0);
        updatedEventTicket.setWlBFeeAmount(0);
        updatedEventTicket.setChargeId(null);
        updatedEventTicket.setDepositAmount(0);
        eventTickets.setDiscountAmount(0);
        eraseEventTicketFees(updatedEventTicket);

        // need to update AE fees CC fees and sales tax
        eventTicketsRepository.save(updatedEventTicket);
        updateTicketOrderBasedOnTicketTypeChanged(ticketingOrder);

        // Add log in order audit log table for ticket transfer
        StringBuilder refundReason = new StringBuilder();
        refundReason.append("Purchased order had been updated from ").append(oldTicketingType.getTicketTypeName()).append(" to ").append(newTicketingType.getTicketTypeName());
        ticketingRefundService.orderAuditLog(event, user, orderId, STRING_EMPTY, STRING_EMPTY, STRING_EMPTY + refundReason.toString() + " by " + user.getFirstName() + SINGLE_WHITE_SPACE + user.getLastName());

        //Sent mail to attendee for update ticketing type
        TicketTransferTypesDto ticketTransferTypesDto=new TicketTransferTypesDto();
        ticketTransferTypesDto.setPaidToFreeTicketTransfer(true);
        Ticketing ticketing = ticketingService.findByEvent(event);
        String mailSubject = "Your Updated Order Confirmation for " + event.getName();
        handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, updatedEventTicket,
                false, false, mailSubject,false,ticketTransferTypesDto,isMailSendToHolder, user);

    }

    public void transferFreeEventTicketToPaidTicket(Long orderId, EventTickets eventTickets, TicketingType oldTicketingType, TicketingType newTicketingType, Event event, User user,boolean isPaidTicketWithZeroAmount, TicketExchangeDto ticketExchangeDto) throws StripeException, ApiException {

        log.info("TicketingOrderTransferServiceImpl | transferFreeEventTicketToPaidTicket | eventTickets | {} | oldTicketTypeId | {} | newTicketTypeId | {} | eventId | {} | userId | {}",
                eventTickets.getId(), oldTicketingType.getId(), newTicketingType.getId(), event.getEventId(), user.getUserId());
        TicketingOrder ticketingOrder = getValidTicketOrder(orderId);
        Ticketing ticketing = ticketingService.findByEvent(event);
        AttendeePartialPaymentDto ticketExchangePaymentDto = ticketExchangeDto.getPaymentDetails();

        List<TicketingOrderManager> ticketingOrderManagerList = ticketingOrderManagerService.getAllByOrderId(ticketingOrder);
        boolean isAllFreeTicketBeforeTransferToPaid = ticketingOrderManagerList.stream().allMatch(ticketType -> ticketType.getTicketType().getTicketType().equals(TicketType.FREE));

        this.validateTicketingModuleAndPaymentProcessingActivated(event, ticketing, ticketingOrder);
        boolean isInvoicePayment = ticketExchangePaymentDto.getPaymentType().equals(INVOICE.toUpperCase());

        // update ticketing_order_manager table with new ticket type id and status
        updateTicketTypeInTicketingOrderManager(ticketingOrder, newTicketingType, eventTickets);

        // update event_ticket  table with new ticket type id available tickets
        EventTickets updatedEventTicket = getValidEventTicket(eventTickets.getId());
        eventTicketTransferDetailService.storeFreeEventTicketTransferDetail(orderId, updatedEventTicket, oldTicketingType.getId(), newTicketingType.getId(), event, user);

        // Set data for the free ticket
        updatedEventTicket.setTicketingTypeId(newTicketingType);
        updatedEventTicket.setTicketStatus(TicketStatus.REGISTERED);
        updatedEventTicket.setPaidAmount(0);
        updatedEventTicket.setTicketPrice(newTicketingType.getPrice());
        updatedEventTicket.setTicketPaymentStatus(TicketPaymentStatus.UNPAID);
        updatedEventTicket.setChargeId(null);
        updatedEventTicket.setDepositAmount(0);
        eventTickets.setDiscountAmount(0);
        eraseEventTicketFees(updatedEventTicket);

        // need to update AE fees CC fees and sales tax
        eventTicketsRepository.save(updatedEventTicket);
        updateTicketOrderBasedOnTicketTypeChanged(ticketingOrder);

        //   find the sum of number of ticket in ticketingOrderManagerList
        if (!TicketingOrder.TicketingOrderStatus.UNPAID.equals(ticketingOrder.getStatus())) {
            // If order status is not unpaid then set order status to UNPAID when all tickets are free else set to PARTIAL means some ticket are already paid
            if(isAllFreeTicketBeforeTransferToPaid){
                ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
                ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
            }else{
                ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PARTIAL);
            }

            ticketingOrderRepository.save(ticketingOrder);
        }

        // Add log in order audit log table for ticket transfer
        StringBuilder refundReason = new StringBuilder();
        refundReason.append("Purchased order had been updated from ").append(oldTicketingType.getTicketTypeName()).append(" to ").append(newTicketingType.getTicketTypeName());
        ticketingRefundService.orderAuditLog(event, user, orderId, STRING_EMPTY, STRING_EMPTY, STRING_EMPTY + refundReason.toString() + " by " + user.getFirstName() + SINGLE_WHITE_SPACE + user.getLastName());

        //Sent mail to attendee for update ticketing type
        TicketTransferTypesDto ticketTransferTypesDto=new TicketTransferTypesDto();
        ticketTransferTypesDto.setFreeToPaidTicketTransfer(true);
        String mailSubject = "Your Updated Order Confirmation for " + event.getName();

        // do Payment at ticket exchange
        if(!isInvoicePayment) {
            log.info("TicketingOrderTransferServiceImpl | transferFreeEventTicketToPaidTicket | payment process started | paymentType | {} | orderId | {} | eventTicketId | {} | event | {} | user | {}",
                    ticketExchangePaymentDto.getPaymentType(), orderId, updatedEventTicket.getId(), event.getEventId(), user.getUserId());
            ticketingPurchaseService.paymentForLowerToHigherTicketTransfer(event, user, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto, 0d, ticketExchangeDto.getCouponCode());
            ticketTransferTypesDto.setPaymentDoneAtTicketTransfer(true);
            mailSubject = String.format("Your Purchased Ticket for %s Has Been Exchanged.", event.getName());
            log.info("TicketingOrderTransferServiceImpl | transferFreeEventTicketToPaidTicket | payment process completed | paymentType | {} | orderId | {} | eventTicketId | {} | event | {} | user | {}",
                    ticketExchangePaymentDto.getPaymentType(), orderId, updatedEventTicket.getId(), event.getEventId(), user.getUserId());
        }else if (StringUtils.isNotEmpty(ticketExchangeDto.getCouponCode())){
            updateEntityForInvoiceTicketAndCouponCode(orderId, eventTickets, newTicketingType, event, ticketExchangeDto, ticketingOrder, ticketTransferTypesDto);
        }

        handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, updatedEventTicket,
                false, false, mailSubject,isPaidTicketWithZeroAmount,ticketTransferTypesDto,ticketExchangeDto.getSentMailToAttendee(), user);
    }

    private static void eraseEventTicketFees(EventTickets updatedEventTicket) {
        updatedEventTicket.setRefundedAmount(0);
        updatedEventTicket.setRefundedAEFee(0);
        updatedEventTicket.setRefundedSalesTaxFee(0);
        updatedEventTicket.setRefundedWlAFee(0);
        updatedEventTicket.setRefundedWlBFee(0);
        updatedEventTicket.setRefundedVatTaxFee(0);
    }

    public void transferPaidEventTicketToPaidTicket(Long orderId, EventTickets eventTickets, TicketingType oldTicketingType, TicketingType newTicketingType, Event event, User user, TicketExchangeDto ticketExchangeDto) throws StripeException, ApiException {

        log.info("TicketingOrderTransferServiceImpl | transferPaidEventTicketToPaidTicket | eventTickets | {} | oldTicketTypeId | {} | newTicketTypeId | {} | eventId | {} | userId | {}",
                eventTickets.getId(), oldTicketingType.getId(), newTicketingType.getId(), event.getEventId(), user.getUserId());
        Ticketing ticketing = ticketingService.findByEvent(event);
        TicketingOrder ticketingOrder = getValidTicketOrder(orderId);
        boolean isMailSendToHolder = ticketExchangeDto.getSentMailToAttendee();
        AttendeePartialPaymentDto ticketExchangePaymentDto = ticketExchangeDto.getPaymentDetails();

        // update event_ticket  table with new ticket type id available tickets
        StripeDTO stripe = stripeService.getStripeFeesByEvent(event);
        double capAmountForNewTicketType=transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(newTicketingType.getTicketTypeFormat(),event);
        double vatTaxRate = vatTaxService.getVatTaxByTicketTypeOrEvent(event.getEventId(),newTicketingType);
        double oldEventTicketVatTaxFee = eventTickets.getVatTaxFee();
        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId());
        // if old ticket paid through the cash or mark as paid or adjust/ apply payment
        boolean oldTicketPaidByCash = eventTickets.getPaidAmount()>0 && StringUtils.isBlank(eventTickets.getChargeId());
        boolean isInvoicePayment = ticketExchangePaymentDto.getPaymentType().equals(INVOICE.toUpperCase());

        List<TicketingOrderManager> ticketingOrderManagerList = ticketingOrderManagerService.getAllByOrderId(ticketingOrder);
        boolean isSinglePaidTicketInOrder = (ticketingOrderManagerList.size() == 1
                && ticketingOrderManagerList.get(0).getTicketType().getTicketType().equals(TicketType.PAID)
                && ticketingOrderManagerList.get(0).getNumberofticket() == 1);

        int totalTicketQty = ticketingOrderManagerList.stream()
                .filter(manager -> manager.getTicketType() != null)
                .filter(manager -> manager.getTicketType().getPrice() > 0.0)
                .mapToInt(TicketingOrderManager::getNumberofticket)
                .sum();

        FeePerTicket newFeePerTicket = new FeePerTicket(newTicketingType,oldTicketPaidByCash, stripe, salesTaxFeeDto, newTicketingType.getPrice(), 1, totalTicketQty, false,capAmountForNewTicketType, vatTaxRate,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        double totalPaidAmount = eventTickets.getPaidAmount() - eventTickets.getRefundedAmount();
        double newTicketPaidAmount = newFeePerTicket.getTotalPayable();

        if(ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.CARD) || ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.APP) ){
            double stripeFeeOldTicket = GeneralUtils.getRoundValue(CalculateFees.getCcFee(eventTickets.getPaidAmount(), 1, stripe.getCCPercentageFee(), stripe.getCCFlatFee()));
            totalPaidAmount -= stripeFeeOldTicket;
            double stripeFeeNewTicket = GeneralUtils.getRoundValue(CalculateFees.getCcFee(newTicketPaidAmount, 1, stripe.getCCPercentageFee(), stripe.getCCFlatFee()));
            newTicketPaidAmount -= stripeFeeNewTicket;
        }
        if(oldTicketPaidByCash){
            newTicketPaidAmount = newFeePerTicket.getTotalPayablePayLater();
        }

        TicketTransferTypesDto ticketTransferTypesDto=new TicketTransferTypesDto();
        ticketTransferTypesDto.setPaidToPaidTicketTransfer(true);

        if (totalPaidAmount == newTicketPaidAmount) {
            //If condition for the same ticket price
            updateTicketTypeInTicketingOrderManager(ticketingOrder, newTicketingType, eventTickets);
            EventTickets updatedEventTicket = getValidEventTicket(eventTickets.getId());
            eventTicketTransferDetailService.storeFreeEventTicketTransferDetail(orderId, updatedEventTicket, oldTicketingType.getId(), newTicketingType.getId(), event, user);

            // Set data for the free ticket
            updatedEventTicket.setTicketingTypeId(newTicketingType);
            updatedEventTicket.setTicketStatus(TicketStatus.REGISTERED);
            updatedEventTicket.setTicketPaymentStatus(TicketPaymentStatus.PAID);

            updatedEventTicket.setTicketPrice(newTicketingType.getPrice());
            updatedEventTicket.setDepositAmount(0);
            updatedEventTicket.setDiscountAmount(0);
            eraseEventTicketFees(updatedEventTicket);
            if(oldTicketPaidByCash){
                updatedEventTicket.setPaidAmount( newFeePerTicket.getTotalPayablePayLater());
                updatedEventTicket.setSalesTaxFee( newFeePerTicket.getPayLaterSalesTax());
                updatedEventTicket.setVatTaxFee(newFeePerTicket.getPayLaterVatTax());
                updatedEventTicket.setAeFeeAmount(0);
                updatedEventTicket.setWlAFeeAmount(0);
                updatedEventTicket.setWlBFeeAmount(0);
                updatedEventTicket.setVatTaxPercentage(GeneralUtils.getRoundValue(vatTaxRate));
            }else {
                updatedEventTicket.setPaidAmount(newFeePerTicket.getTotalPayable());
                updatedEventTicket.setAeFeeAmount(newFeePerTicket.getAeFee());
                updatedEventTicket.setSalesTaxFee( newFeePerTicket.getSalesTaxFee());
                //In invoice payment, the old VAT tax fee is stored, and it updated when the full payment is completed.
                updatedEventTicket.setVatTaxFee(newFeePerTicket.getVatTax());
                updatedEventTicket.setWlAFeeAmount(newFeePerTicket.getWlAFee());
                updatedEventTicket.setWlBFeeAmount(newFeePerTicket.getWlBFee());
                updatedEventTicket.setVatTaxPercentage(GeneralUtils.getRoundValue(vatTaxRate));
            }

            // need to update AE fees CC fees and sales tax
            eventTicketsRepository.save(updatedEventTicket);
            updateTicketOrderBasedOnTicketTypeChanged(ticketingOrder);

           // Add log in order audit log table for ticket transfer

            //Sent mail to attendee for update ticketing type
            ticketTransferTypesDto.setSamePriceTicketTransfer(true);
            String mailSubject = "Your Updated Order Confirmation for " + event.getName();
            handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, updatedEventTicket,
                    false, false, mailSubject,false,ticketTransferTypesDto,isMailSendToHolder, user);

        } else if (totalPaidAmount < newTicketPaidAmount) {
            //If condition for the lower to higher ticket price
            this.validateTicketingModuleAndPaymentProcessingActivated(event, ticketing, ticketingOrder);

            EventTicketTransaction.PaymentType paymentType = isInvoicePayment ? null :  EventTicketTransaction.PaymentType.valueOf(ticketExchangePaymentDto.getPaymentType());
            // boolean isCashPayment = !EventTicketTransaction.PaymentType.CARD.equals(paymentType);

             /* AS Mixed payment type is implement, So We not need to check valid payment method.
            // As backup plan, we keep this method as commented, once the Mixed payment is tested well then we can remove this line of code and method also
            // isValidPaymentMethod(oldTicketingType.getTicketType(),ticketingOrder.getStatus(), isInvoicePayment, isCashPayment, oldTicketPaidByCash);
              */

            // update ticketing_order_manager table with new ticket type id and status
            updateTicketTypeInTicketingOrderManager(ticketingOrder, newTicketingType, eventTickets);

            // update event_ticket  table with new ticket type id available tickets
            EventTickets updatedEventTicket = getValidEventTicket(eventTickets.getId());
            eventTicketTransferDetailService.storeFreeEventTicketTransferDetail(orderId, updatedEventTicket, oldTicketingType.getId(), newTicketingType.getId(), event, user);

            // Set data for the free ticket
            updatedEventTicket.setTicketingTypeId(newTicketingType);
            updatedEventTicket.setTicketStatus(TicketStatus.REGISTERED);
            if (!TicketPaymentStatus.UNPAID.equals(updatedEventTicket.getTicketPaymentStatus())) {
                updatedEventTicket.setTicketPaymentStatus(TicketPaymentStatus.PARTIALLY_PAID);
            }
            updatedEventTicket.setDepositAmount(0);
            updatedEventTicket.setDiscountAmount(0);
            updatedEventTicket.setTicketPrice(newTicketingType.getPrice());
            // TODO : we need to check is this requeired for payLater ticket exchange with invoice type

            if(isInvoicePayment) {
                updatedEventTicket.setVatTaxFee(newFeePerTicket.getVatTax());
                updatedEventTicket.setVatTaxPercentage(GeneralUtils.getRoundValue(vatTaxRate));
            }

            eraseEventTicketFees(updatedEventTicket);

            // need to update AE fees CC fees and sales tax
            eventTicketsRepository.save(updatedEventTicket);
            if(isSinglePaidTicketInOrder){
                ticketingOrder.setTicketingCoupon(null);
            }
            updateTicketOrderBasedOnTicketTypeChanged(ticketingOrder);
            //   find the sum of number of ticket in ticketingOrderManagerList
            if (!TicketingOrder.TicketingOrderStatus.UNPAID.equals(ticketingOrder.getStatus())) {
                ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PARTIAL);
                ticketingOrderRepository.save(ticketingOrder);
            }

            ticketTransferTypesDto.setLowerToHigherTicketTransfer(true);
            String mailSubject = "Your Updated Order Confirmation for " + event.getName();

            // do Payment at ticket exchange
            if(!isInvoicePayment) {
                log.info("TicketingOrderTransferServiceImpl | transferPaidEventTicketToPaidTicket | payment process started | paymentType | {} | orderId | {} | eventTicketId | {} | event | {} | user | {}",
                        paymentType, orderId, updatedEventTicket.getId(), event.getEventId(), user.getUserId());
                StringBuilder refundReason = new StringBuilder();
                refundReason.append("Purchased order had been updated from ").append(oldTicketingType.getTicketTypeName()).append(" to ").append(newTicketingType.getTicketTypeName());
                ticketingRefundService.orderAuditLog(event, user, orderId, STRING_EMPTY, STRING_EMPTY, STRING_EMPTY + refundReason.toString() + " by " + user.getFirstName() + SINGLE_WHITE_SPACE + user.getLastName());
                ticketingPurchaseService.paymentForLowerToHigherTicketTransfer(event, user, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto , oldEventTicketVatTaxFee, ticketExchangeDto.getCouponCode());
                ticketTransferTypesDto.setPaymentDoneAtTicketTransfer(true);
                mailSubject = String.format("Your Purchased Ticket for %s Has Been Exchanged.", event.getName());
                log.info("TicketingOrderTransferServiceImpl | transferPaidEventTicketToPaidTicket | payment process completed | paymentType | {} | orderId | {} | eventTicketId | {} | event | {} | user | {}",
                        paymentType, orderId, updatedEventTicket.getId(), event.getEventId(), user.getUserId());
            }else if (StringUtils.isNotEmpty(ticketExchangeDto.getCouponCode())){
                updateEntityForInvoiceTicketAndCouponCode(orderId, eventTickets, newTicketingType, event, ticketExchangeDto, ticketingOrder, ticketTransferTypesDto);
            }

            //Sent mail to attendee for update ticketing type
            handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, updatedEventTicket,
                    false, false, mailSubject,false,ticketTransferTypesDto,isMailSendToHolder, user);

        } else if(totalPaidAmount > newTicketPaidAmount) {
            double newTicketPrice = newTicketingType.getPrice();
            double refundAmount = (eventTickets.getPaidAmount()-eventTickets.getRefundedAmount() - newTicketPrice);
            proceedRefund(eventTickets, event, user, ticketingOrder, refundAmount, newTicketingType,true);
            updateTicketTypeInTicketingOrderManager(ticketingOrder, newTicketingType, eventTickets);
            EventTickets updatedEventTicket = eventTicketsService.findEventTicketById(eventTickets.getId()).orElseThrow(() -> new NotFoundException(NotFoundException.SessionSpeakerNotFound.EVENT_TICKET_NOT_FOUND));
            eventTicketTransferDetailService.storeFreeEventTicketTransferDetail(orderId, updatedEventTicket, oldTicketingType.getId(), newTicketingType.getId(), event, user);
            // Set data for the free ticket
            updatedEventTicket.setTicketingTypeId(newTicketingType);
            updatedEventTicket.setTicketStatus(TicketStatus.REGISTERED);
            updatedEventTicket.setTicketPaymentStatus(TicketPaymentStatus.PAID);
            double remainingPaidAmount = updatedEventTicket.getPaidAmount() - updatedEventTicket.getRefundedAmount();
            eraseEventTicketFees(updatedEventTicket);
            updatedEventTicket.setTicketPrice(newTicketingType.getPrice());
            if(oldTicketPaidByCash){
                updatedEventTicket.setAeFeeAmount(0);
                updatedEventTicket.setWlAFeeAmount(0);
                updatedEventTicket.setWlBFeeAmount(0);
                updatedEventTicket.setSalesTaxFee(newFeePerTicket.getPayLaterSalesTax());
                updatedEventTicket.setVatTaxFee(newFeePerTicket.getPayLaterVatTax());
            }else{
                updatedEventTicket.setAeFeeAmount(newFeePerTicket.getAeFee());
                updatedEventTicket.setWlAFeeAmount(newFeePerTicket.getWlAFee());
                updatedEventTicket.setWlBFeeAmount(newFeePerTicket.getWlBFee());
                updatedEventTicket.setSalesTaxFee(newFeePerTicket.getSalesTaxFee());
                updatedEventTicket.setVatTaxFee((isInvoicePayment) ? oldEventTicketVatTaxFee : newFeePerTicket.getVatTax());
                ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
            }
            updatedEventTicket.setVatTaxPercentage(GeneralUtils.getRoundValue(vatTaxRate));
            updatedEventTicket.setPaidAmount(remainingPaidAmount);
            updatedEventTicket.setDepositAmount(0);
            updatedEventTicket.setDiscountAmount(0);
            if(isSinglePaidTicketInOrder){
                ticketingOrder.setTicketingCoupon(null);
            }
            ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
            ticketingOrderRepository.save(ticketingOrder);
            // need to update AE fees CC fees and sales tax
            eventTicketsRepository.save(updatedEventTicket);


            //Sent mail to attendee for update ticketing type
            ticketTransferTypesDto.setHigherToLowerTicketTransfer(true);
            String mailSubject = "Your Updated Order Confirmation for " + event.getName();
            handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, updatedEventTicket,
                    false, false, mailSubject,false,ticketTransferTypesDto,isMailSendToHolder, user);
        }

        if(isInvoicePayment) {
            StringBuilder refundReason = new StringBuilder();
            refundReason.append("Purchased order had been updated from ").append(oldTicketingType.getTicketTypeName()).append(" to ").append(newTicketingType.getTicketTypeName());
            ticketingRefundService.orderAuditLog(event, user, orderId, STRING_EMPTY, STRING_EMPTY, STRING_EMPTY + refundReason.toString() + " by " + user.getFirstName() + SINGLE_WHITE_SPACE + user.getLastName());
        }
    }

    private void isValidPaymentMethod(TicketType ticketType, TicketingOrder.TicketingOrderStatus orderStatus, boolean isInvoicePayment, boolean isCashPayment, boolean oldTicketPaidByCash) {
        if (!TicketType.FREE.equals(ticketType) && !TicketingOrder.TicketingOrderStatus.UNPAID.equals(orderStatus) && !isInvoicePayment  && isCashPayment != oldTicketPaidByCash) {
            log.info("TicketingOrderTransferServiceImpl -> isValidPaymentMethod -> oldTicketPaidByCash  {} isCashPayment {}",
                    oldTicketPaidByCash, isCashPayment);
            String oldPaymentMethod = oldTicketPaidByCash ? EventTicketTransaction.PaymentType.CASH.getPaymentTypeDisplayName() : EventTicketTransaction.PaymentType.CARD.getPaymentTypeDisplayName();
            NotAcceptableException.PaymentCreationExceptionMsg paymentCreationExceptionMsg = OTHER_PAYMENT_METHOD_NOT_ALLOWED_FOR_PARTIAL_PAID_ORDER;
            String errorMsg = OTHER_PAYMENT_METHOD_NOT_ALLOWED_FOR_PARTIAL_PAID_ORDER_MESSAGE
                    .replace("${oldPaymentMethod}", oldPaymentMethod);
            paymentCreationExceptionMsg.setErrorMessage(errorMsg);
            paymentCreationExceptionMsg.setDeveloperMessage(errorMsg);
            throw new NotAcceptableException(paymentCreationExceptionMsg);
        }
    }

    private void validateTicketingModuleAndPaymentProcessingActivated(Event event, Ticketing ticketing, TicketingOrder ticketingOrder) {
        if (!ticketing.getActivated()) {
            boolean isPaidTicketExists = ticketingOrderManagerService.isAnyPaidTicketExists(ticketingOrder);
            if (!isPaidTicketExists) {
                log.info("TicketingOrderTransferServiceImpl -> validateTicketingModuleAndPaymentProcessingActivated -> ticketing is not enable for event -> {} -> ticketing -> {} -> ticketingOrder -> {}",
                        event.getEventId(), ticketing.getId(), ticketingOrder.getId());
                throw new NotAcceptableException(NotAcceptableException.PaymentCreationExceptionMsg.ACTIVATE_REGISTRATION);
            }
            if (stripeService.getByEventId(event.getEventId()) == null) {
                log.info("TicketingOrderTransferServiceImpl -> validateTicketingModuleAndPaymentProcessingActivated -> payment gateway is not enable for event -> {} -> ticketing -> {} -> ticketingOrder -> {}",
                        event.getEventId(), ticketing.getId(), ticketingOrder.getId());
                throw new NotAcceptableException(NotAcceptableException.PaymentCreationExceptionMsg.CREDIT_CARD_PROCESSING_NOT_ENABLE);
            }
        }
    }

    private void proceedRefund(EventTickets eventTickets, Event event, User user, TicketingOrder ticketingOrder, double refundAmount, TicketingType newTicketType,boolean isRefundEmailSend) {
        if ((!TicketingOrder.OrderType.EXTERNAL_TRANSACTION.equals(ticketingOrder.getOrderType()) && TicketingOrder.TicketingOrderStatus.PAID.equals(ticketingOrder.getStatus())) || (TicketingOrder.OrderType.CARD.equals(ticketingOrder.getOrderType()) && TicketingOrder.TicketingOrderStatus.PARTIAL.equals(ticketingOrder.getStatus())))  {
            RefundInfo refundInfo = new RefundInfo(eventTickets.getId(), 1, refundAmount, null);
            refundInfo.setExchangeTicketTypeId(null != newTicketType ? newTicketType.getId() : null);
            refundPayment(ticketingOrder.getId(), Arrays.asList(refundInfo), event, user, STRING_EMPTY,isRefundEmailSend);
        } else if(refundAmount >0) {
            ticketingRefundService.orderAuditLog(event, user, ticketingOrder.getId(), STRING_EMPTY, STRING_EMPTY, "" + refundAmount + " refunded by " + user.getFirstName() + " " + user.getLastName());
        }
    }

    /**
     * Transfer paid ticket to free ticket or lower amount ticket with refund
     */
    private void refundPayment(Long orderId, List<RefundInfo> refundInfoList, Event event, User user, String ticketTransferReason,boolean isRefundEmailSend) {
        try {
            ticketingRefundService.refund(orderId, event, refundInfoList, new Date(), isRefundEmailSend, true, user, false, ticketTransferReason, false, true, false);
        } catch (StripeException e) {
            log.error("StripeException | TicketingOrderTransferServiceImpl | refundPayment | orderId | {} | refundInfoList | {} | eventId | {} | userId | {}",
                    orderId, refundInfoList, event.getEventId(), user.getUserId(), e);
            throw new NotAcceptableException(e);
        } catch (ApiException e) {
            log.error("ApiException | TicketingOrderTransferServiceImpl | refundPayment | orderId | {} | refundInfoList | {} | eventId | {} | userId | {}",
                    orderId, refundInfoList, event.getEventId(), user.getUserId(), e);
            throw new NotAcceptableException(e);
        }
    }


    protected void handleEmailForTicketTransfer(Event event, TicketingType newTicketType, TicketingType oldTicketingType, TicketingOrder ticketingOrder, Ticketing ticketing, EventTickets updatedEventTicket,
                                                boolean isSendEmailForMarkAsPaid, boolean isFromReSendEmail, String emailSubject, boolean isPaidTicketWithZeroAmount, TicketTransferTypesDto ticketTransferTypesDto,boolean isMailSendToHolder, User loggedInUser) {
        try {
            sendEventTicketTransferEmail(event, newTicketType, oldTicketingType, ticketingOrder, ticketing, updatedEventTicket,
                    isSendEmailForMarkAsPaid, isFromReSendEmail, emailSubject,isPaidTicketWithZeroAmount,ticketTransferTypesDto,isMailSendToHolder, loggedInUser);
        } catch (Exception e) {
            log.error("handleEmailForTicketTransfer Exception ==> {}", e);
        }
    }



    public void sendEventTicketTransferEmail(Event event, TicketingType newTicketType, TicketingType oldTicketingType, TicketingOrder ticketingOrder, Ticketing ticketing, EventTickets eventTickets,
                                             boolean isSendEmailForMarkAsPaid, boolean isFromReSendEmail, String emailSubject,boolean isPaidTicketWithZeroAmount,TicketTransferTypesDto ticketTransferTypesDto,boolean isMailSendToAttendee, User loggedInUser) throws DocumentException, TemplateException, IOException {
        Map<String, TicketEmailDto> holderEmailDto = new HashMap<>();

        User purchaser = ticketingOrder.getPurchaser();

        TicketEmailDto purchaserEmailData = new TicketEmailDto();
        List<TicketHolderEmailDto> purchaserHolderEmailDate = new ArrayList<>();
        purchaserEmailData.setTicketHolderEmail(purchaserHolderEmailDate);
        purchaserEmailData.setEventname(event.getName());
        purchaserEmailData.setName(purchaser.getFirstName());
        purchaserEmailData.setTomail(purchaser.getEmail());
        purchaserEmailData.setPurchaseDate(ticketingOrder.getOrderDate());

        List<EventTickets> eventTicketsList= Collections.singletonList(eventTickets);
        List<String> chargeIds = eventTicketsList.stream().map(EventTickets::getChargeId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<StripeTransaction> stripeTransactionList = stripeTransactionService.findStripeTransactionByChargeIds(chargeIds);
        Map<String,StripeTransaction> stripeTransactionMap = stripeTransactionList.stream().collect(Collectors.toMap(StripeTransaction::getChargeid, Function.identity(),(o, n)->o,HashMap::new));
        double applicationFee = sendGridMailPrepareService.getApplicationFeeAndCCFees(eventTicketsList);
        double vatTaxFee = getVatFeeForUpdateTicketEmail(eventTickets);
        double salesTaxFee = getSalesTaxFeeForUpdateTicketEmail(eventTickets);

        // Set Application Fee and Sales/Vat tax fee for purchase email details
        purchaserEmailData.setApplicationFee(applicationFee);
        purchaserEmailData.setVatTaxFee(vatTaxFee);
        purchaserEmailData.setSalesTaxFee(salesTaxFee);

        List<Long> recurringEventIds = new ArrayList<>();
        if (null != eventTickets.getRecurringEventId()) {
            recurringEventIds = Collections.singletonList(eventTickets.getRecurringEventId());
        }

        if (eventTickets != null) {
            TicketingType ticketingType = eventTickets.getTicketingTypeId();
            String holderFirstName;
            String holderLastName;

            if(eventTickets.getGuestOfBuyer() && eventTickets.getTicketPurchaserId()!=null){
                holderFirstName  = GUEST_OF +STRING_BLANK + eventTickets.getTicketPurchaserId().getFirstName();
                holderLastName = eventTickets.getTicketPurchaserId().getLastName();
            }else{

                holderFirstName = StringUtils.isNotBlank(eventTickets.getHolderFirstName()) ? eventTickets.getHolderFirstName() : STRING_EMPTY;
                holderLastName = StringUtils.isNotBlank(eventTickets.getHolderLastName()) ? eventTickets.getHolderLastName() : STRING_EMPTY;
            }

            Long holderUserId = (null != eventTickets.getHolderUserId()) ? eventTickets.getHolderUserId().getUserId() : null;
            String email = eventTickets.getHolderEmail();

            TicketEmailDto ticketEmailDto = holderEmailDto.get(email);
            if (ticketEmailDto == null) {
                ticketEmailDto = new TicketEmailDto();
                ticketEmailDto.setEventname(event.getName());
                ticketEmailDto.setTomail(email);
                ticketEmailDto.setName(holderFirstName);
                ticketEmailDto.setPurchaseDate(ticketingOrder.getOrderDate());

                //  Set Application Fee and Sales/Vat tax fee for holder email
                ticketEmailDto.setApplicationFee(applicationFee);
                ticketEmailDto.setVatTaxFee(vatTaxFee);
                ticketEmailDto.setSalesTaxFee(salesTaxFee);

                List<TicketHolderEmailDto> ticketHolderEmail = new ArrayList<>();
                TicketHolderEmailDto ticketHolderEmailDto = new TicketHolderEmailDto();
                ticketHolderEmail.add(ticketHolderEmailDto);
                purchaserHolderEmailDate.add(ticketHolderEmailDto);
                ticketHolderEmailDto.setHolderName(holderFirstName.concat(Constants.STRING_BLANK).concat((StringUtils.isNotBlank(holderLastName)) ? holderLastName : ""));
                ticketHolderEmailDto.setHolderUserId(holderUserId);
                ticketHolderEmailDto.setPrice(eventTickets.getTicketingTypeId().getPrice());
                ticketHolderEmailDto.setPaidAmount(eventTickets.getPaidAmount());
                ticketHolderEmailDto.setTicketTypeFormat(ticketingType.getTicketTypeFormat() != null ? ticketingType.getTicketTypeFormat().name() : null);
                ticketHolderEmailDto.setSeatNumber(eventTickets.getSeatNumber());
                ticketHolderEmailDto.setTicketingTypePrice(ticketingType.getPrice());
                ticketHolderEmailDto.setTicketingTypeId(ticketingType.getId());

                if (TicketBundleType.INDIVIDUAL_TICKET.equals(eventTickets.getTicketingTypeId().getBundleType())) {
                    ticketHolderEmailDto.setTicketingTypePrice(isFromReSendEmail ? eventTickets.getTicketPrice() : eventTickets.getTicketingTypeId().getPrice());
                } else {
                    ticketHolderEmailDto.setTicketingTypePrice(isFromReSendEmail ? eventTickets.getTicketPrice() : eventTickets.getTicketingTypeId().getPrice());
                    ticketHolderEmailDto.setNumberOfTicketPerTable(eventTickets.getTicketingTypeId().getNumberOfTicketPerTable());
                }
                ticketHolderEmailDto.setDiscount(eventTickets.getDiscountAmount());
                ticketHolderEmailDto.setRefundedAmount(eventTickets.getRefundedAmount());
                ticketHolderEmailDto.setBarcode(eventTickets.getBarcodeId());
                ticketHolderEmailDto.setTicketTypeName(ticketingType.getTicketTypeName());
                ticketHolderEmailDto.setAllowPDFDownload(ticketingType.getAllowPDFDownload());
                ticketHolderEmailDto.setAllowInvoicePDFDownloadForBuyer(ticketingType.getAllowInvoicePDFForBuyers());
                ticketHolderEmailDto.setAllowInvoicePDFDownloadForHolder(ticketingType.getAllowInvoicePDFForHolders());
                ticketHolderEmailDto.setTicketingTypeDesc(ticketingType.getTicketTypeDescription());
                ticketHolderEmailDto.setEvnetTicketId(eventTickets.getId());

                ticketHolderEmailDto
                        .setPurchaserName(purchaser.getFirstName() + Constants.STRING_BLANK + purchaser.getLastName());
                ticketHolderEmailDto.setPurchaserEmail(purchaser.getEmail());
                ticketHolderEmailDto.setDateOfPurchase(ticketingOrder.getOrderDate());
                ticketHolderEmailDto.setOrderNumber(ticketingOrder.getId());
                ticketHolderEmailDto.setTicketType(ticketingType.getTicketType());
                ticketHolderEmailDto.setTicketingTypeId(ticketingType.getId());
                ticketHolderEmailDto.setAutoAssignedNumber(attendeeSequenceNumberService.getSequenceNumberByEventTicket(eventTickets, event));
                ticketHolderEmailDto.setAutoSequeneceName(sendGridMailPrepareService.getAutoAssignSeqName(ticketHolderEmailDto.getAutoAssignedNumber(), eventTickets));
                ticketHolderEmailDto.setTicketCode(eventTickets.getTicketCode());
                ticketHolderEmailDto.setCardDetail(getCardDetailTicketingOrderTransferEmail(eventTickets,stripeTransactionMap));
                ticketHolderEmailDto.setTicketLevelVatTax(GeneralUtils.getRoundValue(eventTickets.getVatTaxFee()));
                ticketHolderEmailDto.setTicketLevelVatTaxPercentage(eventTickets.getVatTaxPercentage());
                if (eventTickets.getTicketingTable() != null) {
                    ticketHolderEmailDto.setTableId(eventTickets.getTicketingTable().getTableNoSequence());
                }

                ticketEmailDto.setTicketHolderEmail(ticketHolderEmail);
                if (ticketingOrder.getId() > 0) holderEmailDto.put(email, ticketEmailDto);
            }

            if (isMailSendToAttendee) {
            sendMailToAttendeeForUpdateTicketType(purchaserEmailData, event, newTicketType, oldTicketingType,
                    ticketing, ticketingOrder, recurringEventIds, Optional.empty(), emailSubject, purchaser,
                    isSendEmailForMarkAsPaid, false,
                    eventTickets,isPaidTicketWithZeroAmount,ticketTransferTypesDto, loggedInUser);

                for (Map.Entry<String, TicketEmailDto> entry : holderEmailDto.entrySet()) {
                    if (StringUtils.isNotEmpty(entry.getKey())
                            && entry.getKey().equalsIgnoreCase(purchaserEmailData.getTomail())) {
                        continue;
                    }
                    sendMailToAttendeeForUpdateTicketType(entry.getValue(), event, newTicketType, oldTicketingType,
                            ticketing, ticketingOrder, recurringEventIds, Optional.empty(), emailSubject, purchaser,
                            isSendEmailForMarkAsPaid, false,
                            eventTickets, isPaidTicketWithZeroAmount, ticketTransferTypesDto, loggedInUser);
                }
            }
        } else {
            log.warn("No event ticket found for order id : {} and event id : {}", ticketingOrder.getId(), event.getEventId());
        }
    }

    private String getCardDetailForUpdateTicketType(TicketingOrder ticketingOrder, boolean isAllFreeTickets, TicketHolderEmailDto ticketHolderEmailDto){
        String cardDetail = getPaymentStatus(ticketingOrder, isAllFreeTickets);
        if (ticketHolderEmailDto.getCardDetail() != null){
            cardDetail = ticketHolderEmailDto.getCardDetail();
        }
        return cardDetail;
    }

    private String getCardDetailTicketingOrderTransferEmail(EventTickets eventTickets, Map<String, StripeTransaction> stripeTransactionMap) {
        String cardDetail = null;
        if(eventTickets.getTicketPrice() == 0 && eventTickets.getDiscountAmount() == 0){
            cardDetail=eventTickets.getTicketingTypeId().getTicketType().name();
        } else if(StringUtils.isNotBlank(eventTickets.getChargeId())) {
            cardDetail=StripeTransactionService.getCardDetailByStripeTransaction(stripeTransactionMap.get(eventTickets.getChargeId())).orElse(STRING_EMPTY);
        }
        return cardDetail;
    }

    private void sendMailToAttendeeForUpdateTicketType(TicketEmailDto emaildata, Event event, TicketingType newTicketType, TicketingType oldTicketingType,
                                                       Ticketing ticketing, TicketingOrder ticketingOrder, List<Long> recurringEventIds, Optional<RecurringEvents> optionalRecurringEvents,
                                                       String emailSubject, User purchaser,
                                                       boolean isSendEmailForMarkAsPaid,
                                                       boolean isHolderEmail,
                                                       EventTickets eventTickets,boolean isPaidTicketWithZeroAmount,TicketTransferTypesDto ticketTransferTypesDto, User loggedInUser) throws IOException, TemplateException, com.itextpdf.text.DocumentException {
        String eventName = StringUtils.isNotBlank(emaildata.getEventname()) ? emaildata.getEventname()
                : Constants.STRING_DASH;
        EmailMessage emailMessage = new EmailMessage(BEFREE_DEFAULT_REMINDER_MINIFIED);
        EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        if (StringUtils.isNotEmpty(emailSubject)) {
            emailMessage.setSubject(emailSubject);
        }
        Date eventStartDate = ticketing.getEventStartDate();
        Date eventEndDate = ticketing.getEventEndDate();
        String eventAddress = ticketing.getEventAddress();
        String hostUserEmail = eventDesignDetail.getReplyToEmail();
        emailMessage.setWhiteLabel(event.getWhiteLabel());
        emailMessage.setReplyToEmail(hostUserEmail);
        emailMessage.setSenderNameFromEvent(eventDesignDetail.getEmailSenderName());
        emailMessage.setSenderMailFromEvent(eventDesignDetail.getNotificationEmail());
        emailMessage.setBeefreeTemplate(false);
        emailMessage.setTemplateName(TemplateId.BEEFREE_DEFAULT_ATTENDEE_ORDER_UPDATE_CONFIRMATION_EMAIL.getValue());

        Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();


        substitutionMap.put(TICKET_HOLDER_FIRST_NAME,
                StringUtils.isNotBlank(emaildata.getName()) ? StringTools.getStringEllipsis(emaildata.getName()) : Constants.STRING_DASH);

        String eventUrl = serviceHelper.getEventBaseUrl(event).concat(getEventPath()).concat(event.getEventURL());
        Integer preEventAccessMinutes = ticketing.getPreEventAccessMinutes() != null ? -ticketing.getPreEventAccessMinutes() : 0;
        Date preEventStartDate = DateUtils.getAddedMinutes(ticketing.getEventStartDate(), preEventAccessMinutes);
        boolean isPreEventAccess = new Date().compareTo(preEventStartDate) < 0;
        Date eventStartDateForLabel = TimeZoneUtil.getDateInLocal(eventStartDate, event.getEquivalentTimeZone());
        if ((!isPreEventAccess || eventStartDateForLabel.before(TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone()))) && !IN_PERSON.equals(event.getEventFormat())) {
            eventUrl += "/portal";
        }
        substitutionMap.put(OLD_TICKET_TYPE_NAME, oldTicketingType.getTicketTypeName());
        substitutionMap.put(NEW_TICKET_TYPE_NAME, newTicketType.getTicketTypeName());
        substitutionMap.put(IS_HOLDER_TICKET,eventTickets != null && eventTickets.getHolderEmail()!=null && eventTickets.getHolderEmail().equals(emaildata.getTomail()));
        substitutionMap.put(IS_BUYER_TICKET,eventTickets != null && eventTickets.getTicketPurchaserId().getEmail()!=null && eventTickets.getTicketPurchaserId().getEmail().equals(emaildata.getTomail()));
        String token = UUID.randomUUID().toString();
        log.info("sendMailToAttendeeForUpdateTicketType with MagicLink for event {} user {} magicLinkCreatorUserId {} creationLocation {}", event.getEventId(), emaildata.getUserId(), loggedInUser.getUserId(),  AutoLoginCreationLocation.TICKET_TYPE_TRANSFER_EMAIL);
        autoLoginService.saveAutoLogin(event, emaildata.getTicketHolderEmail().get(0).getHolderUserId(), token, 24 * 7, MagicLinkType.PLATFORM_LEVEL, loggedInUser.getUserId(), AutoLoginCreationLocation.TICKET_TYPE_TRANSFER_EMAIL);
        substitutionMap.put("event_name", eventName);


        String dateString = STRING_EMPTY;
        if (optionalRecurringEvents.isPresent()) {
            RecurringEvents recurringEvents = optionalRecurringEvents.get();
            Date eventStartDateWithoutTime = null;
            Date eventEndDateWithoutTime = null;
            DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            try {
                eventStartDateWithoutTime = formatter.parse(formatter.format(recurringEvents.getRecurringEventStartDate()));
                eventEndDateWithoutTime = formatter.parse(formatter.format(recurringEvents.getRecurringEventEndDate()));
            } catch (ParseException e) {
                e.printStackTrace();
            }


            if (eventStartDateWithoutTime.compareTo(eventEndDateWithoutTime) == 0) {
                dateString = StringTools.formatCalanderDate(recurringEvents.getRecurringEventStartDate(), true, true).concat(FROM_WITH_SPACE)
                        .concat(StringTools.formatCalanderTime(recurringEvents.getRecurringEventStartDate(), false)).concat(" to ")
                        .concat(StringTools.formatCalanderTime(recurringEvents.getRecurringEventEndDate(), false));
            } else {
                dateString = StringTools.formatCalanderDate(recurringEvents.getRecurringEventStartDate(), true, true).concat(FROM_WITH_SPACE)
                        .concat(StringTools.formatCalanderTime(recurringEvents.getRecurringEventStartDate(), false)).concat(" to ")
                        .concat(StringTools.formatCalanderDate(recurringEvents.getRecurringEventEndDate(), true, true)).concat(FROM_WITH_SPACE)
                        .concat(StringTools.formatCalanderTime(recurringEvents.getRecurringEventEndDate(), false));
            }
            substitutionMap.put(Constants.EVENT_START_END_DATETIME, event.isHideEventDate() ? "" : (dateString.concat(" ").concat(event.getTimezoneId())));
            substitutionMap.put("recurring_event_date", event.isHideEventDate() ? "" : (dateString.concat(" ").concat(event.getTimezoneId())));
            substitutionMap.put("event_start_hours", event.isHideEventDate() ? "" : StringTools.formatCalanderTime(recurringEvents.getRecurringEventStartDate(), true));
            if (StringUtils.isNotBlank(recurringEvents.getLocation())) {
                substitutionMap.put("location", recurringEvents.getLocation());
            }

            eventStartDate = TimeZoneUtil.getDateInUTC(recurringEvents.getRecurringEventStartDate(), event.getEquivalentTimeZone());
            eventEndDate = TimeZoneUtil.getDateInUTC(recurringEvents.getRecurringEventEndDate(), event.getEquivalentTimeZone());
        } else {
            if (emaildata.getTicketHolderEmail().get(0).getTicketTypeFormat().equals("VIRTUAL")) {
                eventAddress = EnumEventVenue.ONLINE_VIRTUAL_EVENT.getValue();
            } else if (StringUtils.isBlank(eventAddress)) {
                eventAddress = EnumEventVenue.TO_BE_ANNOUNCED.getValue();
            }
            if (!recurringEventIds.isEmpty()) {
                substitutionMap.put(Constants.EVENT_START_END_DATETIME, "Season Tickets");
            }
            substitutionMap.put("eventAddress", eventAddress.trim());
        }


        substitutionMap.put(Constants.EVENT_ID_UPPERCASE, event.getEventId());
        substitutionMap.put(Constants.BASE_URL, eventUrl);

        if (VIRTUAL.equals(event.getEventFormat())) {
            substitutionMap.put(JOINORVIEWEVENTLABEL, eventStartDateForLabel.after(TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone())) ? Constants.FUTURE_EVENT_LABEL : Constants.JOIN_THE_EVENT_LABEL);
        } else if (IN_PERSON.equals(event.getEventFormat())) {
            substitutionMap.put(JOINORVIEWEVENTLABEL, Constants.VIEW_EVENT_DETAILS_LABEL);
        } else {
            String label = (eventTicketsRepository.checkUserHadPurchasedVirtualTicketInOrder(ticketingOrder.getId(), emaildata.getTomail(), TicketTypeFormat.VIRTUAL) ? (eventStartDateForLabel.after(TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone())) ? Constants.FUTURE_EVENT_LABEL : Constants.JOIN_THE_EVENT_LABEL) : Constants.VIEW_EVENT_DETAILS_LABEL);//NOSONAR
            substitutionMap.put(JOINORVIEWEVENTLABEL, label);
        }

        SimpleDateFormat dateFormatter = new SimpleDateFormat(DD_MMM_YYYY);
        Date purchasedt = TimeZoneUtil.getDateInLocal(emaildata.getPurchaseDate(), event.getEquivalentTimeZone());
        String date = dateFormatter.format(purchasedt);
        String eventTimeZone = java.util.TimeZone.getTimeZone(event.getEquivalentTimeZone()).getDisplayName(false, java.util.TimeZone.SHORT);
        SimpleDateFormat timeFormatters = new SimpleDateFormat(HH_MM_A);
        String time = timeFormatters.format(purchasedt);

        substitutionMap.put(PURCHASE_DATE,
                String.format("%s", date + " at " + time + eventTimeZone));

        final WhiteLabel whiteLabel = event.getWhiteLabel();
        String sender = whiteLabel != null ? whiteLabel.getFirmName() : "Accelevents";
        substitutionMap.put("sender", sender);

        Map<Long, TickeingTableEmail> tablesData = new TreeMap<>();
        double total = 0;
        double discount = 0;
        double refund = 0;
        double totalPaidAmount = 0;
        StringBuilder seatsForCalendar = new StringBuilder();

        List<TicketHolderEmailDto> listForFtl = new ArrayList<>();
        String buyerName = "";
        Map<Long, List<TicketHolderEmailDto>> ticketHolderEmailMap = emaildata.getTicketHolderEmail().stream().collect(Collectors.groupingBy(e -> e.getTicketingTypeId()));
        for (TicketHolderEmailDto item : emaildata.getTicketHolderEmail()) {
            Long numberOfTicketsPerTable = (item.getNumberOfTicketPerTable() > 0) ? item.getNumberOfTicketPerTable() : 1l;
            TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();
            if (item.getTableId() == null) {
                dataForTicketFtl.setPrice(DoubleHelper.roundValueTwoDecimal(item.getTicketingTypePrice()));

            } else {
                TickeingTableEmail tableEmail = tablesData.get(item.getTableId());
                if (tableEmail == null) {
                    tableEmail = new TickeingTableEmail();
                    tableEmail.setType(item.getTicketTypeName());
                }
                TickeingInfo tickeingInfo = new TickeingInfo();
                tickeingInfo.setName(item.getHolderName());
                tickeingInfo.setSeatNumber(item.getSeatNumber());
                tableEmail.addTickeingInfo(tickeingInfo);
                tableEmail.setPrice(tableEmail.getPrice() + item.getPrice() + item.getDiscount());
                tablesData.put(item.getTableId(), tableEmail);


                seatsForCalendar.append(item.getSeatNumber());
                //It should price / numberOFTickets for bundle type
                dataForTicketFtl.setPrice(DoubleHelper.roundValueTwoDecimal(item.getTicketingTypePrice() / item.getNumberOfTicketPerTable()));
            }

            if (isHolderEmail) {
                sendGridMailPrepareService.updateHolderNameWithAutoSequenceNameAndNumber(ticketing, item, dataForTicketFtl);
            } else {
                if (!STRING_EMPTY.equals(item.getAutoAssignedNumber())) {
                    List<TicketHolderEmailDto> holderEmailDtos = ticketHolderEmailMap.get(item.getTicketingTypeId());
                    List<String> stream = holderEmailDtos.stream().map(e -> e.getAutoAssignedNumber()).collect(Collectors.toList());
                    String sequenceNumberComaSep = GeneralUtils.convertListToCommaSeparated(stream);

                    if (TicketType.FREE.equals(item.getTicketType()) || item.getTicketingTypePrice() == 0) {
                        buyerName = getHolderNameWithColonAndDashForEmail(item.getHolderName(), Constants.STRING_BLANK, Constants.STRING_DASH, Constants.STRING_BLANK, item.getAutoSequeneceName(), Constants.STRING_COLON, Constants.STRING_BLANK, sequenceNumberComaSep);
                    } else if (TicketType.PAID.equals(item.getTicketType()) || item.getTicketingTypePrice() > 0) {
                        String sequenceNameAndNumber = getHolderNameWithColonAndDashForEmail(STRING_EMPTY, Constants.STRING_BLANK, Constants.STRING_DASH, Constants.STRING_BLANK, item.getAutoSequeneceName(), Constants.STRING_COLON, Constants.STRING_BLANK, sequenceNumberComaSep);
                        dataForTicketFtl.setAutoAssignedNumber(sequenceNameAndNumber);
                    }
                } else {
                    buyerName = item.getHolderName();
                }
                if (TicketType.FREE.equals(item.getTicketType()) || item.getTicketingTypePrice() == 0) {
                    dataForTicketFtl.setPurchaserName(StringUtils.isNotBlank(item.getPurchaserName()) ? buyerName : Constants.STRING_DASH);
                }
            }
            sendGridMailPrepareService.updateBuyerNameWithSequenceNameAndNumber(isHolderEmail, ticketing, ticketHolderEmailMap, item, dataForTicketFtl);
            dataForTicketFtl.setTicketTypeName(item.getTicketTypeName());
            if (StringUtils.isNotBlank(item.getSeatNumber())) dataForTicketFtl.setSeatNumber(item.getSeatNumber());
            dataForTicketFtl.setTicketType(item.getTicketType());
            dataForTicketFtl.setBundleTicketTypePrice(item.getTicketingTypePrice() / numberOfTicketsPerTable);
            dataForTicketFtl.setCardDetail(ticketTransferTypesDto.isComplementaryTicketTransfer() ? Constants.COMPLIMENTARY : getCardDetailForUpdateTicketType(ticketingOrder,newTicketType.getTicketType().name().equals(TicketType.FREE.name()),item));
            dataForTicketFtl.setTicketLevelVatTax(GeneralUtils.getRoundValue(item.getTicketLevelVatTax()));
            dataForTicketFtl.setTicketLevelVatTaxPercentage(item.getTicketLevelVatTaxPercentage());

            listForFtl.add(dataForTicketFtl);

            total = total + item.getPrice();
            totalPaidAmount = totalPaidAmount + item.getPaidAmount();
            discount = discount + item.getDiscount();
            refund = refund + item.getRefundedAmount() + item.getRefundAmount();


        }
        Collections.reverse(listForFtl);
        Map<String, List<TicketHolderEmailDto>> stringListMap = listForFtl.stream().collect(Collectors.groupingBy(ticket -> ticket.getTicketTypeName()));

        substitutionMap.put("dataForTickets", stringListMap);
        substitutionMap.put("total_amount", event.getCurrency().getSymbol().concat(DoubleHelper.roundValueTwoDecimalReturnString(totalPaidAmount)));

        substitutionMap.put("applicationFee", emaildata.getApplicationFee() > 0 ? DoubleHelper.roundValueTwoDecimal(emaildata.getApplicationFee()) : null);
        substitutionMap.put("total_processing_fee", event.getCurrency().getSymbol().concat(emaildata.getApplicationFee() > 0 ? DoubleHelper.roundValueTwoDecimalReturnString(emaildata.getApplicationFee()) : STRING_EMPTY));

        // It will show the total sales tax fee
        substitutionMap.put("show_total_sales_tax_fee",emaildata.getSalesTaxFee() > 0 ? DoubleHelper.roundValueTwoDecimal(emaildata.getSalesTaxFee()) : null);
        substitutionMap.put("total_sales_fee",event.getCurrency().getSymbol().concat(emaildata.getSalesTaxFee() > 0 ? DoubleHelper.roundValueTwoDecimalReturnString(emaildata.getSalesTaxFee()) : STRING_EMPTY));

        // It will show the total vat tax fee
        sendGridMailPrepareService.addSubstitutionDataForVat(emaildata, event, STRING_EMPTY, STRING_EMPTY, substitutionMap);

        if (null != ticketing.getLatitude() && null != ticketing.getLongitude()) {
            substitutionMap.put(VIEW_ON_MAP, GOOGLE_MAP_URL.concat(ticketing.getLatitude()).concat(",").concat(ticketing.getLongitude()));
        } else {
            substitutionMap.put(VIEW_ON_MAP, STRING_EMPTY);
        }
        substitutionMap.put("payment_method", ticketingOrder.getOrderType().name());
        substitutionMap.put("payment_type", PAID);
        if (!ticketTransferTypesDto.isPaymentDoneAtTicketTransfer() && (eventTickets.getTicketPaymentStatus().equals(TicketPaymentStatus.PARTIALLY_PAID) || eventTickets.getTicketPaymentStatus().equals(TicketPaymentStatus.UNPAID))) {
            StringBuilder paymentLinkBuilder = new StringBuilder(uiBaseurl)
                    .append("/e/u/checkout/")
                    .append(event.getEventURL())
                    .append("/tickets/orderDetails/")
                    .append(EncryptUtils.encryptUrlSafe(String.valueOf(eventTickets.getTicketingOrder().getId())))
                    .append("/ticket/")
                    .append(EncryptUtils.encryptUrlSafe(String.valueOf(eventTickets.getId())));
            if (optionalRecurringEvents.isPresent()) {
                paymentLinkBuilder.append("?recurringEventId=").append(optionalRecurringEvents.get().getId());
            }
            substitutionMap.put("payment_link", paymentLinkBuilder.toString());
            substitutionMap.put("payment_type", UNPAID);
        }
        substitutionMap.put("order_pdf_link", getEventUrlWithOrderId(event, ticketingOrder.getId()));

        substitutionMap.put("discount", DoubleHelper.roundValueTwoDecimal(discount));
        substitutionMap.put(TOTAL_DISCOUNT, event.getCurrency().getSymbol().concat(DoubleHelper.roundValueTwoDecimalReturnString(discount)));

        totalPaidAmount = GeneralUtils.getRoundValue(totalPaidAmount);
        boolean isFree = false;
        if (totalPaidAmount <= 0) {
            isFree = true;
        }
        substitutionMap.put("isFree", isFree);
        substitutionMap.put("payment_type", getPaymentStatus(ticketingOrder, isFree));
        substitutionMap.put("total_amount_in_decimal", DoubleHelper.roundValueTwoDecimal(totalPaidAmount)); //total_amount_double
        substitutionMap.put("total_amount", event.getCurrency().getSymbol().concat(DoubleHelper.roundValueTwoDecimalReturnString(totalPaidAmount)));

        substitutionMap.put("image_prefix", imageConfiguration.getImagePrefix());
        TicketHolderEmailDto holderEmailData = emaildata.getTicketHolderEmail().get(0);
        substitutionMap.put(PURCHASER_NAME, holderEmailData.getPurchaserName());
        substitutionMap.put(PURCHASER_EMAIL, holderEmailData.getPurchaserEmail());
        boolean isNewUser = false;
        Optional<User> userByEmail = roUserService.findOpUserByEmail(holderEmailData.getPurchaserEmail());
        if (userByEmail.isPresent() && StringUtils.isEmpty(userByEmail.get().getPassword())) {
            isNewUser = true;
        }
        String magicLink = eventUrl.concat(QUE_MARK_USER_KEY_EQUALS).concat(token).concat(AND_WAY_TO_LOGIN_EQUALS).concat("MAGIC_LINK").concat(AND_ATTENDEE_MAIL_EQUALS).concat(holderEmailData.getPurchaserEmail()).concat(AND_NEW_USER_EQUALS).concat(String.valueOf(isNewUser)).concat(AND_PASSWORD_LESS_EQUALS).concat("true");
        substitutionMap.put(EVENT_UC_URL, magicLink);
        substitutionMap.put(MERGE_TAG_MAGIC_LINK, magicLink);
        substitutionMap.put("isPasswordCreate", false);
        substitutionMap.put(ORDER_NUMBER, String.valueOf(holderEmailData.getOrderNumber()));
        String startDate = TimeZoneUtil.getFormattedDate(eventStartDate, "yyyyMMdd'T'HHmmss'Z'");
        String endDate = TimeZoneUtil.getFormattedDate(eventEndDate, "yyyyMMdd'T'HHmmss'Z'");
        String startDateForOutlook = TimeZoneUtil.getFormattedDate(eventStartDate, "YYYY-MM-DD'T'HH:mm:SS'Z'");
        String endDateForOutlook = TimeZoneUtil.getFormattedDate(eventEndDate, "YYYY-MM-DD'T'HH:mm:SS'Z'");
        Date eventStartDateTz = TimeZoneUtil.getDateInLocal(eventStartDate, event.getEquivalentTimeZone());
        Date eventEndDateTz = TimeZoneUtil.getDateInLocal(eventEndDate, event.getEquivalentTimeZone());
        String eventStartEndDateTime;
        if (eventStartDateTz.compareTo(eventEndDateTz) == 0) {
            eventStartEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true).concat(" at ")
                    .concat(StringTools.formatCalanderTime(eventStartDateTz, false)).concat("-")
                    .concat(StringTools.formatCalanderTime(eventEndDateTz, false));
        } else {
            eventStartEndDateTime = StringTools.formatCalanderDate(eventStartDateTz, true, true).concat(" at ")
                    .concat(StringTools.formatCalanderTime(eventStartDateTz, false)).concat("-")
                    .concat(StringTools.formatCalanderDate(eventEndDateTz, true, true)).concat(" at ")
                    .concat(StringTools.formatCalanderTime(eventEndDateTz, false));
        }


        if (!optionalRecurringEvents.isPresent() && recurringEventIds.isEmpty()) {
            substitutionMap.put(Constants.EVENT_START_END_DATETIME, event.isHideEventDate() ? "" : (eventStartEndDateTime + " " + event.getTimezoneId()));
        }
        String eventAddressForCal = null != eventAddress ? eventAddress : "Event address is not available";
        String urlOfEvent = "Click this link to see the event page: ".concat(apiBaseUrl).concat("/e/").concat(eventDesignDetail.getEvent().getEventURL());
        String seatsForCal = eventDesignDetail.getEventCalendarInvite() != null ? eventDesignDetail.getEventCalendarInvite() : urlOfEvent;
        if (seatsForCalendar.length() > 0) {
            seatsForCal = seatsForCal + "<p><br></p><p>Seat Detail :" + seatsForCalendar.toString();
        }
        substitutionMap.put("addToCalendarText", event.isHideEventDate() ? "" : EmailUtils.addToCalendar(eventName, startDate, endDate, seatsForCal, eventAddressForCal, true));
        substitutionMap.put("addToGoogleCalendar", EmailUtils.addToGoogleCalendar(eventName, startDate, endDate, seatsForCal, eventAddressForCal));
        substitutionMap.put("addToYahooCalendar", EmailUtils.addToYahooCalendar(eventName, startDate, endDate, seatsForCal, eventAddressForCal));
        substitutionMap.put("addToOutlookCalendar", EmailUtils.addToOutlookCalendar(eventName, startDateForOutlook, endDateForOutlook, seatsForCal, eventAddressForCal));
        substitutionMap.put("addToIcalCalendar", EmailUtils.generateIcalDownloadUrl(apiBaseUrl, event.getEventURL(), event.getEventId()));
        substitutionMap.put("isShowEventDate", !event.isHideEventDate());

        List<TicketHolderEmailDto> ticketHolderEmailDtos = emaildata.getTicketHolderEmail();
        boolean onlyDonationTickets = ticketHolderEmailDtos.stream().allMatch(TicketHolderEmailDto -> TicketType.DONATION.equals(TicketHolderEmailDto.getTicketType()));

        List<TicketHolderEmailDto> donationTicketTypDtos = ticketHolderEmailDtos.stream().filter(dto -> TicketType.DONATION.equals(dto.getTicketType())).collect(Collectors.toList());
        ticketHolderEmailDtos = (ticketHolderEmailDtos.stream().filter(e -> !(TicketType.DONATION.equals(e.getTicketType()))).collect(Collectors.toList()));

        substitutionMap.put("total_donation_tickets", !donationTicketTypDtos.isEmpty() ? String.valueOf(donationTicketTypDtos.size()) : null);
        substitutionMap.put("donation_amount", event.getCurrency().getSymbol().concat(String.valueOf(donationTicketTypDtos.stream().mapToDouble(TicketHolderEmailDto::getPaidAmount).sum())));
        substitutionMap.put("total_donation_amount", donationTicketTypDtos.stream().mapToDouble(TicketHolderEmailDto::getPrice).sum());
        substitutionMap.put("total_tickets", String.valueOf(ticketHolderEmailDtos.size()));
        substitutionMap.put("event_currency", event.getCurrency().getSymbol());
        substitutionMap.put("only_donation_tickets", onlyDonationTickets);
        //set value for rec
        substitutionMap.put(CLOUDINARY_IMAGE_PREFIX, imageConfiguration.getImagePreFixWithCloudinaryUrl());

        List<TicketHolderEmailDto> dtoForDisplay = new ArrayList<>();

        for (String key : stringListMap.keySet()) {
            TicketHolderEmailDto dto = new TicketHolderEmailDto();

            List<TicketHolderEmailDto> value = stringListMap.get(key);
            if (!value.get(0).getTicketType().equals(TicketType.DONATION)) {
                dto.setNumberOfTicketPerTable(value.size());
                dto.setTicketTypeName(key);
                dto.setTicketingTypePrice(value.get(0).getPrice());
                dtoForDisplay.add(dto);
            }
        }

        substitutionMap.put("dtoForDisplay", dtoForDisplay);
        substitutionMap.put("unsubscribe_link", "<center>".concat(getUnSubscribeUrl(event, emaildata.getTomail()).concat("</center>")));
        SimpleDateFormat formatter = new SimpleDateFormat("E, MMM dd, yyyy");
        String startDate1 = formatter.format(eventStartDateTz);
        substitutionMap.put(START_DATE, startDate1);
        String endDate1 = formatter.format(eventEndDateTz);
        substitutionMap.put(END_DATE, endDate1);

        SimpleDateFormat timeFormatter = new SimpleDateFormat("hh:mm a");
        String startTime = timeFormatter.format(eventStartDateTz);
        substitutionMap.put(START_TIME, startTime);
        String endTime = timeFormatter.format(eventEndDateTz);
        substitutionMap.put(END_TIME, endTime);
        String timezone = java.util.TimeZone.getTimeZone(event.getEquivalentTimeZone()).getDisplayName(false, java.util.TimeZone.SHORT);
        substitutionMap.put(EVENT_TIME_ZONE, ("(").concat(timezone).concat(")"));
        setEventLocation(event, substitutionMap, ticketing);
        substitutionMap.put(UNSUBSCRIBE_LINK, serviceHelper.getEventBaseUrl(event).concat(UNSUBSCRIBE_LINK_VALUE).concat(encode(Constants.EVENT_ID_UPPERCASE).concat("=").concat(String.valueOf(event.getEventId())).concat("&").concat(Constants.EMAIL).concat("=").concat( StringUtils.isNotBlank(emaildata.getTomail())? emaildata.getTomail() : STRING_BLANK )));
        String uiBaseUrl = serviceHelper.getEventBaseUrl(event.getWhiteLabel());
        substitutionMap.put(CREATE_EVENT, uiBaseUrl.concat("/events"));

        sendGridMailPrepareService.addTheValueOfFooterForNewTemplates(substitutionMap, event.getWhiteLabel());
        substitutionMap.put(IMAGE_LOWER, sendGridMailPrepareServiceImpl.getEventOrWhiteLabelLogoLocation(event));
        substitutionMap.put(IMAGE_FOOTER, sendGridMailPrepareServiceImpl.getEventOrWhiteLabelHeaderLogoLocation(event));
        substitutionMap.put(IS_EVENT_DATE_HIDE, event.isHideEventDate());
        PlanConfig planConfig = new PlanConfig();
        Optional<EventPlanConfig> eventPlanConfig = eventPlanConfigRepository.findByEventId(event.getEventId());
        if (eventPlanConfig.isPresent()) {
            planConfig = eventPlanConfig.get().getPlanConfig();
        }
        if (!EventFormat.IN_PERSON.equals(event.getEventFormat()) && (planConfig != null
                && (SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(planConfig.getPlanName())
                || STARTER.getName().equals(planConfig.getPlanName()) || FREE_PLAN.getName().equals(planConfig.getPlanName())))) {
            substitutionMap.put(IS_VIEW_EVENT_BUTTON_ENABLE, ticketing.isShowEnterEventButtonInReminderTemplate());
        } else {
            substitutionMap.put(IS_VIEW_EVENT_BUTTON_ENABLE, true);
        }
        substitutionMap.put(EVENT_CURRENCY, event.getCurrency().getSymbol());
        double eventTicketRefundAmount = 0;
        updateTicketTransferType(newTicketType, substitutionMap, eventTicketRefundAmount,eventTickets,event,ticketingOrder,isPaidTicketWithZeroAmount,ticketTransferTypesDto);

        /**/

        String wLHeaderLogo = "";
        if (null != event.getWhiteLabel()) {
            wLHeaderLogo = eventDesignDetailService.getWLHeaderLogo(event);
        }
        Set<String> receivers = new HashSet<>();
        receivers.add(emaildata.getTomail());
        substitutionMap.put(IS_VIRTUAL_EVENT, !IN_PERSON.equals(event.getEventFormat()));
        Attachments attachments = null;
        Attachments invoiceAttachment = null;
        String ticketPdf = "";
        long pdfDownloadCount = 0;  //it is used for allowPDFDownload flag in TicketingType
        long invoicePDFDownloadCount = 0;
        // attache pdf here
        ticketPdf = ticketingService.getTicketPdfByHost(event, Boolean.FALSE);
        if (null != event.getWhiteLabel()) {
            wLHeaderLogo = eventDesignDetailService.getWLHeaderLogo(event);
        }
        String eventOrWhiteLabelLogoForPdf = eventDesignDetailService.getEventOrWhiteLabelLogoLocation(event);
        String eventLogo = eventDesignDetailService.getEventOrDefaultLogo(event);
        String eventOrOrganizerLogoForInvoice = eventDesignDetailService.getEventOrOrganizerLogoLocationForInvoice(event);
        pdfDownloadCount = ticketHolderEmailDtos.stream().filter(TicketHolderEmailDto::getAllowPDFDownload).count();

        ticketHolderEmailDtos.forEach(dto ->
                Optional.ofNullable(ticketingTypeRepository.getTicketPdfDesignByTicketId(dto.getTicketingTypeId()))
                        .ifPresent(dto::setTicketCustomPdfDesign)
        );

        if (pdfDownloadCount > 0) {

            String whiteLabelUrl = "";
            String whiteLabelName = "";
            if(null != event.getWhiteLabel()){
                whiteLabelUrl = event.getWhiteLabel().getWhiteLabelUrl();
                whiteLabelName = event.getWhiteLabel().getFirmName();
            }

            List<TicketHolderPDFDto> ticketHolderPDFData = ticketHolderEmailDtos.stream().map(TicketHolderEmailDto::generateTicketHolderPDFDto).collect(Collectors.toList());
            TicketHolderPDFEventDataDto ticketHolderPDFEventDataDto = new TicketHolderPDFEventDataDto(false, ticketing.getEventAddress(),event.getEventId(),event.getName(),
                    event.getEventFormat().name(), event.getEquivalentTimeZone(),event.getTimezoneId(), event.getCurrency().getSymbol(),
                    ticketing.getChartKey(), ticketing.getEventStartDate(), ticketing.getEventEndDate(), ticketingOrder.getStatus().name(),
                    ticketPdf, StringUtils.isNoneBlank((ticketing.getTicketPdfDesign())),true,
                    event.isHideEventDate(), eventOrWhiteLabelLogoForPdf, imageConfiguration.getImagePrefix() + imageConfiguration.getDefaultHeader(),
                    wLHeaderLogo,
                    whiteLabelUrl, whiteLabelName,eventLogo
            );
            attachments = ticketingPdfCreator.generatePDF(ticketHolderPDFEventDataDto, ticketHolderPDFData);
        }
        if (eventTickets != null && !StringUtils.isEmpty(eventTickets.getHolderEmail())) {
            invoicePDFDownloadCount = ticketHolderEmailDtos.stream().filter(TicketHolderEmailDto::isAllowInvoicePDFDownloadForHolder).count();
            if (invoicePDFDownloadCount == 0L && eventTickets.getHolderEmail().equals(ticketingOrder.getBuyerEmail())) {
                invoicePDFDownloadCount = ticketHolderEmailDtos.stream().filter(TicketHolderEmailDto::getAllowInvoicePDFDownloadForBuyer).count();
            }
        } else {
            invoicePDFDownloadCount = ticketHolderEmailDtos.stream().filter(TicketHolderEmailDto::getAllowInvoicePDFDownloadForBuyer).count();
        }

        if (invoicePDFDownloadCount > 0) {
            invoiceAttachment = generateInvoiceAttachment(event, ticketingOrder, ticketing, eventOrOrganizerLogoForInvoice, wLHeaderLogo, purchaser, false, eventTickets, eventTicketRefundAmount,oldTicketingType.getTicketType(),newTicketType.getTicketType());
        }
        if (seatsForCalendar.length() > 0) {
            seatsForCal = seatsForCal + "<p><br></p><p>Seat Detail :" + seatsForCalendar.toString();
        }

        CustomTemplates emailTemplates = new CustomTemplates();
        emailTemplates=confirmationEmailService.getConfirmationEmailByOrder(event,ticketingOrder.getId());
        if(emailTemplates!=null && emailTemplates.isSaveAsDraft()){
            emailTemplates=confirmationEmailService.findByEventAndIsDefaultEmail(event,true, TemplateType.CONFIRMATION_EMAIL);
        }
        CustomEmailsTemplateDto customTemplatesDto = new CustomEmailsTemplateDto();
        if(emailTemplates != null){
            customTemplatesDto = CustomEmailsTemplateDto.toDto(emailTemplates);
        }

        if (TemplateId.BEEFREE_DEFAULT_ATTENDEE_ORDER_UPDATE_CONFIRMATION_EMAIL.getValue().equals(emailMessage.getTemplateName())) {
            String customInviteLocation = customTemplatesDto.getCustomInviteLocation();
            String customInviteTitle = customTemplatesDto.getCustomInviteTitle();

            String locationToUse;
            if(StringUtils.isNotBlank(customInviteLocation)){
                locationToUse = extractLocationFromMergeTags(customInviteLocation, substitutionMap);
            }else{
                locationToUse = eventAddress;
            }

            String titleToUse = StringUtils.isNotBlank(customInviteTitle) ? customInviteTitle : eventName;
            emailMessage = sendGridMailPrepareServiceImpl.setAttachmentForInviteMailNotification(emailMessage, attachments, pdfDownloadCount, event, titleToUse, startDate, endDate, seatsForCal, locationToUse, false, invoiceAttachment, invoicePDFDownloadCount, Boolean.TRUE,eventTickets.getHolderEmail());
        }

        log.info("Before Sending email service");

        sendGridMailService.sendTemplateMail(emailMessage, receivers);

        log.info("sendReceiptTicketing end");
    }

    private static String extractLocationFromMergeTags(String input, Map<String, Object> substitutionMap) {
        if (input.isBlank()) {
            return input;
        }

        StringBuffer resolved = new StringBuffer();

        try {
            Matcher matcher = MERGE_TAG_PATTERN.matcher(input);
            while (matcher.find()) {
                String key = matcher.group(1);
                Object valueObj = substitutionMap.get(key);
                if (valueObj != null) {
                    matcher.appendReplacement(resolved, Matcher.quoteReplacement(valueObj.toString()));
                }
            }

            matcher.appendTail(resolved);
        } catch(Exception ex) {
            log.error("Failed to replace merge tag in input: {}, Reason: {}", input, ex.getMessage());
            return input;
        }
        return resolved.toString();
    }

    private void updateTicketTransferType(TicketingType newTicketType, Map<String, Object> substitutionMap, double eventTicketRefundAmount,EventTickets eventTickets,Event event,TicketingOrder ticketingOrder,boolean isPaidTicketWithZeroAmount,TicketTransferTypesDto ticketTransferTypesDto) {

        substitutionMap.put(IS_FREE_TO_FREE_TICKET_TRANSFER, ticketTransferTypesDto.isFreeToFreeTicketTransfer());
        substitutionMap.put(IS_PAID_TO_FREE_TICKET_TRANSFER, ticketTransferTypesDto.isPaidToFreeTicketTransfer());
        substitutionMap.put(IS_FREE_TO_PAID_TICKET_TRANSFER, ticketTransferTypesDto.isFreeToPaidTicketTransfer());
        substitutionMap.put(IS_LOWER_TO_HIGHER_TICKET_TRANSFER,ticketTransferTypesDto.isLowerToHigherTicketTransfer());
        substitutionMap.put(IS_HIGHER_TO_LOWER_TICKET_TRANSFER,ticketTransferTypesDto.isHigherToLowerTicketTransfer());
        substitutionMap.put(IS_SAME_PRICE_TICKET_TRANSFER,ticketTransferTypesDto.isSamePriceTicketTransfer());
        substitutionMap.put(IS_COMPLEMENTARY_TICKET_TRANSFER,ticketTransferTypesDto.isComplementaryTicketTransfer());
        substitutionMap.put(IS_PAYMENT_DONE_AT_TICKET_TRANSFER, ticketTransferTypesDto.isPaymentDoneAtTicketTransfer());
        substitutionMap.put(IS_UNPAID_ORDER,false);
        substitutionMap.put(IS_PAID_ORDER, false);
        double totalPaidAmount = eventTickets.getPaidAmount() - eventTickets.getRefundedAmount();
        double newTicketPrice = newTicketType.getPrice();

        if (ticketTransferTypesDto.isPaidToFreeTicketTransfer()) {
            setRefundAmountForTicketTransfer(eventTickets, event, ticketingOrder, substitutionMap,eventTicketRefundAmount);
            substitutionMap.put(PAID_AMOUNTS, 0);
        } else if(ticketTransferTypesDto.isFreeToPaidTicketTransfer()) {
            substitutionMap.put(IS_FREE_TO_PAID_TICKET_TRANSFER, true);
            if (TicketingOrder.TicketingOrderStatus.PAID.equals(ticketingOrder.getStatus())){
                substitutionMap.put(PAID_AMOUNTS, eventTickets.getPaidAmount());
                substitutionMap.put(IS_PAID_ORDER, true);
            }else {
                substitutionMap.put(UNPAID_AMOUNT, newTicketType.getPrice() - eventTickets.getDiscountAmount());
                substitutionMap.put(IS_UNPAID_ORDER,true);
            }
        } else if (ticketTransferTypesDto.isPaidToPaidTicketTransfer()) {
            updateTicketExchangeTypeForPaidTickets(totalPaidAmount,newTicketPrice,substitutionMap,eventTickets,eventTicketRefundAmount,event,ticketingOrder,ticketTransferTypesDto);
        } else if (ticketTransferTypesDto.isComplementaryTicketTransfer()) {
            substitutionMap.put(IS_REFUND_AMOUNT_AVAILABLE, false);
        }
    }

    private void updateTicketExchangeTypeForPaidTickets(double totalPaidAmount, double newTicketPrice, Map<String, Object> substitutionMap, EventTickets eventTickets, double eventTicketRefundAmount, Event event, TicketingOrder ticketingOrder, TicketTransferTypesDto ticketTransferTypesDto) {
        if (ticketTransferTypesDto.isLowerToHigherTicketTransfer()) {
            //lower to higher ticket transfer
            substitutionMap.put("unpaid_amount",newTicketPrice - totalPaidAmount);
        } else if (ticketTransferTypesDto.isSamePriceTicketTransfer()) {
            //same amount paid ticket transfer
            substitutionMap.put(IS_REFUND_AMOUNT_AVAILABLE, false);
            substitutionMap.put(PAID_AMOUNTS,totalPaidAmount);
        } else if (ticketTransferTypesDto.isHigherToLowerTicketTransfer()) {
            //higher to lower ticket transfer
            substitutionMap.put(IS_HIGHER_TO_LOWER_TICKET_TRANSFER,true);
            setRefundAmountForTicketTransfer(eventTickets, event, ticketingOrder, substitutionMap,eventTicketRefundAmount);
            substitutionMap.put(PAID_AMOUNTS, eventTickets.getPaidAmount());
        }
    }

    private void setRefundAmountForTicketTransfer(EventTickets eventTickets, Event event, TicketingOrder ticketingOrder, Map<String, Object> substitutionMap,double eventTicketRefundAmount) {
        List<EventTicketTransferDetail> eventTicketTransferDetails = eventTicketTransferDetailService.findByEventIdAndEventTicketIdAndTicketingOrderId(event.getEventId(), eventTickets.getId(), ticketingOrder.getId());
        List<EventTicketRefundTracker> eventTicketRefundTracker = tableRefundService.findByEventTicketId(eventTickets.getId());
        if (!eventTicketTransferDetails.isEmpty()) {
            if (!eventTicketRefundTracker.isEmpty()) {
                eventTicketRefundAmount = eventTicketRefundTracker.get(eventTicketRefundTracker.size() - 1).getRefundAmount();
                substitutionMap.put(REFUNDED_AMOUNTS, eventTicketRefundAmount);
                substitutionMap.put(IS_REFUND_AMOUNT_AVAILABLE, true);
            } else {
                substitutionMap.put(REFUNDED_AMOUNTS, 0);
                substitutionMap.put(IS_REFUND_AMOUNT_AVAILABLE, false);
            }
        } else {
            log.warn("No event ticket transfer details found for event id : {} and event ticket id : {} and ticketing order id : {}", event.getEventId(), eventTickets.getId(), ticketingOrder.getId());
        }
    }

    private Attachments generateInvoiceAttachment(Event event, TicketingOrder ticketingOrder, Ticketing ticketing,
                                                  String eventOrWhiteLabelLogo, String wLHeaderLogo, User purchaser, boolean isCreditNotePDF, EventTickets eventTickets, double refundAmountWithoutFees,TicketType ticketTypeOldTicket,TicketType ticketTypeNewTicket) {
        try {
            log.info("TicketingOrderTransferServiceImpl generateInvoiceAttachment start");
            String fileName = isCreditNotePDF ? "Ticket" + event.getName() + "_CreditNote.pdf" : "Ticket" + event.getName() + "_invoice.pdf";
            if (purchaser == null) {
                purchaser = ticketingOrder.getPurchaser();
            }
            TicketOrderInvoiceDto ticketOrderInvoiceDto = new TicketOrderInvoiceDto();
            ticketOrderInvoiceDto
                    .setPurchaserName(purchaser.getFirstName() + Constants.STRING_BLANK + purchaser.getLastName());
            ticketOrderInvoiceDto.setDateOfPurchase(ticketingOrder.getOrderDate());
            ticketOrderInvoiceDto.setOrderNumber(ticketingOrder.getId());
            String holderFirstName = StringUtils.isBlank(eventTickets.getHolderFirstName()) ? STRING_DASH : eventTickets.getHolderFirstName();
            String holderLastName = StringUtils.isBlank(eventTickets.getHolderLastName()) ? STRING_DASH : eventTickets.getHolderLastName();
            List<EventTicketTransferDetail> eventTicketTransferDetails = eventTicketTransferDetailService.findByEventIdAndEventTicketIdAndTicketingOrderId(event.getEventId(), eventTickets.getId(), ticketingOrder.getId());
            log.info("eventTicketTransferDetails size : {} and orderId {}", eventTicketTransferDetails.size(), ticketingOrder.getId());
            if (!eventTicketTransferDetails.isEmpty()) {
                EventTicketTransferDetail eventTicketTransferDetail = eventTicketTransferDetails.get(eventTicketTransferDetails.size() - 1);
                ticketOrderInvoiceDto.setHolderFullName(holderFirstName + Constants.STRING_BLANK + (StringUtils.isNotBlank(holderLastName) ? holderLastName : ""));
                ticketOrderInvoiceDto.setTotalTicektPrice(DoubleHelper.roundValueTwoDecimal(eventTickets.getTicketPrice()));
                ticketOrderInvoiceDto.setNewTicketTypeName(eventTickets.getTicketingTypeId().getTicketTypeName());
                TicketingType oldTicketType = ticketingTypeRepository.findTicketingTypeById(eventTicketTransferDetail.getOldTicketingTypeId());
                ticketOrderInvoiceDto.setOldTicketTypeName(oldTicketType.getTicketTypeName());
                ticketOrderInvoiceDto.setRefundAmount(refundAmountWithoutFees);
                log.info("generateInvoiceAttachment getRefundedAmount: {} and orderId: {}", eventTicketTransferDetail.getRefundedAmount(), ticketingOrder.getId());
                ticketOrderInvoiceDto.setEventTicketId(String.valueOf(eventTickets.getId()));
            } else {
                log.warn("eventTicketTransferDetails is empty for ticketingOrderId: {}", ticketingOrder.getId());
            }

            if (!StringUtils.isEmpty(ticketing.getCustomInvoiceText())) {
                if (ticketing.getCustomInvoiceText().contains(NBSP_STRING)) {
                    ticketing.setCustomInvoiceText(ticketing.getCustomInvoiceText().replace(NBSP_STRING, STRING_EMPTY));
                }
                ticketOrderInvoiceDto.setEventLevelCustomInvoiceText(ticketing.getCustomInvoiceText());
            }
            double applicationFee = sendGridMailPrepareServiceImpl.getApplicationFeeAndCCFees(Collections.singletonList(eventTickets));
            ticketOrderInvoiceDto.setServiceFee(applicationFee);
            ticketOrderInvoiceDto.setTotalTicektPrice(eventTickets.getTicketPrice());
            ticketOrderInvoiceDto.setTotalPrice(eventTickets.getPaidAmount());
            if (TicketType.FREE.equals(ticketTypeOldTicket) && TicketType.PAID.equals(ticketTypeNewTicket) || TicketPaymentStatus.UNPAID.equals(eventTickets.getTicketPaymentStatus())) {
                ticketOrderInvoiceDto.setServiceFee(0);
                ticketOrderInvoiceDto.setTotalPrice(eventTickets.getTicketPrice());
            }
            ticketOrderInvoiceDto.setEventFormat(event.getEventFormat().name());
            ticketOrderInvoiceDto = sendGridMailPrepareServiceImpl.updateBillingAddress(Collections.singletonList(eventTickets), ticketOrderInvoiceDto);
            ticketOrderInvoiceDto = sendGridMailPrepareServiceImpl.updateOrganizerAddress(event, ticketOrderInvoiceDto);
            eventOrWhiteLabelLogo = eventOrWhiteLabelLogo.replace("1-300x50/", "");
            if (isCreditNotePDF) {
                ticketOrderInvoiceDto.setPaymentStatus("CREDIT NOTE (REFUND)");
            } else {
                ticketOrderInvoiceDto.setPaymentStatus(TicketingOrder.TicketingOrderStatus.UNPAID.equals(ticketingOrder.getStatus()) ? "Payment Due" : "PAID");
            }
            TicketHolderAndTicketDetails ticketHolderAndTicketDetails = new TicketHolderAndTicketDetails();
            ticketHolderAndTicketDetails.setHolderFullName(holderFirstName + STRING_BLANK + (StringUtils.isNotBlank(holderLastName) ? holderLastName : ""));
            ticketHolderAndTicketDetails.setEventTicketId(String.valueOf(eventTickets.getId()));
            ticketOrderInvoiceDto.setTicketHolderAndTicketDetails(List.of(ticketHolderAndTicketDetails));
            return createTicketInvoiceOrderPdF(ticketOrderInvoiceDto, null, event, ticketing, eventOrWhiteLabelLogo, wLHeaderLogo, fileName);
        } catch (Exception e) {
            log.info("TicketingOrderTransferServiceImpl | generateInvoiceAttachment getting error while creating invoice PDF and orderId {}", ticketingOrder.getId());
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public Attachments createTicketInvoiceOrderPdF(TicketOrderInvoiceDto ticketOrderInvoiceDto, RecurringEvents recurringEvent, Event event, Ticketing ticketing,
                                                   String eventOrWhiteLabelLogo, String wLHeaderLogo, String fileName) throws TemplateException, com.itextpdf.text.DocumentException, IOException {
        log.info("TicketingOrderTransferServiceImpl createTicketInvoiceOrderPdF start and orderId {}", ticketOrderInvoiceDto.getOrderNumber());
        Date eventStartDate = ticketing.getEventStartDate();
        Date eventEndDate = ticketing.getEventEndDate();
        if (null != recurringEvent) {
            eventStartDate = TimeZoneUtil.getDateInUTC(recurringEvent.getRecurringEventStartDate(), event.getEquivalentTimeZone());
            eventEndDate = TimeZoneUtil.getDateInUTC(recurringEvent.getRecurringEventEndDate(), event.getEquivalentTimeZone());
        }
        return generateInvoicePDF(ticketing, ticketOrderInvoiceDto, event, eventStartDate, eventEndDate,
                eventOrWhiteLabelLogo, imageConfiguration.getImagePrefix().concat(imageConfiguration.getDefaultHeader()), wLHeaderLogo, fileName);
    }

    public Attachments generateInvoicePDF(Ticketing ticketing,
                                          TicketOrderInvoiceDto ticketOrderInvoiceDto, Event event, Date eventStartDate,
                                          Date eventEndDate, String eventOrWhiteLabelLogo, String accelLogo,
                                          String eventOrWhiteLabelHeaderLogoLocation, String fileName) throws DocumentException, TemplateException, IOException {
        log.info("TicketingOrderTransferServiceImpl generateInvoicePDF start and orderId {}", ticketOrderInvoiceDto.getOrderNumber());
        Attachments attachments = new Attachments();
        attachments.setType("application/pdf");
        attachments.setFilename(fileName);
        attachments.setDisposition("attachment");
        attachments.setContentId("Banner");

        // PDF generation goes here
        OutputStream outputStream = createInvoicePDFOutputStream(ticketing, ticketOrderInvoiceDto,
                event, eventStartDate, eventEndDate, eventOrWhiteLabelLogo, accelLogo,
                eventOrWhiteLabelHeaderLogoLocation, new ByteArrayOutputStream());

        byte[] bytes = ((ByteArrayOutputStream) outputStream).toByteArray();

        org.apache.commons.codec.binary.Base64 x = new Base64();
        String imageDataString = x.encodeAsString(bytes);
        attachments.setContent(imageDataString);

        return attachments;
    }

    public OutputStream createInvoicePDFOutputStream(Ticketing ticketing,
                                                     TicketOrderInvoiceDto ticketOrderInvoiceDto, Event event, Date eventStartDate, Date eventEndDate,
                                                     String eventOrWhiteLabelLogo, String accelLogo,
                                                     String eventOrWhiteLabelHeaderLogoLocation, OutputStream outputStream)
            throws DocumentException, IOException, TemplateException {

        String whiteLabelName = "";
        PDFCreator pdfCreator = new PDFCreatorImpl();
        if (null != event.getWhiteLabel()) {
            whiteLabelName = event.getWhiteLabel().getFirmName();
        }

        String eventAddress = ticketing.getEventAddress();
        String eventLogo = eventDesignDetailService.getEventOrDefaultLogo(event);

        Map<String, Object> invoiceData = TicketingInvoicePDFHelper.ticketInvoicePDFNew(ticketOrderInvoiceDto, GeneralUtils.removeUnicode(event.getName()), eventStartDate,//NOSONAR
                eventEndDate, event.getTimezoneId(), event.getEquivalentTimeZone(), eventOrWhiteLabelLogo,
                eventAddress, event.getCurrency().getSymbol(), ticketing.getChartKey(),
                Objects.isNull(event.getWhiteLabel()), accelLogo,
                whiteLabelName, eventOrWhiteLabelHeaderLogoLocation, event.isHideEventDate(),eventLogo);

        pdfCreator.createInvoicePDF(TemplateId.TICKET_INVOICE_EXCHANGE_ORDER_PDF, invoiceData, outputStream);

        return outputStream;
    }

    private double getVatFeeForUpdateTicketEmail(EventTickets eventTickets) {
        return BigDecimal.valueOf(eventTickets.getVatTaxFee()).subtract(BigDecimal.valueOf(eventTickets.getRefundedVatTaxFee())).doubleValue();
    }

    private double getSalesTaxFeeForUpdateTicketEmail(EventTickets eventTickets) {
        return BigDecimal.valueOf(eventTickets.getSalesTaxFee()).subtract(BigDecimal.valueOf(eventTickets.getRefundedSalesTaxFee())).doubleValue();
    }

    private String getPaymentStatus(TicketingOrder ticketingOrder, boolean isAllFreeTickets) {
        if (EXTERNAL_TRANSACTION.equals(ticketingOrder.getOrderType().name())) {
            return PAID;
        } else if (isAllFreeTickets) {
            return FREE.toUpperCase();
        }
        return ticketingOrder.getOrderType().name().equals(Constants.PAY_LATER) ? INVOICE :
                ticketingOrder.getOrderType().name();
    }

    private String getHolderNameWithColonAndDashForEmail(String holderName, String stringBlank, String stringDash, String stringBlank2, String autoSequeneceName, String stringColon, String stringBlank3, String autoAssignedNumber) {
        return holderName.concat(stringBlank).concat(stringDash).concat(stringBlank2).concat(autoSequeneceName).concat(stringColon).concat(stringBlank3).concat(autoAssignedNumber);
    }

    private String getUnSubscribeUrl(Event event, String email) {
        String unSubscribeUrl = serviceHelper.getEventBaseUrl(event).concat("/u/unsubscribe?key=").concat(encode(Constants.EVENT_ID_UPPERCASE.concat("=").concat(String.valueOf(event.getEventId())).concat("&").concat(Constants.EMAIL).concat("=").concat(StringUtils.isNotBlank(email)? email:STRING_BLANK)));
        return StringUtils.replace(UNSUBSCRIBE_EMAIL_NOTIFICATION, "[unsubscribe_url]", unSubscribeUrl);
    }

    public void setEventLocation(Event event, Map<String, Object> substitutionMap, Ticketing ticketing) {
        if (IN_PERSON.equals(event.getEventFormat()) || HYBRID.equals(event.getEventFormat())) {
            String location = StringUtils.isBlank(ticketing.getEventAddress()) ? TO_BE_ANNOUNCED : Arrays.stream(ticketing.getEventAddress().split(",")).findFirst().get();
            substitutionMap.put(EVENT_LOCATION, location);
        } else {
            substitutionMap.put(EVENT_LOCATION, "Online Event");
        }
    }

    private String getEventUrlWithOrderId(Event event, long orderId) {
        String eventUrlWithOrderId;
        eventUrlWithOrderId = serviceHelper.getEventBaseUrl(event) + getOrderUrl();
        if (orderId > 0) {
            eventUrlWithOrderId = eventUrlWithOrderId + orderId;
        }
        return eventUrlWithOrderId;
    }

    @Override
    @Transactional
    public TicketTransferDueAmountDto getDueAmountForLowerToHigherTicketTransfer(TicketExchangeDto ticketExchangeDto, Event event, User user) {
        Long orderId = ticketExchangeDto.getOrderId();
        log.info("TicketingOrderTransferServiceImpl getDueAmountForLowerToHigherTicketTransfer start for orderId {} eventId {} ", orderId, event.getEventId());
        EventTickets eventTicket = getValidEventTicket(ticketExchangeDto.getEventTicketId());
        TicketingType oldTicketingType = eventTicket.getTicketingTypeId();

        AttendeePartialPaymentDto paymentDetails = ticketExchangeDto.getPaymentDetails();
        boolean isInvoicePayment = paymentDetails.getPaymentType().equals(INVOICE.toUpperCase());
        boolean isCashPaymentType = !paymentDetails.getPaymentType().equals(CARD);
        boolean oldTicketPaidByCash = eventTicket.getPaidAmount()>0 && StringUtils.isBlank(eventTicket.getChargeId());

        /* AS Mixed payment type is implement, So We not need to check valid payment method.
         // As backup plan, we keep this method as commented, once the Mixed payment is tested well then we can remove this line of code and method also
        // isValidPaymentMethod(oldTicketingType.getTicketType(), null, isInvoicePayment, isCashPaymentType, oldTicketPaidByCash);
         */

        // Need to find new Ticket type details from event_ticket_type table
        TicketingType newTicketingType = getNewTicketingType(ticketExchangeDto.getNewTicketTypeId(), eventTicket.getTicketingTypeId());
        double dueAmount = 0.0;
        double discountAmount = 0.0;
        if((Arrays.asList(TicketType.FREE, TicketType.PAID).contains(oldTicketingType.getTicketType()) || TicketType.DONATION.equals(oldTicketingType.getTicketType())) && (TicketType.PAID.equals(newTicketingType.getTicketType()) ||  TicketType.DONATION.equals(newTicketingType.getTicketType()))) {
            EventTicketFeesDto eventTicketFeesDto = ticketingPurchaseService.calculateFeeForLowerToHigherTicketTransfer(eventTicket, newTicketingType, event, isInvoicePayment ? oldTicketPaidByCash : isCashPaymentType, eventTicket.getVatTaxFee(), ticketExchangeDto.getCouponCode());
            dueAmount = eventTicketFeesDto.getEventTicketPaidAmount();
            discountAmount = eventTicketFeesDto.getDiscountAmount();
            log.info("TicketingOrderTransferServiceImpl getDueAmountForLowerToHigherTicketTransfer end for orderId {} eventId {} oldTicketPaidByCash {} dueAmount {} ", orderId, event.getEventId(), oldTicketPaidByCash,  dueAmount);
        }
        return new TicketTransferDueAmountDto(oldTicketPaidByCash ? EventTicketTransaction.PaymentType.CASH : EventTicketTransaction.PaymentType.CARD, GeneralUtils.getRoundValue(dueAmount), GeneralUtils.getRoundValue(discountAmount));
    }

    private void validateTicketExchangeIsAllowed(Event event) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        if (!ticketing.isAllowTicketExchange()) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_EXCHANGE_NOT_ALLOWED);
        }
    }

//    we implement order exchange feature for which order have all free tickets
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void exchangeOrderForFreeToFreeTickets(Long orderId, OrderExchangeDto orderExchangeDto, Event event, User user, boolean isAdmin) {
        log.info("exchangeOrderForFreeToFreeTickets started for Order => {} by User => {} in Event => {} isAdmin => {}", orderId, user.getUserId(), event.getEventId(), isAdmin);

        TicketingOrder ticketingOrder = getValidTicketOrder(orderId);
        Ticketing ticketing = ticketingService.findByEvent(event);

        List<TicketDetailExchangeDetailDto> ticketDetailExchangeDetailDtoList = orderExchangeDto.getTicketDetailExchangeDetailDtoList();
        List<EventTickets> eventTicketsList = eventTicketsRepoService.getEventTicketByIdsAndNotCanceled(ticketDetailExchangeDetailDtoList.stream().map(TicketDetailExchangeDetailDto::getEventTicketId).collect(Collectors.toList()));
        validateEventTickets(eventTicketsList, ticketingOrder.getId());

        List<Long> oldTicketTypeIds = eventTicketsList.stream()
                .map(EventTickets::getTicketingTypeOnlyId)
                .collect(Collectors.toList());

        Map<Long, EventTickets> eventTicketIdAndEventTicketMap = eventTicketsList.stream()
                .collect(Collectors.toMap(EventTickets::getId, Function.identity()));

        ticketDetailExchangeDetailDtoList.removeIf(dto ->
                eventTicketIdAndEventTicketMap.get(dto.getEventTicketId()).getTicketingTypeOnlyId().equals(dto.getNewTicketTypeId())
        );


        List<Long> newTicketTypeIds = ticketDetailExchangeDetailDtoList.stream()
                .map(TicketDetailExchangeDetailDto::getNewTicketTypeId)
                .collect(Collectors.toList());

        boolean allFree = eventTicketsList.stream()
                .allMatch(ticket -> TicketType.FREE.equals(ticket.getTicketingTypeId().getTicketType()));
        if (!allFree) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.ORDER_EXCHANGE_ALLOWED_ONLY_FOR_FREE_TICKETS);
        }

        // Skip ticket exchange mapping rule validation if user is admin
        if (!isAdmin) {
            List<TicketExchangeMappingRuleDTO> ticketExchangeMappingRuleDTOS = ticketExchangeRuleService.getTicketMappingByTicketIds(oldTicketTypeIds);
            isValidExchange(ticketDetailExchangeDetailDtoList, ticketExchangeMappingRuleDTOS, eventTicketIdAndEventTicketMap);
        } else {
            log.info("exchangeOrderForFreeToFreeTickets skipped ticket exchange mapping rule validation as user is admin");
            EventLevelSettingsDTO eventLevelSettings = roEventLevelSettingService.getEventLevelSettings(event);
            if (!eventLevelSettings.isAllowOrderExchange()){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.ORDER_EXCHANGE_NOT_ALLOWED);
            }
        }

        Map<Long, Long> ticketTypeIdAndNoOfTicketsMap = newTicketTypeIds.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        List<TicketingType> newTicketingTypeList = ticketingTypeService.findByidInAndEvent(newTicketTypeIds, event);

        allFree = newTicketingTypeList.stream()
                .allMatch(ticketType -> TicketType.FREE.equals(ticketType.getTicketType()));
        if (!allFree) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.ORDER_EXCHANGE_ALLOWED_ONLY_FOR_FREE_TICKETS);
        }

        Map<Long, TicketingType> ticketingTypeIdAndTicketingTypeMap = newTicketingTypeList.stream()
                .collect(Collectors.toMap(TicketingType::getId, Function.identity()));

        List<TicketingOrderManager> ticketingOrderManagerList = ticketingOrderManagerService.getTicketingOrderManagerByListOfOrderIds(List.of(ticketingOrder.getId()));;
        Map<Long, TicketingOrderManager> ticketTypeIdAndTicketingOrderManagerMap = ticketingOrderManagerList.stream()
                .collect(Collectors.toMap(
                        tom -> tom.getTicketType().getId(),
                        tom -> tom
                ));

        List<TicketTypeSoldAndBookCountDto> ticketTypeAndSoldTicketsList = ticketingStatisticsService.getSoldTicketsByTicketingTypeIds(newTicketTypeIds);
        Map<Long, Long> ticketTypeIdAndSoldTicketMap = CollectionUtils.isEmpty(ticketTypeAndSoldTicketsList)
                ? Collections.EMPTY_MAP : ticketTypeAndSoldTicketsList.stream().collect(Collectors.toMap(
                        TicketTypeSoldAndBookCountDto::getTicketTypeId,
                        TicketTypeSoldAndBookCountDto::getSoldCount
                ));

        List<TicketTypeSoldAndBookCountDto> ticketTypeAndBookTicketsList = ticketingStatisticsService.getBookTicketsByTicketingTypes(newTicketingTypeList);
        Map<Long, Long> ticketTypeIdAndBookTicketMap = CollectionUtils.isEmpty(ticketTypeAndBookTicketsList)
                ? Collections.EMPTY_MAP : ticketTypeAndBookTicketsList.stream().collect(Collectors.toMap(
                TicketTypeSoldAndBookCountDto::getTicketTypeId,
                TicketTypeSoldAndBookCountDto::getSoldCount
        ));

        List<EventTicketTransferDetail> eventTicketTransferDetailList = new ArrayList<>();
        String messageForOrderAuditLog = "Your order has been exchanged";

        for (TicketDetailExchangeDetailDto ticketDetailExchangeDetail : ticketDetailExchangeDetailDtoList) {
            EventTickets eventTicket = eventTicketIdAndEventTicketMap.get(ticketDetailExchangeDetail.getEventTicketId());

            TicketingType oldTicketingType = eventTicket.getTicketingTypeId();
            // Need to find new Ticket type details from event_ticket_type table
            TicketingType newTicketingType = ticketingTypeIdAndTicketingTypeMap.get(ticketDetailExchangeDetail.getNewTicketTypeId());
            // Need to validate new Ticket type for available tickets from event_ticket_type table

            Long soldTicketCount = ticketTypeIdAndSoldTicketMap.getOrDefault(newTicketingType.getId(),0L);
            if (!TicketBundleType.INDIVIDUAL_TICKET.equals(newTicketingType.getBundleType()) && newTicketingType.getNumberOfTicketPerTable() > 0) {
                soldTicketCount = soldTicketCount / newTicketingType.getNumberOfTicketPerTable();
            }
            long bookedTicketCount = ticketTypeIdAndBookTicketMap.getOrDefault(newTicketingType.getId(),0L);
            long remainingTickets = (newTicketingType.getNumberOfTickets() - (soldTicketCount + bookedTicketCount));

            if (ticketTypeIdAndNoOfTicketsMap.getOrDefault(newTicketingType.getId(),0L) > remainingTickets) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_NOT_AVAILABLE_SALE);
            }

            log.info("TicketingOrderTransferServiceImpl -> exchangeOrderForFreeToFreeTickets -> eventTicketId -> {} -> oldTicketTypeId -> {} -> newTicketTypeId -> {}",
                    eventTicket.getId(), oldTicketingType.getId(), newTicketingType.getId());

            // update ticketing_order_manager table with new ticket type id and status
            ticketTypeIdAndTicketingOrderManagerMap = updateTicketTypeInTicketingOrderManagerForOrderExchange(ticketingOrder, newTicketingType, eventTicket, ticketTypeIdAndTicketingOrderManagerMap, oldTicketingType.getId());

            // update event_ticket_transfer_detail table with new ticket type id available tickets
            eventTicketTransferDetailList.add(eventTicketTransferDetailService.eventTicketTransferDetailForOrderExchange(orderId, eventTicket, oldTicketingType.getId(), newTicketingType.getId(), event, user, isAdmin));

            // update event_ticket table with new ticket type id available tickets
            eventTicket.setTicketingTypeId(newTicketingType);
            eventTicket.setTicketStatus(TicketStatus.REGISTERED);
            eventTicket.setChargeId(null);
            eventTicket.setTicketPrice(0);
            eventTicket.setPaidAmount(0);
            eventTicket.setAeFeeAmount(0);
            eventTicket.setSalesTaxFee(0);
            eventTicket.setVatTaxFee(0);
            eventTicket.setWlAFeeAmount(0);
            eventTicket.setWlBFeeAmount(0);
            eventTicket.setDiscountAmount(0);
            eraseEventTicketFees(eventTicket);
            eventTicket = eventTicketsRepository.save(eventTicket);

            //Sent mail to attendee for update ticketing type
            TicketTransferTypesDto ticketTransferTypesDto=new TicketTransferTypesDto();
            ticketTransferTypesDto.setFreeToFreeTicketTransfer(true);

            String mailSubject = "Your Exchanged Order Confirmation for " + event.getName();
            handleEmailForTicketTransfer(event, newTicketingType, oldTicketingType, ticketingOrder, ticketing, eventTicket,
                    false, false, mailSubject, false, ticketTransferTypesDto, orderExchangeDto.isSentMailToAttendee(), user);

            afterTaskIntegrationTriggerService.onTicketTypeTransferEvent(eventTicket, oldTicketingType);
        }
        eventTicketTransferDetailService.saveAll(eventTicketTransferDetailList);

        // No need to update ticketingOrder if exchange is free to free
        // updateTicketOrderBasedOnTicketTypeChanged(ticketingOrder);
        ticketingOrder.setUpdatedAt(new Date());
        ticketingOrderRepository.save(ticketingOrder);

        // Add log in order audit log table for ticket transfer
        ticketingRefundService.orderAuditLog(event, user, orderId, STRING_EMPTY, STRING_EMPTY, "" + messageForOrderAuditLog + " by " + user.getFirstName() + " " + user.getLastName());
    }

    // update ticketing order manager for only order Exchange
    private Map<Long, TicketingOrderManager> updateTicketTypeInTicketingOrderManagerForOrderExchange(TicketingOrder ticketingOrder, TicketingType newTicketingType, EventTickets eventTicket, Map<Long, TicketingOrderManager> ticketTypeIdAndTicketingOrderManagerMap, Long oldTicketingTypeId) {
        TicketingOrderManager oldTicketingOrderManager = ticketTypeIdAndTicketingOrderManagerMap.get(oldTicketingTypeId);
        TicketingOrderManager newTicketingOrderManager = ticketTypeIdAndTicketingOrderManagerMap.getOrDefault(newTicketingType.getId(),null);
        if(oldTicketingOrderManager == null){
            log.info("TicketingOrderTransferServiceImpl | updateTicketTypeInTicketingOrderManagerForOrderExchange | not found");
            throw new NotFoundException(NotFoundException.NotFound.TICKET_TYPE_NOT_FOUND);
        }
        if (oldTicketingOrderManager.getNumberofticket() > 1) {
            oldTicketingOrderManager.setNumberofticket(oldTicketingOrderManager.getNumberofticket() - 1);
            if (newTicketingOrderManager == null) {
                newTicketingOrderManager = (TicketingOrderManager) oldTicketingOrderManager.clone();
                newTicketingOrderManager.setId(0);
                newTicketingOrderManager.setTicketType(newTicketingType);
                newTicketingOrderManager.setNumberofticket(1);
                newTicketingOrderManager.setNumberOfDiscountedTicket(0);
            } else {
                newTicketingOrderManager.setNumberofticket(newTicketingOrderManager.getNumberofticket() + 1);
            }

            ticketingOrderManagerService.save(newTicketingOrderManager);
            ticketingOrderManagerService.save(oldTicketingOrderManager);
            ticketTypeIdAndTicketingOrderManagerMap.put(newTicketingType.getId(),newTicketingOrderManager);
            ticketTypeIdAndTicketingOrderManagerMap.put(oldTicketingTypeId,oldTicketingOrderManager);
        } else {
            if (newTicketingOrderManager == null) {
                oldTicketingOrderManager.setTicketType(newTicketingType);
                oldTicketingOrderManager.setNumberOfDiscountedTicket(0);
                ticketingOrderManagerService.save(oldTicketingOrderManager);
                ticketTypeIdAndTicketingOrderManagerMap.remove(oldTicketingTypeId);
                ticketTypeIdAndTicketingOrderManagerMap.put(newTicketingType.getId(),oldTicketingOrderManager);
            } else {
                newTicketingOrderManager.setNumberofticket(newTicketingOrderManager.getNumberofticket() + 1);
                ticketingOrderManagerService.save(newTicketingOrderManager);
                ticketTypeIdAndTicketingOrderManagerMap.put(newTicketingType.getId(),newTicketingOrderManager);
                ticketTypeIdAndTicketingOrderManagerMap.remove(oldTicketingTypeId);
                ticketingOrderManagerService.deleteByTicketTypeAndTicketingOrder(oldTicketingOrderManager.getTicketType(), ticketingOrder);
            }
        }
        return ticketTypeIdAndTicketingOrderManagerMap;
    }

    //this method is for validate event tickets in order exchange
    private void validateEventTickets(List<EventTickets> eventTicketsList, Long orderId) {
        AtomicBoolean hasInvalidTicket = new AtomicBoolean(false);
        AtomicBoolean isAnyPaidTicket = new AtomicBoolean(false);

        eventTicketsList.forEach(eventTicket -> {
            if (TicketPaymentStatus.REFUNDED.equals(eventTicket.getTicketPaymentStatus()) ||
                    TicketPaymentStatus.PARTIALLY_REFUNDED.equals(eventTicket.getTicketPaymentStatus())) {
                hasInvalidTicket.set(true);
            }
            if (!eventTicket.getTicketingOrderId().equals(orderId)){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.ORDER_EXCHANGE_NOT_ALLOWED_FOR_DIFFERENT_ORDER_TICKETS);
            }

            if (!TicketType.FREE.equals(eventTicket.getTicketingTypeId().getTicketType())) {
                isAnyPaidTicket.set(true);
            }
        });

        if (hasInvalidTicket.get()) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_ALREADY_CANCELED_OR_REFUNDED);
        }
        if (isAnyPaidTicket.get()) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.INVALID_ORDER_EXCHANGE_MSG);
        }
    }

    // This method is for validate given exchange ticket types are configured or not
    private void isValidExchange(
            List<TicketDetailExchangeDetailDto> exchangeDetails,
            List<TicketExchangeMappingRuleDTO> mappingRules,
            Map<Long, EventTickets> eventTicketIdToTicketTypeIdMap
    ) {
        for (TicketDetailExchangeDetailDto detail : exchangeDetails) {
            Long eventTicketId = detail.getEventTicketId();
            Long newTicketTypeId = detail.getNewTicketTypeId();
            Long oldTicketTypeId = eventTicketIdToTicketTypeIdMap.get(eventTicketId).getTicketingTypeOnlyId();

            boolean matchFound = mappingRules.stream().anyMatch(rule ->
                    rule.getExchangeFrom().equals(oldTicketTypeId) &&
                            rule.getExchangeTo().equals(newTicketTypeId)
            );

            if (!matchFound) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.THIS_ORDER_EXCHANGE_NOT_ALLOWED_MSG);
            }
        }
    }

    private void updateEntityForInvoiceTicketAndCouponCode(Long orderId, EventTickets eventTickets, TicketingType newTicketingType, Event event, TicketExchangeDto ticketExchangeDto, TicketingOrder ticketingOrder, TicketTransferTypesDto ticketTransferTypesDto) {
        log.info("TicketingOrderTransferServiceImpl updateEntityForInvoiceTicketAndCouponCode start for orderId {} | eventId {} | couponCode {}", orderId, event.getEventId(), ticketExchangeDto.getCouponCode());
        EventTicketFeesDto eventTicketFeesDto = ticketingPurchaseService.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, Boolean.FALSE, 0d, ticketExchangeDto.getCouponCode());
        log.info("TicketingOrderTransferServiceImpl updateEntityForInvoiceTicketAndCouponCode eventTicketFeesDto dueAmount : {} | discountAmount : {}", eventTicketFeesDto.getEventTicketPaidAmount(), eventTicketFeesDto.getDiscountAmount());

        eventTickets.setTicketPrice(eventTickets.getTicketPrice() - eventTicketFeesDto.getDiscountAmount());
        eventTickets.setDiscountAmount(eventTicketFeesDto.getDiscountAmount());

        TicketingOrderManager oldTicketingOrderManager = ticketingOrderManagerService.getTicketTypeByOrderIdAndTicketTypeId(eventTickets.getTicketingTypeId(), ticketingOrder);
        oldTicketingOrderManager.setNumberOfDiscountedTicket(oldTicketingOrderManager.getNumberOfDiscountedTicket() +1);
        ticketingOrderManagerService.save(oldTicketingOrderManager);

        TicketingCoupon ticketingCoupon = ticketingCouponService.getByEventIdAndCouponCode(event.getEventId(), ticketExchangeDto.getCouponCode(), 0L);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        EventTicketTransferDetail eventTicketTransferDetail = eventTicketTransferDetailService.findFirstByEventIdAndEventTicketIdAndTicketingOrderIdOrderByIdDesc(event.getEventId(), eventTickets.getId(), orderId);
        eventTicketTransferDetail.setTicketingCouponId(ticketingCoupon.getId());
        eventTicketTransferDetailService.save(eventTicketTransferDetail);

        if (eventTicketFeesDto.getEventTicketPaidAmount() == 0) {
            ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
            ticketingOrder.setOrderType(TicketingOrder.OrderType.PAY_LATER);
            eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
            ticketTransferTypesDto.setPaymentDoneAtTicketTransfer(true);
        }
        eventTicketsService.save(eventTickets);
        ticketingOrderService.save(ticketingOrder);
        log.info("TicketingOrderTransferServiceImpl updateEntityForInvoiceTicketAndCouponCode end for orderId {} | eventId {} | couponCode {}", orderId, event.getEventId(), ticketExchangeDto.getCouponCode());
    }

}