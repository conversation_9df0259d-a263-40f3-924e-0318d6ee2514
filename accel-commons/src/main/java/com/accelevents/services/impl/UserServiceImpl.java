package com.accelevents.services.impl;

import com.accelevents.auction.dto.MessageToContactDto;
import com.accelevents.billing.chargebee.enums.ChargebeeEntitlements;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.configuration.PhoneNumberConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.dto.*;
import com.accelevents.dto.zapier.AppleDto;
import com.accelevents.dto.zapier.LinkedinDto;
import com.accelevents.dto.zapier.MicrosoftDto;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.*;
import com.accelevents.exceptions.ConflictException.UserExceptionConflictMsg;
import com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG;
import com.accelevents.exceptions.NotFoundException.NotFound;
import com.accelevents.helpers.*;
import com.accelevents.hubspot.service.HubspotContactService;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.LoginLink;
import com.accelevents.messages.MagicLinkType;
import com.accelevents.notification.services.InboundMessageService;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.impl.SendGridMailPrepareServiceImpl;
import com.accelevents.repositories.*;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROFCMWhiteLabelKeysService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.dto.FirstLastNameDto;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.spreedly.service.SpreedlyGatewayService;
import com.accelevents.utils.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.*;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.squareup.square.exceptions.ApiException;
import com.squareup.square.models.Card;
import com.stripe.exception.StripeException;
import com.stripe.model.PaymentMethod;
import com.stripe.net.RequestOptions;
import com.twilio.exception.TwilioException;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.validation.ConstraintViolationException;
import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.PrivateKey;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.enums.UserRole.*;
import static com.accelevents.exceptions.NotAcceptableException.AnalyticsExceptionMsg.TOKEN_NOT_FOUND;
import static com.accelevents.exceptions.NotFoundException.UserNotFound.USER_NOT_FOUND;
import static com.accelevents.exceptions.NotFoundException.UserNotFound.USER_ROLE_NOT_FOUND;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.Constants.AutoLoginCreationLocation.PLATFORM_LEVEL_MAGIC_LINK_API;
import static com.accelevents.utils.GeneralUtils.*;
import static com.accelevents.utils.Permissions.WL_AND_EVENT_COORDINATOR_PERMISSIONS;
import static com.accelevents.utils.SecurityUtils.encode;

@Service
public class UserServiceImpl implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private PhoneNumberConfiguration phoneNumberConfiguration;

    @Autowired
    private AutoLoginRepository autoLoginRepository;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserRolesRepoService userRolesRepoService;
    @Autowired
    private UserRolesRepository userRolesRepository;
    @Autowired
    private AuctionBidService auctionBidService;
    @Autowired
    private SubmittedRaffleTicketService submittedRaffleTicketService;
    @Autowired
    private PledgeService pledgeService;
    @Autowired
    private PhoneNumberService phoneNumberService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private EventService eventService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private TicketingOrderRepoService ticketingOrderService;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private UserCustomAttributeRepository userCustomAttributeRepository;
    @Autowired
    private DonationService donationService;
    @Autowired
    private DonationSettingsService donationSettingsService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private PurchasedRaffleTicketService purchasedRaffleTicketService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private ROStaffRoleService roStaffRoleService;
    @Autowired
    private StaffEmailNotificationsRepository staffEmailNotificationsRepository;
    @Autowired
    private StripeTransactionService stripeTransactionService;
    @Autowired
    private WinnerService winnerService;
    @Autowired
    private SendGridMailService emailService;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private TextMessageUtils textMessageUtils;
    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private BidderNumberService bidderNumberService;
    @Autowired
    private CacheStoreService cacheStoreService;

    @Autowired
    private AutoLoginService autoLoginService;

    @Autowired
    private TextMessageService textMessageService;
    @Autowired
    private StripePaymentService stripePaymentService;
    @Autowired
    private StripeService stripeService;
    @Autowired
    private ROStripeService roStripeService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private ServiceHelper serviceHelper;
    @Autowired
    private InboundMessageService inboundMessageService;
    @Autowired
    private FavoriteItemService favoriteItemService;
    @Autowired
    private SendGridMailService sendGridMailService;
    @Autowired
    private URLShortnerService urlShortnerService;
    @Autowired
    private IntegrationService integrationService;
    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private TwilioPhoneNumberValidateService twilioPhoneNumberValidateService;
    @Autowired
    private SpeakerService speakerService;
    @Autowired
    GetStreamService getStreamService;
    @Autowired
    UserService userService;
    @Autowired
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
    @Autowired
    private OrganizerService organizerService;
    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private ChargebeePlanService chargebeePlanService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private ChargebeeService chargebeeService;
    @Autowired
    private StaffPageServiceImpl staffPageService;
    @Autowired
    SendGridMailPrepareService sendGridMailPrepareService;
    @Autowired
    BeeFreeRepository beeFreeRepository;
    @Autowired
    private ApiKeyService apiKeyService;
    @Autowired
    private StripeCustomersRepository stripeCustomersRepository;
    @Autowired
    private HubspotContactService hubspotContactService;
    @Autowired
    private TicketHolderEditAttributesServiceImpl ticketHolderEditAttributesService;
    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private SquarePaymentService squarePaymentService;
    @Autowired
    private AllPaymentService allPaymentService;
    @Autowired
    private TicketingOrderDetailsService ticketingOrderDetailsService;

    private ImageConfiguration imageConfiguration;
    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    @Autowired
    private SSOUserServiceImpl ssoUserService;
    @Autowired
    private WhitelabelSSOConfigurationRepository whitelabelSSOConfigurationRepository;
    @Autowired
    private SpreedlyGatewayService spreedlyGatewayService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;
    @Autowired
    private TicketExchangeRuleService ticketExchangeRuleService;

    private static final Pattern pattern = Pattern.compile(USER_KEY_EXPRESSION);

    @Value("${uiBaseurl}")
    private String uiBaseurl;

    @Value("${app.firebase-config}")
    private String firebaseConfig;


    @Value("${linkedinClientId}")
    private String linkedinClientId;

    @Value("${linkedinClientSecret}")
    private String linkedinClientSecret;

    @Value("${appleClientId}")
    private String appleClientId;

    @Value("${applePrivateKey}")
    private String applePrivateKey;

    @Value("${appleKeyId}")
    private String appleKeyId;

    @Value("${appleTeamId}")
    private String appleTeamId;

    @Value("${okta.authentication.required.domain}")
    private String oktaAuthRequiredDomain;

    @Autowired
    private SendGridMailPrepareServiceImpl sendGridMailPrepareServiceImpl;

    @Autowired
    private StaffRepository staffRepository;
    private EmailImageHelper emailImageHelper;

    private FirebaseApp firebaseApp;

    @Autowired
    private ChargeBeePaymentHandler chargeBeePaymentHandler;

    @Autowired
    private ROFCMWhiteLabelKeysService rofcmWhiteLabelKeysService;

    public static Map<Long,FirebaseApp> firebaseAppHashMap= new ConcurrentHashMap<>();

    @Autowired
    public UserServiceImpl(ImageConfiguration imageConfiguration) {
        emailImageHelper = new EmailImageHelper(imageConfiguration.getImagePrefix(),
                imageConfiguration.getCloudinaryUrl(), imageConfiguration.getImagesAcceleventlogo());
        this.imageConfiguration=imageConfiguration;
    }

    @PostConstruct
    private void initialize() {
        Set<String> appNameAlreadyCreated=new HashSet<>();
        try {
            FirebaseOptions options = FirebaseOptions.builder()
                    .setCredentials(GoogleCredentials.fromStream(new ClassPathResource(firebaseConfig).getInputStream()))
                    .build();
            appNameAlreadyCreated.add("0");
            boolean alreadyInitialized = FirebaseApp.getApps().stream()
                    .anyMatch(app -> app.getName().equals("0"));
            if (alreadyInitialized) {
                this.firebaseApp = FirebaseApp.getInstance("0");
            } else {
                this.firebaseApp = FirebaseApp.initializeApp(options, "0");
            }
            firebaseAppHashMap.put(0L,firebaseApp);
        } catch (IOException e) {
            log.error("UserServiceImpl initialize Create FirebaseApp Error", e);
        }
        //add for all white label firebase app
        List<FCMWhiteLabelKeysDTO> listOfFCMWhiteLabelKeysDTOList = rofcmWhiteLabelKeysService.findAllFCMWhiteLabelKeys();
        Map<String, String> firebaseConfigByProjectName = getFirebaseConfigBasedOnProjectName(listOfFCMWhiteLabelKeysDTOList);
        Map<String, FirebaseApp> fireBaseOptionsMap = new HashMap<>();

        for (Map.Entry<String, String> entry : firebaseConfigByProjectName.entrySet()) {
            try {
                boolean isPresent = appNameAlreadyCreated.contains(entry.getKey());
                if (!isPresent) {
                    boolean alreadyInitialized = FirebaseApp.getApps().stream()
                            .anyMatch(app -> app.getName().equals(entry.getKey()));
                    if (alreadyInitialized) {
                        this.firebaseApp = FirebaseApp.getInstance(entry.getKey());
                        fireBaseOptionsMap.put(entry.getKey(), firebaseApp);
                    } else {
                        FirebaseOptions options = FirebaseOptions.builder()
                                .setCredentials(GoogleCredentials.fromStream(new ClassPathResource(entry.getValue()).getInputStream()))
                                .build();
                        this.firebaseApp = FirebaseApp.initializeApp(options, entry.getKey());
                        fireBaseOptionsMap.put(entry.getKey(), firebaseApp);
                        // Check if already initialized (avoid duplicate error)
                    }
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        listOfFCMWhiteLabelKeysDTOList.forEach(fcmWhiteLabel -> {
            FirebaseApp firebaseAppNew = fireBaseOptionsMap.get(fcmWhiteLabel.getProjectName());
            firebaseAppHashMap.put(fcmWhiteLabel.getWhiteLabelId(), firebaseAppNew);
        });
        log.info("Firebase Apps initialized: {}", firebaseAppHashMap.keySet());
    }

    private Map<String, String> getFirebaseConfigBasedOnProjectName(List<FCMWhiteLabelKeysDTO> listOfFCMWhiteLabelKeysDtos) {
        Map<String, String> firebaseConfigFileName = new HashMap<>();
        if (!CollectionUtils.isEmpty(listOfFCMWhiteLabelKeysDtos)) {
            for (FCMWhiteLabelKeysDTO fcmWhiteLabelKeys : listOfFCMWhiteLabelKeysDtos) {
                firebaseConfigFileName.put(fcmWhiteLabelKeys.getProjectName(), fcmWhiteLabelKeys.getFileName());
            }
        }
        return firebaseConfigFileName;
    }
    @Override
    public void deleteUserRoleIfNoEventRemains(User user) {
        List<Staff> staffData = staffService.findByUserAndRoleAndEventIsNotNullAndNotDeleted(user, StaffRole.admin);
        if (CollectionUtils.isEmpty(staffData)) {
            userRolesRepository.deleteUserRoleByUseridAndRole(user.getUserId(), "ROLE_ADMIN");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public AccessTokenModel getBidderUserDetailForLoginSignupWithoutEvent(UserSignupDto userSignupDto,
                                                                          boolean socialSignUp, boolean skipPassword) {

        User user = this.signUpBidderUserAndReturnUser(userSignupDto, null, true, socialSignUp, false, false,
                skipPassword);

        List<String> userRoles = roUserService.getUserRoles(user);
        if (null != userRoles && !userRoles.isEmpty()) {
            return new AccessTokenModel(userSignupDto.getEmail(), userSignupDto.getPassword(), userRoles, user, false,
                    null, roStaffService.getStaffRole(null, user));
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @Override
    public User findByUserId(Long userId) {
        return roUserService.findByIdOp(userId).orElseThrow(() -> new NotFoundException(USER_NOT_FOUND));
    }

    @Override
    public void sendPushNotificationToEventUsers(MessageToContactDto messageToContactDto, Event event, List<Long> eventUsers) throws FirebaseMessagingException {
        log.info("About to send push notification to {} users for event {} with subject {} and type {}", eventUsers.size(), event.getEventId(), messageToContactDto.getMessageJson(), messageToContactDto.getType());
        List<String> allUserPushNotificationToken = findPushNotificationTokenByUserIdIn(eventUsers);
        List<String> tokenList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allUserPushNotificationToken)) {
            allUserPushNotificationToken.forEach(userToken -> {
                List<String> token = convertCommaSeparatedToList(userToken);
                tokenList.addAll(token);
                log.info("UserServiceImpl sendPushNotificationToEventUsers eventId {} userToken {}", event.getEventId(), userToken);
            });
            log.info("UserServiceImpl sendPushNotificationToEventUsers eventUsers {}", eventUsers);
            String messageBody = messageToContactDto.getMessageBody();
            if (messageToContactDto.getTargetAudienceType().equals(TargetAudienceType.MOBILE_APP) || messageToContactDto.getTargetAudienceType().equals(TargetAudienceType.All)) {
                Document.OutputSettings outputSettings = new Document.OutputSettings();
                outputSettings.prettyPrint(false);
                messageBody = Jsoup.clean(messageBody, "", Whitelist.none(), outputSettings);
            }
            if (!CollectionUtils.isEmpty(tokenList)) {
                List<List<String>> partitionTokensList = ListUtils.partition(tokenList, 100);
                for (int i = 0; i < partitionTokensList.size(); i++) {
                    MulticastMessage message = MulticastMessage.builder()
                            .setNotification(Notification.builder().setTitle(messageToContactDto.getSubjectLine()).setBody(messageBody).build())
                            .putData(Constants.CONTENT, messageToContactDto.getSubjectLine())
                            .putData(Constants.BODY, messageBody)
                            .putData(Constants.EVENT_URL,event.getEventURL())
                            .putData(Constants.NOTIFICATION_ICON, messageToContactDto.getNotificationIcon() != null ? messageToContactDto.getNotificationIcon() : STRING_EMPTY)
                            .putData(Constants.NOTIFICATION_ID,String.valueOf(messageToContactDto.getId()))
                            .putData(Constants.NAVIGATION_INFO, MapUtils.isNotEmpty(messageToContactDto.getNavigationInfo()) ? JsonMapper.convertToString(messageToContactDto.getNavigationInfo()) : STRING_EMPTY)
                            .addAllTokens(partitionTokensList.get(i))
                            .build();
                    FirebaseApp fireBaseApp = null;
                    if (event.getWhiteLabelId() != null && firebaseAppHashMap.containsKey(event.getWhiteLabelId())) {
                        log.info("sendPushNotificationToEventUsers eventId {} whiteLabelId {}", event.getEventId(), event.getWhiteLabelId());
                        fireBaseApp = firebaseAppHashMap.get(event.getWhiteLabelId());
                    } else {
                        log.info("sendPushNotificationToEventUsers default app eventId {} whiteLabelId {}", event.getEventId(), event.getWhiteLabelId());
                        fireBaseApp = firebaseAppHashMap.get(0L);
                    }
                    BatchResponse response = FirebaseMessaging.getInstance(fireBaseApp).sendEachForMulticast(message);
                    if(response.getSuccessCount() < 1) {
                        response.getResponses().stream().findFirst().ifPresent(sendResponse -> log.info("Exception while sending push Notification message, errorMessage {}", sendResponse.getException().getMessage()));
                    }else {
                        log.info("UserServiceImpl sendPushNotificationToEventUsers Successfully sent message: {}, partition no.: {}", response.getSuccessCount(), i);
                    }
                }
                // for the contents of response.
            } else {
                log.info("UserServiceImpl sendPushNotificationToEventUsers User Token Not Found sent message eventId: {}", event.getEventId());
            }

        }
    }

    @Override
    public void sendPushNotificationForCheckin(Map<String, String> checkInStatus) {
        Long userId = Long.parseLong(String.valueOf(checkInStatus.get(Constants.USER_ID)));
        try{
            log.info("request received to send check-in notification to attendee {}",userId);

            Message message = Message
                    .builder()
                    .setTopic(String.valueOf(userId))
                    .putAllData(checkInStatus)
                    .build();
            String messageId = FirebaseMessaging.getInstance().send(message);

            log.info("successfully sent notification for checked-in for attendee {}, message Id {}",userId, messageId);
        }
        catch (Exception exception){
            log.warn("unable to send send checkin notification to attendee {} app: {}",userId,exception.getMessage());
        }
    }

    private List<String> findPushNotificationTokenByUserIdIn(List<Long> eventUsers) {
        return userRepository.findPushNotificationTokenByUserIdIn(eventUsers);
    }

    @Override
    public List<User> findPushNotificationAndUserIdByUserId(List<Long> userIds){
        return userRepository.findPushNotificationAndUserIdByUserId(userIds);
    }

    public List<Long> getAllEventUsers(Event event, String ticketTypeIds) {
            List<String> ticketingTypeIds = convertColonSeparatedToList(ticketTypeIds);
            List<Long> ticketingTypeIdsIn = ticketingTypeIds.stream().filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toList());
            List<Long> attendeeUserIds = eventTicketsRepoService.findAllUserIdByEvent(event, ticketingTypeIdsIn);
            return attendeeUserIds;
    }

    public static List<String> convertColonSeparatedToList(String commaSeparated) {
        if (StringUtils.isNotBlank(commaSeparated)) {
            return new ArrayList<>(Arrays.asList(commaSeparated.split(Constants.STRING_COLON)));
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public User savePushNotificationTokenForUser(User user, String token) {
        String tokenJson = user.getPushNotificationToken();
        List<String> tokenList = convertCommaSeparatedToList(tokenJson);
        if (!CollectionUtils.isEmpty(tokenList)) {
            tokenList.add(token);
        } else {
            tokenList = Collections.singletonList(token);
        }
        user.setPushNotificationToken(convertListToCommaSeparated(tokenList));
        userService.save(user);
        return user;
    }

    @Override
    @Transactional
    public void removePushNotificationTokenForUser(User user, String token) {
        String tokenJson = user.getPushNotificationToken();
        List<String> tokenList = convertCommaSeparatedToList(tokenJson);
        if (!CollectionUtils.isEmpty(tokenList)) {
            tokenList.remove(token);
        }
        user.setPushNotificationToken(convertListToCommaSeparated(tokenList));
        userService.save(user);
    }

    @Override
	public User save(User user) {
	    if(user != null && user.getUserId()!=null && user.getUserId() == 1) {
            try {
                Optional<User>userOptional = roUserService.findByIdOp(user.getUserId());
                if (userOptional.isPresent()) {
                    User userId = userOptional.get();
                    User userBeforeUpdate = userId;
                    log.info("UserBeforeUpdate:{}", userBeforeUpdate);
                    log.info(GeneralUtils.getStackTrace());
                    log.info("UserAfterUpdate:{}", user);
                }
            } catch (Exception e){
                log.error(e.getMessage());
            }
	    }
        validateAndThrowErrorForSpecificDomains(user != null ? user.getEmail() : STRING_EMPTY);
		log.info("User : {}", user);//NOSONAR
        User savedUser;
        try {
            savedUser = userRepository.save(user);
        } catch (DataIntegrityViolationException e) {
            NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.EMAIL_IS_ALREADY_PRESENT;
            String errorMessageTemplate = Constants.EMAIL_IS_ALREADY_PRESENT;
            String errorMessage = errorMessageTemplate.replace(EMAIL_ADDRESS_PARAMETER, user != null ? user.getEmail() : STRING_EMPTY);
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.EMAIL_ADDRESS_PARAMETER,user != null ? user.getEmail() : STRING_EMPTY);
            exception.setErrorMessage(errorMessage);
            exception.setDeveloperMessage(errorMessage);
            exception.setDefaultMessage(Constants.EMAIL_IS_ALREADY_PRESENT);
            exception.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new NotAcceptableException(exception);
        }
        this.addUserRole(savedUser, ROLE_USER.name());
		return savedUser;
	}
    public void validateAndThrowErrorForSpecificDomains(String email){
        String[] domains = {"@gufum.com", "@triots.com", "@unicsite.com"};
        if(StringUtils.isNotBlank(email) && email.contains("@") && Arrays.stream(domains).anyMatch(e-> e.equals(email.substring(email.indexOf("@"))))){
            throw new NotAcceptableException(NotAceptableExeceptionMSG.EMAIL_NOT_VALID);
        }
    }

    @Override
    @Transactional
    public User getOrCreateUser(UserBasicDto userBasicDto, Event event) {
        UserSignupDto userSignupDto = new UserSignupDto();
        Optional<User> optEmailUser = roUserService.getUserByEmail(userBasicDto.getEmail());
        if (optEmailUser.isPresent()){
            User user = optEmailUser.get();
            userSignupDto.setFirstName(user.getFirstName());
            userSignupDto.setLastName(user.getLastName());
        }else {
            userSignupDto.setFirstName(userBasicDto.getFirstName());
            userSignupDto.setLastName(userBasicDto.getLastName());
        }
        userSignupDto.setEmail(userBasicDto.getEmail());
        return signUpBidderUserAndReturnUser(userSignupDto, event, true, false, false, false,
                true);
    }

	@Override
	public Optional<User> getUserPasswordResetToken(String token) {
		return Optional.ofNullable(userRepository.findByPasswordResetToken(token));
	}

	@Override
	public void save(UserRole role) {
		userRolesRepository.save(role);

	}

	public Optional<UserBasicDto> findUserDetailsByBarcodeId(Long barcodeId){
        return userRepository.findUserDetailsByBarcodeId(barcodeId);
    }

	@Override
	public String getUserEmailById(Long userId) {
		if(userId == null){
			throw new NotFoundException(USER_NOT_FOUND);
		}
		Optional<User> userOptional = roUserService.findByIdOp(userId);
		if(userOptional.isPresent()) {
			return userOptional.get().getEmail();
		}
		return null;
	}

	private User saveUserFromText(TwilioMessage twilioMessage) {
		String firstName = null;
		String lastName = null;
		if (twilioMessage.getBody().contains(Constants.STRING_BLANK)) {
			String[] names = twilioMessage.getBody().split(Constants.STRING_BLANK);
			firstName = names[0];
			lastName = names[1];
		}
		Optional<User> optUser = roUserService.getUserByAEPhoneNumber(twilioMessage.getFrom());
		User user;

		if (optUser.isPresent()) {
			user = optUser.get();
			user.setFirstName(firstName);
			user.setLastName(lastName);
			user.setMostRecentEventId(twilioMessage.getEvent().getEventId());
			this.save(user);
		} else {
			user = new User();
			user.setFirstName(firstName);
			user.setLastName(lastName);
			user.setPhoneNumberAndCountryCode(twilioMessage.getFrom());
			user.setMarketingOptIn(true);
			user.setMostRecentEventId(twilioMessage.getEvent().getEventId());
			this.save(user);
			this.addUserRole(user, ROLE_USER.name());
		}

		return user;
	}

	@Override
	public String processNameText(TwilioMessage twilioMessage) {

		User user = this.saveUserFromText(twilioMessage);

		Event event = twilioMessage.getEvent();
		itemService.updateUserUnconfirmedBidsAndItems(user, event);
		itemService.updateUserUnconfirmedPledgeAndItems(user, event);

		boolean bidAvailable = !auctionBidService.findByUserAndAuctionId(user, event.getAuctionId()).isEmpty();
		boolean pledgeAvailable = !pledgeService.findAllByUserAndCauseAuctionId(user, event.getCauseAuctionId())
				.isEmpty();

		return textMessageUtils.getSuccessfulBidAndPledgeFromUserMessage(user, bidAvailable, pledgeAvailable,
				twilioMessage.getEvent());
	}

	@Override
	public String registrationLinkText(TwilioMessage twilioMessage) {

		User user = this.saveUserFromText(twilioMessage);
		Event event = twilioMessage.getEvent();
		String link = serviceHelper.getEventBaseUrl(event) + getEventPath() + event.getEventURL() + "/register/token/" + SecurityUtils.encodeUserid(user.getUserId());
		return "Please register for " + event.getName() + " here: " + urlShortnerService.getShortURL(link);
	}

	private boolean verifyUserRoleExists(User user, List<String> userRoles, com.accelevents.enums.UserRole roleToVerify){
		if (user != null) {
			boolean result = false;
			if (userRoles.contains(roleToVerify.name())) {
				result = true;
			}
			return result;
		} else {
			throw new AuthorizationException(Constants.NOT_AUTHORIZE);
		}
	}

	@Override
	public boolean isSalesAdmin(User user, List<String> userRoles){
		return this.verifyUserRoleExists(user, userRoles, ROLE_SALES_ADMIN);
	}

	@Override
    public boolean isCustomerSupportLead(User user, List<String> userRoles){
        return this.verifyUserRoleExists(user, userRoles, ROLE_CUSTOMER_SUPPORT_LEAD);
    }

    @Override
    public boolean isBillingTypeAdmin(User user, List<String> userRoles){
        return this.verifyUserRoleExists(user, userRoles, ROLE_BILLING_TYPE_ADMIN);
    }

    public void checkIsBillingTypeAdminOrThrowError(User user) {
        if (user == null) {
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
        List<String> userRoles = this.roUserService.getUserRoles(user);
        if (!isBillingTypeAdmin(user, userRoles)) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_BILLING_TYPE_ADMIN);
        }
    }

    public boolean isBillingTypeAdmin(User user) {
        if (user == null) {
            return false;
        }
        List<String> userRoles = this.roUserService.getUserRoles(user);
        if (!isBillingTypeAdmin(user, userRoles)) {
            return false;
        }
        return true;
    }

	public User handleUserMerge(User loggedInUser, String countryCode, String phoneNumber, String email) {
        logDetails(loggedInUser, countryCode, phoneNumber, email);

        Optional<User> optEmailUser = Optional.empty();
		if (StringUtils.isNotBlank(email)) {
			optEmailUser = roUserService.getUserByEmail(email);
			if (optEmailUser.isPresent()) {
				User emailUsr = optEmailUser.get();
                throwErrorIfLoggedInUserEmailNotMatchWithEmailProvided(loggedInUser, email);
                throwErrorIfLoggedInUserPhoneNotMatchesWithEmailUser(loggedInUser, emailUsr);
            }
		}

		AccelEventsPhoneNumber aePhoneNumber = getAccelEventsPhoneNumberWithDetailsProvided(loggedInUser, countryCode, phoneNumber);

		Optional<User> optPhoneUser;
		if (aePhoneNumber != null) {
			log.info("inside aePhoneNumber != null");
			optPhoneUser = roUserService.getUserByAEPhoneNumber(aePhoneNumber);
			if (optEmailUser.isPresent()) {
                throwEmailAlreadyAssociatedWithPhone(optEmailUser, aePhoneNumber);
			}
			if (optPhoneUser.isPresent() && !optPhoneUser.get().getUserId().equals(loggedInUser.getUserId())) {
				log.info("inside optPhoneUser.isPresent() && optPhoneUser.get().getUserId() != oldUser.getUserId()");
				User phoneUser = optPhoneUser.get();
				if (phoneUser.getEmail() != null) {
                    return throwErrorAlreadyEmailExists(phoneUser,countryCode,phoneNumber,loggedInUser.getMostRecentEventId());
				} else {
					log.info("inside newUser.getEmail() == null");
					log.info("LoggedInUser==> {}<== oldPhoneUser==>{}" , loggedInUser , phoneUser);
                    loggedInUser.setPhoneNumber(phoneUser.getPhoneNumber());
					loggedInUser.setCountryCode( phoneUser.getCountryCode());
					updateUserWithNewUser(phoneUser, loggedInUser);
				}
			} else {
				log.info("inside not equal optPhoneUser.isPresent() && optPhoneUser.get().getUserId() != oldUser.getUserId()");
				// user does not exist with this phone number
				loggedInUser.setPhoneNumberAndCountryCode(aePhoneNumber);
				this.save(loggedInUser);
			}
		}

		if (StringUtils.isBlank(loggedInUser.getEmail())) {
			log.info("inside  StringUtils.isBlank(oldUser.getEmail())");
			if (optEmailUser.isPresent()) {
				log.info("inside optEmailUser != null && optEmailUser.isPresent()");
				User emailUser = optEmailUser.get();
				log.info("LoggedInUser==> " + loggedInUser + "<== oldEmailUser==>" + emailUser);

				// Swap the users.
				User tempLoggedInUser = loggedInUser;
				loggedInUser = emailUser;
				User oldUser = tempLoggedInUser;

				loggedInUser.setEmail(email);
				loggedInUser.setPhoneNumber(tempLoggedInUser.getPhoneNumber());
				loggedInUser.setCountryCode(tempLoggedInUser.getCountryCode());

				updateUserWithNewUser(oldUser, loggedInUser);
			} else if (StringUtils.isNotBlank(email)) {
				log.info("inside StringUtils.isNotBlank(email)");
				loggedInUser.setEmail(email);
				this.save(loggedInUser);
			}
		}
		return loggedInUser;
	}

    private User throwErrorAlreadyEmailExists(User oldUser, String countryCode, String phoneNumber, long mostRecentEventId) {
        // TODO : Vikas 10 Uncomment After PROD Deploy
//		Event event = eventService.getEventById(mostRecentEventId);
//		sendCode(new PhoneNumberDto(countryCode,phoneNumber),event.getEventURL());
//
//        UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL;
//        String emailToShow = oldUser.getEmail().substring(0,4);
//        String domainEmail = oldUser.getEmail().substring(oldUser.getEmail().indexOf("@"));
//
//        exceptionMessage.setErrorMessage(
//                "This phone number is already associated with " + emailToShow+"*********"+domainEmail + " email address. The verification code have been sent to your mobile.");
		// throw new ConflictException(exceptionMessage);
		log.info("Already have email");
		UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_EMAIL_PARAMETER;
        String message = Constants.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_EMAIL_PARAMETER.replace(Constants.EMAIL_ADDRESS_PARAMETER, oldUser.getEmail());
        Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
        defaultMessageParamMap.put(Constants.EMAIL_ADDRESS_PARAMETER,oldUser.getEmail());
        exceptionMessage.setErrorMessage(message);
        exceptionMessage.setDeveloperMessage(message);
        exceptionMessage.setDefaultMessage(Constants.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_EMAIL_PARAMETER);
        exceptionMessage.setDefaultMessageParamMap(defaultMessageParamMap);
		throw new ConflictException(exceptionMessage);
    }

    private void throwEmailAlreadyAssociatedWithPhone(Optional<User> optEmailUser, AccelEventsPhoneNumber aePhoneNumber) {
	    Optional<User> userOptional = optEmailUser;
	    if (userOptional.isPresent()) {
            User user = userOptional.get();
            User emailUsr = user;
            if (emailUsr.isAePhoneNumberExist() && !emailUsr.getAePhoneNumber().equals(aePhoneNumber)) {
                log.info("Already have phoner from phone number");
                UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER;
                String constantMessage = Constants.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER.replace(Constants.PHONE_NUMBER_ALREDY_WITH_EMAIL_PARAMETER, String.valueOf(emailUsr.getPhoneNumber()));
                Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
                defaultMessageParamMap.put(Constants.PHONE_NUMBER_ALREDY_WITH_EMAIL_PARAMETER,String.valueOf(emailUsr.getPhoneNumber()));
                exceptionMessage.setErrorMessage(constantMessage);
                exceptionMessage.setDeveloperMessage(constantMessage);
                exceptionMessage.setDefaultMessage(Constants.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER);
                exceptionMessage.setDefaultMessageParamMap(defaultMessageParamMap);
                throw new ConflictException(exceptionMessage);
            }
        }
    }

    private AccelEventsPhoneNumber getAccelEventsPhoneNumberWithDetailsProvided(User loggedInUser, String countryCode, String phoneNumber) {
        if (StringUtils.isNotBlank(countryCode) && StringUtils.isNotBlank(phoneNumber)
                && (!Long.valueOf(phoneNumber).equals(loggedInUser.getPhoneNumber())
                        || !countryCode.equals(loggedInUser.getCountry()))) {
            log.info(
                    "inside StringUtils.isNotBlank(countryCode) && StringUtils.isNotBlank(phoneNumber) && (!Long.valueOf(phoneNumber).equals(loggedInUser.getPhoneNumber()) || !countryCode.equals(loggedInUser.getCountry()))");
            return new AccelEventsPhoneNumber(CountryCode.valueOf(StringUtils.upperCase(countryCode)),
                    Long.valueOf(phoneNumber));
        }
        return null;
    }

    private void throwErrorIfLoggedInUserPhoneNotMatchesWithEmailUser(User loggedInUser, User emailUsr) {
        if (emailUsr.isAePhoneNumberExist()
                && !emailUsr.getAePhoneNumber().equals(loggedInUser.getAePhoneNumber())) {
            log.info("Already have phone from email");
            UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER;
            String constantMessage = Constants.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER.replace(Constants.PHONE_NUMBER_ALREDY_WITH_EMAIL_PARAMETER, String.valueOf(emailUsr.getPhoneNumber()));
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.PHONE_NUMBER_ALREDY_WITH_EMAIL_PARAMETER,String.valueOf(emailUsr.getPhoneNumber()));
            exceptionMessage.setErrorMessage(constantMessage);
            exceptionMessage.setDeveloperMessage(constantMessage);
            exceptionMessage.setDefaultMessage(Constants.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER);
            exceptionMessage.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new ConflictException(exceptionMessage);
        }
    }

    private void throwErrorIfLoggedInUserEmailNotMatchWithEmailProvided(User loggedInUser, String email) {
        if (StringUtils.isNotBlank(loggedInUser.getEmail()) && !loggedInUser.getEmail().equalsIgnoreCase(email)) {
            log.info("Different email address");
            UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.EMAIL_NOT_MATCH_WITH_USER_ID;
            String constantMessage = Constants.EMAIL_NOT_MATCH_WITH_USER_ID.replace(Constants.USER_ID_NOT_MATCH_PARAM, String.valueOf(loggedInUser.getUserId()));
            Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.USER_ID_NOT_MATCH_PARAM,String.valueOf(loggedInUser.getUserId()));
            exceptionMessage.setErrorMessage(constantMessage);
            exceptionMessage.setDeveloperMessage(constantMessage);
            exceptionMessage.setDefaultMessage(Constants.EMAIL_NOT_MATCH_WITH_USER_ID);
            exceptionMessage.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new ConflictException(exceptionMessage);
        }
    }

    private void logDetails(User newUser, String countryCode, String phoneNumber, String email) {
        log.info("oldUser==> {}" , newUser);
        log.info("email==>{}" , email);
        log.info("countryCode==>{}" , countryCode);
        log.info("phoneNumber==>{}" , phoneNumber);
    }

    /**
	 * Merge newuser to old user
	 *
	 * @param oldUser
	 * @param loggedInUser
	 */
	void updateUserWithNewUser(User oldUser, User loggedInUser) {
		if (oldUser.getUserId().equals(loggedInUser.getUserId())) {
			// do nothing
			log.info("Don't need to merge users because they are the same!");
		} else {
			log.info("Trying to merge host and phone number users");
			log.info("host: {}" , loggedInUser);
			log.info("input phone number: {}" , oldUser.getAePhoneNumber());
			log.info("Existing user: {}" , oldUser);

			log.info("Going for merge. Existing user does not have email associated");
			if (StringUtils.isBlank(loggedInUser.getFirstName())) {
				loggedInUser.setFirstName(oldUser.getFirstName());
			}
			if (StringUtils.isBlank(loggedInUser.getLastName())) {
				loggedInUser.setLastName(oldUser.getLastName());
			}
			if (StringUtils.isBlank(loggedInUser.getAddress1())) {
				loggedInUser.setAddress1(oldUser.getAddress1());
			}
			if (StringUtils.isBlank(loggedInUser.getAddress2())) {
				loggedInUser.setAddress2(oldUser.getAddress2());
			}
			if (StringUtils.isBlank(loggedInUser.getZipcode())) {
				loggedInUser.setZipcode(oldUser.getZipcode());
			}
			if (StringUtils.isBlank(loggedInUser.getState())) {
				loggedInUser.setState(oldUser.getState());
			}
			if (StringUtils.isBlank(loggedInUser.getCityOrProvidence())) {
				loggedInUser.setCityOrProvidence(oldUser.getCityOrProvidence());
			}
			if (StringUtils.isBlank(loggedInUser.getCountry())) {
				loggedInUser.setCountry(oldUser.getCountry());
			}

			auctionBidService.updateUserByNewUser(oldUser, loggedInUser);
			donationService.updateUserByNewUser(oldUser, loggedInUser);
			eventService.updateUserByNewUser(oldUser, loggedInUser);
			paymentService.updateUserByNewUser(oldUser, loggedInUser);
			pledgeService.updateUserByNewUser(oldUser, loggedInUser);
			purchasedRaffleTicketService.updateUserByNewUser(oldUser, loggedInUser);
			staffService.updateUserByNewUser(oldUser, loggedInUser);
			stripeTransactionService.updateUserByNewUser(oldUser, loggedInUser);
			submittedRaffleTicketService.updateUserByNewUser(oldUser, loggedInUser);
			winnerService.updateUserByNewUser(oldUser, loggedInUser);
			userRolesRepository.updateByNewUser(oldUser.getUserId(), loggedInUser.getUserId());
			userCustomAttributeRepository.updateByNewUser(oldUser.getUserId(), loggedInUser.getUserId());
			itemService.updateByNewUser(oldUser, loggedInUser);
			ticketingOrderService.updateByNewUser(oldUser, loggedInUser);
			eventCommonRepoService.updatePurchaserByNewUser(oldUser, loggedInUser);
			eventCommonRepoService.updateStaffByNewUser(oldUser, loggedInUser);
			bidderNumberService.updateBidderNumberByNewUser(oldUser, loggedInUser);
			inboundMessageService.updateOldUserWithNewUserMerge(oldUser.getUserId(),loggedInUser.getUserId());
			favoriteItemService.updateOldUserWithNewUserMerge(oldUser.getUserId(),loggedInUser.getUserId());

			this.deleteUser(oldUser);

			//int i = 1/0; //NOSONAR

			// Do not delete this line to find the email, this is work around to
			// flush the deleteUser method changes. :
			// https://jira.spring.io/browse/DATAJPA-727
			roUserService.getUserById(oldUser.getUserId());

          //  int i = 1/0; //NOSONAR

			this.save(loggedInUser);

		}
	}

	@Override
	public void saveCustomAttribute(User user, Event event, String name, String value) {
		UserCustomAttribute c = new UserCustomAttribute(user.getUserId(), event.getEventId(), name, value);
		userCustomAttributeRepository.save(c);
	}

	@Override
	public String processNameTextForSweepstakes(TwilioMessage twilioMessage, Raffle raff, User user) {
		try {
			User textUser = this.saveUserFromText(twilioMessage);

			return textMessageUtils.getProcessNameTextForSweepstakesMessage(user, twilioMessage.getEvent(), textUser);
		} catch (ArrayIndexOutOfBoundsException e) {
			log.error("Could not parse name", e);
			return textMessageUtils.getSweepStaksBadNameMessage();
		}
	}

	@Override
	public boolean nameIsPresent(String message) {
		String[] names = message.trim().split(Constants.STRING_BLANK);
		return names.length == 2 && !message.matches(".*\\d.*");
	}

	@Override
	public String[] parseStringToNames(String message) {
		if (!nameIsPresent(message)) {
			return null;
		} else {
			String[] names = message.trim().split(Constants.STRING_BLANK);
			if (names.length != 2) {
				return null;
			} else {
				return names;
			}
		}
	}

	@Override
	public void deleteUser(User user) {
		log.info("Deleting user == {}" , user);
		userRepository.delete(user);
	}
    @Override
	public List<UserCustomAttribute> findUserCustomAttributesByEventId(long eventId) {
		return userCustomAttributeRepository.findByEventId(eventId);
	}

	@Override
	public boolean hasCustomAttribute(Event event, User user, String attributeName) {
		Optional<UserCustomAttribute> a = userCustomAttributeRepository
				.findByEventIdAndUserIdAndName(event.getEventId(), user.getUserId(), attributeName);
		return a.isPresent();
	}

	@Override
	public void addUserRole(User user, String userRole) {
		List<String> userRoles = roUserService.getUserRoles(user);
		if (!userRoles.contains(userRole)) {
			UserRole role = new UserRole();
			role.setRole(userRole);
			role.setUserid(user.getUserId());
			this.save(role);
		}
	}

	@Override
	public void deleteUserRole(User user, String role) {
		userRolesRepository.deleteUserRoleByUseridAndRole(user.getUserId(), role);
	}

	@Override
	@Transactional
	public void processResetUserPasswordRequest(UserEmailOrPhoneNumberDto userEmailOrPhoneNumberDto,//NOSONAR
												String whiteLabelURL, boolean requiredCode) {
		Optional<User> userOpt = roUserService.getUserByPhoneOrEmail(userEmailOrPhoneNumberDto.getUserEmailOrPhoneNumber());
		if (userOpt.isPresent()) {
			User user = userOpt.get();
			String token = user.getPasswordResetToken();
			String otpCode = "";
			if (requiredCode) {
				OTPGenerator otpGenerator = new OTPGenerator(6);
				otpCode = otpGenerator.nextString();
				cacheStoreService.set("PASSWORD_RESET_CODE_" + user.getUserId(), otpCode, 10, TimeUnit.MINUTES);
			}
			if (StringUtils.isBlank(token)) {
				token = UUID.randomUUID().toString() + System.nanoTime();
				user.setPasswordResetToken(token);
				this.save(user);
			}

			EmailMessage emailMessage = new EmailMessage(TemplateId.RESET_PASSWORD);
			emailMessage.setBody(Constants.STRING_BLANK);
			emailMessage.setSubject("Reset Password Request");
            BeeFree beeFree = beeFreeRepository.findByEmailTypeAndEventIdZero(EmailType.RESET_PASSWORD);
            if(null != beeFree && beeFree.isCustomTemplateEnabled()){
                emailMessage.setTemplateName(RESET_PASSWORD);
                emailMessage.setTemplate(beeFree.getHtmlValue());
                emailMessage.setBeefreeTemplate(true);
            } else {
                emailMessage.setTemplateId(TemplateId.RESET_PASSWORD);
                emailMessage.setTemplateName(TemplateId.RESET_PASSWORD.getValue());
            }
			Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();

			String uiBaseUrl = this.uiBaseurl;
			String path = "/u/new-password"; //NOSONAR
			String firmName = Constants.ACCELEVENTS;
            String logo =imageConfiguration.getImagePreFixWithCloudinaryUrl().concat(DEFAULT_AE_IMAGES).concat("/Smooth_Accelevents_Default_Event_Logo_Black.png");

            WhiteLabel whiteLabel = null;
			if (StringUtils.isNotBlank(whiteLabelURL)) {
				whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelURL);

				if (whiteLabel != null) {
					if (StringUtils.isNotBlank(whiteLabel.getHostBaseUrl())) {
						uiBaseUrl = whiteLabel.getHostBaseUrl();
					}

                    try {
                        String wlURL=URLEncoder.encode(whiteLabelURL,UTF8).replace("+", "%20");
                        path = "/u/wl-new-password/" + wlURL;
                    } catch (UnsupportedEncodingException e) {
                    log.info("issue while encoding url {} on reset ",whiteLabelURL);
                    }
                    if (null != whiteLabel.getHeaderLogoImage()){
                        logo=imageConfiguration.getImagePrefix().concat(whiteLabel.getHeaderLogoImage());
                    }
					if(StringUtils.isNotBlank(whiteLabel.getFirmName())){
						firmName = whiteLabel.getFirmName();
					}
                    emailMessage.setReplyToEmail(whiteLabel.getTransactionalEmail());
				}
				emailMessage.setWhiteLabel(whiteLabel);
			}
			substitutionMap.put(FIRM_NAME, firmName);
            substitutionMap.put(USER_NAME,StringUtils.isNotEmpty(user.getFirstName()) ? user.getFirstName() : STRING_EMPTY);
            substitutionMap.put(CLOUDINARY_IMAGE_PREFIX,imageConfiguration.getImagePreFixWithCloudinaryUrl());
            substitutionMap.put(IMAGE_FOOTER, logo);
            sendGridMailPrepareServiceImpl.addFooterForCustomMailTemplate(substitutionMap, whiteLabel);
            substitutionMap.put(CREATE_EVENT, uiBaseurl.concat(SLASH_EVENTS));
			setImageUrl(substitutionMap,whiteLabel);

			String redirectLink;
			if (StringUtils.isNotBlank(userEmailOrPhoneNumberDto.getRedirectLink())) {
				redirectLink = userEmailOrPhoneNumberDto.getRedirectLink();
			} else {
				Event userMostRecentEvent = roEventService.getEventByIdIfPresentOrNull(user.getMostRecentEventId());
				if(null != userMostRecentEvent ){
                    redirectLink = uiBaseUrl + getEventPath() + userMostRecentEvent.getEventURL();
                }
				else {
                    redirectLink = uiBaseUrl +"/u/myprofile";
                }
			}
			log.info("######token" + uiBaseUrl + path + Constants.TOKEN + token + "&redirect=" + redirectLink); //NOSONAR
			Map<String, Object> data = emailMessage.getSubstitutionData();
			data.put(TOKEN_LOWER, uiBaseUrl + path + Constants.TOKEN + token + "&redirect=" + redirectLink); //NOSONAR
            data.put("passwordResetCode", otpCode);
            data.put("requiredCode", requiredCode);
			//data.put(IMAGE_URL, logo);

            Set<String> receivers = new HashSet<>();
			receivers.add(user.getEmail());
			emailService.sendTemplateMail(emailMessage, receivers);
		}
	}

    @Override
    public void resendUser2FACodeRequest(User user, WhiteLabel whiteLabel, Event event) {

        log.info("2FA code requested by the User {}", user.getEmail());
        if(whiteLabel != null && whiteLabel.getEntitlements() != null){
            validate2FactorEntitlement(whiteLabel, whiteLabel.getId());
        }

        Random rnd = new Random();
        int number = rnd.nextInt(999999);

        // this will convert any number sequence into 6 character.
        String twoFactorCode = String.format("%06d", number);

        cacheStoreService.set(TWO_FACTOR_CODE_USERID_PREFIX + user.getUserId(), twoFactorCode, 5, TimeUnit.MINUTES);

        EmailMessage emailMessage = new EmailMessage(TemplateId.TWO_FACTOR_CODE);
        emailMessage.setBody(Constants.STRING_BLANK);
        emailMessage.setSubject("2FA Code");
        emailMessage.setTemplateName(TemplateId.TWO_FACTOR_CODE.getValue());

        Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();

        String firmName = Constants.ACCELEVENTS;
        String logo = imageConfiguration.getImagePrefix().concat(EMAIL_TEMPLATE_IMAGES).concat(imageConfiguration.getImagesAcceleventlogo());

        if (whiteLabel != null) {
            if (null != whiteLabel.getHeaderLogoImage()) {
                logo = imageConfiguration.getImagePrefix().concat(whiteLabel.getHeaderLogoImage());
            }
            if (StringUtils.isNotBlank(whiteLabel.getFirmName())) {
                firmName = whiteLabel.getFirmName();
            }
        }
        emailMessage.setWhiteLabel(whiteLabel);
        String path = "/u/new-password";
        String token = user.getPasswordResetToken();
        if (StringUtils.isBlank(token)) {
            token = UUID.randomUUID().toString() + System.nanoTime();
            user.setPasswordResetToken(token);
            this.save(user);
        }
        substitutionMap.put(CLOUDINARY_IMAGE_PREFIX,imageConfiguration.getImagePreFixWithCloudinaryUrl());
        substitutionMap.put(CREATE_EVENT,uiBaseurl.concat(SLASH_EVENTS));
        substitutionMap.put(USER_NAME, user.getFirstName());
        substitutionMap.put(IMAGE_FOOTER, sendGridMailPrepareServiceImpl.getWhiteLabelHeaderLogoLocation(whiteLabel));
        sendGridMailPrepareServiceImpl.addTheValueOfFooterForNewTemplates(substitutionMap, whiteLabel);
        substitutionMap.put(RESET_PASSWORD, uiBaseurl + path + Constants.TOKEN + user.getPasswordResetToken() + "&redirect=" + uiBaseurl + "/u/login");

        substitutionMap.put(FIRM_NAME, firmName);
        substitutionMap.put("twoFactorCode", twoFactorCode);
        substitutionMap.put(IMAGE_URL, logo);

        Set<String> receivers = new HashSet<>();
        receivers.add(user.getEmail());
        log.info("2FA code request email to User {} with subject {} for WL url {}", user.getEmail(), emailMessage.getSubject(), firmName);
        emailService.sendTemplateMail(emailMessage, receivers);
    }

    private void validate2FactorEntitlement(ChargeBeeDetails chargeBeeDetails, long whitelableId) {
        if(!chargeBeePaymentHandler.isEntitlementsAvailable(chargeBeeDetails, ChargebeeEntitlements.TWO_FACTOR_AUTHENTICATION)){
            log.info("2-factor entitlement not available for this whitelable id {}", whitelableId);
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.TWO_FACTOR_ENTITLEMENT_IS_NOT_AVAILABLE);
        }
    }

    @Override
    public void sendResetPasswordTestEmail(User superAdminUser){
        String token = TOKEN_LOWER;
        String otpCode = "";
        EmailMessage emailMessage = new EmailMessage(TemplateId.RESET_PASSWORD);
        emailMessage.setBody(Constants.STRING_BLANK);
        emailMessage.setSubject("Recover Password");
        BeeFree beeFree = beeFreeRepository.findByEmailTypeAndEventIdZero(EmailType.RESET_PASSWORD);
        if(null != beeFree){
            emailMessage.setTemplateName(RESET_PASSWORD);
            emailMessage.setTemplate(beeFree.getHtmlValue());
            emailMessage.setBeefreeTemplate(true);
        } else {
            emailMessage.setTemplateName(TemplateId.RESET_PASSWORD.getValue());
        }

        Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();

        String uiBaseUrl = this.uiBaseurl;
        String path = "/u/new-password"; //NOSONAR
        String firmName = Constants.ACCELEVENTS;
        substitutionMap.put("firmName", firmName);
        substitutionMap.put(IMAGE_URL, emailImageHelper.getEmailLogoImageForBeeFree(null));

        String redirectLink;
        Event event = new Event();
        event.setEventId(0);
        event.setEventURL(EVENT_URL);
        event.setName(EVENT_NAME);
        redirectLink = uiBaseUrl + getEventPath() + event.getEventURL();
        Map<String, Object> data = emailMessage.getSubstitutionData();
        data.put("token", uiBaseUrl + path + Constants.TOKEN + token + "&redirect=" + redirectLink); //NOSONAR
        data.put("passwordResetCode", otpCode);
        data.put("requiredCode", false);
        Set<String> receivers = new HashSet<>();
        receivers.add(superAdminUser.getEmail());
        emailService.sendTemplateMail(emailMessage, receivers);
    }

	@Override
	@Transactional
	public User resetNewUserPasswordAndReturnRedirectUrl(String encodedPassword, Optional<User> optionalUser) {

		User user = null;
		if (optionalUser.isPresent()) {
			user = optionalUser.get();
		}
		if (user != null) {
			user.setPassword(encodedPassword);
			user.clearPasswordResetToken();

			return this.save(user);
		}
		return null;
	}

	private String getLoginUrl(WhiteLabel whiteLabel) {
		return whiteLabel!=null ? "/u/wl-login/" +whiteLabel.getWhiteLabelUrl() : "/u/login";
	}

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public User addAdminRoleForUserAndReturnUser(User user) {
        this.addUserRole(user, ROLE_ADMIN.name());
        return user;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, noRollbackFor = {
			ConflictException.class }, isolation = Isolation.READ_COMMITTED)
	public User signUpBidderUserAndReturnUser(UserSignupDto newUser, Event event, boolean isLogin, boolean socialSignUp,boolean bidderSignUp, boolean isStaffSignup, boolean skipPassword) {//NOSONAR
        log.info("signUpBidderUserAndReturnUser | newUser.email:{} | Event.id:{} | isLogin:{} | socialSignUp:{} | bidderSignUp:{} | isStaffSignUp:{} | skipPassword: {} ",
                newUser.getEmail(), event != null ? event.getEventId() : null, isLogin, socialSignUp, bidderSignUp, isStaffSignup, skipPassword);

        checkCountryCodeIsValid(Optional.ofNullable(newUser.getCountryCode()));

		Optional<User> optPhoneUser = this.getUserBySignupDtoPhoneNumber(newUser);
		Optional<User> optEmailUser = Optional.empty();

		Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(null, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());

        // need to check null & empty because many record present with null email in user table.
		if(newUser.getEmail() != null && newUser.getEmail().length() >0){
		    optEmailUser= roUserService.getUserByEmail(newUser.getEmail());
		    if (optEmailUser.isPresent()){
                User user = optEmailUser.get();
                if (null != user.getCountryCode()) {
                    newUser.setCountryCode(user.getCountryCode().name());
                }
            }
        }

		boolean isPhoneUser = optPhoneUser.isPresent();
		boolean isEmailUser = optEmailUser.isPresent();

		log.info("Phone in db: {}" , isPhoneUser);
		log.info("Email in db: {}" , isEmailUser);

		User user;
		CountryCode userCountryCode = StringUtils.isNotBlank(newUser.getCountryCode())
				? CountryCode.valueOf(StringUtils.upperCase(newUser.getCountryCode())) : CountryCode.US;

		if (isPhoneUser && isEmailUser) {
			if (isLogin || bidderSignUp || isStaffSignup) {
				if (optPhoneUser.get().getUserId().equals(optEmailUser.get().getUserId())) {
					user = optEmailUser.get();
					setFirstAndLastName(newUser, user);

                    if (StringUtils.isNotBlank(newUser.getGoogleUserId())) {
                        user.setGoogleUserId(newUser.getGoogleUserId());
                    }
                    if (StringUtils.isNotBlank(newUser.getLinkedinUserId())) {
                        user.setLinkedinUserId(newUser.getLinkedinUserId());
                    }
                    if (StringUtils.isNotBlank(newUser.getAppleUserId())) {
                        user.setAppleUserId(newUser.getAppleUserId());
                    }
					if (!(socialSignUp || bidderSignUp || isStaffSignup)) {
						if (StringUtils.isBlank(user.getPassword())) {
							user.setPassword(passwordEncoder.encode(newUser.getPassword()));
							this.save(user);
						} else if (null == newUser.getPassword() || ( null != newUser.getPassword() && !passwordEncoder.matches(newUser.getPassword(), user.getPassword()))) {
                            UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.INCORRECT_PASSWORD;
                            exceptionMessage.setErrorMessage(resourceBundle.getString(languageMap.get(Constants.INCORRECT_PASSWORD)));
                            exceptionMessage.setDefaultMessage(resourceBundle.getString(languageMap.get(Constants.INCORRECT_PASSWORD)));
							throw new ConflictException(exceptionMessage);
						}
					}
				} else {
					UserExceptionConflictMsg exceptionMessage = null;

					if(null != optPhoneUser.get().getEmail() && !STRING_EMPTY.equals(optPhoneUser.get().getEmail())){
					    exceptionMessage = UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL;
                        exceptionMessage.setErrorMessage(resourceBundle.getString(languageMap.get(Constants.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL)));
                        exceptionMessage.setDefaultMessage(resourceBundle.getString(languageMap.get(Constants.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL)));
                    }else {
					    exceptionMessage = UserExceptionConflictMsg.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER;
                        exceptionMessage.setErrorMessage(resourceBundle.getString(languageMap.get(Constants.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER)));
                        exceptionMessage.setDefaultMessage(resourceBundle.getString(languageMap.get(Constants.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER)));
                    }
					throw new ConflictException(exceptionMessage);
				}
			} else {
                UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.USER_ALREADY_EXIST;
                exceptionMessage.setErrorMessage(resourceBundle.getString(languageMap.get(Constants.USER_ALREADY_EXIST)));
                exceptionMessage.setDefaultMessage(resourceBundle.getString(languageMap.get(Constants.USER_ALREADY_EXIST)));
				throw new ConflictException(exceptionMessage);
			}
		} else if (!isEmailUser && isPhoneUser) {

			user = optPhoneUser.get();
			if (StringUtils.isBlank(user.getEmail()) || newUser.isMergeAccount()) {
				if ((CountryCode.US.equals(userCountryCode) || CountryCode.CA.equals(userCountryCode))
						&& !user.getCountryCode().equals(userCountryCode)) {
					UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.USER_ALREADY_PRESENT_WITH_COUNTRY_CODE;
                    String constantMessage = resourceBundle.getString(languageMap.get(Constants.USER_ALREADY_PRESENT_WITH_COUNTRY_CODE)).replace(Constants.COUNTRY_CODE_FOR_USER_WITH_COUNTRY_CODE_OR_NOT, String.valueOf(user.getCountryCode()));
                    Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
                    defaultMessageParamMap.put(Constants.COUNTRY_CODE_FOR_USER_WITH_COUNTRY_CODE_OR_NOT,String.valueOf(user.getCountryCode()));
                    exceptionMessage.setErrorMessage(constantMessage);
                    exceptionMessage.setDeveloperMessage(constantMessage);
                    exceptionMessage.setDefaultMessage(Constants.USER_ALREADY_PRESENT_WITH_COUNTRY_CODE);
                    exceptionMessage.setDefaultMessageParamMap(defaultMessageParamMap);
                    throw new ConflictException(exceptionMessage);

				}
				// user does not have email
				if (StringUtils.isNotBlank(newUser.getEmail())) {
					user.setEmail(newUser.getEmail());
				}
				setFirstAndLastName(newUser, user);
				if (event != null) {
					user.setMostRecentEventId(event.getEventId());
				}

				if (socialSignUp) {
					user.setFbUserId(newUser.getFacebookUserId());
					user.setGoogleUserId(newUser.getGoogleUserId());
					user.setLinkedinUserId(newUser.getLinkedinUserId());
                    user.setAppleUserId(newUser.getAppleUserId());
                }

				if (!(socialSignUp || bidderSignUp || isStaffSignup || skipPassword)) {
					user.setPassword(passwordEncoder.encode(newUser.getPassword()));
				}
				this.save(user);

				this.addUserRole(user, ROLE_USER.name());

			} else {
				// reject this because this phone number already exists with a
				// email
                return throwErrorAlreadyEmailExists(optPhoneUser.get(), null,null,0l);
			}
		} else if (!isPhoneUser && isEmailUser) {
			user = optEmailUser.get();
			if (!(socialSignUp || bidderSignUp || isStaffSignup || skipPassword)) {
                if (StringUtils.isBlank(user.getPassword())) {
                    user.setPassword(passwordEncoder.encode(newUser.getPassword()));
                } else if (!passwordEncoder.matches(newUser.getPassword(), user.getPassword())) {
                            log.warn("password did not match for user {}",newUser.getEmail());//NOSONAR
                            UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.INCORRECT_PASSWORD;
                            exceptionMessage.setErrorMessage(resourceBundle.getString(languageMap.get(Constants.INCORRECT_PASSWORD)));
                            exceptionMessage.setDefaultMessage(resourceBundle.getString(languageMap.get(Constants.INCORRECT_PASSWORD)));
                            throw new ConflictException(exceptionMessage);
                        }
            }

			// not check user phone number for social signup

            /* user email matched but user number is different, updating the user number in this
             * for reference check thread https://accelevents.slack.com/archives/C01CQ3P1L2J/p1624985592103200?thread_ts=1624898642.048300&cid=C01CQ3P1L2J
             */
			if (socialSignUp || isLogin || user.getPhoneNumber() == 0) {
				if (user.getCountryCode() == null) {
					user.setCountryCode(userCountryCode);
				} else if ((CountryCode.US.equals(userCountryCode) || CountryCode.CA.equals(userCountryCode))
						&& !user.getCountryCode().equals(userCountryCode)) {
                    UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.USER_ALREADY_PRESENT_WITH_COUNTRY_CODE;
                    String constantMessage = resourceBundle.getString(languageMap.get(Constants.USER_ALREADY_PRESENT_WITH_COUNTRY_CODE)).replace(Constants.COUNTRY_CODE_FOR_USER_WITH_COUNTRY_CODE_OR_NOT, String.valueOf(user.getCountryCode()));
                    Map<String,String> defaultMessageParamMap=new LinkedHashMap<>();
                    defaultMessageParamMap.put(Constants.COUNTRY_CODE_FOR_USER_WITH_COUNTRY_CODE_OR_NOT,String.valueOf(user.getCountryCode()));
                    exceptionMessage.setErrorMessage(constantMessage);
                    exceptionMessage.setDeveloperMessage(constantMessage);
                    exceptionMessage.setDefaultMessage(Constants.USER_ALREADY_PRESENT_WITH_COUNTRY_CODE);
                    exceptionMessage.setDefaultMessageParamMap(defaultMessageParamMap);
                    throw new ConflictException(exceptionMessage);
				}
				if (newUser.getPhoneNumber() > 0 ) {
					user.setPhoneNumber(newUser.getPhoneNumber());
				}
				if (event != null) {
					user.setMostRecentEventId(event.getEventId());
				}
				setFirstAndLastName(newUser, user);

				if (socialSignUp) {
					user.setFbUserId(newUser.getFacebookUserId());
					user.setGoogleUserId(newUser.getGoogleUserId());
                    user.setLinkedinUserId(newUser.getLinkedinUserId());
                    user.setAppleUserId(newUser.getAppleUserId());
                }

				this.save(user);

				this.addUserRole(user, ROLE_USER.name());
			} /*else {
				UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER;
				exceptionMessage.setErrorMessage(
						"This email " + user.getEmail() + " is already associated with another phone number");
				throw new ConflictException(exceptionMessage);
			}*/
		} else {

			user = new User();
			if (StringUtils.isNotBlank(newUser.getEmail())) {
				user.setEmail(newUser.getEmail());
			}
			if (newUser.getPhoneNumber() != 0) {
				user.setPhoneNumber(newUser.getPhoneNumber());
			}
			user.setCountryCode(userCountryCode);
			// ADDED for social login
			if (!(socialSignUp || bidderSignUp || isStaffSignup || skipPassword)) {
			    if(StringUtils.isNotBlank(newUser.getPassword())) {
                    validatePassword(newUser.getPassword());
                    user.setPassword(passwordEncoder.encode(newUser.getPassword()));
                }else {
			        log.warn("password is blank for user {}",newUser.getEmail());//NOSONAR
                }
			}
			if (event != null) {
				user.setMostRecentEventId(event.getEventId());
			}
			setFirstAndLastName(newUser, user);

			if (socialSignUp) {
				user.setFbUserId(newUser.getFacebookUserId());
				user.setGoogleUserId(newUser.getGoogleUserId());
                user.setLinkedinUserId(newUser.getLinkedinUserId());
                user.setAppleUserId(newUser.getAppleUserId());
            }

			this.save(user);

			this.addUserRole(user, ROLE_USER.name());
		}
		return user;
	}

	private void setFirstAndLastName(UserSignupDto newUser, User user) {
		if (StringUtils.isNotBlank(newUser.getFirstName())) {
            user.setFirstName(newUser.getFirstName());
        }
		if (StringUtils.isNotBlank(newUser.getLastName())) {
            user.setLastName(newUser.getLastName());
        }
	}

	private void createEventStaff(User user, Event event, StaffRole role) {
		Staff staff = new Staff(role, event, user, new Date());
		staffService.save(staff);
		if(role.equals(StaffRole.admin)) {
			StaffEmailNotifications staffEmailNotifications = new StaffEmailNotifications(staff, true, true, true, false);
			this.staffEmailNotificationsRepository.save(staffEmailNotifications);
		}
	}

	@Override
	@Transactional
	public void setupUserForEvent(User user, Event event) {
		this.updateUserMostResentEvent(user, event);
		this.createEventStaff(user, event, StaffRole.admin);
		this.addUserRole(user, ROLE_ADMIN.name());
        eventService.addNewApiUserToStaff(event.getOrganizer(), event, event.getWhiteLabel());
	}

	@Override
	@Transactional
	public User updateUserMostResentEvent(User user, Event event) {
		log.info("Update Recent Event id {} into User {}" , event.getEventId() , user);
        user.setMostRecentEventId(event.getEventId());
		return this.save(user);
	}

	@Override
	public void saveName(String firstName, String lastName, User user) {
		if (StringUtils.isNoneBlank(firstName, lastName)) {
			if (StringUtils.isBlank(lastName)) {
				user.setFirstName(firstName);
				log.info("Set Only First Name : {}" , firstName);
			} else {
				user.setFirstName(firstName);
				log.info("Set First Name : {}" , firstName);
				user.setLastName(lastName);
				log.info("Set Last Name : {}" , lastName);
			}
			this.save(user);
		}
	}

	@Override
	public void saveEmail(String email, User user) {
		if (StringUtils.isNotBlank(email)) {
			user.setEmail(email);
			this.save(user);
		}
	}

	@Override
	public User getOrInsertByPhoneNumberAndEvent(AccelEventsPhoneNumber accelEventsPhoneNumber, long eventId) {
		Optional<User> optUser = roUserService.getUserByAEPhoneNumber(accelEventsPhoneNumber);
		if (optUser.isPresent()) {
			return optUser.get();
		} else {
			log.info("Creating a new user");
			User user = new User(accelEventsPhoneNumber);
			user.setMostRecentEventId(eventId);
			this.save(user);
			this.addUserRole(user, ROLE_USER.name());
			return user;
		}

	}

	@Override
	public Optional<User> findUserByPhoneNumber(long phoneNumber) {
		log.info("findUserByPhoneNumber|phoneNumber|{}",phoneNumber);
		return Optional.ofNullable(userRepository.findUserByPhoneNumber(phoneNumber));
	}

	@Override
	public Optional<User> getUserBySignupDtoPhoneNumber(UserSignupDto userSignupDto) {
		AccelEventsPhoneNumber ae = new AccelEventsPhoneNumber(
				StringUtils.isBlank(userSignupDto.getCountryCode()) ? CountryCode.US
						: CountryCode.valueOf(StringUtils.upperCase(userSignupDto.getCountryCode())),
				userSignupDto.getPhoneNumber());
		return roUserService.getUserByAEPhoneNumber(ae);
	}

	@Override
	public void addUserAsWhiteLabelStaff(User user, WhiteLabel whiteLabel, Event event) {
		Staff whiteLabelStaff = new Staff(StaffRole.admin, whiteLabel, event, user, new Date());
		staffService.save(whiteLabelStaff);
        staffEmailNotificationsRepository.save(new StaffEmailNotifications(whiteLabelStaff));
        eventService.addNewApiUserToStaff(null, event, whiteLabel);
	}

	// TODO refactor method
	@Override
	public boolean isCCRequired(ModuleType moduleType, User user, Event event, boolean isBidPage) {
		boolean ccRequired = true;

		boolean hasCardPresent = isCardLinked(user, event);

		if (moduleType.equals(ModuleType.AUCTION)) {
			if (isBidPage) {
				ccRequired = event.isCreditCardEnabled() && event.isCcRequiredForBidConfirm() && !hasCardPresent;
			} else {
				ccRequired = event.isCreditCardEnabled() && !hasCardPresent;
			}
		} else if (moduleType.equals(ModuleType.CAUSEAUCTION) || moduleType.equals(ModuleType.DONATION)
				|| moduleType.equals(ModuleType.RAFFLE)) {
			ccRequired = event.isCreditCardEnabled() && !hasCardPresent;
		} else if (moduleType.equals(ModuleType.TICKETING)) {
			ccRequired = true;
		}
		return ccRequired;
	}

	private boolean isCardLinked(User user, Event event) {
		boolean hasCardPresent = false;
		if (user != null) {
			Optional<Payment> paymentOptional = paymentService.findByUserIdAndEventId(user.getUserId(),
					event.getEventId());
			if (paymentOptional.isPresent()) {
				Payment payment = paymentOptional.get();
				hasCardPresent = StringUtils.isNotBlank(payment.getStripeToken());
			}
		}
		return hasCardPresent;
	}

	@Override
	public EventUserInfoDto extractUserInfoForEvent(Long userId, Event event, boolean addStripeLinkedCard) {
		Optional<User> userOptional = roUserService.getUserById(userId);
		if (userOptional.isPresent()) {
			return extractUserInfoForEvent(userOptional.get(), event, addStripeLinkedCard,null);
		} else {
			throw new NotFoundException(USER_NOT_FOUND);
		}
	}

    @Override
	public boolean isEventAdmin(Event event, User user) {
		// TODO : Optimize
        if(user == null){
            throw new AuthorizationException(Constants.NOT_AUTHORIZE);
        }
		boolean result = false;
		// log.info("Checking if admin not");//nosonar
		List<String> userRoles = roUserService.getUserRoles(user);
		if (roUserService.isSuperAdminUser(user, userRoles) ||
                roStaffService.isWhiteLabelAdminForEvent(user, userRoles, event) ||
                roStaffService.isEventCoordinatorForEvent(user, userRoles, event)) {
			// log.info("User is super admin");//nosonar
			result = true;
		} else {
			for (Staff staff : roStaffService.findByEvent(event)) {
				if (staff.getUser().getUserId().equals(user.getUserId()) && staff.getRole().equals(StaffRole.admin)) {
					result = true;
				}
			}
		}
		return result;
	}

	@Override
	public EventUserInfoDto extractUserInfoForEvent(User user, Event event, boolean addStripeLinkedCard,ModuleType moduleType) {
		EventUserInfoDto userInfo = new EventUserInfoDto(user);
        if (null != moduleType) {
             userInfo.setAddressRequired((staffPageService.getRequiredAddressModuleWise(moduleType,event)));
        } else {
            userInfo.setAddressRequired(false);
        }
        com.accelevents.enums.UserRole userRole = roStaffRoleService.getUserRoleByEvent(user,event);
		userInfo.setHasStaffAccess(Permissions.STAFF_OR_HIGHER_ROLE(userRole));
		if (userInfo.isHasStaffAccess()) {
            hubspotContactService.updateHubspotContactLastWebAppAccessProperty(user.getEmail());
        }
		userInfo.setAdmin(Permissions.ADMIN_OR_HIGHER_ROLE(userRole));
		if(addStripeLinkedCard){
            LinkedCard linkedCard = new LinkedCard();
            linkedCard.setStripeCards(this.eventService.getLinkedCreditCard(event, user));
            userInfo.setLinkedCard(linkedCard);
        }
		if (event.getEnableBidderRegistration()) {
			BidderNumber bidderNumber = this.bidderNumberService.findByEventAndUser(event, user);
			if (bidderNumber != null) {
				userInfo.setBidderNumber(bidderNumber.getBidderNumber());
			}
		}
        boolean showPopUp = ticketingOrderDetailsService.getEventShowPopUpStatus(user, event);
        userInfo.setShowPopUp(showPopUp);
        userInfo.setChatExpanded(user.getChatExpanded());

        userInfo.setOrderIds(eventCommonRepoService.findBuyerOrderIdByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(event.getEventId(), user.getEmail()));
		return userInfo;
	}

	@Override
	public List<EventLinkedCard> getAllLinkedCards(User user) {
		return this.eventService.getLikedCard(user);
	}

	@Override
	public List<StripeSubscriptionDto> getAllSubscribedStripePlans(Event event, User user) {
		return this.eventService.getSubscriptionPlansForUser(event, user);
	}

	@Override
	@Transactional
	public void unSubscribeStripePlanToCustomer(Event event, String subscriptionId) throws StripeException {
        Donation donation = donationService.getDonationBySubscriptionId(event, subscriptionId);
        if(donation==null){
    throw new NotAcceptableException(NotAceptableExeceptionMSG.ALREADY_UNSUBSCRIBED);
    }
        String stripeSecretKey = roStripeService.findByEvent(event).getAccessToken();
		this.stripePaymentService.unSubscribeStripePlanToCustomer(stripeSecretKey, subscriptionId, false);
		if(donation!= null){
			donation.setSubscriptionId(null);
			donationService.save(donation);
		}
	}

	@Override
	public LinkedCardListDto getLinkedCreditCard(Event event, User user) {
		LinkedCardListDto linkedCardListDto = new LinkedCardListDto();
		Stripe stripe = roStripeService.findByEvent(event);
		linkedCardListDto.setCreditCardList(this.eventService.getLinkedCreditCard(event, user));
		linkedCardListDto.setPaymentGateway(stripe.getPaymentGateway());
		return linkedCardListDto;
	}

	@Override
	@Transactional
    public AccessTokenModel signUpWLEventAdminUserAndReturnAccessTokenModel(AdminSignupDto adminSignupDto,
                                                                            String whiteLabelUrl) {
        validate(adminSignupDto);

        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
        if(!whiteLabel.isHideCreateEventButton()) {
            validateAndThrowErrorForSpecificDomains(adminSignupDto.getEmail());
            User user = null;
            try {
                if(!adminSignupDto.isSocialSignUp()){
                    adminSignupDto.setPassword(passwordEncoder.encode(adminSignupDto.getPassword()));
                }
                user = userRepository.save(this.createUserBySignupDto(adminSignupDto));
            } catch (DataIntegrityViolationException | ConstraintViolationException e) {
                throw new ConflictException(UserExceptionConflictMsg.EMAIL_ALREADY_REGISTERED);
            }
//            //create default organization for WL user SignUp
            String organizerName;
            if (adminSignupDto.getFirstName() != null) {
                organizerName = adminSignupDto.getFirstName()  + (adminSignupDto.getLastName() == null ? STRING_EMPTY : " " + adminSignupDto.getLastName()) + " Events ";
            }
            else{
                organizerName = adminSignupDto.getEmail().split(STRING_AT)[0] + " Events";
            }
            OrganizerDto organizerDto =  organizerService.createNewOrganizer(organizerName,user, SELF_SIGN_UP, whiteLabel);
            user = this.addAdminRoleForUserAndReturnUser(user);

            List<String> userRoles = roUserService.getUserRoles(user);
            if (userRoles != null && !userRoles.isEmpty()) {
                return new AccessTokenModel(
                        StringUtils.isBlank(user.getEmail()) ? user.getDisplayNumber() : user.getEmail(),
                        user.getPassword(), userRoles, user, true, null, Collections.singletonList(StaffRole.admin),
                        organizerDto.getOrganizerPageURL(), whiteLabel.getId(), organizerDto.getOrganizerId(),adminSignupDto.isFromEntra());
            } else {
                throw new NotFoundException(USER_NOT_FOUND);
            }
        }
        else{
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST);
        }
    }

	private void validate(AdminSignupDto adminSignupDto) {
		if (StringUtils.isBlank(adminSignupDto.getPassword()) && !adminSignupDto.isSocialSignUp()) {
			throw new ConflictException(UserExceptionConflictMsg.EMPTY_PASSWORD);
		}
		if (StringUtils.isBlank(adminSignupDto.getEmail())) {
			throw new ConflictException(UserExceptionConflictMsg.EMPTY_EMAIL);
		}
		if (!adminSignupDto.isSocialSignUp()) {
            validatePassword(adminSignupDto.getPassword());
        }
		Optional<User> optEmailUser = roUserService.getUserByEmail(adminSignupDto.getEmail());
		if (optEmailUser.isPresent()) {
			throw new ConflictException(UserExceptionConflictMsg.USER_ALREADY_EXIST);
		}
	}

	@Override
	@Transactional
	public void addUserCard(User user, Event event, String newToken, boolean isDefault,
			boolean absorbCardFoundException) throws StripeException, ApiException {
        Stripe stripe = roStripeService.findByEvent(event);
        if (null != stripe) {
            if (isStripePaymentGateway(stripe)) {
                RequestOptions ro = RequestOptions.builder().setApiKey(stripe.getAccessToken()).build();
                PaymentMethod paymentMethod = PaymentMethod.retrieve(newToken, ro);
                String fingerprint = paymentMethod.getCard().getFingerprint();
                int count = stripeCustomersRepository.countByFingerPrint(fingerprint, event);
                if (count > 0) {
                    throw new NotAcceptableException(NotAceptableExeceptionMSG.SAME_CARD_DETAIL_IS_NOT_ALLOWED);
                }
                this.paymentService.createOrGetPaymentFromPaymentMethod(newToken,user,event, user);
            } else if (isSquarePaymentGateway(stripe)) {
                List<StripeCustomers> stripeCustomers = stripeCustomersRepository.getByUserAndEvent(user, event);
                if (!CollectionUtils.isEmpty(stripeCustomers)) {
                    String customerId = stripeCustomers.get(0).getStripecustomerid();
                    Card card = squarePaymentService.addCard(stripe, newToken, customerId);

                    int count = stripeCustomersRepository.countByFingerPrint(card.getFingerprint(), event);
                    if (count > 0) {
                        throw new NotAcceptableException(NotAceptableExeceptionMSG.SAME_CARD_DETAIL_IS_NOT_ALLOWED);
                    }
                    allPaymentService.saveSquareCustomer(customerId, card, user, event, true);
                }
            } else if(StripeUtil.isPaymentGatewayFromSpreedly(stripe.getPaymentGateway())) {
                CustomerCardDto card = spreedlyGatewayService.getCardDetailsFromSpreedly(stripe, null, newToken, event, user, false);
                int count = stripeCustomersRepository.countByFingerPrint(card.getFingerPrint(), event);
                if (count > 0) {
                    throw new NotAcceptableException(NotAceptableExeceptionMSG.SAME_CARD_DETAIL_IS_NOT_ALLOWED);
                }
                this.paymentService.createOrGetPaymentFromPaymentMethod(newToken,user,event, user);
            }
        }
	}

    private boolean isStripePaymentGateway(Stripe stripe) {
        return EnumPaymentGateway.STRIPE.value().equals(stripe.getPaymentGateway());
    }

    private boolean isSquarePaymentGateway(Stripe stripe) {
        return EnumPaymentGateway.SQUARE.value().equals(stripe.getPaymentGateway());
    }

	@Override
	@Transactional
	public void setDefaultCard(User user, Event event, String cardId) {
		this.eventService.setDefaultCard(event, user, cardId);
	}


	@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED ,propagation = Propagation.REQUIRES_NEW)
	public void sendEMailToSocialUser(User user,String redirectUrl) {

			String token = user.getPasswordResetToken();
			if (StringUtils.isBlank(token)) {
				token = UUID.randomUUID().toString();
				user.setPasswordResetToken(token);
				this.save(user);
			}

			EmailMessage emailMessage = new EmailMessage(TemplateId.CREATE_PWD);
			emailMessage.setBody(Constants.STRING_BLANK);
			emailMessage.setSubject("Create Password");
			emailMessage.setTemplateName(TemplateId.CREATE_PWD.getValue());

			String uiBaseUrl = this.uiBaseurl;
			String path = "/u/new-password";

			String redirectLink;
			if (StringUtils.isNotBlank(redirectUrl)) {
				redirectLink = redirectUrl;
			} else {
				Event userMostRecentEvent = roEventService.getEventById(user.getMostRecentEventId());
				redirectLink = uiBaseUrl + getEventPath() + userMostRecentEvent.getEventURL();
			}
			Map<String, Object> data = emailMessage.getSubstitutionData();
            data.put(CLOUDINARY_IMAGE_PREFIX,imageConfiguration.getImagePreFixWithCloudinaryUrl());
			data.put(TOKEN_LOWER, uiBaseUrl + path + Constants.TOKEN + token + "&redirect=" + redirectLink); //NOSONAR

			Set<String> receivers = new HashSet<>();
			receivers.add(user.getEmail());
			emailService.sendTemplateMail(emailMessage, receivers);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
	public AccessTokenModel getBidderUserDetailForLoginSignup(UserSignupDto userSignupDto, String eventurl,
                                                              boolean socialSignUp, String ipAddress, boolean skipPassword) {
		Event event = roEventService.getEventByURL(eventurl);
		User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, socialSignUp, false, false,
				skipPassword);

		List<String> userRoles = roUserService.getUserRoles(user);
		if (null != userRoles && !userRoles.isEmpty()) {
			return new AccessTokenModel(userSignupDto.getEmail(), userSignupDto.getPassword(), userRoles, user, false,
					event, roStaffService.getStaffRole(event, user));
		} else {
			throw new NotFoundException(USER_NOT_FOUND);
		}
	}

	@Override
	public String getTags(User user, Event event) {
		StringBuilder tags = new StringBuilder();
		List<Staff> staffList = this.roStaffService.findByEventAndUserNotExhibitor(event, user);
		if (staffList != null) {
			if(staffList.stream().anyMatch(staff -> staff.getRole().equals(StaffRole.admin))) {
				if (StringUtils.isNotBlank(tags.toString())) {
					tags.append(",");
				}
				tags.append("fundraising_event_admin, ticketing_event_admin");
				if (null != event.getWhiteLabel()) {
					tags.append(", white_label_admin");
				}
			} else if (staffList.stream().anyMatch(staff -> staff.getRole().equals(StaffRole.staff))) {
				if (StringUtils.isNotBlank(tags.toString())) {
					tags.append(",");
				}
				tags.append("fundraising_event_staff, ticketing_event_staff");
				if (null != event.getWhiteLabel()) {
					tags.append(", white_label_staff");
				}
			}
		}

		if (event.isSilentAuctionEnabled()) {
			if (StringUtils.isNotBlank(tags.toString())) {
				tags.append(",");
			}
			tags.append("silent_auction");
		}

		if (event.isTicketingEnabled()) {
			if (StringUtils.isNotBlank(tags.toString())) {
				tags.append(",");
			}
			tags.append("ticketing");
		}

		if (event.isRaffleEnabled()) {
			if (StringUtils.isNotBlank(tags.toString())) {
				tags.append(",");
			}
			tags.append("raffle");
		}

		if (event.isCauseAuctionEnabled()) {
			if (StringUtils.isNotBlank(tags.toString())) {
				tags.append(",");
			}
			tags.append("fund_a_need");
		}

		DonationSettings donationSettings = this.donationSettingsService.getByEventId(event.getEventId());
		if (donationSettings != null && donationSettings.isTextToGiveActivated()) {
			if (StringUtils.isNotBlank(tags.toString())) {
				tags.append(",");
			}
			tags.append("text_to_give");
		}

		return tags.toString();
	}

	@Override
	public User getUserByEMailOrCellNumber(String email, String phonenumber, String countryCode) {
		return roUserService.findByEmailOrCellNumberAndCountryCode(email, Long.parseLong(phonenumber),
				CountryCode.valueOf(StringUtils.upperCase(countryCode)));
	}

	@Override
	@Transactional(rollbackFor = ConflictException.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
	public User getOrCreateUserForTicketing(TicketBookingDto ticketBookingDto, Event event,
                                            String ipAddress) {
		User user = new User();
        if (null != ticketBookingDto && ticketBookingDto.getPurchaser()!=null && ticketBookingDto.getPurchaser().getAttributes()!=null) {
            Iterator<AttributeKeyValueDto> buyerAttributes = ticketBookingDto.getPurchaser().getAttributes().iterator();
            String countrycode = null;
            String phoneNumber = null;
            while (buyerAttributes.hasNext()) {
                AttributeKeyValueDto attributeKeyValueDto = buyerAttributes.next();
                if (Constants.STRING_FIRST_SPACE_NAME.equalsIgnoreCase(attributeKeyValueDto.getKey())) {
                    user.setFirstName(attributeKeyValueDto.getValue());
                }
                if (Constants.STRING_LAST_SPACE_NAME.equalsIgnoreCase(attributeKeyValueDto.getKey())) {
                    user.setLastName(attributeKeyValueDto.getValue());
                }
                if (Constants.STRING_EMAIL.equalsIgnoreCase(attributeKeyValueDto.getKey())) {
                    user.setEmail(attributeKeyValueDto.getValue());
                }
                if (COUNTRY_CODE_KEY.equalsIgnoreCase(attributeKeyValueDto.getKey())) {
                    countrycode = attributeKeyValueDto.getValue();
                }
                if (StringUtils.isNotBlank(countrycode) && StringUtils.isNotBlank(phoneNumber)) {//NOSONAR
                    user = this.mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(user, countrycode, phoneNumber, null, null); //NOSONAR
                }
            }
            user.setCountryCode(ticketingHelperService.getCountryCode(countrycode, ticketBookingDto.getCountryCode()));
		}
		Optional<User> optUser = roUserService.getUserByEmail(user.getEmail());
		if (optUser.isPresent()) {
			user = optUser.get();
		} else {
			user.setMostRecentEventId(event.getEventId());
			// user does not have email
            if(user.getCountryCode() == null) {
                user.setCountryCode(CountryCode.US);
            }
            this.save(user);

			this.addUserRole(user, ROLE_USER.name());
		}
		return user;
	}

    private void checkPhoneNumberExistorNot(Event event, User loggedInUser, PurchaserBookingDto purchaserBookingDto) {
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(null, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        Optional<User> optPhoneUser = Optional.empty();

        Optional<AttributeKeyValueDto> optionalCountryCode = purchaserBookingDto.getAttributes().stream().filter(e -> e.getKey().equalsIgnoreCase("CountryCode")).findFirst();
        Optional<AttributeKeyValueDto> phoneNumberOptional = purchaserBookingDto.getAttributes().stream().filter(e -> e.getKey().equalsIgnoreCase("phoneNumber")).findFirst();
        if (optionalCountryCode.isPresent() && phoneNumberOptional.isPresent()) {
            String countryCode = optionalCountryCode.get().getValue();
            String phoneNumber = phoneNumberOptional.get().getValue();
            if (StringUtils.isNotEmpty(countryCode) && StringUtils.isNotEmpty(phoneNumber)) {
                PhoneNumberDto phoneNumberDto = new PhoneNumberDto(countryCode, phoneNumber);
                AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(phoneNumberDto.getCountryCode(), phoneNumberDto.getPhoneNumber());
                optPhoneUser = roUserService.getUserByAEPhoneNumber(accelEventsPhoneNumber);
                if(!Arrays.asList(CountryCode.values()).contains(CountryCode.valueOf(phoneNumberDto.getCountryCode().toString()))){
                    throw new NotFoundException(NotFound.NOT_VALID_COUNTRY_CODE);
                }
                loggedInUser.setCountryCode(phoneNumberDto.getCountryCode());
                loggedInUser.setPhoneNumber(phoneNumberDto.getPhoneNumber());
            }
        }

        if (optPhoneUser.isPresent()) {
            ConflictException.UserExceptionConflictMsg exceptionMessage = ConflictException.UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_PARAMETER;
            String constantMessage = resourceBundle.getString(languageMap.get(Constants.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_PARAMETER)).replace(Constants.PHONE_NUMBER_ALREDY_WITH_EMAIL_PARAMETER, String.valueOf(optPhoneUser.get().getPhoneNumber()));
            Map<String, String> defaultMessageParamMap = new LinkedHashMap<>();
            defaultMessageParamMap.put(Constants.PHONE_NUMBER_ALREDY_WITH_EMAIL_PARAMETER, String.valueOf(optPhoneUser.get().getPhoneNumber()));
            exceptionMessage.setErrorMessage(constantMessage);
            exceptionMessage.setDeveloperMessage(constantMessage);
            exceptionMessage.setDefaultMessage(Constants.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_PARAMETER);
            exceptionMessage.setDefaultMessageParamMap(defaultMessageParamMap);
            throw new ConflictException(exceptionMessage);
        }
    }

	@Override
	@Transactional
    public AccessTokenModel signUpAdminUserAndReturnAccessTokenModel(AdminSignupDto adminSignupDto,boolean isAppleLogin) {
        if (!isAppleLogin) {
          validate(adminSignupDto);
        }
        this.validateUserNotPresentInDB(adminSignupDto.getEmail());
        validateAndThrowErrorForSpecificDomains(adminSignupDto.getEmail());
        User user = null;
        try {
            if(!adminSignupDto.isSocialSignUp()){
                adminSignupDto.setPassword(passwordEncoder.encode(adminSignupDto.getPassword()));
            }
            user = userRepository.save(this.createUserBySignupDto(adminSignupDto));

        } catch (DataIntegrityViolationException | ConstraintViolationException e) {
            throw new ConflictException(UserExceptionConflictMsg.EMAIL_ALREADY_REGISTERED);
        }
        String organizerName = adminSignupDto.getFirstName().concat(" ").concat(adminSignupDto.getLastName()).concat(" Events");
        hubspotContactService.asyncCreateHubspotContact(user);
        OrganizerDto organizerDto = organizerService.createNewOrganizer(organizerName,user, SELF_SIGN_UP, null);

        user = this.addAdminRoleForUserAndReturnUser(user);

        List<String> userRoles = roUserService.getUserRoles(user);
        if (userRoles != null && !userRoles.isEmpty()) {
             return new AccessTokenModel(
                StringUtils.isBlank(user.getEmail()) ? user.getDisplayNumber() : user.getEmail(),
                 user.getPassword(), userRoles, user, true, null, Collections.singletonList(StaffRole.admin),
                     organizerDto.getOrganizerPageURL(), organizerDto.getWhiteLabelId(), organizerDto.getOrganizerId(),adminSignupDto.isFromEntra());
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }

    @Override
	@Transactional
	public AccessTokenModel createNewEventForAdmin(AdminSignupDto adminSignupDto, String whiteLabelUrl) {
		if (StringUtils.isBlank(adminSignupDto.getPassword())) {
			throw new ConflictException(UserExceptionConflictMsg.EMPTY_PASSWORD);
		}
		if (StringUtils.isBlank(adminSignupDto.getEmail())) {
			throw new ConflictException(UserExceptionConflictMsg.EMPTY_EMAIL);
		}
		Optional<User> optEmailUser = roUserService.getUserByEmail(adminSignupDto.getEmail());
		if (!optEmailUser.isPresent()) {
			throw new NotFoundException(USER_NOT_FOUND);
		}
		if (!passwordEncoder.matches(adminSignupDto.getPassword(), optEmailUser.get().getPassword())) {
			throw new ConflictException(UserExceptionConflictMsg.INCORRECT_PASSWORD);
		}

		WhiteLabel whiteLabel = null;
		if (StringUtils.isNotEmpty(whiteLabelUrl)) {
			whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
			List<TransactionFeeConditionalLogic> transactionFeeConfigurations = transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(whiteLabel);

			if(transactionFeeConfigurations.isEmpty()){
				throw new NotFoundException(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND);
			}
		}

		Event event = eventService.createEvent(false, false, false, adminSignupDto.getEmail(), whiteLabel,
				Collections.singletonList(ROLE_ADMIN.name()), optEmailUser.get().getUserId(), null);

		User user = this.addAdminRoleForUserAndReturnUser(optEmailUser.get());

		if (whiteLabel != null) {
			whiteLabelService.createConditionalLogicRecordAndAttachUser(user, whiteLabel, event,
					Collections.singletonList(ROLE_ADMIN.name()));
		}

        eventService.addEventCreatorContactWithLabelForEventToHubspot(event, user);
        eventService.addContactsWithLabelForEventToHubspot(event, user);

		List<String> userRoles = roUserService.getUserRoles(user);
		if (userRoles != null && !userRoles.isEmpty()) {
            Integration integration = integrationService.getIntegrationByWhiteLabelOrOrganizerIfAvailable(event);
            trayIntegrationService.mapEventDetails(integration, event, TrayIOWebhookActionType.CREATE_EVENT);
			return new AccessTokenModel(
					StringUtils.isBlank(user.getEmail()) ? user.getDisplayNumber() : user.getEmail(),
					user.getPassword(), userRoles, user, true, event, Collections.singletonList(StaffRole.admin),
                    Optional.ofNullable(whiteLabel).map(WhiteLabel::getId).orElse(null), null);
		} else {
			throw new NotFoundException(USER_NOT_FOUND);
		}

	}

	private User createUserBySignupDto(AdminSignupDto adminSignupDto) {
		User user = new User();
		user.setEmail(adminSignupDto.getEmail());
		user.setPassword(adminSignupDto.getPassword());

        if (StringUtils.isNotEmpty(adminSignupDto.getFirstName())) {
            user.setFirstName(adminSignupDto.getFirstName());
        } else{
            user.setFirstName(adminSignupDto.getEmail().split(STRING_AT)[0]);
        }
        if (StringUtils.isNotEmpty(adminSignupDto.getLastName())) {
            user.setLastName(adminSignupDto.getLastName());
        }else{
            user.setLastName(STRING_EMPTY);
        }
        if(EnumUtils.isValidEnum(CountryCode.class, adminSignupDto.getCountryCode())) {
		    user.setCountryCode(CountryCode.valueOf(adminSignupDto.getCountryCode()));
        }
		if (adminSignupDto.isSocialSignUp() ) {
		    if(StringUtils.isNotEmpty(adminSignupDto.getFbUserId())){
			user.setFbUserId(adminSignupDto.getFbUserId());
		    }
            if(StringUtils.isNotEmpty(adminSignupDto.getGoogleUserId())){
                user.setGoogleUserId(adminSignupDto.getGoogleUserId());
            }
            if(StringUtils.isNotEmpty(adminSignupDto.getLinkedinUserId())){
                user.setLinkedinUserId(adminSignupDto.getLinkedinUserId());
            }
            if(StringUtils.isNotEmpty(adminSignupDto.getAppleUserId())){
                user.setAppleUserId(adminSignupDto.getAppleUserId());
            }
		}

		return user;
	}

	@Override
	public UserProfileDto getUserProfileInfo(User user) {
		UserProfileDto userProfileDto = buildUserProfileDTO(user);
		return userProfileDto;
	}

    @Override
    public List<EventEndInfoDto>  getUserEventsInfo(User user, String source, String eventStatus, String searchString, Date searchDate) {
        List<EventEndInfoDto> userEventInfo = new ArrayList<>();
        Set<EventEndInfoDto> eventEndInfoDtos = eventService.getEventEndInfoForUser(user, source, searchString, searchDate, null);
        if(StringUtils.equals(Constants.STRING_ACTIVE, eventStatus)){
            eventEndInfoDtos.removeIf(eventEndInfoDto -> eventEndInfoDto.getEventEndDate()!= null && eventEndInfoDto.getEventEndDate().before(new Date()));
        }else if(StringUtils.equals(Constants.STRING_PAST, eventStatus)){
            eventEndInfoDtos.removeIf(eventEndInfoDto -> eventEndInfoDto.getEventEndDate()!= null && eventEndInfoDto.getEventEndDate().after(new Date()));
        }
        userEventInfo.addAll(eventEndInfoDtos);
        userEventInfo.sort((event1, event2) -> event2.getEventEndDate().compareTo(event1.getEventEndDate()));
        return userEventInfo;
    }

    @Override
    public List<EventEndInfoDto> getAllEventsForUserInWhitelabel(User user, String source, Long whitelabelId) {
        List<EventEndInfoDto> userEventInfo = new ArrayList<>(eventService.getEventEndInfoForUser(user, source, null, null, whitelabelId));
        userEventInfo.sort((event1, event2) -> event2.getEventEndDate().compareTo(event1.getEventEndDate()));
        return userEventInfo;
    }

	private UserProfileDto buildUserProfileDTO(User user) {

	    User userData = new User();
        UserProfileDto userProfileDto = new UserProfileDto();
        if(com.accelevents.utils.NumberUtils.isNumberGreaterThanZero(user.getUserId()))
            userData = findByUserId(user.getUserId());
        if(null != userData)
            userProfileDto = new UserProfileDto(userData);
        else
            userProfileDto = new UserProfileDto(user);
		List<String> userRoles = roUserService.getUserRoles(user);
		if (userRoles.contains(ROLE_SUPERADMIN.name())) {
			userProfileDto.setUserLabel("Super Admin");
			userProfileDto.setAdmin(true);
		} else if (userRoles.contains(ROLE_ADMIN.name())) {
			userProfileDto.setUserLabel("Admin");
			userProfileDto.setAdmin(true);
		}
        if(StringUtils.isNotBlank(user.getPassword())) {
            userProfileDto.setPasswordPresent(true);
        }
		return userProfileDto;
	}

	@Override
	@Transactional(rollbackFor = ConflictException.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
	public User updateUserProfileField(String fieldName, String value, User user) {
		switch (fieldName) {
			case Constants.STRING_FIRST_NAME:
				user.setFirstName(value);
				break;
			case Constants.STRING_LAST_NAME:
				user.setLastName(value);
				break;
			case Constants.STRING_PHONE_NUMBER:
				CountryCode CC = CountryCode.valueOf(value.substring(0, 2).toUpperCase());
				String phoneNumberString = value.substring(2);
				validateUserPhoneNumber(CC, phoneNumberString, user, true);
				break;
			case Constants.STRING_ADDRESS1:
				user.setAddress1(value);
				break;
			case Constants.STRING_ADDRESS2:
				user.setAddress2(value);
				break;
			case Constants.STRING_STATE:
				user.setState(value);
				break;
			case Constants.STRING_CITY_OR_PROVIDENCE:
				user.setCityOrProvidence(value);
				break;
			case Constants.STRING_COUNTRY:
				user.setCountry(value);
				break;
			case Constants.STRING_ZIPCODE:
				user.setZipcode(value);
				break;
			case Constants.STRING_EMAIL:
				validateUserNotPresentInDB(value);
				user.setEmail(value);
				break;
			default:
				throw new NotFoundException(NotFound.NOT_VALID_USER_PROFILE_FIELD);
		}
		return this.save(user);
	}

	@Override
	public AccessTokenModel getUserDetailForUnsubscribe(User user, Event event) {
		List<String> userRoles = roUserService.getUserRoles(user);
		if (null != userRoles && !userRoles.isEmpty()) {
			return new AccessTokenModel(
					StringUtils.isBlank(user.getEmail()) ? user.getDisplayNumber() : user.getEmail(),
					user.getPassword(), userRoles, user, false, event,
					roStaffService.getStaffRole(event, user));
		} else {
			throw new NotFoundException(USER_NOT_FOUND);
		}
	}

	@Override
	public List<UserProfileMapping> getUserActivityDetails(User user, Event currentEvent) {
		Set<Event> auctionEvents = eventService.getEventByAuctionBidUser(user);
		Set<Event> causeAuctionEvents = eventService.getEventByCauseAuctionPledgeUser(user);
		Set<Event> raffleEvents = eventService.getEventByRafflePurchasedTicketUser(user);

		Set<Event> allEvents = new HashSet<>();
		allEvents.addAll(auctionEvents);
		allEvents.addAll(causeAuctionEvents);
		allEvents.addAll(raffleEvents);
		if(currentEvent != null) {
			boolean alreadyPresent = allEvents.stream().anyMatch(event -> event.getEventURL().equals(currentEvent.getEventURL()));
			if (!alreadyPresent)
				allEvents.add(currentEvent);
		}
		List<UserProfileMapping> userProfileMappings = new ArrayList<>();
		allEvents.forEach(event -> {
		    boolean isCurrentEvent = false;
		    if (currentEvent !=null && event.getEventURL().equals(currentEvent.getEventURL())) {
                isCurrentEvent = true;
            }
            userProfileMappings.add(buildUserProfileMappingWithCurrentEvent(event, user,isCurrentEvent));
        });
		return userProfileMappings;
	}

    private UserProfileMapping buildUserProfileMappingWithCurrentEvent(Event event, User user, boolean isCurrentEvent) {
        UserProfileMapping userProfileMapping = buildUserProfileMapping(event, user);
        if (isCurrentEvent) {
            userProfileMapping.setCurrentEvent(true);
            userProfileMapping.setBasicEventInfo(new BasicEventInfo(event.getName(),event.getEventURL()));
        }
        return userProfileMapping;
    }

    /**
	 * Copied method from RaffleService, Injecting RaffleService was giving
	 * circular depedency error.
	 *
	 * @param user user details
	 * @param raffleId raffle id
	 * @return long
	 */
	private long getAvailableRaffleTickets(User user, long raffleId) {
		long submittedTickets = submittedRaffleTicketService.getTotalSubmittedTicketsByUserForRaffle(user, raffleId);
		long purchasedTickets = purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user, raffleId);
		return purchasedTickets - submittedTickets;
	}

	@Override
	@Transactional
	public SocialLoginAccessTokenModel addAdminUserToFacebook(String accessToken) {
		try {
			StringBuilder result = getFacebookData(accessToken);
			if (result != null) {
				ObjectMapper mapper = new ObjectMapper();
				FaceBookProfile profile = mapper.readValue(result.toString(), FaceBookProfile.class);

				Optional<User> optEmailUser = roUserService.getUserByEmail(profile.getEmail());
				if (optEmailUser.isPresent()) {
					User user = optEmailUser.get();
					user.setEmail(profile.getEmail());
					user.setFbUserId(profile.getId());
					user.setFirstName(profile.getFirst_name());
					user.setLastName(profile.getLast_name());

					this.save(user);

					Event event = roEventService.getEventByHostUserAndMostRecentEventId(optEmailUser.get());
					return new SocialLoginAccessTokenModel(getAccessTokenModel(event, user, profile.getEmail(), null,0L,false), false);
				} else {
					AdminSignupDto adminSignupDto = new AdminSignupDto();
					adminSignupDto.setEmail(profile.getEmail());
					adminSignupDto.setSocialSignUp(true);
					adminSignupDto.setFirstName(profile.getFirst_name());
					adminSignupDto.setLastName(profile.getLast_name());
					adminSignupDto.setFbUserId(profile.getId());

                    SocialLoginAccessTokenModel socialLoginAccessTokenModel = new SocialLoginAccessTokenModel(
                            this.signUpAdminUserAndReturnAccessTokenModel(adminSignupDto, false), true);
                    return socialLoginAccessTokenModel;
				}
			} else {
				throw new NotFoundException(NotFound.NOT_VALID_FACEBOOK_TOKEN);
			}
		} catch (BaseException e) {
			throw e;
		} catch (Exception exception) {
			throw new NotAcceptableException(exception);
		}
	}

	@Override
    @Transactional
	public SocialLoginAccessTokenModel addAdminUserToGoogle(String accessToken) {
        try {
            StringBuilder result = getGoogleData(accessToken);
            if (result != null) {
                ObjectMapper mapper = new ObjectMapper();
                GoogleProfile profile = mapper.readValue(result.toString(), GoogleProfile.class);

                Optional<User> optEmailUser = roUserService.getUserByEmail(profile.getEmail());
                if (optEmailUser.isPresent()) {
                    User user = optEmailUser.get();
                    user.setEmail(profile.getEmail());
                    user.setGoogleUserId(profile.getId());
                    user.setFirstName(profile.getGiven_name());
                    user.setLastName(profile.getFamily_name());

                    this.save(user);

                    Event event = roEventService.getEventByHostUserAndMostRecentEventId(optEmailUser.get());
                    return new SocialLoginAccessTokenModel(getAccessTokenModel(event, user, profile.getEmail(), null,0L,false), false);
                } else {
                    AdminSignupDto adminSignupDto = new AdminSignupDto();
                    adminSignupDto.setEmail(profile.getEmail());
                    adminSignupDto.setSocialSignUp(true);
                    adminSignupDto.setFirstName(profile.getGiven_name());
                    adminSignupDto.setLastName(profile.getFamily_name());
                    adminSignupDto.setGoogleUserId(profile.getId());

                    SocialLoginAccessTokenModel socialLoginAccessTokenModel = new SocialLoginAccessTokenModel(
                            this.signUpAdminUserAndReturnAccessTokenModel(adminSignupDto, false),true);
                    return socialLoginAccessTokenModel;
                }
            } else {
                throw new NotFoundException(NotFound.NOT_VALID_GOOGLE_TOKEN);
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception exception) {
            throw new NotAcceptableException(exception);
        }
    }

    @Override
    @Transactional
    public SocialLoginAccessTokenModel addAdminUserToLinkedin(LinkedinDto linkedinDto) {
        try {
            List<StringBuilder> result = getLinkedinData(linkedinDto.getAccessToken(),linkedinDto.getRedirectURL());
            String email=null;
            if (!result.isEmpty()) {
                ObjectMapper mapper = new ObjectMapper();
                JsonParser json =new JsonParser();
                LinkedinProfile profile = mapper.readValue(result.get(0).toString(), LinkedinProfile.class);
                JsonElement jb= json.parse(result.get(1).toString());
                JsonElement emailelement = jb.getAsJsonObject().get("elements").getAsJsonArray().get(0);
                if(emailelement!=null){
                    email= emailelement.getAsJsonObject().get("handle~").getAsJsonObject().get("emailAddress").getAsString();
                }
                if(email!=null) {
                    Optional<User> optEmailUser = roUserService.getUserByEmail(email);
                    if (optEmailUser.isPresent()) {
                        User user = optEmailUser.get();
                        user.setEmail(email);
                        user.setLinkedinUserId(profile.getId());
                        user.setFirstName(profile.getLocalizedFirstName());
                        user.setLastName(profile.getLocalizedLastName());
                        this.save(user);

                        Event event = roEventService.getEventByHostUserAndMostRecentEventId(optEmailUser.get());
                        return new SocialLoginAccessTokenModel(getAccessTokenModel(event, user, email, null,0L,false), false);
                    } else {
                        AdminSignupDto adminSignupDto = new AdminSignupDto();
                        adminSignupDto.setEmail(email);
                        adminSignupDto.setSocialSignUp(true);
                        adminSignupDto.setFirstName(profile.getLocalizedFirstName());
                        adminSignupDto.setLastName(profile.getLocalizedLastName());
                        adminSignupDto.setLinkedinUserId(profile.getId());


                        SocialLoginAccessTokenModel socialLoginAccessTokenModel = new SocialLoginAccessTokenModel(this.signUpAdminUserAndReturnAccessTokenModel(adminSignupDto, false), true);
                        return socialLoginAccessTokenModel;
                    }
                }{
                    throw new NotFoundException(NotFound.NOT_VALID_EMAIL);
                }
            } else {
                throw new NotFoundException(NotFound.NOT_VALID_LINKEDIN_TOKEN);
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception exception) {
            throw new NotAcceptableException(exception);
        }
    }

    private  Boolean CheckAuthentication(String code,String redirectURL) throws Exception {

        PrivateKey pKey = getPrivateKeyForApple();
        String token = Jwts.builder()
                .setHeaderParam(JwsHeader.KEY_ID, appleKeyId)
                .setIssuer(appleTeamId)
                .setAudience("https://appleid.apple.com")
                .setSubject(appleClientId)
                .setExpiration(new Date(System.currentTimeMillis() + (1000 * 60 * 5)))
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .signWith(SignatureAlgorithm.ES256, pKey)
                .compact();

        HttpClient clientauth = HttpClientBuilder.create().build();
        String redirecturlIfNotProvided=uiBaseurl.concat(SLASH_EVENTS);
        String redirecturl=redirectURL!= null? redirectURL:redirecturlIfNotProvided ;
        String url = APPLE_AUTH_URL;
        StringBuilder resultauth = null;
        HttpPost requestauth = new HttpPost(url);
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        nvps.add(new BasicNameValuePair("grant_type", "authorization_code"));
        nvps.add(new BasicNameValuePair("code", code));
        nvps.add(new BasicNameValuePair("client_id", appleClientId));
        nvps.add(new BasicNameValuePair("client_secret", token));
        nvps.add(new BasicNameValuePair("redirect_uri",redirecturl));
        UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(nvps, "utf-8");
        formEntity.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
        requestauth.setEntity(formEntity);
        HttpResponse responseauth = clientauth.execute(requestauth);
        return Constants.STATUS_CODE_OK == responseauth.getStatusLine().getStatusCode();
    }

    private  PrivateKey getPrivateKeyForApple() throws Exception {
        //read your key
        StringReader sr=new StringReader(applePrivateKey);
        final PEMParser pemParser = new PEMParser(sr);
        final JcaPEMKeyConverter converter = new JcaPEMKeyConverter();
        final PrivateKeyInfo object = (PrivateKeyInfo) pemParser.readObject();
        final PrivateKey pKey = converter.getPrivateKey(object);
        return pKey;
    }

    @Override
    @Transactional
    public SocialLoginAccessTokenModel addAdminUserToApple(AppleDto appleDto) {
        try {
            String sub = null;
            if(Boolean.TRUE.equals(CheckAuthentication(appleDto.getCode(),appleDto.getRedirectURL()))) {
                String[] parts = appleDto.getIdToken().split("\\.");
                String jwtData = new String(Base64.getUrlDecoder().decode(parts[1]));
                if (StringUtils.isNotEmpty(jwtData)) {
                    JSONObject payload = new JSONObject(jwtData);
                    if (payload.has("sub")) {
                        sub = payload.getString("sub");
                    }
                } else {
                    throw new NotFoundException(NotFound.NOT_VALID_APPLE_TOKEN);
                }

                Optional<User> userdetails = userRepository.findByAppleUserId(sub);
                if (userdetails.isPresent()) {
                    User user = userdetails.get();
                    Event event = roEventService.getEventByHostUserAndMostRecentEventId(user);
                    return new SocialLoginAccessTokenModel(getAccessTokenModel(event, user, user.getEmail(), null,0L,false), false);

                } else {
                    if(appleDto.getEmail()!=null){
                        Optional<User> optEmailUser = roUserService.getUserByEmail(appleDto.getEmail());
                        if (optEmailUser.isPresent()) {
                            User user = optEmailUser.get();
                            user.setAppleUserId(sub);
                            user.setFirstName(appleDto.getFirstName());
                            user.setLastName(appleDto.getLastName());
                            this.save(user);
                            Event event = roEventService.getEventByHostUserAndMostRecentEventId(optEmailUser.get());
                            return new SocialLoginAccessTokenModel(getAccessTokenModel(event, user, appleDto.getEmail(), null,0L,false), false);
                        }
                    }
                    AdminSignupDto adminSignupDto = new AdminSignupDto();
                        adminSignupDto.setEmail(appleDto.getEmail());
                        adminSignupDto.setSocialSignUp(true);
                        adminSignupDto.setFirstName(appleDto.getFirstName());
                        adminSignupDto.setLastName(appleDto.getLastName());
                    adminSignupDto.setAppleUserId(sub);

                    SocialLoginAccessTokenModel socialLoginAccessTokenModel = new SocialLoginAccessTokenModel(this.signUpAdminUserAndReturnAccessTokenModel(adminSignupDto, true), true);
                    return socialLoginAccessTokenModel;
                }
                }else{
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ISSUE_WHILE_LOGGIN_THROUGH_APPLE);
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception exception) {
            throw new NotAcceptableException(exception);
        }
    }


    @Override
    @Transactional
    public AccessTokenModel addUserToApple(AppleDto appleDto,String eventUrl) {
        try {
            String sub = null;
            if(Boolean.TRUE.equals(CheckAuthentication(appleDto.getCode(),appleDto.getRedirectURL()))) {
                String[] parts = appleDto.getIdToken().split("\\.");
                String jwtData = new String(Base64.getUrlDecoder().decode(parts[1]));
                if (StringUtils.isNotEmpty(jwtData)) {
                    JSONObject payload = new JSONObject(jwtData);
                    if (payload.has("sub")) {
                        sub = payload.getString("sub");
                    }
                } else {
                    throw new NotFoundException(NotFound.NOT_VALID_APPLE_TOKEN);
                }

                Optional<User> userdetails = userRepository.findByAppleUserId(sub);
                if (userdetails.isPresent()) {
                    User user = userdetails.get();
                    log.info("User {} found with the Apple user Id {}", user.getUserId(), sub);
                    Event event = StringUtils.isNotEmpty(eventUrl) ? roEventService.getEventByURL(eventUrl) : null;
                    return getAccessTokenModel(event, user, user.getEmail(), null,0L,false);

                } else {
                        if(appleDto.getEmail()!=null){
                            log.info("User {} not found with the apple user id {}", appleDto.getEmail(), sub);
                            Optional<User> optEmailUser = roUserService.getUserByEmail(appleDto.getEmail());
                            if (optEmailUser.isPresent()) {
                                User user = optEmailUser.get();
                                log.info("User found by Email {}", user.getUserId());
                                user.setAppleUserId(sub);
                                user.setFirstName(appleDto.getFirstName());
                                user.setLastName(appleDto.getLastName());
                                this.save(user);
                                Event event = StringUtils.isNotEmpty(eventUrl) ? roEventService.getEventByURL(eventUrl) : null;
                                return getAccessTokenModel(event, user, appleDto.getEmail(), null,0L,false);
                            }
                        }
                    UserSignupDto userSignupDto = new UserSignupDto(appleDto.getEmail(), null, appleDto.getPhoneNumber(),
                            appleDto.getCountryCode());
                    userSignupDto.setAppleUserId(sub);
                    userSignupDto.setFirstName(appleDto.getFirstName());
                    userSignupDto.setLastName(appleDto.getLastName());
                    Event event = StringUtils.isNotEmpty(eventUrl) ? roEventService.getEventByURL(eventUrl) : null;
                    User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
                    return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);

                }
            }else{
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ISSUE_WHILE_LOGGIN_THROUGH_APPLE);
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception exception) {
            throw new NotAcceptableException(exception);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public AccessTokenModel addUserLinkedin(LinkedinDto linkedinDto, String eventUrl, String ipAddress) {
        {
            try {
                List<StringBuilder> result = getLinkedinData(linkedinDto.getAccessToken(),linkedinDto.getRedirectURL());
                String email=null;
                if (!result.isEmpty()) {
                    ObjectMapper mapper = new ObjectMapper();
                    JsonParser json =new JsonParser();
                    LinkedinProfile profile = mapper.readValue(result.get(0).toString(), LinkedinProfile.class);
                    JsonElement jb= json.parse(result.get(1).toString());
                    JsonElement emailelement = jb.getAsJsonObject().get("elements").getAsJsonArray().get(0);
                    if(emailelement!=null){
                        email= emailelement.getAsJsonObject().get("handle~").getAsJsonObject().get("emailAddress").getAsString();
                    }
                    if(email!=null) {
                        UserSignupDto userSignupDto = new UserSignupDto(email, null, linkedinDto.getPhoneNumber(),
                                linkedinDto.getCountryCode());
                        userSignupDto.setFirstName(profile.getLocalizedFirstName());
                        userSignupDto.setLastName(profile.getLocalizedLastName());
                        userSignupDto.setMergeAccount(linkedinDto.isMergeAccount());
                        userSignupDto.setLinkedinUserId(profile.getId());

                        Optional<User> optEmailUser = roUserService.getUserByEmail(userSignupDto.getEmail());

                        if (optEmailUser.isPresent()) {
                            Event event = roEventService.getEventByURL(eventUrl);
                            User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
                            return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);
                        } else {
                            if (linkedinDto.getPhoneNumber() != 0) {

                                Optional<User> optPhoneUser = this.getUserBySignupDtoPhoneNumber(userSignupDto);
                                if (optPhoneUser.isPresent() && !linkedinDto.isMergeAccount()) {
                                    UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL;

                                    String msg = optPhoneUser.get().getEmail() != null
                                            ? "This phone number is already associated with another email address "
                                            + optPhoneUser.get().getEmail() + ", Please merge your account. "
                                            : "This phone number is already present in our system, Please merge your account. ";
                                    exceptionMessage.setErrorMessage(msg);
                                    throw new ConflictException(exceptionMessage);
                                }

                                if (linkedinDto.isMergeAccount() && optPhoneUser.isPresent()) {
                                    User user = optPhoneUser.get();
                                    user.setEmail(email);
                                    user.setLinkedinUserId(profile.getId());
                                    user.setFirstName(profile.getLocalizedFirstName());
                                    user.setLastName(profile.getLocalizedLastName());

                                    this.save(user);

                                    Event event = roEventService.getEventByURL(eventUrl);
                                    return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);
                                }
                            }
                        }

                        Event event = roEventService.getEventByURL(eventUrl);
                        User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
                        return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);
                    }else {
                        throw new NotFoundException(NotFound.NOT_VALID_EMAIL);
                    }
                } else {
                    throw new NotFoundException(NotFound.NOT_VALID_LINKEDIN_TOKEN);
                }
            } catch (BaseException e) {
                throw e;
            } catch (Exception exception) {
                throw new NotAcceptableException(exception);
            }
        }
    }
    @Override
	@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
	public AccessTokenModel addUserFacebook(FacebookDto facebookDto, String eventUrl, String ipAddress) {
		try {
			StringBuilder result = getFacebookData(facebookDto.getAccessToken());
			if (result != null) {
				ObjectMapper mapper = new ObjectMapper();
				FaceBookProfile profile = mapper.readValue(result.toString(), FaceBookProfile.class);
				UserSignupDto userSignupDto = new UserSignupDto(profile.getEmail(), null, facebookDto.getPhoneNumber(),
						facebookDto.getCountryCode());
				userSignupDto.setFirstName(profile.getFirst_name());
				userSignupDto.setLastName(profile.getLast_name());
				userSignupDto.setMergeAccount(facebookDto.isMergeAccount());
				userSignupDto.setFacebookUserId(profile.getId());

				Optional<User> optEmailUser = roUserService.getUserByEmail(userSignupDto.getEmail());

				if (optEmailUser.isPresent()) {
                    Event event = roEventService.getEventByURL(eventUrl);
                    User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
                    return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);
				} else {
                    if (facebookDto.getPhoneNumber() != 0) {

                        Optional<User> optPhoneUser = this.getUserBySignupDtoPhoneNumber(userSignupDto);
                        if (optPhoneUser.isPresent() && !facebookDto.isMergeAccount()) {
                            UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL;

                            String msg = optPhoneUser.get().getEmail() != null
                                    ? "This phone number is already associated with another email address "
                                    + optPhoneUser.get().getEmail() + ", Please merge your account. "
                                    : "This phone number is already present in our system, Please merge your account. ";
                            exceptionMessage.setErrorMessage(msg);
                            throw new ConflictException(exceptionMessage);
                        }

                        if (facebookDto.isMergeAccount() && optPhoneUser.isPresent()) {
                            User user = optPhoneUser.get();
                            user.setEmail(profile.getEmail());
                            user.setFbUserId(profile.getId());
                            user.setFirstName(profile.getFirst_name());
                            user.setLastName(profile.getLast_name());

                            this.save(user);

                            Event event = roEventService.getEventByURL(eventUrl);
                            return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);
                        }
                    }
                }

				Event event = roEventService.getEventByURL(eventUrl);
				User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
				return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);

			} else {
				throw new NotFoundException(NotFound.NOT_VALID_FACEBOOK_TOKEN);
			}
		} catch (BaseException e) {
			throw e;
		} catch (Exception exception) {
			throw new NotAcceptableException(exception);
		}
	}

	private StringBuilder getFacebookData(String accessToken) throws IOException {
		StringBuilder url = new StringBuilder(Constants.FACEBOOK_PROFILE_INFO_URL).append(accessToken);

		// HTTP call
		StringBuilder result = null;
		HttpClient client = HttpClientBuilder.create().build();
		HttpGet request = new HttpGet(url.toString());

		HttpResponse response = client.execute(request);
		if (response.getStatusLine().getStatusCode() == Constants.STATUS_CODE_OK) {
			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			result = new StringBuilder();
			String line;
			while ((line = rd.readLine()) != null) {
				result.append(line);
			}
		}
		return result;
	}

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public AccessTokenModel addUserGoogle(GoogleDto googleDto, String eventUrl, String ipAddress) {
        {
            try {
                StringBuilder result = getGoogleData(googleDto.getAccessToken());
                if (result != null) {
                    ObjectMapper mapper = new ObjectMapper();
                    GoogleProfile profile = mapper.readValue(result.toString(), GoogleProfile.class);
                    UserSignupDto userSignupDto = new UserSignupDto(profile.getEmail(), null, googleDto.getPhoneNumber(),
                            googleDto.getCountryCode());
                    userSignupDto.setFirstName(profile.getGiven_name());
                    userSignupDto.setLastName(profile.getFamily_name());
                    userSignupDto.setMergeAccount(googleDto.isMergeAccount());
                    userSignupDto.setGoogleUserId(profile.getId());

                    Optional<User> optEmailUser = roUserService.getUserByEmail(userSignupDto.getEmail());

                    if (optEmailUser.isPresent()) {
                        Event event = roEventService.getEventByURL(eventUrl);
                        User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
                        return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);

                    } else {
                        if (googleDto.getPhoneNumber() != 0) {

                            Optional<User> optPhoneUser = this.getUserBySignupDtoPhoneNumber(userSignupDto);
                            if (optPhoneUser.isPresent() && !googleDto.isMergeAccount()) {
                                UserExceptionConflictMsg exceptionMessage = UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL;

                                String msg = optPhoneUser.get().getEmail() != null
                                        ? "This phone number is already associated with another email address "
                                        + optPhoneUser.get().getEmail() + ", Please merge your account. "
                                        : "This phone number is already present in our system, Please merge your account. ";
                                exceptionMessage.setErrorMessage(msg);
                                throw new ConflictException(exceptionMessage);
                            }

                            if (googleDto.isMergeAccount() && optPhoneUser.isPresent()) {
                                User user = optPhoneUser.get();
                                user.setEmail(profile.getEmail());
                                user.setGoogleUserId(profile.getId());
                                user.setFirstName(profile.getGiven_name());
                                user.setLastName(profile.getFamily_name());

                                this.save(user);

                                Event event = roEventService.getEventByURL(eventUrl);
                                return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);
                            }
                        }
                    }

                    Event event = roEventService.getEventByURL(eventUrl);
                    User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
                    return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,false);

                } else {
                    throw new NotFoundException(NotFound.NOT_VALID_GOOGLE_TOKEN);
                }
            } catch (BaseException e) {
                throw e;
            } catch (Exception exception) {
                throw new NotAcceptableException(exception);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public SocialLoginAccessTokenModel addAdminUserToMicrosoft(MicrosoftDto microsoftDto) {
        log.info("In addAdminUserToMicrosoft");
        try {
            String[] parts = microsoftDto.getAccessToken().split("\\.");
            String jwtData = new String(Base64.getUrlDecoder().decode(parts[1]));
            String email = null;
            String name = null;
            if (StringUtils.isNotEmpty(jwtData)) {
                log.info("Parsing JWT data");
                JSONObject payload = new JSONObject(jwtData);
                log.info("Parsed data: {}", payload);
                if (payload.has(STRING_EMAIL) || payload.has(UPN)) {
                    email = payload.has(STRING_EMAIL) ? payload.getString(STRING_EMAIL) : payload.getString(UPN);
                    log.info("Email is: {}", email);
                    microsoftDto.setEmail(email);
                    if (payload.has(STRING_NAME)){
                        name=payload.getString(STRING_NAME);
                        String[] fullName=name.split(" ");
                        microsoftDto.setFirstName(fullName.length > 0 ? fullName[0] : STRING_EMPTY);
                        microsoftDto.setLastName(fullName.length > 1 ? fullName[1] : STRING_EMPTY);
                    }else {
                        microsoftDto.setFirstName("Anonymous");
                        microsoftDto.setLastName("Anonymous");
                    }
                } else {
                    log.info("Email not found in response");
                    throw new NotFoundException(NotFound.NOT_VALID_MICROSOFT_TOKEN);
                }
                Optional<User> userdetails = roUserService.findOpUserByEmail(email);
                long whiteLabelId = 0;
                if(StringUtils.isNotBlank(microsoftDto.getWlUrl())){
                    log.info("Microsoft wlUrl is: {}", microsoftDto.getWlUrl());
                    Optional<WhiteLabel> optionalWhiteLabel =  roWhiteLabelService.findWhiteLabelByUrlOp(microsoftDto.getWlUrl());
                    if(optionalWhiteLabel.isPresent()){
                        whiteLabelId = optionalWhiteLabel.get().getId();
                    }
                }
                if (userdetails.isPresent()) {
                    log.info("User found with email {}", email);
                    User user = userdetails.get();
                    Event event = roEventService.getEventByHostUserAndMostRecentEventId(user);
                    log.info("User found and returning");
                    return new SocialLoginAccessTokenModel(getAccessTokenModel(event, user, user.getEmail(), null,whiteLabelId,microsoftDto.isLoginFromEntra()), false);
                } else {
                    log.info("No user found with email {}", email);
                    if (microsoftDto.getEmail() != null) {
                        Optional<User> optEmailUser = roUserService.getUserByEmail(microsoftDto.getEmail());
                        if (optEmailUser.isPresent()) {
                            log.info("test log");
                            User user = optEmailUser.get();
                            user.setFirstName(microsoftDto.getFirstName());
                            user.setLastName(microsoftDto.getLastName());
                            this.save(user);
                            Event event = roEventService.getEventByHostUserAndMostRecentEventId(optEmailUser.get());
                            log.info("Returning from here");
                            return new SocialLoginAccessTokenModel(getAccessTokenModel(event, user, microsoftDto.getEmail(), null,whiteLabelId,microsoftDto.isLoginFromEntra()), false);
                        }
                    }
                    log.info("Admin signup dto");
                    AdminSignupDto adminSignupDto = new AdminSignupDto();
                    adminSignupDto.setEmail(microsoftDto.getEmail());
                    adminSignupDto.setSocialSignUp(true);
                    adminSignupDto.setFirstName(microsoftDto.getFirstName());
                    adminSignupDto.setLastName(microsoftDto.getLastName());
                    adminSignupDto.setFromEntra(microsoftDto.isLoginFromEntra());

                    return new SocialLoginAccessTokenModel(this.signUpAdminUserAndReturnAccessTokenModel(adminSignupDto, true), true);
                }
            }else {
                log.info("JWT data is empty");
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ISSUE_WHILE_LOGGIN_THROUGH_MICROSOFT);
            }
        }catch (BaseException e) {
            log.info("Base Exception, reason {}", e.getMessage());
            throw e;
        } catch (Exception exception) {
            log.info("Exception exception, reason {}", exception.getMessage());
            throw new NotAcceptableException(exception);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public AccessTokenModel addUserMicrosoft(MicrosoftDto microsoftDto, String eventUrl, String ipAddress) {
        try {
            String[] parts = microsoftDto.getAccessToken().split("\\.");
            String jwtData = new String(Base64.getUrlDecoder().decode(parts[1]));
            String email = null;
            String name = null;
            if (StringUtils.isNotEmpty(jwtData)) {
                JSONObject payload = new JSONObject(jwtData);
                if (payload.has(STRING_EMAIL)) {
                    email = payload.getString(STRING_EMAIL);
                    microsoftDto.setEmail(email);
                    if (payload.has(STRING_NAME)){
                        name=payload.getString(STRING_NAME);
                        String[] fullName=name.split(" ");
                        microsoftDto.setFirstName(fullName[0]);
                        microsoftDto.setLastName(fullName[1]);
                    }else {
                        microsoftDto.setFirstName("Anonymous");
                        microsoftDto.setLastName("Anonymous");
                    }
                } else {
                    throw new NotFoundException(NotFound.NOT_VALID_MICROSOFT_TOKEN);
                }
                Optional<User> userdetails = roUserService.findOpUserByEmail(email);
                if (userdetails.isPresent()) {
                    User user = userdetails.get();
                    Event event = roEventService.getEventByHostUserAndMostRecentEventId(user);
                    return getAccessTokenModel(event, user, user.getEmail(), null,0L,microsoftDto.isLoginFromEntra());
                } else {
                    if (microsoftDto.getEmail() != null) {
                        Optional<User> optEmailUser = roUserService.getUserByEmail(microsoftDto.getEmail());
                        if (optEmailUser.isPresent()) {
                            User user = optEmailUser.get();
                            user.setFirstName(microsoftDto.getFirstName());
                            user.setLastName(microsoftDto.getLastName());
                            this.save(user);
                            Event event = roEventService.getEventByHostUserAndMostRecentEventId(optEmailUser.get());
                            return getAccessTokenModel(event, user, microsoftDto.getEmail(), null,0L,microsoftDto.isLoginFromEntra());
                        }
                    }
                    UserSignupDto userSignupDto = new UserSignupDto(microsoftDto.getEmail(), null,0L,null);
                    userSignupDto.setEmail(microsoftDto.getEmail());
                    userSignupDto.setFirstName(microsoftDto.getFirstName());
                    userSignupDto.setLastName(microsoftDto.getLastName());
                    Event event = roEventService.getEventByURL(eventUrl);
                    User user = this.signUpBidderUserAndReturnUser(userSignupDto, event, true, true, false, false, false);
                    return getAccessTokenModel(event, user, userSignupDto.getEmail(), userSignupDto.getPassword(),0L,microsoftDto.isLoginFromEntra());
                }
            }else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ISSUE_WHILE_LOGGIN_THROUGH_MICROSOFT);
            }
        }catch (BaseException e) {
            throw e;
        } catch (Exception exception) {
            throw new NotAcceptableException(exception);
        }
    }


    private StringBuilder getGoogleData(String accessToken) throws IOException {
        StringBuilder url = new StringBuilder(Constants.GOOGLE_PROFILE_INFO_URL).append(accessToken);

        // HTTP call
        StringBuilder result = null;
        HttpClient client = HttpClientBuilder.create().build();
        HttpGet request = new HttpGet(url.toString());

        HttpResponse response = client.execute(request);
        if (response.getStatusLine().getStatusCode() == Constants.STATUS_CODE_OK) {
            BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

            result = new StringBuilder();
            String line;
            while ((line = rd.readLine()) != null) {
                result.append(line);
            }
        }
        return result;
    }

    private List<StringBuilder> getLinkedinData(String accessToken,String redirectURL) throws IOException {
        HttpClient clientauth = HttpClientBuilder.create().build();
        String url=LINKEDIN_AUTH2_URL;
        StringBuilder resultauth = null;
        HttpPost requestauth = new HttpPost(url);
        List <NameValuePair> nvps = new ArrayList <NameValuePair>();
        String redirecturlIfNotProvided=uiBaseurl + "/events/linkedin";
        String redirecturl=redirectURL!= null? redirectURL:redirecturlIfNotProvided ;

        nvps.add(new BasicNameValuePair("grant_type", "authorization_code"));
        nvps.add(new BasicNameValuePair("code",accessToken));
        nvps.add(new BasicNameValuePair("client_id",linkedinClientId));
        nvps.add(new BasicNameValuePair("client_secret",linkedinClientSecret));
        nvps.add(new BasicNameValuePair("redirect_uri",redirecturl));
        UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(nvps, "utf-8");
        formEntity.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
        requestauth.setEntity(formEntity);
        HttpResponse responseauth = clientauth.execute(requestauth);
        if (responseauth.getStatusLine().getStatusCode() == Constants.STATUS_CODE_OK) {
            BufferedReader rd = new BufferedReader(new InputStreamReader(responseauth.getEntity().getContent()));
            resultauth = new StringBuilder();
            String line;
            while ((line = rd.readLine()) != null) {
                resultauth.append(line);
            }
        }
        JsonParser json =new JsonParser();
        JsonElement jb= json.parse(resultauth != null ? resultauth.toString() : null);
        String access_token = jb.getAsJsonObject().get("access_token").getAsString();


        String authToken=new StringBuilder(Constants.BEARER).append(access_token).toString();
        List<StringBuilder> list=new ArrayList<>();
        // HTTP call
        StringBuilder result = null;
        StringBuilder result1 = null;

        HttpClient client = HttpClientBuilder.create().build();
        HttpGet request = new HttpGet(LINKEDIN_PROFILE_INFO_URL);
        request.setHeader(HttpHeaders.AUTHORIZATION,authToken);
        HttpResponse response = client.execute(request);
        if (response.getStatusLine().getStatusCode() == Constants.STATUS_CODE_OK) {
            BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

            result = new StringBuilder();
            String line;
            while ((line = rd.readLine()) != null) {
                result.append(line);
            }
        }
        list.add(result);
        HttpClient client1 = HttpClientBuilder.create().build();
        HttpGet request1 = new HttpGet(LINKEDIN_EMAIL_INFO_URL);
        request1.setHeader(HttpHeaders.AUTHORIZATION,authToken);
        HttpResponse response1 = client1.execute(request1);
        if (response1.getStatusLine().getStatusCode() == Constants.STATUS_CODE_OK) {
            BufferedReader rd1 = new BufferedReader(new InputStreamReader(response1.getEntity().getContent()));

            result1 = new StringBuilder();
            String line;
            while ((line = rd1.readLine()) != null) {
                result1.append(line);
            }
        }
        list.add(result1);
        return list;
    }

	private AccessTokenModel getAccessTokenModel(Event event, User user, String email, String password,long wlId,boolean isLoginUsingSso) {
		List<String> userRoles = roUserService.getUserRoles(user);
		if (null != userRoles && !userRoles.isEmpty()) {
            log.info("Found user roles size {} for the User {}", userRoles.size(), user.getUserId());
			return new AccessTokenModel(email, password, userRoles, user, true, event,
					roStaffService.getStaffRole(event, user),wlId,isLoginUsingSso);
		} else {
            log.info("User roles are not found for user {} with the Email {}", user.getUserId(), email);
			throw new NotFoundException(USER_NOT_FOUND);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
	public User getUserForStaffCheckout(String email, String phonenumber, String countryCode, Event event) {
		UserSignupDto userSignupDto = new UserSignupDto(email, null,
				StringUtils.isBlank(phonenumber) ? 0 : Long.parseLong(phonenumber), countryCode);
		return this.signUpBidderUserAndReturnUser(userSignupDto, event, false, false, false, true, false);
	}

	@Override
	public boolean isUserAdminOfAnyWhiteLabelEvent(User user) {
		Set<Event> events = eventService.getEventsByUserAndWhiteLabelNotNull(user);
		for (Event event : events) {
			if (event.getWhiteLabel() != null) {
				return true;
			}
		}
		return false;
	}

	private void updateFirstNameLastName(User user, String firstName, String lastName) {
		boolean isChanged = false;
		if (StringUtils.isNotBlank(firstName)) {
			user.setFirstName(firstName);
			isChanged = true;
		}
		if (StringUtils.isNotBlank(lastName)) {
			user.setLastName(lastName);
			isChanged = true;
		}
		if (isChanged) {
			this.save(user);
		}
	}

	// Do not touch this method, Please discuss with Team, before changing anything related to user merge
	@Override
	@Transactional(rollbackFor = ConflictException.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
	public User mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(User user, String countryCode, String phoneNumber,
			String email, CheckOutUserDetail checkOutUserDetail) {
		user = handleUserMerge(user, countryCode, phoneNumber, email);
		if(checkOutUserDetail!=null){
			this.saveAddressAndName(user, checkOutUserDetail);
		}
		return user;
	}

	private void saveAddressAndName(User user, CheckOutUserDetail checkOutUserDetail) {
		this.saveAddress(user, checkOutUserDetail);
		updateFirstNameLastName(user, checkOutUserDetail.getFirstName(), checkOutUserDetail.getLastName());
	}

	// only use for bidder
	@Override
	public void saveAddress(User user, AddressDto addressDto) {
		boolean isChanged = false;

		if (StringUtils.isNotBlank(addressDto.getAddress1())) {
			user.setAddress1(addressDto.getAddress1());
			isChanged = true;
		}
		if (StringUtils.isNotBlank(addressDto.getAddress2())) {
			user.setAddress2(addressDto.getAddress2());
			isChanged = true;
		}
		if (StringUtils.isNotBlank(addressDto.getCity())) {
			user.setCityOrProvidence(addressDto.getCity());
			isChanged = true;
		}
		if (StringUtils.isNotBlank(addressDto.getState())) {
			user.setState(addressDto.getState());
			isChanged = true;
		}
		if (StringUtils.isNotBlank(addressDto.getZipcode())) {
			user.setZipcode(addressDto.getZipcode());
			isChanged = true;
		}
		if (StringUtils.isNotBlank(addressDto.getCountry())) {
			user.setCountry(addressDto.getCountry());
			isChanged = true;
		}
		if (isChanged) {
			this.save(user);
		}
	}

	@Override
	public void sendCode(PhoneNumberDto phoneNumberDto, String eventUrl) {
		OTPGenerator otpGenerator = new OTPGenerator(6);
		String otpCode = otpGenerator.nextString();

		AccelEventsPhoneNumber phoneNumber = phoneNumberService.getAePhoneNumber(phoneNumberDto);

		Event event = roEventService.getEventByURL(eventUrl);

		String message = "Your Accelevents temporary verification code is : " + otpCode
				+ ". This code will expire in 24 hours. Thanks!";

		try {
			Long senderPhoneNumber = com.accelevents.utils.NumberUtils.isNumberGreaterThanZero(event.getPhoneNumber()) ? event.getPhoneNumber() : Long.parseLong(phoneNumberConfiguration.getPhoneNumber());
			CountryCode cc = com.accelevents.utils.NumberUtils.isNumberGreaterThanZero(event.getPhoneNumber()) ? event.getCountryCode() : CountryCode.US;
			AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(cc, senderPhoneNumber);
			textMessageService.sendText(accelEventsPhoneNumber, phoneNumber, message);
			cacheStoreService.set(getKey(phoneNumberDto), otpCode, 24, TimeUnit.HOURS);
		} catch (TwilioException e) {
			log.error("Exception Occurred while sending message : " , e);
			throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR.value(),
					String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
					Constants.MESSAGE_SERVICE_UNABLE_TO_SEND_MESSAGE, Constants.MESSAGE_SERVICE_UNABLE_TO_SEND_MESSAGE, null, null);
		}
	}

	private String getKey(PhoneNumberDto phoneNumberDto) {
		return phoneNumberDto.getCountryCode().toString() + phoneNumberDto.getPhoneNumber();
	}

	public void validateOtp(PhoneNumberDto phoneNumberDto, String code) {
		Object object = cacheStoreService.get(getKey(phoneNumberDto));
		if (object != null && !code.equals(object)) {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.CODE_NOT_MATCHED);
		}
	}

	@Override
	public SmsEventUserInfoDto getSmsUserInfoDetail(User user, Event event) {
		return new SmsEventUserInfoDto(this.isCCRequired(ModuleType.AUCTION, user, event, false),
				this.extractUserInfoForEvent(user, event, true,null), this.eventService.getStripeKey(event));
	}

	@Override
	@Transactional
	public void addUserAddressAndCard(User user, Event event, UserAddressCardDto useAddressCardDetail) {
		this.eventService.addUserCard(event, user, useAddressCardDetail.getNewToken(), true, false);
		this.saveAddress(user, useAddressCardDetail);
	}

	@Override
	public AccessTokenModel getAccessTokenModel(UnsubscribeInfoDto unsubscribeInfoDto) {
		Optional<User> userOptional = roUserService.getUserByEmail(unsubscribeInfoDto.getEmail());
		Event event = roEventService.getEventById(unsubscribeInfoDto.getEventId());
		return userOptional.map(user -> getUserDetailForUnsubscribe(user, event)).orElse(null);
	}

	@Override
	public UserProfileMapping getUserActivityDetailsByEvent(Event event, User user) {
		return buildUserProfileMapping(event, user);
	}

	private UserProfileMapping buildUserProfileMapping(Event event, User user) {
		UserProfileMapping userProfileMapping = new UserProfileMapping();
		userProfileMapping.setEventName(event.getName());
		userProfileMapping.setEventId(event.getEventId());
		userProfileMapping.setEventUrl(event.getEventURL());
		userProfileMapping.setCurrency(event.getCurrency().getSymbol());

		List<EventTicketsDto> eventTicketsDtos = eventTicketsRepoService.findEventTicketsByEventAndStatus(event, 0l, user);
		if(!eventTicketsDtos.isEmpty())userProfileMapping.addEventTicketsDto(eventTicketsDtos);

		List<SilentActivity> auctionBidList = auctionBidService.findMaxBidForItemByUserAndAuctionId(user.getUserId(), event.getAuctionId());
		if(!auctionBidList.isEmpty())userProfileMapping.addSilentActivitys(auctionBidList);

		List<RaffleActivity> purchasedRaffleTicketList = purchasedRaffleTicketService.findTotalTicketByItemForUserAndRaffleId(event.getRaffleId(), user);
		if(!purchasedRaffleTicketList.isEmpty())userProfileMapping.addPurchasedRaffleActivitys(purchasedRaffleTicketList);

		List<RaffleActivity> raffleTicketList = submittedRaffleTicketService.findTotalTicketByItemForUserAndRaffleId(event.getRaffleId(), user);
		if(!raffleTicketList.isEmpty())userProfileMapping.addRaffleActivitys(raffleTicketList);

		userProfileMapping.setAvailableRaffleTickets(getAvailableRaffleTickets(user, event.getRaffleId()));

		List<CauseAuctionActivity> pledgeList = pledgeService.findByPaidPledgeAmountForEachItem(user,event.getCauseAuctionId());
		if(!pledgeList.isEmpty())userProfileMapping.addCauseAuctionActivitys(pledgeList);

		userProfileMapping.setSilentAuctionEnabled(event.isSilentAuctionEnabled());
		userProfileMapping.setCauseAuctionEnabled(event.isCauseAuctionEnabled());
		userProfileMapping.setRaffleEnabled(event.isRaffleEnabled());
		userProfileMapping.setDonationEnabled(event.isDonationEnabled());
		userProfileMapping.setTextToGiveEnabled(event.isTextToGiveEnabled());
		return userProfileMapping;
	}

	@Override
	@Transactional(rollbackFor = {
			ConflictException.class }, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
	public User updateUserProfile(UserProfileUpdateDto updateDto, User user, Event event) {
		String countryCode = updateDto.getCountryCode();
		CountryCode CC =  StringUtils.isNotBlank(countryCode) ? CountryCode.valueOf(StringUtils.upperCase(countryCode)) : CountryCode.US;
		Long phoneNumber = updateDto.getPhoneNumber();
		boolean flag = false;
        String oldSpeakerEmail = null;

		validateUserPhoneNumber(CC, phoneNumber.toString(), user, false);

        if (StringUtils.isNotBlank(user.getEmail()) && StringUtils.isNotBlank(updateDto.getEmail())) {
            if (!user.getEmail().equalsIgnoreCase(updateDto.getEmail())) {
                if(StringUtils.isBlank(user.getPassword())){
                    this.validateProfileUpdateAuthenticationCode(user.getUserId(), updateDto.getAuthCode());
                } else if(StringUtils.isBlank(updateDto.getPassword()) || !this.passwordEncoder.matches(updateDto.getPassword(), user.getPassword())) {
                    log.info("updateUserProfile to update email from User Email {} to updated Email {} and password are invalid.", user.getEmail(),updateDto.getEmail());
                    throw new ConflictException(UserExceptionConflictMsg.INCORRECT_PASSWORD);
                }
                validateUserNotPresentInDB(updateDto.getEmail());
                oldSpeakerEmail = user.getEmail();
                flag = true;
            }

            user.setEmail(updateDto.getEmail());
            user.setAddress1(updateDto.getAddress1());
            user.setAddress2(updateDto.getAddress2());
            user.setState(updateDto.getState());
            user.setCityOrProvidence(updateDto.getCityOrProvidence());
            user.setCountry(updateDto.getCountry());
            user.setZipcode(updateDto.getZipcode());
            user.setFirstName(updateDto.getFirstName());
            user.setLastName(updateDto.getLastName());
            log.info("updateUserProfile | User Name | " + user.getFirstName() + " " + user.getLastName() + " | User Email | " + user.getEmail() + "");
        } else {
            log.info("updateUserProfile | User Email | " + user.getEmail() + " | UpdateDto Email | " + updateDto.getEmail() + "");
            throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_VALID_EMAIL);
        }
		User updatedUser =this.save(user);
        ticketHolderEditAttributesService.updateUserEventTicketAndOrderDetails(updatedUser);
        afterTaskIntegrationTriggerService.onAttendeeUserProfileUpdateEvent(updatedUser, event);
        if(flag){
            speakerService.updateSpeakerEmailAndSendEmailToEventAdmin(oldSpeakerEmail,updatedUser.getEmail());
        }
        return  updatedUser;
	}

    private void validateProfileUpdateAuthenticationCode(Long userId, String authCode) {
        Object authCodeFromDb = cacheStoreService.get(USER_PROFILE_UPDATE_AUTH_CODE_PREFIX + userId);
        if (authCodeFromDb == null) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_EXPIRE);
        } else if (!authCodeFromDb.equals(authCode)) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_NOT_MATCH);
        }
        cacheStoreService.delete(USER_PROFILE_UPDATE_AUTH_CODE_PREFIX + userId);
    }

    public User updateUserPhoneNumber(User user) {
	    return this.save(user);
    }

	private void validateUserNotPresentInDB(String email) {
		log.info("validateUserNotPresentInDB|email|{}",email);
		Optional<User> oldUserOptional = roUserService.findOpUserByEmail(email);
		if (oldUserOptional.isPresent()) {
			throw new ConflictException(UserExceptionConflictMsg.EMAIL_ALREADY_REGISTERED);
		}
	}

//	private UserProfileMapping buildUserProfileMapping(Event event, User user) {
//		UserProfileMapping userProfileMapping = new UserProfileMapping();
//		userProfileMapping.setEventName(event.getName());
//		userProfileMapping.setEventId(event.getEventId());
//		userProfileMapping.setEventUrl(event.getEventURL());
//
//		List<SilentActivity> auctionBidList = auctionBidService
//				.findMaxBidForItemByUserAndAuctionId(user.getUserId(), event.getAuctionId());
//		for (SilentActivity auctionBid : auctionBidList) {
//			userProfileMapping.addSilentActivitys(auctionBid);
//		}
//
//		List<RaffleActivity> raffleTicketList = submittedRaffleTicketService
//				.findTotalTicketByItemForUserAndRaffleId(event.getRaffleId(), user);
//		for (RaffleActivity submittedRaffleTicket : raffleTicketList) {
//			userProfileMapping.addRaffleActivitys(submittedRaffleTicket);
//		}
//
//		userProfileMapping.setAvailableRaffleTickets(getAvailableRaffleTickets(user, event.getRaffleId()));
//
//		List<CauseAuctionActivity> pledgeList = pledgeService.findByPaidPledgeAmountForEachItem(user,
//				event.getCauseAuctionId());
//		for (CauseAuctionActivity pledge : pledgeList) {
//			userProfileMapping.addCauseAuctionActivitys(pledge);
//		}
//		return userProfileMapping;
//	}

	private void validateUserPhoneNumber(CountryCode CC, String phoneNumberString, User user, boolean throwError) {
		Long phoneNumber = Long.valueOf(phoneNumberString);
		Optional<User> existingUserOpt = roUserService.getUserByAEPhoneNumber(new AccelEventsPhoneNumber(CC, phoneNumber));
		if (existingUserOpt.isPresent()) {
			User existingUser = existingUserOpt.get();
			if (existingUser.getUserId().equals(user.getUserId())) {
				if (phoneNumber == existingUser.getPhoneNumber() && throwError) {
					throw new ConflictException(UserExceptionConflictMsg.NO_CHANGES_MADE);
				} else {
					user.setPhoneNumber(phoneNumber);
					user.setCountryCode(CC);
				}
			} else if (existingUser.getEmail() == null) {
				this.mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(user, CC.name(), phoneNumberString, null, null);
			} else {
                throwErrorAlreadyEmailExists(existingUser, null,null,0l);
			}
		} else {
			user.setPhoneNumber(phoneNumber);
			user.setCountryCode(CC);
		}
	}

//	private void validateUserPhoneNumber(CountryCode CC, String phoneNumberString, User user, boolean throwError) {
//		if (phoneNumberString.length() != 10) {
//			throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_VALID_PHONE_NUMBER);
//		}
//		Long phoneNumber = Long.valueOf(phoneNumberString);
//		Optional<User> existingUserOpt = roUserService.getUserByAEPhoneNumber(new AccelEventsPhoneNumber(CC, phoneNumber));
//		if (existingUserOpt.isPresent()) {
//			User existingUser = existingUserOpt.get();
//			if(existingUser.getUserId().equals(user.getUserId())) {
//				if(phoneNumber == existingUser.getPhoneNumber() && throwError ) {
//					throw new ConflictException(UserExceptionConflictMsg.NO_CHANGES_MADE);
//				} else {
//					user.setPhoneNumber(phoneNumber);
//					user.setCountryCode(CC);
//				}
//			}else if(existingUser.getEmail()== null){
//				this.mergeHostAndPhoneNumberUserAndEmailAndUpdateAddress(user,CC.name(),phoneNumberString,null, null);
//			}
//
//			else {
//				throw new ConflictException(UserExceptionConflictMsg.USER_ALREADY_EXIST);
//			}
//		} else {
//			user.setPhoneNumber(phoneNumber);
//			user.setCountryCode(CC);
//		}
//	}

	@Override
	public List<Object[]> getUserDetailsForNeonAccount(Event event) {
		return userRepository.findAllUserDetailsForEvent(event.getEventId());
	}

	@Override
	public Set<User> findUserByEmailIn(Set<String> emails) {
		return userRepository.findUserByEmailIn(emails);
	}

	@Override
	@Transactional
	public void validateOtpAndRemovePhoneNumber(PhoneNumberDto phoneNumberDto, String code) {
		validateOtp(phoneNumberDto,code);
        List<User> userList = roUserService.findUserByPhoneNumberCountryCodeList(phoneNumberDto.getPhoneNumber(), phoneNumberDto.getCountryCode());
        if (!userList.isEmpty()) {
            Optional<User> userOpt = userList.stream().findFirst();
            User user = userOpt.get();
            user.setPhoneNumber(0);
            userRepository.save(user);
        }
	}


	@Override
	public List<User> getUserByFromTo(long from,long to){
		return userRepository.getUserByFromTo(from,to);
	}

	@Override
	public User getUserByEmailOrCellPhone(String emailOrCell, Optional<String> countryCode, Event event) {
		checkCountryCodeIsValid(countryCode);
		String emailCell = emailOrCell.trim();
		if (countryCode.isPresent() && NumberUtils.isNumber(emailOrCell.trim())) {
			emailCell = Constants.STRING_PLUS + CountryCode.valueOf(StringUtils.upperCase(countryCode.get())).getCode()
					+ emailOrCell.trim();
		}
		Optional<User> optionalUser = roUserService.getUserByPhoneOrEmail(emailCell);
		if (!optionalUser.isPresent() && countryCode.isPresent()) {
			PhoneNumberDto dto = new PhoneNumberDto(countryCode.get(), emailOrCell);
			AccelEventsPhoneNumber phone = new AccelEventsPhoneNumber(dto.getCountryCode(), dto.getPhoneNumber());
			optionalUser = roUserService.getUserByAEPhoneNumber(phone);
		}

		if (!optionalUser.isPresent()) {
			throw new NotFoundException(USER_NOT_FOUND);
		} else {
			return optionalUser.get();
		}
	}

	private void checkCountryCodeIsValid(Optional<String> countryCode) {
		if (countryCode.isPresent() && !Arrays.stream(CountryCode.values()).anyMatch(countryCode1 -> countryCode1.name().equalsIgnoreCase(countryCode.get()))) {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.COUNTRY_CODE_NOT_VALID);
		}
	}

	@Override
	public boolean validatePasswordRestCode(PasswordResetCodeDto passwordResetCodeDto) {
		Optional<User> user = roUserService.findOpUserByEmail(passwordResetCodeDto.getUserEmail());
		if (user.isPresent()) {
			Object object = cacheStoreService.get("PASSWORD_RESET_CODE_" + user.get().getUserId());
			if (object == null) {
				throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_EXPIRE);
			} else if (!passwordResetCodeDto.getPasswordResetCode().equals(object)) {
				throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_NOT_MATCH);
			}
			return true;
		}
		return false;
	}

    @Override
    public boolean validateTwoFactorCode(User user, TwoFactorCodeDto twoFactorCodeDto, String authToken) {
        Object object = cacheStoreService.get(TWO_FACTOR_CODE_USERID_PREFIX + user.getUserId());
        if (object == null || !twoFactorCodeDto.getTwoFactorCode().equals(object)) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.TWO_FACTOR_CODE_EXPIRE_OR_INVALID);
        }
        cacheStoreService.delete(TWO_FACTOR_CODE_USERID_PREFIX+user.getUserId());
        return true;
    }

	@Override
    public User updateUserProfilePhoto(User user, String photoUrl ,Event event) {
        List<Speaker> speakers = speakerService.findSpeakerByEmail(user.getEmail());
        if (!CollectionUtils.isEmpty(speakers)) {
            speakers.stream().filter(Speaker::getAllowOverrideDetails).forEach(speaker -> speaker.setImageUrl(photoUrl));
            speakerService.saveAll(speakers);
        }
        if (StringUtils.isEmpty(photoUrl)) {
            if (speakers.size() > 0) {
                User userInfo = new User();
                Speaker speaker = speakers.get(0);
                userInfo.setUserId(speaker.getUserId());
                userInfo.setFirstName(speaker.getFirstName());
                userInfo.setLastName(speaker.getLastName());
                userInfo.setPhoto(speaker.getImageUrl());
                getStreamService.updateChatUser(speaker.getAllowOverrideDetails() ? user : userInfo, false, null, event.getEventId());
            } else {
                getStreamService.updateChatUser(user, true, null, event.getEventId());
            }
        }
        user.setPhoto(photoUrl);
        log.info("updateUserProfilePhoto | user : {}",user);
        User updatedUser = save(user);
        afterTaskIntegrationTriggerService.onAttendeeUserProfileUpdateEvent(updatedUser, event);
        return updatedUser;
    }

    @Override
    public User updateProfilePicForParticularUser(User loggedUser, String photoUrl, Event event, Long userId) {
        User user = userService.findByUserId(userId);
        log.info("Request received from kiosk to update profile pic by user {} | for user : {} | old profile pic {}",loggedUser.getUserId(), user.getUserId(), user.getPhoto());
        user.setPhoto(photoUrl);
        User updatedUser = save(user);
        log.info("successfully updated profile image {} for user {} by uesr {}", updatedUser.getPhoto(), updatedUser.getUserId(), loggedUser.getUserId());
        afterTaskIntegrationTriggerService.onAttendeeUserProfileUpdateEvent(updatedUser, event);
        return updatedUser;
    }

    @Override
    public User uploadUserCoverPhoto(User user, String coverPhotoUrl, Event event) {
    user.setCoverPhoto(coverPhotoUrl);
    log.info("uploadUserCoverPhoto | coverPhotoUrl  {} ",coverPhotoUrl);
    if (event == null){
        throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
    }
    return userService.save(user);
    }

	@Override
	public User findById(Long userId) {
		return roUserService.findByIdOp(userId).orElse(null);
	}

	@Override
	public AttendeeProfileDto getUserInfo(Long userId) {
		User user = roUserService.getUserById(userId).orElseThrow(() -> new NotFoundException(USER_NOT_FOUND));
		AttendeeProfileDto attendeeProfileDto = new AttendeeProfileDto();
		attendeeProfileDto.setFirstName(user.getFirstName());
		attendeeProfileDto.setLastName(user.getLastName());
		return attendeeProfileDto;
	}

	@Override
	public List<BasicUserDto> findCountryCodeByByEmailIn(List<String> emailList) {
		if(CollectionUtils.isEmpty(emailList)){
			return Collections.emptyList();
		}
		return userRepository.findByEmailIn(emailList);
	}

	@Override
	public Long findUserIdByEmail(String holderEmail) {
		return userRepository.findUserIdByEmail(holderEmail);
	}

	@Override
	public List<BigInteger> findAttendeeByEvent(Event event, int size, int page){
        int from = page * size;
        return userRepository.findAttendeeByEvent(event.getEventId(), from, size);
	}

	@Override
	public List<BigInteger> findAttendeeIdsByEvent(Long eventId){
		return userRepository.findAttendeeIdsByEvent(eventId);
	}

    @Override
    public List<BigInteger> findAttendeeIdsBySearchAndByEvent(Long eventId, String searchString) {
	    if(StringUtils.isNotBlank(searchString)){
            searchString = searchString.trim().replaceAll(" +", " ");
        }
        return userRepository.findAttendeeIdsBySearchAndByEvent(eventId, searchString);
    }

    @Override
    public void validatePassword(String confirmPassword) {
        Pattern pattern = Pattern.compile(PASSWORD_VALIDATION);
        Matcher matcher = pattern.matcher(confirmPassword);
        if(!matcher.matches()){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.PASSWORD_VALIDATION_FAILED);
        }
    }

    public AccessTokenModel getOrCreateUserForSSO(SSOUserDTO ssoUserDTO) {
        log.info("get or create user for the SSO login first name: {} last name: {} and email: {}",ssoUserDTO.getFirstName(),ssoUserDTO.getLastName(),ssoUserDTO.getEmail());
        Optional<User> objUserOptional = roUserService.getUserByPhoneOrEmail(ssoUserDTO.getEmail());
        WhiteLabel whiteLabel = ssoUserDTO.getWhiteLabel();
        log.info("getOrCreateUserForSSO | user is found {}", objUserOptional.isPresent());
        User objUser = objUserOptional.orElseThrow(() -> new NotFoundException(USER_NOT_FOUND));
        UserLoginDto loginDto = new UserLoginDto(objUser.getEmail(),objUser.getPassword());
        AccessTokenModel accessTokenModel = this.roUserService.getWhiteLabelUserDetailForLogin(loginDto, whiteLabel.getWhiteLabelUrl(),true, true);
        if (accessTokenModel.getUserRoles() != null && !accessTokenModel.getUserRoles().isEmpty()) {
            log.info("found the user roles for user: {}",accessTokenModel.getUsername());
            return accessTokenModel;
        }
        throw new NotFoundException(USER_ROLE_NOT_FOUND);
    }




	@Override
	@Transactional
	public void updateUserCardRecStatus(User user, Event event, String cardId) throws StripeException {
		this.eventService.updateUserCardStatus(event, user, cardId);
	}


    @Override
    public FirstLastNameDto getUserInfoByEmail(String email) {
        log.info("getUserByEmail|email|{}",email);
        return userRepository.getUserInfoByEmail(email);
    }

    @Override
    public List<BasicUserEventBillingDto> findByUserIdIn(List<Long> userIds) {
        return CollectionUtils.isEmpty(userIds) ? Collections.emptyList() : userRepository.findByUserIds(userIds);
    }

    @Override
    public UserCheckDto checkUserAndPasswordArePresent(String email) {
        String userEmail = email.trim();
        Optional<User> optionalUser = roUserService.getUserByEmail(userEmail);
        boolean isUserPresent = optionalUser.isPresent();
        boolean isPasswordPresent = false;
        if (isUserPresent) {
            isPasswordPresent = null != optionalUser.get().getPassword();
        }
        return new UserCheckDto(isUserPresent, isPasswordPresent);
    }

	@Override
	public Page<User> getAllAttendeeByEvent(Long eventId, String searchString, int page, int size) {
        return userRepository.getAllAttendeeByEvent(eventId, StringUtils.trimToEmpty(searchString), PageRequest.of(page, size));
    }

    @Override
    public Page<User> getAllAttendeeByEvent(Long eventId, String searchString, int page, int size, String sortParam) {
        return userRepository.getAllAttendeeByEvent(eventId, StringUtils.trimToEmpty(searchString), sortParam, PageRequest.of(page, size));
    }

    @Override
    public Page<Object[]> getAllAttendeesByTicketType(Long eventId, String searchString, List<Long> ticketTypeIds, int page, int size) {
        return userRepository.getAllAttendeesByTicketType(eventId, StringUtils.trimToEmpty(searchString), ticketTypeIds, PageRequest.of(page, size));
    }

    @Override
    public boolean validateMagicLinkCode(MagicLinkCodeDto magicLinkCodeDto) {
        Optional<User> user = roUserService.findOpUserByEmail(magicLinkCodeDto.getUserEmail());
        if (user.isPresent()) {
            Object object = cacheStoreService.get("MAGIC_LINK_CODE_" + user.get().getUserId());
            if (object == null) {
                throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_EXPIRE);
            } else if (!magicLinkCodeDto.getMagicLinkCode().equals(object)) {
                throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_NOT_MATCH);
            }
            return true;
        }
        return false;
    }

    @Override
    public String validatePhoneNumberAndEmail(String body,AccelEventsPhoneNumber phoneNumber,Event event){
        String response;
        log.info("userServiceImpl | validatePhoneNumberAndEmail Email {} phoneNumber {}",body,phoneNumber);
        if(StringUtils.isNotBlank(body.trim()) && isValidEmailAddress(body.trim())){
          User user =  userRepository.findUserByEmailAndPhoneNumberAndCountryCode(phoneNumber.getPhoneNumber(),phoneNumber.getCountryCode(),body);
              if(null != user){
                  EmailMessage emailMessage = new EmailMessage(TemplateId.CONFIRM_EMAIL);
                  emailMessage.setSubject(CONFIRM_EMAIL_SUBJECT);
                  emailMessage.setTemplateName(TemplateId.CONFIRM_EMAIL.getValue());
                  emailMessage.setWhiteLabel(event.getWhiteLabel());
                  Map<String, Object> substitutionMap = new HashMap<>();
                  StringBuilder emailBody=new StringBuilder();
                  emailBody.append("Dear ").append(null == user.getFirstName()? "User" : user.getFirstName()).append(" ").append(null == user.getLastName()? " ":user.getLastName()).append(",<br>please confirm your email address by click on given confirm email link");
                  substitutionMap.put("confirm_url",getConfirmEmailUrl(user.getPhoneNumber(),user.getEmail(),user.getCountryCode(), event));
                  substitutionMap.put("body_of_query",emailBody);
                  substitutionMap.put("requestType", "Confirm Email Request");
                  setImageUrl(event, substitutionMap);
                  substitutionMap.put(Constants.EVENT_ID_UPPERCASE, event.getEventId());
                  emailMessage.setSubstitutionData(substitutionMap);
                  Set<String> receivers = new HashSet<>();
                  receivers.add(user.getEmail());
                  emailService.sendTemplateMail(emailMessage, receivers);
                  response=CONFIRM_NUMBER_EMAIL_SENT;
             } else {
                  response=NO_ACCOUNT_FOUND;
              }
        } else {
            response=INVALID_EMAIL;
        }
        return response;
    }

    private void setImageUrl(Event event, Map<String, Object> substitutionMap) {
        if (event.getWhiteLabel() != null && event.getWhiteLabel().getLogoImage() != null) {
            substitutionMap.put(IMAGE_URL, imageConfiguration.getImagePrefix().concat(event.getWhiteLabel().getLogoImage()));
        } else {
            substitutionMap.put(IMAGE_URL, imageConfiguration.getImagePrefix().concat(EMAIL_TEMPLATE_IMAGES).concat(imageConfiguration.getImagesAcceleventlogo()));
        }
    }

    private String getConfirmEmailUrl(long phoneNumber, String email,CountryCode code,Event event) {
        StringBuilder confirmUrl=new StringBuilder();
        confirmUrl.append(serviceHelper.getEventBaseUrl(event)).append("/u/confirmEmail?key=").append(encode(EMAIL + "="+email +"&"+PHONE_NUMBER_KEY+"=" + phoneNumber+"&"+COUNTRY_CODE_KEY+"="+code.toString())) ;
        return StringUtils.replace(CONFIRM_EMAIL_ADDRESS,"[confirm_url]",confirmUrl.toString());
    }

    @Override
    @Transactional
    public int updatePhoneNumber(ConfirmEmailInfoDto confirmEmailInfoDto){
        log.info("userServiceImpl | updatePhoneNumber confirmEmailInfoDto {} ",confirmEmailInfoDto);
        try {
            return userRepository.updatePhoneNumber(confirmEmailInfoDto.getEmail(), confirmEmailInfoDto.getPhoneNumber(),CountryCode.valueOf(StringUtils.upperCase(confirmEmailInfoDto.getCode())));
        } catch (Exception e){ //NOSONAR
            log.info("userServiceImpl|updatePhoneNumber confirmEmailInfoDto {} error {}",confirmEmailInfoDto,e.getMessage());
            throw new NotAcceptableException(e);
        }
    }
    @Override
    public List<Object[]> findUserNameByUserIdIn(List<Long> userIds) {
        return userRepository.findUserNameByUserIdIn(userIds);
    }

    @Override
    public void sendMagicLinkToRegisterAttendee(Event event, String email) {
        if(StringUtils.isBlank(email)){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.PASS_EMAIL_ADDRESS);
        }

        User user = roUserService.findByEmail(email);
        if(null == user){
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
        sendGridMailPrepareService.sendMailWithMagicLink(user, AutoLoginCreationLocation.PASSWORD_CREATION_EMAIL, event);
    }
    private void setImageUrl(Map<String, Object> substitutionMap, WhiteLabel whiteLabel) {
        if(whiteLabel != null && whiteLabel.getLogoImage() != null){
            substitutionMap.put(IMAGE_URL, emailImageHelper.getEmailLogoImageForBeeFree(imageConfiguration.getImagePrefix().concat(whiteLabel.getLogoImage())));
        } else {
            substitutionMap.put(IMAGE_URL, emailImageHelper.getEmailLogoImageForBeeFree(null));
        }
    }

    @Override
    @Transactional
    public AccessTokenModel signUpApiUserAndReturnAccessTokenModel(AdminSignupDto adminSignupDto, List<Event> eventList, WhiteLabel whiteLabel, Organizer organizer) {

        User user = this.signUpApiUserAndReturnApiUser(this.createUserBySignupDto(adminSignupDto), eventList, true,
                false);
        if(whiteLabel!=null){
            apiKeyService.addWhiteLabelStaff(whiteLabel, user);
            apiKeyService.associateApiUserForWhiteLabelOrganizer(user, whiteLabel);
        }
        if(organizer!=null) {
            apiKeyService.associateApiUserWithOrganization(user, organizer, null);
        }
        List<String> userRoles = roUserService.getUserRoles(user);
        AccessTokenModel accessTokenModel = null;
        if (userRoles != null && !userRoles.isEmpty()) {
            accessTokenModel = new AccessTokenModel(
                    StringUtils.isBlank(user.getEmail()) ? user.getDisplayNumber() : user.getEmail(),
                    user.getPassword(), userRoles, user, true, !CollectionUtils.isEmpty(eventList) ? eventList.get(0) : null, Collections.singletonList(StaffRole.admin),
                    Optional.ofNullable(whiteLabel).map(WhiteLabel::getId).orElse(null),
                    Optional.ofNullable(organizer).map(Organizer::getId).orElse(null));
        } else {
            log.info("User Not Found of this email "+adminSignupDto.getEmail());
        }
        return accessTokenModel;
    }

    public User signUpApiUserAndReturnApiUser(User user, List<Event> eventList, boolean isSignup, boolean isSocialSignup) {
        if (isSignup && !isSocialSignup) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        user.setMostRecentEventId(!CollectionUtils.isEmpty(eventList)?eventList.get(0).getEventId():0);
        User savedUser = this.save(user);
        if(!CollectionUtils.isEmpty(eventList)) {
            apiKeyService.createApiUserInStaffForEventList(savedUser, eventList, StaffRole.admin);
        }
        this.addUserRole(savedUser, ROLE_ADMIN.name());
        return savedUser;
    }

    @Override
    public User findUserByIdWithoutCache(Long userId) {
        return userRepository.findUserByUserId(userId);
    }

    @Override
    public void updateShowProfile(User user, boolean showProfile) {
        user.setShowProfile(showProfile);
        this.save(user);
    }


    @Override
    public List<User> findAttendeesByEventAndUserIds(Long eventId, List<Long> userIds) {
        return !CollectionUtils.isEmpty(userIds) ? userRepository.findAttendeesByEventAndUserIds(eventId, userIds) : Collections.emptyList();
    }



 @Override
 public boolean isOrganizerSuperAdminRole(User user , List<String> userRoles){
        return this.verifyUserRoleExists(user, userRoles, ROLE_ORGANIZER_SUPER_ADMIN);
}

/**
 This method is used to set a static password on a database for automation users because without a fixed password
 now we are not able to log in, earlier we were able to log in with any password the first time,
 so in automation, we are creating new users then setting passwords using this method.
*/
    @Override
    public void setPasswordForUserEmails(String emails){
	    try {
            log.info("Automation password set emails: {}", emails);
            List<String> emailsList = GeneralUtils.convertCommaSeparatedToList(emails);
            emailsList.forEach(e ->
                    roUserService.findOpUserByEmail(e).ifPresent(user -> {
                        user.setPassword(passwordEncoder.encode("Hello@123")); //NOSONAR
                        userRepository.save(user);
                        log.info("Automation user password saved {}", user.getEmail());
                    })
            );
        }catch (Exception e){
	        log.error("Automation users password exception {}",Arrays.toString(e.getStackTrace()));
        }
    }

    @Override
    public List<Object[]> getUsersGroupByTicketTypeId(Long eventId, List<Long> ticketTypeIds) {
       return CollectionUtils.isEmpty(ticketTypeIds) ? Collections.emptyList() : userRepository.getUsersGroupByTicketTypeId(eventId, ticketTypeIds);
    }

    public AddressDto getAddressRelatedInformation(User user,Event event){
        boolean informationOfUser = itemService.checkUserPlacedAnyBidOrNot(user,event );
        if (informationOfUser){
            return retrieveAddress(user);
        }
        return null;
    }

    @Override
    public AddressDto getUserAddress(String email) {
        Optional<User> userOptional = roUserService.getUserByEmail(email);
        if (!userOptional.isPresent()) {
            return null;
        }
        return retrieveAddress(userOptional.get());
    }

    @NotNull
    private AddressDto retrieveAddress(User user) {
        AddressDto addressDto = new AddressDto();
        addressDto.setAddress1(user.getAddress1());
        addressDto.setAddress2(user.getAddress2());
        addressDto.setCity(user.getCityOrProvidence());
        addressDto.setCountry(user.getCountry());
        addressDto.setState(user.getState());
        addressDto.setZipcode(user.getZipcode());
        addressDto.setPhoneNumberOfUser(user.getPhoneNumber());
        return addressDto;
    }

    @Override
    public List<ChimeAttendeeProfileDTO> getUserProfile(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        return userRepository.findUserByUserIds(userIds);
    }

    @Override
    public void saveVirtualBackground(User user, VirtualBackGroundSettingDto virtualBackGroundSettingDto) {
        User userDetails = findUserByIdWithoutCache(user.getUserId());
        //when deleting the usage image as the virtual background and deleting it at that time we need to delete their details like set as null
        if(StringUtils.isNotBlank(virtualBackGroundSettingDto.getVirtualBackgroundDetails()) && virtualBackGroundSettingDto.getVirtualBackgroundDetails().equals(userDetails.getVirtualBackgroundDetails())){
            userDetails.setVirtualBackgroundDetails(null);
        }
        userDetails.setVirtualBackgroundSettings(virtualBackGroundSettingDto.getVirtualBackgroundSettings());
        userRepository.save(userDetails);
    }

    @Override
    public String getVirtualBackground(User user){
        return roUserService.extractUserInfo(user.getUserId()).getVirtualBackgroundSetting();
    }

    @Override
    public void saveVirtualBackgroundDetails(User user, String virtualBackgroundDetails) {
        User userDetails = findUserByIdWithoutCache(user.getUserId());
        userDetails.setVirtualBackgroundDetails(virtualBackgroundDetails);
        userRepository.save(userDetails);
    }

    @Override
    public String getVirtualBackgroundDetails(User user){
        return roUserService.extractUserInfo(user.getUserId()).getVirtualBackgroundDetails();
    }

    @Override
    public List<User> findByEmails(Set<String> receivers) {
        return userRepository.findUserByEmails(receivers);
    }

    /*
    * When user is login through magic link at that time pass is null so need to allow user to set password.
    * when user have already set his/her password and want to update it at that time check current password from db and from request
    */
    @Override
    public boolean validateCurrentPassword(String currentPasswordFromDto, String currentPasswordFromUser) {
        if(null == currentPasswordFromUser || (null != currentPasswordFromDto && StringUtils.isNotEmpty(currentPasswordFromDto) && passwordEncoder.matches(currentPasswordFromDto,currentPasswordFromUser))){
            return true;
        }
        throw new NotAcceptableException(NotAceptableExeceptionMSG.CURRENT_PASSWORD_NOT_MATCH);
    }

    @Override
    public void sendSessionMagicLinkForRegisteredAttendee(Event event, SessionMagicLinkDto sessionMagicLinkDto) {
        User user = roUserService.findByEmail(sessionMagicLinkDto.getEmail());
        if (null == user) {
            log.info("User not found for send MagicLink email: {} and event_id: {}", sessionMagicLinkDto.getEmail(),event.getEventId());
            throw new NotFoundException(USER_NOT_FOUND);
        }

        if (!eventTicketsRepoService.userAlreadyPurchasedTicketInEvent(event, user)) {
            log.info("User not registered for send MagicLink email: {} and event_id: {}", sessionMagicLinkDto.getEmail(),event.getEventId());
            throw new NotAcceptableException(NotAcceptableException.AttendeeExceptionMsg.NO_REGISTRATION_FOUND_FOR_SESSION_MAGIC_LINK);
        }
        sendGridMailPrepareServiceImpl.sendSessionMagicLinkToGivenEmail(event, user, sessionMagicLinkDto);
    }

    @Override
    public void sendAutoSignInMagicLink(String email) {
        Optional<User> user =roUserService.getUserByEmail(email);
        if (!user.isPresent()){
            log.info("User not found for send MagicLink email: {}", email);
            throw new NotFoundException(USER_NOT_FOUND);
        }
        sendGridMailPrepareServiceImpl.sendAutoSignInMagicLink(user.get());
    }

    @Override
    public void sendPushNotificationOnMobileAppToTicketHolder(Event event, List<Long> holderIds) {
        try {
            log.info("About to send push notification to {} users for event {} ", holderIds.size(), event.getEventId());
            List<String> holdersPushNotificationToken = findPushNotificationTokenByUserIdIn(holderIds);
            if (!CollectionUtils.isEmpty(holdersPushNotificationToken)) {
                List<String> tokenList = new ArrayList<>();
                holdersPushNotificationToken.forEach(userToken -> {
                    List<String> token = convertCommaSeparatedToList(userToken);
                    tokenList.addAll(token);
                });
                log.info("Send push Notification to {} ticket holders using {} token list", holderIds.size(), tokenList.size());
                if (!CollectionUtils.isEmpty(tokenList)) {
                    String notificationSubject = PUSH_NOTIFICATION_TO_HOLDERS_SUBJECT.replace("${event_name}", event.getName());
                    String notificationBody = PUSH_NOTIFICATION_TO_HOLDERS_BODY;
                    List<List<String>> partitionTokensList = ListUtils.partition(tokenList, 100);
                    for (int i = 0; i < partitionTokensList.size(); i++) {
                        MulticastMessage message = MulticastMessage.builder()
                                .setNotification(Notification.builder().setTitle(notificationSubject).setBody(notificationBody).build())
                                .putData(Constants.CONTENT, notificationSubject)
                                .putData(Constants.BODY, notificationBody)
                                .putData(Constants.EVENT_URL, event.getEventURL())
                                .putData(Constants.TYPE, MOBILE_NOTIFICATION_TYPE)
                                .addAllTokens(partitionTokensList.get(i))
                                .build();
                        FirebaseApp fireBaseApp = null;
                        if (event.getWhiteLabelId() != null && firebaseAppHashMap.containsKey(event.getWhiteLabelId())) {
                            fireBaseApp = firebaseAppHashMap.get(event.getWhiteLabelId());
                        } else {
                            fireBaseApp = firebaseAppHashMap.get(0L);
                        }
                        BatchResponse response = FirebaseMessaging.getInstance(fireBaseApp).sendEachForMulticast(message);
                        log.info("UserServiceImpl sendPushNotificationToEventUsers Successfully sent message: {}, partition no.: {}", response.getSuccessCount(), i);
                    }
                }
            } else {
                log.info("Ticket holders token not found to sent message with eventId: {}", event.getEventId());
            }
        } catch (Exception e) {
            log.error("About to send push notification to {} users for event {} , ErrorMsg {} ", holderIds.size(), event.getEventId(), e.getMessage());
        }

    }

    @Override
    public boolean isUserExistByEmail(String email) {
        return userRepository.isUserExistByEmail(email);
    }

    @Override
    public void checkRequestedUserBelongsToOrganizerOrWhiteLabelOrNot(User user, String whiteLabelUrl, String organizerPageUrl) {
        //if event is created from organizer page then check user belongs to that organizer or not
        if(StringUtils.isEmpty(whiteLabelUrl) && StringUtils.isNotBlank(organizerPageUrl) && user!=null){
            Organizer organizer = organizerService.getOrganizerByURL(organizerPageUrl);
            if (organizer!=null){
                List<JoinUsersWithOrganizers> joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findJoinOrganizersByOrganizersId(organizer.getId());
                if(joinUsersWithOrganizers.stream().noneMatch(join -> join.getUserId() == user.getUserId())){
                    log.info(" user {} is not authorized to create event for organizer {}", user.getUserId(), organizerPageUrl);
                    throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_AUTHORIZED_TO_CREATE_EVENT);
                }
                log.info(" user {} is authorized to create event for organizer {}", user.getUserId(), organizerPageUrl);
            }
        }//if event is created from WL organizer page then check user belongs to that white label or organizer or not
        else if (StringUtils.isNotBlank(whiteLabelUrl) && StringUtils.isNotBlank(organizerPageUrl) && user!=null) {
            Organizer organizer = organizerService.getOrganizerByURL(organizerPageUrl);
            WhiteLabel whiteLabel=whiteLabelService.findWhiteLabel(whiteLabelUrl);
            if(whiteLabel != null && organizer!=null && organizer.getWhiteLabel()!=null && (organizer.getWhiteLabel().getWhiteLabelUrl().equals(whiteLabelUrl))) {
                List<Staff> staffs = staffRepository.findByWhiteLabelAndRole(whiteLabel,WL_AND_EVENT_COORDINATOR_PERMISSIONS);
                List<JoinUsersWithOrganizers> joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findJoinOrganizersByOrganizersId(organizer.getId());
                if (staffs.stream().noneMatch(staff -> Objects.equals(staff.getUserId(), user.getUserId())) && joinUsersWithOrganizers.stream().noneMatch(join -> join.getUserId() == user.getUserId())) {
                    log.info(" user {} is not authorized to create event for white label {} and organizer {}", user.getUserId(), whiteLabelUrl, organizerPageUrl);
                    throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_AUTHORIZED_TO_CREATE_EVENT);
                }
                log.info(" user {} is authorized to create event for white label {} and organizer {}", user.getUserId(), whiteLabelUrl, organizerPageUrl);
            }
        }//if event created from WL event or enterprise page then check user belongs to that white label or not
        else if (StringUtils.isNotBlank(whiteLabelUrl) && StringUtils.isEmpty(organizerPageUrl) && user!=null) {
            WhiteLabel whiteLabel=whiteLabelService.findWhiteLabel(whiteLabelUrl);
            if(whiteLabel != null) {
                List<Staff> staffs = staffRepository.findByWhiteLabelAndRole(whiteLabel,WL_AND_EVENT_COORDINATOR_PERMISSIONS);
                if (staffs.stream().noneMatch(staff -> Objects.equals(staff.getUserId(), user.getUserId()))) {
                    log.info(" user {} is not authorized to create event for white label {}", user.getUserId(), whiteLabelUrl);
                    throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_AUTHORIZED_TO_CREATE_EVENT);
                }
                log.info(" user {} is authorized to create event for white label {}", user.getUserId(), whiteLabelUrl);
            }
        }
    }
    public Optional<User> getUserByEventMagicLink(String userKey, String wayToLogin) {
        Optional<User> userOptional;
        if (StringUtils.isNotBlank(wayToLogin)) {
            userOptional = autoLoginService.findAndUpdateAutoLoginByTokenOfEvent(userKey, wayToLogin);
        } else {
            userOptional = roUserService.getUserById(SecurityUtils.decodeUserid(userKey));
        }
        return userOptional;
    }
    @Override
    public boolean isUserLoggedInAsAdminForEvent(String userKey, User loggedInUser,Long eventId) {
        if (StringUtils.isEmpty(userKey)) {
            return false;
        }
        return false;
    }

    @Override
    public void sendPlatformLevelMagicLink(String email, String redirectUrl, Long wlId, Long eventId, boolean isRequireFourDigitCode, String eventUrl) {
        if(StringUtils.isBlank(email)){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.PASS_EMAIL_ADDRESS);
        }
        Optional<User> user = roUserService.getUserByEmail(email);
        if (!user.isPresent()) {
            log.info("User not found for send MagicLink email: {}", email);
            throw new NotFoundException(USER_NOT_FOUND);
        }
        String fourDigitsCode = null;
        /*
         * we will receive wlId only when the API is called from mobile. To temporarily fix an issue, mobile not
         * passing the isRequireFourDigitCode flag, we are checking wlId to generate 4 digit code.Once the issue is fixed
         * in mobile, we can remove this check.
         */
        if (isRequireFourDigitCode || wlId != null) {
            fourDigitsCode=generateFourDigitCodeToAccessPlatformLevel(user.get());
        }
        sendGridMailPrepareServiceImpl.sendPlatformLevelMagicLink(user.get(), redirectUrl, wlId,eventId,fourDigitsCode,eventUrl);
    }

    private String generateFourDigitCodeToAccessPlatformLevel(User user) {
        OTPGenerator otpGenerator = new OTPGenerator(4);
        String otpCode = otpGenerator.nextString();
        cacheStoreService.set(FOUR_DIGIT_CODE_PLATFORM + user.getUserId(), otpCode, 7, TimeUnit.DAYS);
        return otpCode;
    }

    @Override
    public ResponseDto sendFourDigitCodeToAccessPlatformLevel(User user) {
        log.info("sendFourDigitCodeToAccessPlatformLevel | user : {}",user);

            OTPGenerator otpGenerator = new OTPGenerator(4);
            String otpCode = otpGenerator.nextString();
            cacheStoreService.set(FOUR_DIGIT_CODE_PLATFORM + user.getUserId(), otpCode, 5, TimeUnit.MINUTES);
            sendGridMailPrepareService.sendAuthenticationCodeToUser(user, otpCode);

        return new ResponseDto("Success","4 digit code sent successfully to your email");
    }

    @Override
    public void confirmFourDigitCodeToAccessPlatformLevel(User user, Long code) {
        log.info("confirmFourDigitCodeToAccessPlatformLevel | user : {} | code : {}",user,code);

        if (user != null) {
            Object object = cacheStoreService.get(FOUR_DIGIT_CODE_PLATFORM + user.getUserId());
            if (object == null || !String.valueOf(code).equals(object)) {
                throw new NotAcceptableException(NotAcceptableException.AttendeeExceptionMsg.NOT_VALID_CODE);
            }
            cacheStoreService.delete(FOUR_DIGIT_CODE_PLATFORM+user.getUserId());
            log.info("confirmFourDigitCodeToAccessPlatformLevel | user {}", user.getUserId());
        }

    }

    @Override
    public List<Integer> findAllUniqueBrilworksAndAccelEventsUserIds() {
        return userRepository.findAllUniqueBrilworksAndAccelEventsUserIds();
    }

    @Override
    public void resendMagicLink(String email, Event event, String expiredMagicLink,String role) {
        if(StringUtils.isBlank(email)){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.PASS_EMAIL_ADDRESS);
        }
        User user = roUserService.findByEmail(email);
        if (user!=null && expiredMagicLink != null) {
            String token = autoLoginService.getOrCreateEventLevelMagicLinkToken(event, user.getUserId(), user.getUserId(), AutoLoginCreationLocation.RESEND_MAGIC_LINK_EMAIL);
            Matcher matcher = pattern.matcher(expiredMagicLink);
            String modifiedUrl = matcher.replaceAll(USER_KEY_EQUALS + token);
            sendGridMailPrepareService.resendMagicLink(email, event, user, modifiedUrl, token,role);
        } else {
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
    }

    @Override
    public void sendAuthenticationCodeForProfileEmailUpdate(User user) {
        log.info("sendAuthenticationCodeForProfileEmailUpdate process started for user : {}", user);
        OTPGenerator otpGenerator = new OTPGenerator(6);
        String authenticationCode = otpGenerator.nextString();

        cacheStoreService.set(USER_PROFILE_UPDATE_AUTH_CODE_PREFIX + user.getUserId(), authenticationCode, 5, TimeUnit.MINUTES);

        EmailMessage emailMessage = new EmailMessage(TemplateId.PROFILE_UPDATE_AUTHENTICATION_CODE);
        emailMessage.setBody(Constants.STRING_BLANK);
        emailMessage.setSubject("Authentication Code for Profile Update");
        emailMessage.setTemplateName(TemplateId.PROFILE_UPDATE_AUTHENTICATION_CODE.getValue());

        Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();

        String firmName = Constants.ACCELEVENTS;
        String logo = imageConfiguration.getImagePrefix().concat(EMAIL_TEMPLATE_IMAGES).concat(imageConfiguration.getImagesAcceleventlogo());


        String token = user.getPasswordResetToken();
        if (StringUtils.isBlank(token)) {
            token = UUID.randomUUID().toString() + System.nanoTime();
            user.setPasswordResetToken(token);
            this.save(user);
        }
        substitutionMap.put(CLOUDINARY_IMAGE_PREFIX,imageConfiguration.getImagePreFixWithCloudinaryUrl());
        substitutionMap.put(CREATE_EVENT,uiBaseurl.concat(SLASH_EVENTS));
        substitutionMap.put(USER_NAME, user.getFirstName());
        substitutionMap.put(IMAGE_FOOTER, sendGridMailPrepareServiceImpl.getWhiteLabelHeaderLogoLocation(null));
        sendGridMailPrepareServiceImpl.addTheValueOfFooterForNewTemplates(substitutionMap, null);
        substitutionMap.put(RESET_PASSWORD, uiBaseurl + U_SLASH_NEW_PASSWORD_PATH + Constants.TOKEN + user.getPasswordResetToken() + "&redirect=" + uiBaseurl + "/u/login");

        substitutionMap.put(FIRM_NAME, firmName);
        substitutionMap.put(AUTHENTICATION_CODE, authenticationCode);
        substitutionMap.put(IMAGE_URL, logo);

        Set<String> receivers = new HashSet<>();
        receivers.add(user.getEmail());
        emailService.sendTemplateMail(emailMessage, receivers);
        log.info("sendAuthenticationCodeForProfileEmailUpdate process ended and email sent to user : {}", user.getEmail());
    }

    @Override
    public void createNewPasswordFromResetCode(User user, PasswordDto passwordDto) {
        log.info("createNewPasswordFromResetCode for userId {} , using resentCode {}", user.getUserId(), passwordDto.getPasswordResetCode());
        if (passwordDto.getNewPassword().equals(passwordDto.getConfirmPassword())) {
            if (StringUtils.isNotBlank(passwordDto.getPasswordResetCode())) {
                validateResetCodeWithUser(passwordDto.getPasswordResetCode(),user.getUserId());
            } else if (userService.validateCurrentPassword(passwordDto.getCurrentPassword(), user.getPassword())) {
                userService.validatePassword(passwordDto.getConfirmPassword());
                userService.resetNewUserPasswordAndReturnRedirectUrl(passwordEncoder.encode(passwordDto.getConfirmPassword()), Optional.of(user));
            }
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.CONFIRM_PASSWORD_NOT_MATCH);
        }
    }

    @Override
    public List<Long> getAllUserIdByEventIdAndWhiteLabelIdOrOrganizerId(Long eventId, Long whiteLabelId, Long organizerId) {
        return userRepository.getAllUserIdByEventIdAndWhiteLabelIdOrOrganizerId(eventId,whiteLabelId,organizerId);
    }

    @Override
    public String getMagicLinkOfUser(Event event, User staffUser,String email) {
        log.info("getMagicLinkOfUser | event {} | user {} | email : {}",event.getEventId(),staffUser.getUserId(),email);
        User user = roUserService.findByEmail(email);
        checkUserIsNotNullAndPurchaseTicket(event,user,email);
        return autoLoginService.getEventLevelMagicLinkUrlForUser(user,event, staffUser.getUserId());

    }

    private void checkUserIsNotNullAndPurchaseTicket(Event event, User user, String email) {
        if (null == user) {
            NotFoundException.UserNotFound ex = NotFoundException.UserNotFound.USER__NOT_PRESENT_FOR_EMAIL;
            String constantMessage = Constants.USER__NOT_PRESENT_FOR_EMAIL.replace("${email}", email);
            ex.setErrorMessage(constantMessage);
            ex.setDeveloperMessage(constantMessage);
            throw new NotFoundException(ex);
        }

        if (!eventTicketsRepoService.userAlreadyPurchasedTicketInEvent(event, user)) {

            NotAcceptableException.TicketingExceptionMsg ex = NotAcceptableException.TicketingExceptionMsg.EMAIL_IS_NOT_REGISTERED_FOR_EVENT;
            String constantMessage = Constants.EMAIL_IS_NOT_REGISTERED_FOR_EVENT.replace("${email_address}", email);
            constantMessage = constantMessage.replace("${event_name}", event.getName());
            ex.setErrorMessage(constantMessage);
            ex.setDeveloperMessage(constantMessage);
            throw new NotAcceptableException(ex);
        }
    }

    @Override
    public MagicLinkResponseDto expireOldAndGenerateNewMagicLink(Event event, User staffUser, String email, boolean generateNewLink) {
        log.info("expireOldAndGenerateNewMagicLink | event {} | staffUser {} | email : {} | generateNewLink {}",event.getEventId(),staffUser.getUserId(),email, generateNewLink);
        User user = roUserService.findByEmail(email);
        checkUserIsNotNullAndPurchaseTicket(event,user, email);
        return autoLoginService.expireOldAndGenerateNewMagicLink(event,staffUser,user,generateNewLink);
    }

    @Override
    public List<User> findUsersByEmailIn(ArrayList<String> emails) {
        return userRepository.findUsersByEmailIn(emails);
    }

    @Override
    public void saveAll(List<User> users) {
        userRepository.saveAll(users);
    }

    private void validateResetCodeWithUser(String passwordResetCode, Long userId){
        Object object = cacheStoreService.get("PASSWORD_RESET_CODE_" + userId);
        if (object == null) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_EXPIRE);
        } else if (!passwordResetCode.equals(object)) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_NOT_MATCH);
        }
    }

    @Override
    public void saveChatExpanded(User user, Boolean isChatExpanded){
        user.setChatExpanded(isChatExpanded);
        log.info("Updated chatExpanded:{} by User:{} ",isChatExpanded,user.getUserId());
        userService.save(user);
    }

    @Override
    public boolean validateUserDomain(String email) {
        log.info("validate user domain email {}", email);
        if (StringUtils.isNotBlank(email) && email.contains("@")) {
            String emailDomain = email.substring(email.indexOf("@") + 1);
            return oktaAuthRequiredDomain.equalsIgnoreCase(emailDomain);
        }
        return false;
    }

    @Override
    public AccessTokenModel loginUserWithSalesforce(String code, String codeVerifier, String clientId,String eventUrl) {
        log.info("trying to login user with salesforce for event: {}",eventUrl);
        Event event = eventService.getEventByURLWithoutCache(eventUrl);
        String whiteLabelUrl = event.getWhiteLabel() != null ? event.getWhiteLabel().getWhiteLabelUrl() : null;
        if (whiteLabelUrl == null || whiteLabelUrl.isEmpty()) {
            throw new NotFoundException(NotFound.WHITE_LABEL_URL_NOT_FOUND);
        }
        WhitelabelSSOConfiguration whitelabelSSOConfiguration = whitelabelSSOConfigurationRepository.findSSOConfigurationByWhiteLabelUrlAndSSOType(whiteLabelUrl, SSOIdentityProvidersType.SALESFORCE);
        if (whitelabelSSOConfiguration == null) {
            throw new NotFoundException(NotFound.SSO_CONFIG_NOT_FOUND);
        }
        String accessToken = ssoUserService.fetchAccessTokenForSalesforce(code, codeVerifier, clientId, whitelabelSSOConfiguration.getClientSecret());
        return fetchUserDetailsBasedOnSalesforceAccessToken(accessToken);
    }

    private AccessTokenModel fetchUserDetailsBasedOnSalesforceAccessToken(String accessToken) {
        if (accessToken != null) {
            log.info("fetch user details based on salesforce access token");
            SSOUserDTO ssoUserDTO = ssoUserService.fetchSalesforceUserDetails(accessToken);
            if (ssoUserDTO != null) {
                AccessTokenModel accessTokenModel = getUserDetailForSalesforceLogin(ssoUserDTO);
                //accessTokenModel.setAuthenticatedFromSalesforce(true);
                accessTokenModel.setLoginUsingSso(true);
                accessTokenModel.setSsoIdentityProvidersType(AccessTokenModel.SSOIdentityProvidersType.SALESFORCE);
                return accessTokenModel;
            } else {
                log.info("User details not found for the provided access token.");
                throw new NotFoundException(USER_NOT_FOUND);
            }
        } else {
            throw new NotAcceptableException(TOKEN_NOT_FOUND);
        }
    }

    @Override
    public AccessTokenModel loginUserWithSalesforceUsingSSOToken(String token) {
        log.info("login user with salesforce using the sso token");
        return fetchUserDetailsBasedOnSalesforceAccessToken(token);
    }

    public AccessTokenModel getUserDetailForSalesforceLogin(SSOUserDTO ssoUserDTO) {
        Optional<User> objUserOptional = roUserService.getUserByPhoneOrEmail(ssoUserDTO.getEmail());
        if (objUserOptional.isPresent()) {
            User objUser = objUserOptional.get();
            List<String> userRoles = roUserService.getUserRoles(objUser);
            // put all user role in list
            if (null != userRoles && !userRoles.isEmpty()) {
                Event event = roEventService.getEventByHostUserAndMostRecentEventId(objUser);
                return new AccessTokenModel(ssoUserDTO.getEmail(),objUser.getPassword(), userRoles, objUser,false,
                        event, roStaffService.getStaffRole(event, objUser));
            } else {
                throw new NotFoundException(USER_ROLE_NOT_FOUND);
            }
        } else {
            throw new NotFoundException(USER_NOT_FOUND);
        }
    }


    @Override
    public String getAndCreatePlatformLevelMagicLink(User user, Event event) {
        log.info("getAndCreatePlatformLevelMagicLink for user {} and event {}",user.getUserId(),event.getEventId());
        String token = UUID.randomUUID().toString();
        autoLoginService.saveAutoLogin(event, user.getUserId(), token, 24 * 7, MagicLinkType.PLATFORM_LEVEL, user.getUserId(), PLATFORM_LEVEL_MAGIC_LINK_API);
        String domainUrl = serviceHelper.getEventBaseUrl(event);
        String platFormLevelMagicLink = domainUrl.concat(SLASH_U_PROFILE_TICKET).concat(QUE_MARK_USER_KEY_EQUALS).concat(token).concat(AND_WAY_TO_LOGIN_EQUALS).concat(LoginLink.MAGIC_LINK.name())
                .concat(QUE_IS_FROM_PLATFORM_EQUALS).concat("true") + AND_PASSWORD_LESS_EQUALS.concat("true");
        log.info("getAndCreatePlatformLevelMagicLink successfully created for user {} and event {}",user.getUserId(),event.getEventId());
        return platFormLevelMagicLink;
    }

    @Override
    public String getHostBaseRedirectUrl(User user, Event event, String hostBaseUrl, String hostBaseRedirectUrl) {
        if (event.getWhiteLabelId() != null) {
            WhiteLabel whiteLabel = event.getWhiteLabel();
            if (hostBaseUrl.equalsIgnoreCase(whiteLabel.getHostBaseUrl())) {
                log.info("getHostBaseRedirectUrl for user {} and event {}", user.getUserId(), event.getEventId());
                String token = UUID.randomUUID().toString();
                autoLoginService.saveAutoLogin(event, user.getUserId(), token, 24 * 7, MagicLinkType.PLATFORM_LEVEL, user.getUserId(), PLATFORM_LEVEL_MAGIC_LINK_API);
                String url = hostBaseUrl.concat(SLASH_U_PROFILE)
                        .concat(QUE_MARK_USER_KEY_EQUALS).concat(token)
                        .concat(AND_WAY_TO_LOGIN_EQUALS).concat(LoginLink.MAGIC_LINK.name())
                        .concat("&email=").concat(user.getEmail())
                        .concat(AND_PASSWORD_LESS_EQUALS).concat("true")
                        .concat(QUE_IS_FROM_PLATFORM_EQUALS).concat("true").concat("&redirectUrl=").concat(hostBaseRedirectUrl);
                log.info("getHostBaseRedirectUrl successfully created for user {} and event {}", user.getUserId(), event.getEventId());
                user.setMostRecentEventId(event.getEventId());
                userRepository.save(user);
                return url;
            }
            return hostBaseRedirectUrl;
        }
        return hostBaseRedirectUrl;
    }

    @Override
    public List<UserAttendeeDTO> getUserBasicDetailsByUserIds(List<Long> userIds) {
        return userRepository.getUserBasicDetailsByUserIds(userIds);
    }

    @Override
    public void resetUsersMostRecentEventAsZero(Event event) {
         userRepository.updateMostRecentEventIdZeroByEventId(event.getEventId());
    }
}
