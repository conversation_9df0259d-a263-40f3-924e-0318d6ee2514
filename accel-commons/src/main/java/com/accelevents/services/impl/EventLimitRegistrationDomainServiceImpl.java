package com.accelevents.services.impl;

import com.accelevents.common.dto.EventLimitRegistrationDomainDTO;
import com.accelevents.common.dto.EventLimitRegistrationSettingsDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EnumLabel;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.EventLimitRegistrationDomain;
import com.accelevents.dto.tray.io.connector.TicketTypeDetails;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.*;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.ticketing.dto.RegistrationValidationDto;
import com.accelevents.utils.GeneralUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class EventLimitRegistrationDomainServiceImpl implements EventLimitRegistrationDomainService {

    private static final Logger log = LoggerFactory.getLogger(EventLimitRegistrationDomainServiceImpl.class);

    @Autowired
    private EventLimitRegistrationDomainRepository eventLimitRegistrationDomainRepository;

    @Autowired
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Autowired
    private RecurringEventsRepository recurringEventsRepository;

    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private TicketingRepository ticketingRepository;

    @Autowired
    private StaffService staffService;

    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;

    @Autowired
    private ContactsRepository contactsRepository;

    @Override
    public Long saveRegistrationDomainSettings(EventLimitRegistrationDomainDTO eventLimitRegistrationDomainDTO, Event event, User user, long recurringEventId) {
        log.info("saveRegistrationDomainSettings eventId {} , userId {} , eventLimitRegistrationDomainDTO {}", event.getEventId(), user.getUserId(), eventLimitRegistrationDomainDTO);

        //Create the registration domain settings for the event or specific recurring event
        EventLimitRegistrationDomain eventLimitRegistrationDomain =  convertToEventLimitRegistrationDomain(eventLimitRegistrationDomainDTO, event, user, recurringEventId, null, null);
        eventLimitRegistrationDomain = eventLimitRegistrationDomainRepository.save(eventLimitRegistrationDomain);
        long registrationRuleId = eventLimitRegistrationDomain.getId();

        Ticketing ticketing = ticketingService.findByEvent(event);

        if (ticketing.isRecurringEvent() && recurringEventId == 0) {

            //Create the registration domain settings for all the recurring events with created from as the registration rule id
            log.info("saveRegistrationDomainSettings for all the recurring events, event id {} ", event.getEventId());
            List<EventLimitRegistrationDomain> eventLimitRegistrationDomains = new ArrayList<>();
            List<Long> recurringEventsList = recurringEventsScheduleService.getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(event.getEventId());



            List<Long> allowedTicketTypes = eventLimitRegistrationDomainDTO.getTicketTypeIds();
            List<TicketingType> ticketingTypes = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(allowedTicketTypes)) {
                 ticketingTypes = ticketingTypeRepository.findByCreatedFrom(allowedTicketTypes);
            }
            Map<Long, List<TicketingType>> ticketsGroupByRecurring = ticketingTypes.stream()
                    .collect(Collectors.groupingBy(TicketingType::getRecurringEventId));
            recurringEventsList.forEach(recurringEvent ->
                eventLimitRegistrationDomains.add(convertToEventLimitRegistrationDomain(eventLimitRegistrationDomainDTO, event, user, recurringEvent, registrationRuleId, ticketsGroupByRecurring))
            );
            if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomains)) {
                eventLimitRegistrationDomainRepository.saveAll(eventLimitRegistrationDomains);
            }

        }
        return registrationRuleId;
    }


    public EventLimitRegistrationDomain convertToEventLimitRegistrationDomain(EventLimitRegistrationDomainDTO eventLimitRegistrationDomainDTO, Event event, User user, long recurringEvent, Long finalRegistrationRuleId, Map<Long, List<TicketingType>> ticketsGroupByRecurring) {
        EventLimitRegistrationDomain eventLimitRegistrationDomain = new EventLimitRegistrationDomain();
        if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getDomains())) {
            eventLimitRegistrationDomain.setDomains(StringUtils.join(eventLimitRegistrationDomainDTO.getDomains(), ","));
        }
        if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getContactListIds())) {
            eventLimitRegistrationDomain.setContactListIds(GeneralUtils.convertLongListToCommaSeparated(eventLimitRegistrationDomainDTO.getContactListIds()));
        }
        eventLimitRegistrationDomain.setAllContactListsSelected(eventLimitRegistrationDomainDTO.isAllContactListsSelected());
        eventLimitRegistrationDomain.setBlocked(eventLimitRegistrationDomainDTO.isBlockedRule());
        eventLimitRegistrationDomain.setEventId(event.getEventId());
        eventLimitRegistrationDomain.setCreatedAt(new Date());
        eventLimitRegistrationDomain.setUpdatedAt(new Date());
        if (ticketsGroupByRecurring == null) {
            if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getTicketTypeIds())) {
                eventLimitRegistrationDomain.setTicketTypeIds(GeneralUtils.convertLongListToCommaSeparated(eventLimitRegistrationDomainDTO.getTicketTypeIds()));
            }
        } else {
            eventLimitRegistrationDomain.setTicketTypeIds(getTicketTypeIdsComaSep(ticketsGroupByRecurring.get(recurringEvent)));
        }
        eventLimitRegistrationDomain.setRecordStatus(RecordStatus.CREATE);
        eventLimitRegistrationDomain.setCreatedBy(user);
        eventLimitRegistrationDomain.setUpdatedBy(user);
        eventLimitRegistrationDomain.setCreatedFrom(finalRegistrationRuleId);
        eventLimitRegistrationDomain.setRecurringEventId(recurringEvent);
        eventLimitRegistrationDomain.setEnabledRule(eventLimitRegistrationDomainDTO.isEnabledRule());
        eventLimitRegistrationDomain.setGlobalRule(eventLimitRegistrationDomainDTO.isGlobalRule());
        return eventLimitRegistrationDomain;
    }

    public String getTicketTypeIdsComaSep(List<TicketingType> ticketingTypes) {

        List<Long> newTicketingTypeIds = new ArrayList<>();
        if (ticketingTypes != null) {
            newTicketingTypeIds = ticketingTypes.stream().map(TicketingType::getId).collect(Collectors.toList());
        }

        return GeneralUtils.convertListToCommaSeparated(newTicketingTypeIds.stream().map(String::valueOf).collect(Collectors.toList()));
    }

    @Override
    public void updateEventLimitRegistrationDomainRecStatusByRecurringEventIds(List<Long> recurringEventIds, RecordStatus recStatus) {
         eventLimitRegistrationDomainRepository.updateEventLimitRegistrationDomainRecStatusByRecurringEventIds(recurringEventIds, recStatus);
    }

    @Override
    public void updateTicketTypeInEventLimitRegistrationDomain(Event event, TicketingType ticketingType, Ticketing ticketing) {
        log.info("updateTicketTypeInEventLimitRegistrationDomain eventId {} , ticketingType {}", event.getEventId(), ticketingType.getId());

        //Update the ticket type in the registration domain settings if the rule is global because it's applicable to all the ticket types
        List<EventLimitRegistrationDomain> eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findByEventIdAndGlobalRuleEnable(event.getEventId());

        List<TicketingType> recurringTicketTypes = ticketingTypeRepository.findByCreatedFrom(Collections.singletonList(ticketingType.getId()));
        Map<Long, List<TicketingType>> ticketsGroupByRecurring = recurringTicketTypes.stream()
                    .collect(Collectors.groupingBy(TicketingType::getRecurringEventId));

        if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomains)) {
            eventLimitRegistrationDomains.forEach(eventLimitRegistrationDomain -> {
                if(eventLimitRegistrationDomain.getRecurringEventId() == 0) {
                    log.info("updateTicketTypeInEventLimitRegistrationDomain | event | eventId {} , ticketingType {} | rule id {}", event.getEventId(), ticketingType.getId(), eventLimitRegistrationDomain.getId());
                    List<Long> ticketTypeIds = GeneralUtils.convertCommaSeparatedToListLong(eventLimitRegistrationDomain.getTicketTypeIds());
                    if (!ticketTypeIds.contains(ticketingType.getId())) {
                        ticketTypeIds.add(ticketingType.getId());
                    }
                    eventLimitRegistrationDomain.setTicketTypeIds(GeneralUtils.convertListToCommaSeparated(ticketTypeIds.stream().map(String::valueOf).collect(Collectors.toList())));
                }else{
                    List<Long> ticketTypeIds = GeneralUtils.convertCommaSeparatedToListLong(eventLimitRegistrationDomain.getTicketTypeIds());
                    if(ticketsGroupByRecurring.containsKey(eventLimitRegistrationDomain.getRecurringEventId())) {
                        ticketTypeIds.addAll(ticketsGroupByRecurring.get(eventLimitRegistrationDomain.getRecurringEventId()).stream().map(TicketingType::getId).collect(Collectors.toList()));
                        log.info("updateTicketTypeInEventLimitRegistrationDomain | recurring event | eventId {} , ticketingType {} | rule id {}", event.getEventId(), ticketingType.getId(), eventLimitRegistrationDomain.getId());
                        eventLimitRegistrationDomain.setTicketTypeIds(GeneralUtils.convertListToCommaSeparated(ticketTypeIds.stream().map(String::valueOf).collect(Collectors.toList())));
                    }
                }
                });
            eventLimitRegistrationDomainRepository.saveAll(eventLimitRegistrationDomains);
        }
    }

    @Override
    public void removeTicketTypesFromEventLimitRegistrationDomain(Event event, long ticketTypeId) {
        log.info("removeTicketTypesFromEventLimitRegistrationDomain eventId {} , id {}", event.getEventId(), ticketTypeId);
        List<EventLimitRegistrationDomain> eventLimitRegistrationDomainList = new ArrayList<>();
        List<EventLimitRegistrationDomain> eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findAllByEventId(event.getEventId());
        if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomains)) {
            eventLimitRegistrationDomains.forEach(eventLimitRegistrationDomain -> {
                List<Long> ticketTypeIds = GeneralUtils.convertCommaSeparatedToListLong(eventLimitRegistrationDomain.getTicketTypeIds());
                if(ticketTypeIds.contains(ticketTypeId)) {
                    log.info("removeTicketTypesFromEventLimitRegistrationDomain | eventId {} , ticketingType {} | rule id {}", event.getEventId(), ticketTypeId, eventLimitRegistrationDomain.getId());
                    ticketTypeIds.remove(ticketTypeId);
                    eventLimitRegistrationDomain.setTicketTypeIds(GeneralUtils.convertListToCommaSeparated(ticketTypeIds.stream().map(String::valueOf).collect(Collectors.toList())));
                    eventLimitRegistrationDomainList.add(eventLimitRegistrationDomain);
                }
            });
            if(CollectionUtils.isNotEmpty(eventLimitRegistrationDomainList)) {
                eventLimitRegistrationDomainRepository.saveAll(eventLimitRegistrationDomainList);
            }
        }
    }

    @Override
    public List<TicketTypeDetails> validateRegistrationEmail(RegistrationValidationDto registrationValidationDto, Event event) {
        List<TicketTypeDetails> notAllowedTicketTypesDetails = new ArrayList<>();
        if (registrationValidationDto == null) {
            return notAllowedTicketTypesDetails;
        }

        log.info("validateEmailWithRegistrationDomain eventId {}  , email {}", event.getEventId(), registrationValidationDto.getEmail());
        Ticketing ticketing = ticketingService.findByEvent(event);
        String email = registrationValidationDto.getEmail();
        List<Long> ticketTypeIds = registrationValidationDto.getTicketTypeIds();
        long recurringEventId = registrationValidationDto.getRecurringEventId() != null ? registrationValidationDto.getRecurringEventId() : 0;

        if (StringUtils.isBlank(email) || CollectionUtils.isEmpty(ticketTypeIds) || (!ticketing.isLimitRegistrationEmails() && recurringEventId <= 0)) {
            return notAllowedTicketTypesDetails;
        }

        if(recurringEventId > 0){
            RecurringEvents recurringEvents = recurringEventsRepository.findById(recurringEventId).orElse(null);
            if(recurringEvents != null && !recurringEvents.isLimitRegistrationEnabled()){
                return notAllowedTicketTypesDetails;
            }
        }

        if (!isEmailNotRegisteredAsExhibitorOrSpeaker(email, event)) {
            log.info("validateEmailWithRegistrationDomain | email is registered as exhibitor or speaker | eventId {} , email {}", event.getEventId(), email);
            return notAllowedTicketTypesDetails;
        }

        //Registration domain settings for the event or specific recurring event and if enabled rule is true
        List<EventLimitRegistrationDomain> eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findByEventIdAndRecurringEventIdAndEnableRule(event.getEventId(), recurringEventId);
        List<Long> notAllowedTicketTypes = new ArrayList<>();

        for (EventLimitRegistrationDomain eventLimitRegistrationDomain : eventLimitRegistrationDomains) {
            if (eventLimitRegistrationDomain.isGlobalRule()) {
                log.info("validateEmailWithRegistrationDomain | global rule | eventId {} , email {}", event.getEventId(), email);
                //Global rule is applicable to all the ticket types, no need to check the ticket type
                validateEmailAgainstDomainsAndContactList(email, event, eventLimitRegistrationDomain);
            } else {
                List<Long> domainTicketingType = GeneralUtils.convertCommaSeparatedToListLong(eventLimitRegistrationDomain.getTicketTypeIds());
                if (CollectionUtils.isNotEmpty(domainTicketingType) && ticketTypeIds.stream().anyMatch(domainTicketingType::contains)) {
                    try {
                        log.info("validateEmailWithRegistrationDomain | ticket type rule | eventId {} , email {}", event.getEventId(), email);
                        validateEmailAgainstDomainsAndContactList(email, event, eventLimitRegistrationDomain);
                    } catch (NotAcceptableException e) {
                        //If the email is not allowed for the ticket type, add the ticket type to the not allowed list
                        notAllowedTicketTypes.addAll(ticketTypeIds.stream().filter(domainTicketingType::contains).collect(Collectors.toList()));
                        log.info("validateEmailWithRegistrationDomain | ticket type rule | eventId {} , email {} | not allowed ticket type count {}", event.getEventId(), email, notAllowedTicketTypes.size());
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(notAllowedTicketTypes)) {
            //Get the ticket type details for the not allowed ticket types
            notAllowedTicketTypesDetails = ticketingTypeRepository.getTicketTypeDetailsByTicketTypeIdsIn(notAllowedTicketTypes);
        }

        return notAllowedTicketTypesDetails;
    }



    @Override
    @Transactional
    public void updateRegistrationDomainSettings(long id,EventLimitRegistrationDomainDTO eventLimitRegistrationDomainDTO, Event event,User user, long recurringEventId) {
        log.info("updateRegistrationDomainSettings eventId {} , userId {} , eventLimitRegistrationDomainDTO {}",event.getEventId(),user.getUserId(),eventLimitRegistrationDomainDTO);

        Ticketing ticketing = ticketingService.findByEvent(event);
            Optional<EventLimitRegistrationDomain> eventLimitRegistrationDomainOptional = eventLimitRegistrationDomainRepository.findById(id);
            if (eventLimitRegistrationDomainOptional.isEmpty()) {
                throw new NotFoundException(NotFoundException.NotFound.RESTRICTIONS_DOMAINS_NOT_FOUND);
            }
            EventLimitRegistrationDomain eventLimitRegistrationDomain=eventLimitRegistrationDomainOptional.get();

            //Update the registration domain settings for the event or specific recurring event
            if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getDomains())){
                eventLimitRegistrationDomain.setDomains(StringUtils.join(eventLimitRegistrationDomainDTO.getDomains(), ","));
            }else {
                eventLimitRegistrationDomain.setDomains(null);
            }
            if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getContactListIds())){
                eventLimitRegistrationDomain.setContactListIds(GeneralUtils.convertLongListToCommaSeparated(eventLimitRegistrationDomainDTO.getContactListIds()));
            }else {
                eventLimitRegistrationDomain.setContactListIds(null);
            }
            eventLimitRegistrationDomain.setAllContactListsSelected(eventLimitRegistrationDomainDTO.isAllContactListsSelected());
            eventLimitRegistrationDomain.setBlocked(eventLimitRegistrationDomainDTO.isBlockedRule());
            eventLimitRegistrationDomain.setEventId(event.getEventId());
            eventLimitRegistrationDomain.setUpdatedAt(new Date());
            eventLimitRegistrationDomain.setUpdatedBy(user);
            if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getTicketTypeIds())) {
                eventLimitRegistrationDomain.setTicketTypeIds(GeneralUtils.convertLongListToCommaSeparated(eventLimitRegistrationDomainDTO.getTicketTypeIds()));
            }else{
                eventLimitRegistrationDomain.setTicketTypeIds(null);
            }
            eventLimitRegistrationDomain.setCreatedFrom(null);
            eventLimitRegistrationDomain.setRecurringEventId(recurringEventId);
            eventLimitRegistrationDomain.setEnabledRule(eventLimitRegistrationDomainDTO.isEnabledRule());
            eventLimitRegistrationDomain.setGlobalRule(eventLimitRegistrationDomain.isGlobalRule());
            eventLimitRegistrationDomainRepository.save(eventLimitRegistrationDomain);


        if (ticketing.isRecurringEvent() && recurringEventId == 0) {
            //Update the registration domain settings for all the recurring events with created from as the registration rule id
            log.info("updateRegistrationDomainSettings for all the recurring events, event id {} ", event.getEventId());
            List<EventLimitRegistrationDomain> eventLimitRegistrationDomains = new ArrayList<>();

            List<Long> recurringEventsList = recurringEventsScheduleService.getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(event.getEventId());
            List<Long> allowedTicketTypes = eventLimitRegistrationDomainDTO.getTicketTypeIds();
            List<TicketingType> ticketingTypes = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(allowedTicketTypes)){
                ticketingTypes = ticketingTypeRepository.findByCreatedFrom(allowedTicketTypes);
            }
            Map<Long, List<TicketingType>> ticketsGroupByRecurring = ticketingTypes.stream()
                    .collect(Collectors.groupingBy(TicketingType::getRecurringEventId));

            List<EventLimitRegistrationDomain> existingEventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findAllByEventIdAndCreatedFrom(event.getEventId(),id);
            List<Long> recurringEventIds = existingEventLimitRegistrationDomains.stream()
                    .filter(eventLimitRegistrationDomainRec -> eventLimitRegistrationDomainRec.getRecurringEventId() > 0)
                    .map(EventLimitRegistrationDomain::getRecurringEventId)
                    .collect(Collectors.toList());

            recurringEventsList.removeAll(recurringEventIds);

            existingEventLimitRegistrationDomains.forEach(recurringRegistrationDomain -> {
                if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getDomains())) {
                    recurringRegistrationDomain.setDomains(StringUtils.join(eventLimitRegistrationDomainDTO.getDomains(), ","));
                }
                if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomainDTO.getContactListIds())) {
                    recurringRegistrationDomain.setContactListIds(GeneralUtils.convertLongListToCommaSeparated(eventLimitRegistrationDomainDTO.getContactListIds()));
                }
                recurringRegistrationDomain.setAllContactListsSelected(eventLimitRegistrationDomainDTO.isAllContactListsSelected());
                recurringRegistrationDomain.setBlocked(eventLimitRegistrationDomainDTO.isBlockedRule());
                recurringRegistrationDomain.setEventId(event.getEventId());
                recurringRegistrationDomain.setUpdatedAt(new Date());
                if(CollectionUtils.isNotEmpty(allowedTicketTypes)){
                    recurringRegistrationDomain.setTicketTypeIds(getTicketTypeIdsComaSep(ticketsGroupByRecurring.get(recurringRegistrationDomain.getRecurringEventId())));
                }else{
                    recurringRegistrationDomain.setTicketTypeIds(null);
                }
                recurringRegistrationDomain.setUpdatedBy(user);
                recurringRegistrationDomain.setEnabledRule(eventLimitRegistrationDomainDTO.isEnabledRule());
                recurringRegistrationDomain.setGlobalRule(eventLimitRegistrationDomainDTO.isGlobalRule());
                eventLimitRegistrationDomains.add(recurringRegistrationDomain);
            });

            if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomains)) {
                eventLimitRegistrationDomainRepository.saveAll(eventLimitRegistrationDomains);
            }
        }

    }

    @Override
    @Transactional
    public EventLimitRegistrationSettingsDto getRegistrationDomainSettings(Event event, long recurringEventId) {
        log.info("getRegistrationDomainSettings eventId {} , recurringEventId {}",event.getEventId(),recurringEventId);
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<EventLimitRegistrationDomainDTO> eventLimitRegistrationDomainDTOs = new ArrayList<>();
        List<EventLimitRegistrationDomain> eventLimitRegistrationDomains;
        EventLimitRegistrationSettingsDto eventLimitRegistrationSettingsDto = new EventLimitRegistrationSettingsDto();

        //Get the registration domain settings for the event or specific recurring event and if enabled rule is true
        if(ticketing.isRecurringEvent() && recurringEventId > 0){
             eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findByEventIdAndRecurringEventIdAndEnableRule(event.getEventId(),recurringEventId);
        }
        else{
            eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findByEventIdAndRecurringEventIdAndEnableRule(event.getEventId(), 0);
        }
        if (CollectionUtils.isEmpty(eventLimitRegistrationDomains)) {
            throw new NotFoundException(NotFoundException.NotFound.RESTRICTIONS_DOMAINS_NOT_FOUND);
        }

        eventLimitRegistrationDomains.forEach(eventLimitRegistrationDomain -> {
            EventLimitRegistrationDomainDTO eventLimitRegistrationDomainDTO = new EventLimitRegistrationDomainDTO(eventLimitRegistrationDomain);
            if(eventLimitRegistrationDomainDTO.isGlobalRule()){
                //If the rule is global, then set the global rule allowed to true
                eventLimitRegistrationSettingsDto.setGlobalRuleAllowed(true);
            }
            eventLimitRegistrationDomainDTOs.add(eventLimitRegistrationDomainDTO);
        });

        eventLimitRegistrationSettingsDto.setEventLimitRegistrationDomainDTOs(eventLimitRegistrationDomainDTOs);
        return eventLimitRegistrationSettingsDto;

    }

    @Override
    public void changeTheStatus(boolean isLimitRegistration, Event event, long recurringEventId, User user) {
        log.info("changeTheStatus | LimitRegistration eventId {} , isLimitRegistration {} , recurringEventId {}, user id {}",event.getEventId(),isLimitRegistration,recurringEventId, user.getUserId());
        Ticketing ticketing = ticketingService.findByEvent(event);

        if (ticketing.isRecurringEvent() && recurringEventId > 0) {
            Optional<RecurringEvents> recurringEvents = recurringEventsScheduleService.getRecurringEventById(recurringEventId);
            if (recurringEvents.isEmpty()) {
                throw new NotFoundException(NotFoundException.EventNotFound.RECURRING_EVENT_NOT_FOUND);
            }
            RecurringEvents recurringEvent = recurringEvents.get();
            //updated the registration limit status for the specific recurring event
            recurringEvent.setLimitRegistrationEnabled(isLimitRegistration);
            recurringEventsScheduleService.save(recurringEvent);
        } else {
            if(ticketing.isRecurringEvent()) {
                //updated the registration limit status for all the recurring events as we have only one ticketing entry per event
                List<RecurringEvents> recurringEventsList = recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(event);
                recurringEventsList.forEach(e ->
                    e.setLimitRegistrationEnabled(isLimitRegistration)
                );
                recurringEventsRepository.saveAll(recurringEventsList);
            }
            //updated the registration limit status for the event
            ticketing.setLimitRegistrationEmails(isLimitRegistration);
            ticketingRepository.save(ticketing);
        }
    }

    @Override
    public void validateEmail(String email, Event event) {
        Ticketing ticketing = ticketingService.findByEvent(event);

        if (ticketing.isLimitRegistrationEmails() && isEmailNotRegisteredAsExhibitorOrSpeaker(email, event)) {
            Optional<EventLimitRegistrationDomain> eventLimitRegistrationDomainOptional =
                    eventLimitRegistrationDomainRepository.findByEventId(event.getEventId());

            if (eventLimitRegistrationDomainOptional.isPresent() && StringUtils.isNotBlank(email)) {
                EventLimitRegistrationDomain eventLimitRegistrationDomain = eventLimitRegistrationDomainOptional.get();
                 validateEmailAgainstDomainsAndContactList(email, event,eventLimitRegistrationDomain);
            }
        }
    }

    @Override
    public void removeRegistrationDomainSettings(long id, Event event, User user, long recurringEventId) {
        log.info("removeRegistrationDomainSettings eventId {} , userId {} , id {}", event.getEventId(), user.getUserId(), id);
        Ticketing ticketing = ticketingService.findByEvent(event);
        Optional<EventLimitRegistrationDomain> eventLimitRegistrationDomainOptional = eventLimitRegistrationDomainRepository.findById(id);
        if (eventLimitRegistrationDomainOptional.isEmpty()) {
            throw new NotFoundException(NotFoundException.NotFound.RESTRICTIONS_DOMAINS_NOT_FOUND);
        }

        EventLimitRegistrationDomain eventLimitRegistrationDomain = eventLimitRegistrationDomainOptional.get();

        eventLimitRegistrationDomain.setRecordStatus(RecordStatus.DELETE);
        eventLimitRegistrationDomain.setEnabledRule(false);
        eventLimitRegistrationDomain.setUpdatedBy(user);
        eventLimitRegistrationDomain.setUpdatedAt(new Date());
        eventLimitRegistrationDomainRepository.save(eventLimitRegistrationDomain);

        if (ticketing.isRecurringEvent() && recurringEventId == 0) {
            //Remove the registration domain settings for all the recurring events with created from as the registration rule id
            List<EventLimitRegistrationDomain> eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findAllByEventIdAndCreatedFrom(event.getEventId(), id);
            eventLimitRegistrationDomains.add(eventLimitRegistrationDomain);
            if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomains)) {
                eventLimitRegistrationDomains.forEach(eventLimitRegistrationDomainRecurring -> {
                    eventLimitRegistrationDomainRecurring.setRecordStatus(RecordStatus.DELETE);
                    eventLimitRegistrationDomainRecurring.setUpdatedBy(user);
                    eventLimitRegistrationDomainRecurring.setEnabledRule(false);
                    eventLimitRegistrationDomainRecurring.setUpdatedAt(new Date());
                });
                eventLimitRegistrationDomainRepository.saveAll(eventLimitRegistrationDomains);
            }
        }
    }

    @Override
    public boolean getStatusOfLimitRegistration(Event event, long recurringEventId) {
        log.info("getStatusOfLimitRegistration eventId {} , recurringEventId {}",event.getEventId(),recurringEventId);
        Ticketing ticketing = ticketingService.findByEvent(event);
        if (ticketing.isRecurringEvent() && recurringEventId > 0) {
            return recurringEventsScheduleService.getRecurringEventById(recurringEventId)
                    .map(RecurringEvents::isLimitRegistrationEnabled)
                    .orElseThrow(() -> new NotFoundException(NotFoundException.EventNotFound.RECURRING_EVENT_NOT_FOUND));
        }
        else {
            return ticketing.isLimitRegistrationEmails();
        }
    }

    @Override
    public void updateRegistrationRuleStatus(boolean isGlobalRuleApply, Event event, long recurringEventId, User user) {
        log.info("updateRegistrationRuleStatus eventId {} , isGlobalRuleApply {} , recurringEventId {}, user id {}",event.getEventId(),isGlobalRuleApply,recurringEventId, user.getUserId());
        Ticketing ticketing = ticketingService.findByEvent(event);
        List<EventLimitRegistrationDomain> eventLimitRegistrationDomains;
        if (ticketing.isRecurringEvent() && recurringEventId > 0) {
            eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findByEventIdAndRecurringEventId(event.getEventId(),recurringEventId);
            eventLimitRegistrationDomains.forEach(eventLimitRegistrationDomain ->
                eventLimitRegistrationDomain.setCreatedFrom(null));
        }
        else{
            eventLimitRegistrationDomains = eventLimitRegistrationDomainRepository.findAllByEventId(event.getEventId());
        }

        if (CollectionUtils.isNotEmpty(eventLimitRegistrationDomains)) {
            eventLimitRegistrationDomains.forEach(eventLimitRegistrationDomain -> {
                if (eventLimitRegistrationDomain.isGlobalRule()) {
                    eventLimitRegistrationDomain.setEnabledRule(isGlobalRuleApply);
                } else {
                    eventLimitRegistrationDomain.setEnabledRule(!isGlobalRuleApply);
                }
            });
            eventLimitRegistrationDomainRepository.saveAll(eventLimitRegistrationDomains);
        }
    }

    @Override
    public void deleteByRecurringEventIdIn(List<Long> recurringEventsToBeDelete) {
        eventLimitRegistrationDomainRepository.deleteByRecurringEventIdIn(recurringEventsToBeDelete);
    }

    @Override
    public List<EventLimitRegistrationDomain> getAllEventLimitRegistrationDomainByEventAndCreatedFromNullAndRecurringEventIdIsZero(Event event) {
        return eventLimitRegistrationDomainRepository.findByEventIdAndRecurringEventIdAndCreatedFromNull(event.getEventId(), 0);
    }


    private void validateEmailAgainstDomainsAndContactList(String email, Event event, EventLimitRegistrationDomain eventLimitRegistrationDomain) {
        log.info("validateEmailAgainstDomainsAndContactList email {}, eventId {}, contactsListIds {}", email, event.getEventId(), eventLimitRegistrationDomain.getContactListIds());

        boolean isAllowed = false;

        // Check against contact list
        if (StringUtils.isNotBlank(eventLimitRegistrationDomain.getContactListIds())) {
            List<Long> listOfContactsListIds = GeneralUtils.convertCommaSeparatedToListLong(eventLimitRegistrationDomain.getContactListIds());
            if (!listOfContactsListIds.isEmpty()) {
                boolean isEmailInContactList = contactsRepository.existsEmailsByEventIdAndContactsListIds(event.getEventId(), listOfContactsListIds, email);

                if (eventLimitRegistrationDomain.isBlocked() && isEmailInContactList) {
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.REGISTRATION_IS_NOT_PERMITTED);
                } else if (!eventLimitRegistrationDomain.isBlocked() && isEmailInContactList) {
                    isAllowed = true;
                }
            }
        }

        // Check against domains if not already allowed
        if (!isAllowed && StringUtils.isNotBlank(eventLimitRegistrationDomain.getDomains())) {
            List<String> listOfDomains = GeneralUtils.convertCommaSeparatedToList(eventLimitRegistrationDomain.getDomains());
            if (!listOfDomains.isEmpty()) {
                boolean isEmailInDomain = listOfDomains.stream()
                        .anyMatch(domain -> email.toLowerCase().endsWith(domain.toLowerCase()));

                if (eventLimitRegistrationDomain.isBlocked() && isEmailInDomain) {
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.REGISTRATION_IS_NOT_PERMITTED);
                } else if (!eventLimitRegistrationDomain.isBlocked() && isEmailInDomain) {
                    isAllowed = true;
                }
            }
        }

        if (!isAllowed && !eventLimitRegistrationDomain.isBlocked()) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.REGISTRATION_IS_NOT_PERMITTED);
        }
    }



    private boolean isEmailNotRegisteredAsExhibitorOrSpeaker(String email, Event event){
        User user = roUserService.findByEmail(email);
        if (null != user){
            List<String> userRoles = staffService.getAllRolesWithVirtualAndHybridTickets(event.getEventId(), user.getUserId());
            if (!userRoles.isEmpty()){
                return  !(userRoles.contains(StaffRole.exhibitoradmin.name()) || userRoles.contains(EnumLabel.SPEAKER.getLabelName()));
            }
        }
        return true;
    }

}
