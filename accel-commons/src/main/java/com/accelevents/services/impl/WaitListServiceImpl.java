package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.*;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotAcceptableException.WaitListExceptionMsg;
import com.accelevents.helpers.EmailImageHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.messages.WaitListStatus;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.repositories.WaitListRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.waitlist.ROWaitListSettingService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO;
import com.accelevents.utils.Constants;
import com.accelevents.utils.SecurityUtils;
import com.accelevents.utils.TimeZoneUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;

@Service
public class WaitListServiceImpl implements WaitListService {

    private static final Logger log = LoggerFactory.getLogger(WaitListServiceImpl.class);

    @Value("${apiBaseUrl}")
    private String apiBaseUrl;

    @Autowired
    private WaitListRepository waitListRepository;

    @Autowired
    private WaitListSettingService waitListSettingService;

    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Autowired
    private EventService eventService;
    @Autowired
    private ROEventService roEventService;

    @Autowired
    @Lazy
    private TicketingTypeService ticketingTypeService;

    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;

    @Autowired
    private TicketingRepository ticketingRepository;

    @Autowired private ROWhiteLabelService roWhiteLabelService;

    @Autowired private StaffService staffService;

    @Autowired private SendGridMailService sendGridMailService;

    @Autowired
    private EventDesignDetailService eventDesignDetailService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;

    private ImageConfiguration imageConfiguration;
    private EmailImageHelper emailImageHelper;

    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;

    @Autowired
    private ROWaitListSettingService roWaitListSettingService;

    @Autowired
    public WaitListServiceImpl(ImageConfiguration imageConfiguration) {
        emailImageHelper = new EmailImageHelper(imageConfiguration.getImagePrefix(),
                imageConfiguration.getCloudinaryUrl(), imageConfiguration.getImagesAcceleventlogo());
        this.imageConfiguration=imageConfiguration;
    }

    public WaitListServiceImpl() {

    }

    private static final double SEQUENCE = 1000d;

    @Transactional
    @Override
    public void reorderWaitList(Event event, Long waitlistId, Long topId, Long bottomId) {
        WaitList waitList = waitListRepository.findById(waitlistId)
                .orElseThrow(() -> new NotAcceptableException(WaitListExceptionMsg.WAITLIST_NOT_FOUND));

        log.info("Reordering waitlistId={} for eventId={}, topId={}, bottomId={}",
                waitlistId, event.getEventId(), topId, bottomId);

        Double newPosition = computeNewPosition(topId, bottomId, event.getEventId());

        if (newPosition.equals(waitList.getPosition())) {
            log.warn("New position {} is same as current {} for waitlistId={}. Normalizing...",
                    newPosition, waitList.getPosition(), waitlistId);

            normalizePositions(event.getEventId());
            newPosition = computeNewPosition(topId, bottomId, event.getEventId());

            if (newPosition.equals(waitList.getPosition())) {
                log.error("Unable to reorder waitlistId={} after normalization. EventId={}",
                        waitlistId, event.getEventId());
                throw new NotAcceptableException(WaitListExceptionMsg.UNABLE_TO_REORDER_WAITLIST);
            }
        }

        log.info("Updating waitlistId={} from oldPosition={} to newPosition={}",
                waitlistId, waitList.getPosition(), newPosition);
        waitList.setPosition(newPosition);
        saveAndClearCache(waitList);
    }

    private Double computeNewPosition(Long beforeId, Long afterId, Long eventId) {
        double newPosition;
        if (beforeId != null && afterId != null) {
            WaitList before = waitListRepository.findById(beforeId)
                    .orElseThrow(() -> new NotAcceptableException(WaitListExceptionMsg.WAITLIST_NOT_FOUND));
            WaitList after = waitListRepository.findById(afterId)
                    .orElseThrow(() -> new NotAcceptableException(WaitListExceptionMsg.WAITLIST_NOT_FOUND));
            newPosition = (before.getPosition() + after.getPosition()) / 2;
        } else if (beforeId != null) {
            WaitList before = waitListRepository.findById(beforeId)
                    .orElseThrow(() -> new NotAcceptableException(WaitListExceptionMsg.WAITLIST_NOT_FOUND));
            newPosition = before.getPosition() + SEQUENCE;
        } else if (afterId != null) {
            WaitList after = waitListRepository.findById(afterId)
                    .orElseThrow(() -> new NotAcceptableException(WaitListExceptionMsg.WAITLIST_NOT_FOUND));
            newPosition = after.getPosition() - SEQUENCE;
        } else {
            throw new NotAcceptableException(WaitListExceptionMsg.REORDER_ARGUMENTS_INVALID);
        }

        if(waitListRepository.existsByEventIdAndPosition(eventId, newPosition)){
            log.warn("Collision detected at position={} for eventId={}. Shifting positions.", newPosition, eventId);
            waitListRepository.shiftPositions(eventId, newPosition, SEQUENCE);
        }

        return newPosition;
    }

    @Transactional
    @Override
    public void normalizePositions(Long eventId) {
        List<WaitList> waitLists = waitListRepository.findByEventIdOrderByPositionAsc(eventId);
        log.info("Normalizing positions for eventId={}, totalRecords={}", eventId, waitLists.size());

        double position = SEQUENCE;
        for (WaitList waitList : waitLists) {
            waitList.setPosition(position);
            position += SEQUENCE;
        }
        log.info("Normalization complete for eventId={}. Records reset with step={}", eventId, SEQUENCE);
        saveAllAndClearCache(waitLists);
    }

    @Override
    public List<WaitListUpdateDto> getWaitListByEvent(Event event) {
        if(isWaitListEnabled(event)){
            List<WaitList> waitList = waitListRepository.findByEventId(event.getEventId());
            List<WaitListUpdateDto> waitListUpdateDtos = new ArrayList<>();
            waitList.forEach(e->{
                WaitListUpdateDto dto = new WaitListUpdateDto(e);
                waitListUpdateDtos.add(dto);
            });
            return waitListUpdateDtos;
        } else {
            throw new NotAcceptableException(WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED);
        }
    }

    @Override
    public DataTableResponse getWaitListByEvent(Event event, Pageable pageable) {
        if (!isWaitListEnabled(event)) {
            throw new NotAcceptableException(WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED);
        }

        Page<WaitList> waitLists = waitListRepository.findByEventIdOrderByPositionAsc(event.getEventId(), pageable);

        Page<WaitListUpdateDto> waitListUpdateDto = waitLists.map(WaitListUpdateDto::new);

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(waitListUpdateDto.getContent());
        dataTableResponse.setRecordsTotal(waitListUpdateDto.getTotalElements());
        dataTableResponse.setRecordsFiltered(waitListUpdateDto.getNumberOfElements());

        return dataTableResponse;
    }

    @Override
    @Transactional
    public void save(Event event, WaitListDto waitListDto,Long recurringEventId) {
        if(isWaitListEnabled(event)){
            WaitListSettingsDto waitListSettings = waitListSettingService.getWaitListSettingsByEvent(event,recurringEventId);
            long count = waitListRepository.countAllByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
            long size = count + waitListDto.getNumberOfTickets();

            if(waitListSettings.getMaxWaitListSize() != 0 && size > waitListSettings.getMaxWaitListSize()){
                long spotCount = waitListSettings.getMaxWaitListSize() - count;
                NotAcceptableException.WaitListExceptionMsg exceptionMsg = WaitListExceptionMsg.WAIT_LIST_LIMIT_EXCEED;

                exceptionMsg.setErrorMessage(Constants.WAITLIST_LIMIT_EXCEEDED.replace("{SPOT_COUNT}", String.valueOf(spotCount)));
                exceptionMsg.setDeveloperMessage(Constants.WAITLIST_LIMIT_EXCEEDED.replace("{SPOT_COUNT}", String.valueOf(spotCount)));
                throw new NotAcceptableException(exceptionMsg);
            }
            double maxPosition = Optional.ofNullable(
                    waitListRepository.findMaxPositionByEventId(event.getEventId())
            ).orElse(0d);
            for (int waitListEntry = 0; waitListEntry < waitListDto.getNumberOfTickets() ; waitListEntry++){
                WaitList waitList = new WaitList(waitListDto);
                maxPosition += SEQUENCE;
                waitList.setPosition(maxPosition);
                waitList.setTimeAddedToList(new Date());
                waitList.setEventId(event.getEventId());
                saveAndClearCache(waitList);
            }

            snedConfirmationEmail(waitListDto, event, waitListSettings);
        } else {
            throw new NotAcceptableException(WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED);
        }
    }

    private void snedConfirmationEmail(WaitListDto waitListDto, Event event, WaitListSettingsDto waitListSettings) {
        Set<String> receivers = new HashSet<>();
        receivers.add(waitListDto.getEmail());

        EmailMessage emailMessage = new EmailMessage(TemplateId.TRANSACTIONAL_DUMMY);

        String replyToEmail = null;
        if (event.getWhiteLabel() != null) {
            Optional<WhiteLabel> whiteLabelOptional = roWhiteLabelService.getWhiteLabelById(event.getWhiteLabel().getId());
            if (whiteLabelOptional.isPresent()) {
                WhiteLabel whiteLabel = whiteLabelOptional.get();
                Staff staff = staffService.findTopByRoleAndWhiteLabelOrderById(StaffRole.whitelabeladmin, whiteLabel);
                if(staff != null && staff.getUser() != null){
                    replyToEmail = staff.getUser().getEmail();
                }
                emailMessage.setWhiteLabel(whiteLabel);
            }
        }

        EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        if (StringUtils.isEmpty(replyToEmail)){
            replyToEmail = eventDesignDetail.getReplyToEmail();
        }
        emailMessage.setReplyToEmail(replyToEmail);
        emailMessage.setSenderNameFromEvent(eventDesignDetail.getEmailSenderName());
        emailMessage.setSenderMailFromEvent(eventDesignDetail.getNotificationEmail());

        if(StringUtils.isEmpty(waitListSettings.getWaitListTicketConfirmationSubject())){
            emailMessage.setSubject("Join Waitlist Confirmation Message");
        }
        else{
            emailMessage.setSubject(waitListSettings.getWaitListTicketConfirmationSubject());
        }

        Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();

        substitutionMap.put(IMAGE_LOWER, emailImageHelper.getEmailLogoImage(eventDesignDetailService.getEventOrWhiteLabelLogoLocation(event)));
        substitutionMap.put(HEADER_IMAGE, emailImageHelper.getHeaderImageForEmail(eventDesignDetailService.getEventOrWhiteLabelHeaderLogoLocation(event)));
        substitutionMap.put(EVENT_UC_NAME, event.getName());

        substitutionMap.put("waitListTicketConfirmationMessage", StringUtils.isNoneBlank(waitListSettings.getWaitListTicketConfirmationMessage()) ?
                waitListSettings.getWaitListTicketConfirmationMessage() : Constants.STRING_EMPTY);

        emailMessage.setTemplate(prepareDescription());
        substitutionMap.put(Constants.EVENT_ID_UPPERCASE, event.getEventId());
        substitutionMap.put(USER_NAME, waitListDto.getFirstName() +Constants.STRING_BLANK + waitListDto.getLastName());
        sendGridMailService.sendTemplateMail(emailMessage, receivers);
    }

    private String prepareDescription(){
        return "<!DOCTYPE html><html xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" lang=\"en\"><head><title></title><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1\"><style>*{box-sizing:border-box}body{margin:0;padding:0}th.column{padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}@media (max-width:620px){.row-content{width:100%!important}.stack .column{width:100%;display:block}}</style></head><body style=\"background-color:#ececec;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none\"><table class=\"nl-container\" width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0;background-color:#ececec\"><tbody><tr><td><table class=\"row row-1\" align=\"center\" width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0\"><tbody><tr><td><table class=\"row-content stack\" align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0;background-color:#fff\" width=\"600\"><tbody><tr><th class=\"column\" width=\"100%\" style=\"mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:10px;padding-bottom:5px\"><table class=\"text_block\" width=\"100%\" border=\"0\" cellpadding=\"10\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0;word-break:break-word\"><tr><td><div style=\"font-family:sans-serif\"><div style=\"font-size:12px;color:#000;line-height:1.5;font-family:Arial,Helvetica Neue,Helvetica,sans-serif\"><p style=\"margin:0;font-size:12px;text-align:left;mso-line-height-alt:21px\"><span style=\"font-size:14px\">${headerImage}</span></p></div></div></td></tr></table><table class=\"text_block\" width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0;word-break:break-word\"><tr><td style=\"padding-left:10px;padding-right:10px\"><div style=\"font-family:sans-serif\"><div style=\"font-size:12px;color:#000;line-height:1.5;font-family:Arial,Helvetica Neue,Helvetica,sans-serif\"><p style=\"margin:0;font-size:12px;mso-line-height-alt:21px\"><span style=\"font-size:14px\">${image}</span></p></div></div></td></tr></table><table class=\"text_block\" width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0;word-break:break-word\"><tr><td style=\"padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:20px\"><div style=\"font-family:sans-serif\"><div style=\"font-size:14px;color:#000;line-height:1.5;font-family:Arial,Helvetica Neue,Helvetica,sans-serif\"><p style=\"margin:0;font-size:14px;padding-bottom:10px\">Hello ${userName},</p><p style=\"margin:0;font-size:14px\">Thank you! We have received your request to purchase tickets. You will receive a separate email once there are available tickets.</p></div></div></td></tr></table><table class=\"text_block\" width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0;word-break:break-word\"><tr><td style=\"padding-left:10px;padding-right:10px\"><div style=\"font-family:sans-serif\"><div style=\"font-size:12px;color:#000;line-height:1.5;font-family:Arial,Helvetica Neue,Helvetica,sans-serif\"><p style=\"margin:0;font-size:12px;mso-line-height-alt:21px\"><span style=\"font-size:14px\">${waitListTicketConfirmationMessage}</span></p></div></div></td></tr></table><table class=\"divider_block\" width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"mso-table-lspace:0;mso-table-rspace:0\"><tr><td style=\"padding-bottom:20px;padding-left:10px;padding-right:10px;padding-top:10px\"><div align=\"center\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" width=\"100%\" style=\"mso-table-lspace:0;mso-table-rspace:0\"><tr><td class=\"divider_inner\" style=\"font-size:1px;line-height:1px;border-top:2px solid #000\"><span></span></td></tr></table></div></td></tr></table></th></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></body></html>";
    }
    @Override
    @Transactional
    public void update(Long waitListId, Event event, WaitListUpdateDto waitListDto) {
        WaitList waitList = waitListRepository.findByWaitListIdAndEventId(waitListId, event.getEventId());
        if (waitList == null){
            throw new NotAcceptableException(WaitListExceptionMsg.WAIT_LIST_NOT_FOUND);
        }
        if(isWaitListEnabled(event)){
            waitList = new WaitList(waitListDto);
            waitList.setEventId(event.getEventId());
            waitList.setWaitListId(waitListId);
            saveAndClearCache(waitList);
        } else {
            throw new NotAcceptableException(WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED);
        }
    }

    private boolean isWaitListEnabled(Event event) {
        WaitListSettingsDto waitListSettings = waitListSettingService.getWaitListSettingsByEvent(event,0L);
        return waitListSettings != null && waitListSettings.isWaitListEnabled();
    }

    @Override
    @Transactional
    public void remove(Long waitListId, Event event) {
        if(isWaitListEnabled(event)){
            waitListRepository.deleteById(waitListId);
        } else {
            throw new NotAcceptableException(WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED);
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void checkWaitList() {
        List<WaitListSettings> waitListSettingsList = waitListSettingService.getAllWaitListSettings();
        List<Long> listOfEventId = waitListSettingsList.stream().map(WaitListSettings::getEventId).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(listOfEventId)) {
            List<Event> events = roEventService.getEventListByIds(listOfEventId);
            Map<Long, Event> eventListMap = events.stream().collect(Collectors.toMap(Event::getEventId,event->event));

            List<WaitList> waitListAll = waitListRepository.findByEventIdInAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(listOfEventId, WaitListStatus.WAITING, false);
            Map<Long, List<WaitList>> waitListMapByEventId = waitListAll.stream().collect(Collectors.groupingBy(WaitList::getEventId));

            for (WaitListSettings waitListSettings : waitListSettingsList) {
                long eventId = waitListSettings.getEventId();
                try {
                    String waitListTicketReleaseMessage = waitListSettings.getWaitListTicketReleaseMessage();
                    Event event = eventListMap.get(eventId);
                    List<WaitList> waitList = waitListMapByEventId.get(eventId);
                    Map<String, WaitListMailData> mailMap = new HashMap<>();

                    if(CollectionUtils.isNotEmpty(waitList)) {
                        Map<Long, List<WaitList>> waitListMap = waitList.stream().collect(Collectors.groupingBy(WaitList::getTicketingTypeId));
                        for (Long ticketingTypeId : waitListMap.keySet()) {
                            List<WaitList> waitLists = waitListMap.get(ticketingTypeId);

                            int totalTicketsAvailable = getAvailableTickets(ticketingTypeId, event);
                            int numberOfTicketsInWaitList = this.countByEventIdAndStatusAndEmailSentAndTicketingTypeId(event, WaitListStatus.WAITING, true, ticketingTypeId);
                            int availableTickets = totalTicketsAvailable - numberOfTicketsInWaitList;

                            availableTickets = getAvailableTicketsForEventCapacityTriggerType(waitListSettings, event, availableTickets);

                            for (WaitList waitListObj : waitLists) {
                                if (availableTickets > 0) {
                                    Date from = waitListObj.getTimeTicketsBecameAvailable();
                                    // in minutes
                                    int timeToRespond = waitListSettings.getTimeToRespond();

                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(from);
                                    calendar.add(Calendar.MINUTE, timeToRespond);

                                    long diffInMilliSec = calendar.getTime().getTime() - TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone()).getTime();
                                    if (diffInMilliSec > 0) {
                                        long remainingMins = (diffInMilliSec / 60000);
                                        String email = waitListObj.getEmail();

                                        WaitListMailData waitListMailData = mailMap.get(email);
                                        String checkoutLink = String.valueOf(waitListObj.getWaitListId());
                                        if (waitListMailData != null) {
                                            waitListMailData.setCheckoutLink(waitListMailData.getCheckoutLink() + Constants.STRING_COMMA + checkoutLink);
                                            waitListMailData.setNumberOfTickets(waitListMailData.getNumberOfTickets() + 1);
                                        } else {
                                            waitListMailData = new WaitListMailData(remainingMins, checkoutLink, 1);
                                        }
                                        mailMap.put(email, waitListMailData);
                                        availableTickets = availableTickets - 1;
                                    }
                                }
                            }
                        }
                    }
                    sendWaitListEmail(waitListTicketReleaseMessage,waitListSettings.getWaitListTicketReleaseSubject(), event, mailMap);
                } catch (Exception e) {
                    log.error("Exception while processing WAITLIST trigger for event details {}",eventId);
                    e.printStackTrace();
                }
            }
        }
    }

    private int getAvailableTicketsForEventCapacityTriggerType(WaitListSettings waitListSettings, Event event, int availableTickets) {
        if(availableTickets > 0 && waitListSettings.getWaitListTrigger() == 0){

            Map<Long, BigDecimal> ticketSoldCount =  eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId());

            Ticketing ticketing = ticketingRepository.findByEventid(event);
            int eventNumberOfTicketsInWaitList = this.countByEventIdAndStatusAndEmailSentAndTicketingTypeId(event, WaitListStatus.WAITING, true, 0);
            if(  ticketing.isLimitEventCapacity()) {
                if (ticketing.isRecurringEvent()) {
                    List<RecurringEventsTicketingTypeAndSoldCountDTO> eventIdAndSoldCountDTO = eventTicketsRepoService.countNumberOfSoldTicketForRecurringEventsByEventId(event.getEventId());
                    if(!CollectionUtils.isEmpty(eventIdAndSoldCountDTO) &&
                            eventIdAndSoldCountDTO.stream().allMatch(e -> Boolean.TRUE.equals(e.isEventCapacityReach()))){
                        availableTickets = 0;
                    }
                } else {
                    BigDecimal totalSoldTickets = ticketSoldCount.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    availableTickets = (int) (ticketing.getEventCapacity() - (eventNumberOfTicketsInWaitList + totalSoldTickets.doubleValue()));
                }
            }
        }
        return availableTickets;
    }

    private void sendWaitListEmail(String waitListTicketReleaseMessage, String waitListTicketReleaseSubject, Event event, Map<String, WaitListMailData> mailMap) {
        try {
            mailMap.forEach((key, waitListMailData) -> {
                String checkoutLink = waitListMailData.getCheckoutLink();
                sendGridMailPrepareService.sendWaitListEmail(key, event, waitListMailData.getNumberOfTickets(), SecurityUtils.encode(checkoutLink), waitListTicketReleaseMessage, waitListTicketReleaseSubject);
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void releaseWaitList(List<Long> waitListIds,Event event) {
        WaitListSettings waitListSettings = roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId());
        List<WaitList> waitList = waitListRepository.findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(waitListIds,WaitListStatus.WAITING);
        Map<Long, List<WaitList>> waitListMap = waitList.stream().collect(Collectors.groupingBy(item -> item.getTicketingTypeId()));
        Map<String, WaitListMailData> mailMap = new HashMap<>();
        for(Long ticketingTypeId : waitListMap.keySet()){
            List<WaitList> waitLists = waitListMap.get(ticketingTypeId);

            int totalTicketsAvailable = getAvailableTickets(ticketingTypeId, event);
            int numberOfTicketsInWaitList = this.countByEventIdAndStatusAndEmailSentAndTicketingTypeId(event, WaitListStatus.WAITING, true, ticketingTypeId);
            int availableTickets = totalTicketsAvailable - numberOfTicketsInWaitList;

            if(availableTickets <= 0 || waitLists.size() > availableTickets){
                throw new NotAcceptableException(WaitListExceptionMsg.RELEASE_LIST_LIMIT_EXCEED);
            }

            if(waitListSettings.getWaitListTrigger() == 0){

                Map<Long, BigDecimal> ticketSoldCount =  eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId());

                Ticketing ticketing = ticketingRepository.findByEventid(event);
                boolean isEventCapacityReach = false;
                int eventNumberOfTicketsInWaitList = this.countByEventIdAndStatusAndEmailSentAndTicketingTypeId(event, WaitListStatus.WAITING, true, 0);
                if(ticketing.isLimitEventCapacity()) {
                    if (ticketing.isRecurringEvent()) {
                        List<RecurringEventsTicketingTypeAndSoldCountDTO> eventIdAndSoldCountDTO = eventTicketsRepoService.countNumberOfSoldTicketForRecurringEventsByEventId(event.getEventId());
                        isEventCapacityReach = !CollectionUtils.isEmpty(eventIdAndSoldCountDTO) &&
                                eventIdAndSoldCountDTO.stream().allMatch(e -> Boolean.TRUE.equals(e.isEventCapacityReach()));
                    } else {
                        BigDecimal totalSoldTickets = ticketSoldCount.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        isEventCapacityReach = ticketing.getEventCapacity() - eventNumberOfTicketsInWaitList <= totalSoldTickets.doubleValue() ;
                    }
                }
                if(isEventCapacityReach){
                    throw new NotAcceptableException(WaitListExceptionMsg.EVENT_RELEASE_LIST_LIMIT_EXCEED);
                }
            }

            for (WaitList waitListObj : waitLists) {
                String email = waitListObj.getEmail();
                if (availableTickets > 0) {
                    waitListObj.setTimeTicketsBecameAvailable(new Date());
                    saveAndClearCache(waitListObj);
                    availableTickets = availableTickets - 1;

                    WaitListMailData waitListMailData = mailMap.get(email);
                    String checkoutLink = String.valueOf(waitListObj.getWaitListId());
                    if (waitListMailData != null) {
                        waitListMailData.setCheckoutLink(waitListMailData.getCheckoutLink() + Constants.STRING_COMMA + checkoutLink);
                        waitListMailData.setNumberOfTickets(waitListMailData.getNumberOfTickets() + 1);
                    } else {
                        waitListMailData = new WaitListMailData(checkoutLink, 1);
                    }
                    mailMap.put(email, waitListMailData);
                }
            }
            mailMap.forEach((key, waitListMailData) -> {
                String checkoutLink = waitListMailData.getCheckoutLink();
                sendGridMailPrepareService.sendWaitListEmail(key, event, waitListMailData.getNumberOfTickets(), SecurityUtils.encode(checkoutLink), waitListSettings.getWaitListTicketReleaseMessage(),waitListSettings.getWaitListTicketReleaseSubject());
                List<Long> waitListLongIds = Arrays.stream(checkoutLink.split(STRING_COMMA)).filter(s -> StringUtils.isNoneBlank(s) && !"null".equalsIgnoreCase(s)).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                waitListRepository.markWaitListAsPaidWhereIdsIn(waitListLongIds);
            });
        }
    }

    private int getAvailableTickets(long ticketingTypeId, Event event) {
        if(ticketingTypeId > 0) {
            return ticketingStatisticsService.getAvailableTicketsForTicketType(event, ticketingTypeId);
        } else {
            return ticketingStatisticsService.getAvailableTicketsForFullEvent(event);
        }
    }

    @Override
    @Transactional
    public void checkWaitListExpiration() {
        List<WaitList> waitLists = getWaitListByStatus(WaitListStatus.WAITING);
        List<Long> eventIds = waitLists.stream().map(WaitList::getEventId).distinct().collect(Collectors.toList());
        List<WaitListSettings> waitListSettingsList = waitListSettingService.getAllWaitListSettingsByEventIds(eventIds);
        Map<Long, WaitListSettings> waitListSettingsMap = waitListSettingsList.stream().collect(Collectors.toMap(WaitListSettings::getEventId, Function.identity()));
        List<WaitList> waitListsExpired = new ArrayList<>();
        List<WaitList> waitListsNew = new ArrayList<>();
        Map<Long,Double> maxPositions = new HashMap<>();
        eventIds.forEach(eventId -> {

                    double maxPosition = Optional.ofNullable(
                            waitListRepository.findMaxPositionByEventId(eventId)
                    ).orElse(0d);
                    maxPositions.put(eventId, maxPosition);
                });
        waitLists.forEach(waitListObj -> {
            Date available = waitListObj.getTimeTicketsBecameAvailable();
            if (available != null) {
                WaitListSettings waitListSettings = waitListSettingsMap.get(waitListObj.getEventId());
                if(waitListSettings!=null){
                    int timeToRespond = waitListSettings.getTimeToRespond();

                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(available);
                    calendar.add(Calendar.MINUTE, timeToRespond);

                    if(calendar.getTime().before(new Date())){
                        double maxPosition = maxPositions.getOrDefault(waitListObj.getEventId(),0d) + SEQUENCE;
                        maxPositions.put(waitListObj.getEventId(), maxPosition);
                        waitListObj.setStatus(WaitListStatus.TICKET_PURCHASE_EXPIRED);
                        waitListsExpired.add(waitListObj);
                        WaitList waitList = new WaitList();
                        waitList.setStatus(WaitListStatus.WAITING);
                        waitList.setTimeAddedToList(new Date());
                        waitList.setEventId(waitListObj.getEventId());
                        waitList.setEmail(waitListObj.getEmail());
                        waitList.setEmailSent(false);
                        waitList.setTicketingTypeId(waitListObj.getTicketingTypeId());
                        waitList.setFirstName(waitListObj.getFirstName());
                        waitList.setLastName(waitListObj.getLastName());
                        waitList.setCountryCode(waitListObj.getCountryCode());
                        waitList.setTimeTicketsBecameAvailable(null);
                        waitList.setPhoneNumber(waitListObj.getPhoneNumber());
                        waitList.setPosition(maxPosition);
                        waitListsNew.add(waitList);
                    }
                }
            }
        });

        if (!waitListsExpired.isEmpty()) {
            saveAllAndClearCache(waitListsExpired);
        }
        if (!waitListsNew.isEmpty()) {
           saveAllAndClearCache(waitListsNew);
        }
    }

    private List<WaitList> getWaitListByStatus(WaitListStatus waitListStatus) {
        return waitListRepository.findByStatus(waitListStatus);
    }

    @Override
    public List<WaitList> findByEventAndStatus(Event event, WaitListStatus waitListStatus) {
        return waitListRepository.findByEventIdAndStatus(event.getEventId(), waitListStatus);
    }

    @Override
    public int countByEventIdAndStatus(Event event, WaitListStatus waitListStatus){
        return waitListRepository.countByEventIdAndStatus(event.getEventId(), waitListStatus);
    }

    @Override
    public List<WaitList> findByEventAndStatusAndIsEmailSent(Event event, WaitListStatus waitListStatus, boolean isEmailSent) {
        return waitListRepository.findByEventIdAndStatusAndEmailSent(event.getEventId(), waitListStatus, isEmailSent);
    }

    @Override
    public int countByEventIdAndStatusAndEmailSentAndTicketingTypeId(Event event, WaitListStatus waitListStatus, boolean isEmailSent, long ticketingTypeId) {
        return waitListRepository.countByEventIdAndStatusAndEmailSentAndTicketingTypeId(event.getEventId(), waitListStatus, isEmailSent, ticketingTypeId);
    }

    @Override
    public int countByEventIdAndStatusAndEmailSent(Event event, WaitListStatus waitListStatus, boolean isEmailSent) {
        return waitListRepository.countByEventIdAndStatusAndEmailSent(event.getEventId(), waitListStatus, isEmailSent);
    }

    @Override
    public List<WaitList> findByEventAndStatusAndEmail(Event event, WaitListStatus waitListStatus, String email) {
        return waitListRepository.findByEventIdAndStatusAndEmail(event.getEventId(), waitListStatus, email);
    }

    @Override
    public List<WaitList> findByEventAndStatusAndEmailAndTicketingTypeId(Event event, WaitListStatus waitListStatus, String email, long ticketingTypeId) {
        return waitListRepository.findByEventIdAndStatusAndEmailAndTicketingTypeId(event.getEventId(), waitListStatus, email, ticketingTypeId);
    }

    @Override
    @Async
    public void checkTicketsAvailability() {
        log.info("checkWaitList started");
        waitListSettingService.getAllWaitListSettingsIds()
                .forEach(waitListSettingsId -> {
                    try {
                        HttpResponse<JsonNode> response = Unirest.post(apiBaseUrl + "/scheduler/checkWaitListAvailability/" + waitListSettingsId)
                                .header("Content-Type", "application/json")
                                .header("Origin", "Accel Scheduler")
                                .asJson();
                        log.info("From all wait list settings /scheduler/checkWaitListAvailability/{} API called", waitListSettingsId);
                        log.info("Response for waitListSettingsId {}", response.getBody().getObject().getString("message"));
                    }catch (Exception ex){
                        log.error("Error during calling wait-list settings API ", ex);
                    }
                });
        log.info("checkWaitList completed");
    }

    @Override
    @Async
    public void handleWaitListTriggerForGivenId(Long waitListSettingId) {
        log.info("checkWaitList started for waitListSettingId: {}", waitListSettingId);
        try {
            Optional<WaitListSettings> waitListSettingsOptional = waitListSettingService.findById(waitListSettingId);
            if (waitListSettingsOptional.isPresent()) {
                WaitListSettings waitListSettings = waitListSettingsOptional.get();
                long eventId = waitListSettings.getEventId();
                Event event = roEventService.getEventById(eventId);
                String waitListTicketReleaseMessage = waitListSettings.getWaitListTicketReleaseMessage();

                log.info("Waitlist_trigger {} from waitlist setting {}", waitListSettings.getWaitListTrigger(), waitListSettingId);
                if (waitListSettings.getWaitListTrigger() > 0) {
                    List<WaitList> waitList = waitListRepository.findByEventIdAndStatusAndEmailSentOrderByPositionAsc(eventId, WaitListStatus.WAITING, false);
                    Map<Long, List<WaitList>> waitListMap = waitList.parallelStream().collect(Collectors.groupingBy(WaitList::getTicketingTypeId));

                    for (Long ticketingTypeId : waitListMap.keySet()) {
                        List<WaitList> waitLists = waitListMap.get(ticketingTypeId);

                        int totalTicketsAvailable = getAvailableTickets(ticketingTypeId, event);
                        int numberOfTicketsInWaitList = this.countByEventIdAndStatusAndEmailSentAndTicketingTypeId(event, WaitListStatus.WAITING, true, ticketingTypeId);
                        int availableTickets = totalTicketsAvailable - numberOfTicketsInWaitList;


                        List<WaitList> saveWaitListInDb = new ArrayList<>();
                        Map<String, WaitListMailData> mailMap = new HashMap<>();
                        for (WaitList waitListObj : waitLists) {
                            if (availableTickets > 0) {
                                Date from = new Date();
                                waitListObj.setTimeTicketsBecameAvailable(from);
                                waitListObj.setEmailSent(true);
                                // in minutes
                                int timeToRespond = waitListSettings.getTimeToRespond();

                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(from);
                                calendar.add(Calendar.MINUTE, timeToRespond);
                                long diffInMilliSec = calendar.getTime().getTime() - TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone()).getTime();
                                if (diffInMilliSec > 0) {
                                    long remainingMins = (diffInMilliSec / 60000);
                                    String email = waitListObj.getEmail();

                                    WaitListMailData waitListMailData = mailMap.get(email);
                                    String checkoutLink = String.valueOf(waitListObj.getWaitListId());
                                    if (waitListMailData != null) {
                                        waitListMailData.setCheckoutLink(waitListMailData.getCheckoutLink() + Constants.STRING_COMMA + checkoutLink);
                                        waitListMailData.setNumberOfTickets(waitListMailData.getNumberOfTickets() + 1);
                                    } else {
                                        waitListMailData = new WaitListMailData(remainingMins, checkoutLink, 1);
                                    }
                                    mailMap.put(email, waitListMailData);
                                    availableTickets--;
                                    saveWaitListInDb.add(waitListObj);
                                }
                            }
                        }
                        if (!saveWaitListInDb.isEmpty()) {
                            sendWaitListEmail(waitListTicketReleaseMessage, waitListSettings.getWaitListTicketReleaseSubject(), event, mailMap);
                            saveAllAndClearCache(saveWaitListInDb);
                        }

                    }
                } else {
                    List<Long> ticketingTypeIds = ticketingTypeService.findAllIdByEventId(event);
                    Ticketing ticketing = ticketingRepository.findByEventid(event);
                    Map<String, WaitListMailData> mailMap = new HashMap<>();
                    Map<Long, BigDecimal> ticketSoldCount = eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId());
                    List<Long> addonTypeIds = ticketingTypeService.findAllTypeIdByEvent(event, DataType.ADDON);
                    addonTypeIds.forEach(ticketSoldCount.keySet()::remove);
                    BigDecimal totalSoldTickets = ticketSoldCount.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    int eventNumberOfTicketsInWaitList = this.countByEventIdAndStatusAndEmailSent(event, WaitListStatus.WAITING, true);
                    int totalAvailableTickets = ticketing.isLimitEventCapacity() ? (int) (ticketing.getEventCapacity() - totalSoldTickets.doubleValue() - eventNumberOfTicketsInWaitList) : ticketingStatisticsService.getAvailableTicketsForFullEvent(event);
                    log.info("event limit capacity enabled {} for ticketing id {} | event capacity {} | totalAvailableTickets: {} | eventNumberOfTicketsInWaitList: {} | totalSoldTickets: {}", ticketing.isLimitEventCapacity(), ticketing.getId(), ticketing.getEventCapacity(), totalAvailableTickets,eventNumberOfTicketsInWaitList,totalSoldTickets);
                    for (Long ticketingTypeId : ticketingTypeIds) {

                        int availableTickets = 0;

                        if (ticketing.isLimitEventCapacity()) {
                            if (ticketing.isRecurringEvent()) {
                                List<RecurringEventsTicketingTypeAndSoldCountDTO> eventIdAndSoldCountDTO = eventTicketsRepoService.countNumberOfSoldTicketForRecurringEventsByEventId(event.getEventId());
                                if (!CollectionUtils.isEmpty(eventIdAndSoldCountDTO) &&
                                        eventIdAndSoldCountDTO.stream().allMatch(e -> Boolean.TRUE.equals(e.isEventCapacityReach()))) {
                                    availableTickets = 0;
                                }
                            } else {
                                int totalTicketsAvailableByTicketTypeId = getAvailableTickets(ticketingTypeId, event);
                                if (totalTicketsAvailableByTicketTypeId >= totalAvailableTickets){
                                    availableTickets =  totalAvailableTickets;
                                } else {
                                    availableTickets =  totalTicketsAvailableByTicketTypeId - eventNumberOfTicketsInWaitList;
                                }
                            }
                        } else {
                            int totalTicketsAvailable = getAvailableTickets(ticketingTypeId, event);
                            int numberOfTicketsInWaitList = this.countByEventIdAndStatusAndEmailSentAndTicketingTypeId(event, WaitListStatus.WAITING, true, ticketingTypeId);
                            availableTickets = totalTicketsAvailable - numberOfTicketsInWaitList;
                        }

                        List<WaitList> waitList = waitListRepository.findByEventIdAndStatusAndTimeTicketsBecameAvailableIsNullAndEmailSentAndTicketingTypeIdOrderByPositionAsc(eventId, WaitListStatus.WAITING, false, ticketingTypeId);

                        List<WaitList> saveWaitListInDb = new ArrayList<>();
                        for (WaitList waitListObj : waitList) {
                            log.info("Waitlist Id: {} | waitlist email: {} | waitlist status: {} | waitlist ticketing type id: {} | availableTickets: {} | totalAvailableTickets: {}",waitListObj.getWaitListId(), waitListObj.getEmail(), waitListObj.getStatus(), waitListObj.getTicketingTypeId(), availableTickets, totalAvailableTickets);
                            if (availableTickets > 0) {
                                Date from = new Date();
                                waitListObj.setTimeTicketsBecameAvailable(from);
                                waitListObj.setEmailSent(true);
                                waitListObj.setTicketingTypeId(ticketingTypeId);

                                int timeToRespond = waitListSettings.getTimeToRespond();

                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(from);
                                calendar.add(Calendar.MINUTE, timeToRespond);
                                long diffInMilliSec = calendar.getTime().getTime() - TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone()).getTime();
                                log.info("Check difference for waitlist {} | calendar time {} | date in local {}, time diffInMilliSec {}", waitListObj.getWaitListId(), calendar.getTime().getTime(), TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone()).getTime(), diffInMilliSec);
                                if (diffInMilliSec > 0) {
                                    long remainingMins = (diffInMilliSec / 60000);
                                    String email = waitListObj.getEmail();

                                    WaitListMailData waitListMailData = mailMap.get(email);
                                    String checkoutLink = String.valueOf(waitListObj.getWaitListId());
                                    if (waitListMailData != null) {
                                        waitListMailData.setCheckoutLink(waitListMailData.getCheckoutLink() + Constants.STRING_COMMA + checkoutLink);
                                        waitListMailData.setNumberOfTickets(waitListMailData.getNumberOfTickets() + 1);
                                    } else {
                                        waitListMailData = new WaitListMailData(remainingMins, checkoutLink, 1);
                                    }
                                    mailMap.put(email, waitListMailData);

                                    availableTickets--;
                                    totalAvailableTickets--;
                                    saveWaitListInDb.add(waitListObj);
                                }
                            }
                        }
                        if (!saveWaitListInDb.isEmpty()) {
                            log.info("saveWaitListInDb size {} for event: {}", saveWaitListInDb, event.getEventId());
                            sendWaitListEmail(waitListTicketReleaseMessage, waitListSettings.getWaitListTicketReleaseSubject(), event, mailMap);
                            saveAllAndClearCache(saveWaitListInDb);
                        }

                    }
                }
            } else {
                log.info("No waitListSettings found for id {}", waitListSettingId);
            }
        }catch (Exception ex){
            log.error("Error while processing handleWaitListTriggers waitListSettingsId {} has error {}", waitListSettingId, ex);
        }
        log.info("checkWaitList completed for waitListSettingId: {}", waitListSettingId);
    }
    @Override
    public void updateWaitListStatus(List<WaitList> waitListsToUpdate, WaitListStatus waitListStatus) {
        waitListsToUpdate.forEach(waitList -> {
            if(waitList.getWaitListId() > 0){
                waitList.setStatus(waitListStatus);
                saveAndClearCache(waitList);
            }
        });
    }

    @Override
    public List<WaitList> findByIdInAndEventAndStatus(List<Long> waitListIds, Event event, WaitListStatus status) {
        if(waitListIds.isEmpty()){
            return Collections.emptyList();
        }
        return waitListRepository.findByEventIdAndAndStatusAndWaitListIdIn(event.getEventId(), status, waitListIds);
    }

    @Override
    public List<WaitList> findByEventAndStatusAndTicketingTypeId(Event event, WaitListStatus waitListStatus, long waitListTrigger) {
        return waitListRepository.findByEventIdAndStatusAndTicketingTypeId(event.getEventId(), waitListStatus, waitListTrigger);
    }

    @Override
    public List<CountByTicketType> countByEventIdAndStatusAndTicketingTypeIdIn(Event event, WaitListStatus waitListStatus, List<Long> ticketTypeIds){
        return waitListRepository.countByEventIdAndStatusAndTicketingTypeIdIn(event.getEventId(), waitListStatus, ticketTypeIds);
    }

    @Override
    public List<WaitList> findByidInAndEventAndTicketTypeIdAndStatus(List<Long> waitListIds, Event event, long ticketTypeId, WaitListStatus waitListStatus) {
        if(waitListIds.isEmpty()){
            return Collections.emptyList();
        }
        return waitListRepository.findByEventIdAndTicketingTypeIdAndStatusAndWaitListIdIn(event.getEventId(), ticketTypeId, waitListStatus, waitListIds);
    }

    @Override
    public void updateStatusDeleted(Long eventId, WaitListStatus status) {
        waitListRepository.updateStatusDeleted(eventId, status);
    }

    @Override
    public void updateStatusDeletedByEventIds(List<Long> eventIds, WaitListStatus status) {
        waitListRepository.updateStatusDeletedByEventIds(eventIds, status);
    }

    @Override
    public List<WaitList> findWaitListByEmail(String email) {
        return waitListRepository.findWaitListByEmail(email);
    }

    private void saveAllAndClearCache(List<WaitList> waitLists) {
         waitListRepository.saveAll(waitLists);

        if (!waitLists.isEmpty()) {
            Long eventId = waitLists.get(0).getEventId();
            try {
                Event event = roEventService.findEventByEventId(eventId);
                if (event != null && event.getEventURL() != null) {
                    clearAPIGatewayCache.clearAPIGwTicketTypesRemainingCountCache(event.getEventURL());
                } else {
                    log.error("saveAllAndClearCache | event not found for eventId {}", eventId);
                }
            } catch (Exception e) {
                log.error("saveAllAndClearCache | error clearing cache for eventId {}", eventId, e);
            }
        }
    }


    private void saveAndClearCache(WaitList waitList){
        waitListRepository.save(waitList);
        Event event = roEventService.findEventByEventId(waitList.getEventId());
        if(null!=event) {
            clearAPIGatewayCache.clearAPIGwTicketTypesRemainingCountCache(event.getEventURL());
        }else {
            log.error("saveAndClearCache | event not found.");
        }
    }
}