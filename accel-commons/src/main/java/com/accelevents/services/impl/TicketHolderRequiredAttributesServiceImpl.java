
package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderAttributesCapacity;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.repositories.require.attributes.AddOnRequiresAttributesRepo;
import com.accelevents.repositories.require.attributes.HolderAttributeCapacityRepo;
import com.accelevents.repositories.require.attributes.TicketRequiresAttributesRepo;
import com.accelevents.ro.eventTicket.ROTicketHolderRequiredAttributesService;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.ticketing.dto.AttributesDefaultValuesDto;
import com.accelevents.utils.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.DataType.ADDON;
import static com.accelevents.domain.enums.DataType.TICKET;
import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;
import static java.util.stream.Collectors.toList;
import static org.springframework.util.CollectionUtils.isEmpty;

@Service
public class TicketHolderRequiredAttributesServiceImpl implements TicketHolderRequiredAttributesService {

	@Autowired
	private TicketRequiresAttributesRepo ticketRequiresAttributesRepo;
    @Autowired
    private HolderAttributeCapacityRepo holderAttributeCapacityRepo;
	@Autowired
	private AddOnRequiresAttributesRepo addOnRepository;
	@Autowired
	private TicketingHelperService ticketingHelperService;
	@Autowired
	private RegistrationAttributeRepository registrationAttributeRepository;

    @Autowired
    private ROTicketHolderRequiredAttributesService roTicketHolderRequiredAttributesService;

    private static final Logger log = LoggerFactory.getLogger(TicketHolderRequiredAttributesServiceImpl.class);


    @Override
    public Optional<TicketHolderRequiredAttributes> findById(Long ticketHolderAttributeId){
        return ticketRequiresAttributesRepo.findById(ticketHolderAttributeId);
    }

	@Override
	public List<TicketHolderRequiredAttributes> getAllAttributesByCreatedFromAndEventId(Long createdFrom, Event event) {
		return ticketRequiresAttributesRepo.findAllAttributesByCreatedFromAndEventId(createdFrom, event);
	}

	@Override
	public List<TicketHolderRequiredAttributes> getAllAttributesBynameAndEventAndRecurringIdNotNull(String attributeName, Event event) {
		return ticketRequiresAttributesRepo.findAllAttributesBynameAndEventAndRecurringIdNotNull(event,attributeName);
	}

	@Override
	public List<TicketHolderRequiredAttributes> getAllAttributesExcludingSubQueByRecurringEventId(Event event, Long recurringEventId, DataType dataType) {
		// Check
        List<TicketHolderRequiredAttributes> listOfAttr =  ticketRequiresAttributesRepo.findAllAttributesByRecurringEventId(event, recurringEventId, dataType);
        if(CollectionUtils.isEmpty(listOfAttr))return listOfAttr;

        return listOfAttr.stream().filter(e-> !NumberUtils.isNumberGreaterThanZero(e.getParentQuestionId())).collect(Collectors.toList());
	}

	@Override
	public List<TicketHolderRequiredAttributes> getAllAttributes(Event event) {
		return ticketRequiresAttributesRepo.findByEventidAndRecurringEventIdNull(event);
	}

    @Override
    public List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketPurchaser(Event event) {
        return ticketRequiresAttributesRepo.findByEventidAndRecurringEventIdIsNullAndEnabledForTicketPurchaser(event);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketPurchaserExcludingConditionalQue(Event event) {
        return ticketRequiresAttributesRepo.findByEventidAndRecurringEventIdIsNullAndEnabledForTicketPurchaserAndAttributeTypeNotConditionalQue(event);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketHolder(Event event) {
        return ticketRequiresAttributesRepo.findByEventidAndRecurringEventIdIsNullAndEnabledForTicketHolder(event);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketHolderExcludingConditionalQue(Event event) {
        return ticketRequiresAttributesRepo.findByEventidAndRecurringEventIdIsNullAndEnabledForTicketHolderAndAttributeTypeNotConditionalQue(event);
    }

	@Override
	public TicketHolderRequiredAttributes findBynameAndEventidRecurringEventId(String attributeName, Event event, Long recurringEventId, DataType dataType) {
		return ticketRequiresAttributesRepo.findBynameAndEventidAndRecurringEventIdAndDataType(attributeName, event, recurringEventId, dataType);
	}

    @Override
    public List<TicketHolderRequiredAttributes> findAllByAttributeNamesAndEventIdAndRecurringEventIdIsNULLAndDataType(List<String> attributeNames, Event event, DataType dataType) {
        return ticketRequiresAttributesRepo.findAllByAttributeNamesAndEventIdAndRecurringEventIdIsNULLAndDataType(attributeNames, event, dataType);
    }
	@Override
	public TicketHolderRequiredAttributes findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType attributeValueType, Event event) {
		// Both
		return ticketRequiresAttributesRepo.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(attributeValueType, event, TICKET);
	}

	@Override
	public TicketHolderRequiredAttributes findByattributeValueTypeAndEventidAndRecurringEventId(AttributeValueType attributeValueType, Event event, Long recurringEventId) {
		return ticketRequiresAttributesRepo.findByattributeValueTypeAndEventidAndRecurringEventId(attributeValueType, event, recurringEventId);
	}

	@Override
	public List<TicketHolderRequiredAttributes> getAllAttributesOrderByAttributeOrder(Event event) {
		// Both
		return ticketRequiresAttributesRepo.findByEventidAndDataTypeOrderByBuyerAttributeOrder(event, TICKET);
	}

	@Override
	public int findHighestAttributePosition(Event event, boolean isBuyerAttribute) {
		// Both
		return isBuyerAttribute ? ticketRequiresAttributesRepo.findFirstByEventidAndDataTypeOrderByBuyerAttributeOrderDesc(event, TICKET).getBuyerAttributeOrder() :
                ticketRequiresAttributesRepo.findFirstByEventidAndDataTypeOrderByHolderAttributeOrderDesc(event, TICKET).getHolderAttributeOrder();
	}

    @Override
    public int findHighestAttributePosition(Event event, boolean isBuyerAttribute, DataType dataType) {

        TicketHolderRequiredAttributes holderAttribute;
	    if (isBuyerAttribute) {
            holderAttribute = ticketRequiresAttributesRepo.findFirstByEventidAndDataTypeOrderByBuyerAttributeOrderDesc(event, dataType);
            if (null != holderAttribute) {
                return holderAttribute.getBuyerAttributeOrder();
            }
        } else {
            holderAttribute = ticketRequiresAttributesRepo.findFirstByEventidAndDataTypeOrderByHolderAttributeOrderDesc(event, dataType);
            if (null != holderAttribute) {
                return holderAttribute.getHolderAttributeOrder();
            }
        }
        return 0;
    }

    private void deleteAll(List<TicketHolderRequiredAttributes> attributes) {
        if (attributes != null && !attributes.isEmpty()) {
            List<Long> attributeIds = attributes.stream()
                    .map(TicketHolderRequiredAttributes::getId)
                    .collect(Collectors.toList());
            log.info("deleteAll attributeIds : {} ",attributeIds);
            if (!attributeIds.isEmpty()) {
                List<TicketHolderAttributesCapacity> ticketHolderAttributesCapacities =
                        holderAttributeCapacityRepo.findAllByTicketHolderAttributesIds(attributeIds);

                if (ticketHolderAttributesCapacities != null && !ticketHolderAttributesCapacities.isEmpty()) {
                    holderAttributeCapacityRepo.deleteAll(ticketHolderAttributesCapacities);
                }
            }
            ticketRequiresAttributesRepo.deleteAll(attributes);
        }
    }

    @Override
    @Transactional
	public void deleteCustomAttribute(TicketHolderRequiredAttributes attributesDb,
									  boolean isDeletedFromBuyer,
									  Long recurringEventId,
									  Event event) {
        boolean isConditionalQuestion = AttributeValueType.CONDITIONAL_QUE.name().equalsIgnoreCase(attributesDb.getAttributeValueType().getType()) ;
		if(ADDON.equals(attributesDb.getDataType())){
            List<TicketHolderRequiredAttributes> recurringAttributes = new ArrayList<>();
            if(!isNumberGreaterThanZero(recurringEventId)) {
                recurringAttributes = addOnRepository.findByCreatedFromIdAndEventId(attributesDb.getId(), event);
                deleteAll(recurringAttributes);
            }
            List<TicketHolderAttributesCapacity> ticketHolderAttributesCapacities = holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(attributesDb.getId());
            if (ticketHolderAttributesCapacities != null && !ticketHolderAttributesCapacities.isEmpty()) {
                holderAttributeCapacityRepo.deleteAll(ticketHolderAttributesCapacities);
            }
            ticketRequiresAttributesRepo.deleteById(attributesDb.getId());
            if (isConditionalQuestion) {
                deleteAddonSubQuestion(attributesDb);
            }
		} else {
            List<TicketHolderRequiredAttributes> recurringAttributes = new ArrayList<>();
            if(!isNumberGreaterThanZero(recurringEventId)) {
                recurringAttributes = ticketRequiresAttributesRepo.findByCreatedFromIdAndEventId(attributesDb.getId(), event);
            }
            processForDeleteQuestionAndSubQuestion(attributesDb, isDeletedFromBuyer,recurringAttributes, isConditionalQuestion);
            if(isConditionalQuestion){
                boolean isAllowToHardDelete = NumberUtils.isNumberGreaterThanZero(attributesDb.getParentQuestionId());
                deleteSubQuestion(attributesDb,isDeletedFromBuyer, isAllowToHardDelete);
                if(isNumberGreaterThanZero(recurringEventId) && (isHardDeleteApplicable(attributesDb, isDeletedFromBuyer, isConditionalQuestion))){
                    makeParentQueCustom(attributesDb);
                }
            }
        }
	}
	private void deleteBuyer(TicketHolderRequiredAttributes attributesDb) {
		attributesDb.setDeletedForBuyer(true);
		attributesDb.setEnabledForTicketPurchaser(false);
	}
	private void deleteHolder(TicketHolderRequiredAttributes attributesDb) {
		attributesDb.setDeletedForHolder(true);
		attributesDb.setEnabledForTicketHolder(false);
	}

	private boolean isHardDeleteApplicable(TicketHolderRequiredAttributes attributesDb, boolean isDeletedFromBuyer, boolean isConditionalQuestion) {
		return (isDeletedFromBuyer && attributesDb.getDeletedForHolder())
				|| (!isDeletedFromBuyer && attributesDb.getDeletedForBuyer())
                || (isConditionalQuestion && NumberUtils.isNumberGreaterThanZero(attributesDb.getParentQuestionId()));
	}


	@Override
	public Optional<TicketHolderRequiredAttributes> findCustomAttributeById(long attributeId) {
		return ticketRequiresAttributesRepo.findById(attributeId);
	}

    @Override
    public List<TicketHolderRequiredAttributes> findAllSubQueByParentIdAndEventId(Event event, long parentQueId) {
        return ticketRequiresAttributesRepo.findAllSubQueByParentIdAndEventId(event, parentQueId);
    }

    @Override
    public List<TicketHolderRequiredAttributes> findAllSubQueByParentIdsAndEventId(Event event, List<Long> parentQueIds) {
        return ticketRequiresAttributesRepo.findAllSubQueByParentIdsAndEventId(event, parentQueIds);
    }

    @Override
	public void deleteByRecurringIdAndEventId(List<Long> recurringEventsToBeDelete, Event event) {
		ticketRequiresAttributesRepo.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
	}

	@Override
	public List<TicketHolderRequiredAttributes> findByEventId(Event event) {
		return ticketRequiresAttributesRepo.findAllByEventidAndRecurringEventIdNull(event);
	}

    @Override
    public List<TicketHolderRequiredAttributes> getAllConditionalQuestionByEventAndDataType(Event event, DataType dataType) {
        return ticketRequiresAttributesRepo.findAllConditionalQuestionByEventAndDataType(event, dataType);
    }

    @Override
	public Optional<TicketHolderRequiredAttributes> getTicketHolderAttributeById(long id) {
		return ticketRequiresAttributesRepo.findById(id);
	}

	@Override
	public TicketHolderRequiredAttributes getNextPositionAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
		// Both
		List<TicketHolderRequiredAttributes> ticketHolderAttributes = ticketRequiresAttributesRepo.nextPositionAttribute(ticketHolderRequiredAttributes.getEventid(), ticketHolderRequiredAttributes.getBuyerAttributeOrder());
		return ticketHolderAttributes.isEmpty() ? null:ticketHolderAttributes.get(0);
	}

	@Override
	public TicketHolderRequiredAttributes getPreviousPositionAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
		// Both
		List<TicketHolderRequiredAttributes> ticketHolderAttributes = ticketRequiresAttributesRepo.previousPositionAttribute(ticketHolderRequiredAttributes.getEventid(), ticketHolderRequiredAttributes.getBuyerAttributeOrder());
		return ticketHolderAttributes.isEmpty() ? null:ticketHolderAttributes.get(0);
	}

	@Override
	public void updatePositionInGroup(int startPosition, int endPosition, int positionDifferent, Event event) {
		// Both
		ticketRequiresAttributesRepo.updatePositionItem(event,startPosition,endPosition,positionDifferent);
	}
	@Override
	public void updatePositionForAllAttribute(Event event, int sequence, DataType dataType) {
		// Both
		ticketRequiresAttributesRepo.updatePositionForAllAttribute(event,sequence, dataType);
	}

	@Override
	public void saveAll(List<TicketHolderRequiredAttributes> newTicketHolderRequiredAttributes){
		if(!isEmpty(newTicketHolderRequiredAttributes)){
			ticketRequiresAttributesRepo.saveAll(newTicketHolderRequiredAttributes);
		}
	}

	@Override
	public TicketHolderRequiredAttributes saveDefaultTicketAttributes(TicketHolderRequiredAttributes newTicketHolderRequiredAttributes) {
		return ticketRequiresAttributesRepo.save(newTicketHolderRequiredAttributes);
	}


	@Override
	public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributes(Event event, Long recurringEventId) {
		// Done
		return (isNumberGreaterThanZero(recurringEventId)) ?
				ticketRequiresAttributesRepo.findAllAttributesByRecurringEventId(event, recurringEventId, TICKET) :
				ticketRequiresAttributesRepo.findByEventidAndRecurringEventIdNull(event);
	}


    @Override
    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesOrderByAttributeOrder(Event event, Long recurringEventId,DataType dataType) {
        return (isNumberGreaterThanZero(recurringEventId)) ?
                ticketRequiresAttributesRepo.findAllAttributesByRecurringEventIdOrderByBuyerAttributeOrder(event, recurringEventId, dataType) :
                ticketRequiresAttributesRepo.findByEventIdAndRecurringEventIdNullOrderByBuyerAttributeOrder(event, dataType);
    }
	@Override
	public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesHavingEnableForHolderOrderByAttributeOrder(Event event, Long recurringEventId,DataType dataType) {
		return (isNumberGreaterThanZero(recurringEventId)) ?
				ticketRequiresAttributesRepo.findAllAttributesByRecurringEventIdHavingEnableForHolderOrderByHolderAttributeOrder(event, recurringEventId, dataType) :
				ticketRequiresAttributesRepo.findByEventIdAndRecurringEventIdNullHavingEnableForHolderOrderByHolderAttributeOrder(event, dataType);
	}

    @Override
    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesHavingEnableForBuyerOrderByAttributeOrder(Event event, Long recurringEventId,DataType dataType) {
        return (isNumberGreaterThanZero(recurringEventId)) ?
                ticketRequiresAttributesRepo.findAllAttributesByRecurringEventIdHavingEnableForBuyerOrderByBuyerAttributeOrder(event, recurringEventId, dataType) :
                ticketRequiresAttributesRepo.findByEventIdAndRecurringEventIdNullHavingEnableForBuyerOrderByBuyerAttributeOrder(event, dataType);
    }




    @Override
    public List<TicketHolderRequiredAttributes> getEnabledAddonCustomAttributesOrderByBuyerAttributeOrder(Event event, Long recurringEventId) {
        return (isNumberGreaterThanZero(recurringEventId)) ? addOnRepository.findEnabledAttributeByEventIdAndRecurringEventIdOrderByBuyerAttributeOrder(event, ADDON, recurringEventId) :
                addOnRepository.findEnabledAttributeByEventIdAndRecurringEventIdNullOrderByBuyerAttributeOrder(event, ADDON);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getEnabledAddonCustomAttributesOrderByHolderAttributeOrder(Event event, Long recurringEventId) {
        return (isNumberGreaterThanZero(recurringEventId)) ? addOnRepository.findEnabledAttributeByEventIdAndRecurringEventIdOrderByHolderAttributeOrder(event, ADDON, recurringEventId):
                addOnRepository.findEnabledAttributeByEventIdAndRecurringEventIdNullOrderByHolderAttributeOrder(event, ADDON);
    }

	@Override
	public List<TicketHolderRequiredAttributes> getTicketingAttributesByRecurringEventList(List<Long> oldReIdsList) {
		return ticketRequiresAttributesRepo.findTicketingAttributesByRecurringEventList(oldReIdsList);
	}

	@Override
	public List<TicketHolderRequiredAttributes> findByEventIdHavingRecurringEventIdNotNull(Event eventId, String attributeName) {
		return ticketRequiresAttributesRepo.findByEventIdAndRecurringEventIdNotNull(eventId, attributeName);
	}


	@Override
	public List<TicketHolderRequiredAttributes> getAllWhichHavingRecurringEventIdNotNull(Event event) {
		return ticketRequiresAttributesRepo.findAllWhichHavingRecurringEventIdNotNull(event);
	}

	@Override
	public List<TicketHolderRequiredAttributes> getAllAddonCustomAttributes(Event event, boolean isForBuyer) {
        if (isForBuyer) {
            List<TicketHolderRequiredAttributes> attributesForBuyer = addOnRepository.findByEventidAndDataTypeOrderByBuyerAttributeOrderExcludingSubQue(event, ADDON);
            return getTicketHolderRequiredAttributes(attributesForBuyer);
        }
        List<TicketHolderRequiredAttributes> attributesForHolder= addOnRepository.findByEventidAndDataTypeOrderByHolderAttributeOrderExcludingSubQue(event, ADDON);
        return getTicketHolderRequiredAttributes(attributesForHolder);
	}

    private List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributes(List<TicketHolderRequiredAttributes> attributes) {
        return !CollectionUtils.isEmpty(attributes)
                ? attributes.stream().filter(e -> !NumberUtils.isNumberGreaterThanZero(e.getParentQuestionId())).collect(Collectors.toList())
                : Collections.emptyList();
    }

    @Override
	public void updateTicketHolderAttributeRecStatusByRecurringEventIds(List<Long> recurringEventIds, Event event, RecordStatus status) {
		ticketRequiresAttributesRepo.updateTicketHolderAttributeRecStatusByRecurringEventIds(recurringEventIds,event,status);
	}

	@Override
	public List<TicketHolderRequiredAttributes> findByIdsIn(List<Long> ticketHolderAttributeReqIds) {
		return ticketRequiresAttributesRepo.findByIdInOrderByBuyerAttributeOrder(ticketHolderAttributeReqIds);
	}

    @Override
    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesByExhibitor(Event event,List<String> attributeList) {
        return ticketRequiresAttributesRepo.getTicketHolderRequiredAttributesByExhibitor(event,attributeList) ;
    }

    @Override
    public TicketHolderRequiredAttributes getNextPositionForHolderAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
        List<TicketHolderRequiredAttributes> ticketHolderAttributes = ticketRequiresAttributesRepo.nextPositionForHolderAttribute(ticketHolderRequiredAttributes.getEventid(), ticketHolderRequiredAttributes.getHolderAttributeOrder());
        return ticketHolderAttributes.isEmpty() ? null:ticketHolderAttributes.get(0);
    }

    @Override
    public TicketHolderRequiredAttributes getPreviousPositionForHolderAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
        List<TicketHolderRequiredAttributes> ticketHolderAttributes = ticketRequiresAttributesRepo.previousPositionForHolderAttribute(ticketHolderRequiredAttributes.getEventid(), ticketHolderRequiredAttributes.getHolderAttributeOrder());
        return ticketHolderAttributes.isEmpty() ? null:ticketHolderAttributes.get(0);
    }

    @Override
    public void updatePositionForHolderInGroup(int startPosition, int endPosition, int positionDifferent, Event event) {
        ticketRequiresAttributesRepo.updatePositionForHolderItem(event,startPosition,endPosition,positionDifferent);
    }

    @Override
    public void updatePositionForAllHolderAttribute(Event event, int sequence, DataType dataType) {
        ticketRequiresAttributesRepo.updatePositionForAllHolderAttribute(event,sequence, dataType);
    }

    public List<TicketHolderRequiredAttributes> getEventidAndRecurringEventIdNullAndTicketAndAddOnDataType(Event event){
        return ticketRequiresAttributesRepo.findByEventidAndRecurringEventIdNullAndTicketAndAddOnDataType(event);
    }

    @Override
    public List<String> getHolderOrBuyerEnabledAttribute(Event event) {
        return ticketRequiresAttributesRepo.getAllBuyerEnabledAttributeNameByEvent(event);
    }

    @Override
    public List<String> getHolderEnabledAttribute(Event event) {
        return ticketRequiresAttributesRepo.getHolderEnabledAttribute(event);
    }

    public void deleteSubQuestion(TicketHolderRequiredAttributes attributesDb, boolean isDeletedFromBuyer,
                                  boolean isConditionalQuestion) {
        log.info("deleteSubQuestion eventId {} attributeId {}",attributesDb.getEventid(),attributesDb.getId());
            List<TicketHolderRequiredAttributes> subQueList = findAllSubQueByParentIdAndEventId(attributesDb.getEventid(), attributesDb.getId());
            if (!CollectionUtils.isEmpty(subQueList)) {
                subQueList.stream().forEach(e -> {
                    List<TicketHolderRequiredAttributes> recurringAttributes = ticketRequiresAttributesRepo.findByCreatedFromIdAndEventId(e.getId(), e.getEventid());
                    processForDeleteQuestionAndSubQuestion(e, isDeletedFromBuyer, recurringAttributes, isConditionalQuestion);
                    deleteSubQuestion(e,isDeletedFromBuyer, isConditionalQuestion);
                });
            }
    }
    public void processForDeleteQuestionAndSubQuestion(TicketHolderRequiredAttributes attributesDb, boolean isDeletedFromBuyer, List<TicketHolderRequiredAttributes> recurringAttributes, boolean isConditionalQuestion){
        log.info("processForDeleteSubQuestion eventId {} attributeId {}",attributesDb.getEventid(),attributesDb.getId());
            if (isHardDeleteApplicable(attributesDb, isDeletedFromBuyer, isConditionalQuestion)) {
                deleteAll(recurringAttributes);
                List<TicketHolderAttributesCapacity> ticketHolderAttributesCapacities =   holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(attributesDb.getId());
                holderAttributeCapacityRepo.deleteAll(ticketHolderAttributesCapacities);
                ticketRequiresAttributesRepo.deleteById(attributesDb.getId());
            } else if (isDeletedFromBuyer) {
                Ticketing ticketing = ticketingHelperService.findTicketingByEvent(attributesDb.getEventid());
                if (!ticketing.getCollectTicketHolderAttributes()) {
                    deleteAll(recurringAttributes);
                    List<TicketHolderAttributesCapacity> ticketHolderAttributesCapacities =   holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(attributesDb.getId());
                    holderAttributeCapacityRepo.deleteAll(ticketHolderAttributesCapacities);
                    ticketRequiresAttributesRepo.deleteById(attributesDb.getId());
                } else {
                    recurringAttributes.forEach(this::deleteBuyer);
                    saveAll(recurringAttributes);
                    deleteBuyer(attributesDb);
                    ticketRequiresAttributesRepo.save(attributesDb);
                }
            } else {
                //update deleted for holder flag true if only delete from holder
                recurringAttributes.forEach(this::deleteHolder);
                saveAll(recurringAttributes);
                List<TicketHolderAttributesCapacity> ticketHolderAttributesCapacities =   holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(attributesDb.getId());
                holderAttributeCapacityRepo.deleteAll(ticketHolderAttributesCapacities);
                deleteHolder(attributesDb);
                ticketRequiresAttributesRepo.save(attributesDb);
            }
    }

    private void makeParentQueCustom(TicketHolderRequiredAttributes attributesDb) {
        if(!isNumberGreaterThanZero(attributesDb.getParentQuestionId())){
            return;
        }

        List<TicketHolderRequiredAttributes> listOfQuestionsConvertedToCustom = getTicketHolderRequiredAttributesForConditionalQuestions(attributesDb.getParentQuestionId(), attributesDb.getEventid(), false, false);

        Optional<TicketHolderRequiredAttributes> parentQueOpt = listOfQuestionsConvertedToCustom.stream().filter(e-> !isNumberGreaterThanZero(e.getParentQuestionId())).findFirst();

        if(parentQueOpt.isPresent()){
            TicketHolderRequiredAttributes parentQue = parentQueOpt.get();

            List<TicketHolderRequiredAttributes> listOfQuestionsConvertedToCustomAll = getTicketHolderRequiredAttributesForConditionalQuestions1(parentQue.getId(), attributesDb.getEventid());

            List<TicketHolderRequiredAttributes> finalListToMakeCustom = new ArrayList<>();
            finalListToMakeCustom.add(parentQue);
            finalListToMakeCustom.addAll(listOfQuestionsConvertedToCustomAll);
            if (!CollectionUtils.isEmpty(finalListToMakeCustom)) {
                finalListToMakeCustom.stream().forEach(attribute -> attribute.setCreatedFrom(-1l));
                ticketRequiresAttributesRepo.saveAll(finalListToMakeCustom);
            }
        }
    }

    @Override
    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesForConditionalQuestions(long attributeId, Event event, boolean isForBuyer) {
        return getTicketHolderRequiredAttributesForConditionalQuestions(attributeId, event, isForBuyer, true);
    }

    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesForConditionalQuestions(long attributeId, Event event, boolean isForBuyer, boolean isForAll) {
        List<TicketHolderRequiredAttributes> subQueListFinal = new ArrayList<>();
        List<Long> listDb = new ArrayList<>();
        listDb.add(attributeId);
        do {
            List<TicketHolderRequiredAttributes> subQueList = isForAll ? findAllSubQueByParentIdsAndEventId(event, listDb) :  roTicketHolderRequiredAttributesService.findAllParentQueByIdsAndEventId(event, listDb);
            if(isForAll){
                subQueList = getTicketHolderRequiredAttributesBasedOnIsForBuyer(isForBuyer, subQueList);
            }
            subQueListFinal.addAll(subQueList);
            listDb = isForAll ? subQueList.stream().map(TicketHolderRequiredAttributes::getId).collect(toList()) : subQueList.stream().map(TicketHolderRequiredAttributes::getParentQuestionId).collect(toList());
        } while (!CollectionUtils.isEmpty(listDb));
        return subQueListFinal;
    }

    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesForConditionalQuestions1(long attributeId, Event event) {
        List<TicketHolderRequiredAttributes> subQueListFinal = new ArrayList<>();
        List<Long> listDb = new ArrayList<>();
        listDb.add(attributeId);
        do {
            List<TicketHolderRequiredAttributes> subQueList = findAllSubQueByParentIdsAndEventId(event, listDb);
            subQueListFinal.addAll(subQueList);
            listDb = subQueList.stream().map(TicketHolderRequiredAttributes::getId).collect(toList());
        } while (!CollectionUtils.isEmpty(listDb));
        return subQueListFinal;
    }

    private List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesBasedOnIsForBuyer(boolean isForBuyer, List<TicketHolderRequiredAttributes> subQueList) {
        if (isForBuyer) {
            subQueList = subQueList.stream().filter(e -> !e.getDeletedForBuyer()).collect(toList());
        } else {
            subQueList = subQueList.stream().filter(e -> !e.getDeletedForHolder()).collect(toList());
        }
        return subQueList;
    }

    public void deleteAddonSubQuestion(TicketHolderRequiredAttributes attributesDb) {
        log.info("deleteAddonSubQuestion eventId {} attributeId {}", attributesDb.getEventid(), attributesDb.getId());
        List<TicketHolderRequiredAttributes> subQueList;
        subQueList = findAllSubQueByParentIdAndEventId(attributesDb.getEventid(), attributesDb.getId());
            if (!CollectionUtils.isEmpty(subQueList)) {
            ticketRequiresAttributesRepo.deleteAll(subQueList);
            subQueList.forEach(this::deleteAddonSubQuestion);
        }
    }

    @Override
    public void disableInterestAttribute(Event event) {
        TicketHolderRequiredAttributes interestAttribute = findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType.INTEREST, event);
        if (interestAttribute != null) {
            interestAttribute.setHolderOptionalTicketTypeId(null);
            interestAttribute.setHolderOptionalTicketTypeId(null);
            interestAttribute.setEnabledForTicketHolder(false);
            ticketRequiresAttributesRepo.save(interestAttribute);
            log.info("Successfully disabled interest attribute for event {}", event.getEventId());
        }
    }

    @Override
    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesOrderByAttributeOrder(List<Event> event, DataType dataType) {
        return ticketRequiresAttributesRepo.findByEventIdInAndRecurringEventIdNullOrderByBuyerAttributeOrder(event,dataType);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesBasedOnAttributeType(Event event, AttributeValueType attributeValueType) {
        return ticketRequiresAttributesRepo.findByattributeValueTypeAndEventId(attributeValueType, event);
    }

    @Override
    public List<String> findTicketHolderRequiredAttributesByEventIdAndEnabledForHolders(Event event) {
        return ticketRequiresAttributesRepo.findTicketHolderRequiredAttributesByEventIdAndEnabledForHolders(event);
    }

    @Override
    public List<String> findTicketHolderRequiredAttributesByEventIdAndAddon(Event event) {
        return ticketRequiresAttributesRepo.findTicketHolderRequiredAttributesByEventIdAndAddon(event);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getBuyersAttributesByIdsAndEnabledForTicketBuyer(List<Long> ids, List<String> attributeNames, long eventId) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        return ticketRequiresAttributesRepo.getBuyersAttributesByIdsAndEnabledForTicketBuyer(ids, attributeNames, eventId);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getHolderAttributesByIdsAndEnabledForTicketHolder(List<Long> ids, List<String> attributeNames, long eventId) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        return ticketRequiresAttributesRepo.getHolderAttributesByIdsAndEnabledForTicketHolder(ids, attributeNames, eventId);
    }

    @Override
    public List<TicketHolderRequiredAttributes> findByEventAndIds(Event event, List<Long> ticketAttributeIds) {
        return CollectionUtils.isEmpty(ticketAttributeIds) ? Collections.emptyList() : ticketRequiresAttributesRepo.findByEventAndIds(event, ticketAttributeIds);
    }

    @Override
    public TicketHolderRequiredAttributes findAllSubQueByParentIdAndEventIdAndAnswerId(Event event, long parentQueId, Long ansId) {
        return ticketRequiresAttributesRepo.findByEventIdAndParentQuestionIdAndAnswerId(event, parentQueId, ansId);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getBuyerAttributesExcludingTypes(Event event, Long recurringEventId, DataType dataType, List<AttributeValueType> excludedTypes) {

        return (isNumberGreaterThanZero(recurringEventId))
                ? ticketRequiresAttributesRepo.findAllByEventAndRecurringEventIdAndDataTypeExcludingAttributeValueTypesOrderByBuyerAttributeOrder(event, recurringEventId, dataType, excludedTypes)
                : ticketRequiresAttributesRepo.findAllByEventAndRecurringEventIdIsNullAndDataTypeExcludingAttributeValueTypesOrderByBuyerAttributeOrder(event, dataType, excludedTypes);
    }

    @Override
    public List<TicketHolderRequiredAttributes> getHolderAttributesExcludingTypes(Event event, Long recurringEventId, DataType dataType, List<AttributeValueType> excludedTypes) {

        return (isNumberGreaterThanZero(recurringEventId))
                ? ticketRequiresAttributesRepo.findAllByEventAndRecurringEventIdAndDataTypeExcludingAttributeValueTypesOrderByHolderAttributeOrder(event, recurringEventId, dataType, excludedTypes)
                : ticketRequiresAttributesRepo.findAllByEventAndRecurringEventIdIsNullAndDataTypeExcludingAttributeValueTypesOrderByHolderAttributeOrder(event, dataType, excludedTypes);
    }



    @Override
    public List<AttributesDefaultValuesDto> getAttributesDefaultValuesByEvent(Event event) {
        return ticketRequiresAttributesRepo.getAttributesDefaultValuesByEvent(event);
    }

    @Override
    public List<TicketHolderRequiredAttributes> findAllWithDefaultValueNotNullForPurchaser(Long eventId, Long ticketTypeId) {
        return ticketRequiresAttributesRepo.findAllWithDefaultValueNotNullForPurchaser(eventId, ticketTypeId);
    }

    @Override
    public List<TicketHolderRequiredAttributes> findAllWithDefaultValueNotNullForHolder(Long eventId, Long ticketTypeId) {
        return ticketRequiresAttributesRepo.findAllWithDefaultValueNotNullForHolder(eventId, ticketTypeId);
    }

    @Override
    public List<TicketHolderRequiredAttributes> findAllAddOnAttributeWithDefaultValueNotNullForPurchaser(Long eventId, Long addonTicketTypeId) {
        return ticketRequiresAttributesRepo.findAllAddOnAttributeWithDefaultValueNotNullForPurchaser(eventId, addonTicketTypeId);
    }

    @Override
    public List<String> findAllRequiredAttributeNames(Long eventId, Long recurringEventId) {
        return Optional.ofNullable(
                ticketRequiresAttributesRepo.findAllRequiredAttributesName(eventId, recurringEventId)
        ).orElseGet(Collections::emptyList);
    }
}