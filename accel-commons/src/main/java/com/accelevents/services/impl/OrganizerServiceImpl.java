package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.billing.chargebee.dto.BillingSummerDto;
import com.accelevents.billing.chargebee.dto.EventCreditDistributionDto;
import com.accelevents.billing.chargebee.enums.ChargeConfigNames;
import com.accelevents.billing.chargebee.repo.*;
import com.accelevents.billing.chargebee.repositories.ChargebeeEventCreditsService;
import com.accelevents.billing.chargebee.repositories.ChargebeeTransactionRepository;
import com.accelevents.billing.chargebee.service.*;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.ChargebeeConfiguration;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.dto.organizer.OrganizerSecuritySettingDto;
import com.accelevents.enums.BillingType;
import com.accelevents.enums.OrganizerRole;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TemplateId;
import com.accelevents.hubspot.service.HubspotContactService;
import com.accelevents.hubspot.service.HubspotOrganizerService;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.*;
import com.accelevents.ro.chargebee.ROChargeBeeService;
import com.accelevents.ro.event.repository.ROEventRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROJoinUserWithOrganizerService;
import com.accelevents.ro.event.service.ROOrganizerService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.schedulers.ChargebeeUsageUploadScheduler;
import com.accelevents.services.*;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.repo.helper.ChangeHistoryRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.ticketing.dto.EntitlementsAvailabilityDTO;
import com.accelevents.utils.*;
import com.chargebee.models.Card;
import com.chargebee.models.Customer;
import com.chargebee.models.Subscription;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.accelevents.dto.PageUtil.validateSortingForOrgEventListing;
import static com.accelevents.enums.PlanConfigNames.FREE_PLAN;
import static com.accelevents.services.integration.cvent.CventJsonElementConstants.eventType;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.*;
import static com.accelevents.utils.Permissions.WL_AND_EVENT_COORDINATOR_PERMISSIONS;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

@Service
public class OrganizerServiceImpl implements OrganizerService {

    private static final Logger log = LoggerFactory.getLogger(OrganizerServiceImpl.class);

    @Autowired
    private OrganizerRepository organizerRepository;

    @Autowired
    private JoinUserWithOrganizerRepoService joinUserWithOrganizerRepoService;
    @Autowired
    private ROJoinUserWithOrganizerService roJoinUserWithOrganizerService;
    @Autowired
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;

    @Autowired
    private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;

    @Autowired
    private SuperAdminService superAdminService;

    @Autowired
    private ImageConfiguration imageConfiguration;

    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;

    @Autowired
    private TicketingDisplayService ticketingDisplayService;

    @Autowired
    private ChargebeePlanService chargebeePlanService;

    @Autowired
    private ChargebeeService chargebeeService;
    @Autowired
    private ROChargeBeeService roChargeBeeService;

    @Autowired
    private SessionRepoService sessionRepoService;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;
    @Autowired
    private StripeService stripeService;

    @Autowired
    private TicketingHelperService ticketingHelperService;

    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;

    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;

    @Autowired
    private EventChargeUsagesRepoService eventChargeUsagesRepoService;

    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;

    @Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;

    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private ROEventService roEventService;

    @Autowired
    private ChargebeePaymentService chargebeePaymentService;

    @Autowired
    ChargebeeEventUsagesRepoService chargebeeEventUsagesRepoService;

    @Autowired
    CheckInAuditLogRepoService checkInAuditLogRepoService;

    @Autowired
    private ChargebeeEventCreditsService chargebeeEventCreditsService;

    @Autowired
    private  TicketingRepository ticketingRepository;

    @Autowired
    private ChargebeeTransactionRepository chargebeeTransactionRepository;

    @Autowired
    private EventBillingAddOnsRepository eventBillingAddOnsRepository;

    @Autowired
    private OrganizerRepoService organizerRepoService;
    @Autowired
    private ROOrganizerService roOrganizerService;

    @Autowired
    private ChargebeeBillingSummeryService chargebeeBillingSummeryService;

    @Autowired
    private BeeFreeRepository beeFreeRepository;
    @Autowired
    private ApiKeyService apiKeyService;

    @Autowired
    private ChargebeeConfiguration chargebeeConfiguration;
    @Autowired
    private ChargebeePlanRepoService chargebeePlanRepoService;

    @Autowired
    private ChangeHistoryRepoService changeHistoryRepoService;
    @Autowired
    private  ChargebeeEventCreditsRepoService chargebeeEventCreditsRepoService;
    @Autowired
    private EventChargeUsagesService eventChargeUsagesService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private AddressDetailsRepository addressDetailsRepository;
    @Autowired
    private HubspotOrganizerService hubspotOrganizerService;
    @Autowired
    private HubspotContactService hubspotContactService;

    @Autowired
    private IntegrationRepository integrationRepository;
    @Autowired
    private EventService eventService;
    @Autowired
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Autowired
    private GamificationCacheStoreService gamificationCacheStoreService;
    @Autowired
    private VirtualEventService virtualEventService;
    @Autowired
    private EventDesignDetailService eventDesignDetailService;
    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private EventTicketsService eventTicketsService;
    @Autowired
    private ChargebeeUsageUploadScheduler chargebeeUsageUploadScheduler;
    @Autowired
    private ContactModuleSettingsService contactModuleSettingsService;
    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Autowired
    private ROEventRepository roEventRepository;

    @Override
    public Organizer findByIdOrNotFound(long organizerId) {
        return organizerRepository.findById(organizerId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND));
    }

    @Override
    public Organizer save(Organizer organizer) {
        if(StringUtils.isBlank(organizer.getOldOrganizerPageURL())){
            organizer.setOldOrganizerPageURL(organizer.getOrganizerPageURL());
        }
        return organizerRepository.save(organizer);
    }

    @Override
    @Transactional
    public OrganizerDto createNewOrganizer(String organizerName, User user, String createdSource, WhiteLabel whiteLabel) {
          Organizer organizer = createNewOrgAndAssociateUserAndPlan(organizerName, user, whiteLabel);
        hubspotOrganizerService.createHubspotOrganizerAsync(organizer);
        hubspotContactService.asyncAddContactToOrganizerCreatedEvent(user, organizer.getId(), createdSource, HubspotCustomEventType.ORGANIZER_CREATED);
        return new OrganizerDto(organizer.getId(), organizer.getName(), organizer.getOrganizerPageURL(), Optional.ofNullable(organizer.getWhiteLabel()).map(WhiteLabel::getId).orElse(null));
    }

    @Override
    @Transactional
    public OrganizerDto save(String organizerName, User user, Event event) {
        log.info("Create New Organizer organizerName {} userId {} eventId {}",organizerName,user.getUserId(),event.getEventId());
        Organizer organizer = createNewOrgAndAssociateUserAndPlan(organizerName, user, event.getWhiteLabel());
        EventPlanConfig oldEventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
        eventChargeUsagesService.updateVirtualEventHubCustomLabelName(event, organizer.getPlanConfig(), oldEventPlanConfig.getPlatformConfigJson());
        hubspotOrganizerService.createHubspotOrganizerAsync(organizer);
        return new OrganizerDto(organizer.getId(), organizer.getName(), organizer.getOrganizerPageURL());
    }

    @Override
    @Transactional
    public Organizer createNewOrgAndAssociateUserAndPlan(String organizerName, User user, WhiteLabel whiteLabel) {

        String organizerURL = checkOrgPageUrlAlreadyExists(organizerName);
        log.info("Creating new organizer with name: {}", organizerName);
        Organizer organizer = save(organizerName, user, organizerURL, whiteLabel);
        mapDefaultPlanToOrganizer(organizer);
        try {
            organizer = organizerRepository.save(organizer);
        }catch (DataIntegrityViolationException dataIntegrityViolationException){
            throw new ConflictException(ConflictException.UserExceptionConflictMsg.ORGANIZER_URL_ALREADY_EXIST);
        }
        addBaseRecordForOrganizerInTransactionTable(organizer,whiteLabel);
        joinUserWithOrganizerRepoService.associateUserWithOrganization(user, organizer, OrganizerRole.owner, true, organizer.getWhiteLabel() != null ? organizer.getWhiteLabel().getId() : null);
        addApiUserInWlOrganizer(whiteLabel, organizer);
        return organizer;
    }

    @Override
    public void addApiUserInWlOrganizer(WhiteLabel whiteLabel, Organizer organizer) {
        if(whiteLabel != null) {
            Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByWhiteLabelId(whiteLabel.getId());
            if (apiKeyOptional.isPresent()) {
                User user = userService.findById(apiKeyOptional.get().getUserId());
                apiKeyService.associateApiUserWithOrganization(user, organizer, whiteLabel);
            }
        }
    }

    @Transactional
    public void addBaseRecordForOrganizerInTransactionTable(Organizer organizer,WhiteLabel whiteLabel) {
        if (null != whiteLabel) {
            log.info("addBaseRecordForOrganizerInTransactionTable WhiteLabel: {}", whiteLabel.getId());
            List<TransactionFeeConditionalLogic> list = transactionFeeConditionalLogicService.findByWhiteLabelAndEventIsNullAndOrganizerIsNull(whiteLabel);
            List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
            list.forEach(transactionFeeConditionalLogic -> {
                if (transactionFeeConditionalLogic.isAddon()) {
                    transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                            null, GREATER_THAN_EQUAL_TO, transactionFeeConditionalLogic.getAeFeeFlat(), transactionFeeConditionalLogic.getAeFeePercentage(), POINT_ZERO_ONE, false,
                            organizer.getWhiteLabel(), organizer, true, -1));
                } else if (transactionFeeConditionalLogic.isVirtualEvent() && "<".equals(transactionFeeConditionalLogic.getOperator())) {
                    transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                            null, LESS_THAN, transactionFeeConditionalLogic.getAeFeeFlat(), transactionFeeConditionalLogic.getAeFeePercentage(), -1, true,
                            organizer.getWhiteLabel(), organizer, false, POINT_ZERO_ONE));
                } else if (transactionFeeConditionalLogic.isVirtualEvent() && ">=".equals(transactionFeeConditionalLogic.getOperator())) {
                    transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                            null, GREATER_THAN_EQUAL_TO, transactionFeeConditionalLogic.getAeFeeFlat(), transactionFeeConditionalLogic.getAeFeePercentage(), POINT_ZERO_ONE, true,
                            organizer.getWhiteLabel(), organizer, false, -1));
                } else if (!transactionFeeConditionalLogic.isVirtualEvent() && !transactionFeeConditionalLogic.isAddon() && "<".equals(transactionFeeConditionalLogic.getOperator())) {
                    transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                            null, LESS_THAN, transactionFeeConditionalLogic.getAeFeeFlat(), transactionFeeConditionalLogic.getAeFeePercentage(), -1, false,
                            organizer.getWhiteLabel(), organizer, false, POINT_ZERO_ONE));
                } else if (!transactionFeeConditionalLogic.isVirtualEvent() && !transactionFeeConditionalLogic.isAddon() && ">=".equals(transactionFeeConditionalLogic.getOperator())) {
                    transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                            null, GREATER_THAN_EQUAL_TO, transactionFeeConditionalLogic.getAeFeeFlat(), transactionFeeConditionalLogic.getAeFeePercentage(), POINT_ZERO_ONE, false,
                            organizer.getWhiteLabel(), organizer, false, -1));
                }
            });
            transactionFeeConditionalLogicService.saveAll(transactionFeeConditionalLogicList);
        }
        if(organizer != null) {
            log.info("addBaseRecordForOrganizerInTransactionTable Organiser: {}", organizer.getId());
            List<TransactionFeeConditionalLogic> transactionFeeConfigurations =
                    transactionFeeConditionalLogicService.getBaseRecordByOrganizer(organizer);
            if (transactionFeeConfigurations.isEmpty()) {
                List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
                transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                        null, GREATER_THAN_EQUAL_TO, AE_FLAT_FEE_ONE, AE_FEE_PERCENTAGE_THREE, POINT_ZERO_ONE, true,
                        organizer.getWhiteLabel(), organizer, false, -1));
                transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                        null, LESS_THAN, AE_FLAT_FEE_ZERO, AE_FEE_PERCENTAGE_ZERO, -1, true,
                        organizer.getWhiteLabel(), organizer, false, POINT_ZERO_ONE));
                transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                        null, GREATER_THAN_EQUAL_TO, AE_FLAT_FEE_ONE, AE_FEE_PERCENTAGE_THREE, POINT_ZERO_ONE, false,
                        organizer.getWhiteLabel(), organizer, false, -1));
                transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                        null, LESS_THAN, AE_FLAT_FEE_ZERO, AE_FEE_PERCENTAGE_ZERO, -1, false,
                        organizer.getWhiteLabel(), organizer, false, POINT_ZERO_ONE));
                transactionFeeConditionalLogicList.add(transactionFeeConditionalLogicService.prepareTransactionFeeConditionalLogic(
                        null, GREATER_THAN_EQUAL_TO, AE_FLAT_FEE_ONE, AE_FEE_PERCENTAGE_THREE, POINT_ZERO_ONE, false,
                        organizer.getWhiteLabel(), organizer, true, -1));
                transactionFeeConditionalLogicService.saveAll(transactionFeeConditionalLogicList);
            }
        }
    }
    private void mapDefaultPlanToOrganizer(Organizer organizer) {
        if (organizer.getPlanConfig() == null) {
            PlanConfig planConfig;
            if (organizer.getWhiteLabel() != null)
                planConfig = organizer.getWhiteLabel().getPlanConfig();
            else
                planConfig = chargebeePlanService.findByPlanName(FREE_PLAN.getName()).get(); //NOSONAR
            organizer.setPlanConfig(planConfig);
        }
    }

    private Organizer save(String organizerName, User user, String organizerURL, WhiteLabel whiteLabel) {
        Organizer organizer = new Organizer();
        organizer.setName(organizerName);
        organizer.setOrganizerPageURL(organizerURL);
        organizer.setCreatedBy(user);
        organizer.setContactEmailAddress(user.getEmail());
        organizer.setWhiteLabel(whiteLabel);
        if (whiteLabel != null) {
            organizer.setLogoImage(whiteLabel.getHeaderLogoImage());
            organizer.setChargebeeCustomerId(whiteLabel.getChargebeeCustomerId());
            if (null != whiteLabel.getSubscriptionId()) {
                organizer.setSubscriptionId(whiteLabel.getSubscriptionId());
            }
            organizer.setMonthlySubscriptionId(whiteLabel.getMonthlySubscriptionId());
            if(whiteLabel.getHubspotCompanyId() != null) {
                organizer.setHubSpotCompanyId(whiteLabel.getHubspotCompanyId());
            }
        }
        return organizer;
    }

    private String checkOrgPageUrlAlreadyExists(String organizerName) {
        String organizerURL = organizerName.replaceAll(ATOZ_BOTHCASE_AND_NUMBERS, Constants.STRING_EMPTY);
        organizerURL = organizerURL.equalsIgnoreCase(STRING_EMPTY) ? UUID.randomUUID().toString().replaceAll(STRING_DASH, STRING_EMPTY): organizerURL;
        log.info("Organizer name: {} | and URL: {}", organizerName, organizerURL);
        Optional<Organizer> optionalOrganizer = organizerRepository.findOrganizerByOrganizerPageURL(organizerURL);
        if (optionalOrganizer.isPresent()) {
            organizerURL = this.getOrganizerUrl(organizerURL, 0, organizerURL.length(), false);
        }
        return organizerURL;
    }

    @Override
    public String validateOrganizerName(String organizerUrl) {
        Optional<Organizer> organizer = organizerRepository.findOrganizerByOrganizerPageURL(organizerUrl);
        if (organizer.isPresent()) {
            return String.format(Constants.EVENT_WILL_BE_JOINED_WITH_ORGANIZER, organizerUrl);
        }
        return Constants.NEW_ORGANIZER_WILL_BE_CREATED;
    }

    @Override
    @Transactional
    public void updateOrganizer(long organizerId, OrganizerDto organizerDto, User user, Boolean needToMapIntegration) {
        Optional<Organizer> organizerData = getById(organizerId);
        if (organizerData.isPresent()) {
            log.info("Update organizer details of org id {} by user {} | org url {} | org name {} | needToMapIntegration {} ", organizerId, user.getUserId(), organizerDto.getOrganizerPageURL(), organizerDto.getName(), needToMapIntegration);
            Organizer organizer = organizerData.get();
            if (!roUserService.isSuperAdminUser(user) && !(isUserAuthorizedForWhiteLabelOrganizer(user, organizerId) || isEventCoordinatorAuthorizedForWhiteLabelOrganizer(user, organizerId))) {
                checkUserIsAllowToUpdateElseNotThenThrowException(user, organizer);
            }
            organizer.setBackgroundColor(organizerDto.getBackgroundColor());
            organizer.setFacebookLink(organizerDto.getFacebookLink());
            organizer.setTwitterLink(organizerDto.getTwitterLink());
            organizer.setLinkedInLink(organizerDto.getLinkedInLink());
            organizer.setLogoImage(organizerDto.getLogoImage());
            organizer.setOrganizerDescription(organizerDto.getOrganizerDescription());
            organizer.setTextColor(organizerDto.getTextColor());
            organizer.setName(organizerDto.getName());
            organizer.setOldOrganizerPageURL(organizer.getOrganizerPageURL());
            organizer.setOrganizerPageURL(organizerDto.getOrganizerPageURL());
            organizer.setWebsite(organizerDto.getWebsite());
            organizer.setContactEmailAddress(organizerDto.getContactEmailAddress());
            organizer.setAllowSocialSharing(organizerDto.isAllowSocialSharing());
            organizer.setHubSpotCompanyId(organizerDto.getHubspotCompanyId());
            if (organizer.getWhiteLabel() != null) {
                organizer.setEntitlements(organizer.getWhiteLabel().getEntitlements());
            }
            try {
                organizerRepository.save(organizer);
                hubspotOrganizerService.updateHubspotOrganizerProperty(organizer , "org_basic_info");
            }catch (DataIntegrityViolationException dataIntegrityViolationException){
                throw new ConflictException(ConflictException.UserExceptionConflictMsg.ORGANIZER_URL_ALREADY_EXIST);
            }
            if(needToMapIntegration){
                trayIntegrationService.mapOrganizer(getIntegration(organizer), organizerDto, TrayIOWebhookActionType.EDIT_ORGANIZER, null);
            }
            saveOrganizerAddress(organizer, organizerDto);
        } else {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
    }

    @Override
    public Optional<Organizer> getById(long organizerId) {
        return organizerRepository.findById(organizerId);
    }

    @Override
    public void checkUserIsAllowToUpdateElseNotThenThrowException(User user, Organizer organizer) {
        Optional<JoinUsersWithOrganizers> usersWithOrganizers = roJoinUserWithOrganizerService.getByUserIdAndOrgId(user.getUserId(), organizer.getId());
        if(!usersWithOrganizers.isPresent()) {
            usersWithOrganizers = roJoinUserWithOrganizerService.getByApiUserIdAndOrgId(user.getUserId(), organizer.getId());
        }
        if (!usersWithOrganizers.isPresent()) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.USER_NOT_ALLOW_TO_UPDATE);
        }
    }

    @Override
    public DataTableResponse getEventsByOrganizerURLAndPastEvent(User user, String organizerURL, boolean pastEvent, boolean isFromOrganizerProfilePage,PageSizeSearchObj pageSizeSearchObj) {
        DataTableResponse dataTableResponse = new DataTableResponse();
        List<String> ticketTypes = Arrays.asList(TicketType.DONATION.toString(), TicketType.FREE.toString());
        List<EventInfoDto> finalList = new ArrayList<>();
        validateSortingForOrgEventListing(pageSizeSearchObj.getSortBy());
        PageRequest pageRequest = PageRequest.of(pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize(),pageSizeSearchObj.getSortDirection(),pageSizeSearchObj.getSortBy());

        if (pastEvent) {
            Page<Object[]> pastEvents = eventRepository.findPastEventsByOrganizerURL(organizerURL, isFromOrganizerProfilePage,pageSizeSearchObj.getSearch(), pageRequest);
            List<Long> eventIds = pastEvents.stream().map(e -> Long.parseLong(e[8].toString())).collect(Collectors.toList());

            Map<Long, EventPlanConfig> eventPlanConfigMap = eventPlanConfigService.findEventPlanConfigMapByEventIds(eventIds);
            dataTableResponse.setRecordsTotal(pastEvents.getTotalElements());
            Map<Long, String> eventTypeWIthEventId = new HashMap<>();
            if (!ObjectUtils.isEmpty(user)) {
                List<Object[]> eventUserTypes = eventRepository.findEventUserTypes(user.getUserId(), eventIds);
                eventTypeWIthEventId = eventUserTypes.stream()
                        .filter(e -> e[0] != null && e[1] != null)
                        .collect(Collectors.toMap(e -> Long.parseLong(e[0].toString()), e -> e[1].toString(), (e1, e2) -> e2));
            }
            for (Object[] obj : pastEvents) {
                Long eventId = Long.parseLong(obj[8].toString());
                List<Object[]> ticketPriceRange = ticketingTypeRepository.findTicketPriceRange(eventId, ticketTypes);
                EventPlanConfig eventPlanConfig = eventPlanConfigMap.get(eventId);

                if (eventPlanConfig != null) {
                    String planConfigNames = eventPlanConfig.getPlanConfig().getPlanName();
                    if (PlanConfigNames.SINGLE_EVENT_UNIT.getName().equals(planConfigNames)) {
                        planConfigNames = FREE_PLAN.getName();
                    } else {
                        planConfigNames = eventPlanConfig.getPlanConfig().getDisplayPlanName();
                    }
                    Event event = eventPlanConfig.getEvent();
                    int totalRegistrantsWithStaffs;
                    if (FREE_PLAN.getName().equals(eventPlanConfig.getPlanConfig().getPlanName()) || eventPlanConfig.getPlanConfig().isLatestPlan()) {
                        totalRegistrantsWithStaffs = eventTicketsService.getRegistrationCountIncludingStaffMembersAndExcludingAeAndBrilworksUserIds(event);
                    } else {
                        totalRegistrantsWithStaffs = eventTicketsService.getCountOfUniqueAttendeeIncludingStaffMembersAndExcludingAeAndBrilworksUserIds(event);
                    }

                    if (ticketPriceRange != null && !ticketPriceRange.isEmpty()) {
                        EventInfoDto endInfoDto = new EventInfoDto(eventId, (String) obj[0], (String) obj[1], (Date) obj[2],
                                (String) obj[3], (String) obj[4], (Date) obj[5], (String) obj[6], (Boolean) obj[7],
                                (Double) ticketPriceRange.get(0)[1], (Double) ticketPriceRange.get(0)[0], Currency.valueOf(obj[9].toString()).getSymbol(), obj[9].toString(), (Date) ticketPriceRange.get(0)[2],
                                (String) obj[10], (Double) obj[11], (String) obj[12], (long)totalRegistrantsWithStaffs, planConfigNames, (Boolean) obj[13], (Boolean) obj[14], (String) obj[15], (Boolean) obj[16], eventTypeWIthEventId.getOrDefault(eventId, null));
                        finalList.add(endInfoDto);
                    }
                }
            }
        } else {
            Page<Object[]> activeEvents = eventRepository.findActiveEventsByOrganizerURL(organizerURL, isFromOrganizerProfilePage,pageSizeSearchObj.getSearch(), pageRequest);
            List<Long> eventIds = activeEvents.stream().map(e -> Long.parseLong(e[8].toString())).collect(Collectors.toList());
            Map<Long, String> eventTypeWIthEventId = new HashMap<>();
            if(!ObjectUtils.isEmpty(user)) {
                List<Object[]> eventUserTypes = eventRepository.findEventUserTypes(user.getUserId(), eventIds);
                eventTypeWIthEventId = eventUserTypes.stream()
                        .filter(e -> e[0] != null && e[1] != null)
                        .collect(Collectors.toMap(e -> Long.parseLong(e[0].toString()), e -> e[1].toString()));
            }
            Map<Long, EventPlanConfig> eventPlanConfigMap = eventPlanConfigService.findEventPlanConfigMapByEventIds(eventIds);
            dataTableResponse.setRecordsTotal(activeEvents.getTotalElements());
            for (Object[] obj : activeEvents) {
                Long eventId = Long.parseLong(obj[8].toString());
                List<Object[]> ticketPriceRange = ticketingTypeRepository.findTicketPriceRange(eventId, ticketTypes);
                EventPlanConfig eventPlanConfig = eventPlanConfigMap.get(eventId);

                if (eventPlanConfig != null) {
                    String planConfigNames = eventPlanConfig.getPlanConfig().getPlanName();
                    if (PlanConfigNames.SINGLE_EVENT_UNIT.getName().equals(planConfigNames)) {
                        planConfigNames = FREE_PLAN.getName();
                    } else {
                        planConfigNames = eventPlanConfig.getPlanConfig().getDisplayPlanName();
                    }
                    int totalRegistrantsWithStaffs;
                    Event event = eventPlanConfig.getEvent();
                    if (FREE_PLAN.getName().equals(eventPlanConfig.getPlanConfig().getPlanName()) || eventPlanConfig.getPlanConfig().isLatestPlan()) {
                        totalRegistrantsWithStaffs = eventTicketsService.getRegistrationCountIncludingStaffMembersAndExcludingAeAndBrilworksUserIds(event);
                    } else {
                        totalRegistrantsWithStaffs = eventTicketsService.getCountOfUniqueAttendeeIncludingStaffMembersAndExcludingAeAndBrilworksUserIds(event);
                    }
                    if (ticketPriceRange != null && !ticketPriceRange.isEmpty()) {
                        EventInfoDto endInfoDto = new EventInfoDto(eventId, (String) obj[0], (String) obj[1], (Date) obj[2],
                                (String) obj[3], (String) obj[4], (Date) obj[5], (String) obj[6], (Boolean) obj[7],
                                (Double) ticketPriceRange.get(0)[1], (Double) ticketPriceRange.get(0)[0], Currency.valueOf(obj[9].toString()).getSymbol(), obj[9].toString(), (Date) ticketPriceRange.get(0)[2],
                                (String) obj[10], (Double) obj[11], (String) obj[12],(long) totalRegistrantsWithStaffs, planConfigNames, (Boolean) obj[13], (Boolean) obj[14], (String) obj[15], (Boolean) obj[16], eventTypeWIthEventId.getOrDefault(eventId, null));
                        finalList.add(endInfoDto);
                    }
                }
            }
        }

        finalList = ticketingDisplayService.getEventInfoDtoWithTicketTypes(finalList);

        dataTableResponse.setData(finalList);
        dataTableResponse.setRecordsFiltered(finalList.size());
        return dataTableResponse;
    }

    @Override
    public Set<EventInfoDto> getEventsByOrganizerURL(String organizerURL) {
        List<String> ticketTypes = Arrays.asList(TicketType.DONATION.toString(), TicketType.FREE.toString());
        Set<EventInfoDto> finalSet = new HashSet<>();
        List<Object[]> events = eventRepository.findEventsByOrganizerURL(organizerURL);
        for (Object[] obj : events) {
            Long eventId = Long.parseLong(obj[8].toString());
            List<Object[]> ticketPriceRange = ticketingTypeRepository.findTicketPriceRange(eventId, ticketTypes);
            if (ticketPriceRange != null && !ticketPriceRange.isEmpty()) {
                EventInfoDto endInfoDto = new EventInfoDto(eventId, (String) obj[0], (String) obj[1], (Date) obj[2],
                        (String) obj[3], (String) obj[4], (Date) obj[5], (String) obj[6], (Boolean) obj[7],
                        (Double) ticketPriceRange.get(0)[1], (Double) ticketPriceRange.get(0)[0], Currency.valueOf(obj[9].toString()).getSymbol(), (Date) ticketPriceRange.get(0)[2], (Boolean) obj[10]);
                finalSet.add(endInfoDto);
            }
        }
        return finalSet;
    }

    @Override
    public List<OrganizerDto> getListOfOrganizer() {
        return organizerRepository.findAllOrganizer();
    }

    //Moved to RO service change there only and use that method
    @Override
    @Deprecated
    public OrganizerDto getOrganizerByOrganizerURL(String organizerUrl) {
        OrganizerDto organizerDto = getOrganizerByOrganizerPageURL(organizerUrl);
        if (organizerDto != null) {
            String subscriptionId = organizerDto.getSubscriptionId();
            if (subscriptionId != null) {
                Subscription subscription = roChargeBeeService.retrievesubscription(subscriptionId);
                if(subscription != null && subscription.currentTermEnd() != null){
                    organizerDto.setPlanEndDate(new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH).format(subscription.currentTermEnd()));
                }
            }
            setWhiteLabelConfigurationToOrg(organizerDto);
        }else{
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
        return organizerDto;
    }

    @Override
    public OrganizerDto getOrganizerByOrganizerPageURLForLoggedInUser(String organizerUrl, User user) throws Exception {
        OrganizerDto organizerDto = roOrganizerService.getOrganizerDtoByOrganizerPageURL(organizerUrl);
        if (organizerDto != null) {
            if (Boolean.TRUE.equals(roOrganizerService.isUserHaveOrganizerOrSuperAdminAccess(user, organizerDto.getOrganizerId()))) {
                String subscriptionId = organizerDto.getSubscriptionId();
                String mobileSubscriptionId=organizerDto.getMobileChargebeeSubscriptionId();
                if (subscriptionId != null || mobileSubscriptionId != null) {
                    try {
                        Subscription subscription = roChargeBeeService.retrievesubscription(subscriptionId);
                        if (subscription != null && subscription.currentTermEnd() != null){
                            organizerDto.setPlanEndDate(new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH).format(subscription.currentTermEnd()));
                            organizerDto.setRenewalDate(new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH).format(subscription.currentTermEnd().getTime() + 1000));
                            log.info("getOrganizerByOrganizerPageURLForLoggedInUser subscriptionId {}",subscriptionId);
                            if (subscription.cancelledAt() != null) {
                                organizerDto.setPlanEndDate(new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH).format(subscription.cancelledAt()));
                                organizerDto.setRenewalDate(new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH).format(subscription.cancelledAt().getTime() + 1000));
                            }
                        }
                    } catch (NotFoundException e) {
                        log.info("Error while retrieving subscription for {}", subscriptionId);
                    }
                    setOrganizerChargeInfo(organizerDto);
                }
                Optional<PlanConfig> optionalPlanConfig = chargebeePlanRepoService.findById(organizerDto.getPlanId());
                organizerDto.setChargebeePlanId(optionalPlanConfig.map(EventConfig::getChargebeePlanId).orElse(null));
                setWhiteLabelPlanInfoToOrg(organizerDto);
                setOrganizerAddressToOrgDto(organizerDto);
                return organizerDto;
            } else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_ALLOW_TO_EDIT_ORGANIZER_PAGE);
            }
        }
        throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
    }

    public OrganizerDto getOrganizerByOrganizerPageURL(String organizerUrl){
        OrganizerDto organizerDto = roOrganizerService.getOrganizerDtoByOrganizerPageURL(organizerUrl);

        if (organizerDto == null) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
        organizerDto.setHeaderLogoImage(imageConfiguration.getDefaultHeader());

        if(organizerDto.getWhiteLabelId() == null){
            return organizerDto;
        }

        setWhiteLabelPlanInfoToOrg(organizerDto);
        return organizerDto;
    }

    // Moved to RO service change there only and use that
    @Deprecated
    private void setWhiteLabelPlanInfoToOrg(OrganizerDto organizerDto) {
        if(organizerDto.getWhiteLabelId() == null)
            return;
        Optional<WhiteLabel> optionalWhiteLabel = roWhiteLabelService.getWhiteLabelById(organizerDto.getWhiteLabelId());
        if(optionalWhiteLabel.isPresent()){
            WhiteLabel whiteLabel = optionalWhiteLabel.get();
            organizerDto.setHideProductUpdateNotification(whiteLabel.isHideProductUpdateNotification());
            organizerDto.setWhiteLabelURL(whiteLabel.getWhiteLabelUrl());
            organizerDto.setPlanId(whiteLabel.getPlanConfig().getId());
            organizerDto.setChargebeePlanName(whiteLabel.getPlanConfig().getPlanName());
            organizerDto.setSubscriptionId(whiteLabel.getSubscriptionId());
            organizerDto.setChargebeeCustomerId(whiteLabel.getChargebeeCustomerId());
            organizerDto.setWhiteLabelHostBaseUrl(whiteLabel.getHostBaseUrl());
            organizerDto.setEnableWhiteLabelRedirect(whiteLabel.isEnableWhiteLabelRedirect());
            if(PlanConfigNames.WHITE_LABEL_PLAN.getName().equals(whiteLabel.getPlanConfig().getPlanName())) {
                organizerDto.setLogoImage(organizerDto.getLogoImage() != null ? organizerDto.getLogoImage() : whiteLabel.getHeaderLogoImage());
                organizerDto.setHeaderLogoImage(whiteLabel.getHeaderLogoImage() != null ? whiteLabel.getHeaderLogoImage() :organizerDto.getHeaderLogoImage());
            }
        }
    }

    private void setOrganizerAddressToOrgDto(OrganizerDto organizerDto) {
        Optional<AddressDetails> optionalAddressDetails = addressDetailsRepository.findByOrganizerId(organizerDto.getOrganizerId());
        if(optionalAddressDetails.isPresent()) {
            AddressDetails addressDetails = optionalAddressDetails.get();
            organizerDto.setAddress1(addressDetails.getAddress1());
            organizerDto.setAddress2(addressDetails.getAddress2());
            organizerDto.setCity(addressDetails.getCity());
            organizerDto.setState(addressDetails.getState());
            organizerDto.setCountry(addressDetails.getCountry());
            organizerDto.setZipcode(addressDetails.getZipcode());
        }
    }

    @Deprecated
    private void setWhiteLabelConfigurationToOrg(OrganizerDto organizerDto) {
        Long whiteLabelId = organizerDto.getWhiteLabelId();
        if (null != whiteLabelId) {
            Optional<WhiteLabel> optionalWhiteLabel = roWhiteLabelService.getWhiteLabelById(whiteLabelId);
            if (optionalWhiteLabel.isPresent()) {
                WhiteLabel whiteLabel = optionalWhiteLabel.get();
                organizerDto.setWhiteLabelURL(whiteLabel.getWhiteLabelUrl());
                organizerDto.setWhiteLabelFavIcon(whiteLabel.getFaviconDirectory());
                organizerDto.setWhiteLabelFirmName(whiteLabel.getFirmName());
                organizerDto.setHeaderBackgroundColor(whiteLabel.getHeaderColor());
                organizerDto.setHeaderTextColor(whiteLabel.getHeaderFontColor());
                organizerDto.setWhiteLabelHostBaseUrl(whiteLabel.getHostBaseUrl());
                if (null == organizerDto.getLogoImage()) {
                    organizerDto.setLogoImage(whiteLabel.getLogoImage());
                }
            }
        }
    }

    private void setOrganizerChargeInfo(OrganizerDto organizerDto) {
        List<ChargesPurchased> chargesPurchasedList =
                chargesPurchasedRepoService.findByOrganizerId(organizerDto.getOrganizerId());

        List<OrganizerChargeInfoDto> organizerChargeInfoDtos = new ArrayList<>();

        Set<ChargeConfig> chargeConfigs = chargesPurchasedList.stream().map(ChargesPurchased::getChargeConfig).collect(Collectors.toSet());

        chargeConfigs.forEach(e ->
                setChargeDetails(organizerChargeInfoDtos, organizerDto, chargesPurchasedList, e)
        );

        if(!organizerChargeInfoDtos.isEmpty()) {
            organizerDto.setOrganizerChargeInfoDtos(organizerChargeInfoDtos);
        }
    }

    private void setChargeDetails(List<OrganizerChargeInfoDto> organizerChargeInfoDtos, OrganizerDto organizerDto,
                                  List<ChargesPurchased> chargesPurchasedList, ChargeConfig chargeConfig) {
        long totalQty = chargesPurchasedList.stream().filter(e -> chargeConfig.equals(e.getChargeConfig())).count();
        long availableQty = chargesPurchasedList.stream().filter(e -> e.getChargeConfig().equals(chargeConfig)
                && !ChargesPurchased.ChargeStatus.USED.name().equals(e.getChargeStatus().name())).count();

        OrganizerChargeInfoDto organizerChargeInfoDto = new OrganizerChargeInfoDto();
        organizerChargeInfoDto.setOrganizerId(organizerDto.getOrganizerId());
        organizerChargeInfoDto.setTotalQuantity(totalQty);
        organizerChargeInfoDto.setAvailableQuantity(availableQty);
        organizerChargeInfoDto.setChargeConfig(chargeConfig);
        organizerChargeInfoDtos.add(organizerChargeInfoDto);
    }

    @Override
    public String getLogoByOrganizer(String organizerUrl) {
        if (organizerUrl.isEmpty()) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_URL_NOT_FOUND);
        }
        Optional<Organizer> optionalOrganizer = organizerRepository.findOrganizerByOrganizerPageURL(organizerUrl);
        if (optionalOrganizer.isEmpty()) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }

        Organizer organizer = optionalOrganizer.get();
        if ( StringUtils.isNotBlank(organizer.getLogoImage())) {
            return organizer.getLogoImage();
        } else if (organizer.getWhiteLabel() != null && StringUtils.isNotBlank(organizer.getWhiteLabel().getHeaderLogoImage())) {
            return organizer.getWhiteLabel().getHeaderLogoImage();
        }
        return imageConfiguration.getDefaultItem();
    }

    @Override
    public String getOrganizerLogoLocation(Organizer organizer, String size) {
        String logo = organizer.getLogoImage();
        return imageConfiguration.getCloudinaryImageUrlForItem(logo == null ? imageConfiguration.getBlackLogo() : logo, "50");
    }

    @Override
    public List<OrganizerDto> getListOfOrganizerByUser(User user, Event event) {
        if(roUserService.isSuperAdminUser(user)){
            if (event.getWhiteLabel() != null) {
                return organizerRepository.findOrganiserDetailsByWhiteLabel(event.getWhiteLabel());
            }
            return getOrganizerListForSuperAdmin(event);
        } else if(event.getWhiteLabel() != null && roStaffService.hasWhiteLabelAdminOrEventCoordinatorAccess(user, event.getWhiteLabel())) {
            return organizerRepository.findOrganiserDetailsByWhiteLabel(event.getWhiteLabel());
        } else{
            List<JoinUsersWithOrganizers> joinUsersWithOrganizers;
            if(event.getWhiteLabel()!=null){
                joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findOrganizerByWhiteLabel(user.getUserId(),event.getWhiteLabel().getId());
            }else {
                joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findOrganizerByUserAndWhiteLabelIsNull(user.getUserId());
            }
            if (!joinUsersWithOrganizers.isEmpty()) {
                List<Long> organizerIds = joinUsersWithOrganizers.stream().map(JoinUsersWithOrganizers::getOrganizerId).collect(Collectors.toList());
                if(event.getWhiteLabel()!=null) {
                    return (!CollectionUtils.isEmpty(organizerIds)) ? organizerRepository.findOrganiserDetailsByOrganizerIdsAndWhiteLabel(organizerIds, event.getWhiteLabel()) : Collections.emptyList();
                } else {
                    return (!CollectionUtils.isEmpty(organizerIds)) ? organizerRepository.findOrganiserDetailsByOrganizerIds(organizerIds) :  Collections.emptyList();
                }
            } else {
                return Collections.emptyList();
            }
        }
    }

    @Override
    public List<OrganizerDto> getListOfOrganizerByUser(User user) throws Exception {
        List<OrganizerDto> organizerList = joinUsersWithOrganizersRepository.findOrganiserDetailsByUserId(user.getUserId());
        if(roUserService.isWhiteLabelAdminUser(user) || roUserService.isEventCoordinatorUser(user)){
            List<Staff> staffs = roStaffService.findByUserAndWhiteLabelIsNotNull(user, WL_AND_EVENT_COORDINATOR_PERMISSIONS);
            if(CollectionUtils.isNotEmpty(staffs)){
                List<Long> whiteLabelId = staffs.stream().map(Staff::getWhiteLabel).filter(Objects::nonNull).distinct().map(WhiteLabel::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(whiteLabelId)) {
                    organizerList.addAll(organizerRepository.findOrganiserDetailsByWhiteLabelIds(whiteLabelId));
                    if(!organizerList.isEmpty()) {
                        organizerList = organizerList.stream().collect(collectingAndThen(toCollection(() ->
                                        new TreeSet<>(Comparator.comparingLong(OrganizerDto::getOrganizerId))),
                                ArrayList::new));
                    }
                }
            }
        }
        return (!CollectionUtils.isEmpty(organizerList)) ? organizerList : Collections.emptyList();
    }

    private List<OrganizerDto> getOrganizerListForSuperAdmin(Event event){
        List<User> staffUserByEvent = staffService.findAllStaffUserByEvent(event);
        if(event.getWhiteLabel()!=null) {
            List<User> staffUserByWhiteLabel = staffService.findStaffUserByWhiteLabel(event.getWhiteLabel());
            staffUserByEvent.addAll(staffUserByWhiteLabel);
        }
        List<OrganizerDto> organizerDtoList = new ArrayList<>();
        List<JoinUsersWithOrganizers> joinUsersWithOrganizers;
        List<Long> userIds = staffUserByEvent.stream().map(User::getUserId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(staffUserByEvent)) {
            if (event.getWhiteLabel() != null) {
                joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findOrganizerByUserIdsAndWhiteLabel(userIds, event.getWhiteLabelId());
            } else {
                joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findOrganizerByUserIdsAndWhiteLabelIsNull(userIds);
            }
            if(CollectionUtils.isNotEmpty(joinUsersWithOrganizers)) {
                List<Long> organizerIds = joinUsersWithOrganizers.stream().map(JoinUsersWithOrganizers::getOrganizerId).collect(Collectors.toList());
                if (event.getWhiteLabel() != null) {
                    organizerDtoList = (!CollectionUtils.isEmpty(organizerIds)) ? organizerRepository.findOrganiserDetailsByOrganizerIdsAndWhiteLabel(organizerIds, event.getWhiteLabel()) : Collections.emptyList();
                } else {
                    organizerDtoList = (!CollectionUtils.isEmpty(organizerIds)) ? organizerRepository.findOrganiserDetailsByOrganizerIds(organizerIds) : Collections.emptyList();
                }
            }
        }

        if(organizerDtoList.isEmpty())
            return organizerDtoList;

        for(OrganizerDto organizerDto: organizerDtoList){
            if(organizerDto.getWhiteLabelId() != null) {
                setWhiteLabelPlanInfoToOrg(organizerDto);
            }
        }
        return organizerDtoList;
    }

    @Override
    @Transactional
    public void updateOrganizerUrl(String organizerUrl, long organizerId, User user) {

        Optional<Organizer> organizerData = getById(organizerId);
        if (organizerData.isPresent()) {
            Organizer organizer = organizerData.get();
            if (!roUserService.isSuperAdminUser(user) && !(isUserAuthorizedForWhiteLabelOrganizer(user, organizerId) || isEventCoordinatorAuthorizedForWhiteLabelOrganizer(user,organizerId))) {
                checkUserIsAllowToUpdateElseNotThenThrowException(user, organizer);
            }
            organizerUrl = organizerUrl.replaceAll(ATOZ_BOTHCASE_AND_NUMBERS, STRING_EMPTY);
            Optional<Organizer> organizer1 = organizerRepository.findOrganizerByOrganizerPageURL(organizerUrl);
            if (organizer1.isPresent() && organizer1.get() != organizer) {
                organizerUrl = this.getOrganizerUrl(organizerUrl, 0, organizerUrl.length(), true);
            }
            organizer.setOldOrganizerPageURL(organizer.getOrganizerPageURL());
            organizer.setOrganizerPageURL(organizerUrl);
            organizerRepository.save(organizer);
        } else {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
    }

    public String getOrganizerUrl(String url, int count, int urlLength, boolean updateUrl) {
        // remove other characters from url then alphanumeric
        if (!StringUtils.isAlphanumeric(url) && updateUrl) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.URL_NOT_SUPPORT_SPECIAL_CHART);
        }
        Organizer organizer;
        try {
            organizer = this.getOrganizerByURL(url);
        } catch (NotFoundException nfe) {
            if (StringUtils.equals(nfe.getErrorCode(), NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND.getStatusCode())) {
                organizer = null;
            } else {
                throw nfe;
            }
        }
        if (organizer != null && updateUrl) {
            throw new ConflictException(ConflictException.UserExceptionConflictMsg.ORGANIZER_URL_ALREADY_EXIST);
        }
        if (organizer == null) {
            return url;
        } else {
            return getOrganizerUrl(url.substring(0, urlLength) + count, count + 1, urlLength, false);
        }
    }

    @Override
    @Deprecated
    //Moved to RO service change there only and use that
    public Organizer getOrganizerByURL(String url) {
        try {
            url = URLEncoder.encode(url, Constants.UTF8);
        } catch (UnsupportedEncodingException e) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.URL_NOT_SUPPORTED);
        }
        Optional<Organizer> organizer = organizerRepository.findOrganizerByOrganizerPageURL(url);
        if (organizer.isPresent()) {
            return organizer.get();
        } else {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
    }

    @Override
    public Organizer getOrganizerByURLObserbeException(String url) {
        try {
            url = URLEncoder.encode(url, Constants.UTF8);
        } catch (UnsupportedEncodingException e) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.URL_NOT_SUPPORTED);
        }
        Optional<Organizer> organizer = organizerRepository.findOrganizerByOrganizerPageURL(url);
        return organizer.orElse(null);
    }

    @Override
    public void addJoinUsersWithOrganizers(User user, Organizer organizer, OrganizerRole organizerRole, boolean isBillingContact) {
        List<JoinUsersWithOrganizers> joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findJoinOrgByOrgId(organizer.getId());

        isBillingContact = isBillingContact(isBillingContact, joinUsersWithOrganizers);

        Optional<JoinUsersWithOrganizers> usersWithOrganizers = joinUsersWithOrganizers.stream().filter(e -> e.getUserId() == user.getUserId()).findFirst();
        if (!usersWithOrganizers.isPresent()) {
            joinUserWithOrganizerRepoService.associateUserWithOrganization(user, organizer, organizerRole, isBillingContact, organizer.getWhiteLabel() != null ? organizer.getWhiteLabel().getId() : null);
        }
    }

    private boolean isBillingContact(boolean isBillingContact, List<JoinUsersWithOrganizers> joinUsersWithOrganizers) {
        if (isBillingContact) {
            Optional<JoinUsersWithOrganizers> optionalJoinUsersWithOrganizers = joinUsersWithOrganizers.stream().filter(JoinUsersWithOrganizers::isBillingContact).findFirst();
            isBillingContact = !optionalJoinUsersWithOrganizers.isPresent();
        }
        return isBillingContact;
    }

    @Override
    public void deleteJoinUsersWithOrganizers(User user, Organizer organizer) {
        Optional<JoinUsersWithOrganizers> usersWithOrganizers = roJoinUserWithOrganizerService.getByUserIdAndOrgId(user.getUserId(), organizer.getId());
        if(usersWithOrganizers.isPresent() && !OrganizerRole.owner.equals(usersWithOrganizers.get().getRole())) {
            joinUsersWithOrganizersRepository.delete(usersWithOrganizers.get());
        }
    }

    @Override
    public void sendContactEmailToOrganizer(User user, Event event, ContactDto contact, String organizerUrl) {
        if (user == null) {
            if (StringUtils.isBlank(contact.getEmail())) {
                throw new AuthorizationException(NOT_AUTHORIZE);
            }
            user = new User();
            user.setFirstName(contact.getName());
            user.setEmail(contact.getEmail());
        }
        Organizer organizer = this.getOrganizerByURL(organizerUrl);
        EmailMessage emailMessage = new EmailMessage(TemplateId.PARTICIPANT_QUESTION);
        Set<String> receiversList = new HashSet<>();
        if (null != organizer && null != organizer.getContactEmailAddress()) {
            receiversList.add(organizer.getContactEmailAddress());
        }
        EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        emailMessage.setReplyToEmail(user.getEmail());
        emailMessage.setSenderNameFromEvent(eventDesignDetail.getEmailSenderName());
        emailMessage.setSenderMailFromEvent(eventDesignDetail.getNotificationEmail());

        emailMessage.setBody(contact.getMessage());
        emailMessage.setSubject("An event participant has a question");
        BeeFree beeFree = beeFreeRepository.findByEmailTypeAndEventIdZero(EmailType.PARTICIPANT_QUESTION);
        if(beeFree != null && beeFree.isCustomTemplateEnabled()) {
            emailMessage.setTemplateName(PARTICIPANT_QUESTION);
            emailMessage.setTemplate(beeFree.getHtmlValue());
            emailMessage.setBeefreeTemplate(true);
        } else {
            emailMessage.setTemplateName(TemplateId.PARTICIPANT_QUESTION.getValue());
        }

        Map<String, Object> substitutionData = new HashMap<>();
        substitutionData.put("name",
                StringUtils.isNotBlank(user.getFirstName()) ? user.getFirstName() : STRING_DASH);
        substitutionData.put("email", user.getEmail());
        substitutionData.put("eventName", event.getName());
        substitutionData.put("pageUrl", STRING_DASH);
        emailMessage.setSubstitutionData(substitutionData);
        try {
            sendGridMailPrepareService.sendSupportOrContactMailToOrganizer(event, emailMessage, organizer, receiversList);
        } catch (Exception e) {
            log.error("Exception", e);
        }
    }

    @Override
    public List<OrganizerDto> findOrganizersByWhiteLabel(Event event) {
        return organizerRepository.findOrganizersByWhiteLabel(event.getWhiteLabel().getId());
    }
    @Override
    public List<OrganizerDto> findOrganizersByCreatedBy(User user,Event event) {
        if(event.getWhiteLabel()!=null){
            return organizerRepository.findOrganizersByUserAndWhiteLabelId(user,event.getWhiteLabel().getId());
        }else{
            return organizerRepository.findOrganizersByUserAndWhiteLabelIsNull(user);
        }

    }

    @Override
    public Organizer setEventWithOrganizer(Event event, String organizerPageUrl) {
        Organizer organizer = getOrganizerByURLObserbeException(organizerPageUrl);
        if (organizer != null) {
            event.setOrganizerId(organizer.getId());
            event.setOrganizer(organizer);
        }
        return organizer;
    }

    @Override
    public Long getPlanIdBySubscriptionId(String subscriptionId) {
        return organizerRepository.getPlanIdBySubscriptionId(subscriptionId);
    }

    @Override
    public DataTableResponse getAllOrganizerDto(int page, int size, String searchString, String sortField, String sortDirection,User superAdminUser) {
        DataTableResponse dataTableResponse = new DataTableResponse();
        Pageable pageable = getSortablePage(page, size, sortField, sortDirection);
        List<Object> listData = new ArrayList<>();
        List<Long> organizerIds = new ArrayList<>();
        Page<Object[]> listOrganizer;
        if (StringUtils.isNotBlank(searchString)) {
            if (GeneralUtils.isStringContainsUniCode(searchString)) {
                listOrganizer = joinUsersWithOrganizersRepository.getAllFilteredOrganizationWithUnicode(searchString,pageable);
            } else {
                listOrganizer = joinUsersWithOrganizersRepository.getAllFilteredOrganization(searchString,pageable);
            }

        } else {
            listOrganizer = joinUsersWithOrganizersRepository.getAllOrganization(pageable);


        }
        if (CollectionUtils.isNotEmpty(listOrganizer.getContent())) {
            List<Object[]> content = listOrganizer.getContent();
            Map<Long, List<OwnerDto>> ownerDtoMap = new LinkedHashMap<>();
            List<OwnerDto> ownerDtoList;
            for (Object[] organizersList : content) {
                BigInteger orgId = (BigInteger) organizersList[0];
                Long orgValue = orgId.longValue();
                String firstName = (String) organizersList[3];
                String lastName = (String) organizersList[4];
                String email = (String) organizersList[5];
                OwnerDto ownerDto = new OwnerDto(orgValue, firstName, lastName, email);
                if (ownerDtoMap.containsKey(orgValue)) {
                    ownerDtoList = ownerDtoMap.get(orgValue);
                } else {
                    ownerDtoList = new ArrayList<>();
                }
                ownerDtoList.add(ownerDto);
                ownerDtoMap.put(orgValue, ownerDtoList);
            }

            Map<Long, List<OrganizerOwnerDto>> orgOwnerDtoMap = new LinkedHashMap<>();
            List<OrganizerOwnerDto> orgOwnerDtoList;
            for (Object[] organizersList : content) {
                BigInteger orgId = (BigInteger) organizersList[0];
                Long orgValue = orgId.longValue();
                organizerIds.add(orgValue);
                BigInteger eventId = (BigInteger) organizersList[10];
                Long eventIdValue = eventId.longValue();
                BigInteger eventIdCount = (BigInteger) organizersList[12];
                Long eventIdCountValue = null != eventIdCount ? eventIdCount.longValue() : 0;
                OrganizerOwnerDto orgOwnerDto = new OrganizerOwnerDto(orgValue, (String) organizersList[1], (String) organizersList[2], (String) organizersList[3], (String) organizersList[4], (String) organizersList[5], BillingType.valueOf((String) organizersList[6]), (String) organizersList[7], (Date) organizersList[8], (String) organizersList[9], eventIdValue,(String) organizersList[11], eventIdCountValue, (Date) organizersList[13]);
                if (orgOwnerDtoMap.containsKey(orgValue)) {
                    orgOwnerDtoList = orgOwnerDtoMap.get(orgValue);
                } else {
                    orgOwnerDtoList = new ArrayList<>();
                }
                orgOwnerDtoList.add(orgOwnerDto);
                orgOwnerDtoMap.put(orgValue, orgOwnerDtoList);
            }



            Map<Long, Long> orgStaffCountMap = getStaffCountByOrgIds(organizerIds);

            if (MapUtils.isNotEmpty(orgOwnerDtoMap)) {

                orgOwnerDtoMap.values().forEach(e -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put("organizationId", e.get(0).getOrganizationId());
                    data.put("organizationName", e.get(0).getOrgName());
                    data.put("organizationUrl", e.get(0).getOrgUrl());
                    data.put("billingType", e.get(0).getBillingType().name());
                    data.put("firmName", e.get(0).getFirmName());
                    data.put("eventStartDate", e.get(0).getEventStartDate());
                    data.put("name", e.get(0).getEventName());
                    data.put("eventId", e.get(0).getEventId());
                    data.put("whiteLabelURL", e.get(0).getWhiteLabelURL());
                    data.put("ownerList", CollectionUtils.isNotEmpty(ownerDtoMap.get(e.get(0).getOrganizationId())) ? ownerDtoMap.get(e.get(0).getOrganizationId()) : Collections.emptyList());
                    OrganizerDeletionAccessDto organizerDeletionAccessDto = isOrganizerDeletable(e.get(0).getOrganizationId(), superAdminUser);
                    data.put("isOrganizerSuperadmin", organizerDeletionAccessDto.isOrganizerSuperAdmin());
                    data.put("isOrganizerDeletable", organizerDeletionAccessDto.isOrganizerDeletable());
                    data.put("eventCount", e.get(0).getEventCount());
                    data.put("createdDate", e.get(0).getCreatedDate());
                    data.put(STAFF_SMALL, orgStaffCountMap.getOrDefault(e.get(0).getOrganizationId(), 0L));
                    listData.add(data);
                });
            }
            dataTableResponse.setData(listData);
        } else {
            dataTableResponse.setData(Collections.emptyList());
        }
        dataTableResponse.setRecordsTotal(listOrganizer.getTotalElements());
        dataTableResponse.setRecordsFiltered(listOrganizer.getNumberOfElements());
        return dataTableResponse;
    }

    private Map<Long, Long> getStaffCountByOrgIds(List<Long> organizerIds) {

        log.info("Fetching staff count for organizer ids: {} ", organizerIds.size());
        if (org.springframework.util.CollectionUtils.isEmpty(organizerIds)) {
            return Collections.emptyMap();
        }
        List<Object[]> staffCountList =  joinUserWithOrganizerRepoService.getStaffCountByOrgIdsAndRoles(organizerIds, Arrays.asList(OrganizerRole.admin, OrganizerRole.eventandorgadmin, OrganizerRole.owner, OrganizerRole.member));

        if (org.springframework.util.CollectionUtils.isEmpty(staffCountList)) {
            return Collections.emptyMap();
        }

        Map<Long, Long> staffCountMap = staffCountList.stream()
                .filter(organizerStaffCountMap -> organizerStaffCountMap[0] != null && organizerStaffCountMap[1] != null)
                .collect(Collectors.toMap(
                        organizerStaffCountMap -> (Long) organizerStaffCountMap[0],
                        organizerStaffCountMap -> (Long) organizerStaffCountMap[1]
                ));

        log.info("Successfully fetched staff count for {} organizers.", staffCountMap.size());
        return staffCountMap;


    }

    protected Pageable getSortablePage(int page, int size, String sortField, String sortDirection) {
        if (StringUtils.isNoneBlank(sortField)) {
            Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;
            switch (sortField) {
                case BILLING_TYPE:
                    return PageRequest.of(page, size, direction, "organizer.billing_Type");
                case ORGANIZER_NAME:
                    return PageRequest.of(page, size, direction, "organizer.name");
                case Organization_URL:
                    return PageRequest.of(page, size, direction, "organizer.organizer_page_URL");
                case Owner_Name:
                    return PageRequest.of(page, size, direction, "u.first_name");
                case Owner_Email:
                    return PageRequest.of(page, size, direction, "u.email");
                case ENTERPRISE:
                    return PageRequest.of(page, size, direction, "wl.firm_name");
                case NEXT_EVENT:
                    return PageRequest.of(page, size, direction, "x.nextevent");
                case NEXT_EVENT_DATE:
                    return PageRequest.of(page, size, direction, "x.min_start_date");
                default:
                    return PageRequest.of(page, size, direction, "organizer.Id");
            }
        } else {
            return PageRequest.of(page, size, Sort.Direction.DESC, "organizer.Id");
        }
    }

    @Override
    public DataTableResponse getListOfOrganizerForSuperAdmin(int page, int size, String searchString, User user, boolean isFromTemplate, String organizerUrl) {
        Pageable pageable = PageRequest.of(Math.max(page, 0), (size > 0) ? size : 10);
        Page<OrganizerDto> organizerList = Page.empty(pageable);
        if(isFromTemplate && StringUtils.isNotBlank(organizerUrl)) {
            OrganizerDataDto organizerDataDto = organizerRepository.findOrganizerWhiteLabelData(organizerUrl);
            if (organizerDataDto != null && organizerDataDto.isWhiteLabelOrganizer() && null != organizerDataDto.getWhiteLabelId()) {
                organizerList =organizerRepository.findOrganiserDetailsByWhiteLabelId(organizerDataDto.getWhiteLabelId(),pageable,searchString);
            } else {
                organizerList = this.getOrganizerForSuperadmin(user, searchString, pageable, organizerList);
            }
        } else {
            organizerList = this.getOrganizerForSuperadmin(user, searchString, pageable, organizerList);
        }
        organizerList.stream().forEach(e -> {
            e.setAllowSocialSharing(null);
            e.setShowTrayIntegration(null);
            e.setOrganizerId(e.getOrganizerId());
        });
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(organizerList.getTotalElements());
        dataTableResponse.setRecordsFiltered(organizerList.getNumberOfElements());
        dataTableResponse.setData(organizerList.getContent());

        return dataTableResponse;
    }

    private Page<OrganizerDto> getOrganizerForSuperadmin(User user, String searchString, Pageable pageable, Page<OrganizerDto> organizerList) {
        List<Long> organizerIds = joinUsersWithOrganizersRepository.getOrganizerIdsByUserId(user.getUserId());
        organizerList = getOrganizerList(user, organizerIds, pageable,searchString)
                .orElse(Page.empty(pageable));
        return organizerList;
    }

    private Optional<Page<OrganizerDto>> getOrganizerList(User user, List<Long> organizerIds, Pageable pageable, String searchString){

        if(roUserService.isWhiteLabelAdminUser(user) || roUserService.isEventCoordinatorUser(user)){
            List<Staff> staffs = roStaffService.findByUserAndWhiteLabelIsNotNull(user, WL_AND_EVENT_COORDINATOR_PERMISSIONS);

            if(CollectionUtils.isNotEmpty(staffs)){
                List<Long> whiteLabelIds = staffs.stream()
                        .map(Staff::getWhiteLabel)
                        .filter(Objects::nonNull)
                        .distinct()
                        .map(WhiteLabel::getId)
                        .collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(whiteLabelIds)) {
                    return Optional.of(organizerRepository.findOrganiserDetailsByWhiteLabelIdsOrOrganizersIds(whiteLabelIds, organizerIds, pageable, searchString));
                }
            }
        }
        return Optional.of(organizerRepository.findOrganiserDetailsByOrganizersIds(organizerIds, pageable, searchString));
    }

    @Override
    @Transactional
    public OrganizerDto createWLOrganizer(String organizerName, WhiteLabel whiteLabel, OrganizerDto organizerDto, User whiteLabelUser, Boolean needToMapIntegration) {
        Organizer organizer = createNewOrgAndAssociateUserAndPlan(organizerName, whiteLabelUser, whiteLabel);
        hubspotOrganizerService.createHubspotOrganizerAsync(organizer);
        hubspotContactService.asyncAddContactToOrganizerCreatedEvent(whiteLabelUser, organizer.getId(), ENTERPRISE_PAGE, HubspotCustomEventType.ORGANIZER_CREATED);
        organizerDto.setOrganizerId(organizer.getId());
        organizerDto.setOrganizerPageURL(organizer.getOrganizerPageURL());
        organizerDto.setName(organizer.getName());
        organizerDto.setSubscriptionId(whiteLabel.getSubscriptionId());
        organizerDto.setChargebeeCustomerId(whiteLabel.getChargebeeCustomerId());
        organizerDto.setHubspotCompanyId(whiteLabel.getHubspotCompanyId());
        organizerDto.setAllowSocialSharing(organizerDto.isAllowSocialSharing());
        if(needToMapIntegration){
            trayIntegrationService.mapOrganizer(getIntegration(organizer), organizerDto, TrayIOWebhookActionType.CREATE_ORGANIZER, null);
        }
        updateOrganizer(organizer.getId(), organizerDto, whiteLabelUser, needToMapIntegration);
        return organizerDto;
    }

    public List<OrganizerDto> getListOfWhiteLabelEventWithOrg(Long whiteLabelId) {
        List<OrganizerDto> listOfOrganizer = organizerRepository.findOrganizerByAndWhiteLabel(whiteLabelId);
       if (!listOfOrganizer.isEmpty()) {
           List<OrganizerDto> organizerDtoListWithEventCount = new ArrayList<>();
           for (OrganizerDto organiser : listOfOrganizer) {
               Long id = organiser.getOrganizerId();
               Long countOfEventsForOrganiser = eventRepository.findcountOfwhiteLabelEvents(id);
               List<Long> eventsIdList = eventRepository.findEventIdsByOrgainserId(id);
               Set<Long> events = new HashSet<>(eventsIdList);
               long soldTicketCount = 0L;
               long totalTicketsCount = 0L;
               if (!events.isEmpty()) {
                   List<Object[]> soldTicket = eventTicketsRepository.getTicketSoldCount(events, Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
                   if (soldTicket != null && !soldTicket.isEmpty() )
                   {
                       for (Object[] sold : soldTicket) {
                           if (sold.length > 0) {
                               soldTicketCount = soldTicketCount + (Long) sold[1];
                           }
                       }
                   }
                   List<BigDecimal> totalTicketList = eventTicketsRepository.countTotalTicketsByTicketType(events);
                   if (!totalTicketList.isEmpty()) {
                       for(BigDecimal ticktes : totalTicketList) {
                           totalTicketsCount = totalTicketsCount + (ticktes!=null ? ticktes.longValue() : 0);                       }
                   }
               }

               organiser.setLogoImage(getLogoByOrganizer(organiser.getOrganizerPageURL()));
               organiser.setSoldTicketCountForWhiteLabel(soldTicketCount);
               organiser.setTotalTicketCountForWhiteLabel(totalTicketsCount);
               organiser.setCountOfWhiteLabelEvents(countOfEventsForOrganiser);
               setOrganizerAddressToOrgDto(organiser);
               organizerDtoListWithEventCount.add(organiser);
           }
           if (!organizerDtoListWithEventCount.isEmpty()) {
               return organizerDtoListWithEventCount;
           }
       }
        return Collections.emptyList();
    }

    @Override
    public List<OrganizerBasicDto> getListOfOrganiserWithWhiteLabel(long whiteLabelId){
      List<OrganizerBasicDto> listOfOrganisers = organizerRepository.findOrganizerByAndWhiteLabelId(whiteLabelId);
      if(!listOfOrganisers.isEmpty()){
          return listOfOrganisers;
      }
        return Collections.emptyList();
    }

    @Override
    public List<Organizer> findOrganizerByWhiteLabel(Long whitelabelId){
        return organizerRepository.findOrganizerByWhiteLabel(whitelabelId);
    }

    @Override
    public void moveOrganizerToWLAccount(Organizer organizer, WhiteLabel whiteLabel) {

        List<TransactionFeeConditionalLogic> whiteLabelTransactionFeeConditionalLogicList = transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(whiteLabel);
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = transactionFeeConditionalLogicService.getBaseRecordByOrganizer(organizer);
        whiteLabelTransactionFeeConditionalLogicList.forEach(transactionFeeConditionalLogic -> transactionFeeConditionalLogicService.updateTransactionFeeConditionalLogic(transactionFeeConditionalLogic,transactionFeeConditionalLogics,null,organizer,null));
        transactionFeeConditionalLogics.forEach(transactionFeeConditionalLogic -> {
            transactionFeeConditionalLogic.setWhiteLabel(whiteLabel);
            transactionFeeConditionalLogic.setWhiteLabelId(whiteLabel.getId());
        });
        transactionFeeConditionalLogicService.saveAll(transactionFeeConditionalLogics);
        organizer.setWhiteLabel(whiteLabel);
        organizer.setPlanConfig(whiteLabel.getPlanConfig());

        organizer.setSubscriptionId(whiteLabel.getSubscriptionId());
        organizer.setChargebeeCustomerId(whiteLabel.getChargebeeCustomerId());
        organizer.setMobileMonthlySubscriptionId(whiteLabel.getMobileMonthlySubscriptionId());
        log.info("organizer update the Plan while move Organizer into WLAccount for organizer {} from whitelabel {}", organizer.getId() , whiteLabel.getId());

        organizer.setMobilePlanConfig(whiteLabel.getMobilePlanConfig());
        organizer.setMobileSubscriptionId(whiteLabel.getMobileSubscriptionId());
        organizer.setMobileCustomerId(whiteLabel.getMobileCustomerId());
        organizer.setMobileMonthlySubscriptionId(whiteLabel.getMobileMonthlySubscriptionId());
        organizer.setEngageEmailsLimit(whiteLabel.getEngageEmailsLimit());
        organizer.setSubscriptionStatus(whiteLabel.getSubscriptionStatus());
        organizer.setSubscriptionExpireAt(whiteLabel.getSubscriptionExpireAt());
        organizer.setOldSubscriptionId(whiteLabel.getOldSubscriptionId());
        log.info("organizer update the Mobile Plan while move Organizer into WLAccount for organizer {} from whitelabel {}", organizer.getId() , whiteLabel.getId());

        organizer.setEntitlements(whiteLabel.getEntitlements());
        if(organizer.getHubSpotCompanyId() == null) {
            organizer.setHubSpotCompanyId(whiteLabel.getHubspotCompanyId());
        }

        List<JoinUsersWithOrganizers> joinUsersWithOrganizers = joinUserWithOrganizerRepoService.findByOrgId(organizer.getId());
        joinUsersWithOrganizers.forEach(joinUsersWithOrg -> joinUsersWithOrg.setWhiteLabelId(whiteLabel.getId()));
        joinUserWithOrganizerRepoService.saveAll(joinUsersWithOrganizers);

       this.save(organizer);
    }


    @Override
    public void moveOrganizerFromWLAccount(Organizer organizer, WhiteLabel whiteLabel) {
        log.info("moveOrganizerFromWLAccount process started for organizer {} whitelabel {} ", organizer.getId() , whiteLabel.getId());
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = transactionFeeConditionalLogicService.getBaseRecordByOrganizer(organizer);

        transactionFeeConditionalLogics.forEach(transactionFeeConditionalLogic -> {
            if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())) {
                transactionFeeConditionalLogic.setAeFeeFlat(AE_FLAT_FEE_ONE);
                transactionFeeConditionalLogic.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
                transactionFeeConditionalLogic.setFromTicketPriceThreshold(POINT_ZERO_ONE);
                transactionFeeConditionalLogic.setToTicketPriceThreshold(-1);
            } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())) {
                transactionFeeConditionalLogic.setAeFeeFlat(AE_FLAT_FEE_ZERO);
                transactionFeeConditionalLogic.setAeFeePercentage(AE_FEE_PERCENTAGE_ZERO);
                transactionFeeConditionalLogic.setFromTicketPriceThreshold(-1);
                transactionFeeConditionalLogic.setToTicketPriceThreshold(POINT_ZERO_ONE);
            }
           transactionFeeConditionalLogic.setWlAFeeFlat(0d);
           transactionFeeConditionalLogic.setWlBFeeFlat(0d);
           transactionFeeConditionalLogic.setWlAFeePercentage(0d);
           transactionFeeConditionalLogic.setWlBFeePercentage(0d);
           transactionFeeConditionalLogic.setWlAStripeUserId(null);
           transactionFeeConditionalLogic.setWlBStripeUserId(null);
           transactionFeeConditionalLogic.setWhiteLabel(null);
           transactionFeeConditionalLogic.setWhiteLabelId(null);
           log.info("transactionFeeConditionalLogic update while move Organizer From WLAccount for organizer {} whitelabel {} transactionFeeConditionalLogic Id {} ", organizer.getId() , whiteLabel.getId(),  transactionFeeConditionalLogic.getId() );

        });
        transactionFeeConditionalLogicService.saveAll(transactionFeeConditionalLogics);

        organizer.setWhiteLabel(null);
        chargebeePlanService.findByPlanName(FREE_PLAN.getName()).ifPresent(organizer::setPlanConfig);
        log.info("organizer update to FREE Plan while move Organizer From WLAccount for organizer {} whitelabel {} ", organizer.getId() , whiteLabel.getId());
        organizer.setSubscriptionId(null);
        organizer.setChargebeeCustomerId(null);
        organizer.setMonthlySubscriptionId(null);
        organizer.setSubscriptionExpireAt(null);
        organizer.setSubscriptionStatus(null);

        organizer.setMobilePlanConfig(null);
        organizer.setMobileSubscriptionId(null);
        organizer.setMobileCustomerId(null);
        organizer.setMobileMonthlySubscriptionId(null);
        organizer.setOldSubscriptionId(null);

        organizer.setEntitlements(null);
        organizer.setEngageEmailsLimit(0);

        List<JoinUsersWithOrganizers> joinUsersWithOrganizers = joinUserWithOrganizerRepoService.findByOrgId(organizer.getId());
        joinUsersWithOrganizers.forEach(joinUsersWithOrg -> joinUsersWithOrg.setWhiteLabelId(null));
        joinUserWithOrganizerRepoService.saveAll(joinUsersWithOrganizers);
        apiKeyService.deleteApiUserForOrganizerByWLApiUser(organizer, whiteLabel);
        log.info("joinUsersWithOrganizers update while move Organizer From WLAccount for organizer {} whitelabel {} ", organizer.getId() , whiteLabel.getId());

        this.save(organizer);
        log.info("moveOrganizerFromWLAccount process completed for organizer {} whitelabel {} ", organizer.getId() , whiteLabel.getId());
    }

    @Override
    @Transactional
    public void moveOrganizerFromWLAccountToOtherWLAccount(Organizer organizer, WhiteLabel toWhiteLabel) {
        WhiteLabel fromWhiteLabel = organizer.getWhiteLabel();
        log.info("moveOrganizerFromWLAccountToOtherWLAccount process started for organizer {} from WhiteLabel {} to whitelabel {} ", organizer.getId(), fromWhiteLabel.getId(), toWhiteLabel.getId());
        List<TransactionFeeConditionalLogic> organizerTransactionFeeConditionalLogics = transactionFeeConditionalLogicService.getBaseRecordByOrganizer(organizer);

        List<TransactionFeeConditionalLogic> toWhiteLabelTransactionFeeConditionalLogics = transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(toWhiteLabel);
        if (toWhiteLabelTransactionFeeConditionalLogics.isEmpty()) {
            throw new NotFoundException(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND);
        }

        TransactionFeeConditionalLogic greaterThanAndVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> GREATER_THAN_EQUAL_TO.equalsIgnoreCase(e.getOperator()) && e.isVirtualEvent()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(GREATER_THAN_EQUAL_TO, true, false));
        TransactionFeeConditionalLogic greaterThanAndNonVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> GREATER_THAN_EQUAL_TO.equalsIgnoreCase(e.getOperator()) && !e.isVirtualEvent()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(GREATER_THAN_EQUAL_TO, false, false));
        TransactionFeeConditionalLogic greaterThanAndAddon = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> GREATER_THAN_EQUAL_TO.equalsIgnoreCase(e.getOperator()) && e.isAddon()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(GREATER_THAN_EQUAL_TO, false, true));
        TransactionFeeConditionalLogic lessThanAndVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> LESS_THAN.equalsIgnoreCase(e.getOperator()) && e.isVirtualEvent()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(LESS_THAN, true, false));
        TransactionFeeConditionalLogic lessThanAndNonVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> LESS_THAN.equalsIgnoreCase(e.getOperator()) && !e.isVirtualEvent()).findFirst().
                orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(LESS_THAN, false, false));


        organizerTransactionFeeConditionalLogics.forEach(transactionFeeConditionalLogic -> {
            if(transactionFeeConditionalLogic.isAddon()) {
                transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, greaterThanAndAddon, toWhiteLabel);
            } else if(transactionFeeConditionalLogic.isVirtualEvent()) {
                // Set the TFCL for Virtual Event
                if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, greaterThanAndVirtual, toWhiteLabel);
                } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, lessThanAndVirtual, toWhiteLabel);
                }
            } else {
                // Set the TFCL for Non-Virtual Event
                if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, greaterThanAndNonVirtual, toWhiteLabel);
                } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, lessThanAndNonVirtual, toWhiteLabel);
                }
            }
            log.info("transactionFeeConditionalLogic update while move Organizer From WLAccount to other WLAccount for organizer {} from whitelabel {} to whitelabel {} transactionFeeConditionalLogic Id {} ", organizer.getId(), fromWhiteLabel.getId(), toWhiteLabel.getId(),  transactionFeeConditionalLogic.getId() );

        });
        transactionFeeConditionalLogicService.saveAll(organizerTransactionFeeConditionalLogics);

        organizer.setWhiteLabel(toWhiteLabel);
        organizer.setPlanConfig(toWhiteLabel.getPlanConfig());
        organizer.setSubscriptionId(toWhiteLabel.getSubscriptionId());
        organizer.setChargebeeCustomerId(toWhiteLabel.getChargebeeCustomerId());
        organizer.setMobileMonthlySubscriptionId(toWhiteLabel.getMobileMonthlySubscriptionId());
        log.info("organizer update to WLAccount Plan while move Organizer From WLAccount to other WLAccount for organizer {} from whitelabel {} to whitelabel {} ", organizer.getId() , fromWhiteLabel.getId(), toWhiteLabel.getId());

        organizer.setMobilePlanConfig(toWhiteLabel.getMobilePlanConfig());
        organizer.setMobileSubscriptionId(toWhiteLabel.getMobileSubscriptionId());
        organizer.setMobileCustomerId(toWhiteLabel.getMobileCustomerId());
        organizer.setMobileMonthlySubscriptionId(toWhiteLabel.getMobileMonthlySubscriptionId());
        organizer.setSubscriptionStatus(toWhiteLabel.getSubscriptionStatus());
        organizer.setSubscriptionExpireAt(toWhiteLabel.getSubscriptionExpireAt());
        organizer.setOldSubscriptionId(toWhiteLabel.getOldSubscriptionId());
        organizer.setEngageEmailsLimit(toWhiteLabel.getEngageEmailsLimit());
        log.info("organizer update the Mobile Plan while move Organizer From WLAccount to other WLAccount for organizer {} from whitelabel {} to whitelabel {} ", organizer.getId() , fromWhiteLabel.getId(), toWhiteLabel.getId());

        organizer.setEntitlements(toWhiteLabel.getEntitlements());

        if(organizer.getHubSpotCompanyId() == null) {
            organizer.setHubSpotCompanyId(toWhiteLabel.getHubspotCompanyId());
            log.info("organizer HubSpotCompanyId set to toWLAccount HubspotCompanyId while move Organizer From WLAccount for organizer {} from whitelabel {} to whitelabel {} organizer HubSpotCompanyId {} ", organizer.getId() , fromWhiteLabel.getId(), toWhiteLabel.getId(), toWhiteLabel.getHubspotCompanyId() );
        }

        List<JoinUsersWithOrganizers> joinUsersWithOrganizers = joinUserWithOrganizerRepoService.findByOrgId(organizer.getId());
        joinUsersWithOrganizers.forEach(joinUsersWithOrg -> joinUsersWithOrg.setWhiteLabelId(toWhiteLabel.getId()));
        joinUserWithOrganizerRepoService.saveAll(joinUsersWithOrganizers);
        apiKeyService.deleteApiUserForOrganizerByWLApiUser(organizer, fromWhiteLabel);
        apiKeyService.updateApiUserForWLOrganizer(organizer, toWhiteLabel);
        log.info("joinUsersWithOrganizers update while move Organizer From WLAccount to other WLAccount for organizer {} from whitelabel {} to whitelabel {}", organizer.getId() , fromWhiteLabel.getId(), toWhiteLabel.getId());


        this.updateDeleteStatusToClientApiKey(organizer);

        if(fromWhiteLabel.getHubspotCompanyId() !=null) {
            hubspotOrganizerService.deleteCompanyOrganizerAssociation(organizer.getId(),String.valueOf(fromWhiteLabel.getHubspotCompanyId()));
        }
        if(toWhiteLabel.getHubspotCompanyId() !=null) {
            hubspotOrganizerService.createCompanyOrganizerAssociation(organizer.getId(),String.valueOf(toWhiteLabel.getHubspotCompanyId()));
        }
        this.save(organizer);
        log.info("moveOrganizerFromWLAccountToOtherWLAccount process completed for organizer {} from whitelabel {} to whitelabel {}", organizer.getId() , fromWhiteLabel.getId(), toWhiteLabel.getId());
    }

    @Override
    public void hideEventsFromOrganizerProfilePage(String eventUrl, boolean value) {
        Event event = eventRepository.findEventByEventURLWithoutCache(eventUrl);
        if (event != null) {
            event.setHideEventFromOrganizer(value);
            eventRepoService.save(event);
        }else {
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }
    }

    @Override
    public void hideEventsFromApp(String eventUrl, boolean value) {
        Event event = eventRepository.findEventByEventURLWithoutCache(eventUrl);
        if (event != null) {
            event.setHideEventFromApp(value);
            eventRepoService.save(event);
        }else {
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }
    }

    @Override
    public void saveChargebeeCustomerIdInOrganizer(Organizer organizer, User billingContact,String chargeBeeCustomerCreationDescription) {
        if (organizer.getWhiteLabel() != null) {
            organizer.setChargebeeCustomerId(organizer.getWhiteLabel().getChargebeeCustomerId());
        } else {
            Customer customer = chargebeePaymentService.createCustomerIfNotExistInOrgOrWL(organizer.getChargebeeCustomerId(), billingContact, organizer.getName(),chargeBeeCustomerCreationDescription);
            organizer.setChargebeeCustomerId(customer != null ? customer.id() : null);
            log.info("saveChargeBeeCustomerIdInOrganizer chargeBee CustomerId {} date {}", customer != null ? customer.id() : null, new Date());
        }
        organizerRepository.save(organizer);
    }

    @Override
    public BillingSummerDto getBillingSummeryForOrganizer(long organizerId) {

        Optional<Organizer> optionalOrganizer = getById(organizerId);
        if (optionalOrganizer.isEmpty()) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
        return chargebeeBillingSummeryService.getBillingSummeryForOrganizer(optionalOrganizer.get());
    }

    @Override
    public OrganizerSettingDto getOrganizerSetting(String organizerUrl ,boolean isChargeBeeConfigAdminRole,User superAdminUser){
        OrganizerSettingDto organizerSettingDto =  organizerRepository.getOrganizerSetting(organizerUrl);
        OrganizerSettingDto settingDto = new OrganizerSettingDto();
        if (isChargeBeeConfigAdminRole){
            settingDto.setWaiveOffAttendeesUploadFee(organizerSettingDto.isWaiveOffAttendeesUploadFee());
            settingDto.setExpoBoothCharge(organizerSettingDto.getExpoBoothCharge());
            settingDto.setAttendeeUploadCharge(organizerSettingDto.getAttendeeUploadCharge());
        }
        settingDto.setBillingType(organizerSettingDto.getBillingType());
        settingDto.setAutoBilling(organizerSettingDto.isAutoBilling());
        settingDto.setOrganizerPageURL(organizerSettingDto.getOrganizerPageURL());
        settingDto.setEngageEmailsLimit(organizerSettingDto.getEngageEmailsLimit());
        return settingDto;
    }

    @Transactional
    @Override
    public void updateOrganizerSetting(User superAdminUser, OrganizerSettingDto organizerSettingDto, boolean isChargeBeeConfigAdminRole) {
        log.info("updateOrganizerSetting organiserPageUrl {} updatedBy {}", organizerSettingDto.getOrganizerPageURL(), superAdminUser.getUserId());
        Organizer organizer = getOrganizerByURL(organizerSettingDto.getOrganizerPageURL());
        boolean isBillingTypeUpdated = !organizer.getBillingType().equals(organizerSettingDto.getBillingType());
        if (isChargeBeeConfigAdminRole) {
            if (!organizer.getAttendeeUploadCharge().equals(organizerSettingDto.getAttendeeUploadCharge())) {
                organizer.setAttendeeUploadCharge(organizerSettingDto.getAttendeeUploadCharge());
            }
            if (!organizer.getExpoBoothCharge().equals(organizerSettingDto.getExpoBoothCharge())) {
                organizer.setExpoBoothCharge(organizerSettingDto.getExpoBoothCharge());
            }
            if (organizerSettingDto.isWaiveOffAttendeesUploadFee() != organizer.isWaiveOffAttendeesUploadFee()) {
                List<Event> events = roEventService.findByOrganizerId(organizer.getId());
                for (Event event : events) {
                    event.setAttendeesUploadCharge(organizerSettingDto.isWaiveOffAttendeesUploadFee() ?
                            AttendeesUploadCharge.WAIVE_OFF : AttendeesUploadCharge.CHARGE);
                }
                eventRepoService.saveAll(events);
            }
            organizer.setWaiveOffAttendeesUploadFee(organizerSettingDto.isWaiveOffAttendeesUploadFee());
        }
        int autoBilling = Boolean.compare(organizer.isAutoBilling(), organizerSettingDto.isAutoBilling());
        if (autoBilling != 0) {
            organizer.setAutoBilling(organizerSettingDto.isAutoBilling());
            log.info("Auto Billing Flag for organizer {} Changed to {} by {}", organizer.getId(), organizerSettingDto.isAutoBilling(), superAdminUser);
        }
        if (isBillingTypeUpdated) {
            userService.checkIsBillingTypeAdminOrThrowError(superAdminUser);

            organizer.setBillingType(organizerSettingDto.getBillingType());
            log.info("Billing Type for organizer {} Changed to {} by {}", organizer.getId(), organizerSettingDto.getBillingType(), superAdminUser);
            transactionFeeConditionalLogicService.updateTransCondLogic(organizer);
        }
        updateEngageEmailLimit(organizer, organizerSettingDto);
        organizerRepoService.save(organizer);
    }

    @Override
    public List<EventCreditDistributionDto> organizerCreditDistributionBasedOnEventDays(Organizer organizer) {
        ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsService.findByOrganizerId(organizer.getId());
        long scaleRemainingCount = 0L;
        long enterpriseRemainingCount = 0L;
        for (PlanConfig chargebeePlan : chargebeePlanRepoService.findAll()) {
            if (chargebeePlan.getPlanName().equalsIgnoreCase(PlanConfigNames.SCALE_PLAN.getName())|| chargebeePlan.getPlanName().equalsIgnoreCase(PlanConfigNames.BUSINESS_PLAN_2023.getName()))
                scaleRemainingCount =null!=chargebeeEventCredits? chargebeeEventCredits.getRemainingFreeCredits():chargebeePlan.getFreeQuantity();
            else if (chargebeePlan.getPlanName().equalsIgnoreCase(PlanConfigNames.ENTERPRISE.getName()) || chargebeePlan.getPlanName().equalsIgnoreCase(PlanConfigNames.ENTERPRISE_PLAN_2023.getName()))
                enterpriseRemainingCount = null!=chargebeeEventCredits?chargebeeEventCredits.getRemainingFreeCredits():chargebeePlan.getFreeQuantity();
        }
        if (NumberUtils.isNumberGreaterThanZero(organizer.getFreeQuantity())) {
            scaleRemainingCount = organizer.getFreeQuantity();
            enterpriseRemainingCount = organizer.getFreeQuantity();
        }
        List<EventCreditDistributionDto> eventCreditDistributionDtoList = new ArrayList<>();

        List<EventNameLookUpDto> eventNameLookUpDtoList = eventRepoService.getAllEventsByOrganizerId(organizer.getId());
        if (!eventNameLookUpDtoList.isEmpty()) {
            for (EventNameLookUpDto eventNameLookUpDto : eventNameLookUpDtoList) {
                PlanConfig planConfig = eventPlanConfigRepoService.findByEventId(eventNameLookUpDto.getEventId()).getPlanConfig();
                EventCreditDistributionDto eventCreditDistributionDto = retrieveEventAttendeesCreditDistribution(eventNameLookUpDto,planConfig.isLatestPlan());
                if(eventCreditDistributionDto != null) {
                    if(planConfig.getPlanName().equalsIgnoreCase(PlanConfigNames.STARTER.getName()) || planConfig.getPlanName().equalsIgnoreCase(PlanConfigNames.PROFESSIONAL.getName())){
                        eventCreditDistributionDto.setRemainingAttendeeCount(0L);
                    }else if(planConfig.getPlanName().equalsIgnoreCase(PlanConfigNames.SCALE_PLAN.getName()) || planConfig.getPlanName().equalsIgnoreCase(PlanConfigNames.BUSINESS_PLAN_2023.getName())){
                        scaleRemainingCount = scaleRemainingCount - eventCreditDistributionDto.getFreeCredits();
                        eventCreditDistributionDto.setRemainingAttendeeCount(NumberUtils.isNumberGreaterThanZero(scaleRemainingCount) ? scaleRemainingCount : 0);
                    }else if(planConfig.getPlanName().equalsIgnoreCase(PlanConfigNames.ENTERPRISE.getName()) || planConfig.getPlanName().equalsIgnoreCase(PlanConfigNames.ENTERPRISE_PLAN_2023.getName())){
                        enterpriseRemainingCount = enterpriseRemainingCount - eventCreditDistributionDto.getFreeCredits();
                        eventCreditDistributionDto.setRemainingAttendeeCount(NumberUtils.isNumberGreaterThanZero(enterpriseRemainingCount) ? enterpriseRemainingCount : 0);
                    }
                    eventCreditDistributionDtoList.add(eventCreditDistributionDto);
                }
            }
        }
        return eventCreditDistributionDtoList;
    }
    public EventCreditDistributionDto retrieveEventAttendeesCreditDistribution(EventNameLookUpDto eventNameLookUpDto,boolean isLatestPlan) {
        EventCreditDistributionDto eventCreditDistributionDto =  null;
        List<ChargebeeEventUsages> eventChargeUsages = chargebeeEventUsagesRepoService.findByEventId(eventNameLookUpDto.getEventId());
        eventChargeUsages = eventChargeUsages.stream().filter(eventChargeUsage ->
                ProcessingStatus.SUCCESS.equals(eventChargeUsage.getProcessingStatus())).collect(Collectors.toList());

        if (!eventChargeUsages.isEmpty()) {
            eventCreditDistributionDto = new EventCreditDistributionDto();
            long eventId = eventChargeUsages.get(0).getEventId();
            long planId = eventChargeUsages.get(0).getPlanId();
            Optional<PlanConfig> planConfig = chargebeePlanService.findById(planId);
            eventCreditDistributionDto.setEventId(eventId);
            eventCreditDistributionDto.setEventPlan(planConfig.isPresent() ? planConfig.get().getDisplayPlanName() : "");
            eventCreditDistributionDto.setEventURL(eventNameLookUpDto.getEventURL());
            eventCreditDistributionDto.setLatestPlan(isLatestPlan);
            Long freeCredits = 0L;
            Long paidCredits = 0L;
            boolean invoiced=false;
            for (ChargebeeEventUsages eventChargeUsage : eventChargeUsages) {
                if (UsagesType.PAID.equals(eventChargeUsage.getCreditType())) {
                    if (eventChargeUsage.getInvoice() != null && !invoiced && !eventChargeUsage.getInvoice().startsWith("draft")) {
                        eventCreditDistributionDto.setInvoiceId("https://" + chargebeeConfiguration.getChargebeeSite() + ".chargebee.com/d/invoices/" + eventChargeUsage.getInvoice());
                        invoiced = true;
                    }
                    paidCredits += eventChargeUsage.getQuantity();
                } else {
                    freeCredits += eventChargeUsage.getQuantity();
                }
            }
            eventCreditDistributionDto.setPaidCredits(paidCredits);
            eventCreditDistributionDto.setFreeCredits(freeCredits);
            Ticketing ticketing = ticketingRepository.findByEventId(eventId);
            eventCreditDistributionDto.setEventEndDate(ticketing.getEventEndDate());
            if (isLatestPlan) {
                eventCreditDistributionDto.setRegistrantCreditsUsed(paidCredits + freeCredits);
                eventCreditDistributionDto.setRegistrantUsageInvoiced(paidCredits);
            }
            else{
                this.setAttendeeCountAndEventDaysAndFinalAttendeeDays(ticketing,eventCreditDistributionDto,eventId);
            }
        }
        return eventCreditDistributionDto;
    }

    public void setAttendeeCountAndEventDaysAndFinalAttendeeDays(Ticketing ticketing,EventCreditDistributionDto eventCreditDistributionDto,Long eventId){
        long numberOfDaysOfEvent = 0;
        Long attendeeCount = 0L;
        List<Object[]> attendeeUsages;//NOSONAR
        EventListingStatus eventListingStatus = eventRepoService.getEventListingStatusByEventId(eventId);
        if(eventListingStatus.equals(EventListingStatus.PREVIEW)) {
            attendeeUsages = checkInAuditLogRepoService.attendeeUsagesByEventForPreviewType(eventId);
            if (CollectionUtils.isNotEmpty(attendeeUsages)) {
                attendeeCount = (Long.parseLong(attendeeUsages.get(0)[0].toString())) - 25;
                eventCreditDistributionDto.setAttendeeCount(NumberUtils.isNumberGreaterThanZero(attendeeCount) ? attendeeCount : 0);
                numberOfDaysOfEvent = attendeeUsages.get(0)[1] != null ? Long.parseLong(String.valueOf(attendeeUsages.get(0)[1])) : 0;
            }
        }else{
            attendeeUsages = checkInAuditLogRepoService.attendeeUsagesByEvent(eventId);
            if (!attendeeUsages.isEmpty()) {
                attendeeCount = (Long.valueOf(attendeeUsages.get(0)[0].toString()));
                eventCreditDistributionDto.setAttendeeCount(attendeeCount);
                numberOfDaysOfEvent = attendeeUsages.get(0)[1] != null ? Long.parseLong(String.valueOf(attendeeUsages.get(0)[1])) : 0;
            }
        }
        if(!NumberUtils.isNumberGreaterThanZero(numberOfDaysOfEvent)){
            numberOfDaysOfEvent = DateUtils.getNumberDaysInclusiveBothTheDatesInEventTimeZone
                    (ticketing.getEventStartDate(), ticketing.getEventEndDate(), ticketing.getEventid().getEquivalentTimeZone());
        }
        if (NumberUtils.isNumberGreaterThanZero(numberOfDaysOfEvent)) {
            eventCreditDistributionDto.setEventDays(numberOfDaysOfEvent);
            eventCreditDistributionDto.setFinalAttendeeDays(numberOfDaysOfEvent * (NumberUtils.isNumberGreaterThanZero(attendeeCount) ? attendeeCount : 0));
        }
    }

    @Override
    public AccessTokenModel generateAccessTokenModelForApiUser(long organizerId) {
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        List<Event> events = roEventService.findByOrganizerId(organizer.getId());
        return userService.signUpApiUserAndReturnAccessTokenModel(prepareAdminSignUpDto(organizer),events, null, organizer);
    }

    @Override
    public String retrieveApiKeyIfExistForOrganizer(long organizerId) {
        validateOrganizerByPlanAndWhitelabel(organizerId);
        Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizerId);
        return apiKeyOptional.isPresent()?apiKeyOptional.get().getApiKey():STRING_EMPTY;
    }

    private void validateOrganizerByPlanAndWhitelabel(long organizerId) {
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        if(organizer.getWhiteLabel()!=null) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.API_KEY_NOT_AVAILABLE_WHITELABEL_ORGANIZER);
        }
        throwErrorIfFreeOrStarterPlanConfigure(organizer);
    }

    private void throwErrorIfFreeOrStarterPlanConfigure(Organizer organizer) {
        String planName = organizer.getPlanConfig().getPlanName();
        boolean isOnlyStarterPlanExist = false;
        if(PlanConfigNames.SINGLE_EVENT_UNIT.getName().equals(planName)){
            List<ChargesPurchased> chargesPurchasedList =
                    chargesPurchasedRepoService.findByOrganizerId(organizer.getId());
            if(CollectionUtils.isNotEmpty(chargesPurchasedList)) {
                Optional<ChargesPurchased> professionalChargesPurchasedOptional = chargesPurchasedList.stream()
                        .filter(e -> ChargeConfigNames.PROFESSIONAL.getLableName().equals(e.getChargeConfig().getChargeDisplayName())).findAny();
                Optional<ChargesPurchased> starterChargesPurchasedOptional = chargesPurchasedList.stream()
                        .filter(e -> ChargeConfigNames.STARTER.getLableName().equals(e.getChargeConfig().getChargeDisplayName())).findAny();
                if(!professionalChargesPurchasedOptional.isPresent() && starterChargesPurchasedOptional.isPresent()) {
                    isOnlyStarterPlanExist = true;
                }
            }
        }
        if (PlanConfigNames.FREE_PLAN.getName().equals(planName) || isOnlyStarterPlanExist) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.API_KEY_NOT_AVAILABLE_FOR_PLAN);
        }
    }

    @Override
    public String retrieveApiKeyAndInsertAuditLog(long organizerId, User user) {
        validateOrganizerByPlanAndWhitelabel(organizerId);
        String apiKey = STRING_EMPTY;
        Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizerId);
        if(apiKeyOptional.isPresent()) {
            ClientApiKey clientApiKey = apiKeyOptional.get();
            apiKeyService.saveApiKeyAuditLog(clientApiKey, user, ApiKeyAction.VIEWED);
            apiKey = clientApiKey.getApiKey();
        }
        return apiKey;
    }

    @Override
    public ApiKeyDto getApiKeyAuditDetails(long organizerId) {
        validateOrganizerByPlanAndWhitelabel(organizerId);
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizer.getId());
        return apiKeyService.getApiKeyAndAuditDetails(organizer.getId(), 0, apiKeyOptional);
    }

    @Override
    public String generateAccessKey(long organizerId, String accessToken, Long userId, User organizerUSer) {
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizerId);
        if(apiKeyOptional.isPresent()) {
            hubspotOrganizerService.updateHubspotOrganizerProperty(organizer, API_KEY);
            return apiKeyOptional.get().getApiKey();
        } else {
            String key = SecurityUtils.encode(UUID.randomUUID() + "_" + organizer.getOrganizerPageURL());
            ClientApiKey clientApiKey = new ClientApiKey();
            clientApiKey.setApiKey(key);
            clientApiKey.setOrganizer(organizer);
            clientApiKey.setOrganizerId(organizerId);
            clientApiKey.setAccessToken(accessToken);
            clientApiKey.setUserId(userId);
            clientApiKey.setCreatedDate(new Date());
            clientApiKey = apiKeyService.save(clientApiKey);
            apiKeyService.saveApiKeyAuditLog(clientApiKey, organizerUSer, ApiKeyAction.CREATED);
            hubspotOrganizerService.updateHubspotOrganizerProperty(organizer, API_KEY);
            return clientApiKey.getApiKey();
        }
    }

    private AdminSignupDto prepareAdminSignUpDto(Organizer organizer) {
        AdminSignupDto adminSignupDto = new AdminSignupDto();
        adminSignupDto.setEmail(organizer.getOrganizerPageURL()+"_"+System.currentTimeMillis()+"@apiuser.com");
        adminSignupDto.setFirstName(organizer.getOrganizerPageURL()+"_api_user");
        adminSignupDto.setPassword(organizer.getName()+"@"+System.currentTimeMillis());
        return adminSignupDto;
    }

    @Override
    public void updateDeleteStatusToClientApiKey(Organizer organizer) {
        if(organizer!=null) {
            Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizer.getId());
            if(apiKeyOptional.isPresent()){
                ClientApiKey clientApiKey = apiKeyOptional.get();
                apiKeyService.deleteClientApiKey(clientApiKey);
            }
        }
    }

    @Override
    public String rotateApiKeyForOrganizer(long organizerId, User user) {
        validateOrganizerByPlanAndWhitelabel(organizerId);
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizer.getId());
        String key = SecurityUtils.encode(UUID.randomUUID() + "_" + organizer.getOrganizerPageURL());
        String apiKey = apiKeyService.rotateApiKey(apiKeyOptional, key, user);
        hubspotOrganizerService.updateHubspotOrganizerProperty(organizer, API_KEY);
        return apiKey;
    }

    @Override
    public void deactivateApiKeyForOrganizer(long organizerId, User user) {
        validateOrganizerByPlanAndWhitelabel(organizerId);
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizer.getId());
        apiKeyService.deactivateApiKey(apiKeyOptional, user);
    }

    @Override
    public void deleteOrganizer(Long organizerId, User user) {
        Optional<Organizer> organizer = organizerRepository.findById(organizerId);
        if (!organizer.isPresent()) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
        if (!isOrganizerDeletableForHost(organizer.get())) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CANNOT_DELETE_ORGANIZER_WITH_PLAN_OR_INTEGRATION);
        }
        List<Event> events = roEventService.findByOrganizerId(organizerId);

        DeletedEventsDto deletedEventsDto = new DeletedEventsDto("organizer Delete",
                "Deleted organizer's events by  superAdmin");
        events.forEach(event ->  eventService.deleteEvent(event.getEventId(),user,deletedEventsDto));
        joinUsersWithOrganizersRepository.deleteJoinUsersWithOrganizerByOrganizer(organizerId);
        transactionFeeConditionalLogicService.updateRecStatusOfTransactionFeeConditionalLogicForOrganizer(organizer.get(), RecordStatus.DELETE);
        organizerRepository.deleteOrganizer(organizerId, organizer.get().getOrganizerPageURL(), RecordStatus.DELETE);
    }

    private boolean isOrganizerDeletableForHost(Organizer organizer) {
        if (organizer.getWhiteLabel() != null) {
            WhiteLabel whiteLabel = organizer.getWhiteLabel();
            List<IntegrationEnabledDetailsDto> integrationEnabledDetailsDtos = integrationRepository.findIdByIntegrationSourceId(whiteLabel.getId(), IntegrationSourceType.WHITE_LABEL);
            return whiteLabel.getPlanConfig().getPlanName().equals(FREE_PLAN.getName()) && integrationEnabledDetailsDtos.isEmpty();
        } else {
            List<IntegrationEnabledDetailsDto> integrationEnabledDetailsDtos = integrationRepository.findIdByIntegrationSourceId(organizer.getId(), IntegrationSourceType.ORGANIZER);
            return organizer.getPlanConfig().getPlanName().equals(FREE_PLAN.getName()) && integrationEnabledDetailsDtos.isEmpty();
        }
    }

    private Integration getIntegration(Organizer organizer){
        Optional<Integration> integration = Optional.empty();
        if(organizer.getWhiteLabel() != null){
            integration = integrationRepository.findByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(organizer.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);
        }
        return integration.orElse(null);
    }

    @Override
    public boolean isUserAuthorizedForWhiteLabelOrganizer(User user, Long organizerId) {
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        if(organizer==null || organizer.getWhiteLabel()==null){
            return false;
        }
        List<String> userRoles = this.roUserService.getUserRoles(user);
        return roStaffService.isWhiteLabelAdmin(user, userRoles, organizer.getWhiteLabel());
    }

    @Override
    public boolean isEventCoordinatorAuthorizedForWhiteLabelOrganizer(User user, Long organizerId) {
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        if (organizer.getWhiteLabel() == null) {
            return false;
        }
        List<String> userRoles = this.roUserService.getUserRoles(user);
        return roStaffService.isEventCoordinator(user, userRoles, organizer.getWhiteLabel());
    }

    private void saveOrganizerAddress(Organizer organizer, OrganizerDto organizerDto) {
        Optional<AddressDetails> optionalAddressDetails = addressDetailsRepository.findByOrganizerId(organizer.getId());
        AddressDetails addressDetails = null;
        if (optionalAddressDetails.isPresent()) {
            addressDetails = optionalAddressDetails.get();
        }
        if (isOrganizerAddressExistInDto(organizerDto)) {
            if (addressDetails == null) {
                addressDetails = new AddressDetails();
                addressDetails.setOrganizer(organizer);
                addressDetails.setOrganizerId(organizer.getId());
            }
            addressDetails.setAddress1(organizerDto.getAddress1());
            addressDetails.setAddress2(organizerDto.getAddress2());
            addressDetails.setState(organizerDto.getState());
            addressDetails.setCity(organizerDto.getCity());
            addressDetails.setCountry(organizerDto.getCountry());
            addressDetails.setZipcode(organizerDto.getZipcode());
            log.info("saveOrganizerAddress state {} city {} country {}", organizerDto.getState(), organizerDto.getCity(), organizerDto.getCountry());
            addressDetailsRepository.save(addressDetails);

        } else {
            if (addressDetails != null) {
                addressDetails.setRecordStatus(RecordStatus.DELETE);
                addressDetailsRepository.save(addressDetails);
            }
        }
    }

    private boolean isOrganizerAddressExistInDto(OrganizerDto organizerDto) {
        return StringUtils.isNotBlank(organizerDto.getAddress1()) || StringUtils.isNotBlank(organizerDto.getAddress2()) ||
                StringUtils.isNotBlank(organizerDto.getCity()) || StringUtils.isNotBlank(organizerDto.getState()) ||
                StringUtils.isNotBlank(organizerDto.getCountry()) || StringUtils.isNotBlank(organizerDto.getZipcode());
    }

    //For few superAdmin only


    public OrganizerDeletionAccessDto isOrganizerDeletable(Long organizerId, User user) {
        Optional<Organizer> organizer = organizerRepository.findById(organizerId);
        OrganizerDeletionAccessDto organizerDeletionAccessDto=new OrganizerDeletionAccessDto();
        if (organizer.isPresent()) {
        List<String> userRoles = this.roUserService.getUserRoles(user);
            organizerDeletionAccessDto.setOrganizerSuperAdmin(userService.isOrganizerSuperAdminRole(user, userRoles));
            if(organizerDeletionAccessDto.isOrganizerSuperAdmin()){
            if(organizer.get().getWhiteLabel()!=null){
                WhiteLabel whiteLabel=organizer.get().getWhiteLabel();
                List<IntegrationEnabledDetailsDto> integrationEnabledDetailsDtos=  integrationRepository.findIdByIntegrationSourceId(whiteLabel.getId(),IntegrationSourceType.WHITE_LABEL);
                organizerDeletionAccessDto.setOrganizerDeletable(whiteLabel.getPlanConfig().getPlanName().equals(FREE_PLAN.getName()) && integrationEnabledDetailsDtos.isEmpty());
            }else {
                List<IntegrationEnabledDetailsDto> integrationEnabledDetailsDtos=  integrationRepository.findIdByIntegrationSourceId(organizerId,IntegrationSourceType.ORGANIZER);
                organizerDeletionAccessDto.setOrganizerDeletable(organizer.get().getPlanConfig().getPlanName().equals(FREE_PLAN.getName()) && integrationEnabledDetailsDtos.isEmpty());
            }
            }
        }
        return  organizerDeletionAccessDto;
    }

    @Override
    public OrganizerSecuritySettingDto getOrganizerSecuritySetting(Long orgId){
        return new OrganizerSecuritySettingDto(this.findByIdOrNotFound(orgId));
    }

    @Override
    public void updateOrganizerSecuritySetting(Long orgId, OrganizerSecuritySettingDto organizerSecuritySettingDto){
        Organizer organizer = this.findByIdOrNotFound(orgId);
        organizer.setTwoFactorRequired(organizerSecuritySettingDto.isTwoFactorRequired());
        this.save(organizer);

        this.gamificationCacheStoreService.set(TWO_FACTOR_ORGANIZER_KEY_PREFIX+orgId, organizerSecuritySettingDto.isTwoFactorRequired());

        roEventService.findEventIdsByOrganizerId(orgId)
                .forEach(eventId -> this.gamificationCacheStoreService.set(TWO_FACTOR_EVENT_KEY_PREFIX+eventId, organizerSecuritySettingDto.isTwoFactorRequired()));
    }

    @Override
    public EntitlementsAvailabilityDTO getEntitlementsBasedOnOrganizerUrl(String organizerUrl) {
        return virtualEventService.getEntitlementsBasedOnOrganizerUrl(organizerUrl);
    }

    @Override
    public TotalAndUsedRegistrantsDTO getTotalAndUsedRegistrants(String organizerUrl, String eventUrl, String whiteLabelUrl) {
        log.info("getTotalAndUsedRegistrants method OrganizerURL {} EventURL {}", organizerUrl, eventUrl);
        TotalAndUsedRegistrantsDTO totalAndUsedRegistrantsDTO = new TotalAndUsedRegistrantsDTO();
        if (null != eventUrl) {
            Event event = eventService.getEventByURLWithoutCache(eventUrl);
            List<ChargebeeEventUsages> chargeBeeEventUsages = chargebeeEventUsagesRepoService.findByEventId(event.getEventId());
            AtomicReference<Long> totalUsedEventCreditsByEventId = new AtomicReference<>(0L);
            chargeBeeEventUsages.forEach(dto -> {
                if (dto.getQuantity() != null) {
                    totalUsedEventCreditsByEventId.updateAndGet(v -> v + dto.getQuantity());
                }
            });
            if (null != event.getWhiteLabel()) {
                totalAndUsedRegistrantsDTO = getTotalAndUsedRegistrantsByWhiteLabel(event.getWhiteLabel(), event.getEventId());
            } else if (null != event.getOrganizer()) {
                totalAndUsedRegistrantsDTO = getTotalAndUsedRegistrantsByOrganiser(event.getOrganizer(), event.getEventId());
            }
            totalAndUsedRegistrantsDTO.setUsed(totalUsedEventCreditsByEventId.get());
        } else {
            if (null != whiteLabelUrl) {
                WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
                totalAndUsedRegistrantsDTO = getTotalAndUsedRegistrantsByWhiteLabel(whiteLabel,null);
            } else {
                Optional<Organizer> organizerOptional = organizerRepository.findOrganizerByOrganizerPageURL(organizerUrl);
                if (organizerOptional.isPresent()) {
                    totalAndUsedRegistrantsDTO = getTotalAndUsedRegistrantsByOrganiser(organizerOptional.get(), null);
                }
            }
        }
        return totalAndUsedRegistrantsDTO;
    }

    private TotalAndUsedRegistrantsDTO getTotalAndUsedRegistrantsByOrganiser(Organizer organizer, Long eventId) {
        TotalAndUsedRegistrantsDTO totalAndUsedRegistrantsDTO = new TotalAndUsedRegistrantsDTO();
        AtomicReference<Long> totalPurchasedRegistrants = new AtomicReference<>(0L);
        List<ChargesPurchased> chargesPurchasedList = chargesPurchasedRepoService.findByOrganizerIdAndChargeList(organizer.getId());
        chargesPurchasedList.forEach(dto -> {
            if (dto.getQuantity() != null) {
                totalPurchasedRegistrants.updateAndGet(v -> v + dto.getQuantity());
            }
        });
        log.info("totalPurchasedRegistrants {}", totalPurchasedRegistrants.get());
        totalAndUsedRegistrantsDTO.setTotal(totalPurchasedRegistrants.get() + (organizer.getFreeQuantity()!=null?organizer.getFreeQuantity():0));
        if (null == eventId) {
            Long totalUsedEventCreditsByOrganizerId = chargebeeEventUsagesRepoService.findTotalUsedEventCreditsByOrganizerId(organizer.getId());
            long remaining= totalAndUsedRegistrantsDTO.getTotal()-totalUsedEventCreditsByOrganizerId;
            totalAndUsedRegistrantsDTO.setRemaining(remaining<0?0:remaining);
        }
        return totalAndUsedRegistrantsDTO;
    }

    private TotalAndUsedRegistrantsDTO getTotalAndUsedRegistrantsByWhiteLabel(WhiteLabel whiteLabel, Long eventId) {
        TotalAndUsedRegistrantsDTO totalAndUsedRegistrantsDTO = new TotalAndUsedRegistrantsDTO();
        AtomicReference<Long> totalPurchasedRegistrants = new AtomicReference<>(0L);
        List<ChargesPurchased> chargesPurchasedList = chargesPurchasedRepoService.findByWhiteLabel(whiteLabel);
        chargesPurchasedList.forEach(dto -> {
            if (dto.getQuantity() != null) {
                totalPurchasedRegistrants.updateAndGet(v -> v + dto.getQuantity());
            }
        });
        totalAndUsedRegistrantsDTO.setTotal(totalPurchasedRegistrants.get() + (whiteLabel.getFreeQuantity() != null ? whiteLabel.getFreeQuantity() : 0));
        if (null == eventId) {
            Long totalEventUsage = chargebeeEventUsagesRepoService.findTotalUsedEventCreditsByWhiteLabelID(whiteLabel.getId());
            long remaining = totalAndUsedRegistrantsDTO.getTotal() - totalEventUsage;
            totalAndUsedRegistrantsDTO.setRemaining(remaining < 0 ? 0 : remaining);
        }
        return totalAndUsedRegistrantsDTO;
    }

    @Override
    public ChargeBeeCustomerPaymentInfoDTO getOrSaveOrganizerPaymentInformation(Organizer organiser, User user) {
        ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsService.findByOrganizerId(organiser.getId());
        threwExceptionInChargeBeeEventsCreditsNotFound(chargebeeEventCredits);
        if (StringUtils.isNotBlank(organiser.getChargebeeCustomerId())) {
            try {
                if (StringUtils.isBlank(chargebeeEventCredits.getChargeBeeCustomerEmail())) {
                    log.info("organiserPaymentInformation first time insert data in database organizerId {} byUserId {} date {}", organiser.getId(), user.getUserId(), new Date());
                    Customer customer = chargebeePaymentService.retrieveCustomer(organiser.getChargebeeCustomerId());
                    Card card = chargebeePaymentService.retrieveCustomerCard(organiser.getChargebeeCustomerId());
                    if (customer != null && card != null) {
                        chargebeeEventCredits.setCard(card.maskedNumber());
                        chargebeeEventCredits.setCardExpiryDate(card.expiryMonth() + STRING_SLASH + card.expiryYear());
                        chargebeeEventCredits.setBilledTo(customer.billingAddress().firstName() + STRING_BLANK + customer.billingAddress().lastName());
                        chargebeeEventCredits.setChargeBeeCustomerEmail(customer.email());
                        chargebeeEventCredits = chargebeeEventCreditsService.save(chargebeeEventCredits, user);
                        log.info("organiserPaymentInformation insert data in database organizerId {} byUserId {} date {}", organiser.getId(), user.getUserId(), new Date());
                    }
                }
            } catch (Exception e) {
                log.error("organiserPaymentInformation error come while retrieveCustomer organizerId {} error {}", organiser.getId(), e.getMessage());
            }
        }
        return new ChargeBeeCustomerPaymentInfoDTO(chargebeeEventCredits);
    }

    public void threwExceptionInChargeBeeEventsCreditsNotFound(ChargebeeEventCredits chargebeeEventCredits) {
        if (chargebeeEventCredits == null) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.PAYMENT_INFO_NOT_FOUND);
        }
    }

    private void updateEngageEmailLimit(Organizer organizer, OrganizerSettingDto organizerSettingDto) {
        if (organizerSettingDto.getEngageEmailsLimit() >= 0 && organizerSettingDto.getEngageEmailsLimit() != organizer.getEngageEmailsLimit()) {
            organizer.setEngageEmailsLimit(organizerSettingDto.getEngageEmailsLimit());
            contactModuleSettingsService.updateAllEventEngageEmailLimit(null, organizer);
            log.info("updateEngageEmailLimit organiserId {} engageEmailLimit {}", organizer.getId(), organizer.getEngageEmailsLimit());
        }
    }

    @Override
    public void saveAll(List<Organizer> listOrganisers) {
        organizerRepository.saveAll(listOrganisers);
    }

    @Override
    public Organizer findByIdAndWhiteLabelIdOrThrowError(Long organizerId, Long whiteLabelId) {
         return organizerRepository.findByIdAndWhiteLabelId(organizerId, whiteLabelId).
                orElseThrow(() -> new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND));
    }

    @Override
    @CacheEvict(value = "eventById", allEntries = true)
    public boolean updateSecureLastNameOnHubSideFlag(Organizer organizer, User user, boolean isSecureLastName){
        log.info("Update secureLastNameOnHubSideFlag organizerId {} byUserId {} ", organizer.getId(), user.getUserId());

        Organizer org = organizerRepository.findByOrganizerId(organizer.getId())
                .orElseThrow(() -> new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND));
        org.setSecureLastNameOnHubSide(isSecureLastName);
        organizerRepository.save(org);

        // get all the events and clear cache from v3 server
        eventRepository.findAllEventIdsByOrganiserId(organizer.getId()).forEach(e -> {
            clearAPIGatewayCache.clearAPIGwVESettingsCache(e);
        });

       return isSecureLastName;
    }

    @Override
    public boolean getSecureLastNameOnHubSide(Organizer organizer, User user){
        log.info("Get secureLastNameOnHubSideFlag organizerId {} byUserId {} ", organizer.getId(), user.getUserId());

        return organizerRepository.findSecureLastNameOnHubSideByOrganizerId(organizer.getId())
                .orElseThrow(() -> new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND));
    }

    @Override
    public boolean getSecureLastNameOnHubSideByEventUrl(Long eventId){

        Event event = roEventRepository.findEventByEventId(eventId);
        if(!ObjectUtils.isEmpty(event) && !ObjectUtils.isEmpty(event.getOrganizer()))
            return event.getOrganizer().isSecureLastNameOnHubSide();

        return false;
    }

}
