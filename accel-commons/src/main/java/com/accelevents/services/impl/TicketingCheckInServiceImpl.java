package com.accelevents.services.impl;

import com.accelevents.common.dto.CreditCardChargesDto;
import com.accelevents.common.dto.SearchAttendeeDto;
import com.accelevents.common.dto.TicketTypeOtherRestrictionDto;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.virtual.CheckInAuditLog;
import com.accelevents.dto.*;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.BaseException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.messages.*;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.TwilioTextMessagePrepareService;
import com.accelevents.repositories.AttendeeCheckInRecordsRepository;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.ro.entryexit.ROEntryExitSettingsRepository;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.eventTicket.ROEventTicketsService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.dynamodb.user.activity.UserActivity;
import com.accelevents.services.dynamodb.user.activity.UserActivityService;
import com.accelevents.services.elasticsearch.leaderboard.TrackingData;
import com.accelevents.services.kinesis.KinesisService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.services.slack.SlackService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.services.tray.io.tracking.TrayTrackingService;
import com.accelevents.session_speakers.repo.UserSessionRepo;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.ticketing.dto.StaffTicketingOrderDto;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.ticketing.dto.TicketingDatesDto;
import com.accelevents.ticketing.dto.TicketingFeeDto;
import com.accelevents.utils.TimeZone;
import com.accelevents.utils.*;
import eu.bitwalker.useragentutils.UserAgent;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.messages.CheckInSource.*;
import static com.accelevents.messages.CheckInUserTracking.PRE_EVENT_CHECKIN;
import static com.accelevents.messages.CheckInUserTracking.TEST_MODE;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.AREA;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.ChallengeConstants;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.*;
import static com.accelevents.utils.GeneralUtils.getEventPath;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static com.accelevents.utils.TimeZoneUtil.getEquivalentTimeZone;
import static org.springframework.util.CollectionUtils.isEmpty;

@Service
public class TicketingCheckInServiceImpl implements TicketingCheckInService {

    private static final Logger log = LoggerFactory.getLogger(TicketingCheckInServiceImpl.class);
    public static final String TABLE = "Table ";

    @Autowired
    private UserSessionService userSessionService;
    @Autowired
    private EventTicketsService eventTicketsService;
    @Autowired
    private ROEventTicketsService roEventTicketsService;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private TicketingRepository ticketingRepository;
    @Autowired
    private RecurringEventsScheduleBRService recurringEventsScheduleService;
    @Autowired
    private StripeService stripeService;
    @Autowired
    private WaitListService waitListService;
    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;
    @Autowired
    private TicketingTypeService ticketingTypeService;
    @Lazy
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Autowired
    private TwilioTextMessagePrepareService twilioTextMessagePrepareService;
    @Autowired
    private EventService eventService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private CheckInAuditLogService checkInAuditLogService;
    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private StripePaymentService stripePaymentService;
    @Autowired
    private StripeConfiguration stripeConfiguration;

    @Autowired
    private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private SeatsIoService seatsIoService;
    @Autowired
    private SalesTaxService salesTaxService;
    @Autowired
    private UserSessionRepo repo;
    @Autowired
    private SessionService sessionService;
    @Autowired
    private AttendeeCheckInRecordsRepository attendeeCheckInRecordsRepository;
    @Autowired
    private PaymentHandlerService paymentHandlerService;
    @Autowired
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    @Autowired
    private SpeakerService speakerService;
    @Autowired
    private VirtualEventPortalService virtualEventPortalService;
    @Autowired
    private KinesisService kinesisService;
    @Autowired
    private ChallengeConfigService challengeConfigService;
    @Autowired
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Autowired
    private VatTaxService vatTaxService;
    @Autowired
    private ROStaffRoleService roStaffRoleService;

    @Autowired
    private BadgesService badgesService;
    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private EventTicketsRepository eventTicketsRepository;
    @Autowired
    private TrayTrackingService trayTrackingService;
    @Autowired
    private SlackService slackService;
    @Autowired
    private ServiceHelper serviceHelper;
    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;
    @Autowired
    private UserActivityService userActivityService;

    @Autowired
    private ROEntryExitSettingsRepository roEntryExitSettingsRepository;

    @Value("${slack.check.in.error.webhook}")
    private String checkInErrorSlackWebhook;

    @Autowired
    private TicketHolderEditAttributesService ticketHolderEditAttributesService;

    private static final String NEW_LINE = "\n";
    private static final String CHANNEL_NAME = "#check-in-errors";
    private static final String USER_NAME = "Alert Bot";

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public TicketCheckInDto checkInTicket(SearchAttendeeDto searchAttendeeDto, String barcodeid, User user, Event event, String device, boolean isAppLogin,String source,String sourceDescription, boolean isRFIDCheckin) throws IOException {//NOSONAR
        if (user != null) {
            boolean createEntryLog = Boolean.FALSE;
            boolean isEntryExitEnabledForEvent = roEntryExitSettingsRepository.isEntryExitEnabledForEvent(event.getEventId());

            boolean isRecheckInEnabled = false;
            Optional<Boolean>  isRecheckInValue = isAllowRecheckInAtEventLevel(event);
            if(isRecheckInValue.isPresent()){
                isRecheckInEnabled = isRecheckInValue.get();
            }
            boolean checkInWithBarcode = false;
            if (barcodeid != null && !barcodeid.isEmpty()) {
                checkInWithBarcode = true;
            } else if (searchAttendeeDto == null) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.USE_BARCODE_OR_EVENT_TICKET_INFORMATION_TO_CHECKIN);
            }
            Map<String, String> checkInStatus = new HashMap<>();
            EventTickets eventTicket;
            CheckInLog c;
            if (checkInWithBarcode) {
                c = new CheckInLog(barcodeid);
                if(isRFIDCheckin){
                    log.info("checkInTicket: Checking in with RFID : {}", barcodeid);
                    eventTicket = eventTicketsRepoService.findByRfidTagAndEventId(barcodeid, event.getEventId());
                    log.info("checkInTicket: Event Ticket found with RFID : {}", eventTicket);
                    if(!isRecheckInEnabled && eventTicket !=null && TicketStatus.CHECKED_IN.equals(eventTicket.getTicketStatus())){
                        log.info("checkInTicket: Ticket already checked in with RFID : {}", barcodeid);
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_ALREADY_CHECKIN);
                    }
                }
                else{
                    eventTicket = eventCommonRepoService.findByBarcodeId(barcodeid);
                }
            } else {
                c = new CheckInLog(String.valueOf(searchAttendeeDto.getTicketNumber()));
                eventTicket = eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false);
            }
                if (eventTicket != null && !RecordStatus.BLOCK.equals(eventTicket.getRecordStatus())) {
                checkInStatus.put(USER_ID, (eventTicket.getUserId() != null) ? String.valueOf(eventTicket.getUserId()) : "0");
                checkInStatus.put(TICKET_ID, String.valueOf(eventTicket.getId()));
                checkInStatus.put(TICKET_ORDER_ID, String.valueOf(eventTicket.getTicketingOrder().getId()));
                checkInStatus.put(TICKET_BARCODE, barcodeid);
                checkInStatus.put(EVENT_ID, String.valueOf(event.getEventId()));
                checkInStatus.put(STATUS, SUCCESS);
                checkInStatus.put(STATUS_CODE, String.valueOf(STATUS_CODE_OK));
                checkInStatus.put(EVENT_URL, event.getEventURL());
                if (TicketTypeFormat.HYBRID.equals(eventTicket.getTicketingTypeId().getTicketTypeFormat())
                        || TicketTypeFormat.IN_PERSON.equals(eventTicket.getTicketingTypeId().getTicketTypeFormat()) || EventFormat.VIRTUAL.equals(event.getEventFormat())) {
                    c.log("EventTicketFound");
                    log.info("Check-in Event ticket type format {}", eventTicket.getTicketingTypeId().getTicketTypeFormat());

                    List<String> userAllRoles = roStaffService.getAllUserRoles(event.getEventId(), eventTicket.getHolderUserId().getUserId());
                    if(CollectionUtils.isEmpty(userAllRoles) || (userAllRoles.size()==1 && userAllRoles.contains(ATTENDEE.toLowerCase()))) {
                        throwExceptionIfEventTicketIsUnpaid(eventTicket, checkInStatus, eventTicket.getTicketingTypeId(), event.getEventId(), isAppLogin, isEntryExitEnabledForEvent);
                    }

                    TicketStatus ticketStatus = eventTicket.getTicketStatus();
                    if (TicketStatus.REGISTERED.equals(ticketStatus) || TicketTypeFormat.HYBRID.equals(eventTicket.getTicketingTypeId().getTicketTypeFormat()) ||  (TicketStatus.CHECKED_IN.equals(ticketStatus) && isRecheckInEnabled)) {
                        c.log("TicketStatusBooked");
                        log.info("Event ticket status {} and ticket type format {}", ticketStatus, eventTicket.getTicketingTypeId().getTicketTypeFormat());
                        checkInAllowedBeforeTime(eventTicket, event, checkInStatus);

                        eventTicket.setTicketStatus(TicketStatus.CHECKED_IN);
                        eventTicket.setCheckInStaff(user);
                        eventTicket.setCheckInDate(new Date());
                        if (TicketTypeFormat.HYBRID.equals(eventTicket.getTicketingTypeId().getTicketTypeFormat()) && VIRTUAL_EVENT_PORTAL.equals(eventTicket.getCheckInSource())) {
                            eventTicket.setCheckInSource(HYBRID_CHECKIN);
                        } else {
                            eventTicket.setCheckInSource(CheckInSource.IN_PERSON);
                        }

                        if((eventTicket.getEntryExitStatus() == null || EntryExitStatus.EXIT.equals(eventTicket.getEntryExitStatus())) && isEntryExitEnabledForEvent){
                            eventTicket.setEntryExitStatus(EntryExitStatus.ENTRY);
                            createEntryLog = Boolean.TRUE;
                        }

                        eventCommonRepoService.save(eventTicket);

                        c.log("EVENT_TICKET_SAVED");

                        setCheckInAuditLogObject(user, event, device, eventTicket, TicketStatus.CHECKED_IN.name(), eventTicket.getId(),source,sourceDescription);

                        //This will perform all the post process task in Async manner.

                        TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                        User purchaser = eventTicket.getTicketingOrder().getPurchaser();
                        c.log("purchaser_FOUND");
                        String purchaserName = purchaser.getFirstName() + Constants.STRING_BLANK + purchaser.getLastName();
                        String ticketingTypeName = eventTicket.getTicketingTypeId().getTicketTypeName();
                        String responseMsg = null;
                        if (ticketHolderAttributes != null
                                && StringUtils.isNotBlank(ticketHolderAttributes.getJsonValue()) && (event.isRaffleEnabled() || event.isCauseAuctionEnabled() || event.isSilentAuctionEnabled())) {
                            c.log("sendCheckInTicketEmailAndSMS");
                            responseMsg = this.sendCheckInTicketEmailAndSMS(eventTicket, c, event, user);
                        }


                        String ticketHolderName = "";
                        String ticketHolderFirstName = null;
                        String ticketHolderLastName = null;
                        if (ticketHolderAttributes != null && StringUtils.isNotBlank(ticketHolderAttributes.getJsonValue())) {

                            ticketHolderFirstName = eventTicket.getHolderFirstName();
                            ticketHolderLastName = eventTicket.getHolderLastName();

                            ticketHolderName = ticketHolderFirstName + Constants.STRING_BLANK + ticketHolderLastName;
                        }

                        TicketCheckInDto ticketCheckInDto = new TicketCheckInDto(eventTicket.getTicketStatus().getStatus(),
                                eventTicket.getTicketingTypeId().getTicketing().getId(),
                                (ticketHolderAttributes != null && StringUtils.isNotBlank(ticketHolderAttributes.getJsonValue())) ? ticketHolderName : purchaserName,
                                ticketingTypeName);
                        ticketCheckInDto.setGuestOff(eventTicket.getGuestOfBuyer());
                        ticketCheckInDto.setHolderFirstName(ticketHolderFirstName);
                        ticketCheckInDto.setHolderLastName(ticketHolderLastName);
                        ticketCheckInDto.setHolderEmail(eventTicket.getHolderEmail());
                        ticketCheckInDto.setPurchaserFirstName(purchaser.getFirstName());
                        ticketCheckInDto.setPurchaserLastName(purchaser.getLastName());
                        ticketCheckInDto.setPurchaserEmail(purchaser.getEmail());
                        List<BadgesResponseData> ticketingIdAndBadgesIdByEventId = badgesService.getTicketingIdAndBadgesIdByEventId(event.getEventId());
                        BadgesResponseData badgesResponseData = ticketingIdAndBadgesIdByEventId.stream()
                                .filter(e -> !org.apache.commons.collections.CollectionUtils.isEmpty(e.getTicketingTypeIds()) && e.getTicketingTypeIds().contains(eventTicket.getTicketingTypeId().getId())).findFirst().orElse(null);
                        if (null != badgesResponseData) {
                            ticketCheckInDto.setBadgeId(badgesResponseData.getBadgeId());
                        }
                        if (!TicketBundleType.INDIVIDUAL_TICKET.equals(eventTicket.getTicketingTypeId().getBundleType())) {
                            ticketCheckInDto.setBundleTypeTable(true);
                            ticketCheckInDto.setTable(TABLE + eventTicket.getTicketingTable().getTableNoSequence());
                        }
                        Ticketing ticketing = eventTicket.getTicketingTypeId().getTicketing();
                        if (StringUtils.isNotBlank(ticketing.getChartKey())) {
                            ticketCheckInDto.setSeatNumber(eventTicket.getSeatNumber());
                        }
                        if (TicketingOrder.TicketingOrderStatus.PAID.equals(eventTicket.getTicketingOrder().getStatus())) {
                            ticketCheckInDto.setPaid(true);
                        }
                        ticketCheckInDto.setResponseMessage(responseMsg);
                        ticketCheckInDto.setTicketType(eventTicket.getTicketingTypeId().getTicketTypeFormat().toString());
                        log.info("Send notification to attendees with ticket status Booked OR Ticket type format Hybrid");
                        if (barcodeid != null) sendNotificationToAttendee(checkInStatus);
                        if (eventTicket.getHolderUserId() != null && !Permissions.STAFF_OR_HIGHER_ROLE(roStaffRoleService.getUserRoleByEvent(eventTicket.getHolderUserId(), event))) {
                            sendCheckInDataToKinesis(event, eventTicket.getHolderUserId());
                        }
                        afterTaskIntegrationTriggerService.trayCheckInPostProcess(event, eventTicket.getHolderUserId(), eventTicket.getId());
                        //This will perform all the post process task in Async manner.
                        afterTaskIntegrationTriggerService.userCheckInPostProcess(eventTicket, event);
                        addUserActivity(isAppLogin, event, eventTicket.getHolderUserId().getUserId(), CHECK_IN_ACTIVITY, source, sourceDescription, user.getUserId());
                        if (createEntryLog) {
                            createEntryExitActivityLog(event, user, barcodeid, EntryExitStatus.ENTRY, true, null, isRFIDCheckin, source, sourceDescription, isAppLogin, eventTicket, user.getUserId());
                        }
                        return ticketCheckInDto;
                    } else if (TicketStatus.CANCELED.equals(ticketStatus)) {
                        if (checkInWithBarcode) {
                            c.log("BARCODE_ALREADY_REFUNDED");
                        } else {
                            c.log("EVENT_TICKET_ALREADY_REFUNDED");
                        }
                        checkInStatus.put(STATUS, STATUS_FAILED);
                        checkInStatus.put(STATUS_CODE, NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_REFUNDED_AND_CANCELED.getStatusCode());
                        log.info("Send notification to attendees with ticket status Refunded with barcode Id {}", barcodeid != null ? barcodeid : eventTicket.getId());
                        if (barcodeid != null) sendNotificationToAttendee(checkInStatus);
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_REFUNDED_AND_CANCELED);

                    } else {
                        if (checkInWithBarcode) {
                            c.log("BARCODE_ALREADY_CHECKED_IN");
                        } else {
                            c.log("EVENT_TICKET_CHECKED_IN");
                        }
                        checkInStatus.put(STATUS, STATUS_FAILED);
                        checkInStatus.put(STATUS_CODE, NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_CHECKED_IN.getStatusCode());
                        log.info("Send notification to attendees with ticket OR barcode already checked-in with barcode Id {}", barcodeid != null ? barcodeid : eventTicket.getId());
                        if (barcodeid != null) sendNotificationToAttendee(checkInStatus);
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_CHECKED_IN);
                    }
                } else {
                    checkInStatus.put(STATUS, STATUS_FAILED);
                    checkInStatus.put(STATUS_CODE, NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getStatusCode());
                    log.info("Attendee have virtual event ticket with barcode Id {}", barcodeid != null ? barcodeid : eventTicket.getId());
                    if (barcodeid != null) sendNotificationToAttendee(checkInStatus);
                    throw new NotAcceptableException(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET);
                }
            } else {
                if (eventTicket != null && RecordStatus.BLOCK.equals(eventTicket.getRecordStatus())) {
                    log.info("The user is blocked, Email : {}", eventTicket.getHolderEmail());
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.BLOCKED_USER_ADMIN_MSG);
                } else if(checkInWithBarcode){
                    log.info("EventTicket  Not Found : {}", barcodeid);
                    throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
                }else {
                    log.info("EventTicket  Not Found Ticket number : {}, Email : {}", searchAttendeeDto.getTicketNumber(), searchAttendeeDto.getEmail());
                    throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.NO_SUCH_EVENT_TICKET_EXIST);
                }
            }
        } else {
            throw new AuthorizationException(Constants.PLEASE_LOGIN);
        }
    }

    //Capture check-in/ Check-out event only when it is staff/admin-initiated. If the user is joining a virtual event, that is captured from FE as a “Joined event“ activity
    private void addUserActivity(boolean isAppLogin, Event event, Long userId, String actionType, String source, String sourceDescription, Long staffUserId) {
        String sourceName;
        if(StringUtils.isNotEmpty(source)){
            sourceName = source;
        }
        else{
            sourceName = (isAppLogin) ? "MobileApp" : "Desktop";
        }
        HashMap<String, Object> userActivity = new HashMap<>();
        if(sourceDescription != null ){
            try {
                String decodedParam = URLDecoder.decode(sourceDescription, StandardCharsets.UTF_8.name());
                Map<String, Object> sourceDescriptionMap = JsonMapper.stringtoObject(decodedParam, Map.class);
                if (sourceDescriptionMap != null) {
                    userActivity.putAll(sourceDescriptionMap);
                }
            } catch (UnsupportedEncodingException e) {
                log.info("Error decoding source description: {}", e.getMessage());
            }
        }

        userActivityService.createUserActivity(userActivity, event.getEventId(), userId, actionType, sourceName, staffUserId );
    }



    private Optional<Boolean> isAllowRecheckInAtEventLevel(Event event){
        return roEventLevelSettingService.isRecheckInEnabledByEventId(event);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public DataTableResponse getAllCheckInAttendees(User user, Event event, int page, int size) {
        log.info("Get all check-in attendees list for auto complete of event {}", event.getEventId());
        DataTableResponse dataTableResponse = new DataTableResponse();
        Page<EventTickets> eventTicketsList = eventTicketsService.getEventTicketsByEvent(event, PageRequest.of(page, size));
        List<AttendeeCheckInDto> attendeeCheckInDtos = new ArrayList<>();
        if (eventTicketsList.hasContent()) {
            List<BadgesResponseData> ticketingIdAndBadgesIdByEventId = badgesService.getTicketingIdAndBadgesIdByEventId(event.getEventId());
            Map<Long, Long> ticketTypeIdAndBadgeIdMap = new HashMap<>();
            for (BadgesResponseData responseData : ticketingIdAndBadgesIdByEventId) {
                List<Long> ticketingTypeIds = responseData.getTicketingTypeIds();
                if (!ticketingTypeIds.isEmpty()) {
                    Long badgeId = responseData.getBadgeId();
                    for (Long ticketTypeId : ticketingTypeIds) {
                        ticketTypeIdAndBadgeIdMap.put(ticketTypeId, badgeId);
                    }
                }
            }
            for (EventTickets eventTicket : eventTicketsList.getContent()) {
                TicketingType ticketingType = eventTicket.getTicketingTypeId();
                TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                String ticketingTypeName = ticketingType.getTicketTypeName();

                AttendeeCheckInDto attendeeCheckInDto = new AttendeeCheckInDto();
                String ticketHolderName;
                if(eventTicket.getGuestOfBuyer()
                        && (StringUtils.isEmpty(eventTicket.getHolderFirstName()) || StringUtils.isEmpty(eventTicket.getHolderLastName()))){
                    User purchaser = eventTicket.getTicketingOrder().getPurchaser();
                    attendeeCheckInDto.setGuestOfBuyer(eventTicket.getGuestOfBuyer());
                    ticketHolderName = GUEST_OF + STRING_BLANK + purchaser.getFirstName() + STRING_BLANK + purchaser.getLastName();
                    attendeeCheckInDto.setGuestOfBuyerName(ticketHolderName);
                } else if (ticketHolderAttributes != null && StringUtils.isNotBlank(ticketHolderAttributes.getJsonValue())) {
                    ticketHolderName = eventTicket.getHolderFirstName() + STRING_BLANK + eventTicket.getHolderLastName();
                }  else {
                    User purchaser = eventTicket.getTicketingOrder().getPurchaser();
                    ticketHolderName = purchaser.getFirstName() + STRING_BLANK + purchaser.getLastName();
                }
                attendeeCheckInDto.setTicketId(ticketingType.getTicketing().getId());
                attendeeCheckInDto.setTicketHolderName(ticketHolderName);
                attendeeCheckInDto.setTicketingType(ticketingTypeName);
                attendeeCheckInDto.setBadgeId(ticketTypeIdAndBadgeIdMap.getOrDefault(ticketingType.getId(), null));

                attendeeCheckInDto.setEmail(eventTicket.getHolderEmail());
                attendeeCheckInDto.setPhoto(eventTicket.getHolderUserId().getPhoto());
                attendeeCheckInDto.setCheckinStatus(eventTicket.getTicketStatus().getStatus());
                attendeeCheckInDto.setEntryExitStatus(eventTicket.getEntryExitStatus());
                attendeeCheckInDto.setTicketNumber(eventTicket.getId());
                attendeeCheckInDto.setTicketSeat(eventTicketsService.getSeatNumberDisplay(eventTicket));
                attendeeCheckInDto.setEventTicketStatus(eventTicket.getTicketPaymentStatus().name());
                attendeeCheckInDtos.add(attendeeCheckInDto);
            }
        }
        dataTableResponse.setRecordsTotal(eventTicketsList.getTotalElements());
        dataTableResponse.setRecordsFiltered(eventTicketsList.getNumberOfElements());
        dataTableResponse.setData(attendeeCheckInDtos);
        return dataTableResponse;
    }

    private void sendNotificationToAttendee(Map<String, String> checkInStatus) {
        userService.sendPushNotificationForCheckin(checkInStatus);
    }

    private void setCheckInAuditLogObject(User user, Event event, String device, EventTickets eventTicket, String ticketStatus, Long eventTicketId,String source,String sourceDescription) {
        Staff staff = roStaffService.findByEventIdAndUserIdAndRole(event.getEventId(), user.getUserId());
        CheckInUserTracking checkInUserTracking = getCheckinUserTrackingStatus(event, user);
        checkInAuditLogService.setCheckInAuditLogObjectAndSaveInDB(event, staff, eventTicket.getTicketingTypeId(), DateUtils.getCurrentDate(), ticketStatus, device, CheckInSource.IN_PERSON, eventTicketId, null, checkInUserTracking, false,source,sourceDescription);
    }

    private boolean isUnPaidTicketNotAllowedToCheckIn(EventTickets eventTicket, Long eventId) {
        // We are checking if the event is not allowing to check-in with unpaid ticket, if ticket is unpaid and ticket price is not 0.0 and paid amount is 0.0 then it fully unpaid ticket else it is partially paid ticket and need to allowed partially paid ticket to check-in.
        return !ticketingRepository.isAllowCheckInWithUnpaidTicketEnabledByEventId(eventId) && TicketPaymentStatus.UNPAID.equals(eventTicket.getTicketPaymentStatus()) && (eventTicket.getTicketPrice() != 0d && eventTicket.getPaidAmount() == 0d);
    }

    private void throwExceptionIfEventTicketIsUnpaid(EventTickets eventTicket, Map<String, String> checkInStatus, TicketingType ticketingType, Long eventId, boolean isAppLogin, boolean isEntryExitEnabledForEvent) {
        if (this.isUnPaidTicketNotAllowedToCheckIn(eventTicket, eventId) && !TicketType.FREE.equals(ticketingType.getTicketType())) {
            if (checkInStatus != null && !checkInStatus.isEmpty()) {
                checkInStatus.put(STATUS, STATUS_FAILED);
                checkInStatus.put(STATUS_CODE, NotAcceptableException.TicketingExceptionMsg.TICKET_IS_UNPAID.getStatusCode());
                sendNotificationToAttendee(checkInStatus);
            }
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_IS_UNPAID);
        }
    }

    @Override
    public void throwExceptionIfEventTicketIsUnpaid(EventTickets eventTicket, Long eventId, boolean isAdminStaffOrSuperAdmin) {
        if (!isAdminStaffOrSuperAdmin && isUnPaidTicketNotAllowedToCheckIn(eventTicket, eventId)) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_IS_UNPAID);
        }
    }

    @Override
    public void checkIsUserTicketIsPaid(Event event, User user, boolean isAdminStaffOrSuperAdmin) {
        List<EventTickets> eventTickets = eventCommonRepoService.findEventTicketsOnlyByEventIdAndHolderUserId(event.getEventId(), user.getUserId());
        if (!CollectionUtils.isEmpty(eventTickets)) {
            throwExceptionIfEventTicketIsUnpaid(eventTickets.get(0), event.getEventId(), isAdminStaffOrSuperAdmin);
        }
    }

    protected String sendCheckInTicketEmailAndSMS(EventTickets eventTickets, CheckInLog c, Event event, User loggedInUser)
            throws IOException {
        log.info("sendCheckInTicketEmailAndSMS");
        TicketingOrder ticketingOrder = eventTickets.getTicketingOrder();
        String ticketHolderFirstName = eventTickets.getGuestOfBuyer()? eventTickets.getTicketPurchaserId().getFirstName() : eventTickets.getHolderFirstName();
        String ticketHolderEmail = eventTickets.getGuestOfBuyer()? eventTickets.getTicketPurchaserId().getEmail() : eventTickets.getHolderEmail();

        User ticketHolderUser = null;

        if (StringUtils.isNotBlank(ticketHolderEmail)) {
            c.log("ticketHolderEmailFound");
            ticketHolderUser = roUserService.findByEmail(ticketHolderEmail);
            c.log("ticketHolderUserFoundAndEmailTriggered");
            sendGridMailPrepareService.sendAttendeeCheckInEmail(ticketHolderFirstName, ticketHolderEmail,
                    ticketingOrder.getEventid(), ticketHolderUser, loggedInUser);
        }

        Long ticketHolderPhoneNumber = eventTickets.getHolderPhoneNumber();
        String ticketHolderCountryCode = eventTickets.getHolderCountryCode();

        if (NumberUtils.isNumberGreaterThanZero(ticketHolderPhoneNumber) && ticketHolderCountryCode != null && ticketHolderCountryCode.length() > 0) {
            log.info("sendCheckInTicketEmailAndSMS eventId {} ticketHolderPhoneNumber {} ticketHolderCountryCode {}", ticketingOrder.getEventid(), ticketHolderPhoneNumber, ticketHolderCountryCode);
            c.log("ticketHolderPhoneNumberFound");
            CountryCode cc = CountryCode.valueOf(StringUtils.upperCase(ticketHolderCountryCode));
            User phoneUser = null;
            if (ticketHolderUser == null) {
                phoneUser = roUserService.findByPhone(ticketHolderPhoneNumber, cc);
                ticketHolderUser = phoneUser;
                c.log("ticketHolderUserFoundByPhoneNumber");
            }

            if (phoneUser == null) {
                phoneUser = new User();
                phoneUser.setFirstName(ticketHolderFirstName);
                phoneUser.setPhoneNumber(ticketHolderPhoneNumber);
                phoneUser.setCountryCode(cc);
            }

            c.log("sendAttendeeCheckInTextMessageStart");
            log.info("sendCheckInTicketEmailAndSMS eventId {} userPhoneNumber {} userCountryCode {}", ticketingOrder.getEventid(), phoneUser.getPhoneNumber(), phoneUser.getCountryCode());
            twilioTextMessagePrepareService.sendAttendeeCheckInTextMessage(ticketingOrder.getEventid(),
                    phoneUser, loggedInUser);
            c.log("sendAttendeeCheckInTextMessageEnd");
        } else {
            c.log("country code or phone number is empty");
        }
        return null;
    }

    private boolean handleBidderCreditCardNotFound(Event event, User ticketHolderUser) {
        try {
            if (ticketHolderUser != null && ticketHolderUser.getUserId() != null) {
                List<StripeCreditCardDto> stripeCreditCardDto = eventService.getLinkedCreditCard(event,
                        ticketHolderUser);
                if (stripeCreditCardDto.isEmpty()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("Error while checkin - handleBidderCreditCardNotFound");
        }
        return false;
    }

    @Override
    @Transactional
    public TicketCheckInDto changeTicketStatusToBookFromCheckIn(String barcodeid, User user, Event event, String device, boolean isAppLogin, String source, String sourceDescription, boolean isRFID) {
        if (user != null) {
            boolean createExitLog = false;
            EventTickets eventTickets;
            if(isRFID){
                eventTickets = eventTicketsRepoService.findByRfidTagAndEventId(barcodeid, event.getEventId());
            }
            else{
                eventTickets = eventCommonRepoService.findByBarcodeId(barcodeid);
            }
            if (eventTickets != null) {
                Ticketing ticketing = ticketingRepository.findByEventid(event);

                TicketStatus ticcketStatus = eventTickets.getTicketStatus();
                if (TicketStatus.CHECKED_IN.equals(ticcketStatus)) {
                    eventTickets.setTicketStatus(TicketStatus.REGISTERED);
                    eventTickets.setCheckInStaff(user);
                    eventTickets.setCheckInDate(null);

                    if(eventTickets.getEntryExitStatus() != null
                            && roEntryExitSettingsRepository.isEntryExitEnabledForEvent(event.getEventId())){
                        createExitLog = Boolean.TRUE;
                        eventTickets.setEntryExitStatus(EntryExitStatus.EXIT);
                    }

                    eventCommonRepoService.save(eventTickets);
                    User purchaser = eventTickets.getTicketingOrder().getPurchaser();
                    String purchaserName = purchaser.getFirstName() + Constants.STRING_BLANK + purchaser.getLastName();
                    String ticketingTypeName = eventTickets.getTicketingTypeId().getTicketTypeName();
                    TicketCheckInDto ticketCheckInDto = new TicketCheckInDto(eventTickets.getTicketStatus().getStatus(),
                            eventTickets.getTicketingTypeId().getTicketing().getId(), purchaserName, ticketingTypeName);

                    if (!TicketBundleType.INDIVIDUAL_TICKET.equals(eventTickets.getTicketingTypeId().getBundleType())) {
                        ticketCheckInDto.setBundleTypeTable(true);
                        ticketCheckInDto.setTable(TABLE + eventTickets.getTicketingTable().getTableNoSequence());
                    }

                    if (TicketingOrder.TicketingOrderStatus.PAID.equals(eventTickets.getTicketingOrder().getStatus())) {
                        ticketCheckInDto.setPaid(true);
                    }
                    if (StringUtils.isNotBlank(ticketing.getChartKey())) {
                        ticketCheckInDto.setSeatNumber(eventTickets.getSeatNumber());
                    }
                    setCheckInAuditLogObject(user, event, device, eventTickets, TicketStatus.REGISTERED.name(), eventTickets.getId(),null,null);

                    Set<User> holderUsers = Stream.of(eventTickets).map(EventTickets::getHolderUserId).collect(Collectors.toSet());

                    if (!holderUsers.isEmpty()) {
                        List<User> userList = eventTicketsRepository.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(event, holderUsers,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
                        holderUsers.removeAll(userList);
                    }
                    ticketCheckInDto.setTicketType(eventTickets.getTicketingTypeId().getTicketTypeFormat().toString());
                    //This will perform all the post process task in Async manner.
                    afterTaskIntegrationTriggerService.userCheckOutPostProcess(eventTickets, event);

                    if(StringUtils.isNotEmpty(source) && StringUtils.isNotEmpty(sourceDescription)){
                        log.info("Get event ticket holder user userId : {}",eventTickets.getHolderUserId().getUserId());
                        if(!Objects.isNull(eventTickets.getHolderUserId())){
                            addUserActivity(isAppLogin, event, eventTickets.getHolderUserId().getUserId(), CHECK_OUT_ACTIVITY, source, sourceDescription, user.getUserId());
                        }
                    }
                    if (createExitLog) {
                        createEntryExitActivityLog(event, user, barcodeid, EntryExitStatus.EXIT, true, null, isRFID, source, sourceDescription, isAppLogin, eventTickets,user.getUserId());
                    }
                    return ticketCheckInDto;
                } else {
                    if (TicketStatus.CANCELED.equals(ticcketStatus)) {
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_REFUNDED_AND_CANCELED);
                    } else {
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_BOOK_STATUS);
                    }
                }
            } else {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
            }
        } else {
            throw new AuthorizationException("Please Login");
        }
    }

    @Override
    @Transactional
    public TicketingOrder.OrderType getOrderType(StaffTicketingOrderDto staffTicketingOrderDto) {
        TicketingOrder.OrderType orderType;
        if (Constants.CC.equals(staffTicketingOrderDto.getPaymentType())) {
            orderType = TicketingOrder.OrderType.CARD;
        } else if (Constants.CASH.equals(staffTicketingOrderDto.getPaymentType())) {
            orderType = TicketingOrder.OrderType.CASH;
        } else if (Constants.COMPLIMENTARY.equals(staffTicketingOrderDto.getPaymentType())) {
            orderType = TicketingOrder.OrderType.COMPLIMENTARY;
        } else if (Constants.UNPAID.equals(staffTicketingOrderDto.getPaymentType())) {
            orderType = TicketingOrder.OrderType.UNPAID;
        } else if (TicketingOrder.OrderType.APP.toString().equals(staffTicketingOrderDto.getPaymentType())) {
            orderType = TicketingOrder.OrderType.APP;
        } else {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PAYMENT);
        }
        return orderType;
    }

    public String getDeviceInfo(HttpServletRequest httpRequest) {
        UserAgent userAgent = UserAgent.parseUserAgentString(httpRequest.getHeader("User-Agent"));
        String deviceType = userAgent.getOperatingSystem().getDeviceType().getName();
        String browser = userAgent.getBrowser().getName();
        return browser + " from " + deviceType;
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public TicketDisplayPageDto getTicketTYpesDetailsForWaitListCheckout(Event event, String waitListIds, Long recurringEventId) {

        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId());
        if (StringUtils.isBlank(waitListIds)) {
            throw new NotAcceptableException(NotAcceptableException.WaitListExceptionMsg.INVALID_WAIT_LIST_IDS);
        }

        StripeDTO stripe = stripeService.getStripeFeesByEvent(event);

        TicketDisplayPageDto displayPageDto = new TicketDisplayPageDto();

        setTicketingDetails(event, displayPageDto);

        List<Long> waitListLongIds = Arrays.stream(waitListIds.split(STRING_COMMA)).filter(s -> StringUtils.isNoneBlank(s) && !"null".equalsIgnoreCase(s)).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        String eventKey = seatsIoService.getEventKey(event.getEventId(), recurringEventId);
        List<WaitList> waitList = waitListService.findByIdInAndEventAndStatus(waitListLongIds, event, WaitListStatus.WAITING);
        List<Long> ticketTypeIds = waitList.stream().map(WaitList::getTicketingTypeId).collect(Collectors.toList());
        List<TicketingType> ticketingTypes = this.ticketingTypeService.findByidInAndEventForWaitList(ticketTypeIds, event);
        for (TicketingType ticketingType : ticketingTypes) {
            long remainingTicket = ticketingStatisticsService.getRemainingTicketCount(ticketingType);
            long totalTickets = remainingTicket > waitList.size() ? waitList.size() : remainingTicket;

            TicketTypeDto ticketType = new TicketTypeDto();
            ticketType.setTypeId(ticketingType.getId());
            ticketType.setName(ticketingType.getTicketTypeName());
            ticketType.setMinTickerPerBuyer(0);
            ticketType.setMaxTickerPerBuyer(totalTickets);
            addTicketingTypeDetails(ticketingType, ticketType, totalTickets, stripe, salesTaxFeeDto, event);

            // If no ticket type configured then only return the remaining tickets.
            if (isEmpty(ticketTypeIds)) {
                if (remainingTicket > 0) {
                    displayPageDto.add(ticketType);
                }
            } else {
                displayPageDto.add(ticketType);
            }
        }
        displayPageDto.setTicketingFee(getTicketFeesLogic(event));
        displayPageDto.setEventKey(eventKey);
        return displayPageDto;
    }

    protected List<TicketingFeeDto> getTicketFeesLogic(Event event) {
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = transactionFeeConditionalLogicService.getRecordByEvent(event);
        if (transactionFeeConditionalLogics.isEmpty()) {
            TicketingFeeDto ticketingFeeDto = new TicketingFeeDto();
            ticketingFeeDto.setAeFeeFlat(AE_FLAT_FEE_ONE);
            ticketingFeeDto.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
            CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);
            ticketingFeeDto.setCreditCardProcessingFlat(creditCardChargesDto.getCreditCardFlat());
            ticketingFeeDto.setCreditCardProcessingPercentage(creditCardChargesDto.getCreditCardPercentage());

            if (event.getWhiteLabel() != null) {
                ticketingFeeDto.setWlFeeFlat(WL_FEE_FLAT);
                ticketingFeeDto.setWlFeePercentage(WL_FEE_PERCENTAGE);
            } else {
                ticketingFeeDto.setWlFeeFlat(0);
                ticketingFeeDto.setWlFeePercentage(0);
            }

            return (Collections.singletonList(ticketingFeeDto));
        } else {
            List<TicketingFeeDto> ticketingFeeDtos = new ArrayList<>();
            CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);

            transactionFeeConditionalLogics.forEach(e ->
                    ticketingFeeDtos.add(new TicketingFeeDto(e, creditCardChargesDto))
            );

            return ticketingFeeDtos;
        }
    }

    protected void setTicketingDetails(Event event, TicketDisplayPageDto displayPageDto) {
        Ticketing ticketing = this.ticketingRepository.findByEventid(event);
        displayPageDto.setStartDate(ticketing.getEventStartDate());
        displayPageDto.setEndDate(ticketing.getEventEndDate());
        displayPageDto.setAddress(ticketing.getEventAddress());
        displayPageDto.setShowRemainingTickets(ticketing.isShowRemainingTickets());
        displayPageDto.setSeatingChartKey(ticketing.getChartKey());
        displayPageDto.setHolderAttributeRequired(ticketing.getCollectTicketHolderAttributes());
        displayPageDto.setShowMemberCountInCheckout(ticketing.isShowMemberCountInCheckout());
        displayPageDto.setUniqueTicketHolderEmail(ticketing.getUniqueTicketHolderEmail());
        displayPageDto.setUniqueTicketBuyerEmail(ticketing.isUniqueTicketBuyerEmail());
    }

    protected void addTicketingTypeDetails(TicketingType ticketingType, TicketTypeDto ticketType, long remainingTicket, StripeDTO stripe, SalesTaxFeeDto salesTaxFeeDto, Event event) {
        ticketType.setRemaniningTickets(remainingTicket);
        ticketType.setBundleType(ticketingType.getBundleType());
        ticketType.setPosition(ticketingType.getPosition());
        ticketType.setTicketTypeDescription(ticketingType.getTicketTypeDescription());
        ticketType.setEnableTicketDescription(ticketingType.getEnableTicketDescription());
        ticketType.setTicketType(ticketingType.getTicketType());
        ticketType.setPassFeesToBuyer(ticketingType.isPassfeetobuyer());
        if (!TicketBundleType.INDIVIDUAL_TICKET.equals(ticketingType.getBundleType())) {
            ticketType.setTicketsPerTable(ticketingType.getNumberOfTicketPerTable());
        }
        // TODO : Vikas : Need to check
        double capAmount = transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(ticketingType.getTicketTypeFormat(), event);
        double vatTaxRate = vatTaxService.getVatTaxByTicketTypeOrEvent(event.getEventId(),ticketingType);
        FeePerTicket feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, ticketingType.getPrice(), 1, 1, false, capAmount, vatTaxRate, FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();
        ticketType.setFee(GeneralUtils.getRoundValue(feePerTicket.getTotalPayable() - ticketingType.getPrice()));
        ticketType.setVatTax(feePerTicket.getVatTax());
        ticketType.setSalesTax(feePerTicket.getSalesTaxFee());
        ticketType.setEndDate(ticketingType.getEndDate());
        ticketType.setStartDate(ticketingType.getStartDate());
        ticketType.setPrice(ticketingType.getPrice());
        ticketType.setDataType(ticketingType.getDataType());
        ticketType.setPayLater(ticketingType.isPayLater());
        ticketType.setTicketTypeFormat(ticketingType.getTicketTypeFormat());
        ticketType.setDepositType(ticketingType.getDepositType());
        ticketType.setDepositAmount(ticketingType.getDepositAmount());
        ticketType.setRequireDepositAmount(ticketingType.isRequireDepositAmount());
        log.info("paylater option is {} for event {}", (!ticketingType.isPayLater()) ? "Disable" : "Enable", event.getEventId());
    }


    @Override
    public TicketCheckInDto getTicketStatus(String barcodeid) {
        EventTickets eventTickets = eventCommonRepoService.findByBarcodeId(barcodeid);
        if (eventTickets != null) {
            TicketCheckInDto ticketingStatus = new TicketCheckInDto();
            ticketingStatus.setStatus(eventTickets.getTicketStatus().getStatus());
            ticketingStatus.setTicketId(eventTickets.getTicketingTypeId().getTicketing().getId());
            return ticketingStatus;
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
        }
    }

    public static class CheckInLog {

        private final String barCodeOrUserId;

        public CheckInLog(String barCodeOrUserId) {
            this.barCodeOrUserId = barCodeOrUserId;
        }

        public void log(String info) {
            log.info("{} : {}", barCodeOrUserId, info);
        }
    }

    @Override
    public TicketHolderDetailDto getHolderDetail(String barcodeid, long eventId) {
        EventTickets eventTickets = eventTicketsRepoService.findByBarcodeIdAndEventId(barcodeid, eventId);
        if (eventTickets != null) {
            TicketHolderDetailDto holderDetail = new TicketHolderDetailDto();
            holderDetail.setHolderEmail(eventTickets.getHolderEmail());
            holderDetail.setHolderFirstName(eventTickets.getHolderFirstName());
            holderDetail.setHolderLastName(eventTickets.getHolderLastName());
            holderDetail.setHolderPhoneNumber(eventTickets.getHolderPhoneNumber());
            holderDetail.setHolderCountryCode(eventTickets.getHolderCountryCode());
            holderDetail.setEventTicketId(eventTickets.getId());
            return holderDetail;
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
        }
    }

    @Transactional
    public void changeOrderTicketsStausCheckIn(Long orderid, Event event, User checkInStaff, String device, boolean isAdminStaffOrSuperAdmin) {
        List<EventTickets> eventTickets = eventTicketsRepoService.findAllByTicketingOrderIdAndTicketStatusBooked(orderid, TicketStatus.REGISTERED);
        if (eventTickets.isEmpty()) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.EVENT_TICKETS_NOT_FOUND);
        }
        if (TicketingOrder.OrderType.PAY_LATER.equals(eventTickets.get(0).getTicketingOrder().getOrderType())) {
            throwExceptionIfEventTicketIsUnpaid(eventTickets.get(0), event.getEventId(), isAdminStaffOrSuperAdmin);
        }
        checkInAllowedBeforeTime(eventTickets.get(0), event, null);

        List<Long> eventTicketIds = eventTickets.stream().map(e -> e.getId()).collect(Collectors.toList());
        eventTicketsRepoService.updateTicketStatusCheckInStaffAndCheckInDateByEventTickets(eventTicketIds, TicketStatus.CHECKED_IN, checkInStaff, new Date());

        setAllCheckInAuditLogObject(checkInStaff, event, device, TicketStatus.CHECKED_IN.name(), eventTickets);
    }

    private void setAllCheckInAuditLogObject(User user, Event event, String device, String ticketStatus, List<EventTickets> eventTickets) {
        Staff staff = roStaffService.findByEventIdAndUserIdAndRole(event.getEventId(), user.getUserId());
        List<CheckInAuditLog> checkInAuditLogList = new ArrayList<>();
        eventTickets.forEach(eventTickets1 -> {
            CheckInUserTracking checkInUserTracking = getCheckinUserTrackingStatus(event, user);
            checkInAuditLogList.add(new CheckInAuditLog(event, staff.getId(), eventTickets1.getTicketingTypeId(), DateUtils.getCurrentDate(), ticketStatus, device, CheckInSource.IN_PERSON, eventTickets1.getId(), null, "", checkInUserTracking, false, null, null));

        });
        checkInAuditLogService.saveAll(checkInAuditLogList);
    }

    private void checkInAllowedBeforeTime(EventTickets eventTicket, Event event, Map<String, String> checkInStatus) {
        Optional<RecurringEvents> recurringEvents = recurringEventsScheduleService.getRecurringEventById(eventTicket.getRecurringEventId() != null ? eventTicket.getRecurringEventId() : 0L);
        if (recurringEvents.isPresent() && recurringEvents.get().getCheckInXMinutesBefore() != null) {
            Date now = getDateInLocal(new Date(), event.getEquivalentTimeZone());
            if (now.getTime() <
                    (recurringEvents.get().getRecurringEventStartDate().getTime()
                            - (recurringEvents.get().getCheckInXMinutesBefore() * 60 * 1000))) {
                if (checkInStatus != null && !checkInStatus.isEmpty()) {
                    checkInStatus.put(STATUS, STATUS_FAILED);
                    checkInStatus.put(STATUS_CODE, NotAcceptableException.RecurringExceptionMsg.CHECKIN_NOT_ALLOWED_BEFORE_TIME.getStatusCode());
                    sendNotificationToAttendee(checkInStatus);
                }
                throw new NotAcceptableException(NotAcceptableException.RecurringExceptionMsg.CHECKIN_NOT_ALLOWED_BEFORE_TIME);
            }
        }
    }


    @Override
    @Transactional
    public boolean checkInVirtualEventTicket(User user, Event event, String device, boolean isAdminStaffOrSuperAdmin, boolean isAppLogin,String source,String sourceDescription) {
        log.info("checkIn VirtualEventTicket for userId {} and eventId {}", user.getUserId(), event.getEventId());
        boolean checkInFirstTime = false;
        boolean checkInFirstTimeWithTicket = false;
        if (!isAdminStaffOrSuperAdmin && EventListingStatus.UNPUBLISHED.equals(event.getEventListingStatus())) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CANNOT_ALLOWED_CHECK_IN_FOR_UNPUBLISH_EVENT);
        }
        List<CheckInAuditLog> checkInLogObjectList = checkInAuditLogService.findPreviousAddedRecord(event, user.getUserId());
        List<EventTickets> eventTickets = eventCommonRepoService.findTicketRegisterForCheckIn(user, event);

        boolean isHaveAccessGreaterThanAttendee = speakerService.isSpeakerInEvent(event, user)
                || roStaffService.isUserHaveAdminOrStaffOrExhibitorAdminOrLeadRetrieverRole(event, user);
        boolean isSuperAdminAndRolesGreaterThanAttendee = isAdminStaffOrSuperAdmin || isHaveAccessGreaterThanAttendee;

        if (!checkInLogObjectList.isEmpty()) {
            if(eventTickets != null && !eventTickets.isEmpty()) {
                throwExceptionIfEventTicketIsUnpaid(eventTickets.get(0), event.getEventId(), isSuperAdminAndRolesGreaterThanAttendee);
            }
            checkInFirstTimeWithTicket = addRecordToCheckInLog(user, event, eventTickets, checkInLogObjectList.get(0), isAppLogin, isSuperAdminAndRolesGreaterThanAttendee,source,sourceDescription);
        } else {
            checkInFirstTime = handleCreateCheckInForTicketHolder(user, event, eventTickets, device, isAppLogin, isSuperAdminAndRolesGreaterThanAttendee,source,sourceDescription);
            if (!checkInFirstTime && isHaveAccessGreaterThanAttendee) {
                createFirstCheckInRecord(event, null, user, device, isAppLogin,source,sourceDescription);
                checkInFirstTime = true;
            }
        }
        // Send check-in to event data to Kinesis
        if (!isAdminStaffOrSuperAdmin && (checkInFirstTime || checkInFirstTimeWithTicket)) {
            log.info("Sending check-in to event data to Kinesis eventId {}, userId {}, isAdminStaffOrSuperAdmin {}, checkInFirstTime {}, checkInFirstTimeWithTicket {}",
                    event.getEventId(), user.getUserId(), false, checkInFirstTime, checkInFirstTimeWithTicket);
            sendCheckInDataToKinesis(event, user);
        }
        afterTaskIntegrationTriggerService.virtualEventCheckInPostProcess(event, user, isAdminStaffOrSuperAdmin);
        trayTrackingService.trackPostEventHubAccess(event, user);
        return checkInFirstTime;
    }

    private boolean handleCreateCheckInForTicketHolder(User user, Event event, List<EventTickets> eventTickets, String device, boolean isAppLogin, boolean isAdminStaffOrSuperAdmin,String source,String sourceDescription) {
        log.info("handleCreateCheckInForTicketHolder for userId {} and eventId {} ", user.getUserId(), event.getEventId());
        if (eventTickets != null && !eventTickets.isEmpty()) {
            throwExceptionIfEventTicketIsUnpaid(eventTickets.get(0), event.getEventId(), isAdminStaffOrSuperAdmin);

            checkInEventTicketsRecord(user, eventTickets);
            afterTaskIntegrationTriggerService.userCheckInPostProcess(eventTickets.get(0), event);
            createAttendeeCheckInRecords(eventTickets.get(0), user.getUserId(), event.getEventId());
            createFirstCheckInRecord(event, eventTickets.get(0), user, device, isAppLogin,source,sourceDescription);
            return true;
        } else {
            return false;
        }
    }

    private void createFirstCheckInRecord(Event event, EventTickets eventTickets, User user, String device, boolean isAppLogin,String source,String sourceDescription) {
        log.info("createFirstCheckInRecord for user {} and event {} and eventTicket {} ", user.getUserId(), event.getEventId(), eventTickets == null ? "{}" : eventTickets.getId());
        TicketingType ticketingType = null;
        Long eventTicketId = null;
        if (eventTickets != null) {
            ticketingType = eventTickets.getTicketingTypeId();
            eventTicketId = eventTickets.getId();
        }
        CheckInUserTracking checkInUserTracking = getCheckinUserTrackingStatus(event, user);
        checkInAuditLogService.setCheckInAuditLogObjectAndSaveInDB(event,
                null, ticketingType,
                DateUtils.getCurrentDate(),
                TicketStatus.CHECKED_IN.name(),
                device,
                VIRTUAL_EVENT_PORTAL, eventTicketId, user.getUserId(), checkInUserTracking, isAppLogin,source,sourceDescription);
        afterTaskIntegrationTriggerService.trayCheckInPostProcess(event, user, eventTicketId);
    }

    private boolean addRecordToCheckInLog(User user, Event event, List<EventTickets> eventTickets, CheckInAuditLog checkInAuditLog, boolean isAppLogin, boolean isAdminStaffOrSuperAdmin,String source,String sourceDescription) {
        log.info("addRecordToCheckInLog for userId {} and eventId {} and checkinlog {}", user.getUserId(), event.getEventId(), checkInAuditLog.getId());
        TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());
        String eventTimeZone = timeZone.getEquivalentTimezone();
        Date recordDate = DateUtils.getOnlyDateFromDate(DateUtils.getDate(checkInAuditLog.getAuditTime(), eventTimeZone));
        Date todayDate = DateUtils.getOnlyDateFromDate(DateUtils.getDate(DateUtils.getCurrentDate(), eventTimeZone));

        CheckInAuditLog checkInAuditLogCopy = (CheckInAuditLog) checkInAuditLog.clone();
        boolean checkInFirstTimeWithTicket = false;
        if (checkInAuditLogCopy.getEventTicketId() == null) {
            checkInFirstTimeWithTicket = handleCreateCheckInForTicketHolder(user, event, eventTickets, checkInAuditLog.getDevice(), isAppLogin, isAdminStaffOrSuperAdmin,source,sourceDescription);
        } else if (!recordDate.equals(todayDate)) {
            CheckInUserTracking checkInUserTracking = getCheckinUserTrackingStatus(event, user);
            checkInAuditLogCopy.setId(0);
            checkInAuditLogCopy.setAuditTime(new Date());
            checkInAuditLogCopy.setAppLogin(isAppLogin);
            checkInAuditLogCopy.setCheckInUserTracking(checkInUserTracking);
            checkInAuditLogCopy.setSource(source);
            checkInAuditLogCopy.setSourceDescription(sourceDescription);
            checkInAuditLogService.save(checkInAuditLogCopy);
        }

        // When user check-in first time with ticket after that check-in status changed to Booked and again user check-in with same ticket then we need to update status to checked in for all Booked status tickets
        if(!checkInFirstTimeWithTicket && checkInAuditLogCopy.getEventTicketId() != null && !CollectionUtils.isEmpty(eventTickets)) {
            EventTickets eventTicketId = eventTickets.stream().filter(eventTicket -> eventTicket.getId()==checkInAuditLogCopy.getEventTicketId()).findFirst().orElse(null);
            List<EventTickets> bookedEventTickets = eventTickets.stream().filter(eventTicket -> eventTicket.getTicketStatus().equals(TicketStatus.REGISTERED)).collect(Collectors.toList());
            List<EventTickets> updatedEventTickets = new ArrayList<>();
            bookedEventTickets.forEach(eventTicket -> {
                if(eventTicket.getTicketStatus().equals(TicketStatus.REGISTERED)) {
                    eventTicket.setTicketStatus(TicketStatus.CHECKED_IN);
                    eventTicket.setCheckInStaff(user);
                    eventTicket.setCheckInDate(new Date());
                    if (TicketTypeFormat.HYBRID.equals(eventTicket.getTicketingTypeId().getTicketTypeFormat()) && IN_PERSON.equals(eventTicket.getCheckInSource())) {
                        eventTicket.setCheckInSource(HYBRID_CHECKIN);
                    } else {
                        eventTicket.setCheckInSource(VIRTUAL_EVENT_PORTAL);
                    }
                    updatedEventTickets.add(eventTicket);
                    log.info("update event ticket status to checked in for userId {} and eventTicketId {} and checkIn source {} and checkIn Date {}", user.getUserId(), eventTicket.getId(), eventTicket.getCheckInSource(), eventTicket.getCheckInDate());
                }
            });
            eventCommonRepoService.saveAll(updatedEventTickets);
            if(eventTicketId != null && TicketStatus.REGISTERED.equals(eventTicketId.getTicketStatus())) {
                afterTaskIntegrationTriggerService.userCheckInPostProcess(eventTicketId, event);
                afterTaskIntegrationTriggerService.trayCheckInPostProcess(event, user, eventTicketId.getId());
            }
        }


        return checkInFirstTimeWithTicket;
    }


    @Override
    @Transactional
    public void collectPaymentOfFreeTicketTypesForScheduler(Event event, boolean isScheduler) {
        try {
            collectPaymentOfFreeTicketTypes(event, isScheduler);
        } catch (Exception e) {
            log.error("TicketingCheckInServiceImpl | collectPaymentOfFreeTicketTypesForScheduler:{}", e.getMessage());
        }
    }

    @Async
    @Transactional
    public void collectPaymentOfFreeTicketTypes(Event event, boolean isSchedulerCall) {

        Staff staff = staffService.findFirstByEventId(event);
        User user = staff.getUser();
        List<EnumUserSessionPaymentStatus> paymentStatus = Arrays.asList(EnumUserSessionPaymentStatus.UNPAID, EnumUserSessionPaymentStatus.FAILED);

        List<AttendeeCheckInRecords> checkInRecords = attendeeCheckInRecordsRepository.findByStatusAndEventId(paymentStatus, event.getEventId());

        if (!CollectionUtils.isEmpty(checkInRecords) && (isSchedulerCall || checkInRecords.size() >= 50)) {
            TicketingDatesDto ticketing = ticketingRepository.findEventStartAndEndDateByEventid(event);
            long numberOfDaysOfEvent = DateUtils.getNumberDaysInclusiveBothTheDatesInEventTimeZone(ticketing.getEventStartDate(), ticketing.getEventEndDate(), event.getEquivalentTimeZone());
            paymentHandlerService.createVirtualEventBillingPayout(event, numberOfDaysOfEvent, checkInRecords.size(), user, checkInRecords);
        }
    }

    private void createAttendeeCheckInRecords(EventTickets e, Long userId, long eventId) {
        if (null != e.getTicketingTypeId() && TicketType.FREE.equals(e.getTicketingTypeId().getTicketType())) {
            log.info("createAttendeeCheckInRecords for userId {} and eventTicketId {} ", userId, e.getId());
            AttendeeCheckInRecords attendeeCheckInRecords = new AttendeeCheckInRecords();
            attendeeCheckInRecords.setEventId(eventId);
            attendeeCheckInRecords.setUserId(userId);
            attendeeCheckInRecords.setPaymentStatus(EnumUserSessionPaymentStatus.UNPAID);
            attendeeCheckInRecordsRepository.save(attendeeCheckInRecords);
        }
    }

    public void checkInEventTicketsRecord(User user, List<EventTickets> eventTickets) {
        log.info("checkInEventTicketsRecord for userId {} and eventTicketId {} ", user, eventTickets.get(0).getId());
        for (EventTickets eventTicket : eventTickets) {
            eventTicket.setTicketStatus(TicketStatus.CHECKED_IN);
            eventTicket.setCheckInStaff(user);
            eventTicket.setCheckInDate(new Date());
            if (TicketTypeFormat.HYBRID.equals(eventTicket.getTicketingTypeId().getTicketTypeFormat()) && IN_PERSON.equals(eventTicket.getCheckInSource())) {
                eventTicket.setCheckInSource(HYBRID_CHECKIN);
            } else {
                eventTicket.setCheckInSource(VIRTUAL_EVENT_PORTAL);
            }
            eventCommonRepoService.save(eventTicket);
        }
    }

    private CheckInUserTracking getCheckinUserTrackingStatus(Event event, User user) {
        if (!CommonUtil.getLiveEventStatuses().contains(event.getEventListingStatus())) {
            return TEST_MODE;
        }

        Ticketing ticketing = ticketingRepository.findByEventid(event);
        if (ticketing != null) {
            Date currentTime = DateUtils.getCurrentDate();

            Date adjustedEventStartDateZeroHoursTimeInTimeZone = getAdjustedDate(ticketing.getEventStartDate(), getEquivalentTimeZone(event.getEquivalentTimeZone()));
            Date adjustedEventStartDateZeroHoursTimeInUTC = TimeZoneUtil.getDateInUTC(adjustedEventStartDateZeroHoursTimeInTimeZone, event.getEquivalentTimeZone());
            Date adjustedEventEndDateZeroHoursTimeInTimeZone = DateUtils.getOnlyDateFromDate(DateUtils.getDate(ticketing.getEventEndDate(), getEquivalentTimeZone(event.getEquivalentTimeZone())));
            Date adjustedEventEndDateZeroHoursTimeInUTC = new DateTime(TimeZoneUtil.getDateInUTC(adjustedEventEndDateZeroHoursTimeInTimeZone, event.getEquivalentTimeZone())).plusDays(1).toDate();

            if (currentTime.before(adjustedEventStartDateZeroHoursTimeInUTC)) {
                return PRE_EVENT_CHECKIN;
            }

            if (currentTime.after(adjustedEventEndDateZeroHoursTimeInUTC)) {
                Long countPreviousAddedRecord = checkInAuditLogService.countPreviousCheckInRecordAddedRecord(user, event);
                if (NumberUtils.isNumberGreaterThanZero(countPreviousAddedRecord)) {
                    return CheckInUserTracking.POST_EVENT_CHECKIN_LIVE_PRESENT;
                } else {
                    return CheckInUserTracking.POST_EVENT_CHECKIN_NOT_LIVE_PRESENT;
                }
            }

            if (actualEventLiveTime(ticketing, currentTime)) {
                return CheckInUserTracking.LIVE_EVENT_CHECKIN;
            } else {
                return CheckInUserTracking.ADJUSTED_LIVE_EVENT_CHECKIN;
            }
        } else {
            return null;
        }
    }

    private Date getAdjustedDate(Date eventStartDate, String equivalentTimeZone) {
        return DateUtils.getOnlyDateFromDate(DateUtils.getDate(eventStartDate,
                equivalentTimeZone));
    }

    private boolean actualEventLiveTime(Ticketing ticketing, Date currentTime) {
        return currentTime.after(ticketing.getEventStartDate()) && currentTime.before(ticketing.getEventEndDate());
    }

    /**
     * Send data to kinesis for check-in to event
     *
     * @param event
     * @param user
     */
    @Override
    @Async
    public void sendCheckInDataToKinesis(Event event, User user) {
        try {
            log.info("Start send event check in data to kinesis user {}, event {}", user.getUserId(), event.getEventId());
            List<EventChallengeDetail> challengeList = challengeConfigService.findChallengesByEventIdAndType(event.getEventId(), ChallengeType.EARLY_BIRD);
            log.info("Fetched event early bird challenge size {}, event {}", challengeList.size(), event.getEventId());
            if (!CollectionUtils.isEmpty(challengeList)) {
                int users;

                List<String> kinesisRequestBody = new ArrayList<>();

                for (EventChallengeDetail challenge : challengeList) {
                    String json = challenge.getJsonValue();
                    JSONObject jsonObject = new JSONObject(json);
                    if (jsonObject.has(ChallengeConstants.ACTION)) {
                        JSONArray actions = jsonObject.getJSONArray(ChallengeConstants.ACTION);
                        if (actions.length() > 0) {
                            JSONObject action = actions.getJSONObject(0);
                            if (action != null && action.has(ChallengeConstants.NAME) && ChallengeConstants.CHECK_IN.equals(action.get(ChallengeConstants.NAME))) {
                                users = action.getInt(ChallengeConstants.USERS);
                                log.info("Successfully parsed how many users will earn points from challenge json users {}, event {}", users, event.getEventId());
                                Long checkinUserCount = checkInAuditLogService.getCheckInAttendeeCountByEventAndCreatedAfter(event, challenge.getStartDate());
                                log.info("Number of users who checked-in to event checkinUserCount {}, event {}", checkinUserCount, event.getEventId());
                                if (users >= checkinUserCount) {
                                    kinesisRequestBody.add(prepareKinesisRequestBody(event, user));
                                }
                            }
                        }
                    }
                }
                kinesisService.sendDataToKinesis(kinesisRequestBody);
            }
            log.info("End send event check in data to kinesis user {}, event {}", user.getUserId(), event.getEventId());
        } catch (JSONException e) {
            log.info("Error while parsing challenge json event {}, user {}, error {}", event.getEventId(), user.getUserId(), e);
        } catch (Exception e) {
            log.info("Error while sending event check in data to kinesis event {}, user {}, error {}", event.getEventId(), user.getUserId(), e);
        }
    }

    /**
     * Prepare kinesis request body
     *
     * @param event
     * @param user
     * @return
     * @throws JSONException when parsing fails
     */
    private String prepareKinesisRequestBody(Event event, User user) {
        List<TicketTypeOtherRestrictionDto> ticketTypeIds = roEventTicketsService.getUserTicketOtherRestrictions(user, event);
        TrackingData trackingData = new TrackingData();
        trackingData.setEventId(event.getEventId());
        trackingData.setUserId(user.getUserId());
        trackingData.setArea(AREA.EVENT);
        trackingData.setDate(new Date());
        trackingData.setEventCheckIn(true);
        trackingData.setTicketTypeIds(ticketTypeIds.stream().map(TicketTypeOtherRestrictionDto::getTicketTypeId).distinct().collect(Collectors.toList()));
        String requestBody = JsonMapper.convertToString(trackingData);
        log.info("Successfully Event check-in kinesis request data created data {}", requestBody);
        return requestBody;
    }

    @Override
    public void sendMessageToSlack(String message, Event event, User user) {
        String stringBuilder = "*Error : * " + message + NEW_LINE +
                "*Event URL : * " + serviceHelper.getEventBaseUrl(event).concat(getEventPath()).concat(event.getEventURL()) + NEW_LINE +
                "*Event Id : * " + event.getEventId() + NEW_LINE +
                "*User : * " + user.getUserId() + NEW_LINE;
        slackService.sendMessage(new SlackMessage(checkInErrorSlackWebhook, CHANNEL_NAME, USER_NAME, stringBuilder, null));
    }

    public void getEventTicketIdByBarcodeIdAndEventId(String barcodeId, Long eventId, Long loggedInUser, AttendeeAttributeValueDto attendeeAttributes) {

        log.info("Get EventTicketId with barcode {}", barcodeId);
        Long eventTicketId = eventCommonRepoService.findIdByBarcodeIdAndEventId(barcodeId, eventId);
        if (eventTicketId != null) {
            try {
                ticketHolderEditAttributesService.updateTicketHolderData(eventTicketId, loggedInUser, attendeeAttributes);
            } catch (JAXBException e) {
                throw new RuntimeException(e);
            }

        } else {
            throw new NotFoundException(NotFoundException.SessionSpeakerNotFound.EVENT_TICKET_NOT_FOUND);
        }


    }

    @Override
    public boolean isWaitListCheckoutLinkExpired(Event event, String waitListIds) {
        if (StringUtils.isBlank(waitListIds)) {
            throw new NotAcceptableException(NotAcceptableException.WaitListExceptionMsg.INVALID_WAIT_LIST_IDS);
        }
        List<Long> waitListLongIds = Arrays.stream(waitListIds.split(STRING_COMMA)).filter(s -> StringUtils.isNoneBlank(s) && !"null".equalsIgnoreCase(s)).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<WaitList> waitList = waitListService.findByIdInAndEventAndStatus(waitListLongIds, event, WaitListStatus.WAITING);
        return waitList.isEmpty();
    }

    @Override
    public Boolean checkUserAllowedToCheckInWithUnPaidTicket(Event event, User user, boolean isAdminStaffOrSuperAdmin) {
        List<EventTickets> eventTickets = eventCommonRepoService.findEventTicketsOnlyByEventIdAndHolderUserId(event.getEventId(), user.getUserId());
        boolean isHaveAccessGreaterThanAttendee = speakerService.isSpeakerInEvent(event, user)
                || roStaffService.isUserHaveAdminOrStaffOrExhibitorAdminOrLeadRetrieverRole(event, user);

        boolean isSuperAdminAndRolesGreaterThanAttendee = isAdminStaffOrSuperAdmin || isHaveAccessGreaterThanAttendee;

        if(!CollectionUtils.isEmpty(eventTickets) && !isSuperAdminAndRolesGreaterThanAttendee
                && isUnPaidTicketNotAllowedToCheckIn(eventTickets.get(0), event.getEventId())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /***
     * Method to map barcode with RFID
     * @param barcode
     * @param user
     * @param event
     * @param rfid
     * @param enforced
     * @param source
     * @param sourceDescription
     * @return
     */

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public TicketHolderBasicDetailDto barcodeMappingWithRFID(String barcode, User user, Event event, String rfid, boolean enforced, String source, String sourceDescription) {//NOSONAR
        if (user != null) {
            log.info("User {} is trying to map barcode {} with rfid {} for event {}", user.getUserId(), barcode, rfid, event.getEventId());
            if (StringUtils.isBlank(barcode)) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_ID_REQUIRED_TO_MAP_RFID);
            }
            if(StringUtils.isBlank(rfid)){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.RFID_REQUIRED_TO_MAP_BARCODE);
            }
            List<EventTickets> eventTicketsList = new ArrayList<>();
            EventTickets existingEventTicket = eventTicketsRepoService.findByRfidTagAndEventIdWithoutJoin(rfid, event.getEventId());
            if (existingEventTicket != null) {
                log.info("Event ticket with rfid {} already exists for event {}", rfid, event.getEventId());
                boolean guestOfBuyer = existingEventTicket.getGuestOfBuyer();
                String guestOfBuyerName = null;
                if(guestOfBuyer){
                    guestOfBuyerName = GUEST_OF + STRING_BLANK + existingEventTicket.getTicketPurchaserId().getFirstName() + STRING_BLANK + existingEventTicket.getTicketPurchaserId().getLastName();
                }

                if(existingEventTicket.getBarcodeId().equals(barcode)){
                    log.info("Event ticket with rfid {} and barcode {} already mapped for event {}", rfid, barcode, event.getEventId());
                    return new TicketHolderBasicDetailDto(existingEventTicket.getId(), existingEventTicket.getHolderFirstName(), existingEventTicket.getHolderLastName(), existingEventTicket.getHolderEmail(), existingEventTicket.getTicketingOrderId(), false, guestOfBuyer, guestOfBuyerName);
                }
                if(Boolean.FALSE.equals(enforced)){
                    log.info("Event ticket with rfid {} already exists for event {}, but not enforced to map with barcode {}", rfid, event.getEventId(), barcode);
                    return new TicketHolderBasicDetailDto(existingEventTicket.getId(), existingEventTicket.getHolderFirstName(), existingEventTicket.getHolderLastName(), existingEventTicket.getHolderEmail(), existingEventTicket.getTicketingOrderId(), true,guestOfBuyer, guestOfBuyerName);
                }
                log.info("Event ticket with rfid {} already exists for event {}, but will be removed to map with barcode {}", rfid, event.getEventId(), barcode);
                existingEventTicket.setRfidId(null);
                eventTicketsList.add(existingEventTicket);
                createBadgeProgramingActivityLog(event, barcode, source, sourceDescription, existingEventTicket, user.getUserId(), "Badge Deprogram");
            }

            EventTickets eventTickets = eventTicketsRepoService.findByBarcodeIdAndEventId(barcode, event.getEventId());

            if(eventTickets == null){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
            }
            eventTickets.setRfidId(rfid);
            eventTickets.setRfidTagAssignedAt(new Date());
            eventTicketsList.add(eventTickets);
            eventTicketsRepoService.saveAll(eventTicketsList);

            boolean guestOfBuyer = eventTickets.getGuestOfBuyer();
            String guestOfBuyerName = null;
            if(guestOfBuyer){
                guestOfBuyerName = GUEST_OF + STRING_BLANK + eventTickets.getTicketPurchaserId().getFirstName() + STRING_BLANK + eventTickets.getTicketPurchaserId().getLastName();
            }
            log.info("Event ticket with barcode {} and rfid {} mapped successfully for event {}", barcode, rfid, event.getEventId());
            createBadgeProgramingActivityLog(event, barcode, source, sourceDescription, eventTickets, user.getUserId(), "Badge Programming");
            return new TicketHolderBasicDetailDto(eventTickets.getId(), eventTickets.getHolderFirstName(), eventTickets.getHolderLastName(), eventTickets.getHolderEmail(), eventTickets.getTicketingOrderId(), false, guestOfBuyer, guestOfBuyerName);
        } else {
            throw new AuthorizationException(Constants.PLEASE_LOGIN);
        }
    }

    /***
     * This method is used to check in or check out a ticket based on the provided parameters.
     * @param barcodeOrRfidTag
     * @param user
     * @param event
     * @param device
     * @param isAppLogin
     * @param source
     * @param sourceDescription
     * @param isRFIDCheckin
     * @param entry
     * @return
     * @throws IOException
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public TicketCheckInDto entryExit(String barcodeOrRfidTag, User user, Event event, String device, boolean isAppLogin, String source, String sourceDescription, boolean isRFIDCheckin, boolean entry) throws IOException {
        EntryExitSettings entryExitSettings = roEntryExitSettingsRepository.findByEventId(event.getEventId()).orElse(null);

        if (StringUtils.isBlank(barcodeOrRfidTag)) {
            throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.BARCODE_OR_RFID_TAG_REQUIRED_TO_ENTER_EXIT_TICKET);
        }

        EventTickets eventTicket = isRFIDCheckin
                ? eventTicketsRepoService.findByRfidTagAndEventIdWithoutJoin(barcodeOrRfidTag, event.getEventId())
                : eventTicketsRepoService.findByBarcodeIdAndEventIdWithoutJoin(barcodeOrRfidTag, event.getEventId());

        if (eventTicket == null) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.NO_SUCH_EVENT_TICKET_EXIST);
        }

        if (entryExitSettings == null || Boolean.FALSE.equals(entryExitSettings.isEnabled())) {
            throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.ENTRY_EXIT_MODULE_DISABLED);
        }

        if(Boolean.FALSE.equals(entry)){
            if(EntryExitStatus.ENTRY.equals(eventTicket.getEntryExitStatus()) && TicketStatus.CHECKED_IN.equals(eventTicket.getTicketStatus())) {
                eventTicket.setEntryExitStatus(EntryExitStatus.EXIT);
                eventTicketsRepoService.save(eventTicket);
                createEntryExitActivityLog(event, user, barcodeOrRfidTag, EntryExitStatus.EXIT, true, null, isRFIDCheckin, source, sourceDescription, isAppLogin, eventTicket, user.getUserId());
            }
            else{
                throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.ENTRY_REQUIRED_BEFORE_EXIT);
            }
        }
        else{
            if(eventTicket.getTicketStatus() == null || !TicketStatus.CHECKED_IN.equals(eventTicket.getTicketStatus())) {
                checkInTicket(null, barcodeOrRfidTag, user, event, device, isAppLogin, source, sourceDescription, isRFIDCheckin);
            }
            else {
                if(Boolean.FALSE.equals(entryExitSettings.isExitScanRequiredToReEnter())) {
                    eventTicket.setEntryExitStatus(EntryExitStatus.ENTRY);
                    eventTicketsRepoService.save(eventTicket);
                    createEntryExitActivityLog(event, user, barcodeOrRfidTag, EntryExitStatus.ENTRY, true, null, isRFIDCheckin, source, sourceDescription, isAppLogin, eventTicket, user.getUserId());
                }
                else {
                    if(EntryExitStatus.EXIT.equals(eventTicket.getEntryExitStatus())) {
                        eventTicket.setEntryExitStatus(EntryExitStatus.ENTRY);
                        eventTicketsRepoService.save(eventTicket);
                        createEntryExitActivityLog(event, user, barcodeOrRfidTag, EntryExitStatus.ENTRY, true, null, isRFIDCheckin, source, sourceDescription, isAppLogin, eventTicket, user.getUserId());
                    }
                    else{
                        throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.EXIT_REQUIRED_BEFORE_REENTER);
                    }
                }
            }
        }
        return getEventTicketEntryExitDetails(eventTicket);
    }

    /***
     * This method retrieves the entry and exit details of an event ticket.
     * @param eventTicket
     * @return
     */

    private TicketCheckInDto getEventTicketEntryExitDetails(EventTickets eventTicket){

        String ticketHolderName = eventTicket.getGuestOfBuyer() ?
                GUEST_OF + STRING_BLANK + eventTicket.getTicketPurchaserId().getFirstName() + STRING_BLANK + eventTicket.getTicketPurchaserId().getLastName()
                : Stream.of(eventTicket.getHolderFirstName(), eventTicket.getHolderLastName()).filter(StringUtils::isNotBlank).collect(Collectors.joining(" "));

        if (StringUtils.isBlank(ticketHolderName.strip())) {
            User purchaser = eventTicket.getTicketingOrder().getPurchaser();
            ticketHolderName = Stream.of(purchaser.getFirstName(), purchaser.getLastName())
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(" "));
        }

        return new TicketCheckInDto(eventTicket.getTicketStatus().getStatus(),
                eventTicket.getTicketingTypeId().getTicketing().getId(),
                ticketHolderName,
                eventTicket.getTicketingTypeId().getTicketTypeName());

    }

    /***
     * This method is used to bulk check in or check out tickets based on the provided parameters.
     * @param barcodeIds
     * @param user
     * @param event
     * @param device
     * @param source
     * @param sourceDescription
     * @param entry
     * @param isAppLogin
     * @return
     */

    @Override
    @Transactional
    public List<String> bulkEntryExit(List<String> barcodeIds, User user, Event event, String device, String source, String sourceDescription, boolean entry, boolean isAppLogin) {
        if (CollectionUtils.isEmpty(barcodeIds)) {
            throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.BARCODE_IDS_REQUIRED_TO_ENTER_EXIT_TICKET);
        }

        EntryExitSettings entryExitSettings = roEntryExitSettingsRepository.findByEventId(event.getEventId()).orElse(null);
        if (entryExitSettings == null || !entryExitSettings.isEnabled()) {
            throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.ENTRY_EXIT_MODULE_DISABLED);
        }

        if(barcodeIds.size() > 1 && entry){
            throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.BULK_ENTRY_NOT_ALLOWED);
        }

        List<EventTickets> eventTickets = eventCommonRepoService.findByBarcodeIds(barcodeIds);

        if (CollectionUtils.isEmpty(eventTickets)) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.NO_SUCH_EVENT_TICKET_EXIST);
        }

        List<EventTickets> qualifiedEventTickets = new ArrayList<>();
        List<String> nonCheckedInBarcodes = new ArrayList<>();

        EntryExitStatus entryExitStatus = entry ? EntryExitStatus.ENTRY : EntryExitStatus.EXIT;

        for(EventTickets eventTicket: eventTickets){
            if (TicketStatus.CHECKED_IN.equals(eventTicket.getTicketStatus())){
                eventTicket.setEntryExitStatus(entryExitStatus);
                qualifiedEventTickets.add(eventTicket);
            }
            else {
                nonCheckedInBarcodes.add(eventTicket.getBarcodeId());
            }
        }

        if (CollectionUtils.isEmpty(qualifiedEventTickets)) {
            throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.NO_CHECKED_IN_TICKETS_FOUND);
        }

        eventTicketsRepoService.saveAll(qualifiedEventTickets);

        createBulkEntryExitActivityLog(event, entryExitStatus, source, sourceDescription, isAppLogin, qualifiedEventTickets, user.getUserId());

        return nonCheckedInBarcodes;
    }

    /***
     * This method validates the entry exit password for an event.
     * @param event
     * @param entryExitSettingsPasswordDto
     */

    @Override
    public void validateEntryExitPassword(Event event, EntryExitSettingsPasswordDto entryExitSettingsPasswordDto) {
        EntryExitSettings entryExitSettings = roEntryExitSettingsRepository.findByEventId(event.getEventId()).orElse(null);
        if (entryExitSettings == null || !entryExitSettings.isEnabled()) {
            throw new NotAcceptableException(NotAcceptableException.EntryExitExceptionMsg.ENTRY_EXIT_MODULE_DISABLED);
        }
        if(entryExitSettingsPasswordDto.isEntryExitPassword() && !entryExitSettings.getPassword().equals(entryExitSettingsPasswordDto.getPassword())){
            throw new NotFoundException(NotFoundException.NotFound.PASSWORD_NOT_MATCH);
        }
    }

    /***
     * This method creates an entry exit activity log and store in dynomoDB for an event.
     * @param event
     * @param user
     * @param barcodeId
     * @param entryExitLogType
     * @param success
     * @param baseException
     * @param isRfidCheckIn
     * @param source
     * @param sourceDescription
     * @param isAppLogin
     * @param eventTickets
     * @param staffUserId
     */

    @Override
    public void createEntryExitActivityLog(Event event, User user, String barcodeId, EntryExitStatus entryExitLogType, boolean success, BaseException baseException, boolean isRfidCheckIn, String source, String sourceDescription, boolean isAppLogin, EventTickets eventTickets, Long staffUserId){
        try{
            boolean isEntryExitEnabledForEvent = roEntryExitSettingsRepository.isEntryExitEnabledForEvent(event.getEventId());
            if(StringUtils.isBlank(barcodeId)) {
                return;
            }
            if(isEntryExitEnabledForEvent) {
                if(eventTickets == null){
                    if (isRfidCheckIn){
                        eventTickets = eventTicketsRepoService.findByRfidTagAndEventIdWithoutJoin(barcodeId, event.getEventId());
                    } else {
                        eventTickets = eventCommonRepoService.findByBarcodeId(barcodeId);
                    }
                }
                if (eventTickets != null){
                    String sourceName;
                    if(StringUtils.isNotEmpty(source)){
                        sourceName = source;
                    }
                    else{
                        sourceName = (isAppLogin) ? "MobileApp" : "Desktop";
                    }
                    HashMap<String, Object> userActivity = new HashMap<>();
                    if(sourceDescription != null ){
                        try {
                            String decodedParam = URLDecoder.decode(sourceDescription, StandardCharsets.UTF_8.name());
                            Map<String, Object> sourceDescriptionMap = JsonMapper.stringtoObject(decodedParam, Map.class);
                            if (sourceDescriptionMap != null) {
                                userActivity.putAll(sourceDescriptionMap);
                            }

                        } catch (UnsupportedEncodingException e) {
                            log.info("Error decoding source description: {}", e.getMessage());
                        }
                    }

                    if(isAppLogin){
                        userActivity.put(isRfidCheckIn ? "rfIdTag" : "barcodeId", barcodeId);
                    }

                    String errorCode = null;

                    if(Boolean.FALSE.equals(success)){
                        userActivity.put("error", baseException != null ? baseException.getErrorMessage() : "Unknown error");
                        errorCode = (baseException != null) ? baseException.getErrorCode() : null;
                    }

                    userActivityService.createUserActivity(userActivity, event.getEventId(), eventTickets.getUserId(), entryExitLogType.name(), sourceName, errorCode, staffUserId );
                }
            }
        }
        catch (Exception exception) {
            log.info("Exception occurred while creating entry exit activity log: {}", exception.getMessage(), exception);
        }
    }

    /***
     * This method creates a bulk entry exit activity log and store in dynomoDB for an event.
     * @param event
     * @param entryExitLogType
     * @param source
     * @param sourceDescription
     * @param isAppLogin
     * @param eventTickets
     * @param staffUserId
     */

    private void createBulkEntryExitActivityLog(Event event,  EntryExitStatus entryExitLogType,  String source, String sourceDescription, boolean isAppLogin, List<EventTickets> eventTickets, Long staffUserId){
        try{
            if(CollectionUtils.isEmpty(eventTickets)) {
                return;
            }
            String sourceName;
            if(StringUtils.isNotEmpty(source)){
                sourceName = source;
            }
            else{
                sourceName = (isAppLogin) ? "MobileApp" : "Desktop";
            }
            HashMap<String, Object> userActivityDescription = new HashMap<>();
            if(sourceDescription != null ){
                try {
                    String decodedParam = URLDecoder.decode(sourceDescription, StandardCharsets.UTF_8.name());
                    Map<String, Object> sourceDescriptionMap = JsonMapper.stringtoObject(decodedParam, Map.class);
                    if (sourceDescriptionMap != null) {
                        userActivityDescription.putAll(sourceDescriptionMap);
                    }

                } catch (UnsupportedEncodingException e) {
                    log.info("Error decoding source description: {}", e.getMessage());
                }
            }

            List<UserActivity> userActivities = new ArrayList<>();
            for (EventTickets eventTicket : eventTickets) {
                UserActivity userActivity = new UserActivity();
                userActivity.setUserId(eventTicket.getUserId());
                userActivity.setEventId(event.getEventId());
                userActivity.setActionType(entryExitLogType.name());
                userActivity.setDescription(userActivityDescription);
                userActivity.setSource(sourceName);
                userActivity.setTimestamp(ensureUniqueTimestamp(userActivities, eventTicket.getUserId(), System.currentTimeMillis()));
                userActivity.setToBeDisplayedInTimeline(Boolean.TRUE);
                userActivity.setStaffUserId(staffUserId);
                userActivities.add(userActivity);
            }

            userActivityService.createBulkUserActivity(userActivities);
        }
        catch (Exception exception) {
            log.info("Exception occurred while creating entry exit activity log: {}", exception.getMessage(), exception);
        }
    }

    /***
     * This method ensures that the timestamp for each user activity is unique.
     * If a timestamp already exists for a user, it increments the timestamp until it finds a unique one.
     * @param list List of UserActivity objects
     * @param userId ID of the user
     * @param timestamp Initial timestamp to check
     * @return A unique timestamp for the user activity
     */

    private long ensureUniqueTimestamp(List<UserActivity> list, Long userId, Long timestamp) {
        Set<Long> existingTimestamps = list.stream()
                .filter(activity -> activity.getUserId().equals(userId))
                .map(UserActivity::getTimestamp)
                .collect(Collectors.toSet());

        while (existingTimestamps.contains(timestamp)) {
            timestamp++;
        }

        return timestamp;
    }

    private void createBadgeProgramingActivityLog(Event event, String barcodeId, String source, String sourceDescription, EventTickets eventTickets, Long staffUserId, String activity){
        try{
            String sourceName;
            if(StringUtils.isNotEmpty(source)){
                sourceName = source;
            }
            else{
                sourceName = "MobileApp";
            }
            HashMap<String, Object> userActivity = new HashMap<>();
            if(sourceDescription != null ) {
                try {
                    String decodedParam = URLDecoder.decode(sourceDescription, StandardCharsets.UTF_8.name());
                    Map<String, Object> sourceDescriptionMap = JsonMapper.stringtoObject(decodedParam, Map.class);
                    if (sourceDescriptionMap != null) {
                        userActivity.putAll(sourceDescriptionMap);
                    }

                } catch (UnsupportedEncodingException e) {
                    log.info("Error decoding source description: {}", e.getMessage());
                }
            }

            userActivity.put("barcodeId", barcodeId);
            userActivity.put("rfIdTag", eventTickets.getRfidId());
            userActivityService.createUserActivity(userActivity, event.getEventId(), eventTickets.getUserId(), activity, sourceName, null, staffUserId );
        }
        catch (Exception exception) {
            log.info("Exception occurred while creating entry exit activity log: {}", exception.getMessage(), exception);
        }
    }
}