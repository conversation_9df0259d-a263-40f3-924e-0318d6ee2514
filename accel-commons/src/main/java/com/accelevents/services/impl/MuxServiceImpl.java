package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.ResponseDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventLiveStreamingConfiguration;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.AssetType;
import com.accelevents.domain.enums.MuxSupportLanguageCode;
import com.accelevents.domain.enums.ProcessingStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.domain.session_speakers.SessionSubtitle;
import com.accelevents.dto.*;
import com.accelevents.exceptions.MUXException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.services.EventLiveStreamingConfigurationService;
import com.accelevents.services.MuxService;
import com.accelevents.session_speakers.dto.CaptionsDto;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import com.accelevents.session_speakers.services.MUXLivestreamAssetRepoService;
import com.accelevents.session_speakers.services.SessionSubtitleService;
import com.accelevents.session_speakers.util.ThirdPartyUtils;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.JsonMapper;
import com.accelevents.utils.MuxUtils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.json.Json;
import javax.json.JsonArrayBuilder;
import javax.json.JsonObject;
import javax.json.JsonObjectBuilder;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.StreamProvider.ACCELEVENTS;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.CAN_NOT_ENABLE_LIVE_CAPTIONING_IN_LIVE_STREAMING;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.LIVE_CAPTIONS_DOES_NOT_SUPPORT_LOW_LATENCY;

@Service
public class MuxServiceImpl implements MuxService {

	private static final String MUX_STREAM_URL = "https://api.mux.com";
	private static final String MUX_ASSET_ID_PATH = "/video/v1/assets/";

    private static final String MUX_LIVE_STREAM_ID_PATH = "/video/v1/live-streams/";
	private static final String DURATION = "duration";

    private static final Pattern pattern = Pattern.compile(
            "[^\\p{L}\\p{N}\\p{Z}]",
            Pattern.UNICODE_CHARACTER_CLASS);



	private static final Logger log = LoggerFactory.getLogger(MuxServiceImpl.class);

    private static final List<Map<String, String>> MUX_CAPTION_LANGUAGE_LIST = Arrays.stream(MuxSupportLanguageCode.values())
            .map(code -> {
                Map<String, String> languageMap = new HashMap<>();
                languageMap.put("languageCode", code.getLanguageCode());
                languageMap.put("languageRegionName", code.getLanguageRegionName());
                return languageMap;
            })
            .collect(Collectors.toList());;

	@Value("${uiBaseurl}")
	private String uiBaseurl;

    @Value("${app.profile}")
    private String profile;

    @Value("${domainName}")
    private String domainName;

    @Value("${mux.private.key}")
    private String privateKeyFileName;

    @Value("${mux.data.private.key}")
    private String privateKeyDataFileName;

    @Value("${playback_restriction_id}")
    private String playback_restriction_id;

    @Value("${signing_key}")
    private String signing_key;

    @Value("${data_signing_key}")
    private String data_signing_key;

    @Value("${cloud.aws.s3.bucket.mux.assets}")
    private String aeMuxAssetsBucket;

    @Value("${cloud.aws.cloudFront.mux.assets.url}")
    private String aeMuxAssetsCloudFrontUrl;
    @Autowired
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;

    @Autowired
    private EventLiveStreamingConfigurationService eventLiveStreamingConfigurationService;

    private final String authorization;
    private final SessionSubtitleService sessionSubtitleService;

	@Autowired
	public MuxServiceImpl(@Value("${mux.secretKey}") String muxSecretKey,
						  @Value("${mux.apiToken}") String muxToken,
                          SessionSubtitleService sessionSubtitleService){
		authorization = ThirdPartyUtils.BASIC_AUTH(muxToken,muxSecretKey);
        this.sessionSubtitleService = sessionSubtitleService;
	}

	@Override
	public Map<String, String> getAllLiveStreams(int pageNumber){
		HttpResponse<JsonNode> jsonResponse = null;
		Map<String, String> map = new HashMap<>();
		try {
			jsonResponse = Unirest
					.get(MUX_STREAM_URL+"/video/v1/live-streams?limit=100&page="+pageNumber)
					.headers(getHeaders())
					.asJson();
				JSONArray data = (JSONArray) jsonResponse.getBody().getObject().get("data");
				for(int i =0 ; i < data.length(); i++){
					String key = ((JSONObject) data.get(i)).getString("stream_key");
					String id = ((JSONObject) data.get(i)).getString("id");
					map.put(key,id);
				}
            log.info("Exception MuxServiceImpl getAllLiveStreams  data {} success", data);
		} catch (Exception e) {
			log.info("Exception MuxServiceImpl getAllLiveStreams  error {}" , e.getMessage());
			MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_NOT_AVAILABLE;
			exceptionMessage.setErrorMessage(e.getMessage());
			exceptionMessage.setDeveloperMessage(e.getMessage());
			throw new MUXException(exceptionMessage);
		}
		return map;
	}

	@Override
    public String markDisableLiveStreamAbSorbException(String liveStreamId){
	    try {
            return markDisableLiveStream(liveStreamId);
        } catch (Exception e){
	        return null;
        }
    }

	@Override
	@Async
	public String markDisableLiveStream(String liveStreamId){
		try {
			log.info("MuxServiceImpl markDisableLiveStream Stop conf mux to stopped with liveStreamId {}", liveStreamId);
			HttpResponse<JsonNode> jsonResponse = Unirest
					.put(MUX_STREAM_URL+Constants.MUX_LIVE_STREAMS_VIDEO_V1_PATH+liveStreamId+"/disable")
					.headers(getHeaders())
					.asJson();
			if(jsonResponse.getStatus() >= 200
					&& jsonResponse.getStatus() <=209){
				log.info("MuxServiceImpl markDisableLiveStream Stop/disable conf mux to stopped with liveStreamId {} success", liveStreamId);
				return "DONE";
			}
		} catch (Exception e) {
            log.info("Exception MuxServiceImpl markDisableLiveStream conf mux to disable with liveStreamId {} error {}", liveStreamId, e.getMessage());
			MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_NOT_COMPLETED;
			exceptionMessage.setErrorMessage(e.getMessage());
			exceptionMessage.setDeveloperMessage(e.getMessage());
			throw new MUXException(exceptionMessage);
		}

		return null;
	}

    @Override
    @Async
    public String markEnableLiveStreamAbsorbException(String liveStreamId){
	    try {
            makeReEnabledLiveStream(liveStreamId);
            return "DONE";
        }catch (Exception e){

        }
        return null;
    }

    @Override
    public String createAssetPlayBackId(String assetId){
        try {
            log.info("MuxServiceImpl createAssetPlayBackId assetId {}", assetId);
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .post(MUX_STREAM_URL+MUX_ASSET_ID_PATH+assetId+"/playback-ids")
                    .headers(getHeaders())
                    .body("{ \"policy\": \"signed\" }")
                    .asJson();
            if(jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <=209){
                log.info("MuxServiceImpl createAssetPlayBackId assetId {} success", assetId);
                JSONObject jsonObject = jsonResponse.getBody().getObject();
                JSONObject data  = jsonObject.getJSONObject("data");
                return data.getString("id");
            }
        } catch (Exception e) {
            log.info("Exception MuxServiceImpl createAssetPlayBackId assetId {} with error {}", assetId, e.getMessage());
        }

        return null;
    }

    @Override
    @Async
    public ResponseDto deleteAssetPlayBackId(String assetId, String playBackId){
        try {
            log.info("MuxServiceImpl deleteAssetPlayBackId playBackId {} of assetId {}", playBackId, assetId);
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .delete(MUX_STREAM_URL+MUX_ASSET_ID_PATH+assetId+"/playback-ids/"+playBackId)
                    .headers(getHeaders())
                    .asJson();
            if(jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <=209){
                log.info("MuxServiceImpl deleteAssetPlayBackId playBackId {} of assetId {} success",playBackId, assetId);
                return ResponseDto.ok(Constants.ASSET_PLAYBACK_DELETE);
            }
            return ResponseDto.failed(Constants.ASSET_PLAYBACK_DELETE);
        } catch (Exception e) {
            log.info("Exception MuxServiceImpl deleteAssetPlayBackId playBackId {} with error {}", playBackId, e.getMessage());
            return ResponseDto.failed(Constants.ASSET_PLAYBACK_DELETE);
        }
    }

    //This method work as synchronize manner
    @Override
    public ResponseDto deleteAssetWithoutAsync(String assetId){
        return deleteAsset(assetId);
    }

    @Override
    public ResponseDto deleteAssetOnly(String assetId){
        try {
            log.info("MuxServiceImpl deleteAssetOnly assetId {}", assetId);
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .delete(MUX_STREAM_URL+MUX_ASSET_ID_PATH+assetId)
                    .headers(getHeaders())
                    .asJson();
            if(jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <=209){
                log.info("MuxServiceImpl deleteAssetOnly assetId {} success", assetId);
                return ResponseDto.ok(Constants.ASSET_DELETE);
            } else if(jsonResponse.getStatus() == 404 || jsonResponse.getStatus() == 400) {
                log.info("MuxServiceImpl deleteAssetOnly | assetId {} not found", assetId);
                return ResponseDto.failed(Constants.ASSET_NOT_FOUND);
            }
        } catch (Exception e) {
            log.info("Exception MuxServiceImpl deleteAssetOnly assetId {} with error {} ", assetId, e.getMessage());
        }
        return null;
    }

    @Override
    @Async
    public ResponseDto deleteAsset(String assetId){
        try {
            log.info("MuxServiceImpl deleteAsset assetId {}", assetId);
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .delete(MUX_STREAM_URL+MUX_ASSET_ID_PATH+assetId)
                    .headers(getHeaders())
                    .asJson();
            if(jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <=209){
                log.info("MuxServiceImpl deleteAsset assetId {} success", assetId);
                return ResponseDto.ok("ASSET_DELETE");
            } else if(jsonResponse.getStatus() == 404 || jsonResponse.getStatus() == 400) {
                log.info("MuxServiceImpl deleteAsset | assetId {} not found", assetId);
                return ResponseDto.failed(Constants.ASSET_NOT_FOUND);
            } else {
                return deleteAssetsPlayBackId(assetId).get();
            }
        } catch (Exception e) {
            log.info("Exception MuxServiceImpl deleteAsset assetId {} with error {} ", assetId, e.getMessage());
            return deleteAssetsPlayBackId(assetId).get();
        }
    }

    private AtomicReference<ResponseDto> deleteAssetsPlayBackId(String assetId) {
        AtomicReference<ResponseDto> returnResponseDto = new AtomicReference<>(ResponseDto.failed("ASSET_DELETE"));
        getPlayBackIds(assetId).forEach(playBackId->{
            ResponseDto responseDto = deleteAssetPlayBackId(assetId,playBackId);
            if(ProcessingStatus.SUCCESS.equals(responseDto.getProcessingStatus())){
                returnResponseDto.set(responseDto);
            }
        });
        return returnResponseDto;
    }

    private List<String> getPlayBackIds(String assetId) {
	    try {
            JSONObject jsonBody = getAssetDetails(assetId);
            log.info("MuxServiceImpl getPlayBackIds assetId {}", assetId);
            if (jsonBody != null && jsonBody.has(Constants.PLAYBACK_IDS_STRING)) {
                try {
                    JSONArray jsonArray = jsonBody.getJSONArray(Constants.PLAYBACK_IDS_STRING);
                    List<String> playBackIds = new ArrayList<>();
                    if (jsonArray != null && jsonArray.length() > 0) {
                        for (int i = 0; i < jsonArray.length(); i++) {
                            playBackIds.add(new JSONObject(jsonArray.get(i).toString()).getString("id"));
                        }
                    }
                    log.info("MuxServiceImpl getPlayBackIds assetId {} playbackIds {} success", assetId, playBackIds);
                    return playBackIds;
                } catch (JSONException jsonException) {
                    log.info("Exception MuxServiceImpl getPlayBackIds asset {} with error {} ", assetId, jsonException.getMessage());
                }
            }
        } catch (MUXException muxException) {
            log.info("Exception MuxServiceImpl getPlayBackIds assetId=>{}, error=>{}", assetId, muxException.getErrorMessage());
        }
        return Collections.emptyList();
    }

    @Override
	public String checkStreamStatus(String liveStreamId){
		HttpResponse<JsonNode> jsonResponse = null;
		try {
			jsonResponse = Unirest
					.get(MUX_STREAM_URL+Constants.MUX_LIVE_STREAMS_VIDEO_V1_PATH+liveStreamId)
					.headers(getHeaders())
					.asJson();
				JSONObject data = (JSONObject) jsonResponse.getBody().getObject().get("data");
				return data.getString(Constants.STATUS);
		} catch (Exception e) {
			log.info("Exception while getting live streams status {}" , e.getMessage(), e);
			MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_STATUS_NOT_AVAILABLE;
			exceptionMessage.setErrorMessage(e.getMessage());
			exceptionMessage.setDeveloperMessage(e.getMessage());
			throw new MUXException(exceptionMessage);
		}
	}

    @Override
    public StreamKeyAndRtmpUrlDto createStreamKey(Event event, String aeObjectId, CaptionsDto captions) {
        EventLiveStreamingConfiguration settings = eventLiveStreamingConfigurationService.findByEventId(event.getEventId());
        if (null != settings) {
            return createStreamKey(settings.getWatermarkLogoUrl(), aeObjectId, settings.isLowLatency(), event, captions);
        }
        return createStreamKey(null, aeObjectId, false, event, captions);
    }

	@Override
    public StreamKeyAndRtmpUrlDto createStreamKey(String watermarkUrl, String aeObjectId, boolean lowLatency, Event event, CaptionsDto captions) {
        StreamKeyAndRtmpUrlDto streamKeyAndRtmpUrlDto = new StreamKeyAndRtmpUrlDto();
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .post(MUX_STREAM_URL + "/video/v1/live-streams")
                    .headers(getHeaders())
                    .body(getBody(watermarkUrl, aeObjectId, lowLatency, captions))
                    .asJson();
            JSONObject returnObject = jsonResponse.getBody().getObject();
            if (null != returnObject && returnObject.has("data")) {
                JSONObject data = (JSONObject) returnObject.get("data");
                JSONArray jsonArray = (JSONArray) data.get(Constants.PLAYBACK_IDS_STRING);
                String playBackId = MuxUtils.getSignedPlayBackId(jsonArray);
                String liveStreamId = data.getString("id");
                String streamKey = data.getString("stream_key");
                streamKeyAndRtmpUrlDto.setStreamKey(streamKey);
                streamKeyAndRtmpUrlDto.setStreamProvider(ACCELEVENTS);
                streamKeyAndRtmpUrlDto.setStreamUrl(playBackId);
                streamKeyAndRtmpUrlDto.setRtmpUrl(lowLatency ? "rtmp://ll-beta.live.mux.com:5222/app" : "rtmp://live.mux.com/app");
                streamKeyAndRtmpUrlDto.setMetaData(data.toString());
                streamKeyAndRtmpUrlDto.setLiveStreamId(liveStreamId);
                MuxJwtDto muxJwtDto = this.generateDomainPlayBackRestrictionToken(playBackId, event);
                streamKeyAndRtmpUrlDto.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
                streamKeyAndRtmpUrlDto.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());

                log.info("create stream {} ", data);
            }
        } catch (Exception e) {
            log.info("Exception while creating streams key {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_CAN_NOT_CREATE;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
        return streamKeyAndRtmpUrlDto;
    }



//	public String getBody(String watermarkUrl, String aeObjectId, boolean lowLatency, CaptionsDto captions) {
//        StringBuilder body = new StringBuilder("{\n");
//		if(lowLatency){
//            body.append("\"latency_mode\": \"low\",");
//        }else{
//             body.append("\"reconnect_window\": 90,\n") ;
//        }
//        body.append("\"passthrough\": \""+aeObjectId+"\",\n" +
//                "  \"playback_policy\": [\n" +
//                "    \"signed\",\n" +
//                "    \"signed\",\n" +
//                "  ],\n");
//        if (!lowLatency) {
//            if (captions.isGeneratedSubtitles()) {
//                body.append("\"generated_subtitles\": [\n" +
//                        "    {\n" +
//                        "      \"name\": \"English CC (auto)\",\n" +
//                        "      \"passthrough\": \"English closed captions (auto-generated)\",\n" +
//                        "      \"language_code\": \"en-US\"\n" +
//                        "    }\n" +
//                        "  ],");
//            } else if (captions.isEmbeddedSubtitles()) {
//                body.append("  \"embedded_subtitles\": [\n" +
//                        "       {\n" +
//                        "           \"name\": \"English CC\",\n" +
//                        "           \"passthrough\": \"English closed captions\",\n" +
//                        "           \"language_code\": \"en-US\",\n" +
//                        "           \"language_channel\" : \"cc1\"" +
//                        "       }\n" +
//                        "   ],\n");
//            }
//        }
//        body.append("\"new_asset_settings\": {\n" +
//                "    \"playback_policy\": [\n" +
//                "      \"signed\"\n" +
//                "    ]");
//        if (StringUtils.isNotBlank(watermarkUrl)) {
//            body.append(",    \"\n input\": [\n" +
//                    "      {\n" +
//                    "        \"url\": \"" + watermarkUrl + "\",\n" +
//                    "        \"overlay_settings\": {\n" +
//                    "        \"vertical_align\": \"bottom\",\n" +
//                    "        \"vertical_margin\": \"2%\",\n" +
//                    "        \"horizontal_align\": \"right\",\n" +
//                    "        \"horizontal_margin\": \"2%\",\n" +
////							"        \"width\": \"10%\",\n" +
//                    "        \"height\": \"10%\",\n" +
//                    "        \"opacity\": \"100%\"\n" +
//                    "      }\n" +
//                    "      }\n" +
//                    "    ]\n")  ;
//		}
//
//		body.append("  }\n" + "}")  ;
//		return body.toString();
//	}

    public String getBody(String watermarkUrl, String aeObjectId, boolean lowLatency, CaptionsDto captions) {
        JsonObjectBuilder bodyBuilder = Json.createObjectBuilder();

        if (lowLatency) {
            bodyBuilder.add("latency_mode", "low");
        }
        bodyBuilder.add("reconnect_window", 90);


        bodyBuilder.add("passthrough", aeObjectId)
                .add("playback_policy", Json.createArrayBuilder().add("signed").add("public"));

        if (!lowLatency) {
            if (captions.isGeneratedSubtitles()) {
                JsonArrayBuilder generatedSubtitlesArray = Json.createArrayBuilder()
                        .add(Json.createObjectBuilder()
                                .add("name", "English CC (auto)")
                                .add("passthrough", "English closed captions (auto-generated)")
                                .add("language_code", "en-US"));
                bodyBuilder.add("generated_subtitles", generatedSubtitlesArray);
            } else if (captions.isEmbeddedSubtitles()) {
                JsonArrayBuilder embeddedSubtitlesArray = Json.createArrayBuilder()
                        .add(Json.createObjectBuilder()
                                .add("name", "English CC")
                                .add("passthrough", "English closed captions")
                                .add("language_code", "en-US")
                                .add("language_channel", "cc1"));
                bodyBuilder.add("embedded_subtitles", embeddedSubtitlesArray);
            }
        }

        JsonObjectBuilder newAssetSettingsBuilder = Json.createObjectBuilder()
                .add("playback_policy", Json.createArrayBuilder().add("signed").add("public"));

        if (StringUtils.isNotBlank(watermarkUrl )) {
            JsonArrayBuilder inputArray = Json.createArrayBuilder()
                    .add(Json.createObjectBuilder()
                            .add("url", watermarkUrl)
                            .add("overlay_settings", Json.createObjectBuilder()
                                    .add("vertical_align", "bottom")
                                    .add("vertical_margin", "2%")
                                    .add("horizontal_align", "right")
                                    .add("horizontal_margin", "2%")
                                    .add("height", "10%")
                                    .add("opacity", "100%")));

            newAssetSettingsBuilder.add("input", inputArray);
        }

        bodyBuilder.add("new_asset_settings", newAssetSettingsBuilder);

        JsonObject bodyJsonObject = bodyBuilder.build();
        return bodyJsonObject.toString();
    }

	public Map<String,String> getHeaders() {
		Map<String,String> map = new HashMap<>();
		map.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
		map.put(Constants.Authorization, authorization);
		return map;
	}

	@Override
	public MuxDirectUploadIdAndUrlDto createDirectUploadUrl(boolean isDownloadable, String metaData) {
		MuxDirectUploadIdAndUrlDto muxDirectUploadIdAndUrlDto = new MuxDirectUploadIdAndUrlDto();
		try {
			HashMap<String,String> map = new HashMap<>();
			map.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
			map.put(Constants.Authorization, authorization);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("cors_origin", uiBaseurl);
            jsonObject.put("playback_policy", new JSONArray().put("signed").put("public"));
            jsonObject.put("new_asset_settings", new JSONObject().put("playback_policy", new JSONArray().put("signed").put("public")).put("mp4_support", isDownloadable ? "standard" : "none").put("passthrough", metaData));
            jsonObject.put("passthrough", metaData);


			HttpResponse<JsonNode> jsonResponse = Unirest
					.post(MUX_STREAM_URL+"/video/v1/uploads")
					.headers(map)
					.body(jsonObject.toString())
					.asJson();
				JSONObject data = (JSONObject) jsonResponse.getBody().getObject().get("data");
				muxDirectUploadIdAndUrlDto.setDirectUploadId((String) data.get("id"));
				muxDirectUploadIdAndUrlDto.setDirectUploadUrl((String) data.get("url"));

            log.info("Direct upload URL and ID are generated {}" , muxDirectUploadIdAndUrlDto);
				return muxDirectUploadIdAndUrlDto;

		} catch (Exception e) {
			log.info("Exception while generating direct upload URL and ID {}" , e.getMessage(), e);
			MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_CAN_NOT_CREATE_UPLOAD_URL;
			exceptionMessage.setErrorMessage(e.getMessage());
			exceptionMessage.setDeveloperMessage(e.getMessage());
			throw new MUXException(exceptionMessage);
		}
	}

	@Override
	public String getAssetIdByDirectUploadId(String uploadId) {
		if(StringUtils.isNotBlank(uploadId)) {
			sleepForThreeSeconds();
			try {
				Map<String,String> map = getHeaders();
				HttpResponse<JsonNode> jsonResponse = Unirest
						.get(MUX_STREAM_URL + "/video/v1/uploads/" + uploadId)
						.headers(map)
						.asJson();

					JSONObject data = (JSONObject) jsonResponse.getBody().getObject().get("data");
					return data.get("asset_id").toString();

			} catch (Exception e) {
				log.info("Exception while getting uploaded video assetId {}" , e.getMessage(), e);
				MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_ASSET_ID_NOT_AVAILABLE;
				exceptionMessage.setErrorMessage(e.getMessage());
				exceptionMessage.setDeveloperMessage(e.getMessage());
				throw new MUXException(exceptionMessage);
			}
		}
		return Constants.STRING_EMPTY;
	}

	@Override
	public String getPlayBackIdByAssetId(String assetId) {
		if(StringUtils.isNotBlank(assetId)) {
			try {
				Map<String,String> map = getHeaders();
				HttpResponse<JsonNode> jsonResponse = Unirest
						.get(MUX_STREAM_URL + MUX_ASSET_ID_PATH + assetId)
						.headers(map)
						.asJson();

					JSONObject data = (JSONObject) jsonResponse.getBody().getObject().get("data");
					JSONArray jsonArray = (JSONArray) data.get(Constants.PLAYBACK_IDS_STRING);
					return MuxUtils.getSignedPlayBackId(jsonArray);

			} catch (Exception e) {
				log.info("Exception while getting uploaded video playback id {}" , e.getMessage(), e);
				MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_PLAYBACK_ID_NOT_AVAILABLE;
				exceptionMessage.setErrorMessage(e.getMessage());
				exceptionMessage.setDeveloperMessage(e.getMessage());
				throw new MUXException(exceptionMessage);
			}
		}
		return Constants.STRING_EMPTY;
	}

    @Override
    public JSONObject getAssetDetails(String assetId) {
        if(StringUtils.isNotBlank(assetId)) {
            try {
                Map<String,String> map = getHeaders();
                HttpResponse<JsonNode> jsonResponse = Unirest
                        .get(MUX_STREAM_URL + MUX_ASSET_ID_PATH + assetId)
                        .headers(map)
                        .asJson();
                JSONObject returnObject = jsonResponse.getBody().getObject();
                if (null != returnObject && returnObject.has("data")) {
                    JSONObject response = returnObject.getJSONObject("data");
                    log.info("getAssetDetails response  {}", response);
                    return response;
                }
            } catch (Exception e) {
                log.info("Exception while getting asset details {}" , e.getMessage(), e);
                MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_ASSET_ID_NOT_AVAILABLE;
                exceptionMessage.setErrorMessage(e.getMessage());
                exceptionMessage.setDeveloperMessage(e.getMessage());
                throw new MUXException(exceptionMessage);
            }
        }
        return null;
    }


    @Override
    public JSONObject getLiveStreamDetails(String liveStreamId) {
        if(StringUtils.isNotBlank(liveStreamId)) {
            try {
                Map<String,String> map = getHeaders();
                HttpResponse<JsonNode> jsonResponse = Unirest
                        .get(MUX_STREAM_URL + MUX_LIVE_STREAM_ID_PATH + liveStreamId)
                        .headers(map)
                        .asJson();
                JSONObject returnObject = jsonResponse.getBody().getObject();
                if (null != returnObject && returnObject.has("data")) {
                    JSONObject response = returnObject.getJSONObject("data");
                    log.info("getLiveStreamDetails response  {}", response);
                    return response;
                }
            } catch (Exception e) {
                log.info("Exception while getting live stream details {}" , e.getMessage(), e);
                MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_NOT_AVAILABLE;
                exceptionMessage.setErrorMessage(e.getMessage());
                exceptionMessage.setDeveloperMessage(e.getMessage());
                throw new MUXException(exceptionMessage);
            }
        }
        return null;
    }

	@Override
	public void enableMP4SupportForDownload(String assetId, int maxRetryCount) {
		if (StringUtils.isNotBlank(assetId)) {
			try {
				Map<String,String> map = getHeaders();
				HttpResponse<JsonNode> jsonResponse = Unirest
						.put(MUX_STREAM_URL + MUX_ASSET_ID_PATH + assetId + "/mp4-support")
						.headers(map)
						.body("{ \"mp4_support\": \"standard\" }")
						.asJson();
				if (jsonResponse.getStatus() >= 200 && jsonResponse.getStatus() <= 209) {
					log.info("MP4 support is now enabled for asset id {}", assetId);
				} else if (jsonResponse.getStatus() == 500 && maxRetryCount > 0){
					log.error("MUX internal server error while enabling MP4 support for asset id {} reason {}", assetId, jsonResponse.getBody());
					sleepForThreeSeconds();
					enableMP4SupportForDownload(assetId, maxRetryCount-1);
				} else if(jsonResponse.getStatus()==400) {
					log.info("Failed to enable MP4 support for asset id {} reason {}", assetId, jsonResponse.getBody());
                    JSONObject errorMessage = (JSONObject) jsonResponse.getBody().getObject().get(Constants.ERROR);
                    JSONArray jsonArray = (JSONArray) errorMessage.get(Constants.MESSAGES);
                    String muxApiMessage = (String) jsonArray.get(0);
                    MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.FAILED_TO_ENABLE_MP4_SUPPORT_FOR_MUX;
                    exceptionMessage.setErrorMessage(muxApiMessage);
                    exceptionMessage.setDeveloperMessage(muxApiMessage);
                    throw new MUXException(exceptionMessage);
				}else {
                    log.info("Failed to enable MP4 support for asset id {} reason {}", assetId, jsonResponse.getBody());
                }
			} catch (Exception e) {
				log.info("Exception while enabling MP4 support for asset id {} reason {}" , assetId, e.getMessage(), e);
				MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.FAILED_TO_ENABLE_MP4_SUPPORT_FOR_MUX;
				exceptionMessage.setErrorMessage(e.getMessage());
				exceptionMessage.setDeveloperMessage(e.getMessage());
				throw new MUXException(exceptionMessage);
			}
		}
	}

	@Override
	public double getDurationByAssetId(String assetId) {
		if(StringUtils.isNotBlank(assetId)) {
			sleepForThreeSeconds();
			try {
				Map<String,String> map = getHeaders();
				HttpResponse<JsonNode> jsonResponse = Unirest
						.get(MUX_STREAM_URL + MUX_ASSET_ID_PATH + assetId)
						.headers(map)
						.asJson();

					JSONObject data = (JSONObject) jsonResponse.getBody().getObject().get("data");
					if (data.has(DURATION)){
                        log.info("MuxServiceImpl|getDurationByAssetId|assetId| {} |data| {}" , assetId ,data);
                        log.info("Uploaded video duration {}" , data.getDouble(DURATION));
					    return data.getDouble(DURATION);
					}else {
					    return -1d;
                    }

			} catch (Exception e) {
				log.info("Exception while getting uploaded video duration {}" , e.getMessage(), e);
				MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_DURATION_NOT_AVAILABLE;
				exceptionMessage.setErrorMessage(e.getMessage());
				exceptionMessage.setDeveloperMessage(e.getMessage());
				throw new MUXException(exceptionMessage);
			}
		}
		return 0;
	}

	public void sleepForThreeSeconds() {
		try {
			Thread.sleep(3000);
		} catch (InterruptedException e) {
			e.printStackTrace();
			Thread.currentThread().interrupt();
		}
	}

    public URL getS3URL(){
        try {
            return new URL(String.format(aeMuxAssetsCloudFrontUrl, aeMuxAssetsBucket));
        } catch (MalformedURLException e) {
            log.warn(e.getMessage());
            return null;
        }
    }

    @Override
    public void addMuxSubTitleAndCaption(MUXLivestreamAssetDetails muxLivestreamAssetDetails, String subTitleFile, String sessionName, String captionFileName, Long sessionId, String languageCode) {

        String file = new StringBuilder(getS3URL().toString()).append(subTitleFile).toString();
        String assetId = muxLivestreamAssetDetails.getAssetId();
        try {
            Map map = new HashMap();
            map.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
            map.put(Constants.Authorization, authorization);

            //Check asset status is it ready or not
            JSONObject assetDetailsJson = getAssetDetails(assetId);

            if (assetDetailsJson != null){
               String status = assetDetailsJson.has(Constants.STATUS) ? assetDetailsJson.get(Constants.STATUS).toString() : null;
                if(!Constants.MUX_ASSET_STATUS_READY.equalsIgnoreCase(status))
                {
                    MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_PREPARING_VIDEO_ASSET;
                    exceptionMessage.setErrorMessage(exceptionMessage.getErrorMessage());
                    exceptionMessage.setDeveloperMessage(exceptionMessage.getErrorMessage());
                    throw new MUXException(exceptionMessage);
                }
            }

            Matcher matcher = pattern.matcher(sessionName);
            String languageRegionName = MuxSupportLanguageCode.getLanguageRegionNameByTag(languageCode);
            String captionName = matcher.replaceAll(Constants.STRING_EMPTY).concat(Constants.STRING_UNDERSCORE).concat(languageRegionName);
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .post(MUX_STREAM_URL + MUX_ASSET_ID_PATH + assetId + "/tracks")
                    .headers(map)
                    .body("{ \"url\": \""+file+"\", \"type\": \"text\", " +
                            "\"text_type\":  \"subtitles\", \"closed_captions\" :false," +
                            "\"language_code\":\""+languageCode+"\",\"name\":\""+captionName+"\" }")
                    .asJson();

            if(jsonResponse.getStatus()==400) {
                log.info("MUX internal server error while enabling MP4 support for asset id {} reason {}", assetId, jsonResponse.getBody());
                JSONObject errorMessage = (JSONObject) jsonResponse.getBody().getObject().get(Constants.ERROR);
                JSONArray jsonArray = (JSONArray) errorMessage.get(Constants.MESSAGES);
                String muxApiMessage = (String) jsonArray.get(0);
                MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_SUBTITLE_FILE_CAN_NOT_BE_ADDED;
                exceptionMessage.setErrorMessage(muxApiMessage);
                exceptionMessage.setDeveloperMessage(muxApiMessage);
                throw new MUXException(exceptionMessage);

            }
            if(jsonResponse.getStatus()==201) {
                JSONObject data = (JSONObject) jsonResponse.getBody().getObject().get("data");
                String subtitleTrackId = data.getString("id");
                SessionSubtitle sessionSubtitle = new SessionSubtitle();
                sessionSubtitle.setMuxId(muxLivestreamAssetDetails.getId());
                sessionSubtitle.setSubTitleFileUrl(captionFileName);
                sessionSubtitle.setMuxSubtitleTrackId(subtitleTrackId);
                sessionSubtitle.setSessionId(muxLivestreamAssetDetails.getSessionId());
                sessionSubtitle.setLanguageCode(languageCode);
                sessionSubtitle.setCreatedAt(new Date());
                sessionSubtitleService.save(sessionSubtitle);
                log.info("file added successFully");
                log.info(subtitleTrackId);
            }

        } catch (Exception e) {
            log.info("Exception while added subtitle file. " + e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_SUBTITLE_FILE_CAN_NOT_BE_ADDED;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    @Override
    public void removeMuxSubTitleAndCaption(String assetId,SessionSubtitle sessionSubtitle){
        try {
            Map map = new HashMap();
            map.put(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON);
            map.put(Constants.Authorization, authorization);
            String trackId = sessionSubtitle.getMuxSubtitleTrackId();

            if (!StringUtils.isBlank(trackId)) {
                HttpResponse<JsonNode> jsonResponceDeleteTrack = Unirest
                        .delete(MUX_STREAM_URL + MUX_ASSET_ID_PATH + assetId + "/tracks/" + trackId)
                        .headers(map)
                        .asJson();
                if (jsonResponceDeleteTrack.getStatus() == 204 || jsonResponceDeleteTrack.getStatus() == 404) {
                    log.info("Deleted trackId : {} successfully", trackId);
                    sessionSubtitleService.deleteById(sessionSubtitle.getId());
                }
            } else {
                log.info("No trackId found for this assetId {}", assetId);
                sessionSubtitleService.deleteById(sessionSubtitle.getId());
            }
        } catch (Exception ex){
            log.info("Exception while added subtitle file. " + ex.getMessage(), ex);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_SUBTITLE_FILE_CAN_NOT_BE_REMOVED;
            exceptionMessage.setErrorMessage(ex.getMessage());
            exceptionMessage.setDeveloperMessage(ex.getMessage());
            throw new MUXException(exceptionMessage);
        }

	}


    @Override
    public void makeReEnabledLiveStream(String liveStreamId){
        String status = checkStreamStatus(liveStreamId);
        if (Constants.MUX_LIVE_STREAM_STATUS_DISABLED.equalsIgnoreCase(status)) {
            try {
                log.info("in makeReEnabledLiveStream ", liveStreamId);
                HttpResponse<JsonNode> jsonResponse = Unirest
                        .put(MUX_STREAM_URL + Constants.MUX_LIVE_STREAMS_VIDEO_V1_PATH + liveStreamId + "/enable")
                        .headers(getHeaders())
                        .asJson();

                if (jsonResponse.getStatus() >= 200
                        && jsonResponse.getStatus() <= 209) {
                    log.info("Session re enable is success now session can start again", liveStreamId);
                    return;
                }
               else if(jsonResponse.getStatus()==400) {
                    log.info("MUX internal server error while re enable reason {} liveStreamId", liveStreamId, jsonResponse.getBody());
                    JSONObject errorMessage = (JSONObject) jsonResponse.getBody().getObject().get(Constants.ERROR);
                    JSONArray jsonArray = (JSONArray) errorMessage.get(Constants.MESSAGES);
                    String muxApiMessage = (String) jsonArray.get(0);
                    MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_NOT_RE_ENABLED;
                    exceptionMessage.setErrorMessage(muxApiMessage);
                    exceptionMessage.setDeveloperMessage(muxApiMessage);
                    throw new MUXException(exceptionMessage);

                }
            } catch(Exception e){
                log.info("Error conf mux to enable with livestreamId {}", liveStreamId);
                MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_NOT_RE_ENABLED;
                exceptionMessage.setErrorMessage(e.getMessage());
                exceptionMessage.setDeveloperMessage(e.getMessage());
                throw new MUXException(exceptionMessage);
            }
        }
    }

    @Override
    public SigningKeyAndPrivateKeyDto createURLSigningKey() {
        SigningKeyAndPrivateKeyDto signingKeyAndPrivateKeyDto = new SigningKeyAndPrivateKeyDto();
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .post(MUX_STREAM_URL + "/video/v1/signing-keys")
                    .headers(getHeaders())
                    .asJson();
            log.info("createURLSigningKey response  {}", jsonResponse);
            if (jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <= 209) {
                JSONObject jsonObject = jsonResponse.getBody().getObject();
                JSONObject data = jsonObject.getJSONObject(Constants.DATA);
                signingKeyAndPrivateKeyDto.setId(data.getString(Constants.ID));
                signingKeyAndPrivateKeyDto.setPrivateKey(data.getString(Constants.PRIVATE_KEY));
            }
        } catch (Exception e) {
            log.info("Exception while creating URL signing key {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_CREATE_URL_SINGING_KEY;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
        return signingKeyAndPrivateKeyDto;
    }

    @Override
    public MuxJwtDto generateDomainPlayBackRestrictionToken(String playBackID, Event event){
//        WhiteLabel whiteLabel = event.getWhiteLabel();
//        if(null != whiteLabel){
//            String playbackRestrictionId = whiteLabel.getPlayBackRestrictionId();
//            return  createJWTAndSign(playBackID, StringUtils.isNotBlank(playbackRestrictionId) ? playbackRestrictionId : playback_restriction_id);
//        }
        return createJWTAndSign(playBackID, playback_restriction_id);
    }

    @Override
    public MuxJwtDto generateDomainPlayBackRestrictionToken(String playBackID, Long eventId){
//        Event event = eventService.getEventById(eventId);
//        return generateDomainPlayBackRestrictionToken(playBackID, event);
        return createJWTAndSign(playBackID, playback_restriction_id);
    }

    private MuxJwtDto createJWTAndSign(String playBackID, String playbackRestrictionId) {
        try {
            MuxJwtDto muxJwtDto = new MuxJwtDto();
            final long defaultExpireTime = System.currentTimeMillis() / 1000L
                    + TimeUnit.DAYS.toSeconds(2555);
            Map<String, Object> claims = new HashMap<>();
            claims.put("sub", playBackID);
            claims.put("exp", defaultExpireTime);
            claims.put("aud", "v");
            claims.put("default_subtitles_lang", "en");
            claims.put("redundant_streams", true);
            claims.put(Constants.PLAYBACK_RESTRICTION_ID, playbackRestrictionId);
            Map<String, Object> header = new HashMap<>();
            header.put(Constants.SIGNING_KEY, signing_key);

            String playBackRestriction = Jwts.builder()
                    .setHeader(header)
                    .setClaims(claims)
                    .signWith(SignatureAlgorithm.RS256, getPrivateKey(privateKeyFileName))
                    .compact();
            muxJwtDto.setPlayBackRestrictionToken(playBackRestriction);

            claims.put("aud", "t");
            String thumbnailRestriction = Jwts.builder()
                    .setHeader(header)
                    .setClaims(claims)
                    .signWith(SignatureAlgorithm.RS256, getPrivateKey(privateKeyFileName))
                    .compact();
            muxJwtDto.setThumbnailRestrictionToken(thumbnailRestriction);
            return  muxJwtDto;

        } catch (Exception e) {
            log.info("MuxServiceImpl createJWTAndSign Exception while creating JWT token {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_FILED_TO_GENERATE_TOKEN;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    private PrivateKey getPrivateKey(String fileName) {
        ClassPathResource cpr = new ClassPathResource(fileName);
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] xmlBytes = readString(cpr.getInputStream()).getBytes(StandardCharsets.UTF_8);
            String xmlBase64 = Base64.getEncoder().encodeToString(xmlBytes);
            // Decoding string
            String privateKeyDecoded = new String(decoder.decode(xmlBase64));
            String privKeyPEM = privateKeyDecoded.replace(
                    "-----BEGIN RSA PRIVATE KEY-----", "")
                    .replace("-----END RSA PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");
            byte[] pkcs8EncodedBytes = decoder.decode(privKeyPEM);
            // extract the private key
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pkcs8EncodedBytes);
            KeyFactory kf = null;
            kf = KeyFactory.getInstance("RSA");
            PrivateKey privKey = null;
            privKey = kf.generatePrivate(keySpec);
            return privKey;
        } catch (Exception e) {
            log.info("MuxServiceImpl getPrivateKey Exception while creating JWT token {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_FILED_TO_GENERATE_TOKEN;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    String readString(InputStream inputStream) throws IOException {
        ByteArrayOutputStream into = new ByteArrayOutputStream();
        byte[] buf = new byte[4096];
        for (int n; 0 < (n = inputStream.read(buf));) {
            into.write(buf, 0, n);
        }
        into.close();
        return new String(into.toByteArray(), "UTF-8"); // Or whatever encoding
    }

    @Override
    public String createDomainPlayBackRestriction(List<String> whiteLabelDomains) {
        List<String> listOfDomains = new ArrayList<>();
        if(!whiteLabelDomains.contains(domainName)){
            whiteLabelDomains.add(domainName);
        }
        if (!CollectionUtils.isEmpty(whiteLabelDomains)) {
            for (String whiteLabelDomain : whiteLabelDomains) {
                listOfDomains.add(whiteLabelDomain.replace("http://", "").replace("https://", "").replace("/", ""));
            }
        }
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .post(MUX_STREAM_URL + "/video/v1/playback-restrictions")
                    .headers(getHeaders())
                    .body("{\n" +
                            "  \"referrer\": {\n" +
                            "    \"allowed_domains\" : " + JsonMapper.convertToString(listOfDomains) + ",\n" +
                            "    \"allow_no_referrer\" : true\n" +
                            "  }\n" +
                            "}  ")
                    .asJson();
            log.info("createPlayBackRestriction response  {}", jsonResponse);
            if (jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <= 209) {
                JSONObject jsonObject = jsonResponse.getBody().getObject();
                JSONObject data = jsonObject.getJSONObject(Constants.DATA);
                return data.getString(Constants.ID);
            }
            return null;
        } catch (Exception e) {
            log.info("Exception while creating Domain PlayBack Restriction {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_CREATE_URL_SINGING_KEY;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    @Override
    public List<WLBasicDetailsDto> getDomainPlayBackRestrictionList(List<WLBasicDetailsDto> whiteLabelBasicDetailsDto) {
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .get(MUX_STREAM_URL + "/video/v1/playback-restrictions")
                    .headers(getHeaders())
                    .asJson();

            log.info("getDomainPlayBackRestrictionList response  {}", jsonResponse);
            if (jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <= 209) {
                JSONObject jsonObject = jsonResponse.getBody().getObject();
                JSONArray data = jsonObject.getJSONArray(Constants.DATA);
                if (data.length() > 0) {
                    for (WLBasicDetailsDto wLBasicDetailsDto : whiteLabelBasicDetailsDto) {
                        String playbackRestrictionID = wLBasicDetailsDto.getPlaybackRestrictionID();
                        if(StringUtils.isNotBlank(playbackRestrictionID)){
                            for (int i = 0; i < data.length(); i++) {
                                JSONObject row = data.getJSONObject(i);
                                if (row.getString("id").equalsIgnoreCase(playbackRestrictionID)) {
                                    JSONObject referrer = row.getJSONObject(Constants.REFERRER);
                                    JSONArray allowedDomains = referrer.getJSONArray(Constants.ALLOWED_DOMAINS);
                                    if (allowedDomains.length() > 0) {
                                        wLBasicDetailsDto.setAllowedDomains(convertJsonArrayToList(allowedDomains));
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return  whiteLabelBasicDetailsDto;
        } catch (Exception e) {
            log.info("Exception while get Domain PlayBack Restriction List {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_CREATE_URL_SINGING_KEY;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    @Override
    public List<String> getDomainDefaultPlayBackRestrictionById() {
        return this.getDomainPlayBackRestrictionById(playback_restriction_id);
    }

    @Override
    public List<String> getDomainPlayBackRestrictionById(String playbackRestrictionId) {
        if(StringUtils.isBlank(playbackRestrictionId)){
            return Collections.EMPTY_LIST;
        }
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .get(MUX_STREAM_URL + "/video/v1/playback-restrictions/"+ playbackRestrictionId)
                    .headers(getHeaders())
                    .asJson();

            log.info("getDomainPlayBackRestrictionList response  {}", jsonResponse);
            if (jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <= 209) {
                JSONObject jsonObject = jsonResponse.getBody().getObject();
                JSONObject data = jsonObject.getJSONObject(Constants.DATA);
                    JSONObject referrer = data.getJSONObject(Constants.REFERRER);
                    JSONArray allowedDomains = referrer.getJSONArray(Constants.ALLOWED_DOMAINS);
                    if (allowedDomains.length() > 0) {
                        return convertJsonArrayToList(allowedDomains);
                    }
            }
        } catch (Exception e) {
            log.info("Exception while get Domain PlayBack Restriction List {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_CREATE_URL_SINGING_KEY;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
        return Collections.emptyList();
    }


    @Override
    public void updateDomainDefaultPlayBackRestrictionList(List<String> allowedDomains) {
        this.updateDomainPlayBackRestrictionList(allowedDomains, playback_restriction_id);
    }

    @Override
    public void updateDomainPlayBackRestrictionList(List<String> allowedDomains, String playbackRestrictionId) {
        List<String> domains = new ArrayList<>();
        if(StringUtils.isBlank(playbackRestrictionId)){
            return;
        }
        if (!CollectionUtils.isEmpty(allowedDomains)) {
            if(!allowedDomains.contains(domainName)){
                allowedDomains.add(domainName);
            }
            for (String allowedDomain : allowedDomains) {
                domains.add(allowedDomain.replace("http://", "").replace("https://", "").replace("/", ""));
            }
        }
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .put(MUX_STREAM_URL + "/video/v1/playback-restrictions/" + playbackRestrictionId + "/referrer")
                    .headers(getHeaders())
                    .body("{\n" +
                            "  \"allowed_domains\": "+JsonMapper.convertToString(domains) +",\n" +
                            "  \"allow_no_referrer\": true\n" +
                            "}  ")
                    .asJson();
            log.info("updateDomainPlayBackRestrictionList response  {}", jsonResponse);
            if (jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <= 209) {
                JSONObject jsonObject = jsonResponse.getBody().getObject();
                JSONObject data = jsonObject.getJSONObject(Constants.DATA);
                log.info("updateDomainPlayBackRestrictionList PlayBackRestrictionID {}", data.getString(Constants.ID));
            }
        } catch (Exception e) {
            log.info("Exception while creating Domain PlayBack Restriction {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_CREATE_URL_SINGING_KEY;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    private List<String> convertJsonArrayToList(JSONArray allowedDomains) throws JSONException {
        List<String> listdata = new ArrayList<>();
        if (allowedDomains != null) {
            for (int i = 0; i < allowedDomains.length(); i++) {
                listdata.add(allowedDomains.getString(i));
            }
        }
        return listdata;
    }

    @Override
    public JSONObject getResponseByAssetId(String assetId) {
        if(StringUtils.isNotBlank(assetId)) {
            try {
                Map<String,String> map = getHeaders();
                HttpResponse<JsonNode> jsonResponse = Unirest
                        .get(MUX_STREAM_URL + MUX_ASSET_ID_PATH + assetId)
                        .headers(map)
                        .asJson();
                JSONObject data = jsonResponse.getBody().getObject();
                log.info("getAssetDetails response  {}" , data);
                return data;
            } catch (Exception e) {
                log.info("Exception while getting asset details {}" , e.getMessage(), e);
                MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_ASSET_ID_NOT_AVAILABLE;
                exceptionMessage.setErrorMessage(e.getMessage());
                exceptionMessage.setDeveloperMessage(e.getMessage());
                throw new MUXException(exceptionMessage);
            }
        }
        return null;
    }

    @Override
    public void updateLiveCaptionForExistingStreams(String liveStreamId, CaptionsDto captionsDto, Event event) {
        EventLiveStreamingConfiguration settings = eventLiveStreamingConfigurationService.findByEventId(event.getEventId());
        if (null != liveStreamId) {
            String status = this.checkStreamStatus(liveStreamId);
            if (Constants.ACTIVE.equals(status)) {
                throw new NotAcceptableException(CAN_NOT_ENABLE_LIVE_CAPTIONING_IN_LIVE_STREAMING);
            }
        }
        if ((captionsDto.isEmbeddedSubtitles() || captionsDto.isGeneratedSubtitles()) && null != settings && settings.isLowLatency()) {
            throw new NotAcceptableException(LIVE_CAPTIONS_DOES_NOT_SUPPORT_LOW_LATENCY);
        }
        if (captionsDto.isGeneratedSubtitles()) {
            updateAutoCaptioning(liveStreamId, true);
            updateClosedCaptioning(liveStreamId, false);
        } else if (captionsDto.isEmbeddedSubtitles()) {
            updateAutoCaptioning(liveStreamId, false);
            updateClosedCaptioning(liveStreamId, true);
        } else {
            updateAutoCaptioning(liveStreamId, false);
            updateClosedCaptioning(liveStreamId, false);
        }
    }

    private void updateClosedCaptioning(String liveStreamId, boolean autoGenerateLiveCaptions) {
        try {
            Unirest.put(MUX_STREAM_URL + Constants.MUX_LIVE_STREAMS_VIDEO_V1_PATH + liveStreamId + "/embedded-subtitles")
                    .headers(getHeaders())
                    .body(getBodyForAPIClosedCaptioning(autoGenerateLiveCaptions))
                    .asJson();
        } catch (Exception e) {
            log.info("Exception while update close captioning status {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_STATUS_NOT_AVAILABLE;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    private String getBodyForAPIClosedCaptioning(boolean enableCaptioning) {
        StringBuilder body = new StringBuilder("{\n");
        if (enableCaptioning) {
            body.append("\"embedded_subtitles\": [\n" +
                    "    {\n" +
                    "      \"name\": \"en-US\",\n" +
                    "      \"language_code\": \"en-US\",\n" +
                    "      \"language_channel\": \"cc1\"\n" +
                    "    }\n" +
                    "  ]");
        } else {
            body.append("\"embedded_subtitles\" : []");
        }
        body.append("}\n");
        return body.toString();
    }

    private void updateAutoCaptioning(String liveStreamId, boolean autoGenerateLiveCaptions) {
        try {
            Unirest.put(MUX_STREAM_URL + Constants.MUX_LIVE_STREAMS_VIDEO_V1_PATH + liveStreamId + "/generated-subtitles")
                    .headers(getHeaders())
                    .body(getBodyForAPILiveCaptioning(autoGenerateLiveCaptions))
                    .asJson();
        } catch (Exception e) {
            log.info("Exception while update auto captioning status {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_STATUS_NOT_AVAILABLE;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    private String getBodyForAPILiveCaptioning(boolean enableCaptioning) {
        StringBuilder body = new StringBuilder("{\n");
        if (enableCaptioning) {
            body.append("\"generated_subtitles\": [\n" +
                    "    {\n" +
                    "      \"name\": \"English CC (auto)\",\n" +
                    "      \"passthrough\": \"{\\\"description\\\": \\\"English closed captions (auto-generated)\\\"}\",\n" +
                    "      \"language_code\": \"en-US\"\n" +
                    "    }\n" +
                    "  ]");
        } else {
            body.append("\"generated_subtitles\" : []");
        }
        body.append("}\n");
        return body.toString();
    }

    @Override
    public Map<String, String> getMuxAssetFileNameAndFileSize(JSONObject jsonBody) {
        try {
            Map<String, String> fileData = new HashMap<>();
            if (jsonBody != null && jsonBody.has(Constants.STATIC_RENDITIONS)) {
                JSONObject staticRenditions = jsonBody.getJSONObject(Constants.STATIC_RENDITIONS);
                log.info("Get file name from static renditions {}" , staticRenditions);
                if (staticRenditions.has(Constants.FILES)) {
                    JSONArray filesList = staticRenditions.getJSONArray(Constants.FILES);
                    List<String> fileNameList = new ArrayList<>();
                    for (int i = 0; i < filesList.length(); i++) {
                        JSONObject fileObject = filesList.getJSONObject(i);
                        fileNameList.add(fileObject.getString(Constants.NAME_SMALL));
                    }
                    String downloadFileName;
                    if (fileNameList.contains(Constants.HIGH_MP4)) {
                        downloadFileName = Constants.HIGH_MP4;
                    } else if (fileNameList.contains(Constants.MEDIUM_MP4)) {
                        downloadFileName = Constants.MEDIUM_MP4;
                    } else {
                        downloadFileName = Constants.LOW_MP4;
                    }
                    JSONObject fileObject = filesList.getJSONObject(fileNameList.indexOf(downloadFileName));
                    fileData.put(Constants.NAME_SMALL, downloadFileName);
                    fileData.put(Constants.FILESIZE, String.valueOf(fileObject.get(Constants.FILESIZE)));
                    log.info("final static renditions file name {} from file list {}", downloadFileName, fileNameList);
                    return fileData;
                }
            }
        } catch (Exception e) {
            log.error("Error while Get Mux asset file name and file size", e);
        }
        return Collections.emptyMap();
    }

    @Override
    @Transactional
    public void trimRecordedVideo(String assetId, Long sessionId, User user, Event event, Long startTime, Long endTime) {
	    log.info("request received to trim asset {} to start time {} and end time {} by user {}",assetId, startTime, endTime,user.getUserId());

        MuxAssetDTO muxAssetDTO = processTrimming(assetId, startTime, endTime);

        log.info("Mux trimmed Asset Details {}",muxAssetDTO);

        if(muxAssetDTO != null){
            MUXLivestreamAssetDetails muxLivestreamAssetDetails = muxAssetDTO.toEntity(muxAssetDTO);
            muxLivestreamAssetDetails.setSessionId(sessionId);
            muxLivestreamAssetDetails.setEventId(event.getEventId());
            muxLivestreamAssetDetails.setAssetType(AssetType.SESSION_ASSET);
            muxLivestreamAssetDetails.setSourceAssetId(assetId);
            muxLivestreamAssetDetails.setRecordStatus(RecordStatus.CREATE);
            if (!StringUtils.isBlank(muxAssetDTO.getPlayBackId()) && muxAssetDTO.getSigned()) {
                MuxJwtDto muxJwtDto = generateDomainPlayBackRestrictionToken(muxAssetDTO.getPlayBackId(), event.getEventId());
                muxLivestreamAssetDetails.setPlayBackRestrictionToken(muxJwtDto.getPlayBackRestrictionToken());
                muxLivestreamAssetDetails.setThumbnailRestrictionToken(muxJwtDto.getThumbnailRestrictionToken());
            }
            muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBack(sessionId, AssetType.SESSION_ASSET);
            muxLivestreamAssetDetails.setDefaultPlayback(Boolean.TRUE);
            StringBuilder title = new StringBuilder("Trimmed Video - ").append(startTime + "s").append(" to ").append(endTime + "s ");
            title.append("(").append(DateUtils.formatDateToEventTimeZone(new Date(), event.getEquivalentTimeZone())).append(")");
            muxLivestreamAssetDetails.setTitle(title.toString());
            muxLivestreamAssetDetails.setDescription(muxLivestreamAssetRepoService.getDescriptionByAssetIdAndSessionId(assetId,sessionId));
            muxLivestreamAssetDetails.setDuration(endTime - startTime);
            muxLivestreamAssetRepoService.saveWithDefaultSequence(muxLivestreamAssetDetails);
        }
    }

    private MUXLivestreamAssetDetails validateRequest(String assetId, Long sessionId, Long startTime, Long endTime) {
        Optional<MUXLivestreamAssetDetails> assetDetails = muxLivestreamAssetRepoService.findBySessionIdAndAssetId(sessionId, assetId);
        if(!assetDetails.isPresent()){
            throw new NotFoundException(NotFoundException.NotFound.MUX_ASSET_STATIC_RENDITIONS_NOT_FOUND);
        }

        if(endTime != null && (endTime <=0 || endTime <= startTime)){
            throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.SESSION_VIDEO_RECORDING_TRIM_END_TIME_INVALID);
        }
        return assetDetails.get();
    }

    private MuxAssetDTO processTrimming(String assetId, Long startTime, Long endTime){
        try {
            HttpResponse<JsonNode> jsonNodeHttpResponse = Unirest.post(MUX_STREAM_URL + MUX_ASSET_ID_PATH )
                    .headers(getHeaders())
                    .body(getBodyForVideoTrim(startTime, endTime, assetId))
                    .asJson();
            JSONObject object = jsonNodeHttpResponse.getBody().getObject();

            return MuxUtils.buildMuxAssetData(object);

        } catch (Exception e) {
            log.info("Exception while triming asset {}, exception {}", assetId, e.getMessage());
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_LIVE_STREAM_STATUS_NOT_AVAILABLE;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
    }

    private String getBodyForVideoTrim(Long startTime, Long endTime,String assetId){
        JSONObject jsonObject = new JSONObject();
        JSONArray inputArray = new JSONArray();
        JSONObject jsonData = new JSONObject();
        try{
            jsonData.put("url","mux://assets/"+assetId);
            jsonData.put("start_time",startTime);
            if(endTime != null && endTime > 0L){
                jsonData.put("end_time",endTime);
            }
            inputArray.put(jsonData);

            jsonObject.put("input",inputArray);
            jsonObject.put("playback_policy",new JSONArray().put("signed").put("public"));
            jsonObject.put("mp4_support","standard");

        }
        catch (Exception exception){
            log.warn("Error while creating request body for trim video, mux assetId = {}, strtTime = {}, endTime={}, exception ={}",assetId, startTime, endTime,exception.getMessage());
        }

        return jsonObject.toString();
    }

    private String getPlaybackJwtToken(String playbackId, String identifierType){
        final long defaultExpireTime = System.currentTimeMillis() / 1000L
                + TimeUnit.HOURS.toSeconds(1);
        Map<String, Object> claims = new HashMap<>();
        claims.put("sub", playbackId);
        claims.put("exp", defaultExpireTime + 600);
        claims.put("aud", identifierType);

        Map<String, Object> header = new HashMap<>();
        header.put(Constants.SIGNING_KEY, data_signing_key);
        return Jwts.builder()
                .setClaims(claims)
                .setHeader(header)
                .signWith(SignatureAlgorithm.RS256, getPrivateKey(privateKeyDataFileName))
                .compact();
    }

    @Override
    public MuxViewerDto getPlaybackViewDetail(String playbackId, User user) {
	    log.info("Request received for get real time view count for playback Id {} by user {}", playbackId, user.getUserId());
        MuxViewerDto muxViewerDto = new MuxViewerDto();
        if (StringUtils.isBlank(playbackId)) {
            return muxViewerDto;
        }
        try {
            String jwt = getPlaybackJwtToken(playbackId,Constants.PLAYBACK_ID);
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .get("https://stats.mux.com/counts?token=" + jwt)
                    .headers(getHeaders())
                    .asJson();

            log.info("real time view count response for playback id {}  is  response  {}", playbackId ,jsonResponse);
            if (jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <= 209) {
                JSONArray data = (JSONArray) jsonResponse.getBody().getObject().get(Constants.DATA);
                for (int i = 0; i < data.length(); i++) {
                    JSONObject jObj=  (JSONObject)data.get(i);
                    muxViewerDto.setViews(Integer.parseInt(jObj.get("views").toString()));
                    muxViewerDto.setViewers(Integer.parseInt(jObj.get("viewers").toString()));
                    muxViewerDto.setUpdatedAt(jObj.get("updated_at").toString());
                }
            }
        } catch (Exception e) {
            log.info("Exception while get Domain PlayBack Restriction List {}", e.getMessage(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_CREATE_URL_SINGING_KEY;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
        return muxViewerDto;
    }

    @Override
    public String buildUploadMetaData(Long eventId, Long sessionId)  {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Constants.EVENT_ID, eventId);
            jsonObject.put(Constants.SESSION_ID, sessionId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        return jsonObject.toString();
    }

    @Override
    public MuxLiveStreamHealthDto getLiveStreamHealthDetail(String liveStreamId, User user) {
        log.info("Request received for get live stream health for liveStreamId Id {} by user {}", liveStreamId, user.getUserId());
        MuxLiveStreamHealthDto muxLiveStreamHealthDto = new MuxLiveStreamHealthDto();
        if (StringUtils.isBlank(liveStreamId)) {
            return muxLiveStreamHealthDto;
        }
        try {
            String jwt = getPlaybackJwtToken(liveStreamId, Constants.LIVE_STREAM_ID);
            HttpResponse<JsonNode> jsonResponse = Unirest
                    .get("https://stats.mux.com/live-stream-health?token=" + jwt)
                    .headers(getHeaders())
                    .asJson();

            if (jsonResponse.getStatus() >= 200
                    && jsonResponse.getStatus() <= 209) {
                JSONArray data = (JSONArray) jsonResponse.getBody().getObject().get(Constants.DATA);
                for (int i = 0; i < data.length(); i++) {
                    JSONObject jObj = (JSONObject) data.get(i);
                    if (jObj.has(Constants.INGEST_HEALTH)) {
                        JSONObject ingestHealth = jObj.getJSONObject(Constants.INGEST_HEALTH);
                        muxLiveStreamHealthDto.setStreamDriftSessionAvg(Integer.parseInt(ingestHealth.get(Constants.STREAM_DRIFT_SESSION_AVG).toString()));
                        muxLiveStreamHealthDto.setStreamDriftDeviationFromRollingAvg(Integer.parseInt(ingestHealth.get(Constants.STREAM_DRIFT_DEVIATION_FROM_ROLLING_AVG).toString()));
                        muxLiveStreamHealthDto.setUpdatedAt(ingestHealth.get(Constants.UPDATED_AT).toString());
                        muxLiveStreamHealthDto.setStatus(ingestHealth.get(Constants.STATUS).toString());
                    }

                }
            }
        } catch (Exception e) {
            log.info("Exception while request to get live stream health for liveStreamId Id {} by user {} error {}",liveStreamId,user.getUserId(), e);
            MUXException.MUXExceptionMsg exceptionMessage = MUXException.MUXExceptionMsg.MUX_CREATE_URL_SINGING_KEY;
            exceptionMessage.setErrorMessage(e.getMessage());
            exceptionMessage.setDeveloperMessage(e.getMessage());
            throw new MUXException(exceptionMessage);
        }
        return muxLiveStreamHealthDto;
    }

    @Override
    public List<Map<String,String>> getAllMuxSupportableLanguages(){
        return MUX_CAPTION_LANGUAGE_LIST;
    }
}
