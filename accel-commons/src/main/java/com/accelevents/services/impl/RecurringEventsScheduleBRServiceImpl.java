package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.EnumEventPayoutStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.repositories.*;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO;
import com.accelevents.ticketing.dto.TicketingOrderDto;
import com.accelevents.utils.*;
import com.itextpdf.text.DocumentException;
import freemarker.template.TemplateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.StripeTransactionSource.EVENT_TICKETING;
import static com.accelevents.utils.Constants.FREE;
import static com.accelevents.utils.Constants.ONLY_DATE_FORMAT;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static com.accelevents.domain.RecurringEvents.RecurringEventStatus;
import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;

@Service
public class RecurringEventsScheduleBRServiceImpl implements RecurringEventsScheduleBRService {

    protected static final Logger log = LoggerFactory.getLogger(RecurringEventsScheduleBRServiceImpl.class);

    @Autowired
    private RecurringEventsRepository recurringEventsRepository;

    @Autowired
    private RecurringEventsScheduleRepository recurringEventsScheduleRepository;

    @Autowired
    private EventLimitRegistrationDomainService eventLimitRegistrationDomainService;

    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;

    @Autowired
    private EventCommonRepoService eventCommonRepoService;

    @Autowired
    private EventTicketsService eventTicketService;

    @Autowired
    private CommonEventService commonEventService;

    @Autowired
    private TicketingTypeService ticketingTypeService;

    @Autowired
    private TicketingTypeTicketService ticketingTypeTicketService;

    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private TicketingHelperService ticketingHelperService;

    @Autowired
    private TicketingOrderManagerRepository ticketingOrderManagerRepository;

    @Autowired
    private TicketingOrderRepository ticketingOrderRepository;

    @Autowired
    private TicketingTypeRepository ticketingtypeRepository;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;

    @Autowired
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Autowired
    private StripeTransactionService stripeTransactionService;

    @Autowired
    private SeatsIoService seatsIoService;

    @Autowired TicketingOrderService ticketingOrderService;

    @Autowired
    private SeatsIoForMultipleEventsImpl seatsIoForMultipleEventsImpl;

    @Autowired
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;

    @Autowired
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;

    @Autowired
    private TicketingCouponService ticketingCouponService;

    @Autowired
    private TicketingAccessCodeService ticketingAccessCodeService;

    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Autowired
    private EmbedWidgetSettingService embedWidgetSettingService;

    @Autowired
    private ResendTicketingEmailRepository resendTicketingEmailService;

    @Autowired
    private RegistrationAttributeRepository registrationAttributeRepository;

    @Autowired
    private TicketingLimitedDisplayCodeService ticketingLimitedDisplayCodeService;
    @Autowired
    private EventLimitRegistrationDomainRepository eventLimitRegistrationDomainRepository;

    @Override
    public RecurringEvents save(RecurringEvents recurringEvents) {
        return recurringEventsRepository.save(recurringEvents);
    }

    @Override
    public RecurringEventSchedule findRecurringEventScheduleById(Long recurringEventsScheduleId) {
        return recurringEventsScheduleRepository.findById(recurringEventsScheduleId).
                orElseThrow(() -> new NotFoundException(NotFoundException.EventNotFound.RECURRING_EVENT_SCHEDULE_NOT_FOUND));
    }

    @Override
    public List<RecurringScheduleResDto> getRecurringEventSchedule(Event event) {
        List<Object[]> objects = recurringEventsRepository.getCountOfRecurringEventBySchedule(event.getEventId());
        Map<Long, Integer> map = new HashMap<>();
        objects.forEach(e-> map.put(((BigInteger)e[0]).longValue(), ((BigInteger)e[1]).intValue()));

        List<RecurringScheduleResDto> recurringScheduleResDtos =
                recurringEventsScheduleRepository.findByEventId(event)
                        .stream()
                        .map
                                (e -> {
                                    RecurringScheduleResDto scheduleResDto = new RecurringScheduleResDto(map.get(e.getId())).createDto(e);
                                    getRecurringEventsByScheduleId(event, e.getId(), scheduleResDto);
                                    if(scheduleResDto.getRecurringEventResDtos() != null && !scheduleResDto.getRecurringEventResDtos().isEmpty()) {
                                        scheduleResDto.setCheckInXMinutesBefore(scheduleResDto.getRecurringEventResDtos().get(0).getCheckInXMinutesBefore());
                                    }

                                    return scheduleResDto;
                                })
                        .collect(Collectors.toList());

        List<RecurringEvents> customRecurringEvents = recurringEventsRepository.findByEventIdAndRecurringEventScheduleIdIsNullOrderByRecurringEventStartDateAsc(event);

        List<RecurringScheduleResDto> recurringScheduleResDtos2 = customRecurringEvents
                .stream()
                .map(m-> {
                    RecurringScheduleResDto scheduleResDto = new RecurringScheduleResDto(1).createDto(m);

                    getRecurringEventsByRecurringEventId(event, m, scheduleResDto);
                    return  scheduleResDto;
                })
                .collect(Collectors.toList());

        recurringScheduleResDtos.addAll(recurringScheduleResDtos2);

        return recurringScheduleResDtos;
    }

    @Override
    public List<RecurringEventResDto> getRecurringEventsByScheduleId(Event event, Long recurringEventScheduleId) {
        return getRecurringEventsByScheduleId(event, recurringEventScheduleId, null);
    }

    public List<RecurringEventResDto> getRecurringEventsByScheduleId(Event event, Long recurringEventScheduleId, RecurringScheduleResDto scheduleResDto) {
        List<RecurringEvents> recurringEventsList = recurringEventsRepository.findByRecurringEventScheduleId(recurringEventScheduleId);
        List<Long> soldTicketsByRecurringEventIds = eventTicketsRepoService.getRecurringIdsForScheduleWithSoldTickets(recurringEventScheduleId);
        List<Long> orderIdsInProgress = ticketingOrderManagerService.getOrderIdsAreInProgressByRecurringEventScheduleId(recurringEventScheduleId,DateUtils.getCurrentDate(), TicketingOrder.TicketingOrderStatus.CREATE);
        return getRecurringEventResDtos(event, scheduleResDto, recurringEventsList, soldTicketsByRecurringEventIds,orderIdsInProgress);
    }

    protected List<RecurringEventResDto> getRecurringEventResDtos(Event event, RecurringScheduleResDto scheduleResDto, List<RecurringEvents> recurringEventsList, List<Long> soldTicketsByRecurringEventIds,List<Long> orderIdsInProgress) {
        if (!soldTicketsByRecurringEventIds.isEmpty()) {
            Object[] soldMinDate = recurringEventsRepository.findMinRecurringEventSoldDate(soldTicketsByRecurringEventIds);
            if (null != soldMinDate && null != soldMinDate[0] && null != scheduleResDto)
                scheduleResDto.setSoldDate(getDateInLocal((Date) soldMinDate[0], event.getEquivalentTimeZone(), null));
        }

        List<RecurringEventResDto> recurringEventResDtos = recurringEventsList.stream().map(e ->
                RecurringEventResDto.createDtoForRecurringEvents(e, soldTicketsByRecurringEventIds,orderIdsInProgress, null))
                .collect(Collectors.toList());

        if (null != scheduleResDto) {
            scheduleResDto.setRecurringEventResDtos(recurringEventResDtos);
        }
        return recurringEventResDtos;
    }

    @Override
    public void getRecurringEventsByRecurringEventId(Event event, RecurringEvents recurringEvent, RecurringScheduleResDto scheduleResDto) {
        List<Long> soldTicketsByRecurringEventIds =  eventTicketsRepoService.findSoldRecurringEventIds(recurringEvent);
        List<Long> orderIdsInProgress = ticketingOrderManagerService.getOrderIdsAreInProgressByRecurringEventId(recurringEvent.getId());
        getRecurringEventResDtos(event, scheduleResDto, new ArrayList<>(Collections.singletonList(recurringEvent)), soldTicketsByRecurringEventIds,orderIdsInProgress);
    }

    protected void deleteTicketingTypesByfRecurringEvent(Long recuringEventId) {
        ticketingTypeService.deleteByRecurringEventId(recuringEventId);
    }

    @Override
    @Transactional
    public void deleteRecurringEventById(Long recurringEventId, Event event) {

        RecurringEvents recurringEvents = getRecurringEventsAndCheckHostAbleToDeleteEvent(recurringEventId);

        cancelEventAndUpdateCancelStatusToRefundedDeletedOrders(recurringEventId, event, recurringEvents);

        if (!RecurringEventStatus.CANCEL.equals(recurringEvents.getStatus())) {
            deleteRecEvents(recurringEventId, event, recurringEvents);
        }
        deleteScheduledResendTicketingEmails(event,Arrays.asList(recurringEventId));
        updateSeatsIo(event);
    }

    protected void deleteRecEvents(Long recurringEventId, Event event, RecurringEvents recurringEvents) {
        deleteOrderAndOrderManagerByRecurringEventId(recurringEventId);
        deleteCouponCodeByRecurringIdAndEventId(Collections.singletonList(recurringEventId), event);
        deleteAccessCodeByRecurringIdAndEventId(Collections.singletonList(recurringEventId), event);
        deleteTicketHolderRequiredAttribute(Collections.singletonList(recurringEventId), event);
        deleteRegistrationAttribute(Collections.singletonList(recurringEventId), event);
        deleteTicketingTypesByListOfRecurringEventsIds(Collections.singletonList(recurringEventId));
        deleteEventLimitRegistrationRulesOfRecurringEvents(Collections.singletonList(recurringEventId));
        recurringEventsRepository.delete(recurringEvents);

    }

    protected void updateSeatsIo(Event event) {
        checkDisableRecurringEvent(event);

        seatsIoForMultipleEventsImpl.createOrUpdateMultipleEvent(ticketingHelperService.findTicketingByEvent(event).getChartKey(), event.getEventId());

        recurringEventsMainScheduleService.updateTicketingBasedOnLastRecurringEvent(event);
    }

    protected void cancelEventAndUpdateCancelStatusToRefundedDeletedOrders(Long recurringEventId, Event event, RecurringEvents recurringEvents) {
            cancelEvent(recurringEventId, Optional.of(recurringEvents));
            updateRecStatusToCancelInAllRelatedTables(event, Collections.singletonList(recurringEventId));
    }

    private RecurringEvents getRecurringEventsAndCheckHostAbleToDeleteEvent(Long recurringEventId) {
        RecurringEvents recurringEvents = getRecurringEvents(recurringEventId);
        BigInteger ticketSold = eventTicketsRepoService.getRecurringHavingTicketSold(recurringEventId);
        if (null != ticketSold && ticketSold.intValue() > 0) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_DELETE_EVENT);
        }
        return recurringEvents;
    }

    protected void deleteOrderAndOrderManagerByRecurringEventId(Long recurringEventId) {
        List<Long> orderIdsToBeDelete = ticketingOrderManagerService.getSoldOrderIdsByRecurringEventId(recurringEventId);
        List<Long> orderIdsInProgress = ticketingOrderManagerService.getOrderIdsAreInProgressByRecurringEventId(recurringEventId);

        if(null != orderIdsInProgress && !orderIdsInProgress.isEmpty()){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_DELETE_EVENT_ORDER_IS_IN_PROGRESS_BUT_YOU_CAN_CANCEL_EVENT);
        }else {
            deleteOrderAndOrderManager(orderIdsToBeDelete);
        }
    }

    protected void deleteOrderAndOrderManager(List<Long> orderIdsToBeDelete) {
        if (!orderIdsToBeDelete.isEmpty()) {
            ticketingOrderManagerRepository.deleteTicketingOrderManagerByListOfOrderIds(orderIdsToBeDelete);
            ticketingOrderRepository.deleteByIdIn(orderIdsToBeDelete);
        }
    }

    private void deleteTicketingTypesByListOfRecurringEventsIds(List<Long> recuringEventIds) {
        ticketingTypeService.deleteByRecurringEventIdIn(recuringEventIds);
    }

    @Override
    public RecurringEvents getRecurringEvents(Long recurringEventId) {
        return recurringEventsRepository.findById(recurringEventId).
                orElseThrow(() -> new NotFoundException(NotFoundException.EventNotFound.RECURRING_EVENT_NOT_FOUND));
    }

    @Override
    public Optional<RecurringEvents> getRecurringEventById(Long recurringEventId) {
        return recurringEventsRepository.findById(recurringEventId);
    }

    @Override
    @Transactional
    public void deleteRecurringEventsScheduleById(Long recurringEventsScheduleId, Event event) {
        RecurringEventSchedule recurringEventSchedule = getRecurringEventSchedule(recurringEventsScheduleId);
        List<Long> soldRecurringEventIds = ticketingOrderManagerService.getSoldRecurringEventIdsByScheduleId(recurringEventsScheduleId);

        if(!soldRecurringEventIds.isEmpty())
            recurringEventsRepository.updateRecurringEventsToCustom(soldRecurringEventIds);

        List<Long> recurringEventsToBeDelete = recurringEventsRepository.findByRecurringEventIdScheduleId(recurringEventsScheduleId);
        recurringEventsToBeDelete.removeAll(soldRecurringEventIds);

        if(!recurringEventsToBeDelete.isEmpty()){
            deleteCouponCodeByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            deleteDisplayCodeByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            deleteAccessCodeByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            deleteTicketHolderRequiredAttribute(recurringEventsToBeDelete, event);
            deleteRegistrationAttribute(recurringEventsToBeDelete, event);
            deleteTicketingTypesByListOfRecurringEventsIds(recurringEventsToBeDelete);
            deleteEventLimitRegistrationRulesOfRecurringEvents(recurringEventsToBeDelete);
        }

        recurringEventsRepository.deleteByRecurringEventScheduleId(recurringEventSchedule.getId());
        recurringEventsScheduleRepository.delete(recurringEventSchedule);
        deleteScheduledResendTicketingEmails(event,recurringEventsToBeDelete);
        updateSeatsIo(event);
    }

    private void deleteEventLimitRegistrationRulesOfRecurringEvents(List<Long> recurringEventsToBeDelete) {
        eventLimitRegistrationDomainService.deleteByRecurringEventIdIn(recurringEventsToBeDelete);
    }

    private void deleteTicketHolderRequiredAttribute(List<Long> recurringEventsToBeDelete, Event event) {
        ticketHolderRequiredAttributesService.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
    }

    private void deleteAccessCodeByRecurringIdAndEventId(List<Long> recurringEventsToBeDelete, Event event) {
        ticketingAccessCodeService.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
    }

    private void deleteCouponCodeByRecurringIdAndEventId(List<Long> recurringEventsToBeDelete, Event event) {
        ticketingCouponService.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
    }
    private void deleteDisplayCodeByRecurringIdAndEventId(List<Long> recurringEventsToBeDelete, Event event) {
        ticketingLimitedDisplayCodeService.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
    }

    protected RecurringEventSchedule getRecurringEventSchedule(Long recurringEventsScheduleId) {
        return recurringEventsScheduleRepository.findById(recurringEventsScheduleId).
                orElseThrow(() -> new NotFoundException(NotFoundException.EventNotFound.RECURRING_EVENT_SCHEDULE_NOT_FOUND));
    }

    @Override
    @Transactional
    public void updateRecurringEvent( Event event,
                                      RecurringEventDto recurringEventDto,
                                      Long recurringEventId ) {

        RecurringEvents recurringEvents = getRecurringEvents(recurringEventId);

        LocalDate occursFrom = LocalDate.parse(recurringEventDto.getOccursFrom(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));
        LocalDate occursUntil = LocalDate.parse(recurringEventDto.getOccursUntil(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));

        LocalTime startTime = LocalTime.parse(recurringEventDto.getStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime endTime = LocalTime.parse(recurringEventDto.getEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));

        LocalDateTime startDateTimeL = LocalDateTime.of(occursFrom, startTime);
        LocalDateTime endDateTimeL = LocalDateTime.of(occursUntil, endTime);
        Date startDateTime = Date.from(startDateTimeL.toInstant(ZoneOffset.UTC));
        Date endDateTime = Date.from(endDateTimeL.toInstant(ZoneOffset.UTC));
        Date dbStartDateTime = getDateInUTC(recurringEvents.getRecurringEventStartDate(),"Universal Coordinated Time (UTC)");
        Date dbEndDateTime = getDateInUTC(recurringEvents.getRecurringEventEndDate(),"Universal Coordinated Time (UTC)");
        if(!dbEndDateTime.equals(endDateTime)||!dbStartDateTime.equals(startDateTime)) {
            if (isCustom(recurringEvents)) {
                // Custom
                // Look for merge
                RecurringEvents recurringEventsDb = findRecurringEventByDateAndTime(startDateTime, endDateTime, event);
                if (recurringEventsDb != null) {
                    //recurringEvents.setRecurringEventScheduleId(recurringEventsDb.getRecurringEventScheduleId());//nosonar
                    updateEventTicketsWithDbRecurringEvent(recurringEvents.getId(), recurringEventsDb.getId());
                    recurringEventsRepository.delete(recurringEvents);
                } else {
                    recurringEvents.setRecurringEventStartDate(startDateTime);
                    recurringEvents.setRecurringEventEndDate(endDateTime);
                    recurringEventsRepository.save(recurringEvents);
                }
            } else {

                recurringEvents.setRecurringEventStartDate(startDateTime);
                recurringEvents.setRecurringEventEndDate(endDateTime);
                recurringEventsRepository.save(recurringEvents);
            }
        }
        recurringEventsMainScheduleService.updateTicketTypesBasedOnUpdatedRecurringEvents(Collections.singletonList(recurringEvents));
        recurringEventsMainScheduleService.updateTicketingBasedOnLastRecurringEvent(event);
    }

    private boolean isCustom(RecurringEvents recurringEvents) {
        return recurringEvents.getRecurringEventScheduleId() == null;
    }

    private void updateEventTicketsWithDbRecurringEvent(long oldId, long newId) {
        commonEventService.updateEventTicketsOrAddOnWithDbRecurringEvent(oldId, newId);
    }

    @Override
    public List<RecurringEvents> getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(Event event) {
        return recurringEventsRepository.findByEventIdOrderByRecurringEventStartDateAsc(event);
    }

    @Override
    public List<Long> getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(Long eventId) {
        return recurringEventsRepository.findRecurringEventIdByEventIdOrderByRecurringEventStartDateAsc(eventId, RecurringEvents.RecurringEventStatus.CANCEL);
    }

    @Override
    public String getFirstRecId(Event event, Ticketing ticketing) {
        String firstRecId = Constants.STRING_EMPTY;
        if(ticketing.isRecurringEvent()){
            List<Long> recIds = getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(event.getEventId());
            firstRecId = CollectionUtils.isEmpty(recIds) ? Constants.STRING_EMPTY : String.valueOf(recIds.get(0));
        }
        return firstRecId;
    }

    @Override
    public List<RecurringEventResDto> getAllRecurringEventsByEvent(Event event, boolean onlyFutureDates) {

        List<RecurringEvents> recurringEventsList;
        if(!onlyFutureDates){
            recurringEventsList = getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(event);
        }else {
            recurringEventsList = getRecurringEventsOnlyFutureDate(event);
        }

        List<Long> recurringEventIds = recurringEventsList.stream().map(e->e.getId()).collect(Collectors.toList());
        List<RecurringEventsTicketingTypeAndSoldCountDTO> soldCountDTOS = eventTicketsRepoService.getSoldCountByListOfRecurringList(recurringEventIds);

        Map<Long, List<RecurringEventsTicketingTypeAndSoldCountDTO>> mapRecIdAndSoldCountDto =soldCountDTOS.stream().collect(Collectors.groupingBy(e->e.getRecurringEventId()));
        return recurringEventsList.stream().map(e -> RecurringEventResDto.createDtoForRecurringEvents(e, eventTicketsRepoService.findSoldRecurringEventIds(e),
                ticketingOrderManagerService.getOrderIdsAreInProgressByRecurringEventId(e.getId()), mapRecIdAndSoldCountDto.get(e.getId()))).collect(Collectors.toList());
    }

    @Override
    public List<RecurringEvents> getRecurringEventsOnlyFutureDate(Event event) {
        Date currentDateAndTimeInEventTimeZone = TimeZoneUtil.getDateInLocal(DateUtils.getCurrentDate(), event.getEquivalentTimeZone());
        return recurringEventsRepository.findByEventIdOrderByRecurringEventEndDateAscOnlyFutureRecurringEvent(event,currentDateAndTimeInEventTimeZone);
    }

    @Override
    public List<RecurringEvents> getRecurringEventsOnlyFutureDateByEventAndEventSchedule(Event event, RecurringEventSchedule recurringEventSchedule) {
        Date currentDateAndTimeInEventTimeZone = TimeZoneUtil.getDateInLocal(DateUtils.getCurrentDate(), event.getEquivalentTimeZone());
        return recurringEventsRepository.getRecurringEventsOnlyFutureDateByEventAndEventSchedule(event,currentDateAndTimeInEventTimeZone,recurringEventSchedule.getId());
    }

    @Override
    @Transactional
    public void cancelSchedule(Long id, Event event) {
        boolean soldTicketExists = eventTicketsRepoService.countSoldTicketForSchedule(id);

        if (soldTicketExists){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_CANCEL_SCHEDULE_EVENT);
        }

        RecurringEventSchedule recurringEventSchedule = getRecurringEventSchedule(id);
        List<RecurringEvents> recurringEvents = recurringEventsRepository.findByRecurringEventScheduleId(recurringEventSchedule.getId());
        recurringEvents.forEach(e-> cancelEvent(e.getId()));

        List<Long> recurringEventIds = recurringEvents.stream().map(RecurringEvents::getId).collect(Collectors.toList());

        updateRecStatusToCancelInAllRelatedTables(event,recurringEventIds);

        recurringEventSchedule.setStatus(RecurringEventSchedule.EnumScheduleStatus.CANCEL);

        recurringEventsScheduleRepository.save(recurringEventSchedule);

        updateSeatsIo(event);
    }

    @Override
    public void updateRecStatusToCancelInAllRelatedTables(Event event, List<Long> recurringEventIds) {
        recurringEventIds = recurringEventIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> orderIds = ticketingOrderManagerService.getAllOrderIdsByRecurringEventIds(recurringEventIds);
        if (!orderIds.isEmpty()) {
            ticketingOrderService.updateTicketingOrderRecStatusByOrderIds(orderIds, RecordStatus.CANCEL);
        }
        ticketingOrderManagerService.updateTicketingOrderManagerRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        ticketingCouponService.updateTicketCouponRecStatusByRecurringEventIds(recurringEventIds,event.getEventId(), RecordStatus.CANCEL);
        ticketingLimitedDisplayCodeService.updateTicketDisplayCodeRecStatusByRecurringEventIds(recurringEventIds,event.getEventId(), RecordStatus.CANCEL);
        ticketingAccessCodeService.updateAccessCodeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        ticketHolderRequiredAttributesService.updateTicketHolderAttributeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        ticketingTypeTicketService.updateTicketingTypeRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        eventTicketService.updateEventTicketsRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        embedWidgetSettingService.updateEmbedwidgetSettingRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        eventLimitRegistrationDomainService.updateEventLimitRegistrationDomainRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.DELETE);
    }

    @Override
    @Transactional
    public void cancelRecurringEvent(Long id, Event event){

        List<Long> soldOrders = ticketingOrderManagerService.getSoldOrderIdsByRecurringEventId(id);

        if (soldOrders.isEmpty()){
            RecurringEvents recurringEvents = getRecurringEventsAndCheckHostAbleToDeleteEvent(id);
            deleteRecEvents(id, event, recurringEvents);
            updateSeatsIo(event);
        }else {
            BigInteger ticketSold = eventTicketsRepoService.getRecurringHavingTicketSold(id);
            if (null != ticketSold && ticketSold.intValue() > 0) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_CANCEL_RECURRING_EVENT);
            }else {
                Optional<RecurringEvents> recurringEvents = recurringEventsRepository.findById(id);
                cancelEvent(id, recurringEvents);
                if (recurringEvents.isPresent()) {
                    RecurringEvents re = recurringEvents.get();
                    Long recurringEventId = re.getId();

                    updateRecStatusToCancelInAllRelatedTables(event, Collections.singletonList(recurringEventId));

                }
                updateSeatsIo(event);
            }

        }
    }




    @Override
    @Transactional
    public String transferOrderToNewRecurringEvent(long orderId,
                                                   long newRecurringEventId,
                                                   Event event,
                                                   List<TicketingOrderDto> ticketingDto) {
        StripeTransaction stripeTransaction = stripeTransactionService
                .findBySourceAndSourceId(EVENT_TICKETING, orderId);

        String lastFour = Constants.STRING_EMPTY;
        String cardType = Constants.STRING_EMPTY;
        if (stripeTransaction != null) {
            lastFour = stripeTransaction.getLastFour();
            cardType = stripeTransaction.getCardType();
        }

        List<TicketDetailDto> oldRecurOrderTicketType = ticketingOrderManagerRepository.getTickeDetail(orderId);
        Map<TicketingType,TicketDetailDto> newTicketTypeVsOldTicketTypeMap = getNewTTvsOldTTMapAndValidateTT(newRecurringEventId, oldRecurOrderTicketType);

        List<TicketingOrderDto> ticketingOrderDtos = ticketingDto.stream().filter(e-> 0 < e.getTicketTypeId())
                .collect(Collectors.toList());

        Map<Long, List<String>> mapOldTTvsNewSeats = new HashMap<>();
        for(TicketingOrderDto orderDto : ticketingOrderDtos){
            List<String> seats = mapOldTTvsNewSeats.get(orderDto.getTicketTypeId());
            if(CollectionUtils.isEmpty(seats)){
                seats = new ArrayList<>();
            }
            seats.addAll(orderDto.getSeatNumbers());
            mapOldTTvsNewSeats.put(orderDto.getTicketTypeId(), seats);
        }

        for(TicketingType newTicketType : newTicketTypeVsOldTicketTypeMap.keySet()) {

            TicketDetailDto oldTicketTypeDto = newTicketTypeVsOldTicketTypeMap.get(newTicketType);

            String seats = Constants.STRING_EMPTY;
            String seatsToDisplay = Constants.STRING_EMPTY;

            if(!ticketingOrderDtos.isEmpty()){
                List<String> seatNumbers = updateCountSeatNumbers(newRecurringEventId, event, mapOldTTvsNewSeats, oldTicketTypeDto);
                seats = GeneralUtils.convertListToCommaSeparated(seatNumbers);
                seatsToDisplay = String.join("|", seatNumbers);
            }

            ticketingOrderManagerRepository.updateTicketTypeOfTicketingOrderManager(seats,seatsToDisplay,oldTicketTypeDto.getOrderId(), newTicketType,newTicketType.getRecurringEventId(), oldTicketTypeDto.getTicketTypeId());
            eventCommonRepoService.updateTypeOfEventTicketsOrAddOn(oldTicketTypeDto.getOrderId(), newTicketType, oldTicketTypeDto.getTicketTypeId(), oldTicketTypeDto.getRecurringEventId(), newRecurringEventId);
        }

        TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderId, event);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        sendEmailDuringTransferOrder(orderId, event, lastFour, cardType, ticketingOrder, ticketing);
        return getSuccessMessage(newRecurringEventId, event, ticketingOrder, oldRecurOrderTicketType);

    }

    @Override
    public Map<Long, String> getRecurringEventByIdsIn(Set<Long> recurringEventIds) {
        if(recurringEventIds.isEmpty()){
            return Collections.emptyMap();
        }
        //return recurringEventsRepository.getRecurringEventByRecurringEventIdAndDate(recurringEventIds);//nosonar
        List<RecurringIdAndDateDto> recurringIdAndDateDtos = recurringEventsRepository.getrecurringEventByRecurringEventIds(recurringEventIds);
        return recurringIdAndDateDtos.stream().collect(Collectors.toMap(e->e.getId(),e->e.getStartDateStr()));
    }

    @Override
    public List<RecurringIdAndDateDto> getRecEventByIdsIn(Set<Long> recurringEventIds, String equivalentTimezone) {
        if(recurringEventIds.isEmpty()){
            return Collections.emptyList();
        }
        List<RecurringIdAndDateDto> recurringIdAndDateDtos = recurringEventsRepository.getrecEventByRecurringEventIds(recurringEventIds);

        recurringIdAndDateDtos.forEach(e-> {
            String dateInString = DateUtils.getFormattedDateString(e.getStartDate());
            e.setStartDateStr(dateInString);
        });
        return recurringIdAndDateDtos;
    }

    private Map<TicketingType, TicketDetailDto>  getNewTTvsOldTTMapAndValidateTT(long newRecurringEventId, List<TicketDetailDto> oldRecurOrderTicketType) {
        Map<TicketingType, TicketDetailDto> newTicketTypeVsOldTicketTypeMap = new HashMap<>();
        for(TicketDetailDto oldRecurTicketTypeDto : oldRecurOrderTicketType) {

            TicketingType newRecurringEventTicketType = getNewRecurringTicketingType(newRecurringEventId, oldRecurTicketTypeDto);
            if(oldRecurTicketTypeDto.getTicketType() != TicketType.DONATION) checkQuantityAvailableToTransfer(newRecurringEventTicketType, oldRecurTicketTypeDto);
            newTicketTypeVsOldTicketTypeMap.put(newRecurringEventTicketType,oldRecurTicketTypeDto);
        }
        return newTicketTypeVsOldTicketTypeMap;
    }

    protected void sendEmailDuringTransferOrder(long orderId, Event event, String lastFour, String cardType,
                                                TicketingOrder ticketingOrder, Ticketing ticketing) {
        try {
            List<EventTickets> eventTicketsListForEmail = eventCommonRepoService.findByTicketingOrder(orderId);
            sendGridMailPrepareService.sendTicketingPurchaseEmail(event, ticketingOrder.getPurchaser(), eventTicketsListForEmail, orderId, cardType, lastFour, ticketingOrder.getOrderDate(),
                    ticketingHelperService.getTicketOrderHeaderText(event),
                    ticketing.getEventStartDate(), ticketing.getEventEndDate(), ticketing.getEventAddress(), true, false, Optional.empty(), true, false, false,0L,false, null);
        } catch (JAXBException | DocumentException | TemplateException | IOException e) {
            log.error(e.getMessage());
        }
    }

    protected String getSuccessMessage(long newRecurringEventId, Event event, TicketingOrder ticketingOrder, List<TicketDetailDto> oldRecurOrderTicketType) {
        String success = Constants.STRING_EMPTY;
        Long oldRecurringEventId = oldRecurOrderTicketType.get(0).getRecurringEventId();
        Optional<RecurringEvents> oldRecurringEventOpt = recurringEventsRepository.findById(oldRecurringEventId);
        Optional<RecurringEvents> newRecurringEventOpt = recurringEventsRepository.findById(newRecurringEventId);
        if(oldRecurringEventOpt.isPresent() && newRecurringEventOpt.isPresent()){
            RecurringEvents newRe = newRecurringEventOpt.get();
            RecurringEvents oldRe = oldRecurringEventOpt.get();
            String oldStartDate = StringTools.formatCalanderDate(TimeZoneUtil.getDateInLocal(oldRe.getRecurringEventStartDate(), event.getEquivalentTimeZone()), true, true);
            String oldStartTime =  StringTools.formatCalanderTime(TimeZoneUtil.getDateInLocal(oldRe.getRecurringEventStartDate(), event.getEquivalentTimeZone()), false);
            String newStartDate = StringTools.formatCalanderDate(TimeZoneUtil.getDateInLocal(newRe.getRecurringEventStartDate(), event.getEquivalentTimeZone()), true, true);
            String newStartTime =  StringTools.formatCalanderTime(TimeZoneUtil.getDateInLocal(newRe.getRecurringEventStartDate(), event.getEquivalentTimeZone()), false);
            success = Constants.ORDER_TRANSFER_SUCCESSFULLY.replace("${order_id}",String.valueOf(ticketingOrder.getId()))
                    .replace("${old_event_date}", oldStartDate.concat(" ").concat(oldStartTime))
                    .replace("${new_event_date}", newStartDate.concat(" ").concat(newStartTime));
        }
        return success;
    }

    private TicketingType getNewRecurringTicketingType(long newRecurringEventId, TicketDetailDto oldRecurringEventTicketType) {
        TicketingType newRecurringEventTicketType = null;
        if(oldRecurringEventTicketType.getCreatedFrom() != -1) {
            newRecurringEventTicketType = ticketingtypeRepository.
                    findTicketTypeWithSameCreatedFromAndInRecurringEventId(oldRecurringEventTicketType.getCreatedFrom(), newRecurringEventId);
        }
        if (null == newRecurringEventTicketType) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TYPE_NOT_EXIST);
        }
        return newRecurringEventTicketType;
    }

    private List<String> updateCountSeatNumbers(long newRecurringEventId, Event event,
                                                Map<Long, List<String>> mapOldTTvsNewSeats,
                                                TicketDetailDto oldTicketType) {

        List<EventTickets> eventTicketsList = eventTicketService.findByTicketingOrderAndTicketingTypeIdAndRecurringEvent(oldTicketType.getOrderId(),oldTicketType.getTicketTypeId(), oldTicketType.getRecurringEventId());
        List<String> seatNumbersToBeBook = mapOldTTvsNewSeats.get(oldTicketType.getTicketTypeId());

        String newEventKey = TicketingUtils.getEventKey(event.getEventId(), true, newRecurringEventId);
        seatsIoService.bookTicketsInSeatsIo(newEventKey ,seatNumbersToBeBook);

        String oldEventKey = TicketingUtils.getEventKey(event.getEventId(), true, oldTicketType.getRecurringEventId());
        List<String> releaseTickets = GeneralUtils.convertCommaSeparatedToList(oldTicketType.getSeatNumber());
        seatsIoService.changeStatus(oldEventKey, releaseTickets, FREE);

        for(int i =0; i< eventTicketsList.size(); i++){
            eventTicketsList.get(i).setSeatNumber(seatNumbersToBeBook.get(i));
            eventTicketsList.get(i).setSeatNumberDisplay(seatNumbersToBeBook.get(i));
        }

        eventTicketsRepository.saveAll(eventTicketsList);

        return seatNumbersToBeBook;
    }

    protected void checkQuantityAvailableToTransfer(TicketingType TicketTypeNew, TicketDetailDto dto) {

        if(dto.getNumberOfTicket() >= ticketingStatisticsService.getRemainingTicketCount(TicketTypeNew)){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_NOT_AVAILABLE);
        }
    }

    protected void checkDisableRecurringEvent(Event event) {
        BigInteger count = recurringEventsRepository.countByEventId(event);
        if(count ==null || count.intValue() <=0){
            disableRecurringEvent(event);
        }
    }

    protected void cancelEvent(Long id){
        Optional<RecurringEvents> recurringEvents = recurringEventsRepository.findById(id);
        this.cancelEvent(id, recurringEvents);
    }
    protected void cancelEvent(Long id, Optional<RecurringEvents> recurringEvents) {
        if(recurringEvents.isPresent()){
            boolean sold = eventTicketsRepoService.checkRecurringTicketSold(id);
            RecurringEvents recurringEvents1 = recurringEvents.get();
            if (!sold) {
                recurringEvents1.setStatus(RecurringEvents.RecurringEventStatus.CANCEL);
            } else {
                recurringEvents1.setRecurringEventScheduleId(null);
            }
            recurringEventsRepository.save(recurringEvents1);
        }
    }



    public RecurringEvents findRecurringEventByDateAndTime(Date startDate, Date endDate, Event event){
        return recurringEventsRepository.findByStartDateAndEndDate(startDate, endDate, event);
    }

    protected void disableRecurringEvent(Event event) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        if (ticketing.isRecurringEvent()) {
            ticketing.setRecurringEvent(false);
            ticketingService.save(ticketing);
        }
    }

    @Override
    public Page<RecurringEvents> getUpcomingRecurringEvents(Event event, int page, int size) {
        return recurringEventsRepository.getUpcomingRecurringEvents(new Date(), event,
                PageRequest.of(page, size, Sort.Direction.ASC, "recurringEventStartDate"));

    }

    @Override
    public Page<RecurringEvents> getUpcomingRecurringEvents(Event event, Date startDate, int page, int size) {
        return recurringEventsRepository.getUpcomingRecurringEvents(startDate, event,
                PageRequest.of(page, size, Sort.Direction.ASC, "recurringEventStartDate"));

    }

    @Override
    public Page<RecurringEvents> getPastRecurringEvents(Event event, int page, int size) {
        return recurringEventsRepository.getPastRecurringEvents(new Date(), event,
                PageRequest.of(page, size, Sort.Direction.DESC, "recurringEventEndDate"));
    }

    @Override
    public List<RecurringEvents> getEndedRecurringEventsByPayoutStatus(List<EnumEventPayoutStatus> notPaidPayoutStatus, Date endDate){
        return recurringEventsRepository.getEndedRecurringEventsByPayoutStatus( notPaidPayoutStatus, endDate);
    }

    @Override
    @Transactional
    public RecurringEventSchedule save(RecurringEventSchedule recurringEventSchedule) {
        return recurringEventsScheduleRepository.save(recurringEventSchedule);
    }

    @Override
    public void saveAll(List<RecurringEventSchedule> recurringEventSchedules) {
        recurringEventsScheduleRepository.saveAll(recurringEventSchedules);
    }

    @Override
    public void deleteByIdIn(List<Long> scheduleIds) {
        recurringEventsScheduleRepository.deleteByIdIn(scheduleIds);
    }

    @Override
    @Transactional
    public RecurringEvents saveDuplicateRecurringEvent(RecurringEvents recurringEvents) {
        return recurringEventsRepository.save(recurringEvents);
    }

    @Override
    public List<RecurringEventSchedule> getRecurringSchedulerIdByOldEvent(Event oldEvent) {
        return recurringEventsScheduleRepository.findRecurringScheduleIdByEvent(oldEvent);
    }

    @Override
    public List<RecurringEvents> getCustomRecurringEventsByOldEvent(Event oldEvent) {
        return recurringEventsRepository.findByEventIdAndRecurringEventScheduleIdIsNullOrderByRecurringEventStartDateAsc(oldEvent);
    }

    @Override
    public List<Object[]> findAllCanceldRecurringEvents() {
        return recurringEventsRepository.findByCanceldRecurringEevnts();
    }

   @Override
    public void deleteRecurringEventsScheduleByIdForSwichRecurringEvent(Event event, Ticketing ticketing, List<RecurringEvents> recurringEventsList) {

        List<Long> recurringEventsToBeDelete = recurringEventsList.stream().map(RecurringEvents::getId).collect(Collectors.toList());

        if (!recurringEventsToBeDelete.isEmpty()) {
            deleteCouponCodeByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            deleteAccessCodeByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            deleteTicketHolderRequiredAttribute(recurringEventsToBeDelete, event);
            deleteRegistrationAttribute(recurringEventsToBeDelete, event);
            deleteTicketingTypesByListOfRecurringEventsIds(recurringEventsToBeDelete);
            embedWidgetSettingsByListOfReccurringEventsIds(recurringEventsToBeDelete);
        }

        List<Long> scheduleIds = recurringEventsList.stream().map(e-> e.getRecurringEventScheduleId()).collect(Collectors.toList());

        recurringEventsMainScheduleService.updateTicketingBasedOnLastRecurringEvent(event);

        recurringEventsRepository.deleteAll(recurringEventsList);
        recurringEventsScheduleRepository.deleteByIdIn(scheduleIds);

        updateTicketingForRecurringEventToSingleDayEvent(ticketing);

        ticketingTypeService.updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(ticketing);

        seatsIoForMultipleEventsImpl.createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());

    }

    private void embedWidgetSettingsByListOfReccurringEventsIds(List<Long> recurringEventsToBeDelete) {
        embedWidgetSettingService.deleteByRecurringIds(recurringEventsToBeDelete);
    }

    private void updateTicketingForRecurringEventToSingleDayEvent(Ticketing ticketing) {
        ticketing.setRecurringEvent(false);
        ticketing.setEventStartDate(DateUtils.addDaysWithoutTime(new Date(), 30));
        ticketing.setEventEndDate(DateUtils.addDaysWithoutTime(new Date(), 60));
        ticketingService.save(ticketing);
    }

    @Override
    public List<RecurringEvents> getRecurringEventsByEvent(Event event) {
        return recurringEventsRepository.findByEventId(event);
    }
    @Override
    public void deleteScheduledResendTicketingEmails(Event event,List<Long> recurringEventsToBeDelete){
      if(!CollectionUtils.isEmpty(recurringEventsToBeDelete)){
          try {
              log.info("deleteScheduledResendTicketingEmails | recurringEventsToBeDelete {}", recurringEventsToBeDelete);
              List<ResendTicketingEmail> resendTicketingEmailList = resendTicketingEmailService.findByEventIdAndRecurringEventIdInAndResendTicketStatus(event.getEventId(), recurringEventsToBeDelete, ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
              if (!CollectionUtils.isEmpty(resendTicketingEmailList)) {
                  resendTicketingEmailList.forEach(e -> e.setResendTicketStatus(ResendTicketingEmail.ResendTicketingEmailStatus.DELETED));
                  resendTicketingEmailService.saveAll(resendTicketingEmailList);
              }
          } catch(Exception e){
              log.info("deleteScheduledResendTicketingEmails | recurringEventsToBeDelete {},exception {}", recurringEventsToBeDelete,e.getMessage());
          }
      }
    }

    @Override
    public List<RecurringEvents> getRecurringEventsByStatus(Event event) {
        return  recurringEventsRepository.findByEventsByStatus(event);
    }

    private void deleteRegistrationAttribute(List<Long> recurringEventsToBeDelete, Event event) {
        registrationAttributeRepository.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
    }
}
