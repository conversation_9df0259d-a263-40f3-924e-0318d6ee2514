package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.billing.chargebee.enums.ChargebeeEntitlements;
import com.accelevents.billing.chargebee.service.ChargeBeeHelperService;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.billing.chargebee.service.TeamService;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.column.selection.services.AnalyticsUserColumnSelectionService;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.KeyValue;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.dto.lambda.ReminderEmailLambdaMessage;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG;
import com.accelevents.exceptions.NotAcceptableException.TicketHolderAttributesMsg;
import com.accelevents.exceptions.NotAcceptableException.TicketingExceptionMsg;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.TwilioTextMessagePrepareService;
import com.accelevents.perfomance.dto.TicketingBuyerData;
import com.accelevents.perfomance.dto.TicketingBuyerDataWithAmount;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.repositories.*;
import com.accelevents.repository.ROWhiteLabelSettingRepository;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneInterestService;
import com.accelevents.services.repo.helper.*;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.dto.IdNameDto;
import com.accelevents.session_speakers.services.KeyValueService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.ticketing.TicketingBeeFreeDto;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.TimeZone;
import com.accelevents.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.DocumentException;
import freemarker.template.Configuration;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import seatsio.charts.Category;
import seatsio.events.TableBookingMode;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.EnumKeyValueType.TAG;
import static com.accelevents.domain.enums.EnumKeyValueType.TRACK;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.*;
import static com.accelevents.utils.TicketingUtils.getEventKey;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static com.accelevents.utils.TimeZoneUtil.getNameByEquivalentTimeZone;
import static java.util.stream.Collectors.*;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class TicketingServiceImpl implements TicketingService {

	private static final Logger log = LoggerFactory.getLogger(TicketingServiceImpl.class);

	@Autowired
	private TicketingHelperService ticketingHelperService;

	@Autowired
	private EventTicketsCommonRepo eventTicketsCommonRepo;

	@Autowired
	private TicketingRepository ticketingRepository;

    @Autowired
    private ROWhiteLabelSettingRepository roWhiteLabelSettingRepository;
	@Autowired
	private TicketingTypeRepository ticketingTypeRepository;

	@Autowired
	private TicketingTypeCommonRepo ticketingTypeCommonRepo;

	@Autowired
	private TicketingTypeService ticketingTypeService;

    @Autowired
    private TicketTypeTrackSessionLimitsService ticketTypeTrackSessionLimitsService;

	@Autowired
	private TicketingTypeTicketService ticketingTypeTicketService;

	@Autowired
	private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

	@Autowired
	private AllRequiresAttributesService allRequiresAttributesService;

	@Autowired
	private TicketHolderAttributesService ticketHolderAttributesService;

	@Autowired
	private EventTicketsService eventTicketsService;

	@Autowired
	private CommonEventService commonEventService;

	@Autowired
	private EventTicketsRepoService eventTicketsRepoService;

	@Autowired
	private EventCommonRepoService eventCommonRepoService;

	@Autowired
	private TicketingOrderService ticketingOrderService;

    @Autowired
    private StripeTransactionService stripeTransactionService;

    @Autowired
    private OrderAuditLogRepoService orderAuditLogRepoService;

	@Autowired
	private TicketingOrderRepoService ticketingOrderRepoService;

	@Autowired
	private TicketingOrderManagerService ticketingOrderManagerService;

	@Autowired
	private StripeService stripeService;
    @Autowired
    private ROStripeService roStripeService;

	@Autowired
	private EventService eventService;

	@Autowired
	private StaffService staffService;

	@Autowired
	private TicketingCouponService ticketingCouponService;

	@Autowired
	private SendGridMailPrepareService sendGridMailPrepareService;

	@Autowired
	private TwilioTextMessagePrepareService twilioTextMessagePrepareService;

	@Autowired
	private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

	@Autowired
	private CustomEmailService customEmailService;

	@Autowired
	private UserService userService;
    @Autowired
    private ROUserService roUserService;

	@Autowired
	private EventDesignDetailService eventDesignDetailService;

	@Autowired
	private ImageConfiguration imageConfiguration;

	@Autowired
	private PaymentHandlerService paymentHandlerService;

	@Autowired
	private SeatsIoService seatsIoService;

	@Autowired
	private TicketingAccessCodeService ticketingAccessCodeService;

	@Autowired
	private TicketingAccessCodeRepository ticketingAccessCodeRepository;

	@Autowired
	private TicketingCouponRepository ticketingCouponRepository;

	@Autowired
	private TicketHolderAttributesAuditService ticketHolderAttributesAuditService;

	@Autowired
	private RecurringEventsScheduleBRService recurringEventsScheduleService;

	@Autowired
	private RecurringEventsMainScheduleService recurringEventsMainScheduleService;

	@Autowired
	private RecurringEventsRepository recurringEventsRepository;

	@Autowired
	private SeatingCategoryRepository seatingCategoryRepository;

	@Autowired
	private SeatsIoForMultipleEventsImpl seatsIoForMultipleEventsImpl;

	@Autowired
	private TicketingStatisticsService ticketingStatisticsService;

	@Autowired
	private Configuration freemarkerMailConfiguration;

	@Autowired
	private TicketingDisplayService ticketingDisplayService;

	@Autowired
	private SalesTaxService salesTaxService;
    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;

	@Autowired
    private EventDesignDetailRepository eventDesignDetailRepository;

	@Autowired
    private TableRefundService tableRefundService;

	@Autowired
	private EventChecklistService eventChecklistService;

	@Autowired
	private SessionRepoService sessionRepoService;

	@Autowired
	private SessionService sessionService;

    @Autowired
    private EventLimitRegistrationDomainService eventLimitRegistrationDomainService;

	@Autowired
    private ChallengeConfigService challengeConfigService;

	@Autowired
    private EventRepoService eventRepoService;

    @Autowired
    private EventTicketTransactionService eventTicketTransactionService;

	@Autowired
    private ChargebeeService chargebeeService;

	@Autowired
    private ChargebeePlanService chargebeePlanService;

	@Autowired
    private BeeFreeRepository beeFreeRepository;

	@Autowired
    private TransactionFeeConditionalLogicRepository transactionFeeConditionalLogicRepository;

	@Autowired
    private TicketingTypeTagAndTrackRepo ticketingTypeTagAndTrackRepo;
	@Autowired
    private KeyValueService keyValueService;

	@Autowired
    private BadgesService badgesService;
    @Autowired
    private VatTaxService vatTaxService;

    @Autowired
    private AnalyticsUserColumnSelectionService analyticsUserColumnSelectionService;

    @Autowired
    private IntegrationService integrationService;

    @Autowired
    private NeptuneInterestService interestService;

    @Autowired
    private VirtualEventService virtualEventService;

    @Autowired
    private RegistrationRequestsService registrationRequestsService;

    @Autowired
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Autowired
    private SeatingCategoryService seatingCategoryService;

    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private  ContactModuleSettingsService contactModuleSettingsService;

    @Autowired
    ResendTicketingEmailService resendTicketingEmailService;
    @Autowired
    private ChargeBeeHelperService chargeBeeHelperService;

    @Autowired
    private  TicketingManageService ticketingManageService;

    @Autowired
    private EventTicketTransferDetailService eventTicketTransferDetailService;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private TicketingOrderTransferServiceImpl ticketingOrderTransferService;
    @Autowired
    private SqsClient sqsClient;
    @Autowired
    private JoinBadgeTicketTypesService joinBadgeTicketTypesService;
    @Autowired
    private StripeCustomerService stripeCustomerService;

    @Autowired
    private FormRulesService formRulesService;

    @Autowired
    TicketExchangeRuleService ticketExchangeRuleService;

    @Autowired
    ROStaffService roStaffService;

//	@Autowired
//	private AsyncService asyncService;//nosonar


	@Value("${uiBaseurl}")
	private String uiBaseurl;
	@Value("${cloud.aws.s3.bucket.ticketBuyerUploads.url}")
	private String ticketBuyerUploadsUrl;
    @Value("${image.url}")
    private String imageUrl;

    @Value("${imageLogo.url}")
    private String imageLogoUrl;
    @Value("${aws.sqs.generate.reminder.email}")
    private String generateReminderEmailQueue;
    @Autowired
    private ConfirmationEmailRepository confirmationEmailRepository;

	@Override
	public Ticketing save(Ticketing ticketing) {
		Ticketing savedTicketing = ticketingRepository.save(ticketing);

        clearAPIGatewayCache.clearAPIGwTicketingCache(ticketing);
        clearAPIGatewayCache.clearAPIGwBeeFreePageCacheByEvent(ticketing.getEventid());
        clearAPIGatewayCache.clearAPIGwTicketTypesRemainingCountCache(ticketing.getEventid().getEventURL());
		return savedTicketing;
	}

	@Override
	public Optional<Ticketing> findById(Long ticketingId) {
		return ticketingRepository.findById(ticketingId);
	}

	@Override
	public Ticketing findByEvent(Event event){
		return ticketingRepository.findByEventid(event);
	}

	protected TicketAttributeValueDto unmashler(String xml, Unmarshaller unmarshaller) throws JAXBException {
		if (unmarshaller == null) {
			throw new JAXBException("unmarshaller not found!");
		}

		StringReader reader = new StringReader(xml);
		return (TicketAttributeValueDto) unmarshaller.unmarshal(reader);
	}

    @Autowired
    private TeamService teamService;
    @Autowired
    private EventLimitRegistrationDomainRepository eventLimitRegistrationDomainRepository;


    @Override
	public List<TicketHolderRequiredAttributes> getTicketingAttributes(Event event, Long recurringEventId) {
		Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());
			if(ticketing.isRecurringEvent() && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
				List<TicketHolderRequiredAttributes> ticketHolderAttributes = ticketHolderRequiredAttributesService.getAllAttributesExcludingSubQueByRecurringEventId(event, recurringEventId, DataType.TICKET);

				return  CollectionUtils.isEmpty(ticketHolderAttributes) ? Collections.emptyList() : ticketHolderAttributes;
			}


		List<TicketHolderRequiredAttributes> ticketHolderAttributes = ticketHolderRequiredAttributesService.getAllAttributes(event);
        return CollectionUtils.isEmpty(ticketHolderAttributes)
                ? ticketingHelperService.addDefaultAttributes(event)
                : ticketHolderAttributes.stream().filter(e-> !NumberUtils.isNumberGreaterThanZero(e.getParentQuestionId())).collect(Collectors.toList());
	}


	@Override
	public TicketingBuyerDataWithAmount getAllTicketBuyers(Event event, PageSizeSearchObj pageSizeSearchObj, Long recurringEventId, DataType dataType) {
		TicketingBuyerDataWithAmount buyerDataWithAmount = new TicketingBuyerDataWithAmount();
		List<TicketingBuyerData> data = new ArrayList<>();
		long startTime = System.currentTimeMillis();
		Page<TicketingBuyerDataFromDB> ticketingSaleData;
		if(StringUtils.isNoneBlank(pageSizeSearchObj.getSearch())){
			if(null != recurringEventId){
				recurringEventId = 0L;
			}
            ticketingSaleData = eventCommonRepoService.getTicketBuyerDataSearch(event.getEventId(), pageSizeSearchObj.getSearchWithEscapeSpecialChars(), recurringEventId, dataType, pageSizeSearchObj.getPageable());
        } else {
			ticketingSaleData = this.commonEventService.getBuyerData(event.getEventId(), pageSizeSearchObj.getPageable(), recurringEventId, dataType);
		}
        List<Long> orderIds = ticketingSaleData.getContent().stream().map(TicketingBuyerDataFromDB::getOrderId).collect(toList());
        Map<Long, PurchaserInfo> purchaserInfoByOrderId = ticketingHelperService.getPurchaserInfoyByOrderIds(orderIds, event);

		long dbTimeEnds = System.currentTimeMillis();
		log.info("getAllTicketBuyers DB Query time taken -> ={}" , (dbTimeEnds-startTime));
		for (TicketingBuyerDataFromDB ticketingOrder : ticketingSaleData.getContent()) {
			TicketingBuyerData ticketingBuyerData = new TicketingBuyerData();
			ticketingBuyerData.setOrderNo(ticketingOrder.getOrderId());
			PurchaserInfo purchaserInfo = purchaserInfoByOrderId.get(ticketingOrder.getOrderId());
			if(purchaserInfo != null) {
			    ticketingBuyerData.setTicketBuyerName(purchaserInfo.getFirstName() + Constants.STRING_BLANK + purchaserInfo.getLastName());
            }
			ticketingBuyerData.setOrderDate(getDateInLocal(ticketingOrder.getOrderDate(),
					event.getEquivalentTimeZone(), Constants.DATE_FORMAT_WITH_AM_PM));

			if (ticketingOrder.getCancelCount() == ticketingOrder.getTotalTicketCount()) {
				ticketingBuyerData.setPaymentMode(CANCELED);
			} else if(ticketingOrder.getRefundCount() == ticketingOrder.getTotalTicketCount()){
                ticketingBuyerData.setPaymentMode(REFUNDED);
            } else if(ticketingOrder.getRefundCount() >0 || ticketingOrder.getCancelCount() >0){
                ticketingBuyerData.setPaymentMode(PARTIALLY_REFUNDED);
            }else {
				ticketingBuyerData.setPaymentMode(ticketingOrder.getOrderStatus().toString());
			}
			ticketingBuyerData.setQuantity(ticketingOrder.getTotalTicketCount());
			ticketingBuyerData.setOrderAmount(ticketingOrder.getOrderPaidAmount());
			ticketingBuyerData.setRefundedAmount(ticketingOrder.getOrderRefundedAmount());
			data.add(ticketingBuyerData);
		}
		long loopEndTime = System.currentTimeMillis();
		log.info("getAllTicketBuyers loop ends -> ={}" , (loopEndTime - dbTimeEnds));
		buyerDataWithAmount.setBuyerData(data);
		buyerDataWithAmount.setRecordsFiltered(ticketingSaleData.getNumberOfElements());
		buyerDataWithAmount.setRecordsTotal((int) ticketingSaleData.getTotalElements());
		log.info("getAllTicketBuyers ended on -> ={}" , (System.currentTimeMillis()-startTime));
		return buyerDataWithAmount;
	}


	protected void setGrossSaleAndNetSale(TicketingBuyerDataWithAmount buyerDataWithAmount, Event event,
										  Long recurringEventId, DataType dataType){
		double grossAmount = 0;
		double totalFees = 0;

		StripeDTO stripe = stripeService.getStripeFeesByEvent(event);

		PageRequest.of(0, Integer.MAX_VALUE);
		Page<TicketingBuyerDataFromDB> ticketingSaleData = this.commonEventService.getBuyerData(event.getEventId(), PageRequest.of(0, Integer.MAX_VALUE), recurringEventId, dataType);
		for (TicketingBuyerDataFromDB ticketingBuyerData : ticketingSaleData.getContent()) {

			double orderGrossAmount = ticketingBuyerData.getOrderPaidAmount()
					- ticketingBuyerData.getOrderRefundedAmount();
			if (orderGrossAmount > 0) {
				totalFees = totalFees + getFee(ticketingBuyerData, stripe);
			}
			grossAmount = grossAmount + orderGrossAmount;
		}

		buyerDataWithAmount.setGrossAmount(grossAmount);
		buyerDataWithAmount.setNetAmount(GeneralUtils.getRoundValue(grossAmount - totalFees));
	}

	public double getFee(TicketingBuyerDataFromDB ticketingOrder, StripeDTO stripe) {
		double ccFee = 0;
		double refundedCCFee = 0;
		double remainCcFee;

		if(ticketingOrder.getOrderAEFee() > 0){
			ccFee = ((ticketingOrder.getOrderPaidAmount() * stripe.getCCPercentageFee()) / 100) + stripe.getCCFlatFee();
			refundedCCFee = (ticketingOrder.getOrderRefundedAmount() * ccFee) / ticketingOrder.getOrderPaidAmount();
		}
		remainCcFee= EnumPaymentGateway.STRIPE.name().equals(stripe.getPaymentGateway()) ? ccFee : (ccFee - refundedCCFee);
		return (ticketingOrder.getOrderAEFee() - ticketingOrder.getRefundedAEFee()) +remainCcFee
				+ (ticketingOrder.getOrderWLFee() - ticketingOrder.getTotalRefundedWLFee())
				+ (ticketingOrder.getOrderSalesTaxFee() - ticketingOrder.getRefundedSalesTaxFee());
	}

    public double getFeeTicketWise(TicketingBuyerDataFromDB ticketingOrder, double ccPercentageFee, double ccFlatFee, String paymentGateway) {
        double ccFee = 0;
        double refundedCCFee = 0;
        double remainCcFee;

        if(ticketingOrder.getOrderAEFee() > 0){
            ccFee = ((ticketingOrder.getOrderPaidAmount() * ccPercentageFee) / 100) + ccFlatFee;
            refundedCCFee = (ticketingOrder.getOrderRefundedAmount() * ccFee) / ticketingOrder.getOrderPaidAmount();
        }
        remainCcFee= EnumPaymentGateway.STRIPE.name().equals(paymentGateway) ? ccFee : (ccFee - refundedCCFee);
        return (ticketingOrder.getOrderAEFee() - ticketingOrder.getRefundedAEFee()) +remainCcFee
                + (ticketingOrder.getOrderWLFee() - ticketingOrder.getTotalRefundedWLFee())
                + (ticketingOrder.getOrderSalesTaxFee() - ticketingOrder.getRefundedSalesTaxFee());
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void deleteEventType(long ticketingTypeId, Event event) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        List<TicketingType> ticketingTypeCreated = ticketingTypeService.getTicketTypeByCreateFrom(ticketingTypeId);
        TicketingType ticketingType = ticketingTypeService.findByidAndEvent(ticketingTypeId, event);
        if (ticketingType == null) {
            log.info("There are no ticketing types with the ticketingTypeId {} in the event eventId {} to delete", ticketingTypeId, event.getEventId());
            return;
        }
        ticketingManageService.updateAddOnTicketTypesData(ticketingType, event, DELETE, ticketing);
        ticketingManageService.updateDisplayCodeTicketTypesData(ticketingType, event, DELETE);
        ticketingManageService.updateCustomLobbyTabData(ticketingType,event,DELETE);
        if (!ticketingTypeCreated.isEmpty()) {
            for (TicketingType localTicketingType : ticketingTypeCreated) {
                deleteRecurringEventTicketingTypes(event, localTicketingType, ticketing);
            }
        }
        if(DataType.TICKET.equals(ticketingType.getDataType())) {
            eventLimitRegistrationDomainService.removeTicketTypesFromEventLimitRegistrationDomain(event, ticketingTypeId);
        }
        Long categoryId = ticketingType.getCategoryId();
        long registration = registrationRequestsService.getCountOfTicketingTypes(ticketingType.getId());
        if (registration > 0) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_HAS_REGISTRATION_REQUEST);
        }
        deleteTicketingType(event, ticketingType);
        // Update ticket added checklist flag
        setTicketAddedChecklistFlag(event);
        deleteCategory(categoryId);
        //Remove Ticketing Type Id From Badges
        removeTicketingTypeIdsFromBadges(event, ticketingType.getId());
        //Remove Ticketing Type Id From Integration Settings (Cvent,Tray)
        removeTicketingTypeIdsFromIntegration(ticketingType.getId());

        mapTicketTypes(event, ticketingType, TrayIOWebhookActionType.DELETE_TICKET_TYPES);

        // Remove ticket exchange mapping
        ticketExchangeRuleService.markTicketExchangeMappingRuleStatusDeletedByTicketingTypeId(ticketingTypeId);
    }

    @Override
    public void mapTicketTypes(Event event, TicketingType createTicket, TrayIOWebhookActionType actionType) {
        Integration integration = getIntegrationDetails(event);
        if(integration != null){
            trayIntegrationService.mapTicketTypes(integration, event, createTicket, actionType);
            trayIntegrationService.mapEventDetails(integration, event,  TrayIOWebhookActionType.MAP_EVENT);
        }
    }

    private Integration getIntegrationDetails(Event event){
        Optional<Integration> integrationOptional = Optional.empty();
        if (event.getWhiteLabel() != null) {
            log.info("request for update event details in custom integration WhiteLabel {}", event.getWhiteLabelId());
            integrationOptional = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);

        } else if (event.getOrganizer() != null) {
            log.info("request for update event details in custom integration Organizer {}", event.getOrganizerId());
            integrationOptional = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getOrganizer().getId(), IntegrationSourceType.ORGANIZER, IntegrationType.TRAY_IO);
        }

        return integrationOptional.orElse(null);
    }
    public void deleteCategory(Long categoryId) {
        if (null != categoryId) {
            List<TicketingType> list = ticketingTypeService.findAllTicketingTypeByCategoryId(categoryId);
            if (list.isEmpty()) {
                SeatingCategories category = seatingCategoryRepository.findOneById(categoryId);
                if (null != category) {
                    category.setRecordStatus(RecordStatus.DELETE);
                    seatingCategoryRepository.save(category);
                }
            }
        }
    }

    private void removeTicketingTypeIdsFromIntegration(long ticketingTypeId) {
        log.info("TicketingServiceImpl || removeTicketingTypeIdsFromIntegration || ticketingTypeId {}",ticketingTypeId);
	    List<Integration> integrations = integrationService.getAllIntegrationDetailsListByTicketingTypeId(ticketingTypeId);
        log.info("TicketingServiceImpl || removeTicketingTypeIdsFromIntegration || integrationList {}",integrations);
        integrations.forEach(e->e.setTicketingType(null));
        integrationService.saveAll(integrations);
        log.info("TicketingServiceImpl || removeTicketingTypeIdsFromIntegration ||finalResultIntegrationList {}",integrations);
    }

	protected void removeTicketingTypeIdsFromBadges(Event event,Long ticketingTypeId){
        joinBadgeTicketTypesService.deleteByTicketTypeId(ticketingTypeId);
    }

	protected void deleteSessionTicketingType(Event event, long ticketingTypeId) {
		List<Session> sessionList = sessionRepoService.getAllSessionByEventId(event, null,true);

		sessionList.forEach(session -> {
			List<Long> ticketingTypeList = GeneralUtils.convertCommaSeparatedToListLong(session.getTicketTypesThatCanBeRegistered());
			List<Long> newTicketingType = new ArrayList<>();
			for (Long ticketingType : ticketingTypeList) {
				if (ticketingType != ticketingTypeId) {
					newTicketingType.add(ticketingType);
				}
			}
			session.setTicketTypesThatCanBeRegistered(GeneralUtils.convertLongListToCommaSeparated(newTicketingType));
		});
		sessionRepoService.saveAll(sessionList);
	}

	private void  setTicketAddedChecklistFlag(Event event){
		long ticketCount = ticketingTypeService.getTicketTypeCountByEventId(event);
		if(ticketCount == 0){
			updateTicketAddedChecklistFlag(event, false);
		}
	}

	protected void deleteRecurringEventTicketingTypes(Event event, TicketingType ticketingType, Ticketing ticketing){
		checkTicketTypeExist(ticketingType);

		if (!eventCommonRepoService.isTypePurchased(ticketingType)
				&& !ticketingOrderManagerService.isTicketingTypeExist(ticketingType)) {
			deleteTicketingTypesAndReleaseSeatsIo(event, ticketing, ticketingType);
			checkFreeTicketingTypesAvailableThenSetModuleActivated(event, ticketingType.getDataType());

		} else {
			ticketingType.setCreatedFrom(-1L);
			ticketingTypeService.setPositionForTicketingTypeAndsaveTicketingType(ticketingType);
		}
        //remove all the recurring ticket type from the all sessions either it's purchased or not as we don't provide any option to remove the recurring ticket type from the session
        sessionService.updateTicketTypesThatCanBeRegistered(ticketingType.getId(), event);
        if(DataType.TICKET.equals(ticketingType.getDataType())) {
            eventLimitRegistrationDomainService.removeTicketTypesFromEventLimitRegistrationDomain(event, ticketingType.getId());
        }
	}

	protected void deleteTicketingType(Event event, TicketingType ticketingType) {
		checkTicketTypeExist(ticketingType);
		if (!eventCommonRepoService.isTypePurchased(ticketingType)
				&& !ticketingOrderManagerService.isTicketingTypeExistWithNotDeletedOrder(ticketingType)) {
			sessionService.updateTicketTypesThatCanBeRegistered(ticketingType.getId(), event);
            challengeConfigService.updateTicketTypeInChallengeOnDelete(ticketingType,event);
			deleteTicketingTypesAndReleaseSeatsIo(event, ticketingType);
			deleteSessionTicketingType(event,ticketingType.getId());
			if(ticketingType.getCategoryId() != null) {
                Optional<SeatingCategories> categoryOptional = seatingCategoryRepository.findById(ticketingType.getCategoryId());
                if (categoryOptional.isPresent()) {
                    SeatingCategories seatingCategories = categoryOptional.get();
                    seatingCategories.setQuantity((int) (seatingCategories.getQuantity() - ticketingType.getNumberOfTickets()));
                    seatingCategoryRepository.save(seatingCategories);
                }
            }
		} else {
			throw new NotAcceptableException(TicketingExceptionMsg.TICKET_ALREADY_PURCHASED);
		}
		checkFreeTicketingTypesAvailableThenSetModuleActivated(event, ticketingType.getDataType());
	}

	protected void checkFreeTicketingTypesAvailableThenSetModuleActivated(Event event, DataType dataType) {
		List<TicketingType> ticketingTypeList = ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(dataType));
		boolean isFreeTicketTypeAvalable = ticketingTypeList.stream().allMatch(ticketing -> (ticketing.getPrice() <= 0));
        Ticketing ticketing = ticketingRepository.findByEventid(event);
		if (isFreeTicketTypeAvalable) {
			if(!ticketing.isOnlineEvent()){
				ticketing.setActivated(true);
			}
			this.save(ticketing);
		}
        log.info("In checkFreeTicketingTypesAvailableThenSetModuleActivated method Activating ticketing module for eventId {} and value of Activated flag {} ",event.getEventId(),ticketing.getActivated());
	}

	private void checkTicketTypeExist(TicketingType ticketingType) {
		if (ticketingType == null) {
			throw new NotAcceptableException(TicketingExceptionMsg.TICKET_TYPE_NOT_EXIST);
		}
	}

	protected void deleteTicketingTypesAndReleaseSeatsIo(Event event, TicketingType ticketingType) {
		ticketingOrderManagerService.deleteByTicketType(ticketingType);
		Ticketing ticketing = ticketingType.getTicketing();
		String chartKey = ticketing.getChartKey();

        if (null != ticketingType.getCategoryId()){
            seatingCategoryRepository.updateSeatingCategoriesToDELETE(ticketingType.getCategoryId());
        }


        ticketingTypeService.updateTicketingTypeRecordStatusToCancel(Collections.singletonList(ticketingType.getId()));

		if (isNotBlank(chartKey)) {
			Map<Long, Category> categoryIds = new HashMap<>();
			String firstRecId = recurringEventsScheduleService.getFirstRecId(event, ticketing);
			List<Category> remainingCategories = new ArrayList<>(categoryIds.values());
			seatsIoService.createOrUpdateChart(event.getEventId(), chartKey, remainingCategories, firstRecId);
		}
	}

	protected List<TicketingType> getTicketType(Event event, Long recurringEventId, boolean isRecurringEvent) {
		return isRecurringEvent
				? ticketingTypeTicketService.getAllTicketingTypesByRecuurringEvent(recurringEventId)
				: ticketingTypeRepository.findTicketTypeAsCategoryByEvent(event, TicketType.PAID);
	}

	// RemainingTT = Current != PassedTT.getId ; How can we Identify this is default or RecurrTT ?
	// So here we can only get all the  TT and passed same Like during saveTicketType
	protected void deleteTicketingTypesAndReleaseSeatsIo(Event event, Ticketing ticketing, TicketingType ticketingType) {

		ticketingOrderManagerService.deleteByTicketType(ticketingType);
		ticketingTypeService.delete(ticketingType);

		if (StringUtils.isNotEmpty(ticketing.getChartKey())) {
			Map<Long, List<CategoryDto>> categoryDtoMap = getLongListMapOfCategory(event, ticketingType.getId());
			categoryDtoMap.forEach((key, value) ->

				getChartKeyAfterSuccessfullyCreateOrUpdateSeatsIo(event, ticketing, ticketing.isRecurringEvent(), key, value)

			);
		}
	}

	public User getBuyer(EventTickets eventTickets){
		return eventTickets.getTicketingOrder().getPurchaser();
	}

	protected boolean handleAttendeeAttribute(String search, AttendeeDto attendeeDto, TicketHolderAttributes ticketHolderAttributes) {
		boolean addAttendee = true;
		TicketAttributeValueDto1 ticketAttributeValueDto1 = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
		Map<String, String> attributesMap = (Map<String, String>) ticketAttributeValueDto1.getHolder().get("attributes");
		if(isNotBlank(search) && !this.searchMapBuyerHolderMap(attributesMap,search)) {
			addAttendee = false;
		}
		if (attributesMap != null && !attributesMap.isEmpty()) {
			attendeeDto.setFirstName(attributesMap.get(STRING_FIRST_SPACE_NAME));
			attendeeDto.setLastName(attributesMap.get(STRING_LAST_SPACE_NAME));
		}
		return addAttendee;
	}


	public boolean searchMapBuyerHolderMap(Map<String,String> map, String searchKey){
		return map != null && ((map.containsKey(STRING_FIRST_SPACE_NAME) && map.get(STRING_FIRST_SPACE_NAME).toLowerCase().contains(searchKey.toLowerCase()))
				|| (map.containsKey(STRING_LAST_SPACE_NAME) && map.get(STRING_LAST_SPACE_NAME).toLowerCase().contains(searchKey.toLowerCase()))
				|| (map.containsKey(STRING_EMAIL_SPACE) && map.get(STRING_EMAIL_SPACE).toLowerCase().contains(searchKey.toLowerCase()))
				|| (map.containsKey(STRING_PHONE_NUMBER) && map.get(STRING_PHONE_NUMBER).toLowerCase().contains(searchKey.toLowerCase()))
		);
	}

	protected List<TicketingFeeDto> getTicketFeesLogic(Event event, List<DataType> dataType) {
	    boolean isAddonTicket = false;
        boolean isTicketAndAddon = dataType.contains(DataType.TICKET) && dataType.contains(DataType.ADDON);
	    if(dataType.contains(DataType.ADDON)){
            isAddonTicket = true;
        }
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics;
	    if(!EventFormat.HYBRID.equals(event.getEventFormat())) {
            if(isTicketAndAddon) {
                transactionFeeConditionalLogics = transactionFeeConditionalLogicRepository.findByEventAndIsVirtualEvent(event,
                        EventFormat.VIRTUAL.equals(event.getEventFormat()));
            } else {
                transactionFeeConditionalLogics = transactionFeeConditionalLogicRepository.findByEventAndIsVirtualEventAndIsAddon(event,
                        EventFormat.VIRTUAL.equals(event.getEventFormat()), isAddonTicket);
            }
        }
	    else {
            if(isTicketAndAddon) {
                transactionFeeConditionalLogics = transactionFeeConditionalLogicRepository.findByEvent(event);
            } else {
                transactionFeeConditionalLogics = transactionFeeConditionalLogicRepository.findByEventAndIsAddon(event, isAddonTicket);
            }
        }
        if (transactionFeeConditionalLogics.isEmpty()) {
            return setTicketingFeeDto(event);
        } else {
            List<TicketingFeeDto> ticketingFeeDtos = new ArrayList<>();
            CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);

            transactionFeeConditionalLogics.forEach(e ->
                ticketingFeeDtos.add(new TicketingFeeDto(e, creditCardChargesDto))
            );

            return ticketingFeeDtos;
        }
    }

    private List<TicketingFeeDto> setTicketingFeeDto(Event event) {
        CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);
        PlanConfig config ;
        if (event.getOrganizer() != null){
            config = event.getOrganizer().getPlanConfig();
        }else {
            config = chargebeePlanService.getFreePlanConfig();
        }
        List<TicketingFeeDto> ticketingFeeDtos = new ArrayList<>();
        PlanConfig planConfig = event.getWhiteLabel() != null ? event.getWhiteLabel().getPlanConfig() : config;
        if(event.getEventFormat() == null) {
            ticketingFeeDtos.add(setTicketingFeeDto(event,
                    transactionFeeConditionalLogicService.getAeFeeFlat(true, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                    transactionFeeConditionalLogicService.getAeFeePercentage(true, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                    false, creditCardChargesDto));
            return ticketingFeeDtos;
        }
        switch (event.getEventFormat()){
            case VIRTUAL:
                ticketingFeeDtos.add(setTicketingFeeDto(event,
                        transactionFeeConditionalLogicService.getAeFeeFlat(true, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        transactionFeeConditionalLogicService.getAeFeePercentage(true, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        false, creditCardChargesDto));
                break;
            case IN_PERSON:
                ticketingFeeDtos.add(setTicketingFeeDto(event,
                        transactionFeeConditionalLogicService.getAeFeeFlat(false, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        transactionFeeConditionalLogicService.getAeFeePercentage(false, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        true, creditCardChargesDto));
                break;
            case HYBRID:
                ticketingFeeDtos.add(setTicketingFeeDto(event,
                        transactionFeeConditionalLogicService.getAeFeeFlat(false, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        transactionFeeConditionalLogicService.getAeFeePercentage(false, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        true, creditCardChargesDto));
                ticketingFeeDtos.add(setTicketingFeeDto(event,
                        transactionFeeConditionalLogicService.getAeFeeFlat(true, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        transactionFeeConditionalLogicService.getAeFeePercentage(true, planConfig, GREATER_THAN_EQUAL_TO, event.getBillingType(), event),
                        false, creditCardChargesDto));
                break;
        }
        return ticketingFeeDtos;
    }

    private TicketingFeeDto setTicketingFeeDto(Event event, double aeFlat, Double aePercentageFee, boolean isInPesron, CreditCardChargesDto creditCardChargesDto) {
        TicketingFeeDto ticketingFeeDto = new TicketingFeeDto();
        ticketingFeeDto.setAeFeeFlat(aeFlat);
        ticketingFeeDto.setAeFeePercentage(aePercentageFee);
        ticketingFeeDto.setInPerson(isInPesron);
        ticketingFeeDto.setCreditCardProcessingFlat(creditCardChargesDto.getCreditCardFlat());
        ticketingFeeDto.setCreditCardProcessingPercentage(creditCardChargesDto.getCreditCardPercentage());
        if (event.getWhiteLabel() != null) {
            ticketingFeeDto.setWlFeeFlat(WL_FEE_FLAT);
            ticketingFeeDto.setWlFeePercentage(WL_FEE_PERCENTAGE);
        } else {
            ticketingFeeDto.setWlFeeFlat(0);
            ticketingFeeDto.setWlFeePercentage(0);
        }
        return ticketingFeeDto;
    }

    @Override
	public List<Event> getListOfEventByEventIdsAndRecurringEnable(long from, long to) {
		return ticketingRepository.findListOfEventByEventIdsAndRecurringEnable(from, to, true);
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public CreateTicketingDto getTicketing(Event event,
										   Boolean fetchHiddenTicketsOnly,
										   Long recurringEventId,
										   List<DataType> dataTypes,boolean containsDonationType) {

		CreateTicketingDto ticketingDto = new CreateTicketingDto();
		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);

		ticketingDto.setEventAddress(ticketing.getEventAddress());
		ticketingDto.setLatitude(ticketing.getLatitude());
		ticketingDto.setLongitude(ticketing.getLongitude());

		EnumEventVenue enumEventVenue = getEventVenue(ticketing);
		ticketingDto.setEventVenuetype(enumEventVenue.name());

		ticketingDto.setEventStartDate(
				getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone(), null));
		ticketingDto.setEventEndDate(
				getDateInLocal(eventService.getEventEndDate(event), event.getEquivalentTimeZone(), null));

        RecurringEvents recurringEvents = null;
		if(ticketing.isRecurringEvent() && com.accelevents.utils.NumberUtils.isNumberGreaterThanZero(recurringEventId)){
			Optional<RecurringEvents> recurringEventsOpt = recurringEventsRepository.findById(recurringEventId);
			if(recurringEventsOpt.isPresent()) {
                recurringEvents = recurringEventsOpt.get();
				ticketingDto.setEventKey(event.getEventId() + STRING_DASH + recurringEvents.getId());
				boolean showRemainingTickets = (RecurringEvents.ShowRemainingTicketsForRecurringEvent.TRUE.equals(recurringEvents.getShowRemainingTicketsForRecurringEvent()) || RecurringEvents.ShowRemainingTicketsForRecurringEvent.CUSTOM_TRUE.equals(recurringEvents.getShowRemainingTicketsForRecurringEvent()));
				ticketingDto.setShowRemainingTickets(showRemainingTickets);
			}
		}else{
			ticketingDto.setEventKey(String.valueOf(event.getEventId()));
			ticketingDto.setShowRemainingTickets(ticketing.isShowRemainingTickets());
		}
		ticketingDto.setSeatingChartKey(ticketing.getChartKey());

		SalesTaxFeeDto salesTaxFeeAndTicketType = salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId());
		List<TicketingType> ticketingTypes = ticketingTypeService.getTicketingTypesOrAddOnTypes(event, fetchHiddenTicketsOnly, recurringEvents, ticketing, dataTypes);
        List<TicketingType> donations = ticketingTypes.stream().filter(ticketType -> ticketType.getTicketType().equals(TicketType.DONATION)).collect(toList());
        ticketingTypes = ticketingTypes.stream().filter(ticketType -> !ticketType.getTicketType().equals(TicketType.DONATION)).collect(toList());
        if(containsDonationType) {
            ticketingTypes.addAll(donations);
        }
        List<Long> ticketTypeIds = ticketingTypes.stream().map(TicketingType::getId).collect(Collectors.toList());
        Map<Long, List<TicketTypeTrackSessionLimitsDto>> ticketTypeTrackSessionLimitsDtoMap = ticketTypeTrackSessionLimitsService.findByTicketTypeIdsAndEventId(event.getEventId(), ticketTypeIds);
		StripeDTO stripe = stripeService.getStripeFeesByEvent(event);

        List<BadgesResponseData> ticketingIdAndBadgesIdByEventId = badgesService.getTicketingIdAndBadgesIdByEventId(event.getEventId());

        Map<Long, Long> ticketingTypeToBadgeIdMap = ticketingIdAndBadgesIdByEventId.stream()
                .filter(e -> !CollectionUtils.isEmpty(e.getTicketingTypeIds()))
                .flatMap(e -> e.getTicketingTypeIds().stream()
                        .map(ticketingTypeId -> new AbstractMap.SimpleEntry<>(ticketingTypeId, e.getBadgeId())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));


        if (!ticketingTypes.isEmpty()) {
            Map<Long, List<TicketingTypeTagAndTrack>> ticketingTypeTagAndTrackMap = ticketingTypeTagAndTrackRepo.findByTicketingTypeIdIn(ticketTypeIds).stream()
                    .collect(Collectors.groupingBy(TicketingTypeTagAndTrack::getTicketingTypeId));
            double capAmount = transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(TicketTypeFormat.VIRTUAL,event);
            double eventVatTax = vatTaxService.getVatTaxRate(event.getEventId());

			for (TicketingType ticketingType : ticketingTypes) {
				GetTicketTypeSettingDto ticketType = new GetTicketTypeSettingDto(ticketingType);
				ticketType.setStartDate(
						getDateInLocal(ticketingType.getStartDate(), event.getEquivalentTimeZone(), null));
                ticketType.setEndDate(
						getDateInLocal(ticketingType.getEndDate(), event.getEquivalentTimeZone(), null));
                if (EventFormat.IN_PERSON.equals(event.getEventFormat())) {

                    if (ticketing.isRecurringEvent() && TicketingType.RecurringEventSalesEndStatus.START.equals(ticketingType.getRecurringEventSalesEndStatus())) {
                        Integer endTimeInMinutes = ticketingType.getRecurringEventSalesEndTime();
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(ticketing.getEventStartDate());
                        calendar.add(Calendar.MINUTE, -endTimeInMinutes);
                        ticketType.setEndDate(getDateInLocal(calendar.getTime(), event.getEquivalentTimeZone(), null));
                    }

                    if (ticketing.isRecurringEvent() && TicketingType.RecurringEventSalesEndStatus.END.equals(ticketingType.getRecurringEventSalesEndStatus())) {
                        Integer endTimeInMinutes = ticketingType.getRecurringEventSalesEndTime();
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(ticketing.getEventEndDate());
                        calendar.add(Calendar.MINUTE, -endTimeInMinutes);
                        ticketType.setEndDate(getDateInLocal(calendar.getTime(), event.getEquivalentTimeZone(), null));
                    }

                }

                ticketType.setBadgeId(ticketingTypeToBadgeIdMap.get(ticketingType.getId()));

                ticketType.setCategoryId(ticketingType.getCategoryId());
                ticketType.setTracksSessionsLimitAllowed(ticketingType.isTracksSessionsLimitAllowed());
                ticketType.setTicketTypeTrackSessionLimitsDto(ticketTypeTrackSessionLimitsDtoMap.get(ticketingType.getId()));
                ticketType.setAllowAssignedSeating(ticketingType.isAllowAssignedSeating());
                if(ticketingType.getCustomTemplateId()!=null) {
                    ticketType.setCustomTemplateId(ticketingType.getCustomTemplateId());
                }
                if (ticketingType.getCustomConfirmationEmailId()!=null){
                    ticketType.setConfirmationEmailId(ticketingType.getCustomConfirmationEmailId());
                }
                if (ticketingType.getCustomTicketPdfDesignId()!=null){
                    ticketType.setCustomTicketPdfDesignId(ticketingType.getCustomTicketPdfDesignId());
                }
                
                setTicketTypeData(recurringEventId, ticketing, ticketingType, ticketType);

				setSalesTaxRateAndAbsorbTax(salesTaxFeeAndTicketType, ticketingType, ticketType);
                double vatTaxRate = ticketingType.isEnableTicketLevelVatTax() ? ticketingType.getVatTaxPercentage() : eventVatTax;
                FeePerTicket feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeAndTicketType, ticketingType.getPrice(), 1, 1, false,(TicketTypeFormat.NULL.equals(ticketingType.getTicketTypeFormat()) ? 0 : capAmount), vatTaxRate,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();
				ticketType.setTotalPayable(feePerTicket.getTotalPayable());
				ticketType.setTicketTypeFormat(ticketingType.getTicketTypeFormat());

                if(ticketingType.isPassfeetobuyer() && ticketingType.isPassFeeVatToBuyer()) {
                    ticketType.setAeFees(feePerTicket.getAeFee());
                    ticketType.setOrganizerRevenue(feePerTicket.getTicketPrice());
                    if(!ticketType.isPayLater()) {
                        ticketType.setCcFeeCharged(feePerTicket.getProcessingFee());
                        ticketType.setCcFees(feePerTicket.getProcessingFee());
                    }else{
                        ticketType.setTotalPayablePayLater(feePerTicket.getTotalPayablePayLater());
                    }
                }else if(Boolean.FALSE.equals(ticketingType.isPassfeetobuyer()) && Boolean.TRUE.equals(ticketingType.isPassFeeVatToBuyer())){
                    double ccFees = 0;
                    if (!ticketType.isPayLater()) {
                        ccFees = calculateCcFees(feePerTicket.getTicketPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee());
                        ticketType.setCcFeeCharged(ccFees);
                    }else{
                        ticketType.setTotalPayablePayLater(feePerTicket.getTotalPayablePayLater());
                    }
                    ticketType.setOrganizerRevenue(feePerTicket.getTicketPrice()-feePerTicket.getAeFee()-ccFees);
                }else{
                    double ccFees = 0;
                    if (!ticketType.isPayLater()) {
                        ccFees = calculateCcFees(feePerTicket.getTicketPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee());
                        ticketType.setCcFeeCharged(ccFees);
                    }else{
                        ticketType.setTotalPayablePayLater(feePerTicket.getTotalPayablePayLater());
                    }
                    ticketType.setOrganizerRevenue(feePerTicket.getTicketPrice()-feePerTicket.getAeFee()-ccFees-feePerTicket.getVatTax());
                }
                ticketType.setVatTaxFeeCharged(feePerTicket.getVatTax());
                ticketType.setAeFeeCharged(feePerTicket.getAeFee());

                ticketingDto.addTicketType(ticketType);

                List<TicketingTypeTagAndTrack> ticketingTypeTagAndTracks = ticketingTypeTagAndTrackMap.getOrDefault(ticketingType.getId(), Collections.emptyList());
                List<Long> keyValueIds = ticketingTypeTagAndTracks.stream().map(TicketingTypeTagAndTrack::getTagOrTrackId).collect(Collectors.toList());

                List<KeyValue> keyValue = keyValueService.findAllByIds(keyValueIds);
                ticketType.setRestrictedTags(getIdNameDto(keyValue, TAG));
                ticketType.setRestrictedTracks(getIdNameDto(keyValue, TRACK));
                ticketType.setNonTransferable(ticketingType.isNonTransferable());
                ticketType.setEnableTicketLevelVatTax(ticketingType.isEnableTicketLevelVatTax());
                ticketType.setVatTaxPercentage(ticketingType.getVatTaxPercentage());
			}
		}
		ticketingDto.setAvailableTimeZone(TimeZoneUtil.getAllTimeZone());
		ticketingDto.setTimezoneId(event.getTimezoneId());
		ticketingDto.setEquivalentTimezone(event.getEquivalentTimeZone());
        ticketingDto.setShowSessionByTimezone(event.isStaticTimezone());

		ticketingDto.setTicketingFee(getTicketFeesLogic(event, dataTypes));
		ticketingDto.setCurrencySymbol(event.getCurrency().getSymbol());
		ticketingDto.setAllowEditingOfDisclaimer(ticketing.isAllowEditingOfDisclaimer());
		ticketingDto.setCustomDisclaimer(ticketing.getCustomDisclaimer());
		ticketingDto.setSeating(isNotBlank(ticketing.getChartKey()));
		log.info("getTicketingId ticketing==>={}" , ticketing.getId());//only show id
		return ticketingDto;
	}

    private List<IdNameDto> getIdNameDto(List<KeyValue> keyValue, EnumKeyValueType tag) {
        return keyValue.stream().filter(e -> tag.equals(e.getType()))
                .map(IdNameDto::new).collect(toList());
    }

    private double calculateCcFees(double ticketPrice, double cCPercentageFee,double cCFlatFee) {
        return ((ticketPrice * cCPercentageFee) / 100) + (cCFlatFee);
    }

    private void setTicketTypeData(Long recurringEventId, Ticketing ticketing, TicketingType ticketingType, GetTicketTypeSettingDto ticketType) {
        ticketType.setChnageToTabel(!eventCommonRepoService.isTypePurchased(ticketingType)//NOSONAR
                && !ticketingOrderManagerService.isTicketingTypeExist(ticketingType));
        if (ticketing.isRecurringEvent()) {
            if (null != ticketingType.getRecurringEvent()
                    && null != recurringEventId) {
                ticketType.setRecurringEventId(ticketingType.getRecurringEvent().getId());
                ticketType.setCreatedFrom(ticketingType.getCreatedFrom());
            }
            ticketType.setRecurringEventSalesEndTime(ticketingType.getRecurringEventSalesEndTime());
            ticketType.setRecurringEventSalesEndStatus(ticketingType.getRecurringEventSalesEndStatus());
        }
    }

    private void setSalesTaxRateAndAbsorbTax(SalesTaxFeeDto salesTaxFeeAndTicketType, TicketingType ticketingType, GetTicketTypeSettingDto ticketType) {
		ticketType.setAbsorbTax(true);
        ticketType.setSalesTaxRate(0.0);
		if(null != salesTaxFeeAndTicketType && !salesTaxFeeAndTicketType.getAbsorbTax() && salesTaxFeeAndTicketType.getTicketingTypeIds().contains(String.valueOf(ticketingType.getId()))){
			ticketType.setSalesTaxRate(salesTaxFeeAndTicketType.getSalesTaxRate());
			ticketType.setAbsorbTax(false);
		}
	}

	@Override
	public EnumEventVenue getEventVenue(Ticketing ticketing) {
		boolean isOnlineEvent = ticketing.isOnlineEvent();

		EnumEventVenue enumEventVenue = EnumEventVenue.VENUE;

		EventFormat eventFormat = ticketing.getEventid().getEventFormat();

		if(EventFormat.HYBRID.equals(eventFormat) || EventFormat.IN_PERSON.equals(eventFormat)){
			if (StringUtils.isBlank(ticketing.getEventAddress())) {
				enumEventVenue = EnumEventVenue.TO_BE_ANNOUNCED;
			}
		}else if(isOnlineEvent){
			enumEventVenue = EnumEventVenue.ONLINE_VIRTUAL_EVENT;
		}
		return enumEventVenue;
	}

	@Override
	public CreateTicketingDto getTicketing(Event event) {

		CreateTicketingDto ticketingDto = new CreateTicketingDto();

		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);

		ticketingDto.setEventAddress(ticketing.getEventAddress());
		ticketingDto.setLatitude(ticketing.getLatitude());
		ticketingDto.setLongitude(ticketing.getLongitude());
		EnumEventVenue enumEventVenue = getEventVenue(ticketing);
		ticketingDto.setEventVenuetype(enumEventVenue.name());
		ticketingDto.setShowRemainingTickets(ticketing.isShowRemainingTickets());
		ticketingDto.setEventStartDate(
				getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone(), null));
		ticketingDto.setEventEndDate(
				getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone(), null));
		ticketingDto.setSeatingChartKey(ticketing.getChartKey());
		ticketingDto.setEventKey(String.valueOf(event.getEventId()));
		ticketingDto.setAvailableTimeZone(TimeZoneUtil.getAllTimeZone());
		ticketingDto.setTimezoneId(event.getTimezoneId());
		ticketingDto.setEquivalentTimezone(event.getEquivalentTimeZone());

		ticketingDto.setTicketingFee(getTicketFeesLogic(event, Collections.emptyList()));
		ticketingDto.setCurrencySymbol(event.getCurrency().getSymbol());
		ticketingDto.setAllowEditingOfDisclaimer(ticketing.isAllowEditingOfDisclaimer());
		ticketingDto.setCustomDisclaimer(ticketing.getCustomDisclaimer());
		ticketingDto.setSeating(isNotBlank(ticketing.getChartKey()));
		return ticketingDto;
	}

	@Override
	public String getStringDateWithAddedRecurringEventEndTime(Date recurringEventStartDate, Integer minutes) {
		Date addedDates = DateUtils.getAddedMinutes(new DateTime(recurringEventStartDate), minutes);
		return DateUtils.formatDate(addedDates, Constants.LOCAL_DATE_FORMAT);
	}

	@Override
	@Transactional
	public void updateListOFTicketTypeOnAssociation(Event event, Long recurringEventId) {

		List<TicketingType> ticketTypeList = NumberUtils.isNumberGreaterThanZero(recurringEventId)
				? ticketingTypeTicketService.findAllByRecurringEventsDisplay(recurringEventId, true, DataType.TICKET)
				: ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET));

		Ticketing ticketing = ticketingRepository.findByEventid(event);
		if(!ticketing.isRecurringEvent()){
			String eventKey = getEventKey(event.getEventId(), NumberUtils.isNumberGreaterThanZero(recurringEventId), recurringEventId);
			if (ticketTypeList.isEmpty()) {
				seatsIoService.updateTicketTypeOnAssociation(ticketing, eventKey, ticketTypeList);
			}
		}
	}

	private void getChartKeyAfterSuccessfullyCreateOrUpdateSeatsIo(Event event, Ticketing ticketing,
                                                                   boolean isRecurringEvent, Long key, List<CategoryDto> value) {

		String chartKeyOld = getChartKey(value, isRecurringEvent, ticketing, key);
		List<Category> categories1 = value
				.stream()
				.map(CategoryDto::createCategory).collect(Collectors.toList());

		String firstRecId = recurringEventsScheduleService.getFirstRecId(event, ticketing);
        seatsIoService.createOrUpdateChart(event.getEventId(), chartKeyOld, categories1, firstRecId);

    }

	protected Map<Long, List<CategoryDto>> getLongListMapOfCategory(Event event, Long ticketTypeId) {

		List<CategoryDto> categoriesDto = ticketingTypeTicketService.getAllTicketingTypes(event.getEventId());

		return categoriesDto
				.stream()
				.filter(e -> !e.getKey().equals(ticketTypeId))
				.collect(Collectors.groupingBy(CategoryDto::getEventOrRecurringEventId));

	}

	protected String getChartKey(List<CategoryDto> value, boolean isRecurringEvent, Ticketing ticketing, Long key) {

		if(isRecurringEvent && null != key){
			return value.get(0).getRecurringChartKey();
		}else {
			return ticketing.getChartKey();
		}
	}


	@Override
	public TicketSettingGetDto getTicketSettingAttributes(Event event, Long recurringEventId) {
		TicketSettingGetDto ticketSettingDto = new TicketSettingGetDto();
		ticketSettingDto.setEventURL(event.getEventURL());
		List<TicketHolderRequiredAttributes> fields = this.getTicketingAttributes(event, recurringEventId);
		fields.sort(Comparator.comparingInt(TicketHolderRequiredAttributes::getBuyerAttributeOrder));

		for (TicketHolderRequiredAttributes field : fields) {
			ticketSettingDto.addAtrtibute(new TicketSettingAttributeDto(field));
		}
		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
		ticketSettingDto.setHolderAttribute(ticketing.getCollectTicketHolderAttributes());
		ticketSettingDto.setCollectAddOnAttribute(ticketing.isCollectAddOnAttributes());
		ticketSettingDto.setAllowAttendeeToEditInfo(ticketing.isAllowAttendeeToEditInfo());
		ticketSettingDto.setCollectAddOnAttribute(ticketing.isCollectAddOnAttributes());
        ticketSettingDto.setShowMemberCountInCheckout(ticketing.isShowMemberCountInCheckout());
        ticketSettingDto.setUniqueTicketHolderEmail(ticketing.getUniqueTicketHolderEmail());
        ticketSettingDto.setUniqueTicketBuyerEmail(ticketing.isUniqueTicketBuyerEmail());
		return ticketSettingDto;
	}

    @Override
    public TicketSettingGetDto getTicketSettingAttributesWithSubquestions(Event event, Long recurringEventId) {
        TicketSettingGetDto ticketSettingDto = new TicketSettingGetDto();
        ticketSettingDto.setEventURL(event.getEventURL());
        List<TicketHolderRequiredAttributes> fields =  ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(event,recurringEventId);
        fields.sort(Comparator.comparingInt(TicketHolderRequiredAttributes::getBuyerAttributeOrder));
        List<TicketHolderRequiredAttributes> subQuestions=fields.stream().filter(e-> NumberUtils.isNumberGreaterThanZero(e.getParentQuestionId())).collect(Collectors.toList());
        List<TicketHolderRequiredAttributes> attrs=fields.stream().filter(e-> !NumberUtils.isNumberGreaterThanZero(e.getParentQuestionId())).collect(Collectors.toList());
        for (TicketHolderRequiredAttributes field : attrs) {
            ticketSettingDto.addAtrtibute(new TicketSettingAttributeDto(field));
        }
        for(TicketSettingAttributeDto attributes:ticketSettingDto.getAttributes()){
        for (TicketHolderRequiredAttributes subfield : subQuestions) {
            if(subfield.getParentQuestionId()!=null && attributes.getId()==subfield.getParentQuestionId()){
                attributes.addSubAttribute(new TicketSettingAttributeDto(subfield));
            }
        }
        }

        Ticketing ticketing =ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        ticketSettingDto.setHolderAttribute(ticketing.getCollectTicketHolderAttributes());
        ticketSettingDto.setCollectAddOnAttribute(ticketing.isCollectAddOnAttributes());
        ticketSettingDto.setAllowAttendeeToEditInfo(ticketing.isAllowAttendeeToEditInfo());
        ticketSettingDto.setCollectAddOnAttribute(ticketing.isCollectAddOnAttributes());
        ticketSettingDto.setShowMemberCountInCheckout(ticketing.isShowMemberCountInCheckout());
        ticketSettingDto.setUniqueTicketHolderEmail(ticketing.getUniqueTicketHolderEmail());
        ticketSettingDto.setUniqueTicketBuyerEmail(ticketing.isUniqueTicketBuyerEmail());
        return ticketSettingDto;
    }

	/**
	 * method used to update
	 *
	 */
	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public void saveTicketingAttributes(TicketSettingDto ticketSetting, Event event, User loggedInUser, Boolean isForBuyer, Long recurringEventId) {
        validateTicketAttributes(ticketSetting, event);
		Ticketing ticketing = getUpdateTicketingModuleByEvent(ticketSetting, event);
        log.info("saveTicketingAttributes|event={}||loggedInUser={}||isForBuyer={}||recurringEventId={}||ticketSettingDto={}|",event.getEventId(),loggedInUser.getUserId(),isForBuyer,recurringEventId,ticketSetting);
        if(ticketing.isRecurringEvent()){
			if(NumberUtils.isNumberGreaterThanZero(recurringEventId)){
				List<TicketingType> ticketingTypes = ticketingTypeService.findAllByTicketing(recurringEventId, event);
				log.info("saveTicketingAttributes|ticketingTypes={}|",ticketingTypes);
				updateAttributesByToggle(ticketSetting, event, loggedInUser, isForBuyer, recurringEventId, ticketingTypes,true);
			}else {
				updateAttributeForDefaultTicketIncludingRecurrings(ticketSetting, event, loggedInUser, isForBuyer, false);
			}
		}
		if(!NumberUtils.isNumberGreaterThanZero(recurringEventId)){
            updateAttributeForDefaultTicketIncludingRecurrings(ticketSetting, event, loggedInUser, isForBuyer, true);
        }

        analyticsUserColumnSelectionService.updateColumnSelectionWhenOrderFormUpdated(event, ticketSetting.getAttributes());
		this.save(ticketing);
	}


    /**
     * Do not allow to enable interest attribute if there is no interest for event
     */
    private void validateTicketAttributes(TicketSettingDto ticketSettingDto, Event event) {
        Optional<TicketSettingAttributeDto> interestAttribute = ticketSettingDto.getAttributes()
                .stream()
                .filter(attribute -> AttributeValueType.INTEREST.name().equals(attribute.getType()))
                .findAny();
        if (interestAttribute.isPresent() && Boolean.TRUE.equals(interestAttribute.get().getEnabledForTicketHolder())) {
            List<InterestDto> allInterestsOfEvent = interestService.getAllInterestsOfEvent(String.valueOf(event.getEventId()));
            if (CollectionUtils.isEmpty(allInterestsOfEvent)) {
                throw new NotAcceptableException(NotAceptableExeceptionMSG.CANNOT_ENABLE_INTEREST);
            }
        }

        Optional<TicketSettingAttributeDto> hiddenAndRequiredAttr = ticketSettingDto.getAttributes()
                .stream()
                .filter(attribute -> (attribute.getRequiredForTicketHolder() && attribute.isInvisibleForHolder())
                        || (attribute.getRequiredForTicketPurchaser() && attribute.isInvisibleForPurchaser()))
                .findAny();
        if(hiddenAndRequiredAttr.isPresent()){
            throw new NotAcceptableException(NotAceptableExeceptionMSG.HIDDEN_ATTRIBUTE_CANNOT_SET_REQUIRED);
        }
    }

	protected void updateAttributeForDefaultTicketIncludingRecurrings(TicketSettingDto ticketSetting,
																	  Event event, User loggedInUser,
																	  Boolean isForBuyer,
																	  boolean isDefaultUpdate) {

		List<TicketingType> ticketTypes = isDefaultUpdate ?
				ticketingTypeService.findAllByEventId(event, false) :
				ticketingTypeTicketService.findAllByEventIdAndRecurringEventIdPresent(event, false);
		log.info("saveTicketingAttributes|updateAttributeForDefaultTicketIncludingRecurrings|isDefaultUpdate={}||ticketTypes={}|",isDefaultUpdate,ticketTypes);
        if(isDefaultUpdate){
			updateAttributesByToggle(ticketSetting, event, loggedInUser, isForBuyer, 0L, ticketTypes, false);
		}else {
			Map<Long, List<TicketingType>> ticketsGroupByRecurring = ticketTypes.stream()
					.collect(Collectors.groupingBy(TicketingType::getRecurringEventId));
			log.info("saveTicketingAttributes|updateAttributeForDefaultTicketIncludingRecurrings|ticketsGroupByRecurring={}|",ticketsGroupByRecurring);
			for (Map.Entry<Long, List<TicketingType>> reId : ticketsGroupByRecurring.entrySet()) {
				updateAttributesByToggle(ticketSetting, event, loggedInUser, isForBuyer, reId.getKey(), reId.getValue(), false);
			}
		}

	}

	@Override
	public void updateAttributesByToggle(TicketSettingDto ticketSetting, Event event, User loggedInUser, Boolean isForBuyer, Long recurringEventId,
                                         List<TicketingType> ticketTypes, boolean toBeCustomAttribute) {
		String allTicketTypeIds = ticketTypes.stream().map(ticketingType -> String.valueOf(ticketingType.getId())).collect(Collectors.joining(","));
        log.info("updateAttributesByToggle || eventId={}|",event.getEventId());
        ticketSetting.getAttributes().removeAll(Collections.singleton(null));
		List<TicketSettingAttributeDto> settingAttributes = ticketSetting.getAttributes().stream().filter(ticketSettingAttr -> null != ticketSettingAttr.getFieldName())
								.collect(collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(TicketSettingAttributeDto::getFieldName))),
										ArrayList::new));
		log.info("updateAttributesByToggle|allTicketTypeIds={}||settingAttributes={}||recurringEventId={}||eventId={}|",allTicketTypeIds,settingAttributes.stream().map(TicketSettingAttributeDto::getFieldName).filter(Objects::nonNull).collect(Collectors.toList()),recurringEventId,event.getEventId());
        for (TicketSettingAttributeDto attribute : settingAttributes) {
			TicketHolderRequiredAttributes ticketHolderRequiredAttribute = NumberUtils.isNumberGreaterThanZero(recurringEventId) ?
					ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(attribute.getFieldName(), event,recurringEventId, attribute.getDataType()) :
					allRequiresAttributesService.findById(attribute.getId());
            log.info("updateAttributesByToggle|eventId={}||ticketHolderRequiredAttribute={}||settingAttribute={}||toBeCustomAttribute={}|",event.getEventId(),ticketHolderRequiredAttribute.getId(),attribute.getId(), toBeCustomAttribute);
            if ((!toBeCustomAttribute && null != ticketHolderRequiredAttribute && null == ticketHolderRequiredAttribute.getCreatedFrom()) ||  // normal event Or old update For recurring
				(!toBeCustomAttribute && null != ticketHolderRequiredAttribute && null != ticketHolderRequiredAttribute.getCreatedFrom() && ticketHolderRequiredAttribute.getCreatedFrom() != -1) || // recurring event -- default update
				(toBeCustomAttribute)) {   // specific update
				updateTicketHolderRequiredAttributes(event, isForBuyer, recurringEventId, toBeCustomAttribute, allTicketTypeIds, attribute, ticketHolderRequiredAttribute);
			}
		}

		ticketHolderAttributesAuditService.auditLogTicketHolderAttributes(ticketSetting, event,loggedInUser, recurringEventId);
	}

	protected void updateTicketHolderRequiredAttributes(Event event, Boolean isForBuyer, Long recurringEventId, boolean toBeCustomAttribute, String allTicketTypeIds, TicketSettingAttributeDto attribute, //NOSONAR
														TicketHolderRequiredAttributes ticketHolderRequiredAttribute) {
		if (ticketHolderRequiredAttribute == null) {
			ticketHolderRequiredAttribute = new TicketHolderRequiredAttributes();
			ticketHolderRequiredAttribute.setEventid(event);
			ticketHolderRequiredAttribute.setName(attribute.getFieldName());
			ticketHolderRequiredAttribute.setCreatedFrom(-1L);
			ticketHolderRequiredAttribute.setRecurringEventId(recurringEventId);
		}
		if(toBeCustomAttribute) makeItCustomAttribute(ticketHolderRequiredAttribute, attribute);
		if ( isHolderAttribToggleToON(attribute, ticketHolderRequiredAttribute) || isBuyerAttribToggleToON(attribute, ticketHolderRequiredAttribute)){
            if(Boolean.TRUE.equals(isForBuyer) && StringUtils.isBlank(ticketHolderRequiredAttribute.getBuyerRequiredTicketTypeId()) && StringUtils.isBlank(ticketHolderRequiredAttribute.getBuyerOptionalTicketTypeId())){
                ticketHolderRequiredAttribute.setBuyerRequiredTicketTypeId(allTicketTypeIds);
            }
			if(!Boolean.TRUE.equals(isForBuyer) && StringUtils.isBlank(ticketHolderRequiredAttribute.getHolderRequiredTicketTypeId()) && StringUtils.isBlank(ticketHolderRequiredAttribute.getHolderOptionalTicketTypeId())){
                ticketHolderRequiredAttribute.setHolderRequiredTicketTypeId(allTicketTypeIds);
			}
		} else if (isBuyerAndHolderAtrtribToggleToOFF(attribute, ticketHolderRequiredAttribute)){
			if (Boolean.TRUE.equals(isForBuyer)) {
                ticketHolderRequiredAttribute.setBuyerRequiredTicketTypeId(null);
                ticketHolderRequiredAttribute.setBuyerOptionalTicketTypeId(null);
			} else {
                ticketHolderRequiredAttribute.setHolderRequiredTicketTypeId(null);
                ticketHolderRequiredAttribute.setHolderOptionalTicketTypeId(null);
			}
		}
		ticketHolderRequiredAttribute.setEnabledForTicketHolder(attribute.getEnabledForTicketHolder());
		ticketHolderRequiredAttribute.setEnabledForTicketPurchaser(attribute.getEnabledForTicketPurchaser());
        ticketHolderRequiredAttribute.setRequiredForTicketHolder(attribute.getRequiredForTicketHolder());
        ticketHolderRequiredAttribute.setRequiredForTicketPurchaser(attribute.getRequiredForTicketPurchaser());
        checkAttributeIsHidden(attribute, ticketHolderRequiredAttribute);
		allRequiresAttributesService.save(ticketHolderRequiredAttribute);

        /*
        For non-recurring : get all attribute by parent id and update the same as parent attribute
        For recurring and update default que:  same as above
        For recurring and update specific date : same as above and make all sub-que custom(-1) because parent que is custom que now.
        */

        if (AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttribute.getAttributeValueType())) {

            List<TicketHolderRequiredAttributes> subQueList = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesForConditionalQuestions(ticketHolderRequiredAttribute.getId(), event,null != isForBuyer ? isForBuyer : true); //NOSONAR
            if (!CollectionUtils.isEmpty(subQueList)) {
                for (TicketHolderRequiredAttributes attributes : subQueList) {
                    attributes.setBuyerEventTicketTypeId(ticketHolderRequiredAttribute.getBuyerEventTicketTypeId());
                    attributes.setHolderEventTicketTypeId(ticketHolderRequiredAttribute.getHolderEventTicketTypeId());
                    attributes.setBuyerRequiredTicketTypeId(ticketHolderRequiredAttribute.getBuyerRequiredTicketTypeId());
                    attributes.setBuyerOptionalTicketTypeId(ticketHolderRequiredAttribute.getBuyerOptionalTicketTypeId());
                    attributes.setHolderRequiredTicketTypeId(ticketHolderRequiredAttribute.getHolderRequiredTicketTypeId());
                    attributes.setHolderOptionalTicketTypeId(ticketHolderRequiredAttribute.getHolderOptionalTicketTypeId());
                    attributes.setEnabledForTicketHolder(ticketHolderRequiredAttribute.getEnabledForTicketHolder());
                    attributes.setEnabledForTicketPurchaser(ticketHolderRequiredAttribute.getEnabledForTicketPurchaser());
                    attributes.setRequiredForTicketHolder(ticketHolderRequiredAttribute.getRequiredForTicketHolder());
                    attributes.setRequiredForTicketPurchaser(ticketHolderRequiredAttribute.getRequiredForTicketPurchaser());
                    attribute.setBuyerHiddenTicketTypeId(ticketHolderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId());
                    attribute.setHolderHiddenTicketTypeId(ticketHolderRequiredAttribute.getHolderRequiredTicketTypeId());
                    if(!NumberUtils.isNumberGreaterThanZero(ticketHolderRequiredAttribute.getCreatedFrom())){
                        attributes.setCreatedFrom(ticketHolderRequiredAttribute.getCreatedFrom());
                    }
                }
                allRequiresAttributesService.saveAll(subQueList);
            }
        }
	}

	private void makeItCustomAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributeDb, TicketSettingAttributeDto attribute) {
		if(!attribute.getEnabledForTicketHolder().equals(ticketHolderRequiredAttributeDb.getEnabledForTicketHolder())
                || !attribute.getEnabledForTicketPurchaser().equals(ticketHolderRequiredAttributeDb.getEnabledForTicketPurchaser())){
			ticketHolderRequiredAttributeDb.setCreatedFrom(-1L);
		}
	}

    protected Ticketing getUpdateTicketingModuleByEvent(TicketSettingDto ticketSettingDto, Event event) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        int modifiedCount = 0;
        // Handle two cases:
        // 1. When unique email requirement is newly enabled while holder attributes are enabled
        // 2. When holder attributes are being disabled while unique email remains enabled
        if ((!Boolean.TRUE.equals(ticketSettingDto.getHolderAttribute()) &&
                Boolean.TRUE.equals(ticketSettingDto.getUniqueTicketHolderEmail()) &&
                !ticketing.getUniqueTicketHolderEmail()) ||
                (Boolean.TRUE.equals(ticketSettingDto.getUniqueTicketHolderEmail()) &&
                        !Boolean.TRUE.equals(ticketSettingDto.getHolderAttribute()) &&
                        ticketing.getCollectTicketHolderAttributes())) {

            log.info("Event {} settings updated: unique email={}, holder attributes={}",
                    event.getEventId(),
                    Boolean.TRUE.equals(ticketSettingDto.getUniqueTicketHolderEmail()) ? "enabled" : "disabled",
                    Boolean.TRUE.equals(ticketSettingDto.getHolderAttribute()) ? "enabled" : "disabled");

             modifiedCount = limitTicketsPerBuyerAndTable(event);
        }
        log.info("Modified {} ticket types for event ID: {}", modifiedCount, event.getEventId());

        ticketing.setCollectTicketHolderAttributes(ticketSettingDto.getHolderAttribute());
        ticketing.setCollectAddOnAttributes(ticketSettingDto.getCollectAddOnAttribute());
        ticketing.setAllowAttendeeToEditInfo(ticketSettingDto.getAllowAttendeeToEditInfo());
        ticketing.setShowMemberCountInCheckout(ticketing.isShowMemberCountInCheckout());
        if(null!=ticketSettingDto.getUniqueTicketHolderEmail()) {
            ticketing.setUniqueTicketHolderEmail(ticketSettingDto.getUniqueTicketHolderEmail());
        }
        ticketing.setUniqueTicketBuyerEmail(ticketSettingDto.isUniqueTicketBuyerEmail());
        return ticketing;
    }


    /**
     * Limits the number of tickets per buyer and tickets per table to 1 for all ticket types
     * of the specified event. This is typically called when unique email requirement is enabled
     * and when holder attributes collection is disabled.
     *
     * @param event The event whose ticket types need to be limited
     * @return The number of ticket types that were modified
     */
    protected int limitTicketsPerBuyerAndTable(Event event) {
        log.info("Limiting tickets per buyer and per table for event ID: {}", event.getEventId());

        List<TicketingType> ticketingTypes = ticketingTypeService.findAllByEventIdAndDataType(event, Collections.singletonList((DataType.TICKET)));
        if (CollectionUtils.isEmpty(ticketingTypes)) {
            log.info("No ticket types found for event ID: {}", event.getEventId());
            return 0;
        }

        List<TicketingType> modifiedTypes = new ArrayList<>();

        for (TicketingType ticketingType : ticketingTypes) {
            boolean modified = false;

            if (ticketingType.getMaxTicketsPerBuyer() > 1) {
                log.info("Limiting maxTicketsPerBuyer from {} to 1 for ticket type ID: {}",
                        ticketingType.getMaxTicketsPerBuyer(), ticketingType.getId());
                ticketingType.setMaxTicketsPerBuyer(1);
                ticketingType.setMinTicketsPerBuyer(0);
                modified = true;
            }

            if (!TicketBundleType.INDIVIDUAL_TICKET.equals(ticketingType.getBundleType())
                    && ticketingType.getNumberOfTicketPerTable() > 1) {
                log.info("Limiting numberOfTicketPerTable from {} to 1 for ticket type ID: {}",
                        ticketingType.getNumberOfTicketPerTable(), ticketingType.getId());
                ticketingType.setNumberOfTicketPerTable(1);
                modified = true;
            }

            if (modified) {
                modifiedTypes.add(ticketingType);
            }
        }

        if (!modifiedTypes.isEmpty()) {
            log.info("Saving {} modified ticket types for event ID: {}", modifiedTypes.size(), event.getEventId());
            ticketingTypeService.saveAll(modifiedTypes);
        }

        return modifiedTypes.size();
    }


    protected void checkAttributeIsHidden(TicketSettingAttributeDto attribute, TicketHolderRequiredAttributes ticketHolderRequiredAttribute) {
        if ((ticketHolderRequiredAttribute.isHiddenForPurchaser() && Boolean.TRUE.equals(attribute.getRequiredForTicketPurchaser())) ||
                (ticketHolderRequiredAttribute.isHiddenForHolder() && Boolean.TRUE.equals(attribute.getRequiredForTicketHolder()))) {
            throw new NotAcceptableException(TicketingExceptionMsg.HIDDEN_FIELD_CAN_NOT_BE_REQUIRED_FIELD);
        }

    }

	protected boolean isBuyerAttribToggleToON(TicketSettingAttributeDto attribute, TicketHolderRequiredAttributes ticketHolderRequiredAttribute) {
		return !ticketHolderRequiredAttribute.getEnabledForTicketPurchaser() && attribute.getEnabledForTicketPurchaser();
	}

	protected boolean isBuyerAndHolderAtrtribToggleToOFF(TicketSettingAttributeDto attribute, TicketHolderRequiredAttributes ticketHolderRequiredAttribute){
		return ticketHolderRequiredAttribute.getEnabledForTicketHolder() && !attribute.getEnabledForTicketHolder()
				&& ticketHolderRequiredAttribute.getEnabledForTicketPurchaser() && !attribute.getEnabledForTicketPurchaser();
	}
	protected boolean isHolderAttribToggleToON(TicketSettingAttributeDto attribute, TicketHolderRequiredAttributes ticketHolderRequiredAttribute) {
		return !ticketHolderRequiredAttribute.getEnabledForTicketHolder() && attribute.getEnabledForTicketHolder();
	}

	@Override
	@Transactional(rollbackFor = { Exception.class })
	public void deleteCustomAttribute(long attributeId, boolean isDeletedFromBuyer, Long recurringEventId, Event event) {
        formRulesService.checkAttributeAttachedWithFormRule(event.getEventId(), attributeId, isDeletedFromBuyer ? RuleFormType.ORDER_FORM_BUYER :RuleFormType.ORDER_FORM_HOLDER);
        ticketHolderRequiredAttributesService.findCustomAttributeById(attributeId).ifPresent(e-> {
            analyticsUserColumnSelectionService.deleteColumnSelectionWhenOrderFormAttributeDeleted(event, e, isDeletedFromBuyer);
            ticketHolderRequiredAttributesService.deleteCustomAttribute(e, isDeletedFromBuyer, recurringEventId, event);
            }
        );
	}

	@Override
	public List<CustomAttribute> getCustomAttribute(long attributeId, boolean isForBuyer, Event event) {
		Optional<TicketHolderRequiredAttributes> optAttributes = ticketHolderRequiredAttributesService
				.findCustomAttributeById(attributeId);
		if (optAttributes.isPresent()) {

            TicketHolderRequiredAttributes ticketHolderRequiredAttributes = optAttributes.get();
            List<CustomAttribute> queList = new ArrayList<>();
            queList.add(mapAttributesToDto(isForBuyer, ticketHolderRequiredAttributes));

            if (AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType())) {
                List<TicketHolderRequiredAttributes> subQueListFinal = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesForConditionalQuestions(attributeId, event,isForBuyer);

                subQueListFinal.forEach(attribute -> queList.add(mapAttributesToDto(isForBuyer, attribute)));
            }

			return queList;
		} else {
			throw new NotAcceptableException(TicketHolderAttributesMsg.ATTRIBUTE_NOT_EXISTS);
		}
	}



    private CustomAttribute mapAttributesToDto(boolean isForBuyer, TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
		CustomAttribute customAttribute = new CustomAttribute();
        customAttribute.setId(ticketHolderRequiredAttributes.getId());
		customAttribute.setAtrributeName(ticketHolderRequiredAttributes.getName());
		customAttribute.setAttribute(ticketHolderRequiredAttributes.isAttribute());
		customAttribute.setAttributeType(ticketHolderRequiredAttributes.getAttributeValueType());
		customAttribute.setDefaultValue(ticketHolderRequiredAttributes.getDefaultValueJsonPurchaser());
        customAttribute.setDefaultValueJsonPurchaser(ticketHolderRequiredAttributes.getDefaultValueJsonPurchaser());
        customAttribute.setDefaultValueJsonHolder(ticketHolderRequiredAttributes.getDefaultValueJsonHolder());
		customAttribute.setEnabledForTicketHolder(ticketHolderRequiredAttributes.getEnabledForTicketHolder());
		customAttribute.setEnabledForTicketPurchaser(ticketHolderRequiredAttributes.getEnabledForTicketPurchaser());
		customAttribute.setHiddenForHolder(ticketHolderRequiredAttributes.isHiddenForHolder());
		customAttribute.setHiddenForPurchaser(ticketHolderRequiredAttributes.isHiddenForPurchaser());
        customAttribute.setInvisibleForHolder(ticketHolderRequiredAttributes.isInvisibleForHolder());
        customAttribute.setHiddenForPurchaserRegistration(ticketHolderRequiredAttributes.isHiddenForPurchaserRegistration());
        customAttribute.setHiddenForHolderRegistration(ticketHolderRequiredAttributes.isHiddenForHolderRegistration());
        customAttribute.setInvisibleForPurchaser(ticketHolderRequiredAttributes.isInvisibleForPurchaser());
		customAttribute.setDataType(ticketHolderRequiredAttributes.getDataType());
        customAttribute.setParentQuestionId(null != ticketHolderRequiredAttributes.getParentQuestionId() ? ticketHolderRequiredAttributes.getParentQuestionId() : 0L);
        customAttribute.setSelectedAnswer(ticketHolderRequiredAttributes.getSelectedAnswer());
        customAttribute.setCapacityHiddenForPurchaser(ticketHolderRequiredAttributes.isCapacityHiddenForPurchaser());
        customAttribute.setCapacityHiddenForHolder(ticketHolderRequiredAttributes.isCapacityHiddenForHolder());
        customAttribute.setHolderRequiredTicketTypeId(ticketHolderRequiredAttributes.getHolderRequiredTicketTypeId());
        customAttribute.setBuyerRequiredTicketTypeId(ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId());
        customAttribute.setHolderOptionalTicketTypeId(ticketHolderRequiredAttributes.getHolderOptionalTicketTypeId());
        customAttribute.setBuyerOptionalTicketTypeId(ticketHolderRequiredAttributes.getBuyerOptionalTicketTypeId());
        customAttribute.setRequiredForTicketHolder(ticketHolderRequiredAttributes.getRequiredForTicketHolder());
        customAttribute.setEventTicketTypeId(isForBuyer ? ticketHolderRequiredAttributes.getBuyerEventTicketTypeId() : ticketHolderRequiredAttributes.getHolderEventTicketTypeId());
        customAttribute.setRequiredForTicketPurchaser(ticketHolderRequiredAttributes.getRequiredForTicketPurchaser());
        customAttribute.setHiddenForHolderRegistration(ticketHolderRequiredAttributes.isHiddenForHolderRegistration());
        customAttribute.setHiddenForPurchaserRegistration(ticketHolderRequiredAttributes.isHiddenForPurchaserRegistration());
        customAttribute.setHolderRegistrationHiddenTicketTypeId(ticketHolderRequiredAttributes.getHolderRegistrationHiddenTicketTypeId());
        customAttribute.setPurchaserRegistrationHiddenTicketTypeId(ticketHolderRequiredAttributes.getPurchaserRegistrationHiddenTicketTypeId());
        customAttribute.setBuyerDescription(ticketHolderRequiredAttributes.getBuyerDescription());
        customAttribute.setHolderDescription(ticketHolderRequiredAttributes.getHolderDescription());
        customAttribute.setBuyerDefaultRequirement(ticketHolderRequiredAttributes.getBuyerDefaultRequirement());
        customAttribute.setHolderDefaultRequirement(ticketHolderRequiredAttributes.getHolderDefaultRequirement());
        return customAttribute;
	}

	@Transactional
	public void updateTicketTypeSequence(Long typeId, Long topTypeId, Long topBottomTypeId, Event event, User user) {
		TicketingType ticketingType = getTicketingType(typeId, event);
		ticketingTypeService.updatewithsequence(ticketingType, topTypeId, topBottomTypeId, event,user);
	}

	private TicketingType getTicketingType(Long id, Event event) {
		TicketingType ticketingType = ticketingTypeService.findByidAndEvent(id, event);

		checkTicketTypeExist(ticketingType);

		return ticketingType;
	}

	@Override
	public List<TicketingDownloadPurchase> findAllTicketingDownloadPurchase() {
		return this.ticketingRepository.findAllTicketingDownloadPurchase();
	}

	@Override
	public TicketingEmailDto getTicketEmailAttributes(Event event) {
		TicketingEmailDto ticketingEmailDto = new TicketingEmailDto();
		String headerText = ticketingHelperService.getTicketOrderHeaderText(event);
		if (StringUtils.isBlank(headerText)) {
			headerText = Constants.DEFAULT_TICKETING_EMAIL_HEADER_TEXT;
		}
		headerText = headerText.replace("${event_name}", event.getName());
		ticketingEmailDto.setEmailHeader(headerText);
		ticketingEmailDto.setEventStaff(getStaffAdminUsers(event));
		ticketingEmailDto.setDefaultReplyToEmail(getEventHostFirstAdminEmail(event));
		return ticketingEmailDto;
	}

	protected List<UserInfoDto> getStaffAdminUsers(Event event) {
		List<UserInfoDto> users = new ArrayList<>();
		List<Staff> staffList = this.staffService.findAdminAndStaffByEvent(event);
		if (staffList != null) {
			for (Staff staff : staffList) {
				users.add(new UserInfoDto(staff.getUser(), staff.isEmailVerified()));
			}
		}
		return users;
	}

	@Override
    @Transactional
    public void sendTestEmail(String email, Event event,Long customEmailId) throws DocumentException, TemplateException, IOException {
        sendTestEmail(email, event, Optional.empty(), false,customEmailId);
    }

	@Override
	@Transactional
	public void sendTestEmail(String email, Event event, Optional<Long> resendTicketEmailOptional, boolean isSendTestReminder,Long customEmailId) throws DocumentException, TemplateException, IOException {
        Optional<ContactModuleSettings> contactModuleSettings = contactModuleSettingsService.findContactModuleSettingsByEvent(event);
        getEmailLimitByEventOrThrowEmailLimitErrorIfCountIsZero(event, contactModuleSettings);
        Optional<User> userOptional = roUserService.getUserByEmail(email);
	    if(userOptional.isPresent()) {
            User userEmail = userOptional.get();
            try {
                String firstName = isNotBlank(userEmail.getFirstName()) ? userEmail.getFirstName() : "Jon";
                String lastName = isNotBlank(userEmail.getLastName()) ? userEmail.getLastName() : "Kazarian";
                List<EventTickets> eventTicketsList = new ArrayList<>();
                long orderid = 0;

                    TicketingType ticketingType = new TicketingType();
                    TicketingType ticketTypeFromDB = new TicketingType();
                    List<TicketingType> listOfAvailableTicketTypes = ticketingTypeRepository.findAllTicketTypesByCustomConfirmationEmailId(event,customEmailId);

                    if (listOfAvailableTicketTypes.isEmpty()) {
                        ticketingType.setTicketTypeName("General Admission");
                        ticketingType.setPrice(100);
                        ticketingType.setId(123);
                        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
                        ticketingType.setTicketType(TicketType.PAID);
                    } else {
                        ticketTypeFromDB = listOfAvailableTicketTypes.get(0);
                        ticketingType.setPrice(ticketTypeFromDB.getPrice());
                        ticketingType.setId(ticketTypeFromDB.getId());
                        ticketingType.setCustomTicketPdfDesignId(ticketTypeFromDB.getCustomTicketPdfDesignId());
                        ticketingType.setTicketTypeName(ticketTypeFromDB.getTicketTypeName());
                        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
                        ticketingType.setTicketType(TicketType.PAID);
                        for (TicketingType ticketType : listOfAvailableTicketTypes) {
                            if (ticketType.getAllowPDFDownload()) {
                                ticketingType.setAllowPDFDownload(ticketType.getAllowPDFDownload());
                            }
                            if (ticketType.getAllowInvoicePDFForBuyers()) {
                                ticketingType.setAllowInvoicePDFForBuyers(ticketType.getAllowInvoicePDFForBuyers());
                            }
                            if (ticketType.getAllowInvoicePDFForHolders()) {
                                ticketingType.setAllowInvoicePDFForHolders(ticketType.getAllowInvoicePDFForHolders());
                            }
                        }
                    }
                    EventTickets eventTickets1 = new EventTickets();
                    eventTickets1.setTicketPrice(ticketingType.getPrice());
                    eventTickets1.setPaidAmount(ticketingType.getPrice());
                    eventTickets1.setHolderFirstName(firstName);

                CustomTemplates customInvoicePdfDesign =
                        confirmationEmailRepository.findByEventAndIsDefaultEmail(event, true, TemplateType.TICKET_PDF_DESIGN);
                if (!ObjectUtils.isEmpty(customInvoicePdfDesign)) {
                    ticketingType.setCustomTicketPdfDesignId(customInvoicePdfDesign.getId());
                }
                    eventTickets1.setHolderLastName(lastName);
                    eventTickets1.setBarcodeId("beeff541-7864-4f1b-bb05-ac30268afa67");
                    eventTickets1.setId(0L);
                    eventTickets1.setEventId(event.getEventId());
                    eventTickets1.setTicketingTypeId(ticketingType);
                    eventTickets1.setHolderUserId(userEmail);
                    eventTicketsList.add(eventTickets1);

                String cardType = "VISA";
                String last4 = "1111";

                //Date clientDate = new Date(); //nosonar

//			Date eventStartDate = new DateTime(2018, 03, 8, 17, 50, 00).toDate();//sonar
//			Date eventEndDate = new DateTime(2018, 05, 31, 20, 50, 00).toDate();//sonar
//
//			String eventAddress = "Newton, MA, United States";//sonar

                Ticketing ticketing = ticketingRepository.findByEventid(event);


                this.sendGridMailPrepareService.sendTicketingPurchaseEmail(event, userEmail, eventTicketsList, orderid,
                        cardType, last4, new Date(),
                        ticketingHelperService.getTicketOrderHeaderText(event), ticketing.getEventStartDate(), ticketing.getEventEndDate(),
                         ticketing.getEventAddress(), false, false, resendTicketEmailOptional, false, isSendTestReminder,false,customEmailId,false, null);
            } catch (JAXBException | NullPointerException e) {
                log.error("Exception", e);
            }
        }
	}

    @Override
    public void sendTestReminderEmailSqs(String email, Long resendTicketingId) {
        try {
            String generateEmailQueueUrl = sqsClient.getQueueUrl(request -> request.queueName(generateReminderEmailQueue)).queueUrl();

            ReminderEmailLambdaMessage reminderEmailLambdaMessage = new ReminderEmailLambdaMessage(resendTicketingId, email);

            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            String reminderEmailLambdaMessageString = objectMapper.writeValueAsString(reminderEmailLambdaMessage);

            SendMessageRequest sendMsgRequest = SendMessageRequest.builder()
                    .queueUrl(generateEmailQueueUrl)
                    .messageBody(reminderEmailLambdaMessageString)
                    .delaySeconds(1)
                    .build();

            SendMessageResponse sendMsgResponse = sqsClient.sendMessage(sendMsgRequest);
            log.info("Generate email Sqs response Message ID: {}", sendMsgResponse.messageId());

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

	public String getEventHostFirstAdminEmail(Event event) {
		List<String> admins = staffService.findEventHostEmail(StaffRole.admin, event);
		return admins.isEmpty() ? null : admins.get(0);
	}

    private double getEventTicketsOrderAmount(List<EventTicketOrderDto> eventTicketOrderDtos){
        double amount = 0;
        double refundAmount = 0;
        for (EventTicketOrderDto eventTicketOrderDto : eventTicketOrderDtos){
            amount = amount + eventTicketOrderDto.getPaidAmount();
            refundAmount = refundAmount + eventTicketOrderDto.getRefundedAmount();
        }
        return (amount - refundAmount);
    }

	private double getEventTicketsAmount(List<EventTickets> eventTicketsList){
		double amount = 0;
		double refundAmount = 0;
		for (EventTickets eventTickets : eventTicketsList){
			amount = amount + eventTickets.getPaidAmount();
			refundAmount = refundAmount + eventTickets.getRefundedAmount();
		}
		return (amount - refundAmount);
	}

	@Override
	public UserOrdersDto getUserActiveOrdersByOrderIdAndHolderEmail(PageSizeSearchObj pageSizeSearchObj, Long orderId, String email, User user) {
        Page<EventTicketOrderDto> eventTicketOrderDtos = ticketingOrderRepoService.getUserActiveOrdersByOrderIdAndHolderEmail(orderId, email == null ? user.getEmail() : email, pageSizeSearchObj.getPageable());
        return getUserEventOrders(eventTicketOrderDtos);
	}

	@Override
	public UserEventOrderDto getOrderAndEventDetailsByOrderId(Long orderId, User user) {
		TicketingOrder ticketingOrder = ticketingOrderService.findByid(orderId);
		return prepareUserEventOrderDto(ticketingOrder,user);
	}

    @Override
	@Transactional
    public void setEventToOnlineEvent(Event event) {
        ticketingRepository.setEventToOnlineEvent(event);
    }

	@Override
	public List<Event> findAllOnlineEvents() {
		return ticketingRepository.findAllOnlineEvents();
	}

	@Override
	public TicketingDatesDto findEventStartAndEndDateByEventid(Event event) {
		return ticketingRepository.findEventStartAndEndDateByEventid(event);
	}

    @Override
    public TicketingDatesDto findEventStartAndEndDateAndPreEventAccessMinuteByEventid(Event event) {
        return ticketingRepository.findEventStartAndEndDateAndPreEventAccessMinuteByEventid(event);
    }

    @Override
    public UserOrdersDto getUserActiveOrders(User user, PageSizeSearchObj pageSizeSearchObj, Long orderId) {
        Page<Long> orderList;
        if (orderId == null) {
            orderList = ticketingOrderRepoService.findActiveOrderIdsByPurchaser(user, pageSizeSearchObj.getPageable());
        } else {
            orderList = new PageImpl<>(List.of(orderId), pageSizeSearchObj.getPageable(), 0);
        }

        List<EventTicketOrderDto> eventTicketOrderDtoList = ticketingOrderRepoService.findActiveOrderByPurchaserAndOrderId(user, orderList.getContent(), pageSizeSearchObj.getPageableSort());
        Page<EventTicketOrderDto> pagebleList = new PageImpl<>(eventTicketOrderDtoList, pageSizeSearchObj.getPageable(), orderList.getTotalElements());
        return getUserEventOrders(pagebleList);
    }

    @Override
    public List<EventMyTicketOrderDto> getUserTickets(User user,Event event){
        List<EventMyTicketOrderDto> myTicketOrderDtos =  ticketingOrderRepoService.findHolderByEventAndHolderUserIgnoreDonation(user.getUserId(),event.getEventId());
        myTicketOrderDtos.forEach(e -> {
            e.setEventStartDateWithTimeFormat(TimeZoneUtil.getEndDateString(e.getEventStartDate(), event.getEquivalentTimeZone()));
            e.setEventEndDateWithTimeFormat(TimeZoneUtil.getEndDateString(e.getEventEndDate(), event.getEquivalentTimeZone()));
        });

        return myTicketOrderDtos;
    }

	protected UserEventOrderDto prepareUserEventOrderDto(TicketingOrder ticketingOrder, User user) {
		Event event = ticketingOrder.getEventid();
        if(event == null){
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }
		UserEventOrderDto userEventOrderDto = new UserEventOrderDto();
		EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
		userEventOrderDto.setEventLogoImage(eventDesignDetail.getLogoImage());
		userEventOrderDto.setEventName(event.getName());
		userEventOrderDto.setEventUrl(event.getEventURL());
		userEventOrderDto.setTicketOrderNumber(ticketingOrder.getId());

		userEventOrderDto.setCurrency(event.getCurrency().getSymbol());

		Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);

		userEventOrderDto.setEventStartDate(ticketing.getEventStartDate());
		userEventOrderDto.setEventEndDate(ticketing.getEventEndDate());
		userEventOrderDto.setPurchaseDate(ticketingOrder.getOrderDate());
		userEventOrderDto.setHolderAttribute(ticketing.getCollectTicketHolderAttributes());
		userEventOrderDto.setEventLocation(ticketing.getEventAddress());
		userEventOrderDto.setSeating(StringUtils.isNotBlank(ticketing.getChartKey()));
        List<TicketingOrderManager> ticketingOrderManagers = ticketingOrderManagerService.getAllByOrderId(ticketingOrder);
        if (!ticketingOrderManagers.isEmpty() && ticketingOrderManagers.get(0).getRecurringEventId() != null){
            Optional<RecurringEvents> recurringEvents = recurringEventsRepository.findById(ticketingOrderManagers.get(0).getRecurringEventId());
            if (recurringEvents.isPresent()){
                userEventOrderDto.setEventStartDate(recurringEvents.get().getRecurringEventStartDate());
                userEventOrderDto.setEventEndDate(recurringEvents.get().getRecurringEventEndDate());
            }
        }

		List<EventTickets> eventTickets;
                if(StringUtils.isNotEmpty(Constants.STRING_EMPTY)){
                    eventTickets = eventCommonRepoService.findByOrderAndHolderEmail(ticketingOrder, Constants.STRING_EMPTY);
                }else if(user != null && ticketingOrder.getPurchaser() != null && !user.getUserId().equals(ticketingOrder.getPurchaser().getUserId())){
                    eventTickets = eventCommonRepoService.findByOrderAndHolderEmail(ticketingOrder, user.getEmail());
                }else {
                    eventTickets = eventCommonRepoService.findByOrder(ticketingOrder);
                }
		userEventOrderDto.setSeatNumbers(eventTickets.stream().map(EventTickets::getSeatNumber).collect(Collectors.joining(",")));
		List<EventTickets> eventTicketsPaid = eventTickets.stream()
				.filter(e->(!TicketStatus.DELETED.equals(e.getTicketStatus()))).collect(Collectors.toList());
        List<EventTickets> validEventTickets = eventTickets.stream().filter(e -> !TicketStatus.DELETED.equals(e.getTicketStatus())).collect(Collectors.toList());
        int numberOfTickets = validEventTickets.size();
        int ticketsRefunded = 0;
        boolean partiallRefunded = false;
        int ticketsCanceled = 0;
        for (EventTickets eventTicket : validEventTickets) {
            if(TicketStatus.CANCELED.equals(eventTicket.getTicketStatus())) {
                ticketsCanceled++;
                partiallRefunded = true;
            }else if(TicketPaymentStatus.PARTIALLY_REFUNDED.equals(eventTicket.getTicketPaymentStatus())){
                partiallRefunded = true;
            }else if (TicketPaymentStatus.REFUNDED.equals(eventTicket.getTicketPaymentStatus())) {
                ticketsRefunded++;
                partiallRefunded = true;
            }else if(eventTicket.getRefundedAmount() > 0){
                partiallRefunded = true;
            }
        }
        if (ticketsRefunded == numberOfTickets) {
            userEventOrderDto.setStatus(Constants.TICKETING_STATUS_REFUNDED);
        }else if(ticketsCanceled == numberOfTickets){
            userEventOrderDto.setStatus(Constants.TICKETING_STATUS_CANCELED);
        }else if (partiallRefunded) {
            userEventOrderDto.setStatus(Constants.STATUS_TO_DISPLAY_PARTIALLY_REFUNDED);
        } else if (TicketingOrder.OrderType.UNPAID.toString().equals(ticketingOrder.getStatus().toString()) && !TicketingOrder.TicketingOrderStatus.PAID.equals(ticketingOrder.getStatus())) {
            userEventOrderDto.setStatus(TicketingOrder.OrderType.UNPAID.toString());
        } else if (TicketingOrder.TicketingOrderStatus.PARTIAL.equals(ticketingOrder.getStatus())) {
            userEventOrderDto.setStatus(TicketingOrder.TicketingOrderStatus.PARTIAL.toString());
        } else {
            userEventOrderDto.setStatus(TicketingOrder.TicketingOrderStatus.PAID.toString());
        }

		userEventOrderDto.setTotalPurchasedTicketsInOrder(eventTicketsPaid.size());
		userEventOrderDto.setTicketOrderAmount(this.getEventTicketsAmount(eventTicketsPaid));
		userEventOrderDto.setAllowAttendeeToEditInfo(ticketing.isAllowAttendeeToEditInfo());
        userEventOrderDto.setHideEventDate(event.isHideEventDate());
        userEventOrderDto.setAllowToSendOrderConfirmationEmail(ticketing.isEnableOrderConfirmationEmail());
        userEventOrderDto.setRecurringEventId(eventTickets.get(0).getRecurringEventId());
        userEventOrderDto.setCouponCode(ticketingOrder.getTicketingCoupon()!= null ? ticketingOrder.getTicketingCoupon().getName() : null);
		return userEventOrderDto;
	}

	@Override
	public UserOrdersDto getUserPastOrders(User user, PageSizeSearchObj pageSizeSearchObj){
		Page<EventTicketOrderDto> eventTicketOrderDtos = ticketingOrderRepoService.findPastOrderByPurchaserAndStatus(user, pageSizeSearchObj.getPageable());
        return getUserEventOrders(eventTicketOrderDtos);
    }

    private UserOrdersDto getUserEventOrders(Page<EventTicketOrderDto> eventTicketOrderDtos) {

        List<EventTicketOrderDto> dtoList =  eventTicketOrderDtos.getContent().stream().distinct().collect(Collectors.toList());
        List<EventTicketingAndEventDesignDetailDto> eventTicketingAndEventDesignDetailDtos = null;
        UserOrdersDto userOrdersDto = new UserOrdersDto();

        if(!dtoList.isEmpty()) {
            List<Long> eventIds = dtoList.stream()
                    .map(EventTicketOrderDto::getEventId)
                    .distinct()
                    .collect(Collectors.toList());
            eventTicketingAndEventDesignDetailDtos = ticketingOrderRepoService.findEventsTickitingAndEventDesignDetailsByEventIds(eventIds);
        }

        if (eventTicketingAndEventDesignDetailDtos != null && !eventTicketingAndEventDesignDetailDtos.isEmpty()) {

            Map<Long, List<EventTicketOrderDto>> eventTicketOrderMap = dtoList.stream().collect(Collectors.groupingBy(EventTicketOrderDto::getEventId));
            for (EventTicketingAndEventDesignDetailDto eventTicketingAndEventDesignDetailDto : eventTicketingAndEventDesignDetailDtos) {
                List<EventTicketOrderDto> eventTicketOrderDtoList = eventTicketOrderMap.get(eventTicketingAndEventDesignDetailDto.getEventId());

                if(eventTicketOrderDtoList!=null){
                    Map<Long, List<EventTicketOrderDto>> ticketingOrderMap = eventTicketOrderDtoList.stream().collect(Collectors.groupingBy(EventTicketOrderDto::getTicketingOrderId));
                    ticketingOrderMap.forEach((key, value) -> {
                        EventTicketOrderDto orderDto = value.get(0);

                        UserEventOrderDto userEventOrderDto = getUserEventOrderDto(eventTicketingAndEventDesignDetailDto);
                        userEventOrderDto.setTicketOrderNumber(orderDto.getTicketingOrderId());
                        userEventOrderDto.setPurchaseDate(orderDto.getOrderDate());
                        userEventOrderDto.setSeatNumbers(value.stream().map(EventTicketOrderDto::getSeatNumber).filter(Objects::nonNull).filter(item -> !item.isEmpty()).collect(Collectors.joining(",")));
                        List<EventTicketOrderDto> purchasedOrderTickets = (value.stream()
                                .filter(m -> (!TicketStatus.CANCELED.equals(m.getTicketStatus()))
                                        && !TicketStatus.DELETED.equals(m.getTicketStatus())).collect(Collectors.toList()));
                        userEventOrderDto.setTotalPurchasedTicketsInOrder(purchasedOrderTickets.size());
                        userEventOrderDto.setTicketOrderAmount(getEventTicketsOrderAmount(purchasedOrderTickets));
                        userEventOrderDto.setTicketTypeNameQtyDto(setTicketTypeInDtos(value));
                        userEventOrderDto.setStatus(String.valueOf(orderDto.getStatus()));
                        userEventOrderDto.setNonTransferable(orderDto.isDisableTicketTransfer() || orderDto.isNonTransferable());
                        userOrdersDto.addUserEventOrder(userEventOrderDto);
                    });
                }
            }
            userOrdersDto.setRecordsTotal(eventTicketOrderDtos.getTotalElements());
            userOrdersDto.setRecordsFiltered(userOrdersDto.getUserEventOrders().size());
        }
        return userOrdersDto;
    }

    private List<TicketTypeNameQtyDto> setTicketTypeInDtos(List<EventTicketOrderDto> ticketOrderDtos) {
        Map<Long, List<EventTicketOrderDto>> map = ticketOrderDtos.stream().collect(Collectors.groupingBy(EventTicketOrderDto::getTicketTypeId));
        List<TicketTypeNameQtyDto> ticketTypeNameQtyDtos = new ArrayList<>();
        map.forEach((key, value) -> {
            EventTicketOrderDto orderDto = value.get(0);
            TicketTypeNameQtyDto ticketTypeAndQuantityDto = new TicketTypeNameQtyDto();
            ticketTypeAndQuantityDto.setTicketTypeId(orderDto.getTicketTypeId());
            ticketTypeAndQuantityDto.setTicketTypeName(orderDto.getTicketTypeName());
            ticketTypeAndQuantityDto.setDataType(orderDto.getDataType().name());
            List<EventTicketOrderDto> purchasedOrderTickets = (value.stream()
                    .filter(m -> (!TicketStatus.CANCELED.equals(m.getTicketStatus()))
                            && !TicketStatus.DELETED.equals(m.getTicketStatus())).collect(Collectors.toList()));
            ticketTypeAndQuantityDto.setTicketPrice(getTicketPrice(orderDto));
            ticketTypeAndQuantityDto.setTotalTickets(purchasedOrderTickets.size());
            ticketTypeNameQtyDtos.add(ticketTypeAndQuantityDto);
        });
        return ticketTypeNameQtyDtos;
    }

    private double getTicketPrice(EventTicketOrderDto orderDto) {

        if (TicketBundleType.INDIVIDUAL_TICKET.equals(orderDto.getBundleType()))
            return orderDto.getPrice();

        Optional<TicketingType> otpTicketingType = ticketingTypeService.findById(orderDto.getTicketTypeId());
        return otpTicketingType.map(ticketingType -> orderDto.getPrice() / ticketingType.getNumberOfTicketPerTable()).orElseGet(orderDto::getPrice);
    }

    private UserEventOrderDto getUserEventOrderDto(EventTicketingAndEventDesignDetailDto eventTicketingAndEventDesignDetailDto) {
        UserEventOrderDto userEventOrderDto = new UserEventOrderDto();
        userEventOrderDto.setEventLogoImage(eventTicketingAndEventDesignDetailDto.getLogoImage());
        userEventOrderDto.setEventName(eventTicketingAndEventDesignDetailDto.getName());
        userEventOrderDto.setCurrency(eventTicketingAndEventDesignDetailDto.getCurrency().getSymbol());
        userEventOrderDto.setEventUrl(eventTicketingAndEventDesignDetailDto.getEventURL());
        userEventOrderDto.setEventStartDate(eventTicketingAndEventDesignDetailDto.getEventStartDate());
        userEventOrderDto.setEventEndDate(eventTicketingAndEventDesignDetailDto.getEventEndDate());
        userEventOrderDto.setHolderAttribute(eventTicketingAndEventDesignDetailDto.getCollectTicketHolderAttributes());
        userEventOrderDto.setEventLocation(eventTicketingAndEventDesignDetailDto.getEventAddress());
        userEventOrderDto.setHideEventDate(eventTicketingAndEventDesignDetailDto.isHideEventDate());
        userEventOrderDto.setPreEventAccessMinutes(eventTicketingAndEventDesignDetailDto.getPreEventAccessMinutes());
        userEventOrderDto.setEventFormat(eventTicketingAndEventDesignDetailDto.getEventFormat());
        TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(eventTicketingAndEventDesignDetailDto.getEquivalentTimezone());
        userEventOrderDto.setEquivalentTimezone(timeZone.getEquivalentTimezone());
        userEventOrderDto.setAllowToSendOrderConfirmationEmail(eventTicketingAndEventDesignDetailDto.isAllowToSendOrderConfirmationEmail());
        return userEventOrderDto;
    }

	@Override
	public List<UserEventTicketDto> getUserEventTickets(long ticketOrderNumber, User user){
		List<UserEventTicketDto> userEventTicketDtos = new ArrayList<>();
        List<EventTickets> eventTicketsList ;
        TicketingOrder ticketingOrder = ticketingOrderService.findByid(ticketOrderNumber);
		if (user.getUserId().equals(ticketingOrder.getPurchaser().getUserId())){
            eventTicketsList = eventCommonRepoService.findByOrder(ticketOrderNumber);
        }else {
            eventTicketsList = eventCommonRepoService.findByOrderAndHolderEmailAndNotDeleted(ticketingOrder, user.getEmail());
        }
		return getUserEventTicketDtos(userEventTicketDtos, eventTicketsList,ticketingOrder);
	}

	@Override
	public List<UserEventTicketDto> getEventTicketsByOrderIdAndHolderEmail(Long orderId, String holderEmail) {
		List<UserEventTicketDto> userEventTicketDtos = new ArrayList<>();
		TicketingOrder ticketingOrder = ticketingOrderService.findByid(orderId);
		List<EventTickets> eventTicketsList = eventCommonRepoService.findByOrderAndHolderEmailAndNotDeleted(ticketingOrder, holderEmail);
		return getUserEventTicketDtos(userEventTicketDtos, eventTicketsList,ticketingOrder);
	}

	private List<UserEventTicketDto> getUserEventTicketDtos(List<UserEventTicketDto> userEventTicketDtos, List<EventTickets> eventTicketsList,TicketingOrder ticketingOrder) {
		for (EventTickets eventTickets : eventTicketsList) {
			TicketingType ticketingType = eventTickets.getTicketingTypeId();
			if (ticketingType != null){
                UserEventTicketDto userEventTicketDto = new UserEventTicketDto();
                if ( null != ticketingType.getTicketing()) {
                    userEventTicketDto.setChartKey(ticketingType.getTicketing().getChartKey());
                }
                userEventTicketDto.setId(eventTickets.getId());
                userEventTicketDto.setBarcode(eventTickets.getBarcodeId());
                userEventTicketDto.setAmount(eventTickets.getPaidAmount() - eventTickets.getRefundedAmount());
                userEventTicketDto.setCheckInDate(eventTickets.getCheckInDate());
                userEventTicketDto.setTicketTypeName(ticketingType.getTicketTypeName());
                userEventTicketDto.setAllowPDFDownload(ticketingType.getAllowPDFDownload());
                userEventTicketDto.setAllowInvoicePDFDownload(ticketingType.getAllowInvoicePDFDownload());
                userEventTicketDto.setSeatsNumber(eventTickets.getSeatNumber());
                userEventTicketDto.setDataType(eventTickets.getDataType().name());
                userEventTicketDto.setTicketStatus(eventTickets.getTicketStatus().name());
                userEventTicketDto.setGuestOfBuyer(eventTickets.getGuestOfBuyer());
                userEventTicketDto.setTicketPaymentStatus(eventTickets.getTicketPaymentStatus().getStatus());
                userEventTicketDto.setTicketTypeId(eventTickets.getTicketingTypeOnlyId());
                userEventTicketDto.setNonTransferable(ticketingOrder.isDisableTicketTransfer() || ticketingType.isNonTransferable() || eventTickets.isNonTransferable());
                userEventTicketDtos.add(userEventTicketDto);
            }
            }

		return userEventTicketDtos;
	}

	@Override
	public boolean isRecurringEvent(Event event) {
		return ticketingRepository.isRecurringEvent(event);
	}

	@Override
	public void changeSeat(Long eventId, Long eventTicketingId, String newSeatNumber) {
		EventTickets eventTickets = eventCommonRepoService.findById(eventTicketingId);

		boolean isRecurringEvent = null != eventTickets.getRecurringEventId();
		String eventKey = getEventKey(eventId, isRecurringEvent, eventTickets.getRecurringEventId());

		if(StringUtils.isNoneBlank(eventTickets.getSeatNumber())){
			seatsIoService.bookTicketsInSeatsIo(eventKey, Collections.singletonList(newSeatNumber));
			seatsIoService.changeStatus(eventKey, Collections.singletonList(eventTickets.getSeatNumber()),FREE);
		}

		TicketingOrderManager tom = ticketingOrderManagerService.getTicketTypeByOrderIdAndTicketTypeId(eventTickets.getTicketingTypeId(),
				eventTickets.getTicketingOrder());

		tom.setSeats(tom.getSeats().replace(eventTickets.getSeatNumber(), newSeatNumber));
		tom.setSelectSeatsDisplay(tom.getSelectSeatsDisplay().replace(eventTickets.getSeatNumber(), newSeatNumber));

		eventTickets.setSeatNumber(newSeatNumber);
		eventTickets.setSeatNumberDisplay(newSeatNumber);// ALOK:4473:Please ask to front-end for SeatNumberDisplay to store here.
		eventCommonRepoService.save(eventTickets);

		ticketingOrderManagerService.save(tom);
	}

	@Override
	@Transactional
	public String updateOrderNote(long orderId, Event event, String note) {
	    String responseMessage;
		TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderId, event);
		if(StringUtils.isEmpty(ticketingOrder.getNote())){
            responseMessage = ORDER_NOTE_ADDED_SUCCESSFULLY;
        }else{
            responseMessage = ORDER_NOTE_UPDATED_SUCCESSFULLY;
        }
		ticketingOrder.setNote(note);
		ticketingOrderRepoService.save(ticketingOrder);
		return responseMessage;
	}

	@Override
	public AttendeeDtoV2 getHolderData(Event event, long ticketId) {
		EventTickets eventTicket = eventCommonRepoService.findById(ticketId);
		if(eventTicket != null){
			AttendeeDto attendeeDto = new AttendeeDto();
            if(eventTicket.getGuestOfBuyer() &&
                    (StringUtils.isEmpty(eventTicket.getHolderFirstName())
                    || StringUtils.isEmpty(eventTicket.getHolderLastName()))) {
                attendeeDto.setFirstName(GUEST_OF+STRING_BLANK+eventTicket.getTicketPurchaserId().getFirstName());
                attendeeDto.setLastName(eventTicket.getTicketPurchaserId().getLastName());
            }else{
                attendeeDto.setFirstName(eventTicket.getHolderFirstName());
                attendeeDto.setLastName(eventTicket.getHolderLastName());
            }
			attendeeDto.setEventTicketingId(eventTicket.getId());
			attendeeDto.setPaid(eventTicket.getPaidAmount());
			attendeeDto.setTicketPrice(eventTicket.getTicketPrice());
			attendeeDto.setRefundedAmount(eventTicket.getRefundedAmount());
			attendeeDto.setQty(1);
			TicketingTable ticketingTable = eventTicket.getTicketingTable();
			setTicketType(eventTicket, attendeeDto, ticketingTable);
			attendeeDto.setSeatNumber(eventTicket.getSeatNumber());
			attendeeDto.setStatus(eventTicket.getTicketStatus().getStatus());
			attendeeDto.setTicketStatus(eventTicket.getTicketPaymentStatus().name());
			attendeeDto.setBarcode(eventTicket.getBarcodeId());
			attendeeDto.setDataType(eventTicket.getDataType());
			attendeeDto.setEmail(eventTicket.getHolderEmail());
            attendeeDto.setTicketPaymentStatus(eventTicket.getTicketPaymentStatus());
            attendeeDto.setLastUpdated(getDateInLocal(eventTicket.getUpdatedAt(),
                    event.getEquivalentTimeZone(), Constants.DATE_FORMAT_WITH_AM_PM));

            return AttendeeDtoV2.convertToAttendeeDtoV2(attendeeDto);
		} else {
			return new AttendeeDtoV2();
		}
	}

	protected void setTicketType(EventTickets eventTicket, AttendeeDto attendeeDto, TicketingTable ticketingTable) {
		if (ticketingTable != null) {
			TicketingType ticketingType = eventTicket.getTicketingTypeId();
			String bundleType = ticketingType.getBundleType().toString();
			attendeeDto.setBundleType(bundleType);
			attendeeDto.setTicketType(ticketingType.getTicketTypeName() + " - " + bundleType + " "
					+ ticketingTable.getTableNoSequence());
			attendeeDto.setTable(true);
		} else {
			attendeeDto.setTicketType(eventTicket.getTicketingTypeId().getTicketTypeName());
			attendeeDto.setTable(false);
		}
		attendeeDto.setTicketTypeId(eventTicket.getTicketingTypeId().getId());
	}

	@Override
	@Transactional
	public String createCustomer(String email, Event event) {
		Stripe stripe = roStripeService.findByEvent(event);
		return paymentHandlerService.createCustomer(email, stripe);

	}


	@Override
	public void saveTicketing(TicketingDto ticketingDto, Event event) {
		if (isNotBlank(ticketingDto.getTimezoneId()) && isNotBlank(ticketingDto.getEquivalentTimezone())) {
			event.setTimezoneId(getNameByEquivalentTimeZone(ticketingDto.getEquivalentTimezone()));
			event.setEquivalentTimeZone(ticketingDto.getEquivalentTimezone());
			eventRepoService.save(event);
		}
		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);

		if(!ticketing.isRecurringEvent()){
			ticketing.setEventStartDate(
					TimeZoneUtil.getDateInUTC(ticketingDto.getEventStartDate(), event.getEquivalentTimeZone(), null));
			ticketing.setEventEndDate(
					TimeZoneUtil.getDateInUTC(ticketingDto.getEventEndDate(), event.getEquivalentTimeZone(), null));
            if(ticketing.getPreEventAccessMinutes()!=null){
                chargebeeService.checkPreEventAccessByPlanType(event, ticketing.getPreEventAccessMinutes());
            }
            ticketing.setPreEventAccessMinutes(ticketing.getPreEventAccessMinutes()==null ? 30 : ticketing.getPreEventAccessMinutes());
		}
		if (isNotBlank(ticketingDto.getEventVenueType()) && EnumEventVenue.VENUE.name().equals(ticketingDto.getEventVenueType())) {
			ticketing.setEventAddress(ticketingDto.getEventAddress());
			ticketing.setLatitude(ticketingDto.getLatitude());
			ticketing.setLongitude(ticketingDto.getLongitude());
		}else {
			ticketing.setEventAddress(null);
		}
		if(isNotBlank(ticketingDto.getEventVenueType())) {
			ticketing.setOnlineEvent(EnumEventVenue.ONLINE_VIRTUAL_EVENT.name().equals(ticketingDto.getEventVenueType()));
		}
		if(null != ticketingDto.getAllowEditingOfDisclaimer()){
			ticketing.setAllowEditingOfDisclaimer(ticketingDto.getAllowEditingOfDisclaimer());
		}
		if(StringUtils.isNotEmpty(ticketingDto.getCustomDisclaimer())){
			ticketing.setCustomDisclaimer(ticketingDto.getCustomDisclaimer());
		}

		this.save(ticketing);
		if (event.getTicketingId() == null) {
			event.setTicketingId(ticketing.getId());
            eventRepoService.save(event);
		}
		eventRepoService.save(event);

	}

	@Override
	public List<EventCategoryDto> getCategories(Event event, String searchString) {
		if (isNotBlank(searchString)) {
			return seatingCategoryRepository.findCategoriesByEventAndSearchString(event, StringUtils.lowerCase(searchString));
		} else {
			return seatingCategoryRepository.findCategoriesByEvent(event);
		}
	}

	@Override
	public void saveTicketingPdf(String ticketPdfDesign, Event event) {
		Ticketing ticketing = ticketingRepository.findByEventid(event);
		if (null != ticketing) {
			ticketing.setTicketPdfDesign(ticketPdfDesign);
			this.save(ticketing);
		}
	}

	@Override
	public String getTicketPdfByHost(Event event, Boolean onlyMainBody) throws IOException {
        Ticketing ticketing = ticketingRepository.findByEventid(event);
        if(isNotBlank(ticketing.getTicketPdfDesign())){
            return ticketing.getTicketPdfDesign();
        } else if (event.getWhiteLabel() != null) {
            Optional<WhiteLabelSettings> whiteLabelSettingsOptional = roWhiteLabelSettingRepository.findByWhiteLabelId(event.getWhiteLabelId());
            WhiteLabelSettings whiteLabelSettings = whiteLabelSettingsOptional.orElseGet(WhiteLabelSettings::new);
            if (isNotBlank(whiteLabelSettings.getWhiteLabelTicketPdfDesign())) {
                return whiteLabelSettings.getWhiteLabelTicketPdfDesign();
            }
        }
        return Boolean.TRUE.equals(onlyMainBody) ? StringUtils.substringBetween(freemarkerMailConfiguration.getTemplate(TemplateId.TICKET_ORDER_PDF_ORIGINAL.getValue()).toString(), "<body>", "</body>")
                : freemarkerMailConfiguration.getTemplate(TemplateId.TICKET_ORDER_PDF_ORIGINAL.getValue()).toString();
	}

	@Override
	public Set<String> getEventsByEventKeyAndFromTo(long from, long to) {
		Set<String> eventKeys = new HashSet<>();
		List<Ticketing> ticketingList = ticketingRepository.getEventChartKeysFromTo(from, to);
		ticketingList.forEach(ticketing -> {
			if (ticketing.isRecurringEvent()) {
				List<RecurringEvents> recurringEventsList = recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(ticketing.getEventid());
				for (RecurringEvents recurringEvents : recurringEventsList) {
					String eventKey = TicketingUtils.getEventKey(ticketing.getEventid().getEventId(), ticketing.isRecurringEvent(), recurringEvents.getId());
					eventKeys.add(eventKey);
				}
			} else {
				eventKeys.add(String.valueOf(ticketing.getEventid().getEventId()));
			}
		});
		return eventKeys;
	}

	public class ChartTable implements Runnable{
		private final Event event;
		public ChartTable(Event event){
			this.event = event;
		}

		@Override
		public void run(){
			handleChartTable();
		}

		private void handleChartTable() {
			updateChartTickets(event);
		}
	}

	@Override
	public void requireToCallUpdateChartTableModes(Event event) {
		ExecutorService executorService = Executors.newFixedThreadPool(5);
		executorService.execute(new ChartTable(event));
	}

	@Override
	public List<TicketSettingAttributeDto> getAllAddonCustomAttributes(Event event, boolean isForBuyer, Long recurringEventId) {
        Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());
        if(ticketing.isRecurringEvent() && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
            List<TicketHolderRequiredAttributes> ticketHolderAttributes = ticketHolderRequiredAttributesService.getAllAttributesExcludingSubQueByRecurringEventId(event, recurringEventId, DataType.ADDON);
            if(CollectionUtils.isEmpty(ticketHolderAttributes)) {
                return Collections.emptyList();
            }
            ticketHolderAttributes.sort(Comparator.comparingInt(isForBuyer?TicketHolderRequiredAttributes::getBuyerAttributeOrder:TicketHolderRequiredAttributes::getHolderAttributeOrder));
            return ticketHolderAttributes.stream().map(TicketSettingAttributeDto::new).collect(Collectors.toList());
        }
		List<TicketHolderRequiredAttributes> attributes =  ticketHolderRequiredAttributesService.getAllAddonCustomAttributes(event, isForBuyer);
		return attributes.stream().map(TicketSettingAttributeDto::new).collect(Collectors.toList());
	}

	@Override
	public List<Event> getListOfEventByEventIdsAndRecurringEnableAndChartKeyNotNull(long from, long to) {
		return ticketingRepository.getListOfEventByEventIdsAndRecurringEnableAndChartKeyNotNull(from, to, true);
	}

	@Override
	public void updateChartTickets(Event event) {
		Ticketing ticketing = ticketingRepository.findByEventid(event);
		Map<String, TableBookingMode> tableBookingModes = new HashMap<>();
		try {
			Thread.sleep(5000);
		} catch (InterruptedException ex) {
			log.info("InterruptedException|updateChartTickets ", ex);
		}
		if (isNotBlank(ticketing.getChartKey())) {
			Map<Long, Set> map = seatsIoService.updateTickets(ticketing.getChartKey());
			List<TicketingType> ticketingTypes = ticketingTypeTicketService.getAllByTicketingAndCreatedFromNullOnlyPaidOrderByPosition(event);

			for (Map.Entry<Long, Set> setEntry : map.entrySet()) {
                TableBookingMode tableBookingMode;
				if (ticketingDisplayService.isBookBySeat(ticketingTypes, setEntry.getKey())) {
					tableBookingMode = TableBookingMode.BY_SEAT;
				} else {
					tableBookingMode = TableBookingMode.BY_TABLE;
				}
                log.info("table Booking modes {}", tableBookingMode);
				setEntry.getValue().forEach(e -> tableBookingModes.put((String) e, tableBookingMode));
			}

			if (ticketing.isRecurringEvent()) {
                log.info("Update tickets in Seats.io library with modes {} for recurringEvent", tableBookingModes);
				List<Long> recurringEventsIds = recurringEventsScheduleService.getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(event.getEventId());
				seatsIoService.updateTicketsToSeats(tableBookingModes, event, ticketing.getChartKey(), recurringEventsIds);
			} else {
                log.info("Update tickets in Seats.io library with modes {}", tableBookingModes);
				seatsIoService.updateTicketsToSeats(tableBookingModes, event, ticketing.getChartKey());
			}
		}
	}

	@Override
	public DisplayFeeDto calculateFeesByTicketIdAndPrice(HostTicketTypeDto hostTicketTypeDto, Event event) {

		DisplayFeeDto displayFeeDto = new DisplayFeeDto(hostTicketTypeDto.getTicketTypeId(), hostTicketTypeDto.getTicketQuantity(), hostTicketTypeDto.getTicketPrice());
		StripeDTO stripe = stripeService.getStripeFeesByEvent(event);
		SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId());
		TicketingType ticketingType = setTicketingTypeValues(hostTicketTypeDto);
        double capAmount=transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(ticketingType.getTicketTypeFormat(),event);
        double vatTaxRate = ticketingType.isEnableTicketLevelVatTax() ? ticketingType.getVatTaxPercentage() : vatTaxService.getVatTaxRate(event.getEventId());
        FeePerTicket feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, ticketingType.getPrice(), 1, 1, false,capAmount, vatTaxRate,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();
        double totalPayable = DoubleHelper.roundValueTwoDecimal(feePerTicket.getTotalPayable());
		displayFeeDto.setTotalPayable(totalPayable);
		if(ticketingType.isPassfeetobuyer() && ticketingType.isPassFeeVatToBuyer()) {
            displayFeeDto.setAeFees(feePerTicket.getAeFee());
//            displayFeeDto.setWlFees(feePerTicket.getWlAFee() + feePerTicket.getWlBFee());
            displayFeeDto.setOrganizerRevenue(feePerTicket.getTicketPrice());
            displayFeeDto.setAeFeeCharged(feePerTicket.getAeFee());
            displayFeeDto.setVatTaxFeeCharged(feePerTicket.getVatTax());
            displayFeeDto.setVatTaxFee(feePerTicket.getVatTax());
            if(!hostTicketTypeDto.isPayLater()) {
                displayFeeDto.setCcFeeCharged(feePerTicket.getProcessingFee());
                displayFeeDto.setCcFees(feePerTicket.getProcessingFee());
            }else{
                displayFeeDto.setTotalPayablePayLater(feePerTicket.getTotalPayablePayLater());
            }
        }else if(!ticketingType.isPassfeetobuyer() && Boolean.TRUE.equals(ticketingType.isPassFeeVatToBuyer())){
            double ccFees = 0;
            if (!hostTicketTypeDto.isPayLater()) {
                ccFees = calculateCcFees(feePerTicket.getTicketPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee());
                displayFeeDto.setCcFeeCharged(ccFees);
            }else {
                displayFeeDto.setTotalPayablePayLater(feePerTicket.getTotalPayablePayLater());
            }
            displayFeeDto.setVatTaxFee(feePerTicket.getVatTax());
            displayFeeDto.setVatTaxFeeCharged(feePerTicket.getVatTax());
            displayFeeDto.setOrganizerRevenue(feePerTicket.getTicketPrice() - feePerTicket.getAeFee() - ccFees);
            displayFeeDto.setAeFeeCharged(feePerTicket.getAeFee());
        } else {
            double ccFees = 0;
            if (!hostTicketTypeDto.isPayLater()) {
                ccFees = calculateCcFees(feePerTicket.getTicketPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee());
                displayFeeDto.setCcFeeCharged(ccFees);
            } else {
                displayFeeDto.setTotalPayablePayLater(feePerTicket.getTotalPayablePayLater());
            }
            displayFeeDto.setOrganizerRevenue(feePerTicket.getTicketPrice() - feePerTicket.getAeFee() - ccFees - (feePerTicket.getTicketPrice()*(vatTaxRate/100)));
            displayFeeDto.setAeFeeCharged(feePerTicket.getAeFee());
            displayFeeDto.setVatTaxFeeCharged(feePerTicket.getVatTax());

        }
		return displayFeeDto;
	}

	private TicketingType setTicketingTypeValues(HostTicketTypeDto hostTicketTypeDto) {
		TicketingType ticketingType = new TicketingType();
		TicketingFeeDto ticketingFeeDto = hostTicketTypeDto.getTicketingFee();
		ticketingType.setId(hostTicketTypeDto.getTicketTypeId());
		ticketingType.setPrice(hostTicketTypeDto.getTicketPrice());
		ticketingType.setNumberOfTicketPerTable(hostTicketTypeDto.getTicketsPerTable());
		ticketingType.setPassfeetobuyer(hostTicketTypeDto.isPassFeeToBuyer());
		ticketingType.setBundleType(hostTicketTypeDto.getBundleType());
		ticketingType.setTicketTypeFormat(hostTicketTypeDto.getTicketTypeFormat());
        ticketingType.setPayLater(hostTicketTypeDto.isPayLater());
        ticketingType.setPassFeeVatToBuyer(hostTicketTypeDto.isPassFeeVatToBuyer());
        ticketingType.setEnableTicketLevelVatTax(hostTicketTypeDto.isEnableTicketLevelVatTax());
        ticketingType.setVatTaxPercentage(hostTicketTypeDto.getVatTaxPercentage());
		if (ticketingFeeDto!=null) {
            ticketingType.setAeFeeFlat(ticketingFeeDto.getAeFeeFlat());
            ticketingType.setAeFeePercentage(ticketingFeeDto.getAeFeePercentage());
            ticketingType.setWlAFeeFlat(ticketingFeeDto.getWlFeeFlat());
            ticketingType.setWlAFeePercentage(ticketingFeeDto.getWlFeePercentage());
        }
		return ticketingType;
	}

	@Override
	public EnableOrderConfirmationDto isOrderConfirmEmailDisabled(Long ticketingId) {
        Optional<Ticketing> ticketingOptional = this.findById(ticketingId);
        if (ticketingOptional.isPresent()) {
			Ticketing ticketing = ticketingOptional.get();
            EnableOrderConfirmationDto enableOrderConfirmationDto = new EnableOrderConfirmationDto();
            enableOrderConfirmationDto.setEnabledOrderConfirmationEmail(ticketing.isEnableOrderConfirmationEmail());
			return enableOrderConfirmationDto;
		} else {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
		}
	}

	@Override
	public EnableOrderConfirmationDto setOderConfirmationEmail(EnableOrderConfirmationDto enableOrderConfirmationDto, Long ticketingId) {
		Optional<Ticketing> ticketingOptional = this.findById(ticketingId);
		if (ticketingOptional.isPresent()) {
			Ticketing ticketing = ticketingOptional.get();
			ticketing.setEnableOrderConfirmationEmail(enableOrderConfirmationDto.isEnabledOrderConfirmationEmail());
			this.save(ticketing);
			return enableOrderConfirmationDto;
		} else {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
		}
	}
    @Override
    public boolean setShowMemberCountInCheckout(boolean isShowMemberCountInCheckout , Long ticketingId) {
        Optional<Ticketing> ticketingOptional = this.findById(ticketingId);
        if (ticketingOptional.isPresent()) {
            Ticketing ticketing = ticketingOptional.get();
            ticketing.setShowMemberCountInCheckout(isShowMemberCountInCheckout);
            this.save(ticketing);
            return isShowMemberCountInCheckout;
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
        }
    }

    @Override
    public boolean setAccessOfBeeFreeTemplate(boolean allowToAccessBeeFree, Event event, User user,String emailType) {
        Optional<Ticketing> ticketingOptional = this.findById(event.getTicketingId());
        if (ticketingOptional.isPresent()) {
            log.info("setAccessOfBeeFreeTemplate for eventId {} by userId {} and email {} value of allowToAccessBeeFree {}",event.getEventId(), user.getUserId(), user.getEmail(), allowToAccessBeeFree);
            Ticketing ticketing = ticketingOptional.get();
           if(StringUtils.isNotBlank(emailType)) {
               if(!chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.ADVANCED_EMAIL_MARKETING) && allowToAccessBeeFree){
                   throw new NotAcceptableException(NotAceptableExeceptionMSG.ADVANCED_EMAIL_BUILDER_NOT_AVAILABLE_FOR_PLAN);
               }
               if (EmailType.REMINDER.name().equals(emailType)) {
                   ticketing.setAccessBeeFreeReminderTemplate(allowToAccessBeeFree);
               } else if (EmailType.ORDER_CONFIRMATION.name().equals(emailType)) {
                   ticketing.setAccessBeeFreeTemplate(allowToAccessBeeFree);
               }
           }
            this.save(ticketing);
            return allowToAccessBeeFree;
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
        }
    }

    @Override
    public void updateTicketAddedChecklistFlag(Event event, boolean isTicketAdded) {
        EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
        if (null != eventChecklist &&  isTicketAdded != eventChecklist.isTicketAdded()) {
            eventChecklist.setTicketAdded(isTicketAdded);
            eventChecklistService.save(eventChecklist);
        }
    }

    @Override
    public TicketingBeeFreeDto isAccessOfBeeFreeTemplate(Long ticketingId) {
        TicketingBeeFreeDto ticketingBeeFreeDto = new TicketingBeeFreeDto();
        Optional<Ticketing> ticketingOptional = this.findById(ticketingId);
        if (ticketingOptional.isPresent()) {
            ticketingBeeFreeDto.setAccessBeeFreeOrderConfirmationTemplate(ticketingOptional.get().isAccessBeeFreeTemplate());
            ticketingBeeFreeDto.setAccessBeeFreeReminderTemplate(ticketingOptional.get().isAccessBeeFreeReminderTemplate());
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
        }
        return ticketingBeeFreeDto;
    }

    public boolean isValidAccessOfBeeFreeTemplate(Long ticketingId,String emailType) {
        Optional<Ticketing> ticketingOptional = this.findById(ticketingId);
        if (ticketingOptional.isPresent()) {
            if(EmailType.ORDER_CONFIRMATION.name().equals(emailType)) {
                return ticketingOptional.get().isAccessBeeFreeTemplate();
            } else if(EmailType.REMINDER.name().equals(emailType)) {
                return ticketingOptional.get().isAccessBeeFreeReminderTemplate();
            } else {
                return false;
            }
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
        }
    }

    @Override
    public void saveBeeFreeTemplate(BeeFreeTemplateDto beeFreeTemplateDto, Event event, User user) {
	    validation(event.getTicketingId(), beeFreeTemplateDto.getEmailType().name());
        log.info("saveBeeFreeTemplate for eventId {} by userId {} ",event.getEventId(), user.getUserId());
        BeeFree beeFree = findByEmailTypeAndEvent(event,beeFreeTemplateDto.getEmailType());
        extracted(beeFreeTemplateDto, event.getEventId(), beeFree,true);
    }

    private void extracted(BeeFreeTemplateDto beeFreeTemplateDto, Long eventId, BeeFree beeFree, boolean isCustomTemplateEnabled) {
        if (beeFree != null) {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_ALREADY_EXIST);
        }
        beeFree = new BeeFree();
        beeFree.setEventId(eventId);
        beeFree.setEmailType(beeFreeTemplateDto.getEmailType());
        beeFree.setCustomTemplateEnabled(isCustomTemplateEnabled);
        commonUpdateBeefree(beeFreeTemplateDto, beeFree);
    }

    @Override
    public void saveBeeFreeTemplateByEmailType(BeeFreeTemplateDto beeFreeTemplateDto, User user) {
        log.info("saveBeeFreeTemplate for email type {} by userId {} ",beeFreeTemplateDto.getEmailType(), user.getUserId());
        BeeFree beeFree = getBeeFreeByEmailTypeAndEventIdZero(beeFreeTemplateDto.getEmailType());
        extracted(beeFreeTemplateDto, 0L, beeFree,false);

    }

    @Override
    public void updateBeefreeTemplateFromSuperAdminSide(Long beefreeTemplateId , BeeFreeTemplateDto beeFreeTemplateDto) {
        Optional<BeeFree> beeFree = beeFreeRepository.findById(beefreeTemplateId);
        if (!beeFree.isPresent()) {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_NOT_FOUND);
        }
        commonUpdateBeefree(beeFreeTemplateDto, beeFree.get());
    }
    private void updateBeefreeTemplateByEvent(Long beefreeTemplateId ,BeeFreeTemplateDto beeFreeTemplateDto,Event event) {
        BeeFree beeFree = this.getBeeFreeByEventAndId(beefreeTemplateId,event);
        if (null == beeFree) {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_NOT_FOUND);
        }
        commonUpdateBeefree(beeFreeTemplateDto,beeFree);
    }

    @Override
    @Transactional
    public ResponseDto deleteBeefreeTemplateByIdAndEmailType(Long beefreeTemplateId, User user, String emailType) {

        Optional<BeeFree> beeFree = beeFreeRepository.findById(beefreeTemplateId);
        if (beeFree.isEmpty()) {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_NOT_FOUND);
        }

        if(!EmailType.valueOf(emailType).equals(beeFree.get().getEmailType())){
            return new ResponseDto(Constants.ERROR_MESSAGE, ResponseMessage.MESSAGE_TEXT_9998);
        }

        beeFreeRepository.deleteById(beefreeTemplateId);
        return new ResponseDto(Constants.SUCCESS, TEMPLATE_RESET);
    }

    @Override
    public boolean isCollectTicketHolderAttributesEnable(Long eventId) {
        return ticketingRepository.isCollectTicketHolderAttributesEnable(eventId);
    }

    @Transactional
    public void commonUpdateBeefree(BeeFreeTemplateDto beeFreeTemplateDto, BeeFree beeFree) {
        beeFree.setJsonValue(beeFreeTemplateDto.getBeeFreeJson());
        beeFree.setHtmlValue(beeFreeTemplateDto.getBeeFreeHtml());
        beeFree.setCustomJson(beeFreeTemplateDto.getCustomJson());
        saveBeefree(beeFree);
    }

    private void validation(Long ticketingId, String emailType) {
        boolean allowToAccess = isValidAccessOfBeeFreeTemplate(ticketingId,emailType);
        if(!allowToAccess){
            throw new NotAcceptableException(TicketingExceptionMsg.EVENT_CAN_NOT_USE_BEEFREE_TEMPLATE);
        }
    }

    @Override
    public void updateBeeFreeTemplate(Long beeFreeTemplateId, BeeFreeTemplateDto beeFreeTemplateDto, Event event, User user) {
        log.info("updateBeeFreeTemplate for eventId {} by userId {} of beeFreeTemplateId {}",event.getEventId(), user.getUserId(),beeFreeTemplateId);
        validation(event.getTicketingId(), beeFreeTemplateDto.getEmailType().name());
        updateBeefreeTemplateByEvent(beeFreeTemplateId, beeFreeTemplateDto,event);
    }

    @Override
    public BeeFreeTemplateDto getBeeFreeTemplate(Event event,String emailType,User user){
        String path= STRING_EMPTY;
        String wlLogo = null;
        String wlHeaderLogo=null;
        boolean isWhiteLabel=false;
        if(null != event.getWhiteLabel()){
            isWhiteLabel=true;
            if (null != event.getWhiteLabel().getLogoImage()){
                wlLogo=imageUrl.concat(event.getWhiteLabel().getLogoImage());
            }else{
                wlLogo=imageConfiguration.getImagePreFixWithCloudinaryUrl().concat(DEFAULT_AE_IMAGES).concat("/Ae_Icn_700x350.png");
            }
            if (null != event.getWhiteLabel().getHeaderLogoImage()){
                wlHeaderLogo=imageUrl.concat(event.getWhiteLabel().getHeaderLogoImage());
            }else {
                wlHeaderLogo=imageConfiguration.getImagePreFixWithCloudinaryUrl().concat(DEFAULT_AE_IMAGES).concat("/Smooth_Accelevents_Default_Event_Logo_Black.png");
            }
            if(EmailType.REMINDER.name().equals(emailType)){
                path=PATH_OF_DEFAULT_REMINDER_JSON;
            } else if(EmailType.ORDER_CONFIRMATION.name().equals(emailType)){
                path=PATH_OF_DEFAULT_BEEFREE_JSON_FOR_WHITELABEL;
            }
        } else {
            if(EmailType.REMINDER.name().equals(emailType)){
                path=PATH_OF_DEFAULT_REMINDER_JSON;
            } else if(EmailType.ORDER_CONFIRMATION.name().equals(emailType)){
                path=PATH_OF_DEFAULT_BEEFREE_JSON;
            }
        }

        BeeFree beeFree = findByEmailTypeAndEvent(event,EmailType.valueOf(emailType));
        if(beeFree == null){
            return getBeeFreeTemplateDto(TEMPLATES_SLASH.concat(path),isWhiteLabel,wlLogo,wlHeaderLogo,user,event, EmailType.valueOf(emailType));
        }
        return new BeeFreeTemplateDto(beeFree);
    }

    @Override
    public String generateEngageEmailTemplate(String engageEmail) {
        return getJson(generatePathByEmailType(engageEmail));
    }

    private String hidePerksOfAttendingForInPersonEvent(Event event,String json){
        String newJson=null;
        if (event.getEventFormat().equals(EventFormat.IN_PERSON) && null != json) {
            try {
                JSONObject jsonObject = new JSONObject(json);
                JSONArray array = jsonObject.getJSONObject("page").getJSONArray("rows");
                for (int i = 0; i <= array.length(); i++) {
                    String newStr = array.getJSONObject(i).toString();
                    if (newStr.contains(VARIABLE_VIRTUAL_EVENT)) {
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 1);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 2);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 3);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 4);
                        break;
                    }
                }
                newJson = jsonObject.toString();
            }catch (JSONException e){
                log.info("JSON Format Exception".concat(e.getMessage()));
            }
        } else {
            if (null!=json) {
                newJson = json.replace(VARIABLE_VIRTUAL_EVENT, STRING_EMPTY);
            }
        }
        return newJson;
    }

    private BeeFreeTemplateDto getBeeFreeTemplateDto(String path,boolean isWhiteLabel,String wlLogo,String wlHeaderLogo,User user,Event event,EmailType emailType) {
        String json = getJson(path);
        String newJson=null;
        String imagePrefixForWhiteLabel;
        try {
            if (emailType.equals(EmailType.REMINDER)){
               newJson=hidePerksOfAttendingForInPersonEvent(event,json);
            }
            if (emailType.equals(EmailType.ORDER_CONFIRMATION) && event.getEventFormat().equals(EventFormat.IN_PERSON) && null != json) {
                JSONObject jsonObject = new JSONObject(json);
                JSONArray array = jsonObject.getJSONObject("page").getJSONArray("rows");
                for (int i = 0; i <= array.length(); i++) {
                    String newStr = array.getJSONObject(i).toString();
                    if (newStr.contains(VARIABLE_VIRTUAL_EVENT)) {
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 1);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 2);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 3);
                        jsonObject.getJSONObject("page").getJSONArray("rows").remove(i - 4);
                        break;
                    }
                }
                newJson = jsonObject.toString();
            } else {
                if (null!=json && !emailType.equals(EmailType.REMINDER)) {
                    newJson = json.replace(VARIABLE_VIRTUAL_EVENT, STRING_EMPTY);
                }
            }
        }catch (JSONException e){
            log.info("JSON Format Exception".concat(e.getMessage()));
        }

        if (newJson==null){
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_NOT_FOUND);
        } else if (isWhiteLabel && wlLogo!=null && wlHeaderLogo !=null) {
                String replacedJson=newJson.replace("${whiteLabelLogo}",wlHeaderLogo);
                String wlEventReplacedJson=replacedJson.replace("${whiteLabelEventLogo}",wlLogo);
                imagePrefixForWhiteLabel=wlEventReplacedJson.replace("${imagePrefix}",imageLogoUrl);
                return new BeeFreeTemplateDto(imagePrefixForWhiteLabel);
        } else {
            String imagePrefix=newJson.replace("${imagePrefix}",imageLogoUrl);
            if (this.roUserService.isSuperAdminUser(user)){
                String headerImage=imagePrefix.replace("${imagePrefix}/Smooth_Accelevents_Default_Event_Logo_Black.png","${headerImage}");
                String imageLogo=headerImage.replace("${imagePrefix}/Ae_Icn_700x350.png","${image}");
                return new BeeFreeTemplateDto(imageLogo);
            }else {
                return new BeeFreeTemplateDto(imagePrefix);
            }

        }
    }

    private String getJson(String path) {
        String json = null;
        try {
            json = FileReaderUtilsClass.getBeeFreeTemplate(path);
            if (null!=json && json.contains("${cloudinary_image_prefix}"))
            {
                json=json.replace("${cloudinary_image_prefix}",imageConfiguration.getImagePreFixWithCloudinaryUrl());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return json;
    }

    @Override
    public BeeFreeTemplateDto getBeeFreeTemplateByEmailType(EmailType emailType,User user) {
        BeeFree beeFree = getBeeFreeByEmailTypeAndEventIdZero(emailType);
        Event event=new Event();
        if(beeFree == null){
            return getBeeFreeTemplateDto(generatePathByEmailType(emailType.name()),false,null,null,user,event,emailType);
        }
        return new BeeFreeTemplateDto(beeFree);
    }

    private String generatePathByEmailType(String emailType){
        switch (emailType) {
            case "ORDER_CONFIRMATION":
                return TEMPLATES_SLASH.concat("beefree_default_json.json");
            case "ATTENDEE_CHECKIN":
                return TEMPLATES_SLASH.concat("ATTENDEE_CHECKIN_V1.json");
            case "WAIT_LIST_RELEASE":
                return TEMPLATES_SLASH.concat("WAIT_LIST_RELEASE_V1.json");
            case "ADMIN_ORDER_CONFIRMATION_EMAIL":
                return TEMPLATES_SLASH.concat("ADMIN_ORDER_CONFIRMATION_EMAIL_V1.json");
            case "ORGANIZER_TEAM_ACCESS":
                return TEMPLATES_SLASH.concat("ORGANIZER_TEAM_ACCESS_V1.json");
        }
        return TEMPLATES_SLASH.concat(emailType).concat(DOT_JSON);
    }

    @Override
    public void sendTestEmailForBeeFreeTemplate(EmailType emailType, User superAdminUser) {
        switch (emailType) {
            case WAIT_LIST_RELEASE:
                waitListTestEmail(superAdminUser);
                break;
            case ATTENDEE_CHECKIN:
                attendeeCheckInTestEmail(superAdminUser);
                break;
            case MAGIC_LINK_CODE:
                sendGridMailPrepareService.sendMagicLinkTestEmail(superAdminUser);
                break;
            case RESET_PASSWORD:
                resetPasswordTestEmail(superAdminUser);
                break;
            case ORGANIZER_TEAM_ACCESS: teamService.sendOrganizationInviteTestEmail(superAdminUser);
                break;
            case STAFF_ACCESS :
                staffService.sendStaffAddTestEmail(superAdminUser);
                break;
            case REQUEST_NOTIFICATION:
                sendGridMailPrepareService.sendTestConnectionRequestEmail(superAdminUser);
                break;
            case PARTICIPANT_QUESTION:
                sendGridMailPrepareService.sendParticipantQuestionTestEmail(superAdminUser);
                break;
            case SUPPORT_QUESTION:
                sendGridMailPrepareService.sendTestSupportEmail(superAdminUser);
                break;
            case CART_ABANDONMENT:
                sendGridMailPrepareService.sendTestCartAbandonMentEmail(superAdminUser);
                break;
            case MEETING_NOTIFICATION:
                sendGridMailPrepareService.sendTestMeetingRequestEmail(superAdminUser);
                break;
            case EVENT_TICKET_REFUND:
                sendGridMailPrepareService.sendTestTicketingRefundMail(superAdminUser);
                break;
            case BUYER_RECEIPT_AUCTION:
                sendGridMailPrepareService.sendTestBuyerReceiptAuction(superAdminUser);
                break;
            case ADMIN_ORDER_CONFIRMATION_EMAIL:
                sendGridMailPrepareService.sendTestAdminConfirmationEmail(superAdminUser);
                break;
            default:
                log.info("Email type not match {}", emailType);
                break;
        }
    }

    private void attendeeCheckInTestEmail(User user) {
        sendGridMailPrepareService.sendTestAttendeeCheckInEmail(user);
    }

    private void waitListTestEmail(User superAdminUser) {

        sendGridMailPrepareService.sendTestWaitListEmail(superAdminUser, "1,2", WAIT_LIST_MESSAGE);
    }
    private void resetPasswordTestEmail(User superAdminUser){
        userService.sendResetPasswordTestEmail(superAdminUser);
    }

    @Override
    public void saveBeefree(BeeFree beeFree) {
        beeFreeRepository.save(beeFree);
    }

    public BeeFree getBeeFreeByEventAndId(Long beeFreeTemplateId , Event event) {
        return beeFreeRepository.findByIdAndEvent(beeFreeTemplateId,event);
    }

    @Override
    public BeeFree getBeeFreeByEvent(Event event) {
        return beeFreeRepository.findByEvent(event);
    }
    @Override
    public List<BeeFree> getAllBeeFreeByEvent(Event event) {
        return beeFreeRepository.findAllByEvent(event);
    }

    @Override
    public BeeFree getBeeFreeByEmailTypeAndEventIdZero(EmailType emailType) {
        return beeFreeRepository.findByEmailTypeAndEventIdZero(emailType);
    }

    @Override
    @Transactional
    public void activateTicketingModule(Event event, EventTicketingDto ticketingDto) {

        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        if (ticketing != null && event.getTicketingEnabled() && !ticketing.getActivated()) {
                ticketing.setActivated(true);
                ticketingRepository.save(ticketing);
        }
        log.info("In activateTicketingModule method Activating ticketing module for eventId {} and value of Activated flag {} ", event.getEventId(), ticketing != null && ticketing.getActivated());
	}

    @Override
    public boolean isShowRegistrationButton(Event event) {
        return ticketingRepository.isShowRegistrationButton(event);
    }

    @Override
    public void updateRecStatus(Long eventId, RecordStatus recordStatus){
        ticketingRepository.updateStatusDelete(eventId,recordStatus);
    }

    @Override
    public void updateRecordStatusOfTicketingByEventIds(List<Long> eventIds, RecordStatus recordStatus){
        ticketingRepository.updateRecordStatusOfTicketingByEventIds(eventIds,recordStatus);
        log.info("Updating record status of ticketing for event ids: {} and record status {}", eventIds, recordStatus.name());
    }

    @Override
    public void resetEventDates(List<Long> listOfEventId, int days) {
	    log.info("Start of reset event date for eventIds {} and for {} days",listOfEventId,days);
        List<Ticketing> ticketingList = ticketingRepository.findListOfticketingByEventIds(listOfEventId);
        List<Ticketing> updatedList = new ArrayList<>();
        if(!ticketingList.isEmpty()){
            ticketingList.forEach(ticketing -> {
                        ticketing.setEventStartDate(DateUtils.addDaysInDate(ticketing.getEventStartDate(), days));
                        ticketing.setEventEndDate(DateUtils.addDaysInDate(ticketing.getEventEndDate(), days));
                        ticketing.setUpdatedAt(new Date());
                        updatedList.add(ticketing);
            });
            ticketingRepository.saveAll(updatedList);
        }
        log.info("End of reset event date for eventIds {} and for {} days",listOfEventId,days);
    }
    @Override
    @Transactional
    public void updateCustomTemplateEnabled(EmailType type,long beefreeTemplateId,boolean isCustomTemplateEnabled) {
        log.info("updateCustomTemplateEnabled templateId {} | emailType {} | isCustomTemplateEnabled {}",beefreeTemplateId,type,isCustomTemplateEnabled);
        BeeFree beeFree = beeFreeRepository.findByEmailTypeAndId(type,beefreeTemplateId);
        if (null != beeFree) {
            beeFree.setCustomTemplateEnabled(isCustomTemplateEnabled);
            saveBeefree(beeFree);
        } else {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_NOT_FOUND);
        }

    }
    @Override
    public BeeFree findByEmailTypeAndEvent(Event event,EmailType emailType){
     return beeFreeRepository.findByEventAndEmailType(event,emailType);
    }
    @Override
    @Transactional
    public List<BeeFreeTemplateDto> findByEventIdZeroBeeFreeTemplate(){
        return beeFreeRepository.findByEventIdZero();
    }

    @Override
    public List<KeyValueDto> getTicketHolderOrBuyerEnabledAttribute(Event event) {
        List<String> enabledAttributeNameList = ticketHolderRequiredAttributesService.getHolderOrBuyerEnabledAttribute(event);
        enabledAttributeNameList.add(DISCLAIMER);
        return enabledAttributeNameList.stream().map(e -> new KeyValueDto(e, e)).collect(toList());
    }

    @Override
    public boolean isValidEmailType(String emailType){
        if(!ObjectUtils.containsConstant(EmailType.values(),emailType, true)){
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_NOT_FOUND);
        }
        return true;
    }

    @Override
    public boolean isEnableAutoAssignedSequence(Event event) {
        return eventDesignDetailService.isEnableAutoAssignedSequence(event);
    }

    @Override
    public double getProcessingFeesExcludingTax(TicketingBuyerDataFromDB ticketingOrder) {
        double totalProcessingFees = ticketingOrder.getOrderPaidAmount() - ticketingOrder.getTotalTicketPrice();
        return totalProcessingFees - ticketingOrder.getOrderVatTaxFee() - ticketingOrder.getOrderSalesTaxFee();
    }

    /**
     * Get Event start date with adjusted Pre-Event access time
     */
    @Override
    public Date getEventStartDate(Event event) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(ticketing.getEventStartDate());
        calendar.add(Calendar.MINUTE, -ticketing.getPreEventAccessMinutes());
        return calendar.getTime();
    }

    @Override
    public boolean setEnterEventButtonInReminderEmail(boolean isVisibleEnterEventButton, Long ticketingId) {
        Optional<Ticketing> ticketingOptional = this.findById(ticketingId);
        if (ticketingOptional.isPresent()) {
            log.info("request received for show/hide enter event button in reminder email template {} for ticketing{} ",isVisibleEnterEventButton, ticketingId);
            Ticketing ticketing = ticketingOptional.get();
            ticketing.setShowEnterEventButtonInReminderTemplate(isVisibleEnterEventButton);
            this.save(ticketing);
            log.info("successfully updated enter event button to {} in reminder email template for ticketing  {}", isVisibleEnterEventButton, ticketingId);
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
        }
        return isVisibleEnterEventButton;
    }

    @Override
    public boolean isEnabledAccessEventButtonInReminderTemplate(Long ticketingId) {
        Optional<Ticketing> ticketingOptional = this.findById(ticketingId);
        if (ticketingOptional.isPresent()) {
            Ticketing ticketing = ticketingOptional.get();
            return ticketing.isShowEnterEventButtonInReminderTemplate();

        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.TICKETING_NOT_FOUND);
        }
    }

    @Override
    public void disableInterestAttribute(Event event, User user) {
        log.info("Disable interest tag for event {}, user {}", event.getEventId(), user.getUserId());
        VirtualEventSettings virtualEventSettings = virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId());
        List<InterestDto> allInterestsOfEvent = interestService.getAllInterestsOfEvent(String.valueOf(event.getEventId()));
        if (!virtualEventSettings.isAllowInterestToAttendee() && CollectionUtils.isEmpty(allInterestsOfEvent)) {
            ticketHolderRequiredAttributesService.disableInterestAttribute(event);
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketHolderAttributesMsg.INTEREST_TAGS_PRESENT_CANNOT_DISABLE_ATTRIBUTE);
        }
    }

    @Override
    public List<TicketSettingAttributeDto> getConditionalQuestions(Event event) {
        List<TicketHolderRequiredAttributes> conditionalAttributes = ticketHolderRequiredAttributesService.getAllConditionalQuestionByEventAndDataType(event, DataType.TICKET);
        TicketHolderRequiredAttributes interestAttribute = ticketHolderRequiredAttributesService.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType.INTEREST, event);
        if(interestAttribute!= null) {
            conditionalAttributes.add(interestAttribute);
        }
        List<TicketSettingAttributeDto> attributes = new ArrayList<>();
        for (TicketHolderRequiredAttributes field : conditionalAttributes) {
            attributes.add(new TicketSettingAttributeDto(field));
        }
        return attributes;
    }

    @Override
    public OrderAuditLogsHistoryDto getOrderAuditLogsHistory(Event event, Long orderId) {
        log.info("get order audit logs history for order {}", orderId);
        OrderAuditLogsHistoryDto orderAuditLogsHistoryDto = new OrderAuditLogsHistoryDto();
        TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderId,event);
        Set<String> paymentMethods = new HashSet<>();
        List<String> transactionIds = new ArrayList<>();
        if(ticketingOrder != null) {
            orderAuditLogsHistoryDto.setOrderId(ticketingOrder.getId());

            List<StripeTransaction> stripeTransactions = stripeTransactionService.findBySourceAndSourceIdAndEvent(StripeTransactionSource.EVENT_TICKETING, ticketingOrder.getId(), event);
            if (!stripeTransactions.isEmpty()) {
                stripeTransactions.forEach(stripeTransaction -> {
                    transactionIds.add(String.valueOf(stripeTransaction.getId()));
                    if (PaymentMethodTypes.SEPA_DEBIT.equals(stripeTransaction.getPaymentMethodType()) || PaymentMethodTypes.US_BANK_ACCOUNT.equals(stripeTransaction.getPaymentMethodType())) {
                        paymentMethods.add("BANK TRANSFER");
                    } else {
                        paymentMethods.add(stripeTransaction.getPaymentMethodType().name());
                    }
                });
            }

            List<OrderAuditLog> orderAuditLogList = orderAuditLogRepoService.findOrderAuditLogsByOrderId(ticketingOrder.getId());
            log.info("Get order audit logs list {} | id {}", orderAuditLogList, ticketingOrder.getId());
            List<OrderAuditLogDto> orderAuditLogDtos = new ArrayList<>();
            orderAuditLogList.stream().filter(Objects::nonNull).forEach(orderAuditLog -> {
                OrderAuditLogDto orderAuditLogDto = new OrderAuditLogDto(orderAuditLog);
                orderAuditLogDtos.add(orderAuditLogDto);
            });

            List<EventTicketTransaction> eventTicketTransactions = eventTicketTransactionService.findAllEventTransactionByOrderIdAndCashPayment(orderId);
            log.info("Get eventTicket transactions list {}", eventTicketTransactions);
            eventTicketTransactions.stream().filter(Objects::nonNull).collect(Collectors.toList()).forEach(e -> {
                        transactionIds.add(e.getTransactionId());
                        paymentMethods.add(e.getPaymentType().getPaymentTypeDisplayName());
                    }
            );

            orderAuditLogsHistoryDto.setTransactionId(transactionIds);
            orderAuditLogsHistoryDto.setPaymentMethod(new ArrayList<>(paymentMethods));
            orderAuditLogsHistoryDto.setOrderAuditLogs(orderAuditLogDtos);
        }
        return orderAuditLogsHistoryDto;
    }

    @Override
    public ResponseDto resetEventLevelBeeFreeTemplate(Long beeFreeTemplateId, String emailType,Event event) {
        Optional<BeeFree> beeFree = beeFreeRepository.findById(beeFreeTemplateId);
        if (beeFree.isEmpty()) {
            throw new NotFoundException(NotFoundException.BeeFreeTemplateNotFound.BEE_FREE_TEMPLATE_NOT_FOUND);
        }

        if(!EmailType.valueOf(emailType).equals(beeFree.get().getEmailType())){
            return new ResponseDto(Constants.ERROR_MESSAGE, ResponseMessage.MESSAGE_TEXT_9998);
        }
        beeFreeRepository.deleteById(beeFreeTemplateId);
        return new ResponseDto(Constants.SUCCESS, TEMPLATE_RESET);
    }

    @Override
    public Map<Long, Ticketing> findTicketingByEventIds(List<Long> eventIds) {
        HashMap<Long, Ticketing> ticketingMap = new HashMap<>();
        if (!eventIds.isEmpty()) {
            List<Ticketing> listOfTicketing = ticketingRepository.findByEventIds(eventIds);
            listOfTicketing.forEach(ticketing -> ticketingMap.put(ticketing.getEventid().getEventId(), ticketing));
        }
        return ticketingMap;
    }

    @Override
    public List<Ticketing> findByEventIds(List<Long> eventIds) {
        return ticketingRepository.findByEventIds(eventIds);
    }

	@Transactional
	@Override
	public ResponseDto checkSessionOutsideEventTime(Event event, String eventStartDate, String eventEndDate) throws ParseException {
		Date startDate = new SimpleDateFormat(LOCAL_DATE_FORMAT).parse(eventStartDate);
        Date startDateInUTC = TimeZoneUtil.getDateInUTC(startDate, event.getEquivalentTimeZone());
		Date endDate = new SimpleDateFormat(LOCAL_DATE_FORMAT).parse(eventEndDate);
        Date endDateInUTC = TimeZoneUtil.getDateInUTC(endDate, event.getEquivalentTimeZone());
		long count = sessionRepoService.countByEventIdAndSessionStartTime(startDateInUTC, event.getEventId(), endDateInUTC);
		if (count > 0) {
			return new ResponseDto(FAIL, SESSIONS_SCHEDULED_OUTSIDE_EVENT_TIME_FAILURE);
		}
		return new ResponseDto(SUCCESS, SESSIONS_SCHEDULED_OUTSIDE_EVENT_TIME_SUCCESS);
	}

    @Override
    public void resendTicketExchangeEmail(long eventTicketId, Event event, User loggedInUser) {
        EventTickets eventTicket = eventTicketsService.findEventTicketById(eventTicketId).orElseThrow(() -> new NotFoundException(NotFoundException.SessionSpeakerNotFound.EVENT_TICKET_NOT_FOUND));
        List<EventTicketTransferDetail> eventTicketTransferDetails = eventTicketTransferDetailService.findByEventIdAndEventTicketIdAndTicketingOrderId(event.getEventId(),eventTicketId,eventTicket.getTicketingOrderId());
        TicketingOrder ticketingOrder=ticketingOrderService.findByidAndEventid(eventTicket.getTicketingOrderId(),event);
        if (!eventTicketTransferDetails.isEmpty()){
            EventTicketTransferDetail eventTicketTransferDetail=eventTicketTransferDetails.get(eventTicketTransferDetails.size()-1);
            boolean isComplementaryTicket = TicketingOrder.OrderType.COMPLIMENTARY.equals(ticketingOrder.getOrderType()) ;
            boolean is100PercentDiscount = (TicketType.DONATION.equals(eventTicket.getTicketingTypeId().getTicketType()) || TicketType.PAID.equals(eventTicket.getTicketingTypeId().getTicketType())) && eventTicket.getTicketPrice() == 0 ;
            boolean isPaidTicketWithZeroAmount = isComplementaryTicket || is100PercentDiscount;
            TicketingType newTicketType=eventTicketTransferDetail.getNewTicketingType();
            TicketingType oldTicketType=eventTicketTransferDetail.getOldTicketingType();
            Ticketing ticketing = ticketingService.findByEvent(event);
            String mailSubject = "Your Updated Order Confirmation for " + event.getName();
            TicketTransferTypesDto ticketTransferTypesDto=getTicketTransferTypes(newTicketType,oldTicketType,ticketingOrder,eventTicket,event,isPaidTicketWithZeroAmount);
            ticketingOrderTransferService.handleEmailForTicketTransfer(event, newTicketType, oldTicketType, ticketingOrder, ticketing, eventTicket,
                    false, false, mailSubject,isPaidTicketWithZeroAmount,ticketTransferTypesDto,true, loggedInUser);
        }
    }

    private TicketTransferTypesDto getTicketTransferTypes(TicketingType newTicketType, TicketingType oldTicketType, TicketingOrder ticketingOrder,EventTickets eventTickets,Event event,boolean isPaidTicketWithZeroAmount ) {
        TicketTransferTypesDto ticketTransferTypesDto=new TicketTransferTypesDto();
        if ((TicketType.FREE.equals(oldTicketType.getTicketType())|| isPaidTicketWithZeroAmount) && TicketType.PAID.equals(newTicketType.getTicketType())) {
            ticketTransferTypesDto.setFreeToPaidTicketTransfer(true);
        } else if ((TicketType.FREE.equals(oldTicketType.getTicketType()) || isPaidTicketWithZeroAmount) && TicketType.FREE.equals(newTicketType.getTicketType())) {
            ticketTransferTypesDto.setFreeToFreeTicketTransfer(true);
        } else if (TicketingOrder.OrderType.COMPLIMENTARY.equals(ticketingOrder.getOrderType())) {
            ticketTransferTypesDto.setComplementaryTicketTransfer(true);
        } else if ((TicketType.PAID.equals(oldTicketType.getTicketType()) || TicketType.DONATION.equals(oldTicketType.getTicketType())) && TicketType.FREE.equals(newTicketType.getTicketType())) {
            ticketTransferTypesDto.setPaidToFreeTicketTransfer(true);
        } else if (TicketType.PAID.equals(oldTicketType.getTicketType()) && TicketType.PAID.equals(newTicketType.getTicketType())) {
            ticketTransferTypesDto.setPaidToPaidTicketTransfer(true);
            getPaidTicketTransferTypes(newTicketType,eventTickets,event,ticketTransferTypesDto);
        }
        return ticketTransferTypesDto;
    }

    private void getPaidTicketTransferTypes(TicketingType newTicketType, EventTickets eventTickets, Event event, TicketTransferTypesDto ticketTransferTypesDto) {
        StripeDTO stripe = stripeService.getStripeFeesByEvent(event);
        double capAmountForNewTicketType=transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(newTicketType.getTicketTypeFormat(),event);
        double vatTaxRate = newTicketType.isEnableTicketLevelVatTax() ? newTicketType.getVatTaxPercentage() : vatTaxService.getVatTaxRate(event.getEventId());
        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId());
        FeePerTicket newFeePerTicket = new FeePerTicket(newTicketType,newTicketType.isPayLater(), stripe, salesTaxFeeDto, newTicketType.getPrice(), 1, 1, false,capAmountForNewTicketType, vatTaxRate,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();
        double totalPaidAmount = eventTickets.getPaidAmount() - eventTickets.getRefundedAmount();
        double newTicketPaidAmount = newFeePerTicket.getTotalPayable();
        if (totalPaidAmount == newTicketPaidAmount) {
            ticketTransferTypesDto.setSamePriceTicketTransfer(true);
        } else if (totalPaidAmount < newTicketPaidAmount){
            ticketTransferTypesDto.setLowerToHigherTicketTransfer(true);
        } else if (totalPaidAmount > newTicketPaidAmount) {
            ticketTransferTypesDto.setHigherToLowerTicketTransfer(true);
        }
    }


    public void getEmailLimitByEventOrThrowEmailLimitErrorIfCountIsZero(Event event, Optional<ContactModuleSettings> contactModuleSettings) {
        int maxContactsEmailsAllowed;
        if (chargeBeeHelperService.validateEvenFreePlan(event) && null==event.getWhiteLabel()) {
            maxContactsEmailsAllowed = contactModuleSettings.map(ContactModuleSettings::getEmailLimit).orElse(0);
        } else {
            maxContactsEmailsAllowed = contactModuleSettings.map(ContactModuleSettings::getEmailLimit).orElse(6);
        }
        if (maxContactsEmailsAllowed == 0) {
            NotAcceptableException.ContactExceptionMsg exceptionMsg = NotAcceptableException.ContactExceptionMsg.
                    FREE_PLAN_TEST_EMAIL_LIMITATION;
            throw new NotAcceptableException(exceptionMsg);
        }
    }

    @Override
    public void updateAllowAttendeeToCheckInWithUnPaidTicket(Event event, boolean allowAttendeeToCheckInWithUnPaidTicket) {
        Ticketing ticketing = ticketingService.findByEvent(event);
        ticketing.setAllowCheckInWithUnpaidTicket(allowAttendeeToCheckInWithUnPaidTicket);
        ticketingService.save(ticketing);
        log.info("Updated allowCheckInWithUnPaidTicket for event {} to {}", event.getEventId(), allowAttendeeToCheckInWithUnPaidTicket);
    }

    @Override
    public boolean findIsLimitEventCapacityByEventId(Event event) {
        return ticketingRepository.findIsLimitEventCapacityByEventId(event);
    }
    @Override
    public Double findLimitEventCapacityByEventId(Event event) {
        return ticketingRepository.findLimitEventCapacityByEventId(event);
    }

    @Override
    public boolean isApprovalRequestEmailsEnabled(Event event) {
        return ticketingRepository.isApprovalRequestEmailsEnabled(event);
    }

    @Override
    public String findApprovalEmailReceiverIdsByEvent(Event event) {
        return ticketingRepository.findApprovalEmailReceiverIdsByEvent(event);
    }

    @Override
    public RegistrationApprovalSettingsDto getRegistrationApprovalSettingByEvent(Event event) {
        return  ticketingRepository.getRegistrationApprovalSettingByEvent(event);
    }

    @Override
    public List<NameValueDto> getAllDynamicMergeTagsList(Event event, Long recurringEventId) {
        List<NameValueDto> buyerMergeTags = new ArrayList<>();
        List<AttributeValueType> attributeValueTypeList = List.of(AttributeValueType.CONDITIONAL_QUE, AttributeValueType.TEXT_BLOCK);
        List<TicketHolderRequiredAttributes> fields = this.getTicketingAttributes(event, recurringEventId);
        boolean collectTicketHolderAttributesEnable = ticketingService.isCollectTicketHolderAttributesEnable(event.getEventId());
        fields.sort(Comparator.comparingInt(TicketHolderRequiredAttributes::getBuyerAttributeOrder));
        for (TicketHolderRequiredAttributes field : fields) {
            if(field.getEnabledForTicketPurchaser() && !attributeValueTypeList.contains(field.getAttributeValueType())) {
                addAttributeToMergeTags(buyerMergeTags, field, AttendeeTypeEnum.BUYER);
            }
            if (collectTicketHolderAttributesEnable && field.getEnabledForTicketHolder() && !attributeValueTypeList.contains(field.getAttributeValueType())) {
                addAttributeToMergeTags(buyerMergeTags, field, AttendeeTypeEnum.HOLDER);
            }
        }
        return buyerMergeTags;
    }

    private void addAttributeToMergeTags(List<NameValueDto> buyerMergeTags, TicketHolderRequiredAttributes field, AttendeeTypeEnum attendeeType) {
        NameValueDto attributeKeyValueDto=new NameValueDto();
        attributeKeyValueDto.setName(field.getName());
        attributeKeyValueDto.setValue(DOLLAR.concat(OPEN_CURLY_BRACE).concat(attendeeType.getValue()).concat(STRING_UNDERSCORE).concat(Long.toString(field.getId())).concat(CLOSE_CURLY_BRACE));
        buyerMergeTags.add(attributeKeyValueDto);
    }

    @Override
    public List<TicketingDatesDto> findEventStartAndEndDateByEventsIds(List<Long> eventsIds) {
        return ticketingRepository.findEventStartAndEndDateByEventsIds(eventsIds);
    }

    @Override
    public List<StripeCreditCardDto> getCreditCardDetailByEventTicketId(Event event, User staffUser, Long orderId) {

        TicketingOrder ticketOrder = getValidTicketOrder(orderId, event);
        StripeDTO paymentGatewayDTO = stripeService.getStripeFeesByEvent(event);
        log.info("Get used credit card details for eventId {} orderId {} requested by userId {} ", event.getEventId(), orderId, staffUser.getUserId());
        return stripeCustomerService.getCreditCardDetailsByEventAndOrderIdAndPurchaserUser(event.getEventId(), orderId, ticketOrder.getPurchaser().getUserId(), paymentGatewayDTO.getPaymentGateway());
    }

    private TicketingOrder getValidTicketOrder(long orderId, Event event) {
        TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderId, event);
        if (TicketingOrder.TicketingOrderStatus.PAID_DELETE.equals(ticketingOrder.getStatus()) || TicketingOrder.TicketingOrderStatus.UNPAID_DELETE.equals(ticketingOrder.getStatus())  ) {
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND);
        }
        if(TicketingOrder.TicketingOrderStatus.PROCESSING.equals(ticketingOrder.getStatus())){
            throw new NotAcceptableException(NotAcceptableException.PaymentCreationExceptionMsg.PREVIOUS_PAYMENT_STILL_IN_PROCESSING);
        }
        return ticketingOrder;
    }

    @Override
    public void eventTicketNonTransferable(Event event, User user, Long ticketId, boolean nonTransferable) {
        Optional<EventTickets> eventTicketOpt = eventTicketsService.findEventTicketById(ticketId);
        if (eventTicketOpt.isPresent()) {
            EventTickets eventTicket = eventTicketOpt.get();
            if (nonTransferable != eventTicket.isNonTransferable()) {
                eventTicket.setNonTransferable(nonTransferable);
                eventTicketsService.save(eventTicket);
            }
        } else {
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.TICKET_NOT_FOUND);
        }
    }

    @Override
    public List<OrderExchangeTicketTypesDto> getTicketTypesForOrderExchange(Event event){
        List<TicketTypeNameQtyDto> ticketingTypes = ticketingTypeService.findFreeTicketTypeIdAndNumberOfTickets(event);

        List<Long> ticketingTypeIds = ticketingTypes.stream()
                .map(TicketTypeNameQtyDto::getTicketTypeId)
                .collect(Collectors.toList());

        Map<Long, BigDecimal> ticketSoldCount = eventCommonRepoService.findSoldCountGroupByTypeAndTicketTypeId(event.getEventId(), event.getTicketingId(), ticketingTypeIds);

        List<OrderExchangeTicketTypesDto> orderExchangeTicketTypesDtoList = new ArrayList<>();
        for (TicketTypeNameQtyDto ticketType : ticketingTypes) {
            long ticketTypeId = ticketType.getTicketTypeId();
            long numberOfTickets = ticketType.getTotalTickets();

            long soldTickets = ticketSoldCount.getOrDefault(ticketTypeId, BigDecimal.ZERO).longValue();
            long remainingTickets = numberOfTickets - soldTickets;
            orderExchangeTicketTypesDtoList.add(new OrderExchangeTicketTypesDto(ticketTypeId, ticketType.getTicketTypeName(), remainingTickets));

        }

        return orderExchangeTicketTypesDtoList;
    }

    @Override
    public List<AttributesDefaultValuesDto> getAttributesWithDefaultValues(Event event) {
        log.info("getAttributesWithDefaultValues | evnet id {}", event.getEventId());
        return ticketHolderRequiredAttributesService.getAttributesDefaultValuesByEvent(event);

    }

    @Override
    public void checkEventPostEnangeEmailAccessPermitIsPassed(Event event , User loggedInUser) {
        // if user with super admin access then we will not check event post date
        if (!roStaffService.hasSuperAdminAccess(loggedInUser)) {
            Ticketing ticketing = ticketingRepository.findByEventid(event);
            if (!ObjectUtils.isEmpty(ticketing) && !ObjectUtils.isEmpty(ticketing.getEventEndDate())) {
                // event end date + engageEmailDaysPermitsAccess
               Date calculatedDate = DateUtils.getAddedDays(ticketing.getEventEndDate(), Math.toIntExact(ticketing.getEngageEmailDaysPermitsAccess()));
                // check if current date is after event end date + engageEmailDaysPermitsAccess
                if (new Date().after(calculatedDate)) {
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.YOU_CANT_PERFORM_ACTION_AFTER_EVENT_POST_ACCESS_EXCEED);
                }
            }
        }
    }


}