package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.CAPUsageDto;
import com.accelevents.billing.chargebee.dto.PlatformConfigDto;
import com.accelevents.billing.chargebee.dto.RegistrationConfigDto;
import com.accelevents.billing.chargebee.repo.*;
import com.accelevents.billing.chargebee.repositories.ChargebeeEventCreditsService;
import com.accelevents.billing.chargebee.repositories.ChargebeeTransactionRepository;
import com.accelevents.billing.chargebee.service.ChargeBeeHelperService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.OrganizerDeletionAccessDto;
import com.accelevents.common.dto.StaffDetail;
import com.accelevents.common.dto.StaffDetailDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.enums.OrganizerRole;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.hubspot.service.HubspotEventService;
import com.accelevents.hubspot.service.HubspotOrganizerService;
import com.accelevents.messages.EnumAuctionSMS;
import com.accelevents.messages.EnumCauseSMS;
import com.accelevents.messages.EnumRaffleSMS;
import com.accelevents.messages.EnumTicketingSMS;
import com.accelevents.repositories.*;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.services.MUXLivestreamAssetRepoService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.JsonMapper;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.exceptions.NotFoundException.SessionNotFound.EVENT_PLAN_CONFIG_NOT_FOUND;
import static com.accelevents.utils.FeeConstants.*;
import static java.util.concurrent.TimeUnit.DAYS;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class SuperAdminServiceImpl implements SuperAdminService {

    private static final Logger log = LoggerFactory.getLogger(SuperAdminServiceImpl.class);

    @Autowired
    private EventDesignDetailService eventDesignDetailService;

    @Autowired
    private AuctionService auctionService;

    @Autowired
    private CauseAuctionService causeAuctionService;

    @Autowired
    private RaffleService raffleService;

    @Autowired
    private TicketingHelperService ticketingHelperService;

    @Autowired
    private ContactModuleSettingsService contactModuleSettingsService;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private EventService eventService;
    @Autowired
    private ROEventService roEventService;

    @Autowired
    private CauseAuctionCustomSmsService causeAuctionCustomSmsService;

    @Autowired
    private  RaffleCustomSmsService raffleCustomSmsService;

    @Autowired
    private AuctionCustomSmsService auctionCustomSmsService;

    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;

    @Autowired
    private StripeService stripeService;
    @Autowired
    private ROStripeService roStripeService;

    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private OrganizerService organizerService;
    @Autowired
    private TransactionFeeConditionalLogicRepository transactionFeeConditionalLogicRepository;
    @Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Autowired
    private SessionRepoService sessionRepoService;
    @Autowired
    private MuxService muxService;
    @Autowired
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;
    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private ROVirtualEventService roVirtualEventService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private HubspotEventService hubspotEventService;
    @Autowired
    private ApiKeyService apiKeyService;
    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private MessageToContactsRepository messageToContactsRepository;
    @Autowired
    private StaffService staffService;
    @Autowired
    private TicketingRepository ticketingRepository;
    @Autowired
    private ChargebeeEventCreditsRepoService chargebeeEventCreditsRepoService;
    @Autowired
    private ChargebeeEventCreditsService chargebeeEventCreditsService;
    @Autowired
    private HubspotOrganizerService hubspotOrganizerService;
    @Autowired
    private OrganizerRepository organizerRepository;
    @Autowired
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
    @Autowired
    private OrganizerRepoService organizerRepoService;
    @Autowired
    private EventLiveStreamingConfigurationService eventLiveStreamingConfigurationService;
    @Autowired
    private ChargeBeeHelperService chargeBeeHelperService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ChargebeeEventUsagesRepoService chargebeeEventUsagesRepoService;
    @Autowired
    private ChargebeeTransactionRepository chargebeeTransactionRepository;
    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Autowired
    private EventChargeUsagesRepoService eventChargeUsagesRepoService;
    @Autowired
    private IntegrationRepository integrationRepository;
    @Autowired
    private TicketingTypeTicketService ticketingTypeTicketService;

    public void updateEventPlanConfigDataBySuperAdmin(Event event, SuperAdminSetting superAdminSetting, EventPlanConfig eventPlanConfig) {
        Ticketing ticketing = ticketingService.findByEvent(event);
        PlatformConfigDto platformConfigDto = PlatformConfigDto.convertJSONToObject(eventPlanConfig.getPlatformConfigJson());
        if (superAdminSetting.getMaxPreEventAccessDays() != Long.parseLong(platformConfigDto.getPreEventAccess()) ||
                superAdminSetting.getMaxPostEventAccessDays() != Long.parseLong(platformConfigDto.getPostEventAccessDay())) {
            platformConfigDto.setPreEventAccess(superAdminSetting.getMaxPreEventAccessDays().toString());
            platformConfigDto.setPostEventAccessDay(superAdminSetting.getMaxPostEventAccessDays().toString());
            String platformDtoString = JsonMapper.parseToJsonString(platformConfigDto);
            eventPlanConfig.setPlatformConfigJson(platformDtoString);
            if (ticketing.getPreEventAccessMinutes() > (int) DAYS.toMinutes(Long.parseLong(superAdminSetting.getMaxPreEventAccessDays().toString()))) {
                ticketing.setPreEventAccessMinutes((int) DAYS.toMinutes(Long.parseLong(superAdminSetting.getMaxPreEventAccessDays().toString())));
            }
            ticketing.setPostEventAccessMinutes((int) DAYS.toMinutes(Long.parseLong(superAdminSetting.getMaxPostEventAccessDays().toString())));

        }
        ticketing.setEngageEmailDaysPermitsAccess(superAdminSetting.getEngageEmailPermitDaysAfterEventEnd());
        ticketingRepository.save(ticketing);
    }

    @Override
    public SuperAdminSetting getEventSettingForSuperAdmin(Event event, User superAdminUser) {
        EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        EventPlanConfig planConfig = eventPlanConfigService.findByEventId(event.getEventId());
        CAPUsageDto capUsageDto = CAPUsageDto.convertJSONToObject(planConfig.getCapUsageConfigJson());
        RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(planConfig.getRegistrationConfigJson());
        PlatformConfigDto platformConfigDto = PlatformConfigDto.convertJSONToObject(planConfig.getPlatformConfigJson());
        Auction auction = auctionService.findByEvent(event);
        CauseAuction cause = causeAuctionService.findByEvent(event);
        Raffle raffle = raffleService.findByEvent(event);
        SuperAdminSetting settings = new SuperAdminSetting();
        Optional<ContactModuleSettings> contactModuleSettings = contactModuleSettingsService.findContactModuleSettingsByEvent(event);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);

        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        EventLiveStreamingConfiguration eventLiveStreamingConfiguration = eventLiveStreamingConfigurationService.findByEventId(event.getEventId());

        settings.setActiveAuction(auction != null ? auction.isActivated() : Boolean.FALSE);
        settings.setEnableCardRegistration(event.getEnableBidderRegistration());
        settings.setEnableCustomMessage(event.getUsesCustomMessaging());
        settings.setEnableAuctionWinnerNotification(auction == null || auction.isWinnerNotificationEnable());
        settings.setActiveFundANeed(cause != null ? cause.isActivated() : Boolean.FALSE);
        settings.setEnableRaffleWinnerNotification(raffle != null && raffle.isWinnerNotificationEnable());
        settings.setActiveRaffle(raffle != null ? raffle.isActivated() : Boolean.FALSE);
        settings.setActiveSessionAgenda(eventDesignDetail.isEnableSessionsSpeakers());
        settings.setEnableAutoAssignedSequence(eventDesignDetail.isEnableAutoAssignedSequence());
        if (null != event.getWhiteLabel() || !chargeBeeHelperService.validateEvenFreePlan(event)) {
            settings.setNumberOfContactsAllowed(contactModuleSettings.map(ContactModuleSettings::getContactLimit).orElse(5));
            settings.setNumberOfEmailsAllowed(contactModuleSettings.map(ContactModuleSettings::getEmailLimit).orElse(6));
        } else {
            settings.setNumberOfContactsAllowed(contactModuleSettings.map(ContactModuleSettings::getContactLimit).orElse(0));
            settings.setNumberOfEmailsAllowed(contactModuleSettings.map(ContactModuleSettings::getEmailLimit).orElse(0));
        }
        setEngageEmailLimit(settings,event,contactModuleSettings);
        settings.setTicketingCheckoutTimer(ticketing.getCheckoutminutes());
        settings.setViewScrollSpeed(eventDesignDetail.getViewScrollSpeed());
        settings.setEventFormat(event.getEventFormat().getFormat());
        if (capUsageDto.getMaxAgendaItems() == -1){
            settings.setMaxAgendaItems(1000);
        }else {
            settings.setMaxAgendaItems(Math.toIntExact(capUsageDto.getMaxAgendaItems()));
        }
        if (virtualEventSettings != null) {
            settings.setEnableWorkshop(virtualEventSettings.isWorkshopEnabled());
        }
        if(null != eventLiveStreamingConfiguration){
            settings.setLowLatency(eventLiveStreamingConfiguration.isLowLatency());
            settings.setBroadcastResolution(StringUtils.isNotBlank(eventLiveStreamingConfiguration.getBroadcastResolution()) ? Resolution.get(eventLiveStreamingConfiguration.getBroadcastResolution()).getResolution() : Resolution.HD.getResolution());
        }else{
            settings.setLowLatency(false);
            settings.setBroadcastResolution(Resolution.HD.getResolution());
        }
        settings.setActiveTicketingModule(ticketing.getActivated());
        settings.setAutoDisableUnAuthLiveStream(eventPlanConfigRepoService.findByAutoLiveStreamDisableEventId(event.getEventId()));
        settings.setBillingType(event.getBillingType());
        settings.setAutoBilling(event.isAutoBilling());
        settings.setRegistrantUsagesBilling(planConfig.isRegistrantsUsagesBilling());
        settings.setHideEventDate(event.isHideEventDate());
        settings.setUploadBillingPending(event.isUploadBillingPending());
        settings.setMaxPreEventAccessDays(Long.valueOf(platformConfigDto.getPreEventAccess()));
        settings.setMaxPostEventAccessDays(Long.valueOf(platformConfigDto.getPostEventAccessDay()));
        if (registrationConfigDto.getMaxEventDays() == -1){
            settings.setEventDays(1000L);
        }
        else {
            settings.setEventDays(registrationConfigDto.getMaxEventDays());
        }
        settings.setAttendeeImportLimit(planConfig.getAttendeeImportLimit());
        AttendeesUploadCharge attendeesUploadCharge = eventService.getAttendeesUploadCharge(event.getEventId());
        settings.setAttendeeUploadFeeEnabled(AttendeesUploadCharge.CHARGE.equals(attendeesUploadCharge));
        settings.setEventCapacity(null != ticketing.getEventCapacity() ? ticketing.getEventCapacity() : 100d);
        settings.setLimitEventCapacity(ticketing.isLimitEventCapacity());
        settings.setEngageEmailPermitDaysAfterEventEnd(ticketing.getEngageEmailDaysPermitsAccess());
        BigDecimal totalSoldTicketsInEvent = eventCommonRepoService.findSoldCountByEvent(event);
        Long numberOfTotalTicketsInEvent = ticketingTypeTicketService.getNumberOfTotalTicketsInEvent(event.getEventId(), DataType.TICKET);
        settings.setTotalSoldTicketsInEvent(totalSoldTicketsInEvent);
        settings.setNumberOfTotalTicketsInEvent(numberOfTotalTicketsInEvent);
        BigDecimal totalSoldAddonsInEvent = eventCommonRepoService.findAddonSoldCountByEvent(event);
        Long numberOfTotalAddonInEvent = ticketingTypeTicketService.getNumberOfTotalTicketsInEvent(event.getEventId(), DataType.ADDON);
        settings.setTotalSoldAddonsInEvent(totalSoldAddonsInEvent);
        settings.setNumberOfTotalAddonsInEvent(numberOfTotalAddonInEvent);
        log.info("getEventSettingForSuperAdmin Retrieved event settings for super-admin | EventId | {} | EventDesignDetails Id | {}",
                event.getEventId(), eventDesignDetail.getId());
        return settings;
    }

    @Override
    @Transactional
    public SuperAdminSetting updateEventSettingForSuperAdmin(User user, SuperAdminSetting superAdminSetting, Event event) {
        log.info("updateEventSettingForSuperAdmin eventId {} userId {} SuperAdminSetting {}",event.getEventId(),user.getUserId(),superAdminSetting);
        validate(superAdminSetting,event);
        EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        Auction auction = auctionService.findByEvent(event);
        CauseAuction cause = causeAuctionService.findByEvent(event);
        Raffle raffle = raffleService.findByEvent(event);

        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);

        setSuperAdminSettingForAuction(superAdminSetting, auction,event);

        if(null != raffle) {
            raffle.setActivated(superAdminSetting.isActiveRaffle());
            raffle.setWinnerNotificationEnable(superAdminSetting.isEnableRaffleWinnerNotification());
            raffleService.save(raffle);
        }
        if(null != cause) {
            cause.setActivated(superAdminSetting.isActiveFundANeed());
            causeAuctionService.save(cause);
        }

        if(superAdminSetting.isActiveSessionAgenda() && !eventDesignDetail.isEnableSessionsSpeakers() && event.getEventFormat().equals(EventFormat.IN_PERSON)){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CHANGE_EVENT_FORMAT_FROM_IN_PERSON_TO_USE_SESSION_AGENDA);
        }

        setSuperAdminSettingOfEventDesignDetails(superAdminSetting, eventDesignDetail);

        setSuperAdminSettingOfContactModuleSetting(superAdminSetting, event , user);

        setSuperAdminOfTicketing(superAdminSetting, ticketing, event, user);


        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
        CAPUsageDto capUsageDto = CAPUsageDto.convertJSONToObject(eventPlanConfig.getCapUsageConfigJson());
        RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(eventPlanConfig.getRegistrationConfigJson());
        if (superAdminSetting.getMaxAgendaItems() != 1000) {
            capUsageDto.setMaxAgendaItems(Long.valueOf(superAdminSetting.getMaxAgendaItems()));
            String capUsageDtoString = JsonMapper.parseToJsonString(capUsageDto);
            eventPlanConfig.setCapUsageConfigJson(capUsageDtoString);
        }
        if (superAdminSetting.getAttendeeImportLimit() >= 0 && !superAdminSetting.getAttendeeImportLimit().equals(eventPlanConfig.getAttendeeImportLimit())) {
            if (superAdminSetting.getAttendeeImportLimit() > 0) {
                long eventSoldTicketsByCSV = eventCommonRepoService.findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(event.getEventId()).longValue();
                if (superAdminSetting.getAttendeeImportLimit() > eventSoldTicketsByCSV) {
                    eventPlanConfig.setAttendeeImportLimit(superAdminSetting.getAttendeeImportLimit());
                } else {
                    NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.ATTENDEE_IMPORT_LIMIT_LESS_THAN_CURRENT;
                    String constantMessage = Constants.ATTENDEE_IMPORT_LIMIT_LESS_THAN_CURRENT.replace("${importedAttendee}", String.valueOf(eventSoldTicketsByCSV));
                    exception.setErrorMessage(constantMessage);
                    exception.setDeveloperMessage(constantMessage);
                    throw new NotAcceptableException(exception);
                }
            }
            eventPlanConfig.setAttendeeImportLimit(superAdminSetting.getAttendeeImportLimit());
        }
        updateEventDays(superAdminSetting, user, eventPlanConfig, registrationConfigDto);
        updateEventPlanConfigDataBySuperAdmin(event, superAdminSetting, eventPlanConfig);
        updateRegistrantsUsagesBilling(superAdminSetting, eventPlanConfig);
        updateVirtualSettings(superAdminSetting, event);

        eventLiveStreamingConfigurationService.updateEventStreamingConfiguration(superAdminSetting,event);


        event.setEnableBidderRegistration(superAdminSetting.isEnableCardRegistration());
        event.setAttendeesUploadCharge(superAdminSetting.isAttendeeUploadFeeEnabled() ?
                AttendeesUploadCharge.CHARGE : AttendeesUploadCharge.WAIVE_OFF);
        eventService.enableBidderCreditCardRegistration(event, superAdminSetting.isEnableCardRegistration());
        event.setUsesCustomMessaging(superAdminSetting.isEnableCustomMessage());
        event.setUploadBillingPending(superAdminSetting.isUploadBillingPending());
        boolean billingTypeUpdated = !event.getBillingType().equals(superAdminSetting.getBillingType());

        if(billingTypeUpdated){
            userService.checkIsBillingTypeAdminOrThrowError(user);
            event.setBillingType(superAdminSetting.getBillingType());
            log.info("Billing Type for event {} Changed to {} by {}",event.getEventId(),superAdminSetting.getBillingType(),user);
            transactionFeeConditionalLogicService.handleRecord(event, event.getEventFormat());
        }
        if(event.isAutoBilling() != superAdminSetting.isAutoBilling() && null != ticketing && null != cause && null != raffle && null != auction ) {
            List<Date> dateList=new ArrayList<>();
            dateList.add(ticketing.getEventEndDate());
            dateList.add(auction.getEndDate());
            dateList.add(raffle.getEndDate());
            dateList.add(cause.getEndDate());
           Optional<Date> maxDate= dateList.stream().max(Comparator.comparing(Date::toInstant));
           if(maxDate.isPresent()){
            if(!maxDate.get().before(new Date())){
            int autoBilling=Boolean.compare(event.isAutoBilling(),superAdminSetting.isAutoBilling());
            if (autoBilling != 0){
                event.setAutoBilling(superAdminSetting.isAutoBilling());
                log.info("Auto Billing Flag for event {} changed to {} by {}",event.getEventId(),superAdminSetting.isAutoBilling(),user);
            }
            }else{
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.EVENT_ALREADY_ENDED);
            }
           }
        }
        if(event.isHideEventDate()!=superAdminSetting.isHideEventDate()) {
            event.setHideEventDate(superAdminSetting.isHideEventDate());
        }
        event.setUsesCustomMessaging(superAdminSetting.isEnableCustomMessage());
        eventRepoService.save(event);
        log.info("Update event settings for super-admin");
        return superAdminSetting;
    }

    private void updateRegistrantsUsagesBilling(SuperAdminSetting superAdminSetting, EventPlanConfig eventPlanConfig) {
        eventPlanConfig.setRegistrantsUsagesBilling(superAdminSetting.isRegistrantUsagesBilling());
        eventPlanConfigService.save(eventPlanConfig);
    }

    private void updateEventDays(SuperAdminSetting superAdminSetting, User user, EventPlanConfig eventPlanConfig, RegistrationConfigDto registrationConfigDto) {
        if (staffService.hasSuperAdminAndSalesAdminAccess(user) && superAdminSetting.getEventDays() != null && !registrationConfigDto.getMaxEventDays().equals(superAdminSetting.getEventDays())) {
            registrationConfigDto.setMaxEventDays(superAdminSetting.getEventDays());
            String registrationDtoString = JsonMapper.parseToJsonString(registrationConfigDto);
            eventPlanConfig.setRegistrationConfigJson(registrationDtoString);
        }
    }

    private void updateVirtualSettings(SuperAdminSetting superAdminSetting, Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        if(null != virtualEventSettings){
                virtualEventSettings.setWorkshopEnabled(superAdminSetting.isEnableWorkshop());
                virtualEventSettings.setMaxAgendaItems(superAdminSetting.getMaxAgendaItems());
                virtualEventSettingsRepoService.save(virtualEventSettings);
        }
    }

    private void setSuperAdminOfTicketing(SuperAdminSetting superAdminSetting, Ticketing ticketing, Event event, User user) {
        if(null != ticketing){
            ticketing.setCheckoutminutes(superAdminSetting.getTicketingCheckoutTimer());
            ticketing.setActivated(superAdminSetting.getActiveTicketingModule());
            if (isValidEventCapacityFromSuperAdmin(superAdminSetting, event)) {
                ticketing.setEventCapacity(superAdminSetting.getEventCapacity());
                ticketing.setLimitEventCapacity(superAdminSetting.isLimitEventCapacity());
            }
            ticketingService.save(ticketing);
            eventService.createDefaultCustomTemplates(event, user);
        }
    }

    private boolean isValidEventCapacityFromSuperAdmin(SuperAdminSetting superAdminSetting, Event event){
        BigDecimal totalSoldTicketsInEvent = eventCommonRepoService.findSoldCountByEvent(event);
        Long numberOfTotalTicketsInEvent = ticketingTypeTicketService.getNumberOfTotalTicketsInEvent(event.getEventId(), DataType.TICKET);
        log.info("check isValidEventCapacityFromSuperAdmin eventId {} | event capacity {} | isLimitEventCapacity {} | totalSoldTicketsInEvent {} | numberOfTotalTicketsInEvent {}",event.getEventId(), superAdminSetting.getEventCapacity(), superAdminSetting.isLimitEventCapacity(), totalSoldTicketsInEvent, numberOfTotalTicketsInEvent);
        boolean isTotalTickets = numberOfTotalTicketsInEvent != null && numberOfTotalTicketsInEvent >= superAdminSetting.getEventCapacity();
        return superAdminSetting.getEventCapacity() != null && (totalSoldTicketsInEvent.doubleValue() <= superAdminSetting.getEventCapacity()) && superAdminSetting.getEventCapacity() > 0 && isTotalTickets;
    }

    private void setSuperAdminSettingOfContactModuleSetting(SuperAdminSetting superAdminSetting, Event event ,User user) {
        Optional<ContactModuleSettings> contactModuleSettings = contactModuleSettingsService.findContactModuleSettingsByEvent(event);
        ContactModuleSettings contactModuleSetting;
        if(contactModuleSettings.isPresent()){
            contactModuleSetting = contactModuleSettings.get();
            log.info("setSuperAdminSettingOfContactModuleSetting eventId {} userId {} contactModuleSetting {}",event.getEventId(),user.getUserId(),contactModuleSetting);
        }else{
            contactModuleSetting = new ContactModuleSettings();
            contactModuleSetting.setEvent(event);
        }
        contactModuleSetting.setContactLimit(superAdminSetting.getNumberOfContactsAllowed());
        contactModuleSetting.setEmailLimit(superAdminSetting.getNumberOfEmailsAllowed());
        if (superAdminSetting.getEngageEmailsLimit() != null) {
            contactModuleSetting.setEngageEmailsLimit(superAdminSetting.getEngageEmailsLimit());
        }
        contactModuleSettingsService.save(contactModuleSetting);
    }

    private void setSuperAdminSettingOfEventDesignDetails(SuperAdminSetting superAdminSetting, EventDesignDetail eventDesignDetail) {
        if(null != eventDesignDetail) {
            eventDesignDetail.setEnableSessionsSpeakers(superAdminSetting.isActiveSessionAgenda());
            eventDesignDetail.setEnableAutoAssignedSequence(superAdminSetting.isEnableAutoAssignedSequence());
            eventDesignDetail.setViewScrollSpeed(superAdminSetting.getViewScrollSpeed());
            eventDesignDetailService.save(eventDesignDetail);
        }
    }

    private void setSuperAdminSettingForAuction(SuperAdminSetting superAdminSetting, Auction auction,Event event) {
        if(null != auction) {
            if(auction.isActivated()!= superAdminSetting.isActiveAuction()) {
                auction.setActivated(superAdminSetting.isActiveAuction());
                auction.setExtendedBidding(false);
                auction.setExtendedBiddingWaitTime(0);
                auction.setLastExtendedEndTime(null);
            }
            auction.setWinnerNotificationEnable(superAdminSetting.isEnableAuctionWinnerNotification());
            auctionService.saveAndCacheClear(auction,event);
        }
    }

    @Override
    public SuperAdminCustomSmsDto getCustomMessageSetting(Event event){
        SuperAdminCustomSmsDto superAdminCustomSmsDto = new SuperAdminCustomSmsDto();
        RaffleCustomSms raffleCustomSms = raffleCustomSmsService.findByEventId(event.getEventId());
        CauseAuctionCustomSms causeAuctionCustomSms = causeAuctionCustomSmsService.findByEventId(event.getEventId());
        AuctionCustomSms auctionCustomSms = auctionCustomSmsService.findByEventId(event);

        superAdminCustomSmsDto.setRaffleOnlinePurchaseNote(EnumRaffleSMS.RAFFLE_ONLINE_PURCHASE_NOTE.getValue());
        if (null != raffleCustomSms) {
            superAdminCustomSmsDto.setRaffleWinnerNotificationForSingleItem(raffleCustomSms.getRAFFLE_CONGRATULATIONS_SINGLE_ITEM());
            superAdminCustomSmsDto.setRaffleWinnerNotificationForMultipleItem(raffleCustomSms.getRAFFLE_CONGRATULATIONS_MULTIPLE_ITEM());
            superAdminCustomSmsDto.setRaffleOnlinePurchase(raffleCustomSms.getRAFFLE_ONLINE_PURCHASE());
            superAdminCustomSmsDto.setRaffleOnlinePurchaseTickets(raffleCustomSms.getRAFFLE_ONLINE_PURCHASE_TICKETS());
        } else {
            superAdminCustomSmsDto.setRaffleWinnerNotificationForSingleItem(EnumRaffleSMS.RAFFLE_CONGRATULATIONS_SINGLE_ITEM.getValue());
            superAdminCustomSmsDto.setRaffleWinnerNotificationForMultipleItem(EnumRaffleSMS.RAFFLE_CONGRATULATIONS_MULTIPLE_ITEM.getValue());
            superAdminCustomSmsDto.setRaffleOnlinePurchase(EnumRaffleSMS.RAFFLE_ONLINE_PURCHASE.getValue());
            superAdminCustomSmsDto.setRaffleOnlinePurchaseTickets(EnumRaffleSMS.RAFFLE_ONLINE_PURCHASE_TICKETS.getValue());
        }
        superAdminCustomSmsDto.setPledgeCheckoutPaymentEnabled(null != causeAuctionCustomSms ? causeAuctionCustomSms.getPLEDGE_CHECKOUT_PAYMENT_ENABLED() : EnumCauseSMS.PLEDGE_CHECKOUT_PAYMENT_ENABLED.getValue());
        if (null != auctionCustomSms) {
            superAdminCustomSmsDto.setAttendeeCheckIn(auctionCustomSms.getATTENDEE_CHECK_IN());
            superAdminCustomSmsDto.setWinnerBidNotificationForSingleItemPaymentEnable(auctionCustomSms.getWINNING_BID_SINGLE_ITEM_PAYMENT_ENABLED());
            superAdminCustomSmsDto.setWinnerBidNotificationForSingleItemPaymentDisable(auctionCustomSms.getWINNING_BID_SINGLE_ITEM_PAYMENT_DISABLED());
            superAdminCustomSmsDto.setWinnerBidNotificationForMultipleItemPaymentEnable(auctionCustomSms.getWINNING_BID_MULTIPLE_ITEMS_PAYMENT_ENABLED());
            superAdminCustomSmsDto.setWinnerBidNotificationForMultipleItemPaymentDisable(auctionCustomSms.getWINNING_BID_MULTIPLE_ITEMS_PAYMENT_DISABLED());
            superAdminCustomSmsDto.setExtendedBiddingOutbidNotification(auctionCustomSms.getEXTENDED_BIDDING_OUTBID_NOTIFICATION());
        } else {
            superAdminCustomSmsDto.setAttendeeCheckIn(EnumTicketingSMS.ATTENDEE_CHECK_IN.getValue());
            superAdminCustomSmsDto.setWinnerBidNotificationForSingleItemPaymentEnable(EnumAuctionSMS.WINNING_BID_SINGLE_ITEM_PAYMENT_ENABLED.getValue());
            superAdminCustomSmsDto.setWinnerBidNotificationForSingleItemPaymentDisable(EnumAuctionSMS.WINNING_BID_SINGLE_ITEM_PAYMENT_DISABLED.getValue());
            superAdminCustomSmsDto.setWinnerBidNotificationForMultipleItemPaymentEnable(EnumAuctionSMS.WINNING_BID_MULTIPLE_ITEMS_PAYMENT_ENABLED.getValue());
            superAdminCustomSmsDto.setWinnerBidNotificationForMultipleItemPaymentDisable(EnumAuctionSMS.WINNING_BID_MULTIPLE_ITEMS_PAYMENT_DISABLED.getValue());
            superAdminCustomSmsDto.setExtendedBiddingOutbidNotification(EnumAuctionSMS.EXTENDED_BIDDING_OUTBID_NOTIFICATION.getValue());
        }


        return superAdminCustomSmsDto;
    }

    @Override
    @Transactional
    public SuperAdminCustomSmsDto updateCustomMessageSetting(SuperAdminCustomSmsDto superAdminCustomSmsDto, Event event) {
        RaffleCustomSms raffleCustomSms = raffleCustomSmsService.findByEventId(event.getEventId());
        CauseAuctionCustomSms causeAuctionCustomSms = causeAuctionCustomSmsService.findByEventId(event.getEventId());
        AuctionCustomSms auctionCustomSms = auctionCustomSmsService.findByEventId(event);

        if(null != raffleCustomSms){
            raffleCustomSms.setRAFFLE_CONGRATULATIONS_SINGLE_ITEM(superAdminCustomSmsDto.getRaffleWinnerNotificationForSingleItem());
            raffleCustomSms.setRAFFLE_CONGRATULATIONS_MULTIPLE_ITEM(superAdminCustomSmsDto.getRaffleWinnerNotificationForMultipleItem());
            raffleCustomSms.setRAFFLE_ONLINE_PURCHASE(superAdminCustomSmsDto.getRaffleOnlinePurchase());
            raffleCustomSms.setRAFFLE_ONLINE_PURCHASE_TICKETS(superAdminCustomSmsDto.getRaffleOnlinePurchaseTickets());
            raffleCustomSmsService.save(raffleCustomSms);
            raffleCustomSmsService.updateEventFromCache(event.getEventId(),raffleCustomSms);
        }else{
            RaffleCustomSms raffleCustomSmsData = raffleCustomSmsService.createRaffleCustomSmsForDBSave(event.getEventId());
            raffleCustomSmsData.setRAFFLE_CONGRATULATIONS_SINGLE_ITEM(superAdminCustomSmsDto.getRaffleWinnerNotificationForSingleItem());
            raffleCustomSmsData.setRAFFLE_CONGRATULATIONS_MULTIPLE_ITEM(superAdminCustomSmsDto.getRaffleWinnerNotificationForMultipleItem());
            raffleCustomSmsData.setRAFFLE_ONLINE_PURCHASE(superAdminCustomSmsDto.getRaffleOnlinePurchase());
            raffleCustomSmsData.setRAFFLE_ONLINE_PURCHASE_TICKETS(superAdminCustomSmsDto.getRaffleOnlinePurchaseTickets());
            raffleCustomSmsService.save(raffleCustomSmsData);
        }
        if(null != causeAuctionCustomSms){
            causeAuctionCustomSms.setPLEDGE_CHECKOUT_PAYMENT_ENABLED(superAdminCustomSmsDto.getPledgeCheckoutPaymentEnabled());
            causeAuctionCustomSmsService.save(causeAuctionCustomSms);
            causeAuctionCustomSmsService.updateEventFromCache(event.getEventId(),causeAuctionCustomSms);
        }else {
            CauseAuctionCustomSms causeAuctionCustomSmsData = causeAuctionCustomSmsService.createCauseAuctionCustomSmsForDBSave(event.getEventId());
            causeAuctionCustomSmsData.setPLEDGE_CHECKOUT_PAYMENT_ENABLED(superAdminCustomSmsDto.getPledgeCheckoutPaymentEnabled());
            causeAuctionCustomSmsService.save(causeAuctionCustomSmsData);
        }
        if(null != auctionCustomSms){
            auctionCustomSms.setATTENDEE_CHECK_IN(superAdminCustomSmsDto.getAttendeeCheckIn());
            auctionCustomSms.setWINNING_BID_SINGLE_ITEM_PAYMENT_ENABLED(superAdminCustomSmsDto.getWinnerBidNotificationForSingleItemPaymentEnable());
            auctionCustomSms.setWINNING_BID_SINGLE_ITEM_PAYMENT_DISABLED(superAdminCustomSmsDto.getWinnerBidNotificationForSingleItemPaymentDisable());
            auctionCustomSms.setWINNING_BID_MULTIPLE_ITEMS_PAYMENT_ENABLED(superAdminCustomSmsDto.getWinnerBidNotificationForMultipleItemPaymentEnable());
            auctionCustomSms.setWINNING_BID_MULTIPLE_ITEMS_PAYMENT_DISABLED(superAdminCustomSmsDto.getWinnerBidNotificationForMultipleItemPaymentDisable());
            auctionCustomSms.setEXTENDED_BIDDING_OUTBID_NOTIFICATION(superAdminCustomSmsDto.getExtendedBiddingOutbidNotification());
            auctionCustomSmsService.save(auctionCustomSms);
            auctionCustomSmsService.updateEventFromCache(event.getEventId(),auctionCustomSms);
        }else {
            AuctionCustomSms auctionCustomSmsData = auctionCustomSmsService.createAuctionCustomSmsForDBSave(event.getEventId());
            auctionCustomSmsData.setATTENDEE_CHECK_IN(superAdminCustomSmsDto.getAttendeeCheckIn());
            auctionCustomSmsService.save(auctionCustomSmsData);
        }
        return superAdminCustomSmsDto;

    }

    /*Move Non-WhiteLabel Event To WhiteLabel Event*/
    @Override
    public void moveEventToWLAccount(Event event, WhiteLabel whiteLabel, User user) {
        log.info("Moving event {} to WL {} by user {}", event.getEventId(), whiteLabel.getId(), user.getUserId());

        //Check stripe and disconnect stripe for non whitelabel event
        if (Boolean.TRUE.equals(stripeService.isStripeConnected(event))) {
            eventService.disconnectPaymentGateway(event, true, user);
        }

        if (event.getWhiteLabel() == null) {

            //Apply whitelabel settings to event
            eventService.setWhiteLabelSettingToEvent(event,whiteLabel, user);
            log.info("moveEventToWLAccount TransactionFeeConditionalLogic updated whiteLabel vise. eventId {} updatedBy {}",event.getEventId(), user.getUserId());
            Ticketing ticketing = ticketingService.findByEvent(event);
            if (whiteLabel.isManualPayout()) {
                ticketing.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
            }
            ticketingService.save(ticketing);

            eventService.updateDeleteStatusToApiUser(event, event.getOrganizer(), null);
            eventService.addNewApiUserToStaff(event.getOrganizer(), event, event.getWhiteLabel());
            eventRepoService.save(event);
            hubspotEventService.updateHubspotEventDetails(event);
            hubspotEventService.associateOrganizerOrWLEventToCompany(event);
            eventService.setEventDesignDetailsSetting(event, whiteLabel);
            contactModuleSettingsService.updateEventEngageEmailLimit(event);
            log.info("Successfully moved event {} to WL {} by user {}", event.getEventId(), whiteLabel.getId(), user.getUserId());
        } else {
            throw new NotAcceptableException(NotAcceptableException.WhiteLabelExceptionMsg.ALREADY_WHITELABEL_EVENT);
        }
    }

    /*Update Default Design Details For WhiteLabel Event*/
    @Override
    @Transactional
    public void updateEventDesignDetailsForWLAccount(Event event, User user) {

        if (event.getWhiteLabel() != null) {
            WhiteLabel whiteLabel = event.getWhiteLabel();

            //Apply default whitelabel transaction logic to ticketing and event transaction logic
            eventService.setDefaultWlDesignSettingsForEvent(event,whiteLabel);

            //Update event design detail from whitelabel settings
            whiteLabelService.updateEventDesignDetailsUsingWhiteLabelDetails(event, whiteLabel, user);
        }
    }

    /*Add base record for Whitelabel in Transaction fee condition logic table */
    @Override
    @Transactional
    public void addBaseRecordForWhiteLabelInTransactionTable(Long whiteLabelId) {

        Optional<WhiteLabel> whiteLabelOpt = roWhiteLabelService.getWhiteLabelById(whiteLabelId);
        if(whiteLabelOpt.isPresent())
        {
            WhiteLabel whiteLabel = whiteLabelOpt.get();
            List<TransactionFeeConditionalLogic> transactionFeeConfigurations = transactionFeeConditionalLogicService
                    .getBaseRecordByWhiteLabelAndIsVirtualEvent(whiteLabel,false);
            if(transactionFeeConfigurations.isEmpty()) {
                transactionFeeConditionalLogicService
                        .setTransactionFeeConditionalLogicForVirtualEvent(null, GREATER_THAN_EQUAL_TO, AE_FLAT_FEE_ONE,
                                AE_FEE_PERCENTAGE_THREE, POINT_ZERO_ONE, false,whiteLabel, -1);
            }
        }
        else {
            throw new NotFoundException(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND);        }
    }

    @Override
    public void addBaseRecordForTransactionTable(Long eventId, String whiteLabelUrl, String organizerUrl,
                                                 TransactionFeeBaseRecordDto transactionFeeBaseRecordDto, User user) {
        List<TransactionFeeConditionalLogic> transactionFeeConfigurations;
        log.info("addBaseRecordForTransactionTable | eventId | {} | whiteLabelUrl | {} | organizerUrl | {} | Operator | {} ",
                eventId,whiteLabelUrl,organizerUrl, transactionFeeBaseRecordDto.toString());
        if (eventId != null) {
            Event event = roEventService.findEventByEventId(eventId);
            if(event != null) {
                transactionFeeConfigurations = transactionFeeConditionalLogicRepository.findByEventAndIsVirtualEventAndIsAddon(event, transactionFeeBaseRecordDto.isVirtual(), transactionFeeBaseRecordDto.isAddon());
                raiseErrorIfRecordAlreadyExists(transactionFeeBaseRecordDto, transactionFeeConfigurations);
                transactionFeeConditionalLogicService.setTransactionFeeConditionalLogic(
                        event, event.getOrganizer(), transactionFeeBaseRecordDto.getOperator(), transactionFeeBaseRecordDto.getAeFeeFlat(),
                        transactionFeeBaseRecordDto.getAeFeePercentage(), transactionFeeBaseRecordDto.getWlAFeeFlat(),
                        transactionFeeBaseRecordDto.getWlBFeeFlat(), transactionFeeBaseRecordDto.getWlAFeePercentage(),
                        transactionFeeBaseRecordDto.getWlBFeePercentage(), transactionFeeBaseRecordDto.getFromTicketingPriceThreshold(),
                        transactionFeeBaseRecordDto.isVirtual(), event.getWhiteLabel(), transactionFeeBaseRecordDto.getToTicketPriceThreshold());
            }
        } else if (whiteLabelUrl != null) {
            WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
            if (whiteLabel !=null) {
                transactionFeeConfigurations = transactionFeeConditionalLogicRepository.findByWhiteLabelAndEventIsNullAndIsVirtualEventAndOrganizerAndIsAddon(
                        whiteLabel, transactionFeeBaseRecordDto.isVirtual(),  null, transactionFeeBaseRecordDto.isAddon());
                raiseErrorIfRecordAlreadyExists(transactionFeeBaseRecordDto, transactionFeeConfigurations);
                transactionFeeConditionalLogicService.setTransactionFeeConditionalLogic(
                        null, null, transactionFeeBaseRecordDto.getOperator(), transactionFeeBaseRecordDto.getAeFeeFlat(),
                        transactionFeeBaseRecordDto.getAeFeePercentage(), transactionFeeBaseRecordDto.getWlAFeeFlat(),
                        transactionFeeBaseRecordDto.getWlBFeeFlat(), transactionFeeBaseRecordDto.getWlAFeePercentage(),
                        transactionFeeBaseRecordDto.getWlBFeePercentage(), transactionFeeBaseRecordDto.getFromTicketingPriceThreshold(),
                        transactionFeeBaseRecordDto.isVirtual(), whiteLabel, transactionFeeBaseRecordDto.getToTicketPriceThreshold());
            }
        } else if (organizerUrl != null) {
            Organizer organizer = organizerService.getOrganizerByURL(organizerUrl);
            if(organizer != null) {
                transactionFeeConfigurations = transactionFeeConditionalLogicRepository.findByOrganizerAndEventIsNullAndIsVirtualEventAndIsAddon(
                        organizer, transactionFeeBaseRecordDto.isVirtual(), transactionFeeBaseRecordDto.isAddon());
                raiseErrorIfRecordAlreadyExists(transactionFeeBaseRecordDto, transactionFeeConfigurations);
                transactionFeeConditionalLogicService.setTransactionFeeConditionalLogic(
                        null, organizer, transactionFeeBaseRecordDto.getOperator(), transactionFeeBaseRecordDto.getAeFeeFlat(),
                        transactionFeeBaseRecordDto.getAeFeePercentage(), transactionFeeBaseRecordDto.getWlAFeeFlat(),
                        transactionFeeBaseRecordDto.getWlBFeeFlat(), transactionFeeBaseRecordDto.getWlAFeePercentage(),
                        transactionFeeBaseRecordDto.getWlBFeePercentage(), transactionFeeBaseRecordDto.getFromTicketingPriceThreshold(),
                        transactionFeeBaseRecordDto.isVirtual(), organizer.getWhiteLabel(), transactionFeeBaseRecordDto.getToTicketPriceThreshold());
            }
        } else {
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }
        log.info("addBaseRecordForTransactionTable | eventId | {} | whiteLabelUrl | {} | organizerUrl | {} | Operator | {} | AeFeeFlat | {} |" +
                " AeFeePercentage | {} | WlAFeeFlat | {} | WlBFeeFlat | {} | WlAFeePercentage | {} | WlBFeePercentage | {} | TicketingPriceThreshold | {} |" +
                        " IsVirtual | {}",
                eventId,whiteLabelUrl,organizerUrl,transactionFeeBaseRecordDto.getOperator(),transactionFeeBaseRecordDto.getAeFeeFlat(),
                transactionFeeBaseRecordDto.getAeFeePercentage(),transactionFeeBaseRecordDto.getWlAFeeFlat(),transactionFeeBaseRecordDto.getWlBFeeFlat(),
                transactionFeeBaseRecordDto.getWlAFeePercentage(),transactionFeeBaseRecordDto.getWlBFeePercentage(),
                transactionFeeBaseRecordDto.getFromTicketingPriceThreshold(), transactionFeeBaseRecordDto.isVirtual());
    }

    private void raiseErrorIfRecordAlreadyExists(TransactionFeeBaseRecordDto transactionFeeBaseRecordDto, List<TransactionFeeConditionalLogic> transactionFeeConfigurations) {
        if(!transactionFeeConfigurations.isEmpty()){
            Optional<TransactionFeeConditionalLogic> transactionFeeConditionalLogic = transactionFeeConfigurations.stream().filter(e -> !e.isAddon()
                    && e.getOperator().equals(transactionFeeBaseRecordDto.getOperator())
                    && e.getFromTicketPriceThreshold() == transactionFeeBaseRecordDto.getFromTicketingPriceThreshold()).findAny();
            if(transactionFeeConditionalLogic.isPresent()){
                NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.ALREADY_EXISTS_THRESHOLD_AND_OPERATOR;
                String errorMessageTemplate = "Record for operator ${operator} and threshold ${threshold} already exists.";
                String errorMessage = errorMessageTemplate
                        .replace("${operator}", transactionFeeBaseRecordDto.getOperator())
                        .replace("${threshold}", String.valueOf(transactionFeeBaseRecordDto.getFromTicketingPriceThreshold()));
                exception.setErrorMessage(errorMessage);
                exception.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(exception);
            }
        }
    }

    @Override
    public List<TransactionFeeBaseRecordDto> getTransactionTableBaseRecord(Long eventId, String whiteLabelUrl, String organizerUrl) {
        List<TransactionFeeConditionalLogic> transactionFeeConfigurations = null;
        if (whiteLabelUrl != null) {
            WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
            if (whiteLabel !=null) {
                transactionFeeConfigurations =
                        transactionFeeConditionalLogicRepository.findByWhiteLabelAndEventIsNullAndOrganizerIsNull(whiteLabel);
            }
        } else if (organizerUrl != null) {
            Organizer organizer = organizerService.getOrganizerByURL(organizerUrl);
            transactionFeeConfigurations = transactionFeeConditionalLogicRepository.findByOrganizerAndEventIsNull(organizer);
        } else if (eventId != null) {
            Event event = roEventService.findEventByEventId(eventId);
            transactionFeeConfigurations = transactionFeeConditionalLogicService
                    .getRecordByEvent(event);
        } else {
            throw new NotFoundException(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND);
        }
        log.info("getTransactionTableBaseRecord | eventId | {} | whiteLabelUrl | {} | organizerUrl | {} ",eventId,whiteLabelUrl,organizerUrl);
        if(transactionFeeConfigurations != null) {
            return transactionFeeConfigurations.stream().map(TransactionFeeBaseRecordDto::new).collect(Collectors.toList());
        }else {
            throw new NotFoundException(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND);
        }
    }

    @Override
    public void updateTransactionTableBaseRecord(Long transactionFeeConditionId, TransactionFeeBaseRecordDto baseRecordDto, User user) {
        transactionFeeConditionalLogicService.updateTransactionTableBaseRecord(transactionFeeConditionId, baseRecordDto);
    }

    @Override
    public void addBulkTransactionFeeConditionRecords(Long eventId, String whiteLabelUrl, String organizerUrl, List<TransactionFeeBaseRecordDto> transactionFeeBaseRecordDtos, User user) {
        log.info("Adding bulk transaction fee condition records for eventId {} whiteLabelUrl {} organizerUrl {} by user {}",eventId,whiteLabelUrl,organizerUrl,user.getUserId());
        if (transactionFeeBaseRecordDtos != null && !transactionFeeBaseRecordDtos.isEmpty()) {
            transactionFeeBaseRecordDtos.forEach(e -> {
                if (e.getToTicketPriceThreshold() == Double.POSITIVE_INFINITY) {
                    e.setToTicketPriceThreshold(-1);
                }
                if(e.getFromTicketingPriceThreshold() == Double.NEGATIVE_INFINITY){
                    e.setFromTicketingPriceThreshold(-1);
                }
                addBaseRecordForTransactionTable(eventId, whiteLabelUrl, organizerUrl, e, user);
            });
        }
    }

    @Override
    public void bulkUpdateTransactionFeeConditionRecord(List<TransactionFeeBaseRecordDto> transactionFeeBaseRecordDtos, User user) {
        log.info("Bulk update transaction fee condition records by user {}",user.getUserId());
        if (transactionFeeBaseRecordDtos != null && !transactionFeeBaseRecordDtos.isEmpty()) {
            transactionFeeBaseRecordDtos.forEach(e -> {
                if (e.getToTicketPriceThreshold() == Double.POSITIVE_INFINITY) {
                    e.setToTicketPriceThreshold(-1);
                }
                if(e.getFromTicketingPriceThreshold() == Double.NEGATIVE_INFINITY){
                    e.setFromTicketingPriceThreshold(-1);
                }
                updateTransactionTableBaseRecord(e.getId(), e, user);
            });
        }
    }

    @Override
    public void deleteById(Long transactionFeeConditionId, User user) {
        log.info("Deleting transaction fee condition record by user {} transactionFeeConditionId {}",user.getUserId(), transactionFeeConditionId);
        transactionFeeConditionalLogicService.deleteById(transactionFeeConditionId);
    }

    @Override
    @Transactional
    public void updateAutoKillUnAuthorizedStream(Long eventId, boolean autoDisableUnAuthLiveStream) {
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(eventId);
        if (eventPlanConfig != null) {
            if (!autoDisableUnAuthLiveStream) {
                handleForDirectUpload(eventId);
                handleForLiveStreamDisabled(eventId);
            }
            eventPlanConfigRepoService.updateAutoKillUnAuthorizedStream(eventPlanConfig,autoDisableUnAuthLiveStream);
        } else {
            throw new NotFoundException(EVENT_PLAN_CONFIG_NOT_FOUND);
        }

    }

    private void handleForLiveStreamDisabled(Long eventId) {
        List<Session> sessions = sessionRepoService.findByEventIdAndRecordStatus(eventId, RecordStatus.BLOCK);
        sessions.forEach(e->{
            muxService.markEnableLiveStreamAbsorbException(e.getLiveStreamId());
            e.setRecordStatus(RecordStatus.UNBLOCKED);
        });

        sessionRepoService.saveAll(sessions);
    }

    private void handleForDirectUpload(Long eventId) {
        List<MUXLivestreamAssetDetails> muxLivestreamAssetDetails = muxLivestreamAssetRepoService.findMuxPlayBackRemovedLiveStreamAssetsByEventId(eventId);

        Map<Long, String> assetIdAndDefaultPlayBackId = new HashMap<>();

        muxLivestreamAssetDetails.forEach(e -> {
            if (!checkPlayBackIdPresentInMux(e)) {
                String playBackId = muxService.createAssetPlayBackId(e.getAssetId());
                if(StringUtils.isNotBlank(playBackId)){
                    muxLivestreamAssetRepoService.updatePlayBackUrlByAssetId(e.getAssetId(), playBackId);
                }

                if (e.isDefaultPlayback()) {
                    assetIdAndDefaultPlayBackId.put(e.getSessionId(), playBackId);
                }
            }
        });

        assetIdAndDefaultPlayBackId.forEach((key, value) -> sessionRepoService.updatePlayBackUrlBySessionId(key, value, eventId));
    }

    private boolean checkPlayBackIdPresentInMux(MUXLivestreamAssetDetails e) {
        JSONObject jsonBody = muxService.getAssetDetails(e.getAssetId());
        if(jsonBody.has(Constants.PLAYBACK_IDS_STRING)){
            try {
                JSONArray jsonArray = jsonBody.getJSONArray(Constants.PLAYBACK_IDS_STRING);
                List<String> playBackIds = new ArrayList<>();
                if(jsonArray!=null && jsonArray.length()> 0){
                    for(int i=0;i<jsonArray.length();i++){
                        playBackIds.add(new JSONObject(jsonArray.get(i).toString()).getString("id"));
                    }
                }
                if(playBackIds.contains(e.getPlaybackId())){
                   return true;
                }
            } catch (JSONException jsonException) {
                jsonException.printStackTrace();
            }
        }
        return false;
    }

    @Override
    public ResponseDto moveOrganizerToWLAccount(Organizer organizer, WhiteLabel whiteLabel, User user) {
        log.info("Moving Organiser {} to WL {} by User {}", organizer.getId(), whiteLabel.getId(), user.getUserId());
        if (organizer.getWhiteLabel() != null) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ALREADY_WHITELABEL_ORGANIZER);
        }
        List<Object[]> activeEvents = eventRepoService.findActiveEventsByOrganizerURL(organizer.getOrganizerPageURL());
        List<String> eventErrorMsg = eventService.isPaymentGateWayConnectedToOrganizerEvents(activeEvents);
        if(!CollectionUtils.isEmpty(eventErrorMsg)){
            String listToCommaSeparated = GeneralUtils.convertListToCommaSeparated(eventErrorMsg);
            NotAcceptableException.NotAceptableExeceptionMSG exceptionMsg = NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_MOVE_ORGANIZER_TO_WL_ACCOUNT;
            exceptionMsg.setDeveloperMessage(listToCommaSeparated);
            throw new NotAcceptableException(exceptionMsg);
        }
        organizerService.moveOrganizerToWLAccount(organizer, whiteLabel);

        List<Event> events = roEventService.findByOrganizerId(organizer.getId());

        for (Object[] obj : activeEvents) {
            Long eventId = Long.parseLong(obj[8].toString());
            Event event = eventRepoService.findEventByIdOrThrowError(eventId);
            events.remove(event);
            event.setOrganizerId(organizer.getId());
            event.setOrganizer(organizer);
            moveEventToWLAccount(event, whiteLabel, user);
        }

        events.forEach(event -> {
            event.setWhiteLabelId(whiteLabel.getId());
            event.setWhiteLabel(whiteLabel);

            eventService.updateDeleteStatusToApiUser(event, event.getOrganizer(), null);
            eventService.addNewApiUserToStaff(event.getOrganizer(), event, event.getWhiteLabel());
        });

        eventRepoService.saveAll(events);
        apiKeyService.updateApiUserForWLOrganizer(organizer, whiteLabel);
        organizerService.updateDeleteStatusToClientApiKey(organizer);
        trayIntegrationService.updateOrganizerToWLIntegration(organizer, whiteLabel);
        //we want to update engage email limit only for moved organiser , that's why we are passing here organiser and wl as null
        contactModuleSettingsService.updateAllEventEngageEmailLimit(null,organizer);
        if (null != whiteLabel.getHubspotCompanyId()) {
            hubspotOrganizerService.createCompanyOrganizerAssociation(organizer.getId(), whiteLabel.getHubspotCompanyId().toString());
        }

        log.info("organizer moved to whitelabel successfully for whiteLabelUrl => {} and organizerUrl => {} ",whiteLabel.getWhiteLabelUrl(),organizer.getOrganizerPageURL());
        return new ResponseDto(Constants.SUCCESS, Constants.ORGANIZER_MOVED_INTO_WL_ACCOUNT.replace(Constants.CURLY_BRACES_ORGANIZER_NAME,organizer.getName()).replace(Constants.CURLY_BRACES_WL_NAME,whiteLabel.getFirmName()));

    }

    @Override
    public ResponseDto disconnectOrganizerFromWL(Organizer organizer, WhiteLabel whiteLabel, User user) {
        log.info("disconnectOrganizerFromWL process started for organizer=>{} whitelabelId=>{}",organizer.getId(),whiteLabel.getId());
        if (organizer.getWhiteLabel() == null) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_WHITELABEL_ORGANIZER);
        }

        List<Event> events = roEventService.findByOrganizerId(organizer.getId());
        List<String> eventErrorList = new ArrayList<>();

        isStripeConnectedToEvent(events, organizer.getId(), eventErrorList);

        if(!CollectionUtils.isEmpty(eventErrorList)){
            String listToCommaSeparated = GeneralUtils.convertListToCommaSeparated(eventErrorList);
            NotAcceptableException.NotAceptableExeceptionMSG exceptionMsg = NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_MOVE_ORGANIZER_FROM_WL_ACCOUNT;
            exceptionMsg.setDeveloperMessage(listToCommaSeparated);
            throw new NotAcceptableException(exceptionMsg);
        }
        Long organizerHSCompanyId= organizer.getHubSpotCompanyId();
        organizerService.moveOrganizerFromWLAccount(organizer, whiteLabel);

        List<Long> eventIds = events.stream().map(Event::getEventId).collect(Collectors.toList());
        // if Event is associated with WhiteLabel HS company then we need to delete WL HS company association with event
        if(whiteLabel.getHubspotCompanyId() != null) {
            // Delete only WhiteLabel HS Company association Label if Organizer HS Company is same as WhiteLabel HS Company
            hubspotEventService.deleteAssociationOrganizerOrWLEventToCompanyInBatch(eventIds, whiteLabel.getHubspotCompanyId(), whiteLabel, !whiteLabel.getHubspotCompanyId().equals(organizerHSCompanyId));
        }

        for (Event event : events) {
            //Check stripe and disconnect stripe for non whitelabel event
            if (Boolean.TRUE.equals(stripeService.isStripeConnected(event))) {
                eventService.disconnectPaymentGateway(event, true, user);
            }
            if (event.getWhiteLabel() != null) {
                //Remove whitelabel settings from event
                event.setWhiteLabelId(null);
                event.setWhiteLabel(null);
                event.setOrganizerId(organizer.getId());
                event.setOrganizer(organizer);
                eventService.updateDeleteStatusToApiUser(event, null, whiteLabel);
                hubspotEventService.associateOrganizerOrWLEventToCompany(event);
                eventPlanConfigService.handleCreateOrUpdatePlanForEvent(event);
                log.info("Event {} disconnected from whitelabel {} successfully for move organizer from WL account",event.getEventId(), whiteLabel.getId());
            }
        }
        eventRepoService.saveAllAndClearCache(events);

        //remove default whitelabel transaction logic to ticketing and event transaction logic
        eventService.setDefaultTransactionFeeLogicForEventToDisconnectFromWL(organizer);

        hubspotEventService.updateHubSpotEventByEventIds(eventIds);
        trayIntegrationService.deleteIntegrationMappingForEventsAndOrganizer(eventIds, organizer.getId());
        //we want to update engage email limit only for moved organiser , that's why we are passing here organiser and wl as null
        contactModuleSettingsService.updateAllEventEngageEmailLimit(null, organizer);
        log.info("organizer disconnected from whitelabel successfully for whiteLabelUrl => {} and organizerUrl => {} ",whiteLabel.getWhiteLabelUrl(),organizer.getOrganizerPageURL());
        return new ResponseDto(Constants.SUCCESS, Constants.ORGANIZER_DISCONNECTED_FROM_WL.replace(Constants.CURLY_BRACES_ORGANIZER_NAME,organizer.getName()).replace(Constants.CURLY_BRACES_WL_NAME,whiteLabel.getFirmName()));
    }

    @Override
    public ResponseDto moveOrganizerFromWLToOtherWL(Organizer organizer, WhiteLabel toWhiteLabel, User user) {
        if (organizer.getWhiteLabel() == null) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_WHITELABEL_ORGANIZER);
        }
        WhiteLabel fromWhiteLabel = organizer.getWhiteLabel();
        if(fromWhiteLabel.getId() == toWhiteLabel.getId()){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ORGANIZER_ALREADY_CONNECTED_WITH_SAME_WHITELABEL);
        }
        log.info("moveOrganizerFromWLToOtherWL process started for organizer=>{} From WhitelabelId=>{} To WhitelabelId=>{}",organizer.getId(), fromWhiteLabel.getId(), toWhiteLabel.getId());
        List<Event> events = roEventService.findByOrganizerId(organizer.getId());
        List<String> eventErrorList = new ArrayList<>();

        isStripeConnectedToEvent(events, organizer.getId(), eventErrorList);

        if(!CollectionUtils.isEmpty(eventErrorList)){
            String listToCommaSeparated = GeneralUtils.convertListToCommaSeparated(eventErrorList);
            NotAcceptableException.NotAceptableExeceptionMSG exceptionMsg = NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_MOVE_ORGANIZER_FROM_WL_ACCOUNT;
            exceptionMsg.setDeveloperMessage(listToCommaSeparated);
            throw new NotAcceptableException(exceptionMsg);
        }
        Long organizerHSCompanyId= organizer.getHubSpotCompanyId();
        Long fromWhiteLabelHSCompanyId= fromWhiteLabel.getHubspotCompanyId();
        organizerService.moveOrganizerFromWLAccountToOtherWLAccount(organizer, toWhiteLabel);
        log.info("moveOrganizerFromWLToOtherWL TransactionFeeConditionalLogic updateBy {}",user.getUserId());
        List<Long> eventIds = events.stream().map(Event::getEventId).collect(Collectors.toList());
        // if Event is associated with WhiteLabel HS company then we need to delete WL HS company association with event
        if(organizerHSCompanyId != null && fromWhiteLabelHSCompanyId != null && !fromWhiteLabelHSCompanyId.equals(toWhiteLabel.getHubspotCompanyId())) {
            // Delete only WhiteLabel HS Company association Label if Organizer HS Company is same as old WhiteLabel HS Company
            // Delete both Organizer and WhiteLabel HS Company association Label if Organizer HS Company is different from old WhiteLabel HS Company
            hubspotEventService.deleteAssociationOrganizerOrWLEventToCompanyInBatch(eventIds, fromWhiteLabelHSCompanyId, fromWhiteLabel, !organizerHSCompanyId.equals(fromWhiteLabelHSCompanyId));
        }

        Map<Long, Ticketing> eventTicketing = ticketingService.findTicketingByEventIds(eventIds);
        List<Ticketing> ticketingList = new ArrayList<>();

        for (Event event : events) {
            //Check stripe and disconnect stripe for non whitelabel event
            if (Boolean.TRUE.equals(stripeService.isStripeConnected(event))) {
                eventService.disconnectPaymentGateway(event, true, user);
            }
            if (event.getWhiteLabel() != null) {
                //set another whitelabel settings to event
                event.setWhiteLabelId(toWhiteLabel.getId());
                event.setWhiteLabel(toWhiteLabel);
                event.setOrganizer(organizer);
                event.setOrganizerId(organizer.getId());

                //Apply whitelabel settings to event
                eventService.setWhiteLabelSettingToEvent(event,toWhiteLabel, user);

                //Set whitelabel ticketing setting to event ticketing
                Ticketing ticketing = eventTicketing.get(event.getEventId());
                if (null != ticketing && toWhiteLabel.isManualPayout()) {
                    ticketing.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
                    ticketingList.add(ticketing);
                }

                eventService.updateDeleteStatusToApiUser(event, null, fromWhiteLabel);
                eventService.addNewApiUserToStaff(null, event, toWhiteLabel);
                hubspotEventService.associateOrganizerOrWLEventToCompany(event);
                eventPlanConfigService.handleCreateOrUpdatePlanForEvent(event);
                log.info("Event {} is moved from whitelabel {} to other whitelabel {} successfully",event.getEventId(), fromWhiteLabel.getId(), toWhiteLabel.getId());
            }
        }

        ticketingRepository.saveAll(ticketingList);

        List<EventDesignDetail> eventDesignDetailList = eventDesignDetailService.findByEventList(events);
        for (EventDesignDetail eventDesignDetail : eventDesignDetailList) {
            eventDesignDetail.setLogoImage(toWhiteLabel.getLogoImage());
            eventDesignDetail.setHeaderColor(toWhiteLabel.getHeaderColor());
            eventDesignDetail.setBannerImage(toWhiteLabel.getBannerImage());
        }
        
        eventDesignDetailService.saveAll(eventDesignDetailList);
        eventRepoService.saveAllAndClearCache(events);

        //remove default whitelabel transaction logic to ticketing and event transaction logic
        eventService.setDefaultTransactionFeeLogicForEventToMoveWLToOtherWL(organizer, toWhiteLabel);

        hubspotEventService.updateHubSpotEventByEventIds(eventIds);

        trayIntegrationService.updateIntegrationMappingWLToOtherWL(eventIds, fromWhiteLabel, toWhiteLabel);
        //we want to update engage email limit only for moved organiser , that's why we are passing here organiser and wl as null
        contactModuleSettingsService.updateAllEventEngageEmailLimit(null,organizer);
        log.info("organizer {} moved from whitelabel {} to whitelabel {} successfully  and organizerUrl ", organizer.getId(), fromWhiteLabel.getId(), toWhiteLabel.getId());
        return new ResponseDto(Constants.SUCCESS, Constants.ORGANIZER_MOVED_FROM_WL_TO_OTHER_WL.replace(Constants.CURLY_BRACES_ORGANIZER_NAME,organizer.getName())
                .replace(Constants.CURLY_BRACES_FROM_WL_NAME,fromWhiteLabel.getFirmName())
                .replace(Constants.CURLY_BRACES_WL_NAME, toWhiteLabel.getFirmName()));
    }

    private void isStripeConnectedToEvent(List<Event> events, Long organizerId, List<String> eventErrorList) {
        for (Event event : events) {
            Stripe stripeByEvent = roStripeService.findByEvent(event);
            if (isNotBlank(stripeByEvent.getAccessToken()) && paymentService.isCustomerCreatedForEvent(event)) {
                String paymentGateway = stripeByEvent.getPaymentGateway();
                eventErrorList.add(Constants.EVENT_CONNECTED_TO_PAYMENT_GATEWAY_AND_CUSTOMER_CREATED.replace("{eventName}",event.getName()).
                        replace(Constants.PAYMENT_GATEWAY, paymentGateway));
                log.info("Event {} of Organizer {} is connected to payment gateway {}", event.getEventId(), organizerId, paymentGateway);
            }
        }
    }


    /**
     * this method is used to validate input
     * @param superAdminSetting object with Super admin settings
     */
    public void validate(SuperAdminSetting superAdminSetting, Event event) {
        Optional<ContactModuleSettings> contactModuleSettingsOpt = contactModuleSettingsService.findContactModuleSettingsByEvent(event);
        if (contactModuleSettingsOpt.isPresent()) {
            if (superAdminSetting.getEngageEmailsLimit() < 0) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NUMBERS_OF_EMAILS_ALLOWED);
            }
            ContactModuleSettings contactModuleSettingEntity = contactModuleSettingsOpt.get();
            if (contactModuleSettingEntity.getEngageEmailsLimit() != superAdminSetting.getEngageEmailsLimit()) {
                validateEngageEmailLimit(event, superAdminSetting.getEngageEmailsLimit());
            }
        }
    }
    private void validateEngageEmailLimit(Event event, int allowedEmails) {
        log.info("validateEngageEmailLimit | eventId {} isScheduled {}", event.getEventId(), true);
        List<MessageToContacts> messageToContacts;
        messageToContacts = messageToContactsRepository.findByEventIdAndTypeAndStatusIn(event.getEventId(), EmailNotificationType.EMAIL,List.of(MessageStatus.SENT, MessageStatus.SCHEDULED));
        engageEmailLimit(messageToContacts,allowedEmails);
    }

    private void engageEmailLimit(List<MessageToContacts> messageToContacts, int allowedEngageEmails) {
        if(messageToContacts.size() > allowedEngageEmails && 0!=allowedEngageEmails){
            NotAcceptableException.ContactExceptionMsg exceptionMsg = NotAcceptableException.ContactExceptionMsg.
                    CANNOT_SET_LESS_THAN_EMAILS_USED;
            exceptionMsg.setErrorMessage(Constants.CANNOT_SET_LESS_THAN_EMAILS_USED.replace("%d", String.valueOf(messageToContacts.size())));
            exceptionMsg.setDeveloperMessage(Constants.CANNOT_SET_LESS_THAN_EMAILS_USED.replace("%d", String.valueOf(messageToContacts.size())));
            throw new NotAcceptableException(exceptionMsg);
        }
    }

    @Override
    @Async
    @Transactional
    public void deleteOrganizer(Long organizerId, User user, Long eventsMoveToOrganizerId) {
        OrganizerDeletionAccessDto organizerDeletionAccessDto=organizerService.isOrganizerDeletable(organizerId,user);
        if(organizerDeletionAccessDto.isOrganizerDeletable()) {
            List<Event> events=roEventService.findByOrganizerId(organizerId);
            Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
            log.info("Organiser {} is deletable and {} events will be deleted", organizerId, events.size());
            this.deleteEventsForOrganizer(user,events);
            joinUsersWithOrganizersRepository.deleteJoinUsersWithOrganizerByOrganizer(organizerId);
            transactionFeeConditionalLogicService.updateRecStatusOfTransactionFeeConditionalLogicForOrganizer(organizer,RecordStatus.DELETE);
            organizerRepository.deleteOrganizer(organizerId, organizer.getOrganizerPageURL(),RecordStatus.DELETE);
        }else {
            List<Event> events = roEventService.findByOrganizerId(organizerId);
            Organizer oldOrganizer = organizerRepoService.findByIdThrowException(organizerId);
            if (!events.isEmpty()){
                if (null != eventsMoveToOrganizerId) {
                    Organizer newOrganizer = organizerRepoService.findByIdThrowException(eventsMoveToOrganizerId);
                    log.info("events {} will move to the new Organiser {}", events.size(), newOrganizer.getId());
                    List<TransactionFeeConditionalLogic> organiserList = transactionFeeConditionalLogicRepository.findByOrganizerAndEventIsNull(oldOrganizer);
                    List<Long> eventsIds = events.stream().map(Event::getEventId).collect(Collectors.toList());
                    List<TransactionFeeConditionalLogic> eventTransactionFeeConditionalLogicRecords = transactionFeeConditionalLogicRepository.findAllTransactionFeeConditionalLogicByEventIds(eventsIds);
                    eventTransactionFeeConditionalLogicRecords.forEach(transactionFeeConditionalLogic -> transactionFeeConditionalLogic.setOrganizer(newOrganizer));
                    organiserList.forEach(transactionFeeConditionalLogic -> transactionFeeConditionalLogic.setRecordStatus(RecordStatus.DELETE));
                    transactionFeeConditionalLogicRepository.saveAll(organiserList);
                    transactionFeeConditionalLogicRepository.saveAll(eventTransactionFeeConditionalLogicRecords);
                    for (Event event : events) {
                        event.setOrganizer(newOrganizer);
                        event.setOrganizerId(newOrganizer.getId());
                        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
                        eventPlanConfig.setOrganizer(newOrganizer);
                        eventPlanConfig.setWhiteLabel(newOrganizer.getWhiteLabel());
                        eventPlanConfig.setPlanConfig(newOrganizer.getPlanConfig());
                        eventPlanConfig.setPlanConfigId(newOrganizer.getPlanConfig().getId());
                        eventPlanConfigService.save(eventPlanConfig);
                        List<StaffDetailDto> staffDetailDtoForWhiteAdmin = new ArrayList<>();
                        List<StaffDetailDto> staffDetailDto = staffService.getStaffList(event);
                        if (null != event.getWhiteLabel()) {
                            staffDetailDtoForWhiteAdmin = staffService.findStaffDtoByWhiteLabel(event.getWhiteLabel());
                        }
                        staffDetailDto.addAll(staffDetailDtoForWhiteAdmin);
                        List<Long> userIds = staffDetailDto.stream().map(StaffDetail::getUserId).collect(Collectors.toList());
                        List<User> staffDetail = roUserService.getListOfUsersByUserIds(userIds);
                        updateStaffForOrganizer(newOrganizer, oldOrganizer, staffDetailDto, staffDetail);
                    }
                } else {
                    log.info("Organiser {} and {} events with plan associated will be deleted", organizerId, events.size());
                    List<ChargebeeEventUsages> chargebeeEventUsages = chargebeeEventUsagesRepoService.findByOrganizer(oldOrganizer);
                    if (!chargebeeEventUsages.isEmpty()){
                        chargebeeEventUsages.forEach(chargebeeEventUsages1 -> {
                            chargebeeEventUsages1.setOrganizer(null);
                            chargebeeEventUsages1.setOrganizerId(null);
                            chargebeeEventUsages1.setEvent(null);
                            chargebeeEventUsages1.setEventId(null);
                        });
                        chargebeeEventUsagesRepoService.saveAll(chargebeeEventUsages);
                        log.info("chargebeeEventUsages {} updated for organiser {}", chargebeeEventUsages.size(), oldOrganizer.getId());
                    }
                    List<ChargesPurchased> chargesPurchaseds = chargesPurchasedRepoService.findByOrganizerId(oldOrganizer.getId());
                    if (!chargesPurchaseds.isEmpty()){
                        chargesPurchaseds.forEach(chargesPurchased -> chargesPurchased.setRecordStatus(RecordStatus.DELETE));
                        chargesPurchasedRepoService.saveAll(chargesPurchaseds);
                        log.info("chargesPurchased {} updated for organiser {}", chargesPurchaseds.size(), oldOrganizer.getId());
                    }
                    List<ChargebeeEventCredits> chargebeeEventCredits = chargebeeEventCreditsService.findByEvents(events);
                    if (!chargebeeEventCredits.isEmpty()){
                        chargebeeEventCredits.forEach(chargebeeEventCredits1 -> chargebeeEventCredits1.setRecordStatus(RecordStatus.DELETE));
                        chargebeeEventCreditsService.saveAll(chargebeeEventCredits);
                        log.info("chargebeeEventCredits {} updated for organiser {}", chargebeeEventCredits.size(), oldOrganizer.getId());
                    }
                    ChargebeeEventCredits chargebeeEventCredit = chargebeeEventCreditsService.findByOrganizerId(oldOrganizer.getId());
                    if (null != chargebeeEventCredit){
                        chargebeeEventCredit.setRecordStatus(RecordStatus.DELETE);
                        chargebeeEventCreditsRepoService.save(chargebeeEventCredit, user);
                        log.info("chargebeeEventCredit {} updated for organiser {}", chargebeeEventCredit.getId(), oldOrganizer.getId());
                    }

                    List<EventChargeUsages> eventChargeUsages = eventChargeUsagesRepoService.findByOrganizerId(oldOrganizer.getId());
                    if (!eventChargeUsages.isEmpty()){
                        eventChargeUsages.forEach(eventChargeUsages1 -> {
                            eventChargeUsages1.setRecordStatus(RecordStatus.DELETE);
                            eventChargeUsages1.setUpdatedAt(new Date());
                        });
                        eventChargeUsagesRepoService.saveAll(eventChargeUsages);
                        log.info("eventChargeUsages {} updated for organiser {}", eventChargeUsages.size(), oldOrganizer.getId());
                    }

                    this.deleteEventsForOrganizer(user,events);
                    List<ChargebeeTransaction> chargebeeTransactions = chargebeeTransactionRepository.findByOrganizerId(organizerId);
                    if (!chargebeeTransactions.isEmpty()){
                        List<Long> chargebeeTransactionsIds = chargebeeTransactions.stream().distinct().map(ChargebeeTransaction::getId).collect(Collectors.toList());
                        chargebeeTransactionRepository.updateStatusToDeleteByIds(chargebeeTransactionsIds, RecordStatus.DELETE);
                        log.info("chargebeeTransactions {} updated for organiser {}", chargebeeTransactions.size(), oldOrganizer.getId());
                    }
                }
            }
            List<Integration> integrations = integrationRepository.findByIntegrationSourceIdAndSourceType(organizerId, IntegrationSourceType.ORGANIZER);
            if (!integrations.isEmpty()){
                integrations.forEach(integration -> integration.setRecordStatus(RecordStatus.DELETE));
                integrationRepository.saveAll(integrations);
                log.info("integrations {} updated for organiser {}", integrations.size(), organizerId);
            }
            joinUsersWithOrganizersRepository.deleteJoinUsersWithOrganizerByOrganizer(organizerId);
            transactionFeeConditionalLogicService.updateRecStatusOfTransactionFeeConditionalLogicForOrganizer(oldOrganizer,RecordStatus.DELETE);
            organizerRepository.deleteOrganizer(organizerId, oldOrganizer.getOrganizerPageURL(),RecordStatus.DELETE);
        }
    }

    private void updateStaffForOrganizer(Organizer organizer, Organizer oldOrganizer, List<StaffDetailDto> staffDetailDto, List<User> staffDetail) {
        Map<Long, User> userMap = staffDetail.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));
        staffDetailDto.forEach(e -> {
            if (StaffRole.admin.name().equals(e.getRole()) || StaffRole.whitelabeladmin.name().equals(e.getRole()) || StaffRole.eventcoordinator.name().equals(e.getRole())) {
                User user = userMap.get(e.getUserId());
                if (null != oldOrganizer && !oldOrganizer.getCreatedBy().getUserId().equals(e.getUserId())) {
                    organizerService.deleteJoinUsersWithOrganizers(user, oldOrganizer);
                }
                if (StaffRole.admin.name().equals(e.getRole())) {
                    organizerService.addJoinUsersWithOrganizers(user, organizer, OrganizerRole.admin, false);
                }
            }
        });
    }


    @Override
    public void deleteEventsForOrganizer(User user, List<Event> events){
        DeletedEventsDto deletedEventsDto = new DeletedEventsDto("organizer Delete",
                "Deleted organizer's events by  superAdmin");
        events.forEach(event ->  eventService.deleteEvent(event.getEventId(),user,deletedEventsDto));
    }

    private void setEngageEmailLimit(SuperAdminSetting settings, Event event, Optional<ContactModuleSettings> contactModuleSettings) {
        int engageEmailLimit;
        if (event.getWhiteLabel() != null) {
            engageEmailLimit = event.getWhiteLabel().getEngageEmailsLimit();
        } else {
            engageEmailLimit = event.getOrganizer().getEngageEmailsLimit();
        }
        int maxEngageEmailsAllowed = contactModuleSettings.map(ContactModuleSettings::getEngageEmailsLimit).orElse(engageEmailLimit);
        settings.setEngageEmailsLimit(maxEngageEmailsAllowed);
    }
}
