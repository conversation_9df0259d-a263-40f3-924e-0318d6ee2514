package com.accelevents.services.impl;

import com.accelevents.auction.dto.ExhibitorsActivity;
import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.exhibitors.Exhibitor;
import com.accelevents.domain.virtual.LeadRetriverData;
import com.accelevents.dto.*;
import com.accelevents.enums.StaffRole;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exhibitors.services.ExhibitorSettingsService;
import com.accelevents.repositories.VirtualEventLoggingRepository;
import com.accelevents.ro.neptune.RONeptuneAttendeeDetailService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.dynamodb.user.activity.UserActivityService;
import com.accelevents.services.elasticsearch.leaderboard.LeaderboardService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.ExhibitorRepoService;
import com.accelevents.services.tray.io.tracking.TrayTrackingService;
import com.accelevents.session_speakers.dto.EventTicketsIdsDto;
import com.accelevents.session_speakers.dto.FirstLastNameDto;
import com.accelevents.session_speakers.services.MeetingScheduleService;
import com.accelevents.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.*;


@Service
public class LeadServiceImpl implements LeadService {

    private static final Logger log = LoggerFactory.getLogger(LeadServiceImpl.class);

    @Autowired
    private LeadRetriverDataRepoService leadRetriverDataRepoService;

    @Autowired
    private ExhibitorService exhibitorService;
    
    @Autowired
    private StaffService staffService;

    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;

    @Autowired
    private AttendeeProfileService attendeeProfileService;

    @Autowired
    private VirtualEventLoggingRepository virtualEventLoggingRepository;

    @Autowired
    private IPLookUpDataService ipLookUpDataService;

    @Autowired
    private EventTicketsService eventTicketsService;

    @Autowired
    private LeaderboardService leaderboardService;

    @Autowired
    private EventCommonRepoService eventCommonRepoService;

    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Autowired
    private TicketHolderAttributesService ticketHolderAttributesService;

    @Autowired
    private ExhibitorRepoService repoService;

    @Autowired
    private ExhibitorSettingsService exhibitorSettingsService;

    @Autowired
    private TrayTrackingService trayTrackingService;

    @Autowired
    private MeetingScheduleService meetingScheduleService;

    @Autowired
    private TicketingHelperService ticketingHelperService;

    @Autowired
    private UserActivityService userActivityService;

    @Override
    @Transactional
    public ManualLeadsDto addLeadDetailData(LeadDetailDto leadDetailDto, Staff staff, Long exhibitorId, Event event, String source) {
        Exhibitor exhibitor = exhibitorService.findByIdAndEvent(exhibitorId, event);

        if (!exhibitor.isLeadRetrieversAllowed()) {
            throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.CAN_NOT_GENERATE_LEAD);
        }
        if (!StringUtils.isEmpty(leadDetailDto.getLeadData().getHolderFirstName()) && leadDetailDto.getLeadData().getHolderFirstName().length() > 50) {
            throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.FIRST_NAME_SIZE_LIMIT);
        } else if (!StringUtils.isEmpty(leadDetailDto.getLeadData().getHolderLastName()) && leadDetailDto.getLeadData().getHolderLastName().length() > 50) {
            throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.LAST_NAME_SIZE_LIMIT);
        } else if (!StringUtils.isEmpty(leadDetailDto.getLeadData().getHolderEmail()) && leadDetailDto.getLeadData().getHolderEmail().length() > 75) {
            throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.EMAIL_SIZE_LIMIT);
        }
        if (leadDetailDto.getLeadData().getEventTicketId() > 0 &&
                leadRetriverDataRepoService.isEventTicketExistByEventTicketIdAndExhibitorIdAndSourceAndStaffIdNull(leadDetailDto.getLeadData().getEventTicketId(), exhibitorId, leadDetailDto.getSource())) {
            throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.ALREADY_USED_TICKET);
        }
        User user = roUserService.findByEmail(leadDetailDto.getLeadData().getHolderEmail());

        Long userId;
        List<Long> ticketingTypeIds = new ArrayList<>();
        if (user != null) {
            // Do no generate lead for Admin/Staff/Exhibitor roles
            log.info("user found by email {}", user.getUserId());
            checkUserIsStaffForExhibitorAndThrowError(exhibitorId,user, event);
            userId = user.getUserId();
            LeadDetailDto leadRetriverData = null;
            try {
                leadRetriverData = getByUserId(exhibitorId, userId, event);
            } catch (Exception e) {
                log.info("exception raised while get user by id {}",e.getMessage());
            }
            Set<String> leadSources = new HashSet<>();
            leadSources.add("MANUAL");
            if (leadRetriverData != null && (leadDetailDto.getSource() == null || leadSources.contains(leadDetailDto.getSource()))) {
                throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.EXHIBITOR_ALREADY_EXISTS);
            }
            else if(leadRetriverData != null && leadDetailDto.getSource() != null && "SCAN".equalsIgnoreCase(leadDetailDto.getSource())){
                updateLeadDetailData(leadRetriverData.getLeadId(), leadDetailDto,  event, staff, source);
                log.info("Lead already exists so updating existing lead: {}", userId);
                ticketingTypeIds = getTicketTypeId(event, user.getUserId(), leadDetailDto);
                return new ManualLeadsDto(null,null,userId,ticketingTypeIds);
            }
        } else {//NO SOnar
            //TODO need to check with same email
            log.info("User not found with email: {}", leadDetailDto.getLeadData().getHolderEmail());
            user = new User();
            user.setEmail(leadDetailDto.getLeadData().getHolderEmail());
            userService.save(user);
            userId = user.getUserId();
        }

        if(null != userId) {
            ticketingTypeIds = getTicketTypeId(event, user.getUserId(), leadDetailDto);
        }
        Long ownerStaffId = leadDetailDto.getOwnerStaffId();
        Long staffId = null != staff ? staff.getId() : null;
        if (NumberUtils.isNumberGreaterThanZero(ownerStaffId)) {
            staffId = ownerStaffId;
        }
        saveLeadRetriever(leadDetailDto, exhibitor, event, userId, staffId);
        createUserActivity(leadDetailDto, userId, event, staff.getUserId(),exhibitor, LEAD_ADD_ACTIVITY, source);
        return new ManualLeadsDto(null,null,userId,ticketingTypeIds);
    }

    private List<Long> getTicketTypeId(Event event, Long userId, LeadDetailDto leadDetailDto) {
        List<Long> ticketingTypeIds= null;
        boolean isHolderDataPreference = ticketingHelperService.isCollectTicketHolderAttributesByEvent(event);
        List<EventTicketsIdsDto> eventTicketIdsAndTicketingTypeId;
        if(isHolderDataPreference) {
            eventTicketIdsAndTicketingTypeId = eventTicketsService.findEventTicketIdAndTicketingTypeIdByHolderUserIdAndEventId(userId, event.getEventId());
        }else{
            eventTicketIdsAndTicketingTypeId = eventTicketsService.findEventTicketIdAndTicketingTypeIdByPurchaserUserIdAndEventId(userId, event.getEventId());
        }
        if(null!=eventTicketIdsAndTicketingTypeId && !eventTicketIdsAndTicketingTypeId.isEmpty()){
            leadDetailDto.getLeadData().setEventTicketId(eventTicketIdsAndTicketingTypeId.get(0).getEventTicketsId());
            ticketingTypeIds = eventTicketIdsAndTicketingTypeId.stream().map(EventTicketsIdsDto::getTicketingTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        return ticketingTypeIds;
    }

    @Override
    public void saveLeadRetriever(LeadDetailDto leadDetailDto, Exhibitor exhibitor, Event event, Long userId, Long staffId) {
        leadDetailDto.setLocation(getLocation(leadDetailDto.getLeadData().getEventTicketId(), event));
        leadRetriverDataRepoService.save(leadDetailDto.toEntity(staffId, exhibitor.getId(), userId, false));
        trayTrackingService.trackExhibitorLead(event, leadDetailDto.getLeadData(),exhibitor,leadDetailDto.getSource());
    }



    private void deleteLeadById(LeadRetriverData leadRetriever) {
        leadRetriever.setRecordStatus(RecordStatus.DELETE);
        leadRetriverDataRepoService.save(leadRetriever);
    }

    @Override
    @Transactional
    public void deleteLeadDetailData(User user, Event event, Long exhibitorId, Long leadId, boolean leadRetriever) {
        log.info("Deleting LeadRetrieverData {}, with exhibitor {} by user {} " , leadId , exhibitorId, user.getUserId());
        exhibitorService.hasAccessToExhibitor(exhibitorId, event);
        LeadRetriverData leadRetriverData = getLeadRetriverData(leadId);
        log.info("Delete LeadRetrieverData {}, with exhibitor {}, of user {} and event {}" , leadId , leadRetriverData.getExhibitorId() , leadRetriverData.getUserId() , event.getEventId());

        if (leadRetriever) {
            checkLeadBelongToThisLeadRetriever(user, event, exhibitorId, leadRetriverData);
        }

        deleteLeadById(leadRetriverData);
        log.info("Successfully Deleted LeadRetrieverData {} " , leadId);
    }

    private void checkLeadBelongToThisLeadRetriever(User user, Event event, Long exhibitorId, LeadRetriverData leadRetriverData) {
        Staff staff = staffService.getStaffByUserRoleEventExhibitorId(user, event, exhibitorId, StaffRole.leadretriever);

        if (null == leadRetriverData.getStaffId() || staff.getId() != leadRetriverData.getStaffId()) {
            throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.LEAD_IS_NOT_BELONG_TO_THIS_LEADRETRIEVER);
        }
    }

    private LeadRetriverData getLeadRetriverData(Long leadId) {
        return leadRetriverDataRepoService.getLeadRetriverData(leadId) .orElseThrow(() -> new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.LEAD_NOT_EXISTS));
    }

    @Override
    @Transactional
    public void updateLeadDetailData(Long leadId, LeadDetailDto leadDetailDto, Event event, Staff staff, String source) {
        if (!StringUtils.isEmpty(leadDetailDto.getLeadData().getHolderFirstName()) && leadDetailDto.getLeadData().getHolderFirstName().length() > 50) {
            throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.FIRST_NAME_SIZE_LIMIT);
        } else if (!StringUtils.isEmpty(leadDetailDto.getLeadData().getHolderLastName()) && leadDetailDto.getLeadData().getHolderLastName().length() > 50) {
            throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.LAST_NAME_SIZE_LIMIT);
        } else if (!StringUtils.isEmpty(leadDetailDto.getLeadData().getHolderEmail()) && leadDetailDto.getLeadData().getHolderEmail().length() > 75) {
            throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.EMAIL_SIZE_LIMIT);
        }
        LeadRetriverData leadRetriverData = getLeadRetriverData(leadId);
        User user = roUserService.findByEmail(leadDetailDto.getLeadData().getHolderEmail());
        if (null == user) {
            user = new User();
            user.setEmail(leadDetailDto.getLeadData().getHolderEmail());
            userService.save(user);
        }
        checkUserIsStaffForExhibitorAndThrowError(leadRetriverData.getExhibitorId(),user, event);
        List<Long> eventTickets = eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(user.getUserId(), event.getEventId());
        if (!CollectionUtils.isEmpty(eventTickets)) {
            if (eventTickets.contains(leadRetriverData.getEventTicketId())) {
                leadDetailDto.getLeadData().setEventTicketId(leadRetriverData.getEventTicketId());
            } else {
                leadDetailDto.getLeadData().setEventTicketId(eventTickets.get(0));
            }
        }
        LeadRetriverData finalLeadRetriverData = leadDetailDto.toEntity(leadRetriverData, user.getUserId());
        leadRetriverDataRepoService.save(leadDetailDto.toEntity(leadRetriverData, user.getUserId()));
        createUserActivity(leadDetailDto, user.getUserId(),event, (staff != null) ? staff.getUserId() : 0L, finalLeadRetriverData.getExhibitor(), LEAD_UPDATE_ACTIVITY, source);
    }

    @Override
    @Transactional
    public DataTableResponse getAllLeadsData(User user, Event event, int size, int page, String search, Staff staff, Long exhibitorId) {
        log.info("Get all Lead of event {} and user {}, with exhibitor {} " ,event.getEventId(), user.getUserId(), exhibitorId);
        DataTableResponse dataTableResponse = new DataTableResponse();
        Pageable pageable = PageRequest.of(Math.max(page, 0), (size>0)?size:10);
        List<LeadRetriverData> leadDataList;

        List<Long> exhibitorIds = new ArrayList<>();
        exhibitorIds.add(exhibitorId);
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        boolean  isHolderDataPreference = ticketing.getCollectTicketHolderAttributes();
        leadDataList = getLeadRetriverData(search, staff, exhibitorIds);
        List<Long> eventTicketIdList = leadDataList.stream().filter(lead -> lead.getEventTicketId() != 0).map(LeadRetriverData::getEventTicketId).collect(Collectors.toList());
        Map<Long, TicketAttributeValueDto1>  ticketHolderAttributes = exhibitorSettingsService.prepareAttributeValueDto(eventTicketIdList);

        List<Long> userIdList = leadDataList.stream().filter(lead -> lead.getUserId() != null).map(LeadRetriverData::getUserId).distinct().collect(Collectors.toList());
        log.info("Leads of Event {} and all distinct UserIds {} " ,event.getEventId(), userIdList);
        List<BasicUserEventBillingDto> userDataList = userService.findByUserIdIn(userIdList);
        Map<Long,String> userEmailMap = CollectionUtils.isEmpty(userDataList) ? Collections.emptyMap() : userDataList.stream().collect(HashMap::new,(m,v)->m.put(v.getUserId(),v.getEmail()), HashMap::putAll);
        Map<Long, BasicUserEventBillingDto> mapUserDetails = userDataList.stream().collect(Collectors.toMap(BasicUserEventBillingDto::getUserId, Function.identity(), (a, b) -> a));

        if (!CollectionUtils.isEmpty(leadDataList)) {
            List<LeadRetriverData> leadRetriverDataList = new ArrayList<>();
            Map<Long,String> ticketHolderEmailMap = new HashMap<>();
            leadDataList.forEach(e -> {
                TicketHolderDetailDto leadData = JsonMapper.stringtoObject(e.getLeadData(), TicketHolderDetailDto.class);
                if (null != leadData) {
                    if (ticketHolderAttributes.containsKey(e.getEventTicketId())) {
                        TicketAttributeValueDto1 ticketAttributeValueDto = ticketHolderAttributes.get(e.getEventTicketId());
                        setLeadHolderName(leadData, ticketAttributeValueDto, mapUserDetails, e.getUserId(),isHolderDataPreference);
                        String holderEmail = leadData.getHolderEmail();
                        if(StringUtils.isEmpty(holderEmail) && null != e.getUserId()){
                            leadData.setHolderEmail(userEmailMap.get(e.getUserId()));
                        }else{
                            leadData.setHolderEmail(holderEmail);
                        }
                        //TODO- temporary fix for live event
                        if(!Constants.LEAD_SOURCE_MANUAL_TXT.equalsIgnoreCase(e.getSource())) {
                            String phoneNumberString = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_PHONE_NUMBER, isHolderDataPreference, true,false);
                            String countryCode = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_COUNTRY_CODE, isHolderDataPreference, true,false);
                            leadData.setHolderPhoneNumber(StringUtils.isEmpty(phoneNumberString) ? 0 : Long.parseLong(phoneNumberString.replaceAll("[^\\d]", "")));
                            leadData.setHolderCountryCode(StringUtils.isNotBlank(countryCode)?countryCode:null);
                        }
                        e.setLeadData(JsonMapper.convertToString(leadData));
                    }
                    ticketHolderEmailMap.put(e.getId(), leadData.getHolderEmail());
                }
            });
            Map<ExhibitorIdUserEmailDto, List<LeadRetriverData>> exhibitorIdUserIdDtoLeadRetriverDataMap = leadDataList.stream().collect(Collectors.groupingBy(lead -> new ExhibitorIdUserEmailDto(lead.getExhibitorId(),ticketHolderEmailMap.get(lead.getId()))));

            for(Map.Entry<ExhibitorIdUserEmailDto, List<LeadRetriverData>> entry : exhibitorIdUserIdDtoLeadRetriverDataMap.entrySet()){
                List<LeadRetriverData> getDataFromMap = entry.getValue();
                Optional<LeadRetriverData> leadRetriverDataOptional = getDataFromMap.stream().min(Comparator.comparing(LeadRetriverData :: getCreatedOn));
                leadRetriverDataOptional.ifPresent(leadRetriverDataList::add);
            }

            Page<LeadRetriverData> leadData = PageUtil.getPageable(leadRetriverDataList.stream().sorted(Collections.reverseOrder(Comparator.comparingLong(LeadRetriverData :: getId))).collect(Collectors.toList()), pageable);

            List<Long> staffIds = leadData.getContent().stream().filter(lead -> lead.getStaffId() != null).map(LeadRetriverData::getStaffId).collect(Collectors.toList());
            Map<Long, User> staffMap = new HashMap<>();
            if (!staffIds.isEmpty()) {
                List<Staff> staffList = staffService.findAllStaffByIds(staffIds);
                staffMap.putAll(staffList.stream().collect(Collectors.toMap(Staff::getId, Staff::getUser)));
            }
            List<Long> userIds = leadData.getContent().stream().filter(lead -> lead.getUserId() != null).map(LeadRetriverData::getUserId).collect(Collectors.toList());
            List<String> attendeeIds = userIds.stream().map(ids -> RONeptuneAttendeeDetailService.attendeeId(ids.toString())).collect(Collectors.toList());
            List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAttendees(String.valueOf(event.getEventId()),attendeeIds);


            Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                    .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(), (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));


            Map<Long, Boolean> allowAttendeeToScheduleMeeting = meetingScheduleService.isAllowAttendeeToScheduleMeetingByEventTicketType(event, userIds);

            dataTableResponse.setData(leadData.getContent().stream()
                    .map(leadDetail -> new LeadDetailDto(
                            leadDetail,
                            leadDetail.getStaffId() != null ? staffMap.get(leadDetail.getStaffId()) : null,
                            leadDetail.getUserId() != null ? attendeeMap.get(leadDetail.getUserId()) : null,
                            Collections.emptyList(),
                            prepareLeadSourceSet(leadDetail,exhibitorIdUserIdDtoLeadRetriverDataMap.get(new ExhibitorIdUserEmailDto(leadDetail.getExhibitorId(),ticketHolderEmailMap.get(leadDetail.getId()))), staffMap),
                            allowAttendeeToScheduleMeeting.getOrDefault(leadDetail.getUserId(),false))
                    ).sorted(Collections.reverseOrder(Comparator.comparingLong(LeadDetailDto :: getLeadId))).collect(Collectors.toList()));

            dataTableResponse.setRecordsTotal(leadData.getTotalElements());
            dataTableResponse.setRecordsFiltered(leadData.getContent().size());
        }
        return dataTableResponse;
    }

    @Override
    public void setLeadHolderName(TicketHolderDetailDto leadData, TicketAttributeValueDto1 ticketAttributeValueDto, Map<Long, BasicUserEventBillingDto> mapUserDetails, Long leadUserId,boolean isHolderDataPreference) {
        if (StringUtils.isBlank(leadData.getHolderFirstName()) && StringUtils.isBlank(leadData.getHolderLastName()) && null != ticketAttributeValueDto) {
            String fName = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_FIRST_SPACE_NAME, isHolderDataPreference, true, false);
            String lName = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_LAST_SPACE_NAME, isHolderDataPreference, true, false);
            if (StringUtils.isEmpty(fName) && StringUtils.isEmpty(lName)) {
                if (null != leadUserId && null != mapUserDetails.get(leadUserId) && (!StringUtils.isEmpty(mapUserDetails.get(leadUserId).getFirstName()) || !StringUtils.isEmpty(mapUserDetails.get(leadUserId).getLastName()))) {
                    leadData.setHolderFirstName(mapUserDetails.get(leadUserId).getFirstName());
                    leadData.setHolderLastName(mapUserDetails.get(leadUserId).getLastName());
                } else {
                    leadData.setHolderFirstName(TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_FIRST_SPACE_NAME, false, true, false));
                    leadData.setHolderLastName(TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto, STRING_LAST_SPACE_NAME, false, true, false));
                }
            }
        }
    }

    private Set<String> prepareLeadSourceSet(LeadRetriverData leadDetail, List<LeadRetriverData> leadRetriverDataList, Map<Long, User> staffMap) {
        Set<String> sourceSet = new HashSet<>();
        leadRetriverDataList.remove(leadDetail);
        leadRetriverDataList.forEach(leadRetrieverData -> {
            if (null == leadRetrieverData.getSource()) {
                sourceSet.add(Constants.MANUALLY_CAMELCASE);
            } else {
                switch (leadRetrieverData.getSource()) {
                    case Constants.AUTO_GENERATED_LEAD:
                        sourceSet.add(setAutoGeneratedLeadSource(staffMap, leadRetrieverData));
                        break;
                    case Constants.REQUESTED_A_DEMO:
                        sourceSet.add(Constants.CTA_BUTTON);
                        break;
                    case Constants.VISIT:
                        sourceSet.add(Constants.VISIT_CAMELCASE);
                        break;
                    case Constants.DOCUMENT:
                        sourceSet.add(Constants.DOCUMENT_CAMELCASE);
                        break;
                    case Constants.MANUAL_TRANSACTION_TYPE:
                        sourceSet.add(Constants.MANUALLY_CAMELCASE);
                        break;
                    default:
                        sourceSet.add(leadRetrieverData.getSource());
                        break;
                }
            }
        });
        return sourceSet;
    }

    private String setAutoGeneratedLeadSource(Map<Long, User> staffMap, LeadRetriverData leadRetriverData) {
        String source = Constants.CHAT;
        if (null != leadRetriverData.getStaffId()) {
            FirstLastNameDto firstLastNameDto = buildStaffNameFromStaffUser(staffMap.get(leadRetriverData.getStaffId()));
            if (null != firstLastNameDto) {
                String fullName = (firstLastNameDto.getFirstName().concat(Constants.STRING_BLANK).concat(firstLastNameDto.getLastName())).replace("null", Constants.STRING_EMPTY);
                source = source.concat(" with ").concat(fullName);
            }
        }
        return source.trim();
    }

    private FirstLastNameDto buildStaffNameFromStaffUser(User staffUser) {
        if (null == staffUser) {
            return null;
        }
        return new FirstLastNameDto(staffUser.getFirstName(), staffUser.getLastName());
    }

    private List<LeadRetriverData> getLeadRetriverData(String search, Staff staff,  List<Long> exhibitorIds) {
        if (!exhibitorIds.isEmpty()) {
            if (staff != null &&
                    StaffRole.leadretriever.equals(staff.getRole())
                    && !staff.getExhibitor().isSeeAllLeads()) {
                if (StringUtils.isEmpty(search)) {
                    return leadRetriverDataRepoService.getAllByExhibitorsIdAndStaffId(exhibitorIds, staff.getId());
                } else {
                    return leadRetriverDataRepoService.getAllByExhibitorsIdAndStaffIdAndSearch(exhibitorIds, staff.getId(), search);
                }
            } else {
                if (StringUtils.isEmpty(search)) {
                    return leadRetriverDataRepoService.getAllByExhibitorsId(exhibitorIds);
                } else {
                    return leadRetriverDataRepoService.getAllByExhibitorsIdAndSearch(exhibitorIds, search);
                }
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<LeadRetriverData> getAllLeadsDataByExhibitorId(Long exhibitorId) {
        return leadRetriverDataRepoService.findAllByExhibitorId(exhibitorId);
    }

    @Override
    public List<LeadRetriverData> getAllLeadsDataByExhibitorIdAndStaffId(Long exhibitorId, long staffId) {
        return leadRetriverDataRepoService.findAllByExhibitorIdAndStaffId(exhibitorId, staffId);
    }

    @Override
    public List<ExhibitorsActivity> loggingData(long userId, Event event,long exhibitorId){
        List<ExhibitorsActivity> visitorList = virtualEventLoggingRepository.findByUserIdAndExpoId(userId,exhibitorId);
        visitorList.forEach(v->{
            if(v.getCreatedAt() != null){
                v.setVisited(TimeZoneUtil.getDateInLocal(v.getCreatedAt(),event.getEquivalentTimeZone(), null));
            }
        });
        return visitorList;
    }

    @Override
    public LeadDetailDto getByUserId(Long exhibitorId, long userId, Event event) {
        List<LeadRetriverData> leadRetriverData = leadRetriverDataRepoService.findAllByExhibitorIdAndUserId(exhibitorId, userId);
        LeadDetailDto leadDetailDto;
        if (leadRetriverData != null && !leadRetriverData.isEmpty()) {
            LeadRetriverData leadRetriver = leadRetriverData.get(0);
            log.info("LeadRetrieverData {}, with exhibitor {}, user {} and event {}" , leadRetriver.getId() , exhibitorId , userId , event.getEventId());
            AttendeeProfileDto attendeeProfileDto = null;
            try {
                attendeeProfileDto = attendeeProfileService.getAttendeeProfile(String.valueOf(userId),String.valueOf(event.getEventId()));
            } catch (Exception ex) {
                //DO not remove this try catch as it required to handle exception from attendeeProfileService.getAttendeeProfile
            }
            leadDetailDto = new LeadDetailDto(leadRetriver, leadRetriver.getStaffId() != null ? staffService.findStaff(leadRetriver.getStaffId()) : null, attendeeProfileDto, loggingData(userId, event, exhibitorId));
        } else {
            throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.LEAD_NOT_EXISTS);
        }
        return leadDetailDto;
    }

    @Override
    public LeadDetailDto getTrackingDataByUserId(Long exhibitorId, long userId, Event event) {
        LeadRetriverData leadRetriver = leadRetriverDataRepoService.findFirstByExhibitorIdAndUserId(exhibitorId, userId);
        LeadDetailDto leadDetailDto;
        if (leadRetriver != null ) {
            log.info("LeadRetrieverData {}, with exhibitor {}, user {} and event {}." , leadRetriver.getId() , exhibitorId , userId , event.getEventId());
            AttendeeProfileDto attendeeProfileDto = null;
            try {
                attendeeProfileDto = attendeeProfileService.getAttendeeProfile(String.valueOf(userId),String.valueOf(event.getEventId()));
            } catch (Exception ex) {
                //DO not remove this try catch as it required to handle exception from attendeeProfileService.getAttendeeProfile
            }
            leadDetailDto = new LeadDetailDto(leaderboardService.getUserExpoTrackingData(event, exhibitorId, userId),
                    leadRetriver,
                    leadRetriver.getStaffId() != null ? staffService.findStaff(leadRetriver.getStaffId()) : null,
                    attendeeProfileDto);
            if (null != leadDetailDto.getLeadData() && null != attendeeProfileDto) {
                leadDetailDto.getLeadData().setHolderFirstName(attendeeProfileDto.getFirstName());
                leadDetailDto.getLeadData().setHolderLastName(attendeeProfileDto.getLastName());
            }
        } else {
            throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.LEAD_NOT_EXISTS);
        }
        return leadDetailDto;
    }

    @Override
    public String getLocation(Long eventTicketId, Event event) {
        if (eventTicketId > 0) {
            log.info("LeadRetrieverData event ticket {}", eventTicketId);
            Long orderId = eventTicketsService.getTicketingOrderIdByEventTicketIdAndEvent(eventTicketId, event);
            log.info("LeadRetrieverData event ticket {} with ticketingOrder {}" , eventTicketId , orderId);
            IPLookUpData ipLookUpData = ipLookUpDataService.getIpLookUpDataByOrderId(orderId);
            log.info("IPLookUpData for ticketing order {}" , ipLookUpData);
            return ipLookUpData != null ? ipLookUpData.getCity() + Constants.STRING_COMMA + Constants.STRING_BLANK + ipLookUpData.getCountry() : Constants.STRING_EMPTY;
        }
        return Constants.STRING_EMPTY;
    }

    @Override
    public List<LeadRetriverData> getAllManuallyLeadsByExhibitorId(Long exhibitorId) {
        return leadRetriverDataRepoService.getAllManuallyLeadsByExhibitorId(exhibitorId);
    }

    @Override
    public List<LeadRetriverData> getAllLeadsDataByEventId(long eventId) {
        return leadRetriverDataRepoService.findAllLeadsDataByEventId(eventId);
    }

    @Override
    public void checkUserIsStaffForExhibitorAndThrowError(Long exhibitorId, User user, Event event) {
        UserRole userRole = staffService.getRole(user, event);
        if (Permissions.GtEq_EXHIBITOR_STAFF_LEVEL_PERMISSIONS.contains(userRole)) {
            if (Permissions.EXHIBITOR_STAFF_ONLY_PERMISSIONS.contains(userRole)) {
                Staff staff = staffService.findByUserIdAndExhibitorId(user.getUserId(),exhibitorId);
                if (staff != null) {
                    throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.USER_IS_STAFF_CANNOT_ADDED_AS_LEAD);
                }
            }  else {
                throw new NotAcceptableException(NotAcceptableException.ExhibitorExceptionMsg.USER_IS_STAFF_CANNOT_ADDED_AS_LEAD);
            }

        }
    }

    private void createUserActivity(LeadDetailDto leadDetailDto, Long userId, Event event, Long staffId, Exhibitor exhibitor, String actionType, String source){
        String sourceName = (StringUtils.isNotBlank(source) && (MOBILE_APP.equalsIgnoreCase(source) || MOBILE.equals(source) )) ? "MobileApp" : "Desktop";

        HashMap<String, Object> userActivity = new HashMap<>();
        userActivity.put("staffId", staffId);
        userActivity.put("expoId", exhibitor.getId());
        userActivity.put("isScan", leadDetailDto.isScan());
        
        userActivityService.createUserActivity(userActivity, event.getEventId(), userId, actionType, sourceName, staffId );
    }
}
