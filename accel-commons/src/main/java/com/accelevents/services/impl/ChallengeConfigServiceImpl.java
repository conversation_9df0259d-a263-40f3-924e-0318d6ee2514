package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.enums.ChargebeeEntitlements;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.challenge.UserChallengeRewardsTracker;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.exhibitors.Exhibitor;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.virtual.LeaderboardPointSetting;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.ChallengeAndTierMappingRepository;
import com.accelevents.repositories.ChallengeConfigRepository;
import com.accelevents.repositories.EventChallengeTierRepository;
import com.accelevents.repositories.UserChallengeRewardsTrackerRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.services.*;
import com.accelevents.services.elasticsearch.leaderboard.LeaderboardService;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.repo.helper.*;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.JsonMapper;
import com.accelevents.virtualevents.dto.VirtualEventTabsDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.client.RestHighLevelClient;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigInteger;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.accelevents.continuing.ed.ContinuingEdConstants.REWARD_IMAGE;
import static com.accelevents.domain.enums.DataType.TICKET;
import static com.accelevents.enums.PlanConfigNames.*;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.*;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.ChallengeConstants.*;
import static com.accelevents.utils.Constants.ERROR;
import static com.accelevents.utils.Constants.STRING_COMMA;
import static com.accelevents.utils.GeneralUtils.convertCommaSeparatedToListLong;
import static com.accelevents.utils.GeneralUtils.convertLongListToCommaSeparated;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;

@Service
public class ChallengeConfigServiceImpl implements ChallengeConfigService {

    private static final Logger log = LoggerFactory.getLogger(ChallengeConfigServiceImpl.class);

    @Autowired
    private GamificationCacheStoreService<String,Object> gamificationRedisCache;

    @Autowired
    private ChallengeConfigRepository challengeConfigRepository;
    @Autowired
    private EventChallengeRepoService eventChallengeRepoService;

    @Autowired
    private EventChallengeTierRepository eventChallengeTierRepository;

    @Autowired
    private TicketingHelperService ticketingHelperService;

    @Autowired
    @Lazy
    private TicketingTypeService ticketingTypeService;
    @Autowired
    private ExhibitorRepoService exhibitorRepoService;
    @Autowired
    private LeaderBoarPointSettingRepoService leaderboardPointSettingRepoService;
    @Autowired
    private ROVirtualEventService roVirtualEventService;
    @Lazy
    @Autowired
    private SessionService sessionService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private RestHighLevelClient gamificationESClient;

    @Autowired
    private UserChallengeRewardsTrackerRepository userChallengeRewardsTrackerRepository;

    @Autowired
    private CreditManagementRepoService creditManagementRepoService;
    @Autowired
    private RaffleService raffleService;
    @Lazy
    @Autowired
    LeaderboardService leaderboardService;
    @Autowired
    UserChallengeRewardsTrackerRepoService challengeRewardsTrackerRepoService;

    @Autowired
    private ROEventService roEventService;

    @Autowired
    private EventTicketsService eventTicketsService;

    @Autowired
    private StaffService staffService;

    @Lazy
    @Autowired
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Autowired
    private ChallengeAndTierMappingRepository challengeAndTierMappingRepository;


    @Override
    public ChallengeConfigMasterDTO findJsonValueByArea(String area) {
        ChallengeConfiguration challengeConfiguration = challengeConfigRepository.findChallengeConfigurationByArea(area);
        if(challengeConfiguration ==null){
            throw new NotFoundException(NotFoundException.NotFound.CHALLENGE_CONFIG_NOT_FOUND);
        }
        return new ChallengeConfigMasterDTO(challengeConfiguration);
    }

    @Override
    @Transactional
    public EventChallengeDTO createEventChallenge(Event event, EventChallengeDTO eventChallengeDTO) {
        EventChallengeDetail eventChallengeDetail = eventChallengeRepoService.save(eventChallengeDTO.createEntity(event));
        eventChallengeDTO = setGamificationTabFlagInDTO(eventChallengeDTO,event);
        eventChallengeDTO.setEventChallengeId(eventChallengeDetail.getId());
        eventChallengeDTO.setName(eventChallengeDetail.getChallengeName());
        eventChallengeDTO.setDescription(null);
        eventChallengeDTO.setRewards(null);
        eventChallengeDTO.setTierId(null);
        setChallengeDetailInRedis(eventChallengeDTO, event.getEventId());
        return eventChallengeDTO;
    }

    @Override
    @Transactional
    public EventChallengeDTO createEventChallengeOrThrowError(Event event, EventChallengeDTO eventChallengeDTO) throws JSONException {
        validateChallengeDTO(event, eventChallengeDTO, null);
        if(CollectionUtils.isEmpty(eventChallengeDTO.getActionErrors())
                && CollectionUtils.isEmpty(eventChallengeDTO.getTriggerErrors())
                && CollectionUtils.isEmpty(eventChallengeDTO.getNetworkMaxPointErrors())
                && CollectionUtils.isEmpty(eventChallengeDTO.getInvalidActions())
                && CollectionUtils.isEmpty(eventChallengeDTO.getInvalidTriggers())
                && CollectionUtils.isEmpty(eventChallengeDTO.getInvalidTicketTypes())) {
            return createEventChallenge(event, eventChallengeDTO);
        }
        return eventChallengeDTO;
    }

    private void validateChallengeDTO(Event event, EventChallengeDTO dto, Long challengeId) throws JSONException {
        log.info("Validating challenge DTO of challenge {}", dto.getName());
        if(challengeId == null) {
            validateEarlyBirdChallengeDTO(event, dto);
            validateUniqueName(dto, event.getEventId(), null);
        } else {
            EventChallengeDetail challengeDetail = eventChallengeRepoService.findById(challengeId)
                    .orElseThrow(() -> new NotFoundException(NotFoundException.EventChallengeNotFound.EVENT_CHALLENGE_CONFIG_NOT_FOUND));
            checkChallengeIsEditableOrThrowError(event, challengeId, dto);
            validateUniqueName(dto, event.getEventId(), challengeDetail);
        }
        validateTicketingTypeIds(dto, event);
        validateMasterJSON(dto, event);
        validatePlan(event, dto);
    }

    private void validatePlan(Event event, EventChallengeDTO dto) {
        String message = Constants.GAMIFICATON_VALIDATION_FOR_PLAN.replace("{}",dto.getType().getLabel());
        NotAcceptableException.EventChallengeException eventChallengeException = NotAcceptableException.EventChallengeException.CHALLENGE_NOT_AVALABLE_FOR_PLAN;
        eventChallengeException.setDeveloperMessage(message);
        eventChallengeException.setErrorMessage(message);

        if((!dto.isDefaultChallenge() && dto.getType().equals(ChallengeType.STANDARD) && !chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BASIC_GAMIFICATION))
                || (dto.getType().equals(ChallengeType.EARLY_BIRD)&& !chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.CUSTOM_GAMIFICATION))){
            throw new NotAcceptableException(eventChallengeException);
        }
    }

    private void validateTicketingTypeIds(EventChallengeDTO challengeDTO, Event event) {
        log.info("Validating ticket types of challenge {}", challengeDTO.getName());
        ArrayList<String> errors = new ArrayList<>();
        List<Long> selectedTicketTypes = challengeDTO.getTicketTypeAllowInChallenge();
        if (CollectionUtils.isEmpty(selectedTicketTypes)) {
            errors.add("Please select ticket type for challenge");
            return;
        }
        List<TicketingType> tickets = ticketingTypeService.findAllByEventIdHost(event, false, TICKET);
        Map<Long, TicketingType> ticketMap = tickets.stream().collect(Collectors.toMap(ticket -> ticket.getId(), ticket -> ticket));
        selectedTicketTypes.forEach(ticket -> {
            TicketingType ticketingType = ticketMap.get(ticket);
            if (ticketingType == null) {
                errors.add("Not a valid ticket type id " + ticket);
            } else if (ticketingType.getTicketType().equals(TicketType.DONATION)) {
                errors.add("Donation ticket (" + ticketingType.getTicketTypeName() + ") is not allowed to save in challenge");
            }
            /* TODO: Remove below comment code once we test in person ticketing type in Dev or if we get enhancement request
            else if (TicketTypeFormat.IN_PERSON.equals(ticketingType.getTicketTypeFormat()) && !ChallengeType.EARLY_BIRD.equals(challengeDTO.getType())) {
                errors.add("In person ticket (" + ticketingType.getTicketTypeName() + ") is not allowed to save in challenge");
            }
             */
        });
        challengeDTO.setInvalidTicketTypes(errors);
    }

    private void validateMasterJSON(EventChallengeDTO eventChallengeDTO, Event event){
        log.info("Validating challenge {} structure as per master json", eventChallengeDTO.getName());
        String area = eventChallengeDTO.getArea().trim();
        Map<String, List<Map<String,String>>> actionErrors = new HashMap<>();
        List<Map<String,String>> triggerErrors = new ArrayList<>();
        List<Map<String,String>> networkMaxPointsErrors = new ArrayList<>();
        List<Map<String,String>> invalidActions = new ArrayList<>();
        List<Map<String,String>> invalidTriggers = new ArrayList<>();

        ChallengeConfigMasterDTO challengeConfigMasterDTO =  findJsonValueByArea(area);

        switch(area){
            case AREA.EXPO:
                validateExpo(challengeConfigMasterDTO,eventChallengeDTO,actionErrors,triggerErrors,invalidActions);
                break;
            case AREA.SESSIONS:
                validateSession(challengeConfigMasterDTO, eventChallengeDTO, actionErrors, triggerErrors, invalidActions, invalidTriggers, event);
                break;
            case AREA.NETWORKING:
                validateNetwork(challengeConfigMasterDTO,eventChallengeDTO,actionErrors,triggerErrors,networkMaxPointsErrors,invalidActions);
                break;
            case AREA.EVENT:
                validateEvent(challengeConfigMasterDTO,eventChallengeDTO,actionErrors,invalidActions);
                break;
            case AREA.QR_SCAN:
                validateQRScan(challengeConfigMasterDTO,eventChallengeDTO,actionErrors,invalidActions);
                break;
            case AREA.PROFILE:
                validateProfile(challengeConfigMasterDTO,eventChallengeDTO,actionErrors,invalidActions);
                break;
            case AREA.SURVEY:
                validateSurvey(challengeConfigMasterDTO, eventChallengeDTO, actionErrors, triggerErrors, invalidActions, invalidTriggers);
                break;
            default:
                throw new NotAcceptableException(Constants.NEON.NOT_ACCEPTABLE_ERROR_CODE, "Invalid Area of the challenge", "Invalid Area of the challenge");
        }
        if(!CollectionUtils.isEmpty(actionErrors)){
            eventChallengeDTO.setActionErrors(actionErrors);
        }
        if(!CollectionUtils.isEmpty(triggerErrors)) {
            eventChallengeDTO.setTriggerErrors(triggerErrors);
        }
        if(!CollectionUtils.isEmpty(networkMaxPointsErrors)) {
            eventChallengeDTO.setNetworkMaxPointErrors(networkMaxPointsErrors);
        }
        if (!CollectionUtils.isEmpty(invalidActions)) {
            eventChallengeDTO.setInvalidActions(invalidActions);
        }
        if (!CollectionUtils.isEmpty(invalidTriggers)) {
            eventChallengeDTO.setInvalidTriggers(invalidTriggers);
        }
    }

    private void validateProfile(ChallengeConfigMasterDTO challengeConfigMasterDTO, EventChallengeDTO eventChallengeDTO, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions) {
        validateProfileAction(challengeConfigMasterDTO.getAction(), eventChallengeDTO.getAction(),actionErrors, invalidActions);
    }

    private void validateProfileAction(List<Map<String, Object>> actionConfig, List<Map<String, Object>> actions, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions) {
        for (Map<String, Object> action : actions) {
            String actionName = String.valueOf(action.get(ChallengeConstants.NAME));
            List<Map<String, Object>> configList = actionConfig.stream().filter(s -> String.valueOf(s.get(ChallengeConstants.NAME)).equalsIgnoreCase(actionName)).collect(Collectors.toList());
            if (!configList.isEmpty()) {
                Map<String, Object> config = configList.get(0);
                switch (actionName) {
                    case ACTION_USER_CONNECTION:
                        validateOnlyPointField((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    case ACTION_COMPLETE_PROFILE:
                        validateOnlyPointField((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    default:
                        invalidActions.add(new HashMap<String,String>(){{
                            put(ELEMENT,actionName);
                            put(ERROR,Constants.INVAILD_ACTION+actionName+" of Profile Area");
                        }});
                }
            }
            else{
                invalidActions.add(new HashMap<String,String>(){{
                    put(ELEMENT,actionName);
                    put(ERROR,Constants.INVAILD_ACTION+actionName+" of Profile Area");
                }});
            }
        }
    }

    //Expo Area Validation
    private void validateExpo(ChallengeConfigMasterDTO challengeConfigMasterDTO, EventChallengeDTO eventChallengeDTO, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> triggerErrors, List<Map<String,String>> invalidActions) {
        validateExpoAction(challengeConfigMasterDTO.getAction(), eventChallengeDTO.getAction(),actionErrors,invalidActions);
        validateExpoTrigger(eventChallengeDTO.getTrigger(),triggerErrors);
    }

    private void validateExpoAction(List<Map<String, Object>> actionConfig, List<Map<String, Object>> actions, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions){

        for (Map<String, Object> action : actions) {
            String actionName = String.valueOf(action.get(ChallengeConstants.NAME));
            List<Map<String, Object>> configList = actionConfig.stream().filter(s -> String.valueOf(s.get(ChallengeConstants.NAME)).equalsIgnoreCase(actionName)).collect(Collectors.toList());

            if (!configList.isEmpty()) {
                Map<String, Object> config = configList.get(0);
                switch (actionName) {
                    case ACTION_VISIT:
                        validateExpoActionVisit((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    case ACTION_WATCH:
                        validateExpoActionWatch((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    case ACTION_CHAT:
                    case ACTION_LINK_CLICK:
                    case ACTION_DOCUMENT_DOWNLOAD:
                    case LEAD_CAPTURE:
                    case ACTION_REQUEST_MEETING:
                        validateOnlyPointField((List<Map<String, Object>>) config.get(Constants.CONFIG), action, actionErrors,actionName);
                        break;
                    default:
                        invalidActions.add(new HashMap<String,String>(){{
                            put(ELEMENT,actionName);
                            put(ERROR,Constants.INVAILD_ACTION+actionName+" of Expo Area");
                        }});
                }
            }
            else{
                invalidActions.add(new HashMap<String,String>(){{
                    put(ELEMENT,actionName);
                    put(ERROR,Constants.INVAILD_ACTION+actionName+" of Expo Area");
                }});
            }
        }
    }

    private void validateExpoActionVisit(List<Map<String, Object>> config, Map<String, Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){

        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            switch (String.valueOf(configRules.get(ELEMENT))) {
                case POINT:
                case Constants.MIN_VISIT_DURATION:
                case Constants.PER_VISIT_POINT:
                    Integer fieldValue = null;
                    try {
                        fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                    }catch (NumberFormatException e){
                        log.info("Validate Point, NumberFormatException event");
                    }
                    validateField(String.valueOf(configRules.get(ELEMENT)), fieldValue, minValue, MAX_VALUE, actionErrors,actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }

    private void validateExpoActionWatch(List<Map<String, Object>> config, Map<String, Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){

        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            switch (String.valueOf(configRules.get(ELEMENT))) {
                case POINT:
                case MINIMUM_WATCH_DURATION:
                    Integer fieldValue = null;
                    try {
                        fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                    }catch (NumberFormatException e){
                        log.info("Validate number, NumberFormatException event");
                    }
                    validateField(String.valueOf(configRules.get(ELEMENT)), fieldValue, minValue, MAX_VALUE, actionErrors,actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }

    private void validateOnlyPointField(List<Map<String, Object>> config, Map<String, Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){

        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            switch (String.valueOf(configRules.get(ELEMENT))) {
                case POINT:
                    Integer fieldValue = null;
                    try {
                        fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                    }catch (NumberFormatException e){
                       log.info("Validate Point, NumberFormatException event");
                    }
                    validateField(String.valueOf(configRules.get(ELEMENT)),fieldValue , minValue, MAX_VALUE, actionErrors,actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }


    private void validateExpoTrigger(Map<String, List<Long>> actions, List<Map<String, String>> triggerErrors){
        actions.forEach((k,value)->{
            if (value == null || value.isEmpty()) {
                HashMap<String, String> error = new HashMap<>();
                error.put(ELEMENT, k);
                error.put(ERROR, Constants.YOU_CAN_NOT_ADD_EMPTY + ((k.equalsIgnoreCase(Constants.EXHIBITOR.toLowerCase())) ? Constants.EXHIBITOR : "Category") + Constants.LIST);
                triggerErrors.add(error);
            }
        });
    }

    //Session Area Validation
    private void validateSession(ChallengeConfigMasterDTO challengeConfigMasterDTO, EventChallengeDTO eventChallengeDTO, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> triggerErrors, List<Map<String, String>> invalidActions, List<Map<String, String>> invalidTriggers, Event event) {
        validateSessionAction(challengeConfigMasterDTO.getAction(), eventChallengeDTO.getAction(), actionErrors, invalidActions);
        validateSessionTrigger(challengeConfigMasterDTO.getTrigger(), eventChallengeDTO.getTrigger(), triggerErrors, invalidTriggers);
    }

    private void validateSessionAction(List<Map<String, Object>> actionConfig, List<Map<String, Object>> actions, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions){
        for(int index = 0; index<actions.size(); index++){
            Map<String,Object> action = actions.get(index);
            String actionName = String.valueOf(action.get(ChallengeConstants.NAME));
            List<Map<String,Object>> configList = actionConfig.stream().filter(s->String.valueOf(s.get(ChallengeConstants.NAME)).equalsIgnoreCase(actionName)).collect(Collectors.toList());

            if(configList.size() > 0){
                Map<String,Object> config = configList.get(0);
                switch (actionName){
                    case ACTION_VISIT:
                        validateSessionActionVisit((List<Map<String, Object>>) config.get(Constants.CONFIG),action, actionErrors, actionName);
                        break;
                    case ACTION_WATCH:
                        validateSessionActionWatch((List<Map<String, Object>>) config.get(Constants.CONFIG),action, actionErrors, actionName);
                        break;
                    case ACTION_CHAT:
                    case ACTION_ASKQUESTION:
                    case ACTION_POLL:
                        validateOnlyPointField((List<Map<String, Object>>) config.get(Constants.CONFIG),action, actionErrors, actionName);
                        break;
                    default:
                        invalidActions.add(new HashMap<String,String>(){{
                            put(ELEMENT,actionName);
                            put(ERROR,Constants.INVAILD_ACTION+actionName+" of Session Area");
                        }});
                }
            }else{
                invalidActions.add(new HashMap<String,String>(){{
                    put(ELEMENT,actionName);
                    put(ERROR,Constants.INVAILD_ACTION+actionName+" of Session Area");
                }});
            }
        }
    }

    private void validateSessionActionVisit(List<Map<String,Object>> config, Map<String,Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){
        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            switch (String.valueOf(configRules.get(ELEMENT))) {
                case POINT:
                case MIN_VISIT_DURATION:
                case PER_VISIT_POINT:
                    Integer fieldValue = null;
                    try {
                        fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                    }catch (NumberFormatException e){
                        log.info("Validate Point, NumberFormatException event");
                    }
                    validateField(String.valueOf(configRules.get(ELEMENT)), fieldValue, minValue, MAX_VALUE, actionErrors, actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }

    private void validateSessionActionWatch(List<Map<String,Object>> config, Map<String,Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){

        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            String field = String.valueOf(configRules.get(ELEMENT));
            switch (field) {
                case POINT:
                case MINIMUM_WATCH_DURATION:
                case RECORDING_WATCH_POINT:
                case MINIMUM_RECORDING_WATCH_DURATION:
                    String fieldValueStr = String.valueOf(action.get(field));
                    Integer fieldValue = StringUtils.isNumeric(fieldValueStr) ? Integer.parseInt(fieldValueStr): null;
                    validateField(field, fieldValue, minValue, MAX_VALUE, actionErrors, actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }

    private void validateSessionTrigger(List<Map<String, Object>> triggerConfig, Map<String, List<Long>> challengeTrigger, List<Map<String, String>> triggerErrors, List<Map<String, String>> invalidTriggers) {
        challengeTrigger.forEach((triggerName, triggerIds) -> {
            Optional<Map<String, Object>> triggerConfigData = triggerConfig.stream().filter(e -> triggerName.equals(e.get(ELEMENT))).findAny();
            if (triggerConfigData.isPresent()) {
                if (CollectionUtils.isEmpty(triggerIds)) {
                    String sessionType = getSessionTypeName(triggerName);
                    HashMap<String, String> error = new HashMap<>();
                    error.put(ELEMENT, triggerName);
                    error.put(ERROR, Constants.YOU_CAN_NOT_ADD_EMPTY + sessionType + Constants.LIST);
                    triggerErrors.add(error);
                }
            } else {
                HashMap<String, String> error = new HashMap<>();
                error.put(ELEMENT, triggerName);
                error.put(ERROR, Constants.INVALID_TRIGGER + triggerName + Constants.OF_SESSION_AREA);
                invalidTriggers.add(error);
            }
        });
    }

    private String getSessionTypeName(String triggerName) {
        if (RedisChallengeAreaDTO.Sessions.main_stage.name().equalsIgnoreCase(triggerName)) {
            return Constants.MAIN_STAGE_SESSION;
        } else if (RedisChallengeAreaDTO.Sessions.breakout_session.name().equalsIgnoreCase(triggerName)) {
            return Constants.REGULAR_SESSIONS;
        } else if (RedisChallengeAreaDTO.Sessions.workshop.name().equalsIgnoreCase(triggerName)) {
            return Constants.WORKSHOP;
        } else {
            return null;
        }
    }


    //Network Area Validation
    private void validateNetwork(ChallengeConfigMasterDTO challengeConfigMasterDTO, EventChallengeDTO eventChallengeDTO, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> triggerErrors, List<Map<String, String>> networkMaxPointErrors, List<Map<String,String>> invalidActions) {
        validateNetworkAction(challengeConfigMasterDTO.getAction(), eventChallengeDTO.getAction(),actionErrors, invalidActions);
        validateMaxPoint(eventChallengeDTO.getMaxPoints(), networkMaxPointErrors);
        validateNetworkTrigger(eventChallengeDTO.getTrigger(),triggerErrors);
    }
    private void validateNetworkAction(List<Map<String, Object>> actionConfig, List<Map<String, Object>> actions, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions){
        for (Map<String, Object> action : actions) {
            String actionName = String.valueOf(action.get(ChallengeConstants.NAME));
            List<Map<String, Object>> configList = actionConfig.stream().filter(s -> String.valueOf(s.get(ChallengeConstants.NAME)).equalsIgnoreCase(actionName)).collect(Collectors.toList());
            if (configList.size() > 0) {
                Map<String, Object> config = configList.get(0);
                switch (actionName) {
                    case ACTION_MATCHES:
                        validateOnlyPointField((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    case ACTION_CONNECTIONS:
                        validateNetworkActionConnections((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    default:
                        invalidActions.add(new HashMap<String,String>(){{
                            put(ELEMENT,actionName);
                            put(ERROR,Constants.INVAILD_ACTION+actionName+" of Network Area");
                        }});
                }
            }
            else{
                invalidActions.add(new HashMap<String,String>(){{
                    put(ELEMENT,actionName);
                    put(ERROR,Constants.INVAILD_ACTION+actionName+" of Network Area");
                }});
            }
        }
    }

    private void validateSurvey(ChallengeConfigMasterDTO challengeConfigMasterDTO, EventChallengeDTO eventChallengeDTO, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> triggerErrors, List<Map<String, String>> invalidActions, List<Map<String, String>> invalidTriggers){
        validateSurveyAction(challengeConfigMasterDTO.getAction(), eventChallengeDTO.getAction(), actionErrors, invalidActions);
        validateSurveyTrigger(challengeConfigMasterDTO.getTrigger(), eventChallengeDTO.getTrigger(), triggerErrors, invalidTriggers);
    }

    private void validateSurveyAction(List<Map<String, Object>> actionConfig, List<Map<String, Object>> actions, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions){
        for(int index = 0; index<actions.size(); index++){
            Map<String,Object> action = actions.get(index);
            String actionName = String.valueOf(action.get(ChallengeConstants.NAME));
            List<Map<String,Object>> configList = actionConfig.stream().filter(s->String.valueOf(s.get(ChallengeConstants.NAME)).equalsIgnoreCase(actionName)).collect(Collectors.toList());

            if (!configList.isEmpty()) {
                Map<String, Object> config = configList.get(0);
                if (ACTION_SUBMIT.equals(actionName)) {
                    validateSurveyActionSubmit(
                            (List<Map<String, Object>>) config.get(Constants.CONFIG),
                            action,
                            actionErrors,
                            actionName
                    );
                } else {
                    Map<String, String> invalidAction = new HashMap<>();
                    invalidAction.put(ELEMENT, actionName);
                    invalidAction.put(ERROR, String.format(Constants.INVALID_ACTION_MESSAGE_SURVEY,actionName));
                    invalidActions.add(invalidAction);
                }
            } else {
                Map<String, String> invalidAction = new HashMap<>();
                invalidAction.put(ELEMENT, actionName);
                invalidAction.put(ERROR, String.format(Constants.INVALID_ACTION_MESSAGE_SURVEY,actionName));
                invalidActions.add(invalidAction);
            }
        }
    }

    private void validateSurveyActionSubmit(List<Map<String,Object>> config, Map<String,Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){
        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            String field = String.valueOf(configRules.get(ELEMENT));
            if (POINT.equals(field)) {
                Integer fieldValue = null;
                try {
                    fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                }catch (NumberFormatException e){
                    log.info("Validate Point, NumberFormatException event");
                }
                validateField(String.valueOf(configRules.get(ELEMENT)), fieldValue, minValue, MAX_VALUE, actionErrors, actionName, errorMessage);
            } else {
                return;
            }
        }
    }

    private void validateSurveyTrigger(List<Map<String, Object>> triggerConfig, Map<String, List<Long>> challengeTrigger, List<Map<String, String>> triggerErrors, List<Map<String, String>> invalidTriggers) {
        challengeTrigger.forEach((triggerName, triggerIds) -> {
            Optional<Map<String, Object>> triggerConfigData = triggerConfig.stream().filter(e -> triggerName.equals(e.get(ELEMENT))).findAny();
            if (triggerConfigData.isPresent()) {
                if (CollectionUtils.isEmpty(triggerIds)) {
                    HashMap<String, String> error = new HashMap<>();
                    error.put(ELEMENT, triggerName);
                    error.put(ERROR, String.format(Constants.YOU_CAN_NOT_ADD_EMPTY_LIST,AREA.SURVEY));
                    triggerErrors.add(error);
                }
            } else {
                HashMap<String, String> error = new HashMap<>();
                error.put(ELEMENT, triggerName);
                error.put(ERROR, String.format(Constants.INVALID_TRIGGER_MESSAGE_SURVEY,triggerName));
                invalidTriggers.add(error);
            }
        });
    }


    private void validateNetworkActionConnections(List<Map<String,Object>> config, Map<String,Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){
        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            switch (String.valueOf(configRules.get(ELEMENT))) {
                case SENDER_POINT:
                case RECEIVER_POINT:
                    Integer fieldValue = null;
                    try {
                        fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                    }catch (NumberFormatException e){
                        log.info("Validate Point, NumberFormatException event");
                    }
                    validateField(String.valueOf(configRules.get(ELEMENT)), fieldValue, minValue, MAX_VALUE, actionErrors,actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }

    private void validateNetworkTrigger(Map<String, List<Long>> actions, List<Map<String, String>> triggerErrors){
        actions.forEach((k,value)->{
            if(value == null || value.isEmpty()){
                triggerErrors.add(new HashMap<String,String>(){{
                    put(ELEMENT,k);
                    put(ERROR,"You can not add empty Networking Session List");
                }});
            }
        });
    }

    private void validateMaxPoint(Map<String, Object> maxPoint, List<Map<String, String>> networkMaxPointErrors){
        maxPoint.forEach((k,value)->{
            if((k.equalsIgnoreCase(POINT))
                    && (value == null || Integer.parseInt(String.valueOf(value)) < 1 || Integer.parseInt(String.valueOf(value)) > MAX_VALUE)){

                networkMaxPointErrors.add(new HashMap<String, String>(){{
                    put(ELEMENT,k);
                    put(ERROR,"Maximum Points per Networking Session should not be less than 0 and not greater than " + MAX_VALUE);
                }});
            }
        });
    }


    private void validateQRScan(ChallengeConfigMasterDTO challengeConfigMasterDTO, EventChallengeDTO eventChallengeDTO, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions) {
        validateQRScanAction(challengeConfigMasterDTO.getAction(), eventChallengeDTO.getAction(),actionErrors, invalidActions);
    }

    private void validateQRScanAction(List<Map<String, Object>> actionConfig, List<Map<String, Object>> actions, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions) {
        for (Map<String, Object> action : actions) {
            String actionName = String.valueOf(action.get(ChallengeConstants.NAME));
            List<Map<String, Object>> configList = actionConfig.stream().filter(s -> String.valueOf(s.get(ChallengeConstants.NAME)).equalsIgnoreCase(actionName)).collect(Collectors.toList());
            if (!configList.isEmpty()) {
                Map<String, Object> config = configList.get(0);
                switch (actionName) {
                    case QRSCAN:
                        validateOnlyPointField((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    default:
                        invalidActions.add(new HashMap<String,String>(){{
                            put(ELEMENT,actionName);
                            put(ERROR,Constants.INVAILD_ACTION+actionName+" of QR scan Area");
                        }});
                }
            }
            else{
                invalidActions.add(new HashMap<String,String>(){{
                    put(ELEMENT,actionName);
                    put(ERROR,Constants.INVAILD_ACTION+actionName+" of QR scan Area");
                }});
            }
        }

    }

    //Network Event Validation
    private void validateEvent(ChallengeConfigMasterDTO challengeConfigMasterDTO, EventChallengeDTO eventChallengeDTO, Map<String, List<Map<String, String>>> actionErrors, List<Map<String,String>> invalidActions) {
        validateEventAction(challengeConfigMasterDTO.getAction(), eventChallengeDTO.getAction(),actionErrors, invalidActions);
    }
    private void validateEventAction(List<Map<String, Object>> actionConfig, List<Map<String, Object>> actions, Map<String, List<Map<String, String>>> actionErrors, List<Map<String, String>> invalidActions){
        for (Map<String, Object> action : actions) {
            String actionName = String.valueOf(action.get(ChallengeConstants.NAME));
            List<Map<String, Object>> configList = actionConfig.stream().filter(s -> String.valueOf(s.get(ChallengeConstants.NAME)).equalsIgnoreCase(actionName)).collect(Collectors.toList());
            if (configList.size() > 0) {
                Map<String, Object> config = configList.get(0);
                switch (actionName) {
                    case TICKET_PURCHASE:
                        validateEventTicketPurchase((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    case CHECK_IN:
                        validateEventCheckin((List<Map<String, Object>>) config.get(Constants.CONFIG), action,actionErrors,actionName);
                        break;
                    default:
                        invalidActions.add(new HashMap<String,String>(){{
                            put(ELEMENT,actionName);
                            put(ERROR,Constants.INVAILD_ACTION+actionName+" of Event Area");
                        }});
                }
            }
            else{
                invalidActions.add(new HashMap<String,String>(){{
                    put(ELEMENT,actionName);
                    put(ERROR,Constants.INVAILD_ACTION+actionName+" of Event Area");
                }});
            }
        }
    }

    private void validateEventTicketPurchase(List<Map<String,Object>> config, Map<String,Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){
        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            switch (String.valueOf(configRules.get(ELEMENT))) {
                case POINT:
                case USERS:
                    Integer fieldValue = null;
                    try {
                        fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                    }catch (NumberFormatException e){
                        log.info("Validate Point, NumberFormatException event");
                    }
                    validateField(String.valueOf(configRules.get(ELEMENT)), fieldValue, minValue, MAX_VALUE, actionErrors,actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }

    private void validateEventCheckin(List<Map<String,Object>> config, Map<String,Object> action, Map<String, List<Map<String, String>>> actionErrors, String actionName){
        for (Map<String, Object> configRules : config) {
            int minValue = (configRules.get(ELEMENT_MIN_VALUE)) != null ? Integer.parseInt(String.valueOf(configRules.get(ELEMENT_MIN_VALUE))) : 1;
            String errorMessage = configRules.get(LABEL) + Constants.SHOULD_NOT_BE_LESS_THAN + minValue + Constants.NOT_GREATER_THAN + MAX_VALUE;
            switch (String.valueOf(configRules.get(ELEMENT))) {
                case POINT:
                case USERS:
                    Integer fieldValue = null;
                    try {
                        fieldValue = Integer.parseInt(String.valueOf(action.get(String.valueOf(configRules.get(ELEMENT)))));
                    }catch (NumberFormatException e){
                        log.info("Validate Point, NumberFormatException event");
                    }
                    validateField(String.valueOf(configRules.get(ELEMENT)), fieldValue, minValue, MAX_VALUE, actionErrors,actionName, errorMessage);
                    break;
                default:
                    return;
            }
        }
    }

    private void validateField(String field, Integer fieldValue, int minValue, int maxValue, Map<String, List<Map<String,String>>> actionErrors,String actionName, String errorMessae){

        if (fieldValue == null || fieldValue < minValue || fieldValue > maxValue) {
            actionErrors.putIfAbsent(actionName, new ArrayList<>());
            List<Map<String, String>> actionError = actionErrors.get(actionName);
            actionError.add(new HashMap<String,String>(){{
                put(ELEMENT,field);
                put(ERROR,errorMessae);
            }});
        }
    }

    private void validateUniqueName(EventChallengeDTO eventChallengeDTO, Long eventId, EventChallengeDetail eventChallengeDetail) {
        log.info("Validating if name of challenge {} is unique", eventChallengeDTO.getName());
        if ((null == eventChallengeDetail && eventChallengeRepoService.existsByEventIdAndChallengeNameIgnoreCase(eventId,eventChallengeDTO.getName().trim()) )
                || (eventChallengeDetail != null  && eventChallengeRepoService.existsByEventIdAndChallengeNameIgnoreCaseAndIdNot(eventId,eventChallengeDTO.getName().trim(),eventChallengeDetail.getId()))) {
            throw  new NotAcceptableException(NotAcceptableException.EventChallengeException.CHALLENGE_NAME_EXCEPTION);
        }
        log.info("Name of challenge {} is unique", eventChallengeDTO.getName());
    }


    private void validateEarlyBirdChallengeDTO(Event event, EventChallengeDTO eventChallengeDTO) throws JSONException {
        if(ChallengeType.EARLY_BIRD.equals(eventChallengeDTO.getType())) {
            List<EventChallengeDetail> challengeList = findChallengesByEventIdAndType(event.getEventId(), ChallengeType.EARLY_BIRD);
            log.info("Fetched event early bird challenges size {}, event {}", challengeList.size(), event.getEventId());
            isEarlyBirdChallengeAlreadyExists(event, getActionNameFromChallengeDTO(eventChallengeDTO), challengeList);
        }
    }

    private String getActionNameFromChallengeDTO(EventChallengeDTO eventChallengeDTO)  {
        List<Map<String, Object>> actions = eventChallengeDTO.getAction();
        if(!CollectionUtils.isEmpty(actions) && !MapUtils.isEmpty(actions.get(0))) {
            Map<String, Object> action = actions.get(0);
            return String.valueOf(action.get(ChallengeConstants.NAME));
        }
        return "";
    }

    private void isEarlyBirdChallengeAlreadyExists(Event event, String actionName, List<EventChallengeDetail> challengeList) throws JSONException {
        if(!CollectionUtils.isEmpty(challengeList)) {
            for (EventChallengeDetail challenge : challengeList) {
                String json = challenge.getJsonValue();
                JSONObject jsonObject = new JSONObject(json);
                if (jsonObject.has(ChallengeConstants.ACTION)) {
                    checkActionNameIsExistsOrNot(event, actionName, jsonObject);
                }
            }
        }
    }

    private void checkActionNameIsExistsOrNot(Event event, String actionName, JSONObject jsonObject) throws JSONException {
        JSONArray actions = jsonObject.getJSONArray(ChallengeConstants.ACTION);
        if(actions.length() > 0) {
            JSONObject action = actions.getJSONObject(0);
            if (action != null && action.has(ChallengeConstants.NAME) && actionName.equals(action.get(ChallengeConstants.NAME))) {
                log.info("Early bird challenge already exists actionName {}, event {}", actionName, event.getEventId());
                throw new NotAcceptableException(NotAcceptableException.ChallengeExceptionMsg.EARLY_BIRD_CHALLENGE_ALREADY_EXISTS);
            }
        }
    }

    /**
     * This method will create random scavenger hunt challenge for given event
     * Area and Triggers will be selected randomly
     * @param event
     * @return
     */
    @Override
    @Transactional
    public EventChallengeDTO generateRandomScavengerHuntChallenge(Event event) throws NoSuchAlgorithmException {
        log.info("Start Generate random scavenger hunt challenge event {}", event.getEventId());
        String planName = eventPlanConfigService.getEventPlanName(event.getEventId());
        long challengeCount = eventChallengeRepoService.countByEventIdAndType(event.getEventId(), ChallengeType.SCAVENGER_HUNT);
        if ((FREE_PLAN.getName().equals(planName) || STARTER.getName().equals(planName) || SCALE_PLAN.getName().equals(planName) || SINGLE_EVENT_UNIT.getName().equals(planName) || SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(planName)) && challengeCount > 0) {
            log.info("Free and Starter,Scale plan cannot create multiple scavenger hunt challenge event {}, plan {}", event.getEventId(), planName);
            throw new NotAcceptableException(NotAcceptableException.ChallengeExceptionMsg.NOT_ALLOW_TO_CREATE_MULTIPLE_SCAVENGER_HUNT_CHALLENGE);
        }
        List<Long> allowedTicketTypes = ticketingTypeService.findAllIdByEventAndDataTypeAndExcludeFormatAndDonation(event, DataType.TICKET);
        if (CollectionUtils.isEmpty(allowedTicketTypes)) {
            throw new NotAcceptableException(NotAcceptableException.ChallengeExceptionMsg.TICKET_TYPES_NOT_FOUND);
        }

        String newChallengeName = getNextScavengerNameChallenge(event.getEventId());

        List<String> areas = new ArrayList<>();

        List<Long> exhibitorIds = getExhibitorIdByEventId(event.getEventId());
        if (!CollectionUtils.isEmpty(exhibitorIds)) {
            areas.add(AREA.EXPO);
        }
        List<Session> sessions = sessionService.findSessionByEventId(event);
        if (!CollectionUtils.isEmpty(sessions)) {
            areas.add(AREA.SESSIONS);
        }
        List<Session> networkingSessions = sessions.stream().filter(session -> EnumSessionFormat.MEET_UP.equals(session.getFormat())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(networkingSessions)) {
            areas.add(AREA.NETWORKING);
        }
        log.info("Available areas for create scavenger hunt challenge areas {}", areas);

        if (CollectionUtils.isEmpty(areas)) {
            log.info("Not found enough exhibitors, sessions, and networking sessions to create a Scavenger hunt challenge event {}, plan {}", event.getEventId(), planName);
            throw new NotAcceptableException(NotAcceptableException.ChallengeExceptionMsg.EXHIBITOR_AND_SESSION_NOT_FOUND);
        }

        Random random = SecureRandom.getInstanceStrong();
        String randomArea = areas.get(random.nextInt(areas.size()));
        log.info("Random area selected for event {}, randomArea {} ", event.getEventId(), randomArea);

        Map<String, List<Long>> trigger = getRandomTriggersByArea(randomArea, exhibitorIds, sessions, networkingSessions);
        log.info("Random triggers selected for event {}", event.getEventId());

        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);

        EventChallengeDTO eventChallengeDTO = EventChallengeDTO.prepareScavengerChallengeDTO(event, ticketing, allowedTicketTypes, randomArea, trigger, newChallengeName);
        createEventChallenge(event, eventChallengeDTO);
        log.info("End Successfully generated scavenger hunt challenge event {}, challenge {}", event.getEventId(), eventChallengeDTO.getEventChallengeId());

        return eventChallengeDTO;
    }

    /**
     * This method will select random exhibitors and sessions from given exhibitors and sessions and return trigger map
     * @param area the area of which triggers needs to be create
     * @param exhibitorIds all exhibitors ids from event
     * @param sessions all sessions from event
     * @param networkingSessions all networking session from event
     * @return
     */
    private Map<String, List<Long>> getRandomTriggersByArea(String area,
                                                            List<Long> exhibitorIds,
                                                            List<Session> sessions,
                                                            List<Session> networkingSessions) {
        Map<String, List<Long>> randomTriggers = new HashMap<>();

        if (AREA.EXPO.equals(area)) {
            List<Long> randomExhibitorIds = GeneralUtils.pickNRandomElements(exhibitorIds, (int) Math.ceil((double) exhibitorIds.size() / 2));
            randomTriggers.put(RedisChallengeAreaDTO.Expo.exhibitor.name(), randomExhibitorIds);
        } else if (AREA.SESSIONS.equals(area)) {
            List<Session> mainStageSessions = sessions.stream().filter(session -> EnumSessionFormat.MAIN_STAGE.equals(session.getFormat())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(mainStageSessions)) {
                List<Session> randomMainStageSessions = GeneralUtils.pickNRandomElements(mainStageSessions, (int) Math.ceil((double) mainStageSessions.size() / 2));
                List<Long> mainStageSessionIds = randomMainStageSessions.stream().map(Session::getId).collect(Collectors.toList());
                randomTriggers.put(RedisChallengeAreaDTO.Sessions.main_stage.name(), mainStageSessionIds);
            }

            List<Session> breakoutSessions = sessions.stream().filter(session -> EnumSessionFormat.BREAKOUT_SESSION.equals(session.getFormat())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(breakoutSessions)) {
                List<Session> randomBreakoutSessions = GeneralUtils.pickNRandomElements(breakoutSessions, (int) Math.ceil((double) breakoutSessions.size() / 2));
                List<Long> breakOutSessionIds = randomBreakoutSessions.stream().map(Session::getId).collect(Collectors.toList());
                randomTriggers.put(RedisChallengeAreaDTO.Sessions.breakout_session.name(), breakOutSessionIds);
            }

            List<Session> workshopSessions = sessions.stream().filter(session -> EnumSessionFormat.WORKSHOP.equals(session.getFormat())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(workshopSessions)) {
                List<Session> randomWorkshopSessions = GeneralUtils.pickNRandomElements(workshopSessions, (int) Math.ceil((double) workshopSessions.size() / 2));
                List<Long> workshopSessionIds = randomWorkshopSessions.stream().map(Session::getId).collect(Collectors.toList());
                randomTriggers.put(RedisChallengeAreaDTO.Sessions.workshop.name(), workshopSessionIds);
            }
        } else if (AREA.NETWORKING.equals(area)) {
            List<Session> randomNetworkingSessions = GeneralUtils.pickNRandomElements(networkingSessions, (int) Math.ceil((double) networkingSessions.size() / 2));
            List<Long> networkingSessionIds = randomNetworkingSessions.stream().map(Session::getId).collect(Collectors.toList());
            randomTriggers.put(RedisChallengeAreaDTO.Networking.networking.name(), networkingSessionIds);
        }
        return randomTriggers;
    }

    private String getNextScavengerNameChallenge(Long eventId) {
        String lastScavengerChallengeName = eventChallengeRepoService.getLastRandomGeneratedScavengerChallengeName(eventId, ChallengeConstants.SCAVENGER_CHALLENGE_NAME_REGEX);
        String newChallengeName = GeneralUtils.getNextUniqueName(lastScavengerChallengeName);
        log.info("Last scavenger challenge name : {}, New scavenger challenge name : {}", lastScavengerChallengeName, newChallengeName);
        return newChallengeName;
    }

    @Override
    public EventChallengeDTO findById(Long eventChallengeDetailId, Event event) {
        EventChallengeDetail eventChallengeDetail = eventChallengeRepoService.findById(eventChallengeDetailId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.EventChallengeNotFound.EVENT_CHALLENGE_CONFIG_NOT_FOUND));
        EventChallengeDTO eventChallengeDTO =  new EventChallengeDTO(eventChallengeDetail, event);

        EventChallengeTiers eventChallengeTiers = challengeAndTierMappingRepository.getEventChallengeTiersByChallengeIdAndEventId(eventChallengeDetailId, event.getEventId());
        if(null != eventChallengeTiers) {
            eventChallengeDTO.setTierId(eventChallengeTiers.getId());
            eventChallengeDTO.setTierName(eventChallengeTiers.getTierName());
        }
        return eventChallengeDTO;
    }

    @Override
    @Transactional
    public void updateEventChallengeDetail(Event event, EventChallengeDTO dto, Long eventChallengeDetailId) {
        log.info("in updateEventChallengeDetail");
        EventChallengeDetail eventChallengeDetail = eventChallengeRepoService.findById(eventChallengeDetailId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.EventChallengeNotFound.EVENT_CHALLENGE_CONFIG_NOT_FOUND));
        EventChallengeDTO oldEventChallengeDto = new EventChallengeDTO(eventChallengeDetail, event);
        log.info("in updateEventChallengeDetail before saving");
        eventChallengeRepoService.save(dto.updateEntity(eventChallengeDetail, event));
        dto = setGamificationTabFlagInDTO(dto,event);
        dto.setName(eventChallengeDetail.getChallengeName());
        dto.setDescription(null);
        dto.setRewards(null);

        EventChallengeTiers eventChallengeTiers = challengeAndTierMappingRepository.getEventChallengeTiersByChallengeIdAndEventId(eventChallengeDetailId,event.getEventId());
        if(eventChallengeTiers != null){
            dto.setTierId(eventChallengeTiers.getId());
        }

        log.info("Updating challenge {} in redis", eventChallengeDetailId);
        updateChallengeDetailInRedis(dto, oldEventChallengeDto, event.getEventId());
    }

    private void checkChallengeIsEditableOrThrowError(Event event, Long challengeId, EventChallengeDTO dto) throws JSONException {
        log.info("Validating if challenge {} is editable or not", challengeId);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Integer preEventAccessMinutes = ticketing.getPreEventAccessMinutes() != null ? -ticketing.getPreEventAccessMinutes() : 0 ;
        Date eventStartDate = DateUtils.addMinutes(ticketing.getEventStartDate(), preEventAccessMinutes);

        boolean isChallengeEditable = new Date().compareTo(eventStartDate) < 0;
        if(!isChallengeEditable) {
            log.info("Challenge is not editable because event is already started event {}, challenge {}" , event.getEventId(), challengeId);
            throw new NotAcceptableException(NotAcceptableException.ChallengeExceptionMsg.CHALLENGE_NOT_ALLOWED_TO_EDIT);
        }
        if(ChallengeType.EARLY_BIRD.equals(dto.getType())) {
            List<EventChallengeDetail> challengeList = eventChallengeRepoService.findChallengesByEventIdAndTypeAndExceptId(event.getEventId(), dto.getType(),challengeId);
            isEarlyBirdChallengeAlreadyExists(event, getActionNameFromChallengeDTO(dto), challengeList);
        }
        log.info("Challenge {} is editable", challengeId);
    }

    @Override
    @Transactional
    public void updateEventChallengeOrThrowErrorIfNotEditable(Event event, EventChallengeDTO eventChallengeDTO, Long eventChallengeDetailId) throws JSONException  {

        validateChallengeDTO(event, eventChallengeDTO, eventChallengeDetailId);
        if(CollectionUtils.isEmpty(eventChallengeDTO.getActionErrors())
                && CollectionUtils.isEmpty(eventChallengeDTO.getTriggerErrors())
                && CollectionUtils.isEmpty(eventChallengeDTO.getNetworkMaxPointErrors())
                && CollectionUtils.isEmpty(eventChallengeDTO.getInvalidActions())
                && CollectionUtils.isEmpty(eventChallengeDTO.getInvalidTriggers())
                && CollectionUtils.isEmpty(eventChallengeDTO.getInvalidTicketTypes())) {
            log.info("All validations completed for the challenge {}, updating challenge now", eventChallengeDetailId);
            updateEventChallengeDetail(event, eventChallengeDTO, eventChallengeDetailId);
        }
    }

    @Override
    @Transactional
    public void updateStatusToDeleteByEventChallengeId(Event event, Long eventChallengeId) {
        EventChallengeDetail eventChallengeDetail = eventChallengeRepoService.findById(eventChallengeId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.EventChallengeNotFound.EVENT_CHALLENGE_CONFIG_NOT_FOUND));
        eventChallengeRepoService.updateStatusToDeleteByEventChallengeId(event.getEventId(), eventChallengeId, RecordStatus.DELETE);
        EventChallengeDTO eventChallengeDTO = new EventChallengeDTO(eventChallengeDetail, event);
        deleteChallengeDataRedis(eventChallengeDTO, event.getEventId());
        deleteTierMapping(eventChallengeDTO.getEventChallengeId(), event.getEventId());
        deleteChallengeRewards(eventChallengeDTO.getEventChallengeId(), event.getEventId());
        deleteUserCreditManagementDetails(eventChallengeDTO.getEventChallengeId(), event.getEventId());
    }

    private void deleteUserCreditManagementDetails(long eventChallengeId, long eventId) {
        creditManagementRepoService.deleteByChallengeIdAndEventId(eventChallengeId, eventId);
    }

    private void deleteChallengeRewards(long eventChallengeId, long eventId) {
        log.info("deleteChallengeRewards with challenge id {} and event id {}", eventChallengeId, eventId);
        userChallengeRewardsTrackerRepository.deleteByChallengeIdAndEventId(eventChallengeId,eventId);
    }

    private void deleteTierMapping(long eventChallengeId, long eventId) {
        log.info("deleteTierMapping with challenge id {} and event id {}", eventChallengeId, eventId);
        challengeAndTierMappingRepository.deleteByChallengeIdAndEventId(eventChallengeId, eventId);

    }

    @Override
    public DataTableResponse getAllEventChallenges(Event event, String searchString, int page, int size, List<String> filterStatus, List<ChallengeType> filterType, String sortColumn, boolean isAsc) {
        Sort sortBy = isAsc ? Sort.by(sortColumn) : Sort.by(sortColumn).descending();
        Pageable pageable = PageRequest.of(Math.max(page, 0), (size > 0) ? size : 10, sortBy);
        boolean activeStatus = true;
        boolean endedStatus = true;
        if (filterStatus.size() == 1) {
            activeStatus = filterStatus.get(0).equalsIgnoreCase("active");
            endedStatus = !activeStatus;
        }

        Page<EventChallengeDetail> eventChallengeDetailList = eventChallengeRepoService.findChallengesByEventIdAndTypesAndStatus(event.getEventId(), filterType, pageable, new Date(), searchString, activeStatus, endedStatus);
        log.info("fetch event challenges successfully for Event {}", event.getEventId());

        List<Long> challengeIds = eventChallengeDetailList.getContent()
                .stream()
                .map(EventChallengeDetail::getId)
                .collect(Collectors.toList());

        Map<Long, EventChallengeTiers> challengeIdToTierMap = new HashMap<>();
        if (!challengeIds.isEmpty()) {
            List<Object[]> mappings = challengeAndTierMappingRepository.findTiersByChallengeIdsAndEventId(challengeIds, event.getEventId());
            challengeIdToTierMap = mappings.stream()
                    .collect(Collectors.toMap(
                            mapping -> (Long) mapping[0],  // challengeId
                            mapping -> (EventChallengeTiers) mapping[1] // EventChallengeTiers object
                    ));
        }


        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(eventChallengeDetailList.getTotalElements());
        dataTableResponse.setRecordsFiltered(eventChallengeDetailList.getNumberOfElements());

        Map<Long, EventChallengeTiers> finalChallengeIdToTierMap = challengeIdToTierMap;
        dataTableResponse.setData(eventChallengeDetailList.getContent().stream().map(eventChallengeDetail -> {
            EventChallengeDTO eventChallengeDTO = new EventChallengeDTO(eventChallengeDetail, event);
            EventChallengeTiers eventChallengeTiers = finalChallengeIdToTierMap.get(eventChallengeDetail.getId());
            if (eventChallengeTiers != null) {
                eventChallengeDTO.setTierId(eventChallengeTiers.getId());
                eventChallengeDTO.setTierName(eventChallengeTiers.getTierName());
            }
            return eventChallengeDTO;
        }).collect(Collectors.toList()));
        return dataTableResponse;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<ChallengeDetail> getAllEventChallenges(Event event) {
        return eventChallengeRepoService.findAllByEventId(event.getEventId());
    }

    @Override
    public List<String> getArea() {
        return challengeConfigRepository.findArea();
    }

    @Override
    public void setChallengeDetailInRedis(EventChallengeDTO eventChallengeDTO, Long eventId) {
        log.info("Challenge configuration to be saved in redis: {}", eventChallengeDTO);
        if (!CollectionUtils.isEmpty(eventChallengeDTO.getTrigger())) {
            Object[] subAreaList = RedisChallengeAreaDTO.getSubAreaDetails(eventChallengeDTO.getArea());
            if (subAreaList != null) {
                Map<Long, List<Long>> expoIdsByCategory = getExpoIdsByCategories(eventId, eventChallengeDTO.getTrigger());
                Arrays.asList(subAreaList).forEach(subArea -> {
                    if (RedisChallengeAreaDTO.Expo.category.name().equals(subArea.toString())) {
                        // If subarea is `category` then first fetch all expo related to the category
                        // then save challenge data in redis based on expo id keys
                        List<Long> categoryIds = eventChallengeDTO.getTrigger().getOrDefault(subArea.toString(), new ArrayList<>());
                        for (Long categoryId : categoryIds) {
                            List<Long> expoIds = expoIdsByCategory.get(categoryId);
                            setChallengeDataByIds(expoIds, eventChallengeDTO, subArea.toString());
                        }
                    } else if (!CollectionUtils.isEmpty(eventChallengeDTO.getTrigger().get(subArea.toString()))) {
                        // For other subarea just save data in redis as it is by their id
                        List<Long> expoSessionSurveyIds;
                        if(!eventChallengeDTO.getArea().equalsIgnoreCase(AREA.QR_SCAN)) {
                             expoSessionSurveyIds = eventChallengeDTO.getTrigger().get(subArea.toString());
                        }else{
                             expoSessionSurveyIds = Collections.singletonList(eventChallengeDTO.getEventChallengeId());
                        }
                        setChallengeDataByIds(expoSessionSurveyIds, eventChallengeDTO, subArea.toString());
                    }
                });
            }
        } else {
            log.info("Challenge configuration is empty");
        }
    }

    private void setChallengeDataByIds(List<Long> expoSessionSurveyIds, EventChallengeDTO eventChallengeDTO, String subArea) {
        if (!CollectionUtils.isEmpty(expoSessionSurveyIds)) {
            expoSessionSurveyIds.forEach(expoSessionSurveyId -> {
                eventChallengeDTO.setSubArea(subArea.toString());
                String key = StringUtils.lowerCase(eventChallengeDTO.getArea()) + "_" + expoSessionSurveyId;
                log.info("Saving challenge configuration for expo or sessionId or surveyId {}", expoSessionSurveyId);
                setChallengeData(key, eventChallengeDTO);
            });
        }
    }

    private void setChallengeData(String key, EventChallengeDTO eventChallengeDTO) {
        List<EventChallengeDTO> listChallenge = new ArrayList<>();
        String dataChallenges = (String) getData(key);
        if (StringUtils.isNotBlank(dataChallenges)) {
            listChallenge = getEventChallengeDtos(dataChallenges);
        }
        listChallenge.add(eventChallengeDTO);
        log.info("Set challenges In Redis for key {} , value {}", key, listChallenge);
        setData(key, JsonMapper.convertToString(listChallenge));
    }

    @Override
    public void updateChallengeDetailInRedis(EventChallengeDTO eventChallengeDTO, EventChallengeDTO oldEventChallengeDto, Long eventId) {
        deleteChallengeDataRedis(oldEventChallengeDto, eventId);
        setChallengeDetailInRedis(eventChallengeDTO, eventId);
        log.info("Challenge {} successfully updated in Redis", eventChallengeDTO.getEventChallengeId());
    }

    @Override
    public void deleteChallengeDataRedis(EventChallengeDTO eventChallengeDTO, Long eventId) {
        if (!CollectionUtils.isEmpty(eventChallengeDTO.getTrigger())) {
            Object[] subAreaList = RedisChallengeAreaDTO.getSubAreaDetails(eventChallengeDTO.getArea());
            if (subAreaList != null) {
                Map<Long, List<Long>> expoIdsByCategory = getExpoIdsByCategories(eventId, eventChallengeDTO.getTrigger());
                Arrays.asList(subAreaList).forEach(subArea -> {
                    if (RedisChallengeAreaDTO.Expo.category.name().equals(subArea.toString())) {
                        List<Long> categoryIds = eventChallengeDTO.getTrigger().getOrDefault(subArea.toString(), new ArrayList<>());
                        categoryIds.forEach(category -> {
                            List<Long> expoIds = expoIdsByCategory.get(category);
                            deleteChallengeDataByIds(expoIds, eventChallengeDTO);
                        });
                    } else if (!CollectionUtils.isEmpty(eventChallengeDTO.getTrigger().get(subArea.toString()))) {
                        List<Long> expoSessionIds;
                        if(!eventChallengeDTO.getArea().equalsIgnoreCase(AREA.QR_SCAN)) {
                            expoSessionIds = eventChallengeDTO.getTrigger().get(subArea.toString());
                        }else{
                            expoSessionIds = Collections.singletonList(eventChallengeDTO.getEventChallengeId());
                        }
                        deleteChallengeDataByIds(expoSessionIds, eventChallengeDTO);
                    }
                });
            }
        }
    }

    private void deleteChallengeDataByIds(List<Long> expoSessionIds, EventChallengeDTO eventChallengeDTO) {
        if (!CollectionUtils.isEmpty(expoSessionIds)) {
            expoSessionIds.forEach(expoSessionId -> {
                String key = StringUtils.lowerCase(eventChallengeDTO.getArea()) + "_" + expoSessionId;
                log.info("About to delete challenge details for key {}, challenge {}", key, eventChallengeDTO);
                deleteChallengeData(key, eventChallengeDTO);
            });
        }
    }

    private void setData(String key, Object value) {
        try {
            this.gamificationRedisCache.set(key, value, 60, TimeUnit.DAYS);
            log.info("Saved successfullyy in gamification redis cache, key: {}, value: {}", key, value);
        } catch (Exception exception) {
            log.warn("Exception while saving in gamification redis cache, key: {}, value: {}, cause: {}", key, value, exception.getMessage());
        }

    }

    private Object getData(String key) {
        return this.gamificationRedisCache.get(key);
    }

    private void deleteChallengeData(String key, EventChallengeDTO eventChallengeDTO) {
        List<EventChallengeDTO> listChallenge = new ArrayList<>();
        String Challenge = (String) getData(key);
        if (StringUtils.isNotBlank(Challenge)) {
            listChallenge = getEventChallengeDtos(Challenge);
        }
        listChallenge.removeIf(challenge -> challenge.getEventChallengeId() == eventChallengeDTO.getEventChallengeId());
        log.info("Delete challenges from Redis key {} , value {}", key, listChallenge);
        setData(key, JsonMapper.convertToString(listChallenge));
    }

    private static List<EventChallengeDTO> getEventChallengeDtos(String jsonInput) {
        List<EventChallengeDTO> eventChallengeDTOS = null;
        ObjectMapper mapper = new ObjectMapper();

        List<String> list = JsonMapper.stringtoObject(jsonInput, List.class);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        eventChallengeDTOS = mapper.convertValue(list, new TypeReference<List<EventChallengeDTO>>() {
        });
        return eventChallengeDTOS;
    }

    @Override
    public List<EventChallengeDetail> getAllChallengesByEventId(Event event) {
        return eventChallengeRepoService.findAllChallengesByEventId(event.getEventId());
    }

    @Override
    @Transactional
    public void addDefaultEventChallenge(Event event) {
        List<EventChallengeDetail> eventChallengeDetails = getAllChallengesByEventId(event);
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        List<Long> ticketTypesAllowedInChallenge = ticketingTypeService.findAllIdByEventAndDataTypeAndExcludeFormatAndDonation(
                event, DataType.TICKET);
        List<Long> listExhibitorIds = getExhibitorIdByEventId(event.getEventId());
        LeaderboardPointSetting defaultPoints = getPointSetting(event.getEventId());
        if (CollectionUtils.isEmpty(eventChallengeDetails)) {
            log.info("ChallengeConfigServiceImpl || empty eventChallengeDetails ||addDefaultEventChallenge eventId {} ",event.getEventId());
            EventChallengeDTO eventChallengeDTO = EventChallengeDTO.prepareDefaultChallengeDTO(event, ticketing, ticketTypesAllowedInChallenge, listExhibitorIds, defaultPoints);
            createEventChallenge(event, eventChallengeDTO);
        } else {
            log.info("ChallengeConfigServiceImpl || eventChallengeDetails ||addDefaultEventChallenge eventId {} ",event.getEventId());
            eventChallengeDetails.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).forEach(eventChallengeDTO -> {
                if (eventChallengeDTO.isDefaultChallenge()) {
                    eventChallengeDTO = eventChallengeDTO.setDefaultChallengeDynamicParam(eventChallengeDTO, ticketing, event, ticketTypesAllowedInChallenge, listExhibitorIds);
                }
                updateEventChallengeDetail(event, eventChallengeDTO, eventChallengeDTO.getEventChallengeId());
            });
        }
    }

    private LeaderboardPointSetting getPointSetting(long eventId) {

        LeaderboardPointSetting setting = leaderboardPointSettingRepoService.findByEventId(eventId);
        if (setting == null) {
            setting = new LeaderboardPointSetting();
            setting.setChatPoint(20);
            setting.setDownloadDocumentPoint(5);
            setting.setEventId(eventId);
            setting.setEveryVisitPoint(1);
            setting.setMinVisitTime(20);
            setting.setVisitPoint(5);
            leaderboardPointSettingRepoService.save(setting);
        }
        return setting;
    }

    @Override
    @Async
    @Transactional
    public void updateTicketTypeInChallengeOnDelete(TicketingType ticketingType, Event event) {
        log.info("Start updateTicketTypeInChallengeOnDelete for Event Id {}",event.getEventId());
        List<EventChallengeDetail> eventChallengeDetails = getAllChallengesByEventId(event);
        eventChallengeDetails.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).forEach(eventChallengeDTO -> {
            List<Long> allowedEventTicketTypeIds = Arrays.stream(convertLongListToCommaSeparated(eventChallengeDTO.getTicketTypeAllowInChallenge()).split(STRING_COMMA)).filter(s -> !CollectionUtils.isEmpty(eventChallengeDTO.getTicketTypeAllowInChallenge())).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            allowedEventTicketTypeIds.remove(ticketingType.getId());
            eventChallengeDTO.setTicketTypeAllowInChallenge(allowedEventTicketTypeIds);
            updateEventChallengeDetail(event, eventChallengeDTO, eventChallengeDTO.getEventChallengeId());
        });
        log.info("End updateTicketTypeInChallengeOnDelete for Event Id {}",event.getEventId());
    }

    @Override
    @Async
    @Transactional
    public void updateTicketTypeInChallengeOnInsert(TicketingType ticketingType, List<Long> ticketingTypeIds, Event event) {
        log.info("Start updateTicketTypeInChallengeOnInsert for Event Id {}",event.getEventId());
        List<EventChallengeDetail> eventChallengeDetails = getAllChallengesByEventId(event);
        if (null != ticketingTypeIds) {
            ticketingTypeIds.remove(ticketingType.getId());
        }
        eventChallengeDetails.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).forEach(eventChallengeDTO -> {
            List<Long> allowedEventTicketTypeIds = Arrays.stream(convertLongListToCommaSeparated(eventChallengeDTO.getTicketTypeAllowInChallenge()).split(STRING_COMMA)).filter(s -> !CollectionUtils.isEmpty(eventChallengeDTO.getTicketTypeAllowInChallenge())).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            if (allowedEventTicketTypeIds.containsAll(ticketingTypeIds) && TICKET.equals(ticketingType.getDataType())
                    && checkTicketAllowToAdd(ticketingType, eventChallengeDTO.getType())) {
                allowedEventTicketTypeIds.add(ticketingType.getId());
                eventChallengeDTO.setTicketTypeAllowInChallenge(allowedEventTicketTypeIds);
                updateEventChallengeDetail(event, eventChallengeDTO, eventChallengeDTO.getEventChallengeId());
            }
        });
        log.info("End updateTicketTypeInChallengeOnInsert for Event Id {}",event.getEventId());
    }

    /**
     * Do not save Donation tickets to Challenges because by Donation tickets attendees cannot access VEH
     * In-Person Ticket Type format is only allowed in Early Bird challenges
     * For other challenge type not allowed to save In person ticket.
     */
    private boolean checkTicketAllowToAdd(TicketingType ticketingType, ChallengeType type) {
        if (TicketType.DONATION.equals(ticketingType.getTicketType())
                || (TicketTypeFormat.IN_PERSON.equals(ticketingType.getTicketTypeFormat()) && !ChallengeType.EARLY_BIRD.equals(type))) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional
    public void updateExhibitorIdInChallengeOnInsert(Exhibitor exhibitor, List<Long> exhibitorIds, Event event) {
        log.info("Start updateExhibitorIdInChallengeOnInsert for Event Id {}",event.getEventId());
        List<EventChallengeDetail> eventChallengeDetails = getAllChallengesByEventId(event);
        if(exhibitor!=null){
            exhibitorIds.remove(exhibitor.getId());
        }
        eventChallengeDetails.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).forEach(eventChallengeDTO -> {
            if (AREA.EXPO.equalsIgnoreCase(eventChallengeDTO.getArea()) && !CollectionUtils.isEmpty(eventChallengeDTO.getTrigger())) {
                Map<String, List<Long>> triggerMap = eventChallengeDTO.getTrigger();
                List<Long> listExhibitorIds = !CollectionUtils.isEmpty(triggerMap.get(TRIGGER.EXHIBITOR))?triggerMap.get(TRIGGER.EXHIBITOR):new ArrayList<>();
                if(listExhibitorIds.containsAll(exhibitorIds)) {
                    listExhibitorIds.add(exhibitor.getId());
                    triggerMap.put(TRIGGER.EXHIBITOR, listExhibitorIds);
                    eventChallengeDTO.setTrigger(triggerMap);
                    updateEventChallengeDetail(event, eventChallengeDTO, eventChallengeDTO.getEventChallengeId());
                }
            }
        });
        log.info("End updateExhibitorIdInChallengeOnInsert for Event Id {}",event.getEventId());
    }

    /**
     * On creation of a new category if all categories are selected in the challenge
     * then add newly created category in the challenge
     *
     * @param newCategoryId
     * @param allCategoryIds
     * @param event
     */
    @Override
    @Transactional
    public void updateCategoryIdInChallengeOnInsert(Long newCategoryId, List<Long> allCategoryIds, Event event) {
        allCategoryIds.remove(newCategoryId);
        List<EventChallengeDTO> challengeList = getAllEventChallengeDetails(event);
        challengeList.forEach(challenge -> {
            Map<String, List<Long>> triggerMap = challenge.getTrigger();
            if (triggerMap.containsKey(RedisChallengeAreaDTO.Expo.category.name())) {
                List<Long> categoryIds = triggerMap.getOrDefault(RedisChallengeAreaDTO.Expo.category.name(), new ArrayList<>());
                if (categoryIds.containsAll(allCategoryIds)) {
                    categoryIds.add(newCategoryId);
                    triggerMap.put(RedisChallengeAreaDTO.Expo.category.name(), categoryIds);
                    challenge.setTrigger(triggerMap);
                    updateEventChallengeDetail(event, challenge, challenge.getEventChallengeId());
                }
            }
        });
    }

    /**
     * If any exhibitor is added or removed from a exhibitor category 
     * then update challenge data in Redis
     *
     * @param exhibitor
     * @param oldCategoryIds
     * @param newCategoryIds
     */
    @Override
    @Transactional
    public void updateChallengeOnExhibitorCategoryUpdate(Exhibitor exhibitor, List<Long> oldCategoryIds, List<Long> newCategoryIds) {
        log.info("start update challenge for exhibitor {} when category updated for eventId {}",exhibitor.getId(),exhibitor.getEventId());

        boolean isCategoryUpdated = !oldCategoryIds.containsAll(newCategoryIds) || oldCategoryIds.size() != newCategoryIds.size();
        
        if (isCategoryUpdated && exhibitor != null) {
            Set<Long> categoryIds = new HashSet<>();
            categoryIds.addAll(oldCategoryIds);
            categoryIds.addAll(newCategoryIds);
            Event event = exhibitor.getEvent();
            List<EventChallengeDTO> challengeList = getAllEventChallengeDetails(event);

            challengeList.forEach(challenge -> {
                Map<String, List<Long>> trigger = challenge.getTrigger();
                categoryIds.forEach(categoryId -> {
                    List<Long> challengeCategories = trigger.getOrDefault(RedisChallengeAreaDTO.Expo.category.name(), new ArrayList<>());
                    if (challengeCategories.contains(categoryId)) {
                        // Before updating challenge details first delete data from Redis based on expo id
                        deleteChallengeDataByIds(Arrays.asList(exhibitor.getId()), challenge);
                        updateEventChallengeDetail(event, challenge, challenge.getEventChallengeId());
                    }
                });
            });
            log.info("end update challenge for exhibitor {} when category updated for eventId {}",exhibitor.getId(),exhibitor.getEventId());
        }
    }

    @Override
    @Transactional
    public void updateExhibitorIdListInChallengeOnUploadCSV(List<Exhibitor> exhibitorList, List<Long> exhibitorIds, Event event){
        log.info("Start updateExhibitorIdListInChallengeOnUploadCSV for Event Id {}",event.getEventId());
        List<EventChallengeDetail> eventChallengeDetails = getAllChallengesByEventId(event);
        List<Long> exhibitorIdList = exhibitorList.stream().map(exhibitor -> exhibitor.getId()).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(exhibitorIdList)){
            exhibitorIds.removeAll(exhibitorIdList);
        }
        eventChallengeDetails.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).forEach(eventChallengeDTO -> {
            if (!CollectionUtils.isEmpty(eventChallengeDTO.getTrigger())) {
                Map<String, List<Long>> triggerMap = eventChallengeDTO.getTrigger();
                List<Long> listExhibitorIds = !CollectionUtils.isEmpty(triggerMap.get(TRIGGER.EXHIBITOR))?triggerMap.get(TRIGGER.EXHIBITOR):new ArrayList<>();
                if(listExhibitorIds.containsAll(exhibitorIds)) {
                    listExhibitorIds.addAll(exhibitorIdList);
                    triggerMap.put(TRIGGER.EXHIBITOR, listExhibitorIds);
                    eventChallengeDTO.setTrigger(triggerMap);
                    updateEventChallengeDetail(event, eventChallengeDTO, eventChallengeDTO.getEventChallengeId());
                }
            }
        });
        log.info("End updateExhibitorIdListInChallengeOnUploadCSV for Event Id {}",event.getEventId());
    }

    @Override
    @Transactional
    public void removeTriggerFromChallengeOnDelete(Long exhibitorOrSessionOrSurveyId, String trigger, Event event) {
        log.info("Start remove triggers from challenge on delete of expo or session for event {}, trigger {}, id {}", event.getEventId(), trigger, exhibitorOrSessionOrSurveyId);
        if (StringUtils.isNotBlank(trigger)) {
            List<EventChallengeDetail> challenges = getAllChallengesByEventId(event);
            challenges
                    .stream()
                    .map(challenge -> new EventChallengeDTO(challenge, event))
                    .forEach(challengeDto -> {
                        if (!CollectionUtils.isEmpty(challengeDto.getTrigger())) {
                            Map<String, List<Long>> triggerMap = challengeDto.getTrigger();
                            List<Long> exhibitorOrSessionOrSurveyIds = triggerMap.get(trigger);
                            if (!CollectionUtils.isEmpty(exhibitorOrSessionOrSurveyIds)) {
                                exhibitorOrSessionOrSurveyIds.remove(exhibitorOrSessionOrSurveyId);
                                triggerMap.put(trigger, exhibitorOrSessionOrSurveyIds);
                                challengeDto.setTrigger(triggerMap);
                                updateEventChallengeDetail(event, challengeDto, challengeDto.getEventChallengeId());
                            }
                        }
                    });
        }
        log.info("End of remove triggers from challenge event {}, trigger {}, id {}", event.getEventId(), trigger, exhibitorOrSessionOrSurveyId);
    }

    @Override
    public List<Long> getExhibitorIdByEventId(long eventId){
        return exhibitorRepoService.getExhibitorIdByEventId(eventId);
    }

    @Override
    @Transactional
    public void updateDefaultChallengeTime(Ticketing ticketing, Event event) {
        List<EventChallengeDetail> eventChallengeDetails = getAllChallengesByEventId(event);
        eventChallengeDetails.stream().filter(s -> s.isDefaultChallenge()).map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).forEach(eventChallengeDTO -> {
            eventChallengeDTO.setStartDate(getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone(), null));
            eventChallengeDTO.setEndDate(getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone(), null));
            eventChallengeDTO.setStartDateUtc(ticketing.getEventStartDate());
            eventChallengeDTO.setEndDateUtc(ticketing.getEventEndDate());
            updateEventChallengeDetail(event, eventChallengeDTO, eventChallengeDTO.getEventChallengeId());
        });
    }

    private EventChallengeDTO setGamificationTabFlagInDTO(EventChallengeDTO eventChallengeDTO, Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        if (virtualEventSettings != null && StringUtils.isNotBlank(virtualEventSettings.getVirtualTabs())) {
            String virtualTabs = virtualEventSettings.getVirtualTabs();
            Map<String, List<VirtualEventTabsDTO>> virtualTabsInMap = getVirtualTabsInMap(virtualTabs);
            virtualTabsInMap.get("lobbyTab").stream().filter(gamification -> gamification.getKey().equals("Gamification")).findFirst().ifPresent(e -> {
                eventChallengeDTO.setGamificationTabDisabled(e.isHide());
                log.info("Value of Gamification flag {} ", e.isHide());
            });
        } else {
            eventChallengeDTO.setGamificationTabDisabled(Boolean.FALSE);
            log.info("Value of Gamification flag Not Set Default true ");
        }
        return eventChallengeDTO;
    }

    public Map<String, List<VirtualEventTabsDTO>> getVirtualTabsInMap(String virtualTabs) {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, List<VirtualEventTabsDTO>> linkKeyValueDtos = new HashMap<>();
        if (StringUtils.isNotBlank(virtualTabs)) {
            try {
                log.info("prepare map with virtual event tabs data");
                linkKeyValueDtos = JsonMapper.stringtoObject(virtualTabs, Map.class);
                linkKeyValueDtos = mapper.convertValue(linkKeyValueDtos, new TypeReference<Map<String,List<VirtualEventTabsDTO>>>() {
                });
            } catch (Exception e) {
                log.error("getVirtualTabsInMap = {}", e.getMessage());
            }
        }
        return linkKeyValueDtos;
    }

    @Override
    public List<EventChallengeDTO> getAllEventChallengeDetails(Event event) {
        List<EventChallengeDetail> challengeDetailList = eventChallengeRepoService.findAllChallengesByEventId(event.getEventId());
        return challengeDetailList.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).collect(Collectors.toList());
    }

    @Override
    public List<EventChallengeDetail> findChallengesByEventIdAndType(Long eventId, ChallengeType type) {
        return eventChallengeRepoService.findChallengesByEventIdAndType(eventId, type);
    }

    @Override
    public List<EventChallengeDTO> getChallengesByEventAndType(Event event, ChallengeType type) {
        List<EventChallengeDetail> challenges = eventChallengeRepoService.findChallengesByEventIdAndType(event.getEventId(), type);
        return challenges.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).collect(Collectors.toList());
    }

    /**
     * Credit gamification challenge reward to user for completing challenge.
     * @param event
     * @param user
     * @throws IOException
     * @throws JSONException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, List<GamificationRewardDTO>> gamificationUserRewardHandler(Event event, User user) throws IOException, JSONException {
        log.info("Start gamification reward handler for event {}, user {}", event.getEventId(), user.getUserId());
        boolean isStaff = staffService.checkUserHasAdminStaffSpeakerExhibitorAccess(event.getEventId(), user.getUserId());

        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        List<EventChallengeDetail> challenges = getAllChallengesByEventId(event);
        if (CollectionUtils.isEmpty(challenges)) {
            return Collections.emptyMap();
        }
        Map<Long, Long> userChallengePointMap = leaderboardService.getUserChallengePointsWithoutCheckingEligiblePoints(event, user.getUserId());
        List<UserChallengeRewardsTracker> rewardsTrackers = challengeRewardsTrackerRepoService.getByUserIdAndEventId(user.getUserId(), event.getEventId());
        Map<Long, List<UserChallengeRewardsTracker>> challengeRewardsTrackersMap = rewardsTrackers.stream()
                .collect(Collectors.groupingBy(tracker ->
                        tracker.getChallengeId() != null ? tracker.getChallengeId() : -1L
                ));

        Map<Long, List<UserChallengeRewardsTracker>> tiersRewardsTrackersMap = rewardsTrackers.stream()
                .collect(Collectors.groupingBy(tracker ->
                        tracker.getTierId() != null ? tracker.getTierId() : -1L
                ));
        List<EventChallengeTiers> eventChallengeTiers = eventChallengeTierRepository.findByEventId(event.getEventId());
        List<EventChallengeAndTierMapping> mappings = challengeAndTierMappingRepository.findByEventId(event.getEventId());

        log.info("gamificationUserRewardHandler total challenges {} and tiers {} and mapping {}", challenges.size(), eventChallengeTiers.size(), mappings.size());

        Map<Long, List<Long>> tierToChallengesMap = createTierToChallengesMap(mappings);
        Map<Long, Long> challengeToTierMap = createChallengeToTierMap(mappings);

        Set<Long> tierChallengeIds = mappings.stream().map(EventChallengeAndTierMapping::getChallengeId).collect(Collectors.toSet());
        List<EventChallengeTiers> orderedEventTiers = reorderTiersByPrerequisite(eventChallengeTiers);
        List<Long> completedChallengesOfCompletedTiers = new ArrayList<>();
        List<Long> completedTiers = computeCompletedTiers(orderedEventTiers, tierToChallengesMap, challenges, userChallengePointMap,completedChallengesOfCompletedTiers);

        try {
            leaderboardService.makePointsEligibleForUnlockedTierChallenges(user, event, completedChallengesOfCompletedTiers);
        } catch (ElasticsearchStatusException ex) {
            log.info("makePointsEligibleForUnlockedTierChallenges user {}, event {}, and challenges {} message {}", user.getUserId(), event.getEventId(), completedChallengesOfCompletedTiers,ex.getMessage());
        }
        log.info("Fetched event challenges size {}, user challenge points size {}, challenge rewards tracker size {}", challenges.size(), userChallengePointMap.size(), challengeRewardsTrackersMap.size());

        Raffle raffle = raffleService.findByEvent(event);
        boolean isRaffleActive = raffle.isActivated() && event.isRaffleEnabled();

        Map<String, List<GamificationRewardDTO>> userRewards = new HashMap<>();
        List<GamificationRewardDTO> completedRewards = new ArrayList<>();
        List<GamificationRewardDTO> availableRewards = new ArrayList<>();
        List<GamificationRewardDTO> endedRewards = new ArrayList<>();

        List<UserChallengeRewardsTracker> userChallengeRewardsTrackers = new ArrayList<>();

        for (EventChallengeDetail challenge : challenges) {
            Long userPoints = userChallengePointMap.getOrDefault(challenge.getId(), 0l);
            log.info("user points for the challenge {}, event {} user {}, challenge {}", userPoints,event.getEventId(), user.getUserId(), challenge.getId());
            String rewards = challenge.getRewards();

            if (StringUtils.isNotBlank(rewards)) {

                JSONArray rewardList = new JSONArray(rewards);
                for (int i = 0; i < rewardList.length(); i++) {
                    JSONObject reward = rewardList.getJSONObject(i);
                    if (reward != null) {
                        GamificationRewardDTO challengeRewardDTO = new GamificationRewardDTO();
                        setChallengeRewardsDetails(challengeRewardDTO, challenge, reward);

                        List<UserChallengeRewardsTracker> challengeRewardsTrackers = challengeRewardsTrackersMap.getOrDefault(challenge.getId(), Collections.emptyList());

                        UserChallengeRewardsTracker userChallengeRewardsTracker = challengeRewardsTrackers.stream()
                                .filter(rewardTracker -> Boolean.TRUE.equals(rewardTracker.getRewarded()) && rewardTracker.getChallengeId() == challenge.getId())
                                .findAny()
                                .orElse(null);
                        boolean isRewarded = userChallengeRewardsTracker != null;
                        challengeRewardDTO.setChallengeId(challenge.getId());
                        challengeRewardDTO.setChallengeName(challenge.getChallengeName());
                        challengeRewardDTO.setEndDate(challenge.getEndDate());

                        boolean isTierChallenge = tierChallengeIds.contains(challenge.getId());
                        Long tierId = challengeToTierMap.getOrDefault(challenge.getId(), null);
                        if (isTierChallenge) {
                            if (tierId != null && !completedTiers.contains(tierId)) {
                                if (new Date().before(challenge.getEndDate())) {
                                    availableRewards.add(challengeRewardDTO);
                                } else{
                                    endedRewards.add(challengeRewardDTO);
                                }
                            }
                        }

                        if (userPoints >= challenge.getGoalPoints()) {
                            log.info("challenge is completed {}, user {} event {} rewarded {}", challenge.getId(),user.getUserId(), event.getEventId(), isRewarded);
                            if (tierId == null || completedTiers.contains(tierId)) {
                                if (!isRewarded) {
                                    assignRewardsForCompletedChallengesAndTiers(reward, isRaffleActive, event, user, raffle, userChallengeRewardsTrackers, completedRewards, null, challenge.getId(), challengeRewardDTO);
                                } else {
                                    completedRewards.add(challengeRewardDTO);
                                }
                            }
                        } else if (!isTierChallenge && new Date().before(challenge.getEndDate())) {
                            availableRewards.add(challengeRewardDTO);
                        } else if (!isTierChallenge&& new Date().after(challenge.getEndDate())) {
                            endedRewards.add(challengeRewardDTO);
                        }
                    }
                }
            }
        }


        for (EventChallengeTiers eventChallengeTier : eventChallengeTiers) {

            String rewards = eventChallengeTier.getRewards();
            if (StringUtils.isNotBlank(rewards)) {

                JSONArray rewardList = new JSONArray(rewards);
                for (int i = 0; i < rewardList.length(); i++) {
                    JSONObject reward = rewardList.getJSONObject(i);
                    if (reward != null) {
                        GamificationRewardDTO challengeRewardDTO = new GamificationRewardDTO();
                        setGamificationRewardsDetails(challengeRewardDTO, reward);
                        challengeRewardDTO.setEndDate(ticketing.getEventEndDate());

                        List<UserChallengeRewardsTracker> tierRewardsTrackers = tiersRewardsTrackersMap.getOrDefault(eventChallengeTier.getId(), Collections.emptyList());

                        UserChallengeRewardsTracker userChallengeRewardsTracker = tierRewardsTrackers.stream()
                                .filter(rewardTracker -> Boolean.TRUE.equals(rewardTracker.getRewarded()) && rewardTracker.getTierId() == eventChallengeTier.getId())
                                .findAny()
                                .orElse(null);
                        boolean isRewarded = userChallengeRewardsTracker != null;

                        challengeRewardDTO.setTierId(eventChallengeTier.getId());
                        challengeRewardDTO.setTierName(eventChallengeTier.getTierName());
                        if (completedTiers.contains(eventChallengeTier.getId())) {
                            if (!isRewarded) {
                                assignRewardsForCompletedChallengesAndTiers(reward, isRaffleActive, event, user, raffle, userChallengeRewardsTrackers, completedRewards, eventChallengeTier.getId(), null, challengeRewardDTO);
                            } else {
                                completedRewards.add(challengeRewardDTO);
                            }
                        } else if (new Date().before(ticketing.getEventEndDate())) {
                            availableRewards.add(challengeRewardDTO);
                        } else if (new Date().after(ticketing.getEventEndDate())) {
                            endedRewards.add(challengeRewardDTO);
                        }

                    }

                }
            }
        }

        if (!CollectionUtils.isEmpty(userChallengeRewardsTrackers)) {
            log.info("gamificationUserRewardHandler | assigned reward to the user {} and size {}", user.getUserId(), userChallengeRewardsTrackers.size());
            userChallengeRewardsTrackerRepository.saveAll(userChallengeRewardsTrackers);
        }

        userRewards.put("completed", completedRewards);
        userRewards.put("available", availableRewards);
        userRewards.put("ended", endedRewards);

        log.info("End of gamification reward handler for event {}, user {}", event.getEventId(), user.getUserId());
        return userRewards;
    }


    private List<EventChallengeTiers> reorderTiersByPrerequisite(List<EventChallengeTiers> tiers) {
        // Step 1: Create maps for lookup
        Map<Long, EventChallengeTiers> tierMap = tiers.stream()
                .collect(Collectors.toMap(EventChallengeTiers::getId, tier -> tier));

        // Map prerequisites (preRequisiteTier -> tierId)
        Map<Long, Long> prerequisiteMap = tiers.stream()
                .filter(tier -> tier.getPreRequisiteTier() != null)
                .collect(Collectors.toMap(EventChallengeTiers::getPreRequisiteTier, EventChallengeTiers::getId));

        // Step 2: Collect all starting points (tiers with preRequisiteTier = null)
        List<EventChallengeTiers> startingPoints = tiers.stream()
                .filter(tier -> tier.getPreRequisiteTier() == null)
                .collect(Collectors.toList());

        // Step 3: Build the sorted list
        List<EventChallengeTiers> orderedTiers = new ArrayList<>();

        for (EventChallengeTiers startingTier : startingPoints) {
            EventChallengeTiers current = startingTier;

            // Follow the chain of prerequisites
            while (current != null) {
                orderedTiers.add(current);
                Long nextTierId = prerequisiteMap.get(current.getId());
                current = nextTierId != null ? tierMap.get(nextTierId) : null;
            }
        }
        return orderedTiers;
    }

    private void assignRewardsForCompletedChallengesAndTiers(JSONObject reward, boolean isRaffleActive, Event event, User user,
                                                             Raffle raffle, List<UserChallengeRewardsTracker> userChallengeRewardsTrackers, List<GamificationRewardDTO> completedRewards,
                                                             Long tierId, Long challengeId, GamificationRewardDTO challengeRewardDTO) {

        String rewardType = reward.optString(REWARD_CONSTANT.TYPE);
        String rewardImage = reward.optString(REWARD_IMAGE);
        String rewardName = reward.optString(REWARD_CONSTANT.NAME);

        if (REWARD_CONSTANT.RAFFLE.equalsIgnoreCase(rewardType)) {
            if (isRaffleActive) {
                int noOfTickets = reward.optInt(REWARD_CONSTANT.TICKETS);
                long creditedTickets = raffleService.creditGamificationRewardRaffleTicket(
                        raffle,
                        prepareRaffleTicket(raffle.getId(), user.getUserId(), noOfTickets)
                );
                if (creditedTickets > 0) {
                    userChallengeRewardsTrackers.add(prepareUserChallengeRewardsTracker(
                            event.getEventId(), challengeId, user.getUserId(), rewardType, rewardImage, tierId, rewardName
                    ));
                    completedRewards.add(challengeRewardDTO);
                }
            } else {
                log.info("assignRewardsForCompletedChallengesAndTiers | Raffle module is not activated for event {}, raffle {} enabled {} activated {}",
                        event.getEventId(), raffle.getId(), event.isRaffleEnabled(), raffle.isActivated());
            }
        } else if (REWARD_CONSTANT.BADGE.equalsIgnoreCase(rewardType)) {
            userChallengeRewardsTrackers.add(prepareUserChallengeRewardsTracker(
                    event.getEventId(), challengeId, user.getUserId(), rewardType, rewardImage, tierId, rewardName
            ));
            completedRewards.add(challengeRewardDTO);
        }else{
            completedRewards.add(challengeRewardDTO);
        }
    }


    private List<Long> computeCompletedTiers(List<EventChallengeTiers> tiers,
                                             Map<Long, List<Long>> tierToChallengesMap,
                                             List<EventChallengeDetail> challenges,
                                             Map<Long, Long> userChallengePointMap, List<Long> completedTiersChallenges) {
        Set<Long> completedTiers = new HashSet<>();

        for (EventChallengeTiers tier : tiers) {
            List<Long> challengeIds = tierToChallengesMap.getOrDefault(tier.getId(), Collections.emptyList());

            List<EventChallengeDetail> challengesInTier = challenges.stream()
                    .filter(challenge -> challengeIds.contains(challenge.getId()))
                    .collect(Collectors.toList());

            long completedChallengesOfTier = challengesInTier.stream()
                    .filter(challenge -> challenge.getGoalPoints() <= userChallengePointMap.getOrDefault(challenge.getId(), 0L))
                    .count();

            boolean isPrerequisiteCompleted = tier.getPreRequisiteTier() == null || completedTiers.contains(tier.getPreRequisiteTier());

            if (isPrerequisiteCompleted && completedChallengesOfTier == challengesInTier.size()) {
                completedTiers.add(tier.getId());
                completedTiersChallenges.addAll(challengeIds);
            }
        }

        return new ArrayList<>(completedTiers);
    }


    private Map<Long, List<Long>> createTierToChallengesMap(List<EventChallengeAndTierMapping> mappings) {
        return mappings.stream().collect(Collectors.groupingBy(
                EventChallengeAndTierMapping::getTierId,
                Collectors.mapping(EventChallengeAndTierMapping::getChallengeId, Collectors.toList())
        ));
    }

    private Map<Long, Long> createChallengeToTierMap(List<EventChallengeAndTierMapping> mappings) {
        return mappings.stream().collect(Collectors.toMap(
                EventChallengeAndTierMapping::getChallengeId,
                EventChallengeAndTierMapping::getTierId
        ));
    }


    private void setChallengeRewardsDetails(GamificationRewardDTO challengeRewardDTO, EventChallengeDetail challenge, JSONObject reward){
        challengeRewardDTO.setReward(reward.optString(REWARD_CONSTANT.NAME));
        challengeRewardDTO.setRewardDescription(reward.optString(REWARD_CONSTANT.DESCRIPTION));
        challengeRewardDTO.setImage(reward.optString(REWARD_IMAGE));
        challengeRewardDTO.setRewardType(reward.optString(REWARD_CONSTANT.TYPE));
    }

    private boolean checkUserIsRewarded(List<UserChallengeRewardsTracker> challengeRewardsTrackers, String rewardType) {
        if (CollectionUtils.isEmpty(challengeRewardsTrackers)) {
            return false;
        }
        UserChallengeRewardsTracker userChallengeRewardsTracker = challengeRewardsTrackers.stream()
                .filter(rewardTracker -> rewardType.equals(rewardTracker.getRewardType()) && Boolean.TRUE.equals(rewardTracker.getRewarded()))
                .findAny()
                .orElse(null);
        return userChallengeRewardsTracker != null;
    }


    public UserChallengeRewardsTracker prepareUserChallengeRewardsTracker(Long eventId, Long challengeId, Long userId, String rewardType, String image, Long tierId, String rewardName) {
        UserChallengeRewardsTracker userChallengeRewardsTracker = new UserChallengeRewardsTracker();
        userChallengeRewardsTracker.setEventId(eventId);
        userChallengeRewardsTracker.setChallengeId(challengeId);
        userChallengeRewardsTracker.setUserId(userId);
        userChallengeRewardsTracker.setRewardType(rewardType);
        userChallengeRewardsTracker.setImage(image);
        userChallengeRewardsTracker.setTierId(tierId);
        userChallengeRewardsTracker.setRewardedAt(new Date());
        userChallengeRewardsTracker.setRewarded(true);
        userChallengeRewardsTracker.setTierId(tierId);
        userChallengeRewardsTracker.setRewardName(rewardName);
        return userChallengeRewardsTracker;
    }

    public PurchasedRaffleTicket prepareRaffleTicket(Long raffleId, Long userId, int noOfTickets) {
        PurchasedRaffleTicket raffleTicket = new PurchasedRaffleTicket();
        raffleTicket.setRaffleId(raffleId);
        raffleTicket.setTicketsPurchased(noOfTickets);
        raffleTicket.setUser(new User(userId));
        raffleTicket.setPrice(0);
        raffleTicket.setIncludesCompTicket(false);
        raffleTicket.setTransactionType(TransactionType.COMPLIMENTARY);
        return raffleTicket;
    }

    @Override
    public void resetChallengeDates(List<Long> listOfEventId, int days) {
        log.info("Start of reset gamification challenge dates for eventIds {} and for {} days",listOfEventId,days);
        List<EventChallengeDetail> listOfEventChallenges = eventChallengeRepoService.findByEventIdIn(listOfEventId);

        List<Event> events = roEventService.getEventListByIds(listOfEventId);
        Map<Long, Event> eventMap = events.stream().collect(Collectors.toMap(Event::getEventId, e -> e));

        List<EventChallengeDetail> updatedList = new ArrayList<>();
        if(!listOfEventChallenges.isEmpty()){
            listOfEventChallenges.forEach(eventChallengeDetail -> {
                EventChallengeDTO oldChallengeDTO = new EventChallengeDTO(eventChallengeDetail, eventMap.get(eventChallengeDetail.getEventId()));
                eventChallengeDetail.setStartDate(com.accelevents.utils.DateUtils.addDaysInDate(eventChallengeDetail.getStartDate(), days));
                eventChallengeDetail.setEndDate(com.accelevents.utils.DateUtils.addDaysInDate(eventChallengeDetail.getEndDate(), days));
                updatedList.add(eventChallengeDetail);

                // Update challenge details in Redis
                EventChallengeDTO newChallengeDTO = new EventChallengeDTO(eventChallengeDetail, eventMap.get(eventChallengeDetail.getEventId()));
                newChallengeDTO.setDescription(null);
                newChallengeDTO.setRewards(null);
                newChallengeDTO.setTierId(null);
                updateChallengeDetailInRedis(newChallengeDTO, oldChallengeDTO, eventChallengeDetail.getEventId());
            });
            eventChallengeRepoService.saveAll(updatedList);
        }
        log.info("End of reset gamification challenge dates for eventIds {} and for {} days",listOfEventId,days);
    }

    /**
     * Get exhibitor ids by category
     * @param eventId
     * @param triggers
     * @return return map key as category id and values as list of exhibitor ids
     */
    private Map<Long, List<Long>> getExpoIdsByCategories(Long eventId, Map<String, List<Long>> triggers) {
        List<Long> categories = triggers.get(RedisChallengeAreaDTO.Expo.category.name());
        Map<Long, List<Long>> expoByCategory = new HashMap<>();
        if (!CollectionUtils.isEmpty(categories)) {
            List<Object[]> exhibitorsByCategoryIds = exhibitorRepoService.findExhibitorsByCategoryIds(eventId, categories);
            exhibitorsByCategoryIds.forEach(exhibitor -> {
                List<Long> categoryIds = convertCommaSeparatedToListLong(exhibitor[1].toString());
                categories.forEach( c -> {
                    if(categoryIds.contains(c)) {
                        List<Long> expoIds = expoByCategory.getOrDefault(c, new ArrayList<>());
                        expoIds.add(((BigInteger) exhibitor[0]).longValue());
                        expoByCategory.put(c, expoIds);
                    }
                });
            });
        }
        log.info("Fetched Expo by categories size {}" , expoByCategory.size());
        return expoByCategory;
    }

    @Override
    public List<EventChallengeDTO> getChallengeDTOsByUser(User user, Event event) {
        List<EventChallengeDetail> challengesByUser = getAllChallengesByEventId(event);
        return convertToDto(challengesByUser, event);
    }

    @Override
    public ChallengeAndTierDetailsDTO getAllEventChallengeAndTiers(User user, Event event) throws IOException {
        log.info("getAllEventChallengeAndTiers user {} and event {}", user.getUserId(), event.getEventId());

        ChallengeAndTierDetailsDTO challengeAndTierDetailsDTO = new ChallengeAndTierDetailsDTO();

        Map<Long, Long> userChallengePointMap;
        try {
            userChallengePointMap = leaderboardService.getUserChallengePointsWithoutCheckingEligiblePoints(event, user.getUserId());
            log.info("getAllEventChallengeAndTiers userChallengePointMap {}", userChallengePointMap);
        }catch (IOException e){
            log.error("Failed to fetch user challenge points for event {} and user {}: {}", event.getEventId(), user.getUserId(), e.getMessage(), e);
            throw new NotAcceptableException(e);
        }


        List<Long> challengesCompletedOfCompletedTiers = new ArrayList<>();

        //get all challenges
        List<EventChallengeDetail> allChallenges = getAllChallengesByEventId(event);

        //get all the challenge tiers
        List<EventChallengeTiers> eventChallengeTiers = eventChallengeTierRepository.findByEventId(event.getEventId());

        //get all the challenge and tier mapping
        List<EventChallengeAndTierMapping> eventChallengeAndTierMappings  = challengeAndTierMappingRepository.findByEventId(event.getEventId());

        log.info("getAllEventChallengeAndTiers total challenges {} and tiers {} and mapping {}", allChallenges.size(), eventChallengeTiers.size(),eventChallengeAndTierMappings.size());

        Map<Long, List<Long>> tierToChallengesMap = eventChallengeAndTierMappings.stream()
                .collect(Collectors.groupingBy(
                        EventChallengeAndTierMapping::getTierId,
                        Collectors.mapping(EventChallengeAndTierMapping::getChallengeId, Collectors.toList())
                ));

        List<Long> completedTiers = new ArrayList<>();
        AtomicInteger totalCompletedChallenges= new AtomicInteger();

        Set<Long> tierChallengeIds = eventChallengeAndTierMappings.stream()
                .map(EventChallengeAndTierMapping::getChallengeId)
                .collect(Collectors.toSet());

        //re-order the challenge tiers based on the pre-requisite tiers
        List<EventChallengeTiers> orderedEventTiers = reorderTiersByPrerequisite(eventChallengeTiers);

        List<EventChallengeAndTierDTO> eventChallengeAndTierDTOs = orderedEventTiers.stream().map(tier -> {
            List<Long> challengeIdsInTier = tierToChallengesMap.getOrDefault(tier.getId(), Collections.emptyList());

            //filter the tier's challenges
            List<EventChallengeDetail> challengesInTier = allChallenges.stream()
                    .filter(challenge -> challengeIdsInTier.contains(challenge.getId()))
                    .collect(Collectors.toList());

            int challengesInTierCount = challengesInTier.size();

            log.info("getAllEventChallengeAndTiers challenge count {} for tier id {}", challengesInTierCount, tier.getId());

            int tierTotalPoints = 0;
            int completedChallengesOfTier = 0;

            for(EventChallengeDetail eventChallengeDetail: challengesInTier){
                //calculate the tier's total points with sum of the all challenges points
                tierTotalPoints += eventChallengeDetail.getGoalPoints();

                //check user completed the challenge or not based on the goal points
               if(eventChallengeDetail.getGoalPoints() <= userChallengePointMap.getOrDefault(eventChallengeDetail.getId(),0L)){
                   completedChallengesOfTier += 1;
               }

            }

            boolean isLockedTier = tier.getPreRequisiteTier() != null;

            boolean isPrequisiteTierNullOrCompleted = tier.getPreRequisiteTier() == null || completedTiers.contains(tier.getPreRequisiteTier());
            //if user completed the previous tier and started the current tier after the completed the pre-requisite tier then marked as unlocked tier
            if(isPrequisiteTierNullOrCompleted){
                totalCompletedChallenges.getAndAdd(completedChallengesOfTier);
                isLockedTier = false;
            }

            //if the user completed the current tier then marked as unlocked tier
            if(completedChallengesOfTier == challengesInTierCount && isPrequisiteTierNullOrCompleted){
                completedTiers.add(tier.getId());
                isLockedTier = false;
            }

            if(!isLockedTier){
                challengesCompletedOfCompletedTiers.addAll(challengeIdsInTier);
            }

            //prepare DTO for the tiers
            return new EventChallengeAndTierDTO(
                    tier.getId(),
                    tier.getTierName(),
                    tier.getDescription(),
                    tierTotalPoints,
                    isLockedTier,
                    challengesInTierCount,
                    isLockedTier ? 0:completedChallengesOfTier,
                    tier.getImage(),
                    tier.getPreRequisiteTier(),
                    StringUtils.isNotEmpty(tier.getRewards())
            );
        }).collect(Collectors.toList());

        //prepare DTO for the standalone challenges
        List<EventChallengeAndTierDTO> standaloneChallenges = allChallenges.stream()
                .filter(challenge -> !tierChallengeIds.contains(challenge.getId()))
                .map(challenge -> {
                    if(challenge.getGoalPoints() <= userChallengePointMap.getOrDefault(challenge.getId(),0L)){
                        totalCompletedChallenges.getAndIncrement();
                    }
                    return new EventChallengeAndTierDTO(
                            challenge.getId(),
                            challenge.getChallengeName(),
                            challenge.getChallengeDesc(),
                            challenge.getGoalPoints(),
                            challenge.getImage(),
                            String.valueOf(challenge.getType()),
                            Math.toIntExact(userChallengePointMap.getOrDefault(challenge.getId(),0L)),
                            StringUtils.isNotEmpty(challenge.getRewards())
                    );
                }).collect(Collectors.toList());

        // Combine tiers and standalone challenges
        eventChallengeAndTierDTOs.addAll(standaloneChallenges);
        try {
            leaderboardService.makePointsEligibleForUnlockedTierChallenges(user, event, challengesCompletedOfCompletedTiers);
        } catch (ElasticsearchStatusException ex) {
            log.info("makePointsEligibleForUnlockedTierChallenges user {}, event {}, and challenges {} message {}", user.getUserId(), event.getEventId(), challengesCompletedOfCompletedTiers, ex.getMessage());
        }

        challengeAndTierDetailsDTO.setEventChallengeAndTiers(eventChallengeAndTierDTOs);
        challengeAndTierDetailsDTO.setTotalChallenges(allChallenges.size());
        challengeAndTierDetailsDTO.setCompletedChallenges(totalCompletedChallenges.get());
        return challengeAndTierDetailsDTO;
    }

    @Override
    public void setGamificationRewardsDetails(GamificationRewardDTO gamificationRewardDTO, JSONObject reward) {
        gamificationRewardDTO.setReward(reward.optString(REWARD_CONSTANT.NAME));
        gamificationRewardDTO.setRewardDescription(reward.optString(REWARD_CONSTANT.DESCRIPTION));
        gamificationRewardDTO.setImage(reward.optString(REWARD_IMAGE));
        gamificationRewardDTO.setRewardType(reward.optString(REWARD_CONSTANT.TYPE));
    }




    @Override
    public List<ChallengeBasicDetailsDTO> getChallengesBasicDetails(Event event) {
        log.info("getChallengesBasicDetails event {}", event.getEventId());

        List<EventChallengeDetail> eventChallengeDetails = eventChallengeRepoService.findAllChallengesByEventId(event.getEventId());
        Map<Long, Long> challengeToTierMap = challengeAndTierMappingRepository.findByEventId(event.getEventId()).stream()
                .collect(Collectors.toMap(
                        EventChallengeAndTierMapping::getChallengeId,
                        EventChallengeAndTierMapping::getTierId
                ));

        return eventChallengeDetails.stream()
                .map(challenge -> {
                    ChallengeBasicDetailsDTO dto = new ChallengeBasicDetailsDTO();
                    dto.setId(challenge.getId());
                    dto.setName(challenge.getChallengeName());
                    dto.setGoal(challenge.getGoalPoints());
                    dto.setType(challenge.getType());
                    dto.setTierId(challengeToTierMap.getOrDefault(challenge.getId(), null));
                    dto.setChallengeImage(challenge.getImage());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private List<EventChallengeDTO> convertToDto(List<EventChallengeDetail> challengeList, Event event) {
        return CollectionUtils.isEmpty(challengeList) ? Collections.emptyList() :
                challengeList.stream().map(eventChallengeDetail -> new EventChallengeDTO(eventChallengeDetail, event)).collect(Collectors.toList());
    }

}