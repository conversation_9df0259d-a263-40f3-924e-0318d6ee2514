package com.accelevents.services.impl;

import com.accelevents.auction.dto.ContactDto;
import com.accelevents.auction.dto.ContactsListDto;
import com.accelevents.auction.dto.ContactsListNameDto;
import com.accelevents.auction.dto.ContactsNameDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.AttributeType;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.CustomFormAttribute;
import com.accelevents.domain.session_speakers.CustomFormAttributeData;
import com.accelevents.domain.session_speakers.EventLimitRegistrationDomain;
import com.accelevents.dto.*;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.*;
import com.accelevents.ro.analytics.ROAttendeeAnalyticsService;
import com.accelevents.ro.smart_view.RODisplayViewService;
import com.accelevents.ro.user.repo.ROUserRepo;
import com.accelevents.ro.user.service.ROUserCustomRepositoryService;
import com.accelevents.services.*;
import com.accelevents.session_speakers.dto.CustomAttributeDetailsDto;
import com.accelevents.session_speakers.dto.CustomAttributesResponseDto;
import com.accelevents.session_speakers.services.CustomFormAttributeDataRepository;
import com.accelevents.session_speakers.services.CustomFormAttributeService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.GeneralUtils;
import com.google.gson.Gson;
import com.opencsv.CSVReader;
import com.twilio.rest.lookups.v1.PhoneNumber;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.accelevents.exceptions.ConflictException.ContactExceptionMsg.CONTACT_ALREADY_ADDED;
import static com.accelevents.exceptions.ConflictException.UserExceptionConflictMsg.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER;
import static com.accelevents.exceptions.ConflictException.UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.GeneralUtils.isValidEmailAddress;
import static io.swagger.v3.core.util.Constants.COMMA;

@Service
public class ContactServiceImpl implements ContactService {
    private static final Logger log = LoggerFactory.getLogger(ContactServiceImpl.class);

    @Autowired
    private ContactsRepository contactsRepository;

    @Autowired
    private EmailSuppressionService emailSuppressionService;

    @Autowired
    private ContactModuleSettingsService contactModuleSettingsService;

    @Autowired
    private  ContactsCommonRepoService contactsCommonRepoService;
    @Autowired
    private  ContactsListMappingRepository contactsListMappingRepository;
    @Autowired
    private  ContactsListRepository contactsListRepository;

    @Autowired
    private MessageToContactsRepository messageToContactsRepository;

    @Autowired
    private TwilioPhoneNumberValidateService twilioPhoneNumberValidateService;

    @Autowired
    private EventLimitRegistrationDomainRepository eventLimitRegistrationDomainRepository;

    @Autowired
    private ViewFilterDetailsRepository viewFilterDetailsRepository;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;
    @Autowired
    private DisplayViewRepository displayViewRepository;
    @Autowired
    private RODisplayViewService roDisplayViewService;
    @Autowired
    private AttendeeAnalyticsService attendeeAnalyticsService;
    @Autowired
    private ROAttendeeAnalyticsService roAttendeeAnalyticsService;
    @Autowired
    private ROUserCustomRepositoryService roUserCustomRepositoryService;
    @Autowired
    private ROUserRepo roUserRepository;
    @Autowired
    private CustomFormAttributeService customFormAttributeService;
    @Autowired
    private CustomFormAttributeDataRepository customFormAttributeDataRepository;

    protected void isValidHeaders(String[] header, List<String> requiredCustomAttribute,Map<String, CustomFormAttribute> fieldNameToCustomAttributeMap) {
        List<String> headerList;
        if (header == null) {
            log.info("Contacts upload file header is null");
            throw new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_UPLOAD_FILE_MISSING_HEADER);
        }else {
            headerList = new ArrayList<>(Arrays.asList(header));
            log.info("Contacts upload file header is {}",headerList);
        }

        // Required columns in exact order
        String[] required = {
                Constants.CONTACT_SPACE_ID,
                Constants.FIRST_NAME,
                Constants.LAST_NAME,
                Constants.EMAIL
        };
        List<String> defaultRequired = new ArrayList<>();
        for (int i = 0; i < required.length; i++) {
            if (!header[i].trim().equalsIgnoreCase(required[i])) {
                defaultRequired.add(required[i]);
            }
            headerList.remove(required[i]);
        }

        // Track duplicates (case-insensitive)
        Set<String> uniqueHeaders = new HashSet<>();
        Set<String> duplicateRecords = new HashSet<>();
        for (String col : header) {
            String normalized = col.trim();
            if (!uniqueHeaders.add(normalized)) {
                duplicateRecords.add(col.trim());
            }
            if(fieldNameToCustomAttributeMap.containsKey(normalized)){
                headerList.remove(normalized);
            }
            requiredCustomAttribute.removeIf(attr -> attr.equalsIgnoreCase(col.trim()));
        }

        // Check if any issues exist
        if (!defaultRequired.isEmpty() || !duplicateRecords.isEmpty() || !requiredCustomAttribute.isEmpty() || !headerList.isEmpty()) {
            NotAcceptableException.ContactExceptionMsg exceptionMessage =
                    NotAcceptableException.ContactExceptionMsg.CONTACT_UPLOAD_FILE_HEADER_NOT_CORRECT;

            // Prepare template-based message
            String message = Constants.CONTACT_UPLOAD_FILE_HEADER_NOT_CORRECT_ERROR_MESSAGE;

            // Replace required columns placeholder
            if (!defaultRequired.isEmpty() || !requiredCustomAttribute.isEmpty()) {
                String allMissing = Stream.concat(defaultRequired.stream(), requiredCustomAttribute.stream())
                        .collect(Collectors.joining(", "));
                String requiredColumnErrorMessage = "[ Required Fields: " + allMissing + " ]";
                message = message.replace(Constants.REQUIRED_COLUMNS_ERROR_MESSAGE_TEXT, requiredColumnErrorMessage);
            } else {
                message = message.replace(Constants.REQUIRED_COLUMNS_ERROR_MESSAGE_TEXT, "");
            }

            // Replace duplicate columns placeholder
            if (!duplicateRecords.isEmpty()) {
                String duplicateColumnErrorMessage = "[ Duplicate Fields: " + String.join(", ", duplicateRecords) + " ]";
                message = message.replace(Constants.DUPLICATE_COLUMNS_ERROR_MESSAGE_TEXT, duplicateColumnErrorMessage);
            } else {
                message = message.replace(Constants.DUPLICATE_COLUMNS_ERROR_MESSAGE_TEXT, "");
            }
            if(!headerList.isEmpty()){
                String unidentifiedFieldErrorMessage = "[ Unidentified Fields: " + String.join(", ", headerList) + " ]";
                message = message.replace(Constants.UNIDENTIFIED_COLUMNS_ERROR_MESSAGE_TEXT, unidentifiedFieldErrorMessage);
            }else {
                message = message.replace(Constants.UNIDENTIFIED_COLUMNS_ERROR_MESSAGE_TEXT, "");
            }

            exceptionMessage.setErrorMessage(message);
            exceptionMessage.setDeveloperMessage(message);
            throw new NotAcceptableException(exceptionMessage);
        }

    }



    protected String validate(ContactDto contactDto) {
        String error = "";
        if (StringUtils.isEmpty(contactDto.getFirstName()) || contactDto.getFirstName().length() > 50) {
            error = "First name must be not null OR First name must be less than 50 characters.";
        } else if (StringUtils.isEmpty(contactDto.getLastName()) || contactDto.getLastName().length() > 50) {
            error = "Last name must be not null OR Last name must be less than 50 characters.";
        } else if (StringUtils.isEmpty(contactDto.getEmail()) || contactDto.getEmail().length() > 75) {
            error = "Email must be not null OR Email must be less than 75 characters.";
        } else if (!isValidEmailAddress(contactDto.getEmail())) {
            error = "This is not valid email address.";
        } /*else if (StringUtils.isNotBlank(contactDto.getPhoneNumberCSV()) && !twilioPhoneNumberValidateService.validatePhoneNumber(contactDto.getPhoneNumberCSV())) {
            error = "This is not valid phone number.";
        }*/
        return error;
    }


    @Override
    @Transactional
    public Contacts addContact(ContactDto contactDto, Event event,Long contactsListId) {
        log.info("Add Contact using contactsListId {}, contactDto {}",contactsListId,contactDto);
        validateMaxContactUploadPerContactsList(event,contactsListId);

        //To Do validate cc email address and other fields
        ContactsList contactsList = contactsCommonRepoService.findContactsListByIdAndEventId(contactsListId, event.getEventId());
        Contacts contacts = contactsCommonRepoService.findContactsByEmailAndEventId(contactDto.getEmail(),event.getEventId());
        if(null!=contacts) {
            log.info("Contacts already existed with email {} and contactId {}",contacts.getEmail(),contacts.getId());
            ContactsListMapping contactsListMapping = contactsCommonRepoService.findContactsListMappingByContactsAndContactsList(contacts, contactsList);
            if (contactsListMapping != null) {
                throw new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_ALREADY_EXIST_IN_CONTACT_LIST);
            } else {
                contacts = contactDto.modifyContact(contacts, contactDto);
            }
        }else{
            contacts = contactDto.toEntity(event.getEventId());
        }

        // Validate and save custom attributes if provided
        if(contactDto.getContactAttributeDetailsDto() != null){
            // Validate custom field values based on their types
            validateContactCustomFields(contactDto.getContactAttributeDetailsDto(), event);

            CustomFormAttributeData customFormAttributeData = customFormAttributeService.saveCustomAttributeDataWithFormattedKeys(
                    contactDto.getContactAttributeDetailsDto(),
                    new CustomFormAttributeData(),
                    event,
                    AttributeType.CONTACT
            );
            contacts.setContactAttributeId(customFormAttributeData.getId());
        }

        contacts = contactsRepository.save(contacts);

        log.info("Contacts saved successfully and contactId {}",contacts.getId());
        contactsListMappingRepository.save(new ContactsListMapping(contactsList,contacts,new Date()));
        checkAndUpdateMessageToContactsIfRequired(new ArrayList<>(Collections.singleton(contacts.getId())),contactsListId,event.getEventId(),ADD);
        log.info("ContactsListMapping saved successfully");
        return contacts;
    }
    void validateMaxContactUploadPerContactsList(Event event, Long contactsListId) {
        Map<String,Integer> contactsAndMaxUploadContactsMap = getContactsAndMaxUploadContactsCount(event,contactsListId);
        if(contactsAndMaxUploadContactsMap.get(CONTACTS_EXIST_IN_CONTACTS_LIST)>=contactsAndMaxUploadContactsMap.get(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST)){
            throwMaxContactsReachedException(contactsAndMaxUploadContactsMap.get(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST));
        }
    }
    private void throwMaxContactsReachedException(Integer maxContactsUploadLimit) {
            NotAcceptableException.ContactExceptionMsg exceptionMsg = NotAcceptableException.ContactExceptionMsg.CONTACT_UPLOAD_MAX_LIMIT_REACHED;
            exceptionMsg.setErrorMessage(Constants.MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST_ERROR_MSG.replace("${MAX_CONTACTS}",String.valueOf(maxContactsUploadLimit)));
            exceptionMsg.setDeveloperMessage(Constants.MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST_ERROR_MSG.replace("${MAX_CONTACTS}",String.valueOf(maxContactsUploadLimit)));
            throw new NotAcceptableException(exceptionMsg);
    }
    private Map<String,Integer>  getContactsAndMaxUploadContactsCount(Event event,Long contactsListId){
        Integer currentContactUploaded = contactsListRepository.getContactsCountByEventIdAndId(event.getEventId(),contactsListId);
        Optional<ContactModuleSettings> contactModuleSettings = contactModuleSettingsService.findContactModuleSettingsByEvent(event);
        Integer maxContactAllowed = contactModuleSettings.map(ContactModuleSettings::getContactLimit).orElse(5);
        Map<String,Integer> contactsAndMaxUploadContactsMap  = new HashMap<>();
        contactsAndMaxUploadContactsMap.put(CONTACTS_EXIST_IN_CONTACTS_LIST,currentContactUploaded);
        contactsAndMaxUploadContactsMap.put(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST,maxContactAllowed);
        return  contactsAndMaxUploadContactsMap;
    }
    @Override
    public boolean isEmailRegistered(String email, Event event) {
        return getByEmailAndEventId(email, event).isPresent();
    }

    private Optional<Contacts> getByEmailAndEventId(String email, Event event) {
        return contactsRepository.findByEmailAndEventId(email, event.getEventId());
    }

    @Override
    public boolean isPhoneRegistered(long phoneNumber, Event event) {
        return getByPhoneNumberAndEventId(phoneNumber, event).isPresent();
    }

    private Optional<Contacts> getByPhoneNumberAndEventId(long phoneNumber, Event event) {
        return contactsRepository.findByPhoneNumberAndEventId(phoneNumber, event.getEventId());
    }

    @Override
    @Transactional
    public void removeContactFromContactsList(Long contactId, Event event,Long contactsListId) {
        log.info("remove contact from Contacts List using contactId {}, eventId {}, contactsListId {}",contactId,event.getEventId(),contactsListId);
        Contacts contact = contactsRepository.findById(contactId)
                .orElseThrow(() -> new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_NOT_EXISTS));
        ContactsList contactsList = contactsCommonRepoService.findContactsListByIdAndEventId(contactsListId, event.getEventId());
        Optional<ContactsListMapping> contactsListMappingOptional = contactsListMappingRepository.findByContactsAndContactsList(contact, contactsList);
        List<Contacts> contactInAllContactsMappingHavingCountOne = contactsRepository.getContactsInAllContactListsByContactIdsAndEventIdHavingCountOne(Collections.singletonList(contact.getId()),event.getEventId());
        contactsListMappingOptional.ifPresentOrElse(
                contactsListMapping -> {
                    contactsListMapping.setRecStatus(RecordStatus.DELETE);
                    contactsListMappingRepository.save(contactsListMapping);
                    if(!contactInAllContactsMappingHavingCountOne.isEmpty()){
                    contact.setRecStatus(RecordStatus.DELETE);
                    contactsRepository.save(contact);
                    }
                },
                () -> {
                    throw new NotFoundException(NotFoundException.ContactsList.CONTACT_NOT_FOUND_IN_CONTACTS_LIST);
                }
        );
        checkAndUpdateMessageToContactsIfRequired(new ArrayList<>(Collections.singleton(contact.getId())),contactsListId,event.getEventId(),DELETE);
        log.info("removed contact from contacts list successfully");

    }

    @Override
    @Transactional
    public void deleteContact(Long contactId, Event event) {
        log.info("Delete contact using contactId {}, eventId {}",contactId,event.getEventId());
        Contacts contact = contactsRepository.findById(contactId)
                .orElseThrow(() -> new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_NOT_EXISTS));
        contact.setRecStatus(RecordStatus.DELETE);
        contactsRepository.save(contact);
        log.info("Deleted contact successfully and contactId {}",contact.getId());
    }
    @Override
    public ContactDto getContact(Long contactId, Event event) {
        log.info("Get contact using contactId {}, eventId {}",contactId,event.getEventId());
        Contacts contact = contactsRepository.findById(contactId)
                .orElseThrow(() -> new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_NOT_EXISTS));
        return  new ContactDto().createContactDto(contact);
    }

    @Override
    @Transactional
    public void removeContactsFromContactsList(List<Long> contactIds, Event event, boolean deleteAll, Long contactsListId) {
        log.info("delete Contacts using eventId {}, contactsListId {}, contactIds {}", event.getEventId(), contactsListId, contactIds);
        if (!deleteAll && (contactIds == null || contactIds.isEmpty())) {
            throw new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_ID_OR_PARAMETERS_NOT_PROVIDED);
        }
        List<ContactsListMapping> contactsListMappingList;
        List<Contacts> contactInAllContactsMappingHavingCountOne;
        if(deleteAll) {
            contactsListMappingList = contactsListMappingRepository.findByEventIdAndContactsListId(event.getEventId(), contactsListId);
            contactIds = contactsRepository.getContactIdsByEventIdAndContactsListId(event.getEventId(),contactsListId);
            contactInAllContactsMappingHavingCountOne = contactsRepository.getContactsInAllContactListsByContactIdsAndEventIdHavingCountOne(contactIds,event.getEventId());
        } else{
            contactsListMappingList = contactsListMappingRepository.findByContactsIdAndEventIdAndContactsListId(contactIds, event.getEventId(), contactsListId);
            contactInAllContactsMappingHavingCountOne = contactsRepository.getContactsInAllContactListsByContactIdsAndEventIdHavingCountOne(contactIds,event.getEventId());
        }
        log.info("Contacts size {} to remove from Contacts List",contactsListMappingList.size());
        if (!contactsListMappingList.isEmpty()) {
            contactsListMappingList.forEach(e -> e.setRecStatus(RecordStatus.DELETE));
            contactsListMappingRepository.saveAll(contactsListMappingList);
            if(!contactInAllContactsMappingHavingCountOne.isEmpty()){
                updateContacts(contactInAllContactsMappingHavingCountOne);
            }
            log.info("removed contacts from contacts list successfully and contactsListMappingList size {}",contactsListMappingList.size());

        }
    }
    @Override
    @Transactional
    public void deleteContacts(List<Long> contactIds, Event event, boolean deleteAll) {
        log.info("delete Contacts using eventId {} ,contactIds {} ,deleteAll {}", event.getEventId(), contactIds,deleteAll);
        if (!deleteAll && (contactIds == null || contactIds.isEmpty())) {
            throw new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_ID_OR_PARAMETERS_NOT_PROVIDED);
        }
        List<Contacts> contacts;
        if (deleteAll) {
            contacts = contactsRepository.findAllByEventId(event.getEventId());
        } else{
            contacts = contactsRepository.findAllByEventIdAndIds(event.getEventId(), contactIds);
        }
        log.info("Contacts size to Delete {}",contacts.size());
        if (!contacts.isEmpty()) {
            contacts.forEach(e -> e.setRecStatus(RecordStatus.DELETE));
            contactsRepository.saveAll(contacts);
            contacts.forEach(e->checkAndUpdateMessageToContactsIfRequired(new ArrayList<>(Collections.singleton(e.getId())),null,event.getEventId(),DELETE));
            log.info("deleted contacts from contacts list successfully and contacts size {}",contacts.size());
        }
    }


    @Override
    @Transactional
    public void updateContact(ContactDto contactDto, Event event) {
        log.info("Update contact using eventId {}, contactDto {}",event.getEventId(),contactDto);
        Optional<Contacts> optionalContact = contactsRepository.findById(contactDto.getId());
        Contacts contact = optionalContact.orElseThrow(() -> new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_NOT_EXISTS));

        if (!StringUtils.isEmpty(contactDto.getEmail()) && !contactDto.getEmail().equals(contact.getEmail()) && isEmailRegistered(contactDto.getEmail(), event)) {
            throw new ConflictException(EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER);
        }

        if (contactDto.getPhoneNumber() > 0 && contactDto.getPhoneNumber() != contact.getPhoneNumber() && isPhoneRegistered(contactDto.getPhoneNumber(), event)) {
            throw new ConflictException(PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL);
        }

        contact = contactDto.toEntity(contact);

        // Validate and update custom attributes if provided
        if(contactDto.getContactAttributeDetailsDto() != null){
            // Validate custom field values based on their types
            validateContactCustomFields(contactDto.getContactAttributeDetailsDto(), event);

            CustomFormAttributeData customFormAttributeData = new CustomFormAttributeData();

            if (contact.getContactAttribute() != null){
                customFormAttributeData = contact.getContactAttribute();
            }

            customFormAttributeData = customFormAttributeService.saveCustomAttributeDataWithFormattedKeys(
                contactDto.getContactAttributeDetailsDto(),
                customFormAttributeData,
                event,
                AttributeType.CONTACT
            );
            contact.setContactAttributeId(customFormAttributeData.getId());
        }

        contactsRepository.save(contact);
        log.info("Updated contact successfully and contactId {}",contact.getId());

    }

    @Override
    @Transactional
    public List<ContactDto> getContactsByIdsAndContactsListId(Event event, List<Long> ids,Long contactsListId) {
        List<Contacts> contacts;
        if (!CollectionUtils.isEmpty(ids)) {
            contacts = contactsRepository.findAllByEventIdAndIdsAndContactsListId(event.getEventId(),contactsListId,ids);
        }else{
            contacts = contactsRepository.findAllByEventIdAndContactsListId(event.getEventId(),contactsListId);
        }
        List<String> emailSuppressionList = emailSuppressionService.getEmailSuppressionList(event);
        return contacts.stream().map(contact -> new ContactDto(contact, emailSuppressionList)).collect(Collectors.toList());
    }

    @Override
    public List<ContactDto> createContactList(MultipartFile multiPartFile, String listName, Event event, User user) throws IOException {
        listName = StringUtils.trim(listName);
        log.info("Creating ContactList by ListName {}, EventId {}, UserId {}",listName,event.getEventId(),user.getUserId());
        isContactsListNameExist(listName,event.getEventId());
        ContactsList contactsList =  contactsListRepository.save(new ContactsList(listName,event.getEventId(),user));
        log.info("contacts List saved successfully and contactListId {}",contactsList.getId());
        checkAllContactListSelectedInEmailLimitRegistrationDomainAndAddedIt(event.getEventId(),contactsList.getId());
        if(null == multiPartFile)
        {
           return Collections.emptyList();
        }

       return createContactsUsingCSV(event,multiPartFile,contactsList);
    }

    @Override
    public DataTableResponse getAllContactsDataByContactsListId(
            Event event,
            User user,
            Long contactsListId,
            PageSizeSearchObj pageSizeSearchObj
    ) {
        log.info("getAllContactsDataByContactsListId eventId {}, pageSizeSearchObj {}",
                event.getEventId(), pageSizeSearchObj);

        Pageable pageable = getSortablePageForContacts(pageSizeSearchObj);
        Page<Object[]> rawPage = contactsRepository
                .findContactsByEventIdAndContactsListId(
                        event.getEventId(),
                        contactsListId,
                        pageSizeSearchObj.getSearch(),
                        pageable
                );

        List<ContactDto> contacts = rawPage.getContent().stream()
                .map(ContactDto::new)
                .collect(Collectors.toList());

        // Populate custom attributes for each contact
        populateCustomAttributesForContacts(contacts, event, user);

        DataTableResponse response = new DataTableResponse();
        response.setRecordsTotal(rawPage.getTotalElements());
        response.setRecordsFiltered(contacts.size());
        response.setData(contacts);
        return response;
    }



    @Override
    public void validate(ContactDto contactDto, Event event) {
        if (!StringUtils.isEmpty(contactDto.getEmail()) && this.isEmailRegistered(contactDto.getEmail(), event)) {
            throw new ConflictException(CONTACT_ALREADY_ADDED);
        }
    }

    @Override
    public void deleteContactsByEventId(long eventId) {
        log.info("Start deleteContactsByEventId EventId {}",eventId );
        contactsRepository.deleteByEventId(eventId);
        log.info("End deleteContactsByEventId EventId {}",eventId );
    }
    private void isContactsListNameExist(String listName,Long eventId){
        if(contactsListRepository.isExistsByListNameAndEventId(eventId,listName)){
            throw new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_LIST_NAME_EXISTS);
        }
    }
    @Override
    @Transactional
    public DataTableResponse getAllContactsListData(Event event, PageSizeSearchObj pageSizeSearchObj, User user) {
        log.info("getAllContactsListData eventId {}, pageSizeSearchObj {}",event.getEventId(),pageSizeSearchObj);
        Pageable pageable =  getSortablePage(pageSizeSearchObj);
        Map<Long, ViewFilterDetails> viewFilterDetailsMap = viewFilterDetailsRepository.findAllByEventId(event.getEventId())
                .stream().collect(Collectors.toMap(ViewFilterDetails::getViewId, Function.identity()));
        Page<ContactsListDto> contactsListDtoPage = contactsListRepository.findAllContactListsWithContactCountByEventIdAndSearchString(event.getEventId(),pageSizeSearchObj.getSearch(), pageable, user.getUserId());
        List<ContactsListDto> contactsList = contactsListDtoPage.getContent();
        contactsList.forEach(contact -> {
            if (contact.getDisplayViewId() != null && contact.getTotalContactsCount() == 0L) {
                ViewFilterDetails viewFilterDetails = viewFilterDetailsMap.get(contact.getDisplayViewId());
                List<Long> ticketTypes = viewFilterDetails!=null ? GeneralUtils.convertCommaSeparatedToListLong(viewFilterDetails.getFilterTicketTypes()) : null;
                List<String> filterStatus = viewFilterDetails!=null ? GeneralUtils.convertCommaSeparatedToList(viewFilterDetails.getFilterStatus()) : null;
                ticketTypes = (ticketTypes == null || ticketTypes.isEmpty()) ? null : ticketTypes;
                filterStatus = (filterStatus == null || filterStatus.isEmpty()) ? null : filterStatus;
                Long totalContactsCount;
                if (viewFilterDetails != null && (StringUtils.isNotEmpty(viewFilterDetails.getAdvanceFilterJson()) || StringUtils.isNotEmpty(viewFilterDetails.getAdvanceStaticFilterJson()))){
                    AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsAdvanceFilterDto = roAttendeeAnalyticsService.filterAllAttendeeByAdvanceFilter(viewFilterDetails.getAdvanceFilterJson());
                    AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsStaticAdvanceFilterDto = roAttendeeAnalyticsService.filterAllAttendeeByAdvanceFilter(viewFilterDetails.getAdvanceStaticFilterJson());
                    String advanceFilterConditions = roUserCustomRepositoryService.buildQueryWithAdvanceFilterAttributes(attendeeAnalyticsAdvanceFilterDto,attendeeAnalyticsStaticAdvanceFilterDto,event.getEventId());
                    totalContactsCount = roUserCustomRepositoryService.getAllAttendeeCountByEventAndTicketStatusAndTicketTypesWithAdvanceFilter(event.getEventId(),STRING_EMPTY,getFilterStatus(filterStatus),getFilterTicketTypes(ticketTypes),advanceFilterConditions);
                } else {
                    totalContactsCount = eventTicketsRepository.getEventTicketCount(event.getEventId(), ticketTypes, filterStatus);
                }
                contact.setTotalContactsCount(totalContactsCount);
                contact.setSmartList(true);
                log.info("total contact counts for view count {} and view {}",totalContactsCount,contact.getDisplayViewId());
            }
        });
        return  getContactsListDtoDataTable(contactsListDtoPage);
    }

    @Override
    public List<ContactsListNameDto> getContactsListName(Event event, User loggedInUser) {
        log.info("getContactsListName using eventId {} userId {}", event.getEventId(), loggedInUser.getUserId());
            return contactsListRepository.getContactsListNameByEventId(event.getEventId(),loggedInUser.getUserId());
    }

    @Override
    public List<ContactsNameDto> getContactsName(Event event, Long contactsListId) {
        log.info("getContactsListName using eventId {}, contactsListId {}",event.getEventId(),contactsListId);
        return contactsRepository.getContactsNameByEventIdAndContactsListId(event.getEventId(),contactsListId);
    }

    private DataTableResponse getContactsListDtoDataTable(Page<ContactsListDto> contactsListDtoPage) {

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setRecordsTotal(contactsListDtoPage.getTotalElements());
        dataTableResponse.setRecordsFiltered(contactsListDtoPage.getContent().size());
        dataTableResponse.setData(contactsListDtoPage.getContent());
        return dataTableResponse;
    }
    @Override
    public List<ContactDto> uploadContactsCSVIntoContactsList(Long contactsListId, MultipartFile multipartFile, Event event) throws IOException {
        log.info("uploadContactsCSVIntoContactsList contactsListId {}, EventId {}",contactsListId,event.getEventId());
        ContactsList contactsList = contactsCommonRepoService.findContactsListByIdAndEventId(contactsListId,event.getEventId());
        return createContactsUsingCSV(event,multipartFile,contactsList);
    }

    @Override
    public ResponseDto renameContactsList(ContactsListDto contactsListDto, Event event, User user) {
        try {
            log.info("Renaming contacts list for event ID {}, user ID {}, with details: {}",
                    event.getEventId(), user.getUserId(), contactsListDto);

            if(StringUtils.isNotBlank(contactsListDto.getListName())){
                isContactsListNameExist(contactsListDto.getListName().trim(),event.getEventId());
            }else {
                throw new NotAcceptableException(NotAcceptableException.ContactExceptionMsg.CONTACT_LIST_NAME_IS_BLANK);
            }

            ContactsList contactsList = contactsCommonRepoService.findContactsListByIdAndEventId(
                    contactsListDto.getId(), event.getEventId());

            log.info("Old contacts list name: {}", contactsList.getListName());
            contactsList.setListName(contactsListDto.getListName().trim());
            contactsList.setUpdatedDate(new Date());
            contactsListRepository.save(contactsList);

            return new ResponseDto(SUCCESS, CONTACTS_LIST_NAME_RENAME_MESSAGE);

        } catch (NotAcceptableException | NotFoundException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Error while renaming contacts list: {}", ex.getMessage(), ex);
            return new ResponseDto(FAIL, COMMON_ERROR_MESSAGE);
        }
    }

    @Override
    @Transactional
    public ResponseDto deleteContactsList(Event event, User user, Long contactsListId) {
        ContactsList contactsList = contactsCommonRepoService.findContactsListByIdAndEventId(contactsListId,event.getEventId());
        throwExceptionIfSmartContactUsed(contactsList.getDisplayViewId());
        try{
            log.info("deleteContactsList eventId {}, userId {}, contactsListId {}",event.getEventId(),user.getUserId(),contactsListId);
            List<ContactsListMapping> contactsListMappingList = contactsListMappingRepository.findAllByContactsList(contactsList);
            if(!contactsListMappingList.isEmpty()) {
                List<Long> contactIds = contactsRepository.getContactIdsByEventIdAndContactsListId(event.getEventId(),contactsListId);
                List<Contacts> contactIdsInAllContactsMappingHavingCountOne = contactsRepository.getContactsInAllContactListsByContactIdsAndEventIdHavingCountOne(contactIds,event.getEventId());
                if(!contactIdsInAllContactsMappingHavingCountOne.isEmpty()){
                   updateContacts(contactIdsInAllContactsMappingHavingCountOne);
                }
                contactsListMappingList.forEach(contactsListMapping -> contactsListMapping.setRecStatus(RecordStatus.DELETE));
                contactsListMappingRepository.saveAll(contactsListMappingList);
            }
            contactsList.setUpdatedDate(new Date());
            contactsList.setRecStatus(RecordStatus.DELETE);
            contactsList.setDisplayViewId(null);
            contactsListRepository.save(contactsList);
            checkContactsListExistInEngageMailAndRemovedIt(event.getEventId(),contactsListId);
            checkContactsListExistInEmailLimitRegistrationDomainAndRemovedIt(event.getEventId(),contactsListId);
        }catch (Exception ex){
            log.error("deleteContactsList contactsListId {}, ErrorMsg {}",contactsListId,ex.getMessage());
            return new ResponseDto(FAIL,Constants.COMMON_ERROR_MESSAGE);
        }
        return new ResponseDto(SUCCESS,CONTACTS_LIST_DELETE_MESSAGE);
    }
    private void checkContactsListExistInEngageMailAndRemovedIt(Long eventId,Long contactsListId){
        log.info("checkContactsListExistInMailAndRemovedIt eventId {}, contactsListId {}",eventId,contactsListId);
        List<MessageToContacts> messageToContactsList = messageToContactsRepository.findAllByEventIdAndContactsListId(eventId,contactsListId);
        log.info("Records for update messageToContactsList size {}",messageToContactsList.size());
        if(!messageToContactsList.isEmpty()){
            messageToContactsList.forEach(messageToContacts -> {
                messageToContacts.setContactsListId(null);
                messageToContacts.setMembersSelected(null);
            });
            messageToContactsRepository.saveAll(messageToContactsList);
        }
    }
    private Pageable getSortablePage(PageSizeSearchObj pageSizeSearchObj) {
        String sortBy;
        switch (pageSizeSearchObj.getSortBy()) {
            case LIST_NAME:
                sortBy = LIST_NAME;
                break;
            case UPDATED_DATE:
                sortBy = UPDATED_DATE;
                break;
            case CREATED_BY:
                sortBy = CREATED_BY;
                break;
            case TOTAL_CONTACTS_COUNT:
                sortBy = TOTAL_CONTACTS_COUNT;
                break;
            default:
                log.info("No matched found in getSortablePage using sortBy{}",pageSizeSearchObj.getSortBy());
                sortBy = ID;
                break;
        }
        return PageRequest.of(pageSizeSearchObj.getPage(),pageSizeSearchObj.getSize(),pageSizeSearchObj.getSortDirection(), sortBy);
    }
    private Pageable getSortablePageForContacts(PageSizeSearchObj pageSizeSearchObj) {
        String sortBy;
        switch (pageSizeSearchObj.getSortBy()) {
            case STRING_FIRST_NAME:
                sortBy = STRING_FIRST_NAME;
                break;
            case STRING_LAST_NAME:
                sortBy = STRING_LAST_NAME;
                break;
            case PHONE_NUMBER_KEY:
                sortBy = PHONE_NUMBER_KEY;
                break;
            case STRING_EMAIL:
                sortBy = STRING_EMAIL;
                break;
            default:
                log.info("No matched found in getSortablePageForContacts using sortBy{}",pageSizeSearchObj.getSortBy());
                sortBy = CONTACTS_LIST_ID;
                break;
        }
        return PageRequest.of(pageSizeSearchObj.getPage(),pageSizeSearchObj.getSize(),pageSizeSearchObj.getSortDirection(), sortBy);
    }

    protected List<ContactDto> createContactsUsingCSV(Event event, MultipartFile multipartFile, ContactsList contactsList) throws IOException {
        List<ContactDto> invalidContacts = new ArrayList<>();
        List<ContactDto> validContacts = new ArrayList<>();
        List<Contacts> contactsToSave = new ArrayList<>();
        Map<String, AttributeValueType> fieldTypeToCustomAttributeMap = new HashMap<>();
        Set<Long> existingContactIds = new HashSet<>();
        Set<String> existingEmails = new HashSet<>();
        Map<String, CustomFormAttribute> fieldNameToCustomAttributeMap = new HashMap<>();
        List<CustomFormAttribute> customFormAttributeList ;
        try (CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(multipartFile.getInputStream())))) {
            String[] header = csvReader.readNext();
            if (header == null) {
                throw new NotAcceptableException(NotAcceptableException.SessionExceptionMsg.CAN_NOT_UPLOAD_EMPTY_FILE);
            }
            customFormAttributeList = getEnabledCustomAttributesWithoutImageAndConditionalQuestion(event);
            List<String> requiredCustomAttribute = customFormAttributeList.stream()
                    .filter(CustomFormAttribute::isRequired)
                    .map(attr -> attr.getName().trim().toLowerCase()) // adjust getter if needed
                    .collect(Collectors.toList());

            if (header.length > 4 || !CollectionUtils.isEmpty(customFormAttributeList)) {
                fieldTypeToCustomAttributeMap = getContactFieldTypeMap(customFormAttributeList);
                fieldNameToCustomAttributeMap = getContactFieldAttributeMap(customFormAttributeList);
            }
            isValidHeaders(header,requiredCustomAttribute,fieldNameToCustomAttributeMap);
            String[] csvRow;
            while ((csvRow = csvReader.readNext()) != null && csvRow.length >= 3) {

                ContactDto contactDto = createContactDtoFromCsvRow(csvRow, header, fieldTypeToCustomAttributeMap,fieldNameToCustomAttributeMap);
                String contactIdStr = csvRow[0].trim();
                String email = contactDto.getEmail();

                if (StringUtils.isEmpty(contactIdStr)) {
                    // Empty → treat as ID = 0
                    contactDto.setId(0L);
                    contactDto.setContactId(contactIdStr);
                    if (existingEmails.contains(email)) {
                        contactDto.setError("Duplicate email for Contact Id 0: " + email);
                    } else {
                        existingEmails.add(email);
                    }
                } else if (!org.apache.commons.lang3.math.NumberUtils.isDigits(contactIdStr)) {
                    // Non-numeric → invalid
                    contactDto.setContactId(contactIdStr);
                    contactDto.setError("Contact Id should be a numeric positive number");
                } else {
                    // Numeric value
                    Long contactId = Long.parseLong(contactIdStr);
                    contactDto.setId(contactId);
                    contactDto.setContactId(contactIdStr);

                    if (contactId > 0) {
                        // Positive ID → check duplicate IDs only
                        if (existingContactIds.contains(contactId)) {
                            contactDto.setError("Duplicate Contact Id: " + contactIdStr);
                        } else {
                            existingContactIds.add(contactId);
                        }
                    } else if (contactId == 0) {
                        // ID = 0 → check duplicate emails
                        if (existingEmails.contains(email)) {
                            contactDto.setError("Duplicate email for Contact Id 0: " + email);
                        } else {
                            existingEmails.add(email);
                        }
                    }
                }


                // Validate contact after duplicate checks
                String error = validate(contactDto);
                if (StringUtils.isEmpty(error) && StringUtils.isEmpty(contactDto.getError())) {
                    validContacts.add(contactDto);
                } else {
                    if (StringUtils.isNotEmpty(error)) {
                        contactDto.setError(error);
                    }
                    invalidContacts.add(contactDto);
                }
            }
        }

        if (!validContacts.isEmpty()) {
            saveValidContacts(event, validContacts, contactsList, contactsToSave, invalidContacts, existingContactIds, customFormAttributeList,existingEmails);
        }
        return invalidContacts;
    }


    private ContactDto createContactDtoFromCsvRow(String[] csvRow,String[] header,Map<String, AttributeValueType> fieldTypeToCustomAttributeMap,Map<String, CustomFormAttribute> fieldNameToCustomAttributeMap) {
        ContactDto contactDto = new ContactDto();

        contactDto.setContactId(csvRow[0].trim());
        contactDto.setFirstName(csvRow[1].trim());
        contactDto.setLastName(csvRow[2].trim());
        contactDto.setEmail(csvRow[3].trim());
       // contactDto.setPhoneNumberCSV(csvRow[4].trim());

        // Phone number validation
       /* if (StringUtils.isNotBlank(contactDto.getPhoneNumberCSV())) {
            contactDto.setPhoneNumberCSV(checkAndAddPlusSign(contactDto.getPhoneNumberCSV()));
            log.info("createContactDtoFromCsvRow | phoneNumberCSV {}",contactDto.getPhoneNumberCSV());
            *//*if(isValidPhoneNumber(contactDto.getPhoneNumberCSV()) && twilioPhoneNumberValidateService.validatePhoneNumber(contactDto.getPhoneNumberCSV())) {
                PhoneNumber phoneNumber = twilioPhoneNumberValidateService.getPhoneDetail(contactDto.getPhoneNumberCSV());
                if (phoneNumber != null) {*//*
                    //contactDto.setCountryCode(phoneNumber.getCountryCode());
                    //Long nationalFormatPhoneNumber = Long.parseLong(phoneNumber.getNationalFormat().replaceAll("\\D", ""));
                    contactDto.setPhoneNumber(0L);
                    contactDto.setPhoneNumberCSV(String.valueOf(0L));
               // } else {
                    contactDto.setPhoneNumberCSV(csvRow[4].trim());
                    contactDto.setPhoneNumber(Long.parseLong(contactDto.getPhoneNumberCSV().replaceAll("\\D", "")));
               // }
            *//*} else {
                contactDto.setPhoneNumberCSV(csvRow[4].trim());
                contactDto.setError("Not a valid phone number.");
            }*//*
        }*/

        // Capture any extra columns dynamically (if CSV has more than 5)
        if (csvRow.length > 4) {
            List<AttributeKeyValueDto> attributeKeyValueDtoList = new ArrayList<>();

            for (int i = 4; i < csvRow.length; i++) {
                String columnName = header[i].trim();
                String columnValue = csvRow[i].trim();

                AttributeKeyValueDto attributeKeyValueDto = new AttributeKeyValueDto();
                attributeKeyValueDto.setKey(columnName);
                attributeKeyValueDto.setValue(columnValue);

                // Validate only if fieldTypeToCustomAttributeMap contains the column
                if(PHONE.equalsIgnoreCase(columnName)){
                    contactDto.setPhoneNumberCSV(columnValue);
                    if (StringUtils.isNotBlank(contactDto.getPhoneNumberCSV())) {
                        // Ensure phone number starts with a plus sign if needed
                        contactDto.setPhoneNumberCSV(checkAndAddPlusSign(contactDto.getPhoneNumberCSV()));
                        log.info("createContactDtoFromCsvRow | phoneNumberCSV {}", contactDto.getPhoneNumberCSV());

                        // Validate phone number format and Twilio validation
                        if (isValidPhoneNumber(contactDto.getPhoneNumberCSV()) && twilioPhoneNumberValidateService.validatePhoneNumber(contactDto.getPhoneNumberCSV())) {

                            PhoneNumber phoneNumber = twilioPhoneNumberValidateService.getPhoneDetail(contactDto.getPhoneNumberCSV());

                            if (phoneNumber != null) {
                                // Set country code if needed
                                contactDto.setCountryCode(phoneNumber.getCountryCode());

                                // Extract numeric part of national format
                                Long nationalFormatPhoneNumber = Long.parseLong( phoneNumber.getNationalFormat().replaceAll("\\D", ""));

                                contactDto.setPhoneNumber(nationalFormatPhoneNumber);
                                contactDto.setPhoneNumberCSV(String.valueOf(nationalFormatPhoneNumber));

                            } else {
                                // Twilio did not return details, fallback to raw CSV value
                                contactDto.setPhoneNumberCSV(columnValue);
                                contactDto.setPhoneNumber(Long.parseLong(contactDto.getPhoneNumberCSV().replaceAll("\\D", "")));
                            }

                        } else {
                            // Phone number invalid
                            contactDto.setPhoneNumberCSV(columnValue);
                            try {
                                contactDto.setPhoneNumber(Long.parseLong(contactDto.getPhoneNumberCSV().replaceAll("\\D", "")));
                            } catch (NumberFormatException e) {
                                contactDto.setPhoneNumber(0L);
                            }
                            contactDto.setError("Not a valid phone number.");
                        }
                    }

                }
                AttributeValueType fieldType = fieldTypeToCustomAttributeMap.get(columnName);
                if (fieldTypeToCustomAttributeMap.containsKey(columnName)) {
                    CustomFormAttribute customFormAttribute = fieldNameToCustomAttributeMap.get(columnName);
                    if(null!=customFormAttribute && customFormAttribute.isRequired() && StringUtils.isEmpty(columnValue))    {
                        contactDto.setError(String.format("Field '%s' is required field.", columnName));
                    }
                    else if (!isValidFieldValueByType(attributeKeyValueDto, fieldType,fieldNameToCustomAttributeMap)) {
                        contactDto.setError(getErrorMessageByAttributeType(fieldType,columnName));
                    }
                }
                if(!AttributeValueType.PHONE_NUMBER.equals(fieldType)) {
                    attributeKeyValueDtoList.add(attributeKeyValueDto);
                }else if(AttributeValueType.MULTIPLE_CHOICE.equals(fieldType)){

                }else{

                }
            }
            contactDto.setContactAttributeDetailsDto(new CustomAttributeDetailsDto(attributeKeyValueDtoList));
        }
        return contactDto;
    }

    private  boolean isValidPhoneNumber(String phoneNumber) {
        String regex = "^\\+\\d{1,3}\\d{10}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }
    private String checkAndAddPlusSign(String phoneNumberCSV){
        if (!phoneNumberCSV.startsWith("+")) {
            return "+" + phoneNumberCSV;
        } else {
            return phoneNumberCSV;
        }
    }

    void saveValidContacts(Event event, List<ContactDto> validContacts, ContactsList contactsList, List<Contacts> contactsToSave, List<ContactDto> invalidContacts
            ,Set<Long> existingContactIds, List<CustomFormAttribute> customFormAttributeList,Set<String> existingEmails) {
        Set<Contacts> existingContacts = getExistingContactsByEmailsOrIds(event, existingEmails,existingContactIds);
        Map<String, Contacts> contactsByEmail = existingContacts.stream()
                .filter(contact -> StringUtils.isNotBlank(contact.getEmail()))
                .collect(Collectors.toMap(
                        Contacts::getEmail,
                        contact -> contact,
                        (c1, c2) -> c1 // keep first if duplicate emails
                ));

        Map<Long, Contacts> contactsById = new HashMap<>();

        for (Contacts contact : existingContacts) {
            if (existingContactIds.contains(contact.getId())) {
                contactsById.putIfAbsent(contact.getId(), contact);
            }
        }

        List<String> emailListToSave = new ArrayList<>();
        Map<String,Integer>  contactsAndMaxUploadContactsCountMap = getContactsAndMaxUploadContactsCount(event,contactsList.getId());
        int maxContactsCountPerContactsList = contactsAndMaxUploadContactsCountMap.get(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST);
        int existingContactsCountPerContactsList = contactsAndMaxUploadContactsCountMap.get(CONTACTS_EXIST_IN_CONTACTS_LIST) ;
        boolean contactsPerContactsLimitExceed = false;

        for (ContactDto contactDto : validContacts) {

            String email = contactDto.getEmail();
            Long contactId = contactDto.getId();
            log.info("saveValidContacts contactDto {}",contactDto);

            if(emailListToSave.contains(email)) {
                contactDto.setError("Contact already present in the CSV file");
                invalidContacts.add(contactDto);
            } else if (contactsByEmail.containsKey(email) || contactsById.containsKey(contactId)) {
                Contacts contacts;
                if(contactsById.containsKey(contactId)){
                    contacts = contactsById.get(contactId);
                   if(!contacts.getEmail().equalsIgnoreCase(email)){
                       contactDto.setError("Contact Id and email does not match with the existing contacts.");
                       invalidContacts.add(contactDto);
                       break;
                   }
                }else {
                    contacts = contactsByEmail.get(email);
                    if(contactDto.getId()>0 && existingContactIds.contains(contactDto.getId())){
                        contactDto.setError("Contact Id not found in the existing contacts.");
                        invalidContacts.add(contactDto);
                        break;
                    }
                }
                if(null!=contactDto.getContactAttributeDetailsDto()) {
                    createOrUpdateContactsCustomAttributes(contacts, contactDto.getContactAttributeDetailsDto().getAttributes(), customFormAttributeList);
                }
                contactsToSave.add(contactDto.modifyContact(contacts, contactDto));
                emailListToSave.add(email);
            }else {
                if(contactDto.getId()>0){
                    contactDto.setError("Contact Id not found in the existing contacts.");
                    invalidContacts.add(contactDto);
                    break;
                } else if(maxContactsCountPerContactsList <= existingContactsCountPerContactsList) {
                    contactsPerContactsLimitExceed = true;
                    break;
                }
                Contacts contacts = contactDto.toEntity(event.getEventId());
                if(null!=contactDto.getContactAttributeDetailsDto()) {
                    contacts.setContactAttributeId(createOrUpdateContactsCustomAttributes(contacts, contactDto.getContactAttributeDetailsDto().getAttributes(), customFormAttributeList));
                }
                contactsToSave.add(contacts);
                emailListToSave.add(email);
                existingContactsCountPerContactsList++;
            }
        }

        if(!contactsToSave.isEmpty()) {

            Iterable<Contacts> savedContacts = contactsRepository.saveAll(contactsToSave);

            List<Long> existingMappings = contactsListMappingRepository.getContactIdsByContactsListIdAndContactsId(
                    StreamSupport.stream(savedContacts.spliterator(), false).collect(Collectors.toList()),
                    contactsList
            );

            List<Contacts> contactsToActuallySave = StreamSupport.stream(savedContacts.spliterator(), false)
                    .filter(contact -> !existingMappings.contains(contact.getId()))
                    .collect(Collectors.toList());

            List<ContactsListMapping> contactsListMappings = contactsToActuallySave.stream()
                    .map(contact -> new ContactsListMapping(contactsList, contact, new Date()))
                    .collect(Collectors.toList());

            if(!contactsListMappings.isEmpty()){
                contactsListMappingRepository.saveAll(contactsListMappings);
            }

            checkAndUpdateMessageToContactsIfRequired(StreamSupport.stream(savedContacts.spliterator(),false)
                    .map(Contacts::getId)
                    .collect(Collectors.toList()),contactsList.getId(),event.getEventId(),ADD);
            if(contactsPerContactsLimitExceed){
                throwMaxContactsReachedException(contactsAndMaxUploadContactsCountMap.get(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST));
            }
        }
    }

    private int getContactsToUploadWithInUploadLimit(Map<String,Integer> contactsAndMaxUploadContactsCountMap) {
        if (contactsAndMaxUploadContactsCountMap.get(CONTACTS_EXIST_IN_CONTACTS_LIST) >= contactsAndMaxUploadContactsCountMap.get(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST)) {
            throwMaxContactsReachedException(contactsAndMaxUploadContactsCountMap.get(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST));
        }
            return contactsAndMaxUploadContactsCountMap.get(MAX_UPLOAD_CONTACTS_PER_CONTACTS_LIST) - contactsAndMaxUploadContactsCountMap.get(CONTACTS_EXIST_IN_CONTACTS_LIST);
    }
    public Set<Contacts> getExistingContactsByEmailsOrIds(Event event,Set<String> emailSet,Set<Long> existingContactIds) {
        return contactsRepository.findAllByEventIdAndIdsOrEmails(event.getEventId(),existingContactIds,emailSet);
    }
    void checkAndUpdateMessageToContactsIfRequired(List<Long> contactsIds, Long contactsListId, Long eventId, String operation){
        List<MessageToContacts> messageToContactsList;
        if(ADD.equals(operation)){
            messageToContactsList = messageToContactsRepository.findByEventIdAndContactsListIdAndStatusNotSENT(eventId,contactsListId);
            if(!messageToContactsList.isEmpty()){
                updateMessageToContacts(ADD,messageToContactsList,contactsIds);
            }
        } else if (DELETE.equals(operation)) {
            messageToContactsList = messageToContactsRepository.findByEventIdAndStatusNotSENT(eventId);
            updateMessageToContacts(DELETE,messageToContactsList,contactsIds);
        }
    }
    private void updateMessageToContacts(String operation,List<MessageToContacts> messageToContactsList,List<Long> contactsIds){
        log.info("UpdateMessageToContacts operation {}, messageToContactsList size {}, contactsIds size {}",operation,messageToContactsList.size(),contactsIds.size());
        if(ADD.equals(operation)){
            messageToContactsList = messageToContactsList.stream()
                    .map(messageToContacts -> {
                        messageToContacts.setMembersSelected(messageToContacts.getMembersSelected().concat(",").concat(GeneralUtils.convertLongListToCommaSeparated(contactsIds)));
                        return messageToContacts;
                    })
                    .collect(Collectors.toList());

        }else if (DELETE.equals(operation)){
            messageToContactsList = messageToContactsList.stream()
                    .map(messageToContacts -> {messageToContacts.setMembersSelected(updateList(messageToContacts,contactsIds));
                            return messageToContacts;})
                    .collect(Collectors.toList());
        }
        if(!messageToContactsList.isEmpty()){
            log.info("Saving messageTo contacts list size {}",messageToContactsList.size());
            messageToContactsRepository.saveAll(messageToContactsList);
        }else {
            log.info("No message to contacts is present to save");
        }
    }
    private String updateList(MessageToContacts messageToContact,List<Long> contactsIds) {
        log.info("Update List selectedMembers {} and  contactsIds {}",messageToContact.getMembersSelected(),contactsIds);
        List<String> updatedList = GeneralUtils.convertCommaSeparatedToList(messageToContact.getMembersSelected());
        updatedList.remove(String.valueOf(contactsIds.get(0)));
        return GeneralUtils.convertListToCommaSeparated(updatedList);
    }

    private void updateContacts(List<Contacts> contactInAllContactsMappingHavingCountOne){
        log.info("Delete contacts using list of contactInAllContactsMappingHavingCountOne size {}",contactInAllContactsMappingHavingCountOne.size());
        contactInAllContactsMappingHavingCountOne.forEach(e -> e.setRecStatus(RecordStatus.DELETE));
        contactsRepository.saveAll(contactInAllContactsMappingHavingCountOne);
    }

    private void checkContactsListExistInEmailLimitRegistrationDomainAndRemovedIt(Long eventId, Long contactListId) {
        log.info("checkContactsListExistInEmailLimitRegistrationDomainAndRemovedIt eventId {}, contactListId {}", eventId, contactListId);
        processEventLimitRegistrationDomain(eventId, contactListId, false);
    }

    private void checkAllContactListSelectedInEmailLimitRegistrationDomainAndAddedIt(Long eventId, Long contactListId) {
        log.info("checkAllContactListSelectedInEmailLimitRegistrationDomainAndAddedIt eventId {}, contactsListId {}", eventId, contactListId);
        processEventLimitRegistrationDomain(eventId, contactListId, true);
    }

    private void processEventLimitRegistrationDomain(Long eventId, Long contactListId, boolean add) {
        try {
            List<EventLimitRegistrationDomain> eventLimitRegistrationDomainList = eventLimitRegistrationDomainRepository.findAllByEventId(eventId);

            if (eventLimitRegistrationDomainList.isEmpty()) {
                return;
            }

            for(EventLimitRegistrationDomain eventLimitRegistrationDomain : eventLimitRegistrationDomainList) {
            log.info("Found eventLimitRegistrationDomain with id {} and contactListIds {} for contactListId {}",
                    eventLimitRegistrationDomain.getId(), eventLimitRegistrationDomain.getContactListIds(), contactListId);

            if (StringUtils.isNotBlank(eventLimitRegistrationDomain.getContactListIds()) || add) {
                List<Long> contactListIds = GeneralUtils.convertCommaSeparatedToListLong(eventLimitRegistrationDomain.getContactListIds());
                boolean shouldSave = false;

                if (add && eventLimitRegistrationDomain.isAllContactListsSelected() && !contactListIds.contains(contactListId)) {
                    contactListIds.add(contactListId);
                    shouldSave = true;
                } else if (!add && contactListIds.contains(contactListId)) {
                    contactListIds.remove(contactListId);
                    shouldSave = true;
                }

                if (shouldSave) {
                    eventLimitRegistrationDomain.setContactListIds(GeneralUtils.convertLongListToCommaSeparated(contactListIds));
                    eventLimitRegistrationDomain.setUpdatedAt(new Date());
                }
            }
            }
            eventLimitRegistrationDomainRepository.saveAll(eventLimitRegistrationDomainList);
        } catch (Exception ex) {
            log.error("Error processing eventId {}, contactListId {}, add {} , errorMsg {}",
                    eventId, contactListId, add, ex.getMessage());
        }
    }

    private void throwExceptionIfSmartContactUsed(Long displayViewId) {
        if (null != displayViewId) {
            Integer count = displayViewRepository.checkSmartContactListUsed(displayViewId);
            log.info("throwExceptionIfSmartContactUsed displayViewId {} count {} date {}",displayViewId,count,new Date());
            if (count > 0) {
                throw new ConflictException(ConflictException.DisplayViewConflict.DISPLAY_SMART_CONTACT_LIST_USED);
            }
        }
    }

    @Override
    public Long getAudienceCount(Event event, Long contactsListId,User loggedInUser) {
        Optional<ContactsList> contactListOpt = contactsListRepository.findById(contactsListId);
        if (contactListOpt.isPresent())
        {
            ContactsList contactList = contactListOpt.get();
            DisplayViewDto displayViewDto = roDisplayViewService.findDisplayViewById(contactList.getDisplayViewId(), event, loggedInUser);
            ViewFilterDetailsDTO viewFilterDetailsDto = displayViewDto.getViewFilterDetailsDTO();
            AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsAdvanceFilterDto = roAttendeeAnalyticsService.filterAllAttendeeByAdvanceFilter(viewFilterDetailsDto.getAdvanceFilterJson());
            AttendeeAnalyticsAdvanceFilterDto attendeeAnalyticsStaticAdvanceFilterDto = roAttendeeAnalyticsService.filterAllAttendeeByAdvanceFilter(viewFilterDetailsDto.getAdvanceStaticFilterJson());
            String advanceFilterConditions = roUserCustomRepositoryService.buildQueryWithAdvanceFilterAttributes(attendeeAnalyticsAdvanceFilterDto,
                    attendeeAnalyticsStaticAdvanceFilterDto,
                    event.getEventId());
            Long count;
            if (StringUtils.isNotBlank(advanceFilterConditions)) {
                count = roUserCustomRepositoryService.getAllAttendeeCountByEventAndTicketStatusAndTicketTypesWithAdvanceFilter(event.getEventId(),"",getFilterStatus(viewFilterDetailsDto.getFilterStatus()),getFilterTicketTypes(viewFilterDetailsDto.getFilterTicketTypes()),advanceFilterConditions);
            } else {
                count = roUserRepository.getAllAttendeeCountByEventAndTicketStatusAndTicketTypes(event.getEventId(),"",getFilterStatus(viewFilterDetailsDto.getFilterStatus()),getFilterTicketTypes(viewFilterDetailsDto.getFilterTicketTypes()));
            }
            return count;
        }
        return 0L;
    }
    private List<Long> getFilterTicketTypes(List<Long> filterTicketTypes) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(filterTicketTypes)) {
            return null;
        }
        return filterTicketTypes;
    }

    private List<String> getFilterStatus(List<String> filterStatus) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(filterStatus)) {
            return null;
        }
        return filterStatus;
    }

    /**
     * Populates custom attributes for contacts by fetching contact attribute details
     * @param contacts List of ContactDto objects to populate
     * @param event Event object for context
     */
    private void populateCustomAttributesForContacts(List<ContactDto> contacts, Event event, User user) {
        if (CollectionUtils.isEmpty(contacts)) {
            return;
        }

        // Collect all contact attribute IDs that need to be fetched
        List<Long> contactAttributeIds = contacts.stream()
                .filter(contact -> contact.getContactAttributeId() != null && contact.getContactAttributeId() > 0)
                .map(ContactDto::getContactAttributeId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(contactAttributeIds)) {
            return;
        }

        // Batch fetch all custom form attribute data
        List<CustomFormAttributeData> customFormAttributeDataList = customFormAttributeService.getCustomFormAttributeDataByIds(contactAttributeIds);

        // Create a map for quick lookup
        Map<Long, CustomFormAttributeData> attributeDataMap = customFormAttributeDataList.stream()
                .collect(Collectors.toMap(CustomFormAttributeData::getId, Function.identity()));

        // Get all custom form attributes for this event to create name lookup map
        List<CustomFormAttribute> eventAttributes = customFormAttributeService.getCustomFormAttributeOfEvent(event, user, null, AttributeType.CONTACT);
        Map<Long, String> attributeIdToNameMap = eventAttributes.stream()
                .collect(Collectors.toMap(CustomFormAttribute::getId, CustomFormAttribute::getName));

        // Create a map of attribute ID to value type for SINGLE_CHECKBOX handling
        Map<Long, AttributeValueType> attributeIdToValueTypeMap = eventAttributes.stream()
                .collect(Collectors.toMap(CustomFormAttribute::getId, CustomFormAttribute::getAttributeValueType));

        // Get only enabled attributes for filtering
        Set<Long> enabledAttributeIds = eventAttributes.stream()
                .filter(CustomFormAttribute::isEnabled)
                .map(CustomFormAttribute::getId)
                .collect(Collectors.toSet());

        // Populate contact attribute details for each contact
        contacts.forEach(contact -> {
            if (contact.getContactAttributeId() != null && contact.getContactAttributeId() > 0) {
                CustomFormAttributeData customFormAttributeData = attributeDataMap.get(contact.getContactAttributeId());
                if (customFormAttributeData != null) {
                    CustomAttributesResponseDto responseDto = customFormAttributeService.getCustomAttributeResponseDto(customFormAttributeData);
                    CustomAttributeDetailsDto detailsDto = convertToCustomAttributeDetailsDtoWithNames(responseDto, attributeIdToNameMap, enabledAttributeIds, attributeIdToValueTypeMap);
                    contact.setContactAttributeDetailsDto(detailsDto);
                }
            }
        });
    }

    /**
     * Converts CustomAttributesResponseDto to CustomAttributeDetailsDto with name replacement
     * and filters for only enabled attributes
     * @param responseDto The response DTO to convert
     * @param attributeIdToNameMap Map of attribute ID to name for replacement
     * @param enabledAttributeIds Set of enabled attribute IDs for filtering
     * @param attributeIdToValueTypeMap Map of attribute ID to value type for SINGLE_CHECKBOX handling
     * @return CustomAttributeDetailsDto with only enabled attributes
     */
    private CustomAttributeDetailsDto convertToCustomAttributeDetailsDtoWithNames(
            CustomAttributesResponseDto responseDto,
            Map<Long, String> attributeIdToNameMap,
            Set<Long> enabledAttributeIds,
            Map<Long, AttributeValueType> attributeIdToValueTypeMap) {

        CustomAttributeDetailsDto detailsDto = new CustomAttributeDetailsDto();

        if (responseDto.getAttributes() != null) {
            List<AttributeKeyValueDto> attributes = responseDto.getAttributes().entrySet().stream()
                    .filter(entry -> {
                        // Extract attribute ID from key and check if it's enabled
                        Long attributeId = extractAttributeIdFromKey(entry.getKey());
                        return attributeId != null && enabledAttributeIds.contains(attributeId);
                    })
                    .map(entry -> {
                        String newKey = getAttributeNameFromKey(entry.getKey(), attributeIdToNameMap);
                        String value = entry.getValue();

                        // Handle SINGLE_CHECKBOX attributes with empty values
                        Long attributeId = extractAttributeIdFromKey(entry.getKey());
                        if (attributeId != null && attributeIdToValueTypeMap.containsKey(attributeId)) {
                            AttributeValueType valueType = attributeIdToValueTypeMap.get(attributeId);
                            if (AttributeValueType.SINGLE_CHECKBOX.equals(valueType) && (value == null || value.trim().isEmpty())) {
                                value = CHECKED_FALSE;
                            }
                        }

                        return new AttributeKeyValueDto(newKey, value);
                    })
                    .collect(Collectors.toList());
            detailsDto.setAttributes(attributes);
        }

        return detailsDto;
    }

    /**
     * Extracts attribute ID from a key like "$CustomTextTest_201"
     * @param key The key to extract ID from
     * @return The attribute ID or null if not found
     */
    private Long extractAttributeIdFromKey(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }

        // Handle keys in format like "$CustomTextTest_201"
        int underscoreIndex = key.lastIndexOf('_');
        if (underscoreIndex > 0 && underscoreIndex < key.length() - 1) {
            String idPart = key.substring(underscoreIndex + 1);
            try {
                return Long.parseLong(idPart);
            } catch (NumberFormatException e) {
                log.debug("Could not extract attribute ID from key: {}", key);
            }
        }
        return null;
    }

    /**
     * Gets attribute name from key like "$Birthdate_216" -> "Birthdate"
     */
    private String getAttributeNameFromKey(String key, Map<Long, String> attributeIdToNameMap) {
        if (StringUtils.isBlank(key)) return key;

        int underscoreIndex = key.lastIndexOf('_');
        if (underscoreIndex > 0) {
            try {
                Long attributeId = Long.parseLong(key.substring(underscoreIndex + 1));
                return attributeIdToNameMap.getOrDefault(attributeId, key);
            } catch (NumberFormatException e) {
                return key;
            }
        }
        return key;
    }

    /**
     * Validates contact custom field values based on AttributeValueType from custom_form_attribute table
     * Performs field validation for Text/Long Text/Number/Date/Dropdown/Image/Multiple Choice/Single Checkbox/Phone Number fields
     * Validates Other Email Address and CC Email Address fields for proper email format
     */
    private void validateContactCustomFields(CustomAttributeDetailsDto customAttributeDetailsDto, Event event) {
        if (customAttributeDetailsDto == null) {
            return;
        }

        // Fetch custom form attributes once and create both maps
        List<CustomFormAttribute> contactAttributes = customFormAttributeService.getCustomFormAttributeOfEvent(event, null, null, AttributeType.CONTACT);
        Map<String, AttributeValueType> fieldTypeMap = getContactFieldTypeMap(contactAttributes);
        Map<String, CustomFormAttribute> fieldAttributeMap = getContactFieldAttributeMap(contactAttributes);

        // Validate attributes using field type-based validation
        if (!CollectionUtils.isEmpty(customAttributeDetailsDto.getAttributes())) {
            for (AttributeKeyValueDto attribute : customAttributeDetailsDto.getAttributes()) {
                validateCustomFieldAttributeByType(attribute, fieldTypeMap, fieldAttributeMap);
            }
        }
    }

    private List<CustomFormAttribute> getEnabledCustomAttributesWithoutImageAndConditionalQuestion(Event event) {
        try {
            Set<String> required = Set.of(
                    Constants.FIRST_NAME,
                    Constants.LAST_NAME,
                    Constants.EMAIL
            );

            List<CustomFormAttribute> customFormAttributeList =
                    customFormAttributeService.getEnabledCustomAttributes(
                            event.getEventId(),
                            null,
                            AttributeType.CONTACT,
                            Arrays.asList(AttributeValueType.CONDITIONAL_QUE, AttributeValueType.IMAGE)
                    );

            // Filter out attributes that are in required set
            return customFormAttributeList.stream()
                    .filter(attr -> !required.contains(attr.getName()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error fetching contact attributes for event {}: {}",
                    event != null ? event.getEventId() : null, e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    private Map<String, AttributeValueType> getContactFieldTypeMap(List<CustomFormAttribute> attributes) {
        if (attributes == null || attributes.isEmpty()) {
            return Collections.emptyMap();
        }

        return attributes.stream()
                .collect(Collectors.toMap(
                        CustomFormAttribute::getName,
                        CustomFormAttribute::getAttributeValueType,
                        (existing, replacement) -> existing // Keep first in case of duplicates
                ));
    }
    /**
     * Gets a map of field names to their CustomFormAttribute for contact attributes
     */
    private Map<String, CustomFormAttribute> getContactFieldAttributeMap(List<CustomFormAttribute> contactAttributes) {
        try {
            return contactAttributes.stream()
                    .collect(Collectors.toMap(
                            CustomFormAttribute::getName,
                            Function.identity(),
                            (existing, replacement) -> existing // Keep first occurrence in case of duplicates
                    ));
        } catch (Exception e) {
            log.error("Error creating contact field attribute map: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }


    /**
     * Validates individual custom field attributes based on their AttributeValueType
     */
    private void validateCustomFieldAttributeByType(AttributeKeyValueDto attribute, Map<String, AttributeValueType> fieldTypeMap, Map<String, CustomFormAttribute> fieldAttributeMap) {
        String fieldName = attribute.getKey();
        String fieldValue = attribute.getValue();

        if (StringUtils.isBlank(fieldValue)) {
            return;
        }

        // Get the field type from the map
        AttributeValueType fieldType = fieldTypeMap.get(fieldName);

        if (fieldType != null) {
            validateFieldByType(attribute, fieldType, fieldAttributeMap);
        } else {
            // Field not found in our system - this is an unknown attribute
            NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
            String errorMessage = "Unknown attribute: " + fieldName;
            exception.setErrorMessage(errorMessage);
            exception.setDeveloperMessage(errorMessage);
            throw new NotAcceptableException(exception);
        }

    }

    /**
     * Validates the value of a given attribute based on its type.
     * Throws {@link NotAcceptableException} if the value is invalid.
     * @param attributeKeyValueDto the attribute key-value pair to validate
     * @param fieldType the type of the attribute
     * @throws NotAcceptableException if the attribute value is invalid
     */
    private void validateFieldByType(AttributeKeyValueDto attributeKeyValueDto, AttributeValueType fieldType, Map<String, CustomFormAttribute> fieldAttributeMap) {
        String fieldName = attributeKeyValueDto.getKey();

        try {
            boolean isValid = isValidFieldValueByType(attributeKeyValueDto, fieldType,fieldAttributeMap);

            if (!isValid) {
                switch (fieldType) {
                    case TEXT:
                        throwValidateFieldLength(fieldName,"255");
                    case LONG_TEXT:
                         throwValidateFieldLength(fieldName,"500");
                    case DATE:
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DATE_FORMAT_NOT_VALID);
                    case PHONE_NUMBER:
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PHONE_NUMBER);
                    case EMAIL:
                    case OTHER_EMAIL_ADDRESS:
                    case CC_EMAIL_ADDRESS:
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_EMAIL);
                    case NUMBER:
                        throwFieldValidationException(fieldName);
                    case DROPDOWN:
                        validateDropdownField(attributeKeyValueDto, fieldAttributeMap);
                        break;
                    case MULTIPLE_CHOICE:
                        validateMultipleChoiceField(attributeKeyValueDto, fieldAttributeMap);
                        break;
                    case SINGLE_CHECKBOX:
                        validateSingleCheckboxField(attributeKeyValueDto, fieldAttributeMap);
                        break;
                    default:
                        break; // No validation for IMAGE, DROPDOWN, MULTIPLE_CHOICE, SINGLE_CHECKBOX
                }
            }

        } catch (NotAcceptableException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error validating field {} of type {}: {}", fieldName, fieldType, e.getMessage(), e);
        }
    }

    /**
     * Throws a NotAcceptableException with a field-specific message.
     *
     * @param fieldName the field name
     */
    private void throwFieldValidationException(String fieldName) {
        String errorMessage = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE.getErrorMessage().replace("${field}", fieldName);
        NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE.setErrorMessage(errorMessage);
        NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE.setDeveloperMessage(errorMessage);
        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE);
    }

    private void throwValidateFieldLength(String fieldName, String maxLength) {

           String errorMessage = NotAcceptableException.NotAceptableExeceptionMSG.FIELD_LENGTH_SHOULD_BE_LESS_THAN.getErrorMessage()
                    .replace("${field}", fieldName)
                    .replace("${characterCount}", String.valueOf(maxLength));
           NotAcceptableException.NotAceptableExeceptionMSG.FIELD_LENGTH_SHOULD_BE_LESS_THAN.setErrorMessage(errorMessage);
           NotAcceptableException.NotAceptableExeceptionMSG.FIELD_LENGTH_SHOULD_BE_LESS_THAN.setDeveloperMessage(errorMessage);
           throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.FIELD_LENGTH_SHOULD_BE_LESS_THAN);
    }

        private boolean isValidFieldValueByType(AttributeKeyValueDto attributeKeyValueDto, AttributeValueType fieldType,Map<String, CustomFormAttribute> fieldNameToCustomAttributeMap) {
        String fieldName = attributeKeyValueDto.getKey();
        String fieldValue = attributeKeyValueDto.getValue();
        try {
            switch (fieldType) {
                case TEXT:
                    return StringUtils.isBlank(fieldValue) || fieldValue.length() <= 255;

                case LONG_TEXT:
                    return StringUtils.isBlank(fieldValue) || fieldValue.length() <= 500;

                case DATE:
                    return StringUtils.isBlank(fieldValue)
                            || DateUtils.isDateValidStrict(fieldValue, Constants.DATE_FORMAT_ONLY_MONTH);
                case EMAIL:
                case OTHER_EMAIL_ADDRESS:
                    return StringUtils.isBlank(fieldValue)
                            || isValidEmailAddress(fieldValue.trim());

                case CC_EMAIL_ADDRESS:
                    if (StringUtils.isBlank(fieldValue)) return true;
                    String[] ccEmails = fieldValue.trim().split(COMMA);
                    for (String ccEmail : ccEmails) {
                        if (StringUtils.isNotBlank(ccEmail.trim()) && !isValidEmailAddress(ccEmail.trim())) {
                            return false;
                        }
                    }
                    break;
                case DROPDOWN:
                    return isValidDropdownFieldValue(attributeKeyValueDto, fieldNameToCustomAttributeMap);
                case MULTIPLE_CHOICE:
                    return isValidMultipleChoiceFieldValue(attributeKeyValueDto, fieldNameToCustomAttributeMap);
                case SINGLE_CHECKBOX:
                    return isValidSingleCheckboxFieldValue(attributeKeyValueDto, fieldNameToCustomAttributeMap);
                case NUMBER:
                    return fieldValue.length() <= 255 && fieldValue.matches("\\d+");
                default:
                    return true; // No validation applied
            }
        } catch (Exception e) {
            log.error("Error validating field {} of type {} : |  errorMsg : {}", fieldName, fieldType, e.getMessage(), e);
            return false;
        }
        return true;
    }

    private String getErrorMessageByAttributeType(AttributeValueType attributeValueType, String fieldName) {
        switch (attributeValueType) {
            case TEXT:
                return fieldName + " should not be more than 255 characters.";
            case LONG_TEXT:
                return fieldName + " should not be more than 500 characters.";
            case DATE:
                return fieldName + " should be a valid date in format " + Constants.DATE_FORMAT_ONLY_MONTH + ".";
            case PHONE_NUMBER:
                return fieldName + " should be a valid phone number.";
            case EMAIL:
            case OTHER_EMAIL_ADDRESS:
                return fieldName + " should be a valid email address.";
            case CC_EMAIL_ADDRESS:
                return fieldName + " should contain valid comma-separated email addresses.";
            case DROPDOWN:
                return fieldName + " should be a valid dropdown option.";
            case MULTIPLE_CHOICE:
                return fieldName + " should contain valid multiple choice options.";
            case SINGLE_CHECKBOX:
                return fieldName + " should be a valid single checkbox value.";
            case NUMBER:
                return fieldName + " should be a numeric value with no more than 255 digits.";
            default:
                return fieldName + " has an invalid value.";
        }
    }



    private boolean isValidDropdownFieldValue(AttributeKeyValueDto attributeKeyValueDto, Map<String, CustomFormAttribute> fieldNameToCustomAttributeMap) {
        String fieldName = attributeKeyValueDto.getKey();
        String fieldValue = attributeKeyValueDto.getValue();

        CustomFormAttribute customFormAttribute = fieldNameToCustomAttributeMap.get(fieldName);
        if (customFormAttribute == null) {
            return false;
        }
        List<String> validOptions = parseOptionsFromDefaultValue(fieldNameToCustomAttributeMap.get(attributeKeyValueDto.getKey()).getDefaultValue());
        if(customFormAttribute.isRequired() && StringUtils.isEmpty(fieldValue)){
            return false;
        }
        return StringUtils.isBlank(fieldValue) || validOptions.isEmpty() || validOptions.contains(fieldValue);
    }

    private boolean isValidMultipleChoiceFieldValue(AttributeKeyValueDto attributeKeyValueDto, Map<String, CustomFormAttribute> fieldNameToCustomAttributeMap) {
        String fieldName = attributeKeyValueDto.getKey();
        String fieldValue = attributeKeyValueDto.getValue();

        CustomFormAttribute customFormAttribute = fieldNameToCustomAttributeMap.get(fieldName);
        if (customFormAttribute == null) {
            return false; // Unknown field
        }

        List<String> validOptions = parseOptionsFromDefaultValue(customFormAttribute.getDefaultValue());
        if(customFormAttribute.isRequired() && StringUtils.isEmpty(fieldValue)){
            return false;
        }
        if (!validOptions.isEmpty()) {
            String[] selectedValues = fieldValue.split("[|,]");
            for (String selectedValue : selectedValues) {
                if (selectedValue != null && !selectedValue.trim().isEmpty() && !validOptions.contains(selectedValue.trim())) {
                    return false; // Invalid value found
                }
            }
            attributeKeyValueDto.setValue(Arrays.stream(selectedValues)
                    .map(String::trim)
                    .collect(Collectors.joining(",")));
        }
        return true;
    }

    private boolean isValidSingleCheckboxFieldValue(AttributeKeyValueDto attribute, Map<String, CustomFormAttribute> fieldAttributeMap) {
        String fieldName = attribute.getKey();
        String fieldValue = attribute.getValue();

        CustomFormAttribute customFormAttribute = fieldAttributeMap.get(fieldName);
        if (customFormAttribute == null) {
            return false; // Unknown field
        }

        if(customFormAttribute.isRequired() && StringUtils.isEmpty(fieldValue)){
            return false;
        }
        List<String> validOptions = Arrays.asList(Constants.TRUE,Constants.FALSE, CHECKED.toUpperCase(),Constants.UNCHECKED.toUpperCase());
        return (StringUtils.isEmpty(attribute.getValue())) || (!StringUtils.isEmpty(customFormAttribute.getDefaultValue()) && validOptions.contains(fieldValue.toUpperCase( ))); // Value is not valid
    }


    /**
     * Validates dropdown field value against allowed options
     */
    private void validateDropdownField(AttributeKeyValueDto attributeKeyValueDto, Map<String, CustomFormAttribute> fieldAttributeMap) {
        String fieldName = attributeKeyValueDto.getKey();
        String fieldValue = attributeKeyValueDto.getValue();

        try {
            // Get the custom form attribute to access the options
            CustomFormAttribute customFormAttribute = fieldAttributeMap.get(fieldName);
            if (customFormAttribute == null) {
                NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                String errorMessage = "Unknown attribute: " + fieldName;
                exception.setErrorMessage(errorMessage);
                exception.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(exception);
            }

            List<String> validOptions = parseOptionsFromDefaultValue(customFormAttribute.getDefaultValue());
            if (!validOptions.isEmpty() && !validOptions.contains(fieldValue)) {
                NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                String errorMessage = exception.getErrorMessage()
                        .replace("${field}", fieldName)
                        .replace("${type}", "dropdown option. Valid options: " + String.join(", ", validOptions));
                exception.setErrorMessage(errorMessage);
                exception.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(exception);
            }
        } catch (NotAcceptableException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error validating dropdown field {} with value {}: {}", fieldName, fieldValue, e.getMessage(), e);
        }
    }

    /**
     * Validates multiple choice field value against allowed options
     */
    private void validateMultipleChoiceField(AttributeKeyValueDto attribute, Map<String, CustomFormAttribute> fieldAttributeMap) {
        String fieldName = attribute.getKey();
        String fieldValue = attribute.getValue();

        try {
            // Get the custom form attribute to access the options
            CustomFormAttribute customFormAttribute = fieldAttributeMap.get(fieldName);
            if (customFormAttribute == null) {
                NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                String errorMessage = "Unknown attribute: " + fieldName;
                exception.setErrorMessage(errorMessage);
                exception.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(exception);
            }

            List<String> validOptions = parseOptionsFromDefaultValue(customFormAttribute.getDefaultValue());
            if (!validOptions.isEmpty()) {
                // Multiple choice values are typically separated by pipe (|) or comma
                String[] selectedValues = fieldValue.split("[|,]");
                for (String selectedValue : selectedValues) {
                    String trimmedValue = selectedValue.trim();
                    if (StringUtils.isNotBlank(trimmedValue) && !validOptions.contains(trimmedValue)) {
                        NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                        String errorMessage = exception.getErrorMessage()
                                .replace("${field}", fieldName)
                                .replace("${type}", "multiple choice option. Valid options: " + String.join(", ", validOptions));
                        exception.setErrorMessage(errorMessage);
                        exception.setDeveloperMessage(errorMessage);
                        throw new NotAcceptableException(exception);
                    }
                }
            }
        } catch (NotAcceptableException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error validating multiple choice field {} with value {}: {}", fieldName, fieldValue, e.getMessage(), e);
        }
    }

    /**
     * Validates single checkbox field value against configured options
     */
    private void validateSingleCheckboxField(AttributeKeyValueDto attribute, Map<String, CustomFormAttribute> fieldAttributeMap) {
        String fieldName = attribute.getKey();
        String fieldValue = attribute.getValue();

        try {
            // Get the custom form attribute to access the options
            CustomFormAttribute customFormAttribute = fieldAttributeMap.get(fieldName);
            if (customFormAttribute == null) {
                NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                String errorMessage = "Unknown attribute: " + fieldName;
                exception.setErrorMessage(errorMessage);
                exception.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(exception);
            }

            List<String> validOptions = parseOptionsFromDefaultValue(customFormAttribute.getDefaultValue());
            if (!validOptions.isEmpty() && !validOptions.contains(fieldValue)) {
                NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                String errorMessage = exception.getErrorMessage()
                        .replace("${field}", fieldName)
                        .replace("${type}", "single checkbox option. Valid options: " + String.join(", ", validOptions));
                exception.setErrorMessage(errorMessage);
                exception.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(exception);
            }
        } catch (NotAcceptableException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error validating single checkbox field {} with value {}: {}", fieldName, fieldValue, e.getMessage(), e);
        }
    }



    /**
     * Parses dropdown options from defaultValue JSON string
     * Expected format: {"defaultvalues":{"defaultvalue":[{"label":"drop1","value":"drop1","optionId":"..."}],"sortvalue":""}}
     */
    @Override
    public List<String> parseOptionsFromDefaultValue(String defaultValue) {
        List<String> options = new ArrayList<>();

        if (StringUtils.isBlank(defaultValue)) {
            return options;
        }

        try {
            JSONObject jsonObject = new JSONObject(defaultValue);
            if (jsonObject.has(DEFAULT_VALUES_WITHOUT_SPACE)) {
                JSONObject defaultvalues = jsonObject.getJSONObject(DEFAULT_VALUES_WITHOUT_SPACE);
                if (defaultvalues.has(DEFAULT_VALUE_WITHOUT_SPACE)) {
                    org.json.JSONArray defaultvalueArray = defaultvalues.getJSONArray(DEFAULT_VALUE_WITHOUT_SPACE);
                    for (int i = 0; i < defaultvalueArray.length(); i++) {
                        JSONObject option = defaultvalueArray.getJSONObject(i);
                        String value = option.optString(VALUE_SMALL);
                        if (StringUtils.isNotBlank(value)) {
                            options.add(value);
                        }
                    }
                }
            }
        } catch (JSONException e) {
            log.warn("Failed to parse dropdown options from defaultValue: {}", defaultValue, e);
        }
        return options;
    }

    private long createOrUpdateContactsCustomAttributes(
            Contacts contacts,
            List<AttributeKeyValueDto> attributeKeyValueDtoList ,
            List<CustomFormAttribute> customFormAttributeList
    ) {
        // Initialize or reuse existing attribute data
        CustomFormAttributeData customFormAttributeData = contacts.getContactAttribute();
        if (customFormAttributeData == null) {
            customFormAttributeData = new CustomFormAttributeData();
        }

        // Convert existing attributes to editable map
        CustomAttributesResponseDto contactsAttributeResponseDto = customFormAttributeService.getCustomAttributeResponseDto(customFormAttributeData);

        Map<String, String> existingAttributes =
                Optional.ofNullable(contactsAttributeResponseDto.getAttributes())
                        .orElseGet(HashMap::new);

        // Only proceed if custom form attributes are available
        if (!CollectionUtils.isEmpty(customFormAttributeList)) {

            Map<String, String> newAttributesMap = !CollectionUtils.isEmpty(attributeKeyValueDtoList)
                ? convertAttributeKeyValueDtoToMap(attributeKeyValueDtoList)
                : Collections.emptyMap();

            for (CustomFormAttribute customFormAttribute : customFormAttributeList) {
                String originalName = customFormAttribute.getName();
                // Format the key to match /add API behavior: ShortTextCustom_278
                String formattedKey = GeneralUtils.formatAttributeKey(originalName, customFormAttribute.getId(), STRING_UNDERSCORE);

                String value = newAttributesMap.get(customFormAttribute.getName());

                if(AttributeValueType.SINGLE_CHECKBOX.equals(customFormAttribute.getAttributeValueType())){
                    if(StringUtils.isEmpty(value)){
                        value = customFormAttribute.getDefaultValue();
                    }else if(TRUE.equalsIgnoreCase(value) || CHECKED.equalsIgnoreCase(value)){
                        value = TRUE.toLowerCase();
                    }else {
                        value = FALSE.toLowerCase();
                    }
                }
                // Update the attribute if it was provided in CSV, is a SINGLE_CHECKBOX, or already exists
                if (newAttributesMap.containsKey(originalName) ||
                    existingAttributes.containsKey(formattedKey)) {
                    existingAttributes.put(formattedKey, value);
                }
            }
        }

        // Update response DTO
        contactsAttributeResponseDto.setAttributes(existingAttributes);

        // Build attribute JSON map to match /add API format: {"questions":{},"attributes":{}}
        Map<String, Object> attributeMap = new HashMap<>();
        attributeMap.put(TICKETING.ATTRIBUTES, CollectionUtils.isEmpty(contactsAttributeResponseDto.getAttributes())
                ? Collections.emptyMap()
                : contactsAttributeResponseDto.getAttributes());
        attributeMap.put(TICKETING.QUESTIONS, CollectionUtils.isEmpty(contactsAttributeResponseDto.getQuestions())
                ? Collections.emptyMap()
                : contactsAttributeResponseDto.getQuestions()
        );

        // Save updated JSON
        customFormAttributeData.setJsonValue(new Gson().toJson(attributeMap, Map.class));
        return customFormAttributeDataRepository.save(customFormAttributeData).getId();
    }

    private Map<String, String> convertAttributeKeyValueDtoToMap(List<AttributeKeyValueDto> attributeKeyValueDtoList) {
        if (CollectionUtils.isEmpty(attributeKeyValueDtoList)) {
            return Collections.emptyMap();
        }

        // Keep the last value for duplicate keys; trim keys/values and skip blank keys
        return attributeKeyValueDtoList.stream()
                .filter(Objects::nonNull)
                .map(dto -> new AbstractMap.SimpleEntry<>(
                        dto.getKey() == null ? null : dto.getKey().trim(),
                        dto.getValue() == null ? null : dto.getValue().trim())
                )
                .filter(entry -> StringUtils.isNotBlank(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldVal, newVal) -> newVal, // keep last
                        LinkedHashMap::new // preserve insertion order (optional)
                ));
    }
}
