package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.auction.dto.*;
import com.accelevents.billing.chargebee.dto.CAPUsageDto;
import com.accelevents.billing.chargebee.dto.EventChargebeePlanConfigDto;
import com.accelevents.billing.chargebee.dto.PlatformConfigDto;
import com.accelevents.billing.chargebee.enums.ChargeConfigNames;
import com.accelevents.billing.chargebee.enums.ChargebeeEntitlements;
import com.accelevents.billing.chargebee.repo.*;
import com.accelevents.billing.chargebee.repo.impl.ChargebeePlanRepoServiceImpl;
import com.accelevents.billing.chargebee.service.*;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.billing.chargebee.util.ChargeBeeUtils;
import com.accelevents.common.dto.ContactDto;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.exhibitors.ExhibitorSetting;
import com.accelevents.domain.session_speakers.MeetingOptions;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.virtual.CreditType;
import com.accelevents.domain.virtual.HostEventOfflinePaymentConfig;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.enums.OrganizerRole;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.enums.StaffRole;
import com.accelevents.enums.UserRole;
import com.accelevents.event.*;
import com.accelevents.events.templates.repositories.EventsTemplatesRepository;
import com.accelevents.exceptions.*;
import com.accelevents.exceptions.ConflictException.UserExceptionConflictMsg;
import com.accelevents.exceptions.NotAcceptableException.*;
import com.accelevents.exceptions.NotFoundException.EventNotFound;
import com.accelevents.exceptions.NotFoundException.ItemNotFound;
import com.accelevents.exceptions.NotFoundException.ModuleNotFound;
import com.accelevents.exceptions.NotFoundException.NotFound;
import com.accelevents.exhibitors.dto.ExpoEventDto;
import com.accelevents.exhibitors.services.ExhibitorSettingsRepoService;
import com.accelevents.helpers.CalculateFees;
import com.accelevents.helpers.StripeUtil;
import com.accelevents.helpers.TemplateId;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.hubspot.service.HubspotContactService;
import com.accelevents.hubspot.service.HubspotCustomLabelsAssociationService;
import com.accelevents.hubspot.service.HubspotEventService;
import com.accelevents.hubspot.service.HubspotOrganizerService;
import com.accelevents.messages.*;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.TwilioTextMessagePrepareService;
import com.accelevents.payment.HostEventOfflinePaymentConfigService;
import com.accelevents.perfomance.dto.EventFeatureDTO;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.repositories.*;
import com.accelevents.repository.ROWhiteLabelSettingRepository;
import com.accelevents.ro.common.ROBeeFreePagesService;
import com.accelevents.ro.event.service.ROConfirmationEmailService;
import com.accelevents.ro.event.service.ROEventDesignDetailService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.eventTicket.ROTicketingTypeTicketService;
import com.accelevents.ro.payment.ROPayFlowConfigService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROPhoneNumberService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneInterestService;
import com.accelevents.services.repo.helper.*;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.services.ConfirmationPagesService;
import com.accelevents.session_speakers.services.CustomTicketInvoiceDesignService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.spreedly.dto.SpreedlyGatewayType;
import com.accelevents.staff.dto.LiveItemsCodeAndAmountDto;
import com.accelevents.staff.dto.StaffLiveAuctionPurchaseDto;
import com.accelevents.ticketing.dto.TicketAnalytics;
import com.accelevents.ticketing.dto.TicketAnalyticsDto;
import com.accelevents.ticketing.dto.UTMTrackSourceDto;
import com.accelevents.utils.TimeZone;
import com.accelevents.utils.*;
import com.accelevents.virtualevents.dto.VirtualEventSettingsDTO;
import com.accelevents.virtualevents.dto.VirtualEventTabsDTO;
import com.google.gson.Gson;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.squareup.square.models.Error;
import com.squareup.square.models.Location;
import com.stripe.exception.*;
import com.stripe.model.Account;
import com.stripe.model.Coupon;
import com.stripe.model.Plan;
import com.stripe.model.Subscription;
import com.stripe.model.oauth.TokenResponse;
import com.stripe.net.OAuth;
import com.stripe.net.RequestOptions;
import com.twilio.base.ResourceSet;
import com.twilio.exception.ApiException;
import com.twilio.exception.TwilioException;
import com.twilio.rest.api.v2010.account.IncomingPhoneNumber;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Instant;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.persistence.EntityManager;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.billing.chargebee.dto.CAPUsageDto.convertJSONToObject;
import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.PROFESSIONAL_2023;
import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.STARTER_2023;
import static com.accelevents.domain.enums.EnumEventFeatures.*;
import static com.accelevents.domain.enums.EnumSessionFormat.*;
import static com.accelevents.enums.PlanConfigNames.*;
import static com.accelevents.enums.UserRole.*;
import static com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG.CONNECTED_TO_OTHER_PAYMENT_GATEWAY;
import static com.accelevents.exceptions.NotAcceptableException.PaymentCreationExceptionMsg.*;
import static com.accelevents.exceptions.NotAcceptableException.TicketingExceptionMsg.CAN_NOT_CHANGE_AFTER_EVENT_ENDED;
import static com.accelevents.utils.Constants.ENTERPRISE;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.EventFeatures.ENABLE_AUTO_ASSIGNED_SEQUENCE;
import static com.accelevents.utils.EventFeatures.ENABLE_SESSIONS_SPEAKERS;
import static com.accelevents.utils.EventTypeUtils.isEnableFeature;
import static com.accelevents.utils.FeeConstants.*;
import static com.accelevents.utils.GeneralUtils.*;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class EventServiceImpl implements EventService {

	private static final org.slf4j.Logger log = LoggerFactory.getLogger(EventServiceImpl.class);


	@Autowired
	private GetStreamService getStreamService;
	@Autowired
	private EventTicketsRepository eventTicketsRepository;
	@Autowired
	private EventRepository eventRepository;
	@Autowired
	private ItemCategoryService itemCategoryService;
	@Autowired
	private AuctionService auctionService;
	@Autowired
	private EventDesignDetailService eventDesignDetailService;
    @Autowired
    private ROEventDesignDetailService roEventDesignDetailService;
	@Autowired
	private ItemService itemService;
	@Autowired
	private TicketingService ticketingService;
	@Autowired
	private PhoneNumberService phoneNumberService;
    @Autowired
    private ROPhoneNumberService roPhoneNumberService;
	@Autowired
	private EventInteractionRepository eventInteractionRepository;
	@Autowired
	private RaffleService raffleService;
	@Autowired
	private RaffleTicketService raffleTicketService;
	@Autowired
	private StripePaymentService stripePaymentService;
	@Autowired
	private AllPaymentService allPaymentService;
	@Autowired
	private CauseAuctionService causeAuctionService;
	@Autowired
	private EventChecklistService eventChecklistService;
	@Autowired
	private StripeService stripeService;
    @Autowired
    private ROStripeService roStripeService;
	@Autowired
	private TwillioNumberHistoryService twillioNumberHistoryService;
	@Autowired
	private TextMessageService textMessageService;
	@Autowired
	private StaffService staffService;
    @Autowired
    private ROStaffService roStaffService;
	@Autowired
	private UserService userService;
    @Autowired
    private ROUserService roUserService;
	@Autowired
	private StripeTransactionService stripeTransactionService;
	@Autowired
	private WinnerService winnerService;
	@Autowired
	private StripeConfiguration stripeConfiguration;
	@Autowired
	private SquareConfiguration squareConfiguration;
	@Autowired
	private ImageConfiguration imageConfiguration;
	@Autowired
	private DonationSettingsService donationSettingsService;
	@Autowired
	private SendGridMailPrepareService sendGridMailPrepareService;
	@Autowired
	private TwilioTextMessagePrepareService twilioTextMessagePrepareService;
	@Autowired
	private AuctionBidService auctionBidService;
	@Autowired
	private JoinPaymentItemService joinPaymentItemService;
	@Autowired
	private PledgeService pledgeService;
	@Autowired
	private PaymentService paymentService;
	@Autowired
	private PurchasedRaffleTicketService purchasedRaffleTicketService;
	@Autowired
	private TextMessageUtils textMessageUtils;
	@Autowired
	private EventQuestionRepository eventQuestionRepository;
	@Autowired
	private SquarePaymentService squarePaymentService;
	@Autowired
	private BidderNumberService bidderNumberService;
	@Autowired
	private RaffleCustomSmsService raffleCustomSmsService;
	@Autowired
	private AuctionCustomSmsService auctionCustomSmsService;
	@Autowired
	private CauseAuctionCustomSmsService causeAuctionCustomSmsService;
	@Autowired
	private DonationCustomSmsService donationCustomSmsService;
	@Autowired
	private TicketingTypeService ticketingTypeService;
	@Autowired
	private TicketingTypeTicketService ticketingTypeTicketService;
    @Autowired
    private ROTicketingTypeTicketService roTicketingTypeTicketService;
	// checkout controller
	@Autowired
	private DonationService donationService;
	@Autowired
	private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
	@Autowired
	private PaymentHandlerService paymentHandlerService;
	@Autowired
	private TicketingOrderRepoService ticketingOrderRepoService;
	@Autowired
	private OrganizerService organizerService;
	@Autowired
	private RecurringEventsScheduleBRService recurringEventsScheduleService;
	@Autowired
	private  TicketingStatisticsService ticketingStatisticsService;
	@Autowired
	private AutoAssignedAttendeeNumbersService autoAssignedAttendeeNumbersService;
	@Autowired
	private WhiteLabelService whiteLabelService;
	@Autowired
	private DeletedEventsRepository deletedEventsRepository;
	@Autowired
	private WaitListSettingService waitListSettingService;
	@Autowired
	private WaitListService waitListService;
    @Autowired
    private EventDesignDetailRepoService eventDesignDetailRepoServices;
    @Autowired
	private IntercomDetailsService intercomDetailsService;
	@Autowired
	private CheckInAuditLogService checkInAuditLogService;
	@Autowired
    private ChargebeeService chargebeeService;
	@Autowired
    private ChargebeePlanService chargebeePlanService;
	@Autowired
    private EventPlanConfigService eventPlanConfigService;
	@Autowired
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
	@Autowired
    private OrganizerRepository organizerRepository;
	@Autowired
    private SessionRepoService sessionRepoService;
	@Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
	@Autowired
    private ChargebeeEventCreditsRepoService chargebeeEventCreditsRepoService;
	@Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;
	@Autowired
    private ChargeConfigRepoService chargeConfigRepoService;
	@Autowired
    private EventChargeUsagesService eventChargeUsagesService;
	@Autowired
    private EventChargeUsagesRepoService eventChargeUsagesRepoService;
    @Autowired
	private TicketingCouponService ticketingCouponService;
	@Autowired
	private TicketingAccessCodeService ticketingAccessCodeService;
	@Autowired
	private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
	@Autowired
	private TicketingOrderManagerService ticketingOrderManagerService;
	@Autowired
	private EventTicketsRepoService eventTicketsRepoService;
	@Autowired
	private TicketingRepository ticketingRepository;
	@Autowired
	private RecurringEventsRepository recurringEventsRepository;
	@Autowired
    private EmbedWidgetSettingService embedWidgetSettingService;
    @Autowired
    private BeeFreeRepository beeFreeRepository;
    @Autowired
    private AttendeeAnalyticsService attendeeAnalyticsService;
    @Autowired
    private OrganizerRepoService organizerRepoService;
    @Autowired
    private WhiteLabelRepoService whiteLabelRepoService;
    @Autowired
    private ROPayFlowConfigService roPayFlowConfigService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private RegistrationAttributeRepository registrationAttributeRepository;
    @Autowired
    private SponsorsRepoService sponsorsRepoService;
    @Autowired
    private HostEventOfflinePaymentConfigService hostEventOfflinePaymentConfigService;
    @Autowired
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Autowired
    private MeetingOptionsRepoService meetingOptionsRepoService;

    @Autowired
    private WhiteLabelSettingsService whiteLabelSettingsService;

    @Autowired
    private  SponsorsService sponsorsService;
    @Autowired
    private ConfirmationPagesService confirmationPagesService;
    @Autowired
    private ConfirmationEmailService  confirmationEmailService;
    @Autowired
    private ROConfirmationEmailService roConfirmationEmailService;

    @Autowired
    private StripeCustomerService stripeCustomerService;

    @Autowired
    private KioskRegistrationSettingService kioskRegistrationSettingService;

    @Autowired
    private TicketingLimitedDisplayCodeService ticketingLimitedDisplayCodeService;
    @Autowired
    private EventTicketsService eventTicketsService;
    @Autowired
    private ContactModuleSettingsService contactModuleSettingsService;
    @Autowired
    ConfirmationEmailRepository confirmationEmailRepository;

    @Autowired
    private ROWhiteLabelSettingsService roWhiteLabelSettingsService;
	@Value("${uiBaseurl}")
	private String uiBaseurl;

	private final static String SQUARE_SCOPE = "MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE SETTLEMENTS_READ PAYMENTS_WRITE_IN_PERSON PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS";

	@Value("${apiBaseUrl}")
	private String apiBaseUrl;

	@Autowired
	private EntityManager entityManager;

	@Autowired
	private TicketingHelperService ticketingHelperService;

	@Autowired
	private VirtualEventService virtualEventService;
	@Autowired
	private ROVirtualEventService roVirtualEventService;
	@Autowired
	private ExhibitorRepoService exhibitorRepoService;
	@Autowired
	private EventBillingAddOnsService eventBillingAddOnsService;

	@Autowired
	private HubspotContactService hubspotContactService;

	@Autowired
	private HubspotEventService hubspotEventService;

    @Autowired
    private HubspotOrganizerService hubspotOrganizerService;

    @Autowired
    private EventRepoService eventRepoService;

    @Autowired
    private NeptuneInterestService neptuneInterestService;
    @Autowired
    private ChargebeeAttendeeUploadService chargebeeAttendeeUploadService;
    @Autowired
    private ChargebeeBillingService chargebeeBillingService;
    @Autowired
    private VirtualEventPortalService virtualEventPortalService;
    @Autowired
    private PaypalConfiguration paypalConfiguration;
    @Autowired
    private PaypalPaymentService paypalPaymentService;
    @Autowired
    private ApiKeyService apiKeyService;

    @Value("${application.environment}")
    private String environment;

    @Autowired
    private SSOUserService ssoUserService;

    @Autowired
    private EventDesignDetailRepository eventDesignDetailRepository;

    @Autowired
    private ExhibitorSettingsRepoService exhibitorSettingsRepoService;

    @Autowired
    private EventChallengeRepoService eventChallengeRepoService;

    @Autowired
    private ChallengeConfigService challengeConfigService;

    @Autowired
    private ChargebeePlanRepoServiceImpl chargebeePlanRepoService;

    @Autowired
    private HubspotCustomLabelsAssociationService hubspotCustomLabelsAssociationService;

    @Autowired
    private ItemOutbidGraphQLHandler itemOutbidGraphQLHandler;
    @Autowired
    private HubspotConfiguration hubspotConfiguration;
    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;
    @Autowired
    private  AttendeeProfileService attendeeProfileService;
    @Autowired
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;

    @Autowired
    private  ChargeBeeHelperService chargeBeeHelperService;

    @Autowired
    private EventsTemplatesRepository eventTemplateRepository;
    @Autowired
    private BeeFreePageRepoService beeFreePageRepoService;
    @Autowired
    private ROBeeFreePagesService roBeeFreePagesService;

    @Autowired
    private IntegrationService integrationService;
    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private JoinUserWithOrganizerRepoService joinUserWithOrganizerRepoService;
    @Autowired
    private EventBadgeSettingRepository badgeEventSettingRepository;
    @Autowired
    private EventLevelSettingService eventLevelSettingService;
    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Autowired
    private EventCECriteriaRepoService eventCECriteriaRepoService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private ROWhiteLabelSettingRepository roWhiteLabelSettingRepository;
    @Autowired
    private CustomTicketInvoiceDesignService customTicketInvoiceDesignService;
	@Autowired
	private EntryExitSettingService entryExitSettingService;

    private static final List<StaffRole> STAFF_ROLES = new ArrayList<>(Arrays.asList(StaffRole.admin, StaffRole.whitelabeladmin, StaffRole.eventcoordinator));


    Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

    private static final String MERCHANT_ID = "merchant_id";
	@Override
	public Iterable<Event> getAllEvents() {
		return eventRepository.findAll();
	}

	@Override
	public List<Event> getAllAttachedPhoneNumberEvents() {
		return eventRepository.getAllAttachedPhonenumberEvents();
	}

	@Deprecated
	@Override
	public Event getEventFromDb(long id) {
		return Optional.ofNullable(eventRepository.findEventByEventIdWithoutCaching(id)).orElseThrow(() ->
				new NotFoundException(EventNotFound.EVENT_NOT_FOUND));
	}

	@Override
	public Event getEventByIdWithoutCache(long id) {
		return Optional.ofNullable(eventRepository.findEventByEventIdWithoutCache(id)).orElseThrow(() ->
				new NotFoundException(EventNotFound.EVENT_NOT_FOUND));
	}

	@Override
	public List<Event> getEventByFromTo(long from,long to){
		return eventRepository.getEventByFromTo(from,to);
	}

	@Override
	public String getEventUrl(String url, int count, int urlLength, boolean updateUrl, User user) {
        // remove other characters from url then alphanumeric and hyphen
        Pattern myPattern = Pattern.compile(ATOZ_BOTHCASE_AND_NUMBERS_AND_HYPHEN);
        Matcher myMatch = myPattern.matcher(url);
        boolean isNotValid = myMatch.find();

        if (isNotValid && updateUrl) {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.URL_NOT_SUPPORT_SPECIAL_CHART);
		}
		Event event;
		try {
			event = roEventService.getEventByURL(url);
		} catch (NotFoundException nfe) {
			if (StringUtils.equals(nfe.getErrorCode(), EventNotFound.EVENT_NOT_FOUND.getStatusCode())) {
				event = null;
			} else {
				throw nfe;
			}
		}
		if (event != null && updateUrl) {
		    if(null != user && roStaffService.isEventAdmin(event, user)){
                throw new ConflictException(UserExceptionConflictMsg.EVENT_URL_ALREADY_EXIST_AND_SAME_ADMIN);
            }else {
                throw new ConflictException(UserExceptionConflictMsg.EVENT_URL_ALREADY_EXIST);
            }
		}
		if (event == null) {
			return url;
		} else {
			return getEventUrl(url.substring(0, urlLength) + count, count + 1, urlLength, false, user);
		}
	}

    public String getEventName(String name, int count, int nameLength, boolean updateName) {
        // remove other characters from name then alphanumeric and space
        if(!updateName && count == 1){
            name = name + "1";
        }

        Event event;
        try {
            event = this.getEventByName(name);
        } catch (NotFoundException nfe) {
            if (StringUtils.equals(nfe.getErrorCode(), EventNotFound.EVENT_NOT_FOUND.getStatusCode())) {
                event = null;
            } else {
                throw nfe;
            }
        }

        if (event == null) {
            return name;
        } else if (updateName){
            return name;
        } else {
            return getEventName(name.substring(0, nameLength) + count, count + 1, nameLength, false);
        }
    }

	@Override
	@Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
	public Event createEventAndCloneStripeOfWL(boolean silentAuctionEnabled, boolean causeAuctionEnabled, boolean raffleEnabled,
											   String userEmail, WhiteLabel whiteLabel, List<String> userRoles, Stripe stripe, String organizerPageUrl) {
		Event event = createEvent(silentAuctionEnabled, causeAuctionEnabled, raffleEnabled, userEmail, whiteLabel, userRoles, null, organizerPageUrl);

		//copy stripe from super whitelabel
		Stripe newStripe = (Stripe) stripe.clone();
        newStripe.setWhiteLabelId(0);
		newStripe.setId(0L);
		newStripe.setEvent(event);
		newStripe.setDefaultAccount(true);
        StripeUtil.applyStripeProcessingFeeBasedOnConnectedAccountCurrency(stripe, event);
        if(SpreedlyGatewayType.getSpreedlyGatewayTypeList().contains(stripe.getPaymentGateway())) {
            StripeUtil.applyProcessingFeeOnEventBasedSpreedlyGateway(newStripe, SpreedlyGatewayType.valueOf(stripe.getPaymentGateway()), event);
        }
		stripeService.save(newStripe);
        event.setCreditCardEnabled(true);
		return event;
	}

	@Override
	@Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
	public Event createEvent(boolean silentAuctionEnabled, boolean causeAuctionEnabled, boolean raffleEnabled,
                             String userEmail, WhiteLabel whiteLabel, List<String> userRoles, Long userId,
                             String organizerPageUrl) {

        Event event = new Event();

        setEventName(event, userEmail, userId);

        event.setCauseAuctionEnabled(causeAuctionEnabled);
		event.setSilentAuctionEnabled(silentAuctionEnabled);
		event.setRaffleEnabled(raffleEnabled);
		event.setCurrency(Currency.USD); // Default Currency
		event.setCreditCardEnabled(false);
		event.setGoalStartingAmount(0);
		boolean copyWhiteLabelModuleActivations = false;

		if (null != whiteLabel) {
			event.setWhiteLabelId(whiteLabel.getId());
            event.setWhiteLabel(whiteLabel);
			event.setBillingType(whiteLabel.getBillingType());
			String url = whiteLabel.getWhiteLabelUrl();
            url = convertEventUrlBySeparateWords(url);
			event.setEventURL(getEventUrl(url, 0, url.length(), false, null));
			copyWhiteLabelModuleActivations = userRoles.contains(ROLE_WHITELABELADMIN.name()) || userRoles.contains(ROLE_EVENT_COORDINATOR.name())
					|| userRoles.contains(UserRole.ROLE_SUPERADMIN.name());

			if (userRoles.contains(ROLE_WHITELABELADMIN.name()) || userRoles.contains(ROLE_EVENT_COORDINATOR.name()) || userRoles.contains(ROLE_ADMIN.name())
					|| userRoles.contains(UserRole.ROLE_SUPERADMIN.name())) {
				// Copy Enable From white label
                copyWhiteLabelFundRaisingModules(whiteLabel, event);
            }
		} else {
            String url;
            if(userEmail!=null) {
                 url = userEmail.split(STRING_AT)[0];
            }else {
            url=UUID.randomUUID().toString().replaceAll(STRING_DASH, STRING_EMPTY).substring(0, 10);
            }
            url = convertEventUrlBySeparateWords(url);
			event.setEventURL(getEventUrl(url, 0, url.length(), false, null));
		}
		// event.setTimeZone(timeZoneService.findDefaultTimeZone());//NOSONAR
		event.setEventListingStatus(EventListingStatus.PREVIEW);

        eventRepoService.save(event);
        log.info("Event URL : {}", event.getEventURL());

        long eventId = createEventCheckList(event);

        createEventDesignDetails(userEmail, event);
        // event.setDesignDetails(eventDesignDetails);//NOSONAR



        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 30); // 30 Days From Now.


        Auction auctionModule = createAuctionModule(whiteLabel, copyWhiteLabelModuleActivations, eventId, calendar);

        event.setAuctionId(auctionModule.getId());

        List<String> defaultCategories = createDefaultCategoriesForAuction(auctionModule);

        Raffle raffleModule = createRaffle(whiteLabel, copyWhiteLabelModuleActivations, eventId, calendar);

        event.setRaffleId(raffleModule.getId());

		raffleTicketService.addDefaultTicketPrices(raffleModule);

        Ticketing ticketing = createTicketing(whiteLabel, event);

        event.setTicketingId(ticketing.getId());
		event.setTicketingEnabled(false);

        if(StringUtils.isNotBlank(organizerPageUrl)){
            final StopWatch createOrganizerEventStopwatch = new StopWatch();
            //Measure method execution time
            createOrganizerEventStopwatch.start();
            Organizer organizer = organizerService.setEventWithOrganizer(event, organizerPageUrl);
            if (organizer != null) {
                setTCFLAsFreePlanIfOrganiserDoesNotHaveQuantity(organizer);
                eventPlanConfigService.handleCreateOrUpdatePlanForEvent(event);
            }

            createOrganizerEventStopwatch.stop();

            log.info("Execution time of createEvent || Add Organizer in  {} ms",createOrganizerEventStopwatch.getTotalTimeMillis());
        }
        createDefaultCategories(defaultCategories, raffleModule.getId(), ModuleType.RAFFLE);

        CauseAuction causeAuctionModule = createCauseAuction(whiteLabel, copyWhiteLabelModuleActivations, eventId);

        event.setCauseAuctionId(causeAuctionModule.getId());

		/* Default Item Categories For Fund a Need Module */
		defaultCategories = Arrays.asList("Travel", "Experiences", "Memorabilia", "Music");
        createDefaultCategories(defaultCategories, causeAuctionModule.getId(), ModuleType.CAUSEAUCTION);
        // donation setting
        createDonation(whiteLabel, eventId);

        createAuctionSampleItem(auctionModule);

        createFundANeedSampleItem(causeAuctionModule);

        createRaffleSampleItem(raffleModule);

        updateEventAttendeeUploadToggle(event);

        createMeetingScheduleOptions(event);

        getStreamService.createChannelWithChannelName(EVENT_.concat(String.valueOf(eventId)), event.getName(), MESSAGING,1L, null != userId ? Collections.singletonList(userId) : Collections.emptyList(), false, eventId);
        getStreamService.createChannelWithChannelName(STAFF_.concat(String.valueOf(eventId)), "Event Staff", MESSAGING,1L, null != userId ? Collections.singletonList(userId) : Collections.emptyList(), false, eventId);
        getStreamService.createChannelWithChannelName(ADMIN_DES_.concat(String.valueOf(eventId)), ADMIN_SLASH_DES, DES_MESSAGING,1L, null != userId ? Collections.singletonList(userId) : Collections.emptyList(), false, eventId);

        hubspotEventService.createHubspotEvent(event, ticketing, whiteLabel !=null  ? ENTERPRISE_PAGE : StringUtils.isNotBlank(organizerPageUrl) ? ORGANIZER_PAGE : MY_PROFILE);

        return event;
	}

    private void setTCFLAsFreePlanIfOrganiserDoesNotHaveQuantity(Organizer organizer) {
        String planName = organizer.getPlanConfig().getPlanName();
        boolean isValid = STARTER_2023.getLableName().equals(planName) || PROFESSIONAL_2023.getLableName().equals(planName);
        if (isValid) {
            Long count = chargesPurchasedRepoService.findChargePurchasedCountByOrganiser(organizer);
            if (count == 0) {
                List<TransactionFeeConditionalLogic> transactionFeeRecords = transactionFeeConditionalLogicService.getBaseRecordByOrganizer(organizer);
                transactionFeeRecords.forEach(entity -> {
                    entity.setAeFeeFlat(AE_FLAT_FEE_ZERO);
                    entity.setAeFeePercentage(AE_FEE_PERCENTAGE_ZERO);
                    if (GREATER_THAN_EQUAL_TO.equals(entity.getOperator())) {
                        entity.setAeFeeFlat(AE_FLAT_FEE_ONE);
                        entity.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
                    }
                });
                transactionFeeConditionalLogicService.saveAll(transactionFeeRecords);
            }
        }
    }

    private void createMeetingScheduleOptions(Event event) {
        MeetingOptions meetingOptions = new MeetingOptions();
        meetingOptions.setEventId(event.getEventId());
        meetingOptions.setAllowAvailabilitySchedule(Boolean.FALSE);
        meetingOptions.setMeetingReminderTime(5L);
        meetingOptions.setMeetingReminderContext(DEFAULT_MEETING_REMINDER_CONTEXT);
        meetingOptionsRepoService.save(meetingOptions);
    }

    @Override
	public void addEventCreatorContactWithLabelForEventToHubspot(Event event, User user) {
        String contactId = hubspotContactService.createHubspotContact(user);
        hubspotEventService.addEventCreatorContactWithLabelForEventToHubspot(event, contactId);
    }

    @Override
    public void addEventBillingContactContactWithLabelForEventToHubspot(Event event, User user) {
        String contactId = hubspotContactService.createHubspotContact(user);
        hubspotEventService.addEventBillingContactWithLabelForEventToHubspot(event, contactId);
    }

	@Override
	public void addContactsWithLabelForEventToHubspot(Event event, User user) {

        List<Staff> listOfEventAdmin = staffService.getListOfStaffByEventAndRole(event,STAFF_ROLES);
        if (!CollectionUtils.isEmpty(listOfEventAdmin)) {
            listOfEventAdmin.forEach(eventAdmin -> {
                String contactIdInHS = hubspotContactService.createHubspotContact(eventAdmin.getUser());
                hubspotEventService.addEventAdminContactWithLabelForEventToHubspot(event, contactIdInHS);
            });
        }

        User billingContact = getBillingContact(event);
        if (null != billingContact) {
            String billingContactId = hubspotContactService.createHubspotContact(billingContact);
            hubspotEventService.addEventBillingContactWithLabelForEventToHubspot(event, billingContactId);
        }
    }

    @Override
    public void addContactsWithLabelForEventToHubspotForDataFix(Event event, String eventIdInHubspot) {

        List<Staff> listOfEventAdmin = staffService.getListOfStaffByEventAndRole(event,STAFF_ROLES);
        boolean isEventCreatorFoundFromAdminList = false;
        String eventCreatorIdInHSFoundFromAdminList = null;
        if (!CollectionUtils.isEmpty(listOfEventAdmin)) {
            for (int i = 0; i < listOfEventAdmin.size(); i++) {
                Staff eventAdmin = listOfEventAdmin.get(i);
                String contactIdInHS = hubspotContactService.createHubspotContact(eventAdmin.getUser());
                if (i == 0) {
                    isEventCreatorFoundFromAdminList = true;
                    eventCreatorIdInHSFoundFromAdminList = contactIdInHS;
                } else {
                    hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, contactIdInHS, Collections.singletonList(EVENT_ADMIN));
                }
            }
        }

        User billingContact = getBillingContact(event);
        String billingContactIdInHS;
        boolean isBillingContactIsCreator = false;
        if (null != billingContact) {
            billingContactIdInHS = hubspotContactService.createHubspotContact(billingContact);
            if (billingContactIdInHS.equals(eventCreatorIdInHSFoundFromAdminList)) {
                isBillingContactIsCreator = true;
            } else {
                hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, billingContactIdInHS, Collections.singletonList(EVENT_BILLING_CONTACT));
            }
        }

        if (isBillingContactIsCreator) {
            hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, eventCreatorIdInHSFoundFromAdminList, Arrays.asList(Constants.EVENT_ADMIN, EVENT_BILLING_CONTACT, EVENT_CREATOR));
        } else if (isEventCreatorFoundFromAdminList) {
            hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventIdInHubspot, eventCreatorIdInHSFoundFromAdminList, Arrays.asList(Constants.EVENT_ADMIN, EVENT_CREATOR));
        }
    }

    private User getBillingContact(Event event) {
        List<User> billingContacts;
        if (null != event.getWhiteLabelId()) {
            billingContacts = joinUsersWithOrganizersRepository.findBillingContactByWLId(event.getWhiteLabelId());
            if (!CollectionUtils.isEmpty(billingContacts)) {
                return billingContacts.get(0);
            }
        } else if (null != event.getOrganizerId()) {
            billingContacts = joinUsersWithOrganizersRepository.findBillingContactByOrgId(event.getOrganizerId());
            if (!CollectionUtils.isEmpty(billingContacts)) {
                return billingContacts.get(0);
            }
        }
        return null;
    }

    private void setEventName(Event event, String userEmail, Long userId) {
        User user;
        Optional<User> userOptional;
        if (userId != null) {
            userOptional = roUserService.getUserById(userId);
        } else {
            userOptional = roUserService.getUserByEmail(userEmail);
        }
        if (userOptional.isPresent() && userOptional.get().getFirstName() != null) {
            user = userOptional.get();
            String name = user.getFirstName()  + (user.getLastName() == null ? STRING_EMPTY : " " + user.getLastName()) + "'s Event ";
            event.setName(getEventName(name, 1, (name).length(), false));
        }
        else{
            if(userEmail!=null) {
                String name = userEmail.split(STRING_AT)[0] + "'s Event ";
                event.setName(getEventName(name, 1, (name).length(), false));
            }else {
                event.setName(UUID.randomUUID().toString().replaceAll(STRING_DASH, STRING_EMPTY).substring(0, 10));
            }
        }
    }

    private long createEventCheckList(Event event) {
        /* Create Event Checklist */
        EventChecklist eventChecklist = new EventChecklist();
        long eventId = event.getEventId();
        eventChecklist.setEventId(eventId);
        this.eventChecklistService.save(eventChecklist);
        return eventId;
    }

    private void createRaffleSampleItem(Raffle raffleModule) {
        /* Raffle Sample Item */
        Item rafSampleItem = new Item();
        rafSampleItem.setName("My First Raffle Item");
        rafSampleItem.setItemShortName("Default Raffle");
        rafSampleItem.setModuleId(raffleModule.getId());
        rafSampleItem.setModuleType(ModuleType.RAFFLE);
        rafSampleItem.setCode("RAF");
        itemService.savewithsequenceWithoutCacheClear(rafSampleItem);
    }

    private void createFundANeedSampleItem(CauseAuction causeAuctionModule) {
        /* Fund a Need Sample Item */
        Item caexeItem = new Item();
        caexeItem.setName("My Fund a Need Item");
        caexeItem.setItemShortName("Default FundANeed");
        caexeItem.setModuleId(causeAuctionModule.getId());
        caexeItem.setModuleType(ModuleType.CAUSEAUCTION);
        caexeItem.setCode("FAN");
        caexeItem.setStartingBid(300);
        itemService.savewithsequenceWithoutCacheClear(caexeItem);
    }

    private void createAuctionSampleItem(Auction auctionModule) {
        /* Auction Sample Item */
        Item exeItem = new Item();
        exeItem.setName("My First Auction Item");
        exeItem.setItemShortName("Default Auction Item");
        exeItem.setModuleId(auctionModule.getId());
        exeItem.setModuleType(ModuleType.AUCTION);
        exeItem.setCode("AUC");
        exeItem.setStartingBid(300);
        exeItem.setBidIncrement(75d);
        itemService.savewithsequenceWithoutCacheClear(exeItem);
    }

    private void createDonation(WhiteLabel whiteLabel, long eventId) {
        DonationSettings donationSettings = new DonationSettings();
        if (whiteLabel != null) {
            donationSettings.setDonationAmountA(whiteLabel.getDonationAmountA());
            donationSettings.setDonationAmountB(whiteLabel.getDonationAmountB());
            donationSettings.setDonationAmountC(whiteLabel.getDonationAmountC());
            donationSettings.setDonationAmountD(whiteLabel.getDonationAmountD());
        } else {
            donationSettings.setDonationAmountA(5);
            donationSettings.setDonationAmountB(15);
            donationSettings.setDonationAmountC(30);
            donationSettings.setDonationAmountD(50);
        }
        donationSettings.setFee(1);
        donationSettings.setEventId(eventId);
        donationSettings.setAbsorbFee(false);
        donationSettings.setTextToGiveActivated(false);
        donationSettingsService.save(donationSettings);
    }

    private CauseAuction createCauseAuction(WhiteLabel whiteLabel, boolean copyWhiteLabelModuleActivations, long eventId) {
        Calendar calendar;
        /* Creating Fund a Need Module Record */
        CauseAuction causeAuctionModule = new CauseAuction();
        causeAuctionModule.setEventId(eventId);
        calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 30); // 30 Days From Now.
        causeAuctionModule.setEndDate(calendar.getTime());
        if (null != whiteLabel && copyWhiteLabelModuleActivations) {
            causeAuctionModule.setActivated(whiteLabel.isCauseAuctionActivated());
            if(whiteLabel.isManualPayout()){
                causeAuctionModule.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
            }
        }
        causeAuctionModule.setOverageStatus(EnumOverageStatus.CREATE);
        causeAuctionService.save(causeAuctionModule);
        return causeAuctionModule;
    }

    private void createDefaultCategories(List<String> defaultCategories, long id, ModuleType raffle) {
        for (String categoryValue : defaultCategories) {
            ItemCategory category = new ItemCategory();
            category.setModuleId(id);
            category.setModuleType(raffle);
            category.setName(categoryValue);
            itemCategoryService.savewithsequence(category);
        }
    }

    private Ticketing createTicketing(WhiteLabel whiteLabel, Event event) {
        Ticketing ticketing = ticketingHelperService.createNewTicketing(event);
        if (null != whiteLabel) {
            //ticketing.setActivated(true);//NOSONAR
            if(whiteLabel.isManualPayout()){
                ticketing.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
            }
            ticketingService.save(ticketing);
        }
        return ticketing;
    }

    private Raffle createRaffle(WhiteLabel whiteLabel, boolean copyWhiteLabelModuleActivations, long eventId, Calendar calendar) {
        /* Creating Raffle Module Record */
        Raffle raffleModule = new Raffle();
        raffleModule.setEventId(eventId);
        raffleModule.setEndDate(calendar.getTime());
        raffleModule.setSweepstakes(false);
        raffleModule.setAvailableTickets(0);
        raffleModule.setAutoSubmitOnPurchase(false);
        if (null != whiteLabel && copyWhiteLabelModuleActivations) {
            raffleModule.setActivated(whiteLabel.isRafflesActivated());
            if(whiteLabel.isManualPayout()){
                raffleModule.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
            }
        }
        raffleModule.setOverageStatus(EnumOverageStatus.CREATE);
        raffleService.save(raffleModule);
        return raffleModule;
    }

    private List<String> createDefaultCategoriesForAuction(Auction auctionModule) {
        /* Default Item Categories For Auction Module */
        List<String> defaultCategories = Arrays.asList("Travel", "Experiences", "Memorabilia", "Music");
        createDefaultCategories(defaultCategories, auctionModule.getId(), ModuleType.AUCTION);
        return defaultCategories;
    }

    private Auction createAuctionModule(WhiteLabel whiteLabel, boolean copyWhiteLabelModuleActivations, long eventId, Calendar calendar) {
        /* Creating Auction Module Record */
        Auction auctionModule = new Auction();
        auctionModule.setEventId(eventId);
        auctionModule.setEndDate(calendar.getTime());
        if (null != whiteLabel && copyWhiteLabelModuleActivations) {
            auctionModule.setActivated(whiteLabel.isAuctionsActivated());
            if(whiteLabel.isManualPayout()){
                auctionModule.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
            }
        }
        auctionModule.setOverageStatus(EnumOverageStatus.CREATE);
        auctionService.save(auctionModule);
        return auctionModule;
    }

    private void createEventDesignDetails(String userEmail, Event event) {
        /* Create Design Details Record */
        EventDesignDetail eventDesignDetails = new EventDesignDetail();
        eventDesignDetails.setEvent(event);

        eventDesignDetails.setSponsorSection(SPONSORS_DEFAULT_SECT);
        eventDesignDetails.setTicketingTabTitle(BUY_TICKET_BUTTON_TEXT);
        eventDesignDetails.setLogoImage(imageConfiguration.getBlackLogo());
        eventDesignDetails.setHeaderLogoImage(imageConfiguration.getDefaultHeader());
        eventDesignDetails.setReplyToEmail(userEmail);
        eventDesignDetails.setDisplayBackgroundColor("#F7F7FA");
        eventDesignDetails.setEventTagLine(DEFAULT_EVENT_TAGE_LINE);
        eventDesignDetails.setThemeId(3L);
        eventDesignDetails.setThemeWiseDefaultConfig(true);
        eventDesignDetails.setEventCalendarInvite(roConfirmationEmailService.getCalenderInvitation(event));
        if (event.getWhiteLabel() == null) {
            eventDesignDetails.setHeaderColor("#FFFFFF"); //white
            eventDesignDetails.setHeaderFontColor("#6D6F7D"); // Storm Gray
            eventDesignDetails.setDesc(EVENT_DEFAULT_DESC);
            eventDesignDetails.setEnableCaptcha(false);
        }else{
            eventDesignDetails.setDesc(WL_EVENT_DEFAULT_DESC);
            Optional<WhiteLabelSettings> whiteLabelSettings = roWhiteLabelSettingRepository.findByWhiteLabelId(event.getWhiteLabel().getId());
            if(whiteLabelSettings.isPresent()){
                eventDesignDetails.setEnableCaptcha(whiteLabelSettings.get().isEnableCaptchaV2());
                eventDesignDetails.setNotificationEmail(whiteLabelSettings.get().getWhiteLabel().getNotificationEmail());
            }
        }
        eventDesignDetailService.save(eventDesignDetails);
    }

    private void copyWhiteLabelFundRaisingModules(WhiteLabel whiteLabel, Event event) {
        event.setSilentAuctionEnabled(whiteLabel.isSilentAuctionEnabled());
        event.setCauseAuctionEnabled(whiteLabel.isCauseAuctionEnabled());
        event.setRaffleEnabled(whiteLabel.isRaffleEnabled());
        event.setTicketingEnabled(whiteLabel.isTicketingEnabled());
    }

    @Override
    public Event getEventByURLWithoutCache(String eventUrl) {
        log.info("eventUrl {} and length {} ",eventUrl,eventUrl.length());
        try {
            eventUrl = URLEncoder.encode(eventUrl, UTF8);
        } catch (UnsupportedEncodingException e) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.URL_NOT_SUPPORTED);
        }
        log.info("get eventUrl {} for getting virtual event setting {}", eventUrl,eventUrl.length());
        Event event = eventRepository.findEventByEventURLWithoutCache(eventUrl);
        if (event != null) {
            log.info("get event {} from database",event.getEventId());
            return event;
        } else {
            log.info("event not found for eventUrl {}",eventUrl);
            throw new NotFoundException(EventNotFound.EVENT_NOT_FOUND);
        }
    }

	@Override
	public Set<String> sendEmail(WhiteLabel whiteLabel, String replyToEmail,
                                 String subject, String message,
                                 TemplateId templateId,
                                 EmailMessage emailMessage,
                                 String eventNotificationEmail, String emailSenderName) {
		Set<String> receiversList = new HashSet<>();
        // This feature only work for WL customers
		if (templateId.equals(TemplateId.SUPPORT_QUESTION)) {
			if (whiteLabel != null) {
				receiversList.add(whiteLabel.getSupportEmail());
			}
		}

		emailMessage.setSenderMailFromEvent(eventNotificationEmail);
        emailMessage.setSenderNameFromEvent(emailSenderName);
		emailMessage.setBody(message);
		emailMessage.setSubject(subject);
        BeeFree beeFree = beeFreeRepository.findByEmailTypeAndEventIdZero(EmailType.valueOf((templateId.name())));
        if(beeFree != null && beeFree.isCustomTemplateEnabled()){
            emailMessage.setTemplateName(templateId.name());
            emailMessage.setTemplate(beeFree.getHtmlValue());
            emailMessage.setTemplateId(templateId);
            emailMessage.setBeefreeTemplate(true);
       } else {
            emailMessage.setTemplateId(templateId);
            emailMessage.setTemplateName(templateId.getValue());
        }
		emailMessage.setReplyToEmail(replyToEmail);
		emailMessage.setWhiteLabel(whiteLabel);

		return receiversList;
	}

	@Override
	@Transactional
	public void sendContactEmail(User user, ContactDto contact, String eventUrl) {
		if (user == null) {
			if (isBlank(contact.getEmail())) {
				throw new AuthorizationException(NOT_AUTHORIZE);
			}
			user = new User();
			user.setFirstName(contact.getName());
			user.setEmail(contact.getEmail());
		}
		Event event = roEventService.getEventByURL(eventUrl);
        EventDesignDetail eventDesignDetail = eventDesignDetailRepoServices.findByEvent(event);
		EmailMessage emailMessage = new EmailMessage(TemplateId.PARTICIPANT_QUESTION);
		Set<String> receivers = this.sendEmail(event.getWhiteLabel(), user.getEmail(), "An event participant has a question", contact.getMessage(),
				TemplateId.PARTICIPANT_QUESTION, emailMessage, eventDesignDetail.getNotificationEmail(), eventDesignDetail.getEmailSenderName());

		Map<String, Object> substitutionData = new HashMap<>();
		substitutionData.put("name",
				isNotBlank(user.getFirstName()) ? user.getFirstName() : STRING_DASH);
		substitutionData.put("email", user.getEmail());
		substitutionData.put(EVENT_NAME, event.getName());
		substitutionData.put("pageUrl", contact.getPageUrl());
		emailMessage.setSubstitutionData(substitutionData);
		try {
			sendGridMailPrepareService.sendSupportOrContactMail(emailMessage, event, receivers);

			log.info("prepare  eventQuestion object {}",contact);
			EventQuestion eventQuestion = new EventQuestion(contact, user, event);
			log.info("save user contact question details in data base {}",contact);
			eventQuestionRepository.save(eventQuestion);
		} catch (Exception e) {
			log.error(EXCEPTION, e);
		}
	}

	@Override
	@Transactional
	public void sendSupportEmail(User user, String message, Event event) {
		if (user == null) {
			throw new AuthorizationException(NOT_AUTHORIZE);
		}
        EventDesignDetail eventDesignDetail = eventDesignDetailRepoServices.findByEvent(event);
		EmailMessage emailMessage = new EmailMessage(TemplateId.SUPPORT_QUESTION);
		Set<String> receivers = this.sendEmail(event.getWhiteLabel(), user.getEmail(), "An event host has a question", message,
						TemplateId.SUPPORT_QUESTION, emailMessage, eventDesignDetail.getNotificationEmail(), eventDesignDetail.getEmailSenderName());
		Map<String, Object> substitutionData = new HashMap<>();
		substitutionData.put("asker_event_name", event.getName());
		if (event.getWhiteLabel() != null && isNotBlank(event.getWhiteLabel().getSupportEmail())) {
			substitutionData.put("supportEmail", event.getWhiteLabel().getSupportEmail());
			String domainUrl = event.getWhiteLabel().getHostBaseUrl();
			substitutionData.put("asker_event_URL",
				(isNotBlank(domainUrl) ? domainUrl : uiBaseurl) + getEventPath() + event.getEventURL());
		} else {
			substitutionData.put("asker_event_URL", uiBaseurl + getEventPath() + event.getEventURL());
		}
		substitutionData.put("asker_name",
				isNotBlank(user.getFirstName()) ? user.getFirstName() : STRING_DASH);
		substitutionData.put("asker_email",
				isNotBlank(user.getEmail()) ? user.getEmail() : STRING_DASH);
		try {
			emailMessage.setSubstitutionData(substitutionData);
			sendGridMailPrepareService.sendSupportOrContactMail(emailMessage, event, receivers);
		} catch (Exception e) {
			log.error(EXCEPTION, e);
		}
	}

	/**
	 * Method to use to see if an event is isEligibleForRelease for releasing their phone
	 * number
	 */
	@Override
	public boolean isEligibleForRelease(Event event, boolean prod) {

		log.info("Checking if the following event can have their number released: {}" , event.getEventURL());

		int daysBack = -3;
		int daysForward = 33;
		if (prod) {
			daysBack = -14;
			daysForward = 90;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date d = calendar.getTime();

		Calendar calendar2 = Calendar.getInstance();
		calendar2.add(Calendar.DATE, daysForward);
		Date d2 = calendar2.getTime();

		boolean isTextToGiveActive = isTextToGiveActive(event, daysForward);

		if (isAnyEventModuleActivated(event)) {
			log.info("    A module is active.");
			if (!isTextToGiveActive && isAllEventModuleDatesBeforeGivenDate(event, d)) {
				log.info("    A module is active but all module end dates are older than {} days old..." , daysBack
						);
				log.info(RELEASING);
				return true;
			} else {
				log.info("    A module is active and one of the modules ended or is ending after {}  days ago. Event is not elidgable for release."
						, daysBack);
				return false;
			}
		} else {
			if (!isTextToGiveActive && isAllEventModuleDatesBeforeGivenDate(event, new Date())) {
				log.info("    No module activated. All module end dates are in the past....");
				log.info(RELEASING);
				return true;
			} else if (!isTextToGiveActive && isAllEventModuleDatesAfterGivenDate(event, d2)) {
				log.info("    No module activated. All module end dates are more than {} days in the future... " , daysForward
						);
				log.info(RELEASING);
				return true;
			} else {
                log.info( "No module activated, but one or more of the module end dates is coming up soon.");
				return false;
			}
		}

	}

	protected boolean isTextToGiveActive(Event event, int daysForward) {
		DonationSettings donationSetting = this.donationSettingsService.getByEventId(event.getEventId());
		boolean isBeforeCurrentDate = false;
		if(null != donationSetting) {
            if (donationSetting.getSampleTextToGiveDate() != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(donationSetting.getSampleTextToGiveDate());
                calendar.add(Calendar.DAY_OF_MONTH, daysForward);
                Date currentDate = DateUtils.getCurrentDate();
                isBeforeCurrentDate = calendar.getTime().after(currentDate);
            }
            return donationSetting.isTextToGiveActivated() || isBeforeCurrentDate;
        }else{
		    return false;
        }
	}

	protected boolean isAnyEventModuleActivated(Event event) {
		Auction auction = this.auctionService.findByEvent(event);
		Raffle raffle = this.raffleService.findByEvent(event);
		CauseAuction causeAuction = this.causeAuctionService.findByEvent(event);
		Ticketing ticketing = this.ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);

		boolean isAuctionActivated = null != auction && auction.isActivated();
		boolean isRaffleActivated = null != raffle && raffle.isActivated();
		boolean isCauseAuctionActivated = null != causeAuction && causeAuction.isActivated();
		boolean isTicketingActivated = null != ticketing && ticketing.getActivated();

		return isAuctionActivated || isRaffleActivated || isCauseAuctionActivated || isTicketingActivated;
	}

	protected boolean isAllEventModuleDatesBeforeGivenDate(Event event, Date date) {
		Auction auction = this.auctionService.findByEvent(event);
		Raffle raffle = this.raffleService.findByEvent(event);
		CauseAuction causeAuction = this.causeAuctionService.findByEvent(event);
		Ticketing ticketing = this.getTicketing(event);

		Date auctionEndDate = auction != null ? auction.getActualEndDate() : new Date();
		Date raffleEndDate = raffle != null ? raffle.getEndDate() : new Date();
		Date causeAuctionEndDate = causeAuction != null ? causeAuction.getEndDate() : new Date();
		Date ticketingEndDate = ticketing != null ? ticketing.getEventEndDate() : new Date();

		return auctionEndDate.before(date) && raffleEndDate.before(date) && causeAuctionEndDate.before(date)
				&& ticketingEndDate.before(date);
	}

	protected boolean isAllEventModuleDatesAfterGivenDate(Event event, Date date) {
		Auction auction = this.auctionService.findByEvent(event);
		Raffle raffle = this.raffleService.findByEvent(event);
		CauseAuction causeAuction = this.causeAuctionService.findByEvent(event);
		Ticketing ticketing = this.getTicketing(event);

		Date auctionEndDate = null != auction ? auction.getActualEndDate() : new Date();
		Date raffleEndDate = null != raffle ? raffle.getEndDate() : new Date();
		Date causeAuctionEndDate = null != causeAuction ? causeAuction.getEndDate() : new Date();
		Date ticketingEndDate = null != ticketing ? ticketing.getEventEndDate() : new Date();

		return auctionEndDate.after(date) && raffleEndDate.after(date) && causeAuctionEndDate.after(date)
				&& ticketingEndDate.after(date);
	}

	/**
	 * called for each event. check if event is elidgable for release if yes,
	 * proceed, else continue. 1. Remove number from {@link Event} 2. Set event
	 * checklist to have auctionBidSubmitted false, raffleTicketSubmitted false,
	 * and CauseAuctionPledgeSubmitted false 3. find the number in
	 * {@link TwilioNumberHistory} 4. if we already have more than 10 available
	 * numbers, release this one. Set active to false and activeEventId to false
	 * 5. if we have less than 10 available numbers, set activeEventId to null,
	 * leave active as true, append the event id to historical event ids.
	 */
	@Override
	@Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
	public Event releaseTwilioNumber(Event event, boolean prod) {
		if (this.isEligibleForRelease(event, prod)) {
			log.info("=====================================");
			log.info("RELEASING");
			log.info("URL: {}" , event.getEventURL());
			log.info("ID: {}" , event.getEventId());
			log.info("=====================================");

			event.setPhoneNumber(0);
			event.setCountryCode(null);
			eventRepoService.save(event);

			EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
			eventChecklist.setAuctionBidSubmitted(false);
			eventChecklist.setRaffleTicketSubmitted(false);
			eventChecklist.setCauseAuctionPledgeSubmitted(false);
			eventChecklistService.save(eventChecklist);

			TwilioNumberHistory twilioNumberHistory = twillioNumberHistoryService
					.findByActiveEventId(event.getEventId());
			if (twilioNumberHistory != null) {
				long count = twillioNumberHistoryService.findAvailableCount();
				if (count >= 10
						&& (twilioNumberHistory.isShouldKeep() == null || !twilioNumberHistory.isShouldKeep())) {
					try {
						textMessageService.releaseNumber(twilioNumberHistory.getTwillioId());
						twilioNumberHistory.setActive(false);
						twilioNumberHistory.setActiveEventId(null);
						twillioNumberHistoryService.save(twilioNumberHistory);
						return event;
					} catch (Exception e) {
						log.error("releaseTwilioNumber", e);
					}
				} else {
					twilioNumberHistory.setActiveEventId(null);
					twilioNumberHistory.setActive(true);
					twillioNumberHistoryService.save(twilioNumberHistory);
					return event;
				}
			}
			return null;
		} else {
			return null;
		}
	}

	@Override
	@Transactional
	public void releaseActiveNumberWhichIsNotInDB() {
		try {
			ResourceSet<IncomingPhoneNumber> activeNumbers = textMessageService.getAllActiveNumbersFromTwillio();
			for (IncomingPhoneNumber activeNumber : activeNumbers) {
				try {
					TwilioNumberHistory twilioNumberHistory = twillioNumberHistoryService
							.findByTwillioId(activeNumber.getSid());
					if (twilioNumberHistory == null) {
						textMessageService.releaseNumber(activeNumber.getSid());
					}
				} catch (Exception e) {
					log.error("releaseActiveNumberWhichIsNotInDB while", e);
				}
			}
		} catch (Exception e) {
			log.error("releaseActiveNumberWhichIsNotInDB", e);
		}
	}

	@Override
	public List<String> numbersToRelease() {
		List<String> numbersNeedToRelease = new ArrayList<>();
		try {
			ResourceSet<IncomingPhoneNumber> activeNumbers = textMessageService.getAllActiveNumbersFromTwillio();
			for (IncomingPhoneNumber activeNumber : activeNumbers) {
				try {
					TwilioNumberHistory twilioNumberHistory = twillioNumberHistoryService
							.findByTwillioId(activeNumber.getSid());
					if (twilioNumberHistory == null) {
						numbersNeedToRelease.add(activeNumber.getSid());
					}
				} catch (Exception e) {
					log.error("releaseActiveNumberWhichIsNotInDB while", e);
				}
			}
		} catch (Exception e) {
			log.error("numbersToRelease", e);
		}
		return numbersNeedToRelease;
	}

	// used REQUIRES_NEW because if something went wrong in caller method this
	// method transaction never call back
	@Override
	@Transactional(rollbackFor = {
			Exception.class}, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public Event assignEventNumber(long eventId, String smsurl, CountryCode countryCode) {
        //Always read from db get latest data as we have issue with multiple twillo number to same event
        Event event = this.eventRepository.findEventByEventIdWithoutCache(eventId);
        if (event.getPhoneNumber() == 0) {
            createOrUpdateEventNumber(smsurl, countryCode, event);
            eventRepoService.save(event);
        }
        return event;
    }

    private void createOrUpdateEventNumber(String smsurl, CountryCode countryCode, Event event) {
        TwilioNumberHistory twilioNumberHistoryForEvent = twillioNumberHistoryService.findByActiveEventId(event.getEventId());
        AccelEventsPhoneNumber accelEventsPhoneNumber;
        if (twilioNumberHistoryForEvent == null) {
            log.info("assignEventNumber process started for event {} smsUrl {}", event, smsurl);
            countryCode = (countryCode != null ? countryCode : CountryCode.US);
            List<TwilioNumberHistory> availableTwilioNumbers = twillioNumberHistoryService
                    .findByActiveNumberWithInActiveEventId(countryCode);
            if (availableTwilioNumbers.isEmpty()) {
                IncomingPhoneNumber incomingPhoneNumber = purchaseNewPhoneNumberForTwilio(smsurl, countryCode);
                log.info("Event Phone number created for event {} smsUrl {} updatedPhoneNumber {}", event, smsurl,incomingPhoneNumber!=null? incomingPhoneNumber.getPhoneNumber():null);
                if (incomingPhoneNumber != null) {
                    twillioNumberHistoryService.save(createNewTwilioHistory(countryCode, event, incomingPhoneNumber));
                    accelEventsPhoneNumber = roPhoneNumberService.parseIncomingSender(incomingPhoneNumber.getPhoneNumber().toString());
                    event.setPhoneNumberAndCountryCode(accelEventsPhoneNumber);
                    log.info("Event Phone number created for event {} smsUrl {} updatedPhoneNumber {}", event, smsurl, incomingPhoneNumber.getPhoneNumber());
                }
            } else {
                TwilioNumberHistory twilioNumberHistory = createTwilioHistoryFromAvailableTwilioNumbers(event, availableTwilioNumbers);
                twillioNumberHistoryService.save(twilioNumberHistory);
                textMessageService.updateNumber(twilioNumberHistory.getTwillioId(), smsurl);
                log.info("Event Phone number updated for event {} smsUrl {} updatedPhoneNumber {}", event, smsurl, twilioNumberHistory.getPhoneNumber());

                accelEventsPhoneNumber = new AccelEventsPhoneNumber(twilioNumberHistory);
                event.setPhoneNumberAndCountryCode(accelEventsPhoneNumber);
            }
        }
    }

    // used REQUIRES_NEW because if something went wrong in caller method this
    // method transaction never call back
    @Override
    @Transactional(rollbackFor = {
            Exception.class}, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public void assignEventNumberForDataFix(Event event) {
        String smsUrl =  apiBaseUrl + STRING_SMS_URL + event.getEventId();
        createOrUpdateEventNumber(smsUrl, event.getCountryCode(), event);
    }

	private TwilioNumberHistory createTwilioHistoryFromAvailableTwilioNumbers(Event event, List<TwilioNumberHistory> availableTwilioNumbers) {
		TwilioNumberHistory twilioNumberHistory = availableTwilioNumbers.get(0);
		twilioNumberHistory.setActive(true);
		twilioNumberHistory.setActiveEventId(event.getEventId());
		twilioNumberHistory.setStackTrace("TwilioEmptyElse : " + getStackTrace() + " Event Phone number: " + event.getPhoneNumber() + " Date:" + new Date());
		twilioNumberHistory.setHistoricalEventIds(
				twilioNumberHistory.getHistoricalEventIds() + STRING_COMMA + event.getEventId());
		return twilioNumberHistory;
	}

	private TwilioNumberHistory createNewTwilioHistory(CountryCode countryCode, Event event, IncomingPhoneNumber incomingPhoneNumber) {
		TwilioNumberHistory twilioNumberHistory = new TwilioNumberHistory();
		twilioNumberHistory.setActive(true);
		twilioNumberHistory.setActiveEventId(event.getEventId());
		twilioNumberHistory.setHistoricalEventIds(event.getEventId() + STRING_COMMA);
		twilioNumberHistory.setPhoneNumber(incomingPhoneNumber.getPhoneNumber().getEndpoint());
		twilioNumberHistory.setCountryCode(countryCode);
		twilioNumberHistory.setTwillioId(incomingPhoneNumber.getSid());
		twilioNumberHistory.setStackTrace("TwilioEmptyIF : " + getStackTrace() + " Event Phone number : " + event.getPhoneNumber() + "Date:" + new Date());
		return twilioNumberHistory;
	}

	private IncomingPhoneNumber purchaseNewPhoneNumberForTwilio(String smsurl, CountryCode countryCode) {
		IncomingPhoneNumber incomingPhoneNumber;
		try {
			incomingPhoneNumber = textMessageService.registerNumber(smsurl, countryCode.toString());
		} catch (ApiException e) {//NOSONAR
			log.error("Twillio ApiException", e);
			throw new ApiException("Unable to use twilio to register the number.", e);
		}
		return incomingPhoneNumber;
	}

    protected String getStackTrace() {
		StringBuilder sb = new StringBuilder();
		for (StackTraceElement stackTraceElement : Thread.currentThread().getStackTrace()) {
			if (stackTraceElement != null && stackTraceElement.toString().contains("com")) {
				sb.append(stackTraceElement.toString());
				sb.append("\n");
			}
		}
		return sb.toString();
	}

	@Override
	@Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
	public Event createOrAssignEventNumber(Event event, ModuleType moduleType,
										   AccelEventsPhoneNumber phoneNumber, User user) {
		String smsUrl = apiBaseUrl + STRING_SMS_URL + event.getEventId();
		// set phone number
		if (event.getPhoneNumber() == 0) {
			event = assignEventNumber(event.getEventId(), smsUrl, phoneNumber.getCountryCode()); //NOSONAR
		}

		EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
		if (ModuleType.AUCTION.equals(moduleType)) {
			Optional<Item> itemOptional = itemService.getItem(event.getEventId(), "AUC");
			double amount = getSampleBidAmount(event, itemOptional);
			sendText(event, phoneNumber, textMessageUtils.getAuctionDemoMessage(event, amount));
			eventChecklist.setAuctionBidSubmitted(true);
			eventChecklistService.save(eventChecklist);
		} else if (ModuleType.RAFFLE.equals(moduleType)) {
			sendText(event, phoneNumber, textMessageUtils.getRaffleDemoMessage(event));
			eventChecklist.setRaffleTicketSubmitted(true);
			eventChecklistService.save(eventChecklist);
			raffleTicketService.addDefaultTicketPurchase(event.getRaffleId(), user);
		} else if (ModuleType.CAUSEAUCTION.equals(moduleType)) {
			sendText(event, phoneNumber, textMessageUtils.getCauseAuctionDemoMessage(event));
			eventChecklist.setCauseAuctionPledgeSubmitted(true);
			eventChecklistService.save(eventChecklist);
		} else if (ModuleType.DONATION.equals(moduleType)) {
			DonationSettings donationSetting = this.donationSettingsService.getByEventId(event.getEventId());
			if (donationSetting.getSampleTextToGiveDate() == null) {
				donationSetting.setSampleTextToGiveDate(new Date());
			}
			sendText(event, phoneNumber, textMessageUtils.getDonationSampleEvent(event));
			eventChecklist.setTextToDonate(true);
			eventChecklistService.save(eventChecklist);
		}
		return event;
	}

	protected double getSampleBidAmount(Event event, Optional<Item> itemOptional) {
		double amount;
		if (itemOptional.isPresent()) {
			Item item = itemOptional.get();
			double bidIncrement = itemService.getBidIncrement(item, this.auctionService.findByEvent(event));
			amount = (item.getCurrentBid() != 0) ? item.getCurrentBid() + bidIncrement : item.getStartingBid();
		} else {
			log.info("For some reason we couldn't find the item");
			amount = 400;
		}
		return amount;
	}

	private void sendText(Event event, AccelEventsPhoneNumber phoneNumber, String message) {
		try {
			textMessageService.sendText(event.getAePhoneNumber(), phoneNumber, message);
		} catch (TwilioException e) {
			log.info("{}{}",EXCEPTION_OCCURED , e);
		}
	}

	@Override
	public List<Event> getEventsByEventStatus(EventStatus eventStatus) {
		return eventRepository.findByEventStatus(eventStatus);
	}

	@Override
	public Set<Event> getEventsByUser(User user) {
		return staffService.findByUserAndEventIsNotNull(user).stream().map(Staff::getEvent).filter(Objects::nonNull).collect(Collectors.toSet());
	}

	@Override
	public Set<Event> getEventsByAdminUser(User user) {
        return getEvents(user, StaffRole.admin);
    }

	public Set<Event> getEventsByStaffUser(User user) {
        return getEvents(user, StaffRole.staff);
    }

    private Set<Event> getEvents(User user, StaffRole role) {
        return roStaffService.findByUserAndRoleAndEventIsNotNull(user, role).stream().map(Staff::getEvent).filter(Objects::nonNull).collect(Collectors.toSet());
    }


	@Override
	public Set<Event> getEventByAuctionBidUser(User user) {
		return eventRepository.findEventByAuctionBidUser(user, EventStatus.EVENT_DELETED);
	}

	@Override
	public Set<Event> getEventByCauseAuctionPledgeUser(User user) {
		return eventRepository.findEventByCauseAuctionPledgeUser(user, EventStatus.EVENT_DELETED);
	}

	@Override
	public Set<Event> getEventByRaffleSubmittedTicketUser(User user) {
		return eventRepository.findEventByRaffleSubmittedTicketUser(user, EventStatus.EVENT_DELETED);
	}

	@Override
	public Set<Event> getEventByRafflePurchasedTicketUser(User user) {
		return eventRepository.findEventByRafflePurchasedTicketUser(user, EventStatus.EVENT_DELETED);
	}

/*
	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public void connectEventToStripeAccountDifferedAccount(Stripe stripeByEvent, Event event, User user)
			throws StripeException {
		//Alok:3803 : This api is not recommend more by stripe
		log.info("stripe created for event id: "+ event.getEventId() + " ,  by " + user.getEmail());
		//set default false if exist
		this.stripeService.updateStripeDefaultAccount(event);

		Account account = stripePaymentService.createDifferedAccount(COUNTRY_CODE, user.getEmail());
		//stripeByEvent.setAccessToken(account.getKeys().getSecret());
		//stripeByEvent.setStripePublishableKey(account.getKeys().getPublishable());
		stripeByEvent.setStripeUserId(account.getId());
		stripeByEvent.setAccountSource("DEFFERED");
		stripeByEvent.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
		stripeByEvent.setStaff(staffService.findByEventAndUser(event, user, false));
		stripeByEvent.setActivated(false);
		stripeByEvent.setConnectedOn(new Date());
		stripeByEvent.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
		stripeService.save(stripeByEvent);
		event.setCreditCardEnabled(true);
		this.save(event);
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public void activateStripeAccountFromWebHook(String json) {
		log.info("inside activateStripeAccountFromWebHook start json:=" + json);
		try {
			Account account = stripePaymentService.getAccountByEventType(json);
			if (account != null && account.getDetailsSubmitted()) {
				log.info("activate stripeaccount id:==>" + account.getId());
				stripeService.activeStripe(account.getId());
			} else {
				log.info("account is not activated yet");
			}
		} catch (StripeException e) {
			log.info("inside activateStripeAccountFromWebHook exception");
			log.error("Activate Stripe Account : ", e);
		}
		log.info("inside activateStripeAccountFromWebHook json end");
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public void saveWithAnotherEventConnectedStripeAccount(Stripe connectedAccount, Stripe stripeByEvent, Event event) {
		stripeByEvent.setAccessToken(connectedAccount.getAccessToken());
		stripeByEvent.setStripePublishableKey(connectedAccount.getStripePublishableKey());
		stripeByEvent.setStripeUserId(connectedAccount.getStripeUserId());
		stripeByEvent.setAccountSource("CONNECTED EVENTS");
		stripeByEvent.setStaff(connectedAccount.getStaff());
		stripeByEvent.setActivated(connectedAccount.isActivated());
		if(connectedAccount.isActivated()){
			stripeByEvent.setActivatedOn(new Date());
		}
		// Added By hitesh for square
		stripeByEvent.setPaymentGateway(connectedAccount.getPaymentGateway());
		stripeByEvent.setSquareMerchantId(connectedAccount.getSquareMerchantId());
		stripeByEvent.setSquareMerchantLocation(connectedAccount.getSquareMerchantLocation());

		stripeService.save(stripeByEvent);
		event.setCreditCardEnabled(true);
		this.save(event);
		if (connectedAccount.isActivated()) {
			EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
			eventChecklist.setActivatePaymentProcessing(true);
			eventChecklistService.save(eventChecklist);
		}
	}
*/

    @Transactional
    public void createSquareRecord(Event event, Staff staff, Stripe square) {
		square.setAccessToken(squareConfiguration.getAppSecretKey());
		square.setStripePublishableKey(squareConfiguration.getClientID());
		square.setStripeUserId(SANDBOX);
		square.setAccountSource("CONNECTED EVENTS");
		square.setStaff(staff);
		square.setActivated(true);
		square.setConnectedOn(new Date());
		square.setActivatedOn(new Date());
		square.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
		square.setSquareMerchantId(SANDBOX);
		square.setSquareMerchantLocation(SANDBOX);
		stripeService.save(square);

		event.setCreditCardEnabled(true);
		eventRepoService.save(event);

		EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
		eventChecklist.setActivatePaymentProcessing(true);
		eventChecklistService.save(eventChecklist);
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public void saveStripeAccountOfCustomerForEvent(Stripe stripeByEvent, String code, JSONObject response,
													Event event, User user) throws JSONException {
        log.info("saveStripeAccountOfCustomerForEvent eventId {} date {}",event.getEventId(),new Date());
        if (roUserService.isSuperAdminUser(user) && !roStaffService.isEventAdmin(event, user)) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_ALLOW_TO_PAYMENT_PROCESSING);
        }

        if (EnumPaymentGateway.PAYPAL.value().equals(stripeByEvent.getPaymentGateway())) {

            stripeByEvent.setAccessToken(response.getString("tracking_id"));

            //here : setting payer id in publishable key
            if (response.has(MERCHANT_ID)) {
                stripeByEvent.setStripePublishableKey(response.getString(MERCHANT_ID));
            }

            if (response.has("oauth_integrations")) {
                JSONArray integrations = response.getJSONArray("oauth_integrations");
                JSONObject integration = integrations.getJSONObject(0);
                if (integration.has("oauth_third_party")) {
                    JSONArray thirdParty = integration.getJSONArray("oauth_third_party");
                    String merchantClientId = thirdParty.getJSONObject(0).getString("merchant_client_id");
                    stripeByEvent.setStripeUserId(merchantClientId);
                }
            }

            if (response.has("payments_receivable")) {
                boolean isVerified = response.getBoolean("payments_receivable");
                if (isVerified) {
                    enablePaymentProcessing(stripeByEvent, event);
                }
            }

        } else {

            log.info("stripe save account for event id: {}  ,  by {} , id:{}", event.getEventId(), user.getEmail(), stripeByEvent.getId());
            stripeByEvent.setAccessToken((String) response.get(ACCESS_TOKEN));
            stripeByEvent.setTokenType((String) response.get("token_type"));
            stripeByEvent.setRefreshToken((String) response.get("refresh_token"));

            if (EnumPaymentGateway.SQUARE.value().equals(stripeByEvent.getPaymentGateway())) {
                log.info("saveStripeAccountOfCustomerForEvent SQUARE eventId {} date {}",event.getEventId(),new Date());
                Location location = squarePaymentService.getLocationId(stripeByEvent.getAccessToken());
                stripeByEvent.setSquareMerchantLocation(location.getId());
                stripeByEvent.setStripePublishableKey(squareConfiguration.getClientID());
                stripeByEvent.setStripeUserId((String) response.get(MERCHANT_ID));
                stripeByEvent.setLivemode(true);
                Instant instant = Instant.parse((String) response.get("expires_at"));
                stripeByEvent.setTokenExpiracyDate(new Date(instant.getMillis()));
                stripeByEvent.setSquareMerchantId((String) response.get(MERCHANT_ID));
                stripeByEvent.setScope(SQUARE_SCOPE);

                log.info("square location for event id: {} , location:{}", event.getEventId(), location);
                log.info("square location capabilities: {} , status:{}", location.getCapabilities(), location.getStatus());

                if (location.getCapabilities() != null && location.getCapabilities().contains("PROCESSING")
                        && "ACTIVE".equals(location.getStatus())) {
                    enablePaymentProcessing(stripeByEvent, event);
                }

            } else {
                log.info("saveStripeAccountOfCustomerForEvent STRIPE eventId {} date {}",event.getEventId(),new Date());
                stripeByEvent.setStripePublishableKey((String) response.get("stripe_publishable_key"));
                stripeByEvent.setStripeUserId((String) response.get("stripe_user_id"));
                stripeByEvent.setScope((String) response.get("scope"));
                stripeByEvent.setLivemode((Boolean) response.get("livemode"));

                try {
                    Account account = stripePaymentService.retrieveAccount(stripeByEvent.getStripeUserId());
                    stripeByEvent.setCountry(account.getCountry());
                    if (Boolean.TRUE.equals(account.getDetailsSubmitted())) {
                        enablePaymentProcessing(stripeByEvent, event);
                    }
                } catch (StripeException e) {
                    log.error("StripeException", e);
                }

            }
        }
        stripeByEvent.setConnectedOn(new Date());
        stripeByEvent.setCode(code);
        stripeByEvent.setAccountSource("Oauth");
        Staff staff = roStaffService.findByEventAndUserNotExhibitor(event, user, false);
        if (null != staff) {
            stripeByEvent.setStaff(staff);
        } else {
            stripeByEvent.setStaff(staffService.findAdminAndStaffByEvent(event).get(0));
        }

        stripeService.save(stripeByEvent);
        event.setCreditCardEnabled(true);
        eventRepoService.save(event);
        eventPlanConfigService.updateAttendeeImportLimitOnceStripeIsConnected(event);
    }

	@Override
	public void enablePaymentProcessing(Stripe stripeByEvent, Event event) {
		stripeByEvent.setActivated(true);
		stripeByEvent.setActivatedOn(new Date());
		EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
		eventChecklist.setActivatePaymentProcessing(true);
		eventChecklistService.save(eventChecklist);
	}

	protected void bidCalculator(Item item, double itemBid, double bidIncrement, Event event,Map<String,String> languageMap,String languageCode) {
		throwExceptionIfFirstBidIsLessThanStartingBid(item, itemBid);
		boolean isFirstBid = item.getCurrentBid() == 0;
		if (!isFirstBid) {
			boolean isBuyItNowPresent = item.getBuyItNowPrice() == 0;
			if (isBuyItNowPresent) {
				if (!isValidBid(item, itemBid, bidIncrement)) {
					throwMinimumBidIncrementException(bidIncrement, event, item.getCurrentBid(),languageMap,languageCode);
				}
			} else {
				if (itemBid < item.getBuyItNowPrice() && !isValidBid(item, itemBid, bidIncrement)) {
					throwMinimumBidIncrementException(bidIncrement, event, item.getCurrentBid(),languageMap,languageCode);
				}
			}
		}
	}

	protected void throwExceptionIfFirstBidIsLessThanStartingBid(Item item, double itemBid) {
		boolean isFirstBid = item.getCurrentBid() == 0;
		if (isFirstBid && itemBid < item.getStartingBid()) {
			throw new NotAcceptableException(AuctionExceptionMsg.BID_SHOULD_BE_GREATER_THAN_STARTING_BID);
		}
	}

	private void throwMinimumBidIncrementException(double bidIncrement, Event event, double currentBid,Map<String,String> languageMap,String languageCode) {
		log.info(AuctionExceptionMsg.BID_SHOULD_BE_GREATER_THAN_STARTING_BID.getErrorMessage());
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
		AuctionExceptionMsg exception = AuctionExceptionMsg.MINIMUM_BID_INCREMENT_FOR_THIS_ITEM;
		exception.setDeveloperMessage(MINIMUM_BID_INCREMENT_FOR_THIS_ITEM.
				replace("{bidIncrement}", (event.getCurrency().getSymbol() + bidIncrement)).
				replace("{minimumBid}", (event.getCurrency().getSymbol() + (currentBid + bidIncrement))));
		exception.setErrorMessage(resourceBundle.getString(languageMap.get(Constants.MINIMUM_BID_INCREMENT_FOR_THIS_ITEM)).
				replace("{bidIncrement}", (event.getCurrency().getSymbol() + bidIncrement)).
				replace("{minimumBid}", (event.getCurrency().getSymbol() + (currentBid + bidIncrement))));
		log.info(exception.getErrorMessage());
		throw new NotAcceptableException(exception);
	}

	protected boolean isValidBid(Item item, double itemBid, double bidIncrement) {
        BigDecimal decimal = BigDecimal.valueOf(itemBid - item.getCurrentBid());
        decimal = decimal.setScale(2, RoundingMode.HALF_UP);
        return decimal.doubleValue() >= bidIncrement;
	}

	protected void checkModuleActivateAndNotExpired(User user, ModuleType moduelType, Event event, Date currentDate) {
		if (user != null && roStaffService.isEventStaffOrAdmin(event, user)) {
			log.info("Staff or Admin Person can Bid without activating the module.");
		} else {
			if (moduelType == ModuleType.AUCTION) {
                checkAuctionActivated(event, currentDate);
            } else if (moduelType == ModuleType.RAFFLE) {
                checkRaffleActivated(event, currentDate);
            } else if (moduelType == ModuleType.CAUSEAUCTION) {
                checkCauseAuctionActivated(event, currentDate);
            }
		}
	}

    private void checkCauseAuctionActivated(Event event, Date currentDate) {
        CauseAuction causeAuction = causeAuctionService.findByEvent(event);
        if (!causeAuction.isActivated() || currentDate.after(causeAuction.getEndDate())) {
            log.info(FundANeedExceptionMsg.PLEDGE_NOT_ACTIVE.getErrorMessage());
            throw new NotAcceptableException(FundANeedExceptionMsg.PLEDGE_NOT_ACTIVE);
        }
    }

    private void checkRaffleActivated(Event event, Date currentDate) {
        Raffle raffle = raffleService.findByEvent(event);
        if (!raffle.isActivated() || currentDate.after(raffle.getEndDate())) {
            log.info(RaffleExceptionMsg.RAFFLE_NOT_ACTIVE.getErrorMessage());
            throw new NotAcceptableException(RaffleExceptionMsg.RAFFLE_NOT_ACTIVE);
        }
    }

    private void checkAuctionActivated(Event event, Date currentDate) {
        Auction auction = auctionService.findByEvent(event);
        if (!auction.isActivated() || (currentDate.after(auction.getActualEndDate()))) {
            log.info(AuctionExceptionMsg.AUCTION_NOT_ACTIVE.getErrorMessage());
            throw new NotAcceptableException(AuctionExceptionMsg.AUCTION_NOT_ACTIVE);
        }
    }

    // need to move to service layer end

	@Override
	public List<StripeTransactionDto> getCustomerTransaction(Event event) {
		List<StripeTransactionDto> transctions = new ArrayList<>();
		List<StripeTransaction> stripeTransactions = stripeTransactionService.findAllByEvent(event);
		StripeTransactionDto stripeTransactionDto;
		for (StripeTransaction stripeTranaction : stripeTransactions) {
			if (!stripeTranaction.getSource().equals(StripeTransactionSource.MODULE_ACTIVE)) {
				stripeTransactionDto = new StripeTransactionDto();
				stripeTransactionDto.setAmount(stripeTranaction.getAmount());
				stripeTransactionDto.setSource(stripeTranaction.getSource().toString());
				// if we get from stripe api then we will get this status.
				stripeTransactionDto.setStatus(SUCCESS);
				stripeTransactionDto.setName(stripeTranaction.getUser().getFirstName());
				stripeTransactionDto.setPhoneNumber(phoneNumberService.getDisplayNumber(stripeTranaction.getUser()));
				transctions.add(stripeTransactionDto);
				break;
			}
		}
		return transctions;
	}

	@Override
	public void updateUserByNewUser(User user, User newUser) {
		eventInteractionRepository.updateByNewUser(user.getUserId(), newUser.getUserId());
	}

	@Transactional(isolation = Isolation.READ_COMMITTED)
	@Override
	public void updateGeneralSetting(Event event) {
		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
		if (ticketing == null) {
			ticketing = ticketingHelperService.createNewTicketing(event);
		}
		event.setTicketingId(ticketing.getId());
		eventRepoService.save(event);
	}

	@Override
	public Ticketing getTicketing(Event event) {
		return ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
	}

	// TODO : Alok
//	@Transactional(isolation = Isolation.READ_COMMITTED)
//	@Override
//	public void activeStripeAccountForDB() {
//		List<Stripe> stripes = stripeService.findAll();
//		if (stripes != null && !stripes.isEmpty()) {
//			for (Stripe stripe : stripes) {
//				String accountid = stripe.getStripeUserId();
//				if (StringUtils.isNotBlank(accountid)) {
//					try {
//						Account account = stripePaymentService.retrieveAccount(accountid);
//						if (account.getDetailsSubmitted()) {
//							stripe.setActivated(true);
//						} else {
//							stripe.setActivated(false);
//						}
//						stripeService.save(stripe);
//					} catch (StripeException e) {
//						log.info("activeStripeAccountForDB exception for accountid==>" + accountid);
//						log.error("Activate Stripe Account For DB : ", e);
//					}
//				} else {
//					stripe.setActivated(false);
//					stripeService.save(stripe);
//				}
//			}
//		}
//	}

	@Override
	public Set<Event> getEventByWhiteLabelOwner(User user) {
		return eventRepository.findEventsByWhiteLabelUser(user, EventStatus.EVENT_DELETED);
	}

	@Override
	public List<Event> getWhiteLabelEvents(WhiteLabel whiteLabel, String search) {
		if(isNotBlank(search)){
			return eventRepository.findEventsByWhiteLabelAndEventStatusIsNullOrEventStatusNotWithSearch(whiteLabel, StringUtils.lowerCase(search), EventStatus.EVENT_DELETED);
		}else {
			return roEventService.getWhiteLabelEvents(whiteLabel);
		}
	}

	@Override
	public DataTableResponse getAllEventsDto(int page, int size, String searchString, boolean pastEvent, boolean upcomingEvent, boolean activeEvent, String sortField, String sortDirection, EventSearchFilter eventSearchFilter) {//NOSONAR
		DataTableResponse dataTableResponse = new DataTableResponse();
		Pageable pageable = getSortablePage(page, size, sortField, sortDirection);
		long totalCount;
		long filteredCount;
		List<Object> listData = new ArrayList<>();
        Page<Object[]> pageData;
		if (isNotBlank(searchString)) {
            if (GeneralUtils.isStringContainsUniCode(searchString)) {
                pageData = eventRepository.findAllEventByNameContainingOrEventURLContainingAndEventStatusIsNullOrEventStatusIsNotWithUnicode(searchString.trim(), pastEvent, upcomingEvent, activeEvent, pageable);
            }
            else {
                switch(eventSearchFilter){
                    case EVENT_NAME_URL:
                        pageData = eventRepository.findAllEventByNameOrEventURLAndEventStatusIsNullOrEventStatusIsNot(searchString.trim(),pastEvent, upcomingEvent, activeEvent, pageable);
                        break;
                    case SUBSCRIPTION:
                        pageData = eventRepository.findAllEventBySubscriptionAndEventStatusIsNullOrEventStatusIsNot(searchString.trim(), pastEvent, upcomingEvent, activeEvent, pageable);
                        break;
                    case STAFF_EMAIL:
                        pageData = eventRepository.findAllEventStaffAndEventStatusIsNullOrEventStatusIsNot(searchString.trim(), false, pastEvent, upcomingEvent, activeEvent, pageable);
                        break;
                    case EMAIL_DOMAIN:
                        pageData = eventRepository.findAllEventStaffAndEventStatusIsNullOrEventStatusIsNot(searchString.trim(), true, pastEvent, upcomingEvent, activeEvent, pageable);
                        break;
                    case CUSTOMER:
                        pageData = eventRepository.findAllEventByNameChargebeeCustomerIdAndEventStatusIsNullOrEventStatusIsNot(searchString.trim(), pastEvent, upcomingEvent, activeEvent, pageable);
                        break;
                    case INVOICE:
                        pageData = eventRepository.findAllEventByInvoiceIdAndEventStatusIsNullOrEventStatusIsNot(searchString.trim(), pastEvent, upcomingEvent, activeEvent, pageable);
                        break;
                    default:
                        pageData = eventRepository.findAllEventByNameContainingOrEventURLContainingAndEventStatusIsNullOrEventStatusIsNot(searchString.trim(),searchString.trim(), pastEvent, upcomingEvent, activeEvent, pageable);
                }
            }

            List<Long> eventIds = pageData.getContent().stream().map(e -> ((BigInteger) (e[0])).longValue()).collect(Collectors.toList());
            Map<Long, Long> eventRegistrantsOrUniqueAttendeeCountWithStaffsMap = getRegistrantsCountByEventIds(eventIds);
            Map<Long, Long> eventStaffCountMap = staffService.getStaffCountByEventIdsAndRoles(eventIds, Arrays.asList(StaffRole.staff, StaffRole.admin, StaffRole.eventandorgadmin));
            pageData.getContent().forEach(event -> {
				Map<String, Object> data = new LinkedHashMap<>();
				data.put(EVENT_ID, event[0]);
				data.put(EVENT_NAME, event[1]);
				data.put(EVENT_URL, event[2]);
				data.put(EVENT_END_DATE, event[3]);
				data.put(EVENT_CREATE_DATE, event[4]);
                data.put(STAFF_SMALL, eventStaffCountMap.getOrDefault(Long.parseLong(event[0].toString()), 0L));
                data.put(BILLING_TYPE, event[5]);
				data.put(REGISTRATIONS,eventRegistrantsOrUniqueAttendeeCountWithStaffsMap.get(Long.parseLong(event[0].toString())));
				data.put(EVENT_START_DATE,event[6]);
				data.put(ORGANIZER_SMALL,event[7]);
				data.put(ORGANIZER_URL,event[8]);
				data.put(ENTERPRISE,event[9]);
				data.put(ENTERPRISE_URL,event[10]);
				listData.add(data);
			});
			totalCount = pageData.getTotalElements();
			filteredCount = pageData.getNumberOfElements();
		} else {
		    if(pastEvent)
			    pageData = eventRepository.getPastEvents(pageable);
            else if(upcomingEvent)
                pageData = eventRepository.getUpcomingEvents(pageable);
            else if(activeEvent)
                pageData = eventRepository.getActiveEvents(pageable);
            else
                pageData = eventRepository.getAllEvents(pageable);
            List<Long> eventIds = pageData.getContent().stream().map(e -> ((BigInteger) (e[0])).longValue()).collect(Collectors.toList());
            Map<Long, Long> eventRegistrantsOrUniqueAttendeeCountWithStaffsMap = getRegistrantsCountByEventIds(eventIds);
            Map<Long, Long> eventStaffCountMap = staffService.getStaffCountByEventIdsAndRoles(eventIds, Arrays.asList(StaffRole.staff, StaffRole.admin, StaffRole.eventandorgadmin));
			pageData.getContent().forEach(event -> {
				Map<String, Object> data = new LinkedHashMap<>();
				data.put(EVENT_ID, event[0]);
				data.put(EVENT_NAME, event[1]);
				data.put(EVENT_URL, event[2]);
				data.put(EVENT_END_DATE, event[3]);
                data.put(EVENT_CREATE_DATE, event[4]);
                data.put(STAFF_SMALL, eventStaffCountMap.getOrDefault(Long.parseLong(event[0].toString()), 0L));
                data.put(BILLING_TYPE, event[5]);
                data.put(REGISTRATIONS,eventRegistrantsOrUniqueAttendeeCountWithStaffsMap.get(Long.parseLong(event[0].toString())));
                data.put(EVENT_START_DATE,event[6]);
                data.put(ORGANIZER_SMALL,event[7]);
                data.put(ORGANIZER_URL,event[8]);
                data.put(ENTERPRISE,event[9]);
                data.put(ENTERPRISE_URL,event[10]);
                listData.add(data);
			});

			totalCount = pageData.getTotalElements();
			filteredCount = pageData.getNumberOfElements();
		}

		dataTableResponse.setRecordsTotal(totalCount);
		dataTableResponse.setRecordsFiltered(filteredCount);
		dataTableResponse.setData(listData);
		return dataTableResponse;
	}

    public Object getCheckIns(List<Object[]> registrationsAndCheckInsData,Object eventId){
        Object checkIns =  registrationsAndCheckInsData.stream().filter(e -> e[0].equals(eventId)).map(id -> id[2]).findFirst().orElse(null);
        return !ObjectUtils.isEmpty(checkIns) ? checkIns : 0;
    }

	protected Pageable getSortablePage(int page, int size, String sortField, String sortDirection) {
        page = Math.max(page, 0);
        size = Math.max(size, 1);
		if (StringUtils.isNoneBlank(sortField)) {
			Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;
			switch (sortField) {
                case EVENT_NAME:
					return PageRequest.of(page, size, direction, "name");
                case BILLING_TYPE:
                    return PageRequest.of(page, size, direction, "billing_type");
                case EVENT_URL:
					return PageRequest.of(page, size, direction, "eventurl");
                case EVENT_START_DATE:
                    return PageRequest.of(page, size, direction , "t.event_start_date");
                case ORGANIZER_SMALL:
                    return PageRequest.of(page, size, direction , "organizer_name");
                case ENTERPRISE:
                    return PageRequest.of(page, size, direction , "enterprise_wl_name");
				default:
					return PageRequest.of(page, size, direction, "created_date");
			}
		} else {
			return PageRequest.of(page, size, Sort.Direction.DESC, "created_date");
		}

	}

	protected List<TicketAnalyticsDto> getSortablePageForAnalytics(int page, int size,
																   String sortField, String sortDirection, List<TicketAnalyticsDto> finalListData) {
			Comparator comparator;
			switch (sortField) {
				case EVENT_URL:
					comparator = Comparator.comparing(TicketAnalyticsDto::getEventUrl);
					break;
				case "endDate":
					comparator = Comparator.comparing(TicketAnalyticsDto::getEndDate);
					break;
				case "totalSales":
					comparator = Comparator.comparing(TicketAnalyticsDto::getTotalSales);
					break;
				case "totalTicketSold":
					comparator = Comparator.comparing(TicketAnalyticsDto::getTotalTicketSold);
					break;
				case "totalTicketSoldOverPeriod":
					comparator = Comparator.comparing(TicketAnalyticsDto::getTotalTicketSoldOverPeriod);
					break;
				case "changeOverPreviousPeriod":
					comparator = Comparator.comparing(TicketAnalyticsDto::getChangeOverPreviousPeriod);
					break;
				case "totalSaleOverPeriod":
					comparator = Comparator.comparing(TicketAnalyticsDto::getTotalSaleOverPeriod);
					break;
				default:
					comparator = Comparator.comparing(TicketAnalyticsDto::getEventName);
					break;
			}
			comparator = "asc".equalsIgnoreCase(sortDirection) ? comparator : comparator.reversed();
			finalListData.sort(comparator);

		return getSubListFromSourceList(finalListData, page, size);
	}

	private List<TicketAnalyticsDto> getSubListFromSourceList(List<TicketAnalyticsDto> sourceList, int page, int pageSize) {
		int fromIndex = (page) * pageSize;
		if(sourceList == null || sourceList.size() < fromIndex){
			return Collections.emptyList();
		}
		return sourceList.subList(fromIndex, Math.min(fromIndex + pageSize, sourceList.size()));
	}

	private String getTwilioNumber(CountryCode countryCode, BigInteger phoneNumber) {
		return countryCode == null ? null : String.format("+%s%d", countryCode.getCode(), phoneNumber);
	}

	protected boolean isTicketingTheOnlyEnabledModule(Event event) {
		return (!event.isSilentAuctionEnabled() && !event.isRaffleEnabled() && !event.isCauseAuctionEnabled() && event.isTicketingEnabled());
	}

	@Override
	public Date getEventEndDate(Event event) {
		boolean checkOnlyTicketingModule = false;
		if (isTicketingTheOnlyEnabledModule(event)) {
			checkOnlyTicketingModule = true;
		}

		List<Object[]> moduleEndDates = eventRepository.getEventEndDate(event.getEventId(), checkOnlyTicketingModule);
		Map<String, Date> moduleEndDateMap = moduleEndDates.stream().collect(Collectors.toMap(a -> (String) a[1], a -> (Date) a[0], (a, b) -> a));

        log.info("getEventEndDate | moduleEndDateMap {} | eventId {}",moduleEndDateMap,event.getEventId());

		List<Date> eventModuleDates = new ArrayList<>();
		if (event.isTicketingEnabled()) {
			addToList(eventModuleDates, ModuleType.TICKETING, moduleEndDateMap);
		}
		if (event.isSilentAuctionEnabled()) {
			addToList(eventModuleDates, ModuleType.AUCTION, moduleEndDateMap);
		}
		if (event.isCauseAuctionEnabled()) {
			addToList(eventModuleDates, ModuleType.CAUSEAUCTION, moduleEndDateMap);
		}
		if (event.isRaffleEnabled()) {
			addToList(eventModuleDates, ModuleType.RAFFLE, moduleEndDateMap);
		}

		if (eventModuleDates.isEmpty()) {
			return getEventEndDateWhenAllModulesDisabled(moduleEndDateMap);
		}

		return Collections.max(eventModuleDates);
	}

    @Override
    public Map<Long, Date> getEventsEndDate(List<Event> events) {

        Map<Long, Date> result = new HashMap<>();

        List<Long> eventIds = events.stream().map(Event::getEventId).collect(Collectors.toList());

        List<Object[]> moduleEndDates = eventRepository.getEventsEndDate(eventIds);

        Map<Long, Map<String, Date>> moduleEndDateMap = getEventDate(moduleEndDates);

        for (Event event : events) {
            Long eventId = event.getEventId();
            Map<String, Date> moduleEndDate = moduleEndDateMap.getOrDefault(eventId, Map.of());


            List<Date> enabledDates = new ArrayList<>();
            if(isTicketingTheOnlyEnabledModule(event)){
                addToList(enabledDates, ModuleType.TICKETING, moduleEndDate);
            }
            else{
                if (event.isTicketingEnabled()) {
                    addToList(enabledDates, ModuleType.TICKETING, moduleEndDate);
                }
                if (event.isSilentAuctionEnabled()){
                    addToList(enabledDates, ModuleType.AUCTION, moduleEndDate);
                }
                if (event.isCauseAuctionEnabled()){
                    addToList(enabledDates, ModuleType.CAUSEAUCTION, moduleEndDate);
                }
                if (event.isRaffleEnabled()){
                    addToList(enabledDates, ModuleType.RAFFLE, moduleEndDate);
                }
            }

            result.put(eventId, enabledDates.isEmpty()
                    ? getEventEndDateWhenAllModulesDisabled(moduleEndDate)
                    : Collections.max(enabledDates));
        }

        return result;
    }


    private Map<Long, Map<String, Date>> getEventDate(List<Object[]> rawData){
        Map<Long, Map<String, Date>> eventModuleMap = new HashMap<>();
        for (Object[] row : rawData) {
            Long eventId = ((Number) row[0]).longValue();
            Date endDate = (Date) row[1];
            String module = (String) row[2];

            eventModuleMap
                    .computeIfAbsent(eventId, k -> new HashMap<>())
                    .put(module, endDate);
        }
        return eventModuleMap;
    }

	protected void addToList(List<Date> eventModuleDates, ModuleType moduleType, Map<String, Date> moduleEndDateMap) {
		if (Objects.nonNull(moduleEndDateMap.get(moduleType.toString()))) {
			eventModuleDates.add(moduleEndDateMap.get(moduleType.toString()));
		}
	}

	private Date getEventEndDateWhenAllModulesDisabled(Map<String, Date> moduleEndDateMap) {
		List<Date> eventModuleDates = new ArrayList<>();
		addToList(eventModuleDates, ModuleType.AUCTION, moduleEndDateMap);
		addToList(eventModuleDates, ModuleType.CAUSEAUCTION, moduleEndDateMap);
		addToList(eventModuleDates, ModuleType.RAFFLE, moduleEndDateMap);

        return (!eventModuleDates.isEmpty()) ? Collections.max(eventModuleDates) : moduleEndDateMap.get(ModuleType.TICKETING); //NOSONAR
	}

	@Override
	public Set<Event> findOldEventsForCurrentUser(Long moduleId, User user) {
		List<Staff> staffList = staffService.findByUserAndEventIsNotNull(user);
		staffList = staffList.stream().filter(e -> e.getEvent() != null).collect(Collectors.toList());
		Event event;
		Set<Event> events = new HashSet<>();
		for (Staff staff : staffList) {
			event = staff.getEvent();
			if (event != null && event.getAuctionId() != moduleId) {
				events.add(staff.getEvent());
			}
		}

		return events;
	}

	@Override
	public Set<EventDto> findOldEventDtosForCurrentUser(Long moduleId, User user) {
		List<Staff> staffList = staffService.findByUserAndEventIsNotNull(user);
		staffList = staffList.stream().filter(e -> e.getEvent() != null && e.getRole().name().equals(StaffRole.admin.name())).collect(Collectors.toList());
		Event event;
		Set<EventDto> events = new HashSet<>();
		for (Staff staff : staffList) {
			event = staff.getEvent();
			if (event != null && event.getAuctionId() != moduleId) {
				events.add(new EventDto(staff.getEvent()));
			}
		}

		return events;
	}

    @Override
	public String getStripeKey(String eventURL) {
		Event event = roEventService.getEventByURL(eventURL);
		return getStripeKey(event);
	}

	@Override
	public String getStripeKey(Event event) {
		String stripeKey;
		Stripe stripe = this.roStripeService.findByEvent(event);
		if (stripe != null) {
			stripeKey = stripe.getStripePublishableKey();
		} else {
			throw new NotFoundException(NotFound.STRIPE_NOT_FOUND);
		}
		return stripeKey;
	}


	protected List<StripeCreditCardDto> getLinkedCreditCardFromGateway(Event event, User user, Stripe stripe, Optional<Payment> paymentOptional) {

		List<StripeCreditCardDto> stripeCreditCardDtos = new ArrayList<>();
		if (paymentOptional.isPresent() && stripe != null) {
			String customerid = paymentOptional.get().getStripeToken();
			String apiKey = stripe.getAccessToken();
			if (isNotBlank(customerid) && isNotBlank(apiKey)) {
				try {
					stripeCreditCardDtos = this.allPaymentService.getAllCardsForCustomer(customerid, 30, stripe, user, event);
				} catch (StripeException | com.squareup.square.exceptions.ApiException stripeException) {
					log.warn(STRIPE_EXCEPTION, stripeException);
					// throw new NotAcceptableException(stripeException);//NOSONAR
				}
			}
		}
		return stripeCreditCardDtos;
	}

	@Override
	public List<StripeCreditCardDto> getLinkedCreditCard(Event event, User user) {

		Stripe stripe = roStripeService.findByEvent(event);
		Optional<Payment> paymentOptional = this.paymentService.findByUserIdAndEventId(user.getUserId(),
				event.getEventId());

		List<StripeCreditCardDto> stripeCreditCardDtos = new ArrayList<>();
		if (paymentOptional.isPresent() && stripe != null) {
			stripeCreditCardDtos = this.allPaymentService.getAllCardsForCustomer(user, event, 30, stripe);
            stripeCreditCardDtos = new ArrayList(new HashSet(stripeCreditCardDtos));
            Optional<StripeCreditCardDto> defaultCard = stripeCreditCardDtos.stream().filter(StripeCreditCardDto::isDefaultCard).findFirst();
			if(stripeCreditCardDtos.isEmpty() || !defaultCard.isPresent()){//NOSONAR
				stripeCreditCardDtos = getLinkedCreditCardFromGateway(event, user, stripe, paymentOptional);
			}
		}
		return stripeCreditCardDtos;
	}

	@Override
	public List<EventLinkedCard> getLikedCard(User user) {
		List<Long> eventIds = this.paymentService.findDistinctEventIdByUserId(user.getUserId());
		List<EventLinkedCard> creditCardsByEvent = new ArrayList<>();
		if (eventIds != null) {
			for (long eventId : eventIds) {
				Event event = roEventService.getEventByIdIfPresentOrNull(eventId);
				if(event!=null){
					EventLinkedCard eventLinkedCard = new EventLinkedCard();
					eventLinkedCard.setEventUrl(event.getEventURL());
					eventLinkedCard.setStripeCards(getLinkedCreditCard(event, user));
					creditCardsByEvent.add(eventLinkedCard);
				}
			}
		}
		return creditCardsByEvent;
	}

	@Override
	public Set<EventEndInfoDto> getEventEndInfoForUser(User user, String source, String searchString, Date searchDate, Long whitelabelId) {
		Set<EventEndInfoDto> finalSet = new HashSet<>();
		// Find all events in which user performed some activity
		List<Object[]> events;
        events = getAllEventsBasedOnSource(user, source, searchString, whitelabelId);

        Set<Long> eventIds = new HashSet<>();
		events.forEach(obj -> eventIds.add(((Number) obj[0]).longValue()));

		Map<Long, Date> eventEndDateMap = getEventEndDateMap(eventIds);
		Map<Long, Date> eventStartDateMap = getEventStartDateMap(eventIds);
        Map<Long, String> stripePublishableKeyMap = getStripePublishableKeyMap(eventIds);
        Map<Long, String> squareLocationKeyMap = getSquareLocationKeyMap(eventIds);
        Map<Long,List<SponsorsImageDto>> sponsorsImageDtoKeyMap = getSponsorsImageDtoMap(eventIds,source);
        List<Long> addedEventIds = new ArrayList<>();

		List<Object[]> soldCount = ticketingStatisticsService.getTicketsSoldCount(eventIds);
		Map<Long, Long> soldCountMap = new HashMap<>();
		for(Object[] sold : soldCount){
			soldCountMap.put((Long)sold[0], (Long)sold[1]);
		}

		List<Object[]> numberOfTotalTickets = ticketingTypeTicketService.getNumberOfTotalTickets(eventIds);
		Map<Long, Long> numberOfTotalMap = new HashMap<>();
		for(Object[] totalTickets : numberOfTotalTickets){
			numberOfTotalMap.put((Long)totalTickets[0], (Long)totalTickets[1]);
		}


		for (Object[] obj : events) {
			Long eventId = ((Number) obj[0]).longValue();
			if (!addedEventIds.contains(eventId)) {
                EventEndInfoDto endInfoDto = getEventEndInfoDto(eventEndDateMap, eventStartDateMap, stripePublishableKeyMap, soldCountMap.containsKey(eventId) ? soldCountMap.get(eventId).intValue() : 0, numberOfTotalMap.containsKey(eventId) ? numberOfTotalMap.get(eventId).intValue() : 0, obj, eventId,source, squareLocationKeyMap,sponsorsImageDtoKeyMap);
                finalSet.add(endInfoDto);
				addedEventIds.add(eventId);
			}

		}

		return finalSet;
	}

    private List<Object[]> getAllEventsBasedOnSource(User user, String source, String searchString, Long whitelabelId) {
        if (MOBILE_APP.equals(source)) {
            if(NumberUtils.isNumberGreaterThanZero(whitelabelId)){
                return getAllEventsForUserInWhitelabel(user, StringUtils.isNotBlank(searchString) ?
                        StringUtils.lowerCase(searchString) : STRING_EMPTY, whitelabelId);
            }
            else{
                return getAllEventsForUser(user, StringUtils.isNotBlank(searchString) ?
                        StringUtils.lowerCase(searchString) : STRING_EMPTY);
            }
        }

        return getAllEventsForUser(user, STRING_EMPTY);
    }

    private EventEndInfoDto getEventEndInfoDto(Map<Long, Date> eventEndDateMap, Map<Long, Date> eventStartDateMap, Map<Long, String> stripePublishableKeyMap, int numberOfTicketSold, int numberOfTotalTickets1, Object[] obj, Long eventId, String source, Map<Long, String> squareLocationKeyMap,Map<Long,List<SponsorsImageDto>> sponsorsImageDtoKeyMap) {
        Date endDate = null;
        if (eventEndDateMap.containsKey(eventId)) {
            endDate = eventEndDateMap.get(eventId);
        }
        Date startDate = null;
        if(eventStartDateMap.containsKey(eventId)){
            startDate = eventStartDateMap.get(eventId);
        }

        if (source != null && source.equals(MOBILE_APP)){
            return new EventEndInfoDto((String) obj[1], (String) obj[2], endDate,
                    (String) obj[3], stripePublishableKeyMap.get(eventId), (String) obj[5], startDate,
                    numberOfTicketSold,
                    numberOfTotalTickets1, (String) obj[6], (String) obj[7], (Boolean) obj[8], (String) obj[9], (String) obj[10], (obj[11] == null || String.valueOf(obj[11]).isEmpty())?Boolean.FALSE: Boolean.valueOf(String.valueOf(obj[11])), String.valueOf(obj[12]), squareLocationKeyMap.get(eventId),sponsorsImageDtoKeyMap.get(eventId));
        }
        else{
            return new EventEndInfoDto((String) obj[1], (String) obj[2], endDate,
                    (String) obj[3], stripePublishableKeyMap.get(eventId), (String) obj[5], startDate,
                    numberOfTicketSold,
                    numberOfTotalTickets1, (String) obj[6], (String) obj[7], (Boolean) obj[8],(String) obj[12], squareLocationKeyMap.get(eventId));

        }

    }

    @Override
	public List<Object[]> getAllEventsForUser(User user, String searchString) {
		return eventRepository.findAllEventsForUser(user.getUserId(), searchString);
	}

    @Override
    public List<Object[]> getAllEventsForUserInWhitelabel(User user, String searchString, Long whitelabelId) {
        return eventRepository.getAllEventsForUserInWhitelabel(user.getUserId(), searchString, whitelabelId);
    }

	@Override
	public String getFavDirectoryName(Event event) {
		if (event != null) {
			return event.getWhiteLabel() == null ? null : event.getWhiteLabel().getFaviconDirectory();
		} else {
			return null;
		}
	}

	protected Map<Long, Date> getEventEndDateMap(Set<Long> eventIds) {
		if (eventIds.isEmpty()) {
			return Collections.emptyMap();
		}

		List<Object[]> endDates = eventRepository.findEventsEndDate(eventIds);

		return endDates.stream().collect(Collectors.toMap(e -> ((Number) e[0]).longValue(), e -> (Date) e[1]));
	}

	protected Map<Long, Date> getEventStartDateMap(Set<Long> eventIds) {
		if (eventIds.isEmpty()) {
			return Collections.emptyMap();
		}

		List<Object[]> startDates = eventRepository.findEventsStartDate(eventIds);

		return startDates.stream().collect(Collectors.toMap(e -> ((Number) e[0]).longValue(), e -> (Date) e[1]));
	}

    private Map<Long, String> getStripePublishableKeyMap(Set<Long> eventIds) {
        if (eventIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<StripeDTO> stripeDTOs = stripeService.getPublishableKeyByEventIds(eventIds);
        return stripeDTOs.stream().collect(Collectors.toMap(StripeDTO::getEventId, StripeDTO::getStripePublishableKey, (firstValue, secondValue) -> firstValue));
    }

    private Map<Long, String> getSquareLocationKeyMap(Set<Long> eventIds) {
        if (eventIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<StripeDTO> stripeDTOs = stripeService.getPublishableKeyByEventIds(eventIds);
        return stripeDTOs.stream().filter(m -> null != m.getSquareMerchantLocation()).collect(Collectors.toMap(StripeDTO::getEventId, StripeDTO::getSquareMerchantLocation, (firstValue, secondValue) -> firstValue));
    }

	@Override
	public void addUserCard(Event event, User user, String tokenOrPaymentMethodId, boolean isDefault,
							boolean absorbCardFoundException) {
		Stripe stripe = roStripeService.findByEvent(event);
		Optional<Payment> paymentOptional = this.paymentService.findByUserIdAndEventId(user.getUserId(),
				event.getEventId());

		if (paymentOptional.isPresent() && stripe != null) {
			String customerId = paymentOptional.get().getStripeToken();
			String apiKey = stripe.getAccessToken();
			if (isNotBlank(customerId) && isNotBlank(apiKey)) {
				try {
					String cardId = allPaymentService.addCardToCustomer(
							customerId,
							tokenOrPaymentMethodId,
							stripe,
							event,
							user);
					Payment payment = paymentOptional.get();
					payment.setCardIdOrPaymentMethodId(cardId);
					paymentService.save(payment);
				} catch (StripeException stripeException) {
                    log.info("EventServiceImpl | addUserCard | StripeException {} ",stripeException.getStripeError().getMessage());
                    throw new NotAcceptableException(stripeException.getStripeError().getCode(),stripeException.getStripeError().getMessage(),stripeException.getStripeError().getMessage());
                }catch (com.squareup.square.exceptions.ApiException apiException){
                    log.info("EventServiceImpl addUserCard SquareException {}",apiException);
                    log.info("EventServiceImpl addUserCard SquareException errors {}",apiException.getErrors());
                    log.info("EventServiceImpl addUserCard SquareException ResponseCode {}", apiException.getResponseCode());
                    log.info("EventServiceImpl addUserCard SquareException {} ",apiException.getMessage());
                    throw new NotAcceptableException("4066001", getErrorMessage(apiException), getErrorMessage(apiException));
                }
			} else {
				throw new NotFoundException(NotFound.STRIPE_CUSTOMER);
			}
		} else {
			throw new NotFoundException(NotFound.PAYMENT_DETAIL);
		}
	}

	@Override
	public void setDefaultCard(Event event, User user, String cardId) {
		Stripe stripe = roStripeService.findByEvent(event);
		Optional<Payment> paymentOptional = this.paymentService.findByUserIdAndEventId(user.getUserId(),
				event.getEventId());

		if (paymentOptional.isPresent() && stripe != null) {
			String customerId = paymentOptional.get().getStripeToken();
			String apiKey = stripe.getAccessToken();
			if (isNotBlank(customerId) && isNotBlank(apiKey)) {
				try {
					allPaymentService.updateDefaultCard(apiKey, customerId, cardId, stripe, event, user);
				} catch (StripeException stripeException) {
                    log.info("EventServiceImpl | setDefaultCard | StripeException {} ",stripeException.getStripeError().getMessage());
                    throw new NotAcceptableException(stripeException.getStripeError().getCode(),stripeException.getStripeError().getMessage(),stripeException.getStripeError().getMessage());
                }catch (com.squareup.square.exceptions.ApiException apiException){
                    log.info("EventServiceImpl setDefaultCard SquareException {}",apiException);
                    log.info("EventServiceImpl setDefaultCard SquareException errors {}",apiException.getErrors());
                    log.info("EventServiceImpl setDefaultCard SquareException ResponseCode {}", apiException.getResponseCode());
                    log.info("EventServiceImpl setDefaultCard SquareException {} ",apiException.getMessage());
                    throw new NotAcceptableException("4066001", getErrorMessage(apiException), getErrorMessage(apiException));
                }
			} else {
				throw new NotFoundException(NotFound.STRIPE_CUSTOMER);
			}
		} else {
			throw new NotFoundException(NotFound.PAYMENT_DETAIL);
		}
	}

	@Override
	@Transactional(rollbackFor = {StripeException.class,
			NotAcceptableException.class, com.squareup.square.exceptions.ApiException.class}, isolation = Isolation.READ_COMMITTED)
	public String auctionCharges(User user, Event event, AuctionPurchaseDto auctionPurchaseDto,
								 boolean isStaffBid, String paymentType, User staffUser,
								 boolean isLiveItemCheckout,Map<String, String> languageMap) {

		log.info("user for bid {}" ,user);
		log.info("staff user for bid {}" , staffUser);
		log.info("auction purchase dto {}" , auctionPurchaseDto);

		try {
            String languageCode = roEventService.getLanguageCodeByUserOrEvent(staffUser, event);
            ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode);
			Item item = itemService.getItemByModuleType(event, auctionPurchaseDto.getItemCode(),
					ModuleType.AUCTION).orElseThrow(() -> new NotFoundException(ItemNotFound.ITEM_NOT_FOUND));
			log.info("item for bid {}",item);

			User userForOutBidNotification = auctionBidService.getLastBidderLesserThanBuyItNow(item);

			Auction auction = this.auctionService.findByEvent(event);
			validateItemAuctionPurchase(user, event, paymentType, item);

			Date currentDate = new Date();
			double bidIncrement = itemService.getBidIncrement(item, auction);
            log.info("Bid increment {} for item {} ",bidIncrement,item.getId());
			double bidAmount = auctionPurchaseDto.getAmount();
			if (item.getBuyItNowPrice() > 0 && (bidAmount >= item.getBuyItNowPrice())) {
				bidIncrement = 0;
			}

			this.bidCalculator(item, bidAmount, bidIncrement, event,languageMap,languageCode);

			itemService.handleItemCurrentBidAndHighestBidder(item, bidAmount, user);
			itemService.update(event,item);

			boolean normalMsg = true;
			Optional<AuctionBid> optionalAuctionBidHighest = auctionBidService.findHighestBidItemForAuction(item, auction.getId());
			if (auctionService.isApplicableForExtend(optionalAuctionBidHighest, auction, auctionPurchaseDto.getItemCode(), currentDate,languageMap,languageCode)) {
				auction.setAuctionStatus(ModuleStatus.EXTENDED_BIDDING);
				auction.setLastExtendedEndTime(auctionService.addExtendedBiddingTime(currentDate, auction));
				auctionService.save(auction);
				normalMsg = false;
			}

			Payment payment = null;
            paymentType = isNotBlank(paymentType) ? paymentType : CC;

			payment = handlePayment(user, event, auctionPurchaseDto, paymentType, staffUser, item, currentDate, bidAmount, null);

			// For live auction item set the current bid as buy it now price.
		/*if(item.isLiveAuctionItem() && staffUser!=null && isLiveItemCheckout) {//NOSONAR
			item.setBuyItNowPrice(auctionPurchaseDto.getAmount());
		}*/

			AuctionBid auctionBid;
		/*if(item.isLiveAuctionItem()){//NOSONAR
			Optional<AuctionBid> optionalAuctionBid = auctionBidService.findAuctionBidByLiveItem(item);
			auctionBid = optionalAuctionBid.orElse(null);
			if(!isLiveItemCheckout && auctionBid != null) {
				auctionBid.setUser(user);
				auctionBid.setAmount(bidAmount);
				auctionBid.setBidTime(new Date());
				auctionBid.setAmount(bidAmount);
				auctionBidService.save(auctionBid);
			}
			else if(isLiveItemCheckout && auctionBid != null && auctionBid.getUser() != user){
				throw new NotAcceptableException(AuctionExceptionMsg.USER_CONFLICT);
			}
		}*/

			itemService.update(event,item);
			String userMessageText = resourceBundle.getString(languageMap.get(Constants.BID_SUCCESS_NOTIFY));
			String smsText = textMessageUtils.getSuccessfulAuctionBidMessage(event, item, bidAmount);
			Stripe stripe = roStripeService.findByEvent(event);
			boolean isOutBidBuyitNowMessage = false;
            auctionBid = auctionBidService.save(event,user, item.getModuleId(), item, bidAmount,
                    isStaffBid ? BiddingSource.STAFF : BiddingSource.ONLINE,
                    true, false, staffUser);

            // TODO: the sms messages should use EnumAuctionSMS
            Winner winner = null;
			if ((item.getBuyItNowPrice() != 0 && bidAmount >= item.getBuyItNowPrice()) || (item.isLiveAuctionItem() && isLiveItemCheckout)) {
				// IF CC Required To Bid Confirm then Token will be available.
				if (event.isCreditCardEnabled()) {
					auctionBid.setHasPaid(true);
					userMessageText = resourceBundle.getString(languageMap.get(Constants.THANK_YOU_FOR_YOUR_PURCHASE));
					smsText = resourceBundle.getString(languageMap.get(Constants.THANK_YOU_FOR_PURCHASING)) + item.getCode();

					// FIXME need to check when cc disable
					joinPaymentItemService.save(new JoinPaymentItem(payment, item, paymentType.equals(CC)
							? stripe.getPaymentGateway() : MANUAL_TRANSACTION_TYPE));
				} else {
					userMessageText = resourceBundle.getString(languageMap.get(Constants.BUY_IT_NOW_BID_CONTACTED_TO_COLLECT_PAYMENT));
					smsText = userMessageText;
				}
				auctionBidService.save(auctionBid,event.getEventURL());
                winner = new Winner(event.getEventId(), user.getUserId(), item.getId(), auctionBid.getId(),
						ModuleType.AUCTION, event.isCreditCardEnabled(), currentDate,
						event.isCreditCardEnabled() ? currentDate : null);
                winner = winnerService.save(winner);
				log.info("winner-created =>{}",winner);
				List<Winner> declaredWinner = winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId());
				if (declaredWinner.size() >= item.getNumberOfWinners()) {
					isOutBidBuyitNowMessage = true;
				}
				log.info("auctionCharges==>{}" , winner);
				double paidAmout = bidAmount;
				if (event.isCreditCardEnabled() && paymentType.equals(CC)) {
					paidAmout = paymentHandlerService.getPaidAmountForAuctionAfterStripeTransaction(auctionPurchaseDto.getTokenOrIntentId(), user, event, item, currentDate, bidAmount, payment, STRING_EMPTY, auctionBid);

					auctionBid.setStripePaidAmount(paidAmout);
//					auctionBid.setStripeFee(CalculateFees.getStripeFee(paidAmout, auctionBid.getAmount(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), 1));
					auctionBidService.save(auctionBid,event.getEventURL());
				}
				log.info("send buyer receipt start");
				sendBuyerReciept(user, event, item, auction, isOutBidBuyitNowMessage, paidAmout, stripe);
				log.info("send buyer end");
			}

			if (normalMsg) {
				twilioTextMessagePrepareService.sendOutBidNotification(event, item, userForOutBidNotification, bidAmount,
						isOutBidBuyitNowMessage);
			} else {
				twilioTextMessagePrepareService.sendOutBidNotificationForExtendedWaitTime(event, item, userForOutBidNotification, bidAmount,
						isOutBidBuyitNowMessage, true, auction.getExtendedBiddingWaitTime());
			}
            itemOutbidGraphQLHandler.handleCreateOrUpdateItemOutBidStatusLogs(item, event, winner);
			if (null != staffUser && user.getPhoneNumber() != 0) {
                sendTextMessage(user, event, smsText);
            }
			return userMessageText;
		}catch (StripeException stripeException) {
		    log.info("EventServiceImpl | auctionCharges | StripeException {}",stripeException.getStripeError().getMessage());
			throw new NotAcceptableException(stripeException.getStripeError().getCode(),stripeException.getStripeError().getMessage(),stripeException.getStripeError().getMessage());
		}catch (com.squareup.square.exceptions.ApiException apiException){//NOSONAR
            log.info("EventServiceImpl auctionCharges  SquareException {}",apiException);
            log.info("EventServiceImpl auctionCharges  SquareException errors {}",apiException.getErrors());
            log.info("EventServiceImpl auctionCharges  SquareException ResponseCode {}", apiException.getResponseCode());
			throw new NotAcceptableException("4066001", getErrorMessage(apiException), getErrorMessage(apiException));
		}
	}

    private void sendTextMessage(User user, Event event, String smsText) {
        try {
            TextMessage text = new TextMessage(smsText, event.getAePhoneNumber(), user, smsText);
            textMessageService.sendText(text);
        } catch (Exception e) {
log.error("{}{}",EXCEPTION_OCCURED , e);
        }
    }

    public String getErrorMessage(com.squareup.square.exceptions.ApiException e) {
        String errorMsg;
        if (null != e && StringUtils.isNotBlank(e.getMessage())) {
            if (!CollectionUtils.isEmpty(e.getErrors())) {
                Error error = e.getErrors().get(0);
                errorMsg = error.getDetail();
            } else {
                errorMsg = e.getMessage();
            }
        } else {
            errorMsg = "Square API Exception, no error message found in Exception";
        }

        return errorMsg;
    }

    protected Payment handlePayment(User user, Event event, AuctionPurchaseDto auctionPurchaseDto, String paymentType, User staffUser, Item item, Date currentDate, double bidAmount, Payment payment) throws StripeException, com.squareup.square.exceptions.ApiException {//NOSONAR
        if (event.isCreditCardEnabled()
                && CC.equals(paymentType)
                && isItemIsLiveOrBuyItNowPrice(item, bidAmount)) {
            log.info("handlePayment1Start");
            Stripe stripe = roStripeService.findByEvent(event);
            payment = paymentService.createOrGetPayment(auctionPurchaseDto.getTokenOrIntentId(), user, event, null, stripe,
                    staffUser, false);
            log.info("handlePayment1End");
        } else if (CASH.equals(paymentType)) {
            Optional<Payment> paymentOptional = paymentService.findByUserIdAndEventId(user.getUserId(),
                    event.getEventId());
            payment = paymentOptional.orElseGet(() -> new Payment(user.getUserId(), event.getEventId(), STRING_EMPTY, currentDate));
            log.info("payment for bid {}", payment);
            Staff staff = roStaffService.findByEventAndUserNotExhibitor(event, staffUser, false);
            if (staff != null) {
                payment.setStaffUserId(staff.getUser().getUserId());
            }
            paymentService.save(payment);
        } else if (event.isCcRequiredForBidConfirm() && !StringUtils.isEmpty(auctionPurchaseDto.getTokenOrIntentId())) {
            log.info("handlePayment3Start");
            payment = paymentService.createOrGetPaymentFromPaymentMethod(auctionPurchaseDto.getTokenOrIntentId(),
                    user, event, null);
            log.info("handlePayment3End");
        }
        return payment;
    }

	protected void validateItemAuctionPurchase(User user, Event event, String paymentType, Item item) {//NOSONAR
		List<Winner> winners = winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId());
		if ((item.getNumberOfWinners() - winners.size()) <= 0) {
			log.info(AuctionExceptionMsg.ITEM_ALREADY_PURCHASED.getErrorMessage());
			throw new NotAcceptableException(AuctionExceptionMsg.ITEM_ALREADY_PURCHASED);
		}
		if (!item.isLiveAuctionItem()) {
			this.checkModuleActivateAndNotExpired(user, item.getModuleType(), event, DateUtils.getCurrentDate());
		}
		if (!(CC.equals(paymentType) || CASH.equals(paymentType))) {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_VALID_PAYMENT);
		}
	}

	private boolean isItemIsLiveOrBuyItNowPrice(Item item, double bidAmount) {
		return item.isLiveAuctionItem() ||
				(item.getBuyItNowPrice() != 0 && bidAmount >= item.getBuyItNowPrice());
	}

	@Override
	public void sendBuyerReciept(User user, Event event, Item item, Auction auction, boolean isOutBidBuyitNowMessage, double paidAmout, Stripe stripe) {
		try {
			// send mail V2 Buyer Receipt for buy it now - Auction
			log.info("send mail V2 Buyer Receipt buy it now - Auction start");
			List<Item> items = new ArrayList<>();
			items.add(item);
			sendGridMailPrepareService.sendBuyerReceiptAuction(user, event, items,
					auction != null && auction.getEnableMarketValue() != null ? auction.getEnableMarketValue()
							: Boolean.FALSE,
					paidAmout, isOutBidBuyitNowMessage,stripe);
			log.info("send mail V2 Buyer Receipt buy it now- Auction end");
		} catch (Exception e) {
            log.error("{}{}",EXCEPTION_OCCURED , e);
		}
	}

    protected Payment getPaymentForCheckOut(Event event, String paymentType, String tokenOrIntentId, User user, Stripe stripe,
                                            User staffUser) throws StripeException, com.squareup.square.exceptions.ApiException {
        Payment payment = null;
        if (paymentType.equals(CC)) {
            payment = paymentService.createOrGetPayment(tokenOrIntentId, user, event, null, stripe, staffUser, false);
        } else if (paymentType.equals(CASH)) {
            Optional<Payment> paymentOptional = paymentService.findByUserIdAndEventId(user.getUserId(),
                    event.getEventId());
            payment = paymentOptional.orElseGet(() -> new Payment(user.getUserId(), event.getEventId(), STRING_EMPTY, new Date()));
            Staff staff = roStaffService.findByEventAndUserNotExhibitor(event, user, false);
            if (staff != null) {
                payment.setStaffUserId(staff.getUser().getUserId());
            }
            paymentService.save(payment);
        }
        return payment;
    }

	private void sendMaximumNumberPledge(String itemCode, Event event, User user) {
		try {
			String maxPledgeReached = textMessageUtils.getMaxPledgeReachedMessage(itemCode);
			TextMessage text = new TextMessage(maxPledgeReached, event.getAePhoneNumber(), user, maxPledgeReached);
			textMessageService.sendText(text);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	@Override
	@Transactional(rollbackFor = {StripeException.class,
			NotAcceptableException.class, com.squareup.square.exceptions.ApiException.class}, isolation = Isolation.READ_COMMITTED)
	public String submitPledge(Event event, User user, FundANeedPurchaseDto fundANeedPurchaseDto, boolean isStaff,
							   String paymentType, User staffUser) throws IOException, StripeException, com.squareup.square.exceptions.ApiException {

		Optional<Item> optItem = itemService.getItemByModuleType(event, fundANeedPurchaseDto.getItemCode(),
				ModuleType.CAUSEAUCTION);
		if (!optItem.isPresent()) {
			throw new NotFoundException(ItemNotFound.ITEM_NOT_FOUND);
		}

		Item item = optItem.get();
		Integer submittedPledgeCount = this.pledgeService.getPledgeCountForItem(item);
		Integer configuredPlegeCount = item.getNumberOfWinners();
		if (configuredPlegeCount > 0 && submittedPledgeCount >= configuredPlegeCount) {

			this.sendMaximumNumberPledge(item.getCode(), event, user);
			throw new NotAcceptableException(NotAcceptableException.ItemExceptionMsg.MORE_PLEDGES_SUBMITTED);
		}

		this.userService.saveName(fundANeedPurchaseDto.getFirstName(), fundANeedPurchaseDto.getLastName(), user);
		this.userService.saveAddress(user, fundANeedPurchaseDto);

		Date currentDate = new Date();

		this.checkModuleActivateAndNotExpired(user, item.getModuleType(), event, currentDate);

        paymentType = getPaymentValidated(fundANeedPurchaseDto, paymentType, item);

        Stripe stripe = roStripeService.findByEvent(event);
        String apiKey = STRING_EMPTY;//NOSONAR
        if (stripe != null) {
            apiKey = stripe.getAccessToken();//NOSONAR
        }
        //NOSONAR

        String message;

		log.info("Submitted Pledge : {}| Item :{}" , fundANeedPurchaseDto.getAmount(),item.getId());

        Pledge pledge = getPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, staffUser, item, currentDate);
        double paidAmout = fundANeedPurchaseDto.getAmount();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(user,event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
		if (paymentType.equals(CC) && event.isCreditCardEnabled()) {
			Payment payment = getPledgePayment(event, user, fundANeedPurchaseDto, paymentType, staffUser, item, stripe);//NOSONAR
			paidAmout = paymentHandlerService.getPaidAmountForPledgeAfterStripeTransaction(event, user, isStaff, currentDate, stripe, pledge, payment,fundANeedPurchaseDto.getTokenOrIntentId());
			pledge.setStripePaidAmount(paidAmout);
            if (stripe != null) {
                pledge.setStripeFee(CalculateFees.getStripeFee(paidAmout, pledge.getAmount(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), 1));
            }
            pledgeService.save(pledge);
            Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
            message = resourceBundle.getString(languageMap.get(Constants.PLEDGE_SUBMIT_MSG));
		} else if (staffUser != null && paymentType.equals(CASH)) {
            Payment payment = getPledgePayment(event, user, fundANeedPurchaseDto, paymentType, staffUser, item, stripe);// NOSONAR
            Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
            message = resourceBundle.getString(languageMap.get(Constants.STAFF_CASH_PLEDGE));
		} else {
		    String itemCode = item.getCode();
            Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
            String pledgeStaffItemSubmittedAlert = resourceBundle.getString(languageMap.get(Constants.PLEDGE_STAFF_ITEM_SUBMITTED_ALERT));
			message = pledgeStaffItemSubmittedAlert.replace(ITEM_CODE_STR,itemCode);
		}

		try {
				if (user.getPhoneNumber() != 0 && isStaff) {
					String winnerMsg = textMessageUtils.getPledgeStaffSuccessMessage(event, item, pledge.getAmount(),user,languageMap);
					TextMessage text = new TextMessage(winnerMsg, event.getAePhoneNumber(), user, winnerMsg);
					textMessageService.sendText(text);
				}

			// send mail V2 Buyer Receipt - Pledge
			if (!isStaff) {
				Map<Pledge, Double> plagedata = new HashMap<>();
				plagedata.put(pledge, pledge.getAmount());
				log.info("pledgeCheckoutPayment causeAuctionService.sendBuyerReceiptCauseAuction stat");
				sendGridMailPrepareService.sendBuyerReceiptCauseAuction(user, event, plagedata, paidAmout);
				log.info("pledgeCheckoutPayment causeAuctionService.sendBuyerReceiptCauseAuction end");
			}
		} catch (Exception e) {
            log.error("{}{}",EXCEPTION_OCCURED , e);
		}

		return message;
	}

    private String getPaymentValidated(FundANeedPurchaseDto fundANeedPurchaseDto, String paymentType, Item item) {
        if (isBlank(paymentType)) {
            paymentType = CC;
        }

        if (!(paymentType.equals(CC) || paymentType.equals(CASH))) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.NOT_VALID_PAYMENT);
        }

        if (fundANeedPurchaseDto.getAmount() < item.getStartingBid()) {
            throw new NotAcceptableException(FundANeedExceptionMsg.PLEDGE_SHOULD_GREATER_EQUAL);
        }
        return paymentType;
    }

    private Pledge getPledge(Event event, User user, FundANeedPurchaseDto fundANeedPurchaseDto, boolean isStaff, String paymentType, User staffUser, Item item, Date currentDate) {//NOSONAR
        Pledge pledge = new Pledge();
        pledge.setAmount(fundANeedPurchaseDto.getAmount());
        pledge.setPledgeSource(isStaff ? BiddingSource.STAFF : BiddingSource.ONLINE);
        pledge.setCauseAuctionId(event.getCauseAuctionId());
        pledge.setPledgeTime(currentDate);

        boolean isPaidAndConfirmed;
        if (isStaff) {
            isPaidAndConfirmed = CASH.equals(paymentType) || event.isCreditCardEnabled();
        } else {
            isPaidAndConfirmed = event.isCreditCardEnabled();
        }

        pledge.setHasPaid(isPaidAndConfirmed);
        pledge.setConfirmed(isPaidAndConfirmed);
        pledge.setItem(item);
        pledge.setUser(user);
        pledge.setNote(fundANeedPurchaseDto.getNote());
        pledge.setStaffUserId(staffUser);

        pledgeService.save(pledge);
        return pledge;
    }

    private Payment getPledgePayment(Event event, User user, FundANeedPurchaseDto fundANeedPurchaseDto, String paymentType, User staffUser, Item item,
                                     Stripe stripe) throws StripeException, com.squareup.square.exceptions.ApiException {
        Payment payment = this.getPaymentForCheckOut(event, paymentType, fundANeedPurchaseDto.getTokenOrIntentId(),
                user, stripe, staffUser);

		joinPaymentItemService.save(new JoinPaymentItem(payment, item, paymentType.equals(CC)
				? stripe.getPaymentGateway(): MANUAL_TRANSACTION_TYPE));
		return payment;
	}

	// TODO need to add check for staff only can pay in cash otherwise throw

	// error

    @Override
    @Transactional(rollbackFor = {StripeException.class,
            NotAcceptableException.class, com.squareup.square.exceptions.ApiException.class}, isolation = Isolation.READ_COMMITTED)
    public void bidCheckout(User user, Event event, BidCheckOutDto bidCheckOutDto, User staffUser)
            throws StripeException, com.squareup.square.exceptions.ApiException {

		Stripe stripe = roStripeService.findByEvent(event);
		List<Item> items = new ArrayList<>();

        Payment payment = paymentService.createOrGetPayment(bidCheckOutDto.getTokenOrIntentId(), user, event, null, stripe,
                staffUser, false);

		log.info("Payment created for {}", bidCheckOutDto.getTokenOrIntentId());
		Map<String, Object> metadata = new HashMap<>();
		metadata.put(STRING_EMAIL, user.getEmail());
		metadata.put(STRING_CELL, user.getPhoneNumber());
		// Update the Auction Bid Col Has_Paid
		double amount = 0;
		long auctionBidId = 0;
        boolean isOutBidBuyitNowMessage = false;
		for (Long itemId : bidCheckOutDto.getItemIds()) {
			Item item = itemService.getItem(itemId).get();//NOSONAR
			items.add(item);

			AuctionBid auctionBid = auctionBidService.findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(event.getAuctionId(), user, item, false);
			if (null == auctionBid){
                throw new NotFoundException(NotFound.BID_NOT_FOUND);
            }

			if(bidCheckOutDto.getItemIds().size() == 1){
				auctionBidId = auctionBid.getId();
			}
			amount += auctionBid.getAmount();
			auctionBid.setHasPaid(true);
			auctionBid.setConfirmed(true);
			auctionBid.setStripePaidAmount(getStripePaidAmount(stripe, auctionBid.getAmount(), bidCheckOutDto.getItemIds()));
			//auctionBid.setStripeFee(CalculateFees.getStripeFee(auctionBid.getStripePaidAmount(),auctionBid.getAmount(),stripe.getCCPercentageFee(),stripe.getCCFlatFee(),bidCheckOutDto.getItemIds().size()));
			auctionBidService.save(auctionBid,event.getEventURL());
			log.info("bidCheckout - auctionBidService-Paid True {}",auctionBid.getId());

			joinPaymentItemService.save(new JoinPaymentItem(payment, item, stripe.getPaymentGateway()));
			log.info("bidCheckout - created join payment {}",auctionBid.getId());
			Winner winner = winnerService.findWinnersByModuleAndItemAndBid(ModuleType.AUCTION, item.getId(),
					auctionBid.getId());
			if (winner == null) {
				List<Winner> declaredWinner = winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, itemId);
				if (declaredWinner.size() >= item.getNumberOfWinners()) {
					throw new NotAcceptableException(ItemExceptionMsg.ITEM_ALREADY_PURCHASED);
				}
				log.info("bidCheckout - New Winner created with had paid true {}",auctionBid.getId());
				winner = new Winner(event.getEventId(), auctionBid.getUser().getUserId(), itemId, auctionBid.getId(),
						ModuleType.AUCTION, true, new Date(), new Date());
			} else {
				winner.setHasPaid(true);
				winner.setPaidTime(new Date());
				log.info("bidCheckout - winner had set paid true {}",auctionBid.getId());
			}
			winnerService.save(winner);
            List<Winner> declaredWinner = winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId());
            if (declaredWinner.size() >= item.getNumberOfWinners()) {
                isOutBidBuyitNowMessage = true;
            }
        }
        log.info("bidCheckout - getPaidAmountBidCheckout start {}", user.getUserId());
        double paidAmout = paymentHandlerService.getPaidAmountBidCheckout(user, event, bidCheckOutDto, payment, metadata, amount, auctionBidId, bidCheckOutDto.getTokenOrIntentId());
        log.info("bidCheckout - getPaidAmountBidCheckout done {}", user.getUserId());
        try {
            Auction auction = auctionService.findByEvent(event);
            // send mail V2 Buyer Receipt - Auction
            log.info("send mail V2 Buyer Receipt - Auction start");
            sendGridMailPrepareService.sendBuyerReceiptAuction(user, event, items,
                    auction != null && auction.getEnableMarketValue() != null ? auction.getEnableMarketValue() : Boolean.FALSE,
                    paidAmout, isOutBidBuyitNowMessage, stripe);
            log.info("send mail V2 Buyer Receipt - Auction end");
        } catch (Exception e) {
            log.error(EXCEPTION, e);
        }

        AccelEventsPhoneNumber aePhone = new AccelEventsPhoneNumber(event);
        try {
            // send mail V2 Buyer Receipt - Auction
            log.info("send sms V2 Buyer Receipt - Auction start");
			TextMessage textMessage = new TextMessage(THANKS_FOR_PURCHASE_MSG, aePhone, user,
					THANKS_FOR_PURCHASE_MSG);
			textMessageService.sendText(textMessage);

			log.info("send sms V2 Buyer Receipt - Auction end");
		} catch (ApiException e){
            log.warn("bidCheckout send text message issue for event {} and number {}", event.getEventId(), aePhone);
        }
		catch (Exception e) {
            log.error("{}{}",EXCEPTION_OCCURED , e);
		}
	}

    @Override
    @Transactional(rollbackFor = {StripeException.class,
            NotAcceptableException.class, com.squareup.square.exceptions.ApiException.class}, isolation = Isolation.READ_COMMITTED)
    public void pledgeCheckoutPayment(Event event, User user, PledgeCheckoutDto pledgeCheckoutDto)
            throws StripeException, com.squareup.square.exceptions.ApiException {

        Stripe stripe = roStripeService.findByEvent(event);
        Payment payment = paymentService.createOrGetPayment(pledgeCheckoutDto.getTokenOrIntentId(), user, event, null,
                stripe, null, false);

		Map<Pledge, Double> pledgeData = new HashMap<>();
        log.info("pledgeCheckoutPayment|user {}|event {}",user.getUserId(),event.getEventId());
		// Update the Pledge Bid Col Has_Paid
		double amount = 0;
		for (Long id : pledgeCheckoutDto.getPldegeIds()) {
			Pledge pledge = pledgeService.findById(id).get();//NOSONAR
			amount += pledge.getAmount();
			pledgeData.put(pledge, pledge.getAmount());
			pledge.setHasPaid(true);
			pledge.setConfirmed(true);
			pledge.setStripePaidAmount(getStripePaidAmount(stripe, pledge.getAmount(), pledgeCheckoutDto.getPldegeIds()));
			pledge.setStripeFee(CalculateFees.getStripeFee(pledge.getStripePaidAmount(), pledge.getAmount(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), pledgeCheckoutDto.getPldegeIds().size()));
			pledgeService.save(pledge);

            joinPaymentItemService.save(new JoinPaymentItem(payment, pledge.getItem(), stripe.getPaymentGateway()));
        }
        log.info("pledgeCheckoutPayment| amount {}|event {} | user {}", amount, event.getEventId(), user.getUserId());
        double paidAmout = paymentHandlerService.getPaidAmountPledgeCheckout(event, user, payment, amount, pledgeCheckoutDto.getTokenOrIntentId());
        try {
            // send mail V2 Buyer Receipt - Pledge
            log.info("pledgeCheckoutPayment causeAuctionService.sendBuyerReceiptCauseAuction stat");
            sendGridMailPrepareService.sendBuyerReceiptCauseAuction(user, event, pledgeData, paidAmout);
            log.info("pledgeCheckoutPayment causeAuctionService.sendBuyerReceiptCauseAuction end");
        } catch (Exception e) {
            log.error("Error in sending email for pledge checkout :", e);
        }
    }

	protected double getStripePaidAmount(Stripe stripe, Double amount, List<Long> pldegeIds) {
		return stripe.isProcessingFeesToPurchaser() ?
				StripeUtil.getAmountToCharge(amount,
						stripe.getCCPercentageFee(),
						(stripe.getCCFlatFee()
								/ pldegeIds.size()))
				: amount;
	}

    @Override
    @Transactional(rollbackFor = {StripeException.class,
            NotAcceptableException.class, com.squareup.square.exceptions.ApiException.class}, isolation = Isolation.READ_COMMITTED)
    public String buyRaffleTickets(Event event, User user, RaffleCheckoutDto raffleCheckoutDto)
            throws StripeException, com.squareup.square.exceptions.ApiException {

		Raffle raffle = this.raffleService.findByEvent(event);
		log.info("buyRaffleTickets|event {}|user {}|raffle ticket {}|",event.getEventId(),user.getUserId(),raffle.getId());
		RaffleTicket raffleTicket = raffleTicketService
				.findByIdAndRaffleId(raffleCheckoutDto.getRaffleTicketId(), raffle.getId()).orElse(null);
		if (raffleTicket != null) {
            log.info("raffle Ticket {} | user {} | event {}",raffleTicket.getId(),user.getUserId(),event.getEventId());
            validateForLimitedTickets(raffleCheckoutDto, raffle);

            // Update Purchase DB
			PurchasedRaffleTicket purchasedRaffleTicket = new PurchasedRaffleTicket();
			purchasedRaffleTicket.setRaffleId(event.getRaffleId());
			purchasedRaffleTicket.setTicketsPurchased(raffleTicket.getNumOfTicket());
			purchasedRaffleTicket.setUser(user);
			purchasedRaffleTicket.setPrice(raffleTicket.getPrice());
			purchasedRaffleTicket.setTransactionType(TransactionType.STRIPE);
			purchasedRaffleTicketService.save(purchasedRaffleTicket);

            Stripe stripe = roStripeService.findByEvent(event);
            Payment payment = paymentService.createOrGetPayment(raffleCheckoutDto.getTokenOrIntentId(), user, event, null,
                    stripe, null, false);

            double paidAmount = paymentHandlerService.getPaidAmountForRaffleTicketsAfterStripeTransaction(event, user, raffleTicket, purchasedRaffleTicket, payment, raffleCheckoutDto.getTokenOrIntentId());
            purchasedRaffleTicket.setStripePaidAmount(paidAmount);
            purchasedRaffleTicket.setStripeFee(CalculateFees.getStripeFee(paidAmount, raffleTicket.getPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), 1));
            purchasedRaffleTicketService.save(purchasedRaffleTicket);

			try {
				// send V2 Buyer Receipt - Raffle
				if (raffle.isAutoSubmitOnPurchase()) {
					// send V2 Buyer Receipt - Raffle
					log.info(Constants.SEND_BUYER_RECEIPT_RAFFLE_START);
					sendGridMailPrepareService.sendBuyerReceiptRaffleTicketPurchase(user, event, raffleTicket,
							paidAmount, Constants.CC);
					log.info(Constants.SEND_BUYER_RECEIPT_RAFFLE_END);
				} else {
					// send V2 Buyer Receipt - Raffle
					log.info(Constants.SEND_BUYER_RECEIPT_RAFFLE_START);
					sendGridMailPrepareService.sendBuyerReceiptRaffle(user, event, raffleTicket, paidAmount, Constants.CC);
					log.info(Constants.SEND_BUYER_RECEIPT_RAFFLE_END);
				}
			} catch (Exception e) {
				log.error("Exception e :", e);
			}
			try {
				String body;
				if (raffle.isAutoSubmitOnPurchase()) {
					body = textMessageUtils.getRaffleTicketPurchase(raffleTicket.getNumOfTicket(), event, user,languageMap);
				} else {
					body = textMessageUtils.getRaffleOnlinePurchaseMessage(raffleTicket.getNumOfTicket(), event,user,languageMap);
				}
				TextMessage textMessage = new TextMessage(body, event.getAePhoneNumber(), user, body);
				textMessageService.sendText(textMessage);
			} catch (Exception e) {
				log.error("Exception e :", e);
			}
			if (raffle.isAutoSubmitOnPurchase()) {
				return "Thank you for purchasing tickets!";
			} else {
				return String.format(THANKS_FOR_RAFFLE_MSG, raffleTicket.getNumOfTicket());
			}
		} else {
			throw new NotFoundException(ModuleNotFound.RAFFLE_TICKET_PKG_NOT_FOUND);
		}
	}

    private void validateForLimitedTickets(RaffleCheckoutDto raffleCheckoutDto, Raffle raffle) {
        if (raffle.getAvailableTickets() > 0) {
            long availableTickets = raffle.getAvailableTickets();
            long totalTicketsSale = this.purchasedRaffleTicketService.getTotalTicketsByRaffle(raffle.getId());
            long remaningTickets = availableTickets - totalTicketsSale;
            if (remaningTickets <= 0) {
                throw new NotAcceptableException(RaffleExceptionMsg.RAFFLE_TICKETS_ARE_SOLD_OUT);
            }
            if (remaningTickets < raffleService.getRaffleNumberOfTickets(raffleCheckoutDto.getRaffleTicketId(),
                    raffle.getId())) {
                RaffleExceptionMsg exceptionMessage = RaffleExceptionMsg.LIMITTED_TICKET;
                exceptionMessage.setErrorMessage(
                        String.format(exceptionMessage.getErrorMessage(), remaningTickets, remaningTickets));
                exceptionMessage.setDeveloperMessage(
                        String.format(exceptionMessage.getDeveloperMessage(), remaningTickets, remaningTickets));
                throw new NotAcceptableException(exceptionMessage);
            }
        }
    }

	@Override
	public HostGeneralSettingsGet getHostGeneralSettings(Event event) throws StripeException {
		EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
		EnumEventVenue eventVenue = ticketingService.getEventVenue(ticketing);
		HostGeneralSettingsGet settings = new HostGeneralSettingsGet();
		settings.setEventVenueType(null != eventVenue ?eventVenue.name(): STRING_EMPTY);
		settings.setCurrency(event.getCurrency());
		settings.setCurrencySymbol(event.getCurrency().getSymbol());
		settings.setFundRaisingGoal(event.getFundRaisingGoal());
		settings.setGoalStartingAmount(event.getGoalStartingAmount());
		settings.setSilentAuctionEnabled(event.isSilentAuctionEnabled());
		settings.setRaffleEnabled(event.isRaffleEnabled());
		settings.setCauseAuctionEnabled(event.isCauseAuctionEnabled());
		settings.setDonationEnabled(event.isDonationEnabled());
		settings.setTicketingEnabled(event.isTicketingEnabled());
		settings.setAnalyticsId(event.getAnalyticsId());
		settings.setTextToGiveEnabled(event.isTextToGiveEnabled());
		settings.setTotalFundRaisedShow(eventDesignDetail.isTotalFundRaisedShow());
		settings.setSocialSharingEnabled(eventDesignDetail.isSocialSharingEnabled());
		settings.setReplyToEmail(eventDesignDetail.getReplyToEmail());
        settings.setEmailSenderName(eventDesignDetail.getEmailSenderName());
        settings.setNotificationEmailDomain(EmailUtils.getEmailDomain(null != event.getWhiteLabel() ? event.getWhiteLabel().getNotificationEmail() : ""));
        settings.setNotificationEmailUser(EmailUtils.getEmailUserName(eventDesignDetail.getNotificationEmail()));
		settings.setTrackingScript(eventDesignDetail.getTrackingScript());
		settings.setVirtualEventScript(eventDesignDetail.getVirtualEventScript());
		settings.setTrackinPixelId(event.getTrackinPixelId());
		settings.setLinkedinTrackinPartnerId(eventDesignDetail.getLinkedinTrackinPartnerId());
		settings.setLinkedinTrackinConversionId(eventDesignDetail.getLinkedinTrackinConversionId());
        settings.setPrintNodeEnabled(eventDesignDetail.isPrintNodeEnabled());
        settings.setPrintNodeApiKey(eventDesignDetail.getPrintNodeApiKey());
        settings.setEnableRfidBadgeScanning(eventDesignDetail.isEnableRfidBadgeScanning());
		StripeTransaction stripeTransaction = stripeTransactionService.getTextToGiveSubscriptionId(event);
        List<ChargebeeTransaction> chargebeeTransactionList = chargebeeBillingService.getTextToGiveSubscriptionId(event);
        ChargebeeTransaction chargebeeTransaction = chargebeeTransactionList != null && !chargebeeTransactionList.isEmpty() ? chargebeeTransactionList.get(chargebeeTransactionList.size() - 1) : null;
        if(event.isTextToGiveEnabled()){
            if(null != stripeTransaction) {
                Subscription subscription = stripePaymentService.getSubscription(stripeConfiguration.getAPI_KEY(), stripeTransaction.getTextToGiveSubscriptionId());
                if (null != subscription.getCancelAtPeriodEnd() && subscription.getCancelAtPeriodEnd()) {
                    settings.setTextToGiveAvailableUntil(Constants.AVAILABLE_UNTIL_DATE.replace("{SUBSCRIPTION_END_DATE}", new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH).format(new Date(subscription.getCurrentPeriodEnd() * 1000))));
                } else if (Constants.STRIPE_CANCELED_STATUS.equalsIgnoreCase(subscription.getStatus())) {
                    settings.setTextToGiveSubscriptionStatus(STRIPE_CANCELED_STATUS);
                }
            } else if(null != chargebeeTransaction){
                chargebeeBillingService.setTextToGiveSubscriptionInSettings(settings,chargebeeTransaction.getTextToGiveSubscriptionId());
            }
        }
        log.info("Retrieved host general settings of the event: {}", event.getEventId());
		return settings;
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED,rollbackFor = {Exception.class})
	public void setHostGeneralSettings(User user, Event event, HostGeneralSettings settings) {
        log.info("Update host general setting of the event {} | updated by {}",event.getEventId(), user.getUserId());
        log.info("New general setting changes: {}", settings);
		EventDesignDetail eventDesignDetail = eventDesignDetailService.findByEvent(event);
        String oldTrackingScript = eventDesignDetail.getTrackingScript();
        log.info("old tracking script: {} | new tracking script: {}", oldTrackingScript, settings.getTrackingScript());
        String oldVirtualEventScript = eventDesignDetail.getVirtualEventScript();
        log.info("old virtual event script: {} | new virtual event script: {}", oldVirtualEventScript, settings.getVirtualEventScript());
        event.setCurrency(settings.getCurrency());
		event.setFundRaisingGoal(settings.getFundRaisingGoal());
		event.setGoalStartingAmount(settings.getGoalStartingAmount());
		event.setSilentAuctionEnabled(settings.isSilentAuctionEnabled());
		event.setRaffleEnabled(settings.isRaffleEnabled());
		event.setCauseAuctionEnabled(settings.isCauseAuctionEnabled());
		event.setDonationEnabled(settings.isDonationEnabled());
		event.setTicketingEnabled(settings.isTicketingEnabled());
		event.setTextToGiveEnabled(settings.isTextToGiveEnabled());
		event.setAnalyticsId(settings.getAnalyticsId());
		event.setTrackinPixelId(settings.getTrackinPixelId());
        eventDesignDetail.setLinkedinTrackinPartnerId(settings.getLinkedinTrackinPartnerId());
        eventDesignDetail.setLinkedinTrackinConversionId(settings.getLinkedinTrackinConversionId());
		eventDesignDetail.setTotalFundRaisedShow(settings.isTotalFundRaisedShow());
		eventDesignDetail.setSocialSharingEnabled(settings.isSocialSharingEnabled());
		eventDesignDetail.setReplyToEmail(settings.getReplyToEmail());
        eventDesignDetail.setEmailSenderName(settings.getEmailSenderName());
        eventDesignDetail.setNotificationEmail(
                EmailUtils.getEmailUsingUserNameAndDomain(settings.getNotificationEmailUser(),
                        null != event.getWhiteLabel() ? EmailUtils.getEmailDomain(event.getWhiteLabel().getNotificationEmail()) : ""
                )
        );
		eventDesignDetail.setTrackingScript(settings.getTrackingScript());
		eventDesignDetail.setVirtualEventScript(settings.getVirtualEventScript());
        eventDesignDetail.setPrintNodeEnabled(settings.isPrintNodeEnabled());
        eventDesignDetail.setPrintNodeApiKey(settings.getPrintNodeApiKey());
        eventDesignDetail.setEnableRfidBadgeScanning(settings.isEnableRfidBadgeScanning());
		Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        HostEventOfflinePaymentConfig optionalEntity = hostEventOfflinePaymentConfigService.findByEventId(event.getEventId());
        if (Boolean.TRUE.equals(settings.isTicketingEnabled()) && (optionalEntity==null)) {
                hostEventOfflinePaymentConfigService.save(new HostEventOfflinePaymentConfig(event.getEventId()));
                ticketing.setOfflinePayment(false);

        }
		if(settings.isTicketingEnabled() != ticketing.getActivated()){
            ticketing.setActivated(settings.isTicketingEnabled());
            ticketingService.save(ticketing);
            createDefaultCustomTemplates(event, user);
        }
        if(settings.isTicketingEnabled() && CommonUtil.getLiveEventStatuses().contains(event.getEventListingStatus()) &&
                 !EventFormat.IN_PERSON.equals(event.getEventFormat())){
            EventChargebeePlanConfigDto eventChargebeePlanConfigDto = eventPlanConfigService.getPlanConfiguration(event.getEventId());
            String planName = eventChargebeePlanConfigDto.getChargebeePlanName();
            if(FREE_PLAN.getName().equals(planName)) {
                putEventInDraftMode(event);
            }
        }
        if(StringUtils.isNotBlank(settings.getTrackingScript()) && StringUtils.isNotBlank(oldTrackingScript) && !oldTrackingScript.equalsIgnoreCase(settings.getTrackingScript())){
            hubspotContactService.addContactToCustomCreatedEvent(user,event.getEventId(), Constants.CUSTOM_LANDING_PAGE_JS, HubspotCustomEventType.INTEGRATION, null, null);
        }
        if(StringUtils.isNotBlank(settings.getVirtualEventScript()) && StringUtils.isNotBlank(oldVirtualEventScript) && !oldVirtualEventScript.equalsIgnoreCase(settings.getVirtualEventScript())){
            hubspotContactService.addContactToCustomCreatedEvent(user,event.getEventId(), Constants.CUSTOM_EVENT_HUB_JS, HubspotCustomEventType.INTEGRATION, null, null);
        }
		eventDesignDetailService.save(eventDesignDetail);
		eventRepoService.save(event);
		log.info("Saved host general settings of the event: {}", event.getEventId());
	}

	@Override
	public void putEventInDraftMode(Event event){
        event.setEventListingStatus(EventListingStatus.PREVIEW);
        eventRepoService.save(event);
    }

	@Override
	public HostEventDetailsDto getHostEventDetails(Event event) {
		Stripe stripe = this.roStripeService.findByEvent(event);
		HostEventDetailsDto hostEventDetails = new HostEventDetailsDto();
		hostEventDetails.setEventUrl(event.getEventURL());
		hostEventDetails.setEventNamel(event.getName());
		hostEventDetails.setEventPhoneNumber(phoneNumberService.getDisplayNumber(event));
		hostEventDetails.setEventCountryCode(event.getCountryCode() != null ? event.getCountryCode().getCode() : "");
		hostEventDetails.setOrganizerURL(null != event.getOrganizer() ? event.getOrganizer().getOrganizerPageURL() : null);
		hostEventDetails.setNoModuleActivate(!(event.isCauseAuctionEnabled() || event.isSilentAuctionEnabled()
				|| event.isRaffleEnabled() || event.isTicketingEnabled() || event.isTextToGiveEnabled()));
		hostEventDetails.setAuctionEnabled(event.isSilentAuctionEnabled());
		hostEventDetails.setFundANeedEnabled(event.isCauseAuctionEnabled());
		hostEventDetails.setRaffleEnabled(event.isRaffleEnabled());
		hostEventDetails.setTicketingEnabled(event.isTicketingEnabled());
		hostEventDetails.setDonationEnabled(event.isDonationEnabled());
		hostEventDetails.setTextToGiveEnabled(event.isTextToGiveEnabled());
		hostEventDetails.setCurrencySymbol(event.getCurrency().getSymbol());
		hostEventDetails.setEnableBidderRegistration(event.getEnableBidderRegistration());
		hostEventDetails.setEventId(event.getEventId());
		hostEventDetails.setAccountActivatedTriggerStatus(event.getAccountActivatedTriggerStatus().name());
		EventDesignDetail eventDesignDetail = roEventDesignDetailService.findByEventOrThrowError(event);
        EventDesignDetailDto eventDesignDetailDto = new EventDesignDetailDto(eventDesignDetail, imageConfiguration.getDefaultHeader(),
                imageConfiguration.getDefaultItem(), imageConfiguration.getDisplayTabsColor(),
                imageConfiguration.getBlackLogo(), imageConfiguration.getDisplayTabsTextColor(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()),
                imageConfiguration.getDefaultBanner(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()),
                imageConfiguration.getDisplayBackgroundColor(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()),
                imageConfiguration.getDisplayTextColor(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()),
                event.getWhiteLabel(), StringUtils.EMPTY);
        eventDesignDetailDto.setAdvancedWebsiteDefaultActivated(roBeeFreePagesService.isAnyBeeFreePageAsDefaultHome(event.getEventId()));
        hostEventDetails.setEventDesignDetailDto(eventDesignDetailDto);
		hostEventDetails.setCountryCode(event.getCountryCode());
		hostEventDetails.setEquivalentTimezone(TimeZoneUtil.getEquivalentTimeZone(event.getEquivalentTimeZone()));
        hostEventDetails.setShowSessionByTimezone(event.isStaticTimezone());
        EventModules eventModulesStatus = eventRepository.getStatusOfAllTheModulesByEvent(event.getEventId());
		hostEventDetails.setAuctionModuleShow(eventModulesStatus.getAuctionModuleShow());
		hostEventDetails.setRaffleModuleShow(eventModulesStatus.getRaffleModuleShow());
		hostEventDetails.setFundANeedModuleShow(eventModulesStatus.getFundANeedModuleShow());
		hostEventDetails.setRecurring(eventModulesStatus.getRecurringEvent());
		hostEventDetails.setCreditCardEnabled(event.isCreditCardEnabled());
		hostEventDetails.setStripeKey(stripe.getStripePublishableKey());
		if (EnumPaymentGateway.SQUARE.toString().equals(stripe.getPaymentGateway())) {
			hostEventDetails.setSquareLocationId(stripe.getSquareMerchantLocation());
		}
		hostEventDetails.setPaymentGateway(stripe.getPaymentGateway());
		hostEventDetails.setProcessingFeesToPurchaser(stripe.isProcessingFeesToPurchaser());
		hostEventDetails.setEventListingStatus(event.getEventListingStatus());
		hostEventDetails.setEventFormat(event.getEventFormat());
        hostEventDetails.setSupportEnabled(event.getWhiteLabel() != null && StringUtils.isNotEmpty(event.getWhiteLabel().getSupportEmail()));
        setRegistrationApprovalFlags(event, hostEventDetails);
        HostEventOfflinePaymentConfig optionalEntity = hostEventOfflinePaymentConfigService.findByEventId(event.getEventId());
        if (null!=optionalEntity)
        {
            hostEventDetails.setOfflinePayment(optionalEntity.isOfflinePayment());
        }
        Optional<EventsTemplates> eventTemplate = eventTemplateRepository.findByEventId(event.getEventId());
        hostEventDetails.setTemplateEvent(eventTemplate.isPresent());
        hostEventDetails.setChargebeeSubscriptionExpiryDate(chargebeeService.getChargebeeSubscriptionExpiryDate(event));
        if (event.getWhiteLabelId() != null) {
            hostEventDetails.setEnableEventClosingForm(roWhiteLabelSettingsService.isEnabledEventClosingFormForWhiteLabel(event.getWhiteLabelId()));
        }
        return hostEventDetails;
	}

    private void setRegistrationApprovalFlags(Event event, HostEventDetailsDto hostEventDetails) {
        Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());
        if(ticketing != null ){
            hostEventDetails.setAttendeeRegistrationApproval(ticketing.isAttendeeRegistrationApproval());
        }
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        if(virtualEventSettings != null) {
            hostEventDetails.setExpoRegistrationApproval(virtualEventSettings.isExhibitorRegistrationApproval());
            hostEventDetails.setSpeakerRegistrationApproval(virtualEventSettings.isSpeakerRegistrationApproval());
        }
    }

    @Override
	public HostHomeDto getHostHomeDetail(Event event, Long recurringEventId) {
		log.info("home|getHostHomeDetail|start:{}",System.currentTimeMillis());
		HostHomeDto homeDto = new HostHomeDto();

		Auction auction = auctionService.findByEvent(event);
		if (auction != null && auction.getActualEndDate() != null) {
			HostAuctionDetail hostAuctionDetail = new HostAuctionDetail();
			long auctionId = auction.getId();
			hostAuctionDetail.setCollectedAmout(event.getGoalStartingAmount() + auctionService.getSumOfHighestBidAmountForEachItem(auctionId));
			hostAuctionDetail.setEndDate(auction.getActualEndDate());
			hostAuctionDetail.setNumberOfBidder(auctionService.getTotalBidSubmittedUserCount(auctionId));
			hostAuctionDetail.setTotalItems(itemService.countItemByModuleIdAndModuleType(auctionId, ModuleType.AUCTION));
			hostAuctionDetail.setActive(auction.isActivated());
			hostAuctionDetail.setStatus(auction.getAuctionStatus());
			hostAuctionDetail = auctionBidService.setHostAuctionDetail(auctionId, hostAuctionDetail);
			homeDto.setAuctionDetail(hostAuctionDetail);
		}
		Raffle raffle = raffleService.findByEvent(event);
		if (raffle != null && raffle.getEndDate() != null) {
			HostRaffleDetail hostRaffleDetail = new HostRaffleDetail();
			hostRaffleDetail.setEndDate(raffle.getEndDate());
			long raffleId = raffle.getId();
			List<PurchasedRaffleTicket> purchasedRaffleTickets = purchasedRaffleTicketService.getAllPurchasedTicketsForRaffle(raffleId);
			hostRaffleDetail.setCollectedAmout(event.getGoalStartingAmount() + raffleService.getSumOfAllPurchasedRaffleTicketPriceForEachItem(purchasedRaffleTickets));
			hostRaffleDetail.setNumberOfTicketPurchased(raffleService.getTotalTicketSubmittedUserCount(purchasedRaffleTickets));
			hostRaffleDetail.setTotalTicketPurchased(raffleService.geTotalTicketPurchased(purchasedRaffleTickets));
			hostRaffleDetail.setTotalItems(itemService.countItemByModuleIdAndModuleType(raffleId, ModuleType.RAFFLE));
			hostRaffleDetail.setItemsWithTickets(raffleService.getItemsWithTickets(raffleId));
			hostRaffleDetail.setTotalBuyers(raffleService.getTotalBuyers(raffleId));
			hostRaffleDetail.setActive(raffle.isActivated());
			hostRaffleDetail.setStatus(raffle.getRaffleStatus());
			homeDto.setRaffleDetail(hostRaffleDetail);
		}
		CauseAuction causeAuction = causeAuctionService.findByEvent(event);
		if (causeAuction != null && causeAuction.getEndDate() != null) {
			HostFundANeedDetail hostFundANeedDetail = new HostFundANeedDetail();
			hostFundANeedDetail.setActive(causeAuction.isActivated());
			hostFundANeedDetail.setCollectedAmout(event.getGoalStartingAmount() + causeAuctionService.getTotalProceedsCauseAuctionAmount(causeAuction));
			long causeAuctionId = causeAuction.getId();
			hostFundANeedDetail.setDonors(causeAuctionService.getTotalPledgeSubmittedUserCount(causeAuctionId));
			hostFundANeedDetail.setItemsWithDonations(pledgeService.countOfItemsWithPledge(causeAuctionId));
			hostFundANeedDetail.setTotalDonations(pledgeService.countDistinctByCauseAuctionId(causeAuctionId));
			hostFundANeedDetail.setEndDate(causeAuction.getEndDate());
			hostFundANeedDetail.setStatus(causeAuction.getCauseAuctionStatus());
			hostFundANeedDetail.setTotalItems(itemService.countItemByModuleIdAndModuleType(causeAuctionId, ModuleType.CAUSEAUCTION));
			homeDto.setFundANeedDetail(hostFundANeedDetail);
		}

		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
		if (ticketing != null) {
			List<TicketingType> ticketingTypes = ticketingTypeTicketService.findAllByTicketingAndRecuringEvent(recurringEventId, event);
			List<TicketingType> paidTicketType = ticketingTypes.stream().filter(e -> TicketType.PAID.equals(e.getTicketType())).collect(Collectors.toList());
			List<TicketingType> freeTicketType = ticketingTypes.stream().filter(e -> TicketType.FREE.equals(e.getTicketType())).collect(Collectors.toList());
			List<TicketingType> donationTicketType = ticketingTypes.stream().filter(e -> TicketType.DONATION.equals(e.getTicketType())).collect(Collectors.toList());

			long numberOfTicketSold = ticketingTypes.stream().mapToLong(type -> ticketingStatisticsService.soldTicketCount(type)).sum();
			long paidNumberOfTicketSold = paidTicketType.stream().mapToLong(type -> ticketingStatisticsService.soldTicketCount(type)).sum();
			long freeNumberOfTicketSold = freeTicketType.stream().mapToLong(type -> ticketingStatisticsService.soldTicketCount(type)).sum();

			int numberOfDonationTickets = ticketingStatisticsService.getTotalTicketsSoldCountByTicketType(donationTicketType);
			HostEventTicketDetail hostEventTicketDetail = new HostEventTicketDetail();
			hostEventTicketDetail.setActive(ticketing.getActivated());
			hostEventTicketDetail.setCollectedAmout(ticketingStatisticsService.getNetSale(event, recurringEventId, false));
			hostEventTicketDetail.setNumberOfTicketSold((int) numberOfTicketSold - numberOfDonationTickets);
			hostEventTicketDetail.setStartDate(ticketing.getEventStartDate());
			hostEventTicketDetail.setEndDate(ticketing.getEventEndDate());
			hostEventTicketDetail.setTotalPaidTickets(countTotalTickets(paidTicketType));
			hostEventTicketDetail.setTotalFreeTickets(countTotalTickets(freeTicketType));
			hostEventTicketDetail.setTotalPaidTicketsSold(paidNumberOfTicketSold);
			hostEventTicketDetail.setTotalFreeTicketsSold(freeNumberOfTicketSold);
			hostEventTicketDetail.setTotalDonationTicketsSold(numberOfDonationTickets);
			hostEventTicketDetail.setTotalTickets(countTotalTickets(ticketingTypes));
			homeDto.setTicketingDetail(hostEventTicketDetail);
		}

		DonationSettings donationSettings = donationSettingsService.getByEventId(event.getEventId());
		if (donationSettings != null) {
			DonationDisplaySetting donationDisplaySetting= donationService.getNumberOfDonorsWithSumOfTotalPaidDonation(event);
			HostTextToDonateDetail hostTextToDonateDetail = new HostTextToDonateDetail();
			hostTextToDonateDetail.setActive(donationSettings.isTextToGiveActivated());
			hostTextToDonateDetail.setTotalFundRaised(donationDisplaySetting.getTotalFundRaised());
			hostTextToDonateDetail.setNumberOfDonors(donationDisplaySetting.getNumberOfDonors());
			if (event.isTextToGiveEnabled()) {
				hostTextToDonateDetail.setActive(donationSettings.isTextToGiveActivated());
			} else if (event.isDonationEnabled()) {
				hostTextToDonateDetail.setActive(this.getHostCreditCardSettings(event).isStripeActivated());
			}

			homeDto.setTextToDonateDetail(hostTextToDonateDetail);
		}

		homeDto.setEventPhoneNumber(phoneNumberService.getDisplayNumber(event));
		homeDto.setEventCountryCode(event.getCountryCode() != null ? event.getCountryCode().getCode() : "");

		homeDto.setCheckList(getChecklist(event));
		homeDto.setEventUrl(event.getEventURL());
		log.info("home|getHostHomeDetail|end:{}",System.currentTimeMillis());
		return homeDto;
	}





	private Long countTotalTickets(List<TicketingType> ticketingTypes) {
		return ticketingTypes.stream().mapToLong(TicketingType::getNumberOfTickets).sum();
	}

	@Override
	public List<EventChecklistItem> getChecklist(Event event) {
		EventChecklist checklist = eventChecklistService.findByEvent(event);

		List<EventChecklistItem> items = new ArrayList<>();

		EventChecklistItem item;
		item = new EventChecklistItem();
		item.setComplete(checklist.isNamingCompleted());
		item.setLabel("Name your event");
		item.setDescription("Your event name will be used through the system.");
		item.setTodoButtonText("Set Event Name");
		// TODO : Change this if stage merged to prod with old UI
		//item.setTodoLink(event.isOldUI() ? "/host/event-setup" : "/host/event-ticketing/event-details");//NOSONAR
		item.setTodoLink("/host/event-ticketing/event-details");
		item.setDoneButtonText("Set Event Name");
		item.setActive(true);
		items.add(item);

		if (event.isSilentAuctionEnabled()) {
			items.add(addAuctionCheckListItem(checklist));

			item = new EventChecklistItem();
			item.setComplete(checklist.isAuctionItemAdded());
			item.setLabel("Add auction items");
			item.setDescription("Add the Auction Items your guests will bid for.");
			item.setTodoLink("/host/auction/add-items");
			item.setTodoButtonText("Add Auction Items");
			item.setDoneButtonText("Add Auction Items");
			item.setActive(true);
			items.add(item);

			item = new EventChecklistItem();
			item.setComplete(checklist.isAuctionDateTimeSet());
			item.setLabel("Set auction date and ending time");
			item.setDescription("Specify when the silent auction winners will be selected.");
			item.setTodoLink("/host/auction/settings");
			item.setTodoButtonText(SETDATE_AND_TIME);
			item.setDoneButtonText(SETDATE_AND_TIME);
			item.setActive(true);
			items.add(item);

			item = new EventChecklistItem();
			item.setComplete(auctionService.findByEvent(event).isActivated());
			item.setLabel(ACTIVATE_AUCTION);
			item.setDescription("Activate your auction to accept bids from your guests.");
			item.setTodoLink(HOST_ACCOUNT_PAGE_URL);
			item.setTodoButtonText(ACTIVATE_AUCTION);
			item.setDoneButtonText(ACTIVATE_AUCTION);
			item.setActive(true);
			items.add(item);

		}
		if (event.isRaffleEnabled()) {
			items.add(addRaffleCheckListItem(checklist));

			item = new EventChecklistItem();
			item.setComplete(checklist.isRaffleItemAdded());
			item.setLabel("Add raffle items");
			item.setDescription("Add the Raffle Items your guests will bid for.");
			item.setTodoLink("/host/raffle/add-items");
			item.setTodoButtonText("Add Raffle Items");
			item.setDoneButtonText("Add Raffle Items");
			item.setActive(true);
			items.add(item);

			item = new EventChecklistItem();
			item.setComplete(checklist.isRaffleDateTimeSet());
			item.setLabel("Set raffle date and ending time");
			item.setDescription("Specify when the raffle winners will be selected.");
			item.setTodoLink("/host/raffle/settings");
			item.setTodoButtonText(SETDATE_AND_TIME);
			item.setDoneButtonText(SETDATE_AND_TIME);
			item.setActive(true);
			items.add(item);

			item = new EventChecklistItem();
			item.setComplete(raffleService.findByEvent(event).isActivated());
			item.setLabel(ACTIVATE_RAFFLE);
			item.setDescription("Activate your raffle to sell tickets to your guests.");
			item.setTodoLink(HOST_ACCOUNT_PAGE_URL);
			item.setTodoButtonText(ACTIVATE_RAFFLE);
			item.setDoneButtonText(ACTIVATE_RAFFLE);
			item.setActive(true);
			items.add(item);
		}
		if (event.isCauseAuctionEnabled()) {
			items.add(addFundANeedCheckListItem(checklist));

			item = new EventChecklistItem();
			item.setComplete(checklist.isCauseAuctionDateTimeSet());
			item.setLabel("Set fund a need date and ending time");
			item.setDescription("Specify when the fund a need winners will be selected.");
			item.setTodoLink("/host/fund-a-need/settings");
			item.setTodoButtonText(SETDATE_AND_TIME);
			item.setDoneButtonText(SETDATE_AND_TIME);
			item.setActive(true);
			items.add(item);

			item = new EventChecklistItem();
			item.setComplete(causeAuctionService.findByEvent(event).isActivated());
			item.setLabel("Activate Fund a Need Campaign");
			item.setDescription("Activate your fund a need campaign to accept pledges from your guests.");
			item.setTodoLink(HOST_ACCOUNT_PAGE_URL);
			item.setTodoButtonText("Activate Fund a Need");
			item.setDoneButtonText("Activate Fund a Need");
			item.setActive(true);
			items.add(item);
		}

		if (roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event) || ticketingTypeTicketService.isAnyTicketExistsWithDonationType(event)) {
			item = new EventChecklistItem();
			item.setComplete(stripeService.isPresentStripeByEventAndDefaultAccount(event, true));
			item.setLabel(ACTIVATE_PAYMENT_PROCESSING);
			item.setDescription(ACTIVATE_PAYMENT_PROCESSING);
			item.setTodoLink("/host/settings/credit-card");
			item.setTodoButtonText(ACTIVATE_PAYMENT_PROCESSING);
			item.setDoneButtonText(ACTIVATE_PAYMENT_PROCESSING);
			item.setActive(true);
			items.add(item);
		}

		item = new EventChecklistItem();
		item.setComplete(checklist.isEventURLCustomized());
		item.setLabel(CUSTOMIZE_EVENT_URL);
		item.setDescription(CUSTOMIZE_EVENT_URL);
		// TODO : Change this if stage merged to prod with old UI
		//item.setTodoLink(event.isOldUI() ? "/host/event-setup" : "/host/event-ticketing/event-details");//NOSONAR
		item.setTodoLink("/host/event-ticketing/event-theme");
		item.setTodoButtonText(CUSTOMIZE_EVENT_URL);
		item.setDoneButtonText(CUSTOMIZE_EVENT_URL);
		item.setActive(true);
		items.add(item);

		if (event.isTextToGiveEnabled()) {
			items.add(addTextToGiveCheckListItem(checklist));

			item = new EventChecklistItem();
            DonationSettings donationSettings = donationSettingsService.getByEventId(event.getEventId());
			item.setComplete(donationSettings != null && donationSettings.isTextToGiveActivated());
			item.setLabel("Activate Text to Give Campaign");
			item.setDescription("Activate your subscription of text to give campaign to accept donations.");
			item.setTodoLink(HOST_ACCOUNT_PAGE_URL);
			item.setTodoButtonText("Activate Text to Give");
			item.setDoneButtonText("Activate Text to Give");
			item.setActive(true);
			items.add(item);
		}

		//Add virtual event checklist items
		if(event.isTicketingEnabled() && (EventFormat.HYBRID.equals(event.getEventFormat()) || EventFormat.VIRTUAL.equals(event.getEventFormat()))){
			items.addAll(addVirtualEventChecklist(event, checklist));
		}

		items.add(addEventListingCheckListItem(event));
		items.sort((o1, o2) -> {
            int o2complete=o2.isComplete() ? -1 : 0;
            return (o1.isComplete() ? 1 : o2complete);});
		return items;
	}

	private List<EventChecklistItem> addVirtualEventChecklist(Event event, EventChecklist checklist){

		List<EventChecklistItem> items = new ArrayList<>();

		EventChecklistItem item = new EventChecklistItem();
		item.setComplete(checklist.isEventLogoUploaded());
		item.setLabel(ADD_EVENT_LOGO);
		item.setDescription(ADD_EVENT_LOGO);
		item.setTodoLink("/host/event-ticketing/event-details");
		item.setTodoButtonText(ADD_EVENT_LOGO);
		item.setDoneButtonText(ADD_EVENT_LOGO);
		item.setActive(true);
		items.add(item);

		item = new EventChecklistItem();
		item.setComplete(checklist.isTeamAdded());
		item.setLabel(ADD_TEAM_MEMBERS);
		item.setDescription(ADD_TEAM_MEMBERS);
		item.setTodoLink("/host/settings/manage-team");
		item.setTodoButtonText(ADD_TEAM_MEMBERS);
		item.setDoneButtonText(ADD_TEAM_MEMBERS);
		item.setActive(true);
		items.add(item);

		item = new EventChecklistItem();
		item.setComplete(checklist.isTicketAdded());
		item.setLabel(SETUP_REGISTRATION);
		item.setDescription("Configuring a ticket type");
		item.setTodoLink("/host/event-ticketing/create");
		item.setTodoButtonText(SETUP_REGISTRATION);
		item.setDoneButtonText(SETUP_REGISTRATION);
		item.setActive(true);
		items.add(item);

		VirtualEventSettingsDTO virtualEventSetting = virtualEventService.getVirtualEventSettingsById(event);
		if(virtualEventSetting != null){
			if(virtualEventSetting.isSessionEnabled() || virtualEventSetting.isStageEnabled()){
				item = new EventChecklistItem();
				item.setComplete(checklist.isEventAgendaAdded());
				item.setLabel(SETUP_EVENT_AGENDA);
				item.setDescription("Add session for your event");
				item.setTodoLink("/host/event-ticketing/agenda");
				item.setTodoButtonText(SETUP_EVENT_AGENDA);
				item.setDoneButtonText(SETUP_EVENT_AGENDA);
				item.setActive(true);
				items.add(item);
			}

			if(virtualEventSetting.isExpoEnabled()){
				item = new EventChecklistItem();
				item.setComplete(checklist.isExhibitorAdded());
				item.setLabel(ADD_EXHIBITOR);
				item.setDescription(ADD_EXHIBITOR);
				item.setTodoLink("/host/exhibitor");
				item.setTodoButtonText(ADD_EXHIBITOR);
				item.setDoneButtonText(ADD_EXHIBITOR);
				item.setActive(true);
				items.add(item);
			}
		}
		return  items;
	}

	private EventChecklistItem addEventListingCheckListItem(Event event) {
		EventChecklistItem item = new EventChecklistItem();
		item.setComplete(CommonUtil.getLiveEventStatuses().stream()
                .anyMatch(el -> el.equals(event.getEventListingStatus())));
		item.setLabel("Publish your event");
		item.setDescription("Publish your event");
		item.setTodoLink("#publish-event");
		item.setDialog(true);
		item.setTodoButtonText("Publish Event");
		item.setDoneButtonText("Publish Event");
		item.setActive(true);
		return item;
	}

	private EventChecklistItem addAuctionCheckListItem(EventChecklist checklist) {
		EventChecklistItem item = new EventChecklistItem();
		item.setComplete(checklist.isAuctionBidSubmitted());
		item.setLabel("Submit a sample bid for your Auction");
		item.setDescription("Give your silent auction a try to see how it works for free!");
		item.setTodoLink("#sample-bid");
		item.setDialog(true);
		item.setTodoButtonText("Submit Bid");
		item.setDoneButtonText("Submit Bid");
		item.setActive(true);
		return item;
	}

	private EventChecklistItem addRaffleCheckListItem(EventChecklist checklist) {
		EventChecklistItem item = new EventChecklistItem();
		item.setComplete(checklist.isRaffleTicketSubmitted());
		item.setLabel("Submit a sample ticket for your Raffle");
		item.setDescription("Submit a sample ticket for your Raffle to see how it works for free!");
		item.setTodoLink("#sample-ticket");
		item.setDialog(true);
		item.setTodoButtonText("Submit Raffle Ticket");
		item.setDoneButtonText("Submit Raffle Ticket");
		item.setActive(true);
		return item;
	}

	private EventChecklistItem addFundANeedCheckListItem(EventChecklist checklist) {
		EventChecklistItem item = new EventChecklistItem();
		item.setComplete(checklist.isCauseAuctionPledgeSubmitted());
		item.setLabel("Submit a sample pledge for your fund a need");
		item.setDescription("Submit a sample pledge for your fund a need to see how it works for free!");
		item.setTodoLink("#sample-cause-bid");
		item.setDialog(true);
		item.setTodoButtonText("Submit Pledge");
		item.setDoneButtonText("Submit Pledge");
		item.setActive(true);
		return item;
	}

	private EventChecklistItem addTextToGiveCheckListItem(EventChecklist checklist) {
		EventChecklistItem item = new EventChecklistItem();
		item.setComplete(checklist.isTextToDonate());
		item.setLabel("Submit a sample Text to Give");
		item.setDescription("Enter your cell phone number. A text message will be sent to you with instructions on how to 'Text to Give'");
		item.setTodoLink("#sample-texttogive");
		item.setDialog(true);
		item.setTodoButtonText("Sample Text to Give");
		item.setDoneButtonText("Sample Text to Give");
		item.setActive(true);
		return item;
	}

	@Override
	public EventChecklistItem getPopUpDetailsForAccountActivationRequired(Event event) {
		EventChecklist checklist = eventChecklistService.findByEvent(event);
		if (event.isSilentAuctionEnabled()) {
			return addAuctionCheckListItem(checklist);
		} else if (event.isRaffleEnabled()) {
			return addRaffleCheckListItem(checklist);
		} else if (event.isCauseAuctionEnabled()) {
			return addFundANeedCheckListItem(checklist);
		} else {
			return addTextToGiveCheckListItem(checklist);
		}
	}

	@Override
	public GetHostCreditCardSettings getHostCreditCardSettings(Event event) {
		Event dbEvent = roEventService.getEventById(event.getEventId());

		GetHostCreditCardSettings settings = new GetHostCreditCardSettings();
		Stripe stripe = stripeService.findByEventWithEmailDisplayName(dbEvent);
		if (stripe != null) {
			settings.setProcessingFeesToPurchaser(stripe.isProcessingFeesToPurchaser());
			settings.setCcRequireForBidConfirm(dbEvent.isCcRequiredForBidConfirm());
			settings.setTaxId(dbEvent.getTaxId());
			settings.setStripeConnected(true);
			settings.setStripeActivated(stripe.isActivated());
			settings.setConnectButtonEnabled(isBlank(stripe.getAccessToken()));
			settings.setStripeAccountEmail(stripe.getEmail());
			settings.setPaymentGateway(stripe.getPaymentGateway());
			settings.setStripeAccountName(stripe.getAccountDisplayName());
            settings.setAllowConnectingSquarePayments(dbEvent.getWhiteLabel() == null || !transactionFeeConditionalLogicService.isWlAFeeExists(dbEvent));
			settings.setEventBillingId(stripe.getEventBillingId());
            if (stripe.getStaff() != null){
                settings.setStripeAccountConnectorId(stripe.getStaff().getUserId());
            }
		}
		log.info("Retrieved host credit card settings");
		return settings;
	}

	@Override
	@Transactional
	public void setHostCreditCardSettings(Event event, HostCreditCardSettings settings, User user, String ipAddress) {
		event.setCcRequiredForBidConfirm(settings.isCcRequireForBidConfirm());
		event.setTaxId(settings.getTaxId());
		eventRepoService.save(event);
		if (Boolean.TRUE.equals(stripeService.isStripeConnected(event))) {
			Stripe stripe = roStripeService.findByEvent(event);
			stripe.setProcessingFeesToPurchaser(settings.isProcessingFeesToPurchaser());
			stripeService.save(stripe);
			DonationSettings donationSettings = donationSettingsService.getByEventId(event.getEventId());
			donationSettings.setAbsorbFee(!settings.isProcessingFeesToPurchaser());
			donationSettingsService.save(donationSettings);
		}
		log.info("Saved host credit card settings");
	}

	@Override
	public String getWhiteLabelEventUrlOrEventUrl(Event event) {
		String eventURL;
		if (null != event.getWhiteLabel() && isNotBlank(event.getWhiteLabel().getHostBaseUrl())) {
			eventURL = event.getWhiteLabel().getHostBaseUrl() + getEventPath() + event.getEventURL();
		} else {
			eventURL = uiBaseurl + getEventPath() + event.getEventURL();
		}
		return eventURL;
	}

	@Override
	public HostBillingSettings getHostBillingSettings(Event event) {
		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
		HostBillingSettings settings = new HostBillingSettings();
		settings.setSlientAuctionActivated(auctionService.findIsActivatedByEventId(event.getEventId()));
		settings.setCauseAuctionActivated(causeAuctionService.findIsActivatedByEventId(event.getEventId()));
		settings.setRaffleActivated(raffleService.findIsActivatedByEventId(event.getEventId()));
		settings.setNumberOfPurchasedExhibitor(virtualEventService.getTheNumberOfExhibitorsPurchased(event));
		settings.setNumberOfExhibitor(exhibitorRepoService.findNumberOfExhibitorsByEventId(event));
		settings.setNumberOfDaysOfEvent(DateUtils.getNumberDaysInclusiveBothTheDatesInEventTimeZone
				(ticketing.getEventStartDate(), ticketing.getEventEndDate(), event.getEquivalentTimeZone()));
/*		if(event.isTicketingEnabled()){//NOSONAR
			Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
			if(ticketing.isOnlineEvent()){

			} else {
				settings.setTicketingActivated();
			}
		}
*/
		settings.setTicketingActivated(event.isTicketingEnabled()
				&& ticketing.getActivated());
		settings.setTextToGiveActivated(donationSettingsService.findTextToGiveActivatedByEventId(event.getEventId()));
		settings.setPublicKey(stripeConfiguration.getPUBLIC_KEY());
        settings.setBillingType(event.getBillingType());
        StripeTransaction stripeTransaction = stripeTransactionService.getTextToGiveSubscriptionId(event);
        List<ChargebeeTransaction> chargebeeTransactionList = chargebeeBillingService.getTextToGiveSubscriptionId(event);
        ChargebeeTransaction chargebeeTransaction = chargebeeTransactionList != null && !chargebeeTransactionList.isEmpty() ? chargebeeTransactionList.get(chargebeeTransactionList.size() - 1) : null;
        if (stripeTransaction != null) {
			settings.setTextToGiveSubscriptionId(stripeTransaction.getTextToGiveSubscriptionId());
			try {
				Subscription subscription = stripePaymentService.getSubscription(stripeConfiguration.getAPI_KEY(), stripeTransaction.getTextToGiveSubscriptionId());
                if(subscription != null && !subscription.getItems().getData().isEmpty()) {
                    Plan stripePlan = subscription.getItems().getData().get(0).getPlan();
                    if (stripePlan != null && stripePlan.getAmount() != null) {
                        settings.setTextToGiveAmount((stripePlan.getAmount()) / 100);
                    }
                }
			} catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException | com.stripe.exception.ApiException e) {
				log.error(e.getMessage(), e);
			} catch (StripeException e) {
				e.printStackTrace();
			}
		} else if (chargebeeTransaction!=null){
            chargebeeBillingService.setTextToGiveSubscriptionInBillingSettings(settings,chargebeeTransaction.getTextToGiveSubscriptionId());
        }
       if(null != event.getWhiteLabel()){
            WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto = whiteLabelRepoService.findWhiteLabelSettingsById(event.getWhiteLabel().getId());
            settings.setExpoBoothCharge(null != whiteLabelBillingSettingsDto ? whiteLabelBillingSettingsDto.getExpoBoothCharge() : 99.0);
        }else if(null != event.getOrganizer()){
            Organizer organizer = organizerRepoService.findByIdThrowException(event.getOrganizerId());
            settings.setExpoBoothCharge(organizer.getExpoBoothCharge());
        }else {
           settings.setExpoBoothCharge((double) 99);
       }
		return settings;
	}

	@Transactional(rollbackFor = {RuntimeException.class}, isolation = Isolation.READ_COMMITTED)
	@Override
	public void updateDesign(Event event, EventDesignSettingsDto eventSetting, boolean generateUrlByName, User user) {//NOSONAR
		log.info("EventServiceImpl updateDesign eventId {} EventDesignSettingsDto {}",event.getEventId(), eventSetting);
		EventChecklist eventChecklist = eventChecklistService.findByEvent(event);
        checkListForEventName(event,eventSetting,eventChecklist);

        checkAndSetEventUrl(eventSetting,event,generateUrlByName);
        updateEventCUstomizeUrlAndLogo(event,eventSetting,eventChecklist);
        Organizer oldOrganizer = event.getOrganizer();
        if(null != eventSetting.getEventFormat()) {
            event.setEventFormat(eventSetting.getEventFormat());
            log.info("Change Event Format from Host page eventId{} eventFormat{}",event.getEventId(),event.getEventFormat());
        }
        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
        log.info("updateDesign eventId {} eventPlanConfigId {}",event.getEventId(),eventPlanConfig);
        if (eventPlanConfig != null && oldOrganizer != null  && StringUtils.isNotBlank(oldOrganizer.getOrganizerPageURL())  && StringUtils.isNotBlank(eventSetting.getOrganizerPageURL()) && !oldOrganizer.getOrganizerPageURL().equals(eventSetting.getOrganizerPageURL())) {
            associateOrganizerWithEvent(event, eventSetting, user,eventPlanConfig);
            log.info("updateDesign TransactionFeeConditionalLogic eventId {} updatedBy {}",event.getEventId(), user.getUserId());
        } else {
            if(eventSetting.getOrganizerPageURL() == null || STRING_EMPTY.equals(eventSetting.getOrganizerPageURL())){
                throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_URL_CANNOT_BE_EMPTY);
            }
            Organizer organizerByPageUrl = organizerService.getOrganizerByURL(eventSetting.getOrganizerPageURL());
            if(organizerByPageUrl != null) {
                event.setOrganizer(organizerByPageUrl);
                event.setOrganizerId(organizerByPageUrl.getId());
            }
        }
        EventPlanConfig updatedEventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
        if (oldOrganizer != null && oldOrganizer.getId() != event.getOrganizerId() && eventPlanConfig != null) {
            log.info("Set-up attendee import limit eventId {} eventPlanConfig {} updatedEventPlanConfig {}", event.getEventId(), eventPlanConfig, updatedEventPlanConfig);
            PlanConfig planConfig = updatedEventPlanConfig.getPlanConfig();
            String planName = planConfig.getPlanName();
            if (FREE_PLAN.getName().equals(planName)) {
                Ticketing ticketing = ticketingRepository.findByEventId(event.getEventId());
                ticketing.setLimitEventCapacity(true);
                ticketing.setEventCapacity(100d);
                ticketingRepository.save(ticketing);
                updatedEventPlanConfig.setAttendeeImportLimit(100L);
                eventPlanConfigService.save(updatedEventPlanConfig);
            } else {
                updatedEventPlanConfig.setAttendeeImportLimit(0L);
                eventPlanConfigService.save(updatedEventPlanConfig);
            }
        }
        if (null != event.getHubspotObjectId() && ((null == oldOrganizer && null != event.getOrganizerId()) || (null != oldOrganizer && oldOrganizer.getId() != event.getOrganizerId()))) {
            if (null != oldOrganizer) {
                hubspotCustomLabelsAssociationService.executeDeleteAssociationWithoutAsync(hubspotConfiguration.getEventObjectName(), COMPANY, String.valueOf(event.getHubspotObjectId()), String.valueOf(oldOrganizer.getHubSpotCompanyId()));
                hubspotOrganizerService.createOrDeleteEventToOrganizerAssociation(oldOrganizer.getId(), event.getHubspotObjectId().toString(), Constants.DELETE);
            }
            hubspotOrganizerService.createOrDeleteEventToOrganizerAssociation(event.getOrganizerId(), event.getHubspotObjectId().toString(), Constants.CREATE);
            hubspotEventService.associateOrganizerOrWLEventToCompany(event);
        }
       if((null == oldOrganizer && null != event.getOrganizerId()) || (null != oldOrganizer && oldOrganizer.getId() != event.getOrganizerId())){
            afterTaskIntegrationTriggerService.newEventCreated(event);
        }

        if (null != event.getHubspotObjectId() && (null != oldOrganizer && oldOrganizer.getId() != event.getOrganizerId()) ) {

            //While changing organizer remove billing contact of old organizer from hubspot
            List<User> billingContacts = joinUsersWithOrganizersRepository.findBillingContactByOrgId(oldOrganizer.getId());
            if (!CollectionUtils.isEmpty(billingContacts)) {
                User oldBillingContactUser = billingContacts.get(0);
                String oldBillingContactId = hubspotContactService.createHubspotContact(oldBillingContactUser);
                if (null != oldBillingContactId) {
                    String eventByEventId = hubspotEventService.getEventByEventId(event.getEventId());
                    hubspotCustomLabelsAssociationService.executeDeleteAssociation(hubspotConfiguration.getEventObjectName(), CONTACT, eventByEventId, oldBillingContactId);
                }
            }
            //While changing organizer associate billing contact of new organizer in hubspot
            List<User> newBillingContacts = joinUsersWithOrganizersRepository.findBillingContactByOrgId(event.getOrganizerId());
            if (!CollectionUtils.isEmpty(newBillingContacts)) {
                User newBillingContactUser = newBillingContacts.get(0);
                String newBillingContactId = hubspotContactService.createHubspotContact(newBillingContactUser);
                if (null != newBillingContactId) {
                    String eventByEventId = hubspotEventService.getEventByEventId(event.getEventId());
                    hubspotCustomLabelsAssociationService.executeCreateAssociationObjectWithCustomLabelInBatch(hubspotConfiguration.getEventObjectName(), Constants.CONTACT, eventByEventId, newBillingContactId, Collections.singletonList(EVENT_BILLING_CONTACT));
                }
            }

        }

        if(oldOrganizer == null || !oldOrganizer.getOrganizerPageURL().equals(eventSetting.getOrganizerPageURL())){
            intercomDetailsService.sendOrganizerToIntercom(event);
        }
        deleteApiUserFromStaffForOldOrganizer(event, oldOrganizer, event.getOrganizer());
        if(isNotBlank(eventSetting.getEventName()) && !eventSetting.getEventName().equals(event.getName())) {
            getStreamService.updateChannelName(EVENT_.concat(String.valueOf(event.getEventId())), eventSetting.getEventName(), MESSAGING, false, event.getEventId());
            log.info("Update the channel id: {} name: {} with disable chat toggle: {} of the eventId: {} ", EVENT_.concat(String.valueOf(event.getEventId())), eventSetting.getEventName(), false, event.getEventId());
        }
        if(!event.getName().equals(eventSetting.getEventName())) {
            updateEventNameInCustomTemplates(event.getEventId(), event.getName(), eventSetting.getEventName());
            event.setName(this.getEventName(eventSetting.getEventName(), 1, eventSetting.getEventName().length(), true));
        }
        log.info("Event Status{} ,event id {} and OrganizerId {}", event.getEventStatus() , event.getEventId(), event.getOrganizerId());
		eventRepoService.save(event);

        updateCalenderInvitationForCustomTemplates(event);

		EventDesignDetail eventDesign = this.eventDesignDetailService.findByEvent(event);
		eventDesign.setBannerImage(eventSetting.getBannerImage());
		eventDesign.setBannerImageEnabled(eventSetting.isBannerImageEnabled());
        eventDesign.setVenueMapImage(eventSetting.getVenueMapImage());
        eventDesign.setCustomCssEnable(eventSetting.isCustomCssEnable());

        String defaultBanner = imageConfiguration.getDefaultBanner(eventDesign.getThemeId(), eventDesign.isThemeWiseDefaultConfig());
        if(defaultBanner != null && defaultBanner.equals(eventSetting.getBannerImage())) {
            eventDesign.setBannerImageExtension(FilenameUtils.getExtension(eventSetting.getBannerImage()));
        }
		eventDesign.setDesc(eventSetting.getDesc());
		eventDesign.setLogoImage(eventSetting.getLogoImage());
		if(imageConfiguration.getBlackLogo() != null && imageConfiguration.getBlackLogo().equals(eventSetting.getLogoImage())) {
            eventDesign.setLogoImageExtension(FilenameUtils.getExtension(eventSetting.getLogoImage()));
        }
		eventDesign.setLogoEnabled(eventSetting.isLogoEnabled());
		eventDesign.setTotalFundRaisedShow(eventSetting.isTotalFundRaisedShow());
		eventDesign.setSocialSharingEnabled(eventSetting.isSocialSharingEnabled());
		eventDesign.setShowOrganizer(eventSetting.isShowOrganizer());
		eventDesign.setSponsorSection(eventSetting.getSponsorSection());
		eventDesign.setEventTagLine(eventSetting.getEventTagLine());
        setDisplayColorConfigThemeWise(eventSetting, eventDesign);
        eventDesign.setHideSponsorSection(eventSetting.isHideSponsorSection());
		eventDesign.setEnableSessionsSpeakers(eventSetting.isEnableSessionsSpeakers());
		VirtualEventSettingsDTO virtualEventSetting = virtualEventService.getVirtualEventSettingsById(event);
        eventDesign.setEnableSessionsSpeakers(isEnableFeature(ENABLE_SESSIONS_SPEAKERS, eventSetting.getEventType()) ||
                virtualEventSetting.isStageEnabled() || virtualEventSetting.isSessionEnabled() || virtualEventSetting.isNetworkingEnabled());
		eventDesign.setTicketingBuyButtonText(this.convertToGson(eventSetting.getTicketingBuyButtonText()));
        eventDesign.setRegisterButtonText(this.convertToGson(eventSetting.getRegisterButtonText()));
		eventDesign.setRaffleBuyButtonText(this.convertToGson(eventSetting.getRaffleBuyButtonText()));
		eventDesign.setTrackingScript(eventSetting.getTrackingScript());
		eventDesign.setVirtualEventScript(eventSetting.getVirtualEventScript());
		eventDesign.setThemeId(eventSetting.getThemeId());
        eventDesign.setEnableAutoAssignedSequence(isEnableFeature(ENABLE_AUTO_ASSIGNED_SEQUENCE, eventSetting.getEventType()) || Boolean.TRUE.equals(autoAssignedAttendeeNumbersService.isAutoAttendeeSequenceAvailable(event)));
		if (null == event.getWhiteLabel() ? eventDesign.isEnableEventType() : eventDesign.isEnableEventType() && event.getWhiteLabel().isEnableEventType()) {
			eventDesign.setEventType(isNotBlank(eventSetting.getEventType()) ? EventType.valueOf(eventSetting.getEventType()) : null);
		}
		eventDesign.setAuctionTabTitle(eventSetting.getAuctionTabTitle());
		eventDesign.setRaffleTabTitle(eventSetting.getRaffleTabTitle());
		eventDesign.setFundANeedTabTitle(eventSetting.getFundANeedTabTitle());
		eventDesign.setDonationTabTitle(eventSetting.getDonationTabTitle());
		eventDesign.setTicketingTabTitle(eventSetting.getTicketingTabTitle());
		eventDesign.setHideGoogleMap(eventSetting.isHideGoogleMap());
		eventDesign.setDonationButtonText(convertDonationButtonStringToGson(eventSetting.getDonationButtonText()));
		eventDesign.setConfigureTabsAsJson(getJson(eventSetting.getConfigureTabsAsJson()));
		eventDesign.setAllowEndedEventAccess(eventSetting.isAllowEndedEventAccess());
        eventDesign.setEventCalendarInvite(eventSetting.getEventCalendarInvite());
        eventDesign.setDisplayTabsColor(eventSetting.getDisplayTabsColor());
        eventDesign.setSpeakerReg(eventSetting.getSpeakerReg());
        eventDesign.setExhibitorReg(eventSetting.getExhibitorReg());
        eventDesign.setReviewerReg(eventSetting.getReviewerReg());
        eventDesign.setEmbeddedCheckoutFormEnabled(eventSetting.isEmbeddedCheckoutFormEnabled());
        eventDesign.setEventCalendarInvite(roConfirmationEmailService.getCalenderInvitation(event));
		this.eventDesignDetailService.save(eventDesign);
		hubspotEventService.updateHubspotEventDetails(event);
        contactModuleSettingsService.updateEventEngageEmailLimit(event);
        updateIntegration(event);
	}

    private void updateCalenderInvitationForCustomTemplates(Event event) {
        List<CustomTemplates> customTemplates = confirmationEmailRepository.findCustomTemplatesByEvent(event.getEventId());
        if(!CollectionUtils.isEmpty(customTemplates)){
            customTemplates.forEach(customTemplate -> {
                customTemplate.setCalendarInvitation(roConfirmationEmailService.getCalenderInvitation(event));
                confirmationEmailRepository.save(customTemplate);
            });
        }
    }

    private void updateIntegration(Event event) {
        Optional<Integration> integrationOptional = Optional.empty();
        if (event.getWhiteLabel() != null) {
            log.info("request for update event details in custom integration WhiteLabel {}", event.getWhiteLabelId());
            integrationOptional = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);

        } else if (event.getOrganizer() != null) {
            log.info("request for update event details in custom integration Organizer {}", event.getOrganizerId());
            integrationOptional = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getOrganizer().getId(), IntegrationSourceType.ORGANIZER, IntegrationType.TRAY_IO);
        }

        integrationOptional.ifPresent(integration -> trayIntegrationService.mapEventDetails(integration, event, TrayIOWebhookActionType.MAP_EVENT));
    }
    private void setDisplayColorConfigThemeWise(EventDesignSettingsDto eventSetting, EventDesignDetail eventDesignDetail){
        if(eventSetting.getThemeId()!= null && !eventSetting.getThemeId().equals(eventDesignDetail.getThemeId())){
            if(eventSetting.getDisplayBackgroundColor().equals(imageConfiguration.getDisplayBackgroundColor(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()))){
                eventDesignDetail.setDisplayBackgroundColor(imageConfiguration.getDisplayBackgroundColor(eventSetting.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()));
            } else {
                eventDesignDetail.setDisplayBackgroundColor(eventSetting.getDisplayBackgroundColor());
            }
            if(eventSetting.getDisplayTextColor().equals(imageConfiguration.getDisplayTextColor(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()))){
                eventDesignDetail.setDisplayTextColor(imageConfiguration.getDisplayTextColor(eventSetting.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()));
            } else {
                eventDesignDetail.setDisplayTextColor(eventSetting.getDisplayTextColor());
            }
            if(eventSetting.getDisplayTabsTextColor().equals(imageConfiguration.getDisplayTabsTextColor(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()))){
                eventDesignDetail.setDisplayTabTextColor(imageConfiguration.getDisplayTabsTextColor(eventSetting.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()));
            } else {
                eventDesignDetail.setDisplayTabTextColor(eventSetting.getDisplayTabsTextColor());
            }
            if(imageConfiguration.getDefaultBanner(eventDesignDetail.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()).equals(eventSetting.getBannerImage())){
                eventDesignDetail.setBannerImage(imageConfiguration.getDefaultBanner(eventSetting.getThemeId(), eventDesignDetail.isThemeWiseDefaultConfig()));
            } else {
                eventDesignDetail.setBannerImage(eventSetting.getBannerImage());
            }
        } else {
            eventDesignDetail.setDisplayBackgroundColor(eventSetting.getDisplayBackgroundColor());
            eventDesignDetail.setDisplayTextColor(eventSetting.getDisplayTextColor());
            eventDesignDetail.setDisplayTabTextColor(eventSetting.getDisplayTabsTextColor());
        }
    }

    public Event getEventByName(String name) {
        List<Event> events = eventRepository.findEventsByName(name);
        if (!CollectionUtils.isEmpty(events)) {
            return events.get(0);
        } else {
            throw new NotFoundException(EventNotFound.EVENT_NOT_FOUND);
        }
    }

    private void updateEventCUstomizeUrlAndLogo(Event event, EventDesignSettingsDto eventSetting, EventChecklist eventChecklist) {

			if (!eventChecklist.isEventURLCustomized() && !eventSetting.getEventUrl().equalsIgnoreCase(event.getEventURL())) {
				eventChecklist.setEventURLCustomized(true);
				this.eventChecklistService.save(eventChecklist);
			}


        if (!eventChecklist.isEventLogoUploaded() && null != eventSetting.getLogoImage() && !imageConfiguration.getBlackLogo().equals(eventSetting.getLogoImage())) {
        eventChecklist.setEventLogoUploaded(true);
        this.eventChecklistService.save(eventChecklist);
}
    }

    private void associateOrganizerWithEvent(Event event, EventDesignSettingsDto eventSetting, User user,EventPlanConfig eventPlanConfig) {
        if((!(LEGACY.getName().equals(eventPlanConfig.getPlanConfig().getPlanName())
                || WHITE_LABEL_LEGACY.getName().equals(eventPlanConfig.getPlanConfig().getPlanName())))){
            chargebeeService.validateAssociateOrganizerWithEvent(event, eventSetting.getOrganizerPageURL());
        }
        if (isNotBlank(eventSetting.getOrganizerPageURL())) {
            log.info("associateOrganizerWithEvent changeAssociationToNewOrg eventId {} ",event.getEventId());
            changeAssociationToNewOrg(event, eventSetting,eventPlanConfig);
        } else {
            if(eventSetting.getOrganizerId()!=0) { // When the new organizer provided is edited
                log.info("associateOrganizerWithEvent updateOrgUrlAndAssociate eventId {} ",event.getEventId());
                chargebeeService.updateOrgUrlAndAssociate(event, eventSetting, user);
            }
        }
    }

    private void changeAssociationToNewOrg(Event event, EventDesignSettingsDto eventSetting,EventPlanConfig eventPlanConfig) {
        // When an organizer exists for the given user email
        log.info("changeAssociationToNewOrg eventId {} organizerByPageUrl {}",event.getEventId(),eventSetting.getOrganizerPageURL());
        Organizer organizerByPageUrl = organizerService.getOrganizerByURL(eventSetting.getOrganizerPageURL());
        String platformConfigJson= eventPlanConfig.getPlatformConfigJson();
        if(organizerByPageUrl != null) {

            event.setOrganizerId(organizerByPageUrl.getId());
            event.setOrganizer(organizerByPageUrl);
            eventPlanConfigService.handleCreateOrUpdatePlanForEvent(event);
            EventPlanConfig newEventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
            log.info("changeAssociationToNewOrg eventId {} newEventPlanConfig {}",event.getEventId(),newEventPlanConfig);
            eventChargeUsagesService.updateVirtualEventHubCustomLabelName(event, newEventPlanConfig.getPlanConfig(), platformConfigJson);
            organizerService.save(organizerByPageUrl);
            Organizer oldOrganizer = event.getOrganizer();
            if (!organizerByPageUrl.equals(oldOrganizer)) {
                // TODO : Chargebee, Handle DEV-8861
                List<StaffDetailDto> staffDetailDtoForWhiteAdmin = new ArrayList<>();
                List<StaffDetailDto> staffDetailDto = staffService.getStaffList(event);
                if (event.getWhiteLabel() != null && null != event.getWhiteLabel()) {
                    staffDetailDtoForWhiteAdmin = staffService.findStaffDtoByWhiteLabel(event.getWhiteLabel());
                }
                staffDetailDto.addAll(staffDetailDtoForWhiteAdmin);
                List<Long> userIds = staffDetailDto.stream().map(StaffDetail::getUserId).collect(Collectors.toList());
                List<User> staffDetail = roUserService.getListOfUsersByUserIds(userIds);
                updateStaffForOrganizer(organizerByPageUrl, oldOrganizer, staffDetailDto, staffDetail);
            }
        }
    }

    private void checkAndSetEventUrl(EventDesignSettingsDto eventSetting,Event event,boolean generateUrlByName) {
        String eventUrl;
        if(eventSetting.isHasEventUrlChanged() && !StringUtils.isBlank(eventSetting.getEventUrl().trim())){
            eventUrl = convertEventUrlBySeparateWords(eventSetting.getEventUrl());
            boolean isExistingEvent = eventRepository.existsByEventURLAndEventIdNot(eventUrl,event.getEventId());
            if (isExistingEvent) {
                throw new ConflictException(UserExceptionConflictMsg.EVENT_URL_ALREADY_EXIST);
            }
            event.setOldEventURL(event.getEventURL());
            event.setEventURL(eventUrl);
        } else if (StringUtils.isBlank(eventSetting.getEventUrl().trim())) {
            eventUrl= eventSetting.getEventName();
            eventUrl = convertEventUrlBySeparateWords(eventUrl);
            event.setOldEventURL(event.getEventURL());
            event.setEventURL(this.getEventUrl(eventUrl, 0, eventUrl.length(), false, null));
        }
        else {
            setEventURL(event,eventSetting,generateUrlByName);
        }
    }

    private void setEventURL(Event event, EventDesignSettingsDto eventSetting, boolean generateUrlByName) {
        if (generateUrlByName && isNotBlank(eventSetting.getEventName()) && !eventSetting.getEventName().equals(event.getName())) {
			String url = eventSetting.getEventName();
            url = convertEventUrlBySeparateWords(url);
            event.setOldEventURL(event.getEventURL());
			event.setEventURL(this.getEventUrl(url, 0, url.length(), false, null));
		} else if (!event.getEventURL().equalsIgnoreCase(eventSetting.getEventUrl())) {
            event.setOldEventURL(event.getEventURL());
			String url = eventSetting.getEventUrl();
            url = convertEventUrlBySeparateWords(url);
            event.setEventURL(this.getEventUrl(url, 0, url.length(), true, null));
		}
    }

    private void updateStaffForOrganizer(Organizer organizer, Organizer oldOrganizer, List<StaffDetailDto> staffDetailDto, List<User> staffDetail) {
        Map<Long, User> userMap = staffDetail.stream().collect(Collectors.toMap(User::getUserId, Function.identity()));
        if (staffDetail.contains(organizer.getCreatedBy())) {
            staffDetailDto.forEach(e -> {
                if (STAFF_ROLES.contains(e.getRole())) {
                    User user = userMap.get(e.getUserId());
                    if (null != oldOrganizer && !oldOrganizer.getCreatedBy().getUserId().equals(e.getUserId())) {
                        organizerService.deleteJoinUsersWithOrganizers(user, oldOrganizer);
                    }
                    if (StaffRole.admin.name().equals(e.getRole())) {
                        organizerService.addJoinUsersWithOrganizers(user, organizer, OrganizerRole.admin, false);
                    }
                }
            });
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.FOUND_UNKNOWN_ORGANIZER);
        }
    }

    private void checkListForEventName(Event event, EventDesignSettingsDto eventSetting, EventChecklist eventChecklist) {
        if (isNotBlank(eventSetting.getEventName())
                && !eventSetting.getEventName().equals(event.getName())) {

            if (!eventChecklist.isNamingCompleted()) {
                eventChecklist.setNamingCompleted(true);
                this.eventChecklistService.save(eventChecklist);
            }
        }
    }

    @Override
    public String getJson(List<VirtualEventTabsDTO> virtualEventTabsDTOS) {
		if(CollectionUtils.isEmpty(virtualEventTabsDTOS)){
			return null;
		}
		Gson gson = new Gson();
		return gson.toJson(virtualEventTabsDTOS);
	}

    /* hide below code as not used, now we are using Chargbee instead of Stripe for module activation
	@Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
	@Override
	public Event activeEventModules(Long eventId, ActiveModulesDto activeModulesDto, User user)
			throws StripeException {

		log.info("activeEventModules for eventId {} by user {}", eventId, user.getUserId());
		Event event = getEventByIdWithoutCache(eventId);
		CountryCode countryCode = getCountryCode(activeModulesDto, event);
		Coupon coupon = validateAndGetCoupon(activeModulesDto, event);
		event.setCouponId(coupon != null ? coupon.getId() : null);

		event = assignPhoneNumberIfAnyFundRaisingModuleActivating(activeModulesDto, event, countryCode);
		log.info("activeEventModules assignedPhoneNumber");
		populateUserNameDetailsFromCard(activeModulesDto, user);

		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
		long numberOfDaysOfEvent = DateUtils.getNumberDaysInclusiveBothTheDatesInEventTimeZone(ticketing.getEventStartDate(), ticketing.getEventEndDate(), event.getEquivalentTimeZone());
		log.info("activeEventModules|eventStartDate|{}||eventEndDate|{}||numberOfDaysOfEvent|{}||" , ticketing.getEventStartDate() , ticketing.getEventEndDate() , numberOfDaysOfEvent);
		List<Object> itemsParams = new LinkedList<>();

		StringBuilder stripeTransactionMsg = new StringBuilder();
		Auction auction = auctionService.findByEvent(event);

		updateVirtualEventSettingsAndUpdateTheItemParamsForExhibitorPro(activeModulesDto, event, itemsParams, stripeTransactionMsg, numberOfDaysOfEvent, user);
		log.info("updateVirtualEventSettingsAndUpdateTheItemParamsForExhibitorPro");

		handleAuctionProduct(activeModulesDto, event, itemsParams, stripeTransactionMsg, auction);
		log.info("handleAuctionProduct");
		handleCauseAuctionProduct(activeModulesDto, event, itemsParams, stripeTransactionMsg);
		log.info("handleCauseAuctionProduct");
		handleRaffleProduct(activeModulesDto, event, itemsParams, stripeTransactionMsg);
		log.info("handleRaffleProduct");
		Staff staff = staffService.findByEventAndUser(event, user, false);
		String customerId = staff == null ? STRING_EMPTY : staff.getStripeCustomerId();

		HandleCustomerAndCard handleCustomerAndCard = new HandleCustomerAndCard(activeModulesDto, user, event, staff, customerId).invoke();
		customerId = handleCustomerAndCard.getCustomerId();
		String cardId = handleCustomerAndCard.getCardId();
		log.info("handleCustomerAndCard");
		HandleTextToGivePlan handleTextToGivePlan = new HandleTextToGivePlan(activeModulesDto, event, stripeTransactionMsg).invoke();
		DonationSettings donationSettings = handleTextToGivePlan.getDonationSettings();
		Plan plan = handleTextToGivePlan.getPlan();

		log.info("handleTextToGivePlan");
		if (INITIAL.equals(event.getAccountActivatedTriggerStatus())) {
			event.setAccountActivatedTriggerStatus(READY_TO_FIRE_TRIGGER);
		}

        boolean isAllowToUpdateTicketingModule =isAllowToUpdateTicketingModule(event, ticketing, activeModulesDto);

        handleTicketingActivation(isAllowToUpdateTicketingModule, event, ticketing, stripeTransactionMsg, cardId);

        if (donationSettings.isTextToGiveActivated() || !itemsParams.isEmpty() || isAllowToUpdateTicketingModule) {
            processOrderAmountToActiveModule(activeModulesDto, user, event, coupon, itemsParams, stripeTransactionMsg, customerId, cardId, plan, false, numberOfDaysOfEvent, isAllowToUpdateTicketingModule);
        }

		log.info("handleTicketingActivation");
		eventRepoService.save(event);
		return event;
	}



    private boolean isAllowToUpdateTicketingModule(Event event, Ticketing ticketing, ActiveModulesDto activeModulesDto) {

        boolean isActivateTicketingPending = event.isTicketingEnabled() && !ticketing.getActivated();
        boolean paidTicket = ticketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event);

        return (!ticketing.getActivated() && activeModulesDto.isTicketingActive())
                || (isActivateTicketingPending && !paidTicket);
    }

    public void handleTicketingActivation(boolean isAllowToUpdateTicketingModule, Event event, Ticketing ticketing, StringBuilder stripeTransactionMsg, String cardId) {


		if (isAllowToUpdateTicketingModule) {
			if(ticketing.isOnlineEvent() && isBlank(cardId)){//NOSONAR

			}
			ticketing.setActivated(true);
			ticketingService.save(ticketing);
			event.setTicketingEnabled(true);
			stripeTransactionMsg.append(",Ticketing activate");
		}
        log.info("In handleTicketingActivation method Activating ticketing module for eventId {} and value of Activated flag {} ",event.getEventId(),ticketing.getActivated());
	}

	public void handleRaffleProduct(ActiveModulesDto activeModulesDto, Event event, List<Object> itemsParams, StringBuilder stripeTransactionMsg) {
		Raffle raffle = raffleService.findByEvent(event);
		if (!raffle.isActivated() && activeModulesDto.isRaffleActive()) {
			raffle.setActivated(true);
			raffleService.save(raffle);
			raffleService.resetRaffleEvent(event, false);
			event.setRaffleEnabled(true);

			Sku sku = stripeService.getRaffleSKU(event);
			stripeTransactionMsg.append(",Opportunity Drawing pkg activate").append(STRING_BLANK).append(sku.getProduct());
			addSkuItemParams(itemsParams, sku);
		}
	}

	public void handleCauseAuctionProduct(ActiveModulesDto activeModulesDto, Event event, List<Object> itemsParams, StringBuilder stripeTransactionMsg) {
		CauseAuction causeAuction = causeAuctionService.findByEvent(event);
		if (!causeAuction.isActivated() && activeModulesDto.isFundANeedActive()) {
			causeAuction.setActivated(true);
			causeAuctionService.save(causeAuction);
			causeAuctionService.resetCauseAuctionEvent(event, false);
			event.setCauseAuctionEnabled(true);

			Sku sku = stripeService.getFundANeedSKU(event);
			stripeTransactionMsg.append(",Causeauctionpkg activate").append(STRING_BLANK).append(sku.getProduct());
			addSkuItemParams(itemsParams, sku);
		}
	}

	public void handleAuctionProduct(ActiveModulesDto activeModulesDto, Event event, List<Object> itemsParams, StringBuilder stripeTransactionMsg, Auction auction) {
		if (!auction.isActivated() && activeModulesDto.isSilentAuctionActive()) {
			auction.setActivated(true);
			auctionService.save(auction);
			auctionService.resetAuctionEvent(event, false);
			event.setSilentAuctionEnabled(true);

			Sku sku = stripeService.getSilentAuctionSKU(event);
			stripeTransactionMsg.append(",Silentauctionpkg activate").append(STRING_BLANK).append(sku.getProduct());
			addSkuItemParams(itemsParams, sku);
		}
	}

	public void addSkuItemParams(List<Object> itemsParams, Sku sku) {
		Map<String, String> item = new HashMap<>();
		item.put("type", "sku");
		item.put("parent", sku.getId());
		itemsParams.add(item);
	}

     */

	public void populateUserNameDetailsFromCard(ActiveModulesDto activeModulesDto, User user) {
		if (isBlank(user.getFirstName())
				&& isNotBlank(activeModulesDto.getNameOnCard())) {
			String[] names = activeModulesDto.getNameOnCard().split(STRING_BLANK);
			user.setFirstName(names.length > 0 ? names[0] : STRING_EMPTY);
			user.setLastName(names.length > 1 ? names[1] : STRING_EMPTY);
			userService.save(user);
		}
	}

	public Event assignPhoneNumberIfAnyFundRaisingModuleActivating(ActiveModulesDto activeModulesDto, Event event, CountryCode countryCode) {
		if (event.getPhoneNumber() == 0
				&& (activeModulesDto.isSilentAuctionActive()
				|| activeModulesDto.isFundANeedActive()
				|| activeModulesDto.isRaffleActive()
				|| activeModulesDto.isTextToGiveActive()
				|| activeModulesDto.isProExhibitorActive())) {
			event = assignEventNumber(event.getEventId(), (apiBaseUrl + STRING_SMS_URL + event.getEventId()), countryCode); //NOSONAR
		}
		return event;
	}

	public Coupon validateAndGetCoupon(ActiveModulesDto activeModulesDto, Event event) {
		String discountCoupon = activeModulesDto.getDiscountCoupon();
		Coupon coupon = null;
		if (isNotBlank(discountCoupon)) {
			if(event.getCouponId() != null){
				throw new NotAcceptableException(NotAceptableExeceptionMSG.COUPON_ALREADY_APPLIED_TO_EVENT);
			}
			coupon = stripeService.getCouponByCouponCode(stripeConfiguration.getAPI_KEY(), discountCoupon);
			if(coupon == null){
				throw new NotFoundException(NotFound.NOT_VALID_COUPON_CODE);
			}
		}
		return coupon;
	}

	public CountryCode getCountryCode(ActiveModulesDto activeModulesDto, Event event) {
		if (event.getCountryCode() == null && isBlank(activeModulesDto.getCountryCode())) {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.COUNTRY_CODE_REQUIRD_TO_ACTIVE_MODULE);
		}
		CountryCode countryCode = event.getCountryCode();
		if (countryCode == null && isNotBlank(activeModulesDto.getCountryCode())) {
			countryCode = getCountryCode(activeModulesDto.getCountryCode());
		}
		return countryCode;
	}

    /* Below code unused , because in Stripe java sdk version 22 remove SKU class.
	public void updateVirtualEventSettingsAndUpdateTheItemParamsForExhibitorPro(ActiveModulesDto activeModulesDto, Event event, List<Object> itemsParams, StringBuilder stripeTransactionMsg, long numberOfDaysOfEvent, User user) {
		if(activeModulesDto.isProExhibitorActive()){
			if(NumberUtils.isNumberGreaterThanZero(activeModulesDto.getNumberOfLiveStreamingExhibitorsPurchased())){

				createEventBillingRecordForBuyingProBooth(activeModulesDto, event, numberOfDaysOfEvent, user);

				Sku sku = stripeService.getExhibitorProSKU(event);

				Long purchasedExhibitorsMultipliesWithNumberOfDays = activeModulesDto.getNumberOfLiveStreamingExhibitorsPurchased() * numberOfDaysOfEvent;

					stripeTransactionMsg.append("ExhibitorPro activate:").append(STRING_BLANK).append(sku.getProduct());
					Map<String, String> item = new HashMap<>();
					item.put(TYPE_LOWER_CASE, "sku");
					item.put(PARENT, sku.getId());
					item.put(QUANTITY_LOWER_CASE,purchasedExhibitorsMultipliesWithNumberOfDays.toString());
					itemsParams.add(item);
				log.info("updateVirtualEventSettingsAndUpdateTheItemParamsForExhibitorPro|purchasedExhibitorsMultipliesWithNumberOfDays {}",purchasedExhibitorsMultipliesWithNumberOfDays);

			}else {
				throw new NotAcceptableException(ExhibitorExceptionMsg.PLEASE_SELECT_NUMBER_OF_EXHIBITORS_TO_PURCHASE_FOR_LIVE_STREAM);
			}
		}
	}

     */

	public void createEventBillingRecordForBuyingProBooth(ActiveModulesDto activeModulesDto, Event event, long numberOfDaysOfEvent, User user) {

		EventBillingAddOns billingAddOns = new EventBillingAddOns();
		billingAddOns.setEventId(event.getEventId());
		billingAddOns.setNumberOfBooths(activeModulesDto.getNumberOfLiveStreamingExhibitorsPurchased());
		billingAddOns.setAddOnType(AddOnType.PRO_BOOTH);
		billingAddOns.setAmount((double) 99*activeModulesDto.getNumberOfLiveStreamingExhibitorsPurchased() * numberOfDaysOfEvent);
		billingAddOns.setPaymentStatus(PaymentStatus.PAID);
		billingAddOns.setRecordEnteredByEmployeeId(user.getUserId());
		billingAddOns.setProBoothRatePerDay((double)99);
		billingAddOns.setNotes("This is added from Activate module page.");
		eventBillingAddOnsService.save(billingAddOns);
	}

	/* hide below code as not used, now we are using Chargbee instead of Stripe for module activation
    private void processOrderAmountToActiveModule(ActiveModulesDto activeModulesDto, User user, Event event, Coupon coupon, List<Object> itemsParams, StringBuilder stripeTransactionMsg, String customerId, String cardId, Plan plan, boolean isVirtual //NOSONAR
            , long numberOfDaysOfEvent, boolean isRequireToCreateStripeTransacton) throws StripeException {
        paymentHandlerService.processOrderAmountToActivateModule(activeModulesDto, user, event, coupon, customerId, cardId, stripeTransactionMsg, itemsParams, plan, true, isVirtual,numberOfDaysOfEvent, isRequireToCreateStripeTransacton);
    }
	 */
	private String callingAddToCustomer(ActiveModulesDto activeModulesDto, boolean isOnlyTextToGiveEnable, String customerId) throws StripeException {//NOSONAR
		String cardId;
		if (!isOnlyTextToGiveEnable) {
			cardId = stripePaymentService.addCardToCustomer(stripeConfiguration.getAPI_KEY(), customerId,
					activeModulesDto.getTokenOrIntentId(), true, true);
		} else {
			cardId = stripePaymentService.addCardToCustomerUsingSetupIntentForSubscription(stripeConfiguration.getAPI_KEY(), customerId,
					activeModulesDto.getTokenOrIntentId());
		}
		return cardId;
	}

	protected CountryCode getCountryCode(String countrycode) {
		try {
			return CountryCode.valueOf(StringUtils.upperCase(countrycode));
		} catch (Exception exce) {
			log.error("getCountryCode", exce);
			throw new NotFoundException(NotFound.NOT_VALID_COUNTRY_CODE);
		}
	}

	@Transactional(isolation = Isolation.READ_COMMITTED)
	@Override
	public void disconnectPaymentGateway(Event event, boolean isSuperAdmin, User user) {
		Stripe stripeByEvent = roStripeService.findByEvent(event);
        boolean isStripeConnectedUser = user != null && (stripeByEvent.getStaff() != null && stripeByEvent.getStaff().getUserId().equals(user.getUserId()));
        log.info("stripe disconnected for event id: {}  , stripe id: {}, isSuperAdmin: {}, isStripeConnectedUser: {}" ,event.getEventId() ,stripeByEvent.getId(), isSuperAdmin, isStripeConnectedUser);
        if (!isSuperAdmin && !isStripeConnectedUser) {
            throw new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.YOU_ARE_NOT_AUTHORIZED_DISCONNECT_PAYMENT_PROCESSOR);
        }
		if (isNotBlank(stripeByEvent.getAccessToken())) {
			if (this.paymentService.isCustomerCreatedForEvent(event)) {
                List<Payment> payments = paymentService.findPaymentsByEventId(event);
                if (payments.isEmpty()){
                    log.info("No payments found for this event{}", event.getEventId());
                } else {
                    payments.forEach(payment -> payment.setRecordStatus(RecordStatus.CANCEL));
                    paymentService.saveAll(payments);
                    log.info("Payments found for this event {} and marking all payments as CANCELLED", event.getEventId());
                }
			}
			stripeByEvent.setDefaultAccount(false);
			stripeService.save(stripeByEvent);
			event.setCreditCardEnabled(false);
			eventRepoService.save(event);
			DonationSettings donationSettings = donationSettingsService.getByEventId(event.getEventId());
			donationSettings.setAbsorbFee(true);
			donationSettingsService.save(donationSettings);
			EventChecklist eventChecklist = this.eventChecklistService.findByEvent(event);
			eventChecklist.setActivatePaymentProcessing(false);
			eventChecklistService.save(eventChecklist);
            // delete saved customer detail from previous connected account
            stripeCustomerService.updateStripeCustomerRecStatus(event, RecordStatus.CANCEL);
		} else {
			throw new NotAcceptableException(NotAceptableExeceptionMSG.STRIPE_ACCOUNT_IS_NOT_CONNECTED_TO_EVENT);
		}
	}

	@Override
	@Transactional
	public String connectStripe(Event event, User user, String accessToken) {
		// This is never null all event must have user
		/*Stripe stripeByEvent = roStripeService.findByEvent(event);//NOSONAR

		try {//NOSONAR
			this.connectEventToStripeAccountDifferedAccount(stripeByEvent, event, user);
		} catch (StripeException e) {
			log.debug("Stripe differed error", e);
			boolean needToConnectWithWeb = true;*/
			// Commenting code to connect using connected or disconnected account, always go through auth flow.
			/*List<Stripe> stripeDisconnectedAccounts = stripeService.getDisconnectedAccount(event);//NOSONAR
			if (stripeDisconnectedAccounts != null && !stripeDisconnectedAccounts.isEmpty()) {//NOSONAR
				needToConnectWithWeb = true;
			} else {
				List<Stripe> connectedAccounts = stripeService.findByEventUserEmail(user.getEmail());
				connectedAccounts = connectedAccounts.stream().filter(cStripe -> EnumPaymentGateway.STRIPE.value().equals(cStripe.getPaymentGateway())).collect(Collectors.toList());
				if (connectedAccounts != null && !connectedAccounts.isEmpty()) {
					Stripe connectedAccount = connectedAccounts.get(0);
					this.saveWithAnotherEventConnectedStripeAccount(connectedAccount, stripeByEvent, event);
				} else {
					needToConnectWithWeb = true;
				}
			}*/
			//if (needToConnectWithWeb) {//NOSONAR

        String url = stripeConfiguration.getAuthorize_URI() + "?response_type=code&scope=read_write&client_id="
                + stripeConfiguration.getClientID() + Constants.CONST_STATE + accessToken;
        if ("local".equalsIgnoreCase(environment) || "dev".equalsIgnoreCase(environment) || "stage".equalsIgnoreCase(environment)) {
            url = stripeConfiguration.getAuthorize_URI() + "?response_type=code&scope=read_write&client_id="
                    + stripeConfiguration.getClientID() + Constants.CONST_STATE + accessToken + "&stripe_user[country]=US";
        }
        if(StringUtils.isNotBlank(url)) {
            hubspotContactService.addContactToCustomCreatedEvent(user, event.getEventId(), STRIPE_NEW, HubspotCustomEventType.INTEGRATION, null, null);
        }
        log.info("authorization url==>{}", url);
        return "{\"stripeConnectUrl\":\"" + url + "\",\"redirecttostripe\":\"true\"}";
			//autoActivateAndEnableTicketing(event);}//NOSONAR
		//}//NOSONAR

	}

    @Override
    public String connectPaypal(Event event, User user, String accessToken) throws JSONException, UnirestException {

        String url = paypalPaymentService.connectMerchantParter();

        log.info("authorization url==>{}", url);
        return "{\"paypalConnectUrl\":\"" + url + "\",\"redirecttopaypal\":\"true\"}";
    }

    @Override
    @Transactional
    public void authorizePaypal(PaypalValidateDTO paypalValidateDTO, Event event, User user) {

        try {

            JSONObject response = paypalPaymentService.authorizeMultiPartyPaypal(paypalValidateDTO);
            log.info("paypal respone==>{}", response.toString());//NOSONAR
            if (response.has(MERCHANT_ID)) {
                Stripe stripeByEvent = stripeService.findByEventAndPaymentGateway(event, EnumPaymentGateway.PAYPAL);
                stripeByEvent.setPaymentGateway(EnumPaymentGateway.PAYPAL.value());
                this.saveStripeAccountOfCustomerForEvent(stripeByEvent, response.getString(MERCHANT_ID), response, event, user);
                autoActivateAndEnableTicketing(event);
            } else {
                throw new NotAcceptableException(NotAceptableExeceptionMSG.STRIPE_ACCOUNT_CONNECT_ERROR_IN_CONNECT);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
	@Transactional
	public String connectSquare(Event event, User user, String accessToken) {
		if (event.getWhiteLabel() != null  && transactionFeeConditionalLogicService.isWlAFeeExists(event)) {
            log.info("connectSquare, WL A fee is exist for event {}", event.getEventId());
			throw new NotAcceptableException(SQUARE_CONNECT_NOT_AVAILABLE_FOR_WL);
		}

		Stripe squareByEvent = stripeService.findByEventWithEmailDisplayName(event);
		if (squareByEvent.getEmail() != null
				&& !EnumPaymentGateway.SQUARE.value().equals(squareByEvent.getPaymentGateway())) {
			throw new NotAcceptableException(CONNECTED_TO_OTHER_PAYMENT_GATEWAY);
		}

		String url = null;
		try {
			url = squareConfiguration.getAuthorize_URI() + "?client_id=" + squareConfiguration.getClientID()
					+ "&scope=" + URLEncoder.encode(SQUARE_SCOPE, "UTF-8") + Constants.CONST_STATE + accessToken;
		} catch (UnsupportedEncodingException e) {
			log.error("Error", e);
		}
		log.info("authorization url==>{}", url);
        if(StringUtils.isNotBlank(url)) {
            hubspotContactService.addContactToCustomCreatedEvent(user, event.getEventId(), SQUARE_NEW, HubspotCustomEventType.INTEGRATION, null ,null);
        }
		return "{\"stripeConnectUrl\":\"" + url + "\",\"redirecttostripe\":\"true\"}";
	}

	@Override
	@Transactional
	public void authorizeStripe(String code, Event event, User user, Stripe stripeByEvent) throws IOException, URISyntaxException {
        log.info("authorizeStripe eventId {} userId {} date {} ",event!=null?event.getEventId():null,user!=null?user.getUserId():null, new Date());
		try {
			if (stripeService.findByCode(code) == null) {
				// Grab stripe_user_id (use this to authenticate as the
				// connected account)
//               HttpResponse<JsonNode> jsonResponse = Unirest.post(stripeConfiguration.getToken_URI())
//						.queryString("client_secret", stripeConfiguration.getAPI_KEY())
//						.queryString("grant_type", "authorization_code")
//						.queryString("client_id", stripeConfiguration.getClientID())
//						.queryString("code", code)
//						.asJson();
//
//				// Grab stripe_user_id (use this to authenticate as the connected account)
//				JSONObject response = jsonResponse.getBody().getObject();
//				log.info("stripe respone==>{}" , response.toString());//NOSONAR

                RequestOptions requestOptions = RequestOptions.builder().setApiKey(stripeConfiguration.getAPI_KEY()).build();
                Map<String, Object> params = new HashMap<>();
                params.put(GRANT_TYPE, AUTHORIZATION_CODE);
                params.put(CODE, code);
                params.put(CLIENT_ID, stripeConfiguration.getClientID());
                TokenResponse tokenresponse = OAuth.token(params, requestOptions);
                JSONObject response = new JSONObject(tokenresponse.getRawJsonObject().toString());
                log.info("stripe response ==>{}" , response.toString());//NOSONAR

				if (response.has(ACCESS_TOKEN)) {
					stripeByEvent.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
					this.saveStripeAccountOfCustomerForEvent(stripeByEvent, code, response, event, user);
					autoActivateAndEnableTicketing(event);
				} else {
					throw new NotAcceptableException(NotAceptableExeceptionMSG.STRIPE_ACCOUNT_CONNECT_ERROR_IN_CONNECT);
				}
			} else {
				throw new ConflictException(UserExceptionConflictMsg.EVENT_STRIPE_ACCOUNT_CONNECT_TOKEN_ALREADY_USED);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

	}

	@Override
	@Transactional
	public void authorizeSquare(String code, Event event, User user)
			throws IOException, URISyntaxException, com.squareup.square.exceptions.ApiException, UnirestException, JSONException {
        if (stripeService.findByCode(code) == null) {

            // Make /oauth/token endpoint POST request
            HttpResponse<JsonNode> jsonResponse = Unirest.post(squareConfiguration.getToken_URI())
                    .queryString("client_secret", squareConfiguration.getAppSecretKey())
                    .queryString("client_id", squareConfiguration.getClientID())
                    .queryString("grant_type", "authorization_code")
                    .queryString("code", code).asJson();
            JSONObject resp = jsonResponse.getBody().getObject();

            if (resp.has(ACCESS_TOKEN)) {
                Stripe stripeByEvent = roStripeService.findByEvent(event);
                stripeByEvent.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
                this.saveStripeAccountOfCustomerForEvent(stripeByEvent, code, resp, event, user);
                autoActivateAndEnableTicketing(event);
            } else {
                throw new NotAcceptableException(NotAceptableExeceptionMSG.STRIPE_ACCOUNT_CONNECT_ERROR_IN_CONNECT);
            }
        } else {
            throw new ConflictException(UserExceptionConflictMsg.EVENT_STRIPE_ACCOUNT_CONNECT_TOKEN_ALREADY_USED);
        }
	}

    @Override
	public void autoActivateAndEnableTicketing(Event event) {
		Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
		if (Boolean.TRUE.equals(event.getTicketingEnabled() && !ticketing.getActivated()) && !ticketing.isOnlineEvent()) {
			ticketing.setActivated(true);
			ticketingService.save(ticketing);
			event.setTicketingEnabled(true);
			eventRepoService.save(event);
		}
        log.info("In autoActivateAndEnableTicketing method Activating ticketing module for eventId {} and value of Activated flag {} ",event.getEventId(),ticketing.getActivated());
	}

	@Override
	@Transactional
	public Event updateEventUrl(String eventUrl, Event event, User user) {
		event.setOldEventURL(event.getEventURL());

        eventUrl = convertEventUrlBySeparateWords(eventUrl);
		event.setEventURL(eventUrl.equals(event.getEventURL()) ? eventUrl : this.getEventUrl(eventUrl, 0, eventUrl.length(), true, user));
		eventRepoService.save(event);
        updateCalenderInvitationForCustomTemplates(event);

		if (isNotBlank(eventUrl) && !eventUrl.equals(event.getName())) {
			EventChecklist eventChecklist = eventChecklistService.findByEvent(event);

			if (!eventChecklist.isEventURLCustomized()) {
				eventChecklist.setEventURLCustomized(true);
				this.eventChecklistService.save(eventChecklist);
			}
		}
		hubspotEventService.updateHubspotEventDetails(event);
        eventRepoService.clearCacheEventDetailsProcess(event.getOldEventURL());
		return event;
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public boolean isRaffleTicketAvailble(Event event) {
		Raffle raffle = raffleService.findByEvent(event);
		if (raffle != null) {
			long availableTickets = raffle.getAvailableTickets();
			if (availableTickets > 0) {
				long totalTicketsSale = this.purchasedRaffleTicketService.getTotalTicketsByRaffle(raffle.getId());
				return availableTickets - totalTicketsSale > 0;
			} else {
				return true;
			}
		} else {
			return false;
		}
	}

	@Override
	@Transactional
	public void enableBidderCreditCardRegistration(Event event, Boolean enable) {
		if (Boolean.TRUE.equals(enable)) {
			List<Payment> payments = this.paymentService.findByEventIdOrderByUserIdAsc(event.getEventId());
			if (payments != null && !payments.isEmpty()) {
				long nextCount = 0;
				BidderNumber lastBidderNumber = this.bidderNumberService.findOneByEventIdDesc(event);
				if (lastBidderNumber != null) {
					nextCount = nextCount + lastBidderNumber.getBidderNumber();
				}
				for (Payment payment : payments) {
					if (this.bidderNumberService.findByEventAndUser(event, payment.getUserId()) == null) {
						BidderNumber bidderNumber = new BidderNumber(payment.getUserId(), event.getEventId(),
								++nextCount, payment.getTokenCreatedTime(), ONLINE, null);
						this.bidderNumberService.save(bidderNumber);
					}
				}
			}
		}
	}

	@Override
	@Transactional(isolation = Isolation.READ_COMMITTED)
	public void enableCustomMessaging(Event event, Boolean enable) {
		if (Boolean.TRUE.equals(enable)) {
			this.raffleCustomSmsService.saveEventMessages(event);
			this.auctionCustomSmsService.saveEventMessages(event);
			this.causeAuctionCustomSmsService.saveEventMessages(event);
			this.donationCustomSmsService.saveEventMessages(event);
		} else {
			auctionCustomSmsService.removeEventFromCache(event.getEventId());
			causeAuctionCustomSmsService.removeEventFromCache(event.getEventId());
			raffleCustomSmsService.removeEventFromCache(event.getEventId());
			donationCustomSmsService.removeEventFromCache(event.getEventId());
		}
	}

	@Override
	public List<EventBillingDto> getEventsBillingInfo(Date endate) {
		List<EventBillingDto> eventBillings = new ArrayList<>();

		List<Object[]> billingList = eventRepository.findBillingForEvent(endate);
		if (billingList != null && !billingList.isEmpty()) {
			for (Object[] objects : billingList) {
				eventBillings.add(new EventBillingDto(objects));
			}
		}
		return eventBillings;
	}

	@Override
	@Transactional
	public void updateEventModules(Event event, Boolean auctionEnabled, Boolean raffleEnabled, Boolean causeEnabled,
                                   Boolean ticketingEnabled, Boolean textToGiveEnabled, Boolean donationEnabled,User user) {
		if (Boolean.TRUE.equals(ticketingEnabled)) {
            HostEventOfflinePaymentConfig optionalEntity = hostEventOfflinePaymentConfigService.findByEventId(event.getEventId());
            if (null==optionalEntity) {
                hostEventOfflinePaymentConfigService.save(new HostEventOfflinePaymentConfig(event.getEventId()));
            }
			Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
            ticketing.setOfflinePayment(false);
			ticketingTypeTicketService.createFreeAdmissionTicketIfNotExists(ticketing, event);
			if(!ticketing.getActivated()){
                ticketing.setActivated(true);
                ticketingService.save(ticketing);
			}
            createDefaultCustomTemplates(event, user);
		}
		event.setSilentAuctionEnabled(auctionEnabled);
		event.setRaffleEnabled(raffleEnabled);
		event.setCauseAuctionEnabled(causeEnabled);
		event.setTicketingEnabled(ticketingEnabled);
		event.setTextToGiveEnabled(textToGiveEnabled);
		event.setDonationEnabled(donationEnabled);
		eventRepoService.save(event);
	}

    @Override
    public void createDefaultCustomTemplates(Event event, User user) {
        CustomTemplates templates=confirmationPagesService.findByEventAndIsDefaultPage(event, true);
        if(templates==null){
            confirmationPagesService.createConfirmationPage(event,user.getUserId(),null,true);
            log.info("default confirmation page created from the update event modules eventId: {} and userId: {}", event.getEventId(), user.getUserId());
        }
        CustomTemplates customEmailTemplates=confirmationEmailService.findByEventAndIsDefaultEmail(event, true,TemplateType.CONFIRMATION_EMAIL);
        if(customEmailTemplates==null){
            confirmationEmailService.createCustomEmailTemplate(event,user.getUserId(),null,true,TemplateType.CONFIRMATION_EMAIL);
            log.info("default confirmation email created from the update event modules eventId: {} and userId: {}", event.getEventId(), user.getUserId());
        }
        CustomTemplates reminderEmailTemplates=confirmationEmailService.findByEventAndIsDefaultEmail(event, true,TemplateType.REMINDER_EMAIL);
        if(reminderEmailTemplates==null){
            confirmationEmailService.createCustomEmailTemplate(event,user.getUserId(),null,true,TemplateType.REMINDER_EMAIL);
            log.info("default reminder email created from the update event modules eventId: {} and userId: {}", event.getEventId(), user.getUserId());
        }
        CustomTemplates saveASeatEmailTemplates=confirmationEmailService.findByEventAndIsDefaultEmail(event, true,TemplateType.SESSION_SAVE_A_SEAT_CONFIRMATION_EMAIL);
        if(saveASeatEmailTemplates==null){
            confirmationEmailService.createCustomEmailTemplate(event,user.getUserId(),null,true,TemplateType.SESSION_SAVE_A_SEAT_CONFIRMATION_EMAIL);
            log.info("default save a seat email created from the update event modules eventId: {} and userId: {}", event.getEventId(), user.getUserId());
        }

        CustomTemplates ticketPdfDesignTemplate = confirmationEmailService.findByEventAndIsDefaultEmail(event, true,TemplateType.TICKET_PDF_DESIGN);
        if(ticketPdfDesignTemplate==null){
            customTicketInvoiceDesignService.createCustomDesign(event,user.getUserId(),new CustomTemplatesDto(),true,TemplateType.TICKET_PDF_DESIGN);
            log.info("default ticket pdf design page created from the update event modules eventId: {} and userId: {}", event.getEventId(), user.getUserId());
        }

        CustomTemplates ticketInvoiceDesignTemplate = confirmationEmailService.findByEventAndIsDefaultEmail(event, true,TemplateType.INVOICE_PDF_DESIGN);
        if(ticketInvoiceDesignTemplate==null){
            customTicketInvoiceDesignService.createCustomDesign(event,user.getUserId(),new CustomTemplatesDto(),true,TemplateType.INVOICE_PDF_DESIGN);
            log.info("default invoice pdf design page created from the update event modules eventId: {} and userId: {}", event.getEventId(), user.getUserId());
        }

    }

	@Override
	public List<UserStripeSubscription> getSubscribedStripePlans(User user) {
		List<Long> eventIds = this.paymentService.findDistinctEventIdByUserId(user.getUserId());
		List<UserStripeSubscription> stripeSubscriptions = new ArrayList<>();
		if (eventIds != null) {
			for (long eventId : eventIds) {
				UserStripeSubscription userStripeSubscription = new UserStripeSubscription();
				Event event = roEventService.getEventById(eventId);
				userStripeSubscription.setEventUrl(event.getEventURL());
				userStripeSubscription.setSubscriptions(getSubscriptionPlansForUser(event, user));
				stripeSubscriptions.add(userStripeSubscription);
			}
		}
		return stripeSubscriptions;
	}

	@Override
	public List<StripeSubscriptionDto> getSubscriptionPlansForUser(Event event, User user) {

		Stripe stripe = roStripeService.findByEvent(event);
		Optional<Payment> paymentOptional = this.paymentService.findByUserIdAndEventId(user.getUserId(),
				event.getEventId());

		List<StripeSubscriptionDto> subscriptionDtos = new ArrayList<>();
		if (paymentOptional.isPresent() && stripe != null) {
			String customerId = paymentOptional.get().getStripeToken();
			String apiKey = stripe.getAccessToken();
			if (isNotBlank(customerId) && isNotBlank(apiKey) && EnumPaymentGateway.STRIPE.name().equals(stripe.getPaymentGateway())) {
				try {
					subscriptionDtos = this.stripePaymentService.getCustomerSubscriptions(apiKey, customerId, event.getCurrency().getSymbol());
				} catch (StripeException stripeException) {
					log.error(STRIPE_EXCEPTION, stripeException);
				}
			}
		}
		return subscriptionDtos;
	}


	@Override
	@Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
	public String unSubscribeTextToGive(String subscriptionId, Event event) throws StripeException {
        log.info("EventServiceImpl | unSubscribeTextToGive | subscriptionId {} eventId {}",subscriptionId,event.getEventId());
		return this.stripeService.unSubscribeStripePlan(subscriptionId);
	}

	@Override
	public HostActiveButtonSettings getShowButtonSetting(Event event,User user,Map<String,String> languageMap) {
		HostActiveButtonSettings hostActiveButtonSettings = new HostActiveButtonSettings();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());

		Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
		Auction auction = auctionService.findByEvent(event);
		Raffle raffle = raffleService.findByEvent(event);
		CauseAuction causeAuction = causeAuctionService.findByEvent(event);
		DonationSettings donationSettings = donationSettingsService.getByEventId(event.getEventId());

		boolean isActivateTicketingPending = event.isTicketingEnabled() && !ticketing.getActivated();
		boolean isActivateAuctionPending = event.isSilentAuctionEnabled() && !auction.isActivated();
		boolean isActivateRafflePending = event.isRaffleEnabled() && !raffle.isActivated();
		boolean isActivateCauseAuctionPending = event.isCauseAuctionEnabled() && !causeAuction.isActivated();
		boolean isActivateTextToGivePending = event.isTextToGiveEnabled() && (donationSettings != null && !donationSettings.isTextToGiveActivated());

        String message = setStripePaymentMessage(event, isActivateTicketingPending, user,languageMap);

        setHostSettingsForOnlineEvent(hostActiveButtonSettings, ticketing, isActivateTicketingPending);
        if (isActivateAuctionPending || isActivateRafflePending || isActivateCauseAuctionPending || isActivateTextToGivePending) {
			hostActiveButtonSettings.setRedirectURL(HOST_SETTINGS_ACCOUNT);
			hostActiveButtonSettings.setShowActivateButton(true);
		} else {
			if ((isActivateTicketingPending || event.isDonationEnabled()) && !isAnyBaseModuleEnabled(event)) {
				boolean stripeConnectRequiredForTicketing = event.isDonationEnabled() || roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event);
                setHostActiveButtonSettings(event, hostActiveButtonSettings, message, stripeConnectRequiredForTicketing);
            } else if (isActivateTicketingPending) {
				hostActiveButtonSettings.setShowActivateButton(true);
				hostActiveButtonSettings.setOpenTicketingPopup(true);
				hostActiveButtonSettings.setPopupMessage(resourceBundle.getString(languageMap.get(POPUP_ACTIVATE_TICKETING_MODULE)));
			}
		}

		return hostActiveButtonSettings;
	}

    private void setHostSettingsForOnlineEvent(HostActiveButtonSettings hostActiveButtonSettings, Ticketing ticketing, boolean isActivateTicketingPending) {
        if (!isActivateTicketingPending && ticketing.isOnlineEvent()){
            hostActiveButtonSettings.setShowActivateButton(true);
            hostActiveButtonSettings.setRedirectURL(HOST_SETTINGS_ACCOUNT);
        }
    }

    private void setHostActiveButtonSettings(Event event, HostActiveButtonSettings hostActiveButtonSettings, String message, boolean stripeConnectRequiredForTicketing) {
        if (stripeConnectRequiredForTicketing) {
            Stripe stripe = roStripeService.findByEvent(event);
            if (null != stripe && null != stripe.getId()) {
                hostActiveButtonSettings.setShowActivateButton(false);
                hostActiveButtonSettings.setOpenTicketingPopup(false);
            } else {
                hostActiveButtonSettings.setRedirectURL("/host/settings/credit-card");
                hostActiveButtonSettings.setShowActivateButton(true);
                hostActiveButtonSettings.setOpenCreditCardPopup(true);
                hostActiveButtonSettings.setPopupMessage(message);
            }
        } else {
            hostActiveButtonSettings.setShowActivateButton(true);
            hostActiveButtonSettings.setOpenTicketingPopup(false);
hostActiveButtonSettings.setRedirectURL(HOST_SETTINGS_ACCOUNT);
        }
    }

    private String setStripePaymentMessage(Event event, boolean isActivateTicketingPending,User user,Map<String,String> languageMap) {
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(user, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        String message = resourceBundle.getString(languageMap.get(POPUP_FINISH_STRIPE_PAYMENTS));
        if (isActivateTicketingPending && !event.isDonationEnabled()) {
            message = resourceBundle.getString(languageMap.get(POPUP_FINISH_STRIPE_PAYMENTS_FOR_SELLING));
        } else if (!isActivateTicketingPending && event.isDonationEnabled()) {
            message = resourceBundle.getString(languageMap.get(POPUP_FINISH_STRIPE_PAYMENTS_FOR_DONATIONS));
        }
        return message;
    }

    protected boolean isAnyBaseModuleEnabled(Event event) {
		return event.isSilentAuctionEnabled() || event.isRaffleEnabled() || event.isCauseAuctionEnabled() || event.isTextToGiveEnabled();
	}

	@Override
	@Transactional
	public void activateTicketing(Event event, boolean activate) {
		Ticketing ticketing = getTicketing(event);
		if(ticketing.isOnlineEvent()) {
				ticketing.setActivated(isNotBlank(virtualEventService.getCustomerId(event)));
		} else {
			ticketing.setActivated(activate);
		}
        log.info("In activateTicketing method Activating ticketing module for eventId {} and value of Activated flag {} ",event.getEventId(),ticketing.getActivated());
		ticketingService.save(ticketing);
	}

	@Override
	@Transactional
	public ResponseDto deleteEvent(Long eventId, User user, DeletedEventsDto deletedEventsDto) {//NOSONAR
		Event event = roEventService.findEventByEventId(eventId);
		if(null != event){
            validateEventStatusIsNotPublished(event);
			event.setOldEventURL(event.getEventURL());
			event.setEventURL(event.getEventURL().concat("_"+System.currentTimeMillis()));
			event.setName(event.getName().concat("_"+System.currentTimeMillis()));
			event.setEventStatus(EventStatus.EVENT_DELETED);

            DeletedEvents deletedEventDetails = new DeletedEvents(deletedEventsDto, user, event);
            hubspotEventService.updateEventToDeleteInHubspot(event, deletedEventDetails, user);
			eventRepoService.save(event);
			waitListSettingService.updateStatusDeleted(eventId,Status.DELETED);
			waitListService.updateStatusDeleted(eventId, WaitListStatus.DELETED);
            eventDesignDetailRepoServices.updateRecStatus(eventId,RecordStatus.DELETE);
            ticketingService.updateRecStatus(eventId,RecordStatus.DELETE);
            donationSettingsService.updateDonationSettingRecordStatus(eventId,RecordStatus.DELETE);
            virtualEventService.updateRecStatusOfVirtualEventSetting(eventId,RecordStatus.DELETE);
            transactionFeeConditionalLogicService.updateRecStatusOfTransactionFeeConditionalLogicForDeletedEvent(eventId,RecordStatus.DELETE);
            kioskRegistrationSettingService.deleteKioskRegistrationSetting(eventId,user);
            deletedEventsRepository.save(deletedEventDetails);
			intercomDetailsService.updateEventToDeletedInIntercom(event, deletedEventDetails);

            StripeTransaction stripeTransaction = stripeTransactionService.getBySourceGroupByEvent(event, StripeTransactionSource.MODULE_ACTIVE);
           if (null != stripeTransaction && isNotBlank(stripeTransaction.getTextToGiveSubscriptionId())){
                try {
                    stripePaymentService.unSubscribeStripePlanToCustomer(stripeConfiguration.getAPI_KEY(), stripeTransaction.getTextToGiveSubscriptionId(), false);
                } catch (StripeException e) {
                    e.printStackTrace();
                }
            } else {
               List<ChargebeeTransaction> chargebeeTransactionList = chargebeeBillingService.getTextToGiveSubscriptionId(event);
               ChargebeeTransaction chargebeeTransaction = chargebeeTransactionList != null && !chargebeeTransactionList.isEmpty() ? chargebeeTransactionList.get(chargebeeTransactionList.size() - 1) : null;
               if (chargebeeTransaction != null && isNotBlank(chargebeeTransaction.getTextToGiveSubscriptionId())) {
                   try {
                       chargebeeBillingService.unsubscribeTextToGiveFromChargebee(chargebeeTransaction.getTextToGiveSubscriptionId(), false);
                   } catch (Exception e) {
                       e.printStackTrace();
                   }
               }
            }
            eventRepoService.postDeleteEventProcess(event);
            getStreamService.deleteEventChannels(virtualEventPortalService.getStreamChannelIds(user, event, true, false, false), event.getEventId());
            staffService.deleteStaffByEventId(eventId);
            userService.deleteUserRoleIfNoEventRemains(user);
            eventLevelSettingService.updateRecStatusOfEventLevelSetting(eventId,RecordStatus.DELETE);
            userService.resetUsersMostRecentEventAsZero(event);
			entryExitSettingService.deleteEntryExitSettings(user, event);
			return new ResponseDto(SUCCESS, EVENT_DELETED_SUCCESSFULLY);
		}else {
			throw new NotFoundException(EventNotFound.EVENT_NOT_FOUND);
		}
	}

    private void validateEventStatusIsNotPublished(Event event) {
        if (EventListingStatus.PUBLISHED.equals(event.getEventListingStatus())) {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.YOU_MUST_UNPUBLISH_EVENT_BEFORE_DELETE_IT);
        }
    }

    // This method is deprecated and should be removed, as we use getGraphDetailsForTicketNetSales, getGraphDetailsForTicketSoldBetweenDates
    @Deprecated
    @Override
	public List<TicketGraphDetail> getGraphForTicketSoldBetweenDates(Event event, Date from, Date to, Long recurringEventId, DataType dataType) {
		return this.ticketingStatisticsService.getGraphDetailsForTicketSoldBetweenDatesWithNetSales(event, from, to, recurringEventId, dataType);
	}

    @Override
    public List<TicketGraphDetail> getGraphDetailsForTicketNetSales(Event event, Long recurringEventId) {
        return this.ticketingStatisticsService.getGraphDetailsForTicketNetSales(event, recurringEventId);
    }

    @Override
    public List<TicketGraphDetail> getGraphDetailsForTicketSoldBetweenDates(Event event, Long recurringEventId, Date fromDate, Date toDate) {
        return this.ticketingStatisticsService.getGraphDetailsForTicketSoldBetweenDates(event, recurringEventId, fromDate, toDate);
    }

	@Override
	@Transactional
	public void changeSeat(Long eventId, Long eventTicketingId, String newSeatNumber) {
        // If seating chart is not labeled and admin try to change seat from edit ticketing order page then seatsIo API not accepting Question_mark(?) as new seat number and throwing exception.
        // That's why we are checking and throw exception with proper error message if new seat number contains question mark(?)

        if (newSeatNumber.contains("?")){
            log.info("Seating chart is not labeled that's why seatsIo API not accepting Question_Mark as new seat number {} in the TicketingId {}. Please make sure to all seats are labeled in the seating chart in the event{}", newSeatNumber, eventTicketingId, eventId);
            throw new NotAcceptableException(NotAceptableExeceptionMSG.SEATS_ARE_NOT_LABELED);
        }
		this.ticketingService.changeSeat(eventId, eventTicketingId, newSeatNumber);
	}

	@Override
	public DataTableResponse getAllEventAnalytics(int page,
												  int size,
												  String searchString,
												  String sortField, String sortDirection, Date from, Date to, boolean pastEvent,
												  String whiteLabelUrl) {
		DataTableResponse dataTableResponse = new DataTableResponse();

		Date prevDateTo = DateUtils.addDaysWithoutTime(from, -1);
		Date prevDateFrom = DateUtils.addDaysWithoutTime(prevDateTo, -(int) DateUtils.getDaysBetween(from, to));

		List<Object[]> eventData = (isNotBlank(searchString)) ? eventRepository.getEventsByEventNameAndEventEndDateForAnalytic(searchString, pastEvent,whiteLabelUrl) :  eventRepository.getEventsByEventEndDateForAnalytic(pastEvent,whiteLabelUrl);

		List<Long> eventIds = eventData.stream().map(e -> ((BigInteger) e[0]).longValue()).collect(Collectors.toList());
		List<TicketAnalytics> analytics = eventIds.isEmpty() ? Collections.emptyList() : eventTicketsRepository.getTicketingAnalyticsData(Arrays.asList(TicketStatus.CANCELED, TicketStatus.DELETED), from, to, prevDateFrom, prevDateTo, eventIds);
		List<TicketAnalyticsDto> finalPageData = new ArrayList<>();
		eventData.forEach(event -> {
			TicketAnalyticsDto ticketAnalyticsDto = new TicketAnalyticsDto(((BigInteger) event[0]).longValue(), (String) event[1], (String) event[2], (Date) event[3]);
			addTicketAnalytics(analytics, ticketAnalyticsDto);
			finalPageData.add(ticketAnalyticsDto);
		});
		List<TicketAnalyticsDto> pageData = getSortablePageForAnalytics(page, size, sortField, sortDirection, finalPageData);
		dataTableResponse.setRecordsTotal(finalPageData.size());
		dataTableResponse.setRecordsFiltered(pageData.size());
		dataTableResponse.setData(pageData);
		return dataTableResponse;
	}

	protected void addTicketAnalytics(List<TicketAnalytics> analytics, TicketAnalyticsDto ticketAnalyticsDto) {
		Optional<TicketAnalytics> optionalTicketAnalytics = analytics.stream().filter(e -> e.getEventId() == ticketAnalyticsDto.getEventId()).findFirst();
		TicketAnalytics ticketAnalytics = optionalTicketAnalytics.orElseGet(TicketAnalytics::new);

		ticketAnalyticsDto.setTotalTicketSold(ticketAnalytics.getTotalTicketSold());
		ticketAnalyticsDto.setTotalSaleOverPeriod(ticketAnalytics.getTotalTicketSoldOverPeriod());
		ticketAnalyticsDto.setChangeOverPreviousPeriod(ticketAnalytics.getChangeOverPreviousPeriod());
		ticketAnalyticsDto.setTotalSales(ticketAnalytics.getTotalSales());
		ticketAnalyticsDto.setTotalSaleOverPeriod(ticketAnalytics.getTotalSaleOverPeriod());
	}

	@Override
    @Transactional
    public String liveAuctionCharges(User user, Event event, StaffLiveAuctionPurchaseDto staffLiveAuctionPurchaseDto, boolean isvounteerbid, String paymentType, User staffUser,Map<String, String> languageMap) throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
		List<LiveItemsCodeAndAmountDto> liveItemsCodeAndAmountDtos = staffLiveAuctionPurchaseDto.getLiveItemsCodeAndAmountDtos();
		String message = null;
		for (LiveItemsCodeAndAmountDto liveItemsCodeAndAmountDto : liveItemsCodeAndAmountDtos) {
			AuctionPurchaseDto auctionPurchaseDto = new AuctionPurchaseDto();
			auctionPurchaseDto.setAmount(liveItemsCodeAndAmountDto.getAmount());
			auctionPurchaseDto.setItemCode(liveItemsCodeAndAmountDto.getItemCode());
			auctionPurchaseDto.setCountryCode(staffLiveAuctionPurchaseDto.getCountryCode());
			auctionPurchaseDto.setPhoneNumber(staffLiveAuctionPurchaseDto.getCellNumber());
			auctionPurchaseDto.setTokenOrIntentId(staffLiveAuctionPurchaseDto.getTokenOrIntentId());
			message = this.auctionCharges(user, event, auctionPurchaseDto, isvounteerbid, paymentType, staffUser, true,languageMap);
		}
		return message;
	}

	public List<TicketingTypeGraphDetail> getTicketTypeGraphDetails(Event event, Long recurringEventsId, DataType dataType) {
		return this.ticketingStatisticsService.getGraphForNumberOfTicketSoldByTicketType(event, recurringEventsId, dataType);
	}

	@Override
	@Transactional
	public ResponseDto hideEvent(String eventUrl) {
		Event event = eventRepository.findEventByEventURL(eventUrl);
		if (event != null) {
			if (!auctionBidService.findByAuctionIdAndRefunded(event.getAuctionId(), false).isEmpty()) {
				throw new NotAcceptableException(EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN);
			} else if (!purchasedRaffleTicketService.getAllPurchasedTicketsForRaffle(event.getRaffleId()).isEmpty()) {
				throw new NotAcceptableException(EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN);
			} else if (!pledgeService.findAllPledgeForCauseAuction(event.getCauseAuctionId()).isEmpty()) {
				throw new NotAcceptableException(EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN);
			} else if (!ticketingOrderRepoService.findByEventId(event).isEmpty()) {
				throw new NotAcceptableException(EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN);
			} else {
				Auction auction = auctionService.findByEvent(event);
				auction.setEndDate(currentDateInUTC());
				auction.setLastExtendedEndTime(currentDateInUTC());

				Raffle raffle = raffleService.findByEvent(event);
				raffle.setEndDate(currentDateInUTC());

				CauseAuction causeAuction = causeAuctionService.findByEvent(event);
				causeAuction.setEndDate(currentDateInUTC());

				Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
				ticketing.setEventEndDate(currentDateInUTC());
				ticketingService.save(ticketing);
				return new ResponseDto(Constants.SUCCESS, "Event successfully marked as hidden");
			}
		} else {
			throw new NotFoundException(EventNotFound.EVENT_NOT_FOUND);
		}
	}

	@Override
	public Event findMostRecentEventByUser(User user) {
		List<Event> eventList = staffService.findEventsByUserAndRole(user, StaffRole.admin);
		eventList = eventList.stream().filter(e-> !(EventStatus.EVENT_DELETED.equals(e.getEventStatus()))).collect(Collectors.toList());
		eventList.sort(Comparator.comparing(Event::getCreatedDate).reversed());
		return !eventList.isEmpty() ? eventList.get(0) : null;
	}

	@Override
	public Ticketing findTicketingByEvent(Event event) {
		return ticketingHelperService.findTicketingByEvent(event);
	}

	protected Date currentDateInUTC() {
		return TimeZoneUtil.getDateInUTC(DateUtils.getCurrentDate(), "Universal Coordinated Time (UTC)");
	}


	private String convertToGson(BuyButtonDesignDto ticketingBuyButtonStyle) {
		Gson gson = new Gson();
		if(null == ticketingBuyButtonStyle) {
			return null;
		}
		return gson.toJson(ticketingBuyButtonStyle, BuyButtonDesignDto.class);
	}

	@Override
    @Transactional
	public void changeEventListingMode(String eventListingStatus, Event event, User user, boolean isSuperAdmin) {//NOSONAR
        Optional<EventsTemplates> eventTemplate = eventTemplateRepository.findByEventId(event.getEventId());
        if(null!= eventTemplate  && eventTemplate.isPresent()) {
            throw new NotAcceptableException(NotAcceptableException.EventExceptionMsg.CAN_NOT_PUBLISH_EVENT_WITH_TEMPLATE);
        }
		boolean isAnyPaidOrDonationTicketAvailable = roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event) || ticketingTypeTicketService.isAnyTicketExistsWithDonationType(event);
        boolean isEventLive = CommonUtil.getLiveEventStatuses().stream().anyMatch(e -> e.name().equals(eventListingStatus));

        long interestCount = 0;
        if (isEventLive) {
            if (!(roPayFlowConfigService.isPayFlowConnected(event))) {
                if (!event.isCreditCardEnabled()) {
                    if (isAnyPaidOrDonationTicketAvailable) {
                        throw new NotAcceptableException(ACTIVATE_PAYMENT_PROCESSING_FOR_PAID_TICKETTYPES);
                    } else if (isAnyBaseModuleEnabled(event)) {
                        throw new NotAcceptableException(ACTIVATE_PAYMENT_PROCESSING_FOR_FUNDRAISER_MODULE);
                    }
                }
            }
            if ((event.getOrganizer() != null || event.getWhiteLabel() != null) && !isSuperAdmin) {
                PlanConfig planConfig = eventPlanConfigRepoService.findByEventId(event.getEventId()).getPlanConfig();
                if (planConfig != null && (STARTER.getName().equals(planConfig.getPlanName())
                        || PROFESSIONAL.getName().equals(planConfig.getPlanName())
                        || SINGLE_EVENT_PLAN_PRO.getName().equalsIgnoreCase(planConfig.getPlanName())
                        || SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(planConfig.getPlanName()))) {
                    associateEventWithSingleEventPlan(planConfig, event);
                } else {
                    eventPlanConfigService.handleCreateOrUpdatePlanForEvent(event);
                    planConfig = eventPlanConfigRepoService.findByEventId(event.getEventId()).getPlanConfig();
                    if (planConfig != null && (STARTER.getName().equals(planConfig.getPlanName())
                            || PROFESSIONAL.getName().equals(planConfig.getPlanName())
                            || SINGLE_EVENT_PLAN_PRO.getName().equalsIgnoreCase(planConfig.getPlanName())
                            || SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(planConfig.getPlanName()))) {
                        associateEventWithSingleEventPlan(planConfig, event);
                    }
                }
            }
        }
        EventChargebeePlanConfigDto eventChargebeePlanConfigDto = eventPlanConfigService.getPlanConfiguration(event.getEventId());

        addEventDefaultCheckInAttendeesCredits(event.getEventId(), event.getOrganizer(), event.getWhiteLabel(),
                eventChargebeePlanConfigDto.getPlanId(), eventChargebeePlanConfigDto.getChargebeePlanName(), eventChargebeePlanConfigDto.getPlatformPlanCredits(), user);

        String planName = eventChargebeePlanConfigDto.getChargebeePlanName();
        if(isEventLive && FREE_PLAN.getName().equals(planName) && event.isTicketingEnabled() && !isSuperAdmin){
            throw new NotAcceptableException(SUBSCRIPTION_NOT_ACTIVATED);
        }

        if(isEventLive && isSubscriptionCancelled(event) && !isSuperAdmin){
            throw new NotAcceptableException(SUBSCRIPTION_IS_CANCELLED);
        }

        if (isEventLive && !isSuperAdmin) {
            List<String> listOfRestrictedFeature = new ArrayList<>();
            if (EventFormat.IN_PERSON.equals(event.getEventFormat())) {
                try {
                    Ticketing ticketing = ticketingRepository.findByEventId(event.getEventId());
                    chargebeeService.checkEventStartDateOutSideTheSubscriptionExpireDate(event, ticketing.getEventEndDate());
                } catch (NotAcceptableException e) {
                    listOfRestrictedFeature.add("This event’s start date is after the end of your Accelevents plan. Please renew your plan to publish this event.");
                }
            } else {
                listOfRestrictedFeature = listOfEventUsedRestrictedFeatureByEvent(event, eventChargebeePlanConfigDto, interestCount);
            }
            if (!listOfRestrictedFeature.isEmpty()) {
                String listToCommaSeparated = convertListToCommaSeparated(listOfRestrictedFeature);
                NotAcceptableException.NotAceptableExeceptionMSG exceptionMsg = NotAceptableExeceptionMSG.CANNOT_USE_RESTRICTED_FEATURE;
                exceptionMsg.setErrorMessage(CANNOT_USE_RESTRICTED_FEATURE.replace("{plan_name}",eventChargebeePlanConfigDto.isLatestPlan()? eventChargebeePlanConfigDto.getDisplayPlanName():eventChargebeePlanConfigDto.getDisplayPlanName().concat("Plan")));
                exceptionMsg.setDeveloperMessage(listToCommaSeparated);
                throw new NotAcceptableException(exceptionMsg);
            }
        }

        showPublicEventOnOrganizerProfile(eventListingStatus, event);

        event.setEventListingStatus(EventListingStatus.valueOf(eventListingStatus));
        log.info("Change Event Listing Status eventId{} eventListingStatus{}",event.getEventId(),eventListingStatus);
        if (isEventLive) {
            chargebeeAttendeeUploadService.addChargeForUnPaidAttendeeUpload(event, user);
        }
		eventRepoService.save(event);
        hubspotEventService.updateHubspotEventDetails(event);
		if (isEventLive && null != event.getWhiteLabel()) {
			WhiteLabel whiteLabel = event.getWhiteLabel();
			List<String> emails = staffService.findAllWhiteLabelAdminEmailForNotification(whiteLabel);
			if (emails != null && !emails.isEmpty()) {
				Stripe stripe = stripeService.findByEventWithEmailDisplayName(event);
				String hostEmail = isNotBlank(stripe.getEmail()) ? stripe.getEmail() : user.getEmail();
				sendGridMailPrepareService.sendEventPublishedEmailToWhiteLabelAdmin(event, new HashSet<>(emails), hostEmail, whiteLabel,user);
			}
		}
    }

    private void showPublicEventOnOrganizerProfile(String eventListingStatus, Event event) {
        boolean isPublicStatus = CommonUtil.getPublicStatus()
                .stream()
                .map(Enum::name)
                .anyMatch(eventListingStatus::equals);

        event.setHideEventFromOrganizer(!isPublicStatus);
    }

    private void checkLobbyAndExhibitorLiveStreamSettings(String planName, String settingName){
        List<String> planNames = new ArrayList<>();
        planNames.add(PlanConfigNames.ENTERPRISE.getName());
        planNames.add(PlanConfigNames.WHITE_LABEL_PLAN.getName());
        planNames.add(PlanConfigNames.WHITE_LABEL_LEGACY.getName());
        planNames.add(PlanConfigNames.ENTERPRISE_PLAN_2023.getName());
        planNames.add(PlanConfigNames.WHITELABEL_PLAN_2023.getName());
        if (!planNames.contains(planName)){
            NotAcceptableException.NotAceptableExeceptionMSG exceptionMsg = NotAceptableExeceptionMSG.LIVE_STREAM_SETTINGS;
            String constantMessage =  Constants.LIVE_STREAM_SETTINGS.replace("${settingName}", settingName);
            constantMessage = constantMessage.replace("${plan_name}",planName);
            exceptionMsg.setErrorMessage(constantMessage);
            exceptionMsg.setDeveloperMessage(constantMessage);
            throw new NotAcceptableException(exceptionMsg);

        }
    }

    private void checkGamificationAccordingToOldPlan(EventChargebeePlanConfigDto eventChargebeePlanConfigDto,
                                                  Map<ChallengeType, Long> challengeTypeCount, long ceCriteriaCount) {
        if (eventChargebeePlanConfigDto.getPlatformConfigDto().getGamification() != null
                && ("basic").equals(eventChargebeePlanConfigDto.getPlatformConfigDto().getGamification())
                && (challengeTypeCount.getOrDefault(ChallengeType.STANDARD, 0L) > 1
                || challengeTypeCount.getOrDefault(ChallengeType.SCAVENGER_HUNT, 0L) > 1
                || challengeTypeCount.getOrDefault(ChallengeType.EARLY_BIRD, 0L) > 0
                || ceCriteriaCount > 0) && !eventChargebeePlanConfigDto.isLatestPlan()) {
            String challengeCount = challengeTypeCount.keySet().stream()
                    .map(type -> challengeTypeCount.get(type) + STRING_BLANK + type.getLabel())
                    .collect(Collectors.joining(STRING_COMMA_WITH_SPACE));
            NotAcceptableException.NotAceptableExeceptionMSG ex = NotAceptableExeceptionMSG.GAMIFICATION_CHALLENGE;
            String constantMessage = GAMIFICATION_CHALLENGE.replace("${countOfChallenge}", challengeCount);
            constantMessage = constantMessage.replace("${plan_name}", eventChargebeePlanConfigDto.getChargebeePlanName());
            ex.setErrorMessage(constantMessage);
            ex.setDeveloperMessage(constantMessage);
            throw new NotAcceptableException(ex);
        }
    }

    private List<String> checkGamificationAccordingToNewPlans(EventChargebeePlanConfigDto eventChargebeePlanConfigDto,
                                                              Map<ChallengeType, Long> challengeTypeCount, Event event) {
	    List<String> errors = new ArrayList<>();
        if (eventChargebeePlanConfigDto.getPlatformConfigDto() != null) {
            challengeTypeCount.forEach((key,value)->{
                String message = Constants.GAMIFICATON_VALIDATION_FOR_PLAN.replace("{}",key.getLabel());
                if((!SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(eventChargebeePlanConfigDto.getChargebeePlanName()) && ChallengeType.STANDARD.equals(key) && !chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BASIC_GAMIFICATION))
                        ||(ChallengeType.EARLY_BIRD.equals(key) && !chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.CUSTOM_GAMIFICATION))){
                    errors.add(message);
                }
            });
        }
        return errors;
    }

    private String checkCECriteriaAccordingToNewPlan(EventChargebeePlanConfigDto eventChargebeePlanConfigDto,
                                                    long criteriaCount, Event event) {
        if (eventChargebeePlanConfigDto.getPlatformConfigDto() != null && criteriaCount >0) {
            if(!chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.CE_CREDIT_TRACKING)){
                return Constants.GAMIFICATON_VALIDATION_FOR_PLAN.replace("{}", CRITERIA);
            }
        }
        return null;
    }

    private void checkCustomTabAccordingToPlan(VirtualEventSettings virtualEventSettings, String planName){
        String  virtualTabs = virtualEventSettings.getVirtualTabs();
        if(StringUtils.isNotBlank(virtualTabs)){
            Map<String, List<VirtualEventTabsDTO>> virtualTabsInMap = challengeConfigService.getVirtualTabsInMap(virtualTabs);
            if (virtualTabsInMap.containsKey("lobbyTab")){
                int sizeOfLobbyTab = virtualTabsInMap.get("lobbyTab").size();
                if (sizeOfLobbyTab > 6 && (FREE_PLAN.getName().equals(planName) || STARTER.getName().equals(planName) || SINGLE_EVENT_UNIT.getName().equals(planName))){
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ADDED_CUSTOM_TAB);
                }
            }
        }
    }

    private void checkNavigationMenuAccordingToPlan(VirtualEventSettings virtualEventSettings, String planName){
        String  virtualTabs = virtualEventSettings.getVirtualTabs();
        if(StringUtils.isNotBlank(virtualTabs)){
            Map<String, List<VirtualEventTabsDTO>> virtualTabsInMap = challengeConfigService.getVirtualTabsInMap(virtualTabs);
            if (virtualTabsInMap.containsKey(LEFT_SIDE_NAV_MENU)){
                int sizeOfLeftSideNavMenu = virtualTabsInMap.get(LEFT_SIDE_NAV_MENU).size();
                if (sizeOfLeftSideNavMenu > 11 && (FREE_PLAN.getName().equals(planName) || STARTER.getName().equals(planName) || SINGLE_EVENT_UNIT.getName().equals(planName))){
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.ADDED_NAVIGATION_MENU);
                }
            }
        }
    }

    private boolean isSubscriptionCancelled(Event event) {
        String subscriptionId = event.getWhiteLabel() != null ? event.getWhiteLabel().getSubscriptionId()
                : event.getOrganizer() != null ? event.getOrganizer().getSubscriptionId() : null;

        if(subscriptionId == null)
            return false;

        return chargebeeService.isSubscriptionIsCancelledOrNonRenewing(subscriptionId,event);
    }

    private void associateEventWithSingleEventPlan(PlanConfig planConfig, Event event) {
        ChargeConfigNames chargeConfigName = null;
        List<ChargesPurchased> unusedChargesOfParticularType;//NOSONAR
        if (STARTER.getName().equals(planConfig.getPlanName())) {
            chargeConfigName = ChargeConfigNames.STARTER;
        } else if (PROFESSIONAL.getName().equals(planConfig.getPlanName())) {
            chargeConfigName = ChargeConfigNames.PROFESSIONAL;
        }else if(SINGLE_EVENT_PLAN_PRO.getName().equalsIgnoreCase(planConfig.getPlanName())){
            chargeConfigName = ChargeConfigNames.PROFESSIONAL_2023;
        }else if(SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(planConfig.getPlanName())){
            chargeConfigName = ChargeConfigNames.STARTER_2023;
        }
        ChargeConfig chargeConfig = chargeConfigRepoService.findByChargeDisplayNameThrowException(chargeConfigName.getLableName());//NOSONAR
        Optional<EventChargeUsages> eventChargeUsagesOptional = eventChargeUsagesRepoService.findByOrganizerIdAndEventIdAndChargeConfig(event.getOrganizerId(), event.getEventId(), chargeConfig);
        if (!eventChargeUsagesOptional.isPresent() || (eventChargeUsagesOptional.isPresent() && eventChargeUsagesOptional.get().getChargesPurchased() == null)) {//NOSONAR
            List<EventChargeUsages> eventChargeUsagesListWithStatusDeleted = eventChargeUsagesRepoService.findByOrganizerIdAndEventIdAndChargeConfigWithStatusDeleted(event.getOrganizerId(),event.getEventId(),chargeConfig.getId());
            if(!CollectionUtils.isEmpty(eventChargeUsagesListWithStatusDeleted)) {
                unusedChargesOfParticularType = chargesPurchasedRepoService.findByIdAndChargeStatus(eventChargeUsagesListWithStatusDeleted.get(0).getChargesPurchased().getId(),ChargesPurchased.ChargeStatus.USED);
            }else{
                unusedChargesOfParticularType = chargesPurchasedRepoService.findByOrganizerAndChargeConfigIdAndChargeStatusNot(
                        event.getOrganizer(), chargeConfig, ChargesPurchased.ChargeStatus.USED);
            }
            if (CollectionUtils.isEmpty(unusedChargesOfParticularType)) {
                NotAcceptableException.NotAceptableExeceptionMSG exceptionMsg = NotAceptableExeceptionMSG.DOES_NOT_HAVE_ENOUGH_QUANTITY;
                String planName = String.valueOf(planConfig.getDisplayPlanName());
                if (Boolean.FALSE.equals(planConfig.isLatestPlan())) {
                    planName = planName.concat(" Plan");
                }
                exceptionMsg.setErrorMessage(DOES_NOT_HAVE_ENOUGH_QUANTITY.replace(PLAN_NAME_PLACE_HOLDER, planName));
                exceptionMsg.setDeveloperMessage(DOES_NOT_HAVE_ENOUGH_QUANTITY.replace(PLAN_NAME_PLACE_HOLDER, planName));
                throw new NotAcceptableException(exceptionMsg);
            } else {
                markChargeStatusUsed(unusedChargesOfParticularType.get(0));
                eventChargeUsagesService.addOrUpdateEventChargeUsages(event.getOrganizer().getId(), event.getEventId(),
                        unusedChargesOfParticularType.get(0));
            }
        }
    }

    private void addEventDefaultCheckInAttendeesCredits(long eventId, Organizer organizer, WhiteLabel whiteLabel,
                                                        Long planId, String chargebeePlanName,Integer planCredits,User user) {
        boolean chargebeeEventCreditsRecordExists =
                isChargebeeEventCreditsRecordExists(eventId, organizer, whiteLabel, chargebeePlanName);
         String subscriptionId=null;
        if(!chargebeeEventCreditsRecordExists) {
            ChargebeeEventCredits chargebeeEventCredits = new ChargebeeEventCredits();
            if(whiteLabel!=null){
                chargebeeEventCredits.setWhiteLabelId(whiteLabel.getId());
				chargebeeEventCredits.setSubscriptionId(whiteLabel.getSubscriptionId());
                subscriptionId=whiteLabel.getSubscriptionId();
            }else if(SINGLE_EVENT_UNIT.getName().equals(chargebeePlanName)
                    || PROFESSIONAL.getName().equals(chargebeePlanName)
                    || STARTER.getName().equals(chargebeePlanName)
                    || SINGLE_EVENT_PLAN_PRO.getName().equalsIgnoreCase(chargebeePlanName)
                    || SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(chargebeePlanName) ) {
                chargebeeEventCredits.setEventId(eventId);
            }else {
                chargebeeEventCredits.setOrganizerId(organizer != null ? organizer.getId() : null);
                subscriptionId=organizer != null ? organizer.getSubscriptionId() : null;
                chargebeeEventCredits.setSubscriptionId(subscriptionId);
            }
            chargebeeEventCredits.setPlanConfigId(planId);
            chargebeeEventCredits.setRemainingFreeCredits(planCredits);
            chargebeeEventCredits.setFreeCreditsConsumed(false);
			chargebeeEventCredits.setUpdateByAdmin(true);
            chargebeeEventCreditsRepoService.save(chargebeeEventCredits,user);
            //TODO need to rethink
            chargeBeeHelperService.createChargebeeLogRecord(subscriptionId, CreditType.PLAN_DEFAULT.name(), planCredits, whiteLabel,organizer,true);

        }else{
            log.info("Record in Chargebee Event Credits table already exists for eventId: {}, organizer:{}, whiteLabel:{}",
                    eventId, organizer, whiteLabel);
        }
    }

    protected boolean isChargebeeEventCreditsRecordExists(long eventId, Organizer organizer, com.accelevents.domain.WhiteLabel whiteLabel, String chargebeePlanName) {
        Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional= Optional.empty();//NOSONAR
        if (whiteLabel != null) {
            chargebeeEventCreditsOptional = chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
        } else if (SINGLE_EVENT_UNIT.getName().equals(chargebeePlanName)
                || PROFESSIONAL.getName().equals(chargebeePlanName)
                || STARTER.getName().equals(chargebeePlanName)
                || SINGLE_EVENT_PLAN_PRO.getName().equalsIgnoreCase(chargebeePlanName)
                || SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(chargebeePlanName) ) {
            chargebeeEventCreditsOptional =
                    chargebeeEventCreditsRepoService.findByEventId(eventId);
        } else if(organizer!=null){
            chargebeeEventCreditsOptional =
                    chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
        }
        return chargebeeEventCreditsOptional != null && chargebeeEventCreditsOptional.isPresent();
    }

    protected void markChargeStatusUsed(ChargesPurchased chargesPurchased) {//NOSONAR
        chargesPurchased.setChargeStatus(ChargesPurchased.ChargeStatus.USED);
        chargesPurchasedRepoService.save(chargesPurchased);
    }

    private List<String> listOfEventUsedRestrictedFeatureByEvent(Event event, EventChargebeePlanConfigDto eventChargebeePlanConfigDto, long interestCount) {

        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        List<Long> noOfCurrentTicketType = ticketingTypeService.findAllByEventIdNumberOfTickets(event);

        long numberOfExhibitors = exhibitorRepoService.countByEventId(event.getEventId());

        List<EnumSessionFormat> enumSessionFormats = Arrays.asList(BREAK, OTHER, EXPO);
        long nonVisualSessionsCount = sessionRepoService.getNonVisualSessionsByEvent(event.getEventId(), enumSessionFormats);

        long numberOfLiveStreamingExhibitorsPurchased = virtualEventService.getTheNumberOfExhibitorsPurchased(event);

        VirtualEventSettings settings = virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId());
        ExhibitorSetting exhibitorSetting = exhibitorSettingsRepoService.getGlobalConfigRecordByEventId(event.getEventId());

        Map<ChallengeType, Long> challengeTypeCount = eventChallengeRepoService.findChallengeTypeCount(event.getEventId());

        long ceCriteriaCount = eventCECriteriaRepoService.getCECriteriaCountByEventId(event.getEventId());

        List<String> listOfBeyondPlanFeature = new ArrayList<>();
        boolean isEventUsedRestrictedFeature;
        if(ticketing != null) {
            TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());
            String eventTimeZone = timeZone.getEquivalentTimezone();
            Date eventStartDate =  DateUtils.getDate(ticketing.getEventStartDate(), eventTimeZone);
            Date eventEndDate   =  DateUtils.getDate(ticketing.getEventEndDate(), eventTimeZone);
            long dateDiffrence  = DateUtils.getNumberDaysInclusiveBothTheDatesInEvent(eventStartDate,eventEndDate);

            isEventUsedRestrictedFeature = ChargeBeeUtils.validateNumberOfEventDay(eventStartDate, eventEndDate,
                    eventChargebeePlanConfigDto.getRegistrationConfigDto().getMaxEventDays());
                if(isEventUsedRestrictedFeature){
                    listOfBeyondPlanFeature.add((dateDiffrence)+" event days is created which is beyond the plan limit of "+eventChargebeePlanConfigDto.getRegistrationConfigDto().getMaxEventDays());
                }
                // NOTE: We are removing this rule temporarily until the new rules are defined.
            try {
                chargebeeService.checkEventStartDateOutSideTheSubscriptionExpireDate(event,ticketing.getEventEndDate());
            } catch (NotAcceptableException e) {
                listOfBeyondPlanFeature.add("This event's dates are after the end of your Accelevents plan. Please renew your plan to publish this event.");
            }
        }

            try {
                chargebeeService.validateTicketTypesByEventPlan(event, (long) noOfCurrentTicketType.size() - 1 );
            }catch (NotAcceptableException e){
                listOfBeyondPlanFeature.add(((long) noOfCurrentTicketType.size())+" tickets is created which is beyond the plan limit of "+eventChargebeePlanConfigDto.getCapUsageDto().getMaxTicketTypes()+" tickets");
            }

            try {
                chargebeeService.validateExhibitorsByEventPlan(event, numberOfExhibitors -1);
            }catch (NotAcceptableException e){
                listOfBeyondPlanFeature.add((numberOfExhibitors)+" exhibitor booth is used which is beyond the plan limit of "+eventChargebeePlanConfigDto.getCapUsageDto().getMaxExhibitorBooth());
            }
            try {
                chargebeeService.checkMaxAgendaItemsByPlanType(event, nonVisualSessionsCount - 1);
            }catch (NotAcceptableException e){
                listOfBeyondPlanFeature.add((nonVisualSessionsCount)+" session is created which is beyond the plan limit of "+eventChargebeePlanConfigDto.getCapUsageDto().getMaxAgendaItems());
            }
            try {
                neptuneInterestService.validateNumberOfInterestTagForScalePlan(event,interestCount);
            }catch (NotAcceptableException e){
                listOfBeyondPlanFeature.add(interestCount+" Interest tag is created which is beyond the plan limit of "+eventChargebeePlanConfigDto.getRegistrationConfigDto().getInterestTagAvailable());
            }
            try {
                chargebeeService.validateMaxProExhibitors(event, numberOfLiveStreamingExhibitorsPurchased -1);
            }catch (NotAcceptableException e){
                listOfBeyondPlanFeature.add((numberOfLiveStreamingExhibitorsPurchased - 1)+" live stream exhibitor is purchased which is beyond the plan limit of "+eventChargebeePlanConfigDto.getCapUsageDto().getMaxProExhibitors());
            }
            try {
                if (settings.isLobbyLiveStream()) {
                    checkLobbyAndExhibitorLiveStreamSettings(eventChargebeePlanConfigDto.getChargebeePlanName(), "Lobby live stream");
                }
            } catch (NotAcceptableException e) {
                listOfBeyondPlanFeature.add("Lobby live stream toggle is enabled which is not allowed for " + eventChargebeePlanConfigDto.getChargebeePlanName() + " plan");
            }
            try {
                if (exhibitorSetting != null && exhibitorSetting.isExpoLiveStream()) {
                    checkLobbyAndExhibitorLiveStreamSettings(eventChargebeePlanConfigDto.getChargebeePlanName(), "Exhibitor Live Stream");
                }
            } catch (NotAcceptableException e) {
                listOfBeyondPlanFeature.add("Exhibitor live stream toggle is enabled which is not allowed for " + eventChargebeePlanConfigDto.getChargebeePlanName() + " plan");
            }
            try {
                checkGamificationAccordingToOldPlan(eventChargebeePlanConfigDto, challengeTypeCount, ceCriteriaCount);
            } catch (NotAcceptableException e) {
                listOfBeyondPlanFeature.add(e.getErrorMessage());
            }
            try {
                checkCustomTabAccordingToPlan(settings, eventChargebeePlanConfigDto.getChargebeePlanName());
            } catch (NotAcceptableException e) {
                listOfBeyondPlanFeature.add("You have added custom tab which is not allowed under this " + eventChargebeePlanConfigDto.getChargebeePlanName() + " plan. Kindly delete this extra added custom tab or update your organizer plan");
            }
            try {
                checkNavigationMenuAccordingToPlan(settings, eventChargebeePlanConfigDto.getChargebeePlanName());
            } catch (NotAcceptableException e) {
                listOfBeyondPlanFeature.add("You have added navigation menu tab which is not allowed under this " + eventChargebeePlanConfigDto.getChargebeePlanName() + " plan. Kindly delete this extra added navigation menu tab or update your organizer plan");
            }
            listOfBeyondPlanFeature.addAll(checkGamificationAccordingToNewPlans(eventChargebeePlanConfigDto, challengeTypeCount,event));
            String criteriaMessage = checkCECriteriaAccordingToNewPlan(eventChargebeePlanConfigDto, ceCriteriaCount, event);
            if(StringUtils.isNotBlank(criteriaMessage)) {
                listOfBeyondPlanFeature.add(criteriaMessage);
            }

        return listOfBeyondPlanFeature;
    }

    @Override
    public void sendSupportEmailToWhiteLabel(ContactDto contact, String whiteLabelUrl) {
		if (StringUtils.isEmpty(contact.getEmail())) {
			throw new AuthorizationException(NOT_AUTHORIZE);
		}
		EmailMessage emailMessage = new EmailMessage(TemplateId.PARTICIPANT_QUESTION);
		WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(whiteLabelUrl);
		Set<String> receivers = this.sendEmail(whiteLabel, contact.getEmail(), "An event participant has a question", contact.getMessage(),
				TemplateId.PARTICIPANT_QUESTION, emailMessage, whiteLabel.getNotificationEmail(), whiteLabel.getFirmName());
		receivers.add(whiteLabel.getSupportEmail());

		Map<String, Object> substitutionData = new HashMap<>();
		substitutionData.put("name",
				isNotBlank(contact.getName()) ? contact.getName() : STRING_DASH);
		substitutionData.put("email",
				isNotBlank(contact.getEmail()) ? contact.getEmail() : STRING_DASH);
		substitutionData.put("event_name", STRING_DASH);
		substitutionData.put("pageUrl", STRING_DASH);
		emailMessage.setSubstitutionData(substitutionData);
		try {
			sendGridMailPrepareService.sendSupportOrContactMail(emailMessage, null, receivers, whiteLabel);
		} catch (Exception e) {
			log.error(EXCEPTION, e);
		}

    }

    @Override
    public String getEventNameFromCreatedFrom(Long createdFrom) {
		return eventRepository.getEventNameFromCreatedFrom(createdFrom);
    }

	protected String convertDonationButtonStringToGson(DonationButtonDesignDto donationButtonText) {
		Gson gson = new Gson();
		if(null == donationButtonText) {
			donationButtonText = new DonationButtonDesignDto();
		}
		return gson.toJson(donationButtonText, DonationButtonDesignDto.class);
	}

	@Override
	public Set<Event> getEventsByUserAndWhiteLabelNotNull(User user) {
		return staffService.findByUserAndWhiteLabelNotNull(user).stream()
				.map(Staff::getEvent)
				.filter(Objects::nonNull).collect(Collectors.toSet());
	}

	@Override
	public Event findEventByEventURLWithoutCache(String eventurl) {
		return eventRepository.findEventByEventURLWithoutCache(eventurl);
	}

	@Override
    @Transactional
	public void setWhiteLabelSettingToEvent(Event event, WhiteLabel whiteLabel, User loggedInUser) {
        List<JoinUsersWithOrganizers> joinUsersWithOrganizersByWhiteLabel = joinUserWithOrganizerRepoService.findAllOrganizersByWhiteLabelIdAndRoleAdmin(whiteLabel.getId());
        JoinUsersWithOrganizers joinUsersWithOrganizer;
        Organizer organizer;
        if (!joinUsersWithOrganizersByWhiteLabel.isEmpty()) {
            joinUsersWithOrganizer = joinUsersWithOrganizersByWhiteLabel.get(0);
            organizer = joinUsersWithOrganizer.getOrganizer();
            User newUser = joinUsersWithOrganizer.getUser();
            List<Staff> staffsByUser = roStaffService.findByEventAndUserNotExhibitor(event, newUser);
            if (staffsByUser.isEmpty()) {
                StaffDetail staff = new StaffDetail();
                staff.setFirstName(newUser.getFirstName());
                staff.setLastName(newUser.getLastName());
                staff.setEmail(newUser.getEmail());
                staff.setRole(StaffRole.admin.toString());
                staffService.addStaff(event, staff, loggedInUser, null);
            }
        } else {
            organizer = null;
        }

		event.setWhiteLabelId(whiteLabel.getId());
		event.setWhiteLabel(whiteLabel);
        event.setOrganizer(organizer);
        event.setOrganizerId(organizer!=null?organizer.getId():null);
        copyWhiteLabelFundRaisingModules(whiteLabel, event);
        eventRepoService.save(event);

        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
        eventPlanConfig.setOrganizer(organizer);
        eventPlanConfig.setWhiteLabel(whiteLabel);
        eventPlanConfig.setPlanConfig(whiteLabel.getPlanConfig());
        eventPlanConfig.setPlanConfigId(whiteLabel.getPlanConfig().getId());
        eventPlanConfigService.save(eventPlanConfig);

        List<TransactionFeeConditionalLogic> listOfRecordsByEvent = transactionFeeConditionalLogicService.getRecordByEvent(event);
        listOfRecordsByEvent.forEach(transactionFeeConditionalLogic -> {
            transactionFeeConditionalLogic.setWhiteLabelId(whiteLabel.getId());
            transactionFeeConditionalLogic.setWhiteLabel(whiteLabel);
            transactionFeeConditionalLogic.setOrganizer(organizer);
        });
        transactionFeeConditionalLogicService.saveAll(listOfRecordsByEvent);
        List<TransactionFeeConditionalLogic> listOfRecords = transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(whiteLabel);
        listOfRecords.forEach(transactionFeeConditionalLogic -> transactionFeeConditionalLogicService.updateTransactionFeeConditionalLogic(transactionFeeConditionalLogic, listOfRecordsByEvent,null,null,event));
		Auction auctionModule = auctionService.findByEvent(event);
		auctionModule.setActivated(whiteLabel.isAuctionsActivated());
		if (whiteLabel.isManualPayout()) {
			auctionModule.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
		}
		auctionService.save(auctionModule);

		Raffle raffleModule = raffleService.findByEvent(event);
		raffleModule.setActivated(whiteLabel.isRafflesActivated());
		if (whiteLabel.isManualPayout()) {
			raffleModule.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
		}
		raffleService.save(raffleModule);

		CauseAuction causeAuctionModule = causeAuctionService.findByEvent(event);
		causeAuctionModule.setActivated(whiteLabel.isCauseAuctionActivated());
		if (whiteLabel.isManualPayout()) {
			causeAuctionModule.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
		}
		causeAuctionService.save(causeAuctionModule);

	}

	/*Apply Default Whitelabel Transaction Logic To Ticketing And Event Transaction Logic*/
	@Override
	public void setDefaultWlDesignSettingsForEvent(Event event, WhiteLabel whiteLabel) {

		//Set whitelabel ticketing setting to event ticketing
		Ticketing ticketing = ticketingService.findByEvent(event);
		if (whiteLabel.isManualPayout()) {
			ticketing.setEventPayoutStatus(EnumEventPayoutStatus.INITIALIZED);
		}
		ticketingService.save(ticketing);

        List<TransactionFeeConditionalLogic> listOfRecordsByEvent = transactionFeeConditionalLogicService.getRecordByEvent(event);
        List<TransactionFeeConditionalLogic> listOfRecords = transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(whiteLabel);
        listOfRecords.forEach(transactionFeeConditionalLogic -> transactionFeeConditionalLogicService.updateTransactionFeeConditionalLogic(transactionFeeConditionalLogic, listOfRecordsByEvent,null,null,event));
	}

    @Override
    public void setDefaultTransactionFeeLogicForEventToDisconnectFromWL(Organizer organizer) {

        log.info("Set Default TransactionFeeConditionalLogic For Event To Disconnect From WL process started for organizer =>{}", organizer.getId());
        List<TicketingType> allTicketingTypeByOrganiser = ticketingTypeService.findAllTicketingTypeByOrganiser(organizer);
        List<TransactionFeeConditionalLogic> organizerTransactionFeeConditionalLogics = transactionFeeConditionalLogicService.getBaseRecordByOrganizer(organizer);

        List<TicketingType> allAddOnTicketType = allTicketingTypeByOrganiser.stream().filter(ticketingType -> ticketingType.getDataType().equals(DataType.ADDON)).collect(Collectors.toList());
        List<TicketingType> allTicketTypeFree = allTicketingTypeByOrganiser.stream().filter(ticketingType -> ticketingType.getDataType().equals(DataType.TICKET) && ticketingType.getTicketType().equals(TicketType.FREE)).collect(Collectors.toList());
        List<TicketingType> allTicketTypePaid = allTicketingTypeByOrganiser.stream().filter(ticketingType -> ticketingType.getDataType().equals(DataType.TICKET) && ticketingType.getTicketType().equals(TicketType.PAID)).collect(Collectors.toList());

        List<TicketingType> finalTicketingTypeList = new ArrayList<>();
        boolean isPaidFeeLogicUpdated = false;
        boolean isFreeFeeLogicUpdated = false;
        boolean isAddOnFeeLogicUpdated = false;

        //remove whitelabel transaction fee condition logic to ticketing type
        for(TransactionFeeConditionalLogic transactionFeeConditionalLogic : organizerTransactionFeeConditionalLogics) {
            if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())) {
                if(transactionFeeConditionalLogic.isAddon() && !isAddOnFeeLogicUpdated) {
                    allAddOnTicketType.forEach(ticketingType -> finalTicketingTypeList.add(this.setFeesLogicOnTicketTypeByOrganizerFeeLogic(transactionFeeConditionalLogic, ticketingType)));
                    isAddOnFeeLogicUpdated = true;
                    log.info("Set Default TransactionFeeConditionalLogic For Addon ticket Type for all events of organizer {} and Whitelabel TransactionFeeConditionalLogic Id=>{}", organizer.getId(), transactionFeeConditionalLogic.getId());
                } else if(!isPaidFeeLogicUpdated) {
                    allTicketTypePaid.forEach(ticketingType -> finalTicketingTypeList.add(this.setFeesLogicOnTicketTypeByOrganizerFeeLogic(transactionFeeConditionalLogic, ticketingType)));
                    isPaidFeeLogicUpdated = true;
                    log.info("Set Default TransactionFeeConditionalLogic For Paid ticket Type for all events of organizer {} and Whitelabel TransactionFeeConditionalLogic Id=>{}", organizer.getId(), transactionFeeConditionalLogic.getId());
                }
            } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator()) && !isFreeFeeLogicUpdated) {
                allTicketTypeFree.forEach(ticketingType -> finalTicketingTypeList.add(this.setFeesLogicOnTicketTypeByOrganizerFeeLogic(transactionFeeConditionalLogic, ticketingType)));
                isFreeFeeLogicUpdated = true;
                log.info("Set Default TransactionFeeConditionalLogic For Free ticket Type for all events of organizer {} and Whitelabel TransactionFeeConditionalLogic Id=>{}", organizer.getId(), transactionFeeConditionalLogic.getId());
            }
        }
        ticketingTypeService.saveAll(finalTicketingTypeList);


        // Get All Event Transaction Fee Conditional Logic by organizer
        List<TransactionFeeConditionalLogic> allEventsTransactionFeeConditionalLogics = transactionFeeConditionalLogicService.getAllEventsRecordByOrganizer(organizer);

        TransactionFeeConditionalLogic greaterThanEqualToOrgFeeLogic = organizerTransactionFeeConditionalLogics.stream().filter(e -> GREATER_THAN_EQUAL_TO.equalsIgnoreCase(e.getOperator())).findFirst().orElse(null);
        TransactionFeeConditionalLogic lessThanOrgFeeLogic = organizerTransactionFeeConditionalLogics.stream().filter(e -> LESS_THAN.equalsIgnoreCase(e.getOperator())).findFirst().orElse(null);

        //remove whitelabel transaction fee condition logic to event transaction fee condition logic when move organizer from WL
        for(TransactionFeeConditionalLogic transactionFeeConditionalLogic : allEventsTransactionFeeConditionalLogics) {
            if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())) {
                TransactionFeeBaseRecordDto defaultFeeLogic = new TransactionFeeBaseRecordDto(AE_FLAT_FEE_ONE, AE_FEE_PERCENTAGE_THREE, POINT_ZERO_ONE, -1);
                this.setFeesLogicOnEventByOrganizerFeeLogic(transactionFeeConditionalLogic, greaterThanEqualToOrgFeeLogic, defaultFeeLogic);
                log.info("Set Default Paid TransactionFeeConditionalLogic for Event for all events of organizer {}", organizer.getId());

            } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())) {
                TransactionFeeBaseRecordDto defaultFeeLogic = new TransactionFeeBaseRecordDto(AE_FLAT_FEE_ZERO, AE_FEE_PERCENTAGE_ZERO, -1, POINT_ZERO_ONE);
                this.setFeesLogicOnEventByOrganizerFeeLogic(transactionFeeConditionalLogic, lessThanOrgFeeLogic, defaultFeeLogic);
                log.info("Set Default Free TransactionFeeConditionalLogic for Event for all events of organizer {}", organizer.getId());

            }
        }
        transactionFeeConditionalLogicService.saveAll(allEventsTransactionFeeConditionalLogics);
        log.info("Set Default TransactionFeeConditionalLogic For Event To Disconnect From WL process completed for organizer =>{}", organizer.getId());
    }

    @Override
    @Transactional
    public void setDefaultTransactionFeeLogicForEventToMoveWLToOtherWL(Organizer organizer, WhiteLabel toWhiteLabel) {

        log.info("Set Default TransactionFeeConditionalLogic For Event To Move From WL to Other WL process started for organizer =>{} to whiteLabel =>{}", organizer.getId(), toWhiteLabel.getId());
        List<TicketingType> allTicketingTypeByOrganiser = ticketingTypeService.findAllTicketingTypeByOrganiser(organizer);
        List<TransactionFeeConditionalLogic> toWhiteLabelTransactionFeeConditionalLogics = transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(toWhiteLabel);

        List<TicketingType> allAddOnTicketType = allTicketingTypeByOrganiser.stream().filter(ticketingType -> ticketingType.getDataType().equals(DataType.ADDON)).collect(Collectors.toList());
        List<TicketingType> allTicketTypeFree = allTicketingTypeByOrganiser.stream().filter(ticketingType -> ticketingType.getDataType().equals(DataType.TICKET) && ticketingType.getTicketType().equals(TicketType.FREE)).collect(Collectors.toList());
        List<TicketingType> allTicketTypePaid = allTicketingTypeByOrganiser.stream().filter(ticketingType -> ticketingType.getDataType().equals(DataType.TICKET) && ticketingType.getTicketType().equals(TicketType.PAID)).collect(Collectors.toList());

        List<TicketingType> finalTicketingTypeList = new ArrayList<>();
        boolean isPaidFeeLogicUpdated = false;
        boolean isFreeFeeLogicUpdated = false;
        boolean isAddOnFeeLogicUpdated = false;

        //remove from whitelabel transaction fee condition logic to ticketing type and set to new whitelabel transaction fee condition logic
        for(TransactionFeeConditionalLogic transactionFeeConditionalLogic : toWhiteLabelTransactionFeeConditionalLogics) {
            if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())) {
                if(transactionFeeConditionalLogic.isAddon() && !isAddOnFeeLogicUpdated) {
                    allAddOnTicketType.forEach(ticketingType -> finalTicketingTypeList.add(this.setFeesLogicOnTicketTypeByOrganizerFeeLogic(transactionFeeConditionalLogic, ticketingType)));
                    isAddOnFeeLogicUpdated = true;
                    log.info("Set Default TransactionFeeConditionalLogic For Addon ticket Type for all events of organizer {} and TransactionFeeConditionalLogicId {} of toWhiteLabel {}", organizer.getId(), transactionFeeConditionalLogic.getId(), toWhiteLabel.getId());
                } else if(!isPaidFeeLogicUpdated) {
                    allTicketTypePaid.forEach(ticketingType -> finalTicketingTypeList.add(this.setFeesLogicOnTicketTypeByOrganizerFeeLogic(transactionFeeConditionalLogic, ticketingType)));
                    isPaidFeeLogicUpdated = true;
                    log.info("Set Default TransactionFeeConditionalLogic For Paid ticket Type for all events of organizer {} and TransactionFeeConditionalLogicId {} of toWhiteLabel {}", organizer.getId(), transactionFeeConditionalLogic.getId(), toWhiteLabel.getId());
                }
            } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator()) && !isFreeFeeLogicUpdated) {
                allTicketTypeFree.forEach(ticketingType -> finalTicketingTypeList.add(this.setFeesLogicOnTicketTypeByOrganizerFeeLogic(transactionFeeConditionalLogic, ticketingType)));
                isFreeFeeLogicUpdated = true;
                log.info("Set Default TransactionFeeConditionalLogic For Free ticket Type for all events of organizer {} and TransactionFeeConditionalLogicId {} of toWhiteLabel {}", organizer.getId(), transactionFeeConditionalLogic.getId(), toWhiteLabel.getId());
            }
        }
        ticketingTypeService.saveAll(finalTicketingTypeList);


        // Get All Event Transaction Fee Conditional Logic by organizer
        List<TransactionFeeConditionalLogic> allEventsTransactionFeeConditionalLogics = transactionFeeConditionalLogicService.getAllEventsRecordByOrganizer(organizer);

        TransactionFeeConditionalLogic greaterThanAndVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> GREATER_THAN_EQUAL_TO.equalsIgnoreCase(e.getOperator()) && e.isVirtualEvent()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(GREATER_THAN_EQUAL_TO, true, false));
        TransactionFeeConditionalLogic greaterThanAndNonVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> GREATER_THAN_EQUAL_TO.equalsIgnoreCase(e.getOperator()) && !e.isVirtualEvent()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(GREATER_THAN_EQUAL_TO, false, false));
        TransactionFeeConditionalLogic greaterThanAndAddon = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> GREATER_THAN_EQUAL_TO.equalsIgnoreCase(e.getOperator()) && e.isAddon()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(GREATER_THAN_EQUAL_TO, false, true));
        TransactionFeeConditionalLogic lessThanAndVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> LESS_THAN.equalsIgnoreCase(e.getOperator()) && e.isVirtualEvent()).findFirst()
                .orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(LESS_THAN, true, false));
        TransactionFeeConditionalLogic lessThanAndNonVirtual = toWhiteLabelTransactionFeeConditionalLogics.stream().filter(e -> LESS_THAN.equalsIgnoreCase(e.getOperator()) && !e.isVirtualEvent()).findFirst().
                orElse(transactionFeeConditionalLogicService.getDefaultTransactionFeeConditionalLogicWithoutWLFees(LESS_THAN, false, false));

        allEventsTransactionFeeConditionalLogics.forEach(transactionFeeConditionalLogic -> {
            if(transactionFeeConditionalLogic.isAddon()) {
                transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, greaterThanAndAddon, toWhiteLabel);
                log.info("Set Default Addon TransactionFeeConditionalLogic for Event for all events of organizer {} of toWhiteLabel {} TransactionFeeConditionalLogic Id=>{}", organizer.getId(), toWhiteLabel.getId(), transactionFeeConditionalLogic.getId());
            } else if(transactionFeeConditionalLogic.isVirtualEvent()) {
                // Set the TFCL for Virtual Event
                if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, greaterThanAndVirtual, toWhiteLabel);
                } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, lessThanAndVirtual, toWhiteLabel);
                }
                log.info("Set Default Virtual Event TransactionFeeConditionalLogic for Event for all events of organizer {} of toWhiteLabel {} TransactionFeeConditionalLogic Id=>{}", organizer.getId(), toWhiteLabel.getId(), transactionFeeConditionalLogic.getId());
            } else {
                // Set the TFCL for Non-Virtual Event
                if(GREATER_THAN_EQUAL_TO.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, greaterThanAndNonVirtual, toWhiteLabel);
                } else if(LESS_THAN.equalsIgnoreCase(transactionFeeConditionalLogic.getOperator())){
                    transactionFeeConditionalLogicService.setTransactionFeeConditionalLogicByWlAccountTFCL(transactionFeeConditionalLogic, lessThanAndNonVirtual, toWhiteLabel);
                }
                log.info("Set Default Non-Virtual Event TransactionFeeConditionalLogic for Event for all events of organizer {} of toWhiteLabel {} TransactionFeeConditionalLogic Id=>{}", organizer.getId(), toWhiteLabel.getId(), transactionFeeConditionalLogic.getId());
            }
        });

        transactionFeeConditionalLogicService.saveAll(allEventsTransactionFeeConditionalLogics);
        log.info("Set Default TransactionFeeConditionalLogic For Event To Move From WL to Other WL process completed for organizer =>{} to whiteLabel =>{}", organizer.getId(), toWhiteLabel.getId());
    }

    private TicketingType setFeesLogicOnTicketTypeByOrganizerFeeLogic(TransactionFeeConditionalLogic transactionFeeConditionalLogic, TicketingType ticketingType) {
        ticketingType.setWlAFeeFlat(transactionFeeConditionalLogic.getWlAFeeFlat());
        ticketingType.setWlAFeePercentage(transactionFeeConditionalLogic.getWlAFeePercentage());
        ticketingType.setWlBFeeFlat(transactionFeeConditionalLogic.getWlBFeeFlat());
        ticketingType.setWlBFeePercentage(transactionFeeConditionalLogic.getWlBFeePercentage());

        ticketingType.setAeFeeFlat(transactionFeeConditionalLogic.getAeFeeFlat());
        ticketingType.setAeFeePercentage(transactionFeeConditionalLogic.getAeFeePercentage());
        return ticketingType;
    }

    private void setFeesLogicOnEventByOrganizerFeeLogic(TransactionFeeConditionalLogic eventTransactionFeeConditionalLogic, TransactionFeeConditionalLogic organizerTransactionFeeConditionalLogic, TransactionFeeBaseRecordDto defaultTransactionFeeConditionalLogic) {
        // remove WhiteLabel transaction fee condition logic to event transaction fee condition logic when move organizer from WL
        if (organizerTransactionFeeConditionalLogic != null) {
            // Set organizer transaction fee logic
            eventTransactionFeeConditionalLogic.setAeFeeFlat(organizerTransactionFeeConditionalLogic.getAeFeeFlat());
            eventTransactionFeeConditionalLogic.setAeFeePercentage(organizerTransactionFeeConditionalLogic.getAeFeePercentage());
            eventTransactionFeeConditionalLogic.setFromTicketPriceThreshold(organizerTransactionFeeConditionalLogic.getFromTicketPriceThreshold());
            eventTransactionFeeConditionalLogic.setToTicketPriceThreshold(organizerTransactionFeeConditionalLogic.getToTicketPriceThreshold());
        } else {
            // Set default transaction fee logic
            eventTransactionFeeConditionalLogic.setAeFeeFlat(defaultTransactionFeeConditionalLogic.getAeFeeFlat());
            eventTransactionFeeConditionalLogic.setAeFeePercentage(defaultTransactionFeeConditionalLogic.getAeFeePercentage());
            eventTransactionFeeConditionalLogic.setFromTicketPriceThreshold(defaultTransactionFeeConditionalLogic.getFromTicketingPriceThreshold());
            eventTransactionFeeConditionalLogic.setToTicketPriceThreshold(defaultTransactionFeeConditionalLogic.getToTicketPriceThreshold());

        }
        eventTransactionFeeConditionalLogic.setWhiteLabelId(null);
        eventTransactionFeeConditionalLogic.setWhiteLabel(null);
        eventTransactionFeeConditionalLogic.setWlAFeePercentage(0d);
        eventTransactionFeeConditionalLogic.setWlBFeePercentage(0d);
        eventTransactionFeeConditionalLogic.setWlAFeeFlat(0d);
        eventTransactionFeeConditionalLogic.setWlBFeeFlat(0d);
        eventTransactionFeeConditionalLogic.setWlAStripeUserId(null);
        eventTransactionFeeConditionalLogic.setWlBStripeUserId(null);
    }


    @Override
    public List<ExpoEventDto> getEventDetailsByExpoStaffUser(User user, List<StaffRole> exhibitorRoles) {
        List<ExpoEventDto> expoEventDtos = staffService.findEventDetailByUserAndRoles(user, exhibitorRoles);
        log.info("Event details of user {}  ExpoEventDto :{}" , user.getUserId() , expoEventDtos);
        return expoEventDtos;
    }

	@Override
	public void updateUserCardStatus(Event event, User user, String cardId){
		Stripe stripe = roStripeService.findByEvent(event);
		Optional<Payment> paymentOptional = this.paymentService.findByUserIdAndEventId(user.getUserId(),
				event.getEventId());

		if (paymentOptional.isPresent() && stripe != null) {
			String customerId = paymentOptional.get().getStripeToken();
			String apiKey = stripe.getAccessToken();
			if (isNotBlank(customerId) && isNotBlank(apiKey)) {
				try{
					allPaymentService.updateUserCard(apiKey, customerId, cardId, stripe, event, user);
				} catch (StripeException stripeException) {
                    log.info("EventServiceImpl | updateUserCardStatus | StripeException {}",stripeException.getStripeError().getMessage());
                    throw new NotAcceptableException(stripeException.getStripeError().getCode(),stripeException.getStripeError().getMessage(),stripeException.getStripeError().getMessage());
                }catch (com.squareup.square.exceptions.ApiException apiException){
                    log.info("EventServiceImpl updateUserCardStatus SquareException {}",apiException);
                    log.info("EventServiceImpl updateUserCardStatus SquareException errors {}",apiException.getErrors());
                    log.info("EventServiceImpl updateUserCardStatus SquareException ResponseCode {}", apiException.getResponseCode());
                    log.info("EventServiceImpl updateUserCardStatus SquareException {} ",apiException.getMessage());

                    throw new NotAcceptableException("4066001", getErrorMessage(apiException), getErrorMessage(apiException));
                }
			} else {
				throw new NotFoundException(NotFound.STRIPE_CUSTOMER);
			}
		} else {
			throw new NotFoundException(NotFound.PAYMENT_DETAIL);
		}
	}

    @Override
    public boolean validateEventDeletion(Event event){
        long userCount = getCountOfMembersForEvent(event);
        Long stripeTransaction = stripeTransactionService.getTheSumOfAttendeeUsersFromAllTheModule(event.getEventId());
        userCount += (null != stripeTransaction)? stripeTransaction : 0 ;
        return userCount<=Constants.MAX_ATTENDEE_TO_DELETE_EVENT;
    }

    private Long getCountOfMembersForEvent(Event event) {
        try{
            return checkInAuditLogService.countDistinctUsersByEventId(event.getEventId());
        } catch (Exception e) {
            return 0L;
        }
    }

    @Override
    @Transactional
    public void mapOrganizerSubscriptionIdToEvent(Event event) {
        if (event.getWhiteLabel() != null) {
            event.setSubscriptionId(event.getWhiteLabel().getSubscriptionId());
        } else {
            if (event.getOrganizer() != null) {
                event.setSubscriptionId(event.getOrganizer().getSubscriptionId());
            }
        }
        eventRepoService.save(event);
    }

    @Override
    public String convertEventUrlBySeparateWords(String eventUrl) {
        if (StringUtils.isNotBlank(eventUrl)) {
            eventUrl = StringUtils.normalizeSpace(eventUrl);
            eventUrl = eventUrl.replaceAll(ATOZ_BOTHCASE_AND_NUMBERS_AND_HYPHEN, STRING_EMPTY);

            if(eventUrl.length() > 50) {
                eventUrl = eventUrl.substring(0, 50);
            }
            if (eventUrl.matches(ATOZ_LOWERCASE)) {
                return eventUrl;
            } else if (eventUrl.matches(ATOZ_UPPERCASE)) {
                return eventUrl.toLowerCase();
            } else {
                eventUrl = eventUrl.toLowerCase();
                eventUrl = eventUrl.replaceAll(WHITE_SPACE, HYPHEN);
                eventUrl = eventUrl.replaceAll(DOUBLE_HYPHEN, HYPHEN);
                eventUrl = (!StringUtils.isBlank(eventUrl)  && eventUrl.substring(0, 1).equalsIgnoreCase("-"))
                        ? eventUrl.substring(1) : eventUrl;
                return eventUrl;
            }
        }else{
            return eventUrl;
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    public void switchRecurringEventToSingleDayEvent(Event event) {
        log.info("switchRecurringEventToSingleDayEvent | eventId {}", event.getEventId());
        Ticketing ticketing = ticketingRepository.findByEventid(event);
        if (ticketing.isRecurringEvent()) {
            validateEventEnded(ticketing, DateUtils.getCurrentDate());

            List<RecurringEvents> recurringEventsList = recurringEventsScheduleService.getRecurringEventsByEvent(event);
            List<Long> recEventIdList = recurringEventsList.stream().map(RecurringEvents::getId).collect(Collectors.toList());

            List<EventTickets> eventTicketsWithRefundAndDeleted = new ArrayList<>();
            List<Object[]> countSoldTicketList = new ArrayList<>();

            if (!recEventIdList.isEmpty()) {
                eventTicketsWithRefundAndDeleted = eventTicketsRepository.findByTicketStatusRefundesAndDelete(recEventIdList, TicketStatus.CANCELED);
                countSoldTicketList = eventTicketsRepository.countSoldTicketsByRecurringEventId(recEventIdList, Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
            }

            if (countSoldTicketList.isEmpty()) {
                if (!eventTicketsWithRefundAndDeleted.isEmpty()) {
                    List<Long> recEventIds = eventTicketsWithRefundAndDeleted.stream().map(EventTickets::getRecurringEventId).collect(Collectors.toList());
                    updateStatusToCancelForDeletedTickets(eventTicketsWithRefundAndDeleted, event);
                    recurringEventsList = recurringEventsList.stream().filter(e -> !recEventIds.contains(e.getId())).collect(Collectors.toList());
                }

                recurringEventsList = getUpdatedRecurringEventsList(event, recurringEventsList);
                recurringEventsScheduleService.deleteRecurringEventsScheduleByIdForSwichRecurringEvent(event, ticketing, recurringEventsList);

            } else if (countSoldTicketList.size() == 1) {
                if (!eventTicketsWithRefundAndDeleted.isEmpty()) {
                    List<Long> recEventIds = eventTicketsWithRefundAndDeleted.stream().map(EventTickets::getRecurringEventId).collect(Collectors.toList());

                    updateTicketTypeAndEventTicketsHasCreatedFormLessThenZero(eventTicketsWithRefundAndDeleted);
                    updateTicketTypeAndTicketingOrderManagersHasCreatedFormLessThenZero(eventTicketsWithRefundAndDeleted);
                    updateStatusToCancelForDeletedTickets(eventTicketsWithRefundAndDeleted, event);

                    recurringEventsList = recurringEventsList.stream().filter(e -> !recEventIds.contains(e.getId())).collect(Collectors.toList());
                }

                Optional<RecurringEvents> finalRecurringEvents = getFinalRecurringEventHavingSoldTicket(recurringEventsList, countSoldTicketList);

                recurringEventsList = getUpdatedRecurringEventsList(event, recurringEventsList);
                convertRecurringEventToNormalEvent(event, finalRecurringEvents, recurringEventsList, ticketing);
            } else {
                throw new NotAcceptableException(NotAceptableExeceptionMSG.CAN_NOT_CONVERT_RECURRING_EVENT_TO_SINGLE_DAY_EVENT);
            }
        } else {
            throw new NotAcceptableException(NotAceptableExeceptionMSG.ALREADY_SINGLE_DAY_EVENT);
        }
    }

    public List<RecurringEvents> getUpdatedRecurringEventsList(Event event, List<RecurringEvents> recurringEventsList) {
        List<Long> needsToBeRemovedRecIds = getRecurringEventsListNeedsToBeRemoveFromList(event);
        return recurringEventsList.stream().filter(e -> !needsToBeRemovedRecIds.contains(e.getId())).collect(Collectors.toList());
    }

    public Optional<RecurringEvents> getFinalRecurringEventHavingSoldTicket(List<RecurringEvents> recurringEventsList, List<Object[]> countSoldTicketList) {
        Long recurringId = (Long) countSoldTicketList.get(0)[0];
        return recurringEventsList.stream().filter(e -> e.getId() == recurringId).findFirst();
    }

    public List<Long> getRecurringEventsListNeedsToBeRemoveFromList(Event event) {
        List<TicketingOrder> ordersWhichAreOnlyCreatedAndExpired = ticketingOrderRepoService.findListByEventIdAndStatusHavingStatusCreateAndExpired(event);

        List<Long> recEventIdsNeedsToBeRemove = getRecEventIdsToBeRemoved(ordersWhichAreOnlyCreatedAndExpired);

        if (!CollectionUtils.isEmpty(ordersWhichAreOnlyCreatedAndExpired)) {
            updateRecordsToCancel(event, ordersWhichAreOnlyCreatedAndExpired.stream().map(TicketingOrder::getId).collect(Collectors.toList()));
        }

        return recEventIdsNeedsToBeRemove;
    }

    public List<Long> getRecEventIdsToBeRemoved(List<TicketingOrder> ordersWhichAreOnlyCreatedAndExpired) {
	    if(CollectionUtils.isEmpty(ordersWhichAreOnlyCreatedAndExpired)){
	        return Collections.emptyList();
        }
        List<TicketingOrderManager> orderManagerOfDeletedTickets = ticketingOrderManagerService.findAllByOrderIds(ordersWhichAreOnlyCreatedAndExpired.stream().map(TicketingOrder::getId).collect(Collectors.toList()));
        return orderManagerOfDeletedTickets.stream().map(TicketingOrderManager::getRecurringEventId).collect(Collectors.toList());
    }


    private void validateEventEnded(Ticketing ticketing, Date currentDate) {
	    if((currentDate.compareTo(ticketing.getEventEndDate())) > 0){
            throw new NotAcceptableException(CAN_NOT_CHANGE_AFTER_EVENT_ENDED);
        }
    }


    protected void convertRecurringEventToNormalEvent(Event event, Optional<RecurringEvents> finalRecurringEventsOpt, List<RecurringEvents> recurringEventsList,
                                                      Ticketing ticketing) {

        RecurringEvents finalRecurringEvents = finalRecurringEventsOpt.get();//NOSONAR

        List<EventTickets> eventTickets = eventTicketsRepoService.findByRecurringEventId(finalRecurringEvents.getId());
        List<TicketingOrderManager> ticketingOrderManagers = ticketingOrderManagerService.findByRecurringEventId(finalRecurringEvents.getId());
        List<EventTickets> createdFormLessThenZero = eventTickets.stream().filter(e -> e.getTicketingTypeId().getCreatedFrom() < 0).collect(Collectors.toList());

        if (!createdFormLessThenZero.isEmpty()) {
            updateTicketTypeAndEventTicketsHasCreatedFormLessThenZero(createdFormLessThenZero);

            ticketingOrderManagers.removeAll(updateTicketTypeAndTicketingOrderManagersHasCreatedFormLessThenZero(createdFormLessThenZero));
            eventTickets.removeAll(createdFormLessThenZero);
        }

        List<TicketingOrder> ticketingOrders = ticketingOrderRepoService.findByEventId(event);
        ticketingOrders = ticketingOrders.stream().filter(tOrder -> tOrder.getTicketingCoupon() != null).collect(Collectors.toList());

        eventTickets.forEach(eventTicket -> {
            TicketingType defaultId = ticketingTypeService.findByid(eventTicket.getTicketingTypeId().getCreatedFrom());
            eventTicket.setTicketingTypeId(defaultId);
            eventTicket.setRecurringEventId(null);
        });

        ticketingOrderManagers.forEach(ticketingOrderManager -> {
            TicketingType defaultId = ticketingTypeService.findByid(ticketingOrderManager.getTicketType().getCreatedFrom());
            ticketingOrderManager.setTicketType(defaultId);
            ticketingOrderManager.setRecurringEventId(null);
        });

        ticketingOrders.forEach(ticketingOrder -> {
            Optional<TicketingCoupon> defaultCoupon = ticketingCouponService.findById(ticketingOrder.getTicketingCoupon().getCreatedFrom());
            ticketingOrder.setTicketingCoupon(defaultCoupon.get());
        });

        updateTicketingForRecurringEventToSingleDayEvent(ticketing, finalRecurringEvents);
        eventTicketsRepoService.saveAll(eventTickets);
        ticketingOrderManagerService.saveAll(ticketingOrderManagers);
        ticketingOrderRepoService.saveAll(ticketingOrders);

        if(!CollectionUtils.isEmpty(recurringEventsList)){
            List<Long> recurringEventsToBeDelete = recurringEventsList.stream().map(RecurringEvents::getId).collect(Collectors.toList());
            List<Long> scheduleIdsToBeDeleted = recurringEventsList.stream().map(RecurringEvents::getRecurringEventScheduleId).collect(Collectors.toList());

            ticketingCouponService.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            ticketingAccessCodeService.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            ticketHolderRequiredAttributesService.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            registrationAttributeRepository.deleteByRecurringIdAndEventId(recurringEventsToBeDelete, event);
            ticketingTypeService.deleteByRecurringEventIdIn(recurringEventsToBeDelete);
            ticketingTypeService.updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(ticketing);

            recurringEventsRepository.deleteAll(recurringEventsList);
            recurringEventsScheduleService.deleteByIdIn(scheduleIdsToBeDeleted);
        }
    }


    private List<TicketingOrderManager> updateTicketTypeAndTicketingOrderManagersHasCreatedFormLessThenZero(List<EventTickets> createdFormLessThenZero) {
        List<TicketingType> ticketTypes = createdFormLessThenZero.stream().map(EventTickets::getTicketingTypeId).collect(Collectors.toList());
        List<TicketingOrderManager> ticketingOrderManagersHasCreatedFormLessThenZero = ticketingOrderManagerService.findByTicketTypeId(ticketTypes);

        ticketingOrderManagersHasCreatedFormLessThenZero.forEach(ticketingOrderManager -> ticketingOrderManager.setRecurringEventId(null));
        ticketingOrderManagerService.saveAll(ticketingOrderManagersHasCreatedFormLessThenZero);
        return ticketingOrderManagersHasCreatedFormLessThenZero;
    }


    public void updateStatusToCancelForDeletedTickets(List<EventTickets> eventTicketsWithRefundAndDeleted, Event event) {

	    List<Long> orderIds = eventTicketsWithRefundAndDeleted.stream().map(e-> e.getTicketingOrder().getId()).collect(Collectors.toList());
        eventTicketsWithRefundAndDeleted.forEach(e-> e.setRecordStatus(RecordStatus.CANCEL));
        eventTicketsRepository.saveAll(eventTicketsWithRefundAndDeleted);

        updateRecordsToCancel(event, orderIds);

    }

    private void updateRecordsToCancel(Event event, List<Long> orderIds) {
        List<TicketingOrderManager> orderManagerOfDeletedTickets = ticketingOrderManagerService.findAllByOrderIds(orderIds);
        orderManagerOfDeletedTickets.forEach(e-> e.setStatus(RecordStatus.CANCEL));
        ticketingOrderManagerService.saveAll(orderManagerOfDeletedTickets);


	    List<TicketingType> ticketTypeDeleted = orderManagerOfDeletedTickets.stream().map(TicketingOrderManager::getTicketType).collect(Collectors.toList());
        ticketTypeDeleted.forEach(e -> e.setStatus(RecordStatus.CANCEL));
        ticketingTypeService.saveAll(ticketTypeDeleted);

        List<TicketingOrder> ordersDeleted = orderManagerOfDeletedTickets.stream().map(TicketingOrderManager::getOrderId).collect(Collectors.toList());
        ordersDeleted.forEach(e-> e.setRecordStatus(RecordStatus.CANCEL));
        ticketingOrderRepoService.saveAll(ordersDeleted);

        List<RecurringEvents> recurringEventsHavingOrderDeleted = orderManagerOfDeletedTickets.stream().map(TicketingOrderManager::getRecurringEvents).filter(Objects::nonNull).collect(Collectors.toList());//NOSONAR
        recurringEventsHavingOrderDeleted.forEach(e-> e.setStatus(RecurringEvents.RecurringEventStatus.CANCEL));
        recurringEventsRepository.saveAll(recurringEventsHavingOrderDeleted);

        List<RecurringEventSchedule> recurringEventSchedulesHavingOrderDelete = recurringEventsHavingOrderDeleted.stream().map(RecurringEvents::getRecurringEventSchedule).filter(Objects::nonNull).collect(Collectors.toList());//NOSONAR
        recurringEventSchedulesHavingOrderDelete.forEach(e-> e.setStatus(RecurringEventSchedule.EnumScheduleStatus.CANCEL));
        recurringEventsScheduleService.saveAll(recurringEventSchedulesHavingOrderDelete);

        List<Long> recurringEventIds = recurringEventsHavingOrderDeleted.stream().map(RecurringEvents::getId).collect(Collectors.toList());
        recurringEventIds = recurringEventIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        ticketingCouponService.updateTicketCouponRecStatusByRecurringEventIds(recurringEventIds,event.getEventId(), RecordStatus.CANCEL);
        ticketingAccessCodeService.updateAccessCodeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        ticketingLimitedDisplayCodeService.updateTicketDisplayCodeRecStatusByRecurringEventIds(recurringEventIds,event.getEventId(), RecordStatus.CANCEL);
        ticketHolderRequiredAttributesService.updateTicketHolderAttributeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        embedWidgetSettingService.updateEmbedwidgetSettingRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
    }

    private void updateTicketTypeAndEventTicketsHasCreatedFormLessThenZero(List<EventTickets> createdFormLessThenZero) {

        createdFormLessThenZero.forEach(eTickets -> {
            TicketingType ticketingType = eTickets.getTicketingTypeId();
            if (ticketingType.getCreatedFrom() > 0) {
                TicketingType defaultId = ticketingTypeService.findByid(eTickets.getTicketingTypeId().getCreatedFrom());
                eTickets.setTicketingTypeId(defaultId);
                eTickets.setRecurringEventId(null);
            } else {
                ticketingType.setCreatedFrom(null);
                ticketingType.setRecurringEventId(null);
                eTickets.setRecurringEventId(null);
                ticketingTypeService.save(ticketingType);
            }
        });
        eventTicketsRepoService.saveAll(createdFormLessThenZero);
    }

    private void updateTicketingForRecurringEventToSingleDayEvent(Ticketing ticketing, RecurringEvents finalRecurringEvents) {

        ticketing.setRecurringEvent(false);
        ticketing.setEventStartDate(finalRecurringEvents.getRecurringEventStartDate());
        ticketing.setEventEndDate(finalRecurringEvents.getRecurringEventEndDate());
        ticketingService.save(ticketing);
    }

    @Override
    public List<EventNameLookUpDto> getAllEventsByOrganizerId(Long organizerId) {
        return eventRepoService.getAllEventsByOrganizerId(organizerId);
    }

    @Override
    @Transactional
    public ResponseDto bulkDeleteEvents(User user, EventIdsDto eventIdsDto) {

        DeletedEventsDto deletedEventsDto = new DeletedEventsDto("Bulk Delete",
                "Deleted events through super admin");


        if(!CollectionUtils.isEmpty(eventIdsDto.getEventId())){
            List<Event> events = eventRepoService.findByEventIdsAndNotOrganizerPlanConfig(eventIdsDto.getEventId(), chargebeePlanService.getFreePlanConfig());

            if(!CollectionUtils.isEmpty(events)){
                throw new NotAcceptableException(NotAceptableExeceptionMSG.EVENT_CONNECTED_WITH_PAID_ORGANIZER);
            }

            List<Session> sessions = sessionRepoService.getSessionByEventIds(eventIdsDto.getEventId());

            Map<Long, Long> map = sessions.stream().collect(Collectors.groupingBy(Session::getEventId, Collectors.counting()));

            if(map.entrySet().stream().anyMatch(e-> e.getValue() > 2)){
                throw new NotAcceptableException(NotAceptableExeceptionMSG.EVENT_HAS_MORE_THEN_TWO_SESSION);
            }

            List<Staff> staff = staffService.getListOfStaffByEventIdsAndRole(eventIdsDto.getEventId(), StaffRole.admin);

            map = staff.stream().collect(Collectors.groupingBy(Staff::getEventId, Collectors.counting()));

            if(map.entrySet().stream().anyMatch(e-> e.getValue() > 2)){
                throw new NotAcceptableException(NotAceptableExeceptionMSG.EVENT_HAS_MORE_THEN_TWO_ADMIN);
            }


            eventIdsDto.getEventId().forEach(eventId -> {
                long expoCount = exhibitorRepoService.countByEventId(eventId);

                if(expoCount > 0){
                    throw new NotAcceptableException(NotAceptableExeceptionMSG.EVENT_HAS_EXPO_CREATED);
                }

                List<AttendeeAnalyticStatusDTO> analyticStatusDTOS = attendeeAnalyticsService.getAttendeesCountByEventByTicketStatus(
                        eventRepoService.findEventByIdOrThrowError(eventId));

                if(!CollectionUtils.isEmpty(analyticStatusDTOS) &&analyticStatusDTOS.get(1).getCount().intValue() > 3){
                    throw new NotAcceptableException(NotAceptableExeceptionMSG.EVENT_HAS_MORE_THEN_THREE_REG);
                }

                deleteEvent(eventId, user, deletedEventsDto);
            });
        }
        return new ResponseDto(SUCCESS, EVENTS_DELETED_SUCCESSFULLY);
    }

    @Override
    public Event getEventByURLOrThrowException(String eventUrl) {
        return eventRepoService.getEventByURLOrThrowException(eventUrl);
    }

    private void deleteApiUserFromStaffForOldOrganizer(Event event, Organizer oldOrganizer, Organizer organizer) {
        if(oldOrganizer==null && organizer!=null) {
            addNewApiUserToStaff(organizer, event, null);
        } else if(oldOrganizer!=null && !oldOrganizer.getOrganizerPageURL().equals(organizer.getOrganizerPageURL())){
            updateDeleteStatusToApiUser(event, oldOrganizer, null);
            addNewApiUserToStaff(organizer, event, null);
        }
    }

    @Override
    @Transactional
    public void updateDeleteStatusToApiUser(Event event, Organizer organizer, WhiteLabel whiteLabel) {
        if (whiteLabel != null) {
            Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByWhiteLabelId(whiteLabel.getId());
            if (apiKeyOptional.isPresent()) {
                Staff staff = roStaffService.findApiUserByEventAndUserAndRoleAndApiUser(apiKeyOptional.get().getUserId(), event.getEventId());
                if (staff != null) {
                    staff.setRecordStatus(RecordStatus.DELETE);
                    staffService.save(staff);
                }
            }
        } else if(organizer!=null) {
            Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizer.getId());
            if (apiKeyOptional.isPresent()) {
                Staff staff = roStaffService.findApiUserByEventAndUserAndRoleAndApiUser(apiKeyOptional.get().getUserId(), event.getEventId());
                if (staff != null) {
                    staff.setRecordStatus(RecordStatus.DELETE);
                    staffService.save(staff);
                }
            }
        }
    }

    @Override
    public void addNewApiUserToStaff(Organizer organizer, Event event, WhiteLabel whiteLabel) {
        if(whiteLabel != null) {
            Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByWhiteLabelId(whiteLabel.getId());
            if (apiKeyOptional.isPresent()) {
                User user = userService.findById(apiKeyOptional.get().getUserId());
                apiKeyService.createApiUserInStaffForGivenEvent(user, event, StaffRole.admin, whiteLabel);
            }
        } else if(organizer!=null) {
            Optional<ClientApiKey> apiKeyOptional = apiKeyService.findApiKeyByOrganizerId(organizer.getId());
            if (apiKeyOptional.isPresent()) {
                User user = userService.findById(apiKeyOptional.get().getUserId());
                apiKeyService.createApiUserInStaffForGivenEvent(user, event, StaffRole.admin, null);
            }
        }
    }

    public void setEventDesignDetailsSetting(Event event ,WhiteLabel whiteLabel){
      EventDesignDetail eventDesignDetail = eventDesignDetailRepository.findByEvent(event);
      eventDesignDetail.setLogoImage(whiteLabel.getLogoImage());
      eventDesignDetail.setHeaderColor(whiteLabel.getHeaderColor());
      eventDesignDetail.setBannerImage(whiteLabel.getBannerImage());
      eventDesignDetailService.save(eventDesignDetail);
    }

    @Override
    public List<String> isPaymentGateWayConnectedToOrganizerEvents(List<Object[]> eventList) {
        List<String> eventErrorList = new ArrayList<>();
        for (Object[] obj : eventList) {
            Long eventId = Long.parseLong(obj[8].toString());
            Event event = eventRepoService.findEventByIdOrThrowError(eventId);
            Stripe stripeByEvent = roStripeService.findByEvent(event);
            if (isNotBlank(stripeByEvent.getAccessToken()) && this.paymentService.isCustomerCreatedForEvent(event)) {
                String paymentGateway = stripeByEvent.getPaymentGateway();
                eventErrorList.add(Constants.EVENT_CONNECTED_TO_PAYMENT_GATEWAY_AND_CUSTOMER_CREATED.replace("{eventName}",event.getName()).
                        replace(Constants.PAYMENT_GATEWAY, paymentGateway));
            }
        }
        return eventErrorList;
    }

    @Override
    public void updateEventBillingId(Event event, String eventBillingId, User user) {
        Event dbEvent = roEventService.getEventById(event.getEventId());

        com.accelevents.domain.WhiteLabel whiteLabel = dbEvent.getWhiteLabel();
        if (null == whiteLabel) {
            throw new NotAcceptableException(WhiteLabelExceptionMsg.NOT_WHITELABEL_EVENT);
        }

        if (Boolean.FALSE.equals(stripeService.isStripeConnected(dbEvent))) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.EVENT_IS_NOT_CONNECTED_WITH_PAYMENT);
        }

        Stripe stripe = stripeService.findByEventWithEmailDisplayName(dbEvent);
        if ("STRIPE".equalsIgnoreCase(stripe.getPaymentGateway()) || "PAYFLOW".equalsIgnoreCase(stripe.getPaymentGateway())) {
            stripe.setEventBillingId(eventBillingId);
            stripeService.save(stripe);
        }

	}

    @Override
    public Map<String,Map<String,Map<String,String>>> getUTMTrackSourceGraphDetails(Event event, Long recurringEventId) {
        List<TicketingOrder.TicketingOrderStatus> statusList = new ArrayList<>();
        statusList.add(TicketingOrder.TicketingOrderStatus.PAID);
        statusList.add(TicketingOrder.TicketingOrderStatus.UNPAID);
        List<TicketingOrder> ticketingOrderList =  ticketingOrderRepoService.findAllByEventIdAndStatusOrOrderTypeOrderByOrderDateDesc(event,statusList,recurringEventId);

        Map<String,Map<String,Map<String,String>>> map = new HashMap<>();
        List<UTMTrackSourceDto> utmTrackSourceDtoList = ticketingOrderList.stream().filter(ticketingOrder -> StringUtils.isNotBlank(ticketingOrder.getRecSource())).map(ticketingOrder ->JsonMapper.stringtoObject(ticketingOrder.getRecSource(), UTMTrackSourceDto.class)).collect(Collectors.toList());

        Map<String, List<UTMTrackSourceDto>> source = new HashMap<>();
        Map<String, List<UTMTrackSourceDto>> campaign = new HashMap<>();
        Map<String, List<UTMTrackSourceDto>> content = new HashMap<>();
        Map<String, List<UTMTrackSourceDto>> term = new HashMap<>();
        Map<String, List<UTMTrackSourceDto>> medium = new HashMap<>();
        Map<String, List<UTMTrackSourceDto>> referrer = new HashMap<>();
        utmTrackSourceDtoList.forEach(utmTrackSourceDto -> setMapValuesFromList(source, campaign, content, term, medium, referrer, utmTrackSourceDto));
        setDataForPercentageMap(map, utmTrackSourceDtoList, source, campaign, content, term, medium, referrer);
        return  map;
    }

    private void setMapValuesFromList(Map<String, List<UTMTrackSourceDto>> source, Map<String, List<UTMTrackSourceDto>> campaign, Map<String, List<UTMTrackSourceDto>> content, Map<String, List<UTMTrackSourceDto>> term, Map<String, List<UTMTrackSourceDto>> medium, Map<String, List<UTMTrackSourceDto>> referrer, UTMTrackSourceDto utmTrackSourceDto) {
        if (StringUtils.isNotBlank(utmTrackSourceDto.getUtmSource())) {
            setDataInMap(source, utmTrackSourceDto, utmTrackSourceDto.getUtmSource());
        }
        if (StringUtils.isNotBlank(utmTrackSourceDto.getUtmCampaign())) {
            setDataInMap(campaign, utmTrackSourceDto, utmTrackSourceDto.getUtmCampaign());
        }
        if (StringUtils.isNotBlank(utmTrackSourceDto.getUtmContent())) {
            setDataInMap(content, utmTrackSourceDto, utmTrackSourceDto.getUtmContent());
        }
        if (StringUtils.isNotBlank(utmTrackSourceDto.getUtmTerm())) {
            setDataInMap(term, utmTrackSourceDto, utmTrackSourceDto.getUtmTerm());
        }
        if (StringUtils.isNotBlank(utmTrackSourceDto.getUtmMedium())) {
            setDataInMap(medium, utmTrackSourceDto, utmTrackSourceDto.getUtmMedium());
        }
        if (StringUtils.isNotBlank(utmTrackSourceDto.getUtmReferrer())) {
            setDataInMap(referrer, utmTrackSourceDto, utmTrackSourceDto.getUtmReferrer());
        }
    }

    private void setDataForPercentageMap(Map<String, Map<String, Map<String, String>>> map, List<UTMTrackSourceDto> utmTrackSourceDtoList, Map<String, List<UTMTrackSourceDto>> source, Map<String, List<UTMTrackSourceDto>> campaign, Map<String, List<UTMTrackSourceDto>> content, Map<String, List<UTMTrackSourceDto>> term, Map<String, List<UTMTrackSourceDto>> medium, Map<String, List<UTMTrackSourceDto>> referrer) {
        if (!source.isEmpty()) {
            Long count = utmTrackSourceDtoList.stream().filter(utm->StringUtils.isNotBlank(utm.getUtmSource())).count();
            setPercentageAndCountInMap(map, count, source, HEADER_UTM_SOURCE);
        } else {
            map.put(HEADER_UTM_SOURCE,Collections.emptyMap());
        }
	    if (!campaign.isEmpty()) {
            Long count = utmTrackSourceDtoList.stream().filter(utm->StringUtils.isNotBlank(utm.getUtmCampaign())).count();
            setPercentageAndCountInMap(map, count, campaign, HEADER_UTM_CAMPAIGN);
        } else {
            map.put(HEADER_UTM_CAMPAIGN,Collections.emptyMap());
        }
        if (!content.isEmpty()) {
            Long count = utmTrackSourceDtoList.stream().filter(utm->StringUtils.isNotBlank(utm.getUtmContent())).count();
            setPercentageAndCountInMap(map, count, content, HEADER_UTM_CONTENT);
        } else {
            map.put(HEADER_UTM_CONTENT,Collections.emptyMap());
        }
        if (!term.isEmpty()) {
            Long count = utmTrackSourceDtoList.stream().filter(utm->StringUtils.isNotBlank(utm.getUtmTerm())).count();
            setPercentageAndCountInMap(map, count, term, HEADER_UTM_TERM);
        } else {
            map.put(HEADER_UTM_TERM,Collections.emptyMap());
        }
        if (!medium.isEmpty()) {
            Long count = utmTrackSourceDtoList.stream().filter(utm->StringUtils.isNotBlank(utm.getUtmMedium())).count();
            setPercentageAndCountInMap(map, count, medium, HEADER_UTM_MEDIUM);
        } else {
            map.put(HEADER_UTM_MEDIUM,Collections.emptyMap());
        }
        if (!referrer.isEmpty()) {
            Long count = utmTrackSourceDtoList.stream().filter(utm->StringUtils.isNotBlank(utm.getUtmReferrer())).count();
            setPercentageAndCountInMap(map, count, referrer, HEADER_UTM_REFERRER);
        } else {
            map.put(HEADER_UTM_REFERRER,Collections.emptyMap());
        }
    }

    private void setPercentageAndCountInMap(Map<String, Map<String, Map<String,String>>> map,Long totalSize, Map<String, List<UTMTrackSourceDto>> dataMap, String key) {
        Map<String,  Map<String, String> > percentageMap = new HashMap<>();
        dataMap.forEach((key1, value) -> {
            Map<String, String> countAndPercentageMap = new HashMap<>();
            countAndPercentageMap.put("Count", String.valueOf(value.size()));
            countAndPercentageMap.put("Percentage", String.valueOf(getRoundValue((double) (value.size() * 100) / totalSize)));
            percentageMap.put(key1, countAndPercentageMap);
        });
        map.put(key, percentageMap);
    }

    private void setDataInMap(Map<String, List<UTMTrackSourceDto>> map, UTMTrackSourceDto utmTrackSourceDto, String key) {
        if (map.containsKey(key)) {
            List<UTMTrackSourceDto> list = map.get(key);
            if (CollectionUtils.isEmpty(list)) {
                map.put(key, new ArrayList<>(Collections.singletonList(utmTrackSourceDto)));
            } else {
                list.add(utmTrackSourceDto);
                map.put(key, list);
            }
        } else {
            map.put(key,new ArrayList<>(Collections.singletonList(utmTrackSourceDto)));
        }
    }

    @Override
    public AttendeesUploadCharge getAttendeesUploadCharge(Long eventId) {
        return eventRepoService.getAttendeeUploadCharge(eventId);
    }

    public void updateEventAttendeeUploadToggle(Event event) {
        boolean isAttendeeUploadWaiveOff = true;
        if (event.getWhiteLabel() != null) {
            isAttendeeUploadWaiveOff = whiteLabelRepoService.findWaiveOffAttendeeUploadByWhiteLabelId(event.getWhiteLabelId());
        } else if(event.getOrganizer() != null) {
            isAttendeeUploadWaiveOff = organizerRepository.findWaiveOffAttendeeUploadFlag(event.getOrganizerId());
        }
        event.setAttendeesUploadCharge(isAttendeeUploadWaiveOff ? AttendeesUploadCharge.WAIVE_OFF : AttendeesUploadCharge.CHARGE);
    }

    @Override
    public DataTableResponse getAllAttendeesList(Event event, User loggedInUser, PageSizeSearchObj pageSizeSearchObj) {
        Page<User> userPage = eventCommonRepoService.findAllTicketHolderUserByEvent(event, pageSizeSearchObj.getSearchWithEscapeSpecialChars(),
                PageRequest.of(pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize()));
        List<User> users = userPage.getContent();

            List<AttendeeProfileDto> attendees = users.stream().map(user -> {
                AttendeeProfileDto attendeeProfileDto = new AttendeeProfileDto();
                attendeeProfileDto.setId(String.valueOf(user.getUserId()));
                attendeeProfileDto.setUserId(user.getUserId());
                attendeeProfileDto.setFirstName(user.getFirstName());
                attendeeProfileDto.setLastName(user.getLastName());
                attendeeProfileDto.setEmail(user.getEmail());
                attendeeProfileDto.setPhoto(user.getPhoto());
                return attendeeProfileDto;
            }).collect(Collectors.toList());
            log.info("Fetched ticket holder user {}, attendees {}", userPage.getTotalElements(), attendees.size());
            DataTableResponse dataTableResponse = new DataTableResponse();
            dataTableResponse.setData(attendees);
            dataTableResponse.setRecordsTotal(userPage.getTotalElements());
            dataTableResponse.setRecordsFiltered(attendees.size());
            return dataTableResponse;
    }

    @Override
    public List<EventFeatureDTO> getEventFeaturesSummary(Event event) {

        List<EventFeatureDTO> eventFeatureDTOS = new ArrayList<>();
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(event.getEventId());
        CAPUsageDto capUsageDto = null;
        PlatformConfigDto platformConfigDto = null;
        if(eventPlanConfig != null){
            capUsageDto  = convertJSONToObject(eventPlanConfig.getCapUsageConfigJson());
            platformConfigDto= JsonMapper.stringtoObject(eventPlanConfig.getPlatformConfigJson(), PlatformConfigDto.class);
        }else{
            log.info("eventPlanConfig is null for event {}", event.getEventId());
        }
        boolean isExpoAllowed = false;

        if (null != capUsageDto) {
            //currently, for basic functionality returning as not allowed
            isExpoAllowed = capUsageDto.getMaxExhibitorBooth() < 0 || capUsageDto.getMaxExhibitorBooth() > 5;
        }else {
            log.info("capUsageDto is null for event {}", event.getEventId());
        }

        long sessionCountByEventId = sessionRepoService.getSessionCountByEventId(event.getEventId());
        long numberOfNetworkingSessions = sessionRepoService.getSessionCountByEventIdAndSessionFormat(event.getEventId(), MEET_UP);
        long exhibitorCount = exhibitorRepoService.countByEventId(event.getEventId());
        boolean isAnalyticsAllowed = false;
        boolean isGamificationAllowed = false;

        if (null!=platformConfigDto) {
            isGamificationAllowed = platformConfigDto.getGamification().equals("custom");
            isAnalyticsAllowed = platformConfigDto.getSessionAnalytics().getAttendeesVideoViewAnalytics().equals("allow");
        }else {
            log.info("platformConfigDto is null for event {}", event.getEventId());
        }

        eventFeatureDTOS.add(new EventFeatureDTO(TICKETING_AND_REGISTRATION.getValue(), true, event.getTicketingEnabled()
                && ticketingTypeRepository.isTicketCreatedByTicketingId(event.getEventId()), event.getTicketingEnabled()));

        eventFeatureDTOS.add(new EventFeatureDTO(AGENDA_AND_SESSION.getValue(), true, sessionCountByEventId > 0,
                virtualEventSettings != null && (virtualEventSettings.isSessionEnabled() || virtualEventSettings.isStageEnabled() )));

        if (virtualEventSettings != null && virtualEventSettings.isNetworkingEnabled()) {
            eventFeatureDTOS.add(new EventFeatureDTO(EnumEventFeatures.NETWORKING.getValue(), true, numberOfNetworkingSessions > 0,
                    virtualEventSettings.isNetworkingEnabled()));
        }

        if (virtualEventSettings != null && !virtualEventSettings.isExpoEnabled()) {
            List<String> sponsorsNameList = sponsorsRepoService.getAllSponsorsNameByEvent(event);
            eventFeatureDTOS.add(new EventFeatureDTO(EnumEventFeatures.SPONSORS.getValue(), true, !sponsorsNameList.isEmpty(), true));
        } else {
            eventFeatureDTOS.add(new EventFeatureDTO(EXHIBITORS_AND_SPONSORS.getValue(), isExpoAllowed, exhibitorCount > 0, true));
        }
        eventFeatureDTOS.add(new EventFeatureDTO(ANALYTICS.getValue(), isAnalyticsAllowed, true, true));
        eventFeatureDTOS.add(new EventFeatureDTO(GAMIFICATION.getValue(), isGamificationAllowed, true, true));

        if (EventFormat.IN_PERSON.equals(event.getEventFormat()) || EventFormat.HYBRID.equals(event.getEventFormat())) {
            eventFeatureDTOS.add(new EventFeatureDTO(MOBILE_APP_ACCESS.getValue(), true, true, true));
            eventFeatureDTOS.add(new EventFeatureDTO(BADGE_PRINTING.getValue(), true, event.getTicketingEnabled(), event.getTicketingEnabled()));
        }

        return eventFeatureDTOS;
    }

    @Override
    public List<Event> getEventListByIdsAndOrganiserId(List<Long> ids, Long organizerId) {
        return !CollectionUtils.isEmpty(ids) ? eventRepoService.findAllByIdsAndOrganiserId(ids, organizerId) : Collections.emptyList();
    }

    @Override
    public void saveHostEventOfflinePaymentSettingConfig(Long eventId, HostEventOfflinePaymentConfigDTO hostEventOfflinePaymentConfigDTO, User user) {
        HostEventOfflinePaymentConfig optionalEntity = hostEventOfflinePaymentConfigService.findByEventId(eventId);
        log.info("request received for update offline payment setting for event {}, by {} | offlinePayment {} | mixPayNowOrLater {} | customTextShown {}",eventId, user.getUserId(), hostEventOfflinePaymentConfigDTO.isOfflinePayment(), hostEventOfflinePaymentConfigDTO.isMixPayNowOrLater(), hostEventOfflinePaymentConfigDTO.isCustomTextShown());
        if (null!=optionalEntity) {
            optionalEntity.setOfflinePayment(hostEventOfflinePaymentConfigDTO.isOfflinePayment());
            optionalEntity.setMixPayNowOrLater(hostEventOfflinePaymentConfigDTO.isMixPayNowOrLater());
            optionalEntity.setCustomTextShown(hostEventOfflinePaymentConfigDTO.isCustomTextShown());
            optionalEntity.setMessage(hostEventOfflinePaymentConfigDTO.getMessage());
            optionalEntity.setTitle(hostEventOfflinePaymentConfigDTO.getTitle());
            hostEventOfflinePaymentConfigService.save(optionalEntity);
        }
        if (null==optionalEntity && Boolean.TRUE.equals(hostEventOfflinePaymentConfigDTO.isOfflinePayment())) {
            hostEventOfflinePaymentConfigService.save(hostEventOfflinePaymentConfigDTO.toEntity(hostEventOfflinePaymentConfigDTO, eventId));
        }
        log.info("Successfully updated  update offline payment setting for event {}, by {} | offlinePayment {} | mixPayNowOrLater {} | customTextShown {}",eventId, user.getUserId(), hostEventOfflinePaymentConfigDTO.isOfflinePayment(), hostEventOfflinePaymentConfigDTO.isMixPayNowOrLater(), hostEventOfflinePaymentConfigDTO.isCustomTextShown());
    }

    @Override
    public HostEventOfflinePaymentConfigDTO getHostEventOfflinePaymentSettingConfig(Long eventId) {
        HostEventOfflinePaymentConfigDTO hostEventOfflinePaymentConfigDTO = new HostEventOfflinePaymentConfigDTO();
        HostEventOfflinePaymentConfig optionalEntity = hostEventOfflinePaymentConfigService.findByEventId(eventId);
        if (null!=optionalEntity) {
            hostEventOfflinePaymentConfigDTO.setOfflinePayment(optionalEntity.isOfflinePayment());
            hostEventOfflinePaymentConfigDTO.setMixPayNowOrLater(optionalEntity.isMixPayNowOrLater());
            hostEventOfflinePaymentConfigDTO.setCustomTextShown(optionalEntity.isCustomTextShown());
            hostEventOfflinePaymentConfigDTO.setMessage(optionalEntity.getMessage());
            hostEventOfflinePaymentConfigDTO.setTitle(optionalEntity.getTitle());
            return hostEventOfflinePaymentConfigDTO;
        }
        return hostEventOfflinePaymentConfigDTO;
    }

    @Override
    public HostEventOfflinePaymentConfigDTO getHostEventOfflinePaymentSettingConfig(String eventUrl) {
        Event event = roEventService.getEventByURL(eventUrl);
        return this.getHostEventOfflinePaymentSettingConfig(event.getEventId());
    }

    @Override
    public void cancelHostEventOfflinePayment(Event event) {
        HostEventOfflinePaymentConfig optionalEntity = hostEventOfflinePaymentConfigService.findByEventId(event.getEventId());
        if (null!=optionalEntity) {
            optionalEntity.setOfflinePayment(false);
            hostEventOfflinePaymentConfigService.save(optionalEntity);
            Ticketing ticketing = ticketingService.findByEvent(event);
            ticketing.setOfflinePayment(false);
            ticketingService.save(ticketing);
            List<TicketingType> list = ticketingTypeService.findAllTicketTypeByTicketing(ticketing);
            list.forEach(entityData-> {
                entityData.setPayLater(false);
                entityData.setRequireDepositAmount(false);
            });
            ticketingTypeService.saveAll(list);
        }
    }

    @Override
    public void saveEventStartTimeAsSystemTime(String startTime, String timeZone, Event event) {
        log.info("saveEventStartTimeAsSystemTime startTime {} and timeZone {} and eventId {}", startTime, timeZone, event.getEventId());
        if (startTime != null && timeZone != null) {
            event.setEquivalentTimeZone(timeZone);
            event.setTimezoneId(TimeZoneUtil.getNameByEquivalentTimeZone(timeZone));
            eventRepoService.save(event);
            Ticketing ticketing = ticketingService.findByEvent(event);
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat(LOCAL_DATE_FORMAT);
                log.info("saveEventStartTimeAsSystemTime dateFormat timeZone {} and eventId {}", dateFormat.getTimeZone(), event.getEventId());
                Calendar calendarForEventStartTime = Calendar.getInstance();
                Date parseTime = dateFormat.parse(startTime);
                log.info("saveEventStartTimeAsSystemTime {} eventId {}", parseTime, event.getEventId());
                calendarForEventStartTime.setTime(parseTime);
                calendarForEventStartTime.set(Calendar.HOUR_OF_DAY, 9);
                calendarForEventStartTime.set(Calendar.MINUTE, 0);
                calendarForEventStartTime.set(Calendar.SECOND, 0);
                calendarForEventStartTime.set(Calendar.MILLISECOND, 0);
                calendarForEventStartTime.add(Calendar.MINUTE,(30 * 1440));

                log.info("saveEventStartTimeAsSystemTime calendar timeZone {} and eventId {}", calendarForEventStartTime.getTimeZone(), event.getEventId());
                Date startTimeInUTC = TimeZoneUtil.getDateInUTC(calendarForEventStartTime.getTime(), timeZone);
                log.info("saveEventStartTimeAsSystemTime startTimeInUTC {} and eventId {}", startTimeInUTC, event.getEventId());
                ticketing.setEventStartDate(startTimeInUTC);


                Calendar calendarForEventEndTime = Calendar.getInstance();
                calendarForEventEndTime.setTime(parseTime);
                calendarForEventEndTime.set(Calendar.HOUR_OF_DAY, 0);
                calendarForEventEndTime.set(Calendar.MINUTE, 0);
                calendarForEventEndTime.set(Calendar.SECOND, 0);
                calendarForEventEndTime.set(Calendar.MILLISECOND, 0);
                calendarForEventEndTime.add(Calendar.MINUTE,(30 *1440));

                int eventMaxDays;
                String whiteLabelUrl = null != event.getWhiteLabel() ? event.getWhiteLabel().getWhiteLabelUrl() : null;
                String organiserUrl = null != event.getOrganizer() ? event.getOrganizer().getOrganizerPageURL() : null;
                if (null == organiserUrl && whiteLabelUrl == null) {
                    eventMaxDays = 1;
                } else {
                    eventMaxDays = virtualEventService.findEventMaxDays(whiteLabelUrl, organiserUrl, null, null,false);
                }
                calendarForEventEndTime.set(Calendar.MINUTE, (eventMaxDays * 1440) - 1);

                Date startEndInUTC = TimeZoneUtil.getDateInUTC(calendarForEventEndTime.getTime(), timeZone);
                log.info("saveEventStartTimeAsSystemTime {} eventId {}", startEndInUTC, event.getEventId());
                ticketing.setEventEndDate(startEndInUTC);
                if (startTimeInUTC.equals(startEndInUTC)) {
                    calendarForEventStartTime.add(Calendar.MINUTE, 1);
                    calendarForEventEndTime.add(Calendar.MINUTE, 1440);
                    startTimeInUTC = TimeZoneUtil.getDateInUTC(calendarForEventStartTime.getTime(), timeZone);
                    startEndInUTC = TimeZoneUtil.getDateInUTC(calendarForEventEndTime.getTime(), timeZone);
                    ticketing.setEventStartDate(startTimeInUTC);
                    ticketing.setEventEndDate(startEndInUTC);
                }
                ticketingService.save(ticketing);
            } catch (Exception e) {
                log.info("saveEventTimeAsSystemTime {}", e.getMessage());
            }
        }
    }
    private  Map<Long,List<SponsorsImageDto>> getSponsorsImageDtoMap(Set<Long> eventIds,String source){
        log.info("getSponsorsImageDtoMap using eventIds size {} , source {} ",eventIds.size(),source);
        if (MOBILE_APP.equals(source) && !eventIds.isEmpty()) {
            return  sponsorsService.getSponsorsImageDtoMap(eventIds);
        }else {
            return Collections.emptyMap();
        }
    }

    @Override
    public void unPublishEvent(Event event, User user) {
        event.setEventListingStatus(EventListingStatus.UNPUBLISHED);
        log.info("UnPublish the Event eventId{} eventListingStatus{} by user{}",event.getEventId(),event.getEventListingStatus(),user.getUserId());
        eventRepoService.save(event);
    }

    public Map<Long, Long> getRegistrantsCountByEventIds(List<Long> eventIds) {
        Map<Long, Long> eventRegistrantsOrUniqueAttendeeCountWithStaffsMap = new HashMap<>();
        List<EventPlanConfig> eventPlanConfigList = eventPlanConfigService.findEventPlanConfigByEventIds(eventIds);
        eventPlanConfigList.forEach(eventPlanConfig -> {
            Event event = eventPlanConfig.getEvent();
            int totalRegistrantsOrUniqueAttendeeCountWithStaffs;
            if (FREE_PLAN.getName().equals(eventPlanConfig.getPlanConfig().getPlanName()) || eventPlanConfig.getPlanConfig().isLatestPlan()) {
                totalRegistrantsOrUniqueAttendeeCountWithStaffs = eventTicketsService.getRegistrationCountIncludingStaffMembersAndExcludingAeAndBrilworksUserIds(event);
            } else {
                totalRegistrantsOrUniqueAttendeeCountWithStaffs = eventTicketsService.getCountOfUniqueAttendeeIncludingStaffMembersAndExcludingAeAndBrilworksUserIds(event);
            }
            eventRegistrantsOrUniqueAttendeeCountWithStaffsMap.put(eventPlanConfig.getEventId(), (long) totalRegistrantsOrUniqueAttendeeCountWithStaffs);
        });
        return eventRegistrantsOrUniqueAttendeeCountWithStaffsMap;
    }

    @Override
    public EventNameAndEventUrlDto validateEventUrl(EventNameAndEventUrlDto eventNameAndEventUrlDto,boolean isUrlChanged) {
        String eventName = eventNameAndEventUrlDto.getEventName();
        String eventUrl = eventNameAndEventUrlDto.getEventUrl();
        if(isUrlChanged) {
            // When eventURL has been changed by user, check if event exists with same url, return the formatted URL if unique
            if(StringUtils.isBlank(eventUrl.trim())){
                throw new NotAcceptableException(NotAcceptableException.EventExceptionMsg.EVENT_URL_CAN_NOT_EMPTY);
            }
            boolean isEventUrlExists = eventRepository.isEventUrlExist(eventUrl);
            if(isEventUrlExists){
                throw new ConflictException(UserExceptionConflictMsg.EVENT_URL_ALREADY_EXIST);
            }
            eventUrl = convertEventUrlBySeparateWords(eventUrl);
        } else {
            // when only event name changed, create new eventUrl based on eventName
            eventUrl = convertEventUrlBySeparateWords(eventName);
            eventUrl = this.getEventUrl(eventUrl,0,eventUrl.length(),false,null);
        }
        eventNameAndEventUrlDto.setEventName(eventName);
        eventNameAndEventUrlDto.setEventUrl(eventUrl);
        return eventNameAndEventUrlDto;
    }
    @Override
    public boolean isSaleForceRequiredForLoginByEventUrl(String eventUrl) {
        return Boolean.TRUE.equals(eventRepository.isSalesforceRequiredByEventsUrl(eventUrl));
    }

    private void updateEventNameInCustomTemplates(Long eventId, String oldName, String updatedName) {
        List<CustomTemplates> customTemplates = confirmationEmailRepository.findCustomTemplatesByEvent(eventId);
        if(!CollectionUtils.isEmpty(customTemplates)){
            customTemplates.forEach(customTemplate -> {
                if (StringUtils.isNotBlank(customTemplate.getSubjectLine())){
                    customTemplate.setSubjectLine(customTemplate.getSubjectLine().replace(oldName, updatedName));
                }
                if (StringUtils.isNotBlank(customTemplate.getBodyText())){
                    customTemplate.setBodyText(customTemplate.getBodyText().replace(oldName, updatedName));
                }
            });
            confirmationEmailRepository.saveAll(customTemplates);
        }
    }

    @Override
    public void saveEventClosingInfo(String eventUrl, EventClosingInfoDto eventClosingInfoDto, User user) {
        Event event = roEventService.getEventByURL(eventUrl);
        event.setBudgetSpent(eventClosingInfoDto.getBudgetSpent());
        event.setClosingNote(eventClosingInfoDto.getClosingNote());
        eventRepository.save(event);
        log.info("saveEventClosingInfo for eventUrl {} and user {} budgetSpent {} closingNote {}", eventUrl, user.getUserId(), eventClosingInfoDto.getBudgetSpent(), eventClosingInfoDto.getClosingNote());
    }

    @Override
    public void updateEventClosingEmailFlag(Long eventId, boolean isEventClosingEmailSent) {
        eventRepository.updateEventClosingEmailFlagByEventId(eventId,isEventClosingEmailSent);
    }

    @Override
    public Page<Object[]> findEventsByIsPastAndWhiteLabel(Long whiteLabel,Boolean isPast, String search,Pageable pageable){
        return eventRepository.findEventsByIsPastAndWhiteLabel(whiteLabel,isPast,search,pageable);
    }

}