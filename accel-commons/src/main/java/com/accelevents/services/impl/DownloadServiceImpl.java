package com.accelevents.services.impl;

import com.accelevents.auction.dto.ContactDto;
import com.accelevents.auction.dto.ExhibitorDropDownDto;
import com.accelevents.billing.chargebee.dto.ChargebeeEventUsagesDto;
import com.accelevents.billing.chargebee.dto.ChargebeeTicketingFeesDto;
import com.accelevents.billing.chargebee.dto.EventCreditDistributionDto;
import com.accelevents.billing.chargebee.repo.ChargebeeEventUsagesRepoService;
import com.accelevents.billing.chargebee.repo.ChargesPurchasedRepoService;
import com.accelevents.billing.chargebee.repo.OrganizerRepoService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.column.selection.ColumnSelectionConstants;
import com.accelevents.column.selection.dto.ColumnDto;
import com.accelevents.column.selection.dto.UserColumnSelectionDto;
import com.accelevents.column.selection.services.AnalyticsColumnMasterService;
import com.accelevents.column.selection.services.AnalyticsUserColumnSelectionService;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.ChargebeeConfiguration;
import com.accelevents.configuration.GraphQLConfiguration;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.continuing.ed.ContinuingEdConstants;
import com.accelevents.continuing.ed.dto.ContinuingEdProgressDTO;
import com.accelevents.continuing.ed.dto.ContinuingEdUserDTO;
import com.accelevents.continuing.ed.service.ContinuingEdService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.exhibitors.Exhibitor;
import com.accelevents.domain.exhibitors.ExhibitorExportDTO;
import com.accelevents.domain.exhibitors.ExhibitorSetting;
import com.accelevents.domain.session_speakers.*;
import com.accelevents.domain.session_speakers.networking.NetworkingMatches;
import com.accelevents.domain.virtual.LeadRetriverData;
import com.accelevents.domain.virtual.Sponsors;
import com.accelevents.download.dto.DownloadCSVRequestDto;
import com.accelevents.dto.*;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.exhibitors.dto.ExpoStaffDetailDto;
import com.accelevents.exhibitors.dto.TicketHolderAttributeDto;
import com.accelevents.exhibitors.services.ExhibitorSettingsRepoService;
import com.accelevents.exhibitors.services.ExhibitorSettingsService;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.messages.EnumProfileQuestions;
import com.accelevents.messages.EnumQNAType;
import com.accelevents.networking.services.NetworkingLounge;
import com.accelevents.networking.services.NetworkingMatchesService;
import com.accelevents.networking.services.impl.NetworkingLoungeServiceImpl;
import com.accelevents.repositories.*;
import com.accelevents.ro.analytics.ROAttendeeAnalyticsService;
import com.accelevents.ro.audience.service.ROAnalyticsColumnMasterService;
import com.accelevents.ro.audience.service.ROAnalyticsUserColumnSelectionService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.smart_view.RODisplayViewService;
import com.accelevents.ro.speaker.ROSpeakerService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.schedulers.ChargebeeUsageUploadScheduler;
import com.accelevents.services.*;
import com.accelevents.services.audience.Audience;
import com.accelevents.services.audience.filter.service.AudienceFilterService;
import com.accelevents.services.dynamodb.DynamoDBService;
import com.accelevents.services.dynamodb.analytics.ConsolidatedAnalyticsService;
import com.accelevents.services.dynamodb.user.activity.AttendeeTimelineDTO;
import com.accelevents.services.dynamodb.user.activity.UserActivity;
import com.accelevents.services.elasticsearch.leaderboard.*;
import com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.CSVHeaders;
import com.accelevents.services.elasticsearch.networkinglounge.NetworkingLoungeAnalyticsService;
import com.accelevents.services.elasticsearch.sponsor.SponsorAnalyticsConstant;
import com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsQueryConstants;
import com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsUtils;
import com.accelevents.services.mobile.analytics.MobileAnalyticsDataDto;
import com.accelevents.services.mobile.analytics.MobileAnalyticsService;
import com.accelevents.services.repo.helper.*;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.services.*;
import com.accelevents.session_speakers.services.impl.PollsQuestionsAnswers;
import com.accelevents.session_speakers.services.impl.QuestionsAnswers;
import com.accelevents.session_speakers.services.impl.SpeakerRepoService;
import com.accelevents.ticketing.dto.DisplaySharingViewDto;
import com.accelevents.ticketing.dto.TicketingDownloadPurchase;
import com.accelevents.ticketing.dto.TicketingSale;
import com.accelevents.utils.*;
import com.amazonaws.services.lambda.AWSLambdaAsync;
import com.amazonaws.services.lambda.model.InvocationType;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.io.Files;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.opencsv.CSVWriter;
import freemarker.template.Configuration;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Type;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.TimeZone;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.accelevents.domain.enums.EnumKeyValueType.TRACK;
import static com.accelevents.dto.TicketHolderUtils.*;
import static com.accelevents.services.dynamodb.user.activity.UserActivityConstant.*;
import static com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsConstant.CSV_HEADER;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.Constants.NEON.Event_End_Date;
import static com.accelevents.utils.Constants.NEON.Event_SPACE_ID;
import static com.accelevents.utils.EmailUtils.cleanHtmlTags;
import static com.accelevents.utils.GeneralUtils.convertListToCommaSeparated;
import static com.accelevents.utils.GeneralUtils.getEventPath;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.*;

@Service
public class DownloadServiceImpl implements DownloadService {

    private Logger logger = LoggerFactory.getLogger(DownloadServiceImpl.class);

    @Value("${cloud.aws.s3.bucket.ticketBuyerUploads.url}")
    private String ticketBuyerUploadsUrl;
    @Value("${recordingViewStartDate}")
    private String recordingViewStartDate;
    @Value("${uiBaseurl}")
    private String uiBaseurl;

    @Value("${image.url}")
    private String imageUrl;
    @Value("${aws.lambda.download.csv.name}")
    private String downloadCsvAwsLambdaName;

    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private AttendeeProfileService attendeeProfileService;
    @Autowired
    private AuctionBidService auctionBidService;
    @Autowired
    private SubmittedRaffleTicketService submittedRaffleTicketService;
    @Autowired
    private PledgeService pledgeService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private WinnerService winnerService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private ImageConfiguration imageConfiguration;
    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private DonationService donationService;
    @Autowired
    private JoinPaymentItemService joinPaymentItemService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private BidderNumberService bidderNumberService;
    @Autowired
    private PurchasedRaffleTicketService purchasedRaffleTicketService;
    @Autowired
    private StripeService stripeService;
    @Autowired
    private StripeTransactionService stripeTransactionService;
    @Autowired
    private TimeZoneService timeZoneService;
    @Autowired
    private TicketingCSVService ticketingCSVService;
    @Autowired
    private ContactService contactService;
    @Autowired
    private AutoAssignedAttendeeNumbersService autoAssignedAttendeeNumbersService;
    @Autowired
    private LeadService leadService;
    @Autowired
    private UserSessionService userSessionService;
    @Autowired
    private GraphQLConfiguration graphQLConfiguration;
    @Autowired
    private Configuration freemarkerMailConfiguration;
    @Autowired
    private VirtualEventLoggingService virtualEventLoggingService;
    @Autowired
    private GetStreamService getStreamService;
    @Autowired
    private ExhibitorService exhibitorService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private LeaderboardService leaderboardService;
    @Autowired
    private SessionService sessionService;
    @Autowired
    private SessionDetailsService sessionDetailsService;
    @Autowired
    private ConsolidatedAnalyticsService consolidatedAnalyticsService;
    @Autowired
    private ExhibitorSettingsService exhibitorSettingsService;
    @Autowired
    private NetworkingMatchesService networkingMatchesService;
    @Autowired
    private SessionSpeakerService sessionSpeakerService;
    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private SpeakerRepoService speakerRepoService;
    @Autowired
    private NetworkingLoungeServiceImpl networkingLoungeServiceImpl;
    @Autowired
    private TicketHolderRequiredAttributesService holderReqAttributeService;
    @Autowired
    private ChallengeConfigService challengeConfigService;
    @Autowired
    private VirtualEventService virtualEventService;
    @Autowired
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;
    @Autowired
    private FeedService feedService;
    @Autowired
    private NetworkingLoungeAnalyticsService networkingLoungeAnalyticsService;
    @Autowired
    private StaffRepository staffRepository;
    @Autowired
    private ServiceHelper serviceHelper;
    @Autowired
    private EmailSuppressionRepository emailSuppressionRepository;
    @Autowired
    private CheckInAuditLogRepoService checkInAuditLogRepoService;
    @Autowired
    private TicketingTypeService ticketingTypeService;

    @Autowired
    private  CheckInAuditLogService CheckInAuditLogService;
    @Autowired
    private BillingEventsService billingEventsService;
    @Autowired private ExhibitorSettingsRepoService repoService;
    @Autowired private AttendeeConnectionAttributesService attendeeConnectionAttributesService;

    @Autowired
    private  MeetingScheduleService meetingScheduleService;

    @Autowired
    OrganizerService organizerService;

    @Autowired
    private SponsorsService sponsorsService;

    @Autowired
    private CartAbandonmentService cartAbandonmentService;

    @Autowired
    WhiteLabelService whiteLabelService;

    @Autowired
    private TicketHolderAttributesService ticketHolderAttributesService;

    @Autowired
    private AudienceFilterService audienceFilterService;

    @Autowired
    private OrganizerRepoService organizerRepoService;

    @Autowired
    private MobileAnalyticsService mobileAnalyticsService;

    @Autowired
    private AnalyticsUserColumnSelectionService userColumnSelectionService;
    @Autowired
    private AnalyticsColumnMasterService columnMasterService;
    @Autowired
    private RODisplayViewService roDisplayViewService;
    @Autowired
    private ROAttendeeAnalyticsService roAttendeeAnalyticsService;
    @Autowired
    private ROAnalyticsUserColumnSelectionService roAnalyticsUserColumnSelectionService;
    @Autowired
    private ROAnalyticsColumnMasterService roAnalyticsColumnMasterService;
    @Autowired
    private EventTicketsService eventTicketsService;
    @Autowired
    private AttendeeAnalyticsService attendeeAnalyticsService;
    @Autowired
    private DisplayViewService displayViewService;
    @Autowired
    private AnalyticsUserColumnSelectionService columnSelectionService;
    @Autowired
    private ContinuingEdService continuingEdService;
    @Autowired
    private ChallengeConfigService challengeService;
    @Autowired
    private ROSpeakerService roSpeakerService;

    @Autowired
    private ExhibitorCategoryService exhibitorCategoryService;

    @Autowired
    private SurveyQuestionsRepoService surveyQuestionsRepoService;
    @Autowired
    private SessionCheckInLogRepoService sessionCheckInLogRepoService;
    @Autowired
    private VirtualEventSessionLoggingService virtualEventSessionLoggingService;
    @Autowired
    private KeyValueRepoService keyValueRepoService;
    @Autowired
    private SessionTagAndTrackService sessionTagAndTrackService;
    @Autowired
    private S3Wrapper s3Wrapper;
    @Autowired
    private MeetingScheduleRepoService meetingScheduleRepoService;
    @Autowired
    private AWSLambdaAsync awsLambdaAsyncClient;
    @Autowired
    private SendGridMailService sendGridMailService;
    @Autowired
    private ChargebeeConfiguration chargebeeConfiguration;
    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Autowired
    private ChargebeeUsageUploadScheduler chargebeeUsageUploadScheduler;
    @Autowired
    private ChargebeeEventUsagesRepoService chargebeeEventUsagesRepoService;
    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private EventTicketsRepository eventTicketsRepository;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private EventTasksRepo eventTasksRepo;
    @Autowired
    private SessionLocationService sessionLocationService;
    @Autowired
    private DisplayViewServiceImpl displayViewServiceImpl;
    @Autowired
    private ViewFilterDetailsRepository viewFilterDetailsRepository;


    private ContinuingEdChallengePdfCreator edChallengePdfCreator = ContinuingEdChallengePdfCreator.getInstance();

    private DateFormat dateFormatter = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
    @Autowired
    private EventChallengeTierService eventChallengeTierService;
    @Autowired
    private EventChallengeRepository eventChallengeRepository;

    @Autowired
    private ChallengeAndTierMappingRepository challengeAndTierMappingRepository;
    @Autowired
    private CreditManagementService creditManagementService;

    @Autowired
    private CreditManagementRepoService creditManagementRepoService;
    @Autowired
    private EventCECriteriaService eventCECriteriaService;

    @Autowired
    private CustomFormAttributeService customFormAttributeService;

    @Autowired
    private SpeakerService speakerService;

    @Autowired
    private CustomFormAttributeDataRepository customFormAttributeDataRepository;

    @Autowired
    private SurveyResponseRepoService surveyResponseRepoService;

    @Autowired
    private DynamoDBService<UserActivity> dynamoDBService;

    @Autowired
    private UserSessionRepoService userSessionRepoService;

    @Autowired private TicketHolderAttributesService holderAttributeService;

    @Override
    public void downloadAllEventUsagesPerEvent(HttpServletResponse response, Date startDate, Date endDate){
        logger.info("DownloadServiceImpl | downloadAllEventUsagesPerEvent | startDate {} endDate {}",startDate,endDate);
        List<ChargebeeEventUsagesDto> eventUsagesDtos ;
        List<String> headerList = new ArrayList<>();
        headerList.add(Event_SPACE_ID);
        headerList.add(HEADER.EVENT_URL);
        headerList.add(SUBSCRIPTION_ID);
        headerList.add(CUSTOMER_ID_CAMELCASE);
        headerList.add(ATTENDEE_DAYS);
        headerList.add(BILLING_DAYS);
        headerList.add(FINAL_ATTENDEE_DAYS);
        headerList.add(FREE_CREDITS);
        headerList.add(PAID_CREDITS);
        headerList.add(Event_End_Date);
        headerList.add(CHARGEBEE_PLAN);

        if(startDate != null && endDate != null) {
            if (startDate.before(endDate) || startDate.equals(endDate)){
                Calendar calendar1 = Calendar.getInstance();
                calendar1.setTime(startDate);
                calendar1.set(Calendar.HOUR_OF_DAY, 0);
                calendar1.set(Calendar.MINUTE, 0);
                calendar1.set(Calendar.SECOND, 0);

                Calendar calendar2 = Calendar.getInstance();
                calendar2.setTime(endDate);
                calendar2.set(Calendar.HOUR_OF_DAY, 23);
                calendar2.set(Calendar.MINUTE, 59);
                calendar2.set(Calendar.SECOND, 59);

                eventUsagesDtos = CheckInAuditLogService.getAllEventUsagesDataPerEvent(calendar1.getTime(),calendar2.getTime());

            }else{
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.START_DATE_SHOULD_BE_BEFORE_END_DATE);
            }
        }else{
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.START_DATE_AND_END_DATE_MUST_NOT_BE_NULL);
        }

        List<List<String>> eventUsagesData = setEventUsagesData(eventUsagesDtos);

        logger.info("DownloadServiceImpl | downloadAllEventUsagesPerEvent | eventUsagesDtos size{}",eventUsagesDtos.size());
        PrintWriter writer = setHeader(response,"AllEventUsages.csv");
        this.downloadCSVFile(writer, headerList, eventUsagesData);
    }

    public List<List<String>> setEventUsagesData(List<ChargebeeEventUsagesDto> eventUsagesDtos){//NOSONAR
        List<List<String>> eventUsagesData = new ArrayList<>();
        eventUsagesDtos.forEach(usagesDto -> {
            List<String> eventUsagesRow = new ArrayList<>();
            eventUsagesRow.add(usagesDto.getEventId() != null ? usagesDto.getEventId().toString() : EMPTY);
            eventUsagesRow.add(usagesDto.getEventUrl() != null ? usagesDto.getEventUrl() : EMPTY);
            eventUsagesRow.add(usagesDto.getSubscriptionId() != null ? usagesDto.getSubscriptionId() : EMPTY);
            eventUsagesRow.add(usagesDto.getCustomerId() != null ? usagesDto.getCustomerId() : EMPTY);
            eventUsagesRow.add(usagesDto.getAttendeeDays() != null ? usagesDto.getAttendeeDays().toString() : EMPTY);
            eventUsagesRow.add(usagesDto.getBillingDays() != null ? usagesDto.getBillingDays().toString() : EMPTY);
            eventUsagesRow.add(usagesDto.getFinalAttendeeDays() != null ? usagesDto.getFinalAttendeeDays().toString() : EMPTY);
            eventUsagesRow.add(usagesDto.getFreeCredits() != null ? usagesDto.getFreeCredits().toString() : EMPTY);
            eventUsagesRow.add(usagesDto.getPaidCredits() != null ? usagesDto.getPaidCredits().toString() : EMPTY);
            eventUsagesRow.add(usagesDto.getEndDate() != null ? usagesDto.getEndDate() : EMPTY);
            eventUsagesRow.add(usagesDto.getChargebeePlan() != null ? usagesDto.getChargebeePlan() :EMPTY);

            eventUsagesData.add(eventUsagesRow);
        });
        return eventUsagesData;
    }

    @Override
    public void downloadAllTicketFeesPerEvent(HttpServletResponse response , Date startDate, Date endDate){
        logger.info("DownloadServiceImpl | downloadAllTicketFeesPerEvent | startDate {} endDate {}",startDate,endDate);
        List<ChargebeeTicketingFeesDto> ticketingFeesDtos ;
        List<String> headerList = new ArrayList<>();
        headerList.add(Event_SPACE_ID);
        headerList.add(HEADER.EVENT_URL);
        headerList.add(SUBSCRIPTION_ID);
        headerList.add(CUSTOMER_ID_CAMELCASE);
        headerList.add(AMOUNT_CAMELCASE);
        headerList.add(Event_End_Date);

        if(startDate != null && endDate != null) {
            if (startDate.before(endDate) || startDate.equals(endDate)){
                Calendar calendar1 = Calendar.getInstance();
                calendar1.setTime(startDate);
                calendar1.set(Calendar.HOUR_OF_DAY, 0);
                calendar1.set(Calendar.MINUTE, 0);
                calendar1.set(Calendar.SECOND, 0);

                Calendar calendar2 = Calendar.getInstance();
                calendar2.setTime(endDate);
                calendar2.set(Calendar.HOUR_OF_DAY, 23);
                calendar2.set(Calendar.MINUTE, 59);
                calendar2.set(Calendar.SECOND, 59);

                ticketingFeesDtos = eventTicketsService.getAllTicketingFeedDataPerEvent(calendar1.getTime(),calendar2.getTime());
            }else{
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.START_DATE_SHOULD_BE_BEFORE_END_DATE);
            }
        }else{
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.START_DATE_AND_END_DATE_MUST_NOT_BE_NULL);
        }

        List<List<String>> ticketingFeesData = new ArrayList<>();

        ticketingFeesDtos.forEach(feesDto -> {
            List<String> ticketingFeesRow = new ArrayList<>();
            ticketingFeesRow.add(feesDto.getEventId() != null ? feesDto.getEventId().toString() : EMPTY);
            ticketingFeesRow.add(feesDto.getEventUrl() != null ? feesDto.getEventUrl() : EMPTY);
            ticketingFeesRow.add(feesDto.getSubscriptionId() != null ? feesDto.getSubscriptionId() : EMPTY);
            ticketingFeesRow.add(feesDto.getCustomerId() != null ? feesDto.getCustomerId() : EMPTY);
            ticketingFeesRow.add(String.valueOf(feesDto.getAmount()));
            ticketingFeesRow.add(feesDto.getEndDate());

            ticketingFeesData.add(ticketingFeesRow);
        });
        PrintWriter writer = setHeader(response,"Ticket Fees.csv");
        logger.info("DownloadServiceImpl | downloadAllTicketFeesPerEvent | ticketingFeesDtos size{}",ticketingFeesDtos.size());
        this.downloadCSVFile(writer, headerList, ticketingFeesData);
    }

    @Override
    public void downloadcartAbandonedRegistrantsData(PrintWriter writer, Event event, Long recurringEventsId) {

        logger.info("DownloadServiceImpl | downloadcartAbandonedRegistrantsData eventid {},recurringEventsId{}",event.getEventId(),recurringEventsId);
        List<CartAbandonment> cartAbandonmentList ;
         if(NumberUtils.isNumberGreaterThanZero(recurringEventsId))
            cartAbandonmentList = cartAbandonmentService.getCardAbandonmentDataByEventAndRecurringEventId(event,recurringEventsId);
        else
            cartAbandonmentList = cartAbandonmentService.getCardAbandonmentDataByEvent(event);
        List<String> headerList = new ArrayList<>();
        headerList.add(CSV_HEADER.FIRST_NAME);
        headerList.add(CSV_HEADER.LAST_NAME);
        headerList.add(CSV_HEADER.EMAIL);
        headerList.add(CSV_HEADER.TICKET_TYPE_NAME);
        List<List<String>> cartAbandonedRegistrantsData = new ArrayList<>();

        cartAbandonmentList.forEach(abondonedRegistrant -> {
            List<String> cartdataRow = new ArrayList<>();

            if (abondonedRegistrant.getPurchaser() != null) {
                cartdataRow.add(abondonedRegistrant.getPurchaser().getFirstName() != null ? abondonedRegistrant.getPurchaser().getFirstName() : FIRST_NAME);
                cartdataRow.add(abondonedRegistrant.getPurchaser().getLastName() != null ? abondonedRegistrant.getPurchaser().getLastName() : LAST_NAME);
                cartdataRow.add(abondonedRegistrant.getPurchaser().getEmail());
            } else {
                cartdataRow.add(STRING_EMPTY);
                cartdataRow.add(STRING_EMPTY);
                cartdataRow.add(STRING_EMPTY);
            }
            if (StringUtils.isNotBlank(abondonedRegistrant.getTicketTypeIds())){
                List<String> ticketTypesNames = ticketingTypeService.findNameByTicketTypeIds(GeneralUtils.convertCommaSeparatedToListLong(abondonedRegistrant.getTicketTypeIds()));
                cartdataRow.add(GeneralUtils.convertListToCommaSeparated(ticketTypesNames));
            } else {
                cartdataRow.add(STRING_EMPTY);
            }
            cartAbandonedRegistrantsData.add(cartdataRow);
        });

        logger.info("DownloadServiceImpl | downloadcartAbandonedRegistrantsData cartAbandonedRegistrantsData size{}",cartAbandonedRegistrantsData.size());
        this.downloadCSVFile(writer, headerList, cartAbandonedRegistrantsData);

    }


    @Override
    public void downloadCSVFile(PrintWriter writer, List<String> headerList, List<List<String>> dataRowList) {
        try (CSVWriter csvWriter = new CSVWriter(writer, CSVWriter.DEFAULT_SEPARATOR,CSVWriter.DEFAULT_QUOTE_CHARACTER,CSVWriter.DEFAULT_ESCAPE_CHARACTER,CSVWriter.DEFAULT_LINE_END)) {

            String[] header = headerList.toArray(new String[0]);
            csvWriter.writeNext(header);

            for (List<String> dataList : dataRowList) {

                String[] dataCSV = dataList.toArray(new String[0]);

                csvWriter.writeNext(dataCSV);
            }
            csvWriter.flush();
        } catch (IOException ex) {
            logger.error("IOException", ex);
        }
    }

    private void downloadCSVFileForAllLead(PrintWriter writer, Map<String,List<String>> headerMap, Map<String,List<List<String>>> dataRowListMap, Map<String,String> dataBoothCategory) {
        try (CSVWriter csvWriter = new CSVWriter(writer, CSVWriter.DEFAULT_SEPARATOR,CSVWriter.DEFAULT_QUOTE_CHARACTER,CSVWriter.DEFAULT_ESCAPE_CHARACTER,CSVWriter.DEFAULT_LINE_END)) {
            String[] emptyArray = new String[]{};
            for (Map.Entry<String, List<List<String>>> entry : dataRowListMap.entrySet()) {
                String[] exhibitorName = new String[]{entry.getKey()};

                List<String> headerList = headerMap.get(exhibitorName[0]);
                String[] header = headerList.toArray(new String[0]);

                csvWriter.writeNext(exhibitorName);
                csvWriter.writeNext(new String[]{dataBoothCategory.get(exhibitorName[0])});
                csvWriter.writeNext(header);
                for (List<String> dataList : entry.getValue()) {
                    String[] dataCSV = dataList.toArray(new String[0]);
                    csvWriter.writeNext(dataCSV);
                }
                csvWriter.writeNext(emptyArray);
                csvWriter.writeNext(emptyArray);
            }
            if (dataRowListMap.entrySet().isEmpty()) {
                String[] header = {NO_DATA_FOUND};
                csvWriter.writeNext(header);
                csvWriter.writeNext(emptyArray);
                csvWriter.writeNext(emptyArray);
            }
            csvWriter.flush();
        } catch (IOException ex) {
            logger.error("IOException", ex);
        }
    }

    private void downloadCSVFileForTiers(PrintWriter writer,
                                         Map<String, List<String>> tierHeadersMap,
                                         Map<String, List<List<String>>> tierDataRowsMap) {
        try (CSVWriter csvWriter = new CSVWriter(writer,
                CSVWriter.DEFAULT_SEPARATOR,
                CSVWriter.DEFAULT_QUOTE_CHARACTER,
                CSVWriter.DEFAULT_ESCAPE_CHARACTER,
                CSVWriter.DEFAULT_LINE_END)) {
            String[] emptyRow = new String[]{};

            for (Map.Entry<String, List<List<String>>> tierEntry : tierDataRowsMap.entrySet()) {
                // Get the tier name
                String tierName = tierEntry.getKey();

                // Write the tier header
                String[] tierTitle = new String[]{ "Tier: " + tierName };
                csvWriter.writeNext(tierTitle);

                // Fetch the headers for this tier
                List<String> tierHeaders = tierHeadersMap.getOrDefault(tierName, new ArrayList<>());
                String[] headerRow = tierHeaders.toArray(new String[0]);

                // Write the header row for the current tier
                csvWriter.writeNext(headerRow);

                // Write the data rows for the current tier
                for (List<String> dataRow : tierEntry.getValue()) {
                    String[] dataRowArray = dataRow.toArray(new String[0]);
                    csvWriter.writeNext(dataRowArray);
                }

                // Add a blank row to separate tiers
                csvWriter.writeNext(emptyRow);
                csvWriter.writeNext(emptyRow);
            }

            // Handle the case where there are no data rows for any tier
            if (tierDataRowsMap.isEmpty()) {
                String[] noDataRow = { "No Data Found" };
                csvWriter.writeNext(noDataRow);
                csvWriter.writeNext(emptyRow);
            }

            // Flush the writer to ensure all data is written
            csvWriter.flush();
        } catch (IOException ex) {
            logger.error("IOException while writing CSV for tiers", ex);
        }
    }


    private List<String> getHeaderTitleForSilentAuctionAllBidderORWinner()
    {
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.ITEM_CODE);
        headerList.add(HEADER.ITEM_NAME);
        headerList.add(HEADER.BID_AMOUNT);
        headerList.add(HEADER.ITEM_CATEGORY);
        headerList.add(HEADER.MARKET_VALUE);
        headerList.add(HEADER.BID_TIME);
        headerList.add(HEADER.BID_SOURCE);
        headerList.add(HEADER.IS_WINNER);
        headerList.add(HEADER.PAID);
        headerList.add(HEADER.PAYMENT_SOURCE);
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.PHONE_NUMBER);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.ADDRESS_1);
        headerList.add(HEADER.ADDRESS_2);
        headerList.add(HEADER.STATE);
        headerList.add(HEADER.CITY);
        headerList.add(HEADER.ZIP_CODE);
        headerList.add(HEADER.COUNTRY);

        return headerList;
    }

    @Override
    public void downloadSilentAuctionAllBidderData(PrintWriter writer, Event event) {

        List<String> headerList = getHeaderTitleForSilentAuctionAllBidderORWinner();

        if (event.getEnableBidderRegistration()) {
            headerList.add(HEADER.BIDDER_NUMBER);
        }
        headerList.add(STAFF_NAME);
        headerList.add(HEADER.IS_REFUNDED);

        List<List<String>> dataRowList = new ArrayList<>();

        for (AuctionBid auctionBid : auctionBidService.findByAuctionId(event.getAuctionId())) {
            Item item = auctionBid.getItem();
            User bidUser = auctionBid.getUser();
            List<String> dataList = new ArrayList<>();
            dataList.add(item.getCode());
            dataList.add(item.getName());
            dataList.add(Double.toString(auctionBid.getAmount()));
            dataList.add(item.getItemCategory() != null ? item.getItemCategory().getName() : STRING_EMPTY);
            dataList.add(Integer.toString(item.getMarketValue()));
            dataList.add(dateFormatter.format(getDateInLocal(auctionBid.getBidTime(), event.getEquivalentTimeZone())));
            dataList.add(auctionBid.getBidSource().name());
            dataList.add(bidUser != null ? Boolean.toString(winnerService.isUserWinnerForEventModule(event.getEventId(),
                    ModuleType.AUCTION, bidUser.getUserId(), auctionBid.getId())) : STRING_EMPTY);
            dataList.add(Boolean.toString(auctionBid.isHasPaid()));

            Optional<Payment> payment = paymentService.findByUserIdAndEventId(Objects.requireNonNull(bidUser).getUserId(), event.getEventId());
            addPayment(item, dataList, payment.orElse(null));

            dataList.add(bidUser.getFirstName());
            dataList.add(bidUser.getLastName());
            dataList.add(Long.toString(bidUser.getPhoneNumber()));
            dataList.add(bidUser.getEmail());
            dataList.add(bidUser.getAddress1());
            dataList.add(bidUser.getAddress2());
            dataList.add(bidUser.getState());
            dataList.add(bidUser.getCityOrProvidence());
            dataList.add(bidUser.getZipcode());
            dataList.add(bidUser.getCountry());

            if (event.getEnableBidderRegistration()) {
                BidderNumber bidderNumber = bidderNumberService.findByEventAndUser(event, bidUser);
                if (bidderNumber != null)
                    dataList.add(STRING_EMPTY + bidderNumber.getBidderNumber());
            }
            dataList.add( auctionBid.getStaffUserId() != null ? auctionBid.getStaffUserId().getFirstName() + " " + auctionBid.getStaffUserId().getLastName() : STRING_EMPTY);

            dataList.add(auctionBid.isRefunded() ? CAMELCASE_YES : CAMELCASE_NO);
            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void addPayment(Item item, List<String> dataList, Payment payment) {
        if (joinPaymentItemService.hasPayment(item) && payment != null) {
            JoinPaymentItem joinPaymentItem = joinPaymentItemService.findByPaymentAndItem(payment, item);
            dataList.add(joinPaymentItem != null ? joinPaymentItem.getTransactionType() : STRING_EMPTY);
        } else {
            dataList.add(STRING_EMPTY);
        }
    }

    private List<String> getDownloadRaffleWinnerORRaffleParticipantHeaderList()
    {
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.ITEM_CODE);
        headerList.add(HEADER.ITEM_NAME);
        headerList.add(HEADER.TICKETS_SUBMITTED);
        headerList.add(HEADER.TOTAL_DOLLAR_AMOUNT);
        headerList.add(HEADER.ITEM_CATEGORY);
        headerList.add(HEADER.TICKETS_SUBMITTED_DATE);
        headerList.add(HEADER.TICKETS_SOURCE);
        headerList.add(HEADER.IS_WINNER);
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.PHONE_NUMBER);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.ADDRESS_1);
        headerList.add(HEADER.ADDRESS_2);
        headerList.add(HEADER.STATE);
        headerList.add(HEADER.CITY);
        headerList.add(HEADER.ZIP_CODE);
        headerList.add(HEADER.COUNTRY);
        headerList.add(HEADER.STAFF_NAME);

        return headerList;
    }
    @Override
    public void downloadRaffleParticipantData(PrintWriter writer, Event event) {

        List<String> headerList =  getDownloadRaffleWinnerORRaffleParticipantHeaderList();

        List<Object[]> ticketPriceForUsers = purchasedRaffleTicketService.getPerTicketPriceForUsers(event.getRaffleId());
        HashMap<Long,Long> ticketPricePerUser = new HashMap<>();
        if(!ticketPriceForUsers.isEmpty())
        {
            ticketPriceForUsers.forEach(ticketPriceForUser -> ticketPricePerUser.put(Long.parseLong(ticketPriceForUser[0].toString()),Long.parseLong(ticketPriceForUser[1].toString())));
        }
        List<UserCustomAttribute> userCustomAttributes = userService
                .findUserCustomAttributesByEventId(event.getEventId());

        Set<String> customAttributeNameSet = new HashSet<>();

        userCustomAttributes.forEach(attr -> customAttributeNameSet.add(attr.getName()));

        List<String> customeAttributeNameList = new ArrayList<>(customAttributeNameSet);
        headerList.addAll(customeAttributeNameList);

        List<List<String>> dataRowList = new ArrayList<>();

        List<SubmittedRaffleTicket> submittedRaffleTickets = submittedRaffleTicketService
                .getSubmittedRaffleTicketsByRaffleId(event.getRaffleId());

        for (SubmittedRaffleTicket ticket : submittedRaffleTickets) {
            User user = ticket.getUser();
            Long aTicketPrice = 0L;
            List<UserCustomAttribute> userAttribute = userCustomAttributes.stream()
                    .filter(attr -> attr.getUserId() == user.getUserId()).collect(Collectors.toList());

            Map<String, String> attrMap = new HashMap<>();
            userAttribute.forEach(attr -> attrMap.put(attr.getName(), attr.getValue()));
            //submitted ticket price
            if(ticketPricePerUser.containsKey(user.getUserId()))
            {
                aTicketPrice = ticketPricePerUser.get(user.getUserId());
            }
            String totalDollerAmount = Long.toString(aTicketPrice * ticket.getTicketsSubmitted());
            List<String> dataList = new ArrayList<>();
            dataList.add(ticket.getItem().getCode());
            dataList.add(ticket.getItem().getName());
            dataList.add(Long.toString(ticket.getTicketsSubmitted()));
            dataList.add(totalDollerAmount);
            dataList.add(ticket.getItem().getItemCategory() != null ? ticket.getItem().getItemCategory().getName()
                    : STRING_EMPTY);
            dataList.add(dateFormatter.format(getDateInLocal(ticket.getSubmittedDate(), event.getEquivalentTimeZone())));
            dataList.add(BiddingSource.RANDOM.name().equalsIgnoreCase(ticket.getSource().name()) ? AUTOMATIC : ticket.getSource().name());
            dataList.add(Boolean.toString(ticket.isWinner()));
            dataList.add(user.getFirstName());
            dataList.add(user.getLastName());
            dataList.add(Long.toString(user.getPhoneNumber()));
            dataList.add(user.getEmail());
            dataList.add(user.getAddress1());
            dataList.add(user.getAddress2());
            dataList.add(user.getState());
            dataList.add(user.getCityOrProvidence());
            dataList.add(user.getZipcode());
            dataList.add(user.getCountry());
            customeAttributeNameList.forEach(attr -> dataList.add(attrMap.get(attr)));

            if (ticket.getStaffUserId() != null) {
                dataList.add(ticket.getStaffUserId().getFirstName() + " " + ticket.getStaffUserId().getLastName());
            } else {
                dataList.add(STRING_EMPTY);
            }


            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadPurchasedRaffleTicketData(PrintWriter writer, Event event) {

        List<String> headerList = new ArrayList<>();
        headerList.add(NUMBER_OF_TICKETS_PURCHASED);
        headerList.add(HEADER.TOTAL_DOLLAR_AMOUNT);
        headerList.add(HEADER.TICKETS_SOURCE);
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.PHONE_NUMBER);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.ADDRESS_1);
        headerList.add(HEADER.ADDRESS_2);
        headerList.add(HEADER.STATE);
        headerList.add(HEADER.CITY);
        headerList.add(HEADER.ZIP_CODE);
        headerList.add(HEADER.COUNTRY);
        headerList.add(HEADER.STAFF_NAME);
        headerList.add(HEADER.DATE_TIME);
        headerList.add(HEADER.IS_REFUNDED);

        List<UserCustomAttribute> userCustomAttributes = userService
                .findUserCustomAttributesByEventId(event.getEventId());

        Set<String> customAttributeNameSet = new HashSet<>();

        userCustomAttributes.forEach(attr -> customAttributeNameSet.add(attr.getName()));

        List<String> customeAttributeNameList = new ArrayList<>(customAttributeNameSet);
        headerList.addAll(customeAttributeNameList);

        List<List<String>> dataRowList = new ArrayList<>();

        List<PurchasedRaffleTicket> purchasedRaffleTickets = purchasedRaffleTicketService
                .getAllPurchasedRaffleTicketIncludeRefund(event.getRaffleId());

        for (PurchasedRaffleTicket ticket : purchasedRaffleTickets) {
            User user = ticket.getUser();

            List<UserCustomAttribute> userAttribute = userCustomAttributes.stream()
                    .filter(attr -> attr.getUserId() == user.getUserId()).collect(Collectors.toList());

            Map<String, String> attrMap = new HashMap<>();
            userAttribute.forEach(attr -> attrMap.put(attr.getName(), attr.getValue()));

            List<String> dataList = new ArrayList<>();
            dataList.add(Long.toString(ticket.getTicketsPurchased()));
            dataList.add(Long.toString(ticket.getPrice()));
            dataList.add(ticket.getTransactionType() != null ? ticket.getTransactionType().toString()
                    : STRING_EMPTY);
            dataList.add(user.getFirstName());
            dataList.add(user.getLastName());
            dataList.add(Long.toString(user.getPhoneNumber()));
            dataList.add(user.getEmail());
            dataList.add(user.getAddress1());
            dataList.add(user.getAddress2());
            dataList.add(user.getState());
            dataList.add(user.getCityOrProvidence());
            dataList.add(user.getZipcode());
            dataList.add(user.getCountry());
            customeAttributeNameList.forEach(attr -> dataList.add(attrMap.get(attr)));

            if (ticket.getStaffUserId() != null) {
                dataList.add(ticket.getStaffUserId().getFirstName() + " " + ticket.getStaffUserId().getLastName());
            } else {
                dataList.add(STRING_EMPTY);
            }


            if (ticket.getCreatedAt() != null) {
                dataList.add(dateFormatter.format(getDateInLocal(ticket.getCreatedAt(), event.getEquivalentTimeZone())));
            }
            dataList.add(ticket.getRefunded() ? CAMELCASE_YES : CAMELCASE_NO);
            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);

    }

    @Override
    public void downloadCauseAuctionDonorData(PrintWriter writer, Event event) {

        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.ITEM_CODE);
        headerList.add(HEADER.ITEM_NAME);
        headerList.add(HEADER.PLEDGE_AMOUNT);
        headerList.add(HEADER.ITEM_CATEGORY);
        headerList.add(HEADER.PLEDGE_DATE);
        headerList.add(HEADER.PLEDGE_SOURCE);
        headerList.add(HEADER.PAID);
        headerList.add(HEADER.PAYMENT_SOURCE);
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.PHONE_NUMBER);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.ADDRESS_1);
        headerList.add(HEADER.ADDRESS_2);
        headerList.add(HEADER.STATE);
        headerList.add(HEADER.CITY);
        headerList.add(HEADER.ZIP_CODE);
        headerList.add(HEADER.COUNTRY);
        headerList.add(HEADER.NOTE);
        headerList.add(STAFF_NAME);
        headerList.add(HEADER.IS_REFUNDED);
        List<List<String>> dataRowList = new ArrayList<>();

        for (Pledge pledge : pledgeService.findAllPledgeForCauseAuction(event.getCauseAuctionId())) {
            Item item = pledge.getItem();
            User pledgeUser = pledge.getUser();

            List<String> dataList = new ArrayList<>();
            dataList.add(item.getCode());
            dataList.add(item.getName());
            dataList.add(Double.toString(pledge.getAmount()));
            dataList.add(item.getItemCategory() != null ? item.getItemCategory().getName() : STRING_EMPTY);
            dataList.add(pledge.getPledgeTime() != null ? dateFormatter.format(getDateInLocal(pledge.getPledgeTime(), event.getEquivalentTimeZone())) : STRING_EMPTY);
            dataList.add(pledge.getPledgeSource().name());
            dataList.add(Boolean.toString(pledge.isHasPaid()));

            addPaymentDetail(item,dataList,pledgeUser.getUserId(),event.getEventId());

            dataList.add(pledgeUser.getFirstName());
            dataList.add(pledgeUser.getLastName());
            dataList.add(pledgeUser.getPhoneNumber() != 0 ? Long.toString(pledgeUser.getPhoneNumber()) : STRING_EMPTY);
            dataList.add(pledgeUser.getEmail());
            dataList.add(pledgeUser.getAddress1());
            dataList.add(pledgeUser.getAddress2());
            dataList.add(pledgeUser.getState());
            dataList.add(pledgeUser.getCityOrProvidence());
            dataList.add(pledgeUser.getZipcode());
            dataList.add(pledgeUser.getCountry());
            dataList.add(pledge.getNote());

            dataList.add(pledge.getStaffUserId() != null ? pledge.getStaffUserId().getFirstName() + " " + pledge.getStaffUserId().getLastName() : STRING_EMPTY);
            dataList.add(pledge.isRefunded() ? CAMELCASE_YES : CAMELCASE_NO);
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void addPaymentDetail(Item item,List<String> dataList,long userId,long eventId)
    {
        Optional<Payment> payment = paymentService.findByUserIdAndEventId(userId, eventId);

        if (joinPaymentItemService.hasPayment(item) && payment.isPresent()) {
            JoinPaymentItem joinPaymentItem = joinPaymentItemService.findByPaymentAndItem(payment.get(), item);
            dataList.add(joinPaymentItem != null ? joinPaymentItem.getTransactionType() : STRING_EMPTY);
        } else {
            dataList.add(STRING_EMPTY);
        }
    }

    @Override
    public void downloadSilentAuctionWinnerData(PrintWriter writer, Event event) {
        List<String> headerList = getHeaderTitleForSilentAuctionAllBidderORWinner();

        headerList.add(STAFF_NAME);

        List<List<String>> dataRowList = new ArrayList<>();
        List<Winner> winners = winnerService.findWinnersByEventAndModule(event.getEventId(), ModuleType.AUCTION);

        for (Winner winner : winners) {
            Optional<AuctionBid> optionalAuctionBid = auctionBidService.getAuctionBidByIdAndNotRefunded(winner.getBidId());
            if (optionalAuctionBid.isPresent()) {
                AuctionBid auctionBid = optionalAuctionBid.get();
                Item item = auctionBid.getItem();
                User bidUser = auctionBid.getUser();
                List<String> dataList = new ArrayList<>();
                dataList.add(item.getCode());
                dataList.add(item.getName());
                dataList.add(Double.toString(auctionBid.getAmount()));
                dataList.add(item.getItemCategory() != null ? item.getItemCategory().getName() : STRING_EMPTY);
                dataList.add(Integer.toString(item.getMarketValue()));
                dataList.add(dateFormatter.format(getDateInLocal(auctionBid.getBidTime(), event.getEquivalentTimeZone())));
                dataList.add(auctionBid.getBidSource().name());
                dataList.add(Boolean.toString(winnerService.isUserWinnerForEventModule(event.getEventId(), ModuleType.AUCTION, bidUser.getUserId(), auctionBid.getId())));
                dataList.add(Boolean.toString(auctionBid.isHasPaid()));
                addPaymentDetail(item,dataList,bidUser.getUserId(),event.getEventId());
                dataList.add(bidUser.getFirstName());
                dataList.add(bidUser.getLastName());
                dataList.add(Long.toString(bidUser.getPhoneNumber()));
                dataList.add(bidUser.getEmail());
                dataList.add(bidUser.getAddress1());
                dataList.add(bidUser.getAddress2());
                dataList.add(bidUser.getState());
                dataList.add(bidUser.getCityOrProvidence());
                dataList.add(bidUser.getZipcode());
                dataList.add(bidUser.getCountry());
                if (auctionBid.getStaffUserId() != null) {
                    dataList.add(auctionBid.getStaffUserId().getFirstName() + " " + auctionBid.getStaffUserId().getLastName());
                }

                dataRowList.add(dataList);
            }
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadRaffleWinnerData(PrintWriter writer, Event event) {
        List<String> headerList = getDownloadRaffleWinnerORRaffleParticipantHeaderList();

        List<Object[]> ticketPriceForUsers = purchasedRaffleTicketService.getPerTicketPriceForUsers(event.getRaffleId());
        HashMap<Long,Long> ticketPricePerUser = new HashMap<>();
        if(!ticketPriceForUsers.isEmpty())
        {
            ticketPriceForUsers.forEach(ticketPriceForUser -> ticketPricePerUser.put(Long.parseLong(ticketPriceForUser[0].toString()),Long.parseLong(ticketPriceForUser[1].toString())));
        }
        List<UserCustomAttribute> userCustomAttributes = userService
                .findUserCustomAttributesByEventId(event.getEventId());

        Set<String> customAttributeNameSet = new HashSet<>();

        userCustomAttributes.forEach(attr -> customAttributeNameSet.add(attr.getName()));

        List<String> customeAttributeNameList = new ArrayList<>(customAttributeNameSet);
        headerList.addAll(customeAttributeNameList);

        List<List<String>> dataRowList = new ArrayList<>();

        for (Winner winner : winnerService.findWinnersByEventAndModule(event.getEventId(), ModuleType.RAFFLE)) {
            Optional<SubmittedRaffleTicket> optionalTicket = submittedRaffleTicketService.findById(winner.getBidId());
            if (optionalTicket.isPresent()) {
                SubmittedRaffleTicket ticket = optionalTicket.get();
                addUserDetails(event, ticketPricePerUser, userCustomAttributes, customeAttributeNameList, dataRowList, ticket, winner.getUserId());
            }
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void addUserDetails(Event event, HashMap<Long, Long> ticketPricePerUser, List<UserCustomAttribute> userCustomAttributes, List<String> customeAttributeNameList, List<List<String>> dataRowList, SubmittedRaffleTicket ticket, long userId)
    {
        Optional<User> userOpt = roUserService.getUserById(userId);
        if(userOpt.isPresent())
        {
            User user = userOpt.get();
            Long aTicketPrice = 0L;
            List<UserCustomAttribute> userAttribute = userCustomAttributes.stream().filter(attr -> attr.getUserId() == user.getUserId()).collect(Collectors.toList());
            Map<String, String> attrMap = new HashMap<>();
            userAttribute.forEach(attr -> attrMap.put(attr.getName(), attr.getValue()));
            //submitted ticket price
            if(ticketPricePerUser.containsKey(user.getUserId())) {
                aTicketPrice = ticketPricePerUser.get(user.getUserId());
            }
            List<String> dataList = new ArrayList<>();
            dataList.add(ticket.getItem().getCode());
            dataList.add(ticket.getItem().getName());
            dataList.add(Long.toString(ticket.getTicketsSubmitted()));
            dataList.add(Long.toString(aTicketPrice* ticket.getTicketsSubmitted()));
            dataList.add(ticket.getItem().getItemCategory() != null ? ticket.getItem().getItemCategory().getName() : STRING_EMPTY);
            dataList.add(dateFormatter.format(getDateInLocal(ticket.getSubmittedDate(), event.getEquivalentTimeZone())));
            dataList.add(BiddingSource.RANDOM.name().equalsIgnoreCase(ticket.getSource().name()) ? AUTOMATIC : ticket.getSource().name());
            dataList.add(Boolean.toString(ticket.isWinner()));
            dataList.add(user.getFirstName());
            dataList.add(user.getLastName());
            dataList.add(Long.toString(user.getPhoneNumber()));
            dataList.add(user.getEmail());
            dataList.add(user.getAddress1());
            dataList.add(user.getAddress2());
            dataList.add(user.getState());
            dataList.add(user.getCityOrProvidence());
            dataList.add(user.getZipcode());
            dataList.add(user.getCountry());
            customeAttributeNameList.forEach(attr -> dataList.add(attrMap.get(attr)));
            if (ticket.getStaffUserId() != null) {
                dataList.add(ticket.getStaffUserId().getFirstName() + " " + ticket.getStaffUserId().getLastName());
            }

            dataRowList.add(dataList);
        }
    }

    @Override
    public void downloadSilenAuctionItemsCSV(PrintWriter writer, Auction auction) {

        List<String> headerList = new ArrayList<>();

        headerList.add(ITEM_NAME);
        headerList.add(ITEM_CODE);
        headerList.add(ITEM_SHORT_NAME);
        headerList.add(BID_INCREMENT_AMOUNT);
        headerList.add(STARTING_BID_AMOUNT);
        headerList.add(BUY_IT_NOW_PRICE);
        headerList.add(ITEM_DESCRIPTION);
        headerList.add(CURRENT_BID_AMOUNT);
        headerList.add(TYPE);
        if (auction.isAllowMultipleWinnersPerItem()) {
            headerList.add(NUMBER_OF_BIDDERS_WHO_CAN_WIN);
        }
        if(Boolean.TRUE.equals(auction.getEnableMarketValue())){
            headerList.add(ITEM_MARKET_VALUE);
        }
        if (auction.isCategoryEnabled()){
            headerList.add(ITEM_CATEGORY);
        }
        List<List<String>> dataRowList = new ArrayList<>();
        for (Item item : itemService.getAllItems(auction.getId(), ModuleType.AUCTION)) {
            List<String> dataList = new ArrayList<>();
            dataList.add(item.getName());
            dataList.add(item.getCode());
            dataList.add(item.getItemShortName() != null ? item.getItemShortName() : STRING_EMPTY);
            dataList.add(Double.toString(item.getBidIncrement()));
            dataList.add(Double.toString(item.getStartingBid()));
            dataList.add(Double.toString(item.getBuyItNowPrice()));
            dataList.add(htmlToPlain(item.getDescription()));
            dataList.add(Double.toString(item.getCurrentBid()));
            dataList.add(item.isLiveAuctionItem() ? "Live" : "Silent");
            if (auction.isAllowMultipleWinnersPerItem()) {
                dataList.add(Integer.toString(item.getNumberOfWinners()));
            }
            if(Boolean.TRUE.equals(auction.getEnableMarketValue())){
                dataList.add(Integer.toString(item.getMarketValue()));
            }
            if (auction.isCategoryEnabled()){
                dataList.add(item.getItemCategory() != null ? item.getItemCategory().getName() : STRING_EMPTY);
            }
            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadRaffleItemsCSV(PrintWriter writer, Raffle raffle) {

        List<String> headerList = new ArrayList<>();
        headerList.add(ITEM_NAME);
        headerList.add(ITEM_CODE);
        headerList.add(ITEM_SHORT_NAME);
        headerList.add(ITEM_DESCRIPTION);
        if (raffle.isAllowMultipleWinnersPerItem()) {
            headerList.add(NUMBER_OF_BIDDERS_WHO_CAN_WIN);
        }
        if (raffle.isCategoryEnabled()){
            headerList.add(ITEM_CATEGORY);
        }

        List<List<String>> dataRowList = new ArrayList<>();
        for (Item item : itemService.getAllItems(raffle.getId(), ModuleType.RAFFLE)) {
            List<String> dataList = new ArrayList<>();
            dataList.add(item.getName());
            dataList.add(item.getCode());
            dataList.add(item.getItemShortName() != null ? item.getItemShortName() : STRING_EMPTY);
            dataList.add(htmlToPlain(item.getDescription()));
            if (raffle.isAllowMultipleWinnersPerItem()) {
                dataList.add(Integer.toString(item.getNumberOfWinners()));
            }
            if (raffle.isCategoryEnabled()){
                dataList.add(item.getItemCategory() != null ? item.getItemCategory().getName() : STRING_EMPTY);
            }
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadCauseAuctionItemsCSV(PrintWriter writer,CauseAuction causeAuction) {

        List<String> headerList = new ArrayList<>();
        headerList.add(ITEM_NAME);
        headerList.add(ITEM_CODE);
        headerList.add(ITEM_SHORT_NAME);
        headerList.add(STARTING_BID_AMOUNT);
        headerList.add(ITEM_DESCRIPTION);

        if (causeAuction.isCategoryEnabled()){
            headerList.add(ITEM_CATEGORY);
        }

        List<List<String>> dataRowList = new ArrayList<>();
        for (Item item : itemService.getAllItems(causeAuction.getId(), ModuleType.CAUSEAUCTION)) {
            List<String> dataList = new ArrayList<>();
            dataList.add(item.getName());
            dataList.add(item.getCode());
            dataList.add(item.getItemShortName() != null ? item.getItemShortName() : STRING_EMPTY);
            dataList.add(Double.toString(item.getStartingBid()));
            dataList.add(htmlToPlain(item.getDescription()));
            if (causeAuction.isCategoryEnabled()){
                dataList.add(item.getItemCategory() != null ? item.getItemCategory().getName() : STRING_EMPTY);
            }

            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private String htmlToPlain(String html) {
        String plain = STRING_EMPTY;
        if (html != null && html.length() > 0) {
            plain = Jsoup.parse(html).text();
        }
        return plain;
    }

	@Override
	public void downloadTicketHolderData(PrintWriter writer, Event event, User loggedInUser, Long recurringEventId, DataType dateType, String startTime, String endTime) {

        List<String> headerList = new ArrayList<>();
        List<List<String>> ticketHolderData = new ArrayList<>();

        if(TimeZoneUtil.matchWithFormat(startTime)){
            NotAcceptableException.NotAceptableExeceptionMSG execeptionMSG = NotAcceptableException.NotAceptableExeceptionMSG.STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT;
            String message = STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT.replace("${date}", STARTTIME);
            execeptionMSG.setErrorMessage(message);
            execeptionMSG.setDeveloperMessage(message);
            throw new NotAcceptableException(execeptionMSG);
        }else if(TimeZoneUtil.matchWithFormat(endTime)){
            NotAcceptableException.NotAceptableExeceptionMSG execeptionMSG = NotAcceptableException.NotAceptableExeceptionMSG.STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT;
            String message = STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT.replace("${date}", ENDTIME);
            execeptionMSG.setErrorMessage(message);
            execeptionMSG.setDeveloperMessage(message);
            throw new NotAcceptableException(execeptionMSG);
        }

        Date startTime1 = !isEmpty(startTime)? TimeZoneUtil.getDateInUTC(startTime, event.getEquivalentTimeZone(), yyyy_MM_dd_HH_mm_ss) : null;
        Date endTime1 = !isEmpty(endTime)? TimeZoneUtil.getDateInUTC(endTime, event.getEquivalentTimeZone(), yyyy_MM_dd_HH_mm_ss) : null;

        logger.info("setRequestCSVDataForEvent -> start : {}" , System.currentTimeMillis());
		ticketingCSVService.setAllTicketHolderCSVDataForEvent(event, loggedInUser, headerList, ticketHolderData, recurringEventId, dateType, startTime1, endTime1);
        logger.info("setRequestCSVDataForEvent -> End : {}" , System.currentTimeMillis());

        logger.info("downloadCSVFile -> Start : {}" , System.currentTimeMillis());
        try {
            this.downloadCSVFile(writer, headerList, ticketHolderData);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        logger.info("downloadCSVFile -> End : {}" , System.currentTimeMillis());
    }

    @Override
	public void downloadTicketBuyerData(PrintWriter writer, Event event, Long recurringEventsId, DataType dataType, String startTime, String endTime) {

        List<String> headerList = new ArrayList<>();
        List<List<String>> ticketPurchaserData = new ArrayList<>();

        if(TimeZoneUtil.matchWithFormat(startTime)){
            NotAcceptableException.NotAceptableExeceptionMSG execeptionMSG = NotAcceptableException.NotAceptableExeceptionMSG.STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT;
            String message = STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT.replace("${date}", STARTTIME);
            execeptionMSG.setErrorMessage(message);
            execeptionMSG.setDeveloperMessage(message);
            throw new NotAcceptableException(execeptionMSG);
        }else if(TimeZoneUtil.matchWithFormat(endTime)){
            NotAcceptableException.NotAceptableExeceptionMSG execeptionMSG = NotAcceptableException.NotAceptableExeceptionMSG.STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT;
            String message = STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT.replace("${date}", ENDTIME);
            execeptionMSG.setErrorMessage(message);
            execeptionMSG.setDeveloperMessage(message);
            throw new NotAcceptableException(execeptionMSG);
        }

        Date startTime1 = !isEmpty(startTime)? TimeZoneUtil.getDateInUTC(startTime, event.getEquivalentTimeZone(), yyyy_MM_dd_HH_mm_ss) : null;
        Date endTime1 = !isEmpty(endTime) ? TimeZoneUtil.getDateInUTC(endTime, event.getEquivalentTimeZone(), yyyy_MM_dd_HH_mm_ss) : null;

		ticketingCSVService.setAllTicketBuyerCSVDataForEvent(event, headerList, ticketPurchaserData, recurringEventsId, dataType, startTime1, endTime1);

        try {
            this.downloadCSVFile(writer, headerList, ticketPurchaserData);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void downloadTicketBuyerAndHolderData(PrintWriter writer, Event event, Long recurringEventsId) {

        List<String> headerList = new ArrayList<>();
        List<List<String>> buyerAndHolderData = new ArrayList<>();

        ticketingCSVService.setTicketBuyerAndHolderCSVData(event, headerList, buyerAndHolderData, recurringEventsId, null, null, false);

        this.downloadCSVFile(writer, headerList, buyerAndHolderData);

    }

    @Override
    public void downloadDeletedTicketBuyerAndHolderData(PrintWriter writer, Event event, Long recurringEventsId) {

        List<String> headerList = new ArrayList<>();
        List<List<String>> buyerAndHolderData = new ArrayList<>();

        ticketingCSVService.setTicketBuyerAndHolderCSVData(event, headerList, buyerAndHolderData, recurringEventsId, null, null, true);

        this.downloadCSVFile(writer, headerList, buyerAndHolderData);

    }

    @Override
    public void downloadAttendeeDataWithTicketPriceBreakDown(PrintWriter writer, Event event) {

        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        headerList.add(GUEST_TICKET);
        headerList.add("UserId");
        headerList.add("EventTicketId");
        headerList.add("Ticket Format");
        headerList.add("Ticket Type");
        headerList.add("Ticket Price");
        headerList.add("Payment Status");
        headerList.add("Refunded Amount");
        headerList.add("Accelevents Ticket Fee");
        headerList.add("Stripe Processing Fee");
        headerList.add("Remaining Amount to be paid");
        List<List<String>> ticketPurchaserData = new ArrayList<>();

        List<Long> userIds = getListofDistinctUserIdEvent(event);
        ticketingCSVService.setTicketHolderCSVDataWithPayBreakDownForEvent(event, headerList, ticketPurchaserData, userIds);

        this.downloadCSVFile(writer, headerList, ticketPurchaserData);

    }

    @Override
    public void downloadDonationPerformanceData(PrintWriter writer, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.PHONE_NUMBER);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.ADDRESS_1);
        headerList.add(HEADER.ADDRESS_2);
        headerList.add(HEADER.STATE);
        headerList.add(HEADER.CITY);
        headerList.add(HEADER.ZIP_CODE);
        headerList.add(HEADER.COUNTRY);
        headerList.add(HEADER.DONATION_AMOUNT);
        headerList.add(HEADER.DONATION_TIME);
        headerList.add(HEADER.DONATION_SOURCE);
        headerList.add(HEADER.IS_REFUNDED);
        headerList.add(HEADER.NOTE);
        headerList.add(HEADER.STAFF_NAME);
        List<List<String>> dataRowList = new ArrayList<>();
        if (event.getGoalStartingAmount() > 0) {
            List<String> dataList = new ArrayList<>();
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(STRING_EMPTY);
            dataList.add(String.valueOf(event.getGoalStartingAmount()));
            dataList.add(STRING_EMPTY);
            dataList.add("Cash");
            dataList.add("Goal starting amount");
            dataList.add(STRING_EMPTY);
            dataRowList.add(dataList);
        }

        List<Donation> donations = donationService.findAllDonationForEvent(event.getEventId());
        List<Long> donationIds = donations.stream().map(Donation::getId).collect(Collectors.toList());
        addDonationDetails(event, dataRowList, donations, donationIds);

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void addDonationDetails(Event event, List<List<String>> dataRowList, List<Donation> donations, List<Long> donationIds) {
        if (!donationIds.isEmpty()) {
            List<Long> donationIdsWithStripeTransactions = stripeTransactionService.findBySourceAndSourceIdIns(
                    StripeTransactionSource.DONATION_ONLINE, donationIds);

            donations.forEach(donation -> {
                User user = donation.getUser();
                List<String> dataList = new ArrayList<>();
                dataList.add(user.getFirstName());
                dataList.add(user.getLastName());
                dataList.add(user.getPhoneNumber() != 0 ? Long.toString(user.getPhoneNumber()) : STRING_EMPTY);
                dataList.add(user.getEmail());
                dataList.add(user.getAddress1());
                dataList.add(user.getAddress2());
                dataList.add(user.getState());
                dataList.add(user.getCityOrProvidence());
                dataList.add(user.getZipcode());
                dataList.add(user.getCountry());
                dataList.add(Double.toString(donation.getAmount()));
                addDonationDetail(event, donationIdsWithStripeTransactions, donation, dataList);
                dataRowList.add(dataList);
            });
        }
    }

    private void addDonationDetail(Event event, List<Long> donationIdsWithStripeTransactions, Donation donation, List<String> dataList) {
        dataList.add(donation.getTime() != null ? dateFormatter.format(getDateInLocal(donation.getTime(), event.getEquivalentTimeZone())) : STRING_EMPTY);
        dataList.add(donationIdsWithStripeTransactions.contains(donation.getId()) ? "Card" : "Cash");
        dataList.add(donation.getRefunded() != null && donation.getRefunded() ? CAMELCASE_YES : CAMELCASE_NO);
        dataList.add(donation.getNote());
        if (donation.getStaffUserId() != null) {
            dataList.add(donation.getStaffUserId().getFirstName() + " " + donation.getStaffUserId().getLastName());
        }
    }

    @Override
    public void downloadEventTicketingSaleData(PrintWriter writer) {
        List<String> headerList = new ArrayList<>();
        headerList.add("Event name");
        headerList.add(HEADER.EVENT_URL);
        headerList.add("Ticketing event ending date/time");
        headerList.add("Stripe customer ID for the staff who activated the account");
        headerList.add("Total tickets sold with credit card card");
        headerList.add("Total Fees for tickets sold with credit card card");
        headerList.add("Total tickets sold with Cash");
        headerList.add("Total Fees for tickets sold with Cash");

        List<List<String>> dataRowList = new ArrayList<>();

        List<TicketingDownloadPurchase> ticketingDownloadPurchases = ticketingService
                .findAllTicketingDownloadPurchase();

        for (TicketingDownloadPurchase ticketingDownloadPurchase : ticketingDownloadPurchases) {

            List<String> dataList = new ArrayList<>();

            dataList.add(ticketingDownloadPurchase.getEventName());
            dataList.add(ticketingDownloadPurchase.getEventUrl());
            dataList.add(DateUtils.getFormattedDateString(ticketingDownloadPurchase.getEndDate()));
            // need to query stripe active and staff table based on event
            dataList.add(this.stripeService.findStripeCustomerIdByEvent(ticketingDownloadPurchase.getEvent()));
            // need to query tickeing purchase table based on event in one call
            addTicketingCardAndCashSale(ticketingDownloadPurchase, dataList);
            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void addTicketingCardAndCashSale(TicketingDownloadPurchase ticketingDownloadPurchase, List<String> dataList) {
        TicketingSale cardSale = eventTicketsRepoService.getTicketingCardSale(ticketingDownloadPurchase.getEvent());
        if(cardSale != null) {
            dataList.add(
                    cardSale.getCount() != null ? String.valueOf(cardSale.getCount()) : STRING_EMPTY);
            dataList.add(
                    cardSale.getAmount() != null ? String.valueOf(cardSale.getAmount()) : STRING_EMPTY);
        }
        TicketingSale cashSale = eventTicketsRepoService.getTicketingCashSale(ticketingDownloadPurchase.getEvent());
        if(cashSale != null) {
            dataList.add(
                    cashSale.getCount() != null ? String.valueOf(cashSale.getCount()) : STRING_EMPTY);
            dataList.add(
                    cashSale.getAmount() != null ? String.valueOf(cashSale.getAmount()) : STRING_EMPTY);
        }
    }

    @Override
    public void downloadBidderRegistationDataCSV(PrintWriter writer, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(PHONE_NUMBER);
        headerList.add(EMAIL);
        headerList.add("Bidder Number");
        headerList.add(STAFF_NAME);

        List<List<String>> dataRowList = new ArrayList<>();
        List<BidderNumber> bidderNumbers = bidderNumberService.findAllByEventIdDesc(event);

        for (BidderNumber bidderNumber : bidderNumbers) {

            List<String> dataList = new ArrayList<>();
            Optional<User> userOptional = this.roUserService.getUserById(bidderNumber.getUserId());
            if (userOptional.isPresent()) {
                User user = userOptional.get();
                dataList.add(user.getFirstName());
                dataList.add(user.getLastName());
                dataList.add(String.valueOf(user.getPhoneNumber()));
                dataList.add(user.getEmail());
            } else {
                dataList.add(STRING_EMPTY);
                dataList.add(STRING_EMPTY);
                dataList.add(STRING_EMPTY);
                dataList.add(STRING_EMPTY);
            }
            dataList.add(String.valueOf(bidderNumber.getBidderNumber()));
            if (bidderNumber.getStaffUserId() != null) {
                dataList.add(bidderNumber.getStaffUserId().getFirstName() + " " + bidderNumber.getStaffUserId().getLastName());
            }
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadAllContactDataCSV(PrintWriter writer, Event event, List<Long> ids,Long contactsListId) {

        List<String> headerList = new ArrayList<>();
        headerList.add(CONTACT_SPACE_ID);
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(EMAIL);
        headerList.add(PHONE_NUMBER);
        List<CustomFormAttribute> contactEnabledAttributes = customFormAttributeService.getContactEnabledAttributesOrderByAttributeOrder(event, -1L);
        if(CollectionUtils.isNotEmpty(contactEnabledAttributes)){
            for (CustomFormAttribute customAttribute : contactEnabledAttributes) {
                headerList.add(customAttribute.getName());
            }
        }
        Map<Long, CustomFormAttributeData> contactAttributeDataMap = new HashMap<>();
        List<ContactDto> contacts = contactService.getContactsByIdsAndContactsListId(event, ids,contactsListId);
        if(!contacts.isEmpty()) {
            List<Long> contactAttributeIds = contacts.stream().filter(e -> NumberUtils.isNumberGreaterThanZero(e.getContactAttributeId())).map(ContactDto::getContactAttributeId).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(contactAttributeIds)) {
                contactAttributeDataMap = customFormAttributeDataRepository.findByIdIn(contactAttributeIds).stream().collect(Collectors.toMap(CustomFormAttributeData::getId, Function.identity(), (e1, e2) -> e1));
            }
        }
        logger.info("downloadAllContactDataCSV using eventId {},contactsListId {} , ids size {}",event.getEventId(),contactsListId,ids.size());
        List<List<String>> dataRowList = new ArrayList<>();
        for (ContactDto contact : contacts) {
            List<String> dataList = new ArrayList<>();
            CustomAttributesResponseDto contactAttributeResponseDto = new CustomAttributesResponseDto();
            dataList.add(String.valueOf(contact.getId()));
            dataList.add(contact.getFirstName() != null ? contact.getFirstName() : STRING_EMPTY);
            dataList.add(contact.getLastName() != null ? contact.getLastName() : STRING_EMPTY);
            dataList.add(contact.getEmail() != null ? contact.getEmail() : STRING_EMPTY);
            dataList.add(Long.toString(contact.getPhoneNumber()));
            dataRowList.add(dataList);
            if(contactAttributeDataMap.get(contact.getContactAttributeId()) != null){
                contactAttributeResponseDto = customFormAttributeService.getCustomAttributeResponseDto(contactAttributeDataMap.get(contact.getContactAttributeId()));
            }

            for (CustomFormAttribute contactAttribute : contactEnabledAttributes) {
                String value = getAttributeValueForContact(contactAttributeResponseDto, contactAttribute);
                dataList.add(value != null ? value : STRING_EMPTY);
            }
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadAttendeeNumberDataCSV(PrintWriter writer, Event event) {

        List<String> headerList = new ArrayList<>();
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(TICKET_TYPE);
        headerList.add(ASSIGNED_SEQUENCE);
        headerList.add(ASSIGNED_NUMBER);

        List<List<String>> dataRowList = new ArrayList<>();
        for (AttendeeSequenceNumbersDto attendeeSequenceNumbersDto : autoAssignedAttendeeNumbersService.getAttendeeWithAssignedSequences(event)) {
            List<String> dataList = new ArrayList<>();
            dataList.add(isNotBlank(attendeeSequenceNumbersDto.getFirstName()) ? attendeeSequenceNumbersDto.getFirstName() : STRING_EMPTY);
            dataList.add(isNotBlank(attendeeSequenceNumbersDto.getLastName()) ? attendeeSequenceNumbersDto.getLastName() : STRING_EMPTY);
            dataList.add(attendeeSequenceNumbersDto.getTicketType());
            dataList.add(isNotBlank(attendeeSequenceNumbersDto.getSequenceName()) ? attendeeSequenceNumbersDto.getSequenceName() : STRING_EMPTY);
            dataList.add(isNotBlank(attendeeSequenceNumbersDto.getSequenceNumber()) ? attendeeSequenceNumbersDto.getSequenceNumber() : STRING_EMPTY);
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void extractCommonExhibitorData(PrintWriter writer, Long exhibitorId, Staff staff, Event event, Map<Long, ExhibitorSetting> exhibitorSettingMap) {
        ExhibitorSetting deafultExhibitorSetting = prepareExhibitorSettingsWithDefaultData(event);
        Ticketing ticketing = ticketingHelperService.findTicketingByEventIdOrThrowError(event);
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllAttendeeProfiles(event.getEventId());
        Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(), (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));
        List<Staff> allStaffByEvent = staffService.findAllByEventId(event.getEventId());
        this.downloadLeadsCSV(writer, exhibitorId, staff, event, deafultExhibitorSetting, ticketing, attendeeMap, allStaffByEvent, exhibitorSettingMap);
    }

    @Override
    @Transactional
    public void downloadLeadsCSV(PrintWriter writer,
                                 Long exhibitorId,
                                 Staff staff,
                                 Event event,
                                 ExhibitorSetting defaultExhibitorSetting,
                                 Ticketing ticketing,
                                 Map<Long, AttendeeProfileDto> attendeeProfileDtoMap,
                                 List<Staff> allStaffByEvent,
                                 Map<Long,ExhibitorSetting> exhibitorSettingMap) {
            CSVHeaderAndData csvHeaderAndData = prepareExhibitorLeadsCSV(exhibitorId, staff, event,
                    defaultExhibitorSetting, ticketing, attendeeProfileDtoMap, allStaffByEvent, exhibitorSettingMap);

            this.downloadCSVFile(writer, csvHeaderAndData.getHeaderList(), csvHeaderAndData.getDataRowList());
    }

    @Override
    public void downloadLeadHistoryCSV(PrintWriter writer, Long exhibitorId, Staff staff, User loggedInUser, Event event) {
        CSVHeaderAndData csvHeaderAndData = prepareExhibitorLeadsHistoryCSV(exhibitorId, staff, event);
        this.downloadCSVFile(writer, csvHeaderAndData.getHeaderList(), csvHeaderAndData.getDataRowList());
    }

    private CSVHeaderAndData prepareExhibitorLeadsHistoryCSV(Long exhibitorId,
                                                 Staff staff,
                                                 Event event){

        List<String> headerList = new ArrayList<>();
        List<List<String>> rows = new ArrayList<>();
        headerList.add(HEADER.TIMESTAMP);
        headerList.add("Lead Retriever First Name");
        headerList.add("Lead Retriever Last Name");
        headerList.add("Lead Retriever ID");
        headerList.add("Attendee First Name");
        headerList.add("Attendee Last Name");
        headerList.add("Attendee ID");

        List<UserActivity> userActivities = dynamoDBService.executeQueryOnIndex(List.of(LEAD_ADD_ACTIVITY, LEAD_UPDATE_ACTIVITY), event.getEventId(), Boolean.TRUE, UserActivity.class);

        List<UserActivity> filteredActivities = userActivities.stream()
                .filter(ua -> {
                    Map<String, Object> desc = ua.getDescription();
                    return desc != null && desc.get("expoId") != null && org.apache.commons.lang3.math.NumberUtils.isDigits(desc.get("expoId").toString()) && exhibitorId.equals(Long.valueOf(desc.get("expoId").toString()));
                })
                .collect(Collectors.toList());

        List<Long> userIds = filteredActivities.stream()
                .map(UserActivity::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, User> userMap = roUserService.getListOfUsersByUserIds(userIds).stream().collect(Collectors.toMap(User::getUserId, Function.identity(), (existing, replacement) -> existing));

        Map<Long, User> staffMap = staffService.findAllByEventId(event.getEventId()).stream().collect(Collectors.toMap(Staff::getUserId, Staff::getUser, (existing, replacement) -> existing));

        for(UserActivity userActivity: filteredActivities){
            List<String> dataList = new ArrayList<>();
            dataList.add(TimeZoneUtil.getDateInLocal(new Date(userActivity.getTimestamp()),event.getEquivalentTimeZone(),null));
            Map<String, Object> description = userActivity.getDescription();
            if(description != null && description.get("staffId") != null && org.apache.commons.lang3.math.NumberUtils.isDigits(description.get("staffId").toString()) && staffMap.get(Long.valueOf(description.get("staffId").toString())) != null) {
                User leadRetriever = staffMap.get(Long.valueOf(description.get("staffId").toString()));
                dataList.add(leadRetriever.getFirstName() != null ? leadRetriever.getFirstName() : STRING_EMPTY);
                dataList.add(leadRetriever.getLastName() != null ? leadRetriever.getLastName() : STRING_EMPTY);
                dataList.add(leadRetriever.getUserId() != null ? leadRetriever.getUserId().toString() : STRING_EMPTY);
            }
            else {
                dataList.add(STRING_EMPTY);
                dataList.add(STRING_EMPTY);
                dataList.add(STRING_EMPTY);
            }

            if(NumberUtils.isNumberGreaterThanZero(userActivity.getUserId()) && userMap.get(userActivity.getUserId()) != null) {
                User attendee = userMap.get(userActivity.getUserId());
                dataList.add(attendee.getFirstName() != null ? attendee.getFirstName() : STRING_EMPTY);
                dataList.add(attendee.getLastName() != null ? attendee.getLastName() : STRING_EMPTY);
                dataList.add(attendee.getUserId() != null ? attendee.getUserId().toString() : STRING_EMPTY);
            }
            else {
                dataList.add(STRING_EMPTY);
                dataList.add(STRING_EMPTY);
                dataList.add(STRING_EMPTY);
            }
            rows.add(dataList);
        }

        return  new CSVHeaderAndData(headerList, rows);
    }

    private CSVHeaderAndData prepareExhibitorLeadsCSV(Long exhibitorId,
                                                      Staff staff,
                                                      Event event,
                                                      ExhibitorSetting deafultExhibitorSetting,
                                                      Ticketing ticketing,
                                                      Map<Long, AttendeeProfileDto> attendeeMap,
                                                      List<Staff> allStaffByEvent,
                                                      Map<Long,ExhibitorSetting> exhibitorSettingMap) {

        ExhibitorSetting exhibitorSetting = exhibitorSettingMap.getOrDefault(exhibitorId,null);
        if(exhibitorSetting == null){
            exhibitorSetting = deafultExhibitorSetting;
        }
        boolean  isHolderDataPreference = ticketing.getCollectTicketHolderAttributes();
        List<List<String>> dataRowList = new ArrayList<>();
        List<LeadRetriverData> batchLeadRetrieverDatas = getLeadRetriverData(exhibitorId, staff);

        List<Long> userIds = batchLeadRetrieverDatas.stream().filter(e -> null != e.getUserId()).map(LeadRetriverData::getUserId).distinct().collect(Collectors.toList());

        //Later need to remove this code : START
        Map<Long, BasicUserEventBillingDto> mapUserDetails = this.getBasicUserEventBillingDtoList(userIds);
        //Later need to remove this code : END


        List<Long> eventTicketIds = batchLeadRetrieverDatas.stream().filter(e -> null != e.getUserId()).filter(e->NumberUtils.isNumberGreaterThanZero(e.getEventTicketId())).map(LeadRetriverData::getEventTicketId).distinct().collect(Collectors.toList());


        Map<Long, TicketAttributeValueDto1> eventTicketMap = exhibitorSettingsService.prepareAttributeValueDto(eventTicketIds);

        Map<Long, List<Staff>> staffMapWithUser = null;
        if (CollectionUtils.isNotEmpty(userIds)) {

            List<Staff> staffList = staffService.findListOfStaffByEventAndRoleAndUserIds(exhibitorId, userIds, event);
            staffMapWithUser = staffList.stream().collect(Collectors.groupingBy(Staff::getUserId));
        }

        List<String> emailList = new ArrayList<>();
        Map<Long, TicketHolderDetailDto> ticketHolderDetailDtoMap = new HashMap<>();
        batchLeadRetrieverDatas
                .forEach(e -> {
                    TicketHolderDetailDto leadData = JsonMapper.stringtoObject(e.getLeadData(), TicketHolderDetailDto.class);
                    if (null != leadData) {
                        leadService.setLeadHolderName(leadData, null != e.getEventTicketId() ? eventTicketMap.get(e.getEventTicketId()) : null, mapUserDetails, e.getUserId(), isHolderDataPreference);
                        ticketHolderDetailDtoMap.put(e.getId(), leadData);
                        if (StringUtils.isNotBlank(leadData.getHolderEmail())) {
                            emailList.add(leadData.getHolderEmail());
                        }
                    }
                });

        Map<String, BasicUserDto> emailUserMap = userService.findCountryCodeByByEmailIn(emailList).
                stream()
                .collect(Collectors.toMap(
                        dto -> dto.getEmail().toLowerCase(),
                        Function.identity(),
                        (existing, replacement) -> existing,
                        HashMap::new
                ));

        Map<Long,String> leadOwnerStaffMap = getLeadOwnerStaffMap(batchLeadRetrieverDatas);
       /* Refer this comment to remove below code https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523 */
        List<String> profileQuestionsInSeq = getProfileQuestionsHeaderInSeq(exhibitorSetting );

        List<TicketHolderRequiredAttributes> holderQuestions = exhibitorSettingsService.getHolderQuestions(exhibitorSetting,event,isHolderDataPreference);

        List<String> headerList = new ArrayList<>();
        headerList.addAll(defaultMandatoryHeaders());
        /* Refer this comment to remove below code https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523 */
        headerList.addAll(profileQuestionsInSeq);
        headerList.addAll(addTicketHolderRequiredAttributesToHeader(holderQuestions));
        headerList.addAll(addDefaultHeaders());
        headerList.addAll(addAttendeeEngagementHeaders());

        List<AttendeeExpoEngagementDetail> attendeeExpoEngagementDetailList = leaderboardService.getAttendeeExpoEngagementDetail(event, exhibitorId);
        Map<Long, AttendeeExpoEngagementDetail> attendeeExpoEngagementDetailMap = attendeeExpoEngagementDetailList.stream()
                .collect(Collectors.toMap(AttendeeExpoEngagementDetail::getUserId, Function.identity()));

        List<Object[]> virtualEventLoggings = virtualEventLoggingService.findAllByExhibitorId(exhibitorId);
        Map<String, Object[]> virtualEventLoggingsMap = virtualEventLoggings.stream().collect(Collectors.toMap(e -> String.valueOf(e[3]).toLowerCase(), Function.identity()));

        List<LeadRetriverData> oldestCreatedLeadListByUser = new ArrayList<>();
        Map<ExhibitorIdUserEmailDto,Set<String>> exhibitorIdUserIdDtoSourceSetMap = new HashMap<>();
        Map<ExhibitorIdUserEmailDto, List<LeadRetriverData>> exhibitorIdUserIdDtoLeadRetriverDataMap = batchLeadRetrieverDatas.stream().collect(Collectors.groupingBy(lead -> new ExhibitorIdUserEmailDto(lead.getExhibitorId(),ticketHolderDetailDtoMap.get(lead.getId()).getHolderEmail())));

        for(Map.Entry<ExhibitorIdUserEmailDto, List<LeadRetriverData>> entry : exhibitorIdUserIdDtoLeadRetriverDataMap.entrySet()){
            List<LeadRetriverData> getDataFromMap = entry.getValue();
            Optional<LeadRetriverData> leadRetriverDataOptional = getDataFromMap.stream().min(Comparator.comparing(LeadRetriverData :: getCreatedOn));
            leadRetriverDataOptional.ifPresent(oldestCreatedLeadListByUser::add);
            exhibitorIdUserIdDtoSourceSetMap.put(entry.getKey(), getDataFromMap.stream().map(LeadRetriverData::getSource).collect(Collectors.toSet()));
        }

        for (LeadRetriverData leadRetriverData : oldestCreatedLeadListByUser) {
            TicketHolderDetailDto ticketHolderData = ticketHolderDetailDtoMap.get(leadRetriverData.getId());
            AttendeeProfileDto attendeeProfileDto = getAttendeeProfileDto(emailUserMap, attendeeMap, ticketHolderData);
            if(attendeeProfileDto == null || attendeeProfileDto.isExposeProfileToOtherUser() || !holderQuestions.isEmpty()) {
                setDataForLeads(event,  dataRowList, eventTicketMap, staffMapWithUser, ticketHolderDetailDtoMap, leadOwnerStaffMap, profileQuestionsInSeq, holderQuestions, allStaffByEvent, attendeeExpoEngagementDetailMap, virtualEventLoggingsMap, exhibitorIdUserIdDtoSourceSetMap, leadRetriverData, ticketHolderData, attendeeProfileDto,isHolderDataPreference);
            }
        }
        return new CSVHeaderAndData(headerList, dataRowList);
    }

    private Map<Long, BasicUserEventBillingDto> getBasicUserEventBillingDtoList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        List<BasicUserEventBillingDto> userDataList = userService.findByUserIdIn(userIds);

        return userDataList.stream()
                .filter(dto -> dto != null && dto.getUserId() != null)
                .collect(Collectors.toMap(
                        BasicUserEventBillingDto::getUserId,
                        Function.identity(),
                        (existing, replacement) -> {
                            return existing;
                        },
                        HashMap::new
                ));
    }

    private Map<Long, TicketAttributeValueDto1>  prepareEventTicketMap(List<LeadRetriverData> leadRetrieverDatas){
        Set<Long> eventTicketIds = leadRetrieverDatas.parallelStream()
                .filter(Objects::nonNull)
                .filter(lead -> lead.getUserId() != null)
                .filter(lead -> lead.getEventTicketId() != null)
                .filter(lead -> lead.getEventTicketId() > 0)
                .map(LeadRetriverData::getEventTicketId)
                .collect(Collectors.toSet());

        if (eventTicketIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return prepareAttributeValueDto(new ArrayList<>(eventTicketIds));
    }

    public Map<Long, TicketAttributeValueDto1> prepareAttributeValueDto(List<Long> eventTicketIds) {
        if (CollectionUtils.isEmpty(eventTicketIds)) {
            return Collections.emptyMap();
        }

        // Process large lists in batches
        if (eventTicketIds.size() > 500) {
            return prepareAttributeValueDtoBatched(eventTicketIds);
        }

        return processSingleBatch(eventTicketIds);
    }

    private Map<Long, TicketAttributeValueDto1> prepareAttributeValueDtoBatched(List<Long> eventTicketIds) {
        Map<Long, TicketAttributeValueDto1> allTicketAttributes = new ConcurrentHashMap<>();
        List<List<Long>> batches = Lists.partition(eventTicketIds, 500);

        // Process batches in parallel for better performance
        batches.parallelStream().forEach(batch -> {
            try {
                Map<Long, TicketAttributeValueDto1> batchResults = processSingleBatch(batch);
                allTicketAttributes.putAll(batchResults);
            } catch (Exception e) {
                logger.error("Error processing ticket attribute batch of size {}: {}", batch.size(), e.getMessage());
                // Continue with other batches
            }
        });

        return allTicketAttributes;
    }

    private Map<Long, TicketAttributeValueDto1> processSingleBatch(List<Long> eventTicketIds) {
        List<TicketHolderAttributeDto> ticketHolderAttributes =
                holderAttributeService.findByEventTicketIds(eventTicketIds);


        return ticketHolderAttributes.stream()
                .filter(Objects::nonNull)
                .filter(attr -> StringUtils.isNotBlank(attr.getJsonValue()))
                .collect(Collectors.toMap(
                        TicketHolderAttributeDto::getEventTicketId,
                        this::parseTicketAttribute,
                        (existingValue, newValue) -> {
                            logger.info("Duplicate eventTicketId found: {}, keeping existing value",
                                    existingValue != null ? "existing" : "new");
                            return existingValue != null ? existingValue : newValue;
                        },
                        HashMap::new
                ));
    }

    private TicketAttributeValueDto1 parseTicketAttribute(TicketHolderAttributeDto dto) {
        try {
            return TicketHolderAttributesHelper.parseJsonToObject(dto.getJsonValue());
        } catch (Exception e) {
            logger.info("Error parsing JSON for eventTicketId {}: {}", dto.getEventTicketId(), e.getMessage());
            return null;
        }
    }

    private void setDataForLeads(Event event, List<List<String>> dataRowList, Map<Long, TicketAttributeValueDto1> eventTicketMap, Map<Long, List<Staff>> staffMapWithUser, Map<Long, TicketHolderDetailDto> ticketHolderDetailDtoMap, Map<Long, String> leadOwnerStaffMap, List<String> profileQuestionsInSeq, List<TicketHolderRequiredAttributes> holderQuestions, List<Staff> staffList, Map<Long, AttendeeExpoEngagementDetail> attendeeExpoEngagementDetailMap, Map<String, Object[]> virtualEventLoggingsMap, Map<ExhibitorIdUserEmailDto, Set<String>> exhibitorIdUserIdDtoSourceSetMap, LeadRetriverData leadRetriverData, TicketHolderDetailDto ticketHolderData, AttendeeProfileDto attendeeProfileDto,boolean isHolderDataPreference) { //NOSONAR
        List<String> dataList = new ArrayList<>();
        boolean isExhibitorStaff =checkIsUserExhibitorStaff(staffList,leadRetriverData.getUserId(),leadRetriverData.getExhibitorId());
        dataList.addAll(addDefaultMandatoryData(ticketHolderData));
        /* Refer this comment to remove below code https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523 */
        dataList.addAll(profileQuestionsDataPreparation(profileQuestionsInSeq, attendeeProfileDto,ticketHolderData));

        dataList.addAll(holderQuestionsPreparation(holderQuestions,
                eventTicketMap,
                ticketHolderData,
                attendeeProfileDto,leadRetriverData,isHolderDataPreference));//NOSONAR

        dataList.add(Long.toString(leadRetriverData.getEventTicketId()));
        dataList.add(leadRetriverData.getNotes());
        dataList.add(leadRetriverData.getLeadRating());
        dataList.add(getDateInLocal(leadRetriverData.getCreatedOn(), event.getEquivalentTimeZone(), null));

        dataList.add(getRoleOfUser(staffMapWithUser, leadRetriverData));
        String owner = "";
        if(leadRetriverData.getStaffId() != null){
          if(leadRetriverData.getStaffId() == 0){
              owner = UNASSIGNED;
          }
          else{
              owner = leadOwnerStaffMap.get(leadRetriverData.getStaffId());
          }
        }
        else{
            owner = UNASSIGNED;
        }

        dataList.add(owner);
        dataList.add(isExhibitorStaff ? "Yes" : "No");
        dataList.add(leadRetriverData.getSource());
        markOtherLeadGenerate(dataList, leadRetriverData.getExhibitorId(), exhibitorIdUserIdDtoSourceSetMap, ticketHolderDetailDtoMap.get(leadRetriverData.getId()).getHolderEmail());

        addAttendeeEngagementData(attendeeExpoEngagementDetailMap, leadRetriverData, dataList, virtualEventLoggingsMap, ticketHolderData, isExhibitorStaff);
        dataRowList.add(dataList);
    }

    private void markOtherLeadGenerate(List<String> dataList, Long exhibitorId, Map<ExhibitorIdUserEmailDto, Set<String>> exhibitorIdUserIdDtoSourceSetMap, String holderEmail) {
        Set<String> sourceSet = exhibitorIdUserIdDtoSourceSetMap.get(new ExhibitorIdUserEmailDto(exhibitorId, holderEmail));
        dataList.add((sourceSet.contains(null) || sourceSet.contains(MANUAL_TRANSACTION_TYPE) ) ? YES : NO);
        dataList.add(sourceSet.contains(VISIT) ? YES : NO);
        dataList.add(sourceSet.contains(REQUESTED_A_DEMO) ? YES : NO);
        dataList.add(sourceSet.contains(DOCUMENT) ? YES : NO);
    }

    private AttendeeProfileDto getAttendeeProfileDto(Map<String, BasicUserDto> emailUserMap, Map<Long, AttendeeProfileDto> attendeeMap, TicketHolderDetailDto ticketHolderData) {
        BasicUserDto basicUserDto = null;
        if(StringUtils.isNotBlank(ticketHolderData.getHolderEmail())) {
             basicUserDto = emailUserMap.get(ticketHolderData.getHolderEmail().toLowerCase());
        }
        AttendeeProfileDto attendeeProfileDto = null;
        if(basicUserDto!=null){
            attendeeProfileDto = attendeeMap.get(basicUserDto.getUserId());
        }
        return attendeeProfileDto;
    }

    private List<String> defaultMandatoryHeaders(){
        List<String> headerList = new ArrayList<>();
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(EMAIL);
        return headerList;
    }
    private List<String> addDefaultHeaders() {
        List<String> headerList = new ArrayList<>();
        headerList.add(EVENT_TICKET_ID);
        headerList.add(NOTE);
        headerList.add(RATING);
        headerList.add(CREATED_ON);
        headerList.add(ROLE);
        headerList.add(OWNER);
        headerList.add(STAFF);
        headerList.add(LEAD_SOURCE);
        headerList.add(MANUALLY_CAMELCASE);
        headerList.add(VISIT_CAMELCASE);
        headerList.add(CTA_BUTTON);
        headerList.add(DOCUMENT_CAMELCASE);
        return headerList;
    }

    private List<String> addAttendeeEngagementHeaders() {
        List<String> headerList = new ArrayList<>();
        headerList.add(TOTAL_TIME_IN_BOOTH);
        headerList.add(LINK_CLICKED);
        headerList.add(VIDEO_PLAYED);
        headerList.add(CHAT);
        headerList.add(DOCUMENT_DOWNLOADED);
        headerList.add(NUMBER_OF_TIME_BOOTH_VISITED);
        return headerList;
    }

    private void addAttendeeEngagementData(Map<Long, AttendeeExpoEngagementDetail> attendeeExpoEngagementDetailMap, LeadRetriverData leadRetriverData, List<String> dataList, Map<String, Object[]> virtualEventLoggingsMap, TicketHolderDetailDto ticketHolderData, boolean isExhibitorStaff) {
        AttendeeExpoEngagementDetail attendeeExpoEngagementDetailMapElement = attendeeExpoEngagementDetailMap.get(leadRetriverData.getUserId());
        if (isExhibitorStaff) {
            dataList.add(NOT_APPLICABLE);
            dataList.add(NOT_APPLICABLE);
            dataList.add(NOT_APPLICABLE);
            dataList.add(NOT_APPLICABLE);
        } else if (null != attendeeExpoEngagementDetailMapElement) {
            dataList.add(String.valueOf(attendeeExpoEngagementDetailMapElement.getTotalTimeInBooth()));
            if (CollectionUtils.isNotEmpty(attendeeExpoEngagementDetailMapElement.getUniqueLinkClicked())) {
                dataList.add(String.join(", ", attendeeExpoEngagementDetailMapElement.getUniqueLinkClicked()));
            } else {
                dataList.add(EMPTY);
            }
            dataList.add(attendeeExpoEngagementDetailMapElement.getVideoPlayed() ? YES : NO);
            dataList.add(attendeeExpoEngagementDetailMapElement.getChat() ? YES : NO);

        } else {
            dataList.add(EMPTY);
            dataList.add(EMPTY);
            dataList.add(EMPTY);
            dataList.add(EMPTY);
        }

        Object[] array = virtualEventLoggingsMap.get(ticketHolderData.getHolderEmail() !=null ? ticketHolderData.getHolderEmail().toLowerCase() : null );
        String documentDownload = array!=null && array.length>=5 ? (String)array[5] : EMPTY;
        dataList.add(documentDownload);
        String boothVisitCount = array!=null && array.length>=4 ? String.valueOf(array[4]) : EMPTY;
        dataList.add(boothVisitCount);

    }

    private ExhibitorSetting prepareExhibitorSettingsWithDefaultData(Event event) {
        ExhibitorSetting exhibitorSetting;
        exhibitorSetting = new ExhibitorSetting();
        exhibitorSetting.setEventId(event.getEventId());
        exhibitorSetting.setAllowForTicketHolderData(true);
        exhibitorSetting.setGlobal(true);

        List<Long> holderRequiredAttributeId = exhibitorSettingsService.getIdsOfFirstNameLastNameAndEmailFromTicketHolderRequired(event);
        if(!holderRequiredAttributeId.isEmpty()){
            exhibitorSetting.setTicketHolderIdsJson(GeneralUtils.convertLongListToCommaSeparated(holderRequiredAttributeId));
        }
        return exhibitorSetting;
    }

    private List<String> profileQuestionsDataPreparation(
            List<String> profileQuestions,
            AttendeeProfileDto attendeeProfileDto, TicketHolderDetailDto ticketHolderDetailDto) {

          List<String> dataList = new ArrayList<>();


       		if(profileQuestions.contains(EnumProfileQuestions.FIRST_NAME.getValue()))  {
					dataList.add(attendeeProfileDto !=null ? attendeeProfileDto.getFirstName(): ticketHolderDetailDto.getHolderFirstName());
				}
       		if(profileQuestions.contains(EnumProfileQuestions.LAST_NAME.getValue())) {
                dataList.add( attendeeProfileDto !=null ? attendeeProfileDto.getLastName() : ticketHolderDetailDto.getHolderLastName());
            }
        /* Refer this comment to remove below code https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523 */
            addAttendeeProfile(profileQuestions, attendeeProfileDto, dataList);

        return dataList;
    }


    private void addAttendeeProfile(List<String> profileQuestions, AttendeeProfileDto attendeeProfileDto, List<String> dataList) { //NOSONAR
        if(profileQuestions.contains(EnumProfileQuestions.TITLE.getValue())){
            dataList.add(null != attendeeProfileDto ? attendeeProfileDto.getTitle() : STRING_EMPTY);
        }
        if(profileQuestions.contains(EnumProfileQuestions.VIDEO_INTRO.getValue())){
            dataList.add(null != attendeeProfileDto ? attendeeProfileDto.getVideoIntro() : STRING_EMPTY);
        }
        if(profileQuestions.contains(EnumProfileQuestions.COMPANY.getValue())){
            dataList.add(null != attendeeProfileDto ? attendeeProfileDto.getCompany() : STRING_EMPTY);
        }
        if(profileQuestions.contains(EnumProfileQuestions.ACCEPT_MEETING_REQUESTS.getValue())){
            dataList.add(null != attendeeProfileDto ? attendeeProfileDto.getAcceptMeetingRequests().getValue() : MeetingRequests.NO_ONE.getValue());
        }
        if(profileQuestions.contains(EnumProfileQuestions.ACCEPT_DIRECT_MESSAGES.getValue())){
            dataList.add(null != attendeeProfileDto && attendeeProfileDto.isAcceptDirectMessages() ? "Yes" : "No");
        }

        addAttendExtraInfo(profileQuestions, attendeeProfileDto, dataList);
    }

    private void addAttendExtraInfo(List<String> profileQuestions, AttendeeProfileDto attendeeProfileDto, List<String> dataList) {

        AttendeeProfileExtraDetailDto extraDetailDto = null != attendeeProfileDto ? attendeeProfileDto.getExtraInfo() : null;
        if (profileQuestions.contains(EnumProfileQuestions.ABOUT_ME.getValue())) {
            dataList.add(null != extraDetailDto ? extraDetailDto.getAboutMe() : STRING_EMPTY);
        }
        if (profileQuestions.contains(EnumProfileQuestions.WANTS_TO_LEARN.getValue())) {
            dataList.add(null != extraDetailDto ? extraDetailDto.getWantsToLearn() : STRING_EMPTY);
        }
        if (profileQuestions.contains(EnumProfileQuestions.KNOWS.getValue())) {
            dataList.add(null != extraDetailDto ? extraDetailDto.getKnows() : STRING_EMPTY);
        }
        if (profileQuestions.contains(EnumProfileQuestions.WANTS_TO_MEET.getValue())) {
            dataList.add(null != extraDetailDto ? extraDetailDto.getWantsToMeet() : STRING_EMPTY);
        }
    }

    private List<String> holderQuestionsPreparation(
            List<TicketHolderRequiredAttributes> holderQuestions,
            Map<Long, TicketAttributeValueDto1> eventTicketMap,
            TicketHolderDetailDto ticketHolderDetailDto,
            AttendeeProfileDto attendeeProfileDto,
            LeadRetriverData leadRetriverData,boolean isHolderDataPreference) {

        List<String> dataList = new ArrayList<>();
        holderQuestions.forEach(holderRequiredAttributes-> {
            TicketAttributeValueDto1 ticketAttributeValueDto = null;
            if (null != leadRetriverData.getEventTicketId()){
                ticketAttributeValueDto=  eventTicketMap.get(leadRetriverData.getEventTicketId());
             }
            if(ticketAttributeValueDto == null){
                ticketAttributeValueDto = new TicketAttributeValueDto1();
            }
            if(null!=attendeeProfileDto && attendeeProfileDto.isHideContactInfo()){
                if(isBillingOrShipping(holderRequiredAttributes)){
                    String[] values = new String[6];
                    Arrays.fill(values, "");
                    dataList.addAll(Arrays.asList(values));
                }else {
                    dataList.add("");
                }
            }else {
                prepareDataList(null,
                        ticketAttributeValueDto,
                        dataList,
                        holderRequiredAttributes,
                        ticketBuyerUploadsUrl,
                        isHolderDataPreference,
                        true,
                        ticketHolderDetailDto,
                        false, LEAD_SOURCE_MANUAL_TXT.equalsIgnoreCase(leadRetriverData.getSource()));
            }
        });

        return dataList;
    }

    /*  Refer this comment to remove below method https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523*/
    private List<String> getProfileQuestionsHeaderInSeq(ExhibitorSetting exhibitorSetting) {
        List<String> enabledProfileQuestions = GeneralUtils.convertCommaSeparatedToList(exhibitorSetting.getProfileQuestionsJson());

        return enabledProfileQuestions.stream()
                .filter(e -> null != EnumProfileQuestions.fromName(e))
                .map(EnumProfileQuestions::fromName)
                .sorted(Comparator.comparing(EnumProfileQuestions::getSeq))
                .map(EnumProfileQuestions::getValue)
                .collect(Collectors.toList());
    }

    private List<LeadRetriverData> getLeadRetriverData(Long exhibitorId, Staff staff) {
        List<LeadRetriverData> leadRetriverDatas;

        boolean isRestrictedStaff = staff != null
                && StaffRole.leadretriever.equals(staff.getRole())
                && !staff.getExhibitor().isSeeAllLeads();

        if (isRestrictedStaff) {
            leadRetriverDatas = leadService.getAllLeadsDataByExhibitorIdAndStaffId(exhibitorId,staff.getId());
        }else {
            leadRetriverDatas = leadService.getAllLeadsDataByExhibitorId(exhibitorId);
        }
        return leadRetriverDatas;
    }


    private Map<Long, String> getLeadOwnerStaffMap(List<LeadRetriverData> leadRetriverDatas) {
        List<Long> leadOwnerStaffIds = leadRetriverDatas.stream().filter(lead -> lead.getStaffId() != null).map(LeadRetriverData::getStaffId).collect(Collectors.toList());
        Map<Long,String> leadOwnerStaffMap = new HashMap<>();
        if(!leadOwnerStaffIds.isEmpty()){
            List<StaffAndUserDetailDto> staffAndUserDetailDtos = staffService.getAllStaffIdUserFirstAndLastNameByStaffIds(leadOwnerStaffIds);
            leadOwnerStaffMap.putAll(staffAndUserDetailDtos.stream().collect(Collectors.toMap(StaffAndUserDetailDto::getStaffId, s -> s.getFirstName() + STRING_BLANK + s.getLastName())));
        }
        return leadOwnerStaffMap;
    }

    @Override
    public void downloadLeadsCSVForAllExhibitor(PrintWriter writer, Event event) {
        logger.info("start downloadLeadsCSVForAllExhibitor for event {}", event.getEventId());

        // Retrieve necessary data
        List<Exhibitor> exhibitorList = exhibitorService.findAllByEvent(event);
        List<ExhibitorSetting> exhibitorSettings = repoService.findAllByEventId(event.getEventId());
        Map<Long, ExhibitorSetting> exhibitorSettingsMap = new HashMap<>();
        if(!exhibitorSettings.isEmpty()) {
           exhibitorSettingsMap = exhibitorSettings.stream()
                    .collect(Collectors.toMap(
                            setting -> setting.getExhibitorId() == null ? -1L : setting.getExhibitorId(),
                            setting -> setting,
                            (existing, replacement) -> existing,
                            HashMap::new
                    ));
        }else{
            exhibitorSettingsMap.put(-1L,exhibitorSettingsService.getOrCreateGlobalConfig(event));
        }

        Ticketing ticketing = ticketingHelperService.findTicketingByEventIdOrThrowError(event);
        boolean isHolderDataPreference = ticketing.getCollectTicketHolderAttributes();

        List<LeadRetriverData> leadData = leadService.getAllLeadsDataByEventId(event.getEventId());
        Map<Long, List<LeadRetriverData>> leadRetriverMap = new HashMap<>();
        Set<Long> setOfUserIds = new HashSet<>();
        Set<Long> setOfEventTicketIds = new HashSet<>();
        Set<String> setOfEmail = new HashSet<>();
        Map<Long, Map<Long, TicketHolderDetailDto>> ticketHolderDetailDtoMapByExhibitor = new HashMap<>();

        // Organize lead data
        for (LeadRetriverData leadRetriverData : leadData) {
            long exhibitorId = leadRetriverData.getExhibitorId();
            leadRetriverMap.computeIfAbsent(exhibitorId, k -> new ArrayList<>()).add(leadRetriverData);

            if (leadRetriverData.getUserId() != null) setOfUserIds.add(leadRetriverData.getUserId());

            if (leadRetriverData.getUserId() != null && NumberUtils.isNumberGreaterThanZero(leadRetriverData.getEventTicketId())) {
                setOfEventTicketIds.add(leadRetriverData.getEventTicketId());
            }

            TicketHolderDetailDto leadsData = JsonMapper.stringtoObject(leadRetriverData.getLeadData(), TicketHolderDetailDto.class);
            ticketHolderDetailDtoMapByExhibitor
                    .computeIfAbsent(exhibitorId, k -> new HashMap<>())
                    .put(leadRetriverData.getId(), leadsData);

            setOfEmail.add(leadsData.getHolderEmail());
        }

        // Map email to user data
        List<String> emailList = new ArrayList<>(setOfEmail);
        List<BasicUserDto> userDtos = userService.findCountryCodeByByEmailIn(emailList);
        Map<String, BasicUserDto> emailUserMap = new HashMap<>();
        List<Long> userIdList = userDtos.stream()
                .map(dto -> {
                    emailUserMap.putIfAbsent(dto.getEmail().toLowerCase(), dto);
                    return dto.getUserId();
                })
                .collect(Collectors.toList());
        // Retrieve and map staff data
        List<Staff> allStaffByEvent = staffService.findAllByEventId(event.getEventId());
        Map<Long, List<Staff>> staffMap = new HashMap<>();
        for (Staff staff : allStaffByEvent) {
            if (staff.getExhibitor() != null) {
                staffMap.computeIfAbsent(staff.getExhibitorId(), k -> new ArrayList<>()).add(staff);
            }
        }

        List<Long> eventTicketIds = new ArrayList<>(setOfEventTicketIds);
        Map<Long, TicketAttributeValueDto1> eventTicketsMap = exhibitorSettingsService.prepareAttributeValueDto(eventTicketIds);

        // Retrieve and map attendee profiles
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAttendeesProfileForExhibitor(String.valueOf(event.getEventId()),userIdList);
        Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(),  (existingValue, newValue) -> existingValue));

        // Retrieve additional data
        Map<Long, String> leadOwnerStaffMap = getLeadOwnerStaffMap(leadData);
        List<Object[]> virtualEventLoggingsList = virtualEventLoggingService.findAllByEventId(event.getEventId());
        logger.info("downloadLeadsCSVForAllExhibitor virtualEventLoggingsList size: {} and list {}", virtualEventLoggingsList.size(), virtualEventLoggingsList);

        Map<Long, Map<Long, AttendeeExpoEngagementDetail>> attendeeExpoEngagementDetailMap = leaderboardService.getAttendeeExpoEngagementDetailByEvent(event);

        // Prepare CSV data for each exhibitor
        Map<String, List<List<String>>> dataRowListMap = new HashMap<>();
        Map<String, List<String>> dataHeaderMap = new HashMap<>();
        Map<String, String> dataBoothCategory = new HashMap<>();

        List<ExhibitorCategory> exhibitorCategories = exhibitorCategoryService.findByEventId(event.getEventId());
        Map<Long, String> longStringList = exhibitorCategories.stream().collect(Collectors.toMap(ExhibitorCategory::getId, ExhibitorCategory::getName));

        for (Exhibitor exhibitor : exhibitorList) {
            logger.info("start prepareExhibitorLeadsCSV for event {} and exhibitorId {}", event.getEventId(), exhibitor.getId());
            CSVHeaderAndData csvHeaderAndData = prepareExhibitorLeadsCSV(exhibitor.getId(), event, exhibitorSettingsMap, leadRetriverMap, staffMap, eventTicketsMap, attendeeMap, leadOwnerStaffMap, new ArrayList<>(staffMap.keySet()), virtualEventLoggingsList, attendeeExpoEngagementDetailMap,
                    ticketHolderDetailDtoMapByExhibitor, emailUserMap, allStaffByEvent, isHolderDataPreference);

            dataRowListMap.put(exhibitor.getName(), csvHeaderAndData.getDataRowList());
            dataHeaderMap.put(exhibitor.getName(), csvHeaderAndData.getHeaderList());

            if (exhibitor.getCategoryId() != null && isNotEmpty(exhibitor.getCategoryId().trim())) {
                List<Long> categoryIds = GeneralUtils.convertCommaSeparatedToListLong(exhibitor.getCategoryId());
                StringBuilder categoryName = new StringBuilder();
                for (Long categoryId : categoryIds) {
                    categoryName.append(longStringList.get(categoryId));
                    categoryName.append(STRING_COMMA);
                }
                dataBoothCategory.put(exhibitor.getName(), categoryName.substring(0, categoryName.length() - 1));
            }
        }

        // Download the CSV file
        downloadCSVFileForAllLead(writer, dataHeaderMap, dataRowListMap, dataBoothCategory);
    }

    private CSVHeaderAndData prepareExhibitorLeadsCSV(Long exhibitorId,      //NOSONAR
                                                     Event event,
                                                     Map<Long, ExhibitorSetting> exhibitorSettingsMap,
                                                     Map<Long, List<LeadRetriverData>> leadRetriverMap,
                                                     Map<Long, List<Staff>> staffMap,
                                                     Map<Long, TicketAttributeValueDto1> eventTicketsMap,
                                                     Map<Long, AttendeeProfileDto> attendeeMap,
                                                     Map<Long,String> leadOwnerStaffMap,
                                                     List<Long> staffUserIds,
                                                     List<Object[]> virtualEventLoggingsList,
                                                     Map<Long, Map<Long, AttendeeExpoEngagementDetail>> attendeeExpoEngagementEventDetailMap,
                                                     Map<Long,Map<Long, TicketHolderDetailDto>> ticketHolderDetailDtoMapByExhibitor,
                                                     Map<String, BasicUserDto> emailUserMap, List<Staff> allStaffOfEvent,boolean isHolderDataPreference) {
        logger.info("start prepareExhibitorLeadsCSV for event {} and exhibitorId {}", event.getEventId(), exhibitorId);

        // Retrieve exhibitor setting or use default if not found
        ExhibitorSetting exhibitorSetting = exhibitorSettingsMap.getOrDefault(exhibitorId,exhibitorSettingsMap.get(-1L));

        // Initialize data structures for CSV data
        List<List<String>> dataRowList = new ArrayList<>();
        List<LeadRetriverData> leadRetrieverDatas = leadRetriverMap.getOrDefault(exhibitorId, Collections.emptyList());
        logger.info("Lead retriver data list size {} of exhibitor {}", leadRetrieverDatas.size(), exhibitorId);

        // Group staff data by user ID if available
        Map<Long, List<Staff>> staffMapWithUser = staffMap.containsKey(exhibitorId) ?
                staffMap.get(exhibitorId).stream().collect(Collectors.groupingBy(Staff::getUserId)) : null;

        // Get ticket holder details for the exhibitor
        Map<Long, TicketHolderDetailDto> ticketHolderDetailDtoMap = ticketHolderDetailDtoMapByExhibitor.getOrDefault(exhibitorId, Collections.emptyMap());

        // Prepare header lists for the CSV
        /*  Refer this comment to remove code https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523 */
        List<String> profileQuestionsInSeq = getProfileQuestionsHeaderInSeq(exhibitorSetting);
        List<TicketHolderRequiredAttributes> holderQuestions = exhibitorSettingsService.getHolderQuestions(exhibitorSetting, event, isHolderDataPreference);
        List<String> headerList = new ArrayList<>();
        headerList.addAll(defaultMandatoryHeaders());
        /*  Refer this comment to remove code https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523 */
        headerList.addAll(profileQuestionsInSeq);
        headerList.addAll(addTicketHolderRequiredAttributesToHeader(holderQuestions));
        headerList.addAll(addDefaultHeaders());
        headerList.addAll(addAttendeeEngagementHeaders());

        // Get attendee engagement details and virtual event loggings for the exhibitor
        Map<Long, AttendeeExpoEngagementDetail> attendeeExpoEngagementDetailMap = attendeeExpoEngagementEventDetailMap.getOrDefault(exhibitorId, Collections.emptyMap());
        List<Object[]> virtualEventLoggings = virtualEventLoggingsList.stream().filter(objects -> objects[7].toString().equals(exhibitorId.toString())).collect(Collectors.toList());
        Map<String, Object[]> virtualEventLoggingsMap = virtualEventLoggings.stream().collect(Collectors.toMap(e -> (String) e[3], Function.identity()));

        // Process lead data to get the oldest created lead for each user
        List<LeadRetriverData> oldestCreatedLeadListByUser = new ArrayList<>();
        Map<ExhibitorIdUserEmailDto, Set<String>> exhibitorIdUserIdDtoSourceSetMap = new HashMap<>();
        Map<ExhibitorIdUserEmailDto, List<LeadRetriverData>> exhibitorIdUserIdDtoLeadRetriverDataMap = leadRetrieverDatas.stream()
                .collect(Collectors.groupingBy(lead -> new ExhibitorIdUserEmailDto(lead.getExhibitorId(), ticketHolderDetailDtoMap.get(lead.getId()).getHolderEmail())));

        for (Map.Entry<ExhibitorIdUserEmailDto, List<LeadRetriverData>> entry : exhibitorIdUserIdDtoLeadRetriverDataMap.entrySet()) {
            List<LeadRetriverData> getDataFromMap = entry.getValue();
            Optional<LeadRetriverData> oldestLead = getDataFromMap.stream()
                    .min(Comparator.comparing(LeadRetriverData::getCreatedOn));
            oldestLead.ifPresent(oldestCreatedLeadListByUser::add);
            exhibitorIdUserIdDtoSourceSetMap.put(entry.getKey(), getDataFromMap.stream().map(LeadRetriverData::getSource).collect(Collectors.toSet()));
        }

        // Prepare CSV data rows
        for (LeadRetriverData leadRetriverData : oldestCreatedLeadListByUser) {
            logger.info("prepareExhibitorLeadsCSV for user {} ", leadRetriverData.getUserId());

            boolean isExhibitorStaff = checkIsUserExhibitorStaff(allStaffOfEvent, leadRetriverData.getUserId(), leadRetriverData.getExhibitorId());
            TicketHolderDetailDto ticketHolderData = ticketHolderDetailDtoMap.get(leadRetriverData.getId());

            List<String> dataList = new ArrayList<>();
            dataList.addAll(addDefaultMandatoryData(ticketHolderData));
            AttendeeProfileDto attendeeProfileDto = getAttendeeProfileDto(emailUserMap, attendeeMap, ticketHolderData);
            /*  Refer this comment to remove code https://accelevents.atlassian.net/browse/DEV-33220?focusedCommentId=110523 */
            dataList.addAll(profileQuestionsDataPreparation(profileQuestionsInSeq, attendeeProfileDto, ticketHolderData));
            dataList.addAll(holderQuestionsPreparation(holderQuestions, eventTicketsMap, ticketHolderData, attendeeProfileDto, leadRetriverData, isHolderDataPreference));
            dataList.add(Long.toString(ticketHolderData.getEventTicketId()));
            dataList.add(leadRetriverData.getNotes());
            dataList.add(leadRetriverData.getLeadRating());
            dataList.add(getDateInLocal(leadRetriverData.getCreatedOn(), event.getEquivalentTimeZone(), null));
            dataList.add(getRoleOfUser(staffMapWithUser, leadRetriverData));

            // Determine the lead owner
            String owner = "";
            if(leadRetriverData.getStaffId() != null){
                if(leadRetriverData.getStaffId() == 0){
                    owner = UNASSIGNED;
                }
                else{
                    owner = leadOwnerStaffMap.get(leadRetriverData.getStaffId());
                }
            }
            else{
                owner = UNASSIGNED;
            }
            dataList.add(owner);
            dataList.add(isExhibitorStaff ? "Yes" : "No");
            dataList.add(leadRetriverData.getSource());

            markOtherLeadGenerate(dataList, leadRetriverData.getExhibitorId(), exhibitorIdUserIdDtoSourceSetMap, ticketHolderDetailDtoMap.get(leadRetriverData.getId()).getHolderEmail());
            addAttendeeEngagementData(attendeeExpoEngagementDetailMap, leadRetriverData, dataList, virtualEventLoggingsMap, ticketHolderData, isExhibitorStaff);
            dataRowList.add(dataList);
        }
        logger.info("data list preparing is finished for exhibitorId {}", exhibitorId);

        return new CSVHeaderAndData(headerList, dataRowList);
    }

    private String getRoleOfUser(Map<Long, List<Staff>> staffMapWithUser, LeadRetriverData leadRetriverData) {
        String role;
        List<Staff> listStaff = null != staffMapWithUser && !staffMapWithUser.isEmpty() ? staffMapWithUser.get(leadRetriverData.getUserId()) : Collections.emptyList();
        if(CollectionUtils.isNotEmpty( listStaff )){
            if(StaffRole.admin.equals(listStaff.get(0).getRole())){
                role = ORGANIZER;
            }else {
                if (leadRetriverData.getExhibitorId().equals(listStaff.get(0).getExhibitorId())) {
                    role = EXHIBITOR;
                } else {
                    role = ATTENDEE;
                }
            }
        }else {
            role = ATTENDEE;
        }
        return role;
    }

    @Override
    public void downloadRegistrantsCSV(PrintWriter writer, Session session) {

        Event event = roEventService.getEventById(session.getEventId());
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
                .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);

        List<String> headerList = new ArrayList<>();

        // Set up For Holder Questions
        List<TicketHolderRequiredAttributes> listOfAttributesHavingOnlyEnableForTicketHolder = Collections.emptyList();
        if (!holderRequiredAttributes.isEmpty()) {
            listOfAttributesHavingOnlyEnableForTicketHolder = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
        }

        List<TicketHolderRequiredAttributes> finalListOfAttributesHavingOnlyEnableForTicketHolder = listOfAttributesHavingOnlyEnableForTicketHolder;

        headerList.add(REGISTRATION_DATE);
        headerList.add(SESSION_CHECK_IN_DATE);

        List<List<String>> dataRowList = new ArrayList<>();

        PollsQuestionsAnswers graphQLHandler = new PollsQuestionsAnswers(graphQLConfiguration, BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
        PollsResultWrapper wrapper = graphQLHandler.getPollsResult(session.getId(), EnumQNAType.SESSION.name());

        Map<String,String> questions = wrapper.getQuestionMap();
        headerList.addAll(questions.keySet());

        List<EventTickets> eventTickets = eventCommonRepoService.findByEventIdJoinFetch(event, 0L, false, DataType.TICKET, null, null);
        Map<Long, List<EventTickets>> holderUserEventTickets = eventTickets.stream().collect(Collectors.groupingBy(eventTicket -> eventTicket.getHolderUserId().getUserId()));

        Map<Integer, List<PollsResultDto>> userAnswersMap = wrapper.getUserAnswersMap();
        List<UserSession> userSessions=userSessionService.getAllUserSessionBySessionId(session.getId());
        for (UserSession userSession : userSessions){
            List<String> dataList = new ArrayList<>();
            User user = userSession.getUser();
            List<EventTickets> eventTicketsList = holderUserEventTickets.get(userSession.getUserId());
            // Handle Required Questions Answers
            if (CollectionUtils.isNotEmpty(eventTicketsList)) {
                prepareTicketHolderRequiredAttributesDataList(finalListOfAttributesHavingOnlyEnableForTicketHolder, dataList,
                        eventTicketsList);
            } else {
                prepareUserDataListForWithoutTicket(finalListOfAttributesHavingOnlyEnableForTicketHolder, dataList, user);
            }
            dataList.add(userSession.getRegistrationDate() != null ? dateFormatter.format(getDateInLocal(userSession.getRegistrationDate(),event.getEquivalentTimeZone())) : STRING_EMPTY);
            dataList.add(userSession.getCheckInTime() != null ? dateFormatter.format(getDateInLocal(userSession.getCheckInTime(),event.getEquivalentTimeZone())) : STRING_EMPTY);

            List<PollsResultDto> list = userAnswersMap.get(user.getUserId().intValue());
            int index = dataList.size();
            savePollResultAnswers(headerList, dataList, list, index);

            dataRowList.add(dataList);
        }

        questions.keySet().forEach(e->{
            int index = headerList.indexOf(e);
            headerList.set(index, questions.get(e));
        });

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadSessionCheckInLogCSV(PrintWriter writer, Session session) {

        Event event = roEventService.getEventById(session.getEventId());
        List<String> headerList = new ArrayList<>();
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(EMAIL);
        headerList.add(EVENT_TICKET_ID);
        headerList.add(CHECKIN_CHECKOUT_DATE);
        headerList.add(CHECKIN_CHECKOUT_STATUS);
        headerList.add(CHECKIN_CHECKOUT_BY_USER);

        List<List<String>> dataRowList = new ArrayList<>();

        List<SessionCheckInLog> sessionCheckInLogs=sessionCheckInLogRepoService.findAllSessionCheckInLogBySessionIdOrderByAuditTimeAsc(session.getId());
        for (SessionCheckInLog sessionCheckInLog : sessionCheckInLogs){
            List<String> dataList = new ArrayList<>();

            User user = sessionCheckInLog.getUser();
            dataList.add(user.getFirstName());
            dataList.add(user.getLastName());
            dataList.add(user.getEmail());
            dataList.add(sessionCheckInLog.getEventTicketId() != null ? String.valueOf(sessionCheckInLog.getEventTicketId()) :  STRING_EMPTY);
            dataList.add(sessionCheckInLog.getAuditTime() != null ? dateFormatter.format(getDateInLocal(sessionCheckInLog.getAuditTime(),event.getEquivalentTimeZone())) : STRING_EMPTY);
            dataList.add(sessionCheckInLog.getAuditStatus().name());
            dataList.add(sessionCheckInLog.getAuditByUser() != null ? sessionCheckInLog.getAuditByUser().getEmail() : STRING_EMPTY);

            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void savePollResultAnswers(List<String> headerList, List<String> dataList, List<PollsResultDto> list, int index) {
        while (index < headerList.size()){
            String questionId = headerList.get(index);
            if(list !=null){
                List<String> answers = list.stream()
                        .filter(e->e.getQuestionId().equals(questionId))
                        .map(PollsResultDto::getAnswers)
                        .collect(Collectors.toList());

                dataList.add(CollectionUtils.isEmpty(list) ? "":String.join(",", answers));

            } else {
                dataList.add("");
            }
            index++;
        }
    }

    private void prepareUserDataListForWithoutTicket(List<TicketHolderRequiredAttributes> finalListOfAttributesHavingOnlyEnableForTicketHolder, List<String> dataList, User user) {
        for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : finalListOfAttributesHavingOnlyEnableForTicketHolder) {
            if (STRING_FIRST_SPACE_NAME.equals(ticketHolderRequiredAttributes.getName())) {
                dataList.add(user.getFirstName());
            } else if (STRING_LAST_SPACE_NAME.equals(ticketHolderRequiredAttributes.getName())) {
                dataList.add(user.getLastName());
            } else if (STRING_EMAIL_SPACE.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                dataList.add(user.getEmail());
            } else if (AttributeValueType.BILLING_ADDRESS.equals(ticketHolderRequiredAttributes.getAttributeValueType()) ||
                    AttributeValueType.SHIPPING_ADDRESS.equals(ticketHolderRequiredAttributes.getAttributeValueType())) {
                String addressValue = ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR;
                String[] values = addressValue.split("\\" + ADD_SEPARATOR, -1);
                dataList.addAll(Arrays.asList(values));
            } else {
                dataList.add("");
            }
        }
    }

    private void prepareTicketHolderRequiredAttributesDataList(List<TicketHolderRequiredAttributes> finalListOfAttributesHavingOnlyEnableForTicketHolder,
                                                               List<String> dataList,
                                                               List<EventTickets> eventTicketsList) {
        // Handle Required Questions Answers
        if (CollectionUtils.isNotEmpty(eventTicketsList)) {
            EventTickets eventTicket = eventTicketsList.get(0);
            TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
            for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : finalListOfAttributesHavingOnlyEnableForTicketHolder) {
                    TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                    prepareDataList(eventTicket, ticketAttributeValueDto, dataList, ticketHolderRequiredAttributes, ticketBuyerUploadsUrl, true, false, null, false,false);
            }
        }
        else  {
            for (TicketHolderRequiredAttributes ignored : finalListOfAttributesHavingOnlyEnableForTicketHolder) {
                   dataList.add("");
            }
        }
    }

    @Override
    public void downloadChatCSV(PrintWriter writer, String channelId, String channelName, String channelType,Event event) {
        List<String> headerList = new ArrayList<>();

        headerList.add(HEADER.USER_NAME);
        headerList.add(HEADER.MESSAGE);
        headerList.add(HEADER.TIMESTAMP);
        headerList.add(HEADER.THREAD_MESSAGES);

        List<List<String>> dataRowList = new ArrayList<>();

        List<ChatMessageDTO> messages = getStreamService.getChannelMessages(channelId, channelName, channelType,event);

        for (ChatMessageDTO message : messages){
            List<String> dataList = new ArrayList<>();
            dataList.add(message.getUserName());
            dataList.add(message.getMessage());
            dataList.add(message.getTimestamp());
            dataList.add(null != message.getThreads() && !message.getThreads().isEmpty() ? convertListToCommaSeparated(message.getThreads()) : STRING_EMPTY);
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

	@Override
	public void downloadQuestionAnswers(PrintWriter writer, long typeId, EnumQNAType type, Event event, boolean requiredHolderData) {
        List<String> headerList = new ArrayList<>();
        headerList.add(HASH);
        headerList.add(QUESTION);
        headerList.add(ASKER_NAME);
        headerList.add(ANSWER_S);
        headerList.add(ANSWER_TYPE);
        headerList.add(QUESTION_STATUS);

        List<List<String>> dataRowList = new ArrayList<>();

        QuestionsAnswers graphQLHandler = new QuestionsAnswers(graphQLConfiguration, BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
        try {
            JSONArray jsonArray = graphQLHandler.getAllQuestionsAnswers(typeId, type.name());

            if (null != jsonArray){

                Set<Long> answeredBy = IntStream.range(0, jsonArray.length()).mapToObj(i -> {
                    try {
                        return jsonArray.getJSONObject(i);
                    } catch (JSONException e) {
                        logger.info("Excption while gettinguserId of a answered by");
                    }
                    return null;
                }).map(e -> {
                    try {
                        return e.getLong("userId");
                    } catch (JSONException ex) {
                        logger.info("Excption while gettinguserId of a answered by");
                    }
                    return null;
                }).collect(Collectors.toSet());

                List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
                        .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);

                List<TicketHolderRequiredAttributes> listOfAttributesHavingOnlyEnableForTicketHolder = Collections.emptyList();
                if (!holderRequiredAttributes.isEmpty() && requiredHolderData) {
                    listOfAttributesHavingOnlyEnableForTicketHolder = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
                }

                List<EventTickets> eventTickets = eventCommonRepoService.findByEventIdJoinFetch(event, 0L, false, DataType.TICKET, null, null).stream().filter(e-> !TicketStatus.DELETED.equals(e.getTicketStatus())).collect(Collectors.toList());
                Map<Long, EventTickets> holderUserEventTickets = eventTickets.stream().collect(Collectors.toMap(e -> e.getHolderUserId().getUserId(), ticket -> ticket, (firstTicket, secondTicket) -> firstTicket));

                Map<Long, User> userMap = userService.findAttendeesByEventAndUserIds(event.getEventId(), new ArrayList<>(answeredBy)).stream().collect(Collectors.toMap(User::getUserId, Function.identity(), (firstUser, secondUser) -> firstUser));


                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = (JSONObject) jsonArray.get(i);
                    List<String> dataList = new ArrayList<>();
                    dataList.add(String.valueOf(i+1));
                    dataList.add(removeNull(jsonObject, QUESTION_TEXT));
                    dataList.add(removeNull(jsonObject, USER_NAME));
                    dataList.add(removeNull(jsonObject, ANSWER));
                    dataList.add(removeNull(jsonObject, QUESTION_ANSWER_TYPE));
                    dataList.add(removeNull(jsonObject, STATUS));

                    Long userId = 0L;
                    if(!jsonObject.isNull("userId")){
                        userId =  jsonObject.getLong("userId");
                    }
                    if(requiredHolderData){
                        for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : listOfAttributesHavingOnlyEnableForTicketHolder) {
                            EventTickets eventTicket = holderUserEventTickets.get(userId);
                            if(eventTicket != null) {
                                TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                                TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                                prepareDataList(eventTicket, ticketAttributeValueDto, dataList, ticketHolderRequiredAttributes, ticketBuyerUploadsUrl, true, false, null, false,false);
                            } else {
                                User user = userMap.get(userId);
                                if(user != null){
                                    if(STRING_FIRST_SPACE_NAME.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())){
                                        dataList.add(user.getFirstName());
                                    } else if (STRING_LAST_SPACE_NAME.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                        dataList.add(user.getLastName());
                                    } else if (STRING_EMAIL_SPACE.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                        dataList.add(user.getEmail());
                                    } else {
                                        dataList.add(STRING_EMPTY);
                                    }
                                }
                                else {
                                    dataList.add(STRING_EMPTY);
                                }
                            }
                        }
                    }
                    dataRowList.add(dataList);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
	}

    @Override
    public void downloadPolls(PrintWriter writer, long typeId, EnumQNAType type, Event event, Boolean requiredHolderData) { //NOSONAR

        List<String> headerList = new ArrayList<>();
        headerList.add(HASH);
        headerList.add(QUESTION);
        headerList.add(QUESTION_TYPE);
        headerList.add(CHOICES);
        headerList.add(QUESTION_STATUS);
        headerList.add(ANSWER_S);
        headerList.add(ANSWER_BY);

        List<List<String>> dataRowList = new ArrayList<>();
        PollsQuestionsAnswers graphQLHandler = new PollsQuestionsAnswers(graphQLConfiguration, BACKEND_SYSTEM_INTEGRATION_APPSYNC_AUTH_TOKEN);
        try {
            Map<String, PollQuesDto> pollQuesMap = graphQLHandler.getAllPollQuestionsByTypeAndTypeId(typeId, type.name());
            if (pollQuesMap.size() > 0) {
                List<String> questionIdList = new ArrayList<>();
                Map<Long, List<PollAnsDto>> pollAnsMap = graphQLHandler.getAllPollResultByTypeAndTypeId(typeId, type.name(), new ArrayList<>(pollQuesMap.keySet()));
                int count = 1;

                List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
                        .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);

                List<TicketHolderRequiredAttributes> listOfAttributesHavingOnlyEnableForTicketHolder = Collections.emptyList();
                if (!holderRequiredAttributes.isEmpty() && requiredHolderData) {
                    listOfAttributesHavingOnlyEnableForTicketHolder = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
                }

                List<EventTickets> eventTickets = eventCommonRepoService.findByEventIdJoinFetch(event, 0L, false, DataType.TICKET, null, null).stream().filter(e-> !TicketStatus.DELETED.equals(e.getTicketStatus())).collect(Collectors.toList());
                Map<Long, EventTickets> holderUserEventTickets = eventTickets.stream().collect(Collectors.toMap(e -> e.getHolderUserId().getUserId(), ticket -> ticket, (firstTicket, secondTicket) -> firstTicket));

                Map<Long, User> userMap = userService.findAttendeesByEventAndUserIds(event.getEventId(), new ArrayList<>(pollAnsMap.keySet())).stream().collect(Collectors.toMap(User::getUserId, Function.identity(), (firstUser, secondUser) -> firstUser));

                // For Poll that is ENDED
                if (pollAnsMap.size() > 0) {
                    List<Long> userIds = new ArrayList<>(pollAnsMap.keySet());
                    Map<Long, String> userIdToNameMap = userService.findUserNameByUserIdIn(userIds).stream().collect(Collectors.toMap(o -> (Long) o[0], o -> String.valueOf(o[1])));
                    for (int i = 0; i < userIds.size(); i++) {
                        Long userId = userIds.get(i);
                        List<PollAnsDto> pollAns = pollAnsMap.get(userIds.get(i));
                        if (null != pollAns && !pollAns.isEmpty()) {
                            for (int j = 0; j < pollAns.size(); j++) {
                                PollAnsDto pollAnsDto = pollAns.get(j);
                                PollQuesDto pollQuesDto = pollQuesMap.get(pollAnsDto.getQuestionId());
                                if (null != pollQuesDto) {
                                    List<String> dataList = new ArrayList<>();
                                    dataList.add(String.valueOf(count));
                                    dataList.add(pollQuesDto.getQuestion());
                                    dataList.add(pollQuesDto.getQuestionType());
                                    dataList.add(pollQuesDto.getChoices());
                                    dataList.add(pollQuesDto.getStatus());
                                    dataList.add(pollAnsDto.getAnswer());
                                    dataList.add(userIdToNameMap.get(userId).equals("null") ? "" : userIdToNameMap.get(userId));
                                    dataRowList.add(dataList);
                                    count = count + 1;
                                    questionIdList.add(pollAnsDto.getQuestionId());

                                    if(requiredHolderData){
                                        for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : listOfAttributesHavingOnlyEnableForTicketHolder) {
                                            EventTickets eventTicket = holderUserEventTickets.get(userId);
                                            if(eventTicket != null) {
                                                TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                                                TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                                                prepareDataList(eventTicket, ticketAttributeValueDto, dataList, ticketHolderRequiredAttributes, ticketBuyerUploadsUrl, true, false, null, false,false);
                                            } else {
                                                User user = userMap.get(userId);
                                                if(user != null){
                                                    if(STRING_FIRST_SPACE_NAME.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())){
                                                        dataList.add(user.getFirstName());
                                                    } else if (STRING_LAST_SPACE_NAME.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                                        dataList.add(user.getLastName());
                                                    } else if (STRING_EMAIL_SPACE.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                                        dataList.add(user.getEmail());
                                                    } else {
                                                        dataList.add(STRING_EMPTY);
                                                    }
                                                }
                                                else {
                                                    dataList.add(STRING_EMPTY);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // For Poll that is Not ENDED
                for (Map.Entry<String, PollQuesDto> pollQues : pollQuesMap.entrySet()) {
                    if (!questionIdList.contains(pollQues.getKey())) {
                        PollQuesDto pollQuesDto = pollQues.getValue();
                        if (null != pollQuesDto) {
                            List<String> dataList = new ArrayList<>();
                            dataList.add(String.valueOf(count));
                            dataList.add(pollQuesDto.getQuestion());
                            dataList.add(pollQuesDto.getQuestionType());
                            dataList.add(pollQuesDto.getChoices());
                            dataList.add(pollQuesDto.getStatus());
                            dataList.add("");
                            dataList.add("");
                            dataRowList.add(dataList);
                            count = count + 1;
                        }
                    }
                }
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadExhibitor(PrintWriter writer, Long exhibitorId, Event event,User user) { //NOSONAR
        Staff staff = staffService.getExhibitorDetail(user,event,exhibitorId);
        ExhibitorSetting deafultExhibitorSetting = prepareExhibitorSettingsWithDefaultData(event);
        Ticketing ticketing = ticketingHelperService.findTicketingByEventIdOrThrowError(event);
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllAttendeeProfiles(event.getEventId());
        Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(), (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));
        List<Staff> allStaffByEvent = staffService.findAllByEventId(event.getEventId());
        ExhibitorSetting exhibitorSetting = repoService.getExhibitorRecord(exhibitorId);
        if (exhibitorSetting == null) {
            exhibitorSetting = repoService.getGlobalConfigRecordByEventId(event.getEventId());
        }
        Map<Long, ExhibitorSetting> settingsMap = new HashMap<>();
        settingsMap.put(exhibitorId, exhibitorSetting);
        this.downloadLeadsCSV(writer, exhibitorId, staff, event, deafultExhibitorSetting, ticketing, attendeeMap, allStaffByEvent, settingsMap);
    }

    @Override
    public void downloadSessionAttendeesCSV(PrintWriter writer, Session session) {
        List<RegisterdHolderUsers> sessionAttendees = userSessionService.findRegisteredUserInfo(session.getId());

        List<String> headerList = new ArrayList<>();
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(EMAIL);
        headerList.add("Attended");

        List<List<String>> dataRowList = new ArrayList<>();
        sessionAttendees.forEach(e->{
            List<String> dataList = new ArrayList<>();
            dataList.add(e.getFirstName());
            dataList.add(e.getLastName());
            dataList.add(e.getEmail());
            dataList.add(e.isAttended()?"YES":"NO");
            dataRowList.add(dataList);
        });
        this.downloadCSVFile(writer, headerList, dataRowList);

    }

    @Override
	public void downloadAttendeeSessions(PrintWriter writer, List<AttendeeSession> attendeeSessions,Long userId) {
        List<String> headerList = new ArrayList<>();
        headerList.add(SESSION_TITLE);
        headerList.add("Start Time");
        headerList.add("End Time");
        headerList.add("Status");
        headerList.add("Engagement");
        headerList.add(DOCUMENT);
        headerList.add(TOTAL_DOCUMENT_DOWNLOADED);
        headerList.add(UNIQUE_DOWNLOADS);
        headerList.add(DOCUMENT_DOWNLOADED_LIST);

        List<List<String>> dataRowList = new ArrayList<>();
        attendeeSessions.forEach(e->{
            List<String> dataList = new ArrayList<>();
            dataList.add(e.getTitle());
            dataList.add(e.getStartTime());
            dataList.add(e.getEndTime());
            dataList.add(e.getStatus());
            dataList.add(e.getEngagement());
            Long docDocument = virtualEventSessionLoggingService.isDocumentDownloadedByUserOrNot(userId, e.getSessionId());
            dataList.add(docDocument>0?"YES":"NO");
            dataList.add(virtualEventSessionLoggingService.getUserTotalDocumentDownloadByUserId(userId, e.getSessionId()).toString());
            dataList.add(virtualEventSessionLoggingService.uniqueDocumentDownloadsByUserId(userId, e.getSessionId()).toString());
            dataList.add(virtualEventSessionLoggingService.docListDownloadedByUserId(userId, e.getSessionId()));
            dataRowList.add(dataList);
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadLeaderboardDetailDataCSV(PrintWriter writer, Event event) {

        Boolean isNewGamificationEnabled = virtualEventService.isNewGamificationEnabled(event.getEventId());
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.COMPANY);
        headerList.add(HEADER.TITLE);

        headerList.add(HEADER.TOTAL_POINT_EARNED);
        headerList.add(CSVHeaders.VISITING_EXHIBITOR_BOOTH_POINTS);
        headerList.add(CSVHeaders.RE_VISITING_EXHIBITOR_BOOTH_POINTS);
        headerList.add(CSVHeaders.DOWNLOAD_DOCUMENT_POINTS);
        headerList.add(CSVHeaders.EXHIBITOR_BOOTH_CHAT);

        if(Boolean.TRUE.equals(isNewGamificationEnabled)) {
            headerList.add(CSVHeaders.WATCHING_EXHIBITOR_VIDEO_POINTS);
            headerList.add(CSVHeaders.EXHIBITOR_LINK_CLICK_POINTS);
            headerList.add(CSVHeaders.EXHIBITOR_REQUEST_MEETING_POINTS);

            headerList.add(CSVHeaders.VISITING_SESSION_POINTS);
            headerList.add(CSVHeaders.RE_VISITING_SESSION_POINTS);
            headerList.add(CSVHeaders.WATCHING_SESSION_LIVE_UPLOADED_STREAM_POINTS);
            headerList.add(CSVHeaders.WATCHING_SESSION_RECORDING_VIDEO_POINTS);
            headerList.add(CSVHeaders.SESSION_CHAT_POINTS);
            headerList.add(CSVHeaders.SESSION_ASK_QUESTION_POINTS);
            headerList.add(CSVHeaders.SESSION_POLL_POINTS);

            headerList.add(CSVHeaders.NETWORKING_MATCH_POINTS);
            headerList.add(CSVHeaders.NETWORKING_CONNECTION_POITNS);
            headerList.add(CSVHeaders.POINTS_EARNED_VIA_TICKET_PURCHASE);
            headerList.add(CSVHeaders.POINTS_EARNED_VIA_EVENT_CHECK_IN);
            headerList.add(CSVHeaders.POINTS_EARNED_VIA_QR_SCAN);

            headerList.add(CSVHeaders.POINTS_EARNED_VIA_CONNECTIONS);
            headerList.add(CSVHeaders.POINTS_EARNED_VIA_PROFILE_COMPLETION);
            headerList.add(CSVHeaders.POINTS_EARNED_VIA_SURVEY_SUBMISSSION);
        }

        List<List<String>> dataRowList = new ArrayList<>();

        try {
            List<LeaderboardDetailed> leaderboardDetailedList ;
            if(Boolean.TRUE.equals(isNewGamificationEnabled)) {
                leaderboardDetailedList = leaderboardService.getLeaderboardScoreDetails(event);
            } else {
                leaderboardDetailedList = leaderboardService.findLeaderboardScoreDetailsForCsv(event);
            }

            leaderboardDetailedList.forEach(leaderboardDetailed -> {
                List<String> dataList = new ArrayList<>();
                dataList.add(leaderboardDetailed.getFirstName());
                dataList.add(leaderboardDetailed.getLastName());
                dataList.add(leaderboardDetailed.getEmail());
                dataList.add(leaderboardDetailed.getCompany());
                dataList.add(leaderboardDetailed.getTitle());

                dataList.add(String.valueOf(leaderboardDetailed.getPoint()));
                dataList.add(String.valueOf(leaderboardDetailed.getTotalPointsVisitingBooth()));
                dataList.add(String.valueOf(leaderboardDetailed.getPointsRevisitingBooth()));
                dataList.add(String.valueOf(leaderboardDetailed.getPointsDownloadingDocuments()));
                dataList.add(String.valueOf(leaderboardDetailed.getPointsChat()));

                if(Boolean.TRUE.equals(isNewGamificationEnabled)) {
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsVideoWatch()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsLinkClick()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsRequestMeeting()));

                    dataList.add(String.valueOf(leaderboardDetailed.getPointsVisitingSession()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsRevisitingSession()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsVideoWatchSession()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsRecordingVideoWatchSession()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsSessionChat()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsSessionAskQuestion()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsSessionPoll()));

                    dataList.add(String.valueOf(leaderboardDetailed.getPointsNetworkingMatch()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsNetworkingConnection()));

                    dataList.add(String.valueOf(leaderboardDetailed.getPointsTicketPurchase()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsEventCheckIn()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsQRScan()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsConnection()));
                    dataList.add(String.valueOf(leaderboardDetailed.getPointsProfileCompletion()));

                    dataList.add(String.valueOf(leaderboardDetailed.getPointsSurveySubmission()));
                }
                dataRowList.add(dataList);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private String removeNull(JSONObject jsonObject, String questionText) throws JSONException {
        if(jsonObject.get(questionText)!=JSONObject.NULL){
            return jsonObject.getString(questionText);
        } else {
            return null;
        }
    }

    public void setGraphQLConfiguration(GraphQLConfiguration graphQLConfiguration) {
        this.graphQLConfiguration = graphQLConfiguration;
    }

    @Override
    public void downloadMobileAnalyticsReport(PrintWriter writer, Event event) {

        List<List<String>> dataRowList = new ArrayList<>();

        List<String> headerList = new ArrayList<>();

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
                .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);

        List<TicketHolderRequiredAttributes> listOfAttributesHavingOnlyEnableForTicketHolder = Collections.emptyList();
        if (!holderRequiredAttributes.isEmpty()) {
            listOfAttributesHavingOnlyEnableForTicketHolder = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
        }

        headerList.add("First Login");
        headerList.add("Last Seen");
        headerList.add("App Opened");

        List<EventTickets> eventTickets = eventCommonRepoService.findByEventIdJoinFetch(event, 0L, false, DataType.TICKET, null, null);
        Map<Long, EventTickets> holderUserEventTickets = eventTickets.stream().collect(Collectors.toMap(e -> e.getHolderUserId().getUserId(), ticket -> ticket, (firstTicket, secondTicket) -> firstTicket));

        List<MobileAnalyticsDataDto> analyticsDataDtos = mobileAnalyticsService.getMobileAnalyticsReportDataPoint(event);
        for(MobileAnalyticsDataDto analyticsDataDto: analyticsDataDtos){
            List<String> dataList = new ArrayList<>();
            for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : listOfAttributesHavingOnlyEnableForTicketHolder) {
                EventTickets eventTicket = holderUserEventTickets.get(analyticsDataDto.getUserId());
                if(eventTicket != null) {
                    TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                    TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                    prepareDataList(eventTicket, ticketAttributeValueDto, dataList, ticketHolderRequiredAttributes, ticketBuyerUploadsUrl, true, false, null, false,false);
                } else {
                    if(STRING_FIRST_SPACE_NAME.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())){
                        dataList.add(analyticsDataDto.getFirstName());
                    } else if (STRING_LAST_SPACE_NAME.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                        dataList.add(analyticsDataDto.getLastName());
                    } else if (STRING_EMAIL_SPACE.equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                        dataList.add(analyticsDataDto.getEmail());
                    } else {
                        dataList.add(STRING_EMPTY);
                    }
                }
            }
            dataList.add(analyticsDataDto.getFirstLogin());
            dataList.add(analyticsDataDto.getLastLogin());
            dataList.add(String.valueOf(analyticsDataDto.getAppOpenCount()));
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    @Async
    public void downloadAllAttendeeSessionsInfoCSVAsync(Event event, User user) {
        Gson gson = new Gson();
        DownloadCSVRequestDto downloadCSVRequestDto = new DownloadCSVRequestDto(CSV_DOWN_STRING, "SESSION_ATTENDEE", event.getEventId(), user.getUserId());
        InvokeRequest invokeRequest = new InvokeRequest()
                .withFunctionName(downloadCsvAwsLambdaName)
                .withInvocationType(InvocationType.Event)
                .withPayload(gson.toJson(downloadCSVRequestDto));

        awsLambdaAsyncClient.invokeAsync(invokeRequest);
    }

    @Override
    public void downloadAllAttendeeSessionsInfoCSV(PrintWriter writer, Event event) {

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
                .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);

        List<String> headerList = new ArrayList<>();

        // Set up For Holder Questions
        List<TicketHolderRequiredAttributes> listOfAttributesHavingOnlyEnableForTicketHolder = Collections.emptyList();
        if (!holderRequiredAttributes.isEmpty()) {
            listOfAttributesHavingOnlyEnableForTicketHolder = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
        }


        List<EventTickets> eventTickets = eventCommonRepoService.findByEventIdJoinFetch(event, 0L, false, DataType.TICKET, null,null);
        Map<Long, List<EventTickets>> holderUserEventTickets =  eventTickets.stream().collect(Collectors.groupingBy(eventTicket -> eventTicket.getHolderUserId().getUserId()));
        List<TicketHolderRequiredAttributes> finalListOfAttributesHavingOnlyEnableForTicketHolder = listOfAttributesHavingOnlyEnableForTicketHolder;

        List<String> sessionTitle = sessionService.findSessionTitleByEventIdOrderByIdAsc(event.getEventId());

        if (CollectionUtils.isNotEmpty(sessionTitle)){
            headerList.addAll(sessionTitle);
        }

        Map<Long, List<Map<Long,String>>> savedAttendeeDetail = new HashMap<>();
        List<AttendeeAnalyticsDTO> attendeeSessionDetail = userSessionService.findAllAttendeeSessionsByEventId(event.getEventId(),savedAttendeeDetail);
        List<List<String>> dataRowList = new ArrayList<>();

        attendeeSessionDetail.forEach(attendeeSession->{
            List<String> dataList = new ArrayList<>();

            // Handle Required Questions Answers
            prepareTicketHolderRequiredAttributesDataList(finalListOfAttributesHavingOnlyEnableForTicketHolder, dataList,
                    holderUserEventTickets.get(attendeeSession.getUserId()) );

            // Handle List Of sessions
            if(MapUtils.isNotEmpty((savedAttendeeDetail))){
                List<Map<Long,String>> listSession = savedAttendeeDetail.get(attendeeSession.getUserId());
                listSession.forEach(attendeeMap-> dataList.addAll(attendeeMap.values()));
            }
            dataRowList.add(dataList);
        });

        this.downloadCSVFile(writer, headerList, dataRowList);
    }



    class CSVHeaderAndData {
        List<String> headerList;
        List<List<String>> dataRowList;

        CSVHeaderAndData(){};

        CSVHeaderAndData(List<String> headerList,
                         List<List<String>> dataRowList){
            this.headerList = headerList;
            this.dataRowList = dataRowList;
        }

        public List<String> getHeaderList(){
            return headerList;
        }
        List<List<String>> getDataRowList(){
            return dataRowList;
        }
    }

    @Override
    public void downloadMatchesUsersCSVForAllMeetUpSession(PrintWriter writer, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add(SESSION_TITLE);
        headerList.add("Title");
        headerList.add("Company");
        headerList.add("Match User Title");
        headerList.add("Match User Company");
        headerList.add("Name");
        headerList.add("Match User Name");
        headerList.add("Match date/time");

        List<List<String>> dataRowList = new ArrayList<>();
        List<NetworkingMatches> networkingMatches = networkingMatchesService.findByEventId(event.getEventId());
        if(!networkingMatches.isEmpty()) {
            List<Long> userList = networkingMatches.stream().map(NetworkingMatches::getUserId).collect(Collectors.toList());
            userList.addAll(networkingMatches.stream().map(NetworkingMatches::getUserIdOfMatch).collect(Collectors.toList()));
            List<User> users = roUserService.getListOfUsersByUserIds(userList);
            Map<Long, List<User>> userListMap = users.stream().collect(Collectors.groupingBy(User::getUserId));

            List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllAttendeeProfiles(event.getEventId());
            Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                    .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(), (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));//NOSONAR

            List<Long> sessionId = networkingMatches.stream().map(NetworkingMatches::getSessionId).collect(Collectors.toList());
            List<SessionIdTitleDto> sessionIdAndTitleList = sessionService.getSessionIdAndTitleById(sessionId);
            Map<Long, String> sessionIdAndTitleListMap = sessionIdAndTitleList.stream().collect(Collectors.toMap(SessionIdTitleDto::getId, SessionIdTitleDto::getTitle));

            networkingMatches.forEach(networkingMatche -> {
                List<String> dataList = new ArrayList<>();
                User user = userListMap.get(networkingMatche.getUserId()).get(0);
                User matchUser = userListMap.get(networkingMatche.getUserIdOfMatch()).get(0);
                AttendeeProfileDto attendeeProfileDto = attendeeMap.get(networkingMatche.getUserId());
                AttendeeProfileDto matchAttendeeProfileDto = attendeeMap.get(networkingMatche.getUserIdOfMatch());

                dataList.add(sessionIdAndTitleListMap.get(networkingMatche.getSessionId()));
                dataList.add(attendeeProfileDto != null ? attendeeProfileDto.getTitle() : STRING_EMPTY);
                dataList.add(attendeeProfileDto != null ? attendeeProfileDto.getCompany() : STRING_EMPTY);
                dataList.add(matchAttendeeProfileDto != null ? matchAttendeeProfileDto.getTitle() : STRING_EMPTY);
                dataList.add(matchAttendeeProfileDto != null ? matchAttendeeProfileDto.getCompany() : STRING_EMPTY);
                dataList.add(user.getFirstName() + STRING_BLANK + user.getLastName());
                dataList.add(matchUser.getFirstName() + STRING_BLANK + matchUser.getLastName());
                dataList.add(dateFormatter.format(getDateInLocal(networkingMatche.getMatchTimeDate(), event.getEquivalentTimeZone())));

                dataRowList.add(dataList);
            });
            this.downloadCSVFile(writer, headerList, dataRowList);
        }
    }

    @Override
    public void downloadSessionListCSV(PrintWriter writer, Event event) {//NOSONAR

        List<String> headerList = new ArrayList<>();
        headerList.add(ID_CAPITAL);
        headerList.add(TITLE);
        headerList.add(FORMAT);
        headerList.add(SESSION_TYPE);
        headerList.add(LOCATION);
        headerList.add(START_DATE_TIME);
        headerList.add(END_DATE_TIME);
        headerList.add(FULL_DETAIL);
        headerList.add(CAPACITY);
        headerList.add(SPEAKER_LIST);
        headerList.add(TAG);
        headerList.add(TRACKS);
        headerList.add(SPONSORING_COMPANY);
        headerList.add(STREAM_PROVIDER);
        headerList.add(ACCELEVENTS_STUDIO_LINK);
        headerList.add(ACCEL_STREAMING_KEY_RTMP);
        headerList.add(RTMP_URL);
        headerList.add(LIVE_STRAMING_PAGE_URL_RTMP);
        headerList.add(ZOOM_MEETING_ID);
        headerList.add(ZOOM_PASSWORD);
        headerList.add(YOUTUBE_LINK);
        headerList.add(VIMEO_LINK);
        headerList.add(WISTIA_LINK);
        headerList.add(FACEBOOK_LIVE_LINK);
        headerList.add(VIDYARD_LINK);

        List<List<String>> dataRowList = new ArrayList<>();

        List<Session> allSessionList = sessionService.findSessionByEventId(event);
        List<KeyValue> keyValue = keyValueRepoService.findByEventId(event);
        List<Long> sessionIds = allSessionList.stream().map(Session::getId).collect(Collectors.toList());
        List<SessionTagAndTrack> sessionTagAndTrackList = sessionIds.isEmpty() ? Collections.emptyList() : sessionTagAndTrackService.findBySessionIds(sessionIds);
        for(Session session : allSessionList){
            List<String> dataList = new ArrayList<>();
            dataList.add(String.valueOf(session.getId()));
            dataList.add(session.getTitle());

            //Change BREAKOUT_SESSION to REGULAR_SESSION and MAIN_STAGE to MAIN_STAGE_SESSION
            if (isNotBlank(session.getFormat().name())) {
                if (session.getFormat().equals(EnumSessionFormat.BREAKOUT_SESSION)) {
                    dataList.add(REGULAR_SESSION_ENUM);
                } else if (session.getFormat().equals(EnumSessionFormat.MAIN_STAGE)) {
                    dataList.add(MAIN_STAGE_SESSION_ENUM);
                } else {
                    dataList.add(session.getFormat().name());
                }
            }
            if (session.getSessionTypeFormat()!=null) {
                   dataList.add(session.getSessionTypeFormat().name());
            }else {
                dataList.add(SessionTypeFormat.HYBRID.name());
            }

            dataList.add(session.getSessionLocation() != null ? session.getSessionLocation().getName() : STRING_EMPTY);

            dataList.add((session.getStartTime() != null) ? dateFormatter.format(getDateInLocal(session.getStartTime(),event.getEquivalentTimeZone())): STRING_EMPTY);
            dataList.add((session.getEndTime() != null) ? dateFormatter.format(getDateInLocal(session.getEndTime(),event.getEquivalentTimeZone())): STRING_EMPTY);
            dataList.add(cleanHtmlTags(session.getDescription(), false));
            dataList.add(Integer.toString(session.getCapacity()));
            List<SpeakerDTO> sessionSpeakerList = sessionSpeakerService.getSpeakerDtoBySession(session.getId());
            if(null != sessionSpeakerList && !sessionSpeakerList.isEmpty()) {
                List<String> speakerNameList = new ArrayList<>();
                for (SpeakerDTO speakerDTO : sessionSpeakerList) {
                    String speakerName = speakerDTO.getFirstName() + SPACE + speakerDTO.getLastName();
                    speakerNameList.add(speakerName);
                }
                dataList.add(convertListToCommaSeparated(speakerNameList));
            } else {
                dataList.add(EMPTY);
            }
            String tags = null;
            String tracks = null;
            try {
                if (CollectionUtils.isNotEmpty(sessionTagAndTrackList)) {
                    List<Long> keyValueIds = sessionTagAndTrackList.stream().filter(sessionTagAndTrack -> sessionTagAndTrack.getSessionId().equals(session.getId())).map(SessionTagAndTrack::getTagOrTrackId).collect(Collectors.toList());
                    List<String> tagsList = new ArrayList<>();
                    List<String> trackList = new ArrayList<>();
                    keyValue.stream().filter(keyValue1 -> keyValueIds.contains(keyValue1.getId())).forEach(keyValue1 -> {
                        if (keyValue1.getType().equals(EnumKeyValueType.TAG)) {
                            tagsList.add(keyValue1.getName());
                        } else if (keyValue1.getType().equals(TRACK)) {
                            trackList.add(keyValue1.getName());
                        }
                    });
                    tags = String.join(COMMA_SPACE, tagsList);
                    tracks = String.join(COMMA_SPACE, trackList);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            dataList.add(null != tags ? tags : STRING_EMPTY);
            dataList.add(null != tracks ? tracks : STRING_EMPTY);

            String sponsoringCompany = null;
            try {
                if (!org.springframework.util.StringUtils.isEmpty(session.getSponsorExhibitorJson())) {
                    HashMap<String, List<Long>> dto = getData(session.getSponsorExhibitorJson());
                    if (null != dto.get(SPONSORS)) {
                        List<String> sponsoringCompanyList = sponsorsService.getSponsorsByIds(dto.get(SPONSORS)).stream().map(Sponsors::getSponsorName).filter(Objects::nonNull).collect(Collectors.toList());
                        sponsoringCompany = convertListToCommaSeparated(sponsoringCompanyList);
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            dataList.add(null != sponsoringCompany ? sponsoringCompany : STRING_EMPTY);

            if ((EnumSessionFormat.BREAKOUT_SESSION.equals(session.getFormat()) || EnumSessionFormat.MAIN_STAGE.equals(session.getFormat())) && null != session.getStreamProvider()) {
                if (StreamProvider.ACCELEVENTS.equals(session.getStreamProvider())) {
                    if (session.isAccelEventsStudio()) {
                        dataList.add(ACCELEVENTS_STUDIO);
                        String studioLink = getPortalLinkStudio(event);
                        String studioUrl = studioLink + "/" + session.getId();
                        dataList.add(studioUrl);
                    } else if (!session.isAccelEventsStudio()) {
                        dataList.add(ACCELEVENTS_RTMP);
                        dataList.add(STRING_EMPTY);
                        dataList.add(session.getStreamKey());
                        dataList.add(session.getRtmpUrl());
                        String studioLink = getLiveStreamingLinkStudio(event, session);
                        String studioUrl = studioLink + "/" + session.getId();
                        dataList.add(studioUrl);
                    }
                } else {
                    dataList.add(session.getStreamProvider().name());
                    String[] values = {STRING_EMPTY, STRING_EMPTY, STRING_EMPTY, STRING_EMPTY};
                    dataList.addAll(Arrays.asList(values));
                }
                if (StreamProvider.ZOOM.equals(session.getStreamProvider())) {
                    dataList.add(session.getStreamUrl());
                    dataList.add(session.getMeetingPassword());
                } else {
                    dataList.add(STRING_EMPTY);
                    dataList.add(STRING_EMPTY);
                }
                dataList.add(StreamProvider.YOUTUBE.equals(session.getStreamProvider()) ? session.getStreamUrl() : STRING_EMPTY);
                dataList.add(StreamProvider.VIMEO.equals(session.getStreamProvider()) ? session.getStreamUrl() : STRING_EMPTY);
                dataList.add(StreamProvider.WISTIA.equals(session.getStreamProvider()) ? session.getStreamUrl() : STRING_EMPTY);
                dataList.add(StreamProvider.FACEBOOK.equals(session.getStreamProvider()) ? session.getStreamUrl() : STRING_EMPTY);
                dataList.add(StreamProvider.VIDYARD.equals(session.getStreamProvider()) ? session.getStreamUrl() : STRING_EMPTY);
            }else {
                dataList.add(NONE);
            }

            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private HashMap<String, List<Long>> getData(String jsonString) {
        Type type = new TypeToken<HashMap<String, List<Long>>>() {
        }.getType();
        return new Gson().fromJson(jsonString, type);
    }

    private String getPortalLinkStudio(Event event) {
        return getPortalLinkBaseUrl(event).append(PORTAL_LINK).toString();
    }

    private String getLiveStreamingLinkStudio(Event event, Session session) {
        StringBuilder responseUrl = getPortalLinkBaseUrl(event);
        if (EnumSessionFormat.MAIN_STAGE.equals(session.getFormat())) {
            return responseUrl.append(LIVE_STREAMING_MAIN_STAGE_LINK).toString();
        } else if (EnumSessionFormat.BREAKOUT_SESSION.equals(session.getFormat())) {
            return responseUrl.append(LIVE_STREAMING_BREAKOUT_LINK).toString();
        }
        return null;
    }

    private StringBuilder getPortalLinkBaseUrl(Event event) {
        StringBuilder sb = new StringBuilder();
        sb.append(serviceHelper.getEventBaseUrl(event));
        sb.append(getEventPath());
        sb.append(event.getEventURL());
        return sb;
    }

    @Override
    public void downloadEventBillingAttendeeData(PrintWriter writer, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(EMAIL);
        headerList.add(ROLE);
        List<Long> userIds = getListofDistinctUserIdEvent(event);
        List<BasicUserEventBillingDto> userEventBillingDtos = userService.findByUserIdIn(userIds);
        List<List<String>> dataRowList = new ArrayList<>();

        List<Staff> staffList = staffService.findAllByEventId(event.getEventId());

        Map<Long, List<Staff>> staffRole = staffList.stream().collect(Collectors.groupingBy(Staff::getUserId));

        List<Long> speakersUserId = speakerRepoService.findSpeakerUserIdsByEventId(event.getEventId());

        userEventBillingDtos.forEach(e->{
            List<String> dataRow = new ArrayList<>();
            dataRow.add(e.getFirstName());
            dataRow.add(e.getLastName());
            dataRow.add(e.getEmail());

            if(staffRole!=null && CollectionUtils.isNotEmpty(staffRole.get(e.getUserId()))){
                dataRow.add(staffRole.get(e.getUserId()).get(0).getRole().name());
            } else if(speakersUserId.contains(e.getUserId())){
                dataRow.add("Speaker");
            }else {
                dataRow.add("Attendee");
            }

            dataRowList.add(dataRow);
        });

        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private List<Long> getListofDistinctUserIdEvent(Event event) {
        try {
            //until mentioned Date for paid/Payment_Requested amount will have getstream Attendee list.
            logger.info("getListofDistinctUserIdEvent {}",event.getEventId());
            Date oldBillingDate=DateUtils.getFormattedDate("2021-06-10 00:00:00","yyyy-MM-dd HH:mm:ss");
            BillingEvents billingEvents = billingEventsService.findBillingEventsByEventId(event.getEventId());
            if(billingEvents!=null && (billingEvents.getCreatedAt()==null || billingEvents.getCreatedAt().before(oldBillingDate)) && !billingEvents.getPaymentStatus().equals(UNPAID) ){
               return getStreamService.getListOfUserIds(event);
            }else {
                return CheckInAuditLogService.listDistinctUsersByEventId(event.getEventId()).stream().map(BigInteger::longValue).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.info("Issue while getting members of event {}",e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public void downloadLoungeUsersCSVAllSession(PrintWriter writer, Event event) {

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = holderReqAttributeService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);
        List<String> headerList = new ArrayList<>();

        // Set up For Holder Questions
        List<TicketHolderRequiredAttributes> listOfAttributesHavingOnlyEnableForTicketHolder = Collections.emptyList();
        if (!holderRequiredAttributes.isEmpty()) {
            listOfAttributesHavingOnlyEnableForTicketHolder = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
        }
        List<EventTickets> eventTickets = eventCommonRepoService.findByEventIdJoinFetch(event, 0L, false, DataType.TICKET, null, null);
        Map<Long, List<EventTickets>> holderUserEventTickets =  eventTickets.stream().collect(Collectors.groupingBy(eventTicket -> eventTicket.getHolderUserId().getUserId()));
        List<TicketHolderRequiredAttributes> finalListOfAttributesHavingOnlyEnableForTicketHolder = listOfAttributesHavingOnlyEnableForTicketHolder;

        List<List<String>> dataRowList = new ArrayList<>();
        List<AttendeeProfileDto> allLoungeAttendeeProfile = networkingLoungeServiceImpl.getAllLoungeAttendeeProfileByEvent(event);
        if (!allLoungeAttendeeProfile.isEmpty()) {
            List<Long> userList = allLoungeAttendeeProfile.stream().map(AttendeeProfilePeoplePageDetailsDto::getUserId).collect(Collectors.toList());
            List<User> users = roUserService.getListOfUsersByUserIds(userList);
            users.forEach(loungeAttendee->{
                List<String> dataList = new ArrayList<>();
                for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : finalListOfAttributesHavingOnlyEnableForTicketHolder) {
                    List<EventTickets> eventTicketsList = holderUserEventTickets.get(loungeAttendee.getUserId());
                    if(CollectionUtils.isNotEmpty(eventTicketsList)){
                        EventTickets eventTicket = eventTicketsList.get(0);
                        TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                        TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                        prepareDataList(eventTicket, ticketAttributeValueDto, dataList, ticketHolderRequiredAttributes, ticketBuyerUploadsUrl, true, false, null, false,false);
                    } else {
                        setLoungeCSVDataForNonAttendeeUser(loungeAttendee, dataList, ticketHolderRequiredAttributes);
                    }
                }
                if (!dataList.isEmpty())
                    dataRowList.add(dataList);
            });
            this.downloadCSVFile(writer, headerList, dataRowList);
        }
    }

    private void setLoungeCSVDataForNonAttendeeUser(User loungeAttendee, List<String> dataList, TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
        if(ticketHolderRequiredAttributes.getName().equalsIgnoreCase(STRING_FIRST_SPACE_NAME)){
            dataList.add(loungeAttendee.getFirstName());
        } else if (ticketHolderRequiredAttributes.getName().equalsIgnoreCase(STRING_LAST_SPACE_NAME)) {
            dataList.add(loungeAttendee.getLastName());
        } else if (ticketHolderRequiredAttributes.getName().equalsIgnoreCase(STRING_EMAIL_SPACE)) {
            dataList.add(loungeAttendee.getEmail());
        } else {
            dataList.add(STRING_EMPTY);
        }
    }

    public void downloadLeaderboardDataCSVByChallengeId(PrintWriter writer, Event event, long challengeId) {
        EventChallengeDTO challengeDetail = challengeConfigService.findById(challengeId, event);
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.COMPANY);
        headerList.add(HEADER.TITLE);
        if(isNotBlank(challengeDetail.getName())){
            headerList.add(challengeDetail.getName());
        }
        List<List<String>> dataRowList = new ArrayList<>();
        try {
            DataTableResponse dataTableResponse = leaderboardService.getLeaderBoardDataByChallengeId(event, challengeId, new PageSizeSearchObj(0, Integer.MAX_VALUE));
            List<Leaderboard> leaderboardList = dataTableResponse.getData() != null ? (List<Leaderboard>) dataTableResponse.getData() : Collections.emptyList();
            leaderboardList.forEach(leaderboard -> {
                List<String> dataList = new ArrayList<>();
                dataList.add(leaderboard.getFirstName());
                dataList.add(leaderboard.getLastName());
                dataList.add(leaderboard.getEmail());
                dataList.add(leaderboard.getCompany());
                dataList.add(leaderboard.getTitle());
                dataList.add(String.valueOf(leaderboard.getPoint()));
                dataRowList.add(dataList);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadLeaderBoardDataCSV(PrintWriter writer, Event event) throws IOException {
        if (virtualEventService.isNewGamificationEnabled(event.getEventId())) {
            downloadGamificationLeaderBoardData(writer,event);
        } else {
            downloadLeaderBoardData(writer,event);
        }
    }

    private void downloadLeaderBoardData(PrintWriter writer, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.COMPANY);
        headerList.add(HEADER.TITLE);
        headerList.add(HEADER.POINT);
        List<List<String>> dataRowList = new ArrayList<>();
        try {
            List<Leaderboard> leaderboardList;
            leaderboardList = leaderboardService.findAllByEventId(event);
            leaderboardList.forEach(leaderboard -> {
                List<String> dataList = new ArrayList<>();
                dataList.add(leaderboard.getFirstName());
                dataList.add(leaderboard.getLastName());
                dataList.add(leaderboard.getEmail());
                dataList.add(leaderboard.getCompany());
                dataList.add(leaderboard.getTitle());
                dataList.add(String.valueOf(leaderboard.getPoint()));
                dataRowList.add(dataList);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void downloadGamificationLeaderBoardData(PrintWriter writer, Event event) {
        List<EventChallengeDetail> challenges = challengeConfigService.getAllChallengesByEventId(event);
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.COMPANY);
        headerList.add(HEADER.TITLE);
        if (CollectionUtils.isNotEmpty(challenges)) {
            challenges.forEach(challengeDetail -> headerList.add(challengeDetail.getChallengeName()));
        }
        List<List<String>> dataRowList = new ArrayList<>();
        try {
            List<ChallengeLeaderBoardData> leaderboardList = leaderboardService.getLeaderBoardDataByEventIdForCSV(event);
            leaderboardList.forEach(leaderboard -> {
                List<String> dataList = new ArrayList<>();
                dataList.add(leaderboard.getFirstName());
                dataList.add(leaderboard.getLastName());
                dataList.add(leaderboard.getEmail());
                dataList.add(leaderboard.getCompany());
                dataList.add(leaderboard.getTitle());
                if (CollectionUtils.isNotEmpty(challenges)) {
                    challenges.forEach(challengeDetail -> {
                        if (leaderboard.getUserChallengePointMap().get(challengeDetail.getId()) != null) {
                            dataList.add(String.valueOf(leaderboard.getUserChallengePointMap().get(challengeDetail.getId())));
                        } else {
                            dataList.add(String.valueOf(0));
                        }
                    });
                }
                dataRowList.add(dataList);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadSessionAttendanceReport(PrintWriter writer, Long sessionId, Event event) {
        Session session = sessionService.getSessionById(sessionId, event);
        downloadSessionAttendanceReportFromVideoAnalytics(writer, session, event);
    }

    @Override
    public void downloadSessionVideoAnalyticsGraphDataCSV(PrintWriter writer, Long sessionId, Event event) {

        logger.info("Start Download session video analytics graph data sessionId {}", sessionId);
        Session session = sessionService.getSessionById(sessionId, event);
        Map<Double, Map<String, Integer>> graphData = consolidatedAnalyticsService.getSessionVideoAnalyticsGraphDataCSV(event.getEventId(), sessionId);

        logger.info("Fetched graph data graphData {}", graphData.size());

        List<List<String>> dataRowList = new ArrayList<>();
        List<String> headerList = new ArrayList<>();
        headerList.add(TIME);
        headerList.add(LIVE_USERS);
        headerList.add(RECORDING_USERS);

        graphData.forEach((e, views ) -> {
            List<String> dataRow = new ArrayList<>();
            dataRow.add(VideoAnalyticsUtils.getSecondsInHHMMSS(e));
            dataRow.add(String.valueOf(views.getOrDefault(VideoAnalyticsQueryConstants.LIVE_VIEW ,0)));
            dataRow.add(String.valueOf(views.getOrDefault(VideoAnalyticsQueryConstants.RECORDING_VIEW,0)));
            dataRowList.add(dataRow);
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
        logger.info("End of download session video analytics graph data sessionId {}", session);
    }

    private void downloadSessionAttendanceReportFromVideoAnalytics(PrintWriter writer, Session session, Event event) { // NOSONAR
        logger.info("Start downloadSessionAttendanceReportFromVideoAnalytics start for sessionId {}", session.getId());
        
        List<RegisterdHolderUsers> usersList = userSessionService.getRegisteredUserInfo(session.getId());
        double sessionDuration = sessionDetailsService.getSessionVideoDurationBySession(session);

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);
        List<EventTickets> eventTickets = eventCommonRepoService.findByEventAndSessionIdJoinFetch(event, session.getId(), 0L, false, DataType.TICKET);
        Map<Long, EventTickets> userTicketsMap = eventTickets.stream().collect(Collectors.toMap(e -> e.getId(), Function.identity(), (firstTicket, secondTicket) -> firstTicket));

        logger.info("Fetch event tickets info size {}, holder required attributes size {}", eventTickets.size(), holderRequiredAttributes.size());

        List<String> headerList = new ArrayList<>();
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
        int orderFormHeaderCount = headerList.size();

        boolean prefixAdded = isPrefixAdded(ticketHolderRequiredAttributes);
        boolean addVideoAnalyticsData = isAddVideoAnalyticsData(session);

        headerList.add(CSV_HEADER.TICKET_TYPE_NAME);
        headerList.add(CSV_HEADER.IS_BOOKMARKED);
        headerList.add(CSV_HEADER.USER_SESSION_STATUS);
        headerList.add(CSV_HEADER.REGISTERED_DATE);
        headerList.add(CSV_HEADER.SESSION_DURATION);

        if(addVideoAnalyticsData) {
            headerList.add(CSV_HEADER.ATTENDED_LIVE);
            headerList.add(CSV_HEADER.DURATION_TIME_LIVE);
            headerList.add(CSV_HEADER.WATCHED_RECORDING);
            headerList.add(CSV_HEADER.DURATION_TIME_RECORDED);
        }
        headerList.add(CSV_HEADER.DOCUMENT);
        headerList.add(CSV_HEADER.TOTAL_DOCUMENT_DOWNLOADED);
        headerList.add(CSV_HEADER.UNIQUE_DOWNLOADS);
        headerList.add(CSV_HEADER.DOCUMENT_DOWNLOADED_LIST);
        headerList.add(CSV_HEADER.CHECKED_IN_DATE);
        headerList.add(CSV_HEADER.CHECKED_OUT_DATE);
        List<List<String>> dataRowList = new ArrayList<>();

        List<SessionCheckInLog> sessionCheckInLogs=sessionCheckInLogRepoService.findAllSessionLogsBySessionIdAndStatus(session.getId(),EnumSessionCheckInLogStatus.CHECK_IN);
        Map<Long, SessionCheckInLog> userSessionCheckInMap = sessionCheckInLogs.stream().collect
                (Collectors.toMap(SessionCheckInLog::getEventTicketId, Function.identity(), (existingLog, newLog) -> existingLog));

        List<SessionCheckInLog> sessionCheckOutLogs=sessionCheckInLogRepoService.findAllSessionLogsBySessionIdAndStatus(session.getId(),EnumSessionCheckInLogStatus.CHECK_OUT);
        Map<Long, SessionCheckInLog> userSessionCheckOutMap = sessionCheckOutLogs.stream().collect
                (Collectors.toMap(SessionCheckInLog::getEventTicketId, Function.identity(), (existingLog, newLog) -> newLog));

        SessionCheckInLog sessionCheckInLogCheckIn = null;
        SessionCheckInLog sessionCheckInLogCheckOut = null;

        for (RegisterdHolderUsers registeredUser : usersList){
            List<String> dataRow = new ArrayList<>();

            EventTickets eventTicket = userTicketsMap.get(registeredUser.getEventTicketId());

            if(eventTicket != null) {
                TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                for (TicketHolderRequiredAttributes ticketHolderRequiredAttribute : ticketHolderRequiredAttributes) {
                    prepareDataList(eventTicket, ticketAttributeValueDto, dataRow, ticketHolderRequiredAttribute, ticketBuyerUploadsUrl, true, false, null, false,false);
                }
                dataRow.add(eventTicket.getTicketingTypeId().getTicketTypeName());
            } else {
                logger.info("Event ticket is not found for the user {}, sessionId {}", registeredUser.getUserId(), session.getId());
                // If event ticket not found for the user then use RegisterdHolderUsers object to set attendee details
                int nameHeaderCounts = 0;
                // If prefix is added in order form then add empty value
                if(prefixAdded) {
                    dataRow.add(STRING_EMPTY);
                    nameHeaderCounts++;
                }
                dataRow.add(registeredUser.getFirstName());
                dataRow.add(registeredUser.getLastName());
                dataRow.add(registeredUser.getEmail());
                nameHeaderCounts+=3;
                // Empty values for remaining order form fields
                for (int i = 0; i < orderFormHeaderCount - nameHeaderCounts; i++) {
                    dataRow.add(STRING_EMPTY);
                }
                // Empty value for Ticket Type Name
                dataRow.add(STRING_EMPTY);
            }
            dataRow.add(registeredUser.isBookmarked() ? YES : NO);
            dataRow.add(registeredUser.getUserSessionStatus().name());
            dataRow.add(registeredUser.getRegistrationDate() != null && event.getEquivalentTimeZone() != null
                            ? dateFormatter.format(getDateInLocal(registeredUser.getRegistrationDate(),event.getEquivalentTimeZone()))
                            : STRING_EMPTY);
            dataRow.add(VideoAnalyticsUtils.getSecondsInHHMMSS(sessionDuration));

            if(addVideoAnalyticsData) {
                Map<Long, UserSessionEngagementDto> userSessionEngagementDtoMap =  consolidatedAnalyticsService.getSessionUsersEngagementDetails(event, session.getId()).stream()
                        .collect(Collectors.toMap(UserSessionEngagementDto::getUserId, Function.identity(), (first, second) -> first));

                UserSessionEngagementDto userSessionEngagementDto = userSessionEngagementDtoMap.get(registeredUser.getUserId());
                double liveDurationTime = null != userSessionEngagementDto ? userSessionEngagementDto.getLiveWatchTime() : 0.0;

                dataRow.add(liveDurationTime > 0 ? "Yes" : "No");
                dataRow.add(VideoAnalyticsUtils.getSecondsInHHMMSS(liveDurationTime));

                double recordingDurationTime = null != userSessionEngagementDto ? userSessionEngagementDto.getRecordingWatchTime() : 0.0;
                dataRow.add(recordingDurationTime > 0 ? "Yes" : "No");
                dataRow.add(VideoAnalyticsUtils.getSecondsInHHMMSS(recordingDurationTime));
            }
            Long downloaded = virtualEventSessionLoggingService.isDocumentDownloadedByUserOrNot(registeredUser.getUserId(), session.getId());
            dataRow.add(downloaded>0?"YES":"NO");
            dataRow.add(String.valueOf(virtualEventSessionLoggingService.getUserTotalDocumentDownloadByUserId(registeredUser.getUserId(),session.getId())));
            dataRow.add(String.valueOf(virtualEventSessionLoggingService.uniqueDocumentDownloadsByUserId(registeredUser.getUserId(),session.getId())));
            dataRow.add(String.valueOf(virtualEventSessionLoggingService.docListDownloadedByUserId(registeredUser.getUserId(),session.getId())));
            sessionCheckInLogCheckIn=userSessionCheckInMap.get(registeredUser.getEventTicketId());
            dataRow.add(sessionCheckInLogCheckIn!=null && sessionCheckInLogCheckIn.getAuditTime() != null ? dateFormatter.format(getDateInLocal(sessionCheckInLogCheckIn.getAuditTime(), event.getEquivalentTimeZone())) : STRING_EMPTY);
            sessionCheckInLogCheckOut=userSessionCheckOutMap.get(registeredUser.getEventTicketId());
            dataRow.add(sessionCheckInLogCheckOut!=null && sessionCheckInLogCheckOut.getAuditTime() != null ? dateFormatter.format(getDateInLocal(sessionCheckInLogCheckOut.getAuditTime(), event.getEquivalentTimeZone())) : STRING_EMPTY);
            dataRowList.add(dataRow);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
        logger.info("End downloadSessionAttendanceReportFromVideoAnalytics for sessionId {}", session.getId());
    }

    public boolean isAddVideoAnalyticsData(Session session) {
       return Arrays.asList(StreamProvider.ACCELEVENTS, StreamProvider.YOUTUBE, StreamProvider.VIMEO, StreamProvider.DIRECT_UPLOAD,StreamProvider.VIDYARD, StreamProvider.WISTIA, StreamProvider.FACEBOOK).indexOf(session.getStreamProvider()) > -1
              || EnumSessionFormat.WORKSHOP.equals(session.getFormat()) ;
    }

    public boolean isPrefixAdded(List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes) {
       return ticketHolderRequiredAttributes.stream().anyMatch(e -> PREFIX.equals(e.getName()));
    }

    public double getWatchTIme(Map<String,Double> userWatchHistory, String viewType ){

        if( null == userWatchHistory || null == userWatchHistory.get(viewType)){
            return 0;
        }
        else {
            return userWatchHistory.get(viewType);
        }
    }

    @Override
    public void downloadSessionOverviewReport(PrintWriter writer, Event event){ //NOSONAR
        logger.info("Start downloadSessionOverviewReport for eventId {}" , event.getEventId());

        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        boolean isShowRecordingViews = getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone())
                .compareTo(DateUtils.getLocalFormattedDate(recordingViewStartDate)) > 0;

        List<SessionAnalyticsDto> sessionAnalyticsDtos = sessionService.getSessionsOverviewReportData(event);
        Set<Long> sessionIds = sessionAnalyticsDtos.stream().map(SessionAnalyticsDto::getSessionId).collect(Collectors.toSet());

        Map<Long, SessionConsolidatedVideoAnalyticsDto> sessionConsolidatedVideoAnalyticsDtoMap =
                consolidatedAnalyticsService.getConsolidatedVideoAnalyticsForEvent(event.getEventId(), new SessionListInputDto(sessionIds))
                        .stream()
                        .collect(Collectors.toMap(SessionConsolidatedVideoAnalyticsDto::getSessionId, Function.identity()));

        logger.info("Fetched session analytics details size {} ", sessionAnalyticsDtos.size());

        List<String> headerList = new ArrayList<>();
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.SESSION_NAME); //1
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.SESSION_DATE); //2
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.SESSION_LENGTH); //3
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.NUMBER_OF_ATTENDEES_BOOKMARKED); //4
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.NUMBER_OF_ATTENDEES_REGISTERED); //5
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.NUMBER_OF_VIEWS_LIVE); //6
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.AVERAGE_WATCH_TIME_LIVE); //7
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.LIVE_ENGAGEMENT); //8
        if(isShowRecordingViews){
            headerList.add(SESSION_OVERVIEW_REPORT_HEADER.NUMBER_OF_VIEWS_RECORDED); //9
            headerList.add(SESSION_OVERVIEW_REPORT_HEADER.AVERAGE_WATCH_TIME_RECORDED); //10
            headerList.add(SESSION_OVERVIEW_REPORT_HEADER.RECORDED_ENGAGEMENT); //11
        }

        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.DOCUMENT); //12
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.TOTAL_DOCUMENT_DOWNLOADED); //13
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.UNIQUE_DOWNLOADS); //14
        headerList.add(SESSION_OVERVIEW_REPORT_HEADER.DOCUMENT_DOWNLOADED_LIST); //15
        List<List<String>> dataRowList = new ArrayList<>();

        sessionAnalyticsDtos.forEach(session -> {
            Double duration = session.getDuration();
            List<String> dataRow = new ArrayList<>();
            dataRow.add(session.getTitle()); //1
            dataRow.add(session.getStartTime()); //2
            dataRow.add(duration != null && duration > 0 ? VideoAnalyticsUtils.getSecondsInHHMMSS(duration) : NOT_APPLICABLE); //3
            dataRow.add(Long.toString(session.getBookmarkedAttendeesCount() != null ? session.getBookmarkedAttendeesCount(): 0)); //4
            dataRow.add(Long.toString(session.getRegisteredUserCount() != null ? session.getRegisteredUserCount(): 0)); //5
            SessionConsolidatedVideoAnalyticsDto consolidatedData = sessionConsolidatedVideoAnalyticsDtoMap.getOrDefault(session.getSessionId(), new SessionConsolidatedVideoAnalyticsDto());
            // Live Views
            dataRow.add(Integer.toString(consolidatedData.getTotalLiveView() != null ? consolidatedData.getTotalLiveView(): 0)); //6
            Double avgAttendeeDurationLive = consolidatedData.getTotalLiveView() != null && consolidatedData.getLiveWatchTime() != null
                    ? consolidatedData.getLiveWatchTime() / consolidatedData.getTotalLiveView(): 0.0;

            dataRow.add(VideoAnalyticsUtils.getSecondsInHHMMSS(avgAttendeeDurationLive)); //7
            dataRow.add(getEngagementPercentage(avgAttendeeDurationLive, session.getDuration())); //8
            // Recording Views
            if (isShowRecordingViews) {
                dataRow.add(Integer.toString(consolidatedData.getTotalRecordingView() != null ? consolidatedData.getTotalRecordingView() : 0)); //9
                Double avgAttendeeDurationRecorded = consolidatedData.getTotalRecordingView() != null && consolidatedData.getRecordingWatchTime() != null
                        ? consolidatedData.getRecordingWatchTime() / consolidatedData.getTotalRecordingView(): 0.0;
                dataRow.add(VideoAnalyticsUtils.getSecondsInHHMMSS(avgAttendeeDurationRecorded)); //10
                dataRow.add(getEngagementPercentage(avgAttendeeDurationRecorded, session.getDuration())); //11
            }
            dataRow.add(session.getDocumentDownload() > 0 ? "YES" : "NO"); //12
            dataRow.add(Long.toString(session.getDocumentDownload())); //13
            dataRow.add(Long.toString(session.getUniqueDocumentDownload())); //14
            dataRow.add(session.getDocumentDownloadList()); //15
            dataRowList.add(dataRow);
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
        logger.info("End downloadSessionAttendanceReport for eventId {}" , event.getEventId());

    }

    private String getEngagementPercentage(Double avgAttendedDuration, Double sessionDuration) {
        String engagement = "0%";
        if (avgAttendedDuration != null && avgAttendedDuration > 0 && sessionDuration != null && sessionDuration > 0) {
            engagement = DoubleHelper.roundValueTwoDecimal(avgAttendedDuration * 100 / sessionDuration) + "%";
        }
        return engagement;
    }

    @Override
    public void downloadNetworkingLoungeAttendanceReport(HttpServletResponse response, String loungeId, long eventId) {
        logger.info("Start downloadNetworkingLoungeAttendanceReport start for networking lounge {} and eventId {}" , loungeId, eventId);
        List<User> usersList = networkingLoungeServiceImpl.getAllConnectedAttendeeByLoungeAndEventId(loungeId, eventId);
        Map<Long,List<String>> userPhotoMap = networkingLoungeServiceImpl.getPhotosMapByLoungeId(loungeId,String.valueOf(eventId));
        Map<Long, Map<String, String>> joinCountAndDuration = networkingLoungeAnalyticsService.getNetworkingLoungeFromES(eventId, loungeId);

        if(!usersList.isEmpty()){
            PrintWriter writer = setHeader(response,"Networking Lounge Attendance.csv");
            List<String> headerList = new ArrayList<>();
            headerList.add(CSV_HEADER.FIRST_NAME);
            headerList.add(CSV_HEADER.LAST_NAME);
            headerList.add(CSV_HEADER.EMAIL);
            headerList.add(CSV_HEADER_LOUNGE.PHOTO_UPLOAD_COUNT);
            headerList.add(CSV_HEADER_LOUNGE.VIDEO_UPLOAD_COUNT);
            headerList.add(CSV_HEADER_LOUNGE.ATTEND_COUNT);
            headerList.add(CSV_HEADER.DURATION_TIME);
            List<List<String>> dataRowList = new ArrayList<>();

            usersList.forEach(user -> {
                List<String> dataRow = new ArrayList<>();
                dataRow.add(user.getFirstName());
                dataRow.add(user.getLastName());
                dataRow.add(user.getEmail());
                dataRow.add(getPhotosCountByUser(user,userPhotoMap));
                dataRow.add(muxLivestreamAssetRepoService.findAllByNetworkingLoungeIdAndUserId(loungeId,user.getUserId()));
                userAttendedCountAndDuration(dataRow,joinCountAndDuration,user.getUserId());
                dataRowList.add(dataRow);
            });
            this.downloadCSVFile(writer, headerList, dataRowList);
            logger.info("End downloadNetworkingLoungeAttendanceReport for networking lounge {} and eventId {}" , loungeId, eventId);
        }else{
            logger.info("attendee not found for networking lounge {} and eventId {}" , loungeId, eventId);
            throw new NotAcceptableException(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_NOT_FOUND);
        }
    }

    private void userAttendedCountAndDuration(List<String> dataRow, Map<Long, Map<String, String>> joinCountAndDuration, Long userId){
        if(!joinCountAndDuration.isEmpty() && joinCountAndDuration.containsKey(userId)){
            Map<String, String> data = joinCountAndDuration.get(userId);
            if(!data.isEmpty()) {
                dataRow.add(data.get("joinCount"));
                dataRow.add(data.get("duration"));
            }
        } else {
            dataRow.add("N/A");
            dataRow.add("N/A");
        }
    }

    private PrintWriter setHeader(HttpServletResponse response, String fileName){
        CommonUtil.prepareDownloadableResponseHeader(response, fileName, CONTENT_CSV);
        PrintWriter writer = null;
        try {
            writer = response.getWriter();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return writer;
    }

    private String getPhotosCountByUser(User user, Map<Long,List<String>> userPhotoMap){
        List<String> photoList = !userPhotoMap.isEmpty() && userPhotoMap.containsKey(user.getUserId()) ? userPhotoMap.get(user.getUserId()) : Collections.emptyList();
        return photoList != null && !photoList.isEmpty() ? String.valueOf(photoList.size()) : "0";
    }

    @Override
    public void downloadChatCSVForLounge(HttpServletResponse response, String loungeId, Event event) {
        logger.info("Start downloadChatCSVForLounge for networking lounge {} and eventId {}" , loungeId, event.getEventId());
        NetworkingLounge networkingLounge = networkingLoungeServiceImpl.getNetworkingLoungeById(loungeId);
        if(networkingLounge.getEventId().equals(String.valueOf(event.getEventId()))){
            List<ChatMessageDTO> messages = getStreamService.getChannelMessages(loungeId.toUpperCase(), networkingLounge.getName(), LIVESTREAM,event);

            if(!messages.isEmpty()){
                String fileName = networkingLounge.getName().concat(UNDERSCORE_CHAT).concat(".csv");
                PrintWriter writer = setHeader(response,fileName);
                List<String> headerList = new ArrayList<>();

                headerList.add(USER_NAME);
                headerList.add("Message");
                headerList.add(HEADER.TIMESTAMP);
                headerList.add("Thread Messages");
                headerList.add("Attachments");

                List<List<String>> dataRowList = new ArrayList<>();
                for (ChatMessageDTO message : messages){
                    List<String> dataList = new ArrayList<>();
                    dataList.add(message.getUserName());
                    dataList.add(message.getMessage());
                    dataList.add(message.getTimestamp());
                    dataList.add(null != message.getThreads() && !message.getThreads().isEmpty() ? convertListToCommaSeparated(message.getThreads()) : STRING_EMPTY);
                    dataList.add(convertListToCommaSeparated(message.getAttachments()));
                    dataRowList.add(dataList);
                }
                this.downloadCSVFile(writer, headerList, dataRowList);
            } else {
                logger.info("Networking lounge chat not found for loungeId {} and eventId {}",loungeId,event.getEventId());
                throw new NotFoundException(NotFoundException.ChatNotFound.LOUNGE_CHAT_NOT_FOUND);
            }
        } else {
            logger.info("Networking lounge not found for loungeId {} and eventId {}",loungeId,event.getEventId());
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
        }
    }

    @Override
    public void downloadAttendeeLounges(HttpServletResponse response, String userId, Event event) {
        logger.info("download networking lounges for userId {} and eventId {} ",userId,event.getEventId());
        Optional<User> optionalUser = roUserService.getUserById(Long.parseLong(userId));
        List<NetworkingLounge> networkingLoungeList = networkingLoungeServiceImpl.findLoungesByUserIdAndEventId(userId, event);
        User user = null;
        if(optionalUser.isPresent()){
            user = optionalUser.get();
        }
        if(null != networkingLoungeList && !networkingLoungeList.isEmpty()){
            String fileName = null != user ? user.getFirstName().concat("_lounges").concat(".csv") : "Attendee Lounges.csv";
            PrintWriter writer = setHeader(response,fileName);
            List<String> headerList = new ArrayList<>();

            headerList.add("Lounge Name");
            headerList.add("Lounge Description");
            headerList.add(HEADER.TIMESTAMP);

            List<List<String>> dataRowList = new ArrayList<>();
            for (NetworkingLounge lounge : networkingLoungeList){
                List<String> dataList = new ArrayList<>();
                dataList.add(lounge.getName());
                dataList.add(lounge.getDescription());
                dataList.add(lounge.getCreateAt());
                dataRowList.add(dataList);
            }
            this.downloadCSVFile(writer, headerList, dataRowList);
        } else {
            logger.info("Networking lounge not found for userId {} and eventId {} ",userId,event.getEventId());
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
        }
    }

    @Override
    public void downloadLoungeFeed(HttpServletResponse response, Event event, String loungeId, User user, int page, int size) {
        NetworkingLounge networkingLounge = networkingLoungeServiceImpl.getNetworkingLoungeById(loungeId);
        if(null != networkingLounge && networkingLounge.getEventId().equals(String.valueOf(event.getEventId()))) {
            List<FeedDetailsDto> feedDetailsDtoList = feedService.retrieveNetworkingLoungeFeedData(event ,loungeId, user, page, size);
            if (null != feedDetailsDtoList && !feedDetailsDtoList.isEmpty()) {
                String fileName = networkingLounge.getName().concat("_feed").concat(".csv");
                PrintWriter writer = setHeader(response, fileName);
                List<String> headerList = new ArrayList<>();

                headerList.add(USER_NAME);
                headerList.add("Post");
                headerList.add("Attachment");
                headerList.add(HEADER.TIMESTAMP);
                headerList.add("Comments");

                List<List<String>> dataRowList = new ArrayList<>();
                for (FeedDetailsDto feedDetails : feedDetailsDtoList) {
                    List<String> dataList = new ArrayList<>();
                    dataList.add(feedDetails.getAddedBy());
                    dataList.add(feedDetails.getFeedMessage());
                    dataList.add(getAttachmentType(feedDetails.getFeedAttachments()));
                    dataList.add(formatDateToEventTimezone(feedDetails.getFeedAddedTime(),event.getEquivalentTimeZone()));
                    dataList.add(convertListToCommaSeparated(getCommentStringFromList(feedDetails.getFeedReactionList(),event.getEquivalentTimeZone())));
                    dataRowList.add(dataList);
                }
                this.downloadCSVFile(writer, headerList, dataRowList);
            } else {
                logger.info("Networking lounge feed not found for loungeId {} and eventId {} ", loungeId, event.getEventId());
                throw new NotAcceptableException(NotAcceptableException.FeedExceptionMsg.LOUNGE_FEED_NOT_FOUND);
            }
        } else {
            logger.info("Networking lounge not found for loungeId {} and eventId {}",loungeId,event.getEventId());
            throw new NotAcceptableException(NotAcceptableException.NetworkingLoungeExceptionMsg.NETWORKING_LOUNGE_NOT_FOUND);
        }
    }

    private List<String> getCommentStringFromList(List<FeedReaction> feedReactionList, String timeZoneId){
        List<String> listOfComment = new ArrayList<>();
        feedReactionList.forEach(feedReaction -> {
            String commentText = feedReaction.getActivityData().getText();
            String comment = feedReaction.getActivityData().getUser().getName()
                    .concat(":")
                    .concat(isBlank(commentText)? "" : commentText)
                    .concat(":")
                    .concat(formatStringTimeStampToEventTimezone(feedReaction.getCreatedDate(),timeZoneId));
            listOfComment.add(comment);
        });
        return listOfComment;
    }

    private String getAttachmentType(FeedAttachments feedAttachments){
        String attachmentType;
        if(!feedAttachments.getFiles().isEmpty() && null != feedAttachments.getFiles().get(0)){
            attachmentType = feedAttachments.getFiles().get(0).get("name");
        } else if(!feedAttachments.getImages().isEmpty()){
            attachmentType = "Image";
        } else {
            attachmentType = "";
        }
        return attachmentType;
    }

    private static String formatStringTimeStampToEventTimezone(String createdAt, String timezoneId) {
        return DateUtils.getFormattedDateWithPattern(getDateInLocal(DateUtils.getFormattedDateTimeZonString(createdAt, GET_STREAM_DATE_TIME_FORMAT), timezoneId), DATE_FORMAT_MONTH);
    }

    private static String formatDateToEventTimezone(Date createdAt, String timezoneId) {
        return DateUtils.getFormattedDateWithPattern(getDateInLocal(createdAt, timezoneId), DATE_FORMAT_MONTH);
    }

    @Override
    public void downloadConnectedAttendeeDetails(User user, Event event, HttpServletResponse response) {
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllConnectedAttendeeDataCSV(user.getUserId(), event.getEventId());
        if (CollectionUtils.isEmpty(attendeeProfileDtos)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CONNECTION_DATA_NOT_FOUND);
        }
        PrintWriter writer = setHeader(response, "My Connections.csv");
        List<AttendeeConnectionConfigDTO> listAttendeeConnectionConfigDTO = attendeeConnectionAttributesService.getAttendeeConnectionConfiguration(event.getEventId());
        listAttendeeConnectionConfigDTO = listAttendeeConnectionConfigDTO.stream().filter(AttendeeConnectionConfigDTO::getIsEnable).collect(Collectors.toList());//NOSONAR
        List<String> headerList = new ArrayList<>();
        for (AttendeeConnectionConfigDTO attendeeConnectionConfigDTO : listAttendeeConnectionConfigDTO) {
            headerList.add(attendeeConnectionConfigDTO.getName());

        }
        List<List<String>> dataRowList = new ArrayList<>();
        for (AttendeeProfileDto attendeeProfileDto : attendeeProfileDtos) {
            convertDtoToMapAndAddToList(attendeeProfileDto, listAttendeeConnectionConfigDTO, dataRowList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadConnectedAttendeeFromActivityPage(User user, Event event, HttpServletResponse response) {
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllConnectedAttendeeDataCSV(user.getUserId(),event.getEventId());
        if (CollectionUtils.isEmpty(attendeeProfileDtos)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CONNECTION_DATA_NOT_FOUND);
        }
        String fileName = event.getName().concat("_MyConnection").concat(".csv");
        PrintWriter writer = setHeader(response, fileName);
        List<String> headerList = new ArrayList<>();
        headerList.add(EMAIL);
        headerList.add(FIRST_NAME);
        headerList.add(LAST_NAME);
        headerList.add(PRONOUNS);
        headerList.add(TITLE);
        headerList.add(COMPANY);
        List<List<String>> dataRowList = new ArrayList<>();
        for (AttendeeProfileDto attendeeProfileDto : attendeeProfileDtos) {
            List<String> dataList = new ArrayList<>();
            dataList.add(attendeeProfileDto.getEmail());
            dataList.add(attendeeProfileDto.getFirstName());
            dataList.add(attendeeProfileDto.getLastName());
            dataList.add(attendeeProfileDto.getPronouns() !=null ? attendeeProfileDto.getPronouns() : STRING_EMPTY);
            dataList.add(attendeeProfileDto.getTitle() != null ? attendeeProfileDto.getTitle() : STRING_EMPTY);
            dataList.add(attendeeProfileDto.getCompany() != null ? attendeeProfileDto.getCompany() : STRING_EMPTY);
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);

    }

    private void convertDtoToMapAndAddToList(AttendeeProfileDto attendeeProfileDto, List<AttendeeConnectionConfigDTO> listAttendeeConnectionConfigDTO, List<List<String>> dataRowList){
        List<String> dataList = new ArrayList<>();
        ObjectMapper oMapper = new ObjectMapper();
        Map<String, Object> map = oMapper.convertValue(attendeeProfileDto, Map.class);
        Map<String, Object> extraInfoMap = oMapper.convertValue(attendeeProfileDto.getExtraInfo(), Map.class);
        for (AttendeeConnectionConfigDTO attendeeConnectionConfigDTO : listAttendeeConnectionConfigDTO) {
            if (map.containsKey(attendeeConnectionConfigDTO.getValue()) && INTEREST.equals(attendeeConnectionConfigDTO.getValue())) {

                String interest = "";
                if(attendeeProfileDto.getInterests()!=null && CollectionUtils.isNotEmpty(attendeeProfileDto.getInterests())){
                    List<InterestDto> interestDtoList = attendeeProfileDto.getInterests();
                    interest = convertListToCommaSeparated(interestDtoList.stream().map(InterestDto::getName).collect(Collectors.toList()));
                }
                dataList.add(interest);
            } else if (map.containsKey(attendeeConnectionConfigDTO.getValue())) {
                dataList.add(String.valueOf(map.get(attendeeConnectionConfigDTO.getValue())));
            } else if (extraInfoMap.containsKey(attendeeConnectionConfigDTO.getValue())) {
                dataList.add(String.valueOf(extraInfoMap.get(attendeeConnectionConfigDTO.getValue())));
            }
        }
        dataRowList.add(dataList);
    }

    @Override
    public void downloadSpeakerEmailsDataCSV(PrintWriter writer, Event event) { //NOSONAR
        List<Speaker> speakers = roSpeakerService.findSpeakersWithSessionsByEventId(event.getEventId());

        List<CustomFormAttribute> speakerEnabledAttributes = customFormAttributeService
                .getSpeakerEnabledAttributesOrderByAttributeOrder(event, -1L);
        Map<Long, CustomFormAttributeData> speakerAttributeDataMap = new HashMap<>();
        if (null != speakers) {

            List<Long> speakerAttributeId = speakers.stream().filter(e->NumberUtils.isNumberGreaterThanZero(e.getSpeakerAttributeId())).map(Speaker::getSpeakerAttributeId).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(speakerAttributeId)){
                speakerAttributeDataMap = customFormAttributeDataRepository.findByIdIn(speakerAttributeId).stream().collect(Collectors.toMap(CustomFormAttributeData::getId, Function.identity(), (e1, e2) -> e1));
            }

            List<String> headerList = new ArrayList<>();
            headerList.add(SPEAKER_SPACE_ID);
            headerList.add(FIRST_NAME);
            headerList.add(LAST_NAME);
            headerList.add(EMAIL);
            headerList.add(PRONOUNS);
            headerList.add(TITLE);
            headerList.add(COMPANY);
            headerList.add(BIO);
            headerList.add(LINKEDIN_URL);
            headerList.add(INSTAGRAM_HANDLE);
            headerList.add(TWITTER_HANDLE);
            headerList.add(OVERRIDE_PROFILE_DETAILS);
            headerList.add(SPEAKER_TICKET);
            headerList.add(PRIMARY_SESSIONS);
            headerList.add(SECONDARY_SESSIONS);

            if(CollectionUtils.isNotEmpty(speakerEnabledAttributes)){
                for (CustomFormAttribute speakerAttribute : speakerEnabledAttributes) {
                    headerList.add(speakerAttribute.getName());
                }
            }

            List<List<String>> dataRowList = new ArrayList<>();
            for (Speaker speaker : speakers) {
                CustomAttributesResponseDto speakerAttributeResponseDto = new CustomAttributesResponseDto();
                List<String> dataList = new ArrayList<>();
                dataList.add(String.valueOf(speaker.getId()));
                dataList.add(speaker.getFirstName());
                dataList.add(speaker.getLastName());
                dataList.add(speaker.getEmail());
                dataList.add(speaker.getPronouns() !=null ? speaker.getPronouns() : STRING_EMPTY);
                dataList.add(speaker.getTitle() != null ? speaker.getTitle() : STRING_EMPTY);
                dataList.add(speaker.getCompany() != null ? speaker.getCompany() : STRING_EMPTY);
                dataList.add(speaker.getBio() != null ? cleanHtmlTags(speaker.getBio(),false) : STRING_EMPTY);
                dataList.add(speaker.getLinkedIn() != null ? speaker.getLinkedIn() : STRING_EMPTY);
                dataList.add(speaker.getInstagram() != null ? speaker.getInstagram() : STRING_EMPTY);
                dataList.add(speaker.getTwitter() != null ? speaker.getTwitter() : STRING_EMPTY);
                dataList.add(
                        speaker.getAllowOverrideDetails() == null
                                ? STRING_EMPTY
                                : (speaker.getAllowOverrideDetails() ? "Y" : "N")
                );
                dataList.add(null != speaker.getAllowAttendeeAccess() && speaker.getAllowAttendeeAccess() ? TRUE : FALSE);
                if(CollectionUtils.isEmpty(speaker.getSessionSpeakers())) {
                    dataList.add(STRING_EMPTY);
                    dataList.add(STRING_EMPTY);
                } else {
                    Map<Boolean, List<Long>> sessionMap = speaker.getSessionSpeakers()
                            .stream()
                            .collect(Collectors.groupingBy(
                                    SessionSpeaker::isModerator,
                                    Collectors.mapping(SessionSpeaker::getSessionId, Collectors.toList())
                            ));
                    List<Long> moderatorSessionIds = sessionMap.getOrDefault(true, Collections.emptyList());
                    List<Long> nonModeratorSessionIds = sessionMap.getOrDefault(false, Collections.emptyList());
                    String moderatorSessions = GeneralUtils.convertLongListToCommaSeparated(moderatorSessionIds);
                    String nonModeratorSessions = GeneralUtils.convertLongListToCommaSeparated(nonModeratorSessionIds);
                    dataList.add(!moderatorSessions.isEmpty() ? moderatorSessions : STRING_EMPTY);
                    dataList.add(!nonModeratorSessions.isEmpty() ? nonModeratorSessions : STRING_EMPTY);
                }
                dataRowList.add(dataList);
                if(speakerAttributeDataMap.get(speaker.getSpeakerAttributeId()) != null){
                    speakerAttributeResponseDto = customFormAttributeService.getCustomAttributeResponseDto(speakerAttributeDataMap.get(speaker.getSpeakerAttributeId()));
                }

                for (CustomFormAttribute speakerAttribute : speakerEnabledAttributes) {
                    String value = getAttributeValue(speakerAttributeResponseDto, speakerAttribute);
                    dataList.add(value != null ? value : STRING_EMPTY);
                }
            }
            this.downloadCSVFile(writer, headerList, dataRowList);
        }
    }

    private String getAttributeValue(CustomAttributesResponseDto speakerAttributeResponseDto,
                                     CustomFormAttribute customFormAttribute) {
        if(speakerAttributeResponseDto != null) {
            Map<String, String> speakerAttributeData = speakerAttributeResponseDto.getAttributes();
            if ( speakerAttributeData != null) {
                for (Map.Entry<String, String> entry : speakerAttributeData.entrySet()) {
                    if (customFormAttribute.getName().equalsIgnoreCase(entry.getKey())) {
                        return attributeValueCheck(entry.getValue());
                    }
                }
            }
        }
        return null;
    }

    private String getAttributeValueForContact(CustomAttributesResponseDto contactAttributeResponseDto,
                                     CustomFormAttribute customFormAttribute) {
        if(contactAttributeResponseDto != null) {
            Map<String, String> contactAttributeData = contactAttributeResponseDto.getAttributes();
            if (contactAttributeData != null) {
                for (Map.Entry<String, String> entry : contactAttributeData.entrySet()) {
                    if (entry.getKey().endsWith(STRING_UNDERSCORE + customFormAttribute.getId())) {
                        if (AttributeValueType.SINGLE_CHECKBOX.equals(customFormAttribute.getAttributeValueType())) {
                            return TRUE.equalsIgnoreCase(entry.getValue()) ? CHECKED : UNCHECKED;
                        }
                        return attributeValueCheck(entry.getValue());
                    }
                }
                if (AttributeValueType.SINGLE_CHECKBOX.equals(customFormAttribute.getAttributeValueType())) {
                    return UNCHECKED;
                }
            }
        }
        return null;
    }

    private String attributeValueCheck(String attributeValue){
        if("null".equalsIgnoreCase(attributeValue) || StringUtils.isBlank(attributeValue)){
            return null;
        }else {
            return attributeValue;
        }
    }

    @Override
    public void downloadExpoStaffDataCSV(PrintWriter writer, Event event) {
        List<Long> exhibitorIds = exhibitorService.findAllExhibitorIdByEvent(event);
        List<ExhibitorCategory> exhibitorCategories = exhibitorCategoryService.findByEventId(event.getEventId());
        Map<Long, String> longStringList = exhibitorCategories.stream().collect(Collectors.toMap(ExhibitorCategory::getId, ExhibitorCategory::getName));
        Map<Long, ExhibitorExportDTO> exportDTOMap = exhibitorService.findAllForExportByEventId(event.getEventId()).stream().collect(Collectors.toMap(ExhibitorExportDTO::getId, element -> element));
        Map<Long,String> expoIdNameList = exhibitorService.findIdAndNameByEventId(event.getEventId()).stream().collect(Collectors.toMap(ExhibitorDropDownDto::getId,ExhibitorDropDownDto::getName));
        if (null != exhibitorIds) {
            List<String> headerList = new ArrayList<>();
            headerList.add(EXHIBITOR);
            headerList.add(LOCATION);
            headerList.add(STAFF);
            headerList.add("Logo Image");
            headerList.add("Card Image");
            headerList.add("Banner Image");
            headerList.add("Carousel Image");
            headerList.add("Booth Category");

            List<List<String>> dataRowList = new ArrayList<>();
            exhibitorIds.forEach(exhibitorId -> {
                List<String> dataList = new ArrayList<>();
                List<String> staffFullName=new ArrayList<>();
                List<ExpoStaffDetailDto> staffList = staffRepository.findExhibitorStaffDetailsByExhibitorId(exhibitorId,event.getEventId());
                ExhibitorExportDTO exhibitorExportDTO = exportDTOMap.get(exhibitorId);
                for (ExpoStaffDetailDto userStaff : staffList) {
                    staffFullName.add(" "+userStaff.getFirstName()+" "+userStaff.getLastName()+" : "+userStaff.getEmail());
                }
                dataList.add(expoIdNameList.get(exhibitorId));
                if(isNotBlank(exhibitorExportDTO.getLocation()) && !EventFormat.VIRTUAL.getFormat().equals(event.getEventFormat().getFormat())){
                    dataList.add(exhibitorExportDTO.getLocation());
                }else{
                    dataList.add(STRING_EMPTY);
                }
                dataList.add(convertListToCommaSeparated(staffFullName));
                if (isNotBlank(exhibitorExportDTO.getLogo())) {
                    dataList.add(imageUrl + exhibitorExportDTO.getLogo());
                } else {
                    dataList.add(STRING_EMPTY);
                }

                if (isNotBlank(exhibitorExportDTO.getExpoCardImage())) {
                    dataList.add(imageUrl + exhibitorExportDTO.getExpoCardImage());
                } else {
                    dataList.add(STRING_EMPTY);
                }

                if (isNotBlank(exhibitorExportDTO.getBannerImage())) {
                    dataList.add(imageUrl + exhibitorExportDTO.getBannerImage());
                } else {
                    dataList.add(STRING_EMPTY);
                }

                if (isNotBlank(exhibitorExportDTO.getCarouselImage())) {
                    dataList.add(imageUrl + exhibitorExportDTO.getCarouselImage());
                } else {
                    dataList.add(STRING_EMPTY);
                }

                if (null != exhibitorExportDTO.getCategoryId() &&  isNotEmpty(exhibitorExportDTO.getCategoryId().trim())) {
                    List<Long> categoryIds = GeneralUtils.convertCommaSeparatedToListLong(exhibitorExportDTO.getCategoryId());
                    StringBuilder categoryName = new StringBuilder();
                    for(Long categoryId: categoryIds) {
                        categoryName.append(longStringList.get(categoryId));
                        categoryName.append(STRING_COMMA);
                    }
                    dataList.add(categoryName.substring(0,categoryName.length()-1));
                }else{
                    dataList.add(STRING_EMPTY);
                }

                dataRowList.add(dataList);
            });

            this.downloadCSVFile(writer, headerList, dataRowList);
        }
    }

    @Override
    public void getUnsubscribedUsersData(HttpServletResponse response, Long eventId){ //NOSONAR
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.DATE_TIME);
        headerList.add(TITLE);
        headerList.add(COMPANY);
        headerList.add(BIO);
        headerList.add(INSTAGRAM_HANDLE);
        headerList.add(TWITTER_HANDLE);

        List<Object[]> emailSuppressionList = emailSuppressionRepository.findListOfSubscribedUsers(eventId);

        if (CollectionUtils.isEmpty(emailSuppressionList)){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NO_UNSUBSCRIBEDUSERS_ARE_PRESENT);
        }
        String fileName = "unsubscribedUsers_"+ eventId +".csv";

        PrintWriter writer = setHeader(response, fileName);

        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getUnsubscribedUsersProfileData(emailSuppressionList,eventId);

        Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(),
                        (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));

        List<List<String>> dataRowList = new ArrayList<>();

            emailSuppressionList.forEach(e-> {
                List<String> dataList = new ArrayList<>();
                if (e[0] != null) {
                    Long userId = ((BigInteger) e[0]).longValue();
                    String firstName = (String) e[1];
                    String lastName = (String) e[2];
                    String email = (String) e[3];
                    Date date = (Date) e[4];
                    AttendeeProfileDto attendeeProfileDto = attendeeMap.get(userId);
                    boolean isAttendeeProfileNull = (null == attendeeProfileDto);
                    dataList.add(firstName);
                    dataList.add(lastName);
                    dataList.add(email);
                    dataList.add(date.toString());
                    dataList.add(isAttendeeProfileNull ? STRING_EMPTY : attendeeProfileDto.getTitle());
                    dataList.add(isAttendeeProfileNull ? STRING_EMPTY : attendeeProfileDto.getCompany());
                    dataList.add(isAttendeeProfileNull ? STRING_EMPTY : attendeeProfileDto.getExtraInfo() != null ? STRING_EMPTY : attendeeProfileDto.getExtraInfo().getAboutMe()); //NOSONAR
                    dataList.add(isAttendeeProfileNull ? STRING_EMPTY : attendeeProfileDto.getInstagram());
                    dataList.add(isAttendeeProfileNull ? STRING_EMPTY : attendeeProfileDto.getTwitter());
                    dataRowList.add(dataList);
                }
            });
            this.downloadCSVFile(writer, headerList, dataRowList);
        }

    @Override
    public void downloadSponsorAnalyticDataCSV(PrintWriter writer, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_NAME);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_TOTAL_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_LANDING_PAGE_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_VEH_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_LANDING_PAGE_ANONYMOUS_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_LANDING_PAGE_LOGGED_IN_USER_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_LOBBY_CAROUSEL_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_LOBBY_SPONSOR_TAB_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_SESSION_CAROUSEL_CLICKS);
        headerList.add(SPONSOR_ANALYTIC_REPORT_HEADER_SESSION_SPONSOR_TAB_CLICKS);
        List<List<String>> dataRowList = new ArrayList<>();
        List<SponsorAnalyticClickDTO> sponsorsAnalyticsDataReport = sponsorsService.getAllSponsorsAnalyticsCSVData(event);
        sponsorsAnalyticsDataReport.forEach(sponsorAnalyticClickDTO -> {
            List<String> dataList = new ArrayList<>();
            dataList.add(sponsorAnalyticClickDTO.getSponsorName());
            Map<String, Long> countBySource = sponsorAnalyticClickDTO.getCountBySource();
            if (!countBySource.isEmpty()) {
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.TOTAL)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.EVENT_SITE)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.VIRTUAL_EVENT_HUB)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.ANONYMOUS)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.LOGGED_IN)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.LOBBY_CAROUSEL)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.LOBBY)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.SESSION_CAROUSEL)));
                dataList.add(String.valueOf(countBySource.get(SponsorAnalyticsConstant.SESSION)));
            }
            dataRowList.add(dataList);
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private String getExpoFileNameForZip(String expoName, String suffix) {
        expoName = expoName.replaceAll("[^A-Za-z0-9]", "");
        return abbreviate(expoName, FILE_NAME_MAX_LENGTH)  + suffix;
    }

    @Override
    public void downloadZIPForAnalytics(AnalyticsReportDTO analyticsReportDTO, HttpServletResponse response, Event event, User user) throws IOException { //NOSONAR

        CommonUtil.prepareDownloadableResponseHeader(response, "Reports", APPLICATION_ZIP);
        ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());

        Executor executor = Executors.newFixedThreadPool(10);
        List<CompletableFuture<List<File>>> futures = new ArrayList<>();

        // Booth Leads Report
        if (analyticsReportDTO.isBoothLeads()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    List<Exhibitor> exhibitors = exhibitorService.findAllByEvent(event);
                    if (exhibitors.isEmpty()) {
                        File file = getTempFile("Leads.csv");
                        List<String> headerList = Collections.singletonList(NO_DATA_FOUND);
                        List<List<String>> dataRowList = new ArrayList<>();
                        downloadCSVFile(new PrintWriter(file,UTF_16), headerList, dataRowList);
                        localFiles.add(file);
                    } else {
                        List<Long> exhibitorIds = exhibitors.stream()
                                .map(Exhibitor::getId)
                                .collect(Collectors.toList());

                        Map<Long,ExhibitorSetting>  exhibitorSettingMap = exhibitorSettingsService.getExhibitorOrGlobalSettings(exhibitorIds, event);
                        ExhibitorSetting deafultExhibitorSetting = prepareExhibitorSettingsWithDefaultData(event);
                        Ticketing ticketing = ticketingHelperService.findTicketingByEventIdOrThrowError(event);
                        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllAttendeeProfiles(event.getEventId());
                        Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                                .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(), (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));
                        List<Staff> allStaffByEvent = staffService.findAllByEventId(event.getEventId());
                        for (Exhibitor exhibitor : exhibitors) {
                           File file = getTempFile(getExpoFileNameForZip(exhibitor.getName(), LEADS_CSV_FILE_SUFFIX));
                           this.downloadLeadsCSV(new PrintWriter(file,"UTF-16"), exhibitor.getId(), null, event, deafultExhibitorSetting, ticketing, attendeeMap, allStaffByEvent, exhibitorSettingMap);
                           localFiles.add(file);
                        }
                    }
                }
                catch (Exception e) {
                    logger.error("Booth Leads Report failed", e);
                }
                return localFiles;
            }, executor));
        }


        // Expo Summary
        if (analyticsReportDTO.isExpoSummary()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try{
                    File file = getTempFile("Expo Summary.csv");
                    downloadLeadsCSVForAllExhibitor(new PrintWriter(file,UTF_16), event);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Expo Summary failed", e);
                }
                return localFiles;
            },executor));
        }

        // Expo Summary & Leads
        if (analyticsReportDTO.isExpoSummaryAndLeads()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try{
                    List<Exhibitor> exhibitors = exhibitorService.findAllByEvent(event);
                    if (exhibitors.isEmpty()) {
                        File file = getTempFile("Exhibitor.csv");
                        List<String> headerList = Collections.singletonList(NO_DATA_FOUND);
                        List<List<String>> dataRowList = new ArrayList<>();
                        downloadCSVFile(new PrintWriter(file,UTF_16), headerList, dataRowList);
                        localFiles.add(file);
                    } else {
                        for (Exhibitor exhibitor : exhibitors) {
                           File file = getTempFile(getExpoFileNameForZip(exhibitor.getName(), EXPO_SUMMERY_CSV_FILE_SUFFIX));
                           downloadExhibitor(new PrintWriter(file,UTF_16),exhibitor.getId(), event,user);
                           localFiles.add(file);
                        }
                    }
                }
                catch (Exception e) {
                    logger.error("Expo Summary failed", e);
                }
                return localFiles;
            },executor));
        }


        // Gamification
        if (analyticsReportDTO.isGamification()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    File file = getTempFile("Gamification Results.csv");
                    downloadLeaderboardDetailDataCSV(new PrintWriter(file,UTF_16), event);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Gamification failed", e);
                }
                return localFiles;
            },executor));
        }



        // Sales Summary

        if (analyticsReportDTO.isSalesSummary()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    File file = getTempFile("Sales Summary.csv");
                    downloadAttendeeDataWithTicketPriceBreakDown(new PrintWriter(file,UTF_16), event);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Sales Summary failed", e);
                }
                return localFiles;
            },executor));
        }

        // Ticket Add-On Summary

        if (analyticsReportDTO.isTicketAddonSummary()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    File file = getTempFile("Add On Summary.csv");
                    downloadTicketHolderData(new PrintWriter(file,UTF_16), event, user, 0L, DataType.ADDON, null, null);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Ticket Add-on Summary failed", e);
                }
                return localFiles;
            },executor));
        }

        // Ticket Buyer Orders Summary

        if (analyticsReportDTO.isBuyerOrderSummary()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    java.io.File file = getTempFile("Buyers Order Summary.csv");
                    downloadTicketBuyerData(new PrintWriter(file,UTF_16), event, 0L, null, null,null);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Buyer Orders failed", e);
                }
                return localFiles;
            },executor));
        }

        // Ticket Holder Orders Summary

        if (analyticsReportDTO.isHolderOrderSummary()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try{
                    java.io.File file = getTempFile("Holders Order Summary.csv");
                    downloadTicketHolderData(new PrintWriter(file,UTF_16), event, user,0L, null, null, null);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Holder Orders failed", e);
                }
                return localFiles;
            },executor));
        }

        // Ticket Buyer and Holder Orders Summary

        if (analyticsReportDTO.isTicketBuyerAndHolder()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try{
                    File file = getTempFile("Registration Data.csv");
                    downloadTicketBuyerAndHolderData(new PrintWriter(file,UTF_16), event, 0L);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Expo Summary failed", e);
                }
                return localFiles;
            },executor));
        }


        // Deleted Ticket Buyer and Holder Orders Summary

        if (analyticsReportDTO.isDeletedTicketsSummary()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    File file = getTempFile("Deleted Tickets Data.csv");
                    downloadDeletedTicketBuyerAndHolderData(new PrintWriter(file,UTF_16), event, 0L);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Deleted Tickets failed", e);
                }
                return localFiles;
            },executor));
        }

        // Session Overview

        if (analyticsReportDTO.isSessionOverview()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try{
                    File file = getTempFile("Session Overview.csv");
                    downloadSessionOverviewReport(new PrintWriter(file,UTF_16), event);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Session Overview failed", e);
                }
                return localFiles;
            },executor));
        }

        // Session + Attendee Comparison

        if (analyticsReportDTO.isSessionAttendeeComparison()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    File file = getTempFile("Session + Attendee Comparison.csv");
                    downloadAllAttendeeSessionsInfoCSV(new PrintWriter(file,UTF_16), event);
                    localFiles.add(file);
                }
                catch (Exception e) {
                    logger.error("Session Attendee Comparison failed", e);
                }
                return localFiles;
            },executor));
        }

        //download report for the Schedule meetings

        if (analyticsReportDTO.isScheduleMeetings()){
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<File> localFiles = new ArrayList<>();
                try {
                    File booked = getTempFile("Scheduled Meetings Report(BOOKED).csv");
                    downloadScheduledMeetingsReport(new PrintWriter(booked,UTF_16), event, MeetingStatus.BOOKED);
                    localFiles.add(booked);

                    File created = getTempFile("Scheduled Meetings Report(CREATED).csv");
                    downloadScheduledMeetingsReport(new PrintWriter(created,UTF_16), event, MeetingStatus.CREATED);
                    localFiles.add(created);

                    File declined = getTempFile("Scheduled Meetings Report(DECLINED).csv");
                    downloadScheduledMeetingsReport(new PrintWriter(declined,UTF_16), event, MeetingStatus.DECLINED);
                    localFiles.add(declined);
                }
                catch (Exception e) {
                    logger.error("Expo Summary failed", e);
                }
                return localFiles;
            },executor));
        }

        //Polls
        futures.add(CompletableFuture.supplyAsync(() -> {
            List<File> localFiles = new ArrayList<>();
            try{
                if(analyticsReportDTO.isAllPolls()){
                    List<Session> sessionList = sessionService.getAllSessionByEvent(event);
                    for(Session session : sessionList){
                        if(session != null){
                            File file = getTempFile(getExpoFileNameForZip(session.getTitle(), POLLS_CSV_FILE_SUFFIX));
                            downloadPolls(new PrintWriter(file, UTF_16), session.getId(), EnumQNAType.SESSION, event, Boolean.TRUE);
                            localFiles.add(file);
                        }
                    }
                }else{
                    if(CollectionUtils.isNotEmpty(analyticsReportDTO.getPollSessionIds())){
                        for(Long sessionId : analyticsReportDTO.getPollSessionIds()){
                            Session session = sessionService.getSessionById(sessionId, event);
                            if(session != null){
                                java.io.File file = getTempFile(getExpoFileNameForZip(session.getTitle(), POLLS_CSV_FILE_SUFFIX));
                                downloadPolls(new PrintWriter(file, UTF_16), sessionId, EnumQNAType.SESSION, event, Boolean.TRUE);
                                localFiles.add(file);
                            }
                        }
                    }
                }
            }
            catch (Exception e) {
                logger.error("Unexpected error in Polls block", e);
            }
            return localFiles;
        },executor));


        //Q&A Summary

        futures.add(CompletableFuture.supplyAsync(() -> {
            List<File> localFiles = new ArrayList<>();
            try {
                if(analyticsReportDTO.isAllQAs()){
                    List<Session> sessionList = sessionService.getAllSessionByEvent(event);
                    for(Session session : sessionList){
                        if(session != null) {
                            File file = getTempFile(getExpoFileNameForZip(session.getTitle(), QA_SUMMERY_CSV_FILE_SUFFIX));
                            downloadQuestionAnswers(new PrintWriter(file, UTF_16), session.getId(), EnumQNAType.SESSION, event, Boolean.TRUE);
                            localFiles.add(file);
                        }
                    }
                }
                else {
                    if(CollectionUtils.isNotEmpty(analyticsReportDTO.getQaSessionIds())){
                        for(Long sessionId : analyticsReportDTO.getQaSessionIds()){
                            Session session = sessionService.getSessionById(sessionId, event);
                            if(session != null){
                                File file = getTempFile(getExpoFileNameForZip(session.getTitle(), QA_SUMMERY_CSV_FILE_SUFFIX));
                                downloadQuestionAnswers(new PrintWriter(file, UTF_16), sessionId, EnumQNAType.SESSION, event, Boolean.TRUE);
                                localFiles.add(file);
                            }
                        }
                    }
                }
            } catch(Exception e) {
                logger.error("Unexpected error in Q&A block", e);
            }
            return localFiles;
        },executor));


        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        List<File> files = futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        List<String> addedFileNames = new ArrayList<>();
        for (File element : files) {
            String fileName = element.getName();
            if (!addedFileNames.contains(fileName)) {
                zipOutputStream.putNextEntry(new ZipEntry(fileName));
                try (FileInputStream fileInputStream = new FileInputStream(element)) { //NOSONAR
                    IOUtils.copy(fileInputStream, zipOutputStream);
                }
                zipOutputStream.closeEntry();
                addedFileNames.add(fileName);
            }
        }
        zipOutputStream.close();
    }

    private void downloadScheduledMeetingsReport(PrintWriter writer, Event event, MeetingStatus status) {
        List<String> headerList = new ArrayList<>();
        String eventTimeZone = TimeZone.getTimeZone(event.getEquivalentTimeZone()).getDisplayName(false, TimeZone.SHORT);
        headerList.add(MEETING_SENDER_NAME);
        headerList.add(SENDER_EMAIL);
        headerList.add(SENDER_COMPANY);
        headerList.add(MEETING_RECEIVER_NAME);
        headerList.add(RECEIVER_EMAIL);
        headerList.add(RECEIVER_COMPANY);
        headerList.add(MEETING_START_DATE_TIME.concat(" (").concat(eventTimeZone).concat(")"));
        headerList.add(MEETING_END_DATE_TIME.concat(" (").concat(eventTimeZone).concat(")"));
        headerList.add(NOTE);
        headerList.add(EQUIVALENT_TIMEZONE);
        headerList.add(SCHEDULED_BY);
        headerList.add(IS_ADMIN);
        if (MeetingStatus.DECLINED.equals(status)){
            headerList.add(DECLINED_BY);
        }
        List<MeetingSchedule> meetingScheduleList=meetingScheduleRepoService.findScheduledMeetingByEventId(event.getEventId());
        List<List<String>> dataRowList = new ArrayList<>();
        Set<Long> userIds = meetingScheduleList.stream()
                .map(MeetingSchedule::getReceiverUserId)
                .collect(Collectors.toSet());

        userIds.addAll(meetingScheduleList.stream().map(MeetingSchedule::getSenderUserId).collect(Collectors.toSet()));

        Map<Long, User> usersMap = roUserService.getListOfUsersByUserIds(new ArrayList<>(userIds)).stream().collect(Collectors.toMap(User::getUserId, user -> user));
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllAttendeeProfiles(event.getEventId());
        Map<Long, AttendeeProfileDto> attendeeProfileMap = attendeeProfileDtos.stream().collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(), (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));
        for (MeetingSchedule meetingSchedule : meetingScheduleList) {
            if (meetingSchedule.getStatus().equals(status)) {
                List<String> dataList = new ArrayList<>();
                User senderUser = usersMap.get(meetingSchedule.getSenderUserId());
                User receiverUser = usersMap.get(meetingSchedule.getReceiverUserId());
                AttendeeProfileDto senderProfileDto = attendeeProfileMap.get(senderUser.getUserId());
                AttendeeProfileDto receiverProfileDto = attendeeProfileMap.get(receiverUser.getUserId());
                dataList.add(senderUser.getFirstName() + " " + senderUser.getLastName());
                dataList.add(senderUser.getEmail());
                dataList.add(null != senderProfileDto ? senderProfileDto.getCompany() : STRING_EMPTY);
                dataList.add(receiverUser.getFirstName() + " " + receiverUser.getLastName());
                dataList.add(receiverUser.getEmail());
                dataList.add(null != receiverProfileDto ? receiverProfileDto.getCompany() : STRING_EMPTY);
                dataList.add(dateFormatter.format(getDateInLocal(meetingSchedule.getMeetingStartTime(), event.getEquivalentTimeZone())));
                dataList.add(dateFormatter.format(getDateInLocal(meetingSchedule.getMeetingEndTime(), event.getEquivalentTimeZone())));
                dataList.add(meetingSchedule.getNote());
                dataList.add(meetingSchedule.getEquivalentTimeZone());
                dataList.add(meetingSchedule.getCreatedByUser() != null ? meetingSchedule.getCreatedByUser().getFirstName() + " " + meetingSchedule.getCreatedByUser().getLastName() : EMPTY);
                dataList.add(MeetingOrigin.ADMIN_MEETING_REQUEST.equals(meetingSchedule.getOrigin()) ? "Yes" : "No");
                if (MeetingStatus.DECLINED.equals(status)){
                    dataList.add(userService.getUserEmailById(meetingSchedule.getRejectedBy()));
                }
                dataRowList.add(dataList);
            }
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private File getTempFile(String fileName) throws FileNotFoundException {
        File file = new File(Files.createTempDir(), fileName); //NOSONAR
        file.deleteOnExit();
        return file;
    }

    @Override
    public void getCheckedInUsersDetails(HttpServletResponse response, Event event){
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
        PlanConfig planConfig = eventPlanConfig.getPlanConfig();
        List<UserBasicDto> userBasicDtoList = new ArrayList<>();
        List<RegistrantsUserDetailsDTO> registrantsUserDetailsDTOS=new ArrayList<>();
        List<RegistrantsUserDetailsDTO> otherStaffAndSpeakersUserDetailsDTOS=new ArrayList<>();

        if (planConfig.isLatestPlan()) {
            registrantsUserDetailsDTOS = getUsersDetailsBasedOnRegistrants(event);
            registrantsUserDetailsDTOS = registrantsUserDetailsDTOS.stream().filter(e -> e.getEmail() != null && !(e.getEmail().endsWith(ACCEL_EVENT_DOMAIN)
                    || ADMIN_EMAIL_ID.equals(e.getEmail()) || e.getEmail().endsWith(BRILWORKS_DOMAIN) || e.getEmail().endsWith(ACCEL_EVENT_REMOVED_DOMAIN))).collect(Collectors.toList());
            otherStaffAndSpeakersUserDetailsDTOS=getUsersDetailsBasedOnRegistrantsOtherStaffs(event);
        } else {
            userBasicDtoList = checkInAuditLogRepoService.listBasicUserInfoByEvent(event);
            userBasicDtoList = userBasicDtoList.stream().filter(e -> e.getEmail() != null && !(e.getEmail().endsWith(ACCEL_EVENT_DOMAIN)
                    || ADMIN_EMAIL_ID.equals(e.getEmail()) || e.getEmail().endsWith(BRILWORKS_DOMAIN) || e.getEmail().endsWith(ACCEL_EVENT_REMOVED_DOMAIN))).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(userBasicDtoList) && !planConfig.isLatestPlan()) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NO_ATTENDEES_ARE_CHECKEDIN);
        } else if (CollectionUtils.isEmpty(registrantsUserDetailsDTOS) && planConfig.isLatestPlan()) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NO_REGISTRANTS_REGISTER_YET);
        }
        String fileName = "AttendeeList_"+ event.getEventId() +".csv";

        PrintWriter writer = setHeader(response, fileName);

        List<List<String>> dataRowList = new ArrayList<>();
        if (planConfig.isLatestPlan()) {
            registrantsUserDetailsDTOS.forEach(e -> {
                List<String> dataList = new ArrayList<>();
                if (e != null) {
                    dataList.add(e.getFirstName());
                    dataList.add(e.getLastName());
                    dataList.add(e.getEmail());
                    dataRowList.add(dataList);
                }
            });
            if (!otherStaffAndSpeakersUserDetailsDTOS.isEmpty()) {
                List<String> dataList = new ArrayList<>();
                dataList.add("Other Staffs and Speakers");
                dataList.add("Who does not");
                dataList.add("have tickets");
                dataRowList.add(dataList);
                otherStaffAndSpeakersUserDetailsDTOS.forEach(e -> {
                    List<String> dataList1 = new ArrayList<>();
                    dataList1.add(e.getFirstName());
                    dataList1.add(e.getLastName());
                    dataList1.add(e.getEmail());
                    dataRowList.add(dataList1);
                });
            }
        } else {
            userBasicDtoList.forEach(e -> {
                List<String> dataList = new ArrayList<>();
                if (e != null) {
                    dataList.add(e.getFirstName());
                    dataList.add(e.getLastName());
                    dataList.add(e.getEmail());
                    dataRowList.add(dataList);
                }
            });
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private List<RegistrantsUserDetailsDTO> getUsersDetailsBasedOnRegistrants(Event event) {
        List<RegistrantsUserDetailsDTO> registrantsUserDetailsDTOS = new ArrayList<>();
        List<Object[]> list = eventTicketsRepository.findEventTicketsUniqueUsersIdsByEventId(event.getEventId());
        list.forEach(e -> registrantsUserDetailsDTOS.add(new RegistrantsUserDetailsDTO((long) ((BigInteger) e[0]).intValue(), (String) e[1], (String) e[2], (String) e[3])));
        return registrantsUserDetailsDTOS;
    }

    public List<RegistrantsUserDetailsDTO> getUsersDetailsBasedOnRegistrantsOtherStaffs(Event event) {
        List<RegistrantsUserDetailsDTO> otherStaffAndSpeakersUserDetailsDTOS = new ArrayList<>();
        Map<Long, List<Integer>> eventsUserIdsEventsMap = new HashMap<>();
        List<Integer> aeAndBrilworksUsersIds = userService.findAllUniqueBrilworksAndAccelEventsUserIds();
        List<Object[]> eventsUserIdsByEvents = eventTicketsRepository.findEventTicketsUniqueUsersIdsByEventIds(Collections.singletonList(event.getEventId()));
        eventsUserIdsByEvents.forEach(objects -> {
            Long eventId = ((BigInteger) objects[0]).longValue();
            String userIdsString = (String) objects[1];
            List<Integer> userIds = Arrays.stream(userIdsString.split(STRING_COMMA)).map(Integer::parseInt).collect(Collectors.toList());
            eventsUserIdsEventsMap.put(eventId, userIds);
        });
        Map<Long, List<Integer>> staffsUsersIdsEventsMap = new HashMap<>();
        List<Object[]> staffsUsersIdsByEvents = eventTicketsRepository.findAllUniqueEventStaffsUserIdsByEventIds(Collections.singletonList(event.getEventId()));
        staffsUsersIdsByEvents.forEach(objects -> {
            Long eventId = ((BigInteger) objects[0]).longValue();
            String userIdsString = (String) objects[1];
            List<Integer> userIds = Arrays.stream(userIdsString.split(STRING_COMMA)).map(Integer::parseInt).collect(Collectors.toList());
            staffsUsersIdsEventsMap.put(eventId, userIds);
        });
        Map<Long, List<Integer>> speakersUsersIdsEventsMap = new HashMap<>();
        List<Object[]> speakersUsersIdsByEvents = eventTicketsRepository.findAllUniqueEventSpeakerUserIdsByEventIds(Collections.singletonList(event.getEventId()));
        speakersUsersIdsByEvents.forEach(objects -> {
            Long eventId = ((BigInteger) objects[0]).longValue();
            String userIdsString = (String) objects[1];
            List<Integer> userIds = Arrays.stream(userIdsString.split(STRING_COMMA)).map(Integer::parseInt).collect(Collectors.toList());
            speakersUsersIdsEventsMap.put(eventId, userIds);
        });
        List<Integer> eventUserIdsWithAeAndBrilUserIdsList = eventsUserIdsEventsMap.getOrDefault(event.getEventId(),Collections.emptyList());
        List<Integer> eventUserIdsList = new ArrayList<>();
        eventUserIdsWithAeAndBrilUserIdsList.forEach(e -> {
            if (!aeAndBrilworksUsersIds.contains(e)) {
                eventUserIdsList.add(e);
            }
        });
        List<Integer> eventStaffUsersIdsList = staffsUsersIdsEventsMap.getOrDefault(event.getEventId(),Collections.emptyList());
        List<Integer> eventSpeakerUsersIdsList = speakersUsersIdsEventsMap.getOrDefault(event.getEventId(),Collections.emptyList());
        List<Integer> allStaffsAndSpeakersUsersIdsList = new ArrayList<>();
        HashSet<Integer> allStaffsAndSpeakersUsersIdsWithAeAndBrilUserIdsList = new HashSet<>(eventStaffUsersIdsList);
        allStaffsAndSpeakersUsersIdsWithAeAndBrilUserIdsList.addAll(eventSpeakerUsersIdsList);
        allStaffsAndSpeakersUsersIdsWithAeAndBrilUserIdsList.forEach(data -> {
            if (!aeAndBrilworksUsersIds.contains(data)) {
                allStaffsAndSpeakersUsersIdsList.add(data);
            }
        });
        List<Integer> allStaffsAndSpeakersWhoHaveTickets = eventUserIdsList.stream().filter(allStaffsAndSpeakersUsersIdsList::contains).collect(Collectors.toList());
        List<Integer> allStaffsAndSpeakersUsersIdsWhoDoesNotHaveTickets = allStaffsAndSpeakersWhoHaveTickets.isEmpty() ? allStaffsAndSpeakersUsersIdsList : allStaffsAndSpeakersUsersIdsList.stream().filter(e -> !allStaffsAndSpeakersWhoHaveTickets.contains(e)).collect(Collectors.toList());
        List<Long> listOfUsersIds = allStaffsAndSpeakersUsersIdsWhoDoesNotHaveTickets.stream().map(Long::valueOf).collect(Collectors.toList());
        userService.findByUserIdIn(listOfUsersIds).forEach(user -> otherStaffAndSpeakersUserDetailsDTOS.add(new RegistrantsUserDetailsDTO(user.getUserId(), user.getFirstName(), user.getLastName(), user.getEmail())));
        return otherStaffAndSpeakersUserDetailsDTOS;
    }
    public List<String> getHeaderListBasedOnPlan(boolean isLatestPlan) {
        List<String> headerList = new ArrayList<>();
        headerList.add("Event ID");
        headerList.add("Event URL");
        headerList.add("Event End Date");
        headerList.add("Platform Plan");
        headerList.add(!isLatestPlan ? "Unique Attendees" : "Total Registrants");
        if (!isLatestPlan) {
            headerList.add("Number of Event Days");
            headerList.add("Total Attendee Days");
        }
        headerList.add(!isLatestPlan ? "Attendee Day Credits from Platform Plan" : "Registrant Credits Used from Platform Plan");
        headerList.add(!isLatestPlan ? "Plan Attendee Day Credits Remaining" : "Registrant Credits Remaining from Platform Plan");
        headerList.add(!isLatestPlan ? "Attendee Overages to be Invoiced" : "Additional Registrant Usage to be Invoiced");
        headerList.add("Invoice ID If Invoiced");
        return headerList;
    }
    @Override
    public void getAttendeeOverageByOrganizer(HttpServletResponse response, Long organizerId){
        List<EventCreditDistributionDto> eventCreditDistributionDtoList=new ArrayList<>();
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        PlanConfig planConfig = organizer.getPlanConfig();
        List<String> headerList = getHeaderListBasedOnPlan(planConfig.isLatestPlan());
        try {
            if (planConfig.isLatestPlan()) {
                eventCreditDistributionDtoList = creditDistributionBasedOnRegistrants(null, organizer);
            } else {
                eventCreditDistributionDtoList = organizerService.organizerCreditDistributionBasedOnEventDays(organizer);
            }
        } catch (Exception error) {
            logger.info("getAttendeeOverageByOrganizer organiserId {} error {}", organizer.getId(), error.getMessage());
        }
        if (CollectionUtils.isEmpty(eventCreditDistributionDtoList)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NO_USAGE_FOR_ORGANIZER);
        }
        PrintWriter writer = setHeader(response, "UsageReport_".concat(organizer.getName()).concat(".csv"));

        List<List<String>> dataRowList = new ArrayList<>();
        eventCreditDistributionDtoList.forEach(e -> {
            List<String> dataList = new ArrayList<>();
            if (e != null) {
                dataList.add(e.getEventId().toString());
                dataList.add(e.getEventURL());
                dataList.add(new SimpleDateFormat("MM/dd/yyyy").format(e.getEventEndDate()));
                dataList.add(e.getEventPlan());
                dataList.add(e.getAttendeeCount().toString());
                if (!planConfig.isLatestPlan()) {
                    dataList.add(e.getEventDays().toString());
                    dataList.add(e.getFinalAttendeeDays().toString());
                }
                dataList.add(e.getFreeCredits().toString());
                dataList.add(e.getRemainingAttendeeCount().toString());
                dataList.add(e.getPaidCredits().toString());
                if (e.getInvoiceId() != null) {
                    dataList.add(e.getInvoiceId());
                } else {
                    dataList.add(" ");
                }
                dataRowList.add(dataList);
            }
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
    }
    @Override
    public void getAttendeeOverageByOrganizerDataFix(HttpServletResponse response, Long organizerId){
        List<EventCreditDistributionDto> eventCreditDistributionDtoList=new ArrayList<>();
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        PlanConfig planConfig = organizer.getPlanConfig();
        List<String> headerList = getHeaderListBasedOnPlan(planConfig.isLatestPlan());
        headerList.add("TotalEventTickets");
        headerList.add("OtherStaffs");
        try {
            if (planConfig.isLatestPlan()) {
                eventCreditDistributionDtoList = creditDistributionBasedOnRegistrantsDataFix(null, organizer);
            } else {
                eventCreditDistributionDtoList = organizerService.organizerCreditDistributionBasedOnEventDays(organizer);
            }
        } catch (Exception error) {
            logger.info("getAttendeeOverageByOrganizer organiserId {} error {}", organizer.getId(), error.getMessage());
        }
        if (CollectionUtils.isEmpty(eventCreditDistributionDtoList)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NO_USAGE_FOR_ORGANIZER);
        }
        PrintWriter writer = setHeader(response, "UsageReport_".concat(organizer.getName()).concat(".csv"));

        List<List<String>> dataRowList = new ArrayList<>();
        eventCreditDistributionDtoList.forEach(e -> {
            List<String> dataList = new ArrayList<>();
            if (e != null) {
                dataList.add(e.getEventId().toString());
                dataList.add(e.getEventURL());
                dataList.add(new SimpleDateFormat("MM/dd/yyyy").format(e.getEventEndDate()));
                dataList.add(e.getEventPlan());
                dataList.add(e.getAttendeeCount().toString());
                if (!planConfig.isLatestPlan()) {
                    dataList.add(e.getEventDays().toString());
                    dataList.add(e.getFinalAttendeeDays().toString());
                }
                dataList.add(e.getFreeCredits().toString());
                dataList.add(e.getRemainingAttendeeCount().toString());
                dataList.add(e.getPaidCredits().toString());
                if (e.getInvoiceId() != null) {
                    dataList.add(e.getInvoiceId());
                } else {
                    dataList.add(" ");
                }
                dataList.add(e.getFinalAttendeeDays().toString());
                dataList.add(e.getEventDays().toString());
                dataRowList.add(dataList);
            }
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    public List<EventCreditDistributionDto> creditDistributionBasedOnRegistrants(WhiteLabel whiteLabel,Organizer organizer)
    {
        logger.info("creditDistributionBasedOnRegistrants organizerId {} whiteLabelId {} ", organizer != null ? organizer.getId() : null, whiteLabel != null ? whiteLabel.getId() : null);//NOSONAR
        List<EventCreditDistributionDto> creditDistributionDtoList = new ArrayList<>();
        List<EventNameLookUpDto> eventNameLookUpDtoList = new ArrayList<>();
        Long prePurchasedQuantity = 0L;
        PlanConfig planConfig = null;
        Integer planFreeQuantity=0;

        if (whiteLabel != null) {
            planConfig = whiteLabel.getPlanConfig();
            planFreeQuantity = whiteLabel.getFreeQuantity() != null ? whiteLabel.getFreeQuantity() : planConfig.getFreeQuantity();
            logger.info("creditDistributionBasedOnRegistrants before find whiteLabel {} prePurchasedQuantity subscriptionId {}",whiteLabel.getId(),whiteLabel.getSubscriptionId());
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(null, whiteLabel.getId(), whiteLabel.getSubscriptionId());
            eventNameLookUpDtoList = eventRepoService.getAllEventsByOrganizerIdOrWhiteLabelIdAndSubscriptionId(whiteLabel.getId(),null, whiteLabel.getSubscriptionId());
        } else if (organizer != null) {
            planConfig = organizer.getPlanConfig();
            planFreeQuantity = organizer.getFreeQuantity() != null ? organizer.getFreeQuantity() : planConfig.getFreeQuantity();
            logger.info("creditDistributionBasedOnRegistrants before find organizer {} prePurchasedQuantity subscriptionId {}",organizer.getId(),organizer.getSubscriptionId());
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(organizer.getId(), null, organizer.getSubscriptionId());
            eventNameLookUpDtoList = eventRepoService.getAllEventsByOrganizerIdOrWhiteLabelIdAndSubscriptionId(null,organizer.getId(), organizer.getSubscriptionId());
        }
        logger.info("creditDistributionBasedOnRegistrants prePurchasedQuantity {} whiteLabelId {} organiserId {}",prePurchasedQuantity,whiteLabel!=null?whiteLabel.getId():null,organizer!=null?organizer.getId():null);
        String planName = planConfig != null ? planConfig.getDisplayPlanName() : STRING_EMPTY;
        if (eventNameLookUpDtoList.isEmpty()) {
            return creditDistributionDtoList;
        }
        List<Long> eventIdList = new ArrayList<>();
        Map<Long, String> eventUrlMap = createEventUrlMap(eventNameLookUpDtoList);
        eventNameLookUpDtoList.forEach(event -> eventIdList.add(event.getEventId()));
        List<ChargebeeEventUsages> eventChargeUsagesByEventIds = chargebeeEventUsagesRepoService.findByEventIds(eventIdList);
        Map<Long, Ticketing> listOfTicketing = ticketingService.findTicketingByEventIds(eventIdList);
        Map<Long, Integer> eventTicketMapByEventIds = eventTicketsService.findAllNonDeletedAndNonDonationTypeAndTypeTicketByEventIds(eventIdList);
        Map<Long, Integer> eventDistinctStaffsAndSpeakersCountWhoDoesNotHaveTickets = eventTicketsService.findEventsUniqueStaffsAndSpeakersCount(eventIdList,false);
        Map<Long,List<ChargebeeEventUsages>> eventChargeUsagesByEventIdMap=eventChargeUsagesByEventIds.stream().collect(Collectors.groupingBy(ChargebeeEventUsages::getEventId));
        AtomicLong organiserOrWlTotalCredits = new AtomicLong(planFreeQuantity + prePurchasedQuantity);
        logger.info("creditDistributionBasedOnRegistrants organiserOrWlTotalCredits {} whiteLabelId {} organiserId {}",organiserOrWlTotalCredits,whiteLabel!=null?whiteLabel.getId():null,organizer!=null?organizer.getId():null);
        eventIdList.forEach(eventId->{
            List<ChargebeeEventUsages> eventUsagesByEvent = eventChargeUsagesByEventIdMap.get(eventId);
            if (!eventUsagesByEvent.isEmpty())
            {
                EventCreditDistributionDto eventCreditDistributionDto = new EventCreditDistributionDto();
                eventCreditDistributionDto.setEventId(eventId);
                eventCreditDistributionDto.setEventPlan(planName);
                eventCreditDistributionDto.setEventURL(eventUrlMap.get(eventId)!=null?eventUrlMap.get(eventId):STRING_EMPTY);
                Long freeCredits = 0L;
                Long paidCredits = 0L;
                boolean invoiced = false;
                for (ChargebeeEventUsages eventChargeUsage : eventUsagesByEvent) {
                    if (UsagesType.PAID.equals(eventChargeUsage.getCreditType())) {
                        if (eventChargeUsage.getInvoice() != null && !invoiced && !eventChargeUsage.getInvoice().startsWith("draft")) {
                            eventCreditDistributionDto.setInvoiceId("https://" + chargebeeConfiguration.getChargebeeSite() + ".chargebee.com/d/invoices/" + eventChargeUsage.getInvoice());
                            invoiced = true;
                        }
                        paidCredits += eventChargeUsage.getQuantity();
                        organiserOrWlTotalCredits.addAndGet(-eventChargeUsage.getQuantity());
                    } else {
                        freeCredits += eventChargeUsage.getQuantity();
                        organiserOrWlTotalCredits.addAndGet(-eventChargeUsage.getQuantity());
                    }
                }
                eventCreditDistributionDto.setPaidCredits(paidCredits);
                eventCreditDistributionDto.setFreeCredits(freeCredits);
                eventCreditDistributionDto.setRemainingAttendeeCount(organiserOrWlTotalCredits.get() > 0 ? organiserOrWlTotalCredits.get() : 0);
                Ticketing ticketing = listOfTicketing.get(eventId);
                eventCreditDistributionDto.setEventEndDate(ticketing.getEventEndDate());
                eventCreditDistributionDto.setAttendeeCount((long) (eventTicketMapByEventIds.get(eventId) != null ? eventTicketMapByEventIds.get(eventId) : 0) + (long)(eventDistinctStaffsAndSpeakersCountWhoDoesNotHaveTickets != null ? eventDistinctStaffsAndSpeakersCountWhoDoesNotHaveTickets.get(eventId) : 0));
                logger.info("creditDistributionBasedOnRegistrants organiserId {} whiteLabelId {} eventCreditDistributionDto {}",organizer!=null?organizer.getId():null,whiteLabel!=null?whiteLabel.getId():null, eventCreditDistributionDto);
                creditDistributionDtoList.add(eventCreditDistributionDto);
            }
        });
        return creditDistributionDtoList;
    }
    public List<EventCreditDistributionDto> creditDistributionBasedOnRegistrantsDataFix(WhiteLabel whiteLabel,Organizer organizer)
    {
        logger.info("creditDistributionBasedOnRegistrants organizerId {} whiteLabelId {} ", organizer != null ? organizer.getId() : null, whiteLabel != null ? whiteLabel.getId() : null);//NOSONAR
        List<EventCreditDistributionDto> creditDistributionDtoList = new ArrayList<>();
        List<EventNameLookUpDto> eventNameLookUpDtoList = new ArrayList<>();
        Long prePurchasedQuantity = 0L;
        PlanConfig planConfig = null;

        if (whiteLabel != null) {
            planConfig = whiteLabel.getPlanConfig();
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(null, whiteLabel.getId(), whiteLabel.getSubscriptionId());
            eventNameLookUpDtoList = eventRepoService.getAllEventsByOrganizerIdOrWhiteLabelIdAndSubscriptionId(whiteLabel.getId(),null, whiteLabel.getSubscriptionId());
        } else if (organizer != null) {
            planConfig = organizer.getPlanConfig();
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(organizer.getId(), null, organizer.getSubscriptionId());
            eventNameLookUpDtoList = eventRepoService.getAllEventsByOrganizerIdOrWhiteLabelIdAndSubscriptionId(null,organizer.getId(), organizer.getSubscriptionId());
        }
        String planName = planConfig != null ? planConfig.getDisplayPlanName() : STRING_EMPTY;
        if (eventNameLookUpDtoList.isEmpty()) {
            return creditDistributionDtoList;
        }
        List<Long> eventIdList = new ArrayList<>();
        Map<Long, String> eventUrlMap = createEventUrlMap(eventNameLookUpDtoList);
        eventNameLookUpDtoList.forEach(event -> eventIdList.add(event.getEventId()));
        List<ChargebeeEventUsages> eventChargeUsagesByEventIds = chargebeeEventUsagesRepoService.findByEventIds(eventIdList);
        Map<Long, Ticketing> listOfTicketing = ticketingService.findTicketingByEventIds(eventIdList);
        Map<Long, Integer> eventTicketMapByEventIds = eventTicketsService.findAllNonDeletedAndNonDonationTypeAndTypeTicketByEventIds(eventIdList);
        Map<Long, Integer> staffExhibitorAdminSpeakerCountMap = eventTicketsService.findEventsUniqueStaffsAndSpeakersCount(eventIdList,false);
        Map<Long,List<ChargebeeEventUsages>> eventChargeUsagesByEventIdMap=eventChargeUsagesByEventIds.stream().collect(Collectors.groupingBy(ChargebeeEventUsages::getEventId));
        AtomicLong organiserCredits = new AtomicLong((planConfig != null ? planConfig.getFreeQuantity() : 0) + prePurchasedQuantity);
        eventIdList.forEach(eventId->{
            List<ChargebeeEventUsages> eventUsagesByEvent = eventChargeUsagesByEventIdMap.get(eventId);
            if (!eventUsagesByEvent.isEmpty())
            {
                EventCreditDistributionDto eventCreditDistributionDto = new EventCreditDistributionDto();
                eventCreditDistributionDto.setEventId(eventId);
                eventCreditDistributionDto.setEventPlan(planName);
                eventCreditDistributionDto.setEventURL(eventUrlMap.get(eventId)!=null?eventUrlMap.get(eventId):STRING_EMPTY);
                Long freeCredits = 0L;
                Long paidCredits = 0L;
                boolean invoiced = false;
                for (ChargebeeEventUsages eventChargeUsage : eventUsagesByEvent) {
                    if (UsagesType.PAID.equals(eventChargeUsage.getCreditType())) {
                        if (eventChargeUsage.getInvoice() != null && !invoiced && !eventChargeUsage.getInvoice().startsWith("draft")) {
                            eventCreditDistributionDto.setInvoiceId("https://" + chargebeeConfiguration.getChargebeeSite() + ".chargebee.com/d/invoices/" + eventChargeUsage.getInvoice());
                            invoiced = true;
                        }
                        paidCredits += eventChargeUsage.getQuantity();
                        organiserCredits.addAndGet(-eventChargeUsage.getQuantity());
                    } else {
                        freeCredits += eventChargeUsage.getQuantity();
                        organiserCredits.addAndGet(-eventChargeUsage.getQuantity());
                    }
                }
                eventCreditDistributionDto.setPaidCredits(paidCredits);
                eventCreditDistributionDto.setFreeCredits(freeCredits);
                eventCreditDistributionDto.setRemainingAttendeeCount(organiserCredits.get() > 0 ? organiserCredits.get() : 0);
                Ticketing ticketing = listOfTicketing.get(eventId);
                eventCreditDistributionDto.setEventEndDate(ticketing.getEventEndDate());
                Integer otherStaffsAndExpoAndSpeakers = staffExhibitorAdminSpeakerCountMap.get(eventId);
                eventCreditDistributionDto.setAttendeeCount((long) ((eventTicketMapByEventIds.get(eventId) != null ? eventTicketMapByEventIds.get(eventId) : 0)) + (otherStaffsAndExpoAndSpeakers != null ? otherStaffsAndExpoAndSpeakers : 0));
                eventCreditDistributionDto.setFinalAttendeeDays((long) (eventTicketMapByEventIds.get(eventId) != null ? eventTicketMapByEventIds.get(eventId) : 0));
                eventCreditDistributionDto.setEventDays((long) (otherStaffsAndExpoAndSpeakers != null ? otherStaffsAndExpoAndSpeakers : 0));
                logger.info("creditDistributionBasedOnRegistrants organiserId {} whiteLabelId {} eventCreditDistributionDto {}",organizer!=null?organizer.getId():null,whiteLabel!=null?whiteLabel.getId():null, eventCreditDistributionDto);
                creditDistributionDtoList.add(eventCreditDistributionDto);
            }
        });
        return creditDistributionDtoList;
    }
    public Map<Long, String> createEventUrlMap(List<EventNameLookUpDto> eventNameLookUpDtoList) {
        Map<Long, String> eventUrlMap = new HashMap<>();
        eventNameLookUpDtoList.forEach(event -> eventUrlMap.put(event.getEventId(), event.getEventURL()));
        return eventUrlMap;
    }
    @Override
    public void getAttendeeOveragesByWhitelabel(HttpServletResponse response, WhiteLabel whiteLabel) {
        PlanConfig planConfig = whiteLabel.getPlanConfig();
        List<String> headerList = getHeaderListBasedOnPlan(planConfig.isLatestPlan());

        List<EventCreditDistributionDto> eventCreditDistributionDtoList = null;
        if (planConfig.isLatestPlan()) {
            eventCreditDistributionDtoList = creditDistributionBasedOnRegistrants(whiteLabel, null);
        } else {
            eventCreditDistributionDtoList = whiteLabelService.whitelabelCreditDistributionBasedOnEventDays(whiteLabel);
        }

        if (CollectionUtils.isEmpty(eventCreditDistributionDtoList)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NO_USAGE_FOR_WHITE_LABEL);
        }

        PrintWriter writer = setHeader(response, "UsageReport_".concat(whiteLabel.getFirmName()).concat(".csv"));

        List<List<String>> dataRowList = new ArrayList<>();
        eventCreditDistributionDtoList.forEach(e -> {
            List<String> dataList = new ArrayList<>();
            if (e != null) {
                dataList.add(e.getEventId().toString());
                dataList.add(e.getEventURL());
                dataList.add(new SimpleDateFormat("MM/dd/yyyy").format(e.getEventEndDate()));
                dataList.add(e.getEventPlan());
                dataList.add(e.getAttendeeCount().toString());
                if (!planConfig.isLatestPlan()) {
                    dataList.add(e.getEventDays().toString());
                    dataList.add(e.getFinalAttendeeDays().toString());
                }
                dataList.add(e.getFreeCredits().toString());
                dataList.add(e.getRemainingAttendeeCount().toString());
                dataList.add(e.getPaidCredits().toString());
                if (e.getInvoiceId() != null) {
                    dataList.add(e.getInvoiceId());
                } else {
                    dataList.add(" ");
                }
                dataRowList.add(dataList);
            }
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadAttendeeDetails(User user, Event event, HttpServletResponse response) { //NOSONAR
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllAttendeesDetailsWithInterests(String.valueOf(event.getEventId()));

        String fileName = "Attendees Details of "+ event.getName() +".csv";
        PrintWriter writer = setHeader(response, fileName);
        if (null != attendeeProfileDtos) {
            List<String> headerList = new ArrayList<>();
            headerList.add(HEADER.USER_ID);
            headerList.add(PRONOUNS);
            headerList.add(TITLE);
            headerList.add(COMPANY);
            headerList.add(INDUSTRY);
            headerList.add(ABOUT_ME);
            headerList.add(WANTS_TO_MEET);
            headerList.add(WANTS_TO_LEARN);
            headerList.add(KNOWS);
            headerList.add(INTEREST);

            List<List<String>> dataRowList = new ArrayList<>();
            for (AttendeeProfileDto attendeeProfileDto : attendeeProfileDtos) {
                List<String> dataList = new ArrayList<>();
                dataList.add(String.valueOf(attendeeProfileDto.getUserId()));
                dataList.add(attendeeProfileDto.getPronouns() !=null ? attendeeProfileDto.getPronouns() : STRING_EMPTY);
                dataList.add(attendeeProfileDto.getTitle() != null ? attendeeProfileDto.getTitle() : STRING_EMPTY);
                dataList.add(attendeeProfileDto.getCompany() != null ? attendeeProfileDto.getCompany() : STRING_EMPTY);
                dataList.add(attendeeProfileDto.getIndustry() != null ? attendeeProfileDto.getIndustry() : STRING_EMPTY);
                dataList.add(attendeeProfileDto.getExtraInfo() != null ? defaultString(attendeeProfileDto.getExtraInfo().getAboutMe()): STRING_EMPTY);
                dataList.add(attendeeProfileDto.getExtraInfo() != null ? defaultString(attendeeProfileDto.getExtraInfo().getWantsToMeet()) : STRING_EMPTY);
                dataList.add(attendeeProfileDto.getExtraInfo() != null ? defaultString(attendeeProfileDto.getExtraInfo().getWantsToLearn()) : STRING_EMPTY);
                dataList.add(attendeeProfileDto.getExtraInfo() != null ? defaultString(attendeeProfileDto.getExtraInfo().getKnows()) : STRING_EMPTY);
                String interest = STRING_EMPTY;
                if(attendeeProfileDto.getInterests()!=null && CollectionUtils.isNotEmpty(attendeeProfileDto.getInterests())){
                    List<InterestDto> interestDtoList = attendeeProfileDto.getInterests();
                    interest = convertListToCommaSeparated(interestDtoList.stream().map(InterestDto::getName).collect(Collectors.toList()));
                }
                dataList.add(interest);
                dataRowList.add(dataList);
            }
            this.downloadCSVFile(writer, headerList, dataRowList);
        }
    }

     @Override
     public void downloadAttendeeAnalyticsCSV(PrintWriter writer, Event event, User user, Long displayViewId,String searchString){
         Map<String, String> columnKeyLabelMap = columnMasterService.getColumnKeyLabelMap(AnalyticsArea.ATTENDEE_ANALYTICS, event);
         AttendeeAnalyticsPageSizeSearchObj attendeeAnalyticsPageSizeSearchObj = new AttendeeAnalyticsPageSizeSearchObj(0,Integer.MAX_VALUE,STRING_EMPTY);
         List<ColumnDto> columnDtoList;
         DataTableResponse dataTableResponse;
         if (displayViewId != null) {
             attendeeAnalyticsPageSizeSearchObj.setDisplayViewId(displayViewId);
             DisplayViewDto displayViewDto = roDisplayViewService.findDisplayViewById(displayViewId, event, user);
             ViewFilterDetailsDTO viewFilterDetailsDto = displayViewDto.getViewFilterDetailsDTO();
             columnDtoList = viewFilterDetailsDto.getEditColumns();
             attendeeAnalyticsPageSizeSearchObj.setSearch(searchString);
             dataTableResponse = roAttendeeAnalyticsService.findAllAttendeeByDisplayViewId(event, user,attendeeAnalyticsPageSizeSearchObj);
             logger.info("columnDto list {} for event {} and displayView Id {}",columnDtoList.size(),event.getEventId(),displayViewId);
         } else {
             UserColumnSelectionDto userColumnSelectionDto = roAnalyticsUserColumnSelectionService.getOrCreateUserColumnSelection(AnalyticsSource.EVENT, event.getEventId(), AnalyticsArea.ATTENDEE_ANALYTICS, user.getUserId(), -1L);
             columnDtoList = userColumnSelectionDto.getColumnSelection();
             dataTableResponse = roAttendeeAnalyticsService.getAllAttendeeByEvent(event, user, attendeeAnalyticsPageSizeSearchObj);
             logger.info("columnDto list {} for event {}",columnDtoList.size(),event.getEventId());
         }
         List<Audience> audienceList = (List<Audience>) dataTableResponse.getData();
         logger.info("audience list {} to download attendee analytics csv for event {}",audienceList.size(),event.getEventId());

         List<String> csvHeader = columnDtoList.stream().map(this::checkIfColumnHeaderContainsNumericKey).map(columnKeyLabelMap::get).collect(Collectors.toList());

         List<List<String>> dataRowList = new ArrayList<>();
         if (CollectionUtils.isNotEmpty(audienceList)) {
             audienceList.forEach(audience -> {
                 List<String> dataList = new ArrayList<>();
                 for (ColumnDto column : columnDtoList) {
                     String key = checkIfColumnHeaderContainsNumericKey(column);
                     dataList.add(getCellValue(key, audience));
                 }
                 dataRowList.add(dataList);
             });
         }
         this.downloadCSVFile(writer, csvHeader, dataRowList);
         logger.info("Successfully prepared attendee analytics CSV, result size {} ", dataRowList.size());
     }

    public String checkIfColumnHeaderContainsNumericKey(ColumnDto columnDto) {
        String key = columnDto.getKey();
        if(ColumnSelectionConstants.ColumnType.HOLDER.equals(columnDto.getType()) || ColumnSelectionConstants.ColumnType.BUYER.equals(columnDto.getType())) {
            key= key.concat(STRING_UNDERSCORE).concat(columnDto.getType().name());
        }
        return key;
    }

    @Deprecated //Moved to RO user there or create it public and take the reference
    private String getCellValue(String column, Audience audience) {
        String value;
        Map<String, Object> columnSelectionData = audience.getColumnSelectionData();
        switch (column) {
            case ColumnSelectionConstants.FULL_NAME:
                value = audience.getName();
                break;
            case ColumnSelectionConstants.FIRST_NAME:
                value = audience.getFirstName();
                break;
            case ColumnSelectionConstants.LAST_NAME:
                value = audience.getLastName();
                break;
            case ColumnSelectionConstants.PHOTO:
                value =audience.getPhoto();
                break;
            case ColumnSelectionConstants.EMAIL:
                value = audience.getEmail();
                break;
            case ColumnSelectionConstants.COMPANY:
                value = audience.getCompany();
                break;
            case ColumnSelectionConstants.JOB_TITLE:
                value = audience.getTitle();
                break;
            case ColumnSelectionConstants.LAST_ACTIVITY:
                value = audience.getLastActivityAt();
                break;
            default:
                Object columnValue = columnSelectionData !=null ? columnSelectionData.get(column) :NOT_APPLICABLE ;
                value = columnValue != null ? columnValue.toString() : NOT_APPLICABLE;
        }
        if(isBlank(value)) {
            value = NOT_APPLICABLE;
        }
        return value;
    }

    @Override
    public void downloadUserTimeLineForParticularAction(PrintWriter writer, List<UserActivity> userActivities, Map<Long, String> userIds, String eventTimezone) {
        if(userActivities != null && !userActivities.isEmpty()){

            List<List<String>> rowData = new ArrayList<>();
            List<String> headers = new ArrayList<>();
            headers.add("User Id");
            headers.add("Email");
            headers.add("Performed Action");
            headers.add(SESSION_TITLE);
            headers.add("Session Type");
            headers.add("Document Name");
            headers.add("Action Performed Time");

            userActivities.forEach(userActivity -> {
                List<String> row = new ArrayList<>();
                row.add(String.valueOf(userActivity.getUserId()));
                row.add(userIds.getOrDefault(userActivity.getUserId(), STRING_EMPTY));
                row.add(String.valueOf(userActivity.getActionType()));
                row.add(String.valueOf(userActivity.getDescription().getOrDefault("sessionName", STRING_EMPTY)));
                row.add(String.valueOf(userActivity.getDescription().getOrDefault("sessionType", STRING_EMPTY)));
                row.add(String.valueOf(userActivity.getDescription().getOrDefault("documentName", STRING_EMPTY)));
                row.add(getDateInLocal(new Date(userActivity.getTimestamp()),eventTimezone,null));
                rowData.add(row);
            });

            this.downloadCSVFile(writer,headers,rowData);
        }
    }

    @Override
    public void downloadContinuingEdAnalyticsCSV(PrintWriter writer, Long criteriaId, ContinuingEdConstants.ContinuingEdStatus status, User loggedInUser, Event event, PageSizeSearchObj pageSizeSearchObj) {
        pageSizeSearchObj = new PageSizeSearchObj(0, Integer.MAX_VALUE, pageSizeSearchObj.getSearch());
        EventCECriteriaDTO criteriaDTO = eventCECriteriaService.getEventCECriteriaDetailsById(criteriaId, event);
        DataTableResponse dataTableResponse = continuingEdService.getContinuingEdAnalytics(criteriaDTO, status, event, loggedInUser, pageSizeSearchObj);
        List<ContinuingEdUserDTO> continuingEdDtos = (List<ContinuingEdUserDTO>) dataTableResponse.getData();

        List<String> csvHeader = new ArrayList<>();
        List<List<String>> dataRowList = new ArrayList<>();

        csvHeader.add(ContinuingEdConstants.ATTENDEE_NAME);
        if(continuingEdService.isContinueEdWithSurveySubmissionAction(criteriaDTO)) {
            setHeader(csvHeader, ContinuingEdConstants.LAST_ACTIVITY_DATE_HEADER,ContinuingEdConstants.CRITERIA, ContinuingEdConstants.COMPLETION_METHOD, ContinuingEdConstants.QUIZ_SCORE, ContinuingEdConstants.CE_CREDIT_LABEL);
            continuingEdDtos.forEach(dto -> dataRowList.add(List.of(
                    dto.getName(),
                    dto.getLastActivityDate(),
                    dto.getChallenge(),
                    ContinuingEdConstants.SURVEY_SUBMISSION,
                    dto.getUserQuizScore() + STRING_PERCENTAGE,
                    String.valueOf(dto.getRewardPoint())
            )));

        } else if(continuingEdService.isContinueEdWithSessionCheckInAction(criteriaDTO)){
            setHeader(csvHeader,ContinuingEdConstants.LAST_ACTIVITY_DATE_HEADER, ContinuingEdConstants.CRITERIA, ContinuingEdConstants.COMPLETION_METHOD, ContinuingEdConstants.CE_CREDIT_LABEL, ContinuingEdConstants.PROGRESS);
            continuingEdDtos.forEach(dto -> dataRowList.add(List.of(
                    dto.getName(),
                    dto.getLastActivityDate(),
                    dto.getChallenge(),
                    ContinuingEdConstants.SESSION_CHECK_IN,
                    String.valueOf(dto.getRewardPoint()),
                    String.valueOf(dto.getProgress())
            )));
        }else if(continuingEdService.isContinueEdWithSessionCheckInAndSubmitSurveyAction(criteriaDTO)){
            setHeader(csvHeader, ContinuingEdConstants.LAST_ACTIVITY_DATE_HEADER, ContinuingEdConstants.CRITERIA, ContinuingEdConstants.COMPLETION_METHOD, ContinuingEdConstants.CE_CREDIT_LABEL, ContinuingEdConstants.PROGRESS);
            continuingEdDtos.forEach(dto -> dataRowList.add(List.of(
                    dto.getName(),
                    dto.getLastActivityDate(),
                    dto.getChallenge(),
                    ContinuingEdConstants.SESSION_CHECK_IN_AND_SURVEY_COMPLETION,
                    String.valueOf(dto.getRewardPoint()),
                    String.valueOf(dto.getProgress())
            )));
        }
        else {
            setHeader(csvHeader,  ContinuingEdConstants.LAST_ACTIVITY_DATE_HEADER,ContinuingEdConstants.CRITERIA, ContinuingEdConstants.COMPLETION_METHOD, ContinuingEdConstants.PROGRESS);
            continuingEdDtos.forEach(dto -> dataRowList.add(List.of(
                    dto.getName(),
                    dto.getLastActivityDate(),
                    dto.getChallenge(),
                    ContinuingEdConstants.SESSION_WATCH,
                    String.valueOf(dto.getProgress())
            )));
        }
        this.downloadCSVFile(writer, csvHeader, dataRowList);
    }

    private void setHeader(List<String> header, String... fields) {
        header.addAll(Arrays.asList(fields));
    }

    @Override
    public void downloadContinuingEdUserAnalyticsCSV(PrintWriter writer, Long criteriaId, User user, Event event) {
        List<ContinuingEdProgressDTO> continuingEdByUser = continuingEdService.getContinuingEdAnalyticsByUser(criteriaId, user, event);

        boolean addChallengeColumn = !NumberUtils.isNumberGreaterThanZero(criteriaId);
        List<String> csvHeader = new ArrayList<>();
        csvHeader.add(ContinuingEdConstants.SESSION_NAME);
        if (addChallengeColumn) {
            csvHeader.add(ContinuingEdConstants.CRITERIA);
        }
        csvHeader.add(ContinuingEdConstants.COMPLETION_METHOD);
        csvHeader.add(ContinuingEdConstants.PROGRESS);

        List<List<String>> dataRowList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(continuingEdByUser)) {
            for (ContinuingEdProgressDTO progressDTO : continuingEdByUser) {
                List<String> dataList = new ArrayList<>();
                dataList.add(progressDTO.getSessionName());
                if (addChallengeColumn) {
                    dataList.add(progressDTO.getChallengeName());
                }
                dataList.add(progressDTO.getCompletionMethod());
                dataList.add(String.valueOf(progressDTO.getProgress()));
                dataRowList.add(dataList);
            }
        }
        this.downloadCSVFile(writer, csvHeader, dataRowList);
    }


    @Override
    public void downloadContinuingEdUserAnalyticsPDF(Long criteriaId, User user, Event event,HttpServletResponse response) throws IOException {
        logger.info("downloadContinuingEdUserAnalyticsPDF | userId: {} : eventId: {} criteria id: {}",user.getUserId(),event.getEventId(), criteriaId);
        List<ContinuingEdProgressDTO> continuingEdByUser = continuingEdService.getContinuingEdAnalyticsByUser(criteriaId, user, event);
        String criteriaName;
        if (NumberUtils.isNumberGreaterThanZero(criteriaId)) {
           EventCECriteriaDTO criteriaDTO = eventCECriteriaService.getEventCECriteriaDetailsById(criteriaId, event);
            criteriaName = criteriaDTO.getName();
        } else {
            criteriaName = "All criteria";
        }
        String fileName = user.getFirstName() + STRING_DASH + user.getLastName()+STRING_DASH+ criteriaName;
        CommonUtil.prepareDownloadableResponseHeader(response, fileName, APPLICATION_PDF );
        AttendeeProfileDto attendeeProfileDto = attendeeProfileService.getAttendeeProfile(user);
        logger.info("downloadContinuingEdUserAnalyticsPDF | attendeeProfile dto: {}",attendeeProfileDto);
        OutputStream servletOutputStream = response.getOutputStream();
        String profilePhoto = attendeeProfileService.getUserProfilePhoto(attendeeProfileDto.getPhoto());
        String date = TimeZoneUtil.getFormattedDate(new Date(), "dd/MM/yyyy, hh:mm:ss a");
        try (OutputStream pdfOutputStream = edChallengePdfCreator.createEdChallengePDFOutputStream(continuingEdByUser, attendeeProfileDto, criteriaName, date,
                profilePhoto, servletOutputStream)) {
            pdfOutputStream.flush();
        } catch (Exception ex) {
            logger.error("downloadContinuingEdUserAnalyticsPDF => {}", ex.getMessage());
        }
    }

    @Override
    public void downloadAllSurveys(User user, Event event, List<SurveyConfigDto> surveyConfigDtos, Map<Long, Long> countBySurveys, HttpServletResponse response) {
        logger.info("downloadAllSurveys | user {} | event {}", user.getUserId(), event.getEventId());
        String fileName = "All Surveys-" + event.getName() + ".csv";
        PrintWriter printWriter = setHeader(response, fileName);

        List<String> headers = new ArrayList<>();
        headers.add(SURVEY_NAME);
        headers.add(SURVEY_TITLE);
        headers.add(DESCRIPTION);
        headers.add(SURVEY_HEADLINE);
        headers.add(SURVEY_FEEDBACK_MESSAGE);
        headers.add(SURVEY_QUESTIONS);
        headers.add(SUBMISSIONS);

        List<List<String>> dataRowList = new ArrayList<>();
        surveyConfigDtos.forEach(surveyConfigDto -> {
            List<String> dataList = new ArrayList<>();
            dataList.add(surveyConfigDto.getSurveyName());
            dataList.add(surveyConfigDto.getSurveyTitle());
            dataList.add(surveyConfigDto.getDescription() != null ? surveyConfigDto.getDescription() : STRING_EMPTY);
            dataList.add(surveyConfigDto.getSurveyHeadline());
            dataList.add(surveyConfigDto.getSurveyFeedbackMessage() != null ? surveyConfigDto.getSurveyFeedbackMessage() : STRING_EMPTY);
            List<Map<String, Object>> surveyQuestionList = new ArrayList<>();
            surveyConfigDto.getSurveyQuestions().forEach(surveyQuestionsDto -> {
                Map<String, Object> surveyQuestions = new HashMap<>();
                surveyQuestions.put(QUESTION_TITLE, surveyQuestionsDto.getQuestionTitle());
                surveyQuestions.put(IS_REQUIRED, surveyQuestionsDto.isRequired());
                surveyQuestions.put(TYPE, surveyQuestionsDto.getType());
                surveyQuestions.put(POSITION, surveyQuestionsDto.getPosition());
                surveyQuestions.put(ALLOWED_DESCRIPTION, surveyQuestionsDto.isAllowedDescription());
                surveyQuestions.put(OPTIONS, surveyQuestionsDto.getOptions() != null ? surveyQuestionsDto.getOptions() : EMPTY);
                surveyQuestionList.add(surveyQuestions);
            });
            dataList.add(JsonMapper.convertToString(surveyQuestionList));
            dataList.add(String.valueOf(countBySurveys.getOrDefault(surveyConfigDto.getSurveyId(),0L)));
            dataRowList.add(dataList);
        });
        logger.info("Start Download CSV file for the surveys configuration | user {} | event {}", user.getUserId(),event.getEventId());
        this.downloadCSVFile(printWriter, headers, dataRowList);

    }

    @Override
    public void downloadSurveySubmissions(List<SurveyResponse> surveyResponses, SurveyConfiguration surveyConfiguration, User user, Event event, HttpServletResponse response) {
        logger.info("downloadSurveySubmissions | user {} | event {} | survey id {}", user.getUserId(), event.getEventId(), surveyConfiguration.getSurveyId());
        String fileName = "Survey Result of " + surveyConfiguration.getSurveyName() + ".csv";
        PrintWriter printWriter = setHeader(response, fileName);
        List<String> headers = new ArrayList<>();
        headers.add(SURVEY_NAME);
        headers.add(TRIGGER);
        headers.add(SUBMISSION_TIMESTAMP);
        headers.add(EMAIL);
        headers.add(FIRST_NAME);
        headers.add(LAST_NAME);
        List<SurveyQuestions> surveyQuestions = surveyQuestionsRepoService.findBySurveyId(surveyConfiguration.getSurveyId());
        Map<Long, Integer> surveyQuestionsTitleMap = new HashMap<>();
        Map<Long, Integer> questionDescription = new HashMap<>();
        int count = 6;
        for(SurveyQuestions surveyQuestion : surveyQuestions) {
            headers.add(surveyQuestion.getQuestionTitle());
            surveyQuestionsTitleMap.put(surveyQuestion.getId(), count);
            count++;
            if(surveyQuestion.isAllowedDescription()){
                headers.add(surveyQuestion.getQuestionTitle() + " (" +DESCRIPTION+ ")");
                questionDescription.put(surveyQuestion.getId(),count);
                count++;
            }
        }
        headers.add(SESSION_ID);
        headers.add(SESSION_TITLE);
        logger.info("surveyQuestionsTitleMap {} | questionDescription {}",surveyQuestionsTitleMap,questionDescription);
        List<List<String>> dataRowList = prepareSurveySubmissionData(surveyResponses,surveyConfiguration.getSurveyName(),event,surveyQuestionsTitleMap,questionDescription,headers);
            logger.info("Start Download CSV file for the survey submission | user {} | event {}", user.getUserId(),event.getEventId());
            this.downloadCSVFile(printWriter, headers, dataRowList);
    }

    @Override
    public void downloadSurveySubmissionsData(List<SurveysDto> surveysDtoList, Event event, HttpServletResponse response) {
        String fileName = "Survey Result " + event.getEventId() + ".csv";
        PrintWriter printWriter = setHeader(response, fileName);
        Map<String, List<String>> surveyHeadersMap = new HashMap<>();
        Map<String, List<List<String>>> surveyDataRowsMap = new HashMap<>();
        List<SurveyQuestions> surveyQuestions = new ArrayList<>();
        List<SurveyResponse> surveyResponses = new ArrayList<>();
        List<List<String>> dataRowList = new ArrayList<>();
        surveyQuestions = surveyQuestionsRepoService.findByEventId(event.getEventId());
        surveyResponses = surveyResponseRepoService.findAllByEventId(event.getEventId());

        for (SurveysDto surveysDto : surveysDtoList){
            List<SurveyQuestions> surveyQuestionsList = surveyQuestions.stream().filter(surveyQuestions1 -> surveysDto.getSurveyId() == surveyQuestions1.getSurveyId()).collect(toList());
            List<SurveyResponse> surveyResponseList = surveyResponses.stream().filter(surveyResponse -> surveysDto.getSurveyId() == surveyResponse.getSurveyId()).collect(toList());
            int count = 6;
            List<String> headers = new ArrayList<>();
            headers.add(SURVEY_NAME);
            headers.add(TRIGGER);
            headers.add(SUBMISSION_TIMESTAMP);
            headers.add(EMAIL);
            headers.add(FIRST_NAME);
            headers.add(LAST_NAME);
            Map<Long, Integer> surveyQuestionsTitleMap = new HashMap<>();
            Map<Long, Integer> questionDescription = new HashMap<>();
            for(SurveyQuestions surveyQuestion : surveyQuestionsList) {
                headers.add(surveyQuestion.getQuestionTitle());
                surveyQuestionsTitleMap.put(surveyQuestion.getId(), count);
                count++;
                if(surveyQuestion.isAllowedDescription()){
                    headers.add(surveyQuestion.getQuestionTitle() + " (" +DESCRIPTION+ ")");
                    questionDescription.put(surveyQuestion.getId(),count);
                    count++;
                }
                surveyHeadersMap.put(surveysDto.getSurveyName(), headers);
            }
            headers.add(SESSION_ID);
            headers.add(SESSION_TITLE);
            dataRowList = prepareSurveySubmissionData(surveyResponseList,surveysDto.getSurveyName(),event,surveyQuestionsTitleMap,questionDescription,headers);
            surveyDataRowsMap.put(surveysDto.getSurveyName(), dataRowList);

        }
        this.downloadCSVFileForSurveys(printWriter, surveyHeadersMap, surveyDataRowsMap);
    }

    private void downloadCSVFileForSurveys(PrintWriter writer,
                                         Map<String, List<String>> surveyHeadersMap,
                                         Map<String, List<List<String>>> surveyDataRowsMap) {
        try (CSVWriter csvWriter = new CSVWriter(writer,
                CSVWriter.DEFAULT_SEPARATOR,
                CSVWriter.DEFAULT_QUOTE_CHARACTER,
                CSVWriter.DEFAULT_ESCAPE_CHARACTER,
                CSVWriter.DEFAULT_LINE_END)) {
            String[] emptyRow = new String[]{};

            for (Map.Entry<String, List<List<String>>> surveyEntry : surveyDataRowsMap.entrySet()) {
                // Get the survey name
                String surveyName = surveyEntry.getKey();

                // Write the survey header
                String[] surveyTitle = new String[]{ "Survey: " + surveyName };
                csvWriter.writeNext(surveyTitle);

                // Fetch the headers for this survey
                List<String> surveyHeaders = surveyHeadersMap.getOrDefault(surveyName, new ArrayList<>());
                String[] headerRow = surveyHeaders.toArray(new String[0]);

                // Write the header row for the current survey
                csvWriter.writeNext(headerRow);

                // Write the data rows for the current survey
                for (List<String> dataRow : surveyEntry.getValue()) {
                    String[] dataRowArray = dataRow.toArray(new String[0]);
                    csvWriter.writeNext(dataRowArray);
                }

                // Add a blank row to separate survey
                csvWriter.writeNext(emptyRow);
                csvWriter.writeNext(emptyRow);
            }

            // Handle the case where there are no data rows for any survey
            if (surveyDataRowsMap.isEmpty()) {
                String[] noDataRow = { "No Data Found" };
                csvWriter.writeNext(noDataRow);
                csvWriter.writeNext(emptyRow);
            }

            // Flush the writer to ensure all data is written
            csvWriter.flush();
        } catch (IOException ex) {
            logger.error("IOException while writing CSV for surveys", ex);
        }
    }

    private List<List<String>> prepareSurveySubmissionData(List<SurveyResponse> surveyResponses, String surveyName, Event event, Map<Long, Integer> surveyQuestionsTitleMap, Map<Long, Integer> questionDescription, List<String> headers) {
        List<List<String>> dataRowList = new ArrayList<>();
        Map<Long, Session> sessionById = sessionService.findSessionByEventId(event).stream().collect(Collectors.toMap(Session::getId, Function.identity(), (old, new1) -> new1));
        surveyResponses.forEach(surveyResponse -> {
            logger.info("prepareSurveySubmissionData | survey response json value{}", surveyResponse.getJsonValue());
            List<String> dataList = Arrays.asList(new String[headers.size()]);
            dataList.set(0,surveyName);
            dataList.set(1,surveyResponse.getTrigger());
            dataList.set(2,getDateInLocal(surveyResponse.getSubmissionDate(),event.getEquivalentTimeZone()).toString());
            List<SurveyQuestionsKeyValueDto> surveyQuestionsKeyValueDtos = JsonMapper.parseJsonArray(surveyResponse.getJsonValue(), SurveyQuestionsKeyValueDto.class);
            prepareSurveyQuestionData(surveyQuestionsKeyValueDtos,dataList,surveyQuestionsTitleMap,questionDescription);
            if (NumberUtils.isNumberGreaterThanZero(surveyResponse.getSessionId()) && sessionById.get(surveyResponse.getSessionId()) != null) {
                dataList.set(headers.size() - 2, String.valueOf(sessionById.get(surveyResponse.getSessionId()).getId()));
                dataList.set(headers.size() - 1, sessionById.get(surveyResponse.getSessionId()).getTitle());
            } else {
                dataList.set(headers.size() - 2, STRING_EMPTY);
                dataList.set(headers.size() - 1, STRING_EMPTY);
            }

            checkAndReplaceBlankFieldWithDash(dataList);
            logger.info("prepareSurveySubmissionData | user id {} | datalist {}", surveyResponse.getUserId(),dataList);
            dataRowList.add(dataList);
        });
    return dataRowList;
    }

    private void checkAndReplaceBlankFieldWithDash(List<String> dataList){
        dataList.replaceAll(e -> isBlank(e) ? "-" : e);
    }
    private void prepareSurveyQuestionData(List<SurveyQuestionsKeyValueDto> surveyQuestionsKeyValueDtos, List<String> dataList,Map<Long, Integer> surveyQuestionsTitleMap, Map<Long, Integer> questionDescription) {
        for (SurveyQuestionsKeyValueDto surveyQuestionsKeyValueDto : surveyQuestionsKeyValueDtos) {
            if(surveyQuestionsKeyValueDto.getKey().equalsIgnoreCase(EMAIL)){
                dataList.set(3,surveyQuestionsKeyValueDto.getValue());
            }
            else if(surveyQuestionsKeyValueDto.getKey().equalsIgnoreCase(FIRST_NAME)){
                dataList.set(4,surveyQuestionsKeyValueDto.getValue());
            }
            else if(surveyQuestionsKeyValueDto.getKey().equalsIgnoreCase(LAST_NAME)){
                dataList.set(5,surveyQuestionsKeyValueDto.getValue());
            }
            else {
                if (surveyQuestionsTitleMap.containsKey(surveyQuestionsKeyValueDto.getQuestionId())) {
                    if (AttributeValueType.SINGLE_CHECKBOX.name().equals(surveyQuestionsKeyValueDto.getType())) {
                        dataList.set(surveyQuestionsTitleMap.get(surveyQuestionsKeyValueDto.getQuestionId()), Boolean.parseBoolean(surveyQuestionsKeyValueDto.getValue()) ? "Checked" : "Unchecked");
                    } else {
                        dataList.set(surveyQuestionsTitleMap.get(surveyQuestionsKeyValueDto.getQuestionId()), surveyQuestionsKeyValueDto.getValue());
                    }
                }
                if (questionDescription.containsKey(surveyQuestionsKeyValueDto.getQuestionId())) {
                    dataList.set(questionDescription.get(surveyQuestionsKeyValueDto.getQuestionId()), surveyQuestionsKeyValueDto.getDescription());
                }
            }
        }
    }

    @Override
    public void downloadUserActivityReport(PrintWriter printWriter, List<AttendeeTimelineDTO> attendeeTimelineDTOList, User user) {
        List<String> headerColumn = new ArrayList<>();
        attendeeTimelineDTOList.sort(Comparator.comparingLong(AttendeeTimelineDTO::getTimestamp).reversed());
        headerColumn.addAll(getUserActivityDefaultColumns());
        List<String> otherColumns = getUserActivityOtherColumns();
        headerColumn.addAll(otherColumns);
        List<List<String>> rowData = new ArrayList<>();
        for (AttendeeTimelineDTO timelineDTO : attendeeTimelineDTOList) {
            List<String> data = new ArrayList<>();
            setUserActivityRowData(user, otherColumns, timelineDTO, data);
            rowData.add(data);
        }
        List<String> updatedHeaderColumn = updateHeaderColumnName(headerColumn);
        this.downloadCSVFile(printWriter, updatedHeaderColumn, rowData);
    }

    private List<String> updateHeaderColumnName(List<String> headerColumn) {
        return headerColumn.stream().map(columnName->{
            if (columnName.equals(FIRST_NAME) || columnName.equals(LAST_NAME)|| columnName.equals(EMAIL)|| columnName.equals(ACTION)|| columnName.equals(ACTIVITY_TIME)|| columnName.equals(LINK)) {
                return  columnName;
            } else {
                String updateColumnName = "";
                switch (columnName.toLowerCase()) {
                    case "sessionname":
                        updateColumnName = "Session Name";
                        break;
                    case "eventname":
                        updateColumnName = "Event Name";
                        break;
                    case "sessiontype":
                        updateColumnName = "Session Type";
                        break;
                    case "sessiontime":
                        updateColumnName = "Session Time";
                        break;
                    case "userrole":
                        updateColumnName = "User Role";
                        break;
                    case "feedtext":
                        updateColumnName = "Feed Text";
                        break;
                    case "from":
                        updateColumnName = "From";
                        break;
                    case "commenttext":
                        updateColumnName = "Comment Text";
                        break;
                    case "exhibitorname":
                        updateColumnName = "Exhibitor Name";
                        break;
                    case "exhibitorlogo":
                        updateColumnName = "Exhibitor Logo";
                        break;
                    case "loungename":
                        updateColumnName = "Lounge Name";
                        break;
                    case "loungedescription":
                        updateColumnName = "Lounge Description";
                        break;
                    case "profileimage":
                        updateColumnName = "Profile Image";
                        break;
                    case "documentname":
                        updateColumnName = "Document Name";
                        break;
                    case "sponsorname":
                        updateColumnName = "Sponsor Name";
                        break;
                    case "sponsorlogo":
                        updateColumnName = "Sponsor Logo";
                        break;
                    case "linkname":
                        updateColumnName = "Link Name";
                        break;
                    case "personphoto":
                        updateColumnName = "Person Photo";
                        break;
                    case "speakerlogo":
                        updateColumnName = "Speaker Logo";
                        break;
                    case "company":
                        updateColumnName = "Compnay";
                        break;
                    case "title":
                        updateColumnName = "Title";
                        break;
                    case "eventicon":
                        updateColumnName= "Event Logo";
                        break;
                    case "description":
                        updateColumnName= "Description";
                        break;
                    case "firstname":
                        updateColumnName= "First Name";
                        break;
                    case "lastname":
                        updateColumnName= "Last Name";
                        break;
                    case "eventtime":
                        updateColumnName= "Event Time";
                        break;
                    case "productname":
                        updateColumnName= "Product Name";
                        break;
                    default:
                        updateColumnName= columnName;
                        break;
                }
                return updateColumnName;
            }
        }).collect(Collectors.toList());
    }

    private List<String> getUserActivityDefaultColumns() {
        List<String> headerColumn = new ArrayList<>();
        headerColumn.add(FIRST_NAME);
        headerColumn.add(LAST_NAME);
        headerColumn.add(EMAIL);
        headerColumn.add(ACTION);
        headerColumn.add(ACTIVITY_TIME);
        return headerColumn;
    }

    private List<String> getUserActivityOtherColumns() {
        List<String> headerColumn = new ArrayList<>();
        headerColumn.add(EVENT_NAME_FIELD);
        headerColumn.add(FIRST_NAME_FIELD);
        headerColumn.add(LAST_NAME_FIELD);
        headerColumn.add(SESSION_NAME_FIELD);
        headerColumn.add(EXHIBITOR_NAME);
        headerColumn.add(DOCUMENT_NAME);
        headerColumn.add(LINK_NAME);
        headerColumn.add(PRODUCT_NAME);
        headerColumn.add(LOUNGE_NAME);
        headerColumn.add(SPONSOR_NAME);
        headerColumn.add(FEED_TEXT);
        headerColumn.add(COMMENT_TEXT);
        return headerColumn;
    }

    private void setUserActivityRowData(User user, List<String> otherColumns, AttendeeTimelineDTO attendeeTimelineDTO, List<String> rowData) {
        rowData.add(user.getFirstName());
        rowData.add(user.getLastName());
        rowData.add(user.getEmail());
        rowData.add(prepareActionType(attendeeTimelineDTO));
        rowData.add(new Date(attendeeTimelineDTO.getTimestamp()).toString());

        String action = attendeeTimelineDTO.getActionType();
        for (String column : otherColumns) {
            Map<String, Object> description = attendeeTimelineDTO.getDescription();
            String cellValue = String.valueOf(description != null ? description.getOrDefault(column, STRING_EMPTY) : STRING_EMPTY);
            if (EVENT_NAME.equals(column)) {
                if (JOINED_AN_EVENT.equals(action)) {
                    rowData.add(setEmptyValueForNullInCSV(cellValue));
                } else {
                    rowData.add(STRING_EMPTY);
                }
            } else if (EXHIBITOR_NAME.equals(column)) {
                if (VISITED_AN_EXPO.equals(action)) {
                    rowData.add(setEmptyValueForNullInCSV(cellValue));
                } else {
                    rowData.add(STRING_EMPTY);
                }
            } else {
                rowData.add(setEmptyValueForNullInCSV(cellValue));
            }
        }
    }

    private String prepareActionType(AttendeeTimelineDTO attendeeTimelineDTO) {
        String actionType = attendeeTimelineDTO.getActionType();
        switch (actionType) {
            case VIEWED_PEOPLE_STRING:
                return VIEWED_ATTENDEE_STRING;
            case ADDED_A_FEED:
            case LIKED_A_POST:
            case ADDED_A_COMMENT:
            case DELETED_A_COMMENT:
            case DELETED_A_FEED:
            case UNDO_LIKED_A_POST:
                String from = String.valueOf(attendeeTimelineDTO.getDescription().getOrDefault(FROM_FIELD, STRING_EMPTY));
                return isNotBlank(from) ? actionType + " at " + from : actionType;
            default:
                break;
        }
        return actionType;
    }

    private String setEmptyValueForNullInCSV(String value){
        return "null".equals(value) ? "": value;
    }

    public boolean checkIsUserExhibitorStaff(List<Staff> staffList, Long userId, Long exhibiotrId) {
        if (staffList.isEmpty() || userId == null) {
            return false;
        } else if (userId == 1L) {
            return true;
        } else {
            List<StaffRole> staffRoles = Arrays.asList(StaffRole.admin, StaffRole.whitelabeladmin, StaffRole.staff, StaffRole.eventcoordinator);
            if (staffList.stream().anyMatch(staff -> staff.getUserId().equals(userId) && staffRoles.contains(staff.getRole()))) {
                return true;
            } else
                return staffList.stream().filter(staff -> staff.getExhibitor() != null).anyMatch(staff -> staff.getUserId().equals(userId) && null != staff.getExhibitor() && staff.getExhibitor().getId() == exhibiotrId);
        }
    }

    @Override
    public void downloadMeetingReports(PrintWriter writer, String status, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add("Sender First Name");
        headerList.add("Sender Last Name");
        headerList.add("Sender Email");
        headerList.add("Sender Company");
        headerList.add("Receiver First Name");
        headerList.add("Receiver Last Name");
        headerList.add("Receiver Email");
        headerList.add("Receiver Company");
        headerList.add("Meeting Start Time");
        headerList.add("Meeting End Time");
        headerList.add(NOTE);
        headerList.add("Equivalent Timezone");

        List<Object[]> acceptedMeetings = meetingScheduleService.getMeetingByEventIdAndStatus(event.getEventURL(), status);
        List<AttendeeProfileDto> attendeeProfileDtos = attendeeProfileService.getAllAttendeeProfiles(event.getEventId());
        Map<Long, AttendeeProfileDto> attendeeMap = attendeeProfileDtos.stream()
                .collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, Function.identity(), (attendeeProfileDto, attendeeProfileDto2) -> attendeeProfileDto));
        List<List<String>> dataRowList = new ArrayList<>();

        acceptedMeetings.forEach(e->{
            BigInteger senderUserId = (BigInteger) e[0];
            String senderFirstName = (String) e[1];
            String senderLastName = (String) e[2];
            String senderEmail = (String) e[3];
            BigInteger receiverUserId = (BigInteger) e[4];
            String receiverFirstName = (String) e[5];
            String receiverLastName = (String) e[6];
            String receiverEmail = (String) e[7];
            Date meetingStartTime = (Date) e[8];
            Date meetingEndTime = (Date) e[9];
            String note = (String) e[10];
            String equivalentTimezone = (String) e[11];

            AttendeeProfileDto senderProfileDto = attendeeMap.get(senderUserId.longValue());
            AttendeeProfileDto receiverProfileDto = attendeeMap.get(receiverUserId.longValue());

            boolean isSenderProfileNull = (null == senderProfileDto);
            boolean isReceiverProfileNull = (null == receiverProfileDto);

            List<String> dataList = new ArrayList<>();
            dataList.add(senderFirstName);
            dataList.add(senderLastName);
            dataList.add(senderEmail);
            dataList.add(!isSenderProfileNull ? senderProfileDto.getCompany() : STRING_EMPTY);
            dataList.add(receiverFirstName);
            dataList.add(receiverLastName);
            dataList.add(receiverEmail);
            dataList.add(!isReceiverProfileNull ? receiverProfileDto.getCompany() : STRING_EMPTY);
            dataList.add(dateFormatter.format(getDateInLocal(meetingStartTime,event.getEquivalentTimeZone())));
            dataList.add(dateFormatter.format(getDateInLocal(meetingEndTime,event.getEquivalentTimeZone())));
            dataList.add(note);
            dataList.add(equivalentTimezone);

            dataRowList.add(dataList);
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
        }

    @Override
    public void downloadEmailTrackingData(HttpServletResponse response, Event event, String charEncode, String holderEmail) {
        String fileName = "Email Activity.csv";
        response.setCharacterEncoding(charEncode);
        response.setContentType(CONTENT_CSV);
        String headerKey = CONTENT_DISPOSITION;
        String headerValue = String.format(ATTACHMENT_FILENAME, fileName);
        response.setHeader(headerKey, headerValue);
        List<String> headerList = new ArrayList<>();
        headerList.add("Email subject");
        headerList.add("Attendee email");
        headerList.add("Sender email");
        headerList.add("Open counts");
        headerList.add("Last email activity timestamp");
        headerList.add("Status");

        List<List<String>> dataRowList = new ArrayList<>();
        PrintWriter writer = null;
        try {
            writer = new PrintWriter(new OutputStreamWriter(response.getOutputStream(), charEncode));
        } catch (IOException e) {
           logger.warn("Error while creating print writer for email tracking data {} ", e.getMessage());
        }
        getEmailTrackingData(event, holderEmail, dataRowList);
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    private void getEmailTrackingData(Event event, String holderEmail, List<List<String>> dataRowList) {
        List<EmailMessageDTO> messageDTOS = sendGridMailService.getEmailDetails(String.valueOf(event.getEventId()),holderEmail, STRING_EMPTY,100);
        for (Object object : messageDTOS) {
            if (object instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) object;
                List<String> dataRow = new ArrayList<>();
                dataRow.add(String.valueOf(dataMap.get("subject")));
                dataRow.add(String.valueOf(dataMap.get("to_email")));
                dataRow.add(String.valueOf(dataMap.get("from_email")));
                dataRow.add(String.valueOf(dataMap.get("opens_count")));
                addLastEmailActivityTimestamp(dataMap, dataRow,event);
                dataRow.add(String.valueOf(dataMap.get("status")));
                dataRowList.add(dataRow);
            }
        }
    }

    private void addLastEmailActivityTimestamp(Map<String, Object> dataMap, List<String> dataRow, Event event) {
        try {
            Instant instant = Instant.parse(String.valueOf(dataMap.get("last_event_time")));
            ZoneId targetTimeZone = ZoneId.of(event.getEquivalentTimeZone());
            LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, targetTimeZone);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = localDateTime.format(formatter);
            dataRow.add(formattedDateTime);
        } catch (DateTimeParseException e) {
            dataRow.add("Invalid Timestamp");
        }
    }
    @Override
    public void getAttendeeOveragesByWhitelabelDataFix(HttpServletResponse response, String whiteLabelUrl) {
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(trim(whiteLabelUrl));
        PlanConfig planConfig = whiteLabel.getPlanConfig();
        List<String> headerList = getHeaderListBasedOnPlan(planConfig.isLatestPlan());
        headerList.add("TotalEventTickets");
        headerList.add("OtherStaffs");

        List<EventCreditDistributionDto> eventCreditDistributionDtoList = null;
        if (planConfig.isLatestPlan()) {
            eventCreditDistributionDtoList = creditDistributionBasedOnRegistrantsDataFix(whiteLabel, null);
        } else {
            eventCreditDistributionDtoList = whiteLabelService.whitelabelCreditDistributionBasedOnEventDays(whiteLabel);
        }

        if (CollectionUtils.isEmpty(eventCreditDistributionDtoList)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NO_USAGE_FOR_WHITE_LABEL);
        }

        PrintWriter writer = setHeader(response, "UsageReport_".concat(whiteLabel.getFirmName()).concat(".csv"));

        List<List<String>> dataRowList = new ArrayList<>();
        eventCreditDistributionDtoList.forEach(e -> {
            List<String> dataList = new ArrayList<>();
            if (e != null) {
                dataList.add(e.getEventId().toString());
                dataList.add(e.getEventURL());
                dataList.add(new SimpleDateFormat("MM/dd/yyyy").format(e.getEventEndDate()));
                dataList.add(e.getEventPlan());
                dataList.add(e.getAttendeeCount().toString());
                if (!planConfig.isLatestPlan()) {
                    dataList.add(e.getEventDays().toString());
                    dataList.add(e.getFinalAttendeeDays().toString());
                }
                dataList.add(e.getFreeCredits().toString());
                dataList.add(e.getRemainingAttendeeCount().toString());
                dataList.add(e.getPaidCredits().toString());
                if (e.getInvoiceId() != null) {
                    dataList.add(e.getInvoiceId());
                } else {
                    dataList.add(" ");
                }
                dataList.add(e.getFinalAttendeeDays().toString());
                dataList.add(e.getEventDays().toString());
                dataRowList.add(dataList);
            }
        });
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadEventTaskDataCSV(HttpServletResponse response, Event event, Long taskId) {
        logger.info("downloadEventTaskDataCSV | eventId: {}, taskId : {}", event.getEventId(), taskId);

        EventTask eventTask = eventTasksRepo.findByIdWithTaskParticipants(taskId)
                .orElseThrow(() -> new NotAcceptableException(NotAcceptableException.TaskExceptionMsg.TASK_NOT_FOUND));
        try {
            PrintWriter writer = setHeader(response,TASK_CSV_PREFIX.concat(eventTask.getTitle()).concat(DOT_CSV));
            List<List<String>> dataRowList = new ArrayList<>();

            String title = eventTask.getTitle();
            String assignedTo = ASSIGNEE_COUNT.concat(SPACE).concat(eventTask.getTaskType().name());
            String dueDate = getDateInLocal(eventTask.getSubmissionDueDate(),event.getEquivalentTimeZone(),DATE_FORMAT);
            String active = eventTask.isAllowPublish() ? "PUBLISH" : "UNPUBLISH";
            TaskVerificationType taskVerificationType = eventTask.getTaskVerificationType();

            List<TaskParticipants> taskParticipantsList = Optional.ofNullable(eventTask.getTaskParticipants()).orElse(Collections.emptyList());
            populateTaskParticipantRows(dataRowList, taskParticipantsList, title, assignedTo, dueDate, active,eventTask.getTaskType(),taskVerificationType);

            this.downloadCSVFile(writer, getEventTaskHeader(eventTask.getTaskType()), dataRowList);
        } catch (Exception e) {
            logger.error("downloadEventTaskDataCSV | Error occurred while writing CSV for eventId: {} , error: {}",
                    event.getEventId(), e.getMessage());
        }
    }

    @Override
    public void downloadLeaderboardDataCSVByTierId(PrintWriter writer,User user ,Event event, Long tierId) {
        EventChallengeTiersDTO tierDetails = eventChallengeTierService.getEventChallengeTier(tierId, user, event);
        List<ChallengeBasicDTO> challenges = new ArrayList<>();
        if(!tierDetails.getChallengeIds().isEmpty()){
            challenges = eventChallengeRepository.findAllByChallengeIds(tierDetails.getChallengeIds());
        }
        List<String> headerList = new ArrayList<>();
        headerList.add(HEADER.FIRST_NAME);
        headerList.add(HEADER.LAST_NAME);
        headerList.add(HEADER.EMAIL);
        headerList.add(HEADER.COMPANY);
        headerList.add(HEADER.TITLE);

        if (CollectionUtils.isNotEmpty(challenges)) {
            challenges.forEach(challengeDetail -> headerList.add(challengeDetail.getName()));
        }
        List<Long> challengeIds = challenges.stream().map(ChallengeBasicDTO::getId).collect(Collectors.toList());

        List<List<String>> dataRowList = new ArrayList<>();
        try {
            List<ChallengeLeaderBoardData> leaderboardList = leaderboardService.getTierLeaderBoardDataByChallengeIds(event, challengeIds, new PageSizeSearchObj(0, Integer.MAX_VALUE));
            leaderboardList.forEach(leaderboard -> {
                List<String> dataList = new ArrayList<>();
                dataList.add(leaderboard.getFirstName());
                dataList.add(leaderboard.getLastName());
                dataList.add(leaderboard.getEmail());
                dataList.add(leaderboard.getCompany());
                dataList.add(leaderboard.getTitle());
                if (CollectionUtils.isNotEmpty(challengeIds)) {
                    challengeIds.forEach(challengeId -> {
                        if (leaderboard.getUserChallengePointMap().get(challengeId) != null) {
                            dataList.add(String.valueOf(leaderboard.getUserChallengePointMap().get(challengeId)));
                        } else {
                            dataList.add(String.valueOf(0));
                        }
                    });
                }
                dataRowList.add(dataList);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadTiersLeaderboardCSVFile(PrintWriter writer, User user, Event event) throws IOException {
        List<ChallengeTierBasicDTO> eventChallengeTiersDTOS = eventChallengeTierService.getAllEventChallengeTiersWithName(user, event);
        List<EventChallengeAndTierMapping> mappings = challengeAndTierMappingRepository.findByEventId(event.getEventId());
        List<ChallengeBasicDTO> allChallenges = eventChallengeRepository.findAllChallengeBasicDetailsByEventId(event.getEventId());

        List<ChallengeLeaderBoardData> leaderboardList = leaderboardService.getLeaderBoardDataByEventIdForCSV(event);
        logger.info("downloadTiersLeaderboardCSVFile  event id {} user id {} tiers count {}, all challenges count {}, leaderboard list size {}", event.getEventId(), user.getUserId(), eventChallengeTiersDTOS.size(),allChallenges.size(),leaderboardList.size());

        // Group challenges by tiers
        Map<Long, List<ChallengeBasicDTO>> challengesByTier = new HashMap<>();
        for (EventChallengeAndTierMapping mapping : mappings) {
            challengesByTier.computeIfAbsent(mapping.getTierId(), k -> new ArrayList<>())
                    .add(allChallenges.stream()
                            .filter(challenge -> challenge.getId() == mapping.getChallengeId())
                            .findFirst()
                            .orElse(null));
        }

        // Prepare headers and data rows
        Map<String, List<String>> tierHeadersMap = new HashMap<>();
        Map<String, List<List<String>>> tierDataRowsMap = new HashMap<>();

        for (ChallengeTierBasicDTO tier : eventChallengeTiersDTOS) {
            logger.info("Start processing Tier: {}", tier.getTierName());

            // Get challenges for this tier
            List<ChallengeBasicDTO> challenges = challengesByTier.getOrDefault(tier.getId(), new ArrayList<>());
            if (challenges.isEmpty()) {
                continue; // Skip if no challenges exist for this tier
            }

            // Prepare the header for this tier
            List<String> tierHeaders = new ArrayList<>(List.of(
                    HEADER.FIRST_NAME, HEADER.LAST_NAME, HEADER.EMAIL, HEADER.COMPANY, HEADER.TITLE
            ));
            challenges.forEach(challenge -> tierHeaders.add(challenge.getName()));
            tierHeadersMap.put(tier.getTierName(), tierHeaders);

            // Prepare the data rows for this tier
            List<List<String>> tierDataRows = new ArrayList<>();
            for (ChallengeLeaderBoardData leaderboard : leaderboardList) {
                List<String> dataRow = new ArrayList<>(List.of(
                        leaderboard.getFirstName(),
                        leaderboard.getLastName(),
                        leaderboard.getEmail(),
                        leaderboard.getCompany(),
                        leaderboard.getTitle()
                ));
                challenges.forEach(challenge -> {
                    if (challenge != null) {
                        Integer points = Math.toIntExact(
                                leaderboard.getUserChallengePointMap().getOrDefault(challenge.getId(), 0L)
                        );
                        dataRow.add(String.valueOf(points));
                    }
                });
                tierDataRows.add(dataRow);
            }
            tierDataRowsMap.put(tier.getTierName(), tierDataRows);

            logger.info("End processing Tier: {}", tier.getTierName());
        }

        // Write the CSV file using the prepared data
        this.downloadCSVFileForTiers(writer, tierHeadersMap, tierDataRowsMap);
    }

    @Override
    public void downloadSessionLocationListCSV(PrintWriter writer, Event event) {
        List<String> headerList = new ArrayList<>();
        headerList.add(ID_CAPITAL);
        headerList.add(NAME);
        headerList.add(SOURCE_URL);
        headerList.add(ASSIGNED_SESSION);

        List<List<String>> dataRowList = new ArrayList<>();

        List<SessionLocation> sessionLocations = sessionLocationService.getSessionLocationByEventId(event);
        List<Long> sessionLocationIds = sessionLocations.stream().map(SessionLocation::getId).collect(toList());
        List<Object[]> sessionToLocationMapping = sessionLocationService.countSessionsByLocationIds(event.getEventId(),sessionLocationIds);
        Map<Long,Long> locationIdToSessionCountMap = sessionToLocationMapping.stream().collect(Collectors.toMap(e -> (Long) e[0], e -> (Long) e[1]));

        for(SessionLocation sessionLocation : sessionLocations){
            List<String> dataList = new ArrayList<>();
            dataList.add(String.valueOf(sessionLocation.getId()));
            dataList.add(sessionLocation.getName());
            dataList.add(sessionLocation.getSourceUrl());
            dataList.add(String.valueOf(locationIdToSessionCountMap.getOrDefault(sessionLocation.getId(),0L)));
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);
    }

    @Override
    public void downloadSharingViewDataCSV(PrintWriter writer, Event event, String displayViewId, String searchString) {

        AttendeeAnalyticsPageSizeSearchObj searchObj = new AttendeeAnalyticsPageSizeSearchObj(0, Integer.MAX_VALUE, STRING_EMPTY);
        searchObj.setDisplayViewId(Long.valueOf(SecurityUtils.decode(displayViewId)));
        searchObj.setSearch(searchString);

        List<ColumnDto> columnDtoList = roAnalyticsColumnMasterService.getDefaultColumns(AnalyticsArea.ATTENDEE_ANALYTICS);
        Map<String, String> columnKeyLabelMap = columnMasterService.getColumnKeyLabelMap(AnalyticsArea.ATTENDEE_ANALYTICS, event);
        logger.info("ColumnDto list size: {} for event {} and displayViewId {}", columnDtoList.size(), event.getEventId(), searchObj.getDisplayViewId());
        DisplaySharingViewDto displaySharingViewDto = displayViewService.getAttendeesBySharingView("", searchObj, event,true,displayViewId);
        List<Audience> audienceList = (List<Audience>) displaySharingViewDto.getAttendeeData().getData();

        logger.info("Audience list size: {} for CSV download, event: {}", audienceList.size(), event.getEventId());

        List<String> csvHeader = displaySharingViewDto.getViewFilterDetailsDTO().getEditColumns().stream().map(this::checkIfColumnHeaderContainsNumericKey).map(columnKeyLabelMap::get).collect(Collectors.toList());
        List<List<String>> dataRowList = audienceList.stream().map(audience -> displaySharingViewDto.getViewFilterDetailsDTO().getEditColumns().stream()
                .map(this::checkIfColumnHeaderContainsNumericKey).map(key -> getCellValue(key, audience)).collect(Collectors.toList())).collect(Collectors.toList());

        downloadCSVFile(writer, csvHeader, dataRowList);
        logger.info("Successfully prepared attendee analytics CSV, result size: {}", dataRowList.size());
    }


    @Override
    public void downloadContinuingEdCreditManagementCSV(PrintWriter writer, User user, Event event) {
        List<UserCreditManagementDetails> userCreditManagementDetails = creditManagementRepoService.findAllByEventIdAndCreditApplied(event);

        List<EventCECriteriaDTO> criteriaDTOList = eventCECriteriaService.getCECriteriaDTOByEvent(event);

        Map<Long, EventCECriteriaDTO> challengeIdMap = criteriaDTOList.stream()
                .collect(Collectors.toMap(EventCECriteriaDTO::getCriteriaId, Function.identity()));

        Map<Long, String> challengeIdWithTriggerNameMap = criteriaDTOList.stream()
                .collect(Collectors.toMap(
                        EventCECriteriaDTO::getCriteriaId,
                        challengeDto -> challengeDto.getTrigger() != null && challengeDto.getTrigger().containsKey(ContinuingEdConstants.TRACK) ? "Track" : "Sessions",
                        (a, b) -> a
                ));

        Map<Long, String> challengeWithSessionNamesMap = continuingEdService.prepareChallengeWithSessionNamesMap(criteriaDTOList, event);
        Map<Long, String> userCreditManagementWithTracksNamesMap = creditManagementService.prepareUserCreditManagementWithTrackNamesMap(userCreditManagementDetails, event);


        List<String> headerList = Arrays.asList(NAME, CREDITS_EARNED, CRITERIA_NAME, CRITERIA_TYPE, SESSION_NAME_CSV, TRACKS_SLASH_SUBTRACKS);


        List<List<String>> dataRowList = new ArrayList<>();
        for(UserCreditManagementDetails userCreditManagementDetail : userCreditManagementDetails){
            List<String> dataList = new ArrayList<>();

            String userName = Optional.ofNullable(userCreditManagementDetail.getUser())
                    .map(userObj -> userObj.getFirstName() + " " + userObj.getLastName())
                    .orElse(STRING_EMPTY);

            String challengeName = Optional.ofNullable(challengeIdMap.get(userCreditManagementDetail.getChallengeId()))
                    .map(EventCECriteriaDTO::getName)
                    .orElse(STRING_EMPTY);

            dataList.add(userName);
            dataList.add(String.valueOf(userCreditManagementDetail.getTotalCredits()));
            dataList.add(challengeName);
            dataList.add(challengeIdWithTriggerNameMap.getOrDefault(userCreditManagementDetail.getChallengeId(), STRING_EMPTY));
            dataList.add(challengeWithSessionNamesMap.getOrDefault(userCreditManagementDetail.getChallengeId(), STRING_EMPTY));
            dataList.add(userCreditManagementWithTracksNamesMap.getOrDefault(userCreditManagementDetail.getId(),STRING_EMPTY));
            dataRowList.add(dataList);
        }
        this.downloadCSVFile(writer, headerList, dataRowList);


    }

    private void populateTaskParticipantRows(List<List<String>> dataRowList, List<TaskParticipants> taskParticipantsList,
                                             String title, String assignedTo, String dueDate, String active,TaskType taskType,TaskVerificationType taskVerificationType) {
        if (CollectionUtils.isEmpty(taskParticipantsList)) {
            addTaskRow(dataRowList, title, assignedTo.replace(ASSIGNEE_COUNT, ZERO), null, dueDate,
                    ZERO,ZERO, TaskStatus.COMPLETED.name(), active,taskType,taskVerificationType);
            return;
        }
        long totalCompletedTask = taskParticipantsList.stream()
                .filter(tp -> WorkflowStatus.COMPLETED.equals(tp.getWorkflowStatus()))
                .count();
        long totalTask = taskParticipantsList.size();
        String taskStatus = calculateOverallTaskStatus(totalCompletedTask, totalTask);
        assignedTo = assignedTo.replace(ASSIGNEE_COUNT, String.valueOf(totalTask));
        for (TaskParticipants taskParticipant : taskParticipantsList) {
            addTaskRow(dataRowList, title, assignedTo, taskParticipant,
                    dueDate, String.valueOf(totalTask),String.valueOf(totalCompletedTask),taskStatus, active,taskType,taskVerificationType);
        }
    }

    private void addTaskRow(List<List<String>> dataRowList, String title, String assignedTo, TaskParticipants taskParticipant,
                            String dueDate,String totalTask, String totalCompletedTask ,String taskStatus, String active,TaskType taskType, TaskVerificationType taskVerificationType) {
        List<String> dataRow = new ArrayList<>();
        dataRow.add(title);
        dataRow.add(assignedTo);
        if(TaskType.SPEAKER.equals(taskType)) {
            addSpeakerDetails(dataRow, taskParticipant);
        }
        dataRow.add(null!=taskParticipant ? taskParticipant.getWorkflowStatus().name() : WorkflowStatus.PENDING.name());
        dataRow.add(String.valueOf(taskVerificationType));
        dataRow.add(dueDate);
        dataRow.add(totalTask);
        dataRow.add(totalCompletedTask);
        dataRow.add(taskStatus);
        dataRow.add(active);
        dataRowList.add(dataRow);
    }

    private void addSpeakerDetails(List<String> dataRow, TaskParticipants taskParticipant) {
        if (null!=taskParticipant && taskParticipant.getSpeaker() != null) {
            dataRow.add(taskParticipant.getSpeaker().getFirstName() + SPACE + taskParticipant.getSpeaker().getLastName());
            dataRow.add(taskParticipant.getSpeaker().getEmail());
        } else {
            dataRow.add(EMPTY);
            dataRow.add(EMPTY);
        }
    }

    private String calculateOverallTaskStatus(long totalCompletedTask, long totalTask) {
        if (totalCompletedTask == totalTask) {
            return TaskStatus.COMPLETED.name();
        } else if (totalCompletedTask > 0) {
            return TaskStatus.IN_PROGRESS.name();
        } else {
            return TaskStatus.PENDING.name();
        }
    }

    private List<String> getEventTaskHeader(TaskType taskType) {
        List<String> headers = new ArrayList<>(List.of(
                "Title", "Assigned To", "Task Verification Type","Submission Due Date",
                "Total Task Assigned", "Total Task Completed",
                "OverAll Task Status", "Publish/Unpublish"
        ));

        if (TaskType.SPEAKER.equals(taskType)) {
            headers.addAll(2, List.of("Speaker Name", "Speaker Email ID", "Speaker Task Status"));
        } else {
            headers.add(2, "Session Task Status");
        }
        return headers;
    }



    private List<String> addDefaultMandatoryData(TicketHolderDetailDto ticketHolderDetailDto){
        List<String> dataList = new ArrayList<>();
        dataList.add(ticketHolderDetailDto.getHolderFirstName());
        dataList.add(ticketHolderDetailDto.getHolderLastName());
        dataList.add(ticketHolderDetailDto.getHolderEmail());
        return dataList;
    }

    @Override
    public void attendeeCheckInLogs(Event event, PrintWriter writer) {
        List<UserCheckInLogDTO> checkInLogs = CheckInAuditLogService.findUserCheckInLogByEventId(event.getEventId());
        List<String> csvHeader = Arrays.asList(
                "First Name", "Last Name", "Ticket Number", "Action", "Date", "Time"
        );

        List<List<String>> dataRowList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(checkInLogs)) {
            String timeZone = event.getEquivalentTimeZone();
            logger.info("Using time zone: {} for eventId: {}", timeZone, event.getEventId());

            for (UserCheckInLogDTO log : checkInLogs) {
                try {
                    List<String> row = Arrays.asList(
                            log.getFirstName(),
                            log.getLastName(),
                            String.valueOf(log.getTicketId()),
                            log.getAction(),
                            TimeZoneUtil.getDateInLocal(log.getCheckInDate(), timeZone, ONLY_DATE_SIMPLE_FORMAT),
                            TimeZoneUtil.getDateInLocal(log.getCheckInDate(), timeZone, ONLY_TIME_FORMAT)
                    );
                    dataRowList.add(row);
                } catch (Exception e) {
                    logger.error("Error processing check-in log entry for ticketId: {}", log.getTicketId(), e);
                }
            }
        }else {
            logger.warn("No check-in log data found for eventId: {}", event.getEventId());
        }
        this.downloadCSVFile(writer, csvHeader, dataRowList);
        logger.info("CSV download completed for eventId: {}", event.getEventId());
    }

    @Override
    public void downloadWaitlistedUsersCSV(PrintWriter writer, Session session) {
        logger.info("downloadWaitlistedUsersCSV for sessionId {}", session.getId());
        List<UserSession> waitlistedUsers = userSessionRepoService.findAllBySessionIdAndIsWaitlisted(session.getId());

        Event event = roEventService.getEventById(session.getEventId());
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);

        List<String> headerList = new ArrayList<>();

        List<TicketHolderRequiredAttributes> listOfAttributesHavingOnlyEnableForTicketHolder = Collections.emptyList();
        if (!holderRequiredAttributes.isEmpty()) {
            listOfAttributesHavingOnlyEnableForTicketHolder = ticketingCSVService.addRequiredFieldsOfHolderCsvByAttribute(headerList, holderRequiredAttributes, DataType.TICKET);
        }

        List<TicketHolderRequiredAttributes> finalListOfAttributesHavingOnlyEnableForTicketHolder = listOfAttributesHavingOnlyEnableForTicketHolder;

        headerList.add(WAITLISTED_DATE);

        List<List<String>> dataRowList = new ArrayList<>();

        List<EventTickets> eventTickets = eventCommonRepoService.findByEventIdJoinFetch(event, 0L, false, DataType.TICKET, null, null);
        Map<Long, List<EventTickets>> holderUserEventTickets = eventTickets.stream().collect(Collectors.groupingBy(eventTicket -> eventTicket.getHolderUserId().getUserId()));

        // Process only waitlisted users
        for (UserSession userSession : waitlistedUsers){
            List<String> dataList = new ArrayList<>();
            User user = userSession.getUser();
            List<EventTickets> eventTicketsList = holderUserEventTickets.get(userSession.getUserId());

            if (CollectionUtils.isNotEmpty(eventTicketsList)) {
                prepareTicketHolderRequiredAttributesDataList(finalListOfAttributesHavingOnlyEnableForTicketHolder, dataList,
                        eventTicketsList);
            } else {
                prepareUserDataListForWithoutTicket(finalListOfAttributesHavingOnlyEnableForTicketHolder, dataList, user);
            }

            // Add waitlisted date
            dataList.add(userSession.getRegistrationDate() != null ? dateFormatter.format(getDateInLocal(userSession.getRegistrationDate(),event.getEquivalentTimeZone())) : STRING_EMPTY);

            dataRowList.add(dataList);
        }

        this.downloadCSVFile(writer, headerList, dataRowList);
        logger.info("CSV download completed for sessionId: {}", session.getId());
    }
}