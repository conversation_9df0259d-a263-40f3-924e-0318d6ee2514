package com.accelevents.services.impl;

import com.accelevents.common.dto.EventInfoDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.ro.eventTicket.ROTicketingOrderService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.accelevents.dto.TicketHolderHelper.getAttributeValue1;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;

@Service
public class TicketingOrderDetailsServiceImpl implements TicketingOrderDetailsService {

    private static final Logger log = LoggerFactory.getLogger(TicketingOrderDetailsServiceImpl.class);

    @Autowired
    private TicketingOrderService ticketingOrderService;
    @Autowired
    private ROTicketingOrderService roTicketingOrderService;

    @Autowired
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;

    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Autowired
    private TicketingService ticketingService;

    @Autowired
    private StripeTransactionService stripeTransactionService;

    @Autowired
    private EventCommonRepoService eventCommonRepoService;

    @Autowired
    private TicketingHelperService ticketingHelperService;

    @Lazy
    @Autowired
    private TicketingPurchaseService ticketingPurchaseService;

    @Autowired
    private StripeService stripeService;

    @Autowired
    private EventRepoService eventRepoService;

    @Autowired
    private OrganizerService organizerService;

    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;
    @Autowired
    private EventTicketTransferDetailService eventTicketTransferDetailService;
    @Autowired
    private EventTicketTransactionService eventTicketTransactionService;
    @Autowired
    private SalesTaxService salesTaxService;

    private DateFormat dateFormatter = new SimpleDateFormat(yyyy_MM_dd_HH_mm_ss);

    @Override
    public OrderPageData getOrderData(Event event, PageSizeSearchObj pageSizeSearchObj, String search, Long recurringEventId, Date searchDate, DataType dataType, Date searchTimeStamp, boolean isFetchDeletedOrder, Long orderId, boolean ticketUpdatedAfter) {
        log.info("TicketingOrderDetailsServiceImpl getOrderData EventId : {} | recurringEventId : {}", event.getEventId(), recurringEventId);
        boolean isRecurringEvent = ticketingService.isRecurringEvent(event);
        Date formattedDate = fetchFormattedDate(searchTimeStamp,event.getEventId());
        Page<TicketingOrder> ticketingOrdersPages;
        if  (NumberUtils.isNumberGreaterThanZero(orderId)){
            TicketingOrder ticketingOrder = ticketingOrderService.findByid(orderId);
            if (ticketingOrder == null) {
                throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND);
            }
            ticketingOrdersPages = new PageImpl<>(Collections.singletonList(ticketingOrder), PageRequest.of(0, 1),1);
        }
        else{
            ticketingOrdersPages = ticketingOrderService.findByEventidAndStatusIsPaid(event, pageSizeSearchObj.getSize(), pageSizeSearchObj.getPage(), search, recurringEventId, searchDate, dataType, formattedDate, isFetchDeletedOrder);
        }

        OrderPageData orderPageData = new OrderPageData();


        if (ticketingOrdersPages != null && !ticketingOrdersPages.getContent().isEmpty()) {

            StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);

            List<TicketingOrder> ticketingOrders = ticketingOrdersPages.getContent();
            List<Long> ticketingOrderIds = ticketingOrders.stream().map(TicketingOrder::getId).collect(Collectors.toList());
            log.info("TicketingOrderDetailsServiceImpl getOrderData ticketingOrderIds : {}", ticketingOrderIds);
            Executor executor = Executors.newFixedThreadPool(3);

            CompletableFuture<List<EventTickets>> ticketsFuture;
            if(ticketUpdatedAfter && !ObjectUtils.isEmpty(formattedDate) ){
                log.info("TicketingOrderDetailsServiceImpl getOrderData And ticketUpdatedAfter is true |  formattedDate : {} ", formattedDate);
                ticketsFuture = CompletableFuture.supplyAsync(() ->
                        eventCommonRepoService.findAllByTicketingOrderIdsAndFormattedDateAfter(ticketingOrderIds,formattedDate), executor);
            }else {
                ticketsFuture = CompletableFuture.supplyAsync(() ->
                        eventCommonRepoService.findAllByListOrderIds(ticketingOrderIds), executor);
            }


            CompletableFuture<List<EventTicketTransaction>> transactionsFuture = CompletableFuture.supplyAsync(() ->
                    eventTicketTransactionService.findAllEventTransactionByOrderIdsAndDocumentLinkIsNotNull(ticketingOrderIds), executor);
              CompletableFuture<List<StripeTransaction>> stripeFuture = CompletableFuture.supplyAsync(() ->
                    stripeTransactionService.findBySourceAndListOfSourceId(StripeTransactionSource.EVENT_TICKETING, ticketingOrderIds), executor);

            CompletableFuture<Void> allFutures = CompletableFuture.allOf(ticketsFuture, transactionsFuture, stripeFuture);
            allFutures.join();
            List<EventTickets> eventTicketsList = ticketsFuture.join();
            List<EventTicketTransaction> eventTicketTransactionList = transactionsFuture.join();
            List<StripeTransaction> listOfStripeTransactions = stripeFuture.join();
            Map<Long, List<EventTicketTransaction>> eventTicketTransactionMap = eventTicketTransactionList.stream().collect(Collectors.groupingBy(EventTicketTransaction::getTicketingOrderId));
            Map<Long, List<EventTickets>> ticketingOrderListMap = eventTicketsList.stream().collect(Collectors.groupingBy(EventTickets::getTicketingOrderId));
            Map<Long, StripeTransaction> stripeTransactionMap = listOfStripeTransactions.stream().collect(Collectors.toMap(StripeTransaction::getSourceId, st -> st, (oldEntry, newEntry) -> newEntry));
            log.info("TicketingOrderDetailsServiceImpl getOrderData eventTicketList.size() : {}", eventTicketsList.size());

            List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
                    .getAllAttributesOrderByAttributeOrder(event);

            Map<Long, Double> orderWisePayedAmount = eventTicketTransactionService.getOrdersNetPaidAmount(ticketingOrderIds);

            List<EventTicketFeesDto> eventTicketFees = eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(ticketingOrderIds);
            int deleteTicketRecords = 0;

            for (TicketingOrder ticketingOrder : ticketingOrdersPages.getContent()) {
                EventTickets eventTicket = ticketingOrderListMap.get(ticketingOrder.getId()).get(0);
                PurchaserInfo purchaserInfo = new PurchaserInfo();
                if (null != eventTicket) {

                    TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                    if (ticketHolderAttributes != null && StringUtils.isNotBlank(ticketHolderAttributes.getJsonValue())) {
                        try {
                            TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                            if (ticketAttributeValueDto != null) {

                                for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : holderRequiredAttributes) {
                                    if (ticketHolderRequiredAttributes.getEnabledForTicketPurchaser()) {
                                        if ("First Name".equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                            purchaserInfo.setFirstName(getAttributeValue1(ticketAttributeValueDto,
                                                    ticketHolderRequiredAttributes.getName(), false,
                                                    ticketHolderRequiredAttributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType())));
                                        } else if ("Last Name".equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                            purchaserInfo.setLastName(getAttributeValue1(ticketAttributeValueDto,
                                                    ticketHolderRequiredAttributes.getName(), false,
                                                    ticketHolderRequiredAttributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType())));
                                        } else if ("email".equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                            purchaserInfo.setEmail(getAttributeValue1(ticketAttributeValueDto,
                                                    ticketHolderRequiredAttributes.getName(), false,
                                                    ticketHolderRequiredAttributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType())));
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception e) {
                            log.info("getOrderData JAXBException", e);
                        }
                    }
                }

                 EnumSet<TicketingOrder.TicketingOrderStatus> VALID_STATUSES =
                        EnumSet.of(
                                TicketingOrder.TicketingOrderStatus.PAID,
                                TicketingOrder.TicketingOrderStatus.PARTIAL,
                                TicketingOrder.TicketingOrderStatus.PROCESSING,
                                TicketingOrder.TicketingOrderStatus.PAYMENT_FAILED,
                                TicketingOrder.TicketingOrderStatus.UNPAID
                        );
                if (VALID_STATUSES.contains(ticketingOrder.getStatus()) || isFetchDeletedOrder) {
                    OrderDtoV2 orderDto = getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, isRecurringEvent, recurringEventId,eventTicketFees,isFetchDeletedOrder);
                    if (eventTicketTransactionMap.containsKey(ticketingOrder.getId())){
                        String documentLinks = eventTicketTransactionMap.get(ticketingOrder.getId())
                                .stream()
                                .map(EventTicketTransaction::getDocumentLink)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.joining(","));
                        orderDto.setDocumentLink(documentLinks);
                    }
                    StripeTransaction stripeTransaction = stripeTransactionMap.get(ticketingOrder.getId());
                    if (stripeTransaction != null) {
                        orderDto.setLastFour(stripeTransaction.getLastFour());
                        if (stripeTransaction.getPaymentMethodType().equals(PaymentMethodTypes.CARD)){
                            orderDto.setCardType(stripeTransaction.getCardType());
                        }else{
                            orderDto.setCardType(stripeTransaction.getPaymentMethodType().getType());
                        }
                    }
                    if (!orderDto.getAttendee().isEmpty()) {
                        orderDto.setCurrency(event.getCurrency().getSymbol());
                        orderDto.setTimezoneId(event.getTimezoneId());
                        orderDto.setEquivalentTimezone(event.getEquivalentTimeZone());
                        orderPageData.addOrder(orderDto);
                    }
                    else if(ticketUpdatedAfter)
                        deleteTicketRecords++;

                    Map<Long, Double> orderWiseAmount = calculateOrderAmount(ticketingOrder,ticketingOrderListMap);
                    if (TicketingOrder.OrderType.PAY_LATER.equals (ticketingOrder.getOrderType()) && (TicketingOrder.TicketingOrderStatus.PARTIAL.equals(ticketingOrder.getStatus()) || TicketingOrder.TicketingOrderStatus.UNPAID.equals(ticketingOrder.getStatus()))
                            && orderWiseAmount.get(ticketingOrder.getId()) != null) {

                        Double dueAmount = GeneralUtils.getRoundValue(orderWiseAmount.get(ticketingOrder.getId()) - orderWisePayedAmount.getOrDefault(ticketingOrder.getId(), 0.0));

                        if (dueAmount < 0) {
                            dueAmount = 0.0;
                        } else if (dueAmount > orderWiseAmount.get(ticketingOrder.getId())) {
                            dueAmount = orderWiseAmount.get(ticketingOrder.getId());
                        }
                        orderDto.setDueAmount(Math.min(orderWiseAmount.get(ticketingOrder.getId()), dueAmount));
                    }
                }
            }
            orderPageData.setRecordsFiltered(ticketingOrdersPages.getNumberOfElements() - deleteTicketRecords);
            orderPageData.setRecordsTotal(ticketingOrdersPages.getTotalElements()- deleteTicketRecords);
        }
        return orderPageData;
    }

    private Date fetchFormattedDate(Date searchTimeStamp,Long eventId) {
        if (searchTimeStamp == null) {
            return null;
        }
        dateFormatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        String formattedDate = dateFormatter.format(searchTimeStamp);
        try {
            searchTimeStamp = dateFormatter.parse(formattedDate);
        } catch (ParseException e) {
            log.error("Error occurred while formatting the date for event ID: {}", eventId, e);
        }
        return searchTimeStamp;
    }

    @Transactional
    public Map<Long, Double> calculateOrderAmount(TicketingOrder ticketingOrder, Map<Long, List<EventTickets>> orderWiseEventTickets) {
        Map<Long, Double> priceByOrder = new HashMap<>();
        List<EventTickets> eventTickets = orderWiseEventTickets.get(ticketingOrder.getId());
        double paidAmountWithAllFee = eventTickets.stream().filter(e -> !(TicketStatus.DELETED.equals(e.getTicketStatus()) || TicketStatus.CANCELED.equals(e.getTicketStatus())))
                .mapToDouble(e -> {
                    double paidAmount = e.getTicketPrice() - e.getRefundedAmount();
                    double saleTax = 0;
                    double vatTax = 0;
                    if (e.getTicketingTypeId().isPassfeetobuyer() && NumberUtils.isNumberGreaterThanZero(ticketingOrder.getSaleTaxPercentage())) {
                        saleTax = e.getTicketPrice() * (ticketingOrder.getSaleTaxPercentage() / 100);
                    }
                    if (e.getTicketingTypeId().isPassFeeVatToBuyer() && NumberUtils.isNumberGreaterThanZero(e.getVatTaxPercentage())) {
                        vatTax = e.getTicketPrice() * (e.getVatTaxPercentage() / 100);
                    }
                    return paidAmount + saleTax + vatTax;
                }).sum();
        priceByOrder.putIfAbsent(ticketingOrder.getId(), GeneralUtils.getRoundValue(paidAmountWithAllFee));
        return priceByOrder;
    }


    @Override
    public double calculateOrderAmountByOrderId(Event event, long orderId, List<TicketingOrderManager> orderManagers, boolean cardPaymentDone, boolean depositPayment) {
        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        TicketingCoupon ticketingCoupon = null;
        StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);
        double paidAmountWithAllFee = 0;

        TicketingOrder ticketingOrder = ticketingOrderService.findByid(orderId);
        int numberOfTicketTypeExcludingFreeType = ticketingHelperService.getTotalNoOfTicketTypeExcludingFreeType(orderManagers);

        ticketingCoupon = ticketingOrder.getTicketingCoupon();

        TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto = ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(orderManagers, ticketingCoupon);

        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(ticketingOrder.getEventid().getEventId());

        for (TicketingOrderManager ticketingOrderManager : orderManagers) {

            TicketingType ticketingType = ticketingOrderManager.getTicketType();
            int numberOfDiscountedTickets = ticketingOrderManager.getNumberOfDiscountedTicket();

            ticketPriceDetailsList.addAll(
                    ticketingHelperService.getTicketPriceDetails(
                            Math.max(numberOfDiscountedTickets, 0),
                            ticketingOrderManager,
                            cardPaymentDone,
                            ticketingCoupon,
                            ticketingType,
                            numberOfTicketTypeExcludingFreeType,
                            totalTicketsAndTotalTicketPriceDto.getTotalTickets(),
                            ticketingOrder, stripeDTO, false, depositPayment, salesTaxFeeDto,
                            totalTicketsAndTotalTicketPriceDto.getTotalTicketPrice(), 0));

        }
        paidAmountWithAllFee += ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getPriceWithFee).sum();
        return paidAmountWithAllFee;
    }

    /**
     * Fetch all Orders of attendee
     */
    @Override
    public OrderPageData getUserOrderData(Event event, User user, int size, int page, Long recurringEventId) {
        log.info("Start Get user orders for event {}, user {}", event.getEventId(), user.getUserId());

        boolean isRecurringEvent = ticketingService.isRecurringEvent(event);
        Page<TicketingOrder> ticketingOrdersPages = roTicketingOrderService.findUserEventOrders(event, user, size, page, recurringEventId);
        OrderPageData orderPageData = new OrderPageData();

        if (ticketingOrdersPages != null && !ticketingOrdersPages.getContent().isEmpty()) {
            log.info("Fetched TicketingOrder size {}, event {}, user {}", ticketingOrdersPages.getNumberOfElements(), event.getEventId(), user.getUserId());
            StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);
            List<TicketingOrder> ticketingOrders = ticketingOrdersPages.getContent();
            List<Long> ticketingOrderIds = ticketingOrders.stream().map(TicketingOrder::getId).collect(Collectors.toList());
            List<EventTickets> eventTicketsList = eventCommonRepoService.findAllByListOrderIds(ticketingOrderIds);
            Map<Long, List<EventTickets>> ticketingOrderListMap = eventTicketsList.stream().collect(Collectors.groupingBy(EventTickets::getTicketingOrderId));
            List<StripeTransaction> listOfStripeTransactions = stripeTransactionService
                    .findBySourceAndListOfSourceId(StripeTransactionSource.EVENT_TICKETING, ticketingOrderIds);
            Map<Long, List<StripeTransaction>> stripeTransactionMap = listOfStripeTransactions.stream().collect(Collectors.groupingBy(StripeTransaction::getSourceId));

            PurchaserInfo purchaserInfo = new PurchaserInfo();
            // Set purchaser info it will be the same purchaser for all ticketing orders
            setPurchaserInfo(!CollectionUtils.isEmpty(eventTicketsList) ? eventTicketsList.get(0) : null, event, purchaserInfo);
            log.info("Successfully set Purchaser Info event {}, user {}", event.getEventId(), user.getUserId());
            List<EventTicketFeesDto> eventTicketFees = eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(ticketingOrderIds);
            for (TicketingOrder ticketingOrder : ticketingOrdersPages.getContent()) {
                if (TicketingOrder.TicketingOrderStatus.PAID.toString().equals(ticketingOrder.getStatus().toString()) || TicketingOrder.OrderType.UNPAID.toString().equals(ticketingOrder.getStatus().toString())) {
                    OrderDtoV2 orderDto = getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, isRecurringEvent, recurringEventId, eventTicketFees,false);
                    List<StripeTransaction> stripeTransactions = stripeTransactionMap.get(ticketingOrder.getId());
                    if (!CollectionUtils.isEmpty(stripeTransactions)) {
                        StripeTransaction stripeTransaction = stripeTransactions.get(0);
                        orderDto.setLastFour(stripeTransaction.getLastFour());
                        orderDto.setCardType(stripeTransaction.getCardType());
                    }
                    if (!orderDto.getAttendee().isEmpty()) {
                        orderDto.setCurrency(event.getCurrency().getSymbol());
                        orderDto.setTimezoneId(event.getTimezoneId());
                        orderPageData.addOrder(orderDto);
                    }
                }
            }
            orderPageData.setRecordsFiltered(ticketingOrdersPages.getNumberOfElements());
            orderPageData.setRecordsTotal(ticketingOrdersPages.getTotalElements());
            log.info("Successfully set orderPageData size {} , event {}, user {}", ticketingOrdersPages.getNumberOfElements(), event.getEventId(), user.getUserId());
        }

        log.info("Successfully Get user orders for event {}, user {}", event.getEventId(), user.getUserId());
        return orderPageData;
    }

    /**
     * Set purchaser info
     * Get purchaser info from TicketHolderRequiredAttributes by ticket_holder_attributes_id and set in PurchaserInfo
     *
     */
    private void setPurchaserInfo(EventTickets eventTicket, Event event, PurchaserInfo purchaserInfo) {
        if (null != eventTicket) {
            List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
                    .getAllAttributesOrderByAttributeOrder(event);

            TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
            if (ticketHolderAttributes != null && StringUtils.isNotBlank(ticketHolderAttributes.getJsonValue())) {
                try {
                    TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
                    if (ticketAttributeValueDto != null) {
                        for (TicketHolderRequiredAttributes ticketHolderRequiredAttributes : holderRequiredAttributes) {
                            if (ticketHolderRequiredAttributes.getEnabledForTicketPurchaser()) {
                                if ("First Name".equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                    purchaserInfo.setFirstName(getAttributeValue1(ticketAttributeValueDto,
                                            ticketHolderRequiredAttributes.getName(), false,
                                            ticketHolderRequiredAttributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType())));
                                } else if ("Last Name".equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                    purchaserInfo.setLastName(getAttributeValue1(ticketAttributeValueDto,
                                            ticketHolderRequiredAttributes.getName(), false,
                                            ticketHolderRequiredAttributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType())));
                                } else if ("email".equalsIgnoreCase(ticketHolderRequiredAttributes.getName())) {
                                    purchaserInfo.setEmail(getAttributeValue1(ticketAttributeValueDto,
                                            ticketHolderRequiredAttributes.getName(), false,
                                            ticketHolderRequiredAttributes.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType())));
                                }
                            }
                        }
                    }
                }
                catch (Exception e) {
                    log.error("Exception while setting purchaser info JAXBException", e);
                }
            }
        }
    }

    @Override
    public OrderDtoV2 getOrderDetailsByBarcode(Event event, String barcode) {

        EventTickets eventTickets = eventTicketsCommonRepo.findByBarcodeId(barcode);
        StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);
        if (null != eventTickets) {
            TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(eventTickets.getTicketingOrder().getId(), event);
            List<EventTickets> eventTicketsList = eventCommonRepoService.findAllByOrder(ticketingOrder.getId());
            Map<Long, List<EventTickets>> ticketingOrderListMap = new HashMap<>();
            ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);
            List<EventTicketFeesDto> eventTicketFees = eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(Collections.singletonList(ticketingOrder.getId()));
            OrderDtoV2 orderDtoV2 = getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event,
                    ticketingHelperService.getPurchaserInfo(ticketingOrder, event), stripeDTO, NumberUtils.isNumberGreaterThanZero(eventTickets.getRecurringEventId()), 0L, eventTicketFees,false);
            orderDtoV2.setCurrency(event.getCurrency().getSymbol());
            orderDtoV2.setTimezoneId(event.getTimezoneId());
            orderDtoV2.setEquivalentTimezone(event.getEquivalentTimeZone());
            orderDtoV2.setId(ticketingOrder.getId());
            orderDtoV2.setTicketType(null!=eventTickets.getTicketingTypeId().getTicketTypeFormat()?eventTickets.getTicketingTypeId().getTicketTypeFormat().toString():null);
            return orderDtoV2;
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
        }
    }

    @Override
    public TicketAttributeDto getOrderDataByBarcode(Event event, String barCode, User user) {
        EventTickets eventTickets = eventTicketsCommonRepo.findByBarcodeId(barCode);
        if (null != eventTickets) {
            TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(eventTickets.getTicketingOrder().getId(), event);
            return getOrderDataById(event, ticketingOrder.getId(), user);
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST);
        }
    }

    public TicketAttributeDto getOrderDataById(Event event, Long orderId, User loginUser) {
        TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderId, event);
        return ticketingPurchaseService.getFormAttributes(false, ticketingOrder, event, orderId, loginUser, true,0L);
    }

    protected boolean isTicketingActive(Event event) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        return ticketing.getActivated();
    }

    protected OrderDtoV2 getOrderDtoV2(Map<Long, List<EventTickets>> ticketingOrderListMap, TicketingOrder ticketingOrder,
                                       Event event, PurchaserInfo purchaserInfo, StripeDTO stripeDTO, boolean isRecurringEvent, Long recurringEventId, List<EventTicketFeesDto> eventTicketFees,boolean isFetchDeletedOrder) {
        if (!isFetchDeletedOrder && !TicketingOrder.TicketingOrderStatus.PAID.equals(ticketingOrder.getStatus()) &&
                !TicketingOrder.TicketingOrderStatus.PARTIAL.equals(ticketingOrder.getStatus()) &&
                !TicketingOrder.OrderType.UNPAID.toString().equals(ticketingOrder.getStatus().toString()) &&
                !TicketingOrder.TicketingOrderStatus.PROCESSING.toString().equals(ticketingOrder.getStatus().toString()) &&
                !TicketingOrder.TicketingOrderStatus.PAYMENT_FAILED.toString().equals(ticketingOrder.getStatus().toString())) {
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.REFUND_ORDER_NOT_PAID);
        }
        OrderDtoV2 orderDto = new OrderDtoV2();
        orderDto.setId(ticketingOrder.getId());
        orderDto.setPurchaseDate(getDateInLocal(ticketingOrder.getOrderDate(),
                event.getEquivalentTimeZone(), Constants.DATE_FORMAT_WITH_AM_PM));
        orderDto.setOrderLastUpdated(getDateInLocal(ticketingOrder.getUpdatedAt(),
                event.getEquivalentTimeZone(), Constants.DATE_FORMAT_WITH_AM_PM));
        orderDto.setPurchaser(purchaserInfo);
        orderDto.setOrderType(ticketingOrder.getOrderType());
        orderDto.setNote(ticketingOrder.getNote());
        orderDto.setDeliveryMode(ticketingOrder.getDeliveryMode().getMode());
        orderDto.setCouponCode(null != ticketingOrder.getTicketingCoupon() ? ticketingOrder.getTicketingCoupon().getName() : Constants.STRING_EMPTY);
        String json = ticketingOrder.getRecSource();
        if (StringUtils.isNotBlank(json)) {
            orderDto.setUtmTrackSource(JsonMapper.stringtoObject(json, UTMTrackSourceDto.class));
        }

        double amount = 0;
        double refundedAmount = 0;
        List<EventTickets> allEventTickets = ticketingOrderListMap.get(ticketingOrder.getId());
        allEventTickets = CollectionUtils.isEmpty(allEventTickets) ? new ArrayList<>() : allEventTickets;
        if (isRecurringEvent) {
            if (recurringEventId > 0L) {
                allEventTickets = allEventTickets.stream().filter(e -> e.getRecurringEventId() != null && e.getRecurringEventId().equals(recurringEventId)
                        && !TicketStatus.DELETED.equals(e.getTicketStatus())).collect(Collectors.toList());
            }
            Optional<EventTickets> optionalEventTickets = allEventTickets.stream().filter(e ->
                    e.getRecurringEventId() != null && !TicketStatus.DELETED.equals(e.getTicketStatus())).findFirst();
            if (optionalEventTickets.isPresent()) {
                RecurringEvents recurringEvent = optionalEventTickets.get().getRecurringEvents();
                if (null != recurringEvent) {
                    orderDto.setRecurringEventDate(DateUtils.getFormattedDateString(recurringEvent.getRecurringEventStartDate()));
                }
            }
        }
        List<EventTickets> validEventTickets = new ArrayList<>();
        if (!isFetchDeletedOrder) {
            validEventTickets = allEventTickets.stream().filter(e -> !TicketStatus.DELETED.equals(e.getTicketStatus())).collect(Collectors.toList());
        } else {
            validEventTickets = allEventTickets;
        }
        long numberOfTicketTypeExcludingFreeType = validEventTickets.stream().filter(e -> e.getPaidAmount() > 0 || e.getRefundedAmount() > 0).count();

        List<AttendeeDto> attendeeDtos = getAllTicketAttendee(validEventTickets, stripeDTO, numberOfTicketTypeExcludingFreeType,eventTicketFees, event.getEquivalentTimeZone());
        List<AttendeeDtoV2> attendeeDtosV2 = new ArrayList<>();
        int numberOfTickets = validEventTickets.size();
        int ticketsRefunded = 0;
        boolean partiallRefunded = false;
        int ticketsCanceled = 0;
        for (EventTickets eventTicket : validEventTickets) {
            if(TicketStatus.CANCELED.equals(eventTicket.getTicketStatus())) {
                ticketsCanceled++;
            }
            if (TicketPaymentStatus.REFUNDED.equals(eventTicket.getTicketPaymentStatus())) {
                ticketsRefunded++;
                partiallRefunded = true;
            } else if(TicketPaymentStatus.PARTIALLY_REFUNDED.equals(eventTicket.getTicketPaymentStatus())){
                partiallRefunded = true;
            }
        }

        if (attendeeDtos != null && !attendeeDtos.isEmpty()) {
            for (AttendeeDto attendeeDto : attendeeDtos) {
                attendeeDtosV2.add(AttendeeDtoV2.convertToAttendeeDtoV2(attendeeDto));
                amount = amount + attendeeDto.getPaid();
                refundedAmount = refundedAmount + attendeeDto.getRefundedAmount();
            }
        }

        if(ticketsCanceled == numberOfTickets){
            orderDto.setStatus(Constants.TICKETING_STATUS_CANCELED);
        } else if (ticketsRefunded == numberOfTickets ) {
            orderDto.setStatus(Constants.TICKETING_STATUS_REFUNDED);
        } else if (partiallRefunded) {
            orderDto.setStatus(Constants.STATUS_TO_DISPLAY_PARTIALLY_REFUNDED);
        } else if (TicketingOrder.OrderType.UNPAID.toString().equals(ticketingOrder.getStatus().toString()) && !TicketingOrder.TicketingOrderStatus.PAID.equals(ticketingOrder.getStatus())) {
            orderDto.setStatus(TicketingOrder.OrderType.UNPAID.toString());
        } else if (TicketingOrder.TicketingOrderStatus.PARTIAL.equals(ticketingOrder.getStatus())) {
            orderDto.setStatus(TicketingOrder.TicketingOrderStatus.PARTIAL.toString());
        } else if (TicketingOrder.TicketingOrderStatus.PAYMENT_FAILED.equals(ticketingOrder.getStatus())) {
            orderDto.setStatus(TicketingOrder.TicketingOrderStatus.PAYMENT_FAILED.toString());
        } else  if (TicketingOrder.TicketingOrderStatus.PROCESSING.equals(ticketingOrder.getStatus())) {
            orderDto.setStatus(TicketingOrder.TicketingOrderStatus.PROCESSING.toString());
        }else{
            orderDto.setStatus(TicketingOrder.TicketingOrderStatus.PAID.toString());
        }
        orderDto.setAttendee(attendeeDtosV2);
        double totalCCFee = attendeeDtosV2.stream().mapToDouble(AttendeeDtoV2::getCcFee).sum();
        double totalTicketingFee = attendeeDtosV2.stream().mapToDouble(AttendeeDtoV2::getTicketingFee).sum();
        double totalSalesTaxFee = attendeeDtosV2.stream().mapToDouble(AttendeeDtoV2::getSalesTaxFee).sum();
        double totalVatTaxFee = attendeeDtosV2.stream().mapToDouble(AttendeeDtoV2::getVatTaxFee).sum();
        orderDto.setTotalAmount(GeneralUtils.getRoundValue(amount - refundedAmount));
        orderDto.setCcFee(totalCCFee);
        orderDto.setTicketingFee(totalTicketingFee);
        orderDto.setSalesTaxFee(totalSalesTaxFee);
        orderDto.setVatTaxFee(totalVatTaxFee);
        orderDto.setSpeakerOrder(ticketingOrder.isSpeakerOrder());
        orderDto.setStaffOrder(ticketingOrder.isStaffOrder());
        orderDto.setDisableTicketTransfer(ticketingOrder.isDisableTicketTransfer());
        return orderDto;
    }

    protected Unmarshaller getUnmashler() {
        JAXBContext jaxbContext = null;
        try {
            jaxbContext = JAXBContext.newInstance(TicketAttributeValueDto.class);
            return jaxbContext.createUnmarshaller();
        } catch (JAXBException e) {
            log.info(e.getLocalizedMessage(), e);
        }
        return null;
    }

    protected TicketAttributeValueDto unmashler(String xml, Unmarshaller unmarshaller) throws JAXBException {
        if (unmarshaller == null) {
            throw new JAXBException("unmarshaller not found!");
        }

        StringReader reader = new StringReader(xml);
        return (TicketAttributeValueDto) unmarshaller.unmarshal(reader);
    }

    protected List<AttendeeDto> getAllTicketAttendee(List<EventTickets> eventTickets, StripeDTO stripeDTO, long numberOfTicketTypeExcludingFreeType, List<EventTicketFeesDto> eventTicketFees, String equivalentTimeZone) {
        List<AttendeeDto> attendeeDtos = new ArrayList<>();
        if (eventTickets == null) {
            return attendeeDtos;
        }
        List<Long> eventTicketIds = eventTickets.stream().map(EventTickets::getId).collect(Collectors.toList());
        List<EventTicketTransferDetail> eventTicketTransferDetails=eventTicketTransferDetailService.getAllByEventTicketIds(eventTicketIds);
        Map<Long, List<EventTicketTransferDetail>> eventTicketTransferDetailMap = eventTicketTransferDetails.stream()
                .collect(Collectors.groupingBy(EventTicketTransferDetail::getEventTicketId));

        for (EventTickets eventTicket : eventTickets) {

            AttendeeDto attendeeDto = new AttendeeDto();
            TicketingOrder ticketingOrder = eventTicket.getTicketingOrder();
            TicketingType ticketType = eventTicket.getTicketingTypeId();
            setCCfeeAndTicketingFees(stripeDTO, numberOfTicketTypeExcludingFreeType, eventTicket, attendeeDto,eventTicketFees);
            if(eventTicket.getGuestOfBuyer()
                    && (StringUtils.isEmpty(eventTicket.getHolderFirstName()) || StringUtils.isEmpty(eventTicket.getHolderLastName()))) {
                attendeeDto.setFirstName(GUEST_OF+STRING_BLANK+eventTicket.getTicketPurchaserId().getFirstName());
                attendeeDto.setLastName(eventTicket.getTicketPurchaserId().getLastName());
            }else{
                attendeeDto.setFirstName(eventTicket.getHolderFirstName());
                attendeeDto.setLastName(eventTicket.getHolderLastName());
            }
            attendeeDto.setEventTicketingId(eventTicket.getId());
            attendeeDto.setBlocked(RecordStatus.BLOCK.equals(eventTicket.getRecordStatus()));
            attendeeDto.setPaid(eventTicket.getPaidAmount());
            attendeeDto.setTicketPrice(eventTicket.getTicketPrice());
            attendeeDto.setRefundedAmount(eventTicket.getRefundedAmount());
            attendeeDto.setTicketPaymentStatus(eventTicket.getTicketPaymentStatus());
            attendeeDto.setQty(1);
            if (eventTicket.getTicketingTypeId().getTicketType().name().equals(PAID)) {
                attendeeDto.setPaidTicket(true);
            }
            setTicketType(eventTicket, attendeeDto, eventTicket.getTicketingTable());
            attendeeDto.setSeatNumber(eventTicket.getSeatNumber());
            attendeeDto.setStatus(eventTicket.getTicketStatus().getStatus());
            attendeeDto.setTicketStatus(eventTicket.getTicketPaymentStatus().name());
            attendeeDto.setBarcode(eventTicket.getBarcodeId());
            attendeeDto.setDataType(eventTicket.getDataType());
            attendeeDto.setEmail(eventTicket.getHolderEmail());
            attendeeDto.setNonTransferable(ticketingOrder.isDisableTicketTransfer() || ticketType.isNonTransferable() || eventTicket.isNonTransferable());
            attendeeDto.setUserId(String.valueOf(eventTicket.getHolderUserId().getUserId()));
            if (null != eventTicket.getHolderPhoneNumber()) {
                attendeeDto.setPhoneNumber(eventTicket.getHolderPhoneNumber());
            }
            if (eventTicketTransferDetailMap.containsKey(eventTicket.getId())){
                attendeeDto.setResendTicketExchangeEmail(true);
            }

            attendeeDto.setLastUpdated(getDateInLocal(eventTicket.getUpdatedAt(),
                    equivalentTimeZone, Constants.DATE_FORMAT_WITH_AM_PM));

            attendeeDtos.add(attendeeDto);
        }
        return attendeeDtos;
    }

    protected List<EventDetailsPastOrganizerDto> getAllPastEventInfoOfUserByOrganizer(Set<EventInfoDto> eventInfoDtoSet,
                                                                                      List<Long> eventIdsOfUserParticipated) {
        List<EventDetailsPastOrganizerDto> eventDetailsPastOrganizerDtos = new ArrayList<>();
        if (eventInfoDtoSet == null || eventIdsOfUserParticipated == null) {
            return eventDetailsPastOrganizerDtos;
        }

        for (EventInfoDto eventInfoDto : eventInfoDtoSet) {
            if (eventIdsOfUserParticipated.contains(eventInfoDto.getEventId())) {
                EventDetailsPastOrganizerDto eventDetailsPastOrganizerDto = new EventDetailsPastOrganizerDto();
                eventDetailsPastOrganizerDto.setEventName(eventInfoDto.getName());
                eventDetailsPastOrganizerDto.setEventUrl(eventInfoDto.getEventURL());
                eventDetailsPastOrganizerDto.setEventStartDate(eventInfoDto.getEventStartDate());
                eventDetailsPastOrganizerDto.setEventEndDate(eventInfoDto.getEventEndDate());

                eventDetailsPastOrganizerDtos.add(eventDetailsPastOrganizerDto);
            }
        }
        return eventDetailsPastOrganizerDtos;
    }

    @Override
    public void setCCfeeAndTicketingFees(StripeDTO stripeDTO, long numberOfTicketTypeExcludingFreeType, EventTickets eventTicket, AttendeeDto attendeeDto, List<EventTicketFeesDto> eventTicketFees) {
        if (numberOfTicketTypeExcludingFreeType > 0) {
            double ticketingFee = eventTicket.getAeFeeAmount() + eventTicket.getWlAFeeAmount() + eventTicket.getWlBFeeAmount()
                    - eventTicket.getRefundedAEFee() - eventTicket.getRefundedWlFee();
            double ccFee = 0;
            double sum = eventTicketFees.stream().filter(eventTicketFeesDto -> eventTicketFeesDto.getEventTicketId().equals(eventTicket.getId())).mapToDouble(EventTicketFeesDto::getCcFeeAmount).sum();
            //For now, we need to display $0.00 when total amount is refunded that is why here we write paidAmount - refundedAmount.
            if (StringUtils.isNotBlank(eventTicket.getChargeId())) {//NOSONAR
                if (TicketPaymentStatus.REFUNDED.equals(eventTicket.getTicketPaymentStatus())) {
                    ccFee = (GeneralUtils.getRoundValue(eventTicket.getPaidAmount() - eventTicket.getRefundedAmount()));
                } else {
                    ccFee = sum;
                }
                attendeeDto.setCardPayment(true);
            }
            double salesTaxFee = eventTicket.getSalesTaxFee() - eventTicket.getRefundedSalesTaxFee();
            double vatTaxFee = eventTicket.getVatTaxFee() - eventTicket.getRefundedVatTaxFee();

            attendeeDto.setCcFee(DoubleHelper.roundValueTwoDecimal(ccFee));
            attendeeDto.setTicketingFee(DoubleHelper.roundValueTwoDecimal(ticketingFee));
            attendeeDto.setSalesTaxFee(DoubleHelper.roundValueTwoDecimal(salesTaxFee));
            attendeeDto.setVatTaxFee(vatTaxFee);
        }
    }

    @Override
    public List<EventDetailsPastOrganizerDto> getPastEventInfoSameOrganizer(Long eventId, Long purchaserUserId) {
        String eventOrganizerPageUrl = eventRepoService.findOrganizerPageUrlByEventId(eventId);
        if (null != eventOrganizerPageUrl) {
            Set<EventInfoDto> eventInfoDtoSet = organizerService.getEventsByOrganizerURL(eventOrganizerPageUrl);
            List<Long> eventIdsOfUserParticipated = eventRepoService.findEventIdsOfUserParticipated(purchaserUserId);
            return getAllPastEventInfoOfUserByOrganizer(eventInfoDtoSet, eventIdsOfUserParticipated);
        }
        return Collections.emptyList();
    }

    protected void setTicketType(EventTickets eventTicket, AttendeeDto attendeeDto, TicketingTable ticketingTable) {
        if (ticketingTable != null) {
            TicketingType ticketingType = eventTicket.getTicketingTypeId();
            String bundleType = ticketingType.getBundleType().toString();
            attendeeDto.setBundleType(bundleType);
            attendeeDto.setTicketType(ticketingType.getTicketTypeName());
            attendeeDto.setTable(true);
        } else {
            attendeeDto.setTicketType(eventTicket.getTicketingTypeId().getTicketTypeName());
            attendeeDto.setTable(false);
        }
        attendeeDto.setTicketTypeId(eventTicket.getTicketingTypeId().getId());
    }

    @Override
    public boolean getEventShowPopUpStatus(User user, Event event) {
        boolean showPopup = eventTicketsRepository.getEventShowPopUpStatus(event.getEventId(), user.getEmail());
        log.info("getEventShowPopUpStatus showPopup:{}", showPopup);
        return showPopup;
    }

    @Transactional
    @Override
    public void updateShowPopUpWhileEnteringInEvent(User user, Event event) {
        List<EventTickets> list = eventTicketsRepository.getAllEventsBasedOnPayLaterOption(event, user.getEmail());
        list.forEach(entity->{
            entity.setShowPopup(false);
            eventTicketsRepository.save(entity);
        });
    }
}
