package com.accelevents.services.impl;


import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.billing.chargebee.dto.RegistrationConfigDto;
import com.accelevents.billing.chargebee.enums.ChargebeeEntitlements;
import com.accelevents.billing.chargebee.repo.ChargesPurchasedRepoService;
import com.accelevents.billing.chargebee.repo.EventPlanConfigRepoService;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.common.dto.LobbyDetailsDto;
import com.accelevents.common.dto.LobbyImageDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.MUXLivestreamAssetDetails;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.virtual.LobbyImage;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.MultiLanguageLabelRepo;
import com.accelevents.repositories.OrganizerRepository;
import com.accelevents.repositories.VirtualEventSettingsRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.MultiLanguageLabelRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.session_speakers.dto.CaptionsDto;
import com.accelevents.session_speakers.dto.MuxAssetDTO;
import com.accelevents.session_speakers.services.MUXLivestreamAssetRepoService;
import com.accelevents.session_speakers.services.MUXLivestreamAssetService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.ticketing.dto.EntitlementsAvailabilityDTO;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.JsonMapper;
import com.accelevents.utils.NumberUtils;
import com.accelevents.virtualevents.dto.*;
import com.cloudinary.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stripe.exception.StripeException;
import com.stripe.model.Invoice;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.PROFESSIONAL_2023;
import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.STARTER_2023;
import static com.accelevents.domain.enums.EnumSessionFormat.BREAKOUT_SESSION;
import static com.accelevents.domain.enums.EnumSessionFormat.MAIN_STAGE;
import static com.accelevents.domain.enums.EventFormat.IN_PERSON;
import static com.accelevents.domain.enums.EventFormat.VIRTUAL;
import static com.accelevents.enums.PlanConfigNames.FREE_PLAN;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.CAN_NOT_GENERATE_STREAM_IN_PROGRESS;
import static com.accelevents.exceptions.NotAcceptableException.VirtualEventExceptionMsg.SETTING_NOT_ALLOWED;
import static com.accelevents.utils.Constants.*;

@Service
public class VirtualEventServiceImpl implements VirtualEventService {

    private static final Logger log = LoggerFactory.getLogger(VirtualEventServiceImpl.class);
    private static final TypeReference<List<EntitlementsDTO>> entitlementsTypeRef = new TypeReference<>() {};

    @Autowired
    private VirtualEventSettingsRepository virtualEventSettingsRepository;

    @Autowired
    private EventDesignDetailService eventDesignDetailService;

    @Autowired
    private MuxService muxService;

    @Autowired
    private StripePaymentService stripePaymentService;

    @Autowired
    private MUXLivestreamAssetService muxLivestreamAssetService;

    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private ROVirtualEventService roVirtualEventService;

    @Autowired
    private GetStreamService getStreamService;

    @Autowired
    private VirtualEventService virtualEventService;

    @Autowired
    private ChallengeConfigService challengeConfigService;

    @Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;

    @Autowired
    private LobbyImageService lobbyImageService;

    @Autowired
    private EventStreamingService eventStreamingService;

    @Autowired
    private MultiLanguageMasterService multiLanguageMasterService;

    @Autowired
    private MultiLanguageLabelRepoService multiLanguageLabelRepoService;

    @Autowired
    private MultiLanguageLabelRepo multiLanguageLabelRepo;

    @Autowired
    private SessionRepoService sessionRepoService;

    @Autowired
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;

    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;
    @Autowired
    private OrganizerRepository organizerRepository;

    @Autowired
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Autowired
    private ROEventService roEventService;

    @Autowired
    private EventLiveStreamingConfigurationService eventLiveStreamingConfigurationService;
    @Autowired
    private ChargebeePlanService chargebeePlanService;

    @Autowired
    private EventTicketsRepository eventTicketsRepository;

    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;

    @Value("${application.environment}")
    private String environment;

    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;


    @Override
    public boolean checkLimitOfWantsToLearnForHost(Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        if(null != virtualEventSettings && !StringUtils.isEmpty(virtualEventSettings.getWantsToLearn())) {
            List<String> wantsToLearnList = Arrays.asList(virtualEventSettings.getWantsToLearn().split(","));
            return (wantsToLearnList.size() < 100);
        }else
            return true;
    }

    @Override
    public void deleteWantsToLearn(String wantsToLearnForDelete,Long eventId){
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(eventId);
        if(null != virtualEventSettings && !StringUtils.isEmpty(virtualEventSettings.getWantsToLearn()) && virtualEventSettings.getWantsToLearn().contains(wantsToLearnForDelete)){
            List<String> wantsToLearnList = Arrays.stream(virtualEventSettings.getWantsToLearn().split(",")).filter(i -> !i.equalsIgnoreCase(wantsToLearnForDelete)).collect(
                    Collectors.toList());
            virtualEventSettings.setWantsToLearn(String.join(",", wantsToLearnList));
            virtualEventSettingsRepoService.save(virtualEventSettings);
        }else{
            throw new NotFoundException(NotFoundException.NotFound.WANTS_TO_LEARN_NOT_FOUND);
        }
    }

    @Override
    public List<String> getAllWantsToLearnTagsOfEvent(Long eventId){
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(eventId);
        if(null != virtualEventSettings && !StringUtils.isEmpty(virtualEventSettings.getWantsToLearn()))
            return Arrays.asList(virtualEventSettings.getWantsToLearn().split(","));
        else
            return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public void addOrUpdateWantsToLearn(String wantsToLearn, Event event){
        if(!StringUtils.isEmpty(wantsToLearn)){
            VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
            List<String> wantsToLearnList ;
            String newWantsToLearn;
            if(null != virtualEventSettings) {
                if (StringUtils.isNotBlank(virtualEventSettings.getWantsToLearn())) {
                    wantsToLearnList = Arrays.asList(virtualEventSettings.getWantsToLearn().split(","));
                    if (wantsToLearnList.contains(wantsToLearn))
                        throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.WANTS_TO_LEARN_ALREADY_EXISTS);
                    else
                        newWantsToLearn = virtualEventSettings.getWantsToLearn().concat(",").concat(wantsToLearn);
                } else {
                    newWantsToLearn = wantsToLearn;
                }
                virtualEventSettings.setWantsToLearn(newWantsToLearn);
                virtualEventSettingsRepoService.save(virtualEventSettings);
            }else{
                VirtualEventSettingsDTO virtualEventSettingsDTO = new VirtualEventSettingsDTO().createVirtualEventSettingDTO(null);
                virtualEventSettingsDTO.setWantsToLearn(wantsToLearn);
                createVirtualEventsSettings(event,virtualEventSettingsDTO);
            }

        }else{
            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.WANTS_TO_LEARN_EMPTY_OR_NULL);
        }
    }

    @Override
    @Transactional
    public void updateVirtualEventsSettings(Event event,Boolean flag,String type) {
        VirtualEventSettings settings = virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId());
            log.info("VirtualEventServiceImpl | updateVirtualEventsSettings eventId {},flag {},type {}",event.getEventId(),flag,type);
            if(IS_ACCEPT_DIRECT_MESSAGES.equalsIgnoreCase(type)) {
                settings.setAcceptDirectMessages(flag);
            }else if (IS_PRONOUN_TYPE.equalsIgnoreCase(type)) {
                settings.setPronounsFieldEnabled(flag);
            }else if (IS_HOLDER_INFO_VISIBLE_TO_ATTENDEES.equalsIgnoreCase(type)){
                settings.setHolderInfoVisibleToAttendees(flag);
            }
            virtualEventSettingsRepoService.save(settings);
    }

    @Override
    @Transactional(rollbackFor = { Exception.class }, isolation = Isolation.READ_COMMITTED)
    public VirtualEventSettings createVirtualEventsSettings(Event event, VirtualEventSettingsDTO virtualEventsSettingsDTO) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        if(null != virtualEventSettings) {
            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.VIRTUAL_EVENT_ALREADY_CREATED);
        }
        virtualEventSettings = virtualEventsSettingsDTO.createEntity(event);

        log.info("VirtualEventServiceImpl saveVirtualEventsSettings eventId {} ",event.getEventId());
        virtualEventSettingsRepoService.save(virtualEventSettings);
        log.info("VirtualEventServiceImpl ||  enableGamification eventId {} ",event.getEventId());
        virtualEventService.enableGamification(event, true);
        log.info("VirtualEventServiceImpl ||  addDefaultEventChallenge eventId {} ",event.getEventId());
        challengeConfigService.addDefaultEventChallenge(event);
        return virtualEventSettings;
    }

    @Override
    public void updateVirtualEventsSettings(Event event,User user, VirtualEventSettingsDTO virtualEventsSettingsDTO, Long id) {
        VirtualEventSettings virtualEventSettings = getVirtualEventSettings(id);
        log.info("Virtual Event Settings update requested by user {}, old settings {}, new DTO {}" ,user.getUserId(), virtualEventSettings, virtualEventsSettingsDTO);
        eventDesignDetailService.updateSessionsSpeakersBasedOnEventType((virtualEventsSettingsDTO.isStageEnabled() || virtualEventsSettingsDTO.isSessionEnabled() || virtualEventsSettingsDTO.isNetworkingEnabled()),event);

        if(virtualEventSettings.isStageEnabled() && !virtualEventsSettingsDTO.isStageEnabled()){
            List<Session> sessions = sessionRepoService.findByEventAndFormat(event.getEventId(), List.of(MAIN_STAGE));
            sessions.forEach(session -> session.setFormat(BREAKOUT_SESSION));
            sessionRepoService.saveAll(sessions);
        }
        Map<String, List<VirtualEventTabsDTO>>  virtualEventTabs = virtualEventsSettingsDTO.getVirtualEventTabs();
        if (virtualEventTabs.containsKey(Constants.LOBBY_TAB)) {
            List<VirtualEventTabsDTO> virtualEventTabsDTOList = virtualEventTabs.get(Constants.LOBBY_TAB);
            for (VirtualEventTabsDTO virtualEventTabsDTO : virtualEventTabsDTOList) {
                if(!virtualEventTabsDTO.isOpenCustomTab() && StringUtils.isNotBlank(virtualEventTabsDTO.getUrl())){
                    boolean isValidUrl = GeneralUtils.isValidURL(virtualEventTabsDTO.getUrl());
                    if(!isValidUrl){
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_URL);
                    }
                }
            }
        }

        // Save disabled lobby chat configuration
        RightSidebarConfigDto rightSidebarConfigDto = getRightSidebarConfigDtoFromGson(virtualEventSettings.getRightSidebarConfig());
        if(rightSidebarConfigDto.isEnabledLobbyChat() == !virtualEventsSettingsDTO.isEnabledLobbyChat()) {
            getStreamService.updateChannelName(EVENT_.concat(String.valueOf(event.getEventId())), event.getName(), MESSAGING, !virtualEventsSettingsDTO.isEnabledLobbyChat(), event.getEventId());
            log.info("Update the channel id: {} name: {} with disable chat toggle: {} of the eventId: {} ", EVENT_.concat(String.valueOf(event.getEventId())), event.getName(), !virtualEventsSettingsDTO.isEnabledLobbyChat(), event.getEventId());
        }
        if (virtualEventSettings.isLobbyLiveStream() != virtualEventsSettingsDTO.isLobbyLiveStream()) {
            validateLobbyLiveStream(event.getEventId());
        }
        checkKeyValueOfOldAndNewOfVirtualTab(event, virtualEventsSettingsDTO, virtualEventSettings, user);
        changeColorsOnChangeTheme(virtualEventsSettingsDTO, virtualEventSettings);
        virtualEventSettings = virtualEventsSettingsDTO.updateEntity(virtualEventSettings);
        virtualEventSettingsRepoService.save(virtualEventSettings);
        log.info("Virtual Event Settings updated");
        if(virtualEventSettings.isGamificationNewFlow()) {
            challengeConfigService.addDefaultEventChallenge(event);
        }
    }

    @Override
    public void resetColorConfiguration(Event event, User user, Long id) {
        VirtualEventSettings virtualEventSettings = getVirtualEventSettings(id);
        VirtualEventSettingsDTO virtualEventSettingsDTO = new VirtualEventSettingsDTO().createVirtualEventSettingDTO(virtualEventSettings);
        setDefaultColorsByTheme(virtualEventSettingsDTO);
        Gson gson = new Gson();
        virtualEventSettings.setButtonTextColorConfiguration(gson.toJson(virtualEventSettingsDTO.getButtonTextColorConfiguration(), ButtonTextColorConfigurationDto.class));
        virtualEventSettings.setHubNavigationBackgroundColor(virtualEventSettingsDTO.getHubNavigationBackgroundColor());
        virtualEventSettings.setHubNavigationTextColor(virtualEventSettingsDTO.getHubNavigationTextColor());
        virtualEventSettings.setHubMainBackgroundColor(virtualEventSettingsDTO.getHubMainBackgroundColor());
        virtualEventSettings.setBookMarkButtonColor(virtualEventSettingsDTO.getBookMarkButtonColor());
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    private void changeColorsOnChangeTheme(VirtualEventSettingsDTO virtualEventsSettingsDTO, VirtualEventSettings virtualEventSettings) {
        if (!virtualEventSettings.getTheme().equals(virtualEventsSettingsDTO.getTheme())) {
            setDefaultColorsByTheme(virtualEventsSettingsDTO);
        }
    }

    @Override
    public void setDefaultColorsByTheme(VirtualEventSettingsDTO virtualEventsSettingsDTO) {
        if (Theme.DARK.equals(virtualEventsSettingsDTO.getTheme())) {
            setDefaultDarkThemeColors(virtualEventsSettingsDTO);
        } else {
            setDefaultLightThemeColors(virtualEventsSettingsDTO);
        }
    }

    private void setDefaultLightThemeColors(VirtualEventSettingsDTO virtualEventsSettingsDTO) {
        ButtonTextColorConfigurationDto oldButtonColors = virtualEventsSettingsDTO.getButtonTextColorConfiguration();
        ButtonTextColorConfigurationDto colorConfiguration = new ButtonTextColorConfigurationDto();
        colorConfiguration.setVirtualEventHubHeaderColor(HASH_COLOR_FFFFFF );
        colorConfiguration.setVirtualEventHubHeaderTextColor(HASH_COLOR_1E2137);
        colorConfiguration.setHubSessionNameColor(HASH_COLOR_1E2137);
        colorConfiguration.setHubSpeakerNameTextColor(HASH_COLOR_1E2137);
        colorConfiguration.setHubSessionHeaderColor(HASH_COLOR_1E2137);
        colorConfiguration.setHubAllSessionTextColor(HASH_COLOR_1E2137);
        colorConfiguration.setVirtualHubTabTextColor(DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR);
        colorConfiguration.setJoinButtonColor(DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR);
        colorConfiguration.setAgendaTabsColor(oldButtonColors.getAgendaTabsColor());
        colorConfiguration.setExpoTabsColor(oldButtonColors.getExpoTabsColor());
        colorConfiguration.setNumberOfSessionColor(oldButtonColors.getNumberOfSessionColor());
        colorConfiguration.setNumberOfSessionTextColor(oldButtonColors.getNumberOfSessionTextColor());
        colorConfiguration.setChatLinkColor(oldButtonColors.getChatLinkColor());
        colorConfiguration.setEnterEventButtonColor(oldButtonColors.getEnterEventButtonColor());

        virtualEventsSettingsDTO.setButtonTextColorConfiguration(colorConfiguration);
        virtualEventsSettingsDTO.setHubNavigationBackgroundColor(DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR);
        virtualEventsSettingsDTO.setHubNavigationTextColor(HASH_COLOR_FFFFFF);
        virtualEventsSettingsDTO.setHubMainBackgroundColor(DEFAULT_HUB_MAIN_BACKGROUND_COLOR);
        virtualEventsSettingsDTO.setBookMarkButtonColor(DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR);
    }

    private void setDefaultDarkThemeColors(VirtualEventSettingsDTO virtualEventsSettingsDTO) {
        ButtonTextColorConfigurationDto oldButtonColors = virtualEventsSettingsDTO.getButtonTextColorConfiguration();
        ButtonTextColorConfigurationDto colorConfiguration = new ButtonTextColorConfigurationDto();
        colorConfiguration.setVirtualEventHubHeaderColor(HEX_COLOR_1F1F1F);
        colorConfiguration.setVirtualEventHubHeaderTextColor(HASH_COLOR_FFFFFF);
        colorConfiguration.setHubSessionNameColor(HASH_COLOR_FFFFFF);
        colorConfiguration.setHubSpeakerNameTextColor(HASH_COLOR_FFFFFF);
        colorConfiguration.setHubSessionHeaderColor(HASH_COLOR_FFFFFF);
        colorConfiguration.setHubAllSessionTextColor(HASH_COLOR_FFFFFF);
        colorConfiguration.setVirtualHubTabTextColor(DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR);
        colorConfiguration.setJoinButtonColor(DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR);
        colorConfiguration.setAgendaTabsColor(oldButtonColors.getAgendaTabsColor());
        colorConfiguration.setExpoTabsColor(oldButtonColors.getExpoTabsColor());
        colorConfiguration.setNumberOfSessionColor(oldButtonColors.getNumberOfSessionColor());
        colorConfiguration.setNumberOfSessionTextColor(oldButtonColors.getNumberOfSessionTextColor());
        colorConfiguration.setChatLinkColor(oldButtonColors.getChatLinkColor());
        colorConfiguration.setEnterEventButtonColor(oldButtonColors.getEnterEventButtonColor());

        virtualEventsSettingsDTO.setButtonTextColorConfiguration(colorConfiguration);
        virtualEventsSettingsDTO.setHubNavigationBackgroundColor(HEX_COLOR_1F1F1F);
        virtualEventsSettingsDTO.setHubNavigationTextColor(HASH_COLOR_FFFFFF);
        virtualEventsSettingsDTO.setHubMainBackgroundColor(HEX_COLOR_161616);
        virtualEventsSettingsDTO.setBookMarkButtonColor(DEFAULT_HUB_NAVIGATION_BACKGROUND_COLOR);
    }


    public void checkKeyValueOfOldAndNewOfVirtualTab(Event event, VirtualEventSettingsDTO virtualEventsSettingsDTO, VirtualEventSettings virtualEventSettings, User user) {
        if (virtualEventSettings.getVirtualTabs() != null && virtualEventsSettingsDTO.getVirtualEventTabs() != null) {

            if (virtualEventsSettingsDTO.getVirtualEventTabs().containsKey(LOBBY_TAB) || virtualEventsSettingsDTO.getVirtualEventTabs().containsKey(LEFT_SIDE_NAV_MENU))
            {
                String oldVirtualTab = virtualEventSettings.getVirtualTabs();
                Map<String, List<VirtualEventTabsDTO>> oldVirtualTabsInMap = challengeConfigService.getVirtualTabsInMap(oldVirtualTab);
                List<VirtualEventTabsDTO> oldLobbyTab = new ArrayList<>();

                if(oldVirtualTabsInMap != null && oldVirtualTabsInMap.get(LOBBY_TAB) != null) {
                    oldLobbyTab = oldVirtualTabsInMap.get(LOBBY_TAB);
                }

                List<VirtualEventTabsDTO> virtualEventTabsDTOListOfLobbyTab = virtualEventsSettingsDTO.getVirtualEventTabs().get(LOBBY_TAB);
                List<String> newLobbyValueList = virtualEventTabsDTOListOfLobbyTab.stream().map(VirtualEventTabsDTO::getValue).collect(Collectors.toList());
                List<String> valueListOfOldLobby = oldLobbyTab.stream().map(VirtualEventTabsDTO::getValue).collect(Collectors.toList());

                if (!newLobbyValueList.equals(valueListOfOldLobby)) {
                    checkDuplicateValueOfLobbyTab(virtualEventsSettingsDTO.getVirtualEventTabs(), virtualEventSettings, event);
                }

                List<VirtualEventTabsDTO> virtualEventTabsDTOListOfLeftSideNavMenu = virtualEventsSettingsDTO.getVirtualEventTabs().get(LEFT_SIDE_NAV_MENU);
                List<VirtualEventTabsDTO> oldLeftSideNavMenu = oldVirtualTabsInMap.get(LEFT_SIDE_NAV_MENU);

                List<String> newLeftSideMenuValueList = virtualEventTabsDTOListOfLeftSideNavMenu.stream().map(VirtualEventTabsDTO::getValue).collect(Collectors.toList());
                List<String> valueListOfLeftSideMenu = oldLeftSideNavMenu.stream().map(VirtualEventTabsDTO::getValue).collect(Collectors.toList());

                if (!newLeftSideMenuValueList.equals(valueListOfLeftSideMenu)) {
                    if(!virtualEventService.checkNavigationMenuEntitlements(event, user)){
                        throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.NAVIGATION_MENU_NOT_AVAILABLE);
                    }
                    checkDuplicateValueOfNavMenuBar(virtualEventsSettingsDTO.getVirtualEventTabs(), virtualEventSettings, event);

                }
            }
        }
    }

    private RightSidebarConfigDto getRightSidebarConfigDtoFromGson(String rightSidebarConfig) {
        Gson gson = new Gson();
        return org.apache.commons.lang3.StringUtils.isBlank(rightSidebarConfig) ? new RightSidebarConfigDto() : gson.fromJson(rightSidebarConfig, RightSidebarConfigDto.class);
    }


    private void validateLobbyLiveStream(long eventId) {
        EventPlanConfig planConfig = eventPlanConfigRepoService.findByEventId(eventId);
        if (FREE_PLAN.getName().contains(planConfig.getPlanConfig().getPlanName())) {
            throw new NotAcceptableException(SETTING_NOT_ALLOWED);
        }
    }

    @Override
    public VirtualEventSettings getVirtualEventSettings(Long id) {
        return virtualEventSettingsRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(NotFoundException.VirtualEventNotFound.VIRTUAL_EVENT_SETTINGS_NOT_FOUND));
    }


    public VirtualEventSettings getVirtualEventSettingsByEventIdOrThrowError(Long eventId){
        return virtualEventSettingsRepository.findVirtualEventSettingByEventId(eventId)
                .orElseThrow(() -> new NotFoundException(NotFoundException.VirtualEventNotFound.VIRTUAL_EVENT_SETTINGS_NOT_FOUND));
    }


    @Override
    public VirtualEventSettingsDTO getVirtualEventSettingsById(Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        VirtualEventSettingsDTO virtualEventSettingsDTO = new VirtualEventSettingsDTO().createVirtualEventSettingDTO(virtualEventSettings);
        virtualEventSettingsDTO.setSponsorSection(eventDesignDetailService.getSponsorDetailsByEvent(event));
        List<MUXLivestreamAssetDetails> muxLivestreamAssetDetailsList =  muxLivestreamAssetRepoService.findAllByEventIdAndDefaultPlaybackTrueAndSessionIdNullAndNetworkingLoungeIdNullAndEventStreamIdNull(event.getEventId());
        if(!muxLivestreamAssetDetailsList.isEmpty()){
            MUXLivestreamAssetDetails muxLivestreamAssetDetails = muxLivestreamAssetDetailsList.get(0);
            virtualEventSettingsDTO.setWelcomeMessageVideo(muxLivestreamAssetDetails.getPlaybackId());
            virtualEventSettingsDTO.setWelcomeMessageVideoS3Key(muxLivestreamAssetDetails.getS3AssetKey());
            virtualEventSettingsDTO.setPlayBackRestrictionToken(muxLivestreamAssetDetails.getPlayBackRestrictionToken());
            virtualEventSettingsDTO.setThumbnailRestrictionToken(muxLivestreamAssetDetails.getThumbnailRestrictionToken());
        }
        if(virtualEventSettingsDTO.isLobbyLiveStream()) {
            EventStreaming eventStreaming = eventStreamingService.getEventStreamingByEventIdAndType(event.getEventId(), StreamType.LOBBY);
            if(eventStreaming != null){
                LiveVideoStreamDTO liveVideoStreamDTO = new LiveVideoStreamDTO(eventStreaming);
                muxLivestreamAssetRepoService.findFirstByEventStreamIdOrderByIdDesc(eventStreaming.getId()).ifPresent(muxLivestreamAssetDetails -> {
                    liveVideoStreamDTO.setS3AssetKey(muxLivestreamAssetDetails.getS3AssetKey());
                });
                virtualEventSettingsDTO.setLobbyPageStreamInfo(liveVideoStreamDTO);

            }
        }
        return virtualEventSettingsDTO;

    }

    @Override
    public void resetLobbyLiveStream(Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        if(virtualEventSettings != null) {
            virtualEventSettings.setLobbyLiveStream(false);
            virtualEventSettingsRepoService.save(virtualEventSettings);
        }
    }

    @Override
    public VirtualEventSettingsHostPageDTO getVirtualEventSettingsHostPageById(Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        return new VirtualEventSettingsHostPageDTO().createVirtualEventSettingsHostPageDTO(virtualEventSettings);
    }

    @Override
    public void enableLateJoinAllow(Event event, boolean allowLateJoin) {
        if (Boolean.TRUE.equals(allowLateJoin)) {
            validateEventHaveHigherPlanThanScalePlan(event);
        }
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        virtualEventSettings.setAllowLateJoin(allowLateJoin);
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    public void enableSessionSummeryPreview(Event event, boolean showSpeakerSummery) {
        validateEventHaveHigherPlanThanScalePlan(event);

        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        virtualEventSettings.setShowSpeakerSummery(showSpeakerSummery);
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    private void validateEventHaveHigherPlanThanScalePlan(Event event){
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(event.getEventId());
        List<String> allowedPlans = new ArrayList<>();
        allowedPlans.add("Professional");
        allowedPlans.add("Enterprise");
        allowedPlans.add("WhiteLabel");
        if(eventPlanConfig!=null && !allowedPlans.contains(eventPlanConfig.getPlanConfig().getPlanName()) && Boolean.FALSE.equals(eventPlanConfig.getPlanConfig().isLatestPlan()) && !IN_PERSON.equals(event.getEventFormat())){
            throw new NotAcceptableException(SETTING_NOT_ALLOWED);
        }
    }

    @Override
    public String getCustomerId(Event event){
        return virtualEventSettingsRepository.findCustomerIdByEventId(event.getEventId());
    }

    @Override
    public LobbyDetailsDto getVirtualEventDetails(Event event, User user) {
        LobbyDetailsDto lobbyDetailsDto = new LobbyDetailsDto();
        lobbyDetailsDto.setEventId(event.getEventId());
        lobbyDetailsDto.setEventName(event.getName());
        lobbyDetailsDto.setEventUrl(event.getEventURL());
        lobbyDetailsDto.setSponsorSection(eventDesignDetailService.getSponsorDetailsByEvent(event));
        return lobbyDetailsDto;
    }

    @Override
    public StreamKeyAndRtmpUrlDto createStreamKey(Event event, boolean regenerate) {
        VirtualEventSettings settings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        EventLiveStreamingConfiguration eventLiveStreamingConfiguration = eventLiveStreamingConfigurationService.findByEventId(event.getEventId());
        StreamKeyAndRtmpUrlDto dto= new StreamKeyAndRtmpUrlDto();
        if((StringUtils.isBlank(settings.getStreamKey()) && StringUtils.isBlank(settings.getRtmpUrl())) || regenerate ){
            if(regenerate){
                String status = muxService.checkStreamStatus(settings.getLiveStreamId());
                if("active".equals(status)){
                    throw new NotAcceptableException(CAN_NOT_GENERATE_STREAM_IN_PROGRESS);
                }
            }

            if(null != eventLiveStreamingConfiguration){
                dto = muxService.createStreamKey(eventLiveStreamingConfiguration.getWatermarkLogoUrl(), "EVENT_" + event.getEventId(), eventLiveStreamingConfiguration.isLowLatency(), event, new CaptionsDto());
            }else{
                dto = muxService.createStreamKey(null, "EVENT_" + event.getEventId(), false, event, new CaptionsDto());
            }

            GAUtils.handleRtmpTable(settings, dto);
            settings.setPlayBackRestrictionToken(dto.getPlayBackRestrictionToken());
            settings.setThumbnailRestrictionToken(dto.getThumbnailRestrictionToken());
            settings.setLiveStreamId(dto.getLiveStreamId());
            virtualEventSettingsRepoService.save(settings);
        }
        return dto;
    }

    @Override
    public HostInvoiceDto getHostInvoice(Event event) throws StripeException {
        VirtualEventSettings settings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        HostInvoiceDto hostInvoiceDto =null;
        if(null != settings){
            Invoice invoice = stripePaymentService.getHostVirtualEventInvoice(settings.getCustomerId(), settings.getSubscriptionItemId(),event.getEventURL());
            hostInvoiceDto = new HostInvoiceDto(invoice.getAmountPaid(),invoice.getAmountDue());
        }
        return hostInvoiceDto;
    }

    @Override
    public MuxDirectUploadIdAndUrlDto createUploadUrlForWelcomeMessageVideo() {
        MuxDirectUploadIdAndUrlDto muxDirectUploadIdAndUrlDto = muxService.createDirectUploadUrl(false, EMPTY_JSON_BODY);
        log.info("Created upload url for event welcome message, upload details {}" , muxDirectUploadIdAndUrlDto);
        return muxDirectUploadIdAndUrlDto;
    }

    @Override
    @Transactional
    public void updateWelcomeMessageVideoUploadIdAndFile(Long id, String uploadId, Event event) {
        String assetIdByUploadId = muxService.getAssetIdByDirectUploadId(uploadId);
        log.info("Welcome message video assetId {}", assetIdByUploadId);
        String playBackId = muxService.getPlayBackIdByAssetId(assetIdByUploadId);
        log.info("Welcome message video playbackId {}", playBackId);
        MuxJwtDto muxJwtDto = muxService.generateDomainPlayBackRestrictionToken(playBackId, event);
        muxLivestreamAssetService.storeLivestreamAssetDetailsForVirtualEventSetting(id,assetIdByUploadId,playBackId,event.getEventId(), muxJwtDto);
    }

    @Override
    public long getTheNumberOfExhibitorsPurchased(Event event) {
        Long numberOfExhibitorsPurchased = virtualEventSettingsRepository.findTheNumberOfExhibitorsPurchased(event.getEventId());
        return NumberUtils.isNumberGreaterThanZero(numberOfExhibitorsPurchased) ? numberOfExhibitorsPurchased : 0;
    }

    @Override
    @Transactional
    public void removeWelcomeMessageVideo(Event event) {
        log.info("Set MuxLiveStreamAsset welcome message video blank {}", event.getEventId());
        muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBackByEventId(event.getEventId());
        clearAPIGatewayCache.clearAPIGwVESettingsCache(event.getEventId());
    }

    @Override
    public List<MuxAssetDTO> findAllLiveStreamAssetsForVirtualEventSetting(Long eventId) {
        List<MuxAssetDTO> muxAssetDTOS = muxLivestreamAssetService.findAllLiveStreamAssetsForVirtualEventSetting(eventId);
        log.info("Get all past welcome message videos muxAssetDTOS {}" , muxAssetDTOS);
        return muxAssetDTOS;
    }

    @Override
    public List<LobbyImageDto> getPastLobbyBannerImages(Long eventId) {
        List<LobbyImageDto> lobbyImages = lobbyImageService.getLobbyImageByEventId(eventId);
        log.info("Get all past lobby images {}" , lobbyImages);
        return lobbyImages;
    }

    @Override
    @Transactional
    public void removePastLobbyBannerImage(Long imageId) {
        lobbyImageService.removePastLobbyBannerImage(imageId);
    }

    @Override
    @Transactional
    public void setDefaultLobbyBannerImageForVirtualEventSetting(Long virtualEventSettingsId, Long imageId) {
        LobbyImage lobbyImage = lobbyImageService.getLobbyImageById(imageId);
        log.info("Get lobbyBannerImageDetails for virtual event settings {}", lobbyImage.getId());
        VirtualEventSettings virtualEventSettings = getVirtualEventSettings(virtualEventSettingsId);
        virtualEventSettings.setLobbyBannerImage(lobbyImage.getImageId());
        log.info("Set default virtual event settings lobby image {}", virtualEventSettings.getId());
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    @Transactional
    public void setDefaultPlayBackForVirtualEventSetting(Long virtualEventSettingsId, Long muxLiveStreamAssetId) {
        MUXLivestreamAssetDetails muxLivestreamAssetDetails = muxLivestreamAssetService.getMUXLivestreamAssetDetails(muxLiveStreamAssetId);
        if(!muxLivestreamAssetDetails.isDefaultPlayback()){
            muxLivestreamAssetRepoService.markAllAssetAsNonDefaultPlayBackByEventId(muxLivestreamAssetDetails.getEventId());
            muxLivestreamAssetDetails.setDefaultPlayback(true);
            muxLivestreamAssetService.save(muxLivestreamAssetDetails);
            clearAPIGatewayCache.clearAPIGwVESettingsCache(muxLivestreamAssetDetails.getEventId());
        }



    }

    @Override
    @Transactional
    public void removePastWelcomeMessageVideo(Long muxLiveStreamAssetId) {
        muxLivestreamAssetService.removePastWelcomeMessageOrSessionVideo(muxLiveStreamAssetId);
    }

    @Override
    public void changeSpeakerInviteMailSettings(Long id, boolean isSpeakerInviteEnable) {
        VirtualEventSettings virtualEventSettings = getVirtualEventSettings(id);
        virtualEventSettings.setSpeakerInviteEnable(isSpeakerInviteEnable);
        log.info("Set speaker invite enable {} for virtual event settings: {}", isSpeakerInviteEnable, virtualEventSettings.getId());
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    public boolean isSpeakerInviteEnable(Event event) {
        Boolean isSpeakerInviteEnable = virtualEventSettingsRepository.isSpeakerInviteEnable(event.getEventId());
        log.info("get speaker invite disable {} for event: {}", isSpeakerInviteEnable , event.getEventId());
        return null != isSpeakerInviteEnable && isSpeakerInviteEnable;
    }

    @Override
    public void enableSimultaneousSessionScroll(Long id, boolean isSimultaneousSessionScroll) {
        VirtualEventSettings virtualEventSettings = getVirtualEventSettings(id);
        virtualEventSettings.setSimultaneousSessionScroll(isSimultaneousSessionScroll);
        log.info("Set simultaneous session scroll {} for virtual event settings: {}", isSimultaneousSessionScroll, virtualEventSettings.getId());
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    public void enableSessionNotification(Long id, boolean isSessionNotificationEnabled) {
        VirtualEventSettings virtualEventSettings = getVirtualEventSettings(id);
        virtualEventSettings.setSessionNotification(isSessionNotificationEnabled);
        log.info("Set session Notification {} for virtual event settings: {}", isSessionNotificationEnabled, virtualEventSettings.getId());
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    public void enableGamification(Event event, boolean isEnable) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        virtualEventSettings.setGamificationNewFlow(isEnable);
        log.info("Set gamification new flow {} for virtual event settings: {}", isEnable , virtualEventSettings.getId());
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    public Boolean isNewGamificationEnabled(Long eventId) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(eventId);
        if (virtualEventSettings != null) {
            log.info("isNewGamificationEnabled | VirtualEventSettings {}|  | isGamificationNewFlow {}| | Event ID {}|" , virtualEventSettings , virtualEventSettings.isGamificationNewFlow() , eventId);
            return virtualEventSettings.isGamificationNewFlow();
        } else {
            log.info("isNewGamificationEnabled | Event ID |{}" , eventId);
            return false;
        }
    }

    @Override
    public String getCustomizeSpeakerInviteEmailByEventId(Long eventId) {
        return virtualEventSettingsRepository.findCustomizeSpeakerInviteEmailByEventId(eventId);
    }

    @Override
    public VirtualEventChannelsSettingsDTO getVirtualEventSettingsForChannelsById(Event event) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        return new VirtualEventChannelsSettingsDTO().createVirtualEventChannelsSettingsDTO(virtualEventSettings);
    }

    public void updateRecStatusOfVirtualEventSetting(Long eventId,RecordStatus recStatus){
        virtualEventSettingsRepository.updateRecordStatusOfVirtualEventSetting(eventId,recStatus);
    }

    @Override
    public void updateRecordStatusOfVirtualEventSettingByEventIds(List<Long> eventIds,RecordStatus recStatus){
        virtualEventSettingsRepository.updateRecordStatusOfVirtualEventSettingByEventIds(eventIds,recStatus);
        log.info("Updating record status of virtual event settings for event ids: {} and record status {}", eventIds, recStatus.name());
    }

    @Override
    @Transactional
    public void updateAutoLiveStreaming(long eventId, StreamType type, CaptionsDto captions) {
        EventStreaming eventStreaming = eventStreamingService.getEventStreamingByEventIdAndType(eventId, type);
        eventStreaming.setCaptions(JsonMapper.parseToJsonString(captions));
        eventStreamingService.save(eventStreaming);
    }

    public void checkDuplicateValueOfLobbyTab(Map<String, List<VirtualEventTabsDTO>> virtualEventTabs, VirtualEventSettings virtualEventSettings, Event event){

        List<String> lobbyList = Arrays.asList("Feed","Agenda","Sponsors","Info Desk","Gamification","Show Challenges");


        String oldVirtualTab = virtualEventSettings.getVirtualTabs();
        Iterable<MultiLanguageLabel> multiLanguageLable = multiLanguageLabelRepo.getMultiLanguageLableByEventIdAndTypeAndLanguage(event.getEventId(), EnumMultiLanguageLabelType.VIRTUAL_EVENT_HUB);
        Map<String, List<VirtualEventTabsDTO>> virtualTabsInMap = challengeConfigService.getVirtualTabsInMap(oldVirtualTab);
        List<VirtualEventTabsDTO> virtualEventTabsDTOList = virtualEventTabs.get(LOBBY_TAB);
        List<VirtualEventTabsDTO> oldLobbyTab = virtualTabsInMap.get(LOBBY_TAB);
        List<String> lobbyValueList = oldLobbyTab.stream().map(VirtualEventTabsDTO::getValue).collect(Collectors.toList());
        List<String> multiLanguageLabelsList = new ArrayList<>();
        for (MultiLanguageLabel languageLabel: multiLanguageLable) {
            if (!languageLabel.getCustomLabel().equals(languageLabel.getDefaultLabel())){
                multiLanguageLabelsList.add(languageLabel.getCustomLabel());
            }
        }

        if (virtualEventTabsDTOList != null){
            List<VirtualEventTabsDTO> eventTabsDTOS = virtualEventTabsDTOList.stream().filter(virtualEventTabsDTO -> !virtualEventTabsDTO.getKey().equals(virtualEventTabsDTO.getValue())).collect(Collectors.toList());
            List<VirtualEventTabsDTO> value  = virtualEventTabsDTOList.stream().filter(virtualEventTabsDTO -> !virtualEventTabsDTO.getKey().equals(virtualEventTabsDTO.getValue())
                                     && !lobbyList.contains(virtualEventTabsDTO.getKey())).collect(Collectors.toList());

            for (VirtualEventTabsDTO virtualEventTabsDTO : value) {
                    if (!multiLanguageLabelsList.isEmpty() && multiLanguageLabelsList.contains(virtualEventTabsDTO.getValue())){
                        log.info("This is for default tab and value of new lobby tab{}",virtualEventTabsDTO.getValue());
                        throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.TAB_NAME_IS_ALREADY_EXIST);
                    }
            }
            if (virtualEventTabs.containsKey(LOBBY_TAB)){
                for (VirtualEventTabsDTO virtualEventTabsDTO : eventTabsDTOS ) {
                    for (VirtualEventTabsDTO oldLobby : oldLobbyTab) {
                        if (oldLobby.getKey().equals(virtualEventTabsDTO.getKey()) && !virtualEventTabsDTO.getValue().equalsIgnoreCase(oldLobby.getValue())){
                           log.info("key of old lobby{} and key of updated virtualEventTabsDTO{}",oldLobby.getKey(),virtualEventTabsDTO.getKey());
                           log.info("Value of old lobby{} and value of updated virtualEventTabsDTO{}",oldLobby.getValue(),virtualEventTabsDTO.getValue());
                            if (lobbyValueList.contains(virtualEventTabsDTO.getValue().trim()) || STRING_EMPTY.equals(virtualEventTabsDTO.getValue())){
                               log.info("Value of virtualEventTabsDTO{}",virtualEventTabsDTO.getValue());
                                throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.TAB_NAME_IS_ALREADY_EXIST);
                            }
                        }
                    }
                }
            }
        }

    }

    public void checkDuplicateValueOfNavMenuBar(Map<String, List<VirtualEventTabsDTO>> virtualEventTabs, VirtualEventSettings virtualEventSettings, Event event){
        List<String> leftSideNavMenuList = Arrays.asList("Lobby","Main Stage Sessions","Regular Sessions","Networking","Lounges",
                "Workshops","Expo","People","My Speaking Schedule","My Booth","Device Checker");

        String oldVirtualTab = virtualEventSettings.getVirtualTabs();
        Iterable<MultiLanguageLabel> multiLanguageLable = multiLanguageLabelRepo.getMultiLanguageLableByEventIdAndTypeAndLanguage(event.getEventId(), EnumMultiLanguageLabelType.VIRTUAL_EVENT_HUB);

        Map<String, List<VirtualEventTabsDTO>> virtualTabsInMap = challengeConfigService.getVirtualTabsInMap(oldVirtualTab);

        List<VirtualEventTabsDTO> oldLefSideNavMenu = virtualTabsInMap.get(LEFT_SIDE_NAV_MENU);
        if (virtualEventTabs.containsKey(LEFT_SIDE_NAV_MENU) && oldLefSideNavMenu != null){
            List<VirtualEventTabsDTO> newVirtualNavMenu = virtualEventTabs.get(LEFT_SIDE_NAV_MENU);
            List<String> leftSideNavMenuValueList =  oldLefSideNavMenu.stream().map(VirtualEventTabsDTO::getValue).collect(Collectors.toList());
            List<String> multiLanguageLabelsList = new ArrayList<>();

            for (MultiLanguageLabel languageLabel: multiLanguageLable) {
                if (!languageLabel.getCustomLabel().equals(languageLabel.getDefaultLabel())){
                    multiLanguageLabelsList.add(languageLabel.getCustomLabel());
                }
            }
            List<VirtualEventTabsDTO> virtualEventTabsDTOS = newVirtualNavMenu.stream().filter(virtualEventTabsDTO -> !virtualEventTabsDTO.getKey().equals(virtualEventTabsDTO.getValue())).collect(Collectors.toList());

            List<VirtualEventTabsDTO> valueOfNavMenu = newVirtualNavMenu.stream().filter(virtualEventTabsDTO -> !virtualEventTabsDTO.getKey().equals(virtualEventTabsDTO.getValue())
                    && !leftSideNavMenuList.contains(virtualEventTabsDTO.getKey())).collect(Collectors.toList());

            for (VirtualEventTabsDTO virtualEventTabsDTO : valueOfNavMenu) {
                if (!multiLanguageLabelsList.isEmpty() && multiLanguageLabelsList.contains(virtualEventTabsDTO.getValue())){
                    log.info("This is for default tab and value of new left side nav menu tab{}",virtualEventTabsDTO.getValue());
                    throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.TAB_NAME_IS_ALREADY_EXIST);
                }
            }

            for (VirtualEventTabsDTO virtualEventTabsDTO : virtualEventTabsDTOS ) {
                for (VirtualEventTabsDTO oldLeftSideNavMenuBar: oldLefSideNavMenu) {
                    if (virtualEventTabsDTO.getValue() != null && oldLeftSideNavMenuBar.getKey().equals(virtualEventTabsDTO.getKey()) && !virtualEventTabsDTO.getValue().equalsIgnoreCase(oldLeftSideNavMenuBar.getValue())) {
                        log.info("key of old Left Side Nav Menu Bar{} and key of updated virtualEventTabsDTO for Left Side Nav Menu Bar{}", oldLeftSideNavMenuBar.getKey(), virtualEventTabsDTO.getKey());
                        log.info("Value of old Left Side Nav Menu Bar{} and value of updated virtualEventTabsDTO for Left Side Nav Menu Bar{}", oldLeftSideNavMenuBar.getValue(), virtualEventTabsDTO.getValue());
                        if (leftSideNavMenuValueList.contains(virtualEventTabsDTO.getValue().trim()) || STRING_EMPTY.equals(virtualEventTabsDTO.getValue())) {
                            log.info("Value of virtualEventTabsDTO{}", virtualEventTabsDTO.getValue());
                            throw new NotAcceptableException(NotAcceptableException.VirtualEventExceptionMsg.TAB_NAME_IS_ALREADY_EXIST);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void updateVirtualEventHubDisclaimer(Event event, VirtualEventDisclaimerDTO virtualEventDisclaimerDTO) {
        log.info("request received to update virtual event hub access disclaimer for event{} and settings {}",event.getEventId(),virtualEventDisclaimerDTO);
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        virtualEventSettings.setEventHubDisclaimer(virtualEventDisclaimerDTO.isEventHubDisclaimer());
        virtualEventSettings.setAllowEventHubDisagreeDisclaimerConfirmation(virtualEventDisclaimerDTO.isAllowEventHubDisagreeDisclaimerConfirmation());
        virtualEventSettings.setCustomEventHubDisclaimer(virtualEventDisclaimerDTO.getCustomEventHubDisclaimer());
        log.info("virtual event hub access disclaimer settings successfully updated for event {} and settigs {}",event.getEventId(), virtualEventDisclaimerDTO);
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    public EntitlementsAvailabilityDTO checkEntitlements(Event event) {
        ChargeBeeDetails chargeBeeDetails = null;
        if (event.getWhiteLabel() != null) {
            chargeBeeDetails = roWhiteLabelService.getWhiteLabelById(event.getWhiteLabelId()).orElse(null);
        } else if (event.getOrganizer() != null) {
            chargeBeeDetails = organizerRepository.findByOrganizerId(event.getOrganizerId()).orElse(null);
        }
        if (null != chargeBeeDetails && null != chargeBeeDetails.getEntitlements()) {
            List<EntitlementsDTO> entitlementsDTO = JsonMapper.stringToObjectWithTypeReference(chargeBeeDetails.getEntitlements(), entitlementsTypeRef);
            return getEntitlementsAvailabilityDTO(entitlementsDTO, chargeBeeDetails.getPlanConfig(), event.getEventFormat());
        }
        return new EntitlementsAvailabilityDTO();
    }

    @Override
    public boolean checkNavigationMenuEntitlements(Event event, User user) {
        return chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.ADD_CUSTOM_TABS_AND_PAGES);
    }

    @Override
    public EntitlementsAvailabilityDTO getEntitlementsBasedOnOrganizerUrl(String organizerUrl) {
        Optional<Organizer> organizerOptional = organizerRepository.findByOrganizerPageURL(organizerUrl);
        if (organizerOptional.isEmpty()) {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
        Organizer organizer = organizerOptional.get();
        String entitlements = organizer.getEntitlements();
        log.info("getEntitlementsBasedOnOrganizerUrl organiserId {} entitlement {}",organizer.getId(),entitlements);
        PlanConfig planConfig = organizer.getPlanConfig();
        WhiteLabel whiteLabel = organizer.getWhiteLabel();
        if (whiteLabel != null) {
            log.info("getEntitlementsBasedOnOrganizerUrl organiserId {} whiteLabelId {}",organizer.getId(),whiteLabel.getId());
            planConfig = whiteLabel.getPlanConfig();
            entitlements=whiteLabel.getEntitlements();
            log.info("getEntitlementsBasedOnOrganizerUrl organiserId {} whiteLabelId {} entitlement {}",organizer.getId(),whiteLabel.getId(),entitlements);
        }
        if (null == planConfig) {
            log.info("getEntitlementsBasedOnOrganizerUrl organiserId {} planConfig is null",organizer.getId());
            return new EntitlementsAvailabilityDTO();
        }
        log.info("getEntitlementsBasedOnOrganizerUrl organiserId {} planConfig is null",organizer.getId());
        ObjectMapper objectMapper = new ObjectMapper();
        List<EntitlementsDTO> entitlementsDTO = new ArrayList<>();
        if (null != entitlements) {
            try {
                entitlementsDTO = objectMapper.readValue(entitlements, new TypeReference<List<EntitlementsDTO>>() {
                });
            } catch (IOException e) {
                log.error("getEntitlementsBasedOnOrganizerUrl | Error while parsing object {} ", organizer.getEntitlements());
            }
        }
        return getEntitlementsAvailabilityDTO(entitlementsDTO, planConfig, null);
    }
    private boolean checkSpecificEntitlement(List<EntitlementsDTO> entitlementsDTO, PlanConfig planConfig, ChargebeeEntitlements entitlement) {
        if (planConfig == null) {
            return false;
        }
        return entitlementsDTO.stream().anyMatch(entitlements -> entitlements.getFeatureId().equals(entitlement.getEntitlementId())
                && entitlements.isEnabled()
                && "Available".equals(entitlements.getName())
                && "true".equals(entitlements.getValue()));
    }
    @Override
    public EntitlementsAvailabilityDTO getEntitlementsAvailabilityDTO(List<EntitlementsDTO> entitlementsDTOs, PlanConfig planConfig, EventFormat eventFormat)
    {
        log.info("getEntitlementsAvailabilityDTO method eventFormat : {}",eventFormat);
        EntitlementsAvailabilityDTO entitlementsAvailabilityDTO = new EntitlementsAvailabilityDTO();
        String planName = "";
        if (null != planConfig) {
            planName = planConfig.getPlanName();
            log.info("getEntitlementsAvailabilityDTO method plan name : {}",planName);
        }

        if (FREE_PLAN.getName().equalsIgnoreCase(planName) && null != eventFormat) {
            entitlementsAvailabilityDTO.setKioskMode(!VIRTUAL.equals(eventFormat));
        } else {
            entitlementsAvailabilityDTO.setKioskMode(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.KIOSK_MODE));
        }
        entitlementsAvailabilityDTO.setBadgePrinting(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.BADGE_PRINTING));
        entitlementsAvailabilityDTO.setNavigationMenu(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.ADD_CUSTOM_TABS_AND_PAGES));
        entitlementsAvailabilityDTO.setAdvancedPageBuilder(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.ADVANCED_PAGE_BUILDER));
        entitlementsAvailabilityDTO.setApprovalWorkflow(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.APPROVAL_WORKFLOWS));
        entitlementsAvailabilityDTO.setMobileApp(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.ATTENDEE_MOBILE_APP));
        entitlementsAvailabilityDTO.setBasicGamification(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.BASIC_GAMIFICATION));
        entitlementsAvailabilityDTO.setCustomGamification(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.CUSTOM_GAMIFICATION));
        entitlementsAvailabilityDTO.setWebinars(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.WEBINARS));
        entitlementsAvailabilityDTO.setAdvancedEmailMarketing(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.ADVANCED_EMAIL_MARKETING));
        entitlementsAvailabilityDTO.setApiAccess(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.API_ACCESS));
        entitlementsAvailabilityDTO.setCeCreditTracking(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.CE_CREDIT_TRACKING));
        entitlementsAvailabilityDTO.setNativeIntegrations(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.NATIVE_INTEGRATIONS));
        entitlementsAvailabilityDTO.setVirtualComponents(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.VIRTUAL_COMPONENTS));
        entitlementsAvailabilityDTO.setOfflinePayments(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.OFFLINE_PAYMENTS));
        entitlementsAvailabilityDTO.setAudienceBuilder(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.AUDIENCE_BUILDER));
        entitlementsAvailabilityDTO.setAdvancedAnalytics(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.ADVANCED_ANALYTICS));
        entitlementsAvailabilityDTO.setCustomJavaScript(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.CUSTOM_JAVA_SCRIPT));
        entitlementsAvailabilityDTO.setTwoFactorAuthentication(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.TWO_FACTOR_AUTHENTICATION));
        entitlementsAvailabilityDTO.setCustomCSS(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.CUSTOM_CSS));
        entitlementsAvailabilityDTO.setVirtualNetworking(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.VIRTUAL_NETWORKING));
        entitlementsAvailabilityDTO.setPreScheduleMeetings(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.PRE_SCHEDULE_MEETINGS));
        entitlementsAvailabilityDTO.setSurveys(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.SURVEYS));
        entitlementsAvailabilityDTO.setBranding(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.BRANDING));
        entitlementsAvailabilityDTO.setSenderNameModification(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.SENDER_NAME_MODIFICATION));
        entitlementsAvailabilityDTO.setEngageEmails(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.ENGAGE_EMAILS));
        entitlementsAvailabilityDTO.setSingleSignOn(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.SINGLE_SIGN_ON));
        entitlementsAvailabilityDTO.setEventRequestForm(checkSpecificEntitlement(entitlementsDTOs, planConfig, ChargebeeEntitlements.EVENT_REQUEST_FORM));
        return entitlementsAvailabilityDTO;
    }

    @Override
    public int findEventMaxDays(String whiteLabelUrl, String organizerUrl, String eventUrl, EventFormat eventFormat,boolean forDuplicate) {
        log.info("findEventMaxDays organizerUrl : {},eventUrl : {},eventFormat : {}", organizerUrl, eventUrl, eventFormat);
        EventPlanConfig eventPlanConfig;
        PlanConfig planConfig = null;
        String registrationJson;
        if (null != eventUrl) {
            Event event = roEventService.getEventByURL(eventUrl);
            if (forDuplicate) {
                if (event.getWhiteLabelId() != null) {
                    planConfig = event.getWhiteLabel().getPlanConfig();
                } else if (event.getOrganizer() != null) {
                    planConfig = event.getOrganizer().getPlanConfig();
                }
                registrationJson = null != planConfig ? planConfig.getRegistrationConfigJson() : null;
            } else {
                eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
                registrationJson = null != eventPlanConfig ? eventPlanConfig.getRegistrationConfigJson() : null;
                log.info("findEventMaxDays EventURL {} eventId {} event planConfig {},", eventUrl, event.getEventId(), eventPlanConfig);
            }
        } else {
            if (Strings.isNotBlank(whiteLabelUrl)) {
                WhiteLabel whiteLabel = whiteLabelService.findByWhiteLabelURLThrowException(whiteLabelUrl);
                planConfig = whiteLabel.getPlanConfig();
                log.info("findEventMaxDays WhiteLabelURL {} planConfig {},", whiteLabelUrl, planConfig);
            } else if (Strings.isNotBlank(organizerUrl)) {
                Optional<Organizer> organizerOptional = organizerRepository.findOrganizerByOrganizerPageURL(organizerUrl);
                if (organizerOptional.isEmpty()) {
                    throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
                }
                Organizer organizer = organizerOptional.get();
                planConfig = checkSingleEventPlanAndReturnPlanConfig(organizer);
                log.info("findEventMaxDays OrganiserURL {} and planConfig {}", organizerUrl, planConfig);
            } else {
                planConfig = chargebeePlanService.getFreePlanConfig();
            }
            log.info("findEventMaxDays organizer planConfig {}", planConfig);
            registrationJson = null != planConfig ? planConfig.getRegistrationConfigJson() : null;
        }
        RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(registrationJson);
        int eventMaxDays = (int) (null != planConfig && FREE_PLAN.getName().equalsIgnoreCase(planConfig.getPlanName()) && IN_PERSON.equals(eventFormat) ? 4L : registrationConfigDto.getMaxEventDays());
        return eventMaxDays != -1 ? eventMaxDays : 4;
    }

    public PlanConfig checkSingleEventPlanAndReturnPlanConfig(Organizer organizer) {
        PlanConfig planConfig;
        String planName = organizer.getPlanConfig().getPlanName();
        boolean isValid = STARTER_2023.getLableName().equals(planName) || PROFESSIONAL_2023.getLableName().equals(planName);
        if (isValid) {
            Long count = chargesPurchasedRepoService.findChargePurchasedCountByOrganiser(organizer);
            if (count > 0) {
                planConfig = organizer.getPlanConfig();
            } else {
                planConfig = chargebeePlanService.getFreePlanConfig();
            }
        } else {
            planConfig = organizer.getPlanConfig();
        }
        return planConfig;
    }
    @Override
    public void enableSessionBookmarkCapacity(Event event, boolean allowBookmarkCapacity) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        log.info("VirtualEventServiceImpl | enableSessionBookmarkCapacity eventId {}, old allow capacity {}, new allow capacity {}",event.getEventId(),virtualEventSettings.isAllowSessionBookmarkCapacity(), allowBookmarkCapacity);
        virtualEventSettings.setAllowSessionBookmarkCapacity(allowBookmarkCapacity);
        virtualEventSettingsRepoService.save(virtualEventSettings);
        log.info("VirtualEventServiceImpl | enableSessionBookmarkCapacity eventId {}, saved successfully",event.getEventId());
    }

    @Override
    public boolean isAllowSessionBookmarkCapacity(long eventId) {
        Boolean value = virtualEventSettingsRepoService.isAllowSessionBookmarkCapacity(eventId);
        return value != null && value;
    }

    @Override
    public boolean attendeeMobileAppEntitlement(Event event) {
        EntitlementsAvailabilityDTO dto = this.checkEntitlements(event);
        return dto.isMobileApp();
    }

    @Override
    public void save(VirtualEventSettings virtualEventSettings) {
        virtualEventSettingsRepoService.save(virtualEventSettings);
    }

    @Override
    public Set<String> getAttendeeRestrictedCustomLobbyTab(User user, Event event) {
        log.info("getAttendeeRestrictedCustomLobbyTab eventId {}, userId {}", event.getEventId(), user.getUserId());

        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());

        List<VirtualEventTabsDTO> filteredLobbyTabs = getCustomLobbyTabExistWithTicketRestrictionEnabled(virtualEventSettings);
        log.info("filteredLobbyTab {}", filteredLobbyTabs);

        Set<String> restrictedCustomLobbyTabKeysSet = new HashSet<>();

        if (!filteredLobbyTabs.isEmpty()) {

            Set<Long> userTicketingTypes = eventTicketsRepository.getAllTicketTypesByHolderUserIdAndEventIdAndDataTypeAndTicketStatus(
                    user, event, DataType.TICKET,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
            log.info("getAttendeeRestrictedCustomLobbyTab attendee had ticketing types {}", userTicketingTypes);

            if (!userTicketingTypes.isEmpty()) {
                filteredLobbyTabs.stream()
                        .filter(tab -> StringUtils.isNotBlank(tab.getListOfTicketingTypes()))
                        .forEach(tab -> {
                            List<Long> ticketingTypesList = GeneralUtils.convertCommaSeparatedToListLong(tab.getListOfTicketingTypes());
                            if (Collections.disjoint(userTicketingTypes, ticketingTypesList)) {
                                restrictedCustomLobbyTabKeysSet.add(tab.getKey());
                            }
                        });
            }
        }
        return restrictedCustomLobbyTabKeysSet;
    }

    @Override
    public List<VirtualEventTabsDTO> getCustomLobbyTabExistWithTicketRestrictionEnabled(VirtualEventSettings virtualEventSettings) {
        if (StringUtils.isNotBlank(virtualEventSettings.getVirtualTabs())) {
            String virtualTabs = virtualEventSettings.getVirtualTabs();
            Map<String, List<VirtualEventTabsDTO>> virtualTabsMap = challengeConfigService.getVirtualTabsInMap(virtualTabs);

            List<VirtualEventTabsDTO> lobbyTabList = virtualTabsMap.getOrDefault(LOBBY_TAB, Collections.emptyList());

            List<String> defaultLobbyTabs = Arrays.asList("Feed", "Agenda", "Sponsors", "Info Desk", "Gamification", "Show Challenges");

            return lobbyTabList.stream()
                    .filter(tab -> !defaultLobbyTabs.contains(tab.getKey()) && tab.isTicketsRestrictionEnabled() && !tab.isAllTicketingTypeSelected())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public void showBookmarkButton(Event event, boolean showBookmarkButton) {
        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
        log.info("VirtualEventServiceImpl | showBookmarkButton eventId {}, old bookmark button {}, new bookmark button {}", event.getEventId(), virtualEventSettings.isShowBookmarkButton(), showBookmarkButton);
        if (virtualEventSettings.isShowBookmarkButton() != showBookmarkButton) {
            virtualEventSettings.setShowBookmarkButton(showBookmarkButton);
            virtualEventSettingsRepoService.save(virtualEventSettings);
        }
        log.info("VirtualEventServiceImpl | showBookmarkButton eventId {}, saved successfully", event.getEventId());
    }

    @Override
    public void enableSessionWaitlistCapacityReached(Event event, boolean enableSessionWaitlistCapacityReached) {
        if (event == null) {
            log.error("VirtualEventServiceImpl | enableSessionWaitlistCapacityReached - Event cannot be null");
            throw new NotFoundException(NotFoundException.EventNotFound.EVENT_NOT_FOUND);
        }

        VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());

        if (virtualEventSettings == null) {
            log.error("VirtualEventServiceImpl | enableSessionWaitlistCapacityReached - VirtualEventSettings not found for eventId {}", event.getEventId());
            throw new NotFoundException(NotFoundException.VirtualEventNotFound.VIRTUAL_EVENT_SETTINGS_NOT_FOUND);
        }

        log.info("VirtualEventServiceImpl | enableSessionWaitlistCapacityReached eventId {}, old session waitlist capacity reached button {}, new session waitlist capacity  reached button {}", event.getEventId(), virtualEventSettings.isEnableSessionWaitlistCapacityReached(), enableSessionWaitlistCapacityReached);
        if (virtualEventSettings.isAllowSessionBookmarkCapacity() && virtualEventSettings.isEnableSessionWaitlistCapacityReached() != enableSessionWaitlistCapacityReached) {
            virtualEventSettings.setEnableSessionWaitlistCapacityReached(enableSessionWaitlistCapacityReached);
            virtualEventSettingsRepoService.save(virtualEventSettings);
        }
        log.info("VirtualEventServiceImpl | enableSessionWaitlistCapacity eventId {}, saved successfully", event.getEventId());
    }

}