package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.DiscountType;
import com.accelevents.domain.enums.EnumLabelLanguageCode;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.TicketType;
import com.accelevents.registration.approval.domain.RegistrationRequestTicket;
import com.accelevents.registration.approval.dto.CouponCodeApprovalTicketDto;
import com.accelevents.registration.approval.repositories.RegistrationRequestTicketRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.ticketing.dto.TicketTypeManagerDto;
import com.accelevents.ticketing.dto.TicketingAppliedCouponDto;
import com.accelevents.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.Constants.COUPON_APPLIED_BUT_NOT_WITH_DONATION_TICKET_TYPE;
import static com.accelevents.utils.Constants.COUPON_APPLIED_SUCCESS_MSG;
import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;

@Service
public class TicketingCouponCodeServiceImpl implements TicketingCouponCodeService {


    private static final Logger log = LoggerFactory.getLogger(TicketingCouponCodeServiceImpl.class);
    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private TicketingOrderService ticketingOrderService;
    @Autowired
    private TicketingOrderRepoService ticketingOrderRepoService;
    @Autowired
    private TrackingLinksService trackingLinksService;
    @Autowired
    private UserService userService;
    @Autowired
    private EventTicketsService eventTicketsService;
    @Autowired
    private PaymentHandlerService paymentHandlerService;
    @Autowired
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Autowired
    private StripeService stripeService;
    @Autowired
    private TicketingCouponService ticketingCouponService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;
    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private SalesTaxService salesTaxService;
    @Autowired
    private TicketingTypeService ticketingTypeService;
    @Autowired
    private RegistrationRequestTicketRepository registrationRequestTicketRepository;

    @Override
    @Transactional
    public TicketingAppliedCouponDto applyCouponCode(String couponCode, Long orderId, Event event, Long recurringEventId, TicketingOrder ticketingOrder) {
        return applyCouponCode(couponCode, orderId, event, recurringEventId, Collections.EMPTY_LIST, ticketingOrder);
    }

    @Override
    public TicketingAppliedCouponDto applyCouponCode(String couponCode, Long orderid, Event event, Long recurringEventId, List<HolderEmailDiscountDto> holderEmailDiscountDtoList, TicketingOrder ticketingOrder) {
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(null, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        if (ticketing.isRecurringEvent() && !isNumberGreaterThanZero(recurringEventId)) { //we are not allow default coupons to apply
            throw new NotAcceptableException(NotAcceptableException.RecurringExceptionMsg.RECURRING_EVENT_ID_NOT_EMPTY);
        }
        TicketingCoupon ticketingCoupon = ticketingCouponService.getByEventIdAndCouponCode(event.getEventId(),
                couponCode, recurringEventId);

        if(ticketingOrder == null){
            ticketingOrder = ticketingOrderService.findByidAndEventid(orderid, event);
        }

        validateCoupon(ticketingCoupon, ticketingOrder, event, recurringEventId);

        StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);

        boolean isCardPayment = !ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.CASH);
        boolean isTicketTypeAvailbleInOrder = false;
        boolean isTicketTypeDonation = false;
        boolean isForAllTickets = StringUtils.isBlank(ticketingCoupon.getEventTicketTypeId());
        List<TicketingOrderManager> ticketingOrderManagers = ticketingOrderManagerService
                .getAllByOrderId(ticketingOrder);
        if (allTicketsAreOfDonationType(ticketingOrderManagers, ticketingCoupon)) {
            throw new NotAcceptableException(
                    NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_NOT_APPLICABLE_ON_DONATION_TICKETING_TYPE);
        }
        List<TicketTypeManagerDto> discountTicketTypeDtos = new ArrayList<>();
        List<String> couponTicketTypeList = new ArrayList<>();
        if (!isForAllTickets) {
            couponTicketTypeList
                    .addAll(Arrays.asList(ticketingCoupon.getEventTicketTypeId().split(Constants.STRING_COMMA)));
        }

        // need to check order amount is less then discount amount
        double totalPrice = 0;
        double discountAmount = 0;
        long numberOfUsesLeft = ticketingCouponService.getRemainingCount(ticketingCoupon, ticketingOrder);
        boolean isUnlimitedUsage = numberOfUsesLeft == -1;
        TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto = ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(ticketingOrderManagers, ticketingCoupon);

        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(ticketingOrder.getEventid().getEventId());

        for (TicketingOrderManager ticketingOrderManager : ticketingOrderManagers) {
            isTicketTypeDonation = false;
            boolean isDiscountAvailableForTicketType = false;
            long discountToBeAppliedOn = 0;
            double discount = 0;
            if (TicketType.DONATION.equals(ticketingOrderManager.getTicketType().getTicketType())) {
                isTicketTypeDonation = true;
            }

            TicketingType ticketingType = ticketingOrderManager.getTicketType();
            String ticketTypeId = String.valueOf(ticketingType.getId());
            if (couponTicketTypeList.contains(ticketTypeId) || isForAllTickets) {
                isTicketTypeAvailbleInOrder = true;
                discountToBeAppliedOn = numberOfUsesLeft;
                isDiscountAvailableForTicketType = true;
            }
            if ((isUnlimitedUsage || numberOfUsesLeft > ticketingOrderManager.getNumberofticket()
                    || TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo()))
                    && isTicketTypeAvailbleInOrder && !isTicketTypeDonation) {
                discountToBeAppliedOn = ticketingOrderManager.getNumberofticket();
            }
            if(!CollectionUtils.isEmpty(holderEmailDiscountDtoList) && discountToBeAppliedOn > 0){
                int noOfDiscountedTicket = holderEmailDiscountDtoList.stream().filter(ticketTypeIdAndNoOfTicketsDto -> ticketTypeIdAndNoOfTicketsDto.getTicketTypeId().equals(ticketingType.getId())).mapToInt(HolderEmailDiscountDto::getNoOfDiscountedTickets).sum();
                if(discountToBeAppliedOn > 0 ){
                    discountToBeAppliedOn = noOfDiscountedTicket > discountToBeAppliedOn ? discountToBeAppliedOn : noOfDiscountedTicket;
                }
            }

            int numberOfTicketTypeExcludingFreeType = ticketingHelperService.getTotalNoOfTicketTypeExcludingFreeType(ticketingOrderManagers);
            //TODO:vikas isInternationalPayment need to change from false
            List<TicketPriceDetails> ticketPriceDetailsList = ticketingHelperService.
                    getTicketPriceDetails(discountToBeAppliedOn, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketTypeExcludingFreeType, totalTicketsAndTotalTicketPriceDto.getTotalTickets(),
                            ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, totalTicketsAndTotalTicketPriceDto.getTotalTicketPrice(), 0);

            discount += ticketPriceDetailsList.stream().filter(e -> e.getDiscountedAmount() > 0).mapToDouble(TicketPriceDetails::getDiscountedAmount).sum();
            discountAmount+= discount;
            double allTicketPriceSumWithFee = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getPriceWithFee).sum();
            double allValTax = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getVatTaxFee).sum();
            double allTicketSalesFees = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getSalesTaxFee).sum();

            totalPrice += allTicketPriceSumWithFee;
            //discountAmount += ticketTypePrice.getFinalDiscount() * ticketTypePrice.getCountOfTicketOnWhichCouponApplied();//nosonar
            //totalPrice += (ticketTypePrice.getFinalTicketPriceWithFee() * ticketingOrderManager.getNumberofticket()) - (ticketTypePrice.getFinalDiscount() * ticketTypePrice.getCountOfTicketOnWhichCouponApplied());//nosonar

            ticketingType.setNumberofticket(ticketingOrderManager.getNumberofticket());

            ticketingOrderManager.setNumberOfDiscountedTicket(isDiscountAvailableForTicketType ? Long.valueOf(discountToBeAppliedOn).intValue() : 0);
            ticketingOrderManagerService.save(ticketingOrderManager);
            numberOfUsesLeft = isUnlimitedUsage ? -1 : (numberOfUsesLeft - discountToBeAppliedOn);
            OptionalDouble totalFee = ticketPriceDetailsList.stream().mapToDouble(e -> e.getPriceWithFee() - e.getPrice()).average();

            Map<Double, List<TicketPriceDetails>> collect = ticketPriceDetailsList.stream()
                    .collect(Collectors.groupingBy(TicketPriceDetails::getDiscountedAmount));

            collect.entrySet().stream().forEach(e -> {
                List<TicketPriceDetails> discountedTicketPriceDetail = e.getValue();

                discountTicketTypeDtos.add(new TicketTypeManagerDto(ticketingType,
                        discountedTicketPriceDetail.size(),
                        GeneralUtils.getRoundValue(discountedTicketPriceDetail.get(0).getPriceWithFee()),
                        totalFee.isPresent() ? GeneralUtils.getRoundValue(totalFee.getAsDouble()) : 0,
                        GeneralUtils.getRoundValue(allValTax / discountedTicketPriceDetail.size()),
                        GeneralUtils.getRoundValue(allTicketSalesFees / discountedTicketPriceDetail.size()),
                        GeneralUtils.getRoundValue(discountedTicketPriceDetail.get(0).getDiscountedAmount())));
            });

            // TODO : Vikas Need to check on this.
//            discountTicketTypeDtos.add(new DiscountTicketTypeDto(ticketingType.getTicketTypeName(),
//                    ticketingOrderManager.getNumberofticket(),
//                    ticketingType.getPrice(),
//                    GeneralUtils.getRoundValue(allTicketPriceSumWithFee / ticketPriceDetailsList.size()),//ticketTypePrice.getFinalTicketPriceWithFee(),
//                    //ticketTypePrice.getFinalTicketPrice(),
//                    ticketingType.getId(),
//                    ticketingType.getTicketTypeDescription(),
//                    GeneralUtils.getRoundValue(discount / ticketPriceDetailsList.size()),//ticketTypePrice.getFinalDiscount(),
//                    ticketingType.getEnableTicketDescription(), allValTax / ticketPriceDetailsList.size()));
        }

        if (isTicketTypeAvailbleInOrder) {
            TicketingAppliedCouponDto ticketingAppliedCouponDto = new TicketingAppliedCouponDto();
            ticketingOrder.setTicketingCoupon(ticketingCoupon);
            ticketingOrderRepoService.save(ticketingOrder);

            ticketingAppliedCouponDto.setTotalPrice(GeneralUtils.getRoundValue(totalPrice));
            ticketingAppliedCouponDto.setDiscountAmount(GeneralUtils.getRoundValue(discountAmount));
            ticketingAppliedCouponDto.setDiscountTicketTypes(discountTicketTypeDtos);
            ticketingAppliedCouponDto.setMessage(isTicketTypeDonation ? resourceBundle.getString(languageMap.get(COUPON_APPLIED_BUT_NOT_WITH_DONATION_TICKET_TYPE)) :
                    resourceBundle.getString(languageMap.get(COUPON_APPLIED_SUCCESS_MSG)));
            return ticketingAppliedCouponDto;
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE);
        }

    }

    @Override
    public List<TicketingCoupon> getCouponCodeByTicketingTypeId(List<Long> eventTicketTypeIds, Long eventId) {
        List<TicketingCoupon> ticketingCouponList = new ArrayList<>();
        List<TicketingCoupon> ticketingCoupons = ticketingCouponService.getAllByEventId(eventId);
        for (TicketingCoupon ticketingCoupon : ticketingCoupons) {
            for (Long eventTicketTypeId : eventTicketTypeIds) {
                if (ticketingCoupon.getEventTicketTypeId().contains(eventTicketTypeId.toString()) && !ticketingCouponList.contains(ticketingCoupon)) {
                    ticketingCouponList.add(ticketingCoupon);
                }
            }
        }
        return ticketingCouponList;
    }


    @Override
    @Transactional
    public void removeCouponFromTicketingOrder(String eventURL, Long orderid) {
        Event event = roEventService.getEventByURL(eventURL);
        TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderid, event);

        ticketingOrderManagerService.getAllByOrderId(ticketingOrder).forEach(orderManager -> {
            orderManager.setNumberOfDiscountedTicket(0);
            ticketingOrderManagerService.save(orderManager);
        });
        if (ticketingOrder.getTicketingCoupon() != null) {
            ticketingOrder.setTicketingCoupon(null);
            ticketingOrderRepoService.save(ticketingOrder);
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_IS_NOT_APPLIED_TO_THIS_ORDER);
        }
    }

    @Override
    @Transactional
    public void removeCouponFromTicketingOrder(Event event, TicketingOrder ticketingOrder) {

        ticketingOrderManagerService.getAllByOrderId(ticketingOrder).forEach(orderManager -> {
            orderManager.setNumberOfDiscountedTicket(0);
            ticketingOrderManagerService.save(orderManager);
        });
        if (ticketingOrder.getTicketingCoupon() != null) {
            ticketingOrder.setTicketingCoupon(null);
            ticketingOrderRepoService.save(ticketingOrder);
        }
    }

    protected boolean allTicketsAreOfDonationType(List<TicketingOrderManager> ticketingOrderManagers,
                                                  TicketingCoupon ticketingCoupon) {
        if (ticketingCoupon != null) {
            for (TicketingOrderManager ticketingOrderManager : ticketingOrderManagers) {
                if (!TicketType.DONATION.equals(ticketingOrderManager.getTicketType().getTicketType())) {
                    return false;
                }
            }
            return true;
        } else {
            return false;
        }
    }

    protected void validateCoupon(TicketingCoupon ticketingCoupon, TicketingOrder ticketingOrder, Event event,
                                  Long recurringEventId) {
        if (ticketingCoupon != null) {
            TicketingCoupon appliedCoupon = ticketingOrder.getTicketingCoupon();
            if (appliedCoupon != null
                    && ticketingCoupon.getName().equals(appliedCoupon.getName())) {
                throw new NotAcceptableException(
                        NotAcceptableException.TicketingExceptionMsg.THIS_DISCOUNT_CODE_HAS_ALREADY_BEEN_APPLIED_TO_YOUR_TRANSACTION);
            }

            long usedCount = 0;
            if(ticketingCoupon.getUses() != -1) {
                usedCount = ticketingCouponService.couponUsed(ticketingCoupon, event, null, recurringEventId);
            }
            long usedPerUser = 0;
            if(ticketingOrder.getPurchaser()!=null && ticketingCoupon.getUsesPerUser() != -1) {
                usedPerUser = ticketingCouponService.couponUsedPerUser(ticketingCoupon, event, ticketingOrder.getPurchaser(), recurringEventId);
            }
            if (ticketingOrder.getPurchaser() != null
                    && ticketingCoupon.getUsesPerUser() != -1
                    && ticketingCoupon.getUsesPerUser() <= usedPerUser) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_REACHED_MAX_USAGE_PER_USER);
            }

            if (ticketingCoupon.getUses() != -1
                    && ticketingCoupon.getUses() <= usedCount) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_REACHED_MAX_USAGE);
            }
            Date startDate = TimeZoneUtil.getDateInUTC(ticketingCoupon.getCouponStartDate(), event.getEquivalentTimeZone());
            if (startDate.compareTo(new Date()) > 0) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_IS_NOT_AVAILABLE);
            }
            Date endDate = TimeZoneUtil.getDateInUTC(ticketingCoupon.getCouponEndDate(), event.getEquivalentTimeZone());
            if (endDate.compareTo(new Date()) < 0) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_EXPIRED);
            }
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.APPLIED_COUPON_NOT_FOUND);
        }
    }

    @Override
    public Double getDiscountedAmountForOrder(TicketingCoupon ticketingCoupon, TicketingOrder ticketingOrder, Event event, Long recurringEventId, List<HolderEmailDiscountDto> holderEmailDiscountDtos) {

        double discountAmount = 0;
        try {
            validateCoupon(ticketingCoupon, ticketingOrder, event, recurringEventId);
        } catch (NotAcceptableException e) {
            return 0.0;
        }

        StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);

        boolean isCardPayment = !ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.CASH);
        boolean isTicketTypeAvailableInOrder = false;
        boolean isTicketTypeDonation = false;
        boolean isForAllTickets = StringUtils.isBlank(ticketingCoupon.getEventTicketTypeId());
        List<TicketingOrderManager> ticketingOrderManagers = ticketingOrderManagerService
                .getAllByOrderId(ticketingOrder);
        if (allTicketsAreOfDonationType(ticketingOrderManagers, ticketingCoupon)) {
            return 0.0;
        }
        List<String> couponTicketTypeList = new ArrayList<>();
        if (!isForAllTickets) {
            couponTicketTypeList
                    .addAll(Arrays.asList(ticketingCoupon.getEventTicketTypeId().split(Constants.STRING_COMMA)));
        }
        // need to check order amount is less then discount amount
        long numberOfUsesLeft = ticketingCouponService.getRemainingCount(ticketingCoupon, ticketingOrder);
        boolean isUnlimitedUsage = numberOfUsesLeft == -1;
        TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto = ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(ticketingOrderManagers, ticketingCoupon);
        List<Long> holderMemberTicketTypeId= holderEmailDiscountDtos.stream().map(HolderEmailDiscountDto::getTicketTypeId).collect(Collectors.toList());
        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(ticketingOrder.getEventid().getEventId());
        for (TicketingOrderManager ticketingOrderManager : ticketingOrderManagers) {
            // This condition will check if the ticket type is valid for discount or not based on purchased by the holder
            if(!CollectionUtils.isEmpty(holderMemberTicketTypeId) && !holderMemberTicketTypeId.contains(ticketingOrderManager.getTicketType().getId())){
                continue;
            }
            isTicketTypeDonation = false;
            long discountToBeAppliedOn = 0;
            double discount = 0;
            if (TicketType.DONATION.equals(ticketingOrderManager.getTicketType().getTicketType())) {
                isTicketTypeDonation = true;
            }

            TicketingType ticketingType = ticketingOrderManager.getTicketType();
            String ticketTypeId = String.valueOf(ticketingType.getId());
            if (couponTicketTypeList.contains(ticketTypeId)
                    || isForAllTickets) {
                isTicketTypeAvailableInOrder = true;
                discountToBeAppliedOn = numberOfUsesLeft;
            }
            if ((isUnlimitedUsage
                    || numberOfUsesLeft > ticketingOrderManager.getNumberofticket()
                    || TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo()))
                    && isTicketTypeAvailableInOrder && !isTicketTypeDonation) {
                discountToBeAppliedOn = ticketingOrderManager.getNumberofticket();
            }
            Integer noOfDiscountedTicket = CollectionUtils.isEmpty(holderEmailDiscountDtos) ? 0 :
                    holderEmailDiscountDtos.stream().filter(ticketTypeIdAndNoOfTicketsDto -> ticketTypeIdAndNoOfTicketsDto.getTicketTypeId().equals(ticketingType.getId())).mapToInt(HolderEmailDiscountDto::getNoOfDiscountedTickets).sum();
            if(discountToBeAppliedOn > 0  && noOfDiscountedTicket>0){
                discountToBeAppliedOn = noOfDiscountedTicket > discountToBeAppliedOn ? discountToBeAppliedOn : noOfDiscountedTicket;
            }

            int numberOfTicketTypeExcludingFreeType = ticketingHelperService.getTotalNoOfTicketTypeExcludingFreeType(ticketingOrderManagers);
            //TODO:vikas isInternationalPayment need to change from false
            List<TicketPriceDetails> ticketPriceDetailsList = ticketingHelperService.
                    getTicketPriceDetails(discountToBeAppliedOn, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketTypeExcludingFreeType, totalTicketsAndTotalTicketPriceDto.getTotalTickets(),
                            ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, totalTicketsAndTotalTicketPriceDto.getTotalTicketPrice(), 0);

            discount += ticketPriceDetailsList.stream().filter(e -> e.getDiscountedAmount() > 0).mapToDouble(TicketPriceDetails::getDiscountedAmount).sum();
            discountAmount += discount;
        }
        return GeneralUtils.getRoundValue(discountAmount);

    }

    @Override
    public double applyCouponCodeForTicketExchange(String couponCode, Long orderId, Event event, Long recurringEventId, TicketingOrderManager ticketingOrderManager) {
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        if (ticketing.isRecurringEvent() && !isNumberGreaterThanZero(recurringEventId)) { //we are not allow default coupons to apply
            throw new NotAcceptableException(NotAcceptableException.RecurringExceptionMsg.RECURRING_EVENT_ID_NOT_EMPTY);
        }
        TicketingCoupon ticketingCoupon = ticketingCouponService.getByEventIdAndCouponCode(event.getEventId(), couponCode, recurringEventId);
        if (ticketingCoupon != null && TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo())){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_NOT_APPLICABLE_ON_TICKET_EXCHANGE);
        }
        TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderId, event);

        if (ticketingOrder.getTicketingCoupon() != null) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_IS_ALREADY_APPLIED_TO_THIS_ORDER);
        }
        validateCoupon(ticketingCoupon, ticketingOrder, event, recurringEventId);

        boolean isTicketTypeAvailbleInOrder = false;
        boolean isForAllTickets = StringUtils.isBlank(ticketingCoupon.getEventTicketTypeId());
        if (TicketType.DONATION.equals(ticketingOrderManager.getTicketType().getTicketType())) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_NOT_APPLICABLE_ON_DONATION_TICKETING_TYPE);
        }
        List<String> couponTicketTypeList = new ArrayList<>();
        if (!isForAllTickets) {
            couponTicketTypeList.addAll(Arrays.asList(ticketingCoupon.getEventTicketTypeId().split(Constants.STRING_COMMA)));
        }

        // need to check order amount is less then discount amount
        double finalDiscount = 0;
        long numberOfUsesLeft = ticketingCouponService.getRemainingCount(ticketingCoupon, ticketingOrder);
        boolean isUnlimitedUsage = numberOfUsesLeft == -1;
        TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto = ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(List.of(ticketingOrderManager), ticketingCoupon);
        long discountToBeAppliedOn = 0;

        TicketingType ticketingType = ticketingOrderManager.getTicketType();
        String ticketTypeId = String.valueOf(ticketingType.getId());
        if (couponTicketTypeList.contains(ticketTypeId) || isForAllTickets) {
            isTicketTypeAvailbleInOrder = true;
            discountToBeAppliedOn = numberOfUsesLeft;
        }
        if ((isUnlimitedUsage || numberOfUsesLeft > ticketingOrderManager.getNumberofticket()
                || TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo()))
                && isTicketTypeAvailbleInOrder) {
            discountToBeAppliedOn = ticketingOrderManager.getNumberofticket();
        }

        if (discountToBeAppliedOn > 0 && !TicketType.DONATION.equals(ticketingType.getTicketType())) {
            Discount discount = new Discount(discountToBeAppliedOn, ticketingCoupon, ticketingType, 1, totalTicketsAndTotalTicketPriceDto.getTotalTickets(), ticketingType.getPrice(), 0).invoke();
            finalDiscount = discount.getFinalDiscount();
        }

        if (isTicketTypeAvailbleInOrder) {
            return GeneralUtils.getRoundValue(finalDiscount);
        } else {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE);
        }

    }

    @Override
    public TicketingAppliedCouponForExistingOrderDto applyCouponCodeForOldOrder(Long orderId, Event event, Long recurringEventId, List<HolderEmailDiscountDto> holderEmailDiscountDtoList, List<TicketingOrderManager> ticketingOrderManagers, Map<Long, TicketingOrderManager> ticketTypeIdToOrderManagerMap) {
        log.info("applyCouponCodeForOldOrder | orderId: {}, event: {}, recurringEventId: {}", orderId, event.getEventId(), recurringEventId);
        TicketingAppliedCouponForExistingOrderDto ticketingAppliedCouponForExistingOrderDto = new TicketingAppliedCouponForExistingOrderDto();
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(null, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        if (ticketing.isRecurringEvent() && !isNumberGreaterThanZero(recurringEventId)) { //we are not allow default coupons to apply
            throw new NotAcceptableException(NotAcceptableException.RecurringExceptionMsg.RECURRING_EVENT_ID_NOT_EMPTY);
        }
        TicketingOrder ticketingOrder = ticketingOrderService.findByidAndEventid(orderId, event);
        TicketingCoupon ticketingCoupon = ticketingOrder.getTicketingCoupon();
        double totalAppliedDiscountAmount = 0;
        if (DiscountType.FLAT.equals(ticketingCoupon.getDiscountType()) && TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo())) {
            List<EventTickets> eventTicketsList = eventTicketsService.findEventTicketsByOrderId(orderId);
            totalAppliedDiscountAmount = eventTicketsList.stream()
                    .mapToDouble(EventTickets::getDiscountAmount)
                    .sum();
        }
        log.info("applyCouponCodeForOldOrder | totalAppliedDiscountAmount: {} in this order {}", totalAppliedDiscountAmount, orderId);

        long usedCount = 0;
        if (NumberUtils.isNumberGreaterThanZero(ticketingCoupon.getUses())) {
            usedCount = ticketingCouponService.couponUsed(ticketingCoupon, event, null, recurringEventId);
        }
        Date startDate = TimeZoneUtil.getDateInUTC(ticketingCoupon.getCouponStartDate(), event.getEquivalentTimeZone());
        Date endDate = TimeZoneUtil.getDateInUTC(ticketingCoupon.getCouponEndDate(), event.getEquivalentTimeZone());

        if (((startDate.before(new Date())) && (endDate.after(new Date())) && ticketingCoupon.getUses() >= usedCount)) {
            log.info("applyCouponCodeForOldOrder | Coupon is valid for the order {} couponCode {}", orderId, ticketingCoupon.getId());
            StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);

            boolean isTicketTypeAvailbleInOrder = false;
            boolean isTicketTypeDonation = false;
            boolean isForAllTickets = StringUtils.isBlank(ticketingCoupon.getEventTicketTypeId());

            List<TicketTypeManagerDto> discountTicketTypeDtos = new ArrayList<>();
            List<String> couponTicketTypeList = new ArrayList<>();
            if (!isForAllTickets) {
                couponTicketTypeList
                        .addAll(Arrays.asList(ticketingCoupon.getEventTicketTypeId().split(Constants.STRING_COMMA)));
            }

            // need to check order amount is less then discount amount
            double totalPrice = 0;
            double discountAmount = 0;
            long numberOfUsesLeft = (!NumberUtils.isNumberGreaterThanZero(ticketingCoupon.getUses()) || TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo())) ? -1 : (ticketingCoupon.getUses() - usedCount);
            boolean isUnlimitedUsage = !NumberUtils.isNumberGreaterThanZero(numberOfUsesLeft);
            TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto = ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(ticketingOrderManagers, ticketingCoupon);

            for (TicketingOrderManager ticketingOrderManager : ticketingOrderManagers) {
                isTicketTypeDonation = false;
                boolean isDiscountAvailableForTicketType = false;
                long discountToBeAppliedOn = 0;
                double discount = 0;
                if (TicketType.DONATION.equals(ticketingOrderManager.getTicketType().getTicketType())) {
                    isTicketTypeDonation = true;
                }

                TicketingType ticketingType = ticketingOrderManager.getTicketType();
                String ticketTypeId = String.valueOf(ticketingType.getId());
                if (couponTicketTypeList.contains(ticketTypeId) || isForAllTickets) {
                    isTicketTypeAvailbleInOrder = true;
                    discountToBeAppliedOn = numberOfUsesLeft;
                    isDiscountAvailableForTicketType = true;
                }
                if ((isUnlimitedUsage || numberOfUsesLeft > ticketingOrderManager.getNumberofticket()
                        || TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo()))
                        && isTicketTypeAvailbleInOrder && !isTicketTypeDonation) {
                    discountToBeAppliedOn = ticketingOrderManager.getNumberofticket();
                }
                if (!CollectionUtils.isEmpty(holderEmailDiscountDtoList) && discountToBeAppliedOn > 0) {
                    int noOfDiscountedTicket = holderEmailDiscountDtoList.stream().filter(ticketTypeIdAndNoOfTicketsDto -> ticketTypeIdAndNoOfTicketsDto.getTicketTypeId().equals(ticketingType.getId())).mapToInt(HolderEmailDiscountDto::getNoOfDiscountedTickets).sum();
                    if (discountToBeAppliedOn > 0) {
                        discountToBeAppliedOn = noOfDiscountedTicket > discountToBeAppliedOn ? discountToBeAppliedOn : noOfDiscountedTicket;
                    }
                }

                SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(ticketingOrder.getEventid().getEventId());
                int numberOfTicketTypeExcludingFreeType = ticketingHelperService.getTotalNoOfTicketTypeExcludingFreeType(ticketingOrderManagers);
                List<TicketPriceDetails> ticketPriceDetailsList = ticketingHelperService.getTicketPriceDetails(discountToBeAppliedOn, ticketingOrderManager, Boolean.TRUE, ticketingCoupon, ticketingType, numberOfTicketTypeExcludingFreeType, totalTicketsAndTotalTicketPriceDto.getTotalTickets(),
                        ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, totalTicketsAndTotalTicketPriceDto.getTotalTicketPrice(), totalAppliedDiscountAmount);

                discount += ticketPriceDetailsList.stream().filter(e -> e.getDiscountedAmount() > 0).mapToDouble(TicketPriceDetails::getDiscountedAmount).sum();
                discountAmount += discount;
                double allTicketPriceSumWithFee = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getPriceWithFee).sum();
                double allValTax = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getVatTaxFee).sum();
                double allTicketSalesFees = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getSalesTaxFee).sum();

                totalPrice += allTicketPriceSumWithFee;
                log.info("applyCouponCodeForOldOrder | allTicketPriceSumWithFee: {}, allValTax: {}, allTicketSalesFees: {}, discountAmount: {}, totalPrice: {}", allTicketPriceSumWithFee, allValTax, allTicketSalesFees, discountAmount, totalPrice);

                ticketingType.setNumberofticket(ticketingOrderManager.getNumberofticket());
                ticketingOrderManager.setNumberOfDiscountedTicket(isDiscountAvailableForTicketType ? Long.valueOf(discountToBeAppliedOn).intValue() : 0);
                if (ticketTypeIdToOrderManagerMap != null && ticketTypeIdToOrderManagerMap.containsKey(ticketingType.getId())) {
                    TicketingOrderManager updatedTicketingOrderManager = ticketTypeIdToOrderManagerMap.get(ticketingType.getId());
                    updatedTicketingOrderManager.setNumberOfDiscountedTicket(updatedTicketingOrderManager.getNumberOfDiscountedTicket() + ticketingOrderManager.getNumberofticket());
                    ticketingOrderManagerService.save(updatedTicketingOrderManager);
                }
                numberOfUsesLeft = isUnlimitedUsage ? -1 : (numberOfUsesLeft - discountToBeAppliedOn);
                OptionalDouble totalFee = ticketPriceDetailsList.stream().mapToDouble(e -> e.getPriceWithFee() - e.getPrice()).average();

                Map<Double, List<TicketPriceDetails>> collect = ticketPriceDetailsList.stream()
                        .collect(Collectors.groupingBy(TicketPriceDetails::getDiscountedAmount));

                collect.entrySet().stream().forEach(e -> {
                    List<TicketPriceDetails> discountedTicketPriceDetail = e.getValue();

                    discountTicketTypeDtos.add(new TicketTypeManagerDto(ticketingType,
                            discountedTicketPriceDetail.size(),
                            GeneralUtils.getRoundValue(discountedTicketPriceDetail.get(0).getPriceWithFee()),
                            totalFee.isPresent() ? GeneralUtils.getRoundValue(totalFee.getAsDouble()) : 0,
                            GeneralUtils.getRoundValue(allValTax / discountedTicketPriceDetail.size()),
                            GeneralUtils.getRoundValue(allTicketSalesFees / discountedTicketPriceDetail.size()),
                            GeneralUtils.getRoundValue(discountedTicketPriceDetail.get(0).getDiscountedAmount())));
                });

            }

            if (isTicketTypeAvailbleInOrder) {
                log.info("applyCouponCodeForOldOrder | Coupon is applicable for the order {} couponCode {}", orderId, ticketingCoupon.getId());
                TicketingAppliedCouponDto ticketingAppliedCouponDto = new TicketingAppliedCouponDto();
                ticketingAppliedCouponDto.setTotalPrice(GeneralUtils.getRoundValue(totalPrice));
                ticketingAppliedCouponDto.setDiscountAmount(GeneralUtils.getRoundValue(discountAmount));
                ticketingAppliedCouponDto.setDiscountTicketTypes(discountTicketTypeDtos);
                ticketingAppliedCouponDto.setMessage(isTicketTypeDonation ? resourceBundle.getString(languageMap.get(COUPON_APPLIED_BUT_NOT_WITH_DONATION_TICKET_TYPE)) :
                        resourceBundle.getString(languageMap.get(COUPON_APPLIED_SUCCESS_MSG)));
                ticketingAppliedCouponForExistingOrderDto.setTicketingAppliedCouponDto(ticketingAppliedCouponDto);
                ticketingAppliedCouponForExistingOrderDto.setTicketingOrderManagers(ticketingOrderManagers);
                return ticketingAppliedCouponForExistingOrderDto;
            }
        }
        return null;
    }

    @Override
    public void removeCouponFromTicketingOrderWhilePurchase(Event event, TicketingOrder ticketingOrder, List<TicketingOrderManager> ticketingOrdersManager) {
        ticketingOrdersManager.forEach(orderManager -> {
            orderManager.setNumberOfDiscountedTicket(0);
        });
        if (ticketingOrder.getTicketingCoupon() != null) {
            ticketingOrder.setTicketingCoupon(null);
        }
    }

    @Override
    public TicketingAppliedCouponDto applyCouponCodeInApprovalTicket(Event event, CouponCodeApprovalTicketDto couponCodeApprovalTicketDto, User purchaser) {
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = roEventService.getLanguageCodeByUserOrEvent(null, event);
        ResourceBundle resourceBundle = CommonUtil.getLanguageResourceBundle(languageCode != null ? languageCode : EnumLabelLanguageCode.EN.toString());
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Long recurringEventId = couponCodeApprovalTicketDto.getRecurringEventId();
        if (ticketing.isRecurringEvent() && !isNumberGreaterThanZero(recurringEventId)) {
            throw new NotAcceptableException(NotAcceptableException.RecurringExceptionMsg.RECURRING_EVENT_ID_NOT_EMPTY);
        }

        List<TicketingType> ticketingTypes = ticketingTypeService.getByTicketingTypeIds(couponCodeApprovalTicketDto.getTicketTypes().keySet().stream().collect(Collectors.toList()));
        TicketingCoupon ticketingCoupon = ticketingCouponService.getByEventIdAndCouponCode(event.getEventId(), couponCodeApprovalTicketDto.getCouponCode(), recurringEventId);

        // create default ticketing order for validation and used previous methods
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(0);
        ticketingOrder.setEventid(event);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setTicketingCoupon(null);
        ticketingOrder.setPurchaser(purchaser);

        validateCoupon(ticketingCoupon, ticketingOrder, event, recurringEventId);

        List<TicketingOrderManager> ticketingOrderManagers = convertCouponCodeApprovalTicketDtoToTicketingOrderManager(couponCodeApprovalTicketDto, ticketingTypes);
        StripeDTO stripeDTO = stripeService.getStripeFeesByEvent(event);

        boolean isTicketTypeAvailbleInOrder = false;
        boolean isForAllTickets = StringUtils.isBlank(ticketingCoupon.getEventTicketTypeId());

        List<TicketTypeManagerDto> discountTicketTypeDtos = new ArrayList<>();
        List<String> couponTicketTypeList = new ArrayList<>();
        if (!isForAllTickets) {
            couponTicketTypeList.addAll(Arrays.asList(ticketingCoupon.getEventTicketTypeId().split(Constants.STRING_COMMA)));
        }

        // need to check total amount is less than discount amount
        double totalPrice = 0;
        double discountAmount = 0;
        long numberOfUsesLeft = ticketingCouponService.getRemainingCount(ticketingCoupon, ticketingOrder);
        boolean isUnlimitedUsage = numberOfUsesLeft == -1;
        TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto = ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(ticketingOrderManagers, ticketingCoupon);

        SalesTaxFeeDto salesTaxFeeDto = salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId());
        int numberOfTicketTypeExcludingFreeType = ticketingHelperService.getTotalNoOfTicketTypeExcludingFreeType(ticketingOrderManagers);

        for (TicketingOrderManager ticketingOrderManager : ticketingOrderManagers) {
            long discountToBeAppliedOn = 0;
            double discount = 0;
            TicketingType ticketingType = ticketingOrderManager.getTicketType();
            int numberOfTicket = couponCodeApprovalTicketDto.getTicketTypes().get(ticketingType.getId());

            String ticketTypeId = String.valueOf(ticketingType.getId());
            if (couponTicketTypeList.contains(ticketTypeId) || isForAllTickets) {
                isTicketTypeAvailbleInOrder = true;
                discountToBeAppliedOn = numberOfUsesLeft;
            }
            if ((isUnlimitedUsage || numberOfUsesLeft > numberOfTicket
                    || TicketingCoupon.ApplicableTo.PER_ORDER.equals(ticketingCoupon.getApplicableTo()))
                    && isTicketTypeAvailbleInOrder) {
                discountToBeAppliedOn = numberOfTicket;
            }

            List<TicketPriceDetails> ticketPriceDetailsList = ticketingHelperService
                    .getTicketPriceDetails(discountToBeAppliedOn, ticketingOrderManager, true, ticketingCoupon, ticketingType, numberOfTicketTypeExcludingFreeType, totalTicketsAndTotalTicketPriceDto.getTotalTickets(),
                            ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, totalTicketsAndTotalTicketPriceDto.getTotalTicketPrice(), 0);

            discount += ticketPriceDetailsList.stream().filter(e -> isNumberGreaterThanZero(e.getDiscountedAmount())).mapToDouble(TicketPriceDetails::getDiscountedAmount).sum();
            discountAmount += discount;
            double allTicketPriceSumWithFee = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getPriceWithFee).sum();
            double allVatTax = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getVatTaxFee).sum();
            double allTicketSalesFees = ticketPriceDetailsList.stream().mapToDouble(TicketPriceDetails::getSalesTaxFee).sum();

            totalPrice += allTicketPriceSumWithFee;
            ticketingType.setNumberofticket(ticketingOrderManager.getNumberofticket());

            numberOfUsesLeft = isUnlimitedUsage ? -1 : (numberOfUsesLeft - discountToBeAppliedOn);
            OptionalDouble totalFee = ticketPriceDetailsList.stream().mapToDouble(e -> e.getPriceWithFee() - e.getPrice()).average();

            Map<Double, List<TicketPriceDetails>> collect = ticketPriceDetailsList.stream()
                    .collect(Collectors.groupingBy(TicketPriceDetails::getDiscountedAmount));

            collect.entrySet().stream().forEach(e -> {
                List<TicketPriceDetails> discountedTicketPriceDetail = e.getValue();

                discountTicketTypeDtos.add(new TicketTypeManagerDto(ticketingType,
                        discountedTicketPriceDetail.size(),
                        GeneralUtils.getRoundValue(discountedTicketPriceDetail.get(0).getPriceWithFee()),
                        totalFee.isPresent() ? GeneralUtils.getRoundValue(totalFee.getAsDouble()) : 0,
                        GeneralUtils.getRoundValue(allVatTax / discountedTicketPriceDetail.size()),
                        GeneralUtils.getRoundValue(allTicketSalesFees / discountedTicketPriceDetail.size()),
                        GeneralUtils.getRoundValue(discountedTicketPriceDetail.get(0).getDiscountedAmount())));
            });

            if (isTicketTypeAvailbleInOrder) {
                TicketingAppliedCouponDto ticketingAppliedCouponDto = new TicketingAppliedCouponDto();
                ticketingAppliedCouponDto.setTotalPrice(GeneralUtils.getRoundValue(totalPrice));
                ticketingAppliedCouponDto.setDiscountAmount(GeneralUtils.getRoundValue(discountAmount));
                ticketingAppliedCouponDto.setDiscountTicketTypes(discountTicketTypeDtos);
                ticketingAppliedCouponDto.setMessage(resourceBundle.getString(languageMap.get(COUPON_APPLIED_SUCCESS_MSG)));
                return ticketingAppliedCouponDto;
            } else {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE);
            }

        }
        return null;
    }

    private List<TicketingOrderManager> convertCouponCodeApprovalTicketDtoToTicketingOrderManager(CouponCodeApprovalTicketDto couponCodeApprovalTicketDto, List<TicketingType> ticketingTypes) {
        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        couponCodeApprovalTicketDto.getTicketTypes().forEach((k, v) -> {
            TicketingOrderManager ticketingOrderManager = new TicketingOrderManager();
            ticketingOrderManager.setNumberofticket(v);
            ticketingOrderManager.setTicketType(ticketingTypes.stream().filter(ticketingType -> k.equals(ticketingType.getId())).findFirst().get());
            ticketingOrderManagers.add(ticketingOrderManager);
        });
        return ticketingOrderManagers;
    }

    public CouponCodeApprovalTicketDto convertRequestTocouponCodeApprovalTicketDto(CouponCodeApprovalTicketDto couponCodeApprovalTicketDto, Long requestId){
        RegistrationRequestTicket registrationRequestTicket = registrationRequestTicketRepository.findByRegistrationRequestId(requestId);
    }
}