package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.PlatformConfigDto;
import com.accelevents.billing.chargebee.repo.EventPlanConfigRepoService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.messages.TicketType;
import com.accelevents.registration.approval.services.RegistrationAttributeService;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.ro.eventTicket.ROTicketingHelperService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.PurchaserInfo;
import com.accelevents.ticketing.dto.TicketingEmailDto;
import com.accelevents.ticketing.dto.TicketingModuleDTO;
import com.accelevents.utils.*;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.accelevents.dto.TicketHolderHelper.getAttributeValue1;
import static com.accelevents.utils.Constants.*;
import static java.util.concurrent.TimeUnit.DAYS;

@Service
public class TicketingHelperServiceImpl implements TicketingHelperService {

    public static final String EVENT_STATUS_AND_EVENT_ID = "Event status{} and event id{}";
    private final Logger log = LoggerFactory.getLogger(TicketingHelperServiceImpl.class);

	//private static Map<Long, String> CACHED_CUSTOM_EMAIL_HEADER = new HashMap<>();//nosonar


	@Autowired
	private TicketHolderAttributesService ticketHolderAttributesService;

	@Autowired
	private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
	@Autowired
	private TicketingService ticketingService;
	@Autowired
	private TicketingRepository ticketingRepository;
    @Autowired
    private ROTicketingHelperService roTicketingHelperService;
	@Autowired
	private EventService eventService;
	@Autowired
	private EventTicketsRepoService eventTicketsRepoService;
	@Autowired
	private EventCommonRepoService eventCommonRepoService;
	@Autowired
	private CustomEmailService customEmailService;
	@Autowired
	private SalesTaxService salesTaxService;
	@Autowired
	private AllRequiresAttributesService allRequiresAttributesService;
	@Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private TicketingOrderService ticketingOrderService;
    @Autowired
    private VatTaxService vatTaxService;
    @Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Autowired
    private RegistrationAttributeService registrationAttributeService;

	@Override
	public void handleJSONValue(TicketHolderAttributes ticketHolderAttributes, TicketAttributeValueDto1 ticketAttributeValueDto) {
		try{
			ticketHolderAttributes.setJsonValue(ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto));
			ticketHolderAttributesService.save(ticketHolderAttributes);
		} catch (Exception e){
			log.error("Error while converting to json : {}", ticketHolderAttributes.getId());
		}
	}

	@Override
	public Unmarshaller getUnmashler() {
		JAXBContext jaxbContext;
		try {
			jaxbContext = JAXBContext.newInstance(TicketAttributeValueDto.class);
			return jaxbContext.createUnmarshaller();
		} catch (JAXBException e) {
			log.info(e.getLocalizedMessage(), e);
		}
		return null;
	}


	@Transactional(isolation = Isolation.READ_COMMITTED)
	@Override
	public Ticketing findTicketingByEventAndIfNotFoundCreateNew(Event event) {
		Ticketing ticketing = ticketingRepository.findByEventid(event);
		if (ticketing == null) {
			ticketing = createNewTicketing(event);
		}
		return ticketing;
	}

	@Override
	public Ticketing createNewTicketing(Event event) {
	    EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(event.getEventId());
		Ticketing ticketing = new Ticketing();
		ticketing.setCollectTicketHolderAttributes(true);
		ticketing.setCollectAddOnAttributes(false);
        ticketing.setUniqueTicketHolderEmail(false);
        ticketing.setUniqueTicketBuyerEmail(false);
		ticketing.setEventid(event);
        ticketing.setOfflinePayment(false);
		ticketing.setSocialSharing(false);
		ticketing.setShowEnterEventButtonInReminderTemplate(true);
        Calendar calendar = Calendar.getInstance();
        log.info("createNewTicketing calendar time {}",calendar.getTime());
        if (null != event.getEquivalentTimeZone()) {
            calendar.setTimeZone(TimeZone.getTimeZone(event.getEquivalentTimeZone()));
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        Date eventStartDate = TimeZoneUtil.getDateInUTC(dateFormat.format(calendar.getTime()), event.getEquivalentTimeZone(),null);
        log.info("createNewTicketing eventStartDate {}",eventStartDate);
        ticketing.setEventStartDate(eventStartDate);

        calendar.add(Calendar.HOUR, 6);
        Date eventEndDate = TimeZoneUtil.getDateInUTC(dateFormat.format(calendar.getTime()), event.getEquivalentTimeZone(),null);
        log.info("createNewTicketing eventEndDate {}",eventEndDate);
        ticketing.setEventEndDate(eventEndDate);
		if(null != eventPlanConfig) {
            PlatformConfigDto platformConfigDto = PlatformConfigDto.convertJSONToObject(eventPlanConfig.getPlatformConfigJson());
            ticketing.setPostEventAccessMinutes((int) DAYS.toMinutes(Long.parseLong(platformConfigDto.getPostEventAccessDay())));
            ticketing.setPreEventAccessMinutes((int) DAYS.toMinutes(Long.parseLong(platformConfigDto.getPreEventAccess())));
        }else{
		    ticketing.setPostEventAccessMinutes(43200);
            ticketing.setPreEventAccessMinutes(30);
        }
		ticketing.setAllowEditingOfDisclaimer(true);
        ticketing.setStatus(EnumOverageStatus.CREATE);
		save(ticketing, event);
		return ticketing;
	}

	private void save(Ticketing ticketing, Event event) {
		ticketing.setCheckoutminutes(Constants.TICKETING_DEFAULT_TIMEOUT);
		this.ticketingService.save(ticketing);
        log.info(EVENT_STATUS_AND_EVENT_ID,event.getEventStatus(),event.getEventId());
        addDefaultAttributes(event);
		if (event.getTicketingId() == null) {
			event.setTicketingId(ticketing.getId());
			eventRepoService.save(event);
		}
	}


	@Override
	public List<TicketHolderRequiredAttributes> addDefaultAttributes(Event event) {
		List<TicketHolderRequiredAttributes> ticketHolderAttributes = new ArrayList<>();
		TicketHolderRequiredAttributes prefix = new TicketHolderRequiredAttributes();
		prefix.setName(Constants.PREFIX);
		prefix.setAttributeValueType(AttributeValueType.DROPDOWN);
		prefix.setEventid(event);
		prefix.setBuyerAttributeOrder(1000);
        prefix.setHolderAttributeOrder(1000);
		prefix.setAttribute(true);
        prefix.setDefaultValueJsonPurchaser(PREFIX_DEFAULT_JSON);
        prefix.setDefaultValueJsonHolder(PREFIX_DEFAULT_JSON);
        prefix.setDefaultAttribute(true);
		allRequiresAttributesService.save(prefix);
		ticketHolderAttributes.add(prefix);

		TicketHolderRequiredAttributes firstName = new TicketHolderRequiredAttributes();
		firstName.setName(Constants.FIRST_NAME);
		firstName.setAttributeValueType(AttributeValueType.TEXT);
		firstName.setEnabledForTicketPurchaser(true);
		firstName.setRequiredForTicketPurchaser(true);
		firstName.setEnabledForTicketHolder(true);
		firstName.setRequiredForTicketHolder(true);
		firstName.setEventid(event);
		firstName.setBuyerAttributeOrder(2000);
        firstName.setHolderAttributeOrder(2000);
		firstName.setAttribute(true);
        firstName.setDefaultAttribute(true);
        firstName.setEnabledForKiosk(true);
		allRequiresAttributesService.save(firstName);
		ticketHolderAttributes.add(firstName);

		TicketHolderRequiredAttributes lastname = new TicketHolderRequiredAttributes();
		lastname.setName(Constants.LAST_NAME);
		lastname.setAttributeValueType(AttributeValueType.TEXT);
		lastname.setRequiredForTicketHolder(true);
		lastname.setRequiredForTicketPurchaser(true);
		lastname.setEnabledForTicketHolder(true);
		lastname.setEnabledForTicketPurchaser(true);
		lastname.setEventid(event);
		lastname.setBuyerAttributeOrder(3000);
        lastname.setHolderAttributeOrder(3000);
		lastname.setAttribute(true);
        lastname.setDefaultAttribute(true);
        lastname.setEnabledForKiosk(true);
		allRequiresAttributesService.save(lastname);
		ticketHolderAttributes.add(lastname);

		TicketHolderRequiredAttributes emailAddress = new TicketHolderRequiredAttributes();
		emailAddress.setName(Constants.EMAIL);
		emailAddress.setAttributeValueType(AttributeValueType.EMAIL);
		emailAddress.setRequiredForTicketHolder(true);
		emailAddress.setRequiredForTicketPurchaser(true);
		emailAddress.setEnabledForTicketHolder(true);
		emailAddress.setEnabledForTicketPurchaser(true);
		emailAddress.setEventid(event);
		emailAddress.setBuyerAttributeOrder(4000);
        emailAddress.setHolderAttributeOrder(4000);
		emailAddress.setAttribute(true);
        emailAddress.setDefaultAttribute(true);
        emailAddress.setEnabledForKiosk(true);
		allRequiresAttributesService.save(emailAddress);
		ticketHolderAttributes.add(emailAddress);

		TicketHolderRequiredAttributes cellphone = new TicketHolderRequiredAttributes();
		cellphone.setName("Cell Phone");
		cellphone.setAttributeValueType(AttributeValueType.NUMBER);
		cellphone.setEventid(event);
		cellphone.setBuyerAttributeOrder(6000);
        cellphone.setHolderAttributeOrder(6000);
		cellphone.setAttribute(true);
        cellphone.setDefaultAttribute(true);
		allRequiresAttributesService.save(cellphone);
		ticketHolderAttributes.add(cellphone);

        TicketHolderRequiredAttributes country = new TicketHolderRequiredAttributes();
        country.setName(Constants.COUNTRY);
        country.setAttributeValueType(AttributeValueType.COUNTRY);
        country.setEventid(event);
        country.setBuyerAttributeOrder(7000);
        country.setHolderAttributeOrder(7000);
        country.setAttribute(true);
        country.setDefaultAttribute(true);
        allRequiresAttributesService.save(country);
        ticketHolderAttributes.add(country);

        TicketHolderRequiredAttributes state = new TicketHolderRequiredAttributes();
        state.setName(Constants.STATE);
        state.setAttributeValueType(AttributeValueType.STATE);
        state.setEventid(event);
        state.setBuyerAttributeOrder(8000);
        state.setHolderAttributeOrder(8000);
        state.setAttribute(true);
        state.setDefaultAttribute(true);
        allRequiresAttributesService.save(state);
        ticketHolderAttributes.add(state);

        log.info(EVENT_STATUS_AND_EVENT_ID,event.getEventStatus(),event.getEventId());
        TicketHolderRequiredAttributes billingAddressByEvent = ticketHolderRequiredAttributesService.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType.BILLING_ADDRESS, event);
        if (null == billingAddressByEvent) {
            TicketHolderRequiredAttributes billingaddress = new TicketHolderRequiredAttributes();
            billingaddress.setName(Constants.BILLING_ADDRESS);
            billingaddress.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
            billingaddress.setRequiredForTicketPurchaser(false);
            billingaddress.setEnabledForTicketHolder(false);
            billingaddress.setEnabledForTicketPurchaser(false);
            billingaddress.setEventid(event);
            billingaddress.setBuyerAttributeOrder(9000);
            billingaddress.setHolderAttributeOrder(9000);
            billingaddress.setAttribute(true);
            billingaddress.setDefaultAttribute(true);
            allRequiresAttributesService.save(billingaddress);
            ticketHolderAttributes.add(billingaddress);
        }

        log.info(EVENT_STATUS_AND_EVENT_ID,event.getEventStatus(),event.getEventId());
        TicketHolderRequiredAttributes shippingAddressByEvent = ticketHolderRequiredAttributesService.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType.SHIPPING_ADDRESS, event);
        if (null == shippingAddressByEvent) {
            TicketHolderRequiredAttributes shippingaddress = new TicketHolderRequiredAttributes();
            shippingaddress.setName(Constants.SHIPPING_ADDRESS);
            shippingaddress.setAttributeValueType   (AttributeValueType.SHIPPING_ADDRESS);
            shippingaddress.setEventid(event);
            shippingaddress.setBuyerAttributeOrder(10000);
            shippingaddress.setHolderAttributeOrder(10000);
            shippingaddress.setAttribute(true);
            shippingaddress.setDefaultAttribute(true);
            allRequiresAttributesService.save(shippingaddress);
            ticketHolderAttributes.add(shippingaddress);
        }

		TicketHolderRequiredAttributes gender = new TicketHolderRequiredAttributes();
		gender.setName(Constants.GENDER);
		gender.setAttributeValueType(AttributeValueType.DROPDOWN);
		gender.setEventid(event);
		gender.setBuyerAttributeOrder(11000);
        gender.setHolderAttributeOrder(11000);
		gender.setAttribute(true);
        gender.setDefaultValueJsonPurchaser(GENDER_DEFAULT_JSON);
        gender.setDefaultValueJsonHolder(GENDER_DEFAULT_JSON);
        gender.setDefaultAttribute(true);
        allRequiresAttributesService.save(gender);
		ticketHolderAttributes.add(gender);

		TicketHolderRequiredAttributes birthday = new TicketHolderRequiredAttributes();
		birthday.setName(Constants.BIRTHDAY);
		birthday.setAttributeValueType(AttributeValueType.DATE);
		birthday.setEventid(event);
		birthday.setBuyerAttributeOrder(12000);
        birthday.setHolderAttributeOrder(12000);
		birthday.setAttribute(true);
        birthday.setDefaultAttribute(true);
		allRequiresAttributesService.save(birthday);
		ticketHolderAttributes.add(birthday);

		TicketHolderRequiredAttributes age = new TicketHolderRequiredAttributes();
		age.setName(Constants.AGE);
		age.setAttributeValueType(AttributeValueType.NUMBER);
		age.setEventid(event);
		age.setBuyerAttributeOrder(13000);
        age.setHolderAttributeOrder(13000);
		age.setAttribute(true);
        age.setDefaultAttribute(true);
		allRequiresAttributesService.save(age);
		ticketHolderAttributes.add(age);

		TicketHolderRequiredAttributes organization = new TicketHolderRequiredAttributes();
		organization.setName(Constants.ORGANIZATION);
		organization.setAttributeValueType(AttributeValueType.TEXT);
		organization.setEventid(event);
		organization.setAttribute(true);
		organization.setBuyerAttributeOrder(14000);
        organization.setHolderAttributeOrder(14000);
        organization.setDefaultAttribute(true);
		allRequiresAttributesService.save(organization);
		ticketHolderAttributes.add(organization);

		TicketHolderRequiredAttributes jobtitle = new TicketHolderRequiredAttributes();
		jobtitle.setName(Constants.JOB_TITLE);
		jobtitle.setAttributeValueType(AttributeValueType.TEXT);
		jobtitle.setEventid(event);
		jobtitle.setBuyerAttributeOrder(15000);
        jobtitle.setHolderAttributeOrder(15000);
		jobtitle.setAttribute(true);
        jobtitle.setDefaultAttribute(true);
		allRequiresAttributesService.save(jobtitle);
		ticketHolderAttributes.add(jobtitle);

		TicketHolderRequiredAttributes image = new TicketHolderRequiredAttributes();
		image.setName(Constants.IMAGE);
		image.setAttributeValueType(AttributeValueType.IMAGE);
		image.setEventid(event);
		image.setBuyerAttributeOrder(16000);
        image.setHolderAttributeOrder(16000);
		image.setAttribute(true);
        image.setDefaultAttribute(true);
		allRequiresAttributesService.save(image);
		ticketHolderAttributes.add(image);

        TicketHolderRequiredAttributes upload = new TicketHolderRequiredAttributes();
        upload.setName(Constants.UPLOAD);
        upload.setAttributeValueType(AttributeValueType.UPLOAD);
        upload.setEventid(event);
        upload.setBuyerAttributeOrder(17000);
        upload.setHolderAttributeOrder(17000);
        upload.setAttribute(true);
        upload.setDefaultAttribute(true);
        allRequiresAttributesService.save(upload);
        ticketHolderAttributes.add(upload);

        TicketHolderRequiredAttributes pronouns = new TicketHolderRequiredAttributes();
        pronouns.setName(Constants.PRONOUNS);
        pronouns.setAttributeValueType(AttributeValueType.TEXT);
        pronouns.setEventid(event);
        pronouns.setBuyerAttributeOrder(18000);
        pronouns.setHolderAttributeOrder(18000);
        pronouns.setAttribute(true);
        pronouns.setDefaultAttribute(true);
        allRequiresAttributesService.save(pronouns);
        ticketHolderAttributes.add(pronouns);

        TicketHolderRequiredAttributes bio = new TicketHolderRequiredAttributes();
        bio.setName(Constants.ABOUT_ME);
        bio.setAttributeValueType(AttributeValueType.TEXT);
        bio.setEventid(event);
        bio.setBuyerAttributeOrder(19000);
        bio.setHolderAttributeOrder(19000);
        bio.setAttribute(true);
        bio.setDefaultAttribute(true);
        allRequiresAttributesService.save(bio);
        ticketHolderAttributes.add(bio);

        TicketHolderRequiredAttributes facebook = new TicketHolderRequiredAttributes();
        facebook.setName(Constants.FACEBOOK);
        facebook.setAttributeValueType(AttributeValueType.TEXT);
        facebook.setEventid(event);
        facebook.setBuyerAttributeOrder(20000);
        facebook.setHolderAttributeOrder(20000);
        facebook.setAttribute(true);
        facebook.setDefaultAttribute(true);
        allRequiresAttributesService.save(facebook);
        ticketHolderAttributes.add(facebook);

        TicketHolderRequiredAttributes instagram = new TicketHolderRequiredAttributes();
        instagram.setName(Constants.INSTAGRAM);
        instagram.setAttributeValueType(AttributeValueType.TEXT);
        instagram.setEventid(event);
        instagram.setBuyerAttributeOrder(21000);
        instagram.setHolderAttributeOrder(21000);
        instagram.setAttribute(true);
        instagram.setDefaultAttribute(true);
        allRequiresAttributesService.save(instagram);
        ticketHolderAttributes.add(instagram);

        TicketHolderRequiredAttributes linkedIn = new TicketHolderRequiredAttributes();
        linkedIn.setName(Constants.LINKED_IN);
        linkedIn.setAttributeValueType(AttributeValueType.TEXT);
        linkedIn.setEventid(event);
        linkedIn.setBuyerAttributeOrder(22000);
        linkedIn.setHolderAttributeOrder(22000);
        linkedIn.setAttribute(true);
        linkedIn.setDefaultAttribute(true);
        allRequiresAttributesService.save(linkedIn);
        ticketHolderAttributes.add(linkedIn);

        TicketHolderRequiredAttributes twitter = new TicketHolderRequiredAttributes();
        twitter.setName(Constants.TWITTER);
        twitter.setAttributeValueType(AttributeValueType.TEXT);
        twitter.setEventid(event);
        twitter.setBuyerAttributeOrder(23000);
        twitter.setHolderAttributeOrder(23000);
        twitter.setAttribute(true);
        twitter.setDefaultAttribute(true);
        allRequiresAttributesService.save(twitter);
        ticketHolderAttributes.add(twitter);

        TicketHolderRequiredAttributes interest = new TicketHolderRequiredAttributes();
        interest.setName(Constants.INTEREST_NAME);
        interest.setAttributeValueType(AttributeValueType.INTEREST);
        interest.setEventid(event);
        interest.setHiddenForPurchaser(true);
        interest.setBuyerAttributeOrder(24000);
        interest.setHolderAttributeOrder(24000);
        interest.setDeletedForBuyer(true);
        interest.setAttribute(true);
        interest.setDefaultAttribute(false);
        allRequiresAttributesService.save(interest);
        ticketHolderAttributes.add(interest);

		return ticketHolderAttributes;
	}


	@Override
	public PurchaserInfo getPurchaserInfo(TicketingOrder ticketingOrder, Event event) {
		EventTickets eventTickets = eventCommonRepoService.findByOrderWithOneTicket(ticketingOrder);

		if (eventTickets == null) {
			return new PurchaserInfo(ticketingOrder.getPurchaser());
		}

		TicketHolderAttributes ticketHolderAttributes = ticketHolderAttributesService
				.findById(eventTickets.getTicketHolderAttributesId().getId());
		List<TicketHolderRequiredAttributes> holderRequiredAttributes = ticketHolderRequiredAttributesService
				.getAllAttributes(event);
		return this.getPurchaserInfo(ticketingOrder, event, ticketHolderAttributes,
				holderRequiredAttributes, eventTickets);
	}

	@Override
    public Map<Long, PurchaserInfo> getPurchaserInfoyByOrderIds(List<Long> orderIds, Event event) {
	    log.info("Start of get purchaser info by orderIds for event {} ", event.getEventId());
	    Map<Long, PurchaserInfo> purchaserInfoByOrderId = new HashMap<>();

        List<TicketingOrder> orderList = ticketingOrderService.findByOrderIds(orderIds);
        List<EventTickets> eventTicketsList = eventCommonRepoService.findAllEventTicketByOrderIds(orderIds);
        Map<Long, List<EventTickets>> ticketsByOrderId = eventTicketsList.stream().collect(Collectors.groupingBy(EventTickets::getTicketingOrderId));

        orderList.parallelStream().forEach(ticketingOrder -> {
            List<EventTickets> orderEventTickets = ticketsByOrderId.get(ticketingOrder.getId());

            // Fetching the first ticket, if any exist
            EventTickets eventTickets = CollectionUtils.isEmpty(orderEventTickets) ? null : orderEventTickets.get(0);

            // Get PurchaserInfo from ticket holder attributes or fallback to ticket purchaser
            PurchaserInfo purchaserInfo = (eventTickets != null && eventTickets.getTicketHolderAttributesId() != null)
                    ? this.getPurchaseInfoByEventTicket(eventTickets.getTicketHolderAttributesId(), event, ticketingOrder)
                    : new PurchaserInfo(ticketingOrder.getPurchaser());

            purchaserInfoByOrderId.put(ticketingOrder.getId(), purchaserInfo);
        });

        log.info("Successfully fetch purchaser info for orderIds for event {}, result size {}", event.getEventId(), purchaserInfoByOrderId.size());
        return purchaserInfoByOrderId;
    }

	@Override
	public Ticketing findTicketingByEvent(Event event) {
		return ticketingRepository.findByEventid(event);
	}

	@Override
	public TicketingModuleDTO findTicketingByEventId(Event event) {
		return ticketingRepository.findTicketingByEventId(event);
	}

	@Override
	public TicketingModuleDTO findTicketingFieldsByEventId(Event event) {
		return ticketingRepository.findTicketingFieldsByEventId(event);
	}

	@Override
	public boolean findShowRemainingTicketsByEventid(Event event) {
		return ticketingRepository.findShowRemainingTicketsByEventid(event);
	}

    public PurchaserInfo getPurchaseInfoByEventTicket(TicketHolderAttributes ticketHolderAttributes,Event event, TicketingOrder ticketingOrder){

        PurchaserInfo purchaserInfo = new PurchaserInfo();
        TicketAttributeValueDto1 ticketAttributeValueDto = getTicketAttributeValueDto1(event, ticketHolderAttributes);

        Map<String, String> purchaserAttribute = (Map<String, String>) ticketAttributeValueDto.getPurchaser().get(Constants.TICKETING.ATTRIBUTES);
        if (purchaserAttribute != null && !CollectionUtils.isEmpty(purchaserAttribute)) {
            for (Map.Entry<String, String> entry : purchaserAttribute.entrySet()) {
                if (STRING_FIRST_SPACE_NAME.equalsIgnoreCase(entry.getKey())) {
                    purchaserInfo.setFirstName(entry.getValue());
                }else if(STRING_LAST_SPACE_NAME.equalsIgnoreCase(entry.getKey())){
                    purchaserInfo.setLastName(entry.getValue());
                }else if(STRING_EMAIL_SPACE.equalsIgnoreCase(entry.getKey())){
                    purchaserInfo.setEmail(entry.getValue());
                }
            }
        }
        if (StringUtils.isNotEmpty(purchaserInfo.getEmail())
                && StringUtils.isNotEmpty(purchaserInfo.getFirstName())
                && StringUtils.isNotEmpty(purchaserInfo.getLastName())) {
            return purchaserInfo;
        } else {
            return new PurchaserInfo(ticketingOrder.getPurchaser());
        }
    }

	@Override
	public PurchaserInfo getPurchaserInfo(TicketingOrder ticketingOrder, Event event,
										  TicketHolderAttributes ticketHolderAttributes,
										  List<TicketHolderRequiredAttributes> holderRequiredAttributes,
										  EventTickets eventTickets) {
		PurchaserInfo purchaserInfo = null;
		if (eventTickets!=null) {
			try {
                TicketAttributeValueDto1 ticketAttributeValueDto = getTicketAttributeValueDto1(event, ticketHolderAttributes);

                purchaserInfo = new PurchaserInfo();
				for (TicketHolderRequiredAttributes holderRequiredAttribute : holderRequiredAttributes) {
					if (holderRequiredAttribute.getEnabledForTicketPurchaser()) {
						String attributeName = holderRequiredAttribute.getName();
						if (STRING_FIRST_SPACE_NAME.equals(attributeName)) {
							String value = getAttributeValue1(ticketAttributeValueDto, attributeName, false,
									holderRequiredAttribute.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()));
							purchaserInfo.setFirstName(value);
						} else if (STRING_LAST_SPACE_NAME.equals(attributeName)) {
							String value = getAttributeValue1(ticketAttributeValueDto, attributeName, false,
									holderRequiredAttribute.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()));
							purchaserInfo.setLastName(value);
						} else if (STRING_EMAIL_SPACE.equalsIgnoreCase(attributeName)) {
							String value = getAttributeValue1(ticketAttributeValueDto, attributeName, false,
									holderRequiredAttribute.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()));
							purchaserInfo.setEmail(value);
						}
					}
				}
				//log.info("Other Time -> Time : " + (System.currentTimeMillis() - startTime));//nosonar
			} catch (Exception e) {
				purchaserInfo = null;
				log.error(e.getMessage(), e);
			}
		}
		if (purchaserInfo != null && StringUtils.isNotEmpty(purchaserInfo.getEmail())
				&& StringUtils.isNotEmpty(purchaserInfo.getFirstName())
				&& StringUtils.isNotEmpty(purchaserInfo.getLastName())) {
			return purchaserInfo;
		} else {
			return new PurchaserInfo(ticketingOrder.getPurchaser());
		}
	}

    private TicketAttributeValueDto1 getTicketAttributeValueDto1(Event event, TicketHolderAttributes ticketHolderAttributes) {
        TicketAttributeValueDto1 ticketAttributeValueDto;
        try {
            ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
        }catch(Exception e){//NOSONAR
            log.info("Error while unMarshaling || TicketingHelperServiceImpl || getPurchaserInfo() || EventId : {}" +
                    " || ticketHolderAttribute Id : {} || Exception : {}", event.getEventId(), ticketHolderAttributes.getId(), e);
            throw e;
        }
        return ticketAttributeValueDto;
    }

    @Override
	public String getTicketOrderHeaderText(Event event) {
		String templateHeaderText = null;
		Optional<CustomEmail> customEmailO = customEmailService.getCustomEmailByEventId(event.getEventId());
		if (customEmailO.isPresent()) {
			templateHeaderText = customEmailO.get().getEventTicketOrderConfirmationHeader();
		}
		return templateHeaderText;
	}

	@Override
	@Transactional
	public void saveTicketEmailAttributes(TicketingEmailDto emailDto, Event event) {
		this.saveCustomHeaderForEvent(emailDto.getEmailHeader(), event);
	}

	protected void saveCustomHeaderForEvent(String headerText, Event event) {
		if (StringUtils.isNotBlank(headerText)) {
			Optional<CustomEmail> customEmailO = customEmailService.getCustomEmailByEventId(event.getEventId());
			String imageRegEx = "(<img.[^*]*?>)";
            String styleRegEx = "(<img\\b|(?!^)\\G)[^>]*?\\b(style)=([\\\"']?)([^>]*?)\\3";
            String widthRegEx = "(width)=(\\\".*?\\\")";
            String heightRegEx = "(height)=(\\\".*?\\\")";

            Pattern pattern = Pattern.compile(imageRegEx);
            Pattern stylePattern = Pattern.compile(styleRegEx);
            Pattern widthPattern = Pattern.compile(widthRegEx);
            Pattern heightPattern = Pattern.compile(heightRegEx);
            //Matching the compiled pattern in the String

            Matcher matcher = pattern.matcher(headerText);
            while (matcher.find()) {
                String originalImageTag = matcher.group(0);
                String updateImageTag =originalImageTag;
                Matcher widthMatcher = widthPattern.matcher(originalImageTag);
                if(widthMatcher.find()) {
                    updateImageTag = widthMatcher.replaceAll("");
                }
                Matcher heightMatcher = heightPattern.matcher(originalImageTag);
                if (heightMatcher.find()) {
                    updateImageTag = heightMatcher.replaceAll("");
                }
                Matcher styleMatcher = stylePattern.matcher(originalImageTag);
                if (styleMatcher.find()) {
                    StringBuilder updatedString = new StringBuilder();
                    String widthHeightStyle = styleMatcher.group(4);
                    String styleString = "style=\""+widthHeightStyle+"\"";
                    String[] widthHeightArray= widthHeightStyle.split(";");
                    updatedString.append(styleString).append(" ");
                    for (String d : widthHeightArray) {
                        if (d.contains("width") || d.contains("height")) {
                            String updateContentString =d.replace(":","=\"").replace("px","\"").replaceAll("\\s", "");
                            updatedString.append(updateContentString).append(" ");
                        }
                    }
                    updateImageTag= updateImageTag.replaceAll(styleString,updatedString.toString());
                }
                headerText = headerText.replaceAll(originalImageTag,updateImageTag);
            }
			CustomEmail customEmail = customEmailO.orElse(new CustomEmail(event.getEventId()));
			customEmail.setEventTicketOrderConfirmationHeader(headerText);
			this.customEmailService.save(customEmail);
		}
	}

	@Override
	public List<TicketPriceDetails>  getTicketPriceDetails(long numberOfUsageLeft,
                                                           TicketingOrderManager orderManager,
                                                           boolean isCardPayment,
                                                           TicketingCoupon ticketingCoupon,
                                                           TicketingType ticketingType,
                                                           int numberOfTicketTypeExcludingFreeType,
                                                           int totalTickets,
                                                           TicketingOrder ticketingOrder, StripeDTO stripeDTO,
                                                           boolean isInternationalPayment,
                                                           boolean depositPayment,
                                                           SalesTaxFeeDto salesTaxFeeDto,
                                                           double totalTicketsPrice,
                                                           double totalAppliedDiscountAmount) {
		List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
		double finalDiscount = 0;
		boolean is100PerDiscount = false;
		boolean isComplementaryTickets = false;
		double ticketPrice = ticketingType.getPrice();
		int totalPurchasedTicket = orderManager.getNumberofticket() - (NumberUtils.isNumberGreaterThanZero(ticketingOrder.getId()) ? eventTicketsRepoService.countDeletedTicketByOrderIdAndTicketTypeId(ticketingOrder.getId(), ticketingType.getId()) : 0);
        if(totalPurchasedTicket ==0){
            return ticketPriceDetailsList;
        }
		if(TicketType.DONATION.equals(ticketingType.getTicketType())){
			ticketPrice = orderManager.getDonationTicketAmount();
		}

		long countOfTicketOnWhichCouponApplied = 0;
		int numberOf100PerDiscountedTickets = 0;
		if (ticketingCoupon != null && numberOfUsageLeft > 0 && !TicketType.DONATION.equals(ticketingType.getTicketType())) {
			Discount discount = new Discount(numberOfUsageLeft, ticketingCoupon, ticketingType, totalPurchasedTicket, totalTickets, totalTicketsPrice, totalAppliedDiscountAmount).invoke();
			is100PerDiscount = discount.isIs100PerDiscountOnAllTickets();
			countOfTicketOnWhichCouponApplied = discount.getCountOfTicketOnWhichCouponApplied();
			finalDiscount = discount.getFinalDiscount();
			numberOf100PerDiscountedTickets = discount.getNumberOfFullDiscountedTickets();
		}

        if (Constants.COMPLIMENTARY.equals(ticketingOrder.getOrderType().toString())) {
            isComplementaryTickets = true;
        }
        double vatTaxRate = vatTaxService.getVatTaxByTicketTypeOrEvent(ticketingOrder.getEventid().getEventId(),ticketingType);
        double capAmount=transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(ticketingType.getTicketTypeFormat(),ticketingType.getTicketing().getEventid());
        if(ticketingType.isPassFeeVatToBuyer() == null || !ticketingType.isPassFeeVatToBuyer()){
            vatTaxRate = 0;
        }

		if(isComplementaryTickets){
			setTicketPriceDetailsForComplementaryTicket(totalPurchasedTicket, ticketPriceDetailsList);
		} else {
            boolean isNotExternalTransaction=!ticketingOrder.getOrderType().equals(TicketingOrder.OrderType.EXTERNAL_TRANSACTION);

            if (!is100PerDiscount) {
				long ticketsWithoutDiscount = (totalPurchasedTicket - countOfTicketOnWhichCouponApplied);
                double finalTicketPrice = ticketPrice;
                if (finalDiscount > 0) {
                    finalTicketPrice = ticketPrice - finalDiscount;
                }
				if (isCardPayment && isNotExternalTransaction) {
					for(int i = 0; i < ticketsWithoutDiscount;i++){
						TicketPriceDetails ticketPriceDetails = getTicketPriceDetails(ticketingType.isPayLater(),totalPurchasedTicket - numberOf100PerDiscountedTickets, ticketingType, numberOfTicketTypeExcludingFreeType, ticketPrice, stripeDTO, salesTaxFeeDto,vatTaxRate, depositPayment,capAmount);
						ticketPriceDetailsList.add(ticketPriceDetails);
					}

					for(int i = 0; i < countOfTicketOnWhichCouponApplied;i++){
						TicketPriceDetails ticketPriceDetails = getTicketPriceDetails(ticketingType.isPayLater(),totalPurchasedTicket - numberOf100PerDiscountedTickets, ticketingType, numberOfTicketTypeExcludingFreeType, finalTicketPrice,  stripeDTO, salesTaxFeeDto,vatTaxRate, depositPayment,capAmount);
                        ticketPriceDetails.setDiscounted(finalDiscount > 0);
                        ticketPriceDetails.setDiscountedAmount(finalDiscount);
                        ticketPriceDetailsList.add(ticketPriceDetails);
					}

				} else {

                    for(int i = 0; i < ticketsWithoutDiscount;i++){
                        TicketPriceDetails ticketPriceDetails = getTicketPriceDetails(true,totalPurchasedTicket - numberOf100PerDiscountedTickets, ticketingType, numberOfTicketTypeExcludingFreeType, ticketPrice, stripeDTO, salesTaxFeeDto,vatTaxRate, depositPayment,capAmount);
                        updateTaxFeesAndPriceForCashOrUnpaid(ticketingType, ticketPriceDetails);
                        ticketPriceDetailsList.add(ticketPriceDetails);
                    }

                    for(int i = 0; i < countOfTicketOnWhichCouponApplied;i++){
                        TicketPriceDetails ticketPriceDetails = getTicketPriceDetails(true,totalPurchasedTicket - numberOf100PerDiscountedTickets, ticketingType, numberOfTicketTypeExcludingFreeType, finalTicketPrice,  stripeDTO, salesTaxFeeDto,vatTaxRate, depositPayment,capAmount);
                        ticketPriceDetails.setDiscounted(finalDiscount > 0);
                        ticketPriceDetails.setDiscountedAmount(finalDiscount);
                        updateTaxFeesAndPriceForCashOrUnpaid(ticketingType, ticketPriceDetails);
                        ticketPriceDetailsList.add(ticketPriceDetails);
                    }
                }
			} else {
				setTicketPriceDetailsFor100PercentDiscount(totalPurchasedTicket, ticketPriceDetailsList, finalDiscount);
			}
		}
		return ticketPriceDetailsList;
	}

    private void updateTaxFeesAndPriceForCashOrUnpaid(TicketingType ticketingType, TicketPriceDetails ticketPriceDetails) {
        double salesTaxFee = ticketPriceDetails.getPayLaterSalesTax();
        double vatTaxFee = ticketPriceDetails.getPayLaterVatTaxFee();
        ticketPriceDetails.setPriceWithFee( ticketPriceDetails.getPriceWithFeePayLater());
        ticketPriceDetails.setVatTaxFee(vatTaxFee);
        ticketPriceDetails.setSalesTaxFee(salesTaxFee);
        ticketPriceDetails.setTicketTypeId(ticketingType.getId());
    }


    @Override
    public double calculateDepositAmountForTicketType(TicketingType ticketingType, double finalTicketPrice) {
        if (!ticketingType.isRequireDepositAmount() || ticketingType.getDepositAmount() <= 0) {
            return finalTicketPrice;
        }

        double depositAmount = ticketingType.getDepositAmount();
        if (DepositType.PERCENTAGE.equals(ticketingType.getDepositType())) {
            return Math.min(finalTicketPrice, GeneralUtils.getRoundValue((finalTicketPrice * depositAmount / 100)))  ;
        } else if (DepositType.FLAT.equals(ticketingType.getDepositType())) {
            return Math.min(finalTicketPrice ,depositAmount); // Ensure deposit amount does not exceed ticket price
        }
        return finalTicketPrice;
    }

    @Override
    public double calculateNetDepositAmount(TicketingType ticketingType, double finalTicketPrice) {
        if (!ticketingType.isRequireDepositAmount() || ticketingType.getDepositAmount() <= 0 ) {
            return 0d;
        }

        double depositAmount = ticketingType.getDepositAmount();
        if (DepositType.PERCENTAGE.equals(ticketingType.getDepositType())) {
            return  Math.min(finalTicketPrice, GeneralUtils.getRoundValue((finalTicketPrice * depositAmount / 100)))  ;
        } else if (DepositType.FLAT.equals(ticketingType.getDepositType())) {
            return Math.min(depositAmount, finalTicketPrice); // Ensure deposit amount does not exceed ticket price
        }
        return 0d;
    }

    private void setTicketPriceDetailsForComplementaryTicket(int totalPurchasedTicket, List<TicketPriceDetails> ticketPriceDetailsList) {
		for(int i = 0; i < totalPurchasedTicket;i++){
            ticketPriceDetailsList.add(new TicketPriceDetails());
		}
	}

    private double getVatTaxAmount(double vatTaxRate, double ticketPrice) {
        double vatTaxToShow = 0.0;
        if(NumberUtils.isNumberGreaterThanZero(vatTaxRate)) {
            vatTaxToShow = getVatTaxPassed(ticketPrice, vatTaxRate);
        }
        return vatTaxToShow;
    }

    private double addVatTaxInTicketPrice(double vatTaxRate, double ticketPrice) {
        return ticketPrice + getVatTaxAmount(vatTaxRate, ticketPrice);
    }

    public double getVatTaxPassed(double amount, double vatTaxRate){
        return amount * (vatTaxRate/100);
    }

	private void setTicketPriceDetailsFor100PercentDiscount(int totalPurchasedTicket, List<TicketPriceDetails> ticketPriceDetailsList, double finalDiscount) {
		for(int i = 0; i < totalPurchasedTicket;i++){
			TicketPriceDetails ticketPriceDetails = new TicketPriceDetails();
			ticketPriceDetails.setDiscounted(true);
			ticketPriceDetails.setDiscountedAmount(finalDiscount);

			ticketPriceDetailsList.add(ticketPriceDetails);
		}
	}

	protected TicketPriceDetails getTicketPriceDetails(boolean isPayLaterPayment,int totalPurchasedTicket, TicketingType ticketingType, int numberOfTicketTypeExcludingFreeType, double ticketPrice, StripeDTO stripeDTO, SalesTaxFeeDto salesTaxFeeDto, double vatTaxRate, boolean depositPayment, double capAmount) {
       double findTicketPrice = ticketPrice;
        if(depositPayment){
            findTicketPrice = calculateDepositAmountForTicketType(ticketingType, ticketPrice);
        }

        if(Boolean.FALSE.equals(ticketingType.isPassFeeVatToBuyer())){
            vatTaxRate = 0;
        }
        FeePerTicket feePerTicket = new FeePerTicket(ticketingType,isPayLaterPayment, stripeDTO, salesTaxFeeDto, findTicketPrice, numberOfTicketTypeExcludingFreeType, totalPurchasedTicket, false,
               capAmount, vatTaxRate, FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();


		TicketPriceDetails ticketPriceDetails = new TicketPriceDetails(feePerTicket);
        ticketPriceDetails.setPrice(ticketPrice);
        if (depositPayment) {
            ticketPriceDetails.setDepositAmount(calculateNetDepositAmount(ticketingType, ticketPrice));
        }
		return ticketPriceDetails;
	}


	@Override
	public Ticketing findByEventId(Long eventId){
		return ticketingRepository.findByEventId(eventId);
	}

	@Override
	public List<Ticketing> getEndedTicketingByPayoutStatus(List<EnumEventPayoutStatus> eventPayoutStatuses, Date endDate){
		return ticketingRepository.getEndedTicketingByPayoutStatus(eventPayoutStatuses, endDate);
	}

	@Override
	public boolean isTicketingModuleActivated(Event event) {
        Boolean isTicketingActivated = ticketingRepository.isActivated(event);
        if(null != isTicketingActivated){
            return isTicketingActivated;
        }else{
            return Boolean.FALSE;
        }
	}

    @Override
    public String prepareNumber(String nationalFormat) {
        if ("0".equals(nationalFormat.substring(0,1))) {
            nationalFormat = nationalFormat.substring(1);
        }
        nationalFormat = nationalFormat.replaceAll("\\s", "");
        return  nationalFormat;
    }

    /**
     * If in ticket country code is present then return ticket country code
     * Else return user browser country code
     * @param ticketCountryCode Ticket form country code
     * @param clientCountryCode User browser country code
     * @return Returns CountryCode enum value if not valid country code found then return null
     */
    @Override
    public CountryCode getCountryCode(String ticketCountryCode, String clientCountryCode) {
        String holderCountryCode = StringUtils.isNotBlank(ticketCountryCode) ? ticketCountryCode : clientCountryCode;
        if (StringUtils.isNotBlank(holderCountryCode) && EnumUtils.isValidEnum(CountryCode.class, StringUtils.upperCase(holderCountryCode))) {
            return CountryCode.valueOf(StringUtils.upperCase(holderCountryCode));
        }
        return null;
    }

    @Override
    public boolean isAttendeeRegistrationApprovalEnabledByEventId(Long eventId) {
        return ticketingRepository.isAttendeeRegistrationApprovalEnabledByEventId(eventId);
    }

    @Override
    public List<Event> getListOfEventByEventIdsAndChartKeyNotNull(long from, long to) {
        return ticketingRepository.getListOfEventByEventIdsAndChartKeyNotNull(from, to);
    }

    @Override
    public List<Ticketing> findAllByEventIdBetween(Long from, Long to) {
        return ticketingRepository.findAllByEventIdBetween(from,to);
    }
    @Override
    public List<Ticketing> findAllByIsRecurringEventAndEventIdBetween(Long from, Long to) {
        return ticketingRepository.findAllByIsRecurringEventAndEventIdBetween(from,to);
    }

    @Override
    public Ticketing findTicketingByEventIdOrThrowError(Event event) {
        Ticketing ticketing = ticketingRepository.findByEventid(event);
        if (ticketing == null) {
            throw  new NotFoundException(NotFoundException.NotFound.TICKETING_SETTING_NOT_FOUND);
        }
        return ticketing;
    }
    public boolean isCollectTicketHolderAttributesByEvent(Event event){
        return  ticketingRepository.isCollectTicketHolderAttributesByEvent(event);
    }
    @Override
    public boolean isAllowCheckInWithUnpaidTicketEnabledByEventId(Long eventId) {
        return ticketingRepository.isAllowCheckInWithUnpaidTicketEnabledByEventId(eventId);
    }

    @Override
    public int getTotalNoOfTicketTypeExcludingFreeType(List<TicketingOrderManager> ticketingOrderManagers){
        return (int) ticketingOrderManagers.stream()
                .filter(e -> !e.getTicketType().getTicketType().equals(TicketType.FREE))
                .count();
    }

    @Override
    public Map<Long, TicketingModuleDTO> findAllTicketingRequiredFieldsByEvent(List<Event> events) {
        Map<Long, TicketingModuleDTO> map = new HashMap<>();
        List<TicketingModuleDTO> listOfData = roTicketingHelperService.findAllTicketingRequiredFieldsByEvent(events);
        listOfData.forEach(ticketingModuleData -> map.put(ticketingModuleData.getEventId(), ticketingModuleData));
        return map;
    }

    @Override
    public boolean isEnableOrderConfirmationEmail(Long eventId){
        return ticketingRepository.isEnableOrderConfirmationEmail(eventId);
    }
}
