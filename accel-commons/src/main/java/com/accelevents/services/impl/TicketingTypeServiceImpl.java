package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.TicketTypeNameQtyDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.TicketingTypeIdAndCategoryDTO;
import com.accelevents.dto.tray.io.connector.TicketTypeDetails;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.TicketingTypeCommonRepo;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.ro.eventTicket.ROTicketingTypeTicketService;
import com.accelevents.services.*;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class TicketingTypeServiceImpl implements TicketingTypeService {

	private static final Logger log = LoggerFactory.getLogger(TicketingTypeServiceImpl.class);

	@Autowired
	private TicketingTypeRepository ticketingTypeRepository;

	@Autowired
	private TicketingTypeCommonRepo ticketingTypeCommonRepo;

	@Autowired
	private TimeZoneService timeZoneService;

	@Autowired
	TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

	@Autowired
	TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

	@Autowired
	private TicketingTypeTicketService ticketingTypeTicketService;

	@Autowired
	private RecurringEventsScheduleBRService recurringEventsScheduleService;
	@Autowired
	private ClearAPIGatewayCache clearAPIGatewayCache;
    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private ROTicketingTypeTicketService roTicketingTypeTicketService;

    @Override
    public TicketingType save(TicketingType ticketingType){
        TicketingType savedTicketType = this.ticketingTypeCommonRepo.save(ticketingType);

        if(null != ticketingType.getTicketing().getEventid()) {
            clearAPIGatewayCache.clearAPIGwTicketTypes(ticketingType.getTicketing().getEventid().getEventURL());
            clearAPIGatewayCache.clearAPIGwTicketTypesDescriptionAndFees(ticketingType.getTicketing().getEventid().getEventURL(), null);
            clearAPIGatewayCache.clearAPIGwTicketTypesRemainingCountCache(ticketingType.getTicketing().getEventid().getEventURL());
        }
        // Reason for clearing cache here from category API is that we need to update the category API with the new Add-ons and Ticket Types
        if (DataType.ADDON.equals(ticketingType.getDataType())) {
            clearAPIGatewayCache.clearAPIGwTicketingTypesCategoriesCache(ticketingType.getTicketing().getEventid().getEventURL());
        }
        return savedTicketType;
    }

    @Override
    public boolean isAllFreeTicket(List<Long> tickettypeids, Event event) {
        Long numberOfFreeTicket = ticketingTypeCommonRepo.numberOfPaidOrFreeTicketPresent(tickettypeids, event, TicketType.FREE);
        return tickettypeids.size() == numberOfFreeTicket;
    }

	@Override
	public TicketingType setPositionForTicketingTypeAndsaveTicketingType(TicketingType ticketingType) {
        synchronized (this) {
            ticketingTypeTicketService.setPositionForTicketingType(ticketingType);
            return this.save(ticketingType);
        }
	}

	@Override
	public List<TicketingType> findByTicketing_EventidAndCreatedFromNotNullAndDataType(Event event, DataType dataType) {
		return ticketingTypeCommonRepo.findByTicketing_EventidAndCreatedFromNotNullAndDataType(event, dataType);
	}

	@Override
	@Transactional(rollbackFor = { Exception.class })
	public void updatewithsequence(TicketingType ticketingType,
								   Long topId,
								   Long topBottomId,
								   Event event,User user) {
		
		TicketingType topTicketingType = ticketingTypeCommonRepo.findByid(topId);
		TicketingType topNextTicketingType = ticketingTypeCommonRepo.findByid(topBottomId);

        log.info("request received for change Ticket Type position|updatewithsequence|event {}|Ticket Type {}| current Position {}| user {}",event.getEventId(),ticketingType.getId(),ticketingType.getPosition(),user.getUserId());
		//move to middle
		double sequence = 1000;
		if (topTicketingType != null && topNextTicketingType != null) {
			double position = (topNextTicketingType.getPosition() + topTicketingType.getPosition()) / 2;
			if (1 == position) {
				TicketingType ticketTypeNextTicket = getNextPositionItem(ticketingType,event);
				TicketingType ticketTypePrevTicket = getPreviousPositionItem(ticketingType,event);
				double positiondifferent = (ticketTypeNextTicket.getPosition() - ticketTypePrevTicket.getPosition())
						/ 2;
				updatePositionInGroup(event,topTicketingType.getPosition(), ticketingType.getPosition(), positiondifferent);
				ticketingType.setPosition(topNextTicketingType.getPosition());
				this.save(ticketingType);
			} else {
				ticketingType.setPosition(position);
				this.save(ticketingType);
			}
		//move to top
		} else if (topTicketingType == null && topNextTicketingType != null) {
			ticketingType.setPosition(topNextTicketingType.getPosition() + sequence);
			this.save(ticketingType);
		//move at last
		} else if (topTicketingType != null && topNextTicketingType == null) {//NOSONAR
			double posDiff = topTicketingType.getPosition() - sequence;
			if (posDiff <= 1) {
				ticketingTypeRepository.updatePositionForAllTicketTypes(sequence, event.getEventId());
			}
			ticketingType.setPosition(sequence);
			this.save(ticketingType);
		}
        log.info("successfully changed ticket type position|updatewithsequence|event {}|Ticket Type {}| updated Position {}| user {}",event.getEventId(),ticketingType.getId(),ticketingType.getPosition(),user.getUserId());

	}

	private void updatePositionInGroup(Event event,double startposition, double endposition, double positiondifferent) {
		ticketingTypeRepository.updatePositionTicketType(event.getEventId(),startposition,endposition,positiondifferent);
	}


	protected TicketingType getPreviousPositionItem(TicketingType ticketingType,Event event) {
		List<TicketingType> previousPositionTicketTypeList = ticketingTypeCommonRepo.previousPositionTicketType(ticketingType.getId(),event,ticketingType.getPosition());
		return  previousPositionTicketTypeList.isEmpty() ? null: previousPositionTicketTypeList.get(0);
	}


	protected TicketingType getNextPositionItem(TicketingType ticketingType,Event event) {
		List<TicketingType> nextPositionItemList = ticketingTypeCommonRepo.nextPositionTicketType(ticketingType.getId(),event,ticketingType.getPosition());
		return nextPositionItemList.isEmpty() ? null: nextPositionItemList.get(0);
	}

	@Override
	public void delete(TicketingType ticketingType) {
		ticketingTypeCommonRepo.delete(ticketingType);
	}

	@Override
	public List<TicketingType> getTicketingTypes(Event event,
												 Boolean fetchHiddenTicketsOnly,
												 Long recurringEventId,
												 Ticketing ticketing,
												 DataType dataType) {
		List<TicketingType> ticketingTypes;
		if(ticketing.isRecurringEvent() && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
			ticketingTypes = ticketingTypeTicketService.findAllByRecurringEvents(recurringEventId, fetchHiddenTicketsOnly, dataType);
		}else{
			ticketingTypes = findAllByEventIdHost(event, fetchHiddenTicketsOnly, dataType);
		}
		return ticketingTypes;
	}

    @Override
    public List<TicketingType> getTicketingTypesOrAddOnTypes(Event event,
                                                 Boolean fetchHiddenTicketsOnly,
                                                 RecurringEvents recurringEvents,
                                                 Ticketing ticketing,
                                                 List<DataType> dataTypes) {
        List<TicketingType> ticketingTypes;
        if(recurringEvents != null){
            ticketingTypes = ticketingTypeTicketService.findAllTicketTypeOrAddOnByRecurringEvents(Collections.singletonList(recurringEvents.getId()), fetchHiddenTicketsOnly, dataTypes);
        }else{
            ticketingTypes = findAllTicketOrAddonByEventIdHost(event, fetchHiddenTicketsOnly, dataTypes);
        }
        return ticketingTypes;
    }

	@Override
	public List<TicketingType> getTicketingTypesDisplay(Event event,
														Boolean excludeHidden,
														Long recurringEventId,
														Ticketing ticketing) {
		List<TicketingType> ticketingTypes;
		if(ticketing.isRecurringEvent() && NumberUtils.isNumberGreaterThanZero(recurringEventId)){
			ticketingTypes = ticketingTypeTicketService.findAllByRecurringEventsDisplay(recurringEventId, excludeHidden, null);
		}else{
			ticketingTypes = findAllByEventId(event, excludeHidden );
		}
		return ticketingTypes;
	}

	@Override
	public void deleteAll(List<TicketingType> ticketingTypeList) {

		ticketingTypeCommonRepo.deleteAll(ticketingTypeList);

	}

	@Override
	public List<TicketingType> getAllByTicketingAndCreatedFromNullOrderByPosition(Event event) {
		return ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET));
	}

    @Override
    public List<Long> findIdAllByEventIdRecurringIdNull(Event event, List<DataType> dataTypes){
        return ticketingTypeCommonRepo.findIdAllByEventIdRecurringIdNull(event,dataTypes);
    }

	@Override
	public List<TicketingType> getAllByEventAndCreatedFromIsNotNull(Event event) {
		return findByTicketing_EventidAndCreatedFromNotNullAndDataType(event, DataType.TICKET);
	}

	@Override
	public void deleteByRecurringEventId(Long recuringEventId) {
		ticketingTypeCommonRepo.deleteByRecurringEventId(recuringEventId);
	}

	@Override
	public void deleteByRecurringEventIdIn(List<Long> recuringEventIds) {
		ticketingTypeCommonRepo.deleteByRecurringEventIdIn(recuringEventIds);
	}

    @Override
    public List<TicketingType> findByRecurringEventId(Long recuringEventId) {
        return ticketingTypeRepository.findByRecurringEventId(recuringEventId);
    }

    @Override
    public TicketingType findByid(Long id) {
        return ticketingTypeRepository.findTicketingTypeById(id);
    }

    @Override
    public void updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(Ticketing ticketing) {
        ticketingTypeRepository.updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(ticketing.getId());
    }

	@Override
	public List<TicketingType> getAllByEventIdAndRecurringId(Event event, Long recurringEventsId, DataType dataType) {
		return ticketingTypeCommonRepo.findAllByEventIdAndRecurringId(event, recurringEventsId, dataType);
	}

	@Override
	public Set<Long> getMainEventTicketTypeIds(List<Long> ticketingTypeIds) {
		return ticketingTypeCommonRepo.findBaseEventTicketTypeIds(ticketingTypeIds);
	}

	@Override
	public List<TicketingType> getTicketTypeByCreateFrom(Long typeId) {
		return ticketingTypeCommonRepo.findByCreatedFrom(typeId);
	}

	@Override
	public List<TicketingType> findAllByTicketing(Long recurringEventId, Event event) {
        return findAllByTicketingAndDataTypes(recurringEventId, event, Collections.singletonList(DataType.TICKET));
    }

    @Override
    public List<TicketingType> findAllByTicketingAndDataTypes(Long recurringEventId, Event event, List<DataType> dataTypes) {
        if (NumberUtils.isNumberGreaterThanZero(recurringEventId)) {
            return ticketingTypeTicketService.findAllByRecurringEvents(recurringEventId, false, null);
        } else {
            return ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, dataTypes);
        }
    }

	@Override
	public TicketingType findByidAndEvent(long tickettypeid, Event event) {
		return ticketingTypeCommonRepo.findByIdAndEvent(tickettypeid, event);
	}

	@Override
	public Optional<TicketingType> findById(long ticketingTypeId){
		return ticketingTypeCommonRepo.findById(ticketingTypeId);
	}

	@Override
	public List<TicketingType> findByidInAndEvent(List<Long> tickettypeids, Event event) {
		if(tickettypeids.isEmpty()){
			return Collections.emptyList();
		}
		return ticketingTypeCommonRepo.findByIdInAndEvent(tickettypeids, event);
	}

	@Override
	public List<TicketingType> findByidInAndEventForWaitList(List<Long> tickettypeids, Event event) {
		if(CollectionUtils.isEmpty(tickettypeids)){
			tickettypeids = Collections.singletonList(0L);
		}
		return ticketingTypeCommonRepo.findAllOrByIdInAndEvent(tickettypeids, event);
	}

	@Override
	public List<TicketingType> findAllByEventId(Event event, Boolean excludeHidden) {
		List<DataType> dataTypes = Arrays.asList(DataType.TICKET, DataType.ADDON);
		if (Boolean.TRUE.equals(excludeHidden)) {
			return ticketingTypeCommonRepo.findAllByEventIdAndRecurringIdIsNullAndHidden(event, false, dataTypes);
		} else {
			return ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, dataTypes);
		}
	}

	@Override
	public List<TicketingType> findAllByTicketTypeIdsAndCreatedFrom(List<Long> ticketTypeIds) {
		if (CollectionUtils.isEmpty(ticketTypeIds)) {
			return Collections.emptyList();
		}
		return ticketingTypeCommonRepo.findAllByTicketTypeIds(ticketTypeIds);
	}

	@Override
	public List<TicketingType> findAllByEventIdHost(Event event, boolean fetchHiddenOnly, DataType dataType) {
		if (fetchHiddenOnly) {
			return ticketingTypeCommonRepo.findAllByEventIdAndRecurringIdIsNullAndHidden(event, true, Collections.singletonList(dataType));
		} else {
			return ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(dataType));
		}
	}

    @Override
    public List<TicketingType> findAllByEventIdAndDataType(Event event, List<DataType> dataType) {
        return ticketingTypeCommonRepo.findAllByEventIdAndDataType(event, dataType);
    }

    public List<TicketingType> findAllTicketOrAddonByEventIdHost(Event event, boolean fetchHiddenOnly, List<DataType> dataTypes) {
        if (fetchHiddenOnly) {
            return ticketingTypeCommonRepo.findAllByEventIdAndRecurringIdIsNullAndHidden(event, true, dataTypes);
        } else {
            return ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, dataTypes);
        }
    }

	@Override
	public List<Long> findAllIdByEventId(Event event) {
		return ticketingTypeCommonRepo.findAllIdByEventId(event, null);
	}

	@Override
	public List<Long> findAllTypeIdByEvent(Event event, DataType dataType) {
		return ticketingTypeCommonRepo.findAllIdByEventId(event, dataType);
	}

    @Override
    public List<Long> findAllIdByEventAndDataTypeAndExcludeFormatAndDonation(Event event, DataType dataType) {
        return ticketingTypeCommonRepo.findAllIdByEventAndDataTypeAndExcludeFormatAndDonation(event, dataType);
    }

    @Override
	public void saveAll(List<TicketingType> newTicketTypes){
		if(newTicketTypes != null && !newTicketTypes.isEmpty()){
			ticketingTypeCommonRepo.saveAll(newTicketTypes);

            if(null != newTicketTypes.get(0).getEvent()) {
                clearAPIGatewayCache.clearAPIGwTicketTypes(newTicketTypes.get(0).getEvent().getEventURL());
                clearAPIGatewayCache.clearAPIGwTicketTypesDescriptionAndFees(newTicketTypes.get(0).getEvent().getEventURL(), null);
                clearAPIGatewayCache.clearAPIGwTicketingTypesCategoriesCache(newTicketTypes.get(0).getEvent().getEventURL());
            }
		}
	}

	@Override
	public List<TicketingType> getByTicketingTypeIds(List<Long> availableTicketTypeIds) {
		return ticketingTypeCommonRepo.findByTicketingTypeIds(availableTicketTypeIds);
	}

    @Override
    public boolean userHadPurchasedTicket(List<Long> tickettypeids, Event event) {
        return ticketingTypeCommonRepo.userHadPurchasedTicket(tickettypeids, event);
    }


	@Override
	public List<Long> getListOfTicketingTypeIds(Long eventId) {
		return ticketingTypeRepository.findListOfTicketingTypeIds(eventId);
	}

    @Override
    public void updateTicketingTypeRecordStatusToCancel(List<Long> ids) {
	    if(!CollectionUtils.isEmpty(ids))
            ticketingTypeCommonRepo.updateRecordStatusToCancel(ids);
    }

    @Override
    public boolean isAllPaidTicket(List<Long> tickettypeids, Event event) {
	    Long numberOfPaidTicket = ticketingTypeCommonRepo.numberOfPaidOrFreeTicketPresent(tickettypeids, event, TicketType.PAID);
        return tickettypeids.size() == numberOfPaidTicket;
    }

	@Override
	public long getTicketTypeCountByEventId(Event event) {
		return ticketingTypeCommonRepo.countByTicketingEventid(event);
	}

    @Override
    public long getTicketTypeCountByEventId(Long eventId) {
        return ticketingTypeCommonRepo.countByTicketingEventid(eventId);
    }

    @Override
    public List<Long> findAllByEventIdNumberOfTickets(Event event) {
        return ticketingTypeCommonRepo.findAllByEventIdNumberOfTickets(event, null);
    }

    @Override
    public List<TicketingType> getAllTicketingTypeByEvent(Event event) {
        return ticketingTypeCommonRepo.getAllTicketingTypeByEvent(event.getEventId());
    }

    @Override
    public List<TicketingType> getAllFreeTicketingTypeByEvent(Event event) {
        return ticketingTypeCommonRepo.findByTicketing_EventidAndDataTypeAndTicketType(event,DataType.TICKET,TicketType.FREE);
    }

    /**
     * this method is used to set ticket types date to post days
     * @param listOfEventId
     * @param days
     */
    @Override
    public void resetTicketTypesDateByDays(List<Long> listOfEventId, int days) {
	    List<TicketingType>  updatedTicketTypes = new ArrayList<>();
        listOfEventId.forEach(eventId->{
            List<TicketingType> allTicketingTypeByEvent = ticketingTypeCommonRepo.getAllTicketingTypeByEvent(eventId);
            allTicketingTypeByEvent.forEach(eventTicketType ->{
                eventTicketType.setStartDate(DateUtils.addDaysInDate(eventTicketType.getStartDate(),days));
                eventTicketType.setEndDate(DateUtils.addDaysInDate(eventTicketType.getEndDate(),days));
                updatedTicketTypes.add(eventTicketType);
            });
        });
        if (!updatedTicketTypes.isEmpty()) {
            this.saveAll(updatedTicketTypes);
        }

    }

    @Override
    public List<TicketTypeDetails> getTicketTypeDetails(Event event) {
        return ticketingTypeRepository.getTicketTypeDetails(event);
    }

    @Override
    public List<TicketTypeDetails> getRegistrationRequestEnabledTicketTypeDetails(Event event) {
        return ticketingTypeRepository.getRegistrationRequestEnabledTicketTypeDetails(event);
    }

    @Override
    public List<TicketingType> getAllTicketingAndAddonByTicketingAndCreatedFromNullOrderByPosition(Event event) {
        return ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Arrays.asList(DataType.TICKET,DataType.ADDON));
    }

    @Override
    public List<TicketingType> findAllTicketTypeByTicketing(Ticketing ticketing) {
        return ticketingTypeCommonRepo.findAllTicketTypeByTicketing(ticketing);
    }

    @Override
    public List<TicketingType> findAllAddonByTicketingId(Ticketing ticketing){
        return ticketingTypeCommonRepo.findAllAddonByTicketingId(ticketing,DataType.ADDON);
    }

    @Override
    public List<TicketingType> findAllTicketingTypeByCategoryId(Long categoryId) {
        return ticketingTypeCommonRepo.findAllTicketingTypeByCategoryId(categoryId);
    }

    @Override
    public List<Object[]> findTicketPriceRangeExcludingGivenTicketTypes(Long eventId, List<String> ticketTypes){
        return ticketingTypeRepository.findTicketPriceRange(eventId, ticketTypes);
    }

    @Override
    public List<TicketTypeDetails> getAllTicketTypeDetailsByEvent(Event event) {
        return ticketingTypeRepository.getAllTicketTypeDetailsByEvent(event);
    }

    @Override
    public List<Long> getAllTicketTypeIdsByRecurringEventIdAndDataType(Long recurringEventId) {
        return ticketingTypeRepository.getAllTicketTypeIdsByRecurringEventIdAndDataType(recurringEventId,DataType.TICKET);
    }

    @Override
    public List<Long> getAllTicketTypeIdsByRecurringEventIdAndDataTypeAndCreatedFrom(Long recurringEventId, String commaSeparateTicketTypes) {
        return ticketingTypeRepository.getAllTicketTypeIdsByRecurringEventIdAndDataTypeAndCreatedFrom(recurringEventId,
                DataType.TICKET, GeneralUtils.convertCommaSeparatedToListLong(commaSeparateTicketTypes));

    }

    @Override
    public List<TicketingType> findAllByTicketTypeIds(List<Long> ticketTypeIds) {
        if(ticketTypeIds.isEmpty())
        {
            return  Collections.emptyList();
        }
         return ticketingTypeRepository.findAllByTicketTypeIds(ticketTypeIds);
    }

    @Override
    public List<TicketingType> findAllTicketingTypeByOrganiser(Organizer organiser) {
        return ticketingTypeRepository.findAllTicketingTypeByOrganiser(organiser);
    }

    @Override
    public TicketingType findTicketTypeWithSameCreatedFromAndInRecurringEventId(Long createdFrom, Long recurringEventId) {
        return ticketingTypeRepository.findTicketTypeWithSameCreatedFromAndInRecurringEventId(createdFrom, recurringEventId);
    }

    @Override
    public List<Object[]> findTicketPriceRangeExcludingGivenTicketTypesAndHiddenTypes(Long eventId, List<TicketType> ticketTypes){
        return ticketingTypeRepository.findTicketPriceRangeAndExcludeHiddenTypes(eventId, ticketTypes);
    }

    @Override
    public List<TicketingTypeIdAndCategoryDTO> findTicketingTypeIdAndCategoryIdByEventAndDataType(Event event) {
        return ticketingTypeCommonRepo.findTicketingTypeIdAndCategoryIdByEventAndDataType(event);
    }

    @Override
    public List<TicketingType> findAllByEventIdAndDataType(Long eventId, List<DataType> dataTypes) {
        return ticketingTypeCommonRepo.findAllByEventIdAndDataType(eventId, dataTypes);
    }

    @Override
    public List<Long> findTicketTypeIdByEventIdAndTicketTypeNames(Long eventId, List<String> ticketTypeNames) {
        return ticketingTypeRepository.findTicketTypeIdByEventIdAndTicketTypeNames(eventId, ticketTypeNames);
    }

    @Override
    public List<TicketingType> findAllByEventIdAndTicketTypeIdsAndDataTypes(Long eventId, List<Long> ticketTypeIds, List<DataType> dataTypes) {
        return ticketingTypeRepository.findAllByEventIdAndTicketTypeIdsAndDataTypes(eventId, ticketTypeIds, dataTypes);
    }

    @Override
    public List<TicketTypeNameQtyDto> findFreeTicketTypeIdAndNumberOfTickets(Event event) {
        return ticketingTypeRepository.findFreeTicketTypeIdAndNumberOfTickets(event);
    }

    @Override
    public List<String> findNameByTicketTypeIds(List<Long> ticketTypeIds) {
        if(ticketTypeIds.isEmpty())
        {
            return  Collections.emptyList();
        }
        return ticketingTypeRepository.findByIdIn(ticketTypeIds);
    }

}
