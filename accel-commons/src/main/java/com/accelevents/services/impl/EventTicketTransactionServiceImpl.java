package com.accelevents.services.impl;

import com.accelevents.domain.EventTicketTransaction;
import com.accelevents.domain.EventTicketTransaction.TicketTransactionChargeStatus;
import com.accelevents.domain.EventTickets;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.AttendeePartialPaymentDto;
import com.accelevents.dto.EventTicketFeesDto;
import com.accelevents.dto.PartialPaymentDueDTO;
import com.accelevents.dto.TicketTransferDueAmountDetailsDTO;
import com.accelevents.repositories.EventTicketTransactionRepository;
import com.accelevents.services.EventTicketTransactionService;
import com.accelevents.services.EventTicketTransferDetailService;
import com.accelevents.services.VatTaxService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.ChargeDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EventTicketTransactionServiceImpl implements EventTicketTransactionService {
    private static final Logger log = LoggerFactory.getLogger(EventTicketTransactionServiceImpl.class);
    @Autowired
    private EventTicketTransactionRepository eventTicketTransactionRepository;

    @Autowired
    private EventTicketTransferDetailService eventTicketTransferDetailService;

    @Autowired
    private VatTaxService vatTaxService;

    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;

    private static final List<EventTicketTransaction.PaymentType> CASH_PAYMENT_TYPES = Arrays.asList(EventTicketTransaction.PaymentType.CASH, EventTicketTransaction.PaymentType.CASH_CARD, EventTicketTransaction.PaymentType.CASH_WIRE_TRANSFER);
    private static final List<EventTicketTransaction.PaymentType> CARD_PAYMENT_TYPES = List.of(EventTicketTransaction.PaymentType.CARD);

    @Override
    public void storeEventTicketTransaction(Long orderId, List<EventTickets> eventTickets, ChargeDto chargeDto, List<EventTicketFeesDto> eventTicketFees, EventTicketTransaction.PaymentType paymentType, AttendeePartialPaymentDto ticketExchangePaymentDto, Long staffUserId) {
        log.info("storeEventTicketTransaction() - orderId: {}", orderId);
        List<EventTicketTransaction> eventTicketTransactionList = new ArrayList<>();
        eventTickets.stream().forEach(eventTicket -> {
            EventTicketTransaction eventTicketTransaction = new EventTicketTransaction();
            eventTicketTransaction.setEventTicketId(eventTicket.getId());
            if(null != chargeDto) {
                eventTicketTransaction.setChargeId(chargeDto.getId());
                if(StringUtils.isNotBlank(chargeDto.getStatus()) && chargeDto.getStatus().equalsIgnoreCase("UNPAID")) {
                    eventTicketTransaction.setRecordStatus(RecordStatus.CANCEL);
                } else {
                    eventTicketTransaction.setRecordStatus(RecordStatus.CREATE);
                }
            }
            if(ticketExchangePaymentDto != null) {
                eventTicketTransaction.setTransactionId(ticketExchangePaymentDto.getTransactionId());
                eventTicketTransaction.setTransactionDate(ticketExchangePaymentDto.getPaymentDate() != null ? DateUtils.getFormattedDate(ticketExchangePaymentDto.getPaymentDate(), Constants.YYYY_MM_DD) : null);
                eventTicketTransaction.setDocumentLink(ticketExchangePaymentDto.getDocumentLink());
                eventTicketTransaction.setNote(ticketExchangePaymentDto.getNote());
            }
            eventTicketTransaction.setTicketingOrderId(orderId);
            eventTicketTransaction.setUpdatedAt(new Date());
            eventTicketTransaction.setCreatedAt(new Date());
            eventTicketTransaction.setChargeStatus(TicketTransactionChargeStatus.PAID);
            eventTicketTransaction.setPaymentType(paymentType);
            eventTicketTransaction.setCreatedBy(staffUserId);
            eventTicketFees.stream().filter(eventTicketFeesDto -> eventTicketFeesDto.getEventTicketId().equals(eventTicket.getId())).forEach(eventTicketFeesDto -> {
                log.info("storeEventTicketTransaction chargeId:{}, PaidAmount:{}",eventTicketTransaction.getChargeId(),eventTicketFeesDto.getEventTicketPaidAmount());
                eventTicketTransaction.setAeFeeAmount(eventTicketFeesDto.getAeFeeAmount());
                eventTicketTransaction.setWlAFeeAmount(eventTicketFeesDto.getWlAFeeAmount());
                eventTicketTransaction.setWlBFeeAmount(eventTicketFeesDto.getWlBFeeAmount());
                eventTicketTransaction.setPaidAmount(GeneralUtils.getRoundValue(eventTicketFeesDto.getEventTicketPaidAmount()));
                eventTicketTransaction.setCcFeeAmount(eventTicketFeesDto.getCcFeeAmount());
            });
            eventTicketTransaction.setNetSell(GeneralUtils.getRoundValue(eventTicketTransaction.getPaidAmount() - eventTicketTransaction.getAeFeeAmount() - eventTicketTransaction.getWlAFeeAmount() - eventTicketTransaction.getWlBFeeAmount() - eventTicketTransaction.getCcFeeAmount()));
            eventTicketTransactionList.add(eventTicketTransaction);

        });
        log.info("eventTicketTransactionList:{}",eventTicketTransactionList);
        eventTicketTransactionRepository.saveAll(eventTicketTransactionList);
    }


    @Override
    public List<EventTicketTransaction> getByTicketingOrderIdAndChargeStatus(List<Long> ticketingOrderId, TicketTransactionChargeStatus chargeStatus) {
        return eventTicketTransactionRepository.getWithEventTicketByTicketingOrderIdAndChargeStatus(ticketingOrderId, chargeStatus);
    }

    @Override
    public List<EventTicketTransaction> getAllByEventTicketIds(List<Long> eventTicketIds) {
        return eventTicketTransactionRepository.getAllByEventTicketIdsAndChargeStatusAndPaymentTypeIn(eventTicketIds, TicketTransactionChargeStatus.PAID, CARD_PAYMENT_TYPES);
    }

    @Override
    public List<EventTicketTransaction> getAllByEventTicketIdAndPaymentMethod(Long eventTicketId, boolean isCashPayment) {
        return eventTicketTransactionRepository.getAllByEventTicketIdsAndChargeStatusAndPaymentTypeIn(Collections.singletonList(eventTicketId), TicketTransactionChargeStatus.PAID, isCashPayment ? CASH_PAYMENT_TYPES : CARD_PAYMENT_TYPES );
    }
    @Override
    public List<EventTicketTransaction> getAllTransactionsByEventTicketIdsAndChargeStatus(Long eventTicketId) {
        return eventTicketTransactionRepository.getAllByEventTicketIdsAndChargeStatus(Collections.singletonList(eventTicketId), TicketTransactionChargeStatus.PAID);
    }

    @Override
    public void save(EventTicketTransaction eventTicketTransaction) {
        eventTicketTransactionRepository.save(eventTicketTransaction);
    }

    @Override
    public void saveAll(List<EventTicketTransaction> eventTicketTransactions) {
        eventTicketTransactionRepository.saveAll(eventTicketTransactions);
    }

    @Override
    public double getProcessingFeesByEventTicketIds(List<Long> eventTicketIds) {
        Double processingFeesByEventTicketIds = eventTicketTransactionRepository.getProcessingFeesByEventTicketIds(eventTicketIds, TicketTransactionChargeStatus.PAID);
        if(processingFeesByEventTicketIds == null){
            return 0.0;
        }
        return processingFeesByEventTicketIds.doubleValue();
    }

    @Override
    public Map<Long, Double>  getOrdersNetPaidAmount(List<Long> ticketingOrderIds) {
        List<EventTickets> eventTickets = eventTicketsRepoService.getAllEventTicketsWithTicketingTableByTicketingOrderIds(ticketingOrderIds);
        Map<Long, List<EventTickets>> eventTicketOrderWise = eventTickets.stream().collect(Collectors.groupingBy(et -> et.getTicketingOrderId()));
        List<EventTicketTransaction> eventTicketTransactions = eventTicketTransactionRepository.getByTicketingOrderIdAndChargeStatus(ticketingOrderIds, TicketTransactionChargeStatus.PAID);
        Map<Long, List<EventTicketTransaction>> ticketTransactions = eventTicketTransactions.stream().collect(Collectors.groupingBy(eventTicketTransaction -> eventTicketTransaction.getTicketingOrderId()));
        List <PartialPaymentDueDTO> partialPaymentDueDTOList = new ArrayList<>();
        for (Long orderId :ticketingOrderIds ){
            List<EventTickets> eventTicketsList = eventTicketOrderWise.get(orderId);
            List<EventTicketTransaction> eventTicketTransactionList = ticketTransactions.get(orderId);
            if(eventTicketsList != null && eventTicketTransactionList != null){
                for(EventTickets eventTicket : eventTicketsList){
                    double totalPaidAmount = eventTicketTransactionList.stream()
                            .filter(eventTicketTransaction -> eventTicketTransaction.getEventTicketId()==eventTicket.getId())
                            .mapToDouble(transaction ->GeneralUtils.getRoundValue(transaction.getPaidAmount()) - GeneralUtils.getRoundValue(transaction.getRefundedAmount()))
                            .sum();
                    double totalFees = eventTicketTransactionList.stream()
                            .filter(eventTicketTransaction -> eventTicketTransaction.getEventTicketId()==eventTicket.getId())
                            .mapToDouble(transaction ->
                                    GeneralUtils.getRoundValue(transaction.getAeFeeAmount()) +
                                            GeneralUtils.getRoundValue(transaction.getWlAFeeAmount()) +
                                            GeneralUtils.getRoundValue((transaction.getWlBFeeAmount()) +
                                                    GeneralUtils.getRoundValue(transaction.getCcFeeAmount())))
                            .sum();
                    TicketingType ticketingType = eventTicket.getTicketingTypeId();
                    double amount = 0;
                    if (ticketingType.isPassfeetobuyer()) {
                        amount = totalPaidAmount - totalFees ;
                    } else {
                        amount = totalPaidAmount;
                    }
                    PartialPaymentDueDTO partialPaymentDueDTO = new PartialPaymentDueDTO();
                    partialPaymentDueDTO.setOrderId(orderId);
                    partialPaymentDueDTO.setAmount(amount);
                    partialPaymentDueDTO.setEventTicketId(eventTicket.getId());
                    partialPaymentDueDTOList.add(partialPaymentDueDTO);
                }
            }
        }
        // create map of orderId and sum of paid amount using partialPaymentDueDTOList
        Map<Long, Double> orderIdAndSumOfPaidAmount = partialPaymentDueDTOList.stream()
                .collect(Collectors.groupingBy(PartialPaymentDueDTO::getOrderId, Collectors.summingDouble(PartialPaymentDueDTO::getAmount)));

        return orderIdAndSumOfPaidAmount;
    }

    @Override
    public double getPartialPaidAmount(EventTickets eventTickets) {
        List<EventTicketTransaction> eventTicketTransactions = getAllByEventTicketIds(Collections.singletonList(eventTickets.getId()));
        // Calculate the total paid amount
        double totalPaidAmount = eventTicketTransactions.stream()
                .mapToDouble(EventTicketTransaction::getPaidAmount)
                .sum();


        // Calculate the total fees
        double totalFees = eventTicketTransactions.stream()
                .mapToDouble(transaction ->transaction.getRefundedAmount() +
                        transaction.getAeFeeAmount() +
                        transaction.getWlAFeeAmount() +
                        transaction.getWlBFeeAmount() +
                        transaction.getCcFeeAmount())
                .sum();

        // Calculate the partial paid amount
        double partialPaidNetAmount = totalPaidAmount -  totalFees - eventTickets.getSalesTaxFee()- eventTickets.getVatTaxFee();

        return partialPaidNetAmount > 0 ? partialPaidNetAmount : 0;
    }

    @Override
    public List<EventTicketFeesDto> getTotalFeesByTicketingOrderIdsAndChargeStatus(List<Long> ticketingOrderIds) {
        return eventTicketTransactionRepository.getTotalFeesByTicketingOrderIdsAndChargeStatus(ticketingOrderIds, TicketTransactionChargeStatus.PAID);
    }

    @Override
    public double calculateDueAmountForTheTicket(EventTickets eventTickets) {
        return this.calculateDueAmountForGivenTicketPrice(eventTickets, eventTickets.getTicketingTypeId(), BigDecimal.valueOf(eventTickets.getTicketPrice()), eventTickets.getVatTaxFee(), BigDecimal.valueOf(eventTickets.getVatTaxPercentage())).getDueAmount().doubleValue();
    }

    /**
     * This method is used to Calculate the Due Amount and remaining Vat Tax on previous ticket type
     * NOTE: Due Amount is without the CC or VAT, We calculate the CC and VAT on this dueAmount In FeePerTicket Class and that is outside of this method
     * @param eventTickets eventTicket details
     * @param newTicketingType Details of New Ticket Type(Target ticket type)
     * @param newTicketPrice Price of ticket(target ticket type)
     * @param oldEventTicketVatTaxFee Paid Vat Tax in previous ticket type
     * @param oldVatTaxRate Vat Tax rate in previous ticket type
     * @return TicketTransferDueAmountDetailsDTO -> dueAmount and remainingVatTaxFee
     */
    @Override
    public TicketTransferDueAmountDetailsDTO calculateDueAmountForGivenTicketPrice(EventTickets eventTickets, @NotNull TicketingType newTicketingType, BigDecimal newTicketPrice, double oldEventTicketVatTaxFee, BigDecimal oldVatTaxRate) {

        BigDecimal vatTaxRate = BigDecimal.valueOf(vatTaxService.getVatTaxByTicketTypeOrEvent(eventTickets.getEventId(), newTicketingType));
        BigDecimal dueAmount;
        BigDecimal remainingVatTaxFee = new BigDecimal(0);
        List<EventTicketTransaction> eventTicketTransactions = getAllTransactionsByEventTicketIdsAndChargeStatus(eventTickets.getId());
        // Calculate the total paid amount
        BigDecimal totalPaidAmount = eventTicketTransactions.stream().map(e -> BigDecimal.valueOf(e.getPaidAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalPaidAmount.doubleValue() == 0) {
            return new TicketTransferDueAmountDetailsDTO(newTicketPrice, remainingVatTaxFee);
        }

        // When we exchange the ticket with absorb tax -> pass tax with same vat tax rate then we need to calculate vat tax based on current vatTax rate,
        // So If the old ticket have vat tax rate but vatTaxFee are 0 then we need to set remainingVatTaxRate to current ticket VatTax rate to calculate remainingVatTaxFee
        BigDecimal remainingVatTaxRate = oldEventTicketVatTaxFee > 0d ? vatTaxRate.subtract(oldVatTaxRate).abs() :
                oldVatTaxRate.doubleValue() > 0d ? vatTaxRate : BigDecimal.ZERO;

        // Calculate the total fees
        BigDecimal totalFees = eventTicketTransactions.stream()
                .map(transaction -> BigDecimal.valueOf(transaction.getRefundedAmount() +
                        transaction.getAeFeeAmount() +
                        transaction.getWlAFeeAmount() +
                        transaction.getWlBFeeAmount() +
                        transaction.getCcFeeAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (Boolean.TRUE.equals(newTicketingType.isPassfeetobuyer())) {
            BigDecimal partialPaidNetAmount = totalPaidAmount.subtract(totalFees).subtract(BigDecimal.valueOf(eventTickets.getSalesTaxFee())).subtract(BigDecimal.valueOf(oldEventTicketVatTaxFee));
            dueAmount = newTicketPrice.subtract(partialPaidNetAmount);
            remainingVatTaxFee = BigDecimal.valueOf(getVatTaxAmount(remainingVatTaxRate.doubleValue(), partialPaidNetAmount.doubleValue()));
        } else {
            if (Boolean.TRUE.equals(newTicketingType.isPassFeeVatToBuyer())) {
                BigDecimal newVatTaxAmount = BigDecimal.valueOf(getVatTaxAmount(vatTaxRate.doubleValue(), newTicketPrice.doubleValue()));

                // find the expectedPaidAmount = newTicketPriceWithPaidAmount - totalPaidAmount
                // 106.06 = 200 + 20 - 113.94 (Same Vate Tax Rate (10%))
                // 126.06 = 200 + 40 - 113.94 (Different Vate Tax Rate (10%) -> (20%))
                BigDecimal expectedPaidAmount = newTicketPrice.add(newVatTaxAmount).subtract(totalPaidAmount);

                // paidAmountWithoutVat 103.61 = 113.94 - 10.33
                BigDecimal paidAmountWithoutVat = totalPaidAmount.subtract(BigDecimal.valueOf(oldEventTicketVatTaxFee));

                // get the vat Tax amount of the pending in previous paid amount
                // 10.361 = 10% remainingVat and price 103.61
                remainingVatTaxFee = BigDecimal.valueOf(getVatTaxAmount(remainingVatTaxRate.doubleValue(), paidAmountWithoutVat.doubleValue()));

                if (remainingVatTaxFee.doubleValue() <= 0) {
                    // pass fee, passTax -> Absorb fee, passTax (with same Vat tax rate)
                    dueAmount = getRemainingAmountByExpectedPriceAndVatTaxRate(expectedPaidAmount, vatTaxRate);
                } else {
                    // passFee, passTax -> AbsorbFee, passTax (with different Vat tax rate)
                    BigDecimal expectedAmountWithoutRemainingVat = expectedPaidAmount.subtract(remainingVatTaxFee);
                    dueAmount = getRemainingAmountByExpectedPriceAndVatTaxRate(expectedAmountWithoutRemainingVat, vatTaxRate);
                }
            } else {
                dueAmount = newTicketPrice.subtract(totalPaidAmount);
            }
        }
        return new TicketTransferDueAmountDetailsDTO(dueAmount.setScale(2, RoundingMode.HALF_EVEN).max(BigDecimal.ZERO),
                remainingVatTaxFee
        );
    }

    private double getVatTaxAmount(double vatTaxRate, double ticketPrice) {
        double vatTaxToShow = 0.0;
        if(NumberUtils.isNumberGreaterThanZero(vatTaxRate)) {
            vatTaxToShow = getVatTaxPassed(ticketPrice, vatTaxRate);
        }
        return vatTaxToShow;
    }

    public double getVatTaxPassed(double amount, double vatTaxRate){
        return amount * (vatTaxRate/100);
    }

    private BigDecimal getRemainingAmountByExpectedPriceAndVatTaxRate(BigDecimal expectedAmountWithoutRemainingVat, BigDecimal vatTaxRate) {
        BigDecimal remainingAmount = expectedAmountWithoutRemainingVat;
        if(vatTaxRate.doubleValue() > 0) {
            /*Final price = Original price + VAT amount
            Final price = Original price + (Original price × VAT rate)
            Final price = Original price × (1 + VAT rate)
            So to find the original price:
            Original price = Final price ÷ (1 + VAT rate)
            */
             remainingAmount = expectedAmountWithoutRemainingVat.divide(getVatTaxRateInDecimal(vatTaxRate).add(BigDecimal.ONE), RoundingMode.HALF_EVEN);
        }
        return remainingAmount;
    }

    private BigDecimal getVatTaxRateInDecimal(BigDecimal vatTaxRate) {
        return vatTaxRate.divide(BigDecimal.valueOf(100), RoundingMode.HALF_EVEN);
    }


    @Override
    public List<EventTicketTransaction> findAllEventTransactionByOrderIdAndCashPayment(Long orderId) {
        return eventTicketTransactionRepository.findAllEventTransactionByOrderIdAndCashPayment(orderId);
    }

    @Override
    public boolean isCreditCardPaymentExist(Long eventTicketId) {
        return eventTicketTransactionRepository.isCreditCardPaymentExist(eventTicketId);
    }

    @Override
    public Double findCashPaymentSumByOrderId(Long orderId) {
        Double amount = eventTicketTransactionRepository.findCashPaymentSumByOrderId(orderId);
        return amount == null ? 0.0d : amount;
    }

    @Override
    public List<EventTicketTransaction> findAllEventTransactionByOrderIdsAndDocumentLinkIsNotNull(List<Long> orderIds) {
        return eventTicketTransactionRepository.findAllEventTransactionByOrderIdsAndDocumentLinkIsNotNull(orderIds);
    }

    @Override
    public void updateEventTicketsTransactionForPayFlow(String recStatus, String chargeId, long eventTicketId) {
        eventTicketTransactionRepository.updateEventTicketsTransactionForPayFlow(recStatus,chargeId ,eventTicketId);
    }

}
