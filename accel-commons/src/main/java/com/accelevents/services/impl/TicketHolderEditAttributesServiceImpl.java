package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.RegistrationConfigDto;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.HolderBasicDetailsDto;
import com.accelevents.common.dto.UserBasicDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.exhibitors.dto.TicketHolderAttributeDto;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.OrderAuditLogRepo;
import com.accelevents.repositories.require.attributes.TicketRequiresAttributesRepo;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneAttendeeDetailService;
import com.accelevents.services.neptune.NeptuneInterestService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.StaffRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.ticketing.dto.DisplayAttributeDto;
import com.accelevents.ticketing.dto.DisplayNestedQueDto;
import com.accelevents.ticketing.dto.UpdateNestedQueAnsDto;
import com.accelevents.utils.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.twilio.rest.lookups.v1.PhoneNumber;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.util.CollectionUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.dto.TicketHolderHelper.*;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;

@Service
public class TicketHolderEditAttributesServiceImpl implements TicketHolderEditAttributesService {


    private static final Logger log = LoggerFactory.getLogger(TicketHolderEditAttributesServiceImpl.class);

    @Lazy
    @Autowired
    private TrayIntegrationService trayIntegrationService;

    @Autowired
    private NeptuneAttendeeDetailService neptuneAttendeeDetailService;

	@Autowired
	private EventTicketsRepoService eventTicketsRepoService;

	@Autowired
	private EventCommonRepoService eventCommonRepoService;

	@Autowired
	private TicketingOrderService ticketingOrderService;

	@Autowired
	private TicketHolderAttributesService ticketHolderAttributesService;

    @Autowired
    private HolderAttributeCapacityService holderAttributeCapacityService;

    @Autowired
    private OrderAuditLogRepo orderAuditLogRepo;

	@Autowired
	private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
	@Autowired
	private EventTicketsRepository eventTicketsRepository;
	@Autowired
	private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private AttendeeProfileService attendeeProfileService;
    @Autowired
    private TwilioPhoneNumberValidateService twilioPhoneNumberValidateService;
    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private TicketHolderRequiredAttributesService ticketRequireAttributeService;
    @Autowired
    private VatTaxService vatTaxService;
    @Lazy
    @Autowired
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;

    @Autowired
    private NeptuneInterestService neptuneInterestService;

    @Autowired private UserSessionService userSessionService;

    @Autowired
    private BadgesService badgesService;
    @Autowired
    private StaffRepoService staffRepoService;
    @Autowired
    private  EventTicketsService eventTicketsService;
    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;

    @Autowired
    private TicketingEmailService ticketingEmailService;
    @Autowired
    private ROStaffService roStaffService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private TicketingDisplayService ticketingDisplayService;
    @Autowired
    private TicketRequiresAttributesRepo ticketRequiresAttributesRepo;

    private static final Pattern patternForNumber = Pattern.compile("-?\\d*\\.?\\d+");

    @Override
	public HolderDisplayAttributesDto getTicketHolderAttributes(long ticketId, Event event) throws JAXBException {
		EventTickets eventTicket = eventCommonRepoService.findById(ticketId);
		if(eventTicket != null){
			return getEditTicketHoldersAttributeDto(eventTicket, event);
		} else {
			return new HolderDisplayAttributesDto();
		}
	}

	public HolderDisplayAttributesDto getEditTicketHoldersAttributeDto(EventTickets eventTicket, Event event) throws JAXBException {
		Long recurringEventId = eventTicket.getRecurringEventId();
		List<TicketHolderRequiredAttributes> holderRequiredAttributes =  ticketHolderRequiredAttributesService
				.getHolderAttributesExcludingTypes(event, recurringEventId, eventTicket.getDataType(),Collections.singletonList(AttributeValueType.TEXT_BLOCK));
        holderRequiredAttributes.sort(Comparator.comparing(TicketHolderRequiredAttributes :: getHolderAttributeOrder));

		List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
		List<DisplayAttributeDto> holderQuestionDto = new ArrayList<>();
        List<DisplayNestedQueDto> displayNestedQueDtoList = new ArrayList<>();

		TicketAttributeValueDto1 ticketAttributeValueDto = getTicketAttributeValueDto(eventTicket);

        boolean isAddon = DataType.ADDON == eventTicket.getDataType();
        for (TicketHolderRequiredAttributes holderRequiredAttribute : holderRequiredAttributes) {
            if (isAddon && (holderRequiredAttribute.getEnabledForTicketPurchaser() && !holderRequiredAttribute.getDeletedForBuyer())) {
                if (StringUtils.isNotBlank(holderRequiredAttribute.getBuyerEventTicketTypeId()) &&
                        GeneralUtils.convertCommaSeparatedToListLong(holderRequiredAttribute.getBuyerEventTicketTypeId()).contains(eventTicket.getTicketingTypeOnlyId())) {
                    setAddOnData(eventTicket, holderAttributesDto, holderQuestionDto, displayNestedQueDtoList, ticketAttributeValueDto, holderRequiredAttribute);
                }
            } else if ((holderRequiredAttribute.getEnabledForTicketHolder() && !holderRequiredAttribute.getDeletedForHolder())) {
                setHolderData(eventTicket, holderAttributesDto, holderQuestionDto, displayNestedQueDtoList, ticketAttributeValueDto, false, holderRequiredAttribute);
            }
        }

		HolderDisplayAttributesDto attendee = new HolderDisplayAttributesDto();
        if(isAddon){
            attendee.setAttributes(holderAttributesDto);
        }else {
            attendee.setAttributes(filterAttributes(holderAttributesDto, eventTicket.getTicketingTypeId().getId()));
        }
		attendee.setQuestions(holderQuestionDto);
        attendee.setNestedQuestions(displayNestedQueDtoList);
		attendee.setSeatNumber(eventTicket.getSeatNumber());
		attendee.setEventTicketingId(eventTicket.getId());
        attendee.setBarcodeId(eventTicket.getBarcodeId());
		String eventKey = String.valueOf(event.getEventId());
		if(eventTicket.getRecurringEventId()!=null &&
				eventTicket.getRecurringEventId() > 0){
			eventKey = TicketingUtils.getEventKey(event.getEventId(), true, eventTicket.getRecurringEventId());
		}
		attendee.setEventKey(eventKey);

        if(!isAddon) {
            List<TicketingType> addonWithEventTicket = eventTicketsRepoService.getAddonTicketingTypeByEventIdAndTicketIdForAddon(event.getEventId(), eventTicket.getId());
            if (!CollectionUtils.isEmpty(addonWithEventTicket)) {
                List<NameIdDTO> addonTicketTypes = addonWithEventTicket.stream().sorted(Comparator.comparing(TicketingType::getPosition))
                        .map(e-> new NameIdDTO(e.getId(), e.getTicketTypeName())).collect(Collectors.toList());

                attendee.setAddonTicketTypes(addonTicketTypes);
            }
        }
		return attendee;
	}
    private void setHolderData(EventTickets eventTicket, List<DisplayAttributeDto> holderAttributesDto, List<DisplayAttributeDto> holderQuestionDto, List<DisplayNestedQueDto> displayNestedQueDtoList, TicketAttributeValueDto1 ticketAttributeValueDto, boolean isAddon, TicketHolderRequiredAttributes holderRequiredAttribute) {
        DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
        displayAttributeDto.setId(holderRequiredAttribute.getId());
        displayAttributeDto.setName(holderRequiredAttribute.getName());
        displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
        if (holderRequiredAttribute.getAttributeValueType().equals(AttributeValueType.DROPDOWN) && !holderRequiredAttribute.isCapacityHiddenForHolder()) {
            log.info("setHolderData | AttributeValueType.DROPDOWN | holder default value json : {}", holderRequiredAttribute.getDefaultValueJsonHolder());
            List<TicketHolderAttributesCapacity> ticketHolderAttributesCapacities = holderAttributeCapacityService.findAllByTicketHolderAttributesId(holderRequiredAttribute);
            Map<String, Long> labelWithCapacity = ticketHolderAttributesCapacities.stream().collect(Collectors.toMap(TicketHolderAttributesCapacity::getLabel, TicketHolderAttributesCapacity::getCapacity));

            String jsonValueForHolder = holderRequiredAttribute.getDefaultValueJsonHolder();
            JsonObject jsonObjectHolder = JsonMapper.stringToObjectUsingGSON(jsonValueForHolder, JsonObject.class);
            if (jsonObjectHolder != null && jsonObjectHolder.has(DEFAULT_VALUES)) {
                JsonObject jsonObjectHol = jsonObjectHolder.get(DEFAULT_VALUES).getAsJsonObject();
                JsonArray jsonArrayHolder = jsonObjectHol.getAsJsonArray(DEFAULT_VALUE);
                JsonArray updatedArrayHolder = new JsonArray(); // Create a new array to store updated elements

                for (JsonElement e : jsonArrayHolder) {
                    JsonObject jsonObject = e.getAsJsonObject();
                    String label = jsonObject.get(LABEL).getAsString();
                    if (labelWithCapacity.containsKey(label)) {
                        Long usedCapacity = eventTicketsRepository.findCapacityCountBasedOnSelectedOptions(eventTicket.getEvent().getEventId(), holderRequiredAttribute.getName(), label);
                        Long totalCapacity = labelWithCapacity.get(label);
                        log.info("setHolderData | AttributeValueType.DROPDOWN | label : {} | usedCapacity : {} | totalCapacity : {}",
                                label, usedCapacity, totalCapacity);
                        if (usedCapacity < totalCapacity || totalCapacity == -1) {
                            updatedArrayHolder.add(e); // Keep the element
                        } else {
                            jsonObject.addProperty("capacityReached", true); // Add the property
                            updatedArrayHolder.add(jsonObject);
                        }
                    } else {
                        updatedArrayHolder.add(e); // Keep elements not in labelWithCapacity
                    }
                }
                jsonObjectHol.add(DEFAULT_VALUE, updatedArrayHolder);
                jsonObjectHolder.add(DEFAULT_VALUES, jsonObjectHol);
                displayAttributeDto.setDefaultValue(jsonObjectHolder.toString());
                log.info("setHolderData | AttributeValueType.DROPDOWN | Default Value : {}", displayAttributeDto.getDefaultValue());
            }
        } else {
            displayAttributeDto.setDefaultValue(holderRequiredAttribute.getDefaultValueJsonHolder());
        }
        displayAttributeDto.setIsHidden(holderRequiredAttribute.isInvisibleForHolder());
        displayAttributeDto.setMandatory(isAddon ? holderRequiredAttribute.getRequiredForTicketPurchaser()
                :  isAttributeMandatoryForTicketingType(eventTicket.getTicketingTypeOnlyId(),holderRequiredAttribute.getHolderRequiredTicketTypeId()));
        displayAttributeDto.setPosition(holderRequiredAttribute.getHolderAttributeOrder());
        if (Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
            updateCellPhone(eventTicket, displayAttributeDto);
        } else if (displayAttributeDto.getType().equals(AttributeValueType.BILLING_ADDRESS.getType()) ||
                displayAttributeDto.getType().equals(AttributeValueType.SHIPPING_ADDRESS.getType())) {
            updateBillingAndShippingAddress(ticketAttributeValueDto, holderRequiredAttribute, displayAttributeDto);
        } else {
            String value = getTicketAttributeValue(eventTicket, ticketAttributeValueDto, holderRequiredAttribute);
            displayAttributeDto.setValue(getHolderAttributeDefaultValue(value,holderRequiredAttribute,(Map<String, String>) ticketAttributeValueDto.getHolder().get(Constants.TICKETING.ATTRIBUTES)));
        }

        displayAttributeDto.setEventTicketTypeId(holderRequiredAttribute.getHolderEventTicketTypeId());
        displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getHolderRequiredTicketTypeId());
        displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getHolderOptionalTicketTypeId());
        displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getHolderRegistrationHiddenTicketTypeId());
        // Check is question or attribute
        if (holderRequiredAttribute.isAttribute()) {
            setHolderAttributesData(holderAttributesDto, displayNestedQueDtoList, holderRequiredAttribute, displayAttributeDto,false);
        } else {
            holderQuestionDto.add(displayAttributeDto);
        }
    }

    private String getHolderAttributeDefaultValue(String value, TicketHolderRequiredAttributes holderRequiredAttribute, Map<String, String> holderAttributesMap) {
        if (value != null) {
            return value;
        }
        if (AttributeValueType.SINGLE_CHECKBOX.equals(holderRequiredAttribute.getAttributeValueType()) || !(holderAttributesMap!=null && holderAttributesMap.containsKey(holderRequiredAttribute.getName()))) { // added this condition due to need to show default value for required attribute if not present in the holder data
            String defaultJsonForHolder = holderRequiredAttribute.getDefaultValueJsonHolder();
            if (defaultJsonForHolder != null) {
                JSONObject jsonObjectHolder;
                try {
                    jsonObjectHolder = new JSONObject(defaultJsonForHolder);
                    String valueForHolder = jsonObjectHolder.get("defaultValueForHolder").toString();
                    return valueForHolder.equalsIgnoreCase(STRING_NULL) ? null : valueForHolder;
                } catch (JSONException e) {
                    log.info("getHolderAttributeDefaultValue defaultJsonForHolder json parsing error holderRequiredAttributeId {} error {}", holderRequiredAttribute.getId(),e.getMessage());
                }
            }
        }
        return null;
    }
    private void setAddOnData(EventTickets eventTicket, List<DisplayAttributeDto> holderAttributesDto, List<DisplayAttributeDto> holderQuestionDto, List<DisplayNestedQueDto> displayNestedQueDtoList, TicketAttributeValueDto1 ticketAttributeValueDto,TicketHolderRequiredAttributes holderRequiredAttribute) {
        DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
        displayAttributeDto.setId(holderRequiredAttribute.getId());
        displayAttributeDto.setName(holderRequiredAttribute.getName());
        displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
        displayAttributeDto.setIsHidden(holderRequiredAttribute.isInvisibleForHolder());
        displayAttributeDto.setMandatory(holderRequiredAttribute.getRequiredForTicketPurchaser());
        if (Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
            updateCellPhone(eventTicket, displayAttributeDto);
        } else if (displayAttributeDto.getType().equals(AttributeValueType.BILLING_ADDRESS.getType()) ||
                displayAttributeDto.getType().equals(AttributeValueType.SHIPPING_ADDRESS.getType())) {
            updateBillingAndShippingAddress(ticketAttributeValueDto, holderRequiredAttribute, displayAttributeDto);
        } else {
            String value = getTicketAttributeValue(eventTicket, ticketAttributeValueDto, holderRequiredAttribute);
            displayAttributeDto.setValue(value);
        }
        displayAttributeDto.setDefaultValue(holderRequiredAttribute.getDefaultValueJsonPurchaser());
        displayAttributeDto.setEventTicketTypeId(holderRequiredAttribute.getHolderEventTicketTypeId());
        displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getHolderRequiredTicketTypeId());
        displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getHolderOptionalTicketTypeId());
        displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getHolderRegistrationHiddenTicketTypeId());
        // Check is question or attribute
        if (holderRequiredAttribute.isAttribute()) {
            setHolderAttributesData(holderAttributesDto, displayNestedQueDtoList, holderRequiredAttribute, displayAttributeDto,false);
        } else {
            holderQuestionDto.add(displayAttributeDto);
        }
    }

    private void updateBillingAndShippingAddress(TicketAttributeValueDto1 ticketAttributeValueDto, TicketHolderRequiredAttributes holderRequiredAttribute, DisplayAttributeDto displayAttributeDto) {
        String addressValue = getAttributeValue1(ticketAttributeValueDto,
                holderRequiredAttribute.getName(), true,
                holderRequiredAttribute.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()));
        addressValue = StringUtils.isBlank(addressValue) ? ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR + ADD_SEPARATOR : addressValue;
        displayAttributeDto.setValue(addressValue);
    }

    private void updateCellPhone(EventTickets eventTicket, DisplayAttributeDto displayAttributeDto) {
        String countryCode = eventTicket.getHolderCountryCode();
        String cellNumber = getHolderPhoneNumber(eventTicket);
        if (null != cellNumber) {
            PhoneNumber phoneDetail = twilioPhoneNumberValidateService.getPhoneDetail(countryCode, cellNumber);
            if (null != phoneDetail) {
                countryCode = phoneDetail.getCountryCode();
                cellNumber = ticketingHelperService.prepareNumber(phoneDetail.getNationalFormat());
            }
        }
        try {
            countryCode = StringUtils.isNotBlank(countryCode) ? countryCode.substring(0, 2)
                    : CountryCode.US.toString();
        } catch (Exception e) {
            countryCode = CountryCode.US.toString();
        }
        displayAttributeDto.setValue(countryCode + ADD_SEPARATOR + cellNumber);
    }

    private boolean isAllowToGetQuestions(boolean isAddon, TicketHolderRequiredAttributes holderRequiredAttribute) {
        return holderRequiredAttribute.getEnabledForTicketHolder()
                || (isAddon
                && holderRequiredAttribute.getEnabledForTicketPurchaser());
    }

	@Async
    public  void updateUserEventTicketAndOrderDetails(User updatedUser){
        List<EventTickets> eventTicketsList= eventCommonRepoService.getHolderEventTicketsANDNotINRecordStatusAndEventNotDeleted(updatedUser);
        if (!eventTicketsList.isEmpty()) {
            eventTicketsList.stream().forEach(e -> e.updateCommanFields(updatedUser));
            eventCommonRepoService.saveAll(eventTicketsList);
        }
        List<EventTickets> eventTicketsListFromOrders = new ArrayList<>();

        List<TicketingOrder> ticketingOrders =ticketingOrderService.getOrderByPurchaseridAndRecordStatusNotDeleted(updatedUser);
        if(!ticketingOrders.isEmpty()) {
            ticketingOrders.stream().forEach(e -> e.setBuyerEmail(updatedUser.getEmail()));
            ticketingOrderService.saveAll(ticketingOrders);
            List<Long> orderIdList = ticketingOrders.stream().map(TicketingOrder::getId).distinct().collect(Collectors.toList());
            eventTicketsListFromOrders.addAll(eventCommonRepoService.findAllByListOrderIds(orderIdList));
        }
        updateTicketholderandPurchaserinfo(eventTicketsList,updatedUser,eventTicketsListFromOrders);
    }

    @Async
    public  void updateTicketholderandPurchaserinfo(List<EventTickets> eventTickets,User updatedUser,List<EventTickets> eventTicketsListFromOrders){
	    try{
	        if(!eventTickets.isEmpty()){
            Map<Long,Event>  eventwithTicket=new HashMap<>();
           eventTickets.stream().forEach(e->eventwithTicket.put(e.getId(),e.getEvent()));
            Map<Long,List<TicketHolderRequiredAttributes>> ticketHolderRequiredAttributesByEvent = new HashMap<>();
            eventwithTicket.entrySet().stream().forEach(e-> ticketHolderRequiredAttributesByEvent.put(e.getKey(),ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(e.getValue(),null)));

            for(EventTickets eventTicket:eventTickets) {
                TicketHolderAttributes ticketHolderAttributes=eventTicket.getTicketHolderAttributesId();
                TicketAttributeValueDto1 ticketAttributeValueDto = getTicketAttributeValueDto(eventTicket);
                Map<String, String> updatedAttributeValueForHolder = new HashMap<>();

            for (TicketHolderRequiredAttributes holderRequiredAttribute : ticketHolderRequiredAttributesByEvent.get(eventTicket.getId())) {
                if (holderRequiredAttribute.getEnabledForTicketHolder()) {
                    if (FIRST_NAME.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                        updatedAttributeValueForHolder.put(holderRequiredAttribute.getName(), updatedUser.getFirstName());
                    } else if (LAST_NAME.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                        updatedAttributeValueForHolder.put(holderRequiredAttribute.getName(), updatedUser.getLastName());
                    } else if (EMAIL.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                        updatedAttributeValueForHolder.put(holderRequiredAttribute.getName(), updatedUser.getEmail());
                    }else if(CELL_PHONE.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                        updatedAttributeValueForHolder.put(holderRequiredAttribute.getName(),  String.valueOf(updatedUser.getPhoneNumber()));
                    }
                }
            }
            TicketAttributeValueDto1 ticketAttributeValueDto1= setTicketAttributeValueDto(ticketAttributeValueDto, updatedAttributeValueForHolder,null);
            handleJSONValue(ticketHolderAttributes,ticketAttributeValueDto1);
        }
	        }
	        if(!eventTicketsListFromOrders.isEmpty()){
            Map<Long,Event>  eventwithTicket1=new HashMap<>();
            eventTicketsListFromOrders.stream().forEach(e->eventwithTicket1.put(e.getId(),e.getEvent()));
            Map<Long,List<TicketHolderRequiredAttributes>> ticketHolderRequiredAttributesByEvent1 = new HashMap<>();
            eventwithTicket1.entrySet().stream().forEach(e-> ticketHolderRequiredAttributesByEvent1.put(e.getKey(),ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(e.getValue(),null)));

            for(EventTickets eventTicket:eventTicketsListFromOrders) {
                TicketHolderAttributes ticketHolderAttributes=eventTicket.getTicketHolderAttributesId();
                TicketAttributeValueDto1 ticketAttributeValueDto = getTicketAttributeValueDto(eventTicket);
                Map<String, String> updatedAttributeValueForPurchaser = new HashMap<>();

                for (TicketHolderRequiredAttributes holderRequiredAttribute :    ticketHolderRequiredAttributesByEvent1.get(eventTicket.getId())) {
                    if (holderRequiredAttribute.getEnabledForTicketPurchaser()) {
                        if (FIRST_NAME.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                            updatedAttributeValueForPurchaser.put(holderRequiredAttribute.getName(), updatedUser.getFirstName());
                        } else if (LAST_NAME.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                            updatedAttributeValueForPurchaser.put(holderRequiredAttribute.getName(), updatedUser.getLastName());
                        } else if (EMAIL.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                            updatedAttributeValueForPurchaser.put(holderRequiredAttribute.getName(), updatedUser.getEmail());
                        }else if(CELL_PHONE.equalsIgnoreCase(holderRequiredAttribute.getName())) {
                            updatedAttributeValueForPurchaser.put(holderRequiredAttribute.getName(),  String.valueOf(updatedUser.getPhoneNumber()));
                        }
                    }
                }
                TicketAttributeValueDto1 ticketAttributeValueDto1= setTicketAttributeValueDto(ticketAttributeValueDto, null,updatedAttributeValueForPurchaser);
                handleJSONValue(ticketHolderAttributes,ticketAttributeValueDto1);
            }
	        }
	    }catch (Exception e){
            log.error("Error on updating order & EventTicketDetails for user:{} Exception {}",updatedUser.getUserId(),e.getMessage());
        }
    }


	protected TicketAttributeValueDto1 getTicketAttributeValueDto(EventTickets eventTickets) throws JAXBException { //NOSONAR
		TicketHolderAttributes ticketHolderAttributes = ticketHolderAttributesService
				.findById(eventTickets.getTicketHolderAttributesId().getId());

		return TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
	}


    protected List<DisplayAttributeDto> filterAttributes(List<DisplayAttributeDto> holderAttributesDto, long ticketTypeId) {
        List<DisplayAttributeDto> filteredHolderAttributesDto = new ArrayList<>();

        for (DisplayAttributeDto attributeDto : holderAttributesDto) {
            if (StringUtils.isNotBlank(attributeDto.getOptionalTicketTypeId())
                    || StringUtils.isNotBlank(attributeDto.getRequiredTicketTypeId())
                    || StringUtils.isNotBlank(attributeDto.getHiddenTicketTypeId()) ) {
                Set<String> combinedTicketTypeIdsSet = Stream.of(
                        GeneralUtils.convertCommaSeparatedToList(attributeDto.getOptionalTicketTypeId()),
                        GeneralUtils.convertCommaSeparatedToList(attributeDto.getRequiredTicketTypeId()),
                        GeneralUtils.convertCommaSeparatedToList(attributeDto.getHiddenTicketTypeId())
                ).flatMap(java.util.Collection::stream).collect(Collectors.toSet());


                if (combinedTicketTypeIdsSet.contains(String.valueOf(ticketTypeId))) {
                    filteredHolderAttributesDto.add(attributeDto);
                }
            }
        }

        return filteredHolderAttributesDto;
    }

    private String getTicketAttributeValue(EventTickets eventTicket, TicketAttributeValueDto1 ticketAttributeValueDto, TicketHolderRequiredAttributes ticketHolderRequiredAttributes) {
		String ticketHolderAttributeValue = null;
		if(checkEventTicketsAttribute(ticketHolderRequiredAttributes.getName())){
			ticketHolderAttributeValue = getTicketHolderAttributesValues(eventTicket, ticketHolderRequiredAttributes.getName());
		} else if (DataType.ADDON != eventTicket.getDataType()){
			ticketHolderAttributeValue = getAttributeValue1(ticketAttributeValueDto,
					ticketHolderRequiredAttributes.getName(), true,
					ticketHolderRequiredAttributes.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType()) || isNumberGreaterThanZero(ticketHolderRequiredAttributes.getParentQuestionId()));
		} else {
			ticketHolderAttributeValue = TicketHolderHelperPrimitive.getAddOnAttributeValue(ticketAttributeValueDto,
					ticketHolderRequiredAttributes.getName(),
					ticketHolderRequiredAttributes.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(ticketHolderRequiredAttributes.getAttributeValueType()) || isNumberGreaterThanZero(ticketHolderRequiredAttributes.getParentQuestionId()));
		}
		return ticketHolderAttributeValue;
	}


	public String getHolderPhoneNumber(EventTickets eventTickets){
		if(eventTickets.getHolderPhoneNumber()!=null && eventTickets.getHolderPhoneNumber() >0){
			return String.valueOf(eventTickets.getHolderPhoneNumber());
		} else {
			return null;
		}
	}

	private boolean checkEventTicketsAttribute(String name) {
		return Arrays.asList(EMAIL,STRING_FIRST_SPACE_NAME,STRING_LAST_SPACE_NAME).contains(name);
	}

	protected String getTicketHolderAttributesValues(EventTickets eventTicket, String attributeKey) {

		if (STRING_EMAIL_SPACE.equalsIgnoreCase(attributeKey)) {
			return eventTicket.getHolderEmail();
		}
		if (STRING_FIRST_SPACE_NAME.equalsIgnoreCase(attributeKey)) {
			return eventTicket.getHolderFirstName();
		}
		if (STRING_LAST_SPACE_NAME.equalsIgnoreCase(attributeKey)) {
			return eventTicket.getHolderLastName();
		}
		return null;
	}

	@Override
	public HolderDisplayAttributesDto getTicketHolderAttributes(long ticketId, Long userId) throws JAXBException {
		EventTickets eventTickets = eventCommonRepoService.findById(ticketId);
		if(eventTickets != null){
            log.info("getTicketHolderAttributes ticketId: {} , loggedInUser: {}", ticketId,userId);
		    if(eventTickets.getHolderUserId().getUserId().equals(userId) || eventTickets.getTicketPurchaserId().getUserId().equals(userId) ){
                return this.getEditTicketHoldersAttributeDto(eventTickets, eventTickets.getEvent());
            }else{
                throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.EVENT_TICKETS_NOT_FOUND);
            }
		} else {
			return new HolderDisplayAttributesDto();
		}
	}


    @Override
    public HolderDisplayAttributesDto getTicketPurchaserAttributesForMyProfile(long orderId, User user, DataType dataType) throws JAXBException {
        TicketingOrder order = ticketingOrderService.findByIdAndpurchaserId(user.getUserId(),orderId);
        return getTicketPurchaserAttributes(order,dataType);
    }

    @Override
    public HolderDisplayAttributesDto getTicketPurchaserAttributesForHost(long orderId, Event event, DataType dataType) throws JAXBException {
        TicketingOrder order = ticketingOrderService.findByidAndEventid(orderId,event);
        return getTicketPurchaserAttributes(order,dataType);
    }

	private HolderDisplayAttributesDto getTicketPurchaserAttributes(TicketingOrder order, DataType dataType) throws JAXBException {
		List<EventTickets> eventTicketsList = eventCommonRepoService.findByOrder(order);
		if (!eventTicketsList.isEmpty()) {
			return getEditTicketPurchaserAttributeDto(order.getEventid(), eventTicketsList, dataType);
		} else {
			return new HolderDisplayAttributesDto();
		}
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTicketHolderData(long ticketId, Long loggedInUser, AttendeeAttributeValueDto attendeeAttributesDto) throws JAXBException {//NOSONAR
        boolean isTicketTransfer = false;
        log.info("updateTicketHolderData questions {}", attendeeAttributesDto.getQuestions());
        log.info("updateTicketHolderData ticketId {}, nestedQuestions {}", ticketId, attendeeAttributesDto.getNestedQuestions());
        EventTickets eventTickets = eventCommonRepoService.findById(ticketId);
        if(eventTickets==null){
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.TICKET_NOT_FOUND);
        }
        String previousEmailForIntegration = StringUtils.isBlank(eventTickets.getHolderEmail()) ? STRING_EMPTY : eventTickets.getHolderEmail();
        Event event = eventTickets.getEvent();
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        Long recurringEventId = null != eventTickets ? eventTickets.getRecurringEventId() : null;
        List<TicketHolderRequiredAttributes> requiredAttributes = ticketRequireAttributeService.getTicketHolderRequiredAttributes(event, recurringEventId);
        Map<String, TicketHolderRequiredAttributes> holderAttributesMap = requiredAttributes.stream().filter(TicketHolderRequiredAttributes::getEnabledForTicketHolder).collect(Collectors.toMap(TicketHolderRequiredAttributes::getName, Function.identity()));
        TicketHolderAttributes ticketHolderAttributes = eventTickets.getTicketHolderAttributesId();
        TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());

        log.info("TicketHolderEditAttributesServiceImpl | updateTicketHolderData | ticketAttributeValueDto : {} | eventTickets : {} | recurringEventId : {}", ticketAttributeValueDto, eventTickets.getId(), recurringEventId);//NOSONAR
        // Update attendee interest tags in neptune
        updateAttendeeInterest(attendeeAttributesDto, event, eventTickets.getHolderUserId(), recurringEventId);

        validateDropdownCapacity(attendeeAttributesDto, event, ticketAttributeValueDto);

        Map<String, String> holderAttributes = attendeeAttributesDto.getAttributes().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e -> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));
        Map<String, String> holderQue = attendeeAttributesDto.getQuestions().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e -> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));

        Map<String, Object> mapForHolderAndAddons = new HashMap<>();
        updateBillingAndShippingAddress(attendeeAttributesDto, mapForHolderAndAddons);

        Map<String, Map<String,String>> existingHolderDetails =   ticketAttributeValueDto.getHolder();
        if(!existingHolderDetails.isEmpty()){
            Map<String,String> existingHolderAttributes  = existingHolderDetails.get(TICKETING.ATTRIBUTES);
            existingHolderAttributes.forEach(holderAttributes::putIfAbsent);
        }

        mapForHolderAndAddons.put(TICKETING.ATTRIBUTES, holderAttributes);
        mapForHolderAndAddons.put(TICKETING.QUESTIONS, holderQue);

        if (DataType.ADDON != eventTickets.getDataType()) {
            ticketAttributeValueDto.setHolder(mapForHolderAndAddons);
        } else {
            ticketAttributeValueDto.setAddOn(mapForHolderAndAddons);
        }

        Optional<AttributeKeyValueDto> countryCodeOptional = attendeeAttributesDto.getAttributes().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(COUNTRY_CODE_KEY)).findFirst();

        log.info("TicketHolderEditAttributesServiceImpl | updateTicketHolderData | attendeeAttributesDto : {}", attendeeAttributesDto.getAttributes());//NOSONAR
        attendeeAttributesDto.getAttributes().forEach(attribute -> {
                    if (attribute.getKey().equalsIgnoreCase(FIRST_NAME) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 50) {
                        throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.FIRST_NAME_SIZE_LIMIT);
                    } else if (attribute.getKey().equalsIgnoreCase(LAST_NAME) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 50) {
                        throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.LAST_NAME_SIZE_LIMIT);
                    } else if (attribute.getKey().equalsIgnoreCase(EMAIL) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 75) {
                        throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.EMAIL_SIZE_LIMIT);
                    } else if (attribute.getKey().equalsIgnoreCase(EMAIL) && StringUtils.isNotEmpty(attribute.getValue()) && !GeneralUtils.isValidEmailAddress(attribute.getValue().trim())) {
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_EMAIL);
                    } else if (attribute.getKey().equalsIgnoreCase(PHONE_NUMBER_KEY) && StringUtils.isNotEmpty(attribute.getValue()) && countryCodeOptional.isPresent() && !twilioPhoneNumberValidateService.isValidPhoneNumber(countryCodeOptional.get().getValue(), attribute.getValue())) {
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PHONE_NUMBER);
                    }
                    if (NumberUtils.isNumberGreaterThanZero(loggedInUser)) {
                        User loginUser = userService.findByUserId(loggedInUser);
                        if (!roStaffService.isEventStaffOrAdmin(event, loginUser)) {
                            validateTicketAttributeValuesBeforeUpdate(holderAttributesMap, attribute, true, Collections.singletonList(eventTickets.getTicketingTypeOnlyId()));
                        }
                    } else {
                        validateTicketAttributeValuesBeforeUpdate(holderAttributesMap, attribute, true, Collections.singletonList(eventTickets.getTicketingTypeOnlyId()));
                    }
                }
        );

        Map<String, String> questionsMap = new HashMap<>();
        attendeeAttributesDto.getQuestions().forEach(question ->
                questionsMap.put(question.getKey(), question.getValue())
        );

        Map<String, String> ticketHolderAttributesMap;
        if (DataType.ADDON != eventTickets.getDataType()) {
            ticketHolderAttributesMap = (Map<String, String>) ticketAttributeValueDto.getHolder().get(TICKETING.ATTRIBUTES);
        } else {
            ticketHolderAttributesMap = (Map<String, String>) ticketAttributeValueDto.getAddOn().get(TICKETING.ATTRIBUTES);
        }

        if (Boolean.TRUE.equals(ticketing.getUniqueTicketHolderEmail()) && ticketHolderAttributesMap.containsKey(EMAIL)) {
            String email = ticketHolderAttributesMap.get(EMAIL);
            if (!StringUtils.isBlank(email) && !email.equals(eventTickets.getHolderEmail())) {
                User user = roUserService.findByEmail(email);
                if (user != null) {
                    List<Long> eventTicketsCountOfUser = eventTicketsRepository.findByHolderUserIdAndEventId(user.getUserId(), event.getEventId(),  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
                    if (!eventTicketsCountOfUser.isEmpty()) {
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.PROVIDED_HOLDER_EMAIL_ALREADY_HAS_TICKET);
                    }
                }
            }
        }
        ticketHolderAttributesService.save(ticketHolderAttributes);
        addDetailsToEventTickets(event, eventTickets, ticketHolderAttributesMap);
        if (eventTickets.getGuestOfBuyer() && !StringUtils.isBlank(eventTickets.getHolderEmail())) {
            isTicketTransfer = true;
            eventTickets.setGuestOfBuyer(false);
        } else if (!eventTickets.getGuestOfBuyer() && !previousEmailForIntegration.equals(eventTickets.getHolderEmail())) {
            if (TicketStatus.CHECKED_IN.equals(eventTickets.getTicketStatus())) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_FOR_CHECK_IN);
            }
            log.info("updateTicketHolderData ticketId: {} , Old barcodeId: {} and ticketCode: {}",eventTickets.getId(),eventTickets.getBarcodeId(),eventTickets.getTicketCode());
            eventTickets.setBarcodeId(UUID.randomUUID().toString());
            eventTickets.setTicketCode(CommonUtil.generateUniqueCode());
            log.info("updateTicketHolderData New barcodeId: {} and ticketCode: {}", eventTickets.getBarcodeId(), eventTickets.getTicketCode());

            if(StringUtils.isBlank(eventTickets.getHolderEmail())) {
                eventTickets.setGuestOfBuyer(true);
                eventTickets.setHolderUserId(eventTickets.getTicketPurchaserId());
                log.info("updateTicketHolderData Transfer to guest of buyer for ticketId {} and transfer by user {}", ticketId, loggedInUser);
            } else {
                isTicketTransfer = true;
            }
        }

        if(isTicketTransfer){
            EventPlanConfig planConfig = eventPlanConfigService.findByEventId(event.getEventId());
            RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(planConfig.getRegistrationConfigJson());
           /* if (checkEventIsPast(ticketing)) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_FOR_PAST_EVENT);
            } else if (eventTickets.getTransfersCount() >= registrationConfigDto.getMaxTicketTransferLimit()) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_LIMIT_REACHED);
            }*/
            eventTickets.setTransfersCount(eventTickets.getTransfersCount()+1);
        }
        eventTickets.setUpdatedAt(new Date());
        eventTicketsRepository.save(eventTickets);

        //update addons holder info if attached with ticket
        List<EventTickets> addonsAttachedWithTicket = eventTicketsRepoService.findAddonTicketsByEventIdAndTicketId(eventTickets.getEventId(),eventTickets.getId());
        if (!CollectionUtils.isEmpty(addonsAttachedWithTicket)) {
            addonsAttachedWithTicket.forEach(addon -> {
                addon.setHolderFirstName(eventTickets.getHolderFirstName());
                addon.setHolderLastName(eventTickets.getHolderLastName());
                addon.setHolderEmail(eventTickets.getHolderEmail());
                addon.setGuestOfBuyer(eventTickets.getGuestOfBuyer());
                addon.setHolderUserId(eventTickets.getHolderUserId());
            });
            eventTicketsRepoService.saveAll(addonsAttachedWithTicket);
        }

        handleJSONValue(ticketHolderAttributes, ticketAttributeValueDto);
        TicketingOrder ticketingOrder = eventTickets.getTicketingOrder();
        ticketingOrder.setUpdatedAt(new Date());
        ticketingOrderService.save(ticketingOrder);
        log.info(" updateTicketHolderData Is ticket transfer : {}", isTicketTransfer);
        boolean isEnableOrderConfirmationEmail = ticketingHelperService.isEnableOrderConfirmationEmail(event.getEventId());
        if (isTicketTransfer && isEnableOrderConfirmationEmail) {
            this.sendEmailForTransferTicket(eventTickets.getTicketingOrder(), event, Collections.singletonList(eventTickets));
        }
        attendeeProfileService.updateUserDetailOnSQLAndNeptune(eventTickets, loggedInUser, ticketHolderAttributes);
        if (loggedInUser != -1L) {
            if (DataType.ADDON != eventTickets.getDataType()) {
                trayIntegrationService.triggerTrayWebHookForUpdateHolder(eventTickets.getTicketingOrder().getEventid(), eventTickets, previousEmailForIntegration);
            }
            //Getting call for update from slesforce
            afterTaskIntegrationTriggerService.ticketInfoUpdatePostProcess(Collections.singletonList(eventTickets), eventTickets.getTicketingOrder().getEventid(), eventTickets.getTicketingOrder());
            User loggedUser = userService.findByUserId(loggedInUser);
            if (isTicketTransfer) {
                orderAuditLog(event, loggedUser, eventTickets.getTicketingOrderId(), TICKET_TRANSFER_AUDIT_MESSAGE_INFO, true);
            } else {
                orderAuditLog(event, loggedUser, eventTickets.getTicketingOrderId(), TICKET_HOLDER, false);
            }
        }
    }

    private void validateDropdownCapacity(AttendeeAttributeValueDto attendeeAttributesDto, Event event, TicketAttributeValueDto1 ticketAttributeValueDtoDB) {
        log.info("Start of validateDropdownCapacity for event {} holder attributes DB {} attributes dto new {}", event.getEventId(), ticketAttributeValueDtoDB.getHolder(), attendeeAttributesDto);
        List<AttributeKeyValueDto> attributes = attendeeAttributesDto.getAttributes();
        Map holder = ticketAttributeValueDtoDB.getHolder();

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketRequireAttributeService.getTicketHolderRequiredAttributesBasedOnAttributeType(event, AttributeValueType.DROPDOWN);
        Map<String, TicketHolderRequiredAttributes> nameMap = ticketHolderRequiredAttributes.stream()
                .filter(e-> !e.isCapacityHiddenForHolder())
                .collect(Collectors.toMap(TicketHolderRequiredAttributes::getName, Function.identity()));
        if(!CollectionUtils.isEmpty(ticketHolderRequiredAttributes)) {
            List<TicketHolderAttributesCapacity> allTicketHolderAttributesCapacities = holderAttributeCapacityService.findAllByTicketHolderAttributesIds(ticketHolderRequiredAttributes);
            Map<Long, List<TicketHolderAttributesCapacity>> attributeCapacityByAttributeId = allTicketHolderAttributesCapacities.stream().collect(Collectors.groupingBy(
                    capacity -> capacity.getTicketHolderAttributesId().getId(),
                    Collectors.toList()
            ));


            if(!CollectionUtils.isEmpty(attributeCapacityByAttributeId)) {
                for (AttributeKeyValueDto attribute : attributes) {
                    if (nameMap.containsKey(attribute.getKey())) {
                        Map holderMap = holder.get("attributes") != null ? (Map) holder.get("attributes") : new HashMap();
                        String dbValue = (String) holderMap.get(attribute.getKey());
                        if (StringUtils.isNotEmpty(attribute.getValue()) && (dbValue == null || !dbValue.equals(attribute.getValue())) && attributeCapacityByAttributeId.containsKey(nameMap.get(attribute.getKey()).getId())) {
                            List<TicketHolderAttributesCapacity> ticketHolderAttributesCapacities = attributeCapacityByAttributeId.get(nameMap.get(attribute.getKey()).getId());
                            Map<String, Long> labelWithCapacity = ticketHolderAttributesCapacities.stream().collect(Collectors.toMap(TicketHolderAttributesCapacity::getLabel, TicketHolderAttributesCapacity::getCapacity));
                            Long usedCapacity = eventTicketsRepository.findCapacityCountBasedOnSelectedOptions(event.getEventId(), attribute.getKey(), attribute.getValue());
                            Long totalCapacity = labelWithCapacity.get(attribute.getValue());
                            log.info("validateDropdownCapacity | label : {} | usedCapacity : {} | totalCapacity : {} | holder attribute id {}", attribute.getKey(), usedCapacity, totalCapacity, nameMap.get(attribute.getKey()).getId());
                            if (totalCapacity != -1 && usedCapacity >= totalCapacity) {
                                NotAcceptableException.TicketHolderAttributesMsg capacityExceededException = NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_CAPACITY_EXCEEDED;
                                String errorMessage = capacityExceededException.getErrorMessage().replace("${field}", attribute.getKey()).replace("${value}", attribute.getValue());
                                capacityExceededException.setErrorMessage(errorMessage);
                                capacityExceededException.setDeveloperMessage(errorMessage);
                                throw new NotAcceptableException(capacityExceededException);
                            }
                        }
                    }
                }
            }
        }
    }

    private void updateAttendeeInterest(AttendeeAttributeValueDto attendeeAttributesDto, Event event, User holderUser, Long recurringEventId) {
        log.info("Start of Update attendee interest tags for event {} holderUser {}", event.getEventId(), holderUser.getUserId());
        TicketHolderRequiredAttributes interestAttribute;
        if(isNumberGreaterThanZero(recurringEventId)){
            interestAttribute = ticketRequireAttributeService.findByattributeValueTypeAndEventidAndRecurringEventId(AttributeValueType.INTEREST, event, recurringEventId);
        }else {
            interestAttribute = ticketHolderRequiredAttributesService.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType.INTEREST, event);
        }
            if (interestAttribute != null && interestAttribute.getEnabledForTicketHolder()) {
            List<AttributeKeyValueDto> attributes = attendeeAttributesDto.getAttributes();
            for (AttributeKeyValueDto attribute : attributes) {
                if (attribute.getKey().equals(interestAttribute.getName())) {
                    createOrMapInterest(event, holderUser, attribute);
                    break;
                }
            }
            attendeeAttributesDto.setAttributes(attributes);
            log.info("Successfully Updated attendee interest tags for event {} holderUser {}", event.getEventId(), holderUser.getUserId());
        }
    }

    private void createOrMapInterest(Event event, User holderUser, AttributeKeyValueDto attribute) {
        List<InterestDto> interestDtos = JsonMapper.parseJsonArray(attribute.getValue(), InterestDto.class);
        // First remove all the mapped interest tags
        neptuneInterestService.deleteAllInterestsFromUser(String.valueOf(event.getEventId()), String.valueOf(holderUser.getUserId()));
        if (!CollectionUtils.isEmpty(interestDtos)) {
            // Map new interest tags
            for (InterestDto interestDto : interestDtos) {
                if (StringUtils.isBlank(interestDto.getId())) {
                    InterestDto newInterest = neptuneInterestService.createInterestByAttendee(interestDto, event, holderUser);
                    interestDto.setId(newInterest.getId());
                } else {
                    neptuneInterestService.addInterestsToUser(String.valueOf(event.getEventId()), String.valueOf(holderUser.getUserId()), interestDto.getId());
                }
            }
            attribute.setValue(JsonMapper.convertToString(interestDtos));
        }
    }

    private void validateTicketAttributeValuesBeforeUpdate(Map<String, TicketHolderRequiredAttributes> holderAttributesMap, AttributeKeyValueDto attribute, boolean isHolderData,List<Long> ticketingTypeId) {
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = holderAttributesMap.get(attribute.getKey());
        if (ticketHolderRequiredAttributes != null) {
            if (StringUtils.isBlank(attribute.getValue()) && ((isAttributeMandatoryForTicketingType(ticketingTypeId,ticketHolderRequiredAttributes.getHolderRequiredTicketTypeId()) && isHolderData)
                    || (!isHolderData && isAttributeMandatoryForTicketingType(ticketingTypeId,ticketHolderRequiredAttributes.getBuyerRequiredTicketTypeId())))) {
                NotAcceptableException.NotAceptableExeceptionMSG mandatoryFieldException = NotAcceptableException.NotAceptableExeceptionMSG.MANDATORY_FIELD;
                String errorMessage = mandatoryFieldException.getErrorMessage().replace("${field}",ticketHolderRequiredAttributes.getName());
                mandatoryFieldException.setErrorMessage(errorMessage);
                mandatoryFieldException.setDeveloperMessage(errorMessage);
                throw  new NotAcceptableException(mandatoryFieldException);
            }
            if (ticketHolderRequiredAttributes.getAttributeValueType().equals(AttributeValueType.NUMBER) && StringUtils.isNotBlank(attribute.getValue()) && !patternForNumber.matcher(attribute.getValue()).matches()) {
                NotAcceptableException.NotAceptableExeceptionMSG mismatchValueWithFieldTypeException = NotAcceptableException.NotAceptableExeceptionMSG.MISMATCH_VALUE_WITH_FIELD_TYPE;
                String errorMessage = mismatchValueWithFieldTypeException.getErrorMessage().replace("${field}",ticketHolderRequiredAttributes.getName()).replace("${type}",ticketHolderRequiredAttributes.getAttributeValueType().getType());
                mismatchValueWithFieldTypeException.setErrorMessage(errorMessage);
                mismatchValueWithFieldTypeException.setDeveloperMessage(errorMessage);
                throw new NotAcceptableException(mismatchValueWithFieldTypeException);
            }
        }
	}

    private void updateBillingAndShippingAddress(AttendeeAttributeValueDto attendeeAttributesDto, Map<String, Object> mapForHolderAndAddons) {
        if(!CollectionUtils.isEmpty(attendeeAttributesDto.getNestedQuestions())){
            ObjectMapper oMapper = new ObjectMapper();
            List<UpdateNestedQueAnsDto> updateNestedQueAnsDtos = attendeeAttributesDto.getNestedQuestions();

            List<Map<String, String>> mapListToBeStore = new ArrayList<>();
            updateNestedQueAnsDtos.forEach(nestedQue->{
                Map<String, String> nestedQueAnsDto = oMapper.convertValue(nestedQue, Map.class);
                mapListToBeStore.add(nestedQueAnsDto);
                log.info("Each Map {}",nestedQueAnsDto);
            });
            mapForHolderAndAddons.put(TICKETING.NESTEDQUESTIONS,mapListToBeStore);
            log.info("mapForHolderAndAddons {}",mapForHolderAndAddons);
        }
    }

    private String findEventTicketAttributPreviouseEmail(TicketAttributeValueDto1 ticketAttributeValueDto){

        Map<String, String> holder = (Map<String, String>) ticketAttributeValueDto.getHolder().get(Constants.TICKETING.ATTRIBUTES);
	    return (!CollectionUtils.isEmpty(holder))
                ? holder.entrySet().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(EMAIL) && null != attribute.getValue()).map(Map.Entry::getValue).findFirst().map(String::new).orElse("")
                :STRING_EMPTY;
    }

	private void handleJSONValue(TicketHolderAttributes ticketHolderAttributes, TicketAttributeValueDto1 ticketAttributeValueDto) {
        ticketHolderAttributes.setJsonValue(ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto));
        ticketHolderAttributesService.save(ticketHolderAttributes);
    }
	private void addDetailsToEventTickets(Event event, EventTickets eventTickets, Map<String, String> ticketHolderAttributesMap) {
		for (Map.Entry<String, String> mapEntry : ticketHolderAttributesMap.entrySet()){
            setEventsTicketsBasedOnAttributeValue(event, eventTickets, mapEntry.getKey(), mapEntry.getValue(), ticketHolderAttributesMap);
        }
	}


	protected void setEventsTicketsBasedOnAttributeValue(Event event, EventTickets eventTickets, String attributeKey, String attributeValue, Map<String, String> ticketHolderAttributesMap) {
		if (STRING_FIRST_SPACE_NAME.equalsIgnoreCase(attributeKey)) {
			eventTickets.setHolderFirstName(attributeValue);
		}
		if (STRING_LAST_SPACE_NAME.equalsIgnoreCase(attributeKey)) {
			eventTickets.setHolderLastName(attributeValue);
		}

		if (STRING_EMAIL_SPACE.equalsIgnoreCase(attributeKey)) {
			if(!StringUtils.isBlank(attributeValue) &&
					!attributeValue.equals(eventTickets.getHolderEmail())){
				eventTickets.setHolderEmail(attributeValue);

				UserSignupDto userSignupDto = new UserSignupDto();
				userSignupDto.setEmail(eventTickets.getHolderEmail());
                for (Map.Entry<String, String> mapEntry : ticketHolderAttributesMap.entrySet()){
                    if (STRING_FIRST_SPACE_NAME.equalsIgnoreCase(mapEntry.getKey())) {
                        userSignupDto.setFirstName(mapEntry.getValue());
                    }
                    if (STRING_LAST_SPACE_NAME.equalsIgnoreCase(mapEntry.getKey())) {
                        userSignupDto.setLastName(mapEntry.getValue());
                    }
                }
				User user = userService.signUpBidderUserAndReturnUser(userSignupDto, null, true, false, false, false,
						true);

                userSessionService.updateUserSessionUserId(event, user, eventTickets.getHolderUserId(), eventTickets.getId());
				eventTickets.setHolderUserId(user);
			} else if (!eventTickets.getGuestOfBuyer() && StringUtils.isBlank(attributeValue)) {
                eventTickets.setHolderEmail(STRING_EMPTY);
            }
		}

		if (STRING_PHONE_NUMBER.equalsIgnoreCase(attributeKey)) {
		    if (StringUtils.isNotBlank(attributeValue)) {
                eventTickets.setHolderPhoneNumber(Long.parseLong(attributeValue.replaceAll("\\D","")));  // We remove NonDigit Character, From FrontEnd Sometime it pass like this '************'
            } else {
                eventTickets.setHolderPhoneNumber(null);
            }
		}
		if (STRING_COUNTRY_CODE.equalsIgnoreCase(attributeKey)) {
			eventTickets.setHolderCountryCode(attributeValue);
		}
	}



	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateTicketPurchaserDataByOrder(long orderId, Long loggedInUser, AttendeeAttributeValueDto attendeeAttributesDto)//NOSONAR
			throws JAXBException {

		TicketingOrder order = ticketingOrderService.findByid(orderId);
        Event event = order.getEventid();
        List<EventTickets> eventTickets = eventCommonRepoService.findByOrder(order);
        List<Long> ticketingTypeIds = eventTickets.stream().map(EventTickets::getTicketingTypeOnlyId).collect(Collectors.toList());
        validateTicketTransferForCheckedInUsers(event,eventTickets);
		Map<String, String> attributesMap = new HashMap<>();
        Long recurringEventId = !CollectionUtils.isEmpty(eventTickets) ? eventTickets.get(0).getRecurringEventId() : null;
        List<TicketHolderRequiredAttributes> requiredAttributes = ticketRequireAttributeService.getTicketHolderRequiredAttributes(order.getEventid(), recurringEventId);

        Map<String, TicketHolderRequiredAttributes> holderAttributesMap = requiredAttributes.stream().filter(TicketHolderRequiredAttributes::getEnabledForTicketPurchaser).collect(Collectors.toMap(TicketHolderRequiredAttributes::getName, Function.identity()));
        Optional<AttributeKeyValueDto> countryCodeOptional = attendeeAttributesDto.getAttributes().stream().filter(attribute -> attribute.getKey().equalsIgnoreCase(COUNTRY_CODE_KEY)).findFirst();

        attendeeAttributesDto.getAttributes().forEach(attribute -> {
                    if (FIRST_NAME.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 50) {
                        throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.FIRST_NAME_SIZE_LIMIT);
                    } else if (LAST_NAME.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 50) {
                        throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.LAST_NAME_SIZE_LIMIT);
                    } else if (EMAIL.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && attribute.getValue().length() > 75) {
                        throw new NotAcceptableException(NotAcceptableException.SizeLimitExceptionMsg.EMAIL_SIZE_LIMIT);
                    } else if (EMAIL.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && !GeneralUtils.isValidEmailAddress(attribute.getValue().trim())) {
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_EMAIL);
                    } else if (PHONE_NUMBER_KEY.equalsIgnoreCase(attribute.getKey()) && StringUtils.isNotEmpty(attribute.getValue()) && countryCodeOptional.isPresent()) {
                        if (!twilioPhoneNumberValidateService.isValidPhoneNumber(countryCodeOptional.get().getValue(), attribute.getValue())) {
                            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PHONE_NUMBER);
                        }
                    }
                    if (!CELL_PHONE.equalsIgnoreCase(attribute.getKey())) {
                        if(loggedInUser != null) {
                            User loginUser = userService.findByUserId(loggedInUser);
                            if(!roStaffService.isEventStaffOrAdmin(event, loginUser)){
                                validateTicketAttributeValuesBeforeUpdate(holderAttributesMap, attribute,false,ticketingTypeIds);
                            }
                        } else {
                            validateTicketAttributeValuesBeforeUpdate(holderAttributesMap, attribute,false,ticketingTypeIds);
                        }
                    }
                    attributesMap.put(attribute.getKey(), attribute.getValue());
                }
        );

		User newUser = null;
		Long oldPurchaserId = order.getPurchaser().getUserId();
		long oldPhoneNumber = order.getPurchaser().getPhoneNumber();
		String newMobileStr = attributesMap.get(Constants.STRING_PHONE_NUMBER);
		long newPhoneNumber = StringUtils.isNotBlank(newMobileStr) ? Long.parseLong(GeneralUtils.replaceAllNonNumericCharacters(newMobileStr)): oldPhoneNumber;
		if(order.getPurchaser().getEmail() != null && !order.getPurchaser().getEmail().equals(attributesMap.get("Email"))){
            EventPlanConfig planConfig = eventPlanConfigService.findByEventId(event.getEventId());
            RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(planConfig.getRegistrationConfigJson());
           /* if(order.getBuyerTransfersCount() >= registrationConfigDto.getMaxTicketTransferLimit()){
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.BUYER_TRANSFER_LIMIT_REACHED);
            }*/
            order.setBuyerTransfersCount(order.getBuyerTransfersCount()+1);
            UserSignupDto userSignupDto = new UserSignupDto();
			userSignupDto.setEmail(attributesMap.get("Email"));
			userSignupDto.setFirstName(attributesMap.get("First Name"));
			userSignupDto.setLastName(attributesMap.get("Last Name"));
			User user = userService.signUpBidderUserAndReturnUser(userSignupDto, null, true, false, false, false,
					true);
			if(!oldPurchaserId.equals(user.getUserId())){
				newUser = user;
			}
		}
        else if (newPhoneNumber > 0 && oldPhoneNumber != newPhoneNumber) {
            Optional<User> userOpt = roUserService.getUserById(oldPurchaserId);
            if(userOpt.isPresent()) {
                User user = userOpt.get();
                Optional<User> existingUser = roUserService.getUserByAEPhoneNumber(new AccelEventsPhoneNumber(user.getCountryCode(), newPhoneNumber));
                if (existingUser.isPresent()) {
                    throw new ConflictException(ConflictException.UserExceptionConflictMsg.PHONE_NUMBER_ALREADY_ATTACH_TO_DIFFERENT_ACCOUNT);
                } else {
                    user.setPhoneNumber(newPhoneNumber);
                    userService.updateUserPhoneNumber(user);
                }
            }
        }

        Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());

        if(ticketing.isUniqueTicketBuyerEmail() && newUser != null) {
            eventCommonRepoService.findBuyerEmailsByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(
                    event.getEventId(), List.of(newUser.getEmail())).stream().findAny().ifPresent(email -> {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DUPLICATE_BUYER_EMAIL_NOT_ALLOWED);
            });
        }

        User oldTicketHolder = null;
        List<EventTickets> transferTickets = new ArrayList<>();
        for (EventTickets eventTicket : eventTickets) {
            if (newUser != null && eventTicket.getHolderUserId().getUserId().equals(oldPurchaserId)) {
                log.info("updateTicketPurchaserDataByOrder ticketId: {} , Old barcodeId: {} and ticketCode: {}", eventTicket.getId(), eventTicket.getBarcodeId(), eventTicket.getTicketCode());
                eventTicket.setBarcodeId(UUID.randomUUID().toString());
                eventTicket.setTicketCode(CommonUtil.generateUniqueCode());

                if(eventTicket.getGuestOfBuyer()) {
                    eventTicket.setHolderUserId(newUser);
                    log.info("updateTicketPurchaserDataByOrder Transfer to guest of buyer for ticketId {} and transfer by user {} holderUserId {}", eventTicket.getId(), loggedInUser, newUser.getUserId());
                } else {
                    oldTicketHolder = eventTicket.getHolderUserId();
                    eventTicket.setHolderUserId(newUser);
                    eventTicket.setHolderFirstName(newUser.getFirstName());
                    eventTicket.setHolderLastName(newUser.getLastName());
                    eventTicket.setHolderEmail(newUser.getEmail());
                    log.info("updateTicketPurchaserDataByOrder Transfer to new user for ticketId {} and transfer by user {} holder email {}", eventTicket.getId(), loggedInUser, newUser.getEmail());
                    EventPlanConfig planConfig = eventPlanConfigService.findByEventId(event.getEventId());
                    RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(planConfig.getRegistrationConfigJson());
                   /* if (eventTicket.getTransfersCount() >= registrationConfigDto.getMaxTicketTransferLimit()) {
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_LIMIT_REACHED);
                    }*/
                    eventTicket.setTransfersCount(eventTicket.getTransfersCount());
                    transferTickets.add(eventTicket);
                    //while update holder into event ticket, then also update holder into ticket holder attributes
                    this.updateFirstNameAndLastNameAndEmailForHolder(newUser, eventTicket);
                }
            }
            if (newUser != null) {
                eventTicket.setTicketPurchaserId(newUser);
            }
			this.updateTicketPurchaserData(attendeeAttributesDto, eventTicket);

            if(oldTicketHolder != null){
                userSessionService.updateUserSessionUserId(event, newUser, oldTicketHolder, eventTicket.getId());
            }
		}

        if(newUser == null){
            attendeeProfileService.updateBuyerUserDetailOnSQLAndNeptune(attributesMap.get(FIRST_NAME), attributesMap.get(LAST_NAME), oldPurchaserId,  loggedInUser, event.getEventId() );
        }

		if (newUser != null) {
			eventTicketsRepoService.saveAll(eventTickets);
			order.setPurchaser(newUser);
			ticketingOrderService.save(order);
		}
        order.setUpdatedAt(new Date());
        ticketingOrderService.save(order);
        User user=null;
        if (null!=loggedInUser) {
            user = userService.findByUserId(loggedInUser);
        }
        if(!CollectionUtils.isEmpty(eventTickets)){
            TicketingOrder ticketingOrder = eventTickets.get(0).getTicketingOrder();
            ticketingOrder.setUpdatedAt(new Date());
            ticketingOrderService.save(ticketingOrder);
        }
        if (!CollectionUtils.isEmpty(transferTickets)) {

            if(checkEventIsPast(ticketing)) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_FOR_PAST_EVENT);
            }
            if(ticketing.getUniqueTicketHolderEmail()) {
                validateUniqueEmailForBuyerTicketTransfer(transferTickets, event);
            }
            orderAuditLog(event, user, orderId, AUDIT_MESSAGE_WHEN_TICKET_TRANSFERRED_BY_BUYER, true);
            log.info("updateTicketPurchaserDataByOrder ticket transfer for eventTicket size {}", transferTickets.size());
            boolean isEnableOrderConfirmationEmail = ticketingHelperService.isEnableOrderConfirmationEmail(event.getEventId());
            if (isEnableOrderConfirmationEmail){
                this.sendEmailForTransferTicket(transferTickets.get(0).getTicketingOrder(), event, transferTickets);
            }
        } else {
            orderAuditLog(event, user, orderId, TICKET_BUYER, false);
        }
        afterTaskIntegrationTriggerService.ticketInfoUpdatePostProcess(eventTickets, order.getEventid(), order);
    }

    /**
     * Validates that the REGISTRANT email is unique when transferring tickets.
     * Prevents duplicate emails within the same event.
     *
     * @param transferTickets Tickets being transferred
     * @param event Associated event
     * @throws NotAcceptableException if email already exists for another ticket
     */
    private void validateUniqueEmailForBuyerTicketTransfer(List<EventTickets> transferTickets, Event event) {

        if (!transferTickets.isEmpty() && transferTickets.size() > 1) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.PROVIDED_HOLDER_EMAIL_ALREADY_HAS_TICKET);
        }

        // Get the first ticket to extract the email (assuming all tickets have the same email)
        EventTickets firstTicket = transferTickets.get(0);
        String transferEmail = firstTicket.getHolderEmail();

        if (StringUtils.isBlank(transferEmail)) {
            log.error("Email is blank for ticket transfer in event {} , ticketId {} ", event.getEventId(), firstTicket.getId());
            return;
        }

        log.info("Validating uniqueness of email {} for ticket transfer in event {} orderId {} ",
                transferEmail, event.getEventId(), firstTicket.getTicketingOrder().getId());

        Set<String> existingEmails = eventCommonRepoService.findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndIdNotInAndTicketStatusNotIn(
                event.getEventId(), Collections.singletonList(transferEmail),transferTickets.stream().map(EventTickets::getId).collect(Collectors.toList()));
        if (!existingEmails.isEmpty()) {
            throw new NotAcceptableException(
                    NotAcceptableException.NotAceptableExeceptionMSG.PROVIDED_HOLDER_EMAIL_ALREADY_HAS_TICKET);
        }
    }

    private void updateFirstNameAndLastNameAndEmailForHolder(User newUser, EventTickets eventTickets) {
        TicketHolderAttributes ticketHolderAttributes = eventTickets.getTicketHolderAttributesId();
        TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
        Map<String, String> holder = (Map<String, String>) ticketAttributeValueDto.getHolder().get(Constants.TICKETING.ATTRIBUTES);
        if (!CollectionUtils.isEmpty(holder)) {
            holder.put(FIRST_NAME,newUser.getFirstName());
            holder.put(LAST_NAME, newUser.getLastName());
            holder.put(EMAIL,newUser.getEmail());
        }
        ticketAttributeValueDto.setHolder(Collections.singletonMap(Constants.TICKETING.ATTRIBUTES, holder));
        handleJSONValue(ticketHolderAttributes, ticketAttributeValueDto);
    }

     void orderAuditLog(Event event, User user, Long orderId, String info,boolean isTicketTransfer) {
        OrderAuditLog auditLog = new OrderAuditLog();
         auditLog.setEventId(event.getEventId());
         auditLog.setOrderId(orderId);
         auditLog.setCreatedAt(new Date());
         auditLog.setCreatedBy(user);
         auditLog.setNewStatus(STRING_EMPTY);
         auditLog.setOldStatus(STRING_EMPTY);
         if (null!=user) {
            if(isTicketTransfer) {
                auditLog.setMessage(info + user.getFirstName() + " " + user.getLastName() + ".");
            }else {
                auditLog.setMessage(user.getFirstName() + " " + user.getLastName() + " edit " + info + " info.");
            }
        }else {
            auditLog.setMessage("From CEvent API edit " + info + " info.");
        }
         orderAuditLogRepo.save(auditLog);
     }
	private void updateTicketPurchaserData(AttendeeAttributeValueDto attendeeAttributesDto, EventTickets eventTicket) throws JAXBException {
		TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
		TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
		/*ValueDto purchaserValue = new ValueDto();
		purchaserValue.setAttributes(attendeeAttributesDto.getAttributes());
		purchaserValue.setQuestions(attendeeAttributesDto.getQuestions());
		ticketAttributeValueDto.setPurchaser(purchaserValue);*/

        Map<String, String> purchaserAttributes = attendeeAttributesDto.getAttributes().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e-> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));
        Map<String, String> purchaserQue = attendeeAttributesDto.getQuestions().stream().collect(Collectors.toMap(AttributeKeyValueDto::getKey, e-> StringUtils.isNotBlank(e.getValue()) ? e.getValue() : STRING_EMPTY));
        Map<String, Object> purchaserMap = new HashMap<>();
        purchaserMap.put(TICKETING.ATTRIBUTES, purchaserAttributes);
        purchaserMap.put(TICKETING.QUESTIONS, purchaserQue);

        updateBillingAndShippingAddress(attendeeAttributesDto, purchaserMap);

        ticketAttributeValueDto.setPurchaser(purchaserMap);


		handleJSONValue(ticketHolderAttributes, ticketAttributeValueDto);
	}

	public HolderDisplayAttributesDto getEditTicketPurchaserAttributeDto(Event event,
																		 List<EventTickets> eventTicketsList, DataType dataType) throws JAXBException {

		Long recurringEventId = eventTicketsList.get(0).getRecurringEventId();
        Set<Long> ticketingTypeIdSet = eventTicketsList.stream().map(EventTickets::getTicketingTypeOnlyId).collect(Collectors.toSet());
        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME,STRING_LAST_SPACE_NAME,STRING_EMAIL_SPACE);
		List<TicketHolderRequiredAttributes> holderRequiredAttributes =  ticketHolderRequiredAttributesService
				.getBuyerAttributesExcludingTypes(event, recurringEventId, dataType,Collections.singletonList(AttributeValueType.TEXT_BLOCK));

		List<DisplayAttributeDto> purchaserAttributesDto = new ArrayList<>();
		List<DisplayAttributeDto> purchaserQuestionDto = new ArrayList<>();
        List<DisplayNestedQueDto> displayNestedQueDtoList = new ArrayList<>();
		TicketAttributeValueDto1 ticketAttributeValueDto = getTicketAttributeValueDto(eventTicketsList.get(0));

        holderRequiredAttributes = excludeEmptyVATFields(event, ticketAttributeValueDto, holderRequiredAttributes);
		for (TicketHolderRequiredAttributes holderRequiredAttribute : holderRequiredAttributes) {
            if ((defaultAttributes.contains(holderRequiredAttribute.getName()) || !(StringUtils.isBlank(holderRequiredAttribute.getBuyerRequiredTicketTypeId())
                    && StringUtils.isBlank(holderRequiredAttribute.getBuyerOptionalTicketTypeId()) && StringUtils.isBlank(holderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId())) && holderRequiredAttribute.getEnabledForTicketPurchaser()
                    && Boolean.FALSE.equals(holderRequiredAttribute.getDeletedForBuyer())
                    && (defaultAttributes.contains(holderRequiredAttribute.getName().equals(EMAIL) ? holderRequiredAttribute.getName().toUpperCase() : holderRequiredAttribute.getName())
                    || ticketingTypeIdSet.stream().anyMatch(id -> GeneralUtils.convertCommaSeparatedToListLong(holderRequiredAttribute.getBuyerRequiredTicketTypeId()).contains(id))
                    || ticketingTypeIdSet.stream().anyMatch(id -> GeneralUtils.convertCommaSeparatedToListLong(holderRequiredAttribute.getBuyerOptionalTicketTypeId()).contains(id))
                    || ticketingTypeIdSet.stream().anyMatch(id -> GeneralUtils.convertCommaSeparatedToListLong(holderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId()).contains(id))))) {
            DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
                displayAttributeDto.setId(holderRequiredAttribute.getId());
				displayAttributeDto.setName(holderRequiredAttribute.getName());
				displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
				displayAttributeDto.setDefaultValue(holderRequiredAttribute.getDefaultValueJsonPurchaser());
                displayAttributeDto.setIsHidden(holderRequiredAttribute.isInvisibleForPurchaser());
                displayAttributeDto.setMandatory((isAttributeFirstnameLastnameOrEmail(holderRequiredAttribute.getName()) ||
                        isAttributeMandatoryForTicketingType(ticketingTypeIdSet, holderRequiredAttribute.getBuyerRequiredTicketTypeId())));
                String value = getAttributeValue1(ticketAttributeValueDto,
                        displayAttributeDto.getName(), false, holderRequiredAttribute.isAttribute(), AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()) || isNumberGreaterThanZero(holderRequiredAttribute.getParentQuestionId()));
				displayAttributeDto.setValue(getBuyerAttributeDefaultValue(value,holderRequiredAttribute,(Map<String, String>) ticketAttributeValueDto.getPurchaser().get(Constants.TICKETING.ATTRIBUTES)));
                displayAttributeDto.setPosition(holderRequiredAttribute.getBuyerAttributeOrder());
                displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getBuyerRequiredTicketTypeId());
                displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getBuyerOptionalTicketTypeId());
                displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId());
				if ( Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
                    setPhoneNumber(eventTicketsList.get(0), ticketAttributeValueDto, holderRequiredAttribute, displayAttributeDto);
                }
				// Check is question or attribute
                  if (holderRequiredAttribute.isAttribute()) {
                      setHolderAttributesData(purchaserAttributesDto, displayNestedQueDtoList, holderRequiredAttribute, displayAttributeDto,true);
                  } else {
					purchaserQuestionDto.add(displayAttributeDto);
				}
			}
		}
		HolderDisplayAttributesDto attendee = new HolderDisplayAttributesDto();
		attendee.setAttributes(purchaserAttributesDto);
		attendee.setQuestions(purchaserQuestionDto);
        attendee.setNestedQuestions(displayNestedQueDtoList);
		return attendee;
	}

    private String getBuyerAttributeDefaultValue(String value, TicketHolderRequiredAttributes holderRequiredAttribute, Map<String, String> purchaserAttributesMap) {
        if (value != null) {
            return value;
        }
        if (AttributeValueType.SINGLE_CHECKBOX.equals(holderRequiredAttribute.getAttributeValueType()) || !(purchaserAttributesMap!=null && purchaserAttributesMap.containsKey(holderRequiredAttribute.getName()))) { // added this condition due to need to show default value for required attribute if not present in the holder data
            String defaultJsonForHolder = holderRequiredAttribute.getDefaultValueJsonPurchaser();
            if (defaultJsonForHolder != null) {
                JSONObject jsonObjectHolder;
                try {
                    jsonObjectHolder = new JSONObject(defaultJsonForHolder);
                    String valueForPurchaser = jsonObjectHolder.get("defaultValueForPurchaser").toString();
                    return valueForPurchaser.equalsIgnoreCase(STRING_NULL) ? null : valueForPurchaser;
                } catch (JSONException e) {
                    log.info("getBuyerAttributeDefaultValue defaultValueForPurchaser parse error holderRequiredAttributeId {} error {}", holderRequiredAttribute.getId(), e.getMessage());
                }
            }
        }
        return null;
    }
    private List<TicketHolderRequiredAttributes> excludeEmptyVATFields(Event event, TicketAttributeValueDto1 ticketAttributeValueDto,
                                       List<TicketHolderRequiredAttributes> holderRequiredAttributes) {
        if(vatTaxService.isVatTaxEnabled(event)){
            String buyerOrganizationName = getAttributeValue1(ticketAttributeValueDto, ORGANISATION_NAME, false,
                            true, false);
            String buyerVatNumber = getAttributeValue1(ticketAttributeValueDto, VAT_ID, false,
                            true, false);
            if(StringUtils.isBlank(buyerOrganizationName) || StringUtils.isBlank(buyerVatNumber)) {
                holderRequiredAttributes = !holderRequiredAttributes.isEmpty() ? holderRequiredAttributes.stream().filter(e -> !AttributeValueType.VAT_FIELD.equals(e.getAttributeValueType())).collect(Collectors.toList()):null;
            }
        }
        return holderRequiredAttributes;
    }

    private void setHolderAttributesData(List<DisplayAttributeDto> purchaserAttributesDto, List<DisplayNestedQueDto> displayNestedQueDtoList, TicketHolderRequiredAttributes holderRequiredAttribute, DisplayAttributeDto displayAttributeDto,boolean isBuyerAttribute) {
        if(AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType()) || isNumberGreaterThanZero(holderRequiredAttribute.getParentQuestionId())){
            DisplayNestedQueDto displayNestedQueDto=new DisplayNestedQueDto(displayAttributeDto);
            displayNestedQueDto.setId(holderRequiredAttribute.getId());
            displayNestedQueDto.setParentQueId(holderRequiredAttribute.getParentQuestionId());
            displayNestedQueDto.setSelectedAnsId(holderRequiredAttribute.getSelectedAnswer());
            displayNestedQueDto.setEventTicketTypeId(isBuyerAttribute?holderRequiredAttribute.getBuyerEventTicketTypeId():holderRequiredAttribute.getHolderEventTicketTypeId());
            if(isBuyerAttribute){
                displayNestedQueDto.setOptionalTicketTypeId(holderRequiredAttribute.getBuyerOptionalTicketTypeId());
                displayNestedQueDto.setRequiredTicketTypeId(holderRequiredAttribute.getBuyerRequiredTicketTypeId());
                displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId());
            }else{
                displayNestedQueDto.setOptionalTicketTypeId(holderRequiredAttribute.getHolderOptionalTicketTypeId());
                displayNestedQueDto.setRequiredTicketTypeId(holderRequiredAttribute.getHolderRequiredTicketTypeId());
                displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getHolderRegistrationHiddenTicketTypeId());
            }
            displayNestedQueDtoList.add(displayNestedQueDto);
        } else {
            purchaserAttributesDto.add(displayAttributeDto);
        }
    }

    private void setPhoneNumber(EventTickets eventTickets, TicketAttributeValueDto1 ticketAttributeValueDto, TicketHolderRequiredAttributes holderRequiredAttribute, DisplayAttributeDto displayAttributeDto) {
        CountryCode code = eventTickets.getTicketingOrder().getPurchaser().getCountryCode();
        String cellNumber = String.valueOf(eventTickets.getTicketingOrder().getPurchaser().getPhoneNumber());
        String countryCode = null;
        if (null == displayAttributeDto.getValue() && null == code) {
            displayAttributeDto.setValue(getAttributeValue1(ticketAttributeValueDto,
                    Constants.STRING_PHONE_NUMBER, false, holderRequiredAttribute.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType())));
            if (null != displayAttributeDto.getValue()) {
                String numberWithCountryCode = displayAttributeDto.getValue();
                PhoneNumber phoneDetail = twilioPhoneNumberValidateService.getPhoneDetail(countryCode, numberWithCountryCode);
                if (null != phoneDetail) {
                    countryCode = phoneDetail.getCountryCode();
                    cellNumber = ticketingHelperService.prepareNumber(phoneDetail.getNationalFormat());
                }
            }
        }
        if (null == countryCode) {
            countryCode = null != code ? code.name() : CountryCode.US.name();
        }
        if(null != displayAttributeDto && null != displayAttributeDto.getValue() && displayAttributeDto.getValue().contains("|")){
            cellNumber= displayAttributeDto.getValue().substring(displayAttributeDto.getValue().indexOf("|")+1);
             countryCode= displayAttributeDto.getValue().substring(0, displayAttributeDto.getValue().indexOf("|"));
        }
        displayAttributeDto.setValue(countryCode + ADD_SEPARATOR + cellNumber);
    }

    private Unmarshaller getUnmashler() {
		JAXBContext jaxbContext = null;
		try {
			jaxbContext = JAXBContext.newInstance(TicketAttributeValueDto.class);
			return jaxbContext.createUnmarshaller();
		} catch (JAXBException e) {
	//		log.info(e.getLocalizedMessage(), e);//nosonar
		}
		return null;
	}

	@Override
	public List<DisplayAttributeDto> getGenderAndAgeTicketHoldersAttribute(EventTickets eventTickets, List<TicketHolderRequiredAttributes> holderRequiredAttributes, Boolean enableForTicketHolder) throws JAXBException {
		List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
		TicketAttributeValueDto1 ticketAttributeValueDto = getTicketAttributeValueDto(eventTickets);
		for (TicketHolderRequiredAttributes holderRequiredAttribute : holderRequiredAttributes) {
			DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
			if (GENDER.equalsIgnoreCase(holderRequiredAttribute.getName()) || AGE.equalsIgnoreCase(holderRequiredAttribute.getName())) {
				if (enableForTicketHolder && holderRequiredAttribute.getEnabledForTicketHolder()) {

					displayAttributeDto.setName(holderRequiredAttribute.getName());
					displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
					displayAttributeDto.setValue(getTicketAttributeValue(eventTickets, ticketAttributeValueDto, holderRequiredAttribute));
                    displayAttributeDto.setEventTicketTypeId(holderRequiredAttribute.getHolderEventTicketTypeId());
                    displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getHolderOptionalTicketTypeId());
                    displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getHolderRequiredTicketTypeId());
                    displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getHolderRegistrationHiddenTicketTypeId());
				} else if (!enableForTicketHolder && holderRequiredAttribute.getEnabledForTicketPurchaser()) {

					displayAttributeDto.setName(holderRequiredAttribute.getName());
					displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
					displayAttributeDto.setValue(getAttributeValue1(ticketAttributeValueDto, displayAttributeDto.getName(), false, holderRequiredAttribute.isAttribute(),AttributeValueType.CONDITIONAL_QUE.equals(holderRequiredAttribute.getAttributeValueType())));
                    displayAttributeDto.setEventTicketTypeId(holderRequiredAttribute.getBuyerEventTicketTypeId());
                    displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getBuyerOptionalTicketTypeId());
                    displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getBuyerRequiredTicketTypeId());
                    displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getPurchaserRegistrationHiddenTicketTypeId());
				}
				// Check is question or attribute
				if (holderRequiredAttribute.isAttribute()) {
					holderAttributesDto.add(displayAttributeDto);
				}
			}
		}
		return filterAttributes(holderAttributesDto, eventTickets.getTicketingTypeId().getId());
	}

	@Override
	@CacheEvict(value = "findBlockedEmailsForEvent", key = "#event.eventId")
	@Transactional
	public EventTickets blockUnblock(long ticketId, boolean block, Event event) {
        log.info("TicketHolderEditAttributesServiceImpl | blockUnblock | ticketing | {} | eventId | {} |  block | {}",ticketId, event.getEventId(), block );
		EventTickets eventTicket = eventCommonRepoService.findById(ticketId);
        if (eventTicket == null){
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.TICKET_NOT_FOUND);
        }
		setBlockStatus(eventTicket,block);
		attendeeBlockInNeptune(block,eventTicket.getHolderUserId(),event);
        List<EventTickets> otherTickets = eventCommonRepoService.findOtherEventTicketForSameUserANDEvent(event,eventTicket.getHolderUserId(),eventTicket.getId());
        if(!otherTickets.isEmpty())
        {
            for(EventTickets eventTicketObg :otherTickets)
            {
                log.info("TicketHolderEditAttributesServiceImpl blockUnblock before for ticket {}", eventTicketObg.getId());
                setBlockStatus(eventTicketObg,block);
                log.info("TicketHolderEditAttributesServiceImpl blockUnblock after for ticket {}", eventTicketObg.getId());
            }
        }
		return eventTicket;
	}

    public void attendeeBlockInNeptune(boolean block,User user,Event event){
	     neptuneAttendeeDetailService.attendeeBlockedInEvent(block,String.valueOf(user.getUserId()),String.valueOf(event.getEventId()));
    }

    private void setBlockStatus(EventTickets eventTicket ,Boolean block ) {
        eventTicket.setRecordStatus(block?RecordStatus.BLOCK:RecordStatus.CREATE);
    }

    @Override
    public List<HolderDisplayAttributesDto> getTicketHolderDataAttributes(List<Long> ticketIds, Long userId) throws JAXBException {
        log.info("start of get ticket holder data attributes {}, user {}", ticketIds, userId);
        List<HolderDisplayAttributesDto> list = new ArrayList<>();
        List<EventTickets> eventTickets = eventCommonRepoService.findAllByIdAndHolderUserIdOrPurchaserUserId(ticketIds, userId);
        for (EventTickets eventTicket : eventTickets) {
            list.add(this.getEditTicketHoldersAttributeDto(eventTicket, eventTicket.getEvent()));
        }
        log.info("End of get ticket holder data attributes : Fetched {} ticket holder attributes for user {}", list.size(), userId);
        return list;
    }

    @Override
    public HolderBasicDetailsDto getHolderBasicDetailsByBarcodeIdOrRfIdTag(String barcodeIdOrRfIdTag, Event event, boolean isRfidTag) {
        log.info("getHolderBasicDetailsByBarcodeId using barcodeId {}, eventId {}",barcodeIdOrRfIdTag,event.getEventId());

        EventTickets eventTickets = null;

        if(isRfidTag){
            eventTickets = eventTicketsRepoService.findByRfidTagAndEventId(barcodeIdOrRfIdTag, event.getEventId());
        }
        else {
            eventTickets= eventTicketsRepoService.findByBarcodeIdAndEventId(barcodeIdOrRfIdTag, event.getEventId());
        }
        if(eventTickets == null){
            throw new NotFoundException(NotFoundException.TicketingOrderExceptionMsg.TICKET_NOT_FOUND);
        }
        TicketHolderAttributes ticketHolderAttributes = eventTickets.getTicketHolderAttributesId();
        log.info("ticketHolderAttributes {}",ticketHolderAttributes);
        if(null==ticketHolderAttributes){
            throw new NotFoundException(NotFoundException.NotFound.HOLDER_ATTRIBUTE_NOT_FOUND);
        }
        Ticketing ticketing = ticketingHelperService.findTicketingByEventIdOrThrowError(event);
        boolean  isHolderDataPreference = ticketing.getCollectTicketHolderAttributes();
        TicketAttributeValueDto1 ticketAttributeValueDto1 =  TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
        String firstName = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto1, STRING_FIRST_SPACE_NAME, isHolderDataPreference, true, false);
        String lastName = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto1, STRING_LAST_SPACE_NAME, isHolderDataPreference, true, false);
        String email = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto1,STRING_FIRST_CAP_EMAIL_SPACE , isHolderDataPreference, true, false);
        User user = isHolderDataPreference ? eventTickets.getHolderUserId() : eventTickets.getTicketPurchaserId();
        AttendeeProfileDto attendeeProfileDto = attendeeProfileService.getAttendeeProfileInfo(user,event);
        if(attendeeProfileDto.isHideContactInfo()){
            return new HolderBasicDetailsDto(firstName,lastName,email,0L,null,eventTickets.getId(),user.getUserId());
        }
        String phoneNumber = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto1, STRING_PHONE_NUMBER, isHolderDataPreference, true, false);
        String countryCode = TicketHolderHelper.getAttributeValue1(ticketAttributeValueDto1, STRING_COUNTRY_CODE, isHolderDataPreference, true, false);

        return new HolderBasicDetailsDto(firstName,lastName,email,
                null!=phoneNumber?Long.parseLong(phoneNumber.replaceAll("\\D","")): 0L,
                null!=countryCode?CountryCode
                        .valueOf(StringUtils
                                .upperCase(countryCode.substring(0, 2))):null,eventTickets.getId(),user.getUserId());
    }

    @Override
    public List<HolderBadgePdfAttributesDto> getTicketHoldersAttributeByEventTickets(List<EventTickets> eventTickets, Event event) {

        List<Long> eventTicketIdList = eventTickets.stream().map(EventTickets::getId).collect(Collectors.toList());
        log.info("Start of getTicketHoldersAttributeByEventTickets for event {} and eventTickets {}", event.getEventId(), eventTicketIdList);

        List<HolderBadgePdfAttributesDto> holderBadgePdfAttributesDtoList = new ArrayList<>();

        List<Long> recurringEventIds = eventTickets.stream().map(EventTickets::getRecurringEventId).distinct().collect(Collectors.toList());
        Map<Long, List<TicketHolderRequiredAttributes>> holderRequiredAttributesMapByEvent = new HashMap<>();

        List<TicketHolderRequiredAttributes> holderRequiredAttributesList =  ticketHolderRequiredAttributesService
                .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, null, DataType.TICKET);
        holderRequiredAttributesList.sort(Comparator.comparing(TicketHolderRequiredAttributes :: getHolderAttributeOrder));
        holderRequiredAttributesMapByEvent.put(event.getEventId(), holderRequiredAttributesList);

        for (Long recurringEventId : recurringEventIds) {
            if(!holderRequiredAttributesMapByEvent.containsKey(recurringEventId)){
                List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketHolderRequiredAttributesService
                        .getTicketHolderRequiredAttributesOrderByAttributeOrder(event, recurringEventId, DataType.TICKET);
                ticketHolderRequiredAttributes.sort(Comparator.comparing(TicketHolderRequiredAttributes :: getHolderAttributeOrder));
                holderRequiredAttributesMapByEvent.put(recurringEventId, ticketHolderRequiredAttributes);
            }
        }

        Map<Long, TicketAttributeValueDto1> ticketHolderAttributesValueDtoByEventTicketIds = getTicketHolderAttributesValueDtoByEventTicketIds(eventTicketIdList);

        // Get all the addon ticketing type that are assign to event ticket
        List<Object[]> addonWithTicketTypeId = eventTicketsRepoService.getAddonTicketingTypeByEventIdAndTicketIdForAddons(event.getEventId(), eventTicketIdList);
        Map<Long, List<TicketingType>> addonWithEventTicketMap = addonWithTicketTypeId.stream().collect(Collectors.groupingBy(e -> Long.parseLong(e[0].toString()), Collectors.mapping(e -> (TicketingType) e[1], Collectors.toList())));

        List<BadgesResponseData> ticketingIdAndBadgesIdByEventId = badgesService.getTicketingIdAndBadgesIdByEventId(event.getEventId());

        // Get all the unique holder user ids and their roles
        List<Long> uniqueHolderUserId = eventTickets.stream().map(e -> e.getHolderUserId().getUserId()).distinct().collect(Collectors.toList());
        Map<Long, List<String>> userIdAndRolesMap = staffRepoService.findUserRolesAsMapByEventIdAndUserIdsIn(event.getEventId(), uniqueHolderUserId);

        for(EventTickets eventTicket : eventTickets) {
            HolderBadgePdfAttributesDto holderBadgePdfAttributesDto = new HolderBadgePdfAttributesDto();
            List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
            List<DisplayAttributeDto> holderQuestionDto = new ArrayList<>();
            List<DisplayNestedQueDto> displayNestedQueDtoList = new ArrayList<>();

            boolean isAddon = (DataType.ADDON == eventTicket.getDataType());

            TicketAttributeValueDto1 ticketAttributeValueDto = ticketHolderAttributesValueDtoByEventTicketIds.get(eventTicket.getId());
            List<TicketHolderRequiredAttributes> holderRequiredAttributes = holderRequiredAttributesMapByEvent.get(eventTicket.getRecurringEventId() != null ? eventTicket.getRecurringEventId() : event.getEventId());

            for (TicketHolderRequiredAttributes holderRequiredAttribute : holderRequiredAttributes) {
                if (isAllowToGetQuestions(isAddon, holderRequiredAttribute)) {
                    setReadOnlyHolderData(eventTicket, holderAttributesDto, holderQuestionDto, displayNestedQueDtoList, ticketAttributeValueDto, isAddon, holderRequiredAttribute);
                }
            }

            HolderDisplayAttributesDto attendee = new HolderDisplayAttributesDto();
            attendee.setAttributes(filterAttributes(holderAttributesDto, eventTicket.getTicketingTypeId().getId()));
            attendee.setQuestions(holderQuestionDto);
            attendee.setNestedQuestions(displayNestedQueDtoList);
            attendee.setSeatNumber(eventTicket.getSeatNumber());
            attendee.setEventTicketingId(eventTicket.getId());
            attendee.setBarcodeId(eventTicket.getBarcodeId());
            String eventKey = String.valueOf(event.getEventId());
            if(eventTicket.getRecurringEventId()!=null &&
                    eventTicket.getRecurringEventId() > 0){
                eventKey = TicketingUtils.getEventKey(event.getEventId(), true, eventTicket.getRecurringEventId());
            }
            attendee.setEventKey(eventKey);
            if(!isAddon) {
                List<TicketingType> addonWithEventTicket = addonWithEventTicketMap.get(eventTicket.getId());
                if (!CollectionUtils.isEmpty(addonWithEventTicket)) {
                    List<NameIdDTO> addonTicketTypes = addonWithEventTicket.stream().map(e-> new NameIdDTO(e.getId(), e.getTicketTypeName())).collect(Collectors.toList());
                    attendee.setAddonTicketTypes(addonTicketTypes);
                }
            }

            User holderUser = eventTicket.getHolderUserId();
            holderBadgePdfAttributesDto.setUserId(holderUser.getUserId());
            holderBadgePdfAttributesDto.setHolderPhoto(holderUser.getPhoto());
            holderBadgePdfAttributesDto.setFirstName(holderUser.getFirstName());
            holderBadgePdfAttributesDto.setLastName(holderUser.getLastName());
            List<String> roles = userIdAndRolesMap.get(holderUser.getUserId());
            if(roles == null){
                roles = Collections.emptyList();
            }
            holderBadgePdfAttributesDto.setRole(UserRoleUtils.getUserRoleName(roles));

            holderBadgePdfAttributesDto.setHolderAttributesDetails(attendee);
            holderBadgePdfAttributesDto.setBarcode(eventTicket.getBarcodeId());
            holderBadgePdfAttributesDto.setTicketTypeName(eventTicket.getTicketingTypeId().getTicketTypeName());
            Long badgeId = ticketingIdAndBadgesIdByEventId.stream().filter(e -> e.getTicketingTypeIds().contains(eventTicket.getTicketingTypeId().getId())).findFirst().map(BadgesResponseData::getBadgeId).orElse(null);
            holderBadgePdfAttributesDto.setBadgeId(badgeId);
            holderBadgePdfAttributesDtoList.add(holderBadgePdfAttributesDto);

        }

        log.info("End of getTicketHoldersAttributeByEventTickets for event {} and eventTickets {}", event.getEventId(), eventTicketIdList);
        return holderBadgePdfAttributesDtoList;
    }

    @Override
    public Optional<HolderDisplayAttributesDto> getTicketHolderAttributeByEventAndUserId(User loggedInUser, Event event, Long userId, boolean isExhibitorLeadOrAboveRole) throws JAXBException {

        HolderDisplayAttributesDto holderDisplayAttributesDto = null;

        log.info("getTicketHolderAttributeByEventAndUserId by eventId {} ,loggedInUser {} and userId {}", event.getEventId(), loggedInUser.getUserId(), userId);

        if (virtualEventSettingsRepoService.isHolderInfoVisibleToAttendees(event.getEventId()) && checkLoggedInUserHaveAccessToHolderData(loggedInUser, event, isExhibitorLeadOrAboveRole)) {
            User user = userService.findByUserId(userId);
            if (null == user) {
                throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
            }
            EventTickets eventTickets = eventTicketsService.findSingleEventTicketsByHolderUserIdAndEventIdAndDataType(user, event, DataType.TICKET);
            if (eventTickets != null) {
                holderDisplayAttributesDto = getTicketHolderAttributes(eventTickets.getId(), event);
            } else {
                log.info("No event ticket with the TICKET ticketing type was found for this user.");
            }
        } else {
            log.info("No user found with this event Or isHolderInfoVisibleToAttendees field is disabled in General Setting.");
        }

        return Optional.ofNullable(holderDisplayAttributesDto);
    }

    public void updateTicketHolderDataForTicketTransfer(EventTickets eventTickets, UserBasicDto userBasicDto, User loggedInUser,
                                                        TicketHolderAttributes ticketHolderAttributes) {
        TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());
        Map<String, String> holderAttributes = (Map<String, String>) ticketAttributeValueDto.getHolder().get(TICKETING.ATTRIBUTES);
        if (!CollectionUtils.isEmpty(holderAttributes)) {
            updateAttributeByUserBasicDTO(holderAttributes, userBasicDto);
        } else {
            holderAttributes = new HashMap<>();
            holderAttributes.put(FIRST_NAME, userBasicDto.getFirstName());
            holderAttributes.put(LAST_NAME, userBasicDto.getLastName());
            holderAttributes.put(EMAIL, userBasicDto.getEmail());
        }
        TicketHolderHelper.setTicketAttributeValueDto(ticketAttributeValueDto, holderAttributes, null);
        String ticketAttributeValueDtoStr = ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto);
        ticketHolderAttributes.setJsonValue(ticketAttributeValueDtoStr);
        ticketHolderAttributesService.save(ticketHolderAttributes);
        addDetailsToEventTickets(eventTickets.getEvent(), eventTickets, holderAttributes);
        if (eventTickets.getGuestOfBuyer()) {
            eventTickets.setGuestOfBuyer(false);
        } else {
            eventTickets.setBarcodeId(UUID.randomUUID().toString());
            eventTickets.setTicketCode(CommonUtil.generateUniqueCode());
        }
        eventTickets.setTransfersCount(eventTickets.getTransfersCount() + 1);
        orderAuditLog(eventTickets.getEvent(), loggedInUser, eventTickets.getTicketingOrderId(), TICKET_TRANSFER_AUDIT_MESSAGE_INFO, true);
    }


    @Override
    @Transactional
    public void transferTicket(UserBasicDto userBasicDto, List<Long> ticketIdsOrOrderIds, User loggedInUser, boolean isFromUserProfile) {
        log.info("transferTicket ticketIdOrOrderId {} userLoggedIn {} userBasicDto {} isFromUserProfile {}", ticketIdsOrOrderIds, loggedInUser.getUserId(), userBasicDto, isFromUserProfile);
        if (StringUtils.isBlank(userBasicDto.getFirstName()) || StringUtils.isBlank(userBasicDto.getLastName()) || StringUtils.isBlank(userBasicDto.getEmail())) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.ALL_FIELDS_ARE_MANDATORY_FOR_TICKET_TRANSFER);
        }
        if (isFromUserProfile) {
            log.info("transferTicket ticketIds {}", ticketIdsOrOrderIds);
            transferTicketOnHolderDataUpdate(userBasicDto, ticketIdsOrOrderIds, loggedInUser);
        } else {
            log.info("transferTicket orderId {}", ticketIdsOrOrderIds);
            transferTicketOnPurchaserDataUpdate(userBasicDto, ticketIdsOrOrderIds.get(0), loggedInUser);
        }
    }

    private void transferTicketOnPurchaserDataUpdate(UserBasicDto userBasicDto, Long orderId, User loggedInUser) {
        TicketingOrder order = ticketingOrderService.findByid(orderId);
        Event event = order.getEventid();
        List<EventTickets> eventTickets = eventCommonRepoService.findByOrder(order);
        validateNonTransferableTickets(eventTickets,loggedInUser,event,order.isDisableTicketTransfer());
        Ticketing ticketing = ticketingHelperService.findTicketingByEventIdOrThrowError(event);
        EventPlanConfig planConfig = eventPlanConfigService.findByEventId(event.getEventId());
        RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(planConfig.getRegistrationConfigJson());
        if (checkEventIsPast(ticketing)) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_FOR_PAST_EVENT);
        } else {
            if (eventTickets.stream().anyMatch(eventTicket -> eventTicket.getTransfersCount() >= registrationConfigDto.getMaxTicketTransferLimit())) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_LIMIT_REACHED);
            }
        }
        validateTicketTransferForCheckedInUsers(event,eventTickets);
        User user = userService.getOrCreateUser(userBasicDto, event);
        Long oldPurchaserId = order.getPurchaser().getUserId();
        List<EventTickets> transferTickets = new ArrayList<>();
        List<TicketHolderAttributes> ticketHolderAttributesListToSave = new ArrayList<>();
        User oldTicketHolder = null;
        for (EventTickets eventTicket : eventTickets) {
            TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
            if (eventTicket.getHolderUserId().getUserId().equals(oldPurchaserId)) {
                oldTicketHolder = eventTicket.getHolderUserId();
                updateTicketAttributesForTicketTransfer(eventTicket, userBasicDto,ticketHolderAttributes,ticketHolderAttributesListToSave,true);
                log.info("transferTicketOnPurchaserDataUpdate ticketId: {} , Old barcodeId: {} and ticketCode: {}", eventTicket.getId(), eventTicket.getBarcodeId(), eventTicket.getTicketCode());
                eventTicket.setBarcodeId(UUID.randomUUID().toString());
                eventTicket.setTicketCode(CommonUtil.generateUniqueCode());
                eventTicket.setTransfersCount(eventTicket.getTransfersCount()+1);
                transferTickets.add(eventTicket);
                userSessionService.updateUserSessionUserId(event, user, oldTicketHolder, eventTicket.getId());
            }
            eventTicket.setTicketPurchaserId(user);
            updateTicketAttributesForTicketTransfer(eventTicket, userBasicDto,ticketHolderAttributes,ticketHolderAttributesListToSave,false);
        }
        log.info("Going to save eventTickets size: {} and ticketHolderAttributesListToSave size: {}",eventTickets.size(),ticketHolderAttributesListToSave.size());
        ticketHolderAttributesService.saveAll(ticketHolderAttributesListToSave);
        eventTicketsRepoService.saveAll(eventTickets);
        order.setPurchaser(user);
        ticketingOrderService.save(order);
        if (!transferTickets.isEmpty()) {
            if(ticketing.getUniqueTicketHolderEmail()) {
                validateUniqueEmailForBuyerTicketTransfer(transferTickets, event);
            }
            orderAuditLog(event, loggedInUser, orderId, AUDIT_MESSAGE_WHEN_TICKET_TRANSFERRED_BY_BUYER, true);
            boolean isEnableOrderConfirmationEmail = ticketingHelperService.isEnableOrderConfirmationEmail(event.getEventId());
            if (isEnableOrderConfirmationEmail){
                this.sendEmailForTransferTicket(transferTickets.get(0).getTicketingOrder(), event, transferTickets);
            }
        } else {
            orderAuditLog(event, loggedInUser, orderId, TICKET_BUYER, false);
        }
        afterTaskIntegrationTriggerService.ticketInfoUpdatePostProcess(eventTickets, order.getEventid(), order);
    }

    void validateNonTransferableTickets(List<EventTickets> eventTickets, User loggedInUser, Event event, boolean disableTicketTransfer) {
        if (!roStaffService.hasStaffAccessForEvent(loggedInUser, event)) {
            for (EventTickets eventTicket : eventTickets) {
                if (!eventTicket.getGuestOfBuyer()) {
                    if (disableTicketTransfer) {
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_ORDER_IS_DISABLE_FOR_TRANSFER);
                    }

                    TicketingType ticketType = eventTicket.getTicketingTypeId();
                    if (ticketType.isNonTransferable()) {
                        NotAcceptableException.TicketingExceptionMsg error = NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_TICKET_TYPE_NON_TRANSFERABLE;
                        String ticketTypeName = ticketType.getTicketTypeName();
                        String errorMessage = TICKET_TRANSFER_NOT_POSSIBLE_TICKET_TYPE_NON_TRANSFERABLE.replace("${ticketType}", ticketTypeName);
                        error.setErrorMessage(errorMessage);
                        error.setDeveloperMessage(errorMessage);
                        throw new NotAcceptableException(error);
                    }
                }
            }
        }
    }


    private void transferTicketOnHolderDataUpdate(UserBasicDto userBasicDto, List<Long> ticketIds, User loggedInUser) {
        List<EventTickets> eventTickets = eventCommonRepoService.findAllByIdAndHolderUserIdOrPurchaserUserId(ticketIds, loggedInUser.getUserId());
        if (!eventTickets.isEmpty())
        {
            Event event = eventTickets.get(0).getEvent();
            TicketingOrder ticketingOrder = eventTickets.get(0).getTicketingOrder();
            validateNonTransferableTickets(eventTickets, loggedInUser, event, ticketingOrder.isDisableTicketTransfer());
            Ticketing ticketing = ticketingHelperService.findTicketingByEventIdOrThrowError(event);
            if(ticketing.getUniqueTicketHolderEmail()){
                if(eventTickets.size()>1){
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.PROVIDED_HOLDER_EMAIL_ALREADY_HAS_TICKET);
                }else{
                    Set<String> existingEmails = eventCommonRepoService.findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndIdNotInAndTicketStatusNotIn(
                            event.getEventId(), Collections.singletonList(userBasicDto.getEmail()), List.of(eventTickets.get(0).getId()));
                    if (!existingEmails.isEmpty()) {
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.PROVIDED_HOLDER_EMAIL_ALREADY_HAS_TICKET);
                    }
                }

            }
            EventPlanConfig planConfig = eventPlanConfigService.findByEventId(event.getEventId());
            RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(planConfig.getRegistrationConfigJson());
            eventTickets.forEach(eventTicket -> {
                if (checkEventIsPast(ticketing)) {
                    throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_FOR_PAST_EVENT);
                } else if (eventTicket.getTransfersCount() >= registrationConfigDto.getMaxTicketTransferLimit()) {
                    throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_LIMIT_REACHED);
                }
                if (!TicketStatus.CHECKED_IN.equals(eventTicket.getTicketStatus())) {
                    if (Boolean.TRUE.equals(ticketing.getUniqueTicketHolderEmail())) {
                        log.info("new holder email : {}", userBasicDto.getEmail());
                        if (!StringUtils.isBlank(userBasicDto.getEmail()) && !userBasicDto.getEmail().equals(eventTicket.getHolderEmail())) {
                            User user = roUserService.findByEmail(userBasicDto.getEmail());
                            if (user != null) {
                                List<Long> eventTicketsCountOfUser = eventTicketsRepository.findByHolderUserIdAndEventId(user.getUserId(), event.getEventId(), Arrays.asList(TicketStatus.CANCELED, TicketStatus.DELETED));
                                if (!eventTicketsCountOfUser.isEmpty()) {
                                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.PROVIDED_HOLDER_EMAIL_ALREADY_HAS_TICKET);
                                }
                            }
                        }
                    }
                    String previousEmailForIntegration = eventTicket.getHolderEmail();
                    TicketHolderAttributes ticketHolderAttributes = eventTicket.getTicketHolderAttributesId();
                    updateTicketHolderDataForTicketTransfer(eventTicket, userBasicDto, loggedInUser, ticketHolderAttributes);
                    boolean isEnableOrderConfirmationEmail = ticketingHelperService.isEnableOrderConfirmationEmail(event.getEventId());
                    if (isEnableOrderConfirmationEmail){
                        this.sendEmailForTransferTicket(eventTicket.getTicketingOrder(), event, Collections.singletonList(eventTicket));
                    }
                    attendeeProfileService.updateUserDetailOnSQLAndNeptune(eventTicket, loggedInUser.getUserId(), ticketHolderAttributes);
                    trayIntegrationService.triggerTrayWebHookForUpdateHolder(eventTicket.getTicketingOrder().getEventid(), eventTicket, previousEmailForIntegration);
                    afterTaskIntegrationTriggerService.ticketInfoUpdatePostProcess(Collections.singletonList(eventTicket), eventTicket.getTicketingOrder().getEventid(), eventTicket.getTicketingOrder());

                } else {
                    throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_FOR_CHECK_IN);
                }
            });
        }
    }

    public void updateTicketAttributesForTicketTransfer(EventTickets eventTickets, UserBasicDto userBasicDto, TicketHolderAttributes ticketHolderAttributes, List<TicketHolderAttributes> ticketHolderAttributesListToSave, boolean isHolderUpdate) {
        log.info("updateTicketAttributesForTicketTransfer eventTicketsId: {} , newEmail: {} , isisHolderUpdate: {}", eventTickets.getId(), userBasicDto.getEmail(), isHolderUpdate);
        TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());

        Map<String, String> attributes = isHolderUpdate
                ? (Map<String, String>) ticketAttributeValueDto.getHolder().get(TICKETING.ATTRIBUTES)
                : (Map<String, String>) ticketAttributeValueDto.getPurchaser().get(TICKETING.ATTRIBUTES);
        log.info("attributes : {}", attributes);
        if (attributes != null) {
            updateAttributeByUserBasicDTO(attributes, userBasicDto);
        } else {
            attributes = new HashMap<>();
            attributes.put(FIRST_NAME, userBasicDto.getFirstName());
            attributes.put(LAST_NAME, userBasicDto.getLastName());
            attributes.put(EMAIL, userBasicDto.getEmail());
            if (isHolderUpdate) {
                ticketAttributeValueDto.setHolder(attributes);
            } else {
                ticketAttributeValueDto.setPurchaser(attributes);
            }
        }
        if (isHolderUpdate) {
            TicketHolderHelper.setTicketAttributeValueDto(ticketAttributeValueDto, attributes, null);
        } else {
            TicketHolderHelper.setTicketAttributeValueDto(ticketAttributeValueDto, null, attributes);
        }

        String ticketAttributeValueDtoStr = ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto);
        ticketHolderAttributes.setJsonValue(ticketAttributeValueDtoStr);
        ticketHolderAttributesListToSave.add(ticketHolderAttributes);

        if (isHolderUpdate) {
            addDetailsToEventTickets(eventTickets.getEvent(), eventTickets, attributes);
        }

    }
    private void updateAttributeByUserBasicDTO(Map<String, String> attributes,UserBasicDto userBasicDto){
        attributes.forEach((key, value) -> {
            switch (key) {
                case FIRST_NAME:
                    attributes.put(key, userBasicDto.getFirstName());
                    break;
                case LAST_NAME:
                    attributes.put(key, userBasicDto.getLastName());
                    break;
                case EMAIL:
                    attributes.put(key, userBasicDto.getEmail());
                    break;
                default:
                    break;
            }
        });
    }
    private void sendEmailForTransferTicket(TicketingOrder ticketingOrder, Event event, List<EventTickets> eventTickets) {
        log.info("sendEmailForTransferTicket ticket transfer for  eventId: {} , orderId: {} , eventTicket size: {}", event.getEventId(), ticketingOrder.getId(), eventTickets.size());
        try {
            ticketingEmailService.sendEmailForTransferTicket(ticketingOrder, event, eventTickets, true);
        } catch (Exception ex) {
            log.error("Error while Sending order mail for eventId: {} , orderId: {} , errorMsg: {} ", event.getEventId(), ticketingOrder.getId(),ex.getMessage());
        }
    }

    private boolean checkLoggedInUserHaveAccessToHolderData(User loggedInUser, Event event, boolean isExhibitorLeadOrAboveRole) {
        if (isExhibitorLeadOrAboveRole) {
            return true;
        } else {
            return eventTicketsRepoService.userAlreadyPurchasedTicketInEvent(event, loggedInUser);
        }
    }

    private Map<Long, TicketAttributeValueDto1> getTicketHolderAttributesValueDtoByEventTicketIds(List<Long> eventTicketIds) {
        List<TicketHolderAttributeDto> ticketHolderAttributes = ticketHolderAttributesService.findByEventTicketIds(eventTicketIds);
        return ticketHolderAttributes.stream()
                .collect(Collectors.toMap(
                        TicketHolderAttributeDto::getEventTicketId,
                        e -> TicketHolderAttributesHelper.parseJsonToObject(e.getJsonValue()),
                        (existingValue, newValue) -> existingValue));
    }

    private void setReadOnlyHolderData(EventTickets eventTicket, List<DisplayAttributeDto> holderAttributesDto, List<DisplayAttributeDto> holderQuestionDto, List<DisplayNestedQueDto> displayNestedQueDtoList, TicketAttributeValueDto1 ticketAttributeValueDto, boolean isAddon, TicketHolderRequiredAttributes holderRequiredAttribute) {

        DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();

        displayAttributeDto.setId(holderRequiredAttribute.getId());
        displayAttributeDto.setName(holderRequiredAttribute.getName());
        displayAttributeDto.setType(holderRequiredAttribute.getAttributeValueType().getType());
        displayAttributeDto.setEventTicketTypeId(holderRequiredAttribute.getHolderEventTicketTypeId());
        displayAttributeDto.setRequiredTicketTypeId(holderRequiredAttribute.getHolderRequiredTicketTypeId());
        displayAttributeDto.setOptionalTicketTypeId(holderRequiredAttribute.getHolderOptionalTicketTypeId());
        displayAttributeDto.setHiddenTicketTypeId(holderRequiredAttribute.getHolderRegistrationHiddenTicketTypeId());
        displayAttributeDto.setDefaultValue(holderRequiredAttribute.getDefaultValueJsonHolder());
        displayAttributeDto.setIsHidden(holderRequiredAttribute.isInvisibleForHolder());
        displayAttributeDto.setMandatory(isAddon ? (isAttributeFirstnameLastnameOrEmail(holderRequiredAttribute.getName()) ||
                                                    isAttributeMandatoryForTicketingType(eventTicket.getTicketingTypeOnlyId(), holderRequiredAttribute.getBuyerRequiredTicketTypeId()))
                                                 : isAttributeMandatoryForTicketingType(eventTicket.getTicketingTypeOnlyId(),holderRequiredAttribute.getHolderRequiredTicketTypeId()));
        displayAttributeDto.setPosition(holderRequiredAttribute.getBuyerAttributeOrder());

        if (Constants.STRING_CELL_SPACE_PHONE.equals(displayAttributeDto.getName())) {
            updateCellPhone(eventTicket, displayAttributeDto);
        } else if (displayAttributeDto.getType().equals(AttributeValueType.BILLING_ADDRESS.getType()) ||
                displayAttributeDto.getType().equals(AttributeValueType.SHIPPING_ADDRESS.getType())) {
            updateBillingAndShippingAddress(ticketAttributeValueDto, holderRequiredAttribute, displayAttributeDto);
        } else {
            String value = getTicketAttributeValue(eventTicket, ticketAttributeValueDto, holderRequiredAttribute);
            displayAttributeDto.setValue(value);
        }
        // Check is question or attribute
        if (holderRequiredAttribute.isAttribute()) {
            setHolderAttributesData(holderAttributesDto, displayNestedQueDtoList, holderRequiredAttribute, displayAttributeDto,false);
        } else {
            holderQuestionDto.add(displayAttributeDto);
        }
    }

    private void validateTicketTransferForCheckedInUsers(Event event, List<EventTickets> eventTickets) {
        boolean isCollectHolderData = ticketingHelperService.isCollectTicketHolderAttributesByEvent(event);
        if (!isCollectHolderData) {
            eventTickets.forEach(eventTicket -> {
                if (TicketStatus.CHECKED_IN.equals(eventTicket.getTicketStatus())) {
                    throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.TICKET_TRANSFER_NOT_POSSIBLE_FOR_CHECK_IN);
                }
            });
        }
    }

    @Override
    public List<KeyValueLongBooleanDto> getTicketsTransferLimit(List<Long> ticketIds, Event event, User user) {
        log.info("getTicketsTransferLimit eventId: {}, userId: {}, ticketIds: {}", event.getEventId(), user.getUserId(), ticketIds);

        List<KeyValueLongBooleanDto> transferLimitDtoList = new ArrayList<>();
        Ticketing ticketing = ticketingHelperService.findByEventId(event.getEventId());
        if (checkEventIsPast(ticketing)) {
            ticketIds.forEach(ticketId -> transferLimitDtoList.add(new KeyValueLongBooleanDto(ticketId, false)));
        } else {
            EventPlanConfig planConfig = eventPlanConfigService.findByEventId(event.getEventId());
            RegistrationConfigDto registrationConfigDto = RegistrationConfigDto.convertJSONToObject(planConfig.getRegistrationConfigJson());
            long maxTransferCount = Optional.ofNullable(registrationConfigDto)
                    .map(RegistrationConfigDto::getMaxTicketTransferLimit)
                    .orElse(MAX_DEFAULT_TICKET_TRANSFER_LIMIT);
            List<KeyValueLongBooleanDto> transferLimitDetails = eventTicketsService.getTransferLimitDetails(ticketIds, event.getEventId(), maxTransferCount);
            log.info("Transfer Limit Details: {} , maxTransferCount: {}", transferLimitDetails, maxTransferCount);
            if (transferLimitDetails.isEmpty()) {
                log.info("No transfer limit details found.");
                return Collections.emptyList();
            }
            transferLimitDetails.forEach(detail -> {
                transferLimitDtoList.add(new KeyValueLongBooleanDto(detail.getKey(), detail.isValue()));
            });
        }

        return transferLimitDtoList;
    }

    protected boolean checkEventIsPast(Ticketing ticketing) {
        return  null!=ticketing.getEventEndDate() && new Date().after(ticketing.getEventEndDate());
    }

    @Override
    public List<Long> getAdditionalHiddenOrderInfoRequired(Event event, User loggedInUser) {
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = ticketRequiresAttributesRepo.findByEventIdAndHiddenHolderRegistrationTrue(event);
        List<String> hiddenFieldNames = getHiddenFieldNames(ticketHolderRequiredAttributes);
        List<Long> ticketTypeIds = getTicketTypeId(ticketHolderRequiredAttributes);
        Set<Long> ticketingOrderIdsSet = new HashSet<>();
        if (!hiddenFieldNames.isEmpty() && !ticketTypeIds.isEmpty()) {
            List<EventTickets> userTickets = eventTicketsService.findAllEventTicketsByHolderUserIdAndEventId(loggedInUser, event);
            userTickets.forEach(eventTicket -> {
                TicketHolderAttributes ticketHolderAttribute = eventTicket.getTicketHolderAttributesId();
                TicketAttributeValueDto1 ticketAttributeValueDto1 = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttribute.getJsonValue());
                Map<String, String> holderAttributesMap = (Map<String, String>) ticketAttributeValueDto1.getHolder().get(TICKETING.ATTRIBUTES);
                List<Map<String, Object>> holderNestedAttributeList = (List<Map<String, Object>>) ticketAttributeValueDto1.getHolder().get(Constants.TICKETING.NESTEDQUESTIONS);
                Map<String ,String> holderNestedAttributesMap =holderNestedAttributeNameValueMap(holderNestedAttributeList);
                hiddenFieldNames.forEach(fieldName -> {
                    if (ticketTypeIds.contains(eventTicket.getTicketingTypeOnlyId()) &&
                            ((holderAttributesMap != null && ((holderAttributesMap.get(fieldName) == null || STRING_EMPTY.equals(holderAttributesMap.get(fieldName)))))
                                    && ((holderNestedAttributesMap != null && (holderNestedAttributesMap.get(fieldName) == null || STRING_EMPTY.equals(holderNestedAttributesMap.get(fieldName))))))) {
                        ticketingOrderIdsSet.add(eventTicket.getTicketingOrderId());
                    }
                });
            });
        }
        return new ArrayList<>(ticketingOrderIdsSet);
    }

    public Map<String, String> holderNestedAttributeNameValueMap(List<Map<String, Object>> holderNestedAttributeList) {
        Map<String, String> holderNestedAttributesMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(holderNestedAttributeList)) {
            for (Map<String, Object> nestedAttribute : holderNestedAttributeList) {
                String nestedAttributeName = (String) nestedAttribute.get("name");
                String nestedAttributeValue = (String) nestedAttribute.get("value");
                if (nestedAttributeName != null) {
                    holderNestedAttributesMap.put(nestedAttributeName, nestedAttributeValue);
                }
            }
            return holderNestedAttributesMap;
        }
        return holderNestedAttributesMap;
    }

    private List<String> getHiddenFieldNames(List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes) {
        List<String> fieldNames = new ArrayList<>();
        Map<String, String> fieldNameMap = new HashMap<>();
        fieldNameMap.put(CELL_PHONE, PHONE_NUMBER_KEY);
        ticketHolderRequiredAttributes.forEach(attributeName -> {
            if (fieldNameMap.containsKey(attributeName.getName())) {
                fieldNames.add(fieldNameMap.get(attributeName.getName()));
            } else {
                fieldNames.add(attributeName.getName());
            }
        });
        return fieldNames;
    }

    private List<Long> getTicketTypeId(List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes) {
        return ticketHolderRequiredAttributes.stream()
                .map(TicketHolderRequiredAttributes::getHolderRegistrationHiddenTicketTypeId)
                .filter(Objects::nonNull)
                .flatMap(ids -> Arrays.stream(ids.split(","))
                        .map(String::trim)
                        .filter(id -> !id.isEmpty())
                        .map(Long::valueOf)).distinct().collect(Collectors.toList());
    }
}
