package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.badges.Badges;
import com.accelevents.badges.JoinBadgeTicketTypes;
import com.accelevents.billing.chargebee.repo.ChargesPurchasedRepoService;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.CreditCardChargesDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.ResponseDtoSaveTicketType;
import com.accelevents.dto.SeatingCategoryDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.registration.approval.domain.RegistrationAttribute;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.repositories.*;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.services.*;
import com.accelevents.services.elasticsearch.leaderboard.LeaderboardService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.dto.IdNameDto;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.TicketingTypeTagAndTrackService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.TimeZone;
import com.accelevents.utils.*;
import com.accelevents.virtualevents.dto.VirtualEventTabsDTO;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import seatsio.charts.Category;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.accelevents.domain.TicketingType.RecurringEventSalesEndStatus.END;
import static com.accelevents.domain.TicketingType.RecurringEventSalesEndStatus.START;
import static com.accelevents.domain.enums.DataType.ADDON;
import static com.accelevents.domain.enums.DataType.TICKET;
import static com.accelevents.domain.enums.EventFormat.*;
import static com.accelevents.enums.PlanConfigNames.*;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.*;
import static com.accelevents.exceptions.NotAcceptableException.TicketingExceptionMsg.*;
import static com.accelevents.messages.EnumEventVenue.ONLINE_VIRTUAL_EVENT;
import static com.accelevents.messages.TicketBundleType.INDIVIDUAL_TICKET;
import static com.accelevents.messages.TicketType.PAID;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.*;
import static com.accelevents.utils.NumberUtils.isNumberGreaterThanZero;
import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static java.util.Collections.EMPTY_SET;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.logging.log4j.util.Strings.isBlank;

@Service
public class TicketingManageServiceImpl implements TicketingManageService {

    private static final Logger log = LoggerFactory.getLogger(TicketingManageServiceImpl.class);

    @Autowired
    private TicketingService ticketingService;
    @Autowired
    private VatTaxService vatTaxService;
    @Autowired
    @Lazy
    private TicketingTypeService ticketingTypeService;
    @Autowired
    private TicketingTypeTicketService ticketingTypeTicketService;
    @Autowired
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Autowired
    private  EventLimitRegistrationDomainService eventLimitRegistrationDomainService ;
    @Autowired
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;
    @Autowired
    private SeatsIoService seatsIoService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Autowired
    private AllRequiresAttributesService allRequiresAttributesService;
    @Autowired
    private TicketingTypeRepository ticketingTypeRepository;

    @Autowired
    private TicketingCouponService ticketingCouponService;
    @Autowired
    private TicketingCouponRepository ticketingCouponRepository;
    @Autowired
    private TicketingAccessCodeService ticketingAccessCodeService;
    @Autowired
    private TicketingAccessCodeRepository ticketingAccessCodeRepository;
    @Autowired
    private EventTicketsRepoService eventTicketsRepoService;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;
    @Autowired
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Autowired
    private TicketingRepository ticketingRepository;
    @Autowired
    private StripeService stripeService;
    @Autowired
    private TicketingStatisticsService ticketingStatisticsService;
    @Autowired
    private TicketingHelperService ticketingHelperService;
    @Autowired
    private SeatingCategoryRepository seatingCategoryRepository;
    @Autowired
    private SeatingCategoryService seatingCategoryService;
    @Autowired
    private SeatsIoForMultipleEventsImpl seatsIoForMultipleEventsImpl;
    @Autowired
    private EventService eventService;
    @Autowired
    private RecurringEventsRepository recurringEventsRepository;
    @Autowired
    private ResendTicketingEmailService resendTicketingEmailService;
    @Autowired
    private SessionService sessionService;
    @Autowired
    private ChallengeConfigService challengeConfigService;
    @Autowired
    private LeaderboardService leaderboardService;
    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private ChargebeeService chargebeeService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Autowired
    private TicketingTypeTagAndTrackService ticketingTypeTagAndTrackService;
    @Autowired
    private BadgesService badgesService;
    @Autowired
    private PayFlowConfigServiceImpl payFlowConfigService;
    @Autowired
    private RegistrationAttributeRepository registrationAttributeRepository;
    @Autowired
    private IntegrationService integrationService;
    @Autowired
    private TrayIntegrationService trayIntegrationService;
    @Autowired
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Autowired
    private SessionRepoService sessionRepoService;
    @Autowired
    private ConfirmationPagesRepository confirmationPagesRepository;
    @Autowired
    private ConfirmationEmailRepository  confirmationEmailRepository;
    @Autowired
    private TicketingLimitedDisplayCodeService ticketingLimitedDisplayCodeService;
    @Autowired
    private ChargebeePlanService chargebeePlanService;
    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private ROVirtualEventService roVirtualEventService;
    @Autowired
    private VirtualEventService virtualEventService;
    @Autowired
    private JoinBadgeTicketTypesService joinBadgeTicketTypesService;

    @Autowired
    private TicketTypeTrackSessionLimitsService ticketTypeSessionRegistrationRestrictionService;

    Random random;

    @Override
    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    public ResponseDtoSaveTicketType saveTicketType(TicketTypeSettingDto ticketTypeDto, Event event, User user) {
        log.info("saveTicketType ticketTypeDto {}", ticketTypeDto);
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        log.info("Request received to save ticket type for event {} by user {}| paylater status {}", event.getEventId(), user.getUserId(), ticketTypeDto.isPayLater());

        TicketingType ticketingTypeFromDb = null;
        if (isNumberGreaterThanZero(ticketTypeDto.getTypeId())) {
            ticketingTypeFromDb = ticketingTypeService.findByidAndEvent(ticketTypeDto.getTypeId(), event);
        } else {
            if (!IN_PERSON.equals(event.getEventFormat())) {
                List<Long> noOfCurrentTicketType = ticketingTypeService.findAllByEventIdNumberOfTickets(event);
                chargebeeService.validateTicketTypesByEventPlan(event, (long) noOfCurrentTicketType.size());
            }
        }

        if (ticketingTypeFromDb != null) {
            if (isCategoryIdNullForAddOn(ticketTypeDto) || isCategoryIdNullForSeatingTicket(ticketTypeDto, ticketingTypeFromDb)) {
                throw new NotAcceptableException(CATEGORY_REQUIRED);
            }
        }

        boolean isNewTicket = (ticketingTypeFromDb == null);

        if (null == ticketTypeDto.getRecurringEventId()) {
            int existByName = ticketingTypeRepository.countByNameAndEvent(ticketTypeDto.getName(), ticketTypeDto.getDataType(), event);

            if (ADDON.equals(ticketTypeDto.getDataType()) && null != ticketTypeDto.getCategoryId()) {
                existByName = ticketingTypeRepository.countByNameAndEventAndCategoryId(ticketTypeDto.getName(), ticketTypeDto.getDataType(), event, ticketTypeDto.getCategoryId());
            }

            if (((isNewTicket || !ticketTypeDto.getName().equalsIgnoreCase(ticketingTypeFromDb.getTicketTypeName())) && existByName > 0) || (!isNewTicket && existByName > 1)) {
                throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.SAME_TICKET_NAME_IS_NOT_ALLOWED);
            }
        }

        if (ticketTypeDto.getPrice() <= 0d && TicketType.PAID.equals(ticketTypeDto.getTicketType()) && DataType.TICKET.equals(ticketTypeDto.getDataType())) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.PAID_TICKET_PRICE_SHOULD_BE_GREATER_THEN_ZERO);
        }
        ticketingTypeFromDb = copyTicket(event, ticketing, ticketTypeDto, ticketingTypeFromDb);

        if (Boolean.TRUE.equals(ticketTypeDto.isAllowAssignedSeating())) {
            if (VIRTUAL.equals(event.getEventFormat()) || TicketTypeFormat.VIRTUAL.equals(ticketTypeDto.getTicketTypeFormat())) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.SEATING_FEATURE_NOT_ALLOWED_ON_VIRTUAL_EVENT);
            }
            if (DataType.TICKET.equals(ticketTypeDto.getDataType()) && EnumSet.of(TicketType.PAID, TicketType.FREE).contains(ticketTypeDto.getTicketType())) {

                EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());

                if (eventPlanConfig != null && eventPlanConfig.getPlanConfigId().equals(FREE_PLAN_ID_CONSTANT) && ticketTypeDto.getTicketType().equals(TicketType.FREE)) {
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.SEATING_FEATURE_NOT_ALLOWED_ON_FREE_PLAN);
                } else {
                    ticketingTypeFromDb.setAllowAssignedSeating(ticketTypeDto.isAllowAssignedSeating());
                }
            } else {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.SEATING_FEATURE_NOT_ALLOWED_FOR_TICKET);
            }
        } else {
            ticketingTypeFromDb.setAllowAssignedSeating(false);
        }
        if (!ticketing.isRecurringEvent()) {
            setConfirmationPageIdForTicketType(ticketTypeDto, event, ticketingTypeFromDb);
            setConfirmationEmailForTicketType(ticketTypeDto, event, ticketingTypeFromDb,TemplateType.CONFIRMATION_EMAIL);
            setCustomPdfAndInvoiceDesignsForTicketType(ticketTypeDto, event, ticketingTypeFromDb);
        } else {
            setConfirmationPageIdForTicketTypeForRecurringEvents(ticketTypeDto, event, ticketingTypeFromDb);
            setConfirmationEmailForTicketTypeForRecurringEvents(ticketTypeDto, event, ticketingTypeFromDb,TemplateType.CONFIRMATION_EMAIL);
            setCustomPdfAndInvoiceDesignsForTicketTypeForRecurringEvents(ticketTypeDto, event, ticketingTypeFromDb);
        }

        if((isNewTicket && ticketTypeDto.isEnableTicketLevelVatTax()) || (!ticketingTypeFromDb.isEnableTicketLevelVatTax() && ticketTypeDto.isEnableTicketLevelVatTax()) ){
            validateTicketExchangeIsAllowed(ticketing);
        }

        // set non transferable for ticket type
        setNonTransferableForTicketType(ticketTypeDto, ticketingTypeFromDb);
        ticketingTypeFromDb.setEnableTicketLevelVatTax(ticketTypeDto.isEnableTicketLevelVatTax());

        if (isNewTicket || ticketingTypeFromDb.isEnableTicketLevelVatTax()) {
            ticketingTypeFromDb.setVatTaxPercentage(
                    ticketingTypeFromDb.isEnableTicketLevelVatTax() ? ticketTypeDto.getVatTaxPercentage() : vatTaxService.getVatTaxRate(event.getEventId())
            );
        }

        TicketingType ticketingType = ticketingTypeService.setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeFromDb);

        List<TicketingType> ticketingTypesList = ticketingTypeRepository.findAllByEventIdRecurringIdNull(event);
        boolean seatingAllowed = ticketingTypesList.stream().anyMatch(e -> Boolean.TRUE.equals(e.isAllowAssignedSeating()));
        List<Category> categories = createCategoryForDefaultTicketTypeIfCategoryNotPresent(event, ticketing.isRecurringEvent());
        updateSeatsIOData(ticketTypeDto, event, ticketing, ticketingTypeFromDb, isNewTicket, seatingAllowed, categories);
        if (!seatingAllowed) {
            ticketing.setChartKey(null);
            ticketingService.save(ticketing);
            ticketingTypeSeatingFlagChange(event);
        }
        handleTagsAndTracks(ticketingType.getId(), ticketTypeDto);
        handleSessionRegistrationRestriction(ticketingType,ticketTypeDto,user,event);
        List<Long> ticketingTypeIds = ticketingTypeService.findAllTypeIdByEvent(event, TICKET);
        if (isNewTicket) {
            updateHolderRequiredAttributesForNewTicket(event, ticketingType);
            if (TICKET.equals(ticketingType.getDataType())) {
                sessionService.updateSessionTicketTypes(ticketingType, ticketingTypeIds, event, true);
            }
            challengeConfigService.updateTicketTypeInChallengeOnInsert(ticketingType, ticketingTypeIds, event);
        } else {
            if (TICKET.equals(ticketingType.getDataType())) {
                sessionService.updateSessionTicketTypes(ticketingType, ticketingTypeIds, event, false);
            }
        }
        if (ticketing.isRecurringEvent()) {
            handleActionOnAddUpdateDefaultTicketTypeInRecurring(ticketTypeDto, event, ticketing, isNewTicket, ticketingType);
        }
        if (isNewTicket && TICKET.equals(ticketingType.getDataType())) {
            updateAddOnTicketTypesData(ticketingType,event, ADD, ticketing);
            updateCustomLobbyTabData(ticketingType,event,ADD);
            setDefaultReminderEmailForTicketType(event, ticketingType,TemplateType.REMINDER_EMAIL);
        }

        if(isNewTicket && ticketing.isLimitRegistrationEmails() && TICKET.equals(ticketingType.getDataType())){
            eventLimitRegistrationDomainService.updateTicketTypeInEventLimitRegistrationDomain(event, ticketingType, ticketing);
        }
        activateTicketingForFreeTicketOrDeactiveForPaidIfStripeIsNotConnected(ticketing, ticketingType, event);

        // Update ticket added checklist flag
        ticketingService.updateTicketAddedChecklistFlag(event, true);

        if (PAID.equals(ticketTypeDto.getTicketType()) && ticketTypeDto.getPrice().equals(0.0)) {
            throw new NotAcceptableException(TICKET_PRICE_SHOULD_NOT_BE_ZERO);
        }
        if (ticketTypeDto.getBadgeId() != null) {
            if (TICKET.equals(ticketingType.getDataType()) && !ticketingType.getTicketType().equals(TicketType.DONATION)) {
                associateTicketTypeWithBadge(ticketingType.getId(), ticketTypeDto.getBadgeId());
            } else {
                throw new NotAcceptableException(BADGE_NOT_ALLOWED_FOR_DONATION_OR_ADD_ON_TICKETS);
            }
        }
        log.info("Successfully saved ticket type for event {} by user {}| paylater status {}", event.getEventId(), user.getUserId(), ticketTypeDto.isPayLater());
        mapTicketTypes(event, ticketingType, (isNewTicket) ? TrayIOWebhookActionType.CREATE_TICKET_TYPES : TrayIOWebhookActionType.UPDATE_TICKET_TYPES);
        return new ResponseDtoSaveTicketType(SUCCESS, SUCCESS, ticketTypeDto.getTypeId(), ticketingType.getId());
    }

    private void handleSessionRegistrationRestriction(TicketingType ticketingType, TicketTypeSettingDto ticketTypeDto, User loggedInUser, Event event) {
        ticketTypeSessionRegistrationRestrictionService.handleSessionRegistrationRestriction(ticketingType, ticketTypeDto, loggedInUser, event);
    }

    private void setNonTransferableForTicketType(TicketTypeSettingDto ticketTypeDto, TicketingType ticketingType) {
        if (ticketTypeDto.isNonTransferable() != ticketingType.isNonTransferable()) {
            ticketingType.setNonTransferable(ticketTypeDto.isNonTransferable());
        }
    }

    private void setDefaultReminderEmailForTicketType(Event event, TicketingType ticketingType, TemplateType templateType) {
        CustomTemplates customTemplates = confirmationEmailRepository.findByEventAndIsDefaultEmail(event, true, templateType);
        if (customTemplates!=null) {
            List<Long> ticketingTypesIds=GeneralUtils.convertCommaSeparatedToListLong(customTemplates.getAllowedTicketTypesForReminder());
            if (!ticketingTypesIds.contains(ticketingType.getId())) {
                ticketingTypesIds.add(ticketingType.getId());
                customTemplates.setAllowedTicketTypesForReminder(GeneralUtils.convertLongListToCommaSeparated(ticketingTypesIds));
                confirmationEmailRepository.save(customTemplates);
            }
        }
    }

    private void setCustomPdfAndInvoiceDesignsForTicketTypeForRecurringEvents(TicketTypeSettingDto ticketTypeDto, Event event, TicketingType ticketingTypeFromDb) {
        Long customTicketPdfDesignId = 0L;
        Long customInvoicePdfDesignId = 0L;

        if (!ADDON.equals(ticketTypeDto.getDataType())) {
            if (ticketTypeDto.getCustomTicketPdfDesignId() != 0) {
                customTicketPdfDesignId = ticketTypeDto.getCustomTicketPdfDesignId();
            } else {
                CustomTemplates customTicketTemplate = confirmationPagesRepository.findByEventAndIsDefaultPageAndTemplateType(event, true, TemplateType.TICKET_PDF_DESIGN);
                if (customTicketTemplate != null) {
                    customTicketPdfDesignId = customTicketTemplate.getId();
                }
            }

            if (ticketTypeDto.getCustomInvoicePdfDesignId() != 0) {
                customInvoicePdfDesignId = ticketTypeDto.getCustomInvoicePdfDesignId();
            } else {
                CustomTemplates customInvoiceTemplate = confirmationPagesRepository.findByEventAndIsDefaultPageAndTemplateType(event, true, TemplateType.INVOICE_PDF_DESIGN);
                if (customInvoiceTemplate != null) {
                    customInvoicePdfDesignId = customInvoiceTemplate.getId();
                }
            }
        }

        if (ticketingTypeFromDb.getRecurringEventId() == null && ticketingTypeFromDb.getCreatedFrom() == null) {
            List<TicketingType> ticketTypesBelongToRecurring = ticketingTypeRepository.findByCreatedFrom(Collections.singletonList(ticketingTypeFromDb.getId()));
            if (!ticketTypesBelongToRecurring.isEmpty()) {
                for (TicketingType type : ticketTypesBelongToRecurring) {
                    type.setCustomTicketPdfDesignId(customTicketPdfDesignId);
                }
                if(!CollectionUtils.isEmpty(ticketTypesBelongToRecurring))
                ticketingTypeService.saveAll(ticketTypesBelongToRecurring);

                log.info("Saved all parent ticket types for recurring event {} and list of ticket types {}", event.getEventId(), ticketTypesBelongToRecurring.size());
            }
            ticketingTypeFromDb.setCustomTicketPdfDesignId(customTicketPdfDesignId);
           

        } else if (ticketingTypeFromDb.getCreatedFrom() != null &&
                !ticketingTypeFromDb.getCreatedFrom().equals(-1L) &&
                ticketingTypeFromDb.getRecurringEventId() != null) {

            List<Long> ticketTypeIds = Collections.singletonList(ticketingTypeFromDb.getCreatedFrom());
            List<TicketingType> ticketingTypeList = ticketingTypeRepository.findByIds(ticketTypeIds);
            List<TicketingType> ticketTypesBelongToRecurring = ticketingTypeRepository.findByCreatedFrom(ticketTypeIds);
            List<TicketingType> allTicketTypes = Stream.concat(ticketingTypeList.stream(), ticketTypesBelongToRecurring.stream()).collect(Collectors.toList());

            for (TicketingType type : allTicketTypes) {
                type.setCustomTicketPdfDesignId(customTicketPdfDesignId);
            }
            if(!CollectionUtils.isEmpty(allTicketTypes))
            ticketingTypeService.saveAll(allTicketTypes);

            log.info("Saved all child ticket types for recurring event {} and list of ticket types {}", event.getEventId(), allTicketTypes.size());

        } else if (ticketingTypeFromDb.getCreatedFrom().equals(-1L) && ticketingTypeFromDb.getRecurringEventId() != null) {
            ticketingTypeFromDb.setCustomTicketPdfDesignId(customTicketPdfDesignId); 
        }
    }


    private void setConfirmationPageIdForTicketTypeForRecurringEvents(TicketTypeSettingDto ticketTypeDto, Event event, TicketingType ticketingTypeFromDb) {
        Long customTemplateId = 0L;
        if (!ADDON.equals(ticketTypeDto.getDataType())) {
            if (ticketTypeDto.getCustomTemplateId() != 0) {
                customTemplateId = ticketTypeDto.getCustomTemplateId();
            } else {
                CustomTemplates customTemplates = confirmationPagesRepository.findByEventAndIsDefaultPage(event, true);
                if (customTemplates != null) {
                    customTemplateId = customTemplates.getId();
                }
            }
        }



        if (ticketingTypeFromDb.getRecurringEventId() == null && ticketingTypeFromDb.getCreatedFrom() == null) {
            List<TicketingType> ticketTypesBelongToRecurring = ticketingTypeRepository.findByCreatedFrom(Collections.singletonList(ticketingTypeFromDb.getId()));
            Long finalCustomTemplateId = customTemplateId;
            if (!ticketTypesBelongToRecurring.isEmpty()) {
                ticketTypesBelongToRecurring.forEach(e -> e.setCustomTemplateId(finalCustomTemplateId));
            }
            ticketingTypeFromDb.setCustomTemplateId(customTemplateId);
            ticketingTypeService.saveAll(ticketTypesBelongToRecurring);
            log.info("Saved all parent ticket type for recurring event {} and list of ticket types {}", event.getEventId(), ticketTypesBelongToRecurring.size());
        } // set template id for all child ticket types belong to recurring event
        else if (ticketingTypeFromDb.getCreatedFrom() != null && !ticketingTypeFromDb.getCreatedFrom().equals(-1L) && ticketingTypeFromDb.getRecurringEventId() != null) {
            List<Long> ticketTypeIds = Collections.singletonList(ticketingTypeFromDb.getCreatedFrom());
            List<TicketingType> ticketingTypeList = ticketingTypeRepository.findByIds(ticketTypeIds);
            List<TicketingType> ticketTypesBelongToRecurring = ticketingTypeRepository.findByCreatedFrom(ticketTypeIds);
            List<TicketingType> allTicketTypes = Stream.concat(ticketingTypeList.stream(), ticketTypesBelongToRecurring.stream()).collect(Collectors.toList());
            Long finalCustomTemplateId = customTemplateId;
            allTicketTypes.forEach(e -> e.setCustomTemplateId(finalCustomTemplateId));
            ticketingTypeService.saveAll(allTicketTypes);
            log.info("Saved all child ticket type for recurring event {} and list of ticket types {}", event.getEventId(), allTicketTypes.size());
        } // set template id for date specific child ticket type
        else if (ticketingTypeFromDb.getCreatedFrom().equals(-1L) && ticketingTypeFromDb.getRecurringEventId() != null) {
            ticketingTypeFromDb.setCustomTemplateId(customTemplateId);
        }
    }

    private void setConfirmationEmailForTicketTypeForRecurringEvents(TicketTypeSettingDto ticketTypeDto, Event event, TicketingType ticketingTypeFromDb,TemplateType templateType) {
        Long customConfirmationEmailId = 0L;
        if (ticketTypeDto.getConfirmationEmailId() != 0) {
            customConfirmationEmailId = ticketTypeDto.getConfirmationEmailId();
            log.info("set custom confirmation email id {} for recurring event ticket type {}", ticketTypeDto.getConfirmationEmailId(), ticketingTypeFromDb.getId());
        } else {
            Long customTemplatesId = confirmationEmailRepository.findCustomTemplatesIdByEventAndIsDefaultEmail(event, true,templateType);
            if (customTemplatesId != null) {
                customConfirmationEmailId = customTemplatesId;
                log.info("set default confirmation email id {} for recurring event ticket type {}", customTemplatesId, ticketingTypeFromDb.getId());
            }
        }

        if (ticketingTypeFromDb.getRecurringEventId() == null && ticketingTypeFromDb.getCreatedFrom() == null) {
            List<TicketingType> ticketTypesBelongToRecurring = ticketingTypeRepository.findByCreatedFrom(Collections.singletonList(ticketingTypeFromDb.getId()));
            Long finalCustomConfirmationEmailId = customConfirmationEmailId;
            if (!ticketTypesBelongToRecurring.isEmpty()) {
                ticketTypesBelongToRecurring.forEach(e -> e.setCustomConfirmationEmailId(finalCustomConfirmationEmailId));
            }
            ticketingTypeFromDb.setCustomConfirmationEmailId(customConfirmationEmailId);
            ticketingTypeService.saveAll(ticketTypesBelongToRecurring);
            List<Long> childTicketTypeIds = ticketTypesBelongToRecurring.stream().map(TicketingType::getId).collect(Collectors.toList());
            log.info("saved all parent ticket types associated with child ticket types for recurring event {} and parent ticket Id: {} and updated IDs: {}", event.getEventId(),ticketingTypeFromDb.getId(),childTicketTypeIds);
        } // set email template id for all child ticket types belong to recurring event
        else if (ticketingTypeFromDb.getCreatedFrom() != null && !ticketingTypeFromDb.getCreatedFrom().equals(-1L) && ticketingTypeFromDb.getRecurringEventId() != null) {
            List<Long> ticketTypeIds = Collections.singletonList(ticketingTypeFromDb.getCreatedFrom());
            List<TicketingType> ticketingTypeList = ticketingTypeRepository.findByIds(ticketTypeIds);
            List<TicketingType> ticketTypesBelongToRecurring = ticketingTypeRepository.findByCreatedFrom(ticketTypeIds);
            List<TicketingType> allTicketTypes = Stream.concat(ticketingTypeList.stream(), ticketTypesBelongToRecurring.stream()).collect(Collectors.toList());
            Long finalCustomTemplateId = customConfirmationEmailId;
            allTicketTypes.forEach(e -> e.setCustomConfirmationEmailId(finalCustomTemplateId));
            ticketingTypeService.saveAll(allTicketTypes);
            List<Long> allTicketTypesIds = allTicketTypes.stream().map(TicketingType::getId).collect(Collectors.toList());
            log.info("saved all child ticket type for recurring event {} and list of ticket types {}", event.getEventId(),allTicketTypesIds);
        } // set email template id for date specific child ticket type
        else if (ticketingTypeFromDb.getCreatedFrom().equals(-1L) && ticketingTypeFromDb.getRecurringEventId() != null) {
            ticketingTypeFromDb.setCustomConfirmationEmailId(customConfirmationEmailId);
            log.info("save custom confirmation email id {} for date specific child ticket type {} and recurring eventId {}", customConfirmationEmailId, ticketingTypeFromDb.getId(),event.getEventId());
        }

    }

    private void setConfirmationPageIdForTicketType(TicketTypeSettingDto ticketTypeDto, Event event, TicketingType ticketingTypeFromDb) {
        if (ticketTypeDto.getCustomTemplateId()!=0 && !ADDON.equals(ticketTypeDto.getDataType())) {
            ticketingTypeFromDb.setCustomTemplateId(ticketTypeDto.getCustomTemplateId());
            log.info("set custom confirmation page id {} for ticket type {}", ticketTypeDto.getConfirmationEmailId(), ticketingTypeFromDb.getId());
        } else if (!ADDON.equals(ticketTypeDto.getDataType())){
            CustomTemplates customTemplates = confirmationPagesRepository.findByEventAndIsDefaultPage(event, true);
            if (customTemplates != null) {
                ticketingTypeFromDb.setCustomTemplateId(customTemplates.getId());
                log.info("set default confirmation page id {} for ticket type {}", customTemplates.getId(), ticketingTypeFromDb.getId());
            } else {
                log.error("default confirmation page not found for event {}", event.getEventId());
            }
        }
    }

    private void setCustomPdfAndInvoiceDesignsForTicketType(TicketTypeSettingDto ticketTypeDto, Event event, TicketingType ticketingTypeFromDb) {
        if (!ADDON.equals(ticketTypeDto.getDataType())) {
            // Set Ticket PDF Design
            if (ticketTypeDto.getCustomTicketPdfDesignId() != 0) {
                ticketingTypeFromDb.setCustomTicketPdfDesignId(ticketTypeDto.getCustomTicketPdfDesignId());
                log.info("Set custom ticket PDF design ID {} for ticket type {}",
                        ticketTypeDto.getCustomTicketPdfDesignId(), ticketingTypeFromDb.getId());
            } else {
                CustomTemplates defaultTicketTemplate = confirmationPagesRepository
                        .findByEventAndIsDefaultPageAndTemplateType(event, true, TemplateType.TICKET_PDF_DESIGN);
                if (defaultTicketTemplate != null) {
                    ticketingTypeFromDb.setCustomTicketPdfDesignId(defaultTicketTemplate.getId());
                    log.info("Set default ticket PDF design ID {} for ticket type {}",
                            defaultTicketTemplate.getId(), ticketingTypeFromDb.getId());
                } else {
                    log.error("Default ticket PDF design page not found for event {}", event.getEventId());
                }
            }
        }
    }


    private void setConfirmationEmailForTicketType(TicketTypeSettingDto ticketTypeDto, Event event, TicketingType ticketingTypeFromDb,TemplateType templateType) {
        if (ticketTypeDto.getConfirmationEmailId() != 0) {
            ticketingTypeFromDb.setCustomConfirmationEmailId(ticketTypeDto.getConfirmationEmailId());
            log.info("set custom confirmation email id {} for ticket type {}", ticketTypeDto.getConfirmationEmailId(), ticketingTypeFromDb.getId());
        } else {
            Long customTemplatesId = confirmationEmailRepository.findCustomTemplatesIdByEventAndIsDefaultEmail(event, true,templateType);
            if (customTemplatesId != null) {
                ticketingTypeFromDb.setCustomConfirmationEmailId(customTemplatesId);
                log.info("set default confirmation email id {} for ticket type {}", customTemplatesId, ticketingTypeFromDb.getId());
            } else {
                log.error("default confirmation email not found for event {}", event.getEventId());
            }
        }
    }

    public void associateTicketTypeWithBadge(Long ticketTypeId, Long badgeId) {

        Optional<Badges> badgesOptional = badgesService.findById(badgeId);
        if(badgesOptional.isPresent()){
            Optional<JoinBadgeTicketTypes> joinBadgeTicketTypesOptional = joinBadgeTicketTypesService.findByTicketTypeId(ticketTypeId);
            if(joinBadgeTicketTypesOptional.isPresent()){
                JoinBadgeTicketTypes joinBadgeTicketTypes = joinBadgeTicketTypesOptional.get();
                joinBadgeTicketTypes.setBadges(badgesOptional.get());
                joinBadgeTicketTypesService.save(joinBadgeTicketTypes);
            }else {
                joinBadgeTicketTypesService.save(new JoinBadgeTicketTypes(badgesOptional.get(), ticketTypeId));
            }
        }
    }


    private void handleTagsAndTracks(Long ticketingTypeId, TicketTypeSettingDto ticketTypeDto) {
        List<TicketingTypeTagAndTrack> ticketingTypeTagAndTracks = ticketingTypeTagAndTrackService.findByTicketingTypeId(ticketingTypeId);
        Set<Long> dbTagOrTrackIds = ticketingTypeTagAndTracks.stream().map(TicketingTypeTagAndTrack::getTagOrTrackId).collect(Collectors.toSet());
        Set<Long> inputTagOrTrackIds = getIds(ticketTypeDto.getRestrictedTags());
        inputTagOrTrackIds.addAll(getIds(ticketTypeDto.getRestrictedTracks()));

        Set<Long> addNewList = inputTagOrTrackIds.stream().filter(inputTagId -> !dbTagOrTrackIds.contains(inputTagId)).collect(Collectors.toSet());
        ticketingTypeTagAndTrackService.createRecords(ticketingTypeId, addNewList);

        dbTagOrTrackIds.removeAll(inputTagOrTrackIds);
        if (!dbTagOrTrackIds.isEmpty()) {
            // remove the records.
            ticketingTypeTagAndTrackService.deleteRecords(ticketingTypeId, dbTagOrTrackIds);
        }
    }

    private Set<Long> getIds(List<IdNameDto> idNameDtos) {
        return null != idNameDtos ? idNameDtos.stream().map(IdNameDto::getId).filter(Objects::nonNull).collect(toSet()) : EMPTY_SET;
    }

    private void updateSeatsIOData(TicketTypeSettingDto ticketTypeDto, Event event, Ticketing ticketing, TicketingType ticketingTypeFromDb, boolean isNewTicket, boolean isSeatingEnable, List<Category> categories) {
        if (isSeatingEnable) {
            // handleCategoryNameInsertUpdateForSingleTicket(ticketTypeDto, event, isNewTicket, ticketingType);//nosonar
            updateSeatsIOWithNewCategories(true, event, ticketing, categories);

            if (!isNewTicket && !ticketingTypeFromDb.getBundleType().equals(ticketTypeDto.getBundleType())) {
                ticketingService.requireToCallUpdateChartTableModes(event);
            }
        }
    }

    private boolean isCategoryIdNullForSeatingTicket(TicketTypeSettingDto ticketTypeDto, TicketingType ticketingType) {
        return TICKET.equals(ticketTypeDto.getDataType()) && ticketingType.isAllowAssignedSeating() && ticketTypeDto.isAllowAssignedSeating()
                && !isNumberGreaterThanZero(ticketTypeDto.getCategoryId());
    }

    private boolean isCategoryIdNullForAddOn(TicketTypeSettingDto ticketTypeDto) {
        return ADDON.equals(ticketTypeDto.getDataType()) && !isNumberGreaterThanZero(ticketTypeDto.getCategoryId());
    }

    private void handleActionOnAddUpdateDefaultTicketTypeInRecurring(TicketTypeSettingDto ticketTypeDto,
                                                                     Event event,
                                                                     Ticketing ticketing,
                                                                     boolean isNewTicket,
                                                                     TicketingType ticketingType) {
        boolean isDefaultTicket = (ticketingType.getCreatedFrom() == null);
        if (isDefaultTicket) {
            // Create New ticket types for default ticket types : Update old recurring event ticket types for default ticket type update.
            if (isNewTicket) {
                List<RecurringEvents> recurringEventsList = recurringEventsScheduleService.getRecurringEventsOnlyFutureDate(event);
                recurringEventsList.forEach(e -> recurringEventsMainScheduleService.createTicketType(e, Collections.singletonList(ticketingType), event.getEquivalentTimeZone()));

                recurringEventsMainScheduleService.executeHolderRequiredAttributesForRecurringEvent(event,
                        recurringEventsList,
                        Collections.singletonList(ticketingType.getId()),
                        true);
            } else {
                List<TicketingType> ticketTypesForUpdateRecurringEvents = validationToCheckAllDateArePast(ticketing, ticketTypeDto, event);
                List<TicketingType> recurringEventTicketTypes =
                        ticketTypesForUpdateRecurringEvents.stream().map(e -> copyTicket(event, ticketing, ticketTypeDto, e))
                                .collect(Collectors.toList());
                if (!recurringEventTicketTypes.isEmpty()) {
                    ticketingTypeService.saveAll(recurringEventTicketTypes);
                }
            }
        }
    }

    private void activateTicketingForFreeTicketOrDeactiveForPaidIfStripeIsNotConnected(
            Ticketing ticketing,
            TicketingType ticketingType,
            Event event) {
        // TODO : Vikas, Check in all events//NOSONAR
        boolean paidTicket = false;
        if (Arrays.asList(PAID, TicketType.DONATION)
                .contains(ticketingType.getTicketType())) {
            paidTicket = true;
        }
        if (!paidTicket && !ticketing.isOnlineEvent()) {
            ticketing.setActivated(true);
        }
        log.info("In activateTicketingForFreeTicketOrDeactiveForPaidIfStripeIsNotConnected method Activating ticketing module for eventId {} and value of Activated flag {} ", event != null ? event.getEventId() : 0, ticketing.getActivated());
    }

    //Here all logic should be in execute service
    protected void handleHolderRequiredAttributesForRecurringEvent(Event event,
                                                                   List<TicketingType> newDefaultTicketTypes,
                                                                   Long recurringEventId) {
        if (!newDefaultTicketTypes.isEmpty()) {
            List<Long> newDefaultTicketTypeId = newDefaultTicketTypes.stream().map(TicketingType::getId).collect(Collectors.toList());
            if (!isNumberGreaterThanZero(recurringEventId)) {
                List<RecurringEvents> recurringEventsList = recurringEventsScheduleService.getRecurringEventsOnlyFutureDate(event);
                recurringEventsMainScheduleService.executeHolderRequiredAttributesForRecurringEvent(event, recurringEventsList, newDefaultTicketTypeId, true);
            } else {
                Optional<RecurringEvents> recurringEvents = recurringEventsScheduleService.getRecurringEventById(recurringEventId);
                if (!newDefaultTicketTypeId.isEmpty() && recurringEvents.isPresent()) {
                    recurringEventsMainScheduleService.executeHolderRequiredAttributesForRecurringEvent(event, Collections.singletonList(recurringEvents.get()), newDefaultTicketTypeId, false);
                }
            }
        }
    }

    public List<TicketingType> getTicketingTypeList(Long typeId, Event event) {
        List<TicketingType> ticketingTypeList = ticketingTypeTicketService.getTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(typeId, event);
        if (ticketingTypeList.isEmpty() && typeId != 0) {
            throw new NotAcceptableException(NotAcceptableException.RecurringExceptionMsg.ALL_DATES_ARE_PAST);
        } else {
            return ticketingTypeList;
        }
    }

    //We can not update ticket type as all date are past. So we ask to create new dates first.
    // ALOK:3791 : Its working fine but we need to work on this logic.
    private List<TicketingType> validationToCheckAllDateArePast(Ticketing ticketing, TicketTypeSettingDto ticketingTypeTemp, Event event) {
        if (ticketing.isRecurringEvent()) {
            if (isNumberGreaterThanZero(ticketingTypeTemp.getTypeId()) && ticketingTypeTemp.getCreatedFrom() == null) {

                return this.getTicketingTypeList(ticketingTypeTemp.getTypeId(), event);
            } else {
                List<TicketingType> ticketingTypeList = ticketingTypeTicketService.getTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(ticketingTypeTemp.getCreatedFrom(), event);
                if (null != ticketingTypeTemp.getCreatedFrom() && ticketingTypeTemp.getCreatedFrom() != -1 && (ticketingTypeList.isEmpty() && ticketingTypeTemp.getTypeId() != 0)) {
                    throw new NotAcceptableException(NotAcceptableException.RecurringExceptionMsg.ALL_DATES_ARE_PAST);
                }
            }
        }
        return Collections.emptyList();
    }

    public TicketingType copyTicket(Event event,
                                    Ticketing ticketing,
                                    TicketTypeSettingDto tikcetTypeDto,
                                    TicketingType ticketingTypeFromDb) {
        boolean isDonationTypeTicket = TicketType.DONATION.equals(tikcetTypeDto.getTicketType());
        boolean newTicketingType = false;
        long startTime = System.currentTimeMillis();
        if (ticketingTypeFromDb == null) {
            newTicketingType = true;
            ticketingTypeFromDb = new TicketingType();
            ticketingTypeFromDb.setDataType(tikcetTypeDto.getDataType());
            setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent(tikcetTypeDto, ticketingTypeFromDb);
        }

        setTicketTypeEndDate(ticketing, tikcetTypeDto, ticketingTypeFromDb, event.getEquivalentTimeZone());

        validateNumberOfTickets(tikcetTypeDto, ticketingTypeFromDb);

        validateDepositeFeature(tikcetTypeDto);

        copyTicketingTypeElement(tikcetTypeDto, ticketingTypeFromDb, event.getEquivalentTimeZone(), ticketing, event, newTicketingType);

        if (isNumberGreaterThanZero(tikcetTypeDto.getRecurringEventId())
                && null == ticketingTypeFromDb.getRecurringEventId()) {
            // this is because : create TT after selecting any recurring event ,
            // So this TT for this recurring event only
            ticketingTypeFromDb.setRecurringEventId(tikcetTypeDto.getRecurringEventId());
        }
        transactionFeeConditionalLogicService.applyFeeInTicketType(event, ticketingTypeFromDb,
                (TicketTypeFormat.VIRTUAL.equals(tikcetTypeDto.getTicketTypeFormat()) || TicketTypeFormat.HYBRID.equals(tikcetTypeDto.getTicketTypeFormat())),
                isDonationTypeTicket);

        ticketingTypeFromDb.setTicketType(tikcetTypeDto.getTicketType());
        ticketingTypeFromDb.setTicketing(ticketing);
        ticketingTypeFromDb.setEventId(event.getEventId());
        ticketingTypeFromDb.setTracksSessionsLimitAllowed(tikcetTypeDto.getTracksSessionsLimitAllowed());
        ticketingTypeFromDb = ticketingTypeTicketService.setPositionForTicketingType(ticketingTypeFromDb);
        long endTime = System.currentTimeMillis();
        log.info("copyTicketEnd|{}", (endTime - startTime));
        return ticketingTypeFromDb;
    }

    private void validateDepositeFeature(TicketTypeSettingDto ticketTypeSettingDto) {
        if(!ticketTypeSettingDto.isPayLater() && ticketTypeSettingDto.isRequireDepositAmount()) {
            throw new NotAcceptableException(CAN_NOT_ENABLE_DEPOSITE_FEATURE);
        }
        if(ticketTypeSettingDto.isRequireDepositAmount()
                && (ticketTypeSettingDto.getDepositType() != null && ticketTypeSettingDto.getDepositType() == DepositType.FLAT
                && ticketTypeSettingDto.getPrice() < ticketTypeSettingDto.getDepositAmount())) {
            throw new NotAcceptableException(DEPOSITE_AMOUNT_NOT_EXCEED_TO_PRICE);
        }
    }

    private void validateNumberOfTickets(TicketTypeSettingDto ticketingTypeTemp, TicketingType ticketingTypeFromDb) {
        if (isNumberGreaterThanZero(ticketingTypeFromDb.getId())) {
            long numberOfTicketsSold = ticketingStatisticsService.soldTicketCount(ticketingTypeFromDb);

            long numberOfTicket;
            if (INDIVIDUAL_TICKET.equals(ticketingTypeTemp.getBundleType())) {
                numberOfTicket = ticketingTypeTemp.getNumberOfTicket();
            } else {
                numberOfTicket = (long) ticketingTypeTemp.getNumberOfTicket() * ticketingTypeTemp.getTicketsPerTable();
            }

            if (numberOfTicketsSold > numberOfTicket &&
                    !TicketType.DONATION.equals(ticketingTypeTemp.getTicketType())) {
                throw new NotAcceptableException(CAN_NOT_DECRESS_NUMBER_OF_TICKETS);
            }
        }
    }

    @Override
    public void updateHolderRequiredAttributesForNewTicket(Event event, TicketingType ticketingType) {

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = allRequiresAttributesService
                .getDefaultOrParticularRecurringEventTicketHolderAtrributes(event, ticketingType.getRecurringEventId(),
                        ticketingType.getDataType());

        List<String> defaultAttributes = Arrays.asList(STRING_FIRST_SPACE_NAME, STRING_LAST_SPACE_NAME, STRING_EMAIL_SPACE, STRING_CELL_SPACE_PHONE);

        List<TicketHolderRequiredAttributes> updateTicketHolderAttributes = new ArrayList<>();

        ticketHolderRequiredAttributes
                .forEach(attribute -> {

                    boolean updateAttribute = false;
                    // TODO : Will check this second condition later //NOSONAR
                    if (attribute.getEnabledForTicketPurchaser() && !defaultAttributes.contains(attribute.getName())) {
                        if(attribute.getBuyerDefaultRequirement().equals(TicketAttributesDefaultRequirement.REQUIRED)) {
                            attribute.setBuyerRequiredTicketTypeId(appendTicketTypeId(ticketingType, attribute.getBuyerRequiredTicketTypeId()));
                        }else if(attribute.getBuyerDefaultRequirement().equals(TicketAttributesDefaultRequirement.OPTIONAL)){
                            attribute.setBuyerOptionalTicketTypeId(appendTicketTypeId(ticketingType, attribute.getBuyerOptionalTicketTypeId()));
                        }
                        else if(attribute.getBuyerDefaultRequirement().equals(TicketAttributesDefaultRequirement.PROGRESSIVE)){
                            attribute.setPurchaserRegistrationHiddenTicketTypeId(appendTicketTypeId(ticketingType, attribute.getPurchaserRegistrationHiddenTicketTypeId()));
                        }
                        updateAttribute = true;

                        if (DataType.ADDON.equals(attribute.getDataType())) {
                            attribute.setHolderRequiredTicketTypeId(appendTicketTypeId(ticketingType, attribute.getBuyerRequiredTicketTypeId()));
                        }

                    }
                    if (attribute.getEnabledForTicketHolder()) {
                        if(attribute.getHolderDefaultRequirement().equals(TicketAttributesDefaultRequirement.REQUIRED)) {
                            attribute.setHolderRequiredTicketTypeId(appendTicketTypeId(ticketingType, attribute.getHolderRequiredTicketTypeId()));
                        }else if(attribute.getHolderDefaultRequirement().equals(TicketAttributesDefaultRequirement.OPTIONAL)){
                            attribute.setHolderOptionalTicketTypeId(appendTicketTypeId(ticketingType, attribute.getHolderOptionalTicketTypeId()));
                        }
                        else if(attribute.getHolderDefaultRequirement().equals(TicketAttributesDefaultRequirement.PROGRESSIVE)){
                            attribute.setHolderRegistrationHiddenTicketTypeId(appendTicketTypeId(ticketingType, attribute.getHolderRegistrationHiddenTicketTypeId()));
                        }
                        updateAttribute = true;
                    }

                    if (updateAttribute) {
                        updateTicketHolderAttributes.add(attribute);
                    }

                });

        if (!updateTicketHolderAttributes.isEmpty()) {
            ticketHolderRequiredAttributesService.saveAll(updateTicketHolderAttributes);
        }
    }

    private String appendTicketTypeId(TicketingType ticketingType, String existingTicketTypeIds) {
        if (existingTicketTypeIds != null) {
            return existingTicketTypeIds + "," + ticketingType.getId();
        } else {
            return String.valueOf(ticketingType.getId());
        }
    }

    private void setValueOfCategoryColor(SeatingCategories seatingCategories, List<CategoryColors> categoryColorsList) {

        random = new Random();
        int randNumber = random.nextInt(categoryColorsList.size());
        seatingCategories.setColor(categoryColorsList.get(randNumber).getColor());
    }

    protected SeatingCategories createCategoryOrGetCategoryByName(String category, Long eventId, boolean isTicketTypeName) {
        List<CategoryColors> categoryColorsList = Arrays.asList(CategoryColors.values());
        SeatingCategories seatingCategories = seatingCategoryRepository.findByCategoryNameAndEventId(category, eventId);
        if (seatingCategories == null) {
            SeatingCategories seatingCategoriesNew = new SeatingCategories();
            seatingCategoriesNew.setName(category);
            seatingCategoriesNew.setHavingVariations(isTicketTypeName);
            seatingCategoriesNew.setEventId(eventId);
            setValueOfCategoryColor(seatingCategoriesNew, categoryColorsList);
            return seatingCategoryRepository.save(seatingCategoriesNew);
        } else {
            return seatingCategories;
        }
    }

    protected boolean isPaidTicketExistsNow(boolean isPaidTicketExistsNow, TicketTypeSettingDto ticketingTypeTemp) {
        if ((PAID.equals(ticketingTypeTemp.getTicketType())
                && ticketingTypeTemp.getPrice() > 0) || (TicketType.DONATION.equals(ticketingTypeTemp.getTicketType())
                && ticketingTypeTemp.getPrice() <= 0)) {
            isPaidTicketExistsNow = true;
        }
        return isPaidTicketExistsNow;
    }

    protected void isTicketTypeApplicableForDiscountCodeAndAccessCode(Event event,
                                                                      TicketingType ticketingType,
                                                                      List<Long> ticketingTypeIds) {
        List<TicketingCoupon> ticketingCoupons = ticketingCouponService.getAllByEventId(event.getEventId());
        if (!CollectionUtils.isEmpty(ticketingCoupons)) {
            ticketingCoupons.forEach(ticketingCoupon -> {
                List<Long> applicableEventTicketTypeIds = Arrays.stream(ticketingCoupon.getEventTicketTypeId().split(STRING_COMMA)).filter(NumberUtils::isCreatable).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                if (applicableEventTicketTypeIds.containsAll(ticketingTypeIds)) {
                    ticketingCoupon.setEventTicketTypeId(ticketingCoupon.getEventTicketTypeId().concat("," + ticketingType.getId()));
                    ticketingCouponRepository.save(ticketingCoupon);
                }
            });
        }

        List<TicketingAccessCode> accessCodeList = ticketingAccessCodeService.findByEvent(event);
        if (!CollectionUtils.isEmpty(accessCodeList)) {
            accessCodeList.forEach(accessCode -> {
                List<Long> applicableEventTicketTypeIds = Arrays.stream(accessCode.getEventTicketTypeId().split(STRING_COMMA)).filter(NumberUtils::isCreatable).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                if (applicableEventTicketTypeIds.containsAll(ticketingTypeIds)) {
                    accessCode.setEventTicketTypeId(accessCode.getEventTicketTypeId().concat(STRING_COMMA + ticketingType.getId()));
                    ticketingAccessCodeService.save(accessCode);
                    ticketingType.setHidden(true);
                    ticketingTypeService.save(ticketingType);
                }
            });
        }
    }

    protected void setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent(TicketTypeSettingDto ticketTypeDto,
                                                                                       TicketingType ticketingTypeFromDb) {
        if (isNumberGreaterThanZero(ticketTypeDto.getRecurringEventId())) {
            ticketingTypeFromDb.setCreatedFrom(-1L);
        }
    }

    protected void setTicketTypeEndDate(Ticketing ticketing, TicketTypeSettingDto ticketingTypeTemp, TicketingType ticketingTypeFromDb, String equivalentTimezone) {
        if (ticketing.isRecurringEvent()) {
            if (null != ticketingTypeFromDb.getRecurringEventId()) {
                RecurringEvents recurringEvents = recurringEventsScheduleService.getRecurringEvents(ticketingTypeFromDb.getRecurringEventId());
                if (null != ticketingTypeTemp.getRecurringEventSalesEndStatus() && ticketingTypeTemp.getRecurringEventSalesEndTime()!=null) {
                    if (END.equals(ticketingTypeTemp.getRecurringEventSalesEndStatus())) {
                        ticketingTypeTemp.setEndDate(ticketingService.getStringDateWithAddedRecurringEventEndTime(recurringEvents.getRecurringEventEndDate(), -ticketingTypeTemp.getRecurringEventSalesEndTime()));
                    } else if (START.equals(ticketingTypeTemp.getRecurringEventSalesEndStatus())) {
                        ticketingTypeTemp.setEndDate(ticketingService.getStringDateWithAddedRecurringEventEndTime(recurringEvents.getRecurringEventStartDate(), -ticketingTypeTemp.getRecurringEventSalesEndTime()));
                    }
                } else {
                    ticketingTypeTemp.setEndDate(ticketingService.getStringDateWithAddedRecurringEventEndTime(recurringEvents.getRecurringEventStartDate(), -60));
                }
            }
        } else {
            if (ticketingTypeTemp.getEndDate() == null) {
                ticketingTypeTemp.setEndDate(TimeZoneUtil.getEndDateString(ticketing.getEventEndDate(), equivalentTimezone));
            }

            if (ticketingTypeTemp.getStartDate() == null) {
                ticketingTypeTemp.setStartDate(TimeZoneUtil.getEndDateString(ticketing.getEventStartDate(), equivalentTimezone));
            }
        }
    }

    protected void copyTicketingTypeElement(TicketTypeSettingDto dto, TicketingType ticketingType, String equivalentTimezone, Ticketing ticketing, Event event, boolean newTicketingType) {
        validateExistingTicketTypeCanNotChangeAndSet(ticketingType, dto,ticketing);
        // target.setAllow_discount_coupon(source.getAllow_discount_coupon());//nosonar
        ticketingType.setTicketTypeFormat(dto.getTicketTypeFormat());
        ticketingType.setEnableTicketDescription(dto.getEnableTicketDescription());
        ticketingType.setEndDate(TimeZoneUtil.getDateInUTC(dto.getEndDate(), equivalentTimezone, null));
        ticketingType.setHidden(dto.getHidden());
        if (TicketType.DONATION.equals(dto.getTicketType()) || dto.getMinTickerPerBuyer() == null) {
            ticketingType.setMinTicketsPerBuyer(0);
        } else {
            ticketingType.setMinTicketsPerBuyer(dto.getMinTickerPerBuyer());
        }
        if (dto.getMaxTickerPerBuyer() == null) {
            ticketingType.setMaxTicketsPerBuyer(0);
        } else if (TICKET.equals(ticketingType.getDataType()) && ticketing.getUniqueTicketHolderEmail() && !ticketing.getCollectTicketHolderAttributes()) {
            ticketingType.setMaxTicketsPerBuyer(1);
        } else {
            ticketingType.setMaxTicketsPerBuyer(dto.getMaxTickerPerBuyer());
        }
        ticketingType.setCategoryId(dto.getCategoryId());
        ticketingType.setDataType(dto.getDataType());
        if (ticketing.isRecurringEvent() && null != dto.getRecurringEventId()) {
            updateCreatedFromField(dto, ticketingType);
        }

        setTicketTypesSalesEndTimeAndStatus(dto, ticketingType);

        ticketingType.setNumberOfTickets(dto.getNumberOfTicket());
        ticketingType.setTicketTypeDescription(dto.getTicketTypeDescription());
        ticketingType.setCustomInvoiceText(dto.getCustomInvoiceText());
        ticketingType.setPassfeetobuyer(dto.getPassfeetobuyer());
        if (vatTaxService.findByEventId(event.getEventId()).isPresent()) {
            ticketingType.setPassFeeVatToBuyer(dto.getPassFeeVatToBuyer());
        }
        ticketingType.setPrice(dto.getPrice());
        ticketingType.setAllowPDFDownload(dto.getAllowPDFDownload());
        ticketingType.setAllowInvoicePDFForBuyers(dto.getAllowInvoicePDFDownload());
        ticketingType.setAllowInvoicePDFForHolders(dto.getAllowInvoicePDFForHolder());
        ticketingType.setTicketTypeName(dto.getName());
        ticketingType.setMaxSessionRegisterUser(dto.getMaxSessionRegisterUser() != null && dto.getMaxSessionRegisterUser() == 0 ? null : dto.getMaxSessionRegisterUser());
        ticketingType.setRegistrationApprovalRequired(dto.isRegistrationApprovalRequired());

        if (!ticketing.isRecurringEvent() || dto.getTypeId() <= 0) {
            ticketingType.setStartDate(TimeZoneUtil.getDateInUTC(dto.getStartDate(), equivalentTimezone, null));
        }

        ticketingType.setChatRestricted(dto.isChatRestricted());
        ticketingType.setExpoRestricted(dto.isExpoRestricted());
        ticketingType.setLoungesRestricted(dto.isLoungesRestricted());
        ticketingType.setPayLater(dto.isPayLater());
        if(!dto.isPayLater()){
            dto.setRequireDepositAmount(false);
        }
        ticketingType.setRequireDepositAmount(dto.isRequireDepositAmount());
        ticketingType.setDepositType(dto.getDepositType());
        ticketingType.setDepositAmount(dto.getDepositAmount());
        ticketingType.setAllowMeetingCreation(dto.isAllowMeetingCreation());
        if (dto.getDataType() == ADDON) {
            log.info("Update Add-on list for Add-on Ticket types having isRecurringEvent {} , isNewTicketingType {} , ticketTypeId {}",
                    ticketing.isRecurringEvent(), newTicketingType, ticketingType.getId());
            ticketingType.setAllTicketTypesSelectedForAddOn(dto.isAllTicketTypesSelectedForAddOn());
            if (ticketing.isRecurringEvent() && !newTicketingType) {
                if (ticketingType.getCreatedFrom() == null) {
                    ticketingType.setListOfTicketTypesForAddOn(GeneralUtils.convertLongListToCommaSeparated(dto.getListOfTicketTypesForAddOn()));
                } else if (ticketingType.getId() != dto.getTypeId()) {
                    ticketingType.setListOfTicketTypesForAddOn(dto.isAllTicketTypesSelectedForAddOn()
                            ? GeneralUtils.convertLongListToCommaSeparated(ticketingTypeService.getAllTicketTypeIdsByRecurringEventIdAndDataType(ticketingType.getRecurringEventId()))
                            : GeneralUtils.convertLongListToCommaSeparated(ticketingTypeService.getAllTicketTypeIdsByRecurringEventIdAndDataTypeAndCreatedFrom(ticketingType.getRecurringEventId(), GeneralUtils.convertLongListToCommaSeparated(dto.getListOfTicketTypesForAddOn()))));
                } else if (ticketingType.getId() == dto.getTypeId()) {
                    ticketingType.setListOfTicketTypesForAddOn(GeneralUtils.convertLongListToCommaSeparated(dto.getListOfTicketTypesForAddOn()));
                }
            } else{
                ticketingType.setListOfTicketTypesForAddOn(GeneralUtils.convertLongListToCommaSeparated(dto.getListOfTicketTypesForAddOn()));
            }
        }
    }

    private void validateExistingTicketTypeCanNotChangeAndSet(TicketingType target, TicketTypeSettingDto source,Ticketing ticketing) {
        boolean isTicketTypeExistForEventTicket = false;
        boolean isTicketTypeExistForOrderManager = false;
        if (isNumberGreaterThanZero(target.getId())) {
            isTicketTypeExistForEventTicket = eventCommonRepoService.isTypePurchased(target);
            isTicketTypeExistForOrderManager = ticketingOrderManagerService.isTicketingTypeExist(target);
        }
        if (isNumberGreaterThanZero(source.getTypeId()) && (isTicketTypeExistForEventTicket || isTicketTypeExistForOrderManager) && !source.getBundleType().equals(target.getBundleType())) {
            throw new NotAcceptableException(BUNDLE_TYPE_CAN_NOT_BE_CHANGED);
        }
        if (!INDIVIDUAL_TICKET.equals(source.getBundleType()) && source.getTicketsPerTable() < 1) {
            throw new NotAcceptableException(NUMBER_OF_TICKETS_CAN_NOT_BE_LESS_THAN_ONE);
        }
        if (!INDIVIDUAL_TICKET.equals(source.getBundleType()) && isNumberGreaterThanZero(source.getTypeId())
                && (isTicketTypeExistForEventTicket || isTicketTypeExistForOrderManager)
                && (target.getNumberOfTicketPerTable() != source.getTicketsPerTable())) {
            NotAcceptableException.TicketingExceptionMsg ticketingExceptionMsg = NotAcceptableException.TicketingExceptionMsg.NUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED;
            ticketingExceptionMsg.setErrorMessage(Constants.NUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED.replace("{bundle_type}", target.getBundleType().toString()));
            ticketingExceptionMsg.setDeveloperMessage(Constants.NUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED.replace("{bundle_type}", target.getBundleType().toString()));
            throw new NotAcceptableException(ticketingExceptionMsg);
        }

        if ((target.getId() == 0) || (!isTicketTypeExistForEventTicket
                && !isTicketTypeExistForOrderManager)) {
            target.setBundleType(source.getBundleType());
            target.setNumberOfTicketPerTable(source.getTicketsPerTable());
        }
        if (INDIVIDUAL_TICKET.equals(source.getBundleType()) || (ticketing.getUniqueTicketHolderEmail() && !ticketing.getCollectTicketHolderAttributes())) {
            target.setNumberOfTicketPerTable(1);
        }
    }

    protected int getTotalTicketsDb(TicketingType ticketingTypeFromDb) {
        int totalTicketsDb = ticketingTypeFromDb.getNumberofticket();
        if (TicketBundleType.TABLE.equals(ticketingTypeFromDb.getBundleType())) {
            totalTicketsDb = ticketingTypeFromDb.getNumberofticket() * ticketingTypeFromDb.getNumberOfTicketPerTable();
        }
        return totalTicketsDb;
    }

    protected int getTotalTickets(TicketTypeSettingDto ticketingTypeTemp) {
        int totalTickets = ticketingTypeTemp.getNumberOfTicket();
        if (TicketBundleType.TABLE.equals(ticketingTypeTemp.getBundleType())) {
            totalTickets = ticketingTypeTemp.getNumberOfTicket() * ticketingTypeTemp.getTicketsPerTable();
        }
        return totalTickets;
    }

    protected void updateCreatedFromField(TicketTypeSettingDto dto, TicketingType target) {

        if (null != dto.getRecurringEventSalesEndStatus() && dto.getRecurringEventSalesEndStatus() != target.getRecurringEventSalesEndStatus()) {//NOSONAR
            target.setCreatedFrom(-1L);
        } else if (null != dto.getRecurringEventSalesEndTime() && !dto.getRecurringEventSalesEndTime().equals(target.getRecurringEventSalesEndTime())) {//NOSONAR
            target.setCreatedFrom(-1L);
        } else if (!dto.getName().equalsIgnoreCase(target.getTicketTypeName())) {
            target.setCreatedFrom(-1L);
        } else if (null != dto.getPassfeetobuyer() && !dto.getPassfeetobuyer().equals(target.isPassfeetobuyer())) {
            target.setCreatedFrom(-1L);
        } else if (null != dto.getPassFeeVatToBuyer() && !dto.getPassFeeVatToBuyer().equals(target.isPassFeeVatToBuyer())) {
            target.setCreatedFrom(-1L);
        } else if (null != dto.getMaxTickerPerBuyer() && !dto.getMaxTickerPerBuyer().equals(target.getMaxTicketsPerBuyer())) {
            target.setCreatedFrom(-1L);
        } else if (null != dto.getMinTickerPerBuyer() && !dto.getMinTickerPerBuyer().equals(target.getMinTicketsPerBuyer())) {
            target.setCreatedFrom(-1L);
        } else if (!dto.getBundleType().equals(target.getBundleType())) {
            target.setCreatedFrom(-1L);
        } else if (!dto.getBundleType().equals(INDIVIDUAL_TICKET) && (null != dto.getTicketsPerTable() && dto.getTicketsPerTable().equals(target.getNumberOfTicketPerTable()))) {
            target.setCreatedFrom(-1L);
        }
    }

    protected void setTicketTypesSalesEndTimeAndStatus(TicketTypeSettingDto source, TicketingType target) {
        if (null != source.getRecurringEventSalesEndTime()) {
            target.setRecurringEventSalesEndTime(source.getRecurringEventSalesEndTime());
        } else {
            target.setRecurringEventSalesEndTime(60);
        }

        if (null != source.getRecurringEventSalesEndStatus()) {
            target.setRecurringEventSalesEndStatus(source.getRecurringEventSalesEndStatus());
        } else {
            target.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        }
    }

    @Override
    public void updateSeatsIOWithNewCategories(Boolean enableSeatingNow, Event event, Ticketing ticketing, List<Category> categories) {
        String firstRecId = recurringEventsScheduleService.getFirstRecId(event, ticketing);
        String chartKey = seatsIoService.createOrUpdateChart(event.getEventId(), ticketing.getChartKey(), categories, firstRecId);
        try {
            seatsIoService.createOrUpdateChart(event.getEventId(), chartKey, categories, firstRecId);
            seatsIoService.publishChart(chartKey);
        } catch (Exception e) {
            // Absorb chart publish issue
        }
        if (isBlank(ticketing.getChartKey())) {
            ticketing.setChartKey(chartKey);
            ticketingService.save(ticketing);
        }
    }


    protected boolean isSeatingToNonSeatingFlagUpdate(Boolean enableSeatingNow, Ticketing ticketing) {
        return isNotBlank(ticketing.getChartKey()) && !enableSeatingNow;
    }

    @Override
    public EventTicketingDto getTicketing(Event event) {

        EventTicketingDto ticketingDto = new EventTicketingDto();

        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);

        ticketingDto.setEventAddress(ticketing.getEventAddress());
        ticketingDto.setLatitude(ticketing.getLatitude());
        ticketingDto.setLongitude(ticketing.getLongitude());
        EnumEventVenue enumEventVenue = ticketingService.getEventVenue(ticketing);
        ticketingDto.setEventVenueType(enumEventVenue.name());
        ticketingDto.setEventStartDate(
                getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone(), null));
        ticketingDto.setEventEndDate(
                getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone(), null));
        ticketingDto.setSeatingChartKey(ticketing.getChartKey());
        ticketingDto.setEventKey(String.valueOf(event.getEventId()));
        ticketingDto.setAvailableTimeZone(TimeZoneUtil.getAllTimeZone());
        ticketingDto.setTimezoneId(event.getTimezoneId());
        ticketingDto.setEquivalentTimezone(event.getEquivalentTimeZone());
        ticketingDto.setShowSessionByTimezone(event.isStaticTimezone());

        ticketingDto.setTicketingFee(getTicketFeesLogic(event));
        ticketingDto.setCurrencySymbol(event.getCurrency().getSymbol());
        ticketingDto.setEventFormat(event.getEventFormat());
        ticketingDto.setPreEventAccessMinutes(ticketing.getPreEventAccessMinutes());
        ticketingDto.setPostEventAccessMinutes(ticketing.getPostEventAccessMinutes());
        Date currentDate = TimeZoneUtil.getDateInLocal(DateUtils.getCurrentDate(), event.getEquivalentTimeZone());
        Date eventEndDate = TimeZoneUtil.getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone());
        Date eventStartDate = TimeZoneUtil.getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone());
        //Set Event Flag here for is Active or not
        ticketingDto.setEventIsActive(currentDate.before(eventEndDate));
        ticketingDto.setEventIsCurrentActive(currentDate.before(eventEndDate) && currentDate.after(eventStartDate));
        ticketingDto.setShowMemberCountInCheckout(ticketing.isShowMemberCountInCheckout());
        ticketingDto.setEventQrCheckIn(ticketing.isEventQrCheckIn());
        ticketingDto.setEmailSearch(ticketing.isEmailSearch());
        ticketingDto.setTicketNumberSearch(ticketing.isTicketNumberSearch());
        return ticketingDto;
    }


    @Override
    public EventTicketingDto getKioskCheckInConfig(Event event) {
        EventTicketingDto ticketingDto = new EventTicketingDto();
        Ticketing ticketing = ticketingHelperService.findTicketingByEvent(event);
        ticketingDto.setEventQrCheckIn(ticketing.isEventQrCheckIn());
        ticketingDto.setEmailSearch(ticketing.isEmailSearch());
        ticketingDto.setTicketNumberSearch(ticketing.isTicketNumberSearch());
        return ticketingDto;
    }

    protected List<TicketingFeeDto> getTicketFeesLogic(Event event) {
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = transactionFeeConditionalLogicService.getRecordByEvent(event);
        if (transactionFeeConditionalLogics.isEmpty()) {
            TicketingFeeDto ticketingFeeDto = new TicketingFeeDto();
            ticketingFeeDto.setAeFeeFlat(AE_FLAT_FEE_ONE);
            ticketingFeeDto.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
            ticketingFeeDto.setAddon(false);
            ticketingFeeDto.setInPerson(IN_PERSON.equals(event.getEventFormat()));
            CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);
            ticketingFeeDto.setCreditCardProcessingFlat(creditCardChargesDto.getCreditCardFlat());
            ticketingFeeDto.setCreditCardProcessingPercentage(creditCardChargesDto.getCreditCardPercentage());

            if (event.getWhiteLabel() != null) {
                ticketingFeeDto.setWlFeeFlat(WL_FEE_FLAT);
                ticketingFeeDto.setWlFeePercentage(WL_FEE_PERCENTAGE);
            } else {
                ticketingFeeDto.setWlFeeFlat(0);
                ticketingFeeDto.setWlFeePercentage(0);
            }

            return (Collections.singletonList(ticketingFeeDto));
        } else {
            List<TicketingFeeDto> ticketingFeeDtos = new ArrayList<>();
            CreditCardChargesDto creditCardChargesDto = stripeService.getCCProcessingDetails(event);

            transactionFeeConditionalLogics.forEach(e -> ticketingFeeDtos.add(new TicketingFeeDto(e, creditCardChargesDto)));

            return ticketingFeeDtos;
        }
    }

    public void setEventStartDateToCalender(DateFormat formatter, Calendar cal, String eventStartDate) {
        Date eventStartDateWithoutTime;
        try {
            eventStartDateWithoutTime = formatter.parse(eventStartDate);
            cal.setTime(eventStartDateWithoutTime);
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
        } catch (Exception e) {
            log.info("Error while parsing event date", e);
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void saveTicketing(EventTicketingDto ticketingDto, Event event, boolean isFromEventDetailsPage, Boolean isSuperadmin, Boolean isSalesSuperAdmin, boolean isFromAccelEvents) {//NOSONAR
        log.info("saveTicketingWithoutTicketTypes for event: {} and by superadmin: {}", event.getEventId(), isSuperadmin);
        try {
            if (isNotBlank(ticketingDto.getTimezoneId()) && isNotBlank(ticketingDto.getEquivalentTimezone()) && !isFromAccelEvents) {
                event.setTimezoneId(ticketingDto.getTimezoneId());
                event.setEquivalentTimeZone(ticketingDto.getEquivalentTimezone());
                event.setStaticTimezone(ticketingDto.isShowSessionByTimezone());
                eventRepoService.save(event);
            }
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm");
            LocalDateTime startDateTime = LocalDateTime.parse(ticketingDto.getEventStartDate(), dateTimeFormatter);
            LocalDateTime endDateTime = LocalDateTime.parse(ticketingDto.getEventEndDate(), dateTimeFormatter);
            if (startDateTime.isAfter(endDateTime)) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.END_DATE_MUST_BE_AFTER_THE_START_DATE);
            }
            log.info("saveTicketing EventTicketingDto startTime {} endTime {}", ticketingDto.getEventStartDate(), ticketingDto.getEventEndDate());
            Date eventStartDate = TimeZoneUtil.getDateInUTC(ticketingDto.getEventStartDate(), event.getEquivalentTimeZone(), null);
            Date eventEndDate = TimeZoneUtil.getDateInUTC(ticketingDto.getEventEndDate(), event.getEquivalentTimeZone(), null);
            log.info("saveTicketing EventTicketingDto after the conversion startTime {} endTime {}", eventStartDate, eventEndDate);
            Date currentDate = new Date();
            DateFormat formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm");
            Calendar cal = Calendar.getInstance();

            this.setEventStartDateToCalender(formatter, cal, ticketingDto.getEventStartDate());

            long startTime = System.currentTimeMillis();
            log.info("saveTicketingWithoutTicketTypesStartAt|{}", startTime);
            log.info("saveTicketing EventTicketingDto {}", ticketingDto);
            if (isNotBlank(ticketingDto.getTimezoneId()) && isNotBlank(ticketingDto.getEquivalentTimezone())) {
                event.setTimezoneId(ticketingDto.getTimezoneId());
                event.setEquivalentTimeZone(ticketingDto.getEquivalentTimezone());
                event.setStaticTimezone(ticketingDto.isShowSessionByTimezone());
                eventRepoService.save(event);
            }

            Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
            if (ticketingDto.getEventFormat() != null && event.getEventFormat() != null
                    && event.getEventFormat() != ticketingDto.getEventFormat()) {
                // After the event has started, does not allow event admins to change the event format
                LocalDateTime currentLocalDateAndTime = LocalDateTime.now(ZoneId.of(event.getEquivalentTimeZone()));
                log.info("TicketingManageServiceImpl || saveTicketing || currentLocalDateAndTime {} ||Event Start Date {} ", currentLocalDateAndTime, startDateTime);
                if (currentLocalDateAndTime.isAfter(startDateTime)) {
                    throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_EVENT_START);
                }
                if (!IN_PERSON.equals(event.getEventFormat())) {
                    // if event format is not In_person then check preEventAccess Time is not greater then current time.
                    LocalDateTime preEventAccessTime = startDateTime.minus(ticketing.getPreEventAccessMinutes(), ChronoUnit.MINUTES);
                    log.info("TicketingManageServiceImpl || saveTicketing || preAccessMinutes {} || currentLocalDateAndTime {} ||preEventAccessTime {}", ticketing.getPreEventAccessMinutes(), currentLocalDateAndTime, preEventAccessTime);
                    if (currentLocalDateAndTime.isAfter(preEventAccessTime)) {
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_PRE_EVENT_START);
                    }
                }
            }

            EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
            String planName = eventPlanConfig != null ? eventPlanConfig.getPlanConfig().getPlanName() : "";
            if (FREE_PLAN.getName().equals(planName) && !isFromEventDetailsPage) {
                ticketing.setLimitEventCapacity(true);
                ticketing.setEventCapacity(100d);
                if (eventPlanConfig != null) {
                    eventPlanConfig.setAttendeeImportLimit(100L);
                    eventPlanConfigService.save(eventPlanConfig);
                }
            }
            if (!isSuperadmin || (isSalesSuperAdmin && currentDate.before(eventEndDate))) {//NOSONAR
                if (CommonUtil.getLiveEventStatuses().contains(event.getEventListingStatus()) && StringUtils.isNotBlank(planName)
                        && !(LEGACY.getName().equals(planName) || WHITE_LABEL_LEGACY.getName().equals(planName))) {
                    if (pushingForwardEventStartDateAfterStartTime(eventStartDate, ticketing, currentDate)) {
                        throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_CHANGE_EVENT_START_DATE_AFTER_EVENT_STARTED);
                    }
                    if (pushingBackEventEndTimeAfterEventEnds(eventEndDate, ticketing, currentDate)) {
                        throw new NotAcceptableException(CAN_NOT_CHANGE_EVENT_END_DATE_AFTER_EVENT_ENDED);
                    }
                }
            } else {
                if (!ticketing.getEventStartDate().equals(eventStartDate) || !ticketing.getEventEndDate().equals(eventEndDate)) {
                    log.info("Event Start date is {} and end date is {} by super admin ", eventStartDate, eventEndDate);
                }
            }

            if ((!isFromEventDetailsPage && (checkEventTimeDiffWithCurrentTime(eventStartDate) || checkEventTimeDiffWithCurrentTime(eventEndDate)))
                    && !(isSalesSuperAdmin && currentDate.after(eventEndDate))) {
                throw new NotAcceptableException(PAST_EVENT_DATE_TIME);
            }
            if (isFromEventDetailsPage && !(isSalesSuperAdmin && currentDate.after(eventEndDate))) {
                if ((checkEventTimeDiffWithCurrentTime(eventStartDate) || eventEndDate.before(eventStartDate)) && (!eventStartDate.equals(ticketing.getEventStartDate()) && checkEventTimeDiffWithCurrentTime(eventStartDate))) {
                    throw new NotAcceptableException(PAST_EVENT_START_DATE_TIME);
                }
                if (!eventEndDate.equals(ticketing.getEventEndDate()) && checkEventTimeDiffWithCurrentTime(eventEndDate)) {
                    throw new NotAcceptableException(PAST_EVENT_END_DATE_TIME);
                }
            }

            // if event format is changed then event days not validated
            if (ticketing.getEventid().getEventFormat().equals(ticketingDto.getEventFormat())) {
                chargebeeService.validateEventDayBasedOnPlanType(ticketingDto, event);
            }




            if (!ticketing.isRecurringEvent()) {
                ticketing.setEventStartDate(eventStartDate);
                ticketing.setEventEndDate(eventEndDate);
                if (isEventFormatChangedFromInPersonToVirtual(ticketing, ticketingDto)) {
                    if (!CommonUtil.getLiveEventStatuses().contains(event.getEventListingStatus())) {
                        if (isFromEventDetailsPage && eventPlanConfigService.isChangeRequiredForMultipleToSingleDayEvent(event, ticketing, ticketingDto)) {
                            TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());
                            String eventTimeZone = timeZone.getEquivalentTimezone();
                            Date date = TimeZoneUtil.getDateInUTC(cal.getTime(), eventTimeZone);
                            ticketing.setEventEndDate(date);
                        }
                    } else {
                        eventService.putEventInDraftMode(event);
                    }
                }
                if (ticketing.getEventid().getEventFormat().equals(ticketingDto.getEventFormat())
                        && !IN_PERSON.equals(ticketingDto.getEventFormat()) && ticketingDto.getPreEventAccessMinutes() != null) {
                    chargebeeService.checkPreEventAccessByPlanType(event, ticketingDto.getPreEventAccessMinutes());
                }
                ticketing.setPreEventAccessMinutes(ticketingDto.getPreEventAccessMinutes() == null ? 30 : ticketingDto.getPreEventAccessMinutes());

                leaderboardService.resetGamificationData(event.getEventId());
                challengeConfigService.updateDefaultChallengeTime(ticketing, event);
            }
            if (isNotBlank(ticketingDto.getEventVenueType()) && EnumEventVenue.VENUE.name().equals(ticketingDto.getEventVenueType())) {
                ticketing.setEventAddress(ticketingDto.getEventAddress());
                ticketing.setLatitude(ticketingDto.getLatitude());
                ticketing.setLongitude(ticketingDto.getLongitude());
            } else {
                ticketing.setEventAddress(null);
                ticketing.setLatitude(null);
                ticketing.setLongitude(null);
            }

            boolean expectedStateOnline = isTicketingOnline(ticketingDto);
            ticketing.setOnlineEvent(expectedStateOnline);

            if (ticketingDto.getEventFormat() != null && event.getEventFormat() != null
                    && event.getEventFormat() != ticketingDto.getEventFormat()) {
                transactionFeeConditionalLogicService.handleRecord(event, ticketingDto.getEventFormat());
            }
            if (IN_PERSON.equals(event.getEventFormat())
                    && !IN_PERSON.equals(ticketingDto.getEventFormat()) && eventService.findTicketingByEvent(event).isRecurringEvent()) {
                eventService.switchRecurringEventToSingleDayEvent(event);
            }


            ticketingService.save(ticketing);

            if (event.getTicketingId() == null) {
                event.setTicketingId(ticketing.getId());
                eventRepoService.save(event);
            }

            long endTime = System.currentTimeMillis();
            log.info("saveTicketingWithoutTicketTypesEndIn|{}", (endTime - startTime));

            mapEventDetails(event);
        } catch (DateTimeParseException e) {
            throw new NotAcceptableException(NotAcceptableException.SessionSpeakerExceptionMsg.INVALID_SESSION_DATE_TIME);
        }
    }


    private boolean isEventFormatChangedFromInPersonToVirtual(Ticketing ticketing, EventTicketingDto ticketingDto) {
        EventFormat oldEventFormat = ticketing.getEventid().getEventFormat();
        EventFormat newEventFormat = ticketingDto.getEventFormat();
        // checked that event format changed from virtual to in-person or in-person to virtual
        return !oldEventFormat.equals(newEventFormat) &&
                ((VIRTUAL.equals(newEventFormat) || HYBRID.equals(newEventFormat)) && IN_PERSON.equals(oldEventFormat));
    }

    private boolean pushingBackEventEndTimeAfterEventEnds(Date eventEndDate, Ticketing ticketing, Date currentDate) {
        return !ticketing.getEventEndDate().equals(eventEndDate) && (currentDate.compareTo(ticketing.getEventEndDate())) > 0;
    }

    private boolean pushingForwardEventStartDateAfterStartTime(Date eventStartDate, Ticketing ticketing, Date currentDate) {
        return !ticketing.getEventStartDate().equals(eventStartDate) && (currentDate.compareTo(ticketing.getEventStartDate())) > 0;
    }

    public boolean isTicketingOnline(EventTicketingDto ticketingDto) {
        return (VIRTUAL.equals(ticketingDto.getEventFormat()) || HYBRID.equals(ticketingDto.getEventFormat())
                || ONLINE_VIRTUAL_EVENT.name().equals(ticketingDto.getEventVenueType()));
    }

    @Override
    public NewTicketingTypeSettingDto getTicketingTypeSettings(Event event, Long recurringEventId) {
        NewTicketingTypeSettingDto typeSettingsDTO = new NewTicketingTypeSettingDto();
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        if (isNumberGreaterThanZero(recurringEventId)) {
            Optional<RecurringEvents> recurringEvent = recurringEventsRepository.findById(recurringEventId);
            if (recurringEvent.isPresent()) {
                TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto = this.parseJsonToObject(recurringEvent.get().getRecurringJson());
                List<TicketingType> numberOfTickets = ticketingTypeService.findAllByTicketing(recurringEventId, event);
                log.info("getTicketingTypeSettings | recurring event id {} numberOfTickets size {}", recurringEventId, numberOfTickets.size());
                long totalNumberOfEventTickets = numberOfTickets.stream().mapToLong(TicketingType::getNumberOfTickets).sum();
                if (recurringEvent.get().getEventCapacity() != null) {
                    if ((totalNumberOfEventTickets < recurringEvent.get().getEventCapacity())) {
                        log.info("getTicketingTypeSettings | recurring event capacity {} is grater then totalNumberOfEventTickets {} and recurring event id {}", recurringEvent.get().getEventCapacity(), totalNumberOfEventTickets, recurringEventId);
                        typeSettingsDTO.setEventCapacity((double) totalNumberOfEventTickets);
                        typeSettingsDTO.setTotalEventCapacity((double) totalNumberOfEventTickets);
                    } else {
                        typeSettingsDTO.setEventCapacity(recurringEvent.get().getEventCapacity());
                        typeSettingsDTO.setTotalEventCapacity(recurringEvent.get().getEventCapacity());
                    }
                }
                if ((TRUE).equals(ticketingTypeSettingStatusDto.getShowRemainingTickets()) || (CUSTOM_TRUE).equals(ticketingTypeSettingStatusDto.getShowRemainingTickets())) {
                    typeSettingsDTO.setShowRemainingTickets(Boolean.valueOf(TRUE));
                } else {
                    typeSettingsDTO.setShowRemainingTickets(Boolean.valueOf(FALSE));
                }
                typeSettingsDTO.setLimitEventCapacity((TRUE).equals(ticketingTypeSettingStatusDto.getEventLimitCapacity()) || (CUSTOM_TRUE).equals(ticketingTypeSettingStatusDto.getEventLimitCapacity()));
            }
        } else {
            typeSettingsDTO.setLimitEventCapacity(ticketing.isLimitEventCapacity());
            typeSettingsDTO.setShowRemainingTickets(ticketing.isShowRemainingTickets());
            if (ticketing.isLimitEventCapacity()) {
                if (ticketing.isRecurringEvent() && !isNumberGreaterThanZero(recurringEventId)) {
                    setTotalEventCapacityForAllDatesRecurringEvent(typeSettingsDTO, event, recurringEventId);
                    List<TicketingType> numberOfTickets = ticketingTypeService.findAllByTicketing(recurringEventId, event);
                    long totalNumberOfEventTickets = numberOfTickets.stream().mapToLong(TicketingType::getNumberOfTickets).sum();
                    if ((totalNumberOfEventTickets < ticketing.getEventCapacity())) {
                        typeSettingsDTO.setEventCapacity((double) totalNumberOfEventTickets);
                    } else {
                        typeSettingsDTO.setEventCapacity(ticketing.getEventCapacity());
                    }
                } else {
                    typeSettingsDTO.setEventCapacity(ticketing.getEventCapacity());
                    typeSettingsDTO.setTotalEventCapacity(ticketing.getEventCapacity());
                }
            }
            typeSettingsDTO.setAllowTicketExchange(ticketing.isAllowTicketExchange());
        }
        typeSettingsDTO.setAllowEditingOfDisclaimer(ticketing.isAllowEditingOfDisclaimer());
        typeSettingsDTO.setCustomDisclaimer(ticketing.getCustomDisclaimer());
        typeSettingsDTO.setSeating(isNotBlank(ticketing.getChartKey()));
        typeSettingsDTO.setRequireDisclaimerConfirmation(ticketing.isRequireDisclaimerConfirmation());
        typeSettingsDTO.setAllowDisagreeDisclaimerConfirmation(ticketing.isAllowDisagreeDisclaimerConfirmation());
        typeSettingsDTO.setShowRegistrationButton(ticketing.isShowRegistrationButton());
        typeSettingsDTO.setShowTicketPrice(ticketing.isShowTicketPrice());
        typeSettingsDTO.setCustomInvoiceText(ticketing.getCustomInvoiceText());
        typeSettingsDTO.setExitIntentPopupEnabled(ticketing.isExitIntentPopupEnabled());
        return typeSettingsDTO;
    }

    private void setTotalEventCapacityForAllDatesRecurringEvent(NewTicketingTypeSettingDto typeSettingsDTO, Event event, Long recurringEventId) {
        List<RecurringEvents> recurringEvents = recurringEventsRepository.findByEventId(event);
        log.info("setTotalEventCapacityForAllDatesRecurringEvent | recurringEvents size {} and event id {}", recurringEvents.size(), event.getEventId());
        double totalEventCapacityFromAllDates = recurringEvents.stream().filter(recurringEvent -> recurringEvent.getEventCapacity() != null).mapToDouble(RecurringEvents::getEventCapacity).sum();
        List<TicketingType> ticketingTypeList = ticketingTypeCommonRepo.findAllByEventIdAndRecurringId(event, recurringEventId, TICKET);
        List<TicketingType> filteredTicketTypeList = ticketingTypeList.stream()
                .filter(ticketingType -> ticketingType.getRecurringEventId() != null && ticketingType.getRecurringEventId() > 0)
                .collect(Collectors.toList());
        log.info("setTotalEventCapacityForAllDatesRecurringEvent | filteredTicketTypeList size {} and event id {}", filteredTicketTypeList.size(), event.getEventId());
        long totalNumberOfEventTicketsFromAllDates = filteredTicketTypeList.stream().mapToLong(TicketingType::getNumberOfTickets).sum();
        if (totalNumberOfEventTicketsFromAllDates < totalEventCapacityFromAllDates) {
            log.info("setTotalEventCapacityForAllDatesRecurringEvent | totalNumberOfEventTicketsFromAllDates {} is grater then totalEventCapacityFromAllDates {}", totalNumberOfEventTicketsFromAllDates, totalEventCapacityFromAllDates);
            typeSettingsDTO.setTotalEventCapacity((double) totalNumberOfEventTicketsFromAllDates);
        } else {
            typeSettingsDTO.setTotalEventCapacity(totalEventCapacityFromAllDates);
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    public void saveTicketingTypeSettings(TicketingTypeSettingsDto settingsDto, Event event, Long recurringEventId) {
        log.info("saveTicketingTypeSettings event {} recurringEventId {} settingsDto {}", event.getEventId(), recurringEventId, settingsDto);

        if (StringUtils.length(settingsDto.getCustomDisclaimer()) >= 65535) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CHECKOUT_CUSTOM_DISCLAIMER_LIMIT);
        }
        Ticketing ticketing = ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event);
        log.info("Old ticketing data of event {} Is limit event capacity enable: {} , Event capacity: {}", event.getEventId(), ticketing.isLimitEventCapacity(), ticketing.getEventCapacity());
        log.info("New ticketing data of event {} Is limit event capacity enable: {} , Event capacity: {}", event.getEventId(), settingsDto.isLimitEventCapacity(), settingsDto.getEventCapacity());

        if (ticketing.isRecurringEvent()) {
            setShowRemainingTicketForRecurring(settingsDto, event, recurringEventId);
            setEventCapacityForRecurring(settingsDto, event, recurringEventId, ticketing);
        } else {
            checkEventCapacity(settingsDto, event);
            PlanConfig planConfig = getEventPlanConfigFromEventEntity(event);
            Calendar calendar = Calendar.getInstance();
            calendar.set(2024, 05, 05, 00, 00, 00);
            Date fixedJune52024Date = calendar.getTime();
            if (planConfig!=null && FREE_PLAN.getName().equals(planConfig.getPlanName()) && settingsDto.getEventCapacity()!=null && 100 <= settingsDto.getEventCapacity() && fixedJune52024Date.before(event.getCreatedDate()) && ticketing.isLimitEventCapacity()!=settingsDto.isLimitEventCapacity()) {
                NotAcceptableException.TicketingExceptionMsg exception = NotAcceptableException.TicketingExceptionMsg.EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS;
                exception.setErrorMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS);
                exception.setDeveloperMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS);
                log.info("saveTicketingTypeSettings throwExceptionForMoreEventCapacity: eventId {} errorMessage {}",event.getEventId(), exception.getErrorMessage());
                throw new NotAcceptableException(exception);
            }
            ticketing.setEventCapacity(settingsDto.getEventCapacity());
            ticketing.setShowRemainingTickets(null == settingsDto.getShowRemainingTickets() || settingsDto.getShowRemainingTickets());
            ticketing.setLimitEventCapacity(settingsDto.isLimitEventCapacity());
            if(settingsDto.isAllowTicketExchange() && !ticketing.isAllowTicketExchange()) {
                checkVatTaxAtTicketLevelIsPresentAndValidateTicketExchangeFlag(event);
            }
            ticketing.setAllowTicketExchange(settingsDto.isAllowTicketExchange());
        }
        checkValidEventCapacity(settingsDto);
        ticketing.setShowRegistrationButton(settingsDto.isShowRegistrationButton());
        ticketing.setAllowEditingOfDisclaimer(true);
        ticketing.setCustomDisclaimer(settingsDto.getCustomDisclaimer());
        ticketing.setRequireDisclaimerConfirmation(settingsDto.isRequireDisclaimerConfirmation());
        ticketing.setAllowDisagreeDisclaimerConfirmation(settingsDto.isAllowDisagreeDisclaimerConfirmation());
        ticketing.setShowTicketPrice(settingsDto.isShowTicketPrice());
        ticketing.setCustomInvoiceText(settingsDto.getCustomInvoiceText());
        ticketing.setExitIntentPopupEnabled(settingsDto.isExitIntentPopupEnabled());
        log.info("saveTicketingTypeSettings ticketing {}", ticketing); // NOSONAR
        ticketingService.save(ticketing);

        /*if (isSeatingFlagChanging(settingsDto, ticketing)) {
            if (settingsDto.getSeating()) {//NOSONAR
                //List<Category> categories = seatingCategoryService.getDefaultCategories(event, TICKET);//nosonar
                List<Category> categories = createCategoryForDefaultTicketTypeIfCategoryNotPresent(event, ticketing.isRecurringEvent());
                updateSeatsIOWithNewCategories(settingsDto.getSeating(), event, ticketing, categories);
            } else {
                ticketing.setChartKey(null);
                ticketingService.save(ticketing);
                ticketingTypeSeatingFlagChange(event);
            }
        }*/
        mapEventDetails(event);
    }

    private void checkVatTaxAtTicketLevelIsPresentAndValidateTicketExchangeFlag(Event event) {
        boolean checkVatTaxIsEnabledForAnyTicketType = ticketingTypeRepository.isVatEnabledInAnyTicketTypesForEvent(event);
        if (checkVatTaxIsEnabledForAnyTicketType) {
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.CANNOT_ENABLED_TICKET_EXCHANGE_TOGGLE);
        }
    }

    private PlanConfig getEventPlanConfigFromEventEntity(Event event) {
        if (event.getWhiteLabel() != null) {
            return event.getWhiteLabel().getPlanConfig();
        } else if (event.getOrganizer() != null) {
            return event.getOrganizer().getPlanConfig();
        } else {
            return chargebeePlanService.getFreePlanConfig();
        }
    }

    private void mapEventDetails(Event event) {
        Integration integration = getIntegrationDetails(event);
        if (integration != null) {
            trayIntegrationService.mapEventDetails(integration, event, TrayIOWebhookActionType.MAP_EVENT);
        }
    }

    private void mapTicketTypes(Event event, TicketingType createTicket, TrayIOWebhookActionType actionType) {
        Integration integration = getIntegrationDetails(event);
        if (integration != null) {
            trayIntegrationService.mapTicketTypes(integration, event, createTicket, actionType);
            trayIntegrationService.mapEventDetails(integration, event, TrayIOWebhookActionType.MAP_EVENT);
        }
    }

    private Integration getIntegrationDetails(Event event) {
        Optional<Integration> integrationOptional = Optional.empty();
        if (event.getWhiteLabel() != null) {
            log.info("request for update event details in custom integration WhiteLabel {}", event.getWhiteLabelId());
            integrationOptional = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);

        } else if (event.getOrganizer() != null) {
            log.info("request for update event details in custom integration Organizer {}", event.getOrganizerId());
            integrationOptional = integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getOrganizer().getId(), IntegrationSourceType.ORGANIZER, IntegrationType.TRAY_IO);
        }

        return integrationOptional.orElse(null);
    }

    private void checkValidEventCapacity(TicketingTypeSettingsDto settingsDto) {
        if (settingsDto.isLimitEventCapacity() && (!isNumberGreaterThanZero(settingsDto.getEventCapacity()))) {
            throw new NotAcceptableException(NOT_VALID_EVENT_CAPACITY);
        }
    }

    private void ticketingTypeSeatingFlagChange(Event event) {
        List<TicketingType> ticketingTypes = ticketingTypeRepository.findAllByEventIdRecurringIdNull(event);
        List<SeatingCategories> categories =
                ticketingTypes
                        .stream().map(e -> {
                            if (e != null && e.getCategory() != null) {
                                SeatingCategories category = e.getCategory();
                                category.setSeatingActive(true);
                                return category;
                            } else {
                                return null;
                            }
                        }).collect(Collectors.toList());
        categories.removeAll(Collections.singleton(null));
        if (!categories.isEmpty()) {
            seatingCategoryRepository.saveAll(categories);
        }
    }

    @Override
    public List<Category> createCategoryForDefaultTicketTypeIfCategoryNotPresent(Event event, boolean isRecurring) {
        List<TicketingType> ticketingTypes = ticketingTypeRepository.findAllByEventIdRecurringIdNull(event);
        log.info("createCategoryForDefaultTicketTypeIfCategoryNotPresent event {} isRecurring {} ticketingTypes {}", event.getEventId(), isRecurring, ticketingTypes);
        List<Category> categories =
                ticketingTypes
                        .stream().map(e -> {
                            if (e.isAllowAssignedSeating()) {
                                if (e.getCategoryId() == null) {
                                    EventCategoryDto category = seatingCategoryService.addCategory(event, new SeatingCategoryDto(e, false, false), true);
                                    e.setCategoryId(category.getId());
                                    return new Category(category.getId(), category.getName(), category.getColor());
                                } else {
                                    SeatingCategories seatingCategories = seatingCategoryRepository.findOneById(e.getCategoryId());
                                    if (seatingCategories != null) {
                                        seatingCategories.setSeatingActive(false);
                                        seatingCategoryService.save(seatingCategories);
                                        return new Category(seatingCategories.getId(), seatingCategories.getName(), seatingCategories.getColor());
                                    }
                                }
                            }
                            return null;
                        }).filter(Objects::nonNull) // filter out null elements
                        .collect(Collectors.toList());

        ticketingTypeService.saveAll(ticketingTypes);

        if (isRecurring) {
            List<TicketingType> allTicketTypes = ticketingTypeRepository.findAllPaidTicketsOfRecurringEventOnly(event.getEventId());
            ticketingTypes.forEach(e -> {
                List<TicketingType> recurringEventsTicketTypes =
                        allTicketTypes
                                .stream()
                                .filter(ticketType -> ticketType.getCreatedFrom() == e.getId())
                                .map(m -> {
                                    m.setCategoryId(e.getCategoryId());
                                    return m;
                                }).collect(Collectors.toList());
                ticketingTypeService.saveAll(recurringEventsTicketTypes);
            });
        }

        clearAPIGatewayCache.clearAPIGwTicketingTypesCategoriesCache(event.getEventURL());

        return categories;
    }

    private boolean isSeatingFlagChanging(TicketingTypeSettingsDto settings, Ticketing ticketing) {
        return settings.getSeating() != isNotBlank(ticketing.getChartKey());
    }

    protected void checkEventCapacity(TicketingTypeSettingsDto ticketingTypeSettingsDto, Event event) {
        BigDecimal totalSoldTickets = getTicketSoldCount(event);

        throwExceptionForMoreEventCapacity(ticketingTypeSettingsDto, totalSoldTickets.doubleValue());
    }

    private BigDecimal getTicketSoldCount(Event event) {
        return eventCommonRepoService.findSoldCountByEvent(event);
    }

    protected void setShowRemainingTicketForRecurring(TicketingTypeSettingsDto ticketingTypeSettingsDto, Event event, Long recurringEventId) {
        if (isNumberGreaterThanZero(recurringEventId)) {
            Optional<RecurringEvents> recurringEvent = recurringEventsRepository.findById(recurringEventId);
            if (recurringEvent.isPresent()) {
                RecurringEvents recurringEvents = recurringEvent.get();
                TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto = new TicketingTypeSettingStatusDto();
                if (Boolean.TRUE.equals(ticketingTypeSettingsDto.getShowRemainingTickets())) {
                    ticketingTypeSettingStatusDto.setShowRemainingTickets(CUSTOM_TRUE);
                } else {
                    ticketingTypeSettingStatusDto.setShowRemainingTickets(CUSTOM_FALSE);
                }
                if (ticketingTypeSettingsDto.isLimitEventCapacity()) {
                    ticketingTypeSettingStatusDto.setEventLimitCapacity(CUSTOM_TRUE);
                } else {
                    ticketingTypeSettingStatusDto.setEventLimitCapacity(CUSTOM_FALSE);
                }
                recurringEvents.setRecurringJson(this.parseToJsonString(ticketingTypeSettingStatusDto));

                //Check for event capacity
                BigInteger ticketSold = eventTicketsRepoService.getSoldCountByRecurringId(event, recurringEventId);
                throwExceptionForMoreEventCapacity(ticketingTypeSettingsDto, null != ticketSold ? ticketSold.doubleValue() : 0);

                recurringEvents.setEventCapacity(ticketingTypeSettingsDto.getEventCapacity());
                recurringEvents.setShowRemainingTicketsForRecurringEvent(RecurringEvents.ShowRemainingTicketsForRecurringEvent.valueOf(String.valueOf(ticketingTypeSettingStatusDto.getShowRemainingTickets()).toUpperCase()));
                recurringEventsRepository.save(recurringEvents);

            }
        }
    }

    private String getStringForRecurringJson(String jsonValueObject, String jsonValue) throws JSONException {
        JSONObject jsonObject = new JSONObject(jsonValueObject);
        return jsonObject.getString(jsonValue);
    }

    @Override
    public String parseToJsonString(TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto) {
        Gson gson = new Gson();
        return gson.toJson(ticketingTypeSettingStatusDto, TicketingTypeSettingStatusDto.class);
    }

    @Override
    public TicketingTypeSettingStatusDto parseJsonToObject(String jsonString) {
        Gson gson = new Gson();
        try {
            TicketingTypeSettingStatusDto dto = gson.fromJson(jsonString, TicketingTypeSettingStatusDto.class);
            if (dto == null) {
                dto = new TicketingTypeSettingStatusDto();
            }
            return dto;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new TicketingTypeSettingStatusDto();
        }

    }

    protected void setEventCapacityForRecurring(TicketingTypeSettingsDto ticketingTypeSettingsDto, Event event, Long recurringEventId, Ticketing ticketing) {
        if (!isNumberGreaterThanZero(recurringEventId)) {
            List<RecurringEvents> recurringEventList = recurringEventsScheduleService.getRecurringEventsByStatus(event);
            if (!CollectionUtils.isEmpty(recurringEventList)) {
                for (RecurringEvents recurringEvents : recurringEventList) {
                    String jsonValue = recurringEvents.getRecurringJson();
                    if (jsonValue != null && !jsonValue.isEmpty()) {
                        String eventLimitCapacity;
                        String showRemainingTickets;
                        TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto = new TicketingTypeSettingStatusDto();
                        try {
                            eventLimitCapacity = getStringForRecurringJson(jsonValue, "eventLimitCapacity");
                            showRemainingTickets = getStringForRecurringJson(jsonValue, "showRemainingTickets");
                        } catch (Exception e) {
                            throw new NotFoundException(NotFoundException.NotFound.valueOf(e.getMessage()));
                        }
                        if (eventLimitCapacity.equals(TRUE) || eventLimitCapacity.equals(FALSE)) {
                            recurringEvents.setEventCapacity(ticketingTypeSettingsDto.getEventCapacity());
                            ticketingTypeSettingStatusDto.setEventLimitCapacity(String.valueOf(ticketingTypeSettingsDto.isLimitEventCapacity()).toUpperCase());
                        }

                        if ((showRemainingTickets.equals(TRUE) || showRemainingTickets.equals(FALSE))) {
                            ticketingTypeSettingStatusDto.setShowRemainingTickets(String.valueOf(ticketingTypeSettingsDto.getShowRemainingTickets()).toUpperCase());
                            recurringEvents.setRecurringJson(this.parseToJsonString(ticketingTypeSettingStatusDto));
                            recurringEvents.setShowRemainingTicketsForRecurringEvent(RecurringEvents.ShowRemainingTicketsForRecurringEvent.valueOf(String.valueOf(ticketingTypeSettingsDto.getShowRemainingTickets()).toUpperCase()));
                        }
                    } else {
                        TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto = new TicketingTypeSettingStatusDto();
                        ticketingTypeSettingStatusDto.setShowRemainingTickets(String.valueOf(ticketingTypeSettingsDto.getShowRemainingTickets()).toUpperCase());
                        ticketingTypeSettingStatusDto.setEventLimitCapacity(String.valueOf(ticketingTypeSettingsDto.isLimitEventCapacity()).toUpperCase());
                        recurringEvents.setRecurringJson(this.parseToJsonString(ticketingTypeSettingStatusDto));
                        Organizer organiser =recurringEvents.getEventId().getOrganizer();
                        PlanConfig planConfig =organiser!=null? organiser.getPlanConfig():chargebeePlanService.getFreePlanConfig();
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(2024, 05, 05, 00, 00, 00);
                        Date fixedJune52024Date = calendar.getTime();
                        if (planConfig!=null && FREE_PLAN.getName().equals(planConfig.getPlanName()) && 100 <= ticketingTypeSettingsDto.getEventCapacity() && fixedJune52024Date.before(event.getCreatedDate())) {
                            NotAcceptableException.TicketingExceptionMsg exception = NotAcceptableException.TicketingExceptionMsg.EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS;
                            exception.setErrorMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS);
                            exception.setDeveloperMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS);
                            log.info("throwExceptionForMoreEventCapacity: eventId {} errorMessage {}",event.getEventId(), exception.getErrorMessage());
                            throw new NotAcceptableException(exception);
                        }
                        recurringEvents.setEventCapacity(ticketingTypeSettingsDto.getEventCapacity());
                    }
                }
            }
            checkEventCapacityForRecurring(ticketingTypeSettingsDto, event);
            recurringEventsRepository.saveAll(recurringEventList);
            ticketing.setEventCapacity(ticketingTypeSettingsDto.getEventCapacity());
            ticketing.setLimitEventCapacity(ticketingTypeSettingsDto.isLimitEventCapacity());
            ticketing.setShowRemainingTickets(ticketingTypeSettingsDto.getShowRemainingTickets());
        }
    }

    private void checkEventCapacityForRecurring(TicketingTypeSettingsDto ticketingTypeSettingsDto, Event event) {

        List<RecurringEventsTicketingTypeAndSoldCountDTO> soldCountDTOS = eventTicketsRepoService.getSoldCountByForEachRecDateByEventId(event);

        if (CollectionUtils.isNotEmpty(soldCountDTOS) && ticketingTypeSettingsDto.isLimitEventCapacity() && soldCountDTOS.stream()
                .allMatch(e -> e != null && e.getSoldTicketExcludingAddOns() != null && e.getSoldTicketExcludingAddOns() > ticketingTypeSettingsDto.getEventCapacity())) {

            Long soldCount = Collections.max(soldCountDTOS.stream().map(RecurringEventsTicketingTypeAndSoldCountDTO::getSoldTicketExcludingAddOns).collect(Collectors.toList()));

            NotAcceptableException.TicketingExceptionMsg exception = NotAcceptableException.TicketingExceptionMsg.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS;
            exception.setErrorMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS.replace(SOLD_TICKETS, String.valueOf((soldCount))));
            exception.setDeveloperMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS.replace(SOLD_TICKETS, String.valueOf(soldCount)));
            log.info("throwExceptionForMoreEventCapacity: {}", exception.getErrorMessage());
            throw new NotAcceptableException(exception);
        }
    }

    protected void throwExceptionForMoreEventCapacity(TicketingTypeSettingsDto ticketingTypeSettingsDto, double soldCount) {
        if (ticketingTypeSettingsDto.isLimitEventCapacity()
                && soldCount > ticketingTypeSettingsDto.getEventCapacity()) {

            NotAcceptableException.TicketingExceptionMsg exception = NotAcceptableException.TicketingExceptionMsg.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS;
            exception.setErrorMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS.replace(SOLD_TICKETS, String.valueOf(((int) soldCount))));
            exception.setDeveloperMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS.replace(SOLD_TICKETS, String.valueOf((int) soldCount)));
            log.info("throwExceptionForMoreEventCapacity: {}", exception.getErrorMessage());
            throw new NotAcceptableException(exception);
        }
    }

    /*
     * below code is added for if we remove ticket or category and seating is enabled, so it will also affect seat charts too
     */
    @Override
    public void removeCategoryFromSeatIoChart(Event event) {
        Ticketing ticketing = eventService.findTicketingByEvent(event);
        boolean isSeatingEnable = StringUtils.isNotBlank(ticketing.getChartKey());
        if (isSeatingEnable) {
            updateSeatsIOWithNewCategories(true, event, ticketing, seatingCategoryService.getDefaultCategories(event, TICKET));
        }
    }

    @Override
    public void updateRegistrationRequiredAttributesForNewTicket(Event event, TicketingType ticketingType) {

        List<RegistrationAttribute> registrationAttributes = registrationAttributeRepository.findByEventidAndRecurringEventIdNull(event, ticketingType.getRecurringEventId());
        List<RegistrationAttribute> updateRegistrationAttribute = new ArrayList<>();

        registrationAttributes
                .forEach(attribute -> {

                    boolean updateAttribute = false;
                    // TODO : Will check this second condition later //NOSONAR
                    if (attribute.isEnabled()) {
                        attribute.setTicketTypeIds(appendTicketTypeId(ticketingType, attribute.getTicketTypeIds()));
                        updateAttribute = true;

                    }

                    if (updateAttribute) {
                        updateRegistrationAttribute.add(attribute);
                    }

                });

        if (!updateRegistrationAttribute.isEmpty()) {
            registrationAttributeRepository.saveAll(updateRegistrationAttribute);
        }
    }

    public boolean checkEventTimeDiffWithCurrentTime(Date eventsDate) {
        log.info("checkEventTimeDiffWithCurrentTime eventsDate {} ", eventsDate);
        Date currentTime = Calendar.getInstance().getTime();
        if (eventsDate.after(currentTime)) {
            return Boolean.FALSE;
        }
        long differenceInMinutes = currentTime.getTime() - eventsDate.getTime();
        return TimeUnit.MILLISECONDS.toMinutes(differenceInMinutes) > 60;
    }

    public void updateAddOnTicketTypesData(TicketingType ticketingType, Event event, String ticketingTypeOperation, Ticketing ticketing) {
        try {
            log.info("Update Add on ticket types data having eventId {} , TicketingType {} , ticketingTypeOperation {}",
                    event.getEventId(), ticketingType.getId(), ticketingTypeOperation);

            List<TicketingType> ticketingTypeList = Collections.emptyList();
            if(ticketing.isRecurringEvent() && null == ticketingType.getRecurringEventId()) {
                if(ADD.equalsIgnoreCase(ticketingTypeOperation)){
                    ticketingTypeList = ticketingTypeRepository.findAllByEventAndIdOrCreatedFromOrIsAllTicketTypesSelectedForAddOn(event, ticketingType.getId());
                }else if (DELETE.equalsIgnoreCase(ticketingTypeOperation)) {
                    ticketingTypeList =  ticketingTypeRepository.findAllByEventAndIdOrCreatedFromOrIsAllTicketTypesSelectedForAddOnOrDataType(event, ticketingType.getId(), ADDON);
    }
                Set<Long> recurringEventIdSet = Stream.concat(Stream.of(-1L), ticketingTypeList.stream()
                                    .map(TicketingType::getRecurringEventId)
                                    .filter(Objects::nonNull))
                            .collect(Collectors.toSet());
                    Map<Long, List<TicketingType>> mapRecurringEventIdWithTicketingType = ticketingTypeList.stream()
                            .collect(Collectors.groupingBy(
                                    ticket -> (ticket.getRecurringEventId() != null) ? ticket.getRecurringEventId() : -1L,
                                    Collectors.toList()
                            ));
                    for(Long recurringEventId : recurringEventIdSet) {
                        List<TicketingType>  localTicketingTypes = mapRecurringEventIdWithTicketingType.get(recurringEventId);
                        Long ticketTicketingTypeId = localTicketingTypes.stream()
                                .filter(e -> e.getDataType() == TICKET)
                                .map(TicketingType::getId)
                                .findFirst()
                                .orElse(null);
                localTicketingTypes.stream()
                        .filter(t -> t.getDataType() == ADDON)
                        .forEach(loopTicketingType ->
                                loopTicketingType.setListOfTicketTypesForAddOn(
                                        updateCommaSeparatedList(loopTicketingType.getListOfTicketTypesForAddOn(), ticketTicketingTypeId, ticketingTypeOperation)
                                )
                        );
            }

            ticketingTypeRepository.saveAll(ticketingTypeList);
                    return;
                }else if(ticketingType.getTicketing().isRecurringEvent() && null != ticketingType.getRecurringEventId()) {
                if(ADD.equalsIgnoreCase(ticketingTypeOperation)){
                    ticketingTypeList = ticketingTypeRepository.findAlLByEventAndRecurringEventIdAndDataTypeAddOnAndIsAllTicketTypesSelectedForAddOn(event,ticketingType.getRecurringEventId(),DataType.ADDON);
                }else if (DELETE.equalsIgnoreCase(ticketingTypeOperation)) {
                    ticketingTypeList =  ticketingTypeRepository.findAlLByEventAndRecurringEventIdAndDataTypeAddOn(event, ticketingType.getRecurringEventId(), ADDON);
                }
            }else {
                if(ADD.equalsIgnoreCase(ticketingTypeOperation)){
                    ticketingTypeList = ticketingTypeRepository.findAllByEventAndDataTypeAndIsAllTicketTypesSelectedForAddOn(event, DataType.ADDON);
                }else if (DELETE.equalsIgnoreCase(ticketingTypeOperation)) {
                    ticketingTypeList =  ticketingTypeRepository.findAllByEventAndDataType(event, ADDON);
        }
    }

            if (!ticketingTypeList.isEmpty()) {
                ticketingTypeList.forEach(loopTicketingType ->
                        loopTicketingType.setListOfTicketTypesForAddOn(
                                updateCommaSeparatedList(loopTicketingType.getListOfTicketTypesForAddOn(), ticketingType.getId(), ticketingTypeOperation)
                        )
                );
                ticketingTypeRepository.saveAll(ticketingTypeList);
    }
        } catch (Exception ex) {
            log.error("Error in updateAddOnTicketTypesData for eventId: {}, ticketTypeId: {}. errorMessage: {}",
                    event.getEventId(), ticketingType.getId(), ex.getMessage());
        }
    }

    @Override
    public void updateDisplayCodeTicketTypesData(TicketingType ticketingType, Event event, String delete) {
        List<TicketingLimitedDisplayCode>  limitedDisplayCodes = ticketingLimitedDisplayCodeService.findAllByEventId(event);
        if(!CollectionUtils.isEmpty(limitedDisplayCodes)){
        List<TicketingType> allByEventAndIdOrCreatedFrom = ticketingTypeRepository.findAllByEventAndIdOrCreatedFrom(event, ticketingType.getId());
        allByEventAndIdOrCreatedFrom.stream().forEach(ticketingType1 -> {
            limitedDisplayCodes.forEach(limitedDisplayCode -> {
                limitedDisplayCode.setEventTicketTypeId(
                        updateCommaSeparatedList(limitedDisplayCode.getEventTicketTypeId(), ticketingType1.getId(), delete)
                );
            });
        });
            ticketingLimitedDisplayCodeService.saveAll(limitedDisplayCodes);
        }
    }

    private String updateCommaSeparatedList(String commaSeparatedTicketTypesId, Long ticketingTypeId, String ticketingTypeOperation) {
        log.info("Update List commaSeparatedTicketTypesId {} and ticketTypeId {}", commaSeparatedTicketTypesId, ticketingTypeId);
        List<Long> updatedList = GeneralUtils.convertCommaSeparatedToListLong(commaSeparatedTicketTypesId);
        if (ADD.equalsIgnoreCase(ticketingTypeOperation)) {
            updatedList.add(ticketingTypeId);
        } else if (DELETE.equalsIgnoreCase(ticketingTypeOperation)) {
            updatedList.remove(ticketingTypeId);
        }
        return GeneralUtils.convertLongListToCommaSeparated(updatedList);
    }

    @Override
    public void updateCustomLobbyTabData(TicketingType ticketingType, Event event, String ticketingTypeOperation) {
        try {
            log.info("updateCustomLobbyTabData ticketingTypeId {}, eventId {}, ticketingTypeOperation {}", ticketingType.getId(), event.getEventId(), ticketingTypeOperation);
            VirtualEventSettings virtualEventSettings = roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId());
            if(null == virtualEventSettings){
                log.info("No virtualEventSettings found for eventId: {}",event.getEventId());
                return;
            }
            String virtualTabs = virtualEventSettings.getVirtualTabs();
            Map<String, List<VirtualEventTabsDTO>> oldVirtualTabsMap = challengeConfigService.getVirtualTabsInMap(virtualTabs);

            List<VirtualEventTabsDTO> oldLobbyTabList = oldVirtualTabsMap.getOrDefault(LOBBY_TAB, Collections.emptyList());
            List<VirtualEventTabsDTO> newLobbyTab = filterDefaultLobbyTabs(oldLobbyTabList);

            boolean updateVirtualSettingFlag = updateLobbyTabs(ticketingType, ticketingTypeOperation, oldLobbyTabList, newLobbyTab);

            if (updateVirtualSettingFlag) {
                updateVirtualEventSettings(virtualEventSettings, oldVirtualTabsMap, oldLobbyTabList, event);
            }
        } catch (Exception ex) {
            log.error("updateCustomLobbyTabData ticketingTypeId {}, eventId {}, ticketingTypeOperation {} errorMsg {}", ticketingType.getId(), event.getEventId(), ticketingTypeOperation, ex.getMessage());
        }
    }

    private List<VirtualEventTabsDTO> filterDefaultLobbyTabs(List<VirtualEventTabsDTO> oldLobbyTabList) {
        List<String> defaultLobbyTabs = Arrays.asList("Feed", "Agenda", "Sponsors", "Info Desk", "Gamification", "Show Challenges");
        return oldLobbyTabList.stream()
                .filter(tab -> !defaultLobbyTabs.contains(tab.getKey()))
                .collect(Collectors.toList());
    }

    private boolean updateLobbyTabs(TicketingType ticketingType, String ticketingTypeOperation, List<VirtualEventTabsDTO> oldLobbyTabList, List<VirtualEventTabsDTO> newLobbyTab) {
        boolean updateFlag = false;

        for (VirtualEventTabsDTO virtualEventTabsDTO : newLobbyTab) {
            if (ADD.equalsIgnoreCase(ticketingTypeOperation) && virtualEventTabsDTO.isAllTicketingTypeSelected() && isNotBlank(virtualEventTabsDTO.getListOfTicketingTypes())) {
                updateTabList(oldLobbyTabList, virtualEventTabsDTO, ticketingType.getId(), ADD);
                updateFlag = true;
            } else if (DELETE.equalsIgnoreCase(ticketingTypeOperation) && isNotBlank(virtualEventTabsDTO.getListOfTicketingTypes())
                    && GeneralUtils.convertCommaSeparatedToListLong(virtualEventTabsDTO.getListOfTicketingTypes()).contains(ticketingType.getId())) {
                updateTabList(oldLobbyTabList, virtualEventTabsDTO, ticketingType.getId(), DELETE);
                updateFlag = true;
            }
        }

        return updateFlag;
    }

    private void updateTabList(List<VirtualEventTabsDTO> oldLobbyTabList, VirtualEventTabsDTO tabDTO, Long ticketingTypeId, String operation) {
        oldLobbyTabList.stream()
                .filter(oldTab -> oldTab.getKey().equalsIgnoreCase(tabDTO.getKey()))
                .forEach(oldTab -> oldTab.setListOfTicketingTypes(updateCommaSeparatedList(tabDTO.getListOfTicketingTypes(), ticketingTypeId, operation)));
    }

    private void updateVirtualEventSettings(VirtualEventSettings virtualEventSettings, Map<String, List<VirtualEventTabsDTO>> oldVirtualTabsMap, List<VirtualEventTabsDTO> oldLobbyTabList, Event event) {
        oldVirtualTabsMap.put(LOBBY_TAB, oldLobbyTabList);
        Gson gson = new Gson();
        virtualEventSettings.setVirtualTabs(gson.toJson(oldVirtualTabsMap));
        virtualEventSettingsRepoService.save(virtualEventSettings);
        eventRepoService.clearCacheEventDetailsProcess(event.getEventURL());
        log.info("Virtual Event Settings updated from updateCustomLobbyTabData");
    }

    private void validateTicketExchangeIsAllowed(Ticketing ticketing){
        if(ticketing.isAllowTicketExchange()){
            throw new NotAcceptableException(NotAcceptableException.TicketingExceptionMsg.CANNOT_ENABLE_VAT_TAX_AT_TICKET_LEVEL);
        }
    }

    @Autowired
    public List<TimeZone> getAllTimeZonesForEvent(){
       return TimeZoneUtil.getAllTimeZone();
    }

}