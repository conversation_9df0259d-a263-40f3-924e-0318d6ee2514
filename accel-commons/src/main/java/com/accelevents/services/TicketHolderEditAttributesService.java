package com.accelevents.services;

import com.accelevents.common.dto.HolderBasicDetailsDto;
import com.accelevents.common.dto.UserBasicDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.*;
import com.accelevents.ticketing.dto.DisplayAttributeDto;

import javax.xml.bind.JAXBException;
import java.util.List;
import java.util.Optional;

public interface TicketHolderEditAttributesService {

	HolderDisplayAttributesDto getTicketHolderAttributes(long ticketId, Event event) throws JAXBException;

    HolderDisplayAttributesDto getTicketHolderAttributes(long ticketId, Long userId) throws JAXBException;

	void updateTicketHolderData(long ticketid, Long loggedInUser, AttendeeAttributeValueDto holderAttributesDto) throws JAXBException;

	void updateTicketPurchaserDataByOrder(long orderId, Long loggedInUser, AttendeeAttributeValueDto holderAttributesDto) throws JAXBException;

    List<DisplayAttributeDto> getGenderAndAgeTicketHoldersAttribute(EventTickets eventTickets, List<TicketHolderRequiredAttributes> holderRequiredAttributes, Boolean enableForTicketHolder) throws JAXBException;

    EventTickets blockUnblock(long ticketId, boolean block, Event event);

    /**
     * added userId with orderId to fetch only users order details so other user is not able to get orders data by orderId.
     */
    HolderDisplayAttributesDto getTicketPurchaserAttributesForMyProfile(long orderId, User user, DataType dataType) throws JAXBException;

    /**
     * added event with orderId to fetch all the orders details by admin, super-admin which fall in the particular event.
     */
    HolderDisplayAttributesDto getTicketPurchaserAttributesForHost(long orderId, Event event, DataType dataType) throws JAXBException;

    List<HolderDisplayAttributesDto> getTicketHolderDataAttributes(List<Long> ticketId, Long userId) throws JAXBException;

    HolderBasicDetailsDto getHolderBasicDetailsByBarcodeIdOrRfIdTag(String barcodeId, Event event, boolean isRfidTag);

    List<HolderBadgePdfAttributesDto> getTicketHoldersAttributeByEventTickets(List<EventTickets> eventTickets, Event event);

    Optional<HolderDisplayAttributesDto> getTicketHolderAttributeByEventAndUserId(User loggedInUser, Event event, Long userId, boolean isExhibitorLeadOrAboveRole) throws JAXBException;

    void transferTicket(UserBasicDto userBasicDto, List<Long> ticketIdsOrOrderId, User loggedInUser, boolean isFromUserProfile);

    List<KeyValueLongBooleanDto> getTicketsTransferLimit(List<Long> ticketIds, Event event, User user) ;

    List<Long> getAdditionalHiddenOrderInfoRequired(Event event, User loggedInUser);
}
