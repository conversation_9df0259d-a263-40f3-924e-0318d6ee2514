package com.accelevents.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.WaitListSettings;
import com.accelevents.domain.enums.Status;
import com.accelevents.dto.WaitListSettingsDto;

import java.util.List;
import java.util.Optional;

public interface WaitListSettingService {

    WaitListSettingsDto getWaitListSettingsByEvent(Event event, Long recurringEventId);
    Optional<WaitListSettings> findById(Long waitListSettingsId);

    void save(Event event, WaitListSettingsDto waitListSettingsDto);

    List<WaitListSettings> getAllWaitListSettings();

    List<Long> getAllWaitListSettingsIds();

    void save(WaitListSettings newWLS,String eventUrl);

    void updateStatusDeleted(Long eventId, Status status);

    List<WaitListSettings> getAllWaitListSettingsByEventIds(List<Long> eventIds);
}
