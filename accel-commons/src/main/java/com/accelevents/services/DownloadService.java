package com.accelevents.services;

import com.accelevents.common.dto.SurveyConfigDto;
import com.accelevents.common.dto.SurveysDto;
import com.accelevents.continuing.ed.ContinuingEdConstants;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.exhibitors.ExhibitorSetting;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.AnalyticsReportDTO;
import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.messages.EnumQNAType;
import com.accelevents.services.dynamodb.user.activity.AttendeeTimelineDTO;
import com.accelevents.services.dynamodb.user.activity.UserActivity;
import com.accelevents.session_speakers.dto.AttendeeSession;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DownloadService {

    void downloadcartAbandonedRegistrantsData(PrintWriter writer, Event event, Long recurringEventsId);

    void downloadCSVFile(PrintWriter writer, List<String> headerList, List<List<String>> dataRowList);

    void downloadSilentAuctionAllBidderData(PrintWriter writer, Event event);

	void downloadRaffleParticipantData(PrintWriter writer, Event envet);

	void downloadCauseAuctionDonorData(PrintWriter writer, Event event);

	void downloadSilentAuctionWinnerData(PrintWriter writer, Event event);

	void downloadRaffleWinnerData(PrintWriter writer, Event event);
	
	void downloadSilenAuctionItemsCSV(PrintWriter writer, Auction auction);
	
	void downloadRaffleItemsCSV(PrintWriter writer, Raffle raffle);
	
	void downloadCauseAuctionItemsCSV(PrintWriter writer, CauseAuction causeAuction);
	
	void downloadTicketHolderData(PrintWriter writer, Event event, User loggedInUser, Long recurringEventId, DataType dateType, String startTime, String endTime);

	void downloadTicketBuyerData(PrintWriter writer, Event event, Long recurringEventsId, DataType dataType, String startTime, String endTime);

    void downloadTicketBuyerAndHolderData(PrintWriter writer, Event event, Long recurringEventsId);

    void downloadDeletedTicketBuyerAndHolderData(PrintWriter writer, Event event, Long recurringEventsId);

    void downloadAttendeeDataWithTicketPriceBreakDown(PrintWriter writer, Event event);

    void downloadDonationPerformanceData(PrintWriter writer, Event event);

	void downloadPurchasedRaffleTicketData(PrintWriter writer, Event event);

	void downloadEventTicketingSaleData(PrintWriter writer);

	void downloadBidderRegistationDataCSV(PrintWriter writer, Event event);

	void downloadAllContactDataCSV(PrintWriter writer, Event event, List<Long> ids,Long contactsListId);

    void downloadAttendeeNumberDataCSV(PrintWriter writer, Event event);

    void downloadLeadsCSV(PrintWriter writer, Long exhibitorId, Staff staff, Event event, ExhibitorSetting defaultExhibitorSetting, Ticketing ticketing, Map<Long,
            AttendeeProfileDto> attendeeProfileDtoMap, List<Staff> allStaffByEvent, Map<Long,ExhibitorSetting> exhibitorSettingMap);

    void downloadLeadHistoryCSV(PrintWriter writer, Long exhibitorId, Staff staff, User loggedInUser, Event event);

    void downloadLeadsCSVForAllExhibitor(PrintWriter writer, Event event);

    void downloadRegistrantsCSV(PrintWriter writer, Session session);

    void downloadChatCSV(PrintWriter writer, String channelId, String channelName, String channelType,Event event);

	void downloadQuestionAnswers(PrintWriter writer, long typeId, EnumQNAType type, Event event, boolean requiredHolderData);

    void downloadPolls(PrintWriter writer, long typeId, EnumQNAType type, Event event, Boolean requiredHolderData);

	void downloadExhibitor(PrintWriter writer, Long exhibitorId, Event event,User user);

	void downloadAttendeeSessions(PrintWriter writer, List<AttendeeSession> attendeeSessions,Long userId);

	void downloadSessionAttendeesCSV(PrintWriter writer, Session session);

    void downloadLeaderboardDetailDataCSV(PrintWriter writer, Event event);

    void downloadAllAttendeeSessionsInfoCSVAsync(Event event, User user);
	void downloadAllAttendeeSessionsInfoCSV(PrintWriter writer, Event event);

    void downloadMatchesUsersCSVForAllMeetUpSession(PrintWriter writer, Event event);

	void downloadSessionListCSV(PrintWriter writer, Event event);

    void downloadEventBillingAttendeeData(PrintWriter writer, Event event);

    void downloadLoungeUsersCSVAllSession(PrintWriter writer, Event event);

    void downloadLeaderboardDataCSVByChallengeId(PrintWriter writer, Event event, long challengeId);

    void downloadLeaderBoardDataCSV(PrintWriter writer, Event event) throws IOException;

    void downloadSessionAttendanceReport(PrintWriter writer, Long sessionId, Event event);

    void downloadMobileAnalyticsReport(PrintWriter writer, Event event);

    void downloadSessionVideoAnalyticsGraphDataCSV(PrintWriter writer, Long sessionId, Event event);

    void downloadSessionOverviewReport(PrintWriter writer, Event event);

    void downloadNetworkingLoungeAttendanceReport(HttpServletResponse response, String loungeId, long eventId);

    void downloadChatCSVForLounge(HttpServletResponse response, String loungeId, Event event);

    void downloadAttendeeLounges(HttpServletResponse response, String userId, Event event);

    void downloadLoungeFeed(HttpServletResponse response, Event event, String loungeId, User user, int page, int size);

    void downloadConnectedAttendeeDetails(User user, Event event, HttpServletResponse response);

    void downloadConnectedAttendeeFromActivityPage(User user, Event event, HttpServletResponse response);

    void downloadSpeakerEmailsDataCSV(PrintWriter writer, Event event);

    void downloadExpoStaffDataCSV(PrintWriter writer, Event event);

    void getUnsubscribedUsersData(HttpServletResponse response, Long eventId);

    void downloadSponsorAnalyticDataCSV(PrintWriter writer, Event event);

    void downloadZIPForAnalytics(AnalyticsReportDTO analyticsReportDTO, HttpServletResponse response, Event event, User user) throws IOException;

    void getCheckedInUsersDetails(HttpServletResponse response, Event event);

    void getAttendeeOverageByOrganizer(HttpServletResponse response, Long organizerId);

    void getAttendeeOverageByOrganizerDataFix(HttpServletResponse response, Long organizerId);

    void getAttendeeOveragesByWhitelabel(HttpServletResponse response, WhiteLabel whiteLabel);

    void downloadAttendeeDetails(User user, Event event, HttpServletResponse response);

    void downloadAllEventUsagesPerEvent(HttpServletResponse response, Date startDate, Date endDate);

    void downloadAllTicketFeesPerEvent(HttpServletResponse response , Date startDate, Date endDate);

    void downloadUserTimeLineForParticularAction(PrintWriter writer, List<UserActivity> userActivities, Map<Long,String> userIds, String eventTimezone);

    void downloadAttendeeAnalyticsCSV(PrintWriter writer, Event event, User user,Long displayViewId,String searchString);

    void downloadUserActivityReport(PrintWriter printWriter, List<AttendeeTimelineDTO> attendeeTimelineDTOList,User user);

    void downloadContinuingEdAnalyticsCSV(PrintWriter writer, Long criteriaId, ContinuingEdConstants.ContinuingEdStatus status, User loggedInUser, Event event, PageSizeSearchObj pageSizeSearchObj);

    void downloadContinuingEdUserAnalyticsCSV(PrintWriter writer, Long criteriaId, User user, Event event);

    void downloadContinuingEdUserAnalyticsPDF( Long criteriaId, User user, Event event, HttpServletResponse response) throws IOException;

    void downloadAllSurveys(User user, Event event, List<SurveyConfigDto> surveyConfigDtos, Map<Long, Long> countBySurveys, HttpServletResponse response);

    void downloadSurveySubmissions(List<SurveyResponse> surveyResponses, SurveyConfiguration surveyConfiguration, User user, Event event, HttpServletResponse response);

    void downloadSurveySubmissionsData(List<SurveysDto> surveysDtoList, Event event, HttpServletResponse response);

    void downloadSessionCheckInLogCSV(PrintWriter writer, Session session);

    void downloadMeetingReports(PrintWriter writer, String status, Event event);
    void downloadEmailTrackingData(HttpServletResponse response, Event event,String charEncode,String holderEmail);

    void getAttendeeOveragesByWhitelabelDataFix(HttpServletResponse response, String whiteLabel);

    void downloadEventTaskDataCSV(HttpServletResponse response, Event event,Long taskId);

    void downloadLeaderboardDataCSVByTierId(PrintWriter writer,User user, Event event, Long tierId);

    void downloadTiersLeaderboardCSVFile(PrintWriter writer, User user, Event event) throws IOException;

    void downloadSessionLocationListCSV(PrintWriter writer, Event event);

    void downloadContinuingEdCreditManagementCSV(PrintWriter writer,  User user, Event event);

    void downloadSharingViewDataCSV(PrintWriter writer, Event event, String displayViewId, String searchString);

    void attendeeCheckInLogs(Event eventId, PrintWriter writer);

    void downloadWaitlistedUsersCSV(PrintWriter writer, Session session);

    void extractCommonExhibitorData(PrintWriter writer, Long exhibitorId, Staff staff, Event event, Map<Long,ExhibitorSetting> exhibitorSettingMap);

}

