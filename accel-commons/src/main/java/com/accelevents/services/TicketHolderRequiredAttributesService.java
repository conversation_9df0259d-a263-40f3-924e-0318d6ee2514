package com.accelevents.services;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.ticketing.dto.AttributesDefaultValuesDto;

import java.util.List;
import java.util.Optional;

public interface TicketHolderRequiredAttributesService {

	List<TicketHolderRequiredAttributes> getAllAttributes(Event event);

    List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketPurchaser(Event event);

    List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketPurchaserExcludingConditionalQue(Event event);

    List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketHolder(Event event);

    TicketHolderRequiredAttributes findByattributeValueTypeAndEventidAndRecurringEventId(AttributeValueType attributeValueType, Event event, Long recurringEventId);

    List<TicketHolderRequiredAttributes> getAllAttributesOrderByAttributeOrder(Event event);

	int findHighestAttributePosition(Event event, boolean isBuyerAttribute);

    int findHighestAttributePosition(Event event, boolean isBuyerAttribute, DataType dataType);
	
	void deleteCustomAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes, boolean isDeletedFromBuyer, Long recurringEventId, Event event);
	
	Optional<TicketHolderRequiredAttributes> findCustomAttributeById(long attributeId);

    List<TicketHolderRequiredAttributes> findByEventId(Event eventId);

	TicketHolderRequiredAttributes findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType attributeValueType, Event event);

	Optional<TicketHolderRequiredAttributes> getTicketHolderAttributeById(long id);

    TicketHolderRequiredAttributes getNextPositionAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes);

	TicketHolderRequiredAttributes getPreviousPositionAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes);

	void  updatePositionInGroup(int startPosition, int endPosition, int positionDifferent, Event event);

	void  updatePositionForAllAttribute(Event event, int sequence, DataType dataType);

	void saveAll(List<TicketHolderRequiredAttributes> newTicketHolderRequiredAttributes);

	TicketHolderRequiredAttributes saveDefaultTicketAttributes(TicketHolderRequiredAttributes newTicketHolderRequiredAttributes);

	List<TicketHolderRequiredAttributes> getAllAttributesBynameAndEventAndRecurringIdNotNull(String atrributeName, Event event);

    List<TicketHolderRequiredAttributes> getAllAttributesExcludingSubQueByRecurringEventId(Event event, Long recurringEventId, DataType dataType);

    List<TicketHolderRequiredAttributes> getAllAttributesEnabledForTicketHolderExcludingConditionalQue(Event event);

    TicketHolderRequiredAttributes findBynameAndEventidRecurringEventId(String atrributeName, Event event, Long recurringEventId, DataType dataType);
    void deleteByRecurringIdAndEventId(List<Long> recurringEventsToBeDelete, Event event);

	List<TicketHolderRequiredAttributes> getAllAttributesByCreatedFromAndEventId(Long createdFrom, Event event);

    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributes(Event event, Long recurringEventId);


    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesOrderByAttributeOrder(Event event, Long recurringEventId, DataType dataType);

    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesHavingEnableForHolderOrderByAttributeOrder(Event event, Long recurringEventId, DataType dataType);

    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesHavingEnableForBuyerOrderByAttributeOrder(Event event, Long recurringEventId, DataType dataType);

    List<TicketHolderRequiredAttributes> getTicketingAttributesByRecurringEventList(List<Long> oldReIdsList);

    List<TicketHolderRequiredAttributes> findByEventIdHavingRecurringEventIdNotNull(Event eventId, String attributeName);

    List<TicketHolderRequiredAttributes> getAllWhichHavingRecurringEventIdNotNull(Event event);

	List<TicketHolderRequiredAttributes> getAllAddonCustomAttributes(Event event, boolean isForBuyer);


    List<TicketHolderRequiredAttributes> getEnabledAddonCustomAttributesOrderByBuyerAttributeOrder(Event event, Long recurringEventId);

    List<TicketHolderRequiredAttributes> getEnabledAddonCustomAttributesOrderByHolderAttributeOrder(Event event, Long recurringEventId);

    void updateTicketHolderAttributeRecStatusByRecurringEventIds(List<Long> recurringEventIds, Event event, RecordStatus cancel);

	List<TicketHolderRequiredAttributes> findByIdsIn(List<Long> ticketHolderAttributeReqIds);

    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesByExhibitor(Event event,List<String> attributeList);

    TicketHolderRequiredAttributes getNextPositionForHolderAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes);

    TicketHolderRequiredAttributes getPreviousPositionForHolderAttribute(TicketHolderRequiredAttributes ticketHolderRequiredAttributes);

    void updatePositionForHolderInGroup(int startPosition, int endPosition, int positionDifferent, Event event);

    void updatePositionForAllHolderAttribute(Event event, int sequence, DataType dataType);

    List<TicketHolderRequiredAttributes> getEventidAndRecurringEventIdNullAndTicketAndAddOnDataType(Event event);

    List<String> getHolderOrBuyerEnabledAttribute(Event event);

    List<String> getHolderEnabledAttribute(Event event);

    List<TicketHolderRequiredAttributes> findAllSubQueByParentIdAndEventId(Event event, long parentQueId);

    List<TicketHolderRequiredAttributes> findAllSubQueByParentIdsAndEventId(Event event, List<Long> parentQueIds);

    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesForConditionalQuestions(long attributeId, Event event, boolean isForBuyer);


    List<TicketHolderRequiredAttributes> getAllConditionalQuestionByEventAndDataType(Event event, DataType dataType);

    Optional<TicketHolderRequiredAttributes> findById(Long ticketHolderAttributeId);

    void disableInterestAttribute(Event event);

    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesOrderByAttributeOrder(List<Event> event, DataType dataType);

    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesBasedOnAttributeType(Event event, AttributeValueType dropdown);

    List<String> findTicketHolderRequiredAttributesByEventIdAndEnabledForHolders(Event eventd);

    List<String> findTicketHolderRequiredAttributesByEventIdAndAddon(Event event);

    List<TicketHolderRequiredAttributes> findAllByAttributeNamesAndEventIdAndRecurringEventIdIsNULLAndDataType(List<String> attributeNames, Event event, DataType dataType);

    List<TicketHolderRequiredAttributes> getBuyersAttributesByIdsAndEnabledForTicketBuyer(List<Long> ids, List<String> attributeNames, long eventId);

    List<TicketHolderRequiredAttributes> getHolderAttributesByIdsAndEnabledForTicketHolder(List<Long> ids, List<String> attributeNames, long eventId);

    List<TicketHolderRequiredAttributes> findByEventAndIds(Event event, List<Long> ticketAttributeIds);

    TicketHolderRequiredAttributes findAllSubQueByParentIdAndEventIdAndAnswerId(Event event, long parentQues, Long ansId);

    List<TicketHolderRequiredAttributes> getBuyerAttributesExcludingTypes(Event event, Long recurringEventId, DataType dataType, List<AttributeValueType> excludedTypes);

    List<TicketHolderRequiredAttributes> getHolderAttributesExcludingTypes(Event event, Long recurringEventId, DataType dataType, List<AttributeValueType> excludedTypes);

    List<AttributesDefaultValuesDto> getAttributesDefaultValuesByEvent(Event event);

    List<TicketHolderRequiredAttributes> findAllWithDefaultValueNotNullForPurchaser(Long eventId, Long ticketTypeId);

    List<TicketHolderRequiredAttributes> findAllWithDefaultValueNotNullForHolder(Long eventId, Long ticketTypeId);

    List<TicketHolderRequiredAttributes> findAllAddOnAttributeWithDefaultValueNotNullForPurchaser(Long eventId, Long addonTicketTypeId);

    List<String> findAllRequiredAttributeNames(Long eventId, Long recurringEventId);
}

