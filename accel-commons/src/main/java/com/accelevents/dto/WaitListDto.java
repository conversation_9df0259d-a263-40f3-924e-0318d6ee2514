package com.accelevents.dto;

import com.accelevents.domain.WaitList;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Size;

@Schema
public class WaitListDto {

	@Schema(description ="number of tickets")
	private int numberOfTickets;

	@Schema(description ="first name of user")
	@NotEmpty
    @Size(max = Constants.NAME_SIZE, message = Constants.FIRST_NAME_SIZE_MSG)
	private String firstName;

	@Schema(description ="last name of user")
	@NotEmpty
    @Size(max = Constants.NAME_SIZE, message = Constants.LAST_NAME_SIZE_MSG)
	private String lastName;

	@Schema(description ="email of user")
	@NotEmpty
	@Email
    @Size(max = Constants.EMAIL_SIZE, message = Constants.EMAIL_SIZE_MSG)
	private String email;

	@Schema(description ="phone number of user")
	private long phoneNumber;

	@Schema(description ="country code of phone number")
	private CountryCode countryCode;

    @Schema(description ="Ticket Type Id")
    private long ticketingTypeId;

    @Schema(description = "Position")
    private Double position;

	public int getNumberOfTickets() {
		return numberOfTickets;
	}

	public void setNumberOfTickets(int numberOfTickets) {
		this.numberOfTickets = numberOfTickets;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public long getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(long phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public CountryCode getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(CountryCode countryCode) {
		this.countryCode = countryCode;
	}

    public long getTicketingTypeId() {
        return ticketingTypeId;
    }

    public void setTicketingTypeId(long ticketingTypeId) {
        this.ticketingTypeId = ticketingTypeId;
    }

    public Double getPosition() {
        return position;
    }

    public void setPosition(Double position) {
        this.position = position;
    }

    public WaitListDto(WaitList waitList) {
		this.countryCode = waitList.getCountryCode();
		this.phoneNumber = waitList.getPhoneNumber();
		this.email = waitList.getEmail();
		this.firstName = waitList.getFirstName();
		this.lastName = waitList.getLastName();
		this.ticketingTypeId = waitList.getTicketingTypeId();
        this.position = waitList.getPosition();
	}

	public WaitListDto() {
	}
}
