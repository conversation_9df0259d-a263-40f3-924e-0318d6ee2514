package com.accelevents.dto;

import com.accelevents.domain.enums.CertificateTypes;

public class CertificateBasicDto {

    private Long certificateId;
    private String certificateName;
    private CertificateTypes certificateTypes;

    public Long getCertificateId() {
        return certificateId;
    }

    public void setCertificateId(Long certificateId) {
        this.certificateId = certificateId;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public CertificateTypes getCertificateTypes() {
        return certificateTypes;
    }

    public void setCertificateTypes(CertificateTypes certificateTypes) {
        this.certificateTypes = certificateTypes;
    }

    public CertificateBasicDto() {
    }

    public CertificateBasicDto(Long certificateId, String certificateName) {
        this.certificateId = certificateId;
        this.certificateName = certificateName;
    }

    public CertificateBasicDto(Long certificateId, String certificateName, CertificateTypes certificateTypes) {
        this.certificateId = certificateId;
        this.certificateName = certificateName;
        this.certificateTypes = certificateTypes;
    }



}
