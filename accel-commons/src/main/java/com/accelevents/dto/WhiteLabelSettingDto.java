package com.accelevents.dto;

import com.accelevents.domain.Stripe;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.enums.BillingType;
import com.accelevents.utils.Constants;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Size;

@Schema(description ="WhiteLabel Settings")
public class WhiteLabelSettingDto extends StripeSettingsDto {

	@Schema(description ="White label url")
	private String whiteLabelUrl;

	@Schema(description ="First Name")
	private String firmName;

	@Schema(description ="Email Address")
    @Size(max = Constants.EMAIL_SIZE, message = Constants.EMAIL_SIZE_MSG)
	private String email;
	
	@Schema(description ="Logo Image")
	private String logoImage;

	@Schema(description ="Banner Image")
	private String bannerImage;

	@Schema(description ="Default Item Image")
	private String defaultItemImage;

	@Schema(description ="Header Logo Image")
	private String headerLogoImage;

    @Schema(description ="Notification popup image")
    private String notificationPopupImage;

	@Schema(description ="Header Color")
	private String headerColor;

	@Schema(description ="Header Font Color")
	private String headerFontColor;

	@Schema(description ="Is intercom activated?")
	private boolean intercomActivated;

	@Schema(description ="Is Help Center Activated?")
	private boolean helpCenterActivated;

	@Schema(description ="Hide Fund Raising Module Toggle?")
	private boolean hideFundRaisingModuleToggle;

	@Schema(description ="Show Enable Modules Popup?")
	private boolean showEnableModulePopup = true;

	@Schema(description ="Allow Editing Of Disclaimer on event ticketing")
	private boolean allowEditingOfDisclaimer;

	@Schema(description ="Terms of Services")
	private String termsOfService;

	@Schema(description ="Privacy policy")
	private String privacyPolicy;

    @Schema(description ="Privacy policy URL for WhiteLabel User")
    private String privacyPolicyLink;

	@Schema(description ="About Us")
	private String aboutUs;

	@Schema(description ="Sign up page image")
	private String signUpPageImage;

	@Schema(description ="Event listing site banner image for event search page")
	private String eventListingSiteBannerImage;

	@Schema(description = "Favicon directory")
	private String faviconDirectory;

	@Schema(description = "Sell event tickets page")
	private String sellEventTicketsPage;

	@Schema(description = "Facebook share")
	private String facebookShare;

	@Schema(description = "Twitter share")
	private String twitterShare;

	@Schema(description = "Instagram share")
	private String instagramShare;

    @Schema(description = "LinkedIn share")
    private String linkedInShare;

	@Schema(description = "Get started")
	private String getStarted;

	@Schema(description = "Custom disclaimer")
	private String customDisclaimer;

	@Schema(description = "Footer text")
	private String footerText;

	@Schema(description = "Help Senter Url")
	private String helpCenterURL;

	@Schema(description = "Signup Text")
	private String signUpText;

	@Schema(description = "Transactional Email")
	private String transactionalEmail;

	@Schema(description = "Notification Email")
	private String notificationEmail;

	@Schema(description = "Fb app id")
	private String fbAppId;

	@Schema(description = "Hide Create Event Button")
	private boolean hideCreateEventButton;

	@Schema(description = "Hide Product Update Notification")
	private boolean hideProductUpdateNotification;

	@Schema(description ="Is Powered by Ae Active")
	private boolean isPoweredByAeActivated;

    @Schema(description ="Chargebee Plan Id")
    private Long planId;

    @Schema(description ="Subscription Id")
    private String subscriptionId;

    @Schema(description ="Charagebee Customer Id")
    private String chargebeeCustomerId;

    @Schema(description ="Plan end date")
    private String planEndDate;

    @Schema(description ="Plan renewal date")
    private String renewalDate;

    @Schema(description ="Mobile Plan End Date")
    private String mobilePlanEndDate;

    @Schema(description ="Mobile Plan Renewal Date")
    private String mobilePlanRenewalDate;

    @Schema(description ="Ad Blocker Document URL")
    private String adBlockerDocumentUrl;

    @Schema(description ="Ad Blocker Logo Image")
    private String adBlockerLogoImage;

    @Schema(description ="Exit Intent Content")
    private String exitIntentContent;

    @Schema(description ="Intercom Id for chat support")
    private String intercomId;

    @Schema(description ="Waive Off Attendee upload Fee")
    private boolean waiveOffAttendeesUploadFee;

    @Schema(description ="Billing Type")
    private BillingType billingType;

    @Schema(description ="Chargebee plan id")
    private String chargebeePlanId;

    @Schema(description ="Hubspot Company Id")
    private Long hubspotCompanyId;

	@Schema(description ="Is Billing Page Enable")
	private boolean isBillingPageEnable;

    @Schema(description ="Host Base URL")
    private String hostBaseUrl;

    @Schema(description ="Enabled Captcha V2")
    private boolean enableCaptchaV2;

    @Schema(description ="Mail API Key Is Enable")
    private boolean isMailApiKeyEnable;
    @Schema(description ="Enable event closing form")
    private boolean enableEventClosingForm;

    @Schema(description ="Enable white label redirect")
    private boolean enableWhiteLabelRedirect;



    public WhiteLabelSettingDto() {
	}

	public WhiteLabelSettingDto(WhiteLabel whiteLabel, Stripe stripe) {
		super();
		this.whiteLabelUrl = whiteLabel.getWhiteLabelUrl();
		this.firmName = whiteLabel.getFirmName();
		this.email = whiteLabel.getSupportEmail();
		this.logoImage = whiteLabel.getLogoImage();
		this.bannerImage =  whiteLabel.getBannerImage();
		this.defaultItemImage = whiteLabel.getDefaultItemImage();
		this.headerLogoImage = whiteLabel.getHeaderLogoImage();
		this.headerColor = whiteLabel.getHeaderColor();
        this.hostBaseUrl= whiteLabel.getHostBaseUrl();
		this.headerFontColor = whiteLabel.getHeaderFontColor();
		this.intercomActivated = whiteLabel.isIntercomActivated();
		this.helpCenterActivated = whiteLabel.isHelpCenterActivated();
		this.hideFundRaisingModuleToggle = whiteLabel.isHideFundRaisingModuleToggle();
		this.showEnableModulePopup = whiteLabel.isShowEnableModulePopup();
		this.allowEditingOfDisclaimer = whiteLabel.isAllowEditingOfDisclaimer();
		this.termsOfService = whiteLabel.getTermsOfService();
		this.privacyPolicy = whiteLabel.getPrivacyPolicy();
        this.privacyPolicyLink=whiteLabel.getPrivacyPolicyLink();
		this.aboutUs = whiteLabel.getAboutUs();
		this.signUpPageImage = whiteLabel.getSignUpPageImage();
		this.eventListingSiteBannerImage = whiteLabel.getEventListingSiteBannerImage();
		this.faviconDirectory = whiteLabel.getFaviconDirectory();
		this.sellEventTicketsPage = whiteLabel.getSellEventTicketsPage();
		this.facebookShare = whiteLabel.getFacebookShare();
        this.linkedInShare=whiteLabel.getLinkedInShare();
		this.twitterShare = whiteLabel.getTwitterShare();
		this.instagramShare = whiteLabel.getInstagramShare();
		this.getStarted = whiteLabel.getGetStarted();
		this.customDisclaimer = whiteLabel.getCustomDisclaimer();
		this.footerText = whiteLabel.getFooterText();
		this.helpCenterURL = whiteLabel.getHelpCenterURL();
		this.signUpText = whiteLabel.getSignUpText();
		this.transactionalEmail =whiteLabel.getTransactionalEmail();
		this.notificationEmail = whiteLabel.getNotificationEmail();
		this.fbAppId = whiteLabel.getFbAppId();
		this.hideCreateEventButton = whiteLabel.isHideCreateEventButton();
		this.hideProductUpdateNotification = whiteLabel.isHideProductUpdateNotification();
		this.isPoweredByAeActivated=whiteLabel.isPoweredByAeActivated();
        if (whiteLabel.getPlanConfig() != null)
            this.planId=whiteLabel.getPlanConfig().getId();
        this.subscriptionId=whiteLabel.getSubscriptionId();
        this.chargebeeCustomerId = whiteLabel.getChargebeeCustomerId();
        this.notificationPopupImage = whiteLabel.getNotificationPopupImage();
        this.adBlockerDocumentUrl=whiteLabel.getAdBlockerDocURL();
        this.adBlockerLogoImage=whiteLabel.getAdBlockerLogoImage();
        this.exitIntentContent=whiteLabel.getExitIntentContent();
        this.intercomId=whiteLabel.getIntercomId();
        this.waiveOffAttendeesUploadFee = whiteLabel.isWaiveOffAttendeesUploadFee();
        this.billingType = whiteLabel.getBillingType();
        this.hubspotCompanyId = whiteLabel.getHubspotCompanyId();
		this.isBillingPageEnable=whiteLabel.isBiillingPageEnabled();
        this.enableWhiteLabelRedirect = whiteLabel.isEnableWhiteLabelRedirect();

		if(stripe != null){
			this.setStripeConnected(true);
			this.setStripeActivated(stripe.isActivated());
			this.setConnectButtonEnabled(!stripe.isDefaultAccount());
			this.setStripeAccountEmail(stripe.getEmail());
			this.setStripeAccountName(stripe.getAccountDisplayName());
            this.setPaymentGateway(stripe.getPaymentGateway());
		} else{
			this.setConnectButtonEnabled(true);
		}
	}

	public String getWhiteLabelUrl() {
		return whiteLabelUrl;
	}

	public void setWhiteLabelUrl(String whiteLabelUrl) {
		this.whiteLabelUrl = whiteLabelUrl;
	}

	public String getFirmName() {
		return firmName;
	}

	public void setFirmName(String firmName) {
		this.firmName = firmName;
	}

	public String getLogoImage() {
		return logoImage;
	}

	public void setLogoImage(String logoImage) {
		this.logoImage = logoImage;
	}

	public String getBannerImage() {
		return bannerImage;
	}

	public void setBannerImage(String bannerImage) {
		this.bannerImage = bannerImage;
	}

	public String getDefaultItemImage() {
		return defaultItemImage;
	}

	public void setDefaultItemImage(String defaultItemImage) {
		this.defaultItemImage = defaultItemImage;
	}

	public String getHeaderLogoImage() {
		return headerLogoImage;
	}

	public void setHeaderLogoImage(String headerLogoImage) {
		this.headerLogoImage = headerLogoImage;
	}

	public String getHeaderColor() {
		return headerColor;
	}

	public void setHeaderColor(String headerColor) {
		this.headerColor = headerColor;
	}

	public boolean isIntercomActivated() {
		return intercomActivated;
	}

	public void setIntercomActivated(boolean intercomActivated) {
		this.intercomActivated = intercomActivated;
	}

	public boolean isHelpCenterActivated() {
		return helpCenterActivated;
	}

	public void setHelpCenterActivated(boolean helpCenterActivated) {
		this.helpCenterActivated = helpCenterActivated;
	}

	public boolean isHideFundRaisingModuleToggle() {
		return hideFundRaisingModuleToggle;
	}

	public void setHideFundRaisingModuleToggle(boolean hideFundRaisingModuleToggle) {
		this.hideFundRaisingModuleToggle = hideFundRaisingModuleToggle;
	}

	public boolean isShowEnableModulePopup() {
		return showEnableModulePopup;
	}

	public void setShowEnableModulePopup(boolean showEnableModulePopup) {
		this.showEnableModulePopup = showEnableModulePopup;
	}

	public String getFacebookShare() {
		return facebookShare;
	}

	public void setFacebookShare(String facebookShare) {
		this.facebookShare = facebookShare;
	}

	public String getTwitterShare() {
		return twitterShare;
	}

	public void setTwitterShare(String twitterShare) {
		this.twitterShare = twitterShare;
	}

	public String getInstagramShare() {
		return instagramShare;
	}

	public void setInstagramShare(String instagramShare) {
		this.instagramShare = instagramShare;
	}

	public String getGetStarted() {
		return getStarted;
	}

	public void setGetStarted(String getStarted) {
		this.getStarted = getStarted;
	}

	public String getCustomDisclaimer() {
		return customDisclaimer;
	}

	public void setCustomDisclaimer(String customDisclaimer) {
		this.customDisclaimer = customDisclaimer;
	}

	public String getFooterText() {
		return footerText;
	}

	public void setFooterText(String footerText) {
		this.footerText = footerText;
	}

	public String getHelpCenterURL() {
		return helpCenterURL;
	}

	public void setHelpCenterURL(String helpCenterURL) {
		this.helpCenterURL = helpCenterURL;
	}

	public String getSignUpText() {
		return signUpText;
	}

	public void setSignUpText(String signUpText) {
		this.signUpText = signUpText;
	}

	public String getTransactionalEmail() {
		return transactionalEmail;
	}

	public void setTransactionalEmail(String transactionalEmail) {
		this.transactionalEmail = transactionalEmail;
	}

	public String getNotificationEmail() {
		return notificationEmail;
	}

	public void setNotificationEmail(String notificationEmail) {
		this.notificationEmail = notificationEmail;
	}

	public String getFbAppId() {
		return fbAppId;
	}

	public void setFbAppId(String fbAppId) {
		this.fbAppId = fbAppId;
	}

	public boolean isHideCreateEventButton() {
		return hideCreateEventButton;
	}

	public void setHideCreateEventButton(boolean hideCreateEventButton) {
		this.hideCreateEventButton = hideCreateEventButton;
	}

	public boolean isHideProductUpdateNotification() { return hideProductUpdateNotification; }

	public void setHideProductUpdateNotification(boolean hideProductUpdateNotification) {
		this.hideProductUpdateNotification = hideProductUpdateNotification;
	}

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getIntercomId() { return intercomId; }

    public void setIntercomId(String intercomId) { this.intercomId = intercomId; }

	public boolean isBillingPageEnable() {
        return isBillingPageEnable;
    }

    public void setBillingPageEnable(boolean billingPageEnable) {
        isBillingPageEnable = billingPageEnable;
    }

    @Override
    public String toString() {
        return "WhiteLabelSettingDto{" +
                "whiteLabelUrl='" + whiteLabelUrl + '\'' +
                ", firmName='" + firmName + '\'' +
                ", email='" + email + '\'' +
                ", logoImage='" + logoImage + '\'' +
                ", bannerImage='" + bannerImage + '\'' +
                ", defaultItemImage='" + defaultItemImage + '\'' +
                ", headerLogoImage='" + headerLogoImage + '\'' +
                ", notificationPopupImage='" + notificationPopupImage + '\'' +
                ", headerColor='" + headerColor + '\'' +
                ", headerFontColor='" + headerFontColor + '\'' +
                ", intercomActivated=" + intercomActivated +
                ", helpCenterActivated=" + helpCenterActivated +
                ", hideFundRaisingModuleToggle=" + hideFundRaisingModuleToggle +
                ", showEnableModulePopup=" + showEnableModulePopup +
                ", allowEditingOfDisclaimer=" + allowEditingOfDisclaimer +
                ", termsOfService='" + termsOfService + '\'' +
                ", privacyPolicy='" + privacyPolicy + '\'' +
                ", aboutUs='" + aboutUs + '\'' +
                ", signUpPageImage='" + signUpPageImage + '\'' +
                ", eventListingSiteBannerImage='" + eventListingSiteBannerImage + '\'' +
                ", faviconDirectory='" + faviconDirectory + '\'' +
                ", sellEventTicketsPage='" + sellEventTicketsPage + '\'' +
                ", facebookShare='" + facebookShare + '\'' +
                ", twitterShare='" + twitterShare + '\'' +
                ", instagramShare='" + instagramShare + '\'' +
                ", getStarted='" + getStarted + '\'' +
                ", customDisclaimer='" + customDisclaimer + '\'' +
                ", footerText='" + footerText + '\'' +
                ", helpCenterURL='" + helpCenterURL + '\'' +
                ", signUpText='" + signUpText + '\'' +
                ", transactionalEmail='" + transactionalEmail + '\'' +
                ", notificationEmail='" + notificationEmail + '\'' +
                ", fbAppId='" + fbAppId + '\'' +
                ", hideCreateEventButton=" + hideCreateEventButton +
                ", hideProductUpdateNotification=" + hideProductUpdateNotification +
                ", isPoweredByAeActivated=" + isPoweredByAeActivated +
                ", planId=" + planId +
                ", subscriptionId='" + subscriptionId + '\'' +
                ", chargebeeCustomerId='" + chargebeeCustomerId + '\'' +
                ", planEndDate='" + planEndDate + '\'' +
                ", renewalDate='" + renewalDate + '\'' +
                ", adBlockerDocumentUrl='" + adBlockerDocumentUrl + '\'' +
                ", adBlockerLogoImage='" + adBlockerLogoImage + '\'' +
                ", exitIntentContent='" + exitIntentContent + '\'' +
                ", intercomId='" + intercomId + '\'' +
                ", waiveOffAttendeesUploadFee=" + waiveOffAttendeesUploadFee +
                ", billingType=" + billingType +
                ", chargebeePlanId='" + chargebeePlanId + '\'' +
                ", linkedInShare='" + linkedInShare + '\'' +
                ", hubspotCompanyId='" + hubspotCompanyId + '\'' +
				", isBillingPageEnable=" + isBillingPageEnable + '\'' +
                ", privacyPolicyLink="+ privacyPolicyLink + '\'' +
                ", enableWhiteLabelRedirect="+ enableWhiteLabelRedirect + '\'' +
                '}';
    }

    public String getHeaderFontColor() {
		return headerFontColor;
	}

	public void setHeaderFontColor(String headerFontColor) {
		this.headerFontColor = headerFontColor;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public boolean isAllowEditingOfDisclaimer() {
		return allowEditingOfDisclaimer;
	}

	public void setAllowEditingOfDisclaimer(boolean allowEditingOfDisclaimer) {
		this.allowEditingOfDisclaimer = allowEditingOfDisclaimer;
	}

	public String getTermsOfService() {
		return termsOfService;
	}

	public void setTermsOfService(String termsOfService) {
		this.termsOfService = termsOfService;
	}

	public String getPrivacyPolicy() {
		return privacyPolicy;
	}

	public void setPrivacyPolicy(String privacyPolicy) {
		this.privacyPolicy = privacyPolicy;
	}

	public String getAboutUs() {
		return aboutUs;
	}

	public void setAboutUs(String aboutUs) {
		this.aboutUs = aboutUs;
	}

	public String getSignUpPageImage() {
		return signUpPageImage;
	}

	public void setSignUpPageImage(String signUpPageImage) {
		this.signUpPageImage = signUpPageImage;
	}

	public String getEventListingSiteBannerImage() {
		return eventListingSiteBannerImage;
	}

	public void setEventListingSiteBannerImage(String eventListingSiteBannerImage) {
		this.eventListingSiteBannerImage = eventListingSiteBannerImage;
	}

	public String getFaviconDirectory() {
		return faviconDirectory;
	}

	public void setFaviconDirectory(String faviconDirectory) {
		this.faviconDirectory = faviconDirectory;
	}

	public String getSellEventTicketsPage() {
		return sellEventTicketsPage;
	}

	public void setSellEventTicketsPage(String sellEventTicketsPage) {
		this.sellEventTicketsPage = sellEventTicketsPage;
	}

	public boolean isPoweredByAeActivated() {
		return isPoweredByAeActivated;
	}

	public void setPoweredByAeActivated(boolean poweredByAeActivated) {
		isPoweredByAeActivated = poweredByAeActivated;
	}

    public String getChargebeeCustomerId() {
        return chargebeeCustomerId;
    }

    public void setChargebeeCustomerId(String chargebeeCustomerId) {
        this.chargebeeCustomerId = chargebeeCustomerId;
    }

    public String getPlanEndDate() {
        return planEndDate;
    }

    public void setPlanEndDate(String planEndDate) {
        this.planEndDate = planEndDate;
    }

    public String getRenewalDate() {
        return renewalDate;
    }

    public void setRenewalDate(String renewalDate) {
        this.renewalDate = renewalDate;
    }

    public String getNotificationPopupImage() {
        return notificationPopupImage;
    }

    public void setNotificationPopupImage(String notificationPopupImage) {
        this.notificationPopupImage = notificationPopupImage;
    }

    public String getAdBlockerDocumentUrl() {
        return adBlockerDocumentUrl;
    }

    public void setAdBlockerDocumentUrl(String adBlockerDocumentUrl) {
        this.adBlockerDocumentUrl = adBlockerDocumentUrl;
    }

    public String getAdBlockerLogoImage() {
        return adBlockerLogoImage;
    }

    public void setAdBlockerLogoImage(String adBlockerLogoImage) {
        this.adBlockerLogoImage = adBlockerLogoImage;
    }

    public String getExitIntentContent() { return exitIntentContent;   }

    public void setExitIntentContent(String exitIntentContent) {  this.exitIntentContent = exitIntentContent;  }

    public boolean isWaiveOffAttendeesUploadFee() {
        return waiveOffAttendeesUploadFee;
    }

    public void setWaiveOffAttendeesUploadFee(boolean waiveOffAttendeesUploadFee) {
        this.waiveOffAttendeesUploadFee = waiveOffAttendeesUploadFee;
    }

    public BillingType getBillingType() {
        return billingType;
    }

    public void setBillingType(BillingType billingType) {
        this.billingType = billingType;
    }

    public String getChargebeePlanId() {
        return chargebeePlanId;
    }

    public void setChargebeePlanId(String chargebeePlanId) {
        this.chargebeePlanId = chargebeePlanId;
    }

    public String getLinkedInShare() {
        return linkedInShare;
    }

    public void setLinkedInShare(String linkedInShare) {
        this.linkedInShare = linkedInShare;
    }

    public Long getHubspotCompanyId() {
        return hubspotCompanyId;
    }

    public void setHubspotCompanyId(Long hubspotCompanyId) {
        this.hubspotCompanyId = hubspotCompanyId;
    }

    public String getMobilePlanEndDate() { return mobilePlanEndDate; }

    public void setMobilePlanEndDate(String mobilePlanEndDate) { this.mobilePlanEndDate = mobilePlanEndDate; }

    public String getMobilePlanRenewalDate() { return mobilePlanRenewalDate; }

    public void setMobilePlanRenewalDate(String mobilePlanRenewalDate) { this.mobilePlanRenewalDate = mobilePlanRenewalDate; }

    public String getPrivacyPolicyLink() {  return privacyPolicyLink; }

    public void setPrivacyPolicyLink(String privacyPolicyLink) {
        this.privacyPolicyLink = privacyPolicyLink;
    }

    public String getHostBaseUrl() {
        return hostBaseUrl;
    }

    public void setHostBaseUrl(String hostBaseUrl) {
        this.hostBaseUrl = hostBaseUrl;
    }

    public boolean isEnableCaptchaV2() {
        return enableCaptchaV2;
    }

    public void setEnableCaptchaV2(boolean enableCaptchaV2) {
        this.enableCaptchaV2 = enableCaptchaV2;
    }

    public boolean isMailApiKeyEnable() {
        return isMailApiKeyEnable;
    }

    public void setMailApiKeyEnable(boolean mailApiKeyEnable) {
        isMailApiKeyEnable = mailApiKeyEnable;
    }

    public boolean isEnableEventClosingForm() {
        return enableEventClosingForm;
    }

    public void setEnableEventClosingForm(boolean enableEventClosingForm) {
        this.enableEventClosingForm = enableEventClosingForm;
    }

    public boolean isEnableWhiteLabelRedirect() {
        return enableWhiteLabelRedirect;
    }

    public void setEnableWhiteLabelRedirect(boolean enableWhiteLabelRedirect) {
        this.enableWhiteLabelRedirect = enableWhiteLabelRedirect;
    }
}
