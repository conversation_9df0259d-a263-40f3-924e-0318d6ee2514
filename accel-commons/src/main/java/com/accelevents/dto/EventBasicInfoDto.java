package com.accelevents.dto;

import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.EventListingStatus;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema
public class EventBasicInfoDto {

	@Schema
	private String eventName;

	@Schema
	private long eventId;

	@Schema
	private String eventURL;

	@Schema
	private Date eventEndDate;

	@Schema
	private Date eventCreateDate;

	@Schema
	private EventTicketRevenueDto eventTicketRevenueDto;

    @Schema
    private Date eventStartDate;

    @Schema
    private String logoImage;

    @Schema
    private EventFormat eventFormat = EventFormat.VIRTUAL;

	@Schema
    private EventListingStatus eventListingStatus = EventListingStatus.PREVIEW;

	@Schema
    private String eventPlanName;

	@Schema
    private Long totalSoldTicket;

    @Schema
    private Long organizerId;

    @Schema
    private String closingNote;

    @Schema
    private Double budgetSpent;

	public EventBasicInfoDto() {
		super();
	}

    public EventBasicInfoDto(
            String eventName,
            long eventId,
            String eventURL,
            Date eventEndDate,
            Date eventCreateDate,
            EventFormat eventFormat,
            Long organizerId,
            Double budgetSpent,
            String closingNote,
            EventListingStatus eventListingStatus,
            EventTicketRevenueDto eventTicketRevenueDto,
            Date eventStartDate,
            String logoImage,
            String eventPlanName,
            Long totalSoldTicket
    ) {
        super();
        this.eventName               = eventName;
        this.eventId                 = eventId;
        this.eventURL                = eventURL;
        this.eventEndDate            = eventEndDate;
        this.eventCreateDate         = eventCreateDate;
        this.eventFormat             = eventFormat;
        this.eventListingStatus      = eventListingStatus;
        this.organizerId             = organizerId;
        this.budgetSpent             = budgetSpent;
        this.closingNote             = closingNote;
        this.eventTicketRevenueDto   = eventTicketRevenueDto;
        this.eventStartDate          = eventStartDate;
        this.logoImage               = logoImage;
        this.eventPlanName           = eventPlanName;
        this.totalSoldTicket         = totalSoldTicket;
    }


    public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}

	public long getEventId() {
		return eventId;
	}

	public void setEventId(long eventId) {
		this.eventId = eventId;
	}

	public String getEventURL() {
		return eventURL;
	}

	public void setEventURL(String eventURL) {
		this.eventURL = eventURL;
	}

	public Date getEventEndDate() {
		return eventEndDate;
	}

	public void setEventEndDate(Date eventEndDate) {
		this.eventEndDate = eventEndDate;
	}

	public EventTicketRevenueDto getEventTicketRevenueDto() {
		return eventTicketRevenueDto;
	}

	public void setEventTicketRevenueDto(EventTicketRevenueDto eventTicketRevenueDto) {
		this.eventTicketRevenueDto = eventTicketRevenueDto;
	}

	public Date getEventCreateDate() {
		return eventCreateDate;
	}

	public void setEventCreateDate(Date eventCreateDate) {
		this.eventCreateDate = eventCreateDate;
	}

    public Date getEventStartDate() {
        return eventStartDate;
    }

    public void setEventStartDate(Date eventStartDate) {
        this.eventStartDate = eventStartDate;
    }

    public String getLogoImage() {
        return logoImage;
    }

    public void setLogoImage(String logoImage) {
        this.logoImage = logoImage;
    }

    public EventFormat getEventFormat() {
        return eventFormat;
    }

    public void setEventFormat(EventFormat eventFormat) {
        this.eventFormat = eventFormat;
    }

    public EventListingStatus getEventListingStatus() {
        return eventListingStatus;
    }

    public void setEventListingStatus(EventListingStatus eventListingStatus) {
        this.eventListingStatus = eventListingStatus;
    }

    public String getEventPlanName() {
        return eventPlanName;
    }

    public void setEventPlanName(String eventPlanName) {
        this.eventPlanName = eventPlanName;
    }

    public Long getTotalSoldTicket() {
        return totalSoldTicket;
    }

    public void setTotalSoldTicket(Long totalSoldTicket) {
        this.totalSoldTicket = totalSoldTicket;
    }

    public Long getOrganizerId() {
        return organizerId;
    }

    public void setOrganizerId(Long organizerId) {
        this.organizerId = organizerId;
    }

    public String getClosingNote() {
        return closingNote;
    }

    public void setClosingNote(String closingNote) {
        this.closingNote = closingNote;
    }

    public Double getBudgetSpent() {
        return budgetSpent;
    }

    public void setBudgetSpent(Double budgetSpent) {
        this.budgetSpent = budgetSpent;
    }
}
