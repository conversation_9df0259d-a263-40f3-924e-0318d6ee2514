package com.accelevents.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema
public class PurchaserBookingDto implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2723174964290662102L;

	@Schema(description = "purchaser related attributes information as key value")
	private List<AttributeKeyValueDto> attributes;

	@Schema(description = "buyer related questions information as key value")
	private List<AttributeKeyValueDto> questions;

    @Schema(description = "nested questions related information as key value")
    private List<NestedQuestionsDto> nestedQuestions;

    private boolean skipHolderInfo;

	public List<AttributeKeyValueDto> getAttributes() {
		return attributes;
	}

	public void setAttributes(List<AttributeKeyValueDto> attributes) {
		this.attributes = attributes;
	}

	public List<AttributeKeyValueDto> getQuestions() {
		return questions;
	}

	public void setQuestions(List<AttributeKeyValueDto> questions) {
		this.questions = questions;
	}

    public List<NestedQuestionsDto> getNestedQuestions() {
        return nestedQuestions;
    }

    public void setNestedQuestions(List<NestedQuestionsDto> nestedQuestions) {
        this.nestedQuestions = nestedQuestions;
    }

    public boolean isSkipHolderInfo() {
        return skipHolderInfo;
    }

    public void setSkipHolderInfo(boolean skipHolderInfo) {
        this.skipHolderInfo = skipHolderInfo;
    }

    @Override
    public String toString() {
        return "PurchaserBookingDto{" +
                "attributes=" + attributes +
                ", questions=" + questions +
                ", nestedQuestions=" + nestedQuestions +
                ", skipHolderInfo=" + skipHolderInfo +
                '}';
    }
}
