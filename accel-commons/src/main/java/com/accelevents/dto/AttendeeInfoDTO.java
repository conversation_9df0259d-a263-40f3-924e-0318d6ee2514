package com.accelevents.dto;

import com.accelevents.domain.UploadAttendeeTrack;
import com.accelevents.ticketing.dto.UTMTrackSourceDto;
import com.accelevents.utils.Constants;

import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

public class AttendeeInfoDTO {

    @Size(max = Constants.NAME_SIZE, message = Constants.FIRST_NAME_SIZE_MSG)
    private String firstName;
    @Size(max = Constants.NAME_SIZE, message = Constants.LAST_NAME_SIZE_MSG)
    private String lastName;
    private String externalOrderId;
    private String transactionId;
    @Size(max = Constants.EMAIL_SIZE, message = Constants.EMAIL_SIZE_MSG)
    private String email;
    private String ticketTypeName;
    private Long quantity;
    private List<AttributeKeyValueDto> attributes = new ArrayList<>();
    private List<NestedQuestionsDto> nestedQuestions;

    private boolean isInValidTransactionId;
    private String invalidTransactionIdErrorMessage;
    private boolean isInValidFirstName;
    private String invalidFirstNameErrorMessage;
    private boolean isInValidLastName;
    private String invalidLastNameErrorMessage;
    private boolean isInValidEmail;
    private String invalidEmailErrorMessage;
    private boolean isInValidProfileImage;
    private String inValidProfileImageErrorMessage;
    private String attendeeAlreadyExistsErrorMessage;
    private String addonErrorMessage;
    private boolean isInValidPhoneNumber;
    private String inValidPhoneNumberErrorMessage;
    private boolean isUniqueBuyerExists;
    private boolean isUniqueHolderExists;

    private UploadAttendeeTrack.UploadSource uploadSource;
    private String paymentStatus;

    private Long orderId;

    private Long eventTicketId;

    private Long ticketTypeId;

    private String profilePic;

    private UTMTrackSourceDto utmTrackSourceDto;

    public AttendeeInfoDTO() {//default implementation ignored
    }

    public AttendeeInfoDTO(String transactionId, String firstName, String lastName, String email, String profileImage) {
        this.transactionId = transactionId;
        this.externalOrderId = transactionId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.profilePic = profileImage;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTicketTypeName() {
        return ticketTypeName;
    }

    public void setTicketTypeName(String ticketTypeName) {
        this.ticketTypeName = ticketTypeName;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public List<AttributeKeyValueDto> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<AttributeKeyValueDto> attributes) {
        this.attributes = attributes;
    }

    public void addAttribute(AttributeKeyValueDto attributeKeyValueDto){
        this.attributes.add(attributeKeyValueDto);
    }

    public UploadAttendeeTrack.UploadSource getUploadSource() {
        return uploadSource;
    }

    public void setUploadSource(UploadAttendeeTrack.UploadSource uploadSource) {
        this.uploadSource = uploadSource;
    }

    public List<NestedQuestionsDto> getNestedQuestions() {
        return nestedQuestions;
    }

    public void setNestedQuestions(List<NestedQuestionsDto> nestedQuestions) {
        this.nestedQuestions = nestedQuestions;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getEventTicketId() {
        return eventTicketId;
    }

    public void setEventTicketId(Long eventTicketId) {
        this.eventTicketId = eventTicketId;
    }

    public Long getTicketTypeId() {
        return ticketTypeId;
    }

    public void setTicketTypeId(Long ticketTypeId) {
        this.ticketTypeId = ticketTypeId;
    }

    public String getProfilePic() {
        return profilePic;
    }

    public void setProfilePic(String profilePic) {
        this.profilePic = profilePic;
    }

    public UTMTrackSourceDto getUtmTrackSourceDto() {
        return utmTrackSourceDto;
    }

    public void setUtmTrackSourceDto(UTMTrackSourceDto utmTrackSourceDto) {
        this.utmTrackSourceDto = utmTrackSourceDto;
    }

    public boolean isInValidTransactionId() {
        return isInValidTransactionId;
    }

    public void setInValidTransactionId(boolean inValidTransactionId) {
        isInValidTransactionId = inValidTransactionId;
    }

    public String getInvalidTransactionIdErrorMessage() {
        return invalidTransactionIdErrorMessage;
    }

    public void setInvalidTransactionIdErrorMessage(String invalidTransactionIdErrorMessage) {
        this.invalidTransactionIdErrorMessage = invalidTransactionIdErrorMessage;
    }

    public boolean isInValidFirstName() {
        return isInValidFirstName;
    }

    public void setInValidFirstName(boolean inValidFirstName) {
        isInValidFirstName = inValidFirstName;
    }

    public String getInvalidFirstNameErrorMessage() {
        return invalidFirstNameErrorMessage;
    }

    public void setInvalidFirstNameErrorMessage(String invalidFirstNameErrorMessage) {
        this.invalidFirstNameErrorMessage = invalidFirstNameErrorMessage;
    }

    public boolean isInValidLastName() {
        return isInValidLastName;
    }

    public void setInValidLastName(boolean inValidLastName) {
        isInValidLastName = inValidLastName;
    }

    public String getInvalidLastNameErrorMessage() {
        return invalidLastNameErrorMessage;
    }

    public void setInvalidLastNameErrorMessage(String invalidLastNameErrorMessage) {
        this.invalidLastNameErrorMessage = invalidLastNameErrorMessage;
    }

    public boolean isInValidEmail() {
        return isInValidEmail;
    }

    public void setInValidEmail(boolean inValidEmail) {
        isInValidEmail = inValidEmail;
    }

    public String getInvalidEmailErrorMessage() {
        return invalidEmailErrorMessage;
    }

    public void setInvalidEmailErrorMessage(String invalidEmailErrorMessage) {
        this.invalidEmailErrorMessage = invalidEmailErrorMessage;
    }

    public boolean isInValidProfileImage() {
        return isInValidProfileImage;
    }

    public void setInValidProfileImage(boolean inValidProfileImage) {
        isInValidProfileImage = inValidProfileImage;
    }

    public String getInValidProfileImageErrorMessage() {
        return inValidProfileImageErrorMessage;
    }

    public void setInValidProfileImageErrorMessage(String inValidProfileImageErrorMessage) {
        this.inValidProfileImageErrorMessage = inValidProfileImageErrorMessage;
    }

    public String getAttendeeAlreadyExistsErrorMessage() {
        return attendeeAlreadyExistsErrorMessage;
    }

    public void setAttendeeAlreadyExistsErrorMessage(String attendeeAlreadyExistsErrorMessage) {
        this.attendeeAlreadyExistsErrorMessage = attendeeAlreadyExistsErrorMessage;
    }

    public String getAddonErrorMessage() {
        return addonErrorMessage;
    }

    public void setAddonErrorMessage(String addonErrorMessage) {
        this.addonErrorMessage = addonErrorMessage;
    }

    public boolean isInValidPhoneNumber() {
        return isInValidPhoneNumber;
    }

    public void setInValidPhoneNumber(boolean inValidPhoneNumber) {
        isInValidPhoneNumber = inValidPhoneNumber;
    }

    public String getInValidPhoneNumberErrorMessage() {
        return inValidPhoneNumberErrorMessage;
    }

    public void setInValidPhoneNumberErrorMessage(String inValidPhoneNumberErrorMessage) {
        this.inValidPhoneNumberErrorMessage = inValidPhoneNumberErrorMessage;
    }

    public boolean isUniqueBuyerExists() {
        return isUniqueBuyerExists;
    }

    public void setUniqueBuyerExists(boolean uniqueBuyerExists) {
        isUniqueBuyerExists = uniqueBuyerExists;
    }

    public boolean isUniqueHolderExists() {
        return isUniqueHolderExists;
    }

    public void setUniqueHolderExists(boolean uniqueHolderExists) {
        isUniqueHolderExists = uniqueHolderExists;
    }
}
