package com.accelevents.dto;

import java.io.Serializable;


public class PrivateEventDto implements Serializable {

    private static final long serialVersionUID = 5649165556078084092L;

    private boolean isPrivateEvent = false;
    private boolean isPasswordRequired = false;
    private String password;

    public boolean isPrivateEvent() {
        return isPrivateEvent;
    }

    public void setPrivateEvent(boolean privateEvent) {
        isPrivateEvent = privateEvent;
    }

    public boolean isPasswordRequired() {
        return isPasswordRequired;
    }

    public void setPasswordRequired(boolean passwordRequired) {
        isPasswordRequired = passwordRequired;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}