package com.accelevents.dto;

import com.accelevents.enums.BillingType;

import java.io.Serializable;

public class OrganizerSettingDto implements Serializable {

    private static final long serialVersionUID = 4225764830598627501L;

    private String organizerPageURL;
    private BillingType billingType;
    private boolean waiveOffAttendeesUploadFee;
    private  boolean isAutoBilling;
    private Double attendeeUploadCharge;
    private Double expoBoothCharge;
    private int engageEmailsLimit;

    public OrganizerSettingDto() {
    }

    public OrganizerSettingDto(String organizerPageURL, BillingType billingType, boolean waiveOffAttendeesUploadFee,
                               boolean isAutoBilling, Double attendeeUploadCharge, Double expoBoothCharge, int engageEmailsLimit) {
        this.organizerPageURL = organizerPageURL;
        this.billingType = billingType;
        this.waiveOffAttendeesUploadFee = waiveOffAttendeesUploadFee;
        this.isAutoBilling = isAutoBilling;
        this.attendeeUploadCharge = attendeeUploadCharge;
        this.expoBoothCharge = expoBoothCharge;
        this.engageEmailsLimit = engageEmailsLimit;
    }//NOSONAR

    public String getOrganizerPageURL() {
        return organizerPageURL;
    }

    public void setOrganizerPageURL(String organizerPageURL) {
        this.organizerPageURL = organizerPageURL;
    }

    public boolean isWaiveOffAttendeesUploadFee() {
        return waiveOffAttendeesUploadFee;
    }

    public void setWaiveOffAttendeesUploadFee(boolean waiveOffAttendeesUploadFee) {
        this.waiveOffAttendeesUploadFee = waiveOffAttendeesUploadFee;
    }

    public BillingType getBillingType() {
        return billingType;
    }

    public void setBillingType(BillingType billingType) {
        this.billingType = billingType;
    }

    public boolean isAutoBilling() {
        return isAutoBilling;
    }

    public void setAutoBilling(boolean autoBilling) {
        isAutoBilling = autoBilling;
    }

    public Double getAttendeeUploadCharge() { return attendeeUploadCharge; }

    public void setAttendeeUploadCharge(Double attendeeUploadCharge) { this.attendeeUploadCharge = attendeeUploadCharge; }

    public Double getExpoBoothCharge() { return expoBoothCharge; }

    public void setExpoBoothCharge(Double expoBoothCharge) { this.expoBoothCharge = expoBoothCharge; }

    public int getEngageEmailsLimit() {
        return engageEmailsLimit;
    }

    public void setEngageEmailsLimit(int engageEmailsLimit) {
        this.engageEmailsLimit = engageEmailsLimit;
    }
}
