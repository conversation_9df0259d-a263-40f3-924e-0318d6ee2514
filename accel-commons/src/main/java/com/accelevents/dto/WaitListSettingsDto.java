package com.accelevents.dto;

import com.accelevents.domain.WaitListSettings;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.concurrent.TimeUnit;

public class WaitListSettingsDto extends DisplayWaitListSettingsDto {

    @Schema(description ="time to respond for a user in days")
    private int timeToRespondInDays;

    @Schema(description ="time to respond for a user in hours")
    private int timeToRespondInHours;

    @Schema(description ="time to respond for a user in minutes")
    private int timeToRespondInMinutes;

    @Schema(description ="message to send when tickets become available")
    private String waitListTicketReleaseMessage;

    @Schema(description ="Number of tickets allowed for waiting, pass 0 for unlimited tickets.")
    private int maxWaitListSize;

    @Schema(description ="Maximum size reached for waitlist.")
    private boolean maxWaitListSizeReached;

    @Schema(description ="Remaining ticket for waitlist.")
    private int remainingTickets;

    @Schema(description ="Manually release tickets for waitlist.")
    private boolean manualReleaseTickets;

    @Schema(description ="Waitlist Button Text")
    private String waitListButtonText;

    @Schema(description ="message to send when Waitlist Signup")
    private String waitListTicketConfirmationMessage;

    @Schema(description ="Subject line to send when Waitlist Signup")
    private String waitListTicketReleaseSubject;

    @Schema(description ="Subject to send when Waitlist Confirmed")
    private String waitListTicketConfirmationSubject;

    public String getWaitListTicketReleaseMessage() {
        return waitListTicketReleaseMessage;
    }

    public void setWaitListTicketReleaseMessage(String waitListTicketReleaseMessage) {
        this.waitListTicketReleaseMessage = waitListTicketReleaseMessage;
    }

    public int getTimeToRespondInDays() {
        return timeToRespondInDays;
    }

    public void setTimeToRespondInDays(int timeToRespondInDays) {
        this.timeToRespondInDays = timeToRespondInDays;
    }

    public int getTimeToRespondInHours() {
        return timeToRespondInHours;
    }

    public void setTimeToRespondInHours(int timeToRespondInHours) {
        this.timeToRespondInHours = timeToRespondInHours;
    }

    public int getTimeToRespondInMinutes() {
        return timeToRespondInMinutes;
    }

    public void setTimeToRespondInMinutes(int timeToRespondInMinutes) {
        this.timeToRespondInMinutes = timeToRespondInMinutes;
    }

    public int getMaxWaitListSize() {
        return maxWaitListSize;
    }

    public void setMaxWaitListSize(int maxWaitListSize) {
        this.maxWaitListSize = maxWaitListSize;
    }

    public boolean isMaxWaitListSizeReached() {
        return maxWaitListSizeReached;
    }

    public void setMaxWaitListSizeReached(boolean maxWaitListSizeReached) {
        this.maxWaitListSizeReached = maxWaitListSizeReached;
    }

    public int getRemainingTickets() {
        return remainingTickets;
    }

    public void setRemainingTickets(int remainingTickets) {
        this.remainingTickets = remainingTickets;
    }

    public boolean isManualReleaseTickets() {
        return manualReleaseTickets;
    }

    public void setManualReleaseTickets(boolean manualReleaseTickets) {
        this.manualReleaseTickets = manualReleaseTickets;
    }

    public String getWaitListButtonText() {
        return waitListButtonText;
    }

    public void setWaitListButtonText(String waitListButtonText) {
        this.waitListButtonText = waitListButtonText;
    }

    public String getWaitListTicketConfirmationMessage() {
        return waitListTicketConfirmationMessage;
    }

    public void setWaitListTicketConfirmationMessage(String waitListTicketConfirmationMessage) {
        this.waitListTicketConfirmationMessage = waitListTicketConfirmationMessage;
    }

    public String getWaitListTicketReleaseSubject() {
        return waitListTicketReleaseSubject;
    }

    public void setWaitListTicketReleaseSubject(String waitListTicketReleaseSubject) {
        this.waitListTicketReleaseSubject = waitListTicketReleaseSubject;
    }

    public String getWaitListTicketConfirmationSubject() {
        return waitListTicketConfirmationSubject;
    }

    public void setWaitListTicketConfirmationSubject(String waitListTicketConfirmationSubject) {
        this.waitListTicketConfirmationSubject = waitListTicketConfirmationSubject;
    }

    public WaitListSettingsDto() {
    }

    public WaitListSettingsDto(WaitListSettings waitListSettings) {
        super(waitListSettings);
        this.waitListTicketReleaseMessage = waitListSettings.getWaitListTicketReleaseMessage();
        this.maxWaitListSize = waitListSettings.getMaxWaitListSize();
        int seconds = waitListSettings.getTimeToRespond() * 60;
        int day = (int)TimeUnit.SECONDS.toDays(seconds);
        Long hours = TimeUnit.SECONDS.toHours(seconds) - (day * 24);
        Long minute = TimeUnit.SECONDS.toMinutes(seconds) - (TimeUnit.SECONDS.toHours(seconds) * 60);
        this.timeToRespondInDays = day;
        this.timeToRespondInHours = hours.intValue();
        this.timeToRespondInMinutes = minute.intValue();
        this.manualReleaseTickets = waitListSettings.isManualReleaseTickets();
        this.waitListButtonText = waitListSettings.getWaitListButtonText();
        this.waitListTicketConfirmationMessage = waitListSettings.getWaitListTicketConfirmationMessage();
        this.waitListTicketReleaseSubject = waitListSettings.getWaitListTicketReleaseSubject();
        this.waitListTicketConfirmationSubject = waitListSettings.getWaitListTicketConfirmationSubject();
    }

    public WaitListSettings toEntity() {
        WaitListSettings entity = new WaitListSettings();
        entity.setPhoneNumberRequired(this.isPhoneNumberRequired());
        entity.setWaitListEnabled(this.isWaitListEnabled());
        entity.setWaitListTicketReleaseMessage(this.getWaitListTicketReleaseMessage());
        entity.setWaitListTrigger(this.getWaitListTrigger());
        entity.setMaxWaitListSize(this.getMaxWaitListSize());
        entity.setTimeToRespond(
                (this.getTimeToRespondInDays() * 24 + this.getTimeToRespondInHours()) * 60
                        + this.getTimeToRespondInMinutes()
        );
        entity.setManualReleaseTickets(this.isManualReleaseTickets());
        entity.setWaitListButtonText(this.getWaitListButtonText());
        entity.setWaitListTicketConfirmationMessage(this.getWaitListTicketConfirmationMessage());
        entity.setWaitListTicketConfirmationSubject(this.getWaitListTicketConfirmationSubject());
        entity.setWaitListTicketReleaseSubject(this.getWaitListTicketReleaseSubject());
        return entity;
    }

}
