package com.accelevents.dto;

import com.accelevents.enums.BillingType;

import java.io.Serializable;

public class WhiteLabelBillingSettingsDto implements Serializable {

    private static final long serialVersionUID = -2094869970074598150L;

    public String whiteLabelURL;
    private BillingType billingType;
    private boolean isAutoBilling;
    private boolean waiveOffAttendeesUploadFee;
    private Double attendeeUploadCharge;
    private Double expoBoothCharge;
    private Boolean isWlHaveLatestPlan;
    private int engageEmailsLimit;
    private boolean enableWhiteLabelRedirect;

    public WhiteLabelBillingSettingsDto() {
    }

    public WhiteLabelBillingSettingsDto(String whiteLabelURL, BillingType billingType, boolean isAutoBilling,
                                        boolean waiveOffAttendeesUploadFee, Double attendeeUploadCharge, Double expoBoothCharge, int engageEmailsLimit, boolean enableWhiteLabelRedirect) {
        this.whiteLabelURL = whiteLabelURL;
        this.billingType = billingType;
        this.isAutoBilling = isAutoBilling;
        this.waiveOffAttendeesUploadFee = waiveOffAttendeesUploadFee;
        this.attendeeUploadCharge = attendeeUploadCharge;
        this.expoBoothCharge = expoBoothCharge;
        this.engageEmailsLimit = engageEmailsLimit;
        this.enableWhiteLabelRedirect = enableWhiteLabelRedirect;
    }//NOSONAR

    public WhiteLabelBillingSettingsDto(Double attendeeUploadCharge, Double expoBoothCharge){
        this.attendeeUploadCharge = attendeeUploadCharge;
        this.expoBoothCharge = expoBoothCharge;
    }

    public BillingType getBillingType() {
        return billingType;
    }

    public void setBillingType(BillingType billingType) {
        this.billingType = billingType;
    }

    public String getWhiteLabelURL() {
        return whiteLabelURL;
    }

    public void setWhiteLabelURL(String whiteLabelURL) {
        this.whiteLabelURL = whiteLabelURL;
    }

    public boolean isAutoBilling() {
        return isAutoBilling;
    }

    public void setAutoBilling(boolean autoBilling) {
        isAutoBilling = autoBilling;
    }

    public boolean isWaiveOffAttendeesUploadFee() {
        return waiveOffAttendeesUploadFee;
    }

    public void setWaiveOffAttendeesUploadFee(boolean waiveOffAttendeesUploadFee) {
        this.waiveOffAttendeesUploadFee = waiveOffAttendeesUploadFee;
    }

    public Double getAttendeeUploadCharge() { return attendeeUploadCharge; }

    public void setAttendeeUploadCharge(Double attendeeUploadCharge) { this.attendeeUploadCharge = attendeeUploadCharge; }

    public Double getExpoBoothCharge() { return expoBoothCharge; }

    public void setExpoBoothCharge(Double expoBoothCharge) { this.expoBoothCharge = expoBoothCharge; }

    public Boolean getWlHaveLatestPlan() {
        return isWlHaveLatestPlan;
    }

    public void setWlHaveLatestPlan(Boolean wlHaveLatestPlan) {
        isWlHaveLatestPlan = wlHaveLatestPlan;
    }

    public int getEngageEmailsLimit() {
        return engageEmailsLimit;
    }

    public void setEngageEmailsLimit(int engageEmailsLimit) {
        this.engageEmailsLimit = engageEmailsLimit;
    }

    public boolean isEnableWhiteLabelRedirect() {
        return enableWhiteLabelRedirect;
    }

    public void setEnableWhiteLabelRedirect(boolean enableWhiteLabelRedirect) {
        this.enableWhiteLabelRedirect = enableWhiteLabelRedirect;
    }
}
