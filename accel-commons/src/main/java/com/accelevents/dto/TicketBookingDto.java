package com.accelevents.dto;

import com.accelevents.constraint.CheckDateFormat;
import com.accelevents.ticketing.dto.TicketingOrderDto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Schema
public class TicketBookingDto implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1563867144349435365L;

	/**
	 *
	 */

	@Schema(description = "purchaser information")
	@Valid
	private PurchaserBookingDto purchaser;

	@Schema(description = "Add-on information")
	@Valid
	private PurchaserBookingDto addOnAttributes;

	@Schema(description = "ticket holder information")
	@Valid
	private List<HolderBookingDto> holder;

	@Schema(description = "this attribute withs same value when you get data by \"getformattributes\" API")
	@NotNull
	private Boolean hasholderattributes;

	@Schema(description = "stripe token OR intent id generated by stripe API and creditcard information")
	private String tokenOrIntentId;

	@Schema(description = "Disclaimer confirmation before checkout")
	private String disclaimer;

	@Schema(description = "client browser date")
	@NotEmpty
	@CheckDateFormat(pattern = "dd/MM/yyyy HH:mm:ss", message = "Please enter date in dd/MM/yyyy HH:mm:ss format")
	private String clientDate;

    @Schema(description = "user show profile information information as key value")
    private List <Map<String,Boolean>> userShowProfile;

    @Schema(description = "WL Disclaimer confirmation before checkout")
    private String wlDisclaimer;

    private List<AddOnDetailsDTO> addOnDetails;

    private String countryCode;

    private boolean payLater;

    private boolean depositPayment;

    private List<HolderEmailDiscountDto> holderEmailDiscountDtos;

    private List<TicketingOrderDto> orderTicketings;

	public PurchaserBookingDto getPurchaser() {
		return purchaser;
	}

	public void setPurchaser(PurchaserBookingDto purchaser) {
		this.purchaser = purchaser;
	}

	public List<HolderBookingDto> getHolder() {
		return holder;
	}

	public void setHolder(List<HolderBookingDto> holder) {
		this.holder = holder;
	}

	public Boolean getHasholderattributes() {
		return hasholderattributes;
	}

	public void setHasholderattributes(Boolean hasholderattributes) {
		this.hasholderattributes = hasholderattributes;
	}

	public String getTokenOrIntentId() {
		return tokenOrIntentId;
	}

	public void setTokenOrIntentId(String tokenOrIntentId) {
		this.tokenOrIntentId = tokenOrIntentId;
	}

	public String getClientDate() {
		return clientDate;
	}

	public void setClientDate(String clientDate) {
		this.clientDate = clientDate;
	}

	public PurchaserBookingDto getAddOnAttributes() {
		return addOnAttributes;
	}

	public void setAddOnAttributes(PurchaserBookingDto addOnAttributes) {
		this.addOnAttributes = addOnAttributes;
	}

    public String getDisclaimer() {
        return disclaimer;
    }

    public void setDisclaimer(String disclaimer) {
        this.disclaimer = disclaimer;
    }

    public List<Map<String, Boolean>> getUserShowProfile() { return userShowProfile; }

    public void setUserShowProfile(List<Map<String, Boolean>> userShowProfile) {
        this.userShowProfile = userShowProfile;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public boolean isPayLater() {
        return payLater;
    }

    public void setPayLater(boolean payLater) {
        this.payLater = payLater;
    }

    public List<HolderEmailDiscountDto> getHolderEmailDiscountDtos() {
        return holderEmailDiscountDtos;
    }

    public void setHolderEmailDiscountDtos(List<HolderEmailDiscountDto> holderEmailDiscountDtos) {
        this.holderEmailDiscountDtos = holderEmailDiscountDtos;
    }

    public List<AddOnDetailsDTO> getAddOnDetails() {
        return addOnDetails;
    }

    public void setAddOnDetails(List<AddOnDetailsDTO> addOnDetails) {
        this.addOnDetails = addOnDetails;
    }

    public boolean isDepositPayment() {
        return depositPayment;
    }

    public void setDepositPayment(boolean depositPayment) {
        this.depositPayment = depositPayment;
    }

    public String getWlDisclaimer() {return wlDisclaimer;}

    public void setWlDisclaimer(String wlDisclaimer) {this.wlDisclaimer = wlDisclaimer;}

    public List<TicketingOrderDto> getOrderTicketings() {
        return orderTicketings;
    }

    public void setOrderTicketings(List<TicketingOrderDto> orderTicketings) {
        this.orderTicketings = orderTicketings;
    }

    @Override
    public String toString() {
        return "TicketBookingDto{" +
                "purchaser=" + purchaser +
                ", addOnAttributes=" + addOnAttributes +
                ", holder=" + holder +
                ", hasholderattributes=" + hasholderattributes +
                ", tokenOrIntentId='" + tokenOrIntentId + '\'' +
                ", disclaimer='" + disclaimer + '\'' +
                ", clientDate='" + clientDate + '\'' +
                ", userShowProfile=" + userShowProfile +
                ", wlDisclaimer='" + wlDisclaimer + '\'' +
                ", addOnDetails=" + addOnDetails +
                ", countryCode='" + countryCode + '\'' +
                ", payLater=" + payLater +
                ", depositPayment=" + depositPayment +
                ", holderEmailDiscountDtos=" + holderEmailDiscountDtos +
                ", orderTicketings=" + orderTicketings +
                '}';
    }
}
