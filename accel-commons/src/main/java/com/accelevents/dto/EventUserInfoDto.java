package com.accelevents.dto;

import com.accelevents.domain.User;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Set;

import static com.accelevents.utils.ApiMessages.*;
@Schema(description = "Event user Info")
public class EventUserInfoDto extends UserInfoDto {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1144194813284583712L;

	@Schema(description =STAFF_USER_FLAG_DESC)
	private boolean hasStaffAccess;

	@Schema(description =IS_ADMIN)
	private boolean isAdmin;

	@Schema(description =STRIPE_CARD_USER_DESC)
	private LinkedCard linkedCard;

	@Schema(description =IS_ADDRESS_REQUERED_DESC)
	private boolean addressRequired;

	@Schema(description =BIDDER_NUMBER_DESC)
	private Long bidderNumber;
    private boolean showPopUp;

    private Boolean isChatExpanded;

    private Set<Long> orderIds;

	public EventUserInfoDto(User user) {
		super(user);
	}

	public boolean isHasStaffAccess() {
		return hasStaffAccess;
	}

	public void setHasStaffAccess(boolean hasStaffAccess) {
		this.hasStaffAccess = hasStaffAccess;
	}

	public LinkedCard getLinkedCard() {
		return linkedCard;
	}

	public void setLinkedCard(LinkedCard linkedCard) {
		this.linkedCard = linkedCard;
	}
	
	public boolean isAddressRequired() {
		return addressRequired;
	}

	public void setAddressRequired(boolean addressRequired) {
		this.addressRequired = addressRequired;
	}

	public Long getBidderNumber() {
		return bidderNumber;
	}

	public void setBidderNumber(Long bidderNumber) {
		this.bidderNumber = bidderNumber;
	}

	public boolean isAdmin() {
		return isAdmin;
	}

	public void setAdmin(boolean admin) {
		isAdmin = admin;
	}

    public boolean isShowPopUp() {
        return showPopUp;
    }

    public void setShowPopUp(boolean showPopUp) {
        this.showPopUp = showPopUp;
    }

    public Boolean getChatExpanded() {
        return isChatExpanded;
    }

    public void setChatExpanded(Boolean chatExpanded) {
        isChatExpanded = chatExpanded;
    }

    public Set<Long> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(Set<Long> orderIds) {
        this.orderIds = orderIds;
    }
}
