package com.accelevents.dto;

import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.EventListingStatus;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.ticketing.dto.EventCategoryDto;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.ticketing.dto.TicketingFeeDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude(content = Include.NON_NULL)
@Schema
public class TicketDisplayPageDto {

	@Schema(description = "start date of ticketing sale")
	private Date startDate;

	@Schema(description = "end date of ticketing sale")
	private Date endDate;

	@Schema(description = "ticketing venue")
	private String address;

	@Schema(description = "ticketing type")
	private List<TicketTypeDto> ticketTypes = new ArrayList<>();

	@Schema(description ="show remaining tickets on display page")
	private boolean showRemainingTickets;

	@Schema(description ="Seating chart key, if ticketing is of seating type")
	private String seatingChartKey;

	@Schema(description ="Seating event key, if ticketing is of seating type")
	private String eventKey;

	@Schema(description ="isHolderAttributeRequired")
	private boolean isHolderAttributeRequired;

	@Schema(description ="If access code present return true otherwise false")
	private boolean isAvailableAccessCode;

	@Schema(description ="ticketing fee will help in display fee on UI side")
	private List<TicketingFeeDto> ticketingFee;

	@Schema(description ="exit Intent Pop up enable")
	private boolean exitIntentPopupEnabled;

	private boolean requireDisclaimerConfirmation;

    private boolean allowDisagreeDisclaimerConfirmation;

	private String stripeCountry;

	@Schema(description ="If discount code present return true otherwise false")
	private boolean discountCodeAvailable;

	private boolean eventCapacityReach;

	private List<EventCategoryDto> categories;

	@Schema(description ="Event listing status")
	private EventListingStatus eventListingStatus;

	@Schema(description ="Event venue status")
	private EnumEventVenue eventVenueStatus;

	@Schema(description ="Event venue status")
	private EventFormat eventFormat;

	@Schema(description ="Show registration button on display page")
	private boolean showRegistrationButton;

    @Schema(description ="Pre Event Access time in minutes")
    private Integer preEventAccessMinutes;

    @Schema(description ="Show Ticket Price")
    private boolean showTicketPrice;

    @Schema(description ="show member count in checkout")
    private boolean showMemberCountInCheckout;

    @Schema(description ="event dates will be hide or not")
    private boolean hideEventDate;

    @Schema(description ="event billing id")
    private String eventBillingId;

    @Schema(description ="is attendee registration approval enabled")
    private boolean attendeeRegistrationApproval;

    @Schema(description = "is limit registration email toggle")
    private boolean isLimitRegistrationEmails;

    @Schema(description = "Organization note related to Pay Later Checkout")
    private String orgNote;
    private boolean offlinePayment;
    private VirtualEventOfflinePaymentDescDTO offlinePaymentDesc;

    private Boolean isUniqueTicketHolderEmail;
    private boolean allowCheckInWithUnpaidTicket;

    private boolean attendeeApprovalCardCaptureEnabled;
    private boolean isUniqueTicketBuyerEmail;

    public boolean isAttendeeApprovalCardCaptureEnabled() {
        return attendeeApprovalCardCaptureEnabled;
    }

    public void setAttendeeApprovalCardCaptureEnabled(boolean attendeeApprovalCardCaptureEnabled) {
        this.attendeeApprovalCardCaptureEnabled = attendeeApprovalCardCaptureEnabled;
    }

    public boolean isOfflinePayment() {
        return offlinePayment;
    }

    public void setOfflinePayment(boolean offlinePayment) {
        this.offlinePayment = offlinePayment;
    }

    public VirtualEventOfflinePaymentDescDTO getOfflinePaymentDesc() {
        return offlinePaymentDesc;
    }

    public void setOfflinePaymentDesc(VirtualEventOfflinePaymentDescDTO offlinePaymentDesc) {
        this.offlinePaymentDesc = offlinePaymentDesc;
    }

    public boolean isEventCapacityReach() {
		return eventCapacityReach;
	}

	public void setEventCapacityReach(boolean eventCapacityReach) {
		this.eventCapacityReach = eventCapacityReach;
	}

	public String getEventKey() {
		return eventKey;
	}

	public void setEventKey(String eventKey) {
		this.eventKey = eventKey;
	}

	public Date getEndDate() {
		return endDate;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public List<TicketTypeDto> getTicketTypes() {
		return ticketTypes;
	}

	public void add(TicketTypeDto ticketType) {
		ticketTypes.add(ticketType);
	}

    public void addAll(List<TicketTypeDto> ticketTypes) {
        this.ticketTypes.addAll(ticketTypes);
    }

	public String getSeatingChartKey() {
		return seatingChartKey;
	}

	public void setSeatingChartKey(String seatingChartKey) {
		this.seatingChartKey = seatingChartKey;
	}

	public boolean isHolderAttributeRequired() {
		return isHolderAttributeRequired;
	}

	public void setHolderAttributeRequired(boolean holderAttributeRequired) {
		this.isHolderAttributeRequired = holderAttributeRequired;
	}

	public boolean isAvailableAccessCode() {
		return isAvailableAccessCode;
	}

	public void setAvailableAccessCode(boolean availableAccessCode) {
		isAvailableAccessCode = availableAccessCode;
	}

	public List<TicketingFeeDto> getTicketingFee() {
		return ticketingFee;
	}

	public void setTicketingFee(List<TicketingFeeDto> ticketingFee) {
		this.ticketingFee = ticketingFee;
	}

	public boolean isRequireDisclaimerConfirmation() {
		return requireDisclaimerConfirmation;
	}

	public void setRequireDisclaimerConfirmation(boolean requireDisclaimerConfirmation) {
		this.requireDisclaimerConfirmation = requireDisclaimerConfirmation;
	}

	public String getStripeCountry() {
		return stripeCountry;
	}

	public void setStripeCountry(String stripeCountry) {
		this.stripeCountry = stripeCountry;
	}

	public boolean isExitIntentPopupEnabled() {
		return exitIntentPopupEnabled;
	}

	public void setExitIntentPopupEnabled(boolean exitIntentPopupEnabled) {
		this.exitIntentPopupEnabled = exitIntentPopupEnabled;
	}

	public boolean isDiscountCodeAvailable() {
		return discountCodeAvailable;
	}

	public void setDiscountCodeAvailable(boolean discountCodeAvailable) {
		this.discountCodeAvailable = discountCodeAvailable;
	}

	public List<EventCategoryDto> getCategories() {
		return categories;
	}

	public void setCategories(List<EventCategoryDto> categories) {
		this.categories = categories;
	}

	public EventListingStatus getEventListingStatus() {
		return eventListingStatus;
	}

	public void setEventListingStatus(EventListingStatus eventListingStatus) {
		this.eventListingStatus = eventListingStatus;
	}

	public EnumEventVenue getEventVenueStatus() {
		return eventVenueStatus;
	}

	public void setEventVenueStatus(EnumEventVenue eventVenueStatus) {
		this.eventVenueStatus = eventVenueStatus;
	}

	public boolean isAllowDisagreeDisclaimerConfirmation() {
        return allowDisagreeDisclaimerConfirmation;
    }

    public void setAllowDisagreeDisclaimerConfirmation(boolean allowDisagreeDisclaimerConfirmation) {
        this.allowDisagreeDisclaimerConfirmation = allowDisagreeDisclaimerConfirmation;
    }

	public EventFormat getEventFormat() {
		return eventFormat;
	}

	public void setEventFormat(EventFormat eventFormat) {
		this.eventFormat = eventFormat;
	}

    public Integer getPreEventAccessMinutes() {
	    return preEventAccessMinutes;
	}

    public void setPreEventAccessMinutes(Integer preEventAccessMinutes) {
	    this.preEventAccessMinutes = preEventAccessMinutes;
	}
    public boolean isShowRemainingTickets() {
        return showRemainingTickets;
    }

    public void setShowRemainingTickets(boolean showRemainingTickets) {
        this.showRemainingTickets = showRemainingTickets;
    }

    public boolean isShowRegistrationButton() {
        return showRegistrationButton;
    }

    public void setShowRegistrationButton(boolean showRegistrationButton) {
        this.showRegistrationButton = showRegistrationButton;
    }

    public boolean isShowTicketPrice() {
        return showTicketPrice;
    }

    public void setShowTicketPrice(boolean showTicketPrice) {
        this.showTicketPrice = showTicketPrice;
    }

    public boolean isShowMemberCountInCheckout() {
        return showMemberCountInCheckout;
    }

    public void setShowMemberCountInCheckout(boolean showMemberCountInCheckout) {
        this.showMemberCountInCheckout = showMemberCountInCheckout;
    }

    public boolean isHideEventDate() {
        return hideEventDate;
    }

    public void setHideEventDate(boolean hideEventDate) {
        this.hideEventDate = hideEventDate;
    }

    public String getEventBillingId() {
        return eventBillingId;
    }

    public void setEventBillingId(String eventBillingId) {
        this.eventBillingId = eventBillingId;
    }

    public boolean isAttendeeRegistrationApproval() {
        return attendeeRegistrationApproval;
    }

    public void setAttendeeRegistrationApproval(boolean attendeeRegistrationApproval) {
        this.attendeeRegistrationApproval = attendeeRegistrationApproval;
    }

    public boolean isLimitRegistrationEmails() {
        return isLimitRegistrationEmails;
    }

    public void setLimitRegistrationEmails(boolean limitRegistrationEmails) {
        isLimitRegistrationEmails = limitRegistrationEmails;
    }

    public String getOrgNote() {
        return orgNote;
    }

    public void setOrgNote(String orgNote) {
        this.orgNote = orgNote;
    }

    public Boolean getUniqueTicketHolderEmail() {
        return isUniqueTicketHolderEmail;
    }

    public void setUniqueTicketHolderEmail(Boolean uniqueTicketHolderEmail) {
        isUniqueTicketHolderEmail = uniqueTicketHolderEmail;
    }

    public boolean isAllowCheckInWithUnpaidTicket() {
        return allowCheckInWithUnpaidTicket;
    }

    public void setAllowCheckInWithUnpaidTicket(boolean allowCheckInWithUnpaidTicket) {
        this.allowCheckInWithUnpaidTicket = allowCheckInWithUnpaidTicket;
    }

    public boolean isUniqueTicketBuyerEmail() {
        return isUniqueTicketBuyerEmail;
    }

    public void setUniqueTicketBuyerEmail(boolean uniqueTicketBuyerEmail) {
        isUniqueTicketBuyerEmail = uniqueTicketBuyerEmail;
    }
}
