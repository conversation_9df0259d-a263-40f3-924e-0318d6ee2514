package com.accelevents.auction.dto;


import com.accelevents.domain.Contacts;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.session_speakers.dto.CustomAttributeDetailsDto;
import com.accelevents.session_speakers.dto.CustomAttributeDisplayDto;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;


public class ContactDto extends BaseContactDto {


    @Schema(description ="id of contact, if record exists in system.")
    private long id;

    @Schema(description = "Contact Id")
    private String contactId;

    @Schema(description ="country code for phone number")
    private String countryCode;

    @Schema(description ="phone number")
    private long phoneNumber;

    @Schema(description ="phone number used for CSV upload includes country code and phone number both")
    private String phoneNumberCSV;

    private String error;

    private boolean isUnSubscribed;

    private String ticketStatus;

    private CustomAttributeDetailsDto contactAttributeDetailsDto;

    private CustomAttributeDisplayDto contactAttributeDisplayDTO;

    private Long contactAttributeId;

    public long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = StringUtils.upperCase(countryCode);
    }

    public ContactDto() {
    }

    public ContactDto(String firstName, String lastName, String email) {
        super(firstName, lastName, email);
    }

    public ContactDto(String firstName, String lastName, String email, long id, String countryCode, long phoneNumber, boolean isUnSubscribed) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.id = id;
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
        this.isUnSubscribed = isUnSubscribed;
    }

    public ContactDto(String firstName, String lastName, String email, long id, CountryCode countryCode, long phoneNumber, String ticketStatus) {
        super(firstName, lastName, email);
        this.id = id;
        this.countryCode = countryCode == null ? null : countryCode.name();
        this.phoneNumber = phoneNumber;
        this.ticketStatus = ticketStatus;
    }

    public ContactDto(Object[] row) {
        this(
                /* firstName */     (String) row[0],
                /* lastName */      (String) row[1],
                /* email */         (String) row[2],
                /* id */            Long.parseLong(String.valueOf(row[3])),
                /* countryCode */   row[4] == null ? null : CountryCode.valueOf(((String) row[4]).toUpperCase()),
                /* phoneNumber */   Long.parseLong(String.valueOf(row[5])),
                /* ticketStatus */  (String) row[6]
        );
        // Set contact attribute ID if present
        if (row.length > 7 && row[7] != null) {
            this.contactAttributeId = Long.parseLong(String.valueOf(row[7]));
        }
    }
    public Contacts toEntity(long eventId) {
        Contacts contacts = new Contacts();
        contacts.setFirstName(firstName);
        contacts.setCountryCode(getCountryCodeFromString());
        contacts.setEmail(this.email);
        contacts.setEventId(eventId);
        contacts.setLastName(this.lastName);
        contacts.setPhoneNumber(this.phoneNumber);
        contacts.setCreatedDate(new Date());
        return contacts;
    }
    public Contacts toEntity(long eventId,long attributeId) {
       Contacts contacts = toEntity(eventId);
       contacts.setContactAttributeId(attributeId);
        return contacts;
    }


    public CountryCode getCountryCodeFromString() {
        return StringUtils.isNotBlank(this.countryCode)
                ? CountryCode.valueOf(StringUtils.upperCase(this.countryCode)) : CountryCode.US;
    }

    public Contacts toEntity(Contacts contacts) {
        contacts.setFirstName(this.firstName);
        contacts.setCountryCode(getCountryCodeFromString());
        contacts.setEmail(this.email);
        contacts.setLastName(this.lastName);
        contacts.setPhoneNumber(this.phoneNumber);
        return contacts;
    }
    public ContactDto createContactDto(Contacts contact){
        ContactDto contactDto = new ContactDto();
        contactDto.setId(contact.getId());
        contactDto.setFirstName(contact.getFirstName());
        contactDto.setLastName(contact.getLastName());
        contactDto.setEmail(contact.getEmail());
        contactDto.setCountryCode(contact.getCountryCode().name());
        contactDto.setPhoneNumber(contact.getPhoneNumber());
        return contactDto;
    }

    public ContactDto(Contacts contact, List<String> emailSuppressionList) {
        super(contact);
        this.countryCode = contact.getCountryCode().name();
        this.phoneNumber = contact.getPhoneNumber();
        this.id = contact.getId();
        this.contactAttributeId = contact.getContactAttributeId();
        this.isUnSubscribed = !CollectionUtils.isEmpty(emailSuppressionList) && emailSuppressionList.contains(contact.getEmail());
    }
    public Contacts modifyContact(Contacts contacts,ContactDto contactDto) {
        contacts.setFirstName(contactDto.getFirstName());
        contacts.setLastName(contactDto.getLastName());
        contacts.setCountryCode(getCountryCodeFromString());
        contacts.setPhoneNumber(contactDto.getPhoneNumber());
        return contacts;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getPhoneNumberCSV() {
        return phoneNumberCSV;
    }

    public void setPhoneNumberCSV(String phoneNumberCSV) {
        this.phoneNumberCSV = phoneNumberCSV;
    }

    public boolean isUnSubscribed() {
        return isUnSubscribed;
    }

    public String getTicketStatus() {
        return ticketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        this.ticketStatus = ticketStatus;
    }

    public CustomAttributeDetailsDto getContactAttributeDetailsDto() {
        return contactAttributeDetailsDto;
    }

    public void setContactAttributeDetailsDto(CustomAttributeDetailsDto contactAttributeDetailsDto) {
        this.contactAttributeDetailsDto = contactAttributeDetailsDto;
    }

    public CustomAttributeDisplayDto getContactAttributeDisplayDTO() {
        return contactAttributeDisplayDTO;
    }

    public void setContactAttributeDisplayDTO(CustomAttributeDisplayDto contactAttributeDisplayDTO) {
        this.contactAttributeDisplayDTO = contactAttributeDisplayDTO;
    }

    public Long getContactAttributeId() {
        return contactAttributeId;
    }

    public void setContactAttributeId(Long contactAttributeId) {
        this.contactAttributeId = contactAttributeId;
    }

    public String getContactId() {
        return contactId;
    }

    public void setContactId(String contactId) {
        this.contactId = contactId;
    }

    @Override
    public String toString() {
        return "ContactDto{" +
                "id=" + id +
                ", countryCode='" + countryCode + '\'' +
                ", phoneNumber=" + phoneNumber +
                ", phoneNumberCSV='" + phoneNumberCSV + '\'' +
                ", error='" + error + '\'' +
                ", isUnSubscribed=" + isUnSubscribed +
                '}';
    }
}
