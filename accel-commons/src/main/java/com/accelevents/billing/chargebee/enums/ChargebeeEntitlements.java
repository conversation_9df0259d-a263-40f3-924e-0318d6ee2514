package com.accelevents.billing.chargebee.enums;

public enum ChargebeeEntitlements {

    BADGE_PRINTING("Badge Printing","badge-printing-2023"),
    WEBINARS("Webinars","webinars-2023"),
    ADVANCED_EMAIL_MARKETING("Advanced Email Marketing","advanced-email-marketing-2023"),
    ADD_CUSTOM_TABS_AND_PAGES("Add Custom Tabs and Pages","add-custom-tabs-and-pages-2023"),
    API_ACCESS("API Access","api-access-2023"),
    APPROVAL_WORKFLOWS("Approval Workflows","approval-workflows-2023"),
    ATTENDEE_MOBILE_APP("Attendee Mobile App","attendee-mobile-app-2023"),
    CE_CREDIT_TRACKING("CE Credit Tracking","ce-credit-tracking-2023"),
    BASIC_GAMIFICATION("Basic Gamification","basic-gamification-2023"),
    NATIVE_INTEGRATIONS("Native Integrations","native-integrations-2023"),
    CUSTOM_GAMIFICATION("Custom Gamification","custom-gamification-2023"),
    VIRTUAL_COMPONENTS("Virtual Components","virtual-components-2023"),

    //The three below are not in use at the moment, planned to be added later on.
    OFFLINE_PAYMENTS("Offline Payments","offline-payments"),
    ADVANCED_ANALYTICS("Advanced Analytics","advanced-analytics"),
    AUDIENCE_BUILDER("Audience Builder","audience-builder"),
    KIOSK_MODE("Kiosk Mode","kiosk-mode-2023"),
    CUSTOM_JAVA_SCRIPT("Custom Java Script","custom-java-script-2024"),
    TWO_FACTOR_AUTHENTICATION("2-Factor Authentication","2-factor-authentication-2024"),
    CUSTOM_CSS("Custom CSS","custom-css-2024"),
    VIRTUAL_NETWORKING("Virtual Networking","virtual-networking-2024"),
    PRE_SCHEDULE_MEETINGS("Pre-Schedule Meetings","pre-schedule-meetings-2024"),
    SURVEYS("Surveys","surveys-2024"),
    BRANDING("Branding","branding-2024"),
    SENDER_NAME_MODIFICATION("Sender Name Modification","sender-name-modification-2024"),
    ENGAGE_EMAILS("Engage Emails","engage-emails"),
    ADVANCED_PAGE_BUILDER("Advanced Page Builder","advanced-page-builder-2023"),
    SINGLE_SIGN_ON("Single Sign On","single-sign-on"),
    EVENT_REQUEST_FORM("Event Request Form","event-request-form");

    String displayName;
    String entitlementId;

    ChargebeeEntitlements(String displayName, String entitlementId) {
        this.displayName = displayName;
        this.entitlementId = entitlementId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getEntitlementId() {
        return entitlementId;
    }

}
