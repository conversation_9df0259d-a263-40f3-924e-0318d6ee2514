package com.accelevents.billing.chargebee.repo.impl;

import com.accelevents.billing.chargebee.repo.OrganizerRepoService;
import com.accelevents.domain.Organizer;
import com.accelevents.enums.BillingType;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.OrganizerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class OrganizerRepoServiceImpl implements OrganizerRepoService {

    @Autowired
    private OrganizerRepository repository;


    @Override
    public Organizer findByIdThrowException(Long organizerId) {
        if(organizerId != null){
            return repository.findById(organizerId).orElseThrow(()-> new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND));
        }
        else {
            throw new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND);
        }
    }

    @Override
    public List<Organizer> findAllOrganizer(long from, long to) {
        return repository.findAllOrganizer(from, to);
    }

    @Override
    public List<Organizer> findAllOrganizersBetween(long from, long to) {
        return repository.findAllOrganizersBetween(from, to);
    }

    @Override
    public void saveAll(List<Organizer> organizers) {
        organizers.forEach(organizer -> repository.save(organizer));
    }

    @Override
    public void save(Organizer organizer) {
        if(isBlank(organizer.getOldOrganizerPageURL())) {
            organizer.setOldOrganizerPageURL(organizer.getOrganizerPageURL());
        }
        repository.save(organizer);
    }

    @Override
    public List<Organizer> findBySubscriptionId(String subscriptionId) {
        return repository.findBySubscriptionId(subscriptionId);
    }

    @Override
    public List<Organizer> findBySubscriptionIds(List<String> subscriptionIds) {
        return repository.findBySubscriptionIdIn(subscriptionIds);
    }

    @Override
    public List<Organizer> findByChargebeeCustomerId(String customerId) {
        return repository.findByChargebeeCustomerId(customerId);
    }

    @Override
    public List<BigInteger> findOrganizerWhichDoesNotHaveOwner() {
        return repository.findOrganizerWhichDoesNotHaveOwner();
    }

    @Override
    public BillingType findBillingTypeByOrganizerId(Long organizerId) {
        return repository.findBillingTypeByOrganizerId(organizerId);
    }

    @Override
    public List<Organizer> findAllOrganizerByPlanId(Long from, Long to, long planId) {
        return repository.findAllOrganizerByPlanId(from, to, planId);
    }

    @Override
    public List<Organizer> findAllOrganizerWithHubspotCompanyId(long from, long to) {
        return repository.findAllOrganizerWithHubspotCompanyId(from, to);
    }

    @Override
    public List<Organizer> findAllOrganiserWhoHaveOldPlan(long startId, long endId) {
        return repository.findAllOrganiserWhoHaveOldPlan(startId, endId);
    }
    @Override
    public List<Organizer> findAllOrganiserWhoHavePlan(long startId, long endId) {
        return repository.findAllOrganiserWhoHavePlan(startId, endId);
    }

    @Override
    public List<Organizer> findAllOrganisersBySubscriptionIdIsNotNullAndNotHavingVirtualComponentEntitlement(long startId, long endId) {
        return repository.findAllOrganisersBySubscriptionIdIsNotNullAndNotHavingVirtualComponentEntitlement(startId, endId);
    }

    @Override
    public List<Organizer> findAllOrganisersBySubscriptionIdIsNotNull(long startId, long endId) {
        return repository.findAllOrganisersBySubscriptionIdIsNotNull(startId, endId);
    }

    @Override
    public List<Organizer> findOrganizerByWhiteLabel(long whiteLabelId) {
        return repository.findOrganizerByWhiteLabel(whiteLabelId);
    }

    @Override
    public Organizer findByOrganizerId(Long organizerId) {
        return repository.findByOrganizerId(organizerId).orElseThrow(() -> new NotFoundException(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND));
    }

    @Override
    public List<Organizer> findPaidOrganizersByIdRange(long startId, long endId) {
        return repository.findPaidOrganizersByIdRange(startId, endId);
    }

    @Override
    public List<Organizer> findOrganisersByWhiteLabelIds(List<Long> whiteLabelIds) {
        return repository.findOrganisersByWhiteLabelIds(whiteLabelIds);
    }
}
