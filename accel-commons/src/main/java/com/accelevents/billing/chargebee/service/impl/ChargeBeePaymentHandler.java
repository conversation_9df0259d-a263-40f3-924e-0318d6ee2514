package com.accelevents.billing.chargebee.service.impl;

import com.accelevents.billing.chargebee.dto.ChargeebeeCustomerDto;
import com.accelevents.billing.chargebee.dto.ChargesPurchasedDto;
import com.accelevents.billing.chargebee.enums.ChargeConfigNames;
import com.accelevents.billing.chargebee.enums.ChargebeeEntitlements;
import com.accelevents.billing.chargebee.repo.*;
import com.accelevents.billing.chargebee.repositories.ChargebeeCreditsLogsRepository;
import com.accelevents.billing.chargebee.repositories.ChargebeeTransactionRepository;
import com.accelevents.billing.chargebee.service.*;
import com.accelevents.configuration.ChargebeeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.ChargebeeTransactionSource;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.PaymentStatus;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.virtual.CreditType;
import com.accelevents.dto.EntitlementsDTO;
import com.accelevents.enums.BillingType;
import com.accelevents.enums.OrganizerRole;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.hubspot.dto.HubSpotCompanyDTO;
import com.accelevents.hubspot.service.HubspotOrganizerService;
import com.accelevents.repositories.JoinUsersWithOrganizersRepository;
import com.accelevents.repositories.OrganizerRepository;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.repositories.UserRepository;
import com.accelevents.ro.chargebee.ROChargeBeeService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.CacheService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.WhiteLabelRepoService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.JsonMapper;
import com.accelevents.utils.NumberUtils;
import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.models.Customer;
import com.chargebee.models.Invoice;
import com.chargebee.models.Subscription;
import com.chargebee.models.SubscriptionEntitlement;
import com.chargebee.models.enums.AutoCollection;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.*;
import static com.accelevents.utils.Constants.*;
import static com.chargebee.models.Invoice.Status.PAYMENT_DUE;


@Service
public class ChargeBeePaymentHandler implements ChargeBeePaymentHandlerService {
    private final Logger logger = LoggerFactory.getLogger(ChargeBeePaymentHandler.class);

    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private ChargebeePlanRepoService chargebeePlanRepoService;
    @Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Autowired
    private OrganizerRepository organizerRepository;
    @Autowired
    private OrganizerRepoService organizerRepoService;
    @Autowired
    private WhiteLabelRepoService whiteLabelRepoService;
    @Autowired
    private UserRepoService userRepoService;
    @Autowired
    private ChargebeePaymentService chargebeePaymentService;
    @Autowired
    private ChargeBeeHelperService chargeBeeHelperService;
    @Autowired
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
    @Autowired
    private TicketingRepository ticketingRepository;
    @Autowired
    private WhiteLabelUserNotificationService whiteLabelUserNotificationService;
    @Autowired
    private UserService userService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private ChargeConfigRepoService chargeConfigRepoService;
    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Autowired
    private ChargebeeConfiguration chargebeeConfiguration;
    @Autowired
    private ChargebeeService chargebeeService;
    @Autowired
    private ROChargeBeeService roChargeBeeService;
    @Autowired
    private ChargeBeeSubscriptionWorkFlowService chargeBeeSubscriptionWorkFlowService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;
    @Autowired
    private OrganizerService organizerService;
    @Autowired
    private ChargebeeBillingService chargebeeBillingService;

    @Autowired
    private  ChargebeeEventCreditsRepoService chargebeeEventCreditsRepoService;

    @Autowired
    private ChargebeeEventUsagesRepoService chargebeeEventUsagesRepoService;
    @Autowired
    private HubspotOrganizerService hubspotOrganizerService;

    @Autowired
    private ChargebeeCreditsLogsRepository chargebeeCreditsLogsRepository;
    @Autowired
    private ChargeBeeHubSpotSyncService chargeBeeHubSpotSyncService;
    @Autowired
    private ChargebeeTransactionRepository chargebeeTransactionRepository;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ContactModuleSettingsService contactModuleSettingsService;

    @Override
    @Transactional
    public void mapMobileSubscriptionForOrganizer(Long organizerId, ChargeebeeCustomerDto chargeebeeCustomerDto, User createdBy) throws Exception {//NOSONAR

        logger.info("ChargebeePaymentHandler | mapMobileSubscriptionForOrganizer | organizerId {} createdBy {}",organizerId,createdBy.getUserId());
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        User user = getFirstOrgOwner(organizer);

        String subscriptionId;
        Subscription subscription;
        double amount = 0d;
        Optional<PlanConfig> oldPlanConfig = Optional.empty();

        if(NumberUtils.isNumberGreaterThanZero(organizer.getMobilePlanConfig())){
            oldPlanConfig = chargebeePlanRepoService.findById(organizer.getMobilePlanConfig());
        }

        List<ChargesPurchasedDto> chargesPurchasedDtoList = new ArrayList<>();
        if (!chargeebeeCustomerDto.getSubscriptionId().equals(organizer.getMobileSubscriptionId())) {
            raiseErrorIfSubscriptionAlreadyUsed(chargeebeeCustomerDto.getSubscriptionId());
            subscription = mapMobileSubscriptionAndCreateSubscriptionBForOrg(organizer, chargeebeeCustomerDto, user,createdBy);
            subscriptionId = subscription.id();
        } else {
            updateMobilePlanToExistingOrganizer(organizer);
            subscriptionId = organizer.getMobileSubscriptionId();
        }

        Optional<PlanConfig> newPlanConfig = chargebeePlanRepoService.findById(organizer.getMobilePlanConfig());

        if(oldPlanConfig.isPresent() && !oldPlanConfig.get().getPlanName().equals(newPlanConfig.isPresent() ? newPlanConfig.get().getPlanName() : null)){//NOSONAR
                amount += newPlanConfig.isPresent() ? newPlanConfig.get().getPrice() : 0d;
        }
        chargeBeeSubscriptionWorkFlowService.sendNotificationAndUpdateActiveEventsAndTransactionalTable(subscriptionId, organizer);
        chargeebeeCustomerDto.setChargebeeCustomerId(organizer.getMobileCustomerId());
        chargeebeeCustomerDto.setSubscriptionId(organizer.getMobileSubscriptionId());
        associateCharge(chargeebeeCustomerDto, organizer,createdBy, null, false,true,null);
        logger.info("ChargebeePaymentHandler | mapMobileSubscriptionForOrganizer | {} plan purchased by organizer {}",organizer.getMobilePlanConfig(),organizer.getOrganizerPageURL());
        if(StringUtils.isBlank(chargeebeeCustomerDto.getInvoiceId())){
            chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList) ? JsonMapper.convertToString(chargesPurchasedDtoList) : Constants.STRING_EMPTY,
                    newPlanConfig.orElse(null), createdBy, organizer, null, organizer.getMobileCustomerId(), organizer.getMobileSubscriptionId(), amount);
        }
        hubspotOrganizerService.updateHubspotOrganizerProperty(organizer, Constants.CHARGEBEE_PLAN);
    }

    @Override
    public Subscription mapMobileSubscriptionAndCreateSubscriptionBForOrg(Organizer organizer, ChargeebeeCustomerDto chargeebeeCustomerDto, User user, User loggedInUser) throws Exception {

        Subscription subscription = retrieveSubscription(chargeebeeCustomerDto.getSubscriptionId());

        if(!chargeebeeCustomerDto.getChargebeeCustomerId().equals(subscription.customerId())){
            chargeebeeCustomerDto.setChargebeeCustomerId(subscription.customerId());
        }
        Customer customer = retrieveCustomer(chargeebeeCustomerDto.getChargebeeCustomerId());
        sendOrgTeamToChargebeeContact(organizer.getId(), customer,loggedInUser);

        logger.info("ChargebeePaymentHandler | mapMobileSubscriptionAndCreateSubscriptionBForOrg | subscriptionId {} ", chargeebeeCustomerDto.getSubscriptionId());

        PlanConfig planConfig = chargebeePlanRepoService.findPlanConfigBySubscriptionItemPriceIdOrThrow(subscription.subscriptionItems().get(0).itemPriceId());

        raiseErrorIfLegacyPlanTryToActivateAfter2021(planConfig);

        chargeBeeSubscriptionWorkFlowService.createMobileSubscriptionBAndPopulateChargeBeeInfoInDb(customer.id(), user, chargeebeeCustomerDto.getSubscriptionId(), organizer, planConfig);
        organizerRepoService.save(organizer);
        return subscription;
    }

    private void updateMobilePlanToExistingOrganizer(Organizer organizer) {
        String subscriptionId = organizer.getMobileSubscriptionId();
        List<String> newPlanIdList =  IterableUtils.toList(chargebeePlanRepoService.findAll()).stream()
                .map(PlanConfig::getNewYearlyPlanId).collect(Collectors.toList());
        Subscription subscription = retrieveSubscription(subscriptionId);
        Subscription.SubscriptionItem subscriptionItem = subscription.subscriptionItems().stream().filter(
                item -> newPlanIdList.contains(item.itemPriceId())).findFirst().orElseGet(null);//NOSONAR
        if(subscriptionItem == null) {
            logger.error("No subscription Item found");
            return;
        }

        PlanConfig planConfig = chargebeePlanRepoService.findPlanConfigBySubscriptionItemPriceIdOrThrow(subscriptionItem.itemPriceId());
        raiseErrorIfLegacyPlanTryToActivateAfter2021(planConfig);
        organizer.setMobilePlanConfig(planConfig.getId());
        organizerRepository.save(organizer);

    }

    @Override
    @Transactional
    public void mapSubscriptionAAndCreateSubscriptionBForWLMobile(String whiteLabelUrl, ChargeebeeCustomerDto chargeebeeCustomerDto, User loggedInUser) throws Exception {
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(whiteLabelUrl).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND));
        User whiteLabelAdmin = userRepoService.findUserByWhiteLabelId(whiteLabel.getId());

        Subscription subscription = retrieveSubscription(chargeebeeCustomerDto.getSubscriptionId());

        if (!chargeebeeCustomerDto.getChargebeeCustomerId().equals(subscription.customerId())) {
            chargeebeeCustomerDto.setChargebeeCustomerId(subscription.customerId());
        }

        Customer customer = retrieveCustomer(chargeebeeCustomerDto.getChargebeeCustomerId());

        sendWhiteLabelUsersToChargebeeContact(whiteLabel, customer, loggedInUser);

        logger.info("ChargebeePaymentHandler | mapSubscriptionAAndCreateSubscriptionBForWLMobile | subscriptionId {} ", chargeebeeCustomerDto.getSubscriptionId());//NOSONAR
        PlanConfig planConfig = chargebeePlanRepoService.findPlanConfigBySubscriptionItemPriceIdOrThrow(subscription.subscriptionItems().get(0).itemPriceId());

        String subIdA;
        Optional<PlanConfig> oldPlanConfig = Optional.empty();

        double amount = 0d;
        List<ChargesPurchasedDto> chargesPurchasedDtoList = new ArrayList<>();
        if (NumberUtils.isNumberGreaterThanZero(whiteLabel.getMobilePlanConfig())) {
            oldPlanConfig = chargebeePlanRepoService.findById(whiteLabel.getMobilePlanConfig());
        }
        if (!chargeebeeCustomerDto.getSubscriptionId().equals(whiteLabel.getMobileSubscriptionId())) {
            raiseErrorIfSubscriptionAlreadyUsed(chargeebeeCustomerDto.getSubscriptionId());
            chargeBeeSubscriptionWorkFlowService.createMobileSubscriptionBAndPopulateChargeBeeInfoInDb(
                    customer.id(), whiteLabelAdmin, chargeebeeCustomerDto.getSubscriptionId(), whiteLabel, planConfig);
            subIdA = subscription.id();
        } else {
            chargeBeeSubscriptionWorkFlowService.changeMobileSubscriptionPlanAndUpdateRenewalDate(planConfig, whiteLabel, whiteLabel.getBillingType());
            subIdA = whiteLabel.getMobileSubscriptionId();
        }
        Optional<PlanConfig> newPlanConfig = chargebeePlanRepoService.findById(whiteLabel.getMobilePlanConfig());

        if (oldPlanConfig.isPresent() && !oldPlanConfig.get().getPlanName().equals(newPlanConfig.<Object>map(PlanConfig::getPlanName).orElse(null))) {//NOSONAR
            amount += newPlanConfig.isPresent() ? newPlanConfig.get().getPrice() : 0d;
        }
        whiteLabelRepoService.save(whiteLabel);
        logger.info("Unlimited Mobile App Plan Purchase For whitelabel {}", whiteLabel.getFirmName());
        chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList) ? JsonMapper.convertToString(chargesPurchasedDtoList) : Constants.STRING_EMPTY,
                newPlanConfig.orElse(null), loggedInUser, null, whiteLabel, whiteLabel.getMobileCustomerId(), whiteLabel.getMobileSubscriptionId(), amount);
        chargeBeeSubscriptionWorkFlowService.handleNotificationFutureEventsAndTransactionalTable(whiteLabel, whiteLabelAdmin, planConfig, subIdA);
    }

    @Override
    @Transactional
    public String createWLSubscriptionIfNotExistsAndAddChargeQuantity(User user, String token, String whitelabelUrl, Long planId,
                                                                      Long chargeId, Integer quantity) throws Exception {
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(whitelabelUrl).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND));

        logger.info("ChargebeePaymentHandler | createWLSubscriptionIfNotExistsAndAddChargeQuantity | whitelabelId {} whitelabelUrl {}",whiteLabel.getId(),whitelabelUrl);

        raiseErrorIfTokenNotFoundAndBillingTypeIsPaid(token, whiteLabel.getBillingType());

        raiseErrorIfTokenNotFoundAndNotAuthForCompType(token, whiteLabel.getBillingType(), user);

        validatePlanQty(quantity);

        PlanConfig planConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName = planConfig.getPlanName();


        Invoice invoice;
            logger.info("ChargebeePaymentHandler | createSubscriptionIfNotExistsAndAddChargeQuantity | for WL mobile ChargeId {}",chargeId);
            if (null == whiteLabel.getMobilePlanConfig() || whiteLabel.getMobilePlanConfig() == 4){
                createOrUpdateMobileSubscriptionForWhitelabel(user,token,planId,whiteLabel);
                invoice = addMobileChargeForWhitelabel(chargeId,quantity,whiteLabel);
            }else{
                invoice = addMobileChargeQuantityToExistingSubWL(user,token,chargeId,quantity,whiteLabel);
            }

        ChargeConfig chargeConfig = chargeConfigRepoService.findByIdThrowException(chargeId);

        logger.info("Mobile Plan Purchase For Single Unit Plan whitelabel {} PlanName {}",whiteLabel.getWhiteLabelUrl(),planName);

        List<ChargesPurchasedDto> chargesPurchasedDtoList = new ArrayList<>();
        chargesPurchasedDtoList.add(new ChargesPurchasedDto(chargeConfig.getChargeDisplayName(), quantity.longValue()));

        saveChargebeeTransactionData(chargesPurchasedDtoList,whiteLabel,user,chargeConfig,invoice,quantity);

        return chargeConfig.getChargeDisplayName();
    }

    private void saveChargebeeTransactionData(List<ChargesPurchasedDto> chargesPurchasedDtoList, WhiteLabel whiteLabel, User user, ChargeConfig chargeConfig, Invoice invoice, Integer quantity) {//NOSONAR
            Optional<PlanConfig> planConfig = chargebeePlanRepoService.findById(whiteLabel.getMobilePlanConfig());
            chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList)
                            ? JsonMapper.convertToString(chargesPurchasedDtoList) : Constants.STRING_EMPTY,
                    planConfig.orElse(null), user, null, whiteLabel,
                    whiteLabel.getMobileCustomerId(), whiteLabel.getMobileSubscriptionId(), chargeConfig.getAmount() * quantity,
                    invoice != null ? invoice.id() : null, invoice != null ? invoice.status().name() : null);
    }

    @Override
    @Transactional
    public String createOrUpdateMobileSubscriptionForWhitelabel(User loggedInUser, String token, Long planId, WhiteLabel whiteLabel) throws Exception {

        User billingContact = getBillingContact(loggedInUser, whiteLabel);

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, whiteLabel.getMobileCustomerId(), billingContact, whiteLabel.getFirmName());
        logger.info("createOrUpdateMobileSubscriptionForWhitelabel chargeBee CustomerId {} date {}", customer.id(), new Date());
        whiteLabel.setMobileCustomerId(customer.id());
        PlanConfig newPlanConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName = newPlanConfig.getPlanName();
        String subIdA;
        BillingType billingType = userService.isBillingTypeAdmin(loggedInUser) ? whiteLabel.getBillingType() : BillingType.Paid;
        if (null == whiteLabel.getMobilePlanConfig() || whiteLabel.getMobilePlanConfig() == 4
                || (whiteLabel.getMobilePlanConfig() == 6 && whiteLabel.getMobileSubscriptionId() == null)) {
            Subscription subscriptionA = chargeBeeSubscriptionWorkFlowService.createMobileSubscriptionAAndB(customer.id(),
                    newPlanConfig, billingContact, whiteLabel, billingType);
            subIdA = subscriptionA.id();
        } else {
            chargeBeeSubscriptionWorkFlowService.changeMobileSubscriptionPlanAndUpdateRenewalDate(newPlanConfig, whiteLabel, billingType);
            subIdA = whiteLabel.getMobileSubscriptionId();
        }
        whiteLabelRepoService.save(whiteLabel);

        chargeBeeSubscriptionWorkFlowService.handleNotificationFutureEventsAndTransactionalTable(whiteLabel,loggedInUser,newPlanConfig,subIdA);

        activateTicketingModuleForWhitelabel(whiteLabel.getId());
        return planName;
    }

    private void activateTicketingModuleForWhitelabel(Long whitelabelId) {

        List<Event> eventsForTicketing = eventRepoService.findOnlineEventsWithNoPaidTicketsByWhitelabelId(whitelabelId, EventFormat.VIRTUAL);
        if(!CollectionUtils.isEmpty(eventsForTicketing)) {
            eventsForTicketing.forEach(event -> {
                if (Boolean.TRUE.equals(event.getTicketingEnabled())) {
                    Ticketing ticketing = ticketingRepository.findByEventid(event);
                    if (ticketing!=null && !ticketing.getActivated()) {
                        ticketing.setActivated(true);
                        ticketingRepository.save(ticketing);
                    }
                    logger.info("In activateTicketingModule method Activating ticketing module for eventId {}", event.getEventId());
                }
            });
        }
    }

    private Invoice addMobileChargeForWhitelabel(Long chargeId, Integer quantity, WhiteLabel whiteLabel) throws Exception {
        ChargeConfig chargeConfig = chargeConfigRepoService.findByIdThrowException(chargeId);
        String subscriptionId = whiteLabel.getMobileSubscriptionId();
        Date now = com.accelevents.utils.DateUtils.getCurrentDate();
        chargebeePaymentService.setAutoCollectionONorOFF(whiteLabel.getMobileCustomerId(), AutoCollection.ON);
        Invoice invoice = chargebeePaymentService.addChargeToSubscription(subscriptionId, chargeConfig, quantity,"", BillingType.Paid.equals(whiteLabel.getBillingType()));
        chargebeePaymentService.setAutoCollectionONorOFF(whiteLabel.getMobileCustomerId(), AutoCollection.OFF);
        List<ChargesPurchased> chargesPurchasedList = new ArrayList<>();
        if (PLAN_USAGE_INCLUDED_CREDITS.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            chargesPurchasedList.add(prepareChargePurchased(whiteLabel, chargeConfig, invoice, now));
        } else {
            for (int i = 0; i < quantity; i++) {
                chargesPurchasedList.add(prepareChargePurchased(whiteLabel, chargeConfig, invoice, now));
            }
        }
        chargesPurchasedRepoService.saveAll(chargesPurchasedList);
        return invoice;
    }

    @Override
    public Invoice addMobileChargeQuantityToExistingSubWL(User loggedInUser, String token,
                                                          Long chargeId, Integer quantity, WhiteLabel whiteLabel) throws Exception {

        User billingContact = getBillingContact(loggedInUser, whiteLabel);

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, whiteLabel.getMobileCustomerId(),
                billingContact, whiteLabel.getFirmName());
        logger.info("addMobileChargeQuantityToExistingSubWL chargeBee CustomerId {} date {}", customer.id(), new Date());
        whiteLabel.setMobileCustomerId(customer.id());
        return addMobileChargeForWhitelabel(chargeId, quantity, whiteLabel);
    }

    @Transactional
    @Override
    public String createOrUpdateMobileSubscriptionForWhiteLabel(String whiteLabelUrl, String token, Long planId, User loggedInUser) throws Exception {
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(whiteLabelUrl).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND));
        User whiteLabelAdmin = userRepoService.findUserByWhiteLabelId(whiteLabel.getId());

        raiseErrorIfTokenNotFoundAndBillingTypeIsPaid(token, whiteLabel.getBillingType());
        BillingType billingType = userService.isBillingTypeAdmin(loggedInUser) ? whiteLabel.getBillingType() : BillingType.Paid;

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, whiteLabel.getMobileCustomerId(), whiteLabelAdmin, whiteLabelUrl );
        sendWhiteLabelUsersToChargebeeContact(whiteLabel,customer,loggedInUser);

        PlanConfig planConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName = planConfig.getPlanName();

        String subIdA;
        if((null == whiteLabel.getMobilePlanConfig() || whiteLabel.getMobilePlanConfig() == 4 ||
                whiteLabel.getMobilePlanConfig() == 6 || whiteLabel.getMobilePlanConfig() == 7 ) && whiteLabel.getMobileSubscriptionId() == null) {
            Subscription subscription = chargeBeeSubscriptionWorkFlowService.createMobileSubscriptionAAndB(customer.id(),
                    planConfig, whiteLabelAdmin, whiteLabel, billingType);
            subIdA = subscription.id();
        } else {
            chargeBeeSubscriptionWorkFlowService.changeMobileSubscriptionPlanAndUpdateRenewalDate(planConfig, whiteLabel, billingType);
            subIdA = whiteLabel.getMobileSubscriptionId();
        }

        whiteLabelRepoService.save(whiteLabel);
        //for Updating organizer Credits
        chargeBeeSubscriptionWorkFlowService.handleNotificationFutureEventsAndTransactionalTable(whiteLabel, whiteLabelAdmin, planConfig, subIdA);

        logger.info("Plan Purchase For White Label whitelabelHostBaseUrl{} PlanName{}",whiteLabel.getHostBaseUrl(),planName);

        chargebeeBillingService.savePlanUpdateInChargebeeTransaction(planConfig, whiteLabelAdmin, null, whiteLabel,
                whiteLabel.getMobileCustomerId(), whiteLabel.getMobileSubscriptionId());
        return planName;
    }


    private void saveChargebeeTransactionData(List<ChargesPurchasedDto> chargesPurchasedDtoList, Organizer organizer, User user, ChargeConfig chargeConfig, Invoice invoice, Integer quantity, boolean isForMobile) {//NOSONAR
        if(isForMobile){
            Optional<PlanConfig> planConfig = chargebeePlanRepoService.findById(organizer.getMobilePlanConfig());
            chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList)
                            ? JsonMapper.convertToString(chargesPurchasedDtoList) : Constants.STRING_EMPTY,
                    planConfig.orElse(null), user, organizer, null,
                    organizer.getMobileCustomerId(), organizer.getMobileSubscriptionId(), chargeConfig.getAmount() * quantity,
                    invoice != null ? invoice.id() : null, invoice != null ? invoice.status().name() : null);
        }
    }

    @Override
    public Invoice addMobileChargeQuantityToExistingSub(User loggedInUser, String token,
                                                        Long chargeId, Integer quantity, Organizer organizer) throws Exception {

        User billingContact = getBillingContact(loggedInUser, organizer);

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, organizer.getMobileCustomerId(),
                billingContact, organizer.getName());
        logger.info("addMobileChargeQuantityToExistingSub chargeBee CustomerId {} date {}", customer.id(), new Date());
        organizer.setMobileCustomerId(customer.id());
        return addMobileChargeForOrganizer(chargeId, quantity, organizer);
    }

    private Invoice addMobileChargeForOrganizer(Long chargeId, Integer quantity, Organizer organizer) throws Exception {
        ChargeConfig chargeConfig = chargeConfigRepoService.findByIdThrowException(chargeId);
        String subscriptionId = organizer.getMobileSubscriptionId();
        Date now = com.accelevents.utils.DateUtils.getCurrentDate();
        chargebeePaymentService.setAutoCollectionONorOFF(organizer.getMobileCustomerId(), AutoCollection.ON);
        Invoice invoice = chargebeePaymentService.addChargeToSubscription(subscriptionId, chargeConfig, quantity,"", BillingType.Paid.equals(organizer.getBillingType()));
        chargebeePaymentService.setAutoCollectionONorOFF(organizer.getMobileCustomerId(), AutoCollection.OFF);
        List<ChargesPurchased> chargesPurchasedList = new ArrayList<>();
        if (PLAN_USAGE_INCLUDED_CREDITS.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            chargesPurchasedList.add(prepareChargePurchased(organizer, chargeConfig, invoice, now));
        } else {
            for (int i = 0; i < quantity; i++) {
                chargesPurchasedList.add(prepareChargePurchased(organizer, chargeConfig, invoice, now));
            }
        }
        chargesPurchasedRepoService.saveAll(chargesPurchasedList);
        return invoice;
    }

    @Override
    @Transactional
    public String createOrUpdateMobileSubscriptionForOrganizer(User loggedInUser, String token, Long planId, Organizer organizer) throws Exception {

        checkNotOrgMemberRole(loggedInUser, organizer);

        User billingContact = getBillingContact(loggedInUser, organizer);

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, organizer.getMobileCustomerId(), billingContact, organizer.getName());
        logger.info("createOrUpdateMobileSubscriptionForOrganizer chargeBee CustomerId {} date {} loggedInUser {}", customer != null ? customer.id() : null, new Date(), loggedInUser.getUserId());
        organizer.setMobileCustomerId(customer.id());
        sendOrgTeamToChargebeeContact(organizer.getId(), customer,loggedInUser);//NOSONAR
        PlanConfig newPlanConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName = newPlanConfig.getPlanName();
        String subIdA;
        BillingType billingType = userService.isBillingTypeAdmin(loggedInUser) ? organizer.getBillingType() : BillingType.Paid;
        if (null == organizer.getMobilePlanConfig() || organizer.getMobilePlanConfig() == 4
                || (organizer.getMobilePlanConfig() == 6 && organizer.getMobileSubscriptionId() == null)) {
            Subscription subscriptionA = chargeBeeSubscriptionWorkFlowService.createMobileSubscriptionAAndB(customer.id(),
                    newPlanConfig, billingContact, organizer, billingType);
            subIdA = subscriptionA.id();
        } else {
            chargeBeeSubscriptionWorkFlowService.changeMobileSubscriptionPlanAndUpdateRenewalDate(newPlanConfig, organizer, billingType);
            subIdA = organizer.getMobileSubscriptionId();
        }
        organizerRepoService.save(organizer);

        chargeBeeSubscriptionWorkFlowService.sendNotificationAndUpdateActiveEventsAndTransactionalTableForMobile(subIdA, organizer);
        //auto activate ticketing Module
        activateTicketingModule(organizer.getId());
        return planName;
    }


    @Override
    @Transactional
    public String createOrUpdateSubscriptionForOrganizer(User loggedInUser, String token, Long planId, Long organizerId, boolean isForMobile,String hubSpotCompanyName,String hubSpotCompanyWebsiteDomain) throws Exception {
        Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
        PlanConfig planConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName ;
        raiseErrorIfLegacyPlanTryToActivateAfter2021(planConfig);

        if(!roUserService.isSuperAdminUser(loggedInUser) && PlanConfigNames.LEGACY.getName().equals(planConfig.getPlanName())) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_ACTIVATE_LEGACY_PLAN);
        }

        raiseErrorIfTokenNotFoundAndBillingTypeIsPaid(token, organizer.getBillingType());
        if(isForMobile || planConfig.getPlanName().equals("Unlimited App")){
            planName = this.createOrUpdateMobileSubscriptionForOrganizer(loggedInUser,token,planId,organizer);
            //for Updating organizer Credits
            chargebeeBillingService.savePlanUpdateInChargebeeTransaction(planConfig, loggedInUser, organizer, null,
                    organizer.getMobileCustomerId(), organizer.getMobileSubscriptionId());
        }else {
            planName = this.createOrUpdateSubscriptionForOrganizer(loggedInUser, token, planId, organizer);
            chargebeeBillingService.savePlanUpdateInChargebeeTransaction(organizer.getPlanConfig(), loggedInUser, organizer, null,
                    organizer.getChargebeeCustomerId(), organizer.getSubscriptionId());
        }
        Subscription subscription = roChargeBeeService.retrievesubscription(organizer.getSubscriptionId());
        updatePlanUsageOverageRateForOrganiser(organizer,subscription);
        updatePlanUsageIncludedCreditsForOrganiser(organizer,subscription);
        calculateSubscriptionFreeAndPaidCreditsForOrganiser(organizer,loggedInUser,false);
        updatePurchasedProBoothForOrganiser(organizer,null);
        organizer.setSubscriptionExpireAt(chargeBeeHelperService.findSubscriptionEndTermDate(subscription));
        organizer.setSubscriptionStatus(subscription.status().toString());
        organizer.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
        contactModuleSettingsService.updateAllEventEngageEmailLimit(null,organizer);
        saveSubscriptionEntitlements(organizer);
        chargeBeeHubSpotSyncService.createHubSpotCompanyIdAndUpdateChargeBeeCustomer(new HubSpotCompanyDTO(hubSpotCompanyName,hubSpotCompanyWebsiteDomain),organizer);
        hubspotOrganizerService.updateHubspotOrganizerProperty(organizer, CHARGEBEE_PLAN);
        logger.info("Plan Purchase For Organizer OrganizerPageUrl{} PlanName{}",organizer.getOrganizerPageURL(),planName);
        return planName;
    }
    private void saveSubscriptionEntitlements(Organizer organizer){
        String entitlements = this.getAllEntitlementsAssociatedWithSubscription(organizer.getSubscriptionId()).toString();
        logger.info("Insert or update organiser {} and entitlements {}",organizer.getOrganizerPageURL(),entitlements);
        organizer.setEntitlements(entitlements);
        organizerRepository.save(organizer);
    }

    public void addOrUpdateAttendeesCredits(Organizer organizer,
                                            WhiteLabel whiteLabel, Long planId, PlanConfig planConfig, User user) {
        Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional = Optional.empty();
        ChargebeeEventCredits chargebeeEventCredits = null;
        List<ChargebeeEventUsages> chargebeeEventUsagesList = new ArrayList<>();
        Long prePurchasedQuantity=0L;

        String subscriptionId = null;
        if (whiteLabel != null) {
            chargebeeEventCreditsOptional = chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
            subscriptionId = whiteLabel.getSubscriptionId();
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(null, whiteLabel.getId(),subscriptionId);
        } else if (organizer != null) {
            chargebeeEventCreditsOptional = chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
            subscriptionId = organizer.getSubscriptionId();
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(organizer.getId(), null,subscriptionId);
        }
        if (chargebeeEventCreditsOptional.isPresent()) {
            chargebeeEventCredits = chargebeeEventCreditsOptional.get();
            if (whiteLabel != null) {
                chargebeeEventUsagesList = chargebeeEventUsagesRepoService.findByWhiteLabelIdAndUsageTypeAndSubscriptionId(whiteLabel.getId(), Constants.WEB,subscriptionId);
            } else {
                chargebeeEventUsagesList = chargebeeEventUsagesRepoService.findByOrganiserIdAndUsageTypeAndSubscriptionId(organizer.getId(), Constants.WEB,subscriptionId);
            }
            chargebeeEventCredits.setSubscriptionId(subscriptionId);
            setRemainingFreeCredits(chargebeeEventCredits, chargebeeEventUsagesList, planConfig,prePurchasedQuantity);
            chargebeeEventCredits.setPlanConfigId(planId);
            chargebeeEventCredits.setUpdateByAdmin(true);
            chargebeeEventCreditsRepoService.save(chargebeeEventCredits, user);
        } else {
            chargebeeEventCredits = new ChargebeeEventCredits();
            if (whiteLabel != null) {
                chargebeeEventCredits.setWhiteLabelId(whiteLabel.getId());
                chargebeeEventCredits.setSubscriptionId(whiteLabel.getSubscriptionId());
            } else if (organizer != null) {
                chargebeeEventCredits.setOrganizerId(organizer.getId());
                chargebeeEventCredits.setSubscriptionId(organizer.getSubscriptionId());
            }
            chargebeeEventCredits.setPlanConfigId(planId);
            chargebeeEventCredits.setRemainingFreeCredits(planConfig.getFreeQuantity()+prePurchasedQuantity);
            chargebeeEventCreditsRepoService.save(chargebeeEventCredits, user);
            chargeBeeHelperService.createChargebeeLogRecord(subscriptionId, CreditType.PLAN_DEFAULT.name(), planConfig.getFreeQuantity(), whiteLabel, organizer, true);
        }
    }

    @Override
    public void updatePurchasedProBoothForOrganiser(Organizer organizer, String invoiceId) {
        updatePurchasedProBoothForOrganiserOrWhiteLabel(organizer, null, invoiceId);
    }

    @Override
    public void updatePurchasedProBoothForWhiteLabel(WhiteLabel whiteLabel, String invoiceId) {
        updatePurchasedProBoothForOrganiserOrWhiteLabel(null, whiteLabel, invoiceId);
    }
    private void updatePurchasedProBoothForOrganiserOrWhiteLabel(Organizer organizer, WhiteLabel whiteLabel, String invoiceId) {
        logger.info("updatePurchasedProBooth organizerId {} whiteLabelId {} invoiceId {}", organizer != null ? organizer.getId() : null, whiteLabel != null ? whiteLabel.getId() : null, invoiceId);
        Optional<ChargebeeEventCredits> chargebeeEventCreditsOpt = Optional.empty();
        boolean isProBoothPurchasedAtEventLevel = false;
        if (invoiceId != null) {
            isProBoothPurchasedAtEventLevel = chargebeeTransactionRepository.isChargeBeeTransactionExistByInvoiceId(invoiceId, ChargebeeTransactionSource.MODULE_ACTIVATE);
            logger.info("updatePurchasedProBooth chargebeeTransactions organizerId {} whiteLabelId {} invoiceId {} isProBoothPurchasedAtEventLevel {}", organizer != null ? organizer.getId() : null, whiteLabel != null ? whiteLabel.getId() : null, invoiceId, isProBoothPurchasedAtEventLevel);
        }
        Long purchasedProBoothQuantity = 0L;
        if (!isProBoothPurchasedAtEventLevel) {
            if (null != whiteLabel) {
                chargebeeEventCreditsOpt = chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
                purchasedProBoothQuantity = chargesPurchasedRepoService.getPurchasedProBoothQuantity(null, whiteLabel.getId(), whiteLabel.getSubscriptionId(), invoiceId);
            } else if (null != organizer) {
                chargebeeEventCreditsOpt = chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
                purchasedProBoothQuantity = chargesPurchasedRepoService.getPurchasedProBoothQuantity(organizer.getId(), null, organizer.getSubscriptionId(), invoiceId);
            }
            logger.info("updatePurchasedProBooth organizerId {} whiteLabelId {} invoiceId {} purchasedProBoothQuantity {}", organizer != null ? organizer.getId() : null, whiteLabel != null ? whiteLabel.getId() : null, invoiceId, purchasedProBoothQuantity);
            if (chargebeeEventCreditsOpt.isPresent() && purchasedProBoothQuantity > 0) {
                ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsOpt.get();
                chargebeeEventCredits.setRemainingPurchasedProBooths(chargebeeEventCredits.getRemainingPurchasedProBooths() + purchasedProBoothQuantity.intValue());
                chargebeeEventCreditsRepoService.save(chargebeeEventCredits, null);
            }
        }
    }

    public void setRemainingFreeCredits(ChargebeeEventCredits chargebeeEventCredits, List<ChargebeeEventUsages> chargebeeEventUsagesList, PlanConfig planConfig,Long prePurchasedQuantity) {
        if (chargebeeEventUsagesList != null && !chargebeeEventUsagesList.isEmpty()) {
            chargebeeEventCredits.setRemainingFreeCredits(chargebeeEventCredits.getRemainingFreeCredits() + planConfig.getFreeQuantity()+prePurchasedQuantity);
        } else if (chargebeeEventCredits.getRemainingFreeCredits() != chargebeeEventCredits.getPlanConfig().getFreeQuantity() && chargebeeEventCredits.getRemainingFreeCredits() > chargebeeEventCredits.getPlanConfig().getFreeQuantity()) {
            chargebeeEventCredits.setRemainingFreeCredits((chargebeeEventCredits.getRemainingFreeCredits() - chargebeeEventCredits.getPlanConfig().getFreeQuantity()) + planConfig.getFreeQuantity()+prePurchasedQuantity);
        } else {
            chargebeeEventCredits.setRemainingFreeCredits(planConfig.getFreeQuantity()+prePurchasedQuantity);
        }
    }

    public void setRemainingFreeCredit(ChargebeeEventCredits chargebeeEventCredits, List<ChargebeeEventUsages> chargebeeEventUsagesList, int subscriptionItemPlanFreeCredits, Long prePurchasedQuantity, boolean isSubscriptionRenew) {
        if (isSubscriptionRenew) {
            chargebeeEventCredits.setRemainingFreeCredits(subscriptionItemPlanFreeCredits + prePurchasedQuantity);
        } else if (!chargebeeEventUsagesList.isEmpty()) {
            AtomicReference<Long> totalUsage = new AtomicReference<>(0L);
            chargebeeEventUsagesList.forEach(usage -> totalUsage.updateAndGet(v -> v + usage.getQuantity()));
            chargebeeEventCredits.setRemainingFreeCredits((subscriptionItemPlanFreeCredits + prePurchasedQuantity) - totalUsage.get());
        } else {
            chargebeeEventCredits.setRemainingFreeCredits(subscriptionItemPlanFreeCredits + prePurchasedQuantity);
        }
    }
    private void raiseErrorIfLegacyPlanTryToActivateAfter2021(PlanConfig config) {
        if( PlanConfigNames.LEGACY.getName().equals(config.getPlanName()) || PlanConfigNames.WHITE_LABEL_LEGACY.getName().equals(config.getPlanName())){
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(new Date());
            if (calendar.get(Calendar.YEAR) > 2021){
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CAN_NO_ACTIVATE_LEGACY_AFTER_2021);
            }
        }
    }

    @Override
    @Transactional
    public String createOrUpdateSubscriptionForOrganizer(User loggedInUser, String token, Long planId, Organizer organizer) throws Exception {

        checkNotOrgMemberRole(loggedInUser, organizer);

        User billingContact = getBillingContact(loggedInUser, organizer);

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, organizer.getChargebeeCustomerId(), billingContact, organizer.getName());
        sendOrgTeamToChargebeeContact(organizer.getId(), customer,loggedInUser);
        PlanConfig newPlanConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName = newPlanConfig.getPlanName();
        String subIdA;
        BillingType billingType = userService.isBillingTypeAdmin(loggedInUser) ? organizer.getBillingType() : BillingType.Paid;
        Subscription subscription=null;
        //if the subscriptionId is null then not need to retrieve because if we try to retrieve then that will throw the exception
        if (null != organizer.getSubscriptionId()) {
            subscription = this.retrieveSubscription(organizer.getSubscriptionId());
        }
        if ((subscription!=null && Subscription.Status.CANCELLED.equals(subscription.status())) || PlanConfigNames.FREE_PLAN.getName().equals(organizer.getPlanConfig().getPlanName())
                || (PlanConfigNames.LEGACY.getName().equals(organizer.getPlanConfig().getPlanName()) && organizer.getSubscriptionId() == null)) {
            logger.info("createOrUpdateSubscriptionForOrganizer subscriptionId {} is null or CANCELLED",organizer.getSubscriptionId());
            Subscription subscriptionA = chargeBeeSubscriptionWorkFlowService.createSubscriptionAAndB(customer.id(),
                    newPlanConfig, billingContact, organizer, billingType);
            subIdA = subscriptionA.id();
        } else {
            chargeBeeSubscriptionWorkFlowService.changeSubscriptionPlanAndUpdateRenewalDate(newPlanConfig, organizer, billingType);
            subIdA = organizer.getSubscriptionId();
        }
        subscription = roChargeBeeService.retrievesubscription(organizer.getSubscriptionId());
        organizer.setSubscriptionExpireAt(chargeBeeHelperService.findSubscriptionEndTermDate(subscription));
        organizer.setSubscriptionStatus(subscription.status().toString());
        organizerRepoService.save(organizer);

        chargeBeeSubscriptionWorkFlowService.sendNotificationAndUpdateActiveEventsAndTransactionalTable(subIdA, organizer);
        //auto activate ticketing Module
        activateTicketingModule(organizer.getId());
        return planName;
    }

    private void checkNotOrgMemberRole(User loggedInUser, Organizer organizer) {
        String role = joinUsersWithOrganizersRepository.findRoleWithOrganizerId(organizer.getId(), loggedInUser.getUserId());
        if (OrganizerRole.member.name().equals(role)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.MEMBER_CANNOT_SUBSCRIBE_PLAN);
        }
    }

    private void activateTicketingModule(Long organizerId) {

        List<Event> eventsForTicketing = eventRepoService.findOnlineEventsWithNoPaidTicketsByOrganizerId(organizerId, EventFormat.VIRTUAL);
        if(!CollectionUtils.isEmpty(eventsForTicketing)) {
            eventsForTicketing.forEach(event -> {
                if (Boolean.TRUE.equals(event.getTicketingEnabled())) {
                    Ticketing ticketing = ticketingRepository.findByEventid(event);
                    if (ticketing!=null && !ticketing.getActivated()) {
                        ticketing.setActivated(true);
                        ticketingRepository.save(ticketing);
                    }
                    logger.info("In activateTicketingModule method Activating ticketing module for eventId {}", event.getEventId());
                }
            });
        }
    }

    @Override
    public String createOrUpdateSubscriptionForWhiteLabel(String whiteLabelUrl, String token, long planId, User loggedInUser) throws Exception {
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(whiteLabelUrl).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND));
        User whiteLabelAdmin = userRepoService.findUserByWhiteLabelId(whiteLabel.getId());

        raiseErrorIfTokenNotFoundAndBillingTypeIsPaid(token, whiteLabel.getBillingType());
        BillingType billingType = userService.isBillingTypeAdmin(loggedInUser) ? whiteLabel.getBillingType() : BillingType.Paid;

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, whiteLabel.getChargebeeCustomerId(), whiteLabelAdmin, whiteLabelUrl );
        logger.info("createOrUpdateSubscriptionForWhiteLabel chargeBee CustomerId {} date {}", customer != null ? customer.id() : null, new Date());
        sendWhiteLabelUsersToChargebeeContact(whiteLabel,customer,loggedInUser);

        PlanConfig planConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName = planConfig.getPlanName();

        String subIdA;
        if ((PlanConfigNames.FREE_PLAN.getName().equals(whiteLabel.getPlanConfig().getPlanName())
                || PlanConfigNames.LEGACY.getName().equals(whiteLabel.getPlanConfig().getPlanName())
                || PlanConfigNames.WHITE_LABEL_LEGACY.getName().equals(whiteLabel.getPlanConfig().getPlanName())
                ) && ((whiteLabel.getSubscriptionId() == null) || (whiteLabel.getMobileSubscriptionId()==null))){
            Subscription subscription = chargeBeeSubscriptionWorkFlowService.createSubscriptionAAndB(customer.id(),
                    planConfig, whiteLabelAdmin, whiteLabel, billingType);
            subIdA = subscription.id();
        } else {
            chargeBeeSubscriptionWorkFlowService.changeSubscriptionPlanAndUpdateRenewalDate(planConfig, whiteLabel, billingType);
            subIdA = whiteLabel.getSubscriptionId();
        }
        whiteLabel.setEntitlements(this.getAllEntitlementsAssociatedWithSubscription(subIdA).toString());
        whiteLabel.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
        whiteLabelRepoService.save(whiteLabel);
        List<Organizer> organisers = organizerRepository.findOrganizerByWhiteLabel(whiteLabel.getId());
        organisers.forEach(organizer -> {
            organizer.setPlanConfig(whiteLabel.getPlanConfig());
            organizer.setSubscriptionId(whiteLabel.getSubscriptionId());
            organizer.setChargebeeCustomerId(whiteLabel.getChargebeeCustomerId());
            organizer.setEntitlements(whiteLabel.getEntitlements());
            organizer.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
            logger.info("Set WL Organiser Plan organiserId {} subscriptionId {} planId {}",organizer.getId(),organizer.getSubscriptionId(),organizer.getPlanConfig().getId());
        });
        organizerRepository.saveAll(organisers);
        Subscription subscription = roChargeBeeService.retrievesubscription(whiteLabel.getSubscriptionId());
        updatePlanUsageOverageRateForWhiteLabel(whiteLabel, subscription);
        updatePlanUsageIncludedCreditsForWhiteLabel(whiteLabel, subscription);
        calculateSubscriptionFreeAndPaidCreditsForWhiteLabel(whiteLabel, loggedInUser,false);
        updatePurchasedProBoothForWhiteLabel(whiteLabel, null);
        contactModuleSettingsService.updateAllEventEngageEmailLimit(whiteLabel,null);
        chargeBeeSubscriptionWorkFlowService.handleNotificationFutureEventsAndTransactionalTable(whiteLabel, whiteLabelAdmin, planConfig, subIdA);

        logger.info("Plan Purchase For White Label whitelabelHostBaseUrl{} PlanName{}",whiteLabel.getHostBaseUrl(),planName);

        chargebeeBillingService.savePlanUpdateInChargebeeTransaction(whiteLabel.getPlanConfig(), whiteLabelAdmin, null, whiteLabel,
                whiteLabel.getChargebeeCustomerId(), whiteLabel.getSubscriptionId());
        return planName;
    }

    @Override
    @Transactional
    public void mapSubscriptionAAndCreateSubscriptionBForWL(String whiteLabelUrl, ChargeebeeCustomerDto chargeebeeCustomerDto, User loggedInUser) throws Exception {
        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(whiteLabelUrl).orElseThrow(() -> new NotFoundException(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND));
        User whiteLabelAdmin = userRepoService.findUserByWhiteLabelId(whiteLabel.getId());

        Subscription subscription = retrieveSubscription(chargeebeeCustomerDto.getSubscriptionId());

        if(!chargeebeeCustomerDto.getChargebeeCustomerId().equals(subscription.customerId())){
            chargeebeeCustomerDto.setChargebeeCustomerId(subscription.customerId());
        }

        Customer customer = retrieveCustomer(chargeebeeCustomerDto.getChargebeeCustomerId());

        sendWhiteLabelUsersToChargebeeContact(whiteLabel, customer,loggedInUser);

        logger.info("Subscription {} found with item price id {}",
                chargeebeeCustomerDto.getSubscriptionId(), subscription.subscriptionItems().get(0).itemPriceId());//NOSONAR
        PlanConfig planConfig = chargebeePlanRepoService.findPlanConfigBySubscriptionItemPriceIdOrThrow(subscription.subscriptionItems().get(0).itemPriceId());

        String subIdA;

        Double amount = 0d;
        List<ChargesPurchasedDto> chargesPurchasedDtoList = new ArrayList<>();
        String oldPlan = whiteLabel.getPlanConfig().getPlanName();
        if (!chargeebeeCustomerDto.getSubscriptionId().equals(whiteLabel.getSubscriptionId())) {
            raiseErrorIfSubscriptionAlreadyUsed(chargeebeeCustomerDto.getSubscriptionId());
            chargeBeeSubscriptionWorkFlowService.createSubscriptionBAndPopulateChargeBeeInfoInDb(
                    customer.id(), whiteLabelAdmin,chargeebeeCustomerDto.getSubscriptionId(), whiteLabel,  planConfig);
            subIdA = subscription.id();
        } else {
            updatePlanToExistingWhitelabel(whiteLabel);
            subIdA = whiteLabel.getSubscriptionId();
        }

        if(!oldPlan.equals(whiteLabel.getPlanConfig().getPlanName())) {
            amount += whiteLabel.getPlanConfig().getPrice();
        }
        whiteLabel.setEntitlements(this.getAllEntitlementsAssociatedWithSubscription(subIdA).toString());
        whiteLabel.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
        setRegistrationOverageCharge(whiteLabel);
        whiteLabelRepoService.save(whiteLabel);
        List<Organizer> organisers = organizerRepository.findOrganizerByWhiteLabel(whiteLabel.getId());
        organisers.forEach(organizer -> {
            organizer.setPlanConfig(whiteLabel.getPlanConfig());
            organizer.setSubscriptionId(whiteLabel.getSubscriptionId());
            organizer.setChargebeeCustomerId(whiteLabel.getChargebeeCustomerId());
            organizer.setEntitlements(whiteLabel.getEntitlements());
            organizer.setEngageEmailsLimit(whiteLabel.getEngageEmailsLimit());
        });
        organizerRepository.saveAll(organisers);
        updatePlanUsageOverageRateForWhiteLabel(whiteLabel,subscription);
        updatePlanUsageIncludedCreditsForWhiteLabel(whiteLabel,subscription);
        calculateSubscriptionFreeAndPaidCreditsForWhiteLabel(whiteLabel,loggedInUser,false);
        updatePurchasedProBoothForWhiteLabel(whiteLabel, null);
        contactModuleSettingsService.updateAllEventEngageEmailLimit(whiteLabel,null);
        logger.info("Enterprise Plan Purchase For WhiteLabel Organizer whiteLabelId{}",whiteLabel.getId());
        chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList) ? JsonMapper.convertToString(chargesPurchasedDtoList) : Constants.STRING_EMPTY,
                whiteLabel.getPlanConfig(), loggedInUser, null, whiteLabel, whiteLabel.getChargebeeCustomerId(), whiteLabel.getSubscriptionId(), amount);
        chargeBeeSubscriptionWorkFlowService.handleNotificationFutureEventsAndTransactionalTable(whiteLabel, whiteLabelAdmin, planConfig, subIdA);
    }

    public void setRegistrationOverageCharge(WhiteLabel whiteLabel) {
        logger.info("setRegistrationOverageCharge for whiteLabelId {}", whiteLabel.getId());
        ChargeConfig chargebeeCharge = chargeConfigRepoService.findByChargeDisplayNameThrowException(ChargeConfigNames.REGISTRATION_USAGE.getLableName());
        if (chargebeeCharge != null) {
            logger.info("setRegistrationOverageCharge whiteLabelId {} chargeAmout {}", whiteLabel.getId(), chargebeeCharge.getAmount());
            whiteLabel.setOverageCharge(chargebeeCharge.getAmount());
        }
    }
    @Override
    public Subscription mapSubscriptionAndCreateSubscriptionBForOrg(Organizer organizer, ChargeebeeCustomerDto chargeebeeCustomerDto, User user, User loggedInUser) throws Exception {
        logger.info("mapSubscriptionAndCreateSubscriptionBForOrg organizerId {}",organizer.getId());
        Subscription subscription = retrieveSubscription(chargeebeeCustomerDto.getSubscriptionId());

        if(!chargeebeeCustomerDto.getChargebeeCustomerId().equals(subscription.customerId())){
            chargeebeeCustomerDto.setChargebeeCustomerId(subscription.customerId());
        }
        Customer customer = retrieveCustomer(chargeebeeCustomerDto.getChargebeeCustomerId());
        sendOrgTeamToChargebeeContact(organizer.getId(), customer,loggedInUser);

        logger.info("mapSubscriptionAndCreateSubscriptionBForOrg organizerId {} subscriptionId {} itemPriceId {}",
                organizer.getId(), chargeebeeCustomerDto.getSubscriptionId(),subscription.subscriptionItems().get(0).itemPriceId());
        PlanConfig planConfig = chargebeePlanRepoService.findPlanConfigBySubscriptionItemPriceIdOrThrow(subscription.subscriptionItems().get(0).itemPriceId());

        raiseErrorIfLegacyPlanTryToActivateAfter2021(planConfig);

        chargeBeeSubscriptionWorkFlowService.createSubscriptionBAndPopulateChargeBeeInfoInDb(customer.id(), user, chargeebeeCustomerDto.getSubscriptionId(), organizer, planConfig);
        organizer.setEntitlements(this.getAllEntitlementsAssociatedWithSubscription(chargeebeeCustomerDto.getSubscriptionId()).toString());
        organizerRepoService.save(organizer);
        return subscription;
    }

    private User getFirstOrgOwner(Organizer organizer) {
        List<JoinUsersWithOrganizers> joinOrgByOrgId = joinUsersWithOrganizersRepository.findJoinOrgByOrgIdAndRole(organizer.getId(), OrganizerRole.owner);
        if (CollectionUtils.isEmpty(joinOrgByOrgId)) {
            throw new NotFoundException(NotFoundException.UserNotFound.USER_NOT_FOUND);
        }
        return joinOrgByOrgId.get(0).getUser();
    }

    private Invoice addChargeForOrganizer(Long chargeId, Integer quantity, Organizer organizer) throws Exception {
        ChargeConfig chargeConfig = chargeConfigRepoService.findByIdThrowException(chargeId);
        String subscriptionId = organizer.getSubscriptionId();
        Date now = com.accelevents.utils.DateUtils.getCurrentDate();
        chargebeePaymentService.setAutoCollectionONorOFF(organizer.getChargebeeCustomerId(), AutoCollection.ON);
        Invoice invoice = chargebeePaymentService.addChargeToSubscription(subscriptionId, chargeConfig, quantity,"", BillingType.Paid.equals(organizer.getBillingType()));
        chargebeePaymentService.setAutoCollectionONorOFF(organizer.getChargebeeCustomerId(), AutoCollection.OFF);
        List<ChargesPurchased> chargesPurchasedList = new ArrayList<>();
        if ( PLAN_USAGE_INCLUDED_CREDITS.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            chargesPurchasedList.add(prepareChargePurchased(organizer, chargeConfig, invoice, now));
        } else {
            for (int i = 0; i < quantity; i++) {
                chargesPurchasedList.add(prepareChargePurchased(organizer, chargeConfig, invoice, now));
            }
        }
        chargesPurchasedRepoService.saveAll(chargesPurchasedList);
        return invoice;
    }

    private String addChargeForWhiteLabel(Long chargeId, Integer quantity, WhiteLabel whiteLabel, User user) throws Exception {
        ChargeConfig chargeConfig = chargeConfigRepoService.findByIdThrowException(chargeId);
        String subscriptionId = whiteLabel.getSubscriptionId();
        Date now = com.accelevents.utils.DateUtils.getCurrentDate();
        chargebeePaymentService.setAutoCollectionONorOFF(whiteLabel.getChargebeeCustomerId(), AutoCollection.ON);
        Invoice invoice = null;
        if (whiteLabel.getSubscriptionId() != null) {
            invoice = chargebeePaymentService.addChargeToSubscription(subscriptionId, chargeConfig, quantity, "", BillingType.Paid.equals(whiteLabel.getBillingType()));
        } else if (whiteLabel.getChargebeeCustomerId() != null) {
            invoice = chargebeePaymentService.addChargeToCustomer(whiteLabel.getChargebeeCustomerId(), chargeConfig, quantity, "");
        }
        chargebeePaymentService.setAutoCollectionONorOFF(whiteLabel.getChargebeeCustomerId(), AutoCollection.OFF);
        List<ChargesPurchased> chargesPurchasedList = new ArrayList<>();
        if (PLAN_USAGE_INCLUDED_CREDITS.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            chargesPurchasedList.add(prepareChargePurchased(whiteLabel, chargeConfig, invoice, now));
        } else {
            for (int i = 0; i < quantity; i++) {
                chargesPurchasedList.add(prepareChargePurchased(whiteLabel, chargeConfig, invoice, now));
            }
        }
        chargesPurchasedRepoService.saveAll(chargesPurchasedList);

        List<ChargesPurchasedDto> chargesPurchasedDtoList = new ArrayList<>();
        chargesPurchasedDtoList.add(new ChargesPurchasedDto(chargeConfig.getChargeDisplayName(), quantity.longValue()));

        chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList)
                        ? JsonMapper.convertToString(chargesPurchasedDtoList) : STRING_EMPTY,whiteLabel.getPlanConfig(),
                user, null, whiteLabel, whiteLabel.getChargebeeCustomerId(), whiteLabel.getSubscriptionId(),
                chargeConfig.getAmount()*quantity, invoice != null ? invoice.id() : null, invoice != null ? invoice.status().name() : null);
        return chargeConfig.getChargeDisplayName();
    }

    private ChargesPurchased prepareChargePurchased(Organizer organizer, ChargeConfig chargeConfig, Invoice invoice, Date now) {
        ChargesPurchased chargesPurchased = new ChargesPurchased();
        chargesPurchased.setChargeConfig(chargeConfig);
        chargesPurchased.setSubscriptionId(organizer.getSubscriptionId());
        chargesPurchased.setOrganizerId(organizer.getId());
        chargesPurchased.setOrganizer(organizer);
        chargesPurchased.setInvoiceId(invoice != null ? invoice.id() : null);
        chargesPurchased.setChargeStatus(invoice != null ? Invoice.Status.PAID.equals(invoice.status()) ?
                ChargesPurchased.ChargeStatus.PAID : ChargesPurchased.ChargeStatus.UNPAID : ChargesPurchased.ChargeStatus.UNPAID);
        chargesPurchased.setDateOfPurchased(new Date());
        chargesPurchased.setDateOfExpiration(DateUtils.addYears(now, 1));
        return chargesPurchased;
    }

    private ChargesPurchased prepareChargePurchased(WhiteLabel whiteLabel, ChargeConfig chargeConfig, Invoice invoice, Date now) {
        ChargesPurchased chargesPurchased = new ChargesPurchased();
        chargesPurchased.setChargeConfig(chargeConfig);
        chargesPurchased.setWhiteLabel(whiteLabel);
        chargesPurchased.setSubscriptionId(whiteLabel.getSubscriptionId());
        chargesPurchased.setInvoiceId(invoice != null ? invoice.id() : null);
        chargesPurchased.setChargeStatus(invoice != null ? Invoice.Status.PAID.equals(invoice.status()) ?
                ChargesPurchased.ChargeStatus.PAID : ChargesPurchased.ChargeStatus.UNPAID : null);
        chargesPurchased.setDateOfPurchased(new Date());
        chargesPurchased.setDateOfExpiration(DateUtils.addYears(now, 1));
        return chargesPurchased;
    }

    private Subscription retrieveSubscription(String subscriptionId) {
        Subscription subscription;
        try {
            subscription = chargebeePaymentService.retrieveSubscription(subscriptionId);
        } catch (Exception e) {
            throw new NotFoundException(NotFoundException.ChargebeeSubscriptionNotFound.CHARGEBEE_SUBSCRIPTION_NOT_FOUND);
        }
        return subscription;
    }

    private Customer retrieveCustomer(String customerId) {
        try {
            Customer customer = chargebeePaymentService.retrieveCustomer(customerId);
            if(customer == null){
                throw new NotFoundException(NotFoundException.ChargebeeCustomerNotFound.CHARGEBEE_CUSTOMER_NOT_FOUND);
            }
            return customer;
        } catch (Exception e) {
            throw new NotFoundException(NotFoundException.ChargebeeCustomerNotFound.CHARGEBEE_CUSTOMER_NOT_FOUND);
        }
    }

    private void sendOrgTeamToChargebeeContact(Long organizerId,Customer customer,User loggedInUser){
        List<JoinUsersWithOrganizers> joinUsersWithOrganizers = joinUsersWithOrganizersRepository.findJoinOrgByOrgIdAndContactIdIsNull(organizerId);
        List<JoinUsersWithOrganizers> joinUsersWithOrganizersList = new ArrayList<>(0);
        if(!CollectionUtils.isEmpty(joinUsersWithOrganizers)) {
                joinUsersWithOrganizers.stream().filter(e -> (customer != null && customer.email() != null && e.getUser().getEmail()!= null && !e.getUser().getEmail().equalsIgnoreCase(customer.email()))).forEach(joinOrg -> {
                    User user = joinOrg.getUser();
                    String contactId = chargebeePaymentService.addContactToCustomer(user, customer.id(), joinOrg.isBillingContact(),loggedInUser);
                    joinOrg.setChargeBeeContactId(contactId);
                    joinUsersWithOrganizersList.add(joinOrg);
                });
        }
        joinUsersWithOrganizersRepository.saveAll(joinUsersWithOrganizersList);
    }

    private void sendWhiteLabelUsersToChargebeeContact(WhiteLabel whiteLabel, Customer customer, User loggedInUser){
        List<WhiteLabelUserNotification> whiteLabelUserNotifications = whiteLabelUserNotificationService.findByWhiteLabel(whiteLabel);
        List<WhiteLabelUserNotification> whiteLabelUserNotificationList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(whiteLabelUserNotifications)){
            whiteLabelUserNotifications.stream().filter(e->!e.getUser().getEmail().equalsIgnoreCase(customer.email())).forEach(whiteLabelUser -> {
                User user = whiteLabelUser.getUser();
                String contactId;
                if (StringUtils.isBlank(whiteLabelUser.getChargeBeeContactId())) {
                    contactId = chargebeePaymentService.addContactToCustomer(user,customer.id(),whiteLabelUser.isBillingContact(),loggedInUser);
                    whiteLabelUser.setChargeBeeContactId(contactId);
                }
                whiteLabelUserNotificationList.add(whiteLabelUser);
            });
        }
        whiteLabelUserNotificationService.saveAll(whiteLabelUserNotificationList);
    }

    @Override
    @Transactional
    public void mapSubscriptionForOrganizer(Long organizerId, ChargeebeeCustomerDto chargeebeeCustomerDto, User createdBy) throws Exception {
        logger.info("mapSubscriptionForOrganizer organizerId {} customerId {} subscriptionId {} invoiceId {} createdBy {}", organizerId, chargeebeeCustomerDto.getChargebeeCustomerId(), chargeebeeCustomerDto.getSubscriptionId(), chargeebeeCustomerDto.getInvoiceId(), createdBy);
        Organizer organizer = organizerRepoService.findByOrganizerId(organizerId);
        User user = getFirstOrgOwner(organizer);

        Subscription subscription;
        if (!chargeebeeCustomerDto.getSubscriptionId().equals(organizer.getSubscriptionId())) {
            logger.info("mapSubscriptionForOrganizer subscriptionId {} is not equal to organizer subscriptionId {}", chargeebeeCustomerDto.getSubscriptionId(), organizer.getSubscriptionId());
            raiseErrorIfSubscriptionAlreadyUsed(chargeebeeCustomerDto.getSubscriptionId());
            subscription = mapSubscriptionAndCreateSubscriptionBForOrg(organizer, chargeebeeCustomerDto, user, createdBy);
        } else {
            logger.info("mapSubscriptionForOrganizer subscriptionId {} is equal to organizer subscriptionId {}", chargeebeeCustomerDto.getSubscriptionId(), organizer.getSubscriptionId());
            subscription = updatePlanToExistingOrganizer(organizer);
        }
        chargeBeeSubscriptionWorkFlowService.sendNotificationAndUpdateActiveEventsAndTransactionalTable(subscription.id(), organizer);
        transactionFeeConditionalLogicService.updateTransCondLogic(organizer);
        organizer.setSubscriptionExpireAt(chargeBeeHelperService.findSubscriptionEndTermDate(subscription));
        organizer.setSubscriptionStatus(subscription.status().toString());
        organizer.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
        updatePlanUsageOverageRateForOrganiser(organizer, subscription);
        updatePlanUsageIncludedCreditsForOrganiser(organizer, subscription);
        calculateSubscriptionFreeAndPaidCreditsForOrganiser(organizer, createdBy,false);
        updatePurchasedProBoothForOrganiser(organizer, null);
        contactModuleSettingsService.updateAllEventEngageEmailLimit(null,organizer);
        logger.info("mapSubscriptionForOrganizer organizerId {} subscriptionId {} SubscriptionExpireAt {}", organizerId, chargeebeeCustomerDto.getSubscriptionId(), organizer.getSubscriptionExpireAt());
        organizerRepository.save(organizer);
        hubspotOrganizerService.updateHubspotOrganizerProperty(organizer, CHARGEBEE_PLAN);
    }

    private void raiseErrorIfSubscriptionAlreadyUsed(String subscriptionId) {

        String SUBSCRIPTION_ALREADY_LINKED = "Subscription id already linked with the {account}";//NOSONAR
        List<Organizer> organizers = organizerRepoService.findBySubscriptionId(subscriptionId);

        if(!CollectionUtils.isEmpty(organizers)) {
            prepareAndRaiseSubscriptionAlreadyLinkedException(SUBSCRIPTION_ALREADY_LINKED, "Organizer ", organizers.get(0).getOrganizerPageURL());
        }

        Optional<WhiteLabel> optionalWhiteLabel = whiteLabelRepoService.findBySubscriptionId(subscriptionId);

        optionalWhiteLabel.ifPresent(whiteLabel -> prepareAndRaiseSubscriptionAlreadyLinkedException(SUBSCRIPTION_ALREADY_LINKED, "Enterprise ", whiteLabel.getWhiteLabelUrl()));
    }

    private void prepareAndRaiseSubscriptionAlreadyLinkedException(String message, String orgOrWL, String url) {
        NotAcceptableException.NotAceptableExeceptionMSG exception = NotAcceptableException.NotAceptableExeceptionMSG.SUBSCRIPTION_ID_ALREADY_LINKED;
        exception.setDeveloperMessage(message.
                replace("{account}", orgOrWL + url));
        exception.setErrorMessage(message.
                replace("{account}", orgOrWL + url));
        logger.info("Error raise from prepareAndRaiseSubscriptionAlreadyLinkedException {}", exception);
        throw new NotAcceptableException(exception);
    }

    private boolean isSubscriptionIdExistInDatabase(String subscriptionId) {
        boolean isAvailable = false;
        logger.info("isSubscriptionExistInDatabase subscriptionId {}", subscriptionId);
        Optional<WhiteLabel> optionalWhiteLabel = whiteLabelRepoService.findBySubscriptionId(subscriptionId);
        if (optionalWhiteLabel.isPresent()) {
            isAvailable = true;
        }
        List<Organizer> organizers = organizerRepoService.findBySubscriptionId(subscriptionId);
        if (organizers != null && !organizers.isEmpty()) {
            isAvailable = true;
        }
        logger.info("isSubscriptionExistInDatabase subscriptionId {} isAvailable {}", subscriptionId, isAvailable);
        return isAvailable;
    }
    @Override
    public void associateCharge(ChargeebeeCustomerDto chargeebeeCustomerDto, Organizer organizer, User createdBy,//NOSONAR
                                WhiteLabel whiteLabel, boolean isCalledFromWebhook, boolean isForMobile,Subscription subscription) {
        logger.info("associateCharge subscriptionId {} invoiceId {} isCalledFromWebhook {} Subscription {}",chargeebeeCustomerDto.getSubscriptionId(),chargeebeeCustomerDto.getInvoiceId(),isCalledFromWebhook,subscription);
        if (chargeebeeCustomerDto.getInvoiceId() != null) {
            Optional<ChargebeeEventCredits> chargebeeEventCreditsOpt = Optional.empty();
            if (whiteLabel != null) {
                chargebeeEventCreditsOpt = chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
            } else if (organizer != null) {
                chargebeeEventCreditsOpt = chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
            }
            logger.info("associateCharge InvoiceId {}",chargeebeeCustomerDto.getInvoiceId());
            boolean isInvoiceIdDoesNotExistsInDB = CollectionUtils.isEmpty(chargesPurchasedRepoService.findByInvoiceId(chargeebeeCustomerDto.getInvoiceId()));
            logger.info("associateCharge subscriptionId {} isInvoiceIdDoesNotExistsInDB {}",chargeebeeCustomerDto.getSubscriptionId(),isInvoiceIdDoesNotExistsInDB);
            if (isInvoiceIdDoesNotExistsInDB) {
                Invoice invoice = retrieveInvoice(chargeebeeCustomerDto.getInvoiceId());
                List<Invoice.Note> notes = invoice.notes();
                boolean isSubscriptionExistInDatabase;
                if (null == invoice.subscriptionId()) {
                    //if charges and addons are added at the charge-bee customer level then we need to insert records in database
                    isSubscriptionExistInDatabase = true;
                    logger.info("associateCharge marking isSubscriptionExistInDatabase true because charges are added at the charge-bee customer level");
                } else {
                    isSubscriptionExistInDatabase = isSubscriptionIdExistInDatabase(invoice.subscriptionId());
                }
                logger.info("associateCharge InvoiceId {} isSubscriptionExistInDatabase {}",chargeebeeCustomerDto.getInvoiceId(),isSubscriptionExistInDatabase);
                double amount = 0d;
                List<ChargesPurchasedDto> chargesPurchasedDtoList = new ArrayList<>();
                for (Invoice.LineItem item : invoice.lineItems()) {
                    logger.info("associateCharge LineItem item {} EntityType {} isCalledFromWebhook {}",item,item.entityType(),isCalledFromWebhook);
                    if (Invoice.LineItem.EntityType.CHARGE_ITEM_PRICE.equals(item.entityType()) || Invoice.LineItem.EntityType.ADDON_ITEM_PRICE.equals(item.entityType())) {
                        String chargeId = item.entityId();
                        logger.info("associateCharge subscription {} chargeId {}",chargeebeeCustomerDto.getSubscriptionId(), chargeId);
                        Optional<ChargeConfig> chargeConfigOptional = chargeConfigRepoService.findByChargeId(chargeId);

                        if (chargeConfigOptional.isPresent()) {
                            List<ChargesPurchased> chargesPurchasedList = new ArrayList<>();
                            ChargeConfig chargeConfig = chargeConfigOptional.get();
                            if (!isCalledFromWebhook || DEDICATED_SUPPORT_CHARGE.getLableName().equals(chargeConfig.getChargeDisplayName())
                                    || LIVE_STREAMING_EXHIBITORS.getLableName().equals(chargeConfig.getChargeDisplayName())
                                    || LEAD_CAPTURE_EXHIBITORS.getLableName().equals(chargeConfig.getChargeDisplayName())
                                    || EXHIBITOR_PRO_BOOTH.getLableName().equals(chargeConfig.getChargeDisplayName()) ||
                                    PRE_PURCHASED_REGISTRATION_USAGE_CREDITS.getLableName().equals(chargeConfig.getChargeDisplayName())) {

                                if (EXHIBITOR_PRO_BOOTH.getLableName().equals(chargeConfig.getChargeDisplayName()) ||
                                        PRE_PURCHASED_REGISTRATION_USAGE_CREDITS.getLableName().equals(chargeConfig.getChargeDisplayName())) {
                                    logger.info("associateCharge PrePurchased Credits Name {}", chargeConfig.getChargeDisplayName());
                                    chargesPurchasedList.add(prepareChargeToLinkWithOrganizer(chargeConfig, organizer, invoice, item.dateFrom(),
                                            chargeebeeCustomerDto.getSubscriptionId() != null ? chargebeeService.getSubscriptionEndDate(chargeebeeCustomerDto.getSubscriptionId()) : null, whiteLabel, Long.valueOf(item.quantity()), false, false));
                                    // don't merge this if conditions. will add more else if conditions in future
                                } else {
                                    for (int i = 0; i < item.quantity(); i++) {
                                        logger.info("associateCharge PrePurchased Credits Name {} Quantity {}", chargeConfig.getChargeDisplayName(),item.quantity());
                                        chargesPurchasedList.add(prepareChargeToLinkWithOrganizer(chargeConfig, organizer, invoice, item.dateFrom(),
                                                chargeebeeCustomerDto.getSubscriptionId() != null ? chargebeeService.getSubscriptionEndDate(chargeebeeCustomerDto.getSubscriptionId()) : null, whiteLabel, null, false, false));
                                    }
                                }
                                amount = amount + (item.quantity() * chargeConfig.getAmount());
                                chargesPurchasedRepoService.saveAll(chargesPurchasedList);
                                chargesPurchasedDtoList.add(new ChargesPurchasedDto(chargeConfig.getChargeDisplayName(), item.quantity().longValue()));
                                if (PRE_PURCHASED_REGISTRATION_USAGE_CREDITS.getLableName().equals(chargeConfig.getChargeDisplayName()) && chargebeeEventCreditsOpt.isPresent()) {
                                    ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsOpt.get();
                                    chargebeeEventCredits.setRemainingFreeCredits(chargebeeEventCredits.getRemainingFreeCredits() + item.quantity());
                                    chargebeeEventCreditsRepoService.save(chargebeeEventCredits, null);
                                }
                            }
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(chargesPurchasedDtoList)) {
                    if (isForMobile) {
                        this.saveMobileChargeUpdateInChargebeeTransaction(chargesPurchasedDtoList, whiteLabel, organizer, createdBy, chargeebeeCustomerDto, amount, invoice);
                    } else {
                        chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList)
                                        ? JsonMapper.convertToString(chargesPurchasedDtoList) : STRING_EMPTY,
                                whiteLabel != null ? whiteLabel.getPlanConfig() : organizer.getPlanConfig(), CHARGEBEE.equals(createdBy.getFirstName()) ? null
                                        : createdBy, organizer, whiteLabel, chargeebeeCustomerDto.getChargebeeCustomerId(),
                                chargeebeeCustomerDto.getSubscriptionId(), amount, invoice.id(), invoice.status().name());
                    }
                }
            }
        }
        else{
            if (null != subscription) {
                logger.info("associateCharge InvoiceId is null Subscription {}", chargeebeeCustomerDto.getSubscriptionId());
                Long count = chargebeeCreditsLogsRepository.findCountBySubscriptionId(chargeebeeCustomerDto.getSubscriptionId());
                logger.info("associateCharge InvoiceId is null Subscription {} count {}",chargeebeeCustomerDto.getSubscriptionId(),count);
                if (0==count)
                {
                    Integer dueInvoiceCount = subscription.dueInvoicesCount();
                    for (Subscription.SubscriptionItem subscriptionItem:subscription.subscriptionItems()){
                        logger.info("associateCharge Subscription {} SubscriptionItem {} subscriptionItemType {}",chargeebeeCustomerDto.getSubscriptionId(),subscriptionItem.itemPriceId(),subscriptionItem.itemType());
                        if (SUBSCRIPTION_ITEM_TYPE_CHARGE.equals(String.valueOf(subscriptionItem.itemType())) ||
                                SUBSCRIPTION_ITEM_TYPE_ADDON.equals(String.valueOf(subscriptionItem.itemType()))) {
                            Optional<ChargeConfig> chargeConfigOptional = chargeConfigRepoService.findByChargeId(subscriptionItem.itemPriceId());
                            if (chargeConfigOptional.isPresent()) {
                                logger.info("associateCharge chargeConfigOptionalId {}",chargeConfigOptional.get().getId());
                                ChargeConfig chargeConfig = chargeConfigOptional.get();
                                boolean isChargeInvoicePaid= dueInvoiceCount == 0;
                                ChargesPurchased chargesPurchased = prepareChargeToLinkWithOrganizer(chargeConfig, organizer, null, null,
                                        chargeebeeCustomerDto.getSubscriptionId() != null ? chargebeeService.getSubscriptionEndDate(chargeebeeCustomerDto.getSubscriptionId()) : null, whiteLabel, Long.valueOf(subscriptionItem.quantity()),isChargeInvoicePaid,true);
                                chargesPurchasedRepoService.save(chargesPurchased);
                                ChargesPurchasedDto chargesPurchasedDto=new ChargesPurchasedDto(chargeConfig.getChargeDisplayName(),Long.valueOf(subscriptionItem.quantity()));
                                chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(JsonMapper.convertToString(chargesPurchasedDto),
                                        whiteLabel != null ? whiteLabel.getPlanConfig() : organizer.getPlanConfig(), CHARGEBEE.equals(createdBy.getFirstName()) ? null
                                                : createdBy, organizer, whiteLabel, chargeebeeCustomerDto.getChargebeeCustomerId(),
                                        chargeebeeCustomerDto.getSubscriptionId(), Double.valueOf(subscriptionItem.amount()), null,isChargeInvoicePaid?PAID: String.valueOf(PAYMENT_DUE));
                            }
                        }
                    }
                }
            }
        }
    }

    private void saveMobileChargeUpdateInChargebeeTransaction(List<ChargesPurchasedDto> chargesPurchasedDtoList, WhiteLabel whiteLabel, Organizer organizer,
                                                              User createdBy, ChargeebeeCustomerDto chargeebeeCustomerDto,double amount,Invoice invoice) {
        Optional<PlanConfig> planConfig = chargebeePlanRepoService.findById(null != whiteLabel ? whiteLabel.getMobilePlanConfig() : organizer.getMobilePlanConfig());
        chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(!CollectionUtils.isEmpty(chargesPurchasedDtoList)
                        ? JsonMapper.convertToString(chargesPurchasedDtoList) : STRING_EMPTY,
                planConfig.orElse(null), CHARGEBEE.equals(createdBy.getFirstName()) ? null
                        : createdBy, organizer, whiteLabel, chargeebeeCustomerDto.getChargebeeCustomerId(),
                chargeebeeCustomerDto.getSubscriptionId(), amount, invoice.id(), invoice.status().name());

    }

    @Override
    public String addChargeQuantityToWL(User user, String token, Long chargeId, Integer quantity, String whiteLabelUrl) throws Exception {

        WhiteLabel whiteLabel = whiteLabelRepoService.findWhiteLabelOrNotFoundException(whiteLabelUrl);
        User billingContact = getBillingContact(user, whiteLabel);

        Customer customer = chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, whiteLabel.getChargebeeCustomerId(),
                billingContact, whiteLabelUrl);
        logger.info("addChargeQuantityToWL chargeBee customerId {} date {}",customer.id(),new Date());
        if(whiteLabel.getChargebeeCustomerId() == null){
            whiteLabel.setChargebeeCustomerId(customer.id());
            whiteLabelRepoService.save(whiteLabel);
        }

        return addChargeForWhiteLabel(chargeId, quantity, whiteLabel, billingContact);
    }

    private Subscription updatePlanToExistingOrganizer(Organizer organizer) {
        String subscriptionId = organizer.getSubscriptionId();
        List<String> yearlyAndQuarterlyPlanIdList = getYearlyAndQuarterlyPlanIdList();
        Subscription subscription = retrieveSubscription(subscriptionId);
        Optional<Subscription.SubscriptionItem> subscriptionItemOpt = subscription.subscriptionItems().stream().filter(
                item -> yearlyAndQuarterlyPlanIdList.contains(item.itemPriceId())).findFirst();
        if(subscriptionItemOpt.isEmpty()) {
            logger.error("No subscription Item found organiserId {}",organizer.getId());
            return subscription;
        }

        PlanConfig planConfig = chargebeePlanRepoService.findPlanConfigBySubscriptionItemPriceIdOrThrow(subscriptionItemOpt.get().itemPriceId());
        raiseErrorIfLegacyPlanTryToActivateAfter2021(planConfig);
        organizer.setPlanConfig(planConfig);
        organizer.setSubscriptionStatus(subscription.status().toString());
        organizer.setSubscriptionExpireAt(chargeBeeHelperService.findSubscriptionEndTermDate(subscription));
        organizer.setEntitlements(this.getAllEntitlementsAssociatedWithSubscription(subscriptionId).toString());
        organizerRepository.save(organizer);
        return subscription;
    }

    private void updatePlanToExistingWhitelabel(WhiteLabel whiteLabel) {
        String subscriptionId = whiteLabel.getSubscriptionId();
        List<String> yearlyAndQuarterlyPlanIdList = getYearlyAndQuarterlyPlanIdList();
        Subscription subscription = retrieveSubscription(subscriptionId);
        Optional<Subscription.SubscriptionItem> subscriptionItemOpt = subscription.subscriptionItems().stream().filter(
                item -> yearlyAndQuarterlyPlanIdList.contains(item.itemPriceId())).findFirst();
        if(subscriptionItemOpt .isEmpty()) {
            logger.error("No subscription Item found whiteLabelId {}",whiteLabel.getId());
            return;
        }

        PlanConfig planConfig = chargebeePlanRepoService.findPlanConfigBySubscriptionItemPriceIdOrThrow(subscriptionItemOpt.get().itemPriceId());
        raiseErrorIfLegacyPlanTryToActivateAfter2021(planConfig);
        whiteLabel.setSubscriptionStatus(subscription.status().toString());
        whiteLabel.setSubscriptionExpireAt(chargeBeeHelperService.findSubscriptionEndTermDate(subscription));
        whiteLabel.setPlanConfig(planConfig);
        whiteLabelRepoService.save(whiteLabel);

    }
    @Override
    public Invoice addChargeQuantityToExistingSub(User loggedInUser, String token,
                                               Long chargeId, Integer quantity, Organizer organizer) throws Exception {

        User billingContact = getBillingContact(loggedInUser, organizer);

        chargebeePaymentService.createCardForCustomerWithoutChargeBeeCreationDescription(token, organizer.getChargebeeCustomerId(),
                billingContact, organizer.getName());
        transactionFeeConditionalLogicService.updateTransCondLogic(organizer);
        return addChargeForOrganizer(chargeId, quantity, organizer);
    }

    private User getBillingContact(User loggedInUser, Organizer organizer) {
        List<User> billingContactByUser = joinUsersWithOrganizersRepository.findBillingContactByOrgId(organizer.getId());
        return !billingContactByUser.isEmpty() ? billingContactByUser.get(0) : loggedInUser;
    }

    private User getBillingContact(User loggedInUser, WhiteLabel whiteLabel) {
        List<User> billingContactByUser = joinUsersWithOrganizersRepository.findBillingContactByWLId(whiteLabel.getId());
        return !billingContactByUser.isEmpty() ? billingContactByUser.get(0) : loggedInUser;
    }

    private Invoice retrieveInvoice(String invoiceId) {
        Invoice invoice;
        try {
            invoice = chargebeePaymentService.retrieveInvoice(invoiceId);
        } catch (Exception e) {
            throw new NotFoundException(NotFoundException.ChargebeeInvoiceNotFound.CHARGEBEE_INVOICE_NOT_FOUND);
        }
        return invoice;
    }

    @Override
    @Transactional
    public String createSubscriptionIfNotExistsAndAddChargeQuantity(User user, String token, Long organizerId, Long planId,
                                                                    Long chargeId, Integer quantity, boolean isForMobile,String hubSpotCompanyName,String hubSpotCompanyWebsiteDomain) throws Exception {
        Organizer organizer = organizerRepoService.findByOrganizerId(organizerId);

        raiseErrorIfTokenNotFoundAndBillingTypeIsPaid(token, organizer.getBillingType());

        raiseErrorIfTokenNotFoundAndNotAuthForCompType(token, organizer.getBillingType(), user);

        validatePlanQty(quantity);

        PlanConfig planConfig = chargebeePlanRepoService.findByIdOrThrowError(planId);
        String planName = planConfig.getPlanName();


        Invoice invoice = null;
        Subscription subscription=null;
        if(isForMobile) {
            logger.info("ChargebeePaymentHandler | createSubscriptionIfNotExistsAndAddChargeQuantity | for mobile ChargeId {}",chargeId);
            if (null == organizer.getMobilePlanConfig() || organizer.getMobilePlanConfig() == 4){
                this.createOrUpdateMobileSubscriptionForOrganizer(user,token,planId,organizer);
                invoice = addMobileChargeForOrganizer(chargeId,quantity,organizer);
            }else{
                invoice = addMobileChargeQuantityToExistingSub(user,token,chargeId,quantity,organizer);
            }
        }else{
            //if the subscriptionId is null then not need to retrieve because if we try to retrieve then that will throw the exception
            if (null != organizer.getSubscriptionId()) {
                subscription = this.retrieveSubscription(organizer.getSubscriptionId());
            }
            if ((null!=subscription && Subscription.Status.CANCELLED.equals(subscription.status())) || PlanConfigNames.FREE_PLAN.getName().equals(organizer.getPlanConfig().getPlanName())
                    || PlanConfigNames.LEGACY.getName().equals(organizer.getPlanConfig().getPlanName())) {
                logger.info("createSubscriptionIfNotExistsAndAddChargeQuantity subscriptionId {} is null or CANCELLED",organizer.getSubscriptionId());
                this.createOrUpdateSubscriptionForOrganizer(user, token, planId, organizer);
                if (quantity > 1) {
                    // first time plan activation
                    // we are adding one plan quantity by default from chargebee, if user select more than 1 quantity then we need to decrease one quantity
                    invoice = addChargeForOrganizer(chargeId, quantity-1, organizer);
                }
            } else {
                invoice = addChargeQuantityToExistingSub(user, token, chargeId, quantity, organizer);
            }
            saveSubscriptionEntitlements(organizer);
        }
        ChargeConfig chargeConfig = chargeConfigRepoService.findByIdThrowException(chargeId);

        logger.info("Plan Purchase For Single Unit Plan OrganizerPageUrl{} PlanName{}",organizer.getOrganizerPageURL(),planName);

        List<ChargesPurchasedDto> chargesPurchasedDtoList = new ArrayList<>();
        chargesPurchasedDtoList.add(new ChargesPurchasedDto(chargeConfig.getChargeDisplayName(), quantity.longValue()));

        saveChargebeeTransactionData(chargesPurchasedDtoList,organizer,user,chargeConfig,invoice,quantity,isForMobile);
        subscription = roChargeBeeService.retrievesubscription(organizer.getSubscriptionId());
        updatePlanUsageOverageRateForOrganiser(organizer,subscription);
        updatePlanUsageIncludedCreditsForOrganiser(organizer,subscription);
        calculateSubscriptionFreeAndPaidCreditsForOrganiser(organizer,user,false);
        updatePurchasedProBoothForOrganiser(organizer,null);
        organizer.setSubscriptionExpireAt(chargeBeeHelperService.findSubscriptionEndTermDate(subscription));
        organizer.setSubscriptionStatus(subscription.status().toString());
        organizer.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
        contactModuleSettingsService.updateAllEventEngageEmailLimit(null,organizer);
        chargeBeeHubSpotSyncService.createHubSpotCompanyIdAndUpdateChargeBeeCustomer(new HubSpotCompanyDTO(hubSpotCompanyName,hubSpotCompanyWebsiteDomain),organizer);
        organizerRepository.save(organizer);
        hubspotOrganizerService.updateHubspotOrganizerProperty(organizer, CHARGEBEE_PLAN);
        return chargeConfig.getChargeDisplayName();
    }

    private void raiseErrorIfTokenNotFoundAndNotAuthForCompType(String token, BillingType billingType, User user) {
        if(token == null && BillingType.Comp.equals(billingType)){
            userService.checkIsBillingTypeAdminOrThrowError(user);
        }
    }

    @Override
    public void raiseErrorIfTokenNotFoundAndBillingTypeIsPaid(String token, BillingType billingType) {
        if(token == null && BillingType.Paid.equals(billingType)){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.TOKEN_CANNOT_BE_EMPTY);
        }
    }

    private ChargesPurchased prepareChargeToLinkWithOrganizer(ChargeConfig chargeConfig, Organizer organizer, Invoice invoice,
                                                              Timestamp dateFrom, Timestamp dateTo, WhiteLabel whiteLabel, Long quantity, boolean isChargeInvoicePaid, boolean isChargeCountGetFromTheSubscription) {
        ChargesPurchased chargesPurchased = new ChargesPurchased();
        chargesPurchased.setChargeConfig(chargeConfig);
        chargesPurchased.setOrganizer(organizer);
        chargesPurchased.setOrganizerId(organizer == null ? null : organizer.getId());
        chargesPurchased.setInvoiceId(invoice == null ? null : invoice.id());
        if (Boolean.TRUE.equals(isChargeCountGetFromTheSubscription)) {
            chargesPurchased.setChargeStatus(isChargeInvoicePaid ? ChargesPurchased.ChargeStatus.PAID : ChargesPurchased.ChargeStatus.UNPAID);
        } else {
            chargesPurchased.setChargeStatus(invoice == null ? ChargesPurchased.ChargeStatus.CREATED : invoice.status().equals(Invoice.Status.PAID) ?
                    ChargesPurchased.ChargeStatus.PAID : ChargesPurchased.ChargeStatus.UNPAID);
        }
        chargesPurchased.setDateOfPurchased(dateFrom);
        chargesPurchased.setDateOfExpiration(dateTo);
        chargesPurchased.setWhiteLabel(whiteLabel);
        chargesPurchased.setQuantity(quantity);
        if (whiteLabel != null) {
            chargesPurchased.setSubscriptionId(whiteLabel.getSubscriptionId());
        } else if (organizer != null) {
            chargesPurchased.setSubscriptionId(organizer.getSubscriptionId());
        }
        return chargesPurchased;
    }

    private void validatePlanQty(Integer quantity) {
        if(quantity !=null && quantity > 20){
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CANNOT_PURCHASE_MORE_THEN_TWENTY);
        }
    }

    @Override
    public Invoice addChargesForAttendeeUploadToSubscription(Integer quantity, String subscriptionId, String notes, boolean isPaidBillingType) throws Exception {
        ChargeConfig chargeConfig = chargeConfigRepoService.findByChargeDisplayNameThrowException(ATTENDEE_UPLOAD_CHARGE.getLableName());
        return chargebeePaymentService.addChargeToSubscription(subscriptionId, chargeConfig, quantity,notes, isPaidBillingType);
    }

    @Override
    public ChargeBeeDetails getOrganizerOrWLChargebeeDetails(Event event) {
        if (event.getWhiteLabel() != null) {
            return event.getWhiteLabel();
        } else if (event.getOrganizer() != null) {
            return event.getOrganizer();
        }
        return null;
    }

    @Override
    public User getOrgOrWLBillingContact(User loggedInUser, Event event) {
        if (event.getWhiteLabel() != null) {
            User billingContact = userRepository.findUserByWhiteLabelId(event.getWhiteLabel().getId());
            if(billingContact==null){
                throw new NotFoundException(NotFoundException.UserNotFound.USER_REQUIRED_TEXT_TO_GIVE_SUBSCRIPTION_ID);
            }
        } else if (event.getOrganizer() != null) {
            return getBillingContact(loggedInUser, event.getOrganizer());
        }
        return loggedInUser;
    }

    @Override
    public void updateCustomerIdInOrgOrWL(Event event, String customerId){
        if(event.getWhiteLabel()!=null){
            WhiteLabel whiteLabel = event.getWhiteLabel();
            whiteLabel.setChargebeeCustomerId(customerId);
            whiteLabelRepoService.save(whiteLabel);
        } else if(event.getOrganizer()!=null){
            Organizer organizer = event.getOrganizer();
            organizer.setChargebeeCustomerId(customerId);
            organizerService.save(organizer);
        }
    }

    @Override
    public Invoice addChargesForAttendeeUploadToCustomer(int quantity, String chargebeeCustomerId, String notes, boolean isPaidBillingType) throws Exception {
        ChargeConfig chargeConfig = chargeConfigRepoService.findByChargeDisplayNameThrowException(ATTENDEE_UPLOAD_CHARGE.getLableName());
        return chargebeePaymentService.addChargeToCustomer(chargebeeCustomerId, chargeConfig, quantity,notes);
    }

    @Override
    public Invoice importAttendeesUpload(Event event, PaymentStatus status, int quantity, String customerId, Date invoiceDate) throws Exception {
        return chargebeePaymentService.importAttendeesUpload(event, status, quantity, customerId, invoiceDate);
    }

    @Override
    public boolean isEntitlementsAvailable(Event event, ChargebeeEntitlements entitlement) {
        ChargeBeeDetails chargeBeeDetails = null;

        if (event.getWhiteLabel() != null) {
            chargeBeeDetails = roWhiteLabelService.getWhiteLabelById(event.getWhiteLabelId()).orElse(null);
        } else if (event.getOrganizer() != null) {
            chargeBeeDetails = organizerRepository.findById(event.getOrganizerId()).orElse(null);
        }
        return isEntitlementsAvailable(chargeBeeDetails, entitlement);
    }

    @Override
    public boolean isEntitlementsAvailable(ChargeBeeDetails chargeBeeDetails, ChargebeeEntitlements entitlement) {

        if(chargeBeeDetails != null && chargeBeeDetails.getEntitlements() != null){
            ObjectMapper objectMapper = new ObjectMapper();
            try{

                List<EntitlementsDTO> entitlementsDTO = objectMapper.readValue(chargeBeeDetails.getEntitlements(), new TypeReference<List<EntitlementsDTO>>() {});

                return entitlementsDTO.stream().anyMatch(entitlements -> entitlements.getFeatureId().equals(entitlement.getEntitlementId())
                        && entitlements.isEnabled()
                        && "Available".equals(entitlements.getName())
                        && "true".equals(entitlements.getValue()));
            }
            catch (Exception e){
                logger.info("exception raised while de-serialize entitlement {} | Exception {}",chargeBeeDetails.getEntitlements(),e.getMessage());
            }

        }
        return Boolean.FALSE;
    }
    @Override
    public JSONArray getAllEntitlementsAssociatedWithSubscription(String subscriptionId) {
        JSONArray result = new JSONArray();

        try{
            Environment.configure(chargebeeConfiguration.getChargebeeSite(), chargebeeConfiguration.getChargebeeAPIKey());
            ListResult listResult = SubscriptionEntitlement.subscriptionEntitlementsForSubscription(subscriptionId).request();
            if(listResult != null && !listResult.isEmpty()){
                for(ListResult.Entry entry:listResult) {
                    result.put(new JSONObject(entry.subscriptionEntitlement().toJson()));
                }
            }
        }
        catch (Exception exception){
            logger.info("Exception raised while fetching Entitlements for a subscription {} | exception {}",subscriptionId, exception.getMessage());
        }
        return result;
    }

    @Override
    public void updateEntitlements(List<String> subscriptionId) {
        List<WhiteLabel> whiteLabels = whiteLabelRepoService.findBySubscriptionIds(subscriptionId).stream()
                .peek(e -> e.setEntitlements(this.getAllEntitlementsAssociatedWithSubscription(e.getSubscriptionId()).toString()))
                .collect(Collectors.toList());

        List<Organizer> organizers = organizerRepoService.findBySubscriptionIds(subscriptionId).stream()
                .peek(e -> e.setEntitlements(this.getAllEntitlementsAssociatedWithSubscription(e.getSubscriptionId()).toString()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(whiteLabels)) {
            whiteLabelRepoService.saveAll(whiteLabels);
        }
        if (!CollectionUtils.isEmpty(organizers)) {
            organizerRepoService.saveAll(organizers);
        }
    }


    public void createChargeBeeEventCreditsRecordIfNotExits(Organizer organizer) {
        Optional<ChargebeeEventCredits> chargebeeEventCreditsOtp = chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
        if (chargebeeEventCreditsOtp.isEmpty()) {
            logger.info("createChargeBeeEventCreditsRecordIfNotExits organiserId {} ", organizer.getId());
            try {
                ChargebeeEventCredits chargebeeEventCredits = new ChargebeeEventCredits();
                chargebeeEventCredits.setOrganizer(organizer);
                chargebeeEventCredits.setOrganizerId(organizer.getId());
                chargebeeEventCredits.setPlanConfig(organizer.getPlanConfig());
                chargebeeEventCredits.setPlanConfigId(organizer.getPlanConfig().getId());
                chargebeeEventCredits.setRemainingFreeCredits(0);
                chargebeeEventCredits.setSubscriptionId(organizer.getSubscriptionId());
                chargebeeEventCredits.setUpdateByAdmin(true);
                chargebeeEventCredits.setRemainingPurchasedProBooths(0);
                chargebeeEventCreditsRepoService.save(chargebeeEventCredits, null);
            } catch (Exception exception) {
                logger.info("createChargeBeeEventCreditsRecordIfNotExits organiserId {} exception {} ", organizer.getId(), exception.getMessage());
            }
        }
    }

    public List<String> getYearlyAndQuarterlyPlanIdList() {
        Iterable<PlanConfig> chargebeePlans = chargebeePlanRepoService.findAll();
        Set<String> yearlyAndQuarterlyPlanIdList = new HashSet<>();
        chargebeePlans.forEach(plan -> {
            yearlyAndQuarterlyPlanIdList.add(plan.getYearlyPlanId());
            yearlyAndQuarterlyPlanIdList.add(plan.getNewYearlyPlanId());
            yearlyAndQuarterlyPlanIdList.add(plan.getQuarterlyPlanId());
        });
        return new ArrayList<>(yearlyAndQuarterlyPlanIdList);
    }

    public void updateItemsPrice(String id, Long newPrice) {
        logger.info("updateItemsPrice id {} newPrice {}", id, newPrice);
        Optional<ChargeConfig> chargebeeChargeOpt = chargeConfigRepoService.findByChargeId(id);
        if (chargebeeChargeOpt.isPresent()) {
            ChargeConfig chargebeeCharge = chargebeeChargeOpt.get();
            if (REGISTRATION_USAGE.getLableName().equals(chargebeeCharge.getChargeDisplayName())) {
                Double oldPrice = chargebeeCharge.getAmount();
                logger.info("updateItemsPrice id {} oldPrice {}", id, oldPrice);
                chargebeeCharge.setAmount(GeneralUtils.convertCentToDollar(newPrice));
                chargeConfigRepoService.save(chargebeeCharge);
                List<WhiteLabel> whiteLabelList = whiteLabelService.findAllWhiteLabelWhoHaveLatestPlan();
                StringBuilder whiteLabelIds = new StringBuilder();
                whiteLabelList.forEach(whiteLabel -> whiteLabelIds.append(whiteLabel.getId()).append(","));
                logger.info("The overage charge updated for WhiteLabel upon receiving a webhook from ChargeBee. whiteLabelIds {}", whiteLabelIds);
                List<WhiteLabel> whiteLabelListToSave = new ArrayList<>();
                whiteLabelList.forEach(whiteLabel -> {
                    if (oldPrice.equals(whiteLabel.getOverageCharge())) {
                        whiteLabel.setOverageCharge(GeneralUtils.convertCentToDollar(newPrice));
                        whiteLabelListToSave.add(whiteLabel);
                    }
                });
                whiteLabelService.saveAll(whiteLabelListToSave);
            }
        }
    }

    public void savePlanUsageOverageRateChargesPurchasedIfListIfEmpty(ChargeConfig chargeBeeCharge, WhiteLabel whiteLabel, Organizer organizer, Subscription subscription) {
        List<ChargesPurchased> chargesPurchasedList = chargesPurchasedRepoService.findPurchasedChargesByChargeIdAndOrganiserOrWhiteLabelAndSubscriptionId(chargeBeeCharge, organizer, whiteLabel, subscription.id());
        if (chargesPurchasedList.isEmpty()) {
            Timestamp subscriptionExpireDate = subscription.currentTermEnd();
            if (subscription.cancelledAt() != null) {
                subscriptionExpireDate = subscription.cancelledAt();
            }
            ChargesPurchased chargesPurchased = prepareChargeToLinkWithOrganizer(chargeBeeCharge, organizer, null, subscription.activatedAt(),
                    subscriptionExpireDate, whiteLabel, 1L, true, true);
            chargesPurchasedRepoService.save(chargesPurchased);
        }
    }

    @Override
    public void updatePlanUsageOverageRateForOrganiser(Organizer organizer, Subscription subscription) {
        updatePlanUsageOverageRateForOrganiserOrWhiteLabel(null, organizer, subscription,organizer.getChargebeeCustomerId());
    }

    @Override
    public void updatePlanUsageOverageRateForWhiteLabel(WhiteLabel whiteLabel, Subscription subscription) {
        updatePlanUsageOverageRateForOrganiserOrWhiteLabel(whiteLabel, null, subscription,whiteLabel.getChargebeeCustomerId());
    }
    private void updatePlanUsageOverageRateForOrganiserOrWhiteLabel(WhiteLabel whiteLabel, Organizer organizer, Subscription subscription,String chargeBeeCustomerId) {
        logger.info("updatePlanDefaultCreditsInOrganiserOrWhiteLabel subscriptionId {} OrganiserId {} WhiteLabelId {}", subscription.id(), organizer != null ? organizer.getId() : null, whiteLabel != null ? whiteLabel.getId() : null);
        List<Subscription.ItemTier> defaultSubscriptionItemTiers = subscription.itemTiers().stream().filter(itemTier -> itemTier.price() != 0).collect(Collectors.toList());
        defaultSubscriptionItemTiers.forEach(itemTier -> {
            Optional<ChargeConfig> chargeBeeChargeOpt = chargeConfigRepoService.findByChargeId(itemTier.itemPriceId());
            if (chargeBeeChargeOpt.isPresent() && PLAN_USAGE_OVERAGE_RATE.getLableName().equalsIgnoreCase(chargeBeeChargeOpt.get().getChargeName())) {
                logger.info("updatePlanDefaultCreditsInOrganiserOrWhiteLabel subscriptionId {} OverageCharge {}", subscription.id(), itemTier.price());
                updateWhiteLabelOrOrganiserOverageRate(whiteLabel, organizer, itemTier.price());
                ChargeConfig chargeBeeCharge = chargeBeeChargeOpt.get();
                savePlanUsageOverageRateChargesPurchasedIfListIfEmpty(chargeBeeCharge, whiteLabel, organizer, subscription);
                AtomicBoolean isChargeAddedTransactionAvailable = new AtomicBoolean(false);
                List<ChargebeeTransaction> chargebeeTransactionList = chargebeeTransactionRepository.findAllBySubscriptionIdAndSource(subscription.id(), ChargebeeTransactionSource.EVENT_BILLING);
                chargebeeTransactionList.forEach(chargebeeTransaction -> {
                    if (chargebeeTransaction.getModuleActivateJson() != null && !chargebeeTransaction.getModuleActivateJson().isEmpty() && chargebeeTransaction.getModuleActivateJson().startsWith(STRING_LEFT_CURLY_BRACKET)) {
                        ChargesPurchasedDto chargesPurchasedDto = JsonMapper.stringtoObject(chargebeeTransaction.getModuleActivateJson(), ChargesPurchasedDto.class);
                        if (chargeBeeCharge.getChargeDisplayName().equalsIgnoreCase(chargesPurchasedDto.getChargeConfigName())) {
                            chargesPurchasedDto.setQuantity(1L);
                            isChargeAddedTransactionAvailable.set(true);
                            chargebeeTransaction.setAmount(Double.valueOf(itemTier.price()));
                        }
                        chargebeeTransaction.setModuleActivateJson(JsonMapper.convertToString(chargesPurchasedDto));
                    } else if (chargebeeTransaction.getModuleActivateJson() != null && !chargebeeTransaction.getModuleActivateJson().isEmpty() && chargebeeTransaction.getModuleActivateJson().startsWith(STRING_LEFT_SQUARE_BRACKET)) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        try {
                            List<ChargesPurchasedDto> chargesPurchasedDtoList = objectMapper.readValue(chargebeeTransaction.getModuleActivateJson(), objectMapper.getTypeFactory().constructCollectionType(List.class, ChargesPurchasedDto.class));
                            chargesPurchasedDtoList.forEach(chargesPurchasedDto -> {
                                if (chargeBeeCharge.getChargeDisplayName().equalsIgnoreCase(chargesPurchasedDto.getChargeConfigName())) {
                                    chargesPurchasedDto.setQuantity(1L);
                                    chargebeeTransaction.setAmount(Double.valueOf(itemTier.price()));
                                }
                                chargebeeTransaction.setModuleActivateJson(JsonMapper.convertToString(chargesPurchasedDtoList));
                            });
                        } catch (JsonProcessingException e) {
                            logger.info("handleCreditsForRenewalPlan exceptionMessage {}", e.getMessage(), e);
                        }
                    }
                });
                chargebeeTransactionRepository.saveAll(chargebeeTransactionList);
                saveChargeUpdateInChargeBeeTransaction(!isChargeAddedTransactionAvailable.get(), whiteLabel, organizer, chargeBeeCharge.getChargeDisplayName(), 1,chargeBeeCustomerId, subscription.id(), itemTier.price());
            }
        });
    }

    public boolean validateSingleEventPlan(String chargeName) {
        return PROFESSIONAL_2023.getLableName().equalsIgnoreCase(chargeName) ||
                STARTER_2023.getLableName().equalsIgnoreCase(chargeName) ||
                PROFESSIONAL.getLableName().equalsIgnoreCase(chargeName) ||
                STARTER.getLableName().equalsIgnoreCase(chargeName);
    }

    public void adjustSingleEventPlanQuantity(List<ChargesPurchased> chargesPurchasedList, Subscription.SubscriptionItem subscriptionItem, Subscription subscription) {
        Timestamp subscriptionExpireDate = subscription.currentTermEnd();
        if (subscription.cancelledAt() != null) {
            subscriptionExpireDate = subscription.cancelledAt();
        }
        Integer subscriptionItemQuantity = subscriptionItem.quantity();
        if (subscriptionItemQuantity > chargesPurchasedList.size()) {
            int newQuantity = subscriptionItemQuantity - chargesPurchasedList.size();
            ChargesPurchased chargeBeeCharge = chargesPurchasedList.get(0);
            ChargeConfig chargeConfig = chargeBeeCharge.getChargeConfig();
            for (int i = 0; i < newQuantity; i++) {
                ChargesPurchased chargesPurchased = prepareChargeToLinkWithOrganizer(chargeConfig, chargeBeeCharge.getOrganizer(), null, subscription.createdAt(), subscriptionExpireDate, chargeBeeCharge.getWhiteLabel(), 1L, true, true);
                chargesPurchasedRepoService.save(chargesPurchased);
            }
        } else if (subscriptionItemQuantity < chargesPurchasedList.size()) {
            List<ChargesPurchased> unUsedChargePurchasedList = new ArrayList<>();
            chargesPurchasedList.forEach(chargesPurchased -> {
                if (!ChargesPurchased.ChargeStatus.USED.equals(chargesPurchased.getChargeStatus())) {
                    unUsedChargePurchasedList.add(chargesPurchased);
                }
            });
            int removeChargedPurchasedQuantity = chargesPurchasedList.size() - subscriptionItemQuantity;
            if (removeChargedPurchasedQuantity > unUsedChargePurchasedList.size()) {
                unUsedChargePurchasedList.forEach(charge -> charge.setRecordStatus(RecordStatus.DELETE));
                chargesPurchasedRepoService.saveAll(unUsedChargePurchasedList);
            } else {
                List<ChargesPurchased> newUnUsedChargePurchasedList = new ArrayList<>();
                for (int i = 0; i < removeChargedPurchasedQuantity; i++) {
                    ChargesPurchased chargePurchased = unUsedChargePurchasedList.get(i);
                    chargePurchased.setRecordStatus(RecordStatus.DELETE);
                    newUnUsedChargePurchasedList.add(chargePurchased);
                }
                chargesPurchasedRepoService.saveAll(newUnUsedChargePurchasedList);
            }
        }
    }

    public Integer adjustSingleEventPlanQuantityInChargeBeeTransactionTable(List<ChargesPurchased> chargesPurchasedList, int subscriptionItemQuantity) {
        List<ChargesPurchased> usedChargePurchasedList = new ArrayList<>();
        chargesPurchasedList.forEach(chargesPurchased -> {
            if (ChargesPurchased.ChargeStatus.USED.equals(chargesPurchased.getChargeStatus())) {
                usedChargePurchasedList.add(chargesPurchased);
            }
        });
        return Math.max(usedChargePurchasedList.size(), subscriptionItemQuantity);
    }

    public void updateWhiteLabelOrOrganiserPlanFreeQuantity(WhiteLabel whiteLabel, Organizer organizer, String chargeName, Integer quantity) {
        if (whiteLabel != null && (whiteLabel.getFreeQuantity() != null ? whiteLabel.getFreeQuantity().longValue() : 0) != quantity && PLAN_USAGE_INCLUDED_CREDITS.getLableName().equalsIgnoreCase(chargeName)) {
            whiteLabel.setFreeQuantity(quantity);
            whiteLabelRepoService.save(whiteLabel);
        } else if (organizer != null && (organizer.getFreeQuantity() != null ? organizer.getFreeQuantity().longValue() : 0) != quantity && PLAN_USAGE_INCLUDED_CREDITS.getLableName().equalsIgnoreCase(chargeName)) {
            organizer.setFreeQuantity(quantity);
            organizerRepository.save(organizer);
        }
    }

    public void updateWhiteLabelOrOrganiserOverageRate(WhiteLabel whiteLabel, Organizer organizer, Long price) {
        if (whiteLabel != null) {
            whiteLabel.setOverageCharge(GeneralUtils.convertCentToDollar(price));
            whiteLabelRepoService.save(whiteLabel);
        } else if (organizer != null) {
            organizer.setOverageCharge(GeneralUtils.convertCentToDollar(price));
            organizerRepository.save(organizer);
        }
    }

    public void savePlanUsageIncludedCreditsChargePurchasedIfListIsEmpty(List<ChargesPurchased> chargesPurchasedList, ChargeConfig chargeBeeCharge, WhiteLabel whiteLabel, Organizer organizer, Subscription subscription, Subscription.SubscriptionItem subscriptionItem) {
        Timestamp subscriptionExpireDate = subscription.currentTermEnd();
        if (subscription.cancelledAt() != null) {
            subscriptionExpireDate = subscription.cancelledAt();
        }
        if (chargesPurchasedList.isEmpty()) {
            if (validateSingleEventPlan(chargeBeeCharge.getChargeDisplayName())) {
                int quantity = subscriptionItem.quantity();
                for (int i = 0; i < quantity; i++) {
                    ChargesPurchased chargesPurchased = prepareChargeToLinkWithOrganizer(chargeBeeCharge, organizer, null, subscription.activatedAt(),
                            subscriptionExpireDate, whiteLabel, 1L, true, true);
                    chargesPurchasedRepoService.save(chargesPurchased);
                }
            } else {
                ChargesPurchased chargesPurchased = prepareChargeToLinkWithOrganizer(chargeBeeCharge, organizer, null, subscription.activatedAt(),
                        subscriptionExpireDate, whiteLabel, subscriptionItem.quantity().longValue(), true, true);
                chargesPurchasedRepoService.save(chargesPurchased);
            }
        } else {
            if (validateSingleEventPlan(chargeBeeCharge.getChargeDisplayName())) {
                adjustSingleEventPlanQuantity(chargesPurchasedList, subscriptionItem, subscription);
            } else {
                ChargesPurchased defaultCreditCharge = chargesPurchasedList.get(0);
                defaultCreditCharge.setQuantity(subscriptionItem.quantity().longValue());
                chargesPurchasedRepoService.save(defaultCreditCharge);
            }
        }
    }

    @Override
    public void updatePlanUsageIncludedCreditsForOrganiser(Organizer organizer, Subscription subscription) {
        updatePlanUsageIncludedCreditsForOrganiserOrWhiteLabel(null, organizer, subscription,organizer.getChargebeeCustomerId());
        cacheService.evictSingleCacheValue("organizerDtoByOrganizerPageURL",organizer.getOrganizerPageURL());
    }

    @Override
    public void updatePlanUsageIncludedCreditsForWhiteLabel(WhiteLabel whiteLabel, Subscription subscription) {
        updatePlanUsageIncludedCreditsForOrganiserOrWhiteLabel(whiteLabel, null, subscription,whiteLabel.getChargebeeCustomerId());
    }
    private void updatePlanUsageIncludedCreditsForOrganiserOrWhiteLabel(WhiteLabel whiteLabel, Organizer organizer, Subscription subscription,String chargeBeeCustomerId) {
        List<Subscription.SubscriptionItem> subscriptionItems = subscription.subscriptionItems().stream().filter(subscriptionItem -> SUBSCRIPTION_ITEM_TYPE_CHARGE.equals(String.valueOf(subscriptionItem.itemType())) || SUBSCRIPTION_ITEM_TYPE_ADDON.equals(String.valueOf(subscriptionItem.itemType()))).collect(Collectors.toList());
        subscriptionItems.forEach(subscriptionItem -> {
            Optional<ChargeConfig> chargeBeeChargeOpt = chargeConfigRepoService.findByChargeId(subscriptionItem.itemPriceId());
            if (chargeBeeChargeOpt.isPresent() && (PLAN_USAGE_INCLUDED_CREDITS.getLableName().equalsIgnoreCase(chargeBeeChargeOpt.get().getChargeName())
                    || PRE_PURCHASED_REGISTRATION_USAGE_CREDITS.getLableName().equalsIgnoreCase(chargeBeeChargeOpt.get().getChargeName())
                    || EXHIBITOR_PRO_BOOTH.getLableName().equalsIgnoreCase(chargeBeeChargeOpt.get().getChargeDisplayName())
                    || validateSingleEventPlan(chargeBeeChargeOpt.get().getChargeDisplayName()))) {
                updateWhiteLabelOrOrganiserPlanFreeQuantity(whiteLabel, organizer, chargeBeeChargeOpt.get().getChargeName(), subscriptionItem.quantity());
                ChargeConfig chargeBeeCharge = chargeBeeChargeOpt.get();
                List<ChargesPurchased> chargesPurchasedList = chargesPurchasedRepoService.findPurchasedChargesByChargeIdAndOrganiserOrWhiteLabelAndSubscriptionId(chargeBeeCharge, organizer, whiteLabel, subscription.id());
                savePlanUsageIncludedCreditsChargePurchasedIfListIsEmpty(chargesPurchasedList, chargeBeeCharge, whiteLabel, organizer, subscription, subscriptionItem);
                List<ChargebeeTransaction> chargebeeTransactionList = chargebeeTransactionRepository.findAllBySubscriptionIdAndSource(subscription.id(), ChargebeeTransactionSource.EVENT_BILLING);
                boolean isChargeAddedTransactionAvailable = updateChargeBeeTransactionRecords(chargebeeTransactionList, chargesPurchasedList, chargeBeeCharge, subscriptionItem.quantity());
                chargebeeTransactionRepository.saveAll(chargebeeTransactionList);
                saveChargeUpdateInChargeBeeTransaction(!isChargeAddedTransactionAvailable, whiteLabel, organizer, chargeBeeCharge.getChargeDisplayName(), subscriptionItem.quantity(),chargeBeeCustomerId, subscription.id(), subscriptionItem.amount());
            }
        });
    }

    public boolean updateChargeBeeTransactionRecords(List<ChargebeeTransaction> chargebeeTransactionList, List<ChargesPurchased> chargesPurchasedList, ChargeConfig chargeBeeCharge, Integer subscriptionQuantity) {
        AtomicBoolean isChargeAddedTransactionAvailable = new AtomicBoolean(false);
        chargebeeTransactionList.forEach(chargebeeTransaction -> {
            if (chargebeeTransaction.getModuleActivateJson() != null && !chargebeeTransaction.getModuleActivateJson().isEmpty() && chargebeeTransaction.getModuleActivateJson().startsWith(STRING_LEFT_CURLY_BRACKET)) {
                ChargesPurchasedDto chargesPurchasedDto = JsonMapper.stringtoObject(chargebeeTransaction.getModuleActivateJson(), ChargesPurchasedDto.class);
                if (chargesPurchasedDto != null && chargeBeeCharge.getChargeDisplayName().equalsIgnoreCase(chargesPurchasedDto.getChargeConfigName())) {
                    if (validateSingleEventPlan(chargeBeeCharge.getChargeDisplayName())) {
                        Integer quantity = adjustSingleEventPlanQuantityInChargeBeeTransactionTable(chargesPurchasedList, subscriptionQuantity);
                        chargesPurchasedDto.setQuantity(quantity.longValue());
                    } else {
                        chargesPurchasedDto.setQuantity(subscriptionQuantity.longValue());
                    }
                    isChargeAddedTransactionAvailable.set(true);
                }
                chargebeeTransaction.setModuleActivateJson(JsonMapper.convertToString(chargesPurchasedDto));
            } else if (chargebeeTransaction.getModuleActivateJson() != null && !chargebeeTransaction.getModuleActivateJson().isEmpty() && chargebeeTransaction.getModuleActivateJson().startsWith(STRING_LEFT_SQUARE_BRACKET)) {
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    List<ChargesPurchasedDto> chargesPurchasedDtoList = objectMapper.readValue(chargebeeTransaction.getModuleActivateJson(), objectMapper.getTypeFactory().constructCollectionType(List.class, ChargesPurchasedDto.class));
                    chargesPurchasedDtoList.forEach(chargesPurchasedDto -> {
                        if (chargeBeeCharge.getChargeDisplayName().equalsIgnoreCase(chargesPurchasedDto.getChargeConfigName())) {
                            if (validateSingleEventPlan(chargeBeeCharge.getChargeDisplayName())) {
                                Integer quantity = adjustSingleEventPlanQuantityInChargeBeeTransactionTable(chargesPurchasedList, subscriptionQuantity);
                                chargesPurchasedDto.setQuantity(quantity.longValue());
                            } else {
                                chargesPurchasedDto.setQuantity(subscriptionQuantity.longValue());
                            }
                        }
                        chargebeeTransaction.setModuleActivateJson(JsonMapper.convertToString(chargesPurchasedDtoList));
                    });
                } catch (JsonProcessingException e) {
                    logger.info("handleCreditsForRenewalPlan exceptionMessage {}", e.getMessage(), e);
                }
            }
        });
        return isChargeAddedTransactionAvailable.get();
    }
    public void saveChargeUpdateInChargeBeeTransaction(boolean isFirstTime, WhiteLabel whiteLabel, Organizer organizer, String chargeName, Integer quantity,String chargeBeeCustomerId, String subscriptionId, Long amount) {
        if (isFirstTime) {
            User createdBy = User.dummyUser(CHARGEBEE, USER);
            ChargesPurchasedDto chargesPurchasedDto = new ChargesPurchasedDto(chargeName, Long.valueOf(quantity));
            chargebeeBillingService.saveChargeUpdateInChargebeeTransaction(JsonMapper.convertToString(chargesPurchasedDto),
                    whiteLabel != null ? whiteLabel.getPlanConfig() : organizer.getPlanConfig(), CHARGEBEE.equals(createdBy.getFirstName()) ? null
                            : createdBy, organizer, whiteLabel, chargeBeeCustomerId,
                    subscriptionId, Double.valueOf(amount), null, String.valueOf(PAYMENT_DUE));
        }
    }

    @Override
    public void calculateSubscriptionFreeAndPaidCreditsForOrganiser(Organizer organizer, User user,boolean isSubscriptionRenew) {
        calculateSubscriptionFreeAndPaidCredits(null, organizer, user,isSubscriptionRenew);
    }

    @Override
    public void calculateSubscriptionFreeAndPaidCreditsForWhiteLabel(WhiteLabel whiteLabel, User user,boolean isSubscriptionRenew) {
        calculateSubscriptionFreeAndPaidCredits(whiteLabel, null, user,isSubscriptionRenew);
    }

    private void calculateSubscriptionFreeAndPaidCredits(WhiteLabel whiteLabel, Organizer organizer, User user,boolean isSubscriptionRenew) {
        Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional = Optional.empty();
        Long prePurchasedQuantity = 0L;
        Integer subscriptionFreeCredits = 0;
        String subscriptionId = null;
        PlanConfig planConfig;
        if (whiteLabel != null) {
            subscriptionId = whiteLabel.getSubscriptionId();
            planConfig = whiteLabel.getPlanConfig();
            subscriptionFreeCredits = whiteLabel.getFreeQuantity() != null ? whiteLabel.getFreeQuantity() : planConfig.getFreeQuantity();
            chargebeeEventCreditsOptional = chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(null, whiteLabel.getId(), subscriptionId);
        } else if (organizer != null) {
            subscriptionId = organizer.getSubscriptionId();
            planConfig=organizer.getPlanConfig();
            subscriptionFreeCredits = organizer.getFreeQuantity()!=null?organizer.getFreeQuantity():planConfig.getFreeQuantity();
            chargebeeEventCreditsOptional = chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
            prePurchasedQuantity = chargesPurchasedRepoService.getPrePurchasedRegistrationQuantity(organizer.getId(), null, subscriptionId);
        }
        ChargebeeEventCredits chargebeeEventCredits;
        if (chargebeeEventCreditsOptional.isPresent()) {
            chargebeeEventCredits = chargebeeEventCreditsOptional.get();
            List<ChargebeeEventUsages> chargebeeEventUsagesList;
            if (whiteLabel != null) {
                chargebeeEventCredits.setPlanConfigId(whiteLabel.getPlanConfig().getId());
                chargebeeEventUsagesList = chargebeeEventUsagesRepoService.findByWhiteLabelIdAndUsageTypeAndSubscriptionId(whiteLabel.getId(), WEB,subscriptionId);
            } else {
                chargebeeEventCredits.setPlanConfigId(organizer.getPlanConfig().getId());
                chargebeeEventUsagesList = chargebeeEventUsagesRepoService.findByOrganiserIdAndUsageTypeAndSubscriptionId(organizer.getId(), WEB,subscriptionId);
            }
            chargebeeEventCredits.setSubscriptionId(subscriptionId);
            setRemainingFreeCredit(chargebeeEventCredits,chargebeeEventUsagesList, subscriptionFreeCredits, prePurchasedQuantity,isSubscriptionRenew);

            chargebeeEventCredits.setUpdateByAdmin(true);
            chargebeeEventCreditsRepoService.save(chargebeeEventCredits, user);
        } else {
            chargebeeEventCredits = new ChargebeeEventCredits();
            if (whiteLabel != null) {
                chargebeeEventCredits.setWhiteLabelId(whiteLabel.getId());
                chargebeeEventCredits.setPlanConfig(whiteLabel.getPlanConfig());
                chargebeeEventCredits.setPlanConfigId(whiteLabel.getPlanConfig().getId());
                chargebeeEventCredits.setSubscriptionId(whiteLabel.getSubscriptionId());
            } else if (organizer != null) {
                chargebeeEventCredits.setOrganizerId(organizer.getId());
                chargebeeEventCredits.setSubscriptionId(organizer.getSubscriptionId());
                chargebeeEventCredits.setPlanConfigId(organizer.getPlanConfig().getId());
                chargebeeEventCredits.setPlanConfig(organizer.getPlanConfig());
            }
            chargebeeEventCredits.setRemainingFreeCredits(subscriptionFreeCredits + prePurchasedQuantity);
            chargebeeEventCreditsRepoService.save(chargebeeEventCredits, user);
            chargeBeeHelperService.createChargebeeLogRecord(subscriptionId, CreditType.PLAN_DEFAULT.name(), subscriptionFreeCredits, whiteLabel, organizer, true);
        }
    }
}
