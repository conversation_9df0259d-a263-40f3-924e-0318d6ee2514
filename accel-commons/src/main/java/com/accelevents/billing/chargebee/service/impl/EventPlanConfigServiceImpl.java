package com.accelevents.billing.chargebee.service.impl;

import com.accelevents.billing.chargebee.dto.EventChargebeePlanConfigDto;
import com.accelevents.billing.chargebee.enums.ChargeConfigNames;
import com.accelevents.billing.chargebee.repo.ChargebeePlanRepoService;
import com.accelevents.billing.chargebee.repo.ChargesPurchasedRepoService;
import com.accelevents.billing.chargebee.repo.EventChargeUsagesRepoService;
import com.accelevents.billing.chargebee.repo.EventPlanConfigRepoService;
import com.accelevents.billing.chargebee.repositories.ChargebeeEventCreditsService;
import com.accelevents.billing.chargebee.repositories.EventPlanConfigRepository;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.enums.BillingType;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exhibitors.services.ExhibitorSettingsService;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.ticketing.dto.EventTicketingDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.PROFESSIONAL_2023;
import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.STARTER_2023;
import static com.accelevents.domain.enums.EventFormat.IN_PERSON;
import static com.accelevents.enums.PlanConfigNames.*;

@Service
public class EventPlanConfigServiceImpl implements EventPlanConfigService {

    private static final Logger log = LoggerFactory.getLogger(EventPlanConfigServiceImpl.class);

    @Autowired
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Autowired
    private ChargebeePlanService chargebeePlanService;
    @Autowired
    private WhiteLabelService whiteLabelService;
    @Autowired
    private ROWhiteLabelService roWhiteLabelService;
    @Autowired
    private OrganizerService organizerService;
    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private EventService eventService;
    @Autowired
    private ROEventService roEventService;
    @Autowired
    private IntercomDetailsService intercomDetailsService;
    @Autowired
    private ROUserService roUserService;
    @Autowired
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Autowired
    private EventChargeUsagesRepoService eventChargeUsagesRepoService;
    @Autowired
    private ChargebeePlanRepoService chargebeePlanRepoService;
    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Autowired
    private TicketingRepository ticketingRepository;
    @Autowired
    private ExhibitorSettingsService exhibitorSettingsService;
    @Autowired
    private VirtualEventService virtualEventService;
    @Autowired
    private ChargebeeEventCreditsService chargebeeEventCreditsService;
    @Autowired
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Autowired
    private EventPlanConfigRepository eventPlanConfigRepository;
    @Autowired
    private EventCommonRepoService eventCommonRepoService;

    @Override
    public Optional<EventPlanConfig> findById(Long id){
        return eventPlanConfigRepoService.findById(id);
    }

    @Override
    public EventPlanConfig findByEventId(Long eventId) {
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(eventId);
        log.info("findByEventId eventId {} eventPlanConfig {}", eventId, eventPlanConfig != null ? eventPlanConfig.getId() : "not found");
        if (eventPlanConfig == null) {
            Event event = eventRepoService.findEventByIdOrThrowError(eventId);
            eventPlanConfig = new EventPlanConfig();
            mapChargeBeePlanWithEventPlan(event, eventPlanConfig);
            eventPlanConfig.setEvent(event);
            eventPlanConfig.setEventId(eventId);
            log.info("New Event Plan Config of eventId {} eventPlanConfig {} whitelabelId {} planConfigId {}",
                    eventId, eventPlanConfig.getId(),
                    eventPlanConfig.getWhiteLabel() != null ? eventPlanConfig.getWhiteLabel().getId() : null,
                    eventPlanConfig.getPlanConfigId());
            eventPlanConfig = save(eventPlanConfig);
        }
        return eventPlanConfig;
    }


    @Override
    public Optional<EventPlanConfig> findByChargebeePlanId(String chargebeePlanId){
        return eventPlanConfigRepoService.findByChargebeePlanId(chargebeePlanId);
    }

    @Transactional
    public EventChargebeePlanConfigDto getPlanConfiguration(Long eventId){
        log.info("EventPlanConfigServiceImpl getPlanConfiguration for event Id {}", eventId);
        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(eventId);

        if(eventPlanConfig == null){
            log.info("EventPlanConfigServiceImpl getPlanConfiguration return null for event Id {}", eventId);
            eventPlanConfig = eventPlanConfigService.findByEventId(eventId);
        }

        log.info("EventPlanConfigServiceImpl getPlanConfiguration for event Id {} eventPlanConfig Id {}",
                eventId, eventPlanConfig != null ? eventPlanConfig.getId() : null);

        return isAllowToImportAttendeesOnFreePlan(new EventChargebeePlanConfigDto(eventPlanConfig),eventPlanConfig);
    }

    public EventChargebeePlanConfigDto isAllowToImportAttendeesOnFreePlan(EventChargebeePlanConfigDto eventChargebeePlanConfigDto, EventPlanConfig eventPlanConfig) {

        if (eventChargebeePlanConfigDto.getChargebeePlanName().equals(FREE_PLAN.getName())) {
            Long attendeeImportLimit = eventPlanConfig.getAttendeeImportLimit();
            if (attendeeImportLimit == 0) {
                eventChargebeePlanConfigDto.setAttendeeImportLimit(0L);
                eventChargebeePlanConfigDto.setAllowToImportAttendees(true);
                return eventChargebeePlanConfigDto;
            }
            Ticketing ticketing = ticketingRepository.findByEventId(eventPlanConfig.getEventId());
            long eventSoldTickets = eventCommonRepoService.findSoldCountByEventId(eventPlanConfig.getEventId()).longValue();
            Double eventCapacity = ticketing.getEventCapacity() != null ? ticketing.getEventCapacity() : 100;
            if (eventSoldTickets >= eventCapacity) {
                eventChargebeePlanConfigDto.setAttendeeImportLimit(0L);
                eventChargebeePlanConfigDto.setAllowToImportAttendees(false);
                return eventChargebeePlanConfigDto;
            }
            Long eventSoldTicketsByCSV = eventCommonRepoService.findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(eventPlanConfig.getEvent().getEventId());
            Long leftAttendeeImportLimit = attendeeImportLimit - eventSoldTicketsByCSV;
            leftAttendeeImportLimit = leftAttendeeImportLimit > 0 ? leftAttendeeImportLimit : 0;
            Long leftEventCapacity = (long) (eventCapacity - eventSoldTickets);
            if (leftEventCapacity >= leftAttendeeImportLimit) {
                eventChargebeePlanConfigDto.setAttendeeImportLimit(leftAttendeeImportLimit);
            } else {
                eventChargebeePlanConfigDto.setAttendeeImportLimit(leftEventCapacity);
            }
            eventChargebeePlanConfigDto.setAllowToImportAttendees(eventChargebeePlanConfigDto.getAttendeeImportLimit()>0);
        }
        return eventChargebeePlanConfigDto;
    }
    @Override
    public Long getEventPlanCredits(Long eventId){
        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(eventId);
        Event event=roEventService.findEventByEventId(eventId);
        ChargebeeEventCredits chargebeeEventCredits = null;
        EventChargebeePlanConfigDto eventChargebeePlanConfigDto=new EventChargebeePlanConfigDto(eventPlanConfig);
        if (STARTER.getName().equals(eventChargebeePlanConfigDto.getChargebeePlanName()) || PROFESSIONAL.getName().equals(eventChargebeePlanConfigDto.getChargebeePlanName())
                || SINGLE_EVENT_UNIT.getName().equals(eventChargebeePlanConfigDto.getChargebeePlanName())) {
            chargebeeEventCredits = chargebeeEventCreditsService.findByEventId(eventId);
        } else if (event.getWhiteLabelId() != null) {
            chargebeeEventCredits = chargebeeEventCreditsService.findByWhiteLabelId(event.getWhiteLabelId());
        } else if(!FREE_PLAN.getName().equals(eventChargebeePlanConfigDto.getChargebeePlanName())){
            chargebeeEventCredits = chargebeeEventCreditsService.findByOrganizerId(event.getOrganizerId());
        }

        if(chargebeeEventCredits!=null && chargebeeEventCredits.getUpdateByAdmin() != null && chargebeeEventCredits.getUpdateByAdmin()) {
            return chargebeeEventCredits.getRemainingFreeCredits();
        }else{
            return eventChargebeePlanConfigDto.getRegistrationConfigDto().getAttendeesIncluded();
        }
    }


    @Override
    public EventPlanConfig save(EventPlanConfig eventPlanConfig) {
        if(eventPlanConfig!=null){
            eventPlanConfig = eventPlanConfigRepoService.save(eventPlanConfig);
        }
        return eventPlanConfig;
    }

    @Override
    public void handleCreateOrUpdatePlanForEvent(Event event) {
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventIdObserbException(event.getEventId());
        if (eventPlanConfig != null) {
            log.info("handleCreateOrUpdatePlanForEvent map plan with event {} date {}", eventPlanConfig.getId(), new Date());
            mapChargeBeePlanWithEventPlan(event, eventPlanConfig);
        } else {
            eventPlanConfig = createNewEventPlanConfig(event);
            log.info("handleCreateOrUpdatePlanForEvent create planConfig and map with event {} date {}", event.getEventId(), new Date());
        }
        try {
            save(eventPlanConfig);
        } catch (Exception exception) {
            log.error("handleCreateOrUpdatePlanForEvent eventId {} eventPlanConfig {} error {} date {}", event.getEventId(), eventPlanConfig, exception.getMessage(), new Date());
        }
    }

    @Override
    public EventPlanConfig createNewEventPlanConfig(Event event) {
        log.info("Creating new object for event id: {}", event.getEventId());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setEventId(event.getEventId());
        return mapChargeBeePlanWithEventPlan(event, eventPlanConfig);
    }


    @Override
    public void mapChageebe(Event event, Long planId){
        chargebeePlanService.findById(planId).ifPresent(e->{
             EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
             if(eventPlanConfig == null){
                 eventPlanConfig = new EventPlanConfig();
             }
            copyPlanConfigFromOrganizerOrWhiteLabelToEvent(eventPlanConfig, e, event);
            eventPlanConfigService.save(eventPlanConfig);
        });
    }

    private EventPlanConfig mapChargeBeePlanWithEventPlan(Event event, EventPlanConfig eventPlanConfig) {
        PlanConfig basePlanConfig = getPlanConfigForWhiteLabelOrOrganizerAndIfNotFoundReturnFreePlan(event);
        log.info("mapChargeBeePlanWithEventPlan {} basePlanConfig {}", event.getEventId(), basePlanConfig);
        return copyConfigAndSendNotification(event, eventPlanConfig, basePlanConfig);
    }

    private EventPlanConfig copyConfigAndSendNotification(Event event, EventPlanConfig eventPlanConfig, PlanConfig basePlanConfig) {
        log.info("copyConfigAndSendNotification eventId {} eventPlanConfig {} basePlanConfig {}", event.getEventId(), eventPlanConfig, basePlanConfig);
        copyPlanConfigFromOrganizerOrWhiteLabelToEvent(eventPlanConfig, basePlanConfig, event);

        sendNotificationToIntercomAndUpdateTransactionalConditionalLogic(event, eventPlanConfig);
        return eventPlanConfig;
    }

    private void sendNotificationToIntercomAndUpdateTransactionalConditionalLogic(Event event, EventPlanConfig eventPlanConfig) {
        intercomDetailsService.sendCompanyPlanToIntercom(event,eventPlanConfig);
        String eventPlanName = eventPlanConfig.getPlanConfig().getPlanName();
        if(event.getOrganizer() != null) {
            Optional<User> organizerUser = roUserService.getUserByEmail(event.getOrganizer().getContactEmailAddress());
            log.info("get user by organizer email address{}",organizerUser);
            organizerUser.ifPresent(user -> intercomDetailsService.sendPlanToUserIntercom(user, eventPlanName));
        }
        if (PlanConfigNames.FREE_PLAN.getName().equals(eventPlanName)) {
            exhibitorSettingsService.resetExpoLiveStream(event);
            virtualEventService.resetLobbyLiveStream(event);
        }
        transactionFeeConditionalLogicService.handleAeFeeUpdate(event);
    }

    private PlanConfig getPlanConfigForWhiteLabelOrOrganizerAndIfNotFoundReturnFreePlan(Event event) {
        if (event.getWhiteLabel() != null) {
            log.info("getPlanConfigForWhiteLabelOrOrganizerAndIfNotFoundReturnFreePlan eventId {} whiteLabelId {}",event.getEventId(),event.getWhiteLabelId());
            PlanConfig planConfig =  roWhiteLabelService.getWhiteLabelById(event.getWhiteLabel().getId()).get().getPlanConfig(); //NOSONAR
            planConfig = getFreePlanForLegacyEventAfter2021(event, planConfig);
            return planConfig;
        } else if (event.getOrganizer() != null) {
            Organizer organiser = organizerService.findByIdOrNotFound(event.getOrganizerId());
            log.info("getPlanConfigForWhiteLabelOrOrganizerAndIfNotFoundReturnFreePlan eventId {} organiserId {}",event.getEventId(),organiser.getId());
            String planName = organiser.getPlanConfig().getPlanName();
            log.info("getPlanConfigForWhiteLabelOrOrganizerAndIfNotFoundReturnFreePlan eventId {} organiserId {} planName {} ",event.getEventId(),organiser.getId(),planName);
            if (checkSingleEventPlan(event.getOrganizer())) {
                Long count = chargesPurchasedRepoService.findChargePurchasedCountByOrganiser(event.getOrganizer());
                if (count == 0) {
                    return chargebeePlanService.getFreePlanConfig();
                }
            }
            if (IN_PERSON.equals(event.getEventFormat())) {
                    EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(event.getEventId());
                    if (SINGLE_EVENT_PLAN_STARTER.getName().equals(planName) || SINGLE_EVENT_PLAN_PRO.getName().equals(planName)) {
                        if (null != eventPlanConfig && eventPlanConfig.isChargeAssociated()) {
                            return eventPlanConfig.getPlanConfig();
                        }
                        releaseEventChargeUsages(event);
                        return chargebeePlanService.getFreePlanConfig();
                    }
            }
            PlanConfig config = getPlanConfigIfEventChargeUsagesExistElseUpdateOrganizer(event);
            if (config != null) {
                return config;
            }
            PlanConfig orgPlanConfig = organizerService.getById(event.getOrganizer().getId()).get().getPlanConfig();//NOSONAR
            if (!(PlanConfigNames.SINGLE_EVENT_UNIT.getName().equals(orgPlanConfig.getPlanName()) || SINGLE_EVENT_PLAN_PRO.getName().equalsIgnoreCase(orgPlanConfig.getPlanName()) ||
                    SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(orgPlanConfig.getPlanName()))) {
                orgPlanConfig = getFreePlanForLegacyEventAfter2021(event, orgPlanConfig);
                releaseEventChargeUsages(event);
                return orgPlanConfig;
            } else {
                return getPlanConfigOfOrganizerIfAnyChargePurchased(event.getOrganizerId(), event.getEventId());
            }
        }
        return chargebeePlanService.getFreePlanConfig();
    }

    public boolean checkSingleEventPlan(Organizer organizer) {
        String planName = organizer.getPlanConfig().getPlanName();
        return STARTER_2023.getLableName().equals(planName) || PROFESSIONAL_2023.getLableName().equals(planName);
    }
    private PlanConfig getFreePlanForLegacyEventAfter2021(Event event, PlanConfig planConfig) {
        if(LEGACY.getName().equals(planConfig.getPlanName()) || WHITE_LABEL_LEGACY.getName().equals(planConfig.getPlanName())){
            Optional<Ticketing> ticketing = ticketingRepository.findById(event.getTicketingId());
            if(ticketing.isPresent()){
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(ticketing.get().getEventEndDate());
                log.info("getFreePlanForLegacyEventAfter2021 eventEndDate_{}", ticketing.get().getEventStartDate());
                if(calendar.get(Calendar.YEAR) > 2021){
                    planConfig = chargebeePlanService.getFreePlanConfig();
                }
            }
        }
        return planConfig;
    }

    private PlanConfig getPlanConfigOfOrganizerIfAnyChargePurchased(Long organizerId, Long eventId) {
        PlanConfig config = null;
        List<ChargesPurchased> chargesPurchasedList = chargesPurchasedRepoService.findPurchasedChargesByOrganizerIdAndNotUsed(organizerId);
        log.info("Charges Purchased of org{}",chargesPurchasedList);
        if (!chargesPurchasedList.isEmpty()) {

            Optional<ChargesPurchased> professionalChargesPurchasedOptional = chargesPurchasedList.stream()
                    .filter(e -> ChargeConfigNames.PROFESSIONAL.getLableName().equals(e.getChargeConfig().getChargeDisplayName())).findAny();
               log.info("professional Charges Purchased of org{}",professionalChargesPurchasedOptional);

            Optional<ChargesPurchased> starterChargesPurchasedOptional = chargesPurchasedList.stream()
                    .filter(e -> ChargeConfigNames.STARTER.getLableName().equals(e.getChargeConfig().getChargeDisplayName())).findAny();
               log.info("starter Charges Purchased of org{}",starterChargesPurchasedOptional);
            Optional<ChargesPurchased> newStarterChargesPurchasedOptional = chargesPurchasedList.stream()
                    .filter(e -> ChargeConfigNames.STARTER_2023.getLableName().equals(e.getChargeConfig().getChargeDisplayName())).findAny();
            log.info("starter 2023 Charges Purchased of org{}",newStarterChargesPurchasedOptional);
            Optional<ChargesPurchased> newProChargesPurchasedOptional = chargesPurchasedList.stream()
                    .filter(e -> ChargeConfigNames.PROFESSIONAL_2023.getLableName().equals(e.getChargeConfig().getChargeDisplayName())).findAny();
            log.info("professional 2023 Charges Purchased of org{}",newProChargesPurchasedOptional);

            if (professionalChargesPurchasedOptional.isPresent()) {
                config = chargebeePlanRepoService.findByChargebeePlanName(PlanConfigNames.PROFESSIONAL.getName()).get();//NOSONAR
                eventChargeUsagesRepoService.save(prepareChargeUseRecord(organizerId, eventId,
                        professionalChargesPurchasedOptional.get().getChargeConfig()));
            } else if (starterChargesPurchasedOptional.isPresent()) {
                config = chargebeePlanRepoService.findByChargebeePlanName(PlanConfigNames.STARTER.getName()).get();//NOSONAR
                eventChargeUsagesRepoService.save(prepareChargeUseRecord(organizerId, eventId,
                        starterChargesPurchasedOptional.get().getChargeConfig()));
            } else if (newStarterChargesPurchasedOptional.isPresent()) {
                config = chargebeePlanRepoService.findByChargebeePlanName(SINGLE_EVENT_PLAN_STARTER.getName()).get();//NOSONAR
                eventChargeUsagesRepoService.save(prepareChargeUseRecord(organizerId, eventId,
                        newStarterChargesPurchasedOptional.get().getChargeConfig()));
            } else if (newProChargesPurchasedOptional.isPresent()) {
                config = chargebeePlanRepoService.findByChargebeePlanName(SINGLE_EVENT_PLAN_PRO.getName()).get();//NOSONAR
                eventChargeUsagesRepoService.save(prepareChargeUseRecord(organizerId, eventId,
                        newProChargesPurchasedOptional.get().getChargeConfig()));
            }
        }
        log.info("Plan Config{}",config);
        if(config != null) {
            return config;
        }else {
            return chargebeePlanService.getFreePlanConfig();
        }
    }
    private EventChargeUsages prepareChargeUseRecord(Long organizerId, Long eventId, ChargeConfig chargeConfig) {
        EventChargeUsages eventChargeUsages = new EventChargeUsages();
        eventChargeUsages.setEventId(eventId);
        eventChargeUsages.setOrganizerId(organizerId);
        eventChargeUsages.setQty(1);
        eventChargeUsages.setChargeConfig(chargeConfig);
        eventChargeUsages.setRecordStatus(RecordStatus.CREATE);
        log.info("Event Charge Usages{}",eventChargeUsages);
        return eventChargeUsages;
    }

    private PlanConfig getPlanConfigIfEventChargeUsagesExistElseUpdateOrganizer(Event event) {
        PlanConfig config = null;
        Optional<EventChargeUsages> eventChargeUsagesOptional =
                eventChargeUsagesRepoService.findByOrganizerIdAndEventId(event.getOrganizer().getId(), event.getEventId());
        log.info("Event Charge Usages{}",eventChargeUsagesOptional);
        if (eventChargeUsagesOptional.isPresent()) {
            EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(event.getEventId());
            log.info("Event Plan Config of event{}",eventPlanConfig);
            if (eventPlanConfig != null) {
                config = eventPlanConfig.getPlanConfig();
            }
        } else {
            releaseEventChargeUsages(event);
        }
        return config;
    }

    private void releaseEventChargeUsages(Event event) {
        Optional<EventChargeUsages> chargeUsagesOptional = eventChargeUsagesRepoService.findByEventId(event.getEventId());
        log.info("release Event Charge Usages{}",chargeUsagesOptional);
        if (chargeUsagesOptional.isPresent()) {
            EventChargeUsages eventChargeUsages = chargeUsagesOptional.get();
            eventChargeUsages.setRecordStatus(RecordStatus.DELETE);
            eventChargeUsagesRepoService.save(eventChargeUsages);
        }
    }

    private PlanConfig getPlanConfigForSingleEventUnitPlan(ChargeConfig chargeConfig) {
        if (ChargeConfigNames.STARTER.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            return chargebeePlanRepoService.findByChargebeePlanName(PlanConfigNames.STARTER.getName()).get();//NOSONAR
        } else if (ChargeConfigNames.PROFESSIONAL.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            return chargebeePlanRepoService.findByChargebeePlanName(PlanConfigNames.PROFESSIONAL.getName()).get();//NOSONAR
        } else if (ChargeConfigNames.STARTER_2023.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            return chargebeePlanRepoService.findByChargebeePlanName(SINGLE_EVENT_PLAN_STARTER.getName()).get();//NOSONAR
        } else if (ChargeConfigNames.PROFESSIONAL_2023.getLableName().equals(chargeConfig.getChargeDisplayName())) {
            return chargebeePlanRepoService.findByChargebeePlanName(SINGLE_EVENT_PLAN_PRO.getName()).get();//NOSONAR
        }
        else {
            return null;
        }
    }

    private void copyPlanConfigFromOrganizerOrWhiteLabelToEvent(EventPlanConfig eventPlanConfig, PlanConfig planConfig, Event event) {
        if(eventPlanConfig.getPlanConfig()==null || (eventPlanConfig.getPlanConfig().getPlanName().equals(FREE_PLAN.getName()) || !eventPlanConfig.getPlanConfig().getPlanName().equals(planConfig.getPlanName()))) {
            eventPlanConfig.setChargebeePlanId(planConfig.getChargebeePlanId());
            eventPlanConfig.setCapUsageConfigJson(planConfig.getCapUsageConfigJson());
            eventPlanConfig.setCustomBrandingConfigJson(planConfig.getCustomBrandingConfigJson());
            eventPlanConfig.setIntegrationsConfigJson(planConfig.getIntegrationsConfigJson());
            eventPlanConfig.setRegistrationConfigJson(planConfig.getRegistrationConfigJson());
            eventPlanConfig.setPlatformConfigJson(planConfig.getPlatformConfigJson());
        }
        eventPlanConfig.setPlanConfigId(planConfig.getId());
        eventPlanConfig.setPlanConfig(planConfig);
        if (event.getWhiteLabel() != null) {
            eventPlanConfig.setWhiteLabel(event.getWhiteLabel());
            eventPlanConfig.setOrganizer(event.getOrganizer());
            event.setSubscriptionId(event.getWhiteLabel().getSubscriptionId());
            event.setBillingType(event.getWhiteLabel().getBillingType());
        } else {
            eventPlanConfig.setWhiteLabel(null);
            eventPlanConfig.setOrganizer(event.getOrganizer());
            event.setSubscriptionId(event.getOrganizer() != null ? event.getOrganizer().getSubscriptionId() : null);
            event.setBillingType(event.getOrganizer() != null ? event.getOrganizer().getBillingType() : BillingType.Paid);
        }
    }

    @Override
    public String getEventPlanName(Long eventId){
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(eventId);
        return eventPlanConfig!=null ? eventPlanConfig.getPlanConfig().getPlanName():"";
    }

    @Override
    public EventPlanConfig associateNewChargePlanToEvent(Event event, ChargeConfig chargeConfig) {
        EventPlanConfig eventPlanConfig = eventPlanConfigRepoService.findByEventId(event.getEventId());
        eventPlanConfig.setChargeAssociated(true);
        PlanConfig basePlanConfig = getPlanConfigForSingleEventUnitPlan(chargeConfig);
        return copyConfigAndSendNotification(event, eventPlanConfig, basePlanConfig);
    }

    @Override
    public boolean isChangeRequiredForMultipleToSingleDayEvent(Event event, Ticketing ticketing,
                                                               EventTicketingDto ticketingDto){
        String organizerOrWLPlanName = "";
        if(event.getWhiteLabel()!=null && event.getWhiteLabel().getPlanConfig()!=null){
            organizerOrWLPlanName = event.getWhiteLabel().getPlanConfig().getPlanName();
        } else if(event.getOrganizer()!=null && event.getOrganizer().getPlanConfig()!=null){
            organizerOrWLPlanName = event.getOrganizer().getPlanConfig().getPlanName();
        }
        return  (( FREE_PLAN.getName().equals(organizerOrWLPlanName) ||
                SCALE_PLAN.getName().equals(organizerOrWLPlanName) ||
                STARTER.getName().equals(organizerOrWLPlanName) ||
                (event.getOrganizer()!=null && isProfessionalChargeUsedInSingleEventPlan(event.getOrganizer())))
                && IN_PERSON.equals(ticketing.getEventid().getEventFormat()));
    }

    @Override
    public void validateTheOrganizerOrWhiteLabelPlan(Event event) {
        log.info("validateTheOrganizerOrWhiteLabelPlan for event {}", event.getEventId());
        Event eventdb = eventService.findEventByEventURLWithoutCache(event.getEventURL());
        Organizer organizer = eventdb.getOrganizer();
        WhiteLabel whiteLabel = eventdb.getWhiteLabel();
        if (whiteLabel != null) {
            PlanConfig planConfig = whiteLabel.getPlanConfig();
            log.info("validateTheOrganizerOrWhiteLabelPlan for whiteLabelId {} and plan id {}", whiteLabel.getId(), planConfig.getId());
            if (planConfig.getId() == 4 && planConfig.getPlanName().equals(FREE_PLAN.getName())) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_ALLOWED_TO_CREATE_RTMP_KEY_FOR_FREE_PLAN);
            }
        } else if (organizer != null) {
            PlanConfig planConfig = organizer.getPlanConfig();
            log.info("validateTheOrganizerOrWhiteLabelPlan for organizerId {} and plan id {}", organizer.getId(), planConfig.getId());
            if (planConfig.getId() == 4 && planConfig.getPlanName().equals(FREE_PLAN.getName())) {
                throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_ALLOWED_TO_CREATE_RTMP_KEY_FOR_FREE_PLAN);
            }
        }
    }

    private boolean isProfessionalChargeUsedInSingleEventPlan(Organizer eventOrganizer){
        if((SINGLE_EVENT_UNIT.getName().equals(eventOrganizer.getPlanConfig().getPlanName()))
                && !chargesPurchasedRepoService.findByOrganizerId(eventOrganizer.getId()).stream().
                anyMatch(e -> ChargeConfigNames.PROFESSIONAL.getLableName().equals(e.getChargeConfig().getChargeDisplayName()))){
            return true;
        }
        return false;
    }

    @Override
    public Map<Long, EventPlanConfig> findEventPlanConfigMapByEventIds(List<Long> eventIds) {
        List<EventPlanConfig> list = eventPlanConfigRepository.findByEventIds(eventIds);
        Map<Long, EventPlanConfig> map = new HashMap<>();
        for (EventPlanConfig eventPlanConfig : list) {
            map.put(eventPlanConfig.getEventId(), eventPlanConfig);
        }
        return map;
    }

    @Override
    public List<EventPlanConfig> findEventPlanConfigByEventIds(List<Long> eventIds) {
        return eventPlanConfigRepository.findByEventIds(eventIds);
    }

    @Override
    public void updateAttendeeImportLimitOnceStripeIsConnected(Event event) {
        Optional<EventPlanConfig> eventPlanConfigOpt = eventPlanConfigRepository.findByEventId(event.getEventId());
        if (eventPlanConfigOpt.isPresent()) {
            EventPlanConfig eventPlanConfig = eventPlanConfigOpt.get();
            PlanConfig planConfig = eventPlanConfig.getPlanConfig();
            if (planConfig != null && FREE_PLAN.getName().equals(planConfig.getPlanName())) {
                eventPlanConfig.setAttendeeImportLimit(2000L);
                eventPlanConfigRepository.save(eventPlanConfig);
            }
        }
    }
}
