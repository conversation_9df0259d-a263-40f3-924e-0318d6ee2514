package com.accelevents.billing.chargebee.repositories;

import com.accelevents.domain.ChargeConfig;
import com.accelevents.domain.ChargesPurchased;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.WhiteLabel;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ChargesPurchasedRepository extends CrudRepository<ChargesPurchased,Long> {

    Optional<ChargesPurchased> findById(@Param("id") Long id);

    List<ChargesPurchased> findByOrganizerAndChargeConfigAndChargeStatusNot(
            @Param("organizer") Organizer organizer,
            @Param("chargeConfig") ChargeConfig chargeConfig,
            @Param("chargeStatus") ChargesPurchased.ChargeStatus chargeStatus);

    List<ChargesPurchased> findByIdAndChargeStatus(@Param("id") Long id, @Param("chargeStatus") ChargesPurchased.ChargeStatus chargeStatus);

    List<ChargesPurchased> findByOrganizerAndChargeConfig(@Param("organizer") Organizer organizer,
                                                          @Param("chargeConfig") ChargeConfig chargeConfig);

    List<ChargesPurchased> findByOrganizerId(@Param("organizerId") long organizerId);
    @Query("SELECT cp FROM ChargesPurchased cp WHERE cp.organizerId = :organizerId AND COALESCE(cp.chargeStatus,'') <>:chargeStatus ")
    List<ChargesPurchased> findPurchasedChargesByOrganizerIdAndNotUsed(@Param("organizerId") Long organizerId,@Param("chargeStatus") ChargesPurchased.ChargeStatus chargeStatus);

    List<ChargesPurchased> findByInvoiceId(@Param("invoiceId") String invoiceId);

    @Query("SELECT COUNT(*) > 0 FROM ChargesPurchased AS" +
            " chargesPurchased WHERE chargesPurchased.invoiceId = :invoiceId and chargeConfig != :chargeConfig ")
    boolean existsByInvoiceIdAndNotChargeConfig(@Param("invoiceId") String invoiceId,
                                                @Param("chargeConfig") ChargeConfig chargeConfig);

    List<ChargesPurchased> findByWhiteLabel(@Param("whiteLabel") WhiteLabel whiteLabel);
    @Query(value = "select COALESCE(sum(cc.quantity),0) from chargebee_charges_purchased cc " +
            "join  chargebee_charges c on cc.charge_id=c.id  " +
            "where cc.organizer_id=:organiserId and cc.subscription_id=:subscriptionId and c.charge_display_name in (:chargeDisplayNames) order by cc.id desc;",nativeQuery = true)
    Long getPrePurchasedRegistrationQuantity(@Param("organiserId") Long organiserId,@Param("chargeDisplayNames") List<String> chargeDisplayNames,@Param("subscriptionId") String subscriptionId);
    @Query(value = "select COALESCE(sum(cc.quantity),0) from chargebee_charges_purchased cc " +
            "join  chargebee_charges c on cc.charge_id=c.id  " +
            "where cc.whitelabel_id=:whiteLabelId and cc.subscription_id=:subscriptionId and c.charge_display_name in (:chargeDisplayNames) order by cc.id desc;",nativeQuery = true)
    Long getPrePurchasedRegistrationQuantityByWL(@Param("whiteLabelId") Long whiteLabelId,@Param("chargeDisplayNames") List<String> chargeDisplayNames,@Param("subscriptionId") String subscriptionId);
    @Query(value = "select COALESCE(sum(cc.quantity),0) from chargebee_charges_purchased cc " +
            "join  chargebee_charges c on cc.charge_id=c.id  " +
            "where cc.organizer_id=:organiserId and cc.subscription_id=:subscriptionId and c.charge_display_name in (:chargeDisplayNames) order by cc.id desc;",nativeQuery = true)
    Long getPurchasedProBoothQuantity(@Param("organiserId") Long organiserId,@Param("chargeDisplayNames") List<String> chargeDisplayNames,@Param("subscriptionId") String subscriptionId);
    @Query(value = "select COALESCE(sum(cc.quantity),0) from chargebee_charges_purchased cc " +
            "join  chargebee_charges c on cc.charge_id=c.id  " +
            "where cc.whitelabel_id=:whiteLabelId and cc.subscription_id=:subscriptionId and c.charge_display_name in (:chargeDisplayNames) order by cc.id desc;",nativeQuery = true)
    Long getPurchasedProBoothQuantityByWL(@Param("whiteLabelId") Long whiteLabelId,@Param("chargeDisplayNames") List<String> chargeDisplayNames,@Param("subscriptionId") String subscriptionId);
    @Query(value = "select COALESCE(sum(cc.quantity),0) from chargebee_charges_purchased cc " +
            "join  chargebee_charges c on cc.charge_id=c.id  " +
            "where cc.organizer_id=:organiserId and cc.subscription_id=:subscriptionId and c.charge_display_name in (:chargeDisplayNames) and cc.invoice_id=:invoiceId order by cc.id desc;",nativeQuery = true)
    Long getPurchasedProBoothQuantityByInvoiceId(@Param("organiserId") Long organiserId,@Param("chargeDisplayNames") List<String> chargeDisplayNames,@Param("subscriptionId") String subscriptionId,@Param("invoiceId") String invoiceId);
    @Query(value = "select COALESCE(sum(cc.quantity),0) from chargebee_charges_purchased cc " +
            "join  chargebee_charges c on cc.charge_id=c.id  " +
            "where cc.whitelabel_id=:whiteLabelId and cc.subscription_id=:subscriptionId and c.charge_display_name in (:chargeDisplayNames) and cc.invoice_id=:invoiceId order by cc.id desc;",nativeQuery = true)
    Long getPurchasedProBoothQuantityByWLAndInvoiceId(@Param("whiteLabelId") Long whiteLabelId,@Param("chargeDisplayNames") List<String> chargeDisplayNames,@Param("subscriptionId") String subscriptionId,@Param("invoiceId") String invoiceId);

    @Query(value = "Select cp from ChargesPurchased cp join ChargeConfig cc on cp.chargeConfig.id = cc.id " +
            "where cp.organizerId =:organizerId and cc.chargeDisplayName in (:chargeList)")
    List<ChargesPurchased> findByOrganizerIdAndChargeList(@Param("organizerId") long organizerId,@Param("chargeList") List<String> chargeList);
    @Query(value = "Select cp from ChargesPurchased cp where cp.chargeConfig=:chargeConfig and whiteLabel=:whiteLabel and subscriptionId=:subscriptionId")
    List<ChargesPurchased> findPurchasedChargesByChargeAndWhiteLabelAndSubscriptionId(@Param("chargeConfig") ChargeConfig chargeConfig,@Param("whiteLabel") WhiteLabel whiteLabel,@Param("subscriptionId") String subscriptionId);
    @Query(value = "Select cp from ChargesPurchased cp where cp.chargeConfig=:chargeConfig and organizer=:organizer and subscriptionId=:subscriptionId")
    List<ChargesPurchased> findPurchasedChargesByChargeAndOrganiserAndSubscriptionId(@Param("chargeConfig") ChargeConfig charge,@Param("organizer") Organizer organizer,@Param("subscriptionId") String subscriptionId);

    @Query(value = "SELECT COUNT(id) FROM ChargesPurchased WHERE chargeConfig.chargeDisplayName IN (:chargeNames) AND " +
            " organizerId=:organizerId AND subscriptionId=:subscriptionId AND chargeStatus!='USED'")
    Long findChargePurchasedCountByOrganiserIdAndSubscriptionId(@Param("organizerId") Long organizerId, @Param("subscriptionId") String subscriptionId, @Param("chargeNames") List<String> chargeNames);
}
