package com.accelevents.billing.chargebee.repo.impl;

import com.accelevents.billing.chargebee.enums.ChargeConfigNames;
import com.accelevents.billing.chargebee.repo.ChargesPurchasedRepoService;
import com.accelevents.billing.chargebee.repositories.ChargesPurchasedRepository;
import com.accelevents.domain.ChargeConfig;
import com.accelevents.domain.ChargesPurchased;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.WhiteLabel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.accelevents.billing.chargebee.enums.ChargeConfigNames.*;

@Service
public class ChargesPurchasedRepoServiceImpl implements ChargesPurchasedRepoService {

    @Autowired
    private ChargesPurchasedRepository chargesPurchasedRepository;

    @Override
    public Optional<ChargesPurchased> findById(Long id) {
        return chargesPurchasedRepository.findById(id);
    }

    @Override
    public List<ChargesPurchased> findByInvoiceId(String invoiceId) {
        return chargesPurchasedRepository.findByInvoiceId(invoiceId);
    }

    @Override
    public ChargesPurchased save(ChargesPurchased chargesPurchased) {
        return chargesPurchasedRepository.save(chargesPurchased);
    }

    @Override
    public List<ChargesPurchased> findByOrganizerAndChargeConfigIdAndChargeStatusNot(
            Organizer organizer, ChargeConfig chargeConfig, ChargesPurchased.ChargeStatus chargeStatus) {
        return chargesPurchasedRepository.findByOrganizerAndChargeConfigAndChargeStatusNot(organizer, chargeConfig, chargeStatus);
    }

    @Override
    public List<ChargesPurchased> findByOrganizerAndChargeConfig(Organizer organizer, ChargeConfig chargeConfig) {
        return chargesPurchasedRepository.findByOrganizerAndChargeConfig(organizer, chargeConfig);
    }

    @Override
    public List<ChargesPurchased> findByOrganizerId(long organizerId) {
        return chargesPurchasedRepository.findByOrganizerId(organizerId);
    }

    @Override
    public List<ChargesPurchased> findPurchasedChargesByOrganizerIdAndNotUsed(long organizerId) {
        return chargesPurchasedRepository.findPurchasedChargesByOrganizerIdAndNotUsed(organizerId, ChargesPurchased.ChargeStatus.USED);
    }

    @Override
    public List<ChargesPurchased> findByWhiteLabel(WhiteLabel whiteLabel) {
        return chargesPurchasedRepository.findByWhiteLabel(whiteLabel);
    }

    @Override
    public void saveAll(List<ChargesPurchased> chargesPurchasedList) {
        chargesPurchasedRepository.saveAll(chargesPurchasedList);
    }

    @Override
    public boolean existsByInvoiceIdAndNotChargeConfig(String invoiceId, ChargeConfig chargeConfig) {
        return chargesPurchasedRepository.existsByInvoiceIdAndNotChargeConfig(invoiceId, chargeConfig);
    }

    @Override
    public List<ChargesPurchased> findByIdAndChargeStatus(long id, ChargesPurchased.ChargeStatus chargeStatus) {
        return chargesPurchasedRepository.findByIdAndChargeStatus(id,chargeStatus);
    }

    @Override
    public Long getPrePurchasedRegistrationQuantity(Long organiserId, Long whiteLabelId,String subscriptionId) {
        List<String> chargeNameList = ChargeConfigNames.getRegistrantsChargeNameList();
        if (null != whiteLabelId) {
            return chargesPurchasedRepository.getPrePurchasedRegistrationQuantityByWL(whiteLabelId, chargeNameList,subscriptionId);
        }
        return chargesPurchasedRepository.getPrePurchasedRegistrationQuantity(organiserId, chargeNameList,subscriptionId);
    }

    @Override
    public Long getPurchasedProBoothQuantity(Long organiserId, Long whiteLabelId, String subscriptionId, String invoiceId) {
        List<String> chargeNameList = new ArrayList<>();
        chargeNameList.add(EXHIBITOR_PRO_BOOTH.getLableName());
        if (null != invoiceId) {
            if (null != whiteLabelId) {
                return chargesPurchasedRepository.getPurchasedProBoothQuantityByWLAndInvoiceId(whiteLabelId, chargeNameList, subscriptionId, invoiceId);
            }
            return chargesPurchasedRepository.getPurchasedProBoothQuantityByInvoiceId(organiserId, chargeNameList, subscriptionId, invoiceId);
        }
        if (null != whiteLabelId) {
            return chargesPurchasedRepository.getPurchasedProBoothQuantityByWL(whiteLabelId, chargeNameList, subscriptionId);
        }
        return chargesPurchasedRepository.getPurchasedProBoothQuantity(organiserId, chargeNameList, subscriptionId);
    }
    @Override
    public List<ChargesPurchased> findByOrganizerIdAndChargeList(long organizerId) {
        List<String> chargeNameList = ChargeConfigNames.getRegistrantsChargeNameList();
        return chargesPurchasedRepository.findByOrganizerIdAndChargeList(organizerId, chargeNameList);
    }

    @Override
    public List<ChargesPurchased> findPurchasedChargesByChargeIdAndOrganiserOrWhiteLabelAndSubscriptionId(ChargeConfig charge, Organizer organizer, WhiteLabel whiteLabel, String subscriptionId) {
        if (whiteLabel != null) {
            return chargesPurchasedRepository.findPurchasedChargesByChargeAndWhiteLabelAndSubscriptionId(charge, whiteLabel, subscriptionId);
        } else if (organizer != null) {
            return chargesPurchasedRepository.findPurchasedChargesByChargeAndOrganiserAndSubscriptionId(charge, organizer, subscriptionId);
        }
        return Collections.emptyList();
    }

    @Override
    public Long findChargePurchasedCountByOrganiser(Organizer organizer) {
        List<String> chargeNames = new ArrayList<>();
        chargeNames.add(STARTER_2023.getLableName());
        chargeNames.add(PROFESSIONAL_2023.getLableName());
        return chargesPurchasedRepository.findChargePurchasedCountByOrganiserIdAndSubscriptionId(organizer.getId(), organizer.getSubscriptionId(), chargeNames);
    }
}