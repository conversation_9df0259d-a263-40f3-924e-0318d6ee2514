package com.accelevents.billing.chargebee.service.impl;

import com.accelevents.billing.chargebee.repo.*;
import com.accelevents.billing.chargebee.repositories.ChargebeeCreditsLogsRepository;
import com.accelevents.billing.chargebee.repositories.ChargebeeTransactionRepository;
import com.accelevents.billing.chargebee.service.*;
import com.accelevents.billing.chargebee.util.ChargeBeeUtils;
import com.accelevents.configuration.ChargebeeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.ChargebeeTransactionSource;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.virtual.CreditType;
import com.accelevents.services.ContactModuleSettingsService;
import com.accelevents.services.DonationSettingsService;
import com.accelevents.services.repo.helper.WhiteLabelRepoService;
import com.chargebee.models.Invoice;
import com.chargebee.models.Subscription;
import com.chargebee.models.UnbilledCharge;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.accelevents.enums.PlanConfigNames.FREE_PLAN;
import static com.accelevents.utils.Constants.ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN;

@Service
public class ChargeBeeHelper implements ChargeBeeHelperService {

    private static final Logger log = LoggerFactory.getLogger(ChargeBeeHelper.class);

    @Autowired
    private ChargebeePaymentService chargebeePaymentService;
    @Autowired
    private ChargebeePlanService chargebeePlanService;
    @Autowired
    private ChargebeeConfiguration chargebeeConfiguration;
    @Autowired
    private ChargebeeTransactionRepository chargebeeTransactionRepository;
    @Autowired
    private DonationSettingsService donationSettingsService;
    @Autowired
    private ChargebeeBillingService chargebeeBillingService;

    @Autowired
    private WhiteLabelRepoService whiteLabelRepoService;

    @Autowired
    private ChargebeePlanRepoService chargebeePlanRepoService;

    @Autowired
    private ChargebeeEventCreditsRepoService chargebeeEventCreditsRepoService;

    @Autowired
    private OrganizerRepoService organizerRepoService;

    @Autowired
    private ChargebeeCreditsLogsRepository chargebeeCreditsLogsRepository;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private ChargeConfigRepoService chargeConfigRepoService;
    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Autowired
    private ChargeBeePaymentHandlerService chargeBeePaymentHandlerService;
    @Autowired
    private ChargeBeeCustomerInvoicesService chargeBeeCustomerInvoicesService;
    @Autowired
    private ContactModuleSettingsService contactModuleSettingsService;

    @Override
    public void insertUsageInChargeBeeForUsageSubscriptionB(String subscriptionAId, Long planId) throws Exception {
        try {
            PlanConfig planConfig = chargebeePlanService.findById(planId).orElse(null);
            Subscription subscriptionA = chargebeePaymentService.retrieveSubscription(subscriptionAId);
            Long usageTillDateA = chargebeePaymentService.retrieveUsageTillDate(subscriptionAId);
            Long usageCurrentTermA = chargebeePaymentService.retrieveUsageForCurrentTerm(subscriptionAId, subscriptionA.currentTermStart(), subscriptionA.currentTermEnd());
            String subscriptionBId = ChargeBeeUtils.getSubscriptionBId(subscriptionAId,planId);
            Subscription subscriptionB = chargebeePaymentService.retrieveSubscription(subscriptionBId);
            if (subscriptionB.subscriptionItems() != null) {
                Optional<Subscription.SubscriptionItem> optMonthlyPlanId = subscriptionB.subscriptionItems().stream().filter(subscriptionItem -> (planConfig.getMonthlyPlanId().equalsIgnoreCase(subscriptionItem.itemPriceId()) || chargebeeConfiguration.getMonthlyPlanId().equalsIgnoreCase(subscriptionItem.itemPriceId()))).findAny();
                if (optMonthlyPlanId.isPresent()) {
                    String monthlyPlanId = optMonthlyPlanId.get().itemPriceId();
                    log.info("Insert usage in ChargeBee for Usage Subscription Plan, subscriptionAId : {}, planId : {} ", subscriptionAId, planId);
                    if (usageTillDateA != null && usageCurrentTermA != null && usageTillDateA > planConfig.getFreeQuantity() && usageCurrentTermA != 0) {//NOSONAR
                        long usedQty = chargebeePaymentService.checkPreviousInvoiceGenerated(subscriptionBId) ? usageCurrentTermA : (usageTillDateA - planConfig.getFreeQuantity());
                        log.info("Sending monthly usage, subscriptionBId : {}, usedQty : {}, monthlyPlanId : {} ", subscriptionBId, usedQty, planConfig.getMonthlyPlanId());
                        chargebeePaymentService.sendUsageToSubscriptionBMonthly(subscriptionBId, usedQty, monthlyPlanId);
                    }
                } else {
                    log.info("Plan Name Not matching for subscription id {} and plan id {} ", subscriptionA, planId);
                }
            }
        }catch (Exception e){
            log.error("failed insertUsageInChargeBeeForUsageSubscriptionB for subscriptionId {} exception {} ", subscriptionAId, e.getLocalizedMessage());
        }

    }

    @Transactional
    @Override
    public void handleCancelTextToGiveSubscription(Subscription subscription){
        if(!isTextToGiveSubscriptionExist(subscription)){
            log.info("Subscription with text to give plan not exist in subscription id {} and customer id {}",subscription.id(),subscription.customerId());//NOSONAR
            return;
        }
        if(!Subscription.Status.CANCELLED.equals(subscription.status())){
            log.info("Subscription status {} for subscription id {} and customer id {} ", subscription.status(),subscription.id(),subscription.customerId());//NOSONAR
            return;
        }
        ChargebeeTransaction chargebeeTransaction = chargebeeTransactionRepository.findBySourceAndTextToGiveSubscriptionId(ChargebeeTransactionSource.MODULE_ACTIVATE, subscription.id());
        if(chargebeeTransaction!=null) {
            DonationSettings donationSettings = donationSettingsService.getByEventId(chargebeeTransaction.getEvent().getEventId());
            chargebeeTransaction.setTextToGiveSubscriptionId(null);
            chargebeeBillingService.save(chargebeeTransaction);
            if(null != donationSettings) {
                donationSettings.setTextToGiveActivated(false);
                donationSettingsService.save(donationSettings);
            }

        }
    }

    @Override
    public void handleChargeOrAddonCredits(Invoice invoice) {

        WhiteLabel whiteLabel = null;
        Organizer organizer = null;
        Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional = Optional.empty();

        Optional<WhiteLabel> optionalWhiteLabel = whiteLabelRepoService.findBySubscriptionId(invoice.subscriptionId());
        if (optionalWhiteLabel.isPresent()) {
            whiteLabel = optionalWhiteLabel.get();
        } else {
            List<Organizer> organizers = organizerRepoService.findBySubscriptionId(invoice.subscriptionId());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(organizers)) {
                organizer = organizers.get(0);
            }
        }

        if (!org.apache.commons.collections.CollectionUtils.isEmpty(invoice.lineItems())) {
            for (Invoice.LineItem lineItem : invoice.lineItems()) {
                boolean isCreditAddonOrCharge = createChargebeeLogRecord(invoice.subscriptionId(), lineItem.entityId(), lineItem.quantity(), whiteLabel, organizer, false);
                if (isCreditAddonOrCharge) {
                    if (whiteLabel != null) {
                        chargebeeEventCreditsOptional =
                                chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
                    } else if (organizer != null) {
                        chargebeeEventCreditsOptional =
                                chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
                    }
                    if (chargebeeEventCreditsOptional.isPresent()) {
                        ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsOptional.get();
                        chargebeeEventCredits.setRemainingFreeCredits(chargebeeEventCredits.getRemainingFreeCredits() + lineItem.quantity());
                        chargebeeEventCreditsRepoService.save(chargebeeEventCredits, null);
                    }
                }
            }
        }
        chargeBeeCustomerInvoicesService.saveCustomerInvoice(invoice);
    }

    @Override
    public void handleChargeOrAddonCreditsForUnbilledCharge( List<UnbilledCharge> unbilledCharges) {


        if (!org.apache.commons.collections.CollectionUtils.isEmpty(unbilledCharges)) {
            for (UnbilledCharge unbilledCharge : unbilledCharges) {
                WhiteLabel whiteLabel = null;
                Organizer organizer = null;

                Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional=Optional.empty();

                Optional<WhiteLabel> optionalWhiteLabel = whiteLabelRepoService.findBySubscriptionId(unbilledCharge.subscriptionId());
                if (optionalWhiteLabel.isPresent()) {
                    whiteLabel = optionalWhiteLabel.get();
                }else {
                    List<Organizer> organizers = organizerRepoService.findBySubscriptionId(unbilledCharge.subscriptionId());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(organizers)) {
                        organizer = organizers.get(0);
                    }
                }

                boolean isCreditAddonOrCharge=  createChargebeeLogRecord(unbilledCharge.subscriptionId(),unbilledCharge.entityId(),unbilledCharge.quantity(),whiteLabel,organizer,false);

                if (isCreditAddonOrCharge) {
                    if (whiteLabel != null) {
                        chargebeeEventCreditsOptional =
                                chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
                    } else if (organizer != null) {
                        chargebeeEventCreditsOptional =
                                chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
                    }
                    if (chargebeeEventCreditsOptional.isPresent()) {
                        ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsOptional.get();
                        chargebeeEventCredits.setRemainingFreeCredits(chargebeeEventCredits.getRemainingFreeCredits() + unbilledCharge.quantity());
                        chargebeeEventCreditsRepoService.save(chargebeeEventCredits, null);
                    }

                }
            }
        }
    }

    @Override
    public void handleSubscriptionExpiryDate(Subscription subscription) {
        Date subscriptionExpiryDate = findSubscriptionEndTermDate(subscription);
        Optional<WhiteLabel> optionalWhiteLabel = whiteLabelRepoService.findBySubscriptionId(subscription.id());
        if (optionalWhiteLabel.isPresent()) {
            WhiteLabel whiteLabel = optionalWhiteLabel.get();
            whiteLabel.setSubscriptionExpireAt(subscriptionExpiryDate);
            whiteLabel.setSubscriptionStatus(subscription.status().toString());
            whiteLabelRepoService.save(whiteLabel);
            List<Organizer> organisers = organizerRepoService.findOrganizerByWhiteLabel(whiteLabel.getId());
            organisers.forEach(organizer -> {
                organizer.setSubscriptionExpireAt(whiteLabel.getSubscriptionExpireAt());
                organizer.setSubscriptionStatus(whiteLabel.getSubscriptionStatus());
            });
            organizerRepoService.saveAll(organisers);
        } else {
            List<Organizer> organizers = organizerRepoService.findBySubscriptionId(subscription.id());
            if (!organizers.isEmpty()) {
                Organizer organiser = organizers.get(0);
                organiser.setSubscriptionExpireAt(subscriptionExpiryDate);
                organiser.setSubscriptionStatus(subscription.status().toString());
                organizerRepoService.save(organiser);
            }
        }
    }

    @Override
    public void handleCreditsForRenewalPlan(Subscription subscription, boolean isSubscriptionRenew) {
        String subscriptionId = subscription.id();
        log.info("handleCreditsForRenewalPlan subscriptionId {} isSubscriptionRenew {}", subscriptionId, isSubscriptionRenew);

        WhiteLabel whiteLabel = null;
        Organizer organizer = null;
        PlanConfig organiserOrWhiteLabelExistingPlanConfig;

        Optional<WhiteLabel> optionalWhiteLabel = whiteLabelRepoService.findBySubscriptionId(subscriptionId);
        if (optionalWhiteLabel.isPresent()) {
            whiteLabel = optionalWhiteLabel.get();
            organiserOrWhiteLabelExistingPlanConfig = whiteLabel.getPlanConfig();
            whiteLabel.setSubscriptionExpireAt(findSubscriptionEndTermDate(subscription));
            whiteLabel.setSubscriptionStatus(subscription.status().toString());
            whiteLabel.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
            whiteLabelRepoService.save(whiteLabel);
        } else {
            List<Organizer> organizers = organizerRepoService.findBySubscriptionId(subscription.id());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(organizers)) {
                organizer = organizers.get(0);
                organiserOrWhiteLabelExistingPlanConfig = organizer.getPlanConfig();
                organizer.setSubscriptionExpireAt(findSubscriptionEndTermDate(subscription));
                organizer.setSubscriptionStatus(subscription.status().toString());
                organizer.setEngageEmailsLimit(ENGAGE_EMAIL_LIMIT_FOR_PAID_PLAN);
                organizerRepoService.save(organizer);
            } else {
                organiserOrWhiteLabelExistingPlanConfig = null;
            }
        }
        for (Subscription.SubscriptionItem subscriptionItem : subscription.subscriptionItems()) {
            log.info("handleCreditsForRenewalPlan subscriptionId {} itemType {} subscriptionItem {}", subscriptionId, subscriptionItem.itemType(), subscriptionItem);
            Optional<PlanConfig> optionalPlanConfig = chargebeePlanRepoService.findByNewYearlyPlanId(subscriptionItem.itemPriceId());
            if (optionalPlanConfig.isPresent()) {
                PlanConfig planConfig = optionalPlanConfig.get();
                boolean isPlanChange = null != organiserOrWhiteLabelExistingPlanConfig && organiserOrWhiteLabelExistingPlanConfig.getNewYearlyPlanId()!=null && !organiserOrWhiteLabelExistingPlanConfig.getNewYearlyPlanId().equalsIgnoreCase(planConfig.getNewYearlyPlanId());
                Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional = getChargeBeeEventCreditsBasedOnOrganiserAndWhiteLabel(whiteLabel, organizer);
                if (isPlanChange && whiteLabel != null) {
                    whiteLabel.setPlanConfig(planConfig);
                    whiteLabelRepoService.save(whiteLabel);
                }
                if (isPlanChange && organizer != null) {
                    organizer.setPlanConfig(planConfig);
                    organizerRepoService.save(organizer);
                }
                updateOrganiserOrWhiteLabelCreditsBasedOnPlanChangesOrPlanRenewal(isSubscriptionRenew, chargebeeEventCreditsOptional, isPlanChange, subscriptionId, whiteLabel, organizer, planConfig);
            }
        }
        if (whiteLabel != null) {
            chargeBeePaymentHandlerService.updatePlanUsageOverageRateForWhiteLabel(whiteLabel, subscription);
            chargeBeePaymentHandlerService.updatePlanUsageIncludedCreditsForWhiteLabel(whiteLabel, subscription);
            chargeBeePaymentHandlerService.calculateSubscriptionFreeAndPaidCreditsForWhiteLabel(whiteLabel, null,isSubscriptionRenew);
            chargeBeePaymentHandlerService.updatePurchasedProBoothForWhiteLabel(whiteLabel, null);
            contactModuleSettingsService.updateAllEventEngageEmailLimit(whiteLabel,null);
        } else if (organizer != null) {
            chargeBeePaymentHandlerService.updatePlanUsageOverageRateForOrganiser(organizer, subscription);
            chargeBeePaymentHandlerService.updatePlanUsageIncludedCreditsForOrganiser(organizer, subscription);
            chargeBeePaymentHandlerService.calculateSubscriptionFreeAndPaidCreditsForOrganiser(organizer, null,isSubscriptionRenew);
            chargeBeePaymentHandlerService.updatePurchasedProBoothForOrganiser(organizer, null);
            contactModuleSettingsService.updateAllEventEngageEmailLimit(null,organizer);
        }
    }

    private void updateOrganiserOrWhiteLabelCreditsBasedOnPlanChangesOrPlanRenewal(boolean isSubscriptionRenew, Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional, boolean isPlanChange, String subscriptionId, WhiteLabel whiteLabel, Organizer organizer, PlanConfig planConfig) {
        if (chargebeeEventCreditsOptional.isPresent() && (isPlanChange || isSubscriptionRenew)) {
            ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsOptional.get();
            log.info("handleCreditsForRenewalPlan subscriptionId {} isPlanChange {} isSubscriptionRenew {}", subscriptionId, isPlanChange, isSubscriptionRenew);
            createChargebeeLogRecord(subscriptionId, isPlanChange ? CreditType.REMAIN_CREDITS_BEFORE_PLAN_CHANGE.name() : CreditType.REMAIN_CREDITS_BEFORE_RENEWAL.name(), chargebeeEventCredits.getRemainingFreeCredits(), whiteLabel, organizer, false);
            chargebeeEventCreditsRepoService.save(chargebeeEventCredits, null);
            createChargebeeLogRecord(subscriptionId, isPlanChange ? CreditType.PLAN_CHANGE_DEFAULT.name() : CreditType.RENEWAL_PLAN_DEFAULT.name(), planConfig.getFreeQuantity(), whiteLabel, organizer, false);
        }
    }


    private Optional<ChargebeeEventCredits> getChargeBeeEventCreditsBasedOnOrganiserAndWhiteLabel(WhiteLabel whiteLabel, Organizer organizer) {
        if (whiteLabel != null) {
            return chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
        } else if (organizer != null) {
            return chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
        }
        return Optional.empty();
    }
    @Override
    public void handleCreditsForCancelSubscription(Subscription subscription) {
        WhiteLabel whiteLabel = null;
        Organizer organizer = null;
        Optional<ChargebeeEventCredits> chargebeeEventCreditsOptional = Optional.empty();

        log.info("handle credit for cancel subscription..");
        Optional<WhiteLabel> optionalWhiteLabel = whiteLabelRepoService.findBySubscriptionId(subscription.id());
        if (optionalWhiteLabel.isPresent()) {
            whiteLabel = optionalWhiteLabel.get();
            whiteLabel.setSubscriptionExpireAt(findSubscriptionEndTermDate(subscription));
            whiteLabel.setSubscriptionStatus(subscription.status().toString());
            whiteLabelRepoService.save(whiteLabel);
        } else {
            List<Organizer> organizers = organizerRepoService.findBySubscriptionId(subscription.id());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(organizers)) {
                organizer = organizers.get(0);
                organizer.setSubscriptionExpireAt(findSubscriptionEndTermDate(subscription));
                organizer.setSubscriptionStatus(subscription.status().toString());
                organizerRepoService.save(organizer);
            }
        }
        log.info("After org/wl retrival for cancel subscription..");


        if (whiteLabel != null) {
            chargebeeEventCreditsOptional =
                    chargebeeEventCreditsRepoService.findByWhiteLabelId(whiteLabel.getId());
        } else if (organizer != null) {
            chargebeeEventCreditsOptional =
                    chargebeeEventCreditsRepoService.findByOrganizerId(organizer.getId());
        }
        log.info("After chargebeeCredits retrival for cancel subscription..");

        if (chargebeeEventCreditsOptional.isPresent()) {
            ChargebeeEventCredits chargebeeEventCredits = chargebeeEventCreditsOptional.get();
            createChargebeeLogRecord(subscription.id(), CreditType.REMAIN_CREDITS_BEFORE_CANCEL.name(), chargebeeEventCredits.getRemainingFreeCredits(), whiteLabel, organizer, false);
            chargebeeEventCredits.setRemainingFreeCredits(0);
            chargebeeEventCreditsRepoService.save(chargebeeEventCredits, null);
            createChargebeeLogRecord(subscription.id(), CreditType.PLAN_CANCEL.name(), 0, whiteLabel, organizer, false);
        }
    }



    @Override
    public  boolean createChargebeeLogRecord(String subscription,String entityId,long quantity,WhiteLabel whiteLabel,Organizer organizer,boolean isPlan){
        log.info("createChargebeeLogRecord for subscription {} and entity id {} ",subscription,entityId);

        boolean isCreditAddonOrCharge=false;
        if (StringUtils.isNotEmpty(subscription)) {
            ChargebeeCreditsLogs chargebeeCreditsLogs=new ChargebeeCreditsLogs();
            chargebeeCreditsLogs.setCredits(quantity);
            if(("Pre-Purchased-Registration-Usage-Charge").equals(entityId) || ("Pre-Purchased-Registration-Usage-Charge-2023").equals(entityId)){
                chargebeeCreditsLogs.setCreditType(CreditType.PRE_PURCHASED_CHARGE);
                isCreditAddonOrCharge=true;
            }else if(("2023-Promotional-Usage-Credits-USD").equals(entityId)){
                chargebeeCreditsLogs.setCreditType(CreditType.PROMOTIONAL_CREDIT_CHARGE);
                isCreditAddonOrCharge=true;
            }else if(("Pre-Purchased-Registration-Usage-Add-On-USD-Yearly").equals(entityId)){
                chargebeeCreditsLogs.setCreditType(CreditType.PRE_PURCHASED_ADDON);
                isCreditAddonOrCharge=true;
            }else if(("2023-Promotional-Usage-Credits-Add-On-USD-Yearly").equals(entityId)){
                chargebeeCreditsLogs.setCreditType(CreditType.PROMOTIONAL_CREDIT_ADDON);
                isCreditAddonOrCharge=true;
            }else if(isPlan){
                chargebeeCreditsLogs.setCreditType(CreditType.RENEWAL_PLAN_DEFAULT);
                isCreditAddonOrCharge=true;
            }else if(CreditType.REMAIN_CREDITS_BEFORE_RENEWAL.name().equals(entityId)){
                chargebeeCreditsLogs.setCreditType(CreditType.REMAIN_CREDITS_BEFORE_RENEWAL);
                isCreditAddonOrCharge=true;
            }
            else if(CreditType.REMAIN_CREDITS_BEFORE_CANCEL.name().equals(entityId)){
                chargebeeCreditsLogs.setCreditType(CreditType.REMAIN_CREDITS_BEFORE_CANCEL);
                isCreditAddonOrCharge=true;

            }else if(CreditType.PLAN_CANCEL.name().equals(entityId)){
                chargebeeCreditsLogs.setCreditType(CreditType.PLAN_CANCEL);
                isCreditAddonOrCharge=true;
            }else if(CreditType.PLAN_CHANGE_DEFAULT.name().equals(entityId)){
            chargebeeCreditsLogs.setCreditType(CreditType.PLAN_CHANGE_DEFAULT);
            isCreditAddonOrCharge=true;
        }else if(CreditType.REMAIN_CREDITS_BEFORE_PLAN_CHANGE.name().equals(entityId)){
            chargebeeCreditsLogs.setCreditType(CreditType.REMAIN_CREDITS_BEFORE_PLAN_CHANGE);
            isCreditAddonOrCharge=true;
        }
            log.info("After setting credit type");

            chargebeeCreditsLogs.setSubscriptionId(subscription);
            chargebeeCreditsLogs.setCreatedAt(new Date());
            if (whiteLabel!=null && isCreditAddonOrCharge) {
                chargebeeCreditsLogs.setWhiteLabelId(whiteLabel.getId());
                chargebeeCreditsLogsRepository.save(chargebeeCreditsLogs);
            }else if (organizer!=null && isCreditAddonOrCharge) {
                    chargebeeCreditsLogs.setOrganizerId(organizer.getId());
                log.info("before saving credit logs");
                chargebeeCreditsLogsRepository.save(chargebeeCreditsLogs);
                }
            }

        return isCreditAddonOrCharge;
    }
        private boolean isTextToGiveSubscriptionExist(Subscription subscription){
        if(CollectionUtils.isEmpty(subscription.subscriptionItems())){
            log.info("Subscription Line Items not available for subscription id {} and customer id {} ", subscription.id(),subscription.customerId());//NOSONAR
            return false;
        }
        Optional<Subscription.SubscriptionItem> optTextToGiveSubscription = subscription.subscriptionItems().stream().filter(subscriptionItem -> chargebeeConfiguration.getTextToGivePlanId().equalsIgnoreCase(subscriptionItem.itemPriceId())).findAny();
        return optTextToGiveSubscription.isPresent();
    }

    @Override
    public boolean validateEvenFreePlan(Event event) {
        EventPlanConfig eventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
        PlanConfig planConfig = eventPlanConfig.getPlanConfig();
        return FREE_PLAN.getName().equals(planConfig.getPlanName());
    }

    @Override
    public Date findSubscriptionEndTermDate(Subscription subscription) {
        if (Subscription.Status.IN_TRIAL.equals(subscription.status())) {
            return new Date(subscription.trialEnd().getTime());
        }
        if (Subscription.Status.FUTURE.equals(subscription.status())) {
            return new Date(subscription.nextBillingAt().getTime());
        }
        if (subscription.cancelledAt() != null) {
            return new Date(subscription.cancelledAt().getTime());
        }
        return new Date(subscription.currentTermEnd().getTime());
    }
}
