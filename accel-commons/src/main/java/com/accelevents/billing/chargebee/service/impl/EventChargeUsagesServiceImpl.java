package com.accelevents.billing.chargebee.service.impl;

import com.accelevents.billing.chargebee.dto.PlatformConfigDto;
import com.accelevents.billing.chargebee.enums.ChargebeeObjectType;
import com.accelevents.billing.chargebee.repo.ChargeConfigRepoService;
import com.accelevents.billing.chargebee.repo.ChargesPurchasedRepoService;
import com.accelevents.billing.chargebee.repo.EventChargeUsagesRepoService;
import com.accelevents.billing.chargebee.repo.OrganizerRepoService;
import com.accelevents.billing.chargebee.repo.impl.ChargebeePlanRepoServiceImpl;
import com.accelevents.billing.chargebee.service.EventChargeUsagesService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EnumMultiLanguageLabelType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exhibitors.dto.ExpoPremiumLabelSettingsDto;
import com.accelevents.exhibitors.services.ExhibitorSettingsService;
import com.accelevents.repositories.MultiLanguageLabelRepo;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.services.MultiLanguageLabelService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.utils.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.accelevents.domain.enums.EventFormat.IN_PERSON;
import static com.accelevents.enums.PlanConfigNames.BUSINESS_PLAN_2023;
import static java.util.concurrent.TimeUnit.DAYS;

@Service
public class EventChargeUsagesServiceImpl implements EventChargeUsagesService {

    private static final Logger log = LoggerFactory.getLogger(EventChargeUsagesServiceImpl.class);

    @Autowired
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Autowired
    private EventChargeUsagesRepoService eventChargeUsagesRepoService;
    @Autowired
    private EventPlanConfigService eventPlanConfigService;
    @Autowired
    private EventRepoService eventRepoService;
    @Autowired
    private ChargeConfigRepoService chargeConfigRepoService;
    @Autowired
    private OrganizerRepoService organizerRepoService;
    @Autowired
    private ChargebeePlanRepoServiceImpl chargebeePlanRepoService;
    @Autowired
    private TicketingRepository ticketingRepository;
    @Autowired
    private MultiLanguageLabelRepo multiLanguageLabelRepo;
    @Autowired
    private MultiLanguageLabelService multiLanguageLabelService;
    @Autowired
    private ExhibitorSettingsService exhibitorSettingsService;

    @Override
    @Transactional
    public void associatePlanWithEvent(Long id, Long organizerId, ChargebeeObjectType objectType, Event event) {
        validateEventFormatAndEventListingStatus(event);
        if (ChargebeeObjectType.PLAN.equals(objectType)) {
            String planName = chargebeePlanRepoService.findByIdOrThrowError(id).getPlanName();
            releaseOldChargePurchaseId(event.getEventId());

            chargebeePlanRepoService.findByIdOrThrowError(id);
            if (PlanConfigNames.LEGACY.getName().equals(planName) || PlanConfigNames.WHITE_LABEL_LEGACY.getName().equals(planName)) {
                Optional<Ticketing> ticketing = ticketingRepository.findById(event.getTicketingId());
                if (ticketing.isPresent()) {
                    Calendar calendar = new GregorianCalendar();
                    calendar.setTime(ticketing.get().getEventStartDate());
                    if (calendar.get(Calendar.YEAR) > 2021) {
                        throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CAN_NO_ACTIVATE_LEGACY_AFTER_2021);
                    }
                }
            }
            eventPlanConfigService.handleCreateOrUpdatePlanForEvent(event);
            if (!IN_PERSON.equals(event.getEventFormat())
                    && PlanConfigNames.SCALE_PLAN.getName().equals(planName)
                    && BUSINESS_PLAN_2023.getName().equalsIgnoreCase(planName)) {
                exhibitorSettingsService.updatePremiumLabelSettings(new ExpoPremiumLabelSettingsDto(), event);
            }
        } else if (ChargebeeObjectType.CHARGE.equals(objectType)) {
            EventPlanConfig oldEventPlanConfig = eventPlanConfigService.findByEventId(event.getEventId());
            String oldPlatFromConfigJson = oldEventPlanConfig.getPlatformConfigJson();
            ChargeConfig chargeConfig = chargeConfigRepoService.findByIdThrowException(id);
            Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
            List<ChargesPurchased> unusedChargesOfParticularType =
                    chargesPurchasedRepoService.findByOrganizerAndChargeConfig(organizer, chargeConfig);

            raiseErrorIfDoesNotHaveEnoughPlanQty(unusedChargesOfParticularType);

            releaseOldChargePurchaseId(event.getEventId());

            EventChargeUsages eventChargeUsages = prepareChargeUseRecord(organizerId, event.getEventId(), null, chargeConfig);
            eventChargeUsagesRepoService.save(eventChargeUsages);

            EventPlanConfig newEventPlanConfig = eventPlanConfigService.associateNewChargePlanToEvent(event, chargeConfig);
            eventPlanConfigService.save(newEventPlanConfig);

            if (null != newEventPlanConfig) {
                updateTicketingData(event, PlatformConfigDto.convertJSONToObject(newEventPlanConfig.getPlatformConfigJson()), PlatformConfigDto.convertJSONToObject(oldPlatFromConfigJson));
                updateVirtualEventHubCustomLabelName(event, newEventPlanConfig.getPlanConfig(), oldPlatFromConfigJson);
            }
        }
    }

    private void updateTicketingData(Event event,PlatformConfigDto newPlatformConfigDto,PlatformConfigDto oldPlatformConfigDto){

        if(newPlatformConfigDto != oldPlatformConfigDto && (!newPlatformConfigDto.getPostEventAccessDay().equals(oldPlatformConfigDto.getPostEventAccessDay()) ||
                !newPlatformConfigDto.getPreEventAccess().equals(oldPlatformConfigDto.getPreEventAccess()))){
            Ticketing ticketing = ticketingRepository.findByEventId(event.getEventId());
            if(null != ticketing){
                ticketing.setPostEventAccessMinutes((int) DAYS.toMinutes(Long.parseLong(newPlatformConfigDto.getPostEventAccessDay())));
                ticketing.setPreEventAccessMinutes((int) DAYS.toMinutes(Long.parseLong(newPlatformConfigDto.getPreEventAccess())));
                ticketingRepository.save(ticketing);
            }
        }
    }

    private void raiseErrorIfDoesNotHaveEnoughPlanQty(List<ChargesPurchased> unusedChargesOfParticularType) {
        if(unusedChargesOfParticularType.isEmpty()){
            log.info("Does not found available plan quantity");
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_ENOUGH_QUANTITY_TO_LINK_WITH_EVENT);
        }
    }

    private void validateEventFormatAndEventListingStatus(Event event) {
        if (CommonUtil.getLiveEventStatuses().contains(event.getEventListingStatus())) {
            log.info("Cannot associate plan after event published");
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CANNOT_ASSOCIATE_CHARGE_AFTER_PUBLISH_EVENT);
        }
    }

    private EventChargeUsages prepareChargeUseRecord(Long organizerId, Long eventId, ChargesPurchased chargesPurchased,
                                                     ChargeConfig chargeConfig) {
        EventChargeUsages eventChargeUsages = new EventChargeUsages();
        eventChargeUsages.setEventId(eventId);
        eventChargeUsages.setOrganizerId(organizerId);
        eventChargeUsages.setQty(1);
        eventChargeUsages.setChargeConfig(chargeConfig);
        eventChargeUsages.setChargesPurchased(chargesPurchased);
        eventChargeUsages.setRecordStatus(RecordStatus.CREATE);
        return eventChargeUsages;
    }

    private void releaseOldChargePurchaseId(Long eventId) {
        Optional<EventChargeUsages> eventChargeUsagesOptional = eventChargeUsagesRepoService.findByEventId(eventId);

        if (eventChargeUsagesOptional.isPresent()) {
            EventChargeUsages eventChargeUsages = eventChargeUsagesOptional.get();
            eventChargeUsages.setRecordStatus(RecordStatus.DELETE);
            eventChargeUsagesRepoService.save(eventChargeUsages);
        }
    }

    @Override
    public Optional<EventChargeUsages> findByOrganizerIdAndEventId(Long organizerId, Long eventId) {
        return eventChargeUsagesRepoService.findByOrganizerIdAndEventId(organizerId, eventId);
    }

    @Override
    public void addOrUpdateEventChargeUsages(Long organizerId, Long eventId, ChargesPurchased chargesPurchased) {
        Optional<EventChargeUsages> eventChargeUsagesOptional =
                eventChargeUsagesRepoService.findByOrganizerIdAndEventId(organizerId, eventId);
        EventChargeUsages eventChargeUsages = null;
        if (eventChargeUsagesOptional.isPresent()) {
            eventChargeUsages = eventChargeUsagesOptional.get();
            eventChargeUsages.setChargesPurchased(chargesPurchased);
        } else {
            eventChargeUsages = prepareChargeUseRecord(organizerId, eventId, chargesPurchased, chargesPurchased.getChargeConfig());
        }
        eventChargeUsagesRepoService.save(eventChargeUsages);
    }

    public void updateVirtualEventHubCustomLabelName(Event event, PlanConfig newPlanConfig ,String oldPlatFromConfigJson ) {
        PlatformConfigDto oldPlatformConfigDto = PlatformConfigDto.convertJSONToObject(oldPlatFromConfigJson);
        PlatformConfigDto newPlatformConfigDto = PlatformConfigDto.convertJSONToObject(newPlanConfig.getPlatformConfigJson());

        if (newPlatformConfigDto != oldPlatformConfigDto && oldPlatformConfigDto.getNavLabels().equals("custom") && newPlatformConfigDto.getNavLabels().equals("Standardized")) {
            List<MultiLanguageLabel> multiLanguageLabelsList = new ArrayList<>();
            Iterable<MultiLanguageLabel> multiLanguageLabel = multiLanguageLabelRepo.getMultiLanguageLableByEventIdAndTypeAndLanguage(event.getEventId(), EnumMultiLanguageLabelType.VIRTUAL_EVENT_HUB);
            for (MultiLanguageLabel languageLabel : multiLanguageLabel) {
                if (!languageLabel.getCustomLabel().equals(languageLabel.getDefaultLabel())) {
                    log.info("reset custom label while updating plan "+languageLabel.getCustomLabel());
                    languageLabel.setCustomLabel(languageLabel.getDefaultLabel());
                    multiLanguageLabelsList.add(languageLabel);
                }
            }
            if (!multiLanguageLabelsList.isEmpty()){
                multiLanguageLabelService.saveAllMultiLabel(multiLanguageLabelsList);
            }
        }
        if(!IN_PERSON.equals(event.getEventFormat()) && (PlanConfigNames.STARTER.getName().equals(newPlanConfig.getPlanName())
                || PlanConfigNames.SINGLE_EVENT_PLAN_STARTER.getName().equalsIgnoreCase(newPlanConfig.getPlanName())
                || PlanConfigNames.FREE_PLAN.getName().equals(newPlanConfig.getPlanName())
                || PlanConfigNames.SCALE_PLAN.getName().equals(newPlanConfig.getPlanName()))
                || BUSINESS_PLAN_2023.getName().equalsIgnoreCase(newPlanConfig.getPlanName())){
            exhibitorSettingsService.updatePremiumLabelSettings(new ExpoPremiumLabelSettingsDto(), event);
        }
    }
}
