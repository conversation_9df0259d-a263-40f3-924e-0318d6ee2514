package com.accelevents.billing.chargebee.repositories;

import com.accelevents.domain.EventPlanConfig;
import com.accelevents.domain.Organizer;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EventPlanConfigRepository extends CrudRepository<EventPlanConfig,Long> {

    Optional<EventPlanConfig> findById(@Param("id") Long id);

    @Cacheable(value = "findEventPlanConfigByEventId", key = "#p0", condition="#p0!=null", unless="#result == null")
    Optional<EventPlanConfig> findByEventId(@Param("eventId") Long eventId);
    Optional<EventPlanConfig> findByChargebeePlanId(@Param("chargebeePlanId") String chargebeePlanId);

    EventPlanConfig findByOrganizer(Organizer organizer);

    @Query("SELECT config FROM EventPlanConfig config WHERE config.id BETWEEN :from AND :to ")
    List<EventPlanConfig> findAllEventPlanConfigsBetween(@Param("from") long from, @Param("to") long to);

    @Query("SELECT config.event.eventId FROM EventPlanConfig config " +
            " WHERE config.event.eventId IN (:eventIds) " +
            " AND config.autoDisableUnAuthLiveStream = true" +
            " AND config.planConfigId IN (:planConfigIds) ")
    List<Long> findByEventIdsNotAllowedStreamAndOnFreePlan(@Param("eventIds") List<Long> eventIds, @Param("planConfigIds") List<Long> planConfigIds);


    @Query("SELECT config.autoDisableUnAuthLiveStream FROM EventPlanConfig config WHERE config.eventId=:eventId" )
    Boolean findByAutoLiveStreamDisableEventId(@Param("eventId") long eventId);
    @Query("SELECT config FROM EventPlanConfig config WHERE config.eventId in (:eventIds)")
    List<EventPlanConfig> findByEventIds(@Param("eventIds") List<Long> eventIds);
}
