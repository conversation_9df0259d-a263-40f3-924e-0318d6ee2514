package com.accelevents.billing.chargebee.repo;

import com.accelevents.domain.Organizer;
import com.accelevents.enums.BillingType;

import java.math.BigInteger;
import java.util.List;

public interface OrganizerRepoService {
    Organizer findByIdThrowException(Long organizerId);

    List<Organizer> findAllOrganizer(long from, long to);

    List<Organizer> findAllOrganizersBetween(long from, long to);

    void saveAll(List<Organizer> organizers);

    void save(Organizer organizer);

    List<Organizer> findBySubscriptionId(String subscriptionId);

    List<Organizer> findBySubscriptionIds(List<String> subscriptionIds);

    List<Organizer> findByChargebeeCustomerId(String customerId);

    List<BigInteger> findOrganizerWhichDoesNotHaveOwner();

    BillingType findBillingTypeByOrganizerId(Long organizerId);

    List<Organizer> findAllOrganizerByPlanId(Long from, Long to, long planId);

    List<Organizer> findAllOrganizerWithHubspotCompanyId(long from, long to);

    List<Organizer> findAllOrganiserWhoHaveOldPlan(long startId, long endId);

    List<Organizer> findAllOrganisersBySubscriptionIdIsNotNullAndNotHavingVirtualComponentEntitlement(long startId, long endId);

    List<Organizer> findAllOrganisersBySubscriptionIdIsNotNull(long startId, long endId);

    List<Organizer> findAllOrganiserWhoHavePlan(long from, long to);

    List<Organizer> findOrganizerByWhiteLabel(long whiteLabelId);

    Organizer findByOrganizerId(Long organizerId);

    List<Organizer> findPaidOrganizersByIdRange(long startId, long endId);

    List<Organizer> findOrganisersByWhiteLabelIds(List<Long> whiteLabelIds);
}
