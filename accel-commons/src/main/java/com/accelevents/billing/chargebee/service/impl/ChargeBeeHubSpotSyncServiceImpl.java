package com.accelevents.billing.chargebee.service.impl;

import com.accelevents.billing.chargebee.repo.OrganizerRepoService;
import com.accelevents.billing.chargebee.service.ChargeBeeHubSpotSyncService;
import com.accelevents.billing.chargebee.service.ChargebeePaymentService;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.hubspot.dto.Contacts;
import com.accelevents.hubspot.dto.HubSpotCompanyDTO;
import com.accelevents.hubspot.dto.HubspotObjectSearchResponseContainer;
import com.accelevents.hubspot.service.HubspotCompanyService;
import com.accelevents.hubspot.service.HubspotContactService;
import com.accelevents.repositories.JoinUsersWithOrganizersRepository;
import com.accelevents.repositories.OrganizerRepository;
import com.accelevents.ro.hubspot.ROHubspotContactService;
import com.accelevents.services.repo.helper.WhiteLabelRepoService;
import com.accelevents.utils.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChargeBeeHubSpotSyncServiceImpl implements ChargeBeeHubSpotSyncService {

    private final Logger logger = LoggerFactory.getLogger(ChargeBeeHubSpotSyncServiceImpl.class);
    @Autowired
    private OrganizerRepoService organizerRepoService;
    @Autowired
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
    @Autowired
    private WhiteLabelRepoService whiteLabelRepoService;
    @Autowired
    private HubspotContactService hubspotContactService;
    @Autowired
    private ROHubspotContactService roHubspotContactService;
    @Autowired
    private OrganizerRepository organizerRepository;
    @Autowired
    private HubspotCompanyService hubspotCompanyService;
    @Autowired
    private ChargebeePaymentService chargebeePaymentService;
    @Value("${application.environment}")
    private String environment;

    @Override
    public boolean checkHubSpotCompanyIsPresent(User loggedInUser, Long organizerId, Long whiteLabelId) {
        if (!Constants.ENV_PROD.equals(environment)) {
            return true;
        }
        User billingContact = null;
        if (whiteLabelId != null) {
            WhiteLabel whiteLabel = whiteLabelRepoService.findByIdThrowException(whiteLabelId);
            billingContact = getBillingContact(loggedInUser, whiteLabel);
        } else if (organizerId != null) {
            Organizer organizer = organizerRepoService.findByIdThrowException(organizerId);
            billingContact = getBillingContact(loggedInUser, organizer);
        }
        if (billingContact != null) {
            HubspotObjectSearchResponseContainer data = roHubspotContactService.searchHubspotContacts(billingContact.getEmail());
            if (data == null || data.getResults() == null || data.getResults().isEmpty()) {
                return false;
            } else {
                List<Contacts> result = data.getResults();
                Contacts contact = result.get(0);
                List<String> list = hubspotContactService.findAllAssociationsWithContactId(contact.getId(), 0);
                return !list.isEmpty();
            }
        }
        return false;
    }

    private User getBillingContact(User loggedInUser, WhiteLabel whiteLabel) {
        List<User> billingContactByUser = joinUsersWithOrganizersRepository.findBillingContactByWLId(whiteLabel.getId());
        return !billingContactByUser.isEmpty() ? billingContactByUser.get(0) : loggedInUser;
    }

    private User getBillingContact(User loggedInUser, Organizer organizer) {
        List<User> billingContactByUser = joinUsersWithOrganizersRepository.findBillingContactByOrgId(organizer.getId());
        return !billingContactByUser.isEmpty() ? billingContactByUser.get(0) : loggedInUser;
    }

    @Override
    @Async
    public void createHubSpotCompanyIdAndUpdateChargeBeeCustomer(HubSpotCompanyDTO hubSpotCompanyDTO, Organizer organizer) {
        logger.info("createHubSpotCompanyIdAndUpdateChargeBeeCustomer hubSpotCompanyName {} hubSpotCompanyWebsiteDomain {} organiserId {}", hubSpotCompanyDTO.getName(), hubSpotCompanyDTO.getDomain(), organizer.getId());
        if (null != hubSpotCompanyDTO.getName() && null != hubSpotCompanyDTO.getDomain()) {
            String hubSpotCompanyId = hubspotCompanyService.createHubSpotCompany(hubSpotCompanyDTO,1);
            logger.info("createHubSpotCompanyIdAndUpdateChargeBeeCustomer organiserId {} hubSpotCompanyId {}", organizer.getId(), hubSpotCompanyId);
            HubspotObjectSearchResponseContainer contactInfo = roHubspotContactService.searchHubspotContacts(organizer.getContactEmailAddress());
            if (null != hubSpotCompanyId) {
                Organizer organizerFromDb = organizerRepository.findById(organizer.getId()).orElse(null);
                if(organizerFromDb != null) {
                    organizerFromDb.setHubSpotCompanyId(Long.parseLong(hubSpotCompanyId));
                    organizerRepository.save(organizerFromDb);
                    List<Contacts> results = contactInfo.getResults();
                    if (!results.isEmpty()) {
                        logger.info("createHubSpotCompanyIdAndUpdateChargeBeeCustomer organiserId {} contactId {}", organizer.getId(), contactInfo.getResults().get(0).getId());
                        hubspotCompanyService.createCompanyContactAssociation(contactInfo.getResults().get(0).getId(), hubSpotCompanyId, 1);
                    }
                    chargebeePaymentService.setHubSpotCompanyId(organizerFromDb.getChargebeeCustomerId(), hubSpotCompanyId);
                }
            }
        }
    }
}
