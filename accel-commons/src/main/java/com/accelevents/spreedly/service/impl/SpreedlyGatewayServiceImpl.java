package com.accelevents.spreedly.service.impl;

import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.CustomerCardDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.dto.RefundInfoDto;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.exceptions.ThirdPartyExceptions;
import com.accelevents.helpers.StripeUtil;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.ro.payment.ROPayFlowConfigService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.spreedly.dto.SpreedlyGatewayType;
import com.accelevents.spreedly.dto.SpreedlyIframeDTO;
import com.accelevents.spreedly.dto.gateways.*;
import com.accelevents.spreedly.dto.transactions.TransactionRequestDTO;
import com.accelevents.spreedly.dto.transactions.response.GatewayResponseDTO;
import com.accelevents.spreedly.dto.transactions.response.TransactionDataDTO;
import com.accelevents.spreedly.dto.transactions.response.TransactionResponseDTO;
import com.accelevents.spreedly.service.SpreedlyApiExecutionService;
import com.accelevents.spreedly.service.SpreedlyGatewayAsyncTaskService;
import com.accelevents.spreedly.service.SpreedlyGatewayService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.JsonMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.enums.PlanConfigNames.FREE_PLAN;
import static com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG.WL_CONNECTED_TO_OTHER_PAYMENT_GATEWAY;
import static com.accelevents.exceptions.NotAcceptableException.PaymentCreationExceptionMsg.EVENT_CONNECTED_TO_PAYMENT_GATEWAY;
import static com.accelevents.spreedly.dto.SpreedlyGatewayType.SP_AUTHORIZE_NET;
import static com.accelevents.spreedly.dto.SpreedlyGatewayType.SP_BRAINTREE;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.Constants.Spreedly.*;
import static com.accelevents.utils.Constants.Spreedly.CUSTOMER_ID;

@Service
public class SpreedlyGatewayServiceImpl implements SpreedlyGatewayService {

    private static final Logger log = LoggerFactory.getLogger(SpreedlyGatewayServiceImpl.class);

    private static final TypeReference<List<PaymentGatewayDTO>> paymentGatewayTypeRef = new TypeReference<>() {};
    private Map<String, PaymentGatewayDTO> spreedlyGatewayMap = new HashMap<>();
    private static final List<String> AUTHORIZE_NET_CARD_DECLINE_ERROR_CODE = List.of("2","3");

    private final String appProfile;
    private final String spreedlyHostUrl;
    private final String spreedlyEnvkey;
    private final String spreedlyPrivateKeyPath;
    private String spreedlyPrivateKey;
    private final String spreedlyCertificateToken;
    private final StripeService stripeService;
    private final ROStripeService roStripeService;
    private final ROUserService roUserService;
    private final StaffService staffService;
    private final ROStaffService roStaffService;
    private final EventRepoService eventRepoService;
    private final EventPlanConfigService eventPlanConfigService;
    private final EventService eventService;
    private final ROPayFlowConfigService roPayFlowConfigService;
    private final SpreedlyGatewayAsyncTaskService spreedlyGatewayAsyncTaskService;
    private final SpreedlyApiExecutionService spreedlyApiExecutionService;
    private final EventCommonRepoService  eventCommonRepoService;
    private final TicketHolderAttributesService ticketHolderAttributesService;


    @Autowired
    public SpreedlyGatewayServiceImpl(@Value("${app.profile}") String appProfile,
                                      @Value("${spreedly.host.url}") String spreedlyHostUrl,
                                      @Value("${spreedly.environment.key}") String spreedlyEnvkey,
                                      @Value("${spreedly.private.key}") String spreedlyPrivateKeyPath,
                                      @Value("${spreedly.certificate.token}") String spreedlyCertificateToken,
                                      StripeService stripeService, ROStripeService roStripeService,
                                      ROUserService roUserService, StaffService staffService,
                                      ROStaffService roStaffService, EventRepoService eventRepoService,
                                      EventPlanConfigService eventPlanConfigService, EventService eventService,
                                      ROPayFlowConfigService roPayFlowConfigService, SpreedlyGatewayAsyncTaskService spreedlyGatewayAsyncTaskService,
                                      SpreedlyApiExecutionService spreedlyApiExecutionService,
                                      EventCommonRepoService eventCommonRepoService,TicketHolderAttributesService ticketHolderAttributesService) {

        this.appProfile = appProfile;
        this.spreedlyHostUrl = spreedlyHostUrl;
        this.spreedlyPrivateKeyPath = spreedlyPrivateKeyPath;
        this.spreedlyCertificateToken = spreedlyCertificateToken;
        this.spreedlyEnvkey= spreedlyEnvkey;
        this.stripeService = stripeService;
        this.roStripeService = roStripeService;
        this.roUserService = roUserService;
        this.staffService = staffService;
        this.roStaffService = roStaffService;
        this.eventRepoService = eventRepoService;
        this.eventPlanConfigService = eventPlanConfigService;
        this.eventService = eventService;
        this.roPayFlowConfigService = roPayFlowConfigService;
        this.spreedlyGatewayAsyncTaskService = spreedlyGatewayAsyncTaskService;
        this.spreedlyApiExecutionService = spreedlyApiExecutionService;
        this.eventCommonRepoService = eventCommonRepoService;
        this.ticketHolderAttributesService = ticketHolderAttributesService;
    }

    @PostConstruct
    public void init() {
        this.storeSpreedlyGatewayOptionsIntoMap();
        this.getSpreedlyPrivateKey();
    }

    private void storeSpreedlyGatewayOptionsIntoMap() {
        log.info("storeSpreedlyGatewayOptionsIntoMap start the process time {}", System.currentTimeMillis());
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest.get( spreedlyHostUrl + "gateways_options.json").asJson();
            if (GeneralUtils.is2xxSuccess(jsonResponse.getStatus())) {
                JsonNode responseBody = jsonResponse.getBody();
                if (responseBody.getObject().has("gateways")) {
                    List<PaymentGatewayDTO> gatewayDTOList = JsonMapper.stringToObjectWithTypeReference(responseBody.getObject().getJSONArray("gateways").toString(),  paymentGatewayTypeRef);
                    if(!CollectionUtils.isEmpty(gatewayDTOList)) {
                        spreedlyGatewayMap = gatewayDTOList.stream().collect(Collectors.toMap(PaymentGatewayDTO::getGatewayType, value -> value));
                        spreedlyGatewayMap.get(SP_BRAINTREE.getValue()).getAuthModes().removeIf(e->"orange".equals(e.getAuthModeType())); // orange mode is not support by Spreedly
                        log.info("storeSpreedlyGatewayOptionsIntoMap successfully store the response in the map, end time {}", System.currentTimeMillis());
                    }
                }
            } else {
                log.info("storeSpreedlyGatewayOptionsIntoMap getting error from Spreedly, responseStatus: {} responseBody {}", jsonResponse.getStatus(), jsonResponse.getBody());
            }
        } catch (Exception e) {
            log.error("Exception during storeSpreedlyGatewayOptionsIntoMap errorMessage {}", e.getMessage(), e);
        }
    }
    private void getSpreedlyPrivateKey(){
        try {
            ClassPathResource cpr = new ClassPathResource(this.spreedlyPrivateKeyPath);
            Base64.Decoder decoder = Base64.getDecoder();

            byte[] xmlBytes = readString(cpr.getInputStream()).getBytes(StandardCharsets.UTF_8);
            String xmlBase64 = Base64.getEncoder().encodeToString(xmlBytes);
            // Decoding string
            String privateKeyDecoded = new String(decoder.decode(xmlBase64));

             this.spreedlyPrivateKey = privateKeyDecoded
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll(Constants.ATOZ_BOTHCASE_AND_NUMBERS_AND_PLUS_AND_FORWARDSLASH_AND_EQUAL_NOT_REMOVE, "");
            log.info("getSpreedlyPrivateKey Successfully retrieved Spreedly key from path {}",this.spreedlyPrivateKeyPath);
        } catch (Exception e){
            log.error("Exception during generation of spreedly key errorMessage {}", e.getMessage(), e);
            this.spreedlyPrivateKey = null;
        }
    }

    // Get Required Auth mode field based on the payment gateway
    @Override
    public List<AuthMode> getPaymentGatewayAuthModeField(String paymentGateway) {
        SpreedlyGatewayType spreedlyGatewayType = SpreedlyGatewayType.valueOf(paymentGateway);
        if(CollectionUtils.isEmpty(spreedlyGatewayMap)) {
            storeSpreedlyGatewayOptionsIntoMap();
        }
        PaymentGatewayDTO paymentGatewayDTO = spreedlyGatewayMap.get(spreedlyGatewayType.getValue());
        if(paymentGatewayDTO == null) {
            throw new NotFoundException(NotFoundException.NotFound.PAYMENT_GATEWAY_AUTHMODE_NOT_FOUND);
        }
        return paymentGatewayDTO.getAuthModes();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
    public void createSpreedlyPaymentGatewayForWL(WhiteLabel whiteLabel, User user, GatewayRequestDto gatewayRequestDto ) {
        Staff wlStaff = roStaffService.findWhiteLabelAdminStaff(user, whiteLabel)
                .orElseThrow(()->new ForbiddenException(ForbiddenException.UserForbiddenExceptionMsg.NOT_WHITELABEL_ADMIN));
        this.validatePlanAndPaymentGatewayConnectedToWL(whiteLabel);
        this.createPaymentGatewayOnSpreedly(gatewayRequestDto, wlStaff, null, whiteLabel);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class}, isolation = Isolation.READ_COMMITTED)
    public void createSpreedlyPaymentGatewayForEvent(Event event, User user, GatewayRequestDto gatewayRequestDto)  {
        this.validatePlanAndPaymentGatewayConnectedToEvent(event, user);
        Staff staff = Optional.ofNullable(roStaffService.findByEventAndUserNotExhibitor(event, user, false))
                .orElse(staffService.findAdminAndStaffByEvent(event).get(0));
        this.createPaymentGatewayOnSpreedly(gatewayRequestDto, staff, event, null);
    }

    // Get the required credential with payment gateway and we add this gatway in to Spreedly and store the gatewayToken in our Stripe table for this event or wl
    private void createPaymentGatewayOnSpreedly(GatewayRequestDto gatewayRequestDto, Staff staff, Event event, WhiteLabel whiteLabel) {
        long eventOrWlId = 0L;
        try {
            SpreedlyGatewayType gatewayType = gatewayRequestDto.getGatewayType();
            CredentialRequestDto credentialRequestDto = gatewayRequestDto.getGatewayData();

            String gatewayDesc = STRING_EMPTY;
            long userId = staff.getUserId();
            String currency = Currency.USD.getISOCode();
            if (whiteLabel != null) {
                eventOrWlId = whiteLabel.getId();
                gatewayDesc = String.format("Gateway created from whiteLabel_%s_WL_URL_%s", eventOrWlId, whiteLabel.getWhiteLabelUrl());
            } else if (event != null) {
                eventOrWlId = event.getEventId();
                gatewayDesc = String.format("Gateway created from eventId_%s_EVENT_URL_%s", eventOrWlId, event.getEventURL());
                currency = event.getCurrency().getISOCode();
            }

            Map<String, Object> credentialMap = new HashMap<>();
            credentialMap.put(GATEWAY_TYPE, gatewayRequestDto.getGatewayType().getValue());
            credentialMap.put(SANDBOX.toLowerCase(), !ENV_PROD.equalsIgnoreCase(appProfile));

            credentialMap.put(DESCRIPTION.toLowerCase(), gatewayDesc);
            credentialMap.put(MODE, credentialRequestDto.getAuthModeType());
            credentialMap.putAll(credentialRequestDto.getCredentials());

            Map<String, Object> paymentGatewayRequestMap = new HashMap<>();
            paymentGatewayRequestMap.put(GATEWAY_LOWER, credentialMap);

            HttpResponse<JsonNode> gatewayResponse = spreedlyApiExecutionService.executeCreateGatewayConnectionApi(paymentGatewayRequestMap);

            JSONObject responseBody = gatewayResponse.getBody().getObject();
            if (GeneralUtils.is2xxSuccess(gatewayResponse.getStatus())) {
                JSONObject gatewayBody = gatewayResponse.getBody().getObject().getJSONObject(GATEWAY_LOWER);
                String gatewayToken = gatewayBody.getString(TOKEN_LOWER);
                this.verifyPaymentGatewayConnection(eventOrWlId, staff.getUserId(), gatewayType, gatewayToken, currency);
                if (null != whiteLabel) {
                    this.saveSpreedlyGatewayForWL(whiteLabel, staff, gatewayType, gatewayToken, gatewayBody);
                } else {
                    this.saveSpreedlyGatewayForEvent(event, staff, gatewayType, gatewayToken, gatewayBody);
                }
                log.info("createPaymentGatewayOnSpreedly, Payment gateway successfully connected and saved for eventOrWlId {} user {} gatewayType {}", eventOrWlId, userId, gatewayType);
            } else {
                log.info("createPaymentGatewayOnSpreedly, Error from Spreedly for eventOrWlId {} user {} response {}", eventOrWlId, userId, gatewayResponse.getBody());
                String errorMessage = "Error while connecting payment gateway.";
                errorMessage = getErrorMessage(responseBody, errorMessage);
                throw new NotAcceptableException(new Exception(errorMessage));
            }

        } catch (UnirestException | JSONException e) {
            log.error("Exception during the creating the Spreedly gateway eventOrWlId {} user {} gateway {} error", eventOrWlId, staff.getUserId(), gatewayRequestDto.getGatewayType(), e);
        }
    }



    private void validatePlanAndPaymentGatewayConnectedToWL(WhiteLabel whiteLabel) {
        String eventPlanName = whiteLabel.getPlanConfig().getPlanName();
        if(StringUtils.isBlank(eventPlanName) || FREE_PLAN.getName().equals(eventPlanName)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CURRENT_PLAN_NOT_ALLOWED_ACCESS_FEATURE);
        }

        Stripe wlConnectedGateway = stripeService.findByWhiteLabelAndDefaultAccount(whiteLabel, true);
        if(wlConnectedGateway != null ||  roPayFlowConfigService.isPayFlowConnected(whiteLabel)) {
            throw new NotAcceptableException(WL_CONNECTED_TO_OTHER_PAYMENT_GATEWAY);
        }
    }

    private void validatePlanAndPaymentGatewayConnectedToEvent(Event event, User user) {
        String eventPlanName = eventPlanConfigService.getEventPlanName(event.getEventId());
        if(StringUtils.isBlank(eventPlanName) || FREE_PLAN.getName().equals(eventPlanName)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.CURRENT_PLAN_NOT_ALLOWED_ACCESS_FEATURE);
        }

        if (roUserService.isSuperAdminUser(user) && !roStaffService.isEventAdmin(event, user)) {
            throw new NotAcceptableException(NotAcceptableException.NotAceptableExeceptionMSG.NOT_ALLOW_TO_PAYMENT_PROCESSING);
        }

        Stripe connectedPaymentGateway = stripeService.findByEventAndDefaultAccount(event, true);
        if(connectedPaymentGateway != null) {
            NotAcceptableException.PaymentCreationExceptionMsg paymentCreationExceptionMsg = EVENT_CONNECTED_TO_PAYMENT_GATEWAY;
            String errorMsg = Constants.EVENT_CONNECTED_TO_PAYMENT_GATEWAY
                    .replace("${PAYMENT_GATEWAY}", connectedPaymentGateway.getPaymentGateway());
            paymentCreationExceptionMsg.setErrorMessage(errorMsg);
            paymentCreationExceptionMsg.setDeveloperMessage(errorMsg);
            throw new NotAcceptableException(paymentCreationExceptionMsg);
        }
    }

    private void verifyPaymentGatewayConnection(Long eventOrWlId, Long userId, SpreedlyGatewayType spreedlyGatewayType, String gatewayToken, String currency) throws UnirestException {

        log.info("verifyPaymentGatewayConnection process start for eventOrWlId {} by userId {} gatewayType {} ", eventOrWlId, userId, spreedlyGatewayType);
        currency = StringUtils.isBlank(currency) ? Currency.USD.getISOCode() :  currency;
        TransactionRequestDTO transactionRequestDTO = new TransactionRequestDTO()
                .withPaymentMethodToken(this.getDummyPaymentMethodToken(eventOrWlId, userId))
                .withAmount(GeneralUtils.convertToSmallestCurrencyUnit(1, currency))
                .withDescription("VerifyConnectionOnAccelevents")
                .withCurrencyCode(currency)
                .withRetainOnSuccess(false);

        this.setGatewaySpecificFieldToTransactionRequest(spreedlyGatewayType, transactionRequestDTO, STRING_EMPTY);

        Map<String, TransactionRequestDTO> purchaseRequestDto = new HashMap<>();
        purchaseRequestDto.put(TRANSACTION, transactionRequestDTO);

        HttpResponse<JsonNode> gatewayResponse = spreedlyApiExecutionService.executePurchaseApi(gatewayToken, purchaseRequestDto);

        JsonNode responseBody = gatewayResponse.getBody();
        if(GeneralUtils.is2xxSuccess(gatewayResponse.getStatus())) {
            // execution go in this block when we do transaction on sandbox account with testing data and have valid gateway credential
            log.info("verifyPaymentGatewayConnection, payment gateway verified for eventOrWlId {} user {} gatewayType {}", eventOrWlId, userId, spreedlyGatewayType);
        } else {
            log.info("verifyPaymentGatewayConnection, Error from Spreedly for transaction eventOrWlId {}, user {} gatewayType {}", eventOrWlId, userId, spreedlyGatewayType);
            JSONObject responseObj = responseBody.getObject();
            if(responseObj.has(TRANSACTION)){
                TransactionResponseDTO transactionResponseDTO = JsonMapper.stringtoObject(responseObj.toString(), TransactionResponseDTO.class);
                if(null != transactionResponseDTO && null != transactionResponseDTO.getTransaction()) {
                    TransactionDataDTO transactionDataDTO = transactionResponseDTO.getTransaction() ;
                    if(transactionDataDTO.getResponse() != null && this.isValidGatewayCredential(transactionDataDTO.getResponse(), spreedlyGatewayType)) {
                        log.info("verifyPaymentGatewayConnection, payment gateway verified for eventOrWlId {} user {} gatewayType {} in live mode gateway", eventOrWlId, userId, spreedlyGatewayType);
                        return;
                    }
                    log.info("verifyPaymentGatewayConnection, Error from Spreedly for transaction eventOrWlId {}, user {} gatewayType {} errorMessage {}", eventOrWlId, userId, spreedlyGatewayType, transactionResponseDTO.getTransaction().getMessage());
                    this.disablePaymentGatewayOnSpreedly(eventOrWlId, userId, spreedlyGatewayType, gatewayToken);
                    throw  new NotAcceptableException(new Exception(transactionDataDTO.getMessage()));
                }
            }
            log.info("verifyPaymentGatewayConnection, Error from Spreedly for transaction event {}, user {} gatewayType {} response {}", eventOrWlId, userId, spreedlyGatewayType, responseBody);
            this.disablePaymentGatewayOnSpreedly(eventOrWlId, userId, spreedlyGatewayType, gatewayToken);
            throw new NotAcceptableException(new Exception("Error while connecting payment gateway."));
        }
    }

    private void disablePaymentGatewayOnSpreedly(Long eventOrWlId, Long userId, SpreedlyGatewayType spreedlyGatewayType, String gatewayToken) throws UnirestException {
        log.info("disablePaymentGatewayOnSpreedly, process start for eventOrWlId {} userId {}, gatewayType {}", eventOrWlId, userId, spreedlyGatewayType);
        HttpResponse<String> gatewayResponse = spreedlyApiExecutionService.executeRedactGatewayConnectionApi(gatewayToken);
        if(GeneralUtils.is2xxSuccess(gatewayResponse.getStatus())) {
            log.info("disablePaymentGatewayOnSpreedly process completed for eventOrWlId {} userId {}, gatewayType {}", eventOrWlId, userId, spreedlyGatewayType);
        } else {
            log.info("disablePaymentGatewayOnSpreedly, Error from Spreedly for eventOrWlId {} userId {}, gatewayType {} response {}", eventOrWlId, userId, spreedlyGatewayType, gatewayResponse.getBody());
        }

    }

    private String getDummyPaymentMethodToken(Long eventOrWlId, Long userId) throws UnirestException {
        Calendar currentYear = Calendar.getInstance();
        currentYear.add(Calendar.YEAR, 5);
        String pmRequestData = "{\n" +
                "    \"environment_key\":\""+ spreedlyEnvkey +"\",\n" +
                "    \"payment_method\": {\n" +
                "        \"credit_card\": {\n" +
                "        \"first_name\": \"Verify Accelevents gateway\",\n" +
                "        \"last_name\": \"\",\n" +
                "        \"number\": \"****************\",\n" +
                "        \"verification_value\": \"222\",\n" +
                "        \"month\": \"12\",\n" +
                "        \"year\": \""+ currentYear.get(Calendar.YEAR) +"\"\n" +
                "        },\n" +
                "        \"retained\": false,\n" +
                "        \"allow_blank_name\":true,\n" +
                "        \"metadata\": {\n" +
                "        \"description\": \"Verify Accelevents gateway\"\n" +
                "        }\n" +
                "    }\n" +
                "}";
        try {
            HttpResponse<JsonNode> pmResponse = spreedlyApiExecutionService.executeCreatePaymentMethodTokenApi(pmRequestData);
            if (GeneralUtils.is2xxSuccess(pmResponse.getStatus())) {
                log.info("getDummyPaymentMethodToken payment method token generated for eventOrWlId {} by userId {} ", eventOrWlId, userId);
                return pmResponse.getBody().getObject().getJSONObject(TRANSACTION).getJSONObject(PAYMENT_METHOD).getString(TOKEN_LOWER);
            }
        } catch (JSONException e ) {
            log.info("getDummyPaymentMethodToken, Exception for eventOrWlId {} by userId {} errorMessage {} ", eventOrWlId, userId, e.getMessage(), e);
        }
        return STRING_EMPTY;
    }

    /*
     * When we new payment gateway type then we need to add condition here to add gateway specific field
     */
    private void setGatewaySpecificFieldToTransactionRequest(SpreedlyGatewayType spreedlyGatewayType, TransactionRequestDTO transactionRequestDTO, String customerId) {
        if(SP_BRAINTREE.equals(spreedlyGatewayType)) {
            GatewaySpecificField braintreeGatewayFieldDTO = new BraintreeGatewayFieldDTO()
                    .withCustomerId(customerId)
                    .withDescriptorName(transactionRequestDTO.getDescription());
            transactionRequestDTO.addGatewaySpecificFieldDto(spreedlyGatewayType.getValue(), braintreeGatewayFieldDTO);
        } else if(SP_AUTHORIZE_NET.equals(spreedlyGatewayType)) {
            GatewaySpecificField authorizeNetGatewayFieldDto = new AuthorizeNetGatewayFieldDto()
                    .withCustomerProfileId(customerId)
                    .withCustomerId(customerId);

            transactionRequestDTO.addGatewaySpecificFieldDto(spreedlyGatewayType.getValue(), authorizeNetGatewayFieldDto);
        }
        // if we add more payment gateway then we need to change this if into switch case based on the gateway type
    }

    /*
     * When we new payment gateway type then we need to add condition here to add gateway specific field regarding customerId while store the payment method token
     */
    private void setGatewaySpecificFieldForPaymentMethodTokenStoreApi(SpreedlyGatewayType spreedlyGatewayType, Map<String, Object> paymentMethodMap, String customerId) {
        Map<String, Object> gatewaySpecificFieldMap = new HashMap<>();
        if(SP_BRAINTREE.equals(spreedlyGatewayType)) {
            GatewaySpecificField braintreeGatewayFieldDTO = new BraintreeGatewayFieldDTO()
                    .withCustomerId(customerId);
            gatewaySpecificFieldMap.put(spreedlyGatewayType.getValue(), braintreeGatewayFieldDTO);
        } else if(SP_AUTHORIZE_NET.equals(spreedlyGatewayType)) {
            GatewaySpecificField authorizeNetGatewayFieldDto = new AuthorizeNetGatewayFieldDto()
                    .withCustomerProfileId(customerId);
            gatewaySpecificFieldMap.put(spreedlyGatewayType.getValue(), authorizeNetGatewayFieldDto);
        }
        paymentMethodMap.put(GATEWAY_SPECIFIC_FIELDS, gatewaySpecificFieldMap);
        // if we add more payment gateway then we need to change this if into switch case based on the gateway type
    }

    /*
     * When we validate Payment gateway connection Response then we need to validate that response based on payment gateway type
     * If the Credential are valid then we get response like test card is not allowed on Prod
     */
    private boolean isValidGatewayCredential(GatewayResponseDTO gatewayResponseDTO, SpreedlyGatewayType spreedlyGatewayType) {
        if(SP_BRAINTREE.equals(spreedlyGatewayType)) {
            return "2038".equals(gatewayResponseDTO.getErrorCode()) || StringUtils.defaultString(gatewayResponseDTO.getMessage()).contains("Processor Declined");
        } else if(SP_AUTHORIZE_NET.equals(spreedlyGatewayType)) {
            return AUTHORIZE_NET_CARD_DECLINE_ERROR_CODE.contains(StringUtils.defaultString(gatewayResponseDTO.getErrorCode())) || StringUtils.defaultString(gatewayResponseDTO.getMessage()).contains("This transaction has been declined");
        }
         /*if we add more payment gateway type then we need to change this if into switch case based on the gateway type
        and check the error code and message based on that gateway type response
        */
        return false;

    }

    /**
     * This method use to check the error message based on the payment gateway and get the card details if card details are already map with given
     * CustomerId on the targeted payment processor
     *
     * @param stripe Connected Payment gateway details
     * @param customerCardDto customer card dto to hold card details
     * @param errorMessage message that we get from the Payment gateway while storing the card
     * @return basisPaymentMethod Json object of basic payment method from Store API response
     * @throws ThirdPartyExceptions throw the exception if we get other error expect the duplicate card details
     */

    private CustomerCardDto getCardDetailsIfAlreadyStoreElseThrowError(Stripe stripe, CustomerCardDto customerCardDto, String errorMessage, JSONObject basisPaymentMethod) throws JSONException {
        if(SP_AUTHORIZE_NET.name().equals(stripe.getPaymentGateway()) && AUTHORIZE_NET_DUPLICATE_CUSTOMER_PROFILE_MESSAGE.equals(errorMessage)) {
            return this.getAndSetCustomerCardBasicDetailsFromResponse(customerCardDto, basisPaymentMethod);
        }
        // BrainTree allow to store the duplicate card details with same customer, So We not need to check for that.
        throw new ThirdPartyExceptions(400, errorMessage);
    }

    private void saveSpreedlyGatewayForWL(WhiteLabel whiteLabel, Staff staff, SpreedlyGatewayType spreedlyGatewayType, String gatewayToken, JSONObject gatewayBody) throws JSONException {
        log.info("saveSpreedlyGatewayForEvent whiteLabel {} userId {} gatewayType {} date {}",whiteLabel.getId(), staff.getUserId(), spreedlyGatewayType ,new Date());
        Stripe paymentGateway = stripeService.findByWhiteLabel(whiteLabel);
        if (paymentGateway == null) {
            paymentGateway = new Stripe();
            paymentGateway.setWhiteLabelId(whiteLabel.getId());
        }
        log.info("saveSpreedlyGatewayForWL on Stripe for whitelabel id: {} by {}, gatewayType {} paymentGatewayId {}", whiteLabel.getId(), staff.getUserId(), spreedlyGatewayType, paymentGateway.getId());
        savePaymentGateway(paymentGateway, staff, null, spreedlyGatewayType, gatewayToken, gatewayBody);
        log.info("saveSpreedlyGatewayForWL on stripe successfully saved for whiteLabel {} by userId {} gatewayType {}", whiteLabel.getId(), staff.getUserId(), spreedlyGatewayType);
    }

    private void savePaymentGateway(Stripe paymentGateway, Staff staff, Event event, SpreedlyGatewayType spreedlyGatewayType, String gatewayToken, JSONObject gatewayBody) throws JSONException {
        paymentGateway.setPaymentGateway(spreedlyGatewayType.name());
        paymentGateway.setAccessToken(gatewayToken);
        paymentGateway.setTokenType(GATEWAY_TOKEN);
        paymentGateway.setStripePublishableKey(spreedlyEnvkey);
        paymentGateway.setStripeUserId(gatewayToken);
        paymentGateway.setLivemode(!gatewayBody.getBoolean(SANDBOX.toLowerCase()));
        paymentGateway.setConnectedOn(new Date());
        paymentGateway.setActivated(true);
        paymentGateway.setActivatedOn(new Date());
        paymentGateway.setDefaultAccount(true);
        paymentGateway.setAccountSource(SPREEDLY_PAYMENT_PROCESSOR);
        paymentGateway.setDefaultAccount(true);
        paymentGateway.setStaff(staff);
        paymentGateway.setRefreshToken(null);
        paymentGateway.setCode(null);
        paymentGateway.setScope(null);
        StripeUtil.applyProcessingFeeBasedSpreedlyGateway(paymentGateway, spreedlyGatewayType);
        if(event != null) {
            StripeUtil.applyProcessingFeeOnEventBasedSpreedlyGateway(paymentGateway, spreedlyGatewayType, event);
        }
        stripeService.save(paymentGateway);
    }

    private void saveSpreedlyGatewayForEvent(Event event, Staff staff, SpreedlyGatewayType spreedlyGatewayType, String gatewayToken, JSONObject gatewayBody) throws JSONException {

        Stripe stripeByEvent = roStripeService.findByEvent(event);
        log.info("saveSpreedlyGatewayForEvent on Stripe for event id: {} by {}, gatewayType {}, time {}", event.getEventId(), staff.getUserId(), spreedlyGatewayType, System.currentTimeMillis());
        savePaymentGateway(stripeByEvent, staff, event, spreedlyGatewayType, gatewayToken, gatewayBody);
        eventService.enablePaymentProcessing(stripeByEvent, event);
        event.setCreditCardEnabled(true);
        eventRepoService.save(event);
        eventPlanConfigService.updateAttendeeImportLimitOnceStripeIsConnected(event);
        eventService.autoActivateAndEnableTicketing(event);
        log.info("saveSpreedlyGatewayForEvent on stripe successfully saved for event {} by userId {} gatewayType {}", event.getEventId(), staff.getUserId(), spreedlyGatewayType);
    }

    @Override
    public CustomerCardDto getCustomerIdFromSpreedlyThirdPartyVault(Stripe stripe, String paymentMethodTokenId, Event event, User user, String customerId) {
        long eventId = event.getEventId();
        try {
            String gatewayName = SpreedlyGatewayType.valueOf(stripe.getPaymentGateway()).getValue();
            Map<String, Object> paymentMethodMap = new HashMap<>();
            paymentMethodMap.put(PAYMENT_METHOD_TOKEN, paymentMethodTokenId);

            if (StringUtils.isNotBlank(customerId)) {
                this.setGatewaySpecificFieldForPaymentMethodTokenStoreApi(SpreedlyGatewayType.valueOf(stripe.getPaymentGateway()), paymentMethodMap, customerId);
            } else {
                customerId = String.format(CUSTOMER_EVENT_WITH_USERID,event.getEventId(), user.getUserId());
            }

            Map<String, Object> thirdPartyValutRequestMap = new HashMap<>();
            thirdPartyValutRequestMap.put(TRANSACTION, paymentMethodMap);

            HttpResponse<JsonNode> gatewayResponse = spreedlyApiExecutionService.executePaymentMethodStoreApi(stripe.getAccessToken(), thirdPartyValutRequestMap);

            JSONObject responseBody = gatewayResponse.getBody().getObject();

            CustomerCardDto customerCardDto = new CustomerCardDto(customerId, paymentMethodTokenId);
            if (GeneralUtils.is2xxSuccess(gatewayResponse.getStatus())) {
                JSONObject transactionData = responseBody.getJSONObject(TRANSACTION);
                if(SUCCEEDED.equals(transactionData.getString(Spreedly.STATE))) {
                    this.getCustomerIdFromThirdPartyVaultResponse(gatewayName, customerCardDto, transactionData);
                    JSONObject basisPaymentMethod = transactionData.getJSONObject(BASIS_PAYMENT_METHOD);
                    this.getAndSetCustomerCardBasicDetailsFromResponse(customerCardDto, basisPaymentMethod);
                }
                log.info("getCustomerIdFromSpreedlyThirdPartyVault, Payment Method store on Third party vault succeed for event {} user {} gatewayType {} customerId {}", eventId, user.getUserId(), gatewayName, customerCardDto.getCustomerId());
                return customerCardDto;
            }
            else if(gatewayResponse.getStatus() == 422 && SP_AUTHORIZE_NET.getValue().equals(gatewayName)) {
				/*
				 * Authorize.net gateway CIM not available meaning we can't use 3rd party vault
				 * and create token in Authorize.net so set the spreedly token as a payment
				 * token Id for further transaction
				 */            	
            	log.info("getCustomerIdFromSpreedlyThirdPartyVault, Error from Spreedly CIM Disbaled for event {} user {} response {}", eventId, user.getUserId(), gatewayResponse.getBody());
                JSONObject transactionData = responseBody.getJSONObject(TRANSACTION);
                // set customer Id as a paymentMethodTokenId
                customerCardDto.setCustomerId(paymentMethodTokenId);
                JSONObject basisPaymentMethod = transactionData.getJSONObject(BASIS_PAYMENT_METHOD);
                this.getAndSetCustomerCardBasicDetailsFromResponse(customerCardDto, basisPaymentMethod);
                
                log.info("getCustomerIdFromSpreedlyThirdPartyVault, Payment Method store on Spreedly vault succeed for event {} user {} gatewayType {} ", eventId, user.getUserId(), gatewayName);
                return customerCardDto;
            }
            else {
                log.info("getCustomerIdFromSpreedlyThirdPartyVault, Error from Spreedly for event {} user {} response {}", eventId, user.getUserId(), gatewayResponse.getBody());
                String errorMessage = "Error while storing the payment method.";
                if (responseBody.has(ERRORS)) {
                    errorMessage = responseBody.getJSONArray(ERRORS).getJSONObject(0).getString(MESSAGE_LOWER);
                } else if (responseBody.has(TRANSACTION)) {
                    JSONObject transactionData = responseBody.getJSONObject(TRANSACTION);
                    String message = transactionData.getString(MESSAGE_LOWER);
                    JSONObject basisPaymentMethod = transactionData.getJSONObject(BASIS_PAYMENT_METHOD);
                    log.info("getCustomerIdFromSpreedlyThirdPartyVault, Error from Spreedly for payment gateway {} for event {} user {} message {}", stripe.getPaymentGateway(), event.getEventId(), user.getUserId(), message);
                    return this.getCardDetailsIfAlreadyStoreElseThrowError(stripe, customerCardDto, message, basisPaymentMethod);
                }
                throw new ThirdPartyExceptions(400, errorMessage);
            }

        } catch (UnirestException | JSONException e) { //NOSONAR
            log.error("Exception during the store the Payment token method on Third Party Vault for event {} user {} gateway {} error", eventId, user.getUserId(), stripe.getPaymentGateway(), e);
            throw new ThirdPartyExceptions(400, e.getMessage());
        }
    }

    private CustomerCardDto getAndSetCustomerCardBasicDetailsFromResponse(CustomerCardDto customerCardDto, JSONObject basisPaymentMethod) throws JSONException {
        customerCardDto.setLastFour(basisPaymentMethod.getString(LAST_FOUR_DIGIT));
        customerCardDto.setExpDateMonth(basisPaymentMethod.getInt(Spreedly.MONTH));
        customerCardDto.setExpDateYear(basisPaymentMethod.getInt(Spreedly.YEAR));
        customerCardDto.setCardType(basisPaymentMethod.getString(CARD_TYPE));
        customerCardDto.setFingerPrint(basisPaymentMethod.getString(FINGERPRINT));
        return customerCardDto;
    }

    private void getCustomerIdFromThirdPartyVaultResponse(String gatewayName, CustomerCardDto customerCardDto,
			JSONObject transactionData) throws JSONException {
		JSONObject gatewaySpecificResponseFields = transactionData.getJSONObject(GATEWAY_SPECIFIC_RESPONSE_FIELDS);
		if (gatewaySpecificResponseFields.has(gatewayName)) {
			JSONObject responseField = gatewaySpecificResponseFields.getJSONObject(gatewayName);
			if (SP_BRAINTREE.getValue().equals(gatewayName)) {
				customerCardDto.setCustomerId(responseField.getString(CUSTOMER_ID));
			} else if (SP_AUTHORIZE_NET.getValue().equals(gatewayName)) {
					JSONObject paymentMethodData = transactionData.getJSONObject(PAYMENT_METHOD);
					if (paymentMethodData.has(THIRD_PARTY_TOKEN)) {
						String thirdPartyToken = paymentMethodData.getString(THIRD_PARTY_TOKEN);
						if (StringUtils.isNotBlank(thirdPartyToken)) {
							String customerProfileId = thirdPartyToken.split(HASH)[0];
							customerCardDto.setCustomerId(customerProfileId);
						}
					}
			}
		}
	}

    @Override
    public CustomerCardDto getCardDetailsFromSpreedly(Stripe stripe, String customerId, String paymentMethodTokenId, Event event, User user, boolean isRetainCard) {
        long eventId = event.getEventId();
        try {
            String gatewayName = SpreedlyGatewayType.valueOf(stripe.getPaymentGateway()).getValue();

            HttpResponse<JsonNode> gatewayResponse = spreedlyApiExecutionService.executeGetPaymentMethodTokenDetailApi(paymentMethodTokenId);

            JSONObject responseBody = gatewayResponse.getBody().getObject();

            customerId = StringUtils.isNotBlank(customerId) ? customerId : String.format(CUSTOMER_EVENT_WITH_USERID,event.getEventId(), user.getUserId());
            CustomerCardDto customerCardDto = new CustomerCardDto(customerId, paymentMethodTokenId);
            if (GeneralUtils.is2xxSuccess(gatewayResponse.getStatus())) {
                JSONObject transactionData = responseBody.getJSONObject(PAYMENT_METHOD);
                this.getAndSetCustomerCardBasicDetailsFromResponse(customerCardDto, transactionData);
                if(!RETAIN.equals(transactionData.getString(STORAGE_STATE)) && isRetainCard) {
                    log.info("getCardDetailsFromSpreedly, Calling API to retain the card detail based on Payment method token");
                    spreedlyGatewayAsyncTaskService.retainSpreedlyPaymentMethodToken(paymentMethodTokenId);
                }
                log.info("getCardDetailsFromSpreedly, Get Card details succeed for event {} user {} gatewayType {}", eventId, user.getUserId(), gatewayName);
                return customerCardDto;
            } else {
                log.info("getCardDetailsFromSpreedly, Error from Spreedly for event {} user {} response {}", eventId, user.getUserId(), gatewayResponse.getBody());
                String errorMessage = "Error while getting the payment method.";
                errorMessage = getErrorMessage(responseBody, errorMessage);
                throw new ThirdPartyExceptions(400, errorMessage);
            }

        } catch (UnirestException | JSONException e) { //NOSONAR
            log.info("Exception during the store the Payment token method on Third Party Vault for event {} user {} gateway {} error", eventId, user.getUserId(), stripe.getPaymentGateway(), e); //NOSONAR
            throw new ThirdPartyExceptions(400, e.getMessage());
        }
    }

    @Override
    public TransactionDataDTO createChargeFromSpreedly(Stripe stripe, String customerId, double amount, String currency, boolean isCreditCardFeeToBuyer,
                                         String statementDescriptor, Map<String, Object> metadata, String paymentMethodTokenId) {
        log.info("createChargeFromSpreedly");
        log.info("Secret key: {}", stripe.getAccessToken());
        log.info(Constants.CUSTOMER_ID, customerId);
        try {
            if (isCreditCardFeeToBuyer) {
                log.debug("Credit card fee to buyer");
                amount = StripeUtil.getAmountToCharge(amount, stripe.getCCPercentageFee(), stripe.getCCFlatFee());
            }

            SpreedlyGatewayType spreedlyGatewayType = SpreedlyGatewayType.valueOf(stripe.getPaymentGateway());

            Map<String, Object> billingAddress = new HashMap<>();

            setTransactionRequestData(billingAddress,metadata.getOrDefault(Constants.ORDER_ID, STRING_EMPTY).toString(),
                       metadata.getOrDefault(Constants.STRING_EMAIL, STRING_EMPTY).toString());

            TransactionRequestDTO transactionRequestDTO = new TransactionRequestDTO()
                    .withPaymentMethodToken(paymentMethodTokenId)
                    .withAmount(GeneralUtils.convertToSmallestCurrencyUnit(amount, currency))
                    .withCurrencyCode(currency)
                    .withRetainOnSuccess(true)
                    .withBillingAddress(billingAddress)
                    .withTransactionMetadata(metadata)
                    .withDescription(getStatementDescriptor("EVENT-" + statementDescriptor))
                    .withEmail(metadata.getOrDefault(Constants.STRING_EMAIL, STRING_EMPTY).toString())
                    .withOrderId(metadata.getOrDefault(Constants.ORDER_ID, STRING_EMPTY).toString());

            this.setGatewaySpecificFieldToTransactionRequest(spreedlyGatewayType, transactionRequestDTO, customerId);

            Map<String, TransactionRequestDTO> purchaseRequestDto = new HashMap<>();
            purchaseRequestDto.put(TRANSACTION, transactionRequestDTO);

            HttpResponse<JsonNode> gatewayResponse = spreedlyApiExecutionService.executePurchaseApi(stripe.getAccessToken(), purchaseRequestDto);

            JSONObject responseBody = gatewayResponse.getBody().getObject();
            if(GeneralUtils.is2xxSuccess(gatewayResponse.getStatus())) {
                TransactionDataDTO transactionDataDTO = new TransactionDataDTO();
                if (responseBody.has(TRANSACTION)) {
                    TransactionResponseDTO transactionResponseDTO = JsonMapper.stringtoObject(responseBody.toString(), TransactionResponseDTO.class);
                    if (null != transactionResponseDTO && null != transactionResponseDTO.getTransaction()) {
                        transactionDataDTO = transactionResponseDTO.getTransaction();
                        log.info("createChargeFromSpreedly, Charge created successfully and transaction token retrieve for customerId {} gatewayType {}", customerId, spreedlyGatewayType);
                    }
                }
                log.info("createChargeFromSpreedly, Charge created successfully for customerId {} gatewayType {}", customerId, spreedlyGatewayType);
                return transactionDataDTO;
            } else {
                log.info("createChargeFromSpreedly, Error from Spreedly for transaction customerId {}, gatewayType {} response : {}", customerId, spreedlyGatewayType, responseBody);
                String errorMessage = "Error while creating transaction.";
                if (responseBody.has(TRANSACTION)) {
                    TransactionResponseDTO transactionResponseDTO = JsonMapper.stringtoObject(responseBody.toString(), TransactionResponseDTO.class);
                    if (null != transactionResponseDTO && null != transactionResponseDTO.getTransaction()) {
                        TransactionDataDTO transactionDataDTO = transactionResponseDTO.getTransaction();
                        log.info("createChargeFromSpreedly, Error from Spreedly for transaction customerId {}, gatewayType {} errorMessage {}", customerId, spreedlyGatewayType, transactionResponseDTO.getTransaction().getMessage());
                        throw new ThirdPartyExceptions(400, transactionDataDTO.getMessage());
                    }
                }
                errorMessage = getErrorMessage(responseBody, errorMessage);
                throw new ThirdPartyExceptions(400, errorMessage);
            }
        } catch (UnirestException e) { //NOSONAR
            log.error("Exception during the made transaction for customerId {} gateway {} error", customerId, stripe.getPaymentGateway(), e); //NOSONAR
            throw new ThirdPartyExceptions(400, e.getMessage());
        }
    }
    
    private void setTransactionRequestData(Map<String,Object> billingAddress,String orderId,String email) {
        List<EventTickets> eventTicketsList = eventCommonRepoService.findByOrder(Long.parseLong(orderId));
        if (eventTicketsList.isEmpty()) {
            return;
        }
        TicketHolderAttributes ticketHolderAttributes = ticketHolderAttributesService.findById(eventTicketsList.get(0).getTicketHolderAttributesId().getId());
        TicketAttributeValueDto1 ticketAttributeValueDto = TicketHolderAttributesHelper.parseJsonToObject(ticketHolderAttributes.getJsonValue());

        Map<String, String> purchaserAttribute = (Map<String, String>) ticketAttributeValueDto.getPurchaser().get(Constants.TICKETING.ATTRIBUTES);

        if (purchaserAttribute != null && !purchaserAttribute.isEmpty()) {
            populateTransactionRequestDTO(billingAddress, purchaserAttribute,email);
        }
    }

    private void populateTransactionRequestDTO(Map<String,Object> billingAddress, Map<String, String> purchaserAttribute,String email) {
    	
    	String billingAddreddDto = purchaserAttribute.getOrDefault(BILLING_ADDRESS, STRING_EMPTY);
    	if(!ObjectUtils.isEmpty(billingAddreddDto)) {
    	String[] parts = billingAddreddDto.split("\\|", -1);
    	 billingAddress.put("address1", parts.length > 0 ? parts[0] : "");
         billingAddress.put("address2", parts.length > 1 ? parts[1] : "");
         billingAddress.put("city",     parts.length > 2 ? parts[2] : "");
         billingAddress.put("state",    parts.length > 3 ? parts[3] : "");
         billingAddress.put("zip",      parts.length > 4 ? parts[4] : "");
         billingAddress.put("country",  parts.length > 5 ? parts[5] : "");
    	}
    	 billingAddress.put("firstName", purchaserAttribute.getOrDefault(FIRST_NAME, STRING_EMPTY));
         billingAddress.put("lastName", purchaserAttribute.getOrDefault(LAST_NAME, STRING_EMPTY));
         billingAddress.put("email",    email);
         billingAddress.put("phone_number", purchaserAttribute.getOrDefault("phoneNumber",STRING_EMPTY));
        // billingAddress.put("fax_number",    purchaserAttribute.getOrDefault("fax", "**************"));
        }

    @Override
    public SpreedlyIframeDTO createSpreedlyIframeSignature(String eventUrl) {
        try {
            if(this.spreedlyPrivateKey == null) {
                log.info("createSpreedlyIframeSignature, Spreedly key is not found");
                throw new ThirdPartyExceptions(400, "Spreedly key is not found");
            }
            String certificateToken = this.spreedlyCertificateToken;
            Base64.Decoder decoder = Base64.getDecoder();
            String nonce = UUID.randomUUID().toString();
            long timestamp = Instant.now().getEpochSecond();

            String signatureData = nonce + timestamp + certificateToken;

            // Decode base64 to get the raw key bytes
            byte[] keyBytes = decoder.decode(this.spreedlyPrivateKey);

            // Create PKCS8EncodedKeySpec
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);

            // Get RSA key factory
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            // Generate private key
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            // Create signature instance
            Signature signature = Signature.getInstance("SHA256withRSA");

            // Initialize with private key
            signature.initSign(privateKey);

            // Update with data
            signature.update(signatureData.getBytes());

            // Generate signature
            byte[] signedBytes = signature.sign();

            // Convert to base64
            String base64Signature = Base64.getEncoder().encodeToString(signedBytes);

            log.info("createSpreedlyIframeSignature, Spreedly Iframe signature generated successfully for eventUrl : {}", eventUrl);

            return new SpreedlyIframeDTO(nonce,String.valueOf(timestamp),certificateToken,base64Signature);

        } catch (Exception e){
            log.info("Exception during generation of spreedly Iframe signature {}", e.getMessage(), e);
            throw new ThirdPartyExceptions(400, e.getMessage());
        }
    }

    @Override
    public RefundInfoDto refundChargeFromSpreedly(Stripe stripe, String chargeId, double amount, String currency, Map<String, String> metadata) {
        log.info("refundChargeFromSpreedly");
        try {
            SpreedlyGatewayType spreedlyGatewayType = SpreedlyGatewayType.valueOf(stripe.getPaymentGateway());
            Map<String, Object> billingAddress = new HashMap<>();
            if (null != metadata && !metadata.isEmpty()){
                setTransactionRequestData(billingAddress,metadata.getOrDefault(Constants.ORDER_ID, STRING_EMPTY).toString(),
                        metadata.getOrDefault(Constants.STRING_EMAIL, STRING_EMPTY).toString());
            }

            long refundAmount = GeneralUtils.convertToSmallestCurrencyUnit(amount, currency);
            TransactionRequestDTO transactionRequestDTO;
            if (!billingAddress.isEmpty()){
                transactionRequestDTO = new TransactionRequestDTO()
                        .withAmount(refundAmount)
                        .withCurrencyCode(currency)
                        .withBillingAddress(billingAddress)
                        .withTransactionMetadata(CollectionUtils.isEmpty(metadata) ? Collections.emptyMap() : new HashMap<>(metadata));
            } else {
                transactionRequestDTO = new TransactionRequestDTO()
                        .withAmount(refundAmount)
                        .withCurrencyCode(currency)
                        .withTransactionMetadata(CollectionUtils.isEmpty(metadata) ? Collections.emptyMap() : new HashMap<>(metadata));
            }

            Map<String, TransactionRequestDTO> refundRequestDto = new HashMap<>();
            refundRequestDto.put(TRANSACTION, transactionRequestDTO);

            HttpResponse<JsonNode> gatewayResponse = spreedlyApiExecutionService.executeRefundApi(chargeId, refundRequestDto);

            JSONObject responseBody = gatewayResponse.getBody().getObject();
            if(GeneralUtils.is2xxSuccess(gatewayResponse.getStatus()) && responseBody.has(TRANSACTION)) {
                JSONObject refundResponseDTO = responseBody.getJSONObject(TRANSACTION);
                if (null != refundResponseDTO && SUCCEEDED.equals(refundResponseDTO.getString(Spreedly.STATE))) {
                    RefundInfoDto refundInfoDto = new RefundInfoDto(
                            refundResponseDTO.getString(TOKEN_LOWER),
                            refundResponseDTO.getString(Spreedly.STATE),
                            refundAmount, 0);
                    refundInfoDto.setStripeNetSale(refundAmount);
                    log.info("createChargeFromSpreedly, Charge refunded successfully for chargeId {} gatewayType {} paymentId {}", chargeId, spreedlyGatewayType, stripe.getId());
                    return refundInfoDto;
                }
            }
            log.info("createChargeFromSpreedly, Error from Spreedly for refund chargeId {}, gatewayType {} response : {}", chargeId, spreedlyGatewayType, responseBody);
            String errorMessage = "Error while refund the charge.";
            if (responseBody.has(TRANSACTION)) {
                TransactionResponseDTO transactionResponseDTO = JsonMapper.stringtoObject(responseBody.toString(), TransactionResponseDTO.class);
                if (null != transactionResponseDTO && null != transactionResponseDTO.getTransaction()) {
                    TransactionDataDTO transactionDataDTO = transactionResponseDTO.getTransaction();
                    log.info("createChargeFromSpreedly, Error from Spreedly for refund chargeId {}, gatewayType {} errorMessage {}", chargeId, spreedlyGatewayType, transactionResponseDTO.getTransaction().getMessage());
                    throw new ThirdPartyExceptions(400, transactionDataDTO.getMessage());
                }
            }
            errorMessage = getErrorMessage(responseBody, errorMessage);
            throw new ThirdPartyExceptions(400, errorMessage);
        } catch (UnirestException | JSONException e) { //NOSONAR
            log.error("Exception during the refund the charge {} gateway {} errorMessage {}", chargeId, stripe.getPaymentGateway(), e.getMessage(), e);
            throw new ThirdPartyExceptions(400, e.getMessage());
        }
    }

    @Override
    public void removeCardFromSpreedly(Event event, User user, String paymentMethodToken) {
        try {
            log.info("removeCardFromSpreedly, Spreedly API calling to remove payment method token");
            HttpResponse<JsonNode> gatewayResponse = spreedlyApiExecutionService.executeRedactPaymentMethodToken(paymentMethodToken);

            JSONObject responseBody = gatewayResponse.getBody().getObject();
            if (GeneralUtils.is2xxSuccess(gatewayResponse.getStatus())) {
                JSONObject transactionData = responseBody.getJSONObject(TRANSACTION);
                if(SUCCEEDED.equals(transactionData.getString(Spreedly.STATE))) {
                    log.info("removeCardFromSpreedly, Payment method token is removed successfully for event {} user {}", event.getEventId(), user.getUserId());
                }
            } else {
                log.info("removeCardFromSpreedly, Error from Spreedly response {}", gatewayResponse.getBody());
            }
        } catch (UnirestException | JSONException e) {
            log.error("Exception during the remove the Payment token method error {}", e.getMessage(), e);
        }
    }

    private static String getErrorMessage(JSONObject responseBody, String errorMessage) {
       try {
           if (responseBody.has(ERRORS)) {
               errorMessage = responseBody.getJSONArray(ERRORS).getJSONObject(0).getString(MESSAGE_LOWER);
           }
       } catch (JSONException e) {
           log.info("Error while get error message from the response, message {}", e.getMessage());
       }
        return errorMessage;
    }

    private String getStatementDescriptor(String statementDescriptor) {
        if (StringUtils.isEmpty(statementDescriptor)) {
            return statementDescriptor;
        }
        // Remove invalid characters
        statementDescriptor = statementDescriptor.replaceAll("[<>,'\"/*]", "");
        // Append "_A" if descriptor is numeric
        if (statementDescriptor.matches("\\d+")) {
            statementDescriptor += "_A";
        }
        // Pad with '#' if the descriptor becomes empty
        if (statementDescriptor.isEmpty()) {
            statementDescriptor = StringUtils.rightPad(statementDescriptor, 5, '#');
        }
        // Truncate to a maximum of 21 characters
        return statementDescriptor.length() > 21 ? statementDescriptor.substring(0, 21) : statementDescriptor;
    }

    String readString(InputStream inputStream) throws IOException {
        ByteArrayOutputStream into = new ByteArrayOutputStream();
        byte[] buf = new byte[4096];
        for (int n; 0 < (n = inputStream.read(buf));) {
            into.write(buf, 0, n);
        }
        into.close();
        return new String(into.toByteArray(), "UTF-8"); // Or whatever encoding
    }


}
