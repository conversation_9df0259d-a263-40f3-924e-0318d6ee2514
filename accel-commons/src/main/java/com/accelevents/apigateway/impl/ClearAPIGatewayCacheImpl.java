package com.accelevents.apigateway.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.VatTax;
import com.accelevents.domain.enums.WidgetType;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.repo.helper.BeeFreePageRepoService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.NumberUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.accelevents.utils.ConstantsAPIGW.*;

@Service
public class ClearAPIGatewayCacheImpl implements ClearAPIGatewayCache {

    private static final Logger log = LoggerFactory.getLogger(ClearAPIGatewayCacheImpl.class);

    private ROEventService roEventService;
    private RestTemplate restTemplate;
    private BeeFreePageRepoService beeFreePageRepoService;
    private String apiGatewayBaseURL;
    private String EVENTURL= "eventurl";
    private String PAGEURL= "pageUrl";
    private static final String ORGANIZER_URL = "organizerUrl";
    private static final String WHITELABEL_URL = "whiteLabelUrl";

    @Autowired
    public ClearAPIGatewayCacheImpl(ROEventService roEventService, RestTemplate restTemplate, @Value("${aws.apigateway.rest.baseurl}") String apiGatewayBaseURL, BeeFreePageRepoService beeFreePageRepoService) {
        this.roEventService = roEventService;
        this.restTemplate = restTemplate;
        this.apiGatewayBaseURL = apiGatewayBaseURL;
        this.beeFreePageRepoService = beeFreePageRepoService;
    }

    private void clearCacheAPI(String url, Map<String, ?> uriVariables){
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setCacheControl("max-age=0");

            HttpEntity<?> entity = new HttpEntity<>(headers);

            log.info("API Gateway clear cache for URL := {} param: = {}", url, uriVariables);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class, uriVariables);

            if (responseEntity.getStatusCode() != HttpStatus.OK) {
                log.error("##API Gateway cache clear fail with status code = {} for URL : ={} param:={}", responseEntity.getStatusCodeValue(), url, uriVariables);
            }
        }catch (Exception ex){
            log.info("##APIGW Clear Cache Exception for URL {}: ", url);
            log.error("##APIGW Clear Cache Exception: ", ex);
        }
    }

    private void oneSecondGap() {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.warn("UpdateWLAPIGWCache | OneSecondGap {}",e.getMessage());
        }
    }

    private void halfSecondGap() {
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            log.warn("UpdateWLAPIGWCache | HalfSecondGap {}",e.getMessage());
        }
    }

    private void apiGwClearCacheDonationGoal(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        //TODO: APIGW_UN  // NOSONAR
        //this.clearCacheAPI(apiGatewayBaseURL + Donation.donationGoal, uriMaps);  // NOSONAR
    }

    private void apiGwClearCacheDonationScroll(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        //TODO: APIGW_UN  // NOSONAR
        //this.clearCacheAPI(apiGatewayBaseURL + Donation.donationScroll, uriMaps);  // NOSONAR
    }

    private void apiGwClearCacheDonationTotalRaised(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        //TODO: APIGW_UN  // NOSONAR
        //this.clearCacheAPI(apiGatewayBaseURL + Donation.donationTotalRaised, uriMaps);  // NOSONAR
    }

    private void apiGwClearCacheNetworkingRules(String eventUrl, Long sessionId){
        Map<String, Object> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        uriMaps.put("sessionId", sessionId);

        //TODO: APIGW_UN  // NOSONAR
        //this.clearCacheAPI(apiGatewayBaseURL + Ticketing.networkingRuleSession, uriMaps);  // NOSONAR
    }

    private void apiGwClearCacheEventData(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + EVENT_API_URL, uriMaps);
    }

    private void apiGwClearCacheIsVatEnabled(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_IS_VAT_ENABLED, uriMaps);
    }

    private void apiGwClearCacheVESettings(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + VE_SETTINGS, uriMaps);
    }
    private void apiGwClearCacheVEMultiLanguageLabel(String eventUrl, String languageCode){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        uriMaps.put("languageCode", languageCode);

        this.clearCacheAPI(apiGatewayBaseURL + VE_MULTI_LANGUAGE_LABEL, uriMaps);
    }

    private void apiGwClearCacheTicketingSettings(String eventUrl){
        this.halfSecondGap();
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_TICKETING_SETTINGS, uriMaps);
    }

    private void apiGwClearCacheTicketTypes(String eventUrl, String accessCode, Long recurringId){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        String uri = apiGatewayBaseURL + ED_TICKETING_TICKET_TYPES;

        if(StringUtils.isNotBlank(accessCode) && recurringId != null){
            uriMaps.put("accessCode", accessCode);
            uriMaps.put("recurringId", recurringId.toString());
            uri = uri + "?accessCode={accessCode}&recurringId={recurringId}";
        } else if(StringUtils.isNotBlank(accessCode)){
            uriMaps.put("accessCode", accessCode);
            uri = uri + "?accessCode={accessCode}";
        } else if(recurringId != null){
            uriMaps.put("recurringId", recurringId.toString());
            uri = uri + "?recurringId={recurringId}";
        }

        this.clearCacheAPI(uri, uriMaps);
    }

    private void apiGwClearCacheTicketTypesCategories(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_TICKETING_TICKET_TYPES_CATEGORIES, uriMaps);
    }

    private void apiGwClearCacheTicketingFee(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_TICKETING_FEE, uriMaps);
    }

    private void apiGwClearCacheWLHostBaseUrl(String hostBaseUrl){
        Map<String, String> uriMaps = new HashMap<>();

        this.clearCacheAPI(apiGatewayBaseURL + WL_HOST_BASE_URL + "?hostBaseUrl="+hostBaseUrl, uriMaps);
    }

    private void apiGwClearCacheWLSettings(String whiteLabelURL){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put("whiteLabelURL", whiteLabelURL);

        this.clearCacheAPI(apiGatewayBaseURL + WL_SETTINGS, uriMaps);
    }

    private void apiGwClearCacheSpeakerPortal(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_SPEAKER_PORTAL, uriMaps);
        this.clearCacheAPI(apiGatewayBaseURL + ED_SESSION_SPEAKERS, uriMaps);
    }

    private void apiGwClearCacheSponsors(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_SPONSORS, uriMaps);
    }

    private void apiGwClearCacheSessionSponsors(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        this.clearCacheAPI(apiGatewayBaseURL + ED_SESSION_SPONSORS, uriMaps);
    }

    private void apiGwClearCacheOfflinePaymentConfiguration(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_PAYMENT_OFFLINE_CONFIGURATION, uriMaps);
    }

    private void apiGwClearCachePaymentSettings(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_PAYMENT_SETTINGS, uriMaps);
    }

    private void apiGwClearCacheBeeFreeDefaultHomePage(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        this.clearCacheAPI(apiGatewayBaseURL + ED_BEE_FREE_DEFAULT_HOME_PAGE, uriMaps);
    }

    private void apiGwClearCacheBeeFreePublishedPage(String eventUrl, String pageUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        uriMaps.put(PAGEURL, pageUrl);
        this.clearCacheAPI(apiGatewayBaseURL + ED_BEE_FREE_PUBLISHED_PAGE, uriMaps);
    }

//    ----------------------------- Public Methods  -------------------------------- //

    @Override
    @Async
    public void clearAPIGwEventCache(String eventUrl){
        this.apiGwClearCacheEventData(eventUrl);
        this.apiGwClearCacheTicketingSettings(eventUrl);
    }

    @Override
    @Async
    public void clearAPIGwEventCache(long eventId){
        Event event = roEventService.findEventByEventId(eventId);
        if (null != event){
            this.apiGwClearCacheEventData(event.getEventURL());
        }
    }

    @Override
    @Async
    public void clearAPIGwEventCacheByUrl(String eventUrl){
        if (StringUtils.isNotBlank(eventUrl)){
            this.apiGwClearCacheEventData(eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwTicketingCache(Ticketing ticketing){
        Event event = roEventService.findEventByEventId(ticketing.getEventid().getEventId());
        if (null != event){
            this.apiGwClearCacheEventData(event.getEventURL());
            this.apiGwClearCacheTicketingSettings(event.getEventURL());
        }
    }

    @Override
    @Async
    public void clearAPIGwTicketTypes(String eventUrl, String accessCode, Long recurringId) {
        if(StringUtils.isNotBlank(eventUrl)) {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwTicketTypes | eventUrl | {}", eventUrl);
            this.apiGwClearCacheTicketTypes(eventUrl, accessCode, recurringId);
        }else{
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwTicketTypes | eventUrl | {} ", eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwTicketTypes(String eventUrl) {
        if(StringUtils.isNotBlank(eventUrl)) {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwTicketTypes | eventUrl | {}", eventUrl);
            this.apiGwClearCacheTicketTypes(eventUrl, null, null);
        }else{
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwTicketTypes | eventUrl | {} ", eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwSpeakerPortalCache(String eventUrl) {
        if(StringUtils.isNotBlank(eventUrl)) {
            log.info("ClearAPIGatewayCacheImpl | clearSpeakerCache | eventUrl | {}", eventUrl);
            this.apiGwClearCacheSpeakerPortal(eventUrl);
        }else{
            log.info("ClearAPIGatewayCacheImpl | clearSpeakerCache | eventUrl | {} ", eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwSpeakerPortalCache(Long eventId) {
        if(null != eventId) {
            Event currentEvent = roEventService.findEventByEventId(eventId);
            if (null != currentEvent) {
                log.info("ClearAPIGatewayCacheImpl | clearSpeakerCache | eventNotNull | speakerEventId | {}", eventId);
                this.apiGwClearCacheSpeakerPortal(currentEvent.getEventURL());
            } else {
                log.info("ClearAPIGatewayCacheImpl | clearSpeakerCache | eventNull | speakerEventId | {}", eventId);
            }
        }else{
            log.info("ClearAPIGatewayCacheImpl | clearSpeakerCache | eventNull | speakerEventIdNull | {}", eventId);
        }
    }

    @Override
    @Async
    public void clearAPIGwAllSpeakerPortalCache(Iterable<Speaker> speakers) {
        if(speakers.iterator().hasNext()){
            Speaker speaker = speakers.iterator().next();
            Event event = roEventService.getEventByIdIfPresentOrNull(speaker.getEventId());
            if (event != null) {
                log.info("ClearAPIGatewayCacheImpl | clearAllSpeakerCache | eventId | {}", speaker.getEventId());
                this.apiGwClearCacheSpeakerPortal(event.getEventURL());
            } else {
                log.info("ClearSpeakerAPIGatewayCacheImpl | clearAllSpeakerCache | eventId | {} | speakerId | {}", speaker.getEventId(), speaker.getId());
            }
        }
    }

    @Override
    @Async
    public void clearAPIGwTagAndTrackCache(String eventUrl, String type){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        uriMaps.put("type", type);

        this.clearCacheAPI(apiGatewayBaseURL + ED_TAG_AND_TRACK_TYPE, uriMaps);
    }

    @Override
    @Async
    public void clearAPIGwVESettingsCache(String eventUrl) {
        Event event = roEventService.getEventByURL(eventUrl);
        if (event != null) {
            log.info("Event found for event url {}, event id {}", eventUrl, event.getEventId());
            this.apiGwClearCacheVESettings(eventUrl);
        } else {
            log.info("Event Not found for event url {}", eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwVESettingsCache(Long eventId) {
        if (null != eventId){
            Event currentEvent = roEventService.findEventByEventId(eventId);
            if (null != currentEvent) {
                log.info("ClearCache | VirtualEvent | EventId {}", eventId);
                this.apiGwClearCacheVESettings(currentEvent.getEventURL());
            } else {
                log.info("ClearCache | VirtualEvent | EventId {} | EventNotFound", eventId);
            }
        } else {
            log.info("ClearCache | VirtualEvent | EventIdNull");
        }
    }

    @Override
    @Async
    public void clearAPIGwVESettingsLanguageLabelCache(String eventUrl, String languageCode) {
        this.apiGwClearCacheVEMultiLanguageLabel(eventUrl, languageCode);
    }

    @Override
    @Async
    public void clearAPIGwWLSettingsHostBaseUrlCache(String hostBaseUrl) {
        if(StringUtils.isNotBlank(hostBaseUrl)) {
            this.apiGwClearCacheWLHostBaseUrl(hostBaseUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwWLSettingsCache(String whiteLabelURL) {
        this.oneSecondGap();
        if(StringUtils.isNotBlank(whiteLabelURL)) {
            this.apiGwClearCacheWLSettings(whiteLabelURL);
        }
    }

    @Override
    @Async
    public void clearAPIGwSponsorsCache(String eventUrl) {
        Event event = roEventService.getEventByURL(eventUrl);
        if(event != null) {
            log.info("ClearAPIGwCache | clearSponsorCache | eventId | {}",event.getEventId() );
            this.apiGwClearCacheSponsors(eventUrl);
        }else{
            log.info("ClearAPIGwCache | clearSponsorCache | eventurl {}", eventUrl);
        }
        
    }

    @Override
    @Async
    public void clearCacheVatTax(VatTax vatTax){
        Event event = roEventService.findEventByEventId(vatTax.getEventId());
        if(null != event){
            this.apiGwClearCacheIsVatEnabled(event.getEventURL());
        }
    }

    @Override
    //  @Async
    //  Commenting Async because it is updating memcached with  old event value
    public void clearAPIGwTransactionFeeCache(String eventUrl) {
        Event event = roEventService.getEventByURL(eventUrl);
        if(event != null) {
            log.info("ClearAPIGwCache | clearAPIGwTransactionFeeCache | eventId | {} | API call start time {}",event.getEventId(),System.currentTimeMillis());
            this.apiGwClearCacheTicketingFee(eventUrl);
            log.info("ClearAPIGwCache | clearAPIGwTransactionFeeCache | eventId | {} | API call end time {}",event.getEventId(),System.currentTimeMillis());

        }else{
            log.info("ClearAPIGwCache | clearAPIGwTransactionFeeCache | eventurl {}", eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwTicketingTypesCategoriesCache(String eventUrl){
        this.oneSecondGap();
        Event event = roEventService.getEventByURL(eventUrl);
        if(event != null) {
            log.info("ClearAPIGwCache | clearAPIGwTicketingTypesCategoriesCache | eventId | {}",event.getEventId() );
            this.apiGwClearCacheTicketTypesCategories(eventUrl);
        }else{
            log.info("ClearAPIGwCache | clearAPIGwTicketingTypesCategoriesCache | eventurl {}", eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwOfflinePaymentConfigurationCache(Long eventId){
        if(null != eventId){
            Event event = roEventService.findEventByEventId(eventId);
            if(event != null) {
                log.info("ClearAPIGwCache | clearAPIGwOfflinePaymentConfigurationCache | eventId | {}",event.getEventId() );
                this.apiGwClearCacheOfflinePaymentConfiguration(event.getEventURL());
            }else{
                log.info("ClearAPIGwCache | clearAPIGwOfflinePaymentConfigurationCache | event null | eventId {}", eventId);
            }
        }else{
            log.info("ClearAPIGwCache | clearAPIGwOfflinePaymentConfigurationCache | eventId null");
        }
    }

    @Override
    @Async
    public void clearAPIGwPaymentSettingsCache(Long eventId){
        if(null != eventId){
            Event event = roEventService.findEventByEventId(eventId);
            if(event != null) {
                log.info("ClearAPIGwCache | clearAPIGwPaymentSettingsCache | eventId | {}",event.getEventId() );
                this.apiGwClearCachePaymentSettings(event.getEventURL());
            }else{
                log.info("ClearAPIGwCache | clearAPIGwPaymentSettingsCache | event null | eventId {}", eventId);
            }
        }else{
            log.info("ClearAPIGwCache | clearAPIGwPaymentSettingsCache | eventId null");
        }
    }

    @Override
    @Async
    public void clearAPIGwBeeFreeDefaultHomePageCache(Long eventId){
        if(null != eventId){
            Event event = roEventService.findEventByEventId(eventId);
            if(event != null) {
                log.info("ClearAPIGwCache | clearAPIGwBeeFreeDefaultHomePageCache | eventId | {}",event.getEventId() );
                this.apiGwClearCacheBeeFreeDefaultHomePage(event.getEventURL());
            }else{
                log.info("ClearAPIGwCache | clearAPIGwBeeFreeDefaultHomePageCache | event null | eventId {}", eventId);
            }
        }else{
            log.info("ClearAPIGwCache | clearAPIGwBeeFreeDefaultHomePageCache | eventId null");
        }
    }

    @Override
    @Async
    public void clearAPIGwBeeFreePublishedPageCache(String eventUrl, String pageUrl) {
        if (StringUtils.isNotBlank(pageUrl) && StringUtils.isNotBlank(eventUrl)) {
            log.info("ClearAPIGwCache | clearAPIGwBeefreePublishedPageCache | eventUrl {} | pageUrl | {}", eventUrl, pageUrl);
            this.apiGwClearCacheBeeFreePublishedPage(eventUrl, pageUrl);
        } else {
            log.info("ClearAPIGwCache | clearAPIGwBeefreePublishedPageCache | eventUrl {} pageUrl {}", eventUrl, pageUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwBeeFreePageCacheByEvent(Event event) {
        if (event != null) {
            List<String> pageUrlList = beeFreePageRepoService.getAllPublishedBeeFreePageUrlsByEventId(event.getEventId());
            String eventUrl = event.getEventURL();
            this.apiGwClearCacheBeeFreeDefaultHomePage(eventUrl);
            if (CollectionUtils.isNotEmpty(pageUrlList)) {
                for (String pageUrl : pageUrlList) {
                    log.info("ClearAPIGwCache | clearAPIGwBeeFreePublishedPageCache | eventUrl {} | pageUrl | {}", eventUrl, pageUrl);
                    this.apiGwClearCacheBeeFreePublishedPage(eventUrl, pageUrl);
                }
            }
        } else {
            log.info("ClearAPIGwCache | clearAPIGwBeeFreePublishedPageCache | event null");
        }
    }

    @Override
    @Async
    public void clearAPIGwEmbedWidgetsSetting(Event event, String widgetType, Long eventOrOrgId) {
        if (event != null) {
            log.info("ClearAPIGwCache | clearAPIGwEmbedWidgetsSetting | eventId : {} , eventOrOrgId : {} , widgetType : {}", event.getEventId(), eventOrOrgId, widgetType);
            this.apiGwClearCacheEmbedWidgetsSetting(event.getEventURL(), widgetType, eventOrOrgId);
        } else {
            log.error("ClearAPIGwCache | clearAPIGwEmbedWidgetsSetting | event null");
        }
    }

    @Override
    @Async
    public void clearAPIGwTicketTypesDescriptionAndFees(String eventUrl, Long recurringEventId) {
        if(StringUtils.isNotBlank(eventUrl)) {
            Event event = roEventService.getEventByURL(eventUrl);
            if(null != event) {
                log.info("ClearAPIGatewayCacheImpl | clearAPIGwTicketTypesDescriptionAndFees | eventUrl | {}", eventUrl);
                this.apiGwTicketTypesDescriptionAndFees(eventUrl, recurringEventId);
            }
        }else{
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwTicketTypesDescriptionAndFees | eventUrl | {} ", eventUrl);
        }
    }



    @Override
    @Async
    public void clearAPIGwSessionTrackCache(Long eventId) {
        if(null != eventId) {
            Event event = roEventService.getEventById(eventId);
            if(null != event) {
                log.info("ClearAPIGatewayCacheImpl | clearAPIGwSessionTrackCache | eventUrl | {}", event.getEventURL());
                this.apiGwSessionTrackCache(event.getEventURL());
            }
        }else{
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwSessionTrackCache | eventUrl | {} ", eventId);
        }
    }

    @Override
    @Async
    public void clearAPIGwSessionSponsorsCache(String eventUrl) {
        Event event = roEventService.getEventByURL(eventUrl);
        if(event != null) {
            log.info("ClearAPIGwCache | clearSessionSponsorCache | eventId | {}",event.getEventId() );
            this.apiGwClearCacheSessionSponsors(eventUrl);
        }else{
            log.info("ClearAPIGwCache | clearSessionSponsorCache | eventurl {}", eventUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwSessionSponsorsCacheByEventId(Long eventId) {
        if(null != eventId) {
            Event event = roEventService.getEventById(eventId);
            if(null != event) {
                log.info("ClearAPIGatewayCacheImpl | clearAPIGwSessionSponsorsCacheByEventId | eventUrl | {}", event.getEventURL());
                this.apiGwClearCacheSessionSponsors(event.getEventURL());
            }
        }
    }

    @Override
    @Async
    public void clearAPIGwSessionExhibitorCacheByEventId(Long eventId) {
        if(null != eventId) {
            Event event = roEventService.getEventById(eventId);
            if(null != event) {
                log.info("ClearAPIGatewayCacheImpl | clearAPIGwSessionExhibitorCacheByEventId | eventUrl | {}", event.getEventURL());
                this.apiGwClearCacheSessionExhibitors(event.getEventURL());
            }
        }
    }
    private void apiGwClearCacheSessionExhibitors(String eventUrl) {
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_SESSION_EXHIBITORS, uriMaps);
    }

    @Override
    @Async
    public void clearAPIGwEventDirectoryPageByWhiteLabelCache(String whiteLabelUrl) {
        if (StringUtils.isNotBlank(whiteLabelUrl)) {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventDirectoryPageByWhiteLabelUrlCache | whiteLabelUrl | {}", whiteLabelUrl);
            this.apiGwEventDirectoryPageByWhiteLabelUrl(whiteLabelUrl);
        } else {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventDirectoryPageByWhiteLabelUrlCache | whiteLabelUrl : {}.",whiteLabelUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwEventDirectoryPageByOrganizerCache(String organizerUrl) {
        if (StringUtils.isNotBlank(organizerUrl)) {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventDirectoryPageByOrganizerUrlCache | organizerUrl | {}", organizerUrl);
            this.apiGwEventDirectoryPageByOrganizerUrl(organizerUrl);
        } else {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventDirectoryPageByOrganizerUrlCache | organizerUrl : {}",organizerUrl);
        }
    }

    @Override
    @Async
    public void clearAPIGwAuctionItemsByEventId(Long eventId) {
        if (NumberUtils.isNumberGreaterThanZero(eventId)) {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwAuctionItemsByEventId | eventId | {}", eventId);
            Event event = roEventService.findEventByEventId(eventId);
            if (event != null) {
                Map<String, String> uriMaps = new HashMap<>();
                uriMaps.put(EVENTURL, event.getEventURL());
                this.clearCacheAPI(apiGatewayBaseURL + EVENT_API_URL + ED_AUCTION_ITEMS, uriMaps);
            }
        } else {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwAuctionItemsByEventId | eventId : {}", eventId);
        }
    }

    private void apiGwEventDirectoryPageByWhiteLabelUrl(String whiteLabelUrl) {
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(WHITELABEL_URL, whiteLabelUrl);
        this.clearCacheAPI(apiGatewayBaseURL + WHITELABEL_API_URL + ED_EVENT_DIRECTORY_PAGE, uriMaps);
    }

    private void apiGwEventDirectoryPageByOrganizerUrl(String organizerUrl) {
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(ORGANIZER_URL, organizerUrl);
        this.clearCacheAPI(apiGatewayBaseURL + ORGANIZER_API_URL + ED_EVENT_DIRECTORY_PAGE, uriMaps);
    }

    private void apiGwSessionTrackCache(String eventUrl) {
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);

        this.clearCacheAPI(apiGatewayBaseURL + ED_SESSION_TRACKS, uriMaps);
    }

    private void apiGwTicketTypesDescriptionAndFees(String eventUrl, Long recurringEventId) {
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        String uri = apiGatewayBaseURL + ED_TICKETING_TICKET_TYPES_DESC_AND_FEE;

       if(recurringEventId != null){
            uriMaps.put("recurringEventId", recurringEventId.toString());
            uri = uri + "?recurringEventId={recurringEventId}";
        }

        this.clearCacheAPI(uri, uriMaps);
    }

    private void apiGwClearCacheEmbedWidgetsSetting(String eventUrl, String widgetType, Long eventOrOrgId) {
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        uriMaps.put("id", String.valueOf(eventOrOrgId));
        if (WidgetType.EVENT.name().equalsIgnoreCase(widgetType)) {
            this.clearCacheAPI(apiGatewayBaseURL + ED_EMBED_WIDGET_SETTINGS_TYPE_EVENT, uriMaps);
        } else if (WidgetType.ORGANIZER.name().equalsIgnoreCase(widgetType)) {
            this.clearCacheAPI(apiGatewayBaseURL + ED_EMBED_WIDGET_SETTINGS_TYPE_ORGANIZER, uriMaps);
        } else {
            log.error("apiGwClearCacheEmbedWidgetsSetting no matched widgetType found : {}", widgetType);
        }
    }


    @Override
    @Async
    public void clearAPIGwEventAuctionSettingCache(String eventUrl) {
        if (StringUtils.isNotBlank(eventUrl)) {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventAuctionSettingCache | eventUrl | {}", eventUrl);
            this.apiGwClearCacheEventAuctionSetting(eventUrl);
        } else {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventAuctionSettingCache | eventUrl : {}.",eventUrl);
        }
    }

    private void apiGwClearCacheEventAuctionSetting(String eventUrl){
        Map<String, String> uriMaps = new HashMap<>();
        uriMaps.put(EVENTURL, eventUrl);
        this.clearCacheAPI(apiGatewayBaseURL + EVENT_API_URL + ED_AUCTION_SETTING, uriMaps);
    }

    @Override
    @Async
    public void clearAPIGwEventAuctionTotalFundRaisedCache(String eventUrl) {
        if (StringUtils.isNotBlank(eventUrl)) {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventAuctionTotalFundRaisedCache | eventUrl | {}", eventUrl);
            Map<String, String> uriMaps = new HashMap<>();
            uriMaps.put(EVENTURL, eventUrl);
            this.clearCacheAPI(apiGatewayBaseURL + EVENT_API_URL + ED_AUCTION_TOTAL_FUND_RAISED, uriMaps);
        } else {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwEventAuctionTotalFundRaisedCache | eventUrl : {}.",eventUrl);
        }
    }
    @Override
    @Async
    public void clearAPIGwTicketTypesRemainingCountCache(String eventUrl) {
        if (StringUtils.isNotBlank(eventUrl)) {
            log.info("clearAPIGwTicketTypesRemainingCountCache  | eventUrl | {}", eventUrl);
            Map<String, String> uriMaps = new HashMap<>();
            uriMaps.put(EVENTURL, eventUrl);
            this.clearCacheAPI(apiGatewayBaseURL + EVENT_API_URL + ED_TICKETING_TICKET_TYPES_REMAINING_COUNT, uriMaps);
        }else {
            log.info("ClearAPIGatewayCacheImpl | clearAPIGwTicketTypesRemainingCountCache | eventUrl : {}.",eventUrl);
        }
    }

}

