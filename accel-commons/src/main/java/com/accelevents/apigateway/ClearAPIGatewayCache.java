package com.accelevents.apigateway;

import com.accelevents.domain.*;
import com.accelevents.domain.session_speakers.Speaker;

public interface ClearAPIGatewayCache {

    void clearAPIGwEventCache(String eventUrl);
    void clearAPIGwEventCache(long eventId);
    void clearAPIGwEventCacheByUrl(String eventUrl);
    void clearAPIGwTicketingCache(Ticketing ticketing);
    void clearAPIGwTicketTypes(String eventUrl, String accessCode, Long recurringId);
    void clearAPIGwTicketTypes(String eventUrl);
    void clearAPIGwSpeakerPortalCache(String eventUrl);
    void clearAPIGwSpeakerPortalCache(Long eventId);
    void clearAPIGwAllSpeakerPortalCache(Iterable<Speaker> speakers);
    void clearAPIGwTagAndTrackCache(String eventUrl, String type);
    void clearAPIGwVESettingsCache(String eventUrl);
    void clearAPIGwVESettingsCache(Long eventId);
    void clearAPIGwVESettingsLanguageLabelCache(String eventUrl, String languageCode);
    void clearAPIGwWLSettingsHostBaseUrlCache(String hostBaseUrl);
    void clearAPIGwWLSettingsCache(String whiteLabelURL);
    void clearAPIGwSponsorsCache(String eventUrl);
    void clearCacheVatTax(VatTax vatTax);
    void clearAPIGwTransactionFeeCache(String eventUrl);
    void clearAPIGwTicketingTypesCategoriesCache(String eventUrl);
    void clearAPIGwOfflinePaymentConfigurationCache(Long eventId);
    void clearAPIGwPaymentSettingsCache(Long eventId);
    void clearAPIGwBeeFreeDefaultHomePageCache(Long eventId);
    void clearAPIGwBeeFreePublishedPageCache(String eventUrl, String pageUrl);
    void clearAPIGwBeeFreePageCacheByEvent(Event event);
    void clearAPIGwEmbedWidgetsSetting(Event event,String widgetType,Long eventOrOrgId);

    void clearAPIGwTicketTypesDescriptionAndFees(String event, Long reccuringEventId);

    void clearAPIGwSessionTrackCache(Long eventId);

    void clearAPIGwSessionSponsorsCache(String eventURL);

    void clearAPIGwSessionSponsorsCacheByEventId(Long eventId);

    void clearAPIGwSessionExhibitorCacheByEventId(Long eventId);

    void clearAPIGwEventDirectoryPageByWhiteLabelCache(String whiteLabelUrl);
    void clearAPIGwEventDirectoryPageByOrganizerCache(String organizerUrl);
    void clearAPIGwAuctionItemsByEventId(Long eventId);

    void clearAPIGwEventAuctionSettingCache(String eventUrl);
    void clearAPIGwEventAuctionTotalFundRaisedCache(String eventUrl);

    void clearAPIGwTicketTypesRemainingCountCache(String eventUrl);
}
