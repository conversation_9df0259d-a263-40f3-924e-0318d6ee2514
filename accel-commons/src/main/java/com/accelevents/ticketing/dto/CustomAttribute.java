package com.accelevents.ticketing.dto;

import com.accelevents.domain.enums.AttributeType;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.TicketAttributesDefaultRequirement;

public class CustomAttribute {

    private Long id;

	private AttributeValueType attributeType;

    private AttributeType attributeFormType;

	private String atrributeName;

	private boolean isAttribute;

	private boolean enabledForTicketHolder;

	private boolean requiredForTicketHolder;

	private boolean enabledForTicketPurchaser;

	private boolean requiredForTicketPurchaser;

	private String defaultValue;

    private String defaultValueJsonPurchaser;

    private String defaultValueJsonHolder;
	
	private String eventTicketTypeId;

    private String holderRequiredTicketTypeId;
    private String buyerRequiredTicketTypeId;
    private String holderOptionalTicketTypeId;
    private String buyerOptionalTicketTypeId;

    private String holderRegistrationHiddenTicketTypeId;
    private String purchaserRegistrationHiddenTicketTypeId;

	private boolean hiddenForPurchaser;

	private boolean hiddenForHolder;

    private boolean invisibleForPurchaser;

    private boolean invisibleForHolder;

    private boolean isCapacityHiddenForPurchaser;

    private boolean isCapacityHiddenForHolder;

	private DataType dataType;

    private Long selectedAnswer;

    private Long parentQuestionId;

    private boolean hiddenForPurchaserRegistration;

    private boolean hiddenForHolderRegistration;

    private String buyerDescription;

    private String holderDescription;

    private TicketAttributesDefaultRequirement holderDefaultRequirement;
    private TicketAttributesDefaultRequirement buyerDefaultRequirement;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public DataType getDataType() {
		return dataType;
	}

	public void setDataType(DataType dataType) {
		this.dataType = dataType;
	}

	public AttributeValueType getAttributeType() {
		return attributeType;
	}

	public void setAttributeType(AttributeValueType attributeType) {
		this.attributeType = attributeType;
	}

	public String getAtrributeName() {
		return atrributeName;
	}

	public void setAtrributeName(String atrributeName) {
		this.atrributeName = atrributeName;
	}

	public boolean isAttribute() {
		return isAttribute;
	}

	public void setAttribute(boolean isAttribute) {
		this.isAttribute = isAttribute;
	}

	public boolean isEnabledForTicketHolder() {
		return enabledForTicketHolder;
	}

	public void setEnabledForTicketHolder(boolean enabledForTicketHolder) {
		this.enabledForTicketHolder = enabledForTicketHolder;
	}

	public boolean isEnabledForTicketPurchaser() {
		return enabledForTicketPurchaser;
	}

	public void setEnabledForTicketPurchaser(boolean enabledForTicketPurchaser) {
		this.enabledForTicketPurchaser = enabledForTicketPurchaser;
	}

	public String getDefaultValue() {
		return defaultValue;
	}

	public void setDefaultValue(String defaultValue) {
		this.defaultValue = defaultValue;
	}

	public boolean isRequiredForTicketHolder() {
		return requiredForTicketHolder;
	}

	public void setRequiredForTicketHolder(boolean requiredForTicketHolder) {
		this.requiredForTicketHolder = requiredForTicketHolder;
	}

	public boolean isRequiredForTicketPurchaser() {
		return requiredForTicketPurchaser;
	}

	public void setRequiredForTicketPurchaser(boolean requiredForTicketPurchaser) {
		this.requiredForTicketPurchaser = requiredForTicketPurchaser;
	}

	public String getEventTicketTypeId() {
		return eventTicketTypeId;
	}

	public void setEventTicketTypeId(String eventTicketTypeId) {
		this.eventTicketTypeId = eventTicketTypeId;
	}

	public boolean isHiddenForPurchaser() {
		return hiddenForPurchaser;
	}

	public void setHiddenForPurchaser(boolean hiddenForPurchaser) {
		this.hiddenForPurchaser = hiddenForPurchaser;
	}

	public boolean isHiddenForHolder() {
		return hiddenForHolder;
	}

	public void setHiddenForHolder(boolean hiddenForHolder) {
		this.hiddenForHolder = hiddenForHolder;
	}

    public String getDefaultValueJsonPurchaser() {
        return defaultValueJsonPurchaser;
    }

    public void setDefaultValueJsonPurchaser(String defaultValueJsonPurchaser) {
        this.defaultValueJsonPurchaser = defaultValueJsonPurchaser;
    }

    public String getDefaultValueJsonHolder() {
        return defaultValueJsonHolder;
    }

    public void setDefaultValueJsonHolder(String defaultValueJsonHolder) {
        this.defaultValueJsonHolder = defaultValueJsonHolder;
    }

    public Long getSelectedAnswer() {
        return selectedAnswer;
    }

    public void setSelectedAnswer(Long selectedAnswer) {
        this.selectedAnswer = selectedAnswer;
    }

    public Long getParentQuestionId() {
        return parentQuestionId;
    }

    public void setParentQuestionId(Long parentQuestionId) {
        this.parentQuestionId = parentQuestionId;
    }

    public boolean isInvisibleForPurchaser() {
        return invisibleForPurchaser;
    }

    public void setInvisibleForPurchaser(boolean invisibleForPurchaser) {
        this.invisibleForPurchaser = invisibleForPurchaser;
    }

    public boolean isInvisibleForHolder() {
        return invisibleForHolder;
    }

    public void setInvisibleForHolder(boolean invisibleForHolder) {
        this.invisibleForHolder = invisibleForHolder;
    }

    public boolean isCapacityHiddenForPurchaser() {
        return isCapacityHiddenForPurchaser;
    }

    public void setCapacityHiddenForPurchaser(boolean capacityHiddenForPurchaser) {
        isCapacityHiddenForPurchaser = capacityHiddenForPurchaser;
    }

    public boolean isCapacityHiddenForHolder() {
        return isCapacityHiddenForHolder;
    }

    public void setCapacityHiddenForHolder(boolean capacityHiddenForHolder) {
        isCapacityHiddenForHolder = capacityHiddenForHolder;
    }

    public String getHolderRequiredTicketTypeId() {
        return holderRequiredTicketTypeId;
    }

    public void setHolderRequiredTicketTypeId(String holderRequiredTicketTypeId) {
        this.holderRequiredTicketTypeId = holderRequiredTicketTypeId;
    }

    public String getBuyerRequiredTicketTypeId() {
        return buyerRequiredTicketTypeId;
    }

    public void setBuyerRequiredTicketTypeId(String buyerRequiredTicketTypeId) {
        this.buyerRequiredTicketTypeId = buyerRequiredTicketTypeId;
    }

    public String getHolderOptionalTicketTypeId() {
        return holderOptionalTicketTypeId;
    }

    public void setHolderOptionalTicketTypeId(String holderOptionalTicketTypeId) {
        this.holderOptionalTicketTypeId = holderOptionalTicketTypeId;
    }

    public String getBuyerOptionalTicketTypeId() {
        return buyerOptionalTicketTypeId;
    }

    public void setBuyerOptionalTicketTypeId(String buyerOptionalTicketTypeId) {
        this.buyerOptionalTicketTypeId = buyerOptionalTicketTypeId;
    }

    public AttributeType getAttributeFormType() { return attributeFormType;}

    public void setAttributeFormType(AttributeType attributeFormType) { this.attributeFormType = attributeFormType; }

    public boolean isHiddenForPurchaserRegistration() {
        return hiddenForPurchaserRegistration;
    }

    public void setHiddenForPurchaserRegistration(boolean hiddenForPurchaserRegistration) {
        this.hiddenForPurchaserRegistration = hiddenForPurchaserRegistration;
    }

    public boolean isHiddenForHolderRegistration() {
        return hiddenForHolderRegistration;
    }

    public void setHiddenForHolderRegistration(boolean hiddenForHolderRegistration) {
        this.hiddenForHolderRegistration = hiddenForHolderRegistration;
    }

    public String getHolderRegistrationHiddenTicketTypeId() {
        return holderRegistrationHiddenTicketTypeId;
    }

    public void setHolderRegistrationHiddenTicketTypeId(String holderRegistrationHiddenTicketTypeId) {
        this.holderRegistrationHiddenTicketTypeId = holderRegistrationHiddenTicketTypeId;
    }

    public String getPurchaserRegistrationHiddenTicketTypeId() {
        return purchaserRegistrationHiddenTicketTypeId;
    }

    public void setPurchaserRegistrationHiddenTicketTypeId(String purchaserRegistrationHiddenTicketTypeId) {
        this.purchaserRegistrationHiddenTicketTypeId = purchaserRegistrationHiddenTicketTypeId;
    }

    public String getHolderDescription() {
        return holderDescription;
    }

    public void setHolderDescription(String holderDescription) {
        this.holderDescription = holderDescription;
    }

    public String getBuyerDescription() {
        return buyerDescription;
    }

    public void setBuyerDescription(String buyerDescription) {
        this.buyerDescription = buyerDescription;
    }

    public TicketAttributesDefaultRequirement getHolderDefaultRequirement() {
        return holderDefaultRequirement;
    }

    public void setHolderDefaultRequirement(TicketAttributesDefaultRequirement holderDefaultRequirement) {
        this.holderDefaultRequirement = holderDefaultRequirement;
    }

    public TicketAttributesDefaultRequirement getBuyerDefaultRequirement() {
        return buyerDefaultRequirement;
    }

    public void setBuyerDefaultRequirement(TicketAttributesDefaultRequirement buyerDefaultRequirement) {
        this.buyerDefaultRequirement = buyerDefaultRequirement;
    }
}

