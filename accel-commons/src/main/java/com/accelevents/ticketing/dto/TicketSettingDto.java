package com.accelevents.ticketing.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Schema(description ="ticketingSetting")
public class TicketSettingDto {

	@Schema(description ="list of attribute will be display")
	@NotEmpty
	@Valid
	private List<TicketSettingAttributeDto> attributes = new ArrayList<>();

	@Schema(description ="all attributes are for purchaser or for ticket holder")
	@NotNull
	private Boolean holderAttribute;

	@Schema(description ="all attributes are for purchaser or for ticket holder")
	@NotNull
	private Boolean collectAddOnAttribute;

	@Schema(description ="Allow editing of attendee info")
	@NotNull
	private Boolean allowAttendeeToEditInfo;

    @Schema(description ="Allow only unique ticket holder emails")
    private Boolean isUniqueTicketHolderEmail;

    @Schema(description ="show member count in checkout")
    private boolean showMemberCountInCheckout;

    @Schema(description ="Allow only unique ticket holder emails")
    private boolean isUniqueTicketBuyerEmail;

	public List<TicketSettingAttributeDto> getAttributes() {
		return attributes;
	}

	public void addAtrtibute(TicketSettingAttributeDto attribute) {
		this.attributes.add(attribute);
	}

	public Boolean getHolderAttribute() {
		return holderAttribute;
	}

	public void setHolderAttribute(Boolean holderAttribute) {
		this.holderAttribute = holderAttribute;
	}

	public Boolean getAllowAttendeeToEditInfo() {
		return allowAttendeeToEditInfo;
	}

	public void setAllowAttendeeToEditInfo(Boolean allowAttendeeToEditInfo) {
		this.allowAttendeeToEditInfo = allowAttendeeToEditInfo;
	}

	public Boolean getCollectAddOnAttribute() {
		return collectAddOnAttribute;
	}

	public void setCollectAddOnAttribute(Boolean collectAddOnAttribute) {
		this.collectAddOnAttribute = collectAddOnAttribute;
	}

    public boolean isShowMemberCountInCheckout() {
        return showMemberCountInCheckout;
    }

    public void setShowMemberCountInCheckout(boolean showMemberCountInCheckout) {
        this.showMemberCountInCheckout = showMemberCountInCheckout;
    }

    public Boolean getUniqueTicketHolderEmail() {
        return isUniqueTicketHolderEmail;
    }

    public void setUniqueTicketHolderEmail(Boolean uniqueTicketHolderEmail) {
        isUniqueTicketHolderEmail = uniqueTicketHolderEmail;
    }

    public boolean isUniqueTicketBuyerEmail() {
        return isUniqueTicketBuyerEmail;
    }

    public void setUniqueTicketBuyerEmail(boolean uniqueTicketBuyerEmail) {
        isUniqueTicketBuyerEmail = uniqueTicketBuyerEmail;
    }

    @Override
    public String toString() {
        return "TicketSettingDto{" +
                "attributes=" + attributes +
                ", holderAttribute='" + holderAttribute +
                ", collectAddOnAttribute=" + collectAddOnAttribute +
                ", allowAttendeeToEditInfo=" + allowAttendeeToEditInfo +
                ", isUniqueTicketHolderEmail=" + isUniqueTicketHolderEmail +
                ", isUniqueTicketBuyerEmail=" + isUniqueTicketBuyerEmail +
                ", showMemberCountInCheckout=" + showMemberCountInCheckout +
                '}';
    }
}
