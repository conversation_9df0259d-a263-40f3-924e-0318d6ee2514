package com.accelevents.ticketing.dto;

import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.ViewFilterDetailsDTO;

public class DisplaySharingViewDto {

    private boolean isRequiredPassword;

    private boolean isTurnOffSharing;

    private ViewFilterDetailsDTO viewFilterDetailsDTO;

    private String viewName;

    private DataTableResponse attendeeData;

    public boolean isRequiredPassword() { return isRequiredPassword; }

    public void setRequiredPassword(boolean requiredPassword) { isRequiredPassword = requiredPassword; }

    public boolean isTurnOffSharing() { return isTurnOffSharing; }

    public void setTurnOffSharing(boolean turnOffSharing) { isTurnOffSharing = turnOffSharing; }

    public ViewFilterDetailsDTO getViewFilterDetailsDTO() { return viewFilterDetailsDTO; }

    public void setViewFilterDetailsDTO(ViewFilterDetailsDTO viewFilterDetailsDTO) { this.viewFilterDetailsDTO = viewFilterDetailsDTO; }

    public String getViewName() { return viewName; }

    public void setViewName(String viewName) { this.viewName = viewName; }

    public DataTableResponse getAttendeeData() { return attendeeData; }

    public void setAttendeeData(DataTableResponse attendeeData) { this.attendeeData = attendeeData; }
}
