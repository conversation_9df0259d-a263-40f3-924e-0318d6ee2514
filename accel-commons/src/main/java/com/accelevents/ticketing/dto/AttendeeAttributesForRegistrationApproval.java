package com.accelevents.ticketing.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class AttendeeAttributesForRegistrationApproval {


    private List<DisplayAttributeDto> buyerAttributes;

    private List<DisplayAttributeDto> buyerQuestions;

    private List<DisplayNestedQueDto> buyerNestedQuestion;

    @Schema(description = "contains detail for form creation to collect ticket holder data")
    private List<AttendeeAttributeWithTypeAndTableDto> attendees;

    @Schema(description = "can be use to display discount box, if coupon is available then value will be true otherwise false")
    private boolean discountCoupon;

    public List<DisplayAttributeDto> getBuyerAttributes() { return buyerAttributes; }

    public void setBuyerAttributes(List<DisplayAttributeDto> buyerAttributes) { this.buyerAttributes = buyerAttributes; }

    public List<DisplayAttributeDto> getBuyerQuestions() { return buyerQuestions; }

    public void setBuyerQuestions(List<DisplayAttributeDto> buyerQuestions) { this.buyerQuestions = buyerQuestions; }

    public List<DisplayNestedQueDto> getBuyerNestedQuestion() { return buyerNestedQuestion; }

    public void setBuyerNestedQuestion(List<DisplayNestedQueDto> buyerNestedQuestion) { this.buyerNestedQuestion = buyerNestedQuestion; }

    public List<AttendeeAttributeWithTypeAndTableDto> getAttendees() { return attendees; }

    public void setAttendees(List<AttendeeAttributeWithTypeAndTableDto> attendees) { this.attendees = attendees; }

    public boolean isDiscountCoupon() { return discountCoupon; }

    public void setDiscountCoupon(boolean discountCoupon) { this.discountCoupon = discountCoupon; }

}
