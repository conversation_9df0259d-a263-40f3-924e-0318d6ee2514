package com.accelevents.ticketing.dto;

import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Size;

@Schema
public class AttendeeDtoV2 extends FeeDTO {

	@Schema(description = "first name of attendee")
    @Size(max = Constants.NAME_SIZE, message = Constants.FIRST_NAME_SIZE_MSG)
	private String firstName;
	@Schema(description = "last name of attendee")
    @Size(max = Constants.NAME_SIZE, message = Constants.LAST_NAME_SIZE_MSG)
	private String lastName;
	@Schema(description = "email of attendee")
    @Size(max = Constants.EMAIL_SIZE, message = Constants.EMAIL_SIZE_MSG)
	private String email;
	@Schema(description = "number of tickets")
	private int qty;
	@Schema(description = "type of ticket")
	private String ticketType;
	@Schema(description = "type of ticket bundle, value will null if found individual ticket bundle type")
	private String bundleType;
	@Schema(description = "total amount paid for a ticket")
	private double paidAmount;

    @Schema(description = "refunded amount")
    private double refundedAmount;

    @Schema(description = "ticket payment status")
    private TicketPaymentStatus ticketPaymentStatus;

    @Schema(description = "attendee order status, e.g. paid, unpaid")
	private String status;
	@Schema(description = "event ticketing id")
	private long eventTicketingId;
	@Schema(description = "is ticket type is table")
	private boolean isTable;
	@Schema(description = "seat number")
	private String seatNumber;
	@Schema(description = "data type")
	private DataType dataType;
	private boolean blocked;
	private String userId;
	private long phoneNumber;
	private  boolean isPaidTicket;
    private boolean isResendTicketExchangeEmail;

    public boolean cardPayment;
    private boolean nonTransferable;

    private String lastUpdated;

	public DataType getDataType() {
		return dataType;
	}

	public void setDataType(DataType dataType) {
		this.dataType = dataType;
	}

	private long ticketTypeId;

	public double getPaidAmount() {
		return DoubleHelper.roundValueTwoDecimal(paidAmount);
	}

	public void setPaidAmount(double paidAmount) {
		this.paidAmount = paidAmount;
	}


    @Schema(description = "Ticket price")
    private double ticketPrice;


    public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public int getQty() {
		return qty;
	}

	public void setQty(int qty) {
		this.qty = qty;
	}

	public String getTicketType() {
		return ticketType;
	}

	public void setTicketType(String ticketType) {
		this.ticketType = ticketType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public long getEventTicketingId() {
		return eventTicketingId;
	}

	public void setEventTicketingId(long eventTicketingId) {
		this.eventTicketingId = eventTicketingId;
	}

	public String getBundleType() {
		return bundleType;
	}

	public void setBundleType(String bundleType) {
		this.bundleType = bundleType;
	}

	public boolean isTable() {
		return isTable;
	}

	public void setTable(boolean table) {
		isTable = table;
	}

	public String getSeatNumber() {
		return seatNumber;
	}

	public void setSeatNumber(String seatNumber) {
		this.seatNumber = seatNumber;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

    public boolean isPaidTicket() {
        return isPaidTicket;
    }

    public void setPaidTicket(boolean paidTicket) {
        isPaidTicket = paidTicket;
    }

    public double getRefundedAmount() {
        return refundedAmount;
    }

    public void setRefundedAmount(double refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public AttendeeDtoV2 (AttendeeDto attendeeDto){
		super.setCcFee(attendeeDto.getCcFee());
		super.setTicketingFee(attendeeDto.getTicketingFee());
		super.setSalesTaxFee(attendeeDto.getSalesTaxFee());
        super.setVatTaxFee(attendeeDto.getVatTaxFee());
	}

	public AttendeeDtoV2 (){
	}

	public static AttendeeDtoV2 convertToAttendeeDtoV2(AttendeeDto attendeeDto) {
		AttendeeDtoV2 attendeeDtoV2 = new AttendeeDtoV2(attendeeDto);
		attendeeDtoV2.setFirstName(attendeeDto.getFirstName());
		attendeeDtoV2.setLastName(attendeeDto.getLastName());
		attendeeDtoV2.setEmail(attendeeDto.getEmail());
		attendeeDtoV2.setEventTicketingId(attendeeDto.getEventTicketingId());
		attendeeDtoV2.setTicketTypeId(attendeeDto.getTicketTypeId());
		attendeeDtoV2.setPaidAmount(attendeeDto.getPaid() - attendeeDto.getRefundedAmount());
        attendeeDtoV2.setRefundedAmount(attendeeDto.getRefundedAmount());
        attendeeDtoV2.setTicketPaymentStatus(attendeeDto.getTicketPaymentStatus());
		attendeeDtoV2.setQty(attendeeDto.getQty());
		attendeeDtoV2.setStatus(attendeeDto.getStatus());
		attendeeDtoV2.setTicketType(attendeeDto.getTicketType());
		attendeeDtoV2.setTable(attendeeDto.isTable());
		attendeeDtoV2.setBundleType(attendeeDto.getBundleType());
		attendeeDtoV2.setSeatNumber(attendeeDto.getSeatNumber());
		attendeeDtoV2.setDataType(attendeeDto.getDataType());
		attendeeDtoV2.setBlocked(attendeeDto.isBlocked());
		attendeeDtoV2.setUserId(attendeeDto.getUserId());
		attendeeDtoV2.setPhoneNumber(attendeeDto.getPhoneNumber());
		attendeeDtoV2.setPaidTicket(attendeeDto.isPaidTicket());
        attendeeDtoV2.setTicketPrice(attendeeDto.getTicketPrice());
        attendeeDtoV2.setResendTicketExchangeEmail(attendeeDto.isResendTicketExchangeEmail());
        attendeeDtoV2.setCardPayment(attendeeDto.isCardPayment());
        attendeeDtoV2.setNonTransferable(attendeeDto.isNonTransferable());
        attendeeDtoV2.setLastUpdated(attendeeDto.getLastUpdated());
		return attendeeDtoV2;
	}

    private void setCardPayment(boolean cardPayment) {
        this.cardPayment = cardPayment;
    }

    private void setTicketTypeId(long ticketTypeId) {
		this.ticketTypeId = ticketTypeId;
	}


	public long getTicketTypeId() {
		return ticketTypeId;
	}

	public boolean isBlocked() {
		return blocked;
	}

	public void setBlocked(boolean blocked) {
		this.blocked = blocked;
	}

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public TicketPaymentStatus getTicketPaymentStatus() {
        return ticketPaymentStatus;
    }

    public void setTicketPaymentStatus(TicketPaymentStatus ticketPaymentStatus) {
        this.ticketPaymentStatus = ticketPaymentStatus;
    }
    public double getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(double ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public boolean isResendTicketExchangeEmail() { return isResendTicketExchangeEmail; }

    public void setResendTicketExchangeEmail(boolean resendTicketExchangeEmail) { isResendTicketExchangeEmail = resendTicketExchangeEmail;}

    public boolean isNonTransferable() {
        return nonTransferable;
    }

    public void setNonTransferable(boolean nonTransferable) {
        this.nonTransferable = nonTransferable;
    }

    public String getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(String lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
}