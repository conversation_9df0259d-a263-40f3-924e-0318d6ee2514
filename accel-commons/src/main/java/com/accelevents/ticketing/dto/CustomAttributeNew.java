package com.accelevents.ticketing.dto;

import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.TicketAttributesDefaultRequirement;

import java.util.List;

public class CustomAttributeNew {

    private List<CustomAttributeCommon> customAttributeCommons;

    private AttributeValueType attributeType;

    private boolean isAttribute;

    private boolean enabledForTicketHolder;

    private boolean requiredForTicketHolder;

    private boolean enabledForTicketPurchaser;

    private boolean requiredForTicketPurchaser;

    private boolean hiddenForPurchaser;

    private boolean hiddenForHolder;

    private boolean hiddenForPurchaserRegistration;

    private boolean hiddenForHolderRegistration;

    private boolean invisibleForHolder;

    private boolean invisibleForPurchaser;

    private boolean isCapacityHiddenForPurchaser;

    private boolean isCapacityHiddenForHolder;

    private DataType dataType;

    private TicketAttributesDefaultRequirement holderDefaultRequirement = TicketAttributesDefaultRequirement.REQUIRED;

    private TicketAttributesDefaultRequirement buyerDefaultRequirement = TicketAttributesDefaultRequirement.REQUIRED;

    public List<CustomAttributeCommon> getCustomAttributeCommons() {
        return customAttributeCommons;
    }

    public void setCustomAttributeCommons(List<CustomAttributeCommon> customAttributeCommons) {
        this.customAttributeCommons = customAttributeCommons;
    }

    public AttributeValueType getAttributeType() {
        return attributeType;
    }

    public void setAttributeType(AttributeValueType attributeType) {
        this.attributeType = attributeType;
    }

    public boolean isAttribute() {
        return isAttribute;
    }

    public void setAttribute(boolean attribute) {
        isAttribute = attribute;
    }

    public boolean isEnabledForTicketHolder() {
        return enabledForTicketHolder;
    }

    public void setEnabledForTicketHolder(boolean enabledForTicketHolder) {
        this.enabledForTicketHolder = enabledForTicketHolder;
    }

    public boolean isRequiredForTicketHolder() {
        return requiredForTicketHolder;
    }

    public void setRequiredForTicketHolder(boolean requiredForTicketHolder) {
        this.requiredForTicketHolder = requiredForTicketHolder;
    }

    public boolean isEnabledForTicketPurchaser() {
        return enabledForTicketPurchaser;
    }

    public void setEnabledForTicketPurchaser(boolean enabledForTicketPurchaser) {
        this.enabledForTicketPurchaser = enabledForTicketPurchaser;
    }

    public boolean isRequiredForTicketPurchaser() {
        return requiredForTicketPurchaser;
    }

    public void setRequiredForTicketPurchaser(boolean requiredForTicketPurchaser) {
        this.requiredForTicketPurchaser = requiredForTicketPurchaser;
    }

    public boolean isHiddenForPurchaser() {
        return hiddenForPurchaser;
    }

    public void setHiddenForPurchaser(boolean hiddenForPurchaser) {
        this.hiddenForPurchaser = hiddenForPurchaser;
    }

    public boolean isHiddenForHolder() {
        return hiddenForHolder;
    }

    public void setHiddenForHolder(boolean hiddenForHolder) {
        this.hiddenForHolder = hiddenForHolder;
    }

    public DataType getDataType() {
        return dataType;
    }

    public void setDataType(DataType dataType) {
        this.dataType = dataType;
    }

    public boolean isInvisibleForHolder() {
        return invisibleForHolder;
    }

    public void setInvisibleForHolder(boolean invisibleForHolder) {
        this.invisibleForHolder = invisibleForHolder;
    }

    public boolean isInvisibleForPurchaser() {
        return invisibleForPurchaser;
    }

    public void setInvisibleForPurchaser(boolean invisibleForPurchaser) {
        this.invisibleForPurchaser = invisibleForPurchaser;
    }

    public boolean isCapacityHiddenForPurchaser() {
        return isCapacityHiddenForPurchaser;
    }

    public void setCapacityHiddenForPurchaser(boolean capacityHiddenForPurchaser) {
        isCapacityHiddenForPurchaser = capacityHiddenForPurchaser;
    }

    public boolean isCapacityHiddenForHolder() {
        return isCapacityHiddenForHolder;
    }

    public void setCapacityHiddenForHolder(boolean capacityHiddenForHolder) {
        isCapacityHiddenForHolder = capacityHiddenForHolder;
    }

    public boolean isHiddenForPurchaserRegistration() {
        return hiddenForPurchaserRegistration;
    }

    public void setHiddenForPurchaserRegistration(boolean hiddenForPurchaserRegistration) {
        this.hiddenForPurchaserRegistration = hiddenForPurchaserRegistration;
    }

    public boolean isHiddenForHolderRegistration() {
        return hiddenForHolderRegistration;
    }

    public void setHiddenForHolderRegistration(boolean hiddenForHolderRegistration) {
        this.hiddenForHolderRegistration = hiddenForHolderRegistration;
    }

    public TicketAttributesDefaultRequirement getHolderDefaultRequirement() {
        return holderDefaultRequirement;
    }

    public void setHolderDefaultRequirement(TicketAttributesDefaultRequirement holderDefaultRequirement) {
        this.holderDefaultRequirement = holderDefaultRequirement;
    }

    public TicketAttributesDefaultRequirement getBuyerDefaultRequirement() {
        return buyerDefaultRequirement;
    }

    public void setBuyerDefaultRequirement(TicketAttributesDefaultRequirement buyerDefaultRequirement) {
        this.buyerDefaultRequirement = buyerDefaultRequirement;
    }
}
