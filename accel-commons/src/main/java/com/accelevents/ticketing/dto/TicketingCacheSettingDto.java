package com.accelevents.ticketing.dto;

import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.EventListingStatus;
import com.accelevents.messages.EnumEventVenue;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema
public class TicketingCacheSettingDto {

    @Schema(description = "start date of ticketing sale")
    private Date startDate;
    @Schema(description = "end date of ticketing sale")
    private Date endDate;
    @Schema(description = "ticketing venue")
    private String address;
    @Schema(description ="Event listing status")
    private EventListingStatus eventListingStatus;
    @Schema(description ="Event venue status")
    private EventFormat eventFormat;
    @Schema(description ="show registration button on display page")
    private boolean showRegistrationButton;
    @Schema(description ="Pre event access time in minutes")
    private Integer preEventAccessMinutes;
    @Schema(description ="show enter event button on Portal")
    private boolean showEnterEventButton = false;
    @Schema(description ="Event venue status")
    private EnumEventVenue eventVenueStatus;
    @Schema(description ="show ticket price on display page")
    private boolean showTicketPrice;
    @Schema(description ="show member count in checkout")
    private boolean showMemberCountInCheckout;
    @Schema(description ="event dates will be hide or not")
    private boolean hideEventDate;
    @Schema(description ="Post event access time in minutes")
    private Integer postEventAccessMinutes;
    @Schema(description ="Is checkout disclaimer confirmation required?")
    private boolean requireDisclaimerConfirmation;
    @Schema(description ="Allow disagree confirmation before checkout?")
    private boolean allowDisagreeDisclaimerConfirmation;
    @Schema(description = "attendee registration request status")
    private String attendeeRegistrationRequestStatus;
    @Schema(description ="Custom Disclaimer")
    private String customDisclaimer;
    @Schema(description = "expo registration request status")
    private String expRegistrationRequestStatus;
    @Schema(description = "speaker registration request status")
    private String speakerRegistrationRequestStatus;
    @Schema(description = "is limit registration email toggle?")
    private boolean isLimitRegistrationEmails;
    @Schema(description ="show remaining tickets on display page?")
    private boolean showRemainingTickets;
    @Schema(description ="Seating chart key, if ticketing is of seating type")
    private String seatingChartKey;
    @Schema(description ="Seating event key, if ticketing is of seating type")
    private String eventKey;
    @Schema(description ="Is collection of holderAttribute Required?")
    private boolean holderAttributeRequired;
    @Schema(description ="is exit Intent Pop up enabled?")
    private boolean exitIntentPopupEnabled;
    @Schema(description ="is attendee registration approval enabled")
    private boolean attendeeRegistrationApproval;
    @Schema(description = "Organization note related to Pay Later Checkout")
    private String orgNote;

    @Schema(description = "Minimum ticket price from all tickets")
    private Double minTicketPrice;

    @Schema(description = "Maximum ticket price from all tickets")
    private Double maxTicketPrice;

    private boolean isUniqueTicketHolderEmail;

    private boolean allowCheckInWithUnpaidTicket;

    private boolean attendeeApprovalCardCaptureEnabled;

    private boolean isUniqueTicketBuyerEmail;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public EventListingStatus getEventListingStatus() {
        return eventListingStatus;
    }

    public void setEventListingStatus(EventListingStatus eventListingStatus) {
        this.eventListingStatus = eventListingStatus;
    }

    public EventFormat getEventFormat() {
        return eventFormat;
    }

    public void setEventFormat(EventFormat eventFormat) {
        this.eventFormat = eventFormat;
    }

    public Integer getPreEventAccessMinutes() {
        return preEventAccessMinutes;
    }

    public void setPreEventAccessMinutes(Integer preEventAccessMinutes) {
        this.preEventAccessMinutes = preEventAccessMinutes;
    }

    public boolean isShowEnterEventButton() {
        return showEnterEventButton;
    }

    public void setShowEnterEventButton(boolean showEnterEventButton) {
        this.showEnterEventButton = showEnterEventButton;
    }

    public EnumEventVenue getEventVenueStatus() {
        return eventVenueStatus;
    }

    public void setEventVenueStatus(EnumEventVenue eventVenueStatus) {
        this.eventVenueStatus = eventVenueStatus;
    }

    public boolean isShowRegistrationButton() {
        return showRegistrationButton;
    }

    public void setShowRegistrationButton(boolean showRegistrationButton) {
        this.showRegistrationButton = showRegistrationButton;
    }

    public boolean isShowTicketPrice() {
        return showTicketPrice;
    }

    public void setShowTicketPrice(boolean showTicketPrice) {
        this.showTicketPrice = showTicketPrice;
    }

    public boolean isShowMemberCountInCheckout() {
        return showMemberCountInCheckout;
    }

    public void setShowMemberCountInCheckout(boolean showMemberCountInCheckout) {
        this.showMemberCountInCheckout = showMemberCountInCheckout;
    }

    public boolean isHideEventDate() {
        return hideEventDate;
    }

    public void setHideEventDate(boolean hideEventDate) {
        this.hideEventDate = hideEventDate;
    }

    public Integer getPostEventAccessMinutes() { return postEventAccessMinutes; }

    public void setPostEventAccessMinutes(Integer postEventAccessMinutes) { this.postEventAccessMinutes = postEventAccessMinutes; }

    public String getAttendeeRegistrationRequestStatus() {
        return attendeeRegistrationRequestStatus;
    }

    public void setAttendeeRegistrationRequestStatus(String attendeeRegistrationRequestStatus) {
        this.attendeeRegistrationRequestStatus = attendeeRegistrationRequestStatus;
    }

    public String getExpRegistrationRequestStatus() {
        return expRegistrationRequestStatus;
    }

    public void setExpRegistrationRequestStatus(String expRegistrationRequestStatus) {
        this.expRegistrationRequestStatus = expRegistrationRequestStatus;
    }

    public String getSpeakerRegistrationRequestStatus() {
        return speakerRegistrationRequestStatus;
    }

    public void setSpeakerRegistrationRequestStatus(String speakerRegistrationRequestStatus) {
        this.speakerRegistrationRequestStatus = speakerRegistrationRequestStatus;
    }

    public boolean isRequireDisclaimerConfirmation() {
        return requireDisclaimerConfirmation;
    }

    public void setRequireDisclaimerConfirmation(boolean requireDisclaimerConfirmation) {
        this.requireDisclaimerConfirmation = requireDisclaimerConfirmation;
    }

    public boolean isAllowDisagreeDisclaimerConfirmation() {
        return allowDisagreeDisclaimerConfirmation;
    }

    public void setAllowDisagreeDisclaimerConfirmation(boolean allowDisagreeDisclaimerConfirmation) {
        this.allowDisagreeDisclaimerConfirmation = allowDisagreeDisclaimerConfirmation;
    }

    public String getCustomDisclaimer() {
        return customDisclaimer;
    }

    public void setCustomDisclaimer(String customDisclaimer) {
        this.customDisclaimer = customDisclaimer;
    }


    public boolean isLimitRegistrationEmails() { return isLimitRegistrationEmails; }

    public void setLimitRegistrationEmails(boolean limitRegistrationEmails) {
        this.isLimitRegistrationEmails = limitRegistrationEmails;
    }

    public boolean isShowRemainingTickets() {
        return showRemainingTickets;
    }

    public void setShowRemainingTickets(boolean showRemainingTickets) {
        this.showRemainingTickets = showRemainingTickets;
    }

    public String getSeatingChartKey() {
        return seatingChartKey;
    }

    public void setSeatingChartKey(String seatingChartKey) {
        this.seatingChartKey = seatingChartKey;
    }

    public String getEventKey() {
        return eventKey;
    }

    public void setEventKey(String eventKey) {
        this.eventKey = eventKey;
    }

    public boolean isHolderAttributeRequired() {
        return holderAttributeRequired;
    }

    public void setHolderAttributeRequired(boolean holderAttributeRequired) {
        this.holderAttributeRequired = holderAttributeRequired;
    }

    public boolean isExitIntentPopupEnabled() {
        return exitIntentPopupEnabled;
    }

    public void setExitIntentPopupEnabled(boolean exitIntentPopupEnabled) {
        this.exitIntentPopupEnabled = exitIntentPopupEnabled;
    }

    public boolean isAttendeeRegistrationApproval() {
        return attendeeRegistrationApproval;
    }

    public void setAttendeeRegistrationApproval(boolean attendeeRegistrationApproval) {
        this.attendeeRegistrationApproval = attendeeRegistrationApproval;
    }

    public String getOrgNote() {
        return orgNote;
    }

    public void setOrgNote(String orgNote) {
        this.orgNote = orgNote;
    }

    public Double getMinTicketPrice() {
        return minTicketPrice;
    }

    public void setMinTicketPrice(Double minTicketPrice) {
        this.minTicketPrice = minTicketPrice;
    }

    public Double getMaxTicketPrice() {
        return maxTicketPrice;
    }

    public void setMaxTicketPrice(Double maxTicketPrice) {
        this.maxTicketPrice = maxTicketPrice;
    }

    public boolean isUniqueTicketHolderEmail() {
        return isUniqueTicketHolderEmail;
    }

    public void setUniqueTicketHolderEmail(boolean uniqueTicketHolderEmail) {
        isUniqueTicketHolderEmail = uniqueTicketHolderEmail;
    }


    public boolean isAllowCheckInWithUnpaidTicket() {
        return allowCheckInWithUnpaidTicket;
    }

    public void setAllowCheckInWithUnpaidTicket(boolean allowCheckInWithUnpaidTicket) {
        this.allowCheckInWithUnpaidTicket = allowCheckInWithUnpaidTicket;
    }

    public boolean isAttendeeApprovalCardCaptureEnabled() {
        return attendeeApprovalCardCaptureEnabled;
    }

    public void setAttendeeApprovalCardCaptureEnabled(boolean attendeeApprovalCardCaptureEnabled) {
        this.attendeeApprovalCardCaptureEnabled = attendeeApprovalCardCaptureEnabled;
    }

    public boolean isUniqueTicketBuyerEmail() {
        return isUniqueTicketBuyerEmail;
    }

    public void setUniqueTicketBuyerEmail(boolean uniqueTicketBuyerEmail) {
        isUniqueTicketBuyerEmail = uniqueTicketBuyerEmail;
    }
}
