package com.accelevents.ticketing.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class EntitlementsAvailabilityDTO implements Serializable {
    private static final long serialVersionUID = 6235501516216548758L;

    private boolean badgePrinting;
    private boolean webinars;
    private boolean advancedEmailMarketing;
    private boolean apiAccess;
    private boolean navigationMenu;

    private boolean approvalWorkflow;

    private boolean mobileApp;
    @JsonProperty("CECreditTracking")
    private  boolean ceCreditTracking;
    private  boolean nativeIntegrations;
    private boolean basicGamification;
    private boolean customGamification;
    private boolean virtualComponents;
    private boolean offlinePayments;
    private boolean audienceBuilder;
    private boolean advancedAnalytics;
    private boolean kioskMode;
    private boolean advancedPageBuilder;
    private boolean customJavaScript;
    private boolean twoFactorAuthentication;
    private boolean customCSS;
    private boolean virtualNetworking;
    private boolean preScheduleMeetings;
    private boolean surveys;
    private boolean branding;
    private  boolean senderNameModification;
    private boolean engageEmails;
    private boolean singleSignOn;
    private boolean eventRequestForm;

    public boolean isEngageEmails() {
        return engageEmails;
    }

    public void setEngageEmails(boolean engageEmails) {
        this.engageEmails = engageEmails;
    }

    public boolean isSenderNameModification() {
        return senderNameModification;
    }

    public void setSenderNameModification(boolean senderNameModification) {
        this.senderNameModification = senderNameModification;
    }

    public boolean isCustomJavaScript() {
        return customJavaScript;
    }

    public void setCustomJavaScript(boolean customJavaScript) {
        this.customJavaScript = customJavaScript;
    }

    public boolean isTwoFactorAuthentication() {
        return twoFactorAuthentication;
    }

    public void setTwoFactorAuthentication(boolean twoFactorAuthentication) {
        this.twoFactorAuthentication = twoFactorAuthentication;
    }

    public boolean isCustomCSS() {
        return customCSS;
    }

    public void setCustomCSS(boolean customCSS) {
        this.customCSS = customCSS;
    }

    public boolean isVirtualNetworking() {
        return virtualNetworking;
    }

    public void setVirtualNetworking(boolean virtualNetworking) {
        this.virtualNetworking = virtualNetworking;
    }

    public boolean isPreScheduleMeetings() {
        return preScheduleMeetings;
    }

    public void setPreScheduleMeetings(boolean preScheduleMeetings) {
        this.preScheduleMeetings = preScheduleMeetings;
    }

    public boolean isSurveys() {
        return surveys;
    }

    public void setSurveys(boolean surveys) {
        this.surveys = surveys;
    }

    public boolean isBranding() {
        return branding;
    }

    public void setBranding(boolean branding) {
        this.branding = branding;
    }

    public boolean isAdvancedPageBuilder() {
        return advancedPageBuilder;
    }

    public void setAdvancedPageBuilder(boolean advancedPageBuilder) {
        this.advancedPageBuilder = advancedPageBuilder;
    }

    public boolean isKioskMode() {
        return kioskMode;
    }

    public void setKioskMode(boolean kioskMode) {
        this.kioskMode = kioskMode;
    }

    public boolean isAdvancedAnalytics() {
        return advancedAnalytics;
    }

    public void setAdvancedAnalytics(boolean advancedAnalytics) {
        this.advancedAnalytics = advancedAnalytics;
    }

    public boolean isNavigationMenu() {
        return navigationMenu;
    }

    public void setNavigationMenu(boolean navigationMenu) {
        this.navigationMenu = navigationMenu;
    }

    public boolean isApprovalWorkflow() {
        return approvalWorkflow;
    }

    public void setApprovalWorkflow(boolean approvalWorkflow) {
        this.approvalWorkflow = approvalWorkflow;
    }

    public boolean isMobileApp() {
        return mobileApp;
    }

    public void setMobileApp(boolean mobileApp) {
        this.mobileApp = mobileApp;
    }
    public boolean isBasicGamification() {
        return basicGamification;
    }

    public void setBasicGamification(boolean basicGamification) {
        this.basicGamification = basicGamification;
    }

    public boolean isCustomGamification() {
        return customGamification;
    }

    public void setCustomGamification(boolean customGamification) {
        this.customGamification = customGamification;
    }

    public boolean isBadgePrinting() {
        return badgePrinting;
    }

    public void setBadgePrinting(boolean badgePrinting) {
        this.badgePrinting = badgePrinting;
    }

    public boolean isWebinars() {
        return webinars;
    }

    public void setWebinars(boolean webinars) {
        this.webinars = webinars;
    }

    public boolean isAdvancedEmailMarketing() {
        return advancedEmailMarketing;
    }

    public void setAdvancedEmailMarketing(boolean advancedEmailMarketing) {
        this.advancedEmailMarketing = advancedEmailMarketing;
    }

    public boolean isApiAccess() {
        return apiAccess;
    }

    public void setApiAccess(boolean apiAccess) {
        this.apiAccess = apiAccess;
    }

    public boolean isCeCreditTracking() {
        return ceCreditTracking;
    }

    public void setCeCreditTracking(boolean ceCreditTracking) {
        this.ceCreditTracking = ceCreditTracking;
    }

    public boolean isNativeIntegrations() {
        return nativeIntegrations;
    }

    public void setNativeIntegrations(boolean nativeIntegrations) {
        this.nativeIntegrations = nativeIntegrations;
    }

    public boolean isVirtualComponents() {
        return virtualComponents;
    }

    public void setVirtualComponents(boolean virtualComponents) {
        this.virtualComponents = virtualComponents;
    }

    public boolean isOfflinePayments() {
        return offlinePayments;
    }

    public void setOfflinePayments(boolean offlinePayments) {
        this.offlinePayments = offlinePayments;
    }

    public boolean isAudienceBuilder() {
        return audienceBuilder;
    }

    public boolean isEventRequestForm() {
        return eventRequestForm;
    }

    public void setEventRequestForm(boolean eventRequestForm) {
        this.eventRequestForm = eventRequestForm;
    }

    public void setAudienceBuilder(boolean audienceBuilder) {
        this.audienceBuilder = audienceBuilder;
    }

    public boolean isSingleSignOn() {
        return singleSignOn;
    }

    public void setSingleSignOn(boolean singleSignOn) {
        this.singleSignOn = singleSignOn;
    }

    public EntitlementsAvailabilityDTO() {

    }
}
