package com.accelevents.ticketing.dto;

import com.accelevents.domain.TicketingOrder.OrderType;
import com.accelevents.helpers.DoubleHelper;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class OrderDtoV2 extends FeeDTO{

	@Schema(description = "field for host order list")
	private long id;
	@Schema(description = "total order amount")
	private double totalAmount;
	@Schema(description = "order status e.g. paid, partially paid or refunded")
	private String status;
	@Schema(description = "order purchaser information")
	private PurchaserInfo purchaser;
	@Schema(description = "order purchase date")
	private String purchaseDate;
	@Schema(description = "type of order, CASH or CARD")
	private OrderType orderType;
	@Schema(description = "card type used while ordering")
	private String cardType;
	@Schema(description = "last four digits of card")
	private String lastFour;
	@Schema(description = "Additional note added for manually entered note on staff or host page")
	private String note;
	@Schema(description = "list of attendees")
	private List<AttendeeDtoV2> attendee;

	@Schema(description = "order currency")
	private String currency;
	@Schema(description = "order timezone")
	private String timezoneId;
    @Schema(description = "order timezone")
    private String equivalentTimezone;
	@Schema(description = "Recurring event date")
	private String recurringEventDate;
	@Schema(description = "Order id")
	private Long orderId;
	@Schema(description = "Delivery mode")
	private String deliveryMode;
    @Schema(description = "is order for speaker")
    private Boolean isSpeakerOrder;
    @Schema(description = "is order for staff")
    private Boolean isStaffOrder;

    @Schema(description = "order last modified date")
    private String orderLastUpdated;

    private UTMTrackSourceDto utmTrackSource;

    @Schema(description = "Coupon code")
    private String couponCode;

    private double dueAmount;
    private String documentLink;
    private String ticketType;
    private boolean disableTicketTransfer;

    public String getTicketType() {
        return ticketType;
    }

    public void setTicketType(String ticketType) {
        this.ticketType = ticketType;
    }

    public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public double getTotalAmount() {
		return DoubleHelper.roundValueTwoDecimal(totalAmount);
	}

	public void setTotalAmount(double totalAmount) {
		this.totalAmount = totalAmount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getTimezoneId() {
		return timezoneId;
	}

	public void setTimezoneId(String timezoneId) {
		this.timezoneId = timezoneId;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public PurchaserInfo getPurchaser() {
		return purchaser;
	}

	public void setPurchaser(PurchaserInfo purchaser) {
		this.purchaser = purchaser;
	}

	public String getPurchaseDate() {
		return purchaseDate;
	}

	public void setPurchaseDate(String purchaseDate) {
		this.purchaseDate = purchaseDate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getLastFour() {
		return lastFour;
	}

	public void setLastFour(String lastFour) {
		this.lastFour = lastFour;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public OrderType getOrderType() {
		return orderType;
	}

	public void setOrderType(OrderType orderType) {
		this.orderType = orderType;
	}

	public List<AttendeeDtoV2> getAttendee() {
		return attendee;
	}

	public void setAttendee(List<AttendeeDtoV2> attendee) {
		this.attendee = attendee;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getRecurringEventDate() {
		return recurringEventDate;
	}

	public void setRecurringEventDate(String recurringEventDate) {
		this.recurringEventDate = recurringEventDate;
	}

	public String getDeliveryMode() {
		return deliveryMode;
	}

	public void setDeliveryMode(String deliveryMode) {
		this.deliveryMode = deliveryMode;
	}

    public String getEquivalentTimezone() {
        return equivalentTimezone;
    }

    public void setEquivalentTimezone(String equivalentTimezone) {
        this.equivalentTimezone = equivalentTimezone;
    }

    public Boolean getSpeakerOrder() { return isSpeakerOrder; }

    public void setSpeakerOrder(Boolean speakerOrder) { isSpeakerOrder = speakerOrder; }

    public Boolean getStaffOrder() { return isStaffOrder; }

    public void setStaffOrder(Boolean staffOrder) { isStaffOrder = staffOrder; }

    public UTMTrackSourceDto getUtmTrackSource() {
        return utmTrackSource;
    }

    public void setUtmTrackSource(UTMTrackSourceDto utmTrackSource) {
        this.utmTrackSource = utmTrackSource;
    }

    public double getDueAmount() {
        return dueAmount;
    }

    public void setDueAmount(double dueAmount) {
        this.dueAmount = dueAmount;
    }

    public String getDocumentLink() {
        return documentLink;
    }

    public void setDocumentLink(String documentLink) {
        this.documentLink = documentLink;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public boolean isDisableTicketTransfer() {
        return disableTicketTransfer;
    }

    public void setDisableTicketTransfer(boolean disableTicketTransfer) {
        this.disableTicketTransfer = disableTicketTransfer;
    }

    public String getOrderLastUpdated() {
        return orderLastUpdated;
    }

    public void setOrderLastUpdated(String orderLastUpdated) {
        this.orderLastUpdated = orderLastUpdated;
    }
}