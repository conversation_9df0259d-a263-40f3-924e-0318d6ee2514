package com.accelevents.continuing.ed.service.impl;

import com.accelevents.common.dto.CECriteriaBasicDTO;
import com.accelevents.continuing.ed.ContinuingEdConstants;
import com.accelevents.continuing.ed.dto.ContinuingEdProgressDTO;
import com.accelevents.continuing.ed.dto.ContinuingEdStatisticsDTO;
import com.accelevents.continuing.ed.dto.ContinuingEdUserDTO;
import com.accelevents.continuing.ed.service.ContinuingEdService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.CertificateTypes;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.*;
import com.accelevents.helpers.DoubleHelper;
import com.accelevents.repositories.CertificatesRepository;
import com.accelevents.repositories.CreditManagementRepository;
import com.accelevents.repositories.EventCECriteriaRepository;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.neptune.RONeptuneAttendeeDetailService;
import com.accelevents.services.*;
import com.accelevents.services.dynamodb.analytics.ConsolidatedAnalyticsService;
import com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant;
import com.accelevents.services.repo.helper.CreditManagementRepoService;
import com.accelevents.services.repo.helper.SurveyConfigRepoService;
import com.accelevents.services.repo.helper.SurveyResponseRepoService;
import com.accelevents.session_speakers.dto.SessionIdTitleDto;
import com.accelevents.session_speakers.dto.UserSessionEngagementDto;
import com.accelevents.session_speakers.repo.SessionRepo;
import com.accelevents.session_speakers.repo.UserSessionRepo;
import com.accelevents.session_speakers.services.SessionDetailsService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.SessionTagAndTrackService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.NumberUtils;
import com.accelevents.utils.TimeZoneUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.continuing.ed.ContinuingEdConstants.*;
import static com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsQueryConstants.CONTINUING_ED_DATE_FORMAT;
import static com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsQueryConstants.DATE_FORMAT;
import static com.accelevents.utils.GeneralUtils.convertCommaSeparatedToListLong;
import static org.apache.commons.lang3.math.NumberUtils.toDouble;

@Service
public class ContinuingEdServiceImpl implements ContinuingEdService {

    private final Logger log = LoggerFactory.getLogger(ContinuingEdServiceImpl.class);

    @Autowired
    private ChallengeConfigService challengeService;
    @Autowired
    private EventCECriteriaService eventCECriteriaService;
    @Autowired
    private ConsolidatedAnalyticsService consolidatedAnalyticsService;
    @Autowired
    private UserService userService;
    @Autowired
    private SessionDetailsService sessionDetailsService;
    @Autowired
    private SessionTagAndTrackService tagAndTrackService;
    @Autowired
    private SessionService sessionService;
    @Autowired
    private EventService eventService;
    @Autowired
    private AttendeeProfileService attendeeProfileService;
    @Autowired
    private SurveyConfigRepoService surveyConfigRepoService;
    @Autowired
    private SurveyResponseRepoService surveyResponseRepoService;
    @Autowired
    private SessionRepo sessionRepo;

    @Autowired
    private CreditManagementRepository creditManagementRepository;
    @Autowired
    private EventCECriteriaRepository eventCECriteriaRepository;
    @Autowired
    private CertificatesRepository certificatesRepository;

    @Autowired
    private ROEventLevelSettingService roEventLevelSettingService;
    @Autowired
    private UserSessionRepo userSessionRepo;

    @Autowired
    private CreditManagementRepoService creditManagementRepoService;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");


    @Override
    public DataTableResponse getContinuingEdAnalytics(Long challengeId, ContinuingEdStatus status, Event event, User loggedInUser, PageSizeSearchObj pageObj) {
        EventCECriteriaDTO criteriaDTO = eventCECriteriaService.findEventCECriteriaDTOById(challengeId, event);
        return getContinuingEdAnalytics(criteriaDTO, status, event, loggedInUser, pageObj);
    }

    @Override
    public DataTableResponse getContinuingEdAnalytics(EventCECriteriaDTO criteriaDTO, ContinuingEdStatus status, Event event, User loggedInUser, PageSizeSearchObj pageObj) {
        long criteriaId = criteriaDTO.getCriteriaId();
        log.info("Start of get continuing ed users for criteria {}, status {}, user {}", criteriaId, status, loggedInUser.getUserId());
        boolean isAllStatus = ContinuingEdStatus.ALL.equals(status);
        if (!isAllStatus) {
            pageObj = new PageSizeSearchObj(0, Integer.MAX_VALUE, pageObj.getSearch());
        }
        List<Long> allowedTicketTypeIds = criteriaDTO.getTicketTypeAllowInCriteria();

        Page<Object[]> users = userService.getAllAttendeesByTicketType(event.getEventId(), pageObj.getSearchWithEscapeSpecialChars(),
                allowedTicketTypeIds, pageObj.getPage(), pageObj.getSize());

        List<UserAttendeeDTO> userList = getUserDetails(users.getContent());
        List<ContinuingEdUserDTO> continuingEdUserDTOS;
        long totalElements = 0;

        // Detect challenge type
        boolean isSurveyAction = isContinueEdWithSurveySubmissionAction(criteriaDTO);
        boolean isCheckinAction = isContinueEdWithSessionCheckInAction(criteriaDTO);
        boolean isHybridAction  = isContinueEdWithSessionCheckInAndSubmitSurveyAction(criteriaDTO);

        //session check-in or survey submission actions
        if(isSurveyAction || isCheckinAction || isHybridAction ){
            double rewardPoint = parseDoubleFromAction(criteriaDTO, LeaderBoardConstant.ChallengeConstants.POINT);

            int requiredQuizScore = (isSurveyAction || isHybridAction)
                    ? parseIntFromAction(criteriaDTO, REQUIRED_QUIZ_SCORE)
                    : 0;

            List<Long> sessionOrSurveyIds = isSurveyAction
                    ? getSurveyIdsFromContinueEdChallengeTrigger(criteriaDTO)
                    : getSessionIdsFromChallenge(criteriaDTO);

            if (isSurveyAction) {
                continuingEdUserDTOS = prepareContinuingEdUsersByStatusForSurveyChallenge(event, userList, sessionOrSurveyIds, requiredQuizScore, rewardPoint, status, criteriaDTO);
            } else if (isCheckinAction) {
                continuingEdUserDTOS = prepareContinuingEdUsersByStatusForSessionCheckinChallenge(event, userList, sessionOrSurveyIds, rewardPoint, status, criteriaDTO);
            } else {
                continuingEdUserDTOS = prepareContinuingEdUsersByStatusForSessionCheckinAndSurveyChallenge(event, userList, sessionOrSurveyIds, rewardPoint, status, criteriaDTO);
            }
            totalElements = users.getTotalElements();
        }
        else { //session watch time action
            List<Long> sessionIds = getSessionIdsFromChallenge(criteriaDTO);
            List<Long> userIds = userList.stream().map(UserAttendeeDTO::getUserId).collect(Collectors.toList());

            List<UserSessionEngagementDto> userSessionEngagementDtoList = consolidatedAnalyticsService.getSessionUsersEngagementDetails(event, sessionIds, new HashSet<>(userIds));;
            Map<Long, List<UserSessionEngagementDto>> userSessionEngagementMapByUser = userSessionEngagementDtoList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(UserSessionEngagementDto::getUserId));

            continuingEdUserDTOS = prepareContinuingEdUsersByStatus(event, userList, userSessionEngagementMapByUser, status, criteriaDTO, sessionIds.size());
            totalElements = isAllStatus ? users.getTotalElements() : continuingEdUserDTOS.size();
        }

        if (!isAllStatus) {
            totalElements = continuingEdUserDTOS.size();
        }
        log.info("Fetch continuing ed user list size {}, totalElements {}", continuingEdUserDTOS.size(), totalElements);
        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(continuingEdUserDTOS);
        dataTableResponse.setRecordsTotal(totalElements);
        dataTableResponse.setRecordsFiltered(continuingEdUserDTOS.size());
        log.info("End of get continuing ed users for criteria {}, status {}, result size {}", criteriaId, status, continuingEdUserDTOS.size());
        return dataTableResponse;
    }

    private double parseDoubleFromAction(EventCECriteriaDTO criteriaDTO, String key) {
        return criteriaDTO.getAction().stream()
                .findFirst()
                .map(action -> Double.parseDouble(action.getOrDefault(key, "0").toString()))
                .orElse(0.0);
    }

    private int parseIntFromAction(EventCECriteriaDTO criteriaDTO, String key) {
        return criteriaDTO.getAction().stream()
                .findFirst()
                .map(action -> Integer.parseInt(action.getOrDefault(key, "0").toString()))
                .orElse(0);
    }

    private List<UserAttendeeDTO> getUserDetails(List<Object[]> users) {
        if (CollectionUtils.isEmpty(users)) {
            return new ArrayList<>();
        }

        return new ArrayList<>(users.stream().map(row -> new UserAttendeeDTO(
                        row[0] != null ? ((BigInteger) row[0]).longValue() : null,
                        row[1] != null ? (String) row[1] : Constants.STRING_EMPTY,
                        row[2] != null ? (String) row[2] : Constants.STRING_EMPTY,
                        row[3] != null ? (String) row[3] : Constants.STRING_EMPTY,
                        row[4] != null ? (String) row[4] : Constants.STRING_EMPTY
                )).collect(Collectors.toMap(
                        UserAttendeeDTO::getUserId,
                        user -> user,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ))
                .values());
    }

    private List<ContinuingEdUserDTO> prepareContinuingEdUsersByStatus(Event event, List<UserAttendeeDTO> userList, Map<Long, List<UserSessionEngagementDto>> userSessionEngagementMap, ContinuingEdStatus status, EventCECriteriaDTO criteriaDTO, long totalSessions) {

        log.info("Start of preparing continuing ed user list event {}, criteria {}", event.getEventId(), criteriaDTO.getCriteriaId());
        Integer requiredWatchTime = getRequiredWatchTime(criteriaDTO);
        Map<Long, Double> sessionDurationMap = sessionDetailsService.getSessionVideoDurationsByEvent(event);
        List<ContinuingEdUserDTO> allUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> eligibleUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> inEligibleUsers = new ArrayList<>();
        log.info("userWatchTime size {}, Users size {} ", userSessionEngagementMap.size(), userList.size());

        for (UserAttendeeDTO user : userList) {
            List<UserSessionEngagementDto> sessionEngagementDtoList = userSessionEngagementMap.getOrDefault(user.getUserId(), new ArrayList<>());
            Map<Long, Double> sessionLiveWatchTimeMap = sessionEngagementDtoList.stream()
                    .collect(Collectors.toMap(
                            UserSessionEngagementDto::getSessionId,
                            dto -> dto.getLiveWatchTime() + dto.getRecordingWatchTime(),
                            Double::sum // in case of duplicate sessionIds, sum the values
                    ));

            ContinuingEdUserDTO userDto = new ContinuingEdUserDTO();
            setUserBasicDetails(user, userDto);
            for (Long sessionId : sessionLiveWatchTimeMap.keySet()) {
                if (null != sessionId) {
                    double watchTime = sessionLiveWatchTimeMap.getOrDefault(sessionId, 0.0);
                    Double percentage = calculatePercentage(watchTime, sessionDurationMap.get(sessionId));
                    if (percentage >= requiredWatchTime) {
                        userDto.setCompletedSession(userDto.getCompletedSession() + 1);
                    }
                }
            }

            Map<Long, String> userLastActiveMap = sessionEngagementDtoList.stream().collect(Collectors.toMap(
                    UserSessionEngagementDto::getUserId,
                    UserSessionEngagementDto::getLastUpdated,
                    // merge function -> pick the latest timestamp
                    (d1, d2) -> {
                        Instant i1 = Instant.parse(d1);
                        Instant i2 = Instant.parse(d2);
                        return i1.isAfter(i2) ? d1 : d2;
                    }
            ));

            setLastActivityDate(event, userLastActiveMap.get(user.getUserId()), userDto , Boolean.FALSE);
            int progress = calculatePercentage(Double.valueOf(userDto.getCompletedSession()), Double.valueOf(totalSessions)).intValue();
            userDto.setProgress(progress + Constants.STRING_PERCENTAGE);
            userDto.setChallenge(criteriaDTO.getName());
            userDto.setCertificateId(criteriaDTO.getCertificateId());
            if (progress >= 100) {
                eligibleUsers.add(userDto);
            } else {
                inEligibleUsers.add(userDto);
            }
            allUsers.add(userDto);
        }
        log.info("End of preparing continuing ed user list criteria {}, eligible {}, ineligible {}, all {}", criteriaDTO.getCriteriaId(), eligibleUsers.size(), inEligibleUsers.size(), allUsers.size());
        if (ContinuingEdStatus.ELIGIBLE.equals(status)) {
            return eligibleUsers;
        } else if (ContinuingEdStatus.INELIGIBLE.equals(status)) {
            return inEligibleUsers;
        }
        return allUsers;
    }

    private void setUserBasicDetails(UserAttendeeDTO user, ContinuingEdUserDTO userDto) {
        userDto.setUserId(user.getUserId());
        userDto.setPhoto(user.getPhoto());
        userDto.setEmail(user.getEmail());
        userDto.setFirstName(user.getFirstName());
        userDto.setLastName(user.getLastName());
        userDto.setName(user.getFirstName() + Constants.STRING_BLANK + user.getLastName());
    }


    private List<ContinuingEdUserDTO> prepareContinuingEdUsersByStatusForSessionCheckinChallenge(Event event, List<UserAttendeeDTO> userList, List<Long> sessionIds, Double rewardPoint, ContinuingEdStatus status, EventCECriteriaDTO criteriaDTO) {
        log.info("Start of preparing continuing ed user list for session check in criteria, eventId {}, criteria {}", event.getEventId(), criteriaDTO.getCriteriaId());
        List<ContinuingEdUserDTO> allUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> eligibleUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> inEligibleUsers = new ArrayList<>();

        List<Object[]> userSessions = userSessionRepo.findAllUserSessionsByCheckInTimeNotNullSessionIdsIn(sessionIds);
        Map<Long, List<Object[]>> userSessionMap = userSessions.stream()
                .filter(row -> row[1] != null) // ensure userId is not null
                .collect(Collectors.groupingBy(row -> (Long) row[1]));


        int totalSessionCount = sessionIds.size();

        for (UserAttendeeDTO user : userList) {
            Long userId = user.getUserId();
            ContinuingEdUserDTO userDto = new ContinuingEdUserDTO();
            setUserBasicDetails(user, userDto);
            userDto.setChallenge(criteriaDTO.getName());
            userDto.setCertificateId(criteriaDTO.getCertificateId());

            List<Object[]> checkedInSessions = userSessionMap.getOrDefault(userId, Collections.emptyList());
            Optional<Date> latestCheckInDate = checkedInSessions.stream()
                    .map(obj -> (Date) obj[2])
                    .filter(Objects::nonNull)
                    .max(Date::compareTo);

            if(latestCheckInDate.isPresent()) {
                String formattedDate = sdf.format(latestCheckInDate.get());
                setLastActivityDate(event, formattedDate, userDto, Boolean.TRUE);
            }else{
                userDto.setLastActivityDate(Constants.NOT_APPLICABLE);
            }
            int checkedInCount = checkedInSessions.size();
            int progress = calculatePercentage((double) checkedInCount, (double) totalSessionCount).intValue();
            userDto.setProgress(Math.min(progress, 100)+ Constants.STRING_PERCENTAGE);


            //if the user checked-in all the sessions of the criteria then they'll get the CE credit, otherwise not.
            if (checkedInCount == totalSessionCount && totalSessionCount > 0) {
                userDto.setRewardPoint(rewardPoint);
                eligibleUsers.add(userDto);
            } else {
                inEligibleUsers.add(userDto);
            }

            allUsers.add(userDto);
        }

        log.info("End of preparing continuing ed user list  preparing continuing ed user list for session check in criteria, criteria {}, eligible {}, ineligible {}, all {}", criteriaDTO.getCriteriaId(), eligibleUsers.size(), inEligibleUsers.size(), allUsers.size());
        if (ContinuingEdStatus.ELIGIBLE.equals(status)) {
            return eligibleUsers;
        } else if (ContinuingEdStatus.INELIGIBLE.equals(status)) {
            return inEligibleUsers;
        }
        return allUsers;
    }


    private List<ContinuingEdUserDTO> prepareContinuingEdUsersByStatusForSessionCheckinAndSurveyChallenge(Event event, List<UserAttendeeDTO> userList, List<Long> sessionIds, double rewardPoint, ContinuingEdStatus status, EventCECriteriaDTO criteriaDTO) {
        log.info("Start of preparing continuing ed user list for session check in and submit survey criteria, eventId {}, criteria {}", event.getEventId(), criteriaDTO.getCriteriaId());
        List<ContinuingEdUserDTO> allUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> eligibleUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> inEligibleUsers = new ArrayList<>();

        int totalSessions = sessionIds.size();
        int totalActions = totalSessions * 2;

        List<Object[]> userSessions = userSessionRepo.findAllUserSessionsByCheckInTimeNotNullSessionIdsIn(sessionIds);
        Map<Long, List<Object[]>> userSessionMap = userSessions.stream()
                .filter(row -> row[1] != null) // ensure userId is not null
                        .collect(Collectors.groupingBy(row -> (Long) row[1]));



        List<SurveyResponse> userSurveyResponseCountAndLatestSubmissionDate = surveyResponseRepoService.getCountOfSurveyResponseAndLatestSubmissionDateBySessionIds(sessionIds, event.getEventId(), TimeZoneUtil.getDateInUTC(criteriaDTO.getStartDate(), event.getEquivalentTimeZone()),TimeZoneUtil.getDateInUTC(criteriaDTO.getEndDate(), event.getEquivalentTimeZone()));
        Map<Long, List<SurveyResponse>> userSurveyResponseMap = userSurveyResponseCountAndLatestSubmissionDate.stream()
                .collect(Collectors.groupingBy(SurveyResponse::getUserId));


        for (UserAttendeeDTO user : userList) {
            ContinuingEdUserDTO userDto = new ContinuingEdUserDTO();
            Long userId = user.getUserId();
            setUserBasicDetails(user, userDto);
            userDto.setChallenge(criteriaDTO.getName());
            userDto.setCertificateId(criteriaDTO.getCertificateId());

            List<Object[]> checkedInSessions = userSessionMap.getOrDefault(userId, Collections.emptyList());
            List<SurveyResponse> surveySubmissionOfUser = userSurveyResponseMap.getOrDefault(userId, Collections.emptyList());


            Date latestSurveyDate = surveySubmissionOfUser.stream()
                    .map(SurveyResponse::getSubmissionDate)
                    .filter(Objects::nonNull)
                    .max(Date::compareTo)
                    .orElse(null);

            Date latestCheckInDate = checkedInSessions.stream()
                    .map(obj -> (Date) obj[2])
                    .filter(Objects::nonNull)
                    .max(Date::compareTo)
                    .orElse(null);

            // Determine the latest of both
            Date latestDate = null;
            if (latestSurveyDate != null && latestCheckInDate != null) {
                latestDate = latestSurveyDate.after(latestCheckInDate) ? latestSurveyDate : latestCheckInDate;
            } else if (latestSurveyDate != null) {
                latestDate = latestSurveyDate;
            } else {
                latestDate = latestCheckInDate;
            }

            if(latestDate != null) {
                String formattedDate = sdf.format(latestDate);
                setLastActivityDate(event, formattedDate, userDto, Boolean.TRUE);
            }
            else{
                userDto.setLastActivityDate(Constants.NOT_APPLICABLE);
            }

            int completedCount = checkedInSessions.size() + surveySubmissionOfUser.size();
            if(!checkedInSessions.isEmpty()) {
                int progress = calculatePercentage((double) completedCount, (double) totalActions).intValue();
                userDto.setProgress((Math.min(progress, 100)) + Constants.STRING_PERCENTAGE);
            }else{
                userDto.setProgress(0 + Constants.STRING_PERCENTAGE);
            }
            boolean fullyCheckedIn = checkedInSessions.size() == totalSessions;
            boolean fullySubmittedSurveys = totalSessions != 0 && surveySubmissionOfUser.size() == totalSessions;

            //assign CE credit when all the session checked-in and all the survey submitted
            if (fullyCheckedIn && fullySubmittedSurveys) {
                userDto.setRewardPoint(rewardPoint);
                eligibleUsers.add(userDto);
            } else {
                inEligibleUsers.add(userDto);
            }
            allUsers.add(userDto);
        }
        log.info("End of preparing continuing ed user list  preparing continuing ed user list for session check in criteria, criteria {}, eligible {}, ineligible {}, all {}", criteriaDTO.getCriteriaId(), eligibleUsers.size(), inEligibleUsers.size(), allUsers.size());
        if (ContinuingEdStatus.ELIGIBLE.equals(status)) {
            return eligibleUsers;
        } else if (ContinuingEdStatus.INELIGIBLE.equals(status)) {
            return inEligibleUsers;
        }
        return allUsers;

    }

    public List<ContinuingEdUserDTO> prepareContinuingEdUsersByStatusForSurveyChallenge(Event event, List<UserAttendeeDTO> userList, List<Long> surveyIds, int requiredQuizScore, double rewardPoint, ContinuingEdStatus status, EventCECriteriaDTO criteriaDTO) {

        log.info("Start of preparing continuing ed user list for survey criteria, eventId {}, criteria {}", event.getEventId(), criteriaDTO.getCriteriaId());
        // Handle edge case: if no sessions have surveys, all users are eligible (no survey requirements)
        if (surveyIds.isEmpty()) {
            log.info("No sessions with surveys found in criteria {}. All users will be marked as eligible.", criteriaDTO.getCriteriaId());
            List<ContinuingEdUserDTO> allUsers = new ArrayList<>();
            List<ContinuingEdUserDTO> inEligibleUsers = new ArrayList<>();

            for (UserAttendeeDTO user : userList) {
                ContinuingEdUserDTO userDto = new ContinuingEdUserDTO();
                setUserBasicDetails(user, userDto);
                userDto.setChallenge(criteriaDTO.getName());
                userDto.setCertificateId(criteriaDTO.getCertificateId());
                userDto.setUserQuizScore(0);
                inEligibleUsers.add(userDto);
                allUsers.add(userDto);
            }

            if (ContinuingEdStatus.ELIGIBLE.equals(status)) {
                new ArrayList<>();
            } else if (ContinuingEdStatus.INELIGIBLE.equals(status)) {
                return inEligibleUsers;
            }
            return allUsers;
        }

        boolean isQuizAbleSurveyAvailable = surveyConfigRepoService.isAnySurveyQuizAble(surveyIds);
       // Map<Long, Integer> userQuizScoreMap = surveyResponseRepoService.getAllUserSurveyScoreBySurveyIds(surveyIds, criteriaDTO);

        // Get session IDs from the criteria
        List<Long> sessionIds = this.getSessionIdsFromChallenge(criteriaDTO);
        
        log.info("Total sessions in criteria: {}, Sessions with surveys: {}", sessionIds.size(), surveyIds.size());

        // Handle edge case: if no sessions have surveys, all users are eligible (no survey requirements)
        if (surveyIds.isEmpty()) {
            log.info("No sessions with surveys found in criteria {}. All users will be marked as eligible.", criteriaDTO.getCriteriaId());
            List<ContinuingEdUserDTO> allUsers = new ArrayList<>();
            List<ContinuingEdUserDTO> eligibleUsers = new ArrayList<>();
            List<ContinuingEdUserDTO> inEligibleUsers = new ArrayList<>();
            
            for (UserAttendeeDTO user : userList) {
                ContinuingEdUserDTO userDto = new ContinuingEdUserDTO();
                setUserBasicDetails(user, userDto);
                userDto.setChallenge(criteriaDTO.getName());
                userDto.setCertificateId(criteriaDTO.getCertificateId());
                userDto.setUserQuizScore(100);
                userDto.setRewardPoint(rewardPoint);
                eligibleUsers.add(userDto);
                allUsers.add(userDto);
            }
            
            if (ContinuingEdStatus.ELIGIBLE.equals(status)) {
                return eligibleUsers;
            } else if (ContinuingEdStatus.INELIGIBLE.equals(status)) {
                return inEligibleUsers;
            }
            return allUsers;
        }

        // Get survey completion data for all users
        List<SurveyResponse> surveyCompletionData = surveyResponseRepoService.getSurveyCompletionDataBySurveyIds(
                surveyIds, event.getEventId(),
                TimeZoneUtil.getDateInUTC(criteriaDTO.getStartDate(), event.getEquivalentTimeZone()),
                TimeZoneUtil.getDateInUTC(criteriaDTO.getEndDate(), event.getEquivalentTimeZone())
        );

        Map<Long, List<SurveyResponse>> userIdToSurveyResponsesMap = surveyCompletionData.stream()
                .collect(Collectors.groupingBy(SurveyResponse::getUserId));

        Map<Long, Set<Long>> userCompletedSessionsMap = new HashMap<>();
        Map<Long, Double> userSessionScoresMap = new HashMap<>();
        Map<Long, String> userLastSubmissionDateMap = new HashMap<>();

// Iterate over each user
        for (Map.Entry<Long, List<SurveyResponse>> entry : userIdToSurveyResponsesMap.entrySet()) {
            long userId = entry.getKey();
            List<SurveyResponse> responses = entry.getValue();

            // 1. Unique sessionIds completed by user
            Set<Long> completedSessionIds = responses.stream()
                    .map(SurveyResponse::getSessionId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            userCompletedSessionsMap.put(userId, completedSessionIds);

            // 2. Average score per user (if survey has scoring)
            List<Integer> scores = responses.stream()
                    .map(SurveyResponse::getUserQuizScore)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (!scores.isEmpty()) {
                double averageScore = scores.stream()
                        .mapToInt(Integer::intValue)
                        .average()
                        .orElse(0.0);
                userSessionScoresMap.put(userId, averageScore);
            }

            // 3. Last submission date per user
            Optional<Date> latestSubmission = responses.stream()
                    .map(SurveyResponse::getSubmissionDate)
                    .filter(Objects::nonNull)
                    .max(Date::compareTo);

            String formattedDate = sdf.format(latestSubmission.get());
            userLastSubmissionDateMap.put(userId, formattedDate);
            }


        List<ContinuingEdUserDTO> allUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> eligibleUsers = new ArrayList<>();
        List<ContinuingEdUserDTO> inEligibleUsers = new ArrayList<>();


        for (UserAttendeeDTO user : userList) {
            ContinuingEdUserDTO userDto = new ContinuingEdUserDTO();
            Long userId = user.getUserId();
            setUserBasicDetails(user, userDto);

            String lastActivityDate = String.valueOf(userLastSubmissionDateMap.getOrDefault(userId, Constants.STRING_EMPTY));
            setLastActivityDate(event, lastActivityDate, userDto, Boolean.TRUE);

            userDto.setChallenge(criteriaDTO.getName());
            userDto.setCertificateId(criteriaDTO.getCertificateId());

            // Check if user has completed surveys for ALL sessions with surveys
            Set<Long> userCompletedSessions = userCompletedSessionsMap.getOrDefault(userId, new HashSet<>());
            boolean hasCompletedAllSessions = userCompletedSessions.containsAll(sessionIds);

            boolean isEligible;
            if(isQuizAbleSurveyAvailable) {
                // For quiz-enabled surveys: user must complete all sessions AND meet score requirement
                if (hasCompletedAllSessions) {
                    // Calculate average score across all completed sessions
                    Double userScores = userSessionScoresMap.getOrDefault(userId, new Double(0.0));
                    isEligible = userScores >= requiredQuizScore;
                    if(isEligible){
                        userDto.setUserQuizScore(100);
                    }
                } else {
                    userDto.setUserQuizScore(0);
                    isEligible = false;
                }
            } else {
                // For non-quiz surveys: user must complete surveys for all sessions
                isEligible = hasCompletedAllSessions;
                if (isEligible) {
                    userDto.setUserQuizScore(100);
                }
            }
            
            if (isEligible) {
                userDto.setRewardPoint(rewardPoint);
                eligibleUsers.add(userDto);
            } else {
                inEligibleUsers.add(userDto);
            }
            allUsers.add(userDto);
        }
        log.info("End of preparing continuing ed user list  preparing continuing ed user list for survey criteria, criteria {}, eligible {}, ineligible {}, all {}", criteriaDTO.getCriteriaId(), eligibleUsers.size(), inEligibleUsers.size(), allUsers.size());
        if (ContinuingEdStatus.ELIGIBLE.equals(status)) {
            return eligibleUsers;
        } else if (ContinuingEdStatus.INELIGIBLE.equals(status)) {
            return inEligibleUsers;
        }
        return allUsers;
    }

    @Override
    public List<ContinuingEdProgressDTO> getContinuingEdAnalyticsByUser(Long criteriaId, User user, Event event) {
        Long userId = user.getUserId();
        log.info("Start of get continuing ed analytics for user {}, criteria {}, event {}", userId, criteriaId, event.getEventId());
        List<EventCECriteriaDTO> criteriaList = new ArrayList<>();
        List<Long> sessionIds = new ArrayList<>();

        boolean isSurveySubmissionCriteria = false;
        boolean isSessionCheckInCriteria = false;
        boolean isSessionCheckinAndSurveyCriteria = false;
        boolean isSessionWatchCriteria = false;


        if (NumberUtils.isNumberGreaterThanZero(criteriaId)) {
            EventCECriteriaDTO criteriaDTO = eventCECriteriaService.findEventCECriteriaDTOById(criteriaId, event);
            criteriaList.add(criteriaDTO);
            isSurveySubmissionCriteria = isContinueEdWithSurveySubmissionAction(criteriaDTO);
            isSessionCheckinAndSurveyCriteria = isContinueEdWithSessionCheckInAndSubmitSurveyAction(criteriaDTO);
            isSessionCheckInCriteria = isContinueEdWithSessionCheckInAction(criteriaDTO);
            isSessionWatchCriteria = isContinueEdWithSessionWatchAction(criteriaDTO);
        } else {
            criteriaList.addAll(eventCECriteriaService.getCriteriaDTOsByUser(user, event));
        }

        Map<Long, List<Long>> criteriaSessionMap = criteriaList.stream()
                .collect(Collectors.toMap(EventCECriteriaDTO::getCriteriaId, this::getSessionIdsFromChallenge));

        Map<Long, List<Long>> criteriaSurveyMap = new HashMap<>();
        criteriaSessionMap.forEach((k, v) -> {
            sessionIds.addAll(v);
            List<Long> surveys = sessionService.findSurveyIdBySessionIds(v);
            criteriaSurveyMap.put(k, surveys);
        });


        List<Long> quizAbleSurveyIdList = Collections.emptyList();
        // Track survey responses by both surveyId and sessionId
        Map<Pair<Long, Long>, Integer> sessionSurveyScores = new HashMap<>();
        Map<Pair<Long, Long>, Date> sessionSurveyDates = new HashMap<>();

        if (criteriaId < 1 || isSurveySubmissionCriteria || isSessionCheckinAndSurveyCriteria) {
            quizAbleSurveyIdList = surveyConfigRepoService.getAllQuizAbleSurveyIds(event.getEventId());
            List<Object[]> surveyResponses = surveyResponseRepoService.getAllSurveyResponseUserScoreAndSubmissionDateByUserIdAndEventId(userId, event.getEventId());

            for (Object[] row : surveyResponses) {
                Long surveyId = (Long) row[0];
                Integer score = (Integer) row[1];
                Date submissionDate = (Date) row[2];
                Long sessionId = (Long) row[3];

                if (surveyId != null && sessionId != null) {
                    Pair<Long, Long> key = Pair.of(surveyId, sessionId);
                    sessionSurveyScores.put(key, score);
                    sessionSurveyDates.put(key, submissionDate);
                }
            }
        }

        Map<Long, Double> sessionDurationMap = Collections.emptyMap();

        if (isSessionWatchCriteria) {
            sessionDurationMap = sessionDetailsService.getSessionVideoDurationsByEvent(event);
        }

        List<UserSessionEngagementDto> sessionEngagementDtoList = consolidatedAnalyticsService.getUserSessionsEngagementDetails(event, userId);
        Map<Long, Double> sessionLiveWatchTimeMap = sessionEngagementDtoList.stream()
                .collect(Collectors.toMap(
                        UserSessionEngagementDto::getSessionId,
                        dto -> dto.getLiveWatchTime() + dto.getRecordingWatchTime(),
                        Double::sum
                ));


        List<Long> userSessionIds = (isSessionCheckInCriteria || isSessionCheckinAndSurveyCriteria)
                ? userSessionRepo.findCheckedInSessionIdByEventIdAndUserId(event.getEventId(), userId)
                : Collections.emptyList();


        List<Session> sessionList = sessionService.getAllSessionByIds(sessionIds, event);
        Map<Long, Session> sessionMapById = sessionList.stream().collect(Collectors.toMap(Session::getId, e -> e));
        List<ContinuingEdProgressDTO> continuingEdSessionDTOs = new ArrayList<>();

        for (EventCECriteriaDTO criteria : criteriaList) {
            List<Long> sessionIdList = criteriaSessionMap.getOrDefault(criteria.getCriteriaId(), Collections.emptyList());
            List<Long> surveyIdList = criteriaSurveyMap.getOrDefault(criteria.getCriteriaId(), Collections.emptyList());
            boolean isQuizAble = !Collections.disjoint(quizAbleSurveyIdList, surveyIdList);
            Date startUTC = TimeZoneUtil.getDateInUTC(criteria.getStartDate(), event.getEquivalentTimeZone());
            Date endUTC = TimeZoneUtil.getDateInUTC(criteria.getEndDate(), event.getEquivalentTimeZone());

            for (Long sessionId : sessionIdList) {
                Session session = sessionMapById.get(sessionId);
                if (session == null)
                    continue;

                ContinuingEdProgressDTO continuingEdSessionDTO = new ContinuingEdProgressDTO();
                continuingEdSessionDTO.setChallengeName(criteria.getName());
                continuingEdSessionDTO.setSessionName(session.getTitle());

                if (isSurveySubmissionCriteria) {
                    continuingEdSessionDTO.setCompletionMethod(ContinuingEdConstants.SURVEY_SUBMISSION);
                    
                    Long surveyId = session.getSurveyId();
                    Long currentSessionId = session.getId();
                    Pair<Long, Long> responseKey = Pair.of(surveyId, currentSessionId);
                    
                    Date submissionDate = sessionSurveyDates.get(responseKey);
                    int userScore = sessionSurveyScores.getOrDefault(responseKey, 0);
                    int requiredScore = getRequiredQuizScore(criteria);

                    boolean withinDateRange = submissionDate != null &&
                            !submissionDate.before(startUTC) &&
                            !submissionDate.after(endUTC);

                    int progress = 0;
                    if (isQuizAble) {
                        // For quiz-able surveys, check score and date range
                        progress = (withinDateRange && userScore >= requiredScore) ? 100 : 0;
                    } else {
                        // For non-quiz surveys, just check if there's a submission for this session
                        progress = sessionSurveyDates.containsKey(responseKey) ? 100 : 0;
                    }
                    continuingEdSessionDTO.setProgress(progress + Constants.STRING_PERCENTAGE);
                } else if (isSessionWatchCriteria) {
                    continuingEdSessionDTO.setCompletionMethod(SESSION_WATCH);
                    double watchTime = sessionLiveWatchTimeMap.getOrDefault(sessionId, 0.0);
                    double progress = calculatePercentage(watchTime, sessionDurationMap.get(sessionId));
                    continuingEdSessionDTO.setProgress((Math.min(progress, 100)) + Constants.STRING_PERCENTAGE);
                } else if (isSessionCheckInCriteria) {
                    continuingEdSessionDTO.setCompletionMethod(SESSION_CHECK_IN);
                    boolean isCheckedIn = userSessionIds.contains(sessionId);
                    continuingEdSessionDTO.setProgress((isCheckedIn ? 100 : 0) + Constants.STRING_PERCENTAGE);
                } else {
                    continuingEdSessionDTO.setCompletionMethod(SESSION_CHECK_IN_AND_SURVEY_COMPLETION);
                    boolean isCheckedIn = userSessionIds.contains(sessionId);
                    
                    // Check if survey is completed for this specific session
                    Long surveyId = session.getSurveyId();
                    Long currentSessionId = session.getId();
                    Pair<Long, Long> responseKey = Pair.of(surveyId, currentSessionId);
                    boolean isSurveyDone = isCheckedIn && sessionSurveyDates.containsKey(responseKey);
                    
                    // Calculate progress: 0% if not checked in, 50% if checked in but no survey, 100% if both
                    int progress = 0;
                    if (isCheckedIn) {
                        progress = isSurveyDone ? 100 : 50;
                    }
                    continuingEdSessionDTO.setProgress(progress + Constants.STRING_PERCENTAGE);
                }
                continuingEdSessionDTOs.add(continuingEdSessionDTO);
            }
        }
        log.info("Successfully continuing ed analytics data list prepared for user {}, criteriaId {}, result size {}", userId, criteriaId, continuingEdSessionDTOs.size());
        return continuingEdSessionDTOs;
    }

    @Override
    public List<ContinuingEdUserDTO> getEligibleUserByChallenge(Long criteriaId, Event event, User loggedInUser) {

        log.info("Start of get eligible users for criteria {}, user {}", criteriaId, loggedInUser.getUserId());
        EventCECriteriaDTO criteriaDTO = eventCECriteriaService.getEventCECriteriaDetailsById(criteriaId, event);
        List<Long> allowedTicketTypes = criteriaDTO.getTicketTypeAllowInCriteria();
        Page<Object[]> users = userService.getAllAttendeesByTicketType(event.getEventId(), "", allowedTicketTypes, 0, Integer.MAX_VALUE);

        List<UserAttendeeDTO> userList = getUserDetails(users.getContent());

        if (CollectionUtils.isEmpty(userList)){
            return Collections.emptyList();
        }

        List<String> attendeeIds = userList.stream().map(e -> RONeptuneAttendeeDetailService.attendeeId(e.getUserId())).collect(Collectors.toList());
        List<AttendeeProfileDto> attendees = attendeeProfileService.getAttendees(String.valueOf(event.getEventId()), attendeeIds);
        Map<Long, AttendeeProfileDto> attendeeMap = attendees.stream().collect(Collectors.toMap(AttendeeProfilePeoplePageDetailsDto::getUserId, e -> e));

        Map<Long, Double> sessionDurationMap = sessionDetailsService.getSessionVideoDurationsByEvent(event);

        List<Long> sessionIds = getSessionIdsFromChallenge(criteriaDTO);

        List<Long> userIds = userList.stream().map(UserAttendeeDTO::getUserId).collect(Collectors.toList());

        Map<Long, List<UserSessionEngagementDto>> userSessionEngagementMap = consolidatedAnalyticsService.getUserSessionsEngagementDetailsBatch(event, userIds);

        int totalSessions = sessionIds.size();
        Integer requiredWatchTime = getRequiredWatchTime(criteriaDTO);

        List<ContinuingEdUserDTO> eligibleUsers = new ArrayList<>();

        for (UserAttendeeDTO user : userList) {
            //Map<String, Object> sessionWatchTimeMap = userWatchTime.getOrDefault(user.getUserId(), new HashMap<>());
            ContinuingEdUserDTO userDto = new ContinuingEdUserDTO();
            List<UserSessionEngagementDto> sessionEngagementDtoList = userSessionEngagementMap.getOrDefault(user.getUserId(), new ArrayList<>());
            Map<Long, Double> sessionLiveWatchTimeMap = sessionEngagementDtoList.stream()
                    .collect(Collectors.toMap(
                            UserSessionEngagementDto::getSessionId,
                            dto -> dto.getLiveWatchTime() + dto.getRecordingWatchTime(),
                            Double::sum // in case of duplicate sessionIds, sum the values
                    ));

            for (Long sessionId : sessionLiveWatchTimeMap.keySet()) {
                if (null != sessionId) {
                    double watchTime = sessionLiveWatchTimeMap.getOrDefault(sessionId, 0.0);
                    Double percentage = calculatePercentage(watchTime, sessionDurationMap.get(sessionId));
                    if (percentage >= requiredWatchTime) {
                        userDto.setCompletedSession(userDto.getCompletedSession() + 1);
                    }
                }
            }
            Map<Long, String> userLastActiveMap = sessionEngagementDtoList.stream().collect(Collectors.toMap(
                                    UserSessionEngagementDto::getUserId,
                                    UserSessionEngagementDto::getLastUpdated,
                                    // merge function -> pick the latest timestamp
                                    (d1, d2) -> {
                                        Instant i1 = Instant.parse(d1);
                                        Instant i2 = Instant.parse(d2);
                                        return i1.isAfter(i2) ? d1 : d2;
                                    }
                            ));
            int progress = calculatePercentage(Double.valueOf(userDto.getCompletedSession()), Double.valueOf(totalSessions)).intValue();
            if (progress >= 100) {
                AttendeeProfileDto profileDto = attendeeMap.getOrDefault(user.getUserId(), new AttendeeProfileDto());
                userDto.setUserId(user.getUserId());
                userDto.setFirstName(user.getFirstName());
                userDto.setLastName(user.getLastName());
                userDto.setPhoto(user.getPhoto());
                userDto.setChallenge(criteriaDTO.getName());
                userDto.setProgress(progress + Constants.STRING_PERCENTAGE);
                setLastActivityDate(event, userLastActiveMap.get(user.getUserId()), userDto, Boolean.FALSE);
                userDto.setCompany(profileDto.getCompany());
                userDto.setTitle(profileDto.getTitle());
                eligibleUsers.add(userDto);
            }
        }
        log.info("Successfully fetched eligible users for criteria {}, result size {}", criteriaId, eligibleUsers.size());
        return eligibleUsers;
    }

    @Override
    public List<ContinuingEdProgressDTO> getContinuingEdChallengesProgress(User user, Event event, List<EventCECriteriaDTO> continuingEdChallenges) {
        Long userId = user.getUserId();
        log.info("Start of get continuing ed challenge progress for user {}, event {}", userId, event.getEventId());
        if (CollectionUtils.isEmpty(continuingEdChallenges)) {
            return Collections.emptyList();
        }
        Set<Long> sessionIds = new HashSet<>();
        Map<Long, List<Long>> challengeSessionMap = new HashMap<>();
        for (EventCECriteriaDTO criteria : continuingEdChallenges) {
            List<Long> challengeSessionIds = getSessionIdsFromChallenge(criteria);
            challengeSessionMap.put(criteria.getCriteriaId(), challengeSessionIds);
            sessionIds.addAll(challengeSessionIds);
        }
        Map<Long, Double> sessionDurationMap = sessionDetailsService.getSessionVideoDurationsByEvent(event);

        List<UserSessionEngagementDto> sessionEngagementDtoList = consolidatedAnalyticsService.getUserSessionsEngagementDetails(event, userId);
        Map<Long, Double> sessionLiveWatchTimeMap = sessionEngagementDtoList.stream()
                .collect(Collectors.toMap(
                        UserSessionEngagementDto::getSessionId,
                        dto -> dto.getLiveWatchTime() + dto.getRecordingWatchTime(),
                        Double::sum // in case of duplicate sessionIds, sum the values
                ));

        List<ContinuingEdProgressDTO> challengeProgressList = new ArrayList<>();
        for (EventCECriteriaDTO criteria : continuingEdChallenges) {
            int completedSessions = 0;
            Integer requiredWatchTime = getRequiredWatchTime(criteria);
            List<Long> challengeSessionList = challengeSessionMap.getOrDefault(criteria.getCriteriaId(), Collections.emptyList());
            for (Long sessionId : challengeSessionList) {
                double watchTime = sessionLiveWatchTimeMap.getOrDefault(sessionId, 0.0);
                Double watchTimePercentage = calculatePercentage(watchTime, sessionDurationMap.get(sessionId));
                if (watchTimePercentage >= requiredWatchTime) {
                    completedSessions++;
                }
            }
            Double progress = calculatePercentage((double) completedSessions, (double) challengeSessionList.size());
            ContinuingEdProgressDTO challengeProgress = new ContinuingEdProgressDTO();
            challengeProgress.setProgress(String.valueOf(progress));
            challengeProgress.setChallengeId(criteria.getCriteriaId());
            challengeProgressList.add(challengeProgress);
        }
        log.info("Successfully fetched continuing ed challenge progress list for user {}, event {}, result size {}", userId, event.getEventId(), challengeProgressList.size());
        return challengeProgressList;
    }

    @Override
    public ContinuingEdStatisticsDTO getContinuingEdStatistics(Event event, User loggedInUser) {
        log.info("Start of get continuing ed statistics for event {} user {}", event.getEventId(), loggedInUser.getUserId());

        List<EventCECriteriaDTO> criteriaList = eventCECriteriaService.getCECriteriaDTOByEvent(event);
        log.info("getContinuingEdStatistics | get total criteria of the event {} and size {}", event.getEventId(), criteriaList.size());
        Set<Long> allUsers = new HashSet<>();


        Set<Long> participatedUsers = new HashSet<>();

        // Gather all required ticket types and build user lists
        Map<Long, List<Long>> criteriaSessions = new HashMap<>();
        Map<Long, List<Long>> criteriaSurveys = new HashMap<>();
        Map<Long, Set<Long>> criteriaUserMap = new HashMap<>();

        for (EventCECriteriaDTO criteria : criteriaList) {
            List<Long> sessionIds = getSessionIdsFromChallenge(criteria);
            criteriaSessions.put(criteria.getCriteriaId(), sessionIds);
            criteriaSurveys.put(criteria.getCriteriaId(), sessionService.findSurveyIdBySessionIds(sessionIds));
            Set<Long> userIds = getChallengeParticipants(
                    convertToMap(userService.getUsersGroupByTicketTypeId(event.getEventId(), criteria.getTicketTypeAllowInCriteria())),
                    criteria.getTicketTypeAllowInCriteria()
            );
            allUsers.addAll(userIds);
            criteriaUserMap.put(criteria.getCriteriaId(), userIds);
        }

        if (allUsers.isEmpty()) return new ContinuingEdStatisticsDTO();

        List<Long> allSessionIds = criteriaSessions.values().stream().flatMap(List::stream).collect(Collectors.toList());

        // Shared data sources - session watch time
        Map<Long, Double> sessionDurationMap = sessionDetailsService.getSessionVideoDurationsByEvent(event);
        Map<Long, List<UserSessionEngagementDto>> userSessionEngagementMap = consolidatedAnalyticsService.getUserSessionsEngagementDetailsBatch(event, new ArrayList<>(allUsers));


        //shared data sources - session check-in
        List<Object[]> userSessions = userSessionRepo.findAllUserSessionsByCheckInTimeNotNullSessionIdsIn(allSessionIds);
        Map<Long, List<Long>> userSessionCheckIns = userSessions.stream()
                .collect(Collectors.groupingBy(
                        obj -> (Long) obj[1],                             // Key: userId
                        Collectors.mapping(obj -> (Long) obj[0],          // Value: sessionId
                                Collectors.toList())
                ));
//

        // Shared data sources - survey submission
        List<Object[]> surveyResponses = surveyResponseRepoService.getAllSurveyResponseUserScoreAndSubmissionDateByUserIdsAndEventId(
                new ArrayList<>(allUsers), event.getEventId());
        
        // Build session-aware maps for survey scores and dates
        Map<SurveyResponseKeyDto, Integer> surveyScoreMap = buildUserSurveyScoreMap(surveyResponses);
        Map<SurveyResponseKeyDto, Date> surveyDateMap = buildUserSurveyDateMap(surveyResponses);
        List<Long> quizableSurveyIds = surveyConfigRepoService.getAllQuizAbleSurveyIds(event.getEventId());


        List<Session> sessionList = sessionService.getAllSessionByIds(allSessionIds, event);
        Map<Long, Session> sessionMapById = sessionList.stream().collect(Collectors.toMap(Session::getId, e -> e));

        Map<Long, Integer> userCompletedCriteriaCount = new HashMap<>();

        for (EventCECriteriaDTO criteria : criteriaList) {
            List<Long> sessionIds = criteriaSessions.get(criteria.getCriteriaId());
            List<Long> surveyIds = criteriaSurveys.get(criteria.getCriteriaId());
            Set<Long> userIds = criteriaUserMap.get(criteria.getCriteriaId());

            boolean isQuiz = !Collections.disjoint(surveyIds, quizableSurveyIds);
            Integer requiredScore = getRequiredQuizScore(criteria);
            Date startDate = TimeZoneUtil.getDateInUTC(criteria.getStartDate(), event.getEquivalentTimeZone());
            Date endDate = TimeZoneUtil.getDateInUTC(criteria.getEndDate(), event.getEquivalentTimeZone());

            for (Long userId : userIds) {
                double progress = 0;

                if (isContinueEdWithSessionWatchAction(criteria)) {
                    List<UserSessionEngagementDto> sessionEngagementDtoList = userSessionEngagementMap.getOrDefault(userId, new ArrayList<>());
                    Map<Long, Double> sessionLiveWatchTimeMap = sessionEngagementDtoList.stream()
                            .collect(Collectors.toMap(
                                    UserSessionEngagementDto::getSessionId,
                                    dto -> dto.getLiveWatchTime() + dto.getRecordingWatchTime(),
                                    Double::sum // in case of duplicate sessionIds, sum the values
                            ));

                    progress = calculateUserChallengeProgress(sessionLiveWatchTimeMap, sessionIds, getRequiredWatchTime(criteria), sessionDurationMap);
                } else if (isContinueEdWithSessionCheckInAction(criteria)) {
                    List<Long> checkedIn = userSessionCheckIns.getOrDefault(userId, Collections.emptyList());
                    progress = calculatePercentage(
                        (double) checkedIn.stream().filter(sessionIds::contains).count(), 
                        (double) sessionIds.size()
                    );
                } else if (isContinueEdWithSessionCheckInAndSubmitSurveyAction(criteria)) {
                    // For check-in + survey criteria, track both check-ins and survey submissions
                    List<Long> checkedIn = userSessionCheckIns.getOrDefault(userId, Collections.emptyList());
                    int completedSessions = 0;
                    int totalSessionsWithSurveys = 0;
                    
                    for (Long sessionId : sessionIds) {
                        Session session = sessionMapById.get(sessionId);
                        if (session != null && session.getSurveyId() != null) {
                            totalSessionsWithSurveys++;
                            boolean isCheckedIn = checkedIn.contains(sessionId);
                            SurveyResponseKeyDto key = new SurveyResponseKeyDto(userId, session.getSurveyId(), sessionId);
                            Date submissionDate = surveyDateMap.get(key);
                            
                            if (isCheckedIn && submissionDate != null && 
                                !submissionDate.before(startDate) && 
                                !submissionDate.after(endDate)) {
                                completedSessions++;
                            }
                        }
                    }
                    
                    progress = totalSessionsWithSurveys > 0 ? 
                        calculatePercentage((double) completedSessions, (double) totalSessionsWithSurveys) : 
                        0.0;
                } else if (isContinueEdWithSurveySubmissionAction(criteria)) {
                    // For survey submission criteria, track completion per session
                    int completedSessions = 0;
                    int totalSessionsWithSurveys = 0;
                    List<Integer> quizScores = new ArrayList<>();
                    
                    // Find all sessions with surveys for this criteria
                    for (Long sessionId : sessionIds) {
                        Session session = sessionMapById.get(sessionId);
                        if (session != null && session.getSurveyId() != null) {
                            totalSessionsWithSurveys++;
                            SurveyResponseKeyDto key = new SurveyResponseKeyDto(userId, session.getSurveyId(), sessionId);
                            Date submissionDate = surveyDateMap.get(key);
                            
                            if (submissionDate != null && 
                                !submissionDate.before(startDate) && 
                                !submissionDate.after(endDate)) {
                                
                                completedSessions++;
                                
                                // For quiz surveys, collect the score
                                if (isQuiz) {
                                    Integer score = surveyScoreMap.get(key);
                                    if (score != null) {
                                        quizScores.add(score);
                                    }
                                }
                            }
                        }
                    }
                    
                    if (totalSessionsWithSurveys == 0) {
                        // No surveys required for these sessions
                        progress = 0.0;
                    } else if (!isQuiz) {
                        // For non-quiz surveys, check if all required surveys are submitted
                        progress = completedSessions == totalSessionsWithSurveys ? 100.0 : 0.0;
                    } else {
                        // For quiz surveys, check if all required surveys are submitted with passing scores
                        if (completedSessions < totalSessionsWithSurveys) {
                            progress = 0.0; // Not all surveys submitted
                        } else if (quizScores.isEmpty()) {
                            progress = 0.0; // No valid scores
                        } else {
                            double avgScore = quizScores.stream()
                                .mapToInt(Integer::intValue)
                                .average()
                                .orElse(0.0);
                            progress = avgScore >= requiredScore ? 100.0 : 0.0;
                        }
                    }
                }
                // Only increment completed count if progress is 100%
                if (progress >= 100.0) {
                    userCompletedCriteriaCount.merge(userId, 1, Integer::sum);
                }

                // Add to participated users if they have any progress
                if (progress > 0) {
                    participatedUsers.add(userId);
                }
            }
        }

        Set<Long> noParticipationUsers = new HashSet<>(allUsers);
        noParticipationUsers.removeAll(participatedUsers);

        int totalCompleted = userCompletedCriteriaCount.values().stream().mapToInt(Integer::intValue).sum();
        int completedUsers = userCompletedCriteriaCount.keySet().size();
        double attendeeSuccess = calculatePercentage((double) completedUsers, (double) allUsers.size());
        double participation = calculatePercentage((double) participatedUsers.size(), (double) allUsers.size());
        double noParticipation = calculatePercentage((double) noParticipationUsers.size(), (double) allUsers.size());
        double partialSuccess = 100.0 - attendeeSuccess - noParticipation;

        return new ContinuingEdStatisticsDTO(
                totalCompleted,
                attendeeSuccess,
                partialSuccess,
                noParticipation,
                participation
        );

    }

    private Map<SurveyResponseKeyDto, Integer> buildUserSurveyScoreMap(List<Object[]> responses) {
        Map<SurveyResponseKeyDto, Integer> map = new HashMap<>();
        for (Object[] row : responses) {
            if (row.length >= 5 && row[0] != null && row[1] != null && row[3] != null && row[4] != null) {
                Long surveyId = (Long) row[0];
                Integer score = (Integer) row[1];
                Long userId = (Long) row[3];
                Long sessionId = (Long) row[4];
                map.put(new SurveyResponseKeyDto(userId, surveyId, sessionId), score);
            }
        }
        return map;
    }

    private Map<SurveyResponseKeyDto, Date> buildUserSurveyDateMap(List<Object[]> responses) {
        Map<SurveyResponseKeyDto, Date> map = new HashMap<>();
        for (Object[] row : responses) {
            if (row.length >= 5 && row[0] != null && row[2] != null && row[3] != null && row[4] != null) {
                Long surveyId = (Long) row[0];
                Date date = (Date) row[2];
                Long userId = (Long) row[3];
                Long sessionId = (Long) row[4];
                map.put(new SurveyResponseKeyDto(userId, surveyId, sessionId), date);
            }
        }
        return map;
    }

    private void getSessionIdsAndTicketTypeIds(List<EventCECriteriaDTO> criterias, Set<Long> sessionIds, Set<Long> ticketTypes) {
        for (EventCECriteriaDTO criteria : criterias) {
            ticketTypes.addAll(criteria.getTicketTypeAllowInCriteria());
            sessionIds.addAll(getSessionIdsFromChallenge(criteria));
        }
    }

    private Set<Long> getChallengeParticipants(Map<Long, Set<Long>> usersByTicketTypeId, List<Long> allowedTicketTypes) {
        Set<Long> challengeParticipants = new HashSet<>();
        for (Long ticketType : allowedTicketTypes) {
            challengeParticipants.addAll(usersByTicketTypeId.getOrDefault(ticketType, new HashSet<>()));
        }
        // Adding Staff users even if they don't have tickets they can participates
        challengeParticipants.addAll(usersByTicketTypeId.getOrDefault(-1L, new HashSet<>()));
        return challengeParticipants;
    }

    private double calculateUserChallengeProgress(Map<Long, Double> sessionLiveWatchTimeMap,
                                                  List<Long> challengeSessionIds,
                                                  Integer requiredWatchTime,
                                                  Map<Long, Double> sessionDurationMap) {
        int completedSessions = 0;
        for (Long sessionId : challengeSessionIds) {
            double watchTime = toDouble(String.valueOf(sessionDurationMap.get(sessionId)));
            Double percentage = calculatePercentage(watchTime, sessionDurationMap.get(sessionId));
            if (percentage >= requiredWatchTime) {
                completedSessions++;
            }
        }
        return calculatePercentage(Double.valueOf(completedSessions), (double) challengeSessionIds.size());
    }

    private Map<Long, Set<Long>> convertToMap(List<Object[]> usersGroupByTicketTypeId) {
        if(CollectionUtils.isEmpty(usersGroupByTicketTypeId)) {
            return Collections.emptyMap();
        }
        Map<Long, Set<Long>> usersByTicketTypeId = new HashMap<>();
        for (Object[] users : usersGroupByTicketTypeId) {
            long ticketTypeId = ((BigInteger) users[0]).longValue();
            Set<Long> userIds = usersByTicketTypeId.getOrDefault(ticketTypeId, new HashSet<>());
            userIds.addAll(convertCommaSeparatedToListLong(users[1] != null ? users[1].toString() : Constants.STRING_EMPTY));
            usersByTicketTypeId.put(ticketTypeId, userIds);
        }
        return usersByTicketTypeId;
    }

    private Integer getRequiredWatchTime(EventCECriteriaDTO criteriaDTO) {
        Optional<Map<String, Object>> name = criteriaDTO.getAction().stream().filter(e -> SESSION_WATCH.equals(e.get(NAME))).findAny();
        return name.isPresent() ? (Integer) name.get().get(REQUIRED_WATCH_TIME) : 0;
    }

    private Integer getRequiredQuizScore(EventCECriteriaDTO criteriaDTO) {
        Optional<Map<String, Object>> name = criteriaDTO.getAction().stream().filter(e -> SURVEY_SUBMISSION.equals(e.get(NAME))).findAny();
        return name.isPresent() ? (Integer) name.get().get(REQUIRED_QUIZ_SCORE) : 0;
    }

    @Override
    public boolean isContinueEdWithSurveySubmissionAction(EventCECriteriaDTO criteriaDTO) {
        return criteriaDTO.getAction().stream().anyMatch(e -> SURVEY_SUBMISSION.equals(e.get(NAME)));
    }

    @Override
    public boolean isContinueEdWithSessionWatchAction(EventCECriteriaDTO criteriaDTO) {
        return criteriaDTO.getAction().stream().anyMatch(e -> SESSION_WATCH.equals(e.get(NAME)));
    }

    @Override
    public boolean isContinueEdWithSessionCheckInAction(EventCECriteriaDTO criteriaDTO) {
        return criteriaDTO.getAction().stream().anyMatch(e -> SESSION_CHECK_IN.equals(e.get(NAME)));
    }

    @Override
    public boolean isContinueEdWithSessionCheckInAndSubmitSurveyAction(EventCECriteriaDTO criteriaDTO) {
        return criteriaDTO.getAction().stream().anyMatch(e -> SESSION_CHECK_IN_AND_SURVEY_COMPLETION.equals(e.get(NAME)));
    }

    @Override
    public UserCreditManagementDetails prepareUserCreditManagementDetails(User user, Event event, EventCECriteriaDTO criteria) {
        log.info("prepareUserCreditManagementDetails user {} event {} and criteria {}",
                user.getUserId(), event.getEventId(), criteria.getCriteriaId());

        Map<String, Object> actionMap = criteria.getAction().stream().findFirst().orElse(Collections.emptyMap());
        int requiredQuizScore = Integer.parseInt(actionMap.getOrDefault(REQUIRED_QUIZ_SCORE, 0).toString());
        double rewardPoints = Double.parseDouble(actionMap.getOrDefault(LeaderBoardConstant.ChallengeConstants.POINT, 0).toString());



        boolean isSurveyAction = isContinueEdWithSurveySubmissionAction(criteria);
        boolean isCheckinAction = isContinueEdWithSessionCheckInAction(criteria);
        boolean isHybridAction = isContinueEdWithSessionCheckInAndSubmitSurveyAction(criteria);
        log.info("prepareUserCreditManagementDetails user {} event {} and criteria {} and survey action {} checkin {} hybrid {}",
                user.getUserId(), event.getEventId(), criteria.getCriteriaId(), isSurveyAction, isCheckinAction, isHybridAction);
        if (!(isSurveyAction || isCheckinAction || isHybridAction)) {
            log.info("No valid CE action type for user {} event {} criteria {}",
                    user.getUserId(), event.getEventId(), criteria.getCriteriaId());
            return null;
        }

        List<Long> sessionIds = getSessionIdsFromChallenge(criteria);
        List<Long> surveyIds = sessionService.findSurveyIdBySessionIds(sessionIds);

        List<Long> checkedInSessionIds = (isCheckinAction || isHybridAction)
                ? userSessionRepo.findCheckedInSessionIdByEventIdAndUserId(event.getEventId(), user.getUserId())
                : Collections.emptyList();

        boolean isCompleted = false;

        if (isSurveyAction && !CollectionUtils.isEmpty(surveyIds)) {
            if (surveyConfigRepoService.isAnySurveyQuizAble(surveyIds)) {
                Integer quizScore = surveyResponseRepoService.getUserSurveyScoreBySurveyIdsAndUserIdAndEvent(
                        surveyIds, user.getUserId(), event.getEventId(), criteria,sessionIds);
                if(quizScore != null){
                    isCompleted = quizScore >= requiredQuizScore;
                }
            } else {
                long submissionCount = surveyResponseRepoService.getCountOfSurveyResponseAndLatestSubmissionDateBySessionIdsAndUserId(
                        sessionIds, criteria, user);
                isCompleted = submissionCount == sessionIds.size();
            }

        } else if (isHybridAction) {
            boolean allSessionsCheckedIn = new HashSet<>(checkedInSessionIds).containsAll(sessionIds);
            if (allSessionsCheckedIn) {
                long submissionCount = surveyResponseRepoService.getCountOfSurveyResponseAndLatestSubmissionDateBySessionIdsAndUserId(
                        sessionIds, criteria, user);
                isCompleted = submissionCount == sessionIds.size();
            }

        } else if (isCheckinAction) {
            isCompleted = new HashSet<>(checkedInSessionIds).containsAll(sessionIds);
        }

        if (isCompleted) {
            log.info("prepareUserCreditManagementDetails  completed by user {} event {} and criteria {}",
                    user.getUserId(), event.getEventId(), criteria.getCriteriaId());
            UserCreditManagementDetails result = new UserCreditManagementDetails();
            result.setEventId(event.getEventId());
            result.setUserId(user.getUserId());
            result.setChallengeId(criteria.getCriteriaId());
            result.setCreditApplied(false);
            result.setTotalCredits(rewardPoints);
            return result;
        }
        log.info("prepareUserCreditManagementDetails not completed by user {} event {} and criteria {}",
                user.getUserId(), event.getEventId(), criteria.getCriteriaId());
        return null;
    }


    @Override
    public Map<Long, String> prepareChallengeWithSessionNamesMap(List<EventCECriteriaDTO> ceCriteriaDTOList, Event event) {
        if (CollectionUtils.isEmpty(ceCriteriaDTOList)) {
            return Collections.emptyMap();
        }

        // Map of challengeId -> sessionIds
        Map<Long, List<Long>> challengeWithSessionIdMap = ceCriteriaDTOList.stream()
                .collect(Collectors.toMap(
                        EventCECriteriaDTO::getCriteriaId,
                        this::getSessionIdsFromChallenge
                ));

        // Get all unique session IDs
        Set<Long> sessionIds = challengeWithSessionIdMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        if (sessionIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // Fetch session names in a single query

        List<SessionIdTitleDto>  sessionData = sessionRepo.getSessionIdAndTitleById(new ArrayList<>(sessionIds));

        // Convert sessionId -> sessionName map
        Map<Long, String> sessionIdToNameMap = sessionData.stream()
                .collect(Collectors.toMap(
                        SessionIdTitleDto::getId,
                        SessionIdTitleDto::getTitle
                ));

        // Convert challengeId -> sessionNames
        return challengeWithSessionIdMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(sessionIdToNameMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(", "))
                ));
    }

    @Override
    public UserCreditManagementDetails prepareUserCreditManagementDetailsIfUserPerformedAction(
            User user,
            Event event,
            EventCECriteriaDTO eventCECriteriaDTO,
            UserCreditManagementDetails userCreditManagementDetails) {

        log.info("Evaluating credit eligibility for user {} in event {} for criteria {}",
                user.getUserId(), event.getEventId(), eventCECriteriaDTO.getCriteriaId());

        boolean isSurveyAction = isContinueEdWithSurveySubmissionAction(eventCECriteriaDTO);
        boolean isCheckinAction = isContinueEdWithSessionCheckInAction(eventCECriteriaDTO);
        boolean isHybridAction = isContinueEdWithSessionCheckInAndSubmitSurveyAction(eventCECriteriaDTO);

        Map<String, Object> actionMap = eventCECriteriaDTO.getAction().stream()
                .findFirst()
                .orElse(Collections.emptyMap());

        int requiredQuizScore = Integer.parseInt(actionMap.getOrDefault(REQUIRED_QUIZ_SCORE, 0).toString());
        double rewardPoints = Double.parseDouble(actionMap.getOrDefault(LeaderBoardConstant.ChallengeConstants.POINT, 0).toString());

        if (!(isSurveyAction || isCheckinAction || isHybridAction)) {
            log.info("No eligible CE action for user {} in event {} with criteria {}",
                    user.getUserId(), event.getEventId(), eventCECriteriaDTO.getCriteriaId());
            userCreditManagementDetails.setTotalCredits(0.0);
            return userCreditManagementDetails;
        }

        List<Long> sessionIds = getSessionIdsFromChallenge(eventCECriteriaDTO);
        List<Long> surveyIds = sessionService.findSurveyIdBySessionIds(sessionIds);
        List<Long> userSessionIds = (isCheckinAction || isHybridAction)
                ? userSessionRepo.findCheckedInSessionIdByEventIdAndUserId(event.getEventId(), user.getUserId())
                : Collections.emptyList();

        boolean creditAwarded = false;

        if (isSurveyAction && !CollectionUtils.isEmpty(surveyIds)) {
            if (surveyConfigRepoService.isAnySurveyQuizAble(surveyIds)) {
                Integer userQuizScore = surveyResponseRepoService.getUserSurveyScoreBySurveyIdsAndUserIdAndEvent(
                        surveyIds, user.getUserId(), event.getEventId(), eventCECriteriaDTO, sessionIds);
                if(userQuizScore != null){
                    creditAwarded = userQuizScore >= requiredQuizScore;
                }
                log.info("Quiz score check: user {} scored {} (required {}) for criteria {}",
                        user.getUserId(), userQuizScore, requiredQuizScore, eventCECriteriaDTO.getCriteriaId());

            } else {
                long submittedCount = surveyResponseRepoService.getCountOfSurveyResponseAndLatestSubmissionDateBySessionIdsAndUserId(
                        sessionIds, eventCECriteriaDTO, user);

                log.info("Non-quiz survey: user {} submitted {}/{} surveys for criteria {}",
                        user.getUserId(), submittedCount, sessionIds.size(), eventCECriteriaDTO.getCriteriaId());

                creditAwarded = submittedCount == sessionIds.size();
            }

        } else if (isHybridAction) {
            boolean allSessionsCheckedIn = new HashSet<>(userSessionIds).containsAll(sessionIds);
            long submittedCount = surveyResponseRepoService.getCountOfSurveyResponseAndLatestSubmissionDateBySessionIdsAndUserId(
                    sessionIds, eventCECriteriaDTO, user);

            if (allSessionsCheckedIn && submittedCount == sessionIds.size()) {
                creditAwarded = true;
            } else if (allSessionsCheckedIn) {
                // Partial hybrid completion – only check-in done
                log.info("Hybrid criteria: user {} checked in but missing survey submissions for criteria {}",
                        user.getUserId(), eventCECriteriaDTO.getCriteriaId());
            }

        } else if (isCheckinAction) {
            creditAwarded = new HashSet<>(userSessionIds).containsAll(sessionIds);
        }

        if (creditAwarded) {
            userCreditManagementDetails.setTotalCredits(rewardPoints);
            log.info("Rewarded {} credits to user {} for criteria {}", rewardPoints, user.getUserId(), eventCECriteriaDTO.getCriteriaId());
        } else {
            userCreditManagementDetails.setTotalCredits(0.0);
            log.info("User {} did not meet the completion criteria {}", user.getUserId(), eventCECriteriaDTO.getCriteriaId());
        }

        return userCreditManagementDetails;
    }



    private void setLastActivityDate(Event event, String lastActivityAt, ContinuingEdUserDTO userDto, boolean isSurveySubmission) {
        if (StringUtils.isBlank(lastActivityAt)) {
            userDto.setLastActivityDate(Constants.NOT_APPLICABLE);
        } else {
            Date dateInLocal = TimeZoneUtil.getDateInLocal(DateUtils.getFormattedDate(lastActivityAt,isSurveySubmission ?Constants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_S : DATE_FORMAT), event.getEquivalentTimeZone());
            userDto.setLastActivityDate(DateUtils.getDateString(dateInLocal, CONTINUING_ED_DATE_FORMAT));
        }
    }

    private Double calculatePercentage(Double watchTime, Double sessionDuration){
        if ( watchTime != null && watchTime > 0 && sessionDuration != null && sessionDuration > 0) {
            return DoubleHelper.roundValueTwoDecimal(watchTime * 100 / sessionDuration);
        }
        return 0D;
    }

    private List<Long> getSessionIdsFromChallenge(EventCECriteriaDTO criteriaDTO) {
        List<Long> trackIds = new ArrayList<>();
        Set<Long> sessionIds = new HashSet<>();
        for (Map.Entry<String, List<Long>> entry : criteriaDTO.getTrigger().entrySet()) {
            String key = entry.getKey();
            List<Long> value = entry.getValue();
            if (TRACK.equals(key)) {
                trackIds.addAll(value);
            } else {
                sessionIds.addAll(value);
            }
        }
        List<Long> trackSessionIds = tagAndTrackService.findSessionIdsByIds(trackIds);
        sessionIds.addAll(trackSessionIds);
        return new ArrayList<>(sessionIds);
    }

    private List<Long> getSurveyIdsFromContinueEdChallengeTrigger(EventCECriteriaDTO criteriaDTO) {
        List<Long> sessionIds = this.getSessionIdsFromChallenge(criteriaDTO);
        return sessionService.findSurveyIdBySessionIds(sessionIds);
    }


    @Override
    public List<CECriteriaBasicDTO> getCompletedCriteriaOfUser(User user, Event event) {
        Long userId = user.getUserId();
        Long eventId = event.getEventId();
        log.info("getCompletedCriteriaOfUser | Fetching completed criteria for userId={} and eventId={}", userId, eventId);

        List<CECriteriaBasicDTO> completedCriteriaDtos = new ArrayList<>();

        // Fetch completed challenges for the user
        List<Long> completedChallengeIds = creditManagementRepository.findChallangeIdByEventIdAndUserId(eventId, userId);
        if (!completedChallengeIds.isEmpty()) {
            List<EventCECriteria> criteriaList = eventCECriteriaRepository.findCriteriaByChallengeIds(completedChallengeIds);
            for (EventCECriteria cr : criteriaList) {
                completedCriteriaDtos.add(new CECriteriaBasicDTO(cr.getId(), cr.getCriteriaName()));
            }
        }

        // Get all criteria for the user
        List<EventCECriteriaDTO> allCriteria = eventCECriteriaService.getCriteriaDTOsByUser(user, event);

        // Partition criteria into session-watch and others (only incomplete ones)
        Map<Boolean, List<EventCECriteriaDTO>> partitioned = allCriteria.stream()
                .filter(c -> !completedChallengeIds.contains(c.getCriteriaId()))
                .collect(Collectors.partitioningBy(c -> SESSION_WATCH.equalsIgnoreCase(getActionName(c))));

        List<EventCECriteriaDTO> sessionWatchCriterias = partitioned.getOrDefault(true, Collections.emptyList());
        List<EventCECriteriaDTO> otherIncompleteCriterias = partitioned.getOrDefault(false, Collections.emptyList());

        // Handle session watch criteria
        List<EventCECriteriaDTO> completedWatchCriterias = processWatchSessionCriteria(event, user, sessionWatchCriterias);
        for (EventCECriteriaDTO criteria : completedWatchCriterias) {
            completedCriteriaDtos.add(new CECriteriaBasicDTO(criteria.getCriteriaId(), criteria.getName()));
        }

        // Handle non-session-watch criteria
        if (!otherIncompleteCriterias.isEmpty()) {
            List<Long> challengeIds = new ArrayList<>(otherIncompleteCriterias.size());
            List<UserCreditManagementDetails> userCreditManagementDetailsList = new ArrayList<>(otherIncompleteCriterias.size());

            for (EventCECriteriaDTO ceCriteriaDTO : otherIncompleteCriterias) {
                UserCreditManagementDetails details = prepareUserCreditManagementDetails(user, event, ceCriteriaDTO);
                if (details != null) {
                    challengeIds.add(details.getChallengeId());
                    userCreditManagementDetailsList.add(details);
                }
            }

            if (!userCreditManagementDetailsList.isEmpty()) {
                creditManagementRepoService.saveAll(userCreditManagementDetailsList);

                if (!challengeIds.isEmpty()) {
                    List<EventCECriteria> criteriaList = eventCECriteriaRepository.findCriteriaByChallengeIds(challengeIds);
                    for (EventCECriteria cr : criteriaList) {
                        completedCriteriaDtos.add(new CECriteriaBasicDTO(cr.getId(), cr.getCriteriaName()));
                    }
                }

                log.info("getCompletedCriteriaOfUser | saved successfully user {} and event {} and size {}",
                        user.getUserId(), event.getEventId(), userCreditManagementDetailsList.size());
            }
        }

        return completedCriteriaDtos;
    }

    private List<EventCECriteriaDTO> processWatchSessionCriteria(Event event, User user, List<EventCECriteriaDTO> sessionWatchCriterias) {
        if (sessionWatchCriterias == null || sessionWatchCriterias.isEmpty()) {
            return Collections.emptyList();
        }

        Long userId = user.getUserId();
        Set<Long> sessionIds = new HashSet<>();
        Map<Long, List<Long>> criteriaSessionMap = new HashMap<>();

        for (EventCECriteriaDTO criteria : sessionWatchCriterias) {
            List<Long> sessions = getSessionIdsFromChallenge(criteria);
            if (!sessions.isEmpty()) {
                criteriaSessionMap.put(criteria.getCriteriaId(), sessions);
                sessionIds.addAll(sessions);
                log.info("Criteria ID {} has {} sessions", criteria.getCriteriaId(), sessions.size());
            }
        }

        if (sessionIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<UserSessionEngagementDto> sessionEngagementDtoList = consolidatedAnalyticsService.getUserSessionsEngagementDetails(event, userId);
        Map<Long, Double> sessionLiveWatchTimeMap = sessionEngagementDtoList.stream()
                .collect(Collectors.toMap(
                        UserSessionEngagementDto::getSessionId,
                        dto -> dto.getLiveWatchTime() + dto.getRecordingWatchTime(),
                        Double::sum // in case of duplicate sessionIds, sum the values
                ));


        Map<Long, Double> sessionDurationMap = sessionDetailsService.getSessionVideoDurationsByEvent(event);

        Map<Long, Session> sessionMapById = sessionService.getAllSessionByIds(new ArrayList<>(sessionIds), event)
                .stream()
                .collect(Collectors.toMap(Session::getId, s -> s));

        List<EventCECriteriaDTO> completed = new ArrayList<>();

        for (EventCECriteriaDTO criteria : sessionWatchCriterias) {
            List<Long> challengeSessionList = criteriaSessionMap.get(criteria.getCriteriaId());
            if (challengeSessionList == null || challengeSessionList.isEmpty()) {
                continue;
            }

            int completedCount = 0;
            int totalSessionCount = challengeSessionList.size();
            log.info("Evaluating criteria ID {} for watch session action", criteria.getCriteriaId());

            for (Long sessionId : challengeSessionList) {
                Session session = sessionMapById.get(sessionId);
                if (session == null) {
                    log.warn("Session with ID {} not found, skipping", sessionId);
                    continue;
                }

                double watchTime = sessionLiveWatchTimeMap.getOrDefault(sessionId, 0.0);
                double duration = sessionDurationMap.getOrDefault(sessionId, 0.0);
                double percentWatched = calculatePercentage(watchTime, duration);

                log.info("User watched sessionId={} for {}s ({}%) out of {}s", sessionId, watchTime, percentWatched, duration);

                if (percentWatched >= getRequiredWatchTime(criteria)) {
                    completedCount++;
                    log.info("Session ID {} completed for criteria {}", sessionId, criteria.getCriteriaId());
                }
            }

            if (completedCount == totalSessionCount) {
                completed.add(criteria);
            }
        }

        return completed;
    }


    @Override
    public List<UserCertificateDto> getAllCertificatesBasicDetailsByUser(Event event, User user) {
        Long userId = user.getUserId();
        Long eventId = event.getEventId();
        log.info("Fetching certificates for userId={} and eventId={}", userId, eventId);

        try {
            Optional<Boolean> isAllowAttendeeToDownloadCertificate = roEventLevelSettingService.allowAttendeeToDownloadCertificateByEventId(eventId);
            if (isAllowAttendeeToDownloadCertificate.isEmpty() || !isAllowAttendeeToDownloadCertificate.get()) {
                log.warn("Certificate download is disabled for eventId={}", eventId);
                return List.of();
            }

            List<UserCertificateDto> userCertificateDtoList = certificatesRepository.getAllCertificatesByEventIdAndCertificateType(eventId, CertificateTypes.AUTO).stream()
                    .map( certificate -> new UserCertificateDto(certificate.getId(), certificate.getCertificateName(), null, null,
                            CertificateTypes.AUTO, certificate.isDisplayOnVEH(), certificate.isDisplayOnApp())).collect(Collectors.toList());

            // Step 1: Get already completed challenge IDs (e.g. surveys submit)
            List<Long> completedChallengeIds = creditManagementRepository.findChallangeIdByEventIdAndUserId(eventId, userId);
            log.info("Found {} completed challenge IDs from credit management", completedChallengeIds.size());

            List<EventCECriteria> criteriaList = eventCECriteriaRepository.findCriteriaByChallengeIds(completedChallengeIds);

            Set<Long> certificateIds = criteriaList.stream()
                    .map(EventCECriteria::getCertificateId)
                    .collect(Collectors.toSet());

            Map<Long, Certificates> certificatesMap = certificatesRepository.findCertificatesByIds(new ArrayList<>(certificateIds)).stream()
                    .collect(Collectors.toMap(Certificates::getId, cert -> cert));

            // Step 2: Add completed criteria certificates directly
            Set<Long> addedCertIds = new HashSet<>();
            for (EventCECriteria criteria : criteriaList) {
                Long certId = criteria.getCertificateId();
                if (certId != null && certificatesMap.containsKey(certId)) {
                    Certificates cert = certificatesMap.get(certId);
                    if (cert != null) {
                        userCertificateDtoList.add(new UserCertificateDto(cert.getId(), cert.getCertificateName(), criteria.getCriteriaName(), criteria.getId(), cert.getCertificateType(), cert.isDisplayOnVEH(), cert.isDisplayOnApp()));
                        log.info("Added certificate '{}' for completed criteria {}", cert.getCertificateName(), criteria.getId());
                    }
                }
            }

            // Step 3: Get all criteria with certificates (e.g watch session)
            List<EventCECriteriaDTO> allWatchSessionCriterias = eventCECriteriaService.getCriteriaDTOsByUser(user, event).stream()
                    .filter(c -> c.getCertificateId() != null)
                    .filter(c -> SESSION_WATCH.equalsIgnoreCase(getActionName(c)))
                    .collect(Collectors.toList());

            List<Long> watchSessionCertificateIds = allWatchSessionCriterias.stream()
                    .map(EventCECriteriaDTO::getCertificateId)
                    .collect(Collectors.toList());

            Map<Long, Certificates> certificatesMapForWatchSession = certificatesRepository.findCertificatesByIds(new ArrayList<>(watchSessionCertificateIds)).stream()
                    .collect(Collectors.toMap(Certificates::getId, cert -> cert));


            log.info("Criteria: {} completed from credit, {} pending for session watch", criteriaList.size(), allWatchSessionCriterias.size());

            // Step 4: Process pending criteria using session watch logic
            List<EventCECriteriaDTO> completedWatchCriterias = processWatchSessionCriteria(event, user, allWatchSessionCriterias);
            for (EventCECriteriaDTO criteria : completedWatchCriterias) {
                Long certId = criteria.getCertificateId();
                if (certId != null) {
                    Certificates cert = certificatesMapForWatchSession.get(certId);
                    if (cert != null) {
                        userCertificateDtoList.add(new UserCertificateDto(certId, cert.getCertificateName(), criteria.getName(), criteria.getCriteriaId(), cert.getCertificateType(), cert.isDisplayOnVEH(), cert.isDisplayOnApp()));
                        log.info("Watch logic: Added certificate '{}' for criteria {}", cert.getCertificateName(), criteria.getCriteriaId());
                    }
                }
            }

            log.info("Total certificates prepared for user {}: {}", userId, userCertificateDtoList.size());
            return userCertificateDtoList;
        } catch (Exception e) {
            log.error("Exception while preparing certificates for userId={}, eventId={}: {}", userId, eventId, e.getMessage(), e);
            return List.of();
        }
    }

    private String getActionName(EventCECriteriaDTO criteria) {
        return criteria.getAction().stream()
                .map(m -> m.get("name"))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse("").toString();
    }


}
