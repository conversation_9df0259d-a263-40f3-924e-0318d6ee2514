package com.accelevents.continuing.ed.service;

import com.accelevents.common.dto.CECriteriaBasicDTO;
import com.accelevents.common.dto.EventChallengeDTO;
import com.accelevents.continuing.ed.ContinuingEdConstants;
import com.accelevents.continuing.ed.dto.ContinuingEdProgressDTO;
import com.accelevents.continuing.ed.dto.ContinuingEdStatisticsDTO;
import com.accelevents.continuing.ed.dto.ContinuingEdUserDTO;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.UserCreditManagementDetails;
import com.accelevents.dto.*;

import java.util.List;
import java.util.Map;

public interface ContinuingEdService {

    DataTableResponse getContinuingEdAnalytics(Long challengeId, ContinuingEdConstants.ContinuingEdStatus status, Event event, User loggedInUser, PageSizeSearchObj pageObj);

    DataTableResponse getContinuingEdAnalytics(EventCECriteriaDTO criteriaDTO, ContinuingEdConstants.ContinuingEdStatus status, Event event, User loggedInUser, PageSizeSearchObj pageObj);

    List<ContinuingEdProgressDTO> getContinuingEdAnalyticsByUser(Long challengeId, User user, Event event);

    List<ContinuingEdUserDTO> getEligibleUserByChallenge(Long criteriaId, Event event, User loggedInUser);

    List<ContinuingEdProgressDTO> getContinuingEdChallengesProgress(User user, Event event, List<EventCECriteriaDTO> continuingEdChallenges);

    ContinuingEdStatisticsDTO getContinuingEdStatistics(Event event, User loggedInUser);

    boolean isContinueEdWithSurveySubmissionAction(EventCECriteriaDTO criteriaDTO);

    boolean isContinueEdWithSessionWatchAction(EventCECriteriaDTO criteriaDTO);

    UserCreditManagementDetails prepareUserCreditManagementDetails(User user, Event event, EventCECriteriaDTO ceCriteriaDTO);

    Map<Long, String> prepareChallengeWithSessionNamesMap(List<EventCECriteriaDTO> ceCriteriaDTOList, Event event);

    UserCreditManagementDetails prepareUserCreditManagementDetailsIfUserPerformedAction(User user, Event event, EventCECriteriaDTO ceCriteriaDTO, UserCreditManagementDetails userCreditManagement);

    List<UserCertificateDto> getAllCertificatesBasicDetailsByUser(Event event, User user);

    boolean isContinueEdWithSessionCheckInAction(EventCECriteriaDTO criteriaDTO);

    boolean isContinueEdWithSessionCheckInAndSubmitSurveyAction(EventCECriteriaDTO criteriaDTO);

    List<CECriteriaBasicDTO> getCompletedCriteriaOfUser(User user, Event event);
}
