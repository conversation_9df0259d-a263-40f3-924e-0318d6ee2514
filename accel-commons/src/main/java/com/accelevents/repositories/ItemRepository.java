package com.accelevents.repositories;

import com.accelevents.auction.dto.AuctionAllItemDto;
import com.accelevents.auction.dto.AuctionItemDto;
import com.accelevents.common.dto.ItemInstructionDto;
import com.accelevents.domain.Item;
import com.accelevents.domain.ItemCategory;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.dto.ItemCategoryDetails;
import com.accelevents.dto.ItemCountDto;
import com.accelevents.fundaneed.dto.FundANeedAllItemDto;
import com.accelevents.perfomance.dto.AuctionBidAndItemAndBidderDto;
import com.accelevents.perfomance.dto.ItemNameCodeDto;
import com.accelevents.raffle.dto.RaffleAllItemDto;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ItemRepository extends CrudRepository<Item, Long> {

	Optional<Item> findItemById(long id);

	Optional<Item> findItemByCode(String code);

    Optional<Item> findItemByIdAndModuleType(long id,ModuleType moduleType);

	List<Item> findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(long moduleId, ModuleType moduleType);

	List<Item> findAllItemByModuleIdAndModuleTypeAndCode(long moduleId, ModuleType moduleType, String itemCode);

    @Query("SELECT item FROM Item AS item WHERE item.moduleId IN (:moduleId) AND item.code =:itemCode ")
    List<Item> findAllItemByModuleIdAndCode(@Param("moduleId") List<Long> moduleId,@Param("itemCode") String itemCode);

	Optional<Item> findItemByModuleIdAndModuleTypeAndCode(long moduleId, ModuleType moduleType, String itemCode);

	List<Item> findAllItemByModuleIdAndModuleTypeAndItemShortName(long moduleId, ModuleType moduleType,
																  String itemShortName);

	Page<Item> findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(long moduleId, ModuleType moduleType,
																	 Pageable pageable);

	Page<Item> findAllItemByModuleIdAndModuleTypeAndIsLiveAuctionItemOrderByPositionDesc(long moduleId, ModuleType moduleType,
																						 boolean isLiveAuctionItem, Pageable pageable);

	long countByModuleIdAndModuleTypeOrderByPositionDesc(long moduleId, ModuleType moduleType);

	List<Item> findAllItemByModuleIdAndModuleTypeAndCodeContainingOrderByPositionDesc(long moduleId,
																					  ModuleType moduleType, String code, Pageable pageable);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType " +
			" AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
	Page<Item> findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(@Param("moduleId") long moduleId,
																	 @Param("moduleType") ModuleType moduleType,
																	 @Param("searchString") String searchString,
																	 Pageable pageable);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.isLiveAuctionItem = true " +
			" AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
	Page<Item> findAllItemByModuleIdAndModuleTypeAndIsLiveAuctionItemOrderByPositionDesc(@Param("moduleId") long moduleId,
																	 @Param("moduleType") ModuleType moduleType,
																	 @Param("searchString") String searchString,
																	 Pageable pageable);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY item.position DESC")
	List<Item> findAllItemByModuleIdAndModuleTypeAndSearchStringOrderByPositionDesc(@Param("moduleId") long moduleId,
																					@Param("moduleType") ModuleType moduleType, @Param("searchString") String searchString);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.itemShortName LIKE %:searchString%) ORDER BY item.position DESC")
	List<Item> findItemsByModuleIdAndModuleTypeAndSearchStringOrderByPositionDesc(@Param("moduleId") long moduleId,
																					@Param("moduleType") ModuleType moduleType, @Param("searchString") String searchString);

	@Query("SELECT COUNT(item.id) FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
	long countByModuleIdAndModuleTypeAndCodeContainingOrderByPositionDesc(@Param("moduleId") long moduleId,
																		  @Param("moduleType") ModuleType moduleType, @Param("searchString") String searchString);

	List<Item> findAllItemByModuleIdAndModuleTypeAndActiveOrderByPositionDesc(long moduleId,

																			  ModuleType moduleType, boolean active);

    @Cacheable(value = "findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc", key = "#p0.toString()+#p1.name()+#p2" , condition = "#p2 == true")
	@Query("  SELECT new com.accelevents.dto.ItemCategoryDetails(item.id, itemCat) " +
			" FROM Item as item LEFT JOIN item.itemCategory as itemCat " +
			" WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=:active " +
			" ORDER BY item.position DESC ")
	List<ItemCategoryDetails> findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc(
			@Param("moduleId") long moduleId,@Param("moduleType") ModuleType moduleType, @Param("active") boolean active);

	@Query("  SELECT new com.accelevents.common.dto.ItemInstructionDto(item.code, item.startingBid) " +
			" FROM Item as item  WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=:active " +
			" ORDER BY item.position")
	Page<ItemInstructionDto> getItemCodeAndStartingBidOfItemWithLowestPosition(
			@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType, @Param("active") boolean active, Pageable pageable);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND " +
			"item.active=:active AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR " +
			"item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY CASE WHEN item.code LIKE %:searchString% THEN 0 ELSE 1 END" )
	Page<Item> findAllItemByModuleIdAndModuleTypeAndActiveAndCodeContainingOrderByPositionDesc(
            @Param("moduleId") long moduleId,
            @Param("moduleType") ModuleType moduleType,
            @Param("active") boolean active,
            @Param("searchString") String searchString,
            Pageable pageable);


	@Query("SELECT COUNT(item.id) FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=:active AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
	long countByModuleIdAndModuleTypeAndActiveAndCodeContainingOrderByPositionDesc(
			@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType,
			@Param("active") boolean active, @Param("searchString") String searchString);

	@Query("SELECT item FROM Item AS item WHERE " +
			"coalesce(item.itemCategory.id, -1)= coalesce(:itemCategoryId, item.itemCategory.id, -1) AND " +
			"item.moduleId=:moduleId AND " +
			"item.moduleType=:moduleType AND " +
			"item.active=:active AND " +
			"(item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)" +
			"AND ( (item.buyItNowPrice >=:itemBuyitnow OR coalesce(:itemBuyitnow, item.buyItNowPrice) = item.buyItNowPrice ) " +
			" AND item.currentBid = coalesce(:itemCurrentBid, item.currentBid )" +
			" AND item.isLiveAuctionItem = coalesce(:liveAuctionItemFlag, item.isLiveAuctionItem) " +
			" AND item.isLiveAuctionItem = coalesce(:silentAuctionItemFlag, item.isLiveAuctionItem ) " +
			" )  ORDER BY item.position DESC")
	Page<Item> findAllItemByModuleIdAndModuleTypeAndActiveAndItemCategoryAndCodeContainingOrderByPositionDesc(//NOSONAR
			@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType,
			@Param("active") boolean active,
			@Param("itemCategoryId") Long itemCategoryId,
			@Param("searchString") String searchString,
			Pageable pageable,
			@Param("liveAuctionItemFlag") Boolean liveAuctionItemFlag,
			@Param("itemCurrentBid") Integer itemCurrentBid,
			@Param("itemBuyitnow") Double itemBuyitnow,
			@Param("silentAuctionItemFlag") Boolean silentAuctionItemFlag);


	@Query("SELECT COUNT(item.id) FROM Item AS item WHERE " +
			" item.moduleId=:moduleId " +
			" AND item.moduleType=:moduleType " +
			" AND item.active=:active " +
			" AND item.itemCategory=:itemCategory " +
			" AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)" +
			" ORDER BY item.position DESC")
	long countByModuleIdAndModuleTypeAndActiveAndItemCategoryAndCodeContainingOrderByPositionDesc(
			@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType,
			@Param("active") boolean active,
			@Param("itemCategory") ItemCategory itemCategory,
			@Param("searchString") String searchString);

	@Query("SELECT item FROM Item AS item WHERE " +
			" coalesce(item.itemCategory.id, -1)= coalesce(:itemCategoryId,item.itemCategory.id,-1) AND " +
			" item.moduleId=:moduleId AND " +
			" item.moduleType=:moduleType AND " +
			" item.active=:active " +
			" AND ( (item.buyItNowPrice >=:itemBuyitnow OR coalesce(:itemBuyitnow, item.buyItNowPrice) = item.buyItNowPrice ) " +
			"      AND item.currentBid = coalesce(:itemCurrentBid, item.currentBid )" +
			"      AND item.isLiveAuctionItem = coalesce(:liveAuctionItemFlag, item.isLiveAuctionItem ) " +
			"      AND item.isLiveAuctionItem = coalesce(:silentAuctionItemFlag, item.isLiveAuctionItem ) " +
			" ) ORDER BY item.position DESC")
	Page<Item> findAllItemByModuleIdAndModuleTypeAndActiveAndItemCategoryOrderByPositionDesc(//NOSONAR
			@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType,
			@Param("active") boolean active,
			@Param("itemCategoryId") Long itemCategoryId,
			Pageable pageable,
			@Param("liveAuctionItemFlag") Boolean liveAuctionItemFlag,
			@Param("itemCurrentBid") Integer itemCurrentBid,
			@Param("itemBuyitnow") Double itemBuyitnow,
			@Param("silentAuctionItemFlag") Boolean silentAuctionItemFlag);



	long countByModuleIdAndModuleTypeAndActiveAndItemCategoryOrderByPositionDesc(long moduleId,
			ModuleType moduleType, boolean active, ItemCategory itemCategory);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=:active ORDER BY item.position DESC" )
	Page<Item> findAllItemByModuleIdAndModuleTypeAndActiveOrderByPosition(
            @Param("moduleId") long moduleId,
            @Param("moduleType") ModuleType moduleType,
            @Param("active") boolean active,
            Pageable pageable);

    @Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=:active ORDER BY item.position DESC" )
    List<Item> findAllItemByModuleIdAndModuleTypeAndActiveOrderByPosition(
            @Param("moduleId") long moduleId,
            @Param("moduleType") ModuleType moduleType,
            @Param("active") boolean active);

	Optional<Item> findFirstItemByModuleIdAndModuleTypeAndActiveOrderByPositionAsc(long moduleId, ModuleType moduleType, boolean active);

	long countByModuleIdAndModuleTypeAndActiveOrderByPositionDesc(long moduleId, ModuleType moduleType,
			boolean active);

	Item findFirstByModuleIdAndModuleTypeOrderByPositionDesc(long moduleId, ModuleType moduleType);

	List<Item> findAllItemByModuleIdAndAndModuleTypeAndCurrentHighBidder(long moduleId, ModuleType moduleType,
			User currentHighBidder);

	@Query("select item from Item item where item.moduleId = :moduleId AND item.moduleType =:moduleType AND item.position>:currentPosition ORDER BY item.position")
	List<Item> nextPositionItem(@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType,
			@Param("currentPosition") double currentPosition);

	@Query("select item from Item item where item.moduleId = :moduleId AND item.moduleType =:moduleType AND item.position<:currentPosition ORDER BY item.position DESC")
	List<Item> previousPositionItem(@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType,
			@Param("currentPosition") double currentPosition);

    @CacheEvict(value = "findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc",allEntries = true)
	@Modifying
	@Query("UPDATE Item set position=(position+:updatecount) where moduleId = :moduleId AND moduleType =:moduleType AND position> :startposition AND position <:endposition")
	void updatePositionItem(@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType,
			@Param("startposition") double startposition, @Param("endposition") double endposition,
			@Param("updatecount") double updatecount);

    @CacheEvict(value = "findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc",allEntries = true)
	@Modifying
	@Query("UPDATE Item set position=(position+:updatecount) where moduleId = :moduleId AND moduleType =:moduleType")
	void updatePositionForAllItem(@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType,
			@Param("updatecount") double updatecount);

    @CacheEvict(value = "findItemCategoriesCountByModuleIdAndModuleTypeAndActive",allEntries = true)
	@Modifying
	@Query("UPDATE Item set currentBid = :currentBid where moduleId = :moduleId AND moduleType =:moduleType")
	void updateCurrentBidForAllItemOfModule(@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType, @Param("currentBid") double bid);

    @CacheEvict(value = "findItemCategoriesCountByModuleIdAndModuleTypeAndActive",allEntries = true)
	@Modifying
	@Query("UPDATE Item set currentHighBidder = null where moduleId = :moduleId AND moduleType =:moduleType")
	void updateCurrentHighBidderForAllItemOfModule(@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType);

	@Modifying
	@Query("UPDATE Item set numberOfTickets = :numberOfTickets where moduleId = :moduleId AND moduleType =:moduleType")
	void updateNumberOfTicketsForAllItemOfModule(@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType, @Param("numberOfTickets") int numberOfTickets);

	@Modifying
	@Query("UPDATE Item SET currentHighBidder = :newUser WHERE currentHighBidder = :user")
	void updateByNewUser(@Param("user") User user, @Param("newUser") User newUser);

	@Query("SELECT new com.accelevents.auction.dto.AuctionItemDto(item.id, item.position, item.name, item.description, item.code, itemCategory.name,"
			+ " item.active, item.itemShortName, item.currentBid, item.startingBid, item.buyItNowPrice, item.marketValue, item.bidIncrement,imagelocation.id, imagelocation.imgLocations, imagelocation.position) "
			+ " FROM ItemImgLocations AS imagelocation RIGHT JOIN imagelocation.item AS item LEFT JOIN item.itemCategory AS itemCategory"
			+ " WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
	List<AuctionItemDto> findAllIAuctionItemByModuleIdAndModuleTypeOrderByPositionDesc(
			@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType,
			@Param("searchString") String searchString);

	@Query("SELECT new com.accelevents.auction.dto.AuctionItemDto(item.id, item.position, item.name, item.description, item.code, itemCategory.name,"
			+ " item.active, item.itemShortName, item.currentBid, item.startingBid, item.buyItNowPrice, item.marketValue, item.bidIncrement,imagelocation.id, imagelocation.imgLocations, imagelocation.position) "
			+ " FROM ItemImgLocations AS imagelocation RIGHT JOIN imagelocation.item AS item LEFT JOIN item.itemCategory AS itemCategory"
			+ " WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType")
	List<AuctionItemDto> findAllAuctionItemByModuleIdAndModuleTypeOrderByPositionDesc(
			@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType);

	@Query(value = "SELECT new com.accelevents.auction.dto.AuctionAllItemDto(item.id, item.name, item.code, item.startingBid, item.buyItNowPrice, item.position, "
			+ " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
			+ " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id) AS img ,"
			+ " item.active)"
			+ " FROM Item AS item "
			+ " WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION'" +
            " AND (coalesce(:searchString, 1) = 1 OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ")
    Page<AuctionAllItemDto> findAllAuctionItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDesc(
            @Param("moduleId") long moduleId, @Param("searchString") String searchString, Pageable pageable);

	@Query(value = "SELECT new com.accelevents.auction.dto.AuctionAllItemDto(item.id, item.name, item.code, item.startingBid, item.buyItNowPrice, item.position, "
			+ " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
			+ " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id) AS img , "
			+ " item.active)"
			+ " FROM Item AS item "
			+ " WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION' ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION' ")
    Page<AuctionAllItemDto> findAllAuctionItemDtoByModuleIdAndModuleTypeOrderByPositionDesc(@Param("moduleId") long moduleId, Pageable pageable);

	@Query(value = "SELECT new com.accelevents.fundaneed.dto.FundANeedAllItemDto(item.id, item.name, item.code, item.startingBid, item.position, "
			+ " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
			+ " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id) AS img ,"
			+ " item.active)"
			+ " FROM Item AS item "
			+ " WHERE item.moduleId=:moduleId AND item.moduleType='CAUSEAUCTION' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item WHERE item.moduleId=:moduleId " +
            " AND item.moduleType='CAUSEAUCTION' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
	Page<FundANeedAllItemDto> findAllFundANeedItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDesc(
            @Param("moduleId") long moduleId, @Param("searchString") String searchString, Pageable pageable);

	@Query(value = "SELECT new com.accelevents.fundaneed.dto.FundANeedAllItemDto(item.id, item.name, item.code, item.startingBid, item.position, "
			+ " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
			+ " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id) AS img ,"
			+ " item.active)"
			+ " FROM Item AS item "
			+ " WHERE item.moduleId=:moduleId AND item.moduleType='CAUSEAUCTION' ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType='CAUSEAUCTION'")
	Page<FundANeedAllItemDto> findAllFundANeedItemDtoByModuleIdAndModuleTypeOrderByPositionDesc(
            @Param("moduleId") long moduleId, Pageable pageable);

	@Query( value = "SELECT new com.accelevents.raffle.dto.RaffleAllItemDto(item.id, item.name, item.code, item.position, "
			+ " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
			+ " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id) AS img ,"
			+ " item.active)"
			+ " FROM Item AS item "
			+ " WHERE item.moduleId=:moduleId AND item.moduleType='RAFFLE' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item WHERE item.moduleId=:moduleId" +
            " AND item.moduleType='RAFFLE'" +
            " AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
	Page<RaffleAllItemDto> findAllRaffleItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDesc(
            @Param("moduleId") long moduleId, @Param("searchString") String searchString, Pageable pageable);

	@Query(value = "SELECT new com.accelevents.raffle.dto.RaffleAllItemDto(item.id, item.name, item.code, item.position, "
			+ " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
			+ " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id) AS img ,"
			+ " item.active)"
			+ " FROM Item AS item "
			+ " WHERE item.moduleId=:moduleId AND item.moduleType='RAFFLE' ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType='RAFFLE'")
	Page<RaffleAllItemDto> findAllRaffleItemDtoByModuleIdAndModuleTypeOrderByPositionDesc(
            @Param("moduleId") long moduleId, Pageable pageable);

	int countByModuleIdAndModuleTypeAndCurrentBid(long moduleId, ModuleType moduleType, double currentBid);

	Optional<Item> findFirstByModuleIdAndModuleTypeAndPositionGreaterThan(long moduleId, ModuleType moduleType, double position);

	int countItemByModuleIdAndModuleType(long moduleId, ModuleType moduleType);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType " +
			" AND item.active=:active AND item.id IN (:favouriteItemListIds) " +
			" ORDER BY item.position DESC")
	Page<Item> findAllItemByModuleIdAndModuleTypeAndActiveAndFavoriteItemOrderByPositionDesc(
			@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType,
			@Param("active") boolean active,
			@Param("favouriteItemListIds") List<Long> favouriteItemListIds,
			Pageable pageable);


	List<Item> findByIdIn(List<Long> itemIds);

	@Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=:active AND " +
			" (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)" +
			" AND item.id IN (:favouriteItemListIds) ORDER BY item.position DESC")
	Page<Item> findAllItemByModuleIdAndModuleTypeAndActiveAndFavoriteItemAndSearchStringOrderByPositionDesc(
			@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType,
			@Param("searchString") String searchString,
			@Param("active") boolean active,
			@Param("favouriteItemListIds") List<Long> favouriteItemListIds,
			Pageable pageable);

    @Cacheable(value = "findItemCategoriesCountByModuleIdAndModuleTypeAndActive",key = "#p0.toString()+#p1.name()+#p2")
	@Query("SELECT new com.accelevents.dto.ItemCountDto(SUM(CASE WHEN (item.buyItNowPrice) >1 THEN 1 ELSE 0 END), " +
			" SUM(CASE WHEN (item.currentBid) >1 THEN 0 ELSE 1 END)," +
			" SUM(CASE WHEN (item.isLiveAuctionItem) = true THEN 1 ELSE 0 END)," +
			" SUM(CASE WHEN (item.isLiveAuctionItem) = false THEN 1 ELSE 0 END))"+
			" FROM Item AS item WHERE " +
			" item.moduleId=:moduleId AND " +
			" item.moduleType=:moduleType AND " +
			" item.active=:active")
	ItemCountDto findItemCategoriesCountByModuleIdAndModuleTypeAndActive(
			@Param("moduleId") long moduleId,
			@Param("moduleType") ModuleType moduleType,
			@Param("active") boolean active);

	@Query("Select count(item.id) FROM Item item JOIN item.itemCategory as itemCategory WHERE itemCategory.id=:categoryId AND item.moduleType=:moduleType")
	Long findByItemCategoryId(@Param("moduleType") ModuleType moduleType,
									@Param("categoryId") long categoryId);
	@Query("SELECT DISTINCT item FROM Item as item JOIN AuctionBid as auctionBid ON auctionBid.item = item" +
			" WHERE auctionBid.auctionId = :auctionId AND item.active = :active" +
			" AND auctionBid.user = :user ORDER BY item.position DESC")
	Page<Item> findAllItemByModuleIdAndActiveAndUserOrderByPositionDesc(@Param("auctionId") Long auctionId, @Param("active") boolean active, @Param("user") User user, Pageable pageable);

    @Query("  SELECT new com.accelevents.perfomance.dto.AuctionBidAndItemAndBidderDto(item.id,item.name,item.code,item.numberOfWinners,item.position,item.currentBid) "+
            " FROM Item item WHERE item.id NOT IN (:excludedItemIds) " +
            " AND ((coalesce(:searchString, 1) = 1) OR (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.itemShortName LIKE %:searchString% ))" +
            " AND item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=coalesce(:active,item.active)" +
            " ORDER BY item.id ASC")
    List<AuctionBidAndItemAndBidderDto> findRemainItemByModuleIdAndModuleTypeForAuctionPerformance(@Param("excludedItemIds") Set<Long> excludedItemIds,
                                                                                                   @Param("moduleId") long moduleId,
                                                                                                   @Param("moduleType") ModuleType moduleType,
                                                                                                   @Param("active") Boolean active,
                                                                                                   @Param("searchString") String searchString);

	@Query("  SELECT new com.accelevents.perfomance.dto.AuctionBidAndItemAndBidderDto(item.id,item.name,item.code,item.numberOfWinners,item.position,item.currentBid) "+
			" FROM Item item WHERE item.id NOT IN (:excludedItemIds) " +
			" AND item.moduleId=:moduleId AND item.moduleType=:moduleType AND item.active=coalesce(:active,item.active)" +
			" ORDER BY item.id ASC")
	List<AuctionBidAndItemAndBidderDto> findRemainItemByModuleIdAndModuleTypeForAuctionPerformance(
			@Param("excludedItemIds") Set<Long> excludedItemIds,
			@Param("moduleId") long moduleId, @Param("moduleType") ModuleType moduleType, @Param("active") Boolean active);

	@Query("Select new com.accelevents.perfomance.dto.ItemNameCodeDto(item.name, item.code) " +
			" FROM Item item WHERE " +
			" (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.itemShortName LIKE %:searchString% )" +
			" AND ((item.moduleId =:auctionId AND item.moduleType='AUCTION') " +
			"		OR (item.moduleId =:raffleId AND item.moduleType='RAFFLE') " +//NOSONAR
			" 		OR (item.moduleId =:causeAuctionId AND item.moduleType='CAUSEAUCTION')" +//NOSONAR
			"	)" +//NOSONAR
			" ORDER BY CASE WHEN item.code like %:searchString% THEN 1 ELSE 2 END ")
	List<ItemNameCodeDto> findAllItemByModuleIdAndSearchStringOrderByPositionDesc(@Param("searchString") String searchString,
																				  @Param("auctionId") Long auctionId,
																				  @Param("raffleId") Long raffleId,
																				  @Param("causeAuctionId") Long causeAuctionId);

	@Query("Select new com.accelevents.perfomance.dto.ItemNameCodeDto(item.name,item.code)" +
			" FROM Item item WHERE" +
			" (item.code IN(:itemCodes))" +
			" AND ((item.moduleId =:auctionId AND item.moduleType='AUCTION') OR (item.moduleId =:raffleId AND item.moduleType='RAFFLE')" +
			" OR (item.moduleId =:causeAuctionId AND item.moduleType='CAUSEAUCTION'))")
	List<ItemNameCodeDto> findAllItemNameByModuleIdAndItemCodeOrderByPositionDesc(@Param("itemCodes") List<String> itemCodes,
																			  @Param("auctionId") Long auctionId, @Param("raffleId") Long raffleId, @Param("causeAuctionId") Long causeAuctionId);

	List<Item> findAllItemByModuleIdAndModuleType(long moduleId, ModuleType moduleType);

    @Query( value = "SELECT new com.accelevents.auction.dto.AuctionAllItemDto(item.id, item.name, item.code, item.startingBid, item.buyItNowPrice, item.position, "
            + " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
            + " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id and iml.imgLocations NOT IN (:listOfImage)) AS img , "
            + " item.active)"
            + " FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION' ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION' ")
    Page<AuctionAllItemDto> findAllAuctionItemDtoByModuleIdAndModuleTypeOrderByPositionDescWithoutWlLogo(@Param("moduleId") long moduleId, @Param("listOfImage") List<String> listOfImage, Pageable pageable);

    @Query(value = "SELECT new com.accelevents.auction.dto.AuctionAllItemDto(item.id, item.name, item.code, item.startingBid, item.buyItNowPrice, item.position, "
            + " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
            + " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id AND iml.imgLocations NOT IN (:listOfImage)) AS img ,"
            + " item.active)"
            + " FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='AUCTION' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
    Page<AuctionAllItemDto> findAllAuctionItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDescWithoutWlLogo(
            @Param("moduleId") long moduleId, @Param("searchString") String searchString, @Param("listOfImage") List<String> listOfImage, Pageable pageable);


    @Query(value = "SELECT new com.accelevents.raffle.dto.RaffleAllItemDto(item.id, item.name, item.code, item.position, "
            + " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
            + " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id AND iml.imgLocations NOT IN (:listOfImage)) AS img ,"
            + " item.active)"
            + " FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='RAFFLE' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='RAFFLE' " +
            " AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
    Page<RaffleAllItemDto> findAllRaffleItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDescWithoutWlLogo(
            @Param("moduleId") long moduleId, @Param("searchString") String searchString, @Param("listOfImage") List<String> listOfImage, Pageable pageable);

    @Query(value = "SELECT new com.accelevents.raffle.dto.RaffleAllItemDto(item.id, item.name, item.code, item.position, "
            + " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
            + " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id AND iml.imgLocations NOT IN (:listOfImage)) AS img ,"
            + " item.active)"
            + " FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='RAFFLE' ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='RAFFLE'")
    Page<RaffleAllItemDto> findAllRaffleItemDtoByModuleIdAndModuleTypeOrderByPositionDescWithoutWlLogo(
            @Param("moduleId") long moduleId, @Param("listOfImage") List<String> listOfImage, Pageable pageable);


    @Query(value = "SELECT new com.accelevents.fundaneed.dto.FundANeedAllItemDto(item.id, item.name, item.code, item.startingBid, item.position, "
            + " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
            + " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id AND iml.imgLocations NOT IN (:listOfImage)) AS img ,"
            + " item.active)"
            + " FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='CAUSEAUCTION' AND (item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%) ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='CAUSEAUCTION' AND " +
            "(item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
    Page<FundANeedAllItemDto> findAllFundANeedItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDescWithoutWlLogo(
            @Param("moduleId") long moduleId, @Param("searchString") String searchString, @Param("listOfImage") List<String> listOfImage, Pageable pageable);

    @Query(value = "SELECT new com.accelevents.fundaneed.dto.FundANeedAllItemDto(item.id, item.name, item.code, item.startingBid, item.position, "
            + " CASE WHEN item.description = '' OR item.description IS NULL THEN false ELSE true END AS desc, "
            + " (SELECT CASE WHEN COUNT(iml) > 0 THEN true ELSE false END FROM ItemImgLocations AS iml WHERE iml.item.id = item.id AND iml.imgLocations NOT IN (:listOfImage)) AS img ,"
            + " item.active)"
            + " FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='CAUSEAUCTION' ORDER BY item.position DESC",
    countQuery = "SELECT COUNT(1) FROM Item AS item "
            + " WHERE item.moduleId=:moduleId AND item.moduleType='CAUSEAUCTION'")
    Page<FundANeedAllItemDto> findAllFundANeedItemDtoByModuleIdAndModuleTypeOrderByPositionDescWithoutWlLogo(
            @Param("moduleId") long moduleId, @Param("listOfImage") List<String> listOfImage, Pageable pageable);

    @Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType " +
            "AND item.isLiveAuctionItem = true " +
            " AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
    List<Item> findAllItemByModuleIdAndModuleTypeAndIsLiveAuctionItemOrderByPositionDesc(@Param("moduleId") long moduleId,
                                                                                         @Param("moduleType") ModuleType moduleType,
                                                                                         @Param("searchString") String searchString);


    @Query("SELECT item FROM Item AS item WHERE item.moduleId=:moduleId AND item.moduleType=:moduleType " +
            " AND (item.code LIKE %:searchString% OR item.code LIKE %:searchString% " +
            "OR item.name LIKE %:searchString% OR item.description LIKE %:searchString%)")
    List<Item> findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(@Param("moduleId") long moduleId,
                                                                     @Param("moduleType") ModuleType moduleType,
                                                                     @Param("searchString") String searchString);


    @Query("SELECT i FROM Item i " +
            "WHERE i.id IN :itemIds " +
            "AND i.moduleId = :moduleId " +
            "AND i.moduleType = :moduleType")
    List<Item> findByModuleIdAndModuleTypeAndItemIds(@Param("moduleId") long moduleId,
                                                                            @Param("moduleType") ModuleType moduleType,
                                                                            @Param("itemIds") List<Long> itemIds);


    @Caching(evict = {
            @CacheEvict(value = "findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc", allEntries = true),
            @CacheEvict(value = "findItemCategoriesCountByModuleIdAndModuleTypeAndActive", allEntries = true)
    })
    Item save(Item item);

    @Caching(evict = {
            @CacheEvict(value = "findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc", allEntries = true),
            @CacheEvict(value = "findItemCategoriesCountByModuleIdAndModuleTypeAndActive", allEntries = true)
    })
    void delete(Item item);

    @Caching(evict = {
            @CacheEvict(value = "findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc", allEntries = true),
            @CacheEvict(value = "findItemCategoriesCountByModuleIdAndModuleTypeAndActive", allEntries = true)
    })
    void deleteById(long id);

    @Query("SELECT DISTINCT(item.id) FROM Item as item JOIN AuctionBid as auctionBid ON auctionBid.item = item" +
            " WHERE auctionBid.auctionId = :auctionId AND item.active = :active" +
            " AND auctionBid.user = :user ")
    List<Long> findAllItemIdByModuleIdAndActiveAndUser(@Param("auctionId") Long auctionId, @Param("active") boolean active, @Param("user") User user);

}


