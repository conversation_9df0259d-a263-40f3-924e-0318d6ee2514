package com.accelevents.repositories;

import com.accelevents.domain.PlanConfig;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.dto.WhiteLabelBillingSettingsDto;
import com.accelevents.dto.WLBasicDetailsDto;
import com.accelevents.enums.BillingType;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WhiteLabelRepository extends CrudRepository<WhiteLabel, Long> {

    @Cacheable(value = "findWhiteLabelByWhiteLabelUrl", key = "#p0",condition="#p0 != null",unless="#result == null")
	Optional<WhiteLabel> findWhiteLabelByWhiteLabelUrl(String whiteLabelUrl);

    @Query("Select new com.accelevents.dto.WhiteLabelBillingSettingsDto(whiteLabel.attendeeUploadCharge, whiteLabel.expoBoothCharge) FROM " +
            "WhiteLabel whiteLabel WHERE whiteLabel.id =:whiteLabelId")
    WhiteLabelBillingSettingsDto findWhiteLabelSettingsById(@Param("whiteLabelId") Long whiteLabelId);
    
    @Cacheable(value = "findWhiteLabelByHostBaseUrl", key = "#p0", condition="#p0 != null", unless="#result != null")
    List<WhiteLabel> findWhiteLabelByHostBaseUrlAndHostBaseUrlIsNotNull(String whiteLabelHostBaseUrl);

	@Query("SELECT whiteLabelUrl FROM WhiteLabel")
	List<String> findWhiteLabelUrl();

    @Query("SELECT new com.accelevents.dto.WLBasicDetailsDto(wl.id,wl.whiteLabelUrl,wl.hostBaseUrl,wl.playBackRestrictionId) FROM WhiteLabel AS wl WHERE wl.hostBaseUrl is not null AND wl.hostBaseUrl !='' ")
    List<WLBasicDetailsDto> getWhiteLabelBasicDetailsDto();

    @Caching(evict = {
            @CacheEvict(value = "findWhiteLabelByWhiteLabelUrl", key = "#p0.whiteLabelUrl"),
            @CacheEvict(value = "isSalesforceLoginRequiredByEventUrl",key = "#p0.whiteLabelUrl")
    })
	WhiteLabel save(WhiteLabel whiteLabel);


    @Query(value = "select w.* from white_label w " +
                   " where w.id in " +
                   " (select CASE WHEN length(s.white_label) > 0 THEN (s.white_label) ELSE 0 END from staff s left join " +
                   " users u on s.user_id = u.user_id where s.role = 'whitelabeladmin' " +
                   " and u.email LIKE %:searchString% ) OR " +
                   " w.firm_name LIKE %:searchString% OR w.white_label_url LIKE %:searchString% OR w.support_email LIKE %:searchString% OR " +
                   " w.chargebee_customer_id LIKE %:searchString% OR w.subscription_id LIKE %:searchString% ",
            countQuery = "select count(1) from white_label w " +
                    "  where w.id in " +
                    "  (select CASE WHEN length(s.white_label) > 0 THEN (s.white_label) ELSE 0 END from staff s left join " +
                    "  users u on s.user_id = u.user_id where s.role = 'whitelabeladmin' " +
                    "  and u.email LIKE %:searchString% ) OR " +
                    "  w.firm_name LIKE %:searchString% OR w.white_label_url LIKE %:searchString% OR w.support_email LIKE %:searchString% OR " +
                    "  w.chargebee_customer_id LIKE %:searchString% OR w.subscription_id LIKE %:searchString% ", nativeQuery = true)
    Page<WhiteLabel> getAllWhiteLabels(@Param("searchString") String searchString, Pageable pageable);

    @Query("SELECT wl FROM WhiteLabel AS wl WHERE wl.id BETWEEN :from AND :to and wl.chargebeeCustomerId is not null and wl.subscriptionId is not null")
    List<WhiteLabel> findAllWhiteLabel(@Param("from") long from, @Param("to") long to);

    @Query("SELECT wl FROM WhiteLabel AS wl WHERE wl.id BETWEEN :from AND :to and wl.hubspotCompanyId is not null")
    List<WhiteLabel> findAllWhiteLabelWithHubspotCompanyId(@Param("from") long from, @Param("to") long to);

    @Query("SELECT wl FROM WhiteLabel AS wl WHERE wl.id BETWEEN :from AND :to and wl.hostBaseUrl is not null")
    List<WhiteLabel> findAllWhiteLabelForMuxDomain(@Param("from") long from, @Param("to") long to);

    Optional<WhiteLabel> findBySubscriptionId(String subscriptionId);

    List<WhiteLabel> findBySubscriptionIdIn(List<String> subscriptionId);

    @Query("SELECT whiteLabel.waiveOffAttendeesUploadFee FROM  WhiteLabel whiteLabel where whiteLabel.id=:whiteLabelId")
    boolean findWaiveOffWLAttendeeUploadFlag(@Param("whiteLabelId") long whiteLabelId);

    List<WhiteLabel> findByChargebeeCustomerId(String chargebeeCustomerId);

    @Query("SELECT whiteLabel.billingType FROM  WhiteLabel whiteLabel where whiteLabel.id=:whiteLabelId")
    BillingType findBillingTypeByWhiteLabelId(@Param("whiteLabelId") Long whiteLabelId);

    @Query("Select new com.accelevents.dto.WhiteLabelBillingSettingsDto(whiteLabelUrl,billingType, " +
            "isAutoBilling, waiveOffAttendeesUploadFee, attendeeUploadCharge, expoBoothCharge,engageEmailsLimit, enableWhiteLabelRedirect) " +
            "FROM WhiteLabel " +
            "WHERE whiteLabelUrl =:whiteLabelUrl")
    WhiteLabelBillingSettingsDto getWhiteLabelBillingSettings(@Param("whiteLabelUrl") String whiteLabelUrl);

    @Query("SELECT wl FROM WhiteLabel AS wl WHERE wl.id BETWEEN :from AND :to and wl.planConfig.id = :planId")
    List<WhiteLabel> findAllWhiteLabelByPlanId(@Param("from") long from, @Param("to") long to, @Param("planId") long planId);

    @Query("SELECT hostBaseUrl FROM WhiteLabel WHERE hostBaseUrl IS NOT NULL")
    List<String> findAllWhiteLabelHostBaseUrl();

    @Query("SELECT whiteLabel.waiveOffAttendeesUploadFee FROM  WhiteLabel whiteLabel where whiteLabel.id=:whiteLabelId")
    boolean findWaiveOffAttendeeUploadByWhiteLabelId(@Param("whiteLabelId") Long whiteLabelId);
    @Query(value = "select w from WhiteLabel w join PlanConfig cp on w.planConfig.id = cp.id and cp.isLatestPlan=false" +
            " and w.planConfig.id not in (4) and w.id between :from and :to")
    List<WhiteLabel> findAllWhiteLabelWhoHaveOldPlans(@Param("from") long from, @Param("to") long to);
    @Query(value = "select w from WhiteLabel w join PlanConfig cp on w.planConfig.id = cp.id " +
            " and w.planConfig.id not in (4) and w.subscriptionId is not null and w.chargebeeCustomerId is not null and w.id between :from and :to")
    List<WhiteLabel> findAllWhiteLabelWhoHavePlans(@Param("from") long from, @Param("to") long to);

    @Query("select w from WhiteLabel w where w.subscriptionId IS NOT NULL and w.id between :from and :to")
    List<WhiteLabel> findAllWlBySubscriptionIdIsNotNullAnNotEmpty(@Param("from") long from, @Param("to") long to);
    @Query("select w from WhiteLabel w where w.subscriptionId IS NOT NULL and w.id between :from and :to")
    List<WhiteLabel> findAllWlBySubscriptionIdIsNotNull(@Param("from") long from, @Param("to") long to);
    @Query("select w from WhiteLabel w join Organizer o on w.id=o.whiteLabel.id where w.planConfig.id!=4 and (o.subscriptionId IS NULL OR o.chargebeeCustomerId IS NULL) and w.id between :from and :to")
    List<WhiteLabel> findAllWhiteLabelWhoseOrganisersPlanNotSetProperly(@Param("from") long from, @Param("to") long to);

    @Query("SELECT wl FROM WhiteLabel AS wl WHERE wl.id BETWEEN :from AND :to")
    List<WhiteLabel> findAllWhiteLabelBetween(@Param("from") long from, @Param("to") long to);
    @Query(value = "select w from WhiteLabel w join PlanConfig cp on w.planConfig.id = cp.id " +
            "where cp.isLatestPlan is true")
    List<WhiteLabel> findAllWhiteLabelWhoHaveLatestPlan();
}
