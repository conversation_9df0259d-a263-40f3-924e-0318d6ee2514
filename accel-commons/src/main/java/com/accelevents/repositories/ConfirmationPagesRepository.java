package com.accelevents.repositories;

import com.accelevents.domain.CustomTemplates;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.ConfirmationApprovalType;
import com.accelevents.domain.enums.TemplateType;
import com.accelevents.dto.CustomTemplateDetailsDto;
import com.accelevents.dto.CustomTemplatesDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ConfirmationPagesRepository extends CrudRepository<CustomTemplates, Long> {

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.event = :event AND t.templateType = 'CONFIRMATION_PAGE'")
    List<CustomTemplates> findByEvent(Event event);

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.event = :event AND t.templateType =:templateType")
    List<CustomTemplates> findByEventAndTemplateType(Event event,TemplateType templateType);

    @Query(value = "SELECT new com.accelevents.dto.CustomTemplateDetailsDto(t.id, t.templateType, t.pageType, t.pageName, t.position, t.createdAt, t.updatedAt, t.isDefaultPage, t.isPageUpdated, t.pageUrl, t.confirmationApprovalType) " +
            "FROM CustomTemplates t WHERE t.event = :event" +
            " AND (coalesce(:searchString, 1) = 1 OR t.pageName LIKE %:searchString%) AND t.templateType = 'CONFIRMATION_PAGE' ORDER BY t.position DESC")
    Page<CustomTemplateDetailsDto> getConfirmationPagesByEvent(@Param("event") Event event, Pageable pageable, @Param("searchString") String searchString);

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.event = :event AND t.templateType = 'CONFIRMATION_PAGE' AND t.isDefaultPage = :isDefaultPage")
    CustomTemplates findByEventAndIsDefaultPage(Event event, boolean isDefaultPage);

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.event = :event AND t.templateType =:templateType AND t.isDefaultPage = :isDefaultPage")
    CustomTemplates findByEventAndIsDefaultPageAndTemplateType(Event event, boolean isDefaultPage,TemplateType templateType);

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.event.id = :eventId AND t.pageName = :pageName AND t.templateType = 'CONFIRMATION_PAGE'")
    CustomTemplates findByEventAndPageName(Long eventId, String pageName);

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.id = :id AND t.templateType =:templateType ")
    Optional<CustomTemplates> findByIdAndTemplateType(Long id, TemplateType templateType);

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.event.id = :eventId AND t.pageName = :pageName AND t.templateType = :templateType")
    CustomTemplates findByEventAndPageNameAndTemplateType(Long eventId, String pageName,TemplateType templateType);

    @Query(value = "SELECT t FROM CustomTemplates t WHERE t.event = :event AND t.templateType IN (:templateTypes) AND t.position = (SELECT MAX(t2.position) FROM CustomTemplates t2 WHERE t2.event = :event AND t2.templateType IN (:templateTypes))")
    CustomTemplates findFirstByEventAndOrderByPositionDesc(@Param("event") Event event, @Param("templateTypes") List<TemplateType>  templateTypes);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.id=:id AND ct.eventId=:eventId AND ct.templateType = 'CONFIRMATION_PAGE' AND ct.position > :position ORDER BY ct.position ")
    List<CustomTemplates> nextPositionConfirmationPages(@Param("id") Long id,@Param("eventId") Long eventId,@Param("position") Double position);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.id=:id AND ct.eventId=:eventId AND ct.templateType = 'CONFIRMATION_PAGE' AND ct.position < :position ORDER BY ct.position DESC")
    List<CustomTemplates> previousPositionConfirmationPages(@Param("id") Long id,@Param("eventId") Long eventId,@Param("position") Double position);

    @Modifying
    @Query(value = "UPDATE CustomTemplates AS ct SET ct.position =(ct.position + :positionDifference) WHERE ct.eventId = :eventId "
            + " AND ct.position > :startPosition AND ct.position < :endPosition AND ct.templateType = :templateType")
    void updatePositionCustomTemplate(@Param("eventId") long eventId, @Param("startPosition") double startPosition,@Param("endPosition") double endPosition,@Param("positionDifference") double positionDifference,
                                      @Param("templateType") String templateType);

    @Modifying
    @Query(value = "UPDATE custom_templates AS ct SET ct.position =(ct.position + :sequence) WHERE ct.event_id = :eventId AND ct.template_type = :templateType"
            + " ORDER BY ct.position DESC ", nativeQuery = true)
    void updatePositionForAllCustomTemplates(@Param("eventId") Long eventId,@Param("sequence") double sequence,@Param("templateType") String templateType);

    @Query(value = "SELECT count(t.id) FROM CustomTemplates t WHERE t.event.id = :eventId AND t.pageName = :pageName AND t.templateType = :templateType")
    long findCustomPageCountByName(@Param("eventId")long eventId,@Param("pageName") String pageName,@Param("templateType")TemplateType templateType);

    CustomTemplates findByPageUrlAndEvent(String pageUrl,Event event);

    @Query(value = "SELECT new com.accelevents.dto.CustomTemplatesDto(t.id, t.templateType, t.pageType, t.pageName,t.htmlValue, t.unPublishedHtml) " +
            "FROM CustomTemplates t WHERE t.eventId = :eventId AND t.id= :confirmationPageId " )
    CustomTemplatesDto getConfirmationPageByPageIdAndEventId(@Param("confirmationPageId") Long confirmationPageId,@Param("eventId")long eventId);

    @Query(value = "SELECT new com.accelevents.dto.CustomTemplatesDto(t.id, t.templateType, t.pageType, t.pageName,t.htmlValue, t.unPublishedHtml) " +
            "FROM CustomTemplates t WHERE t.eventId = :eventId AND t.id= :confirmationPageId AND t.templateType = :templateType" )
    CustomTemplatesDto getConfirmationPageByPageIdAndEventIdAndTemplateType(@Param("confirmationPageId") Long confirmationPageId,@Param("eventId")long eventId,@Param("templateType") TemplateType templateType);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.id in (:customTemplateIds) AND ct.eventId=:eventId")
    List<CustomTemplates> findByEventAndCustomTemplatesIdIn(@Param("eventId") Long eventId,@Param("customTemplateIds") List<Long> customTemplateIds);

    List<CustomTemplates> findByEventIdAndConfirmationApprovalType(Long eventId, ConfirmationApprovalType confirmationApprovalType);

    @Query("SELECT t.event.id FROM CustomTemplates t WHERE t.event.id IN :eventIds AND t.templateType = :templateType AND t.isDefaultPage = :isDefaultPage")
    List<Long> findEventIdsWithTemplate(
            @Param("eventIds") List<Long> eventIds,
            @Param("templateType") TemplateType templateType,
            @Param("isDefaultPage") boolean isDefaultPage
    );

}
