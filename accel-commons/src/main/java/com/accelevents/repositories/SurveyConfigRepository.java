package com.accelevents.repositories;

import com.accelevents.common.dto.SurveysDto;
import com.accelevents.domain.SurveyConfiguration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyConfigRepository extends CrudRepository<SurveyConfiguration,Long> {

    @Query("SELECT CASE WHEN COUNT(surveyConfiguration.surveyId) > 0 THEN true ELSE false END FROM SurveyConfiguration AS surveyConfiguration WHERE surveyConfiguration.surveyName=:surveyName AND surveyConfiguration.eventId=:eventId")
    boolean isSurveyNameIsExistInEvent(@Param("surveyName") String surveyName, @Param("eventId") long eventId);

    @Query("SELECT surveyConfiguration FROM SurveyConfiguration AS surveyConfiguration WHERE surveyConfiguration.eventId =:eventId AND cast(lower(surveyConfiguration.surveyName) as string) LIKE %:searchString%")
    Page<SurveyConfiguration> findByEventIdAndSearchString(@Param("eventId") long eventId, @Param("searchString") String searchString,Pageable pageable);

    @Query("SELECT surveyConfiguration from SurveyConfiguration as surveyConfiguration where surveyConfiguration.eventId=:eventId")
    Page<SurveyConfiguration> findByEventId(@Param("eventId") long eventId, Pageable pageable);

    @Query("SELECT CASE WHEN COUNT(surveyConfiguration.surveyId) > 0 THEN true ELSE false END FROM SurveyConfiguration AS surveyConfiguration WHERE surveyConfiguration.surveyId=:surveyId AND surveyConfiguration.eventId=:eventId")
    boolean isSurveyExistById(@Param("eventId") Long eventId,@Param("surveyId") Long surveyId);

    @Query("SELECT surveyConfiguration from SurveyConfiguration as surveyConfiguration where surveyConfiguration.eventId=:eventId")
    List<SurveyConfiguration> findSurveysByEventId(@Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.common.dto.SurveysDto(surveyConfiguration.surveyId,surveyConfiguration.surveyName) FROM SurveyConfiguration as surveyConfiguration " +
            " WHERE surveyConfiguration.eventId=:eventId " )
    List<SurveysDto> getAllSurveysByEvent(Long eventId);

    @Query("SELECT COUNT(surveyCfg.surveyId) > 0 FROM SurveyConfiguration as surveyCfg " +
            " WHERE surveyCfg.isSurveyQuizEnabled IS TRUE AND surveyCfg.surveyId IN (:surveyIds)")
    boolean isAnySurveyQuizAble(@Param("surveyIds") List<Long> surveyIds);

    @Query("SELECT surveyConfiguration.surveyId FROM SurveyConfiguration as surveyConfiguration " +
            " WHERE surveyConfiguration.eventId=:eventId " +
            " AND surveyConfiguration.isSurveyQuizEnabled IS TRUE ")
    List<Long> getAllQuizAbleSurveyIds(@Param("eventId") Long eventId);

    @Query("SELECT surveyConfiguration from SurveyConfiguration as surveyConfiguration where surveyConfiguration.surveyId IN (:surveyIds)")
    List<SurveyConfiguration> findSurveysByIds(@Param("surveyIds") List<Long> surveyIds);

    @Query("SELECT new com.accelevents.common.dto.SurveysDto(surveyConfiguration.surveyId,surveyConfiguration.surveyName) FROM SurveyConfiguration as surveyConfiguration " +
            " WHERE surveyConfiguration.surveyId IN (:surveyIds) " )
    List<SurveysDto> getSurveysByIds(@Param("surveyIds") List<Long> surveyIds);

    @Query("SELECT sc FROM SurveyConfiguration sc " +
            "WHERE sc.eventId = :eventId AND sc.isDefaultSurvey = true")
    SurveyConfiguration findDefaultSurveyByEventId(@Param("eventId") long eventId);
}
