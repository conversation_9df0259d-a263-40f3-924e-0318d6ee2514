package com.accelevents.repositories;

import com.accelevents.common.dto.ReviewerEmailDto;
import com.accelevents.domain.ReviewerEmail;
import com.accelevents.domain.enums.ReviewerEmailType;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ReviewerEmailRepository extends CrudRepository<ReviewerEmail, Long> {

    @Query("SELECT NEW com.accelevents.common.dto.ReviewerEmailDto(re.id,re.eventId,re.subject,re.body,re.isAutoSendMail) " +
            "FROM ReviewerEmail re " +
            "WHERE re.eventId = :eventId")
    Optional<ReviewerEmailDto> findByEventId(@Param("eventId") Long eventId);

    @Query("SELECT NEW com.accelevents.common.dto.ReviewerEmailDto(re.id,re.eventId,re.subject,re.body,re.isAutoSendMail,re.type) " +
            "FROM ReviewerEmail re " +
            "WHERE re.eventId = :eventId " +
            "AND re.type=:type")
    Optional<ReviewerEmailDto> findByEventIdAndType(@Param("eventId") Long eventId,@Param("type") ReviewerEmailType type);

    @Query("SELECT NEW com.accelevents.common.dto.ReviewerEmailDto(re.id,re.eventId,re.subject,re.body,re.isAutoSendMail,re.type) " +
            "FROM ReviewerEmail re " +
            "WHERE re.eventId = :eventId ")
    List<ReviewerEmailDto> findAllByEventId(@Param("eventId") Long eventId);
}
