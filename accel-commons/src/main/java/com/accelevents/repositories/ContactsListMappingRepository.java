package com.accelevents.repositories;

import com.accelevents.domain.Contacts;
import com.accelevents.domain.ContactsList;
import com.accelevents.domain.ContactsListMapping;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ContactsListMappingRepository extends CrudRepository<ContactsListMapping, Long> {

    @Query("SELECT c FROM ContactsListMapping c WHERE c.contacts = :contacts AND c.contactsList = :contactsList")
    Optional<ContactsListMapping> findByContactsAndContactsList(@Param("contacts") Contacts contacts, @Param("contactsList") ContactsList contactsList);

    @Query("SELECT c FROM ContactsListMapping c WHERE c.contactsList = :contactsList")
    List<ContactsListMapping> findAllByContactsList(@Param("contactsList") ContactsList contactsList);

    @Query("SELECT clm FROM ContactsListMapping clm " +
            "WHERE clm.contactsList.eventId = :eventId " +
            "AND clm.contactsList.id = :contactsListId ")
    List<ContactsListMapping> findByEventIdAndContactsListId(@Param("eventId") Long eventId,
                                                                          @Param("contactsListId") Long contactsListId);

    @Query("SELECT clm FROM ContactsListMapping clm " +
            "WHERE clm.contacts.id IN :contactsIds " +
            "AND clm.contactsList.eventId = :eventId " +
            "AND clm.contactsList.id = :contactsListId ")
    List<ContactsListMapping> findByContactsIdAndEventIdAndContactsListId(@Param("contactsIds") List<Long> contactsIds,
                                                                          @Param("eventId") Long eventId,
                                                                          @Param("contactsListId") Long contactsListId);


    @Query(value = "select * from contacts_list_mapping where contacts_list_id in (:contactListId)", nativeQuery = true)
    List<ContactsListMapping> findByContactsListIn(@Param("contactListId") List<Long> contactListId);

    @Query("select clm.contacts.id from ContactsListMapping clm where clm.contacts In :contacts and clm.contactsList = :contactsList")
    List<Long> getContactIdsByContactsListIdAndContactsId(@Param("contacts") Iterable<Contacts> contacts, @Param("contactsList") ContactsList contactsList);

}
