package com.accelevents.repositories.require.attributes;

import java.math.BigInteger;
import java.util.List;
import java.util.Optional;

import com.accelevents.exhibitors.dto.TicketHolderAttributeDto;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.accelevents.domain.TicketHolderAttributes;
import org.springframework.data.repository.query.Param;

public interface TicketHolderAttributesRepository extends CrudRepository<TicketHolderAttributes, Long> {

	TicketHolderAttributes findByid(long id); //NOSONAR

    List<TicketHolderAttributes> findByIdIn(List<Long> ids);

    @Query(nativeQuery=true,value="SELECT ticket_holder_attributes_id FROM event_tickets WHERE ticket_holder_attributes_id IN (SELECT ticket_holder_attributes_id FROM event_tickets GROUP BY ticket_holder_attributes_id HAVING COUNT(*) > 1)GROUP BY ticket_holder_attributes_id")
	List<BigInteger> findSameForMultipleTicket();

    List<TicketHolderAttributes> findAllByJsonValueIsNull();

    @Query(nativeQuery=true,value="SELECT * FROM ticket_holder_attributes ta " +
            " Join event_tickets et on et.ticket_holder_attributes_id = ta.id " +
            " Where et.staff_user_id = 1 AND et.ticketing_type_id =:eventTicketTypeId")
    List<TicketHolderAttributes> findByEventTicketTypeId(@Param("eventTicketTypeId") long eventTicketTypeId);

	@Modifying
	@Query(nativeQuery=true,
			value = " UPDATE ticket_holder_attributes tha " +
					" JOIN event_tickets et on tha.id = et.ticket_holder_attributes_id " +
					"        JOIN ticketing_order torder on et.ticket_order_id = torder.id " +
					" SET tha.json_value =REPLACE(tha.json_value,:jsonOldName,:jsonNewName) " +
					" WHERE torder.event_id = :eventId and tha.id > 0")
	void updateAttribute(@Param("jsonOldName") String jsonOldName,
						 @Param("eventId") Long eventId,
						 @Param("jsonNewName") String jsonNewName);

	@Query(value = "select tt from TicketHolderAttributes tt where (tt.jsonValue like '%Mailing Address%')" +
			" and tt.id in (67924,\n" +
			"67925,\n" +
			"68006,\n" +
			"69287,\n" +
			"69481,\n" +
			"69482,\n" +
			"71410,\n" +
			"71411,\n" +
			"71412,\n" +
			"71413,\n" +
			"71414,\n" +
			"72264,\n" +
			"72300,\n" +
			"72634,\n" +
			"72635,\n" +
			"72636,\n" +
			"72637,\n" +
			"72804,\n" +
			"73995,\n" +
			"73996,\n" +
			"73997,\n" +
			"75494,\n" +
			"75495,\n" +
			"75496,\n" +
			"75497,\n" +
			"75498,\n" +
			"75499,\n" +
			"75888,\n" +
			"76342,\n" +
			"76343,\n" +
			"76344,\n" +
			"76349,\n" +
			"76350,\n" +
			"66957,\n" +
			"67507,\n" +
			"67508,\n" +
			"67530,\n" +
			"67684,\n" +
			"67899,\n" +
			"71415,\n" +
			"73998,\n" +
			"75500,\n" +
			"66158,\n" +
			"66159,\n" +
			"66160,\n" +
			"66161,\n" +
			"66162,\n" +
			"66163,\n" +
			"66164,\n" +
			"66165,\n" +
			"66166,\n" +
			"66167,\n" +
			"66168,\n" +
			"66171,\n" +
			"66172,\n" +
			"66173,\n" +
			"66174,\n" +
			"66175,\n" +
			"66176,\n" +
			"66177,\n" +
			"66178,\n" +
			"66179,\n" +
			"66180,\n" +
			"66181,\n" +
			"66945,\n" +
			"66999,\n" +
			"67000,\n" +
			"67001,\n" +
			"67002)")
    List<TicketHolderAttributes> findMailingAddress();

	@Query(" SELECT new com.accelevents.exhibitors.dto.TicketHolderAttributeDto(et.id, holderAttribute.id, holderAttribute.jsonValue )" +
			" FROM EventTickets et JOIN et.ticketHolderAttributesId as holderAttribute"  +
			" WHERE et.id IN (:eventTicketIds)")
	List<TicketHolderAttributeDto> findByEventTicketIds(@Param("eventTicketIds") List<Long> eventTicketIds);

    @Modifying
    @Query("UPDATE TicketHolderAttributes SET value=:newValue , jsonValue=:newValue WHERE id IN (:ids)")
    void updateTicketHolderAttribute(@Param("ids") List<Long> ids , @Param("newValue") String newValue);


    @Modifying
    @Query("UPDATE TicketHolderAttributes SET jsonValue=json_set(jsonValue,'$.\"holder\".\"attributes\".\"First Name\"',:firstName, '$.\"holder\".\"attributes\".\"Last Name\"', :lastName) WHERE id IN (:ids)")
    void updateFirstNameAndLastName(@Param("firstName") String firstName, @Param("lastName") String lastName,@Param("ids") List<Long> ids);

    @Modifying
    @Query("UPDATE TicketHolderAttributes SET jsonValue=json_set(jsonValue,'$.\"holder\".\"attributes\".\"First Name\"',:firstName, '$.\"holder\".\"attributes\".\"Last Name\"', :lastName, '$.\"holder\".\"attributes\".\"Location\"',:attribute) WHERE id IN (:ids)")
    void updateHolderAttributes(@Param("firstName") String firstName, @Param("lastName") String lastName, @Param("attribute") String attribute, @Param("ids") List<Long> ids);

    @Query("SELECT et.ticketHolderAttributesId FROM EventTickets et " +
            " WHERE et.ticketingOrderId = :ticketingOrderId " +
            " AND et.ticketStatus <> 'DELETED' " +
            " AND et.dataType = 'TICKET' " +
            " ORDER BY et.id DESC ")
    List<TicketHolderAttributes> findTopByTicketOrderIdOrderByIdDesc(@Param("ticketingOrderId") Long ticketingOrderId, Pageable pageable);
}
