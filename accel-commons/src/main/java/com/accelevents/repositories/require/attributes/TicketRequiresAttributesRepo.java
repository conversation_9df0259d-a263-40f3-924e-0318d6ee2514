package com.accelevents.repositories.require.attributes;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.ticketing.dto.AttributesDefaultValuesDto;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface TicketRequiresAttributesRepo extends CrudRepository<TicketHolderRequiredAttributes, Long> {

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event WHERE attribute.eventid=:event AND attribute.recurringEventId IS NULL AND attribute.dataType = 'TICKET' AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<TicketHolderRequiredAttributes> findByEventidAndRecurringEventIdNull(@Param("event")Event event);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event WHERE attribute.eventid=:event AND attribute.recurringEventId IS NULL AND attribute.dataType = 'TICKET' AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') AND attribute.enabledForTicketPurchaser = true")
    List<TicketHolderRequiredAttributes> findByEventidAndRecurringEventIdIsNullAndEnabledForTicketPurchaser(@Param("event")Event event);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event " +
            " WHERE attribute.eventid=:event AND attribute.recurringEventId IS NULL AND attribute.dataType = 'TICKET' " +
            " AND attribute.attributeValueType!='CONDITIONAL_QUE' AND ( attribute.parentQuestionId = 0 OR attribute.parentQuestionId IS NULL ) " +
            " AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') AND attribute.enabledForTicketPurchaser = true ")
    List<TicketHolderRequiredAttributes> findByEventidAndRecurringEventIdIsNullAndEnabledForTicketPurchaserAndAttributeTypeNotConditionalQue(@Param("event")Event event);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event WHERE attribute.eventid=:event AND attribute.recurringEventId IS NULL AND attribute.dataType = 'TICKET' AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') AND attribute.enabledForTicketHolder = true")
    List<TicketHolderRequiredAttributes> findByEventidAndRecurringEventIdIsNullAndEnabledForTicketHolder(@Param("event")Event event);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event " +
            " WHERE attribute.eventid=:event AND attribute.recurringEventId IS NULL AND attribute.dataType = 'TICKET' " +
            " AND attribute.attributeValueType!='CONDITIONAL_QUE' AND ( attribute.parentQuestionId = 0 OR attribute.parentQuestionId IS NULL ) " +
            " AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED') AND attribute.enabledForTicketHolder = true ")
    List<TicketHolderRequiredAttributes> findByEventidAndRecurringEventIdIsNullAndEnabledForTicketHolderAndAttributeTypeNotConditionalQue(@Param("event")Event event);

    List<TicketHolderRequiredAttributes> findByEventidAndDataTypeOrderByBuyerAttributeOrder(Event event, DataType dataType);

    TicketHolderRequiredAttributes findFirstByEventidAndDataTypeOrderByBuyerAttributeOrderDesc(Event event, DataType dataType);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.attributeValueType=:attributeValueType AND attributes.eventid=:event AND attributes.recurringEventId IS NULL AND attributes.dataType=:dataType")
    TicketHolderRequiredAttributes findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(AttributeValueType attributeValueType, Event event, DataType dataType);

    @Query("select attributes FROM TicketHolderRequiredAttributes attributes where attributes.eventid =:event AND attributes.buyerAttributeOrder>:currentPosition  AND attributes.dataType='TICKET' ORDER BY attributes.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> nextPositionAttribute(@Param("event") Event event, @Param("currentPosition") int currentPosition);

    @Query("select attributes FROM TicketHolderRequiredAttributes attributes where attributes.eventid = :event AND attributes.buyerAttributeOrder<:currentPosition AND attributes.dataType='TICKET' ORDER BY attributes.buyerAttributeOrder DESC")
    List<TicketHolderRequiredAttributes> previousPositionAttribute(@Param("event") Event event, @Param("currentPosition") int currentPosition);

    @Modifying
    @Query("UPDATE TicketHolderRequiredAttributes set buyerAttributeOrder=(buyerAttributeOrder+:updateCount) where eventid = :event AND buyerAttributeOrder> :startPosition AND buyerAttributeOrder <:endPosition AND dataType='TICKET'")
    void updatePositionItem(@Param("event") Event event, @Param("startPosition") int startPosition, @Param("endPosition") int endPosition,
                            @Param("updateCount") int updateCount);

    @Modifying
    @Query("UPDATE TicketHolderRequiredAttributes set buyerAttributeOrder=(buyerAttributeOrder+:updateCount) where eventid= :event AND dataType= :dataType")
    void updatePositionForAllAttribute(@Param("event") Event event, @Param("updateCount") int updateCount, @Param("dataType") DataType dataType);

    @Query("select attributes FROM TicketHolderRequiredAttributes attributes where " +
            " attributes.eventid =:event " +
            " AND attributes.name  IN(:attributes) " +
            " AND (attributes.enabledForTicketPurchaser =:enabledForTicketHolder " +
            " OR attributes.enabledForTicketHolder =:enabledForTicketHolder)" +
            " AND attributes.dataType='TICKET'")
    List<TicketHolderRequiredAttributes> customAttributeForSequence(@Param("event") Event event, @Param("enabledForTicketHolder") Boolean enabledForTicketHolder, @Param("attributes") List<String> attributes);

    @Modifying
    @Query(nativeQuery = true, value = "Update ticket_holder_required_attributes set holder_event_ticket_type_id=:ticketTypeIds " +
            " WHERE event_id=:eventId AND attribute_name IN ('First Name','Last Name','Email')" +
            " AND data_type='TICKET'")
    void updateAttribute( @Param("eventId")  long eventId, @Param("ticketTypeIds")  String ticketTypeIds);

    // Common
    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event and attributes.createdFrom=:attributeId AND attributes.dataType='TICKET'")
    List<TicketHolderRequiredAttributes> findByCreatedFromIdAndEventId(@Param("attributeId") Long attributeId,@Param("event") Event event);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.name=:attributeName AND attributes.eventid=:event AND attributes.recurringEventId IS NOT NULL AND attributes.dataType='TICKET'")
    List<TicketHolderRequiredAttributes> findAllAttributesBynameAndEventAndRecurringIdNotNull(@Param("event") Event event, @Param("attributeName") String attributeName);

    // Checked
    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes JOIN FETCH attributes.eventid as event WHERE attributes.eventid=:event AND attributes.recurringEventId=:recurringEventId AND attributes.dataType=:dataType AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<TicketHolderRequiredAttributes> findAllAttributesByRecurringEventId(@Param("event") Event event,@Param("recurringEventId")  Long recurringEventId, @Param("dataType") DataType dataType);

    TicketHolderRequiredAttributes findBynameAndEventidAndRecurringEventIdAndDataType(String name, Event event, Long recurringEventId, DataType dataType);

    @Query( " SELECT attributes FROM TicketHolderRequiredAttributes attributes " +
            " WHERE attributes.name IN (:attributeNames) " +
            " AND attributes.eventid=:event " +
            " AND attributes.recurringEventId is null " +
            " AND attributes.dataType=:dataType " )
    List<TicketHolderRequiredAttributes> findAllByAttributeNamesAndEventIdAndRecurringEventIdIsNULLAndDataType(@Param("attributeNames") List<String> attributeNames, @Param("event") Event event, @Param("dataType") DataType dataType);


    @Modifying
    @Query("DELETE FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId IN (:recurringEventsToBeDelete) AND attributes.dataType='TICKET'")
    void deleteByRecurringIdAndEventId(@Param("recurringEventsToBeDelete") List<Long> recurringEventsToBeDelete,@Param("event") Event event);

    @Query("Select attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.attributeValueType=:attributeValueType AND attributes.eventid=:event AND attributes.recurringEventId=:recurringEventId AND attributes.dataType='TICKET'" )
    TicketHolderRequiredAttributes findByattributeValueTypeAndEventidAndRecurringEventId(@Param("attributeValueType") AttributeValueType attributeValueType,@Param("event") Event event,@Param("recurringEventId") Long recurringEventId);

    @Query("Select attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.createdFrom=:createdFrom")
    List<TicketHolderRequiredAttributes>  findAllAttributesByCreatedFromAndEventId(@Param("createdFrom") Long createdFrom,@Param("event")  Event event);

    @Query("Select attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId IS NOT NULL AND attributes.dataType='TICKET'")
    List<TicketHolderRequiredAttributes> getAllWhichHavingRecurringEventIdNotNull(@Param("event")  Event event);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId=:recurringEventId AND (:dataType is null OR attributes.dataType=:dataType)  ORDER BY attributes.holderAttributeOrder" )
    List<TicketHolderRequiredAttributes> findAllAttributesByRecurringEventIdOrderByBuyerAttributeOrder(@Param("event") Event event, @Param("recurringEventId")  Long recurringEventId, @Param("dataType") DataType dataType);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId=:recurringEventId AND (:dataType is null OR attributes.dataType=:dataType)  AND attributes.enabledForTicketPurchaser = TRUE AND (attributes.parentQuestionId is null OR attributes.parentQuestionId=0 )  ORDER BY attributes.holderAttributeOrder" )
    List<TicketHolderRequiredAttributes> findAllAttributesByRecurringEventIdHavingEnableForHolderOrderByHolderAttributeOrder(@Param("event") Event event, @Param("recurringEventId")  Long recurringEventId, @Param("dataType") DataType dataType);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId=:recurringEventId AND (:dataType is null OR attributes.dataType=:dataType)  AND attributes.enabledForTicketHolder = TRUE AND (attributes.parentQuestionId is null OR attributes.parentQuestionId=0 )   ORDER BY attributes.buyerAttributeOrder" )
    List<TicketHolderRequiredAttributes> findAllAttributesByRecurringEventIdHavingEnableForBuyerOrderByBuyerAttributeOrder(@Param("event") Event event, @Param("recurringEventId")  Long recurringEventId, @Param("dataType") DataType dataType);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId is null AND (:dataType is null OR attributes.dataType=:dataType) ORDER BY attributes.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> findByEventIdAndRecurringEventIdNullOrderByBuyerAttributeOrder(@Param("event") Event event, @Param("dataType") DataType dataType);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId is null AND (:dataType is null OR attributes.dataType=:dataType) AND attributes.enabledForTicketHolder = TRUE AND (attributes.parentQuestionId is null OR attributes.parentQuestionId=0 )  ORDER BY attributes.holderAttributeOrder")
    List<TicketHolderRequiredAttributes> findByEventIdAndRecurringEventIdNullHavingEnableForHolderOrderByHolderAttributeOrder(@Param("event") Event event, @Param("dataType") DataType dataType);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId is null AND (:dataType is null OR attributes.dataType=:dataType) AND attributes.enabledForTicketPurchaser = TRUE AND (attributes.parentQuestionId is null OR attributes.parentQuestionId=0 )  ORDER BY attributes.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> findByEventIdAndRecurringEventIdNullHavingEnableForBuyerOrderByBuyerAttributeOrder(@Param("event") Event event, @Param("dataType") DataType dataType);


    @Query("Select attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.recurringEventId IN (:oldReIdsList)")
    List<TicketHolderRequiredAttributes> findTicketingAttributesByRecurringEventList(@Param("oldReIdsList") List<Long> oldReIdsList);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:eventId" +
            " AND attributes.name=:attributeName" +
            " AND attributes.recurringEventId is not null")
    List<TicketHolderRequiredAttributes> findByEventIdAndRecurringEventIdNotNull(@Param("eventId") Event eventId,@Param("attributeName") String attributeName);


    @Modifying
    @Query("UPDATE TicketHolderRequiredAttributes SET status = :status WHERE recurringEventId IN (:recurringEventIds) AND eventid = :eventid AND dataType='TICKET'")
    void updateTicketHolderAttributeRecStatusByRecurringEventIds(@Param("recurringEventIds") List<Long> recurringEventIds, @Param("eventid") Event eventid, @Param("status") RecordStatus status);

    @Query(" SELECT attributes " +
            " FROM TicketHolderRequiredAttributes attributes " +
            " WHERE attributes.id IN (:ids) " +
            " ORDER BY attributes.buyerAttributeOrder ")
    List<TicketHolderRequiredAttributes> findByIdInOrderByBuyerAttributeOrder(@Param("ids") List<Long> ids);

    @Query("Select attributes FROM TicketHolderRequiredAttributes attributes " +
            "WHERE attributes.eventid=:event " +
            "AND name in (:attributes) ")
    List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributesByExhibitor(@Param("event")  Event event, @Param("attributes") List<String> attributes);

    @Query("select attributes FROM TicketHolderRequiredAttributes attributes where attributes.eventid =:event AND attributes.holderAttributeOrder>:currentPosition  AND attributes.dataType='TICKET' ORDER BY attributes.holderAttributeOrder")
    List<TicketHolderRequiredAttributes> nextPositionForHolderAttribute(@Param("event") Event event, @Param("currentPosition") int currentPosition);

    @Query("select attributes FROM TicketHolderRequiredAttributes attributes where attributes.eventid = :event AND attributes.holderAttributeOrder<:currentPosition AND attributes.dataType='TICKET' ORDER BY attributes.holderAttributeOrder DESC")
    List<TicketHolderRequiredAttributes> previousPositionForHolderAttribute(@Param("event") Event event, @Param("currentPosition") int currentPosition);

    @Modifying
    @Query("UPDATE TicketHolderRequiredAttributes set holderAttributeOrder=(holderAttributeOrder+:updateCount) where eventid = :event AND holderAttributeOrder> :startPosition AND holderAttributeOrder <:endPosition AND dataType='TICKET'")
    void updatePositionForHolderItem(@Param("event") Event event, @Param("startPosition") int startPosition, @Param("endPosition") int endPosition,
                            @Param("updateCount") int updateCount);

    @Modifying
    @Query("UPDATE TicketHolderRequiredAttributes set holderAttributeOrder=(holderAttributeOrder+:updateCount) where eventid= :event AND dataType= :dataType")
    void updatePositionForAllHolderAttribute(@Param("event") Event event, @Param("updateCount") int updateCount, @Param("dataType") DataType dataType);

    TicketHolderRequiredAttributes findFirstByEventidAndDataTypeOrderByHolderAttributeOrderDesc(Event event, DataType dataType);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event WHERE attribute.eventid=:event AND attribute.recurringEventId IS NULL AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<TicketHolderRequiredAttributes> findByEventidAndRecurringEventIdNullAndTicketAndAddOnDataType(@Param("event")Event event);

    @Query("SELECT attribute.name FROM TicketHolderRequiredAttributes as attribute where attribute.eventid=:event and attribute.dataType = 'TICKET' ORDER BY attribute.buyerAttributeOrder")
    List<String> getAllBuyerEnabledAttributeNameByEvent(@Param("event") Event event);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute WHERE attribute.eventid=:event AND attribute.parentQuestionId=:parentQueId")
    List<TicketHolderRequiredAttributes> findAllSubQueByParentIdAndEventId(@Param("event") Event event,@Param("parentQueId") long parentQueId);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute WHERE attribute.eventid=:event AND attribute.parentQuestionId IN (:parentQueIds) ORDER BY attribute.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> findAllSubQueByParentIdsAndEventId(@Param("event") Event event,@Param("parentQueIds") List<Long> parentQueIds);
    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute where attribute.eventid=:event and attribute.dataType = 'TICKET'  ORDER BY attribute.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> getAllTicketAttributeByEvent(@Param("event") Event event);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute WHERE attribute.eventid=:event AND attributeValueType='CONDITIONAL_QUE' AND dataType=:dataType")
    List<TicketHolderRequiredAttributes> findAllConditionalQuestionByEventAndDataType(@Param("event") Event event,@Param("dataType") DataType dataType);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event WHERE attribute.eventid=:event AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<TicketHolderRequiredAttributes> findAllByEventId(@Param("event")Event event);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute JOIN FETCH attribute.eventid as event WHERE attribute.eventid=:event AND attribute.recurringEventId IS NULL AND ( event.eventStatus is null or event.eventStatus <> 'EVENT_DELETED')")
    List<TicketHolderRequiredAttributes> findAllByEventidAndRecurringEventIdNull(@Param("event")Event event);

    @Query("Select attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.recurringEventId IS NOT NULL")
    List<TicketHolderRequiredAttributes> findAllWhichHavingRecurringEventIdNotNull(@Param("event")  Event event);

    @Query("select attributes FROM TicketHolderRequiredAttributes attributes where attributes.eventid =:event " +
            " AND attributes.name IN (:attributes) AND attributes.dataType='TICKET'")
    List<TicketHolderRequiredAttributes> findByAttributeNameListAndEventId(@Param("event")  Event event, @Param("attributes") List<String> attributes);

    Optional<TicketHolderRequiredAttributes> findByIdAndEventidAndDataType(Long id, Event event, DataType dataType);

    @Query("SELECT distinct attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid in(:event) AND attributes.recurringEventId is null AND (:dataType is null OR attributes.dataType=:dataType) ORDER BY attributes.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> findByEventIdInAndRecurringEventIdNullOrderByBuyerAttributeOrder(@Param("event") List<Event> event, @Param("dataType") DataType dataType);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute WHERE attribute.eventid=:event AND attributeValueType=:attributeValueType")
    List<TicketHolderRequiredAttributes> findByattributeValueTypeAndEventId(@Param("attributeValueType") AttributeValueType attributeValueType, @Param("event") Event event);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes JOIN FETCH attributes.eventid as event WHERE attributes.eventid=:event AND attributes.recurringEventId=:recurringEventId")
    List<TicketHolderRequiredAttributes> findAllAttributesByEventAndRecurringEventId(@Param("event") Event event,@Param("recurringEventId")  Long recurringEventId);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes JOIN FETCH attributes.eventid as event WHERE attributes.eventid=:event AND attributes.recurringEventId in (:recurringEventId)")
    List<TicketHolderRequiredAttributes> findAllAttributesByEventAndRecurringEventIds(@Param("event") Event event,@Param("recurringEventId")  List<Long> recurringEventId);

    @Query("SELECT attributes.name FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.enabledForTicketHolder=true order by attributes.holderAttributeOrder")
    List<String> findTicketHolderRequiredAttributesByEventIdAndEnabledForHolders(Event event);

    @Query("SELECT attributes.name FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND attributes.enabledForTicketPurchaser=true AND attributes.dataType= 'ADDON' order by attributes.buyerAttributeOrder")
    List<String> findTicketHolderRequiredAttributesByEventIdAndAddon(Event event);


    List<TicketHolderRequiredAttributes> findByIdInAndEventidAndDataType(List<Long> ids,Event event, DataType dataType);

    @Query("SELECT attribute.name FROM TicketHolderRequiredAttributes as attribute where attribute.eventid=:event and attribute.dataType='TICKET' and attribute.dataType = 'TICKET' ORDER BY attribute.buyerAttributeOrder")
    List<String> getHolderEnabledAttribute(@Param("event") Event event);

    @Query(value = " WITH RECURSIVE tree AS ( " +
            "    SELECT *  FROM ticket_holder_required_attributes " +
            "    WHERE id in (:ids) " +
            "    AND enabled_for_ticket_purchaser = 1 " +
            "    AND recurring_event_id is null " +
            "    AND data_type = 'TICKET' " +
            "    AND attribute_name NOT IN (:attributeNames) " +
            "    UNION ALL " +
            "    SELECT childnode.* " +
            "    FROM ticket_holder_required_attributes childnode " +
            "    JOIN tree ON childnode.parent_question_id = tree.id where childnode.event_id=:eventId" +
            "    ) " +
            "    SELECT * FROM tree ORDER BY buyer_attribute_order; ", nativeQuery = true)
    List<TicketHolderRequiredAttributes> getBuyersAttributesByIdsAndEnabledForTicketBuyer(List<Long> ids, List<String> attributeNames, long eventId);

    @Query(value = " WITH RECURSIVE tree AS ( " +
            "    SELECT *  FROM ticket_holder_required_attributes " +
            "    WHERE id in (:ids) " +
            "    AND enabled_for_ticket_holder = 1 " +
            "    AND recurring_event_id is null " +
            "    AND data_type = 'TICKET' " +
            "    AND attribute_name NOT IN (:attributeNames) " +
            "    UNION ALL " +
            "    SELECT childnode.* " +
            "    FROM ticket_holder_required_attributes childnode " +
            "    JOIN tree ON childnode.parent_question_id = tree.id where childnode.event_id=:eventId" +
            "    ) " +
            "    SELECT * FROM tree ORDER BY holder_attribute_order; ",nativeQuery = true)
    List<TicketHolderRequiredAttributes>  getHolderAttributesByIdsAndEnabledForTicketHolder(List<Long> ids,List<String> attributeNames,long eventId);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND enabledForTicketPurchaser=true AND attributes.id IN (:ids) ")
    List<TicketHolderRequiredAttributes> findByEventAndIds(@Param("event") Event event,@Param("ids")List<Long> ticketAttributeIds);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes WHERE attributes.eventid=:event AND hiddenForHolderRegistration=true and enabledForTicketHolder=true")
    List<TicketHolderRequiredAttributes> findByEventIdAndHiddenHolderRegistrationTrue(@Param("event") Event event);

    @Query("SELECT NEW com.accelevents.ticketing.dto.AttributesDefaultValuesDto(attributes.id, attributes.attributeValueType, attributes.defaultValueJsonPurchaser, attributes.defaultValueJsonHolder) FROM TicketHolderRequiredAttributes attributes " +
            " WHERE attributes.eventid=:event" +
            " AND attributes.isAttribute is true" +
            " AND attributes.dataType = 'TICKET' " +
            " AND attributes.attributeValueType IN ('DROPDOWN','MULTIPLE_CHOICE')")
    List<AttributesDefaultValuesDto> getAttributesDefaultValuesByEvent(@Param("event") Event event);


    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute" +
            " WHERE attribute.eventid=:event " +
            " AND attribute.parentQuestionId=:parentQueId " +
            " AND attribute.selectedAnswer=:ansId")
    TicketHolderRequiredAttributes findByEventIdAndParentQuestionIdAndAnswerId(@Param("event") Event event, @Param("parentQueId") long parentQueId, @Param("ansId") Long ansId);


    @Query("SELECT attribute.id FROM TicketHolderRequiredAttributes as attribute" +
            " WHERE attribute.eventid.eventId=:eventId " +
            " AND attribute.enabledForTicketPurchaser is true " +
            " AND attribute.dataType = 'TICKET' "+
            " AND attribute.recurringEventId is null " +
            " AND attribute.isAttribute is true " +
            " AND attribute.isDeletedForBuyer is false")
    List<Long> findAllBuyerAttributesIdByEventId(@Param("eventId") Long eventId);

    @Query("SELECT attribute.id FROM TicketHolderRequiredAttributes as attribute" +
            " WHERE attribute.eventid.eventId=:eventId " +
            " AND attribute.enabledForTicketHolder is true " +
            " AND attribute.dataType = 'TICKET' "+
            " AND attribute.recurringEventId is null " +
            " AND attribute.isAttribute is true " +
            " AND attribute.isDeletedForHolder is false")
    List<Long> findAllHolderAttributesIdByEventId(@Param("eventId") Long eventId);

    @Query("SELECT attribute.id FROM TicketHolderRequiredAttributes as attribute" +
            " WHERE attribute.eventid.eventId=:eventId " +
            " AND attribute.enabledForTicketPurchaser is true " +
            " AND attribute.dataType = 'TICKET' "+
            " AND attribute.isAttribute is true " +
            " AND attribute.isDeletedForBuyer is false " +
            " AND attribute.recurringEventId = :recurringEventId")
    List<Long> findAllBuyerAttributesIdByEventIdAndRecurringEventId(@Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT attribute.id FROM TicketHolderRequiredAttributes as attribute" +
            " WHERE attribute.eventid.eventId=:eventId " +
            " AND attribute.enabledForTicketHolder is true " +
            " AND attribute.dataType = 'TICKET' "+
            " AND attribute.isAttribute is true " +
            " AND attribute.isDeletedForHolder is false " +
            " AND attribute.recurringEventId = :recurringEventId")
    List<Long> findAllHolderAttributesIdByEventIdAndRecurringEventId(@Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute" +
            " WHERE attribute.eventid.eventId=:eventId " +
            " AND attribute.enabledForTicketPurchaser = true " +
            " AND attribute.dataType = 'TICKET' "+
            " AND attribute.recurringEventId IS NOT NULL " +
            " AND attribute.isAttribute = true " +
            " AND attribute.isDeletedForBuyer = false " +
            " AND attribute.createdFrom IN :createdFrom")
    List<TicketHolderRequiredAttributes> findAllBuyerAttributesByCreatedFromAndEventId(@Param("createdFrom") List<Long> createdFrom, @Param("eventId")  Long eventId);

    @Query("SELECT attribute FROM TicketHolderRequiredAttributes as attribute" +
            " WHERE attribute.eventid.eventId=:eventId " +
            " AND attribute.enabledForTicketHolder = true " +
            " AND attribute.dataType = 'TICKET' "+
            " AND attribute.recurringEventId IS NOT NULL " +
            " AND attribute.isAttribute = true " +
            " AND attribute.isDeletedForHolder = false " +
            " AND attribute.createdFrom IN :createdFrom")
    List<TicketHolderRequiredAttributes> findAllHolderAttributesByCreatedFromAndEventId(@Param("createdFrom") List<Long> createdFrom, @Param("eventId")  Long eventId);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes " +
            "WHERE attributes.eventid = :event " +
            "AND attributes.recurringEventId IS NULL " +
            "AND (:dataType IS NULL OR attributes.dataType = :dataType) " +
            "AND attributes.attributeValueType NOT IN :attributeValueTypes " +
            "ORDER BY attributes.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> findAllByEventAndRecurringEventIdIsNullAndDataTypeExcludingAttributeValueTypesOrderByBuyerAttributeOrder(
            @Param("event") Event event,
            @Param("dataType") DataType dataType,
            @Param("attributeValueTypes") List<AttributeValueType> attributeValueTypes);


    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes " +
            "WHERE attributes.eventid = :event " +
            "AND attributes.recurringEventId = :recurringEventId " +
            "AND (:dataType IS NULL OR attributes.dataType = :dataType) " +
            "AND attributes.attributeValueType NOT IN :attributeValueTypes " +
            "ORDER BY attributes.buyerAttributeOrder")
    List<TicketHolderRequiredAttributes> findAllByEventAndRecurringEventIdAndDataTypeExcludingAttributeValueTypesOrderByBuyerAttributeOrder(
            @Param("event") Event event,
            @Param("recurringEventId") Long recurringEventId,
            @Param("dataType") DataType dataType,
            @Param("attributeValueTypes") List<AttributeValueType> attributeValueTypes);

    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes " +
            "WHERE attributes.eventid = :event " +
            "AND attributes.recurringEventId = :recurringEventId " +
            "AND (:dataType IS NULL OR attributes.dataType = :dataType) " +
            "AND attributes.attributeValueType NOT IN :attributeValueTypes " +
            "ORDER BY attributes.holderAttributeOrder")
    List<TicketHolderRequiredAttributes> findAllByEventAndRecurringEventIdAndDataTypeExcludingAttributeValueTypesOrderByHolderAttributeOrder(
            @Param("event") Event event,
            @Param("recurringEventId") Long recurringEventId,
            @Param("dataType") DataType dataType,
            @Param("attributeValueTypes") List<AttributeValueType> attributeValueTypes);


    @Query("SELECT attributes FROM TicketHolderRequiredAttributes attributes " +
            "WHERE attributes.eventid = :event " +
            "AND attributes.recurringEventId IS NULL " +
            "AND (:dataType IS NULL OR attributes.dataType = :dataType) " +
            "AND attributes.attributeValueType NOT IN :attributeValueTypes " +
            "ORDER BY attributes.holderAttributeOrder")
    List<TicketHolderRequiredAttributes> findAllByEventAndRecurringEventIdIsNullAndDataTypeExcludingAttributeValueTypesOrderByHolderAttributeOrder(
            @Param("event") Event event,
            @Param("dataType") DataType dataType,
            @Param("attributeValueTypes") List<AttributeValueType> attributeValueTypes);

    @Query(value = "SELECT * FROM ticket_holder_required_attributes attributes " +
            " WHERE attributes.event_id = :eventId " +
            " AND attributes.enabled_for_ticket_purchaser = true " +
            " AND attributes.is_deleted_for_buyer = false " +
            " AND FIND_IN_SET(:ticketTypeId, attributes.buyer_required_ticket_type_id) > 0 " +
            " AND (attributes.parent_question_id IS NULL OR attributes.parent_question_id = 0) " +
            " AND JSON_VALUE(attributes.default_value_json_purchaser, '$.defaultValueForPurchaser') IS NOT NULL " +
            " AND JSON_VALUE(attributes.default_value_json_purchaser, '$.defaultValueForPurchaser') <> '' ",
            nativeQuery = true)
    List<TicketHolderRequiredAttributes> findAllWithDefaultValueNotNullForPurchaser(@Param("eventId") Long eventId, @Param("ticketTypeId") Long ticketTypeId);

    @Query(value = "SELECT * FROM ticket_holder_required_attributes attributes " +
            " WHERE attributes.event_id = :eventId " +
            " AND attributes.enabled_for_ticket_holder = true " +
            " AND attributes.is_deleted_for_holder = false " +
            " AND FIND_IN_SET(:ticketTypeId, attributes.holder_required_ticket_type_id) > 0 " +
            " AND (attributes.parent_question_id IS NULL OR attributes.parent_question_id = 0) " +
            " AND JSON_VALUE(attributes.default_value_json_holder, '$.defaultValueForHolder') IS NOT NULL " +
            " AND JSON_VALUE(attributes.default_value_json_holder, '$.defaultValueForHolder') <> '' ",
            nativeQuery = true)
    List<TicketHolderRequiredAttributes> findAllWithDefaultValueNotNullForHolder(@Param("eventId") Long eventId, @Param("ticketTypeId") Long ticketTypeId);

    @Query(value = "SELECT * FROM ticket_holder_required_attributes attributes " +
            " WHERE attributes.event_id = :eventId " +
            " AND attributes.enabled_for_ticket_purchaser = true " +
            " AND FIND_IN_SET(:addonTicketTypeId, attributes.buyer_event_ticket_type_id) > 0 " +
            " AND (attributes.parent_question_id IS NULL OR attributes.parent_question_id = 0) " +
            " AND JSON_VALUE(attributes.default_value_json_purchaser, '$.defaultValueForPurchaser') IS NOT NULL " +
            " AND JSON_VALUE(attributes.default_value_json_purchaser, '$.defaultValueForPurchaser') <> '' ",
            nativeQuery = true)
    List<TicketHolderRequiredAttributes> findAllAddOnAttributeWithDefaultValueNotNullForPurchaser(@Param("eventId") Long eventId, @Param("addonTicketTypeId") Long addonTicketTypeId);

    @Query("SELECT attribute.id, attribute.name FROM TicketHolderRequiredAttributes attribute " +
            "WHERE attribute.eventid.eventId = :eventId " +
            "AND attribute.enabledForTicketPurchaser = true " +
            "AND attribute.dataType = 'TICKET' " +
            "AND attribute.isAttribute = true " +
            "AND attribute.isDeletedForBuyer = false " +
            "AND ( (:recurringEventId IS NULL AND attribute.recurringEventId IS NULL) " +
            "   OR (:recurringEventId IS NOT NULL AND attribute.recurringEventId = :recurringEventId) )")
    List<Object[]> findAllIdAndNameByEventAndEnableForBuyer(@Param("eventId") long eventId, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT attribute.id, attribute.name  FROM TicketHolderRequiredAttributes attribute " +
            "WHERE attribute.eventid.eventId = :eventId " +
            "AND attribute.enabledForTicketHolder = true " +
            "AND attribute.dataType = 'TICKET' " +
            "AND attribute.isAttribute = true " +
            "AND attribute.isDeletedForHolder = false " +
            "AND ( (:recurringEventId IS NULL AND attribute.recurringEventId IS NULL) " +
            "   OR (:recurringEventId IS NOT NULL AND attribute.recurringEventId = :recurringEventId) )")
    List<Object[]> findAllIdAndNameByEventAndEnableForHolder(@Param("eventId") long eventId,
                                                             @Param("recurringEventId") Long recurringEventId);



    @Query("SELECT attribute.name  FROM TicketHolderRequiredAttributes attribute " +
            "WHERE attribute.eventid.eventId = :eventId " +
            "AND attribute.dataType = 'TICKET' " +
            "AND attribute.isAttribute = true " +
            "AND ( attribute.enabledForTicketPurchaser = true OR attribute.enabledForTicketHolder = true ) " +
            "AND ( attribute.isDeletedForHolder = false OR attribute.isDeletedForBuyer = false ) " +
            "AND ( (:recurringEventId IS NULL AND attribute.recurringEventId IS NULL) " +
            "   OR (:recurringEventId IS NOT NULL AND attribute.recurringEventId = :recurringEventId) )")
    List<String> findAllRequiredAttributesName(@Param("eventId") Long eventId,
                                               @Param("recurringEventId") Long recurringEventId);


}
