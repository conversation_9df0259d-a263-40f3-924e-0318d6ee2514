package com.accelevents.repositories;

import com.accelevents.common.dto.TicketGraphDetail;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.TicketTypeSoldAndBookCountDto;
import com.accelevents.dto.stats.TicketStats;
import com.accelevents.messages.TicketType;
import com.accelevents.ticketing.dto.TicketingBuyerDataFromDB;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface EventTicketsCommonRepo extends CrudRepository<EventTickets, Long> {

    @Query(" SELECT count(et.id) FROM EventTickets et " +
            " WHERE et.ticketingTypeId=:ticketingType AND et.ticketStatus NOT IN (:ticketStatus)")
    Long findByTicketingTypeIdAndTicketStatusNotIn(@Param("ticketingType") TicketingType ticketingType,
                                                   @Param("ticketStatus") Collection<TicketStatus> ticketStatus);

    @Query(" SELECT new com.accelevents.dto.stats.TicketStats(COUNT(1), SUM(et.paidAmount - et.refundedAmount)) " +
            " FROM EventTickets AS et" +
            " WHERE et.ticketingTypeOnlyId = :ticketingTypeOnlyId AND  et.ticketStatus NOT IN (:ticketStatus) ")
    TicketStats countByTicketingTypeIdAndTicketStatusNotInAndStatusNot(@Param("ticketingTypeOnlyId") Long ticketingTypeOnlyId,
                                                                       @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets et " +
            " JOIN FETCH et.ticketingTypeId as ticketTypeId" +
            " JOIN FETCH et.ticketPurchaserId as purchaser" +
            " LEFT JOIN FETCH et.ticketingTable as ticketingTable" +
            " LEFT JOIN FETCH et.recurringEvents as recurringEvents" +
            " WHERE et.ticketingOrder=:ticketingOrder AND et.ticketStatus NOT IN (:ticketStatus)")
    List<EventTickets> findByTicketingOrderAndTicketStatusNotIn(@Param("ticketingOrder") TicketingOrder ticketingOrder,
                                                                @Param("ticketStatus") Collection<TicketStatus> ticketStatus);

    @Query("SELECT CASE WHEN COUNT(et) > 0 THEN true ELSE false end FROM EventTickets et " +
            " WHERE et.ticketingTypeOnlyId=:ticketingTypeOnlyId" +
            " AND et.ticketStatus <> 'DELETED'")
    boolean isTicketTypePurchased(@Param("ticketingTypeOnlyId") Long ticketingTypeOnlyId);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN FETCH et.ticketingOrder ticketingOrder" +
            " JOIN FETCH et.holderUserId holderUser" +
            " JOIN FETCH ticketingOrder.purchaser purchaser" +
            " JOIN FETCH et.ticketingTypeId ticketingTypeId" +
            " JOIN FETCH ticketingTypeId.ticketing ticketing" +
            " LEFT JOIN FETCH et.recurringEvents recurringEvents" +
            " LEFT JOIN FETCH et.ticketingTable ticketingTable" +
            " JOIN FETCH et.ticketHolderAttributesId ticketHolderAttributesId" +
            " WHERE ticketingOrder=:ticketingOrder",
            countQuery = "SELECT COUNT(et.id) FROM EventTickets et WHERE et.ticketingOrder=:ticketingOrder")
    Page<EventTickets> findByTicketingOrder(@Param("ticketingOrder") TicketingOrder ticketingOrder,
                                            Pageable pageable);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN FETCH et.ticketingOrder ticketingOrder" +
            " JOIN FETCH et.holderUserId holderUser" +
            " JOIN FETCH ticketingOrder.purchaser purchaser" +
            " JOIN FETCH et.ticketingTypeId ticketingTypeId" +
            " JOIN FETCH ticketingTypeId.ticketing ticketing" +
            " LEFT JOIN FETCH et.recurringEvents recurringEvents" +
            " LEFT JOIN FETCH et.ticketingTable ticketingTable" +
            " JOIN FETCH et.ticketHolderAttributesId ticketHolderAttributesId" +
            " WHERE et.paidAmount > 0 AND et.refundedAEFee = 0 AND ticketingOrder IN :ticketingOrder AND ticketingTypeId.passfeetobuyer IS TRUE",
            countQuery = "SELECT COUNT(et.id) FROM EventTickets et WHERE et.paidAmount > 0 AND et.refundedAEFee = 0 AND et.ticketingOrder IN :ticketingOrder AND ticketingTypeId.passfeetobuyer IS TRUE")
    Page<EventTickets> findByTicketingOrderWithPaidAmountAndNotRefundAmount(@Param("ticketingOrder") List<TicketingOrder> ticketingOrder,
                                                                            Pageable pageable);


    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingOrder as ticketingOrder" +
            " JOIN FETCH et.recurringEvents as recurringEvents" +
            " JOIN FETCH et.ticketingTypeId as ticketTypeId " +
            " JOIN FETCH ticketTypeId.ticketing as ticketing" +
            " LEFT JOIN FETCH et.ticketingTable as ticketingTable" +
            " WHERE et.ticketingOrder.id=:ticketingOrderId ")
    List<EventTickets> findAllByTicketingOrder(@Param("ticketingOrderId") long ticketingOrderId);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingOrder as ticketingOrder" +
            " JOIN FETCH et.ticketingTypeId as ticketTypeId " +
            " JOIN FETCH ticketTypeId.ticketing as ticketing" +
            " LEFT JOIN FETCH et.ticketingTable as ticketingTable" +
            " WHERE et.ticketingOrder.id=:ticketingOrderId ")
    List<EventTickets> findAllByTicketingOrderWithoutRecurring(@Param("ticketingOrderId") long ticketingOrderId);

    List<EventTickets> findByTicketingOrderAndHolderEmail(TicketingOrder ticketingOrder, String holderEmail);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId as ticketingType " +
            " JOIN FETCH ticketingType.ticketing as ticketing " +
            " WHERE et.ticketingOrder=:ticketingOrder " +
            " AND  et.ticketStatus <> 'DELETED'  AND  et.holderEmail=:holderEmail")
    List<EventTickets> findByTicketingOrderAndHolderEmailAndNotDeleted(@Param("ticketingOrder") TicketingOrder ticketingOrder,
                                                                       @Param("holderEmail") String holderEmail);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketPurchaserId as purchaser" +
            " JOIN FETCH et.ticketingTypeId as ticketTypeId " +
            " JOIN FETCH ticketTypeId.ticketing as ticketing" +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable" +
            " WHERE et.ticketingOrder.id=:ticketingOrderId " +
            " AND  et.ticketStatus <> 'DELETED'  ")
    List<EventTickets> findByTicketingOrder(@Param("ticketingOrderId") long ticketingOrderId);

    List<EventTickets> findAll();

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketPurchaserId as purchaser" +
            " JOIN FETCH et.ticketingTypeId as ticketTypeId " +
            " JOIN FETCH ticketTypeId.ticketing as ticketing" +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " WHERE et.id=:eventTicketId AND et.ticketingOrder=:ticketingOrder AND et.ticketStatus NOT IN (:ticketStatus)")
    EventTickets findByIdAndTicketingOrderAndNotInTicketStatus(@Param("eventTicketId") long eventTicketId,
                                                               @Param("ticketingOrder") TicketingOrder ticketingOrder, @Param("ticketStatus") Collection<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " LEFT JOIN FETCH ticketOrder.ticketingCoupon AS coupon " +
            " LEFT JOIN FETCH ticketOrder.trackingLinks " +
            " WHERE et.event=:event " +
            " AND (ticketOrder.status IN(:status) ) " +
            " AND (et.ticketStatus <> 'DELETED' ) " +
            " AND (:dataType is null OR et.dataType =:dataType ) " +
            " AND ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (et.recordStatus <> 'CANCEL' OR et.recordStatus is null)" +
            " AND ((:startTime IS NULL OR ticketOrder.orderDate >= :startTime) AND (:endTime IS NULL OR ticketOrder.orderDate <= :endTime))" +
            " ORDER BY ticketOrder.id,  ticketingType.id ")
    List<EventTickets> findByEventIdAndTicketingStatus(@Param("event") Event event,
                                                                @Param("status") List<TicketingOrder.TicketingOrderStatus> status,
                                                                @Param("recurringEventId") Long recurringEventId,
                                                                @Param("dataType") DataType dataType,
                                                                @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " LEFT JOIN FETCH ticketOrder.ticketingCoupon AS coupon " +
            " LEFT JOIN FETCH ticketOrder.trackingLinks " +
            " WHERE et.event=:event " +
            " AND (et.ticketStatus = 'DELETED' ) " +
            " AND ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " ORDER BY ticketOrder.id,  ticketingType.id ")
    List<EventTickets> findDeletedEventTicketByEventIdAndTicketingStatusJoinFetch(@Param("event") Event event,
                                                                @Param("recurringEventId") Long recurringEventId);


    @Query(value = "SELECT et FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " JOIN FETCH et.ticketingOrder AS ticketOrder" +
            " WHERE et.event = :event " +
            " AND (et.ticketStatus IN (:ticketStatus) AND ticketingType.ticketType IN (:ticketTypes)) " +
            " AND (et.ticketPaymentStatus IN (:eventTicketStatus)) " +
            " AND (et.recurringEventId = :recurringEventId OR 0 = :recurringEventId) " +
            " AND (ticketingType.id IN (:ticketingTypeIds)) " +
            " AND (:dataType IS NULL OR et.dataType = :dataType) " +
            " ORDER BY  et.holderFirstName ASC, et.holderLastName ASC ",
            countQuery = "SELECT COUNT(et) FROM EventTickets AS et " +
                    " JOIN et.ticketingTypeId AS ticketingType " +
                    " WHERE et.event = :event " +
                    " AND (et.ticketStatus IN (:ticketStatus) AND ticketingType.ticketType IN (:ticketTypes)) " +
                    " AND (et.ticketPaymentStatus IN (:eventTicketStatus)) " +
                    " AND (et.recurringEventId = :recurringEventId OR 0 = :recurringEventId) " +
                    " AND (ticketingType.id IN (:ticketingTypeIds)) " +
                    " AND (:dataType IS NULL OR et.dataType = :dataType)")
    Page<EventTickets> findByEventidAndTicketTypeAndTicketStatus(
            @Param("event") Event event,
            @Param("ticketTypes") List<TicketType> ticketTypes,
            @Param("eventTicketStatus") List<TicketPaymentStatus> eventTicketStatus,
            @Param("ticketStatus") List<TicketStatus> ticketStatus,
            @Param("recurringEventId") Long recurringEventId,
            @Param("ticketingTypeIds") List<Long> ticketTypeIds,
            @Param("dataType") DataType dataType,
            Pageable pageable);


    @Query(value = "SELECT COUNT(et.id)" +
            " FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ett" +
            " WHERE et.eventId = :eventId" +
            " AND et.ticketStatus = 'CHECKED_IN'" +
            " AND (:ticketTypeId IS NULL OR ett.id = :ticketTypeId)"+
            " AND (:startDate IS NULL OR :endDate IS NULL OR et.checkInDate BETWEEN :startDate AND :endDate)"
    )
    Long findallCheckedInAttendee(@Param("eventId")Long eventId, @Param("ticketTypeId") Long ticketTypeId,
                                   @Param("startDate") Date startDate, @Param("endDate")  Date endDate);


    @Query(value = "SELECT et FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.holderUserId AS user " +
            " JOIN FETCH et.ticketingOrder AS ticketingOrder" +
            " WHERE et.event=:event" +
            " AND (et.ticketStatus IN (:ticketStatuses) " +
            " AND ticketingType.ticketType IN (:ticketTypes)) " +
            " AND (et.ticketPaymentStatus IN (:eventTicketStatus)) " +
            " AND (et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (ticketingType.id IN (:ticketingTypeIds)) " +
            " AND (cast(lower(CASE WHEN et.guestOfBuyer = false THEN CONCAT(et.holderFirstName, ' ', et.holderLastName) ELSE CONCAT(user.firstName, ' ', user.lastName) END) as string) LIKE %:searchStr% " +
            " OR cast(lower(user.email) as string) LIKE %:searchStr% " +
            " OR cast(user.phoneNumber as string) LIKE %:searchStr% "+
            " OR et.barcodeId =:searchStr) " +
            " ORDER BY  et.holderFirstName ASC, et.holderLastName ASC ",
    countQuery = " SELECT COUNT(et) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.holderUserId AS user " +
            " WHERE et.event=:event" +
            " AND (et.ticketStatus IN (:ticketStatuses) " +
            " AND ticketingType.ticketType IN (:ticketTypes)) " +
            " AND (et.ticketPaymentStatus IN (:eventTicketStatus)) " +
            " AND (et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (ticketingType.id IN (:ticketingTypeIds)) " +
            " AND (cast(lower(CASE WHEN et.guestOfBuyer = false THEN CONCAT(et.holderFirstName, ' ', et.holderLastName) ELSE CONCAT(user.firstName, ' ', user.lastName) END) as string) LIKE %:searchStr% " +
            " OR cast(lower(user.email) as string) LIKE %:searchStr% " +
            " OR cast(user.phoneNumber as string) LIKE %:searchStr% "+
            " OR et.barcodeId =:searchStr) ")
    Page<EventTickets> findByEventidAndTicketTypeAndTicketStatusAndSearchStr(@Param("event") Event event,
                                                                             @Param("ticketTypes") List<TicketType> ticketTypes,
                                                                             @Param("eventTicketStatus") List<TicketPaymentStatus> eventTicketStatus,
                                                                             @Param("ticketStatuses") List<TicketStatus> ticketStatus,
                                                                             @Param("searchStr") String searchStr,
                                                                             @Param("recurringEventId") Long recurringEventId,
                                                                             @Param("ticketingTypeIds") List<Long> ticketTypeIds,
                                                                             Pageable pageable);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " LEFT JOIN FETCH ticketOrder.staffUserId AS staffUserId " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " WHERE et.event=:event " +
            " AND (ticketOrder.exitIntentPopupTriggered=:exitIntentPopupTriggered OR false =:exitIntentPopupTriggered) " +
            " AND ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (:dataType is null OR et.dataType =:dataType ) " +
            " AND ((:startTime IS NULL OR ticketOrder.orderDate >= :startTime) AND (:endTime IS NULL OR ticketOrder.orderDate <= :endTime))" +
            " ORDER BY ticketOrder.id, ticketingType.id")
    List<EventTickets> findByEventidJoinFetch(@Param("event") Event event,
                                              @Param("recurringEventId") Long recurringEventId,
                                              @Param("exitIntentPopupTriggered") boolean exitIntentPopupTriggered,
                                              @Param("dataType") DataType dataType,
                                              @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " JOIN FETCH et.holderUserId AS holderUser " +
            " LEFT JOIN FETCH ticketOrder.staffUserId AS staffUserId " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " WHERE et.event=:event AND ticketOrder.status!='UNPAID_DELETE' AND ticketOrder.status!='PAID_DELETE'" +
            " AND (ticketOrder.exitIntentPopupTriggered=:exitIntentPopupTriggered OR false =:exitIntentPopupTriggered) " +
            " AND ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (:dataType is null OR et.dataType =:dataType ) " +
            " AND ((:startTime IS NULL OR ticketOrder.orderDate >= :startTime) AND (:endTime IS NULL OR ticketOrder.orderDate <= :endTime))" +
            " ORDER BY ticketOrder.id, ticketingType.id")
    List<EventTickets> findByEventIdWithTicketingOrderStatusNotDeleted(@Param("event") Event event,
                                              @Param("recurringEventId") Long recurringEventId,
                                              @Param("exitIntentPopupTriggered") boolean exitIntentPopupTriggered,
                                              @Param("dataType") DataType dataType,
                                              @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " LEFT JOIN FETCH ticketOrder.staffUserId AS staffUserId " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " WHERE et.event in (:event) " +
            " AND (ticketOrder.orderType not in (:orderTypes)) " +
            " AND ((:startTime IS NULL OR ticketOrder.orderDate >= :startTime) AND (:endTime IS NULL OR ticketOrder.orderDate <= :endTime))" +
            " ORDER BY ticketOrder.id, ticketingType.id")
    List<EventTickets> findByEventidsJoinFetch(@Param("event") List<Event> events,
                                               @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("orderTypes") List<TicketingOrder.OrderType> orderTypes);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " JOIN UserSession userSession on userSession.user = et.holderUserId " +
            " WHERE et.event=:event " +
            " AND (ticketOrder.exitIntentPopupTriggered=:exitIntentPopupTriggered OR false =:exitIntentPopupTriggered) " +
            " AND ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (:dataType is null OR et.dataType =:dataType ) " +
            " AND ticketingType.ticketType <> 'DONATION' " +
            " AND userSession.sessionId=:sessionId " +
            " AND et.ticketStatus <> 'DELETED' ")
    List<EventTickets> findByEventAndSessionIdJoinFetch(@Param("event") Event event,
                                                        @Param("sessionId") Long sessionId,
                                                        @Param("recurringEventId") Long recurringEventId,
                                                        @Param("exitIntentPopupTriggered") boolean exitIntentPopupTriggered,
                                                        @Param("dataType") DataType dataType);


    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " LEFT JOIN FETCH et.holderUserId AS holderUser " +
            " JOIN FETCH ticketingType.ticketing AS ticketing " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " JOIN FETCH ticketOrder.purchaser AS purchaser " +
            " JOIN et.event as event " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable" +
            " WHERE et.barcodeId=:barcodeId AND et.ticketStatus <> 'DELETED' and  (event.eventStatus is null or  event.eventStatus <> 'EVENT_DELETED') ")
    EventTickets findByBarcodeId(@Param("barcodeId") String barcodeId);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " LEFT JOIN FETCH et.holderUserId AS holderUser " +
            " JOIN FETCH ticketingType.ticketing AS ticketing " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " JOIN FETCH ticketOrder.purchaser AS purchaser " +
            " JOIN et.event as event " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable" +
            " WHERE et.barcodeId IN (:barcodeIds) AND et.eventId =:eventId AND (event.eventStatus is null or  event.eventStatus <> 'EVENT_DELETED') ")
    List<EventTickets> findEventTicketByBarcodeIdsAndEventId(@Param("barcodeIds") List<String> barcodeIds, @Param("eventId") Long eventId);

    @Query("SELECT et FROM EventTickets AS et " +
            " WHERE et.barcodeId in (:barcodeId) AND ( et.event.eventStatus is null or  et.event.eventStatus <> 'EVENT_DELETED') ")
    List<EventTickets> findByBarcodeIds(@Param("barcodeId") List<String> barcodeId);

    // If we remove JOIN FETCH ticket order PDF will start failing
    @Query("SELECT et FROM EventTickets et" +
            " JOIN FETCH et.ticketingTypeId ticketingTypeId" +
            " JOIN FETCH et.ticketingOrder ticketingOrder" +
            " JOIN FETCH ticketingOrder.eventid eventid" +
            " LEFT JOIN FETCH et.ticketingTable ticketingTable" +
            " JOIN FETCH et.ticketHolderAttributesId ticketHolderAttributesId" +
            " WHERE et.id=:id")
    EventTickets findById(@Param("id") long id);

    @Modifying
    @Query("UPDATE EventTickets SET ticketPurchaserId = :newUser WHERE ticketPurchaserId = :user")
    void updatePurchaserByNewUser(@Param("user") User user, @Param("newUser") User newUser);

    @Modifying
    @Query("UPDATE EventTickets SET checkInStaff = :newUser WHERE checkInStaff = :user")
    void updateStaffByNewUser(@Param("user") User user, @Param("newUser") User newUser);

    @Query(value = "SELECT new com.accelevents.ticketing.dto.TicketingBuyerDataFromDB("
            + "ticketingOrder.id, "
            + "purchaser.firstName, "
            + "purchaser.lastName, "
            + "ticketingOrder.orderDate, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.refundedAmount)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.paidAmount)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.aeFeeAmount)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.ticketPrice)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE (COUNT(CASE WHEN ticketingType.bundleType ='INDIVIDUAL_TICKET' THEN 1 ELSE null END)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE (SUM(case when ticketingType.bundleType <>'INDIVIDUAL_TICKET' THEN 1 ELSE 0 END)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE COUNT(et.id) END, "
            + "SUM(CASE WHEN (et.ticketStatus = :ticketStatus ) THEN 1 ELSE 0 END), "
            + "SUM(CASE WHEN (et.ticketPaymentStatus = 'REFUNDED') THEN 1 ELSE 0 END), "
            + "ticketingOrder.status, "
            + "SUM(et.wlAFeeAmount + et.wlBFeeAmount), "
            + "SUM(et.refundedWlAFee + et.refundedWlBFee), "
            + "SUM(et.refundedAEFee), "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.salesTaxFee)) END, "
            + "SUM(et.refundedSalesTaxFee), "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.vatTaxFee)) END, "
            + "SUM(et.refundedVatTaxFee), "
            + "SUM(CASE WHEN (et.chargeId IS NOT NULL) THEN 1 ELSE 0 END) ) "
            + "FROM EventTickets AS et  "
            + "JOIN et.ticketingOrder AS ticketingOrder "
            + "JOIN ticketingOrder.purchaser AS purchaser "
            + "JOIN et.ticketingTypeId AS ticketingType "
            + "JOIN ticketingType.ticketing AS ticketing "
            + " WHERE " +
            "( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND et.eventId=:eventId " +
            " AND et.ticketStatus <> 'DELETED' " +
            "AND ticketingType.ticketType IN (:ticketType) "
            + " AND (:dataType is null OR et.dataType =:dataType) "
            + " AND ( ticketingOrder.exitIntentPopupTriggered=:exitIntentPopupTriggered OR false=:exitIntentPopupTriggered) "
            + " AND ( false =:onlyCardOrderType OR ticketingOrder.orderType='CARD')"
            + "GROUP BY et.ticketingOrder "
            + " ORDER BY ticketingOrder.id desc")
    Page<TicketingBuyerDataFromDB> getTicketingBuyerData(@Param("eventId") Long eventId,//NOSONAR
                                                         Pageable pageable,
                                                         @Param("ticketType") List<TicketType> ticketType,
                                                         @Param("recurringEventId") Long recurringEventId,
                                                         @Param("exitIntentPopupTriggered") boolean exitIntentPopupTriggered,
                                                         @Param("ticketStatus") TicketStatus ticketStatus,
                                                         @Param("onlyCardOrderType") boolean onlyCardOrderType,
                                                         @Param("dataType") DataType dataType);

    @Query(value = "SELECT new com.accelevents.ticketing.dto.TicketingBuyerDataFromDB("
            + "ticketingOrder.id, "
            + "purchaser.firstName, "
            + "purchaser.lastName, "
            + "ticketingOrder.orderDate, "
            + "SUM(et.refundedAmount), "
            + "SUM(et.paidAmount), "
            + "SUM(et.aeFeeAmount), "
            + "SUM(et.ticketPrice), "
            + "COUNT(CASE WHEN ticketingType.bundleType ='INDIVIDUAL_TICKET' THEN 1 ELSE null END), "
            + "SUM(case when ticketingType.bundleType <>'INDIVIDUAL_TICKET' THEN (1/ticketingType.numberOfTicketPerTable) ELSE 0 END), "
            + "COUNT(et.id), "
            + "SUM(CASE WHEN (et.ticketStatus = 'CANCELED') THEN 1 ELSE 0 END), "
            + "SUM(CASE WHEN (et.ticketPaymentStatus = 'REFUNDED') THEN 1 ELSE 0 END), "
            + "ticketingOrder.status, "
            + "SUM(et.wlAFeeAmount + et.wlBFeeAmount), "
            + "SUM(et.refundedWlAFee + et.refundedWlBFee), "
            + "SUM(et.refundedAEFee), "
            + "SUM(et.salesTaxFee), "
            + "SUM(et.refundedSalesTaxFee), "
            + "SUM(et.vatTaxFee), "
            + "SUM(et.refundedVatTaxFee), "
            + "SUM(CASE WHEN (et.chargeId IS NOT NULL) THEN 1 ELSE 0 END) ) "
            + "FROM EventTickets AS et  "
            + "JOIN et.ticketingOrder AS ticketingOrder "
            + "JOIN ticketingOrder.purchaser AS purchaser "
            + "JOIN et.ticketingTypeId AS ticketingType "
            + "WHERE "
            + "( et.recurringEventId = :recurringEventId "
            + "OR 0 = :recurringEventId "
            + ") "
            + "AND et.eventId=:eventId AND"
            + "(et.ticketStatus <>:ticketStatus) AND "
            + "(:dataType is null OR et.dataType =:dataType) AND "
            + "( CONCAT(purchaser.firstName,' ',purchaser.lastName) like %:searchStr% or "
            + "et.ticketPrice like %:searchStr% or et.refundedAmount like %:searchStr% or et.paidAmount like %:searchStr% or ticketingOrder.id like %:searchStr%) "
            + "GROUP BY et.ticketingOrder order by ticketingOrder.orderDate desc")
    Page<TicketingBuyerDataFromDB> getTicketingBuyerDataSearch(@Param("eventId") Long eventId,
                                                               Pageable pageable,
                                                               @Param("searchStr") String searchStr,
                                                               @Param("recurringEventId") Long recurringEventId,
                                                               @Param("ticketStatus") TicketStatus ticketStatus,
                                                               @Param("dataType") DataType dataType);

    @Query(value = "SELECT " +
            "tord.id, " +
            "tord.order_date, " +
            "SUM(et.refunded_amount), " +
            "SUM(et.paid_amount), " +
            "COUNT(et.id), " +
            "SUM(CASE WHEN et.ticket_status = 'CANCELED' THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN et.ticket_payment_status = 'REFUNDED'  THEN 1 ELSE 0 END), " +
            "tord.order_status " +
            "FROM event_tickets et " +
            "INNER JOIN ticketing_order tord ON et.ticket_order_id=tord.id " +
            "INNER JOIN ticket_holder_attributes att on att.id=et.ticket_holder_attributes_id " +
            "WHERE ( et.rec_status<>'CANCEL' or et.rec_status is NULL ) " +
            "AND (et.recurring_event_id=:recurringEventId or 0=:recurringEventId) " +
            "AND et.event_id=:eventId " +
            "AND et.ticket_status<>'DELETED' " +
            "AND (:dataType IS NULL OR et.data_type =:dataType ) " +
            "AND ( " +
            "CAST(CONCAT(att.json_value ->> '$.purchaser.attributes.\"First Name\"', ' ', att.json_value ->> '$.purchaser.attributes.\"Last Name\"' ) AS CHAR) LIKE %:searchStr% " +
            "OR et.refunded_amount LIKE %:searchStr% " +
            "OR et.paid_amount LIKE %:searchStr% " +
            "OR tord.id LIKE %:searchStr% ) " +
            "GROUP BY et.ticket_order_id ORDER BY tord.id DESC",
            countQuery = "SELECT COUNT(1) " +
                    "FROM event_tickets et " +
                    "INNER JOIN ticketing_order tord ON et.ticket_order_id=tord.id " +
                    "INNER JOIN ticket_holder_attributes att on att.id=et.ticket_holder_attributes_id " +
                    "WHERE ( et.rec_status<>'CANCEL' or et.rec_status is NULL ) " +
                    "AND (et.recurring_event_id=:recurringEventId or 0=:recurringEventId) " +
                    "AND et.event_id=:eventId " +
                    "AND et.ticket_status<> 'DELETED' " +
                    "AND (:dataType IS NULL OR et.data_type =:dataType ) " +
                    "AND ( " +
                    "CAST(CONCAT(att.json_value ->> '$.purchaser.attributes.\"First Name\"', ' ', att.json_value ->> '$.purchaser.attributes.\"Last Name\"' ) AS CHAR) LIKE %:searchStr% " +
                    "OR et.refunded_amount LIKE %:searchStr% " +
                    "OR et.paid_amount LIKE %:searchStr% " +
                    "OR tord.id LIKE %:searchStr% ) " +
                    "GROUP BY et.ticket_order_id",
            nativeQuery = true)
    Page<Object[]> getTicketBuyerDataSearch(@Param("eventId") Long eventId,
                                            @Param("searchStr") String searchStr,
                                            @Param("recurringEventId") Long recurringEventId,
                                            @Param("dataType") DataType dataType,
                                            Pageable pageable);


    @Query("select et from EventTickets AS et " +
            " JOIN FETCH et.ticketingOrder as ticketingOrder " +
            " JOIN FETCH et.ticketingTypeId AS ticketingTypeId" +
            " WHERE ticketingOrder.id=:orderId AND et.ticketStatus <>:status ")
    List<EventTickets> findByOrderIdAndStatusNotIn(@Param("orderId") Long orderId, @Param("status") TicketStatus status);

    @Query(" SELECT new com.accelevents.common.dto.TicketGraphDetail( " +
            "  cast(et.ticketingOrder.orderDate as date), " +
            "  count( DISTINCT et), " +
            "  sum(( et.paidAmount - et.refundedAmount ) - ( et.aeFeeAmount + et.refundedAEFee + et.salesTaxFee + et.refundedSalesTaxFee + et.wlAFeeAmount + et.wlBFeeAmount + et.refundedWlAFee + et.refundedWlBFee )) " +
            " ) " +
            " FROM EventTickets AS et " +
            " WHERE et.ticketingTypeId in (:ticketingTypes) " +
            " AND et.ticketStatus <>:ticketStatus " +
            " GROUP BY cast(et.ticketingOrder.orderDate as date) ")
    List<TicketGraphDetail> getEventTicketGraphDetail(@Param("ticketingTypes") List<TicketingType> ticketingTypes, @Param("ticketStatus") TicketStatus ticketStatus);

    @Query(" SELECT new com.accelevents.common.dto.TicketGraphDetail( " +
            "  cast(et.ticketingOrder.orderDate as date), " +
            "  count( DISTINCT et), " +
            "  sum(et.paidAmount-et.refundedAmount)" +
            " ) " +
            " FROM EventTickets et " +
            " WHERE et.ticketingTypeId in (:ticketingTypes) " +
            " AND et.ticketStatus <>:ticketStatus " +
            " AND et.ticketingOrder.orderDate BETWEEN :fromDate AND :toDate " +
            " GROUP BY cast(et.ticketingOrder.orderDate as date) ")
    List<TicketGraphDetail> getEventTicketGraphDetailBetweenDates(@Param("ticketingTypes") List<TicketingType> ticketingTypes, @Param("ticketStatus") TicketStatus ticketStatus, @Param("fromDate") Date fromDate, @Param("toDate") Date toDate);

    @Query(" SELECT et from EventTickets AS et " +
            " WHERE et.ticketingTypeOnlyId=:ticketingTypeOnlyId " +
            " AND et.ticketStatus <> 'DELETED' ")
    List<EventTickets> findByTicketingTypeAndStatusNotDeleted(@Param("ticketingType") Long ticketingTypeOnlyId);

    @Query(" SELECT COUNT(et.id) FROM EventTickets et " +
            " JOIN et.ticketingOrder AS tOrder" +
            " JOIN et.ticketingTypeId AS tType" +
            " WHERE et.eventId=:eventId" +
            " AND et.ticketStatus  not in ('CANCELED','DELETED') " +
            " AND et.dataType =:dataType " +
            " AND tOrder.isSpeakerOrder=false " +
            " AND tOrder.isStaffOrder=false" +
            " AND tType.ticketType <> 'DONATION'")
    BigDecimal getTicketSoldCountByEvent(@Param("eventId") Long eventId,  @Param("dataType") DataType dataType);

    @Query(" SELECT COUNT(et.id) FROM EventTickets et " +
            " JOIN et.ticketingOrder AS tOrder " +
            " JOIN et.ticketingTypeId AS tType " +
            " WHERE et.eventId=:eventId " +
            " AND et.ticketStatus  not in ('CANCELED','DELETED') " +
            " AND et.dataType =:dataType " +
            " AND tOrder.isSpeakerOrder=false " +
            " AND tOrder.isStaffOrder=false " +
            " AND et.recurringEventId=:recurringEventId " +
            " AND tType.ticketType <> 'DONATION' ")
    BigInteger getTicketSoldCountByEvent(@Param("eventId") Long eventId,  @Param("dataType") DataType dataType,
                                         @Param("recurringEventId") Long recurringEventId);

    @Query(" SELECT COUNT(ett.id) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ett " +
            " WHERE ett.id=:ticketingTypeId " +
            " AND  et.ticketStatus  not in ('CANCELED','DELETED') AND ett.dataType ='TICKET'")
    BigDecimal getTicketSoldCountByTicketTypeId(@Param("ticketingTypeId") Long ticketingTypeId);

    @Query(value =
            " SELECT " +
                    " SUM(ticketCount) as sold_per_ticket_type, " +
                    " z.ticketing_type_id, " +
                    " z.ticket_type, " +
                    " z.ticket_bundle_type, " +
                    " ifnull(SUM(z.paid_amount - z.refund_amount) - SUM(total_fee + if(:isStripe, ccFee ,ccFee - refundedCCFee)),0) as netsale, " +
                    " z.number_of_tickets * if(z.ticket_bundle_type !='INDIVIDUAL_TICKET', z.number_of_tickets_per_table,1), " +
                    " z.data_type as data_type" +
                    " FROM ( " +
                    " SELECT " +
                    " x.*, " +
                    " IF( x.ae_fee_amount > 0 ,(((x.paid_amount * :percentage) / 100) + (:fixed/ticketTypeCountInOrder) ),0) as ccFee, " +
                    " IF( x.ae_fee_amount > 0 ,((x.refund_amount * (((x.paid_amount * :percentage) / 100) + (:fixed/ticketTypeCountInOrder))) / x.paid_amount),0) as refundedCCFee " +
                    " FROM " +
                    "( SELECT " +
                    " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, IF((ett.data_type not in (:dataType)), 0, count(et.id))) as ticketCount," +
                    " et.ticketing_type_id, " +
                    " ett.ticket_type, " +
                    " ett.ticket_bundle_type, " +
                    " et.ticket_order_id, " +
                    " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, et.ae_fee_amount) as ae_fee_amount, " +
                    " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, SUM(et.paid_amount)) as paid_amount, " +
                    " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, SUM(et.refunded_amount)) as refund_amount, " +
                    " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, SUM(ae_fee_amount + wl_a_fee_amount + wl_b_fee_amount + sales_tax_fee_amount + vat_tax_fee_amount) - SUM(refunded_AE_fee + refunded_wl_a_fee + refunded_wl_b_fee + refunded_sales_tax_fee + refunded_vat_tax_fee)) as total_fee, " +
                    " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, IF((ett.data_type not in (:dataType)), 0, ett.number_of_tickets)) as number_of_tickets, " +
                    " ett.data_type as data_type, " +
                    " ett.NUMBER_OF_TICKET_PER_TABLE as number_of_tickets_per_table, " +
                    " (SELECT count(1) FROM ticketing_order_manager as tom " +
                    "WHERE tom.order_id = et.ticket_order_id) as ticketTypeCountInOrder " +
                    " FROM event_tickets et " +
                    " JOIN event_ticket_type ett on et.ticketing_type_id = ett.id " +
                    " JOIN ticketing t on ett.ticketing_id = t.id" +
                    " WHERE ett.ticketing_id=:ticketingId and (et.recurring_event_id=:recurringEventId OR 0 = :recurringEventId) " +
                    " and ett.data_type IN (:dataType) " +
                    " and et.ticket_status not in ('CANCELED','DELETED') " +
                    " GROUP BY et.ticket_order_id, et.ticketing_type_id " +
                    " UNION ALL " +
                    " SELECT " +
                    " 0 as ticketCount,event_ticket_type.id, event_ticket_type.ticket_type, event_ticket_type.ticket_bundle_type, 0, 0,0 ,0 ,0 , " +
                    " IF((ticketing.is_recurring_event && event_ticket_type.recurring_event_id is null), 0, event_ticket_type.number_of_tickets), " +
                    " event_ticket_type.data_type as data_type, " +
                    " event_ticket_type.NUMBER_OF_TICKET_PER_TABLE," +
                    " 1 as ticketTypeCountInOrder " +
                    " FROM event_ticket_type " +
                    " JOIN ticketing on ticketing.id = event_ticket_type.ticketing_id" +
                    " WHERE NOT EXISTS (SELECT ticketing_type_id FROM event_tickets WHERE event_tickets.ticketing_type_id = event_ticket_type.id and event_tickets.ticket_status not in ('CANCELED','DELETED')) " +
                    " AND event_ticket_type.data_type IN (:dataType) " +
                    " AND (event_ticket_type.rec_status<>'CANCEL' OR event_ticket_type.rec_status is NULL and is_disable_ticket = false) " +
                    " AND event_ticket_type.ticketing_id=:ticketingId " +
                    " AND (event_ticket_type.recurring_event_id=:recurringEventId OR 0 = :recurringEventId)" +
                    ") x" +
                    " ) z " +
                    " GROUP BY z.ticketing_type_id, z.ticket_bundle_type ",
            nativeQuery = true)
    List<Object[]> getTicketingSalesDataForDashboard(@Param("ticketingId") long ticketingId, @Param("recurringEventId") long recurringEventId,
                                                     @Param("percentage") double percentage, @Param("fixed") double fixed, @Param("isStripe") boolean isStripe,
                                                     @Param("dataType") List<String> dataType);

    @Modifying
    @Transactional
    @Query("UPDATE EventTickets SET ticketingTypeId =:newTicketType, recurringEventId=:newRecurringEventId " +
            " WHERE ticketingOrder.id=:orderid AND ticketingTypeId.id=:oldTicketTypeId AND recurringEventId=:oldRecurringEventId")
    void updateTicketTypeOfEventTickets(@Param("orderid") Long orderid,
                                        @Param("newTicketType") TicketingType newTicketType,
                                        @Param("oldTicketTypeId") Long oldTicketTypeId,
                                        @Param("oldRecurringEventId") Long oldRecurringEventId,
                                        @Param("newRecurringEventId") Long newRecurringEventId);


    @Modifying
    @Query("UPDATE EventTickets SET autoAssignedAttendeeNumber = null WHERE autoAssignedAttendeeNumber.id =:autoAssignedAttendeeNumberId")
    void updateEventTicketForAutoAssignedAttendeeNumber(@Param("autoAssignedAttendeeNumberId") Long autoAssignedAttendeeNumberId);

    @Query("SELECT et.id From EventTickets as et WHERE et.autoAssignedAttendeeNumber.id =:autoAssignedAttendeeNumberId")
    List<Long> getEventTicketIdsByAutoAssignedAttendeeNumberId(@Param("autoAssignedAttendeeNumberId") Long autoAssignedAttendeeNumberId);

    @Query("SELECT et From EventTickets et " +
            "JOIN FETCH et.ticketingOrder ticketingOrder " +
            "JOIN FETCH ticketingOrder.purchaser purchaser " +
            "LEFT JOIN FETCH et.ticketingTable as ticketingTable " +
            "JOIN FETCH et.ticketingTypeId as ticketingTypeId " +
            "JOIN FETCH ticketingTypeId.ticketing ticketing " +
            "LEFT JOIN FETCH et.recurringEvents recurringEvents " +
            "JOIN FETCH et.ticketHolderAttributesId ticketHolderAttributesId " +
            "WHERE et.ticketingOrder=:ticketingOrder AND ticketingTypeId.bundleType <>'INDIVIDUAL_TICKET'")
    List<EventTickets> findByTicketsHaveOnlyBundleTypeTicketByOrderId(@Param("ticketingOrder") TicketingOrder ticketingOrder);

    @Query("select count(etn.id) from EventTickets as etn " +
            " WHERE etn.autoAssignedAttendeeNumber = :sequence group by etn.autoAssignedAttendeeNumber")
    Integer countAttendeeAssignedToSequence(@Param("sequence") AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers);

    @Modifying
    @Query("UPDATE EventTickets SET autoAssignedAttendeeNumber = null WHERE id in (:eventTicketIds)")
    void updateEventTicketSetAutoAssignedAttendeeNumberNull(@Param("eventTicketIds") List<Long> eventTicketIds);

    @Query("SELECT et FROM EventTickets as et " +
            "JOIN FETCH et.ticketingOrder as ticketingOrder " +
            "JOIN FETCH ticketingOrder.eventid as eventId " +
            "JOIN FETCH ticketingOrder.purchaser as purchaser " +
            "JOIN FETCH et.ticketingTypeId as ticketingTypeId " +
            "LEFT JOIN FETCH et.ticketingTable as ticketingTable " +
            "LEFT JOIN FETCH et.recurringEvents as recurringEvents " +
            "WHERE et.id=:ticketId ")
    EventTickets findByIdJoinFetch(@Param("ticketId") long ticketId);

    @Query("SELECT et FROM EventTickets as et " +
            "JOIN FETCH et.ticketingOrder as ticketingOrder " +
            "JOIN FETCH et.ticketingTypeId as ticketingTypeId " +
            "JOIN FETCH et.ticketHolderAttributesId as ticketHolderAttributesId " +
            "LEFT JOIN FETCH et.ticketingTable as ticketingTable " +
            "LEFT JOIN FETCH et.recurringEvents as recurringEvents " +
            "WHERE ticketingOrder.id IN (:ticketingOrderIds)")
    List<EventTickets> findAllByListOfTicketingOrders(@Param("ticketingOrderIds") List<Long> ticketingOrderIds);

	@Query("SELECT et FROM EventTickets as et " + "JOIN FETCH et.ticketingOrder as ticketingOrder "
			+ "JOIN FETCH et.ticketingTypeId as ticketingTypeId "
			+ "JOIN FETCH et.ticketHolderAttributesId as ticketHolderAttributesId "
			+ "LEFT JOIN FETCH et.ticketingTable as ticketingTable "
			+ "LEFT JOIN FETCH et.recurringEvents as recurringEvents "
			+ "WHERE ticketingOrder.id IN (:ticketingOrderIds)"
			+ "AND (et.createdAt >= :formattedDate OR et.updatedAt >= :formattedDate)")
	List<EventTickets> findAllByTicketingOrderIdsAndFormattedDateAfter(
			@Param("ticketingOrderIds") List<Long> ticketingOrderIds, @Param("formattedDate") Date formattedDate);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingOrder as ticketingOrder " +
            " JOIN et.event as event " +
            " WHERE et.userId=:holderUserId " +
            " AND et.ticketStatus <>:status " +
            " AND (event.eventStatus is null or  event.eventStatus <> 'EVENT_DELETED') ")
    List<EventTickets> findByHolderUserIdAndStatusNot(@Param("holderUserId") Long holderUserId, @Param("status") TicketStatus status);


    @Query(" SELECT (SUM(et.salesTaxFee) - SUM(et.refundedSalesTaxFee)) " +
            " FROM EventTickets AS et" +
            " WHERE et.ticketingTypeOnlyId IN (:ticketingTypeIds)")
    Double findTotalSalesTax(@Param("ticketingTypeIds") List<Long> ticketTypeIds);

    @Query(" SELECT et.id " +
            " From EventTickets as et " +
            " WHERE et.ticketingTypeId IN (:ticketingTypeId) " +
            " AND et.ticketStatus <>'DELETED' ")
    List<Long> getEventTicketIdsByTicketTypeIds(@Param("ticketingTypeId") List<TicketingType> ticketingTypeId);

    @Modifying
    @Query(" UPDATE EventTickets" +
            " SET recurringEventId = :newId" +
            " WHERE recurringEventId = :oldId" +
            " AND id > 0 AND dataType ='TICKET' ")
    void updateRecurringId(@Param("oldId") long oldId,
                           @Param("newId") long newId);

    @Query(value = "SELECT new com.accelevents.ticketing.dto.TicketingBuyerDataFromDB("
            + "ticketingOrder.id, "
            + "purchaser.firstName, "
            + "purchaser.lastName, "
            + "ticketingOrder.orderDate, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.refundedAmount)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.paidAmount)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.aeFeeAmount)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.ticketPrice)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE (COUNT(CASE WHEN ticketingType.bundleType ='INDIVIDUAL_TICKET' THEN 1 ELSE null END)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE (SUM(case when ticketingType.bundleType <>'INDIVIDUAL_TICKET' THEN 1 ELSE 0 END)) END, "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE COUNT(et.id) END, "
            + "SUM(CASE WHEN (et.ticketStatus = :ticketStatus) THEN 1 ELSE 0 END), "
            + "SUM(CASE WHEN (et.ticketPaymentStatus = 'REFUNDED') THEN 1 ELSE 0 END), "
            + "ticketingOrder.status, "
            + "SUM(et.wlAFeeAmount + et.wlBFeeAmount), "
            + "SUM(et.refundedWlAFee + et.refundedWlBFee), "
            + "SUM(et.refundedAEFee), "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.salesTaxFee)) END, "
            + "SUM(et.refundedSalesTaxFee), "
            + "CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.vatTaxFee)) END, "
            + "SUM(et.refundedVatTaxFee), "
            + "SUM(CASE WHEN (et.chargeId IS NOT NULL) THEN 1 ELSE 0 END) ) "
            + "FROM EventTickets AS et "
            + "JOIN et.ticketingOrder AS ticketingOrder "
            + "JOIN ticketingOrder.purchaser AS purchaser "
            + "JOIN et.ticketingTypeId AS ticketingType "
            + "JOIN ticketingType.ticketing AS ticketing "
            + "WHERE "
            + "( et.recurringEventId = :recurringEventId "
            + " OR 0 = :recurringEventId "
            + ") "
            + "AND et.eventId=:eventId AND "
            + " et.ticketStatus <> 'DELETED' AND "
            + " (:dataType is null OR et.dataType =:dataType) AND "
            + " ticketingType.ticketType IN (:ticketType) AND "
            + " ticketingOrder.orderDate between :fromDate and :toDate "
            + " GROUP BY et.ticketingOrder order by ticketingOrder.orderDate desc")
    Page<TicketingBuyerDataFromDB> getBuyerDataBetweenDates(@Param("eventId") Long eventId, //NOSONAR
                                                            Pageable pageable,
                                                            @Param("ticketType") List<TicketType> ticketType,
                                                            @Param("fromDate") Date from, @Param("toDate") Date to,
                                                            @Param("recurringEventId") Long recurringEventId,
                                                            @Param("ticketStatus") TicketStatus ticketStatus,
                                                            @Param("dataType") DataType dataType);

    @Query(" SELECT count(DISTINCT et) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId=:eventId AND " +
            " (ticketingType.ticketType =:ticketType) " +
            " AND (:dataType is null OR et.dataType =:dataType) " +
            " AND et.ticketStatus  not in ('CANCELED','DELETED') ")
    Long countTicketsByTicketType(@Param("eventId") Long eventId, @Param("ticketType") TicketType ticketType, @Param("dataType") DataType dataType);

    @Query(" SELECT count(DISTINCT et) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId=:eventId AND " +
            " (et.ticketStatus =:ticketStatus AND ticketingType.ticketType IN (:ticketTypes) AND ticketingType.id IN (:ticketTypeIds)) " +
            " AND (:dataType is null OR et.dataType =:dataType) " +
            " AND et.ticketPaymentStatus not IN (:status) ")
    Long countTicketsByTicketStatus(@Param("eventId") Long event, @Param("ticketTypes") List<TicketType> ticketTypes, @Param("status") List<TicketPaymentStatus> status, @Param("ticketStatus") TicketStatus ticketStatus, @Param("dataType") DataType dataType, @Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Query(" SELECT count(DISTINCT et) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId=:eventId AND" +
            " (et.ticketPaymentStatus =:status AND ticketingType.ticketType IN (:ticketTypes))" +
            " AND (:dataType is null OR et.dataType =:dataType) ")
    Long countTicketsByTicketStatus(@Param("eventId") Long eventId, @Param("ticketTypes") List<TicketType> ticketTypes, @Param("status") TicketPaymentStatus status, @Param("dataType") DataType dataType);

    @Query(" SELECT et FROM EventTickets et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " WHERE et.eventId=:eventId " +
            " AND et.userId=:holderUserId " +
            " AND et.ticketStatus not in (:ticketStatus) ")
    List<EventTickets> checkNotCheckedInNotRefundedTicketExists1(@Param("holderUserId") Long holderUserId,
                                                                 @Param("eventId") Long eventId,
                                                                 @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.holderUserId AS holderUser " +
            " WHERE et.eventId=:eventId " +
            " AND et.userId=:holderUserId " +
            " AND et.ticketStatus <> 'DELETED' ")
    List<EventTickets> findTicketRegisterForUser(@Param("holderUserId") Long holderUserId,
                                                 @Param("eventId") Long eventId);

    @Query("SELECT et.id, et.holderEmail, et.userId FROM EventTickets AS et " +
            " WHERE et.ticketingOrderId=:ticketingOrderId " +
            " AND  et.ticketStatus <> 'DELETED' ")
    List<Object[]> findEmailAndHolderIdByTicketingOrder(@Param("ticketingOrderId") long ticketingOrderId);

    @Query("SELECT et FROM EventTickets et " +
            "WHERE et.eventId=:eventId " +
            "AND et.userId=:holderUserId  AND et.id<>:eventTicketId")
    List<EventTickets> findOtherEventTicketForSameUserANDEvent(@Param("eventId") Long eventId, @Param("holderUserId") Long holderUserId, @Param("eventTicketId") Long eventTicketId);


    @Query(value = "SELECT et FROM EventTickets et  WHERE et.userId=:holderUserId AND et.ticketStatus <>:status ")
    List<EventTickets> getHolderEventTicketsANDNotINRecordStatus(@Param("holderUserId") Long holderUserId, @Param("status") TicketStatus status);

    @Query(value = " SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN et.event as event " +
            " WHERE et.holderUserId=:holderUserId AND et.ticketStatus <>:status " +
            " AND (event.eventStatus is null or  event.eventStatus <> 'EVENT_DELETED') " +
            " AND et.guestOfBuyer = false " +
            " AND (ticketingType.status<>'CANCEL' or ticketingType.status is NULL) " +
            " AND ticketingType.isDisableTicket = false ")
    List<EventTickets> getHolderEventTicketsANDNotINRecordStatusAndEventNotDeleted(@Param("holderUserId") User holderUserId, @Param("status") TicketStatus status);


    @Cacheable(value = "findBlockedEmailsForEvent", key = "#p0", unless="#result == null")
    @Query("SELECT DISTINCT et.holderEmail " +
            "FROM EventTickets et " +
            "WHERE et.event.id = :eventId " +
            "AND et.recordStatus = :status")
    List<String> findBlockedEmailsForEvent(
            @Param("eventId") Long eventId,
            @Param("status") RecordStatus status);


    @Query(value = "SELECT count(et) > 0 FROM EventTickets et " +
            "JOIN et.ticketingTypeId AS ticketingType ON ticketingType.id =et.ticketingTypeId " +
            "WHERE et.ticketingOrderId =:orderId and ticketingType.allowPDFDownload = true")
    Boolean findAllowPDFValue(@Param("orderId") Long orderId);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.holderUserId AS holderUser " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " WHERE et.event=:event " +
            " AND et.dataType = 'TICKET' " +
            " AND et.ticketStatus <> 'DELETED' ")
    List<EventTickets> findAllTicketsByEventIdJoinFetch(@Param("event") Event event);


    @Query("SELECT DISTINCT holderUser " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.holderUserId AS holderUser " +
            " WHERE et.event=:event " +
            " AND et.dataType = 'TICKET' " +
            " AND et.ticketStatus <> 'DELETED' " +
            " AND (COALESCE(:searchString,'') = '' OR (holderUser.firstName like %:searchString% " +
            "       OR holderUser.lastName like %:searchString% " +
            "       OR holderUser.email like %:searchString% " +
            "       OR CONCAT(holderUser.firstName,' ',holderUser.lastName) LIKE %:searchString%)) " +
            " ORDER BY holderUser.firstName ")
    Page<User> findAllTicketHolderUserByEvent(@Param("event") Event event, @Param("searchString") String searchString, Pageable pageable);

    @Query("SELECT et FROM EventTickets et" +
            " WHERE et.id IN (:ids) AND et.holderUserId.userId=:userId")
    List<EventTickets> findByIdsAndHolderUserId(@Param("ids") List<Long> ids, @Param("userId") Long userId);


    @Query(" SELECT et FROM EventTickets as et " +
            " JOIN FETCH et.holderUserId AS holderUser " +
            " WHERE et.ticketingOrderId IN (:ticketingOrderIds)")
    List<EventTickets> findAllHolderUsersByListOrderIds(@Param("ticketingOrderIds") List<Long> ticketingOrderIds);

    @Query("SELECT count(et) > 0 FROM EventTickets AS et " +
            " WHERE et.eventId=:eventId " +
            " AND et.dataType = 'TICKET' " +
            " AND et.ticketStatus <> 'DELETED' ")
    Boolean isAnyTicketSold(@Param("eventId") Long eventId);

    @Query(value = "SELECT id FROM event_tickets WHERE ticket_order_id=:orderId AND (rec_status='CANCEL' OR ticket_status='DELETED' OR status='UNPAID') ", nativeQuery = true)
    List<Object> findUnSuccessTicketsForPayFlowByOrderId(@Param("orderId") long orderId);

    @Modifying
    @Query(value = "UPDATE event_tickets SET rec_status=:recStatus, status=:status, ticket_status='REGISTERED', charge_id=:chargeId WHERE id=:id", nativeQuery = true)
    void updateEventTicketsForPayFlow(@Param("recStatus") String recStatus, @Param("status") String status,@Param("chargeId") String chargeId, @Param("id") long id);


    @Query("SELECT et FROM EventTickets et " +
            " JOIN FETCH et.ticketingTypeId as ticketTypeId" +
            " JOIN FETCH et.holderUserId holderUser" +
            " JOIN FETCH et.ticketPurchaserId as purchaser" +
            " LEFT JOIN FETCH et.ticketingTable as ticketingTableId" +
            " WHERE et.ticketingOrder=:ticketingOrder AND et.holderUserId=:user AND et.ticketStatus <> 'DELETED'")
    List<EventTickets> findByTicketingOrderAndByUserJoinFetch(@Param("ticketingOrder") TicketingOrder ticketingOrder,
                                                              @Param("user") User user);

    @Query(value = "SELECT count(et) > 0 FROM EventTickets et " +
            "JOIN et.ticketingTypeId AS ticketingType ON ticketingType.id =et.ticketingTypeId " +
            "WHERE et.ticketingOrderId =:orderId and (ticketingType.allowInvoicePDFDownload = true OR ticketingType.allowInvoicePDFForBuyers = true)")
    Boolean isAllowedInvoicePDFDownload(@Param("orderId") Long orderId);

    @Query(value = "SELECT count(et) > 0 FROM EventTickets et " +
            "JOIN et.ticketingTypeId AS ticketingType ON ticketingType.id =et.ticketingTypeId " +
            "WHERE et.ticketingOrderId =:orderId and (ticketingType.allowInvoicePDFDownload = true OR ticketingType.allowInvoicePDFForHolders = true)")
    Boolean isAllowedInvoicePDFDownloadForHolder(@Param("orderId") Long orderId);


    @Query(value = "SELECT et FROM EventTickets et " +
            "WHERE et.ticketingOrderId =:orderId and et.ticketStatus <> 'DELETED' ")
    List<EventTickets> findEventTicketByTicketingOrder(@Param("orderId") Long orderId);

    @Query(value = "select et.h_email,et.holder_user_id from event_tickets As et " +
            "where et.event_id=:eventId " +
            "AND et.data_type='TICKET' " +
            "AND ticket_status not in ('CANCELED','DELETED')",nativeQuery = true)
    List<Object[]> getAllHolderEmailAndUserIdOfEventTicketsByEventId(@Param("eventId") Long eventId);

    @Query(value = "select et.userId from EventTickets As et " +
            "JOIN et.holderUserId as u " +
            "where et.eventId=:eventId " +
            "AND et.dataType='TICKET' " +
            "AND et.ticketStatus not in ('CANCELED','DELETED') " +
            "AND u.email <> '<EMAIL>' "+
            "AND u.email not like '%brilworks.com%' " +
            "AND u.email not like '%accelevents.com%' " +
            "AND u.email not like '%acceleventsREMOVED.com%' ")
    List<Long> getAllUserIdOfEventTicketsByEventId(@Param("eventId") Long eventId);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder "  +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.holderUserId holderUser "  +
            " LEFT JOIN  FETCH et.ticketingTable " +
            " WHERE et.id in (:eventTicketIds)")
    List<EventTickets> findByIds(@Param("eventTicketIds") List<Long> eventTicketIds);

    @Query(value = " SELECT SUM(t1.cnt) as soldTicket " +
            " FROM ( " +
            "    SELECT COUNT(et.id) as cnt FROM event_tickets et " +
            "       WHERE et.event_id = :eventId " +
            "       AND et.ticket_status not in ('CANCELED','DELETED') " +
            "       AND et.data_type = :dataType " +
            "    UNION " +
            "    SELECT COUNT(1) as cnt FROM ticketing_order to2 " +
            "       WHERE to2.event_id = :eventId " +
            "       AND to2.expire_in >= now() " +
            "       AND to2.order_status = 'CREATE' " +
            " ) t1 ", nativeQuery = true)
    Integer getNonRefundedSoldEventTicketAndActiveOrderCountUnionTotalWithoutRecurringEvent(@Param("eventId") Long eventId, @Param("dataType") String dataType);

    @Query(value = "SELECT et.id From EventTickets as et WHERE et.barcodeId =:barcodeId AND et.eventId = :eventId AND et.ticketStatus <>'DELETED'")
    Long findIdByBarcodeIdAndEventId(@Param("barcodeId") String barcodeId, @Param("eventId") Long eventId);


    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " LEFT JOIN FETCH ticketOrder.ticketingCoupon AS coupon " +
            " LEFT JOIN FETCH ticketOrder.trackingLinks " +
            " WHERE et.event=:event " +
            " AND (ticketOrder.status IN(:status) ) " +
            " AND (et.recordStatus!='BLOCK' OR et.recordStatus is null)" +
            " AND et.holderEmail IN (:emails)" +
            " ORDER BY ticketOrder.id,  ticketingType.id ")
    List<EventTickets> findByEventIdAndTicketingStatusJoinFetchAndEmails(@Param("event") Event event,
                                                                         @Param("status") List<TicketingOrder.TicketingOrderStatus> status,
                                                                         @Param("emails") Set<String> emailList);
    @Query("SELECT distinct et.holderEmail FROM EventTickets AS et WHERE et.holderEmail != null AND et.eventId=:eventId and et.dataType='TICKET'  and et.ticketStatus not in ('CANCELED','DELETED') ")
    Set<String> findHolderEmailsByEventIdAndNotRefunded(@Param("eventId")Long eventId);

    @Query("SELECT DISTINCT et.holderEmail FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId=:eventId AND " +
            " et.holderEmail IN (:emails) AND " +
            " et.dataType='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.ticketStatus NOT IN ('CANCELED','DELETED')")
    Set<String> findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(@Param("eventId")Long eventId, @Param("emails")Collection<String> emails);

    @Query("SELECT DISTINCT et.holderEmail FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId=:eventId AND " +
            " et.holderEmail IN (:emails) AND " +
            " et.dataType='TICKET' AND " +
            " et.id NOT IN (:ticketIds) AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.ticketStatus NOT IN ('CANCELED','DELETED')")
    Set<String> findHolderEmailsByEventIdAndEmailsInAndDataTypeTicketAndIdNotInAndTicketStatusNotIn(@Param("eventId")Long eventId, @Param("emails")Collection<String> emails, @Param("ticketIds") List<Long> ticketIds);


    @Query("SELECT count(et.id) from EventTickets et join TicketingOrder ot on ot.id=et.ticketingOrderId where et.eventId=:eventId and ot.orderType='EXTERNAL_TRANSACTION'")
    Long findEventTicketsCountByEventIdAndOrderTypeIsExternalTransaction(@Param("eventId") Long eventId);

    @Query("SELECT DISTINCT(et) FROM EventTickets et " +
            " JOIN FETCH et.ticketingOrder " +
            " WHERE et.id IN (:ids) AND  ( et.holderUserId.userId=:userId OR et.ticketPurchaserId.userId=:userId ) ")
    List<EventTickets> findAllByIdAndHolderUserIdOrPurchaserUserId(@Param("ids") List<Long> ids, @Param("userId") Long userId);

    @Query(value = "SELECT new com.accelevents.ticketing.dto.TicketingBuyerDataFromDB(ticketingOrder.id, " +
            " ticketingOrder.orderDate," +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.paidAmount)) END AS paidAmount," +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.ticketPrice)) END AS ticketPrice," +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.refundedAmount)) END AS refundedAmount," +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.aeFeeAmount)) END AS aeFeeAmount," +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.salesTaxFee)) END as salesTaxFeeAmount," +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0.0 ELSE (SUM(et.vatTaxFee)) END AS vatTaxFeeAmount," +
            " SUM(et.refundedAEFee) AS refundedAeFee," +
            " SUM(et.wlAFeeAmount + et.wlBFeeAmount) AS wlABFee," +
            " SUM(et.refundedWlAFee + et.refundedWlBFee) AS refundedWlABFee," +
            " SUM(et.refundedSalesTaxFee) AS refundedSalesTaxFee ," +
            " SUM(et.refundedVatTaxFee) AS refundedVatTaxFee," +
            " ticketingOrder.status AS orderStatus)" +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingOrder AS ticketingOrder" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " JOIN ticketingType.ticketing AS ticketing" +
            " WHERE" +
            "  ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND et.eventId=:eventId " +
            " AND et.ticketStatus <> 'DELETED' " +
            " AND ticketingType.ticketType IN (:ticketType)" +
            " GROUP BY et.ticketingOrder " +
            "  ORDER BY ticketingOrder.orderDate DESC")
    List<TicketingBuyerDataFromDB> getTicketingSalesDataByEventIdAndTicketType(@Param("eventId") Long eventId,
                                                                               @Param("recurringEventId") Long recurringEventId,
                                                                               @Param("ticketType") List<TicketType> ticketType);



    @Query("Select new com.accelevents.ticketing.dto.TicketingBuyerDataFromDB(ett.ticketingOrderId, COALESCE(SUM(ett.ccFeeAmount), 0)) from " +
            " EventTicketTransaction AS ett" +
            " WHERE ett.paymentType = 'CARD'" +
            " AND ett.chargeStatus='PAID'" +
            " AND ett.ticketingOrderId in (:orderIds)" +
            " GROUP By ett.ticketingOrderId" )
    List<TicketingBuyerDataFromDB> getCCFeesDetails(@Param("orderIds") List<Long> orderIds);

    @Query(value = "SELECT new com.accelevents.ticketing.dto.TicketingBuyerDataFromDB(ticketingOrder.id, " +
            " ticketingOrder.orderDate," +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE (COUNT(CASE WHEN ticketingType.bundleType ='INDIVIDUAL_TICKET' THEN 1 ELSE null END)) END, " +
            " CASE WHEN ticketing.isRecurringEvent = TRUE AND ticketingType.recurringEventId = NULL THEN 0L ELSE (SUM(case when ticketingType.bundleType <>'INDIVIDUAL_TICKET' THEN 1 ELSE 0 END)) END )" +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingOrder AS ticketingOrder" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " JOIN ticketingType.ticketing AS ticketing" +
            " WHERE" +
            "  ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND et.eventId=:eventId " +
            " AND et.ticketStatus <> 'DELETED' " +
            " AND ticketingType.ticketType IN (:ticketType)" +
            " AND ticketingOrder.orderDate between :fromDate and :toDate " +
            " GROUP BY et.ticketingOrder " +
            "  ORDER BY ticketingOrder.orderDate DESC")
    Page<TicketingBuyerDataFromDB> getTicketingSoldDataByEventIdAndTicketType(@Param("eventId") Long eventId,
                                                                               @Param("recurringEventId") Long recurringEventId,
                                                                               @Param("ticketType") List<TicketType> ticketType,
                                                                               @Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);

    @Query("SELECT et FROM EventTickets as et " +
            "JOIN FETCH et.ticketHolderAttributesId as ticketHolderAttributesId " +
            "WHERE et.ticketingOrderId IN (:orderIds)")
    List<EventTickets> findAllEventTicketByOrderIds(@Param("orderIds") List<Long> orderIds);

    @Query(" SELECT et FROM EventTickets et " +
            " WHERE et.eventId=:eventId " +
            " AND et.userId=:holderUserId " +
            " AND et.ticketStatus not in (:ticketStatus) ")
    List<EventTickets> findEventTicketsOnlyByEventIdAndHolderUserId(@Param("eventId") Long eventId,
                                                                    @Param("holderUserId") Long holderUserId,
                                                                 @Param("ticketStatus") List<TicketStatus> ticketStatus);


    @Query(" SELECT new com.accelevents.dto.TicketTypeSoldAndBookCountDto(COUNT(1), et.ticketingTypeOnlyId) " +
            " FROM EventTickets AS et" +
            " WHERE et.ticketingTypeOnlyId IN (:ticketingTypeOnlyIds) AND et.ticketStatus NOT IN (:ticketStatus) " +
            " GROUP BY et.ticketingTypeOnlyId ")
    List<TicketTypeSoldAndBookCountDto> soldTicketCountByTicketingTypeId(@Param("ticketingTypeOnlyIds") List<Long> ticketingTypeOnlyIds,
                                                                   @Param("ticketStatus") List<TicketStatus> ticketStatus);


    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " LEFT JOIN FETCH et.holderUserId AS holderUser " +
            " JOIN FETCH ticketingType.ticketing AS ticketing " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " JOIN FETCH ticketOrder.purchaser AS purchaser " +
            " JOIN et.event as event " +
            " LEFT JOIN FETCH et.ticketingTable AS ticketingTable" +
            " WHERE et.barcodeId=:barcodeId and  (event.eventStatus is null or  event.eventStatus <> 'EVENT_DELETED') ")
    List<EventTickets> findAllTicketsBarcodeId(@Param("barcodeId") String barcodeId);

    @Query("SELECT DISTINCT et.ticketingOrderId FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.ticketPurchaserId AS buyer " +
            " WHERE et.eventId=:eventId AND " +
            " buyer.email = :buyerEmail AND " +
            " et.dataType='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.ticketStatus NOT IN ('CANCELED','DELETED')")
    Set<Long> findBuyerOrderIdByEventIdAndBuyerEmailsInAndDataTypeTicketAndTicketStatusNotIn(@Param("eventId")Long eventId, @Param("buyerEmail")String buyerEmail);

    @Query("SELECT DISTINCT buyer.email FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.ticketPurchaserId AS buyer " +
            " WHERE et.eventId=:eventId AND " +
            " buyer.email in (:emails) AND " +
            " et.dataType='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.ticketStatus NOT IN ('CANCELED','DELETED')")
    Set<String> findBuyerEmailsByEventIdAndEmailsInAndDataTypeTicketAndTicketStatusNotIn(@Param("eventId")Long eventId, @Param("emails")Collection<String> emails);
}
