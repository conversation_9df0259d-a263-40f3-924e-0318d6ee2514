package com.accelevents.repositories;

import com.accelevents.domain.TicketingCoupon;
import com.accelevents.domain.enums.RecordStatus;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface TicketingCouponRepository extends CrudRepository<TicketingCoupon, Long> {
	List<TicketingCoupon> findAllByEventId(long eventId);

	@Query("SELECT ticketingCoupon FROM TicketingCoupon as ticketingCoupon " +
			" WHERE ticketingCoupon.eventId =:eventId And ticketingCoupon.recurringEventId =:recurringEventId")
	List<TicketingCoupon> getAllByEventIdAndRecurringEventId(@Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT coupon FROM TicketingCoupon coupon " +
            "WHERE coupon.eventId = :eventId " +
            "AND coupon.recurringEventId = :recurringEventId " +
            "AND (coalesce(:searchString, '') = '' OR coupon.name LIKE %:searchString% OR CAST(coupon.amount AS string) LIKE %:searchString%)")
    Page<TicketingCoupon> getAllByEventIdAndRecurringEventId(@Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId,@Param("searchString")String searchString,Pageable pageable);

    @Query("SELECT coupon FROM TicketingCoupon coupon " +
            "WHERE coupon.eventId = :eventId " +
            "AND coupon.recurringEventId IS NULL " +
            "AND (coalesce(:searchString, '') = ''  OR coupon.name LIKE %:searchString% OR CAST(coupon.amount AS string) LIKE %:searchString%)")
    Page<TicketingCoupon> getAllByEventIdAndRecurringEventIdIsNull(@Param("eventId") Long eventId,@Param("searchString")String searchString,Pageable pageable);

    @Query("SELECT coupon FROM TicketingCoupon coupon " +
            "WHERE coupon.eventId = :eventId " +
            "AND (coalesce(:searchString, '') = '' OR coupon.name LIKE %:searchString% OR CAST(coupon.amount AS string) LIKE %:searchString%)")
    Page<TicketingCoupon> findAllByEventId(@Param("eventId") Long eventId,@Param("searchString")String searchString,Pageable pageable);

	@Query("SELECT coupon FROM TicketingCoupon coupon WHERE coupon.eventId=:eventId AND coupon.recurringEventId IS NULL")
	List<TicketingCoupon> getAllByEventIdAndRecurringEventIdIsNull(@Param("eventId") Long eventId);

	/*@Query("Select ticketingCoupon From TicketingCoupon ticketingCoupon where ticketingCoupon.eventId=:eventId " +
			" AND ticketingCoupon.name=:name AND ticketingCoupon.recurringEventId=coalesce(:recurringEventId, ticketingCoupon.recurringEventId)")
	TicketingCoupon findByEventIdAndName(@Param("eventId") long eventId, @Param("name") String name, @Param("recurringEventId") Long recurringEventId);*/

	TicketingCoupon findByEventIdAndNameAndCreatedFromIsNull(long eventId, String name);

    TicketingCoupon findByEventIdAndIdAndCreatedFromIsNull(long eventId, long couponId);

	TicketingCoupon findByRecurringEventIdAndName(Long recurringEventId, String name);

    Optional<TicketingCoupon> findByEventIdAndId(long eventId, long couponId);

	@Query("SELECT CASE WHEN COUNT(ticketingCoupon) > 0 THEN true ELSE false end FROM TicketingCoupon as ticketingCoupon " +
			" WHERE ticketingCoupon.eventId =:eventId And ticketingCoupon.name=:name and (ticketingCoupon.recurringEventId =:recurringEventId OR 0 =:recurringEventId)")
	boolean isCouponExist(@Param("name") String name, @Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT ticketingCoupon FROM TicketingCoupon as ticketingCoupon " +
            " WHERE ticketingCoupon.eventId =:eventId And ticketingCoupon.name=:name and (ticketingCoupon.recurringEventId =:recurringEventId OR 0 =:recurringEventId)")
    List<TicketingCoupon> getCouponByCodeAndEventAndRecurringEvent(@Param("name") String name, @Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId);

	TicketingCoupon findByEventIdAndNameAndRecurringEventId(long eventId, String couponcode, Long recurringEventId);

    TicketingCoupon findByEventIdAndIdAndRecurringEventId(long eventId, long couponcode, Long recurringEventId);

	@Query("Select coupon From TicketingCoupon coupon WHERE coupon.createdFrom=:createdFromCouponId AND coupon.eventId=:eventId")
	List<TicketingCoupon> findAllByCreatedFromAndEventId(@Param("createdFromCouponId") Long createdFromCouponId,@Param("eventId") Long eventId);

	@Query("Select coupon From TicketingCoupon coupon WHERE coupon.createdFrom=:createdFromCouponId AND coupon.eventId=:eventId AND coupon.recurringEvents.recurringEventStartDate >=:currentDateAndTime")
	List<TicketingCoupon> findAllByCreatedFromAndEventIdWithFutureDate(@Param("createdFromCouponId") Long createdFromCouponId,@Param("eventId") Long eventId, @Param("currentDateAndTime") Date currentDateAndTime);


    @Modifying
    @Caching(evict = {
            @CacheEvict(value = "isTicketingCoupanAvailableForEvent", key = "#p1",condition="#p1!=null")
    })
	@Query("Delete FROM TicketingCoupon coupon WHERE coupon.eventId=:eventId AND coupon.recurringEventId IN (:recurringEventsToBeDelete)")
	void deleteAllByRecurringIdAndEventId(@Param("recurringEventsToBeDelete") List<Long> recurringEventsToBeDelete, @Param("eventId") Long eventId);

	@Query("Select coupon From TicketingCoupon coupon WHERE coupon.eventId=:eventId AND coupon.recurringEventId IS NOT NULL")
	List<TicketingCoupon> getAllWhichHavingRecurringEventIdNotNull(@Param("eventId") Long eventId);

	@Query("Select coupon From TicketingCoupon coupon WHERE coupon.recurringEventId IN (:oldReIdsList) ")
    List<TicketingCoupon> findTicketingCouponByRecurringEventList(@Param("oldReIdsList") List<Long> oldReIdsList);

	@Query("SELECT ticketingCoupon.name from TicketingCoupon AS ticketingCoupon WHERE ticketingCoupon.eventId = :eventId")
    List<String> getCouponCodes(@Param("eventId") long eventId);

    BigInteger countByRecurringEventId(Long recurringEventId);

	BigInteger countByEventId(long eventId);

	@Modifying
	@Query("UPDATE TicketingCoupon SET status = :status WHERE recurringEventId IN (:recurringEventIds) AND eventId = :eventId")
	void updateTicketCouponRecStatusByRecurringEventIds(@Param("recurringEventIds") List<Long> recurringEventIds, @Param("eventId") Long eventId, @Param("status") RecordStatus status);

    List<TicketingCoupon> findByEventIdIn(List<Long> listOfEventId);

	@Query(value = " Select count(ticketingCoupon.id) FROM TicketingCoupon ticketingCoupon where FIND_IN_SET (:eventTicketTypeIds,ticketingCoupon.eventTicketTypeId) > 0")
	Long countByEventTicketTypeId(@Param("eventTicketTypeIds") Long eventTicketTypeId);

    @Query("SELECT CASE WHEN COUNT(ticketingCoupon) > 0 THEN true ELSE false END FROM TicketingCoupon ticketingCoupon where ticketingCoupon.eventId=:eventId and ticketingCoupon.enableThirdPartyValidation is true")
    boolean isEnableThirdPartyValidation(@Param("eventId") Long eventId);

    @Query("SELECT coupon FROM TicketingCoupon coupon WHERE coupon.eventId=:eventId AND coupon.recurringEventId IS NULL and coupon.enableThirdPartyValidation is true")
    List<TicketingCoupon> getAllIntegrationEnabledCouponByEventId(@Param("eventId") Long eventId);

    @Query("SELECT coupon FROM TicketingCoupon coupon WHERE coupon.eventId=:eventId AND coupon.recurringEventId = :recurringEventId and coupon.enableThirdPartyValidation is true")
    List<TicketingCoupon> getAllIntegrationEnabledCouponByEventId(@Param("eventId") Long eventId, @Param("recurringEventId") Long recurringEventId);

    @Caching(evict = {
            @CacheEvict(value = "isTicketingCoupanAvailableForEvent", key = "#p0.eventId",condition="#p0!=null")
    })
    TicketingCoupon save(TicketingCoupon ticketingCoupon);

    @Cacheable(value = "isTicketingCoupanAvailableForEvent", key = "#p0", unless="#result == null")
    @Query("SELECT CASE WHEN COUNT(c.id) > 0 THEN true ELSE false END FROM TicketingCoupon c " +
           "WHERE c.eventId = :eventId " +
           "AND (COALESCE(:recurringEventId, 0) = 0 OR c.recurringEventId = :recurringEventId) ")
    boolean isTicketingCoupanAvailableForEvent(@Param("eventId") long eventId, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT coupon.id FROM TicketingCoupon coupon " +
            " WHERE coupon.eventId=:eventId " +
            " AND coupon.name=:couponCode " +
            " AND coupon.recurringEventId =:recurringEventId")
    Long findCouponIdByEventIdAndNameAndRecurringEventId(@Param("eventId") long eventId, @Param("couponCode") String couponCode, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT coupon.id FROM TicketingCoupon coupon " +
            " WHERE coupon.eventId=:eventId " +
            " AND coupon.name=:couponCode " +
            " AND coupon.recurringEventId IS NULL")
    Long findCouponIdByEventIdAndName(@Param("eventId") long eventId, @Param("couponCode") String couponCode);

    @Query(" SELECT coupon.id, coupon.name " +
            " FROM TicketingCoupon coupon " +
            " WHERE coupon.id IN (:ids)")
    List<Object[]> findNameAndIdByListOfIds(List<Long> ids);

    @Query("SELECT coupon.name FROM TicketingCoupon coupon WHERE coupon.id = :id")
    String findNameById(@Param("id") Long id);
}
