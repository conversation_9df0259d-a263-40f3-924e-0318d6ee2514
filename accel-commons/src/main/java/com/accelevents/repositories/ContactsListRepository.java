package com.accelevents.repositories;

import com.accelevents.auction.dto.ContactsListDto;
import com.accelevents.auction.dto.ContactsListNameDto;
import com.accelevents.domain.ContactsList;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ContactsListRepository extends CrudRepository<ContactsList, Long> {

    Optional<ContactsList> findByIdAndEventId(@Param("id") long id, @Param("eventId") long eventId);

    @Query(" SELECT CASE WHEN COUNT(cl) > 0 THEN TRUE ELSE FALSE END FROM ContactsList cl " +
            " WHERE cl.listName = :listName " +
            " AND cl.eventId = :eventId ")
    boolean isExistsByListNameAndEventId(@Param("eventId") long eventId, @Param("listName") String listName);
    @Query(" SELECT CASE WHEN COUNT(cl) > 0 THEN TRUE ELSE FALSE END FROM ContactsList cl " +
            " WHERE cl.listName = :listName " +
            " AND cl.eventId = :eventId " +
            " AND (COALESCE(cl.displayViewId,0)!=:displayViewId)")
    boolean isExistsByListNameAndEventIdAndDisplayViewId(@Param("eventId") long eventId, @Param("listName") String listName,@Param("displayViewId") Long displayViewId);


    @Query( "SELECT NEW com.accelevents.auction.dto.ContactsListDto(cl.id, cl.listName, cl.eventId, cl.updatedDate, CONCAT(cl.createdBy.firstName, ' ', cl.createdBy.lastName) AS createdByUser, COUNT(c) as totalContactsCount, cl.displayViewId) " +
            "FROM ContactsList cl " +
            "LEFT JOIN ContactsListMapping clm ON cl.id = clm.contactsList.id AND clm.recStatus <> 'DELETE' " +
            "LEFT JOIN Contacts c ON clm.contacts.id = c.id AND c.eventId = :eventId " +
            "LEFT JOIN DisplayView dv ON cl.displayViewId = dv.id " +
            "WHERE cl.eventId = :eventId " +
            "AND  (cl.displayViewId IS NULL OR (dv.isVisibleToEveryone = true OR dv.createdBy = :userId)) " +
            "AND (:searchString IS NULL OR (CONCAT(cl.createdBy.firstName, ' ', cl.createdBy.lastName) LIKE %:searchString% OR cl.listName LIKE %:searchString%)) " +
            "GROUP BY cl.id")
    Page<ContactsListDto> findAllContactListsWithContactCountByEventIdAndSearchString(@Param("eventId") Long eventId, @Param("searchString") String searchString, Pageable pageable, @Param("userId") Long userId);

    @Query( "SELECT COUNT(c) FROM ContactsList cl " +
            "LEFT JOIN ContactsListMapping clm ON cl.id = clm.contactsList.id AND clm.recStatus <> 'DELETE' " +
            "LEFT JOIN Contacts c ON clm.contacts.id = c.id AND c.eventId = :eventId " +
            "WHERE cl.eventId = :eventId AND cl.id = :contactsListId ")
    Integer getContactsCountByEventIdAndId(@Param("eventId") Long eventId,@Param("contactsListId") Long contactsListId);

    @Query(" SELECT NEW com.accelevents.auction.dto.ContactsListNameDto(cl.id, cl.listName, " +
            "CASE WHEN cl.displayViewId IS NOT NULL THEN TRUE ELSE FALSE END, " +
            "CASE WHEN dv.createdBy = :userId THEN TRUE ELSE FALSE END, " +
            "dv.isVisibleToEveryone) FROM ContactsList cl " +
            "LEFT JOIN DisplayView dv ON cl.displayViewId = dv.id " +
            " WHERE cl.eventId = :eventId ")
    List<ContactsListNameDto> getContactsListNameByEventId(@Param("eventId") long eventId, @Param("userId") Long userId);

    Optional<ContactsList> findByDisplayViewId(@Param("displayViewId") Long displayViewId);
    @Query("SELECT displayViewId FROM ContactsList WHERE id=:contactsListId")
    Long findDisplayViewIdByContactListId(@Param("contactsListId") Long contactsListId);

    @Query("SELECT c FROM ContactsList c WHERE c.displayViewId in (:displayViewIds) ")
    List<ContactsList> findByDisplayViewIds(@Param("displayViewIds") List<Long> displayViewIds);

    List<ContactsList> findByEventId(Long eventId);
}
