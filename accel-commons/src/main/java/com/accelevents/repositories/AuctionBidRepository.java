package com.accelevents.repositories;

import com.accelevents.auction.dto.AuctionHomeDto;
import com.accelevents.domain.Auction;
import com.accelevents.domain.AuctionBid;
import com.accelevents.domain.Item;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.dto.AuctionBidDto;
import com.accelevents.perfomance.dto.AuctionBidAndItemAndBidderDto;
import com.accelevents.perfomance.dto.ItemAuctionBidDto;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


@Repository
public interface AuctionBidRepository extends CrudRepository<AuctionBid, Long> {
	//Not in use
	Optional<AuctionBid> findFirstByItemAndAuctionIdAndIsConfirmedAndIsRefundedOrderByAmountDesc(Item item, long auctionId,
			boolean confirmed, boolean isRefunded);
	//ALOK:Its required only 3 fields,why we return whole object.
	Optional<AuctionBid> findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(Item item, long auctionId, boolean isRefunded);
	//Its only in test case, we will remove this soon
	AuctionBid findFirstByItemAndIsRefundedOrderByAmountDesc(Item item, boolean isRefunded);
	//Not in use
	Optional<AuctionBid> findByItem(Item item);
	//Its required only 1 field
	AuctionBid findFirstByAuctionIdAndItemOrderByAmountDesc(long auctionId, Item item);


	@Query(" SELECT " +
			"new com.accelevents.perfomance.dto.ItemAuctionBidDto(item.id,item.numberOfWinners,auctionBid.id,auctionBid.user.userId,auctionBid.hasPaid) "+
			" FROM AuctionBid auctionBid RIGHT JOIN auctionBid.item item" +
			" WHERE auctionBid.auctionId=:auctionId and auctionBid.isDeleted=false AND auctionBid.isRefunded=false" +
			" AND item.moduleId=:auctionId AND item.moduleType=:moduleType AND item.active=coalesce(:active,item.active)" +
			" ORDER BY item.id ASC,auctionBid.hasPaid desc , auctionBid.amount DESC")
	List<ItemAuctionBidDto> findItemIdAndAuctionBidIdByAuctionIdAndIsDeletedFalseAndIsRefundedFalseAndItemIsActive(
			@Param("auctionId") long auctionId, @Param("moduleType") ModuleType moduleType, @Param("active") Boolean active);

	@Query(" SELECT new com.accelevents.perfomance.dto.AuctionBidAndItemAndBidderDto(item.id,item.name,item.code,item.numberOfWinners,item.position,item.currentBid,auctionBid.id,auctionBid.amount,auctionBid.hasPaid,auctionBid.bidTime,auctionBid.user.userId,auctionBid.user.firstName,auctionBid.user.lastName ) "+
			" FROM AuctionBid auctionBid RIGHT JOIN auctionBid.item item" +
			" WHERE auctionBid.id IN (:auctionBids) ")
	List<AuctionBidAndItemAndBidderDto> findAuctionBidAndItemAndBidderByAuctionIdAndIsDeletedFalseAndIsRefundedFalseAndItemIsActive(
			@Param("auctionBids") List<Long> auctionBids);

	@Query("SELECT auctionBid FROM AuctionBid AS auctionBid"+
			" WHERE auctionBid.auctionId=:auctionId AND auctionBid.isDeleted=:isDeleted AND auctionBid.item IN (:items) ORDER BY auctionBid.amount DESC")
	List<AuctionBid> findAllBidsByAuctionIdAndItemListAndIsDeletedOrderByAmountDesc(@Param("auctionId") long auctionId,
																					@Param("items") List<Item> items, @Param("isDeleted") boolean isDeleted);

    @Query("SELECT auctionBid FROM AuctionBid AS auctionBid " +
            "WHERE auctionBid.auctionId = :auctionId " +
            "AND auctionBid.isDeleted = :isDeleted " +
            "AND auctionBid.item.id IN :itemIds " +
            "ORDER BY auctionBid.amount DESC")
    List<AuctionBid> findAllBidsByAuctionIdAndItemIdsAndIsDeletedOrderByAmountDesc(
            @Param("auctionId") long auctionId,
            @Param("itemIds") List<Long> itemIds,
            @Param("isDeleted") boolean isDeleted);

    AuctionBid findFirstByAuctionIdAndItemAndIsDeletedOrderByAmountDesc(long auctionId, Item item, boolean isDeleted);
	//Not in use
	List<AuctionBid> getAllBidsByItemAndIsRefunded(Item item, boolean isRefunded);

	List<AuctionBid> getAllBidsByItemAndIsConfirmedAndIsRefundedOrderByAmountDesc(Item item, boolean confirmed, boolean isRefunded);

	List<AuctionBid> getAllBidsByItemAndIsDeletedOrderByAmountDesc(Item item, boolean isDeleted);

	List<AuctionBid> getAllBidsByItemAndIsConfirmedAndIsRefundedOrderByBidTimeDesc(Item item, boolean confirmed, boolean isRefunded);
	//Need to check: Will find URL to confirm bid with first name and last name.
	List<AuctionBid> getAllBidsByUserAndAuctionIdAndIsConfirmedAndIsRefunded(User user, long auctionId, boolean confirmed, boolean isRefunded);

	List<AuctionBid> getAllBidsByAuctionIdAndItemAndIsRefundedOrderByBidTimeDesc(long auctionId, Item item, boolean isRefunded);

	List<AuctionBid> getAllBidsByAuctionIdAndItemAndIsDeletedOrderByBidTimeDesc(long auctionId, Item item, boolean isDeleted);
	//Not in use
	List<AuctionBid> getAuctionBidByUserAndIsRefundedOrderByBidTime(User user, boolean isRefunded);
	//Not in use
	List<AuctionBid> findByAuctionIdAndIsConfirmedAndIsRefunded(long auctionId, boolean confirmed, boolean isRefunded);

	List<AuctionBid> findByAuctionIdAndIsRefunded(long auctionId, boolean isRefunded);

	List<AuctionBid> findByAuctionIdAndExportedToNeonFalse(long auctionId);

	// @Query("select ab from AuctionBid ab where ab.user = :user AND ab.amount
	// = :amount AND ab.auctionId = :auctionId AND ab.item = :item")
	// public AuctionBid findByUserAndBidAndAuctionAndItem(@Param("user") User
	// user, @Param("amount") int amount,
	// @Param("auctionId") long auctionId, @Param("item") Item item); //NOSONAR
	//Not in use
	AuctionBid findByUserAndAmountAndAuctionIdAndItemAndIsRefunded(User user, double amount, long auctionId, Item item, boolean isRefunded);

	// @Query("select ab from AuctionBid ab where ab.user = :user AND
	// ab.auctionId = :auctionId AND ab.item = :item")
	// public AuctionBid findByUserAndAuctionAndItem(@Param("user") User user,
	// @Param("auctionId") long auctionId,
	// @Param("item") Item item); //NOSONAR

	AuctionBid findFirstByItemAndUserAndAuctionIdAndIsRefundedOrderByAmountDesc(Item item, User user, long auctionId, boolean isRefunded);
	//Will check once sms checkout url is confirmed
	List<AuctionBid> findByUserAndAuctionIdAndIsRefunded(User user, long auctionId, boolean isRefunded);
	//Will check once bidder number
	@Query("SELECT auctionBid FROM AuctionBid AS auctionBid JOIN auctionBid.item AS item where auctionBid.user=:user AND "+
			"auctionBid.auctionId=:auctionId AND auctionBid.hasPaid=:hasPaid AND item.isLiveAuctionItem=:isLiveAuctionItem")
	List<AuctionBid> findByUserAndAuctionIdAndHasPaidAndIsLiveAuctionItem(@Param("user") User user,@Param("auctionId") long auctionId,
													  @Param("hasPaid") boolean hasPaid, @Param("isLiveAuctionItem") boolean isLiveAuctionItem);

	@Query(value = " select i.name,max(a.amount) as my_max_bid, max(b.amount) as current_max_bid "
			+ " from auction_bids a , auction_bids b, items i "
			+ " where a.item_id = b.item_id and a.auction_id = b.auction_id  and a.item_id = i.id "
			+ " and a.user_id = :userId and a.auction_id = :auctionId "
            + " and a.is_refunded=false and a.is_deleted=false and b.is_refunded=false and b.is_deleted=false"
			+ " group by i.id,i.name "
			+ " having max(a.amount) <= max(b.amount)"
			,nativeQuery = true)
	List<Object[]> findMaxBidForItemByUserAndAuctionId(@Param("userId") long userId, @Param("auctionId") long auctionId);

    @CacheEvict(value = "countDistinctItemsByAuctionIdAndUser" , allEntries = true)
	@Transactional
	void deleteAuctionBidsByAuctionId(long auctionId);

    @CacheEvict(value = "countDistinctItemsByAuctionIdAndUser" , allEntries = true)
	@Modifying
	@Query("DELETE FROM AuctionBid auctionBid WHERE auctionBid.item=:item")
	void deleteAuctionBidsByItem(@Param("item") Item item);

	Optional<AuctionBid> findByIdAndIsRefunded(long auctionBidId, boolean isRefunded);

    @CacheEvict(value = "countDistinctItemsByAuctionIdAndUser" , allEntries = true)
	@Modifying
	@Query("UPDATE AuctionBid SET user = :newUser WHERE user = :user")
	void updateByNewUser(@Param("user") User user, @Param("newUser") User newUser);
	//Not in use
	AuctionBid findByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(long auctionId, boolean confirmed, User user, Item item, boolean isRefunded);

	List<AuctionBid> findAllByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(long auctionId, boolean confirmed, User user,
			Item item, boolean isRefunded);

	List<AuctionBid> findAllByItemAndIsRefunded(Item item, boolean isRefunded);
	//Not in use
	@Query("SELECT user FROM AuctionBid AS auctionBid JOIN auctionBid.user AS user WHERE auctionBid.item=:item and auctionBid.isRefunded=false")
	List<User> findAllAuctionUsersByItemAndIsRefunded(@Param("item") Item item);
	//Not in use
	@Query("SELECT hasPaid FROM AuctionBid AS auctionBid WHERE auctionBid.item=:item and auctionBid.isRefunded=:isRefunded")
	Page<Boolean> findByTopNBidsByItemAndIsRefundedOrderByAmountDesc(@Param("item") Item item, Pageable pageable, @Param("isRefunded") boolean isRefunded);

	@Query("SELECT auctionBid FROM AuctionBid auctionBid WHERE auctionBid.auctionId=:auctionId and auctionBid.isRefunded=:refunded ORDER BY auctionBid.item.id DESC , auctionBid.amount DESC")
	List<AuctionBid> findByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(@Param("auctionId") long auctionId, @Param("refunded") boolean refunded);

	@Query("SELECT auctionBid FROM AuctionBid auctionBid " +
			" JOIN auctionBid.user as user" +
			" WHERE auctionBid.item=:item " +
			" AND ( auctionBid.id NOT IN (:auctionBidIds) OR 0 IN (:auctionBidIds) ) Order by auctionBid.amount DESC" )
	List<AuctionBid> findByItemOrderByAmountDesc(@Param("item") Item item, @Param("auctionBidIds") List<Long> auctionBidIds);

	@Query(" SELECT new com.accelevents.dto.AuctionBidDto(auctionBid.item.id,auctionBid.item.numberOfWinners, auctionBid.user.id,auctionBid.amount)   " +
			" FROM AuctionBid auctionBid WHERE auctionBid.auctionId=:auctionId and auctionBid.isRefunded=:refunded ORDER BY auctionBid.item.id DESC , auctionBid.amount DESC")
	List<AuctionBidDto> findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(@Param("auctionId") long auctionId, @Param("refunded") boolean refunded);

	//Not in use
	@Query("Select auctionbid From AuctionBid auctionbid JOIN auctionbid.item item WHERE auctionbid=:auction AND item.code=:itemCode")
    List<AuctionBid> findbyAuctionAndItemCode(@Param("auction")Auction auction,@Param("itemCode") String itemCode);

	Optional<AuctionBid> findById(Long auctionBidId);

    List<AuctionBid> getAllBidsByUser(User user);

    AuctionBid findFirstByAuctionIdAndItemAndUserAndHasPaidOrderByAmountDesc(Long auctionId,Item item, User user, boolean hasPaid);

    AuctionBid findTopByItemAndAmountLessThanOrderByIdDesc(Item item, double buyitNow);

	@Query("SELECT auctionBid FROM AuctionBid auctionBid JOIN auctionBid.item item JOIN auctionBid.user user " +
			" WHERE auctionBid.auctionId =:auctionId " +
			" AND auctionBid.id NOT IN (:winnerBidIds)" +
			" AND auctionBid.isRefunded=false AND auctionBid.isDeleted=false" +
			" AND item.id=:itemId " )
	Page<AuctionBid> findFirstByItemIdAndBidIdNotInAndIsNotRefunded(@Param("auctionId") long auctionId, @Param("itemId") long itemId,
																	@Param("winnerBidIds") List<Long> winnerBidIds, Pageable pageable);

	List<AuctionBid> findByAuctionId(long auctionId);
	@Query(" select new com.accelevents.auction.dto.AuctionHomeDto(count(distinct auctionBid.user), count(distinct auctionBid.item), count(distinct auctionBid.id), "
		+ " COUNT(distinct case when auctionBid.hasPaid=1 then auctionBid.item else null end)) "
		+ " from AuctionBid auctionBid "
		+ " where auctionBid.auctionId=:auctionId and auctionBid.isRefunded=false")
	AuctionHomeDto getAuctionSalesDetails(@Param("auctionId") long auctionId);

	@Query("SELECT COUNT(DISTINCT a.item) FROM AuctionBid a where a.auctionId=:auctionId and a.isRefunded=:isRefunded")
	int countDistinctItemsByAuctionIdAndIsRefunded(@Param("auctionId") long auctionId, @Param("isRefunded") boolean isRefunded);

	int countDistinctByAuctionIdAndIsRefunded(long auctionId, boolean isRefunded);

	@Query("SELECT COUNT(DISTINCT a.item) FROM AuctionBid a where a.auctionId=:auctionId and a.hasPaid=true and a.isRefunded=:isRefunded")
	int countDistinctPaidItemsByAuctionIdAndIsRefunded(@Param("auctionId") long auctionId, @Param("isRefunded") boolean isRefunded);

	@Query(" SELECT sum(COALESCE(ab.stripePaidAmount,0) - COALESCE(ab.stripeFee,0)) " +
			" FROM AuctionBid as ab " +
			" WHERE ab.id IN (:ids) AND ab.hasPaid =true and ab.isRefunded =false ")
	BigDecimal sumBidNetSale(@Param("ids") List<Long> bidIds);

	@Query(" SELECT sum(COALESCE(ab.amount,0)) " +
			" FROM AuctionBid as ab " +
			" WHERE ab.id IN (:ids) ")
	BigDecimal getSumOfAuctionBids(@Param("ids") List<Long> bidIds);

	@Query("SELECT COUNT(auctionBid.id) FROM AuctionBid as auctionBid WHERE auctionBid.auctionId = :auctionId and auctionBid.user = :user and auctionBid.item = :item")
	Integer countAuctionBidIdsByAuctionIdAndUserAndItem(@Param("auctionId") Long auctionId, @Param("user") User user, @Param("item") Item item);

	@Query("SELECT auctionBid FROM AuctionBid as auctionBid WHERE auctionBid.auctionId = :auctionId and auctionBid.user = :user and auctionBid.item IN (:itemList)")
	List<AuctionBid> countAuctionBidIdsByAuctionIdAndUserAndItemList(@Param("auctionId") Long auctionId, @Param("user") User user, @Param("itemList") List<Item> itemList);


    @Query("SELECT auctionBid FROM AuctionBid as auctionBid WHERE auctionBid.auctionId = :auctionId and auctionBid.user = :user and auctionBid.item.id IN (:itemIds)")
    List<AuctionBid> findByAuctionIdAndUserAndItemIds(@Param("auctionId") Long auctionId, @Param("user") User user, @Param("itemIds") List<Long> itemIds);


    @Cacheable(value = "countDistinctItemsByAuctionIdAndUser" , key = "#p1.userId.toString()+#p0.toString()")
    @Query("SELECT COUNT(DISTINCT auctionBid.item) FROM AuctionBid as auctionBid JOIN Item as item ON auctionBid.item = item" +
			" WHERE auctionBid.auctionId = :auctionId and auctionBid.user = :user AND item.active = true")
	Integer countDistinctItemsByAuctionIdAndUser(@Param("auctionId") Long auctionId, @Param("user") User user);

	@Query("SELECT auctionBid FROM AuctionBid auctionBid WHERE auctionBid.id IN(:auctionBidIds) and auctionBid.isRefunded = false ORDER BY auctionBid.item.id DESC , auctionBid.amount DESC")
	List<AuctionBid> findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(@Param("auctionBidIds") List<Long> auctionBidIds);

    @Query("SELECT case when count(auctionBid.user) > 0 then true else false end FROM AuctionBid auctionBid where auctionBid.user =:user and auctionBid.auctionId = :auctionId")
    boolean findRecordPresentOrNotByUserIdAndAuctionId(@Param("auctionId") Long auctionId, @Param("user") User user);

    @Query(" SELECT " +
            "new com.accelevents.perfomance.dto.ItemAuctionBidDto(item.id,item.numberOfWinners,auctionBid.id,auctionBid.user.userId,auctionBid.hasPaid) "+
            " FROM AuctionBid auctionBid RIGHT JOIN auctionBid.item item" +
            " WHERE auctionBid.auctionId=:auctionId and auctionBid.isDeleted=false AND auctionBid.isRefunded=false" +
            " AND item.moduleId=:auctionId AND item.moduleType=:moduleType AND item.active=coalesce(:active,item.active) AND " +
            " (coalesce(:searchString, 1) = 1 " +
            " OR lower(item.name) like %:searchString% " +
            " OR lower(item.code) like %:searchString% )" +
            " ORDER BY item.id ASC,auctionBid.hasPaid desc , auctionBid.amount DESC")
    List<ItemAuctionBidDto> findItemIdAndAuctionBidIdByAuctionIdAndIsDeletedFalseAndIsRefundedFalseAndItemIsActive(@Param("auctionId") long auctionId,
                                                                                                                   @Param("moduleType") ModuleType moduleType,
                                                                                                                   @Param("active") Boolean active,
                                                                                                                   @Param("searchString") String searchString);
    @CacheEvict(value = "countDistinctItemsByAuctionIdAndUser" , key = "#p0.user.userId.toString()+#p0.id.toString()")
    AuctionBid save(AuctionBid auctionBid);

}
