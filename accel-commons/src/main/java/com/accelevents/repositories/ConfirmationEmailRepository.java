package com.accelevents.repositories;

import com.accelevents.domain.CustomTemplates;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.TemplateType;
import com.accelevents.dto.CustomEmailsTemplateDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ConfirmationEmailRepository extends CrudRepository<CustomTemplates, Long> {

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.event = :event AND ct.isDefaultPage = :isDefaultEmail AND ct.templateType = :templateType ")
    CustomTemplates findByEventAndIsDefaultEmail(@Param("event") Event event, @Param("isDefaultEmail")boolean isDefaultEmail,@Param("templateType") TemplateType templateType);

    @Query(value = "SELECT ct.id FROM CustomTemplates AS ct WHERE ct.event = :event AND ct.isDefaultPage = :isDefaultEmail AND ct.templateType = :templateType")
    Long findCustomTemplatesIdByEventAndIsDefaultEmail(@Param("event") Event event,@Param("isDefaultEmail")boolean isDefaultEmail,@Param("templateType") TemplateType templateType);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.event.id = :eventId AND ct.pageName = :pageName AND ct.templateType = :templateType")
    CustomTemplates findByEventAndPageName(@Param("eventId") long eventId, @Param("pageName") String pageName,@Param("templateType") TemplateType templateType);


    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.event = :event AND ct.templateType = :templateType")
    List<CustomTemplates> findByEvent(@Param("event") Event event,@Param("templateType") TemplateType templateType);

    @Query(value = "SELECT new com.accelevents.dto.CustomEmailsTemplateDto(t.id, t.templateType, t.pageName, t.position, t.createdAt, t.updatedAt, t.isDefaultPage, t.isPageUpdated, t.subjectLine, t.bodyText, t.calendarInvitation, t.isSaveAsDraft) " +
            "FROM CustomTemplates t WHERE t.event = :event" +
            " AND (coalesce(:searchString, 1) = 1 OR t.pageName LIKE %:searchString%) AND t.templateType IN (:templateTypes) ORDER BY t.position desc ")
    Page<CustomEmailsTemplateDto> getConfirmationEmailsByEvent(@Param("event") Event event, PageRequest pageable, @Param("searchString")String search, @Param("templateTypes") List<TemplateType> templateTypes);

    @Query(value = "SELECT new com.accelevents.dto.CustomEmailsTemplateDto(t.id, t.templateType, t.pageName, t.position, t.createdAt, t.updatedAt, t.isDefaultPage, t.isPageUpdated,r.eventTicketMailResendTime, r.resendTicketStatus, t.allowedTicketTypesForReminder, r.resendTicketSubject, r.resendTicketOrderText, t.calendarInvitation, t.isSaveAsDraft,r.id) " +
            "FROM CustomTemplates t LEFT JOIN ResendTicketingEmail r ON t.id = r.customEmailId " +
            "WHERE t.event = :event " +
            "AND (coalesce(:searchString, 1) = 1 OR t.pageName LIKE CONCAT('%',:searchString,'%')) " +
            "AND t.templateType = :templateType ")
    Page<CustomEmailsTemplateDto> getReminderEmailsByEvent(@Param("event") Event event,
                                                           PageRequest pageable,
                                                           @Param("searchString") String search,
                                                           @Param("templateType") TemplateType templateType);

    @Query(value = "SELECT ct From  CustomTemplates ct where ct.id BETWEEN :from AND :to AND (ct.event.eventStatus <>'EVENT_DELETED' OR ct.event.eventStatus is NULL)")
    List<CustomTemplates> findAllByIdBetween(@Param("from") long from,@Param("to")long to);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.id in (:customTemplateIds) AND ct.eventId=:eventId")
    List<CustomTemplates> findByEventAndCustomTemplatesIdIn(long eventId, List<Long> customTemplateIds);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct " +
            " LEFT JOIN ResendTicketingEmail rte on ct.id = rte.customEmailId" +
            " WHERE ct.event = :event AND ct.templateType = :templateType and rte.resendTicketStatus='DRAFT'")
    List<CustomTemplates> findByEventAndTemplateTypeAndSaveAsDraft(@Param("event") Event event,@Param("templateType") TemplateType templateType);

    @Query(value = "SELECT ct.eventId FROM CustomTemplates AS ct WHERE ct.event IN (:events) AND ct.templateType = :templateType")
    List<Long> findEventIdByEventListAndTemplateType(@Param("events") List<Event> events, @Param("templateType") TemplateType templateType);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.eventId=:eventId")
    List<CustomTemplates> findCustomTemplatesByEvent(@Param("eventId") long eventId);

    @Query(value = "SELECT ct FROM CustomTemplates AS ct WHERE ct.event = :event AND ct.templateType = :templateType AND ct.id = :id")
    Optional<CustomTemplates> findByEventAndTemplateTypeAndId(@Param("event") Event event, @Param("templateType") TemplateType templateType, @Param("id") Long id);

}
