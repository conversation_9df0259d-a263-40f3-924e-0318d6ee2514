package com.accelevents.repositories;

import com.accelevents.domain.ReviewerFormResponse;
import com.accelevents.domain.enums.WorkflowStatus;
import com.accelevents.dto.KeyValueLongBooleanDto;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
public interface ReviewerFormResponseRepository extends CrudRepository<ReviewerFormResponse,Long> {

    @Query(value = "SELECT rfr FROM ReviewerFormResponse rfr WHERE  rfr.reviewerId=:reviewerId")
    List<ReviewerFormResponse> findAllByReviewerId(@Param("reviewerId")Long reviewerId);

    @Modifying
    @Transactional
    @Query("DELETE FROM ReviewerFormResponse rfr WHERE rfr.reviewerId = :reviewerId AND rfr.registrationRequestId IN :registrationRequestIds")
    void deleteByReviewerIdAndRegistrationRequestIds( @Param("reviewerId") Long reviewerId,
                                                        @Param("registrationRequestIds") List<Long> registrationRequestIds);

    @Modifying
    @Transactional
    @Query("DELETE FROM ReviewerFormResponse rfr WHERE rfr.reviewerId = :reviewerId")
    void deleteByReviewerId( @Param("reviewerId") Long reviewerId);

    @Query("SELECT rfr.registrationRequestId FROM ReviewerFormResponse rfr WHERE rfr.reviewerId = :reviewerId AND rfr.status <> 'RECUSED' ")
    List<Long> findRegistrationIdsByReviewerId( @Param("reviewerId") Long reviewerId);

    @Query("SELECT rfr FROM ReviewerFormResponse rfr " +
            "JOIN FETCH rfr.reviewer r " +
            "JOIN FETCH rfr.registrationRequest rr " +
            "WHERE r.eventId = :eventId " +
            "AND r.userId = :userId " +
            "AND rfr.status IN :statuses")
    List<ReviewerFormResponse> findAllByEventIdAndUserIdAndStatusIn(
            @Param("eventId") Long eventId,
            @Param("userId") Long userId,
            @Param("statuses") List<WorkflowStatus> statuses);


    @Query(" SELECT rfr FROM ReviewerFormResponse rfr " +
                  " JOIN rfr.reviewer r " +
                  " WHERE  r.userId = :userId AND rfr.id IN :ids")
    List<ReviewerFormResponse> findAllByUserIdAndIds(@Param("userId") Long userId,@Param("ids")List<Long> ids);


    @Query("SELECT rfr.reviewerId FROM ReviewerFormResponse rfr WHERE rfr.registrationRequestId = :registrationRequestId AND rfr.status <> 'RECUSED' ")
    List<Long> getReviewerIdListByRequestId( @Param("registrationRequestId") Long registrationRequestId);

    @Modifying
    @Transactional
    @Query("DELETE FROM ReviewerFormResponse rfr WHERE  rfr.reviewerId IN (:reviewerIds) AND rfr.registrationRequestId = :registrationRequestId")
    void deleteByRegistrationRequestIds(@Param("reviewerIds") List<Long> reviewerIds, @Param("registrationRequestId") Long registrationRequestId);

    @Query("SELECT rfr FROM ReviewerFormResponse rfr WHERE rfr.registrationRequestId IN :registrationRequestIds AND rfr.status <> 'RECUSED' ")
    List<ReviewerFormResponse> findAllByRegistrationRequestIds(@Param("registrationRequestIds") List<Long> registrationRequestIds);

    @Query("SELECT rfr FROM ReviewerFormResponse rfr JOIN FETCH rfr.reviewer r WHERE rfr.registrationRequestId = :registrationRequestId")
    List<ReviewerFormResponse> findAllByRegistrationRequestId(@Param("registrationRequestId") Long registrationRequestId);

    @Modifying
    @Transactional
    @Query("DELETE FROM ReviewerFormResponse rfr WHERE rfr.registrationRequestId IN :registrationRequestIds")
    void deleteByRegistrationRequestIds(@Param("registrationRequestIds") List<Long> registrationRequestIds);


    @Modifying
    @Transactional
    @Query("DELETE FROM ReviewerFormResponse rfr WHERE rfr.reviewerId IN :reviewerIds")
    void deleteByReviewerIds(@Param("reviewerIds") List<Long> reviewerIds);

    @Query("SELECT rfr FROM ReviewerFormResponse rfr " +
            "JOIN FETCH rfr.reviewer r " +
            "JOIN FETCH rfr.registrationRequest rr " +
            "WHERE r.id = :id ")
    List<ReviewerFormResponse> findAllById(@Param("id") Long id);

    @Query("SELECT new com.accelevents.dto.KeyValueLongBooleanDto(" +
            "rfr.registrationRequest.id, " +
            "CASE WHEN COUNT(rfr) > 0 AND COUNT(rfr) = SUM(CASE WHEN rfr.status = 'COMPLETED' THEN 1 ELSE 0 END) " +
            "THEN true ELSE false END) " +
            "FROM ReviewerFormResponse rfr " +
            "WHERE rfr.registrationRequest.id IN :registrationRequestIds " +
            "AND rfr.status <> 'RECUSED' " +
            "GROUP BY rfr.registrationRequest.id")
    List<KeyValueLongBooleanDto> getAllRegistrationRequestIdWithIsAllCompleted(@Param("registrationRequestIds") Set<Long> registrationRequestIds);



    @Query(" SELECT DISTINCT(rfr.registrationRequestId) FROM ReviewerFormResponse rfr " +
           " WHERE rfr.reviewerId=:reviewerId")
    List<Long> getAllRegistrationRequestIdByReviewerId(@Param("reviewerId")Long reviewerId);

    @Query(value = "SELECT rfr.status, rfr.registrationRequestId " +
            "FROM ReviewerFormResponse rfr " +
            "WHERE rfr.reviewerId = :reviewerId")
    List<Object[]> findRegistrationIdsGroupedByStatus(@Param("reviewerId") Long reviewerId);

}
