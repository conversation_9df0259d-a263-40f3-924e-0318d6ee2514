package com.accelevents.repositories;

import com.accelevents.domain.ContactModuleSettings;
import com.accelevents.domain.Event;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ContactModuleSettingsRepository extends CrudRepository<ContactModuleSettings, Long> {

    Optional<ContactModuleSettings> findByEvent(Event event);

    @Query("SELECT contactModuleSettings FROM ContactModuleSettings contactModuleSettings " +
            "JOIN Event event ON contactModuleSettings.event=event " +
            "JOIN Ticketing ticketing on event.eventId=ticketing.eventid.eventId " +
            "WHERE (:whiteLabelId IS NULL OR event.whiteLabelId=:whiteLabelId) " +
            "AND (:organizerId IS NULL OR event.organizerId=:organizerId) " +
            "AND ticketing.eventEndDate >= now()")
    List<ContactModuleSettings> findAllByWhiteLabelIdOrOrganiserId(@Param("whiteLabelId") Long whiteLabelId, @Param("organizerId") Long organizerId);
}
