package com.accelevents.repositories;

import com.accelevents.domain.WaitList;
import com.accelevents.dto.CountByTicketType;
import com.accelevents.dto.WaitListUpdateDto;
import com.accelevents.messages.WaitListStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WaitListRepository extends CrudRepository<WaitList, Long> {

    List<WaitList> findByEventId(long eventId);

    Page<WaitList> findByEventIdOrderByPositionAsc(long eventId, Pageable pageable);

    WaitList findByWaitListIdAndEventId(long waitListId, long eventId);

    List<WaitList> findByEventIdInAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(List<Long> eventId, WaitListStatus waitListStatus, boolean emailSent);

    List<WaitList> findByEventIdAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(long eventId, WaitListStatus waitListStatus, boolean emailSent);

    List<WaitList> findByStatus(WaitListStatus waitListStatus);

    List<WaitList> findByEventIdAndStatus(long eventId, WaitListStatus waitListStatus);

    int countByEventIdAndStatus(Long eventId, WaitListStatus waitListStatus);

    List<WaitList> findByEventIdAndStatusAndEmail(long eventId, WaitListStatus waitListStatus, String email);

    long countAllByEventIdAndStatus(long eventId, WaitListStatus status);

    List<WaitList> findByEventIdAndAndStatusAndWaitListIdIn(long eventId, WaitListStatus status, List<Long> waitListIds);

    @Modifying
    @Query("update WaitList set emailSent=true where waitListId in (:waitListIds)")
    void markWaitListAsPaidWhereIdsIn(@Param("waitListIds") List<Long> waitListIds);

    List<WaitList> findByEventIdAndStatusAndEmailSent(long eventId, WaitListStatus waitListStatus, boolean isEmailSent);

    List<WaitList> findByEventIdAndStatusAndEmailSentOrderByPositionAsc(long eventId, WaitListStatus waitListStatus, boolean isEmailSent);

    List<WaitList> findByEventIdAndStatusAndTicketingTypeId(long eventId, WaitListStatus waitListStatus, long waitListTrigger);

    @Query("select new com.accelevents.dto.CountByTicketType(wl.ticketingTypeId, count(wl)) from WaitList wl where wl.eventId=:eventId and wl.status = :waitListStatus and wl.ticketingTypeId in (:ticketTypeIds) group by wl.ticketingTypeId")
    List<CountByTicketType> countByEventIdAndStatusAndTicketingTypeIdIn(@Param("eventId") Long eventId, @Param("waitListStatus") WaitListStatus waitListStatus, @Param("ticketTypeIds") List<Long> ticketTypeId);

    int countByEventIdAndStatusAndEmailSentAndTicketingTypeId(long eventId, WaitListStatus waitListStatus, boolean isEmailSent, long ticketingTypeId);

    int countByEventIdAndStatusAndEmailSent(long eventId, WaitListStatus waitListStatus, boolean isEmailSent);

    List<WaitList> findByEventIdAndStatusAndEmailAndTicketingTypeId(long eventId, WaitListStatus waitListStatus, String email, long ticketingTypeId);

    List<WaitList> findByEventIdAndTicketingTypeIdAndStatusAndWaitListIdIn(long eventId, long ticketTypeId, WaitListStatus waitListStatus, List<Long> waitListIds);

    List<WaitList> findByEventIdAndStatusAndTimeTicketsBecameAvailableIsNullAndEmailSent(long eventId, WaitListStatus waitListStatus, boolean emailSent);

    List<WaitList> findByEventIdAndStatusAndTimeTicketsBecameAvailableIsNullAndEmailSentAndTicketingTypeId(long eventId, WaitListStatus waitListStatus, boolean emailSent, long ticketingTypeId);

    List<WaitList> findByEventIdAndStatusAndTimeTicketsBecameAvailableIsNullAndEmailSentAndTicketingTypeIdOrderByPositionAsc(long eventId, WaitListStatus waitListStatus, boolean emailSent, long ticketingTypeId);

    @Query("Select w From WaitList as w where w.id  IN (:waitListIds) and w.status =:waitingStatus")
    List<WaitList> findByListOfWaitListIdsAndStatusAndEmailSent(@Param("waitListIds") List<Long> waitListIds,@Param("waitingStatus") WaitListStatus waitingStatus);

    @Query("Select w From WaitList as w where w.id  IN (:waitListIds) and w.status =:waitingStatus order by w.position asc")
    List<WaitList> findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(@Param("waitListIds") List<Long> waitListIds,@Param("waitingStatus") WaitListStatus waitingStatus);

    @Modifying
    @Query("UPDATE WaitList SET status = :status WHERE eventId = :eventId")
    void updateStatusDeleted(@Param("eventId") Long eventId, @Param("status") WaitListStatus status);

    @Modifying
    @Query("UPDATE WaitList SET status = :status WHERE eventId IN (:eventIds) ")
    void updateStatusDeletedByEventIds(@Param("eventIds") List<Long> eventIds, @Param("status") WaitListStatus status);
    @Query("SELECT w FROM WaitList as w WHERE w.email=:email")
    List<WaitList> findWaitListByEmail(@Param("email")String email);

    List<WaitList> findByEventIdOrderByPositionAsc(Long eventId);

    boolean existsByEventIdAndPosition(Long eventId, double position);

    @Modifying
    @Query("UPDATE WaitList w SET w.position = w.position + :shift " +
            "WHERE w.eventId = :eventId AND w.position >= :fromPosition")
    void shiftPositions(@Param("eventId") Long eventId,
                        @Param("fromPosition") double fromPosition,
                        @Param("shift") double shift);

    @Query("SELECT COALESCE(MAX(w.position), 0) FROM WaitList w WHERE w.eventId = :eventId")
    Double findMaxPositionByEventId(@Param("eventId") Long eventId);

}