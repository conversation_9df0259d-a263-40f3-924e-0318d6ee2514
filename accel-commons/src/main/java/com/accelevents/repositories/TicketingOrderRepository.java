package com.accelevents.repositories;

import com.accelevents.common.dto.AccessCodeDto;
import com.accelevents.dto.EventMyTicketOrderDto;
import com.accelevents.dto.EventTicketOrderDto;
import com.accelevents.dto.EventTicketingAndEventDesignDetailDto;
import com.accelevents.domain.*;
import com.accelevents.domain.TicketingOrder.TicketingOrderStatus;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface TicketingOrderRepository extends CrudRepository<TicketingOrder, Long> {

	TicketingOrder findByIdAndEventid(long id, Event event);

	List<TicketingOrder> findByTicketingCouponAndEventid(TicketingCoupon ticketingCoupon, Event event);

	List<TicketingOrder> findByTicketingCouponAndEventidAndPurchaser(TicketingCoupon ticketingCoupon, Event event, User purchaser);

	@Modifying
	@Query("UPDATE TicketingOrder SET purchaser = :newUser WHERE purchaser = :user")
	void updateByNewUser(@Param("user") User user, @Param("newUser") User newUser);

	@Modifying
	@Query("UPDATE TicketingOrder SET exitIntentPopupTriggered =:exitIntentPopupTriggered WHERE id =:orderId and eventid=:event")
	void updateExitIntenetTrigger(@Param("exitIntentPopupTriggered") boolean exitIntentPopupTriggered,@Param("event") Event event, @Param("orderId") long orderId);


	@Query(value = "SELECT DISTINCT ticketOrder " +
			" FROM EventTickets AS eventTicket " +
			" JOIN eventTicket.ticketingOrder AS ticketOrder JOIN ticketOrder.purchaser AS user " +
			" LEFT JOIN eventTicket.ticketHolderAttributesId ticketHolder" +
			" WHERE ticketOrder.eventid=:event " +
			" AND ticketOrder.status IN (:status) " +
			" AND (:searchDate is null OR DATE(ticketOrder.orderDate) =:searchDate) " +
			" AND (:dataType is null OR eventTicket.dataType =:dataType) " +
			" AND ( eventTicket.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (:formattedDate IS NULL OR (eventTicket.createdAt >= :formattedDate OR eventTicket.updatedAt >= :formattedDate)) " +
            " AND (user.email LIKE %:search% OR user.firstName LIKE %:search% OR user.lastName  LIKE %:search% OR CONCAT(user.firstName,' ',user.lastName) LIKE %:search% OR CAST(ticketOrder.id as string) LIKE %:search%" +
            " OR eventTicket.seatNumber LIKE %:search% " +
            " OR CONCAT(eventTicket.holderFirstName,' ',eventTicket.holderLastName) LIKE %:search% " +
			" OR eventTicket.holderFirstName LIKE %:search% " +
			" OR eventTicket.holderLastName LIKE %:search% " +
			" OR eventTicket.holderEmail LIKE %:search% " +
            " OR (eventTicket.id LIKE %:search% AND eventTicket.ticketStatus <> 'DELETED')" +
			" OR eventTicket.holderPhoneNumber LIKE %:search% ) order by ticketOrder.orderDate desc ",
			countQuery = "SELECT count(DISTINCT ticketOrder.id) FROM EventTickets AS eventTicket  " +
					"JOIN eventTicket.ticketingOrder AS ticketOrder JOIN ticketOrder.purchaser AS user  " +
					"LEFT JOIN eventTicket.ticketHolderAttributesId ticketHolder WHERE ticketOrder.eventid=:event " +
					"AND (:searchDate is null OR DATE(ticketOrder.orderDate) =:searchDate ) " +
					"AND (:dataType is null OR eventTicket.dataType =:dataType) " +
					"AND ticketOrder.status IN (:status)  AND ( eventTicket.recurringEventId = :recurringEventId OR 0 = :recurringEventId )  " +
                    "AND (:formattedDate IS NULL OR (eventTicket.createdAt >= :formattedDate OR eventTicket.updatedAt >= :formattedDate)) " +
                    "AND (user.email LIKE %:search% OR user.firstName LIKE %:search% OR user.lastName  LIKE %:search% OR CONCAT(user.firstName,' ',user.lastName) LIKE %:search% OR CAST(ticketOrder.id as string) " +
                    "LIKE %:search% OR eventTicket.seatNumber LIKE %:search%  " +
                    "OR CONCAT(eventTicket.holderFirstName,' ',eventTicket.holderLastName) LIKE %:search% " +
                    "OR eventTicket.holderFirstName LIKE %:search%  " +
                    "OR (eventTicket.id LIKE %:search% AND eventTicket.ticketStatus <> 'DELETED')" +
					"OR eventTicket.holderLastName LIKE %:search%  OR eventTicket.holderEmail LIKE %:search%  OR eventTicket.holderPhoneNumber LIKE %:search% )")
	Page<TicketingOrder> findByEventidAndStatusOrOrderType(@Param("event") Event event,
														   @Param("status") List<TicketingOrderStatus> status,
														   @Param("search") String search, Pageable pageable,
														   @Param("recurringEventId") Long recurringEventId,
														   @Param("searchDate") Date searchDate,
														   @Param("dataType") DataType dataType,
                                                           @Param("formattedDate")Date formattedDate);


	@Query(value = " SELECT DISTINCT ticketOrder " +
			" FROM EventTickets AS eventTicket " +
			" JOIN eventTicket.ticketingOrder AS ticketOrder "+
			" WHERE ticketOrder.eventid=:event " +
			" AND (:searchDate is null OR DATE(ticketOrder.orderDate) =:searchDate) " +
			" AND (:dataType is null OR eventTicket.dataType =:dataType) " +
			" AND ticketOrder.status IN (:status) " +
			" AND ( eventTicket.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (:formattedDate IS NULL OR (eventTicket.createdAt >= :formattedDate OR eventTicket.updatedAt >= :formattedDate)) " +
			" ORDER BY ticketOrder.orderDate desc ",countQuery ="SELECT count(DISTINCT ticketOrder.id)" +
			" FROM EventTickets AS eventTicket " +
			" JOIN eventTicket.ticketingOrder AS ticketOrder "+
			" WHERE ticketOrder.eventid=:event " +
			" AND (:searchDate is null OR DATE(ticketOrder.orderDate) =:searchDate) " +
			" AND (:dataType is null OR eventTicket.dataType =:dataType) " +
			" AND ticketOrder.status IN (:status) " +
			" AND ( eventTicket.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND (:formattedDate IS NULL OR (eventTicket.createdAt >= :formattedDate OR eventTicket.updatedAt >= :formattedDate)) ")
	Page<TicketingOrder> findByEventidAndStatusOrOrderTypeOrderByOrderDateDesc(@Param("event") Event event,
																			   @Param("status") List<TicketingOrderStatus> status,
																			   Pageable pageable,
																			   @Param("recurringEventId") Long recurringEventId,
																			   @Param("searchDate") Date searchDate,
																			   @Param("dataType")DataType dataType,
                                                                               @Param("formattedDate") Date formattedDate);
    

    List<TicketingOrder> findByTrackingLinks(TrackingLinks trackingLinks);

	List<TicketingOrder> findByEventid(Event event);

	@Query("SELECT ticketingOrder FROM TicketingOrder AS ticketingOrder " +
			"WHERE ticketingOrder.purchaser=:purchaser AND ticketingOrder.eventid=:event AND ticketingOrder.status=:create")
	List<TicketingOrder> findByPurchaseridAndEventidAndOrderStatus(@Param("event") Event event,@Param("purchaser") User purchaser,
																   @Param("create") TicketingOrderStatus create);

	List<TicketingOrder> findByEventidAndExportedToNeonFalse(Event event);

	Optional<TicketingOrder> findFirstByEventidAndStatusOrderByOrderDateAsc(Event event, TicketingOrderStatus status);

	@Modifying
	void deleteByIdIn(List<Long> orderIdsToBeDelete);

	@Query("SELECT ticketingOrder FROM TicketingOrder ticketingOrder LEFT JOIN FETCH ticketingOrder.purchaser JOIN FETCH ticketingOrder.staffUserId WHERE ticketingOrder.id =:orderId")
        TicketingOrder findOrderAndFetchByUserAndStaff(@Param("orderId") long orderId);
	
        @Query("Select order From TicketingOrder order WHERE order.ticketingCoupon IN (:couponList) AND order.eventid.eventId=:eventId")
        List<TicketingOrder> findByTicketingCouponInAndEventId(@Param("couponList") List<TicketingCoupon> couponList,
														   @Param("eventId") Long eventId);

	@Modifying
	@Query("UPDATE TicketingOrder SET buyerEmail = :email WHERE id IN (:orderIds)")
	void updateTicketingOrderbuyerEmailByOrderIds(@Param("orderIds") List<Long> orderIds, @Param("email") String email);

	@Query("SELECT ticketingOrder FROM TicketingOrder ticketingOrder WHERE ticketingOrder.eventid.eventId=:eventId AND ticketingOrder.status=:status ORDER BY ticketingOrder.id DESC")
	List<TicketingOrder> findByEventIdOrderByIdDesc(@Param("eventId") Long eventId, @Param("status") TicketingOrderStatus status, Pageable pageable);

    @Query("SELECT count(DISTINCT ticketingOrder.id) FROM TicketingOrder ticketingOrder WHERE ticketingOrder.eventid.eventId=:eventId AND ticketingOrder.ticketingAccessCodeId=:accessCodeId AND" +
            "(ticketingOrder.status = 'PAID' OR (ticketingOrder.status = 'CREATE' AND ticketingOrder.expirein >:date)) " )
    Integer getAccessCodeUsed(@Param("eventId") Long eventId, @Param("accessCodeId")Long accessCodeId ,@Param("date") Date date);

    @Query(value = "SELECT new com.accelevents.dto.EventTicketOrderDto(" +
            " ticketingOrder.id," +
            " ticketingOrder.eventid.eventId," +
            " eventTicket.id," +
            " eventTicket.seatNumber, " +
            " eventTicket.ticketStatus, " +
            " eventTicket.ticketPaymentStatus," +
            " eventTicket.paidAmount," +
            " eventTicket.refundedAmount," +
            " ticketingOrder.orderDate, ticketingType.id, ticketingType.ticketTypeName, ticketingType.dataType, ticketingType.price," +
            "(CASE WHEN ticketingType.nonTransferable is true THEN ticketingType.nonTransferable ELSE eventTicket.nonTransferable end),ticketingOrder.disableTicketTransfer) " +
            " FROM EventTickets AS eventTicket " +
            " JOIN eventTicket.ticketingOrder AS ticketingOrder" +
            " JOIN eventTicket.ticketingTypeId AS ticketingType " +
            " JOIN ticketingType.ticketing AS ticketing" +
            " WHERE ( eventTicket.holderUserId=:purchaser OR eventTicket.ticketPurchaserId=:purchaser ) " +
            " AND ticketingOrder.status=:status " +
            " AND ticketing.eventEndDate <= now() " +
            " ORDER BY ticketingOrder.id DESC",
            countQuery = "SELECT count( distinct ticketingOrder.id ) " +
                    " FROM EventTickets AS eventTicket " +
                    " JOIN eventTicket.ticketingOrder AS ticketingOrder" +
                    " JOIN eventTicket.ticketingTypeId AS ticketingType " +
                    " JOIN ticketingType.ticketing AS ticketing" +
                    " WHERE ( eventTicket.holderUserId=:purchaser OR eventTicket.ticketPurchaserId=:purchaser ) " +
                    " AND ticketingOrder.status=:status " +
                    " AND ticketing.eventEndDate <= now()")
    Page<EventTicketOrderDto> findPastOrderByPurchaserAndStatus(@Param("purchaser") User purchaser,
                                                                @Param("status") TicketingOrderStatus status,
                                                                Pageable pageable);

   @Query("SELECT new com.accelevents.dto.EventTicketingAndEventDesignDetailDto(" +
            " event.eventId," +
            " event.name," +
            " event.eventURL," +
            " event.currency," +
            " ticketing.eventStartDate, " +
            " ticketing.eventEndDate, " +
            " ticketing.collectTicketHolderAttributes," +
            " ticketing.eventAddress," +
            " eventDesignDetail.logoImage, event.hideEventDate, ticketing.preEventAccessMinutes, event.eventFormat, event.equivalentTimeZone, ticketing.enableOrderConfirmationEmail) " +
            " FROM Ticketing AS ticketing " +
            " JOIN ticketing.eventid AS event" +
            " JOIN EventDesignDetail as eventDesignDetail on eventDesignDetail.event=event "+
            " WHERE event.eventId IN (:eventIds)" +
           " AND (event.eventStatus<>'EVENT_DELETED' or event.eventStatus is null)")
    List<EventTicketingAndEventDesignDetailDto> findEventsTickitingAndEventDesignDetailsByEventIds(@Param("eventIds") List<Long> eventIds);

    @Query("SELECT new com.accelevents.dto.EventTicketOrderDto(" +
            " ticketingOrder.id," +
            " ticketingOrder.eventid.eventId," +
            " eventTicket.id," +
            " eventTicket.seatNumber, " +
            " eventTicket.ticketStatus, " +
            " eventTicket.ticketPaymentStatus," +
            " eventTicket.paidAmount," +
            " eventTicket.refundedAmount," +
            " ticketingOrder.orderDate, ticketingType.id, ticketingType.ticketTypeName, " +
            "ticketingType.dataType, ticketingType.price, ticketingType.bundleType," +
            "(CASE WHEN ticketingType.nonTransferable is true THEN ticketingType.nonTransferable ELSE eventTicket.nonTransferable end),ticketingOrder.disableTicketTransfer) " +
            " FROM EventTickets AS eventTicket " +
            " JOIN eventTicket.ticketingOrder AS ticketingOrder" +
            " JOIN eventTicket.ticketingTypeId AS ticketingType " +
            " JOIN ticketingType.ticketing AS ticketing" +
            " WHERE  ( eventTicket.holderUserId=:purchaser OR eventTicket.ticketPurchaserId=:purchaser ) " +
            " AND ticketingOrder.status in (:status) " +
            " AND ticketingOrder.id in (:orderId) " +
            " AND ticketing.eventEndDate > now() " +
            " ORDER BY ticketingOrder.id DESC")
        List<EventTicketOrderDto> findActiveOrderByPurchaserAndOrderId(@Param("purchaser")User purchaser,
                                                                   @Param("status") List<TicketingOrderStatus> status,
                                                                   @Param("orderId") List<Long> orderId);

    @Query("SELECT distinct ticketingOrder.id" +
            " FROM EventTickets AS eventTicket " +
            " JOIN eventTicket.ticketingOrder AS ticketingOrder" +
            " JOIN eventTicket.ticketingTypeId AS ticketingType " +
            " JOIN ticketingType.ticketing AS ticketing" +
            " WHERE  ( eventTicket.holderUserId=:purchaser OR eventTicket.ticketPurchaserId=:purchaser ) " +
            " AND ticketingOrder.status in (:status) " +
            " AND ticketing.eventEndDate > now() " +
            " ORDER BY ticketingOrder.id DESC")
    Page<Long> findActiveOrderIdsByPurchaser(@Param("purchaser") User purchaser,
                            @Param("status") List<TicketingOrderStatus> status,
                            Pageable pageable);

    @Query("SELECT new com.accelevents.dto." +
			"EventMyTicketOrderDto(" +
            " ticketingOrder.id," +
            " ticketingOrder.eventid.eventId," +
            " eventTicket.id," +
            " eventTicket.holderFirstName, " +
            " eventTicket.holderLastName, " +
            " eventTicket.holderEmail, " +
            " ticketingOrder.eventid.name, " +
            " ticketingType.ticketTypeName, " +
            " ticketing.eventStartDate, " +
            " ticketing.eventEndDate, " +
            " ticketing.eventAddress, " +
            " eventTicket.barcodeId," +
            " eventTicket.guestOfBuyer, " +
            " eventTicket.ticketPaymentStatus," +
			"eventTicket.dataType) " +
            " FROM EventTickets AS eventTicket " +
            " JOIN eventTicket.ticketingOrder AS ticketingOrder" +
            " JOIN eventTicket.ticketingTypeId AS ticketingType " +
            " JOIN ticketingType.ticketing AS ticketing" +
            " WHERE eventTicket.holderUserId=:user " +
            " AND ticketingOrder.status IN (:ticketingOrderStatusList) " +
            " AND ticketingOrder.eventid=:event " +
            " AND eventTicket.ticketStatus <> 'DELETED' " +
            " AND ticketingType.ticketType <> 'DONATION' " +
            " ORDER BY ticketingOrder.id DESC")
    List<EventMyTicketOrderDto> findHolderByEventAndHolderUser(@Param("user")User user,
                                                               @Param("ticketingOrderStatusList") List<TicketingOrderStatus> ticketingOrderStatusList, @Param("event") Event event);

    @Query(value = "SELECT eto.id AS id, eto.event_id AS eventId, et.id AS ticketId, et.h_first_name AS holderFirstName, " +
            "et.h_last_name AS holderLastName, et.h_email AS holderEmail, e.name AS eventName, " +
            "tt.ticket_type_name AS ticketTypeName, t.event_start_date AS eventStartDate, " +
            "t.event_end_date AS eventEndDate, t.event_address AS eventAddress, et.barcode_id AS barcodeId, " +
            "et.guest_of_buyer AS guestOfBuyer, et.ticket_payment_status AS ticketPaymentStatus, " +
            "et.data_type AS dataType " +
            "FROM event_tickets et " +
            "JOIN ticketing_order eto ON et.ticket_order_id = eto.id " +
            "JOIN event_ticket_type tt ON et.ticketing_type_id = tt.id " +
            "JOIN ticketing t ON tt.ticketing_id = t.id " +
            "JOIN events e ON eto.event_id = e.event_id " +
            "LEFT JOIN event_tickets addon_et ON addon_et.id = et.ticket_id_for_addon " +
            "LEFT JOIN event_ticket_type addon_tt ON addon_et.ticketing_type_id = addon_tt.id " +
            "WHERE et.holder_user_id = :userId " +
            "AND eto.order_status IN (:ticketingOrderStatusList) " +
            "AND eto.event_id = :eventId " +
            "AND et.ticket_status <> 'DELETED' " +
            "AND tt.ticket_type <> 'DONATION' " +
            "AND (addon_tt.ticket_type IS NULL OR addon_tt.ticket_type <> 'DONATION') " +
            "ORDER BY eto.id DESC", nativeQuery = true)
    List<Object[]> findHolderByEventAndHolderUserIgnoreDonation(
            @Param("userId") Long userId,
            @Param("ticketingOrderStatusList") List<String> ticketingOrderStatusList,
            @Param("eventId") Long eventId);


    @Query("SELECT new com.accelevents.dto.EventTicketOrderDto(" +
            " ticketingOrder.id," +
            " ticketingOrder.eventid.eventId," +
            " eventTicket.id," +
            " eventTicket.seatNumber, " +
            " eventTicket.ticketStatus, " +
            " eventTicket.ticketPaymentStatus," +
            " eventTicket.paidAmount," +
            " eventTicket.refundedAmount," +
            " ticketingOrder.orderDate , ticketingType.id, ticketingType.ticketTypeName, ticketingType.dataType, ticketingType.price) " +
            " FROM EventTickets AS eventTicket " +
            " JOIN eventTicket.ticketingOrder AS ticketingOrder" +
            " JOIN eventTicket.ticketingTypeId AS ticketingType " +
            " JOIN ticketingType.ticketing AS ticketing" +
            " WHERE eventTicket.holderEmail=:holderEmail " +
            " AND ticketingOrder.status=:status " +
            " AND ticketingOrder.id = coalesce(:orderId, ticketingOrder.id) " +
            " AND ticketing.eventEndDate > now() " +
            " ORDER BY ticketingOrder.id DESC")
    Page<EventTicketOrderDto> getUserActiveOrdersByOrderIdAndHolderEmail(@Param("orderId") Long orderId,
                                                                         @Param("status") TicketingOrderStatus status,
                                                                         @Param("holderEmail") String holderEmail,
                                                                         Pageable pageable);

    @Query( "SELECT ticketingOrder FROM TicketingOrder AS ticketingOrder JOIN FETCH ticketingOrder.eventid As event JOIN FETCH ticketingOrder.purchaser AS " +
            "purchaser WHERE purchaser.userId=:purchaser AND ticketingOrder.status <> 'PAID_DELETE' AND ( event.eventStatus is null or  event.eventStatus <> 'EVENT_DELETED')")
    List<TicketingOrder> getOrderByPurchaseridAndRecordStatusNotDeleted(@Param("purchaser") long purchaser);

    @Modifying
    @Query("UPDATE TicketingOrder SET recordStatus = :status WHERE id IN (:orderIds)")
    void updateTicketingOrderRecStatusByOrderIds(@Param("orderIds") List<Long> orderIds, @Param("status") RecordStatus status);

    @Query("SELECT ticketingOrder FROM TicketingOrder AS ticketingOrder WHERE ticketingOrder.id=:orderId AND ticketingOrder.purchaser.userId=:userId")
    TicketingOrder findByIdAndPurchaserId(@Param("userId") Long userId, @Param("orderId") long orderId);


    @Query("Select ticketingOrder FROM TicketingOrder AS ticketingOrder WHERE ticketingOrder.eventid=:event AND ticketingOrder.status IN ('CREATE','EXPIRED')")
    List<TicketingOrder> findListByEventIdAndStatus(@Param("event") Event event);

    @Query("SELECT ticketOrder FROM EventTickets AS eventTicket JOIN eventTicket.ticketingOrder AS ticketOrder WHERE ticketOrder.eventid=:event AND ticketOrder.status IN (:status) AND ( eventTicket.recurringEventId = :recurringEventId OR 0 = :recurringEventId) GROUP BY eventTicket.holderUserId ORDER BY ticketOrder.id DESC")
    Page<TicketingOrder> findListByEventIdAndNotDeletedStatus(@Param("event") Event event,@Param("status") List<TicketingOrderStatus> status,@Param("recurringEventId") Long recurringEventId, Pageable pageable);

    @Query(value = "SELECT count(DISTINCT eventTicket.holderUserId) FROM EventTickets AS eventTicket JOIN eventTicket.ticketingOrder AS ticketOrder WHERE ticketOrder.eventid=:event AND ticketOrder.status IN (:status) AND ( eventTicket.recurringEventId = :recurringEventId OR 0 = :recurringEventId)")
    Long getUniqueRegisteredUserCount(@Param("event") Event event,@Param("status") List<TicketingOrderStatus> status,@Param("recurringEventId") Long recurringEventId);

    @Query("SELECT new com.accelevents.common.dto.AccessCodeDto(ticketingOrder.ticketingAccessCodeId,count(DISTINCT ticketingOrder.id)) FROM TicketingOrder ticketingOrder WHERE ticketingOrder.eventid.eventId=:eventId AND ticketingOrder.ticketingAccessCodeId IN (:accessCodeId) AND" +
            "(ticketingOrder.status = 'PAID' OR (ticketingOrder.status = 'CREATE' AND  ticketingOrder.expirein > CURRENT_TIMESTAMP()))  GROUP BY ticketingOrder.ticketingAccessCodeId" )
    List<AccessCodeDto> getAccessCodeForUsed(@Param("eventId") Long eventId, @Param("accessCodeId")List<Long> accessCodeId);

    @Query("SELECT DISTINCT tord FROM TicketingOrder tord WHERE tord.id in :orderIds")
    List<TicketingOrder> findByOrderIds(@Param("orderIds") List<Long> orderIds);

    @Query("SELECT DISTINCT tord FROM TicketingOrder tord WHERE tord.eventid in (:eventIds) and tord.status in (:status)")
    List<TicketingOrder> findOrdersByEventIds(@Param("eventIds") List<Event>  eventIds, @Param("status") List<TicketingOrderStatus> status);

    @Query("SELECT DISTINCT tord FROM TicketingOrder tord WHERE tord.eventid in (:eventIds) and tord.orderType =:orderType")
    List<TicketingOrder> findOrdersByEventIdsAndOrderType(@Param("eventIds") List<Event>  eventIds, @Param("orderType") TicketingOrder.OrderType orderType);

    @Query("SELECT ticketOrder FROM EventTickets AS eventTicket JOIN eventTicket.ticketingOrder AS ticketOrder " +
            " WHERE ticketOrder.id=:orderId " +
            " AND (ticketOrder.purchaser.userId=:userId OR eventTicket.holderUserId.userId=:userId)")
    TicketingOrder findByIdAndPurchaserIdOrHolderUserId(@Param("userId") Long userId, @Param("orderId") long orderId);

    @Query(value = " SELECT DISTINCT ticketOrder " +
            " FROM EventTickets AS eventTicket " +
            " JOIN eventTicket.ticketingOrder AS ticketOrder "+
            " WHERE ticketOrder.eventid=:event " +
            " AND ticketOrder.status IN (:status) " +
            " AND ( eventTicket.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " ORDER BY ticketOrder.orderDate desc ")
    List<TicketingOrder> findAllByEventIdAndStatusOrOrderTypeOrderByOrderDateDesc(@Param("event") Event event,
                                                                               @Param("status") List<TicketingOrderStatus> status,
                                                                               @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT ticketOrder FROM TicketingOrder ticketOrder WHERE ticketOrder.eventid=:event AND ticketOrder<>'PAID' AND ticketOrder.id BETWEEN :from AND :to")
    List<TicketingOrder> findAllByEventIdAndOrderIdFromTo(@Param("event") Event event, @Param("from") Long from, @Param("to") Long to);

    @Query("select t.status from TicketingOrder t " +
            "join EventTickets et on t.id=et.ticketingOrderId " +
            "where t.orderType='PAY_LATER' and t.eventid=:eventId and et.ticketPurchaserId=:purchaserId")
    TicketingOrderStatus findTicketOrderStatusBasedOnUserIdAndEventId(@Param("eventId") Event eventId, @Param("purchaserId") User purchaserId);

    @Modifying
    @Query("UPDATE TicketingOrder SET status = :orderStatus WHERE id = :orderId")
    void updateOrderStatusBasedOnStripeWebHook(@Param("orderId") Long orderId, @Param("orderStatus") TicketingOrderStatus orderStatus);


    @Query(" SELECT DISTINCT ticketOrder.id " +
            " FROM TicketingOrder AS ticketOrder " +
            " WHERE ticketOrder.eventid=:event " +
            " AND ticketOrder.ticketingCoupon=:ticketingCoupon " +
            " AND ticketOrder.purchaser=coalesce(:purchaser, ticketOrder.purchaser) " +
            " AND (ticketOrder.status IN ('PAID','UNPAID','PARTIAL') OR (ticketOrder.status = 'CREATE' AND ticketOrder.expirein > now()))")
    List<Long> findListByTicketingCouponAndEventidAndPurchaser(@Param("ticketingCoupon") TicketingCoupon ticketingCoupon,
                                                               @Param("event") Event event,
                                                               @Param("purchaser") User purchaser);

    @Query("SELECT ticketOrder FROM TicketingOrder ticketOrder WHERE ticketOrder.eventid=:event AND ticketOrder.id BETWEEN :from AND :to AND ticketOrder.status IN (:status) ")
    List<TicketingOrder> findAllOrdersByEventIdAndOrderIdFromTo(@Param("event") Event event, @Param("from") Long from, @Param("to") Long to, @Param("status") List<TicketingOrderStatus> status);

    @Query("SELECT ticketOrder FROM TicketingOrder ticketOrder WHERE ticketOrder.id IN (:orderIds) AND ticketOrder.status NOT IN (:status) ")
    List<TicketingOrder> findAllOrdersByOrderIdsAndOrderStatusNotIn(@Param("orderIds") List<Long> orderIds, @Param("status") List<TicketingOrderStatus> status);
}