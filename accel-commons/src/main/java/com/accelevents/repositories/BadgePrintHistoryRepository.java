package com.accelevents.repositories;

import com.accelevents.badges.printhistory.BadgesPrintHistory;
import com.accelevents.dto.BadgesPrintHistoryDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
    public interface BadgePrintHistoryRepository extends CrudRepository<BadgesPrintHistory,Long> {

    @Query("SELECT new com.accelevents.dto.BadgesPrintHistoryDTO(bph.id,bph.eventId, bph.badgeId,bph.attendeeUser,bph.updatedBy.userId, bph.updatedBy.firstName,bph.updatedBy.lastName,bph.updatedAt,bph.ticketingTypeId,bph.deviceType,bph.printScreen) FROM BadgesPrintHistory bph WHERE bph.eventId=:eventId AND bph.ticketingTypeId=:ticketingTypeOnlyId AND bph.attendeeUserId=:attendeeUserId AND bph.badgeId=:badgeId")
    Page<BadgesPrintHistoryDTO> findByAttendeeUserIdAndBadgeIdAndEventIdAndTicketingTypeIdOrderPage(@Param("attendeeUserId") Long attendeeUserId, @Param("badgeId") long badgeId, @Param("eventId") long eventId, @Param("ticketingTypeOnlyId") Long ticketingTypeOnlyId, Pageable pageable);

    @Query("SELECT COUNT(bph) " +
            " FROM BadgesPrintHistory bph " +
            " WHERE bph.attendeeUserId =:userId " +
            " AND bph.ticketingTypeId =:ticketingTypeId " +
            " AND bph.eventId =:eventId " +
            " AND bph.badgeId =:badgeId " +
            " AND bph.eventTicketId =:eventTicketId " )
    Long attendeeBadgePrintCount(@Param("userId")Long userId, @Param("ticketingTypeId")Long ticketingTypeId,
                                            @Param("eventId")Long eventId, @Param("badgeId")Long badgeId, @Param("eventTicketId")Long eventTicketId);

    @Query(value = "SELECT bph.eventTicketId, COUNT(bph.id) " +
            " FROM BadgesPrintHistory bph " +
            " WHERE bph.eventId = :eventId " +
            " GROUP BY bph.eventTicketId" )
    List<Object[]> getAllBadgePrintCountsByEventId(@Param("eventId") Long eventId);

    @Query(value = "SELECT DISTINCT bph.eventTicketId " +
            " FROM BadgesPrintHistory bph " +
            " WHERE bph.eventId = :eventId" )
    Set<Long> getAllPrintedBadgeEventTicketIds(@Param("eventId") Long eventId);
}
