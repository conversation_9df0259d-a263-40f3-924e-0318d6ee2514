package com.accelevents.repositories;

import com.accelevents.domain.EventRequestForm;
import com.accelevents.form.dto.EventRequestFormBasicDto;
import com.accelevents.form.dto.EventRequestFormDto;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;

import java.util.List;
import java.util.Optional;

/**
 * Repository for EventRequestFormConstant entity
 */
@Repository
public interface EventRequestFormRepository extends CrudRepository<EventRequestForm, Long> {

    @Cacheable(value = "eventRequestFormByUrl", key = "#p0", condition="#p0!=null", unless="#result == null" )
    Optional<EventRequestForm> findByFormUrl(String formUrl);

    @CacheEvict(value = "eventRequestFormByUrl", key = "#p0.formUrl", condition="#p0!=null")
    EventRequestForm save(@NotNull EventRequestForm eventRequestForm);

    List<EventRequestForm> findByWhiteLabelId(Long whiteLabelId);

    @Query("SELECT f FROM EventRequestForm f WHERE f.whiteLabelId = :whiteLabelId " +
            "AND f.status in (:status) " )
    Page<EventRequestForm> findByWhiteLabelIdAndStatus(Long whiteLabelId,@Param("status") List<String> status, Pageable pageable);
    @Query("SELECT new com.accelevents.form.dto.EventRequestFormBasicDto(f.id, f.name) FROM EventRequestForm f " +
            "WHERE f.whiteLabelId = :whiteLabelId ")
    List<EventRequestFormBasicDto> findRequestFormNameAndIdByWhiteLabelId(Long whiteLabelId);

    Page<EventRequestForm> findByWhiteLabelId(Long whiteLabelId, Pageable pageable);

    @Query("SELECT f FROM EventRequestForm f WHERE f.whiteLabelId = :whiteLabelId " +
            "AND f.status in (:status) " +
            "AND (:searchString IS NULL OR :searchString = '' OR " +
            "f.name LIKE CONCAT('%', :searchString, '%') OR " +
            "CONCAT(f.createdBy.firstName, ' ', f.createdBy.lastName) LIKE CONCAT('%', :searchString, '%'))")
    Page<EventRequestForm> searchByWhiteLabelIdAndStatusAndNameContaining(
            @Param("whiteLabelId") Long whiteLabelId,
            @Param("status") List<String> status,
            @Param("searchString") String searchString,
            Pageable pageable);


    boolean existsByNameAndWhiteLabelId(String name, Long whiteLabelId);

    boolean existsByFormUrl(String formUrl);

    @Query("SELECT new com.accelevents.form.dto.EventRequestFormDto(f) FROM EventRequestForm f " +
            "WHERE f.id = :id AND f.whiteLabelId = :whiteLabelId ")
    Optional<EventRequestFormDto> getEventRequestFormDtoByIdAndWhiteLabelId(
            @Param("id") Long id,
            @Param("whiteLabelId") Long whiteLabelId);

    Optional<EventRequestForm> findByIdAndWhiteLabelId(@Param("id") Long id,
                                                       @Param("whiteLabelId") Long whiteLabelId);

    @Query("SELECT f FROM EventRequestForm f " +
            "WHERE f.id IN (:formIds)")
    List<EventRequestForm> findFormsByIds(@Param("formIds") List<Long> formIds);

    @Query("SELECT f.id FROM EventRequestForm f WHERE f.whiteLabelId =:whiteLabelId")
    List<Long> findEventRequestFormIdsByWhiteLabelId(@Param("whiteLabelId") Long whiteLabelId);
}
