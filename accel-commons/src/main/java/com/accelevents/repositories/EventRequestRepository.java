package com.accelevents.repositories;

import java.util.List;
import java.util.Optional;

import com.accelevents.domain.enums.EventRequestFormSubmissionStatus;
import com.accelevents.dto.EventRequestAssignmentCountDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.accelevents.domain.EventRequest;
import com.accelevents.dto.EventRequestDto;

@Repository
public interface EventRequestRepository extends CrudRepository<EventRequest, Long> {


    @Query("SELECT new com.accelevents.dto.EventRequestDto(" +
            "req.id, " +
            "req.requestedForUser.firstName, " +
            "req.requestedForUser.lastName, " +
            "req.requestedForUser.email, " +
            "req.eventRequestForm.name, " +
            "req.ticketHolderAttributes.jsonValue,"+
            "req.createdAt, " +
            "req.status,req.whiteLabelId,req.note, " +
            "req.requestedForUser.photo, " +
            "COALESCE(b.userId,''), " +
            "COALESCE(b.firstName,''), " +
            "COALESCE(b.lastName,''), " +
            "COALESCE(b.email,''), " +
            "COALESCE(b.photo,''), " +
            "COALESCE(c.userId,''), " +
            "COALESCE(c.firstName,''), " +
            "COALESCE(c.lastName,''), " +
            "COALESCE(c.email,''), " +
            "COALESCE(c.photo,''), " +
            "COALESCE(p.userId,''), " +
            "COALESCE(p.firstName,''), " +
            "COALESCE(p.lastName,''), " +
            "COALESCE(p.email,''), " +
            "COALESCE(p.photo,'')) " +
            "FROM EventRequest req " +
            "LEFT JOIN req.budgetOwner b " +
            "LEFT JOIN req.coordinator c " +
            "LEFT JOIN req.planner p "+
            "WHERE req.whiteLabelId = :whiteLabelId " +
            "AND (COALESCE(:status, 1) = 1 OR req.status IN :status) "+
            "AND (COALESCE(:eventPlannerIds,NULL) IS NULL OR (p.userId IN :eventPlannerIds)) " +
            "AND (COALESCE(:eventBudgetOwnerIds,NULL) IS NULL OR (b.userId IN :eventBudgetOwnerIds)) " +
            "AND (COALESCE(:eventCoordinatorIds,NULL) IS NULL OR (c.userId IN :eventCoordinatorIds)) "+
            "AND ((COALESCE(:plannerFlag,NULL) IS NULL OR :plannerFlag = '' )" +
            "          OR (COALESCE(:plannerFlag,NULL) = 'TRUE'  AND p.userId IS NOT NULL) " +
            "          OR (COALESCE(:plannerFlag,NULL) = 'FALSE' AND p.userId IS NULL)) "+
            "AND ((COALESCE(:coordinatorFlag,NULL) IS NULL OR :coordinatorFlag = '' )" +
            "          OR (COALESCE(:coordinatorFlag,NULL) = 'TRUE'  AND c.userId IS NOT NULL) " +
            "          OR (COALESCE(:coordinatorFlag,NULL) = 'FALSE' AND c.userId IS NULL) ) "+
            "AND ((COALESCE(:budgetOwnerFlag,NULL) IS NULL OR :budgetOwnerFlag = '' ) " +
            "          OR (COALESCE(:budgetOwnerFlag,NULL) = 'TRUE'  AND b.userId IS NOT NULL) " +
            "          OR (COALESCE(:budgetOwnerFlag,NULL) = 'FALSE' AND b.userId IS NULL)) "+
            "AND (coalesce(:searchString, 1) = 1 " +
            "OR LOWER(req.requestedForUser.firstName) LIKE LOWER(CONCAT('%', :searchString, '%')) " +
            "OR LOWER(req.requestedForUser.lastName) LIKE LOWER(CONCAT('%', :searchString, '%')) " +
            "OR LOWER(CONCAT(req.requestedForUser.firstName, ' ', req.requestedForUser.lastName)) LIKE LOWER(CONCAT('%', :searchString, '%')) " +
            "OR LOWER(req.requestedForUser.email) LIKE LOWER(CONCAT('%', :searchString, '%')) " +
            "OR LOWER(req.eventRequestForm.name) LIKE LOWER(CONCAT('%', :searchString, '%')) "+
            ")")
    Page<EventRequestDto> findAllByWhiteLabelIdAndSearch(@Param("whiteLabelId") Long whiteLabelId,
                                         @Param("searchString") String searchString,
                                         @Param("status") List<EventRequestFormSubmissionStatus> status,
                                         @Param("eventPlannerIds") List<Long> eventPlannerIds,
                                         @Param("eventBudgetOwnerIds") List<Long> eventBudgetOwnerIds,
                                         @Param("eventCoordinatorIds") List<Long> eventCoordinatorIds,
                                         @Param("budgetOwnerFlag") String budgetOwnerFlag,
                                         @Param("coordinatorFlag") String coordinatorFlag,
                                         @Param("plannerFlag") String plannerFlag,
                                         Pageable pageable);

    Optional<EventRequest> findByIdAndWhiteLabelId(@Param("id")Long id,@Param("whiteLabelId") Long whiteLabelId);

    List<EventRequest> findByEventRequestFormId(@Param("eventRequestFormId")Long eventRequestFormId);

    @Query("SELECT new com.accelevents.dto.EventRequestAssignmentCountDto(" +
            "SUM(CASE WHEN req.budgetOwnerId IS NOT NULL THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN req.budgetOwnerId IS NULL THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN req.coordinatorId IS NOT NULL THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN req.coordinatorId IS NULL THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN req.plannerId IS NOT NULL THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN req.plannerId IS NULL THEN 1 ELSE 0 END) ) " +
            "FROM EventRequest req " +
            "WHERE req.whiteLabelId = :whiteLabelId " +
            "AND (:budgetOwnerId IS NULL OR req.budgetOwnerId = :budgetOwnerId) " +
            "AND (:coordinatorId IS NULL OR req.coordinatorId = :coordinatorId) " +
            "AND (:plannerId IS NULL OR req.plannerId = :plannerId)")
    EventRequestAssignmentCountDto countAssignRequestByWhiteLabelIdAndUserId(@Param("whiteLabelId")Long whiteLabelId,
                                                                             @Param("budgetOwnerId") Long budgetOwnerId, @Param("coordinatorId")Long coordinatorId, @Param("plannerId")Long plannerId);

    @Query("SELECT er " +
            "  FROM EventRequest er" +
            "  WHERE er.whiteLabelId = :whiteLabelId" +
            "  AND ((:role = 'eventcoordinator' AND er.coordinatorId = :userId)" +
            "   OR (:role = 'eventbudgetowner' AND er.budgetOwnerId = :userId)" +
            "   OR (:role = 'eventplanner' AND er.plannerId = :userId)" +
            "     )")
    List<EventRequest> findAllByWhiteLabelIdAndRoleAndUserId(
            Long whiteLabelId, String role, Long userId);

}

