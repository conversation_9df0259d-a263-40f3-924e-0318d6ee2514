package com.accelevents.repositories;

import com.accelevents.auction.dto.BaseContactDto;
import com.accelevents.auction.dto.ContactsNameDto;
import com.accelevents.domain.Contacts;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ContactsRepository extends CrudRepository<Contacts, Long> {

    List<Contacts> findAllByEventId(long eventId);

    Page<Contacts> findAllByEventIdOrderByIdDesc(long eventId, Pageable pageable);

    int countAllByEventId(long eventId);

    Optional<Contacts> findByEmailAndEventId(String email, long eventId);

    Optional<Contacts> findByPhoneNumberAndEventId(long phoneNumber, long eventId);

    @Transactional
    void deleteByEventId(long eventId);

    @Query(value = "SELECT DISTINCT contact From Contacts contact where contact.eventId=:eventId AND (contact.email LIKE %:search% OR contact.firstName LIKE %:search% OR contact.lastName  LIKE %:search% OR contact.phoneNumber LIKE %:search%)" )
    Page<Contacts> getAllByEventIdAndSearch(@Param("eventId") long eventId, @Param("search") String search, Pageable pageable);

    @Query(
            value =
                    "SELECT c.first_name    AS firstName, " +
                            "       c.last_name     AS lastName, " +
                            "       c.email         AS email, " +
                            "       c.id            AS id, " +
                            "       c.country_code  AS countryCode, " +
                            "       c.phone_number  AS phoneNumber, " +
                            "       CASE " +
                            "         WHEN ts.cnt = 0 THEN 'Unregistered' " +
                            "         WHEN ts.checked_in  > 0 THEN 'Checked In' " +
                            "         WHEN ts.registered  > 0 THEN 'Registered' " +
                            "         WHEN ts.canceled   = ts.cnt THEN 'Canceled' " +
                            "         ELSE 'Unregistered' " +
                            "       END AS ticketStatus, " +
                            "       c.contact_attribute AS contactAttribute " +
                            "FROM contacts c " +
                            "JOIN contacts_list_mapping clm " +
                            "  ON c.id                 = clm.contacts_id " +
                            " AND clm.contacts_list_id = :contactsListId " +
                            " AND clm.rec_status      <> 'DELETE' " +
                            "LEFT JOIN ( " +
                            "  SELECT h_email AS holder_email, " +
                            "         COUNT(*)                      AS cnt, " +
                            "         SUM(ticket_status = 'CHECKED_IN')  AS checked_in, " +
                            "         SUM(ticket_status = 'REGISTERED')  AS registered, " +
                            "         SUM(ticket_status = 'CANCELED')    AS canceled " +
                            "  FROM event_tickets " +
                            "  WHERE event_id       = :eventId " +
                            "    AND data_type      = 'TICKET' " +
                            "    AND ticket_status <> 'DELETED' " +
                            "    AND (rec_status <> 'CANCEL' OR rec_status IS NULL) " +
                            "  GROUP BY h_email " +
                            ") ts ON ts.holder_email = c.email " +
                            "WHERE c.event_id    = :eventId " +
                            "  AND c.rec_status <> 'DELETE' " +
                            "  AND ( :searchString IS NULL " +
                            "     OR CONCAT(c.first_name,' ',c.last_name) LIKE CONCAT('%',:searchString,'%') " +
                            "     OR c.email             LIKE CONCAT('%',:searchString,'%') " +
                            "     OR c.phone_number      LIKE CONCAT('%',:searchString,'%') " +
                            "  ) " ,
            countQuery =
                    "SELECT COUNT(*) " +
                            "FROM contacts c " +
                            "JOIN contacts_list_mapping clm " +
                            "  ON c.id                 = clm.contacts_id " +
                            " AND clm.contacts_list_id = :contactsListId " +
                            " AND clm.rec_status      <> 'DELETE' " +
                            "WHERE c.event_id    = :eventId " +
                            "  AND c.rec_status <> 'DELETE' " +
                            "  AND ( :searchString IS NULL " +
                            "     OR CONCAT(c.first_name,' ',c.last_name) LIKE CONCAT('%',:searchString,'%') " +
                            "     OR c.email             LIKE CONCAT('%',:searchString,'%') " +
                            "     OR c.phone_number      LIKE CONCAT('%',:searchString,'%') " +
                            "  )",
            nativeQuery = true
    )
    Page<Object[]> findContactsByEventIdAndContactsListId(
            @Param("eventId")        Long eventId,
            @Param("contactsListId") Long contactsListId,
            @Param("searchString")   String searchString,
            Pageable                pageable
    );


    @Query("SELECT DISTINCT contact FROM Contacts contact " +
            "JOIN ContactsListMapping clm ON contact.id = clm.contacts.id " +
            "WHERE contact.eventId = :eventId " +
            "AND clm.contactsList.id = :contactsListId " +
            "AND clm.recStatus <> 'DELETE' " +
            "AND contact.id IN :ids")
    List<Contacts> findAllByEventIdAndIdsAndContactsListId(@Param("eventId") long eventId,
                                          @Param("contactsListId") long contactsListId,
                                          @Param("ids") List<Long> ids);

    @Query("SELECT DISTINCT contact FROM Contacts contact " +
            "JOIN ContactsListMapping clm ON contact.id = clm.contacts.id " +
            "WHERE contact.eventId = :eventId " +
            "AND clm.contactsList.id = :contactsListId " +
            "AND clm.recStatus <> 'DELETE' ")
    List<Contacts> findAllByEventIdAndContactsListId(@Param("eventId") long eventId,
                                                           @Param("contactsListId") long contactsListId);
    @Query(value = "SELECT contact From Contacts contact where contact.eventId=:eventId AND contact.email in (:emails)")
    Set<Contacts> findAllByEventIdAndEmails(@Param("eventId") Long eventId, @Param("emails") Set<String> emails);

    @Query("SELECT contact " +
            "FROM Contacts contact " +
            "LEFT JOIN FETCH contact.contactAttribute ca " +
            "WHERE contact.eventId = :eventId " +
            "AND (contact.id IN :ids OR contact.email IN :emails)")
    Set<Contacts> findAllByEventIdAndIdsOrEmails(@Param("eventId") Long eventId,
                                                 @Param("ids") Set<Long> ids,
                                                 @Param("emails") Set<String> emails);

    @Query(value = "SELECT contact From Contacts contact where contact.eventId=:eventId AND contact.id in (:ids)")
    List<Contacts> findAllByEventIdAndIds(@Param("eventId") long eventId, @Param("ids") List<Long> ids);


    @Query(value = "SELECT contact From Contacts contact where contact.eventId BETWEEN :from AND :to")
    List<Contacts> findAllByEventIdBetween(@Param("from") long from,@Param("to") long to);

    @Query( " SELECT NEW com.accelevents.auction.dto.ContactsNameDto(c.id,CONCAT(c.firstName, ' ', c.lastName)) FROM Contacts c " +
            " JOIN ContactsListMapping clm ON c.id = clm.contacts.id " +
            " WHERE c.eventId = :eventId " +
            " AND clm.contactsList.id = :contactsListId " +
            " AND clm.recStatus <> 'DELETE' ")
    List<ContactsNameDto> getContactsNameByEventIdAndContactsListId(@Param("eventId") long eventId,
                                                                    @Param("contactsListId") long contactsListId);

    @Query(value = "SELECT NEW com.accelevents.auction.dto.BaseContactDto(c.firstName,c.lastName,c.email) From Contacts c where c.eventId=:eventId AND c.id in (:ids)")
    List<BaseContactDto> getBaseContactDtoByEventIdAndIds(@Param("eventId") long eventId,List<Long> ids);

    @Query(" SELECT u.userId FROM Contacts c " +
            " JOIN User u on c.email = u.email " +
            " Where c.eventId =:eventId " +
            " AND c.id IN (:ids) ")
    List<Long>  getUserIdByEventIdAndIds(@Param("eventId") long eventId,@Param("ids") List<Long> ids);

    @Query("SELECT DISTINCT contact.id FROM Contacts contact " +
            "JOIN ContactsListMapping clm ON contact.id = clm.contacts.id " +
            "WHERE contact.eventId = :eventId " +
            "AND clm.contactsList.id = :contactsListId " +
            "AND clm.recStatus <> 'DELETE' ")
    List<Long> getContactIdsByEventIdAndContactsListId(@Param("eventId") long eventId,
                                                     @Param("contactsListId") long contactsListId);

    @Query( " SELECT clm.contacts FROM ContactsListMapping clm " +
            " WHERE clm.contacts.id IN :contactsIds " +
            " AND clm.contactsList.eventId = :eventId " +
            " GROUP BY clm.contacts.id " +
            " HAVING COUNT(clm.id) = 1 ")
    List<Contacts> getContactsInAllContactListsByContactIdsAndEventIdHavingCountOne(@Param("contactsIds") List<Long> contactsIds,
                                                                                   @Param("eventId") Long eventId);

    @Query("SELECT COUNT(DISTINCT contact.email) > 0 FROM Contacts contact " +
            "JOIN ContactsListMapping clm ON contact.id = clm.contacts.id " +
            "WHERE contact.eventId = :eventId " +
            "AND clm.contactsList.id IN :contactsListIds " +
            "AND contact.email = :email " +
            "AND clm.recStatus <> 'DELETE'")
    boolean existsEmailsByEventIdAndContactsListIds(@Param("eventId") long eventId,
                                                    @Param("contactsListIds") List<Long> contactsListIds,
                                                    @Param("email") String email);

}
