package com.accelevents.repositories;

import com.accelevents.domain.EventBillingAddOns;
import com.accelevents.domain.enums.AddOnType;
import com.accelevents.domain.enums.PaymentStatus;
import com.accelevents.domain.enums.RecordStatus;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface EventBillingAddOnsRepository  extends CrudRepository<EventBillingAddOns,Long> {


    @Modifying
    @Query("UPDATE EventBillingAddOns SET recordStatus=:status WHERE event_id=:eventId AND id=:eventBillingAddOnsId")
    void updateStatusToDeleteByEventBillingAddOnsId(@Param("eventId") Long eventId, @Param("eventBillingAddOnsId") Long eventBillingAddOnsId,
                                                    @Param("status") RecordStatus status);

    @Query("SELECT billingAddOns FROM EventBillingAddOns billingAddOns LEFT JOIN FETCH billingAddOns.event WHERE billingAddOns.eventId=:eventId")
    List<EventBillingAddOns> findAllByEventId(@Param("eventId") Long eventId);

    @Modifying
    @Query("UPDATE EventBillingAddOns SET paymentStatus = :paid WHERE eventId = :eventId")
    void updatePaymentStatusByEventId(@Param("eventId") long eventId,@Param("paid") PaymentStatus paid);

    Optional<EventBillingAddOns> findFirstByEventIdAndAddOnTypeAndNotesAndPaymentStatus(Long eventId, AddOnType addOnType, String notes, PaymentStatus paymentStatus);

    @Query("SELECT billingAddOns FROM EventBillingAddOns billingAddOns WHERE eventId = :eventId and billingAddOns.addOnType=:addOnType " +
            " and billingAddOns.paymentStatus=:paymentStatus")
    List<EventBillingAddOns> findByEventIdAndAddOnTypeAndNotesAndPaymentStatus(@Param("eventId") Long eventId,
                                                                               @Param("addOnType") AddOnType addOnType,
                                                                               @Param("paymentStatus") PaymentStatus paymentStatus);

    @Query(value = "select e.event_id,COALESCE(sum(eb.amount),0) from event_billing_add_ons eb join events e on e.event_id=eb.event_id " +
            "where e.organizer_id=:organizerId and add_on_type=:addOnType " +
            "and eb.rec_status!='DELETE' group by e.event_id",nativeQuery = true)
    List<Object[]> findAllByOrganizerIdAndAddOnTypeNotPaymentStatus(@Param("organizerId") Long organizerId,
                                                                              @Param("addOnType") String addOnType);

    @Query(value = "select e.event_id,COALESCE(sum(eb.amount),0) from event_billing_add_ons eb join events e on e.event_id=eb.event_id " +
            "where e.white_label=:whiteLabelId and add_on_type=:addOnType " +
            "and eb.rec_status!='DELETE' group by e.event_id;" ,nativeQuery = true)
    List<Object[]> findAllByWhiteLabelIdAndAddOnTypeNotPaymentStatus(@Param("whiteLabelId") long whiteLabelId,
                                                                               @Param("addOnType") String upload);

    @Query(value = "select b.event_id,sum(b.amount) amount, b.payment_status status,\n" +
            "  (case when b.created_date is not null then b.created_date else t.event_start_date  end) as created_date ,\n" +
            " (case when e.white_label is not null then  e.white_label else e.organizer_id  end) as orgORwlId,\n" +
            " (case when e.white_label is not null then  'WL' else 'ORG' end) as type\n" +
            "  from event_billing_add_ons b join events e on b.event_id = e.event_id\n" +
            "  join ticketing t on t.event_id=e.event_id\n" +
            "  where b.add_on_type='UPLOAD' \n" +
            "  and e.listing_status in ('PUBLIC', 'PRIVATE', 'PUBLISHED') \n" +
            "  and b.rec_status<>'DELETE'  \n" +
            "  and e.event_format <> 'IN_PERSON' \n" +
            "  and invoice_id is not null \n" +
            "  and payment_status <>  'WAIVE_OFF'\n" +
            "  and notes != 'This is the CSV uploaded and we have charged it from chargebee.'\n" +
            "  and b.created_date > :fromDate" + // '2021-09-30 00:00:00'\n" +
            "  group by b.event_id, b.payment_status", nativeQuery = true)
    List<Object[]> getEventAttendeesCountToSend(@Param("fromDate") Date fromDate);

    @Query(value = "select e.event_id,s.user_id from events e " +
            "inner join event_billing_add_ons eb on e.event_id=eb.event_id " +
            "inner join staff s on e.event_id=s.event_id and s.role='admin' " +
            "where eb.add_on_type=:addOnType and eb.payment_status=:paymentStatus " +
            "and eb.notes = :notes and eb.invoice_id is null " +
            "and e.listing_status IN (:listingStatus) " +
            "and (e.event_status<>'EVENT_DELETED' or e.event_status is NULL) " +
            "and s.rec_status<>'DELETE' and s.is_api_user=false and eb.rec_status<>'DELETE' " +
            "and eb.created_date>=(select scheduler_config_date from chargebee_scheduler_config where charge_name=:chargeName and enable = true) "+
            "group by s.event_id ", nativeQuery = true)
    List<Object[]> findAllUnPaidEventIdAndUserIdByAddonType(@Param("listingStatus") List<String> listingStatus,
                                                            @Param("addOnType") String addOnType,
                                                            @Param("paymentStatus") String paymentStatus,
                                                            @Param("chargeName") String chargeName,
                                                            @Param("notes") String notes);

    @Query(value = "select e.event_id,s.user_id from events e " +
            "inner join event_billing_add_ons eb on e.event_id=eb.event_id " +
            "inner join staff s on e.event_id=s.event_id and s.role='admin' " +
            "left join ticketing t on t.event_id=e.event_id " +
            "where eb.add_on_type=:addOnType and eb.payment_status=:paymentStatus " +
            "and eb.notes = :notes and eb.invoice_id is null " +
            "and t.event_end_date < now() " +
            "and e.listing_status IN (:listingStatus) " +
            "and (e.event_status<>'EVENT_DELETED' or e.event_status is NULL) " +
            "and s.rec_status<>'DELETE' and s.is_api_user=false and eb.rec_status<>'DELETE' " +
            "and eb.created_date>=(select scheduler_config_date from chargebee_scheduler_config where charge_name=:chargeName and enable = true) "+
            "group by s.event_id ", nativeQuery = true)
    List<Object[]> findAllPendingEventIdAndUserIdByAddonType(@Param("listingStatus") List<String> listingStatus,
                                                             @Param("addOnType") String addOnType,
                                                             @Param("paymentStatus") String paymentStatus,
                                                             @Param("chargeName") String chargeName,
                                                             @Param("notes") String notes);
}
