package com.accelevents.repositories;

import com.accelevents.common.dto.EventDesignDetailDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventDesignDetail;
import com.accelevents.domain.enums.EventType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.ticketing.dto.EventDesignLogoDetailDto;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EventDesignDetailRepository extends CrudRepository<EventDesignDetail, Long> {

	// public DesignEvent findDesignEventByName(String name);//NOSONAR

	EventDesignDetail findByEvent(Event event);

	@Query("SELECT edt.allowEndedEventAccess FROM EventDesignDetail edt WHERE edt.event=:event ")
	boolean findAllowEndedEventAccessByEvent(@Param("event") Event event);

	@Query("SELECT edt.totalFundRaisedShow FROM EventDesignDetail edt WHERE edt.event=:event")
	Boolean checkTotalFundRaisedShow(@Param("event") Event event);

	@Query("SELECT edt.enableAutoAssignedSequence FROM EventDesignDetail edt WHERE edt.event=:event")
	boolean checkEnableAutoAssignedSequence(@Param("event") Event event);

	Optional<EventDesignDetail> findByNeonEventId(String neonEventId);

	@Query("SELECT edt FROM EventDesignDetail edt WHERE edt.ticketingBuyButtonText is not null")
    List<EventDesignDetail> getAllByBuyButtonTextIsNotNull();

	@Query("SELECT edt.viewScrollSpeed FROM EventDesignDetail edt WHERE edt.event = :event")
    Long getViewScrollSpeedByEvent(@Param("event") Event event);

	@Query("SELECT edt.sponsorSection FROM EventDesignDetail edt WHERE edt.event = :event")
    String getSponsorDetailsByEvent(@Param("event") Event event);

	@Modifying
	@Query("UPDATE EventDesignDetail SET enableSessionsSpeakers =:isEnable WHERE event =:event ")
	void updateSessionsSpeakers(@Param("event") Event event, @Param("isEnable") Boolean isEnable);

	@Query("SELECT edt.eventType FROM EventDesignDetail edt WHERE edt.event = :event")
	EventType findEventTypeByEvent(@Param("event") Event event);

    @Query("SELECT edt.eventCalendarInvite FROM EventDesignDetail edt WHERE edt.event = :event")
    String getEventCalenderInviteByEvent(@Param("event") Event event);

	@Query("SELECT edt.eventCalendarInvite FROM EventDesignDetail edt WHERE edt.event.eventURL = :eventUrl")
	String getEventCalenderInviteByEventUrl(@Param("eventUrl") String eventUrl);

    @Query(value = "SELECT edt FROM EventDesignDetail edt WHERE edt.id BETWEEN :from AND :to AND edt.configureTabsAsJson IS NOT NULL " +
            "AND (edt.event.eventStatus <> 'EVENT_DELETED' OR edt.event.eventStatus IS NULL)")
    List<EventDesignDetail> findAllEventDesignDetailsBetween(@Param("from") long from, @Param("to") long to);

    @Query(value = "SELECT edt FROM EventDesignDetail edt WHERE edt.id BETWEEN :from AND :to " +
            "AND (edt.event.eventStatus <> 'EVENT_DELETED' OR edt.event.eventStatus IS NULL)")
    List<EventDesignDetail> findOldNewAllEventDesignDetailsBetween(@Param("from") long from, @Param("to") long to);

    @Query("SELECT edd.intercomActivated FROM EventDesignDetail edd WHERE edd.event.eventId=:eventId")
    boolean findIntercomActivatedByEventId(@Param("eventId") long eventId);

    @Modifying
    @Query("UPDATE EventDesignDetail SET recordStatus = :recordStatus WHERE event.eventId = :eventId")
    void updateStatusDeleted(@Param("eventId") Long eventId, @Param("recordStatus") RecordStatus recordStatus);

    @Modifying
    @Query("UPDATE EventDesignDetail SET recordStatus = :recordStatus WHERE event.eventId IN (:eventIds)")
    void updateRecordStatusOfEventDesignDetailsByEventIds(@Param("eventIds") List<Long> eventIds, @Param("recordStatus") RecordStatus recordStatus);



    @Query("SELECT edt FROM EventDesignDetail edt WHERE edt.event in (:events)")
    List<EventDesignDetail> findByEventList(@Param("events") List<Event> events);

    @Query("SELECT new com.accelevents.common.dto.EventDesignDetailDto(edt.replyToEmail, edt.logoImage) FROM EventDesignDetail edt WHERE edt.event=:event")
    EventDesignDetailDto findEventDesignDetailByEvent(@Param("event") Event event);
    @Modifying
    @Query("UPDATE EventDesignDetail SET enableCaptcha = :enableCaptcha WHERE event = :event")
    void updateCaptchaSetting(@Param("event")Event event, @Param("enableCaptcha") boolean enableCaptcha);

    @Query("SELECT edt.enableCaptcha FROM EventDesignDetail edt WHERE edt.event=:event ")
    boolean isEnabledCaptchaSettingForEvent(@Param("event") Event event);

    @Modifying
    @Query("UPDATE EventDesignDetail SET isAdvancedWebsiteLayout = :isAdvancedWebsiteLayout WHERE event = :event")
    void updateEventAdvancedWebsiteLayoutStatus(@Param("event")Event event, @Param("isAdvancedWebsiteLayout") boolean isAdvancedWebsiteLayout);

    @Query("SELECT new com.accelevents.ticketing.dto.EventDesignLogoDetailDto(e.event.eventId,e.logoImage) FROM EventDesignDetail e WHERE e.event.eventId in (:eventsIds)")
    List<EventDesignLogoDetailDto> findAllByEvents(@Param("eventsIds") List<Long> eventsIds);
}