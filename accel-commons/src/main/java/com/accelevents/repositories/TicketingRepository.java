package com.accelevents.repositories;

import com.accelevents.common.dto.RegistrationApprovalSettingsDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.enums.*;
import com.accelevents.ticketing.dto.TicketingDatesDto;
import com.accelevents.ticketing.dto.TicketingDownloadPurchase;
import com.accelevents.ticketing.dto.TicketingModuleDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface TicketingRepository extends CrudRepository<Ticketing, Long> {

    @Query("UPDATE Ticketing SET feeUploadedAmount =:feeUploadedAmount WHERE id =:id ")
    @Modifying
    void updateFeeUploadedAmountForTicketingById(@Param("feeUploadedAmount") double feeUploadedAmount,@Param("id") Long id);

    @Query(value = "select id,fee_uploaded_amount from ticketing t where t.event_id=:eventId",nativeQuery = true)
    List<Object[]> findAllWithDeletedDataByEventId(@Param("eventId") Long eventId);

    @Cacheable(value = "findTicketingByEventId", key = "#p0.eventId", unless="#result == null")
	Ticketing findByEventid(Event event);

	@Query("SELECT new com.accelevents.ticketing.dto.TicketingDatesDto" +
			" (eventStartDate,eventEndDate)" +
			" FROM Ticketing WHERE eventid=:event")
	TicketingDatesDto findEventStartAndEndDateByEventid(@Param("event") Event event);

    @Query("SELECT new com.accelevents.ticketing.dto.TicketingDatesDto" +
            " (eventid.eventId,eventid.name, eventStartDate,eventEndDate)" +
            " FROM Ticketing WHERE eventid.eventId in (:eventsIds)")
    List<TicketingDatesDto> findEventStartAndEndDateByEventsIds(@Param("eventsIds") List<Long> eventsIds);

    @Query("SELECT new com.accelevents.ticketing.dto.TicketingDatesDto" +
            " (eventStartDate,eventEndDate,preEventAccessMinutes)" +
            " FROM Ticketing WHERE eventid=:event")
    TicketingDatesDto findEventStartAndEndDateAndPreEventAccessMinuteByEventid(@Param("event") Event event);

	@Query("SELECT new com.accelevents.ticketing.dto.TicketingDownloadPurchase(event.name, event.eventURL, ticketing.eventEndDate,event) FROM Ticketing as ticketing JOIN ticketing.eventid AS event")
	List<TicketingDownloadPurchase> findAllTicketingDownloadPurchase();

	@Query("Select ticketing FROM Ticketing ticketing WHERE ticketing.eventid.eventId=:eventId")
	Ticketing findByEventId(@Param("eventId") Long eventId);

    @Query("Select ticketing FROM Ticketing ticketing WHERE ticketing.eventid.eventId in (:eventIds)")
    List<Ticketing> findByEventIds(@Param("eventIds") List<Long> eventIds);

	@Query("SELECT t FROM Ticketing t WHERE t.eventEndDate <= :endDate AND t.eventPayoutStatus IN (:eventPayoutStatus)")
	List<Ticketing> getEndedTicketingByPayoutStatus(@Param("eventPayoutStatus") List<EnumEventPayoutStatus> eventPayoutStatuses, @Param("endDate") Date endDate);

	@Query("SELECT ticketing FROM Ticketing ticketing WHERE ticketing.chartKey IS NOT NULL AND ticketing.id BETWEEN :from AND :to")
    List<Ticketing> getEventChartKeysFromTo(@Param("from") long from, @Param("to") long to);

    @Cacheable(value = "isRecurringByEvt", key = "#p0.eventId", unless="#result == null")
	@Query("Select ticketing.isRecurringEvent FROM Ticketing ticketing WHERE ticketing.eventid=:event")
	boolean isRecurringEvent(@Param("event") Event event);

	@Query("SELECT event FROM Ticketing ticketing JOIN ticketing.eventid event WHERE ticketing.isRecurringEvent=:isRecurringEnable AND event.eventId BETWEEN :from AND :to")
    List<Event> findListOfEventByEventIdsAndRecurringEnable(@Param("from") long from,
															@Param("to") long to,
															@Param("isRecurringEnable") boolean isRecurringEnable);

	@Query("Select ticketing.showRemainingTickets FROM Ticketing ticketing WHERE ticketing.eventid=:event")
	boolean findShowRemainingTicketsByEventid(@Param("event") Event event);

	@Query("Select new com.accelevents.ticketing.dto.TicketingModuleDTO(ticketing.isRecurringEvent, ticketing.chartKey,ticketing.onlineEvent) " +
			"FROM Ticketing ticketing WHERE ticketing.eventid=:event")
	TicketingModuleDTO findTicketingByEventId(@Param("event") Event event);

	@Query("Select new com.accelevents.ticketing.dto.TicketingModuleDTO(ticketing.activated, ticketing.isRecurringEvent) " +
			"FROM Ticketing ticketing WHERE ticketing.eventid=:event")
	TicketingModuleDTO findTicketingFieldsByEventId(@Param("event") Event event);

	@Query("SELECT event FROM Ticketing ticketing JOIN ticketing.eventid event WHERE ticketing.isRecurringEvent=:isRecurringEnable AND ticketing.chartKey IS NOT null AND event.eventId BETWEEN :from AND :to")
    List<Event> getListOfEventByEventIdsAndRecurringEnableAndChartKeyNotNull(@Param("from") long from,
																			 @Param("to") long to,
																			 @Param("isRecurringEnable") boolean isRecurringEnable);

    @Caching(evict = {
            @CacheEvict(value = "isRecurringByEvt", key = "#p0.eventId"),
            @CacheEvict(value = "findTicketingByEventId", key = "#p0.eventId")
    })
	@Modifying
	@Query("UPDATE Ticketing SET onlineEvent = true WHERE eventid = :event")
    void setEventToOnlineEvent(@Param("event") Event event);

	@Query("SELECT ticketing.eventid FROM Ticketing ticketing WHERE ticketing.onlineEvent = true" +
			" AND (ticketing.eventid.eventStatus <> 'EVENT_DELETED' OR ticketing.eventid.eventStatus IS NULL) ")
	List<Event> findAllOnlineEvents();


    //@Cacheable(value = "isTicketingActivated", key = "#p0.eventId", unless="#result == null")
    @Query("select ticketing.activated from Ticketing ticketing where eventid = :event")
	Boolean isActivated(@Param("event") Event event);

    @Caching(evict = {
            @CacheEvict(value = "isRecurringByEvt", key = "#p0.eventid.eventId"),
            //@CacheEvict(value = "isTicketingActivated", key = "#p0.eventid.eventId")
            @CacheEvict(value = "isAllowCheckInWithUnpaidTicketEnabledByEventId", key = "#p0.eventid.eventId"),
            @CacheEvict(value = "findTicketingByEventId", key = "#p0.eventid.eventId"),
            @CacheEvict(value = "isLimitEventCapacityByEventId", key = "#p0.eventid.eventId"),
            @CacheEvict(value = "findLimitEventCapacityByEventId", key = "#p0.eventid.eventId")
    })
    Ticketing save(Ticketing ticketing);

    @Query("SELECT ticketing FROM Ticketing AS ticketing" +
            " JOIN ticketing.eventid AS event " +
            " WHERE event.ticketingEnabled = true" +
            " AND ticketing.status = :status" +
            " AND ticketing.eventEndDate <= now() " +
            " AND event.eventListingStatus IN (:eventListingStatus) "+
            " AND (event.eventStatus<>'EVENT_DELETED' or event.eventStatus is NULL) ")
    List<Ticketing> findEndedEventsByTicketingStatus(@Param("status") EnumOverageStatus enumOverageStatus, @Param("eventListingStatus") List<EventListingStatus> eventListingStatus);

    @Query("SELECT ticketing FROM Ticketing ticketing" +
            "   JOIN ticketing.eventid AS event " +
            "   WHERE ticketing.chartKey IS NOT NULL AND ticketing.id BETWEEN :from AND :to" +
            "   and event.eventStatus is NULL")
    List<Ticketing> getEventChartKeysFromToWhereEventIsNotDeleted(@Param("from") long from, @Param("to") long to);

    @Query("select ticketing.showRegistrationButton from Ticketing ticketing where ticketing.eventid = :event")
    boolean isShowRegistrationButton(@Param("event") Event event);

    @Modifying
    @Query("UPDATE Ticketing SET recordStatus = :recordStatus WHERE eventid.eventId = :eventId")
    void updateStatusDelete( @Param("eventId")Long eventId, @Param("recordStatus") RecordStatus recordStatus);

    @Modifying
    @Query("UPDATE Ticketing SET recordStatus = :recordStatus WHERE eventid.eventId IN (:eventIds)")
    void updateRecordStatusOfTicketingByEventIds( @Param("eventIds")List<Long> eventIds, @Param("recordStatus") RecordStatus recordStatus);

    @Query("SELECT ticketing FROM Ticketing ticketing JOIN ticketing.eventid event WHERE event.eventId IN (:listOfEventId)")
    List<Ticketing> findListOfticketingByEventIds(@Param("listOfEventId") List<Long> listOfEventId);

    @Query("SELECT ticketing.collectTicketHolderAttributes FROM Ticketing ticketing WHERE ticketing.eventid.eventId = :eventId")
    boolean isCollectTicketHolderAttributesEnable(@Param("eventId") Long eventId);

    @Query(value = "SELECT ti.event_start_date FROM ticketing ti WHERE ti.event_id=:eventId", nativeQuery = true)
    Date getEventStartDateByEventId(@Param("eventId") Long eventId);

    @Query(value = "SELECT ti.event_end_date FROM ticketing ti WHERE ti.event_id=:eventId", nativeQuery = true)
    Date getEventEndDateByEventId(@Param("eventId") Long eventId);

    @Query("SELECT ticketing FROM Ticketing ticketing WHERE ticketing.eventid.eventId BETWEEN :from AND :to and ticketing.attendeeRegistrationAutoSendMail IN (:autoSendEmails)")
    List<Ticketing> findByAutoEmailStatusIn(@Param("autoSendEmails") List<AutoSendEmail> autoSendEmails, @Param("from") long from, @Param("to") long to);

    @Query("SELECT ticketing.attendeeRegistrationApproval FROM Ticketing ticketing WHERE ticketing.eventid.eventId = :eventId")
    boolean isAttendeeRegistrationApprovalEnabledByEventId(@Param("eventId") Long eventId);

    @Query("SELECT event FROM Ticketing ticketing JOIN ticketing.eventid event WHERE ticketing.chartKey IS NOT null AND event.eventId BETWEEN :from AND :to AND (event.eventStatus<>'EVENT_DELETED' or event.eventStatus is NULL)")
    List<Event> getListOfEventByEventIdsAndChartKeyNotNull(@Param("from") long from,
                                                                             @Param("to") long to);
    @Query("SELECT ticketing FROM Ticketing ticketing JOIN ticketing.eventid event WHERE event.eventId BETWEEN :from AND :to AND (event.eventStatus<>'EVENT_DELETED' or event.eventStatus is NULL)")
    List<Ticketing> findAllByEventIdBetween(@Param("from") Long from,
                                                           @Param("to") Long to);

    @Query("SELECT ticketing FROM Ticketing ticketing JOIN ticketing.eventid event WHERE event.eventId BETWEEN :from AND :to AND ticketing.isRecurringEvent is TRUE AND  (event.eventStatus<>'EVENT_DELETED' or event.eventStatus is NULL)")
    List<Ticketing> findAllByIsRecurringEventAndEventIdBetween(@Param("from") Long from,
                                            @Param("to") Long to);

    @Cacheable(value = "isAllowCheckInWithUnpaidTicketEnabledByEventId", key = "#p0", condition = "#p0 != null", unless="#result == null")
    @Query("SELECT ticketing.allowCheckInWithUnpaidTicket FROM Ticketing ticketing WHERE ticketing.eventid.eventId = :eventId")
    boolean isAllowCheckInWithUnpaidTicketEnabledByEventId(@Param("eventId") Long eventId);

    @Query("Select ticketing.collectTicketHolderAttributes FROM Ticketing ticketing WHERE ticketing.eventid=:event")
    boolean isCollectTicketHolderAttributesByEvent(@Param("event") Event event);

    @Cacheable(value = "isLimitEventCapacityByEventId", key = "#p0.eventId", condition = "#p0 != null", unless="#result == null")
    @Query("select limitEventCapacity from Ticketing where eventid=:eventid")
    boolean findIsLimitEventCapacityByEventId(@Param("eventid") Event eventId);

    @Cacheable(value = "findLimitEventCapacityByEventId", key = "#p0.eventId", condition = "#p0 != null", unless="#result == null")
    @Query("select eventCapacity from Ticketing where eventid=:eventid")
    Double findLimitEventCapacityByEventId(@Param("eventid") Event eventId);

    @Query("Select ticketing.approvalRequestEmailsEnabled FROM Ticketing ticketing WHERE ticketing.eventid=:event")
    boolean isApprovalRequestEmailsEnabled(@Param("event") Event event);

    @Query("Select ticketing.approvalEmailReceiverIds FROM Ticketing ticketing WHERE ticketing.eventid=:event")
    String findApprovalEmailReceiverIdsByEvent(@Param("event") Event event);

    @Query("Select new com.accelevents.common.dto.RegistrationApprovalSettingsDto(ticketing.approvalRequestEmailsEnabled,ticketing.approvalEmailReceiverIds,ticketing.attendeeApprovalCardCaptureEnabled) FROM Ticketing ticketing WHERE ticketing.eventid=:event")
    RegistrationApprovalSettingsDto getRegistrationApprovalSettingByEvent(@Param("event") Event event);

    @Modifying
    @Query(value = "UPDATE ticketing SET status = :status where event_id=:eventId", nativeQuery = true)
    void updateTicketOverageStatus(@Param("eventId") Long eventId, @Param("status") String status);

    @Query("SELECT ticketing.enableOrderConfirmationEmail FROM Ticketing ticketing WHERE ticketing.eventid.eventId = :eventId")
    boolean isEnableOrderConfirmationEmail(@Param("eventId") Long eventId);

}
