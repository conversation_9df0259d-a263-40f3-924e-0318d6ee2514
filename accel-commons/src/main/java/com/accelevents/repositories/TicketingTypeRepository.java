package com.accelevents.repositories;

import com.accelevents.common.dto.EventTicketTypeCountDto;
import com.accelevents.common.dto.TicketTypeNameQtyDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.tray.io.connector.TicketTypeDetails;
import com.accelevents.messages.TicketType;
import com.accelevents.ticketing.dto.CategoryDto;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public interface TicketingTypeRepository extends CrudRepository<TicketingType, Long> {

    TicketingType findTicketingTypeById(long tickettypeid);

    TicketingType findFirstByEventIdAndRecurringEventIdIsNullOrderByPositionDesc(Long eventId);

    @Query(value = "SELECT * FROM event_ticket_type  \n" +
            " where event_id =:eventId and recurring_event_id =:recurringEventId order by position desc LIMIT 1",nativeQuery = true)
    TicketingType findFirstByEventAndRecurringEventIdOrderByPositionDesc(@Param("eventId")Long eventId, @Param("recurringEventId")Long recurringEventId);


    @Query("SELECT ticketType" +
            " FROM TicketingType AS ticketType" +
            " JOIN ticketType.ticketing AS ticketing" +
            " WHERE ticketType.event = :event" +
            " AND ticketType.isHidden=:hidden" +
            " AND ticketType.dataType IN (:dataType) " +
            " AND ticketType.recurringEventId IS NOT NULL" +
            " ORDER BY ticketType.position")
    List<TicketingType> findAllByEventIdAndRecurringIdIsNotNullAndHidden(@Param("event") Event event,
                                                                         @Param("hidden") boolean hidden,
                                                                         @Param("dataType") List<DataType> dataType);

    @Query("SELECT ticketType FROM TicketingType AS ticketType " +
            " JOIN ticketType.ticketing AS ticketing " +
            " WHERE ticketType.event =:event " +
            " AND ticketType.dataType='TICKET' " +
            " AND (ticketType.recurringEventId =:recurringEventId OR 0 = :recurringEventId) " +
            " ORDER BY ticketType.position")
    List<TicketingType> findAllByEventIdRecurring(@Param("event") Event event,
                                                  @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT ticketType FROM TicketingType AS ticketType " +
            " JOIN ticketType.ticketing AS ticketing " +
            " WHERE ticketType.event =:event " +
            " AND (ticketType.recurringEventId =:recurringEventId OR 0 = :recurringEventId) " +
            " ORDER BY ticketType.position")
    List<TicketingType> findAllTicketTypeByEventAndRecurringEvent(@Param("event") Event event,
                                                  @Param("recurringEventId") Long recurringEventId);

    @Modifying
    @Query("UPDATE TicketingType set position=(position+:updatecount) where eventId = :eventId "
            + " AND position> :startposition AND position <:endposition")
    void updatePositionTicketType(@Param("eventId") long eventId, @Param("startposition") double startposition,
                                  @Param("endposition") double endposition, @Param("updatecount") double updatecount);

    @Modifying
    @Query(value = "UPDATE event_ticket_type AS ett SET ett.position=(ett.position+:updatecount)" +
            "WHERE ett.event_id=:eventId ORDER BY ett.position DESC", nativeQuery = true)
    void updatePositionForAllTicketTypes(@Param("updatecount") double updatecount,
                                         @Param("eventId") long eventId);

    @Query("SELECT ticketType " +
            " FROM TicketingType AS ticketType" +
            " WHERE ticketType.event=:event " +
            "AND ticketType.dataType='TICKET' " +
            "AND ticketType.ticketType=:ticketType ORDER BY ticketType.position")
    List<TicketingType> findTicketTypeAsCategoryByEvent(@Param("event") Event event,
                                                        @Param("ticketType") TicketType ticketType);

    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.ticketTypeName=:ticketTypeName " +
            " AND ticketType.eventId =:eventId AND ticketType.dataType ='TICKET'")
    List<TicketingType> findByTicketTypeNameAndEventId(@Param("ticketTypeName") String ticketTypeName,
                                                       @Param("eventId") Long eventId);

    @Query(" SELECT ticketType.id FROM TicketingType AS ticketType " +
            " WHERE ticketType.eventId =:eventId AND ticketType.dataType ='TICKET' " +
            " AND (ticketType.status != 'DELETE' OR ticketType.status IS NULL ) " +
            " AND  ticketType.ticketTypeName IN (:ticketTypeNames) " )
    List<Long> findTicketTypeIdByEventIdAndTicketTypeNames(@Param("eventId") Long eventId, @Param("ticketTypeNames") List<String> ticketTypeNames);

    @Query(" SELECT ticketingType " +
            " FROM TicketingType ticketingType " +
            " WHERE ticketingType.recurringEventId IN (:recurringEventsList) " +
            " AND ticketingType.isHidden = coalesce(:hiddenOnly, ticketingType.isHidden ) " +
            " AND (:dataType is null OR ticketingType.dataType=:dataType) " +
            " ORDER BY ticketingType.position DESC")
    List<TicketingType> findByListOfRecurringEvents(@Param("recurringEventsList") List<Long> recurringEventsList,
                                                    @Param("hiddenOnly") Boolean hiddenOnly,
                                                    @Param("dataType") DataType dataType);
    @Query(" SELECT ticketingType " +
            " FROM TicketingType ticketingType " +
            " WHERE ticketingType.recurringEventId IN (:recurringEventsList) " +
            " AND (ticketingType.isHidden = coalesce(:hiddenOnly, ticketingType.isHidden ) OR ticketType.dataType = 'ADDON') " +
            " AND (:dataType is null OR ticketingType.dataType=:dataType) " +
            " ORDER BY ticketingType.position DESC")
    List<TicketingType> findByListOfRecurringEventsOrAddOns(@Param("recurringEventsList") List<Long> recurringEventsList,
                                                    @Param("hiddenOnly") Boolean hiddenOnly,
                                                    @Param("dataType") DataType dataType);
    @Query(" SELECT ticketingType " +
            " FROM TicketingType ticketingType " +
            " WHERE ticketingType.recurringEventId IN (:recurringEventsList) " +
            " AND ticketingType.isHidden = coalesce(:hiddenOnly, ticketingType.isHidden ) " +
            " AND ticketingType.dataType IN (:dataTypes) " +
            " ORDER BY ticketingType.position DESC")
    List<TicketingType> findAllTicketTypeOrAddOnByRecurringEvents(@Param("recurringEventsList") List<Long> recurringEventsList,
                                                    @Param("hiddenOnly") Boolean hiddenOnly,
                                                    @Param("dataTypes") List<DataType> dataTypes);

    @Query("SELECT tt FROM TicketingType tt WHERE tt.createdFrom=:createdFrom AND tt.recurringEventId=:recurringEventId ")
    TicketingType findTicketTypeWithSameCreatedFromAndInRecurringEventId(@Param("createdFrom") Long createdFrom, @Param("recurringEventId") Long recurringEventId);

    @Query(value = "SELECT if(max(event_ticket_type.price)>0 ,max(event_ticket_type.price), 0), if(min(event_ticket_type.price)>0 ,min(event_ticket_type.price), 0), " +
            " min(event_ticket_type.start_date) FROM event_ticket_type " +
            " WHERE event_ticket_type.event_id=:eventId " +
            " and event_ticket_type.data_type ='TICKET' and (event_ticket_type.rec_status != 'CANCEL' OR event_ticket_type.rec_status is null) " +
            " and event_ticket_type.ticket_type not in(:ticketTypes)", nativeQuery = true)
    List<Object[]> findTicketPriceRange(@Param("eventId") Long eventId,
                                        @Param("ticketTypes") List<String> ticketTypes);

    @Query("SELECT " +
            " CASE WHEN MAX(ett.price) > 0 THEN MAX(ett.price) ELSE 0 END, " +
            " CASE WHEN MIN(ett.price) > 0 THEN MIN(ett.price) ELSE 0 END, " +
            " MIN(ett.startDate) " +
            " FROM TicketingType ett " +
            " WHERE ett.eventId = :eventId " +
            " AND ett.dataType = 'TICKET' " +
            " AND (ett.status != 'CANCEL' OR ett.status IS NULL) " +
            " AND ett.ticketType NOT IN :ticketTypes " +
            " AND ett.isHidden = false")
    List<Object[]> findTicketPriceRangeAndExcludeHiddenTypes(@Param("eventId") Long eventId,
                                                             @Param("ticketTypes") List<TicketType> ticketTypes);


    @Query("SELECT new com.accelevents.ticketing.dto.CategoryDto(ticketType.id, " +
            " ticketType.ticketTypeName, " +
            " ticketType.price, " +
            " ticketType.ticketing.chartKey, " +
            " ticketType.recurringEventId ," +
            " ticketType.createdFrom )" +
            " FROM TicketingType AS ticketType JOIN ticketType.ticketing AS ticketing " +
            " JOIN ticketType.recurringEvent AS recurringEvent " +
            " WHERE ticketType.eventId=:eventId " +
            " AND ticketType.dataType ='TICKET'" +
            " AND ticketType.ticketType=:paid")
    List<CategoryDto> findAllTicketingTypes(@Param("eventId") Long eventId,
                                            @Param("paid") TicketType paid);

    @Query("SELECT ticketType " +
            " FROM TicketingType AS ticketType " +
            " WHERE ticketType.recurringEventId=:recurringEventId " +
            "AND ticketType.dataType ='TICKET' "+
            "AND ticketType.ticketType=:paid")
    List<TicketingType> findAllTicketingTypesByRecuurringEvent(@Param("recurringEventId") Long recurringEventId,
                                                               @Param("paid") TicketType paid);

    @Query("SELECT ett.eventId, " +
            " SUM(case when (ett.bundleType !='INDIVIDUAL_TICKET') then (ett.numberOfTickets * ett.numberOfTicketPerTable) else ett.numberOfTickets end) " +
            " FROM TicketingType as ett" +
            " WHERE  ett.dataType ='TICKET' AND ett.ticketType <> 'DONATION'" +
            "AND ett.eventId IN(:eventIds) GROUP BY ett.eventId")
    List<Object[]> findNumberOfTotalTickets(@Param("eventIds") Set<Long> eventIds);

    @Query("SELECT COALESCE(SUM(case when (ett.bundleType !='INDIVIDUAL_TICKET') then (ett.numberOfTickets * ett.numberOfTicketPerTable) else ett.numberOfTickets end),0) " +
            " FROM TicketingType as ett , Event AS e" +
            " WHERE e.ticketingId = ett.ticketing.id " +
            " AND ett.dataType = :dataType AND ett.ticketType <> 'DONATION'" +
            " AND e.eventId = :eventId")
    Long findNumberOfTotalTicketsInEvent(@Param("eventId") Long eventId,@Param("dataType") DataType dataType);

    @Query(" SELECT COUNT(ticketType.id) FROM TicketingType AS ticketType " +
            " WHERE ticketType.event =:event AND FIND_IN_SET(:categoryId, ticketType.categories) > 0 AND ticketType.dataType='TICKET'")
    Long countCategoryInTicketType(@Param("event") Event event, @Param("categoryId") Long categoryId);

    @Query("SELECT ticketType.id FROM TicketingType ticketType WHERE ticketType.createdFrom in (:createdFroms) AND ticketType.recurringEventId=:recurringEventId")
    List<Long> getTicketTypeIdsByCreatedFromsAndInRecurringEventId(@Param("createdFroms") List<Long> createdFroms, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT ticketingType From TicketingType ticketingType JOIN FETCH ticketingType.ticketing as ticketing JOIN FETCH ticketing.eventid as event WHERE event.eventId BETWEEN :from AND :to AND ticketingType.createdFrom is null AND ticketingType.dataType ='TICKET'")
    List<TicketingType> findByListOfEventIds(@Param("from") long from, @Param("to") long to);

    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " JOIN ticketType.ticketing AS ticketing " +
            " WHERE ticketType.event =:event " +
            " AND ticketType.recurringEventId IS NULL AND  ticketType.ticketType=:paid" +
            " AND ticketType.dataType ='TICKET'" +
            " ORDER BY ticketType.position")
    List<TicketingType> findAllByEventIdRecurringIdNullOnlyPaid(@Param("event") Event event, @Param("paid") TicketType paid);

    @Query("SELECT ticketingType FROM TicketingType ticketingType " +
            "   JOIN ticketingType.recurringEvent as recurring " +
            "   WHERE ticketingType.createdFrom=:ticketTypeId " +
            "   AND recurring.recurringEventStartDate >=:currentDateAndTimeInEventTimeZone ")
    List<TicketingType> findTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(@Param("ticketTypeId") Long ticketTypeId, @Param("currentDateAndTimeInEventTimeZone") Date currentDateAndTimeInEventTimeZone);

    @Query("SELECT ticketingType From TicketingType ticketingType WHERE ticketingType.createdFrom IN (:ticketTypeIds)")
    List<TicketingType> findByCreatedFrom(@Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Query("SELECT ticketingType From TicketingType ticketingType WHERE ticketingType.id IN (:ticketTypeIds)")
    List<TicketingType> findByIds(@Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Transactional
    @Modifying
    @Query("UPDATE TicketingType SET isHidden=:isHidden WHERE id IN (:ticketTypeIds) AND dataType ='TICKET'")
    void markAsHiddenByTicketTypeIds(@Param("ticketTypeIds") List<Long> ticketTypeIds, @Param("isHidden") boolean isHidden);

    @Query("SELECT ticketingType From TicketingType ticketingType WHERE ticketingType.recurringEventId IN (:oldReIdsList)")
    List<TicketingType> findTicketingTypesByRecurringList(@Param("oldReIdsList") List<Long> oldReIdsList);

    @Query(" SELECT ticketType.id FROM TicketingType AS ticketType " +
            " WHERE ticketType.event =:event " +
            " AND ticketType.dataType ='TICKET'" +
            " AND (ticketType.recurringEventId =:recurringEventId OR 0 = :recurringEventId) " +
            " ORDER BY ticketType.position")
    List<Long> findAllIdByEventIdAndRecurringEvent(@Param("event") Event event,
                                                   @Param("recurringEventId") Long recurringEventId);

    @Query(" SELECT ticketType.id FROM TicketingType AS ticketType " +
            " WHERE ticketType.event =:event " +
            " AND ticketType.dataType ='TICKET'" +
            " AND (ticketType.createdFrom <>:createdFrom) ")
    List<Long> findRecurringEventTypeIdByEventAndCreatedFrom(@Param("event") Event event, @Param("createdFrom") Long createdFrom);

    @Query("SELECT ticketType.id FROM TicketingType ticketType WHERE ticketType.createdFrom IN (:createdFrom)")
    List<Long> findTicketTypeIdsByCreatedFroms(@Param("createdFrom") List<Long> createdFrom);

    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.event =:event " +
            "  AND ticketType.ticketType <> 'FREE' " +
            "  AND ticketType.endDate >= :currentDate" +
            "  AND ticketType.dataType IN (:dataTypes)" +
            " AND (ticketType.createdFrom IS NULL OR ticketType.createdFrom = -1) " +
            " ORDER BY ticketType.position DESC")
    List<TicketingType> findAllByEventId(@Param("event") Event event,
                                         @Param("currentDate") Date currentDate,
                                         @Param("dataTypes") List<DataType> dataTypes);

    @Query("SELECT ticketingType From TicketingType ticketingType JOIN FETCH ticketingType.ticketing as ticketing JOIN FETCH ticketing.eventid as event WHERE ticketingType.eventId IN (:eventIds) AND ticketingType.recurringEventId IS null AND ticketingType.dataType ='TICKET'")
    List<TicketingType> findTicketingByEventIdList(@Param("eventIds") List<Long> eventIds);

    @Query("SELECT ticketingType From TicketingType ticketingType WHERE ticketingType.categoryId =:categoryId AND ticketingType.dataType IN (:dataTypes)")
    List<TicketingType> findByCategoryId(@Param("categoryId") Long categoryId,
                                         @Param("dataTypes") List<DataType> dataTypes);

    @Query(value = "select sum(number_of_tickets),count(distinct id) from event_ticket_type " +
            " where category_id = :categoryId and created_from is null ", nativeQuery = true)
    List<Object[]> findByCategoryIdForDataFix(@Param("categoryId") Long categoryId);

    @Query("SELECT ticketingType FROM TicketingType ticketingType " +
            " WHERE ticketingType.categoryId IS NOT NULL " +
            " AND ticketingType.id BETWEEN :from AND :to")
    List<TicketingType> getCategories(@Param("from") long from ,@Param("to") long to);


    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.eventId =:eventId " +
            " AND ticketType.ticketType <> 'FREE' " +
            " AND ticketType.dataType ='TICKET' " +
            " AND ticketType.createdFrom IS NOT NULL " +
            " AND ticketType.recurringEventId IS NOT NULL " )
    List<TicketingType> findAllPaidTicketsOfRecurringEventOnly(@Param("eventId") Long eventId);


    @Modifying
    @Query("UPDATE TicketingType SET status = :status WHERE recurringEventId IN (:recurringEventIds) AND dataType ='TICKET'")
    void updateTicketingTypeRecStatusByRecurringEventIds(@Param("recurringEventIds") List<Long> recurringEventIds, @Param("status") RecordStatus status);

    @Query("SELECT ticketType.id FROM TicketingType ticketType WHERE ticketType.eventId =:eventId AND ticketType.dataType ='TICKET'")
    List<Long> findListOfTicketingTypeIds(@Param("eventId") Long eventId);

    @Query(" SELECT COUNT(ticketType.id) " +
                " FROM TicketingType AS ticketType " +
               " WHERE ticketType.event =:event " +
               " AND ticketType.ticketType <> 'FREE' " +
               " AND ticketType.dataType ='TICKET' " )
    Long countAllPaidTicketsOfEvent(@Param("event") Event event);

    @Query(" SELECT new com.accelevents.common.dto.EventTicketTypeCountDto(ticketType.eventId, COUNT(ticketType.id))" +
            " FROM TicketingType AS ticketType " +
            " WHERE ticketType.eventId in :eventIds " +
            " AND ticketType.ticketType <> 'FREE' " +
            " AND ticketType.dataType ='TICKET' " +
            " GROUP BY ticketType.eventId" )
    List<EventTicketTypeCountDto> countAllPaidTicketsOfEvents(@Param("eventIds") List<Long> eventIds);

    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.event =:event " +
            " AND ticketType.recurringEventId IS NULL " +
            " AND ticketType.dataType ='TICKET'" +
            " ORDER BY ticketType.position")
    List<TicketingType> findAllByEventIdRecurringIdNull(@Param("event") Event event);

    List<TicketingType> findByRecurringEventId(Long recuringEventId);

    @Modifying
    @Query("UPDATE TicketingType tt SET tt.recurringEventSalesEndTime = null, tt.recurringEventSalesEndStatus = null WHERE EXISTS (SELECT 1 FROM Ticketing t WHERE tt.ticketing.id = t.id AND t.id = :ticketingId)")
    void updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(@Param("ticketingId") Long ticketingId);

    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.eventId =:eventId " +
            " AND ticketType.recurringEventId IS NULL AND  ticketType.ticketType=:free" +
            " AND ticketType.dataType ='TICKET'" +
            " ORDER BY ticketType.position")
    List<TicketingType> dataFixForFreeTickets(@Param("eventId") Long eventId, @Param("free") TicketType free);

    @Transactional
    @Modifying
    @Query("UPDATE TicketingType SET category=:seatingCategories where id =:TicketingTypeId")
    void updateCategoryId(@Param("seatingCategories") SeatingCategories seatingCategories, @Param("TicketingTypeId") Long TicketingTypeId);

    @Query("SELECT new com.accelevents.dto.tray.io.connector.TicketTypeDetails(ticketType.id, ticketType.ticketTypeName) FROM TicketingType AS ticketType " +
            "WHERE ticketType.event =:event " +
            "AND ticketType.ticketType <> 'DONATION' " +
            "AND ticketType.recurringEventId IS NULL " +
            "AND ticketType.dataType ='TICKET' " +
            "ORDER BY ticketType.position")
    List<TicketTypeDetails> getTicketTypeDetails(@Param("event") Event event);


    @Query("SELECT ticketingType.ticketTypeName From TicketingType ticketingType WHERE ticketingType.id IN (:ticketTypeIds) AND ticketingType.dataType ='TICKET'")
    List<String> findByIdIn(@Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Query("SELECT new com.accelevents.dto.tray.io.connector.TicketTypeDetails(ticketType.id, ticketType.ticketTypeName) FROM TicketingType AS ticketType " +
    " WHERE ticketType.id IN (:ticketTypeIds) ")
    List<TicketTypeDetails> getTicketTypeDetailsByTicketTypeIdsIn(@Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Query("SELECT COUNT(*) FROM TicketingType AS ticketType " +
            "   WHERE ticketType.event =:event " +
            "   AND ticketType.dataType=:dataType" +
            "   AND ticketType.recurringEventId IS NULL " +
            "   AND ticketType.ticketTypeName=:name")
    int countByNameAndEvent(@Param("name") String name, @Param("dataType") DataType dataType, @Param("event") Event event);

    @Query("SELECT COUNT(*) FROM TicketingType AS ticketType " +
            "   WHERE ticketType.event =:event " +
            "   AND ticketType.dataType=:dataType" +
            "   AND ticketType.ticketTypeName=:name" +
            "   AND ticketType.recurringEventId IS NULL " +
            "   AND ticketType.categoryId=:categoryId")
    int countByNameAndEventAndCategoryId(@Param("name") String name, @Param("dataType") DataType dataType, @Param("event") Event event, @Param("categoryId") Long categoryId);

    @Query("SELECT ticketType.id FROM TicketingType ticketType WHERE ticketType.createdFrom in (:createdFroms) AND ticketType.recurringEventId=:recurringEventId AND ticketType.dataType =:dataType")
    List<Long> getTicketTypeIdsByCreatedFromAndInRecurringEventIdAndDataType(@Param("createdFroms") List<Long> createdFroms, @Param("recurringEventId") Long recurringEventId,
                                                                             @Param("dataType") DataType dataType);

    @Query("SELECT COUNT(1) > 0 FROM TicketingType ticketingType WHERE ticketingType.eventId = :eventId ")
    boolean isTicketCreatedByTicketingId(@Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.dto.tray.io.connector.TicketTypeDetails(ticketType.id, ticketType.ticketTypeName) FROM TicketingType AS ticketType " +
            "WHERE ticketType.event =:event " +
            "ORDER BY ticketType.dataType")
    List<TicketTypeDetails> getAllTicketTypeDetailsByEvent(@Param("event") Event event);

    @Query("SELECT ticketType.id FROM TicketingType ticketType WHERE ticketType.recurringEventId IS NULL and ticketType.id IN (:ticketingTypeIds)")
    List<Long> findListOfTicketingTypeIdsAndRecurringEventIdIsNull(@Param("ticketingTypeIds") List<Long> ticketingTypeIds);

    @Query("select tt from TicketingType tt left join fetch tt.ticketing ticketing " +
            " join Event e on e.eventId=ticketing.eventid where COALESCE(e.eventStatus,'') not in ('EVENT_DELETED') and tt.ticketType=:ticketType and tt.dataType=:dataType" +
            " and ticketing.onlineEvent=:onlineEvent and (e.whiteLabel=:whiteLabel or e.organizer=:organiser)")
    List<TicketingType> findAllTicketingTypeByTicketTypeAndDataTypeOrWhileLabelOrOrganiser(@Param("ticketType") TicketType ticketType,@Param("dataType") DataType dataType,@Param("onlineEvent") boolean onlineEvent,@Param("whiteLabel") WhiteLabel whiteLabel,@Param("organiser") Organizer organiser);

    @Query("select tt from TicketingType tt left join fetch tt.ticketing ticketing " +
            " join Event e on e.eventId=ticketing.eventid where COALESCE(e.eventStatus,'') not in ('EVENT_DELETED') and tt.ticketType=:ticketType and tt.dataType=:dataType and (e.whiteLabel=:whiteLabel or e.organizer=:organiser)")
    List<TicketingType> findAllTicketingTypeByTicketTypeAndDataTypeForAddOnAndWhileLabelOrOrganiser(@Param("ticketType") TicketType ticketType,@Param("dataType") DataType dataType,@Param("whiteLabel") WhiteLabel whiteLabel,@Param("organiser") Organizer organiser);

    @Query("select tt from TicketingType tt left join fetch tt.ticketing ticketing " +
            " join Event e on e.eventId=ticketing.eventid where COALESCE(e.eventStatus,'') not in ('EVENT_DELETED') and ticketing.eventid=:eventid and tt.ticketType=:ticketType and tt.dataType=:dataType" +
            " and ticketing.onlineEvent=:onlineEvent ")
    List<TicketingType> findByEventAndTicketTypeAndDataType(@Param("eventid") Event event,@Param("ticketType") TicketType ticketType,@Param("dataType") DataType dataType,@Param("onlineEvent") boolean onlineEvent);

    @Query("select tt from TicketingType tt left join fetch tt.ticketing ticketing " +
            " join Event e on e.eventId=ticketing.eventid where COALESCE(e.eventStatus,'') not in ('EVENT_DELETED') and ticketing.eventid=:eventid and tt.ticketType=:ticketType and tt.dataType=:dataType")
    List<TicketingType> findByEventAndTicketTypeAndDataTypeForAddOn(@Param("eventid") Event event,@Param("ticketType") TicketType ticketType,@Param("dataType") DataType dataType);

    @Query("SELECT ticketType " +
            " FROM TicketingType AS ticketType" +
            " WHERE ticketType.event=:event " +
            "AND ticketType.dataType='TICKET' " +
            "AND ticketType.allowAssignedSeating IS TRUE " +
            "AND ticketType.ticketType IN (:ticketTypes) ORDER BY ticketType.position")
    List<TicketingType> findTicketTypeAsCategoryByEventBasedOnAllowSeating(@Param("event") Event event,
                                                        @Param("ticketTypes") List<TicketType> ticketTypes);
    @Query("SELECT ticketType " +
            " FROM TicketingType AS ticketType " +
            " WHERE ticketType.recurringEventId=:recurringEventId " +
            "AND ticketType.dataType ='TICKET' "+
            "AND ticketType.allowAssignedSeating IS TRUE " +
            "AND ticketType.ticketType IN (:ticketTypesList)")
    List<TicketingType> getAllTicketingTypesByRecurringEventBasedOnAllowSeating(@Param("recurringEventId") Long recurringEventId, @Param("ticketTypesList") List<TicketType> ticketTypes);


    @Query("SELECT CASE WHEN COUNT(ticketType) > 0 THEN true ELSE false END FROM TicketingType ticketType Where ticketType.id IN (:ticketingTypeIds) AND ticketType.allowMeetingCreation IS TRUE")
    boolean isUserAllowedToCreateMeetingByTicketTypeIds(@Param("ticketingTypeIds") List<Long> ticketingTypeIds);

    @Query("SELECT ticketType.id, ticketType.allowMeetingCreation FROM TicketingType ticketType Where ticketType.id IN (:ticketingTypeIds)")
    List<Object[]> getTicketTypeMeetingCreationStatusByTicketTypeIds(@Param("ticketingTypeIds") List<Long> ticketingTypeIds);

    @Query("SELECT ticketType  FROM TicketingType AS ticketType " +
            " WHERE ticketType.event=:event " +
            " AND ticketType.dataType =:dataType " +
            " AND ticketType.isAllTicketTypesSelectedForAddOn Is TRUE")
    List<TicketingType> findAllByEventAndDataTypeAndIsAllTicketTypesSelectedForAddOn(@Param("event") Event event,
                                                                                     @Param("dataType") DataType dataType);

    @Query("SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.event=:event " +
            " AND ticketType.dataType =:dataType " )
    List<TicketingType> findAllByEventAndDataType(@Param("event") Event event,@Param("dataType") DataType dataType);


    @Query("SELECT ticketType " +
            " FROM TicketingType AS ticketType " +
            " WHERE ticketType.event = :event " +
            " AND (ticketType.id = :ticketTypeId " +
            " OR ticketType.createdFrom =:ticketTypeId" +
            " OR ticketType.isAllTicketTypesSelectedForAddOn = TRUE ) ")
    List<TicketingType> findAllByEventAndIdOrCreatedFromOrIsAllTicketTypesSelectedForAddOn(
            @Param("event") Event event,
            @Param("ticketTypeId") Long ticketTypeId);

    @Query("SELECT ticketType " +
            " FROM TicketingType AS ticketType " +
            " WHERE ticketType.event = :event " +
            " AND (ticketType.id = :ticketTypeId " +
            " OR ticketType.createdFrom =:ticketTypeId ) ")
    List<TicketingType> findAllByEventAndIdOrCreatedFrom(
            @Param("event") Event event,
            @Param("ticketTypeId") Long ticketTypeId);

    @Query(" SELECT ticketType.id FROM TicketingType AS ticketType " +
            "   WHERE ticketType.recurringEventId=:recurringEventId " +
            "   AND ticketType.dataType =:dataType " )
    List<Long> getAllTicketTypeIdsByRecurringEventIdAndDataType(@Param("recurringEventId") Long recurringEventId,@Param("dataType") DataType dataType);

    @Query(" SELECT ticketType.id FROM TicketingType AS ticketType " +
            "   WHERE ticketType.recurringEventId=:recurringEventId " +
            "   AND (ticketType.createdFrom = -1  OR ticketType.createdFrom in (:createdFroms))" +
            "   AND ticketType.dataType =:dataType " )
    List<Long> getAllTicketTypeIdsByRecurringEventIdAndDataTypeAndCreatedFrom(@Param("recurringEventId") Long recurringEventId,
                                                                                         @Param("dataType") DataType dataType,
                                                                                         @Param("createdFroms") List<Long> createdFroms);


    @Query( " SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.id IN (:ticketTypeIds) ")
    List<TicketingType> findAllByTicketTypeIds(@Param("ticketTypeIds") List<Long> ticketTypeIds);



    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.event=:event " +
            " AND ticketType.recurringEventId =:recurringEventId " +
            " AND ticketType.dataType =:dataType " +
            " AND ticketType.isAllTicketTypesSelectedForAddOn Is TRUE")
    List<TicketingType> findAlLByEventAndRecurringEventIdAndDataTypeAddOnAndIsAllTicketTypesSelectedForAddOn(@Param("event") Event event,
                                                                                                             @Param("recurringEventId") Long recurringEventId,
                                                                                                             @Param("dataType") DataType dataType);


    @Query(" SELECT ticketingType FROM TicketingType as ticketingType " +
            " WHERE ticketingType.eventId  in (:eventIds) "  +
            " AND ticketingType.recurringEventId is NOT NULL " )
    List<TicketingType> findAllByTicketingsAndRecurringEventIdNotNull(@Param("eventIds") List<Long> eventIds);


    @Query(" SELECT ticketingType FROM TicketingType as ticketingType " +
            " WHERE ticketingType.eventId  in (:eventIds) "  +
            " AND ticketingType.recurringEventId is NULL " )
    List<TicketingType> findAllByTicketings(@Param("eventIds") List<Long> eventIds);

    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.event = :event " +
            " AND (ticketType.id = :ticketTypeId " +
            " OR ticketType.createdFrom =:ticketTypeId" +
            " OR ticketType.isAllTicketTypesSelectedForAddOn = TRUE " +
            " OR ticketType.dataType=:dataType) ")
    List<TicketingType> findAllByEventAndIdOrCreatedFromOrIsAllTicketTypesSelectedForAddOnOrDataType(
            @Param("event") Event event,
            @Param("ticketTypeId") Long ticketTypeId,
            @Param("dataType") DataType dataType
    );

    @Query("SELECT ticketType FROM TicketingType AS ticketType " +
            "WHERE ticketType.event = :event AND ticketType.customTemplateId = :customTemplateId ")
    List<TicketingType> findAllTicketTypesByCustomTemplateId(@Param("event") Event event, @Param("customTemplateId") Long customTemplateId);

    @Query("SELECT ticketType FROM TicketingType AS ticketType WHERE ticketType.customConfirmationEmailId = :customConfirmationEmailId ")
    List<TicketingType> findAllTicketTypeByCustomConfirmationEmailId(@Param("customConfirmationEmailId") Long customConfirmationEmailId);

    @Query("SELECT ticketType FROM TicketingType AS ticketType " +
            "WHERE ticketType.event = :event AND ticketType.customConfirmationEmailId = :customConfirmationEmailId ")
    List<TicketingType> findAllTicketTypesByCustomConfirmationEmailId(@Param("event") Event event, @Param("customConfirmationEmailId") Long customConfirmationEmailId);

    @Query("SELECT ticketType FROM TicketingType AS ticketType " +
            "WHERE ticketType.event = :event AND ticketType.customTicketPdfDesignId = :customTicketPdfDesignId ")
    List<TicketingType> findAllTicketTypesByCustomTicketPdfDesignId(@Param("event") Event event, @Param("customTicketPdfDesignId") Long customTicketPdfDesignId);

    @Query("SELECT ticketType FROM TicketingType AS ticketType " +
            "WHERE ticketType.event = :event " +
            "AND ticketType.dataType = :dataType ")
    List<TicketingType> findAllTicketTypeByEventAndDataType(@Param("event") Event event,
                                                   @Param("dataType") DataType dataType);


    @Query(value = "SELECT tt.id, tt.ticket_type_name, t.id as ticketing_id FROM event_ticket_type tt " +
            "JOIN ticketing t ON tt.ticketing_id = t.id " +
            "JOIN events e ON t.event_id = e.event_id " +
            "WHERE e.event_id BETWEEN :from AND :to " +
            "AND tt.data_type = 'TICKET' " +
            "AND (e.event_status <> 'EVENT_DELETED' OR e.event_status IS NULL)", nativeQuery = true)
    List<Object[]> findAllByEventIdBetween(@Param("from") Long from, @Param("to") Long to);


    @Query("SELECT ticketType FROM TicketingType AS ticketType WHERE ticketType.customTemplateId = :customTemplateId ")
    List<TicketingType> findAllTicketTypeByCustomTemplateId(@Param("customTemplateId") Long customTemplateId);

    @Query("SELECT ticketType FROM TicketingType AS ticketType WHERE ticketType.customConfirmationEmailId IN (:customConfirmationEmailIds) AND ticketType.dataType = 'TICKET' ")
    List<TicketingType> findAllTicketTypeByCustomConfirmationEmailId(@Param("customConfirmationEmailIds") List<Long> customConfirmationEmailId);

    @Query(" SELECT ticketType FROM TicketingType AS ticketType " +
            " WHERE ticketType.event=:event " +
            " AND ticketType.recurringEventId =:recurringEventId " +
            " AND ticketType.dataType =:dataType " )
    List<TicketingType> findAlLByEventAndRecurringEventIdAndDataTypeAddOn(@Param("event") Event event,
                                                                          @Param("recurringEventId") Long recurringEventId,
                                                                          @Param("dataType") DataType dataType);


    @Query("select tt from TicketingType tt left join fetch tt.ticketing ticketing " +
            " join Event e on e.eventId=ticketing.eventid where COALESCE(e.eventStatus,'') not in ('EVENT_DELETED') and e.organizer=:organiser")
    List<TicketingType> findAllTicketingTypeByOrganiser(@Param("organiser") Organizer organiser);




    @Query("SELECT COUNT(tt) > 0 FROM TicketingType tt WHERE tt.event = :event AND tt.enableTicketLevelVatTax = true")
    boolean isVatEnabledInAnyTicketTypesForEvent(@Param("event") Event event);

    @Query("SELECT tt FROM TicketingType tt " +
            "WHERE tt.event.id = :eventId " +
            "AND tt.id IN (:ticketTypeIds) " +
            "AND tt.ticketType <> 'DONATION' " +
            "AND tt.dataType IN (:dataTypes) ")
    List<TicketingType> findAllByEventIdAndTicketTypeIdsAndDataTypes(
            @Param("eventId") Long eventId,
            @Param("ticketTypeIds") List<Long> ticketTypeIds,
            @Param("dataTypes") List<DataType> dataTypes);

    @Query(value = "select ct.html_value from event_ticket_type tt inner join custom_templates ct on " +
            "tt.custom_ticket_pdf_design_id = ct.id and tt.id = :ticketingTypeId" , nativeQuery = true)
    String getTicketPdfDesignByTicketId(@Param("ticketingTypeId") Long ticketingTypeId);

    @Query(" SELECT new com.accelevents.common.dto.TicketTypeNameQtyDto( tt.id, tt.numberOfTickets, tt.ticketTypeName ) FROM TicketingType tt " +
            " WHERE tt.event = :event " +
            " AND tt.dataType = 'TICKET' " +
            " AND tt.ticketType = 'FREE' " )
    List<TicketTypeNameQtyDto> findFreeTicketTypeIdAndNumberOfTickets(@Param("event") Event event);

    @Query("SELECT new com.accelevents.dto.tray.io.connector.TicketTypeDetails(ticketType.id, ticketType.ticketTypeName) FROM TicketingType AS ticketType " +
            "WHERE ticketType.event =:event " +
            "AND ticketType.ticketType <> 'DONATION' " +
            "AND ticketType.recurringEventId IS NULL " +
            "AND ticketType.isRegistrationApprovalRequired = true " +
            "AND ticketType.dataType ='TICKET' " +
            "ORDER BY ticketType.position")
    List<TicketTypeDetails> getRegistrationRequestEnabledTicketTypeDetails(@Param("event") Event event);

    @Query("SELECT t FROM TicketingType t WHERE t.event.id IN :eventIds AND t.dataType = :dataType")
    List<TicketingType> findAllByEventIdsAndDataType(@Param("eventIds") List<Long> eventIds,
                                                     @Param("dataType") DataType dataType);

}
