package com.accelevents.repositories;

import com.accelevents.common.dto.ChimeAttendeeProfileDTO;
import com.accelevents.common.dto.UserBasicDto;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.dto.BasicUserDto;
import com.accelevents.dto.BasicUserEventBillingDto;
import com.accelevents.dto.UserAttendeeDTO;
import com.accelevents.session_speakers.dto.FirstLastNameDto;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserRepository extends CrudRepository<User, Long> {

	User findUserByUserId(Long id);

    @Query("select user from User user where user.phoneNumber = :phoneNumber AND user.countryCode = :countryCodeOne AND user.email = :email")
    User findUserByEmailAndPhoneNumberAndCountryCode(@Param("phoneNumber") long phoneNumber,
                                                                      @Param("countryCodeOne") CountryCode countryCodeOne, @Param("email") String email);

	User findUserByPhoneNumber(long phonenumber);

	User findByPasswordResetToken(String token);

	@Query(value =  " SELECT CAST(x.user_id AS CHAR) AS user_id, u.first_name, u.last_name, u.email, if(u.phone_number > 0, CAST(u.phone_number AS CHAR), '') AS phone_number, u.address1, u.address2, u.city, u.state, u.country, u.zipcode  FROM  " +
			" (SELECT DISTINCT ab.user_id FROM auction_bids ab JOIN events e ON ab.auction_id = e.auction_id WHERE e.event_id = :eventId " +
			" UNION " +
			" SELECT DISTINCT pr.user_id FROM purchased_raffle_tickets pr JOIN events e ON pr.raffle_id = e.raffle_id WHERE e.event_id = :eventId " +
			" UNION " +
			" SELECT DISTINCT p.user_id FROM pledge p JOIN events e ON p.cause_auction_id = e.event_id WHERE e.event_id = :eventId " +
			" UNION " +
			" SELECT DISTINCT d.user_id FROM donations d WHERE d.event_id = :eventId " +
			" UNION " +
			" SELECT DISTINCT et.ticket_purchaser_id FROM event_tickets et JOIN ticketing_order tor ON et.ticket_order_id = tor.id WHERE tor.event_id = :eventId " +
			" ) x " +
			" JOIN users u ON x.user_id = u.user_id " ,nativeQuery = true)
	List<Object[]> findAllUserDetailsForEvent(@Param("eventId") Long eventId);

	@Query(value = "SELECT * FROM users" +
            " WHERE user_id = (SELECT user_id FROM staff" +
            " WHERE role = 'whitelabeladmin'" +
            " AND white_label = :whiteLabelId" +
            " and is_api_user = false" +
            " LIMIT 1)", nativeQuery = true)
	User findUserByWhiteLabelId(@Param("whiteLabelId") Long whiteLabelId);

    Set<User> findUserByEmailIn(Set<String> emails);

	@Query("SELECT user FROM User user  WHERE  user.userId BETWEEN :from AND :to")
	List<User> getUserByFromTo(@Param("from") long from, @Param("to") long to);

	@Query("Select user FROM User user where user.phoneNumber IN (:phoneNumberList)")
	List<User> findUsersByListOfPhoneNumber(@Param("phoneNumberList") List<Long> phoneNumberList);

	@Query(value = "select max(user_id) from users where phone_number !=0 " +
			" group by phone_number having count(phone_number) > 1  ", nativeQuery = true)
	List<Object>getDuplicatedPhoneUsersIds();

	@Modifying
	@Query("Update User set phoneNumber=0 where userId in(:userIds)")
	void updateUserSetPhoneNumberToZero(@Param("userIds") List<Long> userIds);
	@Query("SELECT new com.accelevents.dto.BasicUserDto(user.userId,user.email,user.countryCode)" +
			" FROM User user WHERE user.email IN (:emails) ")
	List<BasicUserDto> findByEmailIn(@Param("emails") List<String> emailList);

	@Query("SELECT user.userId FROM User user WHERE user.email=:email")
	Long findUserIdByEmail(@Param("email") String email);


	@Query(value = "select distinct userdetails.user_id from " +
			" (select us.* from check_in_audit_log as cal" +
			" join users as us on us.user_id = cal.virtual_checkin_user_id" +
			" WHERE cal.event_id=:eventId" +
			"  UNION" +
			" select us.* from speakers as speaker" +
			" join users as us on us.user_id=speaker.user_id" +
			" where speaker.event_id=:eventId" +
			" UNION" +
			" select us.* from staff as staff" +
			" join users as us on us.user_id=staff.user_id" +
			" where staff.event_id=:eventId AND staff.rec_status != 'DELETE' AND staff.is_api_user = false) AS userdetails" +
			" ORDER BY userdetails.last_name limit :from,:size"
			, nativeQuery = true)
	List<BigInteger> findAttendeeByEvent(@Param("eventId") Long eventId, @Param("from") int from, @Param("size") int size);

	@Query(value = "select distinct userdetails.user_id from " +
			" (select us.* from event_tickets as et" +
			" join ticketing_order as tord on et.ticket_order_id=tord.id" +
			" join users as us on us.user_id = et.holder_user_id" +
			" WHERE tord.event_id=:eventId" +
			" AND et.ticket_status not in ('CANCELED','DELETED') " +
			"  UNION" +
			" select us.* from speakers as speaker" +
			" join users as us on us.user_id=speaker.user_id" +
			" where speaker.event_id=:eventId" +
			" UNION" +
			" select us.* from staff as staff" +
			" join users as us on us.user_id=staff.user_id" +
			" where staff.event_id=:eventId AND staff.rec_status != 'DELETE') AS userdetails" +
			" ORDER BY userdetails.first_name"
			, nativeQuery = true)
	List<BigInteger> findAttendeeIdsByEvent(@Param("eventId") Long eventId);

    @Query(value = "SELECT DISTINCT userdetails.user_id FROM " +
            " (SELECT us.* FROM event_tickets as et" +
            " JOIN ticketing_order as tord ON et.ticket_order_id=tord.id" +
            " JOIN users as us ON us.user_id = et.holder_user_id" +
            " WHERE tord.event_id=:eventId" +
            " AND et.ticket_status not in ('CANCELED','DELETED')" +
            " UNION" +
            " SELECT us.* FROM speakers as speaker" +
            " JOIN users as us ON us.user_id=speaker.user_id" +
            " WHERE speaker.event_id=:eventId" +
            " UNION" +
            " SELECT us.* FROM staff as staff" +
            " JOIN users as us ON us.user_id=staff.user_id" +
            " WHERE staff.event_id=:eventId AND staff.rec_status != 'DELETE' and staff.is_api_user=false " +
            " ) AS userdetails" +
            " WHERE (coalesce(:searchString,'') = '' or (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or userdetails.email like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%)) " +
            " ORDER BY userdetails.first_name"
            , nativeQuery = true)
    List<BigInteger> findAttendeeIdsBySearchAndByEvent(@Param("eventId") Long eventId, @Param("searchString") String searchString);

	@Query(value = "select distinct userdetails.user_id from " +
			" (select us.* from event_tickets as et" +
			" join ticketing_order as tord on et.ticket_order_id=tord.id" +
			" join users as us on us.user_id = et.holder_user_id" +
			" WHERE tord.event_id=:eventId" +
			" AND et.ticket_status not in ('CANCELED','DELETED') " +
			"  UNION" +
			" select us.* from speakers as speaker" +
			" join users as us on us.user_id=speaker.user_id" +
			" where speaker.event_id=:eventId" +
			" UNION" +
			" select us.* from staff as staff" +
			" join users as us on us.user_id=staff.user_id" +
			" where staff.event_id=:eventId AND staff.rec_status != 'DELETE' and staff.is_api_user=false) AS userdetails" +
            " WHERE (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%)" +
			" ORDER BY userdetails.last_name limit :from,:size",
			nativeQuery = true)
	List<BigInteger> findAttendeeByEvent(@Param("eventId") Long eventId, @Param("searchString") String searchString, @Param("from") int from, @Param("size") int size);

	@Query(value = "select distinct userdetails.* from " +
			" (select us.* from event_tickets as et" +
			" join ticketing_order as tord on et.ticket_order_id=tord.id" +
			" join users as us on us.user_id = et.holder_user_id" +
			" WHERE tord.event_id=:eventId" +
			" AND et.ticket_status not in ('CANCELED','DELETED') " +
			"  UNION" +
			" select us.* from speakers as speaker" +
			" join users as us on us.user_id=speaker.user_id" +
			" where speaker.event_id=:eventId" +
			" UNION" +
			" select us.* from staff as staff" +
			" join users as us on us.user_id=staff.user_id" +
			" where staff.event_id=:eventId AND staff.rec_status != 'DELETE' and staff.is_api_user=false) AS userdetails" +
			" WHERE (coalesce(:searchString,'') = '' or (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or userdetails.email like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%))" +
			" ORDER BY userdetails.first_name ",
			countQuery = "select count(*) from " +
                    " (select us.* from event_tickets as et" +
                    " join ticketing_order as tord on et.ticket_order_id=tord.id" +
                    " join users as us on us.user_id = et.holder_user_id" +
                    " WHERE tord.event_id=:eventId" +
                    " AND et.ticket_status not in ('CANCELED','DELETED') " +
                    "  UNION" +
                    " select us.* from speakers as speaker" +
                    " join users as us on us.user_id=speaker.user_id" +
                    " where speaker.event_id=:eventId" +
                    " UNION" +
                    " select us.* from staff as staff" +
                    " join users as us on us.user_id=staff.user_id" +
                    " where staff.event_id=:eventId AND staff.rec_status != 'DELETE' and staff.is_api_user=false) AS userdetails" +
                    " WHERE (coalesce(:searchString,'') = '' or (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or userdetails.email like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%)) ",
			nativeQuery = true)
    Page<User> getAllAttendeeByEvent(@Param("eventId") Long eventId,
                                     @Param("searchString") String searchString,
                                     Pageable pageable);

    @Query(value = "select distinct userdetails.* from " +
            " (select us.* from event_tickets as et" +
            " join ticketing_order as tord on et.ticket_order_id=tord.id" +
            " join users as us on us.user_id = et.holder_user_id" +
            " WHERE tord.event_id=:eventId" +
            " AND et.ticket_status not in ('CANCELED','DELETED') " +
            "  UNION" +
            " select us.* from speakers as speaker" +
            " join users as us on us.user_id=speaker.user_id" +
            " where speaker.event_id=:eventId" +
            " UNION" +
            " select us.* from staff as staff" +
            " join users as us on us.user_id=staff.user_id" +
            " where staff.event_id=:eventId AND staff.rec_status != 'DELETE' and staff.is_api_user=false) AS userdetails" +
            " WHERE (coalesce(:searchString,'') = '' or (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or userdetails.email like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%))" +
            " ORDER BY CASE WHEN :sortParam = 'first_name ASC'  THEN first_name END ASC," +
            "          CASE WHEN :sortParam = 'first_name DESC' THEN first_name END DESC, " +
            "          CASE WHEN :sortParam = 'last_name ASC' THEN last_name END ASC, " +
            "          CASE WHEN :sortParam = 'last_name DESC' THEN last_name END DESC,  "+
            "          CASE WHEN :sortParam = 'email ASC' THEN email END ASC, " +
            "          CASE WHEN :sortParam = 'email DESC' THEN email END DESC  ",
            countQuery = "select count(*) from " +
                    " (select us.* from event_tickets as et" +
                    " join ticketing_order as tord on et.ticket_order_id=tord.id" +
                    " join users as us on us.user_id = et.holder_user_id" +
                    " WHERE tord.event_id=:eventId" +
                    " AND et.ticket_status not in ('CANCELED','DELETED') " +
                    "  UNION" +
                    " select us.* from speakers as speaker" +
                    " join users as us on us.user_id=speaker.user_id" +
                    " where speaker.event_id=:eventId" +
                    " UNION" +
                    " select us.* from staff as staff" +
                    " join users as us on us.user_id=staff.user_id" +
                    " where staff.event_id=:eventId AND staff.rec_status != 'DELETE' and staff.is_api_user=false) AS userdetails" +
                    " WHERE (coalesce(:searchString,'') = '' or (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or userdetails.email like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%)) ",
            nativeQuery = true)
    Page<User> getAllAttendeeByEvent(@Param("eventId") Long eventId,
                                     @Param("searchString") String searchString,
                                     @Param("sortParam") String sortParam,
                                     Pageable pageable);


    @Query(value = "select userdetails.user_id, userdetails.first_name, userdetails.last_name, userdetails.email, userdetails.photo from " +
            " (select  us.user_id, us.first_name, us.last_name, us.email, us.photo" +
            " from event_tickets as et" +
            " join ticketing_order as tord on et.ticket_order_id=tord.id" +
            " join users as us on us.user_id = et.holder_user_id" +
            " WHERE tord.event_id=:eventId" +
            " AND et.ticketing_type_id in :ticketTypeIds" +
            " AND et.ticket_status not in ('CANCELED','DELETED') " +
            " UNION" +
            " select  us.user_id, us.first_name, us.last_name, us.email, us.photo" +
            " from speakers as speaker" +
            " join users as us on us.user_id=speaker.user_id" +
            " WHERE speaker.event_id=:eventId " +
            " UNION" +
            " select  us.user_id, us.first_name, us.last_name, us.email, us.photo" +
            " from staff as staff" +
            " JOIN users AS us ON us.user_id=staff.user_id" +
            " WHERE staff.event_id=:eventId " +
            " AND staff.rec_status != 'DELETE' " +
            " AND staff.is_api_user = false) AS userdetails" +
            " WHERE (coalesce(:searchString,'') = '' or (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or userdetails.email like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%))" +
            " ORDER BY userdetails.first_name ",
            countQuery = "select count(distinct userdetails.user_id) from " +
                    " (select us.user_id, us.first_name, us.last_name, us.email from event_tickets as et" +
                    " join ticketing_order as tord on et.ticket_order_id=tord.id" +
                    " join users as us on us.user_id = et.holder_user_id" +
                    " WHERE tord.event_id=:eventId" +
                    " AND et.ticketing_type_id in :ticketTypeIds" +
                    " AND et.ticket_status not in ('CANCELED','DELETED') " +
                    " UNION" +
                    " select us.user_id, us.first_name, us.last_name, us.email from speakers as speaker" +
                    " join users as us on us.user_id=speaker.user_id" +
                    " WHERE speaker.event_id=:eventId " +
                    " UNION" +
                    " select us.user_id, us.first_name, us.last_name, us.email from staff as staff" +
                    " join users as us on us.user_id=staff.user_id" +
                    " where staff.event_id=:eventId " +
                    " AND staff.rec_status != 'DELETE' " +
                    " AND staff.is_api_user=false) AS userdetails" +
                    " WHERE (coalesce(:searchString,'') = '' or (userdetails.first_name like %:searchString% or userdetails.last_name like %:searchString% or userdetails.email like %:searchString% or CONCAT(userdetails.first_name,' ',userdetails.last_name) LIKE %:searchString%)) ",
            nativeQuery = true)
    Page<Object[]> getAllAttendeesByTicketType(@Param("eventId") Long eventId,
                                                      @Param("searchString") String searchString,
                                                      @Param("ticketTypeIds") List<Long> ticketTypeIds,
                                                      Pageable pageable);

	@Query(value = "SELECT COUNT(1) FROM " +
            " ( " +
                " SELECT us.* FROM event_tickets AS et " +
                " JOIN ticketing_order AS tord ON et.ticket_order_id=tord.id " +
                " JOIN users AS us ON us.user_id = et.holder_user_id " +
                " WHERE tord.event_id=:eventId " +
                " AND et.ticket_status not in ('CANCELED','DELETED') " +
                " UNION " +
                " SELECT us.* FROM speakers AS speaker " +
                " JOIN users AS us ON us.user_id=speaker.user_id " +
                " WHERE speaker.event_id=:eventId " +
                " UNION " +
                " SELECT us.* from staff AS staff " +
                " JOIN users AS us ON us.user_id=staff.user_id " +
                " WHERE staff.event_id=:eventId " +
                " AND staff.rec_status != 'DELETE' " +
                " AND staff.is_api_user=false " +
            " ) " +
            " AS userdetails", nativeQuery = true)
	Long countByEvent(@Param("eventId") Long eventId);

	@Cacheable(value = "findUserById", key = "#p0", condition="#p0!=null", unless="#result == null")
	Optional<User> findById(Long userId);

//	@Caching(evict = {
//			//@CacheEvict(value = "findUserByEmail", key = "#p0.oldEventURL", condition = "#p0.oldEventURL != null"),
//
//	})
	@CacheEvict(value = "findUserById", key = "#p0.userId")
	User save(User user);

    @Query(" SELECT new com.accelevents.dto.BasicUserEventBillingDto(user.userId, user.firstName,user.lastName,user.email) " +
    " FROM User user WHERE user.userId IN (:userIds) ")
    List<BasicUserEventBillingDto> findByUserIds(@Param("userIds") List<Long> userIds);

    @Query("SELECT new com.accelevents.session_speakers.dto.FirstLastNameDto(user.firstName,user.lastName)" +
            " FROM User user WHERE user.email=:email ")
    FirstLastNameDto getUserInfoByEmail(@Param("email") String email);

    Optional<User> findByAppleUserId(String appleUserId);

    @Query("Select user.pushNotificationToken FROM User user where user.userId IN (:userIds) AND user.pushNotificationToken is not null")
    List<String> findPushNotificationTokenByUserIdIn(@Param("userIds") List<Long> userIds);

    @Modifying
      @Query("UPDATE User user set user.phoneNumber=0 WHERE user.email NOT IN (:email) AND user.phoneNumber IN (:phoneNumber) and user.countryCode IN (:countryCode)")
    int updatePhoneNumber(@Param("email") String email,@Param("phoneNumber") long phoneNumber,@Param("countryCode") CountryCode countryCode);

    @Query("Select user.userId , CONCAT(user.firstName,' ',user.lastName) FROM User user where user.userId IN (:userIds)")
    List<Object[]> findUserNameByUserIdIn(@Param("userIds") List<Long> userIds);

    @Query("Select user FROM User user where user.firstName LIKE :startWith% AND user.signUpDate >= '2022-02-23 11:14:00' AND user.userId BETWEEN :from AND :to")
    List<User> findUserByFirstNameStartWithAndFromTo(@Param("startWith") String startWith, @Param("from") Long from, @Param("to") Long to);

    @Query("select new com.accelevents.common.dto.UserBasicDto(user.firstName, user.lastName, user.email)" +
            " FROM User user WHERE user.userId = :barcodeId ")
    Optional<UserBasicDto> findUserDetailsByBarcodeId(@Param("barcodeId") Long barcodeId);

    @Query(" SELECT new com.accelevents.common.dto.ChimeAttendeeProfileDTO(user.userId, user.firstName,user.lastName,user.photo,user.email) " +
            " FROM User user WHERE user.userId IN (:userIds) ")
    List<ChimeAttendeeProfileDTO> findUserByUserIds(@Param("userIds") List<Long> userIds);

    @Query(value = "select userdetails.* from " +
            " (select us.* from event_tickets as et" +
            " join ticketing_order as tord on et.ticket_order_id=tord.id" +
            " join users as us on us.user_id = et.holder_user_id" +
            " WHERE tord.event_id=:eventId" +
            " AND et.ticket_status not in ('CANCELED','DELETED') " +
            "  UNION" +
            " select us.* from speakers as speaker" +
            " join users as us on us.user_id=speaker.user_id" +
            " where speaker.event_id=:eventId" +
            " UNION" +
            " select us.* from staff as staff" +
            " join users as us on us.user_id=staff.user_id" +
            " where staff.event_id=:eventId AND staff.rec_status != 'DELETE' AND staff.is_api_user = false) AS userdetails " +
            " WHERE userdetails.user_id IN (:userIds) "
            , nativeQuery = true)
    List<User> findAttendeesByEventAndUserIds(@Param("eventId") Long eventId, @Param("userIds") List<Long> userIds);


    @Query(value = "SELECT et.ticketing_type_id, GROUP_CONCAT(us.user_id) " +
            " FROM event_tickets AS et " +
            " JOIN ticketing_order AS tord on et.ticket_order_id = tord.id " +
            " JOIN users AS us ON us.user_id = et.holder_user_id " +
            " WHERE tord.event_id =:eventId " +
            " AND et.ticketing_type_id IN :ticketTypeIds " +
            " AND et.ticket_status not in ('CANCELED','DELETED') " +
            " GROUP BY et.ticketing_type_id " +
            " UNION " +
            " SELECT -1, GROUP_CONCAT(us.user_id) " +
            " FROM speakers AS speaker " +
            " JOIN users AS us ON us.user_id = speaker.user_id " +
            " WHERE speaker.event_id =:eventId " +
            " UNION " +
            " SELECT -1, GROUP_CONCAT(us.user_id) " +
            " FROM staff AS staff " +
            " JOIN users AS us ON us.user_id = staff.user_id " +
            " WHERE staff.event_id =:eventId " +
            " AND staff.rec_status != 'DELETE' " +
            " AND staff.is_api_user = false", nativeQuery = true)
    List<Object[]> getUsersGroupByTicketTypeId(@Param("eventId") Long eventId, @Param("ticketTypeIds") List<Long> ticketTypeIds);

    @Query("select user FROM User user where user.email in (:email)")
    List<User> findUserByEmails(@Param("email") Set<String> email);
    @Query("Select user.userId FROM User user where user.userId IN (:userIds) AND user.pushNotificationToken is not null")
    List<Long> findUserIdByPushNotificationTokenNotNullAndUserIdIn(@Param("userIds") List<Long> userIds);

    @Query("select case when count(user) > 0 then true else false end from User user where user.email = :email")
    boolean isUserExistByEmail(@Param("email") String email);

    @Query("Select user FROM User user where user.userId IN (:userIds) AND user.pushNotificationToken is not null")
    List<User> findPushNotificationAndUserIdByUserId(@Param("userIds") List<Long> userIds);

    @Query(value =  " SELECT COUNT(DISTINCT u.user_id)  FROM " +
            " (SELECT DISTINCT ab.user_id FROM auction_bids ab JOIN events e ON ab.auction_id = e.auction_id WHERE e.event_id = :eventId " +
            " UNION " +
            " SELECT DISTINCT pr.user_id FROM purchased_raffle_tickets pr JOIN events e ON pr.raffle_id = e.raffle_id WHERE e.event_id = :eventId " +
            " UNION " +
            " SELECT DISTINCT p.user_id FROM pledge p JOIN events e ON p.cause_auction_id = e.event_id WHERE e.event_id = :eventId " +
            " UNION " +
            " SELECT DISTINCT d.user_id FROM donations d WHERE d.event_id = :eventId " +
            " UNION " +
            " SELECT DISTINCT et.ticket_purchaser_id FROM event_tickets et JOIN ticketing_order tor ON et.ticket_order_id = tor.id WHERE tor.event_id = :eventId " +
            " UNION " +
            " SELECT DISTINCT et.holder_user_id FROM event_tickets et JOIN ticketing_order tor ON et.ticket_order_id = tor.id WHERE tor.event_id = :eventId " +
            " ) x " +
            " JOIN users u ON u.user_id = x.user_id AND u.email IN (:emails)",nativeQuery = true)
    int countUsersPresentInEventByEventIdAndEmail(@Param("eventId") Long eventId,@Param("emails") Set<String> emails);

    @Query("select DISTINCT userId from User where email like '%accelevents.com' or " +
            "email = '<EMAIL>' or email like '%brilworks.com' or email like '%acceleventsREMOVED.com'")
    List<Integer> findAllUniqueBrilworksAndAccelEventsUserIds();

    @Query(value = "SELECT user_id " +
            "FROM staff " +
            "WHERE role IN ('admin', 'whitelabeladmin') AND event_id = :eventId " +
            "UNION " +
            "SELECT user_id " +
            "FROM join_users_with_organizers " +
            "WHERE (whitelabel_id = :whiteLabelId OR organizer_id = :organizerId ) ",
            nativeQuery = true)
    List<Long> getAllUserIdByEventIdAndWhiteLabelIdOrOrganizerId(
            @Param("eventId") Long eventId,
            @Param("whiteLabelId") Long whiteLabelId,
            @Param("organizerId") Long organizerId
    );

    @Query("SELECT user" +
            " FROM User user WHERE user.email IN (:emails) ")
    List<User> findUsersByEmailIn(@Param("emails") List<String> emailList);

    @Query(value = "SELECT distinct us.user_id FROM event_tickets et " +
            "JOIN ticketing_order AS tord on et.ticket_order_id=tord.id  " +
            "JOIN users AS us ON us.user_id = et.holder_user_id " +
            "JOIN ticket_holder_attributes tha ON tha.id = et.ticket_holder_attributes_id " +
            "WHERE tord.event_id=:eventId " +
            "AND (COALESCE(:filterTicketTypes) IS NULL OR et.ticketing_type_id in (:filterTicketTypes)) " +
            "AND (COALESCE(:filterStatus) IS NULL OR et.ticket_status in (:filterStatus)) " +
            "AND tord.order_status in ('PAID','UNPAID','PARTIAL','PROCESSING','PAYMENT_FAILED')",nativeQuery = true)
    List<Long> getUserListByEventTicketsAndStatus(@Param("eventId") Long eventId,@Param("filterStatus") List<String> filterStatus,@Param("filterTicketTypes") List<Long> filterTicketTypes);

    @Query("SELECT NEW com.accelevents.dto.UserAttendeeDTO(user.userId, user.firstName, user.lastName, user.email, user.photo) " +
            " FROM User user WHERE user.userId IN (:userIds) ")
    List<UserAttendeeDTO> getUserBasicDetailsByUserIds(@Param("userIds") List<Long> userIds);

    @Modifying
    @CacheEvict(value = "findUserById", allEntries = true)
    @Query("UPDATE User u SET u.mostRecentEventId = 0 WHERE u.mostRecentEventId = :eventId")
    void updateMostRecentEventIdZeroByEventId(@Param("eventId") long eventId);

    @Query("SELECT u FROM User u " +
            "JOIN EventTickets et ON et.holderUserId = u " +
            "WHERE et.eventId = :eventId " +
            "AND et.ticketStatus NOT IN ('DELETED', 'CANCELED') " +
            "AND NOT EXISTS (" +
            "    SELECT 1 FROM UserSession us " +
            "    WHERE us.userId = u.userId " +
            "    AND us.sessionId = :sessionId " +
            "    AND us.sessionStatus NOT IN ('BOOKMARKED')" +
            ") " +
            "AND (:search IS NULL OR :search = '' OR " +
            "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
            "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
            "LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%'))) " +
            "GROUP BY u.userId")
    Page<User> findAllAttendeesWithOnlyBookmarkedSessions(@Param("eventId") Long eventId, @Param("search") String search, Pageable pageable, Long sessionId);
}