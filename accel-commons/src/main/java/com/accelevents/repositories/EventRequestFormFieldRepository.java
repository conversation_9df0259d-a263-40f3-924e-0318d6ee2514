package com.accelevents.repositories;

import com.accelevents.domain.EventRequestFormField;
import com.accelevents.domain.enums.AttributeValueType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for EventRequestFormField entity
 */
@Repository
public interface EventRequestFormFieldRepository extends CrudRepository<EventRequestFormField, Long> {

    List<EventRequestFormField> findByEventRequestFormId(Long eventRequestFormId);

    @Query("SELECT COALESCE(MAX(f.position), 0) FROM EventRequestFormField f WHERE f.eventRequestFormId = :eventRequestFormId")
    Double findMaxPositionByFormId(@Param("eventRequestFormId") Long eventRequestFormId);

    Page<EventRequestFormField> findByEventRequestFormId(Long formId, Pageable pageable);

    @Query("SELECT f FROM EventRequestFormField f WHERE f.eventRequestFormId = :eventRequestFormId AND f.name LIKE %:search%")
    Page<EventRequestFormField> findByFormIdAndNameContaining(@Param("eventRequestFormId") Long eventRequestFormId, @Param("search") String search, Pageable pageable);

    boolean existsByEventRequestFormIdAndName(Long eventRequestFormId, String name);

    @Query("SELECT f.id FROM EventRequestFormField f WHERE f.eventRequestFormId = :eventRequestFormId")
    List<Long> findAllIdByEventRequestFormId(Long eventRequestFormId);

    @Query("SELECT f FROM EventRequestFormField f WHERE f.eventRequestFormId = :eventRequestFormId AND f.isHidden = false")
    List<EventRequestFormField> findByEventRequestFormIdAndExcludeHidden(@Param("eventRequestFormId") Long eventRequestFormId);

    @Query("SELECT f.id, f.name FROM EventRequestFormField f WHERE f.eventRequestFormId = :eventRequestFormId")
    List<Object[]> findAllIdAndNameByEventRequestFormId(@Param("eventRequestFormId") Long eventRequestFormId);

    @Query("SELECT f FROM EventRequestFormField f WHERE f.eventRequestFormId = :eventRequestFormId ORDER BY f.position ASC")
    List<EventRequestFormField> findByEventRequestFormIdOrderByPositionAsc(@Param("eventRequestFormId") Long eventRequestFormId);

    @Modifying
    @Query("UPDATE EventRequestFormField f SET f.position = f.position + :updateCount " +
            "WHERE f.eventRequestFormId = :formId")
    void updatePositionForAllFieldsByFormId(@Param("updateCount") double updateCount,
                                            @Param("formId") long formId);

    @Query("SELECT f FROM EventRequestFormField f " +
            "JOIN FETCH f.eventRequestForm erf " +
            "WHERE f.id = :id " +
            "AND f.eventRequestFormId = :eventRequestFormId " +
            "AND erf.whiteLabelId = :whiteLabelId ")
    Optional<EventRequestFormField> findByIdAndEventRequestFormIdAndWhiteLabelId(
            @Param("id") Long id,
            @Param("eventRequestFormId") Long eventRequestFormId,
            @Param("whiteLabelId") Long whiteLabelId);

    Optional<EventRequestFormField> findByEventRequestFormIdAndValueType(@Param("eventRequestFormId") Long eventRequestFormId, @Param("valueType") AttributeValueType valueType);

    @Query("SELECT f FROM EventRequestFormField f " +
            "JOIN f.eventRequestForm erf " +
            "WHERE erf.whiteLabelId = :whiteLabelId " +
            "AND erf.id IN :eventRequestFormIds " +
            "AND f.isEnabled = true " +
            "AND f.isHidden = false " +
            "ORDER BY f.position ASC")
    List<EventRequestFormField> findByEventRequestFormIdsOrderByPositionAsc(@Param("whiteLabelId") Long whiteLabelId,@Param("eventRequestFormIds") List<Long> eventRequestFormIds);

    List<EventRequestFormField> findByEventRequestFormIdAndNameContainingOrderByPositionAsc(@Param("eventRequestFormId") Long eventRequestFormId, @Param("search") String search);

}
