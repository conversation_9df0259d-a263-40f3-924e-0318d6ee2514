package com.accelevents.repositories;

import com.accelevents.common.dto.AttendeeAnalyticStatusDTO;
import com.accelevents.domain.*;
import com.accelevents.domain.TicketingOrder.OrderType;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.dto.stats.EventStats;
import com.accelevents.enums.StaffRole;
import com.accelevents.messages.TicketType;
import com.accelevents.perfomance.dto.TicketTypeReportData;
import com.accelevents.session_speakers.dto.AttendeeAnalyticsDTO;
import com.accelevents.session_speakers.dto.EventTicketsIdsDto;
import com.accelevents.session_speakers.dto.SpeakerEventTicketDto;
import com.accelevents.session_speakers.dto.SpeakerTicketTypeDTO;
import com.accelevents.staff.dto.StaffEventTicketDto;
import com.accelevents.staff.dto.StaffTicketTypeDto;
import com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO;
import com.accelevents.ticketing.dto.TicketAnalytics;
import com.accelevents.ticketing.dto.TicketingSale;
import com.accelevents.utils.Constants;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface EventTicketsRepository extends CrudRepository<EventTickets, Long> {

    @Query(value = "SELECT new com.accelevents.staff.dto.StaffEventTicketDto(et.id,ticketingType.id ,et.ticketingOrder.id,et.ticketingOrder.isStaffOrder) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " WHERE et.event = :event" +
            " AND et.holderUserId.userId = :userId " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND et.ticketStatus  not in ('CANCELED','DELETED')" +
            " ORDER BY et.id ASC")
    List<StaffEventTicketDto> findStaffTicketAndOrderIdByEventAndUserId(@Param("event") Event event, @Param("userId") Long userId);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " WHERE et.event = :event " +
            " AND et.holderUserId.userId = :userId " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND et.ticketingOrder.isStaffOrder = true " +
            " AND et.ticketStatus  not in ('CANCELED','DELETED') " +
            " ORDER BY et.id ASC")
    List<EventTickets> findAllByByEventAndUserIdAndTicketStatusAndIsStaffOrder(@Param("event") Event event, @Param("userId") Long userId);


    @Query(value = "SELECT new com.accelevents.staff.dto.StaffTicketTypeDto(et.holderUserId.userId,ticketingType.id,et.ticketingOrder.isStaffOrder,ticketingType.ticketTypeName) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " WHERE et.event = :event" +
            " AND et.holderUserId.userId IN (:userIds) " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND et.ticketStatus  not in ('CANCELED','DELETED') " +
            " ORDER BY et.id ASC")
    List<StaffTicketTypeDto> findStaffTicketsByEventAndUserIds(@Param("event") Event event, @Param("userIds") List<Long> userIds);

    @Query(value = "SELECT new com.accelevents.staff.dto.StaffTicketTypeDto(et.holderUserId.userId,ticketingType.id,et.ticketingOrder.isStaffOrder) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " WHERE et.event  = :event" +
            " AND et.holderUserId.userId IN (:userIds) " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND ticketingType.ticketTypeFormat <> 'IN_PERSON'"+
            " AND et.ticketStatus  not in ('CANCELED','DELETED') " +
            " ORDER BY et.id ASC")
    List<StaffTicketTypeDto> findStaffTicketsByEventAndUserIdsWithOutInPersonTicket(@Param("event") Event event, @Param("userIds") List<Long> userIds);

    @Query(    " SELECT new com.accelevents.dto.stats.EventStats(" +
            "     COUNT(1), " +
            "    SUM(et.paidAmount - et.refundedAmount)," +
            "   SUM(et.wlAFeeAmount+et.wlBFeeAmount+et.salesTaxFee-et.refundedWlAFee-et.refundedWlBFee-et.refundedSalesTaxFee) " +
            " ) " +
            " FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE  " +
            "  et.ticketStatus NOT IN (:ticketStatus)" +
            " AND et.dataType ='TICKET' " +
            " AND et.event = :event " )
    EventStats countByEventIdAndTicketStatusIdNotInAndStatusNot(@Param("event") Event event,
                                                                @Param("ticketStatus") List<TicketStatus> ticketStatus);


    @Query( " SELECT new com.accelevents.dto.stats.EventStats( et.eventId, " +
            "       COUNT(1), " +
            "       SUM(et.paidAmount - et.refundedAmount)," +
            "       SUM(et.wlAFeeAmount+et.wlBFeeAmount+et.salesTaxFee-et.refundedWlAFee-et.refundedWlBFee-et.refundedSalesTaxFee) " +
            " ) " +
            " FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE  " +
            "   et.ticketStatus NOT IN (:ticketStatus)" +
            "   AND et.dataType ='TICKET' " +
            "   AND et.eventId in :eventIds " +
            " GROUP BY et.eventId" )
    List<EventStats> countByEventIdsAndTicketStatusIdNotInAndStatusNot(@Param("eventIds") List<Long> eventIds, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH et.ticketHolderAttributesId AS ticketHolder " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder LEFT JOIN FETCH ticketOrder.ticketingCoupon AS coupon LEFT JOIN FETCH ticketOrder.trackingLinks " +
            " WHERE et.event=:event " +
            " AND ticketOrder.status IN(:status) " +
            " AND ticketOrder.purchaser=:purchaser  " +
            " AND ( et.recurringEventId = :recurringEventId OR 0 = :recurringEventId ) " +
            " AND et.dataType ='TICKET' " +
            " ORDER BY ticketOrder.id,  ticketingType.id ")
    List<EventTickets> findByEventIdAndTicketingStatusJoinFetch(@Param("event") Event event,
                                                                @Param("status") List<TicketingOrder.TicketingOrderStatus> status,
                                                                @Param("purchaser") User user,
                                                                @Param("recurringEventId") Long recurringEventId);

    @Query(value = "SELECT et FROM EventTickets AS et " +
            "JOIN FETCH et.ticketingTypeId AS ticketingType " +
            "LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            "WHERE et.event=:event " +
            "AND  et.dataType='TICKET' " +
            "AND (et.ticketStatus <>:ticketStatus)",
            countQuery =  "SELECT count(et) FROM EventTickets AS et " +
                    "WHERE et.event=:event " +
            " AND  et.dataType='TICKET'" +
            " AND (et.ticketStatus <>:ticketStatus)")
    Page<EventTickets> findByEventid(@Param("event") Event event, Pageable pageable, @Param("ticketStatus") TicketStatus ticketStatus);



    @Query(value = "SELECT et FROM EventTickets AS et " +
            "JOIN FETCH et.ticketingTypeId AS ticketingType " +
            "LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            "WHERE et.event=:event " +
            "AND  et.dataType='TICKET' " +
            "AND (et.ticketStatus <>:ticketStatus) " +
            "ORDER BY et.id ASC",
            countQuery =  "SELECT count(et) FROM EventTickets AS et " +
                    "WHERE et.event=:event " +
                    " AND  et.dataType='TICKET'" +
                    " AND (et.ticketStatus <>:ticketStatus)")
    Page<EventTickets> findByEventIdOrderByAsc(@Param("event") Event event, Pageable pageable, @Param("ticketStatus") TicketStatus ticketStatus);


    @Query(value = "SELECT new com.accelevents.dto.AccelFeesDTO(e.eventId,e.name,e.eventURL," +
            "e.organizerId,e.whiteLabelId, SUM(et.aeFeeAmount) - SUM(et.refundedAEFee)) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.event AS e " +
            " WHERE  et.createdAt between" +
            " :startDate and :endDate group by e.eventId ")
    List<AccelFeesDTO> findNetAETicketFeeEventWise(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    @Query("SELECT new com.accelevents.ticketing.dto.TicketingSale(SUM(et.paidAmount),COUNT(et.id)) " +
            " FROM EventTickets AS et JOIN et.ticketingOrder AS ticketingOrder " +
            " WHERE ticketingOrder.eventid=:event AND ticketingOrder.orderType=:orderType " +
            " AND et.dataType='TICKET'")
    TicketingSale getTicketingSale(@Param("event") Event event, @Param("orderType") OrderType orderType);


    @Query(" SELECT new com.accelevents.perfomance.dto.TicketTypeReportData(" +
            "ticketingType.recurringEventId,"+
            " ticketingType.id,ticketingType.ticketTypeName," +
            " COUNT(et.id)," +
            " SUM(CASE WHEN (et.ticketStatus in ('CANCELED','DELETED')) THEN 1 ELSE 0 END), " +
            " case when ticketingType.bundleType <>'INDIVIDUAL_TICKET' THEN (ticketingType.numberOfTickets*ticketingType.numberOfTicketPerTable) ELSE ticketingType.numberOfTickets END, "+
            " SUM(CASE WHEN (et.checkInDate IS NOT NULL AND et.ticketStatus = 'CHECKED_IN') THEN 1 ELSE 0 END ) , " +
            " SUM (CASE WHEN et.ticketStatus != 'DELETED' THEN (et.paidAmount - et.refundedAmount) ELSE 0 END) ) " +
            " FROM EventTickets et RIGHT JOIN et.ticketingTypeId AS ticketingType RIGHT JOIN ticketingType.ticketing AS ticketing " +
            " WHERE et.event =:event " +
            " AND ticketingType.dataType ='TICKET'" +
            " AND (ticketingType.recurringEventId=:recurringEventId OR 0 = :recurringEventId) "+
            " AND ticketingType.ticketType <>:donation" +
            " AND ticketingType.recurringEvent.status <> 'CANCEL'" +
            " GROUP BY et.recurringEventId,ticketingType.id,ticketingType.ticketTypeName HAVING ticketingType.id IS NOT NULL " )
    List<TicketTypeReportData> getAllTicketingReportData(@Param("event") Event event,
                                                         @Param("donation") TicketType donation,
                                                         @Param("recurringEventId") Long recurringEventId);

    @Query(" SELECT new com.accelevents.perfomance.dto.TicketTypeReportData(" +
            "ticketingType.recurringEventId,"+
            " ticketingType.id,ticketingType.ticketTypeName," +
            " COUNT(et.id)," +
            " SUM(CASE WHEN (et.ticketStatus in ('CANCELED','DELETED')) THEN 1 ELSE 0 END), " +
            " case when ticketingType.bundleType <>'INDIVIDUAL_TICKET' THEN (ticketingType.numberOfTickets*ticketingType.numberOfTicketPerTable) ELSE ticketingType.numberOfTickets END, "+
            " SUM(CASE WHEN (et.checkInDate IS NOT NULL AND et.ticketStatus = 'CHECKED_IN') THEN 1 ELSE 0 END ) , " +
            " SUM (CASE WHEN et.ticketStatus != 'DELETED' THEN (et.paidAmount - et.refundedAmount) ELSE 0 END) ) " +
            " FROM EventTickets et RIGHT JOIN et.ticketingTypeId AS ticketingType RIGHT JOIN ticketingType.ticketing AS ticketing " +
            " WHERE et.event =:event " +
            " AND ticketingType.dataType ='TICKET'" +
            " AND (ticketingType.status is NULL OR ticketingType.status <> 'CANCEL')" +
            " AND ticketingType.ticketType <>:donation" +
            " GROUP BY ticketingType.id,ticketingType.ticketTypeName HAVING ticketingType.id IS NOT NULL " )
    List<TicketTypeReportData> getAllTicketingReportData(@Param("event") Event event,
                                                         @Param("donation") TicketType donation );

    @Query(value = "SELECT  new com.accelevents.ticketing.dto.TicketAnalytics(" +
            " et.eventId," +
            " COUNT(et.id)," +
            " SUM(et.paidAmount),"+
            " SUM(et.refundedAmount)," +
            " SUM(et.aeFeeAmount + et.wlAFeeAmount + et.wlBFeeAmount + et.salesTaxFee), " +
            " SUM(et.refundedAEFee+et.refundedWlAFee + et.refundedWlBFee + et.refundedSalesTaxFee)," +
            " COUNT(DISTINCT ticketingOrder.id) , " +
            " SUM(CASE WHEN ticketingOrder.orderDate BETWEEN :from AND :to THEN 1 else 0 END)," +
            " SUM(CASE WHEN ticketingOrder.orderDate BETWEEN :from AND :to THEN (et.paidAmount) ELSE 0 END)," +
            " SUM(CASE WHEN ticketingOrder.orderDate BETWEEN :from AND :to THEN (et.refundedAmount) ELSE 0 END)," +
            " SUM(CASE WHEN ticketingOrder.orderDate BETWEEN :from AND :to THEN " +
            "              (et.aeFeeAmount + et.wlAFeeAmount + et.wlBFeeAmount + et.salesTaxFee)" +
            " ELSE 0 END)," +

            " SUM(CASE WHEN ticketingOrder.orderDate BETWEEN :from AND :to THEN " +
            " (et.refundedAEFee+et.refundedWlAFee + et.refundedWlBFee + et.refundedSalesTaxFee)"+
            " ELSE 0 END)," +
            " COUNT(DISTINCT CASE WHEN ticketingOrder.orderDate BETWEEN :from AND :to THEN ticketingOrder.id ELSE NULL END) , " +
            " SUM(CASE WHEN ticketingOrder.orderDate BETWEEN :prevFrom AND :prevTo THEN 1 else 0 END) ," +
            " stripe.CCFlatFee, stripe.CCPercentageFee )" +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.ticketingOrder AS ticketingOrder " +
            " JOIN et.event AS event" +
            " , Stripe stripe" +
            " WHERE stripe.event.eventId = event.eventId AND event.eventId IN (:eventIds) AND et.ticketStatus NOT IN (:ticketStatus) " +
            " AND et.dataType ='TICKET' " +
            " GROUP BY et.eventId")
    List<TicketAnalytics> getTicketingAnalyticsData(@Param("ticketStatus") List<TicketStatus> ticketStatus,
                                                    @Param("from") Date from,
                                                    @Param("to") Date to,
                                                    @Param("prevFrom") Date prevDateFrom,
                                                    @Param("prevTo") Date prevDateTo,
                                                    @Param("eventIds") List<Long> eventIds);

    @Query("SELECT count(distinct et) FROM EventTickets AS et JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event=:event AND" +
            "(et.ticketStatus =:ticketStatus AND ticketingType.ticketType IN (:ticketTypes))" +
            "AND et.ticketPaymentStatus not IN (:status) " +
            "AND et.dataType ='TICKET' " +
            "AND et.recurringEventId =:recurringEventId")
    Long countTicketsByTicketStatusRecurringEventId(@Param("event") Event event, @Param("ticketTypes") List<TicketType> ticketTypes, @Param("status") List<TicketPaymentStatus> status, @Param("ticketStatus") TicketStatus ticketStatus, @Param("recurringEventId") Long recurringEventId);

    @Query("SELECT count(distinct et) FROM EventTickets AS et JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event=:event AND" +
            "(et.ticketPaymentStatus =:status AND ticketingType.ticketType IN (:ticketTypes))" +
            "AND et.dataType ='TICKET' " +
            "AND et.recurringEventId =:recurringEventId")
    Long countTicketsByStatusRecurringEventId(@Param("event") Event event, @Param("ticketTypes") List<TicketType> ticketTypes, @Param("status") TicketPaymentStatus status,@Param("recurringEventId") Long recurringEventId);

    @Query("SELECT count(distinct et) FROM EventTickets AS et " +
            "JOIN et.ticketingTypeId AS ticketingType " +
            "WHERE et.event=:event AND " +
            "(ticketingType.ticketType =:ticketType) " +
            "AND et.ticketStatus <>:status " +
            "AND et.dataType ='TICKET' " +
            "AND et.recurringEventId=:recurringEventId")
    Long countTicketsByTicketTypeRecurringEventId(@Param("event") Event event, @Param("ticketType") TicketType ticketType,
                                                  @Param("status") TicketStatus status,@Param("recurringEventId") Long recurringEventId);

    @Query("  SELECT COUNT(1) FROM EventTickets et " +
            " WHERE et.recurringEventId = :recurringEventId " +
            "AND et.dataType ='TICKET' " +
            " AND et.ticketStatus NOT IN (:ticketStatus)")
    BigInteger countByRecurringEventIdNotRefundedAndNotDeleted(@Param("recurringEventId") Long recurringEventId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(value = "SELECT recurring_event_id " +
            " FROM event_tickets " +
            " WHERE recurring_event_id IN ( " +
            " SELECT id FROM recurring_events " +
            " WHERE recurring_event_schedule_id = :scheduleId"+
            " ) " +
            " AND data_type ='TICKET'" +
            " AND ticket_status not in ('CANCELED','DELETED')" +
            " GROUP BY event_tickets.recurring_event_id " +
            " HAVING COUNT(1)  > 0 ", nativeQuery = true)
    List<Object> getRecurringIdsHavingTicketSold(@Param("scheduleId") Long scheduleId);

    @Query("SELECT et.recurringEvents.id FROM EventTickets et WHERE et.recurringEvents=:recurringEvent " +
            " AND et.ticketStatus NOT IN (:ticketStatus) " +
            " AND et.dataType ='TICKET' " +
            " GROUP BY et.recurringEvents.id")
    List<Long> getSoldRecurringEventIds(@Param("recurringEvent") RecurringEvents recurringEvent,
                                        @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(value = "SELECT count(1) " +
            " FROM event_tickets " +
            " WHERE recurring_event_id IN ( :recurringEventId ) " +
            " AND data_type ='TICKET'" +
            " AND ticket_status not in ('CANCELED','DELETED') " +
            " GROUP BY event_tickets.recurring_event_id " +
            " HAVING COUNT(1)  > 0 ", nativeQuery = true)
    BigInteger getRecurringHavingTicketSold(@Param("recurringEventId") Long recurringEventId);


    @Query("SELECT new com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO(re.id," +
            " (case when ett.bundleType<>'INDIVIDUAL_TICKET' then (ett.numberOfTickets*ett.numberOfTicketPerTable) else ett.numberOfTickets END)," +
            " count(et.id) , re.eventCapacity) from RecurringEvents re JOIN TicketingType ett ON ett.recurringEventId=re.id" +
            " LEFT JOIN EventTickets et ON et.ticketingTypeId=ett" +
            " AND et.dataType='TICKET' AND et.ticketStatus NOT IN (:ticketStatus)" +
            " WHERE re.id IN (:recurringEvents)" +
            " GROUP BY re.id ")
    List<RecurringEventsTicketingTypeAndSoldCountDTO> findSoldCountByListOfRecurringList(@Param("recurringEvents") List<Long> recurringEvents,

                                                                                         @Param("ticketStatus") List<TicketStatus> ticketStatuses);

    @Query("SELECT new com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO(count(et.id) , re.eventCapacity) " +
            " from RecurringEvents re" +
            " LEFT JOIN EventTickets et ON et.recurringEventId=re.id" +
            " LEFT JOIN TicketingOrder tOrder ON tOrder.id=et.ticketingOrder" +
            " AND et.dataType='TICKET' AND et.ticketStatus NOT IN (:ticketStatus) AND tOrder.isStaffOrder=false AND tOrder.isSpeakerOrder=false" +
            " WHERE re.id IN (:recurringEvents)" +
            " GROUP BY re.id ")
    List<RecurringEventsTicketingTypeAndSoldCountDTO> findSoldCountByListOfRecurringListExcludingEventStaff(@Param("recurringEvents") List<Long> recurringEvents,
                                                                                         @Param("ticketStatus") List<TicketStatus> ticketStatuses);

    @Query("SELECT new com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO(re.id," +
            " count(et.id) ) from RecurringEvents re JOIN TicketingType ett ON ett.recurringEventId=re.id" +
            " LEFT JOIN EventTickets et ON et.ticketingTypeId=ett" +
            " LEFT JOIN TicketingOrder tOrder ON tOrder.id=et.ticketingOrder" +
            " AND et.dataType='TICKET' AND et.ticketStatus NOT IN (:ticketStatus) AND tOrder.isStaffOrder=false AND tOrder.isSpeakerOrder=false" +
            " WHERE re.eventId =:eventId " +
            " GROUP BY re.id ")
    List<RecurringEventsTicketingTypeAndSoldCountDTO> findSoldCountByForEachRecDateByEventId(@Param("eventId") Event eventId,
                                                                                         @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT new com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO(re.id," +
            " case when re.eventCapacity is null then false else (case when count(et.id) >= re.eventCapacity then true else false end ) end " +
            "  ) from RecurringEvents re JOIN TicketingType ett ON ett.recurringEventId=re.id" +
            " LEFT JOIN EventTickets et ON et.ticketingTypeId=ett" +
            " AND et.ticketStatus NOT IN (:ticketStatus)" +
            " WHERE re.eventId.eventId =:eventId" +
            " GROUP BY re.id ")
    List<RecurringEventsTicketingTypeAndSoldCountDTO> countNumberOfSoldTicketForRecurringEventsByEventId(@Param("eventId") Long eventId,
                                                                                                         @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(value = "select       " +
            "       et.recurring_event_id,       " +
            "       coalesce(sum(event_tickets.paid_amount-event_tickets.refunded_amount),0),       " +
            "       count(case when (et.ticket_bundle_type ='INDIVIDUAL_TICKET' and event_tickets.id > 0 ) then 1 else null end) ,       " +
            "       sum(case when (et.ticket_bundle_type !='INDIVIDUAL_TICKET' and event_tickets.id > 0 )  then 1 else 0 end) ,       " +
            "       (select coalesce(sum(number_of_tickets * if(ticket_bundle_type != 'INDIVIDUAL_TICKET', NUMBER_OF_TICKET_PER_TABLE, 1)),0) from event_ticket_type where recurring_event_id = et.recurring_event_id and rec_status is null)    " +
            "     from event_ticket_type as et left join event_tickets  on event_tickets.ticketing_type_id = et.id        " +
            "     where et.recurring_event_id in (:recurringEventIds) AND et.ticket_type <> 'DONATION' AND et.data_type ='TICKET' " +
            "     GROUP BY et.recurring_event_id", nativeQuery = true)
    List<Object []> getRecurringEventGrossSaleById(@Param("recurringEventIds") Set<Long> recurringEventIds);

    @Query("SELECT e.eventId, COUNT(ett.id) FROM EventTickets et JOIN et.ticketingTypeId AS ett, Event AS e " +
            " WHERE e.ticketingId = ett.ticketing.id AND e.eventId IN(:eventIds) " +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ett.dataType ='TICKET' AND ett.ticketType <> 'DONATION'" +
            " GROUP BY e.eventId")
    List<Object[]> getTicketSoldCount(@Param("eventIds") Set<Long> eventIds, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT new com.accelevents.dto.AttendeeSequenceNumbersDto(asn.id,et.id,et.holderFirstName," +
            " et.holderLastName,ticketingType.ticketTypeName,attendeeSequenceNumber.number,asn.sequenceName,asn.minNumberOfDigits) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " LEFT JOIN et.autoAssignedAttendeeNumber AS asn " +
            " LEFT JOIN AttendeeSequenceNumber As attendeeSequenceNumber ON et.id=attendeeSequenceNumber.eventTicketId " +
            " WHERE " +
            " et.ticketStatus <>:ticketStatus and et.event=:event" +
            " AND et.dataType ='TICKET'")
    List<AttendeeSequenceNumbersDto> getAttendeeWithAssignedSequences(@Param("event") Event event,@Param("ticketStatus") TicketStatus ticketStatus);

    @Query("SELECT new com.accelevents.dto.AttendeeSequenceNumbersDto(asn.id,et.id,et.holderFirstName," +
            " et.holderLastName,ticketingType.ticketTypeName,attendeeSequenceNumber.number,asn.sequenceName,asn.minNumberOfDigits) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " LEFT JOIN et.autoAssignedAttendeeNumber AS asn " +
            " LEFT JOIN AttendeeSequenceNumber As attendeeSequenceNumber ON et.id=attendeeSequenceNumber.eventTicketId " +
            " WHERE " +
            " et.ticketStatus <>:ticketStatus and et.event=:event" +
            " AND et.dataType ='TICKET'")
    Page<AttendeeSequenceNumbersDto> getAttendeeWithAssignedSequencesWithPagination(@Param("event") Event event,@Param("ticketStatus") TicketStatus ticketStatus, Pageable pageable);

    @Query("SELECT new com.accelevents.dto.AttendeeSequenceNumbersDto(asn.id,et.id,et.holderFirstName," +
            " et.holderLastName,ticketingType.ticketTypeName,attendeeSequenceNumber.number,asn.sequenceName,asn.minNumberOfDigits) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " LEFT JOIN et.autoAssignedAttendeeNumber AS asn " +
            " LEFT JOIN AttendeeSequenceNumber As attendeeSequenceNumber ON et.id=attendeeSequenceNumber.eventTicketId " +
            " WHERE " +
            " et.ticketStatus <>:ticketStatus and et.event=:event" +
            " AND et.dataType ='TICKET'" +
            " AND (et.holderFirstName LIKE %:searchString% OR et.holderLastName LIKE %:searchString% OR ticketingType.ticketTypeName LIKE %:searchString% OR asn.sequenceName LIKE %:searchString%)")
    Page<AttendeeSequenceNumbersDto> getAttendeeWithAssignedSequencesWithPaginationAndSearchString(@Param("event") Event event,@Param("ticketStatus") TicketStatus ticketStatus, @Param("searchString") String searchString, Pageable pageable);

    @Query("SELECT et From EventTickets et WHERE et.holderCountryCode is null AND et.holderPhoneNumber is not null AND et.dataType ='TICKET' AND et.eventId=:eventId")
    List<EventTickets> findByEventIdAndCountryCodeMissingAndPhoneNumberIsPresent(@Param("eventId") Long eventId);

    @Query("SELECT new com.accelevents.dto.EventTicketAndHolderBasicDto(et.id,et.holderFirstName, et.holderLastName) " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE ( et.holderUserId=:holderUserId) AND " +
            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.dataType ='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.event=:event ")
    List<EventTicketAndHolderBasicDto> getEventTicketIdAndHolderInfoByEventForRegisterSession(@Param("holderUserId") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus,@Param("event") Event event);

    @Query("  SELECT COUNT(1) FROM EventTickets et " +
            " JOIN TicketingOrder ticketingOrder ON et.ticketingOrder = ticketingOrder.id" +
            " WHERE ticketingOrder.eventid = :event " +
            " AND et.dataType ='TICKET'" +
            " AND et.ticketStatus NOT IN (:ticketStatus)")
    BigInteger countByEventTicketIdNotRefundedAndNotDeleted(@Param("event") Event event, @Param("ticketStatus") List<TicketStatus> ticketStatus);


    @Query("SELECT et From EventTickets et " +
            " WHERE et.ticketingOrder.id=:orderId " +
            " AND et.ticketingTypeId.id=:ticketTypeId " +
            " AND et.recurringEventId=:recurringEventId" +
            " AND et.dataType ='TICKET'")
    List<EventTickets> findByTicketingOrderAndTicketingTypeIdAndRecurringEvent(@Param("orderId") Long orderId,
                                                                               @Param("ticketTypeId") Long ticketTypeId,
                                                                               @Param("recurringEventId") Long recurringEventId);
    @Query("SELECT et From EventTickets et " +
            " WHERE et.ticketingOrder.id=:orderId " +
            " AND et.ticketingTypeId.id=:ticketTypeId ")
    List<EventTickets> findByTicketingOrderAndTicketingTypeId(@Param("orderId") Long orderId,
                                                                               @Param("ticketTypeId") Long ticketTypeId);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId as ticketTypeId " +
            " JOIN FETCH ticketTypeId.ticketing as ticketing" +
            " WHERE et.ticketingOrder.id=:ticketingOrderId" +
            " AND et.ticketPaymentStatus <> :status" +
            " AND et.ticketStatus =:ticketStatus" +
            " AND et.dataType ='TICKET'")
    List<EventTickets> findAllByTicketingOrderIdAndTicketStatusBooked(@Param("ticketingOrderId") long ticketingOrderId,@Param("ticketStatus") TicketStatus ticketStatus,@Param("status") TicketPaymentStatus status);

    @Modifying
    @Query("UPDATE EventTickets SET ticketStatus = :ticketStatus, checkInStaff =:checkInStaff, checkInDate =:date WHERE id IN (:eventTicketIds)")
    void updateTicketStatusCheckInStaffAndCheckInDateByEventTickets(@Param("eventTicketIds") List<Long> eventTicketIds,@Param("ticketStatus") TicketStatus ticketStatus,
                                                                    @Param("checkInStaff") User checkInStaff,@Param("date") Date date);

    @Query("  SELECT COUNT(1) FROM EventTickets et " +
            " JOIN et.recurringEvents AS recurringEvents" +
            " WHERE recurringEvents.recurringEventScheduleId = :recurringEventScheduleId " +
            " AND et.dataType ='TICKET' " +
            " AND et.ticketStatus NOT IN (:ticketStatus)")
    BigInteger countSoldTicketForSchedule(@Param("recurringEventScheduleId") Long recurringEventScheduleId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Modifying
    @Query("UPDATE EventTickets SET recordStatus = :status WHERE recurringEventId IN (:recurringEventIds) AND dataType='TICKET'")
    void updateEventTicketsRecStatusByRecurringEventIds(@Param("recurringEventIds") List<Long> recurringEventIds, @Param("status") RecordStatus status);

    @Query("SELECT et FROM EventTickets AS et " +
            "JOIN FETCH et.ticketingTypeId AS ticketingType " +
            "JOIN FETCH et.ticketingOrder AS ticketingOrder " +
            "JOIN FETCH et.holderUserId AS userId " +
            "WHERE et.eventId=:eventId AND et.barcodeId=:barcodeId ")
    EventTickets findByBarcodeIdAndEventId(@Param("barcodeId") String barcodeId,@Param("eventId") Long eventId);

    @Query("SELECT et FROM EventTickets AS et " +
            "JOIN FETCH et.ticketingTypeId AS ticketingType " +
            "JOIN FETCH et.ticketingOrder AS ticketingOrder " +
            "JOIN FETCH et.holderUserId AS userId " +
            "WHERE et.eventId=:eventId AND et.rfidId=:rfIdTag " +
            " AND et.ticketStatus not in ('CANCELED','DELETED') ")
    EventTickets findByRfidTagAndEventId(@Param("rfIdTag") String rfIdTag, @Param("eventId") Long eventId);

    @Query("SELECT et FROM EventTickets AS et " +
            "WHERE et.eventId=:eventId AND et.rfidId=:rfIdTag " +
            " AND et.ticketStatus not in ('CANCELED','DELETED') ")
    EventTickets findByRfidTagAndEventIdWithoutJoin(@Param("rfIdTag") String rfIdTag, @Param("eventId") Long eventId);

    @Query("SELECT et FROM EventTickets AS et " +
            "WHERE et.eventId=:eventId AND et.barcodeId=:barcodeId " +
            " AND et.ticketStatus not in ('CANCELED','DELETED') ")
    EventTickets findByBarcodeIdAndEventIdWithoutJoin(@Param("barcodeId") String barcodeId, @Param("eventId") Long eventId);


    @Query("SELECT et FROM EventTickets AS et " +
            "WHERE et.ticketingOrderId=:ticketingOrderId " +
            "AND et.dataType='ADDON'")
    List<EventTickets> findEventTicketsByTicketingOrderId(@Param("ticketingOrderId")Long ticketingOrderId);

    @Query("SELECT holderUserId.userId FROM EventTickets WHERE id=:eventTicketId")
    Long findHolderUserIdById(@Param("eventTicketId") Long eventTicketId);

    @Query("SELECT et.id FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId = :eventId" +
            " AND et.ticketStatus NOT IN (:ticketStatus) " +
            " AND et.dataType ='TICKET' " +
            " AND et.holderUserId.userId=:userId " +
            " AND ticketingType.ticketType <> 'DONATION'")
    List<Long> findByHolderUserIdAndEventId(@Param("userId") Long userId,
                                            @Param("eventId") Long eventId,
                                            @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT new com.accelevents.session_speakers.dto.EventTicketsIdsDto(et.id,et.ticketingTypeId.id) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId = :eventId" +
            " AND et.ticketStatus NOT IN (:ticketStatus) " +
            " AND et.dataType ='TICKET' " +
            " AND et.holderUserId.userId=:userId " +
            " AND ticketingType.ticketType <> 'DONATION'" )
    List<EventTicketsIdsDto> findEventTicketIdAndTicketingTypeIdByHolderUserIdAndEventId(@Param("userId") Long userId,
                                                                                         @Param("eventId") Long eventId,
                                                                                         @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT new com.accelevents.session_speakers.dto.EventTicketsIdsDto(et.id,et.ticketingTypeId.id) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId = :eventId" +
            " AND et.ticketStatus NOT IN (:ticketStatus) " +
            " AND et.dataType ='TICKET' " +
            " AND et.ticketPurchaserId.userId=:userId " +
            " AND ticketingType.ticketType <> 'DONATION'")
    List<EventTicketsIdsDto> findEventTicketIdAndTicketingTypeIdByPurchaserUserIdAndEventId(@Param("userId") Long userId,
                                                                                         @Param("eventId") Long eventId,
                                                                                         @Param("ticketStatus") List<TicketStatus> ticketStatus);
    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event = :event" +
            " AND et.holderUserId=:user " +
            " AND et.ticketStatus <> 'DELETED' ORDER BY et.id ASC")
    Page<EventTickets> findEventTicketsByHolderUserIdAndEventId(@Param("user") User user,
                                                                @Param("event") Event event, Pageable pageable);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event = :event" +
            " AND et.holderUserId=:user " +
            " AND et.ticketStatus <> 'DELETED' ORDER BY et.id ASC")
    List<EventTickets> findAllEventTicketsByHolderUserIdAndEventId(@Param("user") User user,
                                                                @Param("event") Event event);

    @Query("SELECT CASE WHEN COUNT(et.id) > 0 THEN true ELSE false END FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND (et.holderUserId = :user or et.ticketPurchaserId = :user) AND et.ticketStatus NOT IN (:ticketStatus) AND et.dataType = 'TICKET' ")
    boolean userAlreadyPurchasedTicketInEvent(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et.holderUserId.email FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND et.dataType = 'TICKET' " +
            " AND et.guestOfBuyer = false")
    Set<String> getAllHoldersEmailByEventAndExcludeGuestOfBuyer(@Param("event") Event event, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et.id FROM EventTickets AS et  WHERE et.ticketingTypeId =:ticketingTypeId")
    List<Long> getEVentTicketsIdByTicketingTypeId(@Param("ticketingTypeId") TicketingType ticketingTypeId);

    @Query("SELECT et.holderUserId FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId IN (:users) AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' AND ticketingType.ticketTypeFormat <> 'IN_PERSON' ")
    List<User> checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(@Param("event") Event event, @Param("users") Set<User> users, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.holderUserId AS userId " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " JOIN FETCH ticketingType.ticketing AS ticketing " +
            " JOIN FETCH et.ticketingOrder AS ticketOrder " +
            " WHERE et.ticketingOrderId=:ticketingOrderId and et.ticketStatus <> 'DELETED' ")
    List<EventTickets> findByTicketingOrder(@Param("ticketingOrderId") Long ticketingOrderId);

    @Query("SELECT et FROM EventTickets et JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId=:eventId " +
            " AND et.ticketStatus=:ticketStatus " +
            " ORDER BY et.id DESC ")
    List<EventTickets> findLatestCheckedInEventTicketsByEventId(@Param("eventId") Long eventId,
                                                                @Param("ticketStatus") TicketStatus ticketStatus,
                                                                Pageable pageable);

    @Query("SELECT ticketingType.id FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user" +
            " AND et.ticketStatus NOT IN (:ticketStatus) " +
            " AND ticketingType.ticketType <> 'DONATION'" +
            " AND ticketingType.ticketTypeFormat <> 'IN_PERSON'")
    List<Long> getEventTicketTypeIdsByEventUserANDNotCanceled(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT ticketingType.id FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user" +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' ")
    List<Long> getEventTicketTypeIdsByEventUserANDNotCanceledAndAllFormates(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);


    @Query("SELECT ticketingType.id FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user" +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' ")
    List<Long> getAllEventTicketTypeIdsByEventUserANDNotCanceled(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT DISTINCT(ticketingType.id) FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.eventId = :eventId" +
            " AND et.userId = :userId " +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION'")
    List<Long> getAllEventTicketTypeIdsByEventIdUserIdAndNotCanceled(@Param("eventId") Long eventId, @Param("userId") Long userId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et.userId, ticketingType.id FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.eventId = :eventId" +
            " AND et.userId IN (:userId) " +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION'  GROUP BY ticketingType.id, et.userId ")
    List<Object[]> getAllEventTicketTypeIdsByEventIdUserIdInAndNotCanceled(@Param("eventId") Long eventId, @Param("userId") List<Long> userId, @Param("ticketStatus") List<TicketStatus> ticketStatus);


    @Query("SELECT et FROM EventTickets AS et" +
            " JOIN FETCH et.holderUserId AS holderUser"+
            " JOIN et.ticketingTypeId AS ticketingType" +
            " JOIN ticketingType.ticketing AS ticketing WHERE et.id = :eventTicketId " +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' ")
    EventTickets getEventTicketByEventTicketIdAndNotCanceled(@Param("eventTicketId") Long eventTicketId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets AS et" +
            " JOIN FETCH et.holderUserId AS holderUser"+
            " JOIN et.ticketingTypeId AS ticketingType" +
            " JOIN ticketingType.ticketing AS ticketing WHERE et.barcodeId = :barcodeId " +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' ")
    EventTickets getEventTicketByBarcodeIdAndNotCanceled(@Param("barcodeId") String barcodeId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets AS et" +
            " JOIN FETCH et.holderUserId AS holderUser"+
            " JOIN et.ticketingTypeId AS ticketingType" +
            " JOIN ticketingType.ticketing AS ticketing WHERE et.rfidId = :rfIdTag " +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' " +
            " AND et.eventId = :eventId " )
    EventTickets getEventTicketByRfIdTagAndNotCanceled(@Param("rfIdTag") String rfIdTag, @Param("ticketStatus") List<TicketStatus> ticketStatus, @Param("eventId") Long eventId);

    @Query("SELECT ticketingType.id FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user" +
            " AND (et.ticketingTypeId.isLoungesRestricted <> true OR et.ticketingTypeId.isLoungesRestricted= null)" +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' ")
    List<Long> getAllEventTicketTypeIdsWithoutLoungeRestrictedTicketsByEventUserANDNotCanceled(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT ticketingType.id FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user" +
            " AND (et.ticketingTypeId.isExpoRestricted <> true OR et.ticketingTypeId.isExpoRestricted= null)" +
            " AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' ")
    List<Long> getAllEventTicketTypeIdsWithoutExpoRestrictedTicketsByEventUserANDNotCanceled(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT ticketHolder FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.ticketHolderAttributesId AS ticketHolder " +
            " WHERE et.event=:event " +
            " AND et.holderUserId=:user ")
    Page<TicketHolderAttributes> getTicketHolderAttributeByUserAndEvent(@Param("user")User user, @Param("event") Event event, Pageable pageable);


    @Query(value = " SELECT ticketOrder.id " +
            " FROM EventTickets AS eventTicket " +
            " JOIN eventTicket.ticketingOrder AS ticketOrder "+
            " WHERE ticketOrder.eventid=:event " +
            " AND eventTicket.id=:eventTicketId" )
    Long findTicketingOrderIdByEventTicketIdAndEvent(@Param("eventTicketId") Long eventTicketId,@Param("event") Event event);

    @Query(value = "select sum(case when (et.ae_fee_amount< :feesPerAttendee ) then (:feesPerAttendee - et.ae_fee_amount) else  0 end) " +
            "  from event_tickets et JOIN event_ticket_type ett ON et.ticketing_type_id = ett.id " +
            "  where ett.ticketing_id =:ticketingId" +
            "  and (ett.ticket_type_format in ('VIRTUAL','HYBRID') OR ett.ticket_type_format is NULL )" +
            "  and et.checkin_source in ('VIRTUAL_EVENT_PORTAL','HYBRID_CHECKIN')" +
            "  and ett.ticket_type='PAID' " +
            "  and et.ticket_status not in ('CANCELED') " +
            "  and et.check_in_date IS NOT NULL", nativeQuery = true)
    BigDecimal getSumOfAEFeesOfPaidTicketsToBeCharge(@Param("ticketingId") Long ticketingId,@Param("feesPerAttendee") double feesPerAttendee);

    //To get Max paid ticket's AE charges for distinct user
    @Query(value = "select  max(paid_amount) , et.ae_fee_amount   from event_tickets et JOIN event_ticket_type ett ON et.ticketing_type_id = ett.id " +
            "  where ett.ticketing_id =:ticketingId" +
            " and (ett.ticket_type_format in ('VIRTUAL','HYBRID') OR ett.ticket_type_format is NULL )" +
            "  and et.checkin_source in ('VIRTUAL_EVENT_PORTAL','HYBRID_CHECKIN')" +
            " and ett.ticket_type='PAID' " +
            " and et.ticket_status not in ('CANCELED') " +
            " and et.check_in_date IS NOT NULL group by et.holder_user_id", nativeQuery = true)
    List<Object[]> getAEFeesOfPaidTicketsToBeChargeForDistinctUser(@Param("ticketingId") Long ticketingId);


    @Query(value = "SELECT et FROM EventTickets et " +
            "       JOIN FETCH et.holderUserId AS userId " +
            "       JOIN FETCH et.ticketingTypeId AS ticketingType " +
            "       JOIN FETCH et.ticketingOrder AS ticketOrder " +
            "       LEFT JOIN FETCH ticketOrder.ticketingCoupon AS coupon " +
            "       WHERE et.event = :event " +
            "       AND et.ticketStatus <> 'DELETED' " +
            "       AND ticketOrder.status <> 'UNPAID_DELETE' " +
            "       AND ticketOrder.status <> 'PAID_DELETE' ")
    List<EventTickets> getEventTicketsByEvent(@Param("event") Event event);

    @Query(value = "select case when (et.ae_fee_amount< :feesPerAttendee ) then (:feesPerAttendee - et.ae_fee_amount) else  0 end " +
            "  from event_tickets et where  et.id=:id ", nativeQuery = true)
    BigDecimal getAEFeesOfPaidTicketsToBeCharge(@Param("feesPerAttendee") double feesPerAttendee,@Param("id")long eventTicketId);

    @Query("select ticketingType.isChatRestricted " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.holderUserId=:holderUserId AND " +
            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.dataType ='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.event=:event ")
    List<Boolean> getUserChatRestrictions(@Param("holderUserId") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus, @Param("event") Event event);


    @Query("SELECT DISTINCT ticketingType.ticketTypeName " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.holderUserId=:holderUserId AND " +
            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.id =:eventTicketId AND " +
            " et.event=:event ")
    List<String> getUserTicketTypeName(@Param("holderUserId") User user,
                                       @Param("ticketStatus") List<TicketStatus> ticketStatus,
                                       @Param("event") Event event, Long eventTicketId);

    @Query("SELECT DISTINCT ticketingType.ticketTypeName " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.holderUserId=:holderUserId AND " +

            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.dataType ='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.event=:event ")
    List<String> getUserTicketTypeName(@Param("holderUserId") User user,
                                       @Param("ticketStatus") List<TicketStatus> ticketStatus,
                                       @Param("event") Event event);

    @Query("SELECT DISTINCT ticketingType.id " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.holderUserId.userId=:userId AND " +
            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.dataType ='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.event=:event ")
    List<Long> getSuperAdminTicketIds(@Param("userId") long id, @Param("ticketStatus") List<TicketStatus> ticketStatus, @Param("event") Event event);

    @Query("SELECT CASE WHEN COUNT(et.id) > 0 THEN true ELSE false END FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.holderEmail =:email" +
            " AND et.ticketingOrderId =:orderId AND ticketingType.ticketTypeFormat =:ticketTypeFormat AND et.ticketStatus <> 'DELETED'")
    boolean checkUserHadPurchasedVirtualTicketInOrder(@Param("orderId") Long orderId, @Param("email") String email, @Param("ticketTypeFormat") TicketTypeFormat ticketTypeFormat);

    @Query(" SELECT new com.accelevents.session_speakers.dto.AttendeeAnalyticsDTO(" +
            "CASE WHEN u.firstName IS NOT NULL THEN u.firstName ELSE '' END," +
            "CASE WHEN u.lastName IS NOT NULL THEN u.lastName ELSE '' END," +
            "CASE WHEN u.email IS NOT NULL THEN u.email ELSE '' END," +
            "ss.id," +
            "et.holderUserId.userId," +
            "CASE WHEN us.checkInTime IS NULL AND us.sessionStatus IS NOT NULL THEN 'REGISTERED' ELSE (CASE WHEN us.checkInTime IS NOT NULL THEN 'JOINED' ELSE '' END) END" +
        ")" +
        " FROM EventTickets AS et " +
        " JOIN TicketingType AS ett ON ett.id = et.ticketingTypeId.id AND (et.recordStatus<>'CANCEL' OR et.recordStatus IS NULL) " +
        " JOIN Session AS ss ON et.eventId = ss.eventId and ss.recordStatus<>'DELETE'" +
        " JOIN User AS u ON et.holderUserId.userId = u.userId "+
        " LEFT JOIN UserSession AS us ON et.holderUserId.userId = us.userId AND et.eventId=us.eventId AND ss.id=us.sessionId AND us.recordStatus<>'DELETE' AND us.userId = u.userId " +
        " WHERE et.eventId =:eventId GROUP BY ss.id,et.holderUserId.userId ORDER BY ss.id ASC ")
    List<AttendeeAnalyticsDTO> findAllAttendeeSessionsDetailsByEventId(@Param("eventId") Long eventId);


    @Query("  SELECT DISTINCT et.ticketingTypeId.id FROM EventTickets et " +
            " JOIN et.ticketingOrder AS ticketingOrder" +
            " WHERE ticketingOrder.ticketingCoupon.id = :ticketCouponId " +
            " AND et.ticketingTypeId.id IN (:ticketingTypeId)" +
            " AND et.ticketStatus <> 'DELETED'" +
            " AND et.dataType ='TICKET' " )
    List<Long> countSoldTicketForTicketTypeIdAndCouponCOde(@Param("ticketingTypeId") List<Long> ticketingTypeId , @Param("ticketCouponId") long ticketCouponId);

    @Query(value = "SELECT sum(number_of_tickets) FROM event_ticket_type et " +
            "where ticketing_id in " +
            "(SELECT id FROM ticketing a " +
            "inner join events e on a.event_id=e.event_id " +
            "where e.event_id in (:eventIds) and e.event_status is null ) and et.rec_status is null",nativeQuery = true)
    List<BigDecimal> countTotalTicketsByTicketType(@Param("eventIds") Set<Long> eventIds);

    @Query("SELECT CASE WHEN COUNT(et.id) > 0 THEN true ELSE false END FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user AND et.ticketStatus NOT IN (:ticketStatus) AND ticketingType.ticketType <> 'DONATION' AND ticketingType.ticketTypeFormat <> 'VIRTUAL' ")
    boolean checkUserHadPurchasedInPersonTicketInEvent(@Param("event") Event event, @Param("user") User user, @Param("ticketStatus") List<TicketStatus> ticketStatus);


    @Query("SELECT COUNT(DISTINCT et.ticketPurchaserId.userId) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId = :eventId" +
            " AND et.ticketStatus NOT IN ('CANCELED','DELETED') " +
            " AND et.dataType ='TICKET' " +
            " AND ticketingType.ticketType <> 'DONATION'" +
            " AND ticketingType.id IS NOT NULL" +
            " AND et.ticketPurchaserId.userId NOT IN (" +  // We are not counting Admin and Staff users if they purchase tickets
            " SELECT DISTINCT staff.userId" +              // Because in Early bird challenge only attendees can participate
            " FROM Staff staff" +
            " WHERE staff.role IN (:roles)" +
            " AND staff.eventId =:eventId )" +
            " AND et.createdAt >=:createdAt")
    Long getTicketPurchaserCountByEventAndCreatedAfter(@Param("eventId") Long eventId,
                                                  @Param("createdAt") Date createdAt,
                                                  @Param("roles") List<StaffRole> staffRoles);


    @Query("SELECT CASE WHEN COUNT(ticketingType.id) > 0 THEN true ELSE false END " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.holderUserId=:holderUserId AND " +
            " et.ticketStatus NOT IN (:ticketStatus) AND " +
            " et.dataType ='TICKET' AND " +
            " ticketingType.ticketType <> 'DONATION' AND " +
            " et.event=:event ")
    boolean checkIsStaffPurchaseTicket(@Param("holderUserId") User user,
                                       @Param("ticketStatus") List<TicketStatus> ticketStatus,
                                       @Param("event") Event event);

    @Query(value = "SELECT new com.accelevents.session_speakers.dto.SpeakerTicketTypeDTO(et.holderUserId.userId,ticketingType.id,et.ticketingOrder.isSpeakerOrder) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " WHERE et.event = :event" +
            " AND et.holderUserId.userId IN (:userIds) " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND et.ticketStatus <> 'DELETED' ORDER BY et.id ASC")
    List<SpeakerTicketTypeDTO> findUsersTicketsByEventAndUserIds(@Param("event") Event event, @Param("userIds") List<Long> userIds );

    @Query(value = "SELECT new com.accelevents.session_speakers.dto.SpeakerTicketTypeDTO(et.holderUserId.userId,ticketingType.id ,et.ticketingOrder.isSpeakerOrder) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " WHERE et.event = :event" +
            " AND et.holderUserId.userId = :userId " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND et.ticketStatus NOT IN (:ticketStatus) ORDER BY et.id ASC")
    List<SpeakerTicketTypeDTO> findUserTicketsByEventAndUserId(@Param("event") Event event, @Param("userId") Long userId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(value = "SELECT new com.accelevents.session_speakers.dto.SpeakerEventTicketDto(et.id,ticketingType.id ,et.ticketingOrder.id,et.ticketingOrder.isSpeakerOrder) FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " WHERE et.event = :event" +
            " AND et.holderUserId.userId = :userId " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND et.ticketStatus NOT IN (:ticketStatus) ORDER BY et.id ASC")
    List<SpeakerEventTicketDto> findUserTicketAndOrderIdByEventAndUserId(@Param("event") Event event, @Param("userId") Long userId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType "+
            " JOIN FETCH et.ticketHolderAttributesId" +
            " WHERE et.event = :event " +
            " AND et.holderUserId.userId = :userId " +
            " AND ticketingType.ticketType = 'FREE' " +
            " AND et.ticketingOrder.isSpeakerOrder = true " +
            " AND et.ticketStatus NOT IN (:ticketStatus) ORDER BY et.id ASC")
    List<EventTickets> findAllByEventAndUserIdAndTicketStatusAndIsSpeakerOrder(@Param("event") Event event, @Param("userId") Long userId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("select new com.accelevents.common.dto.AttendeeAnalyticStatusDTO(et.ticketStatus, COUNT(et.id)) " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event =:event " +
            " AND et.dataType='TICKET' " +
            " AND ticketingType.ticketType <> 'DONATION' " +
            " AND et.ticketStatus in (:ticketStatuses) " +
            " GROUP BY et.ticketStatus ")
    List<AttendeeAnalyticStatusDTO> getAttendeesCountByEventByTicketStatus(@Param("event") Event event, @Param("ticketStatuses") List<TicketStatus> ticketStatuses);


    List<EventTickets> findByRecurringEventId(Long recurringEventId);

    @Query("SELECT et.recurringEventId,COUNT(et.id) FROM EventTickets et WHERE " +
            " et.ticketStatus NOT IN (:ticketStatus) " +
            "AND et.recurringEventId IN (:recurringEventIdList) GROUP BY et.recurringEventId")
    List<Object[]> countSoldTicketsByRecurringEventId(@Param("recurringEventIdList") List<Long> recurringEventIdList, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets as et " +
            "JOIN FETCH et.ticketingOrder as ticketingOrder " +
            "JOIN FETCH et.ticketingTypeId as ticketingTypeId " +
            "JOIN FETCH ticketingTypeId.ticketing AS ticketing " +
            "JOIN FETCH ticketingTypeId.recurringEvent AS recurringEvent " +
            "JOIN FETCH et.recurringEvents AS recurringEvent " +
            "JOIN FETCH recurringEvent.recurringEventSchedule AS recurringEventSchedule " +
            "WHERE et.ticketStatus =:ticketStatus AND et.recurringEventId IN (:recurringEventIdList)")
    List<EventTickets> findByTicketStatusRefundesAndDelete(@Param("recurringEventIdList") List<Long> recurringEventIdList, @Param("ticketStatus") TicketStatus ticketStatus);

    @Query("SELECT CASE WHEN COUNT(et.id) > 0 THEN true ELSE false END FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user AND et.isAgreeDisclaimer = 1")
    boolean checkUserHasAgreedDisclaimer(@Param("event") Event event, @Param("user") User user);

    @Query("SELECT distinct et.isAgreeEventHubDisclaimerStatus FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.holderUserId = :user and et.isAgreeEventHubDisclaimerStatus is not null ")
    List<Constants.VirtualEventHubDisclaimer> checkUserHasAgreedEventHubDisclaimer(@Param("event") Event event, @Param("user") User user);

    @Query(value = "SELECT user.userId FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType JOIN et.holderUserId AS user" +
            " WHERE et.event = :event AND ticketingType.id IN :ticketingTypeIdsIn" +
            " AND et.ticketStatus NOT IN (:ticketStatus)" +
            " ORDER BY et.id ASC")
    List<Long> findAllUserIdByEvent(@Param("event") Event event,@Param("ticketingTypeIdsIn") List<Long> ticketingTypeIdsIn, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Transactional
    @Modifying
    @Query("DELETE FROM EventTickets WHERE ticketingTypeOnlyId IN(:ticketTypesList)")
    void deleteAllByTicketingTypeIds(@Param("ticketTypesList")List<Long> ticketTypesList);

    // We need to Make this as JPQL atfer Hubspot upate properties DataFix executed and add TicketStatus enum as param
    @Query(value = "SELECT COUNT(et.id) FROM event_tickets et" +
            " WHERE  et.data_type ='TICKET' AND et.ticket_status not in ('CANCELED','DELETED') " +
            "  AND et.event_id = :eventId", nativeQuery = true)
    BigInteger countDistinctTicketHolderByEvent(@Param("eventId") Long eventId);

    @Query(value = "SELECT new com.accelevents.dto.AccelFeesDTO(e.eventId,e.name,e.eventURL," +
            "e.organizerId,e.whiteLabelId, SUM(et.aeFeeAmount) - SUM(et.refundedAEFee)) FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN ticketingType.ticketing AS ticketing " +
            " JOIN et.event AS e " +
            " WHERE e.eventId between :from and :to and ticketing.eventEndDate between :startDate and :endDate group by e.eventId ")
    List<AccelFeesDTO> findOldNetAETicketFeeEventWise(@Param("from") Long form, @Param("to") Long to,
                                                      @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Query("SELECT et.ticketingTypeId.id,et.ticketingTypeId.maxSessionRegisterUser From EventTickets et where et.id = :eventTicketId")
    List<Object[]> findTicketingTypeById(@Param("eventTicketId") Long eventTicketId);

    @Query("SELECT ticketOrder FROM EventTickets AS eventTicket " +
            "JOIN eventTicket.ticketingOrder AS ticketOrder "+
            "WHERE eventTicket.ticketingOrder.id=:orderId AND eventTicket.holderUserId.userId=:userId AND eventTicket.id=:eventTicketingId")
    TicketingOrder findByIdAndPurchaserIdAndTicketingId(@Param("orderId") long orderId, @Param("userId") Long userId, @Param("eventTicketingId") Long eventTicketingId );


    //Moved to RO service ROAudienceFilterEventTicketRepo remove this method.
    @Query(value = "SELECT userId, eventName, utm, registerDate " +
            " FROM ( " +
                "SELECT et.id AS id, et.holder_user_id AS userId, e.name AS eventName, tord.rec_source AS utm, et.created_at as registerDate " +
                "FROM event_tickets et " +
                "JOIN ticketing_order tord ON tord.id = et.ticket_order_id " +
                "JOIN events e on e.event_id = et.event_id " +
                "WHERE et.event_id IN :eventIds " +
                "AND et.holder_user_id IN :userIds " +
                "AND et.data_type = 'TICKET' " +
                "AND et.ticket_status not in ('CANCELED','DELETED') " +
                "GROUP BY et.holder_user_id, et.id " +
                "ORDER BY  et.created_at DESC " +
            ") AS t GROUP BY userId ", nativeQuery = true)
    List<Object[]> getLastEventRegistrationInfo(@Param("eventIds") List<Long> eventIds, @Param("userIds") List<Long> userIds);

    @Query(value = "SELECT userId, eventName, utm, registerDate " +
            " FROM ( " +
            "SELECT et.id AS id, et.holder_user_id AS userId, e.name AS eventName, tord.rec_source AS utm, et.created_at as registerDate " +
            "FROM event_tickets et " +
            "JOIN ticketing_order tord ON tord.id = et.ticket_order_id " +
            "JOIN events e on e.event_id = et.event_id " +
            "WHERE et.event_id IN :eventIds " +
            "AND et.holder_user_id IN :userIds " +
            "AND et.data_type = 'TICKET' " +
            "AND et.ticket_status not in ('CANCELED','DELETED') " +
            "GROUP BY et.holder_user_id, et.id " +
            "ORDER BY  et.created_at ASC " +
            ") AS t GROUP BY userId ", nativeQuery = true)
    List<Object[]> getFirstEventRegistrationInfo(@Param("eventIds") List<Long> eventIds, @Param("userIds") List<Long> userIds);


    @Query(value = "SELECT et.holder_user_id, ROUND(SUM(et.paid_amount - et.refunded_amount), 2) " +
            "FROM event_tickets et " +
            "WHERE et.event_id IN :eventIds " +
            "AND et.ticket_status not in ('CANCELED','DELETED') " +
            "AND et.holder_user_id IN :userIds " +
            "GROUP BY holder_user_id", nativeQuery = true)
    List<Object[]> getTotalPaidAmountByUserIdsAndEventIds(@Param("eventIds") List<Long> eventIds, @Param("userIds") List<Long> userIds);

    @Query(value = "select event_id,eventurl,coalesce(wSubscriptionId,oSubscriptionId,'') as subscriptionId,coalesce(wcustomerId,ocustomerId,'') as customerId, " +
            " coalesce(amount,0.0) as amount, event_end_date from " +
            " (Select sum(et.ae_fee_amount - et.refunded_AE_fee) as amount,e.event_id as event_id,t.event_end_date, " +
            " eventurl,o.subscription_id as oSubscriptionId,o.chargebee_customer_id as oCustomerId, " +
            " w.subscription_id as wSubscriptionId,w.chargebee_customer_id as wCustomerId " +
            " from event_tickets et join event_ticket_type ett ON et.ticketing_type_id = ett.id " +
            " join ticketing t on t.id=ett.ticketing_ID " +
            " join events e on e.event_id=et.event_id " +
            " left join organizers o on e.organizer_id=o.id " +
            " left join white_label w on w.id=e.white_label " +
            " where  et.created_at between :startDate and  :endDate " +
            " group by e.event_id) fees where fees.amount>0",nativeQuery = true)
    List<Object[]> getAllTicketingFeedDataPerEvent(@Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Query("SELECT et FROM EventTickets et " +
            " JOIN FETCH et.holderUserId AS userId " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " WHERE et.event=:event " +
            " AND et.ticketStatus <> 'DELETED' " +
            " AND (COALESCE(:firstName, '1') = '1' OR userId.firstName=:firstName)" +
            " AND (COALESCE(:lastName, '1') = '1' OR userId.lastName=:lastName) " +
            " AND (COALESCE(:fullName, '1') = '1' OR CONCAT(userId.firstName,' ',userId.lastName) LIKE %:fullName%)" +
            " AND (coalesce(:eventTicketId, 1) = 1 OR et.id=:eventTicketId) " +
            " AND (COALESCE(:phoneNumber, 1) = 1 OR userId.phoneNumber = :phoneNumber) " +
            " AND (coalesce(:email, '1') = '1' OR userId.email=:email) ")
    List<EventTickets> findEventTicketsByNameAndIdAndEmail(@Param("event") Event event,
                                                           @Param("firstName") String firstName,
                                                           @Param("lastName") String lastName,
                                                           @Param("fullName") String fullName,
                                                           @Param("eventTicketId") Long eventTicketId,
                                                           @Param("phoneNumber") Long phoneNumber,
                                                           @Param("email") String email);

    @Query("SELECT et FROM EventTickets et " +
            " JOIN FETCH et.holderUserId AS userId " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " WHERE et.event=:event " +
            " AND et.ticketStatus <> 'DELETED' " +
            " AND ticketingType.dataType <> 'ADDON'" +
            " AND (COALESCE(:firstName, '1') = '1' OR userId.firstName=:firstName)" +
            " AND (COALESCE(:lastName, '1') = '1' OR userId.lastName=:lastName) " +
            " AND (COALESCE(:fullName, '1') = '1' OR CONCAT(userId.firstName,' ',userId.lastName) LIKE %:fullName%)" +
            " AND (coalesce(:eventTicketId, 1) = 1 OR et.id=:eventTicketId) " +
            " AND (COALESCE(:phoneNumber, 1) = 1 OR userId.phoneNumber = :phoneNumber) " +
            " AND (coalesce(:email, '1') = '1' OR userId.email=:email) ")
    List<EventTickets> findEventTicketsWithoutAddonByNameAndIdAndEmail(@Param("event") Event event,
                                                           @Param("firstName") String firstName,
                                                           @Param("lastName") String lastName,
                                                           @Param("fullName") String fullName,
                                                           @Param("eventTicketId") Long eventTicketId,
                                                           @Param("phoneNumber") Long phoneNumber,
                                                           @Param("email") String email);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN FETCH et.holderUserId AS userId " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " WHERE et.event=:event" +
            "   AND (et.ticketStatus IN (:ticketStatus)" +
            "       OR ticketingType.ticketTypeFormat = 'HYBRID')" +
            "   AND (ticketingType.ticketTypeFormat IN ('HYBRID','IN_PERSON') " +
            "       OR et.event.eventFormat = 'VIRTUAL') " +
            "   AND et.ticketStatus <> 'DELETED' " +
            "   AND ticketingType.dataType <> 'ADDON' ",
            countQuery = "SELECT COUNT(et) FROM EventTickets et " +
                    " JOIN et.holderUserId AS userId " +
                    " JOIN et.ticketingTypeId AS ticketingType " +
                    " WHERE et.event = :event" +
                    "   AND (et.ticketStatus IN (:ticketStatus)" +
                    "       OR ticketingType.ticketTypeFormat = 'HYBRID')" +
                    "   AND (ticketingType.ticketTypeFormat IN ('HYBRID','IN_PERSON')" +
                    "       OR et.event.eventFormat = 'VIRTUAL')" +
                    "   AND ticketingType.dataType <> 'ADDON'" +
                    "   AND et.ticketStatus <> 'DELETED' ")
    Page<EventTickets> findEventTicketsByEvent(@Param("event") Event event, @Param("ticketStatus") List<TicketStatus> ticketStatus, Pageable pageable);


    @Query(value = "SELECT et FROM EventTickets et " +
            "JOIN FETCH et.holderUserId " +
            "JOIN FETCH et.ticketHolderAttributesId " +
            "WHERE et.event = :event " +
            "AND et.ticketStatus IN :ticketStatuses " +
            "AND et.guestOfBuyer = false " +
            "AND (:searchString IS NULL OR " +
            "LOWER(et.holderFirstName) LIKE LOWER(CONCAT('%', :searchString, '%')) OR " +
            "LOWER(et.holderLastName) LIKE LOWER(CONCAT('%', :searchString, '%')) OR " +
            "LOWER(et.holderEmail) LIKE LOWER(CONCAT('%', :searchString, '%'))) " +
            "GROUP BY et.holderUserId.userId",
            countQuery = "SELECT count(DISTINCT et.holderUserId.userId) FROM EventTickets et " +
                    "WHERE et.event = :event " +
                    "AND et.ticketStatus IN :ticketStatuses " +
                    "AND et.guestOfBuyer = false " +
                    "AND et.ticketStatus <> 'DELETED' " +
                    "AND (:searchString IS NULL OR " +
                    "LOWER(et.holderFirstName) LIKE LOWER(CONCAT('%', :searchString, '%')) OR " +
                    "LOWER(et.holderLastName) LIKE LOWER(CONCAT('%', :searchString, '%')) OR " +
                    "LOWER(et.holderEmail) LIKE LOWER(CONCAT('%', :searchString, '%')))")
    Page<EventTickets> findEventTicketsByEventAndTicketStatusForPeople(
            @Param("searchString") String searchString,
            @Param("event") Event event,
            @Param("ticketStatuses") List<TicketStatus> ticketStatuses,
            Pageable pageable
    );

    @Query(value = "SELECT re " +
            " FROM RecurringEvents re " +
            " WHERE re.id IN (:recurringEventIds)")
    List<RecurringEvents> getRecurringEventsByIds(@Param("recurringEventIds")List<Long> recurringEventIds);

    @Query("SELECT ticketHolder.id FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " JOIN et.ticketHolderAttributesId AS ticketHolder " +
            " WHERE et.event=:event " +
            " AND et.holderUserId=:user "+
            " AND et.guestOfBuyer = false")
    List<Long> getAllTicketHolderAttributeIdsByUserAndEvent(@Param("user") User user,@Param("event") Event event);


    @Query(value = "SELECT et.holderUserId FROM EventTickets et WHERE id=:eventTicketId")
    Optional<User> getUserByEventTicketId(@Param("eventTicketId") Long eventTicketId);


    @Query("SELECT ticketingType " +
            " FROM EventTickets AS et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.id =:eventTicketId ")
    TicketingType getTicketTypeByEventTicket(@Param("eventTicketId")Long eventTicketId);

    @Query("select count(et.id) from EventTickets AS et" +
            " JOIN TicketingOrder tor on et.ticketingOrderId=tor.id"+
            " WHERE et.ticketingTypeId in (:ticketingType) and et.ticketPurchaserId=:purchaserId and tor.status='PAID'")
    long getEventTicketsByTicketingIdAndPurchaserId(@Param("ticketingType")List<TicketingType>  ticketingType,@Param("purchaserId") User purchaserId);

    @Modifying
    @Query( value = "UPDATE  event_tickets et " +
            " SET et.usage_logged = :result WHERE  et.ticketing_type_id in" +
            " ( select * from ( select  et1.ticketing_type_id from  event_tickets et1 " +
            " WHERE et1.event_id =:eventId ) tickettypes )" +
            " AND et.id >= :fromEventTicketId " +
            " AND et.id <=:toEventTicketId ",nativeQuery = true)
    void updateEventTicketLog(@Param("eventId") Long eventId,
                               @Param("result") String result,
                               @Param("fromEventTicketId") long fromEventTicketId,
                               @Param("toEventTicketId") Long toEventTicketId);

    @Query(value = "SELECT CASE WHEN EXISTS ( " +
            "select et.show_popup from event_tickets et " +
            "JOIN ticketing_order tor on tor.id=et.ticket_order_id " +
            "JOIN event_ticket_type etype ON et.ticketing_type_id = etype.id " +
            " where etype.pay_later = true" +
            " and tor.order_type='PAY_LATER' " +
            " and et.h_email=:holderEmail " +
            " and et.event_id=:eventId " +
            " and et.ticket_payment_status = 'UNPAID'" +
            " and et.show_popup=true " +
            ") THEN 'TRUE' ELSE 'FALSE' END",nativeQuery = true)
    Boolean getEventShowPopUpStatus(@Param("eventId") Long event, @Param("holderEmail") String holderEmail);

    @Query("select et from EventTickets et " +
            "join TicketingOrder tor on tor.id=et.ticketingOrderId " +
            "where tor.orderType='PAY_LATER' and et.holderEmail=:holderEmail and tor.eventid=:eventId " +
            "and et.ticketPaymentStatus <> ('PAID') and et.showPopup=true")
    List<EventTickets> getAllEventsBasedOnPayLaterOption(@Param("eventId") Event event, @Param("holderEmail") String holderEmail);

    @Query("SELECT et FROM EventTickets et " +
            " JOIN FETCH et.holderUserId AS user " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " WHERE et.event = :event " +
            " AND ticketingType.dataType <> 'ADDON'" +
            " AND (coalesce(:searchString, '') = '' " +
            "     OR CONCAT(user.firstName,' ',user.lastName) LIKE %:searchString%" +
            "     OR et.id LIKE %:searchString% " +
            "     OR et.holderPhoneNumber LIKE %:searchString% " +
            "     OR user.email LIKE %:searchString%) " +
            " AND et.ticketStatus <> 'DELETED' ")
    List<EventTickets> findEventTicketsBySearchString(@Param("searchString")String searchString,@Param("event")Event event);


    @Query("SELECT ticketingType.ticketTypeFormat FROM EventTickets AS et JOIN et.ticketingTypeId AS ticketingType WHERE et.id =:eventTicketId")
    TicketTypeFormat getTicketTypeFormatByEventTicketId(@Param("eventTicketId") Long eventTicketId);

    @Query("select  new com.accelevents.dto.EventTicketHolderAttributeDto(et.holderEmail,et.ticketHolderAttributesId.id) from EventTickets et " +
            "join TicketingOrder tor on tor.id=et.ticketingOrderId " +
            "where et.holderEmail in (:holderEmail) and tor.eventid=:eventId ")
    List<EventTicketHolderAttributeDto> getAllEventTicketByHolderEmail(@Param("eventId") Event event, @Param("holderEmail") List<String> holderEmail);

    @Query("select  et from EventTickets et " +
            "join TicketingOrder tor on tor.id=et.ticketingOrderId " +
            "where et.id in (:ids) and tor.eventid=:eventId and et.ticketStatus <> 'DELETED'")
    List<EventTickets> getAllEventTicketsByIds(@Param("eventId") Event event, @Param("ids") List<Long> ids);

    @Query("select  new com.accelevents.dto.EventTicketTicketTypeDTO(et.id, et.ticketingTypeOnlyId) from EventTickets et " +
            "join TicketingOrder tor on tor.id=et.ticketingOrderId " +
            "where tor.eventid=:eventId and et.ticketStatus <> 'DELETED'")
    List<EventTicketTicketTypeDTO> getAllEventTicketsTicketType(@Param("eventId") Event event);

    @Query(value = "SELECT COUNT(et.id) FROM event_tickets et " +
            "JOIN ticket_holder_attributes AS tha ON et.ticket_holder_attributes_id = tha.id " +
            "WHERE et.event_id = :eventId " +
            "AND JSON_UNQUOTE(JSON_EXTRACT(tha.json_value, CONCAT('$.holder.attributes.\"', :attributeName, '\"'))) = :label " +
            "AND et.ticket_status <> 'DELETED'", nativeQuery = true)
    Long findCapacityCountBasedOnSelectedOptions(@Param("eventId") Long eventId, @Param("attributeName") String attributeName, @Param("label") String label);

    @Query("select count(et.id) from EventTickets AS et" +
            " JOIN TicketingOrder tor on et.ticketingOrderId=tor.id"+
            " WHERE et.ticketingTypeId=:ticketingType and et.ticketPurchaserId=:purchaserId and tor.status in ('PAID','UNPAID','PARTIAL')")
    long getEventTicketsByTicketingTypeIdAndPurchaserId(@Param("ticketingType")TicketingType  ticketingType,@Param("purchaserId") User purchaserId);

    @Query("SELECT et FROM EventTickets AS et " +
            "LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            "WHERE et.ticketingOrderId= :ticketingOrderId " )
    List<EventTickets> getEventTicketsWithTicketingTableByTicketingOrderId(@Param("ticketingOrderId") Long ticketingOrderId);

    @Query("SELECT et FROM EventTickets AS et WHERE et.id in(:ids)" )
    List<EventTickets> getEventTicketsByIds(@Param("ids") List<Long> ids);


    @Query(value = "SELECT SUM(netsale) as totalsale " +
            " FROM (SELECT " +
            " ifnull(SUM(z.paid_amount - z.refund_amount) - SUM(total_fee + if(:isStripe, ccFee ,ccFee - refundedCCFee)),0) as netsale " +
            " FROM ( " +
            " SELECT " +
            " x.*, " +
            " IF( x.paid_amount > 0 AND x.charge_id != 0,(((x.paid_amount * :percentage) / 100) + (:fixed/ticketTypeCountInOrder) ),0) as ccFee, " +
            " IF( x.paid_amount > 0 AND x.charge_id != 0,((x.refund_amount * (((x.paid_amount * :percentage) / 100) + (:fixed/ticketTypeCountInOrder))) / x.paid_amount),0) as refundedCCFee " +
            " FROM " +
            "( SELECT " +
            " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, IF((ett.data_type not in (:dataType)), 0, count(et.id))) as ticketCount, " +
            " et.ticketing_type_id, " +
            " ett.ticket_type, " +
            " ett.ticket_bundle_type, " +
            " et.ticket_order_id, " +
            " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, et.ae_fee_amount) as ae_fee_amount, " +
            " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, SUM(et.paid_amount)) as paid_amount, " +
            " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, SUM(et.refunded_amount)) as refund_amount, " +
            " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, SUM(ae_fee_amount + wl_a_fee_amount + wl_b_fee_amount + sales_tax_fee_amount + vat_tax_fee_amount) - SUM(refunded_AE_fee + refunded_wl_a_fee + refunded_wl_b_fee + refunded_sales_tax_fee + refunded_vat_tax_fee)) as total_fee, " +
            " IF((t.is_recurring_event && ett.recurring_event_id is null), 0, IF((ett.data_type not in (:dataType)), 0, ett.number_of_tickets)) as number_of_tickets, " +
            " ett.data_type as data_type, " +
            " ett.NUMBER_OF_TICKET_PER_TABLE as number_of_tickets_per_table, " +
            " (SELECT count(1) FROM ticketing_order_manager as tom " +
            " WHERE tom.order_id = et.ticket_order_id) as ticketTypeCountInOrder, " +
            " IF((et.charge_id is null), 0, 1) as charge_id " +
            " FROM event_tickets et " +
            " JOIN event_ticket_type ett on et.ticketing_type_id = ett.id " +
            " JOIN ticketing t on ett.ticketing_id = t.id" +
            " WHERE ett.ticketing_id=:ticketingId and (et.recurring_event_id=:recurringEventId OR 0 = :recurringEventId) " +
            " and ett.data_type IN (:dataType) " +
            " and et.ticket_status <> 'DELETED' " +
            " GROUP BY et.ticket_order_id, et.ticketing_type_id " +
            " UNION ALL " +
            " SELECT " +
            " 0 as ticketCount,event_ticket_type.id, event_ticket_type.ticket_type, event_ticket_type.ticket_bundle_type, 0, 0,0 ,0 ,0 , " +
            " IF((ticketing.is_recurring_event && event_ticket_type.recurring_event_id is null), 0, event_ticket_type.number_of_tickets), " +
            " event_ticket_type.data_type as data_type, " +
            " event_ticket_type.NUMBER_OF_TICKET_PER_TABLE," +
            " 1 as ticketTypeCountInOrder, 0 " +
            " FROM event_ticket_type " +
            " JOIN ticketing on ticketing.id = event_ticket_type.ticketing_id" +
            " WHERE NOT EXISTS (SELECT ticketing_type_id FROM event_tickets WHERE event_tickets.ticketing_type_id = event_ticket_type.id and event_tickets.ticket_status <> 'DELETED') " +
            " AND event_ticket_type.data_type IN (:dataType) " +
            " AND (event_ticket_type.rec_status<>'CANCEL' OR event_ticket_type.rec_status is NULL and is_disable_ticket = false) " +
            " AND event_ticket_type.ticketing_id=:ticketingId " +
            " AND (event_ticket_type.recurring_event_id=:recurringEventId OR 0 = :recurringEventId ) " +
            " ) x" +
            " ) z " +
            " GROUP BY z.ticketing_type_id, z.ticket_bundle_type ) totalSales ",
            nativeQuery = true)
    double getNetSalesOfAllTheEventTickets(@Param("ticketingId") long ticketingId, @Param("recurringEventId") long recurringEventId,
                                           @Param("percentage") double percentage, @Param("fixed") double fixed, @Param("isStripe") boolean isStripe,
                                           @Param("dataType") List<String> dataType);

    @Query(value = "SELECT et.* " +
            "FROM event_tickets et " +
            "WHERE et.event_id = :eventId " +
            "AND et.ticket_status not in ('CANCELED','DELETED') " +
            "AND et.holder_user_id IN (:userIds) order by et.id desc ", nativeQuery = true)
    List<EventTickets> findAllEventTicketsByHolderUserIdsAndEventId(@Param("userIds") List<Long> userIds,
                                                                   @Param("eventId") Long eventId);

    @Query("SELECT et.holderUserId.userId, et.id FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.eventId = :eventId" +
            " AND et.ticketStatus NOT IN (:ticketStatus) " +
            " AND et.dataType ='TICKET' " +
            " AND et.holderUserId.userId IN (:userIds) " +
            " AND ticketingType.ticketType <> 'DONATION'" +
            " GROUP BY et.holderUserId.userId")
    List<Object[]> findUserIdAndEventTicketIdByHolderUserIdsAndEventId(@Param("userIds") List<Long> userIds,
                                                                       @Param("eventId") Long eventId,
                                                                       @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT DISTINCT(ticketingType.id) FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.recurringEventId is null " +
            " AND ( et.ticketPurchaserId = :user OR et.holderUserId = :user) " +
            " AND et.dataType=:dataType " +
            " AND et.ticketStatus NOT IN (:ticketStatus) ")
    Set<Long> getEventTicketTypeIdsByEventAndUserAndDataTypeANDNotCanceled(@Param("event") Event event, @Param("user") User user,
                                                                           @Param("dataType") DataType dataType, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT DISTINCT(ticketingType.id) FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.event = :event" +
            " AND et.recurringEventId=:recurringEventId " +
            " AND ( et.ticketPurchaserId = :user OR et.holderUserId = :user) " +
            " AND et.dataType=:dataType " +
            " AND et.ticketStatus NOT IN (:ticketStatus) ")
    Set<Long> getEventTicketTypeIdsByRecurringEventIdAndUserAndDataTypeANDNotCanceled(@Param("event") Event event,@Param("recurringEventId") Long recurringEventId, @Param("user") User user,
                                                                                      @Param("dataType") DataType dataType, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et FROM EventTickets AS et " +
            "LEFT JOIN FETCH et.ticketingTable AS ticketingTable " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            "WHERE et.ticketingOrderId in (:ticketingOrderIds) " )
    List<EventTickets> getAllEventTicketsWithTicketingTableByTicketingOrderIds(@Param("ticketingOrderIds") List<Long> ticketingOrderIds);


    @Query("SELECT ticketHolder FROM EventTickets AS et " +
            " JOIN et.ticketHolderAttributesId AS ticketHolder " +
            " WHERE et.event=:event " +
            " AND et.id=:ticketId"+
            " AND et.holderUserId=:user ")
    Page<TicketHolderAttributes> getTicketHolderAttributeByEventTicketAndEventAndUser(@Param("ticketId") Long eventTicketId,@Param("user") User user,@Param("event") Event event, Pageable pageable);
    @Query(value = "select et.event_id,count(et.id) from event_tickets et join event_ticket_type ett on ett.id=et.ticketing_type_id " +
            "where et.event_id in (:eventIds) and ett.ticket_type not in ('DONATION') and et.ticket_status not in ('CANCELED','DELETED') " +
            "and et.data_type='TICKET' group by et.event_id;",nativeQuery = true)
    List<Object[]> findAllNonDeletedAndNonDonationTypeAndTypeTicketByEventIds(@Param("eventIds") List<Long> eventIds);
    @Query(value = "select et.event_id,group_concat(distinct et.holder_user_id) from event_tickets et join event_ticket_type ett on ett.id=et.ticketing_type_id " +
            "where et.event_id in (:eventIds) and ett.ticket_type<>'DONATION' and et.ticket_status not in ('CANCELED','DELETED') " +
            "and et.data_type='TICKET' group by et.event_id",nativeQuery = true)
    List<Object[]> findEventTicketsUniqueUsersIdsByEventIds(@Param("eventIds") List<Long> eventIds);
    @Query(value = "select distinct et.holder_user_id,u.first_name,u.last_name,u.email from event_tickets et join event_ticket_type ett on ett.id=et.ticketing_type_id  " +
            "join users u on u.user_id=et.holder_user_id " +
            "where et.event_id=:eventId and ett.ticket_type<>'DONATION' and et.ticket_status not in ('CANCELED','DELETED')  " +
            "and et.data_type='TICKET'",nativeQuery = true)
    List<Object[]> findEventTicketsUniqueUsersIdsByEventId(@Param("eventId") Long eventId);
    @Query(value = "select event_id,group_concat(distinct user_id) from staff where event_id in (:eventIds) and rec_status<>'DELETE' group by event_id",nativeQuery = true)
    List<Object[]> findAllUniqueEventStaffsUserIdsByEventIds(@Param("eventIds") List<Long> eventIds);
    @Query(value = "select event_id,group_concat(distinct user_id) from staff where event_id in (:eventIds) group by event_id",nativeQuery = true)
    List<Object[]> findAllUniqueDeletedEventStaffsUserIdsByEventIds(@Param("eventIds") List<Long> eventIds);
    @Query(value = "select event_id,group_concat(distinct user_id) from speakers where event_id in (:eventIds) and speaker_status<>'DELETE' group by event_id",nativeQuery = true)
    List<Object[]> findAllUniqueEventSpeakerUserIdsByEventIds(@Param("eventIds") List<Long> eventIds);
    @Query(value = "select event_id,group_concat(distinct user_id) from speakers where event_id in (:eventIds) group by event_id",nativeQuery = true)
    List<Object[]> findAllUniqueDeletedEventSpeakerUserIdsByEventIds(@Param("eventIds") List<Long> eventIds);

    @Query("SELECT et FROM EventTickets AS et " +
            "WHERE et.ticketingOrderId=:ticketingOrderId ")
    List<EventTickets> findEventTicketsByOrderId(@Param("ticketingOrderId")Long ticketingOrderId);

    @Query("SELECT et FROM EventTickets AS et " +
            " WHERE et.event = :event " +
            " AND et.ticketingTypeOnlyId IN (:allowedTicketTypeIdForAddon) " +
            " AND (et.ticketPurchaserId = :user OR et.holderUserId = :user)" +
            " AND et.ticketStatus NOT IN (:ticketStatus) ORDER BY et.id ASC")
    List<EventTickets> findAllByEventAndUserAndTicketingTypeIdIn(@Param("event") Event event,@Param("user") User user, @Param("allowedTicketTypeIdForAddon") List<Long> allowedTicketTypeIdForAddon,@Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Modifying
    @Query("UPDATE EventTickets et SET et.ticketIdForAddon = NULL WHERE et.ticketIdForAddon IN (:eventTicketIds) AND et.dataType = :dataType")
    void updateAddOnTicketsByTicketIdsAndDataType(@Param("eventTicketIds") List<Long> eventTicketIds,@Param("dataType") DataType dataType);

    @Query("SELECT et.ticketingTypeId FROM EventTickets AS et " +
            " WHERE et.eventId = :eventId " +
            " AND et.ticketIdForAddon = :ticketIdForAddon" +
            " AND et.ticketStatus NOT IN (:ticketStatus) ORDER BY et.id ASC")
    List<TicketingType> getAddonTicketingTypeByEventIdAndTicketIdForAddon(@Param("eventId") Long eventId, @Param("ticketIdForAddon") Long ticketIdForAddon, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(" SELECT ticketHolder FROM EventTickets AS et " +
            " JOIN et.ticketHolderAttributesId AS ticketHolder " +
            " WHERE et.eventId=:eventId " +
            " AND et.barcodeId=:barcodeId ")
    TicketHolderAttributes getTicketHolderAttributeByBarcodeIdAndEventId(@Param("barcodeId")String barcodeId, @Param("eventId") Long eventId);


    @Query("SELECT addOnTickets FROM EventTickets AS addOnTickets "+
            " WHERE  addOnTickets.eventId=:eventId AND addOnTickets.ticketIdForAddon = :ticketId AND addOnTickets.dataType = 'ADDON'  AND addOnTickets.ticketStatus NOT IN (:ticketStatus)")
    List<EventTickets> findAddonTicketsByEventIdAndTicketId(@Param("eventId") Long eventId, @Param("ticketId") long ticketId, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query("SELECT et.ticketIdForAddon, et.ticketingTypeId FROM EventTickets AS et " +
            " WHERE et.eventId = :eventId " +
            " AND et.ticketIdForAddon In (:ticketIdForAddon)" +
            " AND et.ticketStatus NOT IN (:ticketStatus)")
    List<Object[]> getAddonTicketingTypeByEventIdAndTicketIdForAddons(@Param("eventId") Long eventId, @Param("ticketIdForAddon") List<Long> ticketIdForAddon, @Param("ticketStatus") List<TicketStatus> ticketStatus);

    @Query(value = "SELECT et FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType JOIN ticketingType.ticketing AS ticketing " +
            " WHERE ticketing.eventid = :event" +
            " AND et.holderUserId=:user " +
            " AND et.dataType=:dataType " +
            " AND et.ticketStatus <> 'DELETED' ORDER BY et.id ASC")
    Page<EventTickets> findEventTicketsByHolderUserIdAndEventIdAndDataType(@Param("user") User user,
                                                                @Param("event") Event event,@Param("dataType") DataType dataType,Pageable pageable);

    @Query(value = "SELECT et.ticketingTypeId.id FROM EventTickets et " +
            " JOIN et.ticketingTypeId AS ticketingType " +
            " WHERE et.event = :event" +
            " AND et.holderUserId=:user " +
            " AND et.dataType=:dataType " +
            " AND et.ticketStatus NOT IN (:ticketStatus) " )
    Set<Long> getAllTicketTypesByHolderUserIdAndEventIdAndDataTypeAndTicketStatus(@Param("user") User user,
                                                                           @Param("event") Event event,@Param("dataType") DataType dataType,@Param("ticketStatus") List<TicketStatus> ticketStatus);
    @Query("select count(et.id) from EventTickets as et " +
            " WHERE et.ticketingTypeOnlyId=:ticketingTypeOnlyId and et.ticketingOrder.id=:ticketingOrderId  " +
            " AND et.ticketStatus = 'DELETED' ")
    Integer countDeletedTicketByOrderIdAndTicketTypeId(@Param("ticketingOrderId") Long ticketingOrderId, @Param("ticketingTypeOnlyId") Long ticketingTypeOnlyId);

    @Query(value = "SELECT EXISTS ( " +
            " SELECT 1 " +
            " FROM events e " +
            " JOIN event_tickets et on et.event_id = e.event_id " +
            " WHERE (e.event_status IS NULL OR e.event_status != 'EVENT_DELETED') " +
            " AND e.organizer_id = :orgId " +
            " AND et.holder_user_id = :holderUserId " +
            " ) AS record_exists ", nativeQuery = true)
    BigInteger isTicketHolderPresentInAnyEventOfOrg(@Param("orgId")Long orgId, @Param("holderUserId")Long holderUserId);

    @Query(value = "SELECT EXISTS ( " +
            " SELECT 1 " +
            " FROM events e " +
            " JOIN event_tickets et on et.event_id = e.event_id " +
            " WHERE (e.event_status IS NULL OR e.event_status != 'EVENT_DELETED') " +
            " AND e.white_label = :whiteLabelId " +
            " AND et.holder_user_id = :holderUserId " +
            " ) AS record_exists ", nativeQuery = true)
    BigInteger isTicketHolderPresentInAnyEventOfWhiteLabel(@Param("whiteLabelId")Long whiteLabelId, @Param("holderUserId")Long holderUserId);

    @Query(value = "SELECT et FROM EventTickets et " +
            " WHERE et.eventId BETWEEN :from AND :to AND (et.event.eventStatus <>'EVENT_DELETED' OR et.event.eventStatus is NULL) ")
    List<EventTickets> findAllByEventIdBetween(@Param("from") long from, @Param("to") long to);

    @Query("SELECT CASE WHEN COUNT(et.id) > 0 THEN true ELSE false END FROM EventTickets AS et" +
            " JOIN et.ticketingTypeId AS ticketingType" +
            " WHERE et.holderUserId = :user AND et.ticketStatus <> :ticketStatus" +
            " AND ticketingType.ticketType <> 'DONATION' AND ticketingType.ticketTypeFormat NOT IN ('HYBRID','VIRTUAL')" +
            " AND et.ticketStatus <> 'DELETED' AND et.eventId = :eventId")
    boolean checkUserHadPurchasedOnlyInPersonTypeTicketInTheEvent(@Param("eventId") Long eventId, @Param("user") User user, @Param("ticketStatus") TicketStatus ticketStatus);

    @Query("SELECT new com.accelevents.dto.KeyValueLongBooleanDto(et.id, CASE WHEN et.transfersCount <:maxTransferCount THEN true ELSE false END) " +
            "FROM EventTickets AS et WHERE et.id IN (:ids) AND et.eventId = :eventId")
    List<KeyValueLongBooleanDto> getTransferLimitDetails(@Param("ids") List<Long> ids, @Param("eventId") Long eventId, @Param("maxTransferCount") long maxTransferCount);

    @Query(value = "SELECT COUNT(*) " +
            "FROM event_tickets et " +
            "JOIN ticketing_order tord ON et.ticket_order_id = tord.id " +
            "WHERE et.event_id = :eventId " +
            "  AND (COALESCE(:eventTicketTypeIds) IS NULL OR et.ticketing_type_id IN (:eventTicketTypeIds)) " +
            "  AND (COALESCE(:ticketStatus) IS NULL OR et.ticket_status IN (:ticketStatus)) " +
            "  AND tord.order_status IN ('PAID', 'UNPAID', 'PARTIAL', 'PROCESSING', 'PAYMENT_FAILED')",
            nativeQuery = true)
    Long getEventTicketCount(@Param("eventId") Long eventId, @Param("eventTicketTypeIds") List<Long> eventTicketTypeIds,@Param("ticketStatus") List<String> ticketStatus);


    @Query("SELECT et.ticketPurchaserId FROM EventTickets et WHERE et.id=:eventTicketId AND et.eventId=:eventId AND et.ticketStatus <> 'DELETED'")
    Optional<User> findPurchaserUserByEventTicketIdAndEventId(@Param("eventTicketId") Long eventTicketId, @Param("eventId") Long eventId);

    @Query("SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.holderUserId AS holderUser " +
            " JOIN FETCH et.ticketPurchaserId AS purchaserUser " +
            " JOIN FETCH et.ticketingOrder AS ticketingOrder "+
            " WHERE et.id = :eventTicketId " )
    Optional<EventTickets> findByEventTicketIdWithAllFetch(@Param("eventTicketId") Long eventTicketId);

    List<EventTickets> findByIdIn(@Param("ids") List<Long> ids);

    @Query(" SELECT et FROM EventTickets AS et " +
            " JOIN FETCH et.ticketingTypeId AS ticketingType " +
            " WHERE et.id IN (:eventTicketIds) " +
            " AND et.ticketStatus NOT IN (:ticketStatus) ")
    List<EventTickets> getEventTicketByIdsAndNotCanceled(@Param("eventTicketIds") List<Long> eventTicketIds,
                                                         @Param("ticketStatus") List<TicketStatus> statuses);

    @Query("SELECT DISTINCT CASE " +
            "WHEN et.holderUserId IS NOT NULL THEN et.holderUserId.id " +
            "ELSE et.ticketPurchaserId.id END " +
            "FROM EventTickets et " +
            "WHERE et.event = :event " +
            "AND (et.holderUserId.id IN :userIds OR et.ticketPurchaserId.id IN :userIds) " +
            "AND et.ticketStatus NOT IN :ticketStatus " +
            "AND et.dataType = 'TICKET'")
    Set<Long> findUserIdsWithPurchasedTickets(
            @Param("event") Event event,
            @Param("userIds") List<Long> userIds,
            @Param("ticketStatus") List<TicketStatus> ticketStatus
    );
}
