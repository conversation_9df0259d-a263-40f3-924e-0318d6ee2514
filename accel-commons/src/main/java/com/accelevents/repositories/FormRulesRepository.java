package com.accelevents.repositories;

import com.accelevents.domain.FormRules;
import com.accelevents.domain.enums.RuleFormType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface FormRulesRepository extends CrudRepository<FormRules, Long> {

    List<FormRules> findByEventIdAndTypeAndRecurringEventIdIsNull(long eventId,RuleFormType formType);

    List<FormRules> findByEventRequestFormIdAndType(long eventRequestFormId,RuleFormType formType);


    @Query("SELECT CASE WHEN COUNT(fr) > 0 THEN true ELSE false END " +
            "FROM FormRules fr " +
            "LEFT JOIN fr.conditions frc " +
            "LEFT JOIN fr.actions fra " +
            "WHERE fr.eventId = :eventId " +
            "AND fr.type = :formType " +
            "AND (frc.attributeId = :attributeId OR fra.attributeId = :attributeId)")
    boolean isAttributeExistsInAnyRule(@Param("eventId") Long eventId,
                                @Param("attributeId") Long attributeId,
                                @Param("formType") RuleFormType formType);



    List<FormRules> findAllByEventIdAndCreatedFrom(long eventId, long id);

    List<FormRules> findByEventIdAndTypeAndRecurringEventId(long eventId, RuleFormType type, Long recurringEventId);
}
