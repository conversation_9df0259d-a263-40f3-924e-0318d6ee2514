package com.accelevents.repositories;

import com.accelevents.common.dto.OrganizerBasicDto;
import com.accelevents.common.dto.OrganizerDataDto;
import com.accelevents.common.dto.OrganizerDto;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.OrganizerSettingDto;
import com.accelevents.enums.BillingType;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.math.BigInteger;
import java.util.List;
import java.util.Optional;

public interface OrganizerRepository extends CrudRepository<Organizer, Long> {

    @Cacheable(value = "organizerByOrganizerPageURL", key = "#p0", unless="#result == null")
    Optional<Organizer> findOrganizerByOrganizerPageURL(String organizerPageURL);
    @Query("select o from Organizer o where  o.organizerPageURL=:organizerPageURL")
    Optional<Organizer> findByOrganizerPageURL(@Param("organizerPageURL") String organizerPageURL);

    @Cacheable(value = "organizerById", key = "#p0", unless="#result == null")
    Optional<Organizer> findById(Long id);
    @Query("select o from Organizer o where  o.Id=:organiserId")
    Optional<Organizer> findByOrganizerId(@Param("organiserId") Long organiserId);
    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress) From Organizer AS organizer")
    List<OrganizerDto> findAllOrganizer();

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress, organizer.planConfig.id, organizer.planConfig.planName, organizer.chargebeeCustomerId,organizer.subscriptionId, CASE WHEN organizer.whiteLabel is null THEN true ELSE false END, organizer.whiteLabel.id) " +
            "From Organizer AS organizer WHERE organizer.Id IN (:organizerId) and organizer.whiteLabel is NULL")
    List<OrganizerDto> findOrganiserDetailsByOrganizerIds(@Param("organizerId") List<Long> organizerIds);

    @Query("Select organizer.createdBy From Organizer AS organizer WHERE organizer.Id IN (:organizerIds)")
    List<User> findCreatedByOrganizerIds(@Param("organizerIds")List<Long> organizerIds);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL)" +
            " FROM Organizer organizer WHERE organizer.contactEmailAddress=:userEmail")
    List<OrganizerDto> findOrganizersByEmail(@Param("userEmail") String userEmail);

    @Query("Select organizer.planConfig.id From Organizer AS organizer WHERE organizer.subscriptionId=:subscriptionId")
    Long getPlanIdBySubscriptionId(@Param("subscriptionId") String subscriptionId);

    @Query("Select organizer.whiteLabel From Organizer AS organizer WHERE organizer.Id=:Id")
    WhiteLabel getOrganizationByWhitelabel(@Param("Id") long Id);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress,organizer.allowSocialSharing) From Organizer AS organizer WHERE organizer.whiteLabel.id=:whiteLabelId")
    List<OrganizerDto> findOrganizerByAndWhiteLabel(@Param("whiteLabelId")Long whiteLabelId);
    @Query("Select new com.accelevents.common.dto.OrganizerBasicDto(organizer.Id, organizer.name) From Organizer AS organizer WHERE organizer.whiteLabel.id=:whiteLabelId")
    List<OrganizerBasicDto> findOrganizerByAndWhiteLabelId(@Param("whiteLabelId")long whiteLabelId);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress, organizer.planConfig.id, organizer.planConfig.planName, organizer.chargebeeCustomerId,organizer.subscriptionId, CASE WHEN organizer.whiteLabel is null THEN true ELSE false END, organizer.whiteLabel.id) " +
            "From Organizer AS organizer WHERE organizer.Id IN (:organizerId) And organizer.whiteLabel = :whiteLabel")
    List<OrganizerDto> findOrganiserDetailsByOrganizerIdsAndWhiteLabel(@Param("organizerId") List<Long> organizerIds,@Param("whiteLabel") WhiteLabel whiteLabel);

    @Cacheable(value = "organizerByUserAndWhiteLabelNull", key = "#p0.userId", condition="#p0.userId!=null", unless="#result == null")
    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL)" +
            " FROM Organizer organizer WHERE organizer.createdBy=:user and organizer.whiteLabel is NULL")
    List<OrganizerDto> findOrganizersByUserAndWhiteLabelIsNull(@Param("user") User user);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL)" +
            " FROM Organizer organizer WHERE organizer.createdBy=:user and organizer.whiteLabel.id=:whiteLabelId")
    List<OrganizerDto> findOrganizersByUserAndWhiteLabelId(@Param("user") User user, @Param("whiteLabelId")Long whiteLabelId);
    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL)" +
            " FROM Organizer organizer WHERE organizer.whiteLabel.id=:whiteLabelId")
    List<OrganizerDto> findOrganizersByWhiteLabel(@Param("whiteLabelId") Long whiteLabelId);

    @Query("SELECT org FROM Organizer AS org WHERE org.Id BETWEEN :from AND :to and org.chargebeeCustomerId is not null and org.subscriptionId is not null")
    List<Organizer> findAllOrganizer(@Param("from") long from, @Param("to") long to);

    @Query("SELECT org FROM Organizer AS org WHERE org.Id BETWEEN :from AND :to")
    List<Organizer> findAllOrganizersBetween(@Param("from") long from, @Param("to") long to);

    List<Organizer> findBySubscriptionId(String subscriptionId);

    List<Organizer> findBySubscriptionIdIn(List<String> subscriptionId);

    @Query("Select organizer.waiveOffAttendeesUploadFee FROM Organizer organizer where organizer.Id=:organizerId")
    boolean findWaiveOffAttendeeUploadFlag(@Param("organizerId") long organizerId);

    List<Organizer> findByChargebeeCustomerId(String chargebeeCustomerId);

    @Query(value = "select id from organizers o " +
            "where id not in (select organizer_id from join_users_with_organizers where role = 'owner')", nativeQuery = true)
    List<BigInteger> findOrganizerWhichDoesNotHaveOwner();

    @Query("SELECT NEW com.accelevents.dto.OrganizerSettingDto(organizer.organizerPageURL, organizer.billingType, " +
            "organizer.waiveOffAttendeesUploadFee,organizer.isAutoBilling, " +
            "organizer.attendeeUploadCharge, organizer.expoBoothCharge,organizer.engageEmailsLimit) FROM Organizer organizer " +
            "JOIN organizer.planConfig planConfig " +
            "WHERE organizer.organizerPageURL =:organizerUrl")
    OrganizerSettingDto getOrganizerSetting(@Param("organizerUrl") String organizerUrl);

    @Query("Select organizer.billingType From Organizer AS organizer WHERE organizer.Id=:organizerId")
    BillingType findBillingTypeByOrganizerId(@Param("organizerId") Long organizerId);

    @Query("SELECT org FROM Organizer AS org WHERE org.Id BETWEEN :from AND :to and org.planConfig.id =:planId")
    List<Organizer> findAllOrganizerByPlanId(@Param("from") long from, @Param("to") long to, @Param("planId") long planId);

    @Caching(evict = {
            @CacheEvict(value = "organizerByOrganizerPageURL", key = "#p0.oldOrganizerPageURL",condition = "#p0.oldOrganizerPageURL != null"),
            @CacheEvict(value = "organizerById", key = "#p0.Id"),
            @CacheEvict(value = "organizerDtoByOrganizerPageURL", key = "#p0.oldOrganizerPageURL",condition = "#p0.oldOrganizerPageURL != null"),
            @CacheEvict(value = "organizerByUserAndWhiteLabelNull", key = "#p0.createdBy.userId", condition="#p0.createdBy.userId!=null")
    })
    Organizer save(Organizer organizer);

    @Query("SELECT organizer.Id FROM Organizer AS organizer WHERE organizer.organizerPageURL=:organizerURL")
    Long findOrganizerIdByOrganizerPageURL(@Param("organizerURL") String organizerURL);

    @Query("SELECT org FROM Organizer AS org WHERE org.whiteLabel.id=:whiteLabelId")
    List<Organizer> findOrganizerByWhiteLabel(@Param("whiteLabelId")Long whiteLabelId);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress, organizer.planConfig.id, organizer.planConfig.planName, organizer.chargebeeCustomerId,organizer.subscriptionId, CASE WHEN organizer.whiteLabel is null THEN true ELSE false END, organizer.whiteLabel.id) " +
            "From Organizer AS organizer WHERE organizer.whiteLabel = :whiteLabel")
    List<OrganizerDto> findOrganiserDetailsByWhiteLabel(@Param("whiteLabel") WhiteLabel whiteLabel);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress, organizer.planConfig.id, organizer.planConfig.planName, organizer.chargebeeCustomerId,organizer.subscriptionId, CASE WHEN organizer.whiteLabel is null THEN true ELSE false END, organizer.whiteLabel.id) " +
            "From Organizer AS organizer WHERE organizer.whiteLabel.id IN (:whiteLabelId)")
    List<OrganizerDto> findOrganiserDetailsByWhiteLabelIds(@Param("whiteLabelId") List<Long> whiteLabelId);

    @Transactional
    @Modifying
    @Query(value = "UPDATE Organizer set recordStatus =:recordStatus where  Id=:organizerId and organizerPageURL =:organizerURL")
    @Caching(evict = {
            @CacheEvict(value = "organizerByOrganizerPageURL", key = "#p1",condition = "#p1 != null"),
            @CacheEvict(value = "organizerById", key = "#p0"),
            @CacheEvict(value = "organizerDtoByOrganizerPageURL", key = "#p1",condition = "#p1 != null"),
    })
    void deleteOrganizer(@Param("organizerId") Long organizerId,@Param("organizerURL") String organizerURL, @Param("recordStatus") RecordStatus recordStatus);

    @Query("SELECT org FROM Organizer AS org WHERE org.id BETWEEN :from AND :to AND org.hubSpotCompanyId IS NOT NULL")
    List<Organizer> findAllOrganizerWithHubspotCompanyId(@Param("from") long from, @Param("to") long to);

    @Query(value = "select o from Organizer o join PlanConfig cp on cp.id=o.planConfig.id where cp.isLatestPlan=false" +
            " and o.subscriptionId is not null and o.planConfig.id not in (4) and o.Id between :from and :to")
    List<Organizer> findAllOrganiserWhoHaveOldPlan(@Param("from") long from, @Param("to") long to);
    @Query(value = "select o from Organizer o join PlanConfig cp on cp.id=o.planConfig.id" +
            " where o.subscriptionId is not null and o.planConfig.id not in (4) and o.Id between :from and :to")
    List<Organizer> findAllOrganiserWhoHavePlan(@Param("from") long from, @Param("to") long to);

    @Query("select o from Organizer o where o.subscriptionId IS NOT NULL and o.Id between :from and :to")
    List<Organizer> findAllOrganisersBySubscriptionIdIsNotNullAndNotHavingVirtualComponentEntitlement(@Param("from") long from, @Param("to") long to);

    @Query("select o from Organizer o where o.subscriptionId IS NOT NULL and o.Id between :from and :to")
    List<Organizer> findAllOrganisersBySubscriptionIdIsNotNull(@Param("from") long from, @Param("to") long to);

    @Query(value = "SELECT organiser FROM Organizer organiser " +
            "JOIN PlanConfig cp ON cp.id=organiser.planConfig.id " +
            "WHERE organiser.subscriptionId IS NOT NULL " +
            "AND organiser.whiteLabel IS NULL " +
            "AND organiser.planConfig.id NOT IN (4) " +
            "AND organiser.Id BETWEEN :from AND :to")
    List<Organizer> findPaidOrganizersByIdRange(@Param("from") long from, @Param("to") long to);

    @Query("select organizer from Organizer organizer " +
            "WHERE (organizer.whiteLabel IS NOT NULL AND organizer.whiteLabel.id in (:whiteLabelIds))")
    List<Organizer> findOrganisersByWhiteLabelIds(@Param("whiteLabelIds") List<Long> whiteLabelIds);

    @Query("SELECT o FROM Organizer o WHERE o.id =:id AND o.whiteLabel.id = :whiteLabelId ")
    Optional<Organizer> findByIdAndWhiteLabelId(@Param("id") Long id, @Param("whiteLabelId") Long whiteLabelId);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress, organizer.planConfig.id, organizer.planConfig.planName, organizer.chargebeeCustomerId,organizer.subscriptionId, CASE WHEN organizer.whiteLabel is null THEN true ELSE false END, organizer.whiteLabel.id) " +
            "From Organizer AS organizer WHERE (organizer.whiteLabel.id IN (:whiteLabelId) OR organizer.Id IN (:organizersIds)) AND organizer.name LIKE CONCAT('%', COALESCE(:searchString, ''), '%')")
    Page<OrganizerDto> findOrganiserDetailsByWhiteLabelIdsOrOrganizersIds(@Param("whiteLabelId") List<Long> whiteLabelId,
                                                                          @Param("organizersIds")  List<Long> organizersIds,
                                                                          Pageable pageable,
                                                                          @Param("searchString") String searchString);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress, organizer.planConfig.id, organizer.planConfig.planName, organizer.chargebeeCustomerId,organizer.subscriptionId, CASE WHEN organizer.whiteLabel is null THEN true ELSE false END, organizer.whiteLabel.id) " +
            "From Organizer AS organizer WHERE organizer.Id IN (:organizersIds) AND organizer.name LIKE CONCAT('%', COALESCE(:searchString, ''), '%')")
    Page<OrganizerDto> findOrganiserDetailsByOrganizersIds(@Param("organizersIds")  List<Long> organizersIds,
                                                           Pageable pageable,
                                                           @Param("searchString") String searchString);


    @Query("Select new com.accelevents.common.dto.OrganizerDataDto(organizer.whiteLabel.id, CASE WHEN organizer.whiteLabel is not null THEN true ELSE false END) " +
            "From Organizer AS organizer WHERE organizer.organizerPageURL = :organizerUrl")
    OrganizerDataDto findOrganizerWhiteLabelData(@Param("organizerUrl") String organizerUrl);

    @Query("Select new com.accelevents.common.dto.OrganizerDto(organizer.Id, organizer.name, organizer.organizerPageURL, organizer.organizerDescription, organizer.logoImage, organizer.facebookLink, organizer.twitterLink, organizer.linkedInLink, organizer.backgroundColor, organizer.textColor, organizer.website, organizer.contactEmailAddress, organizer.planConfig.id, organizer.planConfig.planName, organizer.chargebeeCustomerId,organizer.subscriptionId, CASE WHEN organizer.whiteLabel is null THEN true ELSE false END, organizer.whiteLabel.id) " +
            "From Organizer AS organizer WHERE organizer.whiteLabel.id = :whiteLabelId AND organizer.name LIKE CONCAT('%', COALESCE(:searchString, ''), '%')")
    Page<OrganizerDto> findOrganiserDetailsByWhiteLabelId(@Param("whiteLabelId") Long whiteLabelId,
                                                          Pageable pageable,@Param("searchString") String searchString);

    @Query("SELECT organizer.isSecureLastNameOnHubSide FROM Organizer AS organizer WHERE organizer.id = :organizerId")
    Optional<Boolean> findSecureLastNameOnHubSideByOrganizerId(@Param("organizerId") Long organizerId);

}
