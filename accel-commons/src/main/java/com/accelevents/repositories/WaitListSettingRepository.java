package com.accelevents.repositories;

import com.accelevents.domain.WaitListSettings;
import com.accelevents.domain.enums.Status;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WaitListSettingRepository extends CrudRepository<WaitListSettings, Long> {

    @CacheEvict(value = "getWaitlistSettingByEventId", key = "#p0.eventId")
    WaitListSettings save(WaitListSettings waitListSettings);

    @Query("SELECT waitListSetting FROM WaitListSettings as waitListSetting JOIN Ticketing as ticketing ON waitListSetting.eventId = ticketing.eventid.eventId" +
            " AND ticketing.eventEndDate > now() AND waitListSetting.waitListEnabled = :waitListEnabled AND waitListSetting.manualReleaseTickets = :manualReleaseTickets")
    List<WaitListSettings> findAllByWaitListEnabled(@Param("waitListEnabled") boolean waitListEnabled, @Param("manualReleaseTickets") boolean manualReleaseTickets);

    @Query("SELECT waitListSetting.id FROM WaitListSettings as waitListSetting JOIN Ticketing as ticketing ON waitListSetting.eventId = ticketing.eventid.eventId" +
            " AND ticketing.eventEndDate > now() AND waitListSetting.waitListEnabled = :waitListEnabled AND waitListSetting.manualReleaseTickets = :manualReleaseTickets")
    List<Long> findAllIdByWaitListEnableAndManualReleaseTickets(@Param("waitListEnabled") boolean waitListEnabled, @Param("manualReleaseTickets") boolean manualReleaseTickets);

    @Caching(evict = {
            @CacheEvict(value = "getWaitlistSettingByEventId", key = "#p0", condition = "#p0 != null")
    })
    @Modifying
    @Query("UPDATE WaitListSettings SET status = :status WHERE eventId = :eventId")
    void updateStatusDeleted(@Param("eventId") Long eventId, @Param("status") Status status);

    @Query("SELECT waitListSetting FROM WaitListSettings as waitListSetting WHERE eventId IN (:eventIds)")
    List<WaitListSettings> getAllWaitListSettingsByEventIds(@Param("eventIds")  List<Long> eventIds);
}