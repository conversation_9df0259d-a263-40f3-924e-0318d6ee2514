package com.accelevents.repositories;

import com.accelevents.domain.Certificates;
import com.accelevents.domain.enums.CertificateTypes;
import com.accelevents.dto.CertificateBasicDto;
import com.accelevents.dto.CertificatesDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CertificatesRepository extends CrudRepository<Certificates, Long> {

    @Query("SELECT new com.accelevents.dto.CertificatesDto(c.id, c.eventId, c.certificateName, c.width, c.height, c.isCopiedCertificate, c.displayStyle, c.updatedAt, c.certificateType, c.displayOnVEH, c.displayOnApp) " +
            "FROM Certificates c WHERE c.eventId=:eventId" +
            " AND (coalesce(:searchString, 1) = 1 " +
            " OR (c.certificateName like %:searchString%))")
    Page<CertificatesDto> getAllCertificatesByEventId(@Param("eventId") Long eventId, @Param("searchString") String searchString, Pageable pageable);

    @Query("SELECT (case when count(c.id)  > 0 then true else false end)  FROM Certificates c WHERE c.eventId=:eventId and c.certificateName =:name")
    boolean isCertificateNameIsExist(@Param("eventId") Long eventId, @Param("name") String name);

    @Query("SELECT count(c.id) FROM Certificates c WHERE c.eventId=:eventId and c.certificateName =:name")
    long countByEventId(@Param("eventId") Long eventId, @Param("name") String name);

    @Query("SELECT new com.accelevents.dto.CertificateBasicDto(c.id, c.certificateName, c.certificateType) FROM Certificates c WHERE c.eventId=:eventId and c.certificateType IN (:certificateTypes)")
    List<CertificateBasicDto> getAllCertificatesDetailsByEventIdAndCertificateTypeIn(@Param("eventId") long eventId, @Param("certificateTypes") List<CertificateTypes> certificateTypes);

    List<Certificates> getAllCertificatesByEventIdAndCertificateType(@Param("eventId") long eventId, @Param("certificateType") CertificateTypes certificateType);

    @Query("SELECT new com.accelevents.dto.CertificateBasicDto(c.id, c.certificateName) FROM Certificates c WHERE c.eventId=:eventId AND c.id=:certificateId")
    CertificateBasicDto getCertificateIdAndNameByEventIdAndId(@Param("eventId") Long eventId, @Param("certificateId") Long certificateId);

    @Query("SELECT c FROM Certificates c WHERE c.id IN :certificateIds")
    List<Certificates> findCertificatesByIds(@Param("certificateIds") List<Long> certificateIds);
}
