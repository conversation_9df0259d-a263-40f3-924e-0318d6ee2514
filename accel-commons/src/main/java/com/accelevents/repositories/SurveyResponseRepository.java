package com.accelevents.repositories;

import com.accelevents.common.dto.SurveySessionsBasicDto;
import com.accelevents.common.dto.SurveysDto;
import com.accelevents.domain.SurveyResponse;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface SurveyResponseRepository extends CrudRepository<SurveyResponse, Long> {

    @Query("SELECT CASE WHEN COUNT(surveyResponse.id) > 0 THEN true ELSE false END FROM SurveyResponse AS surveyResponse WHERE surveyResponse.surveyId=:surveyId AND surveyResponse.userId=:userId")
    boolean findBySurveyIdAndUserId(@Param("surveyId") Long surveyId,@Param("userId") Long userId);

    @Query("SELECT COUNT(surveyResponse.id) FROM SurveyResponse as surveyResponse WHERE surveyResponse.surveyId=:surveyId")
    int findCountBySurveyId(@Param("surveyId") long surveyId);

    @Query("SELECT new com.accelevents.common.dto.SurveysDto(surveyResponse.surveyId, count(surveyResponse.id) as submissions) FROM SurveyResponse as surveyResponse WHERE surveyResponse.eventId=:eventId GROUP BY surveyResponse.surveyId")
    List<SurveysDto> findResponseCountByEventId(@Param("eventId") long eventId);

    List<SurveyResponse> findAllBySurveyId(long surveyId);

    List<SurveyResponse> findAllByEventId(long eventId);

    Optional<SurveyResponse> findFirstSurveyResponseBySurveyIdAndUserId(long surveyId, long userId);

    Optional<SurveyResponse> findFirstSurveyResponseBySurveyIdAndAnonymousUserUuid(long surveyId, String anonymousUserUuid);

    @Query("SELECT CASE WHEN COUNT(surveyResponse.id) > 0 THEN true ELSE false END FROM SurveyResponse AS surveyResponse " +
            " WHERE surveyResponse.surveyId=:surveyId " +
            " AND surveyResponse.anonymousUserUuid=:anonymousUserUuid " +
            " AND surveyResponse.eventId=:eventId ")
    boolean findBySurveyIdAndUserId(@Param("surveyId") Long surveyId,@Param("anonymousUserUuid") String anonymousUserUuid,@Param("eventId") long eventId);

    @Query("SELECT surveyResponse.userId, surveyResponse.surveyId ,surveyResponse.userQuizScore FROM SurveyResponse AS surveyResponse " +
            " WHERE surveyResponse.surveyId IN (:surveyIds)" +
            " AND surveyResponse.submissionDate BETWEEN :challengeStartDate AND :challengeEndDate " +
            " AND surveyResponse.userId IS NOT NULL ")
    List<Object[]> getAllUserSurveyScoreBySurveyIds(@Param("surveyIds") List<Long> surveyIds, @Param("challengeStartDate") Date challengeStartDate, @Param("challengeEndDate") Date challengeEndDate);

    @Query("SELECT surveyResponse.userQuizScore FROM SurveyResponse AS surveyResponse " +
            " WHERE surveyResponse.surveyId IN (:surveyIds)" +
            " AND surveyResponse.submissionDate BETWEEN :challengeStartDate AND :challengeEndDate " +
            " AND surveyResponse.userId = :userId" +
            " AND surveyResponse.eventId = :eventId" +
            " AND surveyResponse.sessionId in (:sessionIds)")
    List<Integer> getUserSurveyScoreBySurveyIdsAndUserIdAndEventId(@Param("surveyIds") List<Long> surveyIds, @Param("userId") long userId, @Param("eventId") long eventId, @Param("challengeStartDate") Date challengeStartDate, @Param("challengeEndDate") Date challengeEndDate, @Param("sessionIds") List<Long> sessionIds);

    @Query("SELECT surveyResponse.surveyId, surveyResponse.userQuizScore, surveyResponse.submissionDate, surveyResponse.sessionId FROM SurveyResponse AS surveyResponse " +
            " WHERE surveyResponse.userId =:userId AND surveyResponse.eventId =:eventId" +
            " AND surveyResponse.sessionId IS NOT NULL" )
    List<Object[]> getAllSurveyResponseUserScoreAndSubmissionDateByUserIdAndEventId(@Param("userId") Long userId, @Param("eventId") long eventId);

    @Query("SELECT surveyResponse FROM SurveyResponse AS surveyResponse " +
            " WHERE surveyResponse.sessionId IN (:sessionIds) " +
            " AND surveyResponse.submissionDate BETWEEN :challengeStartDate AND :challengeEndDate " +
            " AND surveyResponse.userId IS NOT NULL" +
            " AND surveyResponse.eventId = :eventId"
    )
    List<SurveyResponse> getCountOfSurveyResponseAndLatestSubmissionDateBySessionIds(@Param("sessionIds") List<Long> sessionIds, @Param("eventId") long eventId, @Param("challengeStartDate") Date challengeStartDate, @Param("challengeEndDate") Date challengeEndDate);

    @Query("SELECT COUNT(surveyResponse.id) FROM SurveyResponse AS surveyResponse " +
            " WHERE surveyResponse.sessionId IN (:sessionIds) " +
            " AND surveyResponse.submissionDate BETWEEN :challengeStartDate AND :challengeEndDate " +
            " AND surveyResponse.userId = :userId " +
            " AND surveyResponse.surveyId IS NOT NULL" )
    Long getCountOfSurveyResponseAndLatestSubmissionDateBySessionIdsAndUserId(@Param("sessionIds") List<Long> sessionIds, @Param("challengeStartDate") Date challengeStartDate, @Param("challengeEndDate") Date challengeEndDate, @Param("userId") long userId);


    @Query("SELECT  new com.accelevents.common.dto.SurveySessionsBasicDto (sr.surveyId, sr.sessionId) FROM SurveyResponse AS sr WHERE sr.surveyId IN (:surveyIds) " +
            " AND sr.userId = :userId AND sr.eventId = :eventId")
    List<SurveySessionsBasicDto> findSubmittedSurveyByEventIdAndUserIdAndSurveyIds(@Param("eventId") long eventId, @Param("userId") Long userId, @Param("surveyIds") List<Long> surveyIds);


    @Query("SELECT surveyResponse.surveyId, surveyResponse.userQuizScore, surveyResponse.submissionDate, surveyResponse.userId, surveyResponse.sessionId FROM SurveyResponse AS surveyResponse " +
            " WHERE surveyResponse.userId IN (:userIds) AND surveyResponse.eventId =:eventId" +
            " AND surveyResponse.sessionId IS NOT NULL" )
    List<Object[]> getAllSurveyResponseUserScoreAndSubmissionDateByUserIdsAndEventId(@Param("userIds") List<Long> userIds,@Param("eventId")  long eventId);

    @Query("SELECT CASE WHEN COUNT(surveyResponse.id) > 0 THEN true ELSE false END FROM SurveyResponse AS surveyResponse WHERE surveyResponse.surveyId=:surveyId AND surveyResponse.userId=:userId AND surveyResponse.sessionId=:sessionId")
    boolean findBySurveyIdAndUserIdAndSessionId(@Param("surveyId") Long surveyId,@Param("userId") Long userId, @Param("sessionId") Long sessionId);


    @Query("SELECT surveyResponse FROM SurveyResponse surveyResponse " +
            "WHERE surveyResponse.surveyId IN (:surveyIds) " +
            "AND surveyResponse.submissionDate BETWEEN :challengeStartDate AND :challengeEndDate " +
            "AND surveyResponse.userId IS NOT NULL " +
            "AND surveyResponse.sessionId IS NOT NULL " +
            "AND surveyResponse.eventId = :eventId")
    List<SurveyResponse> getSurveyCompletionDataBySurveyIdsAndEventId(
            @Param("surveyIds") List<Long> surveyIds,
            @Param("eventId") long eventId,
            @Param("challengeStartDate") Date challengeStartDate,
            @Param("challengeEndDate") Date challengeEndDate);


    List<SurveyResponse> findAllBySurveyIdAndUserIdIn(long surveyId, List<Long> userIds);
}
