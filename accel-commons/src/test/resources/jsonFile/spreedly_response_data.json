{"gateway_response_string": {"gateway": {"token": "UlSR3GCgSBzryKio8zeIF4aIHqm", "gateway_type": "braintree", "description": "Gateway created from eventId_1_EVENT_URL_abc", "state": "retained", "name": "Braintree", "merchant_id": "xyz", "public_key": "123", "merchant_account_id": "abc", "redacted": false, "sandbox": true, "mode": "blue"}}, "purchase_api_response_string": {"transaction": {"response": {"success": false, "message": "2038 Processor Declined", "error_code": "2038"}}}, "purchase_api_response_error_string": {"transaction": {"response": {"success": false, "message": "Braintree::AuthorizationError", "error_code": "null"}, "message": "Braintree::AuthorizationError"}}, "purchase_api_success_response_string": {"transaction": {"succeeded": true, "state": "succeeded", "token": "Ppq2pMNeNTgtwakx5m0KIJ5", "transaction_type": "Purchase", "order_id": "1", "description": "EVENT-BraintreeEvent", "email": "<EMAIL>", "gateway_specific_fields": {"braintree": {"customer_id": "*********", "descriptor_name": "EVENT-BraintreeEvent", "map_to_customer_id": true}}, "amount": 100, "currency_code": "USD", "retain_on_success": true, "stored_credential_reason_type": "unscheduled", "transaction_metadata": {"EVENT_URL": "braintreeevent", "ORDER_ID": "302645", "EVENT_NAME": "BraintreeEvent", "BUYER_NAME": "<PERSON>", "email": "<EMAIL>"}, "message_key": "messages.transaction_succeeded", "message": "Succeeded!", "gateway_token": "2GWX1KJTS58YGSKHER4", "gateway_type": "braintree"}}, "payment_method_token_api_response_string": {"transaction": {"payment_method": {"token": "abcd"}}}, "payment_method_token_api_error_string": {"errors": [{"key": "errors.non_existent_payment_method", "message": "There is no payment method corresponding to the specified payment method token."}]}, "gateway_api_error_response_string": {"errors": [{"attribute": "merchant_id", "key": "errors.blank", "message": "Merchant can't be blank"}, {"attribute": "private_key", "key": "errors.blank", "message": "Private key can't be blank"}, {"attribute": "public_key", "key": "errors.blank", "message": "Public key can't be blank"}]}, "store_third_party_vault_api_response_string": {"transaction": {"succeeded": true, "token": "Bxja43ykNfq3ggiIboh1dOMSJ", "state": "succeeded", "gateway_specific_response_fields": {"braintree": {"customer_id": "*********"}}, "transaction_type": "Store", "basis_payment_method": {"token": "01JK8GM03H1RSH296ENAM", "storage_state": "retained", "test": true, "last_four_digits": "1111", "first_six_digits": "411111", "card_type": "visa", "last_name": "vvvvvvv", "month": 12, "year": 2029, "zip": "22222", "full_name": "vvvvvvv", "fingerprint": "16a02e37e5043919412c58753da12"}}}, "get_card_details_api_response_error_string": {"errors": [{"key": "errors.payment_method_not_found", "message": "Unable to find the specified payment method."}]}, "get_card_details_api_response_string": {"payment_method": {"token": "1rpKvP8zOUhj4Y9EDrIoIYQzzD5", "email": "<EMAIL>", "storage_state": "cached", "last_four_digits": "1111", "first_six_digits": "411111", "card_type": "visa", "first_name": "Newfirst", "last_name": "Newlast", "month": 12, "year": 2029, "full_name": "<PERSON><PERSON><PERSON>", "payment_method_type": "credit_card", "fingerprint": "16a02e37e5043919412c58753da12"}}, "invalid_amount_error_string": {"errors": [{"attribute": "amount", "key": "errors.greater_than", "message": "Amount must be greater than 0", "count": "0"}]}, "un_settled_amount_refund_error_string": {"transaction": {"succeeded": false, "state": "gateway_processing_failed", "token": "2ppthU38gsq0Q7jUKqgYN", "transaction_type": "Credit", "application_id": "Spreedly_<PERSON>", "amount": 100, "currency_code": "USD", "message": "Cannot refund transaction unless it is settled. (91506)", "gateway_token": "2GWX1KJTS58YGSKHER48", "gateway_type": "braintree", "reference_token": "MaIIceMid7jckO7E6w2rFtaI4iP"}}, "refund_api_response_string": {"transaction": {"succeeded": true, "state": "succeeded", "token": "2ppthU38gsq0Q7jUKqgYNMASz", "transaction_type": "Credit", "amount": 100, "currency_code": "USD", "message_key": "messages.transaction_succeeded", "message": "Succeeded!", "gateway_token": "2GWX1KJTS58YGSKHER48", "gateway_type": "braintree", "reference_token": "MaIIceMid7jckO7E6w2rFtaI4iP"}}, "refund_charge_with_invalid_charge_id_response": {"errors": [{"key": "errors.reference_transaction_not_found", "message": "Unable to find the specified reference transaction."}]}, "remove_card_details_api_response_string": {"transaction": {"succeeded": true, "token": "1rpKvP8zOUhj4Y9EDrIoIYQzzD5", "state": "succeeded", "transaction_type": "RedactPaymentMethod", "message_key": "messages.transaction_succeeded", "message": "Succeeded!"}}}