sessions.sorted.successfully=Sessions sorted successfully ...
infodesk.update.successfully=Info-desk details updated successfully.
infodesk.delete.successfully=Info-desk details deleted successfully.
networking.lounge.deleted=Networking lounge deleted.
networking.lounge.updated=Networking lounge updated.
networking.lounge.photo.deleted=Networking lounge photo deleted.
networking.lounge.video.deleted=Networking lounge video deleted.
networking.lounge.leave.successfully=Networking lounge leave successfully.
networking.lounge.position.changed=Networking lounge position changed successfully ...
exhibitor.staff.saved=Exhibitor staff details saved.
exhibitor.staff.deleted=Exhibitor staff details deleted.
exhibitor.admin.updated=Exhibitor Admin updated for event.
exhibitor.staff.updated=Exhibitor Staff updated for event.
staff.send.invite.msg=Your invitation has been resent.
product.added.success=Product Added Successfully.
product.updated.success=Product Updated Successfully.
product.deleted.success=Product Deleted Successfully.
exhibitor.product.position.changed.success=Exhibitor product position changed successfully ...
attendee.deleted=Attendee deleted
attendee.connected.msg=Attendee Connected
notification.preference.saved=Notification Preference saved.
notification.preference.updated=Notification Preference Updated.
notification.preference.deleted=Notification Preference Deleted Successfully.
send.contact.mail=Thank you for writing to us, we will get back to you soon!
new.bidder.number.message=You have successfully registered for the event!. Your participant number is %d
bidder.registered.message=You have successfully registered for the event!
device.checker.added=Device Checker Added
device.checked.with.device.id=Performed device check on ${DATE}
lead.saved=Lead saved.
lead.deleted=Lead deleted.
lead.updated.msg=Lead updated for event
session.breakout.room.deleted=Session Breakout Room deleted.
breakout.room.updated.success=BreakoutRoom updated successfully.
exhibitor.fields.tablist.updated=Exhibitor TabList updated successfully.
success=Success

#Error
exhibitor.already.exists=Exhibitor already exists

unable.to.create.new.feed=Unable to create new feed
unable.to.update.feed=Unable to update feed
unable.to.retrieve.feed=Unable to retrieve feeds
unable.to.follow.feed=Unable to follow feeds
unable.to.get.client=Unable to get client
unable.to.retrieve.feeds.reaction=Unable to retrieve feeds reaction
activity.stream.not.found=Activity stream not found

info.desk.already.present=Info desk already present.
info.desk.not.found=Info desk not found.

session.dates.not.between.event=Session start and end time should be after event start time and before event end time.
required.for.ae.studio=Stream Key, Stream Url can not be blank for accelevents provider

session.not.found=Session not found!
speaker.not.found=Speaker not found!
tag.or.track.not.found=Tag or Track not found!
event.ticket.not.found=EventTicket Not found!
session.speaker.not.found=Session Speaker not found!
session.location=Session Location ID is not valid for current event.

session.is.ended=Session is ended!
default.playback.not.found=No default playback found for session.
event.plan.config.not.found.error.msg=This flag is only applicable for virtual events
event.plan.config.not.found.developer.msg=Event Plan config details not found for this event

attendee.can.not.added=Attendee doesn't have ticket or not match role admin, exhibitor, speaker.
attendee.already.added=Attendee already added.
attendee.name.can.not.empty=Attendee name can not empty.
super.admin.cannot.connect.without.ticket=Super admin can not connect without purchasing event ticket
attendee.not.found=Attendee Not Found
attendee.can.not.connect.to.self=Attendee can not connect to self.
attendee.already.connected=Attendee already connected.
connection.request.not.found=Connection Request Not Found
connection.request.already.accepted=Connection Request Already Accepted
connection.request.already.rejected=Connection Request Already Rejected

permission.denied.register.event=This is not registered user
event.ended=This event is no longer available
not.allow.to.access.virtual.portal=You have not purchased Virtual Event Tickets. Please, contact the organizers for more information
ticketing.module.not.activated=Please activate your event to access the portal.
max.free.attendee.reached.for.portal=All capacity for free registrations have been redeemed. You can purchase a pass to access the event!
max.attendee.allows.limit.reached=The maximum number of attendees for this event has been reached. Please contact the event organizer.
blocked=You have been blocked by admin. Please contact system administrator.
user.did.donation.not.purchase.ticket=Looks like you forgot to get a ticket when you made a donation, please purchase a ticket from the event page or contact the event organizer.
url.not.supported=Cant create url
event.not.found=Event Not Found

user.not.allowed.to.register.without.ticket=User not allowed to register without ticket
ticketing.type.not.matched=Your registration does not permit access to this session
not.authorized.to.access.session.user.analytics=Admin with the current plan is not authorized to access this functionality. Please upgrade your plan to access.
register.failed=You have not yet registered for the event. You must register for the event before you can register for individual sessions.
time.access.elapsed.live=The time limit for accessing this session is expired.
time.access.elapsed.recording=The recording of this session is only available to users who watched it live.
exceed.session.capacity=Max user registration capacity reached
session.is.not.private=Sorry, This session is not private, Bulk registration is only supported in private sessions.
exceed.workshop.session.capacity=All 250 spaces in this meeting have already been taken. Would you like to remove someone from the session so that you can join?

can.not.generate.stream.in.progress=Can not generate stream key, live stream in process

mux.live.stream.not.available=Mux live stream not available.
mux.live.stream.not.completed=Mux live stream not completed.
mux.live.stream.status.not.available=Mux live stream status not available.
mux.live.stream.can.not.create=Mux live stream can not create.
mux.live.stream.can.not.create.upload.url=Mux live stream can not create upload url.
mux.asset.id.not.available=Mux asset id not available.
mux.playback.id.not.available=Mux playback id not available.
failed.to.enable.mp4.support.for.mux=Failed to enable MP4 support.
mux.duration.not.available=Mux duration not available.
mux.preparing.video.asset=Mux is preparing video asset,wait for some time to add subtitle for it!
mux.subtitle.file.can.not.be.added=Mux subtitle file can not be added.
mux.subtitle.file.can.not.be.removed=Mux subtitle file can not be removed.
mux.live.stream.not.re.enabled=Due to technical error broadcast is not starting.

notification.preference.already.added=Notification Preference already added.
json.format.is.not.valid=Json Format is not valid

you.can.not.delete.networking.lounge.video=You Can Delete Only Own Networking Lounge Video.
default.playback.not.found.for.video=No default playback found for video.
networking.lounge.not.found=Networking Lounge Not Found.
you.can.delete.only.own.networking.lounge.photo=You Can Delete Only Own Networking Lounge Photo.
networking.lounge.with.same.name.already.exist=A networking lounge with the same name already exists.

not.authorize=Not authorize to access

session.breakout.room.not.found=SessionBreakoutRoom chat not found
breakout.room.already.exist=BreakoutRoom Name ${session_breakout_room_name} is already exist in session

expo.category.not.exists=Exhibitor Category Not exists

product.not.found=Product not found

staff.details.not.found=Staff detail not found
user.has.already.logged.in=User has already logged in at website, Can't change email now!
already.staff=You have already added this team member.
email.already.assigned=This email address is already assigned. You can not change existing staff with this email address. Please add new staff if you want to add that email as admin/staff.
expo.staff.role.not.matched=exhibitoradmin & leadretriever are valid roles for exhibitor staff
max.permitted.team.member.reached=The maximum number of team members has been reached.
expo.admin.can.not.remove.expo.admin=Los administradores de expositores no deberï¿½an poder eliminar a los administradores de expositores

can.not.generate.lead=Lead retrieval is not allowed for this exhibitor
first.name.size.limit=Only 50 Characters are allowed in firstName
last.name.size.limit=Only 50 Characters are allowed in lastName
email.name.size.limit=Only 75 Characters are allowed in email
already.used.ticket=You have already used your ticket
lead.is.not.belong.to.this.lead.retrieval=You have not added this lead. You can delete only lead which are added by you.
lead.not.exists=Lead Not exists

not.allow.to.select.interest.tags=Not allowed to select more than 10 interest tags.
item.not.found=Item not found
pledge.not.found=Please activate this module to start accepting pledges.
not.parse.number.to.intended.format=Unable to parse the number to the intended format.
raffle.not.active=Please activate this module to start accepting tickets.
raffle.is.expire=This Raffle is Over!
not.valid.payment=Not valid payment type
event.cc.not.enable=Event CC is not enabled
raffle.tickets.are.sold.out=Raffle tickets are sold out!
limitted.ticket.multi.language=There are only {ticket_max_val} remaining tickets available. Your maximum purchase must be  {ticket_max_min_val} tickets or less..
limited.raffle.ticket=There are only %d remaining tickets available. Your maximum purchase must be %d tickets or less.
raffle.ticket.pkg.not.found=Ticket pkg not found
tickets.added=Tickets Added to Your Account.
ticket.purchasing.for.event=Thank you for purchasing {number_of_ticket} {ticket} for {event_name}
do.not.have.enough.ticket=You do not have enough tickets, please purchase additional tickets below.
tickets.submitted.successfully=Tickets Submitted Successfully.
ticket.should.be.more.than.zero=Tickets should be more than 0.
can.not.default.playback = Can not set default playback for active session
hold.token.does.not.match = Selected seat already booked by someone else, Please try again.
user.not.found = User Not Found
ticket.coupon.not.found = The discount code you entered is not valid for this event
order.not.found = Order not found
access.code.not.found=Access code not found.
attribute.name.exist=Attribute name already exist
attribute.not.exists=Attribute Not exists
mcq.only.for.enterprise.plan=Multiple choice question is only for enterprise plan
purchase.event.ticket.required.enable.show.registration.button=There are currently no tickets available. Please, contact the event organizer.
purchase.event.ticket.required.for.addons.ticket=You can not purchase addons ticket without purchase event ticket.
zero.order.count=order ticket count is zero
number.of.seats.must.equal.to.number.of.tickets=Number of selected seats must be same as number of tickets.
seat.selection.not.allowed.for.free.ticket=Seat selection not allowed for FREE Tickets
not.unpaid.order=Order type is not UNPAID.
already.paid.order=Order is already paid
credit.card.processing.not.enable=Credit card processing not enabled
birth.date.not.allowed.in.future=Birth date is not allowed in future
blocked.admin.msg=This user is blocked
activate.registration=Please activate your event to begin accepting registrations
recurring.event.modified.by.host=Event details are modified by host. Please try again.
checkout.time.expire=You have not completed checkout in the allotted time. Please click here to restart the checkout process.
recurring.event.id.not.empty=Recurring event id can not null for recurring event
coupon.code.not.applicable.on.donation=Discount coupon can not be applied on donation ticket type.
coupon.not.applied.to.this.order=Coupon is not applied to this order
coupon.has.already.applied.to.your.transaction=This discount code has already been applied to your transaction.
coupon.reached.max.usage.per.user=You have already reached the maximum number of uses for this coupon.
coupon.reached.max.usage=This coupon has reached max number of uses.
coupon.not.available=Coupon is not available
coupon.code.expired=This coupon has expired.
applied.coupon.not.found=Applied coupon not found
barcode.not.exist=Barcode not exist
download.pdf.is.not.allowed.for.this.order=Download pdf is not allowed for this order
invalid.wait.list.details=Invalid waitlist details.
event.tickets.not.found=There is no available tickets for update ticket status to Check IN.
check.in.not.allowed.before.time=Check IN Not allowed before configured time
barcode.already.refunded=Checked-in failed. Ticket was refunded.
barcode.already.check.in=Check-in failed. Ticket already used.
attendee.have.virtual.ticket=Este boleto solo otorga acceso al evento virtual
collect.payment.first=Please collect payment first.
can.not.cancel.schedule.event=Can not cancel event schedule. Tickets are sold from this event schedule.
coupon.not.applicable.for.ticket.type=This coupon is not valid for this registration type.
block.user.by.admin=User is block by Admin . ${userEmail}
raffle.online.purchase.tickets=Thank you for purchasing [number_of_tickets] tickets! You can buy tickets at [raffle_ticket_purchase_url]
raffle.online.purchase=Thank you for purchasing [#] tickets. To enter tickets for an item, reply with the 3 character item code and the number of tickets. Example: ABC4
successful.ticket.submission.payment.disabled=Thank you for entering [tickets_submitted] tickets for item [item_code]. You have [numberoftickets] tickets remaining.
causeauction.not.found=CauseAuction Not Found
more.pledges.submitted=The maximum number of pledges being accepted has already been reached.
charge.creation.failed=Charge creation failed
auction.charge.failed=auction charges failed
pledge.submit.msg=Pledge Submitted Successfully.
pledge.staff.success=Thank you for pledging [currency_symbol][amount] for item [item_code].
pledge.staff.item.submitted.alert=Thank you for submitting your pledge for item [item_code], please confirm your pledge by responding with your first name and last name.
staff.cash.pledge=Thank you for your pledge!
number.of.tickets.has.been.submitted.msg=%s tickets have been submitted for this item.
string.item.has.been.purchased.msg= 	This item has been purchased for %s%s.
string.current.bid.for.this.item.msg=The current bid for this item is %s%s, bids may be submitted in %s%s amounts.
string.starting.bid.for.this.item.msg=The starting bid for this item is %s%s, bids may be submitted in %s%s amounts.
min.bid.for.item.msg=The minimum bid for this item is %s%s.
purchase=Purchase
winner=Winner
potential.winner=Potential Winners
outbid=Outbid
paid=Paid
unpaid=Unpaid
alerts.when.buy.it.now.item.purchased=Alerts when buy it now items are purchased
order.confirmations.from.attendees=Order confirmations from my attendees
weekly.sales.report=Weekly Sales Report
questions.from.attendees=Questions from attendees
payment.processing.is.not.setup=Payment processing is not set up. Please notify the administrator.
donation.not.active=Please activate this module to start accepting donation.
recurring.payment.not.supported.in.square=Recurring payment is not supported in square.
donation.charge.failed.msg=donation charges failed
amount.to.large=Credit card donations are limited to $999,999.99. For larger donations please contact the organization directly
donation.thanks=Thank you for your donation.
user.not.found.for.staff.checkout=User Not Found for staff checkout
not.staff.user=Not staff User
incorrect.password=Incorrect password, please try again.
phone.number.already.attach.to.email=This phone number is already associated with another email address
email.already.attach.to.phone.number=This email is already associated with another phone number
user.already.exist=User already exist
user.already.present.with.country.code=User Already Present with [country_code] as country code
item.already.purchased=Sorry, this item has already been purchased.
bid_success_notify=Bid Submitted Successfully, You will be notified if you are outbid.
thank.you.for.your.purchase=Thank you for your purchase!
thank.you.for.purchasing=Thank you for purchasing 
buy.it.now.bid.contacted.to.collect.payment=You are submitting a Buy it Now bid. You will be contacted to collect payment.
bid.should.be.greater.than.starting.bid=Item bid should be greater than starting bid
minimum.bid.increment.for.this.item=The minimum bid increment for this item is {bidIncrement}. Please bid at least {minimumBid}.
bidding.has.ended.for.item.with.itemcode=Bidding has ended for item {item_short_name}.
auction.not.active=Please activate this module to start accepting bids.
more.then.one.user.exist.with.this.email=More than one user exist with same email
password.validation.failed=Password should contain at least 1 uppercase, 1 lowercase, 1 special character (Valid special characters: !@#$%^&*+-_=), 1 number, no whitespaces and should be at least 8 characters in length
email.not.match.with.user.id=email address not match with userd id [user_id]
email.not.match.with.user=email address not match with userd id
bid.not.found=Could not find bid.
bidder.not.found=Bidder not found
payment.method.required=Payment method is required.
coupon.applied.success.msg=Coupon applied successfully
coupon.applied.but.not.with.donation.ticket.type=Coupon applied successfully. Note that Coupon will not be applied to donation tickets.
coupon.delete.success.msg=Delete coupon successfully
limited.display.code.saved=Limited display code saved
limited.display.code.updated=Limited display code updated
limited.display.code.deleted=Limited display code deleted
user.biddernumber.already.present=Bidder number %d already registered with this email or phone number.
bidder.number.not.enanled=Bidder number is not enabled for this event
update.bidder.number.message=Bidder number is %d updated successfully
country.code.not.valid=Country code not valid.
bid.instuction=Bid here or text Your Bid To: [phone_number] with the item's three letter code and bid amount ex. [item_code_example]
card.updated.successfully=Card Updated successfully
please.login=Please Login
receive.amount.for.unpaid.tickets=Successfully received amount
not.event.host=Not Event Host
welcome.bidder.number.sms.donation=[first_name], welcome to the event! Please visit [auction_url] to bid online
welcome.bidder.number.sms=[first_name], welcome to the event! Your bidder number is [bidder_number]. Please visit [auction_url] to bid online
winning.bid.single.item.payment.enabled=CONGRATULATIONS. You have won item [item_short_name]. Please pay at [stripe_link] or contact the organization to pay in cash.
purchase.checkout.payment.disabled=Thank you for purchasing item [item_short_name] ([item_code]) for [currency_symbol][amount].  Please see a staff member to check out.
purchase.checkout.payment.enabled=Thank you for purchasing [item_short_name] ([item_code]), please complete your purchase by clicking this link [link] 
mail.sent.unable.to.use.twilio=Email has been successfully sent. we are unable to use twilio at this time, to notify by message please try again later
unable.to.use.twilio=We were unable to use twilio at this time. Please try again later.
bid.is.refunded=Bid is already refunded, can not mark as distributed.
refund.success.msg=Refund complete.
auction.refund.failed=Unable to refund the auction bid.
bid.already.refunded=Bid is already refunded.
only.paid.bid.can.be.refunded=Sorry, only paid bid can be refunded.
email.send.success.msg=Email has been successfully sent.
bid.delete.success.msg=Bid was successfully deleted.
can.not.delet.alreadt.paid=You cannot delete bid which have already been paid.
payment.confirmation.required=Thank you for bidding for item [item_short_name] ([item_code]). Please visit this page to confirm your bid. [link] 
confirm.purchase.payment.disabled=Please confirm your bid for [item_short_name] ([item_code]) by responding with your name in the following format: FirstName LastName
error.in.generation.of.token=Error in generation of access token
thanks.for.purchase.msg=Thank you for your purchase.
not.sales.rep=Not Sales Representative
tickets.are.not.currently.for.sale.for.this.event=Tickets are not currently for sale for this event.
successfully.updated.order.tickets.status.check.in=Successfully updated order tickets status to Check In.
refund.order.not.paid=Refund order not paid
refund.amount.should.not.greater.then.paid=The refund amount must be less than the paid amount.
virtual.events.are.not.support.with.recurring.event=Please convert event to non-recurring OR contact to support team to make it non-recurring event.
not.allow.to.upload.attendees=Attendees upload not allowed for free plan.
some.of.fields.are.empty.in.attendee.csv=Some of the fields having empty field in upload CSV.
email.field.should.contain.single.value=Email field should contain single email value.
invalid.email=this is not valid emailid,Please provide Valid emailid.
order.id.have.must.number.only=Order Id have must number only.
upload.file.header.not.correct.for.attendee.csv=Upload file headers name are not correct or not in proper sequence. It should be Transaction Id,First Name,Last Name,Email.
csv.upload.success.message=CSV uploaded successfully, We will notify you soon by email.
attendee.list.not.found=Attendee list not found.

#Billing Page
successfully.purchased.plan.msg=Thank you for your purchase of ${planType} plan . Your purchase is in process and you should be gaining access to the platform within an hour. Please let us know through chat if you are unable to access the platform in an hour.
only.admin.owner.can.subscribe.plan=Only organizer admin or owner can subscribe to the plan
member.not.allowed.to.subscribe.plan=Member are not allowed to subscribe the plan, only Admin/Owner can subscribe to the plan
customer.not.found.in.chargebee=Customer Not Found In Chargebee.
subscription.cancelled.successfully=Subscription Cancelled Successfully.
white.label.url.not.found=White Label URL not found
can.not.purchase.more.than.twenty=Max 20 quantity can be purchase at a time
charge.purchased.success=You have purchased ${quantity} ${plan_name} event. You can associate this purchase with an active event on the Events tab.
des.charge.purchased.success=You have successfully purchase ${quantity} quantity of ${charge_name} charge.
payment.collected.successfully=Payment Collected Successfully

#Chargebee Plan Name & charge Display Name
free=Free
starter=Starter
enterprise=Enterprise
professional=Professional
whitelabel=WhiteLabel
legacy=Legacy
whitelabellegacy=WhiteLabelLegacy
scale=Scale
singleeventunit=SingleEventUnit
unlimited.app=Unlimited App
single.event.app.charge=Single Event App Charge
dedicated.event.support.hours=Dedicated Event Support Hours
attendee.upload.charge=Attendee Upload Charge
auction.charge=Auction Charge
fund.a.need.charge=Fund A Need Charge
exhibitor.pro.booth.charge=Exhibitor Pro Booth Charge
raffle.charge=Raffle Charge
max.limit.reached=Your account has been locked to protect your security. Please <a href='/u/password-reset'> reset your password</a> or try again after 30 minutes.
account.is.locked.please.try.after.sometime=Your account has been locked to protect your security. Please <a href='/u/password-reset'> reset your password</a> or wait and try again.
pledge.instuction=Pledge here or text Your Pledge To: [phone_number] with the item's three letter code and pledge amount ex. [item_code_example]
message.service.unable.to.send.message=Message service is unable to send message
raffle.instruction=Submit your raffle tickets here or submit via text message to: [phone_number] with the item's three letter code and number of raffle tickets ex. [item_code_example]
organizer.not.found=Organizer Not Found
organizer.url.not.found=Organizer URL not found or Its changed.
user.not.allow.to.update=User not allow to update.
user.already.present.in.team=User already present in team
organizer.is.updated.successfully=Organizer is updated successfully
not.allow.to.edit.organizer.page=Not authorized for edit organizer page
chargebee.subscription.not.found=Subscription Not Found In Chargebee.
not_valid_email=Not a well-formed email address
contact.to.you.soon=Thank you for writing to us, we will get back to you soon!
not.super.admin=Not Super Admin;
evnet.not.associated.with.this.organizer=Event is not associated with this organizer
no.unsubscribedusers.are.present=There are no unsubscribed users for this event
user.not.found.in.event.tickets=User has not purchase ticket for this Event
event.deleted.successfully=Event is deleted successfully
can.not.delete.event={event_name} has more than 40 participants, due to the size of event it cannot be deleted, if this is a test event, please make sure that the event is marked as such.
phone.number.already.attach.to.email.with.email.parameter=This phone number is already associated with  [email_address] email address
make.duplicate.event=Duplicate event created successfully.
member.cannot.be.marked.as.billing.contact=Member can not be marked as billing contact
can.not.update.billing.contact=You are not authorized to update Billing contact
billing.contact.updated.successfully=Billing contact updated successfully
team.member.remove.from.billing.contact=Team member removed from billing contacts
can.not.remove.last.owner.from.billing.contact=Cannot remove last owner from Billing contact
can.not.remove.owner.from.billing.contact=You are not authorized to remove owner from Billing contact
can.not.delete.billing.contact=You are not authorized to delete Billing contact
deleted.team.member=Team member deleted successfully
can.not.delete.last.owner.billing.contact=Cannot delete last owner or billing contact
can.not.delete.owner=You are not authorized to delete owner
can.not.mark.billing.contact=You are not authorized to add team members with Billing contact enabled
can.not.remove.last.billing.contact=Cannot remove last Billing contact
owner.updated.successfully=Owner updated successfully
can.not.update.owner.of.the.organizer=You are not authorized to change owner
invitation.sent=Invitation sent
add.embed.widget.settings.successfully=Add embed widget settings successfully
organizer.or.event.id.not.exist=Organizer/Event id not exists
widget.setting.already.exists=Embed widget setting already exists
widget.setting.type.not.exists=Embed widget setting type not exists
widget.setting.not.found=Embed widget setting not found
not.authorized.to.create=Not Authorized To Create Integration
white.label.event.not.found=This white label event does not exist
failed.to.get.user.info=Error while fetching users details
failed.to.generate.user.token=Error while creating user token
failed.to.delete.solution.instance=Error while delete solution instance
failed.to.reconfigure.solution.instance=Error while reconfigure solution instance
failed.to.get.auth.code=Error while generating authorization code
donation.successful=Thank you for donating. Please confirm your donation here [link]
transaction.config.not.found=Transaction conditional logic configuration missing for white label
whitelabel.stripe.not.configured=Please connect stripe for whitelabel event.
successfully.created.new.event=Successfully created new event
event.name.already.exist=Event name is already in use. Please try another name.
event.name.can.not.null=Event name can not be empty
cannot.change.organizer.after.event.publish=Cannot change organizer after the event is published
virtual.event.settings.created=Virtual events settings created successfully.
virtual.event.settings.updated=Virtual events settings updated successfully.
virtual.event.custom.label.updated=Virtual events custom label updated successfully.
virtual.event.already.created=Virtual event settings already created
event.challenge.config.not.found=Event Challenge Not Found.
setting.not.allowed=This settings is not allowed for your event plan
virtual.event.settings.not.found=Virtual event settings not found.
popup.activate.ticketing.module=Please confirm to activate Ticketing module.
popup.finish.stripe.payments=Please finish setting up Stripe payment processing to start collecting payments.
popup.finish.stripe.payments.for.selling=Please finish setting up Stripe payment processing to start selling tickets.
popup.finish.stripe.payments.for.donations=Please finish setting up Stripe payment processing to start collecting donations.
settings.saved=Settings saved.

bid.instarction.str=Text your Pledge to [display_number] with the three letter code and pledge amount E.g. [amount_example_str]
phone.number.already.attach.to.email.with.parameter=This phone number [phone_number] is already associated with another email address
need.to.publish.event=The [plan] plan is limited to [noOfItems] [itemSingular]. Please publish event to add more [itemPlural]
counter.exceeded.cap.usage.items=The [plan] plan is limited to [noOfItems] [itemSingular]. Please upgrade your plan to add more [itemPlural].
pre.event.access.is.limited=Pre-event access is limited to [hours] hrs prior to the start of the event on the [plan] plan. Please upgrade your plan to add more Pre Event Access Days.
session.already.exists=Session already exist with same Title
invalid.session.date.time=Please enter valid date/time format. Date is not in yyyy/MM/dd Or Time HH:mm format
session.slots.collides=Main stage sessions cannot overlap. It looks like you have a conflict with {SESSION_NAME}
networking.rule.not.found=Networking Rules not found!
networking.rule.already.exists=Networking Rule already exists.
duplicate.session.detail.id.entry=Duplicate session detail id is not allowed.
already.ticket.type.used=Can not remove this ticket type. Attendees have already registered using this ticket type
register.max.limit.reached=Your registration type only permits registering for {number_of_sessions_permitted} sessions. If you would like to register for this session, please un-register from a different session first.
can.not.upload.empty.file=Can not upload empty file. Please add data and try again.
upload.file.header.not.correct=Upload file headers name are not correct or not in proper sequence
max.records.limit=Maximum 100 records are allowed for CSV upload.
session.can.not.be.delete=This session cannot be deleted because broadcast is running. Please stop broadcast.
invalid.session.format=Invalid Session Format For Selected Area
session.position.changed.successfully=Session position changed successfully ...
can.not.allowed.for.concurrent.session=Only allowed for concurrent sessions
session.deleted=Session deleted.
session.duplicated=Session Duplicated Successfully.
session.hidden=Session Hidden Successfully.
time=Time must be null OR must be within event start and end date/time OR Date is not in dd/MM/yyyy Or Time HH:mm format
title=Title must be not null OR Title must be less than 255 characters.
format=Format must be not null OR must be REGULAR_SESSION, MEET_UP, MAIN_STAGE_SESSION, WORKSHOP, EXPO, BREAK, OTHER
capacity=Capacity must be numeric or empty.
location.id=Location ID must be numeric or empty.
signup.text=By signing up, I agree to Accelevent&#39;s <a href="https://www.accelevents.com/terms-conditions/" target="_blank">terms of service</a> and <a href="https://www.accelevents.com/Privacy-Policy/" target="_blank">privacy policy</a>.
key.value.already.exist={KEY} name already exist
position.changed.successfully=Position changed successfully ...
audience.filter.already.exists=Audience filter already exist with same name
audience.filter.not.found=Audience Filter not found
speaker.already.exist=Speaker already exist with same email
email.is.already.present=Record for [email_address] email is already present.
speaker.position.changed.successfully=Speaker position changed successfully ...
no.speaker.profile.present=No Speaker Profile Present
social_media_url.is.invalid=social media URL is not valid, please enter the valid social media URL.
first.name.not.be.null=First name must be not null OR First name must be less than 50 characters.
primary.session.invalid=Primary Sessions field contains session IDs that do not belong to this event.
secondary.session.invalid=Secondary Sessions field contains session IDs that do not belong to this event
primary.secondary.conflict=Primary speaker and Secondary speaker can not be identical
secondary.session.duplicate=Secondary Sessions field contains duplicate session IDs.
primary.session.duplicate=Primary Sessions field contains duplicate session IDs.
session.ids.invalid=Sessions field contains invalid IDs. Only numeric values are allowed.
last.name.not.be.null=Last name must be not null OR Last name must be less than 50 characters.
email.not.be.null=Email must be not null OR Email must be less than 75 characters.
email.not.valid=This is not valid email address.
email.already.in.csv.file=[email_address] Already present in csv file.
tital.not.be.null=Title must be not null OR Title must be less than 255 characters.
company.must.be.not.null=Company must be not null Company must be less than 255 characters.
linkdin.url.not.valid=LinkedIn URL is not valid
instagram.url.not.valid=Instagram Handle URL is not valid
twitter.url.not.valid=Twitter Handle URL is not valid
email.already.exist=[email_address] Email Already exist.
ticketing.setting.not.found=Ticketing setting not found
coupon.updated.successfully=Coupon updated successfully
more.coupon.is.used.then.set=More coupon is used then set
ticketing.coupon.percentage=Ticketing coupon percentage should not be greater then 100
fill.the.recurring.relative.start.and.end.time=Please fill the recurring relative start and end time.
ticketing.coupon.already.exist=Ticketing coupon already exist
discount.Coupon.Saved=Discount Coupon Saved.
coupon.deleted.successfully=Coupon deleted successfully
discount.code.cannot.be.deleted.once.it.has.been.used=A discount code cannot be deleted once it has been used.
ticket.type.does.not.grant.access=Your ticket type does not grant access to this session
auto.create.moderator=[first_name] [last_name] is now a moderator for this session
successfully.saved.wants.to.learn=Wants to learn saved successfully.
speaker.email.change.not.have.attendee.access=This new speaker has no attendee access yet, please select a ticket type.
speaker.updated=Speaker updated
virtual.portal.image.not.found=Virtual portal image not found
speaker.already.exist.with.email=Speaker already exist with [email_address]
not_found_badges=Badge not found
badges.added.successfully=Badge added successfully
badges.updated.successfully=Badge updated successfully
badges.deleted=Badge deleted successfully
not.found.badges.image=Badge image not found
duplicate.badges.name=Badge name should not be duplicate, Please enter unique badge name
duplicate.ticketing.type.id=Ticket type [ticketing_type_name] is already selected in [badge_name] badge
attendee.connect.request.cancelled=Connection request cancelled
attendees.already.connected.or.rejected=This connection request is not in pending status and hence can not be cancelled
attendee.connection.removed=Connection removed
attendee.has.not.connected=You are not connected with this attendee.
networking.lounge.deleted.by.the.admin=Networking Lounge Deleted By The Admin. Please Refresh The Page And Try Again.
not.allowed.to.access.virtual.event.page=The event format is In-Person and does not allow access to the Virtual Event Hub
does.not.allowed.to.change.event.format.after.event.start=After the event has started, does not allow to change the event format.
does.not.allowed.to.change.event.format.after.pre.event.start=After the pre-event access start, does not allow to change the event format.
not.possible.to.activate.autoplay=It's not possible to activate Autoplay before uploading a video
short.description.should.not.be.more.than.150.characters=Short description should not be more than 150 characters.
ticket.does.not.allow.lounges=Your registration does not allow access to Lounges
ticket.does.not.allow.expo=Your registration does not allow access to Exhibitor Booths
not.found.kiosk.mode.check.in.detail=Kiosk check-in detail not found
already.added.kiosk.detail=You have already added kiosk mode check-in detail
feature.not.available.for.free.plan=Kindly upgrade your plan to use this feature
networking.lounge.document.uploaded=Networking lounge ${document} uploaded successfully
networking.lounge.document.updated=Networking lounge ${document} updated successfully
networking.lounge.document.deleted=Networking lounge ${document} deleted successfully
document=document
link=link
virtual.session.tags.tracks.character.limit=Single tags/tracks should not be more than 50 characters.