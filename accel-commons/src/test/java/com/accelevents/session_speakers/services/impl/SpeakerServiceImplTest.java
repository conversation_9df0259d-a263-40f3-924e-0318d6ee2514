package com.accelevents.session_speakers.services.impl;

import com.accelevents.billing.chargebee.service.impl.EventPlanConfigServiceImpl;
import com.accelevents.common.dto.UploadSpeakerDto;
import com.accelevents.common.dto.UploadedSpeakerResponseContainer;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventPlanConfig;
import com.accelevents.domain.PlanConfig;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.session_speakers.*;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.ResponseDto;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.ExternalEventSyncTrackingRepository;
import com.accelevents.repositories.TicketingTypeCommonRepo;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.session.ROSessionRepoService;
import com.accelevents.ro.session.ROSessionSpeakerService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.services.neptune.NeptuneAttendeeDetailService;
import com.accelevents.session_speakers.dto.FirstLastNameDto;
import com.accelevents.session_speakers.dto.SpeakerBasicDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.dto.SpeakerTicketTypeDTO;
import com.accelevents.session_speakers.repo.SpeakerRepo;
import com.accelevents.session_speakers.services.*;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static com.accelevents.exceptions.NotFoundException.SessionSpeakerNotFound.SPEAKER_NOT_FOUND;
import static com.accelevents.utils.Constants.JOB_TITLE;
import static com.accelevents.utils.Constants.ORGANIZATION;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SpeakerServiceImplTest {

    @Mock
    private SpeakerRepo repo;
    @Mock
    private SpeakerRepoService speakerRepoService;
    @Mock
    private SessionSpeakerService sessionSpeakerService;
    @Mock
    private ROSessionSpeakerService roSessionSpeakerService;
    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;
    @Mock
    private UserSessionService userSessionService;
    @Mock
    private GetStreamService getStreamService;
    @Mock
    private CheckInAuditLogService checkInAuditLogService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private NeptuneAttendeeDetailService neptuneDBService;
    @Mock
    private SessionRepoService sessionRepoService;
    @Spy
    @InjectMocks
    private SpeakerServiceImpl speakerServiceImpl;
    @Mock
    private DeviceCheckerService deviceCheckerService;
    @Mock
    private EventTicketsService eventTicketsService;
    @Mock
    private SessionDetailsRepoService sessionDetailsRepoService;
    @Mock
    private SpeakerHelperService speakerHelperService;
    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Mock
    private SessionTagAndTrackService sessionTagAndTrackService;
    @Mock
    private VirtualEventService virtualEventService;
    @Mock
    private EventPlanConfigServiceImpl eventPlanConfigService;
    @Mock
    private ExternalEventSyncTrackingRepository externalEventSyncTrackingRepository;

    @Mock
    private  EventTaskService eventTaskService;

    @Mock
    private CustomFormAttributeDataRepository customFormAttributeDataRepository;
    @Mock
    private ROEventService roEventService;
    @Mock
    private CustomFormAttributeService customFormAttributeService;
    @Mock
    private ROSessionRepoService roSessionRepoService;

    private Speaker speaker;
    private SpeakerDTO speakerDTO;
    private UploadSpeakerDto uploadSpeakerDto;
    private Event event;
    private User user;
    private Session session;
    private List<Long> resultIds;
    private SpeakerTicketTypeDTO speakerTicketTypeDTO;
    private SessionDetails sessionDetails;
    private SessionTagAndTrack sessionTagAndTrack;
    private SpeakerBasicDTO speakerBasicDTO;

    private Long sessionId = 1L;
    private Long speakerId = 1L;
    private double position = 1000;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();

		SessionSpeaker sessionSpeaker = new SessionSpeaker(sessionId, speakerId, position, false);
        sessionSpeaker.setId(1L);

        speaker = EventDataUtil.getSpeaker();
        speaker.setPosition(position);

        speakerDTO = EventDataUtil.getSpeakerDto();

        session = EventDataUtil.getSession();

        resultIds = new ArrayList<>();
        resultIds.add(1L);

        uploadSpeakerDto = new UploadSpeakerDto();
        uploadSpeakerDto.setFirstName("Milan");
        uploadSpeakerDto.setLastName("Dobariya");
        uploadSpeakerDto.setEmail("<EMAIL>");

        speakerTicketTypeDTO = new SpeakerTicketTypeDTO(1L,1L,Boolean.TRUE);

        sessionDetails = new SessionDetails();
        sessionDetails.setId(1L);
        sessionDetails.setSession(session);

        speakerBasicDTO = EventDataUtil.getSpeakerBasicDTO();

        sessionTagAndTrack = new SessionTagAndTrack(1L,1L);
    }

    @Test
    void test_getSpeakerById() {

        //mock
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.of(speaker));

        //Execution
        Speaker speakerData = speakerServiceImpl.getSpeakerById(speakerId, event);

        //Assertion
        verify(speakerRepoService).findByEventIdAndId(event.getEventId(),speakerId);

        assertNotNull(speakerData);
        assertEquals(speakerData.getId(), speakerId.longValue());
    }

    @Test
    void test_getSpeakerById_speakerNotFound() {

        //mock
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> speakerServiceImpl.getSpeakerById(speakerId, event));

        //Assertion
        assertEquals(SPEAKER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(speakerRepoService).findByEventIdAndId(event.getEventId(), speakerId);
    }

    @Test
    void test_getSpeakerByEventId() {

        //mock
        when(speakerRepoService.findSpeakerByEventId(event.getEventId())).thenReturn(Collections.singletonList(speaker));

        //Execution
        List<Speaker> speakers = speakerServiceImpl.getSpeakerByEventId(event);

        //Assertion
        verify(speakerRepoService).findSpeakerByEventId(event.getEventId());

        assertNotNull(speakers);
        assertEquals(speaker.getId(), speakerId.longValue());
    }

    @Test
    void test_getSpeakerByEventId_speakerNotFound() {

        //mock
        when(speakerRepoService.findSpeakerByEventId(event.getEventId())).thenReturn(Collections.emptyList());

        //Execution
        List<Speaker> speakers = speakerServiceImpl.getSpeakerByEventId(event);

        //Assertion
        verify(speakerRepoService).findSpeakerByEventId(event.getEventId());

        assertTrue(speakers.isEmpty());
    }

   // @Test
    void createSpeaker() {

        //mock
        when(userService.signUpBidderUserAndReturnUser(any(), any(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(user);

        //Execution
        Long id = speakerServiceImpl.createSpeaker(speakerDTO, event);

        //Assertion
        verify(userService).signUpBidderUserAndReturnUser(any(), any(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean());

        ArgumentCaptor<Speaker> speakerArgumentCaptor = ArgumentCaptor.forClass(Speaker.class);
        verify(repo).save(speakerArgumentCaptor.capture());

        Speaker speaker = speakerArgumentCaptor.getValue();
        assertEquals(speaker.getFirstName(), speakerDTO.getFirstName());
        assertEquals(speaker.getLastName(), speakerDTO.getLastName());
        assertEquals(speaker.getEmail(), speakerDTO.getEmail());
        assertEquals(speaker.getBio(), speakerDTO.getBio());
        assertEquals(speaker.getCompany(), speakerDTO.getCompany());
        assertEquals(speaker.getId(), speakerDTO.getSpeakerId().longValue());
    }

    @Test
    void test_updateSpeaker() {

        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = "EN";

        //mock
        user.setEmail("<EMAIL>");
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.of(speaker));
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));

        List<String> attributeNames = Arrays.asList(ORGANIZATION, JOB_TITLE);
        when(ticketHolderRequiredAttributesService
                .findAllByAttributeNamesAndEventIdAndRecurringEventIdIsNULLAndDataType(attributeNames, event, DataType.TICKET)).thenReturn(Collections.emptyList());

        speakerDTO.setEmail("<EMAIL>");
        when(checkInAuditLogService.countPreviousCheckIn(event, speaker.getUserId())).thenReturn(1L);

        //Execution
        speakerServiceImpl.updateSpeaker(speakerId, speakerDTO, event,user,languageCode,languageMap);

        //Assertion
        verify(speakerRepoService).findByEventIdAndId(event.getEventId(),speakerId);

        ArgumentCaptor<Speaker> speakerArgumentCaptor = ArgumentCaptor.forClass(Speaker.class);
        verify(speakerRepoService).save(speakerArgumentCaptor.capture());

        Speaker speaker = speakerArgumentCaptor.getValue();
        assertEquals(speaker.getFirstName(), speakerDTO.getFirstName());
        assertEquals(speaker.getLastName(), speakerDTO.getLastName());
        assertEquals(speaker.getEmail(), speakerDTO.getEmail());
        assertEquals(speaker.getBio(), speakerDTO.getBio());
        assertEquals(speaker.getCompany(), speakerDTO.getCompany());
        assertEquals(speaker.getId(), speakerDTO.getSpeakerId().longValue());
    }

    @Test
    void test_getAllSpeakerByEventId() {

        //setup
        String search = null;
        Pageable pageable = PageRequest.of(1, 10);
        Page<Speaker> page = new PageImpl<>(Collections.singletonList(speaker));

        //mock
        when(speakerRepoService.getAllSpeakerByEvent(search,event.getEventId(), pageable)).thenReturn(page);

        //Execution
        Page<Speaker> speakers = speakerServiceImpl.getAllSpeakerByEventId(event, search, pageable);

        //Assertion
        verify(speakerRepoService).getAllSpeakerByEvent(search, event.getEventId(), pageable);

        assertEquals(speakers.getContent().get(0).getId(), speakerId.longValue());
        assertEquals(speakers.getContent().get(0).getEventId().longValue(), event.getEventId());
    }

    @Test
    void test_getSpeakerList_expandNotSPEAKER() {

        //setup
        String search = null;
        Pageable pageable = PageRequest.of(1, 10);
        Page<Speaker> page = new PageImpl<>(Collections.singletonList(speaker));

        //mock
        when(speakerRepoService.getAllSpeakerByEvent(search,event.getEventId(), pageable)).thenReturn(page);
        when(deviceCheckerService.speakerIdAndRunDeviceChecker(anyList())).thenReturn(Collections.emptyMap());
        when(checkInAuditLogService.countPreviousCheckInUserList(eq(event),anyList())).thenReturn(Collections.emptyList());
        when(eventTicketsService.findUsersTicketsByEventAndUserIds(eq(event),anyList())).thenReturn(Collections.emptyList());

        //Execution
        DataTableResponse dataTableResponse = speakerServiceImpl.getSpeakerList(event, search, "", pageable,true);

        //Assertion
        verify(speakerRepoService).getAllSpeakerByEvent(search,event.getEventId(), pageable);

        assertNotNull(dataTableResponse.getData());
    }

    @Test
    void test_getSpeakerList() {

        //setup
        Map<Long, List<Session>> speakerIdsSessions = new HashMap<>();
        speakerIdsSessions.put(sessionId, Collections.singletonList(session));

        String search = null;
        Pageable pageable = PageRequest.of(1, 10);
        Page<Speaker> page = new PageImpl<>(Collections.singletonList(speaker));

        //mock
        when(speakerRepoService.getAllSpeakerByEvent(search,event.getEventId(), pageable)).thenReturn(page);
        when(roSessionSpeakerService.getSessionSpeakerIdSpeakerIds(anyList(),anyBoolean())).thenReturn(speakerIdsSessions);
        when(checkInAuditLogService.countPreviousCheckInUserList(eq(event),anyList())).thenReturn(Collections.emptyList());
        when(eventTicketsService.findUsersTicketsByEventAndUserIds(eq(event),anyList())).thenReturn(Collections.emptyList());
        //Execution
        DataTableResponse dataTableResponse = speakerServiceImpl.getSpeakerList(event, search, "SESSION", pageable,true);

        //Assertion
        verify(speakerRepoService).getAllSpeakerByEvent(search,event.getEventId(), pageable);
        verify(roSessionSpeakerService).getSessionSpeakerIdSpeakerIds(anyList(),anyBoolean());

        assertNotNull(dataTableResponse.getData());
    }

    @Test
    void test_getSpeakersBasicInfo() {

        //mock
        when(speakerRepoService.findSpeakerByEventId(event.getEventId())).thenReturn(Collections.singletonList(speaker));

        //Execution
        List<SpeakerBasicDTO> speakerBasicDTOS = speakerServiceImpl.getSpeakersBasicInfo(event);

        //Assertion
        verify(speakerRepoService).findSpeakerByEventId(event.getEventId());

        assertEquals(speakerBasicDTOS.get(0).getEmail(), speaker.getEmail());
        assertEquals(speakerBasicDTOS.get(0).getFirstName(), speaker.getFirstName());
        assertEquals(speakerBasicDTOS.get(0).getLastName(), speaker.getLastName());
        assertEquals(speakerBasicDTOS.get(0).getSpeakerId().longValue(), speaker.getId());
        assertEquals(speakerBasicDTOS.get(0).getTitle(), speaker.getTitle());
    }

    @Test
    void test_deleteSpeaker() {

        //mock
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.of(speaker));
        Mockito.doNothing().when(sessionSpeakerService).deleteSessionSpeakerBySpeaker(speakerId, event);
        Mockito.doNothing().when(speakerRepoService).deleteSpeaker(speaker, event);

        //Execution
        speakerServiceImpl.deleteSpeakerAlongWithSessionSpeaker(speakerId, event);

        //Assertion
        verify(sessionSpeakerService).deleteSessionSpeakerBySpeaker(speakerId, event);
        verify(speakerRepoService).deleteSpeaker(speaker, event);
    }

    @Test
    void test_getSpeakerIdForTheUserInEvent() {

        //mock
        when(speakerRepoService.findIdByEventIdAndUserId(event.getEventId(), user.getUserId())).thenReturn(Optional.of(speakerId));

        //Execution
        Long id = speakerServiceImpl.getSpeakerIdForTheUserInEvent(event, user);

        //Assertion
        verify(speakerRepoService).findIdByEventIdAndUserId(event.getEventId(), user.getUserId());
        assertEquals(id, speakerId);
    }

    //TODO: Junit5 review test
    /*@Test
    void test_getSpeakerByDto_expandSESSION(){

        //setup
        Map<Long, List<Session>> speakerIdsSessions = new HashMap<>();
        speakerIdsSessions.put(speakerId, Collections.singletonList(session));

        String expand = "SESSION,TAG,TRACK";
        event.setEventFormat(EventFormat.HYBRID);
        resultIds.add(1L);

        //mock
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.of(speaker));
        when(sessionSpeakerService.getSessionSpeakerIdSpeakerIds(Collections.singletonList(speakerId), true,false)).thenReturn(speakerIdsSessions);
        when(sessionRepoService.filterPrivateSessions(event, user,speakerIdsSessions.get(speakerId))).thenReturn(Collections.singletonList(session));
        when(eventTicketsService.findUserTicketsByEventAndUserId(event, speaker.getUserId())).thenReturn(Collections.singletonList(speakerTicketTypeDTO));
        when(ticketingTypeCommonRepo.findRegisteredTicketingIdWithOutInPersonTicketType(anyList())).thenReturn(resultIds);
        when(sessionDetailsRepoService.findSessionDetailsBySessionIds(anyList())).thenReturn(Collections.singletonList(sessionDetails));
        when(sessionTagAndTrackService.findBySessionIds(anyList())).thenReturn(Collections .singletonList(sessionTagAndTrack));


        //Execution
        SpeakerDTO speakerDTO = speakerServiceImpl.getSpeakerByDto(speakerId, event, expand, user,false);

        //Assertion
        verify(speakerRepoService).findByEventIdAndId(event.getEventId(),speakerId);
        verify(sessionSpeakerService).getSessionSpeakerIdSpeakerIds(Collections.singletonList(speakerId), true,false);

        assertEquals(speakerDTO.getSpeakerId(), speakerId);
       *//* assertNull(speakerDTO.getSessionDTO().get(0).getSpeakerList());
        assertNull(speakerDTO.getSessionDTO().get(0).getSessionStats());*//*

    }*/

    @Test
    void test_getSpeakerByDto_expandSESSIONcurrentUserRegisteredEventTicketId(){

        //setup
        Map<Long, List<Session>> speakerIdsSessions = new HashMap<>();
        speakerIdsSessions.put(speakerId, Collections.singletonList(session));

        Map<Long, List<Long>> currentUserSessionIdAndEventTicketIdMap = new HashMap<>();
        String expand = "SESSION.currentUserRegisteredEventTicketId";

        //mock
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.of(speaker));
        when(roSessionSpeakerService.getSessionSpeakerIdSpeakerIds(Collections.singletonList(speakerId), true,true)).thenReturn(speakerIdsSessions);
        when(userSessionService.findRegisteredEventTicketIdByUserAndSessionId(user.getUserId(), Collections.singletonList(sessionId))).thenReturn(currentUserSessionIdAndEventTicketIdMap);

        //Execution
        SpeakerDTO speakerDTO = speakerServiceImpl.getSpeakerByDto(speakerId, event, expand, user,true);

        //Assertion
        verify(speakerRepoService).findByEventIdAndId(event.getEventId(),speakerId);
        verify(roSessionSpeakerService).getSessionSpeakerIdSpeakerIds(Collections.singletonList(speakerId), true,true);
        verify(userSessionService).findRegisteredEventTicketIdByUserAndSessionId(user.getUserId(), Collections.singletonList(sessionId));

        assertEquals(speakerDTO.getSpeakerId(), speakerId);
        assertNull(speakerDTO.getSessionDTO().get(0).getSpeakerList());
        assertNull(speakerDTO.getSessionDTO().get(0).getSessionStats());
    }

    @Test
    void test_isSpeakerInEvent_present(){

        //mock
        when(speakerRepoService.findByIdEventIdAndUserId(any(), anyLong())).thenReturn(true);

        //Execution
        boolean isSpeakerInEvent = speakerServiceImpl.isSpeakerInEvent(event, user);

        //Assertion
        assertTrue(isSpeakerInEvent);
    }

    @Test
    void test_isSpeakerInEvent_notPresent(){

        //mock


        //Execution
        boolean isSpeakerInEvent = speakerServiceImpl.isSpeakerInEvent(event, user);

        //Assertion
        assertFalse(isSpeakerInEvent);
    }

    @Test
    void test_speaker_CSV_upload() throws IOException {

        //setup
        User user = new User();
        user.setUserId(1L);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.STARTER.getName());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(planConfig);

        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/SpeakerCsvFile.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/SpeakerCsvFile.csv", "multipart/form-data", inputFile);

        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig);
        UploadedSpeakerResponseContainer response = speakerServiceImpl.parseSpeakerCSV(file, event, user, languageMap);

        assertTrue(response != null && response.getValidSpeakers() != null);
    }

    @Test
    void test_speaker_CSV_upload_Wrong_First_name() throws IOException {

        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.STARTER.getName());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(planConfig);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/SpeakerCsvFileWrongFormat.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/SpeakerCsvFileWrongFormat.csv", "multipart/form-data", inputFile);
        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig);
        UploadedSpeakerResponseContainer response = speakerServiceImpl.parseSpeakerCSV(file, event,user,languageMap);

        assertTrue(response != null && response.getInvalidSpeakers()!= null);
    }

    @Test
    void test_speaker_CSV_upload_Wrong_Last_name() throws IOException {

        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.STARTER.getName());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(planConfig);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        //setup
        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/SpeakerCsvFileWrongFormat1.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/SpeakerCsvFileWrongFormat1.csv", "multipart/form-data", inputFile);
        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig);
        UploadedSpeakerResponseContainer response = speakerServiceImpl.parseSpeakerCSV(file, event,user,languageMap);

        assertTrue(response != null && response.getInvalidSpeakers() != null);
    }
    @Test
    void test_speaker_CSV_upload_with_wrong_header() throws IOException {

        //setup
        User user = new User();
        user.setUserId(1L);
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.STARTER.getName());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(planConfig);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();

        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/SpeakerCsvFile.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/SpeakerCsvFile.csv", "multipart/form-data", inputFile);
        Mockito.doReturn(true).when(speakerServiceImpl).isValidSpeakerCsvFileHeaderAttribute(any());
        lenient().doReturn(true).when(speakerServiceImpl)
                .isValidFirstNameAndLastNameAndEmailAndTitleAndCompany(
                        any(), anyString(), anyString(), anyString(), anyString(),
                        anyString(), anyString(), anyString(), anyString(), anyString(),
                        anyString(), anyString(), any(), any(), anyMap(),
                        anyBoolean(), anyList(), anyMap(), anyLong(),
                        anyList(), anyList(), anyList(), anyBoolean()
                );


        when(roEventService.getLanguageCodeByUserOrEvent(any(),any())).thenReturn("EN");
        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig);
        UploadedSpeakerResponseContainer response = speakerServiceImpl.parseSpeakerCSV(file, event, user, languageMap);
        assertTrue(response != null && response.getValidSpeakers() != null);
    }

    public static Object[] getHeader(){
        String[] header =  {Constants.FIRST_NAME, Constants.EMAIL,Constants.TITLE,Constants.COMPANY,Constants.BIO,Constants.LINKEDIN_URL,Constants.INSTAGRAM_HANDLE,Constants.TWITTER_HANDLE};
        String[] header1 = {Constants.FIRST_NAME, Constants.PHONE_NUMBER, Constants.LAST_NAME, Constants.EMAIL};
        String[] header2 = {Constants.LAST_NAME, Constants.FIRST_NAME, Constants.PHONE_NUMBER, Constants.EMAIL};

        return new Object[]{
                new Object[]{header} ,
                new Object[]{header1} ,
                new Object[]{header2} ,
        };
    }
    @ParameterizedTest
    @MethodSource("getHeader")
    void test_invalid_header_attribute(String[] header) {
        //Execution
        boolean validateHeader = speakerServiceImpl.isValidSpeakerCsvFileHeaderAttribute(header);
        //Assertion
        assertFalse(validateHeader);
    }

    @Test
    void test_valid_header_attribute() {
        //setup
        String[] header = {Constants.SPEAKER_SPACE_ID,Constants.FIRST_NAME, Constants.LAST_NAME ,Constants.EMAIL,Constants.PRONOUNS,Constants.TITLE,Constants.COMPANY,Constants.BIO,Constants.LINKEDIN_URL,Constants.INSTAGRAM_HANDLE,Constants.TWITTER_HANDLE,Constants.OVERRIDE_PROFILE_DETAILS, Constants.PRIMARY_SESSIONS, Constants.SECONDARY_SESSIONS};
        //Execution
        boolean validateHeader = speakerServiceImpl.isValidSpeakerCsvFileHeaderAttribute(header);
        //Assertion
        assertTrue(validateHeader);
    }

    @Test
    void test_getOrCreateUser_get(){

        //setup
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        when(userService.signUpBidderUserAndReturnUser(any(), any(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(user);

        //Execution
        User orCreateUser = speakerServiceImpl.getOrCreateUser(speakerDTO, event);

        //Assertion
        assertEquals(user,orCreateUser);

    }

    @Test
    void test_getOrCreateUser_create(){

        //setup
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.ofNullable(null));
        when(userService.signUpBidderUserAndReturnUser(any(), any(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(user);

        //Execution
        User orCreateUser = speakerServiceImpl.getOrCreateUser(speakerDTO, event);

        //Assertion
        assertEquals(user,orCreateUser);
    }

    @Test
    void test_checkSpeakerAlreadyExistByEmail_speaker_already_exist(){

        when(speakerRepoService.checkSpeakerAlreadyExistByEmail(anyString(), anyLong())).thenReturn(Boolean.TRUE);

        try {
            // Due to private method, we need to modify method accessibility
            Method checkSpeakerAlreadyExistByEmail = SpeakerServiceImpl.class.getDeclaredMethod("checkSpeakerAlreadyExistByEmail", SpeakerDTO.class, Event.class);
            checkSpeakerAlreadyExistByEmail.setAccessible(true);
            checkSpeakerAlreadyExistByEmail.invoke(speakerServiceImpl, speakerDTO, event);
        } catch (InvocationTargetException e) {
            assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ALREADY_EXIST.getDeveloperMessage(),e.getTargetException().getMessage());
        } catch (Exception e){
            //Ignore
        }
    }

    @Test
    void test_checkSpeakerAlreadyExistByEmail(){

        when(speakerRepoService.checkSpeakerAlreadyExistByEmail(anyString(), anyLong())).thenReturn(Boolean.FALSE);

        try {
            // Due to private method, we need to modify method accessibility
            Method checkSpeakerAlreadyExistByEmail = SpeakerServiceImpl.class.getDeclaredMethod("checkSpeakerAlreadyExistByEmail", SpeakerDTO.class, Event.class);
            checkSpeakerAlreadyExistByEmail.setAccessible(true);
            checkSpeakerAlreadyExistByEmail.invoke(speakerServiceImpl, speakerDTO, event);
        } catch (Exception e){
            //Ignore
        }

        assertTrue(true);
    }

    @Test
    void test_updateSpeaker_speaker_email_already_exist(){

        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = "EN";
        user.setEmail("<EMAIL>");
        speakerDTO.setEmail("<EMAIL>");

        //mock
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.of(speaker));

        speaker.setId(2L);
        when(speakerRepoService.findByEventIdAndUserId(anyLong(), anyLong())).thenReturn(speaker);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> speakerServiceImpl.updateSpeaker(speakerId, speakerDTO, event,user,languageCode,languageMap));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.SPEAKER_ALREADY_EXIST_WITH_EMAIL.getDeveloperMessage().replace(Constants.EMAIL_ADDRESS_PARAMETER, speaker.getEmail()),
                exception.getMessage());

    }

    @Test
    void test_updateSpeaker_speaker_email_changed(){

        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = "EN";

        //mock
        user.setEmail("<EMAIL>");
        when(speakerRepoService.findByEventIdAndId(event.getEventId(),speakerId)).thenReturn(Optional.of(speaker));
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        when(checkInAuditLogService.findAuditLogByUserId(user.getUserId())).thenReturn(0L);
        when(virtualEventService.isSpeakerInviteEnable(any())).thenReturn(true);
        Mockito.doNothing().when(speakerHelperService).sendInvite(event, speaker.getId(), speaker.getEmail());
        when(checkInAuditLogService.countPreviousCheckIn(any(), any())).thenReturn(1L);
        speakerDTO.setEmail("<EMAIL>");

        //Execution
        ResponseDto responseDto = speakerServiceImpl.updateSpeaker(speakerId, speakerDTO, event, user, languageCode, languageMap);

        //Assertion
        assertEquals(Constants.SUCCESS,responseDto.getType());
        assertEquals(Constants.SPEAKER_EMAIL_CHANGE_NOT_HAVE_ATTENDEE_ACCESS,responseDto.getMessage());
    }

    @Test
    void test_removeOldSpeakerFromChannels(){

        try {
            // Due to private method, we need to modify method accessibility
            Method removeOldSpeakerFromChannels = SpeakerServiceImpl.class.getDeclaredMethod("removeOldSpeakerFromChannels", boolean.class,List.class, Speaker.class);
            removeOldSpeakerFromChannels.setAccessible(true);
            Boolean invoke = (Boolean)removeOldSpeakerFromChannels.invoke(speakerServiceImpl, true, resultIds, speaker);
            assertEquals(Boolean.TRUE,invoke);
        } catch (Exception e){
            //Ignore
            e.printStackTrace();
        }
    }

    @Test
    void test_createOrUpdateGetStreamUser_true(){

        try {
            // Due to private method, we need to modify method accessibility
            Method removeOldSpeakerFromChannels = SpeakerServiceImpl.class.getDeclaredMethod("createOrUpdateGetStreamUser", boolean.class, Speaker.class, List.class,Event.class);
            removeOldSpeakerFromChannels.setAccessible(true);
            removeOldSpeakerFromChannels.invoke(speakerServiceImpl, true, speaker,resultIds, event);
        } catch (Exception e){
            //Ignore
            e.printStackTrace();
        }
        assertTrue(true);
    }

    @Test
    void test_createOrUpdateGetStreamUser_false(){

        try {
            // Due to private method, we need to modify method accessibility
            Method removeOldSpeakerFromChannels = SpeakerServiceImpl.class.getDeclaredMethod("createOrUpdateGetStreamUser", boolean.class, Speaker.class, List.class,Event.class);
            removeOldSpeakerFromChannels.setAccessible(true);
            removeOldSpeakerFromChannels.invoke(speakerServiceImpl, true, speaker,resultIds, event);
        } catch (Exception e){
            //Ignore
            e.printStackTrace();
        }
        assertTrue(true);
    }

    @Test
    void test_getUserId(){

        when(speakerRepoService.getUserId(anyLong())).thenReturn(1L);
        long userId = speakerServiceImpl.getUserId(1L);

        assertEquals(1L,userId);
    }

    @Test
    void test_getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(){

        when(speakerRepoService.getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(anyLong(), anyLong())).thenReturn(Collections.singletonList(speaker));
        List<Speaker> speakersByIdBetweenAndUserIdIsZeroAndEmailNotNull = speakerServiceImpl.getSpeakersByIdBetweenAndUserIdIsZeroAndEmailNotNull(1L, 2L);

        assertEquals(Collections.singletonList(speaker),speakersByIdBetweenAndUserIdIsZeroAndEmailNotNull);
    }

    @Test
    void test_getOrCreateUser(){

        when(userService.signUpBidderUserAndReturnUser(any(), any(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(user);
        User orCreateUser = speakerServiceImpl.getOrCreateUserFromUploadSpeakerDto(uploadSpeakerDto, event);

        assertEquals(user,orCreateUser);

    }

    @Test
    void test_updateSpeakerSequence(){

        when(speakerRepoService.findByEventIdAndId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(speaker));
        when(speakerRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(speaker));

        speakerServiceImpl.updateSpeakerSequence(1L,1L,1L,event,user);

        ArgumentCaptor<Speaker> speakerArgumentCaptor = ArgumentCaptor.forClass(Speaker.class);
        verify(speakerRepoService).save(speakerArgumentCaptor.capture());

        Speaker resultSp = speakerArgumentCaptor.getValue();

        assertEquals(speaker.getId(),resultSp.getId());

    }

    @Test
    void test_updateSpeakerSequence_both_speaker_present(){

        //setUp
        speaker.setPosition(1000D);
        when(speakerRepoService.findByEventIdAndId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(speaker));
        when(speakerRepoService.findById(1L)).thenReturn(Optional.ofNullable(speaker));

        Speaker speaker1 = speaker;
        speaker1.setId(2L);
        speaker1.setPosition(1D);
        when(speakerRepoService.findById(2L)).thenReturn(Optional.ofNullable(speaker1));
        when(speakerRepoService.nextPositionSpeaker(anyLong(),anyLong(),anyDouble())).thenReturn(Collections.singletonList(speaker));
        when(speakerRepoService.previousPositionSpeaker(anyLong(),anyLong(),anyDouble())).thenReturn(Collections.singletonList(speaker1));

        //Execution
        speakerServiceImpl.updateSpeakerSequence(1L,1L,2L,event,user);

        ArgumentCaptor<Speaker> speakerArgumentCaptor = ArgumentCaptor.forClass(Speaker.class);
        verify(speakerRepoService).save(speakerArgumentCaptor.capture());

        Speaker resultSp = speakerArgumentCaptor.getValue();

        assertEquals(speaker.getId(),resultSp.getId());
        assertEquals(speaker.getPosition(),resultSp.getPosition());

    }

    @Test
    void test_updateSpeakerSequence_top_speaker_not_present(){

        //setUp
        speaker.setPosition(1000D);
        when(speakerRepoService.findByEventIdAndId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(speaker));
        when(speakerRepoService.findById(1L)).thenReturn(Optional.ofNullable(null));

        Speaker speaker1 = speaker;
        speaker1.setId(2L);
        speaker1.setPosition(1D);
        when(speakerRepoService.findById(2L)).thenReturn(Optional.ofNullable(speaker1));


        //Execution
        speakerServiceImpl.updateSpeakerSequence(1L,1L,2L,event,user);

        ArgumentCaptor<Speaker> speakerArgumentCaptor = ArgumentCaptor.forClass(Speaker.class);
        verify(speakerRepoService).save(speakerArgumentCaptor.capture());

        Speaker resultSp = speakerArgumentCaptor.getValue();

        assertEquals(speaker.getId(),resultSp.getId());
        assertEquals(speaker.getPosition(),resultSp.getPosition());

    }

    @Test
    void test_updateSpeakerSequence_top_next_speaker_not_present(){

        //setUp
        speaker.setPosition(1000D);
        when(speakerRepoService.findByEventIdAndId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(speaker));
        when(speakerRepoService.findById(1L)).thenReturn(Optional.ofNullable(speaker));

        Speaker speaker1 = speaker;
        speaker1.setId(2L);
        speaker1.setPosition(1D);
        when(speakerRepoService.findById(2L)).thenReturn(Optional.ofNullable(null));


        //Execution
        speakerServiceImpl.updateSpeakerSequence(1L,1L,2L,event,user);

        ArgumentCaptor<Speaker> speakerArgumentCaptor = ArgumentCaptor.forClass(Speaker.class);
        verify(speakerRepoService).save(speakerArgumentCaptor.capture());

        Speaker resultSp = speakerArgumentCaptor.getValue();

        assertEquals(speaker.getId(),resultSp.getId());
        assertEquals(speaker.getPosition(),resultSp.getPosition());

    }

    @Test
    void test_findSpeakerByEventId(){

        when(speakerRepoService.findSpeakerByEventId(anyLong())).thenReturn(Collections.singletonList(speaker));

        List<Speaker> speakerByEventId = speakerServiceImpl.findSpeakerByEventId(event);

        assertEquals(Collections.singletonList(speaker),speakerByEventId);
    }

    @Test
    void test_findSpeakerUserIdsByEventId(){

        resultIds.add(1L);
        when(speakerRepoService.findSpeakerUserIdsByEventId(anyLong())).thenReturn(resultIds);

        List<Long> speakerIdByEventId = speakerServiceImpl.findSpeakerUserIdsByEventId(event);

        assertEquals(resultIds,speakerIdByEventId);
    }

    @Test
    void test_setPositionForSpeaker(){

        when(speakerRepoService.findFirstByEventIdOrderByPositionDesc(speaker.getEventId())).thenReturn(speaker);

        speaker.setId(0);
        Speaker speaker1 = speakerServiceImpl.setPositionForSpeaker(this.speaker);
        assertEquals(speaker,speaker1);
    }

    @Test
    void test_getSpeakerBySamePosition(){

        when(speakerRepoService.getSpeakerBySamePosition(anyDouble(), anyLong())).thenReturn(Collections.singletonList(speaker));

        List<Speaker> speakerBySamePosition = speakerServiceImpl.getSpeakerBySamePosition(1D, event);
        assertEquals(Collections.singletonList(speaker),speakerBySamePosition);
    }

    @Test
    void test_findSpeakerByEmail(){

        when(speakerRepoService.findByEmail(anyString())).thenReturn(Collections.singletonList(speaker));

        List<Speaker> speakerBySamePosition = speakerServiceImpl.findSpeakerByEmail("<EMAIL>");
         assertEquals(Collections.singletonList(speaker),speakerBySamePosition);

    }

    @Test
    void test_getUserInfoByEmail(){

        FirstLastNameDto firstLastNameDto = new FirstLastNameDto("Milan","Dobariya");
        when(userService.getUserInfoByEmail(anyString())).thenReturn(firstLastNameDto);

        FirstLastNameDto userInfoByEmail = speakerServiceImpl.getUserInfoByEmail("<EMAIL>");

        assertEquals(firstLastNameDto,userInfoByEmail);
    }

    @Test
    void test_saveAll(){
        when(speakerRepoService.saveAll(anyList())).thenReturn(Collections.singletonList(speaker));
        speakerServiceImpl.saveAll(Collections.singletonList(speaker));
        assertTrue(true);
    }

    @Test
    void test_removeSpeakerProfilePicture(){

        speaker.setImageUrl("testUrl");
        when(speakerRepoService.findByEventIdAndId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(speaker));

        speakerServiceImpl.removeSpeakerProfilePicture(1L,event);

        ArgumentCaptor<Speaker> argumentCaptor = ArgumentCaptor.forClass(Speaker.class);
        verify(speakerServiceImpl).save(argumentCaptor.capture());

        Speaker speaker1 = argumentCaptor.getValue();

        assertEquals(speaker,speaker1);
    }

    @Test
    void test_removeSpeakerProfilePicture_null(){

        speaker.setImageUrl(null);
        when(speakerRepoService.findByEventIdAndId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(speaker));

        Exception exception = assertThrows(NotFoundException.class,
                () -> speakerServiceImpl.removeSpeakerProfilePicture(1L,event));

        assertEquals(NotFoundException.SessionSpeakerNotFound.N0_SPEAKER_PROFILE_PRESENT.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_checkOverrideProfileEnableForSpeaker_allowOverrideDetails_null(){

        when(speakerRepoService.findByEventIdAndUserId(anyLong(), anyLong())).thenReturn(speaker);

        Boolean aBoolean = speakerServiceImpl.checkOverrideProfileEnableForSpeaker(1L, 1L);

        assertEquals(false,aBoolean);
    }

    @Test
    void test_checkOverrideProfileEnableForSpeaker_allowOverrideDetails__true(){

        speaker.setAllowOverrideDetails(true);
        when(speakerRepoService.findByEventIdAndUserId(anyLong(), anyLong())).thenReturn(speaker);

        Boolean aBoolean = speakerServiceImpl.checkOverrideProfileEnableForSpeaker(1L, 1L);

        assertEquals(true,aBoolean);
    }

    @Test
    void test_getSpeakerByEventIDAndUserId(){
        when(speakerRepoService.findSpeakerByEventIdAndUserId(anyLong(), anyLong())).thenReturn(Collections.singletonList(speaker));

        List<Speaker> speakerByEventIDAndUserId = speakerServiceImpl.getSpeakerByEventIDAndUserId(1L, 1L);

        assertEquals(Collections.singletonList(speaker),speakerByEventIDAndUserId);
    }

    @Test
    void getAllSessionsByEvent() {

        // MOCK
        when(speakerRepoService.getAllSpeakersByEventId(event.getEventId())).thenReturn(List.of(speakerBasicDTO));

        DataTableResponse response = speakerServiceImpl.getAllSpeakersListByEvent(event);

        assertEquals(1, response.getRecordsTotal());
        assertEquals(1, response.getRecordsFiltered());
        assertEquals(1, response.getData().size());
    }

}

