package com.accelevents.session_speakers.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.ro.session.ROSessionSpeakerService;
import com.accelevents.services.EventTaskService;
import com.accelevents.services.GetStreamService;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.session_speakers.dto.IdCountDto;
import com.accelevents.session_speakers.dto.SessionDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.repo.SessionSpeakerRepo;
import com.accelevents.session_speakers.services.SessionSpeakerRepoService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigInteger;
import java.util.*;

import static com.accelevents.domain.enums.EnumSessionFormat.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SessionSpeakerServiceImplTest {

    @Spy
    @InjectMocks
    private SessionSpeakerServiceImpl sessionSpeakerServiceImpl;
    @Mock
    private SessionSpeakerRepo repo;
    @Mock
    private SessionSpeakerRepoService sessionSpeakerRepoService;
    @Mock
    private ROSessionSpeakerService roSessionSpeakerService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Mock
    private SpeakerRepoService speakerRepoService;
    @Mock
    private GetStreamService getStreamService;
    @Mock
    private UserSessionService userSessionService;
    @Mock
    private EventTaskService eventTaskService;
    private SessionSpeaker sessionSpeaker;
    private Speaker speaker;
    private SpeakerDTO speakerDTO;
	private User user;
	private SessionDTO sessionDTO;
	private Event event;
    private Session session;

    private Long sessionId = 1L;
    Long speakerId = 1L;
    private double position = 1000;
    Date startDate = DateUtils.getCurrentDate();
    Date endDate = DateUtils.getAddedHours(new Date(), 5);

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

		event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        session  = EventDataUtil.getSession();

        speaker = EventDataUtil.getSpeaker();
        speaker.setPosition(position);
        speaker.setModerator(Boolean.FALSE);

        sessionSpeaker = new SessionSpeaker(sessionId, speakerId, position, false);
        sessionSpeaker.setId(1L);
        sessionSpeaker.setSpeaker(speaker);

        event = new Event();
        event.setEventId(1L);

    }

    /*@Test
    void test_addSessionSpeaker_newEntry() {

        //mock
        when(repo.findBySessionIdAndSpeakerId(sessionId, speakerId)).thenReturn(Optional.empty());

        //Execution
        sessionSpeakerServiceImpl.addSessionSpeaker(sessionId, speakerId);

        //Assertion
        verify(repo).findBySessionIdAndSpeakerId(sessionId, speakerId);

        ArgumentCaptor<SessionSpeaker> sessionSpeakerArgumentCaptor = ArgumentCaptor.forClass(SessionSpeaker.class);
        verify(repo).save(sessionSpeakerArgumentCaptor.capture());

        SessionSpeaker sessionSpeaker = sessionSpeakerArgumentCaptor.getValue();
        assertEquals(sessionSpeaker.getSessionId(), sessionId);
        assertEquals(sessionSpeaker.getSpeakerId(), speakerId);
    }*/

    @Test
    void test_addSessionSpeaker_notPresentPresent() {

        //mock
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerId(anyLong(),anyLong())).thenReturn(Optional.empty());

        when(speakerRepoService.getSpeakerByIdOrThrowNotFound(anyLong(), any())).thenReturn(speaker);
        Event event = new Event();
        //Execution

        assertEquals(Boolean.TRUE,sessionSpeakerServiceImpl.addSessionSpeaker(sessionId, speakerId, event, Boolean.FALSE));
        //Assertion
    }

    @Test
    void test_getSessionSpeaker_sessionIds() {

        //mock
        when(sessionSpeakerRepoService.findBySessionIdsIn(Collections.singletonList(sessionId))).thenReturn(Collections.singletonList(sessionSpeaker));



        assertNotNull(sessionSpeakerServiceImpl.getSessionSpeakerIdSessionDtoIds(Collections.singletonList(sessionId)));
    }

    @Test
    void test_addSessionSpeaker_alreadyPresent() {

        //mock
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, speakerId)).thenReturn(Optional.of(sessionSpeaker));

        Event event = new Event();
        //Execution
        sessionSpeakerServiceImpl.addSessionSpeaker(sessionId, speakerId, event, Boolean.FALSE);

        //Assertion
        verify(sessionSpeakerRepoService).findBySessionIdAndSpeakerId(sessionId, speakerId);
    }

    @Test
    void test_getSpeakerDtoBySession_speakerNotPresentInSession() {

        //mock
        when(sessionSpeakerRepoService.getSpeakersBySession(sessionId)).thenReturn(Collections.emptyList());

        //Execution
        List<SpeakerDTO> speakerDTOS = sessionSpeakerServiceImpl.getSpeakerDtoBySession(sessionId);

        //Assertion
        verify(sessionSpeakerRepoService).getSpeakersBySession(sessionId);

        assertTrue(speakerDTOS.isEmpty());
    }

    @Test
    void test_getSpeakerDtoBySession_speakerPresentInSession() {

        //setup
        speaker.setPosition(1000d);
        SpeakerDTO speakerDTO = new SpeakerDTO(speaker, event, null,false,0L,null);

        //mock
        when(sessionSpeakerRepoService.getSpeakersBySession(sessionId)).thenReturn(Collections.singletonList(speakerDTO));

        //Execution
        List<SpeakerDTO> speakerDTOS = sessionSpeakerServiceImpl.getSpeakerDtoBySession(sessionId);

        //Assertion
        verify(sessionSpeakerRepoService).getSpeakersBySession(sessionId);

        assertEquals(speakerDTOS.get(0).getBio(), speaker.getBio());
        assertEquals(speakerDTOS.get(0).getCompany(), speaker.getCompany());
        assertEquals(speakerDTOS.get(0).getEmail(), speaker.getEmail());
        assertEquals(speakerDTOS.get(0).getTitle(), speaker.getTitle());
        assertEquals(speakerDTOS.get(0).getSpeakerId().longValue(), speaker.getId());
    }

    @Test
    void test_deleteSessionSpeakerBySpeaker() {

        //mock
        Mockito.doNothing().when(sessionSpeakerRepoService).deleteBySpeakerId(speakerId);


        //Execution
        sessionSpeakerServiceImpl.deleteSessionSpeakerBySpeaker(speakerId, event);

        //Assertion
        verify(sessionSpeakerRepoService).deleteBySpeakerId(speakerId);
    }

    @Test
    void test_deleteSessionSpeakerBySession() {

        //mock
        /*Mockito.doNothing().when(repo).updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);*/
        Mockito.doNothing().when(sessionSpeakerRepoService).updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);

        //Execution
        sessionSpeakerServiceImpl.deleteSessionSpeakerBySession(sessionId);

        //Assertion
        verify(sessionSpeakerRepoService).updateStatusToDeleteBySessionId(sessionId, RecordStatus.DELETE);
    }

    @Test
    void test_getSessionSpeakerIdSessionIds_speakerNull() {

        //setup
        sessionSpeaker = new SessionSpeaker();
        sessionSpeaker.setId(1L);
        sessionSpeaker.setSessionId(sessionId);
        sessionSpeaker.setShowModerator(true);

        //mock
        when(sessionSpeakerRepoService.findBySessionIdsIn(Collections.singletonList(sessionId))).thenReturn(Collections.singletonList(sessionSpeaker));

        //Execution
        Map<Long, List<Speaker>> sessionSpeakerIdsBySessionIds = sessionSpeakerServiceImpl.getSessionSpeakerIdSessionIds(Collections.singletonList(sessionId));

        //Assertion
        verify(sessionSpeakerRepoService).findBySessionIdsIn(Collections.singletonList(sessionId));

        assertEquals(sessionSpeakerIdsBySessionIds.entrySet().iterator().next().getKey(), sessionId);
        assertNull(sessionSpeakerIdsBySessionIds.entrySet().iterator().next().getValue().get(0));
    }

    @Test
    void test_getSessionSpeakerIdSessionIds_sessionIdEmpty() {

        //Execution
        Map<Long, List<Speaker>> sessionSpeakerIdsBySessionIds = sessionSpeakerServiceImpl.getSessionSpeakerIdSessionIds(Collections.emptyList());

        //Assertion
        assertTrue(sessionSpeakerIdsBySessionIds.isEmpty());
    }

    /*@Test
    void getSessionSpeakerIdSpeakerIds() {

        //mock
        when(repo.findSessionBySpeakerUserId(Arrays.asList(speakerId))).thenReturn(Arrays.asList(sessionSpeaker));

        //Execution
        Map<Long, List<Session>> sessionSpeakerIdsBySpeakerIds = sessionSpeakerServiceImpl.getSessionSpeakerIdSpeakerIds(Arrays.asList(speakerId));
        //Assertion
        verify(repo).findSessionBySpeakerUserId(Arrays.asList(speakerId));

        assertEquals(sessionSpeakerIdsBySpeakerIds.entrySet().iterator().next().getKey(), sessionId);
        assertNull(sessionSpeakerIdsBySpeakerIds.entrySet().iterator().next().getValue().get(0));
    }*/

    @Test
    void test_removeSessionSpeaker_sessionSpeakerNotPresent() {

        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = "EN";
        //mock
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, speakerId)).thenReturn(Optional.empty());

        //Execution
        sessionSpeakerServiceImpl.removeSessionSpeaker(sessionId, speakerId, event, languageMap, languageCode);

        //Assertion
        verify(sessionSpeakerRepoService).findBySessionIdAndSpeakerId(sessionId, speakerId);
    }

    @Test
    void test_removeSessionSpeaker() {

        //mock
        Speaker speaker = new Speaker();
        speaker.setUserId(1L);
        sessionSpeaker.setSpeaker(speaker);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = "EN";
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId, speakerId)).thenReturn(Optional.of(sessionSpeaker));
        when( speakerRepoService.getSpeakerByIdOrThrowNotFound(speakerId, event)).thenReturn(speaker);
        Mockito.doNothing().when(sessionSpeakerRepoService).deleteSessionSpeakerById(sessionSpeaker.getId());
        Mockito.doNothing().when(userSessionService).unRegisterSpeakerUser(sessionSpeaker.getSpeaker().getUserId(),sessionId,event);

        //Execution
        sessionSpeakerServiceImpl.removeSessionSpeaker(sessionId, speakerId, event,languageMap,languageCode);

        //Assertion
        verify(sessionSpeakerRepoService).findBySessionIdAndSpeakerId(sessionId, speakerId);
        verify(sessionSpeakerRepoService).deleteSessionSpeakerById(sessionSpeaker.getId());
    }

    @Test
    void test_removeSessionSpeakerModerator() {

        //mock
        Speaker speaker = new Speaker();
        speaker.setUserId(1L);
        speaker.setModerator(Boolean.TRUE);
        sessionSpeaker.setSpeaker(speaker);
        sessionSpeaker.setModerator(Boolean.TRUE);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languageCode = "EN";

        SessionSpeaker sessionSpeaker1 = (SessionSpeaker) sessionSpeaker.clone();
        sessionSpeaker1.setModerator(false);

        //Execution
        //sessionSpeakerServiceImpl.removeSessionSpeaker(sessionId, speakerId, event,languageMap,languageCode);

        //Assertion
        /*verify(sessionSpeakerRepoService).findBySessionIdAndSpeakerId(sessionId, speakerId);
        verify(sessionSpeakerRepoService).deleteById(sessionSpeaker.getId());*/
    }

    @Test
    void test_updateSpeakerPosition() {

        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId,speakerId)).thenReturn(Optional.of(sessionSpeaker));
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerId(sessionId,speakerId+1)).thenReturn(Optional.empty());

        sessionSpeakerServiceImpl.updateSessionSpeakerPosition(speakerId,speakerId,speakerId,sessionId);

        sessionSpeakerServiceImpl.updateSessionSpeakerPosition(speakerId,speakerId+1,speakerId,sessionId);

        sessionSpeakerServiceImpl.updateSessionSpeakerPosition(speakerId,speakerId,speakerId+1,sessionId);
    }

    @Test
    void test_getSpeakerIdFromSessionFormat() {

        sessionSpeaker.setSession(session);
        when(sessionSpeakerRepoService.findAllSessionSpeakerBySpeakerIdAndSessionFormat(any(),any())).thenReturn(Collections.singletonList(sessionSpeaker));
        assertNotNull(sessionSpeakerServiceImpl.getSpeakerSessionIdBySpeakerIdAndSessionFormat(Collections.singletonList(speakerId), Arrays.asList(MAIN_STAGE,BREAKOUT_SESSION,MEET_UP)));
    }

    @Test
    void test_getSessionSpeakerMorderator() {

        sessionSpeaker.setSession(session);
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerIdAndEventId(sessionId,speakerId,event.getEventId())).thenReturn(sessionSpeaker);
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerIdAndEventId(sessionId,speakerId,event.getEventId()+1)).thenReturn(null);
        sessionSpeakerServiceImpl.setModeratorForPassedSessionIdAndSpeakerId(sessionId, speakerId,event,Boolean.TRUE);
        long previousEventId = event.getEventId();
        event.setEventId(2);

        Exception exception = assertThrows(NotFoundException.class,
                () -> sessionSpeakerServiceImpl.setModeratorForPassedSessionIdAndSpeakerId(sessionId, speakerId,event,Boolean.TRUE));

        assertEquals(NotFoundException.SessionSpeakerNotFound.SESSION_SPEAKER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        event.setEventId(previousEventId);

    }

    @Test
    void test_isShowMorderatorForSessionIdAndSpeakerId() {

        sessionSpeaker.setSession(session);
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerIdAndEventId(sessionId,speakerId,event.getEventId())).thenReturn(sessionSpeaker);
        when(sessionSpeakerRepoService.findBySessionIdAndSpeakerIdAndEventId(sessionId,speakerId,event.getEventId()+1)).thenReturn(null);
        sessionSpeakerServiceImpl.isShowModeratorForSessionIdAndSpeakerId(sessionId, speakerId,event,Boolean.TRUE);
        long previousEventId = event.getEventId();
        event.setEventId(2);

        Exception exception = assertThrows(NotFoundException.class,
                () -> sessionSpeakerServiceImpl.isShowModeratorForSessionIdAndSpeakerId(sessionId, speakerId,event,Boolean.TRUE));

        assertEquals(NotFoundException.SessionSpeakerNotFound.SESSION_SPEAKER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        event.setEventId(previousEventId);

    }

    @Test
    void test_isSpeakerMorderatorInSession() {
        when(sessionSpeakerRepoService.isSpeakerModeratorInSession(sessionId,user.getUserId())).thenReturn(Boolean.TRUE);
        assertEquals(Boolean.TRUE,sessionSpeakerServiceImpl.isSpeakerModeratorInSession(sessionId,user.getUserId()));
    }

    @Test
    void test_miscellaneousMethods(){
        when(sessionSpeakerRepoService.getAllSessionIdsBySpeakerId(speakerId)).thenReturn(Collections.singletonList(1L));
        when(sessionSpeakerRepoService.isSpeakerAvailable(speakerId)).thenReturn(Boolean.TRUE);
        when(sessionSpeakerRepoService.isSpeakerAvailable(speakerId)).thenReturn(Boolean.TRUE);
        when(sessionSpeakerRepoService.countSessionSpeakerBySessionIdIn(Collections.singletonList(sessionId))).thenReturn(Collections.singletonList(new IdCountDto(1L,1L)));
        when(sessionSpeakerRepoService.findSpeakerUserIdBySessionId(sessionId)).thenReturn(Collections.singletonList(1L));
        when(sessionSpeakerRepoService.findByEventId(event.getEventId())).thenReturn(Collections.singletonList(sessionSpeaker));
        Pageable pageable = PageRequest.of(1, 10);
        Page<Session> page = new PageImpl<>(Collections.singletonList(session));
        when (sessionSpeakerRepoService.findSessionBySpeakerUserId(anyLong(),anyLong(),anyString(), any(),any(), eq(null))).thenReturn(page);
        when (sessionSpeakerRepoService.findSessionBySpeakerUserIdByPast(anyLong(),anyLong(),anyString(),any(), any(),any(), eq(null))).thenReturn(page);
        when (sessionSpeakerRepoService.findSessionBySpeakerUserIdFuture(anyLong(),anyLong(),anyString(),any(), any(),any(), eq(null))).thenReturn(page);

        assertEquals(Collections.singletonList(1L),sessionSpeakerServiceImpl.getAllSessionIdsBySpeakerId(sessionId));
        assertNotNull(sessionSpeakerServiceImpl.getSessionIdAndSessionSpeakerCount(Collections.singletonList(sessionId)));
        assertEquals(Collections.singletonList(1L),sessionSpeakerServiceImpl.findSpeakerUserIdBySessionId(sessionId));
        assertEquals(Collections.singletonList(sessionSpeaker),sessionSpeakerServiceImpl.findByEventId(sessionId));
        assertEquals(Boolean.TRUE,sessionSpeakerServiceImpl.isSpeakerAvailable(speakerId));
        assertEquals(Boolean.TRUE,sessionSpeakerServiceImpl.isSpeakerAvailable(speakerId));
        assertNotNull(sessionSpeakerServiceImpl.getSessionByEventIdAndSpeakerUserId(event,user.getUserId(),"test",pageable,null, Boolean.TRUE));
        assertNotNull(sessionSpeakerServiceImpl.getSessionByEventIdAndSpeakerUserId(event,user.getUserId(),"test",pageable,Boolean.TRUE, Boolean.TRUE));
        assertNotNull(sessionSpeakerServiceImpl.getSessionByEventIdAndSpeakerUserId(event,user.getUserId(),"test",pageable,Boolean.FALSE, Boolean.TRUE));

    }
    /*@Test
    void test_getSessionByEventIdAndSpeakerUserId_pastFalse(){

        //setup
        String search = null;
        Pageable pageable = PageRequest.of(1, 10);
        Page<Session> page = new PageImpl<>(Arrays.asList(session));

        //mock
        when(repo.findSessionBySpeakerUserId(any(),anyLong(),anyString(), any())).thenReturn(page);

        //Execution
        Page<Session> sessions = sessionSpeakerServiceImpl.getSessionByEventIdAndSpeakerUserId(event, user.getUserId(), search, pageable);

        //Assertion
        verify(repo).findSessionBySpeakerUserId(any(),anyLong(),anyString(), any());

        assertEquals(sessions.getContent().get(0).getId(), sessionId.longValue());
    }*/

    /*@Test
    void test_getSessionByEventIdAndSpeakerUserId_pastTrue(){

        //setup
        String search = null;
        Pageable pageable = PageRequest.of(1, 10);
        Page<Session> page = new PageImpl<>(Arrays.asList(session));

        //mock
        when(repo.findSessionBySpeakerUserId(any(),anyLong(),anyString(), any())).thenReturn(page);

        //Execution
        Page<Session> sessions = sessionSpeakerServiceImpl.getSessionByEventIdAndSpeakerUserId(event, user.getUserId(), search, pageable);

        //Assertion
        verify(repo).findSessionBySpeakerUserId(any(),anyLong(),anyString(), any());

        assertEquals(sessions.getContent().get(0).getId(), sessionId.longValue());
    }*/

    @Test
    void test_isUserSpeakerInSession_speakerPresent(){

        //mock
        when(sessionSpeakerRepoService.isUserSpeakerInSession(anyLong(),anyLong())).thenReturn(BigInteger.ONE);

        //Execution
        boolean isUserSpeakerInSession = sessionSpeakerServiceImpl.isUserSpeakerInSession(user.getUserId(), sessionId);

        //Assertion
        assertTrue(isUserSpeakerInSession);
    }

    @Test
    void test_isUserSpeakerInSession_speakerNotPresent(){

        //mock
        when(sessionSpeakerRepoService.isUserSpeakerInSession(anyLong(),anyLong())).thenReturn(BigInteger.ZERO);

        //Execution
        boolean isUserSpeakerInSession = sessionSpeakerServiceImpl.isUserSpeakerInSession(user.getUserId(), sessionId);

        //Assertion
        assertFalse(isUserSpeakerInSession);
    }
}