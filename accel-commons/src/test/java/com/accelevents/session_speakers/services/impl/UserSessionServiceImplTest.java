package com.accelevents.session_speakers.services.impl;

import com.accelevents.auction.dto.UploadSessionAttendeeResponseContainer;
import com.accelevents.billing.chargebee.repositories.EventPlanConfigRepository;
import com.accelevents.common.dto.SearchAttendeeDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.domain.session_speakers.UserSession;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.TicketCheckInDto;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.ro.event.repository.ROEventLevelSettingsRepo;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.*;
import com.accelevents.services.dynamodb.analytics.ConsolidatedAnalyticsService;
import com.accelevents.services.elasticsearch.videoanalytics.AttendeeSessionVideoAnalyticsData;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.StaffRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.services.tray.io.tracking.TrayTrackingService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.services.*;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigInteger;
import java.util.*;

import static com.accelevents.domain.enums.EnumSessionFormat.MEET_UP;
import static com.accelevents.domain.enums.EnumSessionFormat.WORKSHOP;
import static com.accelevents.domain.enums.EnumUserSessionStatus.*;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.REGISTER_FAILED;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserSessionServiceImplTest
{

    private Event event;
    private User user;
    private Session session;

    private EventLevelSettings eventLevelSettings;
    private UploadSessionAttendeeResponseContainer container;
    private RegisterdHolderUsers registerdHolderUsers;
    private IdCountDto idCountDto;
    private EventPlanConfig eventPlanConfig;
    private PlanConfig planConfig;
    private AttendeeSession attendeeSession;
    private List<Long> resultIds = new ArrayList<>();
    private AttendeeSessionVideoAnalyticsData attendeeSessionVideoAnalyticsData;
    private Map<Long,Double> durationMap;
    private UserSession userSession;
    private AttendeeAnalyticsDTO attendeeAnalyticsDTO;
    private EventTicketsIdsDto eventTicketsIdsDto;
    private EventTickets eventTickets;

    private TicketingType ticketingType;
    List<UserSession> listOfRegisterUserSessions=new ArrayList<>();

    private UserSessionDTO userSessionDTO = new UserSessionDTO();
    private String device = "Chrome 8 from Computer";
    private Staff staff;
    private SearchAttendeeDto searchAttendeeDto;
    @Mock
    private UserRolesRepoService userRolesRepoService;

    @Mock
    private SessionSpeakerService sessionSpeakerService;

    @Mock
    private UserSessionRepoService userSessionRepoService;

    @Mock
    private SessionRepoService sessionRepoService;

    @Mock
    private StaffService staffService;
    @Mock
    private ROStaffService roStaffService;

    @Mock
    private ROStaffRoleService roStaffRoleService;

    @Mock
    private EventTicketsService eventTicketsService;

    @Mock
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;

    @Mock
    private SessionDetailsService sessionDetailsService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventPlanConfigRepository eventPlanConfigRepository;

    @Mock
    private SessionService sessionService;

    @Mock
    private SpeakerRepoService speakerRepoService;

    @Mock
    private UserService userService;

    @Mock
    private ROEventLevelSettingsRepo rOEventLevelSettingsRepo;

    @Mock
    private SpeakerService speakerService;
    @Mock
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    @Mock
    private SessionCheckInLogService sessionCheckInLogService;
    @Mock
    private TicketingCheckInService ticketingCheckInService;
    @Mock
    private CheckInAuditLogService checkInAuditLogService;
    @Mock
    private ConsolidatedAnalyticsService consolidatedAnalyticsService;

    @Mock
    private GamificationCacheStoreService redisCacheService;

    @InjectMocks
    @Spy
    private UserSessionServiceImpl userSessionServiceImpl;

    @Mock
    private VirtualEventService virtualEventService;

    @Mock
    private TrayTrackingService trayTrackingService;
    @Mock
    private StaffRepoService staffRepoService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private ROVirtualEventService roVirtualEventService;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        session = EventDataUtil.getSession();
        session.setTicketTypesThatCanBeRegistered("");

        registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setEventTicketId(1L);
        registerdHolderUsers.setFirstName("Milan");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setLastName("Dobariya");
        registerdHolderUsers.setEngagement("Mr.");
        registerdHolderUsers.setPhoneNumber(9558620170L);
        registerdHolderUsers.setRegistrationDate(new Date());

        planConfig = new PlanConfig();
        planConfig.setPlanName("Free");

        eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(planConfig);

        attendeeSession = new AttendeeSession(1L,"test",new Date(),new Date(),true);


        container = new UploadSessionAttendeeResponseContainer();

        attendeeSessionVideoAnalyticsData = new AttendeeSessionVideoAnalyticsData();
        attendeeSessionVideoAnalyticsData.setSessionId(1L);
        attendeeSessionVideoAnalyticsData.setLiveWatchTime(3.0);
        attendeeSessionVideoAnalyticsData.setRecordingWatchTime(3.0);

        durationMap = new HashMap<>();
        durationMap.put(1L,3.0);

        userSession = new UserSession();

        eventLevelSettings = new EventLevelSettings();
        eventLevelSettings.setSessionTimeConflicts(SessionTimeConflictsEnum.CANNOT_ALLOW_OVERRIDE);

        attendeeAnalyticsDTO = new AttendeeAnalyticsDTO();
        attendeeAnalyticsDTO.setEmailId("<EMAIL>");
        attendeeAnalyticsDTO.setUserId(1L);
        attendeeAnalyticsDTO.setFirstName("Milan");
        attendeeAnalyticsDTO.setLastName("Dobariya");
        attendeeAnalyticsDTO.setSessionId(1L);
        attendeeAnalyticsDTO.setStatus("Create");

        eventTicketsIdsDto = new EventTicketsIdsDto();

        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);

        TicketingOrder ticketingOrder = EventDataUtil.getTicketingOrder();

        eventTickets = new EventTickets();
        eventTickets.setId(1L);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setNumberoftickets(1L);
        eventTickets.setTicketingTypeId(ticketingType);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketingOrder(ticketingOrder);

        List<Integer> registeredUserIds = new ArrayList(Arrays.asList(1,2,3,4,5,6,7,8,9,10));
        registeredUserIds.forEach(e->{
            UserSession userSession1=new UserSession();
            userSession1.setUserId(Long.valueOf(e));
            userSession1.setEventTicketId(Long.valueOf(e));
            listOfRegisterUserSessions.add(userSession1);
        });

        userSessionDTO.setEventId(Long.valueOf("1"));
        userSessionDTO.setUserId(Long.valueOf("1"));
        userSessionDTO.setSessionId(Long.valueOf("1"));

    }

    private List<String> setAttribute(UserRole userRole,EnumSessionFormat enumSessionFormat)
    {
        session.setFormat(enumSessionFormat);
        return Collections.singletonList(userRole.name());
    }

    private UserSession getUserSession()
    {
        UserSession userSession = new UserSession();
        userSession.setId(1L);
        userSession.setUserId(user.getUserId());
        userSession.setSession(session);
        userSession.setEventId(event.getEventId());
        userSession.setEventTicketId(1L);

        try {

            Field f1 = userSession.getClass().getDeclaredField("user");
            f1.setAccessible(true);
            f1.set(userSession, user);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return userSession;
    }
    @Test
    void test_register_user_from_portal_with_super_admin_role()
    {
        List<String> userRole = setAttribute(UserRole.ROLE_SUPERADMIN, EnumSessionFormat.MAIN_STAGE);

        when(userSessionRepoService.save(any())).thenReturn(getUserSession());
        userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.TRUE, null,false, Boolean.FALSE,false);
        assertNotNull(userRole);

        assertNotNull(session);
        assertEquals(UserRole.ROLE_SUPERADMIN.name(),userRole.get(0));
        assertEquals(EnumSessionFormat.MAIN_STAGE, session.getFormat());
    }

    @Test
    void test_register_user_from_portal_with_staff()
    {
        List<String> userRole = setAttribute(UserRole.ROLE_USER, EnumSessionFormat.MAIN_STAGE);



        UserSession userSession = getUserSession();
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));
        session.setTicketTypesThatCanBeRegistered("1");
        List<Long> eventTicketIds = Collections.singletonList(Long.parseLong("1"));
        when(eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(anyLong(),anyLong())).thenReturn(eventTicketIds);
        userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.TRUE, null,false, Boolean.FALSE,false);

        assertNotNull(userRole);
        assertNotNull(session);
        assertEquals(UserRole.ROLE_USER.name(),userRole.get(0));
        assertEquals(EnumSessionFormat.MAIN_STAGE, session.getFormat());
    }
    @Test
    void testRegisterUserFromPortalWithStaffNotRegisterInSession()
    {
        List<String> userRole = setAttribute(UserRole.ROLE_USER, EnumSessionFormat.MAIN_STAGE);




        UserSession userSession = getUserSession();
        session.setTicketTypesThatCanBeRegistered("0");


        List<Long> eventTicketIds = Collections.singletonList(Long.parseLong("1"));

        when(speakerService.isSpeakerInEvent(event, user)).thenReturn(true);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.FALSE, null,true, Boolean.FALSE,false));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.STAFF_NOT_REGISTER_IN_SESSION.getErrorMessage(), exception.getMessage());
        assertNotNull(userRole);
        assertNotNull(session);
        assertEquals(UserRole.ROLE_USER.name(),userRole.get(0));
        assertEquals(EnumSessionFormat.MAIN_STAGE, session.getFormat());
    }

    @Test
    void test_register_user_from_portal_with_normal_meetUp()
    {
        List<String> userRole = setAttribute(UserRole.ROLE_USER, EnumSessionFormat.MEET_UP);
        UserSession userSession = getUserSession();


        when(userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));

        session.setId(1L);
        session.setTicketTypesThatCanBeRegistered("1");

        List<Long> eventTicketIds = Collections.singletonList(Long.parseLong("1"));
        when(eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(anyLong(),anyLong())).thenReturn(eventTicketIds);
        doReturn(List.of(1L)).when(eventTicketsRepoService).getAllEventTicketTypeIdsByEventUserANDNotCanceled(any(),any());
        doReturn(List.of(1L)).when(eventTicketsRepoService).getEventTicketTypeIdsByEventUserANDNotCanceled(any(),any());

        userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.TRUE, null,false, Boolean.FALSE,false);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);

        verify(userSessionRepoService, times(1)).save(userSessionArgumentCaptor.capture());
        assertNotNull(session);
        assertEquals(CHECK_IN_AVAILABLE,userSession.getCheckInStatus());
    }

    @Test
    void test_register_user_from_portal_with_super_speaker()
    {
        List<String> userRole = setAttribute(UserRole.ROLE_USER, EnumSessionFormat.MAIN_STAGE);


        when(sessionSpeakerService.isUserSpeakerInSession(anyLong(),anyLong())).thenReturn(Boolean.TRUE);
        userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.TRUE, null,false, Boolean.FALSE,false);

        assertNotNull(session);
        assertEquals(UserRole.ROLE_USER.name(),userRole.get(0));
        assertEquals(EnumSessionFormat.MAIN_STAGE, session.getFormat());
    }

    @Test
    void test_register_user_from_portal_with_event_without_session()
    {
        List<String> userRole = setAttribute(UserRole.ROLE_USER, EnumSessionFormat.MAIN_STAGE);
        List<Long> eventTicketIds = Collections.singletonList(Long.parseLong("1"));
        BigInteger registeredCount = new BigInteger(String.valueOf(1));

        when(eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(anyLong(),anyLong())).thenReturn(eventTicketIds);
        when(eventTicketsRepoService.getEventTicketByEventTicketIdAndNotCanceled(anyLong())).thenReturn(eventTickets);

        userSessionDTO.setEventId(Long.valueOf("1"));
        userSessionDTO.setUserId(Long.valueOf("1"));
        userSessionDTO.setSessionId(Long.valueOf("1"));
        session.setTicketTypesThatCanBeRegistered("1");

        UserSession userSession = getUserSession();
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));
        userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.TRUE, null,false, Boolean.FALSE,false);
        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService, times(1)).save(userSessionArgumentCaptor.capture());
        assertNotNull(session);
    }

    @Test
    void test_register_user_from_portal_capacity_Success()
    {
        session.setCapacity(2);
        when(virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId())).thenReturn(Boolean.TRUE);
        when(userSessionRepoService.countBySessionIdAndTicketIdIsNotNullAndEventId(anyLong(), anyLong())).thenReturn(1);
        when(userService.findByUserId(user.getUserId())).thenReturn(user);
        when(roStaffService.getUserRole(user,event,null)).thenReturn(UserRole.ROLE_USER);
        when(sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId())).thenReturn(Collections.singletonList(2L));
        when(rOEventLevelSettingsRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(eventLevelSettings));

        userSessionServiceImpl.validateRegisterCapacityAndConcurrentSessionRegistrationRules(session,event, 1L);

    }

    @Test
    void test_register_user_from_portal_capacity_exceeded()
    {
        session.setCapacity(1);
        when(virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId())).thenReturn(Boolean.TRUE);
        when(userSessionRepoService.countBySessionIdAndTicketIdIsNotNullAndEventId(anyLong(),anyLong())).thenReturn(1);
        when(userService.findByUserId(user.getUserId())).thenReturn(user);
        when(rOEventLevelSettingsRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(eventLevelSettings));
        when(roStaffService.getUserRole(user,event,null)).thenReturn(UserRole.ROLE_USER);
        when(sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId())).thenReturn(Collections.singletonList(2L));
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.validateRegisterCapacityAndConcurrentSessionRegistrationRules(session,event, 1L));

        assertEquals(NotAcceptableException.SessionExceptionMsg.SESSION_BOOKMARK_CAPACITY_EXCEEDED.getErrorMessage(), exception.getMessage());

    }


    @Test
    void testValidateTimeAccessElapsed(){
        Date currentDate = DateUtils.getCurrentDate();
        Session session = prepareSession(currentDate, 295);

        SessionDetails sessionDetails = new SessionDetails();
        sessionDetails.setAllowedMinutesToJoinLate(5);
        VirtualEventSettings virtualEventSettings = new VirtualEventSettings();
        virtualEventSettings.setAllowLateJoin(false);
        when(virtualEventSettingsRepoService.isAllowLateJoin(anyLong())).thenReturn(false);
        when(sessionDetailsService.getAllowedMinutesToJoinLate(any())).thenReturn(5);


        doReturn(false).when(userSessionServiceImpl).isEventStaffOrSpeaker(any(), any(), any());

        userSessionServiceImpl.validateTimeAccessElapsed(session,event,new User(),true, false, false);
    }
    private Session prepareSession(Date currentDate, int i) {
        Session session = new Session();
        session.setId(1L);
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        session.setStartTime(DateUtils.getAddSeconds(currentDate, -i));
        return session;
    }
    @Test
    void testValidateTimeAccessElapsed_throwError() {

        Date currentDate = DateUtils.getCurrentDate();
        Session session = prepareSession(currentDate, 301);

        SessionDetails sessionDetails = new SessionDetails();
        sessionDetails.setAllowedMinutesToJoinLate(5);
        VirtualEventSettings virtualEventSettings = new VirtualEventSettings();
        virtualEventSettings.setAllowLateJoin(false);
        when(virtualEventSettingsRepoService.isAllowLateJoin(anyLong())).thenReturn(false);
        when(sessionDetailsService.getAllowedMinutesToJoinLate(any())).thenReturn(5);
        doReturn(false).when(userSessionServiceImpl).isEventStaffOrSpeaker(any(), any(), any());

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.validateTimeAccessElapsed(session, event, new User(), true, false, false));

        assertEquals(NotAcceptableException.SessionExceptionMsg.TIME_ACCESS_ELAPSED_LIVE.getErrorMessage(), exception.getMessage());
    }

    //TODO: Junit5 review test
    /*@Test
    void test_register_user_from_portal_with_exceed_session_capacity()
    {
        List<String> userRole = setAttribute(UserRole.ROLE_USER, EnumSessionFormat.MAIN_STAGE);
        List<Long> eventTicketIds = Collections.singletonList(Long.parseLong("1"));
        BigInteger registeredCount = new BigInteger(String.valueOf(1));

        when(eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(anyLong(),anyLong())).thenReturn(eventTicketIds);
        when(eventTicketsRepoService.getEventTicketByEventTicketIdAndNotCanceled(anyLong())).thenReturn(eventTickets);

        userSessionDTO.setEventId(Long.valueOf("1"));
        userSessionDTO.setUserId(Long.valueOf("1"));
        userSessionDTO.setSessionId(Long.valueOf("1"));

        List<TicketTypeFormat> ticketTypeFormats=new ArrayList<>();
        List<Object[]> ticketingTypeAttribute=new ArrayList<>();
        Object[] obj={ticketingType.getId(),22};
        ticketingTypeAttribute.add(obj);
        BigInteger bigInteger = new BigInteger("20");
        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        String s= String.valueOf(ticketingType.getId());
        session.setTicketTypesThatCanBeRegistered(s);
        userSession.setId(1L);
        userSession.setUserId(user.getUserId());
        userSession.setSessionId(session.getId());
        when( sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId())).thenReturn(Collections.singletonList(1L));
        when(userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatus(session.getId(),event.getEventId())).thenReturn(10L);
        when( staffService.findAllStaffUserIdsByEvent(event)).thenReturn(Collections.singletonList(2L));

        when(eventTicketsService.getTicketTypeFormatByEventTicketId(eventTickets.getId())).thenReturn(TicketTypeFormat.VIRTUAL);
        when(eventTicketsService.findTicketingTypeById(eventTickets.getId())).thenReturn(ticketingTypeAttribute);
        when(userSessionRepoService.countByEventTicketId(eventTickets.getId())).thenReturn(bigInteger);
        when(userSessionServiceImpl.tryAndRegisterUserWithDifferentTicketType(user, session, event,Boolean.TRUE, false)).thenReturn(userSession);

        doNothing().when(userSessionServiceImpl).validateBookmarkCapacity(any(),any(),anyLong());
        session.setCapacity(1);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.TRUE, null));

        assertEquals(EXCEED_SESSION_CAPACITY.getDeveloperMessage(), exception.getMessage());
    }*/

    @Test
    void test_register_user_from_portal_with_exception_registration_failed()
    {
        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);

        session.setTicketTypesThatCanBeRegistered("1");
        session.setFormat(EnumSessionFormat.WORKSHOP);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.FALSE, null,false, Boolean.FALSE,false));

        assertEquals(REGISTER_FAILED.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    void test_register_user_from_portal_with_exception()
    {
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setMaxSessionRegisterUser(1);

        EventTickets eventTickets = new EventTickets();
        eventTickets.setTicketingTypeId(ticketingType);

        List<Long> eventTicketIds = Collections.singletonList(Long.parseLong("1"));


        userSessionDTO.setEventId(Long.valueOf("1"));
        userSessionDTO.setUserId(Long.valueOf("1"));
        userSessionDTO.setSessionId(Long.valueOf("1"));
        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);


        session.setTicketTypesThatCanBeRegistered("1");

        assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,Boolean.TRUE,event, Boolean.FALSE, Boolean.FALSE, null,false,Boolean.FALSE,false));
    }

    @Test
    void test_unRegisterUser_eventTicketIdGreaterThanZero(){

        UserSession userSession = getUserSession();
        userSession.setSessionStatus(REGISTERED);

        when(userSessionRepoService.findByEventTicketIdAndSessionIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));

        doNothing().when(userSessionRepoService).delete(any());

        //Execution
        userSessionServiceImpl.unRegisterUser(userSessionDTO,1L,event, false,false, user);
        assertNotNull(userSession);
    }

    @Test
    void test_unRegisterUser_eventTicketIdLessThanZero(){

        UserSession userSession = getUserSession();
        userSession.setSessionStatus(REGISTERED);
        userSessionDTO.setEventId(1L);
        userSessionDTO.setSessionId(1L);
        userSessionDTO.setUserId(1L);


        when(userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));
        doNothing().when(userSessionRepoService).delete(any());

        //Execution
        userSessionServiceImpl.unRegisterUser(userSessionDTO,-1L,event, true,false, user);
        assertNotNull(userSession);
    }

    @Test
    void test_unRegisterUser_userSessionListEmpty(){

        UserSession userSession = null;

        when(userSessionRepoService.findByEventTicketIdAndSessionIdAndEventId(anyLong(), anyLong(), anyLong())).thenReturn(Collections.emptyList());

        //Execution
        userSessionServiceImpl.unRegisterUser(userSessionDTO,1L,event,false,false, user);
        assertNull(userSession);
    }

    @Test
    void test_unRegisterUserWithoutEventTicket_userSessionListEmpty(){

        UserSession userSession = null;

        when(userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Collections.emptyList());


        //Execution
        userSessionServiceImpl.unRegisterUserWithoutEventTicket(session.getId(),1L,event);
        assertNull(userSession);
    }

    @Test
    void test_unRegisterUserWithoutEventTicket_userSessionList(){

        UserSession userSession = getUserSession();

        when(userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));
        doNothing().when(userSessionRepoService).deleteAll(anyList());

        //Execution
        userSessionServiceImpl.unRegisterUserWithoutEventTicket(session.getId(),1L,event);
        assertNotNull(userSession);
    }



    @Test
    void test_validateMatchingTicketType(){

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});

        session.setTicketTypesThatCanBeRegistered("2,3");
        when( eventTicketsService.findTicketingTypeById(anyLong())).thenReturn(ticketingTypeAttribute);
        when( userSessionRepoService.countByEventTicketId(anyLong())).thenReturn(BigInteger.valueOf(1));

        //Execution
        userSessionServiceImpl.validateMatchingTicketType(user,session,1L,event);
    }

    @Test
    void test_validateMatchingTicketType_ticketTypesThatCanBeRegistered_NULL(){

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});


        //setup

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.validateMatchingTicketType(user,session,1L,event));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.TICKET_TYPE_NOT_MATCHED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_validateMatchingTicketType_eventTicketId_NOT_IN_ticketTypesThatCanBeRegistered(){

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,1});
        ticketingTypeAttribute.add(new Object[]{1});

        session.setTicketTypesThatCanBeRegistered("3,4");
        when( eventTicketsService.findTicketingTypeById(anyLong())).thenReturn(ticketingTypeAttribute);

        //setup

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.validateMatchingTicketType(user,session,1L,event));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.TICKET_TYPE_NOT_MATCHED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_validateMatchingTicketType_maxRegisterUserReached(){

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,1});
        ticketingTypeAttribute.add(new Object[]{1});

        session.setTicketTypesThatCanBeRegistered("2,3");
        when( eventTicketsService.findTicketingTypeById(anyLong())).thenReturn(ticketingTypeAttribute);
        when( userSessionRepoService.countByEventTicketId(anyLong())).thenReturn(BigInteger.valueOf(1));

        //setup

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.validateMatchingTicketType(user,session,1L,event));

        assertEquals("Your registration type only permits registering for 1 sessions. If you would like to register for this session, please un-register from a different session first.",
                exception.getMessage());
    }

    @Test
    void test_registerUser(){

        doNothing().when(userSessionServiceImpl).validateMatchingTicketType(any(),any(),anyLong(),any());

        //Execution
        userSessionServiceImpl.registerUser(1L,user,session,event);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());

        UserSession userSession1 = userSessionArgumentCaptor.getValue();
        assertEquals(userSession1.getEventId(),(Long) event.getEventId());
        assertEquals(userSession1.getSessionId(),(Long) session.getId());
        assertEquals(userSession1.getUserId(),user.getUserId());

    }

    @Test
    void test_registerUser_loginUserNull(){

        doNothing().when(userSessionServiceImpl).validateMatchingTicketType(any(),any(),anyLong(),any());
        when( eventTicketsService.findUserIdByEventTicketId(anyLong())).thenReturn(1L);

        //Execution
        userSessionServiceImpl.registerUser(1L,null,session,event);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());

        UserSession userSession1 = userSessionArgumentCaptor.getValue();
        assertEquals(userSession1.getEventId(),(Long) event.getEventId());
        assertEquals(userSession1.getSessionId(),(Long) session.getId());
        assertEquals(userSession1.getUserId(),(Long) 1L);
    }

    @Test
    void test_isNotMeetUpSession_meetUpSession_true(){

        session.setFormat(EnumSessionFormat.MEET_UP);
        //Execution
        boolean notMeetUpSession = userSessionServiceImpl.isNotMeetUpSession(session);

        assertFalse(notMeetUpSession);
    }

    @Test
    void test_isNotMeetUpSession_meetUpSession_false(){

        //Execution
        boolean notMeetUpSession = userSessionServiceImpl.isNotMeetUpSession(session);

        assertTrue(notMeetUpSession);
    }

    @Test
    void test_isEventStaff_true(){

        when(sessionSpeakerService.isUserSpeakerInSession(anyLong(), anyLong())).thenReturn(true);

        //Execution
        boolean notMeetUpSession = userSessionServiceImpl.isEventStaffOrSpeaker(user,session,event);

        assertTrue(notMeetUpSession);

    }

    @Test
    void test_isEventStaff_false(){

        when(sessionSpeakerService.isUserSpeakerInSession(anyLong(), anyLong())).thenReturn(false);
        when(roStaffRoleService.getUserRoleByEvent(user, event)).thenReturn(UserRole.ROLE_LEAD_RETRIEVER);
        //Execution
        boolean notMeetUpSession = userSessionServiceImpl.isEventStaffOrSpeaker(user,session,event);

        assertFalse(notMeetUpSession);

    }

    @Test
    void test_registerUserWithoutTicket_notMeetUpSession(){

        List<UserSession> sessionIds = new ArrayList<>();
        List<TicketTypeFormat> ticketTypeFormats=new ArrayList<>();

        when(sessionSpeakerService.isUserSpeakerInSession(anyLong(), anyLong())).thenReturn(true);

        when(userSessionServiceImpl.isNotMeetUpSession(session)).thenReturn(true);
        when(userSessionServiceImpl.isEventStaffOrSpeaker(user,session,event)).thenReturn(true);

        when( userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(sessionIds);


        //Execution
        userSessionServiceImpl.registerUserWithoutTicket(user,session,event);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());

        UserSession userSession1 = userSessionArgumentCaptor.getValue();
        assertEquals(userSession1.getEventId(),(Long) event.getEventId());
        assertEquals(userSession1.getSessionId(),(Long) session.getId());
        assertEquals(userSession1.getUserId(),(Long) 1L);
    }

    //TODO: Junit5 review test
    /*@Test
    void test_registerUserWithoutTicket_meetUpSession(){

        //setup
        when(sessionSpeakerService.isUserSpeakerInSession(anyLong(), anyLong())).thenReturn(true);

        when(userSessionServiceImpl.isNotMeetUpSession(session)).thenReturn(false);
        when(userSessionServiceImpl.isEventStaffOrSpeaker(user,session,event)).thenReturn(false);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserWithoutTicket(user,session,event));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.USER_NOT_ALLOWED_TO_REGITER_WITHOUT_TICKET.getDeveloperMessage(), exception.getMessage());
    }*/

    @Test
    void test_registerUserWithoutTicket_userSessionListNotEmpty(){

        UserSession userSession = getUserSession();
        List<UserSession> sessionIds = new ArrayList<>();
        sessionIds.add(userSession);

        when(sessionSpeakerService.isUserSpeakerInSession(anyLong(), anyLong())).thenReturn(true);

        when(userSessionServiceImpl.isNotMeetUpSession(session)).thenReturn(true);
        when(userSessionServiceImpl.isEventStaffOrSpeaker(user,session,event)).thenReturn(true);

        when( userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(sessionIds);


        //Execution
        UserSession userSession1 = userSessionServiceImpl.registerUserWithoutTicket(user, session, event);

        assertEquals(userSession1,userSession);
    }

    @Test
    void test_validateMatchingTicketTypeForBulkUpload_ticketTypesThatCanBeRegistered_null(){

        boolean b = userSessionServiceImpl.validateMatchingTicketTypeForBulkUpload(session, 1L, container, "Milan", "Dobariya", "EN");
        assertFalse(b);

    }

    @Test
    void test_validateMatchingTicketTypeForBulkUpload_optionalEventTickets_null(){

        session.setTicketTypesThatCanBeRegistered("1,2");

        when(eventTicketsService.findEventTicketById(anyLong())).thenReturn(Optional.ofNullable(null));

        boolean uploaded = userSessionServiceImpl.validateMatchingTicketTypeForBulkUpload(session, 1L, container, "Milan", "Dobariya", "EN");
        assertEquals(false,uploaded);

    }

    @Test
    void test_validateMatchingTicketTypeForBulkUpload_maxRegister(){

        session.setTicketTypesThatCanBeRegistered("1,2");
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setMaxSessionRegisterUser(1);

        EventTickets eventTickets = new EventTickets();
        eventTickets.setTicketingTypeId(ticketingType);

        when(eventTicketsService.findEventTicketById(anyLong())).thenReturn(Optional.ofNullable(eventTickets));
        when(userSessionRepoService.countByEventTicketId(anyLong())).thenReturn(new BigInteger(String.valueOf(1)));

        boolean isUploaded = userSessionServiceImpl.validateMatchingTicketTypeForBulkUpload(session, 1L, container, "Milan", "Dobariya", "EN");
        assertEquals(false,isUploaded);

    }

    @Test
    void test_validateMatchingTicketTypeForBulkUpload_eventTicketIdNotInRegisteredTicketType(){

        session.setTicketTypesThatCanBeRegistered("2,3");
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setMaxSessionRegisterUser(1);

        EventTickets eventTickets = new EventTickets();
        eventTickets.setTicketingTypeId(ticketingType);

        when(eventTicketsService.findEventTicketById(anyLong())).thenReturn(Optional.ofNullable(eventTickets));


        boolean b = userSessionServiceImpl.validateMatchingTicketTypeForBulkUpload(session, 1L, container, "Milan", "Dobariya", "EN");
        assertEquals(false,b);

    }

    @Test
    void test_validateMatchingTicketTypeForBulkUpload(){

        session.setTicketTypesThatCanBeRegistered("1,2");
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setMaxSessionRegisterUser(3);

        EventTickets eventTickets = new EventTickets();
        eventTickets.setTicketingTypeId(ticketingType);

        when(eventTicketsService.findEventTicketById(anyLong())).thenReturn(Optional.ofNullable(eventTickets));
        when(userSessionRepoService.countByEventTicketId(anyLong())).thenReturn(new BigInteger(String.valueOf(1)));

        boolean b = userSessionServiceImpl.validateMatchingTicketTypeForBulkUpload(session, 1L, container, "Milan", "Dobariya", "EN");
        assertEquals(true,b);

    }

    @Test
    void test_unRegisterSpeakerUser_true(){

        when(sessionRepoService.getSessionById(any(),any())).thenReturn(session);
        doNothing().when(userSessionServiceImpl).unRegisterUserWithoutEventTicket(anyLong(), anyLong(), any());


        //Execution
        userSessionServiceImpl.unRegisterSpeakerUser(1L, 1L, event);
        assertEquals(1L,session.getId());

    }

    @Test
    void test_unRegisterSpeakerUser_false(){

        when(sessionRepoService.getSessionById(any(),any())).thenReturn(session);
        doNothing().when(userSessionServiceImpl).unRegisterUserWithoutEventTicket(anyLong(), anyLong(), any());


        //Execution
        userSessionServiceImpl.unRegisterSpeakerUser(1L, 1L, event);
        assertEquals(1L,session.getId());
    }

    @Test
    void test_validateCapacity(){

        List<Long> speakerUserIdsBySessionId = new ArrayList(Arrays.asList(1L,2L));
        List<Long> registeredUserIds = new ArrayList(Arrays.asList(6L,7L,8L,9L,10L));
        List<Long> allStaffAndAdminUserIds = new ArrayList(Arrays.asList(3L,4L,5L));

        when( sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId())).thenReturn(speakerUserIdsBySessionId);
        when( userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatus(session.getId(), event.getEventId())).thenReturn((long) registeredUserIds.size());
        doReturn(false).when(userSessionServiceImpl).isWorkshopSession(any());
        when(staffService.findAllStaffUserIdsByEvent(event)).thenReturn(allStaffAndAdminUserIds);
        when(eventTicketsService.getTicketTypeFormatByEventTicketId(eventTickets.getId())).thenReturn(TicketTypeFormat.VIRTUAL);

        //Execution
        userSessionServiceImpl.validateCapacity(session,event,eventTickets.getId(),false);

        assertEquals(new ArrayList(Arrays.asList(6L,7L,8L,9L,10L)),registeredUserIds);
    }

    @Test
    void test_validateCapacity_exceedSessionCapacity(){

        //setup
        List<TicketTypeFormat> ticketTypeFormats=new ArrayList<>();
        List<Long> registeredUserIds = new ArrayList(Arrays.asList(1L,2L,3L,4L,5L,6L,7L,8L,9L,10L));
        List<Long> speakerUserIdsBySessionId = new ArrayList(Arrays.asList(1L,2L));

        List<Long> allStaffAndAdminUserIds = new ArrayList(Arrays.asList(3L,4L,5L));
        session.setFormat(EnumSessionFormat.MEET_UP);
        session.setCapacity(4);

        when( sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId())).thenReturn(speakerUserIdsBySessionId);
        when(userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatus(session.getId(),event.getEventId())).thenReturn((long) registeredUserIds.size());

        doReturn(false).when(userSessionServiceImpl).isWorkshopSession(any());


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.validateCapacity(session,event,eventTickets.getId(),false));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.EXCEED_SESSION_CAPACITY.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_validateCapacity_exceedWorkshopSessionCapacity(){

        //setup

        List<Long> speakerUserIdsBySessionId = new ArrayList(Arrays.asList(1L,2L));
        List<Long> registeredUserIds = new ArrayList();
        for (Long i=1L;i<=300L;i++){
            registeredUserIds.add(i);
        }
        List<TicketTypeFormat> ticketTypeFormats=new ArrayList<>();
        session.setCapacity(4);

        for(Long i = 1L; i<=300L; i++){
            UserSession userSession1=new UserSession();
            userSession1.setUserId(user.getUserId());
            userSession1.setEventTicketId(eventTickets.getId());
            listOfRegisterUserSessions.add(userSession1);
        }

        when( sessionSpeakerService.findSpeakerUserIdBySessionId(session.getId())).thenReturn(speakerUserIdsBySessionId);
        doReturn(true).when(userSessionServiceImpl).isWorkshopSession(any());
        when(userSessionRepoService.findUserSessionCountByEventIdAndSessionIdAndCheckInStatus(session.getId(),event.getEventId())).thenReturn((long) listOfRegisterUserSessions.size());


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.validateCapacity(session,event,eventTickets.getId(),false));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.EXCEED_WORKSHOP_SESSION_CAPACITY.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_tryAndRegisterUserWithDifferentTicketType(){

        //setup
        UserSession userSession = getUserSession();
        List<UserSession> sessionIds = new ArrayList<>();
        sessionIds.add(userSession);

        when( userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(sessionIds);

        //Execution
        UserSession userSession1 = userSessionServiceImpl.tryAndRegisterUserWithDifferentTicketType(user, session, event,true, false,false, false, false);

        assertEquals(userSession1,userSession);

    }

    @Test
    void test_tryAndRegisterUserWithDifferentTicketType_all(){

        //setup
        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        List<TicketTypeFormat> ticketTypeFormats=new ArrayList<>();
        UserSession userSession = getUserSession();
        List<UserSession> sessionIds = new ArrayList<>();
        List<Long> eventTicketIds = new ArrayList(Arrays.asList(1L,2L));

        when( userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(sessionIds);
        when(eventTicketsService.eventTicketIdsByHolderUserIdAndEventId(anyLong(), anyLong())).thenReturn(eventTicketIds);
        when(eventTicketsRepoService.getEventTicketByEventTicketIdAndNotCanceled(anyLong())).thenReturn(eventTickets);
        doReturn(userSession).when(userSessionServiceImpl).registerUser(anyLong(),any(),any(),any());


        //Execution
        UserSession userSession1 = userSessionServiceImpl.tryAndRegisterUserWithDifferentTicketType(user, session, event,true, false,false, false,false);

        assertEquals(userSession1,userSession);

    }

    @Test
    void test_findSessionAttendedUserInfo(){

        when(userSessionRepoService.findRegisteredUserBySessionId(anyLong())).thenReturn(Collections.singletonList(registerdHolderUsers));
        when(sessionRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(session));
        when(sessionDetailsService.getSessionVideoDurationBySession(any())).thenReturn(2.0);



        //Execution
        List<RegisterdHolderUsers> sessionAttendedUserInfo = userSessionServiceImpl.findSessionAttendedUserInfo(1L);

        sessionAttendedUserInfo.forEach(sessionAttendedUser ->{
            assertEquals(registerdHolderUsers,sessionAttendedUser);
        });
    }

    @Test
    void test_findSessionAttendedUserInfo_session_notfound() {

        //setup
        when(userSessionRepoService.findRegisteredUserBySessionId(anyLong())).thenReturn(Collections.singletonList(registerdHolderUsers));
        when(sessionRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(null));

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> userSessionServiceImpl.findSessionAttendedUserInfo(1L));

        assertEquals(NotFoundException.SessionNotFound.SESSION_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_countSessionAttendeeBySessionIdIn_empty_sessionList(){

        //Execution
        List<IdCountDto> idCountDtos = userSessionServiceImpl.countSessionAttendeeBySessionIdIn(Collections.emptyList());

        assertEquals(Collections.emptyList(),idCountDtos);
    }

    @Test
    void test_countSessionAttendeeBySessionIdIn(){

        idCountDto = new IdCountDto(1L,2L);

        when(userSessionRepoService.countSessionAttendeeBySessionIdIn(anyList())).thenReturn(Collections.singletonList(idCountDto));

        //Execution
        List<IdCountDto> idCountDtos = userSessionServiceImpl.countSessionAttendeeBySessionIdIn(Collections.singletonList(1L));

        assertEquals(Collections.singletonList(idCountDto),idCountDtos);
    }

    @Test
    void test_countSessionAttendeeBySessionId(){

        when(userSessionRepoService.countSessionAttendeeBySessionId(anyLong())).thenReturn(1L);

        //Execution
        long l = userSessionServiceImpl.countSessionAttendeeBySessionId(1L);

        assertEquals(1L,l);
    }

    @Test
    void test_findRegisteredUserInfo(){

        when(userSessionRepoService.findRegisteredUserBySessionId(anyLong())).thenReturn(Collections.singletonList(registerdHolderUsers));

        //Execution
        List<RegisterdHolderUsers> registeredUserInfo = userSessionServiceImpl.findRegisteredUserInfo(1L);

        registeredUserInfo.forEach(registerdHolderUsersResult ->{
            assertEquals(registerdHolderUsers,registerdHolderUsersResult);
        });
    }

    @Test
    void test_findAttendeeSessionsByUserId_free_pan(){

        //Setup
        when(eventPlanConfigRepository.findByEventId(anyLong())).thenReturn(Optional.ofNullable(eventPlanConfig));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.findAttendeeSessionsByUserId(event,user.getUserId(), true, null));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.NOT_AUTHORIZED_TO_ACCESS_SESSION_USER_ANALYTICS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_findAttendeeSessionsByUserId_starter_pan(){

        //Setup
        planConfig.setPlanName("Starter");
        eventPlanConfig.setPlanConfig(planConfig);

        when(eventPlanConfigRepository.findByEventId(anyLong())).thenReturn(Optional.ofNullable(eventPlanConfig));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.findAttendeeSessionsByUserId(event,user.getUserId(), true, null));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.NOT_AUTHORIZED_TO_ACCESS_SESSION_USER_ANALYTICS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_findAttendeeSessionsByUserId(){

        //Setup
        planConfig.setPlanName("Professional");
        eventPlanConfig.setPlanConfig(planConfig);

        when(eventPlanConfigRepository.findByEventId(anyLong())).thenReturn(Optional.ofNullable(eventPlanConfig));
        when(userSessionRepoService.findAttendeeSessionsByUserId(anyLong(), anyLong(), eq(null))).thenReturn(Collections.singletonList(attendeeSession));

        //Execution
        List<AttendeeSession> attendeeSessionsByUserId = userSessionServiceImpl.findAttendeeSessionsByUserId(event, user.getUserId(), true, null);

        attendeeSessionsByUserId.forEach(attendeeSessionResult ->{
            assertEquals(attendeeSession,attendeeSessionResult);
        });

    }

    @Test
    void test_isUserAvailableStateAndTicketTypesIn(){

        when(userSessionRepoService.isUserAvailableStateForTicketTypesIn(anyLong(), anyLong(), anyLong(), any(), anyList())).thenReturn(Boolean.TRUE);

        boolean userAvailableStateAndTicketTypesIn = userSessionServiceImpl.isUserAvailableStateAndTicketTypesIn(anyLong(), anyLong(), anyLong(), anyList());

        assertEquals(Boolean.TRUE,userAvailableStateAndTicketTypesIn);
    }

    @Test
    void test_isUserAvailableState(){

        when(userSessionRepoService.isUserAvailableState(anyLong(),anyLong(),anyLong(),any())).thenReturn(Boolean.TRUE);

        boolean userAvailableStateAndTicketTypesIn = userSessionServiceImpl.isUserAvailableState(anyLong(), anyLong(), anyLong());

        assertEquals(Boolean.TRUE,userAvailableStateAndTicketTypesIn);
    }

    @Test
    void test_getCheckInUserCountBySession(){

        when(userSessionRepoService.getCheckInUserCountBySession(anyLong())).thenReturn(1);

        //Execution
        int checkInUserCountBySession = userSessionServiceImpl.getCheckInUserCountBySession(anyLong(), any());

        assertEquals(1,checkInUserCountBySession);
    }

    @Test
    void test_findActiveUsersByTicketTypesInAndNotInUserId(){

        //Setup
        resultIds.add(1L);

        when(userSessionRepoService.findByEventIdAndSessionIdAndSateAndNotUserIdAndTicketTypesIn(anyLong(), anyLong(), any(), anyList(), anyList())).thenReturn(resultIds);

        //Execution
        List activeUsersByTicketTypesInAndNotInUserId = userSessionServiceImpl.findActiveUsersByTicketTypesInAndNotInUserId(anyLong(), anyLong(), anyList(), anyList());

        assertEquals(resultIds,activeUsersByTicketTypesInAndNotInUserId);

    }

    @Test
    void test_updateUserSessionState(){

        doNothing().when(userSessionRepoService).updateUserState(any(),anyLong(),anyLong(),anyList());

        //Execution
        userSessionServiceImpl.updateUserSessionState(anyLong(),anyLong(),anyList(),any());

        assertNull(null);
    }

    @Test
    void test_updateUserSessionCheckInState(){

        doNothing().when(userSessionRepoService).updateUserCheckInState(any(),anyLong(),anyLong(),anyList());

        //Execution
        userSessionServiceImpl.updateUserSessionCheckInState(anyLong(),anyLong(),anyList(),any());

        assertNull(null);
    }

    @Test
    void test_getSessionStatsForIdsIn_empty_sessionList(){

        //Execution
        List<IdCountDto> idCountDtos = userSessionServiceImpl.getSessionStatsForIdsIn(Collections.emptyList());

        assertEquals(Collections.emptyList(),idCountDtos);
    }

    @Test
    void test_getSessionStatsForIdsIn(){

        idCountDto = new IdCountDto(1L,2L);

        when(userSessionRepoService.registerCountBySessionIdsAndSessionStatus(anyList(),any())).thenReturn(Collections.singletonList(idCountDto));

        //Execution
        List<IdCountDto> idCountDtos = userSessionServiceImpl.getSessionStatsForIdsIn(Collections.singletonList(1L));

        assertEquals(Collections.singletonList(idCountDto),idCountDtos);
    }

    @Test
    void test_countSessionAttendeeByEventAndUserIds(){

        idCountDto = new IdCountDto(1L,2L);

        when(userSessionRepoService.countSessionAttendeeByEventAndUserIds(anyList(),anyList())).thenReturn(Collections.singletonList(idCountDto));

        //Execution
        List<IdCountDto> idCountDtos = userSessionServiceImpl.countSessionAttendeeByEventAndUserIds(Collections.singletonList(1L),Collections.singletonList(1L));

        assertEquals(Collections.singletonList(idCountDto),idCountDtos);

    }

    @Test
    void test_countSessionAttendeeByEventAndUserIds_empty_eventIdList_userIdList(){

        //Execution
        List<IdCountDto> idCountDtos = userSessionServiceImpl.countSessionAttendeeByEventAndUserIds(Collections.emptyList(),Collections.emptyList());

        assertEquals(Collections.emptyList(),idCountDtos);

    }

    @Test
    void test_findRegisteredEventTicketIdByUserAndSessionId_sessionIds_empty(){

        //Execution
        Map<Long, List<Long>> registeredEventTicketIdByUserAndSessionId = userSessionServiceImpl.findRegisteredEventTicketIdByUserAndSessionId(1L, resultIds);

        assertEquals(Collections.emptyMap(),registeredEventTicketIdByUserAndSessionId);
    }

    @Test
    void test_findRegisteredEventTicketIdByUserAndSessionId(){

        userSession.setEventId(Long.valueOf("1"));
        userSession.setUserId(Long.valueOf("1"));
        userSession.setSessionId(Long.valueOf("1"));

        resultIds.add(1L);

        userSession.setEventTicketId(1L);

        when(userSessionRepoService.findRegisteredEventTicketIdByUserAndSessionId(anyLong(),anyList())).thenReturn(Collections.singletonList(userSession));

        //Execution
        Map<Long, List<Long>> registeredEventTicketIdByUserAndSessionId = userSessionServiceImpl.findRegisteredEventTicketIdByUserAndSessionId(1L, resultIds);

        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(1L,resultIds);

        assertEquals(expectedResult,registeredEventTicketIdByUserAndSessionId);
    }

    @Test
    void test_updateStatus(){

        userSession.setEventId(Long.valueOf("1"));
        userSession.setUserId(Long.valueOf("1"));
        userSession.setSessionId(Long.valueOf("1"));

        when(userSessionRepoService.findBySessionIdAndUserIdAndEventId(anyLong(), anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));

        //Execution
        userSessionServiceImpl.updateStatus(1L,1L,"CHECK_IN_AVAILABLE",event,"India", null);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);

        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        assertNotNull(session);
        assertEquals(CHECK_IN_AVAILABLE,userSession.getCheckInStatus());
    }

    @Test
    void test_countRegisteredBySessionId(){

        when(userSessionRepoService.registerCountBySessionId(anyLong(),any())).thenReturn(BigInteger.valueOf(1));

        //Execution
        Integer integer = userSessionServiceImpl.countRegisteredBySessionId(anyLong());

        assertEquals(Integer.valueOf(1),integer);

    }

    @Test
    void test_countRegisteredBySessionId_count_null(){

        when(userSessionRepoService.registerCountBySessionId(anyLong(),any())).thenReturn(null);

        //Execution
        Integer integer = userSessionServiceImpl.countRegisteredBySessionId(anyLong());

        assertEquals(Integer.valueOf(0),integer);

    }

    @Test
    void test_getEventTicketIdsByEventIdAndUserIdAndSessionId(){

        //Setup
        resultIds.add(1L);

        when(userSessionRepoService.getEventTicketIdsByEventIdAndUserIdAndSessionId(anyLong(),anyLong(),anyLong())).thenReturn(resultIds);

        //Execution
        List<Long> eventTicketIdsByEventIdAndUserIdAndSessionId = userSessionServiceImpl.getEventTicketIdsByEventIdAndUserIdAndSessionId(anyLong(), anyLong(), anyLong());

        assertEquals(resultIds,eventTicketIdsByEventIdAndUserIdAndSessionId);
    }

    @Test
    void test_getEventTicketIdsByEventIdAndUserIdAndSessionId_empty_result(){

        //Setup

        when(userSessionRepoService.getEventTicketIdsByEventIdAndUserIdAndSessionId(anyLong(),anyLong(),anyLong())).thenReturn(resultIds);

        //Execution
        List<Long> eventTicketIdsByEventIdAndUserIdAndSessionId = userSessionServiceImpl.getEventTicketIdsByEventIdAndUserIdAndSessionId(anyLong(),anyLong(),anyLong());

        assertEquals(resultIds,eventTicketIdsByEventIdAndUserIdAndSessionId);
    }

    @Test
    void test_getTicketingTypeIdByEventIdAndUserIdAndSessionId(){

        //Setup
        resultIds.add(1L);

        when(userSessionRepoService.getTicketingTypeIdByEventIdAndUserIdAndSessionId(anyLong(),anyLong(),anyLong())).thenReturn(resultIds);

        //Execution
        List<Long> eventTicketIdsByEventIdAndUserIdAndSessionId = userSessionServiceImpl.getTicketingTypeIdByEventIdAndUserIdAndSessionId(anyLong(), anyLong(), anyLong());

        assertEquals(resultIds,eventTicketIdsByEventIdAndUserIdAndSessionId);
    }

    @Test
    void test_getTicketingTypeIdByEventIdAndUserIdAndSessionId_empty_result(){

        //Setup
        when(userSessionRepoService.getTicketingTypeIdByEventIdAndUserIdAndSessionId(anyLong(),anyLong(),anyLong())).thenReturn(resultIds);

        //Execution
        List<Long> eventTicketIdsByEventIdAndUserIdAndSessionId = userSessionServiceImpl.getTicketingTypeIdByEventIdAndUserIdAndSessionId(anyLong(),anyLong(),anyLong());

        assertEquals(resultIds,eventTicketIdsByEventIdAndUserIdAndSessionId);
    }

    @Test
    void test_saveUserSession_join_false(){

        //Execution
        userSessionServiceImpl.saveUserSession(false,true,userSession, new Date());

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);

        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        assertEquals(PAST_SESSION_CHECK_IN,userSession.getCheckInStatus());

    }

    @Test
    void test_saveUserSession_join_true(){

        //Execution
        userSessionServiceImpl.saveUserSession(true,true,userSession, new Date());

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);

        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        assertEquals(CHECK_IN_AVAILABLE,userSession.getCheckInStatus());

    }

    @Test
    void test_registerUserFromPortal_no_grant_access_no_meetUp_session(){

        resultIds.add(1L);
        when(eventTicketsRepoService.getAllEventTicketTypeIdsByEventUserANDNotCanceled(any(), any())).thenReturn(resultIds);

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,true,event,true,Boolean.FALSE, null,false, Boolean.FALSE,false));

        assertEquals(NotFoundException.SessionNotFound.TICKET_TYPE_DOES_NOT_GRANT_ACCESS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_registerUserFromPortal_no_register_no_meetUp_session(){

        when(eventTicketsRepoService.getAllEventTicketTypeIdsByEventUserANDNotCanceled(any(), any())).thenReturn(resultIds);
       // when(sessionService.isValidUserForSession(any(), any(), any(), any())).thenReturn(Boolean.FALSE);
//        when(speakerRepoService.findByIdEventIdAndUserId(anyLong(),anyLong())).thenReturn(Boolean.FALSE);
        when(staffService.isEventExhibitorAdminOrLeadretriever(any(),any())).thenReturn(Boolean.FALSE);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,true,event,true,Boolean.FALSE, null,false,Boolean.FALSE,false));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.REGISTER_FAILED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_registerUserFromPortal_staff_not_register_no_meetUp_session(){

        when(eventTicketsRepoService.getAllEventTicketTypeIdsByEventUserANDNotCanceled(any(), any())).thenReturn(resultIds);
        when(speakerService.isSpeakerInEvent(event, user)).thenReturn(true);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,true,event,true,Boolean.FALSE, null,false, Boolean.FALSE,false));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.STAFF_NOT_REGISTER_IN_SESSION.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_registerUserFromPortal_no_register_meetUp_session(){

        //Setup
        session.setFormat(MEET_UP);

        when(eventTicketsRepoService.getAllEventTicketTypeIdsByEventUserANDNotCanceled(any(), any())).thenReturn(resultIds);




        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,true,event,true,Boolean.TRUE, null,false,Boolean.FALSE,false));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.REGISTER_FAILED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_registerUserFromPortal_no_grant_access_meetUp_session(){

        //Setup
        session.setFormat(MEET_UP);
        resultIds.add(1L);

        when(eventTicketsRepoService.getAllEventTicketTypeIdsByEventUserANDNotCanceled(any(), any())).thenReturn(resultIds);



        when(eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(any(), any())).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> userSessionServiceImpl.registerUserFromPortal(user,session,true,event,true,Boolean.TRUE, null,false,Boolean.FALSE,false));

        assertEquals(NotFoundException.SessionNotFound.TICKET_TYPE_DOES_NOT_GRANT_ACCESS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void testCapacityConditionIsTrue() {
        // Arrange
        session.setId(100L);  // ensure non-null ID
        session.setCapacity(10);
        event.setEventId(42L);

        // registeredCount >= capacity
        doReturn(10).when(userSessionServiceImpl)
                .countRegisteredBySessionId(eq(session.getId()));

        // allowSessionBookmarkCapacity = true
        when(roVirtualEventService.hasSessionBookmarkCapacity(eq(event.getEventId())))
                .thenReturn(true);

        // Act
        Integer registeredCount = userSessionServiceImpl.countRegisteredBySessionId(session.getId());
        boolean allowSessionBookmarkCapacity =
                roVirtualEventService.hasSessionBookmarkCapacity(event.getEventId());

        boolean condition = (allowSessionBookmarkCapacity &&
                (null != registeredCount &&
                        session.getCapacity() > 0 &&
                        session.getCapacity() <= registeredCount));

        // Assert the condition itself
        assertTrue(condition, "Expected condition to be true");

    }

    @Test
    void test_registerUserFromHost_TicketTypeNotMatched_Throw_Exception(){

        session.setCapacity(10);
        resultIds.add(1L);
        userSessionDTO.setEventId(1L);
        userSessionDTO.setSessionId(1L);
        userSessionDTO.setUserId(1L);
        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());
        when(userSessionRepoService.registerCountBySessionId(anyLong(),any())).thenReturn(null);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromHost(userSessionDTO,session,event));

        assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.TICKET_TYPE_NOT_MATCHED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_registerUserFromHost_registerMaxLimitReached_Throw_Exception(){

        Object[] obj = new Object[2];
        obj[0] = 1;
        obj[1] = 2;

        eventTicketsIdsDto.setEventTicketsId(2L);
        eventTicketsIdsDto.setTicketingTypeId(2L);
        session.setTicketTypesThatCanBeRegistered("2");

        session.setCapacity(10);
        resultIds.add(2L);
        userSessionDTO.setEventId(1L);
        userSessionDTO.setSessionId(1L);
        userSessionDTO.setUserId(1L);
        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());
        when(eventTicketsService.findEventTicketIdAndTicketingTypeIdByHolderUserIdAndEventId(userSessionDTO.getUserId(), userSessionDTO.getEventId())).thenReturn(Collections.singletonList(eventTicketsIdsDto));
        when(eventTicketsService.findTicketingTypeById(anyLong())).thenReturn(Collections.singletonList(obj));
        when(userSessionRepoService.countByEventTicketId(anyLong())).thenReturn(new BigInteger(String.valueOf(11)));
        when(userSessionRepoService.registerCountBySessionId(anyLong(),any())).thenReturn(null);
        when(userSessionRepoService.findByEventIdAndUserId(anyLong(),anyLong())).thenReturn(resultIds);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromHost(userSessionDTO,session,event));

        assertEquals("Your registration type only permits registering for 2 sessions. If you would like to register for this session, please un-register from a different session first.", exception.getMessage());
    }

    @Test
    void test_registerUserFromHost_register(){

        Object[] obj = new Object[2];
        obj[0] = 1;
        obj[1] = 2;

        eventTicketsIdsDto.setEventTicketsId(2L);
        eventTicketsIdsDto.setTicketingTypeId(2L);
        session.setTicketTypesThatCanBeRegistered("2");

        session.setCapacity(10);
        resultIds.add(2L);
        userSessionDTO.setEventId(1L);
        userSessionDTO.setSessionId(1L);
        userSessionDTO.setUserId(1L);
        when(eventTicketsService.findEventTicketIdAndTicketingTypeIdByHolderUserIdAndEventId(userSessionDTO.getUserId(), userSessionDTO.getEventId())).thenReturn(Collections.singletonList(eventTicketsIdsDto));
        when(eventTicketsService.findTicketingTypeById(anyLong())).thenReturn(Collections.singletonList(obj));
        when(userSessionRepoService.countByEventTicketId(anyLong())).thenReturn(new BigInteger(String.valueOf(1)));
        when(userSessionRepoService.registerCountBySessionId(anyLong(),any())).thenReturn(null);
        when(userSessionRepoService.save(any())).thenReturn(getUserSession());
        when(userSessionRepoService.findByEventIdAndUserId(anyLong(),anyLong())).thenReturn(resultIds);
        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());


        //Execution
        userSessionServiceImpl.registerUserFromHost(userSessionDTO,session,event);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);

        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        userSession = userSessionArgumentCaptor.getValue();
        assertEquals(userSessionDTO.getSessionId(),userSession.getSessionId());
    }

    @Test
    void test_registerUserFromHostInBulk_session_is_not_private() throws IOException {

        //setup
        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/AttendeeRegistrationSampleTemplate.csv", "multipart/form-data", inputFile);

        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.registerUserFromHostInBulk(session.getId(),file,event,"EN"));

        assertEquals(NotAcceptableException.SessionExceptionMsg.SESSION_IS_NOT_PRIVATE.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_registerUserFromHostInBulk_session_is_private() throws IOException {

        UploadSessionAttendeeResponseContainer uploadSessionAttendeeResponseContainer = new UploadSessionAttendeeResponseContainer();
        uploadSessionAttendeeResponseContainer.setMessage(Constants.SUCCESS);
        uploadSessionAttendeeResponseContainer.setInvalidAttendees(Collections.emptyList());

        //setup
        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/AttendeeRegistrationSampleTemplate.csv", "multipart/form-data", inputFile);

        session.setSessionVisibilityType(SessionVisibilityType.PRIVATE);

        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);
        doReturn(uploadSessionAttendeeResponseContainer).when(userSessionServiceImpl).handleRegisterUserFromHostInBulk(any(), any(), any(),anyString(), any());

        //Execution
        UploadSessionAttendeeResponseContainer uploadSessionAttendeeResponseContainer1 = userSessionServiceImpl.registerUserFromHostInBulk(session.getId(), file, event, "EN");

        assertEquals(uploadSessionAttendeeResponseContainer.getMessage(),uploadSessionAttendeeResponseContainer1.getMessage());

    }

    @Test
    void test_isValidHeaders_true(){
        String[] header = {Constants.FIRST_NAME,Constants.LAST_NAME,Constants.EMAIL};

        //Execution
        boolean validHeaders = userSessionServiceImpl.isValidHeaders(header);

        assertEquals(Boolean.TRUE,validHeaders);
    }

    @Test
    void test_isValidHeaders_false(){
        String[] header = {Constants.FIRST_NAME,Constants.LAST_NAME,Constants.LAST_NAME};

        //Execution
        boolean validHeaders = userSessionServiceImpl.isValidHeaders(header);

        assertEquals(Boolean.FALSE,validHeaders);
    }

    @Test
    void test_isNotWorkshopSession_true(){

        Session session1 = session;
        session1.setFormat(MEET_UP);
        //Execution
        boolean validHeaders = userSessionServiceImpl.isNotWorkshopSession(session);

        assertEquals(Boolean.TRUE,validHeaders);
    }

    @Test
    void test_registerSpeakerUser_workshop_session_true(){

        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);
        //Execution
        userSessionServiceImpl.registerSpeakerUser(1L,1L,event);

        assertTrue(true);
    }

    @Test
    void test_registerSpeakerUser_network_session(){

        session.setFormat(WORKSHOP);

        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);
        when(userService.findById(anyLong())).thenReturn(user);
        when(speakerService.isSpeakerInEvent(event, user)).thenReturn(true);
        when(sessionSpeakerService.isUserSpeakerInSession(user.getUserId(), session.getId())).thenReturn(true);


        //Execution
        userSessionServiceImpl.registerSpeakerUser(1L,1L,event);

    }

    @Test
    void test_getUserSessionByUserId(){

        when(userSessionRepoService.findEventTicketIdByUserIdAndEventId(anyLong(), anyLong())).thenReturn(resultIds);

        //Execution
        List<Long> userSessionByUserId = userSessionServiceImpl.getUserSessionByUserId(user.getUserId(), event.getEventId());

        assertEquals(resultIds,userSessionByUserId);

    }

    @Test
    void test_getRegisteredSessionCountForUser(){

        when(userSessionRepoService.userSessionCount(anyLong(), anyLong())).thenReturn(Long.valueOf(1L));

        //Execution
        Long userSessionByUserId = userSessionServiceImpl.getRegisteredSessionCountForUser(1L,user.getUserId());

        assertEquals(Long.valueOf(1L),userSessionByUserId);

    }

    @Test
    void test_deleteBySessionId(){
        doNothing().when(userSessionRepoService).updateStatusToDeleteBySessionId(1L, RecordStatus.DELETE );

        //Execution
        userSessionServiceImpl.deleteBySessionId(1L);

        assertTrue(true);
    }

    @Test
    void test_getEventTicketIdsByEventIdAndUserIdAndSessionPuchaserId(){

        when(userSessionRepoService.getEventTicketIdsByEventIdAndPurchaserUserIdAndSessionId(anyLong(), anyLong(),anyLong())).thenReturn(resultIds);

        //Execution
        List<Long> userSessionByUserId = userSessionServiceImpl.getEventTicketIdsByEventIdAndUserIdAndSessionPuchaserId(event.getEventId(),user.getUserId(),session.getId());

        assertEquals(resultIds,userSessionByUserId);

    }

    @Test
    void test_getAllUserSessionBySessionId(){

        when(userSessionRepoService.findAllBySessionId(anyLong())).thenReturn(Collections.singletonList(userSession));

        //Execution
        List<UserSession> userSessionByUserId = userSessionServiceImpl.getAllUserSessionBySessionId(session.getId());

        assertEquals(Collections.singletonList(userSession),userSessionByUserId);

    }

    @Test
    void test_getUserRegionByUserAndSessionIdAndCheckedInAvailable(){

        userSession.setRegion("test");

        when(userSessionRepoService.findUserByUserAndSessionIdAndCheckedInAvailable(anyLong(),anyLong())).thenReturn(Collections.singletonList(userSession));

        String userRegionByUserAndSessionIdAndCheckedInAvailable = userSessionServiceImpl.getUserRegionByUserAndSessionIdAndCheckedInAvailable(user.getUserId(), session.getId());
        assertEquals("test",userRegionByUserAndSessionIdAndCheckedInAvailable);
    }

    @Test
    void test_getUserRegionByUserAndSessionIdAndCheckedInAvailable_null(){

        when(userSessionRepoService.findUserByUserAndSessionIdAndCheckedInAvailable(anyLong(),anyLong())).thenReturn(Collections.emptyList());

        String userRegionByUserAndSessionIdAndCheckedInAvailable = userSessionServiceImpl.getUserRegionByUserAndSessionIdAndCheckedInAvailable(user.getUserId(), session.getId());
        assertNull(userRegionByUserAndSessionIdAndCheckedInAvailable);
    }

    @Test
    void test_findAllAttendeeSessionsByEventId(){

        Map<Long, String> sessionMap = new HashMap<>();
        Map<Long, List<Map<Long,String>>> savedAttendeeDetail = new HashMap<>();
        sessionMap.put(1L,"test session");
        List listSessions = new ArrayList<>();
        listSessions.add(listSessions);
        savedAttendeeDetail.put(1L,listSessions);

        when(eventTicketsService.findAllAttendeeSessionsDetailsByEventId(anyLong())).thenReturn(Collections.singletonList(attendeeAnalyticsDTO));

        List<AttendeeAnalyticsDTO> allAttendeeSessionsByEventId = userSessionServiceImpl.findAllAttendeeSessionsByEventId(event.getEventId(), savedAttendeeDetail);

        allAttendeeSessionsByEventId.forEach(e -> {
            assertEquals(attendeeAnalyticsDTO.getFirstName(),e.getFirstName());
            assertEquals(attendeeAnalyticsDTO.getEmailId(),e.getEmailId());
            assertEquals(attendeeAnalyticsDTO.getLastName(),e.getLastName());
            assertEquals(attendeeAnalyticsDTO.getSessionId(),e.getSessionId());
            assertEquals(attendeeAnalyticsDTO.getUserId(),e.getUserId());
        });

    }

    @Test
    void test_findAllAttendeeSessionsByEventId_null_sessionMap(){

        Map<Long, String> sessionMap = new HashMap<>();
        Map<Long, List<Map<Long,String>>> savedAttendeeDetail = new HashMap<>();
        sessionMap.put(1L,"test session");
        List listSessions = new ArrayList<>();
        listSessions.add(listSessions);
        savedAttendeeDetail.put(2L,listSessions);

        when(eventTicketsService.findAllAttendeeSessionsDetailsByEventId(anyLong())).thenReturn(Collections.singletonList(attendeeAnalyticsDTO));

        List<AttendeeAnalyticsDTO> allAttendeeSessionsByEventId = userSessionServiceImpl.findAllAttendeeSessionsByEventId(event.getEventId(), savedAttendeeDetail);

        allAttendeeSessionsByEventId.forEach(e -> {
            assertEquals(attendeeAnalyticsDTO.getFirstName(),e.getFirstName());
            assertEquals(attendeeAnalyticsDTO.getEmailId(),e.getEmailId());
            assertEquals(attendeeAnalyticsDTO.getLastName(),e.getLastName());
        });
    }

    @Test
    void test_findAttendeeNetworkingSessionsByUserId(){

        when(userSessionRepoService.findAttendeeNetworkingSessionsByUserId(anyLong(), anyLong(), any())).thenReturn(Collections.singletonList(attendeeSession));
        List<AttendeeSession> attendeeNetworkingSessionsByUserId = userSessionServiceImpl.findAttendeeNetworkingSessionsByUserId(event.getEventId(), user.getUserId(), MEET_UP);

        assertEquals(Collections.singletonList(attendeeSession),attendeeNetworkingSessionsByUserId);
    }

    @Test
    void test_getRegisteredSessionCountForUserBySessionFormat(){

        when(userSessionRepoService.getRegisteredSessionCountForUserBySessionFormat(anyLong(), anyLong(), any())).thenReturn(1L);
        Long registeredSessionCountForUserBySessionFormat = userSessionServiceImpl.getRegisteredSessionCountForUserBySessionFormat(event.getEventId(), user.getUserId(), MEET_UP);

        assertEquals(Long.valueOf(1L),registeredSessionCountForUserBySessionFormat);

    }

    @Test
    void test_deleteByEventId(){

        doNothing().when(userSessionRepoService).deleteByEventId(anyLong());

        userSessionServiceImpl.deleteByEventId(session.getId());

        assertTrue(true);
    }

    @Test
    void test_checkInSessionUsingBarcodeId_with_BarcodeId_NULL() {
        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.checkInSessionUsingBarcodeId(event, null, userSessionDTO, true, false, null, user, false, false, null, null, false));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_checkInSessionUsingBarcodeId_with_UserId_success() {

        userSessionDTO.setEventId(Long.valueOf("1"));
        userSessionDTO.setUserId(Long.valueOf("1"));
        userSessionDTO.setSessionId(Long.valueOf("1"));

        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        session.setTicketTypesThatCanBeRegistered("1,2");

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});
        ticketingTypeAttribute.add(new Object[]{1});

        UserSession userSession = getUserSession();
        userSession.setCheckInStatus(CHECK_OUT);
        eventTickets.setHolderUserId(user);

        when(userService.findByUserId(anyLong())).thenReturn(user);
        when(eventTicketsService.findSingleEventTicketsByHolderUserIdAndEventId(any(),any())).thenReturn(eventTickets);
        when(sessionRepoService.getSessionById(any(), any())).thenReturn(session);
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(anyLong(),anyLong(),anyLong(),anyLong())).thenReturn(userSession);
        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());





        //Execution
        userSessionServiceImpl.checkInSessionUsingBarcodeId(event, "1", userSessionDTO, true, false, null, user,false, false, null, null, false);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        assertNotNull(session);
        assertEquals(CHECK_IN_AVAILABLE,userSession.getCheckInStatus());
        assertEquals(user.getUserId(),userSession.getUserId());
    }


    @Test
    void test_checkInSessionUsingBarcodeId_CheckInWithAnotherTicket() {

        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        session.setTicketTypesThatCanBeRegistered("1,2");

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});
        ticketingTypeAttribute.add(new Object[]{1});

        UserSession userSession = getUserSession();
        userSession.setCheckInStatus(CHECK_OUT);

        eventTickets.setHolderUserId(user);
        eventTickets.setId(2);

        when(userService.findByUserId(anyLong())).thenReturn(user);
        when(eventTicketsService.findSingleEventTicketsByHolderUserIdAndEventId(any(),any())).thenReturn(eventTickets);
        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());


        when(sessionRepoService.getSessionById(any(), any())).thenReturn(session);
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(anyLong(),anyLong(),anyLong(),anyLong())).thenReturn(userSession);


        //Execution
        userSessionServiceImpl.checkInSessionUsingBarcodeId(event, "1", userSessionDTO, true, false, null, user, false,false, null, null, false);

    }

    @Test
    void test_checkInSessionUsingBarcodeId_RegisteredWithAnotherTicket() {
        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        session.setTicketTypesThatCanBeRegistered("1,2");

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});
        ticketingTypeAttribute.add(new Object[]{1});

        UserSession userSession = getUserSession();
        userSession.setCheckInStatus(CHECK_OUT);

        eventTickets.setHolderUserId(user);
        eventTickets.setId(3);

        when(userService.findByUserId(anyLong())).thenReturn(user);
        when(eventTicketsService.findSingleEventTicketsByHolderUserIdAndEventId(any(),any())).thenReturn(eventTickets);

        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());

        when(sessionRepoService.getSessionById(any(), any())).thenReturn(session);
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(anyLong(),anyLong(),anyLong(),anyLong())).thenReturn(userSession);


        //Execution
        userSessionServiceImpl.checkInSessionUsingBarcodeId(event, "1", userSessionDTO, true, false, null, user, false,false, null, null, false);

    }


    @Test
    void test_checkInUserInSessionWithEventTicketIdOrBarcodeId_with_eventTicketId_NULL() {

        //MOCK
        when(eventTicketsRepoService.getEventTicketByBarcodeIdAndNotCanceled(any())).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> userSessionServiceImpl.checkInSessionUsingBarcodeId(event, UUID.randomUUID().toString(), userSessionDTO, true, false, null, user, false, false, null, null, false));

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.EVENT_TICKETS_NOT_FOUND.getErrorMessage(), exception.getMessage());
    }


    @Test
    void test_checkInUserInSessionWithEventTicketIdOrBarcodeId_with_userId_NULL() {

        when(userService.findByUserId(anyLong())).thenReturn(null);
        when(eventTicketsService.findSingleEventTicketsByHolderUserIdAndEventId(any(),any())).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> userSessionServiceImpl.checkInSessionUsingBarcodeId(event, "1", userSessionDTO, true, false, null, user, false,false, null, null, false));

        assertEquals(NotFoundException.UserNotFound.USER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_checkInUserInSessionWithEventTicketIdOrBarcodeId_and_ticket_not_allowed_to_virtual_event() throws Exception {
        session.setSessionTypeFormat(SessionTypeFormat.IN_PERSON);

        //MOCK
        when(eventTicketsRepoService.getEventTicketByBarcodeIdAndNotCanceled(any())).thenReturn(eventTickets);
        when(sessionRepoService.getSessionById(any(), any())).thenReturn(session);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> userSessionServiceImpl.checkInSessionUsingBarcodeId(event, UUID.randomUUID().toString(), userSessionDTO, true, false, null, user, false, false, null, null, false));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.ATTENDEE_TICKET_NOT_GRANT_ACCESS_TO_THIS_SESSION.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_checkInUserInSessionWithEventTicketIdOrBarcodeId_success() {
        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        session.setTicketTypesThatCanBeRegistered("1,2");

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});
        ticketingTypeAttribute.add(new Object[]{1});

        UserSession userSession = getUserSession();
        userSession.setCheckInStatus(CHECK_OUT);

        eventTickets.setHolderUserId(user);

        //MOCK
        when(eventTicketsRepoService.getEventTicketByBarcodeIdAndNotCanceled(any())).thenReturn(eventTickets);
        when(sessionRepoService.getSessionById(any(), any())).thenReturn(session);
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(anyLong(),anyLong(),anyLong(),anyLong())).thenReturn(userSession);
        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());







        //Execution
        userSessionServiceImpl.checkInSessionUsingBarcodeId(event, UUID.randomUUID().toString(), userSessionDTO, true, false, null, user, false, false, null, null, false);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        assertNotNull(session);
        assertEquals(CHECK_IN_AVAILABLE,userSession.getCheckInStatus());
    }


    @Test
    void test_checkInUserInSessionWithEventTicketIdOrBarcodeId_success_withOfflineData() {
        userSessionDTO.setEventId(Long.valueOf("1"));
        userSessionDTO.setUserId(Long.valueOf("1"));
        userSessionDTO.setSessionId(Long.valueOf("1"));

        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        session.setTicketTypesThatCanBeRegistered("1,2");

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});
        ticketingTypeAttribute.add(new Object[]{1});

        UserSession userSession = getUserSession();
        userSession.setCheckInStatus(CHECK_OUT);

        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.DAY_OF_MONTH, -2);
        Date pastDate = calendar.getTime();
        userSessionDTO.setCheckInTime(pastDate);

        eventTickets.setHolderUserId(user);

        //MOCK
        when(eventTicketsRepoService.getEventTicketByBarcodeIdAndNotCanceled(any())).thenReturn(eventTickets);
        when(sessionRepoService.getSessionById(any(), any())).thenReturn(session);
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(anyLong(),anyLong(),anyLong(),anyLong())).thenReturn(userSession);
        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());

        //Execution
        userSessionServiceImpl.checkInSessionUsingBarcodeId(event, UUID.randomUUID().toString(), userSessionDTO, true, false, null, user, true, false, null, null, false);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        assertNotNull(session);
        assertEquals(CHECK_IN_AVAILABLE,userSession.getCheckInStatus());
    }

    @Test
    void test_checkInSessionAndEventBoth_success() throws IOException {

        //setup
        session.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        session.setTicketTypesThatCanBeRegistered("1,2");

        List<Object[]> ticketingTypeAttribute =new ArrayList<>();
        ticketingTypeAttribute.add(new Object[]{2L,3});
        ticketingTypeAttribute.add(new Object[]{1});

        UserSession userSession = getUserSession();
        userSession.setCheckInStatus(CHECK_OUT);

        TicketCheckInDto ticketCheckInDto= new TicketCheckInDto();
        ticketCheckInDto.setStatus(TicketStatus.CHECKED_IN.getStatus());

        eventTickets.setHolderUserId(user);
        eventTickets.setBarcodeId(UUID.randomUUID().toString());

        //MOCK
        when(eventTicketsRepoService.getEventTicketByBarcodeIdAndNotCanceled(any())).thenReturn(eventTickets);
        when(sessionRepoService.getSessionById(any(), any())).thenReturn(session);
        when(userSessionRepoService.findBySessionIdAndUserIdAndEventIdAndTicketIdAndCheckout(anyLong(),anyLong(),anyLong(),anyLong())).thenReturn(userSession);


        doNothing().when(userSessionServiceImpl).validateRegisterCapacityAndConcurrentSessionRegistrationRules(any(),any(),any());
        when(ticketingCheckInService.checkInTicket(any(),anyString(),any(), any(), any(),eq(false), any(), any(), eq(false))).thenReturn(ticketCheckInDto);

        when(userSessionRepoService.save(any())).thenReturn(userSession);


        //Execution
        userSessionServiceImpl.checkInSessionUsingBarcodeId(event, UUID.randomUUID().toString(), userSessionDTO, true, false, device, user, false, false, null, null, false);

        ArgumentCaptor<UserSession> userSessionArgumentCaptor = ArgumentCaptor.forClass(UserSession.class);
        verify(userSessionRepoService).save(userSessionArgumentCaptor.capture());
        assertNotNull(session);
        assertEquals(CHECK_IN_AVAILABLE,userSession.getCheckInStatus());
        assertEquals(TicketStatus.CHECKED_IN.getStatus(),ticketCheckInDto.getStatus());
    }


}
