package com.accelevents.session_speakers.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.auction.dto.ExhibitorDropDownDto;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.common.dto.ChimeConfigDto;
import com.accelevents.common.dto.SessionMassOperationConfigDTO;
import com.accelevents.common.dto.UploadSessionDto;
import com.accelevents.common.dto.UploadSessionResponseContainer;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.networking.services.NetworkingLounge;
import com.accelevents.networking.services.NetworkingMatcheQueueRepoService;
import com.accelevents.networking.services.NetworkingMatchesService;
import com.accelevents.networking.services.NetworkingRulesService;
import com.accelevents.repositories.RtmpSessionAuditLogsRepo;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.repositories.TicketingTypeCommonRepo;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.accelevents.services.elasticsearch.videoanalytics.SessionVideoAnalyticsData;
import com.accelevents.services.elasticsearch.videoanalytics.VideoAnalyticsService;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.services.keystore.GamificationCacheStoreService;
import com.accelevents.services.neptune.NeptuneNetworkingLoungeService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.KeyValueRepoService;
import com.accelevents.services.repo.helper.SurveyConfigRepoService;
import com.accelevents.session_speakers.dto.*;
import com.accelevents.session_speakers.repo.SessionLocationRepo;
import com.accelevents.session_speakers.repo.SessionRepo;
import com.accelevents.session_speakers.repo.SessionSpeakerRepo;
import com.accelevents.session_speakers.services.*;
import com.accelevents.ticketing.dto.TicketingDatesDto;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.amazonaws.services.chimesdkmeetings.model.Meeting;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockHttpServletRequest;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.domain.enums.EnumSessionFormat.*;
import static com.accelevents.domain.enums.StreamProvider.DIRECT_UPLOAD;
import static com.accelevents.exceptions.NotAcceptableException.SessionSpeakerExceptionMsg.*;
import static com.accelevents.utils.Constants.START;
import static com.accelevents.utils.Constants.STOP;
import static java.util.Collections.EMPTY_SET;
import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SessionServiceImplTest {

    @Spy
    @InjectMocks
    private SessionServiceImpl sessionServiceImpl = new SessionServiceImpl(null,null);

    @Mock
    private SessionSpeakerRepoService sessionSpeakerRepoService;
    @Mock
    private SessionTagAndTrackService sessionTagAndTrackService;

    @Mock
    private SessionSpeakerService sessionSpeakerService;

    @Mock
    private SessionService sessionService;

    @Mock
    private SessionRepoService sessionRepoService;


    @Mock
    private UserService userService;

    @Mock
    private UserSessionService userSessionService;

    @Mock
    private SessionSpeakerRepo sessionSpeakerRepo;

    @Mock
    private SessionDetailsRepoService sessionDetailsRepoService;


    @Mock
    MuxService muxService;

    @Mock
    private SessionRepo repo;

    @Mock
    private KeyValueService keyValueService;

    @Mock
    private UserInterestedSessionService userInterestedSessionService;

    @Mock
    private SpeakerService speakerService;

    @Mock
    private GetStreamService getStreamService;

    @Mock
    private NetworkingRulesService networkingRulesService;

    @Mock
    private NetworkingMatcheQueueRepoService networkingMatchesQueueRepoService;

    @Mock
    private RtmpSessionAuditLogsRepo rtmpSessionAuditLogsRepo;

    @Mock
    private NetworkingMatchesService networkingMatchesService;

    @Mock
    private MUXLivestreamAssetService muxLivestreamAssetService;

    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    @Mock
    private ChimeService chimeService;

    @Mock
    private EventChecklistService eventChecklistService;

    @Mock
    private SessionDetailsService sessionDetailsService;

    @Mock
    private SessionRepo sessionRepo;

    @Mock
    private VideoAnalyticsService videoAnalyticsService;

    @Mock
    private TicketingRepository ticketingRepository;

    @Mock
    TicketingTypeCommonRepo ticketingTypeCommonRepo;

    @Mock
    SessionLocationRepo sessionLocationRepo;

    @Mock
    private ChargebeeService chargebeeService;

    @Mock
    private StaffService staffService;

    @Mock
    private SessionThirdPartyService sessionThirdPartyService;

    @Mock
    private WorkshopRecordingAssetService workshopRecordingAssetService;

    @Mock
    private TicketingTypeTagAndTrackRepoService ticketingTypeTagAndTrackRepoService;

    @Mock
    private ROEventService roEventService;
    @Mock
    private VirtualEventService virtualEventService;

    @Mock
    private SpeakerHelperService speakerHelperService;

    @Mock
    private SponsorsService sponsorsService;

    @Mock
    private ExhibitorService exhibitorService;

    @Mock
    private SessionSubtitleRepoService sessionSubtitleRepoService;
    @Mock
    private SessionSubtitleService sessionSubtitleService;

    @Mock
    private MUXLivestreamAssetRepoService muxLivestreamAssetRepoService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private SessionDeleteHandler sessionDeleteHandler;
    @Mock
    private MessageToContactService messageToContactService;
    @Mock
    private WorkshopRecordingAssetsRepoService workshopRecordingAssetsRepoService;
    @Mock
    private WorkshopSessionRecordingService workshopSessionRecordingService;
    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private NeptuneNetworkingLoungeService neptuneNetworkingLoungeService;
    @Mock
    private SurveyConfigRepoService surveyConfigRepoService;
    @Mock
    UserSessionRepoService userSessionRepoService;
    @Mock
    VirtualEventSessionLoggingService virtualEventSessionLoggingService;
    @Mock
    private KeyValueRepoService keyValueRepoService;

    @Mock
    private GamificationCacheStoreService redisCacheService;
    @Mock
    private EventTaskService eventTaskService;

    @Mock
    SessionLocationService sessionLocationService;

    @Mock
    private CustomFormAttributeService customFormAttributeService;

    private SessionDTO sessionDTO;
    private Event event;
    private User user;
    private Session session;
    private SessionTagAndTrack sessionTagAndTrack;
    private SpeakerDTO speakerDTO;
    private SessionDetails sessionDetails;
    private  TicketingTypeTagAndTrack ticketingTypeTagAndTrack;
    private Long sessionId = 1L;
    private double position = 1000;
    private String testAppsyncAuthToken = "test-auth-token";
    private SessionBasicDetailsDTO sessionBasicDetailsDTO;

    private DateTime today,yesterday,tomorrow;

    private Speaker speaker;
	@BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();

		Long speakerId = 1L;
		SessionSpeaker sessionSpeaker = new SessionSpeaker(sessionId, speakerId,position, false);
        sessionSpeaker.setId(1L);

        session = EventDataUtil.getSession();
        session.setId(sessionId);
//        session.setLocationId(4L);

        sessionDTO = EventDataUtil.getSessionDto();

        speakerDTO  = EventDataUtil.getSpeakerDto();

        sessionTagAndTrack = EventDataUtil.getSessionTagAndTrack();

        sessionDetails = new SessionDetails();
        sessionDetails.setId(1L);

        Date date  = new Date();
        today = new DateTime(date);
        yesterday = today.minusDays(1);
        tomorrow = today.plus(1);

        KeyValue tag = new KeyValue();
        tag.setEventId(event);
        tag.setPosition(1.1);
        tag.setName("Tag-1");
        tag.setId(1L);
        tag.setType(EnumKeyValueType.TAG);

        KeyValue track = new KeyValue();
        tag.setEventId(event);
        tag.setPosition(1.1);
        tag.setName("Track-1");
        tag.setId(2L);
        tag.setType(EnumKeyValueType.TRACK);


        ticketingTypeTagAndTrack = new TicketingTypeTagAndTrack();
        ticketingTypeTagAndTrack.setId(1L);
        ticketingTypeTagAndTrack.setTagOrTrack(tag);
        speaker = EventDataUtil.getSpeaker();
        sessionBasicDetailsDTO = EventDataUtil.getSessionBasicDetailsDTO();
    }

    @Test
    void test_saveAll_success(){

	    //mock
        Mockito.doNothing().when(sessionRepoService).saveAll(anyList());

        //Execution
        sessionServiceImpl.saveAll(Collections.singletonList(session));

        //Assertion
        verify(sessionRepoService).saveAll(anyList());
    }

    @Test
    void test_save_success(){

	    //mock
        when(sessionRepoService.save(any())).thenReturn(session);

        //Execution
        Session sessionData = sessionServiceImpl.save(session);

        //Assertion
        verify(sessionRepoService).save(any());
        assertEquals(sessionData.getFormat(), session.getFormat());
    }

    @Test
    void test_getSessionById(){

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);

        //Execution
        Session sessionData = sessionServiceImpl.getSessionById(session.getId(), event);

        //Assertion
        verify(sessionRepoService).getSessionById(anyLong(), any());
        assertEquals(sessionData.getFormat(), session.getFormat());
    }

    @Test
    void test_createSession_MAIN_STAGE_success(){

        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");


        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());

        Mockito.when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(true);

        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));

	    //setup
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        sessionDTO.setFormat(EnumSessionFormat.MAIN_STAGE);

        //mock
        when(sessionRepoService.findSessionsWithConflictingSlot(any(), anyString(), anyString(), anyLong())).thenReturn(List.of());
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        when(sessionRepoService.save(any())).thenReturn(session);
        when(sessionRepoService.getLastConcurrentSession(any(), any(), any())).thenReturn(session);
        EventChecklist eventChecklist = new EventChecklist();
        eventChecklist.setEventAgendaAdded(Boolean.FALSE);
        doReturn(eventChecklist).when(eventChecklistService).findByEvent(any());

        //Execution
        Long newSessionId = sessionServiceImpl.createSession(event, sessionDTO);

        //Assertion
        verify(sessionRepoService).findSessionsWithConflictingSlot(any(), anyString(), anyString(), anyLong());
        verify(sessionRepoService).save(any());

        assertEquals(newSessionId.longValue(), session.getId());
    }

    @Test
    void test_createSession_MAIN_STAGE_success_with_survey(){

        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");


        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));

        //setup
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        sessionDTO.setFormat(EnumSessionFormat.MAIN_STAGE);

        //mock
        when(sessionRepoService.findSessionsWithConflictingSlot(any(), anyString(), anyString(), anyLong())).thenReturn(List.of());
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        when(sessionRepoService.save(any())).thenReturn(session);
        when(sessionRepoService.getLastConcurrentSession(any(), any(), any())).thenReturn(session);
        when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(true);
        doNothing().when(eventTaskService).updateEventTaskAndAssignTaskToSession(any(), anyList());

        EventChecklist eventChecklist = new EventChecklist();
        eventChecklist.setEventAgendaAdded(Boolean.FALSE);
        doReturn(eventChecklist).when(eventChecklistService).findByEvent(any());

        //Execution
        Long newSessionId = sessionServiceImpl.createSession(event, sessionDTO);

        //Assertion
        verify(sessionRepoService).findSessionsWithConflictingSlot(any(), anyString(), anyString(), anyLong());
        verify(sessionRepoService).save(any());

        assertEquals(newSessionId.longValue(), session.getId());
    }

    @Test
    void test_createSession_MAIN_STAGE_success_withOut_survey(){

        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");


        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));

        //setup
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        sessionDTO.setFormat(EnumSessionFormat.MAIN_STAGE);

        //mock
        when(sessionRepoService.findSessionsWithConflictingSlot(any(), anyString(), anyString(), anyLong())).thenReturn(List.of());
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);


        when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(false);

        EventChecklist eventChecklist = new EventChecklist();
        eventChecklist.setEventAgendaAdded(Boolean.FALSE);



        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> sessionServiceImpl.createSession(event, sessionDTO));

        //Assertion
        assertEquals(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(surveyConfigRepoService).isSurveyExist(anyLong(),anyLong());
    }


    @Test
    void test_createSession_MAIN_STAGE_conflictSession(){


        //setup
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        sessionDTO.setFormat(EnumSessionFormat.MAIN_STAGE);


        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));


        String conflictingSessionName = "mainStage session";
        NotAcceptableException.SessionSpeakerExceptionMsg exceptionMsg = SESSION_SLOTS_COLLIDES;
        exceptionMsg.setErrorMessage(Constants.SESSION_SLOTS_COLLIDES.replace("{SESSION_NAME}", conflictingSessionName));
        exceptionMsg.setDeveloperMessage(
                Constants.SESSION_SLOTS_COLLIDES.replace("{SESSION_NAME}", conflictingSessionName));

        //mock
        when(sessionRepoService.findSessionsWithConflictingSlot(any(), anyString(), anyString(), anyLong())).thenReturn(Collections.singletonList(conflictingSessionName));
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> sessionServiceImpl.createSession(event, sessionDTO));

        //Assertion
        assertEquals(SESSION_SLOTS_COLLIDES.getDeveloperMessage(), exception.getMessage());
        verify(sessionRepoService).findSessionsWithConflictingSlot(any(), anyString(), anyString(), anyLong());
    }

    @Test
    void test_createSession_MAIN_STAGE_EmptyStreamKeyStreamUrlAndRtmpUrl(){

        //setup
        sessionDTO.setFormat(EnumSessionFormat.MAIN_STAGE);
        sessionDTO.setStreamKey(Constants.STRING_EMPTY);
        sessionDTO.setStreamUrl(Constants.STRING_EMPTY);
        sessionDTO.setRtmpUrl(Constants.STRING_EMPTY);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> sessionServiceImpl.createSession(event, sessionDTO));

        assertEquals(REQUIRED_FOR_AE_STUDIO.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createSession_MEET_UP_success(){

        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));
        Mockito.when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(true);

        //setup
        session.setFormat(EnumSessionFormat.MEET_UP);
        sessionDTO.setFormat(EnumSessionFormat.MEET_UP);

        //mock
        when(sessionRepoService.save(any())).thenReturn(session);
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        Mockito.doNothing().when(networkingRulesService).createOrUpdateNetworkingRuleWhenCreatingSession(any(), any());

        //Execution
        Long newSessionId = sessionServiceImpl.createSession(event, sessionDTO);

        //Assertion
        verify(sessionRepoService).save(any());
        verify(networkingRulesService).createOrUpdateNetworkingRuleWhenCreatingSession(any(), any());

        assertEquals(newSessionId.longValue(), session.getId());
    }

    @Test
    void test_updateSession_MAIN_STAGE() {

        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        session.setTicketTypesThatCanBeRegistered("1");

        Mockito.when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(true);

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));
        sessionDTO.setTicketTypesThatCanBeRegistered(Collections.singletonList(1L));

        //mock
        when(sessionRepoService.getSessionByIdWithoutCache(sessionId,event)).thenReturn((session));
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearSessionCache(session);
        when(sessionRepoService.save(session)).thenReturn(session);
        //Execution
        sessionDTO.setFormat(session.getFormat());
        sessionServiceImpl.updateSession(sessionId, sessionDTO, event, user);

        //Assertion
        verify(sessionRepoService).getSessionByIdWithoutCache(sessionId, event);

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());

        Session sessionData = sessionArgumentCaptor.getValue();
        assertEquals(sessionData.getRtmpUrl(), sessionDTO.getRtmpUrl());
        assertEquals(sessionData.getTitle(), sessionDTO.getTitle());
        assertEquals(sessionData.getStreamKey(), sessionDTO.getStreamKey());
        assertEquals(sessionData.getTicketTypesThatCanBeRegistered(),"1");

        Session session1 = (Session) session.clone();
        session1.setFormat(MAIN_STAGE);
        session1.setLiveStreamId("");

        Session session2 = (Session) session.clone();
        session2.setFormat(MEET_UP);
        session2.setLiveStreamId("");

        SessionDTO sessionDTO1 = new SessionDTO(session,event);
        sessionDTO1.setFormat(MEET_UP);

        SessionDTO sessionDTO2 = new SessionDTO(session,event);
        sessionDTO2.setFormat(MAIN_STAGE);



        assertNotNull(sessionServiceImpl.updateSession(sessionId, sessionDTO1, event, user));
        assertNotNull(sessionServiceImpl.updateSession(sessionId, sessionDTO2, event, user));


    }

    //TODO: Junit5 review test
    /*@Test
    void test_updateSession_MAIN_STAGE_without_Survey() {

        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        session.setTicketTypesThatCanBeRegistered("1");

        Mockito.when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(true);

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());
        when(ticketingDatesDto.getEventEndDate()).thenReturn(tomorrow.toDate());

        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));
        sessionDTO.setTicketTypesThatCanBeRegistered(Collections.singletonList(1L));

        //mock
        when(sessionRepoService.getSessionByIdWithoutCache(sessionId,event)).thenReturn((session));
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(false);

        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearSessionCache(session);
        when(sessionRepoService.save(session)).thenReturn(session);
        sessionDTO.setFormat(session.getFormat());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> sessionServiceImpl.updateSession(sessionId, sessionDTO, event, user));

        //Assertion
        assertEquals(NotFoundException.SurveyNotFound.SURVEY_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(sessionRepoService).getSessionByIdWithoutCache(sessionId, event);

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());

        Session sessionData = sessionArgumentCaptor.getValue();
        assertEquals(sessionData.getRtmpUrl(), sessionDTO.getRtmpUrl());
        assertEquals(sessionData.getTitle(), sessionDTO.getTitle());
        assertEquals(sessionData.getStreamKey(), sessionDTO.getStreamKey());
        assertEquals(sessionData.getTicketTypesThatCanBeRegistered(),"1");

        Session session1 = (Session) session.clone();
        session1.setFormat(MAIN_STAGE);
        session1.setLiveStreamId("");

        Session session2 = (Session) session.clone();
        session2.setFormat(MEET_UP);
        session2.setLiveStreamId("");

        SessionDTO sessionDTO1 = new SessionDTO(session,event);
        sessionDTO1.setFormat(MEET_UP);

        SessionDTO sessionDTO2 = new SessionDTO(session,event);
        sessionDTO2.setFormat(MAIN_STAGE);

        when(sessionRepoService.getSessionByIdWithoutCache(sessionId+1, event)).thenReturn(session1);
        when(sessionRepoService.getSessionByIdWithoutCache(sessionId+1, event)).thenReturn(session2);
        assertNotNull(sessionServiceImpl.updateSession(sessionId, sessionDTO1, event, user));
        assertNotNull(sessionServiceImpl.updateSession(sessionId, sessionDTO2, event, user));

    }*/

    @Test
    void test_updateSession_MEET_UP() {

	    //setup
        session.setFormat(EnumSessionFormat.MEET_UP);
        session.setSessionTagAndTracks(Collections.singleton(sessionTagAndTrack));
        sessionDTO.setFormat(EnumSessionFormat.MEET_UP);
        session.setTicketTypesThatCanBeRegistered("1");
        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());

        Mockito.when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(true);

        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));
        sessionDTO.setTicketTypesThatCanBeRegistered(Collections.singletonList(1L));
        //mock
        when(sessionRepoService.getSessionByIdWithoutCache(sessionId,event)).thenReturn((session));
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        when(sessionRepoService.save(session)).thenReturn(session);

        Mockito.doNothing().when(networkingRulesService).createOrUpdateNetworkingRuleWhenCreatingSession(any(), any());
        Mockito.doNothing().when(sessionTagAndTrackService).createRecords(anyLong(), anySet());
        Mockito.doNothing().when(sessionTagAndTrackService).deleteRecords(anyLong(), anySet());

        //Execution
        sessionServiceImpl.updateSession(sessionId, sessionDTO, event, user);

        //Assertion
        verify(sessionRepoService).getSessionByIdWithoutCache(sessionId, event);
        verify(networkingRulesService).createOrUpdateNetworkingRuleWhenCreatingSession(any(), any());
        verify(sessionTagAndTrackService).createRecords(anyLong(), anySet());
        verify(sessionTagAndTrackService).deleteRecords(anyLong(), anySet());

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());

        Session sessionData = sessionArgumentCaptor.getValue();
        assertEquals(sessionData.getRtmpUrl(), sessionDTO.getRtmpUrl());
        assertEquals(sessionData.getTitle(), sessionDTO.getTitle());
        assertEquals(sessionData.getStreamKey(), sessionDTO.getStreamKey());
        assertEquals(sessionData.getTicketTypesThatCanBeRegistered(), "1");
    }

    @Test
    void test_updateSession_format() {

        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        session.setTicketTypesThatCanBeRegistered("1");
        Mockito.when(surveyConfigRepoService.isSurveyExist(anyLong(),anyLong())).thenReturn(true);


        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        sessionDTO.setStartTime(dtfOut.print(today));
        sessionDTO.setEndTime(dtfOut.print(today));
        sessionDTO.setTicketTypesThatCanBeRegistered(Collections.singletonList(1L));

        String oldSessionDTOTitle = sessionDTO.getTitle();
        sessionDTO.setTitle(session.getTitle());

        //mock
        when(sessionRepoService.getSessionByIdWithoutCache(sessionId,event)).thenReturn((session));
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearSessionCache(session);
        when(sessionRepoService.save(session)).thenReturn(session);

        sessionDTO.setFormat(session.getFormat());
        //Execution
        sessionServiceImpl.updateSession(sessionId, sessionDTO, event, user);

        //Assertion
        verify(sessionRepoService).getSessionByIdWithoutCache(sessionId, event);

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());

        Session sessionData = sessionArgumentCaptor.getValue();
        assertEquals(sessionData.getRtmpUrl(), sessionDTO.getRtmpUrl());
        assertEquals(sessionData.getTitle(), sessionDTO.getTitle());
        assertEquals(sessionData.getStreamKey(), sessionDTO.getStreamKey());
        assertEquals(sessionData.getTicketTypesThatCanBeRegistered(),"1");

        sessionDTO.setTitle(oldSessionDTOTitle);
    }

    @Test
    void test_getSessionList() {

        //setup
        Pageable pageable = PageRequest.of(1, 10);
        Page<Session> page = new PageImpl<>(Collections.singletonList(session));
        SessionFilter sessionFilter = new SessionFilter();
        sessionFilter.setSessionFormat(EnumSessionFormat.MAIN_STAGE.name());
        String calledFrom ="PORTAL";



        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);






        //mock
        when(sessionRepoService.getAllSessionByEventId(any(), any(), any(), any(), anyBoolean(), anyString())).thenReturn(page);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        //Execution
        DataTableResponse dataTableResponse = sessionServiceImpl.getSessionList("SESSION",event, user, pageable, sessionFilter, false, calledFrom);

        //Assertion
        assertNotNull(dataTableResponse.getData().get(0));
    }

    @Test
    void test_getSessionInfoById_withExpand_SPEAKER(){

	    //setup
        speakerDTO.setSessionDTO(Collections.singletonList(sessionDTO));

        //mock
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionSpeakerService.getSpeakerDtoBySession(anyLong())).thenReturn(Collections.singletonList(speakerDTO));
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "SPEAKER");

        //Assertion
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        verify(sessionSpeakerService).getSpeakerDtoBySession(anyLong());
        assertEquals(sessionDTOData.getSpeakerList().get(0).getSessionDTO().get(0).getFormat(), sessionDTO.getFormat());
        assertEquals(sessionDTOData.getSpeakerList().get(0).getFirstName(), speakerDTO.getFirstName());
    }


    @Test
    void test_getSessionInfoById_with_bookmark_capacity_reached_true(){

        session.setCapacity(1);

        //mock
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionSpeakerService.getSpeakerDtoBySession(anyLong())).thenReturn(Collections.singletonList(speakerDTO));
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        when(userSessionService.getSessionRegisterCountAndTicketIdIsNotNullAndEventId(sessionId, event.getEventId())).thenReturn(1);
        when(virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId())).thenReturn(Boolean.TRUE);
        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "SPEAKER");

        //Assertion
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(Boolean.TRUE,sessionDTOData.isSessionBookmarkCapacityReached());
    }


    @Test
    void test_getSessionInfoById_with_bookmark_capacity_reached_false(){

        session.setCapacity(1);

        //mock
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionSpeakerService.getSpeakerDtoBySession(anyLong())).thenReturn(Collections.singletonList(speakerDTO));
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        when(virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId())).thenReturn(Boolean.TRUE);
        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "SPEAKER");

        //Assertion
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(Boolean.FALSE,sessionDTOData.isSessionBookmarkCapacityReached());
    }

    @Test
    void test_getSessionInfoById_with_bookmark_capacity_reached_false_when_toggle_off(){

        session.setCapacity(1);

        //mock
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionSpeakerService.getSpeakerDtoBySession(anyLong())).thenReturn(Collections.singletonList(speakerDTO));
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        when(virtualEventService.isAllowSessionBookmarkCapacity(event.getEventId())).thenReturn(Boolean.FALSE);
        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "SPEAKER");

        //Assertion
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(Boolean.FALSE,sessionDTOData.isSessionBookmarkCapacityReached());
    }


    @Test
    void test_getSessionInfoById_withExpand_TAG(){

	    //setup
        KeyValue keyValue = new KeyValue();
        keyValue.setEventId(event);
        keyValue.setId(1L);
        keyValue.setType(EnumKeyValueType.TAG);
        keyValue.setName("Demo Tag");

        session.setSessionTagAndTracks(Collections.singleton(sessionTagAndTrack));

        //mock
        when(keyValueService.findAllByIds(anyList())).thenReturn(Collections.singletonList(keyValue));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "TAG");

        //Assertion
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        verify(keyValueService).findAllByIds(anyList());
        assertEquals(sessionDTOData.getTags().get(0).getName(), keyValue.getName());
    }

    @Test
    void test_getSessionInfoById_withExpand_TRACK(){

        //setup
        KeyValue keyValue = new KeyValue();
        keyValue.setEventId(event);
        keyValue.setId(2L);
        keyValue.setType(EnumKeyValueType.TRACK);
        keyValue.setName("Demo TRACK");

        session.setSessionTagAndTracks(Collections.singleton(sessionTagAndTrack));

        //mock
        when(keyValueService.findAllByIds(anyList())).thenReturn(Collections.singletonList(keyValue));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "TRACK");

        //Assertion
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        verify(keyValueService).findAllByIds(anyList());
        assertEquals(sessionDTOData.getTracks().get(0).getName(), keyValue.getName());
    }

    @Test
    void test_getSessionInfoById_withExpand_registerdHolderUsers(){

	    //setup
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setEventTicketId(1L);
        registerdHolderUsers.setFirstName("Aayushi");
        registerdHolderUsers.setLastName("Chauhan");
        registerdHolderUsers.setUserId(1L);

        //mock
        when(userSessionRepoService.findRegisteredUsersBySessionIdAndRegisteredStatus(anyLong(),any())).thenReturn(Collections.singletonList(registerdHolderUsers));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "registerdHolderUsers");

        //Assertion
        verify(userSessionRepoService).findRegisteredUsersBySessionIdAndRegisteredStatus(anyLong(),any());
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(sessionDTOData.getRegisterdHolderUsers().get(0).getEmail(), registerdHolderUsers.getEmail());
    }

    @Test
    void test_getSessionInfoById_withExpand_STATS(){

	    //setup
        Integer registerCount = 1;
        Integer interestedCount = 1;

        //mock
        when(userSessionService.countRegisteredBySessionId(anyLong())).thenReturn(registerCount);
        when(userInterestedSessionService.countInterestedBySessionId(anyLong())).thenReturn(interestedCount);
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        when(userSessionService.countRegisteredBySessionId(sessionId)).thenReturn(1);
        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "STATS");

        //Assertion
        verify(userSessionService,times(1)).countRegisteredBySessionId(anyLong());
        verify(userInterestedSessionService).countInterestedBySessionId(anyLong());
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(sessionDTOData.getSessionStats().getInterestedUserCount(), interestedCount.intValue());
        assertEquals(sessionDTOData.getSessionStats().getRegisteredUserCount(), registerCount.intValue());
    }

    @Test
    void test_getSessionInfoById_withExpand_duration(){

	    //setup
        IdDurationDto idDurationDto = new IdDurationDto(session.getId(), 03.00);

        //mock
        when(sessionDetailsService.getSessionVideoDurationBySession(session)).thenReturn(idDurationDto.getDuration());
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "duration");

        //Assertion
        verify(sessionDetailsService).getSessionVideoDurationBySession(session);
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(sessionDTOData.getDuration(), idDurationDto.getDuration());
    }

    @Test
    void test_getSessionInfoById_withExpand_PLAYBACKS(){

	    //setup
        MuxAssetDTO muxAssetDTO = new MuxAssetDTO();
        muxAssetDTO.setPlayBackId("PlayBackId");
        muxAssetDTO.setFileName("fileName");
        muxAssetDTO.setDuration(03.00);
        muxAssetDTO.setCreatedAt(DateUtils.getCurrentDate());
        muxAssetDTO.setDefaultPlayback(true);
        muxAssetDTO.setAssetId("assetId");
        muxAssetDTO.setId(1L);

        //mock
        when(muxLivestreamAssetRepoService.getMuxAssetsByAssetTypeAndVisibleStatusAndSessionId(anyLong(), any(), anyBoolean())).thenReturn(Collections.singletonList(muxAssetDTO));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "PLAYBACKS");

        //Assertion
        verify(muxLivestreamAssetRepoService).getMuxAssetsByAssetTypeAndVisibleStatusAndSessionId(anyLong(), any(), anyBoolean());
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(sessionDTOData.getAssetList().get(0).getPlayBackId(), muxAssetDTO.getPlayBackId());
    }

    @Test
    void test_getSessionInfoById_withExpand_currentUserRegisteredEventTicketId(){

	    //setup
        UserSession userSession = new UserSession();
        userSession.setId(1L);
        userSession.setUserId(user.getUserId());
        userSession.setSession(session);
        userSession.setEventId(event.getEventId());
        userSession.setEventTicketId(1L);

        //mock
        when(userSessionService.getEventTicketIdsByEventIdAndUserIdAndSessionId(anyLong(), anyLong(), anyLong())).thenReturn(Collections.singletonList(userSession.getEventTicketId()));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "currentUserRegisteredEventTicketId");

        //Assertion
        verify(userSessionService).getEventTicketIdsByEventIdAndUserIdAndSessionId(anyLong(), anyLong(), anyLong());
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(sessionDTOData.getCurrentUserRegisteredEventTicketId().get(0), userSession.getEventTicketId());

    }

    @Test
    void test_getSessionInfoById_withExpand_purchaserUserRegisteredEventTicketId(){

	    //setup
        UserSession userSession = new UserSession();
        userSession.setId(1L);
        userSession.setUserId(user.getUserId());
        userSession.setSession(session);
        userSession.setEventId(event.getEventId());
        userSession.setEventTicketId(1L);

        //mock
        when(userSessionService.getEventTicketIdsByEventIdAndUserIdAndSessionPuchaserId(anyLong(), anyLong(), anyLong())).thenReturn(Collections.singletonList(userSession.getEventTicketId()));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, "purchaserUserRegisteredEventTicketId");

        //Assertion
        verify(userSessionService).getEventTicketIdsByEventIdAndUserIdAndSessionPuchaserId(anyLong(), anyLong(), anyLong());
        verify(sessionRepoService).getSessionByIdJoinFetchTagsTrack(anyLong(), any());
        assertEquals(sessionDTOData.getPurchaserUserRegisteredEventTicketId().get(0), userSession.getEventTicketId());
    }

    @Test
    void test_getSessionInfoById_withExpand_exhibitor(){

        //setup
        UserSession userSession = new UserSession();
        userSession.setId(1L);
        userSession.setUserId(user.getUserId());
        userSession.setSession(session);
        userSession.setEventId(event.getEventId());
        userSession.setEventTicketId(1L);

        session.setSponsorExhibitorJson("{}");
        //mock
        when(sponsorsService.getExhibitorIdsFromSponsorExhibitorJson(anyString())).thenReturn(Collections.singletonList(1L));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(anyLong(), any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        SessionDTO sessionDTOData = sessionServiceImpl.getSessionInfoById(session.getId(), event, user, Constants.EXHIBITOR_EXPAND+","+Constants.SPONSOR_EXPAND);

        //Assertion

        sessionServiceImpl.getSessionInfoById(session.getId(), event, user, Constants.EXHIBITOR_EXPAND);
    }

    @Test
    void test_getSessionInformationBySessionIds_withEmptySessionIds() {


        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);





        //Execution
        DataTableResponse dataTableResponse = sessionServiceImpl.getSessionInformationBySessionIds(emptyList(), event, "SESSION", user, "", true,true, null);

        //Assertion
        assertTrue(dataTableResponse.getData().isEmpty());
    }

    @Test
    void test_getSessionInformationBySessionIds_sessionInfoNotAvailable() {

        //setup
        Page<Session> page = new PageImpl<>(emptyList());

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);

        //mock
        when(sessionRepoService.findAllByEventIdAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE), null,false,true, null)).thenReturn(page);
        //Execution
        DataTableResponse dataTableResponse = sessionServiceImpl.getSessionInformationBySessionIds(Collections.singletonList(sessionId), event, "SESSION", null, null,false,true, null);

        //Assertion
        verify(sessionRepoService).findAllByEventIdAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE), null,false,true, null);

        assertTrue(dataTableResponse.getData().isEmpty());
    }

    @Test
    void test_getSessionInformationBySessionIds() {

        //setup
        Page<Session> page = new PageImpl<>(Collections.singletonList(session));

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);

        //mock
        when(sessionRepoService.findAllByEventIdAndSessionFormatAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE),"MAIN_STAGE",true,true,user, null)).thenReturn(page);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));

        //Execution
        DataTableResponse dataTableResponse = sessionServiceImpl.getSessionInformationBySessionIds(Collections.singletonList(sessionId), event, "SESSION", user, "MAIN_STAGE",true,true, null);

        //Assertion
        verify(sessionRepoService).findAllByEventIdAndSessionFormatAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE),"MAIN_STAGE",true , true,user, null);

        assertNotNull(dataTableResponse.getData().get(0));
    }

    //TODO: Mockito ReWrite
//    @Test
//    void test_getSpeakerTalks() {
//
//        //setup
//        Pageable pageable = PageRequest.of(1, 10);
//        Page<Session> page = new PageImpl<>(Collections.singletonList(session));
//
//        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
//        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());
//        when(ticketingDatesDto.getEventEndDate()).thenReturn(tomorrow.toDate());
//
//        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
//
//
//        //mock
//        when(sessionSpeakerService.getSessionByEventIdAndSpeakerUserId(any(), anyLong(), anyString(), any(), anyBoolean(), anyBoolean())).thenReturn(page);
//        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
//
//        //Execution
//        DataTableResponse dataTableResponse = sessionServiceImpl.getSpeakerTalks(event, user, null, pageable, false, null, false);
//
//        //Assertion
//        verify(sessionSpeakerService).getSessionByEventIdAndSpeakerUserId(any(), anyLong(), anyString(), any(), anyBoolean(), anyBoolean());
//
//        assertNotNull(dataTableResponse.getData().get(0));
//    }

    @Test
    void test_getMyRegisteredSessions_SessionFormatBlank(){

	    //setup
        SessionFilter sessionFilter = new SessionFilter("21,22", "51", "MAIN_STAGE", "abc", false,"abc", false);
        Page<Session> page = new PageImpl<>(Collections.singletonList(session));
        UserSession userSession = new UserSession();
        userSession.setId(1L);
        userSession.setUserId(user.getUserId());
        userSession.setSession(session);
        userSession.setEventId(event.getEventId());
        userSession.setEventTicketId(1L);

        //setup

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);




        //mock
        //when(userSessionService.findByEventIdAndUserId(anyLong(), anyLong())).thenReturn(Collections.singletonList(userSession.getId()));
        when(sessionRepoService.findAllByEventIdAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE), user,true,true, null)).thenReturn(page);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        when(sessionRepo.filterSessionByUserId(anyLong(),anyList(),anyString(),anyLong())).thenReturn(Collections.singletonList(sessionId));

        //Execution
        DataTableResponse dataTableResponse = sessionServiceImpl.getMyRegisteredSessions(event, "SESSION", user, "", true,true, null, sessionFilter);

        //Assertion
        verify(sessionRepo).filterSessionByUserId(anyLong(),anyList(),anyString(),anyLong());
        verify(sessionRepoService).findAllByEventIdAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE), user,true,true, null);

        assertFalse(dataTableResponse.getData().isEmpty());
    }

    @Test
    void test_getMyRegisteredSessions_SessionFormatMAIN_STAGE(){

        //setup
        SessionFilter sessionFilter = new SessionFilter("21,22", "51", "MAIN_STAGE", "abc", false,"abc", false);
        Page<Session> page = new PageImpl<>(Collections.singletonList(session));
        UserSession userSession = new UserSession();
        userSession.setId(1L);
        userSession.setUserId(user.getUserId());
        userSession.setSession(session);
        userSession.setEventId(event.getEventId());
        userSession.setEventTicketId(1L);

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);

        //mock
        //when(userSessionService.findByEventIdAndUserId(anyLong(), anyLong())).thenReturn(Collections.singletonList(userSession.getId()));
        when(sessionRepoService.findAllByEventIdAndSessionFormatAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE), EnumSessionFormat.MAIN_STAGE.name(),true,true ,user, null)).thenReturn(page);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        when(sessionRepo.filterSessionByUserId(anyLong(),anyList(),anyString(),anyLong())).thenReturn(Collections.singletonList(sessionId));
        //Execution
        DataTableResponse dataTableResponse = sessionServiceImpl.getMyRegisteredSessions(event, "SESSION", user, EnumSessionFormat.MAIN_STAGE.name(), true,true, null, sessionFilter);

        //Assertion
        verify(sessionRepo).filterSessionByUserId(anyLong(),anyList(),anyString(),anyLong());
        verify(sessionRepoService).findAllByEventIdAndSessionFormatAndIdIn(event, Collections.singletonList(sessionId), PageRequest.of(0, Integer.MAX_VALUE),EnumSessionFormat.MAIN_STAGE.name(),true ,true,user, null);

        assertFalse(dataTableResponse.getData().isEmpty());
    }

    @Test
    void test_updateDirectUploadIdAndFile(){

	    //setup
        String playBackRestrictionToken = "testToken";
        String thumbnailRestrictionToken = "thumbnilToken";
        String streamUrl = "StreamUrl";
        session.setStreamUrl(null);
        session.setStreamProvider(DIRECT_UPLOAD);
        //mock
        when(sessionRepoService.getSessionByIdWithoutCache(anyLong(), any())).thenReturn(session);

        //Execution
        Session sessionData = sessionServiceImpl.updateDirectUploadIdAndFile(session.getId(), streamUrl, DIRECT_UPLOAD, event, playBackRestrictionToken, thumbnailRestrictionToken);

        //Assertion
        verify(sessionRepoService).getSessionByIdWithoutCache(anyLong(), any());

        assertEquals(sessionData.getStreamUrl(), streamUrl);
        assertEquals(sessionData.getStreamProvider(), DIRECT_UPLOAD);
        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());

        Session updatedSessionData = sessionArgumentCaptor.getValue();
        assertEquals(updatedSessionData.getStreamUrl(), streamUrl);
    }

    @Test
    void test_findDefaultPlayBackForSession(){

	    //setup
        MuxAssetDTO muxAssetDTO = new MuxAssetDTO();
        muxAssetDTO.setPlayBackId("PlayBackId");
        muxAssetDTO.setFileName("fileName");
        muxAssetDTO.setDuration(03.00);
        muxAssetDTO.setCreatedAt(DateUtils.getCurrentDate());
        muxAssetDTO.setDefaultPlayback(true);
        muxAssetDTO.setAssetId("assetId");
        muxAssetDTO.setId(1L);

        //mock
        when(muxLivestreamAssetService.findDefaultPlayBackForSession(anyLong(), any())).thenReturn(muxAssetDTO);
        when(sessionRepoService.findById(session.getId())).thenReturn(Optional.empty());

        //Execution
        MuxAssetDTO muxAssetDTOData = sessionServiceImpl.findDefaultPlayBackForSession(session.getId(), AssetType.SESSION_ASSET);

        //Assertion
        verify(muxLivestreamAssetService).findDefaultPlayBackForSession(anyLong(), any());
        assertEquals(muxAssetDTOData.getPlayBackId(), muxAssetDTO.getPlayBackId());
        assertEquals(muxAssetDTOData.getDuration(), muxAssetDTO.getDuration(), 0);
    }

    @Test
    void test_setDefaultPlayBackForSession(){

	    //setup
        Long playBackId = 1L;
        session.setEndTime(DateUtils.getAddedHours(new Date(), -6));

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);

        Mockito.doNothing().when(muxLivestreamAssetService).markAssetAsDefaultPlayBackForSession(any(), anyLong(), any());

        //Execution
        sessionServiceImpl.setDefaultPlayBackForSession(session.getId(), playBackId, event);

        //Assertion
        verify(sessionRepoService).getSessionById(anyLong(), any());
        verify(muxLivestreamAssetService).markAssetAsDefaultPlayBackForSession(any(), anyLong(), any());
    }

    @Test
    void test_setDefaultPlayBackForSession_currentDateTimeBeforeSessionEndDateTime(){

        //setup
        Long playBackId = 1L;
        session.setEndTime(DateUtils.getAddedHours(new Date(), 6));

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> sessionServiceImpl.setDefaultPlayBackForSession(session.getId(), playBackId, event));

        //Assertion
        assertEquals(CAN_NOT_SET_DEFAULT_PLAYBACK.getDeveloperMessage(), exception.getMessage());
        verify(sessionRepoService).getSessionById(anyLong(), any());
    }

    @Test
    void test_getSessionsAnalyticsById(){

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);

        //Execution
        SessionDetailsAnalytics sessionDetailsAnalyticsData = sessionServiceImpl.getSessionsAnalyticsById(session.getId(), "", event);

        //Assertion
        verify(sessionRepoService).getSessionById(anyLong(), any());

        assertNull(sessionDetailsAnalyticsData.getTotalChatCount());
        assertNotNull(sessionDetailsAnalyticsData.getTotalRecordingViews());
        assertEquals(sessionDetailsAnalyticsData.getSessionId().longValue(), session.getId());
    }

    @Test
    void test_updateSessionTicketTypes(){

	    //setup
        TicketingType ticketingType = new TicketingType();
        ticketingType.setDataType(DataType.TICKET);
        ticketingType.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
        ticketingType.setId(2L);
        ticketingType.setTicketTypeName("General Admission");

        //mock
        when(sessionRepoService.findSessionByEventId(any())).thenReturn(Arrays.asList(session));

        //Execution
        sessionServiceImpl.updateSessionTicketTypes(ticketingType, Collections.EMPTY_LIST, event, true);

        //Assertion
        verify(sessionRepoService).findSessionByEventId(any());

        Class<ArrayList<Session>> listClass = (Class<ArrayList<Session>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<Session>> arrayListArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(sessionRepoService, times(1)).saveAll(arrayListArgumentCaptor.capture());

        List<Session> sessionData = arrayListArgumentCaptor.getAllValues().get(0);
        assertTrue(sessionData.get(0).getTicketTypesThatCanBeRegistered().contains("1,2"));
    }

    @Test
    void test_findSessionByEventId(){

	    //mock
        when(sessionRepoService.findSessionByEventId(any())).thenReturn(Collections.singletonList(session));

        //Execution
        List<Session> sessionList = sessionServiceImpl.findSessionByEventId(event);

        //Assertion
        assertEquals(sessionList.get(0).getId(), session.getId());
        assertEquals(sessionList.get(0).getEventId(), session.getEventId());
    }


    //TODO: Junit5 review test
   /* @Test
    void test_parseSessionCSV() throws IOException {

	    long currentTimeMillis = System.currentTimeMillis();

        //setup
        String headers = "Title,Format,Session Type,Location,Start Date,Start Time,End Time,Full Detail,Capacity,Short Description\n" +
                "Valid Meetup Demo Session new_"+currentTimeMillis+",MEET_UP,'HYBRID','New York',30/07/2029 ,07:00 ,08:00 ,dfhdfh,400,short description\n" +
                ",MAIN_STAGE_SESSION,'HYBRID',,30/07/2020 ,02:00_"+currentTimeMillis+" ,03:00 ,,100,short description\n" +
                "Valid MainStage Demo Session new_"+currentTimeMillis+",MAIN_STAGE_SESSION,'HYBRID',,30/07/2029, 02:00 ,03:00 ,,100,short description\n" +
                "Demo Session new_"+currentTimeMillis+",MAIN_STAGE_SESSION,'HYBRID',,30/07/2017, 02:00 ,03:00 ,,100,short description\n" +
                "null,null,'HYBRID',30/07/2018,02:00 ,03:00 ,null,\n"+
                "MainStage Demo Session new_"+currentTimeMillis+",MAIN_STAGE_SESSION,'HYBRID',,,,,,100,short description";
        MockMultipartFile file =
                new MockMultipartFile(
                        "file",
                        "test contract.csv",
                        MediaType.MULTIPART_FORM_DATA_VALUE,
                        headers.getBytes(StandardCharsets.UTF_8));

        Date date  = new Date();
        DateTime today = new DateTime(date);
        DateTime beforeDate = today.minusDays(4015);
        DateTime afterDate = today.plusDays(4015);

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(beforeDate.toDate());
        when(ticketingDatesDto.getEventEndDate()).thenReturn(afterDate.toDate());

        //mock
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);
        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();
        //Execution
        UploadSessionResponseContainer uploadSessionResponseContainerData = sessionServiceImpl.parseSessionCSV(file, event, user, languageMap);

        //Assertion
        assertEquals(2, uploadSessionResponseContainerData.getValidSession().size());
        assertTrue(uploadSessionResponseContainerData.getValidSession().get(0).getTitle().contains(String.valueOf(currentTimeMillis)));
        assertTrue(uploadSessionResponseContainerData.getValidSession().get(1).getTitle().contains(String.valueOf(currentTimeMillis)));
    }*/

    @Test
    void test_uploadSessionCSV_notValidSession(){

	    //setup
        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        UploadSessionDto uploadSessionDto = new UploadSessionDto();
        uploadSessionDto.setFormat(null);
        uploadSessionDto.setStartDateTime(dtfOut.print(today));
        uploadSessionDto.setEndDateTime(dtfOut.print(today));
        uploadSessionDto.setShortDescriptionOfSession("short description");
        UploadSessionDto[] sessionDtos = {uploadSessionDto};

        //mock
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);

        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

        //Execution
        UploadSessionResponseContainer uploadSessionResponseContainerData = sessionServiceImpl.uploadSessionCSVOrZapier(sessionDtos, event, false,false);

        //Assertion
        assertTrue(uploadSessionResponseContainerData.getValidSession().isEmpty());
    }

    @Test
    void test_uploadSessionCSVOrZapier(){

        //setup
        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");

        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);
        when(ticketingDatesDto.getEventStartDate()).thenReturn(yesterday.toDate());


        UploadSessionDto uploadSessionDto = new UploadSessionDto();
        uploadSessionDto.setFormat(EnumSessionFormat.MEET_UP);
        uploadSessionDto.setSessionId(session.getId());
        uploadSessionDto.setCapacity(100);
        uploadSessionDto.setDescription("Description");
        uploadSessionDto.setTitle("Demo session");
        uploadSessionDto.setStartDateTime(dtfOut.print(today));
        uploadSessionDto.setEndDateTime(dtfOut.print(today));
        uploadSessionDto.setShortDescriptionOfSession("short description");
        uploadSessionDto.setSessionTypeFormat(SessionTypeFormat.VIRTUAL);
        UploadSessionDto[] sessionDtos = {uploadSessionDto};

        session.setFormat(EnumSessionFormat.MEET_UP);

        //mock
        when(sessionRepoService.getAllSessionByIdsAndEventId(any(),any())).thenReturn(List.of(session));
        when(sessionRepoService.save(any())).thenReturn(session);
        Mockito.doNothing().when(networkingRulesService).createOrUpdateNetworkingRuleWhenCreatingSession(any(), any());
        when(ticketingRepository.findEventStartAndEndDateByEventid(any())).thenReturn(ticketingDatesDto);

        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();
        when(customFormAttributeService.saveCustomAttributeData(any(), any()))
                .thenReturn(new CustomFormAttributeData());

        //Execution
        UploadSessionResponseContainer uploadSessionResponseContainerData = sessionServiceImpl.uploadSessionCSVOrZapier(sessionDtos, event, true,false);

        //Assertion
        verify(networkingRulesService).createOrUpdateNetworkingRuleWhenCreatingSession(any(), any());
        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());
        Session sessionData = sessionArgumentCaptor.getValue();
        assertEquals(sessionData.getDescription(), uploadSessionDto.getDescription());
        assertEquals(sessionData.getTitle(), uploadSessionDto.getTitle());
        assertEquals(sessionData.getFormat(), uploadSessionDto.getFormat());
        assertEquals(sessionData.getCapacity(), uploadSessionDto.getCapacity());
        assertFalse(uploadSessionResponseContainerData.getValidSession().isEmpty());
    }

    @Test
    void test_joinSession_sessionNull(){

	    //setup
        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> sessionServiceImpl.joinSession(session.getId(), event, user));

        assertEquals(NotFoundException.SessionNotFound.SESSION_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_joinSession_withMeetingObjBlank(){

	    //setup
        Meeting meeting = new Meeting();
        meeting.setMeetingId("1");
        meeting.setMediaRegion("Ahmedabad");
        MeetingAttendeeDto meetingAttendeeDto = new MeetingAttendeeDto();
        meetingAttendeeDto.setAttendee(user.toString());
        meetingAttendeeDto.setMeeting(meeting.toString());

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);
        when(chimeService.createWorkshopAndJoinAttendee(any(), any(), any())).thenReturn(meetingAttendeeDto);

        //Execution
        MeetingAttendeeDto meetingAttendeeDtoData = sessionServiceImpl.joinSession(session.getId(), event, user);

        //Assertion
        verify(sessionRepoService).getSessionById(anyLong(), any());
        verify(chimeService).createWorkshopAndJoinAttendee(any(), any(), any());


        assertEquals(meetingAttendeeDtoData.getSessionId().longValue(), session.getId());
        assertEquals(meetingAttendeeDtoData.getTitle(), session.getTitle());

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());
        Session sessionData = sessionArgumentCaptor.getValue();
        assertEquals(sessionData.getMeetingObj(), meetingAttendeeDtoData.getMeeting());
    }

    @Test
    void test_clearMeeting_sessionNull(){

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(null);

        //Execution
        sessionServiceImpl.clearMeeting(session.getId(), event);

        //Assertion
        verify(sessionRepoService).getSessionById(anyLong(), any());
    }

    @Test
    void test_clearMeeting(){

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);

        //Execution
        sessionServiceImpl.clearMeeting(session.getId(), event);

        //Assertion
        verify(sessionRepoService).getSessionById(anyLong(), any());

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());
        Session sessionData = sessionArgumentCaptor.getValue();
        assertEquals(sessionData.getMeetingObj(), "");
    }

    @Test
    void test_duplicateSession(){

	    //setup
        SessionTagAndTrack sessionTagAndTrack = new SessionTagAndTrack();
        sessionTagAndTrack.setId(1L);
        sessionTagAndTrack.setSessionId(session.getId());
        sessionTagAndTrack.setTagOrTrackId(1L);
        sessionTagAndTrack.setRecordStatus(RecordStatus.CREATE);

        SessionSpeaker sessionSpeaker = new SessionSpeaker();
        sessionSpeaker.setSessionId(session.getId());
        sessionSpeaker.setPosition(1000);
        sessionSpeaker.setSpeakerId(1L);
        sessionSpeaker.setId(1L);
        sessionSpeaker.setRecordStatus(RecordStatus.CREATE);

        sessionSpeaker.setSpeaker(speaker);
        session.setSessionSpeakers(Collections.singleton(sessionSpeaker));
        session.setSessionTagAndTracks(Collections.singleton(sessionTagAndTrack));

        when(sessionRepoService.getSessionByIdWithoutCache(any(),any())).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(any())).thenReturn(Optional.of(sessionDetails));
        when(virtualEventService.isSpeakerInviteEnable(any())).thenReturn(Boolean.TRUE);
	    //mock


        //Execution
        Session sessionData = sessionServiceImpl.duplicateSession(session.getId(), event, user);

        //Assertion
        //verify(sessionRepoService).getSessionById(sessionId, session.getEvent());

        assertNotNull(sessionData.getTitle());

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());
        Session duplicatedSessionData = sessionArgumentCaptor.getValue();
        assertEquals(duplicatedSessionData.getSessionSpeakers(), EMPTY_SET);
        assertEquals(duplicatedSessionData.getSessionTagAndTracks(), EMPTY_SET);
        assertNull(duplicatedSessionData.getStreamUrl());
        assertNull(duplicatedSessionData.getRtmpUrl());
        assertNull(duplicatedSessionData.getLiveStreamId());
        assertNull(duplicatedSessionData.getStreamKey());

        Class<Set<SessionSpeaker>> speakerListClass = (Class<Set<SessionSpeaker>>)(Class)ArrayList.class;
        ArgumentCaptor<Set<SessionSpeaker>> sessionSpeakerArgumentCapture = ArgumentCaptor.forClass(speakerListClass);
        verify(sessionSpeakerService, times(1)).saveAll( sessionSpeakerArgumentCapture.capture());

        Set<SessionSpeaker> sessionSpeakerData = sessionSpeakerArgumentCapture.getAllValues().get(0);
        assertEquals(sessionSpeakerData.iterator().next().getSessionId().longValue(), duplicatedSessionData.getId());

        Class<Set<SessionTagAndTrack>> listClass = (Class<Set<SessionTagAndTrack>>)(Class)ArrayList.class;
        ArgumentCaptor<Set<SessionTagAndTrack>> sessionTagTrackArgumentCapture = ArgumentCaptor.forClass(listClass);
        verify(sessionTagAndTrackService, times(1)).saveAll( sessionTagTrackArgumentCapture.capture());

        Set<SessionTagAndTrack> sessionTagAndTracksData = sessionTagTrackArgumentCapture.getAllValues().get(0);
        assertEquals(sessionTagAndTracksData.iterator().next().getSessionId().longValue(), duplicatedSessionData.getId());
    }

    //@Test
    void test_duplicateSession_titleSizeGreaterThan50(){

        //setup
        session.setSessionSpeakers(Collections.emptySet());
        session.setSessionTagAndTracks(Collections.emptySet());
        session.setTitle("This is title info that contain more than fifty characters");

        //mock
        when(sessionRepoService.getSessionById(anyLong(), any())).thenReturn(session);

        //Execution
        Session sessionData = sessionServiceImpl.duplicateSession(session.getId(), event, user);

        //Assertion
        verify(sessionRepoService).getSessionById(anyLong(), any());

        assertEquals(sessionData.getTitle(), "COPYThis is title info that contain more");

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionRepoService).save(sessionArgumentCaptor.capture());
        Session duplicatedSessionData = sessionArgumentCaptor.getValue();
        assertTrue(duplicatedSessionData.getTitle().length() == 40);

        Class<Set<SessionSpeaker>> speakerListClass = (Class<Set<SessionSpeaker>>)(Class)ArrayList.class;
        ArgumentCaptor<Set<SessionSpeaker>> sessionSpeakerArgumentCapture = ArgumentCaptor.forClass(speakerListClass);
        verify(sessionSpeakerService, times(1)).saveAll( sessionSpeakerArgumentCapture.capture());

        Class<Set<SessionTagAndTrack>> listClass = (Class<Set<SessionTagAndTrack>>)(Class)ArrayList.class;
        ArgumentCaptor<Set<SessionTagAndTrack>> sessionTagTrackArgumentCapture = ArgumentCaptor.forClass(listClass);
        verify(sessionTagAndTrackService, times(1)).saveAll( sessionTagTrackArgumentCapture.capture());
    }

    @Test
    void test_getAllSessionDatesInEvent(){

	    //setup
        TimeZone timezone = TimeZone.getTimeZone("UTC");

        //mock
        when(sessionRepoService.findAllSessionDatesByEvent(any(), anyString(), anyBoolean())).thenReturn(Collections.singleton(new Date().toString()));

        //Execution
        Set<String> allSessionDates = sessionServiceImpl.getAllSessionDatesInEvent(event, (long) timezone.getOffset(Calendar.ZONE_OFFSET), false);

        //Assertion
        verify(sessionRepoService).findAllSessionDatesByEvent(any(), anyString(), anyBoolean());
        assertEquals(allSessionDates.size(),1);
    }

    @Test
    void test_getAllCommandCenterSessionList() {
        //Setup
        Pageable pageable = PageRequest.of(1, 10);
        Page<Session> page = new PageImpl<>(Collections.singletonList(session));
        SessionFilterCommandCenter sessionFilter = new SessionFilterCommandCenter();
        sessionFilter.setSessionFormat(EnumSessionFormat.MAIN_STAGE.name());
        TicketingDatesDto ticketingDatesDto = mock(TicketingDatesDto.class);



        //Mock
        when(sessionRepoService.getCommandCenterAllSessionByEventId(event,sessionFilter,pageable)).thenReturn(page);

        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        //Execution
        DataTableResponse session = sessionServiceImpl.getCommandCenterSessionList(event, user, pageable, sessionFilter, "SESSION");

        //Assertion
        assertNotNull(session.getData().get(0));
    }

    @Test
    void test_sessionAnalytics(){
        SessionVideoAnalyticsData sessionVideoAnalyticsData = new SessionVideoAnalyticsData(1L);
	    sessionVideoAnalyticsData.setLiveViews(10);
	    sessionVideoAnalyticsData.setRecordingViews(10);
	    sessionVideoAnalyticsData.setAvgAttendedDuration(10);
	    sessionVideoAnalyticsData.setAvgRecordingDuration(10);
	    sessionVideoAnalyticsData.setHour("10");
	    sessionVideoAnalyticsData.setUserCount(10);
        Map<Long, SessionVideoAnalyticsData> videoWatchTimeMap = new HashMap<Long, SessionVideoAnalyticsData>(){{
            put(session.getId(),sessionVideoAnalyticsData);
        }};
        Page<Session> page = PageUtil.getPageable(Collections.singletonList(session), PageRequest.of(0, 10));

	    when(videoAnalyticsService.getVideoWatchTimeAndRecordingViews(event)).thenReturn(videoWatchTimeMap);
	    when(videoAnalyticsService.getVideoWatchTimeAndVideoViews(event)).thenReturn(videoWatchTimeMap);
        when(sessionRepoService.getAllHostSessionByEventId(any(), any(), any(), anyBoolean(), eq(null), eq(Collections.emptyList()),anyList(),anyBoolean(),anyBoolean())).thenReturn(page);
        DataTableResponse dataTableResponse = sessionServiceImpl.getSessionsAnalytics("", "SPEAKER,ASSETS,SPONSOR,videoAnalytics,EXHIBITOR,STATS,watchTime,watchTimeViews,recordingViews,registerCount,TAG,sessionSpeakerCount", event,PageRequest.of(0, 10));


        sessionServiceImpl.getDisplayPortalSessionDataTable(event,user,"currentUserRegisteredEventTicketId,",page,new PastUpComingDto());

        List<SessionAnalyticsDto> sessionAnalyticsDtos = sessionServiceImpl.getSessionsOverviewReportData(event);
        assertNotNull(dataTableResponse);
    }

    @Test
    void test_joinSession(){
	    session.setSponsorExhibitorJson("{}");
	    when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(any(),any())).thenReturn(session);
	    when(eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user)).thenReturn(Collections.singletonList(1L));
	    when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        DisplayPortalCommonSessionDTO displayPortalCommonSessionDTO = sessionServiceImpl.getDisplayPortalSessionInfoById(sessionId,event,user,"STATS,SESSION_EXPAND.ATTENDEE_COUNT,Constants.EXHIBITOR_EXPAND,Constants.SPONSOR_EXPAND,registerdHolderUsers,currentUserRegisteredEventTicketId,PLAYBACKS", Boolean.FALSE);

        assertNotNull(displayPortalCommonSessionDTO);
    }
    @Test
    void test_joinSession_STAFF_NOT_REGISTER_IN_SESSION(){
        session.setSponsorExhibitorJson("{}");
        session.setTicketTypesThatCanBeRegistered("0");



        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(any(),any())).thenReturn(session);
        when(eventTicketsRepoService.getEventTicketTypeIdsByEventUserANDNotCanceled(event, user)).thenReturn(Collections.singletonList(1L));

        when(speakerService.isSpeakerInEvent(event, user)).thenReturn(true);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> sessionServiceImpl.getDisplayPortalSessionInfoById(sessionId,event,user,"STATS,SESSION_EXPAND.ATTENDEE_COUNT,Constants.EXHIBITOR_EXPAND,Constants.SPONSOR_EXPAND,registerdHolderUsers,currentUserRegisteredEventTicketId,PLAYBACKS", Boolean.FALSE));

       assertEquals(NotAcceptableException.SessionSpeakerExceptionMsg.STAFF_NOT_REGISTER_IN_SESSION.getErrorMessage(), exception.getMessage());
    }

    void test_getSessionDropdownList(){

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> sessionServiceImpl.getSessionDropDownList(event,List.of(MAIN_STAGE)));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_SESSION_FORMAT.getDeveloperMessage(), exception.getMessage());

        assertNotNull(sessionServiceImpl.getSessionDropDownList(event, List.of(MAIN_STAGE)));
    }

    @Test
    void updateSessionSequence(){
	    session.setPosition((double) 100);
        DateTimeFormatter dtfOut = DateTimeFormat.forPattern("yyyy/MM/dd HH:mm");
        session.setStartTime(new Date());
        session.setEndTime(new Date());
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(sessionId, event)).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.empty());
        when(sessionRepoService.getSessionById(sessionId, event)).thenReturn(session);
        when(sessionRepoService.findById(sessionId)).thenReturn(Optional.of(session));
        when(sessionRepoService.findById(sessionId+1)).thenReturn(Optional.empty());


        sessionServiceImpl.updateSessionSequence(sessionId,sessionId,sessionId,event);
        sessionServiceImpl.updateSessionSequence(sessionId,sessionId+1,sessionId,event);
        sessionServiceImpl.updateSessionSequence(sessionId,sessionId,sessionId+1,event);

        assertNotNull(sessionServiceImpl.getSessionInfoById(sessionId,event,user,""));

    }

    @Test
    void test_sortSession(){
	    when(sessionRepoService.getSessionsByStartTimeAndEndTimeUnique(event.getEventId())).thenReturn(Collections.singletonList(session));
	    when(sessionRepoService.getSessionsByTimeAndSortType(anyLong(),any(),any(), anyString())).thenReturn(Collections.singletonList(session));
        when(sessionRepoService.getSessionByIdJoinFetchTagsTrack(sessionId, event)).thenReturn(session);
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));


	    sessionServiceImpl.sortSessionPositionByTitle(event.getEventId(),"ASC");
	    sessionServiceImpl.sortSessionPositionByTitle(event.getEventId(),"DESC");
        assertNotNull(sessionServiceImpl.getSessionInfoById(sessionId,event,user,""));

    }
    @Test
    void test_exception(){

        IdNameDto idNameDto = new IdNameDto();
        idNameDto.setId(1L);
	    sessionDTO.setTags(Collections.singletonList(idNameDto));
        sessionDTO.setTicketTypesThatCanBeRegistered(Collections.singletonList(1L));

        SessionTagAndTrack sessionTagAndTrack = new SessionTagAndTrack();
        sessionTagAndTrack.setId(1L);
        sessionTagAndTrack.setSessionId(session.getId());
        sessionTagAndTrack.setTagOrTrackId(1L);
        sessionTagAndTrack.setRecordStatus(RecordStatus.CREATE);

        session.setSessionTagAndTracks(Collections.singleton(sessionTagAndTrack));

        when(ticketingTypeTagAndTrackRepoService.findByTicketingTypeIdAndTagOrTrackIdIn(any(), any())).thenReturn(Collections.singletonList(ticketingTypeTagAndTrack));

        try {

            Method method = SessionServiceImpl.class.getDeclaredMethod("raiseErrorIfRestrictedTicketTypeSelected", Session.class, SessionDTO.class);
            method.setAccessible(true);
            method.invoke(sessionServiceImpl, session, sessionDTO);
        } catch ( InvocationTargetException notAcceptableException){
            assertEquals("Ticket Type(s)  restricted for this session tag and/or tracks.",notAcceptableException.getTargetException().getMessage());
        }
        catch (Exception ex){
            //ignored
        }
    }

    @Test
    void test_getDisplayPortalSessionList(){
        Page<Session> page = new PageImpl<>(Collections.singletonList(session));
        when(sessionRepoService.getAllSessionByEventId(any(), any(),any(),any(), anyBoolean(), anyString())).thenReturn(page);
        assertNotNull(sessionServiceImpl.getDisplayPortalSessionList("",event,user,PageRequest.of(1, 10),new SessionFilter(),true,true,"test"));
        SessionFilter sessionFilter = new SessionFilter("","","MAIN_STAGE","",null,"",Boolean.TRUE);
        assertNotNull(sessionServiceImpl.getDisplayPortalSessionList("",event,user,PageRequest.of(1, 10),sessionFilter,true,null,"test"));
    }

    @Test
    void test_getFutureSessionExpoLoungesByEvent(){
        when(sessionRepoService.getUpcomingSessionByEvent(any(),any())).thenReturn(Collections.singletonList(new SessionIdNameTypeDto(sessionId,session.getTitle(),session.getFormat())));
        when(exhibitorService.getExhibitorDropDown(any())).thenReturn(Collections.singletonList(new ExhibitorDropDownDto(1L,"Test")));
        when(neptuneNetworkingLoungeService.getEventNetworkingLounges(event)).thenReturn(Collections.singletonList(new NetworkingLounge(new HashMap<>())));

        assertNotNull(sessionServiceImpl.getFutureSessionExpoLoungesByEvent(event));
    }

    @Test
    void test_getFutureSessionExpoLoungesByEventAndSessionId(){
	    when(sessionRepoService.getSessionById(anyLong(),any())).thenReturn(session);
        when(sessionRepoService.getUpcomingSessionByEventAndEndDateOfCurrentSession(any(),any())).thenReturn(Collections.singletonList(new SessionIdNameTypeDto(sessionId,session.getTitle(),session.getFormat())));
        when(exhibitorService.getExhibitorDropDown(any())).thenReturn(Collections.singletonList(new ExhibitorDropDownDto(1L,"Test")));
        when(neptuneNetworkingLoungeService.getEventNetworkingLounges(event)).thenReturn(Collections.singletonList(new NetworkingLounge(new HashMap<>())));

        assertNotNull(sessionServiceImpl.getFutureSessionExpoLoungesByEventAndSessionId(event,1L));
    }

    @Test
    void test_startOrStopWorkshopRecording(){
        WorkshopRecordingAssetDetails workshopRecordingAssetDetails = new WorkshopRecordingAssetDetails();
        workshopRecordingAssetDetails.setId(1L);
        workshopRecordingAssetDetails.setSessionId(1L);
        workshopRecordingAssetDetails.setEventId(1L);
        workshopRecordingAssetDetails.setFileLocation("file Location");
        workshopRecordingAssetDetails.setFileName("file Name");
        workshopRecordingAssetDetails.setDefaultPlayback(Boolean.TRUE);
        workshopRecordingAssetDetails.setPipelineId("PipeLine Id");
        workshopRecordingAssetDetails.setAttendeeCount("attendeeCount");
        workshopRecordingAssetDetails.setPlayBackId("playBackId");
        workshopRecordingAssetDetails.setDuration(20.5);
        workshopRecordingAssetDetails.setAssetStatus(WorkshopRecordingAssetDetails.ProcessStatus.COMPLETED);


        EnumSessionFormat oldFormat = session.getFormat();
        session.setFormat(WORKSHOP);

        when(sessionRepoService.findById(sessionId)).thenReturn(Optional.of(session));
        when(sessionDetailsRepoService.findBySessionId(sessionId)).thenReturn(Optional.of(sessionDetails));
        when(workshopRecordingAssetsRepoService.findBySessionIdAndAttendeeCountIsNotNull(any())).thenReturn(Optional.of(workshopRecordingAssetDetails));

        //Execution
	    sessionServiceImpl.startOrStopWorkshopRecording(sessionId,event,START);

        sessionServiceImpl.startOrStopWorkshopRecording(sessionId,event,STOP);
	    session.setFormat(oldFormat);
    }
    @Test
    void test_getSortedSessionWithDateAndTime(){
        when(sessionRepo.getAllSortedSessionWithDateAndTimeForHost(any(),any())).thenReturn(new PageImpl<>(Collections.singletonList(session)));
        when(sessionDetailsService.getSessionDetailsBySession(any())).thenReturn(Optional.empty());
        assertNotNull(sessionServiceImpl.getSortedSessionWithDateAndTime("video",event,PageRequest.of(0,10),"title","ASC"));
        assertNotNull(sessionServiceImpl.getSortedSessionWithDateAndTime("video",event,PageRequest.of(0,10),null,"DESC"));

    }
    @Test
    void test_miscellaneousMethods(){
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();
	    ChimeConfigDto chimeConfigDto = new ChimeConfigDto();
        when(sessionRepoService.getSessionById(any(),any())).thenReturn(session);
        when(sessionDetailsService.getChimeConfigDetails(session)).thenReturn(chimeConfigDto);
        when(sessionRepoService.getNonVisualSessionsByEvent(event.getEventId(), Arrays.asList(BREAK, OTHER, EXPO))).thenReturn(10L);
        sessionServiceImpl.updateChimeConfigDetails(sessionId,event,user,chimeConfigDto);
        ChimeConfigDto chimeConfigDto1 = sessionServiceImpl.getChimeConfigDetails(sessionId,event);

        sessionServiceImpl.checkNonVisualSessionCount(MAIN_STAGE,event, BREAK,true);
        assertEquals(chimeConfigDto,chimeConfigDto1);

        sessionServiceImpl.removeRecordedSessionVideo(1L);
        when(sessionRepoService.getSessionCountByEventId(event.getEventId())).thenReturn(0L);
        sessionServiceImpl.setAgendaAddedChecklistFlag(event);

        when(roEventService.getLanguageCodeByUserOrEvent(user, event)).thenReturn(EnumLabelLanguageCode.EN.toString());
        when(sessionRepoService.getSessionByIdWithoutCache(sessionId, event)).thenReturn(session);
        KeyValueDto keyValueDto = new KeyValueDto("key","value");

        sessionServiceImpl.updateSessionDocumentBySpeaker(event,user,sessionId,Collections.singletonList(keyValueDto));

        try {
            ResponseDto responseDto = sessionServiceImpl.sessionMassOperation(event, user, EnumSessionMassOperation.DELETE, Collections.singletonList(sessionId),languageMap, new SessionMassOperationConfigDTO(), new MockHttpServletRequest(), testAppsyncAuthToken);
            assertNotNull(responseDto.getMessage());
            session.setSessionSpeakers(Collections.emptySet());
            when(sessionRepoService.getSessionByIdWithoutCache(sessionId, event)).thenReturn(session);
            when(ticketingRepository.findEventStartAndEndDateByEventid(event)).thenReturn(new TicketingDatesDto(new DateTime().minusHours(2).toDate(),new DateTime(new Date()).plusDays(1).toDate()));
            when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
            assertNotNull(sessionServiceImpl.sessionMassOperation(event, user, EnumSessionMassOperation.DUPLICATE, Collections.singletonList(sessionId),languageMap, new SessionMassOperationConfigDTO(), new MockHttpServletRequest(), testAppsyncAuthToken));
            assertNotNull(sessionServiceImpl.sessionMassOperation(event, user, EnumSessionMassOperation.HIDE, Collections.singletonList(sessionId),languageMap, new SessionMassOperationConfigDTO(), new MockHttpServletRequest(), testAppsyncAuthToken));


            Pageable pageable = PageRequest.of(1, 10);
            Page<Session> page = new PageImpl<>(Collections.singletonList(session));

            when(sessionRepoService.getAllCommandCenterSessionList(event, pageable,user,false)).thenReturn(page);
            assertNotNull(sessionServiceImpl.getAllCommandCenterSessionList("SPEAKER,ASSETS",event,user, pageable, false));

            when(sessionRepoService.getSessionByEventIds(any())).thenReturn(Collections.singletonList(session));
            sessionServiceImpl.resetSessionDates(Collections.singletonList(event.getEventId()),1);

            when(workshopRecordingAssetService.generateWorkshopRecordingAssetPreSignedUrl(any())).thenReturn("Test");
            assertEquals(sessionServiceImpl.createWorkshopRecordAssetPlaybackToken(1L),"Test");

            when(sessionRepoService.findSessionByEventIdOrderByIdAsc(event.getEventId())).thenReturn(Collections.singletonList(session));
            assertNotNull(sessionServiceImpl.findSessionByEventIdOrderByIdAsc(event.getEventId()));

            //execution
            sessionServiceImpl.getSessionIdAndTitleById(Collections.singletonList( sessionId));
        }
        catch (JSONException e){
            //Ignored
        }
    }

    @Test
    void test_speakerRegisterSessionCount(){
        when(userSessionService.getRegisteredSessionCountForUser(event.getEventId(), user.getUserId())).thenReturn(0L);
        when(userSessionService.getRegisteredSessionCountForUser(event.getEventId(), user.getUserId())).thenReturn(5L);
        when(userSessionService.getRegisteredSessionCountForUserBySessionFormat(event.getEventId(), user.getUserId(),BREAKOUT_SESSION)).thenReturn(5L);

        assertNotNull(sessionServiceImpl.speakerRegisterSessionCount(event,user, BREAKOUT_SESSION));
        assertNotNull(sessionServiceImpl.speakerRegisterSessionCount(event,user, null));
        assertNotNull(sessionServiceImpl.speakerRegisterSessionCount(event,null, null));

    }

    @Test
    void test_sessionOrLoungeOrExpoExistingOrUpcoming(){
        when(sessionDetailsRepoService.findBySessionId(sessionId)).thenReturn(Optional.of(sessionDetails));
        assertNotNull(sessionServiceImpl.sessionOrLoungeOrExpoExistingOrUpcoming(event, user, sessionId, false));
        assertNotNull(sessionServiceImpl.sessionOrLoungeOrExpoExistingOrUpcoming(event, user, sessionId, true));
    }

    void test_updateSubTitleFileInSession_success() {
        String captionFileName = "captionFile.vtt";
        String captionFileFullUrl = "fullCaptionFile.vtt";
        String assetId = "assetId";
        String languageCode = "en-US";

        // MOCK
        when(sessionDetailsService.getSessionDetailsBySession(session)).thenReturn(Optional.of(sessionDetails));
        when(muxLivestreamAssetRepoService.findBySessionIdAndAssetId(session.getId(), assetId)).thenReturn(Optional.empty());

        ArgumentCaptor<MUXLivestreamAssetDetails> sessionArgumentCaptor = ArgumentCaptor.forClass(MUXLivestreamAssetDetails.class);
        MUXLivestreamAssetDetails sessionData = sessionArgumentCaptor.getValue();

        sessionServiceImpl.updateSubTitleFileInSession(session,sessionData, captionFileName, captionFileFullUrl, languageCode);

        verify(muxLivestreamAssetRepoService).save(sessionArgumentCaptor.capture());
    }
    
    @Test
    void updateSessionsStatusInBulk_WhenUpdatingToVisible_WithValidSessions_ShouldSucceed() {
        // Arrange
        List<Long> sessionIds = Arrays.asList(1L, 2L);
        List<Session> sessions = createTestSessions(sessionIds, BREAKOUT_SESSION);
        when(sessionRepoService.getAllSessionByIdsAndEventId(sessionIds, event)).thenReturn(sessions);
        when(sessionRepoService.findByEventAndFormat(eq(event.getEventId()), anyList()))
            .thenReturn(Collections.emptyList());
        
        // Set up argument captor
        ArgumentCaptor<List<Session>> sessionsCaptor = ArgumentCaptor.forClass(List.class);

        // Act
        BulkSessionStatusUpdateResultDto result = sessionServiceImpl.updateSessionsStatusInBulk(
            event, user, sessionIds, EnumSessionStatus.VISIBLE);

        // Assert
        assertEquals(2, result.getUpdatedSessionIds().size());
        assertTrue(result.getFailedSessionIds().isEmpty());
        
        // Verify saveAll was called with the expected sessions
        verify(sessionRepoService).saveAll(sessionsCaptor.capture());
        List<Session> savedSessions = sessionsCaptor.getValue();
        assertEquals(2, savedSessions.size());
        assertTrue(savedSessions.stream().allMatch(s -> s.getStatus() == EnumSessionStatus.VISIBLE));
    }

    @Test
    void updateSessionsStatusInBulk_WhenUpdatingToDraft_WithNoRegistrations_ShouldSucceed() {
        // Arrange
        List<Long> sessionIds = Arrays.asList(1L, 2L);
        List<Session> sessions = createTestSessions(sessionIds, BREAKOUT_SESSION);
        when(sessionRepoService.getAllSessionByIdsAndEventId(sessionIds, event)).thenReturn(sessions);
        when(userSessionRepoService.getUserRegisteredSessionsBySessionIds(sessionIds, event))
            .thenReturn(Collections.emptyMap());
        
        // Set up argument captor
        ArgumentCaptor<List<Session>> sessionsCaptor = ArgumentCaptor.forClass(List.class);

        // Act
        BulkSessionStatusUpdateResultDto result = sessionServiceImpl.updateSessionsStatusInBulk(
            event, user, sessionIds, EnumSessionStatus.DRAFT);

        // Assert
        assertEquals(2, result.getUpdatedSessionIds().size());
        assertTrue(result.getFailedSessionIds().isEmpty());
        
        // Verify saveAll was called with the expected sessions
        verify(sessionRepoService).saveAll(sessionsCaptor.capture());
        List<Session> savedSessions = sessionsCaptor.getValue();
        assertEquals(2, savedSessions.size());
        assertTrue(savedSessions.stream().allMatch(s -> s.getStatus() == EnumSessionStatus.DRAFT));
    }

    @Test
    void updateSessionsStatusInBulk_WhenUpdatingToDraft_WithExistingRegistrations_ShouldFail() {
        // Arrange
        List<Long> sessionIds = Arrays.asList(1L, 2L);
        List<Session> sessions = createTestSessions(sessionIds, BREAKOUT_SESSION);
        Map<Long, Integer> registrations = new HashMap<>();
        registrations.put(1L, 1); // Session 1 has registrations
        
        when(sessionRepoService.getAllSessionByIdsAndEventId(sessionIds, event)).thenReturn(sessions);
        when(userSessionRepoService.getUserRegisteredSessionsBySessionIds(sessionIds, event))
            .thenReturn(registrations);

        // Act
        BulkSessionStatusUpdateResultDto result = sessionServiceImpl.updateSessionsStatusInBulk(
            event, user, sessionIds, EnumSessionStatus.DRAFT);

        // Assert
        assertEquals(1, result.getUpdatedSessionIds().size()); // Only session 2 should be updated
        assertEquals(1, result.getFailedSessionIds().size()); // Session 1 should fail
        assertEquals(1L, result.getFailedSessionIds().get(0));
    }

    @Test
    void updateSessionsStatusInBulk_WhenUpdatingToVisible_WithMainStageConflict_ShouldFail() {
        // Arrange
        List<Long> sessionIds = Arrays.asList(1L, 2L);
        List<Session> sessions = createTestSessions(sessionIds, MAIN_STAGE);
        
        // Set up non-overlapping times for the first session
        Date now = new Date();
        sessions.get(0).setStartTime(now);
        sessions.get(0).setEndTime(new Date(now.getTime() + 3600000)); // 1 hour later
        
        // Set up overlapping time for the second session
        sessions.get(1).setStartTime(now);
        sessions.get(1).setEndTime(new Date(now.getTime() + 3600000)); // Same time as first session
        
        // Mock repository to return sessions
        when(sessionRepoService.getAllSessionByIdsAndEventId(eq(sessionIds), eq(event))).thenReturn(sessions);
        
        // Mock to return existing main stage sessions (including the ones we're trying to update)
        when(sessionRepoService.findByEventAndFormat(eq(event.getEventId()), anyList()))
            .thenReturn(Collections.singletonList(sessions.get(0))); // Only return first session as existing

        // Act
        BulkSessionStatusUpdateResultDto result = sessionServiceImpl.updateSessionsStatusInBulk(
            event, user, sessionIds, EnumSessionStatus.VISIBLE);

        // Assert - Only one session should be updated (the first one)
        assertEquals(1, result.getUpdatedSessionIds().size(), "Should have 1 updated session");
        assertEquals(1, result.getFailedSessionIds().size(), "Should have 1 failed session");
        
        // Verify which session was updated and which failed
        assertEquals(1L, result.getUpdatedSessionIds().get(0), "First session should be updated");
        assertEquals(2L, result.getFailedSessionIds().get(0), "Second session should fail due to time conflict");
        
        // Verify saveAll was called with only the valid session (ID 1)
        verify(sessionRepoService).saveAll(argThat(list -> 
            list.size() == 1 && list.get(0).getId() == 1L));
    }

    @Test
    void updateSessionsStatusInBulk_WhenUpdatingToVisible_WithInvalidSession_ShouldFail() {
        // Arrange
        List<Long> sessionIds = Arrays.asList(1L, 2L);
        List<Session> sessions = createTestSessions(sessionIds, BREAKOUT_SESSION);
        // Make session 1 invalid by setting required fields to null
        Session invalidSession = sessions.get(0);
        invalidSession.setTitle(null);
        invalidSession.setStartTime(null);
        invalidSession.setEndTime(null);
        
        // Set up mocks
        when(sessionRepoService.getAllSessionByIdsAndEventId(eq(sessionIds), eq(event))).thenReturn(sessions);
        when(sessionRepoService.findByEventAndFormat(eq(event.getEventId()), anyList()))
            .thenReturn(Collections.emptyList());

        // Act
        BulkSessionStatusUpdateResultDto result = sessionServiceImpl.updateSessionsStatusInBulk(
            event, user, sessionIds, EnumSessionStatus.VISIBLE);

        // Assert - Verify the results
        assertEquals(1, result.getUpdatedSessionIds().size(), "Should have 1 updated session");
        assertEquals(2L, result.getUpdatedSessionIds().get(0), "Session 2 should be updated");
        
        assertEquals(1, result.getFailedSessionIds().size(), "Should have 1 failed session");
        assertEquals(1L, result.getFailedSessionIds().get(0), "Session 1 should be in failed list");
        
        // Verify saveAll was called with only the valid session (ID 2)
        verify(sessionRepoService).saveAll(argThat(list -> 
            list.size() == 1 && list.get(0).getId() == 2L));
    }

    @Test
    void updateSessionsStatusInBulk_WhenNoSessionsFound_ShouldReturnEmptyLists() {
        // Arrange
        List<Long> sessionIds = Arrays.asList(1L, 2L);
        when(sessionRepoService.getAllSessionByIdsAndEventId(sessionIds, event))
            .thenReturn(Collections.emptyList());

        // Act
        BulkSessionStatusUpdateResultDto result = sessionServiceImpl.updateSessionsStatusInBulk(
            event, user, sessionIds, EnumSessionStatus.VISIBLE);

        // Assert
        assertTrue(result.getUpdatedSessionIds().isEmpty());
        assertTrue(result.getFailedSessionIds().isEmpty());
    }

    private List<Session> createTestSessions(List<Long> ids, EnumSessionFormat format) {
        return ids.stream().map(id -> {
            Session session = new Session();
            session.setId(id);
            session.setTitle("Test Session " + id);
            session.setStartTime(new Date());
            session.setEndTime(new Date(System.currentTimeMillis() + 3600000)); // 1 hour later
            session.setFormat(format);
            session.setStatus(EnumSessionStatus.DRAFT);
            return session;
        }).collect(Collectors.toList());
    }

    @Test
    void getAllSessionsByEvent() {
        // MOCK
        when(sessionRepoService.getAllSessionsByEventId(event.getEventId())).thenReturn(List.of(sessionBasicDetailsDTO));

        DataTableResponse response = sessionServiceImpl.getAllSessionsListByEvent(event);

        assertEquals(1, response.getRecordsTotal());
        assertEquals(1, response.getRecordsFiltered());
        assertEquals(1, response.getData().size());
    }
    
    

}