package com.accelevents.session_speakers.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EnumCommandCenterFilter;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionDetails;
import com.accelevents.domain.session_speakers.SessionSpeaker;
import com.accelevents.domain.session_speakers.SessionTagAndTrack;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.session_speakers.dto.SessionDTO;
import com.accelevents.session_speakers.dto.SessionFilterCommandCenter;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.session_speakers.repo.SessionRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;


@ExtendWith(MockitoExtension.class)
public class SessionRepoServiceImplTest {

    @Spy
    @InjectMocks
    private SessionRepoServiceImpl sessionRepoServiceImpl = new SessionRepoServiceImpl();

    @Mock
    private SessionRepo sessionRepo;


    private SessionDTO sessionDTO;
    private Event event;
    private User user;
    private Session session;
    private SessionTagAndTrack sessionTagAndTrack;
    private SpeakerDTO speakerDTO;
    private SessionDetails sessionDetails;
    private Long sessionId = 1L;
    private double position = 1000;
    private org.joda.time.DateTime today, yesterday, tomorrow;

    @BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();

        Long speakerId = 1L;
        SessionSpeaker sessionSpeaker = new SessionSpeaker(sessionId, speakerId, position, false);
        sessionSpeaker.setId(1L);

        session = EventDataUtil.getSession();
        session.setId(sessionId);

        sessionDTO = EventDataUtil.getSessionDto();

        speakerDTO = EventDataUtil.getSpeakerDto();

        sessionTagAndTrack = EventDataUtil.getSessionTagAndTrack();

        sessionDetails = new SessionDetails();
        sessionDetails.setId(1L);
        Date date = new Date();
        today = new org.joda.time.DateTime(date);
        yesterday = today.minusDays(1);
        tomorrow = today.plus(1);

    }

    @Test
    void test_getCommandCenterAllSessionByEventId_AllSessions_NoTagsTracks() {
        //Setup
        Pageable pageable = PageRequest.of(1, 10);
        SessionFilterCommandCenter sessionFilter = new SessionFilterCommandCenter();
        sessionFilter.setEnumCommandCenterFilter(null);
        sessionFilter.setSessionFormatList(Collections.singletonList(EnumSessionFormat.MAIN_STAGE.name()));
        sessionFilter.setTagOrTrackIds(Collections.emptyList());
        List<Long> filterSessionIds = new ArrayList<>();
        filterSessionIds.add(1L);
        List<Session> sessions = new ArrayList<>();
        sessions.add(session);

        //Mock
        Mockito.when(sessionRepo.getSessionByEvent(event.getEventId())).thenReturn(sessions);

        //Execution
        Page<Session> commandCenterAllSessionByEventId = sessionRepoServiceImpl.getCommandCenterAllSessionByEventId(event, sessionFilter, pageable);

        //Assertion
        assertNotNull(commandCenterAllSessionByEventId.getPageable().first());
    }


    @Test
    void test_getCommandCenterAllSessionByEventId_AllSession_TagsTracks() {
        //Setup
        Pageable pageable = PageRequest.of(1, 10);
        SessionFilterCommandCenter sessionFilter = new SessionFilterCommandCenter();
        sessionFilter.setEnumCommandCenterFilter(null);
        sessionFilter.setSessionFormatList(Collections.singletonList(EnumSessionFormat.MAIN_STAGE.name()));
        sessionFilter.setTagOrTrackIds(Collections.singletonList(1L));
        sessionFilter.setSearch("");
        List<Long> filterSessionIds = new ArrayList<>();
        filterSessionIds.add(1L);
        List<Session> sessions = new ArrayList<>();
        sessions.add(session);

        //Mock
        Mockito.when(sessionRepo.filterSessionWithoutSearch(event.getEventId(), sessionFilter.getTagOrTrackIds())).thenReturn(filterSessionIds);
        Mockito.when(sessionRepo.getSessionByEvent(event.getEventId())).thenReturn(sessions);

        //Execution
        Page<Session> commandCenterAllSessionByEventId = sessionRepoServiceImpl.getCommandCenterAllSessionByEventId(event, sessionFilter, pageable);

        //Assertion
        assertNotNull(commandCenterAllSessionByEventId.getPageable().first());

    }

    @Test
    void test_getCommandCenterAllSessionByEventId_EnumCommandCenterFilter_Upcoming() {
        //Setup
        Pageable pageable = PageRequest.of(1, 10);
        SessionFilterCommandCenter sessionFilter = new SessionFilterCommandCenter();
        sessionFilter.setEnumCommandCenterFilter(null);
        sessionFilter.setSessionFormatList(Collections.singletonList(EnumSessionFormat.MAIN_STAGE.name()));
        sessionFilter.setTagOrTrackIds(Collections.singletonList(1L));
        sessionFilter.setEnumCommandCenterFilter(EnumCommandCenterFilter.UPCOMING);
        sessionFilter.setSearch("");
        List<Long> filterSessionIds = new ArrayList<>();
        filterSessionIds.add(1L);
        List<Session> sessions = new ArrayList<>();
        sessions.add(session);

        //Mock
        Mockito.when(sessionRepo.filterSessionWithoutSearch(event.getEventId(), sessionFilter.getTagOrTrackIds())).thenReturn(filterSessionIds);
        Mockito.when(sessionRepo.getSessionByEvent(event.getEventId())).thenReturn(sessions);

        //Execution
        Page<Session> commandCenterAllSessionByEventId = sessionRepoServiceImpl.getCommandCenterAllSessionByEventId(event, sessionFilter, pageable);

        //Assertion
        assertNotNull(commandCenterAllSessionByEventId.getPageable().first());

    }

    @Test
    void test_getCommandCenterAllSessionByEventId_EnumCommandCenterFilter_Ended() {
        //Setup
        Pageable pageable = PageRequest.of(1, 10);
        SessionFilterCommandCenter sessionFilter = new SessionFilterCommandCenter();
        sessionFilter.setEnumCommandCenterFilter(null);
        sessionFilter.setSessionFormatList(Collections.singletonList(EnumSessionFormat.MAIN_STAGE.name()));
        sessionFilter.setTagOrTrackIds(Collections.singletonList(1L));
        sessionFilter.setEnumCommandCenterFilter(EnumCommandCenterFilter.ENDED);
        sessionFilter.setSearch("");
        List<Long> filterSessionIds = new ArrayList<>();
        filterSessionIds.add(1L);
        List<Session> sessions = new ArrayList<>();
        sessions.add(session);

        //Mock
        Mockito.when(sessionRepo.filterSessionWithoutSearch(event.getEventId(), sessionFilter.getTagOrTrackIds())).thenReturn(filterSessionIds);
        Mockito.when(sessionRepo.getSessionByEvent(event.getEventId())).thenReturn(sessions);

        //Execution
        Page<Session> commandCenterAllSessionByEventId = sessionRepoServiceImpl.getCommandCenterAllSessionByEventId(event, sessionFilter, pageable);

        //Assertion
        assertNotNull(commandCenterAllSessionByEventId.getPageable().first());
    }
}