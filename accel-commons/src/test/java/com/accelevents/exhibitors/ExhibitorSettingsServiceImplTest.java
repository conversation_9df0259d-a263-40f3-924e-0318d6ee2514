package com.accelevents.exhibitors;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.exhibitors.ExhibitorSetting;
import com.accelevents.exhibitors.dto.ExpoSettingsDetails;
import com.accelevents.exhibitors.services.ExhibitorSettingsRepoService;
import com.accelevents.exhibitors.services.impl.ExhibitorSettingsServiceImpl;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.impl.EventDataUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static com.accelevents.utils.Constants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ExhibitorSettingsServiceImplTest {

	@Spy
	@InjectMocks
	private ExhibitorSettingsServiceImpl exhibitorSettingsService = new ExhibitorSettingsServiceImpl();

	@Mock private ExhibitorSettingsRepoService repoService;
	@Mock private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock private TicketingHelperService ticketingHelperService;

	private Event event;
	private Long exhibitorId = 1L;

	@BeforeEach
	void setUp() throws Exception {
		MockitoAnnotations.openMocks(this);

		event = EventDataUtil.getEvent();
	}

	@Test
	void getOrCreateExhibitorSettingsWhenSettingsNotExists(){

		ExhibitorSetting exhibitorSettingGlobal = getExhibitorSetting();
		ExhibitorSetting saveExhibitorSettings = getExhibitorSetting();
		saveExhibitorSettings.setExhibitorId(exhibitorId);

		when(repoService.getGlobalConfigRecordByEventId(event.getEventId())).thenReturn(exhibitorSettingGlobal);
		when(repoService.getExhibitorRecord(exhibitorId)).thenReturn(null);
		when(repoService.save(any())).thenReturn(saveExhibitorSettings);

		ExhibitorSetting exhibitorSetting = exhibitorSettingsService.getOrCreateExhibitorSettings(exhibitorId, event);

		verify(repoService).save(any());
		assertEquals(exhibitorSetting.getExhibitorId(), exhibitorId);
		assertNull(exhibitorSetting.getTicketHolderIdsJson());
		assertNull(exhibitorSetting.getProfileQuestionsJson());
	}

	public ExhibitorSetting getExhibitorSetting() {
		ExhibitorSetting exhibitorSettingGlobal = new ExhibitorSetting();
		exhibitorSettingGlobal.setGlobal(true);
		exhibitorSettingGlobal.setEventId(event.getEventId());
		exhibitorSettingGlobal.setId(1L);
		return exhibitorSettingGlobal;
	}

	@Test
	void getOrCreateExhibitorSettingsWhenSettingsExists(){

		ExhibitorSetting exhibitorSettingInput = new ExhibitorSetting();
		exhibitorSettingInput.setExhibitorId(1L);
		exhibitorSettingInput.setTicketHolderIdsJson("1,2");
		when(repoService.getExhibitorRecord(exhibitorId)).thenReturn(exhibitorSettingInput);

		ExhibitorSetting exhibitorSetting = exhibitorSettingsService.getOrCreateExhibitorSettings(exhibitorId, event);

		verify(repoService, times(0)).save(any());
		assertEquals(exhibitorSetting.getExhibitorId(), exhibitorId);
		assertEquals(exhibitorSetting.getTicketHolderIdsJson(), exhibitorSettingInput.getTicketHolderIdsJson());
		assertEquals(exhibitorSetting.getProfileQuestionsJson(), exhibitorSettingInput.getProfileQuestionsJson());
	}

	@Test
	void prepareExpoSettingsDetails(){

		ExhibitorSetting exhibitorSettingInput = new ExhibitorSetting();
		exhibitorSettingInput.setExhibitorId(exhibitorId);
		exhibitorSettingInput.setTicketHolderIdsJson("1,2,3");
        Ticketing ticketing = new Ticketing();
        ticketing.setEventid(event);
        ticketing.setCollectTicketHolderAttributes(true);

		List<TicketHolderRequiredAttributes> holderAttributeList = new ArrayList<>();
		holderAttributeList.add(getTicketHolderAttributes(FIRST_NAME,1,true));
		holderAttributeList.add(getTicketHolderAttributes(LAST_NAME,2,true));
		holderAttributeList.add(getTicketHolderAttributes(EMAIL,3,true));
		holderAttributeList.add(getTicketHolderAttributes(PHONE_NUMBER,4,false));

        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesHavingEnableForHolderOrderByAttributeOrder(event,null,DataType.TICKET)).thenReturn(holderAttributeList);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
		List<ExpoSettingsDetails> expoSettingsDetailsList = exhibitorSettingsService.prepareExpoSettingsDetails(event,exhibitorSettingInput);

		System.out.println(expoSettingsDetailsList.size());


	}

	public TicketHolderRequiredAttributes getTicketHolderAttributes(String name, long id, boolean enabledForHolder) {
		TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
		attributes.setId(id);
		attributes.setName(name);
		attributes.setEventid(event);
		attributes.setEnabledForTicketPurchaser(!enabledForHolder);
		attributes.setEnabledForTicketHolder(enabledForHolder);
		return attributes;
	}
}


