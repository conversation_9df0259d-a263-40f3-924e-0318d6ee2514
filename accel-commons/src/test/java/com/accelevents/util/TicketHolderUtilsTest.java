package com.accelevents.util;

import com.accelevents.domain.EventTickets;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.User;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.dto.TicketHolderDetailDto;
import com.accelevents.dto.ValueDto;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.accelevents.dto.TicketHolderUtils.prepareDataList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class TicketHolderUtilsTest
{
    @Mock
    TicketAttributeValueDto1 ticketAttributeValueDto = new TicketAttributeValueDto1();

    @Mock
    EventTickets eventTickets;

    @BeforeEach
    void setup() {
        eventTickets = EventDataUtil.getEventTickets();
    }
    private TicketHolderRequiredAttributes getTicketHolderRequiredAttributes() {
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName(Constants.STRING_CELL_SPACE_PHONE);
        return ticketHolderRequiredAttributes;
    }

    @Test
    void test_get_cell_number()
    {
        String validatePhoneNumber ="********";
        User user = new User();
        user.setFirstName("test");
        user.setLastName("attendee");
        user.setEmail("<EMAIL>");
        user.setCountry("US");
        user.setPhoneNumber(78563);
        eventTickets.setTicketPurchaserId(user);
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(Constants.STRING_PHONE_NUMBER);
        keyValueDto.setValue("78563");
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(keyValueDto);
        ValueDto valueDto = new ValueDto();
        valueDto.setAttributes(attributeKeyValueDtos);

        Map<String, String> holderAtt = new HashMap<>();
        holderAtt.put(keyValueDto.getKey(), keyValueDto.getValue());

        Map<String, Map<String, String>> holder = new HashMap<>();
        holder.put(Constants.TICKETING.ATTRIBUTES, holderAtt);

        ticketAttributeValueDto.setHolder(holder);
        List<String> ticketHolderData = new ArrayList<>();

        //Purchaser attribute
        AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute.setKey(Constants.STRING_PHONE_NUMBER);
        purchaserAttributeKeyValueDtoAttribute.setValue("78563");
        attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(purchaserAttributeKeyValueDtoAttribute);
        valueDto = new ValueDto();
        valueDto.setAttributes(attributeKeyValueDtos);

        Map<String, String> purchaserAtt = new HashMap<>();
        purchaserAtt.put(purchaserAttributeKeyValueDtoAttribute.getKey(), purchaserAttributeKeyValueDtoAttribute.getValue());

        Map<String, Map<String, String>> purchaser = new HashMap<>();
        purchaser.put(Constants.TICKETING.ATTRIBUTES, purchaserAtt);


        ticketAttributeValueDto.setPurchaser(purchaser);

        prepareDataList(eventTickets,ticketAttributeValueDto,ticketHolderData,getTicketHolderRequiredAttributes(),"",Boolean.FALSE,Boolean.FALSE,new TicketHolderDetailDto(),Boolean.FALSE,Boolean.FALSE);

        assertTrue(ticketHolderData.size() > 0);

        assertEquals(validatePhoneNumber,ticketHolderData.get(0).trim());
    }


}
