package com.accelevents.util;

import com.accelevents.domain.TicketingType;
import com.accelevents.dto.SalesTaxFeeDto;
import com.accelevents.dto.StripeDTO;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.utils.FeePerTicket;
import com.accelevents.utils.GeneralUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class FeePerTicketTest {

    FeePerTicket feePerTicket = null;
    StripeDTO stripe = null;
    SalesTaxFeeDto salesTaxFeeDto;
    double capAmount=0;
    double vatTax = 0;

    @BeforeEach
    void setup() {
        stripe = new StripeDTO();
        stripe.setCCPercentageFee(2.9);
        stripe.setCCFlatFee(0.3);

        salesTaxFeeDto = new SalesTaxFeeDto();
        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setSalesTaxRate(0d);
    }

    @Test
    void testPassFeeAndPassTaxToBuyerForIndividualTicketType() {
        double price = 100;

        TicketingType ticketingType = TestFeeDataUtil.createTicketTypeIndv(price, true);
        setSalesTaxFeeDtoObject(ticketingType, false);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount, vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(118.47999999999999,feePerTicket.getTotalPayable(), 0.001);
        assertEquals(10.741503604531411,feePerTicket.getSalesTaxFee(), 0.001);
        assertEquals(4.0, feePerTicket.getAeFee(),0);
        assertEquals(4.0, feePerTicket.getServiceFee(),0);
        assertEquals(3.74, feePerTicket.getProcessingFee(),0.001);

        //assertEquals(GeneralUtils.getRoundValue(price+feePerTicket.getSalesTaxFee()), GeneralUtils.getRoundValue(feePerTicket.getExpectedStripeNetSale()),0);
        //assertEquals(price, GeneralUtils.getRoundValue(feePerTicket.getActualNetSale()),0);
        assertEquals(GeneralUtils.getRoundValue(price+feePerTicket.getSalesTaxFee()), GeneralUtils.getRoundValue(feePerTicket.getExpectedStripeNetSale()),0.1);
        assertEquals(price, GeneralUtils.getRoundValue(feePerTicket.getActualNetSale()),0.1);
    }

    private void setSalesTaxFeeDtoObject(TicketingType ticketingType, boolean b) {
        salesTaxFeeDto.setAbsorbTax(b);
        salesTaxFeeDto.setSalesTaxRate(10d);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(ticketingType.getId()));
    }

    @Test
    void testPassFeeAndAbsorbTaxToBuyerForIndividualTicketType() {
        double price = 100;

        TicketingType ticketingType = TestFeeDataUtil.createTicketTypeIndv(price, true);
        setSalesTaxFeeDtoObject(ticketingType, true);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount,vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(107.42,feePerTicket.getTotalPayable(), 0.001);
        assertEquals(9.765003276846741,feePerTicket.getSalesTaxFee(), 0.001);
        assertEquals(4.0, feePerTicket.getAeFee(),0);

        System.out.println(feePerTicket.getSalesTaxFee());
    }

    @Test
    void testAbsorbFeeAndAbsorbTaxToBuyerForIndividualTicketType() {
        double price = 100;

        TicketingType ticketingType = TestFeeDataUtil.createTicketTypeIndv(price, false);
        setSalesTaxFeeDtoObject(ticketingType, true);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount,vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(100,feePerTicket.getTotalPayable(), 0.001);
        assertEquals(9.09,feePerTicket.getSalesTaxFee(), 0.001);
        assertEquals(4.0, feePerTicket.getAeFee(),0);
        System.out.println(feePerTicket.getSalesTaxFee());
    }

    @Test
    void testAbsorbFeeAndPassTaxToBuyerForIndividualTicketType() {
        double price = 100;

        TicketingType ticketingType = TestFeeDataUtil.createTicketTypeIndv(price, false);
        setSalesTaxFeeDtoObject(ticketingType, false);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount,vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(110,feePerTicket.getTotalPayable(), 0.001);
        assertEquals(10,feePerTicket.getSalesTaxFee(), 0.001);
        assertEquals(4.0, feePerTicket.getAeFee(),0);
        System.out.println(feePerTicket.getSalesTaxFee());
    }

    @Test
    void testPassFeeToBuyerForIndividualTicketType() {
        //50, 53.35
        double price = 50;

        TicketingType ticketingType = TestFeeDataUtil.createTicketTypeIndv(price, true);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount, vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(54.38, feePerTicket.getTotalPayable(), 0);
        System.out.println(feePerTicket.getSalesTaxFee());
    }

    @Test
    void testPassFeeToBuyerForIndividualTicketTypeWithWLAFee() {
        //100, 105.36
        double price = 100;

        TicketingType ticketingType = TestFeeDataUtil.createTicketTypeIndv(price, true);
        ticketingType.setWlAFeeFlat(0.85);
        ticketingType.setWlAFeePercentage(1);
        ticketingType.setAeFeePercentage(1);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 2, 1, false,capAmount, vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(107.11, feePerTicket.getTotalPayable(), 0);
        assertEquals(0.0, feePerTicket.getSalesTaxFee(), 0);
    }

    @Test
    void testAbsorbFeeToBuyerForIndividualTicketType() {
        //50, 50
        double price = 50;

        TicketingType ticketingType = TestFeeDataUtil.createTicketTypeIndv(price, false);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount, vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(price, feePerTicket.getTotalPayable(),0);
    }

    @Test
    void testPassFeeToBuyerForTableTicketType() {
        //50, 57.5
        double price = 50;

        TicketingType ticketingType = TestFeeDataUtil.createTicketType(price, true, TicketBundleType.TABLE, 5);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount, vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(58.5, feePerTicket.getTotalPayable(), 0);
    }

    @Test
    void testAbsorbFeeToBuyerForTableTicketType() {
        //50, 50
        double price = 50;

        TicketingType ticketingType = TestFeeDataUtil.createTicketType(price, false, TicketBundleType.TABLE, 4);

        feePerTicket = new FeePerTicket(ticketingType,ticketingType.isPayLater(), stripe, salesTaxFeeDto, price, 1, 1, false,capAmount, vatTax,FeePerTicket.PLATEFORM_FEES_TYPE.FULL).invoke();

        assertEquals(price, feePerTicket.getTotalPayable(), 0);
    }
}
