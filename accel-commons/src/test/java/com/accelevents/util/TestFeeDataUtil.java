package com.accelevents.util;

import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingCoupon;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.DiscountType;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.ticketing.dto.TicketingCouponDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;

import java.util.Calendar;

import static com.accelevents.utils.FeeConstants.*;

public class TestFeeDataUtil {



    public static TicketingType createTicketTypeIndv(double price,
                                                 boolean passFeeToBuyer){
        return createTicketType(price, passFeeToBuyer, TicketBundleType.INDIVIDUAL_TICKET,1);
    }
    public static TicketingType createTicketType(double price,
                                                 boolean passFeeToBuyer,
                                                 TicketBundleType bundleType,
                                                 int numberOfTicketPerTable){
        return createTicketType(new Ticketing(),
                Calendar.getInstance(),
                price,
                "General Admission",
                passFeeToBuyer,
                bundleType,
                numberOfTicketPerTable);
    }

    public static TicketingType createTicketType(Ticketing ticketing,
                                                 Calendar calTicketType,
                                                 double price,
                                                 String ticketTypeName,
                                                 boolean passFeeToBuyer,
                                                 TicketBundleType bundleType,
                                                 int numberOfTicketPerTable){
        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketing(ticketing);
        ticketingType.setStartDate(calTicketType.getTime());
        calTicketType.add(Calendar.DATE, 30);
        ticketingType.setEndDate(calTicketType.getTime());
        ticketingType.setNumberOfTickets(120);
        ticketingType.setPrice(price);
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setMaxTicketsPerBuyer(5);
        ticketingType.setPassfeetobuyer(passFeeToBuyer);
        ticketingType.setAeFeeFlat(AE_FLAT_FEE_ONE);
        ticketingType.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
        ticketingType.setBundleType(bundleType);
        ticketingType.setNumberOfTicketPerTable(numberOfTicketPerTable);
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setEnableTicketDescription(false);
        return ticketingType;
    }

    public static TicketingType createTicketType(Ticketing ticketing,
                                                 Calendar calTicketType,
                                                 double price,
                                                 String ticketTypeName,
                                                 boolean passFeeToBuyer) {
        return createTicketType(ticketing,calTicketType,price,ticketTypeName,passFeeToBuyer, TicketBundleType.INDIVIDUAL_TICKET, 0);
    }

    public static TicketingCouponDto createTicketingCoupon(TicketingType ticketingType,
                                                           Event event,
                                                           String couponCode,
                                                           Calendar calTicketType,
                                                           double discountAmount,
                                                           DiscountType discountType) {
        TicketingCouponDto ticketingCoupon = new TicketingCouponDto();
        ticketingCoupon.setAmount(discountAmount);

        calTicketType.add(Calendar.DATE, -1);
        ticketingCoupon.setCouponStartDate(DateUtils.getDateString(calTicketType.getTime(), Constants.DATE_FORMAT));
        calTicketType.add(Calendar.DATE, 30);
        ticketingCoupon.setCouponEndDate(DateUtils.getDateString(calTicketType.getTime(), Constants.DATE_FORMAT));
        ticketingCoupon.setDiscountType(discountType);
        ticketingCoupon.setEventTicketTypeId(String.valueOf(ticketingType.getId()));
        ticketingCoupon.setCouponCode(couponCode);
        ticketingCoupon.setUses(-1l);
        ticketingCoupon.setUsesPerUser(-1l);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
        return ticketingCoupon;

    }
}
