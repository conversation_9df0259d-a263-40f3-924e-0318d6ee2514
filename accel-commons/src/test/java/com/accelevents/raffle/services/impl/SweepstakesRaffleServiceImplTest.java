package com.accelevents.raffle.services.impl;

import com.accelevents.domain.*;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.notification.services.InboundMessageService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.SubmittedRaffleTicketService;
import com.accelevents.services.UserService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SweepstakesRaffleServiceImplTest {
	
	@Spy
	@InjectMocks
	private SweepstakesRaffleServiceImpl sweepstakesRaffleServiceImpl;
	
	@Mock
	private InboundMessageService inboundMessageService;
	@Mock
	private TextMessageUtils textMessageUtils;
	@Mock
	private UserService userService;
    @Mock
    private ROUserService roUserService;
	@Mock
	SubmittedRaffleTicketService submittedRaffleTicketService;

	@Test
	void testServiceRequestForSweepstakesException(){
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("testBody", aePhoneNumber, event);

		when(inboundMessageService.getInboundMessagesByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId())).thenThrow(new RuntimeException());
		
		sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		
		verify(textMessageUtils).getSweepstakesAlreadyBeenEnteredMessage();
	}
	
	@Test
	void testServiceRequestForSweepstakesNamePresent() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName("James");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("test Body msg", aePhoneNumber, event);

		List<InboundMessage> inboundMessages = new ArrayList<InboundMessage>();
		inboundMessages.add(new InboundMessage());
		
		when(inboundMessageService.getInboundMessagesByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId())).thenReturn(inboundMessages);

		boolean isNamePresent = true;
		when(userService.nameIsPresent(twilioMessage.getBody())).thenReturn(isNamePresent);

		String msg = "testMsg";
		when(userService.processNameTextForSweepstakes(twilioMessage, raffle, user)).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testServiceRequestForSweepstakesNameNotPresent() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName("here");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("test Body msg", aePhoneNumber, event);

		List<InboundMessage> inboundMessages = new ArrayList<InboundMessage>();
		inboundMessages.add(new InboundMessage());
		
		when(inboundMessageService.getInboundMessagesByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId())).thenReturn(inboundMessages);

		boolean isNamePresent = false;
		when(userService.nameIsPresent(twilioMessage.getBody())).thenReturn(isNamePresent);

		String msg = "testMsg";
		when(textMessageUtils.getSweepStaksBadNameMessage()).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testServiceRequestForSweepstakesForEmail() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName(null);
//		user.setEmail("<EMAIL>");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("<EMAIL> Body msg", aePhoneNumber, event);

		List<InboundMessage> inboundMessages = new ArrayList<InboundMessage>();
		when(inboundMessageService.getInboundMessagesByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId())).thenReturn(inboundMessages);

		Optional<User> ou = Optional.empty();
		when(roUserService.getUserByEmail(twilioMessage.getBody())).thenReturn(ou);
		
		String msg = "testMsg";
		when(textMessageUtils.getSweepStakesSchoolMessage(user.getFirstName())).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testServiceRequestForSweepstakesForInboundSize1() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("test Body msg", aePhoneNumber, event);

		List<InboundMessage> inboundMessages = new ArrayList<InboundMessage>();
		inboundMessages.add(new InboundMessage());
		
		when(inboundMessageService.getInboundMessagesByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId())).thenReturn(inboundMessages);

		String msg = "testMsg";
		when(userService.processNameTextForSweepstakes(twilioMessage, raffle, user)).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testServiceRequestForSweepstakesForFirstNameNotBlankAndEmailNotBlankAndHasCustomAttrSchool() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName("James");
		user.setEmail("<EMAIL>");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("Body msg", aePhoneNumber, event);

		boolean isNamePresent = true;
		when(userService.hasCustomAttribute(twilioMessage.getEvent(), user, "school")).thenReturn(isNamePresent);
		
		List<SubmittedRaffleTicket> submittedRaffleTicketsByUser = new ArrayList<SubmittedRaffleTicket>();
		when(submittedRaffleTicketService.findByUserAndRaffleId(user, raffle.getId())).thenReturn(submittedRaffleTicketsByUser);
		
		sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		verify(submittedRaffleTicketService).findByUserAndRaffleId(user, raffle.getId());
	}
	
	@Test
	void testServiceRequestForSweepstakesForFirstNameBlankAndBodyNamePresent() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName(null);
		user.setEmail("<EMAIL>");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("Body msg", aePhoneNumber, event);

		boolean isNamePresent = true;
		when(userService.nameIsPresent(twilioMessage.getBody())).thenReturn(isNamePresent);

		String msg = "testMsg";
		when(userService.processNameTextForSweepstakes(twilioMessage, raffle, user)).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testServiceRequestForSweepstakesForFirstNameBlankAndBodyNameNotPresent() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName(null);
		user.setEmail("<EMAIL>");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("Body msg", aePhoneNumber, event);

		boolean isNamePresent = false;
		when(userService.nameIsPresent(twilioMessage.getBody())).thenReturn(isNamePresent);

		String msg = "testMsg";
		when(textMessageUtils.getSweepStaksBadNameMessage()).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testServiceRequestForSweepstakesForFirstNameNotBlankAndEmailBlank() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName("James");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("Body msg", aePhoneNumber, event);

		String msg = "testMsg";
		when(textMessageUtils.getSweepStakesSchoolMessage(user.getFirstName())).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testServiceRequestForSweepstakesForFirstNameNotBlankAndEmailNotBlank() {
		
		Raffle raffle = new Raffle();
		User user = new User();
		user.setUserId(1L);
		user.setFirstName("James");
		user.setEmail("<EMAIL>");
		Event event = new Event();
		event.setEventId(2L);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage("Body msg", aePhoneNumber, event);

		String msg = "testMsg";
		when(textMessageUtils.getServiceRequestForSchoolMessage(twilioMessage.getEvent(), user, twilioMessage.getBody())).thenReturn(msg);
		
		String sweepStakesMessage = sweepstakesRaffleServiceImpl.serviceRequestForSweepstakes(raffle, twilioMessage, user);
		assertEquals(msg, sweepStakesMessage);
	}
	
	@Test
	void testIsStateForNoStateScenario(){
		
		String message = "123";
		boolean state = sweepstakesRaffleServiceImpl.isState(message);
		
		assertFalse(state);
		
		
		message = "12";
		state = sweepstakesRaffleServiceImpl.isState(message);
		assertFalse(state);
	}
	
	@Test
	void testIsStateForStateExistScenario(){
		
		String message = "AL";
		boolean state = sweepstakesRaffleServiceImpl.isState(message);
		
		assertTrue(state);
	}
	
	@Test
	void testProcessSweepstakesEmailAddress(){
		
		String body = "EMAIL";
		Event event = new Event();
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body , from, event );
		User user = new User();
		String firstName = "FIRST_NAME";
		user.setFirstName(firstName);
		
		String expectedmessage = "EXPECTED_MESSAGE";
		when(textMessageUtils.getSweepStakesSchoolMessage(user.getFirstName())).thenReturn(expectedmessage);
		String message = sweepstakesRaffleServiceImpl.processSweepstakesEmailAddress(twilioMessage, user);
		
		assertEquals(expectedmessage, message);
		
		ArgumentCaptor<User> captor = ArgumentCaptor.forClass(User.class);
		verify(userService).save(captor.capture());
		
		assertEquals(twilioMessage.getBody(), captor.getValue().getEmail());
	}
	
}
