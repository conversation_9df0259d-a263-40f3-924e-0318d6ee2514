package com.accelevents.notification.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.Currency;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.services.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TwilioTextMessagePrepareServiceImplTest {

	@Spy
	@InjectMocks
	private TwilioTextMessagePrepareServiceImpl textMessagePrepareService;

	@Mock
	private TextMessageService mockTextMessageService;
	@Mock
	private ItemService mockItemService;
	@Mock
	private EventService mockEventService;
	@Mock
	private TextMessageUtils mockTextMessageUtils;
	@Mock
	private AuctionService mockAuctionService;
	@Mock
	private AuctionCustomSmsService auctionCustomSmsService;

	@Test
	void testSendOutBidNotificationWhenCurrentBidderIsNull() {
		Event event = new Event();
		Item item = new Item();
		User user = new User();
		User currentHighBidder = null;
		textMessagePrepareService.sendOutBidNotification(event, item, currentHighBidder, 0, false);

		verifyZeroInteractions(mockTextMessageService);
		verifyZeroInteractions(mockItemService);
		verifyZeroInteractions(mockEventService);

	}

	@Test
	void testSendOutBidNotificationWhenCurrentBidderIsCurrentUser() {
		Event event = new Event();
		Item item = new Item();
		User user = new User();
		item.setCurrentHighBidder(user);
		User currentHighBidder = user;
		textMessagePrepareService.sendOutBidNotification(event, item, currentHighBidder, 0, false);

		verifyZeroInteractions(mockTextMessageService);
		verifyZeroInteractions(mockItemService);
		verifyZeroInteractions(mockEventService);

	}

	@Test
	void testSendOutBidNotificationWhenCurrentBidderIsNotNullAndDifferentThenUserWithBidDifferenceZero() {

        when(mockTextMessageUtils.getLooserOutBidNotificationMessage(anyDouble(), anyDouble(), anyString(), anyString(),
                anyString(), isA(Event.class), anyBoolean(),anyBoolean(),anyLong(),anyBoolean(), isA(User.class))).thenReturn("testString");
		Event event = new Event();
		event.setCurrency(Currency.USD);
		Item item = new Item();
		item.setCode("TST");
        item.setItemShortName("short");
		User user = new User("<EMAIL>", 8888888888L);

		User currentHighBidder = new User("<EMAIL>", 9999999999L);
		item.setCurrentHighBidder(currentHighBidder);

		when(mockAuctionService.findByEvent(event)).thenReturn(new Auction());
		doReturn(0d).when(mockItemService).getBidIncrement(any(), any());

		textMessagePrepareService.sendOutBidNotification(event, item, user, 10, false);
		verify(mockTextMessageService).sendText(any(TextMessage.class));

	}

	@Test
	void testSendAttendeeCheckInTextMessage() {

		Event event = new Event();
		event.setPhoneNumber(1L);
		event.setCountryCode(CountryCode.IN);
		AccelEventsPhoneNumber aePhoneNumber = event.getAePhoneNumber();
		User checkInUser = new User();
		checkInUser.setUserId(1L);
		String baseUrl = "BASE_URL";

		String checkInMsg = "CHECKIN_MESSAGE";
		when(auctionCustomSmsService.getValueBysmsKeyAndEvent(any(),any(),any())).thenReturn(checkInMsg);
		when(mockTextMessageUtils.capitalizeFirstCharacter(checkInMsg)).thenReturn(checkInMsg);
		textMessagePrepareService.sendAttendeeCheckInTextMessage(event, checkInUser, checkInUser);

		ArgumentCaptor<TextMessage> captor = ArgumentCaptor.forClass(TextMessage.class);

		verify(mockTextMessageService).sendText(captor.capture());

		TextMessage textMessage = captor.getValue();

		assertEquals(textMessage.getMessage(), checkInMsg);
		assertEquals(textMessage.getBody(), checkInMsg);
		assertEquals(textMessage.getSender(), aePhoneNumber);
		assertEquals(textMessage.getReceiver(), checkInUser);
	}
}
