package com.accelevents.helpers;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.BiddingSource;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.ModuleStatus;
import com.accelevents.messages.EnumDonationSMS;
import com.accelevents.messages.EnumRaffleSMS;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.impl.AuctionCustomSmsServiceImpl;
import com.accelevents.services.impl.CauseAuctionCustomSmsServiceImpl;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.services.impl.RaffleCustomSmsServiceImpl;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static com.accelevents.messages.EnumAuctionSMS.WELCOME_BIDDER_NUMBER_SMS_DONATION;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TextMessageUtilsTest {

    @Spy
    @InjectMocks
    private TextMessageUtils textMessageUtils;
	@Mock
	private DonationCustomSmsService donationCustomSmsService;
	@Mock
	private AutoLoginService autoLoginService;
	@Mock
	private URLShortnerService urlShortnerService;
	@Mock
	private UserService userService;
    @Mock
    private ROUserService roUserService;
	@Mock
	private PaymentService paymentService;
	@Mock
	private AuctionBidService auctionBidService;
	@Mock
	private PurchasedRaffleTicketService purchasedRaffleTicketService;
	@Mock
	private RaffleCustomSmsServiceImpl raffleCustomSmsServiceImpl;
	@Mock
	private AuctionCustomSmsServiceImpl auctionCustomSmsServiceImpl;
	@Mock
	private CauseAuctionCustomSmsServiceImpl causeAuctionCustomSmsServiceImpl;
	@Mock CommonUtil commonUtil;
    @Mock
    private ROEventService roEventService;

    private BidderNumber bidderNumber;
    private Event event;
    private User user;

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        bidderNumber = new BidderNumber();
        long bidderNumbers = 1L;
        bidderNumber.setBidderNumber(bidderNumbers);
        bidderNumber.setEventId(event.getEventId());
        long id1 = 1L;
        bidderNumber.setId(id1);
        bidderNumber.setStaffUserId(user);
        bidderNumber.setUserId(user.getUserId());
    }

	@Test
	void testGetMultiAuctionItemWinnerTextMessageWithCCEnable() {

		String auctionItemWinnerCheckoutLink = "testUrl";
		doReturn("http://testShortUrl").when(urlShortnerService).getShortURL(auctionItemWinnerCheckoutLink);

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getMultiAuctionItemWinnerTextMessage(true, auctionItemWinnerCheckoutLink,
				new Event());

		assertEquals(
				"CONGRATULATIONS. You have won multiple items. Please pay at http://testShortUrl or contact the organization to pay in cash.",
				message);
	}

	@Test
	void testGetMultiAuctionItemWinnerTextMessageWithCCDisable() {

		String auctionItemWinnerCheckoutLink = "testUrl";
		doReturn("http://testShortUrl").when(urlShortnerService).getShortURL(auctionItemWinnerCheckoutLink);

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getMultiAuctionItemWinnerTextMessage(false, auctionItemWinnerCheckoutLink,
				new Event());

		assertEquals("CONGRATULATIONS. You have won multiple items. Please contact the organization to pay in cash.", message);
	}

	@Test
	void testGetSingleAuctionItemWinnerTextMessageWithCCEnable() {

		String auctionItemWinnerCheckoutLink = "testUrl";
		doReturn("http://testShortUrl").when(urlShortnerService).getShortURL(auctionItemWinnerCheckoutLink);

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getSingleAuctionItemWinnerTextMessage("NIK", true,
				auctionItemWinnerCheckoutLink, new Event());

		assertEquals(
				"CONGRATULATIONS. You have won item NIK. Please pay at http://testShortUrl or contact the organization to pay in cash.",
				message);
	}

	@Test
	void testGetSingleAuctionItemWinnerTextMessageWithCCDisable() {

		String auctionItemWinnerCheckoutLink = "testUrl";
		doReturn("http://testShortUrl").when(urlShortnerService).getShortURL(auctionItemWinnerCheckoutLink);

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getSingleAuctionItemWinnerTextMessage("NIK", false,
				auctionItemWinnerCheckoutLink, new Event());

		assertEquals(
				"CONGRATULATIONS. You have won item NIK. Please contact the organization for pickup of item and check out.",
				message);
	}

	@Test
	void testGetLooserOutBidNotificationMessageWithBidIncrementZero() {

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getLooserOutBidNotificationMessage(0, 10, "NIK", "Nik Short", "$", new Event(),
				false,false,00, true, new User());

		assertEquals(
				"You have been outbid for item Nik Short (NIK). The new bid is $10.0, so you need to bid more than that to win the item. For example: NIK$10.0",
				message);
	}

	@Test
	void testGetLooserOutBidNotificationMessageWithBidIncrementNonZero() {

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getLooserOutBidNotificationMessage(2, 10, "NIK", "Nik Short", "$", new Event(),
				false,false,00, true, new User());

		assertEquals(
				"You have been outbid for item Nik Short (NIK). The current bid is $10.0. Please bid at least $2.0 more than that to be the lead bidder. For example: NIK$12.0",
				message);
	}

	@Test
	void testGetRaffleItemWinnerTextMessageWithMultiItems() {
		List<Item> items = new ArrayList<>();
		items.add(new Item());
		items.add(new Item());
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getRaffleItemWinnerTextMessage(items, new Event());

		assertEquals(EnumRaffleSMS.RAFFLE_CONGRATULATIONS_MULTIPLE_ITEM.getValue(),message);
	}

	@Test
	void testGetRaffleItemWinnerTextMessageWithSingleItems() {
		List<Item> items = new ArrayList<>();
		Item item1 = new Item();
		item1.setCode("NIK");
		item1.setItemShortName("Nik Short");
		items.add(item1);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getRaffleItemWinnerTextMessage(items, new Event());
		String expactedMessage=EnumRaffleSMS.RAFFLE_CONGRATULATIONS_SINGLE_ITEM.getValue();
		expactedMessage = expactedMessage.replace("[item_short_name]", item1.getItemShortName()).replace("[item_code]", item1.getCode());
		assertEquals(expactedMessage,message);
	}

	@Test
	void testGetHighestBidderPriceCheckMessage() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		item.setCurrentBid(1d);
		User user = new User();
		item.setCurrentHighBidder(user);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(user);
		int bidIncrement = 1;
		String currencySymbol = "$";
		Optional<User> oUser = Optional.ofNullable(user);

		when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(oUser);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getHighestBidderPriceCheckMessage(aePhoneNumber, bidIncrement, item,
				currencySymbol, new Event());

		assertEquals("You are currently the highest bidder for "+item.getItemShortName()+" (" +item.getCode()+")"+" with a bid of $1.0.", message);
	}

	@Test
	void testGetHighestBidderPriceCheckMessageForUserNotExistWithPhoneNumberWithNoBidExist() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(1);
		item.setItemShortName("Item1");
		item.setCurrentBid(0d);
		User user = new User();
		item.setCurrentHighBidder(user);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(user);
		int bidIncrement = 1;
		String currencySymbol = "$";
		Optional<User> oUser = Optional.ofNullable(null);

		when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(oUser);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getHighestBidderPriceCheckMessage(aePhoneNumber, bidIncrement, item,
				currencySymbol, new Event());

		assertEquals(
				item.getItemShortName()+" has not yet received any bids. The starting bid amount is $1.0. Please submit a bid of this amount or greater. Example: ITEM_CODE$1.0",
				message);
	}

	@Test
	void testGetHighestBidderPriceCheckMessageForUserNotExistWithPhoneNumberWithNoHighestBid() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(1);
		item.setItemShortName("Item1");
		item.setCurrentBid(2d);
		User user = new User();
		item.setCurrentHighBidder(user);
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(user);
		int bidIncrement = 1;
		String currencySymbol = "$";
		Optional<User> oUser = Optional.ofNullable(null);

		when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(oUser);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getHighestBidderPriceCheckMessage(aePhoneNumber, bidIncrement, item,
				currencySymbol, new Event());

		assertEquals(
				"The current highest bid for item "+item.getItemShortName() + " ("+item.getCode()+ ") is $2.0. Please submit a bid of at least $1.0 higher than the current bid.",
				message);
	}

	@Test
	void testGetHighestBidderPriceCheckMessageForUserIsNotHighestBidderNoBidExist() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		item.setStartingBid(1);
		item.setCurrentBid(0d);
		User user = new User();
		item.setCurrentHighBidder(new User());
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(user);
		int bidIncrement = 1;
		String currencySymbol = "$";
		Optional<User> oUser = Optional.ofNullable(null);

		when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(oUser);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getHighestBidderPriceCheckMessage(aePhoneNumber, bidIncrement, item,
				currencySymbol, new Event());

		assertEquals(
				item.getItemShortName()+" has not yet received any bids. The starting bid amount is $1.0. Please submit a bid of this amount or greater. Example: ITEM_CODE$1.0",
				message);
	}

	@Test
	void testGetHighestBidderPriceCheckMessageForUserIsNotHighestBidderAndNoHighestBidExist() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(1);
		item.setCurrentBid(2d);
		item.setItemShortName("Item1");
		User user = new User();
		item.setCurrentHighBidder(new User());
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(user);
		int bidIncrement = 1;
		String currencySymbol = "$";
		Optional<User> oUser = Optional.ofNullable(null);

		when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(oUser);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getHighestBidderPriceCheckMessage(aePhoneNumber, bidIncrement, item,
				currencySymbol, new Event());

		assertEquals(
				"The current highest bid for item "+ item.getItemShortName() + " ("+ item.getCode() +")"+" is $2.0. Please submit a bid of at least $1.0 higher than the current bid.",
				message);
	}

	@Test
	void testGetHighestBidderPriceCheckMessageForUserNotExistWithPhoneNumberAndUserIsNotHighestBidderWithNoBidExist() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("ITEM_SHORT_NAME");
		item.setStartingBid(1);
		item.setCurrentBid(0d);
		item.setCurrentHighBidder(new User());
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(new User());
		int bidIncrement = 1;
		String currencySymbol = "$";
		Optional<User> oUser = Optional.ofNullable(null);

		when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(oUser);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getHighestBidderPriceCheckMessage(aePhoneNumber, bidIncrement, item,
				currencySymbol, new Event());

		assertEquals(
				"ITEM_SHORT_NAME has not yet received any bids. The starting bid amount is $1.0. Please submit a bid of this amount or greater. Example: ITEM_CODE$1.0",
				message);
	}

	@Test
	void testGetHighestBidderPriceCheckMessageForUserNotExistWithPhoneNumberAndUserIsNotHighestBidderWithNoHighestBidExist() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(1);
		item.setItemShortName("Item1");
		item.setCurrentBid(2d);
		item.setCurrentHighBidder(new User());
		AccelEventsPhoneNumber aePhoneNumber = new AccelEventsPhoneNumber(new User());
		int bidIncrement = 1;
		String currencySymbol = "$";
		User user = new User();
		Optional<User> oUser = Optional.ofNullable(user);

		when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(oUser);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getHighestBidderPriceCheckMessage(aePhoneNumber, bidIncrement, item,
				currencySymbol, new Event());

		assertEquals(
				"The current highest bid for item " +item.getItemShortName() +" ("+item.getCode()+") is $2.0. Please submit a bid of at least $1.0 higher than the current bid.",
				message);
	}

	@Test
	void testGetAuctionModuleNotActiveMessage() {
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getAuctionModuleNotActiveMessage(new Event());
		assertEquals("This auction is not activated yet.", message);
	}

	@Test
	void testGetAuctionModuleEndedMessage() {
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getAuctionModuleEndedMessage(new Event());
		assertEquals("Sorry, this auction has already concluded.", message);
	}

	@Test
	void testGetItemAlreadyPurchasedMessage() {
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getItemAlreadyPurchasedMessage(new Event());
		assertEquals("Sorry, this item has already been purchased.", message);
	}

	@Test
	void testGetAuctionBidConfirmationOrSuccessBidMessageForCreditCardDisableAndUserFirsNameAvaiable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(false);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		User user = new User();
		user.setFirstName("FIRST_NAME");
		int amount = 1;
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Thank you for bidding " + event.getCurrency().getSymbol() + amount + ".0 for item " + item.getItemShortName() + " (" + item.getCode()+")"
				+ ". You will be notified if you are outbid.", message);
	}

	@Test
	void testGetAuctionBidConfirmationOrSuccessBidMessageForCreditCardDisableAndUserFirsNameNotAvaiable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(false);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		int amount = 1;

		User user = new User();

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Please confirm your bid for " + item.getItemShortName() + " ("+ item.getCode() +")"
				+ " by responding with your name in the following format: FirstName LastName", message);

		user.setFirstName("");
		message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Please confirm your bid for " + item.getItemShortName() + " ("+ item.getCode() +")"
				+ " by responding with your name in the following format: FirstName LastName", message);
	}

	@Test
	void testGetAuctionBidConfirmationOrSuccessBidMessageForCreditCardEnableWithCCNotRequiredAndUserFirsNameAvaiable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(true);
		event.setCcRequiredForBidConfirm(false);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("ITEM1");

		User user = new User();
		user.setFirstName("FIRST_NAME");

		int amount = 1;

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Thank you for bidding " + event.getCurrency().getSymbol() + amount + ".0 for item " + item.getItemShortName() + " ("+item.getCode()+")"
				+ ". You will be notified if you are outbid.", message);
	}

	@Test
	void testGetAuctionBidConfirmationOrSuccessBidMessageForCreditCardEnableWithCCNotRequiredAndUserFirsNameNotAvaiable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(true);
		event.setCcRequiredForBidConfirm(false);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");

		int amount = 1;

		User user = new User();

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Please confirm your bid for " + item.getItemShortName() + " ("+ item.getCode() +")"
				+ " by responding with your name in the following format: FirstName LastName", message);

		user.setFirstName("");
		message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Please confirm your bid for " + item.getItemShortName() + " ("+ item.getCode() +")"
				+ " by responding with your name in the following format: FirstName LastName", message);
	}

	@Test
	void testGetAuctionBidConfirmationOrSuccessBidMessageForCreditCardEnableWithCCRequiredAndUserPaymentServiceAvailable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(true);
		event.setCcRequiredForBidConfirm(true);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("FIRST_NAME");

		int amount = 1;

		Payment payment = new Payment();
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(optPayment);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Thank you for bidding " + event.getCurrency().getSymbol() + amount + ".0 for item " + item.getItemShortName() + " ("+item.getCode()+")"
				+ ". You will be notified if you are outbid.", message);
	}

	@Test
	void testGetAuctionBidConfirmationOrSuccessBidMessageForCreditCardEnableWithCCRequiredAndUserPaymentServiceNotAvailable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(true);
		event.setCcRequiredForBidConfirm(true);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("FIRST_NAME");

		int amount = 1;

		Optional<Payment> optPayment = Optional.ofNullable(null);

		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(optPayment);
		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(event, item, user, amount);
		assertEquals("Thank you for bidding for item " + item.getItemShortName() + " ("+ item.getCode() +")"
				+ ". Please visit this page to confirm your bid. " + url + Constants.STRING_BLANK, message);
	}

	@Test
	void testGetAuctionBidBuyItNowItemMessageForCreditCardDisableAndUserFirsNameAvaiable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(false);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		User user = new User();
		user.setFirstName("FIRST_NAME");
		int amount = 1;
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getAuctionBidBuyItNowItemMessage(event, item, user, amount);
		assertEquals("Thank you for purchasing item " + item.getItemShortName() + " ("+ item.getCode() +")"+ " for " + event.getCurrency().getSymbol()
				+ amount + ".0.  Please see a staff member to check out.", message);
	}

	@Test
	void testGetAuctionBidBuyItNowItemMessageForCreditCardDisableAndUserFirsNameNotAvaiable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(false);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		int amount = 1;

		User user = new User();
		user.setFirstName(null);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getAuctionBidBuyItNowItemMessage(event, item, user, amount);
		assertEquals("Please confirm your bid for " + item.getItemShortName() +" ("+item.getCode()+")"
				+ " by responding with your name in the following format: FirstName LastName", message);

		user.setFirstName("");
		message = textMessageUtils.getAuctionBidBuyItNowItemMessage(event, item, user, amount);
		assertEquals("Please confirm your bid for " + item.getItemShortName()+" ("+item.getCode()+")"
				+ " by responding with your name in the following format: FirstName LastName", message);
	}

	@Test
	void testGetAuctionBidBuyItNowItemMessageForCreditCardEnable() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(true);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		int amount = 1;

		User user = new User();

		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getAuctionBidBuyItNowItemMessage(event, item, user, amount);

		assertEquals("Thank you for purchasing " + item.getItemShortName() + " ("+ item.getCode() +")"
				+ ", please complete your purchase by clicking this link " + url + Constants.STRING_BLANK, message);
	}

	@Test
	void testgetBelowAuctionBidMessageForBidBelowStartingBid() {

		Event event = new Event();
		event.setCurrency(Currency.USD);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		item.setStartingBid(2);
		item.setCurrentBid(0d);

		int bidAmount = 1;
		int bidIncrement = 1;

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getBelowAuctionBidMessage(event, item, bidAmount, bidIncrement);

		assertEquals("Sorry, the starting price for item " + item.getItemShortName() +" ("+ item.getCode()+")" + " is " + event.getCurrency().getSymbol()
				+ (int) item.getStartingBid() + ". Please submit a bid of this amount. E.g. " + item.getCode()
				+ event.getCurrency().getSymbol() + (int) item.getStartingBid(), message);
	}

	@Test
	void testgetBelowAuctionBidMessageForBidBelowStartingBidAndCurrentBidNotEqZero() {

		Event event = new Event();
		event.setCurrency(Currency.USD);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(2);
		item.setCurrentBid(10d);
		item.setItemShortName("Item1");

		int bidAmount = 1;
		int bidIncrement = 1;

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getBelowAuctionBidMessage(event, item, bidAmount, bidIncrement);

		assertEquals("Sorry, the current bid for item "  + item.getItemShortName() + " ("+ item.getCode() +")" + " is " + event.getCurrency().getSymbol()
				+ item.getCurrentBid().intValue() + " with a bid increment of " + event.getCurrency().getSymbol() + bidIncrement
				+ ". Please submit a bid of at least this amount. E.g. " + item.getCode()
				+ (item.getCurrentBid().intValue() + bidIncrement), message);
	}

	@Test
	void testgetBelowAuctionBidMessageForBidBelowRequiredBid() {

		Event event = new Event();
		event.setCurrency(Currency.USD);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		item.setStartingBid(2);
		item.setCurrentBid(3d);

		int bidAmount = 3;
		int bidIncrement = 0;
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getBelowAuctionBidMessage(event, item, bidAmount, bidIncrement);

		assertEquals("Sorry, the current bid for item " +item.getItemShortName() +" (" +item.getCode() + ") is " + event.getCurrency().getSymbol()
				+ item.getCurrentBid().intValue() + ". Please submit a bid of at least this amount. E.g. " + item.getCode()
				+ event.getCurrency().getSymbol() + item.getCurrentBid().intValue(), message);

		item.setCurrentBid(2d);
		message = textMessageUtils.getBelowAuctionBidMessage(event, item, bidAmount, bidIncrement);
		assertEquals(StringUtils.EMPTY, message);

	}

	@Test
	void testgetBelowAuctionBidMessageForBidBelowRequiredBidWithIncrement() {

		Event event = new Event();
		event.setCurrency(Currency.USD);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		item.setStartingBid(2);
		item.setCurrentBid(3d);

		int bidAmount = 3;
		int bidIncrement = 1;
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getBelowAuctionBidMessage(event, item, bidAmount, bidIncrement);

		assertEquals("Sorry, the current bid for item " +item.getItemShortName() +" ("+ item.getCode() + ") is " + event.getCurrency().getSymbol()
				+ item.getCurrentBid().intValue() + " with a bid increment of " + event.getCurrency().getSymbol() + bidIncrement
				+ ". Please submit a bid of at least this amount. E.g. " + item.getCode()
				+ (item.getCurrentBid().intValue() + bidIncrement), message);

	}

	@Test
	void testgetBelowAuctionBidMessageForEmpty() {

		Event event = new Event();
		event.setCurrency(Currency.USD);

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(2);
		item.setCurrentBid(3d);

		int bidAmount = 4;
		int bidIncrement = 1;

		String message = textMessageUtils.getBelowAuctionBidMessage(event, item, bidAmount, bidIncrement);

		assertEquals("", message);

	}

	@Test
	void testGetConfirmPurchasePaymentDisabledMessage() {
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		String message = textMessageUtils.getConfirmPurchasePaymentDisabledMessage(item, new Event(),languageCode,languageMap);

		assertEquals("Please confirm your bid for " +item.getItemShortName() +" ("+ item.getCode()+")"
				+ " by responding with your name in the following format: FirstName LastName", message);
	}

	@Test
	void testGetSuccessfulAuctionBidMessage() {

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		int bidAmount = 1;

		Event event = new Event();
		event.setCurrency(Currency.USD);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getSuccessfulAuctionBidMessage(event, item, bidAmount);

		String expectedMessage = "Thank you for bidding " + event.getCurrency().getSymbol() + bidAmount + ".0 for item "
				+ item.getItemShortName() + " ("+item.getCode()+"). You will be notified if you are outbid.";

		assertEquals(expectedMessage, message);

	}

	@Test
	void testGetSuccessfulBidFromUserMessage() {

		User user = new User();
		user.setFirstName("FIRST_NAME");

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getSuccessfulBidFromUserMessage(user, new Event());

		String expectedMessage = user.getFirstName()
				+ ", thank you for bidding. You will be notified if you are outbid.";

		assertEquals(expectedMessage, message);

	}

	@Test
	void testGetAuctionDemoMessage() {

		Event event = new Event();
		event.setCurrency(Currency.USD);

		int amount = 1;
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getAuctionDemoMessage(event, amount);

		String expectedMessage = "Welcome to your Accelevents Auction demo. To submit a bid, reply with the 3 letter item code, "
				+ event.getCurrency().getSymbol() + ", and bid amount Eg.) AUC" + event.getCurrency().getSymbol()
				+ amount + ".0 would bid " + event.getCurrency().getSymbol() + amount + ".0 on item AUC.";

		assertEquals(expectedMessage, message);

	}

	@Test
	void testGetNotifyAuctionBidderMessageForWinningBidSingleItemPaymentEnabled() {

		Event event = new Event();
		event.setCcRequiredForBidConfirm(true);
		event.setCreditCardEnabled(true);

		Auction auction = new Auction();
		auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

		User user = new User();
		long auctionId = 1;

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		int buyItNowPrice = 2;
		item.setBuyItNowPrice(buyItNowPrice);

		boolean hasPaid = false;
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		Double amount = 1d;
		BiddingSource bidSource = BiddingSource.ONLINE;
		boolean isConfirmed = false;
		AuctionBid bid = new AuctionBid(user, auctionId, item, amount, bidSource, isConfirmed);



		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getNotifyNotPaidAuctionBidWinnerMessage(event, auction, bid, user,languageCode,languageMap);

		String expectedMessage = "CONGRATULATIONS. You have won item " + item.getItemShortName() +". Please pay at " + url
				+ " or find a staff member to pay in cash.";

////		assertEquals(expectedMessage, message);
//
//		buyItNowPrice = 0;
//		item.setBuyItNowPrice(buyItNowPrice);
//		bid.setAmount(3);
//
//		message = textMessageUtils.getNotifyNotPaidAuctionBidWinnerMessage(event, auction, bid, user);
//		assertEquals(expectedMessage, message);

	}

	@Test
	void testGetNotifyAuctionBidderMessageForPurchaseCheckoutPaymentDisabled() {

		Event event = new Event();
		event.setCcRequiredForBidConfirm(false);
		event.setCreditCardEnabled(false);
		event.setCurrency(Currency.USD);

		Auction auction = new Auction();
		auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

		User user = new User();
		long auctionId = 1;

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		int buyItNowPrice = 2;
		item.setBuyItNowPrice(buyItNowPrice);

		boolean hasPaid = false;

		Double amount = 1d;
		BiddingSource bidSource = BiddingSource.ONLINE;
		boolean isConfirmed = false;
		AuctionBid bid = new AuctionBid(user, auctionId, item, amount, bidSource, isConfirmed);


        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		String url = "testUrl";

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getNotifyNotPaidAuctionBidWinnerMessage(event, auction, bid, user,languageCode,languageMap);

		String expectedMessage = "Thank you for purchasing item " + item.getItemShortName() + " ("+ item.getCode() +")"+ " for "
				+ event.getCurrency().getSymbol() + bid.getAmount() + ".  Please see a staff member to check out.";

		assertEquals(expectedMessage, message);

		buyItNowPrice = 0;
		item.setBuyItNowPrice(buyItNowPrice);
		bid.setAmount(3d);

		expectedMessage = "Thank you for purchasing item " + item.getItemShortName() + " ("+ item.getCode() +")"+ " for " + event.getCurrency().getSymbol()
				+ bid.getAmount() + ".  Please see a staff member to check out.";
		message = textMessageUtils.getNotifyNotPaidAuctionBidWinnerMessage(event, auction, bid, user,languageCode,languageMap);
		assertEquals(expectedMessage, message);

	}

	@Test
	void testGetNotifyAuctionBidderMessageForPurchaseCheckoutPaymentEnabled() {

		Event event = new Event();
		event.setCcRequiredForBidConfirm(false);
		event.setCreditCardEnabled(true);
		event.setCurrency(Currency.USD);

		Auction auction = new Auction();
		auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

		User user = new User();
		long auctionId = 1;

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");
		int buyItNowPrice = 2;
		item.setBuyItNowPrice(buyItNowPrice);

		boolean hasPaid = false;
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		Double amount = 3d;
		BiddingSource bidSource = BiddingSource.ONLINE;
		boolean isConfirmed = false;
		AuctionBid bid = new AuctionBid(user, auctionId, item, amount, bidSource, isConfirmed);



		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getNotifyNotPaidAuctionBidWinnerMessage(event, auction, bid, user,languageCode,languageMap);

		String expectedMessage = "CONGRATULATIONS. You have won item " +item.getItemShortName()
				+ ". Please pay at "+ url + Constants.STRING_BLANK +"or contact the organization to pay in cash.";

		assertEquals(expectedMessage, message);

	}

	@Test
	void testGetAuctionUrl() {
		Event event = new Event();
		event.setEventURL("EVENT_URL");

		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getAuctionUrl(event);

//		String expectedMessage = "Please visit " + url + " to bid online.";
//
//		assertEquals(expectedMessage, message);

	}

	@Test
	void testGetItemCodeDoesNotExistMessage() {
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getItemCodeDoesNotExistMessage(new Event());
		String expectedMessage = "Please enter a 3 letter item code.";
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetPaymentConfirmationRequiredMessage() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");
		event.setCurrency(Currency.USD);

		int amount = 1;
		User u = new User();
		u.setUserId(1L);
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Item1");

		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getPaymentConfirmationRequiredMessage(event, amount, u, item,languageCode,languageMap);

		String expectedMessage = "Thank you for bidding for item "
				+ item.getItemShortName() + " ("+ item.getCode() +")" + ". Please visit this page to confirm your bid. " + url + Constants.STRING_BLANK;

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetBuyRaffleTicketMessageForInavlidMessageBody() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");

		Raffle raffle = new Raffle();
		User user = new User();
		//when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Invalid message body");
		assertNull(message);
	}

	@Test
	void testGetBuyRaffleTicketMessageForValidMessageBodyWithNoPurchasedTicketAndCreditCardEnabled() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");
		event.setCreditCardEnabled(true);

		Raffle raffle = new Raffle();
		raffle.setId(1L);
		User user = new User();

		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

		when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user, raffle.getId())).thenReturn(1L);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Buy Tickets");

		String messageToBeSend = "Visit " + url
				+ " to purchase tickets. To enter your tickets for an item, reply with the 3 character item code and the number of tickets.";
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Purchase Tickets");
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Raffle Tickets");
		assertEquals(messageToBeSend, message);
	}

	@Test
	void testGetBuyRaffleTicketMessageForValidMessageBodyWithPurchasedTicketAndCreditCardDisabled() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");
		event.setCreditCardEnabled(false);

		Raffle raffle = new Raffle();
		raffle.setId(1L);
		User user = new User();

		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

		when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user, raffle.getId())).thenReturn(1L);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Buy Tickets");

		String messageToBeSend = "Please see a staff member to purchase tickets. To enter your tickets for an item, reply with the 3 character item code and the number of tickets.";
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Purchase Tickets");
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Raffle Tickets");
		assertEquals(messageToBeSend, message);
	}

	@Test
	void testGetBuyRaffleTicketMessageForBuyTicketsMessageBodyWithPurchasedTicketAndCreditCardEnabled() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");
		event.setCreditCardEnabled(true);

		Raffle raffle = new Raffle();
		raffle.setId(1L);
		User user = new User();

		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

		when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user, raffle.getId())).thenReturn(0L);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Buy Tickets");

		String messageToBeSend = "You have not purchased any raffle tickets. Please visit " + url
				+ " to purchase tickets.";
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Purchase Tickets");
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Raffle Tickets");
		assertEquals(messageToBeSend, message);
	}

	@Test
	void testGetBuyRaffleTicketMessageForValidMessageBodyWithNoPurchasedTicketAndCreditCardDisabled() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");
		event.setCreditCardEnabled(false);

		Raffle raffle = new Raffle();
		raffle.setId(1L);
		User user = new User();

		String url = "testUrl";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

		when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user, raffle.getId())).thenReturn(0L);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Buy Tickets");

		String messageToBeSend = "You have not purchased any raffle tickets. Please see a staff member to purchase tickets.";
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Purchase Tickets");
		assertEquals(messageToBeSend, message);

		message = textMessageUtils.getBuyRaffleTicketMessage(event, raffle, user, "Raffle Tickets");
		assertEquals(messageToBeSend, message);
	}

	@Test
	void testGetComplimentaryTicketMessageForCompTicketUsed() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");

		User user = new User();
		user.setUserId(1L);

		boolean isCompTicketUsed = true;

		String url = "testURL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getComplimentaryTicketMessage(event, user, isCompTicketUsed);
		assertEquals("Sorry, you have already used your complimentary ticket.", message);
	}

	@Test
	void testGetComplimentaryTicketMessageForCompTicketNotUsedCCEnabled() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");
		event.setCreditCardEnabled(true);

		User user = new User();
		user.setUserId(1L);

		boolean isCompTicketUsed = false;

		String url = "[buy_link]";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getComplimentaryTicketMessage(event, user, isCompTicketUsed);
		assertEquals(
				"You have 1 ticket. Visit [buy_link] to submit your ticket and purchase more!",
				message);
	}

	@Test
	void testGetComplimentaryTicketMessageForCompTicketNotUsedCCDisabled() {

		Event event = new Event();
		event.setEventURL("EVENT_URL");
		event.setCreditCardEnabled(false);

		User user = new User();
		user.setUserId(1L);

		boolean isCompTicketUsed = false;

		String url = "[buy_link]";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getComplimentaryTicketMessage(event, user, isCompTicketUsed);
		assertEquals(
				"You have 1 ticket. Visit [buy_link] to submit your ticket.",
				message);
	}

	@Test
	void testGetSuccessfulTicketSubmissionMessageForCCEnabled() {

		Event event = new Event();
		event.setEventURL("[buy_link]");
		event.setCreditCardEnabled(true);

		User user = new User();
		user.setUserId(1L);

		Item item = new Item();
		item.setCode("[item_code]");
		item.setItemShortName("[item_short_name]");

		long submittedTickets = 10;
		long purchasedTickets = 100;
		long attemptedSubmittedTickets = 1;

		String actualMessage = "Thank you for entering " + attemptedSubmittedTickets + " tickets for item "
				+ item.getItemShortName()+" ("
				+ item.getCode() + "). You have "
				+ (purchasedTickets - submittedTickets - attemptedSubmittedTickets)
				+ " tickets remaining. To purchase more tickets visit " + event.getEventURL();

		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getSuccessfulTicketSubmissionMessage(event, user, item, submittedTickets,
				purchasedTickets, attemptedSubmittedTickets);

		assertEquals(actualMessage, message);
	}

	@Test
	void testGetSuccessfulTicketSubmissionMessageForCCDisabled() {

		Event event = new Event();
		event.setEventURL("[buy_link]");
		event.setCreditCardEnabled(false);

		User user = new User();
		user.setUserId(1L);

		Item item = new Item();
		item.setCode("AUC");

		long submittedTickets = 10;
		long purchasedTickets = 100;
		long attemptedSubmittedTickets = 1;
		String actualMessage = "Thank you for entering " + attemptedSubmittedTickets + " tickets for item "
				+ item.getCode() + ". You have "
				+ (purchasedTickets - submittedTickets - attemptedSubmittedTickets)
				+ " tickets remaining.";

		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getSuccessfulTicketSubmissionMessage(event, user, item, submittedTickets,
				purchasedTickets, attemptedSubmittedTickets);

		assertEquals(actualMessage, message);
	}

	@Test
	void testGetRaffleModuleEndedMessage() {

		String message = textMessageUtils.getRaffleModuleEndedMessage();
		assertEquals("Sorry, this raffle has already concluded.", message);
	}

	@Test
	void testGetRaffleModuleNotActivatedMessage() {

		String message = textMessageUtils.getRaffleModuleNotActivatedMessage();
		assertEquals("This event has not been activated.  Please notify the event host.", message);
	}

	@Test
	void testGetSuccessfulTicketSubmissionPaymetnDisabledMessage() {

		String itemCode = "ITEM_CODE";
		long remainingTickets = 2;
		int itemBid = 1;
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
		int numberoftickets = (int) (remainingTickets - itemBid);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getSuccessfulTicketSubmissionPaymetnDisabledMessage(itemCode,
				remainingTickets, itemBid, new Event(),new User(),languageMap);
		String expectedMessage = "Thank you for entering " + itemBid + " tickets for item " + itemCode + ". You have "
				+ numberoftickets + " tickets remaining.";

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetRaffleOnlinePurchaseMessage() {

		int numberoftickets = 1;
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getRaffleOnlinePurchaseMessage(numberoftickets, new Event(),user,languageMap);
		String expectedMessage = "Thank you for purchasing " + numberoftickets
				+ " tickets. To enter tickets for an item, reply with the 3 character item code and the number of tickets. Example: ABC4";

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetInvalidCodeMessageForInvalidItemCode() {
		boolean raffleEnabled = true;
		boolean auctionEnabled = true;
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getInvalidCodeMessage(raffleEnabled, auctionEnabled, new Event());
		String expectedMessage = "Invalid item code. Please submit the 3 letter code of the item you are interested in.";

		assertEquals(expectedMessage, message);

		raffleEnabled = false;
		auctionEnabled = false;
		message = textMessageUtils.getInvalidCodeMessage(raffleEnabled, auctionEnabled, new Event());
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetInvalidCodeMessageForInvalidRaffleTicketSubmission() {
		boolean raffleEnabled = true;
		boolean auctionEnabled = false;
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getInvalidCodeMessage(raffleEnabled, auctionEnabled, new Event());
		String expectedMessage = "Please submit tickets in whole numbers. Example: ABC10.";

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetInvalidCodeMessageForInvalidBidFormat() {
		boolean raffleEnabled = false;
		boolean auctionEnabled = true;
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getInvalidCodeMessage(raffleEnabled, auctionEnabled, new Event());
		String expectedMessage = "Invalid bid format. Please bid with the three letter item code then the amount. E.g. ABC$10";

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetCauseAuctionStatusCheckMessageForModuleEnd() {

		Event event = new Event();
		CauseAuction causeAuction = new CauseAuction();
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, -1);
		causeAuction.setEndDate(cal.getTime());
		Item item = new Item();
		String currencySymbol = Currency.USD.getSymbol();
		boolean isStaff = true;
		int ammount = 1;
		String message = textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item, currencySymbol, isStaff,
				ammount, event);
		String expectedMessage = "Sorry, this event is no longer accepting pledges.";

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetCauseAuctionStatusCheckMessageForModuleNotActivated() {

		Event event = new Event();
		CauseAuction causeAuction = new CauseAuction();
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, 1);
		causeAuction.setEndDate(cal.getTime());
		causeAuction.setActivated(false);
		Item item = new Item();
		String currencySymbol = Currency.USD.getSymbol();
		boolean isStaff = false;
		int ammount = 1;
		String message = textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item, currencySymbol, isStaff,
				ammount, event);
		String expectedMessage = "Sorry, this event is not yet accepting pledges.";

		assertEquals(expectedMessage, message);

		isStaff = true;
		message = textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item, currencySymbol, isStaff,
				ammount, event);
		assertNotEquals(expectedMessage, message);
	}

	@Test
	void testGetCauseAuctionStatusCheckMessageForBelowRequirePledge() {

		Event event = new Event();
		CauseAuction causeAuction = new CauseAuction();
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, 1);
		causeAuction.setEndDate(cal.getTime());
		causeAuction.setActivated(true);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(2);
		String currencySymbol = Currency.USD.getSymbol();
		boolean isStaff = false;
		int ammount = 1;
		when(causeAuctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item, currencySymbol, isStaff,
				ammount, event);
		String expectedMessage = "Sorry, the minimum pledge for " + item.getCode() + " is " + currencySymbol
				+ item.getStartingBid() + ". Please submit a pledge of at least " + currencySymbol
				+ item.getStartingBid() + " in the format " + item.getCode() + currencySymbol + item.getStartingBid();

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetCauseAuctionStatusCheckMessageForNoBid() {

		Event event = new Event();
		CauseAuction causeAuction = new CauseAuction();
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, 1);
		causeAuction.setEndDate(cal.getTime());
		causeAuction.setActivated(true);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(1);
		String currencySymbol = Currency.USD.getSymbol();
		boolean isStaff = false;
		int ammount = 2;
		String message = textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item, currencySymbol, isStaff,
				ammount, event);
		String expectedMessage = StringUtils.EMPTY;

		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetCauseAuctionPriceCheckNoBidsMessage() {
		Event event = new Event();
		CauseAuction causeAuction = new CauseAuction();
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, 1);
		causeAuction.setEndDate(cal.getTime());
		causeAuction.setActivated(true);
		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setStartingBid(1);
		String currencySymbol = Currency.USD.getSymbol();
		boolean isStaff = false;
		int ammount = 2;

		String message = textMessageUtils.getCauseAuctionPriceCheckNoBidsMessage(event, causeAuction, item, currencySymbol,
				isStaff, ammount);

		String expectedMessage = "The minimum pledge for this item is " + item.getStartingBid() + ". Example.) "
				+ item.getCode() + currencySymbol + item.getStartingBid();
		assertEquals(expectedMessage, message);

		causeAuction.setActivated(false);
		message = textMessageUtils.getCauseAuctionPriceCheckNoBidsMessage(event, causeAuction, item, currencySymbol, isStaff,
				ammount);
		assertNotEquals(expectedMessage, message);
	}

	@Test
	void testGetPledgeCheckoutMessageForCreditCardEnable() {
		Event event = new Event();
		event.setCreditCardEnabled(true);
		Currency currency = Currency.USD;
		event.setCurrency(currency);
		User user = new User();
		Item item = new Item();
		item.setCode("ITEM_CODE");
		int ammount = 1;

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(causeAuctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getPledgeCheckoutMessage(event, user, item, ammount);
		String expectedMessage = "Thank you for pledging for item "
				+ item.getCode() + ". To check out please use this link " + url + " or see a staff member.";
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetPledgeCheckoutMessageForCreditCardDisableUserNameAvailable() {
		Event event = new Event();
		event.setCreditCardEnabled(false);
		Currency currency = Currency.USD;
		event.setCurrency(currency);
		User user = new User();
		user.setFirstName("FIRST_NAME");
		Item item = new Item();
		item.setCode("ITEM_CODE");
		int ammount = 1;

		String url = "TEST_URL";


		String message = textMessageUtils.getPledgeCheckoutMessage(event, user, item, ammount);
		String expectedMessage = "Thank you for pledging " + currency.getSymbol() + ammount + ".0 for item "
				+ item.getCode() + ". Please see an event staff member to checkout.";
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetPledgeCheckoutMessageForCreditCardDisableUserNameNotAvailable() {
		Event event = new Event();
		event.setCreditCardEnabled(false);
		Currency currency = Currency.USD;
		event.setCurrency(currency);
		User user = new User();
		user.setFirstName("");
		Item item = new Item();
		item.setCode("ITEM_CODE");
		int ammount = 1;

		String url = "TEST_URL";


		String message = textMessageUtils.getPledgeCheckoutMessage(event, user, item, ammount);
		String expectedMessage = "Please confirm your pledge of " + currency.getSymbol() + ammount + ".0 for "
				+ item.getCode() + " by responding with your name in the following format: FirstName LastName";
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetInsufficentTicketMessageForCreditCardEnableAndDoNotHaveTicketsToSubmit() {

		Event event = new Event();
		event.setCreditCardEnabled(true);
		User user = new User();
		long submittedTickets = 3;
		long purchasedTickets = 3;

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getInsufficentTicketMessage(event, user, submittedTickets, purchasedTickets);
		String expectedMessage = "You do not have any tickets. Please purchase tickets here: " + url;
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetInsufficientTicketMessageForCreditCardEnableAndHaveTicketsToSubmit() {

		Event event = new Event();
		event.setCreditCardEnabled(true);
		User user = new User();
		long submittedTickets = 3;
		long purchasedTickets = 4;

		long ticketsHave = purchasedTickets - submittedTickets;

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getInsufficentTicketMessage(event, user, submittedTickets, purchasedTickets);
		String expectedMessage = "You only have " + ticketsHave
				+ " tickets. Please try submitting a fewer number of tickets or purchase tickets from " + url;
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetInsufficientTicketMessageForCreditCardDisableAndDoNotHaveTicketsToSubmit() {

		Event event = new Event();
		event.setCreditCardEnabled(false);
		User user = new User();
		long submittedTickets = 3;
		long purchasedTickets = 3;

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getInsufficentTicketMessage(event, user, submittedTickets, purchasedTickets);
		assertEquals("You do not have any tickets. Please see a staff member to purchase tickets.", message);
	}

	@Test
	void testGetInsufficientTicketMessageForCreditCardDisableAndHaveTicketsToSubmit() {

		Event event = new Event();
		event.setCreditCardEnabled(false);
		User user = new User();
		long submittedTickets = 3;
		long purchasedTickets = 4;

		long ticketsHave = purchasedTickets - submittedTickets;

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String message = textMessageUtils.getInsufficentTicketMessage(event, user, submittedTickets, purchasedTickets);
		String expectedMessage = "You only have " + ticketsHave
				+ " tickets. Please see a staff member to purchase tickets.";
		assertEquals(expectedMessage, message);
	}

	@Test
	void testGetSweepStakeServiceRequestForStateMessageForNotAlreadySubmitted() {

		boolean isUserAlreadySubmittedRaffleTicket = false;
		boolean isState = false;
		String message = textMessageUtils.getSweepStakeServiceRequestForStateMessage(isUserAlreadySubmittedRaffleTicket,
				isState);

		assertEquals("Sorry, you have already entered this raffle. A Kaplan rep will be in touch if you win!", message);
	}

	@Test
	void testGetSweepStakeServiceRequestForStateMessageForAlreadySubmittedFalseState() {

		boolean isUserAlreadySubmittedRaffleTicket = true;
		boolean isState = false;
		String message = textMessageUtils.getSweepStakeServiceRequestForStateMessage(isUserAlreadySubmittedRaffleTicket,
				isState);

		assertEquals("We were unable to find your state code. Please provide the two letter state code ", message);
	}

	@Test
	void testGetSweepStakeServiceRequestForStateMessageForAlreadySubmittedTrueState() {

		boolean isUserAlreadySubmittedRaffleTicket = true;
		boolean isState = true;
		String message = textMessageUtils.getSweepStakeServiceRequestForStateMessage(isUserAlreadySubmittedRaffleTicket,
				isState);

		assertEquals("A Kaplan rep will be in touch if you win!", message);
	}

	@Test
	void testGetSweepStaksBadNameMessage() {

		String message = textMessageUtils.getSweepStaksBadNameMessage();

		assertEquals(
				"Sorry, we were unable to parse your message. Please send your name in the following format: FirstName LastName",
				message);
	}

	@Test
	void testGetProcessNameTextForSweepstakesMessageForUserEmailEmpty() {
		User textUser = new User();
		textUser.setFirstName("User 1");
		User user = new User();
		Event event = new Event();
		event.setName("Test Event");

		String actualMessage = "Hi " + textUser.getFirstName() + ", to enter " + event.getName()
				+ " raffle, please respond with your email address.";

		String message = textMessageUtils.getProcessNameTextForSweepstakesMessage(user, event, textUser);

		assertEquals(actualMessage, message);
	}

	@Test
	void testGetProcessNameTextForSweepstakesMessageForUserEmailNotEmpty() {
		User textUser = new User();
		textUser.setFirstName("User 1");
		User user = new User();
		user.setEmail("<EMAIL>");
		Event event = new Event();
		event.setName("Test Event");

		String actualMessage = "Thanks " + textUser.getFirstName() + ", what school do you go to?";
		String message = textMessageUtils.getProcessNameTextForSweepstakesMessage(user, event, textUser);

		assertEquals(actualMessage, message);
	}

	@Test
	void testGetSweepStakesSchoolMessage() {
		String firstName = "user 1";
		String actualMessage = "Hi " + firstName
				+ ", to enter [event_name] raffle, please respond with your email address.";
		String message = textMessageUtils.getSweepStakesSchoolMessage(firstName);
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetSweepStakesWhichAlreadyHadEmailSchoolMessage() {
		User user = new User();
		user.setFirstName("user 1");
		user.setEmail("<EMAIL>");
		String actualMessage = "Thanks " + user.getFirstName() + ", we already have your email as " + user.getEmail()
				+ ". What school do you go to?";
		String message = textMessageUtils.getSweepStakesWhichAlreadyHadEmailSchoolMessage(user);
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetRaffleDemoMessage() {
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getRaffleDemoMessage(new Event());
		assertEquals(
				"You have 1 demo ticket.  To complete the demo, reply with RAF1 to submit 1 ticket for the example prize.",
				message);
	}

	@Test
	void testGetServiceRequestForSchoolMessageForAlreadyHadSchool() {
		Event event = new Event();
		User user = new User();
		String bodyMessage = "";

		when(userService.hasCustomAttribute(event, user, "school")).thenReturn(true);

		String message = textMessageUtils.getServiceRequestForSchoolMessage(event, user, bodyMessage);
		assertEquals("We already have your school, please provide your two letter state code.", message);
	}

	@Test
	void testGetServiceRequestForSchoolMessageForSweepGetState() {
		Event event = new Event();
		User user = new User();
		String bodyMessage = "test message";

		when(userService.hasCustomAttribute(event, user, "school")).thenReturn(false);

		doNothing().when(userService).saveCustomAttribute(user, event, "school", bodyMessage);

		String message = textMessageUtils.getServiceRequestForSchoolMessage(event, user, bodyMessage);
		assertEquals("Thank you. Now please enter your two letter state code to be entered into the raffle!", message);
		verify(userService).saveCustomAttribute(user, event, "school", bodyMessage);
	}

	@Test
	void testGetBidWholeNumber() {
		Event event = new Event();
		event.setCurrency(Currency.USD);
		String actualMessage = "Please bid in whole numbers. Example: " + event.getCurrency().getSymbol() + "300.00";
		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getBidWholeNumber(event);
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetCauseAuctionDemoMessage() {
		Event event = new Event();
		event.setCurrency(Currency.EURO);
		String actualMessage = "Welcome to your Accelevents Fund a Need demo.  To submit a pledge, reply with the 3 letter item code, "
				+ event.getCurrency().getSymbol() + ", and amount Ex.) CAU" + event.getCurrency().getSymbol()
				+ "400 would bid " + event.getCurrency().getSymbol() + "400 on item CAU.";
		String message = textMessageUtils.getCauseAuctionDemoMessage(event);
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetPledgeStaffSuccessMessage() {
		Event event = new Event();
		event.setCurrency(Currency.EURO);
		Item item = new Item();
		item.setCode("AUC");
		int amount = 10;

        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
		String actualMessage = "Thank you for pledging " + event.getCurrency().getSymbol() + "" + amount + ".0 for item "
				+ item.getCode() + Constants.STRING_DOT;
		String message = textMessageUtils.getPledgeStaffSuccessMessage(event, item, amount,user,languageMap);
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetPledgeCheckoutPaymentMessageForCCEnabled() {
		Item item = new Item();
		item.setCode("AUC");

		Pledge pledge = new Pledge();
		pledge.setItem(item);
		pledge.setAmount(10d);

		User user = new User();
		user.setUserId(1L);

		Event event = new Event();
		event.setCurrency(Currency.AUD);
		event.setCreditCardEnabled(true);

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(causeAuctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();

		String actualMessage = "Thank you for pledging"
				+ " for item " + item.getCode() + ". To check out please use this link " + url + " or see a staff member.";
		String message = textMessageUtils.getPledgeCheckoutPaymentMessage(pledge, user, event);

		assertEquals(actualMessage, message);
	}

	@Test
	void testGetPledgeCheckoutPaymentMessageForCCDisabled() {
		Item item = new Item();
		item.setCode("AUC");

		Pledge pledge = new Pledge();
		pledge.setItem(item);
		pledge.setAmount(10d);

		User user = new User();
		user.setUserId(1L);

		Event event = new Event();
		event.setCurrency(Currency.AUD);
		event.setCreditCardEnabled(false);

		String actualMessage = "Thank you for pledging " + event.getCurrency().getSymbol() + "" + pledge.getAmount()
				+ " for item " + item.getCode() + ". Please see an event staff member to checkout.";
		String message = textMessageUtils.getPledgeCheckoutPaymentMessage(pledge, user, event);

		assertEquals(actualMessage, message);
	}

	@Test
	void testGetInvalidFormatDonationMessage() {
		Event event = new Event();
		User user = new User();

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

		String actualMessage = "Please submit your donation here: " + url;
		String message = textMessageUtils.getInvalidFormatDonationMessage(event, user);
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetInvalidFormatWholeNumberDonationMessage() {
		String actualMessage = "Please donate in whole numbers.";
		String message = textMessageUtils.getInvalidFormatWholeNumberDonationMessage();
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetTextToGiveModuleNotActivatedMessage() {
		String actualMessage = "Text to Give is not currently active. Please contact the organizer.";
		String message = textMessageUtils.getTextToGiveModuleNotActivatedMessage();
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetTextToGiveModuleNotEnabledMessage() {
		String actualMessage = "Text to Give is not yet enabled.";
		String message = textMessageUtils.getTextToGiveModuleNotEnabledMessage();
		assertEquals(actualMessage, message);
	}

	@Test
	void testGetDonationSuccessfulMessage() {
		User user = new User();
		user.setUserId(1L);

		Event event = new Event();
		event.setCurrency(Currency.AUD);

		int amount = 10;

		String url = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(url);
		when(donationCustomSmsService.getValueBysmsKeyAndEvent(anyMap(),any(),any(),any())).thenReturn(EnumDonationSMS.DONATION_SUCCESSFUL.getValue());
		String actualMessage = "Thank you for donating. Please confirm your donation here " + url;
		String message = textMessageUtils.getDonationSuccessfulMessage(event, user, amount);

		//assertEquals(actualMessage, message);
	}

	@Test
	void testGetAttendeeCheckInTicketingMessage() {

		Event event = new Event();
		String eventURL = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(eventURL);

		User user = new User();
		user.setUserId(1L);

        when(autoLoginService.getOrCreateEventLevelMagicLinkToken(any(), anyLong(), anyLong(), anyString())).thenReturn("magicLinkToken");

		String message = textMessageUtils.getAttendeeCheckInTicketingMessage(event, user, user);
		String expectedMesssage = "Welcome to the event! Check out the event page to continue donating " + eventURL
				+ " .";
		assertEquals(expectedMesssage, message);
	}

	@Test
	void testGetSweepstakesAlreadyBeenEnteredMessage() {
		String message = textMessageUtils.getSweepstakesAlreadyBeenEnteredMessage();
		assertEquals("You have already been entered into this drawing. A rep will be in touch if you win.", message);
	}

	@Test
	void testRaffleTicketPurchaseMessage() {
		Event event = new Event();
		String eventURL = "TEST_URL";
		when(urlShortnerService.getShortURL(anyString())).thenReturn(eventURL);

		User user = new User();
		user.setUserId(11L);

		Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
		when(raffleCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		when(roEventService.getLanguageCodeByUserOrEvent(user,event)).thenReturn("EN");

        String message = textMessageUtils.getRaffleTicketPurchase(10, event, user,languageMap);
		assertEquals("Thank you for purchasing 10 tickets! You can buy tickets at " + eventURL , message);
	}

	@Test
	void testGetStaffBidderMessage() {
		Event event = new Event();
		event.setName("Test Event");

		when(auctionCustomSmsServiceImpl.getValueBysmsKeyAndEvent(any(), any(), any())).thenCallRealMethod();
		String message = textMessageUtils.getStaffBidderMessage(event, user, 8889998889L,1, new User());
		assertEquals("Welcome to the event! Your bidder number is 8889998889. Please visit [auction_url] to bid online", message);
	}

    @Test
    void test_getStaffBidderMessage(){

        //setup
        String url = "testUrl";

        //mock

        doReturn(WELCOME_BIDDER_NUMBER_SMS_DONATION.getValue()).when(auctionCustomSmsServiceImpl).getValueBysmsKeyAndEvent(any(),any(),any());

         //execute
        String message = textMessageUtils.getStaffBidderMessage(event, user, bidderNumber.getBidderNumber(),bidderNumber.getUserId(), user, true);

        //assertion
        assertEquals( "Welcome to the event! Please visit [auction_url] to bid online",message);

    }

    @Test
    void test_getAttendeeCheckInTicketingParams(){

        //setup
        String url = "test_url";
        Event event = new Event();
        User user = new User();

        //mock

        when(urlShortnerService.getShortURL(anyString())).thenReturn(url);

        //execute
        textMessageUtils.getAttendeeCheckInTicketingParams(event,user, user);
    }
}