package com.accelevents.ticketing.pdf;

import com.accelevents.domain.Event;
import com.accelevents.domain.EventDesignDetail;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.enums.Currency;
import com.accelevents.dto.TicketHolderEmailDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class TicketOrderPdfTest {
	TicketOrderPdf pdf;

	@Mock
	Event event;
	@Mock
	Ticketing ticketing;
	@Mock
	List<TicketHolderEmailDto> holderData;

	@BeforeEach
	public void setUp() throws Exception {
		event = new Event();
		event.setName("Name of event");
		EventDesignDetail detail = new EventDesignDetail();
		detail.setLogoImage(
				"https://v2-dev-images-public.s3.amazonaws.com/6963b812-6c82-4cb8-832a-75380e734058tostitos-logo.jpeg");
		detail.setDesc("asdf");

		event.setCurrency(Currency.USD);

		ticketing = new Ticketing();
		ticketing.setEventStartDate(new Date());
		ticketing.setEventEndDate(new Date());
		ticketing.setEventAddress("add ress");

		holderData = new ArrayList<>();

		TicketHolderEmailDto holder = new TicketHolderEmailDto();
		holder.setPurchaserName("purchaser");
		holder.setTicketTypeName("type");
		holder.setDateOfPurchase(new Date());
		holder.setOrderNumber(7765556);
		holder.setPrice(33.22);
		holder.setBarcode(UUID.randomUUID().toString());
		holderData.add(holder);
		pdf = new TicketOrderPdf(event, detail, ticketing.getEventStartDate(), ticketing.getEventEndDate(), ticketing.getEventAddress(), holderData, null);
	}

	@Test
	public void test() throws IOException {
		// ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// pdf.createPdf(baos);
		//
		// FileOutputStream fos = new FileOutputStream(new
		// File("d:\\mockPdf.pdf"));
		// baos.writeTo(fos);
	}

}
