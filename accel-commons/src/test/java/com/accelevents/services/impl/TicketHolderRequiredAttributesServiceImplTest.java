package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.dto.TicketAttributeValueDto;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.dto.ValueDto;
import com.accelevents.repositories.require.attributes.HolderAttributeCapacityRepo;
import com.accelevents.repositories.require.attributes.TicketRequiresAttributesRepo;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.ticketing.dto.TicketSettingDto;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.xml.bind.Unmarshaller;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketHolderRequiredAttributesServiceImplTest {

    @Spy
    @InjectMocks
    private TicketHolderRequiredAttributesServiceImpl ticketHolderRequiredAttributeServiceImpl = new TicketHolderRequiredAttributesServiceImpl();

    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketRequiresAttributesRepo attributesRepository;

    @Mock
    private HolderAttributeCapacityRepo holderAttributeCapacityRepo;

	private Event event;
    private Ticketing ticketing;
	private TicketAttributeValueDto ticketAttributeValueDto;
    private TicketAttributeValueDto1 ticketAttributeValueDto1;
    private ValueDto valueDtoHolder, valueDtoPurchaser;
    private AttributeKeyValueDto attributeKeyValueDtoAttribute;
    private TicketSettingDto ticketSettingDto;
    private Unmarshaller unmarshaller;


	private Long eventId = 1L;
	private Long customAttributeId = 1L;
    private Long topAttributeId = 1L;
    private Long topBottomAttributeId = 1L;
    private int sequence = 1000;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
		User user = new User();
        user.setUserId(1L);
        ticketing = new Ticketing();
		TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();

		TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
		long id = 1L;
		ticketHolderAttributes.setId(id);
		String email = "<EMAIL>";
		String lastName = "Kaz";
		String firstName = "Jon";
		ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue(firstName, lastName, email));

		AttributeKeyValueDto holderAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoAttribute.setKey(Constants.FIRST_NAME);
        holderAttributeKeyValueDtoAttribute.setValue(firstName);

		AttributeKeyValueDto holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        holderAttributeKeyValueDtoQuestion.setValue(lastName);

		AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute.setKey(Constants.FIRST_NAME);
        purchaserAttributeKeyValueDtoAttribute.setValue(firstName);


		AttributeKeyValueDto purchaserAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        purchaserAttributeKeyValueDtoQuestion.setValue(lastName);
    }

    static Object[] getRecurringEventId(){
        return new Object[]{
                new Object[]{null},
                new Object[]{0L},
        };
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_deleteCustomAttribute_AlreadyDeletedForBuyerAndTryToDeleteForHolder(Long recurringEventId){

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(false);
        attributes.setDeletedForBuyer(true);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(null != recurringEventId);

        List<TicketHolderRequiredAttributes> reAttributesList = new ArrayList<>();

        //mock

        when(attributesRepository.findByCreatedFromIdAndEventId(anyLong(), any())).thenReturn(reAttributesList);
        when(holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(anyLong())).thenReturn(new ArrayList<>());
        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, false, recurringEventId, event);
        verify(attributesRepository, times(1)).deleteById(anyLong());
    }

    @Test
    void test_deleteCustomAttribute_AlreadyDeletedForBuyerAndTryToDeleteForHolder_nonRecurringEvent(){

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(false);
        attributes.setDeletedForBuyer(true);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(false);

        List<TicketHolderRequiredAttributes> reAttributesList = new ArrayList<>();

        //mock

        when(attributesRepository.findByCreatedFromIdAndEventId(anyLong(), any())).thenReturn(reAttributesList);
        when(holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(anyLong())).thenReturn(new ArrayList<>());


        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, false, 0L, event);
        verify(attributesRepository, times(1)).deleteById(anyLong());
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_deleteCustomAttribute_AlreadyDeletedForHolderAndTryToDeleteForBuyer(Long recurringEventId){

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(true);
        attributes.setDeletedForBuyer(false);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(null != recurringEventId);

        List<TicketHolderRequiredAttributes> reAttributesList = new ArrayList<>();

        //mock

        when(attributesRepository.findByCreatedFromIdAndEventId(anyLong(), any())).thenReturn(reAttributesList);
        when(holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(anyLong())).thenReturn(new ArrayList<>());


        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, true, recurringEventId, event);
        verify(attributesRepository, times(1)).deleteById(anyLong());
    }

    @Test
    void test_deleteCustomAttribute_AlreadyDeletedForBuyerAndTryToDeleteForHolder_withNonRecurringEvent(){
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(false);
        attributes.setDeletedForBuyer(true);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(false);

        //mock

        when(holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(anyLong())).thenReturn(new ArrayList<>());


        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, false, 0L, event);
        verify(attributesRepository, times(1)).deleteById(anyLong());
    }

    /*@Test
    void test_deleteCustomAttribute_AlreadyDeletedForHolderAndTryToDeleteForBuyer_withRecurringEventId(){
        Long recurringEventId=1L;
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(true);
        attributes.setDeletedForBuyer(false);

        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, true, recurringEventId, event);
    }
    @Test
    void test_deleteCustomAttribute_AlreadyDeletedForHolderAndTryToDeleteForBuyer_recurringEventFalse(){
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(true);
        attributes.setDeletedForBuyer(false);

        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, true, 0L, event);
    }*/

    static Object[] getRecurringEventIdExceptRecurringIdIsOne(){
        return new Object[]{
                new Object[]{null},
                new Object[]{0L},
        };
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventIdExceptRecurringIdIsOne")
    void test_deleteCustomAttribute_updateDeletedForBuyerFlag(Long recurringEventId){

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setEnabledForTicketPurchaser(true);
        attributes.setDeletedForHolder(false);
        attributes.setDeletedForBuyer(false);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(true);

        List<TicketHolderRequiredAttributes> reAttributesList = new ArrayList<>();
        reAttributesList.add(attributes);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(attributesRepository.findByCreatedFromIdAndEventId(anyLong(), any())).thenReturn(reAttributesList);



        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, true, recurringEventId, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }

    @Test
    void test_deleteCustomAttribute_updateDeletedForBuyerFlag_specificRecurringDate(){

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setEnabledForTicketPurchaser(true);
        attributes.setDeletedForHolder(false);
        attributes.setDeletedForBuyer(false);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(true);

        List<TicketHolderRequiredAttributes> reAttributesList = new ArrayList<>();
        reAttributesList.add(attributes);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, true, 1L, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }

    /*@Test
    void test_deleteCustomAttribute_updateDeletedForBuyerFlag_withRecurringEventId(){
        Long recurringEventId=1L;
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setEnabledForTicketPurchaser(true);
        attributes.setDeletedForHolder(false);
        attributes.setDeletedForBuyer(false);

        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, true, recurringEventId, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }*/
    @Test
    void test_deleteCustomAttribute_updateDeletedForBuyerFlag_recurringEventFalse(){
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setEnabledForTicketPurchaser(true);
        attributes.setDeletedForHolder(false);
        attributes.setDeletedForBuyer(false);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);



        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, true, 0L, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_deleteCustomAttribute_updateDeletedForHolderFlag(Long recurringEventId){

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(false);
        attributes.setEnabledForTicketHolder(true);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(true);

        List<TicketHolderRequiredAttributes> reAttributesList = new ArrayList<>();
        reAttributesList.add(attributes);

        //mock

        when(attributesRepository.findByCreatedFromIdAndEventId(anyLong(), any())).thenReturn(reAttributesList);
        when(holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(anyLong())).thenReturn(new ArrayList<>());


        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, false, recurringEventId, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }

    @Test
    void test_deleteCustomAttribute_updateDeletedForHolderFlag(){

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(false);
        attributes.setEnabledForTicketHolder(true);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(true);

        List<TicketHolderRequiredAttributes> reAttributesList = new ArrayList<>();
        reAttributesList.add(attributes);

        //mock


        when(holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(anyLong())).thenReturn(new ArrayList<>());


        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, false, 1L, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }

    /*
    @Test
    void test_deleteCustomAttribute_updateDeletedForHolderFlag_withRecurringEventId(){
        Long recurringEventId=1L;
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(false);
        attributes.setEnabledForTicketHolder(true);

        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, false, recurringEventId, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }*/
    @Test
    void test_deleteCustomAttribute_updateDeletedForHolderFlag_recurringEventFalse(){
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setEventid(event);
        attributes.setDeletedForHolder(false);
        attributes.setEnabledForTicketHolder(true);
        attributes.setAttributeValueType(AttributeValueType.TEXT);

        ticketing.setRecurringEvent(false);

        //mock

        when(holderAttributeCapacityRepo.findAllByTicketHolderAttributesId(anyLong())).thenReturn(new ArrayList<>());


        //execute
        ticketHolderRequiredAttributeServiceImpl.deleteCustomAttribute(attributes, false, 0L, event);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketingAttributes = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(attributesRepository, times(1)).save(ticketingAttributes.capture());

        TicketHolderRequiredAttributes actual = ticketingAttributes.getValue();
        assertEquals(attributes.getDeletedForBuyer(),actual.getDeletedForBuyer());
        assertEquals(attributes.getEnabledForTicketPurchaser(),actual.getEnabledForTicketPurchaser());
    }

}
