package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.AttendeePartialPaymentDto;
import com.accelevents.dto.EventTicketFeesDto;
import com.accelevents.dto.TicketExchangeDto;
import com.accelevents.dto.TicketTransferDueAmountDetailsDTO;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.EventTicketTransactionRepository;
import com.accelevents.services.VatTaxService;
import com.accelevents.ticketing.dto.ChargeDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.accelevents.utils.Constants.CASH;
import static com.accelevents.utils.Constants.GENERAL_ADMISSION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EventTicketTransactionServiceImplTest {

    @InjectMocks
    @Spy
    private EventTicketTransactionServiceImpl eventTicketTransactionServiceImpl;

    @Mock
    private EventTicketTransactionRepository eventTicketTransactionRepository;
    @Mock
    private VatTaxService vatTaxService;

    private static final List<EventTicketTransaction.PaymentType> CARD_PAYMENT_TYPES = List.of(EventTicketTransaction.PaymentType.CARD);

    private Event event;
    private Ticketing ticketing;
    private TicketingType newTicketingType;
    private AttendeePartialPaymentDto ticketExchangePaymentDto;
    private ChargeDto chargeDto;
    private EventTickets eventTickets;
    private List<EventTickets> eventTicketsList;
    private EventTicketFeesDto eventTicketFeesDto;
    private User staffUser;

    private Long ticketingOrderId = 1L;

    @BeforeEach
    void setUp() {

        eventTickets = EventDataUtil.getEventTickets();
        eventTickets.setRefundedAmount(0d);
        eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        event = EventDataUtil.getEvent();
        ticketing = EventDataUtil.getTicketing(event);

        newTicketingType = new TicketingType();
        newTicketingType.setId(2L);
        newTicketingType.setTicketTypeName(GENERAL_ADMISSION+"_1");
        newTicketingType.setTicketType(TicketType.PAID);
        newTicketingType.setPrice(100);
        newTicketingType.setEndDate(ticketing.getEventEndDate());
        newTicketingType.setStartDate(new Date());
        newTicketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        newTicketingType.setRecurringEventSalesEndTime(60);
        newTicketingType.setNumberOfTickets(100);
        newTicketingType.setNumberOfTicketPerTable(1);
        newTicketingType.setMaxTicketsPerBuyer(10);
        newTicketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        newTicketingType.setTicketing(ticketing);
        newTicketingType.setDataType(DataType.TICKET);

        // this fee calculated based on our calculation formula and fee are pass to buyer and sales tax fee percentage is 10%
        eventTicketFeesDto = new EventTicketFeesDto();
        eventTicketFeesDto.setEventTicketId(eventTickets.getId());
        eventTicketFeesDto.setEventTicketPaidAmount(58.85);
        eventTicketFeesDto.setCcFeeAmount(2.01);
        eventTicketFeesDto.setAeFeeAmount(1.5);
        eventTicketFeesDto.setSalesTaxFee(5.33);

        chargeDto = new ChargeDto();
        chargeDto.id("charge_1");

        staffUser = new User();
        staffUser.setUserId(2L);
        staffUser.setEmail("<EMAIL>");
        staffUser.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        staffUser.setFirstName("Normal");
        staffUser.setLastName("User");
        staffUser.setMostRecentEventId(1L);

        ticketExchangePaymentDto = new AttendeePartialPaymentDto();
        ticketExchangePaymentDto.setPaymentType(CASH);
        ticketExchangePaymentDto.setTransactionId("transactionId_123");
        ticketExchangePaymentDto.setPaymentDate("2024/09/10");
        ticketExchangePaymentDto.setNote("Payment Done While Ticket Transfer");


    }

    @Test
    void test_storeEventTicketTransaction_when_ticketExchangeDto_is_not_null_and_payment_method_is_cash() {

        eventTicketFeesDto.setEventTicketPaidAmount(51.5);
        eventTicketFeesDto.setSalesTaxFee(5.15);
        eventTicketFeesDto.setAeFeeAmount(1.5);
        eventTicketFeesDto.setCcFeeAmount(0);

        eventTicketTransactionServiceImpl.storeEventTicketTransaction(ticketingOrderId, eventTicketsList, null, Collections.singletonList(eventTicketFeesDto), EventTicketTransaction.PaymentType.CASH, ticketExchangePaymentDto, staffUser.getUserId());

        ArgumentCaptor<List<EventTicketTransaction>> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(eventTicketTransactionRepository, Mockito.times(1)).saveAll(eventTicketsArgumentCaptor.capture());
        EventTicketTransaction resEventTicketTransaction = eventTicketsArgumentCaptor.getValue().get(0);

        assertEquals(ticketingOrderId, resEventTicketTransaction.getTicketingOrderId());
        assertEquals(eventTickets.getId(), resEventTicketTransaction.getEventTicketId());
        assertEquals(51.5, resEventTicketTransaction.getPaidAmount());
        assertEquals(EventTicketTransaction.PaymentType.CASH, resEventTicketTransaction.getPaymentType());
        assertEquals(EventTicketTransaction.TicketTransactionChargeStatus.PAID, resEventTicketTransaction.getChargeStatus());

        assertEquals("transactionId_123", resEventTicketTransaction.getTransactionId());
        assertEquals("Payment Done While Ticket Transfer", resEventTicketTransaction.getNote());
    }

    @Test
    void test_storeEventTicketTransaction_when_ticketExchangeDto_is_null_and_payment_method_is_card() {

        // Execution
        eventTicketTransactionServiceImpl.storeEventTicketTransaction(ticketingOrderId, eventTicketsList, chargeDto, Collections.singletonList(eventTicketFeesDto), EventTicketTransaction.PaymentType.CARD, ticketExchangePaymentDto, staffUser.getUserId());

        ArgumentCaptor<List<EventTicketTransaction>> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(eventTicketTransactionRepository, Mockito.times(1)).saveAll(eventTicketsArgumentCaptor.capture());
        EventTicketTransaction resEventTicketTransaction = eventTicketsArgumentCaptor.getValue().get(0);

        assertEquals(ticketingOrderId, resEventTicketTransaction.getTicketingOrderId());
        assertEquals(eventTickets.getId(), resEventTicketTransaction.getEventTicketId());
        assertEquals(58.85, resEventTicketTransaction.getPaidAmount());
        assertEquals(2.01, resEventTicketTransaction.getCcFeeAmount());
        assertEquals(EventTicketTransaction.PaymentType.CARD, resEventTicketTransaction.getPaymentType());
        assertEquals(EventTicketTransaction.TicketTransactionChargeStatus.PAID, resEventTicketTransaction.getChargeStatus());
        assertEquals("charge_1", resEventTicketTransaction.getChargeId());
    }

    @Test
    void test_calculateDueAmountForGivenTicketPrice_with_pass_fee_to_buyer_and_card_payment() {

        //Setup
        EventTicketTransaction eventTicketTransaction = new EventTicketTransaction();
        eventTicketTransaction.setPaidAmount(58.85);
        eventTicketTransaction.setCcFeeAmount(2.01);
        eventTicketTransaction.setAeFeeAmount(1.5);

        eventTickets.setVatTaxFee(5.33);
        newTicketingType.setPassfeetobuyer(true);
        newTicketingType.setPassFeeVatToBuyer(true);
        eventTicketTransaction.setEventTicket(eventTickets);

        // mock
        when(vatTaxService.getVatTaxByTicketTypeOrEvent(eventTickets.getEventId(),newTicketingType)).thenReturn(10.0);
        when(eventTicketTransactionRepository.getAllByEventTicketIdsAndChargeStatus(List.of(eventTickets.getId()), EventTicketTransaction.TicketTransactionChargeStatus.PAID)).thenReturn(Collections.singletonList(eventTicketTransaction));

        // Execution
        TicketTransferDueAmountDetailsDTO dueAmountDTO = eventTicketTransactionServiceImpl.calculateDueAmountForGivenTicketPrice(eventTickets, newTicketingType, BigDecimal.valueOf(100), eventTickets.getVatTaxFee(), BigDecimal.valueOf(eventTickets.getVatTaxPercentage()));

        assertEquals(49.99, dueAmountDTO.getDueAmount().doubleValue());
    }

    @Test
    void test_calculateDueAmountForGivenTicketPrice_with_absorb_fee_and_card_payment() {

        // Setup
        EventTicketTransaction eventTicketTransaction = new EventTicketTransaction();
        eventTicketTransaction.setPaidAmount(58.85);
        eventTicketTransaction.setCcFeeAmount(2.01);
        eventTicketTransaction.setAeFeeAmount(1.5);

        eventTickets.setVatTaxFee(5.33);
        newTicketingType.setPassfeetobuyer(false);
        newTicketingType.setPassFeeVatToBuyer(false);
        eventTicketTransaction.setEventTicket(eventTickets);

        // Mock
        when(vatTaxService.getVatTaxByTicketTypeOrEvent(eventTickets.getEventId(),newTicketingType)).thenReturn(10.0);
        when(eventTicketTransactionRepository.getAllByEventTicketIdsAndChargeStatus(List.of(eventTickets.getId()), EventTicketTransaction.TicketTransactionChargeStatus.PAID)).thenReturn(Collections.singletonList(eventTicketTransaction));

        // Execution
        TicketTransferDueAmountDetailsDTO dueAmountDTO = eventTicketTransactionServiceImpl.calculateDueAmountForGivenTicketPrice(eventTickets, newTicketingType, BigDecimal.valueOf(100), eventTickets.getVatTaxFee(), BigDecimal.valueOf(eventTickets.getVatTaxPercentage()));

        assertEquals(41.15, dueAmountDTO.getDueAmount().doubleValue());
    }
}
