package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.TicketHolderAttributes;
import com.accelevents.domain.TicketHolderRequiredAttributes;
import com.accelevents.domain.User;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.dto.TicketAttributeValueDto;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.dto.ValueDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.repositories.require.attributes.TicketHolderAttributesRepository;
import com.accelevents.services.AllRequiresAttributesService;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.services.TicketingService;
import com.accelevents.ticketing.dto.TicketSettingDto;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.math.BigInteger;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketHolderAttributesServiceImplTest {

    @Spy
    @InjectMocks
    private  TicketHolderAttributesServiceImpl ticketHolderAttributesServiceImpl = new TicketHolderAttributesServiceImpl();

    @Mock
    private TicketHolderAttributesRepository ticketHolderAttributesRepository;

    @Mock
    private AllRequiresAttributesService allRequiresAttributesService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;


    @Mock
    private TicketingService ticketingService;

	private Event event;
	private User user;
    private TicketHolderAttributes ticketHolderAttributes;
    private TicketAttributeValueDto ticketAttributeValueDto;
    private TicketAttributeValueDto1 ticketAttributeValueDto1;
    private ValueDto valueDtoHolder, valueDtoPurchaser;
    private AttributeKeyValueDto attributeKeyValueDtoAttribute;
	private AttributeKeyValueDto holderAttributeKeyValueDtoAttribute, holderAttributeKeyValueDtoQuestion,
            purchaserAttributeKeyValueDtoAttribute, purchaserAttributeKeyValueDtoQuestion;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;


    private long Id = 1L;
	private String firstName = "Jon";
    private String lastName = "Kaz";
	private Long customAttributeId = 1L;
    private Long topAttributeId = 1L;
    private Long topBottomAttributeId = 1L;
    private int sequence = 1000;

    @BeforeEach
    void setUp() throws Exception {

        event = EventDataUtil.getEvent();
        user = new User();
        user.setUserId(1L);

        ticketHolderRequiredAttributes  = new TicketHolderRequiredAttributes();

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(Id);
		String email = "<EMAIL>";
		ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue(firstName, lastName, email));

        holderAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoAttribute.setKey(Constants.FIRST_NAME);
        holderAttributeKeyValueDtoAttribute.setValue(firstName);

        holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        holderAttributeKeyValueDtoQuestion.setValue(lastName);

        purchaserAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute.setKey(Constants.FIRST_NAME);
        purchaserAttributeKeyValueDtoAttribute.setValue(firstName);


        purchaserAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        purchaserAttributeKeyValueDtoQuestion.setValue(lastName);
    }

    @Test
    void test_save_success() {

        //mock
        when(ticketHolderAttributesRepository.save(ticketHolderAttributes)).thenReturn(ticketHolderAttributes);

        //Execution
        ticketHolderAttributesServiceImpl.save(ticketHolderAttributes);
        ArgumentCaptor<TicketHolderAttributes> ticketHolderAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderAttributes.class);
        verify(ticketHolderAttributesRepository,times(1)).save(ticketHolderAttributesArgumentCaptor.capture());

        TicketHolderAttributes actual = ticketHolderAttributesArgumentCaptor.getValue();
        assertEquals(ticketHolderAttributes.getJsonValue(), actual.getJsonValue());
    }

    @Test
    void test_getUnmarshaller_success() throws JAXBException{

        //Execution
        Unmarshaller unmarshaller = ticketHolderAttributesServiceImpl.getUnmarshaller();
        assertNotNull(unmarshaller);
    }

    @Test
    void test_unmashler_success() throws JAXBException{

        //setup
		Unmarshaller unmarshaller = ticketHolderAttributesServiceImpl.getUnmarshaller();
        String xml = EventDataUtil.getSampleXML();

        //Execution
        TicketAttributeValueDto ticketAttributeValueDto =  ticketHolderAttributesServiceImpl.unmashler(xml, unmarshaller);
        assertNotNull(ticketAttributeValueDto);
        assertTrue(ticketAttributeValueDto.getHolder().toString().contains(firstName));
    }

    @Test
    void test_marshaller_success() throws JAXBException {

        //setup
        getTicketAttributeValueDto();

        //Excecution
        String marshalData = ticketHolderAttributesServiceImpl.marshaller(ticketAttributeValueDto);
        assertNotNull(marshalData);
        assertTrue(marshalData.contains(firstName));
    }

    @Test
    void test_convertTo1_success() {

        //setup
        getTicketAttributeValueDto();

        //Excecution
        TicketAttributeValueDto1 convertedDAta = ticketHolderAttributesServiceImpl.convertTo1(ticketAttributeValueDto);
        assertNotNull(convertedDAta);
        assertTrue(convertedDAta.getHolder().toString().contains(firstName));
    }

    @Test
    void test_convertTo1_success_with_purchaser_Null() {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);

        List<AttributeKeyValueDto> attributeKeyValueDtosHolderAttribute = new ArrayList<>();
        attributeKeyValueDtosHolderAttribute.add(holderAttributeKeyValueDtoAttribute);

        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtosHolderAttribute);
        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);

        //Excecution
        TicketAttributeValueDto1 convertedDAta = ticketHolderAttributesServiceImpl.convertTo1(ticketAttributeValueDto);
        assertNotNull(convertedDAta);
        assertTrue(convertedDAta.getHolder().toString().contains(firstName));
    }

    @Test
    void test_convertTo1_success_with_holder_Null() {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserAttribute = new ArrayList<>();
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserQuestion = new ArrayList<>();
        attributeKeyValueDtosPurchaserQuestion.add(purchaserAttributeKeyValueDtoQuestion);

        valueDtoPurchaser = new ValueDto();
        valueDtoPurchaser.setAttributes(attributeKeyValueDtosPurchaserAttribute);
        valueDtoPurchaser.setQuestions(attributeKeyValueDtosPurchaserQuestion);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setPurchaser(valueDtoPurchaser);

        //Excecution
        TicketAttributeValueDto1 convertedDAta = ticketHolderAttributesServiceImpl.convertTo1(ticketAttributeValueDto);
        assertNotNull(convertedDAta);
        assertTrue(convertedDAta.getPurchaser().toString().contains(firstName));
    }

    @Test
    void test_parseToJsonString_success_ticketSettingDto() {

        //setup
        String expectedData = "{\"attributes\":[],\"holderAttribute\":true,\"allowAttendeeToEditInfo\":true,\"showMemberCountInCheckout\":false,\"isUniqueTicketBuyerEmail\":false}";
		TicketSettingDto ticketSettingDto = new TicketSettingDto();
        ticketSettingDto.setAllowAttendeeToEditInfo(true);
        ticketSettingDto.setHolderAttribute(true);

        //Execution
        String jsonString = ticketHolderAttributesServiceImpl.parseToJsonString(ticketSettingDto);
        assertNotNull(jsonString);
        assertEquals(expectedData, jsonString );
    }

    @Test
    void test_parseToJsonString_successs_ticketAttributeDto() {

        //setup
        getTicketAttributeValueDto();

        //Execution
        String jsonString = ticketHolderAttributesServiceImpl.parseToJsonString(ticketAttributeValueDto);

        assertNotNull(jsonString);
        assertTrue(jsonString.contains(firstName));
    }

    @Test
    void test_parseJsonToXML_success() {

        //setup
        Map<String, String> holder = new HashMap<>();
        holder.put(Constants.FIRST_NAME,firstName);

        Map<String, String> purchaser = new HashMap<>();
        holder.put(Constants.LAST_NAME,lastName);

        ticketAttributeValueDto1 = new TicketAttributeValueDto1();
        ticketAttributeValueDto1.setHolder(holder);
        ticketAttributeValueDto1.setPurchaser(purchaser);

        //Execution
        String xmlData = ticketHolderAttributesServiceImpl.parseJsonToXML(ticketAttributeValueDto1);
        assertNotNull(xmlData);
    }

    @Test
    void test_updateAttributes_success() {

        //setup
        String jsonOldName = "oldJson";
        String jsonNewName = "newJson";

        //mock
		Long eventId = 1L;
		doNothing().when(this.ticketHolderAttributesRepository).updateAttribute(jsonOldName, eventId, jsonNewName);

        //Execution
        ticketHolderAttributesServiceImpl.updateAttributes(jsonOldName, eventId,jsonNewName);
        Mockito.verify(ticketHolderAttributesRepository, Mockito.atLeastOnce()).updateAttribute(jsonOldName, eventId, jsonNewName);
    }

    @Test
    void test_findMailingAddress_success() {

        //setup
        List<TicketHolderAttributes> ticketHolderAttributeList = new ArrayList<>();
        ticketHolderAttributeList.add(ticketHolderAttributes);

        //mock
        when(ticketHolderAttributesRepository.findMailingAddress()).thenReturn(ticketHolderAttributeList);

        //Execution
        List<TicketHolderAttributes> ticketHolderAttributeData = ticketHolderAttributesServiceImpl.findMailingAddress();
        for (TicketHolderAttributes actual: ticketHolderAttributeData) {
            assertEquals(ticketHolderAttributes.getJsonValue(), actual.getJsonValue());
        }

    }

    @Test
    void test_parseToJsonString_success_ticketAttributeValueDto() {

        //setup
        Map<String, String> holder = new HashMap<>();
        holder.put(Constants.FIRST_NAME,firstName);
        Map<String, String> purchaser = new HashMap<>();
        holder.put(Constants.LAST_NAME,lastName);
        ticketAttributeValueDto1 = new TicketAttributeValueDto1();
        ticketAttributeValueDto1.setHolder(holder);
        ticketAttributeValueDto1.setPurchaser(purchaser);

        //Execution
        String jsonString = ticketHolderAttributesServiceImpl.parseToJsonString(ticketAttributeValueDto1);
        assertNotNull(jsonString);
        assertTrue(jsonString.contains(firstName) && jsonString.contains(lastName));
    }

    @Test
    void test_parseJsonToObject_success() {

        //setup
        String jsonString = EventDataUtil.getJsonValue();

        //Execution
        TicketAttributeValueDto1 jsonObject = TicketHolderAttributesHelper.parseJsonToObject(jsonString);
        assertNotNull(jsonObject);
    }

    @Test
    void test_parseJsonToObject_success_with_jsonString_null() {

        //setup
        String jsonString = "";

        //Execution
        TicketAttributeValueDto1 jsonObject = TicketHolderAttributesHelper.parseJsonToObject(jsonString);
        assertNotNull(jsonObject);
    }

    @Test
    void test_findById_success() {

        //mock
        when(ticketHolderAttributesRepository.findByid(ticketHolderAttributes.getId())).thenReturn(ticketHolderAttributes);

        //Execution
        TicketHolderAttributes ticketHolderAttributeData = ticketHolderAttributesServiceImpl.findById(ticketHolderAttributes.getId());
        assertEquals(ticketHolderAttributes.getId(), ticketHolderAttributeData.getId());
    }

    @Test
    void test_findSameForMultipleTicket_success() {

        //setup
        List<BigInteger> ticketAttributeHolderId = new ArrayList<>();
        ticketAttributeHolderId.add(BigInteger.valueOf(Id));

        //mock
        when(ticketHolderAttributesRepository.findSameForMultipleTicket()).thenReturn(ticketAttributeHolderId);

        //Execution
        List<BigInteger> retrivedData = ticketHolderAttributesServiceImpl.findSameForMultipleTicket();
        assertNotNull(retrivedData);
    }

    @Test
    void test_getTicketsWithNullValue_success() {

        //setup
        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(Id);

        List<TicketHolderAttributes> ticketHolderAttributesList = new ArrayList<>();
        ticketHolderAttributesList.add(ticketHolderAttributes);

        //mock
        when(ticketHolderAttributesRepository.findAllByJsonValueIsNull()).thenReturn(ticketHolderAttributesList);

        //Execution
        List<TicketHolderAttributes> ticketHolderAttributeData = ticketHolderAttributesServiceImpl.getTicketsWithNullValue();
        assertNotNull(ticketHolderAttributeData);
    }

    @Test
    void test_getAttributeKeyValueDtos_success() {

        //setup
        Map<String, String> attribute = new HashMap<>();
        attribute.put(Constants.FIRST_NAME,firstName);
        attribute.put(Constants.LAST_NAME,lastName);

        //Execution
        List<AttributeKeyValueDto> attributeKeyValueDtoData = ticketHolderAttributesServiceImpl.getAttributeKeyValueDtos(attribute);
        assertNotNull(attributeKeyValueDtoData);
    }

    @Test
    void test_listToMap_success_with_dto() {

        //setup
        attributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        attributeKeyValueDtoAttribute.setKey(Constants.LAST_NAME);
        attributeKeyValueDtoAttribute.setValue(lastName);

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDtoAttribute);

        //Execution
        Map<String, String> attributeKeyValueDtoData = ticketHolderAttributesServiceImpl.listToMap(attributeKeyValueDtos);
        assertNotNull(attributeKeyValueDtoData);
        assertTrue(attributeKeyValueDtoData.containsValue(lastName));
    }

    @Test
    void test_listToMap_success_with_dto_empty() {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();

        //Execution
        Map<String, String> attributeKeyValueDtoData = ticketHolderAttributesServiceImpl.listToMap(attributeKeyValueDtos);
        assertTrue(attributeKeyValueDtoData.isEmpty());
    }

    @Test
    void test_listToMap_success_with_nullDto() {

        //setup
        attributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();

        //Execution
        Map<String, String> attributeKeyValueDtoData = ticketHolderAttributesServiceImpl.listToMap(null);
        assertTrue(attributeKeyValueDtoData.isEmpty());
    }

    @Test
    void test_updateCustomAttributeSequence_success() {

        //setup
        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getNextPositionAttribute(ticketHolderRequiredAttributes)).thenReturn(ticketHolderRequiredAttributes);
        when(ticketHolderRequiredAttributesService.getPreviousPositionAttribute(ticketHolderRequiredAttributes)).thenReturn(ticketHolderRequiredAttributes);
        Mockito.doNothing().when(ticketHolderRequiredAttributesService).updatePositionInGroup(anyInt(), anyInt(), anyInt(),any());

        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService, times(1)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());

        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(actualData.getId(), ticketHolderRequiredAttributes.getId());
        assertEquals(actualData.getBuyerAttributeOrder(), ticketHolderRequiredAttributes.getBuyerAttributeOrder());
    }

    @Test
    void test_updateCustomAttributeSequence_success_with_attributeNextPositionItem_null() {

        //setup
        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getNextPositionAttribute(ticketHolderRequiredAttributes)).thenReturn(null);
        when(ticketHolderRequiredAttributesService.getPreviousPositionAttribute(ticketHolderRequiredAttributes)).thenReturn(ticketHolderRequiredAttributes);

        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);
    }

    @Test
    void test_updateCustomAttributeSequence_success_with_attributePreviousPositionItem_null() {

        //setup
        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getNextPositionAttribute(ticketHolderRequiredAttributes)).thenReturn(ticketHolderRequiredAttributes);
        when(ticketHolderRequiredAttributesService.getPreviousPositionAttribute(ticketHolderRequiredAttributes)).thenReturn(null);

        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);
    }

    @Test
    void test_updateCustomAttributeSequence_success1() {

        //setup
        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(10);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));


        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService, times(1)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());

        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(actualData.getId(), ticketHolderRequiredAttributes.getId());
        assertEquals(actualData.getBuyerAttributeOrder(), ticketHolderRequiredAttributes.getBuyerAttributeOrder());
    }

    @Test
    void test_updateCustomAttributeSequence_success_with_topAttributeId_null() {

        //setup
        Long topAttributeId = 2L;

        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(sequence);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.empty());
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        Mockito.doNothing().when(ticketHolderRequiredAttributesService).updatePositionForAllAttribute(ticketHolderRequiredAttributes.getEventid(), sequence, ticketHolderRequiredAttributes.getDataType());

        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService, times(1)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());

        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(actualData.getId(), ticketHolderRequiredAttributes.getId());
        assertEquals(actualData.getBuyerAttributeOrder(), sequence);
    }

    @Test
    void test_updateCustomAttributeSequence_success_with_topAttributeId_null_and_posDiff_greateThan_one() {

        //setup
        Long topAttributeId = 2L;

        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(2000);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.empty());
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));

        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService, times(1)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());

        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(actualData.getId(), ticketHolderRequiredAttributes.getId());
        assertEquals(actualData.getBuyerAttributeOrder(), sequence);
    }

    @Test
    void test_updateCustomAttributeSequence_success_with_topAttributeId_and_topNextAttribute_null() {

        //setup
        Long topAttributeId = 2L;

        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(sequence);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.empty());
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.empty());

        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);
    }

    @Test
    void test_updateCustomAttributeSequence_success_with_topNextAttribute_null() {

        //setup
        Long topBottomAttributeId = 2L;

        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1000);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topBottomAttributeId)).thenReturn(Optional.empty());
        when(ticketHolderRequiredAttributesService.getTicketHolderAttributeById(topAttributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));



        //Execution
        ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService, times(1)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());

        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(actualData.getId(), ticketHolderRequiredAttributes.getId());
        assertEquals(actualData.getBuyerAttributeOrder(), ticketHolderRequiredAttributes.getBuyerAttributeOrder());
    }

    @Test
    void test_updateCustomAttributeSequence_throwException_ATTRIBUTE_NOT_EXISTS() {

        //setup
        ticketHolderRequiredAttributes.setId(1L);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(customAttributeId)).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketHolderAttributesServiceImpl.updateCustomAttributeSequence(customAttributeId, topAttributeId, topBottomAttributeId, 0L, event, user, true));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_convertTo_success_with_holder_and_purchaser_null() {

        //setup
        ticketAttributeValueDto1 = new TicketAttributeValueDto1();

        ticketAttributeValueDto1.setHolder(null);
        ticketAttributeValueDto1.setPurchaser(null);

        //Execution
        TicketAttributeValueDto ticketAttributeValueDtoData = ticketHolderAttributesServiceImpl.convertTo(ticketAttributeValueDto1);

        assertNull(ticketAttributeValueDtoData.getHolder());
        assertNull(ticketAttributeValueDtoData.getPurchaser());
    }

    private void getTicketAttributeValueDto() {
        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);

        List<AttributeKeyValueDto> attributeKeyValueDtosHolderAttribute = new ArrayList<>();
        attributeKeyValueDtosHolderAttribute.add(holderAttributeKeyValueDtoAttribute);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserAttribute = new ArrayList<>();
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserQuestion = new ArrayList<>();
        attributeKeyValueDtosPurchaserQuestion.add(purchaserAttributeKeyValueDtoQuestion);

        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtosHolderAttribute);
        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);

        valueDtoPurchaser = new ValueDto();
        valueDtoPurchaser.setAttributes(attributeKeyValueDtosPurchaserAttribute);
        valueDtoPurchaser.setQuestions(attributeKeyValueDtosPurchaserQuestion);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);
        ticketAttributeValueDto.setPurchaser(valueDtoPurchaser);
    }
}