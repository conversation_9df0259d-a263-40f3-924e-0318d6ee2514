package com.accelevents.services.impl;

import com.accelevents.common.dto.CreditCardChargesDto;
import com.accelevents.common.dto.SearchAttendeeDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.domain.enums.TicketTypeFormat;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.domain.virtual.CheckInAuditLog;
import com.accelevents.dto.*;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.TwilioTextMessagePrepareService;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.ro.entryexit.ROEntryExitSettingsRepository;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.dynamodb.user.activity.UserActivityService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.services.SpeakerService;
import com.accelevents.ticketing.dto.StaffTicketingOrderDto;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.ticketing.dto.TicketingFeeDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.*;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingCheckInServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingCheckInServiceImpl ticketingCheckInServiceImpl;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private StaffRoleService staffRoleService;
    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;
    @Mock
    private StripeService stripeService;
    @Mock
    private WaitListService waitListService;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private TwilioTextMessagePrepareService twilioTextMessagePrepareService;
    @Mock
    private EventService eventService;
    @Mock
    private StripeCreditCardDto stripeCreditCardDto;
    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Mock
    private TicketingStatisticsService ticketingStatisticsService;
    @Mock
    private StaffService staffService;
    @Mock
    private ROStaffService roStaffService;
    @Mock
    private CheckInAuditLogService checkInAuditLogService;
    @Mock
    private SeatsIoService seatsIoService;
    @Mock
    private SalesTaxService salesTaxService;
   @Mock
   private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
   @Mock
   private SpeakerService speakerService;
   @Mock
   private VirtualEventPortalService virtualEventPortalService;
   @Mock
   private VatTaxService vatTaxService;
   @Mock
   private EventTicketsRepository eventTicketsRepository;
   @Mock
   private TrayIntegrationService trayIntegrationService;
   @Mock
   private EventTicketsService eventTicketsService;
   @Mock
   private BadgesService badgesService;
   @Mock
   private ChallengeConfigService challengeConfigService;
   @Mock
   private ROStaffRoleService roStaffRoleService;

   @Mock
   private ROEventLevelSettingService roEventLevelSettingService;

    @Mock
    private UserActivityService userActivityService;
    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private ROEntryExitSettingsRepository roEntryExitSettingsRepository;

    private Ticketing ticketing;
    private Event event;
    private EventTickets  eventTickets;
    private TicketStatus ticketStatus;
    private TicketingType ticketingType;
    private StaffTicketingOrderDto staffTicketingOrderDto;
    private User user;
    private TicketingOrder ticketingOrder;
    private TicketingTable ticketingTable;
    private RecurringEvents recurringEvents;
    private TicketHolderAttributes ticketHolderAttributes;
    private StripeDTO stripeDTO;
    private WaitList waitList;
    private TransactionFeeConditionalLogic transactionFeeConditionalLogic;
    private CreditCardChargesDto creditCardChargesDto;
    private TicketDisplayPageDto ticketDisplayPageDto;
	private TicketTypeDto ticketTypeDto;
    private Staff staff;
    private Speaker speaker;

    private Long id = 1L;
    private static String startDate = "2019/01/01 05:30";
    private static Date startDate1 = new Date(startDate);
    private String firstName = "Jon";
    private String email = "<EMAIL>";
    private String barcodeId = "1d44e288-38f2-425e-929f-68a014b08c88";
    private String chartKey = "chart key";
    private String seatNumber = "D1-15";
	private long remainingTicket= 1L;
	private String device = "Chrome 8 from Computer";
    private String source = "DESKTOP";
    private String sourceDescription = "{\"browser\":\"Chrome\",\"browserVersion\":\"*********\",\"os\":\"Linux\",\"osVersion\":\"Unknown\",\"deviceType\":\"Desktop\",\"userAgent\":\"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"platform\":\"Linux x86_64\"}";
    private SalesTaxFeeDto salesTaxFeeDto;
    private Long orderid = 1L;
    private SearchAttendeeDto searchAttendeeDto;

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.HYBRID);
        ticketing = EventDataUtil.getTicketing(event);
        ticketingType = EventDataUtil.getTicketingType(event);
        ticketingType.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
        eventTickets = EventDataUtil.getEventTickets(event);
        eventTickets.setTicketingTypeId(ticketingType);
        eventTickets.setSeatNumber(seatNumber);
        ticketStatus = TicketStatus.REGISTERED;
        staffTicketingOrderDto = EventDataUtil.getStaffTicketingOrderDto();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        user = EventDataUtil.getUser();
        user.setEmail("<EMAIL>");
        recurringEvents = EventDataUtil.getRecurringEvents();
        searchAttendeeDto=new SearchAttendeeDto();
        ticketingTable = new TicketingTable();
		long tableNumberSequence = 1L;
		ticketingTable.setTableNoSequence(tableNumberSequence);
        ticketingTable.setId(id);
        ticketingTable.setEventId(event);

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);
        ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue());
        staff = new Staff();
        staff.setId(1L);
        staff.setUser(user);
        staff.setEvent(event);

        salesTaxFeeDto = new SalesTaxFeeDto(true,10d,"1");

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        speaker = new Speaker();
        speaker.setUserId(user.getUserId());
        speaker.setEvent(event);
    }

    private void setSalesTaxDtoObject(String ticketingTypeIds, boolean isAbsorbtax, double salesTaxRate) {
        salesTaxFeeDto = new SalesTaxFeeDto();
        salesTaxFeeDto.setTicketingTypeIds(ticketingTypeIds);
        salesTaxFeeDto.setAbsorbTax(isAbsorbtax);
        salesTaxFeeDto.setSalesTaxRate(salesTaxRate);
    }

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_success() throws IOException{

        //setup
        event.setRaffleEnabled(true);
        event.setSilentAuctionEnabled(true);
        event.setCauseAuctionEnabled(true);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event, device,false ,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/


    //TODO: Junit5 review test case
    /*@Test
    void whenTicketRefundedCheckInsSuccessfully() throws IOException{

        //setup
        event.setRaffleEnabled(true);
        event.setSilentAuctionEnabled(true);
        event.setCauseAuctionEnabled(true);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event, device,false ,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_search() throws IOException{

        //setup
        event.setRaffleEnabled(true);
        event.setSilentAuctionEnabled(true);
        event.setCauseAuctionEnabled(true);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,null,user,event, device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);
        verify(eventTicketsService).getEventTicketForKioskCheckInOrThrowError(any(), any());

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/


    @Test
    void test_checkInTicket_search_email_success() throws IOException {

        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(user);

        searchAttendeeDto.setEmail(user.getEmail());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());

        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false)).thenReturn(eventTickets);
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(Collections.singletonList(Constants.ATTENDEE.toLowerCase()));

        when(recurringEventsScheduleService.getRecurringEventById(eventTickets.getRecurringEventId())).thenReturn(Optional.of(recurringEvents));
        when(roStaffService.findByEventIdAndUserIdAndRole(anyLong(), anyLong())).thenReturn(staff);
        when(badgesService.getTicketingIdAndBadgesIdByEventId(event.getEventId())).thenReturn(Collections.emptyList());



        //Execution
        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, null, user, event, device,false,source,sourceDescription, false);

        //Assertion
        verify(roStaffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(), isNull(), any(), anyBoolean(),any(),any());
        verify(eventTicketsService).getEventTicketForKioskCheckInOrThrowError(any(), any(), anyBoolean());
        assertNotNull(ticketCheckInDto);
        assertTrue(ticketCheckInDto.isPaid());

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }

    @Test
    void test_checkInTicket_search_ticket_number_success() throws IOException {

        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(user);

        searchAttendeeDto.setTicketNumber(eventTickets.getId());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());

        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false)).thenReturn(eventTickets);
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(Collections.singletonList(Constants.ATTENDEE.toLowerCase()));
        when(recurringEventsScheduleService.getRecurringEventById(eventTickets.getRecurringEventId())).thenReturn(Optional.of(recurringEvents));
        when(roStaffService.findByEventIdAndUserIdAndRole(anyLong(), anyLong())).thenReturn(staff);
        when(badgesService.getTicketingIdAndBadgesIdByEventId(event.getEventId())).thenReturn(Collections.emptyList());



        //Execution
        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, null, user, event, device,false,source,sourceDescription,false);

        //Assertion
        verify(roStaffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(), isNull(), any(), anyBoolean(),any(),any());
        verify(eventTicketsService).getEventTicketForKioskCheckInOrThrowError(any(), any(), anyBoolean());
        assertNotNull(ticketCheckInDto);
        assertTrue(ticketCheckInDto.isPaid());

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }


    @Test
    void test_checkInTicket_search_ticket_number_and_email_success() throws IOException {

        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(user);

        searchAttendeeDto.setTicketNumber(eventTickets.getId());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());
        searchAttendeeDto.setEmail(user.getEmail());

        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false)).thenReturn(eventTickets);
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(Collections.singletonList(Constants.ATTENDEE.toLowerCase()));
        when(recurringEventsScheduleService.getRecurringEventById(eventTickets.getRecurringEventId())).thenReturn(Optional.of(recurringEvents));
        when(roStaffService.findByEventIdAndUserIdAndRole(anyLong(), anyLong())).thenReturn(staff);
        when(badgesService.getTicketingIdAndBadgesIdByEventId(event.getEventId())).thenReturn(Collections.emptyList());



        //Execution
        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, null, user, event, device,false,source,sourceDescription,false);

        //Assertion
        verify(roStaffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(), isNull(), any(), anyBoolean(),any(),any());
        verify(eventTicketsService).getEventTicketForKioskCheckInOrThrowError(any(), any(), anyBoolean());
        assertNotNull(ticketCheckInDto);
        assertTrue(ticketCheckInDto.isPaid());

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }

    @Test
    void test_checkInTicket_search_ticket_number_and_email_not_found() throws IOException {

        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        searchAttendeeDto.setTicketNumber(eventTickets.getId());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());
        searchAttendeeDto.setEmail(user.getEmail());

        EventLevelSettings eventLevelSettings = new EventLevelSettings();
        eventLevelSettings.setRecheckInEnabled(false);
        eventLevelSettings.setEventId(event.getEventId());


        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false)).thenReturn(null);
        when(roEventLevelSettingService.isRecheckInEnabledByEventId(event)).thenReturn(Optional.of(eventLevelSettings.isRecheckInEnabled()));

        //Execution
        NotAcceptableException exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, null, user, event, device,false,source,sourceDescription, false));
        //Assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.NO_SUCH_EVENT_TICKET_EXIST.getErrorMessage(), exception.getErrorMessage());
        verify(eventTicketsService).getEventTicketForKioskCheckInOrThrowError(any(), any(), anyBoolean());
    }

    @Test
    void test_checkInTicket_eventLevelRecheckInDisabled_ticketAlreadyCheckedIn(){
        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);

        searchAttendeeDto.setTicketNumber(eventTickets.getId());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());
        searchAttendeeDto.setEmail(user.getEmail());

        EventLevelSettings eventLevelSettings = new EventLevelSettings();
        eventLevelSettings.setRecheckInEnabled(false);
        eventLevelSettings.setEventId(event.getEventId());

        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false)).thenReturn(eventTickets);
        when(roEventLevelSettingService.isRecheckInEnabledByEventId(event)).thenReturn(Optional.of(eventLevelSettings.isRecheckInEnabled()));

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, null, user, event, device,false,source,sourceDescription, false));

        //Assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_CHECKED_IN.getErrorMessage(), exception.getMessage());
        verify(roEventLevelSettingService).isRecheckInEnabledByEventId(any());

    }

    @Test
    void test_checkInTicket_eventLevelRecheckInEnabled_ticketAlreadyCheckedIn() throws IOException {
        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);

        searchAttendeeDto.setTicketNumber(eventTickets.getId());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());
        searchAttendeeDto.setEmail(user.getEmail());

        EventLevelSettings eventLevelSettings = new EventLevelSettings();
        eventLevelSettings.setRecheckInEnabled(true);
        eventLevelSettings.setEventId(event.getEventId());

        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false)).thenReturn(eventTickets);
        when(roEventLevelSettingService.isRecheckInEnabledByEventId(event)).thenReturn(Optional.of(eventLevelSettings.isRecheckInEnabled()));

        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, null, user, event, device,false,source,source,false);

        //Assertion
        assertNotNull(ticketCheckInDto);
        assertTrue(ticketCheckInDto.isPaid());
        verify(roEventLevelSettingService).isRecheckInEnabledByEventId(any());

    }


    @Test
    void test_checkInTicket_search_ticket_number_not_found() throws IOException {

        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        searchAttendeeDto.setTicketNumber(eventTickets.getId());
        searchAttendeeDto.setFullName(user.getFirstName() + " " + user.getLastName());

        //mock
        when(eventTicketsService.getEventTicketForKioskCheckInOrThrowError(event, searchAttendeeDto, false)).thenReturn(null);

        //Execution
        NotAcceptableException exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, null, user, event, device,false,source,sourceDescription,false));

        //Assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.NO_SUCH_EVENT_TICKET_EXIST.getErrorMessage(), exception.getErrorMessage());
        verify(eventTicketsService).getEventTicketForKioskCheckInOrThrowError(any(), any(), anyBoolean());
    }



    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_success_with_recurringEVents_CheckInXMinutesBefore_null() throws IOException{

        //setup
        event.setRaffleEnabled(false);
        event.setSilentAuctionEnabled(false);
        event.setCauseAuctionEnabled(true);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets = getEventTicketsDetail();

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event, device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(afterTaskIntegrationTriggerService).userCheckInPostProcess(eventTickets,event);
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/

    private EventTickets getEventTicketsDetail() {
        EventTickets eventTickets = new EventTickets();
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setTicketingTypeId(ticketingType);
        eventTickets.setSeatNumber(seatNumber);
        eventTickets.setTicketingOrder(ticketingOrder);
        return eventTickets;
    }

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_success_with_raffleEnable_false() throws IOException{

        //setup
        event.setRaffleEnabled(false);
        event.setSilentAuctionEnabled(false);
        event.setCauseAuctionEnabled(false);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event, device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_success_with_silentAuctionEnable_and_causeAuctionEnable_true() throws IOException{

        //setup
        event.setRaffleEnabled(false);
        event.setSilentAuctionEnabled(false);
        event.setCauseAuctionEnabled(true);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event, device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/

    //TODO: Junit5 review test case
   /* @Test
    void test_checkInTicket_success_with_ticketHolderAttributes_value_blank() throws IOException{

        //setup
        event.setRaffleEnabled(true);
        event.setSilentAuctionEnabled(false);
        event.setCauseAuctionEnabled(false);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_success_with_chartKey_empty() throws IOException{

        //setup
        event.setRaffleEnabled(false);
        event.setSilentAuctionEnabled(true);
        event.setCauseAuctionEnabled(false);

        ticketing.setChartKey("");

        ticketingType.setTicketing(ticketing);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingTypeId(ticketingType);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }*/

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_throwException_checkInNotAllowedBeforeTime() throws IOException {

        //setup
        Date startDate = DateUtils.addDaysToNow(10);

        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        recurringEvents = new RecurringEvents();
        recurringEvents.setId(id);
        recurringEvents.setCheckInXMinutesBefore(60);
        recurringEvents.setRecurringEventStartDate(startDate);

        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEventId(id);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);
    }*/

    @Test
    void test_checkInTicket_throwException_bracodeAlreadyRefunded() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        NotAcceptableException exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device,false,source,sourceDescription,false));

        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getErrorMessage());
    }

    @Test
    void whenWithCanceledTicketCheckInByStaffThenThrowException() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTickets.setBarcodeId(barcodeId);
        eventTickets.setHolderUserId(user);

        ticketingType.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
        event.setEventFormat(EventFormat.VIRTUAL);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(Collections.singletonList(Constants.ATTENDEE.toLowerCase()));

        //Execution
        NotAcceptableException exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device,false,source,sourceDescription,false));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_REFUNDED_AND_CANCELED.getErrorMessage(), exception.getErrorMessage());
    }

    @Test
    void whenWithCanceledTicketCheckInByStaffInHybridEventThenThrowException() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTickets.setBarcodeId(barcodeId);
        eventTickets.setHolderUserId(user);
        ticketingType.setTicketTypeFormat(TicketTypeFormat.IN_PERSON);
        event.setEventFormat(EventFormat.HYBRID);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(Collections.singletonList(Constants.ATTENDEE.toLowerCase()));

        //Execution
        NotAcceptableException exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device,false,source,sourceDescription,false));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_REFUNDED_AND_CANCELED.getErrorMessage(), exception.getErrorMessage());
    }

    @Test
    void whenNotAllowedToCheckInWithUnPaidTicketAndCheckInByStaffInHybridEventThenThrowException() {

        //setup
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setBarcodeId(barcodeId);
        eventTickets.setHolderUserId(user);
        eventTickets.setPaidAmount(0d);
        eventTickets.setTicketPrice(1d);

        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.UNPAID);
        ticketingType.setTicketTypeFormat(TicketTypeFormat.IN_PERSON);
        event.setEventFormat(EventFormat.HYBRID);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(Collections.singletonList(Constants.ATTENDEE.toLowerCase()));
        when(ticketingRepository.isAllowCheckInWithUnpaidTicketEnabledByEventId(event.getEventId())).thenReturn(false);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device,false,source,sourceDescription,false));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_IS_UNPAID.getErrorMessage(), exception.getMessage());
    }

    @Test
    void whenNotAllowedToCheckInWithPartialPaidTicketAndCheckInByStaffInHybridEventThenAllowedToCheckIn() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setBarcodeId(barcodeId);
        eventTickets.setHolderUserId(user);
        eventTickets.setPaidAmount(1d);
        eventTickets.setTicketPrice(2d);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.UNPAID);
        eventTickets.getTicketingTypeId().setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType.setTicketTypeFormat(TicketTypeFormat.IN_PERSON);
        event.setEventFormat(EventFormat.HYBRID);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(Collections.singletonList(Constants.ATTENDEE.toLowerCase()));
        when(ticketingRepository.isAllowCheckInWithUnpaidTicketEnabledByEventId(event.getEventId())).thenReturn(false);

        //Execution
        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto, barcodeId, user, event, device,false,source,sourceDescription, false);

        //Assertion
        verify(roStaffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(), isNull(), any(), anyBoolean(),any(),any());
        assertNotNull(ticketCheckInDto);
        assertTrue(ticketCheckInDto.isPaid());
        assertEquals(Constants.TICKETING_STATUS_CHECKED_IN, ticketCheckInDto.getStatus());


        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_throwException_bracodeAlreadyCheckedIn() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);
    }*/

    //TODO: Junit5 review test case
    /*@Test
    void test_checkInTicket_throwException_bracodeNotExist() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
        eventTickets.setTicketingTable(EventDataUtil.getTicketingTable(event));

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        
        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,user,event,device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.AttendeeExceptionMsg.ATTENDEE_HAVE_VIRTUAL_TICKET.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),anyLong(),any(),false);
    }*/

    @Test
    void test_checkInTicket_throwAuthorizationException() throws IOException{

        //Execution
        Exception exception = assertThrows(AuthorizationException.class,
                () -> ticketingCheckInServiceImpl.checkInTicket(searchAttendeeDto,barcodeId,null,event,device,false,source,sourceDescription, false));
        assertEquals("Please Sign In", exception.getMessage());
    }

    @Test
    void test_changeTicketStatusToBookFromCheckIn_success() {

        //setup
        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when( roStaffService.findByEventIdAndUserIdAndRole(anyLong(), anyLong())).thenReturn(staff);


        //Execution
        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.changeTicketStatusToBookFromCheckIn(barcodeId,user,event,device,false, source, sourceDescription, false);

        //Assertion
        verify(roStaffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),isNull(),any(),anyBoolean(),any(),any());
        assertNotNull(ticketCheckInDto);
        assertTrue(ticketCheckInDto.isPaid());

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }

    @Test
    void whenTicketStutusIsRefundedCheckinSuccessfully() {

        //setup
        ticketing.setChartKey(chartKey);

        ticketingType.setTicketing(ticketing);

        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when( roStaffService.findByEventIdAndUserIdAndRole(anyLong(), anyLong())).thenReturn(staff);


        //Execution
        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.changeTicketStatusToBookFromCheckIn(barcodeId,user,event,device,false, source, sourceDescription, false);

        //Assertion
        verify(roStaffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),isNull(),any(),anyBoolean(),any(),any());
        assertNotNull(ticketCheckInDto);
        assertTrue(ticketCheckInDto.isPaid());

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }

    @Test
    void test_changeTicketStatusToBookFromCheckIn_success_with_TicketBundleType_INDIVIDUAL_TICKET() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);

        ticketing.setChartKey("");

        ticketingType.setTicketing(ticketing);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setTicketingTypeId(ticketingType);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setHolderUserId(user);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when( roStaffService.findByEventIdAndUserIdAndRole(anyLong(), anyLong())).thenReturn(staff);


        //Execution
        TicketCheckInDto ticketCheckInDto = ticketingCheckInServiceImpl.changeTicketStatusToBookFromCheckIn(barcodeId,user,event,device,false, source, sourceDescription, false);

        //Assertion
        verify(roStaffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),isNull(),any(),anyBoolean(),any(), any());
        assertNotNull(ticketCheckInDto);
        assertFalse(ticketCheckInDto.isPaid());

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(user, actual.getCheckInStaff());
    }

    //TODO: Junit5 review test case
    /*@Test
    void whenTicketStutusIsRefundedthenTHrowException() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.changeTicketStatusToBookFromCheckIn(barcodeId,user,event,device, false,false));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_REFUNDED_AND_CANCELED.getErrorMessage(), exception.getMessage());

        //Assertion
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),isNull(),any(),false);
    }*/

    //TODO: Junit5 review test case
    /*@Test
    void test_changeTicketStatusToBookFromCheckIn_throwException_bracodeAlreadyBookStatus() throws IOException {

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        
        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.changeTicketStatusToBookFromCheckIn(barcodeId,user,event,device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_ALREADY_BOOK_STATUS.getErrorMessage(), exception.getMessage());
        verify(staffService).findByEventIdAndUserIdAndRole(anyLong(), anyLong());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),isNull(),any(),false);
    }*/

    //TODO: Junit5 review test case
    /*@Test
    void test_changeTicketStatusToBookFromCheckIn_throwException_bracodeNotExist() throws IOException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(null);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.changeTicketStatusToBookFromCheckIn(barcodeId,user,event,device, false,false));

        //Assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST.getErrorMessage(), exception.getMessage());
        verify(checkInAuditLogService).setCheckInAuditLogObjectAndSaveInDB(any(), any(), any(), any(), any(), anyString(), any(), anyLong(),isNull(),any(),anyBoolean());
    }*/

    @Test
    void test_changeTicketStatusToBookFromCheckIn_throwAuthorizationException() throws IOException{

        //Execution
        Exception exception = assertThrows(AuthorizationException.class,
                () -> ticketingCheckInServiceImpl.changeTicketStatusToBookFromCheckIn(barcodeId,null,event,device,false, source, sourceDescription, false));
        assertEquals("Please Login", exception.getMessage());
    }

    @Test
    void test_getOrderType_success_with_PaymentType_CC() {

        //setup
        staffTicketingOrderDto.setPaymentType(Constants.CC);

        //Execution
        TicketingOrder.OrderType orderType = ticketingCheckInServiceImpl.getOrderType(staffTicketingOrderDto);
        assertEquals(Constants.CARD,orderType.toString());
    }

    @Test
    void test_getOrderType_success_with_PaymentType_CASH() {

        //setup
        staffTicketingOrderDto.setPaymentType(Constants.CASH);

        //Execution
        TicketingOrder.OrderType orderType = ticketingCheckInServiceImpl.getOrderType(staffTicketingOrderDto);
        assertEquals(staffTicketingOrderDto.getPaymentType(),orderType.toString());
    }

    @Test
    void test_getOrderType_success_with_PaymentType_COMPLEMENTARY() {

        //setup
        staffTicketingOrderDto.setPaymentType(Constants.COMPLIMENTARY);

        //Execution
        TicketingOrder.OrderType orderType = ticketingCheckInServiceImpl.getOrderType(staffTicketingOrderDto);
        assertEquals(staffTicketingOrderDto.getPaymentType(),orderType.toString());
    }

    @Test
    void test_getOrderType_success_with_PaymentType_UNPAID() {

        //setup
        staffTicketingOrderDto.setPaymentType(Constants.UNPAID);

        //Execution
        TicketingOrder.OrderType orderType = ticketingCheckInServiceImpl.getOrderType(staffTicketingOrderDto);
        assertEquals(staffTicketingOrderDto.getPaymentType(),orderType.toString());
    }

    @Test
    void test_getOrderType_throwNotAcceptableException() throws  IOException {

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.getOrderType(staffTicketingOrderDto));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PAYMENT.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_getTicketTYpesDetailsForWaitListCheckout_success() {

        //setup
        String waitlistId = "1";
        double ccFlatFee = 0.3;
        double ccPercentageFee = 2.9;
        long remainingTicket = 1L;
        long recurringEventId = 0L;
        String eventKey = "1";

        ticketingType.setEnableTicketDescription(true);
        ticketingType.setTicketTypeDescription("this ticket is General Admission type ticket.");

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(ccFlatFee);
        stripeDTO.setCCPercentageFee(ccPercentageFee);

        waitList = new WaitList();
        waitList.setEventId(event.getEventId());
        waitList.setTicketingTypeId(ticketingType.getId());

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

        List<Long> waitListIds = new ArrayList<>();
        waitListIds.add(1L);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setEvent(event);
        transactionFeeConditionalLogic.setId(id);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = new ArrayList<>();
        transactionFeeConditionalLogics.add(transactionFeeConditionalLogic);

        creditCardChargesDto = new CreditCardChargesDto(ccFlatFee,ccPercentageFee);


        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        doNothing().when(ticketingCheckInServiceImpl).setTicketingDetails(any(),any());
        when(waitListService.findByIdInAndEventAndStatus(any(), any(), any())).thenReturn(waitLists);

        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogics);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        when(seatsIoService.getEventKey(anyLong(), anyLong())).thenReturn(eventKey);
        doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(event.getEventId());
        //Execution
        TicketDisplayPageDto ticketDisplayPageDtoData = ticketingCheckInServiceImpl.getTicketTYpesDetailsForWaitListCheckout(event,waitlistId, recurringEventId);

        assertTrue(ticketDisplayPageDtoData.getTicketingFee().iterator().next().getCreditCardProcessingFlat() == ccFlatFee);
        assertTrue(ticketDisplayPageDtoData.getTicketingFee().iterator().next().getCreditCardProcessingPercentage() == ccPercentageFee);
    }

    @Test
    void test_getTicketTYpesDetailsForWaitListCheckout_success_with_remainTicket() {

        //setup
        String waitlistId = "null";
        double ccFlatFee = 0.3;
        double ccPercentageFee = 2.9;
        long remainingTicket = 2L;
        long recurringEventId = 0L;
        String eventKey = "1";

        ticketingType.setEnableTicketDescription(true);
        ticketingType.setTicketTypeDescription("this ticket is General Admission type ticket.");

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(ccFlatFee);
        stripeDTO.setCCPercentageFee(ccPercentageFee);

        waitList = new WaitList();
        waitList.setEventId(event.getEventId());
        waitList.setTicketingTypeId(ticketingType.getId());

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

        List<Long> waitListIds = new ArrayList<>();
        waitListIds.add(1L);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setEvent(event);
        transactionFeeConditionalLogic.setId(id);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = new ArrayList<>();
        transactionFeeConditionalLogics.add(transactionFeeConditionalLogic);

        creditCardChargesDto = new CreditCardChargesDto(ccFlatFee,ccPercentageFee);


        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        doNothing().when(ticketingCheckInServiceImpl).setTicketingDetails(any(),any());
        when(waitListService.findByIdInAndEventAndStatus(any(), any(), any())).thenReturn(waitLists);

        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogics);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        when(seatsIoService.getEventKey(anyLong(), anyLong())).thenReturn(eventKey);
        doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(event.getEventId());

        //Execution
        TicketDisplayPageDto ticketDisplayPageDtoData = ticketingCheckInServiceImpl.getTicketTYpesDetailsForWaitListCheckout(event,waitlistId, recurringEventId);

        assertEquals(ticketDisplayPageDtoData.getTicketingFee().iterator().next().getCreditCardProcessingFlat(), ccFlatFee);
        assertEquals(ticketDisplayPageDtoData.getTicketingFee().iterator().next().getCreditCardProcessingPercentage(), ccPercentageFee);
    }

    @Test
    void test_getTicketTYpesDetailsForWaitListCheckout_throwException_invalidWaitListIds() {

        //setup
        String waitlistId = "";
        long recurringEventId = 0L;
        //mock
        when(salesTaxService.getTaxFeeAndTicketTypeId(anyLong())).thenReturn(salesTaxFeeDto);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.getTicketTYpesDetailsForWaitListCheckout(event,waitlistId, recurringEventId));
        assertEquals(NotAcceptableException.WaitListExceptionMsg.INVALID_WAIT_LIST_IDS.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_getTicketStatus_success() {

        //setup
        eventTickets.setTicketStatus(ticketStatus);
        ticketingType.setTicketing(ticketing);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(eventTickets);

        //Execution
        TicketCheckInDto ticketStatus = ticketingCheckInServiceImpl.getTicketStatus(barcodeId);
        assertEquals(eventTickets.getTicketStatus().getStatus(), ticketStatus.getStatus());
    }

    @Test
    void test_getTicketStatus_throwException_barcodeNotExist() throws IOException {

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcodeId)).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.getTicketStatus(barcodeId));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_setTicketingDetails_success(){

        //setup
        ticketDisplayPageDto = new TicketDisplayPageDto();

        //mock
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);

        //Execution
        ticketingCheckInServiceImpl.setTicketingDetails(event,ticketDisplayPageDto);
        assertNotNull(ticketDisplayPageDto);
    }

    @Test
    void test_getTicketFeesLogic_success_with_transactionFeeConditionalLogics_isEmpty_And_whiteLabel(){

        //setup
        double ccFlatFee = 0.3;
        double ccPercentageFee = 2.9;

		WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setId(id);

        event.setWhiteLabel(whiteLabel);

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        creditCardChargesDto = new CreditCardChargesDto(ccFlatFee,ccPercentageFee);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = new ArrayList<>();

        //mock
        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogics);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        //Execution
        List<TicketingFeeDto> getTicketFeesLogic = ticketingCheckInServiceImpl.getTicketFeesLogic(event);
        for (TicketingFeeDto actual : getTicketFeesLogic) {
            assertEquals(actual.getCreditCardProcessingFlat(), ccFlatFee);
            assertEquals(actual.getCreditCardProcessingPercentage(), ccPercentageFee);
        }
    }

    @Test
    void test_getTicketFeesLogic_success_without_whiteLabel(){

        //setup
        double ccFlatFee = 0.3;
        double ccPercentageFee = 2.9;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        creditCardChargesDto = new CreditCardChargesDto(ccFlatFee,ccPercentageFee);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = new ArrayList<>();

        //mock
        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogics);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        //Execution
        List<TicketingFeeDto> getTicketFeesLogic = ticketingCheckInServiceImpl.getTicketFeesLogic(event);
        for (TicketingFeeDto actual : getTicketFeesLogic) {
            assertEquals(actual.getCreditCardProcessingFlat(), ccFlatFee);
            assertEquals(actual.getCreditCardProcessingPercentage(), ccPercentageFee);
        }
    }

    @Test
    void test_sendCheckInTicketEmailAndSMS_success() throws IOException{

        //setup
        Long holderPhoneNumber = 9898989898L;
        String holderCountryCode = "US";

        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setHolderFirstName(firstName);
        eventTickets.setHolderEmail(email);
        eventTickets.setHolderCountryCode(holderCountryCode);
        eventTickets.setHolderPhoneNumber(holderPhoneNumber);

        stripeCreditCardDto = new StripeCreditCardDto();


        user = new User();
        user.setUserId(id);

        Optional<User> userOptional = Optional.of(user);

        //mock
        when(roUserService.findByEmail(email)).thenReturn(user);
        doNothing().when(sendGridMailPrepareService).sendAttendeeCheckInEmail(any(),any(),any(),any(), any());
        doNothing().when(twilioTextMessagePrepareService).sendAttendeeCheckInTextMessage(any(),any(), any());

        //Execution
        String data = ticketingCheckInServiceImpl.sendCheckInTicketEmailAndSMS(eventTickets, new TicketingCheckInServiceImpl.CheckInLog(barcodeId), event, user);
        assertNull(data);
    }


   /* @Test // We have comment this test case. @vikas Please provide your feedback on this.
    void test_sendCheckInTicketEmailAndSMS_success_with_ticketHolder_Null() throws IOException{

        //setup
        Long holderPhoneNumber = 9898989898L;
        String holderCountryCode = "US";

        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setHolderFirstName(firstName);
        eventTickets.setHolderEmail(email);
        eventTickets.setHolderCountryCode(holderCountryCode);
        eventTickets.setHolderPhoneNumber(holderPhoneNumber);

        stripeCreditCardDto = new StripeCreditCardDto();

        List<StripeCreditCardDto> stripeCreditCardDtos = new ArrayList<>();

        user = new User();
        user.setUserId(id);

        Optional<User> userOptional = Optional.of(user);

        //mock
        when(userService.findByEmailOrCellNumberAndCountryCode(email, 0, null)).thenReturn(null);
        doNothing().when(sendGridMailPrepareService).sendAttendeeCheckInEmail(any(),any(),any(),any());
        doNothing().when(twilioTextMessagePrepareService).sendAttendeeCheckInTextMessage(any(),any());
        when(roUserService.getUserById(any())).thenReturn(userOptional);
        when(eventService.getLinkedCreditCard(any(),any())).thenReturn(stripeCreditCardDtos);

        //Execution
        String data = ticketingCheckInServiceImpl.sendCheckInTicketEmailAndSMS(eventTickets, event);
        assertEquals(Constants.BIDDER_CREDIT_CARD_NOT_FOUND, data);
    }*/

    @Test
    void test_addTicketingTypeDetails_withBundleTypeIndividualTicket(){

        //setup
        setSalesTaxDtoObject("16017,16018", true, 10.0);
        setTicketingTypeObject(true, 0d);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketTypeDto = new TicketTypeDto();

        ticketDisplayPageDto = new TicketDisplayPageDto();

        //Execution
        ticketingCheckInServiceImpl.addTicketingTypeDetails(ticketingType, ticketTypeDto, remainingTicket, stripeDTO, salesTaxFeeDto,event);
        assertNotNull(ticketDisplayPageDto);
    }

    private void setTicketingTypeObject(boolean isPassFeesToBuyer, double price) {
        ticketingType.setEnableTicketDescription(true);
        ticketingType.setTicketTypeDescription("this ticket is General Admission type ticket.");
        ticketingType.setEnableTicketDescription(true);
		double position = 1000d;
		ticketingType.setPosition(position);
        ticketingType.setPassfeetobuyer(isPassFeesToBuyer);
        ticketingType.setPrice(price);
    }

    @Test
    void test_addTicketingTypeDetails__withBundleTypeTable(){

        //setup
        setSalesTaxDtoObject("16017", false, 10.0);
        setTicketingTypeObject(true, 1d);

        ticketTypeDto = new TicketTypeDto();

        ticketDisplayPageDto = new TicketDisplayPageDto();

        //Execution
        ticketingCheckInServiceImpl.addTicketingTypeDetails(ticketingType, ticketTypeDto, remainingTicket, stripeDTO, salesTaxFeeDto,event);
        assertNotNull(ticketDisplayPageDto);
    }

    @Test
    void test_getHolderDetail__throwBarcodeNotExist(){
        //mock
        when(eventTicketsRepoService.findByBarcodeIdAndEventId(anyString(),anyLong())).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.getHolderDetail(barcodeId,1L));

        //Assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST.getErrorMessage(), exception.getMessage());
        verify(eventTicketsRepoService).findByBarcodeIdAndEventId(anyString(),anyLong());
    }

    @Test
    void test_getHolderDetail__success(){
        //setup
        eventTickets = setEventTicketDetails();

        //mock
        when(eventTicketsRepoService.findByBarcodeIdAndEventId(anyString(),anyLong())).thenReturn(eventTickets);

        //Execution
        TicketHolderDetailDto holderDetail = ticketingCheckInServiceImpl.getHolderDetail(barcodeId,1L);

        //Assertion
        assertEquals(holderDetail.getHolderEmail(),eventTickets.getHolderEmail());
        assertEquals(holderDetail.getHolderFirstName(),eventTickets.getHolderFirstName());
        assertEquals(holderDetail.getHolderLastName(),eventTickets.getHolderLastName());
        assertEquals(holderDetail.getHolderPhoneNumber(),eventTickets.getHolderPhoneNumber());
        assertEquals(holderDetail.getHolderCountryCode(),eventTickets.getHolderCountryCode());
        assertEquals(holderDetail.getEventTicketId(),eventTickets.getId());

        verify(eventTicketsRepoService).findByBarcodeIdAndEventId(anyString(),anyLong());
    }

    private EventTickets setEventTicketDetails() {
        eventTickets.setHolderEmail(email);
        eventTickets.setHolderFirstName(firstName);
        eventTickets.setHolderLastName(firstName);
        eventTickets.setHolderPhoneNumber(8568888444l);
        eventTickets.setHolderCountryCode("US");
        return eventTickets;
    }

    @Test
    void test_changeOrderTicketsStausCheckIn_throwEventTicketsNotFound(){

        //Mock
        when(eventTicketsRepoService.findAllByTicketingOrderIdAndTicketStatusBooked(orderid,TicketStatus.REGISTERED)).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCheckInServiceImpl.changeOrderTicketsStausCheckIn(orderid,event,user,device, false));

        //Assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.EVENT_TICKETS_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(eventTicketsRepoService).findAllByTicketingOrderIdAndTicketStatusBooked(orderid,TicketStatus.REGISTERED);
    }

    @Test
    void test_changeOrderTicketsStausCheckIn_success(){

        //Setup
        eventTickets = getEventTicketsDetail();
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //Mock
        when(eventTicketsRepoService.findAllByTicketingOrderIdAndTicketStatusBooked(orderid,TicketStatus.REGISTERED)).thenReturn(eventTicketsList);
        when(recurringEventsScheduleService.getRecurringEventById(eventTickets.getRecurringEventId())).thenReturn(Optional.of(recurringEvents));
        when(roStaffService.findByEventIdAndUserIdAndRole(event.getEventId(),user.getUserId())).thenReturn(staff);
        doNothing().when(eventTicketsRepoService).updateTicketStatusCheckInStaffAndCheckInDateByEventTickets(anyList(),any(),any(),any());

        //Execution
        ticketingCheckInServiceImpl.changeOrderTicketsStausCheckIn(orderid,event,user,device, false);

        //Assetion

        Class<ArrayList<CheckInAuditLog>> listClass = (Class<ArrayList<CheckInAuditLog>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<CheckInAuditLog>> checkInAuditLogArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(checkInAuditLogService, times(1)).saveAll(checkInAuditLogArgumentCaptor.capture());
        List<CheckInAuditLog> checkInAuditLogList = checkInAuditLogArgumentCaptor.getValue();

        assertEquals(checkInAuditLogList.get(0).getTicketStatus(),TicketStatus.CHECKED_IN.name());
        assertEquals(checkInAuditLogList.get(0).getEventid(),event);
        assertEquals(checkInAuditLogList.get(0).getStaffId().longValue(),staff.getId());
        assertEquals(checkInAuditLogList.get(0).getAuditTime().toString(),DateUtils.getCurrentDate().toString());
        assertEquals(checkInAuditLogList.get(0).getTicketingTypeId(),eventTicketsList.get(0).getTicketingTypeId());

        verify(eventTicketsRepoService).findAllByTicketingOrderIdAndTicketStatusBooked(orderid,TicketStatus.REGISTERED);
        verify(recurringEventsScheduleService).getRecurringEventById(eventTickets.getRecurringEventId());
        verify(roStaffService).findByEventIdAndUserIdAndRole(event.getEventId(),user.getUserId());
        verify(eventTicketsRepoService).updateTicketStatusCheckInStaffAndCheckInDateByEventTickets(anyList(),any(),any(),any());
    }

}