package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.TicketTypeFormat;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.*;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.TimeZoneUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.accelevents.utils.Constants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketTypeServiceImplTest {

    @Spy
    @InjectMocks
    private TicketTypeServiceImpl ticketTypeServiceImpl = new TicketTypeServiceImpl();

    @Mock
    private SeatsIoForMultipleEventsImpl seatsIoForMultipleEventsImpl;
    
    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private TicketingRepository ticketingRepository;

    @Mock
    private TicketingTypeService ticketingTypeService;

    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Mock
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Mock
    private TicketingCouponService ticketingCouponService;

    @Mock
    private TicketingAccessCodeService ticketingAccessCodeService;

    @Mock
    private TicketingCouponRepository ticketingCouponRepository;

    @Mock
    private TicketingTypeRepository ticketingTypeRepository;

    @Mock
    private TicketingAccessCodeRepository ticketingAccessCodeRepository;

    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock
    private TicketingService ticketingService;

    @Mock
    private TicketingStatisticsService ticketingStatisticsService;

    @Mock
    private StripeService stripeService;

    @Mock
    private SeatsIoService seatsIoService;

    @Mock
    private SeatingCategoryRepository seatingCategoryRepository;

    @Mock
    private TicketingManageService ticketingManageService;

    private Ticketing ticketing;
    private TicketingTicketTypeDto ticketingTicketTypeDto;
    private TicketTypeSettingDto ticketTypeSettingDto;
    private TicketingCoupon ticketingCoupon;
    private TicketingAccessCode ticketingAccessCode;
    private TicketingType ticketingType;
    private Event event;
    private RecurringEvents recurringEvents;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private SeatingCategories seatingCategories, seatingCategoriesNew;
    private EventCategoryDto eventCategoryDto;

    private Long id = 1L;
    private long key = 1L;
    private String chartKey = "Seating Chart";
    private String color = "#D1D1D1";
    private Long createdFrom = -1L;
    private Double price = 10D;
    private String startDate = "2019/01/01 05:30";
    private String endDate = "2019/04/04 05:30";
    private String ticketTypeName ="ticketTypeName";
    private String ticketTypeId = "1";
    private String timeZone = "Asia/Calcutta";
    private String categoryName = "Category Name";
    private Integer recurringEventSalesEndTime = 50;
    private  String categories = "1";
    private Long countCategoryInTicketType = 2L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        ticketing = EventDataUtil.getTicketing(event);
        ticketingType = EventDataUtil.getTicketingType(event);
        ticketTypeSettingDto = EventDataUtil.getTicketTypeSettingDtoData(event);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
        recurringEvents = EventDataUtil.getRecurringEvents();
        ticketingTicketTypeDto = EventDataUtil.getTicketingTicketTypeDto();
    }

    @Test
    void test_saveTicketType_success_with_paidTicketExistsNow(){

        //setup
        ticketTypeSettingDto.setCreatedFrom(null);

        ticketingTicketTypeDto.setTicketType(ticketTypeSettingDto);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();
        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.getTicketTypeByCreateFrom(ticketTypeSettingDto.getTypeId())).thenReturn(ticketingTypes);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(true).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doNothing().when(ticketTypeServiceImpl).isTicketTypeApplicableForDiscountCodeAndAccessCode(any(Event.class), any(TicketingType.class),any());
        doReturn(false).when(stripeService).isStripeConnected(any(Event.class));

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertFalse(actual.getActivated());
    }

    @Test
    void test_saveTicketType_success_with_paidTicketExistsNow_and_recurringEventId_zero(){

        //setup
        ticketTypeSettingDto.setCreatedFrom(null);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketing.setRecurringEvent(false);
        ticketing.setActivated(true);
        ticketingTicketTypeDto.setTicketType(ticketTypeSettingDto);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();
        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(true).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doNothing().when(ticketTypeServiceImpl).isTicketTypeApplicableForDiscountCodeAndAccessCode(any(Event.class), any(TicketingType.class),any());
        doReturn(true).when(stripeService).isStripeConnected(any(Event.class));

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertTrue(actual.getActivated());
    }

    @Test
    void test_saveTicketType_success_with_paidTicketExistsNow_true(){

        //setup
        ticketTypeSettingDto.setCreatedFrom(null);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketing.setRecurringEvent(false);
        ticketing.setActivated(false);
        ticketingTicketTypeDto.setTicketType(ticketTypeSettingDto);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();

        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(true).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doNothing().when(ticketTypeServiceImpl).isTicketTypeApplicableForDiscountCodeAndAccessCode(any(Event.class), any(TicketingType.class),any());

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertFalse(actual.getActivated());
    }

    @Test
    void test_saveTicketType_success_with_paidTicketExistsNow_and_isRecurringEvent_false(){

        //setup
        ticketing.setRecurringEvent(false);
        ticketTypeSettingDto.setCreatedFrom(null);

        ticketingTicketTypeDto.setTicketType(ticketTypeSettingDto);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(id);

        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        when(ticketingTypeService.findByidAndEvent(anyLong(), any())).thenReturn(ticketingType);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(true).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doReturn(false).when(stripeService).isStripeConnected(any(Event.class));

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertFalse(actual.getActivated());
    }

    @Test
    void test_saveTicketType_success_with_paidTicketExistsNow_and_typeId_zero(){

        //setup
        ticketTypeSettingDto.setCreatedFrom(null);
        ticketTypeSettingDto.setTypeId(0L);

        ticketingTicketTypeDto.setTicketType(ticketTypeSettingDto);

        ticketingType.setTicketType(TicketType.PAID);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();
        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(true).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doNothing().when(ticketTypeServiceImpl).isTicketTypeApplicableForDiscountCodeAndAccessCode(any(Event.class), any(TicketingType.class),any());
        doReturn(false).when(stripeService).isStripeConnected(any(Event.class));

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertFalse(actual.getActivated());
    }

    @Test
    void test_saveTicketType_success(){

        //setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();
        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(false).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doNothing().when(ticketTypeServiceImpl).isTicketTypeApplicableForDiscountCodeAndAccessCode(any(Event.class), any(TicketingType.class),any());

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertTrue(actual.getActivated());
    }

    @Test
    void test_saveTicketType_success_with_ticketCategory(){

        ticketingTicketTypeDto.setSeating(true);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();

        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        seatingCategories = new SeatingCategories();
        seatingCategories.setId(id);
        seatingCategories.setHavingVariations(true);
        seatingCategories.setName(categoryName);

        eventCategoryDto = new EventCategoryDto();
        eventCategoryDto.setId(id);
        eventCategoryDto.setName(categoryName);
        eventCategoryDto.setHavingVariations(true);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(false).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doNothing().when(ticketTypeServiceImpl).isTicketTypeApplicableForDiscountCodeAndAccessCode(any(Event.class), any(TicketingType.class),any());

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());
        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertTrue(actual.getActivated());
    }

    @Test
    void test_saveTicketType_success_without_ticketCategory(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(id);
        ticketTypeSettingDto.setCreatedFrom(id);

        ticketingTicketTypeDto.setSeating(true);
        ticketingTicketTypeDto.setTicketType(ticketTypeSettingDto);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<Long> ticketTypeIds = new ArrayList<>();

        List<RecurringEvents> recurringEventsList = getRecurringEventsList();

        seatingCategories = new SeatingCategories();
        seatingCategories.setId(id);
        seatingCategories.setHavingVariations(true);
        seatingCategories.setName(categoryName);

        eventCategoryDto = new EventCategoryDto();
        eventCategoryDto.setId(id);
        eventCategoryDto.setName(categoryName);
        eventCategoryDto.setHavingVariations(true);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findAllIdByEventId(event)).thenReturn(ticketTypeIds);
        when(recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(event)).thenReturn(recurringEventsList);
        doReturn(ticketingType).when(ticketTypeServiceImpl).copyTicket(any(), any(), any(), any(Map.class), any(), any(), anyBoolean());
        doReturn(false).when(ticketTypeServiceImpl).isPaidTicketExistsNow(anyBoolean(), any(TicketTypeSettingDto.class));
        doNothing().when(ticketTypeServiceImpl).isTicketTypeApplicableForDiscountCodeAndAccessCode(any(Event.class), any(TicketingType.class),any());
        doNothing().when(recurringEventsMainScheduleService).createTicketType(any(),any(),any());

        //Execution
        ticketTypeServiceImpl.saveTicketType(ticketingTicketTypeDto,event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actual = ticketingArgumentCaptor.getValue();
        assertNotNull(actual);
        assertTrue(actual.isShowRemainingTickets());
        assertTrue(actual.getActivated());
    }

    @Test
    void test_setTicketTypeEndDate_success_with_recurringEvent_RecurringEventSalesEndStatus_END() {

        //setup
        recurringEvents = new RecurringEvents();
        recurringEvents.setRecurringEventStartDate(ticketing.getEventStartDate());
        recurringEvents.setRecurringEventEndDate(ticketing.getEventEndDate());
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.END);
        ticketingTicketTypeDto.setSeating(true);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock
        when(recurringEventsScheduleService.getRecurringEvents(any())).thenReturn(recurringEvents);
        when(ticketingService.getStringDateWithAddedRecurringEventEndTime(any(),any())).thenReturn(endDate);

        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
        assertEquals(endDate,ticketTypeSettingDto.getEndDate());
    }

    @Test
    void test_setTicketTypeEndDate_success_with_recurringEventId_null() {

        //setup
        TicketTypeSettingDto ticketTypeSettingDto = new TicketTypeSettingDto();
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
    }



    @Test
    void test_setTicketTypeEndDate_success_with_recurringEvent_RecurringEventSalesEndStatus_START() {

        //setup
        recurringEvents = new RecurringEvents();
        recurringEvents.setRecurringEventStartDate(ticketing.getEventStartDate());
        recurringEvents.setRecurringEventEndDate(ticketing.getEventEndDate());
        ticketingTicketTypeDto.setSeating(true);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock
        when(recurringEventsScheduleService.getRecurringEvents(any())).thenReturn(recurringEvents);
        when(ticketingService.getStringDateWithAddedRecurringEventEndTime(any(),any())).thenReturn(endDate);

        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
        assertEquals(endDate,ticketTypeSettingDto.getEndDate());
    }

    @Test
    void test_setTicketTypeEndDate_success_with_recurringEvent() {

        //setup
        recurringEvents = new RecurringEvents();
        recurringEvents.setRecurringEventStartDate(ticketing.getEventStartDate());
        recurringEvents.setRecurringEventEndDate(ticketing.getEventEndDate());

        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(id);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(recurringEventSalesEndTime);
        ticketingTicketTypeDto.setSeating(true);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock
        when(recurringEventsScheduleService.getRecurringEvents(any())).thenReturn(recurringEvents);
        when(ticketingService.getStringDateWithAddedRecurringEventEndTime(any(),any())).thenReturn(endDate);

        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
        assertEquals(endDate,ticketTypeSettingDto.getEndDate());
    }

    @Test
    void test_setTicketTypeEndDate_success_with_endDate_Null() {

        //setup
        String endDate = TimeZoneUtil.getEndDateString(ticketing.getEventEndDate(), timeZone);

        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(id);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(recurringEventSalesEndTime);
        ticketTypeSettingDto.setStartDate(startDate);
        ticketTypeSettingDto.setTypeId(id);
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setPrice(price);
        ticketTypeSettingDto.setCategoryColor(color);
        ticketTypeSettingDto.setHidden(true);
        ticketingTicketTypeDto.setSeating(true);
        ticketing.setRecurringEvent(false);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();
        //mock
        doReturn(1).when(ticketTypeServiceImpl).getTotalTickets(any());
        doReturn(2L).when(ticketTypeServiceImpl).getTotalTicketsDb(ticketingType);

        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
        assertEquals(endDate,ticketTypeSettingDto.getEndDate());
    }

    @Test
    void test_setTicketTypeEndDate_success_with_startDate_Null() {

        //setup
        String startDate = TimeZoneUtil.getEndDateString(ticketing.getEventStartDate(), timeZone);

        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(id);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(recurringEventSalesEndTime);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setTypeId(id);
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setPrice(price);
        ticketTypeSettingDto.setCategoryColor(color);
        ticketTypeSettingDto.setHidden(true);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock

        doReturn(1).when(ticketTypeServiceImpl).getTotalTickets(any());
        doReturn(2L).when(ticketTypeServiceImpl).getTotalTicketsDb(ticketingType);

        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
        assertEquals(startDate,ticketTypeSettingDto.getStartDate());
    }

    @Test
    void test_setTicketTypeEndDate_success1() {

        //setup
        String startDate = TimeZoneUtil.getEndDateString(ticketing.getEventStartDate(), timeZone);

        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(id);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(recurringEventSalesEndTime);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setTypeId(id);
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setPrice(price);
        ticketTypeSettingDto.setCategoryColor(color);
        ticketTypeSettingDto.setHidden(true);

        ticketingTicketTypeDto.setSeating(false);

        ticketing.setRecurringEvent(false);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock




        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
        assertEquals(startDate,ticketTypeSettingDto.getStartDate());
    }

    @Test
    void test_setTicketTypeEndDate_success2() {

        //setup
        String startDate = TimeZoneUtil.getEndDateString(ticketing.getEventStartDate(), timeZone);

        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(id);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(recurringEventSalesEndTime);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setTypeId(id);
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setPrice(price);
        ticketTypeSettingDto.setCategoryColor(color);
        ticketTypeSettingDto.setHidden(true);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock

        doReturn(2).when(ticketTypeServiceImpl).getTotalTickets(any());
        doReturn(1L).when(ticketTypeServiceImpl).getTotalTicketsDb(ticketingType);

        //Execution
        ticketTypeServiceImpl.setTicketTypeEndDate(ticketingTicketTypeDto,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,timeZone);
        assertEquals(startDate,ticketTypeSettingDto.getStartDate());
    }

    @Test
    void test_getTicketTypes_success_with_ticketingTypeExist() {

        //setup
        recurringEvents = new RecurringEvents();
        recurringEvents.setId(id);
        recurringEvents.setEventId(event);

        ticketingType.setRecurringEvent(recurringEvents);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.getTicketingTypes(any(), any(), any(), any(), any())).thenReturn(ticketingTypes);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        //Excecution
        List<GetTicketTypeSettingDto> ticketTypes = ticketTypeServiceImpl.getTicketTypes(event, false, id);
        assertNotNull(ticketTypes);
        for (GetTicketTypeSettingDto actual : ticketTypes) {
            assertEquals(ticketingType.getId(), actual.getTypeId().longValue());
            assertFalse(actual.getChnageToTabel());
            assertEquals(ticketingType.getRecurringEvent().getId(), (long) actual.getRecurringEventId());
            assertEquals(ticketingType.getCreatedFrom(), actual.getCreatedFrom());
            assertEquals(ticketingType.getRecurringEventSalesEndTime(), actual.getRecurringEventSalesEndTime());
            assertEquals(ticketingType.getRecurringEventSalesEndStatus(), actual.getRecurringEventSalesEndStatus());
        }
    }

    @Test
    void test_getTicketTypes_success_with_ticketingType_empty() {

        //setup
        List<TicketingType> ticketingTypes = new ArrayList<>();

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.getTicketingTypes(any(), any(), any(), any(), any())).thenReturn(ticketingTypes);

        //Excecution
        List<GetTicketTypeSettingDto> ticketTypes = ticketTypeServiceImpl.getTicketTypes(event, false, null);

        assertTrue(ticketTypes.isEmpty());
    }

    @Test
    void test_getTicketTypes_success() {

        //setup
        recurringEvents = new RecurringEvents();
        recurringEvents.setId(id);
        recurringEvents.setEventId(event);

        ticketingType.setRecurringEvent(recurringEvents);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.getTicketingTypes(any(), any(), any(), any(), any())).thenReturn(ticketingTypes);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);

        //Excecution
        List<GetTicketTypeSettingDto> ticketTypes = ticketTypeServiceImpl.getTicketTypes(event, false, id);

        assertNotNull(ticketTypes);
        for (GetTicketTypeSettingDto actual : ticketTypes) {
            assertEquals(ticketingType.getId(), actual.getTypeId().longValue());
            assertTrue(actual.getChnageToTabel());
            assertEquals(ticketingType.getRecurringEvent().getId(), (long) actual.getRecurringEventId());
            assertEquals(ticketingType.getCreatedFrom(), actual.getCreatedFrom());
            assertEquals(ticketingType.getRecurringEventSalesEndTime(), actual.getRecurringEventSalesEndTime());
            assertEquals(ticketingType.getRecurringEventSalesEndStatus(), actual.getRecurringEventSalesEndStatus());
        }
    }

    @Test
    void test_getTicketTypes_success_with_ticketingType_exist_false() {

        //setup
        ticketing.setRecurringEvent(false);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.getTicketingTypes(any(), any(), any(), any(), any())).thenReturn(ticketingTypes);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(true);

        //Excecution
        List<GetTicketTypeSettingDto> ticketTypes = ticketTypeServiceImpl.getTicketTypes(event, false, id);

        assertNotNull(ticketTypes);
        for (GetTicketTypeSettingDto actual : ticketTypes) {
            assertEquals(ticketingType.getId(), actual.getTypeId().longValue());
            assertFalse(actual.getChnageToTabel());
        }
    }

    @Test
    void test_getTicketTypes_success_with_recurringEvents_null_and_recurringEventId_null() {

        //setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.getTicketingTypes(any(), any(), any(), any(), any())).thenReturn(ticketingTypes);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);

        //Excecution
        List<GetTicketTypeSettingDto> ticketTypes = ticketTypeServiceImpl.getTicketTypes(event, false, null);

        assertNotNull(ticketTypes);
        for (GetTicketTypeSettingDto actual : ticketTypes) {
            assertEquals(ticketingType.getId(), actual.getTypeId().longValue());
            assertTrue(actual.getChnageToTabel());
            assertEquals(ticketingType.getRecurringEventSalesEndTime(), actual.getRecurringEventSalesEndTime());
            assertEquals(ticketingType.getRecurringEventSalesEndStatus(), actual.getRecurringEventSalesEndStatus());
        }
    }

    @Test
    void test_getTicketTypes_success_with_recurringEvents_and_recurringEventId_null() {

        //setup
        recurringEvents = new RecurringEvents();
        recurringEvents.setId(id);
        recurringEvents.setEventId(event);

        ticketingType.setRecurringEvent(recurringEvents);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.getTicketingTypes(any(), any(), any(), any(), any())).thenReturn(ticketingTypes);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);

        //Excecution
        List<GetTicketTypeSettingDto> ticketTypes = ticketTypeServiceImpl.getTicketTypes(event, false, null);

        assertNotNull(ticketTypes);
        for (GetTicketTypeSettingDto actual : ticketTypes) {
            assertEquals(ticketingType.getId(), actual.getTypeId().longValue());
            assertTrue(actual.getChnageToTabel());
            assertEquals(ticketingType.getRecurringEventSalesEndTime(), actual.getRecurringEventSalesEndTime());
            assertEquals(ticketingType.getRecurringEventSalesEndStatus(), actual.getRecurringEventSalesEndStatus());
        }
    }

    @Test
    void test_getTicketTypes_success_with_recurringEvents_null_and_recurringEventId() {

        //setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.getTicketingTypes(any(), any(), any(), any(), any())).thenReturn(ticketingTypes);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);

        //Excecution
        List<GetTicketTypeSettingDto> ticketTypes = ticketTypeServiceImpl.getTicketTypes(event, false, id);

        assertNotNull(ticketTypes);
        for (GetTicketTypeSettingDto actual : ticketTypes) {
            assertEquals(ticketingType.getId(), actual.getTypeId().longValue());
            assertTrue(actual.getChnageToTabel());
            assertEquals(ticketingType.getRecurringEventSalesEndTime(), actual.getRecurringEventSalesEndTime());
            assertEquals(ticketingType.getRecurringEventSalesEndStatus(), actual.getRecurringEventSalesEndStatus());
        }
    }

    @Test
    void test_setTicketTypesSalesEndTimeAndStatus_successs(){

        //Execution
        ticketTypeServiceImpl.setTicketTypesSalesEndTimeAndStatus(ticketTypeSettingDto,ticketingType);
        assertEquals(ticketingType.getRecurringEventSalesEndTime(),ticketTypeSettingDto.getRecurringEventSalesEndTime());
        assertEquals(ticketingType.getRecurringEventSalesEndStatus(),ticketTypeSettingDto.getRecurringEventSalesEndStatus());
    }

    @Test
    void test_setTicketTypesSalesEndTimeAndStatus_with_timeAndStatusNull_successs(){

        //setup
        Integer recurringEventSalesEndTime = 60;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(id);

        //Execution
        ticketTypeServiceImpl.setTicketTypesSalesEndTimeAndStatus(ticketTypeSettingDto,ticketingType);
        assertEquals(ticketingType.getRecurringEventSalesEndTime(),recurringEventSalesEndTime);
        assertEquals(ticketingType.getRecurringEventSalesEndStatus(),TicketingType.RecurringEventSalesEndStatus.START);
    }

    @Test
    void test_getTotalTicketsDb_successs_with_ticketBundleType_table(){

        //setup
        long totalTicket = ticketingType.getNumberofticket()*ticketingType.getNumberOfTicketPerTable();

        //Execution
        long numberOfTicket = ticketTypeServiceImpl.getTotalTicketsDb(ticketingType);
        assertEquals(totalTicket,numberOfTicket);
    }

    @Test
    void test_getTotalTicketsDb_successs_with_ticketBundleType_individualTicket(){

        //setup
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //Execution
        long numberOfTicket = ticketTypeServiceImpl.getTotalTicketsDb(ticketingType);
        assertEquals(ticketingType.getNumberOfTickets(),numberOfTicket);
    }

    @Test
    void test_getTotalTickets_success_with_ticketBundleType_table(){

        //setup
        long totalTicket = ticketTypeSettingDto.getNumberOfTicket()*ticketTypeSettingDto.getTicketsPerTable();

        //Execution
        long numberOfTicket = ticketTypeServiceImpl.getTotalTickets(ticketTypeSettingDto);
        assertEquals(totalTicket,numberOfTicket);
    }

    @Test
    void test_getTotalTickets_success_with_ticketBundleType_individualTicket(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //Execution
        Integer numberOfTicket = ticketTypeServiceImpl.getTotalTickets(ticketTypeSettingDto);
        assertEquals(ticketTypeSettingDto.getNumberOfTicket(),numberOfTicket);
    }

    @Test
    void test_setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent_success(){

        //setup
        Long createdFrom = -1L;

        //Execution
        ticketTypeServiceImpl.setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent_success_with_recurringEventId_lessThanZero(){

        //setup
        Long createdFrom = 1L;

        TicketTypeSettingDto ticketTypeSettingDtoData = new TicketTypeSettingDto();
        ticketTypeSettingDtoData.setRecurringEventId(0L);

        //Execution
        ticketTypeServiceImpl.setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDtoData, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent_success_with_recurringEventId_null(){

        //setup
        Long createdFrom = 1L;

        TicketTypeSettingDto ticketTypeSettingDtoData = new TicketTypeSettingDto();

        //Execution
        ticketTypeServiceImpl.setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDtoData, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_RecurringEventSalesEndStatus(){

        //setup
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.END);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_RecurringEventSalesEndTime(){

        //setup
        ticketTypeSettingDto.setRecurringEventSalesEndTime(60);

        ticketingType.setRecurringEventSalesEndTime(recurringEventSalesEndTime);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_RecurringEventSalesEndTime_null(){

        //setup
        ticketingType.setRecurringEventSalesEndTime(recurringEventSalesEndTime);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_Name(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName("ticketTypeName2");

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_Passfeetobuyer(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setPassfeetobuyer(true);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setPassfeetobuyer(false);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_MaxTickerPerBuyer(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setMaxTickerPerBuyer(1L);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setMaxTicketsPerBuyer(2L);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_MinTickerPerBuyer(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setMinTickerPerBuyer(1L);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setMinTicketsPerBuyer(2L);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_BundleType(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_TicketsPerTable(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setNumberOfTicketPerTable(1);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
        assertEquals(createdFrom,ticketingType.getCreatedFrom());
    }

    @Test
    void test_updateCreatedFromField_success_with_bundleType_INDIVIDUAL_TICKET(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
    }

    @Test
    void test_updateCreatedFromField_success_with_TicketsPerTable_null(){

        //setup
        TicketTypeSettingDto ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);

        TicketingType  ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
    }

    @Test
    void test_updateCreatedFromField_success_with_TicketsPerTable_and_numberOfTicketsPerTable_are_different(){

        //setup
        TicketTypeSettingDto ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName(ticketTypeName);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(ticketTypeName);
        ticketingType.setNumberOfTicketPerTable(2);

        //Execution
        ticketTypeServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);
    }

    @Test
    void test_isPaidTicketExistsNow_success_with_PaidTicket() {

        //Execution
        boolean ticketExists = ticketTypeServiceImpl.isPaidTicketExistsNow(false, ticketTypeSettingDto);
        assertEquals(true,ticketExists);
    }

    @Test
    void test_isPaidTicketExistsNow_success() {

        //setup
        ticketTypeSettingDto.setPrice(0D);

        //Execution
        boolean ticketExists = ticketTypeServiceImpl.isPaidTicketExistsNow(false, ticketTypeSettingDto);
        assertFalse(ticketExists);
    }

    @Test
    void test_isPaidTicketExistsNow_success_with_Donation() {

        //setup
        ticketTypeSettingDto.setTicketType(TicketType.DONATION);
        ticketTypeSettingDto.setPrice(0D);

        //Execution
        boolean ticketExists = ticketTypeServiceImpl.isPaidTicketExistsNow(false, ticketTypeSettingDto);
        assertTrue(ticketExists);
    }

    @Test
    void test_isPaidTicketExistsNow_success_with_Donation1() {

        //setup
        ticketTypeSettingDto.setTicketType(TicketType.DONATION);
        ticketTypeSettingDto.setPrice(2D);

        //Execution
        boolean ticketExists = ticketTypeServiceImpl.isPaidTicketExistsNow(false, ticketTypeSettingDto);
        assertFalse(ticketExists);
    }

    @Test
    void test_isPaidTicketExistsNow_success_with_free() {

        //setup
        ticketTypeSettingDto.setTicketType(TicketType.FREE);
        ticketTypeSettingDto.setPrice(2D);

        //Execution
        boolean ticketExists = ticketTypeServiceImpl.isPaidTicketExistsNow(false, ticketTypeSettingDto);
        assertFalse(ticketExists);
    }

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_success(){

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setEventTicketTypeId(ticketTypeId);
        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
        ticketingCoupons.add(ticketingCoupon);

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId(ticketTypeId);
        List<TicketingAccessCode> ticketingAccessCodes = new ArrayList<>();
        ticketingAccessCodes.add(ticketingAccessCode);

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(id);

        //mock
        when(ticketingCouponService.getAllByEventId(event.getEventId())).thenReturn(ticketingCoupons);
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(ticketingAccessCodes);

        //Execution
        ticketTypeServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketTypeIds);

        ArgumentCaptor<TicketingCoupon> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(TicketingCoupon.class);
        verify(ticketingCouponRepository, times(1)).save(ticketingCouponArgumentCaptor.capture());

        TicketingCoupon actualCouponCode = ticketingCouponArgumentCaptor.getValue();
        assertEquals(ticketingCoupon.getEventTicketTypeId(), actualCouponCode.getEventTicketTypeId());

        ArgumentCaptor<TicketingAccessCode> ticketingAccessCodeArgumentCaptor = ArgumentCaptor.forClass(TicketingAccessCode.class);
        verify(ticketingAccessCodeService, times(1)).save(ticketingAccessCodeArgumentCaptor.capture());

        TicketingAccessCode actualAccessCode = ticketingAccessCodeArgumentCaptor.getValue();
        assertEquals(ticketingAccessCode.getEventTicketTypeId(), actualAccessCode.getEventTicketTypeId());

        ArgumentCaptor<TicketingType> TicketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).save(TicketingTypeArgumentCaptor.capture());

        TicketingType actualData = TicketingTypeArgumentCaptor.getValue();
        assertTrue(actualData.isHidden());
    }

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_success1(){

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setEventTicketTypeId(ticketTypeId);
        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
        ticketingCoupons.add(ticketingCoupon);

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId(ticketTypeId);
        List<TicketingAccessCode> ticketingAccessCodes = new ArrayList<>();
        ticketingAccessCodes.add(ticketingAccessCode);

        Long ticketTypeId = 5L;

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(ticketTypeId);

        //mock
        when(ticketingCouponService.getAllByEventId(event.getEventId())).thenReturn(ticketingCoupons);
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(ticketingAccessCodes);

        //Execution
        ticketTypeServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketTypeIds);

    }

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_success2(){

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setEventTicketTypeId(ticketTypeId);
        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
        ticketingCoupons.add(ticketingCoupon);

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId(ticketTypeId);
        List<TicketingAccessCode> ticketingAccessCodes = new ArrayList<>();
        ticketingAccessCodes.add(ticketingAccessCode);

        Long ticketTypeId = 5L;

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(ticketTypeId);

        //mock
        when(ticketingCouponService.getAllByEventId(event.getEventId())).thenReturn(null);
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(null);

        //Execution
        ticketTypeServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketTypeIds);

    }

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_success3(){

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setEventTicketTypeId(ticketTypeId);
        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId(ticketTypeId);
        List<TicketingAccessCode> ticketingAccessCodes = new ArrayList<>();

        Long ticketTypeId = 5L;

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(ticketTypeId);

        //mock
        when(ticketingCouponService.getAllByEventId(event.getEventId())).thenReturn(ticketingCoupons);
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(ticketingAccessCodes);

        //Execution
        ticketTypeServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketTypeIds);

    }

    @Test
    void test_copyTicketingTypeElement_throwNotAcceptableException_BundleTypeCannotBeChanged() throws IOException {

        //setup
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BUNDLE_TYPE_CAN_NOT_BE_CHANGED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_copyTicketingTypeElement_throwNotAcceptableException_NumberOfTicketsCannotBeLessThanOne() throws IOException {

        //setup
        ticketTypeSettingDto.setTicketsPerTable(0);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.NUMBER_OF_TICKETS_CAN_NOT_BE_LESS_THAN_ONE.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_copyTicketingTypeElement_throwException_numberOfTicketsForBundleTypeCanNotBeChanged() throws IOException{

        //setup
        ticketingType.setNumberOfTicketPerTable(2);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true));

        assertEquals("The number of tickets per "+ticketingType.getBundleType()+" cannot be changed after an order is filled.", exception.getMessage());
    }

    @Test
    void test_copyTicketingTypeElement_throwException_numberOfTicketsForBundleTypeCanNotBeChanged1() throws IOException{

        //setup
        ticketingType.setNumberOfTicketPerTable(2);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true));

        assertEquals("The number of tickets per "+ticketingType.getBundleType()+" cannot be changed after an order is filled.", exception.getMessage());
    }

    @Test
    void test_copyTicketingTypeElement_throwException_numberOfTicketsForBundleTypeCanNotBeChanged2() throws IOException{

        //setup
        ticketingType.setNumberOfTicketPerTable(2);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true));

        assertEquals("The number of tickets per "+ticketingType.getBundleType()+" cannot be changed after an order is filled.", exception.getMessage());
    }

    @Test
    void test_copyTicketingTypeElement_success_with_recurringEvent1(){

        //setup
        TicketTypeSettingDto ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(id);
        ticketTypeSettingDto.setHidden(true);
        ticketTypeSettingDto.setCategoryColor("#D1D1D1");
        ticketTypeSettingDto.setPrice(10D);
        ticketTypeSettingDto.setName("General Admission");
        ticketTypeSettingDto.setTicketType(TicketType.PAID);
        ticketTypeSettingDto.setNumberOfTicket(10);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setStartDate(startDate);
        ticketTypeSettingDto.setPassfeetobuyer(true);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(50);
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketTypeSettingDto.setCreatedFrom(1L);
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getEnableTicketDescription()), java.util.Optional.of(ticketingType.getEnableTicketDescription()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getHidden()), java.util.Optional.of(ticketingType.isHidden()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getNumberOfTicket()), java.util.Optional.of(ticketingType.getNumberofticket()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPassfeetobuyer()), java.util.Optional.of(ticketingType.isPassfeetobuyer()));
    }

    @Test
    void test_copyTicketingTypeElement_success_with_recurringEvent(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");
        ticketTypeSettingDto.setTypeId(1L);

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(true);
        doNothing().when(ticketTypeServiceImpl).updateCreatedFromField(any(),any());
        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

//        PowermockStatic(TimeZoneUtil.class);

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getEnableTicketDescription()), java.util.Optional.of(ticketingType.getEnableTicketDescription()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getHidden()), java.util.Optional.of(ticketingType.isHidden()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getNumberOfTicket()), java.util.Optional.of(ticketingType.getNumberofticket()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPassfeetobuyer()), java.util.Optional.of(ticketingType.isPassfeetobuyer()));
    }

    @Test
    void test_copyTicketingTypeElement_success1_with_recurringEvent(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");
        ticketTypeSettingDto.setTypeId(0L);

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(true);
        doNothing().when(ticketTypeServiceImpl).updateCreatedFromField(any(),any());
        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getEnableTicketDescription()), java.util.Optional.of(ticketingType.getEnableTicketDescription()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getHidden()), java.util.Optional.of(ticketingType.isHidden()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getNumberOfTicket()), java.util.Optional.of(ticketingType.getNumberofticket()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPassfeetobuyer()), java.util.Optional.of(ticketingType.isPassfeetobuyer()));
    }

    @Test
    void test_copyTicketingTypeElement_success2_with_recurringEvent(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");
        ticketTypeSettingDto.setTypeId(0L);
        ticketTypeSettingDto.setNumberOfTicket(2);

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(true);
        doNothing().when(ticketTypeServiceImpl).updateCreatedFromField(any(),any());
        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getEnableTicketDescription()), java.util.Optional.of(ticketingType.getEnableTicketDescription()));
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getHidden()), java.util.Optional.of(ticketingType.isHidden()));
    }

    @Test
    void test_copyTicketingTypeElement_success1(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);

        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,false);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(ticketTypeSettingDto.getTicketsPerTable().longValue(), ticketingType.getNumberOfTicketPerTable());
        assertEquals(ticketTypeSettingDto.getBundleType(), ticketingType.getBundleType());
        assertEquals((long) ticketTypeSettingDto.getTypeId(), ticketingType.getId());
    }

    @Test
    void test_copyTicketingTypeElement_success2(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setTicketsPerTable(2);

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,false);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(ticketTypeSettingDto.getTicketsPerTable().longValue(), ticketingType.getNumberOfTicketPerTable());
        assertEquals(ticketTypeSettingDto.getBundleType(), ticketingType.getBundleType());
        assertEquals((long) ticketTypeSettingDto.getTypeId(), ticketingType.getId());
    }

    @Test
    void test_copyTicketingTypeElement_success3(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");
        ticketTypeSettingDto.setNumberOfTicket(3);

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);

        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,false);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(ticketTypeSettingDto.getBundleType(), ticketingType.getBundleType());
        assertEquals((long) ticketTypeSettingDto.getTypeId(), ticketingType.getId());
    }

    @Test
    void test_copyTicketingTypeElement_success4(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);

        doNothing().when(ticketTypeServiceImpl).updateCreatedFromField(any(),any());
        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,true);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(ticketTypeSettingDto.getBundleType(), ticketingType.getBundleType());
        assertEquals((long) ticketTypeSettingDto.getTypeId(), ticketingType.getId());
    }

    @Test
    void test_copyTicketingTypeElement_success5(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");
        ticketTypeSettingDto.setTypeId(0L);

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,false);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
    }

    @Test
    void test_copyTicketingTypeElement_success6(){

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setTicketTypeDescription("Ticket Type Description");
        ticketTypeSettingDto.setTypeId(0L);

        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);

        doNothing().when(ticketTypeServiceImpl).setTicketTypesSalesEndTimeAndStatus(any(),any());

        //Execution
        ticketTypeServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto,ticketingType, timeZone,false);
        assertEquals(java.util.Optional.of(ticketTypeSettingDto.getPrice()), java.util.Optional.of(ticketingType.getPrice()));
        assertEquals(ticketTypeSettingDto.getTicketsPerTable().longValue(), ticketingType.getNumberOfTicketPerTable());
        assertEquals(ticketTypeSettingDto.getBundleType(), ticketingType.getBundleType());
        assertEquals((long) ticketTypeSettingDto.getTypeId(), ticketingType.getId());
    }

    @Test
    void test_copyTicket_success1() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName("name");
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId(ticketTypeId);
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId(ticketTypeId);
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId(ticketTypeId);
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId(ticketTypeId);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);

        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getRecurringEventId(), actualticketingTypeData.getRecurringEventId());
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success2() {

        //setup
        TicketTypeSettingDto ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(id);
        ticketTypeSettingDto.setHidden(true);
        ticketTypeSettingDto.setCategoryColor("#D1D1D1");
        ticketTypeSettingDto.setPrice(10D);
        ticketTypeSettingDto.setName("General Admission");
        ticketTypeSettingDto.setNumberOfTicket(10);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setStartDate(startDate);
        ticketTypeSettingDto.setPassfeetobuyer(true);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketTypeSettingDto.setMaxTickerPerBuyer(1L);
        ticketTypeSettingDto.setMinTickerPerBuyer(1L);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(50);
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketTypeSettingDto.setCreatedFrom(1L);
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setTicketType(TicketType.DONATION);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName("name");
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId(ticketTypeId);
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId(ticketTypeId);
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId(ticketTypeId);
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId(ticketTypeId);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(11L);
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);


        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success3() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName("name");

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);

        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success4() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(STRING_FIRST_SPACE_NAME);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);


        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success5() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(STRING_EMAIL_SPACE);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);

        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success6() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(STRING_LAST_SPACE_NAME);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);

        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success7() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(false);
        ticketHolderRequiredAttributes.setName(STRING_CELL_SPACE_PHONE);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);

        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success8() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(STRING_CELL_SPACE_PHONE);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock
        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);

        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success9() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(false);
        ticketHolderRequiredAttributes.setName(STRING_CELL_SPACE_PHONE);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock


        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());




        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());


        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());

        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);


        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_success10() {

        //setup
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName("name");

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        CategoryDto categoryDto = new CategoryDto(key,"label",price,color,false,id);

        //mock


        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(),any(),any(),any(),any(),any());




        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());

        when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(1L);

        doNothing().when(ticketTypeServiceImpl).copyTicketingTypeElement(ticketTypeSettingDto,ticketingType,event.getEquivalentTimeZone(),ticketing.isRecurringEvent());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());

        TicketTypeSettingDto ticketTypeSettingDtoObj = new TicketTypeSettingDto();
        TicketTypeSettingDto ticketingTypeTemp = spy(ticketTypeSettingDtoObj);


        //Execution
        TicketingType ticketingTypeData = ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto,event,ticketing,decrementSeatsMap,ticketTypeSettingDto,ticketingType,false);
        assertNotNull(ticketingTypeData);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketTypeSettingDto.getRecurringEventId(), actualticketingTypeData.getRecurringEventId());
        assertEquals(ticketTypeSettingDto.getTicketType(), actualticketingTypeData.getTicketType());
        assertEquals(ticketing.getId(), actualticketingTypeData.getTicketing().getId());

    }

    @Test
    void test_copyTicket_throwException_canNotDecreaseNumberOfTickets() {

        //setup
        long numberOfTicketsSold = 11L;

        ticketingTicketTypeDto.setSeating(true);

        ticketing.setRecurringEvent(false);

        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock

        doNothing().when(ticketTypeServiceImpl).setTicketTypeEndDate(any(), any(), any(), any(), any(), any());
        doReturn(numberOfTicketsSold).when(ticketingStatisticsService).soldTicketCount(any());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketTypeServiceImpl.copyTicket(ticketingTicketTypeDto, event, ticketing, decrementSeatsMap, ticketTypeSettingDto, ticketingType, false));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_DECRESS_NUMBER_OF_TICKETS.getErrorMessage(), exception.getMessage());
    }

    private List<RecurringEvents> getRecurringEventsList() {

        recurringEvents.setEventId(event);

        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);
        return recurringEventsList;
    }
}