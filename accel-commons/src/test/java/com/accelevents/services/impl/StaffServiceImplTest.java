package com.accelevents.services.impl;

import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.common.dto.ExhibitorDetail;
import com.accelevents.common.dto.StaffDetail;
import com.accelevents.domain.*;
import com.accelevents.domain.exhibitors.Exhibitor;
import com.accelevents.domain.exhibitors.ExhibitorSetting;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.*;
import com.accelevents.exhibitors.services.ExhibitorSettingsRepoService;
import com.accelevents.exhibitors.services.ExhibitorSettingsService;
import com.accelevents.helpers.EmailImageHelper;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.hubspot.service.HubspotContactService;
import com.accelevents.repositories.*;
import com.accelevents.ro.event.service.ROJoinUserWithOrganizerService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneSyncService;
import com.accelevents.services.repo.helper.StaffRepoService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.accelevents.exceptions.ConflictException.UserExceptionConflictMsg.ALREADY_STAFF;
import static com.accelevents.utils.Constants.STRING_EMPTY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class StaffServiceImplTest {

    @Spy
    @InjectMocks
    private StaffServiceImpl staffServiceimpl = new StaffServiceImpl();

    @Mock
    private ExhibitorRepository exhibitorRepository;

    @Mock
    private StaffRepoService staffRepoService;

    @Mock
    private CommonUtil commonUtil;

    @Mock
    private StaffRepository staffRepository;

    @Mock
    private ServiceHelper serviceHelper;

    @Mock
    private NeptuneSyncService neptuneSyncService;

    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;
    @Mock
    private ROStaffService roStaffService;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private SendGridMailService sendGridMailService;

    @Mock
    private StaffEmailNotificationsRepository staffEmailNotificationsRepository;

    @Mock
    private OrganizerService organizerService;

    @Mock
    private LeadRetriverDataRepoService leadRetriverDataRepoService;

    @Mock
    private GetStreamService getStreamService;

    @Mock
    private SessionRepoService sessionService;

    @Mock
    private AttendeeProfileService attendeeProfileService;

    @Mock
    private EventChecklistService eventChecklistService;

    @Mock
    private CheckInAuditLogService checkInAuditLogService;

    @Mock
    private ExhibitorSettingsRepoService exhibitorSettingsRepoService;

    @Mock
    private BeeFreeRepository repository;

    @Mock
    private EventTicketsService eventTicketsService;

    @Mock
    private HubspotContactService hubspotContactService;

    @Mock
    private AutoLoginService autoLoginService;
    @Mock
    private EmailImageHelper emailImageHelper;

    @Mock
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
    @Mock
    private ROJoinUserWithOrganizerService roJoinUserWithOrganizerService;

    @Mock
    private ExhibitorSettingsService exhibitorSettingsService;

    private Event event;
    private StaffDetail staffDetail;
    private Exhibitor exhibitor;
    private User user;
    private EventDesignDetail eventDesignDetail;
    private WhiteLabel whiteLabel;
    private Staff staff;
    private Organizer organizer;
    private Pageable pageable;
    private AttendeeProfileDto attendeeProfileDto;
    private JoinUsersWithOrganizers joinUsersWithOrganizers;
    private Long exhibitorId = 1L;
    private ExhibitorSetting exhibitorSetting;


    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        staffDetail = setStaffDetail();
        staffDetail.setEmail("<EMAIL>");
        staffDetail.setFirstName("firstName");
        staffDetail.setLastName("lastName");
        exhibitor = new Exhibitor();
        eventDesignDetail = new EventDesignDetail();
        whiteLabel = new WhiteLabel();
        staff = new Staff();
        staff.setEvent(event);
        staff.setUser(user);
        organizer = new Organizer();
        pageable = PageRequest.of(0, 10);
        attendeeProfileDto = new AttendeeProfileDto();
    }

    WhiteLabel setWhiteLable() {
        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setFirmName(STRING_EMPTY);
        whiteLabel.setWhiteLabelUrl(STRING_EMPTY);
        whiteLabel.setTransactionalEmail("<EMAIL>");
        return whiteLabel;
    }

    @Test
    void test_addStaff_throwExhibitorNotFoundException() {

        //mock
        when(exhibitorRepository.findById(exhibitorId)).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> staffServiceimpl.addStaff(event, staffDetail, user,exhibitorId));

        //Assertion
        verify(exhibitorRepository).findById(exhibitorId);
        assertEquals(NotFoundException.NotFound.EXHIBITOR_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_addStaff_throwMaxLeadRetrieverMaxLimitReached() {

        exhibitor = getExhibitor();

        //mock
        when(exhibitorRepository.findById(exhibitorId)).thenReturn(Optional.of(exhibitor));
        when(staffRepository.isAddLeadRetriverStaffAllowed(any(), anyLong(), anyLong())).thenReturn(false);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> staffServiceimpl.addStaff(event, staffDetail, user,exhibitorId));
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.MAX_TEAM_MEMBER_LIMIT_REACHED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_addStaff_InvalidRole() {

        when(exhibitorRepository.findById(exhibitorId)).thenReturn(Optional.of(exhibitor));

        staffDetail.setRole(StaffRole.staff.name());
        staff.setEvent(event);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> staffServiceimpl.addStaff(event, staffDetail, user,exhibitorId));
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.EXHIBITOR_STAFF_ROLE_NOT_MATCHED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_addStaff_throwAlreadyStaffExist() {

        //setup
        exhibitor = getExhibitor();

        staffDetail.setRole(StaffRole.leadretriever.name());
        staff.setEvent(event);

        //mock
        when(exhibitorRepository.findById(exhibitorId)).thenReturn(Optional.of(exhibitor));
        when(staffRepository.isAddLeadRetriverStaffAllowed(any(), anyLong(), anyLong())).thenReturn(true);
        when(roUserService.getUserByPhoneOrEmail(staffDetail.getEmail())).thenReturn(Optional.of(user));

        when(staffRepository.findStaffByEventIdAndUserIdAndRoleAndExhibitorId(any(), any(), any(), anyLong())).thenReturn(staff);

        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> staffServiceimpl.addStaff(event, staffDetail, user, exhibitorId));

        //Assertion
        verify(exhibitorRepository).findById(anyLong());
        verify(staffRepository).isAddLeadRetriverStaffAllowed(any(), anyLong(), anyLong());
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(staffRepository).findStaffByEventIdAndUserIdAndRoleAndExhibitorId(any(), any(), any(), anyLong());
        assertEquals(ALREADY_STAFF.getDeveloperMessage(), exception.getMessage());
    }

    private void mockStaffSendEmail() {
        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
        when(eventDesignDetailService.getEventOrWhiteLabelHeaderLogoLocation(any())).thenReturn(STRING_EMPTY);
        when(eventDesignDetailService.getEventOrWhiteLabelLogoLocationWithOutsideSize(any())).thenReturn(STRING_EMPTY);
    }

    static Object[] getStaffRole() {
        return new Object[]{
                new Object[]{StaffRole.leadretriever.name()},
                new Object[]{StaffRole.exhibitoradmin.name()},
        };
    }

    @ParameterizedTest
    @MethodSource("getStaffRole")
    void test_addStaff_existingUser(String role) {

        //setup

        exhibitor = getExhibitor();
        staffDetail.setRole(role);
        exhibitorSetting = new ExhibitorSetting();

        //mock
        when(exhibitorRepository.findById(anyLong())).thenReturn(Optional.of(exhibitor));
        when(staffRepository.isAddLeadRetriverStaffAllowed(any(), anyLong(), anyLong())).thenReturn(true);
        when(serviceHelper.getEventBaseUrl(event)).thenReturn("baseUrl");
        when(roUserService.getUserByPhoneOrEmail(anyString())).thenReturn(Optional.of(user));
        when(userService.updateUserMostResentEvent(any(), any())).thenReturn(user);
        doNothing().when(userService).addUserRole(any(), anyString());
        doNothing().when(neptuneSyncService).addStaffInNeptuneAsync(any(), any(), anyBoolean());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());

        when(exhibitorSettingsService.getOrCreateGlobalConfig(event)).thenReturn(exhibitorSetting);

        //Execution
        Staff addStaff = staffServiceimpl.addStaff(event, staffDetail, user, exhibitorId);


        //Assertion
        ArgumentCaptor<Staff> argumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService, Mockito.times(1)).save(argumentCaptor.capture());
        Staff actualStatus = argumentCaptor.getValue();

        assertEquals(actualStatus.getUser(), user);

        verify(exhibitorRepository).findById(anyLong());
        verify(serviceHelper).getEventBaseUrl(event);
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(userService).updateUserMostResentEvent(any(), any());
        verify(userService).addUserRole(any(), anyString());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }


    static Object[] getWhiteLable() {
        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setFirmName(STRING_EMPTY);
        whiteLabel.setWhiteLabelUrl(STRING_EMPTY);
        whiteLabel.setTransactionalEmail("<EMAIL>");

        return new Object[]{
                new Object[]{null},
                new Object[]{whiteLabel},
        };
    }

    @ParameterizedTest
    @MethodSource("getWhiteLable")
    void test_addStaff_existingUserAndStaffRoleAdmin(WhiteLabel whiteLabel) {

        //setup

        staffDetail.setRole(StaffRole.admin.name());

        event.setWhiteLabel(whiteLabel);

        //mock
        when(serviceHelper.getEventBaseUrl(event)).thenReturn("baseUrl");
        when(roUserService.getUserByPhoneOrEmail(anyString())).thenReturn(Optional.of(user));
        when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(null);
        when(userService.updateUserMostResentEvent(any(), any())).thenReturn(user);
        doNothing().when(userService).addUserRole(any(), anyString());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());


        //Execution
        Staff addStaff = staffServiceimpl.addStaff(event, staffDetail, user, null);

        //Assertion
        ArgumentCaptor<Staff> argumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService, Mockito.times(1)).save(argumentCaptor.capture());
        Staff actualStatus = argumentCaptor.getValue();

        ArgumentCaptor<StaffEmailNotifications> staffEmailNotificationsArgumentCaptor = ArgumentCaptor.forClass(StaffEmailNotifications.class);
        verify(staffEmailNotificationsRepository).save(staffEmailNotificationsArgumentCaptor.capture());

        assertEquals(actualStatus.getUser(), user);

        verify(serviceHelper).getEventBaseUrl(event);
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(roStaffService, Mockito.times(1)).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
        verify(userService).updateUserMostResentEvent(any(), any());
        verify(userService).addUserRole(any(), anyString());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }

    @Test
    void test_addStaff_existingUserAndTransactionalEmailNull() {

        //setup

        whiteLabel = setWhiteLable();
        whiteLabel.setTransactionalEmail(null);
        event.setWhiteLabel(whiteLabel);

        //mock
        when(serviceHelper.getEventBaseUrl(event)).thenReturn("baseUrl");
        when(roUserService.getUserByPhoneOrEmail(anyString())).thenReturn(Optional.of(user));
        when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(null);
        when(userService.updateUserMostResentEvent(any(), any())).thenReturn(user);
        doNothing().when(userService).addUserRole(any(), anyString());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());


        //Execution
        Staff addStaff = staffServiceimpl.addStaff(event, staffDetail, user, null);

        //Assertion
        ArgumentCaptor<Staff> argumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService, Mockito.times(1)).save(argumentCaptor.capture());
        Staff actualStatus = argumentCaptor.getValue();

        assertEquals(actualStatus.getUser(), user);

        verify(serviceHelper).getEventBaseUrl(event);
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(roStaffService, Mockito.times(1)).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
        verify(userService).updateUserMostResentEvent(any(), any());
        verify(userService).addUserRole(any(), anyString());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }

    @Test
    void test_addStaff_existingUserAndStaffRoleAdminAndEventOrganization() {

        //setup

        staffDetail.setRole(StaffRole.admin.name());

        event.setOrganizer(organizer);

        //mock
        when(serviceHelper.getEventBaseUrl(event)).thenReturn("baseUrl");
        when(roUserService.getUserByPhoneOrEmail(anyString())).thenReturn(Optional.of(user));
        when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(null);
        when(userService.updateUserMostResentEvent(any(), any())).thenReturn(user);
        doNothing().when(userService).addUserRole(any(), anyString());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(user.getUserId(),event.getOrganizerId())).thenReturn(Optional.ofNullable(joinUsersWithOrganizers));


        doReturn(null).when(eventChecklistService).findByEvent(any());

        //Execution
        Staff addStaff = staffServiceimpl.addStaff(event, staffDetail, user,null);

        //Assertion
        ArgumentCaptor<Staff> argumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService, Mockito.times(1)).save(argumentCaptor.capture());
        Staff actualStatus = argumentCaptor.getValue();

        ArgumentCaptor<StaffEmailNotifications> staffEmailNotificationsArgumentCaptor = ArgumentCaptor.forClass(StaffEmailNotifications.class);
        verify(staffEmailNotificationsRepository).save(staffEmailNotificationsArgumentCaptor.capture());

        assertEquals(actualStatus.getUser(), user);

        verify(serviceHelper).getEventBaseUrl(event);
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(roStaffService, Mockito.times(1)).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
        verify(userService).updateUserMostResentEvent(any(), any());
        verify(userService).addUserRole(any(), anyString());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }

    @Test
    void test_addStaff_existingUserNotExistAndExhibitorIdNull() {

        //setup

        staffDetail.setRole(StaffRole.admin.name());

        //mock
        when(serviceHelper.getEventBaseUrl(event)).thenReturn("baseUrl");
        when(roUserService.getUserByPhoneOrEmail(anyString())).thenReturn(Optional.empty());
        doNothing().when(userService).addUserRole(any(), anyString());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());


        //Execution
        Staff addStaff = staffServiceimpl.addStaff(event, staffDetail, user,null);

        //Assertion
        ArgumentCaptor<Staff> argumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService, Mockito.times(1)).save(argumentCaptor.capture());
        Staff actualStatus = argumentCaptor.getValue();

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService).save(userArgumentCaptor.capture());

        ArgumentCaptor<StaffEmailNotifications> staffEmailNotificationsArgumentCaptor = ArgumentCaptor.forClass(StaffEmailNotifications.class);
        verify(staffEmailNotificationsRepository).save(staffEmailNotificationsArgumentCaptor.capture());

        assertTrue(actualStatus.getUser().isMarketingOptIn());
        assertEquals(actualStatus.getUser().getMostRecentEventId(), event.getEventId());
        assertEquals(actualStatus.getUser().getEmail(), staffDetail.getEmail());
        assertEquals(actualStatus.getUser().getFirstName(), staffDetail.getFirstName());
        assertEquals(actualStatus.getUser().getLastName(), staffDetail.getLastName());
        assertEquals(actualStatus.getUser().getLastName(), staffDetail.getLastName());

        verify(serviceHelper).getEventBaseUrl(event);
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(userService).addUserRole(any(), anyString());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }

    @ParameterizedTest
    @MethodSource("getStaffRole")
    void test_addStaff_existingUserNotExist(String role) {

        //setup
        exhibitor = getExhibitor();

        staffDetail.setRole(role);

        whiteLabel = setWhiteLable();
        event.setWhiteLabel(whiteLabel);

        exhibitorSetting = new ExhibitorSetting();

        //mock
        when(exhibitorRepository.findById(anyLong())).thenReturn(Optional.of(exhibitor));
        when(staffRepository.isAddLeadRetriverStaffAllowed(any(), anyLong(), anyLong())).thenReturn(true);
        when(serviceHelper.getEventBaseUrl(event)).thenReturn("baseUrl");
        when(roUserService.getUserByPhoneOrEmail(anyString())).thenReturn(Optional.empty());
        doNothing().when(userService).addUserRole(any(), anyString());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());
        when(exhibitorSettingsService.getOrCreateGlobalConfig(event)).thenReturn(exhibitorSetting);

        //Execution
        Staff addStaff = staffServiceimpl.addStaff(event, staffDetail, user, exhibitorId);

        //Assertion
        ArgumentCaptor<Staff> argumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService, Mockito.times(1)).save(argumentCaptor.capture());
        Staff actualStatus = argumentCaptor.getValue();

//        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
//        verify(userService).save(userArgumentCaptor.capture());

        assertTrue(actualStatus.getUser().isMarketingOptIn());
        assertEquals(actualStatus.getUser().getMostRecentEventId(), event.getEventId());
        assertEquals(actualStatus.getUser().getEmail(), staffDetail.getEmail());
        assertEquals(actualStatus.getUser().getFirstName(), staffDetail.getFirstName());
        assertEquals(actualStatus.getUser().getLastName(), staffDetail.getLastName());
        assertEquals(actualStatus.getUser().getLastName(), staffDetail.getLastName());

        verify(serviceHelper).getEventBaseUrl(event);
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(userService).addUserRole(any(), anyString());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
        verify(exhibitorRepository).findById(anyLong());
    }

    @Test
    void test_addStaff_existingUserNotExistAndExhibitorIdNullAndStaaroleNotAdmin() {

        //mock
        when(serviceHelper.getEventBaseUrl(event)).thenReturn("baseUrl");
        when(roUserService.getUserByPhoneOrEmail(anyString())).thenReturn(Optional.empty());
        doNothing().when(userService).addUserRole(any(), anyString());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());


        //Execution
        Staff addStaff = staffServiceimpl.addStaff(event, staffDetail, user, null);

        //Assertion
        ArgumentCaptor<Staff> argumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService, Mockito.times(1)).save(argumentCaptor.capture());
        Staff actualStatus = argumentCaptor.getValue();

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService).save(userArgumentCaptor.capture());

        assertTrue(actualStatus.getUser().isMarketingOptIn());
        assertEquals(actualStatus.getUser().getMostRecentEventId(), event.getEventId());
        assertEquals(actualStatus.getUser().getEmail(), staffDetail.getEmail());
        assertEquals(actualStatus.getUser().getFirstName(), staffDetail.getFirstName());
        assertEquals(actualStatus.getUser().getLastName(), staffDetail.getLastName());
        assertEquals(actualStatus.getUser().getLastName(), staffDetail.getLastName());

        verify(serviceHelper).getEventBaseUrl(event);
        verify(roUserService).getUserByPhoneOrEmail(anyString());
        verify(userService).addUserRole(any(), anyString());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }

    private Exhibitor getExhibitor() {
        Exhibitor exhibitor = new Exhibitor();
        exhibitor.setId(1L);
        exhibitor.setEvent(event);
        exhibitor.setLeadRetrieversAllowed(true);
        exhibitor.setMaxLeadRetrivers(10);
        exhibitor.setName("Exhibitor");
        exhibitor.setSeeAllLeads(true);
        return exhibitor;
    }

    private StaffDetail setStaffDetail() {
        StaffDetail staffDetail = new StaffDetail();
        staffDetail.setRole(StaffRole.leadretriever.name());
        return staffDetail;
    }

    @Test
    void test_removeExhibitor_throwStaffNotFoundException() {

        //mock
        when(staffRepository.findByIdAndExhibitorId(anyLong(), any())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> staffServiceimpl.removeExhibitorStaff(1L, exhibitor.getId(), false));

        //Assertion
        verify(staffRepository).findByIdAndExhibitorId(anyLong(), any());
        assertEquals(NotFoundException.NotFound.STAFF_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_removeExhibitor_success() {

        //setup
        List<Staff> staffList = new ArrayList<>();
        staffList.add(staff);

        //mock
        when(staffRepository.findByIdAndExhibitorId(anyLong(), any())).thenReturn(Optional.of(staff));
        doNothing().when(staffRepository).removeExhibitorByStaffId(anyLong(), any());
        when(staffRepository.findByEventAndUserAndRole(anyLong(), anyLong(), anyList())).thenReturn(staffList);

        //Execution
        staffServiceimpl.removeExhibitorStaff(1L, exhibitor.getId(), false);

        //Assertion
        verify(staffRepository).findByIdAndExhibitorId(anyLong(), any());
        verify(staffRepository).removeExhibitorByStaffId(anyLong(), any());
        verify(staffRepository).findByEventAndUserAndRole(anyLong(), anyLong(), anyList());
    }

    //@Test
    void test_updateStaff_throwStaffNotFoundException() {

        //mock
        when(staffRepository.findById(1L)).thenReturn(Optional.empty());
        when(staffRepository.findByIdAndExhibitorId(anyLong(), any())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> staffServiceimpl.updateStaff(1L, event, staffDetail, user, exhibitor.getId()));

        //Assertion
        verify(staffRepository).findByIdAndExhibitorId(anyLong(), any());
        assertEquals(NotFoundException.NotFound.STAFF_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    //@Test
    void test_updateStaff_exhibitorNotNull() {

        //setup
        user.setEmail("<EMAIL>");
        staff.setUser(user);
        staffDetail.setEmail("<EMAIL>");
        staffDetail.setUserId(user.getUserId());

        exhibitor.setMaxLeadRetrivers(0);
        exhibitor.setId(1);

        //mock
        when(staffRepository.findById(1L)).thenReturn(Optional.of(staff));
        when(staffRepository.findByEventAndRole(any(), any())).thenReturn(Collections.emptyList());
        when(staffRepository.findByUserAndRole(any(), any())).thenReturn(Collections.emptyList());
        when(exhibitorRepository.findById(exhibitorId)).thenReturn(Optional.of(exhibitor));

        //Execution
        User updateUser = staffServiceimpl.updateStaff(1L, event, staffDetail,user, exhibitor.getId());

        //Assertion
        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService).save(staffArgumentCaptor.capture());

        assertEquals(updateUser.getUserId(), staffDetail.getUserId());

        verify(staffRepository).findByUserAndRole(any(), any());
    }

   // @Test
    void test_updateStaff_AdminStaffListWithSuccess() {

        //setup
        user.setEmail("<EMAIL>");
        staff.setUser(user);
        staffDetail.setEmail("<EMAIL>");
        staffDetail.setRole(StaffRole.admin.name());
        staffDetail.setUserId(user.getUserId());

        List<Staff> eventAdminStaffList = new ArrayList<>();
        eventAdminStaffList.add(staff);

        //mock
        when(staffRepository.findById(anyLong())).thenReturn(Optional.of(staff));
        when(staffRepository.findAdminAndStaffByEvent(any())).thenReturn(eventAdminStaffList);
        when(staffRepository.findByUserAndRole(any(), any())).thenReturn(eventAdminStaffList);
        doNothing().when(userService).addUserRole(any(), anyString());

        //Execution
        User updateUser = staffServiceimpl.updateStaff(1L, event, staffDetail,user,null);

        //Assertion
        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService).save(staffArgumentCaptor.capture());

        assertEquals(updateUser.getUserId(), staffDetail.getUserId());

        verify(userService).addUserRole(any(), anyString());
        verify(staffRepository).findById(anyLong());
        verify(staffRepository).findAdminAndStaffByEvent(any());
        verify(staffRepository).findByUserAndRole(any(), any());
    }

   // @Test
    void test_updateStaff_AdminStaffListEmptySuccess() {

        //setup
        user.setEmail("<EMAIL>");
        staff.setUser(user);
        staffDetail.setEmail("<EMAIL>");
        staffDetail.setUserId(user.getUserId());
        event.setWhiteLabel(whiteLabel);

        //mock
        when(staffRepository.findById(anyLong())).thenReturn(Optional.of(staff));
        when(staffRepository.findAdminAndStaffByEvent(any())).thenReturn(Collections.emptyList());
        when(staffRepository.findByUserAndRole(any(), any())).thenReturn(Collections.emptyList());
        doNothing().when(userService).addUserRole(any(), anyString());
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.empty());
        when(roUserService.getUserRoles(any())).thenReturn(Collections.emptyList());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());

        //Execution
        User updateUser = staffServiceimpl.updateStaff(1L, event, staffDetail, user,null);

        //Assertion
        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService).save(staffArgumentCaptor.capture());

        assertEquals(updateUser.getUserId(), staffDetail.getUserId());

        verify(userService).addUserRole(any(), anyString());
        verify(staffRepository).findById(anyLong());
        verify(staffRepository).findAdminAndStaffByEvent(any());
        verify(staffRepository).findByUserAndRole(any(), any());
        verify(roUserService).getUserByEmail(anyString());
        verify(roUserService).getUserRoles(any());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }

 //   @ParameterizedTest
 //	@MethodSource("getWhiteLable")
    void test_updateStaff_AdminStaffListEmptyAndStaffRoleAdminSuccess(WhiteLabel whiteLabel) {

        //setup
        user.setEmail("<EMAIL>");
        staff.setUser(user);
        staffDetail.setEmail("<EMAIL>");
        staffDetail.setUserId(user.getUserId());
        staffDetail.setRole(StaffRole.admin.name());
        event.setWhiteLabel(whiteLabel);

        //mock
        when(staffRepository.findById(anyLong())).thenReturn(Optional.of(staff));
        when(staffRepository.findByUserAndRole(any(), any())).thenReturn(Collections.emptyList());
        doNothing().when(userService).addUserRole(any(), anyString());
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.empty());
        when(roUserService.getUserRoles(any())).thenReturn(Collections.emptyList());
        mockStaffSendEmail();
        doNothing().when(sendGridMailService).sendTemplateMail(any(), anySet());

        //Execution
        User updateUser = staffServiceimpl.updateStaff(1L, event, staffDetail, user,null);

        //Assertion
        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffRepoService).save(staffArgumentCaptor.capture());

        assertEquals(updateUser.getUserId(), staffDetail.getUserId());

        verify(userService, atLeastOnce()).addUserRole(any(), anyString());
        verify(staffRepository).findById(anyLong());
        verify(staffRepository).findByUserAndRole(any(), any());
        verify(roUserService).getUserByEmail(anyString());
        verify(roUserService).getUserRoles(any());
        verify(eventDesignDetailService).findByEvent(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelHeaderLogoLocation(any());
        verify(eventDesignDetailService).getEventOrWhiteLabelLogoLocationWithOutsideSize(any());
        verify(sendGridMailService).sendTemplateMail(any(), anySet());
    }

  //  @Test
    void test_updateStaff_throwCanNotChangeLastAdminRole() {

        user.setEmail("<EMAIL>");
        staff.setUser(user);
        staffDetail.setEmail("<EMAIL>");
        staffDetail.setUserId(user.getUserId());

        List<Staff> eventAdminStaffList = new ArrayList<>();
        eventAdminStaffList.add(staff);

        //mock
        when(staffRepository.findById(anyLong())).thenReturn(Optional.of(staff));
        when(staffRepository.findAdminAndStaffByEvent(any())).thenReturn(eventAdminStaffList);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> staffServiceimpl.updateStaff(1L, event, staffDetail, user,null));

        //Assertion
        verify(staffRepository).findById(anyLong());
        verify(staffRepository).findAdminAndStaffByEvent(any());
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.CAN_NOT_CHANGE_LAST_ADMIN_ROLE.getDeveloperMessage(), exception.getMessage());
    }

   // @Test
    void test_updateStaff_throwEmailAlreadyRegister() {

        user.setEmail("<EMAIL>");
        staff.setUser(user);
        staffDetail.setEmail("<EMAIL>");
        staffDetail.setUserId(user.getUserId());

        List<Staff> eventAdminStaffList = new ArrayList<>();
        eventAdminStaffList.add(staff);

        //mock
        when(staffRepository.findById(anyLong())).thenReturn(Optional.of(staff));
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        when(checkInAuditLogService.findAuditLogByUserId(user.getUserId())).thenReturn(0L);
        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> staffServiceimpl.updateStaff(1L, event, staffDetail, user, null));

        //Assertion
        verify(staffRepository).findById(anyLong());
        verify(roUserService).getUserByEmail(anyString());
        assertEquals(ConflictException.UserExceptionConflictMsg.EMAIL_ALREADY_ASSIGNEDED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getExhibitorStaffList_searchStringNull() {

        //setup
        staff = getStaffDetails();
        Page<Staff> staffs = new PageImpl<>(Collections.singletonList(staff));

        //mock
        when(staffRepository.findExhibitorStaffByExhibitorId(exhibitorId, pageable)).thenReturn(staffs);
        when(leadRetriverDataRepoService.findAllByExhibitorsId(exhibitorId)).thenReturn(Collections.emptyList());

        //Execution
        DataTableResponse dataTableResponse = staffServiceimpl.getExhibitorStaffList(exhibitorId, 10, 0, null,staff.getEvent());

        //Assertion
        ExhibitorDetail exhibitorDetail = (ExhibitorDetail) dataTableResponse.getData().get(0);

        assertEquals(exhibitorDetail.getExhibitorId(), staffs.getContent().get(0).getExhibitorId());
        assertEquals(exhibitorDetail.isExhibitorAdmin(), StaffRole.exhibitoradmin.name().equals(staffs.getContent().get(0).getRole().name()));
        assertEquals(exhibitorDetail.getEmail(), staffs.getContent().get(0).getUser().getEmail());
        assertEquals(exhibitorDetail.getFirstName(), staffs.getContent().get(0).getUser().getFirstName());
        assertEquals(exhibitorDetail.getLastName(), staffs.getContent().get(0).getUser().getLastName());
        assertEquals(exhibitorDetail.getRole(), staffs.getContent().get(0).getRole().name());
        assertEquals(dataTableResponse.getRecordsTotal(), staffs.getContent().size());
        assertEquals(dataTableResponse.getRecordsFiltered(), staffs.getContent().size());

        verify(staffRepository).findExhibitorStaffByExhibitorId(exhibitorId, pageable);
        verify(leadRetriverDataRepoService).findAllByExhibitorsId(exhibitorId);
    }

    @Test
    void test_getExhibitorStaffList_searchString() {

        //setup
        staff = getStaffDetails();
        Page<Staff> staffs = new PageImpl<>(Collections.singletonList(staff));

        //mock
        String search = "search";
        when(staffRepository.findExhibitorStaffByExhibitorIdAndSerchString(exhibitorId, search, pageable)).thenReturn(staffs);
        when(leadRetriverDataRepoService.findAllByExhibitorsId(exhibitorId)).thenReturn(Collections.emptyList());

        //Execution
        DataTableResponse dataTableResponse = staffServiceimpl.getExhibitorStaffList(exhibitorId, 10, 0, search,staff.getEvent());

        //Assertion
        ExhibitorDetail exhibitorDetail = (ExhibitorDetail) dataTableResponse.getData().get(0);

        assertEquals(exhibitorDetail.getExhibitorId(), staffs.getContent().get(0).getExhibitorId());
        assertEquals(exhibitorDetail.isExhibitorAdmin(), StaffRole.exhibitoradmin.name().equals(staffs.getContent().get(0).getRole().name()));
        assertEquals(exhibitorDetail.getEmail(), staffs.getContent().get(0).getUser().getEmail());
        assertEquals(exhibitorDetail.getFirstName(), staffs.getContent().get(0).getUser().getFirstName());
        assertEquals(exhibitorDetail.getLastName(), staffs.getContent().get(0).getUser().getLastName());
        assertEquals(exhibitorDetail.getRole(), staffs.getContent().get(0).getRole().name());
        assertEquals(dataTableResponse.getRecordsTotal(), staffs.getContent().size());
        assertEquals(dataTableResponse.getRecordsFiltered(), staffs.getContent().size());

        verify(staffRepository).findExhibitorStaffByExhibitorIdAndSerchString(exhibitorId, search, pageable);
        verify(leadRetriverDataRepoService).findAllByExhibitorsId(exhibitorId);
    }

    private Staff getStaffDetails() {
        exhibitor = getExhibitor();
        Staff staff = new Staff();
        staff.setExhibitorId(exhibitor.getId());
        staff.setUser(user);
        staff.setRole(StaffRole.exhibitoradmin);
        return staff;
    }

    static Object[] getHostAccessForEvent() {
        return new Object[]{
                new Object[]{false, false, false, false},
                new Object[]{false, false, false, true},
        };
    }

    /*@ParameterizedTest
    @MethodSource("getHostAccessForEvent")
    void test_hasHostAccessForEvent_success(boolean isSuperAdminUser, boolean isSalesRepresentativeForEvent, boolean isWhiteLabelAdminForEvent, boolean isUserAdminForEvent) {

        //mock
        when(roUserService.getUserRoles(any())).thenReturn(Collections.emptyList());


        when(roStaffService.isUserAdminForEvent(any(), anyList(), any())).thenReturn(isUserAdminForEvent);

        //Execution
        boolean hasHostAccessForEvent = staffServiceimpl.hasHostAccessForEvent(user, event);

        //Assertion
        assertEquals(hasHostAccessForEvent, isUserAdminForEvent);

        verify(roUserService).getUserRoles(any());
        verify(roStaffService).isUserAdminForEvent(any(), anyList(), any());
    }*/

    static Object[] getUserAndExhibitor() {
        User user = new User();
        Exhibitor exhibitor = new Exhibitor();
        return new Object[]{
                new Object[]{user, null},
                new Object[]{null, exhibitor},
        };
    }


    @ParameterizedTest
    @MethodSource("getUserAndExhibitor")
    void test_isExhibitorAdmin_notAuthorizeException(User user, Exhibitor exhibitor) {

        //Execution
        Exception exception = assertThrows(AuthorizationException.class,
                () -> staffServiceimpl.isExhibitorAdmin(user, event, exhibitor));
        
        assertEquals(Constants.NOT_AUTHORIZE, exception.getMessage());
    }

    @Test
    void test_isExhibitorAdmin_notEventHostException() {

        //Execution
        Exception exception = assertThrows(ForbiddenException.class,
                () -> staffServiceimpl.isExhibitorAdmin(user, null, exhibitor));
        
        assertEquals(ForbiddenException.UserForbiddenExceptionMsg.NOT_EVENT_HOST.getDeveloperMessage(), exception.getMessage());
    }

    static Object[] getStaff() {
        Staff staff = new Staff();
        return new Object[]{
                new Object[]{null},
                new Object[]{staff},
        };
    }

    @ParameterizedTest
    @MethodSource("getStaff")
    void test_isExhibitorAdmin_success(Staff staff) {

        //mock
        when(staffRepository.findByUserAndRoleAndEventAndExhibitorId(any(), any(), any(), any())).thenReturn(staff);

        //Execution
        boolean isExhibitorAdmin = staffServiceimpl.isExhibitorAdmin(user, event, exhibitor);

        //Assertion
        assertEquals(isExhibitorAdmin, staff != null);
    }

}