//TODO: PowerMock issue with java17
/*
package com.accelevents.services.impl;

import com.accelevents.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.Rule;
import org.junit.jupiter.api.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingHelperServiceImplTestWithPowerMockito {

    @Spy
    @InjectMocks
    private TicketingHelperServiceImpl ticketingHelperServiceImpl = new TicketingHelperServiceImpl();

    @BeforeEach
    public void setUp() throws Exception {
    }

    @Test
    public void test_getUnmashler_throwJAXBException() throws JAXBException{
        PowerMockito.mockStatic(JAXBContext.class);

        //mock
        when(JAXBContext.newInstance(TicketAttributeValueDto.class)).thenThrow(JAXBException.class);

        //Execution
        Unmarshaller unmarshaller = ticketingHelperServiceImpl.getUnmashler();
        assertNull(unmarshaller);
    }
}*/
