package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.service.ChargeBeeHelperService;
import com.accelevents.domain.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.ResendTicketingEmailRepository;
import com.accelevents.services.ContactModuleSettingsService;
import com.accelevents.services.TicketingService;
import com.accelevents.ticketing.dto.ResendEventEmailBodyDto;
import com.accelevents.ticketing.dto.ResendEventEmailDto;
import com.accelevents.ticketing.dto.ResendEventEmailGetDto;
import com.accelevents.ticketing.dto.ResendEventEmailListDto;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.TimeZoneUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static com.accelevents.utils.Constants.DATE_FORMAT;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class ResendTicketingEmailServiceImplTest {

    @Mock
    private ResendTicketingEmailRepository resendTicketingEmailRepository;

    @Mock
    private TicketingService ticketingService;

    @Mock
    private RecurringEventsScheduleBRServiceImpl recurringEventsScheduleBRService;

    @Mock
    private EventDesignDetailServiceImpl eventDesignDetailService;
    @Mock
    private ChargeBeeHelperService chargeBeeHelperService;

    @Mock
    private ContactModuleSettingsService contactModuleSettingsService;
    
    @InjectMocks
    @Spy
    ResendTicketingEmailServiceImpl resendTicketingEmailServiceImpl = new ResendTicketingEmailServiceImpl(resendTicketingEmailRepository,ticketingService,recurringEventsScheduleBRService,eventDesignDetailService);

    private Event event;
    private ResendEventEmailBodyDto resendEventEmailBodyDto;
    private ResendTicketingEmail resendTicketingEmail;
    private ResendEventEmailDto resendEventEmailDto;
    private EventDesignDetail eventDesignDetail;
    private RecurringEvents recurringEvents;
    private Ticketing ticketing;

    private String resendTime = "07/04/2020 00:00:00";
    private Long resendEmailId = 1L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        resendEventEmailBodyDto = new ResendEventEmailBodyDto();
        resendTicketingEmail = new ResendTicketingEmail();
        resendEventEmailDto = new ResendEventEmailGetDto();
        eventDesignDetail = new EventDesignDetail();
        recurringEvents = new RecurringEvents();
        ticketing = new Ticketing();
        resendEventEmailDto.setResendTicketOrderText("");
    }

    @Test
    void test_saveResendEventEmail_throwExceptionUseRecurringEventApi() {

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> resendTicketingEmailServiceImpl.saveResendEventEmail(resendEventEmailBodyDto,event));

        //assert
        verify(ticketingService).isRecurringEvent(event);
        assertEquals(NotAcceptableException.RecurringExceptionMsg.USE_RECURRING_EVENT_API.getDeveloperMessage(), exception.getMessage());
    }

   // @Test
    void test_saveResendEventEmail_throwExceptionResendEmailAlreadyScheduled() {

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(resendTicketingEmailRepository.findByEventIdAndResendTicketStatusInAndRecurringEventIdNull(event.getEventId(), List.of(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED))).thenReturn(List.of(resendTicketingEmail));

        //Execution
         Exception exception = assertThrows(NotAcceptableException.class,
                 () -> resendTicketingEmailServiceImpl.saveResendEventEmail(resendEventEmailBodyDto,event));

        //assert
        verify(ticketingService).isRecurringEvent(event);
        verify(resendTicketingEmailRepository).findByEventIdAndResendTicketStatusInAndRecurringEventIdNull(event.getEventId(), List.of(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.RESEND_TICKET_EMAIL_ALREADY_SCHEDULED.getDeveloperMessage(), exception.getMessage());
    }

    static Object[] getResendTicketSubject(){
        return new Object[]{
                new Object[]{null} ,
                new Object[]{"Testing Email"} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getResendTicketSubject")
    void test_saveResendEventEmail_success(String resendTicketSubject) {

        //setup
        resendTicketingEmail.setResendTicketStatus(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
        resendTicketingEmail.setResendTicketSubject(resendTicketSubject);
        resendEventEmailBodyDto.setResendTicketSubject(resendTicketSubject);
        resendEventEmailBodyDto.setResendTicketOrderText("test");

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(resendTicketingEmailRepository.save(any())).thenReturn(resendTicketingEmail);
        when(contactModuleSettingsService.findContactModuleSettingsByEvent(event)).thenReturn(Optional.empty());

        //Execution
        ResendEventEmailGetDto resendEventEmailGetDto = resendTicketingEmailServiceImpl.saveResendEventEmail(resendEventEmailBodyDto,event);

        //assert
        assertEquals(resendEventEmailGetDto.getId(),resendTicketingEmail.getId());
        assertEquals(resendEventEmailGetDto.getResendTicketStatus(),resendTicketingEmail.getResendTicketStatus().toString());
        assertFalse(resendEventEmailGetDto.isRecurringEvent());
        assertEquals(resendEventEmailGetDto.getResendTicketSubject(),resendTicketSubject);
        assertEquals(resendEventEmailGetDto.getResendTicketOrderText(),resendTicketingEmail.getResendTicketOrderText());
        assertEquals(resendEventEmailGetDto.getResendTime(),"");

        verify(ticketingService).isRecurringEvent(event);
        verify(resendTicketingEmailRepository).save(any());
    }

    @Test
    void test_saveResendEventEmailSchedule_throwExceptionUseRecurringEventApi() {

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> resendTicketingEmailServiceImpl.saveResendEventEmailSchedule(resendEventEmailDto,event));

        //assert
        verify(ticketingService).isRecurringEvent(event);
        assertEquals(NotAcceptableException.RecurringExceptionMsg.USE_RECURRING_EVENT_API.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_saveResendEventEmailSchedule_throwExceptionResendEmailAlreadyScheduled() {
        
        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(resendTicketingEmailRepository.findByEventIdAndResendTicketStatusInAndRecurringEventIdNull(event.getEventId(), List.of(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED))).thenReturn(List.of(resendTicketingEmail));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> resendTicketingEmailServiceImpl.saveResendEventEmailSchedule(resendEventEmailDto,event));

        //assert
        verify(ticketingService).isRecurringEvent(event);
        verify(resendTicketingEmailRepository).findByEventIdAndResendTicketStatusInAndRecurringEventIdNull(event.getEventId(), List.of(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.RESEND_TICKET_EMAIL_ALREADY_SCHEDULED.getDeveloperMessage(), exception.getMessage());
    }

    @ParameterizedTest
    @MethodSource("getResendTicketSubject")
    void test_saveResendEventEmailSchedule_success(String resendTicketSubject) {

        //setup
        resendTicketingEmail.setResendTicketStatus(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
        resendTicketingEmail.setResendTicketSubject(resendTicketSubject);
        resendTicketingEmail.setEventTicketMailResendTime(new Date(resendTime));
        resendEventEmailDto.setResendTicketSubject(resendTicketSubject);
        resendEventEmailDto.setResendTime(resendTime);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(false);
        when(resendTicketingEmailRepository.save(any())).thenReturn(resendTicketingEmail);

        //Execution
        ResendEventEmailGetDto resendEventEmailGetDto = resendTicketingEmailServiceImpl.saveResendEventEmailSchedule(resendEventEmailDto,event);

        //assert
        assertEquals(resendEventEmailGetDto.getId(),resendTicketingEmail.getId());
        assertEquals(resendEventEmailGetDto.getResendTicketStatus(),resendTicketingEmail.getResendTicketStatus().toString());
        assertFalse(resendEventEmailGetDto.isRecurringEvent());
        assertEquals(resendEventEmailGetDto.getResendTicketSubject(),resendTicketSubject);
        assertEquals(resendEventEmailGetDto.getResendTicketOrderText(),resendTicketingEmail.getResendTicketOrderText());
        assertEquals(resendEventEmailGetDto.getResendTime(), TimeZoneUtil.getDateInLocal(resendTicketingEmail.getEventTicketMailResendTime(), event.getEquivalentTimeZone(), DATE_FORMAT));

        verify(ticketingService).isRecurringEvent(event);
        verify(resendTicketingEmailRepository).save(any());
    }

    @Test
    void test_updateDefaultHoursBeforeResendEmailForRecurringEvent() {

        //setup
        List<ResendTicketingEmail> resendTicketingEmailList = new ArrayList<>();
        resendTicketingEmailList.add(resendTicketingEmail);

        recurringEvents.setRecurringEventStartDate(new Date());

        //mock
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(resendTicketingEmailRepository.findByEventIdAndRecurringEventIdIsNotNullAndResendTicketStatus(event.getEventId(), ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED)).thenReturn(resendTicketingEmailList);
        when(recurringEventsScheduleBRService.getRecurringEventById(resendTicketingEmail.getRecurringEventId())).thenReturn(Optional.of(recurringEvents));

        //Execution
        resendTicketingEmailServiceImpl.updateDefaultHoursBeforeResendEmailForRecurringEvent(event,1);

        //assert
        ArgumentCaptor<EventDesignDetail> eventDetailsArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
        verify(eventDesignDetailService, times(1)).save(eventDetailsArgumentCaptor.capture());

        EventDesignDetail actual = eventDetailsArgumentCaptor.getValue();
        assertEquals(actual.getDefaultResendEmailHoursBeforeEvent(),1);

        ArgumentCaptor<ResendTicketingEmail> resendTicketingEmailArgumentCaptor = ArgumentCaptor.forClass(ResendTicketingEmail.class);
        verify(resendTicketingEmailRepository, times(1)).save(resendTicketingEmailArgumentCaptor.capture());

        ResendTicketingEmail actualResendTicketingEmail = resendTicketingEmailArgumentCaptor.getValue();
        assertEquals(actualResendTicketingEmail.getEventTicketMailResendTime(), DateUtils.getAddedHours(TimeZoneUtil.getDateInUTC(recurringEvents.getRecurringEventStartDate(),event.getEquivalentTimeZone()), -1));

        verify(eventDesignDetailService).findByEvent(event);
        verify(resendTicketingEmailRepository).findByEventIdAndRecurringEventIdIsNotNullAndResendTicketStatus(event.getEventId(), ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
        verify(recurringEventsScheduleBRService).getRecurringEventById(resendTicketingEmail.getRecurringEventId());
    }

    static Object[] getResendTicketSubjectAndResponse(){
        return new Object[]{
                new Object[]{null,"Reminder for TestEvent1"} ,
                new Object[]{"Testing Email","Testing Email"} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getResendTicketSubjectAndResponse")
    void test_createResendEventEmailForRecurringEvent(String resendTicketSubject,String ticketSubject) {

        //setup
        resendTicketingEmail.setRecurringEventId(1L);
        List<ResendTicketingEmail> resendTicketingEmailList = new ArrayList<>();
        resendTicketingEmailList.add(resendTicketingEmail);

        recurringEvents.setRecurringEventStartDate(new Date());
        recurringEvents.setId(2L);
        RecurringEvents recurringEvents1 = new RecurringEvents();
        recurringEvents1.setId(1L);
        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);
        recurringEventsList.add(recurringEvents1);

        resendEventEmailBodyDto.setResendTicketSubject(resendTicketSubject);
        resendEventEmailBodyDto.setResendTicketOrderText("test");
        resendEventEmailBodyDto.setCustomTemplateId(0L);

        eventDesignDetail.setDefaultResendEmailHoursBeforeEvent(1);

        //mock
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(resendTicketingEmailRepository.findByEventIdAndRecurringEventIdIsNotNullAndResendTicketStatus(event.getEventId(), ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED)).thenReturn(resendTicketingEmailList);
        when(recurringEventsScheduleBRService.getRecurringEventsOnlyFutureDate(event)).thenReturn(recurringEventsList);

        //Execution
        resendTicketingEmailServiceImpl.createResendEventEmailForRecurringEvent(event,resendEventEmailBodyDto);

        //verify
        ArgumentCaptor<ResendTicketingEmail> resendTicketingEmailArgumentCaptor = ArgumentCaptor.forClass(ResendTicketingEmail.class);
        verify(resendTicketingEmailRepository, times(1)).save(resendTicketingEmailArgumentCaptor.capture());

        ResendTicketingEmail actualResendTicketingEmail = resendTicketingEmailArgumentCaptor.getValue();
        assertEquals(actualResendTicketingEmail.getResendTicketStatus(), ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
        assertEquals(actualResendTicketingEmail.getRecurringEventId(),recurringEvents.getId(),0);
        assertEquals(actualResendTicketingEmail.getEventId(),event.getEventId(),0);
        assertEquals(actualResendTicketingEmail.getResendTicketSubject(),ticketSubject);
        assertEquals(actualResendTicketingEmail.getResendTicketOrderText(),resendEventEmailBodyDto.getResendTicketOrderText());
        assertEquals(actualResendTicketingEmail.getEventTicketMailResendTime(),DateUtils.getAddedHours(TimeZoneUtil.getDateInUTC(recurringEvents.getRecurringEventStartDate(),event.getEquivalentTimeZone()), -eventDesignDetail.getDefaultResendEmailHoursBeforeEvent()));
        assertEquals(actualResendTicketingEmail.getCreatedDate().toString(),new Date().toString());


        verify(eventDesignDetailService).findByEvent(event);
        verify(resendTicketingEmailRepository).findByEventIdAndRecurringEventIdIsNotNullAndResendTicketStatus(event.getEventId(), ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
        verify(recurringEventsScheduleBRService).getRecurringEventsOnlyFutureDate(event);
    }

    @Test
    void test_updateResendEventEmail_throwExceptionRecurringEventScheduleNotFound() {

        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> resendTicketingEmailServiceImpl.updateResendEventEmail(resendEventEmailBodyDto,event,resendEmailId));

        //assert
        verify(resendTicketingEmailRepository).findById(resendEmailId);
        assertEquals(NotFoundException.NotFound.RECURRING_EVENT_SCHEDULE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @ParameterizedTest
    @MethodSource("getResendTicketSubjectAndResponse")
    void test_updateResendEventEmail_success(String resendTicketSubject,String ticketSubject) {

        //setup
        resendTicketingEmail.setResendTicketStatus(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
        resendEventEmailBodyDto.setResendTicketSubject(resendTicketSubject);
        resendEventEmailBodyDto.setResendTicketOrderText("%24test");

        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.of(resendTicketingEmail));

        //Execution
        ResendEventEmailGetDto resendEventEmailGetDto = resendTicketingEmailServiceImpl.updateResendEventEmail(resendEventEmailBodyDto,event,resendEmailId);

        //assert
        ArgumentCaptor<ResendTicketingEmail> resendTicketingEmailArgumentCaptor = ArgumentCaptor.forClass(ResendTicketingEmail.class);
        verify(resendTicketingEmailRepository, times(1)).save(resendTicketingEmailArgumentCaptor.capture());

        assertEquals(resendEventEmailGetDto.getResendTicketSubject(),ticketSubject);
        assertEquals(resendEventEmailGetDto.getId(),resendTicketingEmail.getId());
        assertEquals(resendEventEmailGetDto.getResendTicketStatus(),resendTicketingEmail.getResendTicketStatus().toString());
        assertFalse(resendEventEmailGetDto.isRecurringEvent());
        assertEquals(resendEventEmailGetDto.getResendTicketSubject(),ticketSubject);
        assertEquals(resendEventEmailGetDto.getResendTicketOrderText(),resendTicketingEmail.getResendTicketOrderText());
        assertEquals("$test", resendEventEmailGetDto.getResendTicketOrderText());

        verify(resendTicketingEmailRepository).findById(resendEmailId);
    }

    @Test
    void test_updateResendEventEmailSchedule_throwExceptionRecurringEventScheduleNotFound() {

        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> resendTicketingEmailServiceImpl.updateResendEventEmailSchedule(resendEventEmailDto,event,resendEmailId));

        //assert
        verify(resendTicketingEmailRepository).findById(resendEmailId);
        assertEquals(NotFoundException.NotFound.RECURRING_EVENT_SCHEDULE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @ParameterizedTest
    @MethodSource("getResendTicketSubjectAndResponse")
    void test_updateResendEventEmailSchedule_success(String resendTicketSubject,String ticketSubject) {

        //setup
        resendTicketingEmail.setResendTicketStatus(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);
        resendEventEmailDto.setResendTicketSubject(resendTicketSubject);
        resendEventEmailDto.setResendTime(resendTime);

        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.of(resendTicketingEmail));

        //Execution
        ResendEventEmailGetDto resendEventEmailGetDto = resendTicketingEmailServiceImpl.updateResendEventEmailSchedule(resendEventEmailDto,event,resendEmailId);

        //assert
        ArgumentCaptor<ResendTicketingEmail> resendTicketingEmailArgumentCaptor = ArgumentCaptor.forClass(ResendTicketingEmail.class);
        verify(resendTicketingEmailRepository, times(1)).save(resendTicketingEmailArgumentCaptor.capture());

        assertEquals(resendEventEmailGetDto.getResendTicketSubject(),ticketSubject);
        assertEquals(resendEventEmailGetDto.getId(),resendTicketingEmail.getId());
        assertEquals(resendEventEmailGetDto.getResendTicketStatus(),resendTicketingEmail.getResendTicketStatus().toString());
        assertFalse(resendEventEmailGetDto.isRecurringEvent());
        assertEquals(resendEventEmailGetDto.getResendTicketOrderText(),resendTicketingEmail.getResendTicketOrderText());

        verify(resendTicketingEmailRepository).findById(resendEmailId);
    }

    @Test
    void test_getResendEventEmailScheduleDetails_throwExceptionNoResendEventTicketEmailScheduled() {

        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> resendTicketingEmailServiceImpl.getResendEventEmailScheduleDetails(event,resendEmailId));

        //assert
        verify(resendTicketingEmailRepository).findById(resendEmailId);
        assertEquals(NotAcceptableException.TicketingExceptionMsg.NO_RESEND_EVENT_TICKET_EMAIL_SCHEDULED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getResendEventEmailScheduleDetails_success() {

        //setup
        resendTicketingEmail.setResendTicketSubject("Testing Email");
        resendTicketingEmail.setResendTicketStatus(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED);

        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.of(resendTicketingEmail));

        //Execution
        ResendEventEmailGetDto resendEventEmailGetDto = resendTicketingEmailServiceImpl.getResendEventEmailScheduleDetails(event,resendEmailId);

        //assert
        assertEquals(resendEventEmailGetDto.getResendTicketSubject(),"Testing Email");
        assertEquals(resendEventEmailGetDto.getId(),resendTicketingEmail.getId());
        assertEquals(resendEventEmailGetDto.getResendTicketStatus(),resendTicketingEmail.getResendTicketStatus().toString());
        assertFalse(resendEventEmailGetDto.isRecurringEvent());
        assertEquals(resendEventEmailGetDto.getResendTicketOrderText(),resendTicketingEmail.getResendTicketOrderText());

        verify(resendTicketingEmailRepository).findById(resendEmailId);
    }

    static Object[] getRecurringEventIdAndIsRecurringEventsAndResendTicketingEmailStatus(){
        return new Object[]{
                new Object[]{null,false,ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED,Optional.empty(),null} ,
                new Object[]{null,false,ResendTicketingEmail.ResendTicketingEmailStatus.FAILED,Optional.empty(),null} ,
                new Object[]{1L,true,ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED,Optional.empty(),null} ,
                new Object[]{1L,true,ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED,Optional.of(new RecurringEvents()),new Date()} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventIdAndIsRecurringEventsAndResendTicketingEmailStatus")
    void test_getResendEventEmailScheduledList(Long recurringEventId, boolean isRecurring, ResendTicketingEmail.ResendTicketingEmailStatus resendTicketStatus,
                                                      Optional<RecurringEvents> recurringEventsOptional,Date eventTicketMailResendTime) {

        //setup
        resendTicketingEmail.setRecurringEventId(recurringEventId);
        resendTicketingEmail.setResendTicketStatus(resendTicketStatus);
        resendTicketingEmail.setEventTicketMailResendTime(eventTicketMailResendTime);
        List<ResendTicketingEmail> resendTicketingEmailList = new ArrayList<>();
        resendTicketingEmailList.add(resendTicketingEmail);

        eventDesignDetail.setDefaultResendEmailHoursBeforeEvent(1);

        ticketing.setRecurringEvent(isRecurring);
        ticketing.setEventStartDate(new Date());

        //mock
        when(resendTicketingEmailRepository.findByEventIdOrderByCreatedDate(event.getEventId(),"DESC")).thenReturn(resendTicketingEmailList);
        when(ticketingService.findByEvent(event)).thenReturn(ticketing);
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);


        //Execution
        ResendEventEmailListDto resendEventEmailListDto = resendTicketingEmailServiceImpl.getResendEventEmailScheduledList(event, "DESC");

        //assert
        assertEquals(resendEventEmailListDto.isRecurringEvent(),isRecurring);
        assertEquals(resendEventEmailListDto.getResendEmailHoursBeforeEvent(),eventDesignDetail.getDefaultResendEmailHoursBeforeEvent());
        assertNotNull(resendEventEmailListDto.getList());


        verify(resendTicketingEmailRepository).findByEventIdOrderByCreatedDate(event.getEventId(),"DESC");
        verify(ticketingService).findByEvent(event);
        verify(eventDesignDetailService).findByEvent(event);
    }

    @Test
    void test_findScheduledResendTicketingEmailByEventId() {

        //mock
        when(resendTicketingEmailRepository.findByEventIdAndResendTicketStatusInAndRecurringEventIdNull(1L, List.of(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED))).thenReturn(List.of());

        //Execution
        List<ResendTicketingEmail> resendTicketingEmailList = resendTicketingEmailServiceImpl.findScheduledResendTicketingEmailByEventId(1L);

        //assert

        assertTrue(resendTicketingEmailList.isEmpty());
        verify(resendTicketingEmailRepository).findByEventIdAndResendTicketStatusInAndRecurringEventIdNull(1L, List.of(ResendTicketingEmail.ResendTicketingEmailStatus.SCHEDULED));
    }

    @Test
    void test_deleteResendTicketingEmail_ResendTicketingEmailEmpty() {
        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.empty());

        //Execution
        resendTicketingEmailServiceImpl.deleteResendTicketingEmail(resendEmailId);

        //assert
        verify(resendTicketingEmailRepository).findById(resendEmailId);

    }

    @Test
    void test_deleteResendTicketingEmail_success() {
        //mock
        when(resendTicketingEmailRepository.findById(resendEmailId)).thenReturn(Optional.of(resendTicketingEmail));

        //Execution
        resendTicketingEmailServiceImpl.deleteResendTicketingEmail(resendEmailId);

        //assert
        ArgumentCaptor<ResendTicketingEmail> resendTicketingEmailArgumentCaptor = ArgumentCaptor.forClass(ResendTicketingEmail.class);
        verify(resendTicketingEmailRepository, times(1)).save(resendTicketingEmailArgumentCaptor.capture());

        ResendTicketingEmail actualResendTicketingEmail = resendTicketingEmailArgumentCaptor.getValue();
        assertEquals(actualResendTicketingEmail.getResendTicketStatus(), ResendTicketingEmail.ResendTicketingEmailStatus.DELETED);

        verify(resendTicketingEmailRepository).findById(resendEmailId);

    }
}