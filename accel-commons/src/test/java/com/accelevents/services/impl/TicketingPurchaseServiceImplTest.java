package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.messages.WaitListStatus;
import com.accelevents.notification.services.impl.SendGridMailPrepareServiceImpl;
import com.accelevents.registration.approval.repositories.RegistrationRequestRepository;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.repositories.OrderAuditLogRepo;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.payment.ROPayFlowConfigService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneAttendeeDetailService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.GeneralUtils;
import com.accelevents.utils.PurchaserJsonValidation;
import com.itextpdf.text.DocumentException;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import freemarker.template.TemplateException;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.OngoingStubbing;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingPurchaseServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingPurchaseServiceImpl ticketingPurchaseServiceImpl;

    @Mock
    private TicketingPurchaseService ticketingPurchaseService;
    @Mock
    private ROEventLevelSettingService roEventLevelSettingService;

    @Mock
    private FormRuleEngineService formRuleEngineService;


    Event event;
    EventTickets eventTickets;
    Ticketing ticketing;
    User user, staffUser;
    TicketHolderAttributes ticketHolderAttributes;
    List<TicketPriceDetails> ticketPriceDetailsList;
    TicketingOrderManager ticketingOrderManager, ticketingOrderManager1;
    TicketingOrderDto ticketingOrderDto;
    TicketingType ticketingType;
    TicketingType newTicketingType;
    DisplayAttributeDto displayAttributeDto;
    WaitListSettingsDto waitListSettingsDto;
    WaitList waitList;
    TicketingOrder ticketingOrder, ticketingOrder1;
    TicketBookingDto ticketBookingDto;
    TicketingCoupon ticketingCoupon;
    StripeDTO stripeDTO;
    TicketPriceDetails ticketPriceDetails;
    TicketingTable highetCountTable;
    TicketAttributeDto ticketAttribute;
    TicketHolderRequiredAttributes ticketHolderRequiredAttributes, ticketHolderRequiredAttributes1;
    TrackingLinks trackingLinks;
    CardInfoDto cardInfoDto;
    PurchaserJsonValidation purchaserJsonValidation;
    PurchaserBookingDto purchaserBookingDto;
    HolderBookingDto holderBookingDto;
    TicketAttributeValueDto ticketAttributeValueDto;
    ValueDto valueDto;
    TicketingTable ticketingTable;
    TicketStatus ticketStatus;
    CardInfoDto card;
    private EventDesignDetail eventDesignDetail;
    private SalesTaxFeeDto salesTaxFeeDto;
    private SalesTax salesTax;
    private String accessCode ="test";
    TicketingAccessCode ticketingAccessCode;

    AttendeePartialPaymentDto ticketExchangePaymentDto;

    @Mock
    private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;
    @Mock
    private ROPayFlowConfigService roPayFlowConfigService;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketHolderRequiredAttributesService ticketRequireAttributeService;
    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;
    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Mock
    private GetStreamService getStreamService;

    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingStatisticsService ticketingStatisticsService;
    @Mock
    private TicketingOrderService ticketingOrderService;
    @Mock
    private PaymentHandlerService paymentHandlerService;
    @Mock
    private SeatsIoService seatsIoService;
    @Mock
    private SendGridMailPrepareServiceImpl sendGridMailPrepareService;
    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;
    @Mock
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;
    @Mock
    private TrackingLinksService trackingLinksService;
    @Mock
    private TicketingCouponCodeService ticketingCouponCodeService;
    @Mock
    private TicketingTableService ticketingTableService;
    @Mock
    private WaitListSettingService waitListSettingService;
    @Mock
    private WaitListService waitListService;
    @Mock
    private ROEventService roEventService;
    @Mock
    private TicketingCouponService ticketingCouponService;
    @Mock
    private EventTicketsService eventTicketsService;
    @Mock
    private CommonEventService commonEventService;
    @Mock
    private AutoAssignedAttendeeNumbersService autoAssignedAttendeeNumbersService;
    @Mock
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;
    @Mock
    private EventDesignDetailService eventDesignDetailService;
    @Mock
    private CacheStoreService cacheStoreService;
    @Mock
    private SalesTaxService salesTaxService;
    @Mock
    private TicketingAccessCodeService ticketingAccessCodeService;
    @Mock
    private EventRepoService eventRepoService;
    @Mock
    private TicketingService ticketingService;
    @Mock
    TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Mock
    UserRepoService userRepoService;
    @Mock
    private NeptuneAttendeeDetailService neptuneDBService;
    @Mock
    private PayFlowConfigServiceImpl payFlowConfigService;
    @Mock
    private VatTaxService vatTaxService;
    @Mock
    private OrderAuditLogRepo orderAuditLogRepo;

    @Mock
    private RegistrationRequestsService registrationRequestsService;
    @Mock
    private RegistrationRequestRepository registrationRequestRepository;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Mock TicketingLimitedDisplayCodeService ticketingLimitedDisplayCodeService;
    @Mock
    private EventTicketTransactionService eventTicketTransactionService;

    @Mock
    private RegistrationRequestAndStripeCustomerMappingService registrationRequestAndStripeCustomerMappingService;
    @Mock
    private StripeTransactionService stripeTransactionService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private AllPaymentService allPaymentService;


    private Long id = 1L;

    @BeforeEach
    void setUp() {

        // Prepare data
        event = new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
        event.setEventId(id);
        event.setCurrency(Currency.AUD);
        event.setEventURL("asd");

        ticketingAccessCode = new TicketingAccessCode(event,accessCode,new Date(),new Date(),"Sample",1,1,1);
        ticketing = new Ticketing();
        ticketing.setId(id);
        ticketing.setActivated(true);
        ticketing.setEventid(event);
        ticketing.setEventAddress("testAddress");
        ticketing.setShowRemainingTickets(false);
        ticketing.setEventStartDate(new Date());
        ticketing.setEventEndDate(LocalDate.now().plusDays(10).toDate());
        ticketing.setCheckoutminutes(10);

        user = new User();
        user.setEmail("<EMAIL>");
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setFirstName("Normal");
        user.setLastName("User");
        user.setMostRecentEventId(id);

        staffUser = new User();
        staffUser.setUserId(2L);
        staffUser.setEmail("<EMAIL>");
        staffUser.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        staffUser.setFirstName("Normal");
        staffUser.setLastName("User");
        staffUser.setMostRecentEventId(id);

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);

        TicketPriceDetails ticketPriceDetails = new TicketPriceDetails();
        ticketPriceDetails.setPrice(50.0);
        ticketPriceDetails.setPriceWithFee(53.35);
        ticketPriceDetails.setAeFee(1.5);
        ticketPriceDetails.setDiscounted(false);

        ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

        ticketingOrderManager1 = EventDataUtil.getTicketingOrderManager();

        ticketingOrderDto = new TicketingOrderDto();
        ticketingOrderDto.setNumberOfTicket(1);
        ticketingOrderDto.setTicketTypeId(id);
        ticketingOrderDto.setPrice(50.00);

        displayAttributeDto = new DisplayAttributeDto();

        eventTickets = EventDataUtil.getEventTickets();

        waitList = new WaitList();

        waitListSettingsDto = new WaitListSettingsDto();
        waitListSettingsDto.setWaitListEnabled(true);

        ticketingOrder = EventDataUtil.getTicketingOrder();
        ticketingOrder1 = new TicketingOrder();
        ticketingOrder1.setId(id);
        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder1.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder1.setExpirein(LocalDateTime.now().plusMinutes(10).toDate());
        ticketingOrder1.setEventid(event);

        ticketingCoupon = new TicketingCoupon();

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);

        highetCountTable = new TicketingTable();

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();

        ticketAttribute = new TicketAttributeDto();

        trackingLinks = new TrackingLinks();

        cardInfoDto = new CardInfoDto();

        purchaserBookingDto = new PurchaserBookingDto();

        ticketAttributeValueDto = new TicketAttributeValueDto();

        valueDto = new ValueDto();

        holderBookingDto = new HolderBookingDto();

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName(GENERAL_ADMISSION);
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setPrice(50);
        ticketingType.setEndDate(ticketing.getEventEndDate());
        ticketingType.setStartDate(new Date());
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(60);
        ticketingType.setNumberOfTickets(100);
        ticketingType.setMaxTicketsPerBuyer(10);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType.setTicketing(ticketing);
        ticketingType.setRecurringEventId(id);
        ticketingType.setDataType(DataType.TICKET);

        newTicketingType = new TicketingType();
        newTicketingType.setId(2L);
        newTicketingType.setTicketTypeName(GENERAL_ADMISSION+"_1");
        newTicketingType.setTicketType(TicketType.PAID);
        newTicketingType.setPrice(100);
        newTicketingType.setEndDate(ticketing.getEventEndDate());
        newTicketingType.setStartDate(new Date());
        newTicketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        newTicketingType.setRecurringEventSalesEndTime(60);
        newTicketingType.setNumberOfTickets(100);
        newTicketingType.setNumberOfTicketPerTable(1);
        newTicketingType.setMaxTicketsPerBuyer(10);
        newTicketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        newTicketingType.setTicketing(ticketing);
        newTicketingType.setDataType(DataType.TICKET);

        card = new CardInfoDto();
        card.setId("1000");
        card.setBrand("visa");
        card.setLast4("1111");

        ticketingOrderManager = new TicketingOrderManager();
        ticketingOrderManager.setNumberofticket(1);
        ticketingOrderManager.setSeats("");
        ticketingOrderManager.setSelectSeatsDisplay("");
        ticketingOrderManager.setTicketType(ticketingType);
        ticketingOrderManager.setSelectTable("");

        ticketBookingDto  = new TicketBookingDto();
        ticketBookingDto.setHasholderattributes(false);
        ticketBookingDto.setClientDate(DateUtils.getFormattedDateString(new Date()));
        ticketBookingDto.setTokenOrIntentId("Stripe_Test");

        eventDesignDetail=EventDataUtil.getEventDesignDetail(event);
        salesTaxFeeDto = new SalesTaxFeeDto(true,10d,"1");
        salesTax= new SalesTax();
        salesTax.setAbsorbSalesTax(false);
        salesTax.setSalesTaxRate(10d);
        salesTax.setSalesTaxId("874512L");
        salesTax.setSalesTaxName("SALE-TAX");

        ticketExchangePaymentDto = new AttendeePartialPaymentDto();
        ticketExchangePaymentDto.setPaymentType(CARD);
        ticketExchangePaymentDto.setTokenOrIntentId("cnon:CBASENNUEfPB1VjoMtzRUtSZzXAgAQ");
        ticketExchangePaymentDto.setNote("Paid By Test Case using Card");

    }

    @Test
    void test_bookTicket_successWithoutSeating() {

        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));



        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);
        when(ticketingStatisticsService.getRemainingTicketCount(ticketingType)).thenReturn(99L);
        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(Optional.ofNullable(ticketingAccessCode));
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        OrderDto bookTicket = ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, STRING_EMPTY,accessCode,STRING_EMPTY,0L,null,null);

        //verify
        ArgumentCaptor<TicketingOrder> ticketingOrderCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(ticketingOrderCaptor.capture());
        TicketingOrder savedTicketingOrder = ticketingOrderCaptor.getValue();

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(ticketingOrderManagerCaptor.capture());
        List<TicketingOrderManager> savedTicketingOrderManager = ticketingOrderManagerCaptor.getAllValues();

        assertEquals(savedTicketingOrderManager.size(), 1);
        assertEquals(Optional.of(savedTicketingOrder.getId()), Optional.ofNullable(bookTicket.getOrderId()));
        assertNull(savedTicketingOrder.getHoldToken());
    }

    @Test
    void test_bookTicket_successWithoutSeating_with_staff_nulll() {

        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        ticketingOrderManager1.setId(id);
        ticketingOrderManager1.setTicketType(ticketingType);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        Mockito.doReturn(ticketingOrderManager1).when(ticketingPurchaseServiceImpl).createNewTicketingOrderManagerObject(any(), any(), any());
        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);

        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(Optional.ofNullable(ticketingAccessCode));

        //execute
        OrderDto bookTicket = ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, true, STRING_EMPTY,accessCode, STRING_EMPTY,0L,null,null);

        //verify
        ArgumentCaptor<TicketingOrder> ticketingOrderCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(ticketingOrderCaptor.capture());
        TicketingOrder savedTicketingOrder = ticketingOrderCaptor.getValue();

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(ticketingOrderManagerCaptor.capture());
        List<TicketingOrderManager> savedTicketingOrderManager = ticketingOrderManagerCaptor.getAllValues();

        assertEquals(savedTicketingOrderManager.size(), 1);
        assertEquals(Optional.of(savedTicketingOrder.getId()), Optional.ofNullable(bookTicket.getOrderId()));
        assertNull(savedTicketingOrder.getHoldToken());
    }

    //TODO: Mockito Junit5 re-write
    /*@Test
    void test_bookTicket_throw_NotFoundException_LIMIT_TICKET_AVAILABLE() {
        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        TicketingType ticketingTypeObj = null;

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);


        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);

        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "",STRING_EMPTY,0L,null,null));
        assertEquals(String.format("Ticket type id %d is not valid for event", ticketingOrderDto.getTicketTypeId()), exception.getMessage());
    }*/

    @Test
    void test_checkHiddenField_success_with_isStaff_false() {

        //execute
        boolean hiddenField = ticketingPurchaseServiceImpl.checkHiddenField(true, false);

        assertFalse(hiddenField);
    }

    @Test
    void test_isDefaultAttribute_success_with_attributeName_empty() {

        //setup
        String attributeName = "";

        //execute
        boolean defaultAttribute = ticketingPurchaseServiceImpl.isDefaultAttribute(attributeName);

        assertFalse(defaultAttribute);
    }

    @Test
    void test_isDefaultAttribute_success_with_attributeName_STRING_PASSWORD() {

        //setup
        String attributeName = Constants.STRING_PASSWORD;

        //execute
        boolean defaultAttribute = ticketingPurchaseServiceImpl.isDefaultAttribute(attributeName);

        assertTrue(defaultAttribute);
    }

//    @Test
//    void test_isDefaultAttribute_success_with_attributeName_STRING_CELL_SPACE_PHONE() {
//
//        //setup
//        String attributeName = Constants.STRING_CELL_SPACE_PHONE;
//
//        //execute
//        boolean defaultAttribute = ticketingPurchaseServiceImpl.isDefaultAttribute(attributeName);
//
//        assertTrue(defaultAttribute);
//    }

    @Test
    void test_isPurchaseAttributeRequired_success_with_staffOrder_false() {

        //setup
        displayAttributeDto.setName("name");
        //execute
        boolean purchaseAttributeRequired = ticketingPurchaseServiceImpl.isPurchaseAttributeRequired(user, false, displayAttributeDto, true);

        assertTrue(purchaseAttributeRequired);
    }

    @Test
    void test_bookTicket_throw_NotFoundException_NOT_VALID_TICKET_TYPE_AVAILABLE() {

        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);


        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);
        when(ticketingStatisticsService.getRemainingTicketCount(ticketingType)).thenReturn(0L);
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "",STRING_EMPTY, STRING_EMPTY,0L,null,null));
        
        assertEquals(String.format("Sorry, there are only %d tickets available at this price.", 0), exception.getMessage());
        
    }

    @Test
    void test_bookTicket_successWithSeatingAndNoRecurring() {

        //setup
        ticketing.setChartKey("chart_key");

        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        ticketingOrderDto.setSeatNumbers(Collections.singletonList("a-A-15"));
        ticketingOrderDto.setSeatNumbersDisplay(Collections.singletonList("Row a Seat A"));
        orderTicketings.add(ticketingOrderDto);
        Optional<TicketingAccessCode> optionalTicketingAccessCode = Optional.of(new TicketingAccessCode());

        Date clientDate = new Date();

        ticketingType.setTicketing(ticketing);
        ticketingType.setRecurringEventId(null);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));



        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);
        when(ticketingStatisticsService.getRemainingTicketCount(ticketingType)).thenReturn(99L);




        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(optionalTicketingAccessCode);
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        OrderDto bookTicket = ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, STRING_EMPTY,accessCode, STRING_EMPTY,0L,null,null);

        //verify
        ArgumentCaptor<TicketingOrder> ticketingOrderCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(2)).save(ticketingOrderCaptor.capture());
        TicketingOrder savedTicketingOrder = ticketingOrderCaptor.getValue();

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(ticketingOrderManagerCaptor.capture());
        List<TicketingOrderManager> savedTicketingOrderManager = ticketingOrderManagerCaptor.getAllValues();

        assertEquals(savedTicketingOrderManager.size(), 1);
        assertEquals(Optional.of(savedTicketingOrder.getId()), Optional.ofNullable(bookTicket.getOrderId()));
    }

    @Test
    void test_bookTicket_successWithSeatingAndRecurring() {

        //setup
        ticketing.setChartKey("chart_key");

        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        ticketingOrderDto.setSeatNumbers(Collections.singletonList("a-A-15"));
        ticketingOrderDto.setSeatNumbersDisplay(Collections.singletonList("Row a Seat A"));
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();
        Optional<TicketingAccessCode> optionalTicketingAccessCode = Optional.of(new TicketingAccessCode());


        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));



        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);
        when(ticketingStatisticsService.getRemainingTicketCount(ticketingType)).thenReturn(99L);



        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(optionalTicketingAccessCode);
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        OrderDto bookTicket = ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, STRING_EMPTY,accessCode, STRING_EMPTY,0L,null,null);

        //verify
        ArgumentCaptor<TicketingOrder> ticketingOrderCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(2)).save(ticketingOrderCaptor.capture());
        TicketingOrder savedTicketingOrder = ticketingOrderCaptor.getValue();

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(ticketingOrderManagerCaptor.capture());
        List<TicketingOrderManager> savedTicketingOrderManager = ticketingOrderManagerCaptor.getAllValues();

        assertEquals(Optional.of(savedTicketingOrder.getId()), Optional.ofNullable(bookTicket.getOrderId()));
        assertEquals(savedTicketingOrderManager.size(), 1);
    }

    @Test
    void test_bookTicket_throw_NotFoundException_ZEROORDERCOUNT() {

        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        ticketingOrderDto.setNumberOfTicket(0);
        ticketingOrderDto.setTicketTypeId(0L);
        ticketingOrderDto.setPrice(0.00);
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));

        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(Optional.ofNullable(ticketingAccessCode));
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, STRING_EMPTY,accessCode, STRING_EMPTY,0L,null,null));
        assertEquals(NotFoundException.TicketingOrderExceptionMsg.ZEROORDERCOUNT.getErrorMessage(), exception.getMessage());
    }


//    @Test
//    void test_bookTicket_throw_NotFoundException_NUMBER_OF_SEATS_MUST_BE_EQUAL_TO_NUMBER_OF_TICKETS() {
//
//        //setup
//        ticketing.setChartKey("chart_key");
//
//        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
//        ticketingOrderDto.setSeatNumbers(Arrays.asList(new String[]{"a-A-15", "a-A-16"}));
//        ticketingOrderDto.setSeatNumbersDisplay(Arrays.asList(new String[]{"Row a Seat A", "Row a Seat A"}));
//        orderTicketings.add(ticketingOrderDto);
//
//        Date clientDate = new Date();
//
//        //expected
//
//        //mock
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
//
//        mockTicketingTypeOngoingStubbing(ticketingOrderDto, ticketingType);
//
//        //execute
//        ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "");
//    }

    @Test
    void test_bookTicket_successWithTicketTypeDonation() {

        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        ticketingType.setTicketTypeName("Donation");
        ticketingType.setTicketType(TicketType.DONATION);
        ticketingType.setPrice(0);

        TicketingAccessCode optionalTicketingAccessCode = mock(TicketingAccessCode.class);


        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));



        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);

        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(Optional.ofNullable(ticketingAccessCode));
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        OrderDto bookTicket = ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false,STRING_EMPTY,accessCode, STRING_EMPTY,0L,null,null);

        //verify
        ArgumentCaptor<TicketingOrder> ticketingOrderCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(ticketingOrderCaptor.capture());
        TicketingOrder savedTicketingOrder = ticketingOrderCaptor.getValue();

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(ticketingOrderManagerCaptor.capture());
        List<TicketingOrderManager> savedTicketingOrderManager = ticketingOrderManagerCaptor.getAllValues();

        assertEquals(Optional.of(savedTicketingOrder.getId()), Optional.ofNullable(bookTicket.getOrderId()));
        assertEquals(savedTicketingOrderManager.size(), 1);
        assertEquals(savedTicketingOrderManager.get(0).getTicketType().getTicketType(), TicketType.DONATION);
    }

    // Ticket can be booked now event if event is ended , DEV-9371
//    @Test
//    void test_bookTicket_throw_NotAcceptableException_EVENT_ALREADY_ENDED() {
//
//        ticketing.setEventEndDate(LocalDate.now().minusDays(1).toDate());
//        //setup
//        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
//        ticketingOrderDto.setNumberOfTicket(0);
//        ticketingOrderDto.setTicketTypeId(0L);
//        ticketingOrderDto.setPrice(0.00);
//        orderTicketings.add(ticketingOrderDto);
//
//        Date clientDate = new Date();
//
//        //expected
//
//        //mock
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
//        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
//
//        //execute
//        ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "",STRING_EMPTY,0L);
//    }

//    @Test
//    void test_bookTicket_throw_NotAcceptableException_SEAT_SELECTION_NOT_ALLOWED_FOR_FREE_TICKET() {
//
//        //setup
//        ticketing.setChartKey("chart_key");
//
//        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
//        ticketingOrderDto.setPrice(0.00);
//        ticketingOrderDto.setSeatNumbers(Arrays.asList(new String[]{"a-A-15"}));
//        ticketingOrderDto.setSeatNumbersDisplay(Arrays.asList(new String[]{"Row a Seat A"}));
//        orderTicketings.add(ticketingOrderDto);
//
//        Date clientDate = new Date();
//
//        ticketingType.setTicketType(TicketType.FREE);
//        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
//        ticketingType.setTicketing(ticketing);
//
//        //expected
//
//        //mock
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
//
//        mockTicketingTypeOngoingStubbing(ticketingOrderDto, ticketingType);
//
//        //execute
//        ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "");
//    }

    @Test
    void test_bookTicket_successWithBundleTypeTable() {

        //setup
        ticketing.setChartKey("chart_key");
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        ticketingOrderDto.setSeatNumbers(Arrays.asList("a-A-15", "a-A-16"));
        ticketingOrderDto.setSeatNumbersDisplay(Arrays.asList("Row a Seat A", "Row a Seat A"));
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        ticketingType.setPrice(0);
        ticketingType.setNumberOfTicketPerTable(2);
        ticketingType.setBundleType(TicketBundleType.TABLE);
        ticketingType.setTicketing(ticketing);

        Optional<TicketingAccessCode> optionalTicketingAccessCode = Optional.of(new TicketingAccessCode());

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));



        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);
        when(ticketingStatisticsService.getRemainingTicketCount(ticketingType)).thenReturn(99L);
        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);
        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(optionalTicketingAccessCode);
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        OrderDto bookTicket = ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, STRING_EMPTY,accessCode, STRING_EMPTY,0L,null,null);

        //verify
        ArgumentCaptor<TicketingOrder> ticketingOrderCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(2)).save(ticketingOrderCaptor.capture());
        TicketingOrder savedTicketingOrder = ticketingOrderCaptor.getValue();

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(ticketingOrderManagerCaptor.capture());
        List<TicketingOrderManager> savedTicketingOrderManager = ticketingOrderManagerCaptor.getAllValues();

        assertEquals(Optional.of(savedTicketingOrder.getId()), Optional.ofNullable(bookTicket.getOrderId()));
        assertEquals(savedTicketingOrderManager.size(), 1);
    }

//    @Test
//    void test_bookTicket_getNumberOfTickets_return_0() {
//
//        //setup
//        ticketing.setChartKey("chart_key");
//
//        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
//        ticketingOrderDto.setPrice(0.00);
//        ticketingOrderDto.setSeatNumbers(Arrays.asList(new String[]{"a-A-15"}));
//        ticketingOrderDto.setSeatNumbersDisplay(Arrays.asList(new String[]{"Row a Seat A"}));
//        orderTicketings.add(ticketingOrderDto);
//
//        Date clientDate = new Date();
//
//        ticketingType.setTicketType(TicketType.DONATION);
//        ticketingType.setTicketing(ticketing);
//
//        //expected
//
//        //mock
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
//
//        mockTicketingTypeOngoingStubbing(ticketingOrderDto, ticketingType);
//
//        //execute
//        ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "");
//    }

    @Test
    void test_bookTicket_successWithOrderId() {

        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(id);
        Optional<TicketingAccessCode> optionalTicketingAccessCode = Optional.of(new TicketingAccessCode());


        //mock
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.findByid(id));




        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(true);

        when(ticketingTypeService.findByidAndEvent(ticketingOrderDto.getTicketTypeId(), event)).thenReturn(ticketingType);
        when(ticketingStatisticsService.getRemainingTicketCount(ticketingType)).thenReturn(99L);
        doReturn(Optional.of(salesTax)).when(salesTaxService).getSalesTaxByEvent(event);
        when(ticketingAccessCodeService.getByCode(accessCode,event,0L)).thenReturn(optionalTicketingAccessCode);
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        OrderDto bookTicket = ticketingPurchaseServiceImpl.bookTicket(id, event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, STRING_EMPTY,accessCode, STRING_EMPTY,0L,null,null);

        //verify
        ArgumentCaptor<TicketingOrder> ticketingOrderCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(2)).save(ticketingOrderCaptor.capture());
        TicketingOrder savedTicketingOrder = ticketingOrderCaptor.getValue();

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(ticketingOrderManagerCaptor.capture());
        List<TicketingOrderManager> savedTicketingOrderManager = ticketingOrderManagerCaptor.getAllValues();

        assertEquals(Optional.of(savedTicketingOrder.getId()), Optional.ofNullable(bookTicket.getOrderId()));
        assertEquals(savedTicketingOrderManager.size(), 1);
    }

    //TODO: Fix after mockito version upgrade
//    @Test
//    void test_purchaseTickets() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
//        //setup
//        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");
//        List<HolderBookingDto> holder = new ArrayList<>();
//        HolderBookingDto holderBookingDto = new HolderBookingDto();
//        holderBookingDto.setAttributes(attributeKeyValueDtos);
//        holderBookingDto.setTickettypeid(ticketingType.getId());
//        holder.add(holderBookingDto);
//        ticketBookingDto.setHolder(holder);
//        PurchaserBookingDto purchaser = new PurchaserBookingDto();
//        purchaser.setAttributes(attributeKeyValueDtos);
//
//        ticketBookingDto.setPurchaser(purchaser);
//        ticketingOrderManager.setRecurringEventId(0L);
//        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
//        ticketingOrderManagerList.add(ticketingOrderManager);
//
//        //mock
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
//        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));
//
//        mockCreateTicketPurchaserPayment(card);
//        mockEventDesignDetailByEvent();
//        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
//
//        mockSendmailList();
//
//        doThrow(new RuntimeException()).when(sendGridMailPrepareService).sendTicketingPurchaseEmail(any(), any(), anyList(), anyLong(),
//                anyString(), anyString(), any(), anyString(), any(),
//                any(), anyString(), anyBoolean(), anyBoolean(),anyBoolean());
//        mockTicketPriceDetails();
//        mockGetUserByEmail(user);
//        when(cacheStoreService.get(anyLong())).thenReturn(null);
//        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
//
//        //execute
//        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false);
//
//        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
//        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
//
//        mockSendTicketingPurchaserEmail(verify(sendGridMailPrepareService));
//
//        assertNotNull(ticketsArgumentCaptor.getValue());
//
//    }

    @Test
    void test_purchaseTicketsWithSeating() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {

        //setup
        user.setPhoneNumber(0L);

        ticketingOrder.setHoldToken("5e678844-7d98-4e86-b0c7-9c9bb5a7d91b");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);

        ticketBookingDto.setPurchaser(purchaser);

        ticketingOrderManager.setSeats("a-A-15");
        ticketingOrderManager.setSelectSeatsDisplay("a-A-15");
        ticketingOrderManager.setSelectTable("T1");
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManager.setRecurringEventId(0L);
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));

        mockCreateTicketPurchaserPayment(card);

        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        mockTicketPriceDetails();
        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", staffUser, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(eventTickets.getSeatNumber());
        assertNotNull(eventTickets.getSeatNumberDisplay());
    }

    @Test
    void test_purchaseTicketsWithRecurringAndSeating() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        user.setPhoneNumber(9898989898L);
        ticketingOrder.setHoldToken("5e678844-7d98-4e86-b0c7-9c9bb5a7d91b");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList(EMAIL, "<EMAIL>");
        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);
        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        ticketingType.setRecurringEventId(1L);
        ticketingType.setRecurringEvent(new RecurringEvents(1L));
        ticketingOrderManager.setRecurringEventId(0L);
        ticketingOrderManager.setSeats("a-A-15");
        ticketingOrderManager.setSelectSeatsDisplay("a-A-15");
        ticketingOrderManager.setSelectTable("T1");
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));

        mockCreateTicketPurchaserPayment(card);

        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        mockTicketPriceDetails();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));

        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", staffUser, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(eventTickets.getSeatNumber());
        assertNotNull(eventTickets.getSeatNumberDisplay());
        assertNotNull(eventTickets.getRecurringEventId());
    }

    @Test
    void test_purchaseTicket_with_ticketingOrderManager_selecttable_empty() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        user.setPhoneNumber(9898989898L);
        user.setUserId(1L);
        ticketingOrder1.setHoldToken("5e678844-7d98-4e86-b0c7-9c9bb5a7d91b");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);
        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);

        ticketBookingDto.setPurchaser(purchaser);

        ticketingType.setRecurringEvent(new RecurringEvents(id));

        ticketingOrderManager.setSeats("a-A-15");
        ticketingOrderManager.setSelectSeatsDisplay("a-A-15");
        ticketingOrderManager.setRecurringEventId(0L);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        mockCreateTicketPurchaserPayment(card);
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        mockTicketPriceDetails();
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", staffUser, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(eventTickets.getSeatNumber());
        assertNotNull(eventTickets.getSeatNumberDisplay());
        assertNotNull(eventTickets.getRecurringEventId());
    }

    @Test
    void test_purchaseTicket_with_recurringIdLiss_empty() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        user.setPhoneNumber(9898989898L);
        ticketingOrder1.setHoldToken("5e678844-7d98-4e86-b0c7-9c9bb5a7d91b");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");
        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);
        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        ticketingType.setRecurringEvent(new RecurringEvents(id));
        ticketingOrderManager.setRecurringEventId(0L);
        ticketingOrderManager.setSeats("a-A-15");
        ticketingOrderManager.setSelectSeatsDisplay("a-A-15");
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Set<Long> recurringIds = new HashSet<>();
        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));
        mockEventDesignDetailByEvent();
        mockCreateTicketPurchaserPayment(card);
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockSendmailList();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        mockTicketPriceDetails();

        Mockito.doReturn(recurringIds).when(ticketingPurchaseServiceImpl).getRecurringIds(anyList());


        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);


        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", staffUser, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        verify(seatsIoService, Mockito.times(1)).bookTicketsInSeatsIo(anyString(), anyLong(), anyString(), anyString());

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(eventTickets.getSeatNumber());
        assertNotNull(eventTickets.getSeatNumberDisplay());
        assertNotNull(eventTickets.getRecurringEventId());
    }

    @Test
    void test_purchaseTicketsWithUnPaidOrder() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        ticketingOrder1.setOrderType(TicketingOrder.OrderType.UNPAID);

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");
        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);
        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);
        ticketBookingDto.setTokenOrIntentId("");

        CardInfoDto card = null;
        ticketingOrderManager.setRecurringEventId(0L);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));
        mockCreateTicketPurchaserPayment(card);
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        mockTicketPriceDetails();

        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertEquals(eventTickets.getTicketPaymentStatus(), TicketPaymentStatus.UNPAID);
    }

    @Test
    void test_purchaseTicketsWithNewUser() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        user.setFirstName(null);
        user.setLastName(null);

        ticketingOrder1.setOrderType(TicketingOrder.OrderType.UNPAID);

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("First Name");
        keyValueDto.setValue("Normal");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Last Name");
        keyValueDto.setValue("User");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Email");
        keyValueDto.setValue("<EMAIL>");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("phoneNumber");
        keyValueDto.setValue("5417541234");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("countryCode");
        keyValueDto.setValue("US");
        attributeKeyValueDtos.add(keyValueDto);

        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);
        ticketBookingDto.setTokenOrIntentId("");

        CardInfoDto card = null;

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManager.setRecurringEventId(0L);
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));
        mockCreateTicketPurchaserPayment(card);
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        mockTicketPriceDetails();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false,false, 0l);

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService, Mockito.times(1)).save(userArgumentCaptor.capture());
        User user = userArgumentCaptor.getValue();

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(user.getEmail());
        assertNotNull(user.getFirstName());
        assertNotNull(user.getLastName());
    }

    //TODO: Fix after mockito version upgrade

   /* @Test
    void test_purchaseTicketsWithDiscountCoupon() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {

        //setup

        user.setPhoneNumber(0L);
        TicketingCoupon ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setName("TestDC");
        ticketingCoupon.setAmount(10);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);

        ticketingOrder1.setTicketingCoupon(ticketingCoupon);

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        CardInfoDto card = null;

        ticketingOrderManager.setNumberOfDiscountedTicket(10);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.findByidAndEventid(anyLong(), any()));

        mockCreateTicketPurchaserPayment(card);

        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);

        mockRemoveCouponFromTicketingOrder();

        doReturn(null).when(ticketingCouponCodeService).applyCouponCode(anyString(), anyLong(), any());

        mockSendTicketingPurchaserEmail(doNothing().when(sendGridMailPrepareService));
        mockEventDesignDetailByEvent();
        mockSendmailList();

        mockBookTicketsInSeatsIo();
        mockTicketPriceDetails();
        when(cacheStoreService.get(anyLong())).thenReturn(null);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "token", "", null, "TestTrackingUrl", "", false);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());

        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder resTicketingOrder = orderArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(resTicketingOrder.getTicketingCoupon());
        assertEquals(resTicketingOrder.getTicketingCoupon().getName(), ticketingCoupon.getName());
    }

    private void mockRemoveCouponFromTicketingOrder() {
        doNothing().when(ticketingCouponCodeService).removeCouponFromTicketingOrder(anyString(), anyLong());
    }

    @Test
    void test_purchaseTicketsWith100PercentDiscountCoupon() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {

        //setup
        TicketingCoupon ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setName("TestDC");
        ticketingCoupon.setAmount(100);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);

        ticketingOrder1.setTicketingCoupon(ticketingCoupon);

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        CardInfoDto card = null;

        ticketingOrderManager.setNumberOfDiscountedTicket(10);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.findByidAndEventid(anyLong(), any()));

        mockCreateTicketPurchaserPayment(card);

        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);

        mockRemoveCouponFromTicketingOrder();

        doReturn(null).when(ticketingCouponCodeService).applyCouponCode(anyString(), anyLong(), any());
        mockEventDesignDetailByEvent();
        mockSendTicketingPurchaserEmail(doNothing().when(sendGridMailPrepareService));

        mockSendmailList();

        mockBookTicketsInSeatsIo();
        mockTicketPriceDetails();
        when(cacheStoreService.get(anyLong())).thenReturn(null);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "token", "", null, "TestTrackingUrl", "", false);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertEquals(eventTickets.getPaidAmount(), 53.35, 2);
    }

    @Test
    void test_purchaseTicketsWithCOMPLEMENTARY() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {

        //setup
        ticketingOrder1.setOrderType(TicketingOrder.OrderType.COMPLEMENTARY);

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        CardInfoDto card = null;

        ticketingOrderManager.setNumberOfDiscountedTicket(10);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.findByidAndEventid(anyLong(), any()));

        mockCreateTicketPurchaserPayment(card);

        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);

        mockRemoveCouponFromTicketingOrder();

        doReturn(null).when(ticketingCouponCodeService).applyCouponCode(anyString(), anyLong(), any());
        mockEventDesignDetailByEvent();
        mockSendTicketingPurchaserEmail(doNothing().when(sendGridMailPrepareService));

        mockSendmailList();

        mockBookTicketsInSeatsIo();
        mockTicketPriceDetails();
        when(cacheStoreService.get(anyLong())).thenReturn(null);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "token", "", null, "TestTrackingUrl", "", false);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertEquals(eventTickets.getPaidAmount(), 53.35 , 2);
    }

    @Test
    void test_purchaseTicketsWithDiscountCouponAndCashPayment() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {

        //setup
        TicketingCoupon ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setName("TestDC");
        ticketingCoupon.setAmount(10);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);

        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CASH);
        ticketingOrder1.setTicketingCoupon(ticketingCoupon);

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        CardInfoDto card = null;

        ticketingOrderManager.setNumberOfDiscountedTicket(10);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.findByidAndEventid(anyLong(), any()));

        mockCreateTicketPurchaserPayment(card);

        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);

        mockRemoveCouponFromTicketingOrder();

        doReturn(null).when(ticketingCouponCodeService).applyCouponCode(anyString(), anyLong(), any());

        mockSendTicketingPurchaserEmail(doNothing().when(sendGridMailPrepareService));
        mockEventDesignDetailByEvent();
        mockSendmailList();

        mockBookTicketsInSeatsIo();
        mockTicketPriceDetails();
        when(cacheStoreService.get(anyLong())).thenReturn(null);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "token", "", user, "TestTrackingUrl", "", false);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(eventTickets.getTicketingOrder().getTicketingCoupon());
        assertEquals(eventTickets.getTicketingOrder().getTicketingCoupon().getName(), ticketingCoupon.getName());
        assertEquals(eventTickets.getTicketingOrder().getOrderType(), TicketingOrder.OrderType.CASH);
    }*/

    @Test
    void test_purchaseTicketsWithHolderData() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("First Name");
        keyValueDto.setValue("Normal");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Last Name");
        keyValueDto.setValue("User");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Email");
        keyValueDto.setValue("<EMAIL>");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Billing Address");
        keyValueDto.setValue("a|a|a|US|123");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Shipping Address");
        keyValueDto.setValue("a|a|a|US|123");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Cell Phone");
        keyValueDto.setValue("5417541234|US");
        attributeKeyValueDtos.add(keyValueDto);

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);
        purchaser.setSkipHolderInfo(false);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManager.setRecurringEventId(0L);
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);

        TicketBookingDto ticketBookingDto  = new TicketBookingDto();
        ticketBookingDto.setPurchaser(purchaser);
        ticketBookingDto.setHasholderattributes(true);
        ticketBookingDto.setHolder(holder);
        ticketBookingDto.setClientDate(DateUtils.getFormattedDateString(new Date()));
        ticketBookingDto.setTokenOrIntentId("stripe_test");

        TicketHolderRequiredAttributes billingAddress = new TicketHolderRequiredAttributes();
        billingAddress.setName("Billing Address");

        TicketHolderRequiredAttributes shippingAddress = new TicketHolderRequiredAttributes();
        shippingAddress.setName("Shipping Address");
        Optional<User> optionalUser = Optional.empty();
        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));
        when(roUserService.getUserByAEPhoneNumber(any())).thenReturn(optionalUser);
        mockCreateTicketPurchaserPayment(card);
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        mockTicketPriceDetails();


        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false,false, 0l);

        ArgumentCaptor<TicketHolderAttributes> holderAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderAttributes.class);
        verify(ticketHolderAttributesService, Mockito.times(2)).save(holderAttributesArgumentCaptor.capture());

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(holderAttributesArgumentCaptor.getValue());
    }

    @Test
    void test_addUserPhone_success_with_user() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Cell Phone");
        keyValueDto.setValue("5417541234|US");
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();

        attributeKeyValueDtos.add(keyValueDto);

        when(roUserService.getUserByAEPhoneNumber(any())).thenReturn(Optional.of(user));

        //execute
        ticketingPurchaseServiceImpl.addUserPhone(user, attributeKeyValueDtos);

    }

    @Test
    void test_addUserPhone_success2() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Cell Phone");
        keyValueDto.setValue(" |US");
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();

        attributeKeyValueDtos.add(keyValueDto);



        //execute
        ticketingPurchaseServiceImpl.addUserPhone(user, attributeKeyValueDtos);
    }

    @Test
    void test_purchaseTicketsWithHasholderattributesTrue() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);
        ticketingOrderManager.setRecurringEventId(0L);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<HolderBookingDto> holder = new ArrayList<>();

        TicketBookingDto ticketBookingDto  = new TicketBookingDto();
        ticketBookingDto.setPurchaser(purchaser);
        ticketBookingDto.setHasholderattributes(true);
        ticketBookingDto.setHolder(holder);
        ticketBookingDto.setClientDate(DateUtils.getFormattedDateString(new Date()));
        ticketBookingDto.setTokenOrIntentId("stripe_test");

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));
        mockCreateTicketPurchaserPayment(card);
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.empty());
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        mockTicketPriceDetails();
        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<TicketHolderAttributes> holderAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderAttributes.class);
        verify(ticketHolderAttributesService, Mockito.times(2)).save(holderAttributesArgumentCaptor.capture());

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());

        Class<ArrayList<User>> listUserClass = (Class<ArrayList<User>>)(Class)ArrayList.class;

        ArgumentCaptor<List<User>> ticketsArgumentForUserCaptor = ArgumentCaptor.forClass(listUserClass);
        verify(userService).saveAll(ticketsArgumentForUserCaptor.capture());

        User userData = ticketsArgumentForUserCaptor.getValue().get(0);
        assertEquals(userData.getFirstName(),attributeKeyValueDtos.get(0).getValue());
        assertEquals(userData.getLastName(),attributeKeyValueDtos.get(1).getValue());
        assertEquals(userData.getEmail(),attributeKeyValueDtos.get(2).getValue());

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertNotNull(holderAttributesArgumentCaptor.getValue());
    }

    @Test
    void test_purchaseTicketsWithTicketTypeTable() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        user.setFirstName(null);
        user.setLastName(null);

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");
        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);
        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        CardInfoDto card = null;

        ticketingType.setMaxTicketsPerBuyer(10);
        ticketingType.setBundleType(TicketBundleType.TABLE);
        ticketingType.setNumberOfTicketPerTable(5);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManager.setRecurringEventId(0L);
        ticketingOrderManagerList.add(ticketingOrderManager);

        TicketingTable highetCountTable = new TicketingTable();
        highetCountTable.setEventId(event);
        highetCountTable.setTableNoSequence(5);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot( any(),anyLong()));
        mockCreateTicketPurchaserPayment(card);
        mockAllOrderById(any(), ticketingOrderManagerList);
        when(ticketingTableService.findOneByEventIdDesc(any())).thenReturn(highetCountTable);
        mockEventDesignDetailByEvent();
        mockSendmailList();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        mockTicketPriceDetails();


        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService, Mockito.times(1)).save(userArgumentCaptor.capture());

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(5)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(ticketsArgumentCaptor.getValue());
        assertEquals(eventTickets.getTicketingTypeId().getBundleType(), TicketBundleType.TABLE);

    }

    @Test
    void test_getFormAttributes_success() {

        Long orderid = id;
        ticketingCoupon.setId(id);
        ticketingOrder1.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CASH);
        ticketingOrder1.setExpirein( new Date("08/05/2020"));
        ticketingOrder1.setTicketingCoupon(ticketingCoupon);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManagers.add(ticketingOrderManager1);

        ticketPriceDetails = new TicketPriceDetails();
        ticketPriceDetails.setDiscountedAmount(10d);
        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

                //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockStripeFeeByEvent();
        mockAllOrderById(any(), ticketingOrderManagers);
        Mockito.doNothing().when(ticketingPurchaseServiceImpl).setBuyerAndHolderData(any(), anyBoolean(), anyBoolean(), anyList(), anyList(), anyList(), any(), anyList(), anyList(), anyList(), anyList(), any(),anyLong(), any());
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any() ,anyBoolean(), any(), any(), anyInt(), anyInt(), any(), any(), anyBoolean(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(ticketPriceDetailsList);

        //execute
        TicketAttributeDto ticketAttributeDtoData = ticketingPurchaseServiceImpl.getFormAttributes(false, ticketingOrder1, event, orderid, user, true,0L);

        assertEquals(ticketAttributeDtoData.getOrderId(), orderid);
        assertTrue(ticketAttributeDtoData.getOrderData().iterator().next().getPrice() == ticketingOrderManager1.getTicketType().getPrice());
    }

    @Test
    void test_filterAttributes_throwException()  {

        //setup
        displayAttributeDto.setEventTicketTypeId("1,2,null");
        List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
        holderAttributesDto.add(displayAttributeDto);

        long ticketTypeId = id;

        //execute
        try {
            ticketingPurchaseServiceImpl.filterAttributes(holderAttributesDto, ticketTypeId);
        }catch (NumberFormatException e){}
    }

    @Test
    void test_setBuyerAndHolderData_success()  {

        //setup
        List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
        List<DisplayNestedQueDto> holderNestedQueDto = new ArrayList<>();
        List<DisplayAttributeDto> holderQuestionDto  = new ArrayList<>();
        List<DisplayAttributeDto> purchaserAttributesDto  = new ArrayList<>();
        List<DisplayNestedQueDto> purchaserNestedQueDto  = new ArrayList<>();
        List<DisplayAttributeDto> purchaserQuestion  = new ArrayList<>();
        List<TicketingOrderManager> ticketingOrderManagers  = new ArrayList<>();

        ticketHolderRequiredAttributes.setName("jon");
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId("1");
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setHiddenForHolder(true);
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        //Execution
        ticketingPurchaseServiceImpl.setBuyerAndHolderData(event, false, false, holderAttributesDto, holderNestedQueDto, holderQuestionDto, user, purchaserAttributesDto, purchaserNestedQueDto, purchaserQuestion, ticketingOrderManagers, ticketAttribute,0L, new ArrayList<>());

        //no need to assertion
    }

    @Test
    void test_setBuyerAndHolderData_success1()  {

        //setup
        List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
        List<DisplayAttributeDto> holderQuestionDto  = new ArrayList<>();
        List<DisplayNestedQueDto> holderNestedQueDto = new ArrayList<>();
        List<DisplayAttributeDto> purchaserAttributesDto  = new ArrayList<>();
        List<DisplayNestedQueDto> purchaserNestedQueDto  = new ArrayList<>();
        List<DisplayAttributeDto> purchaserQuestion  = new ArrayList<>();
        List<TicketingOrderManager> ticketingOrderManagers  = new ArrayList<>();

        ticketHolderRequiredAttributes.setName("jon");
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId("");
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId("");
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setHiddenForHolder(true);
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        //Execution
        ticketingPurchaseServiceImpl.setBuyerAndHolderData(event, true, false, holderAttributesDto, holderNestedQueDto, holderQuestionDto, user, purchaserAttributesDto, purchaserNestedQueDto, purchaserQuestion, ticketingOrderManagers, ticketAttribute,0L, new ArrayList<>());

        //no need to assertion
    }

    @Test
    void test_setBuyerAndHolderData_success2()  {

        //setup
        List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
        List<DisplayAttributeDto> holderQuestionDto  = new ArrayList<>();
        List<DisplayNestedQueDto> holderNestedQueDto = new ArrayList<>();
        List<DisplayAttributeDto> purchaserAttributesDto  = new ArrayList<>();
        List<DisplayNestedQueDto> purchaserNestedQueDto  = new ArrayList<>();
        List<DisplayAttributeDto> purchaserQuestion  = new ArrayList<>();
        List<TicketingOrderManager> ticketingOrderManagers  = new ArrayList<>();

        ticketHolderRequiredAttributes.setName("jon");
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId("");
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId("");
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setHiddenForHolder(true);
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        //Execution
        ticketingPurchaseServiceImpl.setBuyerAndHolderData(event, true, false, holderAttributesDto,  holderNestedQueDto, holderQuestionDto, user, purchaserAttributesDto, purchaserNestedQueDto, purchaserQuestion, ticketingOrderManagers, ticketAttribute,0L, new ArrayList<>());

        //no need to assertion
    }

    @Test
    void test_setBuyerAndHolderData_success3()  {

        //setup
        user.setPhoneNumber(0);
        List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
        List<DisplayAttributeDto> holderQuestionDto  = new ArrayList<>();
        List<DisplayNestedQueDto> holderNestedQueDto = new ArrayList<>();
        List<DisplayAttributeDto> purchaserAttributesDto  = new ArrayList<>();
        List<DisplayNestedQueDto> purchaserNestedQueDto  = new ArrayList<>();
        List<DisplayAttributeDto> purchaserQuestion  = new ArrayList<>();
        List<TicketingOrderManager> ticketingOrderManagers  = new ArrayList<>();

        ticketHolderRequiredAttributes.setName("jon");
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId("");
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId("");
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setHiddenForHolder(true);
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);
        displayAttributeDto.setName("Cell Phone");


        //Execution
        ticketingPurchaseServiceImpl.setBuyerAndHolderData(event, true, false, holderAttributesDto,  holderNestedQueDto, holderQuestionDto, user, purchaserAttributesDto, purchaserNestedQueDto, purchaserQuestion, ticketingOrderManagers, ticketAttribute,0L, new ArrayList<>());

        //no need to assertion
    }

    @Test
    void test_purchaseTicket_success() throws Exception {

        //setup
        user.setPhoneNumber(0L);
        long orderId = id;
        String token = "token";
        String name = "name";
        String trackingUrl = "trackinUrl";
        String waitListIds = "1";
        String paymentIntentId = "1";
        ticketingOrder.setHoldToken("holdToken");
        ticketingCoupon.setName("coupon name");
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setOrderDate(new Date());

        eventTickets.setPaidAmount(100d);
        eventTickets.setWlAFeeAmount(1d);
        eventTickets.setWlBFeeAmount(1d);
        eventTickets.setAeFeeAmount(1d);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        ticketingOrderManager1.setTicketType(ticketingType);
        ticketingOrderManager1.setSeats("A-1-15");
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager1);

        Set<Long> recurringEventIds = new HashSet<>();
        recurringEventIds.add(id);

        List<String> eventKeys = new ArrayList<>();
        eventKeys.add("eventKey");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        purchaserBookingDto.setAttributes(attributeKeyValueDtos);
        ticketBookingDto.setPurchaser(purchaserBookingDto);

        cardInfoDto.setBrand("VISA");
        cardInfoDto.setLast4("1111");

        PurchaserJsonValidation purchaserJsonValidation1 = new PurchaserJsonValidation(ticketBookingDto, user);
        PurchaserJsonValidation spy = Mockito.spy(purchaserJsonValidation1);



        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketOrderForCheckout();
        mockTrackingLink(trackingUrl, event, trackingLinks);
//        mockAddUserPhone(Mockito.doNothing().when(ticketingPurchaseServiceImpl), any(), anyList());
        mockSaveAndGetEventTickets(eventTicketsList);
        when(paymentHandlerService.createTicketPurchasePayment(any(), any(), anyDouble(), anyDouble(), anyBoolean(), anyString(), anyString(), anyLong(),
                any(), any(), anyDouble(), anyDouble(), any(), anyString())).thenReturn(cardInfoDto);
        mockAllOrderById(any(), ticketingOrderManagerList);
        Mockito.doNothing().when(ticketingPurchaseServiceImpl).checkRecurringIdsArePresentNotDeletedAndNotCancelAndEventCapacity(any(), any());
        Mockito.doReturn(recurringEventIds).when(ticketingPurchaseServiceImpl).getRecurringIds(any());
        Mockito.doNothing().when(ticketingPurchaseServiceImpl).handleWaitlist(any(), anyList(), any());
        mockHandleEmail();


        //Execution
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, orderId, user, token, name, null, trackingUrl, waitListIds, paymentIntentId, false);

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, times(2)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder ticketingOrderData = ticketingOrderArgumentCaptor.getValue();
        assertEquals(ticketingOrderData.getBuyerEmail(), user.getEmail());
    }

    @Test
    void test_purchaseTicket_throwException_CREDIT_CARD_PROCESSING_NOT_ENABLE() throws Exception {

        //setup
        user.setPhoneNumber(0L);
        user.setLastName("");
        user.setFirstName("");
        long orderId = id;
        String token = "token";
        String name = "name";
        String trackingUrl = "";
        String waitListIds = "";
        String paymentIntentId = "1";

        ticketing.setActivated(false);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, orderId, user, token, name, staffUser, trackingUrl, waitListIds, paymentIntentId, false));
        
        assertEquals(NotAcceptableException.PaymentCreationExceptionMsg.CREDIT_CARD_PROCESSING_NOT_ENABLE.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_purchaseTicket_success1() throws Exception {

        //setup
        user.setPhoneNumber(0L);
        user.setLastName("");
        user.setFirstName("");
        long orderId = id;
        String token = "token";
        String name = "name";
        String trackingUrl = "";
        String waitListIds = "";
        String paymentIntentId = "1";
        ticketingOrder.setHoldToken("");
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
        ticketingOrder.setTicketingCoupon(null);

        ticketing.setActivated(true);

        eventTickets.setPaidAmount(100d);
        eventTickets.setWlAFeeAmount(1d);
        eventTickets.setWlBFeeAmount(1d);
        eventTickets.setAeFeeAmount(1d);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        ticketingOrderManager1.setTicketType(ticketingType);
        ticketingOrderManager1.setSeats("A-1-15");
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager1);

        Set<Long> recurringEventIds = new HashSet<>();

        List<String> eventKeys = new ArrayList<>();
        eventKeys.add("eventKey");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        purchaserBookingDto.setAttributes(attributeKeyValueDtos);
        ticketBookingDto.setPurchaser(purchaserBookingDto);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketOrderForCheckout();
//        mockTrackingLink(trackingUrl, event, trackingLinks);
//        mockAddUserPhone(Mockito.doNothing().when(ticketingPurchaseServiceImpl), any(), anyList());
        mockSaveAndGetEventTickets(eventTicketsList);

        mockAllOrderById(any(), ticketingOrderManagerList);
        Mockito.doNothing().when(ticketingPurchaseServiceImpl).checkRecurringIdsArePresentNotDeletedAndNotCancelAndEventCapacity(any(), any());


        mockHandleEmail();


        PurchaserJsonValidation purchaserJsonValidation = new PurchaserJsonValidation(ticketBookingDto, user);
        PurchaserJsonValidation spy = Mockito.spy(purchaserJsonValidation);


        //Execution
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, orderId, user, token, name, staffUser, trackingUrl, waitListIds, paymentIntentId, false);

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService, times(1)).save(userArgumentCaptor.capture());

        User userData = userArgumentCaptor.getValue();
        assertEquals(userData.getEmail(), user.getEmail());
        assertEquals(userData.getFirstName(), user.getFirstName());
        assertEquals(userData.getLastName(), user.getLastName());

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, times(2)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder ticketingOrderData = ticketingOrderArgumentCaptor.getValue();
        assertEquals(ticketingOrderData.getBuyerEmail(), user.getEmail());
        assertEquals(ticketingOrderData.getStatus(), TicketingOrder.TicketingOrderStatus.PAID);
    }

    @Test
    void test_purchaseTicket_success2() throws Exception {

        //setup
        user.setPhoneNumber(98989898l);
        user.setLastName("");
        user.setFirstName("");
        long orderId = id;
        String token = "token";
        String name = "name";
        String trackingUrl = "";
        String waitListIds = "";
        String paymentIntentId = "1";
        ticketingOrder.setHoldToken(token);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
        ticketingOrder.setTicketingCoupon(null);

        ticketing.setActivated(true);

        eventTickets.setPaidAmount(100d);
        eventTickets.setWlAFeeAmount(1d);
        eventTickets.setWlBFeeAmount(1d);
        eventTickets.setAeFeeAmount(1d);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        ticketingOrderManager1.setTicketType(ticketingType);
        ticketingOrderManager1.setSeats("A-1-15");
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager1);

        Set<Long> recurringEventIds = new HashSet<>();

        List<String> eventKeys = new ArrayList<>();
        eventKeys.add("eventKey");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        purchaserBookingDto.setAttributes(attributeKeyValueDtos);
        ticketBookingDto.setPurchaser(purchaserBookingDto);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketOrderForCheckout();
//        mockTrackingLink(trackingUrl, event, trackingLinks);
//        mockAddUserPhone(Mockito.doNothing().when(ticketingPurchaseServiceImpl), any(), anyList());
        mockSaveAndGetEventTickets(eventTicketsList);
        when(paymentHandlerService.createTicketPurchasePayment(any(), any(), anyDouble(), anyDouble(), anyBoolean(), anyString(), anyString(), anyLong(), any(), any(), anyDouble(), anyDouble(), any(), any())).thenReturn(null);
        mockAllOrderById(any(), ticketingOrderManagerList);
        Mockito.doNothing().when(ticketingPurchaseServiceImpl).checkRecurringIdsArePresentNotDeletedAndNotCancelAndEventCapacity(any(), any());
        Mockito.doReturn(recurringEventIds).when(ticketingPurchaseServiceImpl).getRecurringIds(any());

        mockHandleEmail();


        PurchaserJsonValidation purchaserJsonValidation = new PurchaserJsonValidation(ticketBookingDto, user);
        PurchaserJsonValidation spy = Mockito.spy(purchaserJsonValidation);


        //Execution
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, orderId, user, token, name, staffUser, trackingUrl, waitListIds, paymentIntentId, false);

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService, times(1)).save(userArgumentCaptor.capture());

        User userData = userArgumentCaptor.getValue();
        assertEquals(userData.getEmail(), user.getEmail());
        assertEquals(userData.getFirstName(), user.getFirstName());
        assertEquals(userData.getLastName(), user.getLastName());

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, times(2)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder ticketingOrderData = ticketingOrderArgumentCaptor.getValue();
        assertEquals(ticketingOrderData.getBuyerEmail(), user.getEmail());
        assertEquals(ticketingOrderData.getStatus(), TicketingOrder.TicketingOrderStatus.PAID);
    }

    @Test
    void test_getBookedSeats_success()  {

        //setup
        List<String> tableNumberId = new ArrayList<>();
        tableNumberId.add("1");

        List<String> seatNumberId = new ArrayList<>();
        seatNumberId.add("1A-15");

        TicketingOrderDto ticketingOrderDto = new TicketingOrderDto();
        ticketingOrderDto.setTableNumber(tableNumberId);
        ticketingOrderDto.setSeatNumbers(seatNumberId);

        List<TicketingOrderDto> ticketingOrderDtoList = new ArrayList<>();
        ticketingOrderDtoList.add(ticketingOrderDto);

        ticketing.setChartKey("");

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));


        //execute
        List<String> bookedSeats = ticketingPurchaseServiceImpl.getBookedSeats(event, ticketingOrderDtoList);

        assertTrue(bookedSeats.contains(ticketingOrderDto.getTableNumber().get(0)));
    }

    @Test
    void test_updatePurchaserNamesToUser_success()  {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtos();

        valueDto.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto.setPurchaser(valueDto);

        //execute
        ticketingPurchaseServiceImpl.updatePurchaserNamesToUser(ticketAttributeValueDto, user);
    }

    @Test
    void test_updatePurchaserNamesToUser_success__with_attributeKeyValueDtos_firstName_lastName_empty()  {

        //setup
        user.setFirstName("");
        user.setLastName("");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(STRING_FIRST_SPACE_NAME);
        keyValueDto.setValue("");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(STRING_LAST_SPACE_NAME);
        keyValueDto.setValue("");
        attributeKeyValueDtos.add(keyValueDto);

        valueDto.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto.setPurchaser(valueDto);

        //execute
        ticketingPurchaseServiceImpl.updatePurchaserNamesToUser(ticketAttributeValueDto, user);
    }

    @Test
    void test_updatePurchaserNamesToUser_success_with_user_firstName_lastName_empty()  {

        //setup
        user.setFirstName("");
        user.setLastName("");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtos();

        valueDto.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto.setPurchaser(valueDto);

        //execute
        ticketingPurchaseServiceImpl.updatePurchaserNamesToUser(ticketAttributeValueDto, user);

        ArgumentCaptor<User> userArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService, times(1)).save(userArgumentCaptor.capture());

        User userData = userArgumentCaptor.getValue();
        assertEquals(userData.getFirstName(), attributeKeyValueDtos.listIterator(0).next().getValue());
        assertEquals(userData.getLastName(), attributeKeyValueDtos.listIterator(1).next().getValue());
    }

    @Test
    void test_setPurchaseDataToTicketHolder_success()  {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtos();

        valueDto.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto.setHolder(null);

        eventTickets.setHolderLastName("");
        eventTickets.setHolderFirstName("");

        //execute
        ticketingPurchaseServiceImpl.setPurchaseDataToTicketHolder(eventTickets, ticketAttributeValueDto, user);
    }

    @Test
    void test_setPurchaseDataToTicketHolder_success1()  {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtos();

        valueDto.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto.setHolder(null);

        eventTickets.setHolderLastName("kaz");
        eventTickets.setHolderFirstName("jon");

        //execute
        ticketingPurchaseServiceImpl.setPurchaseDataToTicketHolder(eventTickets, ticketAttributeValueDto, user);
    }

    @Test
    void test_setPurchaseDataToTicketHolder_success2()  {

        //setup
        user.setFirstName("");
        user.setLastName("");

        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtos();

        valueDto.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto.setHolder(null);

        eventTickets.setHolderLastName("");
        eventTickets.setHolderFirstName("");

        //execute
        ticketingPurchaseServiceImpl.setPurchaseDataToTicketHolder(eventTickets, ticketAttributeValueDto, user);
    }

    @Test
    void test_handleJSONValue_throwError()  {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtos();

        valueDto.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto.setHolder(valueDto);

        //mock
        when(ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto)).thenThrow(new RuntimeException());

        //execute
        ticketingPurchaseServiceImpl.handleJSONValue(ticketHolderAttributes, ticketAttributeValueDto);
    }

    /*@Test
    void test_saveAndGetEventTickets_success() throws Exception {

        //setup
        ticketingType.setBundleType(TicketBundleType.TABLE);
        ticketingType.setNumberOfTicketPerTable(1);
        ticketingOrderManager1.setSeats("A-1, A-2");
        ticketingOrderManager1.setSelectSeatsDisplay("A-1-15, A-1-16");
        ticketingOrderManager1.setSelectTable("");
        ticketingOrderManager1.setTicketType(ticketingType);
        ticketingOrderManager1.setRecurringEventId(0L);

        int totalTickets = 10;
        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager1);

        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

        //mock
        mockAllOrderById(ticketingOrder, ticketingOrderManagerList);
        mockStripeFeeByEvent();
        when(ticketingStatisticsService.getTotalTicketsForDiscount(any(), any())).thenReturn(totalTickets);
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any(), anyBoolean(), any(), any(), anyInt(), anyInt(), any(), any())).thenReturn(ticketPriceDetailsList);
        when(ticketingTableService.findOneByEventIdDesc(event)).thenReturn(null);
        Mockito.doReturn(eventTickets).when(ticketingPurchaseServiceImpl).saveEventTickets(any(), any(), any(), any(), any(), any(), any(), any(), anyString(), anyString(), 0L);
        when(cacheStoreService.get(anyLong())).thenReturn(null);

        //Execution
        List<EventTickets> eventTicketData = ticketingPurchaseServiceImpl.saveAndGetEventTickets(ticketingOrder, ticketBookingDto, true, user, event, staffUser);

        assertEquals(eventTicketData.get(0).getId(), eventTickets.getId());

        ArgumentCaptor<TicketingTable> ticketingTableArgumentCaptor = ArgumentCaptor.forClass(TicketingTable.class);
        verify(ticketingTableService, times(1)).save(ticketingTableArgumentCaptor.capture());

        TicketingTable userData = ticketingTableArgumentCaptor.getValue();
        assertEquals(userData.getEventId().getEventId(), event.getEventId());
    }*/

    @Test
    void test_purchaseTicketsWithCHECK_OUT_TIME_EXPIRE() throws StripeException, JAXBException, ApiException {
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setExpirein(LocalDateTime.now().minusMinutes(10).toDate());
        ticketingOrder.setEventid(event);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));

        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingPurchaseServiceImpl.purchaseTicket(event, null, id, user, "", null, "", "", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l));
        
        assertEquals(NotAcceptableException.TicketingExceptionMsg.CHECK_OUT_TIME_EXPIRE.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_purchaseTicketsWithWaitingList() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);
        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);

        ticketBookingDto.setPurchaser(purchaser);
        ticketingOrderManager.setRecurringEventId(0L);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        WaitList waitList = new WaitList();
        waitList.setEventId(event.getEventId());
        waitList.setStatus(WaitListStatus.WAITING);
        waitList.setEmail("<EMAIL>");

        List<WaitList> waitListList = new ArrayList<>();
        waitListList.add(waitList);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        mockCreateTicketPurchaserPayment(card);
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockSendmailList();

        when(waitListSettingService.getWaitListSettingsByEvent(event,0L)).thenReturn(waitListSettingsDto);
        when(waitListService.findByidInAndEventAndTicketTypeIdAndStatus(anyList(), any(), anyLong(), any())).thenReturn(waitListList);
        mockEventDesignDetailByEvent();
        doNothing().when(waitListService).updateWaitListStatus(anyList(), any());
        mockTicketPriceDetails();
        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "fX23QMIx_M2RSpDbBQwv2Q", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());

        assertNotNull(ticketsArgumentCaptor.getValue());

    }

    @Test
    void test_purchaseTicketsWithMultipleWaitingList() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");
        List<HolderBookingDto> holder = new ArrayList<>();
        HolderBookingDto holderBookingDto = new HolderBookingDto();
        holderBookingDto.setAttributes(attributeKeyValueDtos);
        holderBookingDto.setTickettypeid(ticketingType.getId());
        holder.add(holderBookingDto);
        ticketBookingDto.setHolder(holder);
        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);
        ticketingOrderManager.setRecurringEventId(0L);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        WaitList waitList = new WaitList();
        waitList.setEventId(event.getEventId());
        waitList.setStatus(WaitListStatus.WAITING);
        waitList.setEmail("<EMAIL>");

        List<WaitList> waitListList = new ArrayList<>();
        waitListList.add(waitList);

        waitList = new WaitList();
        waitList.setEventId(event.getEventId());
        waitList.setStatus(WaitListStatus.WAITING);
        waitList.setEmail("<EMAIL>");
        waitListList.add(waitList);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        mockCreateTicketPurchaserPayment(card);
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockSendmailList();
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));


        when(waitListSettingService.getWaitListSettingsByEvent(event,0L)).thenReturn(waitListSettingsDto);
        when(waitListService.findByidInAndEventAndTicketTypeIdAndStatus(anyList(), any(), anyLong(), any())).thenReturn(waitListList);
        mockEventDesignDetailByEvent();
        doNothing().when(waitListService).updateWaitListStatus(anyList(), any());
        mockTicketPriceDetails();
        doNothing().when(ticketingPurchaseServiceImpl).validateAlreadyPurchaseTicket(any(), any(), any(), any());
        doNothing().when(formRuleEngineService).validateFormRulesOfHolderRegistration(event, ticketBookingDto.getHolder(), 0L);
        doNothing().when(formRuleEngineService).validateFormRulesOfBuyerRegistration(event, ticketBookingDto.getPurchaser().getAttributes(), 0L);

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "", null, "", "OINEUDpGYGDye5XdKLxmmQ", false, false, false, CheckoutFrom.ATTENDEE_CHECKOUT, false, false, 0l);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventCommonRepoService, Mockito.times(1)).save(ticketsArgumentCaptor.capture());

        assertNotNull(ticketsArgumentCaptor.getValue());

    }

    /*@Test
    void test_purchaseTicketsWithDonation() throws StripeException, JAXBException, ApiException, DocumentException, TemplateException, IOException {
        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = getAttributeKeyValueDtosList("Email", "<EMAIL>");

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        ticketBookingDto.setPurchaser(purchaser);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();

        ticketingType.setPassfeetobuyer(true);

        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName("DONATION");
        ticketingType.setTicketType(TicketType.DONATION);
        ticketingType.setPrice(5);
        ticketingType.setEndDate(ticketing.getEventEndDate());
        ticketingType.setStartDate(new Date());
        ticketingType.setNumberOfTickets(100);
        ticketingType.setMaxTicketsPerBuyer(10);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        ticketingOrderManager = new TicketingOrderManager();
        ticketingOrderManager.setNumberofticket(1);
        ticketingOrderManager.setSeats("");
        ticketingOrderManager.setSelectSeatsDisplay("");
        ticketingOrderManager.setTicketType(ticketingType);
        ticketingOrderManager.setSelectTable("");
        ticketingOrderManager.setRecurringEventId(0L);
        ticketingOrderManagerList.add(ticketingOrderManager);

        WaitList waitList = new WaitList();
        waitList.setEventId(event.getEventId());
        waitList.setStatus(WaitListStatus.WAITING);
        waitList.setEmail("<EMAIL>");

        List<WaitList> waitListList = new ArrayList<>();
        waitListList.add(waitList);

        //mock
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));

        mockCreateTicketPurchaserPayment(card);

        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);

        mockSendTicketingPurchaserEmail(doNothing().when(sendGridMailPrepareService));

        mockSendmailList();
        when(cacheStoreService.get(anyLong())).thenReturn(null);

        when(waitListSettingService.getWaitListSettingsByEvent(event,0L)).thenReturn(waitListSettingsDto);
        when(waitListService.findByidInAndEventAndTicketTypeIdAndStatus(anyList(), any(), anyLong(), any())).thenReturn(waitListList);
        mockEventDesignDetailByEvent();
        doNothing().when(waitListService).updateWaitListStatus(anyList(), any());
        mockTicketPriceDetails();

        //execute
        ticketingPurchaseServiceImpl.purchaseTicket(event, ticketBookingDto, id, user, "token", "", null, "", "fX23QMIx_M2RSpDbBQwv2Q", false);

        ArgumentCaptor<EventTickets> ticketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepoService, Mockito.times(2)).save(ticketsArgumentCaptor.capture());
        EventTickets eventTickets = ticketsArgumentCaptor.getValue();

        assertNotNull(eventTickets);
        assertEquals(eventTickets.getTicketingTypeId().getTicketType(), TicketType.DONATION);
    }*/

    @Test
    void test_formAttributes(){

        //setup
        user.setUserId(id);

        ticketing.setCollectTicketHolderAttributes(false);
        ticketing.setCustomDisclaimer("");

        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);


        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
        mockTicketPriceDetails();

        //execute
        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, false, 0L, false,"0", false);
        assertNotNull(ticketingCheckOutDto);

    }

    @Test
    void test_formAttributesWithTicketingCoupon(){

        //setup
        user.setUserId(id);

        TicketingCoupon ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setName("TestDC");
        ticketingCoupon.setAmount(10);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);

        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
        ticketingCoupons.add(ticketingCoupon);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        ticketing.setCollectTicketHolderAttributes(false);
        ticketing.setCustomDisclaimer("");

        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);


        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
        mockTicketPriceDetails();

        //execute
        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, false, 0L, false,"0", false);
        assertNotNull(ticketingCheckOutDto);
        assertFalse(ticketingCheckOutDto.isDiscountCoupon());
    }

    @Test
    void test_formAttributesWithOnlyDonationTicket(){

        //setup
        user.setUserId(0L);

        ticketing.setCollectTicketHolderAttributes(false);
        ticketing.setCustomDisclaimer("");

        ticketingType.setTicketTypeName("DONATION");
        ticketingType.setTicketType(TicketType.DONATION);
        ticketingType.setPrice(5);
        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);


        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
        mockTicketPriceDetails();

        //execute
        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, false, 0L, false,"0", false);
        assertNotNull(ticketingCheckOutDto);
        assertTrue(ticketingCheckOutDto.getTicketAttribute().isOnlyDonationTicket());
    }

    @Test
    void test_formAttributesWithHolderData(){

        //setup
        user.setUserId(id);
        user.setPhoneNumber(8525252525L);

        ticketing.setCollectTicketHolderAttributes(true);
        ticketing.setCustomDisclaimer("");

        ticketingType.setId(1001);
        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = getDefaultTicketHolderRequireAttributes("1001", false);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
        mockTicketPriceDetails();

        //execute
        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, false, 0L, false,"0", false);
        assertNotNull(ticketingCheckOutDto);
        assertTrue(ticketingCheckOutDto.getTicketAttribute().isHasHolderAttributes());
    }

    @Test
    void test_formAttributesWithHolderDataAndQuestion(){

        //setup
        user.setUserId(id);
        user.setPhoneNumber(8525252525L);

        ticketing.setCollectTicketHolderAttributes(true);
        ticketing.setCustomDisclaimer("");

        ticketingType.setId(1001);
        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = getDefaultTicketHolderRequireAttributes("1001", true);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
        mockTicketPriceDetails();

        //execute
        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, false, 0L, false,"0", false);
        assertNotNull(ticketingCheckOutDto);
        assertTrue(ticketingCheckOutDto.getTicketAttribute().isHasHolderAttributes());
        assertNotNull(ticketingCheckOutDto.getTicketAttribute().getBuyerQuestions());
        assertNotNull(ticketingCheckOutDto.getTicketAttribute().getAttendees());

    }

    @Test
    void test_formAttributesWithHolderDataWithReccuring(){

        //setup
        user.setUserId(id);

        ticketing.setCollectTicketHolderAttributes(true);
        ticketing.setCustomDisclaimer("");

        ticketingType.setId(1001);
        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = getDefaultTicketHolderRequireAttributes("1001", false);
        Set<Long> createFromTicketTypeIds = new HashSet<>();
        createFromTicketTypeIds.add(id);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
        mockTicketPriceDetails();

        //execute
        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(null, "TestEvent", id, false, 0L, false,"0", false);

        assertEquals(ticketingCheckOutDto.getTicketAttribute().getOrderId(), id);
        assertFalse(ticketingCheckOutDto.isDiscountCoupon());
        assertTrue(ticketingCheckOutDto.isProcessingFeesToPurchaser());
        assertNull(ticketingCheckOutDto.getPurchaserDetail());
        assertEquals(ticketingCheckOutDto.getCustomDisclaimer(), STRING_EMPTY);
    }

    //TODO: Fix after mockito version upgrade
//    @Test
//    void test_formAttributesWithHolderDataAndTicketTypeTable(){
//
//        //setup
//        user.setUserId(id);
//        user.setPhoneNumber(8525252525L);
//
//        ticketing.setCollectTicketHolderAttributes(true);
//        ticketing.setCustomDisclaimer("");
//
//        ticketingType.setId(1001);
//        ticketingType.setPrice(0);
//        ticketingType.setNumberOfTicketPerTable(2);
//        ticketingType.setBundleType(TicketBundleType.TABLE);
//        ticketingType.setTicketing(ticketing);
//        ticketingType.setDataType(DataType.TICKET);
//
//        ticketingOrderManager.setTicketType(ticketingType);
//
//        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
//        ticketingOrderManagerList.add(ticketingOrderManager);
//
//        Stripe stripe = new Stripe();
//        stripe.setProcessingFeesToPurchaser(true);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = getDefaultTicketHolderRequireAttributes("1001", false);
//
//        //mock
//        when(roEventService.getEventByURL(anyString())).thenReturn(event);
//        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
//        when(roStripeService.findByEvent(event)).thenReturn(stripe);
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
//        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
//        mockAllAttributesWithNonRecurring(ticketHolderRequiredAttributes);
//
//        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(Collections.EMPTY_LIST);
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
//        mockTicketPriceDetails();
//
//        //execute
//        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, false, 0L, false);
//        assertNotNull(ticketingCheckOutDto);
//        assertNotNull(ticketingCheckOutDto.getTicketAttribute().getAttendees().get(0).getTableId());
//        assertEquals(ticketingCheckOutDto.getTicketAttribute().getOrderId(), id);
//        assertFalse(ticketingCheckOutDto.isDiscountCoupon());
//        assertTrue(ticketingCheckOutDto.isProcessingFeesToPurchaser());
//        assertNull(ticketingCheckOutDto.getPurchaserDetail());
//        assertEquals(ticketingCheckOutDto.getCustomDisclaimer(), STRING_EMPTY);
//    }

    //TODO: Fix after mockito version upgrade

//    @Test
//    void test_formAttributesWithHolderDataAndNewUser(){
//
//        //setup
//        user.setUserId(id);
//        user.setFirstName("");
//        user.setLastName("");
//
//        ticketing.setCollectTicketHolderAttributes(true);
//        ticketing.setCustomDisclaimer("");
//
//        ticketingType.setId(1001);
//        ticketingType.setTicketing(ticketing);
//        ticketingType.setPassfeetobuyer(true);
//
//        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
//        ticketingOrderManagerList.add(ticketingOrderManager);
//
//        Stripe stripe = new Stripe();
//        stripe.setProcessingFeesToPurchaser(true);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = getDefaultTicketHolderRequireAttributes("", false);
//
//        //mock
//        when(roEventService.getEventByURL(anyString())).thenReturn(event);
//        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
//        when(roStripeService.findByEvent(event)).thenReturn(stripe);
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
//        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
//        mockAllAttributesWithNonRecurring(ticketHolderRequiredAttributes);
//        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(Collections.EMPTY_LIST);
//        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
//        mockTicketPriceDetails();
//
//        //execute
//        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, false, 0L, false);
//        assertNotNull(ticketingCheckOutDto);
//        assertNull(ticketingCheckOutDto.getTicketAttribute().getBuyerInformationFields().getAttributes().get(0).getValue());
//        assertEquals(ticketingCheckOutDto.getTicketAttribute().getOrderId(), id);
//        assertFalse(ticketingCheckOutDto.isDiscountCoupon());
//        assertTrue(ticketingCheckOutDto.isProcessingFeesToPurchaser());
//        assertNull(ticketingCheckOutDto.getPurchaserDetail());
//        assertEquals(ticketingCheckOutDto.getCustomDisclaimer(), STRING_EMPTY);
//    }

    @Test
    void test_formAttributesWithHolderDataAndStaffUser(){

        //setup
        user = new User();
        user.setUserId(id);
        user.setFirstName("");
        user.setLastName("");
        user.setEmail("");

        ticketing.setCollectTicketHolderAttributes(true);
        ticketing.setCustomDisclaimer("");

        ticketingType.setId(1001);
        ticketingType.setTicketing(ticketing);
        ticketingType.setPassfeetobuyer(true);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes = getDefaultTicketHolderRequireAttributes("", false);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        mockTicketingOrderOngoingStubbing(ticketingOrder1, ticketingOrderService.checkOrderIsPaidOrNot(any(), anyLong()));
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event));
        mockAllOrderById(ticketingOrder1, ticketingOrderManagerList);
        mockTicketingOngoingStubbing(ticketingHelperService.findTicketingByEvent(event));
        mockTicketPriceDetails();

        //execute
        TicketingCheckOutDto ticketingCheckOutDto = ticketingPurchaseServiceImpl.formAttributes(user, "TestEvent", id, true, 0L, false,"0", false);
        assertNotNull(ticketingCheckOutDto);
        assertEquals(ticketingCheckOutDto.getTicketAttribute().getOrderId(), id);
        assertFalse(ticketingCheckOutDto.isDiscountCoupon());
        assertTrue(ticketingCheckOutDto.isProcessingFeesToPurchaser());
        assertNull(ticketingCheckOutDto.getPurchaserDetail());
        assertEquals(ticketingCheckOutDto.getCustomDisclaimer(), STRING_EMPTY);
    }

    @Test
    void test_paymentFromStaffCheckInWithCash() throws TemplateException, JAXBException, DocumentException, IOException, StripeException, ApiException {

        //setup
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setExpirein(LocalDateTime.now().plusMinutes(10).toDate());
        ticketingOrder.setEventid(event);

        PaymentDetailsForStaffDto detailsForStaffDto = new PaymentDetailsForStaffDto();
        detailsForStaffDto.setNote("Test Note");
        detailsForStaffDto.setOrderId(ticketingOrder.getId());
        detailsForStaffDto.setPaymentType("CASH");
        detailsForStaffDto.setTokenOrIntentId("");

        //mock
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.findByid(anyLong()));
        doNothing().when(commonEventService).updateOrderStatus(anyLong(), any(), anyString());

        //execution
        ticketingPurchaseServiceImpl.paymentFromStaffCheckIn(event, user, detailsForStaffDto);

        verify(commonEventService).updateOrderStatus(anyLong(), any(), anyString());

        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder resTicketingOrder = orderArgumentCaptor.getValue();
        assertNotNull(orderArgumentCaptor.getValue());
        assertNotNull(resTicketingOrder.getStaffUserId());
    }

    @Test
    void test_paymentFromStaffCheckInWithCard() throws TemplateException, JAXBException, DocumentException, IOException, StripeException, ApiException {
        //setup
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setExpirein(LocalDateTime.now().plusMinutes(10).toDate());
        ticketingOrder.setEventid(event);

        PaymentDetailsForStaffDto detailsForStaffDto = new PaymentDetailsForStaffDto();
        detailsForStaffDto.setNote("Test Note");
        detailsForStaffDto.setOrderId(ticketingOrder.getId());
        detailsForStaffDto.setPaymentType("CC");
        detailsForStaffDto.setTokenOrIntentId("cnon:CBASENNUEfPB1VjoMtzRUtSZzXAgAQ");

        EventTickets eventTickets = TestTicketDataUtil.getEventTickets();
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setEventId(event.getEventId());
        eventTickets.setTicketingTypeId(ticketingType);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.findByid(anyLong()));
        when(eventCommonRepoService.findByOrder(anyLong())).thenReturn(eventTicketsList);
        doNothing().when(commonEventService).updateOrderStatus(anyLong(), any(), anyString());

        doReturn(new CardInfoDto()).when(paymentHandlerService).createTicketPurchasePayment(any(), any(), anyDouble(), anyDouble(),
        anyBoolean(), anyString(), anyString(), anyLong(), any(), any(), anyDouble(), anyDouble(), any(), anyLong(), anyBoolean());

        //execution
        ticketingPurchaseServiceImpl.paymentFromStaffCheckIn(event, user, detailsForStaffDto);

        verify(commonEventService).updateOrderStatus(anyLong(), any(), anyString());
        verify(paymentHandlerService).createTicketPurchasePayment(any(), any(), anyDouble(), anyDouble(),
                anyBoolean(), anyString(), anyString(), anyLong(), any(), any(), anyDouble(), anyDouble(), any(), anyLong(), anyBoolean());

        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder resTicketingOrder = orderArgumentCaptor.getValue();
        assertNotNull(orderArgumentCaptor.getValue());
        assertNotNull(resTicketingOrder.getStaffUserId());
    }

    @Test
    void test_paymentFromStaffCheckInWithCard_numberOfTicketTypeExcludingFreeTypeGraterThenZero() throws TemplateException, JAXBException, DocumentException, IOException, StripeException, ApiException {
        //setup
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setExpirein(LocalDateTime.now().plusMinutes(10).toDate());
        ticketingOrder.setEventid(event);
        ticketingType.setPassfeetobuyer(true);

        PaymentDetailsForStaffDto detailsForStaffDto = new PaymentDetailsForStaffDto();
        detailsForStaffDto.setNote("Test Note");
        detailsForStaffDto.setOrderId(ticketingOrder.getId());
        detailsForStaffDto.setPaymentType("CC");
        detailsForStaffDto.setTokenOrIntentId("cnon:CBASENNUEfPB1VjoMtzRUtSZzXAgAQ");

        EventTickets eventTickets = TestTicketDataUtil.getEventTickets();
        eventTickets.setId(1L);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setEventId(event.getEventId());
        eventTickets.setTicketingTypeId(ticketingType);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        ticketingOrderManager.setTicketType(ticketingType);
        ticketingOrderManager.setOrderId(ticketingOrder);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManagers.add(ticketingOrderManager);
        //mock
        mockStripeFeeByEvent();
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.findByid(anyLong()));
        when(eventCommonRepoService.findByOrder(anyLong())).thenReturn(eventTicketsList);
        when(ticketingOrderManagerService.getTicketingOrderManagerByListOfOrderIds(anyList())).thenReturn(ticketingOrderManagers);
        doNothing().when(commonEventService).updateOrderStatus(anyLong(), any(), anyString());

        doReturn(new CardInfoDto()).when(paymentHandlerService).createTicketPurchasePayment(any(), any(), anyDouble(), anyDouble(),
                anyBoolean(), anyString(), anyString(), anyLong(), any(), any(), anyDouble(), anyDouble(), any(), anyLong(), anyBoolean());
        doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(event.getEventId());
        //execution
        ticketingPurchaseServiceImpl.paymentFromStaffCheckIn(event, user, detailsForStaffDto);

        verify(commonEventService).updateOrderStatus(anyLong(), any(), anyString());
        verify(paymentHandlerService).createTicketPurchasePayment(any(), any(), anyDouble(), anyDouble(),
                anyBoolean(), anyString(), anyString(), anyLong(), any(), any(), anyDouble(), anyDouble(), any(), anyLong(), anyBoolean());

        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(orderArgumentCaptor.capture());
        verify(ticketingOrderManagerService).getTicketingOrderManagerByListOfOrderIds(anyList());
        TicketingOrder resTicketingOrder = orderArgumentCaptor.getValue();
        assertNotNull(orderArgumentCaptor.getValue());
        assertNotNull(resTicketingOrder.getStaffUserId());
    }

    @Test
    void test_paymentFromStaffCheckInWithComplimentary() throws TemplateException, JAXBException, DocumentException, IOException, StripeException, ApiException {
        //setup
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setExpirein(LocalDateTime.now().plusMinutes(10).toDate());
        ticketingOrder.setEventid(event);

        PaymentDetailsForStaffDto detailsForStaffDto = new PaymentDetailsForStaffDto();
        detailsForStaffDto.setNote("Test Note");
        detailsForStaffDto.setOrderId(ticketingOrder.getId());
        detailsForStaffDto.setPaymentType("COMPLIMENTARY");
        detailsForStaffDto.setTokenOrIntentId("cnon:CBASENNUEfPB1VjoMtzRUtSZzXAgAQ");

        EventTickets eventTickets = TestTicketDataUtil.getEventTickets();
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.findByid(anyLong()));
        when(eventCommonRepoService.findByOrder(anyLong())).thenReturn(eventTicketsList);
        doNothing().when(commonEventService).updateOrderStatus(anyLong(), any(), anyString());

        //execution
        ticketingPurchaseServiceImpl.paymentFromStaffCheckIn(event, user, detailsForStaffDto);

        verify(commonEventService).updateOrderStatus(anyLong(), any(), anyString());

        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder resTicketingOrder = orderArgumentCaptor.getValue();
        assertNotNull(orderArgumentCaptor.getValue());
        assertNotNull(resTicketingOrder.getStaffUserId());
        assertEquals(ticketingOrder.getOrderType(),TicketingOrder.OrderType.COMPLIMENTARY);
    }

    @Test
    void test_paymentFromStaffCheckInWithThrowNOT_UNPAID_ORDER() throws StripeException, ApiException {
        //setup
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);

        PaymentDetailsForStaffDto detailsForStaffDto = new PaymentDetailsForStaffDto();
        detailsForStaffDto.setNote("Test Note");
        detailsForStaffDto.setOrderId(ticketingOrder.getId());

        //mock
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.findByid(anyLong()));

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingPurchaseServiceImpl.paymentFromStaffCheckIn(event, user, detailsForStaffDto));
        
        assertEquals(NotAcceptableException.TicketingExceptionMsg.NOT_UNPAID_ORDER.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_paymentFromStaffCheckInWithThrowALREADY_PAID_ORDER() throws StripeException, ApiException {
        //setup
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);

        PaymentDetailsForStaffDto detailsForStaffDto = new PaymentDetailsForStaffDto();
        detailsForStaffDto.setNote("Test Note");
        detailsForStaffDto.setOrderId(ticketingOrder.getId());

        //mock
        mockTicketingOrderOngoingStubbing(ticketingOrder, ticketingOrderService.findByid(anyLong()));

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingPurchaseServiceImpl.paymentFromStaffCheckIn(event, user, detailsForStaffDto));
        
        assertEquals(NotAcceptableException.TicketingExceptionMsg.ALREADY_PAID_ORDER.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_checkRecurringIdsArePresentNotDeletedAndNotCancel_success() {
        //setup
        boolean isRecurringEvent = true;
        long countOfPresentRecIds = id;

        List<TicketingOrderManager> orderManagers = new ArrayList<>();
        orderManagers.add(ticketingOrderManager1);

        //mock


        //execution
        ticketingPurchaseServiceImpl.checkRecurringIdsArePresentNotDeletedAndNotCancelAndEventCapacity(orderManagers, ticketing);
    }

    @Test
    void test_checkRecurringIdsArePresentNotDeletedAndNotCancel_success_with_recurringEventIdsToCheckRecIdsPresentInDb_empty() {
        //setup
        boolean isRecurringEvent = true;
        long countOfPresentRecIds = id;

        Set<Long> recurringEventIdsToCheckRecIdsPresentInDb = new HashSet<>();

        List<TicketingOrderManager> orderManagers = new ArrayList<>();
        orderManagers.add(ticketingOrderManager1);

        //mock



        //execution
        ticketingPurchaseServiceImpl.checkRecurringIdsArePresentNotDeletedAndNotCancelAndEventCapacity(orderManagers, ticketing);
    }


    @Test
    void test_checkRecurringIdsArePresentNotDeletedAndNotCancel_throwException_RECURRING_EVENT_MODIFIED_BY_HOST() {
        //setup
        long countOfPresentRecIds = 2L;
        ticketing.setRecurringEvent(true);

        List<TicketingOrderManager> orderManagers = new ArrayList<>();
        orderManagers.add(ticketingOrderManager1);

        //mock
        when(recurringEventsMainScheduleService.getCountOfPresentRecurringIds(any())).thenReturn(countOfPresentRecIds);

        //execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingPurchaseServiceImpl.checkRecurringIdsArePresentNotDeletedAndNotCancelAndEventCapacity(orderManagers, ticketing));
        assertEquals(NotFoundException.EventNotFound.RECURRING_EVENT_MODIFIED_BY_HOST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getNumberOfTickets_success_with_recurringEventIdsToCheckRecIdsPresentInDb_empty() {
        //setup
        Long ticketTypeId = id;
        List<String> seatNumberList = new ArrayList<>();

        Set<Long> recurringEventIdsToCheckRecIdsPresentInDb = new HashSet<>();

        ticketingOrderDto = new TicketingOrderDto();
        ticketingOrderDto.setSeatNumbers(seatNumberList);
        ticketingOrderDto.setTicketTypeId(ticketTypeId);

        List<TicketingOrderDto> ticketingOrderDtoList = new ArrayList<>();
        ticketingOrderDtoList.add(ticketingOrderDto);

        ticketingType = new TicketingType();
        ticketingType.setTicketType(TicketType.FREE);

        //mock



        //execution
        int numberOfTickets = ticketingPurchaseServiceImpl.getNumberOfTickets(event, ticketingOrderDtoList);

        assertEquals(numberOfTickets, 0);
    }

    @Test
    void test_handleWaitlist_success_with_waitListIds_empty_and_waitListSettingsDto_null() {
        //setup
        String waitListIds ="";

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        when(waitListSettingService.getWaitListSettingsByEvent(event,0L)).thenReturn(null);

        //execution
        ticketingPurchaseServiceImpl.handleWaitlist(event, eventTicketsList, waitListIds);
    }

    @Test
    void test_handleWaitlist_success_with_waitListIds_empty_and_waitListEnable_false() {
        //setup
        String waitListIds ="";

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        waitListSettingsDto.setWaitListEnabled(false);

        //mock
        when(waitListSettingService.getWaitListSettingsByEvent(event,0L)).thenReturn(waitListSettingsDto);

        //execution
        ticketingPurchaseServiceImpl.handleWaitlist(event, eventTicketsList, waitListIds);
    }

    @Test
    void test_handleWaitlist_success_with_waitListEnable_true() {
        //setup
        String waitListIds ="null";

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        waitList.setEventId(event.getEventId());

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

                //mock
        when(waitListSettingService.getWaitListSettingsByEvent(event,0L)).thenReturn(waitListSettingsDto);
        when(waitListService.findByidInAndEventAndTicketTypeIdAndStatus(anyList(), any(), anyLong(), any())).thenReturn(waitLists);
        doNothing().when(waitListService).updateWaitListStatus(anyList(), any());

        //execution
        ticketingPurchaseServiceImpl.handleWaitlist(event, eventTicketsList, waitListIds);

        verify(waitListService, times(1)).updateWaitListStatus(anyList(), any());
    }

    @Test
    void test_handleWaitlist_success_with_waitLisId() {
        //setup
        String waitListIds ="";

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        waitList.setEventId(event.getEventId());

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

        //mock
        when(waitListSettingService.getWaitListSettingsByEvent(event,0L)).thenReturn(waitListSettingsDto);



        //execution
        ticketingPurchaseServiceImpl.handleWaitlist(event, eventTicketsList, waitListIds);
    }


     @Test
    void test_setEventsTicketsBasedOnAttributeValue_success() {

        //setup
        EventTickets eventTickets = new EventTickets();
        String attributeKey = Constants.STRING_PHONE_NUMBER;
        String attributeValue = "";

        //execution
        ticketingPurchaseServiceImpl.setEventsTicketsBasedOnAttributeValue(eventTickets, attributeKey, attributeValue);
    }

    @Test
    void test_addUserPhone_success() {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("First Name");
        keyValueDto.setValue("Normal");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Last Name");
        keyValueDto.setValue("User");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("email");
        keyValueDto.setValue("<EMAIL>");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(Constants.STRING_CELL_SPACE_PHONE);
        keyValueDto.setValue("989898");
        attributeKeyValueDtos.add(keyValueDto);

        PurchaserBookingDto purchaser = new PurchaserBookingDto();
        purchaser.setAttributes(attributeKeyValueDtos);

        TicketBookingDto ticketBookingDto  = new TicketBookingDto();
        ticketBookingDto.setPurchaser(purchaser);

        PurchaserJsonValidation purchaserJsonValidation =
                new PurchaserJsonValidation(ticketBookingDto, user).invoke();

        //execution
        ticketingPurchaseServiceImpl.addUserPhone(user, attributeKeyValueDtos);
    }

    @Test
    void test_addUserPhone_success1() {

        //setup
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(Constants.STRING_CELL_SPACE_PHONE);
        keyValueDto.setValue("9898989898");
        attributeKeyValueDtos.add(keyValueDto);

        //execution
        ticketingPurchaseServiceImpl.addUserPhone(user, attributeKeyValueDtos);
    }

    private List<TicketHolderRequiredAttributes> getDefaultTicketHolderRequireAttributes(String eventTicketTypeId, Boolean withQuestion){
        List<TicketHolderRequiredAttributes> ticketHolderAttributes = new ArrayList<>();

        TicketHolderRequiredAttributes prefix = new TicketHolderRequiredAttributes();
        prefix.setName("Prefix");
        prefix.setAttributeValueType(AttributeValueType.DROPDOWN);
        prefix.setEventid(event);
        prefix.setBuyerAttributeOrder(1000);
        prefix.setAttribute(true);
        ticketHolderAttributes.add(prefix);

        TicketHolderRequiredAttributes firstName = new TicketHolderRequiredAttributes();
        firstName.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        firstName.setHolderRequiredTicketTypeId(eventTicketTypeId);
        firstName.setName("First Name");
        firstName.setAttributeValueType(AttributeValueType.TEXT);
        firstName.setEnabledForTicketPurchaser(true);
        firstName.setEnabledForTicketHolder(true);
        firstName.setEventid(event);
        firstName.setBuyerAttributeOrder(2000);
        firstName.setAttribute(true);
        ticketHolderAttributes.add(firstName);

        TicketHolderRequiredAttributes lastname = new TicketHolderRequiredAttributes();
        lastname.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        lastname.setHolderRequiredTicketTypeId(eventTicketTypeId);
        lastname.setName("Last Name");
        lastname.setAttributeValueType(AttributeValueType.TEXT);
        lastname.setEnabledForTicketHolder(true);
        lastname.setEnabledForTicketPurchaser(true);
        lastname.setEventid(event);
        lastname.setBuyerAttributeOrder(3000);
        lastname.setAttribute(true);
        ticketHolderAttributes.add(lastname);

        TicketHolderRequiredAttributes emailAddress = new TicketHolderRequiredAttributes();
        emailAddress.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        emailAddress.setHolderRequiredTicketTypeId(eventTicketTypeId);
        emailAddress.setName("Email");
        emailAddress.setAttributeValueType(AttributeValueType.EMAIL);
        emailAddress.setEnabledForTicketHolder(true);
        emailAddress.setEnabledForTicketPurchaser(true);
        emailAddress.setEventid(event);
        emailAddress.setBuyerAttributeOrder(4000);
        emailAddress.setAttribute(true);
        ticketHolderAttributes.add(emailAddress);

        TicketHolderRequiredAttributes cellphone = new TicketHolderRequiredAttributes();
        cellphone.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        cellphone.setHolderRequiredTicketTypeId(eventTicketTypeId);
        cellphone.setName("Cell Phone");
        cellphone.setAttributeValueType(AttributeValueType.NUMBER);
        cellphone.setEnabledForTicketPurchaser(true);
        cellphone.setEventid(event);
        cellphone.setBuyerAttributeOrder(5000);
        cellphone.setAttribute(true);
        ticketHolderAttributes.add(cellphone);

        TicketHolderRequiredAttributes billingaddress = new TicketHolderRequiredAttributes();
        billingaddress.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        billingaddress.setHolderRequiredTicketTypeId(eventTicketTypeId);
        billingaddress.setName(Constants.BILLING_ADDRESS);
        billingaddress.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
        billingaddress.setEnabledForTicketHolder(false);
        billingaddress.setEnabledForTicketPurchaser(false);
        billingaddress.setEventid(event);
        billingaddress.setBuyerAttributeOrder(6000);
        billingaddress.setAttribute(true);
        ticketHolderAttributes.add(billingaddress);

        TicketHolderRequiredAttributes shippingaddress = new TicketHolderRequiredAttributes();
        shippingaddress.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        shippingaddress.setHolderRequiredTicketTypeId(eventTicketTypeId);
        shippingaddress.setName(Constants.SHIPPING_ADDRESS);
        shippingaddress.setAttributeValueType(AttributeValueType.SHIPPING_ADDRESS);
        shippingaddress.setEventid(event);
        shippingaddress.setBuyerAttributeOrder(7000);
        shippingaddress.setAttribute(true);
        ticketHolderAttributes.add(shippingaddress);

        TicketHolderRequiredAttributes gender = new TicketHolderRequiredAttributes();
        gender.setName("Gender");
        gender.setAttributeValueType(AttributeValueType.DROPDOWN);
        gender.setEventid(event);
        gender.setBuyerAttributeOrder(8000);
        gender.setAttribute(true);
        ticketHolderAttributes.add(gender);

        TicketHolderRequiredAttributes birthday = new TicketHolderRequiredAttributes();
        birthday.setName("Birthday");
        birthday.setAttributeValueType(AttributeValueType.DATE);
        birthday.setEventid(event);
        birthday.setBuyerAttributeOrder(9000);
        birthday.setAttribute(true);
        ticketHolderAttributes.add(birthday);

        TicketHolderRequiredAttributes age = new TicketHolderRequiredAttributes();
        age.setName("Age");
        age.setAttributeValueType(AttributeValueType.NUMBER);
        age.setEventid(event);
        age.setBuyerAttributeOrder(10000);
        age.setAttribute(true);
        ticketHolderAttributes.add(age);

        TicketHolderRequiredAttributes organization = new TicketHolderRequiredAttributes();
        organization.setName("Organization");
        organization.setAttributeValueType(AttributeValueType.TEXT);
        organization.setEventid(event);
        organization.setAttribute(true);
        organization.setBuyerAttributeOrder(11000);
        ticketHolderAttributes.add(organization);

        TicketHolderRequiredAttributes jobtitle = new TicketHolderRequiredAttributes();
        jobtitle.setName("Job title");
        jobtitle.setAttributeValueType(AttributeValueType.TEXT);
        jobtitle.setEventid(event);
        jobtitle.setBuyerAttributeOrder(12000);
        jobtitle.setAttribute(true);
        ticketHolderAttributes.add(jobtitle);

        TicketHolderRequiredAttributes image = new TicketHolderRequiredAttributes();
        image.setName("Image");
        image.setAttributeValueType(AttributeValueType.IMAGE);
        image.setEventid(event);
        image.setBuyerAttributeOrder(13000);
        image.setAttribute(true);
        ticketHolderAttributes.add(image);

        if(withQuestion){
            TicketHolderRequiredAttributes question = new TicketHolderRequiredAttributes();
            question.setName("Question");
            question.setAttributeValueType(AttributeValueType.TEXT);
            question.setEventid(event);
            question.setBuyerAttributeOrder(14000);
            question.setAttribute(false);
            question.setEnabledForTicketHolder(true);
            question.setEnabledForTicketPurchaser(true);
            question.setBuyerRequiredTicketTypeId(eventTicketTypeId);
            question.setHolderRequiredTicketTypeId(eventTicketTypeId);
            ticketHolderAttributes.add(question);
        }
        return ticketHolderAttributes;
    }

    private OngoingStubbing<Ticketing> mockTicketingOngoingStubbing(Ticketing ticketingByEventAndIfNotFoundCreateNew) {
        return when(ticketingByEventAndIfNotFoundCreateNew).thenReturn(ticketing);
    }

    private void mockAllOrderById(TicketingOrder ticketingOrder, List<TicketingOrderManager> ticketingOrderManagerList) {
        when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(ticketingOrderManagerList);
    }

    private void mockSendmailList() {
        doNothing().when(afterTaskIntegrationTriggerService).ticketPurchasePostProcess(any(), any(), any(), anyBoolean(), anyBoolean());
    }

    private void mockTicketPriceDetails() {
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any(), anyBoolean(), any(), any(), anyInt(), anyInt(), any(), any(), anyBoolean(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(ticketPriceDetailsList);
    }

    private void mockCreateTicketPurchaserPayment(CardInfoDto card) throws StripeException, ApiException {
        when(paymentHandlerService.createTicketPurchasePayment(any(),any(),anyDouble(),anyDouble(),anyBoolean(),anyString(),anyString(),anyLong(),any(),any(),anyDouble(),anyDouble(),any(), anyLong(), anyBoolean())).thenReturn(card);
    }

    private void mockEventDesignDetailByEvent() {
        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
    }

    private OngoingStubbing<TicketingOrder> mockTicketingOrderOngoingStubbing(TicketingOrder ticketingOrder, TicketingOrder byidAndEventid) {
        return when(byidAndEventid).thenReturn(ticketingOrder);
    }

    private void mockHandleEmail() {
        Mockito.doNothing().when(ticketingPurchaseServiceImpl).handleEmail(any(), anyLong(), any(), any(), any(), anyList(), anyString(), anyString(), eq(null));
    }

    private void mockSaveAndGetEventTickets(List<EventTickets> eventTicketsList) throws JAXBException {
        Mockito.doReturn(eventTicketsList).when(ticketingPurchaseServiceImpl).saveAndGetEventTickets(any(), any(), anyBoolean(), any(), any(), any(), anyBoolean(), any(), anyBoolean());
    }

    private void mockTrackingLink(String trackingUrl, Event event, TrackingLinks trackingLinks) {
        when(trackingLinksService.getTrackingLink(event, trackingUrl)).thenReturn(trackingLinks);
    }

    private void mockTicketOrderForCheckout() {
        Mockito.doReturn(ticketingOrder).when(ticketingPurchaseServiceImpl).getTicketOrderForCheckOut(any(), anyLong());
    }

    /*private void mockAddUserPhone(TicketingPurchaseServiceImpl when, User o, List list) {
        when.addUserPhone(o, list);
    }*/

    private void mockStripeFeeByEvent() {
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
    }
    private void mockAllAttributesWithNonRecurring(List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes) {
        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(any(),anyLong())).thenReturn(ticketHolderRequiredAttributes);
    }

    private List<AttributeKeyValueDto> getAttributeKeyValueDtos() {
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(STRING_FIRST_SPACE_NAME);
        keyValueDto.setValue("Normal");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(STRING_LAST_SPACE_NAME);
        keyValueDto.setValue("User");
        attributeKeyValueDtos.add(keyValueDto);
        return attributeKeyValueDtos;
    }

    private List<AttributeKeyValueDto> getAttributeKeyValueDtosList(String email, String s) {
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("First Name");
        keyValueDto.setValue("Normal");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Last Name");
        keyValueDto.setValue("User");
        attributeKeyValueDtos.add(keyValueDto);
        keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey(email);
        keyValueDto.setValue(s);
        attributeKeyValueDtos.add(keyValueDto);
        return attributeKeyValueDtos;
    }

    @Test
    void  test_createHolderUserAndSetInEventTickets_ExistingUser() throws JAXBException {
        //setup
        Unmarshaller unmarshaller = JAXBContext.newInstance(TicketAttributeValueDto.class).createUnmarshaller();
        EventTickets eventTickets = EventDataUtil.getEventTickets();
        List<EventTickets> eventTicketList=new ArrayList<>();
        eventTicketList.add(eventTickets);

        //Mock
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.of(user));
        //Excution
        ticketingPurchaseServiceImpl.createHolderUserAndSetInEventTickets(eventTicketList, event, CountryCode.US.name());


        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argumentCaptorForEventTicket = ArgumentCaptor.forClass(listClass);
        ArgumentCaptor<Long> eventId = ArgumentCaptor.forClass(Long.class);
        verify(eventTicketsRepoService).saveAll(argumentCaptorForEventTicket.capture());

    }

    @Test
    void test_createHolderUserAndSetInEventTickets_NewUser(){
        //setup
        EventTickets eventTickets = EventDataUtil.getEventTickets();
        List<EventTickets> eventTicketList=new ArrayList<>();
        eventTicketList.add(eventTickets);

        //Mock
        when(roUserService.getUserByEmail(anyString())).thenReturn(Optional.empty());
        //Excution
        ticketingPurchaseServiceImpl.createHolderUserAndSetInEventTickets(eventTicketList, event, CountryCode.US.name());


        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argumentCaptorForEventTicket = ArgumentCaptor.forClass(listClass);
        ArgumentCaptor<Long> eventId = ArgumentCaptor.forClass(Long.class);
        verify(eventTicketsRepoService).saveAll(argumentCaptorForEventTicket.capture());

        Class<ArrayList<User>> listUserClass = (Class<ArrayList<User>>)(Class)ArrayList.class;

        ArgumentCaptor<List<User>> ticketsArgumentForUserCaptor = ArgumentCaptor.forClass(listUserClass);
        verify(userService).saveAll(ticketsArgumentForUserCaptor.capture());

        User userData = ticketsArgumentForUserCaptor.getValue().get(0);
        assertEquals(userData.getFirstName(),eventTicketList.get(0).getHolderFirstName());
        assertEquals(userData.getLastName(),eventTicketList.get(0).getHolderLastName());
        assertEquals(userData.getEmail(),eventTicketList.get(0).getHolderEmail());

    }

    @Test
    void test_bookTicket_throw_NotFoundException_VirtualTicketNotPurchased() {
        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        //mock
        when(ticketingTypeService.userHadPurchasedTicket(anyList(), any())).thenReturn(false);
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(true);

        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "",STRING_EMPTY, STRING_EMPTY,0L,null,null));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.PURCHASE_EVENT_TICKET_REQUIRED_FOR_ADDONS_TICKET.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_bookTicket_throw_NotFoundException_ShowRegistrationButtonDisable() {
        //setup
        List<TicketingOrderDto> orderTicketings = new ArrayList<>();
        orderTicketings.add(ticketingOrderDto);

        Date clientDate = new Date();

        //mock
        when(ticketingService.isShowRegistrationButton(event)).thenReturn(false);

        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingPurchaseServiceImpl.bookTicket(event, orderTicketings, user, clientDate, TicketingOrder.OrderType.CARD, false, "",STRING_EMPTY, STRING_EMPTY,0L,null,null));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.PURCHASE_EVENT_TICKET_REQUIRED_ENABLE_SHOW_REGISTRATION_BUTTON.getDeveloperMessage(), exception.getMessage());
    }

    private void mockMethodsForLowerToHigherTicketTransfer(boolean isCashPayment) {
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId())).thenReturn(salesTaxFeeDto);
        when(vatTaxService.getVatTaxByTicketTypeOrEvent(event.getEventId(),newTicketingType)).thenReturn(0d);
        when(transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(newTicketingType.getTicketTypeFormat(), event)).thenReturn(19.95);
        when(eventTicketTransactionService.calculateDueAmountForGivenTicketPrice(eventTickets, newTicketingType, BigDecimal.valueOf(newTicketingType.getPrice()), eventTickets.getVatTaxFee(), BigDecimal.valueOf(eventTickets.getVatTaxPercentage()))).thenReturn(new TicketTransferDueAmountDetailsDTO(BigDecimal.valueOf(50), BigDecimal.valueOf(0)));
    }

    @Test
    void test_calculateFeeForLowerToHigherTicketTransfer_with_card_payment_with_pass_fee_to_buyer() {

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        //mock
        mockMethodsForLowerToHigherTicketTransfer(false);

        EventTicketFeesDto eventTicketFeesDto = ticketingPurchaseServiceImpl.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, false, eventTickets.getVatTaxFee(), null);

        assertEqualForCalculateFeeLowerToHigherTicketTransfer(eventTicketFeesDto, 1.96, 5.18, 57.14);
    }

    @Test
    void test_calculateFeeForLowerToHigherTicketTransfer_with_card_payment_with_absorb_fee() {

        salesTaxFeeDto.setAbsorbTax(true);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));
        newTicketingType.setPassfeetobuyer(false);
        newTicketingType.setPassFeeVatToBuyer(false);

        //mock
        mockMethodsForLowerToHigherTicketTransfer(false);

        EventTicketFeesDto eventTicketFeesDto = ticketingPurchaseServiceImpl.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, false, eventTickets.getVatTaxFee(), null);

        assertEqualForCalculateFeeLowerToHigherTicketTransfer(eventTicketFeesDto, 1.75, 4.55, 50);
    }

    private static void assertEqualForCalculateFeeLowerToHigherTicketTransfer(EventTicketFeesDto eventTicketFeesDto, double ccFee,  double salesTaxFee, double totalPaidAmount) {
        assertEquals(ccFee, eventTicketFeesDto.getCcFeeAmount());
        assertEquals(salesTaxFee, GeneralUtils.getRoundValue(eventTicketFeesDto.getSalesTaxFee()));
        assertEquals(totalPaidAmount, eventTicketFeesDto.getEventTicketPaidAmount());
    }

    @Test
    void test_calculateFeeForLowerToHigherTicketTransfer_with_cash_payment_with_pass_fee_to_buyer() {

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        //mock
        mockMethodsForLowerToHigherTicketTransfer(true);

        EventTicketFeesDto eventTicketFeesDto = ticketingPurchaseServiceImpl.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, true, eventTickets.getVatTaxFee(), null);

        assertEqualForCalculateFeeLowerToHigherTicketTransfer(eventTicketFeesDto, 0, 5, 55);
    }

    @Test
    void test_calculateFeeForLowerToHigherTicketTransfer_with_cash_payment_with_absorb_fee() {

        salesTaxFeeDto.setAbsorbTax(true);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        newTicketingType.setPassfeetobuyer(false);
        newTicketingType.setPassFeeVatToBuyer(false);

        //mock
        mockMethodsForLowerToHigherTicketTransfer(true);

        EventTicketFeesDto eventTicketFeesDto = ticketingPurchaseServiceImpl.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, true, eventTickets.getVatTaxFee(), null);

        assertEqualForCalculateFeeLowerToHigherTicketTransfer(eventTicketFeesDto, 0, 0, 50);
    }

    @Test
    void calculateFeeForLowerToHigherTicketTransfer_with_card_payment_with_max_cap_amount_fee_paid_already() {

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        ticketingType.setAeFeeFlat(1);
        ticketingType.setAeFeePercentage(50);

        newTicketingType.setAeFeeFlat(1);
        newTicketingType.setAeFeePercentage(10);

        eventTickets.setAeFeeAmount(19.95); // Max capAmount for AE Fee

        //mock
        mockMethodsForLowerToHigherTicketTransfer(false);

        EventTicketFeesDto eventTicketFeesDto = ticketingPurchaseServiceImpl.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, false, eventTickets.getVatTaxFee(), null);

        assertEqualForCalculateFeeLowerToHigherTicketTransfer(eventTicketFeesDto, 1.96, 5.18, 57.14);
    }

    private void assertEqualsForPaymentForLowerToHigherTicketTransfer(String paymentType, double paidAmount, double ccFee, double salesTaxFee) {
        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepoService, Mockito.times(1)).save(eventTicketsArgumentCaptor.capture());
        EventTickets resEventTickets = eventTicketsArgumentCaptor.getValue();

        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder resTicketingOrder = orderArgumentCaptor.getValue();

        ArgumentCaptor<OrderAuditLog> orderAuditLogArgumentCaptor = ArgumentCaptor.forClass(OrderAuditLog.class);
        verify(orderAuditLogRepo, Mockito.times(1)).save(orderAuditLogArgumentCaptor.capture());
        OrderAuditLog orderAuditLog = orderAuditLogArgumentCaptor.getValue();

        assertEquals(paidAmount, resEventTickets.getPaidAmount());
        assertEquals(ccFee, resEventTickets.getCcFeeAmount());
        assertEquals(salesTaxFee, GeneralUtils.getRoundValue(resEventTickets.getSalesTaxFee()));
        assertEquals(TicketPaymentStatus.PAID, resEventTickets.getTicketPaymentStatus());
        assertEquals(PAID, resTicketingOrder.getStatus().name());
        assertEquals(paymentType, ticketingOrder.getOrderType().name());
        assertEquals(String.format(ORDER_AUDIT_SUCCESS_MESSAGE_FORMAT, PAID + STRING_BLANK, ticketingOrder.getId()), orderAuditLog.getMessage());
    }

    @Test
    void test_paymentForLowerToHigherTicketTransfer_with_paid_to_paid_ticket_with_card() throws StripeException, ApiException {

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        eventTickets.setPaidAmount(51.75);
        eventTickets.setCcFeeAmount(1.75);
        eventTickets.setRecurringEventId(0L);

        //mock
        mockMethodsForLowerToHigherTicketTransfer(false);
        doNothing().when(afterTaskIntegrationTriggerService).afterPaymentForUnpaidOrPartialPayment(any(), any(), anyList());
        when(paymentHandlerService.createTicketPurchasePayment(ticketingOrder.getPurchaser(),event, 57.14,0d,true, ticketExchangePaymentDto.getTokenOrIntentId(),null,
                ticketingOrder.getId(),ticketingOrder.getOrderDate(),staffUser, 0d,0d,ticketingOrder.getPurchaser(), 0L, false)).thenReturn(card);


        ticketingPurchaseServiceImpl.paymentForLowerToHigherTicketTransfer(event, staffUser, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto, 0d, null);
        verify(eventTicketTransactionService, Mockito.times(1)).storeEventTicketTransaction(any(), anyList(),any(), anyList(), any(), any(), anyLong());
        assertEqualsForPaymentForLowerToHigherTicketTransfer(CARD,108.89, 1.96, 5.18);
    }

    @Test
    void test_paymentForLowerToHigherTicketTransfer_with_paid_to_paid_ticket_with_card_payment_with_absorb_fee() throws StripeException, ApiException {

        salesTaxFeeDto.setAbsorbTax(true);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        newTicketingType.setPassFeeVatToBuyer(false);
        newTicketingType.setPassfeetobuyer(false);

        eventTickets.setPaidAmount(51.75);
        eventTickets.setCcFeeAmount(1.75);
        eventTickets.setRecurringEventId(0L);

        newTicketingType.setAeFeePercentage(1);
        newTicketingType.setAeFeeFlat(1);

        //mock
        mockMethodsForLowerToHigherTicketTransfer(false);
        doNothing().when(afterTaskIntegrationTriggerService).afterPaymentForUnpaidOrPartialPayment(any(), any(), anyList());
        when(eventTicketTransactionService.isCreditCardPaymentExist(eventTickets.getId())).thenReturn(true);
        when(paymentHandlerService.createTicketPurchasePayment(ticketingOrder.getPurchaser(),event, 50d,0.5d,true, ticketExchangePaymentDto.getTokenOrIntentId(),null,
                ticketingOrder.getId(),ticketingOrder.getOrderDate(),staffUser, 0d,0d,ticketingOrder.getPurchaser(), 0L, false)).thenReturn(card);


        ticketingPurchaseServiceImpl.paymentForLowerToHigherTicketTransfer(event, staffUser, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto, 0d, null);
        verify(eventTicketTransactionService, Mockito.times(1)).storeEventTicketTransaction(any(), anyList(),any(), anyList(), any(), any(), anyLong());
        assertEqualsForPaymentForLowerToHigherTicketTransfer(CARD,101.75, 1.75, 4.55);

    }

    @Test
    void test_paymentForLowerToHigherTicketTransfer_with_free_to_paid_ticket_with_card() throws StripeException, ApiException {

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        eventTickets.setPaidAmount(0);
        eventTickets.setRecurringEventId(0L);

        newTicketingType.setAeFeePercentage(1);
        newTicketingType.setAeFeeFlat(1);

        //mock
        mockMethodsForLowerToHigherTicketTransfer(false);
        doNothing().when(afterTaskIntegrationTriggerService).afterPaymentForUnpaidOrPartialPayment(any(), any(), anyList());
        when(paymentHandlerService.createTicketPurchasePayment(ticketingOrder.getPurchaser(),event, 58.85,1.5d,true, ticketExchangePaymentDto.getTokenOrIntentId(),null,
                ticketingOrder.getId(),ticketingOrder.getOrderDate(),staffUser, 0d,0d,ticketingOrder.getPurchaser(), 0L, false)).thenReturn(card);

        ticketingPurchaseServiceImpl.paymentForLowerToHigherTicketTransfer(event, staffUser, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto, 0d, null);
        verify(eventTicketTransactionService, Mockito.times(1)).storeEventTicketTransaction(any(), anyList(),any(), anyList(), any(), any(), anyLong());
        assertEqualsForPaymentForLowerToHigherTicketTransfer(CARD,58.85, 2.01, 5.33);

    }

    @Test
    void test_paymentForLowerToHigherTicketTransfer_with_paid_to_paid_ticket_with_cash() throws StripeException, ApiException {

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        ticketExchangePaymentDto.setPaymentType(CASH);
        ticketExchangePaymentDto.setNote("Paid By Test Case using Cash");
        ticketExchangePaymentDto.setPaymentDate("2023/09/10");
        ticketExchangePaymentDto.setTransactionId("1234567890");

        //mock
        mockMethodsForLowerToHigherTicketTransfer(true);
        doNothing().when(afterTaskIntegrationTriggerService).afterPaymentForUnpaidOrPartialPayment(any(), any(), anyList());

        ticketingPurchaseServiceImpl.paymentForLowerToHigherTicketTransfer(event, staffUser, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto, 0d, null);
        verify(eventTicketTransactionService, Mockito.times(1)).storeEventTicketTransaction(any(), anyList(),any(), anyList(), any(), any(), anyLong());
        assertEqualsForPaymentForLowerToHigherTicketTransfer(CASH,105.0, 0, 5);
    }

    @Test
    void test_paymentForLowerToHigherTicketTransfer_with_free_to_paid_ticket_with_cash() throws StripeException, ApiException {
        eventTickets.setPaidAmount(0);

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        ticketExchangePaymentDto.setPaymentType(CASH);
        ticketExchangePaymentDto.setNote("Paid By Test Case using Cash");
        ticketExchangePaymentDto.setPaymentDate("2023/09/10");
        ticketExchangePaymentDto.setTransactionId("1234567890");

        // this is get by calculateFeeForLowerToHigherTicketTransfer method
        EventTicketFeesDto eventTicketFeesDto = new EventTicketFeesDto();
        eventTicketFeesDto.setEventTicketId(eventTickets.getId());
        eventTicketFeesDto.setEventTicketPaidAmount(55.0);
        eventTicketFeesDto.setSalesTaxFee(5.0);

        //mock
        mockMethodsForLowerToHigherTicketTransfer(true);
        doNothing().when(afterTaskIntegrationTriggerService).afterPaymentForUnpaidOrPartialPayment(any(), any(), anyList());

        ticketingPurchaseServiceImpl.paymentForLowerToHigherTicketTransfer(event, staffUser, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto, 0d, null);
        verify(eventTicketTransactionService, Mockito.times(1)).storeEventTicketTransaction(any(), anyList(),any(), anyList(), any(), any(), anyLong());
        assertEqualsForPaymentForLowerToHigherTicketTransfer(CASH,55.0, 0, 5);
    }

    @Test
    void test_paymentForLowerToHigherTicketTransfer_with_free_to_paid_ticket_with_card_with_empty_token() {

        salesTaxFeeDto.setAbsorbTax(false);
        salesTaxFeeDto.setTicketingTypeIds(String.valueOf(newTicketingType.getId()));

        eventTickets.setPaidAmount(0);
        eventTickets.setRecurringEventId(0L);
        newTicketingType.setAeFeePercentage(1);
        newTicketingType.setAeFeeFlat(1);
        stripeDTO = new StripeDTO();
        ticketExchangePaymentDto.setTokenOrIntentId(null);

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(eventTicketTransactionService.calculateDueAmountForGivenTicketPrice(eventTickets, newTicketingType, BigDecimal.valueOf(newTicketingType.getPrice()), eventTickets.getVatTaxFee(), BigDecimal.valueOf(eventTickets.getVatTaxPercentage()))).thenReturn(new TicketTransferDueAmountDetailsDTO(BigDecimal.valueOf(50), BigDecimal.valueOf(0)));

        //mock
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingPurchaseServiceImpl.paymentForLowerToHigherTicketTransfer(event, staffUser, ticketingOrder, eventTickets, newTicketingType, ticketExchangePaymentDto, 0d, null));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.PAYMENT_TOKEN_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

    }


 }