package com.accelevents.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.dto.WaitListDto;
import com.accelevents.dto.WaitListSettingsDto;
import com.accelevents.dto.WaitListUpdateDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.EmailImageHelper;
import com.accelevents.messages.WaitListStatus;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.repositories.WaitListRepository;
import com.accelevents.repositories.WaitListSettingRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class WaitListServiceImplTest {

    @Spy
    @InjectMocks
    private WaitListServiceImpl waitListServiceImpl=new WaitListServiceImpl();
    @Mock
    private WaitListRepository waitListRepository;

    @Mock
    private WaitListSettingRepository waitListSettingRepository;

    @Mock
    private WaitListSettingService waitListSettingService;

    @Mock
    private ROEventService roEventService;

    @Mock
    private WaitListSettingsDto waitListSettingsDto;

    @Mock
    private TimeZoneService timeZoneService;

    @Mock
    private TicketingStatisticsService ticketingStatisticsService;

    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Mock
    private SendGridMailService sendGridMailService;

    @Mock
    private WhiteLabelService whiteLabelService;

    @Mock
    private StaffService staffService;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private TicketingTypeService ticketingTypeService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private ImageConfiguration imageConfiguration;
    @Mock
    private EmailImageHelper emailImageHelper;

    private Event event;
    private WaitListDto waitListDto;
    private WaitListUpdateDto waitListUpdateDto;
    private WaitListSettings waitListSettings;
    private WaitList waitList;

    private Long recurringEventId = 1L;

    @BeforeEach
    void setUp(){
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();

        waitListSettingsDto = new WaitListSettingsDto();
        waitListSettingsDto.setMaxWaitListSize(1);
        waitListSettingsDto.setPhoneNumberRequired(false);
        waitListSettingsDto.setWaitListEnabled(true);

        waitList = new WaitList();
        waitList.setEventId(1);
        waitList.setEmail("<EMAIL>");

        waitListDto = new WaitListDto();
        waitListUpdateDto = new WaitListUpdateDto();

        waitListSettings = new WaitListSettings();
        waitListSettings.setWaitListTicketReleaseMessage("Response message");
        waitListSettings.setWaitListTicketReleaseSubject("Release subject");
    }

    @Test
    void testRetrieveWaitListWithEnabledSetting() {
        // Set data
        List<WaitList> waitListData = new ArrayList<>();
        waitListData.add(waitList);

        //Execute method
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);
        doReturn(waitListData).when(waitListRepository).findByEventId(event.getEventId());

        // Assert results
        assertEquals(1, waitListServiceImpl.getWaitListByEvent(event).size());
    }

    @Test
    void testRetrieveWaitListWithDisabledSettingThrowsException() {
        // Set data
        List<WaitList> waitListData = new ArrayList<>();
        waitListData.add(waitList);
        waitListSettingsDto.setWaitListEnabled(false);

        //Execute method
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);



        assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.getWaitListByEvent(event).size());
    }

    @Test
    void testCreateWaitListWithEnabledSetting() {
        // Set data
        List<WaitList> waitListData = new ArrayList<>();
        waitListData.add(waitList);
        waitListSettingsDto.setWaitListEnabled(true);

        //Execute method
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);
        doReturn(waitListData).when(waitListRepository).findByEventId(event.getEventId());
        // Assert results
        assertEquals(1, waitListServiceImpl.getWaitListByEvent(event).size());
    }

    @Test
    void testCreateWaitListWithDisabledSettingThrowsException() {
        // Set data
        waitListSettingsDto.setWaitListEnabled(false);

        //Execute method
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);


        // Assert results
        assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.getWaitListByEvent(event).size());
        verify(waitListServiceImpl).getWaitListByEvent(event);
    }

    @Test
    void test_save_throwExceptionWaitListNotEnable() {

        //setup
        //mock
        doReturn(null).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.save(event,waitListDto,recurringEventId));

        //assert
        assertEquals(NotAcceptableException.WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED.getDeveloperMessage(), exception.getMessage());
        verify(waitListSettingService).getWaitListSettingsByEvent(event,0L);

    }

    public static Object[] getMaxWaitListSizeOrTotalTicketsAvailable(){
        return new Object[]{
                new Object[]{0},
                new Object[]{1},
        };
    }

    @ParameterizedTest
    @MethodSource("getMaxWaitListSizeOrTotalTicketsAvailable")
    void test_save_successNumberOfTicketsZero(int maxWaitListSize) {

        //setup
        waitListSettingsDto.setMaxWaitListSize(maxWaitListSize);
        EventDesignDetail eventDesignDetail= new EventDesignDetail();
        eventDesignDetail.setReplyToEmail("<EMAIL>");

        //mock
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(any(),anyLong());
        doReturn(eventDesignDetail).when(eventDesignDetailService).findByEvent(any());
        doReturn(1L).when(waitListRepository).countAllByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);

        //Execution
        waitListServiceImpl.save(event,waitListDto,recurringEventId);

        //assert
        verify(waitListSettingService,times(2)).getWaitListSettingsByEvent(any(),anyLong());
        verify(waitListRepository).countAllByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);

    }

    @Test
    void test_save_successWithThrowExceptionWaitListLimitExceed() {

        //setup
        waitListDto.setNumberOfTickets(1);

        //mock
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(any(),anyLong());
        doReturn(1L).when(waitListRepository).countAllByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.save(event,waitListDto,recurringEventId));

        //assert
        assertEquals("There are only 0 spots remaining on the waitlist. Please adjust your request accordingly.", exception.getMessage());
        verify(waitListSettingService,times(2)).getWaitListSettingsByEvent(any(),anyLong());
        verify(waitListRepository).countAllByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);

    }

    @Test
    void test_save_success() {

        //setup
        waitListDto.setNumberOfTickets(1);
        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setReplyToEmail("<EMAIL>");

        //mock
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(any(),anyLong());
        doReturn(eventDesignDetail).when(eventDesignDetailService).findByEvent(any());
        doReturn(0L).when(waitListRepository).countAllByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);

        //Execution
        waitListServiceImpl.save(event,waitListDto,recurringEventId);

        //assert
        ArgumentCaptor<WaitList> waitListArgumentCaptor = ArgumentCaptor.forClass(WaitList.class);
        verify(waitListRepository, times(1)).save(waitListArgumentCaptor.capture());
        WaitList waitListData = waitListArgumentCaptor.getValue();

        assertEquals(waitListData.getTicketingTypeId(), waitListSettingsDto.getWaitListTrigger());
        assertEquals(waitListData.getEmail(),waitListDto.getEmail());
        assertEquals(waitListData.getFirstName(),waitListDto.getFirstName());
        assertEquals(waitListData.getLastName(),waitListDto.getLastName());
        assertEquals(waitListData.getCountryCode(),waitListDto.getCountryCode());
        assertEquals(waitListData.getPhoneNumber(),waitListDto.getPhoneNumber());
        assertEquals(WaitListStatus.WAITING,waitListData.getStatus());
        assertEquals(waitListData.getTimeAddedToList().toString(),new Date().toString());
        assertEquals(waitListData.getEventId(),event.getEventId());

        verify(waitListSettingService,times(2)).getWaitListSettingsByEvent(any(),anyLong());
        verify(waitListRepository).countAllByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
    }

    @Test
    void test_update_throwWaitListNotFound() {

        //mock
        when(waitListRepository.findByWaitListIdAndEventId(1L, event.getEventId())).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.update(1L,event,waitListUpdateDto));

        //assert
        assertEquals(NotAcceptableException.WaitListExceptionMsg.WAIT_LIST_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(waitListRepository).findByWaitListIdAndEventId(1L, event.getEventId());
    }

    @Test
    void test_update_throwWaitListNotEnable() {

        //mock
        when(waitListRepository.findByWaitListIdAndEventId(1L, event.getEventId())).thenReturn(waitList);
        doReturn(null).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.update(1L,event,waitListUpdateDto));

        //assert
        assertEquals(NotAcceptableException.WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED.getDeveloperMessage(), exception.getMessage());
        verify(waitListRepository).findByWaitListIdAndEventId(1L, event.getEventId());
        verify(waitListSettingService).getWaitListSettingsByEvent(event,0L);
    }

    @Test
    void test_update_success() {

        //mock
        when(waitListRepository.findByWaitListIdAndEventId(1L, event.getEventId())).thenReturn(waitList);
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);

        //Execution
        waitListServiceImpl.update(1L,event,waitListUpdateDto);

        //assert
        ArgumentCaptor<WaitList> waitListArgumentCaptor = ArgumentCaptor.forClass(WaitList.class);
        verify(waitListRepository, times(1)).save(waitListArgumentCaptor.capture());
        WaitList waitListData = waitListArgumentCaptor.getValue();

        assertEquals(waitListData.getEmail(),waitListUpdateDto.getEmail());
        assertEquals(waitListData.getFirstName(),waitListUpdateDto.getFirstName());
        assertEquals(waitListData.getLastName(),waitListUpdateDto.getLastName());
        assertEquals(waitListData.getCountryCode(),waitListUpdateDto.getCountryCode());
        assertEquals(waitListData.getPhoneNumber(),waitListUpdateDto.getPhoneNumber());
        assertEquals(waitListData.getStatus(),waitListUpdateDto.getStatus());
        assertEquals(waitListData.getEventId(),event.getEventId());
        assertEquals(waitListData.getTimeTicketsBecameAvailable(),waitListUpdateDto.getTimeTicketsBecameAvailable());
        assertEquals(1L,waitListData.getWaitListId());

        verify(waitListRepository).findByWaitListIdAndEventId(1L, event.getEventId());
        verify(waitListSettingService).getWaitListSettingsByEvent(event,0L);
    }

    @Test
    void test_remove_throwWaitListNotEnable() {

        //mock
        doReturn(null).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.remove(1L,event));

        //assert
        assertEquals(NotAcceptableException.WaitListExceptionMsg.WAIT_LIST_NOT_ENABLED.getDeveloperMessage(), exception.getMessage());
        verify(waitListSettingService).getWaitListSettingsByEvent(event,0L);
    }

    @Test
    void test_remove_success() {

        //mock
        doReturn(waitListSettingsDto).when(waitListSettingService).getWaitListSettingsByEvent(event,0L);
        doNothing().when(waitListRepository).deleteById(1L);

        //Execution
        waitListServiceImpl.remove(1L,event);

        //assert
        verify(waitListSettingService).getWaitListSettingsByEvent(event,0L);
        verify(waitListRepository).deleteById(1L);
    }

    @Test
    void test_checkWaitList_throwException() {

        //setup
        List<WaitListSettings> waitListSettingsList = new ArrayList<>();
        waitListSettingsList.add(waitListSettings);

        //mock
        when(waitListSettingService.getAllWaitListSettings()).thenReturn(waitListSettingsList);
        waitList.setEventId(0L);
        when(waitListRepository.findByEventIdInAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(Collections.singletonList(0L), WaitListStatus.WAITING, false))
                .thenReturn(Collections.singletonList(waitList));
        doThrow(new RuntimeException()).when(waitListServiceImpl).countByEventIdAndStatusAndEmailSentAndTicketingTypeId(null,WaitListStatus.WAITING,true,0L);

        //Execution
        waitListServiceImpl.checkWaitList();

        //assert
        verify(waitListSettingService).getAllWaitListSettings();
        verify(waitListServiceImpl).countByEventIdAndStatusAndEmailSentAndTicketingTypeId(null, WaitListStatus.WAITING, true, 0L);
    }

    @Test
    void test_checkWaitList_successWithWaitListEmpty() {

        //setup
        List<WaitListSettings> waitListSettingsList = new ArrayList<>();
        waitListSettingsList.add(waitListSettings);

        //mock
        when(waitListSettingService.getAllWaitListSettings()).thenReturn(waitListSettingsList);

        //Execution
        waitListServiceImpl.checkWaitList();

        //assert
        verify(waitListSettingService).getAllWaitListSettings();
    }

    @Test
    void test_checkWaitList_successMailMapEmpty() {

        //setup
        List<WaitListSettings> waitListSettingsList = new ArrayList<>();
        waitListSettingsList.add(waitListSettings);

        waitList.setTicketingTypeId(1L);
        waitList.setTimeTicketsBecameAvailable(new Date());

        WaitList waitList1 = new WaitList();
        waitList1.setTicketingTypeId(-1L);
        waitList1.setTimeTicketsBecameAvailable(DateUtils.addDaysWithoutTime(new Date(), 1));

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);
        waitLists.add(waitList1);

        //mock
        when(waitListSettingService.getAllWaitListSettings()).thenReturn(waitListSettingsList);





        //Execution
        waitListServiceImpl.checkWaitList();

        //assert
        verify(waitListSettingService).getAllWaitListSettings();
    }

    @Test
    void test_checkWaitList_success() {

        //setup
        waitListSettings.setEventId(1);
        List<WaitListSettings> waitListSettingsList = new ArrayList<>();
        waitListSettingsList.add(waitListSettings);

        waitList = getWaitlist();

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);
        waitLists.add(waitList);

        //mock
        when(waitListSettingService.getAllWaitListSettings()).thenReturn(waitListSettingsList);
        when(roEventService.getEventListByIds(anyList())).thenReturn(Collections.singletonList(event));

        when(waitListRepository.findByEventIdInAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(anyList(), any(), anyBoolean()))
                .thenReturn(Collections.singletonList(waitList));
        when(ticketingStatisticsService.getAvailableTicketsForFullEvent(any())).thenReturn(6);
        doNothing().when(sendGridMailPrepareService).sendWaitListEmail(anyString(),any(),anyInt(),anyString(),anyString(), anyString());

        when(ticketingRepository.findByEventid(any())).thenReturn(new Ticketing());

        //Execution
        waitListServiceImpl.checkWaitList();

        //assert
        verify(waitListSettingService).getAllWaitListSettings();
        verify(waitListRepository).findByEventIdInAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(anyList(),any(),anyBoolean());
        verify(ticketingStatisticsService).getAvailableTicketsForFullEvent(any());
        verify(sendGridMailPrepareService).sendWaitListEmail(anyString(),any(),anyInt(),anyString(),anyString(), anyString());
    }

    @Test
    void test_checkWaitList_successWithExceptionThrow() {

        //setup
        waitListSettings.setEventId(1);
        List<WaitListSettings> waitListSettingsList = new ArrayList<>();
        waitListSettingsList.add(waitListSettings);

        waitList = getWaitlist();

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

        //mock
        when(waitListSettingService.getAllWaitListSettings()).thenReturn(waitListSettingsList);
        when(roEventService.getEventListByIds(anyList())).thenReturn(Collections.singletonList(event));

        when(waitListRepository.findByEventIdInAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(anyList(), any(), anyBoolean()))
                .thenReturn(Collections.singletonList(waitList));
        when(ticketingStatisticsService.getAvailableTicketsForFullEvent(any())).thenReturn(6);
        doNothing().when(sendGridMailPrepareService).sendWaitListEmail(anyString(),any(),anyInt(),anyString(),anyString(), anyString());

        when(ticketingRepository.findByEventid(any())).thenReturn(new Ticketing());

        //Execution
        waitListServiceImpl.checkWaitList();

        //assert
        verify(waitListSettingService).getAllWaitListSettings();
        verify(waitListRepository).findByEventIdInAndStatusAndTimeTicketsBecameAvailableIsNotNullAndEmailSent(anyList(),any(),anyBoolean());
        verify(ticketingStatisticsService).getAvailableTicketsForFullEvent(any());
        verify(sendGridMailPrepareService).sendWaitListEmail(anyString(),any(),anyInt(),anyString(),anyString(), anyString());
    }

    private WaitList getWaitlist() {
        waitList.setTicketingTypeId(-1L);
        waitList.setTimeTicketsBecameAvailable(DateUtils.addDaysWithoutTime(new Date(), 1));
        waitList.setEmail("<EMAIL>");
        return waitList;
    }

    @Test
    void test_releaseWaitList_successWithWaitListEmpty() {

        //setup

        //mock
        when(waitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);
        when(waitListRepository.findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(anyList(),any())).thenReturn(Arrays.asList());

        //Execution
        waitListServiceImpl.releaseWaitList(Arrays.asList(),event);

        //assert
        verify(waitListSettingService).getWaitListSettingsByEventId(event.getEventId());
        verify(waitListRepository).findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(anyList(),any());
    }


    @ParameterizedTest
    @MethodSource("getMaxWaitListSizeOrTotalTicketsAvailable")
    void test_releaseWaitList_throwExceptionReleaseListLimitExceed(int totalTicketsAvailable) {

        //setup
        List<Long> waitListIds = new ArrayList<>();
        waitListIds.add(1L);

        waitList = getWaitlist();

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);
        waitLists.add(waitList);

        //mock
        when(waitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);
        when(waitListRepository.findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(anyList(),any())).thenReturn(waitLists);
        when(ticketingStatisticsService.getAvailableTicketsForFullEvent(any())).thenReturn(totalTicketsAvailable);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListServiceImpl.releaseWaitList(waitListIds,event));

        //assert
        assertEquals(NotAcceptableException.WaitListExceptionMsg.RELEASE_LIST_LIMIT_EXCEED.getDeveloperMessage(), exception.getMessage());
        verify(waitListSettingService).getWaitListSettingsByEventId(event.getEventId());
        verify(waitListRepository).findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(anyList(),any());
        verify(ticketingStatisticsService).getAvailableTicketsForFullEvent(any());
    }

    @Test
    void test_releaseWaitList_success() {

        //setup
        List<Long> waitListIds = new ArrayList<>();
        waitListIds.add(1L);

        waitList = getWaitlist();

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);
        waitLists.add(waitList);
        Ticketing ticketing = new Ticketing();

        //mock
        when(waitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);
        when(waitListRepository.findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(anyList(),any())).thenReturn(waitLists);
        when(ticketingStatisticsService.getAvailableTicketsForFullEvent(any())).thenReturn(2);
        doNothing().when(sendGridMailPrepareService).sendWaitListEmail(anyString(),any(),anyInt(),anyString(),anyString(), anyString());
        doNothing().when(waitListRepository).markWaitListAsPaidWhereIdsIn(anyList());

        when(ticketingRepository.findByEventid(any())).thenReturn(ticketing);

        //Execution
        waitListServiceImpl.releaseWaitList(waitListIds,event);

        //assert
        ArgumentCaptor<WaitList> waitListArgumentCaptor = ArgumentCaptor.forClass(WaitList.class);
        verify(waitListRepository, times(2)).save(waitListArgumentCaptor.capture());
        WaitList waitListData = waitListArgumentCaptor.getValue();

        assertEquals(waitListData.getTimeTicketsBecameAvailable().getDate(),new Date().getDate());

        verify(waitListSettingService).getWaitListSettingsByEventId(event.getEventId());
        verify(waitListRepository).findByListOfWaitListIdsAndStatusAndEmailSentOrderByPositionAsc(anyList(),any());
        verify(ticketingStatisticsService).getAvailableTicketsForFullEvent(any());
        verify(sendGridMailPrepareService).sendWaitListEmail(anyString(),any(),anyInt(),anyString(),anyString(), anyString());
        verify(waitListRepository).markWaitListAsPaidWhereIdsIn(anyList());
    }

    @Test
    void test_checkWaitListExpiration_successWithAvailableDateNull() {

        //setup
        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

        //mock
        when(waitListRepository.findByStatus(WaitListStatus.WAITING)).thenReturn(waitLists);
        when(waitListRepository.findMaxPositionByEventId(event.getEventId())).thenReturn(1000d);

        //Execution
        waitListServiceImpl.checkWaitListExpiration();

        //assert
        verify(waitListRepository).findByStatus(WaitListStatus.WAITING);
    }

    @Test
    void test_checkWaitListExpiration_successWithAvailableDateAfterDate() {

        //setup
        waitList.setTimeTicketsBecameAvailable(DateUtils.addDaysToNow(1));
        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

        //mock
        when(waitListRepository.findByStatus(WaitListStatus.WAITING)).thenReturn(waitLists);
        when(waitListRepository.findMaxPositionByEventId(event.getEventId())).thenReturn(1000d);


        //Execution
        waitListServiceImpl.checkWaitListExpiration();

        //assert
        verify(waitListRepository).findByStatus(WaitListStatus.WAITING);
    }

    //@Test
    void test_checkWaitListExpiration_successWithAvailableDateBeforeDate() {

        //setup
        waitList.setTimeTicketsBecameAvailable(new Date());
        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(waitList);

        //mock
        when(waitListRepository.findByStatus(WaitListStatus.WAITING)).thenReturn(waitLists);
        when(waitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);
        when(waitListRepository.findMaxPositionByEventId(event.getEventId())).thenReturn(1000d);

        //Execution
        waitListServiceImpl.checkWaitListExpiration();

        //assert
        ArgumentCaptor<WaitList> waitListArgumentCaptor = ArgumentCaptor.forClass(WaitList.class);
        verify(waitListRepository, times(1)).save(waitListArgumentCaptor.capture());
        WaitList waitListData = waitListArgumentCaptor.getValue();

        assertEquals(WaitListStatus.TICKET_PURCHASE_EXPIRED,waitListData.getStatus());

        verify(waitListRepository).findByStatus(WaitListStatus.WAITING);
        verify(waitListSettingService).getWaitListSettingsByEventId(event.getEventId());
    }

    @Test
    void test_findByEventAndStatus_success() {

        //mock
        when(waitListRepository.findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING)).thenReturn(Collections.emptyList());

        //Execution
        List<WaitList>  waitLists = waitListServiceImpl.findByEventAndStatus(event,WaitListStatus.WAITING);

        //assert
        assertTrue(waitLists.isEmpty());
        verify(waitListRepository).findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
    }

    @Test
    void test_findByEventAndStatusAndIsEmailSent_success() {

        //mock
        when(waitListRepository.findByEventIdAndStatusAndEmailSent(event.getEventId(), WaitListStatus.WAITING,true)).thenReturn(Arrays.asList());

        //Execution
        List<WaitList>  waitLists = waitListServiceImpl.findByEventAndStatusAndIsEmailSent(event,WaitListStatus.WAITING,true);

        //assert
        assertTrue(waitLists.isEmpty());
        verify(waitListRepository).findByEventIdAndStatusAndEmailSent(event.getEventId(), WaitListStatus.WAITING,true);
    }

    @Test
    void test_findByEventAndStatusAndEmail_success() {

        //mock
        when(waitListRepository.findByEventIdAndStatusAndEmail(event.getEventId(), WaitListStatus.WAITING,"<EMAIL>")).thenReturn(Arrays.asList());

        //Execution
        List<WaitList>  waitLists = waitListServiceImpl.findByEventAndStatusAndEmail(event,WaitListStatus.WAITING,"<EMAIL>");

        //assert
        assertTrue(waitLists.isEmpty());
        verify(waitListRepository).findByEventIdAndStatusAndEmail(event.getEventId(), WaitListStatus.WAITING,"<EMAIL>");
    }

    @Test
    void test_findByEventAndStatusAndEmailAndTicketingTypeId_success() {

        //mock
        when(waitListRepository.findByEventIdAndStatusAndEmailAndTicketingTypeId(event.getEventId(), WaitListStatus.WAITING,"<EMAIL>",1L)).thenReturn(Arrays.asList());

        //Execution
        List<WaitList>  waitLists = waitListServiceImpl.findByEventAndStatusAndEmailAndTicketingTypeId(event,WaitListStatus.WAITING,"<EMAIL>",1L);

        //assert
        assertTrue(waitLists.isEmpty());
        verify(waitListRepository).findByEventIdAndStatusAndEmailAndTicketingTypeId(event.getEventId(), WaitListStatus.WAITING,"<EMAIL>",1L);
    }

    @Test
    void test_checkTicketsAvailability_success() throws InterruptedException {

        //setup
        waitListSettings.setEventId(1L);
        waitListSettings.setWaitListTrigger(2L);
        waitList.setTicketingTypeId(1L);

        List<WaitListSettings> waitListSettingsList = new ArrayList<>();
        waitListSettingsList.add(waitListSettings);

        //mock

        //Execution
        waitListServiceImpl.checkTicketsAvailability();

        //assert
        verify(waitListSettingService).getAllWaitListSettingsIds();
    }

    @Test
    void test_checkTicketsAvailability_zero_success() throws InterruptedException {

        //setup
        waitListSettings.setEventId(1L);
        waitListSettings.setWaitListTrigger(0L);
        waitList.setTicketingTypeId(1L);

        List<WaitListSettings> waitListSettingsList = new ArrayList<>();
        waitListSettingsList.add(waitListSettings);

        //mock

        //Execution
        waitListServiceImpl.checkTicketsAvailability();

        //assert
        verify(waitListSettingService).getAllWaitListSettingsIds();
    }

    @Test
    void test_updateWaitListStatus_successWithWaitListIdZero() {

        //setup
        List<WaitList> waitListUpdated = new ArrayList<>();
        waitListUpdated.add(waitList);

        //Execution
        waitListServiceImpl.updateWaitListStatus(waitListUpdated,WaitListStatus.WAITING);

        assertTrue(Boolean.TRUE);
    }

    @Test
    void test_updateWaitListStatus_successWithWaitListIdGreaterThanZero() {

        //setup
        waitList.setWaitListId(1L);

        List<WaitList> waitListUpdated = new ArrayList<>();
        waitListUpdated.add(waitList);

        //Execution
        waitListServiceImpl.updateWaitListStatus(waitListUpdated,WaitListStatus.WAITING);

        //assert
        ArgumentCaptor<WaitList> waitListArgumentCaptor = ArgumentCaptor.forClass(WaitList.class);
        verify(waitListRepository, times(1)).save(waitListArgumentCaptor.capture());
        WaitList waitListData = waitListArgumentCaptor.getValue();

        assertEquals(WaitListStatus.WAITING,waitListData.getStatus());
    }

    @Test
    void test_findByIdInAndEventAndStatus_successWithWaitListIdNotEmpty() {

        //setup
        List<Long> waitListIds = new ArrayList<>();
        waitListIds.add(1L);

        //mock
        when(waitListRepository.findByEventIdAndAndStatusAndWaitListIdIn(event.getEventId(), WaitListStatus.WAITING, waitListIds)).thenReturn(Arrays.asList());

        //Execution
        List<WaitList> waitLists = waitListServiceImpl.findByIdInAndEventAndStatus(waitListIds,event,WaitListStatus.WAITING);

        //assert
        assertTrue(waitLists.isEmpty());
        verify(waitListRepository).findByEventIdAndAndStatusAndWaitListIdIn(event.getEventId(), WaitListStatus.WAITING, waitListIds);
    }

    @Test
    void test_findByIdInAndEventAndStatus_successWithWaitListIdsEmpty() {

        //setup
        List<Long> waitListIds = new ArrayList<>();

        //mock


        //Execution
        List<WaitList> waitLists = waitListServiceImpl.findByIdInAndEventAndStatus(waitListIds,event,WaitListStatus.WAITING);

        //assert
        assertTrue(waitLists.isEmpty());
    }

    @Test
    void test_findByEventAndStatusAndTicketingTypeId_success() {

        //mock
        when(waitListRepository.findByEventIdAndStatusAndTicketingTypeId(event.getEventId(),WaitListStatus.WAITING,1L)).thenReturn(Arrays.asList());

        //Execution
        List<WaitList> waitLists = waitListServiceImpl.findByEventAndStatusAndTicketingTypeId(event,WaitListStatus.WAITING,1L);

        //assert
        assertTrue(waitLists.isEmpty());
        verify(waitListRepository).findByEventIdAndStatusAndTicketingTypeId(event.getEventId(),WaitListStatus.WAITING,1L);
    }

    @Test
    void test_findByidInAndEventAndTicketTypeIdAndStatus_successWithWaitListIdNotEmpty() {

        //setup
        List<Long> waitListIds = new ArrayList<>();
        waitListIds.add(1L);

        //mock
        when(waitListRepository.findByEventIdAndTicketingTypeIdAndStatusAndWaitListIdIn(event.getEventId(),1L,WaitListStatus.WAITING, waitListIds)).thenReturn(Arrays.asList());

        //Execution
        List<WaitList> waitLists = waitListServiceImpl.findByidInAndEventAndTicketTypeIdAndStatus(waitListIds,event,1L,WaitListStatus.WAITING);

        //assert
        assertTrue(waitLists.isEmpty());
        verify(waitListRepository).findByEventIdAndTicketingTypeIdAndStatusAndWaitListIdIn(event.getEventId(),1L,WaitListStatus.WAITING, waitListIds);
    }

    @Test
    void test_findByidInAndEventAndTicketTypeIdAndStatus_successWithWaitListIdsEmpty() {

        //setup
        List<Long> waitListIds = new ArrayList<>();

        //Execution
        List<WaitList> waitLists = waitListServiceImpl.findByidInAndEventAndTicketTypeIdAndStatus(waitListIds,event,1L,WaitListStatus.WAITING);

        //assert
        assertTrue(waitLists.isEmpty());
    }

    @Test
    void test_updateStatusDeleted(){

        waitListServiceImpl.updateStatusDeleted(anyLong(),any());

        verify(waitListRepository).updateStatusDeleted(anyLong(), any());
    }

    @Test
    void test_updateStatusDeletedByEventIds(){

        waitListServiceImpl.updateStatusDeletedByEventIds(any(),any());

        verify(waitListRepository).updateStatusDeletedByEventIds(any(), any());
    }

}
